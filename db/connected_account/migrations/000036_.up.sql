CREATE TABLE IF NOT EXISTS public.aa_connection_flows
(
	id          uuid                     DEFAULT public.uuid_generate_v4()                                   NOT NULL,
	actor_id    character varying                                                                            NOT NULL,
	ca_flow_name   character varying        DEFAULT 'CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT'::character varying NOT NULL,
	ca_flow_params JSONB                                                                                        NULL,
	created_at  timestamp with time zone DEFAULT now()                                                       NOT NULL,
	updated_at  timestamp with time zone DEFAULT now()                                                       NOT NULL,
	deleted_at  timestamp with time zone,
	CONSTRAINT aa_connection_flows_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.aa_connection_flows IS 'stores data related to flows started for connecting accounts';
COMMENT ON COLUMN public.aa_connection_flows.id IS 'unique identifier of a flow initiated';
COMMENT ON COLUMN public.aa_connection_flows.ca_flow_name IS 'a unique name identifying a particular type of flows';
COMMENT ON COLUMN public.aa_connection_flows.ca_flow_params IS 'parameters that may be used later in the flow to take actions like redirecting to a specific screen';
CREATE INDEX IF NOT EXISTS aa_connection_flows_updated_at_idx ON public.aa_connection_flows USING btree (updated_at DESC);
