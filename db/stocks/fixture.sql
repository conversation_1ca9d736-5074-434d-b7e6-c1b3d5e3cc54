set timezone to 'UTC';

INSERT INTO public.securities (id, security_type, security_name, vendor, vendor_security_id, logo_url, created_at, updated_at, financial_info) VALUES
('SECA1B2', 'SECURITY_TYPE_STOCK', 'Security 1', 'BRIDGEWISE', 'Bridgewise123', 'logo url', '2025-09-05 00:00:00.000000', '2025-09-05 00:00:00.000000', '{"market_cap": {"currency_code": "USD", "units": 3000000000, "nanos": 0}, "ttm_fundamental_parameters": {"pe_ratio": 30.5, "pb_ratio": 10.2}}'),
('SECA1C3', 'SECURITY_TYPE_STOCK', 'Security 2', 'BRIDGEWISE', 'Bridgewise234', 'logo url', '2025-09-05 00:00:00.000000', '2025-09-05 00:00:00.000000', '{"market_cap": {"currency_code": "USD", "units": 2500000000, "nanos": 0}, "ttm_fundamental_parameters": {"pe_ratio": 25.1, "pb_ratio": 8.7}}'),
('SECA1D4', 'SECURITY_TYPE_ETF', 'Security 3', 'MORNINGSTAR', 'MorningStar123', 'logo url', '2025-09-05 00:00:00.000000', '2025-09-05 00:00:00.000000', '{"market_cap": {"currency_code": "INR", "units": 1800000000, "nanos": 0}, "ttm_fundamental_parameters": {"pe_ratio": 18.3, "pb_ratio": 2.5}}');

INSERT INTO public.security_listings (internal_id, external_id, security_id, exchange, symbol, is_primary_listing, status, isin, vendor, vendor_listing_id, created_at, updated_at, deleted_at_unix) VALUES
('SLX8K2', 'USSX1A2B3', 'SECA1B2', 'EXCHANGE_USA_NYSE', 'AAPL', TRUE, 'LISTING_STATUS_ACTIVE', 'US0378331005', 'ALPACA', 'VENDL001', '2023-09-15 10:30:00+00', '2023-09-15 10:30:00+00', 0),
('SLQ7P9', 'USSZ9Y8X7', 'SECA1C3', 'EXCHANGE_USA_NASDAQ', 'TCS', FALSE, 'LISTING_STATUS_ACTIVE', 'US02079K3059', 'ALPACA', 'VENDL002', '2023-09-15 10:30:00+00', '2023-09-15 10:30:00+00', 0),
('SLR4M1', 'INSK3L2M1', 'SECA1D4', 'EXCHANGE_INDIA_NSE', 'RELIANCE', TRUE, 'LISTING_STATUS_INACTIVE', 'INE002A01018', 'FINVU', 'VENDL003', '2023-09-15 10:30:00+00', '2023-09-15 10:30:00+00', 0),
('SLJ5T8', 'INSN7P6Q5', 'SECA1C3', 'EXCHANGE_INDIA_NSE', 'TCS', TRUE, 'LISTING_STATUS_ACTIVE', 'INE467B01029', 'ONE_MONEY', 'VENDL004', '2023-09-15 10:30:00+00', '2023-09-15 10:30:00+00', 0);

INSERT INTO public.historical_prices (id, security_listing_id, price_date, close_price, vendor, price_derived_date, created_at, updated_at, deleted_at_unix) VALUES
('0c070a9e-5c2b-11ed-9b6a-0242ac120101', 'USSX1A2B3', '2024-06-01', '{"currency_code":"USD","units":180,"nanos":250000000}'::jsonb, 'ONE_MONEY', '2024-06-01', '2023-09-15 10:30:00+00', '2023-09-15 10:30:00+00', 0),
('0c070a9e-5c2b-11ed-9b6a-0242ac120102', 'USSX1A2B3', '2024-06-02', '{"currency_code":"USD","units":182,"nanos":100000000}'::jsonb, 'ONE_MONEY', '2024-06-02', '2023-09-15 10:30:00+00', '2023-09-15 10:30:00+00', 0),
('0c070a9e-5c2b-11ed-9b6a-0242ac120103', 'USSZ9Y8X7', '2024-06-01', '{"currency_code":"USD","units":2750,"nanos":500000000}'::jsonb, 'ALPACA', '2024-06-01', '2023-09-15 10:30:00+00', '2023-09-15 10:30:00+00', 0),
('0c070a9e-5c2b-11ed-9b6a-0242ac120104', 'INSN7P6Q5', '2024-06-01', '{"currency_code":"INR","units":2500,"nanos":0}'::jsonb, 'VENDOR_UNSPECIFIED', '2024-06-01', '2023-09-15 10:30:00+00', '2023-09-15 10:30:00+00', 0);
