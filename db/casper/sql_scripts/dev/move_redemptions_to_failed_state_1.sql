-- This fixture is to bulk move the redeemed offers to OFFER_REDEMPTION_FAILED state
-- for all cases where Fi-Coins have been deducted but redemption is not successful


update redeemed_offers
set redemption_state = 'OFFER_REDEMPTION_FAILED',
	updated_at       = now()
where
	redemption_state not in ('OFFER_REDEMPTION_SUCCESSFUL',
	                         'OFFER_REDEMPTION_FAILED',
	                         'PURCHASED_OFFER_FROM_INVENTORY',
	                         'VENDOR_REDEMPTION_MANUAL_INTERVENTION' )
	and id in (
	           -- Jan 2025 onwards
			 '949a1250-bf82-4d12-97a3-6ceaeb5bd02c',
			 '4cd7641c-3a22-4280-a2d5-2e03aea0f553',
			 'a556b907-0602-4883-a975-de7948eccc60',
			 '251b19ff-0135-4564-9db9-c5ba6dfa17ad',
			 '03643c26-0ba3-4626-82d7-057ad4ccfbfe',
			 '83f098f0-da19-4d41-8b32-f66044a94a31',
			 '44e7f270-b96c-49e0-b1c3-6f580eb6d22c',
			 'efbbffc3-dad4-4967-8ccb-f63c8055d237',
			 '4d54d375-665e-4a9f-867f-90278243ac5b',
			 '56fc2047-18c8-4f79-bb62-1fbe8f50d535',

			-- Before Jan 2025
			 'e5127385-9f1a-4c9e-ba15-7de3f3a0a300',
			 '652a9491-7381-4eab-99b5-6c27a688011f',
			 '9701865e-3682-4314-bb36-14d2f4ba4dc3',
			 '53bd8ab9-7846-4040-9d3f-c05a919327d3',
			 '6ca00f87-49a1-4406-aa81-7ae79cd06d2e',
			 '61850c01-7d08-4538-87ef-8d8a6f7ef4cd',
			 '3fbb0e38-469a-4732-9577-6b953bef9bd9',
			 '0bc3f73e-6161-4323-a8d6-2197311d46b3',
			 'd8389005-dc15-4653-bd9f-592a6c3cefc1',
			 '4ca86df2-b3d7-445b-9da8-2210a5fff141',
			 '0824f87f-9b6e-4b85-93f8-67992ca45671',
			 '02823d81-1e9f-4bb5-9f28-9e4403dd5f7a',
			 '5818801e-f3a2-45dd-ab68-5a81c9a59bfd',
			 '67d79181-7664-4d78-a243-99d144acd278',
			 '9658dfd4-33ae-440d-9137-020404f1af06',
			 'ddb11341-917c-4ac5-9657-80f60a2d590d',
			 'b387f651-172f-4158-a0a8-5703aa1d8db6',
			 'b8d53973-e610-40ba-abca-1c9c7fd271ab',
			 'cd353faa-7153-4aa2-b96d-1e0813a6792a',
			 '703fe965-e884-4857-890f-cf36670a0193',
			 'd3979285-d18d-4f9b-915b-f864fb36a07c',
			 'b72c426a-b641-4f47-82bb-b1db500eb1a3',
			 '35ed6132-d720-4ed1-b65f-c12f32085af8',
			 '9b189a25-ada4-4904-921c-aeedfc17733f',
			 '675edb75-3b76-4e75-a889-de89f9c61278',
			 '97ebc429-ad68-47aa-b9d1-ec74dd06f883',
			 '537d0d57-a07e-466c-9703-96c0fbae4800',
			 '70a0cfe9-e6d6-4786-b026-e693d90ff78d',
			 '48a4a087-82e6-494d-b958-2e72dc79e191',
			 '45cfcac5-5af2-45e7-89d0-e1b4bf00fb32',
			 '821e17f5-20d2-4300-8439-b9e951e7527b',
			 'ee9599e6-1f3d-47b8-9d68-12727a6007e6',
			 '2e1a8227-6d39-460b-8cdb-3c6bec758b08',
			 'bae78d87-89d2-495d-9030-bf8c2bace404',
			 '0eadeb7c-62a0-48dd-8622-7d98a7513bdf',
			 'd9144803-b4df-4bcf-852a-1fb4f59df9da',
			 '1709fcf5-0611-4dc3-a2b2-89f3e4149caf',
			 '516e072e-528a-410c-81fe-fd619b8ebe8d',
			 '990ca966-f1a7-4a86-8e11-2a18dd6c3ea7',
			 'd1dd6cac-ca38-4248-b226-96d36f1368dd',
			 '62989930-83c2-4006-8d6c-acb532a369ae',
			 '2180e3f3-b975-4e5a-91b6-8484a0d4874f',
			 '680739b9-b5b7-4a89-b272-fb2704cebe16',
			 '782b4f5b-3814-460f-afce-f50ef11528f0',
			 '66224345-3b76-4984-8c1d-0cf377ce9067',
			 '340f1c36-7a58-4130-a460-cca076bfb076',
			 'e113aa64-f73f-4c5c-8671-ff20d3285919',
			 '5277aadf-90d2-47b0-8ce9-22d174c35a99',
			 'd1ccfa95-38da-4522-a49d-aa984de131fa',
			 '6e2724d0-8417-45f0-ab8d-90d68c3105c5',
			 'ec5fd1cd-c1b9-4f6d-898a-5f7495047307',
			 '8beacaac-233b-420c-8682-531f4264fc73',
			 '8db266ab-732a-422b-8590-0918a8dc9d3e',
			 '446b5366-8cd7-488f-a831-2ee877a43da3',
			 '11535a31-6329-4fea-b1f0-ef590f17f2e7',
			 '8ec85279-ea58-4545-a43f-642d1452267c',
			 '65be95c7-fc16-4c86-99eb-419afa8c211f',
			 'e6fa8035-67b8-44ca-83fd-99a5cc33e62f',
			 'dce7ffdc-aeba-47a1-8f4d-4528118f7f7b',
			 'c55cb5d0-f0f4-4c86-99aa-78ffb66a710a',
			 '1f338087-f0a2-4c08-9d4e-8649d672e0c1',
			 'e88d3fc0-6e7c-453b-b4fa-695104764e41',
			 'ec1e4450-c4d1-431c-ba3c-be93c2105cee',
			 '26eee7e9-720a-4ee1-b15f-367f60425aa4',
			 '8b82111d-34c8-4883-b4c5-12e3d3ac92c7',
			 '7fab6ac6-23da-42bc-96e2-98b0bac2cb13',
			 '8d382e53-71f4-404e-b16c-dec0763f72ba',
			 '7a3251f3-b0e9-4a9d-931d-d52a00b6e908',
			 '6d465be3-7266-46a1-ac6c-27888696bd8e',
			 'bf7d9401-3660-4549-87bb-c030dcaab4a1',
			 '86dbb83a-8762-4f0d-8025-19111e56f599',
			 'a4ed5443-96ef-4e6b-b44d-5a8132092ccd',
			 'e36f5727-ac04-4c59-9057-ffa228bce368',
			 '39a3a645-787b-48a7-b4bd-8787ecda0c93',
			 '542970c2-c395-49a8-813e-cd109873e151',
			 '025ee52b-6e41-4dd3-9ced-631a3b6414cd',
			 '7f85bc10-3158-460e-9911-abd76a1356f3',
			 '5a89f966-9f96-41a9-a4e1-33da31ce388a',
			 'bbc74e0c-9032-48c3-ab0c-0b5fc7039d67',
			 '44560849-42d0-45a8-9fa3-3f6a76ad6088',
			 '8742e2f8-de5f-418b-82f9-4c7ba575017d',
			 '263a8b3d-0a5d-49f2-9c69-f7a47e15ec1f',
			 '1a3a57ea-87f1-4d06-9ddf-0a1312d3d2f8',
			 '68bbf580-47ad-4780-a639-8a52cd43ff95',
			 '8262f1dd-56e2-4f74-bf30-b149abfec009',
			 'f0506b1a-c81d-4859-b3c0-ec5696ed92ab',
			 'd228aef0-0fda-4d95-b0b9-80e1e3b18c04',
			 '57bcc070-32f8-44cc-b6f3-fbf5eb7e74a1',
			 '070735e3-ae55-4b9d-b37a-fc0ce247c83a',
			 '3b450608-3f29-473d-90f4-1ef1603361e1',
			 '7e753658-15ac-4307-a346-6fbc97ad7865',
			 '79d22107-5009-45b0-93d3-5cfc25dc6632',
			 '67a8e514-eeb9-4231-9937-4ca3c8e6b7e6',
			 '43bd772e-7034-4a29-92d2-20ffe1468432',
			 'b6832a76-66c5-4d8b-8cb5-8f65c2f4b23e',
			 'e0ec0a1d-6f58-4ce1-8ae3-7a9522ea317e',
			 '9044e714-08be-4821-8bbd-6326326e7794',
			 '672fe51e-836f-4252-8db3-96022a8d8475',
			 '54946cae-202f-4811-96cf-ed7eebc694da',
			 '95f0107b-1116-4087-b8dd-e46c2be59c53',
			 'ec76d5f0-4d93-40c1-92a0-882faa4168d9',
			 '61dc3945-8ddf-4d9b-8ec2-8cd874cbe418',
			 '7f013590-65b1-4859-9c89-dda7391317f9',
			 'a05b281d-76e5-48bf-ae05-22fd483a6f70',
			 '7e7e3692-b673-4cda-bfab-7b941b0229d8',
			 '4ddf240f-8005-44e9-b1ca-e735f20f462c',
			 '9e5a7ca8-d29f-4a8a-9a14-1e7385e612d4',
			 '0db1fff4-289e-4bdb-8649-ce10853e4988',
			 '1d266e70-1e7e-4a64-bce9-80ec088be9c6',
			 '8662d87c-06f3-454d-bf5a-57723187a96b',
			 'e44f3c2d-9aaa-4fe2-9bd1-707d81050215',
			 '83d42dea-4913-418d-bcf8-5cd239079663',
			 '2c7c38f7-6696-484f-9406-37143214aaf9',
			 'debe8e52-70c8-4b00-9dbb-4cd03459dd79',
			 '6a14bd77-8855-4d64-be68-f47fa6c0a29e',
			 '624d9507-f676-4b8d-ad6b-b9663b9cb88d',
			 '5b442937-e929-4116-a317-180c57f0a74a',
			 'fa9da5eb-f9fc-4d1d-a7ae-2c52227c9fdc',
			 '63b3260e-20ad-4cf1-aabc-b348ff34b381',
			 '9db4fc03-c36b-46fd-b207-f15a73367136',
			 '9ac45e0a-29d4-42a9-bf3a-956129184256',
			 '5a41244f-591f-4782-944a-a3ea4d32862a',
			 'c755969e-3ea7-4070-ab0e-4dc97ee6c625',
			 'e55c425d-97e9-43da-b87e-b4786482d1ec',
			 '852ef4bc-169e-4534-a4e5-179599ef0221',
			 '3ec0d9cb-75aa-415a-8efe-8ce43f1746a1',
			 '7ccbd578-9413-4169-84b5-1f1b21c399f6',
			 '0fb34a7e-6c9e-4ae3-9fb3-58c2a7c8c797',
			 'ced10e9b-14f6-4660-8a50-ebbb8e7e6f25',
			 '33dd95a4-f131-4854-9c46-49b7dffa5e5a',
			 '8738e08f-5af6-44c1-9423-bdeb720024f5',
			 'a1fc9abf-c93f-494c-8759-7882b8e16c65',
			 '84e7f2d6-5f78-40a9-9f4b-3ea72f90fbee',
			 '57545a62-7c65-4fbd-a9f6-cbc0dd3ee697',
			 '4274b3c0-c717-4f08-b111-7ff2a8db01c2',
			 '1ce86725-4643-4eba-b358-9381e8839373',
			 'd4ae5874-2a33-469d-b6c9-8ea1516b29d9',
			 'c648ac9c-ecf1-4206-ae15-66751a24fc6b',
			 'd2ae32a2-592f-4c38-a949-1ed76c0ccb0c',
			 '707b5d72-5b75-4784-8db4-de2bcc61e5d6'
	);
