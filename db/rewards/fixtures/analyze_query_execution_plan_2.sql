-- Query for getting execution plan of a query.
-- query to get the txns for which referee got the reward but referrer doesn't from 2022-12-10 to 2022-12-15.
explain
select id, actor_id, ref_id, offer_id, status, created_at
from rewards
where offer_type = 'REFERRAL_REFEREE_OFFER'
  and external_ref in (select external_ref
					   from rewards
					   where offer_type in ('REFERRAL_REFEREE_OFFER', 'REFERRAL_REFERRER_OFFER')
						 and created_at >= '2022-12-10T00:00:00 +05:30'
						 and created_at <= '2022-12-15T23:59:59 +05:30'
					   group by external_ref
					   having count(*) = 1);
