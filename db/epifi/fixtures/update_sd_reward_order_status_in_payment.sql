-- there are order in prod which moved to PAYMENT_FAILED. (id - OD2105032DWo1jIRT+yv6XC6WVAyLQ==)
-- this was due to a bug in b2c fund transfer state machine.
-- Issue is fixed now. Moving the order to IN_PAYMENT, so that we can force initiate an enquiry for the order

UPDATE orders SET status = 'IN_PAYMENT' where id = 'OD2105032DWo1jIRT+yv6XC6WVAyLQ==' and status = 'PAYMENT_FAILED';
UPDATE orders SET status = 'IN_PAYMENT' where id = 'OD210506JM2OCtmYSOSJcNdjDkflQg==' and status = 'PAYMENT_FAILED';
