-- https://monorail.pointz.in/p/fi-app/issues/detail?id=66404
-- Discussion thread : https://epifi.slack.com/archives/C0101Q3TXFF/p1699944969613289
-- Order fixture : db/epifi/fixtures/update_p2p_fund_transfer_orders_to_paid-1.sql

UPDATE workflow_requests SET STATUS = 'SUCCESSFUL', updated_at = NOW() where ID = 'WFRzwUqKZWaTi67DmKkQbaD7Q231031==' AND STATUS = 'TIMEOUT' ;
UPDATE workflow_requests SET STATUS = 'SUCCESSFUL', updated_at = NOW() where ID = 'WFR2TW5hi4fSEKBlV+gHSn0ew231028==' AND STATUS = 'TIMEOUT' ;
UPDATE workflow_requests SET STATUS = 'SUCCESSFUL', updated_at = NOW() where ID = 'WFRY0S1OzrnQ2Ww/XbozwO6Vw231009==' AND STATUS = 'TIMEOUT' ;
UPDATE workflow_requests SET STATUS = 'SUCCESSFUL', updated_at = NOW() where ID = 'WFRGNoYSMRfTwmTkl6L+BMqKQ231024==' AND STATUS = 'TIMEOUT' ;

UPDATE workflow_histories SET STATUS = 'SUCCESSFUL', updated_at = NOW() where WF_REQ_ID = 'WFRzwUqKZWaTi67DmKkQbaD7Q231031==' AND STATUS = 'TIMEOUT' ;
UPDATE workflow_histories SET STATUS = 'SUCCESSFUL', updated_at = NOW() where WF_REQ_ID = 'WFR2TW5hi4fSEKBlV+gHSn0ew231028==' AND STATUS = 'TIMEOUT' ;
UPDATE workflow_histories SET STATUS = 'SUCCESSFUL', updated_at = NOW() where WF_REQ_ID = 'WFRY0S1OzrnQ2Ww/XbozwO6Vw231009==' AND STATUS = 'TIMEOUT' ;
UPDATE workflow_histories SET STATUS = 'SUCCESSFUL', updated_at = NOW() where WF_REQ_ID = 'WFRGNoYSMRfTwmTkl6L+BMqKQ231024==' AND STATUS = 'TIMEOUT' ;
