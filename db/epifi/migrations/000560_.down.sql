DROP INDEX IF EXISTS aml_client_id_to_actor_id_mapping_updated_at_idx;
DROP INDEX IF EXISTS aml_client_id_to_actor_id_mapping_file_generation_status_idx;
DROP INDEX IF EXISTS aml_client_id_to_actor_id_mapping_actor_id_client_id_idx CASCADE;
ALTER TABLE aml_client_id_to_actor_id_mapping RENAME TO aml_attempt_id_client_id_mapper;
ALTER TABLE aml_attempt_id_client_id_mapper ADD COLUMN screening_attempt_id UUID NOT NULL;
ALTER TABLE aml_attempt_id_client_id_mapper ADD CONSTRAINT aml_attempt_id_client_id_mapper_screening_attempt_id_fkey FOREIGN KEY (screening_attempt_id) REFERENCES aml_screening_attempts(id);
CREATE INDEX aml_attempt_id_client_id_mapper_updated_at_idx ON aml_attempt_id_client_id_mapper (updated_at desc);
CREATE UNIQUE INDEX screening_attempt_id_client_request_id_idx ON aml_attempt_id_client_id_mapper (screening_attempt_id asc, client_request_id asc);
CREATE INDEX aml_attempt_id_client_id_mapper_actor_id_idx ON aml_attempt_id_client_id_mapper (actor_id asc);
COMMENT ON TABLE aml_attempt_id_client_id_mapper IS 'stores the mapping of screening attempt id and client request id raised for file generation';
COMMENT ON COLUMN aml_attempt_id_client_id_mapper.client_request_id IS 'client request id to identify the screening attempts eligible for file generation';
COMMENT ON COLUMN aml_attempt_id_client_id_mapper.screening_attempt_id IS 'primary id of aml_screening_attempts table';
COMMENT ON COLUMN aml_attempt_id_client_id_mapper.file_generation_status IS 'status of the file generation for this screening_attempt_id';
