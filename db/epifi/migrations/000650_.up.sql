ALTER TABLE onboarding_details ADD COLUMN IF NOT EXISTS feature STRING NULL;
ALTER TABLE onboarding_details ADD COLUMN IF NOT EXISTS fi_lite_details JSONB NULL;
ALTER TABLE onboarding_details ADD COLUMN IF NOT EXISTS feature_details JSONB NULL;

comment on column onboarding_details.feature is 'ENUM for the feature the current onboarding details belong to';
comment on column onboarding_details.fi_lite_details is 'JSON data regarding fi lite accessibility for a user';
comment on column onboarding_details.feature_details is 'JSON data for onboarding data for all the feature journeys';
