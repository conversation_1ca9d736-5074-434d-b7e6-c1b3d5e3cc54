-- drop id as <PERSON><PERSON>AR<PERSON> KEY and add (actor_id, id) as PRIMARY KEY
BEGIN;
ALTER TABLE deposit_requests
    DROP CONSTRAINT IF EXISTS "deposit_requests_pkey";

ALTER TABLE deposit_requests
    ADD CONSTRAINT "primary" PRIMARY KEY (actor_id, id);
COMMIT;

-- since id is not primary key anymore, add unique index on id
ALTER TABLE deposit_requests
    ADD CONSTRAINT deposit_requests_id_key UNIQUE (id);
