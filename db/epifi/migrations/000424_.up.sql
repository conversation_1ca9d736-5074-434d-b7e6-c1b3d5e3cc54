CREATE TABLE IF NOT EXISTS user_preferences (
		id UUID NOT NULL DEFAULT gen_random_uuid(),
		actor_id STRING NOT NULL,
		preference_type STRING NOT NULL,
		preference_value JSONB NOT NULL,
		created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
		updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
		deleted_at_unix INT8 NOT NULL DEFAULT 0,
		PRIMARY KEY (actor_id ASC, preference_type ASC, deleted_at_unix ASC),
		UNIQUE INDEX id_key(id ASC),
		INDEX user_preferences_updated_at_idx (updated_at ASC)
);

COMMENT ON TABLE user_preferences IS 'Table to store the user preferences';
COMMENT ON COLUMN user_preferences.actor_id IS 'actor id of the user for whom the preference is stored';
COMMENT ON COLUMN user_preferences.preference_type IS '{"proto_type":"user.preferences.PreferenceType", "comment":"type of the user preference being stored eg: CALL_LANGUAGE"}';
COMMENT ON COLUMN user_preferences.preference_value IS '{"proto_type":"user.preferences.PreferenceValue", "comment":"value of the preference dependent upon the preference_type"}';
