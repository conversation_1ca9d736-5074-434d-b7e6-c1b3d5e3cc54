CREATE TABLE IF NOT EXISTS mutual_funds (
    id                      string          NOT NULL,
    amc_id                  string          NOT NULL,
    name_data               JSONB           NOT NULL,
    plan_type               STRING          NOT NULL,
    investment_type         STRING          NOT NULL,
    option_type             STRING          NOT NULL,
    div_reinv_option_type   STRING          NOT NULL,
    asset_class             STRING          NOT NULL,
    sip_allowed             boolean         NOT NULL,
    swp_allowed             boolean         NOT NULL,
    stp_allowed             boolean         NOT NULL,
    isin_number             string          NOT NULL,
    txn_constraints         JSONB           NOT NULL    DEFAULT '{}':::JSONB,
    nav                     JSONB           NOT NULL    DEFAULT '{}':::J<PERSON><PERSON><PERSON>,

    created_at              TIMESTAMPTZ     NOT NULL DEFAULT NOW(),
    updated_at              TIMESTAMPTZ     NOT NULL DEFAULT NOW(),
    deleted_at              TIMESTAMPTZ     NULL,

    PRIMARY KEY (id ASC)
);

COMMENT ON TABLE mutual_funds IS 'store house for the catalog of mutual funds offering to the epiFi users';
COMMENT ON COLUMN mutual_funds.amc_id IS 'unique code assigned to asset management company of the mutual fund';
COMMENT ON COLUMN mutual_funds.nav IS 'the Net Asset Value of the mutual fund scheme';
COMMENT ON COLUMN mutual_funds.plan_type IS '{"proto_type":"investment.mutual_fund.PlanType", "comment": "enum to store the plan type offered", "ref": "api/investment/mutualfund/mutual_fund.proto"}';
COMMENT ON COLUMN mutual_funds.investment_type IS '{"proto_type":"investment.mutual_fund.InvestmentType", "comment": "enum to store the investment type offered", "ref": "api/investment/mutualfund/mutual_fund.proto"}';
COMMENT ON COLUMN mutual_funds.option_type IS '{"proto_type":"investment.mutual_fund.OptionType", "comment": "enum to store the option type offered", "ref": "api/investment/mutualfund/mutual_fund.proto"}';
COMMENT ON COLUMN mutual_funds.div_reinv_option_type IS '{"proto_type":"investment.mutual_fund.DividendReinvestmentOptionType", "comment": "enum to store the DividendReinvestmentOptionType offered", "ref": "api/investment/mutualfund/mutual_fund.proto"}';
COMMENT ON COLUMN mutual_funds.txn_constraints IS '{"proto_type":"investment.mutual_fund.TransactionConstraints", "comment": "txn level permissible amount and values", "ref": "api/investment/mutualfund/mutual_fund.proto"}';
