-- update pan
update onboarding_details
set personal_details = personal_details || jsonb_build_object('panDetails', personal_details->'panDetails' || jsonb_build_object('s3Paths', json_build_array('converted_doc_proof_image/AC220331jV4T03A6Tc2sJ4l8fYQvXQ==/aa4fca02-a09b-464e-a6fa-cc41ae3bda17/DOCUMENT_PROOF_TYPE_PAN/0.JPEG'))),
    customer_provided_data=customer_provided_data||jsonb_build_object('pan',jsonb_build_object('proofType', 'DOCUMENT_PROOF_TYPE_PAN', 'id', '**********', 's3Paths', json_build_array('converted_doc_proof_image/AC220331jV4T03A6Tc2sJ4l8fYQvXQ==/aa4fca02-a09b-464e-a6fa-cc41ae3bda17/DOCUMENT_PROOF_TYPE_PAN/0.JPEG')))
where actor_id = 'AC220331jV4T03A6Tc2sJ4l8fYQvXQ==' and onboarding_type = 'ONBOARDING_TYPE_WEALTH';
