//nolint:dupl
package salaryestimation

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	caPb "github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/connected_account/analytics"
	caDataAnalytics "github.com/epifi/gamma/api/connected_account/data_analytics"
	"github.com/epifi/gamma/api/connected_account/enums"
	"github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/datasharing"
	"github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/salaryestimation"
	datasharingTypes "github.com/epifi/gamma/api/typesv2/datasharing"
	caScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/connectedaccount"
	salaryEstimationScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/salaryestimation"
	salaryEstimationTypes "github.com/epifi/gamma/api/typesv2/salaryestimation"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/feature/release"
	salaryestimationPkg "github.com/epifi/gamma/pkg/salaryestimation"
	seEvents "github.com/epifi/gamma/salaryestimation/events"
)

const (
	attemptTtlInMinutes = 2 * 24 * 60 // 2 days
)

func (s *Service) computeSalaryWithStatefulLogic(ctx context.Context, req *salaryestimation.ComputeSalaryRequest) (*salaryestimation.ComputeSalaryResponse, error) {
	// checks if attempt already present or not. If present, use the same, else create a new attempt
	attempt, err := s.salaryEstimationAttemptDao.GetByClientReqID(ctx, req.GetClientReqId())
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error getting existing attempt", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
		return &salaryestimation.ComputeSalaryResponse{Status: rpc.StatusInternal()}, nil
	}
	if err != nil && errors.Is(err, epifierrors.ErrRecordNotFound) {
		attempt, err = s.createAttempt(ctx, req)
		if err != nil {
			logger.Error(ctx, "error creating new attempt and processing", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
			return &salaryestimation.ComputeSalaryResponse{Status: rpc.StatusInternal()}, nil
		}
	}
	if attempt == nil {
		logger.Error(ctx, "attempt found or unable to create", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
		return &salaryestimation.ComputeSalaryResponse{Status: rpc.StatusInternal()}, nil
	}
	// proactively expire attempt if beyond expiry
	if attempt.GetStatus() == salaryestimation.AttemptStatus_ATTEMPT_STATUS_PENDING &&
		datetime.IsBefore(attempt.GetExpiryAt(), timestamppb.Now()) {
		attempt.Status = salaryestimation.AttemptStatus_ATTEMPT_STATUS_EXPIRED
		fieldMasks := []salaryestimation.SalaryEstimationAttemptFieldMask{
			salaryestimation.SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_STATUS,
		}
		attempt, err = s.salaryEstimationAttemptDao.Update(ctx, attempt, fieldMasks)
		if err != nil {
			return nil, errors.Wrapf(err, "error updating attempt step and status for attempt id: %s", attempt.GetId())
		}
	}

	var nextActionDl *deeplink.Deeplink
	// check if attempt not already in terminal state
	switch attempt.GetStatus() {
	case salaryestimation.AttemptStatus_ATTEMPT_STATUS_PENDING:
		// do nothing, pass through
	case salaryestimation.AttemptStatus_ATTEMPT_STATUS_EXPIRED,
		salaryestimation.AttemptStatus_ATTEMPT_STATUS_CANCELLED,
		salaryestimation.AttemptStatus_ATTEMPT_STATUS_FAILED:
		nextActionDl, err = s.getClientNextActionForFailedAnalysisForExistingAttempt(ctx, attempt)
		if err != nil {
			return nil, errors.Wrap(err, "error getting failed next action for existing attempt, id: "+attempt.GetId())
		}
	case salaryestimation.AttemptStatus_ATTEMPT_STATUS_SUCCESSFUL:
		nextActionDl, err = s.getClientNextActionForSuccessfulAnalysisForExistingAttempt(ctx, attempt)
		if err != nil {
			return nil, errors.Wrap(err, "error getting success next action for existing attempt, id: "+attempt.GetId())
		}
	default:
		return nil, errors.Errorf("invalid attempt status: %s", attempt.GetStatus())
	}

	if req.GetRequireHoldingScreen() {
		if nextActionDl == nil {
			nextActionDl = getHoldingScreenForAAAnalysis(req.GetClient(), req.GetClientReqId())
		}
		return &salaryestimation.ComputeSalaryResponse{
			Status:        rpc.StatusOk(),
			AttemptStatus: attempt.Status,
			NextAction:    nextActionDl,
		}, nil
	}

	nextAction, err := s.processAttempt(ctx, req, attempt)
	if err != nil {
		logger.Error(ctx, "error handling existing attempt", zap.Error(err), zap.String("attempt_id", attempt.GetId()))
		return &salaryestimation.ComputeSalaryResponse{Status: rpc.StatusInternal()}, nil
	}
	return &salaryestimation.ComputeSalaryResponse{
		Status:        rpc.StatusOk(),
		NextAction:    nextAction,
		AttemptStatus: attempt.GetStatus(),
	}, nil
}

func (s *Service) processAttempt(ctx context.Context, req *salaryestimation.ComputeSalaryRequest, attempt *salaryestimation.SalaryEstimationAttempt) (*deeplink.Deeplink, error) {
	var err error
	// check if got flow params input in request
	aaDataFlowParams := req.GetSourceFlowParams().GetAaDataFlowParams()

	if attempt.GetStep() == salaryestimation.AttemptStep_ATTEMPT_STEP_ANALYSIS {
		return s.getAnalysisStatusAndUpdateAttemptAndGetNextAction(ctx, attempt)
	}

	// if not found, consider this as initial all to compute salary
	if aaDataFlowParams == nil {
		nextAction, err := s.resumeAttempt(ctx, attempt)
		if err != nil {
			return nil, errors.Wrap(err, "error resuming attempt")
		}
		return nextAction, nil
	}

	switch aaDataFlowParams.GetInputType() {
	// case when data share is in progress
	case salaryEstimationTypes.AaDataFlowInputType_AA_DATA_FLOW_INPUT_TYPE_DATA_SHARE:
		// if user proceeds ahead after connecting and account to account selection step, update step name
		if attempt.GetStep() != salaryestimation.AttemptStep_ATTEMPT_STEP_DATA_SHARING {
			// fire and forget call to wealth analysis service to start analysis process
			err = s.initiateAnalysis(ctx, attempt.GetActorId(), attempt.GetClientParams().GetEmploymentType(), attempt.GetClientParams().GetOrganisationName())
			if err != nil {
				return nil, errors.Wrap(err, "error initiating wealth analysis")
			}

			attempt, err = s.updateAttemptStepStatus(ctx, attempt.GetId(),
				salaryestimation.AttemptStep_ATTEMPT_STEP_DATA_SHARING, salaryestimation.AttemptStatus_ATTEMPT_STATUS_PENDING)
			if err != nil {
				return nil, errors.Wrap(err, "error updating attempt for data sharing")
			}
		}

		nextAction, err := s.checkConsentAndInitiateDataSharing(ctx, attempt, req.GetSourceFlowParams().GetAaDataFlowParams().GetDataShareParams())
		if err != nil {
			return nil, errors.Wrap(err, "error getting next action after initiating data sharing using consent")
		}
		return nextAction, nil

	// case when initiate tech analysis
	case salaryEstimationTypes.AaDataFlowInputType_AA_DATA_FLOW_INPUT_TYPE_DATA_STATUS:
		dataSharingRecord := req.GetSourceFlowParams().GetAaDataFlowParams().GetDataStatusParams().GetDataSharingRecord()
		err = s.initiateAnalysisWithSharedData(ctx, dataSharingRecord, attempt)
		if err != nil {
			return nil, errors.Wrap(err, "error initiating analysis with shared data")
		}
		attempt, err = s.updateAttemptStepStatus(ctx, attempt.GetId(),
			salaryestimation.AttemptStep_ATTEMPT_STEP_ANALYSIS, salaryestimation.AttemptStatus_ATTEMPT_STATUS_PENDING)
		if err != nil {
			return nil, errors.Wrap(err, "error storing analysis client request ID in attempt info")
		}
		return s.getAnalysisStatusAndUpdateAttemptAndGetNextAction(ctx, attempt)

	// case when analysis is in progress
	case salaryEstimationTypes.AaDataFlowInputType_AA_DATA_FLOW_INPUT_TYPE_ANALYSIS:
		return s.getAnalysisStatusAndUpdateAttemptAndGetNextAction(ctx, attempt)

	default:
		return nil, errors.Errorf("unhandled aa data flow input type: %s", aaDataFlowParams.GetInputType().String())
	}
}

func (s *Service) initiateAnalysisWithSharedData(
	ctx context.Context,
	dataSharingRecord *datasharingTypes.DataSharingRecord,
	attempt *salaryestimation.SalaryEstimationAttempt,
) error {
	var files []*analytics.File
	for _, sharedFile := range dataSharingRecord.GetData().GetAaAccountsData().GetFiles() {
		file := &analytics.File{
			FilePath: sharedFile.GetUrl(),
			FileMetadata: &analytics.FileMetadata{
				AccountsMetadata: []*analytics.AccountMetadata{
					{
						AccountId:           sharedFile.GetMetadata().GetAaAccountFileMetadata().GetAccountId(),
						OldestTransactionTs: sharedFile.GetMetadata().GetAaAccountFileMetadata().GetOldestTransactionTs(),
						LatestTransactionTs: sharedFile.GetMetadata().GetAaAccountFileMetadata().GetLatestTransactionTs(),
					},
				},
			},
		}
		files = append(files, file)
	}
	initAnalysisRes, err := s.caDataAnalyticsClient.InitiateAnalysis(ctx, &caDataAnalytics.InitiateAnalysisRequest{
		ClientReqId: attempt.GetClientReqId(),
		ActorId:     dataSharingRecord.GetActorId(),
		RequestParams: &caDataAnalytics.AnalysisRequestParams{
			DataExchangeRecord: &analytics.DataExchangeRecord{
				ClientReqId: dataSharingRecord.GetClientRequestId(),
				ActorId:     dataSharingRecord.GetActorId(),
				Owner:       dataSharingRecord.GetClientOwnership(),
				Data: &analytics.Data{
					Files: files,
				},
			},
			EmploymentType:   attempt.GetClientParams().GetEmploymentType(),
			OrganisationName: attempt.GetClientParams().GetOrganisationName(),
		},
		EmploymentType:   attempt.GetClientParams().GetEmploymentType(),
		OrganisationName: attempt.GetClientParams().GetOrganisationName(),
	})
	if err = epifigrpc.RPCError(initAnalysisRes, err); err != nil && !initAnalysisRes.GetStatus().IsAlreadyExists() {
		return errors.Wrap(err, "error initiating analysis")
	}
	return nil
}

func (s *Service) getAnalysisStatusAndUpdateAttemptAndGetNextAction(ctx context.Context, attempt *salaryestimation.SalaryEstimationAttempt) (*deeplink.Deeplink, error) {
	analysisStatusRes, err := s.caDataAnalyticsClient.GetAnalysisStatus(ctx, &caDataAnalytics.GetAnalysisStatusRequest{
		ActorId:     attempt.GetActorId(),
		ClientReqId: attempt.GetClientReqId(),
	})
	if err = epifigrpc.RPCError(analysisStatusRes, err); err != nil {
		return nil, errors.Wrap(err, "error getting latest analysis status")
	}

	analysisStatus := getSalaryEstAnalysisStatusFromCaAnalysisStatus(analysisStatusRes.GetAnalysisStatus())
	switch analysisStatus {
	case salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_SUCCESSFUL:
		attempt, err = s.updateAttemptStepStatus(ctx, attempt.GetId(), salaryestimation.AttemptStep_ATTEMPT_STEP_ANALYSIS, salaryestimation.AttemptStatus_ATTEMPT_STATUS_SUCCESSFUL)
	case salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_FAILED:
		attempt, err = s.updateAttemptStepStatus(ctx, attempt.GetId(), salaryestimation.AttemptStep_ATTEMPT_STEP_ANALYSIS, salaryestimation.AttemptStatus_ATTEMPT_STATUS_FAILED)
	case salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_IN_PROGRESS:
		if attempt.GetStep() != salaryestimation.AttemptStep_ATTEMPT_STEP_ACCOUNT_CONNECTION || attempt.GetStatus() != salaryestimation.AttemptStatus_ATTEMPT_STATUS_PENDING {
			attempt, err = s.updateAttemptStepStatus(ctx, attempt.GetId(), salaryestimation.AttemptStep_ATTEMPT_STEP_ANALYSIS, salaryestimation.AttemptStatus_ATTEMPT_STATUS_PENDING)
		}
	default:
		return nil, errors.Errorf("unhandled analysis status: %s", analysisStatus.String())
	}
	if err != nil {
		return nil, errors.Wrapf(err, "error updating attempt for analysis status: %s ", analysisStatus.String())
	}
	nextAction, err := s.getAnalysisStatusScreen(ctx, attempt, analysisStatus)
	if err != nil {
		return nil, errors.Wrap(err, "error getting analysis status screen based on request")
	}
	return nextAction, nil
}

func (s *Service) getAnalysisStatusScreen(
	ctx context.Context,
	attempt *salaryestimation.SalaryEstimationAttempt,
	latestAnalysisStatus salaryEstimationTypes.AnalysisStatus,
) (*deeplink.Deeplink, error) {
	commonScreenOptions := &salaryEstimationScreenOptions.IncomeAnalysisStatusScreen{
		BackgroundColor: widget.GetBlockBackgroundColour(colors.ColorSnow),
		VtsHeaderComponent: &widget.VisualElementTitleSubtitleElement{
			BackgroundColor: colors.ColorSnow,
		},
		Client:      attempt.GetClientParams().GetClient().String(),
		ClientReqId: attempt.GetClientReqId(),
		Source:      attempt.GetSource().String(),
		AnalysisParams: &salaryEstimationTypes.AnalysisParams{
			AttemptId: attempt.GetClientReqId(),
			Status:    latestAnalysisStatus,
		},
	}
	switch latestAnalysisStatus {
	case salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_IN_PROGRESS:
		commonScreenOptions.VtsHeaderComponent.VisualElement = common.
			GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/salaryestimation/blue-hourglass.png").
			WithProperties(&common.VisualElementProperties{Width: 200, Height: 200})
		commonScreenOptions.VtsHeaderComponent.TitleText = common.GetPlainStringText("Reviewing your account to verify income").
			WithFontStyle(common.FontStyle_HEADLINE_L).WithFontColor(colors.ColorNight)
		commonScreenOptions.VtsHeaderComponent.SubtitleText = common.GetPlainStringText("This usually takes a few minutes").
			WithFontStyle(common.FontStyle_BODY_S).WithFontColor(colors.ColorLead)
		analysisInProgressScreen, err := s.getAnalysisInProgressOrFailedScreen(attempt)
		if err != nil {
			return nil, errors.Wrap(err, "error getting next action for in progress analysis")
		}
		commonScreenOptions.GetAnalysisParams().RetryDelaySeconds = analysisStatusRetryDelaySeconds
		commonScreenOptions.Cta = &deeplink.Cta{
			Type:     deeplink.Cta_DONE,
			Text:     "Ok, got it!",
			Deeplink: analysisInProgressScreen,
		}
	case salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_SUCCESSFUL:
		commonScreenOptions.VtsHeaderComponent.VisualElement = common.
			GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/salaryestimation/white-ticker-green-bg.png").
			WithProperties(&common.VisualElementProperties{Width: 180, Height: 180})
		commonScreenOptions.VtsHeaderComponent.TitleText = common.GetPlainStringText("Verified successfully").
			WithFontStyle(common.FontStyle_HEADLINE_XL).WithFontColor(colors.ColorOnLightHighEmphasis)
		analysisSuccessfulScreen, err := s.getAnalysisSuccessfulScreen(ctx, attempt)
		if err != nil {
			return nil, errors.Wrap(err, "error getting next action for successful analysis")
		}
		commonScreenOptions.Cta = &deeplink.Cta{
			Type:     deeplink.Cta_DONE,
			Text:     "Proceed",
			Deeplink: analysisSuccessfulScreen,
		}
	case salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_FAILED:
		var secondaryCta *deeplink.Cta
		cancellationDl, err := s.getCancellationDl(ctx, attempt)
		if err != nil {
			return nil, errors.Wrap(err, "error getting cancellation dl")
		}
		if cancellationDl != nil {
			secondaryCta = &deeplink.Cta{
				Type:         deeplink.Cta_CUSTOM,
				Text:         "I don’t want to connect account",
				Deeplink:     cancellationDl,
				DisplayTheme: deeplink.Cta_TEXT,
				Status:       deeplink.Cta_CTA_STATUS_ENABLED,
			}
			commonScreenOptions.AdditionalInfoContainer = &salaryEstimationScreenOptions.IncomeAnalysisStatusScreen_AdditionalInfoContainer{
				Title: ui.NewITC().WithTexts(common.GetPlainStringText("What do to next?").WithFontColor(colors.ColorOnLightHighEmphasis).WithFontStyle(common.FontStyle_HEADLINE_S)),
				Blocks: []*salaryEstimationScreenOptions.AdditionInformationBlock{
					{
						BgColor: widget.GetBlockBackgroundColour(colors.ColorOnDarkHighEmphasis),
						LeftIcon: common.
							GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/salaryestimation/failed_analysis_info_item.png").
							WithProperties(&common.VisualElementProperties{Width: 48, Height: 48}),
						Title: ui.NewITC().WithTexts(common.GetPlainStringText("Try again to connect your account").
							WithFontStyle(common.FontStyle_SUBTITLE_XS).WithFontColor(colors.ColorOnDarkLowEmphasis)),
					},
				},
			}
		}
		commonScreenOptions.VtsHeaderComponent.VisualElement = common.
			GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/salaryestimation/white-exclamation-red-bg.png").
			WithProperties(&common.VisualElementProperties{Width: 200, Height: 200})
		commonScreenOptions.VtsHeaderComponent.TitleText = common.GetPlainStringText("Salary couldn’t be verified").
			WithFontStyle(common.FontStyle_HEADLINE_XL).WithFontColor(colors.ColorOnLightHighEmphasis)
		analysisFailedScreen, err := s.getAnalysisInProgressOrFailedScreen(attempt)
		if err != nil {
			return nil, errors.Wrap(err, "error getting next action for failed analysis")
		}
		commonScreenOptions.SecondaryCta = secondaryCta
		commonScreenOptions.Cta = &deeplink.Cta{
			Type:     deeplink.Cta_DONE,
			Text:     "Okay",
			Deeplink: analysisFailedScreen,
		}
	default:
		return nil, errors.Errorf("unknown analysis status: %s", latestAnalysisStatus)
	}
	return &deeplink.Deeplink{
		Screen:          deeplink.Screen_INCOME_ANALYSIS_STATUS_SCREEN,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(commonScreenOptions),
	}, nil
}

func (s *Service) getClientNextActionForSuccessfulAnalysisForExistingAttempt(ctx context.Context, attempt *salaryestimation.SalaryEstimationAttempt) (*deeplink.Deeplink, error) {
	switch attempt.GetClientParams().GetClient() {
	case salaryestimation.Client_CLIENT_LOANS:
		return s.getLoansRedirectDLForExistingAttempt(ctx, attempt)
	default:
		return nil, errors.Errorf("unknown client: %s", attempt.GetClientParams().GetClient())
	}
}

func (s *Service) getClientNextActionForFailedAnalysisForExistingAttempt(_ context.Context, attempt *salaryestimation.SalaryEstimationAttempt) (*deeplink.Deeplink, error) {
	switch attempt.GetClientParams().GetClient() {
	case salaryestimation.Client_CLIENT_LOANS:
		return &deeplink.Deeplink{
			Screen: deeplink.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
		}, nil
	default:
		return nil, errors.Errorf("unknown client: %s", attempt.GetClientParams().GetClient())
	}
}

func (s *Service) getLoansRedirectDLForExistingAttempt(ctx context.Context, attempt *salaryestimation.SalaryEstimationAttempt) (*deeplink.Deeplink, error) {
	res, err := s.preApprovedLoanClient.GetRedirectDL(ctx, &palPb.GetRedirectDLRequest{
		ActorId:         attempt.GetActorId(),
		ClientRequestId: attempt.GetClientReqId(),
	})
	if err = epifigrpc.RPCError(res, err); err != nil {
		if res.GetStatus().IsRecordNotFound() {
			logger.Error(ctx, "client req id not found in loans", zap.String(logger.CLIENT_REQUEST_ID, attempt.GetClientReqId()))
			return &deeplink.Deeplink{
				Screen: deeplink.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
			}, nil
		}
		return nil, errors.Wrap(err, "error getting loans redirect dl")
	}
	return res.GetRedirectDeeplink(), nil
}

func (s *Service) checkConsentAndInitiateDataSharing(ctx context.Context, attempt *salaryestimation.SalaryEstimationAttempt, dataShareParams *salaryEstimationTypes.DataShareParams) (*deeplink.Deeplink, error) {
	perpetualConsentRes, err := s.consentClient.FetchConsent(ctx, &consent.FetchConsentRequest{
		ActorId:     attempt.GetActorId(),
		ConsentType: consent.ConsentType_CONSENT_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP_V2,
		Owner:       common.Owner_OWNER_EPIFI_WEALTH,
	})
	if err = epifigrpc.RPCError(perpetualConsentRes, err); err != nil && !perpetualConsentRes.GetStatus().IsRecordNotFound() {
		return nil, errors.Wrap(err, "error fetching perpetual consent")
	}
	if perpetualConsentRes.GetStatus().IsRecordNotFound() {
		nextAction, nextActionErr := s.initiateDataSharingUsingOneTimeConsent(ctx, attempt, dataShareParams)
		if nextActionErr != nil {
			return nil, errors.Wrap(nextActionErr, "error initiating data sharing using one time consent")
		}
		return nextAction, nil
	}
	if perpetualConsentRes.GetConsentId() == "" {
		nextAction, nextActionErr := s.initiateDataSharingUsingOneTimeConsent(ctx, attempt, dataShareParams)
		if nextActionErr != nil {
			return nil, errors.Wrap(nextActionErr, "error initiating data sharing using one time consent")
		}
		return nextAction, nil
	}
	downloadDataRes, err := s.dataSharingClient.DownloadData(ctx, &datasharing.DownloadDataRequest{
		Client:          datasharingTypes.Client_CLIENT_SALARY_ESTIMATION,
		ClientOwnership: common.Owner_OWNER_EPIFI_TECH,
		ClientReqId:     attempt.GetClientReqId(),
		ActorId:         attempt.GetActorId(),
		DataType:        datasharingTypes.DataType_DATA_TYPE_AA_ACCOUNTS,
		DataRequestParams: &datasharingTypes.DataRequestParams{
			ReqParams: &datasharingTypes.DataRequestParams_AaAccountsDataQueryParams{
				AaAccountsDataQueryParams: &datasharingTypes.AaAccountsDataQueryParams{
					AccountIds:          dataShareParams.GetAaAccountIds(),
					OldestTransactionTs: timestamppb.New(s.conf.OldestAATransactionUpdatedAt()),
					LatestTransactionTs: timestamppb.Now(),
					ConsentId:           perpetualConsentRes.GetConsentId(),
				},
			},
		},
	})
	if err = epifigrpc.RPCError(downloadDataRes, err); err != nil {
		return nil, errors.Wrap(err, "error downloading data")
	}
	uploadDataRes, err := s.dataSharingClient.UploadData(ctx, &datasharing.UploadDataRequest{
		DataSharingRecord: downloadDataRes.GetDataSharingRecord(),
	})
	if err = epifigrpc.RPCError(uploadDataRes, err); err != nil {
		return nil, errors.Wrap(err, "error uploading data")
	}
	return uploadDataRes.GetNextAction(), nil
}

func (s *Service) initiateDataSharingUsingOneTimeConsent(
	ctx context.Context,
	attempt *salaryestimation.SalaryEstimationAttempt,
	dataShareParams *salaryEstimationTypes.DataShareParams,
) (*deeplink.Deeplink, error) {
	latestOneTimeDataSharingConsentRes, err := s.consentClient.FetchConsent(ctx, &consent.FetchConsentRequest{
		ConsentType: consent.ConsentType_CONSENT_TYPE_ONE_TIME_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP,
		ActorId:     attempt.GetActorId(),
		Owner:       common.Owner_OWNER_EPIFI_TECH,
	})
	if err = epifigrpc.RPCError(latestOneTimeDataSharingConsentRes, err); err != nil && !latestOneTimeDataSharingConsentRes.GetStatus().IsRecordNotFound() {
		return nil, errors.Wrap(err, "error fetching latest one time data sharing consent")
	}
	if latestOneTimeDataSharingConsentRes.GetStatus().IsRecordNotFound() ||
		// This is an edge case scenario.
		// Can happen in the rare scenario where an existing one-time data sharing consent has expired by the time user chooses to share data.
		time.Now().After(latestOneTimeDataSharingConsentRes.GetExpiresAt().AsTime()) {
		nextAction, err := s.resumeAttempt(ctx, attempt)
		if err != nil {
			return nil, errors.Wrap(err, "error getting next action after resuming attempt processing")
		}
		return nextAction, nil
	}
	dataSharingScreen, err := s.initiateDataSharing(ctx, attempt, dataShareParams, latestOneTimeDataSharingConsentRes.GetConsentId())
	if err != nil {
		return nil, errors.Wrap(err, "error initiating data sharing")
	}
	return dataSharingScreen, nil
}

func (s *Service) initiateDataSharing(ctx context.Context, attempt *salaryestimation.SalaryEstimationAttempt,
	dataShareParams *salaryEstimationTypes.DataShareParams, consentId string) (*deeplink.Deeplink, error) {
	initDataShareRes, err := s.dataSharingClient.InitiateDataSharing(ctx, &datasharing.InitiateDataSharingRequest{
		Client:      datasharingTypes.Client_CLIENT_SALARY_ESTIMATION,
		ClientReqId: attempt.GetClientReqId(),
		ActorId:     attempt.GetActorId(),
		DataType:    datasharingTypes.DataType_DATA_TYPE_AA_ACCOUNTS,
		DataRequestParams: &datasharingTypes.DataRequestParams{
			ReqParams: &datasharingTypes.DataRequestParams_AaAccountsDataQueryParams{
				AaAccountsDataQueryParams: &datasharingTypes.AaAccountsDataQueryParams{
					AccountIds:          dataShareParams.GetAaAccountIds(),
					OldestTransactionTs: timestamppb.New(s.conf.OldestAATransactionUpdatedAt()),
					LatestTransactionTs: timestamppb.Now(),
					ConsentId:           consentId,
				},
			},
		},
	})
	if err = epifigrpc.RPCError(initDataShareRes, err); err != nil {
		return nil, errors.Wrap(err, "error initiating data sharing")
	}
	return initDataShareRes.GetNextActionDeeplink(), nil
}

func (s *Service) createAttempt(ctx context.Context, req *salaryestimation.ComputeSalaryRequest) (*salaryestimation.SalaryEstimationAttempt, error) {
	source, err := s.convertSourceToAttemptSource(req.GetSource())
	if err != nil {
		return nil, errors.Wrap(err, "error converting source to attempt source")
	}
	newAttempt := &salaryestimation.SalaryEstimationAttempt{
		ClientReqId: req.GetClientReqId(),
		Source:      source,
		ActorId:     req.GetActorId(),
		ClientParams: &salaryestimation.ClientParams{
			Client:           req.GetClient(),
			EmploymentType:   req.GetEmploymentType(),
			OrganisationName: req.GetOrganisationName(),
		},
		Step:     salaryestimation.AttemptStep_ATTEMPT_STEP_ACCOUNT_CONNECTION,
		Status:   salaryestimation.AttemptStatus_ATTEMPT_STATUS_PENDING,
		ExpiryAt: timestamppb.New(time.Now().Add(attemptTtlInMinutes * time.Minute)),
	}
	createdAttempt, err := s.salaryEstimationAttemptDao.Create(ctx, newAttempt)
	if err != nil {
		return nil, errors.Wrap(err, "error creating new attempt")
	}
	return createdAttempt, nil
}

func (s *Service) updateAttemptStepStatus(ctx context.Context, attemptId string, step salaryestimation.AttemptStep, status salaryestimation.AttemptStatus) (*salaryestimation.SalaryEstimationAttempt, error) {
	attempt := &salaryestimation.SalaryEstimationAttempt{
		Id:     attemptId,
		Step:   step,
		Status: status,
	}
	fieldMasks := []salaryestimation.SalaryEstimationAttemptFieldMask{
		salaryestimation.SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_STEP,
		salaryestimation.SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_STATUS,
	}
	attempt, err := s.salaryEstimationAttemptDao.Update(ctx, attempt, fieldMasks)
	if err != nil {
		return nil, errors.Wrapf(err, "error updating attempt step and status for attempt id: %s", attemptId)
	}
	return attempt, nil
}

func (s *Service) resumeAttempt(ctx context.Context, attempt *salaryestimation.SalaryEstimationAttempt) (*deeplink.Deeplink, error) {
	perpetualConsentRes, err := s.consentClient.FetchConsent(ctx, &consent.FetchConsentRequest{
		ActorId:     attempt.GetActorId(),
		ConsentType: consent.ConsentType_CONSENT_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP_V2,
		Owner:       common.Owner_OWNER_EPIFI_WEALTH,
	})
	if err = epifigrpc.RPCError(perpetualConsentRes, err); err != nil && !perpetualConsentRes.GetStatus().IsRecordNotFound() {
		return nil, errors.Wrap(err, "error fetching perpetual consent")
	}
	oneTimeConsentRes, err := s.consentClient.FetchConsent(ctx, &consent.FetchConsentRequest{
		ActorId:     attempt.GetActorId(),
		ConsentType: consent.ConsentType_CONSENT_TYPE_ONE_TIME_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP,
		Owner:       common.Owner_OWNER_EPIFI_TECH,
	})
	if err = epifigrpc.RPCError(oneTimeConsentRes, err); err != nil && !oneTimeConsentRes.GetStatus().IsRecordNotFound() {
		return nil, errors.Wrap(err, "error fetching one-time consent")
	}
	hasValidOneTimeConsent := false
	if oneTimeConsentRes.GetConsentId() != "" && oneTimeConsentRes.GetExpiresAt() != nil {
		if time.Now().Before(oneTimeConsentRes.GetExpiresAt().AsTime()) {
			hasValidOneTimeConsent = true
		}
	}
	hasConsent := perpetualConsentRes.GetConsentId() != "" || hasValidOneTimeConsent
	aaDataPullStatus, err := salaryestimationPkg.GetAADataPullStatus(ctx, s.connectedAccountClient, attempt.GetActorId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			accConnScreen, screenErr := s.getAccountConnectionScreen(ctx, attempt, hasConsent)
			if screenErr != nil {
				return nil, errors.Wrap(screenErr, "error getting account connection screen")
			}
			return accConnScreen, nil
		}
		return nil, errors.Wrap(err, "error getting AA data pull status")
	}
	switch {
	case aaDataPullStatus.IsDataPullInProgress():
		return s.getAnalysisStatusScreen(ctx, attempt, salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_IN_PROGRESS)
	case aaDataPullStatus.IsDataPullFailed():
		attempt, err = s.updateAttemptStepStatus(ctx, attempt.GetId(), attempt.GetStep(), salaryestimation.AttemptStatus_ATTEMPT_STATUS_FAILED)
		if err != nil {
			return nil, errors.Wrap(err, "error updating attempt step status in failed case")
		}
		return s.getAnalysisStatusScreen(ctx, attempt, salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_FAILED)
	case aaDataPullStatus.IsDataPullSuccess():
		// if user proceeds ahead after connecting and account to account selection step, update step name
		if attempt.GetStep() == salaryestimation.AttemptStep_ATTEMPT_STEP_ACCOUNT_CONNECTION {
			attempt, err = s.updateAttemptStepStatus(ctx, attempt.GetId(),
				salaryestimation.AttemptStep_ATTEMPT_STEP_ACCOUNT_SELECTION, salaryestimation.AttemptStatus_ATTEMPT_STATUS_PENDING)
			if err != nil {
				return nil, errors.Wrap(err, "error updating attempt for data selection")
			}
		}

		goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
			s.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), seEvents.NewSalaryEstimationEvent(attempt.GetActorId(), seEvents.AccountSelectionScreenLoadedEventName))
		})
		accountSelectionScreen, screenErr := s.getAccountSelectionScreen(ctx,
			&getAccountSelectionScreenReq{attempt: attempt, aaDataPullStatus: aaDataPullStatus, hasConsent: hasConsent})
		if screenErr != nil {
			return nil, errors.Wrap(screenErr, "error getting account selection screen")
		}
		return accountSelectionScreen, nil
	default:
		logger.Error(ctx, "data pull status not handled",
			zap.Int("total_accounts", len(aaDataPullStatus.GetTotalAccounts())),
			zap.Int("success_accounts", len(aaDataPullStatus.GetSuccessAccounts())),
			zap.Int("failed_accounts", len(aaDataPullStatus.GetFailedAccounts())),
			zap.Int("in_progress_accounts", len(aaDataPullStatus.GetInProgressAccounts())))
		return nil, errors.Errorf("unhandled AA data pull status: %s", aaDataPullStatus)
	}
}

func (s *Service) convertSourceToAttemptSource(source salaryEstimationTypes.Source) (salaryestimation.Source, error) {
	switch source {
	case salaryEstimationTypes.Source_SOURCE_AA:
		return salaryestimation.Source_SOURCE_AA, nil
	default:
		return salaryestimation.Source_SOURCE_UNSPECIFIED, errors.Errorf("unhandled source: %s", source.String())
	}
}

func (s *Service) getAnalysisSuccessfulScreen(ctx context.Context, attempt *salaryestimation.SalaryEstimationAttempt) (*deeplink.Deeplink, error) {
	switch attempt.GetClientParams().GetClient() {
	case salaryestimation.Client_CLIENT_LOANS:
		clientRedirectionScreen, err := s.getLoansClientRedirectionScreen(ctx, attempt)
		if err != nil {
			return nil, errors.Wrap(err, "error getting client redirection screen")
		}
		return clientRedirectionScreen, nil
	default:
		return nil, errors.Errorf("unknown client: %s", attempt.GetClientParams().GetClient().String())
	}
}

func (s *Service) getAnalysisInProgressOrFailedScreen(attempt *salaryestimation.SalaryEstimationAttempt) (*deeplink.Deeplink, error) {
	switch attempt.GetClientParams().GetClient() {
	case salaryestimation.Client_CLIENT_LOANS:
		return &deeplink.Deeplink{
			Screen: deeplink.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
		}, nil
	default:
		return nil, errors.Errorf("unknown client: %s", attempt.GetClientParams().GetClient().String())
	}
}

func (s *Service) getLoansClientRedirectionScreen(ctx context.Context, attempt *salaryestimation.SalaryEstimationAttempt) (*deeplink.Deeplink, error) {
	res, err := s.preApprovedLoanClient.GetRedirectDL(ctx, &palPb.GetRedirectDLRequest{
		ActorId:         attempt.GetActorId(),
		ClientRequestId: attempt.GetClientReqId(),
	})
	if err = epifigrpc.RPCError(res, err); err != nil {
		if res.GetStatus().IsRecordNotFound() {
			logger.Error(ctx, "client req id not found in loans", zap.String(logger.CLIENT_REQUEST_ID, attempt.GetClientReqId()))
			return &deeplink.Deeplink{
				Screen: deeplink.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
			}, nil
		}
		return nil, errors.Wrap(err, "error getting loans redirect dl")
	}
	return res.GetRedirectDeeplink(), nil
}

func (s *Service) getAccountConnectionScreen(ctx context.Context, attempt *salaryestimation.SalaryEstimationAttempt, hasConsent bool) (*deeplink.Deeplink, error) {
	var consentCheckboxes []*widget.CheckboxItem
	if !hasConsent {
		consentCheckboxes = append(consentCheckboxes, getCheckboxForOneTimeDataSharingConsent())
	}
	consentCheckboxes = append(consentCheckboxes, getCheckboxForEpifiWealthAndFinvuTnc())
	dataPullFailureDeeplink, err := s.getAnalysisStatusScreen(ctx, attempt, salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_FAILED)
	if err != nil {
		return nil, errors.Wrap(err, "error getting data pull failure deeplink")
	}
	caConnectionFlowRes, err := s.connectedAccountClient.StartConnectionFlow(ctx, &caPb.StartConnectionFlowRequest{
		ClientReqId: attempt.GetClientReqId(),
		ActorId:     attempt.GetActorId(),
		CaFlowName:  enums.CAFlowName_CA_FLOW_NAME_SALARY_ESTIMATION,
		CaFlowParams: &caPb.CAFlowParams{
			DataPullSuccessRedirectionDeeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_SALARY_ACCOUNT_SELECTION_SCREEN,
				ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&salaryEstimationScreenOptions.SalaryAccountSelectionScreenOptions{
					Client:      attempt.GetClientParams().GetClient().String(),
					ClientReqId: attempt.GetClientReqId(),
					Source:      attempt.GetSource().String(),
				}),
			},
			DataPullFailureRedirectionDeeplink: dataPullFailureDeeplink,
		},
	})
	if err = epifigrpc.RPCError(caConnectionFlowRes, err); err != nil {
		return nil, errors.Wrap(err, "error starting connection flow")
	}
	accountConnectionScreen, err := s.createAccountConnectionScreen(ctx, attempt, consentCheckboxes, caConnectionFlowRes.GetConnectionFlow().GetId())
	if err != nil {
		return nil, errors.Wrap(err, "error creating account connection screen")
	}
	return accountConnectionScreen, nil
}

type getAccountSelectionScreenReq struct {
	attempt          *salaryestimation.SalaryEstimationAttempt
	aaDataPullStatus *salaryestimationPkg.AADataPullStatus
	hasConsent       bool
}

func (s *Service) getAccountSelectionScreen(ctx context.Context, req *getAccountSelectionScreenReq) (*deeplink.Deeplink, error) {
	accountBlocks, err := getAccountBlocks(req.aaDataPullStatus.GetSuccessAccounts())
	if err != nil {
		return nil, errors.Wrap(err, "error getting account blocks")
	}
	var consentCheckboxes []*widget.CheckboxItem
	if !req.hasConsent {
		consentCheckboxes = append(consentCheckboxes, getCheckboxForOneTimeDataSharingConsent())
	}
	consentCheckboxes = append(consentCheckboxes, getCheckboxForEpifiWealthAndFinvuTnc())
	dataPullFailureDeeplink, err := s.getAnalysisStatusScreen(ctx, req.attempt, salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_FAILED)
	if err != nil {
		return nil, errors.Wrap(err, "error getting data pull failure deeplink")
	}
	caConnectionFlowRes, err := s.connectedAccountClient.StartConnectionFlow(ctx, &caPb.StartConnectionFlowRequest{
		ClientReqId: req.attempt.GetClientReqId(),
		ActorId:     req.attempt.GetActorId(),
		CaFlowName:  enums.CAFlowName_CA_FLOW_NAME_SALARY_ESTIMATION,
		CaFlowParams: &caPb.CAFlowParams{
			DataPullSuccessRedirectionDeeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_SALARY_ACCOUNT_SELECTION_SCREEN,
				ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&salaryEstimationScreenOptions.SalaryAccountSelectionScreenOptions{
					Client:      req.attempt.GetClientParams().GetClient().String(),
					ClientReqId: req.attempt.GetClientReqId(),
					Source:      req.attempt.GetSource().String(),
				}),
			},
			DataPullFailureRedirectionDeeplink: dataPullFailureDeeplink,
		},
	})
	if err = epifigrpc.RPCError(caConnectionFlowRes, err); err != nil {
		return nil, errors.Wrap(err, "error starting connection flow")
	}
	accSelectionScreen := s.createAccountSelectionScreen(ctx, req.attempt, accountBlocks, consentCheckboxes,
		caConnectionFlowRes.GetConnectionFlow().GetId())
	return accSelectionScreen, nil
}

func (s *Service) createAccountConnectionScreen(ctx context.Context, attempt *salaryestimation.SalaryEstimationAttempt, consentCheckboxes []*widget.CheckboxItem, caFlowId string) (*deeplink.Deeplink, error) {
	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		s.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), seEvents.NewSalaryEstimationEvent(attempt.GetActorId(), seEvents.NewAccountConnectionScreenLoadedEventName))
	})
	// Disabling cancellation from account connection intro screen
	/*
		var secondaryCta *deeplink.Cta
		cancellationDl, err := s.getCancellationDl(ctx, attempt)
		if err != nil {
			return nil, errors.Wrap(err, "error getting cancellation dl")
		}
		if cancellationDl != nil {
			secondaryCta = &deeplink.Cta{
				Type:         deeplink.Cta_CUSTOM,
				Text:         "I don’t want to connect account",
				Deeplink:     cancellationDl,
				DisplayTheme: deeplink.Cta_TEXT,
				Status:       deeplink.Cta_CTA_STATUS_ENABLED,
			}
		}
	*/
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_VERIFY_INCOME_HOME_SCREEN,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&salaryEstimationScreenOptions.VerifyIncomeHomeScreenOptions{
			BackgroundColor: widget.GetBlockBackgroundColour(colors.ColorSnow),
			VtsHeaderComponent: &widget.VisualElementTitleSubtitleElement{
				VisualElement: common.
					GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/salaryestimation/green-chain-link.png").
					WithProperties(&common.VisualElementProperties{Width: 150, Height: 100}),
				TitleText: common.GetPlainStringText("Connect your account to verify salary").
					WithFontColor(colors.ColorOnLightHighEmphasis).WithFontStyle(common.FontStyle_HEADLINE_XL),
				SubtitleText: common.GetPlainStringText("Our partner needs your salary proof").
					WithFontColor(colors.ColorOnDarkLowEmphasis).WithFontStyle(common.FontStyle_BODY_S),
				BackgroundColor: colors.ColorSnow,
			},
			AdditionalInfos: []*ui.IconTextComponent{
				ui.NewITC().
					WithLeftVisualElement(
						common.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/salaryestimation/rupee-bank-building.png").
							WithProperties(&common.VisualElementProperties{Width: 36, Height: 36}),
					).
					WithLeftImagePadding(12).
					WithTexts(common.GetPlainStringText("Choose your salary account").
						WithFontStyle(common.FontStyle_SUBTITLE_M).WithFontColor("#383838")),
				ui.NewITC().
					WithLeftVisualElement(
						common.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/salaryestimation/bank-statement-paper.png").
							WithProperties(&common.VisualElementProperties{Width: 36, Height: 36}),
					).
					WithLeftImagePadding(12).
					WithTexts(common.GetPlainStringText("Fetch your account statement").
						WithFontStyle(common.FontStyle_SUBTITLE_M).WithFontColor("#383838")),
			},
			Consents: getTncComponent(consentCheckboxes),
			Cta: &deeplink.Cta{
				Type:         deeplink.Cta_CONTINUE,
				DisplayTheme: deeplink.Cta_PRIMARY,
				Text:         "Connect salary account",
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_CONNECTED_ACCOUNT_BENEFITS_SCREEN_V2,
					ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&caScreenOptions.BenefitsScreenOptions{
						CaFlowName: enums.CAFlowName_CA_FLOW_NAME_SALARY_ESTIMATION.String(),
						CaFlowId:   caFlowId,
					}),
				},
			},
			FooterLabel: moreVerificationMethodsFooter,
			Client:      attempt.GetClientParams().GetClient().String(),
			ClientReqId: attempt.GetClientReqId(),
			Source:      attempt.GetSource().String(),
			// SecondaryCta: secondaryCta,
		}),
	}, nil
}

func (s *Service) getCancellationDl(ctx context.Context, attempt *salaryestimation.SalaryEstimationAttempt) (*deeplink.Deeplink, error) {
	appVersionConstraintData := release.NewAppVersionConstraintData(s.conf.CancelAttemptAppVersionConfig())
	isAppVersionCompatible, appVerErr := release.NewAppVersionConstraint().Evaluate(ctx, appVersionConstraintData, nil)
	if appVerErr != nil {
		return nil, errors.Wrap(appVerErr, "unable to evaluate app version constraint")
	}
	if !isAppVersionCompatible {
		return nil, nil
	}
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_SALARY_EST_CANCELLATION_BOTTOM_SHEET,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&salaryEstimationScreenOptions.SalaryEstCancellationBottomSheetScreenOptions{
			BgColor: &widget.BackgroundColour{Colour: &widget.BackgroundColour_BlockColour{BlockColour: colors.ColorSnow}},
			Vts: &widget.VisualElementTitleSubtitleElement{
				VisualElement:   common.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/salaryestimation/cancellation_bottom_sheet_icon.png", 120, 120),
				TitleText:       common.GetPlainStringText("Don't want to connect?").WithFontColor(colors.ColorDarkLayer2).WithFontStyle(common.FontStyle_HEADLINE_XL),
				SubtitleText:    common.GetPlainStringText("No worries, you have 2 options: Retry later or start a new loan application.").WithFontColor(colors.ColorOnDarkLowEmphasis).WithFontStyle(common.FontStyle_BODY_2),
				BackgroundColor: colors.ColorSnow,
			},
			ClientReqId: attempt.GetClientReqId(),
			Cta: &deeplink.Cta{
				Type:         deeplink.Cta_CONTINUE,
				Text:         "Ok, got it",
				Deeplink:     nil,
				DisplayTheme: deeplink.Cta_PRIMARY,
				Status:       deeplink.Cta_CTA_STATUS_ENABLED,
			},
		}),
	}, nil
}

func (s *Service) createAccountSelectionScreen(
	ctx context.Context,
	attempt *salaryestimation.SalaryEstimationAttempt,
	bankAccountBlocks []*salaryEstimationScreenOptions.SalaryAccountSelectionScreenOptions_BankAccountBlock,
	consentCheckboxes []*widget.CheckboxItem,
	caFlowId string,
) *deeplink.Deeplink {
	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		s.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), seEvents.NewSalaryEstimationEvent(attempt.GetActorId(), seEvents.AccountSelectionScreenLoadedEventName))
	})
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_SALARY_ACCOUNT_SELECTION_SCREEN,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&salaryEstimationScreenOptions.SalaryAccountSelectionScreenOptions{
			BackgroundColor: widget.GetBlockBackgroundColour(colors.ColorSnow),
			VtsHeaderComponent: &widget.VisualElementTitleSubtitleElement{
				VisualElement: common.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/salaryestimation/rupee-gold-bg.png").
					WithProperties(&common.VisualElementProperties{Width: 125, Height: 125}),
				TitleText: common.GetPlainStringText("Confirm if all your sources of income are listed here").
					WithFontStyle(common.FontStyle_HEADLINE_L).WithFontColor(colors.ColorNight),
				SubtitleText: common.GetPlainStringText("This is required to verify your salary").
					WithFontStyle(common.FontStyle_BODY_S).WithFontColor(colors.ColorLead),
				BackgroundColor: colors.ColorSnow,
			},
			BankAccountBlocks: bankAccountBlocks,
			PartnerLogo: ui.NewITC().
				WithLeftVisualElement(common.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/salaryestimation/powered-by-epifi-wealth.png").
					WithProperties(&common.VisualElementProperties{Width: 106, Height: 16})),
			Consents: getTncComponent(consentCheckboxes),
			PrimaryCta: &deeplink.Cta{
				Type:         deeplink.Cta_CONTINUE,
				DisplayTheme: deeplink.Cta_PRIMARY,
				Text:         "Proceed",
				// No deeplink is sent as the next action is computed dynamically by calling ComputeSalary with account IDs
			},
			SecondaryCta: &deeplink.Cta{
				Type:         deeplink.Cta_CONTINUE,
				DisplayTheme: deeplink.Cta_SECONDARY,
				Text:         "Connect account",
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_CONNECTED_ACCOUNT_BENEFITS_SCREEN_V2,
					ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&caScreenOptions.BenefitsScreenOptions{
						CaFlowName: enums.CAFlowName_CA_FLOW_NAME_SALARY_ESTIMATION.String(),
						CaFlowId:   caFlowId,
					}),
				},
			},
			FooterLabel: moreVerificationMethodsFooter,
			Client:      attempt.GetClientParams().GetClient().String(),
			ClientReqId: attempt.GetClientReqId(),
			Source:      attempt.GetSource().String(),
		}),
	}
}
