## Transaction Backfill

Transaction backfill can be used to enrich/update existing transactions or create new enriched data from raw transactions. 
Overview of the process:
* Spark job to fetch payload for backfilling
* Push to a SQS queue in batches
* Consumer executes a temporal workflow for backfilling

### Spark job

A generic spark job which publishes the desired payload to a SQS queue. Each packet/payload has a limit of 256 kB. Refer
to the spark script [here](https://github.com/epiFi/data-platform/blob/develop/epiview/src/main/scala/com/epifi/dataplatform/epiview/scripts/TransactionBackfill.scala).

### Consumer

[`InititateTxnBackfill`](../../consumer/service.go) consume packets from the
SQS queue and simply initiates the temporal workflow for the backfilling process

### Temporal Workflow

Broadly, the workflow has the following stages
* Parse: to convert raw transaction data to a parsed representation.
* Enrich: use the parsed data for Actor, PI and timeline resolution.
* Update: update in the corresponding enriched transaction database. Create the record if it doesn't exist already.
* Publish: publish to a topic to notify downstream services. Each transaction/service must have its own topic.

The activities are generic for any type of transaction and are created based on the above stages [here](). All transactions
can re-use the workflow and activities. Any new type of transaction just needs to implement [`ITransactionBackfill`](./transaction.go) interface
to use the backfill workflow.

To ensure efficient execution of the process, the workflow is executed with the following strategy.

```
// TODO (abhinit@)
```
