package nudges

import (
	"context"
	"fmt"
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/gamma/pkg/accrual"

	"github.com/samber/lo"

	"github.com/golang/protobuf/ptypes/timestamp"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	colorPkg "github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/accounts/balance"
	balanceEnums "github.com/epifi/gamma/api/accounts/balance/enums"
	"github.com/epifi/gamma/api/actor"
	tcPb "github.com/epifi/gamma/api/comms/inapptargetedcomms"
	dePb "github.com/epifi/gamma/api/dynamic_elements"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/kyc"
	vkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/consent"
	usersPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/kyc/vkyc/common"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/feature/release"
	releaseGenConf "github.com/epifi/gamma/pkg/feature/release/config/genconf"
	vkycPkg "github.com/epifi/gamma/pkg/vkyc"
)

type VkycDetail struct {
	VkycUpdatedAt                      *timestamp.Timestamp
	CifSucceededAt                     *timestamp.Timestamp
	AccFreezeOn                        *timestamp.Timestamp
	FundTransferEligibleOnLastDay      *timestamp.Timestamp
	FundTransferEligibleOnLastThirdDay *timestamppb.Timestamp
	KycLevel                           kyc.KYCLevel
	AccountCloseInLast30Days           *timestamppb.Timestamp
	PopupAccountFreezeThreshold        *timestamppb.Timestamp
	Mappings                           []*tcPb.InAppTargetedCommsMapping
	LandingDeeplink                    *deeplink.Deeplink
	AccFreezeDaysRemaining             float64
	InstructionDeeplink                *deeplink.Deeplink
	MaxAllowedSavingsLimitAmount       int64
	StaleBalance                       *money.Money
	VkycInfoResp                       *vkycPb.GetVKYCInfoResponse
	InAppTargetedCommsClient           tcPb.InAppTargetedCommsClient
	ActorId                            string
	PerformEkycAfter                   time.Duration
	VKYCApproved                       bool
}

type NudgeContent struct {
	Deeplink          *deeplink.Deeplink
	UIElement         vkycPb.UIElement
	Title             string
	Icon              string
	DynamicEleCtaList []*dePb.DynamicElementCta
	Body              string
	BgColor           string
	IsDismissible     bool
	NudgeId           string
	CtaList           []*deeplink.Cta
}

// determines whether a "nudge" is eligible to be shown based on a time constraint.
// if the difference between the current time and the creation time is less than the number of days to be shown.
// then we show the banner
func timeConstraintEligibility(createdAt *timestamppb.Timestamp, numberOfDaysToShown int) bool {
	return createdAt.IsValid() && !datetime.IsDatePastXDays(createdAt, numberOfDaysToShown)

}

// if the current time is after the time obtained by adding the numberOfHoursAfterShown to the lastCallbackTime.
// this means enough time has passed since the last time the popup was shown, so the popup is eligible to be shown.
func timeConstraintEligibilityDuration(lastCallbackTime *timestamppb.Timestamp, numberOfHoursAfterShown time.Duration) bool {
	return lastCallbackTime == nil ||
		datetime.IsAfter(timestamppb.Now(), timestamppb.New(lastCallbackTime.AsTime().
			Add(numberOfHoursAfterShown)))
}

func isVkycInTerminalState(vkycSummary *vkycPb.VKYCSummary, isVKYCApproved bool) bool {
	return vkycSummary.GetStatus() == vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_APPROVED ||
		vkycSummary.GetStatus() == vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_REJECTED ||
		vkycSummary.GetStatus() == vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_IN_REVIEW ||
		isVKYCApproved
}

func isScheduledSlotCutOffReached(slotStartTime *timestamppb.Timestamp) bool {
	return time.Until(slotStartTime.AsTime().In(datetime.IST)) <= time.Duration(2)*time.Minute &&
		timestamppb.Now().AsTime().In(datetime.IST).Before(slotStartTime.AsTime().In(datetime.IST).Add(5*time.Minute))
}

// getCommsMappingByElementId is used to get the nudge mapping based on nudge id for a actor
func getCommsMappingByElementId(mappings []*tcPb.InAppTargetedCommsMapping, nudgeId string) *tcPb.InAppTargetedCommsMapping {
	for _, rec := range mappings {
		if rec.GetElementId() == nudgeId {
			return rec
		}
	}
	return nil
}

func addNudgeInIAT(ctx context.Context, client tcPb.InAppTargetedCommsClient, actorId string, nudgeId string) error {
	addTargetedMappingResp, addTargetedMappingErr := client.
		AddTargetedCommsMapping(ctx, &tcPb.AddTargetedCommsMappingRequest{
			TargetedCommsElementId: nudgeId,
			MappingDetailsList: []*tcPb.MappingDetails{
				{
					MappingType: tcPb.MappingType_MAPPING_TYPE_USER,
					MappedValue: actorId,
				},
			},
			AppDetailsMappingList: nil,
		})
	if rpcErr := epifigrpc.RPCError(addTargetedMappingResp, addTargetedMappingErr); rpcErr != nil {
		logger.Error(ctx, "error while adding nudge in in_app_targeted_comms", zap.Error(rpcErr))
		return rpcErr
	}
	return nil
}

func getStaleBalance(ctx context.Context, actorId string, actorClient actor.ActorClient, savingsClient savingsPb.SavingsClient, accountSavingsClient balance.BalanceClient) (*money.Money, error) {
	staleAccountBalance := &money.Money{}
	actorResp, actorErr := actorClient.GetActorById(ctx, &actor.GetActorByIdRequest{Id: actorId})
	if te := epifigrpc.RPCError(actorResp, actorErr); te != nil {
		logger.Error(ctx, "unable to fetch actor", zap.Error(te))
		return nil, te
	}
	// cloning context since for saving client sometime context got cancelled
	// and this call is non blocking
	clonedCtx, cancel := context.WithTimeout(epificontext.CloneCtx(ctx), 1*time.Second)
	defer cancel()

	savingsAccountRes, err := savingsClient.GetAccount(clonedCtx, &savingsPb.GetAccountRequest{
		Identifier: &savingsPb.GetAccountRequest_PrimaryUserId{
			PrimaryUserId: actorResp.GetActor().GetEntityId(),
		}},
	)
	if err != nil || savingsAccountRes.GetAccount() == nil {
		logger.Error(ctx, "error while fetching savings account for user", zap.Error(err))
	} else {
		accountBalRes, rpcErr := accountSavingsClient.GetAccountBalance(clonedCtx, &balance.GetAccountBalanceRequest{
			Identifier: &balance.GetAccountBalanceRequest_Id{
				Id: savingsAccountRes.GetAccount().GetId(),
			},
			ActorId:       actorId,
			DataFreshness: balanceEnums.DataFreshness_LAST_KNOWN_BALANCE_FROM_DB,
		})
		if te := epifigrpc.RPCError(accountBalRes, rpcErr); te != nil {
			logger.Error(ctx, "error while fetching savings account balance", zap.Error(err))
		}
		staleAccountBalance = accountBalRes.GetAvailableBalance()
	}
	return staleAccountBalance, nil
}

// GetABEvaluatorOfFeature returns an instance of the AB evaluator to perform experiments of type ABExperiment
func GetABEvaluatorOfFeature[ABExperiment any](
	actorClient actor.ActorClient, userClient usersPb.UsersClient, userGroupClient userGroupPb.GroupClient,
	abFeatureReleaseConf *releaseGenConf.ABFeatureReleaseConfig,
	strToExprFn func(str string) ABExperiment,
) *release.ABEvaluator[ABExperiment] {
	abEvaluator := release.NewABEvaluator[ABExperiment](
		abFeatureReleaseConf,
		release.NewConstraintFactoryImpl(
			release.NewAppVersionConstraint(),
			release.NewStickinessConstraint(),
			release.NewUserGroupConstraint(actorClient, userClient, userGroupClient),
		),
		strToExprFn,
	)

	return abEvaluator
}

func IsUserEligibleToReattemptVKYC(ctx context.Context, actorId string, enableReAttemptVKYCNudge bool, enableReAttemptVKYCNudgeRollOut, reAttemptVkycStartHour, reAttemptVkycEndHour int) bool {
	if lo.Contains(vkycPkg.ReAttemptVKYCPriority, actorId) {
		logger.Info(ctx, "user is in re-attempt vkyc priority list")
		return true
	}

	if !common.IsBusinessHours(time.Now().In(datetime.IST), reAttemptVkycStartHour, reAttemptVkycEndHour) {
		logger.Info(ctx, "user is not in business hours for reattempt vkyc")
		return false
	}

	reeAttemptVKYCNudgeFeature := "REATTEMPTROLLOUTFEATURE"
	isEligible := enableReAttemptVKYCNudge &&
		CheckReleaseStickinessConstraint(ctx, actorId, reeAttemptVKYCNudgeFeature, enableReAttemptVKYCNudgeRollOut)
	if isEligible {
		// TODO (Rishu Sahu): delete the log once it is stable
		logger.Info(ctx, "flag enabled and user match re-attempt vkyc  rollout criteria")
	}
	return isEligible
}

func CheckReleaseStickinessConstraint(ctx context.Context, actorId, feature string, rolloutPercentage int) bool {
	featureAndActor := fmt.Sprintf("%v%v", feature, actorId)
	num, err := release.GetHashNum(featureAndActor)
	if err != nil {
		logger.Error(ctx, "error while generating hash for actor")
		return false
	}
	if num%100 < uint64(rolloutPercentage) {
		// TODO (RISHU SAHU): Remove once feature is stable
		logger.Info(ctx, "re attempt vkyc release constraint true")
		return true
	}
	return false
}

func getVkycApprovedDl(ackId string) *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_VKYC_INTRO,
		ScreenOptions: &deeplink.Deeplink_VkycIntroOptions{
			VkycIntroOptions: &deeplink.VKYCIntroOptions{
				Title:              LandingPageTitle,
				Description:        LandingPageDescription,
				BackgroundColorHex: colorPkg.ColorSupportingMoss100,
				Blocks: []*deeplink.LandingPageBlock{
					{
						Icon:                 LandingPageBlockStarIcon,
						Text:                 LandingPageBlockStarTitle,
						BackgroundColorHex:   LandingPageBlockStarColorHex,
						LandingPageBlockType: deeplink.LandingPageBlockType_LANDING_PAGE_BLOCK_TYPE_HALF,
					},
					{
						Icon:                 LandingPageBlockDollarIcon,
						Text:                 LandingPageBlockDollarTitle,
						BackgroundColorHex:   LandingPageBlockDollarColorHex,
						LandingPageBlockType: deeplink.LandingPageBlockType_LANDING_PAGE_BLOCK_TYPE_HALF,
					},
					{
						Icon:                 LandingPageBlockRocketIcon,
						Text:                 LandingPageBlockRocketTitle,
						BackgroundColorHex:   LandingPageBlockRocketColorHex,
						LandingPageBlockType: deeplink.LandingPageBlockType_LANDING_PAGE_BLOCK_TYPE_HALF,
					},
					{
						Icon:                 LandingPageBlockThunderIcon,
						Text:                 LandingPageBlockThunderTitle,
						BackgroundColorHex:   LandingPageBlockThunderColorHex,
						LandingPageBlockType: deeplink.LandingPageBlockType_LANDING_PAGE_BLOCK_TYPE_HALF,
					},
				},
				Ctas: []*deeplink.Cta{
					{
						Type:         deeplink.Cta_CUSTOM,
						Text:         LandingPageCompleteCtaText,
						DisplayTheme: deeplink.Cta_PRIMARY,
						Deeplink: deeplinkv3.GetDeeplinkV3WithoutError(deeplink.Screen_PROCESS_USER_ACK_API, &consent.ProcessUserAckApiScreenOptions{
							AckId:   ackId,
							AckType: consent.AckType_ACK_TYPE_VKYC_APPROVED_NUDGE.String(),
						}),
					},
				},
				BottomBanners: []*deeplink.BottomBanner{
					{
						BgColor:      widget.GetBlockBackgroundColour(colorPkg.ColorSnow),
						BgColorBlock: widget.GetBlockBackgroundColour(colorPkg.ColorSnow),
						Icon: &commontypes.Image{
							ImageType: commontypes.ImageType_PNG,
							ImageUrl:  accrual.ReturnApplicableValue(TwoCoinsIcon, "https://epifi-icons.pointz.in/rewards/fi_point_icon.png", nil, true).(string),
							Width:     56,
							Height:    56,
						},
						Text: commontypes.GetTextFromStringFontColourFontStyle("Rewards on all spends", colorPkg.ColorSupportingMoss900, commontypes.FontStyle_SUBTITLE_S),
					},
				},
			},
		},
	}
}
