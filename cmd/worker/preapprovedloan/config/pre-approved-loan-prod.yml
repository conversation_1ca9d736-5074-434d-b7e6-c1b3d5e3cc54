Application:
  Environment: "prod"
  Namespace: "prod-pre-approved-loan"
  TaskQueue: "prod-pre-approved-loan-task-queue"
  RedisConfig:
    IsSecureRedis: true
    Options:
      Addr: "redis-10246.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:10246"
      Password: ""
    AuthDetails:
      SecretPath: "prod/redis/cards/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
    ClientName: preapprovedloan_worker
    HystrixCommand:
      CommandName: "firefly_redis_circuit_breaker_command"
      TemplateName: "Q20"
      OverrideTemplateConfig:
        ExecutionMaxConcurrency: 10000
        ExecutionTimeout: 1s
        RequiredConsecutiveSuccessful: 6
        HalfOpenAttemptsAllowedPerSleepWindow: 10
        ErrorThresholdPercentage: 50
        SleepWindow: 15s
        FallbackMaxConcurrency: 10000

S3:
  BucketNames:
    Preapprovedloan: "epifi-prod-preapprovedloan"

LoanStageParams:
  Mandate:
    LiquiloansExitUrl: "https://web.liquiloans.com/mandatecallback"
    IsFiAccOperationalStatusCheckEnabled: false
    IsPrePopulateDefaultAccountDisabled: true

PgDbConfigMap:
  FEDERAL_BANK:
    DbType: "PGDB"
    AppName: "pre-approved-loan-worker"
    StatementTimeout: 10s
    Name: "loans_federal"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_federal_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  LIQUILOANS_PL:
    DbType: "PGDB"
    AppName: "pre-approved-loan-worker"
    StatementTimeout: 10s
    Name: "loans_liquiloans"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_liquiloans_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  IDFC_PL:
    DbType: "PGDB"
    AppName: "pre-approved-loan-worker"
    StatementTimeout: 10s
    Name: "loans_idfc"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_idfc_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  FIFTYFIN_LAMF:
    DbType: "PGDB"
    AppName: "pre-approved-loan-worker"
    StatementTimeout: 5s
    Name: "loans_fiftyfin"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_fiftyfin_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  MONEYVIEW_PL:
    DbType: "PGDB"
    AppName: "pre-approved-loan-worker"
    StatementTimeout: 5s
    Name: "loans_moneyview"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_moneyview_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  LOANS_ABFL:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 5s
    Name: "loans_abfl"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_abfl_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  LOANS_LENDEN:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 5s
    Name: "loans_lenden"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_lenden_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  LOANS_STOCK_GUARDIAN_LSP:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 5s
    Name: "loans_stock_guardian_lsp"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_stock_guardian_lsp_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  EPIFI_TECH_V2:
    DbType: "PGDB"
    AppName: "pre-approved-loan-worker"
    StatementTimeout: 5s
    Name: "loans_epifi_tech"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_epifi_tech_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false

DbConfigMap:
  EPIFI_TECH:
    DBType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "pre-approved-loan-worker"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
  FEDERAL_BANK:
    DBType: "CRDB"
    Username: "federal_dev_user"
    Password: ""
    Name: "federal"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.federal_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.federal_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "pre-approved-loan-worker"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
  LIQUILOANS_PL:
    DBType: "CRDB"
    Username: "pl_liquiloans_dev_user"
    Password: ""
    Name: "pl_liquiloans"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.pl_liquiloans_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.pl_liquiloans_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "pre-approved-loan-worker"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
  IDFC_PL:
    DBType: "CRDB"
    Username: "pl_idfc_dev_user"
    Password: ""
    Name: "pl_idfc"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.pl_idfc_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.pl_idfc_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "pre-approved-loan-worker"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
  FIFTYFIN_LAMF:
    DBType: "CRDB"
    Username: "loans_fiftyfin_crdb_dev_user"
    Password: ""
    Name: "loans_fiftyfin_crdb"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.loans_fiftyfin_crdb_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.loans_fiftyfin_crdb_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "pre-approved-loan-worker"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
  MONEYVIEW_PL:
    DbType: "PGDB"
    AppName: "pre-approved-loan-worker"
    StatementTimeout: 5s
    Name: "loans_moneyview"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_moneyview_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  LOANS_ABFL:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 5s
    Name: "loans_abfl"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_abfl_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  LOANS_LENDEN:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 5s
    Name: "loans_lenden"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_lenden_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  LOANS_STOCK_GUARDIAN_LSP:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 5s
    Name: "loans_stock_guardian_lsp"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_stock_guardian_lsp_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  EPIFI_TECH_V2:
    DbType: "PGDB"
    AppName: "pre-approved-loan-worker"
    StatementTimeout: 5s
    Name: "loans_epifi_tech"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_epifi_tech_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false

CommonDbConfigMap:
  EPIFI_TECH:
    DBType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "pre-approved-loan-worker"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
  FEDERAL_BANK:
    DBType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "pre-approved-loan-worker"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
  LIQUILOANS_PL:
    DBType: "CRDB"
    Username: "pl_liquiloans_dev_user"
    Password: ""
    Name: "pl_liquiloans"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.pl_liquiloans_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.pl_liquiloans_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "pre-approved-loan-worker"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
  IDFC_PL:
    DBType: "CRDB"
    Username: "pl_idfc_dev_user"
    Password: ""
    Name: "pl_idfc"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.pl_idfc_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.pl_idfc_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "pre-approved-loan-worker"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
  FIFTYFIN_LAMF:
    DBType: "CRDB"
    Username: "loans_fiftyfin_crdb_dev_user"
    Password: ""
    Name: "loans_fiftyfin_crdb"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.loans_fiftyfin_crdb_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.loans_fiftyfin_crdb_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "pre-approved-loan-worker"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
  MONEYVIEW_PL:
    DbType: "PGDB"
    AppName: "celestial"
    StatementTimeout: 10s
    Name: "loans_moneyview"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_moneyview_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  LOANS_ABFL:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 5s
    Name: "loans_abfl"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_abfl_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  LOANS_LENDEN:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 5s
    Name: "loans_lenden"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_lenden_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  LOANS_STOCK_GUARDIAN_LSP:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 5s
    Name: "loans_stock_guardian_lsp"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_stock_guardian_lsp_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  EPIFI_TECH_V2:
    DbType: "PGDB"
    AppName: "pre-approved-loan-worker"
    StatementTimeout: 5s
    Name: "loans_epifi_tech"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_epifi_tech_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false

WorkflowUpdatePublisher:
  TopicName: "prod-celestial-workflow-update-topic"

WorkflowParamsList:
  - WorkflowName: "PreApprovedLoanApplication"
    ActivityParamsList:
      - ActivityName: "ProcessLoanApplication"
        ScheduleToCloseTimeout: "105h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 4 days with max cap between retries at 1 hour
          # Retry interval - 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h 1h 1h 1h 1h 1h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "1h"
            BackoffCoefficient: 2.0
            MaxAttempts: 105
      - ActivityName: "GetOtpVerificationStatus"
        ScheduleToCloseTimeout: "50m"
        StartToCloseTimeout: "5m"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 300
      - ActivityName: "CheckKycEligibility"
        ScheduleToCloseTimeout: "4h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 4 hours with max cap between retries at 5 min
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 5m 5m 5m 5m 5m
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 2.0
            MaxAttempts: 56
      - ActivityName: "CheckLivenessEligibility"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # The activity just initiates liveness for a user
          # Regular retry strategy that runs for 30 minutes at 3 second interval
          # Retry interval - 3s 3s 3s 3s ...
          RegularInterval:
            Interval: "3s"
            MaxAttempts: 600
      - ActivityName: "CheckFaceMatchEligibility"
        ScheduleToCloseTimeout: "30m"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Face match is triggered after liveness, so data should be there with the service.
          # Regular retry strategy that runs for 20 minutes at 2 second interval
          # Retry interval - 2s 2s 2s 2s ...
          RegularInterval:
            Interval: "2s"
            MaxAttempts: 900
      - ActivityName: "UpdateLoanRequest"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 4 hours with max cap between retries at 5 min
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 5m 5m 5m 5m 5m
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 2.0
            MaxAttempts: 56
      - ActivityName: "ProcessLivenessVendorReview"
        ScheduleToCloseTimeout: "30m"
        StartToCloseTimeout: "5m"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "CheckForRisk"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for ~3 hours with max cap between retries at 5 min
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 5m 5m 5m 5m 5m
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 2.0
            MaxAttempts: 40
      - ActivityName: "ProcessLoanAccountCreation"
        ScheduleToCloseTimeout: "97h"
        StartToCloseTimeout: "180s"
        RetryParams:
          # Exponential retry strategy that runs for 4 days with max cap between retries at 1 hour
          # Retry interval - 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h 1h 1h 1h 1h 1h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "1h"
            BackoffCoefficient: 2.0
            MaxAttempts: 105
      - ActivityName: "CheckKfsStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # 3s, 6s, 12s, 24s, 48s, 1m36s, 3m12s, 6m24s, 12m48s, 25m36s, 51m12s, 1h, 1h, 1h...
          ExponentialBackOff:
            BaseInterval: "3s"
            MaxInterval: "1h"
            BackoffCoefficient: 2.0
            MaxAttempts: 81
      - ActivityName: "InitialiseKfsStage"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Regular retry strategy that runs for 30 minutes at 2 second interval
          # Retry interval - 2s 2s 2s 2s ...
          RegularInterval:
            Interval: "2s"
            MaxAttempts: 900
      - ActivityName: "CheckProfileValidation"
        ScheduleToCloseTimeout: "50h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 2 days with max cap between retries at 1h
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 34m8s, 1h, 1h, 1h, 1h...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2.0
            MaxAttempts: 60
      - ActivityName: "CheckForLimitChange"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for ~3 hours with max cap between retries at 5 min
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 5m 5m 5m 5m 5m
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 2.0
            MaxAttempts: 40
      - ActivityName: "CheckAuthLiveness"
        ScheduleToCloseTimeout: "48h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # 1s, 2s, 4s, 8s, 16s, 32s, 1m4s, 2m8s, 4m16s, 8m32s, 17m4s, 30m, 30m, 30m, 30m....
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 105
      - ActivityName: "GenerateKfsDocumentInternally"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # 1s, 2s, 4s, 8s, 16s, 32s, 1m4s, 2m8s, 4m16s, 8m32s, 17m4s, 34m8s, 1h, 1h, 1h...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "CheckForRiskV2"
        ScheduleToCloseTimeout: "2h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for ~3 hours with max cap between retries at 5 min
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 5m 5m 5m 5m 5m
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 2.0
            MaxAttempts: 40
      - ActivityName: "CheckForLimit"
        ScheduleToCloseTimeout: "2h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for ~3 hours with max cap between retries at 5 min
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 5m 5m 5m 5m 5m
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 2.0
            MaxAttempts: 40
  - WorkflowName: "KycCheck"
    ActivityParamsList:
      - ActivityName: "ProcessKycCheck"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # 1s, 2s, 4s, 8s, 16s, 32s, 1m4s, 2m8s, 4m16s, 8m32s, 17m4s, 30m, 30m, 30m, 30m....
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 125
  - WorkflowName: "LivenessCheck"
    ActivityParamsList:
      - ActivityName: "ProcessLivenessCheck"
        ScheduleToCloseTimeout: "48h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # 1s, 2s, 4s, 8s, 16s, 32s, 1m4s, 2m8s, 4m16s, 8m32s, 17m4s, 30m, 30m, 30m, 30m....
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 105
  - WorkflowName: "FaceMatchCheck"
    ActivityParamsList:
      - ActivityName: "ProcessFaceMatchCheck"
        ScheduleToCloseTimeout: "11h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # 1s, 2s, 4s, 8s, 16s, 32s, 1m4s, 2m8s, 4m16s, 8m32s, 10m, 10m, 10m, 10m, 10m....
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "10m"
            BackoffCoefficient: 2.0
            MaxAttempts: 70
  - WorkflowName: "ManualReviewCheck"
    ActivityParamsList:
      - ActivityName: "CheckManualReviewStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          RegularInterval:
            Interval: "30m"
            MaxAttempts: 144
      - ActivityName: "CheckManualReviewForLivenessAndFaceMatch"
        ScheduleToCloseTimeout: "4h"
        StartToCloseTimeout: "5m"
        # Regular retry strategy of 5s that runs for ~10 hours
        RetryParams:
          # 1s, 2s, 4s, 8s, 16s, 32s, 1m4s, 2m8s, 4m16s, 8m32s, 10m, 10m, 10m, 10m, 10m....
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "10m"
            BackoffCoefficient: 2.0
            MaxAttempts: 70
  - WorkflowName: "PreApprovedLoanPrePay"
    ActivityParamsList:
      - ActivityName: "ProcessPrePayPayment"
        ScheduleToCloseTimeout: "10h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # 1s, 2s, 4s, 8s, 16s, 32s, 1m4s, 2m8s, 4m16s, 8m32s, 10m, 10m, 10m, 10m, 10m....
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "10m"
            BackoffCoefficient: 2.0
            MaxAttempts: 70
  - WorkflowName: "PreCloseLoanAccount"
    ActivityParamsList:
      - ActivityName: "ProcessPreClosure"
        ScheduleToCloseTimeout: "10h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # 1s, 2s, 4s, 8s, 16s, 32s, 1m4s, 2m8s, 4m16s, 8m32s, 10m, 10m, 10m, 10m, 10m....
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "10m"
            BackoffCoefficient: 2.0
            MaxAttempts: 70
  - WorkflowName: "LoanApplication"
    ActivityParamsList:
      - ActivityName: "AbflRegisterPwaUser"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 900
      - ActivityName: "IncomeEstimation"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
      - ActivityName: "IncomeEstimationStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
      - ActivityName: "LendenPreBreLoanDataCollectionStatus"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "120s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "AbflPushDataToVendor"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 900
      - ActivityName: "FetchAndSetAbflPwaJourneyLink"
        ScheduleToCloseTimeout: "720h"
        StartToCloseTimeout: "5m"
        RetryParams:
          RegularInterval:
            Interval: "1h"
            MaxAttempts: 721
      - ActivityName: "AbflPwaCreateLoanAccount"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 900
      - ActivityName: "CheckKycEligibilityV2"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # The activity checks whether a user is full-KYC or not and accordingly initiates KYC flow
          # Regular retry strategy that runs for 40 minutes at 2 second interval
          # Retry interval - 2s 2s 2s 2s ...
          RegularInterval:
            Interval: "2s"
            MaxAttempts: 1800
      - ActivityName: "SgKycAmlStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 900
      - ActivityName: "SgGetCkycUserVerificationStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 1.1
            MaxAttempts: 900
      - ActivityName: "SgInitiateVkyc"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 900
      - ActivityName: "SgCheckForRiskScreenActor"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 900
      - ActivityName: "SgVkycStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 900
      - ActivityName: "SgCheckVkycStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 900
      - ActivityName: "SgInitiateEmploymentCheck"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 900
      - ActivityName: "SgEmploymentCheckStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 900
      - ActivityName: "ContactabilityCheck"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 1.1
            MaxAttempts: 900
      - ActivityName: "SgOnboardingAtVendor"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 900
      - ActivityName: "SgKycDocumentDownload"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          RegularInterval:
            Interval: "1h"
            MaxAttempts: 75
      - ActivityName: "SgKycDataVerification"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          RegularInterval:
            Interval: "1h"
            MaxAttempts: 75
      - ActivityName: "SgUpdateUserDetailsAtVendor"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 900
      - ActivityName: "IsFiCoreUser"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 65
      - ActivityName: "IsBasicAddressCollected"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 65
      - ActivityName: "ExecuteOnboardingStage"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "1m"
        RetryParams:
          RegularInterval:
            Interval: "1h"
            MaxAttempts: 72
      - ActivityName: "UpdateBankingDetailsAtSg"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 900
      - ActivityName: "SgVendorBre"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 900
      - ActivityName: "SgInitiateLivenessAndStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 900
      - ActivityName: "SgInitiateDrawdown"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 900
      - ActivityName: "SgInitiatePennyDrop"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 900
      - ActivityName: "SgPennyDropStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 900
      - ActivityName: "SgGetDrawdownStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 900
      - ActivityName: "SgInitiateDisbursal"
        ScheduleToCloseTimeout: "720h" # around 30days
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "60m" # will reach after around 4hrs
            BackoffCoefficient: 1.5
            MaxAttempts: 737
      - ActivityName: "SgGetDisbursalStatus"
        ScheduleToCloseTimeout: "720h" # around 30days
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "60m" # will reach after around 4hrs
            BackoffCoefficient: 1.5
            MaxAttempts: 737
      - ActivityName: "SgInitiateEsign"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 900
      - ActivityName: "SgGetEsignStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 900
      - ActivityName: "SgLoanAccountCreation"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 900
      - ActivityName: "SgGetMandateStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 900
      - ActivityName: "ExecuteInSync"
        ScheduleToCloseTimeout: "2m"
        StartToCloseTimeout: "30s"
        RetryParams:
          RegularInterval:
            Interval: "1s"
            MaxAttempts: 10
      - ActivityName: "UpdateSyncStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 100
      - ActivityName: "SgInitiateMandate"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 900
      - ActivityName: "SgInitiateKyc"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 900
      - ActivityName: "SgGetOTPVerificationStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 100
      - ActivityName: "SgCkycStatusAndSetVerificationScreen"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 900
      - ActivityName: "ContactabilityVerify"
        ScheduleToCloseTimeout: "55h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 65
      - ActivityName: "ProcessKycCheckV2"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # 1s, 2s, 4s, 8s, 16s, 32s, 1m4s, 2m8s, 4m16s, 8m32s, 17m4s, 30m, 30m...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 150
      - ActivityName: "CheckAuthLivenessV2"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # 1s, 2s, 4s, 8s, 16s, 32s, 1m4s, 2m8s, 4m16s, 8m32s, 10m, 10m, 10m, 10m, 10m....
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2.0
            MaxAttempts: 100
      - ActivityName: "GetFeatureVersion"
        ScheduleToCloseTimeout: "12h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 23
      - ActivityName: "FederalPreKfs"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 100
      - ActivityName: "FederalProcessLoanAccountCreationV2"
        ScheduleToCloseTimeout: "97h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 4 days with max cap between retries at 1 hour
          # Retry interval - 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h 1h 1h 1h 1h 1h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "1h"
            BackoffCoefficient: 2.0
            MaxAttempts: 105
      - ActivityName: "CheckKfsStatusV2"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # 1s, 2s, 4s, 8s, 16s, 32s, 1m4s, 2m8s, 4m16s, 8m32s, 17m4s, 30m, 30m, 30m
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 155
      - ActivityName: "FedKfsInSync"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "1m"
        RetryParams:
          RegularInterval:
            Interval: "1h"
            MaxAttempts: 72
      - ActivityName: "CheckForLimitChangeV2"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # 1s, 2s, 4s, 8s, 16s, 32s, 1m4s, 2m8s, 4m16s, 5m, 5m, 5m, ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 100
      - ActivityName: "ProcessLivenessVendorReviewV2"
        ScheduleToCloseTimeout: "30m"
        StartToCloseTimeout: "5m"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "FederalProcessLoanApplicationV2"
        ScheduleToCloseTimeout: "92h"
        StartToCloseTimeout: "2m"
        RetryParams:
          # Exponential retry strategy that runs for 4 days with max cap between retries at 1 hour
          # Retry interval - 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h 1h 1h 1h 1h 1h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "1h"
            BackoffCoefficient: 2.0
            MaxAttempts: 105
      - ActivityName: "FederalCheckProfileValidationV2"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 2 days with max cap between retries at 30 minutes
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m 30m 30m 30m 30m ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2.0
            MaxAttempts: 106
      - ActivityName: "LLCheckCkyc"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 2 days with max cap between retries at 2 hrs
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "CreateLoanStepExecution"
        ScheduleToCloseTimeout: "2h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "UpdateLoanStepExecution"
        ScheduleToCloseTimeout: "2h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "LLFetchOffer"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 2 hrs
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 70
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "LLESCheckCkyc"
        ScheduleToCloseTimeout: "50h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 2 days with max cap between retries at 2 hrs
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "LLESSi"
        ScheduleToCloseTimeout: "10h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 2
            MaxAttempts: 120
      - ActivityName: "LLCreateApplicant"
        ScheduleToCloseTimeout: "48h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 2 days with max cap between retries at 2 hrs
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "LLAddAddressDetailsV2"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 24 hours  with max cap between retries at 1 hr
          # Retry interval -  1s 2s 4s 8s 16s ....till 1hr 1hr
          # After around 2 hours, 12 attempts, the interval reaches around 1hr, Post this all intervals will be 1hr
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "LLAddEmploymentDetailsV2"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 24 hours  with max cap between retries at 1 hr
          # Retry interval -  1s 2s 4s 8s 16s ....till 1hr 1hr
          # After around 2 hours, 12 attempts, the interval reaches around 1hr, Post this all intervals will be 1hr
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "LLAddEmploymentDetails"
        ScheduleToCloseTimeout: "50h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 2 days with max cap between retries at 2 hrs
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "LLAddEmploymentDetailsWithSalary"
        ScheduleToCloseTimeout: "50h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 2 days with max cap between retries at 2 hrs
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "LLAddAddressDetails"
        ScheduleToCloseTimeout: "50h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 2 days with max cap between retries at 2 hrs
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "LLCheckLivenessFacematch"
        ScheduleToCloseTimeout: "48h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # 1s, 2s, 4s, 8s, 16s, 32s, 1m4s, 2m8s, 4m16s, 8m32s, 17m4s, 30m, 30m
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 105
      - ActivityName: "LLGetMandateUrl"
        ScheduleToCloseTimeout: "50h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 2 days with max cap between retries at 2 hrs
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "LLGetMandateStatus"
        ScheduleToCloseTimeout: "50h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 2 days with max cap between retries at 2 hrs
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "LLGetAddressStatus"
        ScheduleToCloseTimeout: "50h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 2 days with max cap between retries at 2 hrs
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "LLGetEmploymentStatus"
        ScheduleToCloseTimeout: "50h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 2 days with max cap between retries at 2 hrs
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "UpdateLRNextAction"
        ScheduleToCloseTimeout: "2h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "GetAlternateOfferOrFailNextAction"
        ScheduleToCloseTimeout: "2h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "LLGetESignAgreementDocument"
        ScheduleToCloseTimeout: "50h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 2 days with max cap between retries at 2 hrs
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "LLUploadDocument"
        ScheduleToCloseTimeout: "50h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "LLAsyncStepTerminalStatusCheck"
        ScheduleToCloseTimeout: "50h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # 1s, 2s, 4s, 8s, 16s, 32s, 1m4s, 2m8s, 4m16s, 8m32s, 17m4s, 20m, 20m, 20m...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "20m"
            BackoffCoefficient: 2
            MaxAttempts: 150
      - ActivityName: "LLDrawdown"
        ScheduleToCloseTimeout: "50h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "LLCheckDisbursal"
        ScheduleToCloseTimeout: "720h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 30 days with max cap between retries at 24h
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "24h"
            BackoffCoefficient: 2
            MaxAttempts: 50
      - ActivityName: "GetLoanRequest"
        ScheduleToCloseTimeout: "4h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "IsFeatureEnabled"
        ScheduleToCloseTimeout: "4h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "GetLoanOffer"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "2m"
            BackoffCoefficient: 3
            MaxAttempts: 10
      - ActivityName: "UpdateLoanRequest"
        ScheduleToCloseTimeout: "4h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "LLCheckApplicantStatus"
        ScheduleToCloseTimeout: "50h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "GetBankingStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 2h 2h 2h 2h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "AddBankingDetails"
        ScheduleToCloseTimeout: "50h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 2 days with max cap between retries at 2 hrs
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "AddAadhaarDetails"
        ScheduleToCloseTimeout: "50h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 2 days with max cap between retries at 2 hrs
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "AddLeadDetails"
        ScheduleToCloseTimeout: "50h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 2 days with max cap between retries at 2 hrs
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.

      - ActivityName: "EmitEvent"
        ScheduleToCloseTimeout: "60s"
        StartToCloseTimeout: "5m"
        RetryParams:
          RegularInterval:
            Interval: "2s"
            MaxAttempts: 30
      - ActivityName: "CheckForRisk"
        ScheduleToCloseTimeout: "2h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 2
            MaxAttempts: 30
      - ActivityName: "GenerateKfsDocumentInternally"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 24 hours  with max cap between retries at 1 hr
          # Retry interval -  1s 2s 4s 8s 16s ....till 1hr 1hr
          # After around 2 hours, 12 attempts, the interval reaches around 1hr, Post this all intervals will be 1hr
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "CheckForRiskV2"
        ScheduleToCloseTimeout: "2h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 2
            MaxAttempts: 30
      - ActivityName: "CheckForLimit"
        ScheduleToCloseTimeout: "2h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 2
            MaxAttempts: 30
      - ActivityName: "CheckForRiskScreenActor"
        ScheduleToCloseTimeout: "48h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Regular interval of 2s is followed for the first 60 seconds.
          # Post that another regular interval of 5m is followed for 5 hours (5 hours)
          Hybrid:
            RetryStrategy1:
              # Exponential retry strategy that runs for ~3 hours with max cap between retries at 5 min
              # Retry interval - 1s 4s 8s 32s 2m8s 8m32s
              ExponentialBackOff:
                BaseInterval: "1s"
                MaxInterval: "9m"
                BackoffCoefficient: 4.0
                MaxAttempts: 6
            RetryStrategy2:
              RegularInterval:
                Interval: "20m"
                MaxAttempts: 144
            MaxAttempts: 150
            CutOff: 6
      - ActivityName: "IdfcGetMandatePage"
        ScheduleToCloseTimeout: "50h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 2 days with max cap between retries at 2 hrs
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "IdfcGetMandateStatus"
        ScheduleToCloseTimeout: "50h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
      - ActivityName: "RefreshLoanScheduleFromVendor"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 10
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "IdfcValidateOffer"
        ScheduleToCloseTimeout: "50h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "IdfcLoanStatus"
        ScheduleToCloseTimeout: "50h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "IdfcPanVerification"
        ScheduleToCloseTimeout: "50h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "IdfcValidatePan"
        ScheduleToCloseTimeout: "50h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "IdfcPanStatus"
        ScheduleToCloseTimeout: "50h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "IdfcDobVerification"
        ScheduleToCloseTimeout: "50h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "IdfcCheckCkyc"
        ScheduleToCloseTimeout: "50h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "IdfcCheckLivenessFacematch"
        ScheduleToCloseTimeout: "48h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "15m"
            BackoffCoefficient: 2
            MaxAttempts: 200
      - ActivityName: "IdfcCkycStatus"
        ScheduleToCloseTimeout: "50h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "IdfcOccupationVerification"
        ScheduleToCloseTimeout: "50h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "IdfcDrawdown"
        ScheduleToCloseTimeout: "50h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "IdfcUploadLivenessFacematchDetails"
        ScheduleToCloseTimeout: "50h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "IdfcGenerateKfsDocument"
        ScheduleToCloseTimeout: "50h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "IdfcCheckLoanStatus"
        ScheduleToCloseTimeout: "50h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "IdfcVkyc"
        ScheduleToCloseTimeout: "50h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 2 days with max cap between retries at 2 hrs
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "InitiateIdfcVkyc"
        ScheduleToCloseTimeout: "50h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 2 days with max cap between retries at 2 hrs
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
      - ActivityName: "CheckVkycStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # The activity checks polls for the status of vkyc from vendor (idfc) and updates the status in our system
          # Regular retry strategy that runs every 5 minutes for 3 days
          # Retry interval - 5m 5m 5m 5m ...
          RegularInterval:
            Interval: "5m"
            MaxAttempts: 864 # 3 * 24 * 12 / 5
      - ActivityName: "AsyncStepTerminalStatusCheck"
        ScheduleToCloseTimeout: "4h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 2
            MaxAttempts: 30
      - ActivityName: "IdfcDisbursal"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 2h 2h 2h 2h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "CheckForRiskV3"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 100
      - ActivityName: "PanUniqueCheck"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "10m"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "AbflPerformCkyc"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h" # 2hrs 10mins
            BackoffCoefficient: 2
            MaxAttempts: 83
      - ActivityName: "AbflPollCkycStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h" # 2hrs 10mins
            BackoffCoefficient: 2
            MaxAttempts: 83
      - ActivityName: "AbflPerformBre"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h" # 2hrs 10mins
            BackoffCoefficient: 2
            MaxAttempts: 83
      - ActivityName: "AbflPollBreStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h" # 2hrs 10mins
            BackoffCoefficient: 2
            MaxAttempts: 83
      - ActivityName: "CaptureSelfie"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "30m" # around 2hrs
            BackoffCoefficient: 1.5
            MaxAttempts: 160
      - ActivityName: "AbflPerformUkyc"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h" # 2hrs 10mins
            BackoffCoefficient: 2
            MaxAttempts: 83
      - ActivityName: "AbflPollUkycStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h" # 2hrs 10mins
            BackoffCoefficient: 2
            MaxAttempts: 83
      - ActivityName: "AbflGetMandateUrl"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h" # 2hrs 10mins
            BackoffCoefficient: 2
            MaxAttempts: 83
      - ActivityName: "AbflGetMandateStatus"
        ScheduleToCloseTimeout: "50h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 2
            MaxAttempts: 600 #TODO: Since we don't have signal for it, retry are very aggressive. Implement using signal for less retries
      - ActivityName: "AbflGetDigilockerUrl"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h" # 2hrs 10mins
            BackoffCoefficient: 2
            MaxAttempts: 83
      - ActivityName: "AbflGetDigilockerStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "60s" # in around 34 mins
            BackoffCoefficient: 1.03
            MaxAttempts: 4430 #TODO: Since we don't have signal for it, retry are very aggressive. Implement using signal for less retries
      - ActivityName: "AbflLatLong"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h" # 2hrs 10mins
            BackoffCoefficient: 2
            MaxAttempts: 83
      - ActivityName: "AbflPennyDrop"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h" # 2hrs 10mins
            BackoffCoefficient: 2
            MaxAttempts: 83
      - ActivityName: "AbflPennyDropStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h" # 2hrs 10mins
            BackoffCoefficient: 2
            MaxAttempts: 83
      - ActivityName: "AbflAddCommonDetails"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h" # 2hrs 10mins
            BackoffCoefficient: 2
            MaxAttempts: 83
      - ActivityName: "SetAbflKycDetailsScreen"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h" # 2hrs 10mins
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "AbflGetVerifyKycStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "30m" # around 2hrs
            BackoffCoefficient: 1.5
            MaxAttempts: 160
      - ActivityName: "GetAgreementSignStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "30m" # around 2hrs
            BackoffCoefficient: 1.5
            MaxAttempts: 160
      - ActivityName: "AbflUploadEsignDocument"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h" # 2hrs 10mins
            BackoffCoefficient: 2
            MaxAttempts: 83
      - ActivityName: "AbflRecalculateLoanValues"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h" # 2hrs 10mins
            BackoffCoefficient: 2
            MaxAttempts: 83
      - ActivityName: "AbflGetESignStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h" # 2hrs 10mins
            BackoffCoefficient: 2
            MaxAttempts: 83
      - ActivityName: "AbflPerformDisbursement"
        ScheduleToCloseTimeout: "720h" # around 30days
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "60m" # will reach after around 4hrs
            BackoffCoefficient: 1.5
            MaxAttempts: 737
      - ActivityName: "AbflGenerateKfsDocument"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h" # 2hrs 10mins
            BackoffCoefficient: 2
            MaxAttempts: 40
      - ActivityName: "AbflPollDisbursementStatus"
        ScheduleToCloseTimeout: "720h" # around 30days
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "60m" # will reach after around 4hrs
            BackoffCoefficient: 1.5
            MaxAttempts: 737
      - ActivityName: "AbflCreateLoanAccount"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h" # 2hrs 10mins
            BackoffCoefficient: 2
            MaxAttempts: 35
      # ======== LENDEN ACTIVITIES : START =========
      - ActivityName: "LdcUpdateUserSelectedOffer"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "30m" # around 2hrs
            BackoffCoefficient: 1.5
            MaxAttempts: 160
      - ActivityName: "LdcInitKyc"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "30m" # around 2hrs
            BackoffCoefficient: 1.5
            MaxAttempts: 160
      - ActivityName: "LdcCheckKycStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        # This activity runs in sync-proxy mode, hence no need for having retries that are too close apart
        # which is usually the case in exponential backoff.
        RetryParams:
          RegularInterval:
            Interval: "1h"
            MaxAttempts: 721
      - ActivityName: "LdcCheckMandateStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        # This activity runs in sync-proxy mode, hence no need for having retries that are too close apart
        # which is usually the case in exponential backoff.
        RetryParams:
          RegularInterval:
            Interval: "1h"
            MaxAttempts: 721
      - ActivityName: "LdcGenerateKfsDocs"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "30m" # around 2hrs
            BackoffCoefficient: 1.5
            MaxAttempts: 160
      - ActivityName: "LDCSignKFSLADocs"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "30m" # around 2hrs
            BackoffCoefficient: 1.5
            MaxAttempts: 160
      - ActivityName: "LDCAllowROIModification"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "30m" # around 2hrs
            BackoffCoefficient: 1.5
            MaxAttempts: 160
      - ActivityName: "LDCInitiateReKfsLaEsign"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "30m" # around 2hrs
            BackoffCoefficient: 1.5
            MaxAttempts: 160
      - ActivityName: "LdcDisbursal"
        ScheduleToCloseTimeout: "240h" # 10 days
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "3h"
            BackoffCoefficient: 2
            MaxAttempts: 500
      # ======== LENDEN ACTIVITIES : END ===========
      - ActivityName: "GetAddressStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "30m" # around 2hrs
            BackoffCoefficient: 1.5
            MaxAttempts: 160
      - ActivityName: "GetEmploymentStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "30m" # around 2hrs
            BackoffCoefficient: 1.5
            MaxAttempts: 160
      - ActivityName: "GetReferencesAdditionStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "30m" # around 2hrs
            BackoffCoefficient: 1.5
            MaxAttempts: 160
      - ActivityName: "FiftyfinGetAdditionalKycDetailScreen"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "5s"
            BackoffCoefficient: 1.01
            MaxAttempts: 200
      - ActivityName: "CheckFiftyfinAdditionalKycDetailsInputStatus"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "5s"
            BackoffCoefficient: 1.01
            MaxAttempts: 200
      - ActivityName: "FiftyfinSubmitAdditionalKycDetail"
        ScheduleToCloseTimeout: "30m"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5s"
            BackoffCoefficient: 1.1
            MaxAttempts: 400
      - ActivityName: "FiftyfinKyc"
        ScheduleToCloseTimeout: "30m"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5s"
            BackoffCoefficient: 1.1
            MaxAttempts: 400
      - ActivityName: "FiftyfinGetKycStatus"
        ScheduleToCloseTimeout: "12h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "30s"
            BackoffCoefficient: 1.01
            MaxAttempts: 700
      - ActivityName: "FiftyfinKycSetNextAction"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "30s"
            BackoffCoefficient: 1.01
            MaxAttempts: 700
      - ActivityName: "FiftyfinFetchMfPortfolio"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "3s"
            MaxInterval: "5s"
            BackoffCoefficient: 1.03
            MaxAttempts: 800
      - ActivityName: "FiftyfinFundVerification"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "10m"
            BackoffCoefficient: 2
            MaxAttempts: 160
      - ActivityName: "FiftyfinFundVerificationV2"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "10m"
            BackoffCoefficient: 2
            MaxAttempts: 160
      - ActivityName: "FiftyfinWaitForUserAction"
        ScheduleToCloseTimeout: "168h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10m"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 200
      - ActivityName: "FiftyfinWaitForLoanVerifyUserAction"
        ScheduleToCloseTimeout: "168h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10m"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 200
      - ActivityName: "SetOtpDataForLienMark"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "10s"
            BackoffCoefficient: 1.1
            MaxAttempts: 1500
      - ActivityName: "GetLienMarkOtpStatus"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "30s"
            BackoffCoefficient: 1.01
            MaxAttempts: 4000
      - ActivityName: "ValidateMarkedLien"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "30s"
            BackoffCoefficient: 1.01
            MaxAttempts: 12000
      - ActivityName: "ValidateLoanAmount"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "30s"
            BackoffCoefficient: 1.01
            MaxAttempts: 12000
      - ActivityName: "FiftyfinLoanAccountCreation"
        ScheduleToCloseTimeout: "240h"
        StartToCloseTimeout: "3m"
        RetryParams:
          RegularInterval:
            Interval: "10m"
            MaxAttempts: 1440
      - ActivityName: "FiftyfinInitLoan"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "30s"
            BackoffCoefficient: 1.1
            MaxAttempts: 10000
      - ActivityName: "FiftyfinCreateLoan"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "30s"
            BackoffCoefficient: 1.1
            MaxAttempts: 10000
      - ActivityName: "FiftyfinLinkBankAccount"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "30s"
            BackoffCoefficient: 1.1
            MaxAttempts: 10000
      - ActivityName: "FiftyfinGetEsignMandatePage"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "30s"
            BackoffCoefficient: 1.1
            MaxAttempts: 10000
      - ActivityName: "FiftyfinSetDrawdownSuccessScreen"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "30s"
            BackoffCoefficient: 1.1
            MaxAttempts: 10000
      - ActivityName: "FiftyfinGetApplicationProcessStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "5m"
            BackoffCoefficient: 1.5
            MaxAttempts: 1000
      - ActivityName: "FiftyfinSendLienMarkSuccessComms"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "5m"
            BackoffCoefficient: 1.2
            MaxAttempts: 1800
      - ActivityName: "FiftyfinSendLoanDisbursedSuccessComms"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "5m"
            BackoffCoefficient: 1.2
            MaxAttempts: 1800
      - ActivityName: "FiftyfinVoidLoan"
        ScheduleToCloseTimeout: "168h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 200
      - ActivityName: "FiftyfinSetLoanResetNextAction"
        ScheduleToCloseTimeout: "168h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 200
      - ActivityName: "FiftyfinLoanResetLoanWaitForUserAction"
        ScheduleToCloseTimeout: "720h"
        StartToCloseTimeout: "5m"
        RetryParams:
          RegularInterval:
            Interval: "1h"
            MaxAttempts: 720
      - ActivityName: "MvValidateConsent"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            BackoffCoefficient: 2.0
            MaxInterval: "30m"
            MaxAttempts: 10
      - ActivityName: "MvCreateLead"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            BackoffCoefficient: 2.0
            MaxInterval: "30m"
            MaxAttempts: 60
      - ActivityName: "MvFetchOffer"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            BackoffCoefficient: 2.0
            MaxInterval: "30m"
            MaxAttempts: 60
      # retry time window is set in accordance with the expected expiry of PWA flow at vendor's end
      - ActivityName: "MvProcessVendorPwaStages"
        ScheduleToCloseTimeout: "1440h" # 60 days
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            BackoffCoefficient: 2.5
            MaxInterval: "24h"
            MaxAttempts: 550
      - ActivityName: "MvProcessAccountCreation"
        ScheduleToCloseTimeout: "120h"
        StartToCloseTimeout: "5m"
        # retry for around 15 mins with exponential backoff strategy i.e retry should happen at 2s, 4s, 8s, 10s, 20s, 40s .....
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            BackoffCoefficient: 2.0
            MaxInterval: "60m"
            MaxAttempts: 100
      - ActivityName: "CheckPanAadhaarLinkedStatus"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 2 hrs
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 2h 2h 2h 2h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 24
      - ActivityName: "AsyncLoanStepStateCheck"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 2 hrs
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 2h 2h 2h 2h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
      - ActivityName: "DeactivateLoanOffer"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 2 hrs
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 2h 2h 2h 2h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
      - ActivityName: "GetExpiredPropertyForStage"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 16
      - ActivityName: "FiftyfinUpdateUserDetails"
        ScheduleToCloseTimeout: "2m"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "10s"
            BackoffCoefficient: 2
            MaxAttempts: 5
      - ActivityName: "SetCreditReportFetchScreen"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 2 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 2h 2h 2h 2h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
      - ActivityName: "FedRealtimeOfferCheck"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 2 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 2h 2h 2h 2h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
      - ActivityName: "FedRealtimeLoanAccountCreation"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 2 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 2h 2h 2h 2h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
      - ActivityName: "FedRealtimeLoanAccountCreationStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 2 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 2h 2h 2h 2h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
      - ActivityName: "FederalLoanDisbursement"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 2 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 2h 2h 2h 2h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
      - ActivityName: "FederalLoanDisbursementEnquiry"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 2 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 2h 2h 2h 2h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
      - ActivityName: "CreditReportFetchStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 2 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 2h 2h 2h 2h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
      - ActivityName: "FetchUserActionStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 2 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 2h 2h 2h 2h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
      - ActivityName: "LLCreateRps"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 24 hours  with max cap between retries at 1 hr
          # Retry interval -  1s 2s 4s 8s 16s ....till 1hr 1hr
          # After around 2 hours, 12 attempts, the interval reaches around 1hr, Post this all intervals will be 1hr
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "AddLeadDetailsV2"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 24 hours  with max cap between retries at 1 hr
          # Retry interval -  1s 2s 4s 8s 16s ....till 1hr 1hr
          # After around 2 hours, 12 attempts, the interval reaches around 1hr, Post this all intervals will be 1hr
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "PartnerLmsUserCreation"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 24 hours  with max cap between retries at 1 hr
          # Retry interval -  1s 2s 4s 8s 16s ....till 1hr 1hr
          # After around 2 hours, 12 attempts, the interval reaches around 1hr, Post this all intervals will be 1hr
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "PartnerLmsLoanCreation"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 24 hours  with max cap between retries at 1 hr
          # Retry interval -  1s 2s 4s 8s 16s ....till 1hr 1hr
          # After around 2 hours, 12 attempts, the interval reaches around 1hr, Post this all intervals will be 1hr
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "PartnerLmsLoanDisbursal"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 24 hours  with max cap between retries at 1 hr
          # Retry interval -  1s 2s 4s 8s 16s ....till 1hr 1hr
          # After around 2 hours, 12 attempts, the interval reaches around 1hr, Post this all intervals will be 1hr
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "LlRtSubvenVendorBreCheck"
        ScheduleToCloseTimeout: "55h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for ~2.5 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
      - ActivityName: "FedInitiatePennyDrop"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 24 hours  with max cap between retries at 1 hr
          # Retry interval -  1s 2s 4s 8s 16s ....till 1hr 1hr
          # After around 2 hours, 12 attempts, the interval reaches around 1hr, Post this all intervals will be 1hr
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 83
      - ActivityName: "FedPennyDropStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 24 hours  with max cap between retries at 1 hr
          # Retry interval -  1s 2s 4s 8s 16s ....till 1hr 1hr
          # After around 2 hours, 12 attempts, the interval reaches around 1hr, Post this all intervals will be 1hr
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 83
      - ActivityName: "FedSetupMandateIntroScreen"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 24 hours  with max cap between retries at 1 hr
          # Retry interval -  1s 2s 4s 8s 16s ....till 1hr 1hr
          # After around 2 hours, 12 attempts, the interval reaches around 1hr, Post this all intervals will be 1hr
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "FedSetupPennyDropIntroScreen"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 24 hours  with max cap between retries at 1 hr
          # Retry interval -  1s 2s 4s 8s 16s ....till 1hr 1hr
          # After around 2 hours, 12 attempts, the interval reaches around 1hr, Post this all intervals will be 1hr
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "FedGetMandateStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 24 hours  with max cap between retries at 1 hr
          # Retry interval -  1s 2s 4s 8s 16s ....till 1hr 1hr
          # After around 2 hours, 12 attempts, the interval reaches around 1hr, Post this all intervals will be 1hr
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 83
      - ActivityName: "VendorHardOfferCreation"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 100
      - ActivityName: "CheckHardOffer"
        ScheduleToCloseTimeout: "4h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 2
            MaxAttempts: 30
      - ActivityName: "EstimateSalary"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 100

  - WorkflowName: "LoanDataCollection"
    ActivityParamsList:
      - ActivityName: "MultiPolicyFiPreBre"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "10m"
            BackoffCoefficient: 1.5
            MaxAttempts: 200
      - ActivityName: "MultiPolicyFiBre"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "10m"
            BackoffCoefficient: 1.5
            MaxAttempts: 200
      - ActivityName: "EmitEvent"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "5m"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 30
      - ActivityName: "UpdateLRNextAction"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "3s"
            MaxInterval: "1h" # 2hrs 10mins
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "UpdateLoanRequest"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "3s"
            MaxInterval: "1h" # 2hrs 10mins
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "CreateLoanStepExecution"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "3s"
            MaxInterval: "1h" # 2hrs 10mins
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "UpdateLoanStepExecution"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "3s"
            MaxInterval: "1h" # 2hrs 10mins
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "NameGenderCheck"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 72 hours with max cap between retries at 5seconds
          # Retry interval - 1s 1.02s 1.05s 1.09s 1.15s 1.24s ....till 5s 5s 5s 5s 5s
          # After around 5:30 hours, 8050 attempts, the interval reaches around 5s, Post this all intervals will be 5s
          # 47520 more attempts with 5s interval will lead to total time around 72 hours
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55 # around 72hrs
      - ActivityName: "NameGenderCheckV2"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # Exponential retry strategy that runs for 72 hours with max cap between retries at 5seconds
          # Retry interval - 1s 1.02s 1.05s 1.09s 1.15s 1.24s ....till 5s 5s 5s 5s 5s
          # After around 5:30 hours, 8050 attempts, the interval reaches around 5s, Post this all intervals will be 5s
          # 47520 more attempts with 5s interval will lead to total time around 72 hours
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55 # around 72hrs
      - ActivityName: "CheckPanDobStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "PanDobCheck"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
      - ActivityName: "PanDobCheckV2"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "SetCreditReportFetchScreen"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 72 hours with max cap between retries at 5seconds
          # Retry interval - 1s 1.02s 1.05s 1.09s 1.15s 1.24s ....till 5s 5s 5s 5s 5s
          # After around 5:30 hours, 8050 attempts, the interval reaches around 5s, Post this all intervals will be 5s
          # 47520 more attempts with 5s interval will lead to total time around 72 hours
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55 # around 72hrs
      - ActivityName: "CreditReportFetchStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55 # around 72hrs
      - ActivityName: "CreditReportFetchStatusSync"
        ScheduleToCloseTimeout: "8s"
        StartToCloseTimeout: "2s"
        RetryParams:
          RegularInterval:
            Interval: "1s"
            MaxAttempts: 2
      - ActivityName: "FetchUserActionStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 2h 2h 2h 2h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
      - ActivityName: "AsyncStepTerminalStatusCheck"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 2h 2h 2h 2h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
      - ActivityName: "AsyncStepTerminalStatusCheckV2"
        ScheduleToCloseTimeout: "720h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 720 hours with max cap between retries at 24 hours
          # Retry interval -  1s 2s 4s 8s 16s ....till 24hr
          # After around 17 attempts, the interval reaches around 24hr, Post this all intervals will be 24hr
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "24h"
            BackoffCoefficient: 2
            MaxAttempts: 46
      - ActivityName: "FetchEpfoData"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "120s"
        RetryParams:
          # Exponential retry strategy that runs for 24 hours  with max cap between retries at 1 hr
          # Retry interval -  1s 2s 4s 8s 16s ....till 1hr 1hr
          # After around 2 hours, 12 attempts, the interval reaches around 1hr, Post this all intervals will be 1hr
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "GetAddressStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 2 days with max cap between retries at 2 hrs
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 2h 2h 2h 2h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 2 hr interval, another 55 attempts.
      - ActivityName: "GetEmploymentStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 2 days with max cap between retries at 2 hrs
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 2h 2h 2h 2h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 2 hr interval, another 55 attempts.
      - ActivityName: "DeactivateLoanOffer"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 2 hrs
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 2h 2h 2h 2h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
      - ActivityName: "UpdateLoec"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "5m"
        RetryParams:
          RegularInterval:
            Interval: "30s"
            MaxAttempts: 120
      - ActivityName: "GetAlternateOfferOrFailNextAction"
        ScheduleToCloseTimeout: "2h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "UpdateLoecDataStatus"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "120s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "LendenPreBreLoanDataCollectionStatus"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "120s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "LendenPreBreConsentStatus"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "120s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "FedBreConsentStatus"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "120s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "OfferCreation"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 100
      - ActivityName: "CheckDataCompleteness"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 100
      - ActivityName: "EstimateSalary"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 100
      - ActivityName: "IncomeEstimation"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
      - ActivityName: "IncomeEstimationStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
      - ActivityName: "FiPreBreV2"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 100
      - ActivityName: "GetAlternateOfferOrFailNextAction"
        ScheduleToCloseTimeout: "2h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "ExecuteOnboardingStage"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "1m"
        RetryParams:
          RegularInterval:
            Interval: "1h"
            MaxAttempts: 72
      - ActivityName: "ExecuteInSync"
        ScheduleToCloseTimeout: "2m"
        StartToCloseTimeout: "30s"
        RetryParams:
          RegularInterval:
            Interval: "1s"
            MaxAttempts: 10
      - ActivityName: "UpdateSyncStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 100
      - ActivityName: "WaitForDataCollection"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 100
      - ActivityName: "CreditReportFetch"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 100
      - ActivityName: "CheckHardOffer"
        ScheduleToCloseTimeout: "4h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 2
            MaxAttempts: 30
  - WorkflowName: "ProcessFiEligibleBase"
    ActivityParamsList:
      - ActivityName: "ReadCsvFromS3InBatches"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "3m"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 30
  - WorkflowName: "ProcessChildFiEligibleBase"
    ActivityParamsList:
      - ActivityName: "ProcessLOEC"
        ScheduleToCloseTimeout: "30m"
        StartToCloseTimeout: "5m"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 50
  - WorkflowName: "LoanPrePay"
    ActivityParamsList:
      - ActivityName: "LDCPaymentStatusCheck"
        ScheduleToCloseTimeout: "30m"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "5m"
            BackoffCoefficient: 1.5
            MaxAttempts: 13
      - ActivityName: "ProcessPayment"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "10m"
            BackoffCoefficient: 1.5
            MaxAttempts: 200
      - ActivityName: "PerformPaymentReconcilation"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "UpdateLoanPaymentRequest"
        ScheduleToCloseTimeout: "12h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 24
      - ActivityName: "CreateLoanStepExecution"
        ScheduleToCloseTimeout: "2h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "5m"
            BackoffCoefficient: 1.5
            MaxAttempts: 80
      - ActivityName: "UpdateLoanStepExecution"
        ScheduleToCloseTimeout: "2h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "5m"
            BackoffCoefficient: 1.5
            MaxAttempts: 80
      - ActivityName: "EmitEvent"
        ScheduleToCloseTimeout: "60s"
        StartToCloseTimeout: "5m"
        RetryParams:
          RegularInterval:
            Interval: "2s"
            MaxAttempts: 30
      - ActivityName: "RefreshMirroredLmsFromVendor"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "2m"
        RetryParams:
          # Exponential retry strategy that runs for 24 hours  with max cap between retries at 1 hr
          # Retry interval -  1s 2s 4s 8s 16s ....till 1hr 1hr
          # After around 2 hours, 12 attempts, the interval reaches around 1hr, Post this all intervals will be 1hr
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 40
      - ActivityName: "RecordPaymentInPartnerLms"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 24 hours  with max cap between retries at 1 hr
          # Retry interval -  1s 2s 4s 8s 16s ....till 1hr 1hr
          # After around 2 hours, 12 attempts, the interval reaches around 1hr, Post this all intervals will be 1hr
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "InitiateLoanAccountClosureAtLender"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "30s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 30
      - ActivityName: "CheckLoanAccountClosureStatusAtLender"
        ScheduleToCloseTimeout: "120h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "30s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 70

  - WorkflowName: "LoanPreCloseV3"
    ActivityParamsList:
      - ActivityName: "CreateLoanStepExecution"
        ScheduleToCloseTimeout: "12h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "30m"
            BackoffCoefficient: 3
            MaxAttempts: 30
      - ActivityName: "UpdateLRNextAction"
        ScheduleToCloseTimeout: "12h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "30m"
            BackoffCoefficient: 3
            MaxAttempts: 30
      - ActivityName: "EmitEvent"
        ScheduleToCloseTimeout: "60s"
        StartToCloseTimeout: "5m"
        RetryParams:
          RegularInterval:
            Interval: "1s"
            MaxAttempts: 3
      - ActivityName: "UpdateWorkflowStage"
        ScheduleToCloseTimeout: "12h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "30m"
            BackoffCoefficient: 3
            MaxAttempts: 30
      - ActivityName: "PublishWorkflowUpdateEventV2"
        ScheduleToCloseTimeout: "12h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "30m"
            BackoffCoefficient: 3
            MaxAttempts: 30
      - ActivityName: "UpdateLoanStepExecution"
        ScheduleToCloseTimeout: "12h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "30m"
            BackoffCoefficient: 3
            MaxAttempts: 30
      - ActivityName: "DeactivateLoanOffer"
        ScheduleToCloseTimeout: "12h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "30m"
            BackoffCoefficient: 3
            MaxAttempts: 30
      - ActivityName: "UpdateLRNextAction"
        ScheduleToCloseTimeout: "12h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "30m"
            BackoffCoefficient: 3
            MaxAttempts: 30
      - ActivityName: "UpdateLoanRequest"
        ScheduleToCloseTimeout: "12h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "30m"
            BackoffCoefficient: 3
            MaxAttempts: 30
      - ActivityName: "RefreshMirroredLmsFromVendor"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "2m"
        RetryParams:
          # Exponential retry strategy that runs for 24 hours  with max cap between retries at 1 hr
          # Retry interval -  1s 2s 4s 8s 16s ....till 1hr 1hr
          # After around 2 hours, 12 attempts, the interval reaches around 1hr, Post this all intervals will be 1hr
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 40
      - ActivityName: "InitiateLoanAccountClosureAtLender"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "30s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 30
      - ActivityName: "CheckLoanAccountClosureStatusAtLender"
        ScheduleToCloseTimeout: "120h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "30s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 70

  - WorkflowName: "VendorCallbackProcessor"
    ActivityParamsList:
      - ActivityName: "ProcessCallbackNotification"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 5 hours with max cap between retries at 5 min
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 5m 5m 5m 5m 5m
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 2.0
            MaxAttempts: 56

  - WorkflowName: "ProcessVendorNotification"
    ActivityParamsList:
      - ActivityName: "ProcessCallbackNotification"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 5 hours with max cap between retries at 5 min
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 5m 5m 5m 5m 5m
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 2.0
            MaxAttempts: 56
      - ActivityName: "ProcessLamfVendorNotification"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 5 hours with max cap between retries at 5 min
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 5m 5m 5m 5m 5m
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 2.0
            MaxAttempts: 56
      - ActivityName: "MvProcessVendorNotification"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            BackoffCoefficient: 2.0
            MaxInterval: "10m"
            MaxAttempts: 30
      - ActivityName: "SetuProcessVendorNotification"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            BackoffCoefficient: 2.0
            MaxInterval: "20m"
            MaxAttempts: 30


  - WorkflowName: "LoanCollectionPaymentAndReconcile"
    ActivityParamsList:
      - ActivityName: "CreateLoanStepExecution"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            BackoffCoefficient: 2.0
            MaxInterval: "30m"
            MaxAttempts: 60
      - ActivityName: "UpdateLoanStepExecution"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            BackoffCoefficient: 2.0
            MaxInterval: "30m"
            MaxAttempts: 60
      - ActivityName: "ExecuteSi"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            BackoffCoefficient: 2.0
            MaxInterval: "30m"
            MaxAttempts: 60
      - ActivityName: "LmsRefreshAndSaveApiReconcile"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            BackoffCoefficient: 2.0
            MaxInterval: "30m"
            MaxAttempts: 60
      - ActivityName: "RefreshLmsFetchAndEvaluationReconcile"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          RegularInterval:
            Interval: "30m"
            MaxAttempts: 144
      - ActivityName: "CreateLoanActivity"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            BackoffCoefficient: 2.0
            MaxInterval: "30m"
            MaxAttempts: 60

  - WorkflowName: "LoanCollection"
    ChildWorkflowParamsList:
      - WorkflowName: "LoanCollectionPaymentAndReconcile"
        WorkflowExecutionTimeout: "24h"
        WorkflowRunTimeout: "24h"
        ParentClosePolicy: 1
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "3s"
            BackoffCoefficient: 2.0
            MaxAttempts: 1
    ActivityParamsList:
      - ActivityName: "CreateLoanPaymentRequest"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            BackoffCoefficient: 2.0
            MaxInterval: "30m"
            MaxAttempts: 60
      - ActivityName: "GetRepaymentBreakup"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          RegularInterval:
            Interval: "10m"
            MaxAttempts: 150
      - ActivityName: "CreateLoanActivity"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            BackoffCoefficient: 2.0
            MaxInterval: "30m"
            MaxAttempts: 60
      - ActivityName: "RefreshLmsAndSaveApiReconcile"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            BackoffCoefficient: 2.0
            MaxInterval: "30m"
            MaxAttempts: 60
      - ActivityName: "FetchLmsAndRefreshReconcile"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            BackoffCoefficient: 2.0
            MaxInterval: "30m"
            MaxAttempts: 60
      - ActivityName: "UpdateLoanPaymentRequest"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            BackoffCoefficient: 2.0
            MaxInterval: "30m"
            MaxAttempts: 60

  - WorkflowName: "LoanEligibility"
    ActivityParamsList:
      - ActivityName: "EmitEvent"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "5m"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 30
      - ActivityName: "UpdateLRNextAction"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "3s"
            MaxInterval: "1h" # 2hrs 10mins
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "UpdateLoanRequest"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "3s"
            MaxInterval: "1h" # 2hrs 10mins
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "CreateLoanStepExecution"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "3s"
            MaxInterval: "1h" # 2hrs 10mins
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "UpdateLoanStepExecution"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "3s"
            MaxInterval: "1h" # 2hrs 10mins
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "SgRtDistFiBreCheck"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 900
      - ActivityName: "SgSetCreditReportFetchScreen"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 900
      - ActivityName: "SgFetchUserActionStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 1.1
            MaxAttempts: 900
      - ActivityName: "SgCreditReportFetchStatusSync"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 900
      - ActivityName: "SgCreditReportFetchStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 900
      - ActivityName: "NameGenderCheck"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 72 hours with max cap between retries at 5seconds
          # Retry interval - 1s 1.02s 1.05s 1.09s 1.15s 1.24s ....till 5s 5s 5s 5s 5s
          # After around 5:30 hours, 8050 attempts, the interval reaches around 5s, Post this all intervals will be 5s
          # 47520 more attempts with 5s interval will lead to total time around 72 hours
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55 # around 72hrs
      - ActivityName: "CheckPanDobStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "CreateApplicant"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 2 days with max cap between retries at 2 hrs
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "PanDobCheck"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "SetCreditReportFetchScreen"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 72 hours with max cap between retries at 5seconds
          # Retry interval - 1s 1.02s 1.05s 1.09s 1.15s 1.24s ....till 5s 5s 5s 5s 5s
          # After around 5:30 hours, 8050 attempts, the interval reaches around 5s, Post this all intervals will be 5s
          # 47520 more attempts with 5s interval will lead to total time around 72 hours
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55 # around 72hrs
      - ActivityName: "CreditReportFetchStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55 # around 72hrs
      - ActivityName: "CreditReportFetchStatusSync"
        ScheduleToCloseTimeout: "8s"
        StartToCloseTimeout: "2s"
        RetryParams:
          RegularInterval:
            Interval: "1s"
            MaxAttempts: 2
      - ActivityName: "FlFetchOffer"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "FetchUserActionStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 2h 2h 2h 2h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
      - ActivityName: "AsyncStepTerminalStatusCheck"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 2h 2h 2h 2h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
      - ActivityName: "AsyncStepTerminalStatusCheckV2"
        ScheduleToCloseTimeout: "720h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 720 hours with max cap between retries at 24 hours
          # Retry interval -  1s 2s 4s 8s 16s ....till 24hr
          # After around 17 attempts, the interval reaches around 24hr, Post this all intervals will be 24hr
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "24h"
            BackoffCoefficient: 2
            MaxAttempts: 46
      - ActivityName: "LLGetAddressStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "FlAddAddressDetails"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "LLGetEmploymentStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "FLAddEmploymentDetails"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "CreateLoec"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "5s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 8
      - ActivityName: "LLCheckApplicantStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "FlVendorBreCheck"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "FetchEpfoData"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "120s"
        RetryParams:
          # Exponential retry strategy that runs for 24 hours  with max cap between retries at 1 hr
          # Retry interval -  1s 2s 4s 8s 16s ....till 1hr 1hr
          # After around 2 hours, 12 attempts, the interval reaches around 1hr, Post this all intervals will be 1hr
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "FiBreCheck"
        ScheduleToCloseTimeout: "8h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "RtSubvenFiBreCheck"
        ScheduleToCloseTimeout: "55h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for ~2.5 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "FiPreBreCheck"
        ScheduleToCloseTimeout: "8h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 1 hour
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 1 hr interval, another 55 attempts.
      - ActivityName: "GenerateUserOffer"
        ScheduleToCloseTimeout: "30m"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "5m"
            BackoffCoefficient: 2.0
            MaxAttempts: 12
      - ActivityName: "PortfolioFetch"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "2m"
            BackoffCoefficient: 2
            MaxAttempts: 10
      - ActivityName: "FiCoreBreCheck"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 24 hours  with max cap between retries at 1 hr
          # Retry interval -  1s 2s 4s 8s 16s ....till 1hr 1hr
          # After around 2 hours, 12 attempts, the interval reaches around 1hr, Post this all intervals will be 1hr
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "FiCorePreBreCheck"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 24 hours  with max cap between retries at 1 hr
          # Retry interval -  1s 2s 4s 8s 16s ....till 1hr 1hr
          # After around 2 hours, 12 attempts, the interval reaches around 1hr, Post this all intervals will be 1hr
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "GetAddressStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 2 days with max cap between retries at 2 hrs
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 2h 2h 2h 2h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 2 hr interval, another 55 attempts.
      - ActivityName: "GetEmploymentStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 2 days with max cap between retries at 2 hrs
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 2h 2h 2h 2h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
          # Will cap at 2h after 4.5 and 16 retries. post that 2 hr interval, another 55 attempts.
      - ActivityName: "LLCreateApplicantV2"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 24 hours  with max cap between retries at 1 hr
          # Retry interval -  1s 2s 4s 8s 16s ....till 1hr 1hr
          # After around 2 hours, 12 attempts, the interval reaches around 1hr, Post this all intervals will be 1hr
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "LLAddEmploymentDetailsV2"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 24 hours  with max cap between retries at 1 hr
          # Retry interval -  1s 2s 4s 8s 16s ....till 1hr 1hr
          # After around 2 hours, 12 attempts, the interval reaches around 1hr, Post this all intervals will be 1hr
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "LLAddAddressDetailsV2"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 24 hours  with max cap between retries at 1 hr
          # Retry interval -  1s 2s 4s 8s 16s ....till 1hr 1hr
          # After around 2 hours, 12 attempts, the interval reaches around 1hr, Post this all intervals will be 1hr
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "LLCheckApplicantStatusRealTime"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 24 hours  with max cap between retries at 1 hr
          # Retry interval -  1s 2s 4s 8s 16s ....till 1hr 1hr
          # After around 2 hours, 12 attempts, the interval reaches around 1hr, Post this all intervals will be 1hr
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "LLFetchOfferRealtime"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 24 hours  with max cap between retries at 1 hr
          # Retry interval -  1s 2s 4s 8s 16s ....till 1hr 1hr
          # After around 2 hours, 12 attempts, the interval reaches around 1hr, Post this all intervals will be 1hr
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "UpdateApplicantDetails"
        ScheduleToCloseTimeout: "2h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 24 hours  with max cap between retries at 1 hr
          # Retry interval -  1s 2s 4s 8s 16s ....till 1hr 1hr
          # After around 2 hours, 12 attempts, the interval reaches around 1hr, Post this all intervals will be 1hr
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 12
      - ActivityName: "EstimateIncome"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 24 hours  with max cap between retries at 1 hr
          # Retry interval -  1s 2s 4s 8s 16s ....till 1hr 1hr
          # After around 2 hours, 12 attempts, the interval reaches around 1hr, Post this all intervals will be 1hr
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "EstimateIncomeViaItrIntimation"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 24 hours  with max cap between retries at 1 hr
          # Retry interval -  1s 2s 4s 8s 16s ....till 1hr 1hr
          # After around 2 hours, 12 attempts, the interval reaches around 1hr, Post this all intervals will be 1hr
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "IncomeEstimatePreRequisite"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 24 hours  with max cap between retries at 1 hr
          # Retry interval -  1s 2s 4s 8s 16s ....till 1hr 1hr
          # After around 2 hours, 12 attempts, the interval reaches around 1hr, Post this all intervals will be 1hr
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "DeactivateLoanOffer"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 2 hrs
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 2h 2h 2h 2h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
      - ActivityName: "UpdateLoec"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "5m"
        RetryParams:
          RegularInterval:
            Interval: "30s"
            MaxAttempts: 120
      - ActivityName: "FedAddEmploymentDetails"
        ScheduleToCloseTimeout: "8s"
        StartToCloseTimeout: "2s"
        RetryParams:
          RegularInterval:
            Interval: "1s"
            MaxAttempts: 2
      - ActivityName: "LLCreateApplicant"
        ScheduleToCloseTimeout: "48h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 2 days with max cap between retries at 2 hrs
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
      - ActivityName: "GetSetuAaConsentUrl"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 1 day with max cap between retries at 2 hrs
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 2h 2h 2h 2h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 32
      - ActivityName: "GetSetuJourneyStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 3 days with max cap between retries at 2 hrs
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 2h 2h 2h 2h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
      - ActivityName: "FedAddAddressDetails"
        ScheduleToCloseTimeout: "8s"
        StartToCloseTimeout: "2s"
        RetryParams:
          RegularInterval:
            Interval: "1s"
            MaxAttempts: 2
      - ActivityName: "GetAlternateOfferOrFailNextAction"
        ScheduleToCloseTimeout: "2h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "AddAddressDetailsV2"
        ScheduleToCloseTimeout: "8s"
        StartToCloseTimeout: "2s"
        RetryParams:
          RegularInterval:
            Interval: "1s"
            MaxAttempts: 2

  - WorkflowName: "CreateAllocation"
    ActivityParamsList:
      - ActivityName: "CreateAllocationAtCollection"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "10m"
            BackoffCoefficient: 2
            MaxAttempts: 40
      - ActivityName: "CreateAllocationAtVendor"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "10m"
            BackoffCoefficient: 2
            MaxAttempts: 40

  - WorkflowName: "UpdatePayment"
    ActivityParamsList:
      - ActivityName: "UpdatePaymentAtVendor"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "10m"
            BackoffCoefficient: 2
            MaxAttempts: 40

  - WorkflowName: "SyncAllActiveCollectionDetails"
    ActivityParamsList:
      - ActivityName: "FetchBatchActiveLeads"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "10m"
            BackoffCoefficient: 2
            MaxAttempts: 40
    ChildWorkflowParamsList:
      - WorkflowName: "SyncLeadDetails"
        WorkflowExecutionTimeout: "6h"
        WorkflowRunTimeout: "2m"
        ParentClosePolicy: 2
        RetryParams:
          # Exponential retry strategy that runs for ~20min with max cap between retries at 10min
          # Retry interval - 10s 20s 40s 1min20s 2min40s 5min20s 10min40s
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 1

  - WorkflowName: "SyncLeadDetails"
    ActivityParamsList:
      - ActivityName: "UpdateLeadDetailsAtVendor"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "1m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "10m"
            BackoffCoefficient: 2
            MaxAttempts: 1
      - ActivityName: "DownloadLeadDetailsFromVendor"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "1m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "10m"
            BackoffCoefficient: 2
            MaxAttempts: 1

  - WorkflowName: "LoanPreClose"
    ActivityParamsList:
      - ActivityName: "UpdateLoanRequest"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "10m"
            BackoffCoefficient: 2.0
            MaxAttempts: 40
      - ActivityName: "CheckPrePayStatus"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "10m"
            BackoffCoefficient: 1.5
            MaxAttempts: 45
      - ActivityName: "CloseLoanAccount"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "10m"
            BackoffCoefficient: 2.0
            MaxAttempts: 40

  - WorkflowName: "AssetPortfolioFetch"
    ActivityParamsList:
      - ActivityName: "GetWorkflowProcessingParamsV2"
        ScheduleToCloseTimeout: "2m"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "15s"
            BackoffCoefficient: 2.0
            MaxAttempts: 5
      - ActivityName: "CreateLoanStepExecution"
        ScheduleToCloseTimeout: "2m"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "15s"
            BackoffCoefficient: 2.0
            MaxAttempts: 5
      - ActivityName: "InitiateWorkflowStageV2"
        ScheduleToCloseTimeout: "2m"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "15s"
            BackoffCoefficient: 2.0
            MaxAttempts: 5
      - ActivityName: "UpdateLoanStepExecution"
        ScheduleToCloseTimeout: "2m"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "15s"
            BackoffCoefficient: 2.0
            MaxAttempts: 5
      - ActivityName: "FetchPortfolio"
        ScheduleToCloseTimeout: "6m"
        StartToCloseTimeout: "5m"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 50
      - ActivityName: "VerifyOtpStatus"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "5m"
        RetryParams:
          RegularInterval:
            Interval: "60s"
            MaxAttempts: 6
      - ActivityName: "SetOtpData"
        ScheduleToCloseTimeout: "1m"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "15s"
            BackoffCoefficient: 2
            MaxAttempts: 5
      - ActivityName: "SetUserDetailsScreen"
        ScheduleToCloseTimeout: "1m"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "15s"
            BackoffCoefficient: 2
            MaxAttempts: 5
      - ActivityName: "CheckUserDetailsStatus"
        ScheduleToCloseTimeout: "2m"
        StartToCloseTimeout: "5m"
        RetryParams:
          RegularInterval:
            Interval: "30s"
            MaxAttempts: 5
      - ActivityName: "UpdateUserDetails"
        ScheduleToCloseTimeout: "2m"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "15s"
            BackoffCoefficient: 2
            MaxAttempts: 5
      - ActivityName: "FiftyfinUpdateUserDetailsInLoanApplicant"
        ScheduleToCloseTimeout: "2m"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "15s"
            BackoffCoefficient: 2
            MaxAttempts: 5
      - ActivityName: "UpdateLoanRequestV2"
        ScheduleToCloseTimeout: "12h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 5 hours with max cap between retries at 5 min
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 5m 5m 5m 5m 5m
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "15s"
            BackoffCoefficient: 2.0
            MaxAttempts: 5
      - ActivityName: "UpdateWorkflowStage"
        ScheduleToCloseTimeout: "12h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 5 hours with max cap between retries at 5 min
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 5m 5m 5m 5m 5m
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "15s"
            BackoffCoefficient: 2.0
            MaxAttempts: 5
      - ActivityName: "PublishWorkflowUpdateEventV2"
        ScheduleToCloseTimeout: "12h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 5 hours with max cap between retries at 5 min
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 5m 5m 5m 5m 5m
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 2.0
            MaxAttempts: 130
      - ActivityName: "GetLoanRequest"
        ScheduleToCloseTimeout: "2m"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "15s"
            BackoffCoefficient: 2
            MaxAttempts: 5
      - ActivityName: "FetchMfcPortfolio"
        ScheduleToCloseTimeout: "6m"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "15s"
            BackoffCoefficient: 1.25
            MaxAttempts: 30
      - ActivityName: "FetchMfcCasSummaryPortfolio"
        ScheduleToCloseTimeout: "3m"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "15s"
            BackoffCoefficient: 1.25
            MaxAttempts: 30
      - ActivityName: "VerifyOtpSuccess"
        ScheduleToCloseTimeout: "2m"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "15s"
            BackoffCoefficient: 2
            MaxAttempts: 5
      - ActivityName: "ProcessPortfolioFetchCompletion"
        ScheduleToCloseTimeout: "2m"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "15s"
            BackoffCoefficient: 2
            MaxAttempts: 5

  - WorkflowName: "SetupSi"
    ActivityParamsList:
      - ActivityName: "CreateSi"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "10s"
            BackoffCoefficient: 1.3
            MaxAttempts: 60
      - ActivityName: "UpdateLoanRequest"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 8
      - ActivityName: "CreateLoanStepExecution"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 8
      - ActivityName: "UpdateLoanStepExecution"
        ScheduleToCloseTimeout: "30m"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 8
      - ActivityName: "UpdateLRNextAction"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 8

  - WorkflowName: "ProcessOffAppRepayment"
    ActivityParamsList:
      - ActivityName: "CreateLoanActivityEntry"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "10m"
            BackoffCoefficient: 2
            MaxAttempts: 40
      - ActivityName: "LLRecordPayment"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "30m"
            BackoffCoefficient: 2
            MaxAttempts: 60
      - ActivityName: "IDFCRecordPayment"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "30m"
            BackoffCoefficient: 2
            MaxAttempts: 60

  - WorkflowName: "LoanOffAppPrepay"
    ActivityParamsList:
      - ActivityName: "CreatePaymentLoanActivity"
        ScheduleToCloseTimeout: "12h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 24
      - ActivityName: "PerformPaymentReconcilation"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "UpdateLoanPaymentRequest"
        ScheduleToCloseTimeout: "12h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 24
      - ActivityName: "CreateLoanStepExecution"
        ScheduleToCloseTimeout: "12h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 24
      - ActivityName: "UpdateLoanStepExecution"
        ScheduleToCloseTimeout: "12h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 24
      - ActivityName: "EmitEvent"
        ScheduleToCloseTimeout: "60s"
        StartToCloseTimeout: "5m"
        RetryParams:
          RegularInterval:
            Interval: "2s"
            MaxAttempts: 30
      - ActivityName: "RefreshMirroredLmsFromVendor"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "2m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 40
      - ActivityName: "RecordPaymentInPartnerLms"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "InitiateLoanAccountClosureAtLender"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "30s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 30
      - ActivityName: "CheckLoanAccountClosureStatusAtLender"
        ScheduleToCloseTimeout: "120h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "30s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 70

  - WorkflowName: "LoanAutoPay"
    ActivityParamsList:
      - ActivityName: "CreatePaymentLoanActivity"
        ScheduleToCloseTimeout: "12h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 24
      - ActivityName: "UpdateLoanPaymentRequest"
        ScheduleToCloseTimeout: "12h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 24
      - ActivityName: "CreateLoanStepExecution"
        ScheduleToCloseTimeout: "12h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 24
      - ActivityName: "UpdateLoanStepExecution"
        ScheduleToCloseTimeout: "12h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 24
      - ActivityName: "EmitEvent"
        ScheduleToCloseTimeout: "60s"
        StartToCloseTimeout: "5m"
        RetryParams:
          RegularInterval:
            Interval: "2s"
            MaxAttempts: 30
      - ActivityName: "RefreshMirroredLmsFromVendor"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "2m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 40
      - ActivityName: "RecordPaymentInPartnerLms"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "ExecuteRecurringPayment"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 90
      - ActivityName: "PerformPaymentReconcilation"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 35

  - WorkflowName: "MutualFundNft"
    ActivityParamsList:
      - ActivityName: "CreateLoanStepExecution"
        ScheduleToCloseTimeout: "120s"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "30s"
            BackoffCoefficient: 3
            MaxAttempts: 6
      - ActivityName: "UpdateLRNextAction"
        ScheduleToCloseTimeout: "120s"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "20s"
            BackoffCoefficient: 3
            MaxAttempts: 6
      - ActivityName: "EmitEvent"
        ScheduleToCloseTimeout: "60s"
        StartToCloseTimeout: "5m"
        RetryParams:
          RegularInterval:
            Interval: "1s"
            MaxAttempts: 3
      - ActivityName: "UpdateWorkflowStage"
        ScheduleToCloseTimeout: "120s"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "20s"
            BackoffCoefficient: 3
            MaxAttempts: 6
      - ActivityName: "PublishWorkflowUpdateEventV2"
        ScheduleToCloseTimeout: "120s"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "20s"
            BackoffCoefficient: 3
            MaxAttempts: 6
      - ActivityName: "UpdateLoanStepExecution"
        ScheduleToCloseTimeout: "120s"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "20s"
            BackoffCoefficient: 3
            MaxAttempts: 6
      - ActivityName: "DeactivateLoanOffer"
        ScheduleToCloseTimeout: "120s"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "20s"
            BackoffCoefficient: 3
            MaxAttempts: 6
      - ActivityName: "UpdateLRNextAction"
        ScheduleToCloseTimeout: "120s"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "20s"
            BackoffCoefficient: 3
            MaxAttempts: 6
      - ActivityName: "UpdateLoanRequest"
        ScheduleToCloseTimeout: "120s"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "20s"
            BackoffCoefficient: 3
            MaxAttempts: 6
      - ActivityName: "FiftyfinMutualFundNftSetOtpData"
        ScheduleToCloseTimeout: "120s"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "20s"
            BackoffCoefficient: 3
            MaxAttempts: 6
      - ActivityName: "FiftyfinMutualFundNftVerifyOtpStatus"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "5m"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 60
      - ActivityName: "FiftyfinMutualFundNftVerifyOtpCompletion"
        ScheduleToCloseTimeout: "120s"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "20s"
            BackoffCoefficient: 3
            MaxAttempts: 6
      - ActivityName: "GetLoanRequest"
        ScheduleToCloseTimeout: "120s"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "20s"
            BackoffCoefficient: 3
            MaxAttempts: 6
      - ActivityName: "UpdateLoanRequestV2"
        ScheduleToCloseTimeout: "120s"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "20s"
            BackoffCoefficient: 3
            MaxAttempts: 6

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Secrets:
  Ids:
    RudderWriteKey: "prod/rudder/internal-writekey"
    TemporalCodecAesKey: "prod/temporal/codec-encryption-key"

Notification:
  VkycSuccess:
    Title: "Yay, time to move forward ➡️"
    Desc: "Your video KYC was successful! Tap to continue your loan application"
    Icon: ""
  ManualReviewSuccess:
    Title: "You are verified on Fi ✅"
    Desc: "Our bank agents have given you the go-ahead! Tap to continue your loan application"
    Icon: ""
  VkycFailed:
    Title: "There seems to be an issue 🤔"
    Desc: " Our partner bank cannot process your loan application due to a verification issue"
    Icon: ""
  ManualReviewFailed:
    Title: "Loan application cancelled"
    Desc: "According to our partner bank, it was due to a verification issue"
    Icon: ""
  LoanAccountCreation:
    Title: "Yay, good news!"
    Desc: "Your loan account number #accNum is now ready for active use ✅"
    Icon: ""
  LoanAccountClosure:
    Title: "Congrats on wrapping up the loan 🚀"
    Desc: "Your loan account number %v is now closed"
    Icon: ""
  LoanPrePay:
    Title: "Money Received"
    Desc: "You have received %v amount in your loan account %v"
    Icon: ""
  VkycPending:
    Title: "Next steps to get your loan ⬇️"
    Desc: "Complete your vKYC to proceed with the loan application."
    Icon:
  LivenessPending:
    Title: "Complete your loan application ✅"
    Desc: "Finish your liveness check to get your loan"
    Icon: ""
  ESignPending:
    Title: "🏁 Finish line to your loan"
    Desc: "Just sign the dotted line on the agreement to get your loan. Agreement expires soon - Hurry!"
    Icon:
  DropOffComms:
    Enable: true
    BlackoutTimeStart: "00:00"
    BlackoutTimeStop: "06:00"
    # Waiting times in minutes
    VkycWaitingTimes: [ 15, 1440, 2880 ]
    LivenessWaitingTimes: [ 15, 1440, 2880 ]
    ESignWaitingTimes: [ 15, 1440, 2880 ]

# TODO(@prasoon): Update once available
EarlySalary:
  Si:
    ToActor: "actor-pl-liquiloans"
    ToPiId: "PAYMENT-INSTRUMENTS-LIQUILOANS-LOAN-REPAYMENT-ACCOUNT"
    MaximumAllowedTransactions: 90
    AdditionalAmountRs: 0
    ValidityDuration:
      Day: 0
      Month: 3
      Year: 0

PgdbMigrationFlag: true

PreApprovedLoanBucketName: "epifi-prod-preapprovedloan"

RawDataDevBucketName: "epifi-raw"

RawBucketScienapticBreResponseFilePath: "vendor/bre_scienaptic/bre/%s/%s-.csv"
RawBucketScienapticBreDataFilePath: "vendor/bre_scienaptic/bre_data/%s/%s-.csv"

QuestSdk:
  Disable: false

CreditReportConfig:
  UseCreditReportV2: true

IdfcPlFlowConfig:
  IsVkycEnabled: false

OfferDeactivationConfig:
  "FEDERAL:LOAN_PROGRAM_PRE_APPROVED_LOAN":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_REAL_TIME_OFFER_NOT_FOUND"
      - "LOAN_STEP_EXECUTION_STEP_NAME_PROFILE_VALIDATION:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED"
      - "LOAN_STEP_EXECUTION_STEP_NAME_RISK_CHECK:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED"
  "FEDERAL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_OFFER_CHANGED"
      - "LOAN_STEP_EXECUTION_STEP_NAME_PROFILE_VALIDATION:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED"
      - "LOAN_STEP_EXECUTION_STEP_NAME_RISK_CHECK:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED"
  "FEDERAL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_OFFER_CREATION:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
  "LIQUILOANS:LOAN_PROGRAM_PRE_APPROVED_LOAN":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
  "LIQUILOANS:LOAN_PROGRAM_FLDG":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
  "LIQUILOANS:LOAN_PROGRAM_REALTIME_SUBVENTION":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_BRE:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
  "LIQUILOANS:LOAN_PROGRAM_REALTIME_STPL":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_BRE:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
  "LIQUILOANS:LOAN_PROGRAM_STPL":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
  "LIQUILOANS:LOAN_PROGRAM_EARLY_SALARY":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
  "IDFC:LOAN_PROGRAM_PRE_APPROVED_LOAN":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_LIVE_LOAN"
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_BAD_PERFORMANCE"
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_PREVIOUSLY_REJECTED"
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_GATING_NORMS"
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_FLAGGED_ACCOUNT"
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED"
      - "LOAN_STEP_EXECUTION_STEP_NAME_CKYC:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_REJECTED_PIN_CODE_UNSERVICEABLE"
  "MONEYVIEW:LOAN_PROGRAM_PRE_APPROVED_LOAN":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_PROCESS_VENDOR_PWA_STAGES:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED"
  "MONEYVIEW:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_PROCESS_VENDOR_PWA_STAGES:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED"
  "FIFTYFIN:LOAN_PROGRAM_LAMF":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_PHONE_ALREADY_USED"
      - "LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_EMAIL_ALREADY_USED"
  "ABFL:LOAN_PROGRAM_PRE_APPROVED_LOAN":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_BRE:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_USER_INCOME_CRITERIA_NOT_SATISFIED"
      - "LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_BRE:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED"
      - "LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_BRE:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED"
  "LIQUILOANS:LOAN_PROGRAM_NON_FI_CORE_STPL":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_BRE:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
  "LIQUILOANS:LOAN_PROGRAM_NON_FI_CORE_SUBVENTION":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_BRE:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
  "ABFL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_PROCESS_VENDOR_PWA_STAGES:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_LOAN_VERIFICATION_UNSUCCESSFUL"

SecondLookNextOfferConfig:
  "FEDERAL:LOAN_PROGRAM_PRE_APPROVED_LOAN":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_REAL_TIME_OFFER_NOT_FOUND"
      - "LOAN_STEP_EXECUTION_STEP_NAME_PROFILE_VALIDATION:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED"
      - "LOAN_STEP_EXECUTION_STEP_NAME_RISK_CHECK:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED"
  "FEDERAL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_OFFER_CHANGED"
      - "LOAN_STEP_EXECUTION_STEP_NAME_PROFILE_VALIDATION:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED"
      - "LOAN_STEP_EXECUTION_STEP_NAME_RISK_CHECK:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED"
  "FEDERAL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_OFFER_CREATION:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
  "LIQUILOANS:LOAN_PROGRAM_PRE_APPROVED_LOAN":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
  "LIQUILOANS:LOAN_PROGRAM_FLDG":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
  "LIQUILOANS:LOAN_PROGRAM_REALTIME_SUBVENTION":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_BRE:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
  "LIQUILOANS:LOAN_PROGRAM_REALTIME_STPL":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_BRE:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
  "LIQUILOANS:LOAN_PROGRAM_STPL":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
  "LIQUILOANS:LOAN_PROGRAM_EARLY_SALARY":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
  "IDFC:LOAN_PROGRAM_PRE_APPROVED_LOAN":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_LIVE_LOAN"
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_BAD_PERFORMANCE"
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_PREVIOUSLY_REJECTED"
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_GATING_NORMS"
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_FLAGGED_ACCOUNT"
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED"
      - "LOAN_STEP_EXECUTION_STEP_NAME_CKYC:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_REJECTED_PIN_CODE_UNSERVICEABLE"
  "MONEYVIEW:LOAN_PROGRAM_PRE_APPROVED_LOAN":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_PROCESS_VENDOR_PWA_STAGES:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED"
  "MONEYVIEW:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_PROCESS_VENDOR_PWA_STAGES:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED"
  "FIFTYFIN:LOAN_PROGRAM_LAMF":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_PHONE_ALREADY_USED"
      - "LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_EMAIL_ALREADY_USED"
  "ABFL:LOAN_PROGRAM_PRE_APPROVED_LOAN":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_BRE:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_USER_INCOME_CRITERIA_NOT_SATISFIED"
      - "LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_BRE:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED"
      - "LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_BRE:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED"
  "LIQUILOANS:LOAN_PROGRAM_NON_FI_CORE_STPL":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_BRE:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
  "LIQUILOANS:LOAN_PROGRAM_NON_FI_CORE_SUBVENTION":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_BRE:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
  "ABFL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_PROCESS_VENDOR_PWA_STAGES:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_LOAN_VERIFICATION_UNSUCCESSFUL"


CibilCreditReportFetchVersions:
  MinIosVersion: 500
  MinAndroidVersion: 342

Lamf:
  IntermediateMfLinkScreenWaitingTime: 2h

FeatureReleaseConfig:
  FeatureConstraints:
    FEATURE_LAMF_PHASE3_1:
      AppVersionConstraintConfig:
        MinAndroidVersion: 345
        MinIOSVersion: 501
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    LOANS_IDFC_VKYC_V2:
      AppVersionConstraintConfig:
        MinAndroidVersion: 328
        MinIOSVersion: 473
      StickyPercentageConstraintConfig:
        RolloutPercentageByPlatform:
          RolloutPercentageIOS: 0
          RolloutPercentageAndroid: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_LOAN_ADD_ALTERNATE_PHONE_NUMBER:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      AppVersionConstraintConfig:
        MinAndroidVersion: 345
        MinIOSVersion: 501
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_LOANS_NEW_DATA_COLLECTION_SCREENS:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      AppVersionConstraintConfig:
        MinAndroidVersion: 441
        MinIOSVersion: 599
    FEATURE_LOANS_INCOME_ESTIMATION_IN_SYNC:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      AppVersionConstraintConfig:
        MinAndroidVersion: 10000
        MinIOSVersion: 10000
    FEATURE_LOANS_SECOND_LOOK_V1:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      AppVersionConstraintConfig:
        MinAndroidVersion: 441
        MinIOSVersion: 605
    FEATURE_LOANS_PREQUAL_OFFER_FLOW:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      AppVersionConstraintConfig:
        MinAndroidVersion: 439
        MinIOSVersion: 602

RedisStore:
  IsSecureRedis: true
  Options:
    Addr: "redis-10246.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:10246"
    Password: ""
  AuthDetails:
    SecretPath: "prod/redis/cards/prefixaccess"
    Environment: "prod"
    Region: "ap-south-1"

Collections:
  Allocation:
    IsTotalClaimAmtNewDefEnabled: true
  # setting this flag as false since we have started using applicant_contact_details in create allocation workflow
  # and there is no need of updating in sync lead workflow
  # this flag can be enabled to true if we need to sync contact post creating allocation on credgenics
  IsContactDetailsUpdateEnabledInSyncLeadWorkflow: false

MandateConfig:
  LiquiloansMandateConfig:
    IsMandateCoolOffCheckEnabled: true
    MinCoolOffMinutesBetweenMandateAttempts: 8
    IsMandateRequestBasedCountLogicEnabled: false
    IsPreviousMandateStatusCheckEnabled: false

DeeplinkConfig:
  OfferDetailsV3Config:
    IsEnabled: true
    AppVersionConstraintConfig:
      MinAndroidVersion: 393
      MinIOSVersion: 545
    VendorLoanProgramMap:
      - "LIQUILOANS:LOAN_PROGRAM_FLDG": true
      - "LIQUILOANS:LOAN_PROGRAM_PRE_APPROVED_LOAN": true
      - "LIQUILOANS:LOAN_PROGRAM_STPL": true
      - "STOCK_GUARDIAN_LSP:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION": true
      - "FEDERAL:LOAN_PROGRAM_PRE_APPROVED_LOAN": false
      - "ABFL:LOAN_PROGRAM_PRE_APPROVED_LOAN": false
      - "FEDERAL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION": false
  AbflReferencesAppVersionConstraintConfig:
    MinAndroidVersion: 364
    MinIOSVersion: 514
  InfoItemV3MinVersion:
    MinAndroidVersion: 407
    MinIOSVersion: 568

Flags:
  HideIdfcOffer: false
  IsAbflKfsGenerationV2: true
  IsSgDigilockerEnabled: true
  IsLdcApplicationMovementEnabled: false
  PreferUPIMandateTypeForLDC: false

VendorProgramLevelFeature:
  VendorProgramActiveMap:
    "STOCK_GUARDIAN_LSP:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
      IsAllowed: true
    "FEDERAL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
      IsAllowed: true
    "FEDERAL:LOAN_PROGRAM_PRE_APPROVED_LOAN":
      IsAllowed: true
    "IDFC:LOAN_PROGRAM_PRE_APPROVED_LOAN":
      IsAllowed: false
    "LIQUILOANS:LOAN_PROGRAM_PRE_APPROVED_LOAN":
      IsAllowed: false
    "LIQUILOANS:LOAN_PROGRAM_FLDG":
      IsAllowed: false
    "LIQUILOANS:LOAN_PROGRAM_REALTIME_SUBVENTION":
      IsAllowed: false
    "LIQUILOANS:LOAN_PROGRAM_REALTIME_STPL":
      IsAllowed: false
    "LIQUILOANS:LOAN_PROGRAM_STPL":
      IsAllowed: false
    "MONEYVIEW:LOAN_PROGRAM_PRE_APPROVED_LOAN":
      IsAllowed: true
    "ABFL:LOAN_PROGRAM_PRE_APPROVED_LOAN":
      IsAllowed: true
    "LIQUILOANS:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
      IsAllowed: false
    "FEDERAL:LOAN_PROGRAM_FED_REAL_TIME":
      IsAllowed: true
    "LIQUILOANS:LOAN_PROGRAM_FI_LITE_PL":
      IsAllowed: false
    "LIQUILOANS:LOAN_PROGRAM_ACQ_TO_LEND":
      IsAllowed: false
    "LIQUILOANS:LOAN_PROGRAM_NON_FI_CORE_SUBVENTION":
      IsAllowed: false
    "LIQUILOANS:LOAN_PROGRAM_NON_FI_CORE_STPL":
      IsAllowed: false
    "ABFL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
      IsAllowed: false
    "LENDEN:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
      IsAllowed: true
    "STOCK_GUARDIAN_LSP:LOAN_PROGRAM_EARLY_SALARY_V2":
      IsAllowed: true
  NonFiCoreVendorProgramActiveMap:
    - "STOCK_GUARDIAN_LSP:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
        IsAllowed: true
    - "ABFL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
        IsAllowed: false
    - "FEDERAL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB":
        IsAllowed: true
    - "MONEYVIEW:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
        IsAllowed: true
    - "LENDEN:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
        IsAllowed: true



SkipEpfoCheck:
  - "EPIFI_TECH:LOAN_PROGRAM_ELIGIBILITY": true

SgEtbNewEligibilityFlow:
  IsAllowed: true

SgKycUrl: "https://fi.money/features/kyc/digilocker?kycApplicationId="

EligibilityNuggetBotFeature:
  DisableFeature: true
  MinIOSVersion: 10000
  MinAndroidVersion: 10000
  FallbackToEnableFeature: true
