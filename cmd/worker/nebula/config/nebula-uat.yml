Application:
  Environment: "uat"
  Namespace: "uat-nebula"
  TaskQueue: "uat-nebula-task-queue"
  RedisConfig:
    Options:
      Addr: "localhost:6379"
      Password: ""
      DB: 0
    IsSecureRedis: false

Secrets:
  Ids:
    TemporalCodecAes<PERSON>ey: "uat/temporal/codec-encryption-key"

MigrateToPgdbConf:
  Default:
    BulkParams:
      BufferTime: "30s"
      StepTime: "24h"
      BatchDelay: "0ms"
      BatchSize: 1000
      MinSyncInterval: "5s"

    DeltaParams:
      BufferTime: "30s" #Buffer time <=1s is not useful, since we already query the table as of 1 second back
      StepTime: "24h"
      BatchDelay: "0ms"
      BatchSize: 1000
      MinSyncInterval: "5s"


VortexConf:
  Default:
    BulkParams:
      BufferTime: "30s"
      StepTime: "24h"
      BatchDelay: "0ms"
      BatchSize: 1000
      MinSyncInterval: "5s"

    DeltaParams:
      BufferTime: "30s" #Buffer time <=1s is not useful, since we already query the table as of 1 second back
      StepTime: "24h"
      BatchDelay: "0ms"
      BatchSize: 1000
      MinSyncInterval: "5s"

VortexConnectorConf:
  KafkaSinkConnectorConf:
    FlushFrequency: "1s"
    FlushMessages: 5000
    FlushMaxMessages: 10000
    FlushMaxBytes: 1024

PgdbArchivalConf:
  ArchivalS3Bucket: "epifi-uat-pgdb-archive"

WorkflowUpdatePublisher:
  TopicName: "uat-celestial-workflow-update-topic"
