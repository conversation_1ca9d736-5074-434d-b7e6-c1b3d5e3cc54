Application:
  Environment: "development"
  Namespace: "firefly"
  TaskQueue: "firefly-task-queue"
  RedisConfig:
    Options:
      Addr: "localhost:6379"
      Password: ""
      DB: 0
    IsSecureRedis: false

DbConfigMap:
  EPIFI_TECH:
    DbType: "CRDB"
    Host: "localhost"
    Port: 26257
    Username: "root"
    Password: ""
    Name: "epifi"
    EnableDebug: false
    SSLMode: "disable"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

UsecaseDbConfigMap:
  EPIFI_TECH:
    DbType: "CRDB"
    Host: "localhost"
    Port: 26257
    Username: "root"
    Password: ""
    Name: "epifi"
    EnableDebug: false
    SSLMode: "disable"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

WorkflowUpdatePublisher:
  TopicName: "celestial-workflow-update-topic"

CreditCardPgDb:
  Host: "localhost"
  Port: 5432
  Name: "credit_card_pgdb"
  DbType: "PGDB"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  AppName: "firefly-worker"
  SecretName: "{\"username\": \"root\", \"password\": \"\"}"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

CreditCardDb:
  Host: "localhost"
  DbType: "CRDB"
  Port: 26257
  Username: "root"
  Password: ""
  Name: "credit_card"
  EnableDebug: true
  SSLMode: "disable"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true
    EnableMultiDBSupport: false
    DBResolverList:
      - TableName: []
        Alias: "credit_card_pgdb_conn"
        DbDsn:
          Host: "localhost"
          Port: 5432
          DbType: "PGDB"
          AppName: "firefly-worker"
          StatementTimeout: 1m
          Name: "credit_card_pgdb"
          EnableDebug: true
          SSLMode: "disable"
          SecretName: "{\"username\": \"root\", \"password\": \"\"}"

EnableActivityConfig:
  EnableProfileValidation: true

FundTransferPoolAccountActorId: "actor-creditcard-federal-pool-account"


CCTransactionEventPublisher:
  TopicName: "cc-transaction-event-topic"

CCBillGenerationEventPublisher:
  TopicName: "cc-bill-generation-event-topic"

CCStageUpdateEventPublisher:
  TopicName: "cc-stage-update-event-topic"

CCTransactionNotificationPublisher:
  QueueName: "cc-txn-notification-queue"

StatementPdfConfig:
  StatementDocumentsBucketName: "epifi-credit-card-docs"
  EnableRewardsInStatements: true
  # pausing the statement generation for 5 minutes, to generate actual rewards for statement
  StatementRewardsGenerationPauseDuration: 5m
  StatementRewardsGenerationPauseDurationDuringFcFpMigration: 5m

NotificationSchedule:
  # We will be sending notifications at 10:00 AM
  PaymentDeadlineExceededReminderAfterHours:
    email: [10]
    notification: []
    sms: [10]
  PaymentDeadlineExceededWithInterestRateReminderAfterHours:
    email: [34]
    notification: []
    sms: [34]
  BeforeDueDateReminderHours:
    email: [182,134,86,62]
    whatsapp: [14]
    notification: [182,134,86,62]
    sms: [182,134,86,62]
  OnDueDateReminderHours:
    email: [14]
    whatsapp: [14]
    notification: [14]
    sms: [14]
  AfterDueDateReminderHours:
    email: [10,34,106,178,226,274]
    whatsapp: [10,34,106,154,226,346,466]
    notification: [10,34,106,178,226,274]
    sms: [10,34,106,178,226,274]

Flags:
  EnableAddressV2Screen:
    MinAndroidVersion: 256
    MinIOSVersion: 359
    DisableFeature: false
    FallbackToEnableFeature: false
  EnableNewFdScreen:
    MinAndroidVersion: 100
    MinIOSVersion: 100
    DisableFeature: false
    FallbackToEnableFeature: false
  EnableRevampedNewFdScreen:
    MinAndroidVersion: 100
    MinIOSVersion: 100
    DisableFeature: false
    FallbackToEnableFeature: false

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Secrets:
  Ids:
    RudderWriteKey: "1eGkhVch5jk2oJBF9lrTEN4I3zB"
    TemporalCodecAesKey: "853bbce933313713af7f43bb8fcc1d84"

FeatureReleaseConfig:
  FeatureConstraints:
    CC_REGISTER_CUSTOMER_V2:
      AppVersionConstraintConfig:
        MinAndroidVersion: 263
        MinIOSVersion: 361
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 17 # CREDIT_CARD_INTERNAL
    FEATURE_CC_REGISTER_CUSTOMER_V3:
      AppVersionConstraintConfig:
        MinAndroidVersion: 10000
        MinIOSVersion: 10000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_FILITE_CIBIL_INTEGRATION:
      AppVersionConstraintConfig:
        MinAndroidVersion: 10000
        MinIOSVersion: 10000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_UPDATE_CARD_DETAILS_AT_BANK:
      AppVersionConstraintConfig:
        MinAndroidVersion: 266
        MinIOSVersion: 369
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_BE_DRIVEN_LIVENESS_SCREEN:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
    FEATURE_CC_SECURED_CARDS:
      AppVersionConstraintConfig:
        MinAndroidVersion: 200
        MinIOSVersion: 200
      StickyPercentageConstraintConfig:
        RolloutPercentageByPlatform:
          RolloutPercentageIOS: 0
          RolloutPercentageAndroid: 0
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_INHOUSE_BRE:
      AppVersionConstraintConfig:
        MinAndroidVersion: 2000
        MinIOSVersion: 2000
      StickyPercentageConstraintConfig:
        RolloutPercentageByPlatform:
          RolloutPercentageIOS: 0
          RolloutPercentageAndroid: 0
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_USER_INELIGIBLE_FEEDBACK:
      AppVersionConstraintConfig:
        MinAndroidVersion: 290
        MinIOSVersion: 1874
      StickyPercentageConstraintConfig:
        RolloutPercentageByPlatform:
          RolloutPercentageIOS: 100
          RolloutPercentageAndroid: 100
        RolloutPercentage: 100
    MASS_UNSECURED_CARD:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
    FEATURE_CC_AUTOMATIC_FEE_WAIVER:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL

QuestSdk:
  Disable: true

RepaymentNudges:
  SevenDaysPriorToDueDateNudgeId: "9009d1f6-9b58-494e-9428-c6bfd05431d2"
  OneDayPriorToDueDateNudgeId: "2f307816-ab95-4267-a4b7-d7be1d8ad279"
  DueDateNudgeId: "c0f57949-b694-410c-b2c1-5a9d6ef15a0a"
  OneDayPostDueDateNudgeId: "2f737b97-5910-4a39-8ca9-0de233b20690"

RewardCvpVersionConfigs:
  - CvpType : "REWARD_CVP_UNSECURED"
    SupportedCardProgramTypes: ["CARD_PROGRAM_TYPE_UNSECURED","CARD_PROGRAM_TYPE_UNSPECIFIED"]
    CvpVersions:
      - Name: "REWARD_CVP_UNSECURED_V1"
        StartDate:
          Year: 2023
          Month: 1
          Day: 1
        EndDate:
          Year: 2023
          Month: 11
          Day: 14
        LastStatementDate:
          Year: 2024
          Month: 1
          Day: 4
      - Name: "REWARD_CVP_UNSECURED_V2"
        StartDate:
          Year: 2023
          Month: 11
          Day: 15
  - CvpType: "REWARD_CVP_SECURED"
    SupportedCardProgramTypes: ["CARD_PROGRAM_TYPE_SECURED"]
    CvpVersions:
      - Name: "REWARD_CVP_SECURED_V1"
        StartDate:
          Year: 2023
          Month: 11
          Day: 15
  - CvpType: "REWARD_CVP_MASS_UNSECURED"
    SupportedCardProgramTypes: [ "CARD_PROGRAM_TYPE_MASS_UNSECURED" ]
    CvpVersions:
      - Name: "REWARD_CVP_MASS_UNSECURED_V1"
        StartDate:
          Year: 2023
          Month: 11
          Day: 1

# please give time in minutes in increasing order
CommunicationTypeToCommunicationParamsMap:
  COMMUNICATION_TYPE_WEB_ELIGIBILITY_DROP_OFF:
    CARD_REQUEST_STAGE_NAME_COMMUNICATION_WEB_ELIGIBILITY_UNSECURED:
      - CommNotificationType: "CREDIT_CARD_ELIGIBLE_WEB_FLOW_START_APPLICATION_SMS"
        DelayDuration: "30s"
        CommNotificationMedium: "SMS"
      - CommNotificationType: "CREDIT_CARD_ELIGIBLE_WEB_FLOW_COMPLETE_APPLICATION_SMS"
        DelayDuration: "120s"
        CommNotificationMedium: "SMS"

FireflyRedisStore:
  IsSecureRedis: false
  Options:
    Addr: "localhost:6379"
    Password: ""
    DB: 0
  ClientName: firefly
  HystrixCommand:
    CommandName: "firefly_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 200
      ExecutionTimeout: 100ms
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

ProcrastinatorWorkflowPublisher:
  QueueName: "celestial-initiate-procrastinator-workflow-queue"
