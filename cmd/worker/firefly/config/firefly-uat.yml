Application:
  Environment: "uat"
  Namespace: "uat-firefly"
  TaskQueue: "uat-firefly-task-queue"
  RedisConfig:
    IsSecureRedis: true
    Options:
      Addr: "master.uat-common-cache-redis.ud7kyq.aps1.cache.amazonaws.com:6379"
      Password: ""
      DB: 0

DbConfigMap:
  EPIFI_TECH:
    DbType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "uat/cockroach/ca.crt"
    SSLClientCert: "uat/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "uat/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "firefly-worker"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

UsecaseDbConfigMap:
  EPIFI_TECH:
    DbType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "uat/cockroach/ca.crt"
    SSLClientCert: "uat/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "uat/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "firefly-worker"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

WorkflowUpdatePublisher:
  TopicName: "uat-celestial-workflow-update-topic"

CreditCardPgDb:
  Username: "credit_card_pgdb_dev_user"
  Password: ""
  Name: "credit_card_pgdb"
  DbType: "PGDB"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  AppName: "firefly"
  SecretName: "uat/rds/epifimetis/credit_card_pgdb_dev_user"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

CreditCardDb:
  Username: "credit_card_dev_user"
  DbType: "CRDB"
  Password: ""
  Name: "credit_card"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "uat/cockroach/ca.crt"
  SSLClientCert: "uat/cockroach/client.credit_card_dev_user.crt"
  SSLClientKey: "uat/cockroach/client.credit_card_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  AppName: "firefly-worker"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true
    EnableMultiDBSupport: false
    DBResolverList:
      - TableName: [ ]
        Alias: "credit_card_pgdb_conn"
        DbDsn:
          DbType: "PGDB"
          AppName: "firefly-worker"
          StatementTimeout: 1m
          Name: "credit_card_pgdb"
          EnableDebug: true
          SSLMode: "disable"
          SecretName: "uat/rds/epifimetis/credit_card_pgdb_dev_user"

FundTransferPoolAccountActorId: "actor-creditcard-federal-pool-account"

CCTransactionEventPublisher:
  TopicName: "uat-cc-transaction-event-topic"

CCBillGenerationEventPublisher:
  TopicName: "uat-cc-bill-generation-event-topic"

CCStageUpdateEventPublisher:
  TopicName: "uat-cc-stage-update-event-topic"

CCTransactionNotificationPublisher:
  QueueName: "uat-cc-txn-notification-queue"

StatementPdfConfig:
  StatementDocumentsBucketName: "epifi-uat-credit-card-docs"
  EnableRewardsInStatements: false
  # pausing the statement generation for 3 minutes, to generate actual rewards for statement
  StatementRewardsGenerationPauseDuration: 3m
  StatementRewardsGenerationPauseDurationDuringFcFpMigration: 3m

NotificationSchedule:
  # We will be sending notifications at 10:00 AM
  PaymentDeadlineExceededReminderAfterHours:
    email: [10]
    notification: []
    sms: [10]
  PaymentDeadlineExceededWithInterestRateReminderAfterHours:
    email: [34]
    notification: []
    sms: [34]
  BeforeDueDateReminderHours:
    email: [182,134,86,62]
    whatsapp: [14]
    notification: [182,134,86,62]
    sms: [182,134,86,62]
  OnDueDateReminderHours:
    email: [14]
    whatsapp: [14]
    notification: [14]
    sms: [14]
  AfterDueDateReminderHours:
    email: [10,34,106,178,226,274]
    whatsapp: [10,34,106,154,226,346,466]
    notification: [10,34,106,178,226,274]
    sms: [10,34,106,178,226,274]

Flags:
  EnableAddressV2Screen:
    MinAndroidVersion: 256
    MinIOSVersion: 359
    DisableFeature: false
    FallbackToEnableFeature: false
  EnableNewFdScreen:
    MinAndroidVersion: 2000
    MinIOSVersion: 2000
    DisableFeature: true
    FallbackToEnableFeature: false
  EnableRevampedNewFdScreen:
    MinAndroidVersion: 2000
    MinIOSVersion: 2000
    DisableFeature: true
    FallbackToEnableFeature: false

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Secrets:
  Ids:
    RudderWriteKey: "uat/rudder/internal-writekey"
    TemporalCodecAesKey: "uat/temporal/codec-encryption-key"

FeatureReleaseConfig:
  FeatureConstraints:
    CC_REGISTER_CUSTOMER_V2:
      AppVersionConstraintConfig:
        MinAndroidVersion: 263
        MinIOSVersion: 361
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 17 # CREDIT_CARD_INTERNAL
    FEATURE_CC_REGISTER_CUSTOMER_V3:
      AppVersionConstraintConfig:
        MinAndroidVersion: 10000
        MinIOSVersion: 10000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_FILITE_CIBIL_INTEGRATION:
      AppVersionConstraintConfig:
        MinAndroidVersion: 100
        MinIOSVersion: 100
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_UPDATE_CARD_DETAILS_AT_BANK:
      AppVersionConstraintConfig:
        MinAndroidVersion: 266
        MinIOSVersion: 369
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_SECURED_CARDS:
      AppVersionConstraintConfig:
        MinAndroidVersion: 2000
        MinIOSVersion: 2000
      StickyPercentageConstraintConfig:
        RolloutPercentageByPlatform:
          RolloutPercentageIOS: 0
          RolloutPercentageAndroid: 0
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_INHOUSE_BRE:
      AppVersionConstraintConfig:
        MinAndroidVersion: 2000
        MinIOSVersion: 2000
      StickyPercentageConstraintConfig:
        RolloutPercentageByPlatform:
          RolloutPercentageIOS: 0
          RolloutPercentageAndroid: 0
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_BE_DRIVEN_LIVENESS_SCREEN:
      AppVersionConstraintConfig:
        MinAndroidVersion: 2000
        MinIOSVersion: 2000
      StickyPercentageConstraintConfig:
        RolloutPercentageByPlatform:
          RolloutPercentageIOS: 0
          RolloutPercentageAndroid: 0
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_USER_INELIGIBLE_FEEDBACK:
      AppVersionConstraintConfig:
        MinAndroidVersion: 290
        MinIOSVersion: 1874
      StickyPercentageConstraintConfig:
        RolloutPercentageByPlatform:
          RolloutPercentageIOS: 100
          RolloutPercentageAndroid: 100
        RolloutPercentage: 100
    FEATURE_CC_AUTOMATIC_FEE_WAIVER:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL

QuestSdk:
  Disable: true

RepaymentNudges:
  SevenDaysPriorToDueDateNudgeId: ""
  OneDayPriorToDueDateNudgeId: ""
  DueDateNudgeId: ""
  OneDayPostDueDateNudgeId: ""

FireflyRedisStore:
  IsSecureRedis: true
  Options:
    Addr: "master.uat-common-cache-redis.ud7kyq.aps1.cache.amazonaws.com:6379"
    Password: ""
    DB: 5
  ClientName: firefly
  HystrixCommand:
    CommandName: "firefly_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 200
      ExecutionTimeout: 100ms
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

ProcrastinatorWorkflowPublisher:
  QueueName: "uat-celestial-initiate-procrastinator-workflow-queue"
