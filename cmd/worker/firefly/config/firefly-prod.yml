Application:
  Environment: "prod"
  Namespace: "prod-firefly"
  TaskQueue: "prod-firefly-task-queue"
  RedisConfig:
    IsSecureRedis: true
    Options:
      Addr: "redis-10246.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:10246"
      Password: ""
    AuthDetails:
      SecretPath: "prod/redis/cards/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
    ClientName: firefly_worker
    HystrixCommand:
      CommandName: "firefly_redis_circuit_breaker_command"
      TemplateName: "Q20"
      OverrideTemplateConfig:
        ExecutionMaxConcurrency: 10000
        ExecutionTimeout: 1s
        RequiredConsecutiveSuccessful: 6
        HalfOpenAttemptsAllowedPerSleepWindow: 10
        ErrorThresholdPercentage: 50
        SleepWindow: 15s
        FallbackMaxConcurrency: 10000

DbConfigMap:
  EPIFI_TECH:
    DbType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 10
    MaxIdleConn: 5
    MaxConnTtl: "30m"
    AppName: "firefly-worker"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms

UsecaseDbConfigMap:
  EPIFI_TECH:
    DbType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 10
    MaxIdleConn: 5
    MaxConnTtl: "30m"
    AppName: "firefly-worker"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms

WorkflowUpdatePublisher:
  TopicName: "prod-celestial-workflow-update-topic"

CreditCardDb:
  Username: "credit_card_dev_user"
  Password: ""
  DbType: "CRDB"
  Name: "credit_card"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/cockroach/ca.crt"
  SSLClientCert: "prod/cockroach/client.credit_card_dev_user.crt"
  SSLClientKey: "prod/cockroach/client.credit_card_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false
    EnableMultiDBSupport: false
    DBResolverList:
      - TableName: [ ]
        Alias: "credit_card_pgdb_conn"
        DbDsn:
          DbType: "PGDB"
          AppName: "firefly-worker"
          StatementTimeout: 1m
          Name: "credit_card_pgdb"
          EnableDebug: true
          SSLMode: "verify-full"
          SSLRootCert: "prod/rds/rds-ca-root-2061"
          SecretName: "prod/rds/epifimetis/credit_card_pgdb_dev_user"

CreditCardPgDb:
  Username: "credit_card_pgdb_dev_user"
  Password: ""
  Name: "credit_card_pgdb"
  DbType: "PGDB"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  MaxOpenConn: 10
  MaxIdleConn: 5
  MaxConnTtl: "30m"
  AppName: "firefly-worker"
  SecretName: "prod/rds/epifimetis/credit_card_pgdb_dev_user"
  GormV2:
    # TODO(abhishekprakash) Mark log level as WARN or DEBUG once this is stable
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

EnableActivityConfig:
  # EnableProfileValidation introduced while moving profile validation stage from first stage to card creation stage
  EnableProfileValidation: true
  # EnableSkipProfileValidationForAllProgram is used to completely skip the profile validation stage
  EnableSkipProfileValidationForAllProgram: true
  EnablePanAadhaarCheck: false
  EnableActiveProductsCheck: true
  EnablePollCreditReportFeature: true
  EnablePanAadhaarValidation: true

DisableVendorCallsConfig:
  DisableBreCall: true
  DisableCardCreation: true

FundTransferPoolAccountActorId: "actor-creditcard-federal-pool-account"

CCTransactionEventPublisher:
  TopicName: "prod-cc-transaction-event-topic"

CCBillGenerationEventPublisher:
  TopicName: "prod-cc-bill-generation-event-topic"

CCStageUpdateEventPublisher:
  TopicName: "prod-cc-stage-update-event-topic"

CCTransactionNotificationPublisher:
  QueueName: "prod-cc-txn-notification-queue"

StatementPdfConfig:
  StatementDocumentsBucketName: "epifi-prod-credit-card-docs"
  EnableRewardsInStatements: false
  # pausing the statement generation for 120 minutes, to generate actual rewards for statement
  StatementRewardsGenerationPauseDuration: 120m
  # pausing the statement generation for 960 minutes(16 hrs), to generate actual rewards for statement during fi-coins to fi-points migration
  StatementRewardsGenerationPauseDurationDuringFcFpMigration: 960m

NotificationSchedule:
  # We will be sending notifications at 10:00 AM
  PaymentDeadlineExceededReminderAfterHours:
    email: [10]
    notification: []
    sms: [10]
  PaymentDeadlineExceededWithInterestRateReminderAfterHours:
    email: [34]
    notification: []
    sms: [34]
  BeforeDueDateReminderHours:
    email: [182,134,86,62]
    whatsapp: [14]
    notification: [182,134,86,62]
    sms: [182,134,86,62]
  OnDueDateReminderHours:
    email: [14]
    whatsapp: [14]
    notification: [14]
    sms: [14]
  AfterDueDateReminderHours:
    email: [10,34,106,178,226,274]
    whatsapp: [10,34,106,154,226,346,466]
    notification: [10,34,106,178,226,274]
    sms: [10,34,106,178,226,274]

Flags:
  EnableAddressV2Screen:
    MinAndroidVersion: 263
    MinIOSVersion: 361
    DisableFeature: false
    FallbackToEnableFeature: false
  EnableNewFdScreen:
    MinAndroidVersion: 311
    MinIOSVersion: 443
    DisableFeature: false
    FallbackToEnableFeature: false
  EnableRevampedNewFdScreen:
    MinAndroidVersion: 329
    MinIOSVersion: 476
    DisableFeature: false
    FallbackToEnableFeature: false

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Secrets:
  Ids:
    RudderWriteKey: "prod/rudder/internal-writekey"
    TemporalCodecAesKey: "prod/temporal/codec-encryption-key"

FeatureReleaseConfig:
  FeatureConstraints:
    CC_REGISTER_CUSTOMER_V2:
      AppVersionConstraintConfig:
        MinAndroidVersion: 250
        MinIOSVersion: 350
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_REGISTER_CUSTOMER_V3:
      AppVersionConstraintConfig:
        MinAndroidVersion: 250
        MinIOSVersion: 350
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_FILITE_CIBIL_INTEGRATION:
      AppVersionConstraintConfig:
        MinAndroidVersion: 10000
        MinIOSVersion: 10000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_UPDATE_CARD_DETAILS_AT_BANK:
      AppVersionConstraintConfig:
        MinAndroidVersion: 228
        MinIOSVersion: 329
      StickyPercentageConstraintConfig:
        RolloutPercentageByPlatform:
          RolloutPercentageIOS: 0
          RolloutPercentageAndroid: 0
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_INHOUSE_BRE:
      AppVersionConstraintConfig:
        MinAndroidVersion: 293
        MinIOSVersion: 409
      StickyPercentageConstraintConfig:
        RolloutPercentageByPlatform:
          RolloutPercentageIOS: 0
          RolloutPercentageAndroid: 100
        RolloutPercentage: 100
    FEATURE_CC_BE_DRIVEN_LIVENESS_SCREEN:
      AppVersionConstraintConfig:
        MinAndroidVersion: 2000
        MinIOSVersion: 2000
      StickyPercentageConstraintConfig:
        RolloutPercentageByPlatform:
          RolloutPercentageIOS: 0
          RolloutPercentageAndroid: 0
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_SECURED_CARDS:
      AppVersionConstraintConfig:
        MinAndroidVersion: 293
        MinIOSVersion: 423
      StickyPercentageConstraintConfig:
        RolloutPercentageByPlatform:
          RolloutPercentageIOS: 0
          RolloutPercentageAndroid: 0
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_USER_INELIGIBLE_FEEDBACK:
      AppVersionConstraintConfig:
        MinAndroidVersion: 300
        MinIOSVersion: 410
      StickyPercentageConstraintConfig:
        RolloutPercentageByPlatform:
          RolloutPercentageIOS: 0
          RolloutPercentageAndroid: 0
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    MASS_UNSECURED_CARD:
      AppVersionConstraintConfig:
        MinAndroidVersion: 317
        MinIOSVersion: 452
    CREDIT_REPORT_ADDRESS_SELECTION:
      AppVersionConstraintConfig:
        MinAndroidVersion: 2000
        MinIOSVersion: 2000
    FEATURE_CC_AUTOMATIC_FEE_WAIVER:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL

# CardProgramNudgeConfigurations is a map that defines Nudge configurations
# for different card program types and their respective request stages.
#
# It is structured as a map with the following hierarchy:
#   - Card Program Type (e.g., CARD_PROGRAM_TYPE_UNSECURED)
#     - Card Request Stage Name (e.g., CARD_REQUEST_STAGE_NAME_AUTH)
#       - Nudge Configuration (e.g., NudgeId)
CardProgramNudgeConfigurations:
  CARD_PROGRAM_TYPE_UNSECURED:
    CARD_REQUEST_STAGE_NAME_AUTH:
      NudgeId: "a2b54eef-207a-4067-85fe-f5e6fa92a33d"
    CARD_REQUEST_STAGE_NAME_ADDRESS_CAPTURE:
      NudgeId: "98be51c0-bec4-412f-a72c-f543226d88c7"
    CARD_REQUEST_STAGE_NAME_CARD_CREATION:
      NudgeId: "bd5e07a5-d2b5-4789-9445-b8abc48fc12a"
  CARD_PROGRAM_TYPE_MASS_UNSECURED:
    CARD_REQUEST_STAGE_NAME_ADDRESS_CAPTURE:
      NudgeId: "8c6bb98c-d73e-48b2-9a4e-6006257fb00e"
    CARD_REQUEST_STAGE_NAME_AUTH:
      NudgeId: "9c4562fe-a049-4ab1-bd86-29a39cf1abb7"
    CARD_REQUEST_STAGE_NAME_CARD_CREATION:
      NudgeId: "2ebf7260-8b98-45d0-b1e4-4b65532c2257"

QuestSdk:
  Disable: false

QuestRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "redis-12231.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:12231"
    Password: ""
  AuthDetails:
    SecretPath: "prod/redis/growth-infra/prefixaccess"
    Environment: "prod"
    Region: "ap-south-1"
  ClientName: ff-worker-quest-sdk
  HystrixCommand:
    CommandName: "quest_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 10000
      ExecutionTimeout: 1s
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 50
      SleepWindow: 15s
      FallbackMaxConcurrency: 10000

FireflyRedisStore:
  IsSecureRedis: true
  Options:
    Addr: "redis-10246.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:10246"
    Password: ""
  AuthDetails:
    SecretPath: "prod/redis/cards/prefixaccess"
    Environment: "prod"
    Region: "ap-south-1"
  ClientName: firefly
  HystrixCommand:
    CommandName: "firefly_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 10000
      ExecutionTimeout: 1s
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 50
      SleepWindow: 15s
      FallbackMaxConcurrency: 10000

UsePgdbConnForCcDb: true

RepaymentNudges:
  SevenDaysPriorToDueDateNudgeId: "27dbdc5e-5b63-4bfe-8443-9f7180188e10"
  OneDayPriorToDueDateNudgeId: "fcdb5fe8-daad-4a3c-813f-e2953cbb5f87"
  DueDateNudgeId: "34a1e2fe-a699-4708-b7cd-a3359961d557"
  OneDayPostDueDateNudgeId: "330eb366-f95d-47e2-8bc0-6afbb4509d9d"

InHouseBreConfig:
  EnableInHouseBre: false

CreditCardCacheConfig:
  CreditCardOfferCacheConfig:
    Prefix: "creditCardOfferId:"
    IsCachingEnabled: true
    CacheTTl: "168h"
  CardRequestCacheConfig:
    Prefix: "creditCardRequestId:"
    IsCachingEnabled: true
    CacheTTl: "30m"

ProcrastinatorWorkflowPublisher:
  QueueName: "prod-celestial-initiate-procrastinator-workflow-queue"

UnsecuredCCRenewalFeeWaiverConfig:
  IneligibleUsersSegmentId: "65ea3cc4-87b1-4c45-9d7e-0901c5d4c530"
  IsRenewalFeeVouchersEnabled: true
  IsRenewalFeeReversalEnabled: false
