Application:
  Environment: "test"
  Namespace: "firefly"
  TaskQueue: "firefly-task-queue"
  RedisConfig:
    Options:
      Addr: "localhost:6379"
      Password: ""
      DB: 0
    IsSecureRedis: false

DbConfigMap:
  EPIFI_TECH:
    DbType: "CRDB"
    Host: "localhost"
    Port: 26257
    Username: "root"
    Password: ""
    Name: "epifi_test"
    EnableDebug: false
    SSLMode: "disable"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

UsecaseDbConfigMap:
  EPIFI_TECH:
    DbType: "CRDB"
    Host: "localhost"
    Port: 26257
    Username: "root"
    Password: ""
    Name: "epifi_test"
    EnableDebug: false
    SSLMode: "disable"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

EnableActivityConfig:
  EnablePollCreditReportFeature: true
  EnablePanAadhaarValidation: true

CreditCardPgDb:
  Host: "localhost"
  Port: 5432
  Name: "credit_card_pgdb_test"
  DbType: "PGDB"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  AppName: "firefly"
  SecretName: "{\"username\": \"root\", \"password\": \"\"}"
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

WorkflowUpdatePublisher:
  TopicName: "celestial-workflow-update-topic"

CreditCardDb:
  Host: "localhost"
  Port: 26257
  Username: "root"
  DbType: "CRDB"
  Password: ""
  Name: "credit_card_test"
  EnableDebug: true
  SSLMode: "disable"
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true
    EnableMultiDBSupport: false
    DBResolverList:
      - TableName: [ ]
        Alias: "credit_card_pgdb_conn"
        DbDsn:
          DbType: "PGDB"
          AppName: "firefly"
          Host: "localhost"
          Port: 5432
          StatementTimeout: 1m
          Name: "credit_card_pgdb"
          EnableDebug: true
          SSLMode: "disable"
          SecretName: "{\"username\": \"root\", \"password\": \"\"}"

Server:
  HttpPort: 9551
  GrpcPort: 9003

FundTransferPoolAccountActorId: "actor-creditcard-federal-pool-account"

CCTransactionEventPublisher:
  TopicName: "cc-transaction-event-topic"

CCBillGenerationEventPublisher:
  TopicName: "cc-bill-generation-event-topic"

CCStageUpdateEventPublisher:
  TopicName: "cc-stage-update-event-topic"

CCTransactionNotificationPublisher:
  QueueName: "cc-txn-notification-queue"

StatementPdfConfig:
  StatementDocumentsBucketName: "epifi-credit-card-docs"
  EnableRewardsInStatements: false
  # pausing the statement generation for 3 minutes, to generate actual rewards for statement
  StatementRewardsGenerationPauseDuration: 3m
  StatementRewardsGenerationPauseDurationDuringFcFpMigration: 3m

NotificationSchedule:
  # We will be sending notifications at 10:00 AM
  PaymentDeadlineExceededReminderAfterHours:
    email: [10]
    notification: []
    sms: [10]
  PaymentDeadlineExceededWithInterestRateReminderAfterHours:
    email: [34]
    notification: []
    sms: [34]
  BeforeDueDateReminderHours:
    email: [182,134,86,62]
    whatsapp: [14]
    notification: [182,134,86,62]
    sms: [182,134,86,62]
  OnDueDateReminderHours:
    email: [14]
    whatsapp: [14]
    notification: [14]
    sms: [14]
  AfterDueDateReminderHours:
    email: [10,34,106,178,226,274]
    whatsapp: [10,34,106,154,226,346,466]
    notification: [10,34,106,178,226,274]
    sms: [10,34,106,178,226,274]

Flags:
  EnableNewFdScreen:
    MinAndroidVersion: 2000
    MinIOSVersion: 2000
    DisableFeature: true
    FallbackToEnableFeature: false
  EnableRevampedNewFdScreen:
    MinAndroidVersion: 2000
    MinIOSVersion: 2000
    DisableFeature: true
    FallbackToEnableFeature: false

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Secrets:
  Ids:
    RudderWriteKey: "1eGkhVch5jk2oJBF9lrTEN4I3zB"
    TemporalCodecAesKey: "853bbce933313713af7f43bb8fcc1d84"

FeatureReleaseConfig:
  FeatureConstraints:
    CC_REGISTER_CUSTOMER_V2:
      AppVersionConstraintConfig:
        MinAndroidVersion: 263
        MinIOSVersion: 361
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 17 # CREDIT_CARD_INTERNAL
    FEATURE_CC_REGISTER_CUSTOMER_V3:
      AppVersionConstraintConfig:
        MinAndroidVersion: 10000
        MinIOSVersion: 10000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_FILITE_CIBIL_INTEGRATION:
      AppVersionConstraintConfig:
        MinAndroidVersion: 100
        MinIOSVersion: 100
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_BE_DRIVEN_LIVENESS_SCREEN:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
    FEATURE_CC_SECURED_CARDS:
      AppVersionConstraintConfig:
        MinAndroidVersion: 2000
        MinIOSVersion: 2000
      StickyPercentageConstraintConfig:
        RolloutPercentageByPlatform:
          RolloutPercentageIOS: 0
          RolloutPercentageAndroid: 0
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_USER_INELIGIBLE_FEEDBACK:
      AppVersionConstraintConfig:
        MinAndroidVersion: 290
        MinIOSVersion: 1874
      StickyPercentageConstraintConfig:
        RolloutPercentageByPlatform:
          RolloutPercentageIOS: 100
          RolloutPercentageAndroid: 100
        RolloutPercentage: 100
    FEATURE_CC_INHOUSE_BRE:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentageByPlatform:
          RolloutPercentageIOS: 0
          RolloutPercentageAndroid: 0
        RolloutPercentage: 100
    FEATURE_CC_AUTOMATIC_FEE_WAIVER:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL

RepaymentNudges:
  SevenDaysPriorToDueDateNudgeId: ""
  OneDayPriorToDueDateNudgeId: ""
  DueDateNudgeId: ""
  OneDayPostDueDateNudgeId: ""

FireflyRedisStore:
  IsSecureRedis: false
  Options:
    Addr: "localhost:6379"
    Password: ""
    DB: 0
  ClientName: firefly
  HystrixCommand:
    CommandName: "firefly_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 200
      ExecutionTimeout: 100ms
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

ProcrastinatorWorkflowPublisher:
  QueueName: "celestial-initiate-procrastinator-workflow-queue"

QuestSdk:
  Disable: true

WorkflowParamsList:
 - WorkflowName: "PerformAuthTest"
   ActivityParamsList:
     - ActivityName: "PerformCardOnboardingAuth"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "20s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
 - WorkflowName: "PerformPreprocessCommonTest"
   ActivityParamsList:
     - ActivityName: "InitiateWorkflowStageV2"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "20s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "CreateCardRequestStage"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "20s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "UpdateCardRequest"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "20s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
 - WorkflowName: "RealtimeCardEligibilityCheck"
   ActivityParamsList:
     - ActivityName: "CheckForRisk"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 20
     - ActivityName: "FetchCreditReport"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 30
     - ActivityName: "InhouseBRECheck"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 1 hours with max cap between retries at 30min
         # Retry interval - 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s
         ExponentialBackOff:
           BaseInterval: "2s"
           MaxInterval: "30m"
           BackoffCoefficient: 2.0
           MaxAttempts: 11
     - ActivityName: "ValidateCreditReport"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 20
     - ActivityName: "RealtimeVendorOfferCheck"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 30
     - ActivityName: "CheckCardEligibility"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 30
     - ActivityName: "AddCardEligibility"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 30
 - WorkflowName: "PerformUserCommunication"
 - WorkflowName: "PerformBiometricRevalidation"
 - WorkflowName: "ResetCardPin"
 - WorkflowName: "FreezeUnfreezeCard"
   ActivityParamsList:
     - ActivityName: "ProcessFreezeUnfreezeCard"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "20s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
 - WorkflowName: "SetCardUsage"
   ActivityParamsList:
     - ActivityName: "PerformSetCardUsage"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "20s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "GetCardSettingsCommsActionData"
       ScheduleToCloseTimeout: "3h"
       StartToCloseTimeout: "10s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 30
 - WorkflowName: "ProcessWelcomeOffer"
   ActivityParamsList:
     - ActivityName: "ClaimWelcomeOffer"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "20s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
 - WorkflowName: "PerformCardOnboarding"
   ActivityParamsList:
     - ActivityName: "PerformCardOnboardingAuth"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         Hybrid:
           RetryStrategy1:
             RegularInterval:
               Interval: "2s"
               MaxAttempts: 150
           RetryStrategy2:
             ExponentialBackOff:
               BaseInterval: "4s"
               MaxInterval: "30m"
               BackoffCoefficient: 2.0
               MaxAttempts: 24
           MaxAttempts: 200
           CutOff: 150
     - ActivityName: "CreateCard"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 1 hours with max cap between retries at 30min
         # Retry interval - 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s
         ExponentialBackOff:
           BaseInterval: "2s"
           MaxInterval: "30m"
           BackoffCoefficient: 2.0
           MaxAttempts: 11
     - ActivityName: "CaptureBillGenDate"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "SetBillingDays"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "AutoSelectWelcomeOffer"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "ProcessKycCheck"
       ScheduleToCloseTimeout: "75h"
       StartToCloseTimeout: "60s"
       RetryParams:
         ExponentialBackOff:
           BaseInterval: "1m"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 50
     - ActivityName: "CheckProfileValidation"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       # Linear retry strategy for 15 seconds and then exponential retry strategy for next 24 hours with base
       # interval of 5 min and coefficient of 2
       # Retry interval : 1s 1s ... 1s for 15s and 5m 10m 20m till 24 hours
       RetryParams:
         Hybrid:
           RetryStrategy1:
             RegularInterval:
               Interval: "1s"
               MaxAttempts: 15
           RetryStrategy2:
             ExponentialBackOff:
               BaseInterval: "5m"
               MaxInterval: "24h"
               BackoffCoefficient: 2.0
               MaxAttempts: 30
     - ActivityName: "RequestPhysicalCard"
       ScheduleToCloseTimeout: "7h"
       StartToCloseTimeout: "60s"
       RetryParams:
         Hybrid:
           RetryStrategy1:
             RegularInterval:
               Interval: "5s"
               MaxAttempts: 15
           RetryStrategy2:
             ExponentialBackOff:
               BaseInterval: "3.5s"
               MaxInterval: "30m"
               BackoffCoefficient: 1.5
               MaxAttempts: 24
           MaxAttempts: 40
           CutOff: 15
     - ActivityName: "CheckForLimitChange"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
 - WorkflowName: "PerformCardOnboardingV1"
   ActivityParamsList:
     - ActivityName: "PerformCardOnboardingAuth"
       ScheduleToCloseTimeout: "720h"
       StartToCloseTimeout: "60s"
       RetryParams:
         Hybrid:
           RetryStrategy1:
             RegularInterval:
               Interval: "1s"
               MaxAttempts: 1500
           RetryStrategy2:
             ExponentialBackOff:
               BaseInterval: "4s"
               MaxInterval: "5m"
               BackoffCoefficient: 2.0
               MaxAttempts: 86400
           MaxAttempts: 87900
           CutOff: 1500
     - ActivityName: "RealtimeVendorOfferCheck"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 30
     - ActivityName: "CheckCardEligibility"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "CreateCard"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 1 hours with max cap between retries at 30min
         # Retry interval - 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s
         ExponentialBackOff:
           BaseInterval: "2s"
           MaxInterval: "30m"
           BackoffCoefficient: 2.0
           MaxAttempts: 11
     - ActivityName: "CaptureBillGenDate"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "SetBillingDays"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "ProcessKycCheck"
       ScheduleToCloseTimeout: "75h"
       StartToCloseTimeout: "60s"
       RetryParams:
         ExponentialBackOff:
           BaseInterval: "1m"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 50
     - ActivityName: "CheckProfileValidation"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 1 hours with max cap between retries at 30min
         # Retry interval - 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s
         ExponentialBackOff:
           BaseInterval: "2s"
           MaxInterval: "30m"
           BackoffCoefficient: 2.0
           MaxAttempts: 11
     - ActivityName: "UpdateCardReqNextActionForAddressSelection"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 1 hours with max cap between retries at 30min
         # Retry interval - 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s
         ExponentialBackOff:
           BaseInterval: "2s"
           MaxInterval: "30m"
           BackoffCoefficient: 2.0
           MaxAttempts: 11
     - ActivityName: "RequestPhysicalCard"
       ScheduleToCloseTimeout: "7h"
       StartToCloseTimeout: "60s"
       RetryParams:
         Hybrid:
           RetryStrategy1:
             RegularInterval:
               Interval: "5s"
               MaxAttempts: 15
           RetryStrategy2:
             ExponentialBackOff:
               BaseInterval: "4s"
               MaxInterval: "30m"
               BackoffCoefficient: 2.0
               MaxAttempts: 24
           MaxAttempts: 40
           CutOff: 15
     - ActivityName: "CheckForLimitChange"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "TriggerRewardGeneration"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "VerifyPanAadhaarLink"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "20s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "ValidatePanAadhaar"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "20s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "AutoSelectWelcomeOffer"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "10s"
           MaxAttempts: 150
 - WorkflowName: "PerformCardOnboardingV2"
   ActivityParamsList:
     - ActivityName: "CheckCardEligibility"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "InitiateFdCreation"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "10s"
           MaxAttempts: 15
     - ActivityName: "GetCardRequestStageNextAction"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "10s"
           MaxAttempts: 15
     - ActivityName: "GetIntentSelectionDl"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "CheckActiveProducts"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "PerformOnboardingStage"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "PerformOnboardingStageAsync"
       ScheduleToCloseTimeout: "48h"
       StartToCloseTimeout: "60s"
       RetryParams:
         ExponentialBackOff:
           # Retrying for 40 hours in an exponential manner
           BaseInterval: "2s"
           MaxInterval: "60m"
           BackoffCoefficient: 2.0
           MaxAttempts: 50
     - ActivityName: "PollParentsName"
       ScheduleToCloseTimeout: "720h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "10s"
           MaxAttempts: 8640
     - ActivityName: "PollEmploymentDetailsCollection"
       ScheduleToCloseTimeout: "720h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "10s"
           MaxAttempts: 8640
     - ActivityName: "CollectPanDob"
       ScheduleToCloseTimeout: "720h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "10s"
           MaxAttempts: 8640
     - ActivityName: "PollFdStatus"
       ScheduleToCloseTimeout: "240h"
       StartToCloseTimeout: "60s"
       RetryParams:
         Hybrid:
           RetryStrategy1:
             RegularInterval:
               Interval: "10s"
               MaxAttempts: 50
           RetryStrategy2:
             ExponentialBackOff:
               BaseInterval: "4s"
               MaxInterval: "30m"
               BackoffCoefficient: 2.0
               MaxAttempts: 50
           MaxAttempts: 100
           CutOff: 50
     - ActivityName: "PerformCardOnboardingAuth"
       ScheduleToCloseTimeout: "120h"
       StartToCloseTimeout: "60s"
       RetryParams:
         Hybrid:
           RetryStrategy1:
             RegularInterval:
               Interval: "1s"
               MaxAttempts: 1500
           RetryStrategy2:
             ExponentialBackOff:
               BaseInterval: "4s"
               MaxInterval: "30m"
               BackoffCoefficient: 2.0
               MaxAttempts: 240
           MaxAttempts: 1740
           CutOff: 1500
     - ActivityName: "PerformRiskCheck"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "CheckDeviceRegistration"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "RealtimeVendorOfferCheck"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 30
     - ActivityName: "RegisterCustomerToVendor"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 1 hours with max cap between retries at 30min
         # Retry interval - 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s
         ExponentialBackOff:
           BaseInterval: "2s"
           MaxInterval: "30m"
           BackoffCoefficient: 2.0
           MaxAttempts: 50
     - ActivityName: "CaptureBillGenDate"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "SetBillingDays"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "ProcessKycCheckV2"
       ScheduleToCloseTimeout: "75h"
       StartToCloseTimeout: "60s"
       RetryParams:
         ExponentialBackOff:
           BaseInterval: "1m"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 50
     - ActivityName: "CheckProfileValidation"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 1 hours with max cap between retries at 30min
         # Retry interval - 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s
         ExponentialBackOff:
           BaseInterval: "2s"
           MaxInterval: "30m"
           BackoffCoefficient: 2.0
           MaxAttempts: 11
     - ActivityName: "RequestPhysicalCard"
       ScheduleToCloseTimeout: "75h"
       StartToCloseTimeout: "60s"
       RetryParams:
         Hybrid:
           RetryStrategy1:
             RegularInterval:
               Interval: "5s"
               MaxAttempts: 150
           RetryStrategy2:
             ExponentialBackOff:
               BaseInterval: "4s"
               MaxInterval: "30m"
               BackoffCoefficient: 2.0
               MaxAttempts: 240
           MaxAttempts: 390
           CutOff: 150
     - ActivityName: "CreateCardV2"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "CreateCard"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "RunDedupeCheck"
       ScheduleToCloseTimeout: "2h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "10s"
           MaxAttempts: 50
     - ActivityName: "InhouseBRECheck"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 1 hours with max cap between retries at 30min
         # Retry interval - 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s
         ExponentialBackOff:
           BaseInterval: "2s"
           MaxInterval: "30m"
           BackoffCoefficient: 2.0
           MaxAttempts: 11
     - ActivityName: "FdStatusValidation"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "20s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "VerifyPanAadhaarLink"
       ScheduleToCloseTimeout: "75h"
       StartToCloseTimeout: "60s"
       RetryParams:
         ExponentialBackOff:
           BaseInterval: "1m"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 50
     - ActivityName: "RealtimeVendorOfferCheck"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 30
     - ActivityName: "AddCardEligibility"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "StartVkycStage"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "10s"
           MaxAttempts: 50
     - ActivityName: "TriggerRewardGeneration"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "20s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "ValidatePanAadhaar"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "20s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
 - WorkflowName: "ProcessReissueCard"
   ActivityParamsList:
     - ActivityName: "UpdateCardDeliveryAddress"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         ExponentialBackOff:
           BaseInterval: "4s"
           MaxInterval: "5h"
           BackoffCoefficient: 2.0
           MaxAttempts: 18
     - ActivityName: "BlockCard"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "ReissueCard"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "RequestPhysicalCard"
       ScheduleToCloseTimeout: "75h"
       StartToCloseTimeout: "60s"
       RetryParams:
         Hybrid:
           RetryStrategy1:
             RegularInterval:
               Interval: "5s"
               MaxAttempts: 150
           RetryStrategy2:
             ExponentialBackOff:
               BaseInterval: "4s"
               MaxInterval: "30m"
               BackoffCoefficient: 2.0
               MaxAttempts: 24
           MaxAttempts: 390
           CutOff: 150
 - WorkflowName: "SetCardLimits"
   ActivityParamsList:
     - ActivityName: "PerformSetCardLimits"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
 - WorkflowName: "ProcessViewCardDetails"
   ActivityParamsList:
     - ActivityName: "ViewCardDetails"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
 - WorkflowName: "IssuePhysicalCreditCard"
   ActivityParamsList:
     - ActivityName: "TrackCardShipment"
       ScheduleToCloseTimeout: "720h"
       StartToCloseTimeout: "60s"
       RetryParams:
         ExponentialBackOff:
           BaseInterval: "5m"
           BackoffCoefficient: 1.3
           MaxAttempts: 31
 - WorkflowName: "ActivateCreditCard"
   ActivityParamsList:
     - ActivityName: "UpdateCardRequest"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "PerformSetCardUsage"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
 - WorkflowName: "ProcessDispute"
   ActivityParamsList:
     - ActivityName: "MarkDispute"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "CreateDisputedTransaction"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "PollDispute"
       ScheduleToCloseTimeout: "1440h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
         # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
         ExponentialBackOff:
           BaseInterval: "4s"
           MaxInterval: "30m"
           BackoffCoefficient: 2.0
           MaxAttempts: 2889
 - WorkflowName: "PerformBillGeneration"
   ActivityParamsList:
     - ActivityName: "FetchAndStoreBill"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 50
     - ActivityName: "PublishBillGenerationEvent"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 50
     - ActivityName: "FetchAndCreateStatement"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 50
       RateLimit:
         Rate: 100
         Period: 1s
     - ActivityName: "GenerateStatementPdf"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 50
       RateLimit:
         Rate: 100
         Period: 1s
     - ActivityName: "ProcessPaymentReminderDatesGeneration"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 100
     - ActivityName: "ProcessPaymentReminderNotification"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "EmailStatement"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "TriggerRepaymentNudge"
       ScheduleToCloseTimeout: "1000h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "24h"
           MaxAttempts: 30
 - WorkflowName: "PerformCreditCardPayment"
   ActivityParamsList:
     - ActivityName: "InitiatePayment"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "1s"
           MaxAttempts: 30
     - ActivityName: "GetPaymentStatus"
       ScheduleToCloseTimeout: "240h"
       StartToCloseTimeout: "60s"
       RetryParams:
         Hybrid:
           RetryStrategy1:
             RegularInterval:
               Interval: "10s"
               MaxAttempts: 50
           RetryStrategy2:
             ExponentialBackOff:
               BaseInterval: "4s"
               MaxInterval: "30m"
               BackoffCoefficient: 2.0
               MaxAttempts: 150
           MaxAttempts: 200
           CutOff: 50
     - ActivityName: "UpdateVendorRepayment"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "UpsertPaymentInfo"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "InitiateAutoRepaymentWorkflow"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24

 - WorkflowName: "ProcessCardTransaction"
   ActivityParamsList:
     - ActivityName: "CreateTransaction"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 30
     - ActivityName: "ResolveMerchantName"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 30
     - ActivityName: "CreateTransactionAdditionalInfo"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 30
     - ActivityName: "UpdateAccountLimitFromVendor"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 30
     - ActivityName: "PopulateParentAndChildTxnId"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 30
     - ActivityName: "TransactionEventPublish"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 30
     - ActivityName: "ProcessRepaymentPaymentInfo"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 30
     - ActivityName: "GetTxnCommsActionData"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 30
     - ActivityName: "CheckWelcomeOfferEligibilityAndClaim"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
 - WorkflowName: "ReconcileCreditAccountAndTxn"
   ActivityParamsList:
     - ActivityName: "FetchTxnFromVendor"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "30s"
           MaxAttempts: 10
     - ActivityName: "ReconcileAndUpdateTxnStatus"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "1m"
           MaxAttempts: 30
     - ActivityName: "FetchAndUpdateDues"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "30s"
           MaxAttempts: 10
     - ActivityName: "FetchAndUpdateCardStatus"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "30s"
           MaxAttempts: 10
 - WorkflowName: "PerformBillGenerationV1"
   ActivityParamsList:
     - ActivityName: "FetchAndStoreBill"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 30
     - ActivityName: "PublishBillGenerationEvent"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 30
     - ActivityName: "FetchAndCreateStatement"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 30
     - ActivityName: "GenerateStatementPdf"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 30
     - ActivityName: "ProcessPaymentReminderDatesGeneration"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 30
     - ActivityName: "GetPaymentReminderNotification"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 30
     - ActivityName: "UploadStatementToS3"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "UpdateCreditCardBill"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "GetEmailStatementNotification"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "UpdateStatementTxnMapping"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "120s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 30
     - ActivityName: "CheckWelcomeOfferEligibilityAndClaim"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "TriggerRepaymentNudge"
       ScheduleToCloseTimeout: "1000h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "24h"
           MaxAttempts: 30
 - WorkflowName: "ExportCreditCardStatement"
   ActivityParamsList:
     - ActivityName: "GetEmailStatementNotification"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 30
     - ActivityName: "GetUpdatedNotificationStageExecutionDetails"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 30
 - WorkflowName: "TriggerReconcileWorkflows"
   ChildWorkflowParamsList:
     - WorkflowName: "ReconcileCreditAccountAndTxn"
       WorkflowExecutionTimeout: "6h"
       WorkflowRunTimeout: "1h"
       ParentClosePolicy: 2
       RetryParams:
         # Exponential retry strategy that runs for ~20min with max cap between retries at 10min
         # Retry interval - 10s 20s 40s 1min20s 2min40s 5min20s 10min40s
         ExponentialBackOff:
           BaseInterval: "10s"
           MaxInterval: "3h"
           BackoffCoefficient: 2.0
           MaxAttempts: 7
   ActivityParamsList:
     - ActivityName: "FetchReconcileAccounts"
       ScheduleToCloseTimeout: "3h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5m"
           MaxAttempts: 5
 - WorkflowName: "RealtimeCardEligibilityCheck"
   ActivityParamsList:
     - ActivityName: "CheckForRisk"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 20
     - ActivityName: "FetchCreditReport"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 30
     - ActivityName: "InhouseBRECheck"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 1 hours with max cap between retries at 30min
         # Retry interval - 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s
         ExponentialBackOff:
           BaseInterval: "2s"
           MaxInterval: "30m"
           BackoffCoefficient: 2.0
           MaxAttempts: 11
     - ActivityName: "ValidateCreditReport"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 20
     - ActivityName: "RealtimeVendorOfferCheck"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 30
     - ActivityName: "CheckCardEligibility"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 30
     - ActivityName: "AddCardEligibility"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 30
 - WorkflowName: "RealTimeProfileValidationCheck"
   ActivityParamsList:
     - ActivityName: "GetCreditReport"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 30
     - ActivityName: "UserProfileValidation"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 11
 - WorkflowName: "ProcessAutoRepayment"
   ActivityParamsList:
     - ActivityName: "PerformAutomatedFundTransfer"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 30
     - ActivityName: "UpsertPaymentInfo"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 30
     - ActivityName: "UpdateVendorRepayment"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 30
 - WorkflowName: "RealtimeCardEligibilityCheckV2"
   ActivityParamsList:
     - ActivityName: "GetIntentSelectionDl"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "FetchCreditReport"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 30
     - ActivityName: "PullCreditReport"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         Hybrid:
           RetryStrategy1:
             RegularInterval:
               Interval: "1s"
               MaxAttempts: 10
           RetryStrategy2:
             # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
             # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
             ExponentialBackOff:
               BaseInterval: "3.5s"
               MaxInterval: "30m"
               BackoffCoefficient: 1.5
               MaxAttempts: 24
           MaxAttempts: 35
           CutOff: 10
     - ActivityName: "PerformOnboardingStageAsync"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Fast polling with a 2s interval for 20 minutes. Then slow polling for 7 days with an interval of 10m
         Hybrid:
           RetryStrategy1:
             RegularInterval:
               Interval: "2s"
               MaxAttempts: 600
           RetryStrategy2:
             RegularInterval:
               Interval: "10m"
               MaxAttempts: 1008
           MaxAttempts: 1608
           CutOff: 600
     - ActivityName: "GetCardRequestStageNextAction"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "10s"
           MaxAttempts: 50
     - ActivityName: "PerformOnboardingStage"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "1s"
           MaxAttempts: 50
     - ActivityName: "RealtimeVendorOfferCheck"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 30
     - ActivityName: "StartCardOnboarding"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "1s"
           MaxAttempts: 50
     - ActivityName: "AddCardEligibility"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "InhouseBRECheck"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 1 hours with max cap between retries at 30min
         # Retry interval - 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s
         ExponentialBackOff:
           BaseInterval: "2s"
           MaxInterval: "30m"
           BackoffCoefficient: 2.0
           MaxAttempts: 11
     - ActivityName: "PollCreditReportFeature"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 1 hours with max cap between retries at 30min
         # Retry interval - 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 34m8s 1hr 2hr
         ExponentialBackOff:
           BaseInterval: "2s"
           MaxInterval: "120m"
           BackoffCoefficient: 2.0
           MaxAttempts: 13
     - ActivityName: "LogCreditReportFeature"
       ScheduleToCloseTimeout: "1h"
       StartToCloseTimeout: "60s"
       RetryParams:
         ExponentialBackOff:
           BaseInterval: "2s"
           MaxInterval: "5h"
           BackoffCoefficient: 2.0
           MaxAttempts: 40
     - ActivityName: "CheckActiveProducts"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
         # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
         ExponentialBackOff:
           BaseInterval: "3.5s"
           MaxInterval: "30m"
           BackoffCoefficient: 1.5
           MaxAttempts: 24
     - ActivityName: "FetchOnboardingCreditReport"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "5s"
           MaxAttempts: 120
     - ActivityName: "CreditReportAddressSelection"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "1s"
           MaxAttempts: 100
 - WorkflowName: "PerformCreditCardPaymentFromTpap"
   ActivityParamsList:
     - ActivityName: "InitiateTpapFundTransfer"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "1s"
           MaxAttempts: 50
     - ActivityName: "UpdateVendorRepayment"
       ScheduleToCloseTimeout: "5h"
       StartToCloseTimeout: "60s"
       RetryParams:
         RegularInterval:
           Interval: "1s"
           MaxAttempts: 50

# CardProgramNudgeConfigurations is a map that defines Nudge configurations
# for different card program types and their respective request stages.
#
# It is structured as a map with the following hierarchy:
#   - Card Program Type (e.g., CARD_PROGRAM_TYPE_UNSECURED)
#     - Card Request Stage Name (e.g., CARD_REQUEST_STAGE_NAME_AUTH)
#       - Nudge Configuration (e.g., NudgeId)
CardProgramNudgeConfigurations:
  CARD_PROGRAM_TYPE_MASS_UNSECURED:
    CARD_REQUEST_STAGE_NAME_ADDRESS_CAPTURE:
      NudgeId: "2285fd14-c3a6-43df-bf25-3e44f64cd570"
    CARD_REQUEST_STAGE_NAME_AUTH:
      NudgeId: "39e2a4ce-7f32-4775-9956-2d6fa66bd9fb"
    CARD_REQUEST_STAGE_NAME_CARD_CREATION:
      NudgeId: "e713e7d2-56ab-416d-9f8d-afc6f2ed63f7"
