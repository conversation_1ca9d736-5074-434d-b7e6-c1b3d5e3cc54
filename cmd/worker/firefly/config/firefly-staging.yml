Application:
  Environment: "staging"
  Namespace: "staging-firefly"
  TaskQueue: "staging-firefly-task-queue"
  RedisConfig:
    Options:
      Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
      Password: ""
      DB: 0
    IsSecureRedis: true

DbConfigMap:
  EPIFI_TECH:
    DbType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "staging/cockroach/ca.crt"
    SSLClientCert: "staging/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "staging/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "firefly-worker"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

UsecaseDbConfigMap:
  EPIFI_TECH:
    DbType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "staging/cockroach/ca.crt"
    SSLClientCert: "staging/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "staging/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "firefly-worker"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

WorkflowUpdatePublisher:
  TopicName: "staging-celestial-workflow-update-topic"

CreditCardPgDb:
  Username: "credit_card_pgdb_dev_user"
  Password: ""
  Name: "credit_card_pgdb"
  DbType: "PGDB"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  AppName: "firefly"
  SecretName: "staging/rds/epifimetis/credit_card_pgdb_dev_user"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

CreditCardDb:
  Username: "credit_card_dev_user"
  Password: ""
  Name: "credit_card"
  DbType: "CRDB"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "staging/cockroach/ca.crt"
  SSLClientCert: "staging/cockroach/client.credit_card_dev_user.crt"
  SSLClientKey: "staging/cockroach/client.credit_card_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  AppName: "firefly-worker"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true
    EnableMultiDBSupport: false
    DBResolverList:
      - TableName: [ ]
        Alias: "credit_card_pgdb_conn"
        DbDsn:
          DbType: "PGDB"
          AppName: "firefly-worker"
          StatementTimeout: 1m
          Name: "credit_card_pgdb"
          EnableDebug: true
          SSLMode: "disable"
          SecretName: "staging/rds/epifimetis/credit_card_pgdb_dev_user"

FundTransferPoolAccountActorId: "actor-creditcard-federal-pool-account"

CCTransactionEventPublisher:
  TopicName: "staging-cc-transaction-event-topic"

CCBillGenerationEventPublisher:
  TopicName: "staging-cc-bill-generation-event-topic"

CCStageUpdateEventPublisher:
  TopicName: "staging-cc-stage-update-event-topic"

CCTransactionNotificationPublisher:
  QueueName: "staging-cc-txn-notification-queue"

StatementPdfConfig:
  StatementDocumentsBucketName: "epifi-staging-credit-card-docs"
  EnableRewardsInStatements: false
  # pausing the statement generation for 3 minutes, to generate actual rewards for statement
  StatementRewardsGenerationPauseDuration: 3m
  StatementRewardsGenerationPauseDurationDuringFcFpMigration: 3m

NotificationSchedule:
  # We will be sending notifications at 10:00 AM
  PaymentDeadlineExceededReminderAfterHours:
    email: []
    notification: []
    sms: [10]
  PaymentDeadlineExceededWithInterestRateReminderAfterHours:
    email: []
    notification: []
    sms: [34]
  BeforeDueDateReminderHours:
    email: [182,134,86,62]
    whatsapp: [14]
    notification: [182,134,86,62]
    sms: [182,134,86,62]
  OnDueDateReminderHours:
    email: [14]
    whatsapp: [14]
    notification: [14]
    sms: [14]
  AfterDueDateReminderHours:
    email: [10,34,106,178,226,274]
    whatsapp: [10,34,106,154,226,346,466]
    notification: [10,34,106,178,226,274]
    sms: [10,34,106,178,226,274]

Flags:
  EnableAddressV2Screen:
    MinAndroidVersion: 0
    MinIOSVersion: 0
    DisableFeature: false
    FallbackToEnableFeature: false
  EnableNewFdScreen:
    MinAndroidVersion: 2000
    MinIOSVersion: 2000
    DisableFeature: true
    FallbackToEnableFeature: false
  EnableRevampedNewFdScreen:
    MinAndroidVersion: 2000
    MinIOSVersion: 2000
    DisableFeature: true
    FallbackToEnableFeature: false

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Secrets:
  Ids:
    RudderWriteKey: "staging/rudder/internal-writekey"
    TemporalCodecAesKey: "staging/temporal/codec-encryption-key"

FeatureReleaseConfig:
  FeatureConstraints:
    CC_REGISTER_CUSTOMER_V2:
      AppVersionConstraintConfig:
        MinAndroidVersion: 241
        MinIOSVersion: 100
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 17 # CREDIT_CARD_INTERNAL
    FEATURE_CC_REGISTER_CUSTOMER_V3:
      AppVersionConstraintConfig:
        MinAndroidVersion: 10000
        MinIOSVersion: 10000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_FILITE_CIBIL_INTEGRATION:
      AppVersionConstraintConfig:
        MinAndroidVersion: 100
        MinIOSVersion: 100
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_INHOUSE_BRE:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentageByPlatform:
          RolloutPercentageIOS: 100
          RolloutPercentageAndroid: 100
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_UPDATE_CARD_DETAILS_AT_BANK:
      AppVersionConstraintConfig:
        MinAndroidVersion: 266
        MinIOSVersion: 369
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_BE_DRIVEN_LIVENESS_SCREEN:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 2000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
    FEATURE_CC_SECURED_CARDS:
      AppVersionConstraintConfig:
        MinAndroidVersion: 2000
        MinIOSVersion: 2000
      StickyPercentageConstraintConfig:
        RolloutPercentageByPlatform:
          RolloutPercentageIOS: 0
          RolloutPercentageAndroid: 0
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_USER_INELIGIBLE_FEEDBACK:
      AppVersionConstraintConfig:
        MinAndroidVersion: 290
        MinIOSVersion: 1874
      StickyPercentageConstraintConfig:
        RolloutPercentageByPlatform:
          RolloutPercentageIOS: 100
          RolloutPercentageAndroid: 100
        RolloutPercentage: 100
    MASS_UNSECURED_CARD:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
    CREDIT_REPORT_ADDRESS_SELECTION:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
    FEATURE_CC_AUTOMATIC_FEE_WAIVER:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL

CardRequestStageNameToNudgeConfigMap:
  CARD_REQUEST_STAGE_NAME_ADDRESS_CAPTURE:
    NudgeId: "e1634fbd-c5c8-4cca-b8e5-967b87a6e1a3"
  CARD_REQUEST_STAGE_NAME_BILLING_DAYS_CAPTURE:
    NudgeId: "e1634fbd-c5c8-4cca-b8e5-967b87a6e1b3"
  CARD_REQUEST_STAGE_NAME_AUTH:
    NudgeId: "e1634fbd-c5c8-4cca-b8e5-967b87a6e1c3"
  CARD_REQUEST_STAGE_NAME_CARD_CREATION:
    NudgeId: "e1634fbd-c5c8-4cca-b8e5-967b87a6e1d3"

QuestRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 10
  ClientName: ff-worker-quest-sdk
  HystrixCommand:
    CommandName: "firefly_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 500
      ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

FireflyRedisStore:
  IsSecureRedis: true
  Options:
    Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
    Password: ""
    DB: 12
    PoolSize: 20 # this is set to twice the size of default connection pool size
    MinIdleConns: 10 # this allows min. number of conn to be readily available
  ClientName: firefly
  HystrixCommand:
    CommandName: "firefly_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 200
      ExecutionTimeout: 500ms
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

RepaymentNudges:
  SevenDaysPriorToDueDateNudgeId: ""
  OneDayPriorToDueDateNudgeId: ""
  DueDateNudgeId: ""
  OneDayPostDueDateNudgeId: ""

ProcrastinatorWorkflowPublisher:
  QueueName: "staging-celestial-initiate-procrastinator-workflow-queue"
