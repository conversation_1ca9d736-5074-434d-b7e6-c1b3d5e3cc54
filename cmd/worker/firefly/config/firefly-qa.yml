Application:
  Environment: "qa"
  Namespace: "qa-firefly"
  TaskQueue: "qa-firefly-task-queue"
  RedisConfig:
    IsSecureRedis: true
    Options:
      Addr: "master.qa-common-cache-redis.rxzivf.aps1.cache.amazonaws.com:6379"
      Password: "" ## empty string for no password
      DB: 12

DbConfigMap:
  EPIFI_TECH:
    DbType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "qa/cockroach/ca.crt"
    SSLClientCert: "qa/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "qa/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "firefly-worker"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

UsecaseDbConfigMap:
  EPIFI_TECH:
    DbType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "qa/cockroach/ca.crt"
    SSLClientCert: "qa/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "qa/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "firefly-worker"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

WorkflowUpdatePublisher:
  TopicName: "qa-celestial-workflow-update-topic"

# TODO: update once db is created
CreditCardDb:
  Username: "credit_card_dev_user"
  Password: ""
  DbType: "CRDB"
  Name: "credit_card"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "qa/cockroach/ca.crt"
  SSLClientCert: "qa/cockroach/client.credit_card_dev_user.crt"
  SSLClientKey: "qa/cockroach/client.credit_card_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  AppName: "firefly-worker"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true
    EnableMultiDBSupport: false
    DBResolverList:
      - TableName: [ ]
        Alias: "credit_card_pgdb_conn"
        DbDsn:
          DbType: "PGDB"
          AppName: "firefly-worker"
          StatementTimeout: 1m
          Name: "credit_card_pgdb"
          EnableDebug: true
          SSLMode: "disable"
          SecretName: "qa/rds/epifimetis/credit_card_pgdb_dev_user"

CreditCardPgDb:
  Username: "credit_card_pgdb_dev_user"
  Password: ""
  Name: "credit_card_pgdb"
  DbType: "PGDB"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  AppName: "firefly"
  SecretName: "qa/rds/epifimetis/credit_card_pgdb_dev_user"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

FundTransferPoolAccountActorId: "actor-creditcard-federal-pool-account"

CCTransactionEventPublisher:
  TopicName: "qa-cc-transaction-event-topic"

CCBillGenerationEventPublisher:
  TopicName: "qa-cc-bill-generation-event-topic"

CCStageUpdateEventPublisher:
  TopicName: "qa-cc-stage-update-event-topic"

CCTransactionNotificationPublisher:
  QueueName: "qa-cc-txn-notification-queue"

StatementPdfConfig:
  StatementDocumentsBucketName: "epifi-qa-credit-card-docs"
  EnableRewardsInStatements: true
  # pausing the statement generation for 10 minutes, to generate actual rewards for statement
  StatementRewardsGenerationPauseDuration: 5m
  StatementRewardsGenerationPauseDurationDuringFcFpMigration: 6m

NotificationSchedule:
  # We will be sending notifications at 10:00 AM
  PaymentDeadlineExceededReminderAfterHours:
    email: [10]
    notification: []
    sms: [10]
  PaymentDeadlineExceededWithInterestRateReminderAfterHours:
    email: [34]
    notification: []
    sms: [34]
  BeforeDueDateReminderHours:
    email: [182,134,86,62]
    whatsapp: [14]
    notification: [182,134,86,62]
    sms: [182,134,86,62]
  OnDueDateReminderHours:
    email: [14]
    whatsapp: [14]
    notification: [14]
    sms: [14]
  AfterDueDateReminderHours:
    email: [10,34,106,178,226,274]
    whatsapp: [10,34,106,154,226,346,466]
    notification: [10,34,106,178,226,274]
    sms: [10,34,106,178,226,274]

Flags:
  EnableAddressV2Screen:
    MinAndroidVersion: 256
    MinIOSVersion: 359
    DisableFeature: false
    FallbackToEnableFeature: false
  EnableNewFdScreen:
    MinAndroidVersion: 310
    MinIOSVersion: 1955
    DisableFeature: false
    FallbackToEnableFeature: false
  EnableRevampedNewFdScreen:
    MinAndroidVersion: 327
    MinIOSVersion: 2096
    DisableFeature: false
    FallbackToEnableFeature: false

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Secrets:
  Ids:
    RudderWriteKey: "qa/rudder/internal-writekey"
    TemporalCodecAesKey: "qa/temporal/codec-encryption-key"

FeatureReleaseConfig:
  FeatureConstraints:
    CC_REGISTER_CUSTOMER_V2:
      AppVersionConstraintConfig:
        MinAndroidVersion: 263
        MinIOSVersion: 361
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 17 # CREDIT_CARD_INTERNAL
    FEATURE_CC_REGISTER_CUSTOMER_V3:
      AppVersionConstraintConfig:
        MinAndroidVersion: 10000
        MinIOSVersion: 10000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_FILITE_CIBIL_INTEGRATION:
      AppVersionConstraintConfig:
        MinAndroidVersion: 332
        MinIOSVersion: 2150
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_UPDATE_CARD_DETAILS_AT_BANK:
      AppVersionConstraintConfig:
        MinAndroidVersion: 266
        MinIOSVersion: 369
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_BE_DRIVEN_LIVENESS_SCREEN:
      AppVersionConstraintConfig:
        MinAndroidVersion: 294
        MinIOSVersion: 1910
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_INHOUSE_BRE:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentageByPlatform:
          RolloutPercentageIOS: 100
          RolloutPercentageAndroid: 100
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_SECURED_CARDS:
      AppVersionConstraintConfig:
        MinAndroidVersion: 290
        MinIOSVersion: 1874
      StickyPercentageConstraintConfig:
        RolloutPercentageByPlatform:
          RolloutPercentageIOS: 100
          RolloutPercentageAndroid: 100
        RolloutPercentage: 100
    FEATURE_CC_USER_INELIGIBLE_FEEDBACK:
      AppVersionConstraintConfig:
        MinAndroidVersion: 290
        MinIOSVersion: 1874
      StickyPercentageConstraintConfig:
        RolloutPercentageByPlatform:
          RolloutPercentageIOS: 100
          RolloutPercentageAndroid: 100
        RolloutPercentage: 100
    MASS_UNSECURED_CARD:
      AppVersionConstraintConfig:
        MinAndroidVersion: 317
        MinIOSVersion: 452
    CREDIT_REPORT_ADDRESS_SELECTION:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
    FEATURE_CC_AUTOMATIC_FEE_WAIVER:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL

QuestRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.qa-common-cache-redis.rxzivf.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 10
  ClientName: ff-worker-quest-sdk
  HystrixCommand:
    CommandName: "quest_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 500
      ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

FireflyRedisStore:
  IsSecureRedis: true
  Options:
    Addr: "master.qa-common-cache-redis.rxzivf.aps1.cache.amazonaws.com:6379"
    Password: ""
    DB: 12
    PoolSize: 20 # this is set to twice the size of default connection pool size
    MinIdleConns: 10 # this allows min. number of conn to be readily available
  ClientName: firefly
  HystrixCommand:
    CommandName: "firefly_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 200
      ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

RepaymentNudges:
  SevenDaysPriorToDueDateNudgeId: "9009d1f6-9b58-494e-9428-c6bfd05431d2"
  OneDayPriorToDueDateNudgeId: "2f307816-ab95-4267-a4b7-d7be1d8ad279"
  DueDateNudgeId: "c0f57949-b694-410c-b2c1-5a9d6ef15a0a"
  OneDayPostDueDateNudgeId: "2f737b97-5910-4a39-8ca9-0de233b20690"

InHouseBreConfig:
  EnableInHouseBre: false

RewardCvpVersionConfigs:
  - CvpType : "REWARD_CVP_UNSECURED"
    SupportedCardProgramTypes: ["CARD_PROGRAM_TYPE_UNSECURED","CARD_PROGRAM_TYPE_UNSPECIFIED"]
    CvpVersions:
      - Name: "REWARD_CVP_UNSECURED_V1"
        StartDate:
          Year: 2023
          Month: 1
          Day: 1
        EndDate:
          Year: 2023
          Month: 12
          Day: 14
        LastStatementDate:
          Year: 2024
          Month: 1
          Day: 4
      - Name: "REWARD_CVP_UNSECURED_V2"
        StartDate:
          Year: 2023
          Month: 12
          Day: 15
  - CvpType: "REWARD_CVP_SECURED"
    SupportedCardProgramTypes: ["CARD_PROGRAM_TYPE_SECURED"]
    CvpVersions:
      - Name: "REWARD_CVP_SECURED_V1"
        StartDate:
          Year: 2023
          Month: 11
          Day: 15
  - CvpType: "REWARD_CVP_MASS_UNSECURED"
    SupportedCardProgramTypes: [ "CARD_PROGRAM_TYPE_MASS_UNSECURED" ]
    CvpVersions:
      - Name: "REWARD_CVP_MASS_UNSECURED_V1"
        StartDate:
          Year: 2023
          Month: 11
          Day: 1

# CardProgramNudgeConfigurations is a map that defines Nudge configurations
# for different card program types and their respective request stages.
#
# It is structured as a map with the following hierarchy:
#   - Card Program Type (e.g., CARD_PROGRAM_TYPE_UNSECURED)
#     - Card Request Stage Name (e.g., CARD_REQUEST_STAGE_NAME_AUTH)
#       - Nudge Configuration (e.g., NudgeId)
CardProgramNudgeConfigurations:
  CARD_PROGRAM_TYPE_MASS_UNSECURED:
    CARD_REQUEST_STAGE_NAME_ADDRESS_CAPTURE:
      NudgeId: "2285fd14-c3a6-43df-bf25-3e44f64cd570"
    CARD_REQUEST_STAGE_NAME_AUTH:
      NudgeId: "39e2a4ce-7f32-4775-9956-2d6fa66bd9fb"
    CARD_REQUEST_STAGE_NAME_CARD_CREATION:
      NudgeId: "e713e7d2-56ab-416d-9f8d-afc6f2ed63f7"

ProcrastinatorWorkflowPublisher:
  QueueName: "qa-celestial-initiate-procrastinator-workflow-queue"


CreditCardCacheConfig:
  CreditCardOfferCacheConfig:
    Prefix: "creditCardOfferId:"
    IsCachingEnabled: true
    CacheTTl: "30m"
  CardRequestCacheConfig:
    Prefix: "creditCardRequestId:"
    IsCachingEnabled: true
    CacheTTl: "30m"
