// code generated by tools/servergen
package server

import (
	context "context"
	"fmt"
	"net/http"
	strings "strings"
	time "time"

	awssdk "github.com/aws/aws-sdk-go-v2/aws"
	awssqs "github.com/aws/aws-sdk-go-v2/service/sqs"
	prometheus "github.com/prometheus/client_golang/prometheus"
	redis "github.com/redis/go-redis/v9"
	analytics "github.com/rudderlabs/analytics-go"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
	reflection "google.golang.org/grpc/reflection"
	gorm "gorm.io/gorm"

	celestial "github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/pkg/async/goroutine"
	awsconfpkg "github.com/epifi/be-common/pkg/aws/v2/config"
	s3pkg "github.com/epifi/be-common/pkg/aws/v2/s3"
	sns "github.com/epifi/be-common/pkg/aws/v2/sns"
	sqs "github.com/epifi/be-common/pkg/aws/v2/sqs"
	cache "github.com/epifi/be-common/pkg/cache"
	cfg "github.com/epifi/be-common/pkg/cfg"
	consul "github.com/epifi/be-common/pkg/cfg/consul"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	commonexplorer "github.com/epifi/be-common/pkg/cfg/explorer"
	config "github.com/epifi/be-common/pkg/cmd/config"
	genconf "github.com/epifi/be-common/pkg/cmd/config/genconf"
	types "github.com/epifi/be-common/pkg/cmd/types"
	epificontext "github.com/epifi/be-common/pkg/epificontext"
	epifigrpc "github.com/epifi/be-common/pkg/epifigrpc"
	commoninterceptors "github.com/epifi/be-common/pkg/epifigrpc/interceptors"
	servergenwire2 "github.com/epifi/be-common/pkg/epifigrpc/interceptors/servergen_wire"
	resolver "github.com/epifi/be-common/pkg/epifigrpc/resolver"
	epifiserver "github.com/epifi/be-common/pkg/epifiserver"
	temporalpkg "github.com/epifi/be-common/pkg/epifitemporal"
	errgroup "github.com/epifi/be-common/pkg/errgroup"
	events "github.com/epifi/be-common/pkg/events"
	logger "github.com/epifi/be-common/pkg/logger"
	profiling "github.com/epifi/be-common/pkg/profiling"
	queue "github.com/epifi/be-common/pkg/queue"
	ratelimiter "github.com/epifi/be-common/pkg/ratelimiter"
	store "github.com/epifi/be-common/pkg/ratelimiter/store"
	storage "github.com/epifi/be-common/pkg/storage"
	storage2 "github.com/epifi/be-common/pkg/storage/v2"
	usecase "github.com/epifi/be-common/pkg/storage/v2/usecase"
	otel "github.com/epifi/be-common/pkg/tracing/opentelemetry"
	accountsconf "github.com/epifi/gamma/accounts/config"
	genconf2 "github.com/epifi/gamma/accounts/config/genconf"
	wire "github.com/epifi/gamma/accounts/wire"
	accountstypes "github.com/epifi/gamma/accounts/wire/types"
	actorconf "github.com/epifi/gamma/actor/config"
	genconf3 "github.com/epifi/gamma/actor/config/genconf"
	wire2 "github.com/epifi/gamma/actor/wire"
	actorwiretypes "github.com/epifi/gamma/actor/wire/types"
	accountbalancepb "github.com/epifi/gamma/api/accounts/balance"
	developer2 "github.com/epifi/gamma/api/accounts/developer"
	operstatuspb "github.com/epifi/gamma/api/accounts/operstatus"
	accountstatementpb "github.com/epifi/gamma/api/accounts/statement"
	actorpb "github.com/epifi/gamma/api/actor"
	actorconsumerpb "github.com/epifi/gamma/api/actor/consumer"
	developer4 "github.com/epifi/gamma/api/actor/developer"
	authpb "github.com/epifi/gamma/api/auth"
	bcpb "github.com/epifi/gamma/api/bankcust"
	comms "github.com/epifi/gamma/api/comms"
	depositpb "github.com/epifi/gamma/api/deposit"
	docspb "github.com/epifi/gamma/api/docs"
	healthenginepb "github.com/epifi/gamma/api/health_engine"
	mpb "github.com/epifi/gamma/api/merchant"
	developer6 "github.com/epifi/gamma/api/merchant/developer"
	aaorderpb "github.com/epifi/gamma/api/order/aa"
	beneficiarymanagementpb "github.com/epifi/gamma/api/pay/beneficiarymanagement"
	pipb "github.com/epifi/gamma/api/paymentinstrument"
	accountpipb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	piconsumerpb "github.com/epifi/gamma/api/paymentinstrument/consumer"
	developer8 "github.com/epifi/gamma/api/paymentinstrument/developer"
	explorerpb "github.com/epifi/gamma/api/pkg/cfg/explorer"
	savingspb "github.com/epifi/gamma/api/savings"
	timeline "github.com/epifi/gamma/api/timeline"
	developer10 "github.com/epifi/gamma/api/timeline/developer"
	tspuserpb "github.com/epifi/gamma/api/tspuser"
	_ "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/screen_option_dependency"
	upipb "github.com/epifi/gamma/api/upi"
	userpb "github.com/epifi/gamma/api/user"
	usergrouppb "github.com/epifi/gamma/api/user/group"
	merchantresolutionpb "github.com/epifi/gamma/api/vendorgateway/merchantresolution"
	vgaccountpb "github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	vgsavingspb "github.com/epifi/gamma/api/vendorgateway/openbanking/savings"
	hook "github.com/epifi/gamma/cmd/servers/qa/actor/hook"
	docstypes "github.com/epifi/gamma/docs/wire/types"
	merchantconf "github.com/epifi/gamma/merchant/config"
	genconf4 "github.com/epifi/gamma/merchant/config/genconf"
	wire3 "github.com/epifi/gamma/merchant/wire"
	merchanttypes "github.com/epifi/gamma/merchant/wire/types"
	paymentinstrumentconf "github.com/epifi/gamma/paymentinstrument/config"
	genconf5 "github.com/epifi/gamma/paymentinstrument/config/genconf"
	wire4 "github.com/epifi/gamma/paymentinstrument/wire"
	piwiretypes "github.com/epifi/gamma/paymentinstrument/wire/types"
	explorer "github.com/epifi/gamma/pkg/cfg/explorer"
	questinterceptor "github.com/epifi/gamma/pkg/epifigrpc/interceptors/quest"
	servergenwire "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
	fepkg "github.com/epifi/gamma/pkg/frontend/msg"
	timelineconf "github.com/epifi/gamma/timeline/config"
	genconf6 "github.com/epifi/gamma/timeline/config/genconf"
	wire5 "github.com/epifi/gamma/timeline/wire"
	timelinetypes "github.com/epifi/gamma/timeline/wire/types"
)

// nolint: funlen
func Run(ctx context.Context, initNotifier chan<- cfg.ServerName) error {
	// Panic handler for logging panics in standard format
	epifiserver.HandlePanic()
	resolver.RegisterBuilder(resolver.ConsulSchemeName)
	var configNameToConfMap = make(commonexplorer.CfgExplorerMap)

	err := initLogger()
	if err != nil {
		// cannot log error since logger maynot be initialized yet.
		return err
	}
	ctx = epificontext.WithTraceId(ctx, metadata.MD{})
	// Load static configuration
	conf, err := config.Load(cfg.ACTOR_SERVER)
	if err != nil {
		logger.Error(ctx, "failed to load static conf", zap.Error(err))
		return err
	}
	_ = conf
	// Load generated dynamic configuration
	gconf, err := genconf.Load(cfg.ACTOR_SERVER)
	if err != nil {
		logger.Error(ctx, "failed to load dynamic conf", zap.Error(err))
		return err
	}
	logger.Info(ctx, "initiating server")

	if gconf.Profiling().AutomaticProfiling().EnableAutoProfiling() {
		profilingErr := profiling.AutomaticProfiling(context.Background(), cfg.ACTOR_SERVER, gconf.Profiling(), gconf.Environment(), gconf.AWS().Region)
		if profilingErr != nil {
			logger.Error(ctx, "failed to start automatic profiling", zap.Error(profilingErr))
			return profilingErr
		}
	}

	if gconf.Tracing().Enable {
		_, shutdown, tracingErr := otel.ConfigureOpenTelemetryV2(context.Background(), cfg.ACTOR_SERVER, gconf.Environment(), gconf.Profiling().PyroscopeProfiling().EnablePyroscope())
		if tracingErr != nil {
			logger.Error(ctx, "failed to configure open telemetry", zap.Error(tracingErr))
			return err
		}
		defer shutdown()
	}

	err = profiling.PyroscopeProfilingV2(gconf.Name(), gconf.Profiling(), gconf.Environment())
	if err != nil {
		logger.Error(ctx, "error starting pyroscope profiler", zap.Error(err))
	}

	if !cfg.IsLocalEnv(gconf.Environment()) && !cfg.IsRemoteDebugEnabled() && !cfg.IsTestTenantEnabled() && !cfg.IsTeamSpaceTenant() {
		initDisc, _ := consul.GetBool(consul.GetInitDiscoveryKey(gconf.Environment(), string(gconf.Name())))
		queue.SetWorkerInitialization(initDisc)
	}

	rateLimiterRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()[gconf.RedisRateLimiterName()], gconf.Tracing().Enable)
	defer func() { _ = rateLimiterRedisStore.Close() }()

	var (
		crdbResourceMap    *storage2.DBResourceProvider[*gorm.DB]
		crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor]
	)

	epifiCRDB, err := storage2.NewCRDBWithConfig(gconf.Databases()["EpifiCRDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "EpifiCRDB"))
		return err
	}
	epifiCRDBSqlDb, err := epifiCRDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "EpifiCRDB"))
		return err
	}
	defer func() { _ = epifiCRDBSqlDb.Close() }()

	payPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["PayPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "PayPGDB"))
		return err
	}
	payPGDBSqlDb, err := payPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "PayPGDB"))
		return err
	}
	defer func() { _ = payPGDBSqlDb.Close() }()
	actorPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["ActorPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "ActorPGDB"))
		return err
	}
	actorPGDBSqlDb, err := actorPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "ActorPGDB"))
		return err
	}
	defer func() { _ = actorPGDBSqlDb.Close() }()
	merchantPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["MerchantPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "MerchantPGDB"))
		return err
	}
	merchantPGDBSqlDb, err := merchantPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "MerchantPGDB"))
		return err
	}
	defer func() { _ = merchantPGDBSqlDb.Close() }()
	paymentInstrumentPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["PaymentInstrumentPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "PaymentInstrumentPGDB"))
		return err
	}
	paymentInstrumentPGDBSqlDb, err := paymentInstrumentPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "PaymentInstrumentPGDB"))
		return err
	}
	defer func() { _ = paymentInstrumentPGDBSqlDb.Close() }()
	timelinePGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["TimelinePGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "TimelinePGDB"))
		return err
	}
	timelinePGDBSqlDb, err := timelinePGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "TimelinePGDB"))
		return err
	}
	defer func() { _ = timelinePGDBSqlDb.Close() }()

	attributeRateLimiter := ratelimiter.NewAttributeRateLimiter(store.NewSlidingWindowLogWithRedis(rateLimiterRedisStore), gconf.GrpcAttributeRatelimiterParams().Namespace())
	collapserRueidisRedisStore, err := storage2.NewRueidisClientFromConfig(gconf.RueidisRedisClients()["CollapserRueidisRedisStore"])
	if err != nil {
		logger.Error(ctx, "failed to establish rueidis redis client conn for CollapserRueidisRedisStore", zap.Error(err))
		return err
	}
	defer func() { collapserRueidisRedisStore.Close() }()
	collapserRueidisCacheStorage := cache.NewRueidisCacheStorageWithHystrix(cache.NewRueidisCacheWithOptions(collapserRueidisRedisStore), gconf.RueidisRedisClients()["CollapserRueidisRedisStore"].Hystrix)
	centralgrowthConn := epifigrpc.NewServerConn(cfg.ACTOR_SERVER, cfg.CENTRAL_GROWTH_SERVER)
	defer epifigrpc.CloseConn(centralgrowthConn)
	savingsClient := savingspb.NewSavingsClient(centralgrowthConn)
	authConn := epifigrpc.NewServerConn(cfg.ACTOR_SERVER, cfg.AUTH_SERVER)
	defer epifigrpc.CloseConn(authConn)
	usersClient := userpb.NewUsersClient(authConn)
	authClient := authpb.NewAuthClient(authConn)
	wealthdmfConn := epifigrpc.NewServerConn(cfg.ACTOR_SERVER, cfg.WEALTH_DMF_SERVER)
	defer epifigrpc.CloseConn(wealthdmfConn)
	depositClient := depositpb.NewDepositClient(wealthdmfConn)
	actorConn := epifigrpc.NewServerConn(cfg.ACTOR_SERVER, cfg.ACTOR_SERVER)
	defer epifigrpc.CloseConn(actorConn)
	actorClient := actorpb.NewActorClient(actorConn)
	groupClient := usergrouppb.NewGroupClient(authConn)
	vendorgatewayConn := epifigrpc.NewServerConn(cfg.ACTOR_SERVER, cfg.VENDOR_GATEWAY_SERVER)
	defer epifigrpc.CloseConn(vendorgatewayConn)
	accountsClient := vgaccountpb.NewAccountsClient(vendorgatewayConn)
	onboardingConn := epifigrpc.NewServerConn(cfg.ACTOR_SERVER, cfg.ONBOARDING_SERVER)
	defer epifigrpc.CloseConn(onboardingConn)
	bankCustomerServiceClient := bcpb.NewBankCustomerServiceClient(onboardingConn)
	savingsClientVar4 := vgsavingspb.NewSavingsClient(vendorgatewayConn)
	payRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["PayRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = payRedisStore.Close() }()
	payCacheStorage := cache.NewRedisCacheStorageWithHystrix(cache.NewRedisCacheStorage(payRedisStore), gconf.RedisClusters()["PayRedisStore"].HystrixCommand)
	docsConn := epifigrpc.NewServerConn(cfg.ACTOR_SERVER, cfg.DOCS_SERVER)
	defer epifigrpc.CloseConn(docsConn)
	healthEngineServiceClient := healthenginepb.NewHealthEngineServiceClient(docsConn)
	growthinfraConnClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptor := servergenwire.NewActorRequestClientInterceptor()
	if unaryClientInterceptor != nil {
		growthinfraConnClientInterceptors = append(growthinfraConnClientInterceptors, unaryClientInterceptor)
	}
	growthinfraConn := epifigrpc.NewServerConn(cfg.ACTOR_SERVER, cfg.GROWTH_INFRA_SERVER, growthinfraConnClientInterceptors...)
	defer epifigrpc.CloseConn(growthinfraConn)
	commsClient := comms.NewCommsClient(growthinfraConn)
	docsConnVar2ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar2 := servergenwire.NewActorRequestClientInterceptor()
	if unaryClientInterceptorVar2 != nil {
		docsConnVar2ClientInterceptors = append(docsConnVar2ClientInterceptors, unaryClientInterceptorVar2)
	}
	docsConnVar2 := epifigrpc.NewServerConn(cfg.ACTOR_SERVER, cfg.DOCS_SERVER, docsConnVar2ClientInterceptors...)
	defer epifigrpc.CloseConn(docsConnVar2)
	docsClient := docspb.NewDocsClient(docsConnVar2)
	accountStatementClient := accountstatementpb.NewAccountStatementClient(actorConn)
	nebulaConn := epifigrpc.NewServerConn(cfg.ACTOR_SERVER, cfg.NEBULA_SERVER)
	defer epifigrpc.CloseConn(nebulaConn)
	celestialClient := celestial.NewCelestialClient(nebulaConn)
	operationalStatusServiceClient := operstatuspb.NewOperationalStatusServiceClient(actorConn)
	piClient := pipb.NewPiClient(actorConn)
	accountPIRelationClient := accountpipb.NewAccountPIRelationClient(actorConn)
	merchantServiceClient := mpb.NewMerchantServiceClient(actorConn)
	orderConn := epifigrpc.NewServerConn(cfg.ACTOR_SERVER, cfg.ORDER_SERVER)
	defer epifigrpc.CloseConn(orderConn)
	uPIClient := upipb.NewUPIClient(orderConn)
	vendormappingActorRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["VendormappingActorRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = vendormappingActorRedisStore.Close() }()
	timelineServiceClient := timeline.NewTimelineServiceClient(actorConn)
	beneficiaryManagementClient := beneficiarymanagementpb.NewBeneficiaryManagementClient(orderConn)
	useCaseDbResourceProvider, useCaseDbResourceProviderTxnExec, useCaseDbResourceProviderTeardown, err := usecase.NewDBResourceProvider(gconf.UseCaseDBConfigMap(), gconf.Tracing().Enable, gconf.PgdbConns())
	if err != nil {
		logger.Error(ctx, "failed to initialize usecase db resource provider", zap.Error(err))
		return err
	}
	defer func() {
		useCaseDbResourceProviderTeardown()
	}()
	_ = useCaseDbResourceProvider
	_ = useCaseDbResourceProviderTxnExec
	tspUserServiceClient := tspuserpb.NewTspUserServiceClient(onboardingConn)
	accountAggregatorClient := aaorderpb.NewAccountAggregatorClient(orderConn)
	payMerchantRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["PayMerchantRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = payMerchantRedisStore.Close() }()
	merchantResolutionClient := merchantresolutionpb.NewMerchantResolutionClient(vendorgatewayConn)
	vendormappingPiRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["VendormappingPiRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = vendormappingPiRedisStore.Close() }()
	userTimelineRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["UserTimelineRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = userTimelineRedisStore.Close() }()

	awsConf, awsErr := awsconfpkg.NewAWSConfig(ctx, gconf.AWS().Region, gconf.Tracing().Enable)
	if awsErr != nil {
		logger.Error(ctx, "error in loading aws v2 config", zap.Error(awsErr))
		if !cfg.IsRemoteDebugEnabled() {
			return err
		}
	}

	sqsClient := sqs.InitSQSClient(awsConf)
	_ = sqsClient

	var broker *events.RudderStackBroker

	// load rudder-stack broker for sending events
	rudderClient, err := analytics.NewWithConfig(
		gconf.Secrets().Ids[config.RudderWriteKey], "http://"+cfg.GetServerEndPoint(cfg.RUDDER_SERVER).URL(),
		analytics.Config{
			Callback:  events.NewCallback(),
			Interval:  gconf.RudderStack().IntervalInSec * time.Second, // nolint: durationcheck
			BatchSize: gconf.RudderStack().BatchSize,
			Verbose:   gconf.RudderStack().Verbose,
		})
	if err != nil {
		logger.Error(ctx, "error in connecting rudder", zap.Error(err))
		return err
	}
	broker = events.NewRudderStackBroker(rudderClient)
	defer broker.Close()

	rateLimiter := ratelimiter.NewRateLimiterV2(store.NewSlidingWindowLogWithRedis(rateLimiterRedisStore), gconf.GrpcRatelimiterParams().RateLimitConfig())
	_ = rateLimiter

	var unaryInterceptors []grpc.UnaryServerInterceptor

	unaryServerInterceptor := servergenwire.AddQuestUserContextUnaryInterceptor(gconf)
	if unaryServerInterceptor != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptor)
	}

	unaryServerInterceptorVar2 := servergenwire2.RateLimitServerInterceptorV2WithDefaultKeyGen(gconf, rateLimiter, attributeRateLimiter)
	if unaryServerInterceptorVar2 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar2)
	}

	unaryServerInterceptorVar3, err := servergenwire2.CollapserInterceptor(collapserRueidisRedisStore, collapserRueidisCacheStorage)
	if err != nil {
		logger.Error(ctx, "failed to init grpc unary interceptor", zap.Error(err))
		return err
	}
	if unaryServerInterceptorVar3 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar3)
	}

	var streamInterceptors []grpc.StreamServerInterceptor

	histogramBuckets := prometheus.DefBuckets
	if len(gconf.LatencyHistogramBuckets()) > 0 {
		histogramBuckets = epifigrpc.AddGrpcLatencyBuckets(gconf.LatencyHistogramBuckets()...)
	}

	serverOptions := []grpc.ServerOption{}
	if gconf.GrpcServerConfig().MaxRecvMsgSize() != nil && gconf.GrpcServerConfig().MaxRecvMsgSize().Size != 0 {
		serverOptions = append(serverOptions, grpc.MaxRecvMsgSize(gconf.GrpcServerConfig().MaxRecvMsgSize().Size))
	}
	var extractFeRespStatusWithErrMsgFunc commoninterceptors.ExtractFeRespStatusWithErrMsgFunc
	extractFeRespStatusWithErrMsgFunc = fepkg.ExtractFeRespStatusWithErrMsgFunc

	s := epifigrpc.NewSecureServerWithInterceptors(gconf.GrpcServerConfig(), gconf.Flags().TrimDebugMessageFromStatus, string(gconf.Name()), histogramBuckets, unaryInterceptors, streamInterceptors, extractFeRespStatusWithErrMsgFunc, serverOptions...)

	if cfg.IsNonProdEnv(gconf.Environment()) {
		reflection.Register(s)
	}

	httpMux := http.NewServeMux()
	_ = httpMux

	var sqsSubscribers []queue.Subscriber
	var subs []queue.Subscriber
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSubs []*sqs.ExtendedSqsSubscriber

	subs, extSubs, err = setupAccounts(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, savingsClient, usersClient, authClient, depositClient, actorClient, epifiCRDB, groupClient, accountsClient, bankCustomerServiceClient, payPGDB, savingsClientVar4, payCacheStorage, payRedisStore, healthEngineServiceClient, commsClient, docsClient, accountStatementClient, celestialClient, operationalStatusServiceClient)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupActor(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, actorPGDB, piClient, accountPIRelationClient, usersClient, merchantServiceClient, uPIClient, vendormappingActorRedisStore, timelineServiceClient, authClient, beneficiaryManagementClient, useCaseDbResourceProvider, tspUserServiceClient, bankCustomerServiceClient, accountAggregatorClient)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupMerchant(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, merchantPGDB, piClient, payMerchantRedisStore, merchantResolutionClient)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupPaymentinstrument(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, paymentInstrumentPGDB, savingsClient, vendormappingPiRedisStore, accountPIRelationClient, useCaseDbResourceProvider, useCaseDbResourceProviderTxnExec, actorClient, usersClient, groupClient, piClient, accountAggregatorClient)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupTimeline(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, timelinePGDB, merchantServiceClient, actorClient, groupClient, usersClient, userTimelineRedisStore)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	explorerpb.RegisterConfigExplorerServer(s, explorer.NewServer(configNameToConfMap))

	cleanupFn, err := hook.InitAccountsServiceGroup(epifiCRDB) // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "InitAccountsServiceGroup"), zap.Error(err))
		return err
	}
	defer cleanupFn()

	cleanupFnVar2, err := servergenwire2.LoadCollapserWithGrpcServer(gconf, s) // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "LoadCollapserWithGrpcServer"), zap.Error(err))
		return err
	}
	defer cleanupFnVar2()

	// Load rpc level log sampling rate descriptors.
	epifigrpc.GetRPCLogSamplingRatesMapInstance().Load(s)

	initNotifier <- gconf.Name()

	// TODO(Sundeep): Remove this additional logic for starting the servers inside go-routine, by exposing functions
	// from epifiserver package that take errgroup.Group as parameter.
	grp, _ := errgroup.WithContext(context.Background())

	grp.Go(func() error {
		epifiserver.StartSecureServer(s, gconf.ServerPorts(), string(gconf.Name()))
		return nil
	})

	// Wait for server health before starting SQS workers
	if queue.IsWorkerInitializationEnabled() {
		goroutine.RunWithCtx(ctx, func(ctx context.Context) {
			if epifiserver.IsServerReady(gconf.ServerPorts().HttpPort) {
				logger.Info(ctx, "Server is healthy, starting SQS workers")
				sqs.StartSQSWorkers(ctx, sqsSubscribers, extendedSqsSubscribers)
			} else {
				logger.PanicWithCtx(ctx, "Server health check timed out, not starting SQS workers")
			}
		})
	}
	if err := grp.Wait(); err != nil {
		return err
	}

	return nil
}

// nolint: funlen
func setupAccounts(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	savingsClient savingspb.SavingsClient,
	usersClient userpb.UsersClient,
	authClient authpb.AuthClient,
	depositClient depositpb.DepositClient,
	actorClient actorpb.ActorClient,
	epifiCRDB types.EpifiCRDB,
	groupClient usergrouppb.GroupClient,
	accountsClient vgaccountpb.AccountsClient,
	bankCustomerServiceClient bcpb.BankCustomerServiceClient,
	payPGDB types.PayPGDB,
	savingsClientVar4 vgsavingspb.SavingsClient,
	payCacheStorage accountstypes.PayCacheStorage,
	payRedisStore accountstypes.PayRedisStore,
	healthEngineServiceClient healthenginepb.HealthEngineServiceClient,
	commsClient accountstypes.AccountCommsClientWithInterceptors,
	docsClient docstypes.DocsClientWithInterceptors,
	accountStatementClient accountstatementpb.AccountStatementClient,
	celestialClient celestial.CelestialClient,
	operationalStatusServiceClient operstatuspb.OperationalStatusServiceClient) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	accountsConf, err := accountsconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.ACCOUNTS_SERVICE))
		return nil, nil, err
	}
	_ = accountsConf

	accountsGenConf, err := dynconf.LoadConfig(accountsconf.Load, genconf2.NewConfig, cfg.ACCOUNTS_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.ACCOUNTS_SERVICE))
		return nil, nil, err
	}

	_ = accountsGenConf

	accountStmtPublisher, err := sqs.NewPublisherWithConfig(ctx, accountsGenConf.AccountStmtPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	accountMonthlyStmtPublisher, err := sqs.NewPublisherWithConfig(ctx, accountsGenConf.AccountMonthlyStmtPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}

	accountOperationalStatusPublisher, err := sns.NewSnsPublisherWithConfig(ctx, accountsGenConf.AccountOperationalStatusPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}
	balanceChangeEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, accountsGenConf.BalanceChangeEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}

	sftpStatementS3Client := s3pkg.NewClient(awsConf, accountsGenConf.SftpStatementAwsBucket().AwsBucket)
	sftpStatementS3ClientVar2 := s3pkg.NewClient(awsConf, accountsGenConf.SftpStatementAwsBucket().AwsBucket)

	service, err := wire.InitialiseStatementService(savingsClient, accountStmtPublisher, usersClient, authClient, depositClient, actorClient, epifiCRDB, groupClient, accountsConf, sftpStatementS3Client, accountMonthlyStmtPublisher, accountsClient, accountsGenConf, bankCustomerServiceClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	accountstatementpb.RegisterAccountStatementServer(s, service)

	serviceVar2 := wire.InitialiseOperationalStatusService(accountsGenConf, epifiCRDB, savingsClient, accountsClient, usersClient, accountOperationalStatusPublisher, broker)

	operstatuspb.RegisterOperationalStatusServiceServer(s, serviceVar2)

	serviceVar3 := wire.InitialiseBalanceService(payPGDB, savingsClient, savingsClientVar4, authClient, actorClient, bankCustomerServiceClient, payCacheStorage, payRedisStore, accountsConf, groupClient, usersClient, balanceChangeEventPublisher, accountsGenConf, broker, healthEngineServiceClient)

	accountbalancepb.RegisterBalanceServer(s, serviceVar3)

	statementConsumerService, err := wire.InitialiseStatementConsumerService(commsClient, accountsClient, docsClient, usersClient, accountsConf, epifiCRDB, groupClient, savingsClient, accountStatementClient, sftpStatementS3ClientVar2, actorClient, celestialClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, accountsGenConf.AccountStmtSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		accountstatementpb.RegisterProcessAccountStatementMethodToSubscriber(subscriber, statementConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, accountsGenConf.AccountMonthlyStmtSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		accountstatementpb.RegisterProcessMonthlyAccountStatementMethodToSubscriber(subscriber, statementConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, accountsGenConf.SftpStatementEventSubsriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		accountstatementpb.RegisterProcessAccountStatementSftpEventMethodToSubscriber(subscriber, statementConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	consumerService := wire.InitialiseOperStatusConsumerService(savingsClient, accountOperationalStatusPublisher, operationalStatusServiceClient, accountsGenConf, epifiCRDB, broker)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, accountsGenConf.AccountStatusCallBackSub(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		operstatuspb.RegisterProcessFederalAccountStatusCallBackMethodToSubscriber(subscriber, consumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	accountsDevService := wire.InitialiseAccountsDevService(payPGDB, payCacheStorage, accountsConf, accountsGenConf)

	developer2.RegisterAccountsDbStatesServer(s, accountsDevService)

	configNameToConfMap[cfg.ConfigName(cfg.ACCOUNTS_SERVICE)] = &commonexplorer.Config{StaticConf: &accountsconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupActor(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	actorPGDB types.ActorPGDB,
	piClient pipb.PiClient,
	accountPIRelationClient accountpipb.AccountPIRelationClient,
	usersClient userpb.UsersClient,
	merchantServiceClient mpb.MerchantServiceClient,
	uPIClient upipb.UPIClient,
	vendormappingActorRedisStore actorwiretypes.VendormappingActorRedisStore,
	timelineServiceClient timeline.TimelineServiceClient,
	authClient authpb.AuthClient,
	beneficiaryManagementClient beneficiarymanagementpb.BeneficiaryManagementClient,
	useCaseDbResourceProvider *usecase.DBResourceProvider[*gorm.DB],
	tspUserServiceClient tspuserpb.TspUserServiceClient,
	bankCustomerServiceClient bcpb.BankCustomerServiceClient,
	accountAggregatorClient aaorderpb.AccountAggregatorClient) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	actorConf, err := actorconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.ACTOR_SERVICE))
		return nil, nil, err
	}
	_ = actorConf

	actorGenConf, err := dynconf.LoadConfig(actorconf.Load, genconf3.NewConfig, cfg.ACTOR_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.ACTOR_SERVICE))
		return nil, nil, err
	}

	_ = actorGenConf

	actorPurgePublisher, err := sqs.NewPublisherWithConfig(ctx, actorGenConf.ActorPurgePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	piPurgePublisher, err := sqs.NewPublisherWithConfig(ctx, actorGenConf.PiPurgePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}

	actorCreationEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, actorGenConf.ActorCreationEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}

	serviceVar4, err := wire2.InitializeService(actorConf, actorPGDB, piClient, accountPIRelationClient, usersClient, broker, merchantServiceClient, uPIClient, vendormappingActorRedisStore, actorCreationEventPublisher, timelineServiceClient, authClient, gconf.VendorApiConf(), actorGenConf, beneficiaryManagementClient, useCaseDbResourceProvider, tspUserServiceClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	actorpb.RegisterActorServer(s, serviceVar4)

	actorDevEntity, err := wire2.InitializeActorDevEntityService(actorConf, actorPGDB, usersClient, vendormappingActorRedisStore, bankCustomerServiceClient, actorGenConf, useCaseDbResourceProvider)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	developer4.RegisterDevActorServer(s, actorDevEntity)

	serviceVar5, err := wire2.InitializeActorConsumerService(actorConf, actorPGDB, actorPurgePublisher, accountAggregatorClient, piPurgePublisher, vendormappingActorRedisStore, actorGenConf, useCaseDbResourceProvider)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	actorconsumerpb.RegisterConsumerServer(s, serviceVar5)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, actorGenConf.ActorPiRelationPurgeSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		actorconsumerpb.RegisterPurgeActorPiResolutionMethodToSubscriber(subscriber, serviceVar5)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, actorGenConf.ActorPurgeSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		actorconsumerpb.RegisterPurgeWealthActorsMethodToSubscriber(subscriber, serviceVar5)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}
	configNameToConfMap[cfg.ConfigName(cfg.ACTOR_SERVICE)] = &commonexplorer.Config{StaticConf: &actorconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupMerchant(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	merchantPGDB types.MerchantPGDB,
	piClient pipb.PiClient,
	payMerchantRedisStore merchanttypes.PayMerchantRedisStore,
	merchantResolutionClient merchantresolutionpb.MerchantResolutionClient) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	merchantConf, err := merchantconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.MERCHANT_SERVICE))
		return nil, nil, err
	}
	_ = merchantConf

	merchantGenConf, err := dynconf.LoadConfig(merchantconf.Load, genconf4.NewConfig, cfg.MERCHANT_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.MERCHANT_SERVICE))
		return nil, nil, err
	}

	_ = merchantGenConf

	serviceVar6, err := wire3.InitializeMerchantService(merchantPGDB, piClient, merchantConf, payMerchantRedisStore, merchantGenConf, merchantResolutionClient, gconf.VendorApiConf())
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	mpb.RegisterMerchantServiceServer(s, serviceVar6)

	merchantDevEntity, err := wire3.InitializeMerchantDevService(merchantPGDB, merchantConf, payMerchantRedisStore, merchantGenConf)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	developer6.RegisterDevMerchantServer(s, merchantDevEntity)

	configNameToConfMap[cfg.ConfigName(cfg.MERCHANT_SERVICE)] = &commonexplorer.Config{StaticConf: &merchantconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupPaymentinstrument(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	paymentInstrumentPGDB types.PaymentInstrumentPGDB,
	savingsClient savingspb.SavingsClient,
	vendormappingPiRedisStore piwiretypes.VendormappingPiRedisStore,
	accountPIRelationClient accountpipb.AccountPIRelationClient,
	useCaseDbResourceProvider *usecase.DBResourceProvider[*gorm.DB],
	useCaseDbResourceProviderTxnExec *usecase.DBResourceProvider[storage2.IdempotentTxnExecutor],
	actorClient actorpb.ActorClient,
	usersClient userpb.UsersClient,
	groupClient usergrouppb.GroupClient,
	piClient pipb.PiClient,
	accountAggregatorClient aaorderpb.AccountAggregatorClient) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	paymentinstrumentConf, err := paymentinstrumentconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.PAYMENT_INSTRUMENT_SERVICE))
		return nil, nil, err
	}
	_ = paymentinstrumentConf

	paymentinstrumentGenConf, err := dynconf.LoadConfig(paymentinstrumentconf.Load, genconf5.NewConfig, cfg.PAYMENT_INSTRUMENT_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.PAYMENT_INSTRUMENT_SERVICE))
		return nil, nil, err
	}

	_ = paymentinstrumentGenConf

	aaTxnPurgePublisher, err := sqs.NewPublisherWithConfig(ctx, paymentinstrumentGenConf.AaTxnPurgePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}

	piEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, paymentinstrumentGenConf.PiEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}

	serviceVar7 := wire4.InitializePiService(paymentinstrumentConf, paymentInstrumentPGDB, piEventPublisher, savingsClient, vendormappingPiRedisStore, accountPIRelationClient, paymentinstrumentGenConf, useCaseDbResourceProvider, useCaseDbResourceProviderTxnExec, actorClient, usersClient, groupClient)

	pipb.RegisterPiServer(s, serviceVar7)

	serviceVar8 := wire4.InitializeAccountPiService(paymentInstrumentPGDB, piEventPublisher, piClient, vendormappingPiRedisStore, paymentinstrumentConf, paymentinstrumentGenConf)

	accountpipb.RegisterAccountPIRelationServer(s, serviceVar8)

	pIDevService := wire4.InitializeDbStatesPiService(paymentInstrumentPGDB, vendormappingPiRedisStore, paymentinstrumentConf, paymentinstrumentGenConf, useCaseDbResourceProvider, useCaseDbResourceProviderTxnExec)

	developer8.RegisterDevPaymentIntrumentServer(s, pIDevService)

	serviceVar9 := wire4.InitializePiConsumerService(paymentInstrumentPGDB, aaTxnPurgePublisher, accountAggregatorClient, vendormappingPiRedisStore, paymentinstrumentConf, paymentinstrumentGenConf, useCaseDbResourceProvider, useCaseDbResourceProviderTxnExec)

	piconsumerpb.RegisterConsumerServer(s, serviceVar9)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, paymentinstrumentGenConf.AaAccountPiPurgeSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		piconsumerpb.RegisterPurgeAaAccountPiRelationMethodToSubscriber(subscriber, serviceVar9)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, paymentinstrumentGenConf.PiPurgeSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		piconsumerpb.RegisterPurgeWealthPisMethodToSubscriber(subscriber, serviceVar9)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}
	configNameToConfMap[cfg.ConfigName(cfg.PAYMENT_INSTRUMENT_SERVICE)] = &commonexplorer.Config{StaticConf: &paymentinstrumentconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupTimeline(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	timelinePGDB types.TimelinePGDB,
	merchantServiceClient mpb.MerchantServiceClient,
	actorClient actorpb.ActorClient,
	groupClient usergrouppb.GroupClient,
	usersClient userpb.UsersClient,
	userTimelineRedisStore timelinetypes.UserTimelineRedisStore) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	timelineConf, err := timelineconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.TIMELINE_SERVICE))
		return nil, nil, err
	}
	_ = timelineConf

	timelineGenConf, err := dynconf.LoadConfig(timelineconf.Load, genconf6.NewConfig, cfg.TIMELINE_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.TIMELINE_SERVICE))
		return nil, nil, err
	}

	_ = timelineGenConf

	timeLineEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, timelineGenConf.TimeLineEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}
	timelineMerchantMergeEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, timelineGenConf.TimelineMerchantMergeEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}
	backFillTxnTimelineEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, timelineGenConf.BackFillTxnTimelineEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}

	serviceVar10 := wire5.InitializeService(timelinePGDB, timeLineEventPublisher, timelineConf, timelineMerchantMergeEventPublisher, merchantServiceClient, actorClient, groupClient, usersClient, userTimelineRedisStore, backFillTxnTimelineEventPublisher, timelineGenConf)

	timeline.RegisterTimelineServiceServer(s, serviceVar10)

	timelineDevService := wire5.InitializeTimelineDevService(timelinePGDB, timelineConf, userTimelineRedisStore, timelineGenConf)

	developer10.RegisterDevTimelineServer(s, timelineDevService)

	serviceVar11 := wire5.InitializeConsumerService(timelinePGDB, timeLineEventPublisher, timelineConf, userTimelineRedisStore, timelineGenConf)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, timelineGenConf.OrderUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		timeline.RegisterUpdateTimelineMethodToSubscriber(subscriber, serviceVar11)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, timelineGenConf.InPaymentOrderUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		timeline.RegisterUpdateInPaymentOrderTimelineMethodToSubscriber(subscriber, serviceVar11)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}
	configNameToConfMap[cfg.ConfigName(cfg.TIMELINE_SERVICE)] = &commonexplorer.Config{StaticConf: &timelineconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

func initLogger() error {
	// Load configuration
	conf, err := config.Load(cfg.ACTOR_SERVER)
	if err != nil {
		// cannot log error since logger is not initialized yet.
		return err
	}

	logger.InitV2(conf.Environment, conf.Logging)
	if conf.SecureLogging != nil {
		logger.InitSecureLoggerV2(conf.SecureLogging)
	}
	return nil
}

func getEnvNamespaceClient(ns temporalpkg.Namespace, env string) string {
	if cfg.IsLocalEnv(env) {
		return strings.ToLower(string(ns))
	}

	return fmt.Sprintf("%s-%s", env, strings.ToLower(string(ns)))
}

func getSQSConsumerInterceptors() []grpc.UnaryServerInterceptor {
	return []grpc.UnaryServerInterceptor{questinterceptor.UserContextInterceptor}
}
