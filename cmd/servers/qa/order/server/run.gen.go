// code generated by tools/servergen
package server

import (
	context "context"
	"fmt"
	"net/http"
	strings "strings"
	time "time"

	awssdk "github.com/aws/aws-sdk-go-v2/aws"
	awssqs "github.com/aws/aws-sdk-go-v2/service/sqs"
	prometheus "github.com/prometheus/client_golang/prometheus"
	redis "github.com/redis/go-redis/v9"
	analytics "github.com/rudderlabs/analytics-go"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
	reflection "google.golang.org/grpc/reflection"
	gorm "gorm.io/gorm"

	celestialpb "github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/pkg/async/goroutine"
	awsconfpkg "github.com/epifi/be-common/pkg/aws/v2/config"
	s3pkg "github.com/epifi/be-common/pkg/aws/v2/s3"
	sns "github.com/epifi/be-common/pkg/aws/v2/sns"
	sqs "github.com/epifi/be-common/pkg/aws/v2/sqs"
	awswire "github.com/epifi/be-common/pkg/aws/v2/wire"
	cache "github.com/epifi/be-common/pkg/cache"
	cfg "github.com/epifi/be-common/pkg/cfg"
	consul "github.com/epifi/be-common/pkg/cfg/consul"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	commonexplorer "github.com/epifi/be-common/pkg/cfg/explorer"
	config "github.com/epifi/be-common/pkg/cmd/config"
	genconf "github.com/epifi/be-common/pkg/cmd/config/genconf"
	types "github.com/epifi/be-common/pkg/cmd/types"
	epificontext "github.com/epifi/be-common/pkg/epificontext"
	epifigrpc "github.com/epifi/be-common/pkg/epifigrpc"
	commoninterceptors "github.com/epifi/be-common/pkg/epifigrpc/interceptors"
	servergenwire3 "github.com/epifi/be-common/pkg/epifigrpc/interceptors/servergen_wire"
	resolver "github.com/epifi/be-common/pkg/epifigrpc/resolver"
	epifiserver "github.com/epifi/be-common/pkg/epifiserver"
	temporalpkg "github.com/epifi/be-common/pkg/epifitemporal"
	temporalcl "github.com/epifi/be-common/pkg/epifitemporal/client"
	namespace "github.com/epifi/be-common/pkg/epifitemporal/namespace"
	errgroup "github.com/epifi/be-common/pkg/errgroup"
	events "github.com/epifi/be-common/pkg/events"
	logger "github.com/epifi/be-common/pkg/logger"
	profiling "github.com/epifi/be-common/pkg/profiling"
	queue "github.com/epifi/be-common/pkg/queue"
	ratelimiter "github.com/epifi/be-common/pkg/ratelimiter"
	store "github.com/epifi/be-common/pkg/ratelimiter/store"
	storage "github.com/epifi/be-common/pkg/storage"
	storage2 "github.com/epifi/be-common/pkg/storage/v2"
	usecase "github.com/epifi/be-common/pkg/storage/v2/usecase"
	otel "github.com/epifi/be-common/pkg/tracing/opentelemetry"
	accountbalancepb "github.com/epifi/gamma/api/accounts/balance"
	operationalstatuspb "github.com/epifi/gamma/api/accounts/operstatus"
	statement "github.com/epifi/gamma/api/accounts/statement"
	actor "github.com/epifi/gamma/api/actor"
	txnaggregatespb "github.com/epifi/gamma/api/analyser/txnaggregates"
	authpb "github.com/epifi/gamma/api/auth"
	locationpb "github.com/epifi/gamma/api/auth/location"
	authorchestratorpb "github.com/epifi/gamma/api/auth/orchestrator"
	bankcustpb "github.com/epifi/gamma/api/bankcust"
	billpaypb "github.com/epifi/gamma/api/billpay"
	dcmandatepb "github.com/epifi/gamma/api/card/debitcardmandate"
	cardprovpb "github.com/epifi/gamma/api/card/provisioning"
	categorizer "github.com/epifi/gamma/api/categorizer"
	comms "github.com/epifi/gamma/api/comms"
	connectedaccountpb "github.com/epifi/gamma/api/connected_account"
	consentpb "github.com/epifi/gamma/api/consent"
	disputepb "github.com/epifi/gamma/api/cx/dispute"
	watsonpb "github.com/epifi/gamma/api/cx/watson"
	depositpb "github.com/epifi/gamma/api/deposit"
	docspb "github.com/epifi/gamma/api/docs"
	ffpb "github.com/epifi/gamma/api/firefly"
	accounting "github.com/epifi/gamma/api/firefly/accounting"
	fireflyv2pb "github.com/epifi/gamma/api/firefly/v2"
	healthengine "github.com/epifi/gamma/api/health_engine"
	catalogmanagerpb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	kycpb "github.com/epifi/gamma/api/kyc"
	merchant "github.com/epifi/gamma/api/merchant"
	nudge "github.com/epifi/gamma/api/nudge"
	orderpb "github.com/epifi/gamma/api/order"
	aapb "github.com/epifi/gamma/api/order/aa"
	actoractivitypb "github.com/epifi/gamma/api/order/actoractivity"
	campaignpb "github.com/epifi/gamma/api/order/campaign"
	cxpb "github.com/epifi/gamma/api/order/cx"
	developer2 "github.com/epifi/gamma/api/order/developer"
	paymentpb "github.com/epifi/gamma/api/order/payment"
	disputes "github.com/epifi/gamma/api/order/payment/disputes"
	reconpb "github.com/epifi/gamma/api/order/recon"
	p2ppb "github.com/epifi/gamma/api/p2pinvestment"
	parserpb "github.com/epifi/gamma/api/parser"
	pay "github.com/epifi/gamma/api/pay"
	beneficiarymanagementpb "github.com/epifi/gamma/api/pay/beneficiarymanagement"
	paycxpb "github.com/epifi/gamma/api/pay/cx"
	paydeveloper "github.com/epifi/gamma/api/pay/developer"
	internationalfundtransfer "github.com/epifi/gamma/api/pay/internationalfundtransfer"
	consumer3 "github.com/epifi/gamma/api/pay/internationalfundtransfer/consumer"
	filegenpb "github.com/epifi/gamma/api/pay/internationalfundtransfer/file_generator"
	consumer2 "github.com/epifi/gamma/api/pay/internationalfundtransfer/file_generator/consumer"
	forex2 "github.com/epifi/gamma/api/pay/internationalfundtransfer/forex"
	payincidentmanagerconsumer "github.com/epifi/gamma/api/pay/payincidentmanager"
	payincidentmgrconsumerpb "github.com/epifi/gamma/api/pay/payincidentmanager/consumer"
	pgconsumerpb "github.com/epifi/gamma/api/pay/paymentgateway/consumer"
	paymentrecommendationsystem2 "github.com/epifi/gamma/api/pay/paymentrecommendationsystem"
	savingsconsumerpb "github.com/epifi/gamma/api/pay/savings_account/consumer"
	velocityengine2 "github.com/epifi/gamma/api/pay/velocity_engine"
	pipb "github.com/epifi/gamma/api/paymentinstrument"
	accountpipb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	explorerpb "github.com/epifi/gamma/api/pkg/cfg/explorer"
	managerpb "github.com/epifi/gamma/api/quest/manager"
	rppb "github.com/epifi/gamma/api/recurringpayment"
	recurringpaymentconsumerpb "github.com/epifi/gamma/api/recurringpayment/consumer"
	recurringpaymentcxpb "github.com/epifi/gamma/api/recurringpayment/cx"
	recurringpaymentdeveloper "github.com/epifi/gamma/api/recurringpayment/developer"
	enachpb "github.com/epifi/gamma/api/recurringpayment/enach"
	enachconsumerpb "github.com/epifi/gamma/api/recurringpayment/enach/consumer"
	enachdevpb "github.com/epifi/gamma/api/recurringpayment/enach/developer"
	paymentgateway2 "github.com/epifi/gamma/api/recurringpayment/paymentgateway"
	sipb "github.com/epifi/gamma/api/recurringpayment/standinginstruction"
	rewardspinotpb "github.com/epifi/gamma/api/rewards/pinot"
	projectorpb "github.com/epifi/gamma/api/rewards/projector"
	savingspb "github.com/epifi/gamma/api/savings"
	segmentpb "github.com/epifi/gamma/api/segment"
	tieringpb "github.com/epifi/gamma/api/tiering"
	timelinepb "github.com/epifi/gamma/api/timeline"
	tspuserpb "github.com/epifi/gamma/api/tspuser"
	_ "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/screen_option_dependency"
	upcomingtxnspb "github.com/epifi/gamma/api/upcomingtransactions"
	upipb "github.com/epifi/gamma/api/upi"
	consumer5 "github.com/epifi/gamma/api/upi/consumer"
	upicxpb "github.com/epifi/gamma/api/upi/cx"
	upideveloper "github.com/epifi/gamma/api/upi/developer"
	upimandatepb "github.com/epifi/gamma/api/upi/mandate"
	consumer6 "github.com/epifi/gamma/api/upi/mandate/consumer"
	upionboardingpb "github.com/epifi/gamma/api/upi/onboarding"
	upionboardingconsumerpb "github.com/epifi/gamma/api/upi/onboarding/consumer"
	simulation2 "github.com/epifi/gamma/api/upi/simulation"
	user "github.com/epifi/gamma/api/user"
	usergrouppb "github.com/epifi/gamma/api/user/group"
	onboarding "github.com/epifi/gamma/api/user/onboarding"
	usstocksaccountpb "github.com/epifi/gamma/api/usstocks/account"
	usstockscatalogpb "github.com/epifi/gamma/api/usstocks/catalog"
	usstocksorderpb "github.com/epifi/gamma/api/usstocks/order"
	billpayvgpb "github.com/epifi/gamma/api/vendorgateway/billpay"
	location "github.com/epifi/gamma/api/vendorgateway/location"
	merchantresolutionpb "github.com/epifi/gamma/api/vendorgateway/merchantresolution"
	namecheck "github.com/epifi/gamma/api/vendorgateway/namecheck"
	vgaccountspb "github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	vgenachpb "github.com/epifi/gamma/api/vendorgateway/openbanking/enach"
	vgpaymentpb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment"
	vgb2cpaymentpb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment/b2c"
	vgiftpb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment/internationalfundtransfer"
	sivg "github.com/epifi/gamma/api/vendorgateway/openbanking/standinginstruction"
	vgupipb "github.com/epifi/gamma/api/vendorgateway/openbanking/upi"
	vgparserpb "github.com/epifi/gamma/api/vendorgateway/parser"
	vgpgpb "github.com/epifi/gamma/api/vendorgateway/pg"
	rechargepb "github.com/epifi/gamma/api/vendorgateway/recharge"
	billpayconf "github.com/epifi/gamma/billpay/config"
	genconf2 "github.com/epifi/gamma/billpay/config/genconf"
	wire5 "github.com/epifi/gamma/billpay/wire"
	wiretypes "github.com/epifi/gamma/billpay/wire/types"
	hook "github.com/epifi/gamma/cmd/servers/qa/order/hook"
	orderconf "github.com/epifi/gamma/order/config"
	config2 "github.com/epifi/gamma/order/config/genconf"
	wire "github.com/epifi/gamma/order/wire"
	types2 "github.com/epifi/gamma/order/wire/types"
	payconf "github.com/epifi/gamma/pay/config/server"
	payservergenconfig "github.com/epifi/gamma/pay/config/server/genconf"
	wire2 "github.com/epifi/gamma/pay/wire"
	types3 "github.com/epifi/gamma/pay/wire/types"
	explorer "github.com/epifi/gamma/pkg/cfg/explorer"
	questinterceptor "github.com/epifi/gamma/pkg/epifigrpc/interceptors/quest"
	servergenwire2 "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
	fepkg "github.com/epifi/gamma/pkg/frontend/msg"
	questsdkinit "github.com/epifi/gamma/quest/sdk/init"
	recurringpaymentconf "github.com/epifi/gamma/recurringpayment/config/server"
	rpservergenconf "github.com/epifi/gamma/recurringpayment/config/server/genconf"
	wire3 "github.com/epifi/gamma/recurringpayment/wire"
	types5 "github.com/epifi/gamma/recurringpayment/wire/types"
	upiconf "github.com/epifi/gamma/upi/config"
	upidyanmicconf "github.com/epifi/gamma/upi/config/genconf"
	servergenwire "github.com/epifi/gamma/upi/interceptor/servergen_wire"
	wire4 "github.com/epifi/gamma/upi/wire"
	upitypes "github.com/epifi/gamma/upi/wire/types"
	vgtypes "github.com/epifi/gamma/vendorgateway/wire/types"
)

// nolint: funlen
func Run(ctx context.Context, initNotifier chan<- cfg.ServerName) error {
	// Panic handler for logging panics in standard format
	epifiserver.HandlePanic()
	resolver.RegisterBuilder(resolver.ConsulSchemeName)
	var configNameToConfMap = make(commonexplorer.CfgExplorerMap)

	err := initLogger()
	if err != nil {
		// cannot log error since logger maynot be initialized yet.
		return err
	}
	ctx = epificontext.WithTraceId(ctx, metadata.MD{})
	// Load static configuration
	conf, err := config.Load(cfg.ORDER_SERVER)
	if err != nil {
		logger.Error(ctx, "failed to load static conf", zap.Error(err))
		return err
	}
	_ = conf
	// Load generated dynamic configuration
	gconf, err := genconf.Load(cfg.ORDER_SERVER)
	if err != nil {
		logger.Error(ctx, "failed to load dynamic conf", zap.Error(err))
		return err
	}
	logger.Info(ctx, "initiating server")

	if gconf.Profiling().AutomaticProfiling().EnableAutoProfiling() {
		profilingErr := profiling.AutomaticProfiling(context.Background(), cfg.ORDER_SERVER, gconf.Profiling(), gconf.Environment(), gconf.AWS().Region)
		if profilingErr != nil {
			logger.Error(ctx, "failed to start automatic profiling", zap.Error(profilingErr))
			return profilingErr
		}
	}

	if gconf.Tracing().Enable {
		_, shutdown, tracingErr := otel.ConfigureOpenTelemetryV2(context.Background(), cfg.ORDER_SERVER, gconf.Environment(), gconf.Profiling().PyroscopeProfiling().EnablePyroscope())
		if tracingErr != nil {
			logger.Error(ctx, "failed to configure open telemetry", zap.Error(tracingErr))
			return err
		}
		defer shutdown()
	}

	err = profiling.PyroscopeProfilingV2(gconf.Name(), gconf.Profiling(), gconf.Environment())
	if err != nil {
		logger.Error(ctx, "error starting pyroscope profiler", zap.Error(err))
	}

	if !cfg.IsLocalEnv(gconf.Environment()) && !cfg.IsRemoteDebugEnabled() && !cfg.IsTestTenantEnabled() && !cfg.IsTeamSpaceTenant() {
		initDisc, _ := consul.GetBool(consul.GetInitDiscoveryKey(gconf.Environment(), string(gconf.Name())))
		queue.SetWorkerInitialization(initDisc)
	}

	rateLimiterRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()[gconf.RedisRateLimiterName()], gconf.Tracing().Enable)
	defer func() { _ = rateLimiterRedisStore.Close() }()

	var (
		crdbResourceMap    *storage2.DBResourceProvider[*gorm.DB]
		crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor]
	)

	var dbConnTeardown func()
	crdbResourceMap, crdbTxnResourceMap, dbConnTeardown, err = storage2.NewDBResourceProviderV2(conf.DBConfigMap.GetOwnershipToDbConfigMap(), conf.Tracing.Enable, gconf.PgdbConns())
	if err != nil {
		logger.Error(ctx, "failed to get db resource provider", zap.Error(err))
		return err
	}
	defer func() {
		dbConnTeardown()
	}()

	epifiCRDB, err := storage2.NewCRDBWithConfig(gconf.Databases()["EpifiCRDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "EpifiCRDB"))
		return err
	}
	epifiCRDBSqlDb, err := epifiCRDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "EpifiCRDB"))
		return err
	}
	defer func() { _ = epifiCRDBSqlDb.Close() }()
	epifiWealthCRDB, err := storage2.NewCRDBWithConfig(gconf.Databases()["EpifiWealthCRDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "EpifiWealthCRDB"))
		return err
	}
	epifiWealthCRDBSqlDb, err := epifiWealthCRDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "EpifiWealthCRDB"))
		return err
	}
	defer func() { _ = epifiWealthCRDBSqlDb.Close() }()

	payPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["PayPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "PayPGDB"))
		return err
	}
	payPGDBSqlDb, err := payPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "PayPGDB"))
		return err
	}
	defer func() { _ = payPGDBSqlDb.Close() }()
	recurringPaymentPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["RecurringPaymentPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "RecurringPaymentPGDB"))
		return err
	}
	recurringPaymentPGDBSqlDb, err := recurringPaymentPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "RecurringPaymentPGDB"))
		return err
	}
	defer func() { _ = recurringPaymentPGDBSqlDb.Close() }()
	billpayPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["BillpayPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "BillpayPGDB"))
		return err
	}
	billpayPGDBSqlDb, err := billpayPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "BillpayPGDB"))
		return err
	}
	defer func() { _ = billpayPGDBSqlDb.Close() }()

	authConn := epifigrpc.NewServerConn(cfg.ORDER_SERVER, cfg.AUTH_SERVER)
	defer epifigrpc.CloseConn(authConn)
	locationClient := locationpb.NewLocationClient(authConn)

	collapserRueidisRedisStore, err := storage2.NewRueidisClientFromConfig(gconf.RueidisRedisClients()["CollapserRueidisRedisStore"])
	if err != nil {
		logger.Error(ctx, "failed to establish rueidis redis client conn for CollapserRueidisRedisStore", zap.Error(err))
		return err
	}
	defer func() { collapserRueidisRedisStore.Close() }()
	collapserRueidisCacheStorage := cache.NewRueidisCacheStorageWithHystrix(cache.NewRueidisCacheWithOptions(collapserRueidisRedisStore), gconf.RueidisRedisClients()["CollapserRueidisRedisStore"].Hystrix)
	growthinfraConn := epifigrpc.NewServerConn(cfg.ORDER_SERVER, cfg.GROWTH_INFRA_SERVER)
	defer epifigrpc.CloseConn(growthinfraConn)
	managerClient := managerpb.NewManagerClient(growthinfraConn)
	groupClient := usergrouppb.NewGroupClient(authConn)
	usersClient := user.NewUsersClient(authConn)
	actorConn := epifigrpc.NewServerConn(cfg.ORDER_SERVER, cfg.ACTOR_SERVER)
	defer epifigrpc.CloseConn(actorConn)
	actorClient := actor.NewActorClient(actorConn)
	segmentationServiceClient := segmentpb.NewSegmentationServiceClient(growthinfraConn)
	piClient := pipb.NewPiClient(actorConn)
	authClient := authpb.NewAuthClient(authConn)
	timelineServiceClient := timelinepb.NewTimelineServiceClient(actorConn)
	wealthdmfConn := epifigrpc.NewServerConn(cfg.ORDER_SERVER, cfg.WEALTH_DMF_SERVER)
	defer epifigrpc.CloseConn(wealthdmfConn)
	depositClient := depositpb.NewDepositClient(wealthdmfConn)
	cxConn := epifigrpc.NewServerConn(cfg.ORDER_SERVER, cfg.CX_SERVER)
	defer epifigrpc.CloseConn(cxConn)
	disputeClient := disputepb.NewDisputeClient(cxConn)
	accountPIRelationClient := accountpipb.NewAccountPIRelationClient(actorConn)
	centralgrowthConn := epifigrpc.NewServerConn(cfg.ORDER_SERVER, cfg.CENTRAL_GROWTH_SERVER)
	defer epifigrpc.CloseConn(centralgrowthConn)
	connectedAccountClient := connectedaccountpb.NewConnectedAccountClient(centralgrowthConn)
	onboardingConn := epifigrpc.NewServerConn(cfg.ORDER_SERVER, cfg.ONBOARDING_SERVER)
	defer epifigrpc.CloseConn(onboardingConn)
	kycClient := kycpb.NewKycClient(onboardingConn)
	savingsClient := savingspb.NewSavingsClient(centralgrowthConn)
	growthinfraConnVar3ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar2 := servergenwire2.NewOrderRequestClientInterceptor()
	if unaryClientInterceptorVar2 != nil {
		growthinfraConnVar3ClientInterceptors = append(growthinfraConnVar3ClientInterceptors, unaryClientInterceptorVar2)
	}
	growthinfraConnVar3 := epifigrpc.NewServerConn(cfg.ORDER_SERVER, cfg.GROWTH_INFRA_SERVER, growthinfraConnVar3ClientInterceptors...)
	defer epifigrpc.CloseConn(growthinfraConnVar3)
	commsClient := comms.NewCommsClient(growthinfraConnVar3)
	vendorgatewayConnClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar5 := servergenwire.NewReverseLocationClientInterceptor(locationClient)
	if unaryClientInterceptorVar5 != nil {
		vendorgatewayConnClientInterceptors = append(vendorgatewayConnClientInterceptors, unaryClientInterceptorVar5)
	}
	vendorgatewayConn := epifigrpc.NewServerConn(cfg.ORDER_SERVER, cfg.VENDOR_GATEWAY_SERVER, vendorgatewayConnClientInterceptors...)
	defer epifigrpc.CloseConn(vendorgatewayConn)
	uNNameCheckClient := namecheck.NewUNNameCheckClient(vendorgatewayConn)
	merchantServiceClient := merchant.NewMerchantServiceClient(actorConn)
	orderConn := epifigrpc.NewServerConn(cfg.ORDER_SERVER, cfg.ORDER_SERVER)
	defer epifigrpc.CloseConn(orderConn)
	upiOnboardingClient := upionboardingpb.NewUpiOnboardingClient(orderConn)
	uPIClient := upipb.NewUPIClient(orderConn)
	p2PInvestmentClient := p2ppb.NewP2PInvestmentClient(wealthdmfConn)
	vendorgatewayConnVar2ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar11 := servergenwire.NewReverseLocationClientInterceptor(locationClient)
	if unaryClientInterceptorVar11 != nil {
		vendorgatewayConnVar2ClientInterceptors = append(vendorgatewayConnVar2ClientInterceptors, unaryClientInterceptorVar11)
	}
	vendorgatewayConnVar2 := epifigrpc.NewServerConn(cfg.ORDER_SERVER, cfg.VENDOR_GATEWAY_SERVER, vendorgatewayConnVar2ClientInterceptors...)
	defer epifigrpc.CloseConn(vendorgatewayConnVar2)
	paymentClient := vgpaymentpb.NewPaymentClient(vendorgatewayConnVar2)
	nebulaConn := epifigrpc.NewServerConn(cfg.ORDER_SERVER, cfg.NEBULA_SERVER)
	defer epifigrpc.CloseConn(nebulaConn)
	celestialClient := celestialpb.NewCelestialClient(nebulaConn)
	internationalFundTransferClient := internationalfundtransfer.NewInternationalFundTransferClient(orderConn)
	vendorgatewayConnVar3ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptor := servergenwire.NewReverseLocationClientInterceptor(locationClient)
	if unaryClientInterceptor != nil {
		vendorgatewayConnVar3ClientInterceptors = append(vendorgatewayConnVar3ClientInterceptors, unaryClientInterceptor)
	}
	vendorgatewayConnVar3 := epifigrpc.NewServerConn(cfg.ORDER_SERVER, cfg.VENDOR_GATEWAY_SERVER, vendorgatewayConnVar3ClientInterceptors...)
	defer epifigrpc.CloseConn(vendorgatewayConnVar3)
	merchantResolutionClient := merchantresolutionpb.NewMerchantResolutionClient(vendorgatewayConnVar3)
	bankCustomerServiceClient := bankcustpb.NewBankCustomerServiceClient(onboardingConn)
	cardConn := epifigrpc.NewServerConn(cfg.ORDER_SERVER, cfg.CARD_SERVER)
	defer epifigrpc.CloseConn(cardConn)
	cardProvisioningClient := cardprovpb.NewCardProvisioningClient(cardConn)
	txnCategorizerClient := categorizer.NewTxnCategorizerClient(wealthdmfConn)
	docsConn := epifigrpc.NewServerConn(cfg.ORDER_SERVER, cfg.DOCS_SERVER)
	defer epifigrpc.CloseConn(docsConn)
	parserClient := parserpb.NewParserClient(docsConn)
	recurringPaymentServiceClient := rppb.NewRecurringPaymentServiceClient(orderConn)
	enachServiceClient := enachpb.NewEnachServiceClient(orderConn)
	orderRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["OrderRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = orderRedisStore.Close() }()
	vendorgatewayConnVar4 := epifigrpc.NewServerConn(cfg.ORDER_SERVER, cfg.VENDOR_GATEWAY_SERVER)
	defer epifigrpc.CloseConn(vendorgatewayConnVar4)
	uPIClientVar2 := vgupipb.NewUPIClient(vendorgatewayConnVar4)
	payClient := pay.NewPayClient(orderConn)
	vendorgatewayConnVar6ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar12 := servergenwire.NewReverseLocationClientInterceptor(locationClient)
	if unaryClientInterceptorVar12 != nil {
		vendorgatewayConnVar6ClientInterceptors = append(vendorgatewayConnVar6ClientInterceptors, unaryClientInterceptorVar12)
	}
	vendorgatewayConnVar6 := epifigrpc.NewServerConn(cfg.ORDER_SERVER, cfg.VENDOR_GATEWAY_SERVER, vendorgatewayConnVar6ClientInterceptors...)
	defer epifigrpc.CloseConn(vendorgatewayConnVar6)
	uPIClientVar3 := vgupipb.NewUPIClient(vendorgatewayConnVar6)
	delayQueue1RedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["DelayQueue1RedisStore"], gconf.Tracing().Enable)
	defer func() { _ = delayQueue1RedisStore.Close() }()
	consumerClient := paymentpb.NewConsumerClient(orderConn)
	healthEngineServiceClient := healthengine.NewHealthEngineServiceClient(docsConn)
	savingsLedgerRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["SavingsLedgerRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = savingsLedgerRedisStore.Close() }()
	orderServiceClient := orderpb.NewOrderServiceClient(orderConn)
	paymentClientVar3 := paymentpb.NewPaymentClient(orderConn)
	beneficiaryManagementClient := beneficiarymanagementpb.NewBeneficiaryManagementClient(orderConn)
	operationalStatusServiceClient := operationalstatuspb.NewOperationalStatusServiceClient(actorConn)
	accountAggregatorClient := aapb.NewAccountAggregatorClient(orderConn)
	vendorgatewayConnVar8ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar8 := servergenwire.NewReverseLocationClientInterceptor(locationClient)
	if unaryClientInterceptorVar8 != nil {
		vendorgatewayConnVar8ClientInterceptors = append(vendorgatewayConnVar8ClientInterceptors, unaryClientInterceptorVar8)
	}
	vendorgatewayConnVar8 := epifigrpc.NewServerConn(cfg.ORDER_SERVER, cfg.VENDOR_GATEWAY_SERVER, vendorgatewayConnVar8ClientInterceptors...)
	defer epifigrpc.CloseConn(vendorgatewayConnVar8)
	paymentClientVar4 := vgb2cpaymentpb.NewPaymentClient(vendorgatewayConnVar8)
	b2CPaymentLockRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["B2CPaymentLockRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = b2CPaymentLockRedisStore.Close() }()
	payIncidentManagerClient := payincidentmanagerconsumer.NewPayIncidentManagerClient(orderConn)
	onboardingClient := onboarding.NewOnboardingClient(authConn)
	vendorgatewayConnVar12ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar10 := servergenwire.NewReverseLocationClientInterceptor(locationClient)
	if unaryClientInterceptorVar10 != nil {
		vendorgatewayConnVar12ClientInterceptors = append(vendorgatewayConnVar12ClientInterceptors, unaryClientInterceptorVar10)
	}
	vendorgatewayConnVar12 := epifigrpc.NewServerConn(cfg.ORDER_SERVER, cfg.VENDOR_GATEWAY_SERVER, vendorgatewayConnVar12ClientInterceptors...)
	defer epifigrpc.CloseConn(vendorgatewayConnVar12)
	parserClientVar2 := vgparserpb.NewParserClient(vendorgatewayConnVar12)
	balanceClient := accountbalancepb.NewBalanceClient(actorConn)
	tieringClient := tieringpb.NewTieringClient(centralgrowthConn)
	vendorgatewayConnVar23ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar7 := servergenwire.NewReverseLocationClientInterceptor(locationClient)
	if unaryClientInterceptorVar7 != nil {
		vendorgatewayConnVar23ClientInterceptors = append(vendorgatewayConnVar23ClientInterceptors, unaryClientInterceptorVar7)
	}
	vendorgatewayConnVar23 := epifigrpc.NewServerConn(cfg.ORDER_SERVER, cfg.VENDOR_GATEWAY_SERVER, vendorgatewayConnVar23ClientInterceptors...)
	defer epifigrpc.CloseConn(vendorgatewayConnVar23)
	accountsClient := vgaccountspb.NewAccountsClient(vendorgatewayConnVar23)
	accountStatementClient := statement.NewAccountStatementClient(actorConn)
	payRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["PayRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = payRedisStore.Close() }()
	decisionEngineClient := paymentpb.NewDecisionEngineClient(orderConn)
	txnAggregatesClient := txnaggregatespb.NewTxnAggregatesClient(wealthdmfConn)
	paymentGatewayClient := vgpgpb.NewPaymentGatewayClient(vendorgatewayConnVar4)
	fireflyClient := ffpb.NewFireflyClient(cardConn)
	rewardsAggregatesClient := rewardspinotpb.NewRewardsAggregatesClient(growthinfraConn)
	projectorServiceClient := projectorpb.NewProjectorServiceClient(growthinfraConn)
	accountingClient := accounting.NewAccountingClient(cardConn)
	fireflyV2Client := fireflyv2pb.NewFireflyV2Client(cardConn)
	fileGeneratorClient := filegenpb.NewFileGeneratorClient(orderConn)
	internationalFundTransferClientVar5 := vgiftpb.NewInternationalFundTransferClient(vendorgatewayConnVar4)
	orderManagerClient := usstocksorderpb.NewOrderManagerClient(wealthdmfConn)
	locationClientVar13 := location.NewLocationClient(vendorgatewayConnVar4)
	forexServiceClient := forex2.NewForexServiceClient(orderConn)
	docsClient := docspb.NewDocsClient(docsConn)
	accountManagerClient := usstocksaccountpb.NewAccountManagerClient(wealthdmfConn)
	watsonClient := watsonpb.NewWatsonClient(cxConn)
	orchestratorClient := authorchestratorpb.NewOrchestratorClient(authConn)
	useCaseDbResourceProvider, useCaseDbResourceProviderTxnExec, useCaseDbResourceProviderTeardown, err := usecase.NewDBResourceProvider(gconf.UseCaseDBConfigMap(), gconf.Tracing().Enable, gconf.PgdbConns())
	if err != nil {
		logger.Error(ctx, "failed to initialize usecase db resource provider", zap.Error(err))
		return err
	}
	defer func() {
		useCaseDbResourceProviderTeardown()
	}()
	_ = useCaseDbResourceProvider
	_ = useCaseDbResourceProviderTxnExec
	tspUserServiceClient := tspuserpb.NewTspUserServiceClient(onboardingConn)
	standingInstructionServiceClient := sipb.NewStandingInstructionServiceClient(orderConn)
	mandateServiceClient := upimandatepb.NewMandateServiceClient(orderConn)
	growthinfraConnVar9ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar3 := servergenwire2.NewOrderRequestClientInterceptor()
	if unaryClientInterceptorVar3 != nil {
		growthinfraConnVar9ClientInterceptors = append(growthinfraConnVar9ClientInterceptors, unaryClientInterceptorVar3)
	}
	growthinfraConnVar9 := epifigrpc.NewServerConn(cfg.ORDER_SERVER, cfg.GROWTH_INFRA_SERVER, growthinfraConnVar9ClientInterceptors...)
	defer epifigrpc.CloseConn(growthinfraConnVar9)
	commsClientVar5 := comms.NewCommsClient(growthinfraConnVar9)
	upiRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["UpiRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = upiRedisStore.Close() }()
	vendorgatewayConnVar33ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar9 := servergenwire.NewReverseLocationClientInterceptor(locationClient)
	if unaryClientInterceptorVar9 != nil {
		vendorgatewayConnVar33ClientInterceptors = append(vendorgatewayConnVar33ClientInterceptors, unaryClientInterceptorVar9)
	}
	vendorgatewayConnVar33 := epifigrpc.NewServerConn(cfg.ORDER_SERVER, cfg.VENDOR_GATEWAY_SERVER, vendorgatewayConnVar33ClientInterceptors...)
	defer epifigrpc.CloseConn(vendorgatewayConnVar33)
	enachClient := vgenachpb.NewEnachClient(vendorgatewayConnVar33)
	upcomingTransactionsClient := upcomingtxnspb.NewUpcomingTransactionsClient(wealthdmfConn)
	catalogManagerClient := catalogmanagerpb.NewCatalogManagerClient(wealthdmfConn)
	paymentGatewayServiceClient := paymentgateway2.NewPaymentGatewayServiceClient(orderConn)
	catalogManagerClientVar2 := usstockscatalogpb.NewCatalogManagerClient(wealthdmfConn)
	vendorgatewayConnVar34ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar4 := servergenwire.NewReverseLocationClientInterceptor(locationClient)
	if unaryClientInterceptorVar4 != nil {
		vendorgatewayConnVar34ClientInterceptors = append(vendorgatewayConnVar34ClientInterceptors, unaryClientInterceptorVar4)
	}
	vendorgatewayConnVar34 := epifigrpc.NewServerConn(cfg.ORDER_SERVER, cfg.VENDOR_GATEWAY_SERVER, vendorgatewayConnVar34ClientInterceptors...)
	defer epifigrpc.CloseConn(vendorgatewayConnVar34)
	standingInstructionClient := sivg.NewStandingInstructionClient(vendorgatewayConnVar34)
	debitCardMandateServiceClient := dcmandatepb.NewDebitCardMandateServiceClient(cardConn)
	growthinfraConnVar10ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar6 := servergenwire2.NewOrderRequestClientInterceptor()
	if unaryClientInterceptorVar6 != nil {
		growthinfraConnVar10ClientInterceptors = append(growthinfraConnVar10ClientInterceptors, unaryClientInterceptorVar6)
	}
	growthinfraConnVar10 := epifigrpc.NewServerConn(cfg.ORDER_SERVER, cfg.GROWTH_INFRA_SERVER, growthinfraConnVar10ClientInterceptors...)
	defer epifigrpc.CloseConn(growthinfraConnVar10)
	commsClientVar6 := comms.NewCommsClient(growthinfraConnVar10)
	nudgeServiceClient := nudge.NewNudgeServiceClient(growthinfraConn)
	uPIOnboardingRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["UPIOnboardingRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = uPIOnboardingRedisStore.Close() }()
	consentClient := consentpb.NewConsentClient(authConn)
	commsClientVar9 := comms.NewCommsClient(growthinfraConn)
	billPayServiceClient := billpayvgpb.NewBillPayServiceClient(vendorgatewayConnVar4)
	mobileRechargeServiceClient := rechargepb.NewMobileRechargeServiceClient(vendorgatewayConnVar4)
	billersRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["BillersRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = billersRedisStore.Close() }()

	awsConf, awsErr := awsconfpkg.NewAWSConfig(ctx, gconf.AWS().Region, gconf.Tracing().Enable)
	if awsErr != nil {
		logger.Error(ctx, "error in loading aws v2 config", zap.Error(awsErr))
		if !cfg.IsRemoteDebugEnabled() {
			return err
		}
	}

	sqsClient := sqs.InitSQSClient(awsConf)
	_ = sqsClient

	var broker *events.RudderStackBroker

	// load rudder-stack broker for sending events
	rudderClient, err := analytics.NewWithConfig(
		gconf.Secrets().Ids[config.RudderWriteKey], "http://"+cfg.GetServerEndPoint(cfg.RUDDER_SERVER).URL(),
		analytics.Config{
			Callback:  events.NewCallback(),
			Interval:  gconf.RudderStack().IntervalInSec * time.Second, // nolint: durationcheck
			BatchSize: gconf.RudderStack().BatchSize,
			Verbose:   gconf.RudderStack().Verbose,
		})
	if err != nil {
		logger.Error(ctx, "error in connecting rudder", zap.Error(err))
		return err
	}
	broker = events.NewRudderStackBroker(rudderClient)
	defer broker.Close()

	rateLimiter := ratelimiter.NewRateLimiterV2(store.NewSlidingWindowLogWithRedis(rateLimiterRedisStore), gconf.GrpcRatelimiterParams().RateLimitConfig())
	_ = rateLimiter

	var unaryInterceptors []grpc.UnaryServerInterceptor

	unaryServerInterceptor := servergenwire2.AddQuestUserContextUnaryInterceptor(gconf)
	if unaryServerInterceptor != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptor)
	}

	unaryServerInterceptorVar2 := servergenwire2.OrderRateLimiterInterceptor(gconf)
	if unaryServerInterceptorVar2 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar2)
	}

	unaryServerInterceptorVar3 := servergenwire2.PayRateLimiterInterceptor(gconf)
	if unaryServerInterceptorVar3 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar3)
	}

	unaryServerInterceptorVar4, err := servergenwire3.CollapserInterceptor(collapserRueidisRedisStore, collapserRueidisCacheStorage)
	if err != nil {
		logger.Error(ctx, "failed to init grpc unary interceptor", zap.Error(err))
		return err
	}
	if unaryServerInterceptorVar4 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar4)
	}

	var streamInterceptors []grpc.StreamServerInterceptor

	histogramBuckets := prometheus.DefBuckets
	if len(gconf.LatencyHistogramBuckets()) > 0 {
		histogramBuckets = epifigrpc.AddGrpcLatencyBuckets(gconf.LatencyHistogramBuckets()...)
	}

	serverOptions := []grpc.ServerOption{}
	if gconf.GrpcServerConfig().MaxRecvMsgSize() != nil && gconf.GrpcServerConfig().MaxRecvMsgSize().Size != 0 {
		serverOptions = append(serverOptions, grpc.MaxRecvMsgSize(gconf.GrpcServerConfig().MaxRecvMsgSize().Size))
	}
	var extractFeRespStatusWithErrMsgFunc commoninterceptors.ExtractFeRespStatusWithErrMsgFunc
	extractFeRespStatusWithErrMsgFunc = fepkg.ExtractFeRespStatusWithErrMsgFunc

	s := epifigrpc.NewSecureServerWithInterceptors(gconf.GrpcServerConfig(), gconf.Flags().TrimDebugMessageFromStatus, string(gconf.Name()), histogramBuckets, unaryInterceptors, streamInterceptors, extractFeRespStatusWithErrMsgFunc, serverOptions...)

	if cfg.IsNonProdEnv(gconf.Environment()) {
		reflection.Register(s)
	}

	httpMux := http.NewServeMux()
	_ = httpMux

	var sqsSubscribers []queue.Subscriber
	var subs []queue.Subscriber
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSubs []*sqs.ExtendedSqsSubscriber

	subs, extSubs, err = setupOrder(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, epifiCRDB, epifiWealthCRDB, piClient, authClient, timelineServiceClient, depositClient, disputeClient, accountPIRelationClient, connectedAccountClient, kycClient, savingsClient, commsClient, uNNameCheckClient, merchantServiceClient, upiOnboardingClient, uPIClient, p2PInvestmentClient, paymentClient, celestialClient, internationalFundTransferClient, merchantResolutionClient, bankCustomerServiceClient, cardProvisioningClient, txnCategorizerClient, parserClient, recurringPaymentServiceClient, enachServiceClient, orderRedisStore, uPIClientVar2, payClient, uPIClientVar3, delayQueue1RedisStore, consumerClient, healthEngineServiceClient, locationClient, savingsLedgerRedisStore, orderServiceClient, paymentClientVar3, beneficiaryManagementClient, operationalStatusServiceClient, accountAggregatorClient, paymentClientVar4, b2CPaymentLockRedisStore, payIncidentManagerClient, onboardingClient, parserClientVar2, balanceClient, tieringClient, accountsClient, accountStatementClient)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupPay(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, epifiCRDB, payPGDB, internationalFundTransferClient, payRedisStore, celestialClient, orderServiceClient, actorClient, savingsClient, decisionEngineClient, usersClient, authClient, piClient, paymentClient, paymentClientVar3, accountPIRelationClient, upiOnboardingClient, txnAggregatesClient, timelineServiceClient, groupClient, bankCustomerServiceClient, onboardingClient, uPIClient, balanceClient, paymentGatewayClient, fireflyClient, uNNameCheckClient, recurringPaymentServiceClient, cardProvisioningClient, tieringClient, rewardsAggregatesClient, projectorServiceClient, accountingClient, fireflyV2Client, fileGeneratorClient, internationalFundTransferClientVar5, connectedAccountClient, orderManagerClient, payClient, accountAggregatorClient, locationClientVar13, forexServiceClient, docsClient, accountManagerClient, watsonClient, merchantServiceClient, payIncidentManagerClient, orchestratorClient, enachServiceClient)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupRecurringpayment(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, paymentGatewayClient, payClient, orderServiceClient, epifiCRDB, usersClient, celestialClient, recurringPaymentServiceClient, piClient, useCaseDbResourceProvider, tspUserServiceClient, groupClient, actorClient, enachServiceClient, standingInstructionServiceClient, savingsClient, authClient, paymentClientVar3, mandateServiceClient, accountPIRelationClient, timelineServiceClient, uPIClient, commsClientVar5, upiRedisStore, balanceClient, enachClient, operationalStatusServiceClient, upcomingTransactionsClient, merchantServiceClient, catalogManagerClient, depositClient, paymentGatewayServiceClient, catalogManagerClientVar2, standingInstructionClient, paymentClient, bankCustomerServiceClient, recurringPaymentPGDB, cardProvisioningClient, debitCardMandateServiceClient)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupUpi(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, uPIClientVar3, piClient, accountPIRelationClient, epifiCRDB, orderServiceClient, paymentClientVar3, actorClient, timelineServiceClient, savingsClient, authClient, commsClientVar6, uPIClient, usersClient, groupClient, onboardingClient, upiRedisStore, upiOnboardingClient, connectedAccountClient, locationClient, recurringPaymentServiceClient, nudgeServiceClient, uPIOnboardingRedisStore, celestialClient, consentClient, commsClientVar9)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupBillpay(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, billpayPGDB, billPayServiceClient, mobileRechargeServiceClient, usersClient, celestialClient, billersRedisStore)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	explorerpb.RegisterConfigExplorerServer(s, explorer.NewServer(configNameToConfMap))

	cleanupFn, err := hook.InitOrderServer(epifiCRDB) // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "InitOrderServer"), zap.Error(err))
		return err
	}
	defer cleanupFn()

	cleanupFnVar2, err := hook.InitOrderServiceGroupWorkflowConfigMap() // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "InitOrderServiceGroupWorkflowConfigMap"), zap.Error(err))
		return err
	}
	defer cleanupFnVar2()

	cleanupFnVar3, err := hook.InitialisePgProgramToAuthParamsMap() // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "InitialisePgProgramToAuthParamsMap"), zap.Error(err))
		return err
	}
	defer cleanupFnVar3()

	cleanupFnVar4, err := servergenwire3.LoadCollapserWithGrpcServer(gconf, s) // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "LoadCollapserWithGrpcServer"), zap.Error(err))
		return err
	}
	defer cleanupFnVar4()

	// Load rpc level log sampling rate descriptors.
	epifigrpc.GetRPCLogSamplingRatesMapInstance().Load(s)

	initNotifier <- gconf.Name()

	// TODO(Sundeep): Remove this additional logic for starting the servers inside go-routine, by exposing functions
	// from epifiserver package that take errgroup.Group as parameter.
	grp, _ := errgroup.WithContext(context.Background())

	grp.Go(func() error {
		epifiserver.StartSecureServer(s, gconf.ServerPorts(), string(gconf.Name()))
		return nil
	})

	// Wait for server health before starting SQS workers
	if queue.IsWorkerInitializationEnabled() {
		goroutine.RunWithCtx(ctx, func(ctx context.Context) {
			if epifiserver.IsServerReady(gconf.ServerPorts().HttpPort) {
				logger.Info(ctx, "Server is healthy, starting SQS workers")
				sqs.StartSQSWorkers(ctx, sqsSubscribers, extendedSqsSubscribers)
			} else {
				logger.PanicWithCtx(ctx, "Server health check timed out, not starting SQS workers")
			}
		})
	}
	if err := grp.Wait(); err != nil {
		return err
	}

	return nil
}

// nolint: funlen
func setupOrder(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	managerClient managerpb.ManagerClient,
	groupClient usergrouppb.GroupClient,
	usersClient user.UsersClient,
	actorClient actor.ActorClient,
	segmentationServiceClient segmentpb.SegmentationServiceClient,
	epifiCRDB types.EpifiCRDB,
	epifiWealthCRDB types.EpifiWealthCRDB,
	piClient pipb.PiClient,
	authClient authpb.AuthClient,
	timelineServiceClient timelinepb.TimelineServiceClient,
	depositClient depositpb.DepositClient,
	disputeClient disputepb.DisputeClient,
	accountPIRelationClient accountpipb.AccountPIRelationClient,
	connectedAccountClient connectedaccountpb.ConnectedAccountClient,
	kycClient kycpb.KycClient,
	savingsClient savingspb.SavingsClient,
	commsClient types2.OrderCommsClientWithInterceptors,
	uNNameCheckClient vgtypes.UNNameCheckClientWithInterceptors,
	merchantServiceClient merchant.MerchantServiceClient,
	upiOnboardingClient upionboardingpb.UpiOnboardingClient,
	uPIClient upipb.UPIClient,
	p2PInvestmentClient p2ppb.P2PInvestmentClient,
	paymentClient vgtypes.VgPaymentClientWithInterceptors,
	celestialClient celestialpb.CelestialClient,
	internationalFundTransferClient internationalfundtransfer.InternationalFundTransferClient,
	merchantResolutionClient vgtypes.MerchantResolutionClientWithInterceptors,
	bankCustomerServiceClient bankcustpb.BankCustomerServiceClient,
	cardProvisioningClient cardprovpb.CardProvisioningClient,
	txnCategorizerClient categorizer.TxnCategorizerClient,
	parserClient parserpb.ParserClient,
	recurringPaymentServiceClient rppb.RecurringPaymentServiceClient,
	enachServiceClient enachpb.EnachServiceClient,
	orderRedisStore types.OrderRedisStore,
	uPIClientVar2 vgupipb.UPIClient,
	payClient pay.PayClient,
	uPIClientVar3 vgtypes.VgUpiClientWithInterceptors,
	delayQueue1RedisStore types2.DelayQueue1RedisStore,
	consumerClient paymentpb.ConsumerClient,
	healthEngineServiceClient healthengine.HealthEngineServiceClient,
	locationClient locationpb.LocationClient,
	savingsLedgerRedisStore types2.SavingsLedgerRedisStore,
	orderServiceClient orderpb.OrderServiceClient,
	paymentClientVar3 paymentpb.PaymentClient,
	beneficiaryManagementClient beneficiarymanagementpb.BeneficiaryManagementClient,
	operationalStatusServiceClient operationalstatuspb.OperationalStatusServiceClient,
	accountAggregatorClient aapb.AccountAggregatorClient,
	paymentClientVar4 vgtypes.VgB2CPaymentClientWithInterceptors,
	b2CPaymentLockRedisStore types2.B2CPaymentLockRedisStore,
	payIncidentManagerClient payincidentmanagerconsumer.PayIncidentManagerClient,
	onboardingClient onboarding.OnboardingClient,
	parserClientVar2 vgtypes.VgParserClientWithInterceptors,
	balanceClient accountbalancepb.BalanceClient,
	tieringClient tieringpb.TieringClient,
	accountsClient vgtypes.VgAccountsClientWithInterceptors,
	accountStatementClient statement.AccountStatementClient) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	orderConf, err := orderconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.ORDER_SERVICE))
		return nil, nil, err
	}
	_ = orderConf

	orderGenConf, err := dynconf.LoadConfigWithQuestConfig(orderconf.Load, config2.NewConfigWithQuest, cfg.ORDER_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.ORDER_SERVICE))
		return nil, nil, err
	}

	if !conf.QuestSdk.Disable {
		orderGenConfAppConfig := questsdkinit.QuestSdkAppConfig{AppConfig: orderGenConf, SdkConfig: orderGenConf.QuestSdk()}
		questsdkinit.SetupQuestSDK([]questsdkinit.QuestSdkAppConfig{orderGenConfAppConfig}, string(cfg.ORDER_SERVER), managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, broker)
	}

	_ = orderGenConf

	workflowProcessingPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.WorkflowProcessingPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	workflowProcessingDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.WorkflowProcessingPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	eventsCompletedTnCPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.EventsCompletedTnCPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	txnNotificationPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.TxnNotificationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	orderOrchestrationPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.OrderOrchestrationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	orderNotificationPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.OrderNotificationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	orderSearchPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.OrderSearchPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	orderVpaVerificationPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.OrderVpaVerificationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	intraBankEnquiryPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.IntraBankEnquiryPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	iMPSEnquiryPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.IMPSEnquiryPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	nEFTEnquiryPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.NEFTEnquiryPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	rTGSEnquiryPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.RTGSEnquiryPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	uPIEnquiryPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.UPIEnquiryPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	paymentOrchestrationPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.PaymentOrchestrationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	inPaymentOrderUpdatePublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.InPaymentOrderUpdatePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	savingsLedgerReconPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.SavingsLedgerReconPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	deemedTransactionUPIEnquiryPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.DeemedTransactionUPIEnquiryPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	aaAccountPiPurgePublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.AaAccountPiPurgePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	actorPiRelationPurgePublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.ActorPiRelationPurgePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	aaDataPurgeOrchestrationPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.AaDataPurgeOrchestrationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	orderCollectNotificationPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.OrderCollectNotificationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	orderNotificationFallbackPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.OrderNotificationFallbackPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}

	orderUpdateEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, orderGenConf.OrderUpdateEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}
	orderMerchantMergeEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, orderGenConf.OrderMerchantMergeEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}
	txnDetailedStatusUpdateSnsPublisher, err := sns.NewSnsPublisherWithConfig(ctx, orderGenConf.TxnDetailedStatusUpdateSnsPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}
	aATxnPublisher, err := sns.NewSnsPublisherWithConfig(ctx, orderGenConf.AATxnPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}

	service := wire.InitializeService(orderGenConf, gconf.VendorApiConf(), epifiCRDB, epifiWealthCRDB, piClient, actorClient, authClient, timelineServiceClient, depositClient, disputeClient, accountPIRelationClient, connectedAccountClient, workflowProcessingPublisher, workflowProcessingDelayPublisher, orderUpdateEventPublisher, orderMerchantMergeEventPublisher, broker, kycClient, savingsClient, usersClient, groupClient, commsClient, uNNameCheckClient, merchantServiceClient, upiOnboardingClient, uPIClient, eventsCompletedTnCPublisher, txnNotificationPublisher, orderOrchestrationPublisher, orderNotificationPublisher, orderSearchPublisher, p2PInvestmentClient, orderVpaVerificationPublisher, paymentClient, celestialClient, internationalFundTransferClient, merchantResolutionClient, bankCustomerServiceClient, cardProvisioningClient, txnCategorizerClient, parserClient, recurringPaymentServiceClient, enachServiceClient, txnDetailedStatusUpdateSnsPublisher, orderRedisStore, uPIClientVar2, crdbResourceMap, crdbTxnResourceMap, payClient)

	orderpb.RegisterOrderServiceServer(s, service)

	serviceVar2 := wire.InitializePaymentService(epifiCRDB, paymentClient, piClient, savingsClient, authClient, actorClient, usersClient, uPIClientVar3, intraBankEnquiryPublisher, iMPSEnquiryPublisher, nEFTEnquiryPublisher, rTGSEnquiryPublisher, uPIEnquiryPublisher, orderOrchestrationPublisher, paymentOrchestrationPublisher, broker, orderGenConf, commsClient, delayQueue1RedisStore, consumerClient, accountPIRelationClient, uPIClient, groupClient, inPaymentOrderUpdatePublisher, merchantServiceClient, merchantResolutionClient, bankCustomerServiceClient, healthEngineServiceClient, upiOnboardingClient, celestialClient, txnDetailedStatusUpdateSnsPublisher, orderRedisStore, locationClient, crdbResourceMap, crdbTxnResourceMap, timelineServiceClient, payClient)

	paymentpb.RegisterPaymentServer(s, serviceVar2)

	serviceVar3 := wire.InitializeReconService(orderGenConf, epifiCRDB, savingsLedgerReconPublisher, usersClient, healthEngineServiceClient, groupClient, actorClient, broker, savingsLedgerRedisStore, bankCustomerServiceClient, crdbResourceMap)

	reconpb.RegisterLedgerReconciliationServer(s, serviceVar3)

	serviceVar4 := wire.InitializePaymentDecisionService(actorClient, piClient, accountPIRelationClient, uPIClient, usersClient, groupClient, authClient, orderServiceClient, paymentClientVar3, orderGenConf, payClient, upiOnboardingClient, savingsClient, healthEngineServiceClient, beneficiaryManagementClient, crdbResourceMap, operationalStatusServiceClient)

	paymentpb.RegisterDecisionEngineServer(s, serviceVar4)

	serviceVar5 := wire.InitializeActorActivityService(orderServiceClient, actorClient, accountPIRelationClient, piClient, depositClient, orderGenConf, timelineServiceClient, accountAggregatorClient, usersClient, groupClient, connectedAccountClient, crdbResourceMap)

	actoractivitypb.RegisterActorActivityServer(s, serviceVar5)

	serviceVar6 := wire.InitializeCxService(epifiCRDB, orderGenConf, accountPIRelationClient, recurringPaymentServiceClient, orderRedisStore, crdbResourceMap)

	cxpb.RegisterCXServer(s, serviceVar6)

	serviceVar7 := wire.InitializeBusinessService(epifiCRDB, piClient, paymentClientVar4, savingsClient, orderGenConf, b2CPaymentLockRedisStore, txnDetailedStatusUpdateSnsPublisher, orderRedisStore, crdbResourceMap, crdbTxnResourceMap)

	paymentpb.RegisterBusinessServer(s, serviceVar7)

	orderDevService := wire.InitializeDevService(epifiCRDB, epifiWealthCRDB, accountPIRelationClient, savingsLedgerRedisStore, orderGenConf, orderRedisStore, crdbResourceMap, crdbTxnResourceMap)

	developer2.RegisterDevServer(s, orderDevService)

	serviceVar8 := wire.InitializePaymentConsumerService(epifiCRDB, paymentClient, authClient, orderOrchestrationPublisher, uPIClientVar3, broker, orderGenConf, piClient, orderServiceClient, uPIClient, actorClient, usersClient, groupClient, deemedTransactionUPIEnquiryPublisher, healthEngineServiceClient, accountPIRelationClient, merchantServiceClient, savingsClient, merchantResolutionClient, orderUpdateEventPublisher, payIncidentManagerClient, upiOnboardingClient, txnDetailedStatusUpdateSnsPublisher, orderRedisStore, crdbResourceMap, crdbTxnResourceMap)

	paymentpb.RegisterConsumerServer(s, serviceVar8)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.UPIEnquirySubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		paymentpb.RegisterProcessPaymentMethodToSubscriber(subscriber, serviceVar8)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.IMPSEnquirySubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		paymentpb.RegisterProcessPaymentMethodToSubscriber(subscriber, serviceVar8)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.PaymentOrchestrationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		paymentpb.RegisterProcessPaymentMethodToSubscriber(subscriber, serviceVar8)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.IntraBankEnquirySubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		paymentpb.RegisterProcessPaymentMethodToSubscriber(subscriber, serviceVar8)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.NEFTEnquirySubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		paymentpb.RegisterProcessPaymentMethodToSubscriber(subscriber, serviceVar8)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.RTGSEnquirySubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		paymentpb.RegisterProcessPaymentMethodToSubscriber(subscriber, serviceVar8)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.DeemedTransactionUpiEnquirySubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		paymentpb.RegisterProcessDeemedPaymentsMethodToSubscriber(subscriber, serviceVar8)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	serviceVar9 := wire.InitializeCampaignService(orderGenConf, epifiCRDB, onboardingClient, commsClient, orderRedisStore, crdbResourceMap)

	campaignpb.RegisterCampaignServer(s, serviceVar9)

	serviceVar10 := wire.InitializeAccountAggregatorService(orderGenConf, epifiWealthCRDB, connectedAccountClient, accountPIRelationClient)

	aapb.RegisterAccountAggregatorServer(s, serviceVar10)

	serviceVar11 := wire.InitializeAAConsumerService(orderGenConf, epifiCRDB, epifiWealthCRDB, aATxnPublisher, authClient, uPIClient, piClient, merchantServiceClient, accountPIRelationClient, actorClient, connectedAccountClient, timelineServiceClient, parserClientVar2, aaAccountPiPurgePublisher, actorPiRelationPurgePublisher, aaDataPurgeOrchestrationPublisher, delayQueue1RedisStore, paymentClient, usersClient, groupClient, savingsClient, merchantResolutionClient, upiOnboardingClient, locationClient, uPIClientVar2, gconf.VendorApiConf())

	aapb.RegisterConsumerServer(s, serviceVar11)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.AATxnSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		aapb.RegisterProcessAATxnMethodToSubscriber(subscriber, serviceVar11)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.PurgeAaDataSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		aapb.RegisterProcessPurgeAaDataEventMethodToSubscriber(subscriber, serviceVar11)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.AaTxnPurgeSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		aapb.RegisterPurgeAaTransactionMethodToSubscriber(subscriber, serviceVar11)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.AaDataPurgeOrchestrationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		aapb.RegisterOrchestrateDataPurgingMethodToSubscriber(subscriber, serviceVar11)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.AAFirstDataPullTxnSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		aapb.RegisterProcessAATxnMethodToSubscriber(subscriber, serviceVar11)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	serviceVar12 := wire.InitializeNotificationConsumer(orderGenConf, epifiCRDB, commsClient, actorClient, piClient, timelineServiceClient, depositClient, savingsClient, accountPIRelationClient, usersClient, groupClient, orderCollectNotificationPublisher, orderNotificationFallbackPublisher, recurringPaymentServiceClient, balanceClient, uPIClient, authClient, upiOnboardingClient, orderRedisStore, cardProvisioningClient, tieringClient, locationClient, crdbResourceMap, crdbTxnResourceMap, uPIClientVar2)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.OrderUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		orderpb.RegisterProcessOrderNotificationMethodToSubscriber(subscriber, serviceVar12)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.OrderCollectNotificationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		orderpb.RegisterProcessCollectOrderNotificationMethodToSubscriber(subscriber, serviceVar12)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.OrderNotificationFallbackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		orderpb.RegisterProcessOrderNotificationFallbackMethodToSubscriber(subscriber, serviceVar12)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	serviceVar13, err := wire.InitializeConsumerService(ctx, epifiCRDB, paymentClientVar3, actorClient, timelineServiceClient, savingsClient, accountPIRelationClient, piClient, depositClient, eventsCompletedTnCPublisher, uPIClient, authClient, broker, workflowProcessingPublisher, workflowProcessingDelayPublisher, orderOrchestrationPublisher, orderUpdateEventPublisher, orderNotificationPublisher, orderCollectNotificationPublisher, orderNotificationFallbackPublisher, orderSearchPublisher, merchantServiceClient, p2PInvestmentClient, orderGenConf, paymentClient, usersClient, groupClient, orderVpaVerificationPublisher, txnNotificationPublisher, awsConf, celestialClient, internationalFundTransferClient, merchantResolutionClient, balanceClient, parserClient, recurringPaymentServiceClient, enachServiceClient, upiOnboardingClient, txnDetailedStatusUpdateSnsPublisher, orderRedisStore, payClient, crdbResourceMap, crdbTxnResourceMap, uPIClientVar2, gconf.VendorApiConf())
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.OrderOrchestrationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		orderpb.RegisterProcessOrderMethodToSubscriber(subscriber, serviceVar13)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.InboundTxnSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		orderpb.RegisterProcessInboundTxnOrderMethodToSubscriber(subscriber, serviceVar13)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.OrderWorkflowProcessingSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		orderpb.RegisterProcessWorkflowMethodToSubscriber(subscriber, serviceVar13)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.DeclineCardTransactionsProcessingSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		orderpb.RegisterProcessDeclineCardTransactionsDataDumpMethodToSubscriber(subscriber, serviceVar13)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.InboundUpiTxnSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		orderpb.RegisterProcessUpiInboundTxnOrderMethodToSubscriber(subscriber, serviceVar13)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.TxnNotificationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		orderpb.RegisterProcessTxnNotificationMethodToSubscriber(subscriber, serviceVar13)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	serviceVar14 := wire.InitializeDisputedTxnConsumerService(epifiCRDB, crdbResourceMap)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.DisputeEventProcessingSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		disputes.RegisterProcessDisputeMethodToSubscriber(subscriber, serviceVar14)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	callbackService := wire.InitializeCallbackConsumer(epifiCRDB, orderOrchestrationPublisher, piClient, broker, orderGenConf, celestialClient, txnDetailedStatusUpdateSnsPublisher, healthEngineServiceClient, accountPIRelationClient, merchantServiceClient, paymentClient, groupClient, usersClient, actorClient, merchantResolutionClient, savingsClient, uPIClient, authClient, upiOnboardingClient, orderRedisStore, crdbResourceMap, crdbTxnResourceMap, uPIClientVar2)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.PaymentCallbackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		paymentpb.RegisterProcessCallBackMethodToSubscriber(subscriber, callbackService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	serviceVar15 := wire.InitializeReconConsumerService(epifiCRDB, accountsClient, actorClient, authClient, timelineServiceClient, accountPIRelationClient, piClient, depositClient, uPIClient, broker, orderOrchestrationPublisher, orderUpdateEventPublisher, orderNotificationPublisher, orderSearchPublisher, eventsCompletedTnCPublisher, merchantServiceClient, p2PInvestmentClient, orderGenConf, savingsLedgerRedisStore, paymentClient, usersClient, groupClient, orderVpaVerificationPublisher, txnNotificationPublisher, celestialClient, internationalFundTransferClient, merchantResolutionClient, accountStatementClient, savingsClient, parserClient, recurringPaymentServiceClient, enachServiceClient, upiOnboardingClient, txnDetailedStatusUpdateSnsPublisher, orderRedisStore, crdbResourceMap, crdbTxnResourceMap, payClient, uPIClientVar2, gconf.VendorApiConf())

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.SavingsLedgerReconSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		reconpb.RegisterReconcileAccountMethodToSubscriber(subscriber, serviceVar15)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}
	configNameToConfMap[cfg.ConfigName(cfg.ORDER_SERVICE)] = &commonexplorer.Config{StaticConf: &orderconf.Config{}, QuestIntegratedConfig: orderGenConf}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupPay(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	epifiCRDB types.EpifiCRDB,
	payPGDB types.PayPGDB,
	internationalFundTransferClient internationalfundtransfer.InternationalFundTransferClient,
	payRedisStore types3.PayRedisStore,
	celestialClient celestialpb.CelestialClient,
	orderServiceClient orderpb.OrderServiceClient,
	actorClient actor.ActorClient,
	savingsClient savingspb.SavingsClient,
	decisionEngineClient paymentpb.DecisionEngineClient,
	usersClient user.UsersClient,
	authClient authpb.AuthClient,
	piClient pipb.PiClient,
	paymentClient vgtypes.VgPaymentClientWithInterceptors,
	paymentClientVar3 paymentpb.PaymentClient,
	accountPIRelationClient accountpipb.AccountPIRelationClient,
	upiOnboardingClient upionboardingpb.UpiOnboardingClient,
	txnAggregatesClient txnaggregatespb.TxnAggregatesClient,
	timelineServiceClient timelinepb.TimelineServiceClient,
	groupClient usergrouppb.GroupClient,
	bankCustomerServiceClient bankcustpb.BankCustomerServiceClient,
	onboardingClient onboarding.OnboardingClient,
	uPIClient upipb.UPIClient,
	balanceClient accountbalancepb.BalanceClient,
	paymentGatewayClient vgpgpb.PaymentGatewayClient,
	fireflyClient ffpb.FireflyClient,
	uNNameCheckClient vgtypes.UNNameCheckClientWithInterceptors,
	recurringPaymentServiceClient rppb.RecurringPaymentServiceClient,
	cardProvisioningClient cardprovpb.CardProvisioningClient,
	tieringClient tieringpb.TieringClient,
	rewardsAggregatesClient rewardspinotpb.RewardsAggregatesClient,
	projectorServiceClient projectorpb.ProjectorServiceClient,
	accountingClient accounting.AccountingClient,
	fireflyV2Client fireflyv2pb.FireflyV2Client,
	fileGeneratorClient filegenpb.FileGeneratorClient,
	internationalFundTransferClientVar5 vgiftpb.InternationalFundTransferClient,
	connectedAccountClient connectedaccountpb.ConnectedAccountClient,
	orderManagerClient usstocksorderpb.OrderManagerClient,
	payClient pay.PayClient,
	accountAggregatorClient aapb.AccountAggregatorClient,
	locationClientVar13 location.LocationClient,
	forexServiceClient forex2.ForexServiceClient,
	docsClient docspb.DocsClient,
	accountManagerClient usstocksaccountpb.AccountManagerClient,
	watsonClient watsonpb.WatsonClient,
	merchantServiceClient merchant.MerchantServiceClient,
	payIncidentManagerClient payincidentmanagerconsumer.PayIncidentManagerClient,
	orchestratorClient authorchestratorpb.OrchestratorClient,
	enachServiceClient enachpb.EnachServiceClient) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	payConf, err := payconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.PAY_SERVICE))
		return nil, nil, err
	}
	_ = payConf

	payGenConf, err := dynconf.LoadConfig(payconf.Load, payservergenconfig.NewConfig, cfg.PAY_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.PAY_SERVICE))
		return nil, nil, err
	}

	_ = payGenConf

	inPaymentOrderUpdatePublisherVar2, err := sqs.NewPublisherWithConfig(ctx, payGenConf.InPaymentOrderUpdatePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	generateSofLimitStrategiesValuesPublisher, err := sqs.NewPublisherWithConfig(ctx, payGenConf.GenerateSofLimitStrategiesValuesPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}

	orderUpdateEventPublisherVar2, err := sns.NewSnsPublisherWithConfig(ctx, payGenConf.OrderUpdateEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}
	iFTRemittanceFileProcessingEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, payGenConf.IFTRemittanceFileProcessingEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}

	txnBackfillS3Client := s3pkg.NewClient(awsConf, payGenConf.TxnBackfillBucketName())

	payClientVar6, err := temporalcl.NewWorkflowClient(getEnvNamespaceClient(namespace.Pay, env), true, gconf.Secrets().Ids[config.TemporalCodecAesKey])
	if err != nil {
		logger.Error(ctx, "failed to celestial client", zap.Error(err))
		return nil, nil, err
	}

	payClientVar7, err := temporalcl.NewWorkflowClient(getEnvNamespaceClient(namespace.Pay, env), true, gconf.Secrets().Ids[config.TemporalCodecAesKey])
	if err != nil {
		logger.Error(ctx, "failed to celestial client", zap.Error(err))
		return nil, nil, err
	}

	payClientVar12, err := temporalcl.NewWorkflowClient(getEnvNamespaceClient(namespace.Pay, env), true, gconf.Secrets().Ids[config.TemporalCodecAesKey])
	if err != nil {
		logger.Error(ctx, "failed to celestial client", zap.Error(err))
		return nil, nil, err
	}

	payDevService := wire2.InitializeDevService(epifiCRDB, payPGDB, internationalFundTransferClient, payConf, payGenConf, payRedisStore, crdbResourceMap, crdbTxnResourceMap)

	paydeveloper.RegisterDevServer(s, payDevService)

	serviceVar16 := wire2.InitialisePayService(epifiCRDB, celestialClient, orderServiceClient, actorClient, payConf, savingsClient, decisionEngineClient, usersClient, authClient, piClient, paymentClient, paymentClientVar3, broker, accountPIRelationClient, payGenConf, upiOnboardingClient, txnAggregatesClient, timelineServiceClient, groupClient, inPaymentOrderUpdatePublisherVar2, bankCustomerServiceClient, onboardingClient, txnBackfillS3Client, uPIClient, balanceClient, paymentGatewayClient, fireflyClient, uNNameCheckClient, payClientVar6, payRedisStore, crdbTxnResourceMap, recurringPaymentServiceClient, crdbResourceMap, cardProvisioningClient, orderUpdateEventPublisherVar2, tieringClient, rewardsAggregatesClient, projectorServiceClient, accountingClient, fireflyV2Client)

	pay.RegisterPayServer(s, serviceVar16)

	serviceVar17 := wire2.InitializeForexService(crdbTxnResourceMap, epifiCRDB, celestialClient, payGenConf)

	forex2.RegisterForexServiceServer(s, serviceVar17)

	serviceVar18 := wire2.InitializeInternationalFundTransferService(epifiCRDB, payGenConf, celestialClient, orderServiceClient, accountPIRelationClient, piClient, usersClient, savingsClient, actorClient, fileGeneratorClient, awsConf, timelineServiceClient, payClientVar7, internationalFundTransferClientVar5, groupClient, bankCustomerServiceClient, txnAggregatesClient, onboardingClient, connectedAccountClient, orderManagerClient, payClient, payRedisStore, balanceClient, accountAggregatorClient, locationClientVar13, generateSofLimitStrategiesValuesPublisher, authClient, crdbResourceMap, crdbTxnResourceMap, payConf, forexServiceClient)

	internationalfundtransfer.RegisterInternationalFundTransferServer(s, serviceVar18)

	serviceVar19 := wire2.InitializeInternationalFundTransferFileGeneratorService(epifiCRDB, payConf, payGenConf, awsConf, docsClient, internationalFundTransferClient, celestialClient, iFTRemittanceFileProcessingEventPublisher, savingsClient, actorClient, orderManagerClient, accountManagerClient, balanceClient, payRedisStore, crdbResourceMap, crdbTxnResourceMap)

	filegenpb.RegisterFileGeneratorServer(s, serviceVar19)

	serviceVar20 := wire2.InitializePayIncidentManagerService(payConf, watsonClient, payClient, orderServiceClient, accountPIRelationClient, actorClient, usersClient, groupClient, uPIClient, upiOnboardingClient, merchantServiceClient)

	payincidentmanagerconsumer.RegisterPayIncidentManagerServer(s, serviceVar20)

	serviceVar21 := wire2.InitializeCxService(epifiCRDB, payGenConf, payConf, payRedisStore, crdbResourceMap, crdbTxnResourceMap)

	paycxpb.RegisterCXServer(s, serviceVar21)

	serviceVar22 := wire2.InitializeVelocityEngineService(payClient, payConf)

	velocityengine2.RegisterVelocityEngineServer(s, serviceVar22)

	serviceVar23 := wire2.InitializeFileConsumerService(epifiCRDB, awsConf, internationalFundTransferClient, payGenConf, iFTRemittanceFileProcessingEventPublisher, celestialClient)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, payGenConf.IFTProcessFileSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer2.RegisterProcessFileMethodToSubscriber(subscriber, serviceVar23)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	consumer := wire2.InitializeSavingsAccountConsumer(savingsClient, balanceClient)

	savingsconsumerpb.RegisterConsumerServer(s, consumer)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, payGenConf.OrderUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		savingsconsumerpb.RegisterProcessOrderUpdateEventMethodToSubscriber(subscriber, consumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	serviceVar24 := wire2.InitializePayIncidentManagerConsumerService(payConf, payIncidentManagerClient, accountPIRelationClient, actorClient, merchantServiceClient, payGenConf, piClient, crdbResourceMap, orderServiceClient, watsonClient)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, payGenConf.PayIncidentMgrOrderUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		payincidentmanagerconsumer.RegisterProcessOrderUpdateMethodToSubscriber(subscriber, serviceVar24)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, payGenConf.TransactionDetailedStatusUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		payincidentmgrconsumerpb.RegisterProcessTransactionDetailedStatusUpdateMethodToSubscriber(subscriber, serviceVar24)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	serviceVar25 := wire2.InitializeBeneficiaryManagementService(payPGDB, orchestratorClient)

	beneficiarymanagementpb.RegisterBeneficiaryManagementServer(s, serviceVar25)

	consumerService := wire2.InitialiseIFTConsumer(epifiCRDB, connectedAccountClient, balanceClient, payClient, accountAggregatorClient, savingsClient)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, payGenConf.GenerateSofLimitStrategiesValuesSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer3.RegisterGenerateSofLimitStrategiesValuesMethodToSubscriber(subscriber, consumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	consumerVar2 := wire2.InitialisePaymentGatewayConsumer(payGenConf, epifiCRDB, payClientVar12, celestialClient, recurringPaymentServiceClient, enachServiceClient)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, payGenConf.ProcessPaymentGatewayWebhookEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		pgconsumerpb.RegisterProcessPaymentGatewayWebhookEventMethodToSubscriber(subscriber, consumerVar2)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	serviceVar26 := wire2.InitializePaymentRecommendationSystemService(actorClient, payGenConf, paymentClientVar3)

	paymentrecommendationsystem2.RegisterPaymentRecommendationSystemServer(s, serviceVar26)

	configNameToConfMap[cfg.ConfigName(cfg.PAY_SERVICE)] = &commonexplorer.Config{StaticConf: &payconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupRecurringpayment(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	paymentGatewayClient vgpgpb.PaymentGatewayClient,
	payClient pay.PayClient,
	orderServiceClient orderpb.OrderServiceClient,
	epifiCRDB types.EpifiCRDB,
	usersClient user.UsersClient,
	celestialClient celestialpb.CelestialClient,
	recurringPaymentServiceClient rppb.RecurringPaymentServiceClient,
	piClient pipb.PiClient,
	useCaseDbResourceProvider *usecase.DBResourceProvider[*gorm.DB],
	tspUserServiceClient tspuserpb.TspUserServiceClient,
	groupClient usergrouppb.GroupClient,
	actorClient actor.ActorClient,
	enachServiceClient enachpb.EnachServiceClient,
	standingInstructionServiceClient sipb.StandingInstructionServiceClient,
	savingsClient savingspb.SavingsClient,
	authClient authpb.AuthClient,
	paymentClientVar3 paymentpb.PaymentClient,
	mandateServiceClient upimandatepb.MandateServiceClient,
	accountPIRelationClient accountpipb.AccountPIRelationClient,
	timelineServiceClient timelinepb.TimelineServiceClient,
	uPIClient upipb.UPIClient,
	commsClientVar5 types5.RecPaymentCommsClientWithInterceptors,
	upiRedisStore upitypes.UpiRedisStore,
	balanceClient accountbalancepb.BalanceClient,
	enachClient vgtypes.VgEnachClientWithInterceptors,
	operationalStatusServiceClient operationalstatuspb.OperationalStatusServiceClient,
	upcomingTransactionsClient upcomingtxnspb.UpcomingTransactionsClient,
	merchantServiceClient merchant.MerchantServiceClient,
	catalogManagerClient catalogmanagerpb.CatalogManagerClient,
	depositClient depositpb.DepositClient,
	paymentGatewayServiceClient paymentgateway2.PaymentGatewayServiceClient,
	catalogManagerClientVar2 usstockscatalogpb.CatalogManagerClient,
	standingInstructionClient vgtypes.SIVGClientWithInterceptors,
	paymentClient vgtypes.VgPaymentClientWithInterceptors,
	bankCustomerServiceClient bankcustpb.BankCustomerServiceClient,
	recurringPaymentPGDB types.RecurringPaymentPGDB,
	cardProvisioningClient cardprovpb.CardProvisioningClient,
	debitCardMandateServiceClient dcmandatepb.DebitCardMandateServiceClient) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	recurringpaymentConf, err := recurringpaymentconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.RECURRING_PAYMENT_SERVICE))
		return nil, nil, err
	}
	_ = recurringpaymentConf

	recurringpaymentGenConf, err := dynconf.LoadConfig(recurringpaymentconf.Load, rpservergenconf.NewConfig, cfg.RECURRING_PAYMENT_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.RECURRING_PAYMENT_SERVICE))
		return nil, nil, err
	}

	_ = recurringpaymentGenConf

	inPaymentOrderUpdatePublisherVar3, err := sqs.NewPublisherWithConfig(ctx, recurringpaymentGenConf.InPaymentOrderUpdatePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	fetchAndCreateOffAppRecurringPaymentPublisher, err := sqs.NewPublisherWithConfig(ctx, recurringpaymentGenConf.FetchAndCreateOffAppRecurringPaymentPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	fetchAndCreateFailedEnachTransactionPublisher, err := sqs.NewPublisherWithConfig(ctx, recurringpaymentGenConf.FetchAndCreateFailedEnachTransactionPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}

	serviceVar27 := wire3.InitialisePaymentGatewayService(recurringpaymentConf, paymentGatewayClient, payClient, orderServiceClient, epifiCRDB, usersClient, celestialClient, recurringPaymentServiceClient, piClient, crdbResourceMap, useCaseDbResourceProvider, tspUserServiceClient, groupClient, actorClient, enachServiceClient, recurringpaymentGenConf)

	paymentgateway2.RegisterPaymentGatewayServiceServer(s, serviceVar27)

	serviceVar28 := wire3.InitialiseRecurringPaymentService(epifiCRDB, standingInstructionServiceClient, orderServiceClient, savingsClient, actorClient, authClient, usersClient, paymentClientVar3, recurringpaymentConf, mandateServiceClient, accountPIRelationClient, piClient, timelineServiceClient, uPIClient, commsClientVar5, recurringpaymentGenConf, celestialClient, upiRedisStore, inPaymentOrderUpdatePublisherVar3, groupClient, enachServiceClient, balanceClient, enachClient, fetchAndCreateOffAppRecurringPaymentPublisher, operationalStatusServiceClient, upcomingTransactionsClient, merchantServiceClient, catalogManagerClient, depositClient, paymentGatewayServiceClient, crdbResourceMap, crdbTxnResourceMap, useCaseDbResourceProvider, tspUserServiceClient, payClient, broker, catalogManagerClientVar2)

	rppb.RegisterRecurringPaymentServiceServer(s, serviceVar28)

	serviceVar29 := wire3.InitialiseCxService(epifiCRDB, orderServiceClient, recurringpaymentConf, crdbResourceMap, recurringpaymentGenConf)

	recurringpaymentcxpb.RegisterCXServer(s, serviceVar29)

	serviceVar30 := wire3.InitialiseStandingInstructionService(epifiCRDB, standingInstructionClient, piClient, authClient, savingsClient, usersClient, actorClient, paymentClient, bankCustomerServiceClient, crdbResourceMap, recurringpaymentConf, recurringpaymentGenConf)

	sipb.RegisterStandingInstructionServiceServer(s, serviceVar30)

	recurringPaymentDevService := wire3.InitialiseDevService(epifiCRDB, crdbResourceMap, useCaseDbResourceProvider, recurringpaymentConf, recurringpaymentGenConf)

	recurringpaymentdeveloper.RegisterRecurringPaymentDevServer(s, recurringPaymentDevService)

	serviceVar31, err := wire3.InitialiseEnachService(recurringPaymentPGDB, useCaseDbResourceProvider, recurringpaymentConf, recurringpaymentGenConf, recurringPaymentServiceClient, actorClient, piClient, usersClient, celestialClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	enachpb.RegisterEnachServiceServer(s, serviceVar31)

	enachDevService := wire3.InitializeEnachDevService(recurringpaymentGenConf, recurringPaymentPGDB, useCaseDbResourceProvider)

	enachdevpb.RegisterEnachDevServer(s, enachDevService)

	consumerServiceVar2 := wire3.InitialiseRecurringPaymentConsumerService(recurringPaymentServiceClient, savingsClient, enachClient, piClient, enachServiceClient, usersClient, groupClient, epifiCRDB, actorClient, merchantServiceClient, crdbResourceMap, recurringpaymentConf, recurringpaymentGenConf, broker, cardProvisioningClient, debitCardMandateServiceClient, timelineServiceClient, orderServiceClient, gconf.VendorApiConf(), fetchAndCreateFailedEnachTransactionPublisher)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, recurringpaymentGenConf.RecurringPaymentCreationAuthVendorCallbackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		recurringpaymentconsumerpb.RegisterProcessRecurringPaymentCreationAuthorizationVendorCallbackMethodToSubscriber(subscriber, consumerServiceVar2)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, recurringpaymentGenConf.FetchAndCreateOffAppRecurringPaymentSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		recurringpaymentconsumerpb.RegisterFetchAndCreateOffAppRecurringPaymentsMethodToSubscriber(subscriber, consumerServiceVar2)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, recurringpaymentGenConf.OffAppRecurringPaymentExecutionSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		recurringpaymentconsumerpb.RegisterProcessOffAppRecurringPaymentExecutionMethodToSubscriber(subscriber, consumerServiceVar2)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, recurringpaymentGenConf.FetchAndCreateFailedEnachTransactionSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		recurringpaymentconsumerpb.RegisterFetchAndCreateFailedEnachTransactionsMethodToSubscriber(subscriber, consumerServiceVar2)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	consumerServiceVar3 := wire3.InitializeEnachConsumerService(celestialClient)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, recurringpaymentGenConf.EnachFundTransferOrderUpdateSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		enachconsumerpb.RegisterProcessOrderEventForExecutionStatusUpdateMethodToSubscriber(subscriber, consumerServiceVar3)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}
	configNameToConfMap[cfg.ConfigName(cfg.RECURRING_PAYMENT_SERVICE)] = &commonexplorer.Config{StaticConf: &recurringpaymentconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupUpi(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	uPIClientVar3 vgtypes.VgUpiClientWithInterceptors,
	piClient pipb.PiClient,
	accountPIRelationClient accountpipb.AccountPIRelationClient,
	epifiCRDB types.EpifiCRDB,
	orderServiceClient orderpb.OrderServiceClient,
	paymentClientVar3 paymentpb.PaymentClient,
	actorClient actor.ActorClient,
	timelineServiceClient timelinepb.TimelineServiceClient,
	savingsClient savingspb.SavingsClient,
	authClient authpb.AuthClient,
	commsClientVar6 upitypes.UpiCommsClientWithInterceptors,
	uPIClient upipb.UPIClient,
	usersClient user.UsersClient,
	groupClient usergrouppb.GroupClient,
	onboardingClient onboarding.OnboardingClient,
	upiRedisStore upitypes.UpiRedisStore,
	upiOnboardingClient upionboardingpb.UpiOnboardingClient,
	connectedAccountClient connectedaccountpb.ConnectedAccountClient,
	locationClient locationpb.LocationClient,
	recurringPaymentServiceClient rppb.RecurringPaymentServiceClient,
	nudgeServiceClient nudge.NudgeServiceClient,
	uPIOnboardingRedisStore upitypes.UPIOnboardingRedisStore,
	celestialClient celestialpb.CelestialClient,
	consentClient consentpb.ConsentClient,
	commsClientVar9 comms.CommsClient) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	upiConf, err := upiconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.UPI_SERVICE))
		return nil, nil, err
	}
	_ = upiConf

	upiGenConf, err := dynconf.LoadConfig(upiconf.Load, upidyanmicconf.NewConfig, cfg.UPI_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.UPI_SERVICE))
		return nil, nil, err
	}

	_ = upiGenConf

	vPACreationPublisher, err := sqs.NewPublisherWithConfig(ctx, upiGenConf.VPACreationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	vPAVerificationPublisher, err := sqs.NewPublisherWithConfig(ctx, upiGenConf.VPAVerificationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	upiComplaintStatusAutoUpdateEventPublisher, err := sqs.NewPublisherWithConfig(ctx, upiGenConf.UpiComplaintStatusAutoUpdateEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	uPIEnquiryPublisherVar2, err := sqs.NewPublisherWithConfig(ctx, upiGenConf.UPIEnquiryPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}

	upiEventSnsPublisher, err := sns.NewSnsPublisherWithConfig(ctx, upiGenConf.UpiEventSnsPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}
	pinAttemptsExceededEventSnsPublisher, err := sns.NewSnsPublisherWithConfig(ctx, upiGenConf.PinAttemptsExceededEventSnsPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}

	serviceVar32 := wire4.InitializeService(uPIClientVar3, piClient, accountPIRelationClient, epifiCRDB, upiConf, orderServiceClient, paymentClientVar3, actorClient, timelineServiceClient, broker, savingsClient, vPACreationPublisher, authClient, commsClientVar6, uPIClient, usersClient, groupClient, onboardingClient, upiRedisStore, vPAVerificationPublisher, upiEventSnsPublisher, pinAttemptsExceededEventSnsPublisher, upiGenConf, upiOnboardingClient, connectedAccountClient, locationClient, recurringPaymentServiceClient)

	upipb.RegisterUPIServer(s, serviceVar32)

	serviceVar33 := wire4.InitializeUpiCxService(accountPIRelationClient, piClient, uPIClient, upiOnboardingClient)

	upicxpb.RegisterUpiCXServer(s, serviceVar33)

	upiDevService := wire4.InitializeDevService(epifiCRDB, upiRedisStore, upiConf)

	upideveloper.RegisterDevServer(s, upiDevService)

	serviceVar34 := wire4.InitializeConsumerService(paymentClientVar3, uPIClientVar3, piClient, accountPIRelationClient, actorClient, timelineServiceClient, orderServiceClient, epifiCRDB, broker, commsClientVar6, savingsClient, upiConf, authClient, uPIClient, recurringPaymentServiceClient, upiRedisStore, upiOnboardingClient, usersClient, groupClient, upiComplaintStatusAutoUpdateEventPublisher, uPIEnquiryPublisherVar2, upiEventSnsPublisher, onboardingClient, nudgeServiceClient, upiGenConf, locationClient)

	if func(conf *config.Config) bool {
		return cfg.IsSimulatedEnv(conf.Environment)
	}(conf) {
		upipb.RegisterConsumerServer(s, serviceVar34)
	}

	serviceVar35 := wire4.InitializeConsumerService(paymentClientVar3, uPIClientVar3, piClient, accountPIRelationClient, actorClient, timelineServiceClient, orderServiceClient, epifiCRDB, broker, commsClientVar6, savingsClient, upiConf, authClient, uPIClient, recurringPaymentServiceClient, upiRedisStore, upiOnboardingClient, usersClient, groupClient, upiComplaintStatusAutoUpdateEventPublisher, uPIEnquiryPublisherVar2, upiEventSnsPublisher, onboardingClient, nudgeServiceClient, upiGenConf, locationClient)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, upiGenConf.VPAVerificationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer5.RegisterVerifyVpaMethodToSubscriber(subscriber, serviceVar35)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, upiGenConf.UPIUserDevicePropertiesUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer5.RegisterProcessUserDevicePropertiesUpdateEventMethodToSubscriber(subscriber, serviceVar35)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, upiGenConf.OrderUpdateEventForComplianceSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer5.RegisterProcessOrderUpdateEventForComplianceMethodToSubscriber(subscriber, serviceVar35)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, upiGenConf.ReqAuthEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		upipb.RegisterProcessReqAuthMethodToSubscriber(subscriber, serviceVar35)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, upiGenConf.RespPayEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		upipb.RegisterProcessResPayMethodToSubscriber(subscriber, serviceVar35)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, upiGenConf.ReqTxnConfirmationEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		upipb.RegisterProcessReqTxnConfirmationMethodToSubscriber(subscriber, serviceVar35)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, upiGenConf.ReqValAddressEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		upipb.RegisterProcessReqValAddressMethodToSubscriber(subscriber, serviceVar35)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, upiGenConf.ListPspKeysEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		upipb.RegisterProcessListPspKeysMethodToSubscriber(subscriber, serviceVar35)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, upiGenConf.UPIOnboardingEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		upipb.RegisterProcessOnboardingEventMethodToSubscriber(subscriber, serviceVar35)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, upiGenConf.UPIAuthFactorUpdateEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		upipb.RegisterProcessAuthFactorUpdateEventMethodToSubscriber(subscriber, serviceVar35)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, upiGenConf.UpiComplaintReqTxnConfirmationEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		upipb.RegisterProcessReqTxnConfirmationComplaintMethodToSubscriber(subscriber, serviceVar35)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, upiGenConf.RespComplaintSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		upipb.RegisterProcessRespComplaintMethodToSubscriber(subscriber, serviceVar35)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	extSub, err = awswire.InitializeExtendedSubscriberWithGenConf(ctx, upiGenConf.ListVaeEventSubscriber(), awsConf, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		upipb.RegisterProcessListVaeMethodToSubscriber(subscriber, serviceVar35)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors())
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if extSub.ShouldStartWorker {
		extendedSqsSubscribers = append(extendedSqsSubscribers, extSub)
	}

	serviceVar36 := wire4.InitializeMandateService(epifiCRDB, piClient, accountPIRelationClient, uPIClientVar3, orderServiceClient, paymentClientVar3, upiRedisStore, upiConf, usersClient, groupClient, actorClient, savingsClient, authClient, recurringPaymentServiceClient, locationClient, upiGenConf)

	upimandatepb.RegisterMandateServiceServer(s, serviceVar36)

	serviceVar37 := wire4.InitializeUpiOnboardingService(epifiCRDB, uPIClientVar3, piClient, accountPIRelationClient, uPIOnboardingRedisStore, upiRedisStore, upiConf, upiGenConf, celestialClient, savingsClient, actorClient, authClient, uPIClient, groupClient, usersClient, consentClient, upiEventSnsPublisher, broker, recurringPaymentServiceClient, orderServiceClient, onboardingClient, connectedAccountClient, nudgeServiceClient, locationClient)

	upionboardingpb.RegisterUpiOnboardingServer(s, serviceVar37)

	serviceVar38 := wire4.InitializeUpiOnboardingConsumerProcessor(epifiCRDB, upiOnboardingClient, uPIClientVar3, commsClientVar9, upiGenConf, broker)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, upiGenConf.VpaMigrationConsentSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		upionboardingconsumerpb.RegisterMigrateVpaMethodToSubscriber(subscriber, serviceVar38)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, upiGenConf.ReqMapperConfirmationEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		upionboardingconsumerpb.RegisterProcessReqMapperConfirmationMethodToSubscriber(subscriber, serviceVar38)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	serviceVar39 := wire4.InitializeSimulationService(uPIClientVar3)

	if func(conf *config.Config) bool {
		return cfg.IsSimulatedEnv(conf.Environment)
	}(conf) {
		simulation2.RegisterSimulationServer(s, serviceVar39)
	}

	serviceVar40 := wire4.InitializeMandateConsumerService(epifiCRDB, piClient, accountPIRelationClient, uPIClientVar3, actorClient, recurringPaymentServiceClient, upiRedisStore, upiConf, usersClient, groupClient, commsClientVar6, savingsClient, authClient, locationClient, upiGenConf)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, upiGenConf.ReqAuthMandateEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer6.RegisterProcessReqAuthMandateMethodToSubscriber(subscriber, serviceVar40)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, upiGenConf.ReqMandateConfirmationEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer6.RegisterProcessReqMandateConfirmationMethodToSubscriber(subscriber, serviceVar40)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, upiGenConf.RespMandateEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer6.RegisterProcessRespMandateMethodToSubscriber(subscriber, serviceVar40)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, upiGenConf.ReqAuthValCustEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer6.RegisterProcessReqAuthValCustMethodToSubscriber(subscriber, serviceVar40)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}
	configNameToConfMap[cfg.ConfigName(cfg.UPI_SERVICE)] = &commonexplorer.Config{StaticConf: &upiconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupBillpay(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	billpayPGDB types.BillpayPGDB,
	billPayServiceClient billpayvgpb.BillPayServiceClient,
	mobileRechargeServiceClient rechargepb.MobileRechargeServiceClient,
	usersClient user.UsersClient,
	celestialClient celestialpb.CelestialClient,
	billersRedisStore wiretypes.BillersRedisStore) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	billpayConf, err := billpayconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.BILL_PAY_SERVICE))
		return nil, nil, err
	}
	_ = billpayConf

	billpayGenConf, err := dynconf.LoadConfig(billpayconf.Load, genconf2.NewConfig, cfg.BILL_PAY_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.BILL_PAY_SERVICE))
		return nil, nil, err
	}

	_ = billpayGenConf

	serviceVar41 := wire5.InitializeService(billpayGenConf, billpayPGDB, billPayServiceClient, mobileRechargeServiceClient, usersClient, celestialClient, billersRedisStore)

	billpaypb.RegisterBillPayServer(s, serviceVar41)

	configNameToConfMap[cfg.ConfigName(cfg.BILL_PAY_SERVICE)] = &commonexplorer.Config{StaticConf: &billpayconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

func initLogger() error {
	// Load configuration
	conf, err := config.Load(cfg.ORDER_SERVER)
	if err != nil {
		// cannot log error since logger is not initialized yet.
		return err
	}

	logger.InitV2(conf.Environment, conf.Logging)
	if conf.SecureLogging != nil {
		logger.InitSecureLoggerV2(conf.SecureLogging)
	}
	return nil
}

func getEnvNamespaceClient(ns temporalpkg.Namespace, env string) string {
	if cfg.IsLocalEnv(env) {
		return strings.ToLower(string(ns))
	}

	return fmt.Sprintf("%s-%s", env, strings.ToLower(string(ns)))
}

func getSQSConsumerInterceptors() []grpc.UnaryServerInterceptor {
	return []grpc.UnaryServerInterceptor{questinterceptor.UserContextInterceptor}
}
