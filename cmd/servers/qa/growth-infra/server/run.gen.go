// code generated by tools/servergen
package server

import (
	context "context"
	"fmt"
	"net/http"
	strings "strings"
	time "time"

	awssdk "github.com/aws/aws-sdk-go-v2/aws"
	awssqs "github.com/aws/aws-sdk-go-v2/service/sqs"
	prometheus "github.com/prometheus/client_golang/prometheus"
	redis "github.com/redis/go-redis/v9"
	analytics "github.com/rudderlabs/analytics-go"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
	reflection "google.golang.org/grpc/reflection"
	gorm "gorm.io/gorm"

	"github.com/epifi/be-common/pkg/async/goroutine"
	awsconfpkg "github.com/epifi/be-common/pkg/aws/v2/config"
	s3pkg "github.com/epifi/be-common/pkg/aws/v2/s3"
	sns "github.com/epifi/be-common/pkg/aws/v2/sns"
	sqs "github.com/epifi/be-common/pkg/aws/v2/sqs"
	awswire "github.com/epifi/be-common/pkg/aws/v2/wire"
	cache "github.com/epifi/be-common/pkg/cache"
	cfg "github.com/epifi/be-common/pkg/cfg"
	consul "github.com/epifi/be-common/pkg/cfg/consul"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	commonexplorer "github.com/epifi/be-common/pkg/cfg/explorer"
	config "github.com/epifi/be-common/pkg/cmd/config"
	genconf "github.com/epifi/be-common/pkg/cmd/config/genconf"
	types "github.com/epifi/be-common/pkg/cmd/types"
	epificontext "github.com/epifi/be-common/pkg/epificontext"
	epifigrpc "github.com/epifi/be-common/pkg/epifigrpc"
	commoninterceptors "github.com/epifi/be-common/pkg/epifigrpc/interceptors"
	servergenwire2 "github.com/epifi/be-common/pkg/epifigrpc/interceptors/servergen_wire"
	resolver "github.com/epifi/be-common/pkg/epifigrpc/resolver"
	epifiserver "github.com/epifi/be-common/pkg/epifiserver"
	temporalpkg "github.com/epifi/be-common/pkg/epifitemporal"
	errgroup "github.com/epifi/be-common/pkg/errgroup"
	events "github.com/epifi/be-common/pkg/events"
	logger "github.com/epifi/be-common/pkg/logger"
	profiling "github.com/epifi/be-common/pkg/profiling"
	queue "github.com/epifi/be-common/pkg/queue"
	ratelimiter "github.com/epifi/be-common/pkg/ratelimiter"
	store "github.com/epifi/be-common/pkg/ratelimiter/store"
	storage "github.com/epifi/be-common/pkg/storage"
	storage2 "github.com/epifi/be-common/pkg/storage/v2"
	storagev2analytics "github.com/epifi/be-common/pkg/storage/v2/analytics"
	otel "github.com/epifi/be-common/pkg/tracing/opentelemetry"
	servermetrics "github.com/epifi/be-common/tools/servergen/metrics"
	accrualconf "github.com/epifi/gamma/accrual/config"
	accrualgenconf "github.com/epifi/gamma/accrual/config/genconf"
	wire "github.com/epifi/gamma/accrual/wire"
	operstatuspb "github.com/epifi/gamma/api/accounts/operstatus"
	accrualpb "github.com/epifi/gamma/api/accrual"
	accrualconsumer "github.com/epifi/gamma/api/accrual/consumer"
	crossattachpb "github.com/epifi/gamma/api/acquisition/crossattach"
	actor "github.com/epifi/gamma/api/actor"
	alfredpb "github.com/epifi/gamma/api/alfred"
	dynamicelements "github.com/epifi/gamma/api/analyser/dynamicelements"
	authpb "github.com/epifi/gamma/api/auth"
	bankcust "github.com/epifi/gamma/api/bankcust"
	compliancepb "github.com/epifi/gamma/api/bankcust/compliance"
	provisioning "github.com/epifi/gamma/api/card/provisioning"
	casperpb "github.com/epifi/gamma/api/casper"
	casperdeveloperpb "github.com/epifi/gamma/api/casper/developer"
	discountspb "github.com/epifi/gamma/api/casper/discounts"
	exchangerpb "github.com/epifi/gamma/api/casper/exchanger"
	evrpb "github.com/epifi/gamma/api/casper/external_vendor_redemption"
	notificationpb "github.com/epifi/gamma/api/casper/external_vendor_redemption/notification"
	casperjobhelperpb "github.com/epifi/gamma/api/casper/jobhelper"
	itc "github.com/epifi/gamma/api/casper/offervendor/itc"
	vistara "github.com/epifi/gamma/api/casper/offervendor/vistara"
	redemptionpb "github.com/epifi/gamma/api/casper/redemption"
	categorizerpb "github.com/epifi/gamma/api/categorizer"
	cmspb "github.com/epifi/gamma/api/cms"
	cmsdeveloperpb "github.com/epifi/gamma/api/cms/developer"
	comms "github.com/epifi/gamma/api/comms"
	cdpb "github.com/epifi/gamma/api/comms/developer"
	commsdevaction "github.com/epifi/gamma/api/comms/developer/actions"
	commsdevicetokenpb "github.com/epifi/gamma/api/comms/device_token"
	tcpb "github.com/epifi/gamma/api/comms/inapptargetedcomms"
	uppb "github.com/epifi/gamma/api/comms/user_preference"
	capb "github.com/epifi/gamma/api/connected_account"
	depositpb "github.com/epifi/gamma/api/deposit"
	docspb "github.com/epifi/gamma/api/docs"
	depb "github.com/epifi/gamma/api/dynamic_elements"
	employmentpb "github.com/epifi/gamma/api/employment"
	fireflypb "github.com/epifi/gamma/api/firefly"
	ffaccountspb "github.com/epifi/gamma/api/firefly/accounting"
	ffbillingpb "github.com/epifi/gamma/api/firefly/billing"
	ffpinotpb "github.com/epifi/gamma/api/firefly/pinot"
	fitttpb "github.com/epifi/gamma/api/fittt"
	healthenginepb "github.com/epifi/gamma/api/health_engine"
	faqpb "github.com/epifi/gamma/api/inapphelp/faq/serving"
	inappreferralpb "github.com/epifi/gamma/api/inappreferral"
	epfpb "github.com/epifi/gamma/api/insights/epf"
	networth "github.com/epifi/gamma/api/insights/networth"
	invnotificationpb "github.com/epifi/gamma/api/investment/mutualfund/notifications"
	vkycpb "github.com/epifi/gamma/api/kyc/vkyc"
	merchantpb "github.com/epifi/gamma/api/merchant"
	nudgepb "github.com/epifi/gamma/api/nudge"
	nudgeconsumerpb "github.com/epifi/gamma/api/nudge/consumer"
	nudgedatacollectorpb "github.com/epifi/gamma/api/nudge/datacollector"
	nudgedeveloperpb "github.com/epifi/gamma/api/nudge/developer"
	nudgeentryevaluatorpb "github.com/epifi/gamma/api/nudge/entryevaluator"
	nudgeexitevaluatorpb "github.com/epifi/gamma/api/nudge/exitevaluator"
	journeypb "github.com/epifi/gamma/api/nudge/journey"
	journeyconsumerpb "github.com/epifi/gamma/api/nudge/journey/consumer"
	orderpb "github.com/epifi/gamma/api/order"
	ordercxpb "github.com/epifi/gamma/api/order/cx"
	txnpb "github.com/epifi/gamma/api/order/payment"
	pan "github.com/epifi/gamma/api/pan"
	paypb "github.com/epifi/gamma/api/pay"
	pipb "github.com/epifi/gamma/api/paymentinstrument"
	accountpipb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	explorerpb "github.com/epifi/gamma/api/pkg/cfg/explorer"
	preapprovedloan "github.com/epifi/gamma/api/preapprovedloan"
	questdevpb "github.com/epifi/gamma/api/quest/developer"
	frontend2 "github.com/epifi/gamma/api/quest/frontend"
	managerpb "github.com/epifi/gamma/api/quest/manager"
	rewardspb "github.com/epifi/gamma/api/rewards"
	campaigncommpb "github.com/epifi/gamma/api/rewards/campaigncomm"
	clawbackprocessor "github.com/epifi/gamma/api/rewards/clawbackprocessor"
	datacollector "github.com/epifi/gamma/api/rewards/datacollector"
	rewardsdeveloperpb "github.com/epifi/gamma/api/rewards/developer"
	expiry2 "github.com/epifi/gamma/api/rewards/expiry"
	generator "github.com/epifi/gamma/api/rewards/generator"
	rewardsjobhelperpb "github.com/epifi/gamma/api/rewards/jobhelper"
	luckydrawpb "github.com/epifi/gamma/api/rewards/luckydraw"
	notification3 "github.com/epifi/gamma/api/rewards/notification"
	rewardspinotpb "github.com/epifi/gamma/api/rewards/pinot"
	processor "github.com/epifi/gamma/api/rewards/processor"
	rewardsprojector2 "github.com/epifi/gamma/api/rewards/projector"
	requeueservice "github.com/epifi/gamma/api/rewards/requeueservice"
	rewardofferspb "github.com/epifi/gamma/api/rewards/rewardoffers"
	unlocker2 "github.com/epifi/gamma/api/rewards/unlocker"
	riskpb "github.com/epifi/gamma/api/risk"
	salaryprogrampb "github.com/epifi/gamma/api/salaryprogram"
	savingspb "github.com/epifi/gamma/api/savings"
	searchpb "github.com/epifi/gamma/api/search"
	segmentpb "github.com/epifi/gamma/api/segment"
	segmentconsumerpb "github.com/epifi/gamma/api/segment/consumer"
	segmentdeveloperpb "github.com/epifi/gamma/api/segment/developer"
	tieringpb "github.com/epifi/gamma/api/tiering"
	timelinepb "github.com/epifi/gamma/api/timeline"
	_ "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/screen_option_dependency"
	user "github.com/epifi/gamma/api/user"
	usergrouppb "github.com/epifi/gamma/api/user/group"
	onboardingpb "github.com/epifi/gamma/api/user/onboarding"
	userintelpb "github.com/epifi/gamma/api/userintel"
	usstocksordermgpb "github.com/epifi/gamma/api/usstocks/order"
	ussrewardmgpb "github.com/epifi/gamma/api/usstocks/rewards"
	usstockspb "github.com/epifi/gamma/api/usstocks/uss_dynamic_elements"
	emailpb "github.com/epifi/gamma/api/vendorgateway/email"
	fcmpb "github.com/epifi/gamma/api/vendorgateway/fcm"
	dreamfolksvgpb "github.com/epifi/gamma/api/vendorgateway/offers/dreamfolks"
	loyltypb "github.com/epifi/gamma/api/vendorgateway/offers/loylty"
	poshvinevgpb "github.com/epifi/gamma/api/vendorgateway/offers/poshvine"
	qwikcilvervgpb "github.com/epifi/gamma/api/vendorgateway/offers/qwikcilver"
	thriwevgpb "github.com/epifi/gamma/api/vendorgateway/offers/thriwe"
	vistaravgpb "github.com/epifi/gamma/api/vendorgateway/offers/vistara"
	vgaccountpb "github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	smspb "github.com/epifi/gamma/api/vendorgateway/sms"
	whatsapppb "github.com/epifi/gamma/api/vendorgateway/whatsapp"
	vmpb "github.com/epifi/gamma/api/vendormapping"
	whatsappbot "github.com/epifi/gamma/api/whatsapp_bot"
	casperconf "github.com/epifi/gamma/casper/config"
	genconf2 "github.com/epifi/gamma/casper/config/genconf"
	wire2 "github.com/epifi/gamma/casper/wire"
	types2 "github.com/epifi/gamma/casper/wire/types"
	hook "github.com/epifi/gamma/cmd/servers/qa/growth-infra/hook"
	cmsconf "github.com/epifi/gamma/cms/config"
	wire4 "github.com/epifi/gamma/cms/wire"
	commsconf "github.com/epifi/gamma/comms/config"
	genconf4 "github.com/epifi/gamma/comms/config/genconf"
	wire6 "github.com/epifi/gamma/comms/wire"
	commstypes "github.com/epifi/gamma/comms/wire/types"
	customdelayqueueconf "github.com/epifi/gamma/customdelayqueue/config"
	dynamicelementsconf "github.com/epifi/gamma/dynamicelements/config"
	dynamicelementsgenconf "github.com/epifi/gamma/dynamicelements/config/genconf"
	wire7 "github.com/epifi/gamma/dynamicelements/wire"
	wiretypes "github.com/epifi/gamma/dynamicelements/wire/types"
	nudgeconf "github.com/epifi/gamma/nudge/config"
	genconf5 "github.com/epifi/gamma/nudge/config/genconf"
	wire9 "github.com/epifi/gamma/nudge/wire"
	types4 "github.com/epifi/gamma/nudge/wire/types"
	explorer "github.com/epifi/gamma/pkg/cfg/explorer"
	customdelayqueue "github.com/epifi/gamma/pkg/customdelayqueue"
	customqueuewire "github.com/epifi/gamma/pkg/customdelayqueue/wire"
	questinterceptor "github.com/epifi/gamma/pkg/epifigrpc/interceptors/quest"
	servergenwire "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
	fepkg "github.com/epifi/gamma/pkg/frontend/msg"
	questconf "github.com/epifi/gamma/quest/config"
	questgenconfig "github.com/epifi/gamma/quest/config/genconf"
	questsdkinit "github.com/epifi/gamma/quest/sdk/init"
	wire5 "github.com/epifi/gamma/quest/wire"
	rewardsconf "github.com/epifi/gamma/rewards/config"
	genconf3 "github.com/epifi/gamma/rewards/config/genconf"
	wire3 "github.com/epifi/gamma/rewards/wire"
	types3 "github.com/epifi/gamma/rewards/wire/types"
	segmentconf "github.com/epifi/gamma/segment/config"
	genconf6 "github.com/epifi/gamma/segment/config/genconf"
	wire10 "github.com/epifi/gamma/segment/wire"
	types5 "github.com/epifi/gamma/segment/wire/types"
	whatsappbotconf "github.com/epifi/gamma/whatsappbot/config"
	whatsappbotgenconf "github.com/epifi/gamma/whatsappbot/config/genconf"
	wire8 "github.com/epifi/gamma/whatsappbot/wire"
	wabottypes "github.com/epifi/gamma/whatsappbot/wire/types"
)

// nolint: funlen
func Run(ctx context.Context, initNotifier chan<- cfg.ServerName) error {
	// Panic handler for logging panics in standard format
	epifiserver.HandlePanic()
	resolver.RegisterBuilder(resolver.ConsulSchemeName)
	var configNameToConfMap = make(commonexplorer.CfgExplorerMap)

	err := initLogger()
	if err != nil {
		// cannot log error since logger maynot be initialized yet.
		return err
	}
	ctx = epificontext.WithTraceId(ctx, metadata.MD{})
	// Load static configuration
	conf, err := config.Load(cfg.GROWTH_INFRA_SERVER)
	if err != nil {
		logger.Error(ctx, "failed to load static conf", zap.Error(err))
		return err
	}
	_ = conf
	// Load generated dynamic configuration
	gconf, err := genconf.Load(cfg.GROWTH_INFRA_SERVER)
	if err != nil {
		logger.Error(ctx, "failed to load dynamic conf", zap.Error(err))
		return err
	}
	logger.Info(ctx, "initiating server")

	if gconf.Profiling().AutomaticProfiling().EnableAutoProfiling() {
		profilingErr := profiling.AutomaticProfiling(context.Background(), cfg.GROWTH_INFRA_SERVER, gconf.Profiling(), gconf.Environment(), gconf.AWS().Region)
		if profilingErr != nil {
			logger.Error(ctx, "failed to start automatic profiling", zap.Error(profilingErr))
			return profilingErr
		}
	}

	if gconf.Tracing().Enable {
		_, shutdown, tracingErr := otel.ConfigureOpenTelemetryV2(context.Background(), cfg.GROWTH_INFRA_SERVER, gconf.Environment(), gconf.Profiling().PyroscopeProfiling().EnablePyroscope())
		if tracingErr != nil {
			logger.Error(ctx, "failed to configure open telemetry", zap.Error(tracingErr))
			return err
		}
		defer shutdown()
	}

	err = profiling.PyroscopeProfilingV2(gconf.Name(), gconf.Profiling(), gconf.Environment())
	if err != nil {
		logger.Error(ctx, "error starting pyroscope profiler", zap.Error(err))
	}

	if !cfg.IsLocalEnv(gconf.Environment()) && !cfg.IsRemoteDebugEnabled() && !cfg.IsTestTenantEnabled() && !cfg.IsTeamSpaceTenant() {
		initDisc, _ := consul.GetBool(consul.GetInitDiscoveryKey(gconf.Environment(), string(gconf.Name())))
		queue.SetWorkerInitialization(initDisc)
	}

	rateLimiterRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()[gconf.RedisRateLimiterName()], gconf.Tracing().Enable)
	defer func() { _ = rateLimiterRedisStore.Close() }()

	var (
		crdbResourceMap    *storage2.DBResourceProvider[*gorm.DB]
		crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor]
	)

	epifiCRDB, err := storage2.NewCRDBWithConfig(gconf.Databases()["EpifiCRDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "EpifiCRDB"))
		return err
	}
	epifiCRDBSqlDb, err := epifiCRDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "EpifiCRDB"))
		return err
	}
	defer func() { _ = epifiCRDBSqlDb.Close() }()

	accrualPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["AccrualPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "AccrualPGDB"))
		return err
	}
	accrualPGDBSqlDb, err := accrualPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "AccrualPGDB"))
		return err
	}
	defer func() { _ = accrualPGDBSqlDb.Close() }()
	casperPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["CasperPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "CasperPGDB"))
		return err
	}
	casperPGDBSqlDb, err := casperPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "CasperPGDB"))
		return err
	}
	defer func() { _ = casperPGDBSqlDb.Close() }()
	rewardsPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["RewardsPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "RewardsPGDB"))
		return err
	}
	rewardsPGDBSqlDb, err := rewardsPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "RewardsPGDB"))
		return err
	}
	defer func() { _ = rewardsPGDBSqlDb.Close() }()
	cmsPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["CmsPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "CmsPGDB"))
		return err
	}
	cmsPGDBSqlDb, err := cmsPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "CmsPGDB"))
		return err
	}
	defer func() { _ = cmsPGDBSqlDb.Close() }()
	questPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["QuestPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "QuestPGDB"))
		return err
	}
	questPGDBSqlDb, err := questPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "QuestPGDB"))
		return err
	}
	defer func() { _ = questPGDBSqlDb.Close() }()
	commsPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["CommsPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "CommsPGDB"))
		return err
	}
	commsPGDBSqlDb, err := commsPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "CommsPGDB"))
		return err
	}
	defer func() { _ = commsPGDBSqlDb.Close() }()
	nudgePGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["NudgePGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "NudgePGDB"))
		return err
	}
	nudgePGDBSqlDb, err := nudgePGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "NudgePGDB"))
		return err
	}
	defer func() { _ = nudgePGDBSqlDb.Close() }()
	segmentPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["SegmentPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "SegmentPGDB"))
		return err
	}
	segmentPGDBSqlDb, err := segmentPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "SegmentPGDB"))
		return err
	}
	defer func() { _ = segmentPGDBSqlDb.Close() }()

	collapserRueidisRedisStore, err := storage2.NewRueidisClientFromConfig(gconf.RueidisRedisClients()["CollapserRueidisRedisStore"])
	if err != nil {
		logger.Error(ctx, "failed to establish rueidis redis client conn for CollapserRueidisRedisStore", zap.Error(err))
		return err
	}
	defer func() { collapserRueidisRedisStore.Close() }()
	collapserRueidisCacheStorage := cache.NewRueidisCacheStorageWithHystrix(cache.NewRueidisCacheWithOptions(collapserRueidisRedisStore), gconf.RueidisRedisClients()["CollapserRueidisRedisStore"].Hystrix)
	growthinfraConn := epifigrpc.NewServerConn(cfg.GROWTH_INFRA_SERVER, cfg.GROWTH_INFRA_SERVER)
	defer epifigrpc.CloseConn(growthinfraConn)
	managerClient := managerpb.NewManagerClient(growthinfraConn)
	authConn := epifigrpc.NewServerConn(cfg.GROWTH_INFRA_SERVER, cfg.AUTH_SERVER)
	defer epifigrpc.CloseConn(authConn)
	groupClient := usergrouppb.NewGroupClient(authConn)
	usersClient := user.NewUsersClient(authConn)
	actorConn := epifigrpc.NewServerConn(cfg.GROWTH_INFRA_SERVER, cfg.ACTOR_SERVER)
	defer epifigrpc.CloseConn(actorConn)
	actorClient := actor.NewActorClient(actorConn)
	segmentationServiceClient := segmentpb.NewSegmentationServiceClient(growthinfraConn)
	accrualClient := accrualpb.NewAccrualClient(growthinfraConn)
	centralgrowthConn := epifigrpc.NewServerConn(cfg.GROWTH_INFRA_SERVER, cfg.CENTRAL_GROWTH_SERVER)
	defer epifigrpc.CloseConn(centralgrowthConn)
	savingsClient := savingspb.NewSavingsClient(centralgrowthConn)
	vendorgatewayConn := epifigrpc.NewServerConn(cfg.GROWTH_INFRA_SERVER, cfg.VENDOR_GATEWAY_SERVER)
	defer epifigrpc.CloseConn(vendorgatewayConn)
	loyltyClient := loyltypb.NewLoyltyClient(vendorgatewayConn)
	vendormappingConn := epifigrpc.NewServerConn(cfg.GROWTH_INFRA_SERVER, cfg.VENDORMAPPING_SERVER)
	defer epifigrpc.CloseConn(vendormappingConn)
	vendorMappingServiceClient := vmpb.NewVendorMappingServiceClient(vendormappingConn)
	cardConn := epifigrpc.NewServerConn(cfg.GROWTH_INFRA_SERVER, cfg.CARD_SERVER)
	defer epifigrpc.CloseConn(cardConn)
	cardProvisioningClient := provisioning.NewCardProvisioningClient(cardConn)
	onboardingClient := onboardingpb.NewOnboardingClient(authConn)
	poshvineClient := poshvinevgpb.NewPoshvineClient(vendorgatewayConn)
	fireflyClient := fireflypb.NewFireflyClient(cardConn)
	accountingClient := ffaccountspb.NewAccountingClient(cardConn)
	externalVendorRedemptionServiceClient := evrpb.NewExternalVendorRedemptionServiceClient(growthinfraConn)
	casperRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["CasperRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = casperRedisStore.Close() }()
	offerRedemptionServiceClient := redemptionpb.NewOfferRedemptionServiceClient(growthinfraConn)
	questRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["QuestRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = questRedisStore.Close() }()
	questCacheStorage := cache.NewRedisCacheStorageWithHystrix(cache.NewRedisCacheStorage(questRedisStore), gconf.RedisClusters()["QuestRedisStore"].HystrixCommand)
	growthinfraConnVar16ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptor := servergenwire.NewRewardsRequestClientInterceptor()
	if unaryClientInterceptor != nil {
		growthinfraConnVar16ClientInterceptors = append(growthinfraConnVar16ClientInterceptors, unaryClientInterceptor)
	}
	growthinfraConnVar16 := epifigrpc.NewServerConn(cfg.GROWTH_INFRA_SERVER, cfg.GROWTH_INFRA_SERVER, growthinfraConnVar16ClientInterceptors...)
	defer epifigrpc.CloseConn(growthinfraConnVar16)
	commsClient := comms.NewCommsClient(growthinfraConnVar16)
	qwikcilverClient := qwikcilvervgpb.NewQwikcilverClient(vendorgatewayConn)
	dreamfolksClient := dreamfolksvgpb.NewDreamfolksClient(vendorgatewayConn)
	thriweClient := thriwevgpb.NewThriweClient(vendorgatewayConn)
	vistaraClient := vistaravgpb.NewVistaraClient(vendorgatewayConn)
	cmsServiceClient := cmspb.NewCmsServiceClient(growthinfraConn)
	rewardsGeneratorClient := rewardspb.NewRewardsGeneratorClient(growthinfraConn)
	docsConn := epifigrpc.NewServerConn(cfg.GROWTH_INFRA_SERVER, cfg.DOCS_SERVER)
	defer epifigrpc.CloseConn(docsConn)
	healthEngineServiceClient := healthenginepb.NewHealthEngineServiceClient(docsConn)
	orderConn := epifigrpc.NewServerConn(cfg.GROWTH_INFRA_SERVER, cfg.ORDER_SERVER)
	defer epifigrpc.CloseConn(orderConn)
	orderServiceClient := orderpb.NewOrderServiceClient(orderConn)
	payClient := paypb.NewPayClient(orderConn)
	timelineServiceClient := timelinepb.NewTimelineServiceClient(actorConn)
	accountPIRelationClient := accountpipb.NewAccountPIRelationClient(actorConn)
	billingClient := ffbillingpb.NewBillingClient(cardConn)
	operationalStatusServiceClient := operstatuspb.NewOperationalStatusServiceClient(actorConn)
	commsClientVar3 := comms.NewCommsClient(growthinfraConn)
	rewardsRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["RewardsRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = rewardsRedisStore.Close() }()
	luckyDrawServiceClient := luckydrawpb.NewLuckyDrawServiceClient(growthinfraConn)
	merchantServiceClient := merchantpb.NewMerchantServiceClient(actorConn)
	txnAggregatesClient := ffpinotpb.NewTxnAggregatesClient(cardConn)
	onboardingConn := epifigrpc.NewServerConn(cfg.GROWTH_INFRA_SERVER, cfg.ONBOARDING_SERVER)
	defer epifigrpc.CloseConn(onboardingConn)
	vKYCClient := vkycpb.NewVKYCClient(onboardingConn)
	tieringClient := tieringpb.NewTieringClient(centralgrowthConn)
	inAppReferralClient := inappreferralpb.NewInAppReferralClient(onboardingConn)
	salaryProgramClient := salaryprogrampb.NewSalaryProgramClient(centralgrowthConn)
	userIntelServiceClient := userintelpb.NewUserIntelServiceClient(onboardingConn)
	bankCustomerServiceClient := bankcust.NewBankCustomerServiceClient(onboardingConn)
	employmentClient := employmentpb.NewEmploymentClient(onboardingConn)
	growthinfraConnVar43ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar4 := servergenwire.NewRewardsRequestClientInterceptor()
	if unaryClientInterceptorVar4 != nil {
		growthinfraConnVar43ClientInterceptors = append(growthinfraConnVar43ClientInterceptors, unaryClientInterceptorVar4)
	}
	growthinfraConnVar43 := epifigrpc.NewServerConn(cfg.GROWTH_INFRA_SERVER, cfg.GROWTH_INFRA_SERVER, growthinfraConnVar43ClientInterceptors...)
	defer epifigrpc.CloseConn(growthinfraConnVar43)
	commsClientVar6 := comms.NewCommsClient(growthinfraConnVar43)
	cXClient := ordercxpb.NewCXClient(orderConn)
	paymentClient := txnpb.NewPaymentClient(orderConn)
	wealthdmfConn := epifigrpc.NewServerConn(cfg.GROWTH_INFRA_SERVER, cfg.WEALTH_DMF_SERVER)
	defer epifigrpc.CloseConn(wealthdmfConn)
	actionBarClient := searchpb.NewActionBarClient(wealthdmfConn)
	connectedAccountClient := capb.NewConnectedAccountClient(centralgrowthConn)
	fitttClient := fitttpb.NewFitttClient(wealthdmfConn)
	authClient := authpb.NewAuthClient(authConn)
	txnCategorizerClient := categorizerpb.NewTxnCategorizerClient(wealthdmfConn)
	exchangerOfferServiceClient := exchangerpb.NewExchangerOfferServiceClient(growthinfraConn)
	offerCatalogServiceClient := casperpb.NewOfferCatalogServiceClient(growthinfraConn)
	projectorServiceClient := rewardsprojector2.NewProjectorServiceClient(growthinfraConn)
	piClient := pipb.NewPiClient(actorConn)
	alfredClient := alfredpb.NewAlfredClient(onboardingConn)
	orderManagerClient := usstocksordermgpb.NewOrderManagerClient(wealthdmfConn)
	epfClient := epfpb.NewEpfClient(wealthdmfConn)
	depositClient := depositpb.NewDepositClient(wealthdmfConn)
	nudgeServiceClient := nudgepb.NewNudgeServiceClient(growthinfraConn)
	ussRewardManagerClient := ussrewardmgpb.NewUssRewardManagerClient(wealthdmfConn)
	accountsClient := vgaccountpb.NewAccountsClient(vendorgatewayConn)
	docsClient := docspb.NewDocsClient(docsConn)
	rewardOffersClient := rewardofferspb.NewRewardOffersClient(growthinfraConn)
	var analyticsDBConnTeardown func()
	analyticsDBResourceProvider, analyticsDBConnTeardown, err := storage2.NewAnalyticsDBResourceProvider(gconf.AnalyticsDBConfigMap().GetOwnershipToDbConfigMap())
	if err != nil {
		logger.Error(ctx, "failed to get db conn provider", zap.Error(err))
		return err
	}
	defer analyticsDBConnTeardown()
	sMSClient := smspb.NewSMSClient(vendorgatewayConn)
	emailClient := emailpb.NewEmailClient(vendorgatewayConn)
	fCMClient := fcmpb.NewFCMClient(vendorgatewayConn)
	whatsAppClient := whatsapppb.NewWhatsAppClient(vendorgatewayConn)
	growthinfraConnVar92ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar5 := servergenwire.NewCommsRequestClientInterceptor()
	if unaryClientInterceptorVar5 != nil {
		growthinfraConnVar92ClientInterceptors = append(growthinfraConnVar92ClientInterceptors, unaryClientInterceptorVar5)
	}
	growthinfraConnVar92 := epifigrpc.NewServerConn(cfg.GROWTH_INFRA_SERVER, cfg.GROWTH_INFRA_SERVER, growthinfraConnVar92ClientInterceptors...)
	defer epifigrpc.CloseConn(growthinfraConnVar92)
	userPreferenceClient := uppb.NewUserPreferenceClient(growthinfraConnVar92)
	commsRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["CommsRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = commsRedisStore.Close() }()
	cxConn := epifigrpc.NewServerConn(cfg.GROWTH_INFRA_SERVER, cfg.CX_SERVER)
	defer epifigrpc.CloseConn(cxConn)
	serveFAQClient := faqpb.NewServeFAQClient(cxConn)
	growthinfraConnVar97ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar2 := servergenwire.NewCommsRequestClientInterceptor()
	if unaryClientInterceptorVar2 != nil {
		growthinfraConnVar97ClientInterceptors = append(growthinfraConnVar97ClientInterceptors, unaryClientInterceptorVar2)
	}
	growthinfraConnVar97 := epifigrpc.NewServerConn(cfg.GROWTH_INFRA_SERVER, cfg.GROWTH_INFRA_SERVER, growthinfraConnVar97ClientInterceptors...)
	defer epifigrpc.CloseConn(growthinfraConnVar97)
	commsClientVar10 := comms.NewCommsClient(growthinfraConnVar97)
	growthinfraConnVar100ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar3 := servergenwire.NewCommsRequestClientInterceptor()
	if unaryClientInterceptorVar3 != nil {
		growthinfraConnVar100ClientInterceptors = append(growthinfraConnVar100ClientInterceptors, unaryClientInterceptorVar3)
	}
	growthinfraConnVar100 := epifigrpc.NewServerConn(cfg.GROWTH_INFRA_SERVER, cfg.GROWTH_INFRA_SERVER, growthinfraConnVar100ClientInterceptors...)
	defer epifigrpc.CloseConn(growthinfraConnVar100)
	inAppTargetedCommsClient := tcpb.NewInAppTargetedCommsClient(growthinfraConnVar100)
	notificationsClient := invnotificationpb.NewNotificationsClient(wealthdmfConn)
	vKYCFeClient := vkycpb.NewVKYCFeClient(onboardingConn)
	lendingConn := epifigrpc.NewServerConn(cfg.GROWTH_INFRA_SERVER, cfg.LENDING_SERVER)
	defer epifigrpc.CloseConn(lendingConn)
	preApprovedLoanClient := preapprovedloan.NewPreApprovedLoanClient(lendingConn)
	uSSDynamicElementsManagerClient := usstockspb.NewUSSDynamicElementsManagerClient(wealthdmfConn)
	userriskConn := epifigrpc.NewServerConn(cfg.GROWTH_INFRA_SERVER, cfg.USER_RISK_SERVER)
	defer epifigrpc.CloseConn(userriskConn)
	riskClient := riskpb.NewRiskClient(userriskConn)
	employmentFeClient := employmentpb.NewEmploymentFeClient(onboardingConn)
	complianceClient := compliancepb.NewComplianceClient(onboardingConn)
	dynamicElementsClient := dynamicelements.NewDynamicElementsClient(wealthdmfConn)
	netWorthClient := networth.NewNetWorthClient(wealthdmfConn)
	panClient := pan.NewPanClient(onboardingConn)
	crossAttachClient := crossattachpb.NewCrossAttachClient(onboardingConn)
	growthinfraConnVar104ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar6 := servergenwire.NewWhatsappBotRequestClientInterceptor()
	if unaryClientInterceptorVar6 != nil {
		growthinfraConnVar104ClientInterceptors = append(growthinfraConnVar104ClientInterceptors, unaryClientInterceptorVar6)
	}
	growthinfraConnVar104 := epifigrpc.NewServerConn(cfg.GROWTH_INFRA_SERVER, cfg.GROWTH_INFRA_SERVER, growthinfraConnVar104ClientInterceptors...)
	defer epifigrpc.CloseConn(growthinfraConnVar104)
	commsClientVar12 := comms.NewCommsClient(growthinfraConnVar104)
	growthinfraConnVar105ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar7 := servergenwire.NewWhatsappBotRequestClientInterceptor()
	if unaryClientInterceptorVar7 != nil {
		growthinfraConnVar105ClientInterceptors = append(growthinfraConnVar105ClientInterceptors, unaryClientInterceptorVar7)
	}
	growthinfraConnVar105 := epifigrpc.NewServerConn(cfg.GROWTH_INFRA_SERVER, cfg.GROWTH_INFRA_SERVER, growthinfraConnVar105ClientInterceptors...)
	defer epifigrpc.CloseConn(growthinfraConnVar105)
	userPreferenceClientVar6 := uppb.NewUserPreferenceClient(growthinfraConnVar105)
	nudgeRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["NudgeRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = nudgeRedisStore.Close() }()
	nudgeRankingRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["NudgeRankingRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = nudgeRankingRedisStore.Close() }()
	journeyServiceClient := journeypb.NewJourneyServiceClient(growthinfraConn)
	segmentRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["SegmentRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = segmentRedisStore.Close() }()
	segmentCacheStorage := cache.NewRedisCacheStorageWithHystrix(cache.NewRedisCacheStorage(segmentRedisStore), gconf.RedisClusters()["SegmentRedisStore"].HystrixCommand)
	var bigqueryConnTeardown func()
	bigqueryDBResourceProvider, bigqueryConnTeardown, err := storagev2analytics.NewBigqueryDBResourceProvider(ctx, gconf.BigqueryClientsMap().GetOwnerToClientConfigMap())
	if err != nil {
		logger.Error(ctx, "failed to get bigquery conn provider", zap.Error(err))
		return err
	}
	defer bigqueryConnTeardown()

	awsConf, awsErr := awsconfpkg.NewAWSConfig(ctx, gconf.AWS().Region, gconf.Tracing().Enable)
	if awsErr != nil {
		logger.Error(ctx, "error in loading aws v2 config", zap.Error(awsErr))
		if !cfg.IsRemoteDebugEnabled() {
			return err
		}
	}

	sqsClient := sqs.InitSQSClient(awsConf)
	_ = sqsClient

	var broker *events.RudderStackBroker

	// load rudder-stack broker for sending events
	rudderClient, err := analytics.NewWithConfig(
		gconf.Secrets().Ids[config.RudderWriteKey], "http://"+cfg.GetServerEndPoint(cfg.RUDDER_SERVER).URL(),
		analytics.Config{
			Callback:  events.NewCallback(),
			Interval:  gconf.RudderStack().IntervalInSec * time.Second, // nolint: durationcheck
			BatchSize: gconf.RudderStack().BatchSize,
			Verbose:   gconf.RudderStack().Verbose,
		})
	if err != nil {
		logger.Error(ctx, "error in connecting rudder", zap.Error(err))
		return err
	}
	broker = events.NewRudderStackBroker(rudderClient)
	defer broker.Close()

	rateLimiter := ratelimiter.NewRateLimiterV2(store.NewSlidingWindowLogWithRedis(rateLimiterRedisStore), gconf.GrpcRatelimiterParams().RateLimitConfig())
	_ = rateLimiter

	var unaryInterceptors []grpc.UnaryServerInterceptor

	unaryServerInterceptor := servergenwire.AddQuestUserContextUnaryInterceptor(gconf)
	if unaryServerInterceptor != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptor)
	}

	unaryServerInterceptorVar2, err := servergenwire2.CollapserInterceptor(collapserRueidisRedisStore, collapserRueidisCacheStorage)
	if err != nil {
		logger.Error(ctx, "failed to init grpc unary interceptor", zap.Error(err))
		return err
	}
	if unaryServerInterceptorVar2 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar2)
	}

	var streamInterceptors []grpc.StreamServerInterceptor

	histogramBuckets := prometheus.DefBuckets
	if len(gconf.LatencyHistogramBuckets()) > 0 {
		histogramBuckets = epifigrpc.AddGrpcLatencyBuckets(gconf.LatencyHistogramBuckets()...)
	}

	serverOptions := []grpc.ServerOption{}
	if gconf.GrpcServerConfig().MaxRecvMsgSize() != nil && gconf.GrpcServerConfig().MaxRecvMsgSize().Size != 0 {
		serverOptions = append(serverOptions, grpc.MaxRecvMsgSize(gconf.GrpcServerConfig().MaxRecvMsgSize().Size))
	}
	var extractFeRespStatusWithErrMsgFunc commoninterceptors.ExtractFeRespStatusWithErrMsgFunc
	extractFeRespStatusWithErrMsgFunc = fepkg.ExtractFeRespStatusWithErrMsgFunc

	s := epifigrpc.NewSecureServerWithInterceptors(gconf.GrpcServerConfig(), gconf.Flags().TrimDebugMessageFromStatus, string(gconf.Name()), histogramBuckets, unaryInterceptors, streamInterceptors, extractFeRespStatusWithErrMsgFunc, serverOptions...)

	if cfg.IsNonProdEnv(gconf.Environment()) {
		reflection.Register(s)
	}

	httpMux := http.NewServeMux()
	_ = httpMux

	var grpcWebUnaryInterceptors []grpc.UnaryServerInterceptor

	unaryServerInterceptorVar3, err := servergenwire.JarvisAuthInterceptor(ctx, gconf)
	if err != nil {
		logger.Error(ctx, "failed to init grpc web unary interceptor", zap.Error(err))
		return err
	}
	if unaryServerInterceptorVar3 != nil {
		grpcWebUnaryInterceptors = append(grpcWebUnaryInterceptors, unaryServerInterceptorVar3)
	}

	unaryServerInterceptorVar4 := servergenwire.AddQuestUserContextUnaryInterceptor(gconf)
	if unaryServerInterceptorVar4 != nil {
		grpcWebUnaryInterceptors = append(grpcWebUnaryInterceptors, unaryServerInterceptorVar4)
	}

	var grpcWebStreamInterceptors []grpc.StreamServerInterceptor

	grpcWebServer := epifigrpc.NewSecureServerWithInterceptors(gconf.GrpcServerConfig(), gconf.Flags().TrimDebugMessageFromStatus, string(gconf.Name()), prometheus.DefBuckets, grpcWebUnaryInterceptors, grpcWebStreamInterceptors, extractFeRespStatusWithErrMsgFunc)

	var sqsSubscribers []queue.Subscriber
	var subs []queue.Subscriber
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSubs []*sqs.ExtendedSqsSubscriber

	subs, extSubs, err = setupAccrual(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, accrualPGDB, accrualClient, savingsClient)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupCasper(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, casperPGDB, loyltyClient, segmentationServiceClient, vendorMappingServiceClient, cardProvisioningClient, usersClient, onboardingClient, poshvineClient, fireflyClient, accountingClient, externalVendorRedemptionServiceClient, casperRedisStore, groupClient, offerRedemptionServiceClient, actorClient, questCacheStorage, managerClient, commsClient, accrualClient, qwikcilverClient, dreamfolksClient, thriweClient, vistaraClient, cmsServiceClient, rewardsGeneratorClient, healthEngineServiceClient, orderServiceClient, payClient, timelineServiceClient, accountPIRelationClient, billingClient, savingsClient, operationalStatusServiceClient, commsClientVar3)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupCustomdelayqueue(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupRewards(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, rewardsPGDB, rewardsRedisStore, luckyDrawServiceClient, merchantServiceClient, accountingClient, billingClient, txnAggregatesClient, payClient, questCacheStorage, vKYCClient, tieringClient, cardProvisioningClient, inAppReferralClient, onboardingClient, orderServiceClient, salaryProgramClient, userIntelServiceClient, bankCustomerServiceClient, employmentClient, savingsClient, commsClientVar6, cXClient, paymentClient, accountPIRelationClient, actionBarClient, connectedAccountClient, fitttClient, authClient, offerRedemptionServiceClient, txnCategorizerClient, fireflyClient, exchangerOfferServiceClient, offerCatalogServiceClient, projectorServiceClient, piClient, alfredClient, orderManagerClient, epfClient, depositClient, nudgeServiceClient, healthEngineServiceClient, accrualClient, timelineServiceClient, ussRewardManagerClient, accountsClient, docsClient, rewardOffersClient, rewardsGeneratorClient)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupCms(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, cmsPGDB)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupQuest(ctx, s, grpcWebServer, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, questPGDB, questRedisStore, questCacheStorage, actorClient, usersClient, analyticsDBResourceProvider, segmentationServiceClient, managerClient, groupClient)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupComms(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, sMSClient, emailClient, fCMClient, whatsAppClient, userPreferenceClient, commsRedisStore, authClient, commsPGDB, onboardingClient, serveFAQClient, commsClientVar10)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupDynamicelements(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, questCacheStorage, inAppTargetedCommsClient, notificationsClient, vKYCFeClient, savingsClient, preApprovedLoanClient, fireflyClient, uSSDynamicElementsManagerClient, tieringClient, riskClient, payClient, employmentFeClient, offerCatalogServiceClient, complianceClient, salaryProgramClient, bankCustomerServiceClient, onboardingClient, dynamicElementsClient, netWorthClient, connectedAccountClient, panClient, cardProvisioningClient, crossAttachClient)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupWhatsappbot(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, commsClientVar12, actorClient, usersClient, userPreferenceClientVar6, onboardingClient)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupNudge(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, nudgePGDB, nudgeRedisStore, nudgeRankingRedisStore, onboardingClient, payClient, journeyServiceClient, questCacheStorage, connectedAccountClient, rewardsGeneratorClient, nudgeServiceClient, orderServiceClient)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupSegment(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, segmentRedisStore, segmentCacheStorage, segmentPGDB, analyticsDBResourceProvider, bigqueryDBResourceProvider)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	explorerpb.RegisterConfigExplorerServer(s, explorer.NewServer(configNameToConfMap))

	cleanupFn, err := hook.InitCustomDelayQueueService(ctx, gconf) // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "InitCustomDelayQueueService"), zap.Error(err))
		return err
	}
	defer cleanupFn()

	cleanupFnVar2, err := hook.InitCommsServer(s, epifiCRDB) // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "InitCommsServer"), zap.Error(err))
		return err
	}
	defer cleanupFnVar2()

	servermetrics.ResetHookInitFailureToZero(cfg.GROWTH_INFRA_SERVER, "RegisterProcessRudderEventMethodToSubscriber")
	cleanupFnVar3, err := hook.RegisterProcessRudderEventMethodToSubscriber(ctx, awsConf, epifiCRDB) // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "RegisterProcessRudderEventMethodToSubscriber"), zap.Error(err))
		servermetrics.RecordHookInitFailure(cfg.GROWTH_INFRA_SERVER, "RegisterProcessRudderEventMethodToSubscriber")
	}
	defer cleanupFnVar3()

	cleanupFnVar4, err := hook.LoadJarvisAuthDescriptors(grpcWebServer) // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "LoadJarvisAuthDescriptors"), zap.Error(err))
		return err
	}
	defer cleanupFnVar4()

	cleanupFnVar5, err := servergenwire2.LoadCollapserWithGrpcServer(gconf, s) // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "LoadCollapserWithGrpcServer"), zap.Error(err))
		return err
	}
	defer cleanupFnVar5()

	// Load rpc level log sampling rate descriptors.
	epifigrpc.GetRPCLogSamplingRatesMapInstance().Load(s)

	initNotifier <- gconf.Name()

	// TODO(Sundeep): Remove this additional logic for starting the servers inside go-routine, by exposing functions
	// from epifiserver package that take errgroup.Group as parameter.
	grp, _ := errgroup.WithContext(context.Background())

	grp.Go(func() error {
		epifiserver.StartSecureServerWithGrpcWeb(s, grpcWebServer, gconf.ServerPorts(), conf.HttpCorsOptions, string(gconf.Name()))
		return nil
	})

	// Wait for server health before starting SQS workers
	if queue.IsWorkerInitializationEnabled() {
		goroutine.RunWithCtx(ctx, func(ctx context.Context) {
			if epifiserver.IsServerReady(gconf.ServerPorts().HttpPort) {
				logger.Info(ctx, "Server is healthy, starting SQS workers")
				sqs.StartSQSWorkers(ctx, sqsSubscribers, extendedSqsSubscribers)
			} else {
				logger.PanicWithCtx(ctx, "Server health check timed out, not starting SQS workers")
			}
		})
	}
	if err := grp.Wait(); err != nil {
		return err
	}

	return nil
}

// nolint: funlen
func setupAccrual(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	accrualPGDB types.AccrualPGDB,
	accrualClient accrualpb.AccrualClient,
	savingsClient savingspb.SavingsClient) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	accrualConf, err := accrualconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.ACCRUAL_SERVICE))
		return nil, nil, err
	}
	_ = accrualConf

	accrualGenConf, err := dynconf.LoadConfig(accrualconf.Load, accrualgenconf.NewConfig, cfg.ACCRUAL_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.ACCRUAL_SERVICE))
		return nil, nil, err
	}

	_ = accrualGenConf

	service := wire.InitializeService(accrualPGDB)

	accrualpb.RegisterAccrualServer(s, service)

	accrualConsumerService := wire.InitializeAccrualConsumerService(accrualClient, savingsClient)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, accrualGenConf.OperationalStatusUpdateEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		accrualconsumer.RegisterProcessAccountStatusUpdateEventMethodToSubscriber(subscriber, accrualConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}
	configNameToConfMap[cfg.ConfigName(cfg.ACCRUAL_SERVICE)] = &commonexplorer.Config{StaticConf: &accrualconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupCasper(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	casperPGDB types.CasperPGDB,
	loyltyClient loyltypb.LoyltyClient,
	segmentationServiceClient segmentpb.SegmentationServiceClient,
	vendorMappingServiceClient vmpb.VendorMappingServiceClient,
	cardProvisioningClient provisioning.CardProvisioningClient,
	usersClient user.UsersClient,
	onboardingClient onboardingpb.OnboardingClient,
	poshvineClient poshvinevgpb.PoshvineClient,
	fireflyClient fireflypb.FireflyClient,
	accountingClient ffaccountspb.AccountingClient,
	externalVendorRedemptionServiceClient evrpb.ExternalVendorRedemptionServiceClient,
	casperRedisStore types2.CasperRedisStore,
	groupClient usergrouppb.GroupClient,
	offerRedemptionServiceClient redemptionpb.OfferRedemptionServiceClient,
	actorClient actor.ActorClient,
	questCacheStorage types.QuestCacheStorage,
	managerClient managerpb.ManagerClient,
	commsClient types2.CasperCommsClientWithInterceptors,
	accrualClient accrualpb.AccrualClient,
	qwikcilverClient qwikcilvervgpb.QwikcilverClient,
	dreamfolksClient dreamfolksvgpb.DreamfolksClient,
	thriweClient thriwevgpb.ThriweClient,
	vistaraClient vistaravgpb.VistaraClient,
	cmsServiceClient cmspb.CmsServiceClient,
	rewardsGeneratorClient rewardspb.RewardsGeneratorClient,
	healthEngineServiceClient healthenginepb.HealthEngineServiceClient,
	orderServiceClient orderpb.OrderServiceClient,
	payClient paypb.PayClient,
	timelineServiceClient timelinepb.TimelineServiceClient,
	accountPIRelationClient accountpipb.AccountPIRelationClient,
	billingClient ffbillingpb.BillingClient,
	savingsClient savingspb.SavingsClient,
	operationalStatusServiceClient operstatuspb.OperationalStatusServiceClient,
	commsClientVar3 comms.CommsClient) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	casperConf, err := casperconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.CASPER_SERVICE))
		return nil, nil, err
	}
	_ = casperConf

	casperGenConf, err := dynconf.LoadConfig(casperconf.Load, genconf2.NewConfig, cfg.CASPER_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.CASPER_SERVICE))
		return nil, nil, err
	}

	_ = casperGenConf

	retryRedemptionEventsSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, casperGenConf.RetryRedemptionEventsSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	exchangerOrderOrchestrationEventsSqsDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, casperGenConf.ExchangerOrderOrchestrationEventsSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	exchangerOrderOrchestrationEventsSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, casperGenConf.ExchangerOrderOrchestrationEventsSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	fiStoreOrderNotificationSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, casperGenConf.FiStoreOrderNotificationSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	exchangerOrderOrchestrationEventsSqsPublisherDlq, err := sqs.NewPublisherWithConfig(ctx, casperGenConf.ExchangerOrderOrchestrationEventsSqsPublisherDlq(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}

	exchangerOrderNotificationCustomDelayPublisher, err := customqueuewire.InitializeSqsCustomDelayQueuePublisher(ctx,
		casperGenConf.ExchangerOrderNotificationCustomDelayPublisher().OrchestratorSqsPublisher,
		customdelayqueue.QueueName(casperGenConf.ExchangerOrderNotificationCustomDelayPublisher().GetDestQueueName()),
		sqsClient, queue.NewDefaultMessage(),
	)
	if err != nil {
		logger.Error(ctx, "failed to create custom sqs publisher", zap.Error(err))
		return nil, nil, err
	}

	offerRedemptionStatusUpdateEventsSnsPublisher, err := sns.NewSnsPublisherWithConfig(ctx, casperGenConf.OfferRedemptionStatusUpdateEventsSnsPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}

	offerCatalogService := wire2.InitializeOfferCatalogService(casperPGDB, loyltyClient, segmentationServiceClient, vendorMappingServiceClient, cardProvisioningClient, usersClient, onboardingClient, poshvineClient, fireflyClient, accountingClient, externalVendorRedemptionServiceClient, casperConf, casperGenConf, casperRedisStore, groupClient)

	casperpb.RegisterOfferCatalogServiceServer(s, offerCatalogService)

	offerInventoryService := wire2.InitializeOfferInventoryService(casperPGDB, casperGenConf, casperRedisStore)

	casperpb.RegisterOfferInventoryServiceServer(s, offerInventoryService)

	offerListingService := wire2.InitializeOfferListingService(casperPGDB, loyltyClient, offerRedemptionServiceClient, actorClient, usersClient, segmentationServiceClient, groupClient, questCacheStorage, managerClient, vendorMappingServiceClient, cardProvisioningClient, onboardingClient, poshvineClient, fireflyClient, accountingClient, externalVendorRedemptionServiceClient, broker, casperConf, casperGenConf, casperRedisStore)

	casperpb.RegisterOfferListingServiceServer(s, offerListingService)

	serviceVar2, err := wire2.InitializeRedemptionService(casperPGDB, awsConf, commsClient, accrualClient, vendorMappingServiceClient, casperGenConf, loyltyClient, qwikcilverClient, dreamfolksClient, thriweClient, usersClient, actorClient, broker, casperConf, retryRedemptionEventsSqsPublisher, groupClient, vistaraClient, cmsServiceClient, offerRedemptionStatusUpdateEventsSnsPublisher, rewardsGeneratorClient, segmentationServiceClient, cardProvisioningClient, onboardingClient, poshvineClient, fireflyClient, accountingClient, externalVendorRedemptionServiceClient, casperRedisStore)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	redemptionpb.RegisterOfferRedemptionServiceServer(s, serviceVar2)

	consumerService := wire2.InitializeRedemptionConsumerService(casperPGDB, awsConf, commsClient, accrualClient, vendorMappingServiceClient, casperGenConf, loyltyClient, qwikcilverClient, dreamfolksClient, thriweClient, usersClient, actorClient, broker, casperConf, groupClient, cmsServiceClient, offerRedemptionStatusUpdateEventsSnsPublisher, rewardsGeneratorClient, segmentationServiceClient, cardProvisioningClient, onboardingClient, poshvineClient, fireflyClient, accountingClient, externalVendorRedemptionServiceClient, casperRedisStore)

	redemptionpb.RegisterConsumerServer(s, consumerService)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, casperGenConf.RetryRedemptionEventsSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		redemptionpb.RegisterProcessRetryRedemptionEventMethodToSubscriber(subscriber, consumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	serviceVar3 := wire2.InitializeExchangerOfferService(casperPGDB, healthEngineServiceClient, accrualClient, orderServiceClient, payClient, actorClient, timelineServiceClient, accountPIRelationClient, casperConf, casperGenConf, exchangerOrderOrchestrationEventsSqsDelayPublisher, vendorMappingServiceClient, exchangerOrderNotificationCustomDelayPublisher, exchangerOrderOrchestrationEventsSqsPublisher, groupClient, usersClient, broker, awsConf, loyltyClient, qwikcilverClient, dreamfolksClient, thriweClient, cmsServiceClient, offerRedemptionStatusUpdateEventsSnsPublisher, rewardsGeneratorClient, segmentationServiceClient, questCacheStorage, managerClient, billingClient, onboardingClient, savingsClient, operationalStatusServiceClient, fireflyClient)

	exchangerpb.RegisterExchangerOfferServiceServer(s, serviceVar3)

	casperDevService := wire2.InitializeDevService(casperPGDB, loyltyClient, qwikcilverClient, casperRedisStore, casperGenConf)

	casperdeveloperpb.RegisterCasperDevServer(s, casperDevService)

	casperJobHelperService := wire2.InitializeCasperJobHelperService(casperPGDB)

	casperjobhelperpb.RegisterCasperJobHelperServer(s, casperJobHelperService)

	serviceVar4 := wire2.InitializeDiscountsService(casperPGDB)

	discountspb.RegisterDiscountServiceServer(s, serviceVar4)

	serviceVar5, err := wire2.InitializeExternalVendorRedemptionService(casperPGDB, accrualClient, fireflyClient, fiStoreOrderNotificationSqsPublisher, broker, casperGenConf)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	evrpb.RegisterExternalVendorRedemptionServiceServer(s, serviceVar5)

	serviceVar6 := wire2.InitializeVistaraConsumerService(casperPGDB, vistaraClient, retryRedemptionEventsSqsPublisher, awsConf, casperGenConf)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, casperGenConf.VistaraUploadFileSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		vistara.RegisterProcessVistaraAirMilesTransferUpstreamMethodToSubscriber(subscriber, serviceVar6)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, casperGenConf.VistaraDownloadFileSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		vistara.RegisterProcessVistaraAirMilesTransferDownstreamMethodToSubscriber(subscriber, serviceVar6)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	serviceVar7 := wire2.InitializeItcConsumerService(casperPGDB, commsClientVar3, retryRedemptionEventsSqsPublisher, awsConf, casperGenConf)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, casperGenConf.ItcUploadFileSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		itc.RegisterProcessClubItcGreenPointsTransferUpstreamMethodToSubscriber(subscriber, serviceVar7)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, casperGenConf.ItcDownloadFileSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		itc.RegisterProcessClubItcGreenPointsTransferDownstreamMethodToSubscriber(subscriber, serviceVar7)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	serviceVar8 := wire2.InitializeExchangerConsumerService(casperPGDB, healthEngineServiceClient, accrualClient, orderServiceClient, payClient, actorClient, commsClient, groupClient, usersClient, timelineServiceClient, accountPIRelationClient, vendorMappingServiceClient, broker, exchangerOrderNotificationCustomDelayPublisher, exchangerOrderOrchestrationEventsSqsPublisherDlq, casperConf, casperGenConf, awsConf, loyltyClient, qwikcilverClient, dreamfolksClient, thriweClient, cmsServiceClient, offerRedemptionStatusUpdateEventsSnsPublisher, rewardsGeneratorClient, billingClient, onboardingClient, casperRedisStore, savingsClient, fireflyClient)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, casperGenConf.ExchangerOrderOrchestrationEventsSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		exchangerpb.RegisterPerformExchangerOfferOrderOrchestrationMethodToSubscriber(subscriber, serviceVar8)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, casperGenConf.ExchangerOrderOrchestrationEventsSqsSubscriberDlq(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		exchangerpb.RegisterPerformExchangerOfferOrderOrchestrationDlqMethodToSubscriber(subscriber, serviceVar8)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, casperGenConf.ExchangerOrderNotificationSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		exchangerpb.RegisterProcessExchangerOrderNotificationEventMethodToSubscriber(subscriber, serviceVar8)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	notificationConsumerService := wire2.InitializeNotificationConsumerService(casperPGDB, actorClient, commsClient, vendorMappingServiceClient, casperGenConf)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, casperGenConf.FiStoreOrderNotificationSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		notificationpb.RegisterProcessFiStoreOrderNotificationEventMethodToSubscriber(subscriber, notificationConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}
	configNameToConfMap[cfg.ConfigName(cfg.CASPER_SERVICE)] = &commonexplorer.Config{StaticConf: &casperconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupCustomdelayqueue(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	customdelayqueueConf, err := customdelayqueueconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.CUSTOM_DELAY_QUEUE_SERVICE))
		return nil, nil, err
	}
	_ = customdelayqueueConf

	configNameToConfMap[cfg.ConfigName(cfg.CUSTOM_DELAY_QUEUE_SERVICE)] = &commonexplorer.Config{StaticConf: &customdelayqueueconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupRewards(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	managerClient managerpb.ManagerClient,
	groupClient usergrouppb.GroupClient,
	usersClient user.UsersClient,
	actorClient actor.ActorClient,
	segmentationServiceClient segmentpb.SegmentationServiceClient,
	rewardsPGDB types.RewardsPGDB,
	rewardsRedisStore types3.RewardsRedisStore,
	luckyDrawServiceClient luckydrawpb.LuckyDrawServiceClient,
	merchantServiceClient merchantpb.MerchantServiceClient,
	accountingClient ffaccountspb.AccountingClient,
	billingClient ffbillingpb.BillingClient,
	txnAggregatesClient ffpinotpb.TxnAggregatesClient,
	payClient paypb.PayClient,
	questCacheStorage types3.QuestCacheStorage,
	vKYCClient vkycpb.VKYCClient,
	tieringClient tieringpb.TieringClient,
	cardProvisioningClient provisioning.CardProvisioningClient,
	inAppReferralClient inappreferralpb.InAppReferralClient,
	onboardingClient onboardingpb.OnboardingClient,
	orderServiceClient orderpb.OrderServiceClient,
	salaryProgramClient salaryprogrampb.SalaryProgramClient,
	userIntelServiceClient userintelpb.UserIntelServiceClient,
	bankCustomerServiceClient bankcust.BankCustomerServiceClient,
	employmentClient employmentpb.EmploymentClient,
	savingsClient savingspb.SavingsClient,
	commsClientVar6 types3.RewardsCommsClientWithInterceptors,
	cXClient ordercxpb.CXClient,
	paymentClient txnpb.PaymentClient,
	accountPIRelationClient accountpipb.AccountPIRelationClient,
	actionBarClient searchpb.ActionBarClient,
	connectedAccountClient capb.ConnectedAccountClient,
	fitttClient fitttpb.FitttClient,
	authClient authpb.AuthClient,
	offerRedemptionServiceClient redemptionpb.OfferRedemptionServiceClient,
	txnCategorizerClient categorizerpb.TxnCategorizerClient,
	fireflyClient fireflypb.FireflyClient,
	exchangerOfferServiceClient exchangerpb.ExchangerOfferServiceClient,
	offerCatalogServiceClient casperpb.OfferCatalogServiceClient,
	projectorServiceClient rewardsprojector2.ProjectorServiceClient,
	piClient pipb.PiClient,
	alfredClient alfredpb.AlfredClient,
	orderManagerClient usstocksordermgpb.OrderManagerClient,
	epfClient epfpb.EpfClient,
	depositClient depositpb.DepositClient,
	nudgeServiceClient nudgepb.NudgeServiceClient,
	healthEngineServiceClient healthenginepb.HealthEngineServiceClient,
	accrualClient accrualpb.AccrualClient,
	timelineServiceClient timelinepb.TimelineServiceClient,
	ussRewardManagerClient ussrewardmgpb.UssRewardManagerClient,
	accountsClient vgaccountpb.AccountsClient,
	docsClient docspb.DocsClient,
	rewardOffersClient rewardofferspb.RewardOffersClient,
	rewardsGeneratorClient rewardspb.RewardsGeneratorClient) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	rewardsConf, err := rewardsconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.REWARD_SERVICE))
		return nil, nil, err
	}
	_ = rewardsConf

	rewardsGenConf, err := dynconf.LoadConfigWithQuestConfig(rewardsconf.Load, genconf3.NewConfigWithQuest, cfg.REWARD_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.REWARD_SERVICE))
		return nil, nil, err
	}

	if !conf.QuestSdk.Disable {
		rewardsGenConfAppConfig := questsdkinit.QuestSdkAppConfig{AppConfig: rewardsGenConf, SdkConfig: rewardsGenConf.QuestSdk()}
		questsdkinit.SetupQuestSDK([]questsdkinit.QuestSdkAppConfig{rewardsGenConfAppConfig}, string(cfg.GROWTH_INFRA_SERVER), managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, broker)
	}

	_ = rewardsGenConf

	rewardsSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, rewardsGenConf.RewardsSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	rewardsManualGiveawayEventSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, rewardsGenConf.RewardsManualGiveawayEventSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	bulkClaimRewardsEventSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, rewardsGenConf.BulkClaimRewardsEventSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	luckyDrawWinningProcessingSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, rewardsGenConf.LuckyDrawWinningProcessingSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	rewardsRewardUnlockerSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, rewardsGenConf.RewardsRewardUnlockerSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	rewardClawbackProcessingSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, rewardsGenConf.RewardClawbackProcessingSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	dataCollectorEventSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, rewardsGenConf.DataCollectorEventSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	rewardClawbackEventCollectedDataPublisher, err := sqs.NewPublisherWithConfig(ctx, rewardsGenConf.RewardClawbackEventCollectedDataPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	rewardsProjectionsGenerationEventSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, rewardsGenConf.RewardsProjectionsGenerationEventSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	rewardClawbackProcessingSqsDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, rewardsGenConf.RewardClawbackProcessingSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	luckyDrawWinningProcessingSqsDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, rewardsGenConf.LuckyDrawWinningProcessingSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	rewardsProjectionUpdateEventSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, rewardsGenConf.RewardsProjectionUpdateEventSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	rewardsOrderUpdateEventQueueSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, rewardsGenConf.RewardsOrderUpdateEventQueueSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	rewardsCreditCardTxnEventQueueSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, rewardsGenConf.RewardsCreditCardTxnEventQueueSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	rewardsCreditCardBillingEventQueueSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, rewardsGenConf.RewardsCreditCardBillingEventQueueSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}

	rewardsProcessingSqsCustomDelayPublisher, err := customqueuewire.InitializeSqsCustomDelayQueuePublisher(ctx,
		rewardsGenConf.RewardsProcessingSqsCustomDelayPublisher().OrchestratorSqsPublisher,
		customdelayqueue.QueueName(rewardsGenConf.RewardsProcessingSqsCustomDelayPublisher().GetDestQueueName()),
		sqsClient, queue.NewDefaultMessage(),
	)
	if err != nil {
		logger.Error(ctx, "failed to create custom sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	dataCollectorEventSqsCustomDelayPublisher, err := customqueuewire.InitializeSqsCustomDelayQueuePublisher(ctx,
		rewardsGenConf.DataCollectorEventSqsCustomDelayPublisher().OrchestratorSqsPublisher,
		customdelayqueue.QueueName(rewardsGenConf.DataCollectorEventSqsCustomDelayPublisher().GetDestQueueName()),
		sqsClient, queue.NewDefaultMessage(),
	)
	if err != nil {
		logger.Error(ctx, "failed to create custom sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	rewardsClawbackEventCollectedDataCustomDelayPublisher, err := customqueuewire.InitializeSqsCustomDelayQueuePublisher(ctx,
		rewardsGenConf.RewardsClawbackEventCollectedDataCustomDelayPublisher().OrchestratorSqsPublisher,
		customdelayqueue.QueueName(rewardsGenConf.RewardsClawbackEventCollectedDataCustomDelayPublisher().GetDestQueueName()),
		sqsClient, queue.NewDefaultMessage(),
	)
	if err != nil {
		logger.Error(ctx, "failed to create custom sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	rewardsNotificationEventSqsCustomDelayPublisher, err := customqueuewire.InitializeSqsCustomDelayQueuePublisher(ctx,
		rewardsGenConf.RewardsNotificationEventSqsCustomDelayPublisher().OrchestratorSqsPublisher,
		customdelayqueue.QueueName(rewardsGenConf.RewardsNotificationEventSqsCustomDelayPublisher().GetDestQueueName()),
		sqsClient, queue.NewDefaultMessage(),
	)
	if err != nil {
		logger.Error(ctx, "failed to create custom sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	rewardsClaimRewardEventSqsCustomDelayPublisher, err := customqueuewire.InitializeSqsCustomDelayQueuePublisher(ctx,
		rewardsGenConf.RewardsClaimRewardEventSqsCustomDelayPublisher().OrchestratorSqsPublisher,
		customdelayqueue.QueueName(rewardsGenConf.RewardsClaimRewardEventSqsCustomDelayPublisher().GetDestQueueName()),
		sqsClient, queue.NewDefaultMessage(),
	)
	if err != nil {
		logger.Error(ctx, "failed to create custom sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	rewardsRewardUnlockerSqsCustomDelayPublisher, err := customqueuewire.InitializeSqsCustomDelayQueuePublisher(ctx,
		rewardsGenConf.RewardsRewardUnlockerSqsCustomDelayPublisher().OrchestratorSqsPublisher,
		customdelayqueue.QueueName(rewardsGenConf.RewardsRewardUnlockerSqsCustomDelayPublisher().GetDestQueueName()),
		sqsClient, queue.NewDefaultMessage(),
	)
	if err != nil {
		logger.Error(ctx, "failed to create custom sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	rewardsRewardExpirySqsCustomDelayPublisher, err := customqueuewire.InitializeSqsCustomDelayQueuePublisher(ctx,
		rewardsGenConf.RewardsRewardExpirySqsCustomDelayPublisher().OrchestratorSqsPublisher,
		customdelayqueue.QueueName(rewardsGenConf.RewardsRewardExpirySqsCustomDelayPublisher().GetDestQueueName()),
		sqsClient, queue.NewDefaultMessage(),
	)
	if err != nil {
		logger.Error(ctx, "failed to create custom sqs publisher", zap.Error(err))
		return nil, nil, err
	}

	rewardStatusUpdateEventSnsPublisher, err := sns.NewSnsPublisherWithConfig(ctx, rewardsGenConf.RewardStatusUpdateEventSnsPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}
	projectionEventSnsPublisher, err := sns.NewSnsPublisherWithConfig(ctx, rewardsGenConf.ProjectionEventSnsPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}
	rewardGenerationEventSnsPublisher, err := sns.NewSnsPublisherWithConfig(ctx, rewardsGenConf.RewardGenerationEventSnsPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}

	rewardsS3Client := s3pkg.NewClient(awsConf, rewardsGenConf.RewardsBucketName())
	rewardsS3ClientVar2 := s3pkg.NewClient(awsConf, rewardsGenConf.RewardsBucketName())
	rewardsS3ClientVar3 := s3pkg.NewClient(awsConf, rewardsGenConf.RewardsBucketName())
	rewardsS3ClientVar4 := s3pkg.NewClient(awsConf, rewardsGenConf.RewardsBucketName())

	rewardsService := wire3.InitializeRewardsGeneratorService(rewardsPGDB, rewardsRedisStore, rewardsSqsPublisher, rewardsProcessingSqsCustomDelayPublisher, rewardsManualGiveawayEventSqsPublisher, bulkClaimRewardsEventSqsPublisher, rewardsGenConf, broker, luckyDrawServiceClient, merchantServiceClient, accountingClient, billingClient, txnAggregatesClient, payClient, rewardsConf)

	rewardspb.RegisterRewardsGeneratorServer(s, rewardsService)

	rewardOfferService := wire3.InitializeRewardOfferService(rewardsPGDB, rewardsRedisStore, questCacheStorage, actorClient, usersClient, vKYCClient, tieringClient, cardProvisioningClient, groupClient, inAppReferralClient, onboardingClient, orderServiceClient, salaryProgramClient, segmentationServiceClient, userIntelServiceClient, rewardsConf, rewardsGenConf, awsConf, merchantServiceClient, bankCustomerServiceClient, employmentClient, managerClient, broker, savingsClient)

	rewardofferspb.RegisterRewardOffersServer(s, rewardOfferService)

	luckyDrawService := wire3.InitializeLuckyDrawService(rewardsPGDB, luckyDrawWinningProcessingSqsPublisher)

	luckydrawpb.RegisterLuckyDrawServiceServer(s, luckyDrawService)

	rewardsJobHelperService := wire3.InitializeRewardsJobHelperService(rewardsPGDB)

	rewardsjobhelperpb.RegisterRewardsJobHelperServer(s, rewardsJobHelperService)

	rewardsDevService := wire3.InitializeDevService(rewardsPGDB, rewardsRedisStore)

	rewardsdeveloperpb.RegisterRewardsDevServer(s, rewardsDevService)

	serviceVar9 := wire3.InitializeRewardCampaignCommService(rewardsPGDB, rewardsRedisStore, actorClient, commsClientVar6, orderServiceClient, savingsClient, rewardsConf)

	campaigncommpb.RegisterRewardsCampaignCommServer(s, serviceVar9)

	rewardsProjectionService := wire3.InitialiseRewardsProjectionService(rewardsPGDB)

	rewardsprojector2.RegisterProjectorServiceServer(s, rewardsProjectionService)

	serviceVar10 := wire3.InitializeUnlockerConsumerService(rewardsPGDB, orderServiceClient, cXClient, merchantServiceClient, paymentClient, accountPIRelationClient, actorClient, usersClient, groupClient, tieringClient, inAppReferralClient, onboardingClient, cardProvisioningClient, actionBarClient, connectedAccountClient, vKYCClient, fitttClient, luckyDrawServiceClient, salaryProgramClient, segmentationServiceClient, userIntelServiceClient, employmentClient, rewardsRedisStore, rewardsRewardUnlockerSqsPublisher, bankCustomerServiceClient, accountingClient, savingsClient, authClient, offerRedemptionServiceClient, txnCategorizerClient, payClient, fireflyClient, billingClient, txnAggregatesClient, exchangerOfferServiceClient, offerCatalogServiceClient, projectorServiceClient, piClient, rewardsSqsPublisher, rewardsConf, rewardsGenConf, alfredClient, orderManagerClient, epfClient, managerClient, questCacheStorage, broker, rewardsS3Client)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.RewardsRewardUnlockerSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		unlocker2.RegisterProcessUnlockEventMethodToSubscriber(subscriber, serviceVar10)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	serviceVar11 := wire3.InitializeExpiryConsumerService(rewardsPGDB, rewardsRedisStore)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.RewardsRewardExpirySqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		expiry2.RegisterProcessExpiryEventMethodToSubscriber(subscriber, serviceVar11)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	consumerServiceVar2 := wire3.InitializeClawbackConsumerService(rewardsPGDB, rewardsRedisStore, accountingClient, merchantServiceClient, billingClient, txnAggregatesClient, rewardsConf, rewardsGenConf, rewardClawbackProcessingSqsPublisher)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.RewardClawbackEventCollectorSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		clawbackprocessor.RegisterProcessClawbackEventMethodToSubscriber(subscriber, consumerServiceVar2)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	rewardsConsumerService, err := wire3.InitializeRewardsConsumerService(dataCollectorEventSqsPublisher, dataCollectorEventSqsCustomDelayPublisher, actorClient, fitttClient, depositClient, connectedAccountClient, rewardClawbackEventCollectedDataPublisher, rewardsClawbackEventCollectedDataCustomDelayPublisher, accountingClient, txnCategorizerClient, onboardingClient, usersClient, piClient, rewardsRewardUnlockerSqsPublisher, rewardsProjectionsGenerationEventSqsPublisher, rewardsConf, rewardsGenConf, cardProvisioningClient, savingsClient, nudgeServiceClient, rewardsRedisStore, groupClient, tieringClient, inAppReferralClient, orderServiceClient, salaryProgramClient, vKYCClient, segmentationServiceClient, userIntelServiceClient, employmentClient, bankCustomerServiceClient, rewardsPGDB)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.OrderUpdateSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		datacollector.RegisterProcessOrderUpdateEventsMethodToSubscriber(subscriber, rewardsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.UserSearchSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		datacollector.RegisterProcessUserSearchEventsMethodToSubscriber(subscriber, rewardsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.UserSearchV1SqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		datacollector.RegisterProcessUserSearchEventV1MethodToSubscriber(subscriber, rewardsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.ManualGiveawayEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		datacollector.RegisterProcessManualGiveawayEventsMethodToSubscriber(subscriber, rewardsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.FitttExecutionUpdateSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		datacollector.RegisterProcessFitttActionExecutionUpdateEventsMethodToSubscriber(subscriber, rewardsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.ExtraInterestSdBonusPayoutSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		datacollector.RegisterProcessExtraInterestSdBonusPayoutEventsMethodToSubscriber(subscriber, rewardsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.MinBalanceSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		datacollector.RegisterProcessMinBalanceEventMethodToSubscriber(subscriber, rewardsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.CAAccountUpdateEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		datacollector.RegisterProcessCAAccountUpdateEventMethodToSubscriber(subscriber, rewardsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.KYCSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		datacollector.RegisterProcessKYCEventMethodToSubscriber(subscriber, rewardsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.FitttSportsEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		datacollector.RegisterProcessFitttSportsEventMethodToSubscriber(subscriber, rewardsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.SavingsAccountStateUpdateSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		datacollector.RegisterProcessSavingsAccountStateUpdateEventMethodToSubscriber(subscriber, rewardsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.SalaryDetectionSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		datacollector.RegisterProcessSalaryDetectionEventMethodToSubscriber(subscriber, rewardsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.SalaryProgramStatusUpdateSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		datacollector.RegisterProcessSalaryProgramStatusUpdateEventMethodToSubscriber(subscriber, rewardsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.OnboardingStageUpdateSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		datacollector.RegisterProcessOnboardingStageUpdateEventMethodToSubscriber(subscriber, rewardsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.CreditCardTransactionsSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		datacollector.RegisterProcessCreditCardTransactionEventMethodToSubscriber(subscriber, rewardsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.CreditCardRequestStageUpdateSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		datacollector.RegisterProcessCreditCardRequestStageUpdateEventMethodToSubscriber(subscriber, rewardsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.CreditCardBillGenerationEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		datacollector.RegisterProcessCreditCardBillingEventMethodToSubscriber(subscriber, rewardsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.CreditReportDownloadEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		datacollector.RegisterProcessCreditReportDownloadEventMethodToSubscriber(subscriber, rewardsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.RewardsOfferRedemptionStatusUpdateEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		datacollector.RegisterProcessOfferRedemptionStatusUpdateEventMethodToSubscriber(subscriber, rewardsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.InvestmentEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		datacollector.RegisterProcessInvestmentRetentionRewardEventMethodToSubscriber(subscriber, rewardsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.TieringTierUpdateEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		datacollector.RegisterProcessTieringTierUpdateEventMethodToSubscriber(subscriber, rewardsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.DebitCardSwitchNotificationSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		datacollector.RegisterProcessDebitCardSwitchNotificationEventMethodToSubscriber(subscriber, rewardsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.EpfPassbookImportEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		datacollector.RegisterProcessEpfPassbookImportEventMethodToSubscriber(subscriber, rewardsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.CaAccountDataSyncEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		datacollector.RegisterProcessCAAccountDataSyncEventMethodToSubscriber(subscriber, rewardsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.RewardsActorNudgeStatusUpdateEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		datacollector.RegisterProcessActorNudgeStatusUpdateEventMethodToSubscriber(subscriber, rewardsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.VendorRewardFulfillmentEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		datacollector.RegisterProcessVendorRewardFulfillmentEventMethodToSubscriber(subscriber, rewardsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	rewardsConsumerServiceVar2, err := wire3.InitializeRewardsConsumerService(dataCollectorEventSqsPublisher, dataCollectorEventSqsCustomDelayPublisher, actorClient, fitttClient, depositClient, connectedAccountClient, rewardClawbackEventCollectedDataPublisher, rewardsClawbackEventCollectedDataCustomDelayPublisher, accountingClient, txnCategorizerClient, onboardingClient, usersClient, piClient, rewardsRewardUnlockerSqsPublisher, rewardsProjectionsGenerationEventSqsPublisher, rewardsConf, rewardsGenConf, cardProvisioningClient, savingsClient, nudgeServiceClient, rewardsRedisStore, groupClient, tieringClient, inAppReferralClient, orderServiceClient, salaryProgramClient, vKYCClient, segmentationServiceClient, userIntelServiceClient, employmentClient, bankCustomerServiceClient, rewardsPGDB)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.TieringPeriodicRewardEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			datacollector.RegisterProcessTieringPeriodicRewardEventMethodToSubscriber(subscriber, rewardsConsumerServiceVar2)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return nil, nil, err
		}
		if shouldStartWorker {
			sqsSubscribers = append(sqsSubscribers, subs)
		}
	}

	rewardProcessorSubscriberService, err := wire3.InitializeRewardProcessorSubscriberService(rewardsPGDB, healthEngineServiceClient, rewardsRedisStore, rewardsProcessingSqsCustomDelayPublisher, rewardClawbackProcessingSqsPublisher, rewardClawbackProcessingSqsDelayPublisher, rewardStatusUpdateEventSnsPublisher, luckyDrawWinningProcessingSqsDelayPublisher, rewardsSqsPublisher, accrualClient, accountPIRelationClient, orderServiceClient, payClient, depositClient, usersClient, vKYCClient, tieringClient, groupClient, inAppReferralClient, onboardingClient, cardProvisioningClient, luckyDrawServiceClient, timelineServiceClient, salaryProgramClient, segmentationServiceClient, broker, actorClient, commsClientVar6, offerRedemptionServiceClient, userIntelServiceClient, rewardsGenConf, rewardsConf, rewardsNotificationEventSqsCustomDelayPublisher, bankCustomerServiceClient, employmentClient, savingsClient, billingClient, fireflyClient, ussRewardManagerClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.LuckyDrawWinningProcessingSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		processor.RegisterProcessLuckyDrawWinningMethodToSubscriber(subscriber, rewardProcessorSubscriberService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.RewardsSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		processor.RegisterProcessRewardMethodToSubscriber(subscriber, rewardProcessorSubscriberService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.RewardsProcessingDelaySqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		processor.RegisterProcessRewardMethodToSubscriber(subscriber, rewardProcessorSubscriberService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.RewardClawbackProcessingSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		processor.RegisterProcessRewardClawbackMethodToSubscriber(subscriber, rewardProcessorSubscriberService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.RewardStatusUpdateSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		processor.RegisterProcessRewardStatusUpdateEventMethodToSubscriber(subscriber, rewardProcessorSubscriberService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.BulkClaimRewardsEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		processor.RegisterProcessBulkClaimRewardsEventMethodToSubscriber(subscriber, rewardProcessorSubscriberService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.RewardsAccountStatusUpdateEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		processor.RegisterProcessAccountStatusUpdateEventMethodToSubscriber(subscriber, rewardProcessorSubscriberService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	notificationConsumerServiceVar2 := wire3.InitializeNotificationConsumerService(commsClientVar6, actorClient, nudgeServiceClient, rewardsPGDB, rewardsRedisStore, rewardsConf, accountsClient, docsClient, savingsClient, usersClient, bankCustomerServiceClient, authClient, projectorServiceClient, orderServiceClient, rewardOffersClient, rewardsGenConf)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.RewardsNotificationEventsSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		notification3.RegisterProcessRewardNotificationEventMethodToSubscriber(subscriber, notificationConsumerServiceVar2)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	projectionsConsumer := wire3.InitialiseRewardsProjectionConsumer(rewardsPGDB, orderServiceClient, cXClient, merchantServiceClient, paymentClient, accountPIRelationClient, actorClient, usersClient, groupClient, tieringClient, inAppReferralClient, onboardingClient, cardProvisioningClient, actionBarClient, connectedAccountClient, vKYCClient, fitttClient, luckyDrawServiceClient, salaryProgramClient, segmentationServiceClient, userIntelServiceClient, employmentClient, rewardsRedisStore, rewardsRewardUnlockerSqsPublisher, projectionEventSnsPublisher, bankCustomerServiceClient, accountingClient, savingsClient, authClient, offerRedemptionServiceClient, txnCategorizerClient, payClient, fireflyClient, billingClient, txnAggregatesClient, exchangerOfferServiceClient, offerCatalogServiceClient, projectorServiceClient, piClient, rewardsSqsPublisher, rewardsConf, rewardsGenConf, alfredClient, orderManagerClient, broker, epfClient, managerClient, questCacheStorage, rewardsS3ClientVar2)

	rewardsprojector2.RegisterConsumerServer(s, projectionsConsumer)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.RewardsProjectionUpdateEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		rewardsprojector2.RegisterProcessProjectionUpdateEventMethodToSubscriber(subscriber, projectionsConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.RewardsProjectionsGenerationEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		rewardsprojector2.RegisterProcessProjectionGenerationEventMethodToSubscriber(subscriber, projectionsConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	generatorConsumerService, err := wire3.InitializeGeneratorConsumerService(rewardsPGDB, rewardsRedisStore, rewardsSqsPublisher, rewardsGeneratorClient, rewardsClaimRewardEventSqsCustomDelayPublisher, orderServiceClient, cXClient, merchantServiceClient, paymentClient, accountPIRelationClient, actorClient, usersClient, groupClient, tieringClient, inAppReferralClient, onboardingClient, cardProvisioningClient, actionBarClient, connectedAccountClient, vKYCClient, fitttClient, luckyDrawServiceClient, salaryProgramClient, segmentationServiceClient, commsClientVar6, broker, rewardsConf, rewardsGenConf, rewardGenerationEventSnsPublisher, authClient, offerRedemptionServiceClient, txnCategorizerClient, payClient, fireflyClient, accountingClient, billingClient, txnAggregatesClient, rewardsNotificationEventSqsCustomDelayPublisher, userIntelServiceClient, bankCustomerServiceClient, employmentClient, exchangerOfferServiceClient, offerCatalogServiceClient, savingsClient, piClient, projectorServiceClient, rewardsRewardUnlockerSqsCustomDelayPublisher, rewardsProjectionUpdateEventSqsPublisher, alfredClient, orderManagerClient, rewardsRewardExpirySqsCustomDelayPublisher, epfClient, managerClient, questCacheStorage, rewardsS3ClientVar3)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.DataCollectorEventsSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		generator.RegisterProcessDataCollectorEventMethodToSubscriber(subscriber, generatorConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.ClaimRewardEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		generator.RegisterProcessClaimRewardEventMethodToSubscriber(subscriber, generatorConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	generateRewardSimulatorService := wire3.InitializeRewardsSimulator(merchantServiceClient, paymentClient, onboardingClient, actorClient, accountPIRelationClient, authClient, offerRedemptionServiceClient, salaryProgramClient, txnCategorizerClient, payClient, groupClient, usersClient, rewardsConf, rewardsGenConf, actionBarClient, connectedAccountClient, fireflyClient, accountingClient, txnAggregatesClient, exchangerOfferServiceClient, offerCatalogServiceClient, tieringClient, inAppReferralClient, cardProvisioningClient, vKYCClient, segmentationServiceClient, userIntelServiceClient, employmentClient, bankCustomerServiceClient, savingsClient, billingClient, rewardsRedisStore, cXClient, piClient, projectorServiceClient, rewardsPGDB, fitttClient, orderServiceClient, rewardsOrderUpdateEventQueueSqsPublisher, alfredClient, orderManagerClient, rewardsCreditCardTxnEventQueueSqsPublisher, rewardsCreditCardBillingEventQueueSqsPublisher, epfClient, managerClient, questCacheStorage, broker, rewardsS3ClientVar4)

	generator.RegisterSimulatorServer(s, generateRewardSimulatorService)

	serviceVar12 := wire3.InitializeRewardsPinotAggregateService(rewardsConf, rewardsGeneratorClient, fireflyClient)

	rewardspinotpb.RegisterRewardsAggregatesServer(s, serviceVar12)

	rewardsPinotService, err := wire3.InitializeRewardsPinotService(rewardsPGDB, accountingClient, txnCategorizerClient, merchantServiceClient, actorClient, orderServiceClient, usersClient, groupClient, tieringClient, inAppReferralClient, onboardingClient, cardProvisioningClient, vKYCClient, salaryProgramClient, segmentationServiceClient, userIntelServiceClient, employmentClient, bankCustomerServiceClient, savingsClient, rewardsConf, rewardsGenConf, rewardsRedisStore)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	rewardspinotpb.RegisterConsumerServer(s, rewardsPinotService)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.RewardsTerminalStatusUpdateEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		rewardspinotpb.RegisterProcessTerminalRewardEventsForPinotMethodToSubscriber(subscriber, rewardsPinotService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.ProjectionEventForPinotSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		rewardspinotpb.RegisterProcessProjectionEventForPinotMethodToSubscriber(subscriber, rewardsPinotService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	requeueServiceConsumerService := wire3.InitializeRequeueServiceConsumerService(accountingClient, orderServiceClient, rewardsOrderUpdateEventQueueSqsPublisher, rewardsCreditCardTxnEventQueueSqsPublisher, rewardsConf)

	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, rewardsGenConf.MerchantPiUpdateAffectedEntitiesEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			requeueservice.RegisterProcessMerchantPiUpdateAffectedEntitiesEventMethodToSubscriber(subscriber, requeueServiceConsumerService)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return nil, nil, err
		}
		if shouldStartWorker {
			sqsSubscribers = append(sqsSubscribers, subs)
		}
	}
	configNameToConfMap[cfg.ConfigName(cfg.REWARD_SERVICE)] = &commonexplorer.Config{StaticConf: &rewardsconf.Config{}, QuestIntegratedConfig: rewardsGenConf}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupCms(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	cmsPGDB types.CmsPGDB) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	cmsConf, err := cmsconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.CMS_SERVICE))
		return nil, nil, err
	}
	_ = cmsConf

	cmsService := wire4.InitializeCmsService(cmsPGDB, cmsConf, awsConf)

	cmspb.RegisterCmsServiceServer(s, cmsService)

	cmsDevService := wire4.InitializeCmsDevService(cmsPGDB)

	cmsdeveloperpb.RegisterCmsDevServer(s, cmsDevService)

	configNameToConfMap[cfg.ConfigName(cfg.CMS_SERVICE)] = &commonexplorer.Config{StaticConf: &cmsconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupQuest(ctx context.Context, s, grpcWebServer *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	questPGDB types.QuestPGDB,
	questRedisStore types.QuestRedisStore,
	questCacheStorage types.QuestCacheStorage,
	actorClient actor.ActorClient,
	usersClient user.UsersClient,
	analyticsDBResourceProvider storage2.AnalyticsDBResourceProvider,
	segmentationServiceClient segmentpb.SegmentationServiceClient,
	managerClient managerpb.ManagerClient,
	groupClient usergrouppb.GroupClient) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	questConf, err := questconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.QUEST_SERVICE))
		return nil, nil, err
	}
	_ = questConf

	questGenConf, err := dynconf.LoadConfig(questconf.Load, questgenconfig.NewConfig, cfg.QUEST_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.QUEST_SERVICE))
		return nil, nil, err
	}

	_ = questGenConf

	serviceVar13, err := wire5.InitializeManagerService(questGenConf, questPGDB, questRedisStore, questCacheStorage, actorClient, usersClient, analyticsDBResourceProvider, segmentationServiceClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	managerpb.RegisterManagerServer(s, serviceVar13)

	serviceVar14, err := wire5.InitializeQuestFrontendService(questGenConf, managerClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	frontend2.RegisterFrontendServer(grpcWebServer, serviceVar14)

	questDBStatesService := wire5.InitializeQuestDBStatesService(questGenConf, questPGDB, questRedisStore, managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, questCacheStorage)

	questdevpb.RegisterQuestDbStatesServer(s, questDBStatesService)

	configNameToConfMap[cfg.ConfigName(cfg.QUEST_SERVICE)] = &commonexplorer.Config{StaticConf: &questconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupComms(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	managerClient managerpb.ManagerClient,
	groupClient usergrouppb.GroupClient,
	usersClient user.UsersClient,
	actorClient actor.ActorClient,
	segmentationServiceClient segmentpb.SegmentationServiceClient,
	sMSClient smspb.SMSClient,
	emailClient emailpb.EmailClient,
	fCMClient fcmpb.FCMClient,
	whatsAppClient whatsapppb.WhatsAppClient,
	userPreferenceClient commstypes.UserPreferenceClientWithInterceptors,
	commsRedisStore commstypes.CommsRedisStore,
	authClient authpb.AuthClient,
	commsPGDB types.CommsPGDB,
	onboardingClient onboardingpb.OnboardingClient,
	serveFAQClient faqpb.ServeFAQClient,
	commsClientVar10 commstypes.CommsClientWithInterceptors) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	commsConf, err := commsconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.COMMS_SERVICE))
		return nil, nil, err
	}
	_ = commsConf

	commsGenConf, err := dynconf.LoadConfigWithQuestConfig(commsconf.Load, genconf4.NewConfigWithQuest, cfg.COMMS_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.COMMS_SERVICE))
		return nil, nil, err
	}

	if !conf.QuestSdk.Disable {
		commsGenConfAppConfig := questsdkinit.QuestSdkAppConfig{AppConfig: commsGenConf, SdkConfig: commsGenConf.QuestSdk()}
		questsdkinit.SetupQuestSDK([]questsdkinit.QuestSdkAppConfig{commsGenConfAppConfig}, string(cfg.GROWTH_INFRA_SERVER), managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, broker)
	}

	_ = commsGenConf

	smsSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, commsGenConf.SmsSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	emailSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, commsGenConf.EmailSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	highPriorityNotificationSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, commsGenConf.HighPriorityNotificationSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	normalPriorityNotificationSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, commsGenConf.NormalPriorityNotificationSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	lowPriorityNotificationSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, commsGenConf.LowPriorityNotificationSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	whatsappSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, commsGenConf.WhatsappSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	notificationUpdateSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, commsGenConf.NotificationUpdateSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	smsStatusUpdateSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, commsGenConf.SmsStatusUpdateSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	whatsappBotSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, commsGenConf.WhatsappBotSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	smsStatusUpdateSqsDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, commsGenConf.SmsStatusUpdateSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	sentSmsStatusSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, commsGenConf.SentSmsStatusSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	smsSqsDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, commsGenConf.SmsSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}

	commsService, err := wire6.InitializeCommsService(ctx, awsConf, sMSClient, emailClient, fCMClient, usersClient, smsSqsPublisher, emailSqsPublisher, highPriorityNotificationSqsPublisher, normalPriorityNotificationSqsPublisher, lowPriorityNotificationSqsPublisher, whatsappSqsPublisher, actorClient, broker, commsConf, whatsAppClient, userPreferenceClient, groupClient, notificationUpdateSqsPublisher, smsStatusUpdateSqsPublisher, commsRedisStore, authClient, commsPGDB, commsGenConf)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	comms.RegisterCommsServer(s, commsService)

	serviceVar15 := wire6.InitializeDeviceTokenService(actorClient, commsPGDB)

	commsdevicetokenpb.RegisterFCMDeviceTokenServer(s, serviceVar15)

	userCommsPreferenceService := wire6.InitializeUserCommsPreferenceService(commsPGDB, actorClient)

	uppb.RegisterUserPreferenceServer(s, userCommsPreferenceService)

	serviceVar16 := wire6.InitializeInAppTargetedCommsService(commsPGDB, commsConf, commsGenConf, commsRedisStore, segmentationServiceClient, onboardingClient)

	tcpb.RegisterInAppTargetedCommsServer(s, serviceVar16)

	commsDbStatesService := wire6.InitializeCommsDbStatesService(commsPGDB, usersClient, actorClient, sMSClient, commsConf, whatsAppClient, userPreferenceClient, groupClient, commsRedisStore, authClient, serveFAQClient, commsGenConf)

	cdpb.RegisterCommsDbStatesServer(s, commsDbStatesService)

	commsDevActionService := wire6.InitializeCommsDevActionService(commsPGDB, usersClient, actorClient, sMSClient, commsConf, whatsAppClient, userPreferenceClient, groupClient, commsRedisStore, authClient, commsGenConf)

	commsdevaction.RegisterCommsDevActionServer(s, commsDevActionService)

	commsConsumerService, err := wire6.InitializeCommsConsumerService(sMSClient, emailClient, fCMClient, usersClient, actorClient, commsConf, whatsAppClient, userPreferenceClient, commsClientVar10, groupClient, whatsappBotSqsPublisher, commsRedisStore, smsStatusUpdateSqsDelayPublisher, sentSmsStatusSqsPublisher, smsSqsDelayPublisher, authClient, commsPGDB, commsGenConf)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, commsGenConf.SmsSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		comms.RegisterProcessSMSMethodToSubscriber(subscriber, commsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, commsGenConf.EmailSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		comms.RegisterProcessEmailMethodToSubscriber(subscriber, commsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, commsGenConf.NotificationSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		comms.RegisterProcessNotificationMethodToSubscriber(subscriber, commsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, commsGenConf.HighPriorityNotificationSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		comms.RegisterProcessNotificationMethodToSubscriber(subscriber, commsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, commsGenConf.NormalPriorityNotificationSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		comms.RegisterProcessNotificationMethodToSubscriber(subscriber, commsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, commsGenConf.LowPriorityNotificationSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		comms.RegisterProcessNotificationMethodToSubscriber(subscriber, commsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, commsGenConf.AclSmsCallbackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		comms.RegisterProcessAclSmsCallBackMethodToSubscriber(subscriber, commsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, commsGenConf.AclWhatsappCallbackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		comms.RegisterProcessAclWhatsappCallBackMethodToSubscriber(subscriber, commsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, commsGenConf.WhatsappUserRepliesSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		comms.RegisterProcessAclWhatsappReplyMethodToSubscriber(subscriber, commsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, commsGenConf.KaleyraSmsCallbackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		comms.RegisterProcessKaleyraSmsCallBackMethodToSubscriber(subscriber, commsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, commsGenConf.WhatsappSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		comms.RegisterProcessWhatsappMethodToSubscriber(subscriber, commsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, commsGenConf.NotificationUpdateSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		comms.RegisterUpdateNotificationMethodToSubscriber(subscriber, commsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, commsGenConf.EmailCallbackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		comms.RegisterProcessEmailCallBackMethodToSubscriber(subscriber, commsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, commsGenConf.SmsStatusUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		comms.RegisterUpdateSmsStatusMethodToSubscriber(subscriber, commsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, commsGenConf.SentSmsStatusSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		comms.RegisterProcessSentSMSMethodToSubscriber(subscriber, commsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	extSub, err = awswire.InitializeExtendedSubscriberWithGenConf(ctx, commsGenConf.EmailExtendedSqsSubscriber(), awsConf, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		comms.RegisterProcessEmailMethodToSubscriber(subscriber, commsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors())
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if extSub.ShouldStartWorker {
		extendedSqsSubscribers = append(extendedSqsSubscribers, extSub)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, commsGenConf.GupshupWhatsappCallbackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		comms.RegisterProcessGupshupWhatsappCallBackMethodToSubscriber(subscriber, commsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, commsGenConf.GupshupRcsCallbackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		comms.RegisterProcessGupshupRcsCallbackMethodToSubscriber(subscriber, commsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, commsGenConf.NetCoreSmsCallbackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		comms.RegisterProcessNetCoreSmsCallbackMethodToSubscriber(subscriber, commsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, commsGenConf.AirtelSmsCallbackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		comms.RegisterProcessAirtelSmsCallbackMethodToSubscriber(subscriber, commsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, commsGenConf.AirtelWhatsappCallbackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		comms.RegisterProcessAirtelWhatsappCallbackMethodToSubscriber(subscriber, commsConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	commsConsumerServiceVar2, err := wire6.InitializeCommsConsumerService(sMSClient, emailClient, fCMClient, usersClient, actorClient, commsConf, whatsAppClient, userPreferenceClient, commsClientVar10, groupClient, whatsappBotSqsPublisher, commsRedisStore, smsStatusUpdateSqsDelayPublisher, sentSmsStatusSqsPublisher, smsSqsDelayPublisher, authClient, commsPGDB, commsGenConf)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, commsGenConf.PinpointEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			comms.RegisterProcessPinpointEventMethodToSubscriber(subscriber, commsConsumerServiceVar2)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return nil, nil, err
		}
		if shouldStartWorker {
			sqsSubscribers = append(sqsSubscribers, subs)
		}
	}
	configNameToConfMap[cfg.ConfigName(cfg.COMMS_SERVICE)] = &commonexplorer.Config{StaticConf: &commsconf.Config{}, QuestIntegratedConfig: commsGenConf}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupDynamicelements(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	managerClient managerpb.ManagerClient,
	groupClient usergrouppb.GroupClient,
	usersClient user.UsersClient,
	actorClient actor.ActorClient,
	segmentationServiceClient segmentpb.SegmentationServiceClient,
	questCacheStorage types.QuestCacheStorage,
	inAppTargetedCommsClient wiretypes.InAppTargetedCommsClientWithInterceptors,
	notificationsClient invnotificationpb.NotificationsClient,
	vKYCFeClient vkycpb.VKYCFeClient,
	savingsClient savingspb.SavingsClient,
	preApprovedLoanClient preapprovedloan.PreApprovedLoanClient,
	fireflyClient fireflypb.FireflyClient,
	uSSDynamicElementsManagerClient usstockspb.USSDynamicElementsManagerClient,
	tieringClient tieringpb.TieringClient,
	riskClient riskpb.RiskClient,
	payClient paypb.PayClient,
	employmentFeClient employmentpb.EmploymentFeClient,
	offerCatalogServiceClient casperpb.OfferCatalogServiceClient,
	complianceClient compliancepb.ComplianceClient,
	salaryProgramClient salaryprogrampb.SalaryProgramClient,
	bankCustomerServiceClient bankcust.BankCustomerServiceClient,
	onboardingClient onboardingpb.OnboardingClient,
	dynamicElementsClient dynamicelements.DynamicElementsClient,
	netWorthClient networth.NetWorthClient,
	connectedAccountClient capb.ConnectedAccountClient,
	panClient pan.PanClient,
	cardProvisioningClient provisioning.CardProvisioningClient,
	crossAttachClient crossattachpb.CrossAttachClient) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	dynamicelementsConf, err := dynamicelementsconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.DYNAMIC_ELEMENTS_SERVICE))
		return nil, nil, err
	}
	_ = dynamicelementsConf

	dynamicelementsGenConf, err := dynconf.LoadConfigWithQuestConfig(dynamicelementsconf.Load, dynamicelementsgenconf.NewConfigWithQuest, cfg.DYNAMIC_ELEMENTS_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.DYNAMIC_ELEMENTS_SERVICE))
		return nil, nil, err
	}

	if !conf.QuestSdk.Disable {
		dynamicelementsGenConfAppConfig := questsdkinit.QuestSdkAppConfig{AppConfig: dynamicelementsGenConf, SdkConfig: dynamicelementsGenConf.QuestSdk()}
		questsdkinit.SetupQuestSDK([]questsdkinit.QuestSdkAppConfig{dynamicelementsGenConfAppConfig}, string(cfg.GROWTH_INFRA_SERVER), managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, broker)
	}

	_ = dynamicelementsGenConf

	dynamicElementsService := wire7.InitializeDynamicElementsService(dynamicelementsConf, dynamicelementsGenConf, questCacheStorage, actorClient, usersClient, groupClient, inAppTargetedCommsClient, notificationsClient, vKYCFeClient, savingsClient, preApprovedLoanClient, fireflyClient, uSSDynamicElementsManagerClient, tieringClient, riskClient, payClient, segmentationServiceClient, managerClient, broker, employmentFeClient, offerCatalogServiceClient, complianceClient, salaryProgramClient, bankCustomerServiceClient, onboardingClient, dynamicElementsClient, netWorthClient, connectedAccountClient, panClient, cardProvisioningClient, crossAttachClient)

	depb.RegisterDynamicElementsServer(s, dynamicElementsService)

	configNameToConfMap[cfg.ConfigName(cfg.DYNAMIC_ELEMENTS_SERVICE)] = &commonexplorer.Config{StaticConf: &dynamicelementsconf.Config{}, QuestIntegratedConfig: dynamicelementsGenConf}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupWhatsappbot(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	commsClientVar12 wabottypes.WaBotCommsClientWithInterceptors,
	actorClient actor.ActorClient,
	usersClient user.UsersClient,
	userPreferenceClientVar6 wabottypes.WaBotUserPreferenceClientWithInterceptors,
	onboardingClient onboardingpb.OnboardingClient) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	whatsappbotConf, err := whatsappbotconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.WHATSAPP_BOT_SERVICE))
		return nil, nil, err
	}
	_ = whatsappbotConf

	whatsappbotGenConf, err := dynconf.LoadConfig(whatsappbotconf.Load, whatsappbotgenconf.NewConfig, cfg.WHATSAPP_BOT_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.WHATSAPP_BOT_SERVICE))
		return nil, nil, err
	}

	_ = whatsappbotGenConf

	waitlistAccessGrantPublisher, err := sqs.NewPublisherWithConfig(ctx, whatsappbotGenConf.WaitlistAccessGrantPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}

	botConsumer := wire8.InitializeWhatsappBotConsumer(commsClientVar12, whatsappbotConf, actorClient, usersClient, userPreferenceClientVar6, onboardingClient, waitlistAccessGrantPublisher)

	if func(conf *config.Config) bool {
		commsConf, err := commsconf.Load()
		if err != nil {
			panic(err)
		}
		return commsConf.FeatureFlags.EnableWhatsappBot
	}(conf) {
		subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, whatsappbotGenConf.WhatsappBotSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			whatsappbot.RegisterProcessUserMessageMethodToSubscriber(subscriber, botConsumer)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return nil, nil, err
		}
		if shouldStartWorker {
			sqsSubscribers = append(sqsSubscribers, subs)
		}
	}
	configNameToConfMap[cfg.ConfigName(cfg.WHATSAPP_BOT_SERVICE)] = &commonexplorer.Config{StaticConf: &whatsappbotconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupNudge(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	managerClient managerpb.ManagerClient,
	groupClient usergrouppb.GroupClient,
	usersClient user.UsersClient,
	actorClient actor.ActorClient,
	segmentationServiceClient segmentpb.SegmentationServiceClient,
	nudgePGDB types.NudgePGDB,
	nudgeRedisStore types4.NudgeRedisStore,
	nudgeRankingRedisStore types4.NudgeRankingRedisStore,
	onboardingClient onboardingpb.OnboardingClient,
	payClient paypb.PayClient,
	journeyServiceClient journeypb.JourneyServiceClient,
	questCacheStorage types.QuestCacheStorage,
	connectedAccountClient capb.ConnectedAccountClient,
	rewardsGeneratorClient rewardspb.RewardsGeneratorClient,
	nudgeServiceClient nudgepb.NudgeServiceClient,
	orderServiceClient orderpb.OrderServiceClient) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	nudgeConf, err := nudgeconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.NUDGE_SERVICE))
		return nil, nil, err
	}
	_ = nudgeConf

	nudgeGenConf, err := dynconf.LoadConfigWithQuestConfig(nudgeconf.Load, genconf5.NewConfigWithQuest, cfg.NUDGE_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.NUDGE_SERVICE))
		return nil, nil, err
	}

	if !conf.QuestSdk.Disable {
		nudgeGenConfAppConfig := questsdkinit.QuestSdkAppConfig{AppConfig: nudgeGenConf, SdkConfig: nudgeGenConf.QuestSdk()}
		questsdkinit.SetupQuestSDK([]questsdkinit.QuestSdkAppConfig{nudgeGenConfAppConfig}, string(cfg.GROWTH_INFRA_SERVER), managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, broker)
	}

	_ = nudgeGenConf

	exitEventSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, nudgeGenConf.ExitEventSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	exitEvaluatorSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, nudgeGenConf.ExitEvaluatorSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	journeyEntryEventSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, nudgeGenConf.JourneyEntryEventSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	entryEvaluatorSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, nudgeGenConf.EntryEvaluatorSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}

	entryEventCustomDelayPublisher, err := customqueuewire.InitializeSqsCustomDelayQueuePublisher(ctx,
		nudgeGenConf.EntryEventCustomDelayPublisher().OrchestratorSqsPublisher,
		customdelayqueue.QueueName(nudgeGenConf.EntryEventCustomDelayPublisher().GetDestQueueName()),
		sqsClient, queue.NewDefaultMessage(),
	)
	if err != nil {
		logger.Error(ctx, "failed to create custom sqs publisher", zap.Error(err))
		return nil, nil, err
	}

	actorNudgeStatusUpdateEventSnsPublisher, err := sns.NewSnsPublisherWithConfig(ctx, nudgeGenConf.ActorNudgeStatusUpdateEventSnsPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}

	serviceVar17 := wire9.InitializeNudgeService(nudgePGDB, nudgeRedisStore, nudgeRankingRedisStore, nudgeConf, nudgeGenConf, segmentationServiceClient, onboardingClient, actorClient, usersClient, groupClient, managerClient, payClient, entryEventCustomDelayPublisher, exitEventSqsPublisher, journeyServiceClient, questCacheStorage, broker)

	nudgepb.RegisterNudgeServiceServer(s, serviceVar17)

	consumerServiceVar3 := wire9.InitializeNudgeDataCollectorConsumerService(nudgeGenConf, exitEvaluatorSqsPublisher, journeyEntryEventSqsPublisher, entryEvaluatorSqsPublisher)

	nudgedatacollectorpb.RegisterConsumerServer(s, consumerServiceVar3)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, nudgeGenConf.KYCSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		nudgedatacollectorpb.RegisterProcessKYCEventMethodToSubscriber(subscriber, consumerServiceVar3)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, nudgeGenConf.OrderUpdateSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		nudgedatacollectorpb.RegisterProcessOrderUpdateEventMethodToSubscriber(subscriber, consumerServiceVar3)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, nudgeGenConf.CAAccountUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		nudgedatacollectorpb.RegisterProcessCAAccountUpdateEventMethodToSubscriber(subscriber, consumerServiceVar3)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, nudgeGenConf.InvestmentSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		nudgedatacollectorpb.RegisterProcessInvestmentEventMethodToSubscriber(subscriber, consumerServiceVar3)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, nudgeGenConf.SalaryProgramStatusUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		nudgedatacollectorpb.RegisterProcessSalaryProgramStatusUpdateEventMethodToSubscriber(subscriber, consumerServiceVar3)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, nudgeGenConf.UpiEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		nudgedatacollectorpb.RegisterProcessUpiEventMethodToSubscriber(subscriber, consumerServiceVar3)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, nudgeGenConf.RewardGenerationEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		nudgedatacollectorpb.RegisterProcessRewardGenerationEventMethodToSubscriber(subscriber, consumerServiceVar3)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, nudgeGenConf.RewardStatusUpdateEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		nudgedatacollectorpb.RegisterProcessRewardStatusUpdateEventMethodToSubscriber(subscriber, consumerServiceVar3)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, nudgeGenConf.PALEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		nudgedatacollectorpb.RegisterProcessPALEventMethodToSubscriber(subscriber, consumerServiceVar3)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, nudgeGenConf.NonFinancialInvestmentSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		nudgedatacollectorpb.RegisterProcessNonFinancialInvestmentEventMethodToSubscriber(subscriber, consumerServiceVar3)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, nudgeGenConf.DebitCardUpdateEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		nudgedatacollectorpb.RegisterProcessDebitCardUpdateEventMethodToSubscriber(subscriber, consumerServiceVar3)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, nudgeGenConf.OfferRedemptionStatusUpdateSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		nudgedatacollectorpb.RegisterProcessOfferRedemptionStatusUpdateEventMethodToSubscriber(subscriber, consumerServiceVar3)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, nudgeGenConf.IncomeUpdateEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		nudgedatacollectorpb.RegisterProcessIncomeUpdateEventMethodToSubscriber(subscriber, consumerServiceVar3)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	consumerServiceVar4 := wire9.InitializeNudgeExitEvaluatorConsumerService(nudgePGDB, nudgeRedisStore, connectedAccountClient, rewardsGeneratorClient, actorClient, onboardingClient, nudgeConf, actorNudgeStatusUpdateEventSnsPublisher, payClient, journeyServiceClient)

	nudgeexitevaluatorpb.RegisterConsumerServer(s, consumerServiceVar4)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, nudgeGenConf.ExitEvaluatorSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		nudgeexitevaluatorpb.RegisterProcessDataCollectorEventMethodToSubscriber(subscriber, consumerServiceVar4)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	consumerServiceVar5 := wire9.InitializeNudgeEntryEvaluatorConsumerService(nudgePGDB, nudgeRedisStore, connectedAccountClient, rewardsGeneratorClient, actorClient, onboardingClient, segmentationServiceClient, nudgeConf, payClient, journeyServiceClient)

	nudgeentryevaluatorpb.RegisterConsumerServer(s, consumerServiceVar5)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, nudgeGenConf.EntryEvaluatorSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		nudgeentryevaluatorpb.RegisterProcessDataCollectorEventMethodToSubscriber(subscriber, consumerServiceVar5)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	serviceVar18 := wire9.InitializeNudgeConsumerService(nudgeConf, nudgeGenConf, nudgePGDB, nudgeRedisStore, segmentationServiceClient, onboardingClient, actorClient, payClient, journeyServiceClient, actorNudgeStatusUpdateEventSnsPublisher)

	nudgeconsumerpb.RegisterConsumerServer(s, serviceVar18)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, nudgeGenConf.EntryEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		nudgeconsumerpb.RegisterProcessActorNudgeEntryMethodToSubscriber(subscriber, serviceVar18)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, nudgeGenConf.ExitEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		nudgeconsumerpb.RegisterProcessActorNudgeExitMethodToSubscriber(subscriber, serviceVar18)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, nudgeGenConf.DismissalEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		nudgeconsumerpb.RegisterProcessActorNudgeDismissalFeedbackInfoEventMethodToSubscriber(subscriber, serviceVar18)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	nudgeDbStates := wire9.InitializeNudgeDbStateService(nudgePGDB, nudgeRedisStore)

	nudgedeveloperpb.RegisterNudgeDbStatesServer(s, nudgeDbStates)

	serviceVar19 := wire9.InitializeJourneyService(nudgeGenConf, nudgePGDB, nudgeServiceClient, segmentationServiceClient, onboardingClient, orderServiceClient, payClient)

	journeypb.RegisterJourneyServiceServer(s, serviceVar19)

	serviceVar20 := wire9.InitializeJourneyConsumerService(nudgeGenConf, nudgePGDB, nudgeServiceClient, segmentationServiceClient, onboardingClient, orderServiceClient, payClient, broker)

	journeyconsumerpb.RegisterConsumerServer(s, serviceVar20)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, nudgeGenConf.ActorNudgeStatusUpdateEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		journeyconsumerpb.RegisterProcessActorNudgeStatusUpdateEventMethodToSubscriber(subscriber, serviceVar20)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, nudgeGenConf.JourneyEntryEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		journeyconsumerpb.RegisterProcessActorJourneyEntryMethodToSubscriber(subscriber, serviceVar20)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}
	configNameToConfMap[cfg.ConfigName(cfg.NUDGE_SERVICE)] = &commonexplorer.Config{StaticConf: &nudgeconf.Config{}, QuestIntegratedConfig: nudgeGenConf}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupSegment(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	segmentRedisStore types5.SegmentRedisStore,
	segmentCacheStorage types5.SegmentCacheStorage,
	segmentPGDB types.SegmentPGDB,
	analyticsDBResourceProvider storage2.AnalyticsDBResourceProvider,
	bigqueryDBResourceProvider storagev2analytics.BigqueryDBResourceProvider) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	segmentConf, err := segmentconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.SEGMENT_SERVICE))
		return nil, nil, err
	}
	_ = segmentConf

	segmentGenConf, err := dynconf.LoadConfig(segmentconf.Load, genconf6.NewConfig, cfg.SEGMENT_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.SEGMENT_SERVICE))
		return nil, nil, err
	}

	_ = segmentGenConf

	triggerSegmentExportPublisher, err := sqs.NewPublisherWithConfig(ctx, segmentGenConf.TriggerSegmentExportPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	pollSegmentExportPublisher, err := sqs.NewPublisherWithConfig(ctx, segmentGenConf.PollSegmentExportPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	uploadSegmentExportPartFilePublisher, err := sqs.NewPublisherWithConfig(ctx, segmentGenConf.UploadSegmentExportPartFilePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	compareSegmentInstancesDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, segmentGenConf.CompareSegmentInstancesPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}

	serviceVar21 := wire10.InitializeUserSegmentService(segmentRedisStore, segmentCacheStorage, segmentPGDB, analyticsDBResourceProvider, bigqueryDBResourceProvider, segmentConf, triggerSegmentExportPublisher, awsConf, broker)

	if func(conf *config.Config) bool {
		return !cfg.IsTestEnv(conf.Environment)
	}(conf) {
		segmentpb.RegisterSegmentationServiceServer(s, serviceVar21)
	}

	serviceVar22 := wire10.InitializeUserSegmentConsumerService(segmentRedisStore, segmentCacheStorage, segmentPGDB, triggerSegmentExportPublisher, pollSegmentExportPublisher, uploadSegmentExportPartFilePublisher, compareSegmentInstancesDelayPublisher, awsConf, segmentConf, segmentGenConf, broker)

	if func(conf *config.Config) bool {
		return !cfg.IsTestEnv(conf.Environment)
	}(conf) {
		segmentconsumerpb.RegisterConsumerServer(s, serviceVar22)
	}

	if func(conf *config.Config) bool {
		return !cfg.IsTestEnv(conf.Environment)
	}(conf) {
		subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, segmentGenConf.TriggerSegmentExportSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			segmentconsumerpb.RegisterTriggerSegmentExportMethodToSubscriber(subscriber, serviceVar22)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return nil, nil, err
		}
		if shouldStartWorker {
			sqsSubscribers = append(sqsSubscribers, subs)
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestEnv(conf.Environment)
	}(conf) {
		subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, segmentGenConf.PollSegmentExportSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			segmentconsumerpb.RegisterPollSegmentExportMethodToSubscriber(subscriber, serviceVar22)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return nil, nil, err
		}
		if shouldStartWorker {
			sqsSubscribers = append(sqsSubscribers, subs)
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestEnv(conf.Environment)
	}(conf) {
		subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, segmentGenConf.ProcessSegmentExportPartFileSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			segmentconsumerpb.RegisterProcessSegmentExportPartFileMethodToSubscriber(subscriber, serviceVar22)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return nil, nil, err
		}
		if shouldStartWorker {
			sqsSubscribers = append(sqsSubscribers, subs)
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestEnv(conf.Environment)
	}(conf) {
		subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, segmentGenConf.UploadSegmentExportPartFileSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			segmentconsumerpb.RegisterUploadSegmentExportPartFileCsvMethodToSubscriber(subscriber, serviceVar22)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return nil, nil, err
		}
		if shouldStartWorker {
			sqsSubscribers = append(sqsSubscribers, subs)
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestEnv(conf.Environment)
	}(conf) {
		subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, segmentGenConf.ProcessSegmentExportPartFileV2Subscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			segmentconsumerpb.RegisterProcessSegmentExportPartFileV2MethodToSubscriber(subscriber, serviceVar22)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return nil, nil, err
		}
		if shouldStartWorker {
			sqsSubscribers = append(sqsSubscribers, subs)
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestEnv(conf.Environment)
	}(conf) {
		subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, segmentGenConf.CompareSegmentInstancesSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			segmentconsumerpb.RegisterCompareSegmentInstancesMethodToSubscriber(subscriber, serviceVar22)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return nil, nil, err
		}
		if shouldStartWorker {
			sqsSubscribers = append(sqsSubscribers, subs)
		}
	}

	segmentDbStates := wire10.InitializeUserSegmentDbStateService(segmentRedisStore, segmentCacheStorage, segmentPGDB, segmentConf, triggerSegmentExportPublisher, awsConf, broker, analyticsDBResourceProvider, bigqueryDBResourceProvider)

	if func(conf *config.Config) bool {
		return !cfg.IsTestEnv(conf.Environment)
	}(conf) {
		segmentdeveloperpb.RegisterSegmentDbStatesServer(s, segmentDbStates)
	}

	configNameToConfMap[cfg.ConfigName(cfg.SEGMENT_SERVICE)] = &commonexplorer.Config{StaticConf: &segmentconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

func initLogger() error {
	// Load configuration
	conf, err := config.Load(cfg.GROWTH_INFRA_SERVER)
	if err != nil {
		// cannot log error since logger is not initialized yet.
		return err
	}

	logger.InitV2(conf.Environment, conf.Logging)
	if conf.SecureLogging != nil {
		logger.InitSecureLoggerV2(conf.SecureLogging)
	}
	return nil
}

func getEnvNamespaceClient(ns temporalpkg.Namespace, env string) string {
	if cfg.IsLocalEnv(env) {
		return strings.ToLower(string(ns))
	}

	return fmt.Sprintf("%s-%s", env, strings.ToLower(string(ns)))
}

func getSQSConsumerInterceptors() []grpc.UnaryServerInterceptor {
	return []grpc.UnaryServerInterceptor{questinterceptor.UserContextInterceptor}
}
