// code generated by tools/servergen
package server

import (
	context "context"
	"fmt"
	"net/http"
	strings "strings"
	time "time"

	awssdk "github.com/aws/aws-sdk-go-v2/aws"
	awssqs "github.com/aws/aws-sdk-go-v2/service/sqs"
	prometheus "github.com/prometheus/client_golang/prometheus"
	redis "github.com/redis/go-redis/v9"
	analytics "github.com/rudderlabs/analytics-go"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
	reflection "google.golang.org/grpc/reflection"
	gorm "gorm.io/gorm"

	"github.com/epifi/be-common/pkg/async/goroutine"
	awsconfpkg "github.com/epifi/be-common/pkg/aws/v2/config"
	sqs "github.com/epifi/be-common/pkg/aws/v2/sqs"
	cfg "github.com/epifi/be-common/pkg/cfg"
	consul "github.com/epifi/be-common/pkg/cfg/consul"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	commonexplorer "github.com/epifi/be-common/pkg/cfg/explorer"
	config "github.com/epifi/be-common/pkg/cmd/config"
	genconf "github.com/epifi/be-common/pkg/cmd/config/genconf"
	types "github.com/epifi/be-common/pkg/cmd/types"
	epificontext "github.com/epifi/be-common/pkg/epificontext"
	epifigrpc "github.com/epifi/be-common/pkg/epifigrpc"
	commoninterceptors "github.com/epifi/be-common/pkg/epifigrpc/interceptors"
	resolver "github.com/epifi/be-common/pkg/epifigrpc/resolver"
	epifiserver "github.com/epifi/be-common/pkg/epifiserver"
	temporalpkg "github.com/epifi/be-common/pkg/epifitemporal"
	errgroup "github.com/epifi/be-common/pkg/errgroup"
	events "github.com/epifi/be-common/pkg/events"
	logger "github.com/epifi/be-common/pkg/logger"
	profiling "github.com/epifi/be-common/pkg/profiling"
	queue "github.com/epifi/be-common/pkg/queue"
	ratelimiter "github.com/epifi/be-common/pkg/ratelimiter"
	store "github.com/epifi/be-common/pkg/ratelimiter/store"
	storage "github.com/epifi/be-common/pkg/storage"
	storage2 "github.com/epifi/be-common/pkg/storage/v2"
	otel "github.com/epifi/be-common/pkg/tracing/opentelemetry"
	explorerpb "github.com/epifi/gamma/api/pkg/cfg/explorer"
	vmpb "github.com/epifi/gamma/api/vendormapping"
	vmconsumerpb "github.com/epifi/gamma/api/vendormapping/consumer"
	developer2 "github.com/epifi/gamma/api/vendormapping/developer"
	explorer "github.com/epifi/gamma/pkg/cfg/explorer"
	questinterceptor "github.com/epifi/gamma/pkg/epifigrpc/interceptors/quest"
	fepkg "github.com/epifi/gamma/pkg/frontend/msg"
	vendormappingconf "github.com/epifi/gamma/vendormapping/config"
	genconf2 "github.com/epifi/gamma/vendormapping/config/genconf"
	wire "github.com/epifi/gamma/vendormapping/wire"
)

// nolint: funlen
func Run(ctx context.Context, initNotifier chan<- cfg.ServerName) error {
	// Panic handler for logging panics in standard format
	epifiserver.HandlePanic()
	resolver.RegisterBuilder(resolver.ConsulSchemeName)
	var configNameToConfMap = make(commonexplorer.CfgExplorerMap)

	err := initLogger()
	if err != nil {
		// cannot log error since logger maynot be initialized yet.
		return err
	}
	ctx = epificontext.WithTraceId(ctx, metadata.MD{})
	// Load static configuration
	conf, err := config.Load(cfg.VENDORMAPPING_SERVER)
	if err != nil {
		logger.Error(ctx, "failed to load static conf", zap.Error(err))
		return err
	}
	_ = conf
	// Load generated dynamic configuration
	gconf, err := genconf.Load(cfg.VENDORMAPPING_SERVER)
	if err != nil {
		logger.Error(ctx, "failed to load dynamic conf", zap.Error(err))
		return err
	}
	logger.Info(ctx, "initiating server")

	if gconf.Profiling().AutomaticProfiling().EnableAutoProfiling() {
		profilingErr := profiling.AutomaticProfiling(context.Background(), cfg.VENDORMAPPING_SERVER, gconf.Profiling(), gconf.Environment(), gconf.AWS().Region)
		if profilingErr != nil {
			logger.Error(ctx, "failed to start automatic profiling", zap.Error(profilingErr))
			return profilingErr
		}
	}

	if gconf.Tracing().Enable {
		_, shutdown, tracingErr := otel.ConfigureOpenTelemetryV2(context.Background(), cfg.VENDORMAPPING_SERVER, gconf.Environment(), gconf.Profiling().PyroscopeProfiling().EnablePyroscope())
		if tracingErr != nil {
			logger.Error(ctx, "failed to configure open telemetry", zap.Error(tracingErr))
			return err
		}
		defer shutdown()
	}

	err = profiling.PyroscopeProfilingV2(gconf.Name(), gconf.Profiling(), gconf.Environment())
	if err != nil {
		logger.Error(ctx, "error starting pyroscope profiler", zap.Error(err))
	}

	if !cfg.IsLocalEnv(gconf.Environment()) && !cfg.IsRemoteDebugEnabled() && !cfg.IsTestTenantEnabled() && !cfg.IsTeamSpaceTenant() {
		initDisc, _ := consul.GetBool(consul.GetInitDiscoveryKey(gconf.Environment(), string(gconf.Name())))
		queue.SetWorkerInitialization(initDisc)
	}

	rateLimiterRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()[gconf.RedisRateLimiterName()], gconf.Tracing().Enable)
	defer func() { _ = rateLimiterRedisStore.Close() }()

	var (
		crdbResourceMap    *storage2.DBResourceProvider[*gorm.DB]
		crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor]
	)

	vendormappingPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["VendormappingPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "VendormappingPGDB"))
		return err
	}
	vendormappingPGDBSqlDb, err := vendormappingPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "VendormappingPGDB"))
		return err
	}
	defer func() { _ = vendormappingPGDBSqlDb.Close() }()

	vendorMappingRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["VendorMappingRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = vendorMappingRedisStore.Close() }()

	awsConf, awsErr := awsconfpkg.NewAWSConfig(ctx, gconf.AWS().Region, gconf.Tracing().Enable)
	if awsErr != nil {
		logger.Error(ctx, "error in loading aws v2 config", zap.Error(awsErr))
		if !cfg.IsRemoteDebugEnabled() {
			return err
		}
	}

	sqsClient := sqs.InitSQSClient(awsConf)
	_ = sqsClient

	var broker *events.RudderStackBroker

	// load rudder-stack broker for sending events
	rudderClient, err := analytics.NewWithConfig(
		gconf.Secrets().Ids[config.RudderWriteKey], "http://"+cfg.GetServerEndPoint(cfg.RUDDER_SERVER).URL(),
		analytics.Config{
			Callback:  events.NewCallback(),
			Interval:  gconf.RudderStack().IntervalInSec * time.Second, // nolint: durationcheck
			BatchSize: gconf.RudderStack().BatchSize,
			Verbose:   gconf.RudderStack().Verbose,
		})
	if err != nil {
		logger.Error(ctx, "error in connecting rudder", zap.Error(err))
		return err
	}
	broker = events.NewRudderStackBroker(rudderClient)
	defer broker.Close()

	rateLimiter := ratelimiter.NewRateLimiterV2(store.NewSlidingWindowLogWithRedis(rateLimiterRedisStore), gconf.GrpcRatelimiterParams().RateLimitConfig())
	_ = rateLimiter

	var unaryInterceptors []grpc.UnaryServerInterceptor

	var streamInterceptors []grpc.StreamServerInterceptor

	histogramBuckets := prometheus.DefBuckets
	if len(gconf.LatencyHistogramBuckets()) > 0 {
		histogramBuckets = epifigrpc.AddGrpcLatencyBuckets(gconf.LatencyHistogramBuckets()...)
	}

	serverOptions := []grpc.ServerOption{}
	if gconf.GrpcServerConfig().MaxRecvMsgSize() != nil && gconf.GrpcServerConfig().MaxRecvMsgSize().Size != 0 {
		serverOptions = append(serverOptions, grpc.MaxRecvMsgSize(gconf.GrpcServerConfig().MaxRecvMsgSize().Size))
	}
	var extractFeRespStatusWithErrMsgFunc commoninterceptors.ExtractFeRespStatusWithErrMsgFunc
	extractFeRespStatusWithErrMsgFunc = fepkg.ExtractFeRespStatusWithErrMsgFunc

	s := epifigrpc.NewSecureServerWithInterceptors(gconf.GrpcServerConfig(), gconf.Flags().TrimDebugMessageFromStatus, string(gconf.Name()), histogramBuckets, unaryInterceptors, streamInterceptors, extractFeRespStatusWithErrMsgFunc, serverOptions...)

	if cfg.IsNonProdEnv(gconf.Environment()) {
		reflection.Register(s)
	}

	httpMux := http.NewServeMux()
	_ = httpMux

	var sqsSubscribers []queue.Subscriber
	var subs []queue.Subscriber
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSubs []*sqs.ExtendedSqsSubscriber

	subs, extSubs, err = setupVendormapping(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, vendormappingPGDB, vendorMappingRedisStore)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	explorerpb.RegisterConfigExplorerServer(s, explorer.NewServer(configNameToConfMap))

	// Load rpc level log sampling rate descriptors.
	epifigrpc.GetRPCLogSamplingRatesMapInstance().Load(s)

	initNotifier <- gconf.Name()

	// TODO(Sundeep): Remove this additional logic for starting the servers inside go-routine, by exposing functions
	// from epifiserver package that take errgroup.Group as parameter.
	grp, _ := errgroup.WithContext(context.Background())

	grp.Go(func() error {
		epifiserver.StartSecureServer(s, gconf.ServerPorts(), string(gconf.Name()))
		return nil
	})

	// Wait for server health before starting SQS workers
	if queue.IsWorkerInitializationEnabled() {
		goroutine.RunWithCtx(ctx, func(ctx context.Context) {
			if epifiserver.IsServerReady(gconf.ServerPorts().HttpPort) {
				logger.Info(ctx, "Server is healthy, starting SQS workers")
				sqs.StartSQSWorkers(ctx, sqsSubscribers, extendedSqsSubscribers)
			} else {
				logger.PanicWithCtx(ctx, "Server health check timed out, not starting SQS workers")
			}
		})
	}
	if err := grp.Wait(); err != nil {
		return err
	}

	return nil
}

// nolint: funlen
func setupVendormapping(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	vendormappingPGDB types.VendormappingPGDB,
	vendorMappingRedisStore types.VendorMappingRedisStore) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	vendormappingConf, err := vendormappingconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.VENDORMAPPING_SERVICE))
		return nil, nil, err
	}
	_ = vendormappingConf

	vendormappingGenConf, err := dynconf.LoadConfig(vendormappingconf.Load, genconf2.NewConfig, cfg.VENDORMAPPING_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.VENDORMAPPING_SERVICE))
		return nil, nil, err
	}

	_ = vendormappingGenConf

	vendorMappingService := wire.InitializeService(vendormappingPGDB, vendorMappingRedisStore, vendormappingGenConf)

	vmpb.RegisterVendorMappingServiceServer(s, vendorMappingService)

	vendorMappingConsumerService := wire.InitializeConsumerService(vendormappingPGDB, vendorMappingRedisStore, vendormappingGenConf)

	vmconsumerpb.RegisterVendorMappingConsumerServer(s, vendorMappingConsumerService)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, vendormappingGenConf.ActorCreationEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		vmconsumerpb.RegisterProcessActorCreationEventMethodToSubscriber(subscriber, vendorMappingConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, vendormappingGenConf.VendorMappingConsentSyncSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		vmconsumerpb.RegisterVendorMappingActorSyncToSubscriber(subscriber, vendorMappingConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	vendorMappingDev := wire.InitializeVendorMappingDevService(vendormappingPGDB, vendorMappingRedisStore, vendormappingGenConf)

	developer2.RegisterDevVendorMappingServer(s, vendorMappingDev)

	configNameToConfMap[cfg.ConfigName(cfg.VENDORMAPPING_SERVICE)] = &commonexplorer.Config{StaticConf: &vendormappingconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

func initLogger() error {
	// Load configuration
	conf, err := config.Load(cfg.VENDORMAPPING_SERVER)
	if err != nil {
		// cannot log error since logger is not initialized yet.
		return err
	}

	logger.InitV2(conf.Environment, conf.Logging)
	if conf.SecureLogging != nil {
		logger.InitSecureLoggerV2(conf.SecureLogging)
	}
	return nil
}

func getEnvNamespaceClient(ns temporalpkg.Namespace, env string) string {
	if cfg.IsLocalEnv(env) {
		return strings.ToLower(string(ns))
	}

	return fmt.Sprintf("%s-%s", env, strings.ToLower(string(ns)))
}

func getSQSConsumerInterceptors() []grpc.UnaryServerInterceptor {
	return []grpc.UnaryServerInterceptor{questinterceptor.UserContextInterceptor}
}
