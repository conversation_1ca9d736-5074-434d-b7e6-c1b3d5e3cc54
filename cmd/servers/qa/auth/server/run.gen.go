// code generated by tools/servergen
package server

import (
	context "context"
	"fmt"
	"net/http"
	strings "strings"
	time "time"

	awssdk "github.com/aws/aws-sdk-go-v2/aws"
	awssqs "github.com/aws/aws-sdk-go-v2/service/sqs"
	prometheus "github.com/prometheus/client_golang/prometheus"
	redis "github.com/redis/go-redis/v9"
	analytics "github.com/rudderlabs/analytics-go"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
	reflection "google.golang.org/grpc/reflection"
	gorm "gorm.io/gorm"

	celestialpb "github.com/epifi/be-common/api/celestial"
	devcache "github.com/epifi/be-common/api/developer/devcache"
	"github.com/epifi/be-common/pkg/async/goroutine"
	awsconfpkg "github.com/epifi/be-common/pkg/aws/v2/config"
	s3pkg "github.com/epifi/be-common/pkg/aws/v2/s3"
	sns "github.com/epifi/be-common/pkg/aws/v2/sns"
	sqs "github.com/epifi/be-common/pkg/aws/v2/sqs"
	cache "github.com/epifi/be-common/pkg/cache"
	cfg "github.com/epifi/be-common/pkg/cfg"
	consul "github.com/epifi/be-common/pkg/cfg/consul"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	commonexplorer "github.com/epifi/be-common/pkg/cfg/explorer"
	config "github.com/epifi/be-common/pkg/cmd/config"
	genconf "github.com/epifi/be-common/pkg/cmd/config/genconf"
	types "github.com/epifi/be-common/pkg/cmd/types"
	epificontext "github.com/epifi/be-common/pkg/epificontext"
	epifigrpc "github.com/epifi/be-common/pkg/epifigrpc"
	commoninterceptors "github.com/epifi/be-common/pkg/epifigrpc/interceptors"
	servergenwire2 "github.com/epifi/be-common/pkg/epifigrpc/interceptors/servergen_wire"
	resolver "github.com/epifi/be-common/pkg/epifigrpc/resolver"
	epifiserver "github.com/epifi/be-common/pkg/epifiserver"
	temporalpkg "github.com/epifi/be-common/pkg/epifitemporal"
	errgroup "github.com/epifi/be-common/pkg/errgroup"
	events "github.com/epifi/be-common/pkg/events"
	logger "github.com/epifi/be-common/pkg/logger"
	profiling "github.com/epifi/be-common/pkg/profiling"
	queue "github.com/epifi/be-common/pkg/queue"
	ratelimiter "github.com/epifi/be-common/pkg/ratelimiter"
	store "github.com/epifi/be-common/pkg/ratelimiter/store"
	storage "github.com/epifi/be-common/pkg/storage"
	storage2 "github.com/epifi/be-common/pkg/storage/v2"
	otel "github.com/epifi/be-common/pkg/tracing/opentelemetry"
	accountbalancepb "github.com/epifi/gamma/api/accounts/balance"
	operationalstatuspb "github.com/epifi/gamma/api/accounts/operstatus"
	actor "github.com/epifi/gamma/api/actor"
	authpb "github.com/epifi/gamma/api/auth"
	authorizerpb "github.com/epifi/gamma/api/auth/authorizer"
	biometricspb "github.com/epifi/gamma/api/auth/biometrics"
	authconsumerpb "github.com/epifi/gamma/api/auth/consumer"
	authdevpb "github.com/epifi/gamma/api/auth/developer"
	liveness "github.com/epifi/gamma/api/auth/liveness"
	locationpb "github.com/epifi/gamma/api/auth/location"
	authv2pb "github.com/epifi/gamma/api/auth/orchestrator"
	authorchdevpb "github.com/epifi/gamma/api/auth/orchestrator/developer"
	authpartnersdkpb "github.com/epifi/gamma/api/auth/partnersdk"
	sessionpb "github.com/epifi/gamma/api/auth/session"
	totp2 "github.com/epifi/gamma/api/auth/totp"
	bankcust "github.com/epifi/gamma/api/bankcust"
	cardpb "github.com/epifi/gamma/api/card/provisioning"
	comms "github.com/epifi/gamma/api/comms"
	devicetoken "github.com/epifi/gamma/api/comms/device_token"
	uppb "github.com/epifi/gamma/api/comms/user_preference"
	beconnectedaccpb "github.com/epifi/gamma/api/connected_account"
	consent "github.com/epifi/gamma/api/consent"
	creditreportv "github.com/epifi/gamma/api/creditreportv2"
	derivedattributespb "github.com/epifi/gamma/api/creditreportv2/derivedattributes"
	watson "github.com/epifi/gamma/api/cx/watson"
	employment "github.com/epifi/gamma/api/employment"
	fireflypb "github.com/epifi/gamma/api/firefly"
	ffaccpb "github.com/epifi/gamma/api/firefly/accounting"
	ffv2pb "github.com/epifi/gamma/api/firefly/v2"
	healthengine "github.com/epifi/gamma/api/health_engine"
	inappreferralpb "github.com/epifi/gamma/api/inappreferral"
	mfpb "github.com/epifi/gamma/api/investment/mutualfund/external"
	kycpb "github.com/epifi/gamma/api/kyc"
	agent "github.com/epifi/gamma/api/kyc/agent"
	kycdocspb "github.com/epifi/gamma/api/kyc/docs"
	vkycpb "github.com/epifi/gamma/api/kyc/vkyc"
	leadspb "github.com/epifi/gamma/api/leads"
	omegle "github.com/epifi/gamma/api/omegle"
	orderpb "github.com/epifi/gamma/api/order"
	pan "github.com/epifi/gamma/api/pan"
	paypb "github.com/epifi/gamma/api/pay"
	explorerpb "github.com/epifi/gamma/api/pkg/cfg/explorer"
	palpb "github.com/epifi/gamma/api/preapprovedloan"
	productpb "github.com/epifi/gamma/api/product"
	managerpb "github.com/epifi/gamma/api/quest/manager"
	riskpb "github.com/epifi/gamma/api/risk"
	casemanagement "github.com/epifi/gamma/api/risk/case_management"
	redlist "github.com/epifi/gamma/api/risk/redlist"
	savings "github.com/epifi/gamma/api/savings"
	screener "github.com/epifi/gamma/api/screener"
	segmentpb "github.com/epifi/gamma/api/segment"
	simauthfederal "github.com/epifi/gamma/api/simulator/openbanking/auth/federal"
	tiering "github.com/epifi/gamma/api/tiering"
	_ "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/screen_option_dependency"
	upipb "github.com/epifi/gamma/api/upi"
	user "github.com/epifi/gamma/api/user"
	accessrevokeconsumerpb "github.com/epifi/gamma/api/user/accessrevoke/consumer"
	usercontactpb "github.com/epifi/gamma/api/user/contact"
	developer4 "github.com/epifi/gamma/api/user/developer"
	event2 "github.com/epifi/gamma/api/user/event"
	usergrouppb "github.com/epifi/gamma/api/user/group"
	userlocation "github.com/epifi/gamma/api/user/location"
	obfuscatorpb "github.com/epifi/gamma/api/user/obfuscator"
	onbpb "github.com/epifi/gamma/api/user/onboarding"
	vkyc "github.com/epifi/gamma/api/user/onboarding/vkyc"
	onboardingwatsonclientpb "github.com/epifi/gamma/api/user/onboarding/watson"
	vkycconsumer "github.com/epifi/gamma/api/user/vkyc_consumer"
	userintel "github.com/epifi/gamma/api/userintel"
	ippb "github.com/epifi/gamma/api/vendordata/ip"
	vgdocpb "github.com/epifi/gamma/api/vendorgateway/docs"
	ekycpb "github.com/epifi/gamma/api/vendorgateway/ekyc"
	vglivenesspb "github.com/epifi/gamma/api/vendorgateway/liveness"
	locationvgpb "github.com/epifi/gamma/api/vendorgateway/location"
	vgmoengagepb "github.com/epifi/gamma/api/vendorgateway/moengage"
	namecheck "github.com/epifi/gamma/api/vendorgateway/namecheck"
	vgauth "github.com/epifi/gamma/api/vendorgateway/openbanking/auth"
	vgpartnersdkpb "github.com/epifi/gamma/api/vendorgateway/openbanking/auth/partnersdk"
	ovgcustomerpb "github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	deposit "github.com/epifi/gamma/api/vendorgateway/openbanking/deposit"
	vgpan "github.com/epifi/gamma/api/vendorgateway/pan"
	vmpb "github.com/epifi/gamma/api/vendormapping"
	vkyccalltroubleshootpb "github.com/epifi/gamma/api/vkyccall/troubleshoot"
	wire3 "github.com/epifi/gamma/auth/biometrics/wire"
	authconf "github.com/epifi/gamma/auth/config"
	genconf2 "github.com/epifi/gamma/auth/config/genconf"
	wire2 "github.com/epifi/gamma/auth/orchestrator/wire"
	wire "github.com/epifi/gamma/auth/wire"
	wiretypes "github.com/epifi/gamma/auth/wire/types"
	hook "github.com/epifi/gamma/cmd/servers/qa/auth/hook"
	types2 "github.com/epifi/gamma/comms/wire/types"
	wire6 "github.com/epifi/gamma/consent/wire"
	explorer "github.com/epifi/gamma/pkg/cfg/explorer"
	questinterceptor "github.com/epifi/gamma/pkg/epifigrpc/interceptors/quest"
	servergenwire "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
	fepkg "github.com/epifi/gamma/pkg/frontend/msg"
	questsdkinit "github.com/epifi/gamma/quest/sdk/init"
	userconf "github.com/epifi/gamma/user/config"
	genconf3 "github.com/epifi/gamma/user/config/genconf"
	wire5 "github.com/epifi/gamma/user/onboarding/wire"
	wire4 "github.com/epifi/gamma/user/wire"
	wiretypes3 "github.com/epifi/gamma/user/wire/types"
)

// nolint: funlen
func Run(ctx context.Context, initNotifier chan<- cfg.ServerName) error {
	// Panic handler for logging panics in standard format
	epifiserver.HandlePanic()
	resolver.RegisterBuilder(resolver.ConsulSchemeName)
	var configNameToConfMap = make(commonexplorer.CfgExplorerMap)

	err := initLogger()
	if err != nil {
		// cannot log error since logger maynot be initialized yet.
		return err
	}
	ctx = epificontext.WithTraceId(ctx, metadata.MD{})
	// Load static configuration
	conf, err := config.Load(cfg.AUTH_SERVER)
	if err != nil {
		logger.Error(ctx, "failed to load static conf", zap.Error(err))
		return err
	}
	_ = conf
	// Load generated dynamic configuration
	gconf, err := genconf.Load(cfg.AUTH_SERVER)
	if err != nil {
		logger.Error(ctx, "failed to load dynamic conf", zap.Error(err))
		return err
	}
	logger.Info(ctx, "initiating server")

	if gconf.Profiling().AutomaticProfiling().EnableAutoProfiling() {
		profilingErr := profiling.AutomaticProfiling(context.Background(), cfg.AUTH_SERVER, gconf.Profiling(), gconf.Environment(), gconf.AWS().Region)
		if profilingErr != nil {
			logger.Error(ctx, "failed to start automatic profiling", zap.Error(profilingErr))
			return profilingErr
		}
	}

	if gconf.Tracing().Enable {
		_, shutdown, tracingErr := otel.ConfigureOpenTelemetryV2(context.Background(), cfg.AUTH_SERVER, gconf.Environment(), gconf.Profiling().PyroscopeProfiling().EnablePyroscope())
		if tracingErr != nil {
			logger.Error(ctx, "failed to configure open telemetry", zap.Error(tracingErr))
			return err
		}
		defer shutdown()
	}

	err = profiling.PyroscopeProfilingV2(gconf.Name(), gconf.Profiling(), gconf.Environment())
	if err != nil {
		logger.Error(ctx, "error starting pyroscope profiler", zap.Error(err))
	}

	if !cfg.IsLocalEnv(gconf.Environment()) && !cfg.IsRemoteDebugEnabled() && !cfg.IsTestTenantEnabled() && !cfg.IsTeamSpaceTenant() {
		initDisc, _ := consul.GetBool(consul.GetInitDiscoveryKey(gconf.Environment(), string(gconf.Name())))
		queue.SetWorkerInitialization(initDisc)
	}

	rateLimiterRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()[gconf.RedisRateLimiterName()], gconf.Tracing().Enable)
	defer func() { _ = rateLimiterRedisStore.Close() }()

	var (
		crdbResourceMap    *storage2.DBResourceProvider[*gorm.DB]
		crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor]
	)

	var dbConnTeardown func()
	crdbResourceMap, crdbTxnResourceMap, dbConnTeardown, err = storage2.NewDBResourceProviderV2(conf.DBConfigMap.GetOwnershipToDbConfigMap(), conf.Tracing.Enable, gconf.PgdbConns())
	if err != nil {
		logger.Error(ctx, "failed to get db resource provider", zap.Error(err))
		return err
	}
	defer func() {
		dbConnTeardown()
	}()

	epifiCRDB, err := storage2.NewCRDBWithConfig(gconf.Databases()["EpifiCRDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "EpifiCRDB"))
		return err
	}
	epifiCRDBSqlDb, err := epifiCRDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "EpifiCRDB"))
		return err
	}
	defer func() { _ = epifiCRDBSqlDb.Close() }()

	authPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["AuthPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "AuthPGDB"))
		return err
	}
	authPGDBSqlDb, err := authPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "AuthPGDB"))
		return err
	}
	defer func() { _ = authPGDBSqlDb.Close() }()
	userPropertiesPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["UserPropertiesPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "UserPropertiesPGDB"))
		return err
	}
	userPropertiesPGDBSqlDb, err := userPropertiesPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "UserPropertiesPGDB"))
		return err
	}
	defer func() { _ = userPropertiesPGDBSqlDb.Close() }()
	nudgePGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["NudgePGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "NudgePGDB"))
		return err
	}
	nudgePGDBSqlDb, err := nudgePGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "NudgePGDB"))
		return err
	}
	defer func() { _ = nudgePGDBSqlDb.Close() }()

	collapserRueidisRedisStore, err := storage2.NewRueidisClientFromConfig(gconf.RueidisRedisClients()["CollapserRueidisRedisStore"])
	if err != nil {
		logger.Error(ctx, "failed to establish rueidis redis client conn for CollapserRueidisRedisStore", zap.Error(err))
		return err
	}
	defer func() { collapserRueidisRedisStore.Close() }()
	collapserRueidisCacheStorage := cache.NewRueidisCacheStorageWithHystrix(cache.NewRueidisCacheWithOptions(collapserRueidisRedisStore), gconf.RueidisRedisClients()["CollapserRueidisRedisStore"].Hystrix)
	growthinfraConn := epifigrpc.NewServerConn(cfg.AUTH_SERVER, cfg.GROWTH_INFRA_SERVER)
	defer epifigrpc.CloseConn(growthinfraConn)
	managerClient := managerpb.NewManagerClient(growthinfraConn)
	authConn := epifigrpc.NewServerConn(cfg.AUTH_SERVER, cfg.AUTH_SERVER)
	defer epifigrpc.CloseConn(authConn)
	groupClient := usergrouppb.NewGroupClient(authConn)
	usersClient := user.NewUsersClient(authConn)
	actorConn := epifigrpc.NewServerConn(cfg.AUTH_SERVER, cfg.ACTOR_SERVER)
	defer epifigrpc.CloseConn(actorConn)
	actorClient := actor.NewActorClient(actorConn)
	segmentationServiceClient := segmentpb.NewSegmentationServiceClient(growthinfraConn)
	growthinfraConnVar5ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptor := servergenwire.NewAuthRequestClientInterceptor()
	if unaryClientInterceptor != nil {
		growthinfraConnVar5ClientInterceptors = append(growthinfraConnVar5ClientInterceptors, unaryClientInterceptor)
	}
	growthinfraConnVar5 := epifigrpc.NewServerConn(cfg.AUTH_SERVER, cfg.GROWTH_INFRA_SERVER, growthinfraConnVar5ClientInterceptors...)
	defer epifigrpc.CloseConn(growthinfraConnVar5)
	commsClient := comms.NewCommsClient(growthinfraConnVar5)
	vendorgatewayConn := epifigrpc.NewServerConn(cfg.AUTH_SERVER, cfg.VENDOR_GATEWAY_SERVER)
	defer epifigrpc.CloseConn(vendorgatewayConn)
	vendorAuthClient := vgauth.NewVendorAuthClient(vendorgatewayConn)
	centralgrowthConn := epifigrpc.NewServerConn(cfg.AUTH_SERVER, cfg.CENTRAL_GROWTH_SERVER)
	defer epifigrpc.CloseConn(centralgrowthConn)
	savingsClient := savings.NewSavingsClient(centralgrowthConn)
	cardConn := epifigrpc.NewServerConn(cfg.AUTH_SERVER, cfg.CARD_SERVER)
	defer epifigrpc.CloseConn(cardConn)
	cardProvisioningClient := cardpb.NewCardProvisioningClient(cardConn)
	authRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["AuthRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = authRedisStore.Close() }()
	authTokenRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["AuthTokenRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = authTokenRedisStore.Close() }()
	authDeviceIntegrityNonceRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["AuthDeviceIntegrityNonceRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = authDeviceIntegrityNonceRedisStore.Close() }()
	authDeviceIntegrityNonceCacheStorage := cache.NewRedisCacheStorageWithHystrix(cache.NewRedisCacheStorage(authDeviceIntegrityNonceRedisStore), gconf.RedisClusters()["AuthDeviceIntegrityNonceRedisStore"].HystrixCommand)
	authDeviceRegistrationsRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["AuthDeviceRegistrationsRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = authDeviceRegistrationsRedisStore.Close() }()
	authDeviceRegistrationsCacheStorage := cache.NewRedisCacheStorageWithHystrix(cache.NewRedisCacheStorage(authDeviceRegistrationsRedisStore), gconf.RedisClusters()["AuthDeviceRegistrationsRedisStore"].HystrixCommand)
	onboardingConn := epifigrpc.NewServerConn(cfg.AUTH_SERVER, cfg.ONBOARDING_SERVER)
	defer epifigrpc.CloseConn(onboardingConn)
	livenessClient := liveness.NewLivenessClient(onboardingConn)
	consentClient := consent.NewConsentClient(authConn)
	bankCustomerServiceClient := bankcust.NewBankCustomerServiceClient(onboardingConn)
	userriskConn := epifigrpc.NewServerConn(cfg.AUTH_SERVER, cfg.USER_RISK_SERVER)
	defer epifigrpc.CloseConn(userriskConn)
	redListClient := redlist.NewRedListClient(userriskConn)
	riskClient := riskpb.NewRiskClient(userriskConn)
	simulatorConn := epifigrpc.NewServerConn(cfg.AUTH_SERVER, cfg.SIMULATOR_GRPC_SERVER)
	defer epifigrpc.CloseConn(simulatorConn)
	authClient := simauthfederal.NewAuthClient(simulatorConn)
	docsConn := epifigrpc.NewServerConn(cfg.AUTH_SERVER, cfg.DOCS_SERVER)
	defer epifigrpc.CloseConn(docsConn)
	healthEngineServiceClient := healthengine.NewHealthEngineServiceClient(docsConn)
	caseManagementClient := casemanagement.NewCaseManagementClient(userriskConn)
	kycAgentServiceClient := agent.NewKycAgentServiceClient(onboardingConn)
	growthinfraConnVar6ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar2 := servergenwire.NewAuthRequestClientInterceptor()
	if unaryClientInterceptorVar2 != nil {
		growthinfraConnVar6ClientInterceptors = append(growthinfraConnVar6ClientInterceptors, unaryClientInterceptorVar2)
	}
	growthinfraConnVar6 := epifigrpc.NewServerConn(cfg.AUTH_SERVER, cfg.GROWTH_INFRA_SERVER, growthinfraConnVar6ClientInterceptors...)
	defer epifigrpc.CloseConn(growthinfraConnVar6)
	fCMDeviceTokenClient := devicetoken.NewFCMDeviceTokenClient(growthinfraConnVar6)
	operationalStatusServiceClient := operationalstatuspb.NewOperationalStatusServiceClient(actorConn)
	productClient := productpb.NewProductClient(onboardingConn)
	kycClient := kycpb.NewKycClient(onboardingConn)
	onboardingClient := onbpb.NewOnboardingClient(authConn)
	orderConn := epifigrpc.NewServerConn(cfg.AUTH_SERVER, cfg.ORDER_SERVER)
	defer epifigrpc.CloseConn(orderConn)
	payClient := paypb.NewPayClient(orderConn)
	authClientVar2 := authpb.NewAuthClient(authConn)
	partnerSDKClient := vgpartnersdkpb.NewPartnerSDKClient(vendorgatewayConn)
	accountingClient := ffaccpb.NewAccountingClient(cardConn)
	authDeviceLocationRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["AuthDeviceLocationRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = authDeviceLocationRedisStore.Close() }()
	authDeviceLocationCacheStorage := cache.NewRedisCacheStorageWithHystrix(cache.NewRedisCacheStorage(authDeviceLocationRedisStore), gconf.RedisClusters()["AuthDeviceLocationRedisStore"].HystrixCommand)
	nebulaConn := epifigrpc.NewServerConn(cfg.AUTH_SERVER, cfg.NEBULA_SERVER)
	defer epifigrpc.CloseConn(nebulaConn)
	celestialClient := celestialpb.NewCelestialClient(nebulaConn)
	uPIClient := upipb.NewUPIClient(orderConn)
	sessionAuthRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["SessionAuthRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = sessionAuthRedisStore.Close() }()
	customerClient := ovgcustomerpb.NewCustomerClient(vendorgatewayConn)
	vKYCClient := vkycpb.NewVKYCClient(onboardingConn)
	depositClient := deposit.NewDepositClient(vendorgatewayConn)
	commsClientVar2 := comms.NewCommsClient(growthinfraConn)
	userRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["UserRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = userRedisStore.Close() }()
	userDevicePropertiesRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["UserDevicePropertiesRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = userDevicePropertiesRedisStore.Close() }()
	minimalUserRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["MinimalUserRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = minimalUserRedisStore.Close() }()
	screenerClient := screener.NewScreenerClient(onboardingConn)
	employmentClient := employment.NewEmploymentClient(onboardingConn)
	userRueidisRedisStore, err := storage2.NewRueidisClientFromConfig(gconf.RueidisRedisClients()["UserRueidisRedisStore"])
	if err != nil {
		logger.Error(ctx, "failed to establish rueidis redis client conn for UserRueidisRedisStore", zap.Error(err))
		return err
	}
	defer func() { userRueidisRedisStore.Close() }()
	userRueidisCacheStorage := cache.NewRueidisCacheStorageWithHystrix(cache.NewRueidisCacheWithOptions(userRueidisRedisStore), gconf.RueidisRedisClients()["UserRueidisRedisStore"].Hystrix)
	minimalUserRueidisRedisStore, err := storage2.NewRueidisClientFromConfig(gconf.RueidisRedisClients()["MinimalUserRueidisRedisStore"])
	if err != nil {
		logger.Error(ctx, "failed to establish rueidis redis client conn for MinimalUserRueidisRedisStore", zap.Error(err))
		return err
	}
	defer func() { minimalUserRueidisRedisStore.Close() }()
	minimalUserRueidisCacheStorage := cache.NewRueidisCacheStorageWithHystrix(cache.NewRueidisCacheWithOptions(minimalUserRueidisRedisStore), gconf.RueidisRedisClients()["MinimalUserRueidisRedisStore"].Hystrix)
	panClient := pan.NewPanClient(onboardingConn)
	docExtractionClient := kycdocspb.NewDocExtractionClient(onboardingConn)
	lendingConn := epifigrpc.NewServerConn(cfg.AUTH_SERVER, cfg.LENDING_SERVER)
	defer epifigrpc.CloseConn(lendingConn)
	userLeadSvcClient := leadspb.NewUserLeadSvcClient(lendingConn)
	orderServiceClient := orderpb.NewOrderServiceClient(orderConn)
	inAppReferralClient := inappreferralpb.NewInAppReferralClient(onboardingConn)
	eKYCClient := ekycpb.NewEKYCClient(vendorgatewayConn)
	userPreferenceClient := uppb.NewUserPreferenceClient(growthinfraConn)
	uNNameCheckClient := namecheck.NewUNNameCheckClient(vendorgatewayConn)
	locationClient := userlocation.NewLocationClient(authConn)
	obfuscatorClient := obfuscatorpb.NewObfuscatorClient(authConn)
	locationClientVar2 := locationpb.NewLocationClient(authConn)
	employmentFeClient := employment.NewEmploymentFeClient(onboardingConn)
	derivedAttributesManagerClient := derivedattributespb.NewDerivedAttributesManagerClient(lendingConn)
	userIntelServiceClient := userintel.NewUserIntelServiceClient(onboardingConn)
	cxConn := epifigrpc.NewServerConn(cfg.AUTH_SERVER, cfg.CX_SERVER)
	defer epifigrpc.CloseConn(cxConn)
	watsonClient := watson.NewWatsonClient(cxConn)
	balanceClient := accountbalancepb.NewBalanceClient(actorConn)
	fireflyClient := fireflypb.NewFireflyClient(cardConn)
	preApprovedLoanClient := palpb.NewPreApprovedLoanClient(lendingConn)
	pANClient := vgpan.NewPANClient(vendorgatewayConn)
	creditReportManagerClient := creditreportv.NewCreditReportManagerClient(lendingConn)
	onboardingRueidisRedisStore, err := storage2.NewRueidisClientFromConfig(gconf.RueidisRedisClients()["OnboardingRueidisRedisStore"])
	if err != nil {
		logger.Error(ctx, "failed to establish rueidis redis client conn for OnboardingRueidisRedisStore", zap.Error(err))
		return err
	}
	defer func() { onboardingRueidisRedisStore.Close() }()
	onboardingRueidisCacheStorage := cache.NewRueidisCacheStorageWithHystrix(cache.NewRueidisCacheWithOptions(onboardingRueidisRedisStore), gconf.RueidisRedisClients()["OnboardingRueidisRedisStore"].Hystrix)
	omegleClient := omegle.NewOmegleClient(onboardingConn)
	docsClient := vgdocpb.NewDocsClient(vendorgatewayConn)
	onboardingMinRueidisRedisStore, err := storage2.NewRueidisClientFromConfig(gconf.RueidisRedisClients()["OnboardingMinRueidisRedisStore"])
	if err != nil {
		logger.Error(ctx, "failed to establish rueidis redis client conn for OnboardingMinRueidisRedisStore", zap.Error(err))
		return err
	}
	defer func() { onboardingMinRueidisRedisStore.Close() }()
	onboardingMinRueidisCacheStorage := cache.NewRueidisCacheStorageWithHystrix(cache.NewRueidisCacheWithOptions(onboardingMinRueidisRedisStore), gconf.RueidisRedisClients()["OnboardingMinRueidisRedisStore"].Hystrix)
	troubleshootClient := vkyccalltroubleshootpb.NewTroubleshootClient(onboardingConn)
	tieringClient := tiering.NewTieringClient(centralgrowthConn)
	livenessClientVar5 := vglivenesspb.NewLivenessClient(vendorgatewayConn)
	questRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["QuestRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = questRedisStore.Close() }()
	questCacheStorage := cache.NewRedisCacheStorageWithHystrix(cache.NewRedisCacheStorage(questRedisStore), gconf.RedisClusters()["QuestRedisStore"].HystrixCommand)
	wealthdmfConn := epifigrpc.NewServerConn(cfg.AUTH_SERVER, cfg.WEALTH_DMF_SERVER)
	defer epifigrpc.CloseConn(wealthdmfConn)
	mFExternalOrdersClient := mfpb.NewMFExternalOrdersClient(wealthdmfConn)
	connectedAccountClient := beconnectedaccpb.NewConnectedAccountClient(centralgrowthConn)
	fireflyV2Client := ffv2pb.NewFireflyV2Client(cardConn)
	locationClientVar3 := locationvgpb.NewLocationClient(vendorgatewayConn)
	ipServiceClient := ippb.NewIpServiceClient(onboardingConn)
	userContactRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["UserContactRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = userContactRedisStore.Close() }()
	userGroupRueidisRedisStore, err := storage2.NewRueidisClientFromConfig(gconf.RueidisRedisClients()["UserGroupRueidisRedisStore"])
	if err != nil {
		logger.Error(ctx, "failed to establish rueidis redis client conn for UserGroupRueidisRedisStore", zap.Error(err))
		return err
	}
	defer func() { userGroupRueidisRedisStore.Close() }()
	userGroupRueidisCacheStorage := cache.NewRueidisCacheStorageWithHystrix(cache.NewRueidisCacheWithOptions(userGroupRueidisRedisStore), gconf.RueidisRedisClients()["UserGroupRueidisRedisStore"].Hystrix)
	vendormappingConn := epifigrpc.NewServerConn(cfg.AUTH_SERVER, cfg.VENDORMAPPING_SERVER)
	defer epifigrpc.CloseConn(vendormappingConn)
	vendorMappingServiceClient := vmpb.NewVendorMappingServiceClient(vendormappingConn)
	moEngageClient := vgmoengagepb.NewMoEngageClient(vendorgatewayConn)

	awsConf, awsErr := awsconfpkg.NewAWSConfig(ctx, gconf.AWS().Region, gconf.Tracing().Enable)
	if awsErr != nil {
		logger.Error(ctx, "error in loading aws v2 config", zap.Error(awsErr))
		if !cfg.IsRemoteDebugEnabled() {
			return err
		}
	}

	sqsClient := sqs.InitSQSClient(awsConf)
	_ = sqsClient

	var broker *events.RudderStackBroker

	// load rudder-stack broker for sending events
	rudderClient, err := analytics.NewWithConfig(
		gconf.Secrets().Ids[config.RudderWriteKey], "http://"+cfg.GetServerEndPoint(cfg.RUDDER_SERVER).URL(),
		analytics.Config{
			Callback:  events.NewCallback(),
			Interval:  gconf.RudderStack().IntervalInSec * time.Second, // nolint: durationcheck
			BatchSize: gconf.RudderStack().BatchSize,
			Verbose:   gconf.RudderStack().Verbose,
		})
	if err != nil {
		logger.Error(ctx, "error in connecting rudder", zap.Error(err))
		return err
	}
	broker = events.NewRudderStackBroker(rudderClient)
	defer broker.Close()

	rateLimiter := ratelimiter.NewRateLimiterV2(store.NewSlidingWindowLogWithRedis(rateLimiterRedisStore), gconf.GrpcRatelimiterParams().RateLimitConfig())
	_ = rateLimiter

	var unaryInterceptors []grpc.UnaryServerInterceptor

	unaryServerInterceptor := servergenwire.AddQuestUserContextUnaryInterceptor(gconf)
	if unaryServerInterceptor != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptor)
	}

	unaryServerInterceptorVar2, err := servergenwire2.CollapserInterceptor(collapserRueidisRedisStore, collapserRueidisCacheStorage)
	if err != nil {
		logger.Error(ctx, "failed to init grpc unary interceptor", zap.Error(err))
		return err
	}
	if unaryServerInterceptorVar2 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar2)
	}

	var streamInterceptors []grpc.StreamServerInterceptor

	histogramBuckets := prometheus.DefBuckets
	if len(gconf.LatencyHistogramBuckets()) > 0 {
		histogramBuckets = epifigrpc.AddGrpcLatencyBuckets(gconf.LatencyHistogramBuckets()...)
	}

	serverOptions := []grpc.ServerOption{}
	if gconf.GrpcServerConfig().MaxRecvMsgSize() != nil && gconf.GrpcServerConfig().MaxRecvMsgSize().Size != 0 {
		serverOptions = append(serverOptions, grpc.MaxRecvMsgSize(gconf.GrpcServerConfig().MaxRecvMsgSize().Size))
	}
	var extractFeRespStatusWithErrMsgFunc commoninterceptors.ExtractFeRespStatusWithErrMsgFunc
	extractFeRespStatusWithErrMsgFunc = fepkg.ExtractFeRespStatusWithErrMsgFunc

	s := epifigrpc.NewSecureServerWithInterceptors(gconf.GrpcServerConfig(), gconf.Flags().TrimDebugMessageFromStatus, string(gconf.Name()), histogramBuckets, unaryInterceptors, streamInterceptors, extractFeRespStatusWithErrMsgFunc, serverOptions...)

	if cfg.IsNonProdEnv(gconf.Environment()) {
		reflection.Register(s)
	}

	httpMux := http.NewServeMux()
	_ = httpMux

	var sqsSubscribers []queue.Subscriber
	var subs []queue.Subscriber
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSubs []*sqs.ExtendedSqsSubscriber

	subs, extSubs, err = setupAuth(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, epifiCRDB, authPGDB, commsClient, vendorAuthClient, savingsClient, cardProvisioningClient, authRedisStore, authTokenRedisStore, authDeviceIntegrityNonceCacheStorage, authDeviceRegistrationsCacheStorage, livenessClient, consentClient, bankCustomerServiceClient, redListClient, riskClient, authClient, healthEngineServiceClient, caseManagementClient, kycAgentServiceClient, fCMDeviceTokenClient, operationalStatusServiceClient, productClient, kycClient, onboardingClient, payClient, authClientVar2, partnerSDKClient, accountingClient, authDeviceLocationCacheStorage, celestialClient, uPIClient, sessionAuthRedisStore)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupUser(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, epifiCRDB, kycClient, customerClient, savingsClient, authClientVar2, vKYCClient, depositClient, commsClientVar2, userRedisStore, userDevicePropertiesRedisStore, minimalUserRedisStore, livenessClient, bankCustomerServiceClient, screenerClient, employmentClient, productClient, userRueidisCacheStorage, minimalUserRueidisCacheStorage, panClient, userPropertiesPGDB, onboardingClient, docExtractionClient, userLeadSvcClient, cardProvisioningClient, consentClient, orderServiceClient, inAppReferralClient, eKYCClient, userPreferenceClient, uNNameCheckClient, locationClient, obfuscatorClient, locationClientVar2, riskClient, employmentFeClient, derivedAttributesManagerClient, userIntelServiceClient, nudgePGDB, watsonClient, operationalStatusServiceClient, balanceClient, fireflyClient, preApprovedLoanClient, pANClient, creditReportManagerClient, onboardingRueidisCacheStorage, omegleClient, docsClient, onboardingMinRueidisCacheStorage, troubleshootClient, tieringClient, livenessClientVar5, questCacheStorage, mFExternalOrdersClient, connectedAccountClient, fireflyV2Client, locationClientVar3, ipServiceClient, userContactRedisStore, userGroupRueidisCacheStorage, vendorMappingServiceClient, moEngageClient)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	explorerpb.RegisterConfigExplorerServer(s, explorer.NewServer(configNameToConfMap))

	cleanupFn, err := hook.AuthBeforeServerStartHook(epifiCRDB) // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "AuthBeforeServerStartHook"), zap.Error(err))
		return err
	}
	defer cleanupFn()

	cleanupFnVar2, err := servergenwire2.LoadCollapserWithGrpcServer(gconf, s) // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "LoadCollapserWithGrpcServer"), zap.Error(err))
		return err
	}
	defer cleanupFnVar2()

	// Load rpc level log sampling rate descriptors.
	epifigrpc.GetRPCLogSamplingRatesMapInstance().Load(s)

	initNotifier <- gconf.Name()

	// TODO(Sundeep): Remove this additional logic for starting the servers inside go-routine, by exposing functions
	// from epifiserver package that take errgroup.Group as parameter.
	grp, _ := errgroup.WithContext(context.Background())

	grp.Go(func() error {
		epifiserver.StartSecureServer(s, gconf.ServerPorts(), string(gconf.Name()))
		return nil
	})

	// Wait for server health before starting SQS workers
	if queue.IsWorkerInitializationEnabled() {
		goroutine.RunWithCtx(ctx, func(ctx context.Context) {
			if epifiserver.IsServerReady(gconf.ServerPorts().HttpPort) {
				logger.Info(ctx, "Server is healthy, starting SQS workers")
				sqs.StartSQSWorkers(ctx, sqsSubscribers, extendedSqsSubscribers)
			} else {
				logger.PanicWithCtx(ctx, "Server health check timed out, not starting SQS workers")
			}
		})
	}
	if err := grp.Wait(); err != nil {
		return err
	}

	return nil
}

// nolint: funlen
func setupAuth(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	managerClient managerpb.ManagerClient,
	groupClient usergrouppb.GroupClient,
	usersClient user.UsersClient,
	actorClient actor.ActorClient,
	segmentationServiceClient segmentpb.SegmentationServiceClient,
	epifiCRDB types.EpifiCRDB,
	authPGDB types.AuthPGDB,
	commsClient wiretypes.AuthCommsClientWithInterceptors,
	vendorAuthClient vgauth.VendorAuthClient,
	savingsClient savings.SavingsClient,
	cardProvisioningClient cardpb.CardProvisioningClient,
	authRedisStore types.AuthRedisStore,
	authTokenRedisStore types.AuthTokenRedisStore,
	authDeviceIntegrityNonceCacheStorage wiretypes.AuthDeviceIntegrityNonceCacheStorage,
	authDeviceRegistrationsCacheStorage wiretypes.AuthDeviceRegistrationsCacheStorage,
	livenessClient liveness.LivenessClient,
	consentClient consent.ConsentClient,
	bankCustomerServiceClient bankcust.BankCustomerServiceClient,
	redListClient redlist.RedListClient,
	riskClient riskpb.RiskClient,
	authClient simauthfederal.AuthClient,
	healthEngineServiceClient healthengine.HealthEngineServiceClient,
	caseManagementClient casemanagement.CaseManagementClient,
	kycAgentServiceClient agent.KycAgentServiceClient,
	fCMDeviceTokenClient types2.FCMDeviceTokenClientWithInterceptors,
	operationalStatusServiceClient operationalstatuspb.OperationalStatusServiceClient,
	productClient productpb.ProductClient,
	kycClient kycpb.KycClient,
	onboardingClient onbpb.OnboardingClient,
	payClient paypb.PayClient,
	authClientVar2 authpb.AuthClient,
	partnerSDKClient vgpartnersdkpb.PartnerSDKClient,
	accountingClient ffaccpb.AccountingClient,
	authDeviceLocationCacheStorage wiretypes.AuthDeviceLocationCacheStorage,
	celestialClient celestialpb.CelestialClient,
	uPIClient upipb.UPIClient,
	sessionAuthRedisStore types.SessionAuthRedisStore) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	authConf, err := authconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.AUTH_SERVICE))
		return nil, nil, err
	}
	_ = authConf

	authGenConf, err := dynconf.LoadConfigWithQuestConfig(authconf.Load, genconf2.NewConfigWithQuest, cfg.AUTH_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.AUTH_SERVICE))
		return nil, nil, err
	}

	if !conf.QuestSdk.Disable {
		authGenConfAppConfig := questsdkinit.QuestSdkAppConfig{AppConfig: authGenConf, SdkConfig: authGenConf.QuestSdk()}
		questsdkinit.SetupQuestSDK([]questsdkinit.QuestSdkAppConfig{authGenConfAppConfig}, string(cfg.AUTH_SERVER), managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, broker)
	}

	_ = authGenConf

	aFUVendorUpdatePublisher, err := sqs.NewPublisherWithConfig(ctx, authGenConf.AFUVendorUpdatePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	biometricEventPublisher, err := sqs.NewPublisherWithConfig(ctx, authGenConf.BiometricEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}

	authFactorUpdatePublisher, err := sns.NewSnsPublisherWithConfig(ctx, authGenConf.AuthFactorUpdatePublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}
	authTokenCreationPublisher, err := sns.NewSnsPublisherWithConfig(ctx, authGenConf.AuthTokenCreationPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}

	service := wire.InitializeService(epifiCRDB, authPGDB, commsClient, vendorAuthClient, authConf, authGenConf, usersClient, actorClient, savingsClient, broker, aFUVendorUpdatePublisher, authFactorUpdatePublisher, authTokenCreationPublisher, cardProvisioningClient, authRedisStore, authTokenRedisStore, authDeviceIntegrityNonceCacheStorage, authDeviceRegistrationsCacheStorage, livenessClient, consentClient, bankCustomerServiceClient, redListClient, riskClient, authClient, healthEngineServiceClient, caseManagementClient, kycAgentServiceClient, fCMDeviceTokenClient, operationalStatusServiceClient, productClient, kycClient, onboardingClient, payClient)

	authconsumerpb.RegisterAuthFactorUpdateConsumerServer(s, service)

	authpb.RegisterAuthServer(s, service)

	authorizerpb.RegisterAuthorizerServer(s, service)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, authGenConf.AFUVendorUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		authconsumerpb.RegisterProcessVendorUpdateMethodToSubscriber(subscriber, service)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, authGenConf.DeviceReregCallbackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		authconsumerpb.RegisterDeviceReregCallbackConsumerMethodToSubscriber(subscriber, service)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, authGenConf.AFUManualReviewNotificationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		authconsumerpb.RegisterProcessAFUManualReviewMethodToSubscriber(subscriber, service)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, authGenConf.DeviceRegSMSAckSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		authconsumerpb.RegisterProcessDevRegSMSAcknowledgementMethodToSubscriber(subscriber, service)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, authGenConf.ProcessPinAttemptsExceededEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		authconsumerpb.RegisterProcessPinAttemptsExceededEventMethodToSubscriber(subscriber, service)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	devAuthService := wire.InitializeDevAuthService(epifiCRDB, authPGDB, authGenConf, authTokenRedisStore, broker)

	authdevpb.RegisterDevAuthServer(s, devAuthService)

	serviceVar2 := wire.InitializePartnerSDKService(authClientVar2, actorClient, partnerSDKClient, authGenConf, groupClient, authRedisStore, accountingClient)

	authpartnersdkpb.RegisterPartnerSDKServer(s, serviceVar2)

	serviceVar3 := wire.InitializeLocationService(epifiCRDB, authPGDB, authGenConf, authDeviceLocationCacheStorage, broker)

	locationpb.RegisterLocationServer(s, serviceVar3)

	serviceVar4 := wire2.InitializeService(epifiCRDB, celestialClient, actorClient, savingsClient, uPIClient, authClientVar2, usersClient, livenessClient, authGenConf)

	authv2pb.RegisterOrchestratorServer(s, serviceVar4)

	orchestratorDevEntity := wire2.InitializeOrchestratorDevEntityService(epifiCRDB)

	authorchdevpb.RegisterDevOrchestratorServer(s, orchestratorDevEntity)

	serviceVar5 := wire3.InitializeBiometricsService(authPGDB, biometricEventPublisher)

	biometricspb.RegisterBiometricsServiceServer(s, serviceVar5)

	serviceVar6 := wire.InitializeTotpService(authGenConf, sessionAuthRedisStore)

	totp2.RegisterTotpServer(s, serviceVar6)

	sessionManager := wire.InitializeSessionManagerService(authGenConf, authClientVar2, sessionAuthRedisStore)

	sessionpb.RegisterSessionManagerServer(s, sessionManager)

	configNameToConfMap[cfg.ConfigName(cfg.AUTH_SERVICE)] = &commonexplorer.Config{StaticConf: &authconf.Config{}, QuestIntegratedConfig: authGenConf}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupUser(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	managerClient managerpb.ManagerClient,
	groupClient usergrouppb.GroupClient,
	usersClient user.UsersClient,
	actorClient actor.ActorClient,
	segmentationServiceClient segmentpb.SegmentationServiceClient,
	epifiCRDB types.EpifiCRDB,
	kycClient kycpb.KycClient,
	customerClient ovgcustomerpb.CustomerClient,
	savingsClient savings.SavingsClient,
	authClientVar2 authpb.AuthClient,
	vKYCClient vkycpb.VKYCClient,
	depositClient deposit.DepositClient,
	commsClientVar2 comms.CommsClient,
	userRedisStore types.UserRedisStore,
	userDevicePropertiesRedisStore wiretypes3.UserDevicePropertiesRedisStore,
	minimalUserRedisStore types.MinimalUserRedisStore,
	livenessClient liveness.LivenessClient,
	bankCustomerServiceClient bankcust.BankCustomerServiceClient,
	screenerClient screener.ScreenerClient,
	employmentClient employment.EmploymentClient,
	productClient productpb.ProductClient,
	userRueidisCacheStorage types.UserRueidisCacheStorage,
	minimalUserRueidisCacheStorage types.MinimalUserRueidisCacheStorage,
	panClient pan.PanClient,
	userPropertiesPGDB types.UserPropertiesPGDB,
	onboardingClient onbpb.OnboardingClient,
	docExtractionClient kycdocspb.DocExtractionClient,
	userLeadSvcClient leadspb.UserLeadSvcClient,
	cardProvisioningClient cardpb.CardProvisioningClient,
	consentClient consent.ConsentClient,
	orderServiceClient orderpb.OrderServiceClient,
	inAppReferralClient inappreferralpb.InAppReferralClient,
	eKYCClient ekycpb.EKYCClient,
	userPreferenceClient uppb.UserPreferenceClient,
	uNNameCheckClient namecheck.UNNameCheckClient,
	locationClient userlocation.LocationClient,
	obfuscatorClient obfuscatorpb.ObfuscatorClient,
	locationClientVar2 locationpb.LocationClient,
	riskClient riskpb.RiskClient,
	employmentFeClient employment.EmploymentFeClient,
	derivedAttributesManagerClient derivedattributespb.DerivedAttributesManagerClient,
	userIntelServiceClient userintel.UserIntelServiceClient,
	nudgePGDB types.NudgePGDB,
	watsonClient watson.WatsonClient,
	operationalStatusServiceClient operationalstatuspb.OperationalStatusServiceClient,
	balanceClient accountbalancepb.BalanceClient,
	fireflyClient fireflypb.FireflyClient,
	preApprovedLoanClient palpb.PreApprovedLoanClient,
	pANClient vgpan.PANClient,
	creditReportManagerClient creditreportv.CreditReportManagerClient,
	onboardingRueidisCacheStorage types.OnboardingRueidisCacheStorage,
	omegleClient omegle.OmegleClient,
	docsClient vgdocpb.DocsClient,
	onboardingMinRueidisCacheStorage types.OnboardingMinRueidisCacheStorage,
	troubleshootClient vkyccalltroubleshootpb.TroubleshootClient,
	tieringClient tiering.TieringClient,
	livenessClientVar5 vglivenesspb.LivenessClient,
	questCacheStorage types.QuestCacheStorage,
	mFExternalOrdersClient mfpb.MFExternalOrdersClient,
	connectedAccountClient beconnectedaccpb.ConnectedAccountClient,
	fireflyV2Client ffv2pb.FireflyV2Client,
	locationClientVar3 locationvgpb.LocationClient,
	ipServiceClient ippb.IpServiceClient,
	userContactRedisStore types.UserContactRedisStore,
	userGroupRueidisCacheStorage types.UserGroupRueidisCacheStorage,
	vendorMappingServiceClient vmpb.VendorMappingServiceClient,
	moEngageClient vgmoengagepb.MoEngageClient) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	userConf, err := userconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.USER_SERVICE))
		return nil, nil, err
	}
	_ = userConf

	userGenConf, err := dynconf.LoadConfigWithQuestConfig(userconf.Load, genconf3.NewConfigWithQuest, cfg.USER_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.USER_SERVICE))
		return nil, nil, err
	}

	if !conf.QuestSdk.Disable {
		userGenConfAppConfig := questsdkinit.QuestSdkAppConfig{AppConfig: userGenConf, SdkConfig: userGenConf.QuestSdk()}
		questsdkinit.SetupQuestSDK([]questsdkinit.QuestSdkAppConfig{userGenConfAppConfig}, string(cfg.AUTH_SERVER), managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, broker)
	}

	_ = userGenConf

	shippingAddressUpdatePublisher, err := sqs.NewPublisherWithConfig(ctx, userGenConf.ShippingAddressUpdatePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	processAccessRevokeCooldownDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, userGenConf.ProcessAccessRevokeCooldownPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	syncOnboardingSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, userGenConf.Onboarding().SyncOnboardingSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	afPurchasePublisher, err := sqs.NewPublisherWithConfig(ctx, userGenConf.AfPurchasePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	eventsCompletedTnCPublisher, err := sqs.NewPublisherWithConfig(ctx, userGenConf.EventsCompletedTnCPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	vpaMigrationConsentPublisher, err := sqs.NewPublisherWithConfig(ctx, userGenConf.VpaMigrationConsentPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}

	userAccessRevokeUpdatePublisher, err := sns.NewSnsPublisherWithConfig(ctx, userGenConf.UserAccessRevokeUpdatePublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}
	userDevicePropertiesUpdatePublisher, err := sns.NewSnsPublisherWithConfig(ctx, userGenConf.UserDevicePropertiesUpdatePublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}
	shippingAddressUpdateEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, userGenConf.ShippingAddressUpdateEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}
	deleteUserPublisher, err := sns.NewSnsPublisherWithConfig(ctx, userGenConf.DeleteUserPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}
	onboardingStageEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, userGenConf.OnboardingStageEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}
	consentEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, userGenConf.ConsentEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}

	usersS3Client := s3pkg.NewClient(awsConf, userGenConf.AWS().S3.UsersBucketName)
	usersS3ClientVar2 := s3pkg.NewClient(awsConf, userGenConf.AWS().S3.UsersBucketName)
	nrS3Client := s3pkg.NewClient(awsConf, userGenConf.Onboarding().NrBucketName())

	serviceVar7 := wire4.InitializeService(epifiCRDB, shippingAddressUpdatePublisher, kycClient, customerClient, savingsClient, usersS3Client, userConf, actorClient, authClientVar2, vKYCClient, depositClient, commsClientVar2, broker, userRedisStore, userDevicePropertiesRedisStore, minimalUserRedisStore, userGenConf, livenessClient, userAccessRevokeUpdatePublisher, userDevicePropertiesUpdatePublisher, bankCustomerServiceClient, shippingAddressUpdateEventPublisher, groupClient, screenerClient, employmentClient, productClient, userRueidisCacheStorage, minimalUserRueidisCacheStorage, panClient, userPropertiesPGDB, deleteUserPublisher, onboardingClient, docExtractionClient, processAccessRevokeCooldownDelayPublisher, userLeadSvcClient)

	user.RegisterUsersServer(s, serviceVar7)

	serviceVar8 := wire4.InitializeAccessRevokeConsumer(userGenConf, epifiCRDB, userRueidisCacheStorage, minimalUserRueidisCacheStorage, processAccessRevokeCooldownDelayPublisher)

	accessrevokeconsumerpb.RegisterConsumerServer(s, serviceVar8)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, userGenConf.ProcessAccessRevokeCooldownSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		accessrevokeconsumerpb.RegisterProcessAccessRevokeCooldownMethodToSubscriber(subscriber, serviceVar8)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	serviceVar9 := wire5.InitializeOnboardingService(userGenConf, epifiCRDB, usersClient, savingsClient, actorClient, cardProvisioningClient, consentClient, authClientVar2, kycClient, broker, vKYCClient, onboardingStageEventPublisher, orderServiceClient, groupClient, syncOnboardingSqsPublisher, commsClientVar2, usersS3ClientVar2, employmentClient, inAppReferralClient, eKYCClient, userPreferenceClient, userConf, uNNameCheckClient, livenessClient, locationClient, obfuscatorClient, locationClientVar2, riskClient, screenerClient, bankCustomerServiceClient, userRedisStore, employmentFeClient, derivedAttributesManagerClient, userIntelServiceClient, nudgePGDB, watsonClient, panClient, operationalStatusServiceClient, balanceClient, fireflyClient, preApprovedLoanClient, productClient, pANClient, creditReportManagerClient, onboardingRueidisCacheStorage, docExtractionClient, omegleClient, docsClient, nrS3Client, onboardingMinRueidisCacheStorage, troubleshootClient, tieringClient, livenessClientVar5, managerClient, questCacheStorage, segmentationServiceClient, mFExternalOrdersClient, connectedAccountClient, userLeadSvcClient, fireflyV2Client)

	onbpb.RegisterOnboardingServer(s, serviceVar9)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, userGenConf.SyncOnboardingSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		onbpb.RegisterSyncOnboardingMethodToSubscriber(subscriber, serviceVar9)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, userGenConf.ProcessCardCreationEvent(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		onbpb.RegisterProcessCardCreationEventMethodToSubscriber(subscriber, serviceVar9)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, userGenConf.UserUpdateEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		onbpb.RegisterProcessUserUpdateEventMethodToSubscriber(subscriber, serviceVar9)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, userGenConf.ProcessSavingsAccountUpdateEvent(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		onbpb.RegisterProcessSavingsAccountUpdateEventMethodToSubscriber(subscriber, serviceVar9)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, userGenConf.LivManualReviewEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		onbpb.RegisterProcessLivManualReviewEventMethodToSubscriber(subscriber, serviceVar9)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, userGenConf.BankCustomerUpdateEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		onbpb.RegisterProcessBankCustomerUpdateEventMethodToSubscriber(subscriber, serviceVar9)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, userGenConf.CreditReportVerificationEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		onbpb.RegisterProcessCreditReportVerificationEventMethodToSubscriber(subscriber, serviceVar9)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, userGenConf.InHouseVkycCallCompletedEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		onbpb.RegisterProcessInhouseVkycCallCompletedEventMethodToSubscriber(subscriber, serviceVar9)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	serviceVar10 := wire4.InitializeUserLocationService(userGenConf, epifiCRDB, locationClientVar3, locationClientVar2, obfuscatorClient, ipServiceClient)

	userlocation.RegisterLocationServer(s, serviceVar10)

	serviceVar11 := wire4.InitializeUserObfuscatorService(epifiCRDB)

	obfuscatorpb.RegisterObfuscatorServer(s, serviceVar11)

	userDevService := wire4.InitializeDevUserService(epifiCRDB, kycClient, actorClient, onboardingClient, userRueidisCacheStorage, userDevicePropertiesRedisStore, minimalUserRedisStore, userGenConf, minimalUserRueidisCacheStorage, userPropertiesPGDB, crdbResourceMap)

	developer4.RegisterDevUserServer(s, userDevService)

	serviceVar12 := wire4.InitializeUserContactsService(userGenConf, usersClient, actorClient, userContactRedisStore, userPropertiesPGDB)

	usercontactpb.RegisterContactServer(s, serviceVar12)

	serviceVar13 := wire4.InitializeUserGroupService(userGenConf, epifiCRDB, userGroupRueidisCacheStorage)

	usergrouppb.RegisterGroupServer(s, serviceVar13)

	consumerService := wire4.InitializeEventConsumerService(userConf, broker, afPurchasePublisher, savingsClient, authClientVar2, usersClient, balanceClient)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, userGenConf.EventsAfPurchaseSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		event2.RegisterPublishAfPurchaseMethodToSubscriber(subscriber, consumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, userGenConf.EventsCompletedTnCSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		event2.RegisterPublishCompletedTnCMethodToSubscriber(subscriber, consumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	onboardingUserUpdateVKYCConsumerService := wire5.InitializeOnboardingUserUpdateVKYCConsumerService(usersClient, actorClient, onboardingClient)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, userGenConf.OnboardingUserUpdateVKYCSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		vkyc.RegisterOnboardingUserUpdateVKYCConsumerMethodToSubscriber(subscriber, onboardingUserUpdateVKYCConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, userGenConf.VKYCCallCompletedEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		vkyc.RegisterProcessVKYCCallCompletedEventMethodToSubscriber(subscriber, onboardingUserUpdateVKYCConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	userVKYCUpdateConsumerService := wire4.InitializeUserVKYCUpdateConsumerService(epifiCRDB, savingsClient, commsClientVar2, broker, usersClient, kycClient, vKYCClient, userGenConf, bankCustomerServiceClient, onboardingClient)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, userGenConf.VKYCUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		vkycconsumer.RegisterUserVKYCUpdateConsumerMethodToSubscriber(subscriber, userVKYCUpdateConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, userGenConf.EKYCSuccessSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		vkycconsumer.RegisterProcessEKYCSuccessEventMethodToSubscriber(subscriber, userVKYCUpdateConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	serviceVar14 := wire5.InitialiseWatsonClientService(usersClient, onboardingClient)

	onboardingwatsonclientpb.RegisterWatsonServer(s, serviceVar14)

	serviceVar15 := wire4.InitializeDevCacheService(userGenConf, minimalUserRueidisCacheStorage, userRueidisCacheStorage)

	devcache.RegisterDevCacheServer(s, serviceVar15)

	userContactConsumer := wire4.InitializeUserContactConsumer(usersClient, userPropertiesPGDB, vendorMappingServiceClient, moEngageClient)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, userGenConf.ProcessAfuEventUserContactSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		usercontactpb.RegisterProcessAfuCompletionEventMethodToSubscriber(subscriber, userContactConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, userGenConf.ProcessOnboardingEventUserContactSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		usercontactpb.RegisterProcessOnboardingCompletionEventMethodToSubscriber(subscriber, userContactConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, userGenConf.ProcessDeleteUserSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		usercontactpb.RegisterProcessDeleteUserEventMethodToSubscriber(subscriber, userContactConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	serviceVar16 := wire6.InitializeService(crdbResourceMap, userConf, consentEventPublisher, eventsCompletedTnCPublisher, vendorMappingServiceClient, obfuscatorClient, locationClient, vpaMigrationConsentPublisher)

	consent.RegisterConsentServer(s, serviceVar16)

	configNameToConfMap[cfg.ConfigName(cfg.USER_SERVICE)] = &commonexplorer.Config{StaticConf: &userconf.Config{}, QuestIntegratedConfig: userGenConf}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

func initLogger() error {
	// Load configuration
	conf, err := config.Load(cfg.AUTH_SERVER)
	if err != nil {
		// cannot log error since logger is not initialized yet.
		return err
	}

	logger.InitV2(conf.Environment, conf.Logging)
	if conf.SecureLogging != nil {
		logger.InitSecureLoggerV2(conf.SecureLogging)
	}
	return nil
}

func getEnvNamespaceClient(ns temporalpkg.Namespace, env string) string {
	if cfg.IsLocalEnv(env) {
		return strings.ToLower(string(ns))
	}

	return fmt.Sprintf("%s-%s", env, strings.ToLower(string(ns)))
}

func getSQSConsumerInterceptors() []grpc.UnaryServerInterceptor {
	return []grpc.UnaryServerInterceptor{questinterceptor.UserContextInterceptor}
}
