Name: "frontend"

ServerPorts:
  GrpcPort: 8082
  GrpcSecurePort: 9509
  HttpPort: 9999
  HttpPProfPort: 9990

QuestSdk:
  Disable: false

TieringBenefitsEarnedScreenHeaderBanner:
  LeftPadding: 16
  RightPadding: 16
  TopPadding: 8
  BottomPadding: 8

Logging:
  EnableLoggingToFile: true
  LogPath: "/var/log/frontend/info.log"
  MaxSizeInMBs: 1024
  MaxBackups: 1

SecureLogging:
  EnableSecureLog: true
  DefaultFileLoggingParams:
    LogPath: "/var/log/frontend/secure.log"
    MaxSizeInMBs: 5
    MaxBackups: 20

GrpcServerConfig:
  MaxRecvMsgSize:
    # 9 MB
    Size: 9437184
  SuppressZapLogger: true

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

Frontend:
  EnableDeviceIntegrityCheck: true
  EnableLocationInterceptor: true
  MaxGRPCTimeout: "1m"
  MaxGRPCStreamTimeout: "20m"
  DeviceIntegrity:
    EnableWhitelistedTokens: true
    SkipAsyncDeviceIntegrityChecks: false
    WhitelistedTokensList: [ "DUMMY_TOKEN" ]
    DefaultHighRiskDeviceConsentDuration: "24h"
    MaxHighRiskDeviceConsentDuration: "1080h" # 45 days
    AsyncDeviceIntegrityCheck:
      DisableFeature: false
      MinAndroidVersion: 174
      MinIOSVersion: 10000
  IPInterceptorParams:
    EnableIPInterceptor: true

EnableGetPaymentOptionsV1: true

GrpcRatelimiterParams:
  Disable: false

GrpcAttributeRatelimiterParams:
  Namespace: "frontend-rpc"
  Disable: false
  ResourceMap:
    frontend_insights_networth_networth_magicimportfiles:
      RateLimitList:
        "0":
          ArrayElement:
            Position: 0
          Attribute: "ATTRIBUTE_CTX_ACTOR_ID"
          AttributeValueRegexp: ".*"
          Rate: 1000
          Period: 24h
      EnableLogging: false
    frontend_account_signup_signup_loginwithoauth:
      RateLimitList:
        "0":
          ArrayElement:
            Position: 0
          Attribute: "ATTRIBUTE_REQ_HEADER_DEVICE_ID"
          AttributeValueRegexp: ".*"
          Rate: 20
          Period: 1m
      EnableLogging: false
    frontend_account_signup_signup_addoauthaccount:
      RateLimitList:
        "0":
          ArrayElement:
            Position: 0
          Attribute: "ATTRIBUTE_REQ_HEADER_DEVICE_ID"
          AttributeValueRegexp: ".*"
          Rate: 20
          Period: 1m
      EnableLogging: false
    frontend_account_signup_signup_fetchaccesstoken:
      RateLimitList:
        "0":
          ArrayElement:
            Position: 0
          Attribute: "ATTRIBUTE_REQ_HEADER_DEVICE_ID"
          AttributeValueRegexp: ".*"
          Rate: 20
          Period: 1m
      EnableLogging: false
    frontend_pay_transaction_transaction_createfundtransferorder:
      RateLimitList:
        "0":
          ArrayElement:
            Position: 0
          Attribute: "ATTRIBUTE_REQ_HEADER_DEVICE_ID"
          AttributeValueRegexp: ".*"
          Rate: 10
          Period: 1s
      EnableLogging: false
    frontend_pay_transaction_transaction_createfundtransferorderv1:
      RateLimitList:
        "0":
          ArrayElement:
            Position: 0
          Attribute: "ATTRIBUTE_REQ_HEADER_DEVICE_ID"
          AttributeValueRegexp: ".*"
          Rate: 10
          Period: 1s
      EnableLogging: false
    frontend_upi_onboarding_onboarding_balanceenquiry:
      RateLimitList:
        "0":
          ArrayElement:
            Position: 0
          Attribute: "ATTRIBUTE_REQ_HEADER_DEVICE_ID"
          AttributeValueRegexp: ".*"
          Rate: 50
          Period: 24h
      EnableLogging: false

Recorder:
  # turn this flag on to enable logging of grpc request and response
  Enable: false
  RPCLogFilePath: "fe-rpcs.log"
  DiscardFrontendReqAuth: true
  DiscardFrontendReqHeader: true
  BlacklistedRPCRegExps:
    - ".*FetchConfig"
    - ".*FetchApiKey"
    - ".*FetchAccessToken"
    - ".*LoginWithOAuth"
    - ".*GetUserSessionDetails"
    - ".*CheckApiKeyUpdate"
    - ".*SyncBiometricIdentifier"
    - ".*BatchedLog"
    - ".*VerifyDeviceIntegrity"
    - ".*GetDeviceIntegrityNonce"
    - ".*GetTieringLaunchInfo"
    - ".*FetchDynamicElements"
    - ".*GetAlternateAppIcon"
    - ".*GetNavigationBarHighlights"
    - ".*GetHomeProfileInfo"
    - ".*GetHomeLayoutAndWalkthrough"
    - ".*GetRewardsAndOffersWidget"
