Environment: "prod"

AWS:
  Region: "ap-south-1"

Flags:
  TrimDebugMessageFromStatus: false

GrpcServerConfig:
  SuppressZapLogger: false
  UnaryRpcLogDeciderConfig:
    IsLogSuppressionEnabled: true
    IsLogSuppressionEnabledForStatusCode:
      - true # OK
      - false # CANCELLED
      - false # UNKNOWN
      - false # INVALID_ARGUMENT
      - false # DEADLINE_EXCEEDED
      - true # NOT_FOUND
      - false # ALREADY_EXISTS
      - false # PERMISSION_DENIED
      - false # RESOURCE_EXHAUSTED
      - true # FAILED_PRECONDITION
      - false # ABORTED
      - false # OUT_OF_RANGE
      - false # UNIMPLEMENTED
      - false # INTERNAL
      - false # UNAVAILABLE
      - false # DATA_LOSS
      - false # UNAUTHENTICATED
    StatusCodeToLogSuppressedMethodList:
      OK:
        - "/actor.Actor/GetActorById"
        - "/actor.Actor/GetEntityDetails"
        - "/actor.Actor/GetEntityDetailsByActorId"
        - "/actor.Actor/ResolveOtherActorPiAndTimeline"
        - "/auth.Auth/GetDeviceIntegrityStatus"
        - "/auth.Auth/ValidateToken"
        - "/bankcust.BankCustomerService/GetBankCustomer"
        - "/frontend.config.Config/FetchConfig"
        - "/frontend.clientlogger.ClientLogger/Log"
        - "/frontend.dynamic_elements.DynamicElements/FetchDynamicElements"
        - "/location.Location/GetOrCreateToken"
        - "/merchant.MerchantService/GetMerchant"
        - "/merchant.MerchantService/GetMerchants"
        - "/parser.Parser/ParseTxn"
        - "/paymentinstrument.Pi/GetPi"
        - "/paymentinstrument.Pi/GetPiById"
        - "/paymentinstrument.Pi/GetPiByIds"
        - "/savings.Savings/GetAccount"
        - "/savings.Savings/GetSavingsAccountEssentials"
        - "/savings.Savings/FetchDynamicElements"
        - "/segment.SegmentationService/IsMember"
        - "/segment.SegmentationService/IsMemberOfExpressions"
        - "/user.group.Group/GetGroupsMappedToEmail"
        - "/user.onboarding.Onboarding/GetDetails"
        - "/user.onboarding.Onboarding/GetFeatureDetails"
        - "/user.Users/GetUser"
        - "/user.Users/GetUsers"
        - "/timeline.TimelineService/GetByActorIds" # 13m daily logs
        - "/paymentinstrument.Pi/GetPIsByIds" # 12m+ daily logs
        - "/paymentinstrument.AccountPIRelation/GetPiByAccountId" # 5m daily logs
        - "/paymentinstrument.AccountPIRelation/GetPiByActorId" # 4m daily logs
        - "/paymentinstrument.AccountPIRelation/GetByPiId" # 3m daily logs
        - "/accounts.balance.Balance/GetAccountBalance" # 6m daily logs
        - "/vendormapping.VendorMappingService/GetDPMappingById"
        - "/cx.chat.Chats/GetChatInitInformationForActor"
        - "/actor_activity.consumer.Consumer/ProcessRudderEvent"
        - "/actor_activity.ActorActivity/CreateActivity"
        - "/inapphelp.app_feedback.AppFeedback/IsEligibleForFeedback"
        - "/frontend.inapphelp.app_feedback.AppFeedback/IsEligibleForFeedback"
        - "/creditreportv2.CreditReportManager/GetCreditReports"
        - "/api.investment.aggregator.InvestmentAggregator/GetInvestmentSummary"
        - "/usstocks.portfolio.PortfolioManager/GetInvestmentSummaryInfo"
        - "/usstocks.account.AccountManager/GetAccount"
        - "/usstocks.account.AccountManager/GetTradingAccountDetails"
        - "/firefly.Firefly/FetchCreditCardEligibility"
        - "/card.notification.NotificationConsumerService/ProcessCardTransactions"
        - "/card.provisioning.CardProvisioning/GetCardGroups"
        - "/card.notification.NotificationConsumerService/ProcessForexTransactionsRefund"
        - "/firefly.accounting.Accounting/GetAccount"
        - "/card.provisioning.CardProvisioning/GetPhysicalCardActivationInfo"
        - "/card.provisioning.CardProvisioning/FetchPhysicalCardChargesForUser"
        - "/card.provisioning.CardProvisioning/FetchCards"
        - "/firefly.Firefly/GetCreditCardOffers"
        - "/firefly.Firefly/GetCreditCard"
        - "/firefly.Firefly/GetCardRequestAndCardRequestStage"
        - "/analyser.consumer.Consumer/ProcessAATxnUpdateEvent"
        - "/analyser.consumer.Consumer/ProcessOrderUpdateEvent"
        - "/analyser.consumer.Consumer/ProcessCategoryUpdateEvent"
        - "/api.fitt.event.consumer.EventConsumer/ProcessOrderUpdateEvent"
        - "/categorizer.consumer.Consumer/CategorizeAATxnEvent"
        - "/categorizer.consumer.Consumer/ProcessOrderEvent"
        - "/reminder.consumer.Consumer/ProcessCategoryEvent"
        - "/upcomingtransactions.consumer.Consumer/UpdateUpcomingTxnState"
        - "/order.aa.Consumer/ProcessAATxn"
        - "/search.indexer.Indexer/ProcessAATxnEvent"
        - "/search.indexer.Indexer/ProcessCategorizerEvent"
        - "/search.indexer.Indexer/ProcessPiEvent"
        - "/search.indexer.Indexer/ProcessTimeLineEvent"
        - "/search.indexer.Indexer/ProcessTxnEvent"
        - "/api.investment.mutualfund.catalog.CatalogManager/GetInvestmentSummaryInfo" # 10.5m daily logs
        - "/p2pinvestment.P2pInvestment/GetInvestmentDashboard" # 10.5m daily logs
        - "/deposit.Deposit/GetDepositAggregatesForUser" # 10.5m daily logs
        - "/preapprovedloan.PreApprovedLoan/FetchDynamicElements"
        - "/preapprovedloan.PreApprovedLoan/GetLoanSummaryForHome"
        - "/preapprovedloan.PreApprovedLoan/GetLoansUserStatus"
        - "/creditlimitestimator.CreditLimitEstimator/GetCreditCardConservativeLimit"
        - "/creditreportv2.CreditReportManager/GetCreditReportConsentStatus"
        - "/creditreportv2.CreditReportManager/GetCreditReport"
        - "/tiering.Tiering/GetTierAtTime"
        - "/tiering.Tiering/GetTieringPitchV2"
        - "/tiering.Tiering/FetchDynamicElements"
        - "/connected_account.ConnectedAccount/GetAccounts"
        - "/connected_account.ConnectedAccount/GetAccountDetails"
        - "/salaryprogram.SalaryProgram/GetCurrentRegStatusAndNextRegStage"
        - "/salaryprogram.SalaryProgram/FetchDynamicElements"
        - "/api.frontend.smsfetcher.SmsFetcher/PushSmsData"
        - "/consent.Consent/FetchConsent"
      NOT_FOUND:
        - "/frontend.inapphelp.feedback_engine.FeedbackEngine/GetFirstFeedbackQuestion"
        - "/inapphelp.feedback_engine.FeedbackEngine/GetFirstFeedbackQuestion"
        - "/cx.sherlock_banners.SherlockBanners/GetBanners"
        - "/usstocks.account.AccountManager/GetAccount"
        - "/usstocks.account.AccountManager/GetTradingAccountDetails"
        - "/kyc.vkyc.VKYCFe/FetchDynamicElements"
        - "/firefly.card_recommendation.CardRecommendationService/FetchCcRecommendationInfoForUser"
        - "/firefly.Firefly/GetCreditCard"
        - "/firefly.Firefly/FetchDynamicElements"
        - "/firefly.accounting.Accounting/GetAccount"
        - "/firefly.accounting.Accounting/GetPaginatedCreditCardTxnView"
        - "/card.provisioning.CardProvisioning/GetPhysicalCardDispatchStatus"
        - "/firefly.Firefly/GetCardRequestAndCardRequestStage"
        - "/card.provisioning.CardProvisioning/FetchCards"
        - "/p2pinvestment.P2pInvestment/GetInvestmentDashboard" # 6m daily logs
        - "/api.frontend.smsfetcher.SmsFetcher/PushSmsData"
        - "/consent.Consent/FetchConsent"
        - "/preapprovedloan.PreApprovedLoan/FetchDynamicElements"
      FAILED_PRECONDITION:
        - "/firefly.card_recommendation.CardRecommendationService/EvaluateCardRecommendationsForUser"

RudderStack:
  Host: "https://rudder.pointz.in"
  IntervalInSec: 10
  BatchSize: 20
  Verbose: false

Tracing:
  Enable: true

Profiling:
  StackDriverProfiling:
    ProjectId: "epifi-prod"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-prod-automatic-profiling"
    CPUPercentageLimit: 80
    MemoryPercentageLimit: 80
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

Secrets:
  Ids:
    RudderWriteKey: "prod/rudder/internal-writekey"

VendorApiConf:
  Application:
    InhouseMerchantResolutionServiceUrl: "https://merchant.data-prod.epifi.in/resolution"
    InhouseMerchantNameCategoriserUrl: "https://text-semantics.data-prod.epifi.in/v1/name_categoriser"

  RedactedRawRequestLogExceptionList: [
    "https://fortuna-ds.data-prod.epifi.in/income_fetch",
    "https://prod.fe-2bv371kswn.aws.fennel.ai/api/v1/log",
    "https://main.epifi.aws.fennel.ai/api/v1/log",
    "https://main.epifi.aws.fennel.ai/api/v1/extract_features",
    "https://api.transunioncibil.com/consumer/dtc/v4/GetCustomerAssets"
  ]

  RedactedRawResponseLogExceptionList: [
    "https://main.epifi.aws.fennel.ai/api/v1/extract_features"
  ]

  # HTTP client config inspired from DefaultTransport of http package
  # https://golang.org/src/net/http/transport.go?h=DefaultTransport#L42
  HttpClientConfig:
    Transport:
      DialContext:
        Timeout: 30s
        KeepAlive: 30s
      TLSHandshakeTimeout: 10s
      MaxIdleConns: 100
      IdleConnTimeout: 90s
      MaxConnsPerHost: 500
      MaxIdleConnsPerHost: 50

  APIMetricsEnabledURLList: [
    "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/ckyc/enquiry",
    "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/ckyc/download",
    "https://gateway.federalbank.co.in/prod/prod/digitalCredit/v1.0.0/profileUpdation",
    "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/customer/creation",
    "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/enquiry/service:customer_creation_enquiry",
    "https://gateway.federalbank.co.in/prod/prod/ddupe/v3.0.0/check",
    "https://gateway.federalbank.co.in:443/prod/prod/neobanking/v1.0.0/customer-details/enquire",
    "https://gateway.federalbank.co.in:553/prod/prod/digitalCredit/v1.0.0/PANAadhaarValidate",
    "https://gateway.federalbank.co.in/prod/prod/pan/v2.0.0/validate",
    "https://gateway.federalbank.co.in/prod/prod/ekyc/namedob/validation",
    "https://gateway.federalbank.co.in/prod/prod/account_utility/v1.0.0/aadharMob",
    "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/user-device/registration",
    "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/device/re-registration",
    "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/user-device/deactivation",
    "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/user-device/reactivate",
    "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/rtgs/fundtransfer",
    "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/neft/fundtransfer",
    "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/intra/fundtransfer",
    "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/imps/fundtransfer",
    "https://gateway.federalbank.co.in:553/prod/prod/neobanking/v1.0.0/OwnDepositAccFT",
    "https://remittance.federalbank.co.in:8804/UPIMerchantApp:BalanceEnquiry",
    "https://remittance.federalbank.co.in:8804/UPIMerchantApp:ListAccount",
  ]
