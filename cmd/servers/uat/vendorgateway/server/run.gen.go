// code generated by tools/servergen
package server

import (
	context "context"
	"fmt"
	"net/http"
	strings "strings"
	time "time"

	awssdk "github.com/aws/aws-sdk-go-v2/aws"
	awssqs "github.com/aws/aws-sdk-go-v2/service/sqs"
	prometheus "github.com/prometheus/client_golang/prometheus"
	redis "github.com/redis/go-redis/v9"
	analytics "github.com/rudderlabs/analytics-go"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
	reflection "google.golang.org/grpc/reflection"
	gorm "gorm.io/gorm"

	vgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/async/goroutine"
	awsconfpkg "github.com/epifi/be-common/pkg/aws/v2/config"
	sqs "github.com/epifi/be-common/pkg/aws/v2/sqs"
	cfg "github.com/epifi/be-common/pkg/cfg"
	consul "github.com/epifi/be-common/pkg/cfg/consul"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	commonexplorer "github.com/epifi/be-common/pkg/cfg/explorer"
	config "github.com/epifi/be-common/pkg/cmd/config"
	genconf "github.com/epifi/be-common/pkg/cmd/config/genconf"
	types "github.com/epifi/be-common/pkg/cmd/types"
	epificontext "github.com/epifi/be-common/pkg/epificontext"
	epifigrpc "github.com/epifi/be-common/pkg/epifigrpc"
	commoninterceptors "github.com/epifi/be-common/pkg/epifigrpc/interceptors"
	resolver "github.com/epifi/be-common/pkg/epifigrpc/resolver"
	epifiserver "github.com/epifi/be-common/pkg/epifiserver"
	temporalpkg "github.com/epifi/be-common/pkg/epifitemporal"
	errgroup "github.com/epifi/be-common/pkg/errgroup"
	events "github.com/epifi/be-common/pkg/events"
	logger "github.com/epifi/be-common/pkg/logger"
	profiling "github.com/epifi/be-common/pkg/profiling"
	queue "github.com/epifi/be-common/pkg/queue"
	ratelimiter "github.com/epifi/be-common/pkg/ratelimiter"
	store "github.com/epifi/be-common/pkg/ratelimiter/store"
	storage "github.com/epifi/be-common/pkg/storage"
	storage2 "github.com/epifi/be-common/pkg/storage/v2"
	otel "github.com/epifi/be-common/pkg/tracing/opentelemetry"
	explorerpb "github.com/epifi/gamma/api/pkg/cfg/explorer"
	tokenizerpb "github.com/epifi/gamma/api/tokenizer"
	vgaapb "github.com/epifi/gamma/api/vendorgateway/aa"
	igvgpb "github.com/epifi/gamma/api/vendorgateway/aa/analytics/ignosis"
	aml2 "github.com/epifi/gamma/api/vendorgateway/aml"
	seon2 "github.com/epifi/gamma/api/vendorgateway/appscreener/seon"
	billpayvgpb "github.com/epifi/gamma/api/vendorgateway/billpay"
	bouncycastle2 "github.com/epifi/gamma/api/vendorgateway/bouncycastle"
	ckycpb "github.com/epifi/gamma/api/vendorgateway/ckyc"
	aclvgpb "github.com/epifi/gamma/api/vendorgateway/comms/acl"
	creditreport2 "github.com/epifi/gamma/api/vendorgateway/credit_report"
	vgccv2pb "github.com/epifi/gamma/api/vendorgateway/creditcard"
	crmpb "github.com/epifi/gamma/api/vendorgateway/crm"
	leadsquaredpb "github.com/epifi/gamma/api/vendorgateway/crm/leadsquared"
	currencyinsightsvgpb "github.com/epifi/gamma/api/vendorgateway/currencyinsights"
	senseforthpb "github.com/epifi/gamma/api/vendorgateway/cx/chatbot/livechatfallback/senseforth"
	nuggetpb "github.com/epifi/gamma/api/vendorgateway/cx/chatbot/nugget"
	federal "github.com/epifi/gamma/api/vendorgateway/cx/federal"
	freshchatpb "github.com/epifi/gamma/api/vendorgateway/cx/freshchat"
	freshdeskpb "github.com/epifi/gamma/api/vendorgateway/cx/freshdesk"
	cxinhousepb "github.com/epifi/gamma/api/vendorgateway/cx/inhouse"
	ozonetelpb "github.com/epifi/gamma/api/vendorgateway/cx/ozonetel"
	solutionspb "github.com/epifi/gamma/api/vendorgateway/cx/solutions"
	digilockerpb "github.com/epifi/gamma/api/vendorgateway/digilocker"
	dlvalidatepb "github.com/epifi/gamma/api/vendorgateway/dl"
	docpb "github.com/epifi/gamma/api/vendorgateway/docs"
	ekycpb "github.com/epifi/gamma/api/vendorgateway/ekyc"
	emailpb "github.com/epifi/gamma/api/vendorgateway/email"
	employmentpb "github.com/epifi/gamma/api/vendorgateway/employment"
	esign2 "github.com/epifi/gamma/api/vendorgateway/esign"
	extvalidatepb "github.com/epifi/gamma/api/vendorgateway/extvalidate"
	fcmservicepb "github.com/epifi/gamma/api/vendorgateway/fcm"
	vgfnpb "github.com/epifi/gamma/api/vendorgateway/fennel"
	fitttpb "github.com/epifi/gamma/api/vendorgateway/fittt"
	gplacepb "github.com/epifi/gamma/api/vendorgateway/gplace"
	riskcovryvgpb "github.com/epifi/gamma/api/vendorgateway/healthinsurance/riskcovry"
	vgidfcpb "github.com/epifi/gamma/api/vendorgateway/idfc"
	idvalidate2 "github.com/epifi/gamma/api/vendorgateway/idvalidate"
	incomeestimator2 "github.com/epifi/gamma/api/vendorgateway/incomeestimator"
	p2ppb "github.com/epifi/gamma/api/vendorgateway/investments/p2p"
	vgiplocpb "github.com/epifi/gamma/api/vendorgateway/iplocation"
	vgitrpb "github.com/epifi/gamma/api/vendorgateway/itr"
	uqudo2 "github.com/epifi/gamma/api/vendorgateway/kyc/uqudo"
	vgidfcvkycpb "github.com/epifi/gamma/api/vendorgateway/kyc/vkyc/idfc"
	bre "github.com/epifi/gamma/api/vendorgateway/lending/bre"
	mutualfund "github.com/epifi/gamma/api/vendorgateway/lending/collateral/mutualfund"
	vgcredgenicspb2 "github.com/epifi/gamma/api/vendorgateway/lending/collections/credgenics"
	creditcard2 "github.com/epifi/gamma/api/vendorgateway/lending/creditcard"
	creditline2 "github.com/epifi/gamma/api/vendorgateway/lending/creditline"
	digitap "github.com/epifi/gamma/api/vendorgateway/lending/digitap"
	vgfinfluxpb "github.com/epifi/gamma/api/vendorgateway/lending/lms/finflux"
	preapprovedloan "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan"
	abflplpb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/abfl"
	idfcplpb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/idfc"
	lenden "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
	lentra2 "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lentra"
	liquiloansplpb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/liquiloans"
	moneyviewvgpb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/moneyview"
	fiftyfinpb "github.com/epifi/gamma/api/vendorgateway/lending/securedloans/fiftyfin"
	setuvgpb "github.com/epifi/gamma/api/vendorgateway/lending/setu"
	vglivenesspb "github.com/epifi/gamma/api/vendorgateway/liveness"
	locationpb "github.com/epifi/gamma/api/vendorgateway/location"
	merchantresolutionpb "github.com/epifi/gamma/api/vendorgateway/merchantresolution"
	moengage2 "github.com/epifi/gamma/api/vendorgateway/moengage"
	namecheckpb "github.com/epifi/gamma/api/vendorgateway/namecheck"
	employernamecategoriserpb "github.com/epifi/gamma/api/vendorgateway/namecheck/employernamecategoriser"
	employernamematchpb "github.com/epifi/gamma/api/vendorgateway/namecheck/employernamematch"
	npspb "github.com/epifi/gamma/api/vendorgateway/nps"
	ocr2 "github.com/epifi/gamma/api/vendorgateway/ocr"
	dreamfolksvgpb "github.com/epifi/gamma/api/vendorgateway/offers/dreamfolks"
	loyltypb "github.com/epifi/gamma/api/vendorgateway/offers/loylty"
	poshvinevgpb "github.com/epifi/gamma/api/vendorgateway/offers/poshvine"
	qwikcilvervgpb "github.com/epifi/gamma/api/vendorgateway/offers/qwikcilver"
	thriwevgpb "github.com/epifi/gamma/api/vendorgateway/offers/thriwe"
	vistaravgpb "github.com/epifi/gamma/api/vendorgateway/offers/vistara"
	onsurity2 "github.com/epifi/gamma/api/vendorgateway/onsurity"
	vgaccountspb "github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	vendorauthpb "github.com/epifi/gamma/api/vendorgateway/openbanking/auth"
	partnersdkpb "github.com/epifi/gamma/api/vendorgateway/openbanking/auth/partnersdk"
	bankcustomervgpb "github.com/epifi/gamma/api/vendorgateway/openbanking/bank_customer"
	cardpb "github.com/epifi/gamma/api/vendorgateway/openbanking/card"
	customerservicepb "github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	depositpb "github.com/epifi/gamma/api/vendorgateway/openbanking/deposit"
	vgdipb "github.com/epifi/gamma/api/vendorgateway/openbanking/dispute"
	vgenachpb "github.com/epifi/gamma/api/vendorgateway/openbanking/enach"
	lienpb "github.com/epifi/gamma/api/vendorgateway/openbanking/lien"
	paymentpb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment"
	b2cpaymentpb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment/b2c"
	internationalfundtransferpb2 "github.com/epifi/gamma/api/vendorgateway/openbanking/payment/internationalfundtransfer"
	savingsservicepb "github.com/epifi/gamma/api/vendorgateway/openbanking/savings"
	shippingprefpb "github.com/epifi/gamma/api/vendorgateway/openbanking/shipping_preference"
	standinginstructionpb "github.com/epifi/gamma/api/vendorgateway/openbanking/standinginstruction"
	upipb "github.com/epifi/gamma/api/vendorgateway/openbanking/upi"
	panpb "github.com/epifi/gamma/api/vendorgateway/pan"
	parserpb "github.com/epifi/gamma/api/vendorgateway/parser"
	pgpb "github.com/epifi/gamma/api/vendorgateway/pg"
	phonenetwork2 "github.com/epifi/gamma/api/vendorgateway/phonenetwork"
	profilevalidationpb2 "github.com/epifi/gamma/api/vendorgateway/profilevalidation"
	rechargevgpb "github.com/epifi/gamma/api/vendorgateway/recharge"
	riskpb "github.com/epifi/gamma/api/vendorgateway/risk"
	vgscienapticpb "github.com/epifi/gamma/api/vendorgateway/scienaptic"
	shipwaypb "github.com/epifi/gamma/api/vendorgateway/shipway"
	slackpb "github.com/epifi/gamma/api/vendorgateway/slack_bot"
	smsservicepb "github.com/epifi/gamma/api/vendorgateway/sms"
	usstockpb "github.com/epifi/gamma/api/vendorgateway/stocks"
	catalog2 "github.com/epifi/gamma/api/vendorgateway/stocks/catalog"
	bridgewise2 "github.com/epifi/gamma/api/vendorgateway/stocks/catalog/bridgewise"
	vgtieringpb "github.com/epifi/gamma/api/vendorgateway/tiering"
	vgtransactionmonitoringpb "github.com/epifi/gamma/api/vendorgateway/transactionmonitoring/dronapay"
	userseg2 "github.com/epifi/gamma/api/vendorgateway/userseg"
	vkycpb "github.com/epifi/gamma/api/vendorgateway/vkyc"
	vkyccallpb "github.com/epifi/gamma/api/vendorgateway/vkyccall"
	wealthckycpb "github.com/epifi/gamma/api/vendorgateway/wealth/ckyc"
	cvlvgpb "github.com/epifi/gamma/api/vendorgateway/wealth/cvl"
	wealthdigilockerpb "github.com/epifi/gamma/api/vendorgateway/wealth/digilocker"
	wealthdigiopb "github.com/epifi/gamma/api/vendorgateway/wealth/digio"
	wealthocrpb "github.com/epifi/gamma/api/vendorgateway/wealth/inhouseocr"
	wealthkarzapb "github.com/epifi/gamma/api/vendorgateway/wealth/karza"
	wealthmanchpb "github.com/epifi/gamma/api/vendorgateway/wealth/manch"
	wealthmutualfundpb "github.com/epifi/gamma/api/vendorgateway/wealth/mutualfund"
	mfanalyticspb "github.com/epifi/gamma/api/vendorgateway/wealth/mutualfund/analytics"
	holdingsimporterpb "github.com/epifi/gamma/api/vendorgateway/wealth/mutualfund/holdingsimporter"
	wealthnsdlpb "github.com/epifi/gamma/api/vendorgateway/wealth/nsdl"
	whatsapppb "github.com/epifi/gamma/api/vendorgateway/whatsapp"
	zenduty2 "github.com/epifi/gamma/api/vendorgateway/zenduty"
	explorer "github.com/epifi/gamma/pkg/cfg/explorer"
	questinterceptor "github.com/epifi/gamma/pkg/epifigrpc/interceptors/quest"
	servergenwire "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
	fepkg "github.com/epifi/gamma/pkg/frontend/msg"
	vendorgatewayconf "github.com/epifi/gamma/vendorgateway/config"
	genconf2 "github.com/epifi/gamma/vendorgateway/config/genconf"
	servergenwire2 "github.com/epifi/gamma/vendorgateway/interceptor/servergen_wire"
	servergenhook "github.com/epifi/gamma/vendorgateway/servergenhook"
	wire "github.com/epifi/gamma/vendorgateway/wire"
)

// nolint: funlen
func Run(ctx context.Context, initNotifier chan<- cfg.ServerName) error {
	// Panic handler for logging panics in standard format
	epifiserver.HandlePanic()
	resolver.RegisterBuilder(resolver.ConsulSchemeName)
	var configNameToConfMap = make(commonexplorer.CfgExplorerMap)

	err := initLogger()
	if err != nil {
		// cannot log error since logger maynot be initialized yet.
		return err
	}
	ctx = epificontext.WithTraceId(ctx, metadata.MD{})
	// Load static configuration
	conf, err := config.Load(cfg.VENDOR_GATEWAY_SERVER)
	if err != nil {
		logger.Error(ctx, "failed to load static conf", zap.Error(err))
		return err
	}
	_ = conf
	// Load generated dynamic configuration
	gconf, err := genconf.Load(cfg.VENDOR_GATEWAY_SERVER)
	if err != nil {
		logger.Error(ctx, "failed to load dynamic conf", zap.Error(err))
		return err
	}
	logger.Info(ctx, "initiating server")

	if gconf.Profiling().AutomaticProfiling().EnableAutoProfiling() {
		profilingErr := profiling.AutomaticProfiling(context.Background(), cfg.VENDOR_GATEWAY_SERVER, gconf.Profiling(), gconf.Environment(), gconf.AWS().Region)
		if profilingErr != nil {
			logger.Error(ctx, "failed to start automatic profiling", zap.Error(profilingErr))
			return profilingErr
		}
	}

	if gconf.Tracing().Enable {
		_, shutdown, tracingErr := otel.ConfigureOpenTelemetryV2(context.Background(), cfg.VENDOR_GATEWAY_SERVER, gconf.Environment(), gconf.Profiling().PyroscopeProfiling().EnablePyroscope())
		if tracingErr != nil {
			logger.Error(ctx, "failed to configure open telemetry", zap.Error(tracingErr))
			return err
		}
		defer shutdown()
	}

	err = profiling.PyroscopeProfilingV2(gconf.Name(), gconf.Profiling(), gconf.Environment())
	if err != nil {
		logger.Error(ctx, "error starting pyroscope profiler", zap.Error(err))
	}

	if !cfg.IsLocalEnv(gconf.Environment()) && !cfg.IsRemoteDebugEnabled() && !cfg.IsTestTenantEnabled() && !cfg.IsTeamSpaceTenant() {
		initDisc, _ := consul.GetBool(consul.GetInitDiscoveryKey(gconf.Environment(), string(gconf.Name())))
		queue.SetWorkerInitialization(initDisc)
	}

	rateLimiterRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()[gconf.RedisRateLimiterName()], gconf.Tracing().Enable)
	defer func() { _ = rateLimiterRedisStore.Close() }()

	var (
		crdbResourceMap    *storage2.DBResourceProvider[*gorm.DB]
		crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor]
	)

	tokenizerConn := epifigrpc.NewServerConn(cfg.VENDOR_GATEWAY_SERVER, cfg.TOKENIZER_SERVER)
	defer epifigrpc.CloseConn(tokenizerConn)
	tokenizerClient := tokenizerpb.NewTokenizerClient(tokenizerConn)

	attributeRateLimiter := ratelimiter.NewAttributeRateLimiter(store.NewSlidingWindowLogWithRedis(rateLimiterRedisStore), gconf.GrpcAttributeRatelimiterParams().Namespace())
	vendorgatewayRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["VendorgatewayRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = vendorgatewayRedisStore.Close() }()
	vendorgatewayConn := epifigrpc.NewServerConn(cfg.VENDOR_GATEWAY_SERVER, cfg.VENDOR_GATEWAY_SERVER)
	defer epifigrpc.CloseConn(vendorgatewayConn)
	accountAggregatorClient := vgaapb.NewAccountAggregatorClient(vendorgatewayConn)
	tieringClient := vgtieringpb.NewTieringClient(vendorgatewayConn)
	cvlClient := cvlvgpb.NewCvlClient(vendorgatewayConn)
	bouncyCastleClient := bouncycastle2.NewBouncyCastleClient(vendorgatewayConn)
	idfcClient := vgidfcpb.NewIdfcClient(vendorgatewayConn)
	billPayServiceClient := billpayvgpb.NewBillPayServiceClient(vendorgatewayConn)
	mobileRechargeServiceClient := rechargevgpb.NewMobileRechargeServiceClient(vendorgatewayConn)

	awsConf, awsErr := awsconfpkg.NewAWSConfig(ctx, gconf.AWS().Region, gconf.Tracing().Enable)
	if awsErr != nil {
		logger.Error(ctx, "error in loading aws v2 config", zap.Error(awsErr))
		if !cfg.IsRemoteDebugEnabled() {
			return err
		}
	}

	sqsClient := sqs.InitSQSClient(awsConf)
	_ = sqsClient

	var broker *events.RudderStackBroker

	// load rudder-stack broker for sending events
	rudderClient, err := analytics.NewWithConfig(
		gconf.Secrets().Ids[config.RudderWriteKey], "http://"+cfg.GetServerEndPoint(cfg.RUDDER_SERVER).URL(),
		analytics.Config{
			Callback:  events.NewCallback(),
			Interval:  gconf.RudderStack().IntervalInSec * time.Second, // nolint: durationcheck
			BatchSize: gconf.RudderStack().BatchSize,
			Verbose:   gconf.RudderStack().Verbose,
		})
	if err != nil {
		logger.Error(ctx, "error in connecting rudder", zap.Error(err))
		return err
	}
	broker = events.NewRudderStackBroker(rudderClient)
	defer broker.Close()

	rateLimiter := ratelimiter.NewRateLimiterV2(store.NewSlidingWindowLogWithRedis(rateLimiterRedisStore), gconf.GrpcRatelimiterParams().RateLimitConfig())
	_ = rateLimiter

	var unaryInterceptors []grpc.UnaryServerInterceptor

	unaryServerInterceptor := servergenwire.TokenizerServerInterceptor(tokenizerClient)
	if unaryServerInterceptor != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptor)
	}

	unaryServerInterceptorVar2 := servergenwire.VGRateLimitServerInterceptor(gconf, rateLimiter, attributeRateLimiter)
	if unaryServerInterceptorVar2 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar2)
	}

	unaryServerInterceptorVar3, err := servergenwire2.NewVgTimeoutInterceptor()
	if err != nil {
		logger.Error(ctx, "failed to init grpc unary interceptor", zap.Error(err))
		return err
	}
	if unaryServerInterceptorVar3 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar3)
	}

	unaryServerInterceptorVar4, err := servergenwire2.NewVgDowntimeInterceptor()
	if err != nil {
		logger.Error(ctx, "failed to init grpc unary interceptor", zap.Error(err))
		return err
	}
	if unaryServerInterceptorVar4 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar4)
	}

	unaryServerInterceptorVar5, err := servergenwire2.NewVgInstrumentBillingInterceptor(broker)
	if err != nil {
		logger.Error(ctx, "failed to init grpc unary interceptor", zap.Error(err))
		return err
	}
	if unaryServerInterceptorVar5 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar5)
	}

	var streamInterceptors []grpc.StreamServerInterceptor

	histogramBuckets := prometheus.DefBuckets
	if len(gconf.LatencyHistogramBuckets()) > 0 {
		histogramBuckets = epifigrpc.AddGrpcLatencyBuckets(gconf.LatencyHistogramBuckets()...)
	}

	serverOptions := []grpc.ServerOption{}
	if gconf.GrpcServerConfig().MaxRecvMsgSize() != nil && gconf.GrpcServerConfig().MaxRecvMsgSize().Size != 0 {
		serverOptions = append(serverOptions, grpc.MaxRecvMsgSize(gconf.GrpcServerConfig().MaxRecvMsgSize().Size))
	}
	var extractFeRespStatusWithErrMsgFunc commoninterceptors.ExtractFeRespStatusWithErrMsgFunc
	extractFeRespStatusWithErrMsgFunc = fepkg.ExtractFeRespStatusWithErrMsgFunc

	s := epifigrpc.NewSecureServerWithInterceptors(gconf.GrpcServerConfig(), gconf.Flags().TrimDebugMessageFromStatus, string(gconf.Name()), histogramBuckets, unaryInterceptors, streamInterceptors, extractFeRespStatusWithErrMsgFunc, serverOptions...)

	if cfg.IsNonProdEnv(gconf.Environment()) {
		reflection.Register(s)
	}

	httpMux := http.NewServeMux()
	_ = httpMux

	var sqsSubscribers []queue.Subscriber
	var subs []queue.Subscriber
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSubs []*sqs.ExtendedSqsSubscriber

	subs, extSubs, err = setupVendorgateway(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, vendorgatewayRedisStore, accountAggregatorClient, tieringClient, cvlClient, bouncyCastleClient, tokenizerClient, idfcClient, billPayServiceClient, mobileRechargeServiceClient)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	explorerpb.RegisterConfigExplorerServer(s, explorer.NewServer(configNameToConfMap))

	cleanupFn, err := servergenhook.InitVendorgatewayServer(s) // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "InitVendorgatewayServer"), zap.Error(err))
		return err
	}
	defer cleanupFn()

	// Load rpc level log sampling rate descriptors.
	epifigrpc.GetRPCLogSamplingRatesMapInstance().Load(s)

	initNotifier <- gconf.Name()

	// TODO(Sundeep): Remove this additional logic for starting the servers inside go-routine, by exposing functions
	// from epifiserver package that take errgroup.Group as parameter.
	grp, _ := errgroup.WithContext(context.Background())

	grp.Go(func() error {
		epifiserver.StartSecureServer(s, gconf.ServerPorts(), string(gconf.Name()))
		return nil
	})

	// Wait for server health before starting SQS workers
	if queue.IsWorkerInitializationEnabled() {
		goroutine.RunWithCtx(ctx, func(ctx context.Context) {
			if epifiserver.IsServerReady(gconf.ServerPorts().HttpPort) {
				logger.Info(ctx, "Server is healthy, starting SQS workers")
				sqs.StartSQSWorkers(ctx, sqsSubscribers, extendedSqsSubscribers)
			} else {
				logger.PanicWithCtx(ctx, "Server health check timed out, not starting SQS workers")
			}
		})
	}
	if err := grp.Wait(); err != nil {
		return err
	}

	return nil
}

// nolint: funlen
func setupVendorgateway(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	vendorgatewayRedisStore types.VendorgatewayRedisStore,
	accountAggregatorClient vgaapb.AccountAggregatorClient,
	tieringClient vgtieringpb.TieringClient,
	cvlClient cvlvgpb.CvlClient,
	bouncyCastleClient bouncycastle2.BouncyCastleClient,
	tokenizerClient tokenizerpb.TokenizerClient,
	idfcClient vgidfcpb.IdfcClient,
	billPayServiceClient billpayvgpb.BillPayServiceClient,
	mobileRechargeServiceClient rechargevgpb.MobileRechargeServiceClient) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	vendorgatewayConf, err := vendorgatewayconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.VENDOR_GATEWAY_SERVICE))
		return nil, nil, err
	}
	_ = vendorgatewayConf

	vendorgatewayGenConf, err := dynconf.LoadConfig(vendorgatewayconf.Load, genconf2.NewConfig, cfg.VENDOR_GATEWAY_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.VENDOR_GATEWAY_SERVICE))
		return nil, nil, err
	}

	_ = vendorgatewayGenConf

	service := wire.InitializeSMSService(vendorgatewayGenConf, gconf.VendorApiConf())

	smsservicepb.RegisterSMSServer(s, service)

	serviceVar2 := wire.InitializeGPlaceService(vendorgatewayGenConf, gconf.VendorApiConf())

	gplacepb.RegisterGPlaceServer(s, serviceVar2)

	serviceVar3 := wire.InitializeCustomerService(vendorgatewayGenConf, gconf.VendorApiConf())

	customerservicepb.RegisterCustomerServer(s, serviceVar3)

	serviceVar4 := wire.InitializeSavingsService(vendorgatewayGenConf, gconf.VendorApiConf())

	savingsservicepb.RegisterSavingsServer(s, serviceVar4)

	serviceVar5 := wire.InitializeShippingPreferenceService(vendorgatewayGenConf, gconf.VendorApiConf())

	shippingprefpb.RegisterShippingPreferenceServer(s, serviceVar5)

	serviceVar6 := wire.InitializeVendorAuthService(vendorgatewayGenConf, gconf.VendorApiConf())

	vendorauthpb.RegisterVendorAuthServer(s, serviceVar6)

	serviceVar7 := wire.InitializePartnerSDKService(vendorgatewayGenConf, gconf.VendorApiConf())

	partnersdkpb.RegisterPartnerSDKServer(s, serviceVar7)

	serviceVar8 := wire.InitializePaymentService(vendorgatewayGenConf, gconf.VendorApiConf())

	paymentpb.RegisterPaymentServer(s, serviceVar8)

	serviceVar9 := wire.InitializeCKYCService(vendorgatewayGenConf, gconf.VendorApiConf())

	ckycpb.RegisterCKycServer(s, serviceVar9)

	serviceVar10 := wire.InitializeLivenessService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	vglivenesspb.RegisterLivenessServer(s, serviceVar10)

	serviceVar11 := wire.InitializeEmploymentService(vendorgatewayRedisStore, vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	employmentpb.RegisterEmploymentServer(s, serviceVar11)

	serviceVar12 := wire.InitializeTransactionMonitoringService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	vgtransactionmonitoringpb.RegisterTransactionRiskDronaPayServer(s, serviceVar12)

	serviceVar13, err := wire.InitializePANService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	panpb.RegisterPANServer(s, serviceVar13)

	serviceVar14 := wire.InitializeUNNameCheckService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	namecheckpb.RegisterUNNameCheckServer(s, serviceVar14)

	serviceVar15 := wire.InitializeEmployerNameMatchService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	employernamematchpb.RegisterEmployerNameMatchServer(s, serviceVar15)

	serviceVar16 := wire.InitializeSenseforthLiveChatFallbackService(vendorgatewayGenConf, gconf.VendorApiConf())

	senseforthpb.RegisterSenseforthLiveChatFallbackServer(s, serviceVar16)

	serviceVar17 := wire.InitializeFreshdeskService(ctx, vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	freshdeskpb.RegisterFreshdeskServer(s, serviceVar17)

	serviceVar18 := wire.InitializeFreshChatServer(vendorgatewayGenConf, gconf.VendorApiConf())

	freshchatpb.RegisterFreshchatServer(s, serviceVar18)

	serviceVar19 := wire.InitializeOzonetelService(vendorgatewayGenConf, gconf.VendorApiConf())

	ozonetelpb.RegisterOzonetelServer(s, serviceVar19)

	serviceVar20 := wire.InitializeSolutionsService(vendorgatewayGenConf, gconf.VendorApiConf())

	solutionspb.RegisterSolutionsServer(s, serviceVar20)

	serviceVar21 := wire.InitializeDepositService(vendorgatewayGenConf, gconf.VendorApiConf())

	depositpb.RegisterDepositServer(s, serviceVar21)

	serviceVar22 := wire.InitializeB2CService(vendorgatewayGenConf, gconf.VendorApiConf())

	b2cpaymentpb.RegisterPaymentServer(s, serviceVar22)

	serviceVar23 := wire.InitializeLeadSquaredService(vendorgatewayGenConf, gconf.VendorApiConf())

	leadsquaredpb.RegisterLeadManagementServer(s, serviceVar23)

	serviceVar24 := wire.InitializeStandingInstructionService(vendorgatewayGenConf, gconf.VendorApiConf())

	standinginstructionpb.RegisterStandingInstructionServer(s, serviceVar24)

	serviceVar25 := wire.InitializeLoyltyService(vendorgatewayGenConf, gconf.VendorApiConf())

	loyltypb.RegisterLoyltyServer(s, serviceVar25)

	serviceVar26 := wire.InitializeQwikcilverService(vendorgatewayRedisStore, vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	qwikcilvervgpb.RegisterQwikcilverServer(s, serviceVar26)

	serviceVar27 := wire.InitializeVkycService(vendorgatewayRedisStore, vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	vkycpb.RegisterVkycServer(s, serviceVar27)

	serviceVar28 := wire.InitializeAccountsServer(vendorgatewayGenConf, gconf.VendorApiConf())

	vgaccountspb.RegisterAccountsServer(s, serviceVar28)

	serviceVar29 := wire.InitializeEKYCService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	ekycpb.RegisterEKYCServer(s, serviceVar29)

	serviceVar30 := wire.InitializeDOCSService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	docpb.RegisterDocsServer(s, serviceVar30)

	serviceVar31 := wire.InitialiseFitttCricketServcie(vendorgatewayGenConf, gconf.VendorApiConf())

	fitttpb.RegisterFitttServer(s, serviceVar31)

	serviceVar32 := wire.InitializeWhatsAppService(vendorgatewayGenConf, gconf.VendorApiConf())

	whatsapppb.RegisterWhatsAppServer(s, serviceVar32)

	serviceVar33 := wire.InitializeIpLocationService(vendorgatewayGenConf, gconf.VendorApiConf())

	vgiplocpb.RegisterIPLocationServer(s, serviceVar33)

	serviceVar34 := wire.InitializeAAService(accountAggregatorClient, vendorgatewayGenConf, gconf.VendorApiConf())

	vgaapb.RegisterAccountAggregatorServer(s, serviceVar34)

	serviceVar35 := wire.InitialiseLamfService(vendorgatewayRedisStore, vendorgatewayGenConf, gconf.VendorApiConf())

	mutualfund.RegisterLoanAgainstMutualFundServer(s, serviceVar35)

	serviceVar36 := wire.InitializeTieringService(tieringClient, vendorgatewayGenConf, gconf.VendorApiConf())

	vgtieringpb.RegisterTieringServer(s, serviceVar36)

	serviceVar37 := wire.InitializeBcService(vendorgatewayGenConf, gconf.VendorApiConf())

	bouncycastle2.RegisterBouncyCastleServer(s, serviceVar37)

	serviceVar38 := wire.InitializeCreditReportService(vendorgatewayGenConf, gconf.VendorApiConf())

	creditreport2.RegisterCreditReportServer(s, serviceVar38)

	serviceVar39, err := wire.InitializeWealthCvlService(vendorgatewayConf, cvlClient, vendorgatewayGenConf, gconf.VendorApiConf())
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	cvlvgpb.RegisterCvlServer(s, serviceVar39)

	serviceVar40 := wire.InitializeWealthNsdlService(vendorgatewayGenConf, gconf.VendorApiConf())

	wealthnsdlpb.RegisterNsdlServer(s, serviceVar40)

	serviceVar41 := wire.InitializeWealthMutualFundService(ctx, vendorgatewayConf, vendorgatewayRedisStore, vendorgatewayGenConf, gconf.VendorApiConf())

	wealthmutualfundpb.RegisterMutualFundServer(s, serviceVar41)

	serviceVar42 := wire.InitializeHoldingsImporterService(vendorgatewayRedisStore, vendorgatewayGenConf, gconf.VendorApiConf())

	holdingsimporterpb.RegisterHoldingImporterServer(s, serviceVar42)

	serviceVar43 := wire.InitialiseShipwayService(vendorgatewayGenConf, gconf.VendorApiConf())

	shipwaypb.RegisterShipwayServer(s, serviceVar43)

	serviceVar44 := wire.InitializeWealthCkycService(vendorgatewayConf, bouncyCastleClient, vendorgatewayGenConf, gconf.VendorApiConf())

	wealthckycpb.RegisterCkycServer(s, serviceVar44)

	serviceVar45 := wire.InitializeWealthManchService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	wealthmanchpb.RegisterManchServer(s, serviceVar45)

	serviceVar46 := wire.InitializeWealthKarzaService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	wealthkarzapb.RegisterKarzaServer(s, serviceVar46)

	serviceVar47 := wire.InitializeWealthDigioService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	wealthdigiopb.RegisterDigioServer(s, serviceVar47)

	serviceVar48 := wire.InitializeDlAuthService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	dlvalidatepb.RegisterDLServer(s, serviceVar48)

	serviceVar49 := wire.InitializeParserService(vendorgatewayGenConf, gconf.VendorApiConf())

	parserpb.RegisterParserServer(s, serviceVar49)

	serviceVar50 := wire.InitializeSeonService(vendorgatewayGenConf, gconf.VendorApiConf())

	seon2.RegisterSeonServer(s, serviceVar50)

	serviceVar51 := wire.InitializeCXInHouseService(vendorgatewayGenConf, gconf.VendorApiConf())

	cxinhousepb.RegisterCXInHouseServer(s, serviceVar51)

	serviceVar52 := wire.InitializeCRMService(vendorgatewayConf, vendorgatewayRedisStore, vendorgatewayGenConf, gconf.VendorApiConf())

	crmpb.RegisterCRMServer(s, serviceVar52)

	serviceVar53 := wire.InitializeIdValidateService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	idvalidate2.RegisterIdValidateServer(s, serviceVar53)

	serviceVar54 := wire.InitializeWealthInhouseOCRService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	wealthocrpb.RegisterOcrServer(s, serviceVar54)

	serviceVar55 := wire.InitializeWealthDigilockerService(vendorgatewayRedisStore, vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	wealthdigilockerpb.RegisterDigilockerServer(s, serviceVar55)

	serviceVar56, err := wire.InitializeP2pInvestmentService(ctx, vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	p2ppb.RegisterP2PServer(s, serviceVar56)

	serviceVar57 := wire.InitializeLocationService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	locationpb.RegisterLocationServer(s, serviceVar57)

	serviceVar58 := wire.InitializeExternalValidateService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	extvalidatepb.RegisterExternalValidateServer(s, serviceVar58)

	serviceVar59 := wire.InitializeUserSegmentationService(vendorgatewayGenConf, gconf.VendorApiConf())

	userseg2.RegisterUserSegmentationServer(s, serviceVar59)

	serviceVar60 := wire.InitialisePhoneNetworkService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	phonenetwork2.RegisterPhoneNetworkServer(s, serviceVar60)

	serviceVar61 := wire.InitialiseCredgenicsService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	vgcredgenicspb2.RegisterCredgenicsServer(s, serviceVar61)

	serviceVar62 := wire.InitialiseFinfluxService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf(), vendorgatewayRedisStore)

	vgfinfluxpb.RegisterFinfluxServer(s, serviceVar62)

	serviceVar63 := wire.InitialiseIdfcService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	idfcplpb.RegisterIdfcServer(s, serviceVar63)

	serviceVar64 := wire.InitialiseMoneyviewService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	moneyviewvgpb.RegisterMoneyviewServer(s, serviceVar64)

	serviceVar65 := wire.InitialiseSetuService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	setuvgpb.RegisterSetuServer(s, serviceVar65)

	serviceVar66 := wire.InitialiseFiftyFinService(vendorgatewayGenConf, gconf.VendorApiConf())

	fiftyfinpb.RegisterFiftyFinServer(s, serviceVar66)

	serviceVar67, err := wire.InitialisePreApprovedLoanService(ctx, vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	preapprovedloan.RegisterPreApprovedLoanServer(s, serviceVar67)

	serviceVar68 := wire.InitialiseInhouseRiskService(vendorgatewayGenConf, gconf.VendorApiConf())

	riskpb.RegisterRiskServer(s, serviceVar68)

	serviceVar69 := wire.InitialiseEsignService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	esign2.RegisterESignServer(s, serviceVar69)

	serviceVar70 := wire.InitialiseCreditCardService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	creditcard2.RegisterCreditCardServer(s, serviceVar70)

	serviceVar71 := wire.InitialiseUSStockService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	usstockpb.RegisterStocksServer(s, serviceVar71)

	serviceVar72 := wire.InitialiseProfileValidationService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	profilevalidationpb2.RegisterProfileValidationServer(s, serviceVar72)

	serviceVar73 := wire.InitializeAmlService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	aml2.RegisterAmlServer(s, serviceVar73)

	serviceVar74 := wire.InitializeInternationalfundtransferService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	internationalfundtransferpb2.RegisterInternationalFundTransferServer(s, serviceVar74)

	serviceVar75 := wire.InitializeRiskcovryService(vendorgatewayGenConf, gconf.VendorApiConf())

	riskcovryvgpb.RegisterRiskcovryServer(s, serviceVar75)

	serviceVar76 := wire.InitializeOnsurityService(vendorgatewayGenConf, gconf.VendorApiConf())

	onsurity2.RegisterOnSurityServer(s, serviceVar76)

	serviceVar77 := wire.InitalizeUsStockCatalogService(vendorgatewayConf, vendorgatewayRedisStore, vendorgatewayGenConf, gconf.VendorApiConf())

	catalog2.RegisterCatalogServer(s, serviceVar77)

	serviceVar78, err := wire.InitialiseVistaraService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	vistaravgpb.RegisterVistaraServer(s, serviceVar78)

	serviceVar79 := wire.InitializeLienService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	lienpb.RegisterLienServer(s, serviceVar79)

	serviceVar80 := wire.InitialiseIncomeEstimatorService(vendorgatewayGenConf, gconf.VendorApiConf())

	incomeestimator2.RegisterIncomeEstimatorServer(s, serviceVar80)

	serviceVar81, err := wire.InitialiseEnachService(vendorgatewayGenConf, gconf.VendorApiConf(), vendorgatewayConf)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	vgenachpb.RegisterEnachServer(s, serviceVar81)

	serviceVar82 := wire.InitialiseLentra(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	lentra2.RegisterLentraServer(s, serviceVar82)

	serviceVar83 := wire.InitializeBankCustomerService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	bankcustomervgpb.RegisterBankCustomerServer(s, serviceVar83)

	syncWrapperConsumer := wire.InitializeSyncWrapperConsumerService()

	vgpb.RegisterSyncWrapperConsumerServer(s, syncWrapperConsumer)

	serviceVar84, err := wire.InitializeCardProvisioningService(ctx, vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf(), tokenizerClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	cardpb.RegisterCardProvisioningServer(s, serviceVar84)

	serviceVar85 := wire.InitialiseCreditLineService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	creditline2.RegisterCreditLineServer(s, serviceVar85)

	serviceVar86 := wire.InitialiseBreService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	bre.RegisterBusinessRuleEngineServer(s, serviceVar86)

	serviceVar87 := wire.InitializeFennelFeatureStoreService(vendorgatewayGenConf, gconf.VendorApiConf(), vendorgatewayConf)

	vgfnpb.RegisterFennelFeatureStoreServer(s, serviceVar87)

	serviceVar88 := wire.InitializeThriweService(vendorgatewayGenConf, gconf.VendorApiConf())

	thriwevgpb.RegisterThriweServer(s, serviceVar88)

	serviceVar89 := wire.InitializeDreamfolksService(vendorgatewayGenConf, gconf.VendorApiConf())

	dreamfolksvgpb.RegisterDreamfolksServer(s, serviceVar89)

	serviceVar90 := wire.InitialiseDisputeService(ctx, vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	vgdipb.RegisterDisputeServer(s, serviceVar90)

	vgEmailService := wire.InitialiseEmailService(ctx, vendorgatewayConf)

	emailpb.RegisterEmailServer(s, vgEmailService)

	serviceVar91 := wire.InitialiseFcmService(vendorgatewayConf, vendorgatewayGenConf)

	fcmservicepb.RegisterFCMServer(s, serviceVar91)

	serviceVar92 := wire.InitializeIdfcService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	vgidfcpb.RegisterIdfcServer(s, serviceVar92)

	serviceVar93 := wire.InitializeIdfcVkycService(idfcClient, vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	vgidfcvkycpb.RegisterIdfcServer(s, serviceVar93)

	serviceVar94 := wire.InitialiseLiquiloansService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	liquiloansplpb.RegisterLiquiloansServer(s, serviceVar94)

	serviceVar95 := wire.InitializeMFAnalyticsService(vendorgatewayGenConf, gconf.VendorApiConf())

	mfanalyticspb.RegisterMFAnalyticsServer(s, serviceVar95)

	serviceVar96 := wire.InitializeMerchantResolutionService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	merchantresolutionpb.RegisterMerchantResolutionServer(s, serviceVar96)

	serviceVar97, err := wire.InitialiseUpiService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf(), broker)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	upipb.RegisterUPIServer(s, serviceVar97)

	serviceVar98 := wire.InitializeSlackBotService(vendorgatewayConf)

	if func(conf *config.Config) bool {
		return cfg.IsDevelopmentEnv(conf.Environment) || cfg.IsStagingEnv(conf.Environment) || cfg.IsProdEnv(conf.Environment)
	}(conf) {
		slackpb.RegisterSlackBotServer(s, serviceVar98)
	}

	serviceVar99 := wire.InitialiseAbflService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	abflplpb.RegisterAbflServer(s, serviceVar99)

	serviceVar100 := wire.InitializePoshvineService(vendorgatewayGenConf, gconf.VendorApiConf())

	poshvinevgpb.RegisterPoshvineServer(s, serviceVar100)

	serviceVar101 := wire.InitializeITRService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	vgitrpb.RegisterITRServer(s, serviceVar101)

	serviceVar102 := wire.InitializeEmployerNameCategoriserService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	employernamecategoriserpb.RegisterEmployerNameCategoriserServer(s, serviceVar102)

	serviceVar103 := wire.InitialiseOCRService(vendorgatewayGenConf, gconf.VendorApiConf())

	ocr2.RegisterOCRServer(s, serviceVar103)

	serviceVar104, err := wire.InitialiseAclSftpService(vendorgatewayGenConf, gconf.VendorApiConf())
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	aclvgpb.RegisterAclSftpServer(s, serviceVar104)

	serviceVar105 := wire.InitializePaymentGatewayService(vendorgatewayGenConf, gconf.VendorApiConf(), vendorgatewayConf)

	pgpb.RegisterPaymentGatewayServer(s, serviceVar105)

	serviceVar106, err := wire.InitializeVkycCallService(vendorgatewayGenConf, gconf.VendorApiConf())
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	vkyccallpb.RegisterVkycCallServer(s, serviceVar106)

	serviceVar107 := wire.InitializeUqudoService(vendorgatewayGenConf, gconf.VendorApiConf())

	uqudo2.RegisterUqudoServer(s, serviceVar107)

	serviceVar108 := wire.InitialiseDigitapService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	digitap.RegisterDigitapServiceServer(s, serviceVar108)

	serviceVar109 := wire.InitialiseLendenService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	lenden.RegisterLendenServer(s, serviceVar109)

	serviceVar110 := wire.InitializeMoEngageService(vendorgatewayGenConf, gconf.VendorApiConf())

	moengage2.RegisterMoEngageServer(s, serviceVar110)

	serviceVar111 := wire.InitialiseDigilockerService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	digilockerpb.RegisterDigilockerServer(s, serviceVar111)

	serviceVar112 := wire.InitializeCurrencyInsightsService(vendorgatewayGenConf, gconf.VendorApiConf())

	currencyinsightsvgpb.RegisterServiceServer(s, serviceVar112)

	serviceVar113 := wire.InitializeScienapticService(vendorgatewayGenConf, gconf.VendorApiConf())

	vgscienapticpb.RegisterScienapticServer(s, serviceVar113)

	serviceVar114 := wire.InitializeBillPayService(vendorgatewayGenConf, gconf.VendorApiConf(), billPayServiceClient)

	billpayvgpb.RegisterBillPayServiceServer(s, serviceVar114)

	serviceVar115 := wire.InitializeMobileRechargeService(vendorgatewayGenConf, gconf.VendorApiConf(), mobileRechargeServiceClient)

	rechargevgpb.RegisterMobileRechargeServiceServer(s, serviceVar115)

	serviceVar116 := wire.InitializeAaIgnosisService(vendorgatewayGenConf, gconf.VendorApiConf())

	igvgpb.RegisterIgnosisAaAnalyticsServiceServer(s, serviceVar116)

	serviceVar117 := wire.InitalizeStockCatalogService(vendorgatewayConf, vendorgatewayRedisStore, vendorgatewayGenConf, gconf.VendorApiConf())

	bridgewise2.RegisterCatalogServer(s, serviceVar117)

	serviceVar118 := wire.InitializeNpsService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	npspb.RegisterNPSServer(s, serviceVar118)

	serviceVar119 := wire.InitializeFederalEscalationService(ctx, vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	federal.RegisterFederalEscalationServiceServer(s, serviceVar119)

	serviceVar120, err := wire.InitializeZendutyService(vendorgatewayGenConf, gconf.VendorApiConf())
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	zenduty2.RegisterZendutyServer(s, serviceVar120)

	serviceVar121, err := wire.InitializeCreditCardV2Service(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	vgccv2pb.RegisterCreditCardServer(s, serviceVar121)

	serviceVar122 := wire.InitializeNuggetService(vendorgatewayConf, vendorgatewayGenConf, gconf.VendorApiConf())

	nuggetpb.RegisterNuggetChatbotServiceServer(s, serviceVar122)

	configNameToConfMap[cfg.ConfigName(cfg.VENDOR_GATEWAY_SERVICE)] = &commonexplorer.Config{StaticConf: &vendorgatewayconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

func initLogger() error {
	// Load configuration
	conf, err := config.Load(cfg.VENDOR_GATEWAY_SERVER)
	if err != nil {
		// cannot log error since logger is not initialized yet.
		return err
	}

	logger.InitV2(conf.Environment, conf.Logging)
	if conf.SecureLogging != nil {
		logger.InitSecureLoggerV2(conf.SecureLogging)
	}
	return nil
}

func getEnvNamespaceClient(ns temporalpkg.Namespace, env string) string {
	if cfg.IsLocalEnv(env) {
		return strings.ToLower(string(ns))
	}

	return fmt.Sprintf("%s-%s", env, strings.ToLower(string(ns)))
}

func getSQSConsumerInterceptors() []grpc.UnaryServerInterceptor {
	return []grpc.UnaryServerInterceptor{questinterceptor.UserContextInterceptor}
}
