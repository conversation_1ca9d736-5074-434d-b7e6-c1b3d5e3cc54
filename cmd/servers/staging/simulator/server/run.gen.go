// code generated by tools/servergen
package server

import (
	context "context"
	"fmt"
	"net/http"
	strings "strings"
	time "time"

	awssdk "github.com/aws/aws-sdk-go-v2/aws"
	awssqs "github.com/aws/aws-sdk-go-v2/service/sqs"
	prometheus "github.com/prometheus/client_golang/prometheus"
	redis "github.com/redis/go-redis/v9"
	analytics "github.com/rudderlabs/analytics-go"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
	reflection "google.golang.org/grpc/reflection"
	gorm "gorm.io/gorm"

	celestialpb "github.com/epifi/be-common/api/celestial"
	awsconfpkg "github.com/epifi/be-common/pkg/aws/v2/config"
	s3pkg "github.com/epifi/be-common/pkg/aws/v2/s3"
	sqs "github.com/epifi/be-common/pkg/aws/v2/sqs"
	cache "github.com/epifi/be-common/pkg/cache"
	cfg "github.com/epifi/be-common/pkg/cfg"
	consul "github.com/epifi/be-common/pkg/cfg/consul"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	commonexplorer "github.com/epifi/be-common/pkg/cfg/explorer"
	config "github.com/epifi/be-common/pkg/cmd/config"
	genconf "github.com/epifi/be-common/pkg/cmd/config/genconf"
	types2 "github.com/epifi/be-common/pkg/cmd/types"
	epificontext "github.com/epifi/be-common/pkg/epificontext"
	epifigrpc "github.com/epifi/be-common/pkg/epifigrpc"
	commoninterceptors "github.com/epifi/be-common/pkg/epifigrpc/interceptors"
	resolver "github.com/epifi/be-common/pkg/epifigrpc/resolver"
	epifiserver "github.com/epifi/be-common/pkg/epifiserver"
	temporalpkg "github.com/epifi/be-common/pkg/epifitemporal"
	events "github.com/epifi/be-common/pkg/events"
	logger "github.com/epifi/be-common/pkg/logger"
	profiling "github.com/epifi/be-common/pkg/profiling"
	queue "github.com/epifi/be-common/pkg/queue"
	ratelimiter "github.com/epifi/be-common/pkg/ratelimiter"
	store "github.com/epifi/be-common/pkg/ratelimiter/store"
	storage "github.com/epifi/be-common/pkg/storage"
	storage2 "github.com/epifi/be-common/pkg/storage/v2"
	otel "github.com/epifi/be-common/pkg/tracing/opentelemetry"
	p2ppb "github.com/epifi/gamma/api/p2pinvestment"
	p2pdspb "github.com/epifi/gamma/api/p2pinvestment/developer"
	explorerpb "github.com/epifi/gamma/api/pkg/cfg/explorer"
	savingspb "github.com/epifi/gamma/api/savings"
	seon2 "github.com/epifi/gamma/api/simulator/app_screener/seon"
	cibil2 "github.com/epifi/gamma/api/simulator/creditreport/cibil"
	watsonclientpb "github.com/epifi/gamma/api/simulator/cx/watson_client"
	dlpb "github.com/epifi/gamma/api/simulator/dl/karza"
	dobby2 "github.com/epifi/gamma/api/simulator/dobby"
	docs2 "github.com/epifi/gamma/api/simulator/docs"
	karzaempl2 "github.com/epifi/gamma/api/simulator/employment/karza"
	inhouseepanpb "github.com/epifi/gamma/api/simulator/epan/inhouse"
	epanpb "github.com/epifi/gamma/api/simulator/epan/karza"
	esignpb "github.com/epifi/gamma/api/simulator/esign"
	extvalidate2 "github.com/epifi/gamma/api/simulator/extvalidate"
	idvalidate2 "github.com/epifi/gamma/api/simulator/idvalidate/karza"
	incomeestimator2 "github.com/epifi/gamma/api/simulator/incomeestimator"
	itrsimulatorpb "github.com/epifi/gamma/api/simulator/itr/inhouse"
	federal4 "github.com/epifi/gamma/api/simulator/kyc/federal"
	bre2 "github.com/epifi/gamma/api/simulator/lending/bre"
	creditcardpb "github.com/epifi/gamma/api/simulator/lending/creditcard"
	creditline2 "github.com/epifi/gamma/api/simulator/lending/creditline"
	preapprovedpb "github.com/epifi/gamma/api/simulator/lending/preapprovedloan"
	inhouselivpb2 "github.com/epifi/gamma/api/simulator/liveness/inhouse"
	karza2 "github.com/epifi/gamma/api/simulator/liveness/karza"
	veri2 "github.com/epifi/gamma/api/simulator/liveness/veri5"
	location2 "github.com/epifi/gamma/api/simulator/location"
	federalnamecheck2 "github.com/epifi/gamma/api/simulator/namecheck/federal"
	inhousenamecheck "github.com/epifi/gamma/api/simulator/namecheck/inhouse"
	inhouse2 "github.com/epifi/gamma/api/simulator/ocr/inhouse"
	karza3 "github.com/epifi/gamma/api/simulator/ocr/karza"
	dreamfolkssimulatorpb "github.com/epifi/gamma/api/simulator/offers/dreamfolks"
	developer2 "github.com/epifi/gamma/api/simulator/openbanking/accounts/developer"
	accountfederal "github.com/epifi/gamma/api/simulator/openbanking/accounts/federal"
	authfederal "github.com/epifi/gamma/api/simulator/openbanking/auth/federal"
	federalpartnersdkpb "github.com/epifi/gamma/api/simulator/openbanking/auth/partnersdk/federal"
	m2ppartnersdkpb "github.com/epifi/gamma/api/simulator/openbanking/auth/partnersdk/m2p"
	cardpb "github.com/epifi/gamma/api/simulator/openbanking/card/federal"
	simulatorchkbqpb "github.com/epifi/gamma/api/simulator/openbanking/chequebook/federal"
	customer2 "github.com/epifi/gamma/api/simulator/openbanking/customer"
	federaldeposit2 "github.com/epifi/gamma/api/simulator/openbanking/deposit/federal"
	enachfedpb "github.com/epifi/gamma/api/simulator/openbanking/enach/federal"
	enquiryfederal "github.com/epifi/gamma/api/simulator/openbanking/enquiry/federal"
	fedb2cfundtransferpb "github.com/epifi/gamma/api/simulator/openbanking/fundtransfer/b2c/federal"
	fedfundtransferpb "github.com/epifi/gamma/api/simulator/openbanking/fundtransfer/federal"
	federal6 "github.com/epifi/gamma/api/simulator/openbanking/lien/federal"
	fedsavingspb "github.com/epifi/gamma/api/simulator/openbanking/savings/federal"
	shippingpreferencepb "github.com/epifi/gamma/api/simulator/openbanking/shipping_preference/federal"
	tiering2 "github.com/epifi/gamma/api/simulator/openbanking/tiering"
	simp2pdspb "github.com/epifi/gamma/api/simulator/p2pinvestment/developer"
	liquiloanspb "github.com/epifi/gamma/api/simulator/p2pinvestment/liquiloans"
	panfederal "github.com/epifi/gamma/api/simulator/pan/federal"
	karzapb "github.com/epifi/gamma/api/simulator/pan/karza"
	phonenetwork2 "github.com/epifi/gamma/api/simulator/phonenetwork"
	profileevaluatorpb2 "github.com/epifi/gamma/api/simulator/profileevaluator"
	riskpb "github.com/epifi/gamma/api/simulator/risk/inhouse"
	shipwaypb "github.com/epifi/gamma/api/simulator/shipway"
	scienaptic2 "github.com/epifi/gamma/api/simulator/sms/scienaptic"
	fedstandinginstructionpb "github.com/epifi/gamma/api/simulator/standinginstruction/federal"
	federal7 "github.com/epifi/gamma/api/simulator/vkyc/federal"
	camspb "github.com/epifi/gamma/api/simulator/wealth/mutualfund/cams"
	userpb "github.com/epifi/gamma/api/user"
	sms "github.com/epifi/gamma/api/vendorgateway/sms"
	vnccpb "github.com/epifi/gamma/api/vendornotification/creditcard"
	vnloansabflpb "github.com/epifi/gamma/api/vendornotification/lending/loans/abfl"
	fedpay "github.com/epifi/gamma/api/vendornotification/openbanking/payment/federal"
	explorer "github.com/epifi/gamma/pkg/cfg/explorer"
	questinterceptor "github.com/epifi/gamma/pkg/epifigrpc/interceptors/quest"
	fepkg "github.com/epifi/gamma/pkg/frontend/msg"
	simulatorconf "github.com/epifi/gamma/simulator/config"
	genconf2 "github.com/epifi/gamma/simulator/config/genconf"
	servergenhook "github.com/epifi/gamma/simulator/servergenhook"
	wire "github.com/epifi/gamma/simulator/wire"
	types "github.com/epifi/gamma/simulator/wire/types"
)

// nolint: funlen
func Run(ctx context.Context, initNotifier chan<- cfg.ServerName) error {
	// Panic handler for logging panics in standard format
	epifiserver.HandlePanic()
	resolver.RegisterBuilder(resolver.ConsulSchemeName)
	var configNameToConfMap = make(commonexplorer.CfgExplorerMap)

	err := initLogger()
	if err != nil {
		// cannot log error since logger maynot be initialized yet.
		return err
	}
	ctx = epificontext.WithTraceId(ctx, metadata.MD{})
	// Load static configuration
	conf, err := config.Load(cfg.SIMULATOR_GRPC_SERVER)
	if err != nil {
		logger.Error(ctx, "failed to load static conf", zap.Error(err))
		return err
	}
	_ = conf
	// Load generated dynamic configuration
	gconf, err := genconf.Load(cfg.SIMULATOR_GRPC_SERVER)
	if err != nil {
		logger.Error(ctx, "failed to load dynamic conf", zap.Error(err))
		return err
	}
	logger.Info(ctx, "initiating server")

	if gconf.Profiling().AutomaticProfiling().EnableAutoProfiling() {
		profilingErr := profiling.AutomaticProfiling(context.Background(), cfg.SIMULATOR_GRPC_SERVER, gconf.Profiling(), gconf.Environment(), gconf.AWS().Region)
		if profilingErr != nil {
			logger.Error(ctx, "failed to start automatic profiling", zap.Error(profilingErr))
			return profilingErr
		}
	}

	if gconf.Tracing().Enable {
		_, shutdown, tracingErr := otel.ConfigureOpenTelemetryV2(context.Background(), cfg.SIMULATOR_GRPC_SERVER, gconf.Environment(), gconf.Profiling().PyroscopeProfiling().EnablePyroscope())
		if tracingErr != nil {
			logger.Error(ctx, "failed to configure open telemetry", zap.Error(tracingErr))
			return err
		}
		defer shutdown()
	}

	err = profiling.PyroscopeProfilingV2(gconf.Name(), gconf.Profiling(), gconf.Environment())
	if err != nil {
		logger.Error(ctx, "error starting pyroscope profiler", zap.Error(err))
	}

	if !cfg.IsLocalEnv(gconf.Environment()) && !cfg.IsRemoteDebugEnabled() && !cfg.IsTestTenantEnabled() && !cfg.IsTeamSpaceTenant() {
		initDisc, _ := consul.GetBool(consul.GetInitDiscoveryKey(gconf.Environment(), string(gconf.Name())))
		queue.SetWorkerInitialization(initDisc)
	}

	rateLimiterRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()[gconf.RedisRateLimiterName()], gconf.Tracing().Enable)
	defer func() { _ = rateLimiterRedisStore.Close() }()

	var (
		crdbResourceMap    *storage2.DBResourceProvider[*gorm.DB]
		crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor]
	)

	simulatorCRDB, err := storage2.NewCRDBWithConfig(gconf.Databases()["SimulatorCRDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "SimulatorCRDB"))
		return err
	}
	simulatorCRDBSqlDb, err := simulatorCRDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "SimulatorCRDB"))
		return err
	}
	defer func() { _ = simulatorCRDBSqlDb.Close() }()

	simulatorSqlDB, err := storage.NewSQLDBWithConfig(gconf.Databases()["SimulatorSqlDB"])
	if err != nil {
		logger.Error(ctx, "unable to connect to db", zap.Error(err))
		return err
	}
	defer func() { _ = simulatorSqlDB.Close() }()

	vendorgatewayConn := epifigrpc.NewServerConn(cfg.SIMULATOR_GRPC_SERVER, cfg.VENDOR_GATEWAY_SERVER)
	defer epifigrpc.CloseConn(vendorgatewayConn)
	vendornotificationConn := epifigrpc.NewServerConn(cfg.SIMULATOR_GRPC_SERVER, cfg.VENDOR_NOTIFI_SERVER)
	defer epifigrpc.CloseConn(vendornotificationConn)
	dobbyRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["DobbyRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = dobbyRedisStore.Close() }()
	dobbyCacheStorage := cache.NewRedisCacheStorageWithHystrix(cache.NewRedisCacheStorage(dobbyRedisStore), gconf.RedisClusters()["DobbyRedisStore"].HystrixCommand)
	onboardingConn := epifigrpc.NewServerConn(cfg.SIMULATOR_GRPC_SERVER, cfg.ONBOARDING_SERVER)
	defer epifigrpc.CloseConn(onboardingConn)
	usersClient := userpb.NewUsersClient(onboardingConn)
	centralgrowthConn := epifigrpc.NewServerConn(cfg.SIMULATOR_GRPC_SERVER, cfg.CENTRAL_GROWTH_SERVER)
	defer epifigrpc.CloseConn(centralgrowthConn)
	savingsClient := savingspb.NewSavingsClient(centralgrowthConn)
	nebulaConn := epifigrpc.NewServerConn(cfg.SIMULATOR_GRPC_SERVER, cfg.NEBULA_SERVER)
	defer epifigrpc.CloseConn(nebulaConn)
	celestialClient := celestialpb.NewCelestialClient(nebulaConn)
	wealthdmfConn := epifigrpc.NewServerConn(cfg.SIMULATOR_GRPC_SERVER, cfg.WEALTH_DMF_SERVER)
	defer epifigrpc.CloseConn(wealthdmfConn)
	devP2PInvestmentClient := p2pdspb.NewDevP2PInvestmentClient(wealthdmfConn)
	p2PInvestmentClient := p2ppb.NewP2PInvestmentClient(wealthdmfConn)
	paymentClient := fedpay.NewPaymentClient(vendornotificationConn)
	abflCallbackClient := vnloansabflpb.NewAbflCallbackClient(vendornotificationConn)
	creditCardClient := vnccpb.NewCreditCardClient(vendornotificationConn)
	sMSClient := sms.NewSMSClient(vendorgatewayConn)

	awsConf, awsErr := awsconfpkg.NewAWSConfig(ctx, gconf.AWS().Region, gconf.Tracing().Enable)
	if awsErr != nil {
		logger.Error(ctx, "error in loading aws v2 config", zap.Error(awsErr))
		if !cfg.IsRemoteDebugEnabled() {
			return err
		}
	}

	sqsClient := sqs.InitSQSClient(awsConf)
	_ = sqsClient

	var broker *events.RudderStackBroker

	// load rudder-stack broker for sending events
	rudderClient, err := analytics.NewWithConfig(
		gconf.Secrets().Ids[config.RudderWriteKey], "http://"+cfg.GetServerEndPoint(cfg.RUDDER_SERVER).URL(),
		analytics.Config{
			Callback:  events.NewCallback(),
			Interval:  gconf.RudderStack().IntervalInSec * time.Second, // nolint: durationcheck
			BatchSize: gconf.RudderStack().BatchSize,
			Verbose:   gconf.RudderStack().Verbose,
		})
	if err != nil {
		logger.Error(ctx, "error in connecting rudder", zap.Error(err))
		return err
	}
	broker = events.NewRudderStackBroker(rudderClient)
	defer broker.Close()

	rateLimiter := ratelimiter.NewRateLimiterV2(store.NewSlidingWindowLogWithRedis(rateLimiterRedisStore), gconf.GrpcRatelimiterParams().RateLimitConfig())
	_ = rateLimiter

	var unaryInterceptors []grpc.UnaryServerInterceptor

	var streamInterceptors []grpc.StreamServerInterceptor

	histogramBuckets := prometheus.DefBuckets
	if len(gconf.LatencyHistogramBuckets()) > 0 {
		histogramBuckets = epifigrpc.AddGrpcLatencyBuckets(gconf.LatencyHistogramBuckets()...)
	}

	serverOptions := []grpc.ServerOption{}
	if gconf.GrpcServerConfig().MaxRecvMsgSize() != nil && gconf.GrpcServerConfig().MaxRecvMsgSize().Size != 0 {
		serverOptions = append(serverOptions, grpc.MaxRecvMsgSize(gconf.GrpcServerConfig().MaxRecvMsgSize().Size))
	}
	var extractFeRespStatusWithErrMsgFunc commoninterceptors.ExtractFeRespStatusWithErrMsgFunc
	extractFeRespStatusWithErrMsgFunc = fepkg.ExtractFeRespStatusWithErrMsgFunc

	s := epifigrpc.NewServerWithConfig(gconf.GrpcServerConfig(), gconf.Flags().TrimDebugMessageFromStatus, histogramBuckets, unaryInterceptors, streamInterceptors, extractFeRespStatusWithErrMsgFunc, serverOptions...)

	if cfg.IsNonProdEnv(gconf.Environment()) {
		reflection.Register(s)
	}

	httpMux := http.NewServeMux()
	_ = httpMux

	var sqsSubscribers []queue.Subscriber
	var subs []queue.Subscriber
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSubs []*sqs.ExtendedSqsSubscriber

	subs, extSubs, err = setupSimulator(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, simulatorCRDB, simulatorSqlDB, usersClient, savingsClient, celestialClient, devP2PInvestmentClient, p2PInvestmentClient, paymentClient, abflCallbackClient, creditCardClient, sMSClient, dobbyCacheStorage)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	explorerpb.RegisterConfigExplorerServer(s, explorer.NewServer(configNameToConfMap))

	// Load rpc level log sampling rate descriptors.
	epifigrpc.GetRPCLogSamplingRatesMapInstance().Load(s)

	serverStartCleanupFn, err := servergenhook.InitSimulatorServer(ctx, s, gconf, awsConf, simulatorSqlDB, simulatorCRDB, vendorgatewayConn, vendornotificationConn, dobbyCacheStorage, usersClient, initNotifier, sqsSubscribers, extendedSqsSubscribers) // Hook configured to start the server
	if err != nil {
		logger.Error(ctx, "failed to start server", zap.Error(err))
		return err
	}
	defer serverStartCleanupFn()

	return nil
}

// nolint: funlen
func setupSimulator(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	simulatorCRDB types2.SimulatorCRDB,
	simulatorSqlDB types.SimulatorSqlDB,
	usersClient userpb.UsersClient,
	savingsClient savingspb.SavingsClient,
	celestialClient celestialpb.CelestialClient,
	devP2PInvestmentClient p2pdspb.DevP2PInvestmentClient,
	p2PInvestmentClient p2ppb.P2PInvestmentClient,
	paymentClient fedpay.PaymentClient,
	abflCallbackClient vnloansabflpb.AbflCallbackClient,
	creditCardClient vnccpb.CreditCardClient,
	sMSClient sms.SMSClient,
	dobbyCacheStorage types.DobbyCacheStorage) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	simulatorConf, err := simulatorconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.SIMULATOR_GRPC_SERVICE))
		return nil, nil, err
	}
	_ = simulatorConf

	simulatorGenConf, err := dynconf.LoadConfig(simulatorconf.Load, genconf2.NewConfig, cfg.SIMULATOR_GRPC_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.SIMULATOR_GRPC_SERVICE))
		return nil, nil, err
	}

	_ = simulatorGenConf

	federalPaymentEngineDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, simulatorGenConf.FederalPaymentEnginePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	federalPaymentCallBackDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, simulatorGenConf.FederalPaymentCallBackPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	federalCardCallBackPublisher, err := sqs.NewPublisherWithConfig(ctx, simulatorGenConf.FederalCardCallBackPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	federalCardDispatchCallBackPublisher, err := sqs.NewPublisherWithConfig(ctx, simulatorGenConf.FederalCardDispatchCallBackPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	federalAccountsCallbackDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, simulatorGenConf.FederalAccountsCallbackPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	federalShippingAddressUpdateDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, simulatorGenConf.FederalShippingAddressUpdatePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	federalDepositEnginePublisher, err := sqs.NewPublisherWithConfig(ctx, simulatorGenConf.FederalDepositEnginePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	federalDepositCallBackPublisher, err := sqs.NewPublisherWithConfig(ctx, simulatorGenConf.FederalDepositCallBackPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	complaintStatusDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, simulatorGenConf.ComplaintStatusPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	federalInboundTransactionNotificationDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, simulatorGenConf.FederalInboundTransactionNotificationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}

	livenessS3Client := s3pkg.NewClient(awsConf, simulatorGenConf.Application().Liveness.LivenessBucketName)
	camsS3Client := s3pkg.NewClient(awsConf, simulatorGenConf.Application().Cams.CamsS3Bucket)

	fundTransferService, err := wire.InitialiseFederalFundTransferService(simulatorCRDB, simulatorSqlDB, federalPaymentEngineDelayPublisher, simulatorConf, simulatorGenConf)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	fedfundtransferpb.RegisterFundTransferServer(s, fundTransferService)

	b2CFundTransferService, err := wire.InitialiseFederalB2CFundTransferService(simulatorCRDB, federalPaymentEngineDelayPublisher, simulatorConf)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	fedb2cfundtransferpb.RegisterFundTransferServer(s, b2CFundTransferService)

	standingInstructionService, err := wire.InitializeFederalStandingInstructionService(simulatorCRDB, federalPaymentCallBackDelayPublisher, simulatorConf)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	fedstandinginstructionpb.RegisterStandingInstructionServer(s, standingInstructionService)

	cardProvisioningService := wire.InitializeFederalCardProvisioningService(simulatorConf, simulatorGenConf, federalCardCallBackPublisher, federalCardDispatchCallBackPublisher, simulatorCRDB)

	cardpb.RegisterCardProvisioningServer(s, cardProvisioningService)

	service := wire.InitializeFederalPartnerSDKService(simulatorConf, simulatorCRDB)

	federalpartnersdkpb.RegisterPartnerSDKServer(s, service)

	serviceVar2 := wire.InitializeM2PPartnerSDKService(simulatorConf, simulatorCRDB)

	m2ppartnersdkpb.RegisterPartnerSDKServer(s, serviceVar2)

	accountCreationService := wire.InitialiseFederalAccountCreationService(simulatorSqlDB, simulatorCRDB, federalAccountsCallbackDelayPublisher, simulatorGenConf, usersClient, savingsClient)

	accountfederal.RegisterAccountsServer(s, accountCreationService)

	enquiryService := wire.InitializeEnquiryService(simulatorSqlDB, simulatorCRDB, simulatorConf)

	enquiryfederal.RegisterEnquiryServer(s, enquiryService)

	shippingPreferenceService := wire.InitializeFederalShippingPreferenceService(federalShippingAddressUpdateDelayPublisher)

	shippingpreferencepb.RegisterShippingPreferenceServer(s, shippingPreferenceService)

	nameMatchService := wire.InitializeNamematchService()

	inhousenamecheck.RegisterNameMatchServer(s, nameMatchService)

	shipwayService := wire.InitialiseShipwayService()

	shipwaypb.RegisterShipwayServer(s, shipwayService)

	dLService := wire.InitializeDLValidationService()

	dlpb.RegisterDLServer(s, dLService)

	serviceVar3 := wire.InitializeSeonService()

	seon2.RegisterSeonServer(s, serviceVar3)

	idValidateService := wire.InitializeIdValidateService()

	idvalidate2.RegisterIdValidateServer(s, idValidateService)

	serviceVar4 := wire.InitializeExternalValidateService()

	extvalidate2.RegisterExternalValidateServer(s, serviceVar4)

	serviceVar5 := wire.InitializeLocationService()

	location2.RegisterLocationServer(s, serviceVar5)

	simulatorP2PDevEntity := wire.InitializeSimulatorP2PDevService(simulatorCRDB, celestialClient, devP2PInvestmentClient, p2PInvestmentClient, paymentClient)

	simp2pdspb.RegisterDevSimulatorP2PInvestmentServer(s, simulatorP2PDevEntity)

	serviceVar6 := wire.InitializePhoneNetworkService()

	phonenetwork2.RegisterPhoneNetworkServer(s, serviceVar6)

	serviceVar7 := wire.InitializeChequebookService(simulatorGenConf)

	simulatorchkbqpb.RegisterChequebookServer(s, serviceVar7)

	authService := wire.InitializeFederalAuthService(simulatorSqlDB, simulatorCRDB, simulatorConf)

	authfederal.RegisterAuthServer(s, authService)

	depositService := wire.InitializeFederalDepositService(simulatorCRDB, federalDepositEnginePublisher, simulatorConf)

	federaldeposit2.RegisterDepositServer(s, depositService)

	depositEngine := wire.InitializeFederalDepositEngineConsumer(simulatorCRDB, federalDepositCallBackPublisher, simulatorConf)

	federaldeposit2.RegisterDepositEngineConsumerServer(s, depositEngine)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, simulatorGenConf.FederalDepositEngineSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		federaldeposit2.RegisterProcessDepositMethodToSubscriber(subscriber, depositEngine)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	depositCallBack := wire.InitializeFederalDepositCallbackConsumer()

	federaldeposit2.RegisterCallBackConsumerServer(s, depositCallBack)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, simulatorGenConf.FederalDepositCallBackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		federaldeposit2.RegisterProcessDepositCallbackMethodToSubscriber(subscriber, depositCallBack)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	paymentEngine := wire.InitializeFederalPaymentEngineConsumer(simulatorCRDB, federalPaymentCallBackDelayPublisher, simulatorConf)

	fedfundtransferpb.RegisterPaymentEngineConsumerServer(s, paymentEngine)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, simulatorGenConf.FederalPaymentEngineSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		fedfundtransferpb.RegisterProcessPaymentMethodToSubscriber(subscriber, paymentEngine)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	paymentCallBack := wire.InitializeFederalPaymentCallBackConsumer(simulatorCRDB, complaintStatusDelayPublisher)

	fedfundtransferpb.RegisterCallBackConsumerServer(s, paymentCallBack)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, simulatorGenConf.FederalPaymentCallBackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		fedfundtransferpb.RegisterProcessCallBackMethodToSubscriber(subscriber, paymentCallBack)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, simulatorGenConf.FederalUpiCallBackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		fedfundtransferpb.RegisterProcessUpiCallBackMethodToSubscriber(subscriber, paymentCallBack)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, simulatorGenConf.ComplaintStatusSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		fedfundtransferpb.RegisterProcessComplaintStatusChangeMethodToSubscriber(subscriber, paymentCallBack)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	cardCallBack := wire.InitializeFederalCardCallBackConsumer()

	cardpb.RegisterCallbackConsumerServer(s, cardCallBack)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, simulatorGenConf.FederalCardCallBackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		cardpb.RegisterProcessCreateCardCallBackMethodToSubscriber(subscriber, cardCallBack)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, simulatorGenConf.FederalCardDispatchCallBackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		cardpb.RegisterProcessPhysicalCardDispatchCallBackMethodToSubscriber(subscriber, cardCallBack)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	shippingAddressUpdateCallBackService := wire.InitializeFederalShippingAddressUpdateCallbackConsumer()

	shippingpreferencepb.RegisterCallbackConsumerServer(s, shippingAddressUpdateCallBackService)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, simulatorGenConf.FederalShippingAddressUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		shippingpreferencepb.RegisterProcessShippingAddressUpdateCallBackMethodToSubscriber(subscriber, shippingAddressUpdateCallBackService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	serviceVar8 := wire.InitializeProfileEvaluatorService(simulatorCRDB, simulatorGenConf)

	profileevaluatorpb2.RegisterProfileEvaluatorServer(s, serviceVar8)

	accountsCallback := wire.InitializeCallbackConsumer()

	accountfederal.RegisterCallbackConsumerServer(s, accountsCallback)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, simulatorGenConf.FederalAccountsCallbackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		accountfederal.RegisterProcessCustomerCreationCallbackMethodToSubscriber(subscriber, accountsCallback)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	inboundTransactionNotificationConsumer := wire.InitializeFederalInboundTransactionNotificationConsumer(simulatorConf)

	fedfundtransferpb.RegisterNotificationConsumerServer(s, inboundTransactionNotificationConsumer)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, simulatorGenConf.FederalInboundTransactionNotificationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		fedfundtransferpb.RegisterProcessInboundNotificationMethodToSubscriber(subscriber, inboundTransactionNotificationConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	serviceVar9 := wire.InitializeLiquiloansService(simulatorCRDB, celestialClient, p2PInvestmentClient, paymentClient)

	liquiloanspb.RegisterLiquiloansServer(s, serviceVar9)

	serviceVar10 := wire.InitialiseInhouseRiskService()

	riskpb.RegisterRiskServer(s, serviceVar10)

	inhouseLivenessService := wire.InitializeInHouseLivenessService(livenessS3Client, simulatorConf, usersClient)

	inhouselivpb2.RegisterLivenessServer(s, inhouseLivenessService)

	serviceVar11 := wire.InitializePreApprovedLoanService(simulatorCRDB, simulatorSqlDB, abflCallbackClient, simulatorConf, usersClient, simulatorGenConf)

	preapprovedpb.RegisterPreApprovedLoanServer(s, serviceVar11)

	serviceVar12 := wire.InitializeEsignService()

	esignpb.RegisterESignServer(s, serviceVar12)

	accountsDevService := wire.InitializeAccountsDevService(simulatorCRDB)

	developer2.RegisterAccountsDevServiceServer(s, accountsDevService)

	serviceVar13 := wire.InitialiseCreditCardService(simulatorCRDB, creditCardClient)

	creditcardpb.RegisterCreditCardServer(s, serviceVar13)

	consumerService := wire.InitializeCamsConsumerService(camsS3Client, simulatorConf)

	if func(config *config.Config) bool {
		return !cfg.IsProdEnv(config.Environment)
	}(conf) {
		camspb.RegisterCamsServiceServer(s, consumerService)
	}

	if func(config *config.Config) bool {
		return !cfg.IsProdEnv(config.Environment)
	}(conf) {
		subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, simulatorGenConf.UploadCreditMISToVendorSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			camspb.RegisterGenerateReverseFeedFileAfterPaymentNotificationMethodToSubscriber(subscriber, consumerService)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return nil, nil, err
		}
		if shouldStartWorker {
			sqsSubscribers = append(sqsSubscribers, subs)
		}
	}

	enachService, err := wire.InitializeEnachService(simulatorCRDB, simulatorConf, federalInboundTransactionNotificationDelayPublisher)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	enachfedpb.RegisterEnachServer(s, enachService)

	serviceVar14 := wire.InitializeBusinessRuleEngineService(simulatorGenConf)

	bre2.RegisterBusinessRuleEngineServer(s, serviceVar14)

	cKycService := wire.InitializeFederalCKycService(simulatorConf, simulatorCRDB)

	federal4.RegisterCkycServer(s, cKycService)

	serviceVar15 := wire.InitialiseCreditLineService()

	creditline2.RegisterCreditLineServer(s, serviceVar15)

	customerService := wire.InitializeOpenbankingCustomerService(simulatorGenConf, simulatorSqlDB, simulatorCRDB, federalAccountsCallbackDelayPublisher)

	customer2.RegisterCustomerServer(s, customerService)

	serviceVar16 := wire.InitializeEPANService(simulatorGenConf)

	epanpb.RegisterEPANServer(s, serviceVar16)

	inhouseepanpb.RegisterEPANServer(s, serviceVar16)

	employmentService := wire.InitializeKarzaEmploymentService()

	karzaempl2.RegisterEmploymentServer(s, employmentService)

	eKYCService := wire.InitializeFederalEKycService(sMSClient, simulatorCRDB)

	federal4.RegisterFederalEKYCServer(s, eKYCService)

	serviceVar17 := wire.InitialiseIncomeEstimatorService(simulatorGenConf, dobbyCacheStorage, usersClient)

	incomeestimator2.RegisterIncomeEstimatorServer(s, serviceVar17)

	lienService := wire.InitializeFederalLienService()

	federal6.RegisterLienServer(s, lienService)

	livenessService := wire.InitializeVeri5LivenessService()

	veri2.RegisterLivenessServer(s, livenessService)

	livenessServiceVar2 := wire.InitializeKarzaLivenessService(simulatorConf)

	karza2.RegisterLivenessServer(s, livenessServiceVar2)

	pANService := wire.InitializeFederalPANService(simulatorConf, simulatorGenConf)

	panfederal.RegisterPANServer(s, pANService)

	savingsService := wire.InitializeFederalSavingsService(simulatorCRDB)

	fedsavingspb.RegisterSavingsServer(s, savingsService)

	serviceVar18 := wire.InitializeTieringService()

	tiering2.RegisterTieringServer(s, serviceVar18)

	uNNameCheckService := wire.InitializeFederalUNNameCheckService(simulatorConf)

	federalnamecheck2.RegisterUNNameCheckServer(s, uNNameCheckService)

	serviceVar19 := wire.InitializeWatsonClientService()

	watsonclientpb.RegisterWatsonClientServer(s, serviceVar19)

	serviceVar20 := wire.InitializeITRService(simulatorGenConf)

	itrsimulatorpb.RegisterITRServer(s, serviceVar20)

	serviceVar21 := wire.InitialiseDobbyService(simulatorGenConf, dobbyCacheStorage, usersClient)

	dobby2.RegisterDobbyServer(s, serviceVar21)

	serviceVar22 := wire.InitialiseCibilService(simulatorGenConf)

	cibil2.RegisterCibilServer(s, serviceVar22)

	karzaService := wire.InitKarzaService()

	karzapb.RegisterKarzaServer(s, karzaService)

	serviceVar23 := wire.InitialiseOCRService(simulatorGenConf)

	inhouse2.RegisterOCRServer(s, serviceVar23)

	serviceVar24 := wire.InitialiseKarzaOCRService(simulatorGenConf)

	karza3.RegisterOCRServer(s, serviceVar24)

	serviceVar25 := wire.InitialiseDreamfolksService()

	dreamfolkssimulatorpb.RegisterDreamfolksServer(s, serviceVar25)

	serviceVar26 := wire.InitialiseFederalVKYCService()

	federal7.RegisterVKYCServer(s, serviceVar26)

	serviceVar27 := wire.InitialiseDocsService()

	docs2.RegisterDocsServer(s, serviceVar27)

	serviceVar28 := wire.InitialiseScienapticService()

	scienaptic2.RegisterScienapticServer(s, serviceVar28)

	configNameToConfMap[cfg.ConfigName(cfg.SIMULATOR_GRPC_SERVICE)] = &commonexplorer.Config{StaticConf: &simulatorconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

func initLogger() error {
	// Load configuration
	conf, err := config.Load(cfg.SIMULATOR_GRPC_SERVER)
	if err != nil {
		// cannot log error since logger is not initialized yet.
		return err
	}

	logger.InitV2(conf.Environment, conf.Logging)
	if conf.SecureLogging != nil {
		logger.InitSecureLoggerV2(conf.SecureLogging)
	}
	return nil
}

func getEnvNamespaceClient(ns temporalpkg.Namespace, env string) string {
	if cfg.IsLocalEnv(env) {
		return strings.ToLower(string(ns))
	}

	return fmt.Sprintf("%s-%s", env, strings.ToLower(string(ns)))
}

func getSQSConsumerInterceptors() []grpc.UnaryServerInterceptor {
	return []grpc.UnaryServerInterceptor{questinterceptor.UserContextInterceptor}
}
