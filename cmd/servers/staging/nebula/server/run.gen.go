// code generated by tools/servergen
package server

import (
	context "context"
	"fmt"
	http "net/http"
	strings "strings"
	time "time"

	awssdk "github.com/aws/aws-sdk-go-v2/aws"
	awssqs "github.com/aws/aws-sdk-go-v2/service/sqs"
	prometheus "github.com/prometheus/client_golang/prometheus"
	redis "github.com/redis/go-redis/v9"
	analytics "github.com/rudderlabs/analytics-go"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
	reflection "google.golang.org/grpc/reflection"
	gorm "gorm.io/gorm"

	celestialpb "github.com/epifi/be-common/api/celestial"
	consumer "github.com/epifi/be-common/api/celestial/consumer"
	"github.com/epifi/be-common/pkg/async/goroutine"
	awsconfpkg "github.com/epifi/be-common/pkg/aws/v2/config"
	sqs "github.com/epifi/be-common/pkg/aws/v2/sqs"
	awswire "github.com/epifi/be-common/pkg/aws/v2/wire"
	cfg "github.com/epifi/be-common/pkg/cfg"
	consul "github.com/epifi/be-common/pkg/cfg/consul"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	commonexplorer "github.com/epifi/be-common/pkg/cfg/explorer"
	config "github.com/epifi/be-common/pkg/cmd/config"
	genconf "github.com/epifi/be-common/pkg/cmd/config/genconf"
	types "github.com/epifi/be-common/pkg/cmd/types"
	epificontext "github.com/epifi/be-common/pkg/epificontext"
	epifigrpc "github.com/epifi/be-common/pkg/epifigrpc"
	commoninterceptors "github.com/epifi/be-common/pkg/epifigrpc/interceptors"
	resolver "github.com/epifi/be-common/pkg/epifigrpc/resolver"
	epifiserver "github.com/epifi/be-common/pkg/epifiserver"
	temporalpkg "github.com/epifi/be-common/pkg/epifitemporal"
	errgroup "github.com/epifi/be-common/pkg/errgroup"
	events "github.com/epifi/be-common/pkg/events"
	logger "github.com/epifi/be-common/pkg/logger"
	profiling "github.com/epifi/be-common/pkg/profiling"
	queue "github.com/epifi/be-common/pkg/queue"
	ratelimiter "github.com/epifi/be-common/pkg/ratelimiter"
	store "github.com/epifi/be-common/pkg/ratelimiter/store"
	storage "github.com/epifi/be-common/pkg/storage"
	storage2 "github.com/epifi/be-common/pkg/storage/v2"
	usecase "github.com/epifi/be-common/pkg/storage/v2/usecase"
	otel "github.com/epifi/be-common/pkg/tracing/opentelemetry"
	servermetrics "github.com/epifi/be-common/tools/servergen/metrics"
	celestialdeveloper "github.com/epifi/gamma/api/celestial/developer"
	explorerpb "github.com/epifi/gamma/api/pkg/cfg/explorer"
	rewardofferspb "github.com/epifi/gamma/api/rewards/rewardoffers"
	slackpb "github.com/epifi/gamma/api/vendorgateway/slack_bot"
	zenduty "github.com/epifi/gamma/api/vendorgateway/zenduty"
	celestialconf "github.com/epifi/gamma/celestial/config"
	genconf3 "github.com/epifi/gamma/celestial/config/genconf"
	wire2 "github.com/epifi/gamma/celestial/wire"
	hook "github.com/epifi/gamma/cmd/servers/staging/nebula/hook"
	slackbot "github.com/epifi/gamma/cmd/service_groups/slack_bot"
	explorer "github.com/epifi/gamma/pkg/cfg/explorer"
	questinterceptor "github.com/epifi/gamma/pkg/epifigrpc/interceptors/quest"
	fepkg "github.com/epifi/gamma/pkg/frontend/msg"
	slackbotconf "github.com/epifi/gamma/slack_bot/config"
	genconf4 "github.com/epifi/gamma/slack_bot/config/genconf"
	wire3 "github.com/epifi/gamma/slack_bot/wire"
	varysconf "github.com/epifi/gamma/varys/config"
	genconf2 "github.com/epifi/gamma/varys/config/genconf"
	wire "github.com/epifi/gamma/varys/wire"
)

// nolint: funlen
func Run(ctx context.Context, initNotifier chan<- cfg.ServerName) error {
	// Panic handler for logging panics in standard format
	epifiserver.HandlePanic()
	resolver.RegisterBuilder(resolver.ConsulSchemeName)
	var configNameToConfMap = make(commonexplorer.CfgExplorerMap)

	err := initLogger()
	if err != nil {
		// cannot log error since logger maynot be initialized yet.
		return err
	}
	ctx = epificontext.WithTraceId(ctx, metadata.MD{})
	// Load static configuration
	conf, err := config.Load(cfg.NEBULA_SERVER)
	if err != nil {
		logger.Error(ctx, "failed to load static conf", zap.Error(err))
		return err
	}
	_ = conf
	// Load generated dynamic configuration
	gconf, err := genconf.Load(cfg.NEBULA_SERVER)
	if err != nil {
		logger.Error(ctx, "failed to load dynamic conf", zap.Error(err))
		return err
	}
	logger.Info(ctx, "initiating server")

	if gconf.Profiling().AutomaticProfiling().EnableAutoProfiling() {
		profilingErr := profiling.AutomaticProfiling(context.Background(), cfg.NEBULA_SERVER, gconf.Profiling(), gconf.Environment(), gconf.AWS().Region)
		if profilingErr != nil {
			logger.Error(ctx, "failed to start automatic profiling", zap.Error(profilingErr))
			return profilingErr
		}
	}

	if gconf.Tracing().Enable {
		_, shutdown, tracingErr := otel.ConfigureOpenTelemetryV2(context.Background(), cfg.NEBULA_SERVER, gconf.Environment(), gconf.Profiling().PyroscopeProfiling().EnablePyroscope())
		if tracingErr != nil {
			logger.Error(ctx, "failed to configure open telemetry", zap.Error(tracingErr))
			return err
		}
		defer shutdown()
	}

	err = profiling.PyroscopeProfilingV2(gconf.Name(), gconf.Profiling(), gconf.Environment())
	if err != nil {
		logger.Error(ctx, "error starting pyroscope profiler", zap.Error(err))
	}

	if !cfg.IsLocalEnv(gconf.Environment()) && !cfg.IsRemoteDebugEnabled() && !cfg.IsTestTenantEnabled() && !cfg.IsTeamSpaceTenant() {
		initDisc, _ := consul.GetBool(consul.GetInitDiscoveryKey(gconf.Environment(), string(gconf.Name())))
		queue.SetWorkerInitialization(initDisc)
	}

	rateLimiterRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()[gconf.RedisRateLimiterName()], gconf.Tracing().Enable)
	defer func() { _ = rateLimiterRedisStore.Close() }()

	var (
		crdbResourceMap    *storage2.DBResourceProvider[*gorm.DB]
		crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor]
	)

	vendorgatewayConn := epifigrpc.NewServerConn(cfg.NEBULA_SERVER, cfg.VENDOR_GATEWAY_SERVER)
	defer epifigrpc.CloseConn(vendorgatewayConn)
	zendutyClient := zenduty.NewZendutyClient(vendorgatewayConn)
	useCaseDbResourceProvider, useCaseDbResourceProviderTxnExec, useCaseDbResourceProviderTeardown, err := usecase.NewDBResourceProvider(gconf.UseCaseDBConfigMap(), gconf.Tracing().Enable, gconf.PgdbConns())
	if err != nil {
		logger.Error(ctx, "failed to initialize usecase db resource provider", zap.Error(err))
		return err
	}
	defer func() {
		useCaseDbResourceProviderTeardown()
	}()
	_ = useCaseDbResourceProvider
	_ = useCaseDbResourceProviderTxnExec
	slackBotClient := slackpb.NewSlackBotClient(vendorgatewayConn)
	questRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["QuestRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = questRedisStore.Close() }()
	growthinfraConn := epifigrpc.NewServerConn(cfg.NEBULA_SERVER, cfg.GROWTH_INFRA_SERVER)
	defer epifigrpc.CloseConn(growthinfraConn)
	rewardOffersClient := rewardofferspb.NewRewardOffersClient(growthinfraConn)

	awsConf, awsErr := awsconfpkg.NewAWSConfig(ctx, gconf.AWS().Region, gconf.Tracing().Enable)
	if awsErr != nil {
		logger.Error(ctx, "error in loading aws v2 config", zap.Error(awsErr))
		if !cfg.IsRemoteDebugEnabled() {
			return err
		}
	}

	sqsClient := sqs.InitSQSClient(awsConf)
	_ = sqsClient

	var broker *events.RudderStackBroker

	// load rudder-stack broker for sending events
	rudderClient, err := analytics.NewWithConfig(
		gconf.Secrets().Ids[config.RudderWriteKey], "http://"+cfg.GetServerEndPoint(cfg.RUDDER_SERVER).URL(),
		analytics.Config{
			Callback:  events.NewCallback(),
			Interval:  gconf.RudderStack().IntervalInSec * time.Second, // nolint: durationcheck
			BatchSize: gconf.RudderStack().BatchSize,
			Verbose:   gconf.RudderStack().Verbose,
		})
	if err != nil {
		logger.Error(ctx, "error in connecting rudder", zap.Error(err))
		return err
	}
	broker = events.NewRudderStackBroker(rudderClient)
	defer broker.Close()

	rateLimiter := ratelimiter.NewRateLimiterV2(store.NewSlidingWindowLogWithRedis(rateLimiterRedisStore), gconf.GrpcRatelimiterParams().RateLimitConfig())
	_ = rateLimiter

	var unaryInterceptors []grpc.UnaryServerInterceptor

	var streamInterceptors []grpc.StreamServerInterceptor

	histogramBuckets := prometheus.DefBuckets
	if len(gconf.LatencyHistogramBuckets()) > 0 {
		histogramBuckets = epifigrpc.AddGrpcLatencyBuckets(gconf.LatencyHistogramBuckets()...)
	}

	serverOptions := []grpc.ServerOption{}
	if gconf.GrpcServerConfig().MaxRecvMsgSize() != nil && gconf.GrpcServerConfig().MaxRecvMsgSize().Size != 0 {
		serverOptions = append(serverOptions, grpc.MaxRecvMsgSize(gconf.GrpcServerConfig().MaxRecvMsgSize().Size))
	}
	var extractFeRespStatusWithErrMsgFunc commoninterceptors.ExtractFeRespStatusWithErrMsgFunc
	extractFeRespStatusWithErrMsgFunc = fepkg.ExtractFeRespStatusWithErrMsgFunc

	s := epifigrpc.NewSecureServerWithInterceptors(gconf.GrpcServerConfig(), gconf.Flags().TrimDebugMessageFromStatus, string(gconf.Name()), histogramBuckets, unaryInterceptors, streamInterceptors, extractFeRespStatusWithErrMsgFunc, serverOptions...)

	if cfg.IsNonProdEnv(gconf.Environment()) {
		reflection.Register(s)
	}

	httpMux := http.NewServeMux()
	_ = httpMux

	var sqsSubscribers []queue.Subscriber
	var subs []queue.Subscriber
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSubs []*sqs.ExtendedSqsSubscriber

	subs, extSubs, err = setupVarys(ctx, s, httpMux, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, zendutyClient)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupCelestial(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, useCaseDbResourceProvider, useCaseDbResourceProviderTxnExec)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	servermetrics.ResetServiceGroupInitFailureToZero(cfg.NEBULA_SERVER, cfg.SLACK_BOT_SERVICE)
	subs, extSubs, err = setupSlackbot(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, slackBotClient, questRedisStore, rewardOffersClient)
	if err != nil {
		servermetrics.RecordServiceGroupInitFailure(cfg.NEBULA_SERVER, cfg.SLACK_BOT_SERVICE)
		logger.Error(ctx, "failed to init service group", zap.String("service_group", string(cfg.SLACK_BOT_SERVICE)), zap.Error(err))
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	explorerpb.RegisterConfigExplorerServer(s, explorer.NewServer(configNameToConfMap))

	cleanupFn, err := hook.InitVarysHttpServer(gconf, httpMux) // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "InitVarysHttpServer"), zap.Error(err))
		return err
	}
	defer cleanupFn()

	// Load rpc level log sampling rate descriptors.
	epifigrpc.GetRPCLogSamplingRatesMapInstance().Load(s)

	initNotifier <- gconf.Name()

	// TODO(Sundeep): Remove this additional logic for starting the servers inside go-routine, by exposing functions
	// from epifiserver package that take errgroup.Group as parameter.
	grp, _ := errgroup.WithContext(context.Background())

	grp.Go(func() error {
		epifiserver.StartSecureServer(s, gconf.ServerPorts(), string(gconf.Name()))
		return nil
	})

	// Wait for server health before starting SQS workers
	if queue.IsWorkerInitializationEnabled() {
		goroutine.RunWithCtx(ctx, func(ctx context.Context) {
			if epifiserver.IsServerReady(gconf.ServerPorts().HttpPort) {
				logger.Info(ctx, "Server is healthy, starting SQS workers")
				sqs.StartSQSWorkers(ctx, sqsSubscribers, extendedSqsSubscribers)
			} else {
				logger.PanicWithCtx(ctx, "Server health check timed out, not starting SQS workers")
			}
		})
	}
	if err := grp.Wait(); err != nil {
		return err
	}

	return nil
}

// nolint: funlen
func setupVarys(ctx context.Context, s *grpc.Server, httpMux *http.ServeMux, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	zendutyClient zenduty.ZendutyClient) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	varysConf, err := varysconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.VARYS_SERVICE))
		return nil, nil, err
	}
	_ = varysConf

	varysGenConf, err := dynconf.LoadConfig(varysconf.Load, genconf2.NewConfig, cfg.VARYS_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.VARYS_SERVICE))
		return nil, nil, err
	}

	_ = varysGenConf

	service := wire.InitialiseVarysService(varysGenConf, zendutyClient)

	httpMux.HandleFunc("/varys/alertmanager-webhook", service.AlertmanagerWebhookHandler)

	httpMux.HandleFunc("/varys/vendor-api-failure/alertmanager-webhook", service.VendorApiFailureWebhookHandler)

	configNameToConfMap[cfg.ConfigName(cfg.VARYS_SERVICE)] = &commonexplorer.Config{StaticConf: &varysconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupCelestial(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	useCaseDbResourceProvider *usecase.DBResourceProvider[*gorm.DB],
	useCaseDbResourceProviderTxnExec *usecase.DBResourceProvider[storage2.IdempotentTxnExecutor]) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	celestialConf, err := celestialconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.CELESTIAL_SERVICE))
		return nil, nil, err
	}
	_ = celestialConf

	celestialGenConf, err := dynconf.LoadConfig(celestialconf.Load, genconf3.NewConfig, cfg.CELESTIAL_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.CELESTIAL_SERVICE))
		return nil, nil, err
	}

	_ = celestialGenConf

	pgdbConns := gconf.PgdbConns()

	serviceVar2 := wire2.InitializeService(ctx, pgdbConns, celestialGenConf, awsConf, useCaseDbResourceProvider, useCaseDbResourceProviderTxnExec)

	celestialpb.RegisterCelestialServer(s, serviceVar2)

	celestialDevService := wire2.InitializeDevService(pgdbConns, useCaseDbResourceProvider)

	celestialdeveloper.RegisterDeveloperServer(s, celestialDevService)

	serviceVar3 := wire2.InitializeConsumerService(pgdbConns, celestialGenConf, useCaseDbResourceProvider)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, celestialGenConf.InitiateProcrastinatorSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer.RegisterInitiateProcrastinatorWorkflowMethodToSubscriber(subscriber, serviceVar3)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	extSub, err = awswire.InitializeExtendedSubscriberWithGenConf(ctx, celestialGenConf.InitiateWorkflowSubscriber(), awsConf, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer.RegisterInitiateWorkflowMethodToSubscriber(subscriber, serviceVar3)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors())
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if extSub.ShouldStartWorker {
		extendedSqsSubscribers = append(extendedSqsSubscribers, extSub)
	}

	extSub, err = awswire.InitializeExtendedSubscriberWithGenConf(ctx, celestialGenConf.SignalWorkflowSubscriber(), awsConf, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer.RegisterSignalWorkflowMethodToSubscriber(subscriber, serviceVar3)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors())
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if extSub.ShouldStartWorker {
		extendedSqsSubscribers = append(extendedSqsSubscribers, extSub)
	}
	configNameToConfMap[cfg.ConfigName(cfg.CELESTIAL_SERVICE)] = &commonexplorer.Config{StaticConf: &celestialconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupSlackbot(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	slackBotClient slackpb.SlackBotClient,
	questRedisStore types.QuestRedisStore,
	rewardOffersClient rewardofferspb.RewardOffersClient) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	slack_botConf, err := slackbotconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.SLACK_BOT_SERVICE))
		return nil, nil, err
	}
	_ = slack_botConf

	slack_botGenConf, err := dynconf.LoadConfig(slackbotconf.Load, genconf4.NewConfig, cfg.SLACK_BOT_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.SLACK_BOT_SERVICE))
		return nil, nil, err
	}

	_ = slack_botGenConf

	serviceVar4, err := wire3.InitializeSlackBotManagerService(ctx, slack_botGenConf, slackBotClient, questRedisStore, rewardOffersClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	configNameToConfMap[cfg.ConfigName(cfg.SLACK_BOT_SERVICE)] = &commonexplorer.Config{StaticConf: &slackbotconf.Config{}, QuestIntegratedConfig: nil}
	err = slackbot.AfterServiceGroupInit(serviceVar4)
	if err != nil {
		logger.Error(ctx, "failed to run AfterServicesInitHook", zap.Error(err))
		return nil, nil, err
	}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

func initLogger() error {
	// Load configuration
	conf, err := config.Load(cfg.NEBULA_SERVER)
	if err != nil {
		// cannot log error since logger is not initialized yet.
		return err
	}

	logger.InitV2(conf.Environment, conf.Logging)
	if conf.SecureLogging != nil {
		logger.InitSecureLoggerV2(conf.SecureLogging)
	}
	return nil
}

func getEnvNamespaceClient(ns temporalpkg.Namespace, env string) string {
	if cfg.IsLocalEnv(env) {
		return strings.ToLower(string(ns))
	}

	return fmt.Sprintf("%s-%s", env, strings.ToLower(string(ns)))
}

func getSQSConsumerInterceptors() []grpc.UnaryServerInterceptor {
	return []grpc.UnaryServerInterceptor{questinterceptor.UserContextInterceptor}
}
