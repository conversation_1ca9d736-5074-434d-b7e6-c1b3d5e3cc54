// code generated by tools/servergen
package server

import (
	context "context"
	"fmt"
	http "net/http"
	strings "strings"
	time "time"

	awssdk "github.com/aws/aws-sdk-go-v2/aws"
	awssqs "github.com/aws/aws-sdk-go-v2/service/sqs"
	prometheus "github.com/prometheus/client_golang/prometheus"
	redis "github.com/redis/go-redis/v9"
	analytics "github.com/rudderlabs/analytics-go"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
	reflection "google.golang.org/grpc/reflection"
	gorm "gorm.io/gorm"

	celestialpb "github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/pkg/async/goroutine"
	awsconfpkg "github.com/epifi/be-common/pkg/aws/v2/config"
	s3pkg "github.com/epifi/be-common/pkg/aws/v2/s3"
	sqs "github.com/epifi/be-common/pkg/aws/v2/sqs"
	cache "github.com/epifi/be-common/pkg/cache"
	cfg "github.com/epifi/be-common/pkg/cfg"
	consul "github.com/epifi/be-common/pkg/cfg/consul"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	commonexplorer "github.com/epifi/be-common/pkg/cfg/explorer"
	config "github.com/epifi/be-common/pkg/cmd/config"
	genconf "github.com/epifi/be-common/pkg/cmd/config/genconf"
	types "github.com/epifi/be-common/pkg/cmd/types"
	epificontext "github.com/epifi/be-common/pkg/epificontext"
	epifigrpc "github.com/epifi/be-common/pkg/epifigrpc"
	commoninterceptors "github.com/epifi/be-common/pkg/epifigrpc/interceptors"
	servergenwire "github.com/epifi/be-common/pkg/epifigrpc/interceptors/servergen_wire"
	resolver "github.com/epifi/be-common/pkg/epifigrpc/resolver"
	epifiserver "github.com/epifi/be-common/pkg/epifiserver"
	temporalpkg "github.com/epifi/be-common/pkg/epifitemporal"
	errgroup "github.com/epifi/be-common/pkg/errgroup"
	events "github.com/epifi/be-common/pkg/events"
	logger "github.com/epifi/be-common/pkg/logger"
	profiling "github.com/epifi/be-common/pkg/profiling"
	queue "github.com/epifi/be-common/pkg/queue"
	ratelimiter "github.com/epifi/be-common/pkg/ratelimiter"
	store "github.com/epifi/be-common/pkg/ratelimiter/store"
	storage "github.com/epifi/be-common/pkg/storage"
	storage2 "github.com/epifi/be-common/pkg/storage/v2"
	storagev2analytics "github.com/epifi/be-common/pkg/storage/v2/analytics"
	otel "github.com/epifi/be-common/pkg/tracing/opentelemetry"
	operstatuspb "github.com/epifi/gamma/api/accounts/operstatus"
	actor "github.com/epifi/gamma/api/actor"
	auth "github.com/epifi/gamma/api/auth"
	livpb "github.com/epifi/gamma/api/auth/liveness"
	location "github.com/epifi/gamma/api/auth/location"
	bankcust "github.com/epifi/gamma/api/bankcust"
	categorizer "github.com/epifi/gamma/api/categorizer"
	commspb "github.com/epifi/gamma/api/comms"
	creditreportv2pb "github.com/epifi/gamma/api/creditreportv2"
	derivedattributes "github.com/epifi/gamma/api/creditreportv2/derivedattributes"
	cxticketpb "github.com/epifi/gamma/api/cx/ticket"
	depositpb "github.com/epifi/gamma/api/deposit"
	employment "github.com/epifi/gamma/api/employment"
	inappreferralpb "github.com/epifi/gamma/api/inappreferral"
	kycpb "github.com/epifi/gamma/api/kyc"
	merchant "github.com/epifi/gamma/api/merchant"
	order "github.com/epifi/gamma/api/order"
	paymentpb "github.com/epifi/gamma/api/order/payment"
	paypb "github.com/epifi/gamma/api/pay"
	pipb "github.com/epifi/gamma/api/paymentinstrument"
	explorerpb "github.com/epifi/gamma/api/pkg/cfg/explorer"
	preapprovedloanpb "github.com/epifi/gamma/api/preapprovedloan"
	product "github.com/epifi/gamma/api/product"
	riskpb "github.com/epifi/gamma/api/risk"
	casemanagementpb "github.com/epifi/gamma/api/risk/case_management"
	riskdeveloper "github.com/epifi/gamma/api/risk/developer"
	leapb "github.com/epifi/gamma/api/risk/lea"
	mnrl "github.com/epifi/gamma/api/risk/mnrl"
	profilepb "github.com/epifi/gamma/api/risk/profile"
	redlistpb "github.com/epifi/gamma/api/risk/redlist"
	transactionmonitoring "github.com/epifi/gamma/api/risk/transaction_monitoring"
	txnmonitoringdronapaypb "github.com/epifi/gamma/api/risk/transaction_monitoring/dronapay"
	whitelistpb "github.com/epifi/gamma/api/risk/whitelist"
	salaryprogrampb "github.com/epifi/gamma/api/salaryprogram"
	savingspb "github.com/epifi/gamma/api/savings"
	screenerpb "github.com/epifi/gamma/api/screener"
	segment "github.com/epifi/gamma/api/segment"
	tieringpb "github.com/epifi/gamma/api/tiering"
	_ "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/screen_option_dependency"
	upipb "github.com/epifi/gamma/api/upi"
	user "github.com/epifi/gamma/api/user"
	contact "github.com/epifi/gamma/api/user/contact"
	userloc "github.com/epifi/gamma/api/user/location"
	obfuscator "github.com/epifi/gamma/api/user/obfuscator"
	beonbpb "github.com/epifi/gamma/api/user/onboarding"
	userintel "github.com/epifi/gamma/api/userintel"
	vgcrmpb "github.com/epifi/gamma/api/vendorgateway/crm"
	namecheck "github.com/epifi/gamma/api/vendorgateway/namecheck"
	vgriskpb "github.com/epifi/gamma/api/vendorgateway/risk"
	vgtxnmonitoringpb "github.com/epifi/gamma/api/vendorgateway/transactionmonitoring/dronapay"
	vmpb "github.com/epifi/gamma/api/vendormapping"
	hook "github.com/epifi/gamma/cmd/servers/staging/userrisk/hook"
	explorer "github.com/epifi/gamma/pkg/cfg/explorer"
	questinterceptor "github.com/epifi/gamma/pkg/epifigrpc/interceptors/quest"
	fepkg "github.com/epifi/gamma/pkg/frontend/msg"
	riskconf "github.com/epifi/gamma/risk/config"
	genconf2 "github.com/epifi/gamma/risk/config/genconf"
	wire "github.com/epifi/gamma/risk/wire"
)

// nolint: funlen
func Run(ctx context.Context, initNotifier chan<- cfg.ServerName) error {
	// Panic handler for logging panics in standard format
	epifiserver.HandlePanic()
	resolver.RegisterBuilder(resolver.ConsulSchemeName)
	var configNameToConfMap = make(commonexplorer.CfgExplorerMap)

	err := initLogger()
	if err != nil {
		// cannot log error since logger maynot be initialized yet.
		return err
	}
	ctx = epificontext.WithTraceId(ctx, metadata.MD{})
	// Load static configuration
	conf, err := config.Load(cfg.USER_RISK_SERVER)
	if err != nil {
		logger.Error(ctx, "failed to load static conf", zap.Error(err))
		return err
	}
	_ = conf
	// Load generated dynamic configuration
	gconf, err := genconf.Load(cfg.USER_RISK_SERVER)
	if err != nil {
		logger.Error(ctx, "failed to load dynamic conf", zap.Error(err))
		return err
	}
	logger.Info(ctx, "initiating server")

	if gconf.Profiling().AutomaticProfiling().EnableAutoProfiling() {
		profilingErr := profiling.AutomaticProfiling(context.Background(), cfg.USER_RISK_SERVER, gconf.Profiling(), gconf.Environment(), gconf.AWS().Region)
		if profilingErr != nil {
			logger.Error(ctx, "failed to start automatic profiling", zap.Error(profilingErr))
			return profilingErr
		}
	}

	if gconf.Tracing().Enable {
		_, shutdown, tracingErr := otel.ConfigureOpenTelemetryV2(context.Background(), cfg.USER_RISK_SERVER, gconf.Environment(), gconf.Profiling().PyroscopeProfiling().EnablePyroscope())
		if tracingErr != nil {
			logger.Error(ctx, "failed to configure open telemetry", zap.Error(tracingErr))
			return err
		}
		defer shutdown()
	}

	err = profiling.PyroscopeProfilingV2(gconf.Name(), gconf.Profiling(), gconf.Environment())
	if err != nil {
		logger.Error(ctx, "error starting pyroscope profiler", zap.Error(err))
	}

	if !cfg.IsLocalEnv(gconf.Environment()) && !cfg.IsRemoteDebugEnabled() && !cfg.IsTestTenantEnabled() && !cfg.IsTeamSpaceTenant() {
		initDisc, _ := consul.GetBool(consul.GetInitDiscoveryKey(gconf.Environment(), string(gconf.Name())))
		queue.SetWorkerInitialization(initDisc)
	}

	rateLimiterRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()[gconf.RedisRateLimiterName()], gconf.Tracing().Enable)
	defer func() { _ = rateLimiterRedisStore.Close() }()

	var (
		crdbResourceMap    *storage2.DBResourceProvider[*gorm.DB]
		crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor]
	)

	epifiCRDB, err := storage2.NewCRDBWithConfig(gconf.Databases()["EpifiCRDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "EpifiCRDB"))
		return err
	}
	epifiCRDBSqlDb, err := epifiCRDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "EpifiCRDB"))
		return err
	}
	defer func() { _ = epifiCRDBSqlDb.Close() }()
	fRMCRDB, err := storage2.NewCRDBWithConfig(gconf.Databases()["FRMCRDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "FRMCRDB"))
		return err
	}
	fRMCRDBSqlDb, err := fRMCRDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "FRMCRDB"))
		return err
	}
	defer func() { _ = fRMCRDBSqlDb.Close() }()

	fRMPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["FRMPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "FRMPGDB"))
		return err
	}
	fRMPGDBSqlDb, err := fRMPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "FRMPGDB"))
		return err
	}
	defer func() { _ = fRMPGDBSqlDb.Close() }()

	userriskConn := epifigrpc.NewServerConn(cfg.USER_RISK_SERVER, cfg.USER_RISK_SERVER)
	defer epifigrpc.CloseConn(userriskConn)
	dronapayCallbackClient := txnmonitoringdronapaypb.NewDronapayCallbackClient(userriskConn)

	collapserRueidisRedisStore, err := storage2.NewRueidisClientFromConfig(gconf.RueidisRedisClients()["CollapserRueidisRedisStore"])
	if err != nil {
		logger.Error(ctx, "failed to establish rueidis redis client conn for CollapserRueidisRedisStore", zap.Error(err))
		return err
	}
	defer func() { collapserRueidisRedisStore.Close() }()
	collapserRueidisCacheStorage := cache.NewRueidisCacheStorageWithHystrix(cache.NewRueidisCacheWithOptions(collapserRueidisRedisStore), gconf.RueidisRedisClients()["CollapserRueidisRedisStore"].Hystrix)
	onboardingConn := epifigrpc.NewServerConn(cfg.USER_RISK_SERVER, cfg.ONBOARDING_SERVER)
	defer epifigrpc.CloseConn(onboardingConn)
	usersClient := user.NewUsersClient(onboardingConn)
	payConn := epifigrpc.NewServerConn(cfg.USER_RISK_SERVER, cfg.PAY_SERVER)
	defer epifigrpc.CloseConn(payConn)
	actorClient := actor.NewActorClient(payConn)
	locationClient := userloc.NewLocationClient(onboardingConn)
	locationClientVar2 := location.NewLocationClient(onboardingConn)
	authClient := auth.NewAuthClient(onboardingConn)
	vendorgatewayConn := epifigrpc.NewServerConn(cfg.USER_RISK_SERVER, cfg.VENDOR_GATEWAY_SERVER)
	defer epifigrpc.CloseConn(vendorgatewayConn)
	riskClient := vgriskpb.NewRiskClient(vendorgatewayConn)
	inAppReferralClient := inappreferralpb.NewInAppReferralClient(onboardingConn)
	livenessClient := livpb.NewLivenessClient(onboardingConn)
	kycClient := kycpb.NewKycClient(onboardingConn)
	centralgrowthConn := epifigrpc.NewServerConn(cfg.USER_RISK_SERVER, cfg.CENTRAL_GROWTH_SERVER)
	defer epifigrpc.CloseConn(centralgrowthConn)
	savingsClient := savingspb.NewSavingsClient(centralgrowthConn)
	nebulaConn := epifigrpc.NewServerConn(cfg.USER_RISK_SERVER, cfg.NEBULA_SERVER)
	defer epifigrpc.CloseConn(nebulaConn)
	celestialClient := celestialpb.NewCelestialClient(nebulaConn)
	redListClient := redlistpb.NewRedListClient(userriskConn)
	uNNameCheckClient := namecheck.NewUNNameCheckClient(vendorgatewayConn)
	employmentClient := employment.NewEmploymentClient(onboardingConn)
	userIntelServiceClient := userintel.NewUserIntelServiceClient(onboardingConn)
	lendingConn := epifigrpc.NewServerConn(cfg.USER_RISK_SERVER, cfg.LENDING_SERVER)
	defer epifigrpc.CloseConn(lendingConn)
	creditReportManagerClient := creditreportv2pb.NewCreditReportManagerClient(lendingConn)
	growthinfraConn := epifigrpc.NewServerConn(cfg.USER_RISK_SERVER, cfg.GROWTH_INFRA_SERVER)
	defer epifigrpc.CloseConn(growthinfraConn)
	commsClient := commspb.NewCommsClient(growthinfraConn)
	derivedAttributesManagerClient := derivedattributes.NewDerivedAttributesManagerClient(lendingConn)
	bankCustomerServiceClient := bankcust.NewBankCustomerServiceClient(onboardingConn)
	onboardingClient := beonbpb.NewOnboardingClient(onboardingConn)
	uPIClient := upipb.NewUPIClient(payConn)
	operationalStatusServiceClient := operstatuspb.NewOperationalStatusServiceClient(payConn)
	screenerClient := screenerpb.NewScreenerClient(onboardingConn)
	tieringClient := tieringpb.NewTieringClient(centralgrowthConn)
	caseManagementClient := casemanagementpb.NewCaseManagementClient(userriskConn)
	contactClient := contact.NewContactClient(onboardingConn)
	userRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["UserRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = userRedisStore.Close() }()
	preApprovedLoanClient := preapprovedloanpb.NewPreApprovedLoanClient(lendingConn)
	obfuscatorClient := obfuscator.NewObfuscatorClient(onboardingConn)
	payClient := paypb.NewPayClient(payConn)
	riskClientVar2 := riskpb.NewRiskClient(userriskConn)
	transactionRiskDronaPayClient := vgtxnmonitoringpb.NewTransactionRiskDronaPayClient(vendorgatewayConn)
	txnRiskScoreServiceClient := riskpb.NewTxnRiskScoreServiceClient(userriskConn)
	piClient := pipb.NewPiClient(payConn)
	merchantServiceClient := merchant.NewMerchantServiceClient(payConn)
	wealthdmfConn := epifigrpc.NewServerConn(cfg.USER_RISK_SERVER, cfg.WEALTH_DMF_SERVER)
	defer epifigrpc.CloseConn(wealthdmfConn)
	txnCategorizerClient := categorizer.NewTxnCategorizerClient(wealthdmfConn)
	cxConn := epifigrpc.NewServerConn(cfg.USER_RISK_SERVER, cfg.CX_SERVER)
	defer epifigrpc.CloseConn(cxConn)
	ticketClient := cxticketpb.NewTicketClient(cxConn)
	paymentClient := paymentpb.NewPaymentClient(payConn)
	profileClient := profilepb.NewProfileClient(userriskConn)
	orderServiceClient := order.NewOrderServiceClient(payConn)
	cRMClient := vgcrmpb.NewCRMClient(vendorgatewayConn)
	vendormappingConn := epifigrpc.NewServerConn(cfg.USER_RISK_SERVER, cfg.VENDORMAPPING_SERVER)
	defer epifigrpc.CloseConn(vendormappingConn)
	vendorMappingServiceClient := vmpb.NewVendorMappingServiceClient(vendormappingConn)
	segmentationServiceClient := segment.NewSegmentationServiceClient(growthinfraConn)
	productClient := product.NewProductClient(onboardingConn)
	salaryProgramClient := salaryprogrampb.NewSalaryProgramClient(centralgrowthConn)
	var bigqueryConnTeardown func()
	bigqueryDBResourceProvider, bigqueryConnTeardown, err := storagev2analytics.NewBigqueryDBResourceProvider(ctx, gconf.BigqueryClientsMap().GetOwnerToClientConfigMap())
	if err != nil {
		logger.Error(ctx, "failed to get bigquery conn provider", zap.Error(err))
		return err
	}
	defer bigqueryConnTeardown()
	depositClient := depositpb.NewDepositClient(wealthdmfConn)

	awsConf, awsErr := awsconfpkg.NewAWSConfig(ctx, gconf.AWS().Region, gconf.Tracing().Enable)
	if awsErr != nil {
		logger.Error(ctx, "error in loading aws v2 config", zap.Error(awsErr))
		if !cfg.IsRemoteDebugEnabled() {
			return err
		}
	}

	sqsClient := sqs.InitSQSClient(awsConf)
	_ = sqsClient

	var broker *events.RudderStackBroker

	// load rudder-stack broker for sending events
	rudderClient, err := analytics.NewWithConfig(
		gconf.Secrets().Ids[config.RudderWriteKey], "http://"+cfg.GetServerEndPoint(cfg.RUDDER_SERVER).URL(),
		analytics.Config{
			Callback:  events.NewCallback(),
			Interval:  gconf.RudderStack().IntervalInSec * time.Second, // nolint: durationcheck
			BatchSize: gconf.RudderStack().BatchSize,
			Verbose:   gconf.RudderStack().Verbose,
		})
	if err != nil {
		logger.Error(ctx, "error in connecting rudder", zap.Error(err))
		return err
	}
	broker = events.NewRudderStackBroker(rudderClient)
	defer broker.Close()

	rateLimiter := ratelimiter.NewRateLimiterV2(store.NewSlidingWindowLogWithRedis(rateLimiterRedisStore), gconf.GrpcRatelimiterParams().RateLimitConfig())
	_ = rateLimiter

	var unaryInterceptors []grpc.UnaryServerInterceptor

	unaryServerInterceptor, err := servergenwire.CollapserInterceptor(collapserRueidisRedisStore, collapserRueidisCacheStorage)
	if err != nil {
		logger.Error(ctx, "failed to init grpc unary interceptor", zap.Error(err))
		return err
	}
	if unaryServerInterceptor != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptor)
	}

	var streamInterceptors []grpc.StreamServerInterceptor

	histogramBuckets := prometheus.DefBuckets
	if len(gconf.LatencyHistogramBuckets()) > 0 {
		histogramBuckets = epifigrpc.AddGrpcLatencyBuckets(gconf.LatencyHistogramBuckets()...)
	}

	serverOptions := []grpc.ServerOption{}
	if gconf.GrpcServerConfig().MaxRecvMsgSize() != nil && gconf.GrpcServerConfig().MaxRecvMsgSize().Size != 0 {
		serverOptions = append(serverOptions, grpc.MaxRecvMsgSize(gconf.GrpcServerConfig().MaxRecvMsgSize().Size))
	}
	var extractFeRespStatusWithErrMsgFunc commoninterceptors.ExtractFeRespStatusWithErrMsgFunc
	extractFeRespStatusWithErrMsgFunc = fepkg.ExtractFeRespStatusWithErrMsgFunc

	s := epifigrpc.NewSecureServerWithInterceptors(gconf.GrpcServerConfig(), gconf.Flags().TrimDebugMessageFromStatus, string(gconf.Name()), histogramBuckets, unaryInterceptors, streamInterceptors, extractFeRespStatusWithErrMsgFunc, serverOptions...)

	if cfg.IsNonProdEnv(gconf.Environment()) {
		reflection.Register(s)
	}

	httpMux := http.NewServeMux()
	_ = httpMux

	var sqsSubscribers []queue.Subscriber
	var subs []queue.Subscriber
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSubs []*sqs.ExtendedSqsSubscriber

	subs, extSubs, err = setupRisk(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, epifiCRDB, fRMCRDB, fRMPGDB, usersClient, actorClient, locationClient, locationClientVar2, authClient, riskClient, inAppReferralClient, livenessClient, kycClient, savingsClient, celestialClient, redListClient, uNNameCheckClient, employmentClient, userIntelServiceClient, creditReportManagerClient, commsClient, derivedAttributesManagerClient, bankCustomerServiceClient, onboardingClient, uPIClient, operationalStatusServiceClient, screenerClient, tieringClient, caseManagementClient, contactClient, userRedisStore, preApprovedLoanClient, obfuscatorClient, payClient, riskClientVar2, transactionRiskDronaPayClient, txnRiskScoreServiceClient, piClient, merchantServiceClient, txnCategorizerClient, ticketClient, paymentClient, profileClient, orderServiceClient, cRMClient, vendorMappingServiceClient, segmentationServiceClient, productClient, salaryProgramClient, bigqueryDBResourceProvider, depositClient)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	explorerpb.RegisterConfigExplorerServer(s, explorer.NewServer(configNameToConfMap))

	cleanupFn, err := hook.InitRiskHttpServer(gconf, httpMux, dronapayCallbackClient) // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "InitRiskHttpServer"), zap.Error(err))
		return err
	}
	defer cleanupFn()

	cleanupFnVar2, err := hook.InitCRDBTxn(epifiCRDB) // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "InitCRDBTxn"), zap.Error(err))
		return err
	}
	defer cleanupFnVar2()

	cleanupFnVar3, err := servergenwire.LoadCollapserWithGrpcServer(gconf, s) // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "LoadCollapserWithGrpcServer"), zap.Error(err))
		return err
	}
	defer cleanupFnVar3()

	// Load rpc level log sampling rate descriptors.
	epifigrpc.GetRPCLogSamplingRatesMapInstance().Load(s)

	initNotifier <- gconf.Name()

	// TODO(Sundeep): Remove this additional logic for starting the servers inside go-routine, by exposing functions
	// from epifiserver package that take errgroup.Group as parameter.
	grp, _ := errgroup.WithContext(context.Background())

	grp.Go(func() error {
		epifiserver.StartSecureServer(s, gconf.ServerPorts(), string(gconf.Name()))
		return nil
	})

	// Wait for server health before starting SQS workers
	if queue.IsWorkerInitializationEnabled() {
		goroutine.RunWithCtx(ctx, func(ctx context.Context) {
			if epifiserver.IsServerReady(gconf.ServerPorts().HttpPort) {
				logger.Info(ctx, "Server is healthy, starting SQS workers")
				sqs.StartSQSWorkers(ctx, sqsSubscribers, extendedSqsSubscribers)
			} else {
				logger.PanicWithCtx(ctx, "Server health check timed out, not starting SQS workers")
			}
		})
	}
	if err := grp.Wait(); err != nil {
		return err
	}

	return nil
}

// nolint: funlen
func setupRisk(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	epifiCRDB types.EpifiCRDB,
	fRMCRDB types.FRMCRDB,
	fRMPGDB types.FRMPGDB,
	usersClient user.UsersClient,
	actorClient actor.ActorClient,
	locationClient userloc.LocationClient,
	locationClientVar2 location.LocationClient,
	authClient auth.AuthClient,
	riskClient vgriskpb.RiskClient,
	inAppReferralClient inappreferralpb.InAppReferralClient,
	livenessClient livpb.LivenessClient,
	kycClient kycpb.KycClient,
	savingsClient savingspb.SavingsClient,
	celestialClient celestialpb.CelestialClient,
	redListClient redlistpb.RedListClient,
	uNNameCheckClient namecheck.UNNameCheckClient,
	employmentClient employment.EmploymentClient,
	userIntelServiceClient userintel.UserIntelServiceClient,
	creditReportManagerClient creditreportv2pb.CreditReportManagerClient,
	commsClient commspb.CommsClient,
	derivedAttributesManagerClient derivedattributes.DerivedAttributesManagerClient,
	bankCustomerServiceClient bankcust.BankCustomerServiceClient,
	onboardingClient beonbpb.OnboardingClient,
	uPIClient upipb.UPIClient,
	operationalStatusServiceClient operstatuspb.OperationalStatusServiceClient,
	screenerClient screenerpb.ScreenerClient,
	tieringClient tieringpb.TieringClient,
	caseManagementClient casemanagementpb.CaseManagementClient,
	contactClient contact.ContactClient,
	userRedisStore types.UserRedisStore,
	preApprovedLoanClient preapprovedloanpb.PreApprovedLoanClient,
	obfuscatorClient obfuscator.ObfuscatorClient,
	payClient paypb.PayClient,
	riskClientVar2 riskpb.RiskClient,
	transactionRiskDronaPayClient vgtxnmonitoringpb.TransactionRiskDronaPayClient,
	txnRiskScoreServiceClient riskpb.TxnRiskScoreServiceClient,
	piClient pipb.PiClient,
	merchantServiceClient merchant.MerchantServiceClient,
	txnCategorizerClient categorizer.TxnCategorizerClient,
	ticketClient cxticketpb.TicketClient,
	paymentClient paymentpb.PaymentClient,
	profileClient profilepb.ProfileClient,
	orderServiceClient order.OrderServiceClient,
	cRMClient vgcrmpb.CRMClient,
	vendorMappingServiceClient vmpb.VendorMappingServiceClient,
	segmentationServiceClient segment.SegmentationServiceClient,
	productClient product.ProductClient,
	salaryProgramClient salaryprogrampb.SalaryProgramClient,
	bigqueryDBResourceProvider storagev2analytics.BigqueryDBResourceProvider,
	depositClient depositpb.DepositClient) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	riskConf, err := riskconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.RISK_SERVICE))
		return nil, nil, err
	}
	_ = riskConf

	riskGenConf, err := dynconf.LoadConfig(riskconf.Load, genconf2.NewConfig, cfg.RISK_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.RISK_SERVICE))
		return nil, nil, err
	}

	_ = riskGenConf

	leaActorsPublisher, err := sqs.NewPublisherWithConfig(ctx, riskGenConf.LeaActorsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	alertsPublisher, err := sqs.NewPublisherWithConfig(ctx, riskGenConf.AlertsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	formSubmissionEventPublisher, err := sqs.NewPublisherWithConfig(ctx, riskGenConf.FormSubmissionEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	dronapayRuleHitCallbackPublisher, err := sqs.NewPublisherWithConfig(ctx, riskGenConf.DronapayRuleHitCallbackPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}

	batchRuleEngineS3Client := s3pkg.NewClient(awsConf, riskGenConf.BatchRuleEngineS3().BucketName)
	batchRuleEngineS3ClientVar2 := s3pkg.NewClient(awsConf, riskGenConf.BatchRuleEngineS3().BucketName)
	batchRuleEngineS3ClientVar3 := s3pkg.NewClient(awsConf, riskGenConf.BatchRuleEngineS3().BucketName)

	service := wire.InitialiseRiskService(epifiCRDB, fRMCRDB, fRMPGDB, usersClient, actorClient, locationClient, locationClientVar2, authClient, riskClient, inAppReferralClient, riskConf, livenessClient, kycClient, savingsClient, celestialClient, redListClient, uNNameCheckClient, employmentClient, userIntelServiceClient, riskGenConf, creditReportManagerClient, commsClient, derivedAttributesManagerClient, bankCustomerServiceClient, onboardingClient, leaActorsPublisher, uPIClient, operationalStatusServiceClient, screenerClient, tieringClient, caseManagementClient, contactClient, userRedisStore, preApprovedLoanClient, obfuscatorClient)

	riskpb.RegisterRiskServer(s, service)

	consumer := wire.InitialiseRiskConsumer(epifiCRDB, fRMPGDB, awsConf, payClient, riskClientVar2, savingsClient, transactionRiskDronaPayClient, txnRiskScoreServiceClient, riskConf, riskGenConf)

	riskpb.RegisterRiskConsumerServer(s, consumer)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, riskGenConf.ProcessRedListUpdateSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		riskpb.RegisterProcessRedListUpdateMethodToSubscriber(subscriber, consumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, riskGenConf.ProcessDisputeUploadSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		riskpb.RegisterProcessDisputeUploadMethodToSubscriber(subscriber, consumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	serviceVar2 := wire.InitialiseWhiteListService(fRMPGDB)

	whitelistpb.RegisterWhiteListServer(s, serviceVar2)

	serviceVar3 := wire.InitialiseRedListService(epifiCRDB, fRMPGDB, payClient, riskClientVar2, savingsClient, transactionRiskDronaPayClient, txnRiskScoreServiceClient, riskConf, riskGenConf)

	redlistpb.RegisterRedListServer(s, serviceVar3)

	riskDevService := wire.InitializeDevService(epifiCRDB, fRMCRDB, fRMPGDB)

	riskdeveloper.RegisterDeveloperServer(s, riskDevService)

	txnScoreService := wire.InitialiseTxnRiskScoreService(epifiCRDB, fRMPGDB, riskGenConf)

	riskpb.RegisterTxnRiskScoreServiceServer(s, txnScoreService)

	txnMonitoringService := wire.InitialiseTxnMonitoringService(epifiCRDB, fRMCRDB, fRMPGDB, locationClientVar2, riskConf, piClient, actorClient, onboardingClient, kycClient, livenessClient, transactionRiskDronaPayClient, usersClient, txnRiskScoreServiceClient, employmentClient, inAppReferralClient, merchantServiceClient, riskGenConf, contactClient, savingsClient, caseManagementClient, bankCustomerServiceClient, txnCategorizerClient, uNNameCheckClient)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, riskGenConf.ProcessSyncLeaActorsSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		transactionmonitoring.RegisterSyncLeaActorsMethodToSubscriber(subscriber, txnMonitoringService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	txnMonitoringCallbackService := wire.InitialiseTxnMonitoringCallbackService(caseManagementClient, riskGenConf)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, riskGenConf.RuleHitCallbackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		transactionmonitoring.RegisterProcessRuleHitCallBackMethodToSubscriber(subscriber, txnMonitoringCallbackService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	caseManagementConsumerService := wire.InitialiseCaseManagementConsumer(epifiCRDB, livenessClient, caseManagementClient, riskConf, riskGenConf, commsClient, actorClient, fRMPGDB, fRMCRDB, userRedisStore, alertsPublisher, batchRuleEngineS3Client, celestialClient, savingsClient, usersClient, ticketClient, paymentClient)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, riskGenConf.ProcessRiskCasesIngestEventBEQueueSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		casemanagementpb.RegisterAddCasesMethodToSubscriber(subscriber, caseManagementConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, riskGenConf.ProcessCallRoutingEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		casemanagementpb.RegisterProcessCallRoutingEventMethodToSubscriber(subscriber, caseManagementConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, riskGenConf.ProcessFormSubmissionEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		casemanagementpb.RegisterProcessFormSubmissionMethodToSubscriber(subscriber, caseManagementConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, riskGenConf.RiskCXTicketUpdateEventQueueSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		casemanagementpb.RegisterProcessCXTicketUpdateEventMethodToSubscriber(subscriber, caseManagementConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	caseManagementConsumerServiceVar2 := wire.InitialiseCaseManagementConsumer(epifiCRDB, livenessClient, caseManagementClient, riskConf, riskGenConf, commsClient, actorClient, fRMPGDB, fRMCRDB, userRedisStore, alertsPublisher, batchRuleEngineS3ClientVar2, celestialClient, savingsClient, usersClient, ticketClient, paymentClient)

	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, riskGenConf.RiskBatchRuleEngineEventQueueSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			casemanagementpb.RegisterProcessBatchRuleEngineEventMethodToSubscriber(subscriber, caseManagementConsumerServiceVar2)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return nil, nil, err
		}
		if shouldStartWorker {
			sqsSubscribers = append(sqsSubscribers, subs)
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, riskGenConf.ProcessRiskSignalEventQueueSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			casemanagementpb.RegisterProcessRiskSignalEventMethodToSubscriber(subscriber, caseManagementConsumerServiceVar2)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return nil, nil, err
		}
		if shouldStartWorker {
			sqsSubscribers = append(sqsSubscribers, subs)
		}
	}

	caseManagementConsumerServiceVar3 := wire.InitialiseCaseManagementConsumer(epifiCRDB, livenessClient, caseManagementClient, riskConf, riskGenConf, commsClient, actorClient, fRMPGDB, fRMCRDB, userRedisStore, alertsPublisher, batchRuleEngineS3ClientVar3, celestialClient, savingsClient, usersClient, ticketClient, paymentClient)

	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, riskGenConf.ProcessRiskCasesIngestEventDataQueueSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			casemanagementpb.RegisterAddCasesMethodToSubscriber(subscriber, caseManagementConsumerServiceVar3)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return nil, nil, err
		}
		if shouldStartWorker {
			sqsSubscribers = append(sqsSubscribers, subs)
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, riskGenConf.ProcessRiskAlertIngestEventDSQueueSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			casemanagementpb.RegisterAddAlertsMethodToSubscriber(subscriber, caseManagementConsumerServiceVar3)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return nil, nil, err
		}
		if shouldStartWorker {
			sqsSubscribers = append(sqsSubscribers, subs)
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, riskGenConf.RiskAlertIngestionQueueSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			casemanagementpb.RegisterAddAlertsMethodToSubscriber(subscriber, caseManagementConsumerServiceVar3)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return nil, nil, err
		}
		if shouldStartWorker {
			sqsSubscribers = append(sqsSubscribers, subs)
		}
	}

	serviceVar4 := wire.InitialiseProfileService(fRMCRDB, epifiCRDB, riskGenConf, savingsClient, riskConf, operationalStatusServiceClient, contactClient, inAppReferralClient, usersClient, actorClient, celestialClient, fRMPGDB, ticketClient)

	profilepb.RegisterProfileServer(s, serviceVar4)

	profileConsumerService := wire.InitialiseProfileConsumer(fRMPGDB, epifiCRDB, profileClient, savingsClient, riskClientVar2)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, riskGenConf.RiskAccountOperationStatusUpdateQueueSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		profilepb.RegisterProcessAccountOperationStatusUpdateEventMethodToSubscriber(subscriber, profileConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	serviceVar5 := wire.InitialiseCaseManagementService(orderServiceClient, piClient, merchantServiceClient, cRMClient, vendorMappingServiceClient, fRMCRDB, celestialClient, segmentationServiceClient, riskGenConf, userRedisStore, usersClient, productClient, formSubmissionEventPublisher, fRMPGDB, salaryProgramClient, employmentClient, ticketClient, bigqueryDBResourceProvider)

	casemanagementpb.RegisterCaseManagementServer(s, serviceVar5)

	serviceVar6 := wire.InitializeDronapayRuleHitCallbackService(dronapayRuleHitCallbackPublisher)

	txnmonitoringdronapaypb.RegisterDronapayCallbackServer(s, serviceVar6)

	serviceVar7 := wire.InitializeLeaService(fRMPGDB, usersClient, savingsClient, bankCustomerServiceClient, depositClient, commsClient, actorClient, riskGenConf, operationalStatusServiceClient, riskConf, celestialClient)

	leapb.RegisterLeaServer(s, serviceVar7)

	consumerVar2 := wire.InitialiseMnrlConsumer(fRMPGDB, riskConf)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, riskGenConf.ProcessMnrlReportSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		mnrl.RegisterAddMnrlReportMethodToSubscriber(subscriber, consumerVar2)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, riskGenConf.ProcessMnrlSuspectedFlaggedMobileSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		mnrl.RegisterAddMnrlSuspectedFlaggedMobileMethodToSubscriber(subscriber, consumerVar2)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}
	configNameToConfMap[cfg.ConfigName(cfg.RISK_SERVICE)] = &commonexplorer.Config{StaticConf: &riskconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

func initLogger() error {
	// Load configuration
	conf, err := config.Load(cfg.USER_RISK_SERVER)
	if err != nil {
		// cannot log error since logger is not initialized yet.
		return err
	}

	logger.InitV2(conf.Environment, conf.Logging)
	if conf.SecureLogging != nil {
		logger.InitSecureLoggerV2(conf.SecureLogging)
	}
	return nil
}

func getEnvNamespaceClient(ns temporalpkg.Namespace, env string) string {
	if cfg.IsLocalEnv(env) {
		return strings.ToLower(string(ns))
	}

	return fmt.Sprintf("%s-%s", env, strings.ToLower(string(ns)))
}

func getSQSConsumerInterceptors() []grpc.UnaryServerInterceptor {
	return []grpc.UnaryServerInterceptor{questinterceptor.UserContextInterceptor}
}
