package risk

import (
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cmd/config"
	"github.com/epifi/be-common/tools/servergen/meta"

	caseManagementPb "github.com/epifi/gamma/api/risk/case_management"
	riskdeveloper "github.com/epifi/gamma/api/risk/developer"
	"github.com/epifi/gamma/api/risk/mnrl"
	profilePb "github.com/epifi/gamma/api/risk/profile"
	"github.com/epifi/gamma/api/risk/redlist"
	txnMonitoringDronapayPb "github.com/epifi/gamma/api/risk/transaction_monitoring/dronapay"
	whitelistpb "github.com/epifi/gamma/api/risk/whitelist"

	riskpb "github.com/epifi/gamma/api/risk"
	leaPb "github.com/epifi/gamma/api/risk/lea"

	transactionmonitoring "github.com/epifi/gamma/api/risk/transaction_monitoring"

	riskwire "github.com/epifi/gamma/risk/wire"
)

var serviceWireInitializers = []*meta.ServiceInitializer{
	{
		WireInitializer:     riskwire.InitialiseRiskService,
		GRPCRegisterMethods: []any{riskpb.RegisterRiskServer},
	},
	{
		WireInitializer:     riskwire.InitialiseRiskConsumer,
		GRPCRegisterMethods: []any{riskpb.RegisterRiskConsumerServer},
		ConsumerMethods: []*meta.ConsumerMethod{
			{
				MethodName:  riskpb.ProcessRedListUpdateMethod,
				ConfigField: "ProcessRedListUpdateSqsSubscriber",
			},
			{
				MethodName:  riskpb.ProcessDisputeUploadMethod,
				ConfigField: "ProcessDisputeUploadSqsSubscriber",
			},
		},
	},
	{
		WireInitializer:     riskwire.InitialiseWhiteListService,
		GRPCRegisterMethods: []any{whitelistpb.RegisterWhiteListServer},
	},
	{
		WireInitializer:     riskwire.InitialiseRedListService,
		GRPCRegisterMethods: []any{redlist.RegisterRedListServer},
	},
	{
		WireInitializer:     riskwire.InitializeDevService,
		GRPCRegisterMethods: []any{riskdeveloper.RegisterDeveloperServer},
	},
	{
		WireInitializer:     riskwire.InitialiseTxnRiskScoreService,
		GRPCRegisterMethods: []any{riskpb.RegisterTxnRiskScoreServiceServer},
	},
	{
		WireInitializer: riskwire.InitialiseTxnMonitoringService,
		ConsumerMethods: []*meta.ConsumerMethod{
			{
				MethodName:  transactionmonitoring.SyncLeaActorsMethod,
				ConfigField: "ProcessSyncLeaActorsSqsSubscriber",
			},
		},
	},
	{
		WireInitializer: riskwire.InitialiseTxnMonitoringCallbackService,
		ConsumerMethods: []*meta.ConsumerMethod{
			{
				MethodName:  transactionmonitoring.ProcessRuleHitCallBackMethod,
				ConfigField: "RuleHitCallbackSubscriber",
			},
		},
	},
	{
		WireInitializer: riskwire.InitialiseCaseManagementConsumer,
		ConsumerMethods: []*meta.ConsumerMethod{
			{
				MethodName:  caseManagementPb.AddCasesMethod,
				ConfigField: "ProcessRiskCasesIngestEventBEQueueSubscriber",
			},
			{
				MethodName:  caseManagementPb.ProcessCallRoutingEventMethod,
				ConfigField: "ProcessCallRoutingEventSqsSubscriber",
			},
			{
				MethodName:  caseManagementPb.ProcessFormSubmissionMethod,
				ConfigField: "ProcessFormSubmissionEventSqsSubscriber",
			},
			{
				MethodName:  caseManagementPb.ProcessCXTicketUpdateEventMethod,
				ConfigField: "RiskCXTicketUpdateEventQueueSubscriber",
			},
		},
	},
	{
		WireInitializer: riskwire.InitialiseCaseManagementConsumer,
		ConsumerMethods: []*meta.ConsumerMethod{
			{
				MethodName:  caseManagementPb.ProcessBatchRuleEngineEventMethod,
				ConfigField: "RiskBatchRuleEngineEventQueueSubscriber",
			},
			{
				MethodName:  caseManagementPb.ProcessRiskSignalEventMethod,
				ConfigField: "ProcessRiskSignalEventQueueSubscriber",
			},
		},
		InitCondition: func(conf *config.Config) bool {
			return !cfg.IsTestTenantEnabled()
		},
	},
	{
		WireInitializer: riskwire.InitialiseCaseManagementConsumer,
		ConsumerMethods: []*meta.ConsumerMethod{
			{
				MethodName:  caseManagementPb.AddCasesMethod,
				ConfigField: "ProcessRiskCasesIngestEventDataQueueSubscriber",
			},
			{
				MethodName:  caseManagementPb.AddAlertsMethod,
				ConfigField: "ProcessRiskAlertIngestEventDSQueueSubscriber",
			},
			{
				MethodName:  caseManagementPb.AddAlertsMethod,
				ConfigField: "RiskAlertIngestionQueueSubscriber",
			},
		},
		InitCondition: func(conf *config.Config) bool {
			return !cfg.IsTestTenantEnabled()
		},
	},
	{
		WireInitializer:     riskwire.InitialiseProfileService,
		GRPCRegisterMethods: []any{profilePb.RegisterProfileServer},
	},
	{
		WireInitializer: riskwire.InitialiseProfileConsumer,
		ConsumerMethods: []*meta.ConsumerMethod{
			{
				MethodName:  profilePb.ProcessAccountOperationStatusUpdateEventMethod,
				ConfigField: "RiskAccountOperationStatusUpdateQueueSubscriber",
			},
		},
	},
	{
		WireInitializer:     riskwire.InitialiseCaseManagementService,
		GRPCRegisterMethods: []any{caseManagementPb.RegisterCaseManagementServer},
	},
	{
		WireInitializer:     riskwire.InitializeDronapayRuleHitCallbackService,
		GRPCRegisterMethods: []any{txnMonitoringDronapayPb.RegisterDronapayCallbackServer},
	},
	{
		WireInitializer:     riskwire.InitializeLeaService,
		GRPCRegisterMethods: []any{leaPb.RegisterLeaServer},
	},
	{
		WireInitializer: riskwire.InitialiseMnrlConsumer,
		ConsumerMethods: []*meta.ConsumerMethod{
			{
				MethodName:  mnrl.AddMnrlReportMethod,
				ConfigField: "ProcessMnrlReportSqsSubscriber",
			},
			{
				MethodName:  mnrl.AddMnrlSuspectedFlaggedMobileMethod,
				ConfigField: "ProcessMnrlSuspectedFlaggedMobileSqsSubscriber",
			},
		},
	},
}
