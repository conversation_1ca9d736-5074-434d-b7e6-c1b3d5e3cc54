---
description: To translate the request and response from a json log into static Go code
globs:
alwaysApply: false
---
### Goal
To intelligently augment an existing integration test with a new RPC call, translating the request and response from a JSON log into static Go code with robust, targeted assertions.

### Step-by-step Instructions

#### 1. Analyze the JSON Log
From the provided JSON log, extract the `rpc` path, the `req` (request) JSON object, and the `resp` (response) JSON object for analysis.

#### 2. Identify Go Types for the RPC
- Take the `rpc` path (e.g., `/frontend.preapprovedloan.PreApprovedLoan/CollectFormData`).
- Perform a `grep` search for this string within the `api/` directory to find the generated `*_grpc.pb.go` file.
- Read this file to find the client interface (e.g., `PreApprovedLoanClient`) and the exact method signature.
- From the signature (e.g., `CollectFormData(ctx context.Context, in *CollectFormDataRequest, ...) (*CollectFormDataResponse, error)`), determine the exact Go types for the request (e.g., `preapprovedloan.CollectFormDataRequest`) and response (e.g., `preapprovedloan.CollectFormDataResponse`). Note the package name (e.g., `preapprovedloan`).

#### 3. Ask the User for the Target Location
- Prompt the user to specify the target test file and function where the new test logic should be inserted.

#### 4. Generate Go Code from JSON
- Your primary task is to convert the `req` JSON object into fully-formed Go struct literals of the types identified in step 2.
- Iterate through the JSON fields. For each field, create the corresponding Go struct field, translating JSON values into their Go equivalents (strings, booleans, nested structs, etc.).
- Pay close attention to nested objects and arrays, creating the corresponding nested struct literals and slices in Go.
- Ensure all necessary protobuf enums are correctly referenced (e.g., `preapprovedloan.LoanProgram_LOAN_PROGRAM_ELIGIBILITY`).
- From the `resp` JSON object, identify a few key fields and their values to use for assertions. Focus on status messages, identifiers, or enum values that confirm the success of the operation.

#### 5. Insert the Generated Go Code into the Test
- In the user-specified test function, insert the newly generated Go code. This includes:
    - The request struct literal.
    - The gRPC client call.
    - A series of assertions using `assert.Equal()` to validate the key fields. 
    - **Use protobuf getter methods** (e.g., `resp.GetRespHeader().GetStatus().GetShortMessage()`) for safe access.

#### 6. Verify and Add Imports
- Ensure the Go package for the service's API (e.g., `github.com/epifi/gamma/api/preapprovedloan`) is imported.
- Add any other necessary imports (`context`, `testing`, `protocmp`, etc.) if they are not already present.

### Example of Generated Code

Given a JSON log, the AI should generate the following Go code block.

```go
	// Define the request based on the JSON log
	req := &preapprovedloan.CollectFormDataRequest{
		LoanHeader: &preapprovedloan.LoanHeader{
			LoanProgram: preapprovedloan.LoanProgram_LOAN_PROGRAM_ELIGIBILITY,
			Vendor:      preapprovedloan.Vendor_EPIFI_TECH,
			EventData: &preapprovedloan.EventData{
				ComponentIdentifier: "LOAN_STEP_EXECUTION_STEP_NAME_EMPLOYMENT",
			},
		},
		LoanRequestId: "PALRJ/tPD3B/TTuQUxI3iGdIkA250724==",
		FormFieldValues: []*preapprovedloan.FormFieldValue{
			{
				FieldId:          "EMPLOYMENT_TYPE",
				SelectedOptionId: "EMPLOYMENT_TYPE_SALARIED",
			},
			{
				FieldId:          "EMPLOYER_NAME",
				SelectedOptionId: "EPIFI TECHNOLOGIES PRIVATE LIMITED",
			},
			{
				FieldId: "MONTHLY_INCOME",
				Value: &preapprovedloan.FormFieldValue_MoneyValue{
					MoneyValue: &money.Money{
						CurrencyCode: "INR",
						Units:        50000,
					},
				},
			},
		},
	}

	// Make the gRPC call
	resp, err := ts.preApprovedLoanClient.CollectFormData(ctx, req)
	assert.Nil(t, err)
	assert.NotNil(t, resp)

	// Assert key fields in the response using getters for safety
	assert.Equal(t, rpcPb.StatusOk().GetCode(), resp.GetRespHeader().GetStatus().GetCode())
    assert.Equal(t, deeplinkPb.Screen_PRE_APPROVED_LOAN_APPLICATION_STATUS_POLL_SCREEN, resp.GetNextAction().GetScreen())
```
