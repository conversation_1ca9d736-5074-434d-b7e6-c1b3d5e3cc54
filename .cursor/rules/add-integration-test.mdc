---
description: To write an integration test using logs
alwaysApply: false
---

This document outlines the rules, patterns, and best practices for writing new integration tests in the `gamma` repository. The goal is to create reliable, maintainable, and efficient tests that accurately validate end-to-end user flows.

## 1. Core Principles

*   **Isolate and Reproduce:** Tests should be self-contained and airtight. They must not depend on the state left by other tests. Each test should handle its own setup and teardown, primarily through user data preparation and dependency injection.
*   **Organize with Test Suites:** For any given feature (e.g., Pre-Approved Loans, Bill Payments), encapsulate the related test logic, clients, and DAOs into a dedicated "Test Suite" struct. This improves code reuse and organization. (e.g., `preapprovedloan.PlTestSuite`).
*   **Use Pooled Resources:** To accelerate test execution, use pre-created users from the test user pool (`GetPooledUser`) whenever the test flow does not require a brand-new user with specific, unique characteristics.
*   **Describe with Metadata:** Every test must have metadata (`TestProperty`) that defines its business unit (`BU`), scope (`Smoke`, `Regression`), and a clear description. This allows for selective test runs and better test management.

## 2. How to Understand a Flow Using Server Logs

Before writing a test, you must understand the sequence of API calls that constitute the user flow. The server logs you are provided with are the primary source of truth for this.

### Step 2.1: Understanding the Log Format

The logs consist of a series of JSON objects. Each object represents a single gRPC call and has the following structure:

```json
{
    "index": 91,
    "timestamp": "2025-07-23T16:58:00+05:30",
    "rpc": "/frontend.preapprovedloan.PreApprovedLoan/GetLandingInfo",
    "req": {
        "loan_header": {
            "event_data": {
                "entry_point": "ENTRY_POINT_EXPLORE_TAB"
            }
        },
        "loan_type": "LOAN_TYPE_PERSONAL"
    },
    "resp": {
        "resp_header": { "status": { "short_message": "Success" } },
        "deeplink": { "screen": "LOANS_OFFER_INTRO_SCREEN", "..." }
    },
    "error": ""
}
```

-   **`rpc`**: This is the full gRPC method path. It tells you which service and method were called.
-   **`req`**: This is the JSON payload sent *to* the server for that RPC call.
-   **`resp`**: This is the JSON payload sent *from* the server in response.
-   **`timestamp`**: Use this to determine the chronological order of the API calls.
-   **`index`**: A sequential identifier for the log entry. Can also be used for ordering.

### Step 2.2: Reconstructing the Flow from Logs

Your task is to analyze the sequence of these JSON objects to build a step-by-step map of the user flow.

1.  **Identify the Starting Point**: Look for the first relevant RPC call in the logs, ordered by `timestamp`. In the example, the flow starts with a user action on the explore tab, which triggers a call to `/frontend.home.Home/GetHomeExplore`.

2.  **Trace the API Calls**: Follow the logs chronologically.
    -   The user clicks on the "Instant Loan" shortcut.
    -   This action, as seen in the logs, triggers a call to `/frontend.preapprovedloan.PreApprovedLoan/GetLandingInfo`.
    -   The `req` shows it was initiated from the explore tab: `"entry_point": "ENTRY_POINT_EXPLORE_TAB"`.

3.  **Connect Request and Response**: Pay close attention to how the response (`resp`) of one API call provides the data needed for the request (`req`) of the next call.
    -   The `GetLandingInfo` response (`resp`) contains multiple loan offers, each with a `loan_offer_id`.
    -   For example: `"loan_offer_id":"PALOoUi02s8YSIqImUS6VEU3eQ250723=="`.
    -   It also contains a `deeplink` or `cta_action` which specifies the next RPC to be called (`RPC_NAME_APPLY_FOR_LOAN` or `RPC_NAME_OFFER_DETAILS`) and the parameters to use, including the `loan_offer_id`.

4.  **Build the Test Blueprint**: This reconstructed sequence of (1) calling an RPC, (2) inspecting its response, and (3) using data from the response to call the next RPC, becomes the blueprint for your integration test logic. You will replicate these calls in your test function using the appropriate gRPC clients.

**Note**: There might be some generic RPC calls for cross cutting concerns like Authentication, Login, Home Load, etc in the logs. You should ignore these if they are not relevant to the flow.

## 3. Step-by-Step Guide to Writing the Test

**Note**: Pause and check with user if they want to add this test in an existing test suite. If yes, then skip steps 3.1 to 3.4 and directly jump to step 3.5

### Step 3.1: Create the Test File
Create a new file in the `testing/integration/` directory, named after the feature, e.g., `feature_xyz_test.go`. The package must be `integration`.

### Step 3.2: Define the Test Case Entrypoint
Every new test file should expose a function that returns a slice of `*TestCase`. This is how the test runner discovers and categorizes the tests.

```go
// in testing/integration/feature_xyz_test.go

func FeatureXYZTestCases() []*TestCase {
    return []*TestCase{
        {
            TestProperty: &TestProperty{
                Description: "This is the test for feature xyz flows",
                BU:          owners.BUSINESS_UNIT_SOME_TEAM,
                Scope:       []Scope{Smoke, Regression},
            },
            Test: FeatureXYZFlows, // This is your main test function
        },
    }
}
```

### Step 3.3: Create a Feature-Specific Test Suite Helper
In the `testing/integration/app/<feature>` directory, create a helper file (e.g., `testing/integration/app/featurexyz/suite.go`). This file will contain your test suite struct and its constructor.

The suite should hold all the gRPC clients and DAOs needed to test the flow. This centralizes dependencies.

```go
// in testing/integration/app/featurexyz/suite.go

package featurexyz

type FeatureXYZTestSuite struct {
    // Add gRPC clients for services identified in logs
    FeXyzClient      xyz.XYZClient
    AuthClient       auth.AuthClient

    // Add DAOs for DB verification if needed
    XyzDAO           xyzdao.DAO
}

func NewFeatureXYZTestSuite(
    feXyzClient xyz.XYZClient,
    authClient auth.AuthClient,
    xyzDAO xyzdao.DAO,
) *FeatureXYZTestSuite {
    return &FeatureXYZTestSuite{ /* ... initialize fields ... */ }
}

// Add methods to the suite that encapsulate parts of the flow
func (ts *FeatureXYZTestSuite) TestHappyFlow(t *testing.T, reqH *header.RequestHeader) {
    // Test logic goes here
}
```

### Step 3.4: Implement the Main Test Function
This function, which was referenced in `FeatureXYZTestCases`, is responsible for setting up the overall test environment and calling the test suite logic.

```go
// in testing/integration/feature_xyz_test.go

func FeatureXYZFlows(t *testing.T) {
    defer Recover(t) // Panic recovery helper
    a := require.New(t)
    ctx := context.Background()

    // 1. Get a user
    pooledUser, release := GetPooledUser(ctx, a)
    defer release()
    reqH := proto.Clone(pooledUser.RequestHeader).(*header.RequestHeader)

    // 2. Initialize DAOs and Clients (dependencies)
    // These dependencies like feXyzClient, authClient, xyzDao are typically
    // initialized in a central test setup file and available as package variables.

    // 3. Create the test suite instance
    xyzTs := featurexyz.NewFeatureXYZTestSuite(feXyzClient, authClient, xyzDao)

    // 4. Run the test using t.Run for clear, structured output
    t.Run("feature xyz happy flow", func(t *testing.T) {
        defer Recover(t)
        xyzTs.TestHappyFlow(t, reqH)
    })
}
```

### Step 3.5: Write the Test Logic
Inside the test suite's methods (`TestHappyFlow` in our example), implement the API call sequence you derived from the logs.

```go
// in testing/integration/app/featurexyz/suite.go

func (ts *FeatureXYZTestSuite) TestHappyFlow(t *testing.T, reqH *header.RequestHeader) {
    // Step 1: Call the first API from your reconstructed flow
    res, err := ts.FeXyzClient.ApiOne(context.Background(), &xyz.ApiOneRequest{
        Header: reqH,
        // ...other request fields
    })
    require.NoError(t, err)
    require.NotNil(t, res)

    // Extract an ID or data needed for the next step
    entityId := res.GetEntityId()

    // Step 2: Call the second API
    res2, err := ts.FeXyzClient.ApiTwo(context.Background(), &xyz.ApiTwoRequest{
        Header: reqH,
        EntityId: entityId, // Use data from the previous response
    })
    require.NoError(t, err)
    require.NotNil(t, res2)
    require.Equal(t, "expected_status", res2.GetStatus())

    // (Optional) Step 3: Verify database state using a DAO
    dbEntity, err := ts.XyzDAO.GetEntity(context.Background(), entityId)
    require.NoError(t, err)
    require.Equal(t, "COMPLETED", dbEntity.State)
}
