// nolint:funlen
package layout

import (
	"context"
	"flag"
	"os"
	"reflect"
	"testing"
	"time"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	cardPb "github.com/epifi/gamma/api/card"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/tiering/external"
	accountsTypes "github.com/epifi/gamma/api/typesv2/account"
	"github.com/epifi/gamma/card/config"
	"github.com/epifi/gamma/card/config/genconf"
	"github.com/epifi/gamma/card/test"
)

var (
	conf        *config.Config
	dynamicConf *genconf.Config
)

func TestLayoutPriorityProvider_GetEligibleLayoutsPriority(t *testing.T) {
	t.Parallel()
	l := NewLayoutPriorityProvider(conf, dynamicConf)

	type args struct {
		ctx      context.Context
		userData *CardUserData
	}
	tests := []struct {
		name    string
		args    args
		want    map[string]float64
		wantErr bool
	}{
		{
			name: "valid layout present #1(layout_id_1)",
			args: args{
				ctx: context.Background(),
				userData: &CardUserData{
					AllCards: []*cardPb.Card{
						{
							Id:        "card-1",
							ActorId:   "actor-1",
							Form:      cardPb.CardForm_DIGITAL,
							CreatedAt: timestampPb.Now(),
						},
					},
					CurrentCard: &cardPb.Card{
						Id:        "card-1",
						ActorId:   "actor-1",
						Form:      cardPb.CardForm_DIGITAL,
						CreatedAt: timestampPb.Now(),
					},
					CurrentTier: external.Tier_TIER_FI_BASIC,
				},
			},
			want: map[string]float64{
				"LAYOUT_ID_1": 0.1,
			},
			wantErr: false,
		},
		{
			name: "valid layout present #2(layout_id_2)",
			args: args{
				ctx: context.Background(),
				userData: &CardUserData{
					AllCards: []*cardPb.Card{
						{
							Id:        "card-1",
							ActorId:   "actor-1",
							Form:      cardPb.CardForm_PHYSICAL,
							CreatedAt: timestampPb.Now(),
						},
					},
					CurrentCard: &cardPb.Card{
						Id:        "card-1",
						ActorId:   "actor-1",
						Form:      cardPb.CardForm_PHYSICAL,
						CreatedAt: timestampPb.Now(),
					},
				},
			},
			want: map[string]float64{
				"LAYOUT_ID_2": 0.1,
			},
			wantErr: false,
		},
		{
			name: "valid layout present (layout_id_2, layout_id_6)",
			args: args{
				ctx: context.Background(),
				userData: &CardUserData{
					AllCards: []*cardPb.Card{
						{
							Id:        "card-1",
							ActorId:   "actor-1",
							Form:      cardPb.CardForm_PHYSICAL,
							CreatedAt: timestampPb.Now(),
						},
					},
					CurrentCard: &cardPb.Card{
						Id:        "card-1",
						ActorId:   "actor-1",
						Form:      cardPb.CardForm_PHYSICAL,
						CreatedAt: timestampPb.Now(),
					},
					SavingsAccount: &savingsPb.Account{
						Id:        "sa_id_1",
						AccountNo: "acc_no_1",
						SkuInfo: &savingsPb.SKUInfo{
							AccountProductOffering: accountsTypes.AccountProductOffering_APO_NRE,
						},
					},
				},
			},
			want: map[string]float64{
				"LAYOUT_ID_2": 0.1,
				"LAYOUT_ID_6": 0.3,
			},
			wantErr: false,
		},
		{
			name: "no valid layout present (layout_id_default)",
			args: args{
				ctx: context.Background(),
				userData: &CardUserData{
					AllCards: []*cardPb.Card{
						{
							Id:        "card-1",
							ActorId:   "actor-1",
							Form:      cardPb.CardForm_CARD_FORM_UNSPECIFIED,
							CreatedAt: timestampPb.New(timestampPb.Now().AsTime().Add(-130 * 24 * time.Hour)),
						},
					},
					CurrentCard: &cardPb.Card{
						Id:        "card-1",
						ActorId:   "actor-1",
						Form:      cardPb.CardForm_CARD_FORM_UNSPECIFIED,
						CreatedAt: timestampPb.Now(),
					},
					CurrentTier: external.Tier_TIER_UNSPECIFIED,
				},
			},
			want: map[string]float64{
				"LAYOUT_ID_DEFAULT": 0.2,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got, err := l.GetEligibleLayoutsPriority(tt.args.ctx, tt.args.userData)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetEligibleLayoutsPriority() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetEligibleLayoutsPriority() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMain(m *testing.M) {
	flag.Parse()

	var teardownV2 func()
	conf, dynamicConf, _, teardownV2 = test.InitTestServerV2()
	exitCode := m.Run()
	teardownV2()
	os.Exit(exitCode)
}
