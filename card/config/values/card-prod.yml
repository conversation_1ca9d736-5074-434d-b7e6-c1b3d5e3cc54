Application:
  Environment: "prod"
  Name: "card"

Server:
  Ports:
    GrpcPort: 8093
    GrpcSecurePort: 9504
    HttpPort: 9999
    HttpPProfPort: 9990

DebitCardPgdb:
  Username: "debit_card_pgdb_dev_user"
  Name: "debit_card_pgdb"
  DbType: "PGDB"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  MaxOpenConn: 10
  MaxIdleConn: 5
  MaxConnTtl: "30m"
  AppName: "card"
  SecretName: "prod/rds/epifimetis/debit_card_pgdb_dev_user"
  GormV2:
    # TODO(abhishekprakash) Mark log level as WARN or DEBUG once this is stable
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

EpifiDb:
  AppName: "card"
  StatementTimeout: 10s
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  DbType: "CRDB"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/cockroach/ca.crt"
  SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 1
  MaxIdleConn: 1
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false
    EnableMultiDBSupport: true
    DBResolverList:
      - TableName: [ ]
        Alias: "debit_card_pgdb_conn"
        DbDsn:
          DbType: "PGDB"
          AppName: "card"
          StatementTimeout: 1m
          Name: "debit_card_pgdb"
          EnableDebug: true
          SSLMode: "verify-full"
          SSLRootCert: "prod/rds/rds-ca-root-2061"
          SecretName: "prod/rds/epifimetis/debit_card_pgdb_dev_user"

# TODO(chandres): rename this at resources level as well -> sftp, consumer, s3buckets-policies, upload path
CardsDispatchedCsvFileSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-dc-cards-sent-for-printing-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CreationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-card-process-creation-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~51 min post that regular interval of 2 hours is followed for next 24 hours.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 3
          MaxAttempts: 10
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 2
          MaxAttempts: 12
          TimeUnit: "Hour"
      MaxAttempts: 22
      CutOff: 10
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "card"

CreationCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-card-creation-callback-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~51 min post that regular interval of 2 hours is followed for next 24 hours.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 3
          MaxAttempts: 10
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 2
          MaxAttempts: 12
          TimeUnit: "Hour"
      MaxAttempts: 22
      CutOff: 10

CreationPublisher:
  QueueName: "prod-card-process-creation-queue"

PiCreationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-card-pi-creation-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~51 min post that regular interval of 2 hours is followed for next 7 days.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 3
          MaxAttempts: 10
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 2
          MaxAttempts: 83
          TimeUnit: "Hour"
      MaxAttempts: 93
      CutOff: 10
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "card"

PiCreationPublisher:
  QueueName: "prod-card-pi-creation-queue"

PinSetEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 2
  QueueName: "prod-card-pin-set-event-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~51 min post that regular interval of 2 hours is followed for next 7 days.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 5
          MaxAttempts: 10
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 2
          MaxAttempts: 83
          TimeUnit: "Hour"
      MaxAttempts: 93
      CutOff: 10
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "card"

PinSetEventPublisher:
  QueueName: "prod-card-pin-set-event-queue"

# TODO(team): cleanup renew card consumer as it not being used now.
RenewCardSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-card-renew-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~51 min post that regular interval of 2 hours is followed for next 24 hours.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 3
          MaxAttempts: 10
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 2
          MaxAttempts: 12
          TimeUnit: "Hour"
      MaxAttempts: 22
      CutOff: 10
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "card"

RenewCardPublisher:
  QueueName: "prod-card-renew-queue"

AWS:
  Region: "ap-south-1"

RudderStack:
  IntervalInSec: 10
  BatchSize: 20
  Verbose: false

Flags:
  TrimDebugMessageFromStatus: false
  EnableSelectedB2BUserCheck: true
  SkipControlValidation: true
  EnableCardSuccessNotification: true
  EnableEcommNotificationFlag: true
  EnableSMSFromEpifi: true
  EnableOnbEmail: false
  EnableCardDeliveryInAppNotif: true
  EnableCardDeliveryDelayNotif: true
  EnableV2QRVerificationLogic: true
  EnableRenewCardRateLimiter: true
  EnableVgPciServerClient: true
  EnablePhysicalCardChargesFlowV2:
    IsEnableOnAndroid: true
    MinAndroidVersion: 315
    IsEnableOnIos: true
    MinIosVersion: 475
  EnablePhysicalCardChargesFlowWithTiering: false
  EnablePhysicalCardChargesFlow:
    MinAndroidVersion: 217
    MinIOSVersion: 328
    FallbackToEnableFeature: false
    DisableFeature: false
  EnableConsolidatedCardControl:
    IsFeatureRestricted: false
    AllowedUserGroups:
      - 11 # FREE_CARD_REPLACEMENT
  EnablePhysicalCardChargingFlowWithTiering:
    MinAndroidVersion: 229
    MinIOSVersion: 331
    FallbackToEnableFeature: false
    DisableFeature: false
  FeatureReleaseConfig:
    FeatureConstraints:
      FEATURE_DC_TRAVEL_MODE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 401
          MinIOSVersion: 550
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
      FEATURE_DISABLE_DC_DELIVERY_ADDRESS_UPDATE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 388
          MinIOSVersion: 542
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
      CARD_SWITCH_NOTIFICATION:
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
      PHYSICAL_DEBIT_CARD_CHARGES_PAYMENT_OPTIONS_SCREEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 351
          MinIOSVersion: 505
      FEATURE_ENRICH_ORDER_AND_TXN_FROM_DC_SWITCH:
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
      FEATURE_DC_CHARGES_API:
        AppVersionConstraintConfig:
          MinAndroidVersion: 440
          MinIOSVersion: 599
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups: [ ]

  EnableRenewCardFlowV2:
    IsEnableOnAndroid: true
    MinAndroidVersion: 324
    IsEnableOnIos: true
    MinIosVersion: 465
  EnableSwitchNotificationDbWrites: true
  ShowUpdatedUiOnPhysicalCardOrderScreen:
    IsEnableOnAndroid: true
    MinAndroidVersion: 350
    IsEnableOnIos: true
    MinIosVersion: 505
  ShowUpdatedUiOnPhysicalCardOrderSuccessScreen:
    IsEnableOnAndroid: true
    MinAndroidVersion: 350
    IsEnableOnIos: true
    MinIosVersion: 505
  EnablePhysicalCardOrderPaymentsViaAllChannels:
    IsEnableOnAndroid: false
    MinAndroidVersion: 100
    IsEnableOnIos: false
    MinIosVersion: 100
  OrderPhysicalCardCriticalNotification: true
  DisableOrderPhysicalCardBenefitsInfo:
    IsEnableOnAndroid: true
    MinAndroidVersion: 382
    IsEnableOnIos: true
    MinIosVersion: 539
  EnableOrderPhysicalCardScreenV2:
    PlatformVersionConfig:
      IsEnableOnAndroid: true
      MinAndroidVersion:  421
      IsEnableOnIos: true
      MinIosVersion: 584
    UserGroupCheckConfig:
      EnableUserGroupCheck: false
      AllowedUserGrp:
        - 1 # INTERNAL
  IsCollectionApiV2FlowEnabled:
    UserGroupCheckConfig:
      IsFeatureRestricted: false
      AllowedUserGroups:
        - 1 # INTERNAL


Secrets:
  Ids:
    RudderWriteKey: "prod/rudder/internal-writekey"
    GoogleCloudProfilingServiceAccountKey: "prod/gcloud/profiling-service-account-key"
    ClientEncryptionKey: "prod/card/card-qr-code-aes-cbc-encryption-key"
    ClientEncryptionInitialisationVector: "prod/card/card-qr-code-aes-cbc-encryption-iv"
    MctClientEncryptionKey: "prod/card/mct-card-qr-code-aes-cbc-encryption-key"
    MctClientEncryptionInitialisationVector: "prod/card/mct-card-qr-code-aes-cbc-encryption-iv"
    SlackOauthToken: "prod/card/slack-oauth-token"

CardOnboardingEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-card-onboarding-stage-update-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 5
        Period: 1s
    Namespace: "card"

CardAuthFactorUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-card-auth-factor-update-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 5
        Period: 1s
    Namespace: "card"

UserDevicePropertiesUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-dc-user-device-properties-update-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 3
      MaxAttempts: 10
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 5
        Period: 1s
    Namespace: "card"

ShipmentRegisterEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-card-shipment-register-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~51 min post that regular interval of 2 hours is followed for next 7 days.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 3
          MaxAttempts: 10
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 2
          MaxAttempts: 83
          TimeUnit: "Hour"
      MaxAttempts: 93
      CutOff: 10
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "card"

ShipmentRegisterEventPublisher:
  QueueName: "prod-card-shipment-register-queue"

TrackingCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-card-tracking-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

DeliveryDelayEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-card-delivery-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "card"

DeliveryDelayEventPublisher:
  QueueName: "prod-card-delivery-notification-queue"

DeliveryDelayNotificationConfig:
  NotificationTime:
    - "144h"
    - "216h"
  DelayTimePostNotif: "48h"
  DelayTime: "6h"

ActivationNotificationConfig:
  NotificationTime:
    - "30m"
    - "24h"
    - "72h"
  DelayTimePostNotif: "24h"
  DelayTime: "6h"

DeliveredEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-card-delivered-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 30
        Period: 1s
    Namespace: "card"

DeliveredEventPublisher:
  QueueName: "prod-card-delivered-event-queue"

MinAndroidVersionForAuthValidation : 111
MinIOSVersionForAuthValidation: 98

CardReplacementFeeUserGroupCheck:
  EnableUserGroupCheck: false
  AllowedUserGrp:
    - 1 # INTERNAL

FreeCardReplacementUserGroupCheck:
  EnableUserGroupCheck: true
  AllowedUserGrp:
    - 11 # FREE_CARD_REPLACEMENT

FetchTrackingDetailsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-card-tracking-details-fetch-queue"
  RetryStrategy: # Regular interval of 2 hours is followed for next 7 days.
    RegularInterval:
      Interval: 2
      MaxAttempts: 83
      TimeUnit: "Hour"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "card"

FetchCardDeliveryDetailsDelay: "24h"

Tracing:
  Enable: true

CardDispatchRequestPublisher:
  QueueName: "prod-card-dispatch-request-queue"

# TODO(team): cleanup card dispatch consumer as it not being used now, since dispatch flow has been moved to temporal wf.
CardDispatchRequestSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-card-dispatch-request-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~51 min post that regular interval of 2 hours is followed for next 24 hours.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 3
          MaxAttempts: 10
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 2
          MaxAttempts: 12
          TimeUnit: "Hour"
      MaxAttempts: 22
      CutOff: 10
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "card"

CardDispatchRequestCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-card-dispatch-request-callback-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~51 min post that regular interval of 2 hours is followed for next 24 hours.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 3
          MaxAttempts: 10
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 2
          MaxAttempts: 12
          TimeUnit: "Hour"
      MaxAttempts: 22
      CutOff: 10

InitDispatchWithAddressUpdatePublisher:
  QueueName: "prod-physical-card-dispatch-queue"

InitDispatchWithAddressUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-physical-card-dispatch-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~51 min post that regular interval of 2 hours is followed for next 24 hours.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 3
          MaxAttempts: 10
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 2
          MaxAttempts: 12
          TimeUnit: "Hour"
      MaxAttempts: 22
      CutOff: 10
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "card"

DigitalCardVersionConfig:
  MinAndroidVersion: 151
  MinIOSVersion: 204
  FallbackToEnableFeature: false

Profiling:
  StackDriverProfiling:
    ProjectId: "epifi-prod"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-prod-automatic-profiling"
    CPUPercentageLimit: 80
    MemoryPercentageLimit: 80
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

CardSwitchFinancialNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-card-switch-financial-notification-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~51 min post that regular interval of 2 hours is followed for next 7 days.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 3
          MaxAttempts: 10
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 2
          MaxAttempts: 83
          TimeUnit: "Hour"
      MaxAttempts: 93
      CutOff: 10
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "card"

CardSwitchNonFinancialNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-card-switch-non-financial-notification-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~51 min post that regular interval of 2 hours is followed for next 7 days.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 3
          MaxAttempts: 10
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 2
          MaxAttempts: 83
          TimeUnit: "Hour"
      MaxAttempts: 93
      CutOff: 10
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "card"

VendorFlagsMap:
  FEDERAL_BANK:
    EnableConsolidatedCardControlApi: true
    OfflineControlsDisabledByVendor: true

CardForexTxnNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-dc-forex-refund-order-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 11
      TimeUnit: "Minute"

CardTxnNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-card-txns-update-queue"
  RetryStrategy :
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 11
      TimeUnit: "Minute"

Buckets:
  DebitCardDocs: "epifi-prod-debit-card-docs"

CardCreationEventPublisher:
  TopicName: "prod-card-creation-event-topic"

DebitCardUpdateEventPublisher:
  TopicName: "prod-debit-card-update-topic"

UsePgdbConnForCardsDb: true

CardSwitchNotificationsRawDataStore:
  BucketName: "epifi-raw"
  FinancialNotificationsObjectKey: "vendor/federal_reports/card_switch_notifications/%s/data_dump_%s.csv"
  NonFinancialNotificationsObjectKey: "vendor/federal_reports/card_switch_notifications/%s/data_dump_1_%s.csv"

CardNudgeConfig:
  CARD_NUDGE_TYPE_ORDER_PHYSICAL_CARD:
    NudgeId: "90b2ec11-5913-4601-be61-2d066230f2a3"
    IsEnabled: false
  CARD_NUDGE_TYPE_TRACK_CARD_DELIVERY:
    NudgeId: "104edfdd-3b26-487e-9aeb-c090da6d565c"
    IsEnabled: true

PhysicalCardChargesConfig:
  FirstPhysicalCardChargesConfig:
    SegmentIdToChargesMap:
      480cf956-8e61-4ae9-842c-854908e07c56:  # high propensity users
        IsEnabled: true
        Amount:
          CurrencyCode: "INR"
          Units: 5
          Nanos: 0
        ExcludedSegmentIds:
          - "b6a8b426-bc47-4852-8d32-cd35a1336e43"
    UserGroupToChargeConfig:
      DEBIT_CARD_PARTNER_ACQUISITION:
        CurrencyCode: "INR"
        Units: 0
        Nanos: 0
      FREE_PHYSICAL_DC_REWARD:
        CurrencyCode: "INR"
        Units: 0
        Nanos: 0
  ReplacementPhysicalCardChargesMap:
    SegmentIdToChargesMap:
      480cf956-8e61-4ae9-842c-854908e07c56: # high propensity users
        IsEnabled: true
        Amount:
          CurrencyCode: "INR"
          Units: 5
          Nanos: 0
        ExcludedSegmentIds:
          - "b6a8b426-bc47-4852-8d32-cd35a1336e43"

CardsRedisStore:
  IsSecureRedis: true
  Options:
    Addr: "redis-10246.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:10246"
    Password: ""
  AuthDetails:
    SecretPath: "prod/redis/cards/prefixaccess"
    Environment: "prod"
    Region: "ap-south-1"
  ClientName: card
  HystrixCommand:
    CommandName: "card_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 10000
      ExecutionTimeout: 1s
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 50
      SleepWindow: 15s
      FallbackMaxConcurrency: 10000

CacheConfigs:
  CardCacheConfig:
    Prefix: "card:"
    IsCachingEnabled: true
    CacheTTl: "10m"
  UserLocationCacheConfig:
    CacheKey: "userLocationCacheKey"
    TTL: 168h
  UserTravelModeCacheConfig:
    CacheKey: "userTravelMode"
    TTL: 120h

PhysicalCardOrderPageSegmentConfig:
  BenefitsSegmentConfig:
    IsEnabled: false
    Segments:
      - SegmentId: "e1059dd5-bae7-4d2c-92e2-5a6b00e2f134"
        RewardInfo:
          Title: "Get a free 0 Forex Debit Card! Limited time offer."
          Icon: "https://epifi-icons.pointz.in/2x_fi_coins_icon.png"
        ValidFrom: 0
        ValidTill: **********
      - SegmentId: "d245349f-2f69-4e81-b80c-14b5e858de5e"
        RewardInfo:
          Title: "0 Forex Debit Card price drop: Now only ₹149+GST. Limited offer"
          Icon: "https://epifi-icons.pointz.in/2x_fi_coins_icon.png"
        ValidFrom: 0
        ValidTill: **********

DcAmcChargesEventQueueSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "prod-dc-amc-charges-event-queue"
  # data-prod account owns this queue
  QueueOwnerAccountId: "************"
  RetryStrategy:
    RegularInterval:
      Interval: 15
      MaxAttempts: 5
      TimeUnit: "Minute"

OrderPhysicalCardCriticalNotificationPublisher:
  QueueName: "prod-dc-onboarding-queue"

OrderPhysicalCardCriticalNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-dc-onboarding-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 3
      TimeUnit: "Second"

PhysicalDCOfferExperiment:
  IsExperimentEnabled: false
  IncludedSegments:
    - SegmentId: "2ca12435-2329-48c7-87bc-71eedd219b7a"
    - SegmentId: "91f52116-f3b1-4041-8bf3-c5935e0c93a0"
  ExcludedSegments:
    - SegmentId: "6d188aa7-45b2-4eb0-bb1d-0d826d6c695f"
  OfferAmount:
    CurrencyCode: "INR"
    Units: 149
    Nanos: 0
  Description: "Price drop alert! Fi-Federal Debit Card now for ₹149"
  Icon: "https://epifi-icons.pointz.in/card-images/cash-asset.png"

DcSlackAlertChannelId: "C05S8DKUDMG"

QuestSdk:
  Disable: false

F30IssuanceFeeRefundContent:
  IsEnabled: true
  SegmentIds:
    - "b6a8b426-bc47-4852-8d32-cd35a1336e43"
  Title: "You get the card fee back over next 3 spends!"

CardSwitchNotificationRewardsPublisher:
  TopicName: "prod-dc-switch-notification-topic"


OrderPhysicalCardScreenV2Config:
  CardFeesSectionConfig:
    SegmentIdToOfferInfoMap:
      b6a8b426-bc47-4852-8d32-cd35a1336e43:
        VisualElement:
          Url: "https://epifi-icons.pointz.in/card-images/3x100cashback-icon.png"
          Properties:
            Width: 84
            Height: 60
        IconTextComponent:
          Texts:
            - DisplayText: "Get ₹300 on your next 3 debit card spends >"
              FontStyle: "OVERLINE_2XS_CAPS"
              FontColor: "#EAE8F1"
          ContainerProperties:
            BgColor: "#6F62A4"
        OfferId: "PHYSICAL_CARD_WELCOME_OFFER_300_CB"

OrderPhysicalCardSuccessScreenV2Config:
  SegmentIdToInfoBannerMap:
    b6a8b426-bc47-4852-8d32-cd35a1336e43:
      Texts:
        - DisplayText: "Win ₹200 when you make your first international payment"
          FontStyle: "SUBTITLE_S"
          FontColor: "#F6F9FD"
      LeftImgTxtPadding: 34
      ContainerProperties:
        Height: 76
        Padding:
          Left: 20
          Right: 12
          Top: 5
          Bottom: 5
        CornerRadius: 20
        BgColor: "#28292B"
      LeftVisualElementImage:
        Url: "https://epifi-icons.pointz.in/card-images/free-atm-icon-4x.png"
        Properties:
          Width: 66
          Height: 66
