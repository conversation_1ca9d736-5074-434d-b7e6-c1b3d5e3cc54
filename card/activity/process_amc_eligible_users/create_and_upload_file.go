// nolint:depguard,funlen
package process_amc_eligible_users

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/logger"
	"google.golang.org/genproto/googleapis/type/money"

	"context"
	"fmt"
	"time"

	"github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/epifitemporal"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/jszwec/csvutil"
	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"

	bankCustPb "github.com/epifi/gamma/api/bankcust"
	cardPb "github.com/epifi/gamma/api/card"
	cardActivityPb "github.com/epifi/gamma/api/card/activity/processamceligibleusers"
	cardEnumsPb "github.com/epifi/gamma/api/card/enums"
	savingsPb "github.com/epifi/gamma/api/savings"
)

type SuccessfulAmcChargeCsvRow struct {
	UniqueNumber  string `csv:"UNIQUE_NUMBER"`
	Date          string `csv:"DATE"`
	Sol           string `csv:"SOL"`
	AccountNumber string `csv:"Account"`
	Debit         string `csv:"D"`
	Amount        string `csv:"AMOUNT(Excluding GST)"`
	DatePlusOne   string `csv:"DATE"`
	Narration     string `csv:"Narration"`
	Currency      string `csv:"INR"`
	Ocdem         string `csv:"OCDEM"`
	EmptyColumn   string `csv:""`
	BatchJob      string `csv:"Batch Job"`
}

type FailureUserAmcRow struct {
	CardId  string `csv:"card_id"`
	ActorId string `csv:"actor_id"`
	Reason  string `csv:"reason"`
}

func (p *Processor) CreateAndUploadFinalAmcFile(ctx context.Context, req *cardActivityPb.CreateAndUploadAmcUsersBatchFileRequest) (*cardActivityPb.CreateAndUploadAmcUsersBatchFileResponse, error) {
	var (
		res = &cardActivityPb.CreateAndUploadAmcUsersBatchFileResponse{}
	)
	lg := activity.GetLogger(ctx)

	fileGenTime := datetime.DateToTime(req.GetFileGenDate(), datetime.IST)
	nextDayTime := fileGenTime.AddDate(0, 0, 1)
	nextDate := datetime.TimeToDateInLoc(nextDayTime, datetime.IST)
	nextDateString := datetime.DateToString(nextDate, p.conf.AmcConfig.AmcDateLayout, datetime.IST)

	successCsvRows, failureCsvRows, err := p.getSuccessfulAndFailedUserRows(ctx, req.GetCardIds(), req.GetFileGenDate(), nextDateString)
	if err != nil {
		lg.Error("error in fetching rows", zap.Error(err))
		return nil, epifitemporal.NewTransientError(err)
	}
	successCsvRowsBytes, err := csvutil.Marshal(successCsvRows)
	if err != nil {
		lg.Error("error in marshalling success csv rows", zap.Error(err))
		return nil, epifitemporal.NewTransientError(err)
	}

	lg.Info("uploading amc users batch success file to s3",
		zap.Int32("batchNumber", req.GetBatchS3PathDetails().GetBatchNumber()),
		zap.String(logger.S3_PATH, req.GetBatchS3PathDetails().GetEligibleUsersS3Path()))
	err = p.uploadFileToS3(ctx, successCsvRowsBytes, req.GetBatchS3PathDetails().GetEligibleUsersS3Path())
	if err != nil {
		lg.Error("error in uploading file to s3:", zap.Error(err))
		return nil, epifitemporal.NewTransientError(err)
	}

	failureCsvRowInBytes, err := csvutil.Marshal(failureCsvRows)
	if err != nil {
		lg.Error("error in marshalling success csv rows", zap.Error(err))
		return nil, epifitemporal.NewTransientError(err)
	}

	lg.Info("uploading amc users batch failure file to s3",
		zap.Int32("batchNumber", req.GetBatchS3PathDetails().GetBatchNumber()),
		zap.String("failedUsersS3Path", req.GetBatchS3PathDetails().GetFailedUsersS3Path()))
	err = p.uploadFileToS3(ctx, failureCsvRowInBytes, req.GetBatchS3PathDetails().GetFailedUsersS3Path())
	if err != nil {
		lg.Error("error in uploading file to s3:", zap.Error(err))
		return nil, epifitemporal.NewTransientError(err)
	}

	return res, nil
}

func (p *Processor) getSuccessfulAndFailedUserRows(ctx context.Context, cardIds []string, fileGenDate *date.Date, nextDateString string) ([]*SuccessfulAmcChargeCsvRow, []*FailureUserAmcRow, error) {
	successRows := make([]*SuccessfulAmcChargeCsvRow, 0)
	failureRows := make([]*FailureUserAmcRow, 0)
	for _, cardId := range cardIds {
		card, err := p.cardDao.GetByID(ctx, cardId, cardPb.CardFieldMask_CARD_ACTOR_ID)
		switch {
		case storagev2.IsRecordNotFoundError(err):
			failureRows = append(failureRows, &FailureUserAmcRow{
				CardId: cardId,
				Reason: "Card not found",
			})
			continue
		case err != nil:
			return nil, nil, err
		}

		cardRequests, err := p.cardRequestDao.GetByActorIdAndWorkflow(ctx, card.GetActorId(), cardEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_PROCESS_AMC_CHARGES, nil)
		if storagev2.IsRecordNotFoundError(err) {
			failureRows = append(failureRows, &FailureUserAmcRow{
				CardId: cardId,
				Reason: "Card request for amc charges not found",
			})
		}

		if !p.isValidCardRequest(cardRequests[0]) {
			failureRows = append(failureRows, &FailureUserAmcRow{
				CardId: cardId,
				Reason: "Card request for amc charges is not of current year",
			})
			continue
		}

		cardRequest := cardRequests[0]
		userFileGenDate := cardRequest.GetRequestDetails().GetAmcChargesDetails().GetFileGenDate()
		if !p.isDateInValidRange(userFileGenDate, fileGenDate) {
			userFileGenDateString := datetime.DateToString(userFileGenDate, p.conf.AmcConfig.AmcDateLayout, datetime.IST)
			fileGenDateString := datetime.DateToString(fileGenDate, p.conf.AmcConfig.AmcDateLayout, datetime.IST)
			failureRows = append(failureRows, &FailureUserAmcRow{
				CardId: cardId,
				Reason: fmt.Sprintf("Mismatching file gen date. User file gen date %s,Actual file gen date: %s", userFileGenDateString, fileGenDateString),
			})
			continue
		}
		if cardRequest.GetRequestDetails().GetAmcChargesDetails().GetOperationalStatus() == cardEnumsPb.OperationalStatus_OPERATIONAL_STATUS_CLOSED {
			failureRows = append(failureRows, &FailureUserAmcRow{
				CardId: cardId,
				Reason: fmt.Sprintf("Operational status is %s", cardRequest.GetRequestDetails().GetAmcChargesDetails().GetOperationalStatus().String()),
			})
			continue
		}
		if cardRequest.GetRequestDetails().GetAmcChargesDetails().GetFreezeStatus() != cardEnumsPb.FreezeStatus_FREEZE_STATUS_UNSPECIFIED {
			failureRows = append(failureRows, &FailureUserAmcRow{
				CardId: cardId,
				Reason: fmt.Sprintf("Freeze status is %s", cardRequest.GetRequestDetails().GetAmcChargesDetails().GetFreezeStatus().String()),
			})
			continue
		}

		successRow, err := p.getSuccessRowDetails(ctx, card.GetActorId(), cardId, len(successRows)+1, nextDateString, cardRequest.GetRequestDetails().GetAmcChargesDetails().GetAmcChargeAmount())
		if err != nil {
			return nil, nil, errors.Wrap(err, "error in fetching success row for the user")
		}

		successRows = append(successRows, successRow)

	}
	return successRows, failureRows, nil
}

func (p *Processor) uploadFileToS3(ctx context.Context, csvData []byte, s3Path string) error {
	err := p.dcDocsS3Client.Write(ctx, s3Path, csvData, string(types.ObjectCannedACLBucketOwnerFullControl))
	if err != nil {
		return errors.Wrap(err, "error in writing csv file to s3")
	}
	return nil
}

func (p *Processor) getSuccessRowDetails(ctx context.Context, actorId string, cardId string, rowNumber int, dateString string, amcCharge *money.Money) (*SuccessfulAmcChargeCsvRow, error) {
	savingsResp, err := p.savingsClient.GetSavingsAccountEssentials(ctx, &savingsPb.GetSavingsAccountEssentialsRequest{
		Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
			ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
				ActorId:     actorId,
				PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
			},
		},
	})
	if te := epifigrpc.RPCError(savingsResp, err); te != nil {
		return nil, errors.Wrap(te, "error fetching savings account")
	}
	bankCustomer, err := p.bankCustomerClient.GetBankCustomer(ctx, &bankCustPb.GetBankCustomerRequest{
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankCustPb.GetBankCustomerRequest_ActorId{
			ActorId: actorId,
		},
	})
	if te := epifigrpc.RPCError(bankCustomer, err); te != nil {
		return nil, errors.Wrap(te, "error facing savings account")
	}
	userCard, err := p.cardDao.GetByID(ctx, cardId)
	if err != nil {
		return nil, err
	}
	maskedCardNumber := userCard.GetBasicInfo().GetMaskedCardNumber()
	if len(maskedCardNumber) < 4 {
		return nil, errors.New(fmt.Sprintf("masked card number is of length < 4, actorId: %s, cardId: %s", actorId, cardId))
	}
	lastFourDigits := maskedCardNumber[len(maskedCardNumber)-4:]
	amountString := moneyPkg.GetDisplayString(amcCharge, 2, false, false, moneyPkg.IndianNumberSystem)

	return &SuccessfulAmcChargeCsvRow{
		UniqueNumber:  fmt.Sprintf(p.conf.AmcConfig.ChargePrefixFormat, rowNumber),
		Date:          dateString,
		Sol:           bankCustomer.GetBankCustomer().GetVendorMetadata().GetFederalMetadata().GetSolId(),
		AccountNumber: savingsResp.GetAccount().GetAccountNo(),
		Debit:         p.conf.AmcConfig.Debit,
		Amount:        amountString,
		DatePlusOne:   dateString,
		Narration:     fmt.Sprintf(p.conf.AmcConfig.NarrationPrefixFormat, lastFourDigits),
		Currency:      amcCharge.GetCurrencyCode(),
		Ocdem:         p.conf.AmcConfig.Ocdem,
		// explicitly left empty: do not change
		EmptyColumn: "",
		BatchJob:    p.conf.AmcConfig.BatchJob,
	}, nil
}

func (p *Processor) isDateInValidRange(userFileGenDate *date.Date, currentFileGenDate *date.Date) bool {
	userFileGenTime := datetime.DateToTime(userFileGenDate, datetime.IST)
	currentFileGenTime := datetime.DateToTime(currentFileGenDate, datetime.IST)
	timeDiffDuration := currentFileGenTime.Sub(*userFileGenTime)
	// if the date is within 10 days of current file generation it is valid
	return timeDiffDuration < (24 * 10 * time.Hour)
}
