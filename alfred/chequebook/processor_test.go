package chequebook

import (
	"context"
	"fmt"
	"reflect"
	"sync"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/idgen"
	mock2 "github.com/epifi/be-common/pkg/mock"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/pagination"
	"github.com/epifi/gamma/alfred/eligibility"
	"github.com/epifi/gamma/alfred/procfactory"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	alfredPb "github.com/epifi/gamma/api/alfred"
	"github.com/epifi/gamma/api/frontend/deeplink"
	types "github.com/epifi/gamma/api/typesv2"
)

var (
	actor1            = "actor-1"
	id1               = "id-1"
	id2               = "id-2"
	id3               = "id-3"
	trackingId        = "tracking-id"
	courierPartner    = "courier-partner"
	chequebookReqType = alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK
	now               = timestamp.New(timestamp.Now().AsTime().Round(time.Millisecond))

	srReq1 = &alfredPb.ServiceRequest{
		Id:          id1,
		ActorId:     actor1,
		RequestType: chequebookReqType,
		Status:      alfredPb.Status_STATUS_IN_PROGRESS,
		Details: &alfredPb.Details{
			Metadata: nil,
		},
	}

	orderChequebookDeeplink    = initialiseOrderChqbkDeeplinkOptions(context.Background(), false, srReq1)
	highTierChequebookDeeplink = initialiseOrderChqbkDeeplinkOptions(context.Background(), true, srReq1)
)

func TestProcessor_ProvisionNewRequest(t *testing.T) {
	type args struct {
		ctx     context.Context
		actorId string
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(mock *mockDependencies)
		want    *deeplink.Deeplink
		wantErr error
	}{
		{
			name: "failed: permanent error while fetching entry from alfred db",
			args: args{
				ctx:     context.Background(),
				actorId: actor1,
			},
			mocks: func(mock *mockDependencies) {
				mock.mockAlfredDao.EXPECT().GetByActorIdRequestType(context.Background(), actor1, chequebookReqType).Return(nil, epifierrors.ErrPermanent)
			},
			want:    nil,
			wantErr: epifierrors.ErrPermanent,
		},
		{
			name: "error in creating new requested",
			args: args{
				ctx:     context.Background(),
				actorId: actor1,
			},
			mocks: func(mock *mockDependencies) {
				mock.mockAlfredDao.EXPECT().GetByActorIdRequestType(context.Background(), actor1, chequebookReqType).Return(nil, epifierrors.ErrRecordNotFound)
				mock.mockIdGen.EXPECT().GetInAlphaNumeric(idgen.Chequebook).Return(id1)
				mock.mockAlfredDao.EXPECT().Create(context.Background(), &alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_IN_PROGRESS,
					Details:     &alfredPb.Details{},
				}).Return(nil, epifierrors.ErrDowntimeExpected)
			},
			want:    nil,
			wantErr: epifierrors.ErrDowntimeExpected,
		},
		{
			name: "error in checking eligibility",
			args: args{
				ctx:     context.Background(),
				actorId: actor1,
			},
			mocks: func(mock *mockDependencies) {
				mock.mockAlfredDao.EXPECT().GetByActorIdRequestType(context.Background(), actor1, chequebookReqType).Return(nil, epifierrors.ErrRecordNotFound)
				mock.mockIdGen.EXPECT().GetInAlphaNumeric(idgen.Chequebook).Return(id1)
				mock.mockAlfredDao.EXPECT().Create(context.Background(), &alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_IN_PROGRESS,
					Details:     &alfredPb.Details{},
				}).Return(&alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_IN_PROGRESS,
					Details:     &alfredPb.Details{},
				}, nil)
				mock.mockRequestEligibilityChecker.EXPECT().IsActorEligible(gomock.Any(), &procfactory.IsActorEligibleRequest{
					ActorId: actor1,
				}).Return(nil, epifierrors.ErrDowntimeExpected)
			},
			want:    nil,
			wantErr: epifierrors.ErrDowntimeExpected,
		},
		{
			name: "success: error in checking free chequebook",
			args: args{
				ctx:     context.Background(),
				actorId: actor1,
			},
			mocks: func(mock *mockDependencies) {
				mock.mockAlfredDao.EXPECT().GetByActorIdRequestType(context.Background(), actor1, chequebookReqType).Return(nil, epifierrors.ErrRecordNotFound)
				mock.mockIdGen.EXPECT().GetInAlphaNumeric(idgen.Chequebook).Return(id1)
				mock.mockAlfredDao.EXPECT().Create(context.Background(), &alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_IN_PROGRESS,
					Details:     &alfredPb.Details{},
				}).Return(&alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_IN_PROGRESS,
					Details:     &alfredPb.Details{},
				}, nil)
				mock.mockRequestEligibilityChecker.EXPECT().IsActorEligible(gomock.Any(), &procfactory.IsActorEligibleRequest{
					ActorId: actor1,
				}).Return(&procfactory.IsActorEligibleResponse{
					IsEligible: true,
				}, nil)
				mock.mockFreeChequebookEligibilityChecker.EXPECT().IsEligibleForFreeChequebook(gomock.Any(), actor1).Return(false, epifierrors.ErrDowntimeExpected)
			},
			want:    nil,
			wantErr: epifierrors.ErrDowntimeExpected,
		},
		{
			name: "success: error in updating chequebook metadata",
			args: args{
				ctx:     context.Background(),
				actorId: actor1,
			},
			mocks: func(mock *mockDependencies) {
				mock.mockAlfredDao.EXPECT().GetByActorIdRequestType(context.Background(), actor1, chequebookReqType).Return(nil, epifierrors.ErrRecordNotFound)
				mock.mockIdGen.EXPECT().GetInAlphaNumeric(idgen.Chequebook).Return(id1)
				mock.mockAlfredDao.EXPECT().Create(context.Background(), &alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_IN_PROGRESS,
					Details:     &alfredPb.Details{},
				}).Return(&alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_IN_PROGRESS,
					Details:     &alfredPb.Details{},
				}, nil)
				mock.mockRequestEligibilityChecker.EXPECT().IsActorEligible(gomock.Any(), &procfactory.IsActorEligibleRequest{
					ActorId: actor1,
				}).Return(&procfactory.IsActorEligibleResponse{
					IsEligible: true,
				}, nil)
				mock.mockFreeChequebookEligibilityChecker.EXPECT().IsEligibleForFreeChequebook(gomock.Any(), actor1).Return(false, nil)
				mock.mockAlfredDao.EXPECT().UpdateServiceRequestByFieldMask(gomock.Any(), &alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_IN_PROGRESS,
					Details: &alfredPb.Details{
						Metadata: &alfredPb.Details_ChequebookMetadata{
							ChequebookMetadata: &alfredPb.ChequebookMetadata{
								Charges: &types.Money{
									Units:        118,
									CurrencyCode: moneyPkg.RupeeCurrencyCode,
								},
							},
						},
					},
				}, []alfredPb.ServiceRequestFieldMask{alfredPb.ServiceRequestFieldMask_SERVICE_REQUEST_FIELD_MASK_METADATA}).
					Return(nil, epifierrors.ErrDowntimeExpected)
			},
			want:    nil,
			wantErr: epifierrors.ErrDowntimeExpected,
		},
		{
			name: "success: error in updating chequebook metadata",
			args: args{
				ctx:     context.Background(),
				actorId: actor1,
			},
			mocks: func(mock *mockDependencies) {
				mock.mockAlfredDao.EXPECT().GetByActorIdRequestType(context.Background(), actor1, chequebookReqType).Return(nil, epifierrors.ErrRecordNotFound)
				mock.mockIdGen.EXPECT().GetInAlphaNumeric(idgen.Chequebook).Return(id1)
				mock.mockAlfredDao.EXPECT().Create(context.Background(), &alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_IN_PROGRESS,
					Details:     &alfredPb.Details{},
				}).Return(&alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_IN_PROGRESS,
					Details:     &alfredPb.Details{},
				}, nil)
				mock.mockRequestEligibilityChecker.EXPECT().IsActorEligible(gomock.Any(), &procfactory.IsActorEligibleRequest{
					ActorId: actor1,
				}).Return(&procfactory.IsActorEligibleResponse{
					IsEligible: true,
				}, nil)
				mock.mockFreeChequebookEligibilityChecker.EXPECT().IsEligibleForFreeChequebook(gomock.Any(), actor1).Return(false, nil)
				mock.mockAlfredDao.EXPECT().UpdateServiceRequestByFieldMask(gomock.Any(), &alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_IN_PROGRESS,
					Details: &alfredPb.Details{
						Metadata: &alfredPb.Details_ChequebookMetadata{
							ChequebookMetadata: &alfredPb.ChequebookMetadata{
								Charges: &types.Money{
									Units:        118,
									CurrencyCode: moneyPkg.RupeeCurrencyCode,
								},
							},
						},
					},
				}, []alfredPb.ServiceRequestFieldMask{alfredPb.ServiceRequestFieldMask_SERVICE_REQUEST_FIELD_MASK_METADATA}).
					Return(nil, epifierrors.ErrDowntimeExpected)
			},
			want:    nil,
			wantErr: epifierrors.ErrDowntimeExpected,
		},
		{
			name: "success: no previous record found and create a successful request for high tier user",
			args: args{
				ctx:     context.Background(),
				actorId: actor1,
			},
			mocks: func(mock *mockDependencies) {
				mock.mockAlfredDao.EXPECT().GetByActorIdRequestType(context.Background(), actor1, chequebookReqType).Return(nil, epifierrors.ErrRecordNotFound)
				mock.mockIdGen.EXPECT().GetInAlphaNumeric(idgen.Chequebook).Return(id1)
				mock.mockAlfredDao.EXPECT().Create(context.Background(), &alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_IN_PROGRESS,
					Details:     &alfredPb.Details{},
				}).Return(&alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_IN_PROGRESS,
					Details:     &alfredPb.Details{},
				}, nil)
				mock.mockRequestEligibilityChecker.EXPECT().IsActorEligible(gomock.Any(), &procfactory.IsActorEligibleRequest{
					ActorId: actor1,
				}).Return(&procfactory.IsActorEligibleResponse{
					IsEligible: true,
				}, nil)
				mock.mockFreeChequebookEligibilityChecker.EXPECT().IsEligibleForFreeChequebook(gomock.Any(), actor1).Return(true, nil)
				mock.mockAlfredDao.EXPECT().UpdateServiceRequestByFieldMask(gomock.Any(), &alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_IN_PROGRESS,
					Details: &alfredPb.Details{
						Metadata: &alfredPb.Details_ChequebookMetadata{
							ChequebookMetadata: &alfredPb.ChequebookMetadata{
								Charges: &types.Money{
									Units:        0,
									CurrencyCode: moneyPkg.RupeeCurrencyCode,
								},
							},
						},
					},
				}, []alfredPb.ServiceRequestFieldMask{alfredPb.ServiceRequestFieldMask_SERVICE_REQUEST_FIELD_MASK_METADATA}).
					Return(nil, nil)
			},
			want:    highTierChequebookDeeplink,
			wantErr: nil,
		},
		{
			name: "success: previous request failed and create a successful request for chequebook",
			args: args{
				ctx:     context.Background(),
				actorId: actor1,
			},
			mocks: func(mock *mockDependencies) {
				mock.mockAlfredDao.EXPECT().GetByActorIdRequestType(context.Background(), actor1, chequebookReqType).Return([]*alfredPb.ServiceRequest{{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_FAILED,
					Details:     &alfredPb.Details{},
				}}, nil)
				mock.mockRequestSynchronizer.EXPECT().SyncRequest(gomock.Any(), id1).Return(&alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_FAILED,
					Details:     &alfredPb.Details{},
				}, nil)
				mock.mockAlfredDao.EXPECT().DeleteByActorIdRequestType(context.Background(), actor1, chequebookReqType).Return(nil)
				mock.mockIdGen.EXPECT().GetInAlphaNumeric(idgen.Chequebook).Return(id1)
				mock.mockAlfredDao.EXPECT().Create(context.Background(), &alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_IN_PROGRESS,
					Details:     &alfredPb.Details{},
				}).Return(&alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_IN_PROGRESS,
					Details:     &alfredPb.Details{},
				}, nil)
				mock.mockRequestEligibilityChecker.EXPECT().IsActorEligible(gomock.Any(), &procfactory.IsActorEligibleRequest{
					ActorId: actor1,
				}).Return(&procfactory.IsActorEligibleResponse{
					IsEligible: true,
				}, nil)
				mock.mockFreeChequebookEligibilityChecker.EXPECT().IsEligibleForFreeChequebook(gomock.Any(), actor1).Return(false, nil)
				mock.mockAlfredDao.EXPECT().UpdateServiceRequestByFieldMask(gomock.Any(), &alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_IN_PROGRESS,
					Details: &alfredPb.Details{
						Metadata: &alfredPb.Details_ChequebookMetadata{
							ChequebookMetadata: &alfredPb.ChequebookMetadata{
								Charges: &types.Money{
									Units:        118,
									CurrencyCode: moneyPkg.RupeeCurrencyCode,
								},
							},
						},
					},
				}, []alfredPb.ServiceRequestFieldMask{alfredPb.ServiceRequestFieldMask_SERVICE_REQUEST_FIELD_MASK_METADATA}).
					Return(nil, nil)
			},
			want:    orderChequebookDeeplink,
			wantErr: nil,
		},
		{
			name: "error while syncing latest status",
			args: args{
				ctx:     context.Background(),
				actorId: actor1,
			},
			mocks: func(mock *mockDependencies) {
				mock.mockAlfredDao.EXPECT().GetByActorIdRequestType(context.Background(), actor1, chequebookReqType).Return([]*alfredPb.ServiceRequest{{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_FAILED,
					Details:     &alfredPb.Details{},
				}}, nil)
				mock.mockRequestSynchronizer.EXPECT().SyncRequest(gomock.Any(), id1).Return(nil, epifierrors.ErrDowntimeExpected)
			},
			want:    nil,
			wantErr: epifierrors.ErrDowntimeExpected,
		},
		{
			name: "failed to delete previous request",
			args: args{
				ctx:     context.Background(),
				actorId: actor1,
			},
			mocks: func(mock *mockDependencies) {
				mock.mockAlfredDao.EXPECT().GetByActorIdRequestType(context.Background(), actor1, chequebookReqType).Return([]*alfredPb.ServiceRequest{{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_FAILED,
					Details:     &alfredPb.Details{},
				}}, nil)
				mock.mockRequestSynchronizer.EXPECT().SyncRequest(gomock.Any(), id1).Return(&alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_FAILED,
					Details:     &alfredPb.Details{},
				}, nil)
				mock.mockAlfredDao.EXPECT().DeleteByActorIdRequestType(context.Background(), actor1, chequebookReqType).Return(epifierrors.ErrDowntimeExpected)
			},
			want:    nil,
			wantErr: fmt.Errorf("error while deleting request %v", id1),
		},
		{
			name: "success: got success response from db",
			args: args{
				ctx:     context.Background(),
				actorId: actor1,
			},
			mocks: func(mock *mockDependencies) {
				mock.mockAlfredDao.EXPECT().GetByActorIdRequestType(context.Background(), actor1, chequebookReqType).Return([]*alfredPb.ServiceRequest{
					{
						Id:          id1,
						ActorId:     actor1,
						RequestType: chequebookReqType,
						Status:      alfredPb.Status_STATUS_SUCCESS,
						Details: &alfredPb.Details{
							Metadata: &alfredPb.Details_ChequebookMetadata{
								ChequebookMetadata: &alfredPb.ChequebookMetadata{
									OrderedAt:    timestamp.New(now.AsTime().Add(-15 * 24 * time.Hour)),
									CourieredVia: courierPartner,
									TrackingId:   trackingId,
								},
							},
						},
					},
				}, nil)
				mock.mockRequestSynchronizer.EXPECT().SyncRequest(gomock.Any(), id1).Return(&alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_SUCCESS,
					Details: &alfredPb.Details{
						Metadata: &alfredPb.Details_ChequebookMetadata{
							ChequebookMetadata: &alfredPb.ChequebookMetadata{
								OrderedAt:    timestamp.New(now.AsTime().Add(-15 * 24 * time.Hour)),
								CourieredVia: courierPartner,
								TrackingId:   trackingId,
							},
						},
					},
				}, nil)
			},
			want: alreadyPlacedRequestWithInXDays(&alfredPb.ServiceRequest{
				Id:          id1,
				ActorId:     actor1,
				RequestType: chequebookReqType,
				Status:      alfredPb.Status_STATUS_SUCCESS,
				Details: &alfredPb.Details{
					Metadata: &alfredPb.Details_ChequebookMetadata{
						ChequebookMetadata: &alfredPb.ChequebookMetadata{
							OrderedAt:    timestamp.New(now.AsTime().Add(-15 * 24 * time.Hour)),
							CourieredVia: courierPartner,
							TrackingId:   trackingId,
						},
					},
				},
			}, false),
			wantErr: nil,
		},
		{
			name: "success: got success response from db but breached 31 days",
			args: args{
				ctx:     context.Background(),
				actorId: actor1,
			},
			mocks: func(mock *mockDependencies) {
				mock.mockAlfredDao.EXPECT().GetByActorIdRequestType(context.Background(), actor1, chequebookReqType).Return([]*alfredPb.ServiceRequest{
					{
						Id:          id1,
						ActorId:     actor1,
						RequestType: chequebookReqType,
						Status:      alfredPb.Status_STATUS_SUCCESS,
						Details: &alfredPb.Details{
							Metadata: &alfredPb.Details_ChequebookMetadata{
								ChequebookMetadata: &alfredPb.ChequebookMetadata{
									OrderedAt:    timestamp.New(now.AsTime().Add(-31 * 24 * time.Hour)),
									CourieredVia: courierPartner,
									TrackingId:   trackingId,
								},
							},
						},
					},
				}, nil)
				mock.mockRequestSynchronizer.EXPECT().SyncRequest(gomock.Any(), id1).Return(&alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_SUCCESS,
					Details: &alfredPb.Details{
						Metadata: &alfredPb.Details_ChequebookMetadata{
							ChequebookMetadata: &alfredPb.ChequebookMetadata{
								OrderedAt:    timestamp.New(now.AsTime().Add(-31 * 24 * time.Hour)),
								CourieredVia: courierPartner,
								TrackingId:   trackingId,
							},
						},
					},
				}, nil)
				mock.mockAlfredDao.EXPECT().DeleteByActorIdRequestType(context.Background(), actor1, chequebookReqType).Return(nil)
				mock.mockIdGen.EXPECT().GetInAlphaNumeric(idgen.Chequebook).Return(id1)
				mock.mockAlfredDao.EXPECT().Create(context.Background(), &alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_IN_PROGRESS,
					Details:     &alfredPb.Details{},
				}).Return(&alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_IN_PROGRESS,
					Details:     &alfredPb.Details{},
				}, nil)
				mock.mockRequestEligibilityChecker.EXPECT().IsActorEligible(gomock.Any(), &procfactory.IsActorEligibleRequest{
					ActorId: actor1,
				}).Return(&procfactory.IsActorEligibleResponse{
					IsEligible: true,
				}, nil)
				mock.mockFreeChequebookEligibilityChecker.EXPECT().IsEligibleForFreeChequebook(gomock.Any(), actor1).Return(false, nil)
				mock.mockAlfredDao.EXPECT().UpdateServiceRequestByFieldMask(gomock.Any(), &alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_IN_PROGRESS,
					Details: &alfredPb.Details{
						Metadata: &alfredPb.Details_ChequebookMetadata{
							ChequebookMetadata: &alfredPb.ChequebookMetadata{
								Charges: &types.Money{
									Units:        118,
									CurrencyCode: moneyPkg.RupeeCurrencyCode,
								},
							},
						},
					},
				}, []alfredPb.ServiceRequestFieldMask{alfredPb.ServiceRequestFieldMask_SERVICE_REQUEST_FIELD_MASK_METADATA}).
					Return(nil, nil)
			},
			want:    orderChequebookDeeplink,
			wantErr: nil,
		},
		{
			name: "success: request was in progress and workflow return no record found",
			args: args{
				ctx:     context.Background(),
				actorId: actor1,
			},
			mocks: func(mock *mockDependencies) {
				mock.mockAlfredDao.EXPECT().GetByActorIdRequestType(context.Background(), actor1, chequebookReqType).Return([]*alfredPb.ServiceRequest{
					{
						Id:          id1,
						ActorId:     actor1,
						RequestType: chequebookReqType,
						Status:      alfredPb.Status_STATUS_IN_PROGRESS,
						Details:     &alfredPb.Details{},
					},
				}, nil)
				mock.mockRequestSynchronizer.EXPECT().SyncRequest(gomock.Any(), id1).Return(&alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_IN_PROGRESS,
					Details:     &alfredPb.Details{},
				}, epifierrors.ErrRecordNotFound)
				mock.mockRequestEligibilityChecker.EXPECT().IsActorEligible(gomock.Any(), &procfactory.IsActorEligibleRequest{
					ActorId: actor1,
				}).Return(&procfactory.IsActorEligibleResponse{
					IsEligible: true,
				}, nil)
				mock.mockFreeChequebookEligibilityChecker.EXPECT().IsEligibleForFreeChequebook(gomock.Any(), actor1).Return(false, nil)
				mock.mockAlfredDao.EXPECT().UpdateServiceRequestByFieldMask(gomock.Any(), mock2.NewProtoMatcher(&alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_IN_PROGRESS,
					Details: &alfredPb.Details{
						Metadata: &alfredPb.Details_ChequebookMetadata{
							ChequebookMetadata: &alfredPb.ChequebookMetadata{
								Charges: &types.Money{
									Units:        118,
									CurrencyCode: moneyPkg.RupeeCurrencyCode,
								},
							},
						},
					},
				}), []alfredPb.ServiceRequestFieldMask{alfredPb.ServiceRequestFieldMask_SERVICE_REQUEST_FIELD_MASK_METADATA}).
					Return(nil, nil)
			},
			want:    orderChequebookDeeplink,
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p, md := newProcessorWithMocks(t)
			if tt.mocks != nil {
				tt.mocks(md)
			}
			got, err := p.ProvisionNewRequest(tt.args.ctx, tt.args.actorId, nil)
			if (err != nil) || tt.wantErr != nil {
				if !assert.Equal(t, tt.wantErr.Error(), err.Error()) {
					t.Errorf("ProvisionNewRequest() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&deeplink.OrderChequebookOptions{}, "credblock_request_id"),
			}
			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("ProvisionNewRequest() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProcessor_GetRequestStatus(t *testing.T) {
	type args struct {
		requestId string
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(md *mockDependencies)
		want    *deeplink.Deeplink
		wantErr error
	}{
		{
			name: "error in fetching the request",
			args: args{
				requestId: id1,
			},
			mocks: func(md *mockDependencies) {
				md.mockAlfredDao.EXPECT().GetById(gomock.Any(), id1).Return(nil, epifierrors.ErrDowntimeExpected)
			},
			want:    nil,
			wantErr: epifierrors.ErrDowntimeExpected,
		},
		{
			name: "",
			args: args{
				requestId: id1,
			},
			mocks: func(md *mockDependencies) {
				md.mockAlfredDao.EXPECT().GetById(context.Background(), id1).Return(&alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_IN_PROGRESS,
					Details:     &alfredPb.Details{},
				}, nil)
				md.mockRequestSynchronizer.EXPECT().SyncRequest(gomock.Any(), id1).
					Return(nil, epifierrors.ErrDowntimeExpected)
			},
			want:    nil,
			wantErr: epifierrors.ErrDowntimeExpected,
		},
		{
			name: "request is in pending state",
			args: args{
				requestId: id1,
			},
			mocks: func(md *mockDependencies) {
				md.mockAlfredDao.EXPECT().GetById(context.Background(), id1).Return(&alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_IN_PROGRESS,
					Details: &alfredPb.Details{
						Metadata: &alfredPb.Details_ChequebookMetadata{
							ChequebookMetadata: &alfredPb.ChequebookMetadata{
								Charges: &types.Money{
									Units:        118,
									CurrencyCode: moneyPkg.RupeeCurrencyCode,
								},
							},
						},
					},
				}, nil)
				md.mockRequestSynchronizer.EXPECT().SyncRequest(gomock.Any(), id1).Return(&alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_IN_PROGRESS,
					Details: &alfredPb.Details{
						Metadata: &alfredPb.Details_ChequebookMetadata{
							ChequebookMetadata: &alfredPb.ChequebookMetadata{
								Charges: &types.Money{
									Units:        118,
									CurrencyCode: moneyPkg.RupeeCurrencyCode,
								},
								ChequeLeavesCount: 10,
							},
						},
					},
				}, nil)
			},
			want: sendPollingDL(&alfredPb.ServiceRequest{
				Id:          id1,
				ActorId:     actor1,
				RequestType: chequebookReqType,
				Status:      alfredPb.Status_STATUS_FAILED,
				Details: &alfredPb.Details{
					Metadata: &alfredPb.Details_ChequebookMetadata{
						ChequebookMetadata: &alfredPb.ChequebookMetadata{
							Charges: &types.Money{
								Units:        118,
								CurrencyCode: moneyPkg.RupeeCurrencyCode,
							},
							ChequeLeavesCount: 10,
						},
					},
				},
			}),
			wantErr: nil,
		},
		{
			name: "error while checking free chequebook",
			args: args{
				requestId: id1,
			},
			mocks: func(md *mockDependencies) {
				md.mockAlfredDao.EXPECT().GetById(context.Background(), id1).Return(&alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_IN_PROGRESS,
					Details: &alfredPb.Details{
						Metadata: &alfredPb.Details_ChequebookMetadata{
							ChequebookMetadata: &alfredPb.ChequebookMetadata{
								Charges: &types.Money{
									Units:        118,
									CurrencyCode: moneyPkg.RupeeCurrencyCode,
								},
							},
						},
					},
				}, nil)
				md.mockRequestSynchronizer.EXPECT().SyncRequest(gomock.Any(), id1).Return(&alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_IN_PROGRESS,
					Details: &alfredPb.Details{
						Metadata: &alfredPb.Details_ChequebookMetadata{
							ChequebookMetadata: &alfredPb.ChequebookMetadata{
								Charges: &types.Money{
									Units:        118,
									CurrencyCode: moneyPkg.RupeeCurrencyCode,
								},
								OrderedAt:         now,
								ChequeLeavesCount: 10,
							},
						},
					},
				}, nil)
				md.mockFreeChequebookEligibilityChecker.EXPECT().IsEligibleForFreeChequebook(gomock.Any(), actor1).
					Return(false, epifierrors.ErrDowntimeExpected)
			},
			want:    nil,
			wantErr: epifierrors.ErrDowntimeExpected,
		},
		{
			name: "request placed successfully",
			args: args{
				requestId: id1,
			},
			mocks: func(md *mockDependencies) {
				md.mockAlfredDao.EXPECT().GetById(context.Background(), id1).Return(&alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_IN_PROGRESS,
					Details: &alfredPb.Details{
						Metadata: &alfredPb.Details_ChequebookMetadata{
							ChequebookMetadata: &alfredPb.ChequebookMetadata{
								Charges: &types.Money{
									Units:        118,
									CurrencyCode: moneyPkg.RupeeCurrencyCode,
								},
							},
						},
					},
				}, nil)
				md.mockRequestSynchronizer.EXPECT().SyncRequest(gomock.Any(), id1).Return(&alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_IN_PROGRESS,
					Details: &alfredPb.Details{
						Metadata: &alfredPb.Details_ChequebookMetadata{
							ChequebookMetadata: &alfredPb.ChequebookMetadata{
								Charges: &types.Money{
									Units:        118,
									CurrencyCode: moneyPkg.RupeeCurrencyCode,
								},
								OrderedAt:         now,
								ChequeLeavesCount: 10,
							},
						},
					},
				}, nil)
				md.mockFreeChequebookEligibilityChecker.EXPECT().IsEligibleForFreeChequebook(gomock.Any(), actor1).
					Return(false, nil)
			},
			want: eligibility.InfoAcknowledgementScreen(eligibility.ContentOptions{
				Title:    infoAckTitle,
				Subtitle: infoAckSubtitle,
				ImageUrl: infoAckImageUrl,
				CtaDeeplink: requestDetailsDL(&alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_IN_PROGRESS,
					Details: &alfredPb.Details{
						Metadata: &alfredPb.Details_ChequebookMetadata{
							ChequebookMetadata: &alfredPb.ChequebookMetadata{
								Charges: &types.Money{
									Units:        118,
									CurrencyCode: moneyPkg.RupeeCurrencyCode,
								},
								OrderedAt:         now,
								ChequeLeavesCount: 10,
							},
						},
					},
				}, false),
				CtaText: infoAckCtaText,
			}),
			wantErr: nil,
		},
		{
			name: "request in terminal failed state",
			args: args{
				requestId: id1,
			},
			mocks: func(md *mockDependencies) {
				md.mockAlfredDao.EXPECT().GetById(context.Background(), id1).Return(&alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_IN_PROGRESS,
					Details: &alfredPb.Details{
						Metadata: &alfredPb.Details_ChequebookMetadata{
							ChequebookMetadata: &alfredPb.ChequebookMetadata{
								Charges: &types.Money{
									Units:        118,
									CurrencyCode: moneyPkg.RupeeCurrencyCode,
								},
							},
						},
					},
				}, nil)
				md.mockRequestSynchronizer.EXPECT().SyncRequest(gomock.Any(), id1).Return(&alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: chequebookReqType,
					Status:      alfredPb.Status_STATUS_FAILED,
					Details: &alfredPb.Details{
						Metadata: &alfredPb.Details_ChequebookMetadata{
							ChequebookMetadata: &alfredPb.ChequebookMetadata{
								Charges: &types.Money{
									Units:        118,
									CurrencyCode: moneyPkg.RupeeCurrencyCode,
								},
								OrderedAt:         now,
								ChequeLeavesCount: 10,
							},
						},
					},
				}, nil)
			},
			want:    eligibility.DetailedErrorViewScreen(failedRequestState),
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p, md := newProcessorWithMocks(t)
			if tt.mocks != nil {
				tt.mocks(md)
			}
			got, err := p.GetRequestStatus(context.Background(), tt.args.requestId)
			if err != nil || tt.wantErr != nil {
				if !assert.Equal(t, tt.wantErr.Error(), err.Error()) {
					t.Errorf("GetRequestStatus() error = %v, wantErr %v", err.Error(), tt.wantErr.Error())
				}
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&deeplink.Deeplink{}),
			}
			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("GetRequestStatus() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProcessor_GetRequestStatusDetails(t *testing.T) {
	type args struct {
		ctx context.Context
		sr  *alfredPb.ServiceRequest
	}
	var tests = []struct {
		name    string
		args    args
		mocks   func(mock *mockDependencies)
		want    *alfredPb.ServiceRequest
		wantErr error
	}{
		{
			name: "request in success state",
			args: args{
				ctx: context.Background(),
				sr: &alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
					Status:      alfredPb.Status_STATUS_SUCCESS,
					Details: &alfredPb.Details{
						Metadata: &alfredPb.Details_ChequebookMetadata{
							ChequebookMetadata: &alfredPb.ChequebookMetadata{
								OrderedAt:    now,
								CourieredVia: courierPartner,
								TrackingId:   trackingId,
								Charges: &types.Money{
									CurrencyCode: moneyPkg.RupeeCurrencyCode,
									Units:        118,
								},
								ChequeLeavesCount: 10,
							},
						},
					},
					DeletedAtUnix: 9,
				},
			},
			mocks: func(mock *mockDependencies) {
				mock.mockRequestSynchronizer.EXPECT().SyncRequest(gomock.Any(), id1).Return(&alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
					Status:      alfredPb.Status_STATUS_SUCCESS,
					Details: &alfredPb.Details{
						Metadata: &alfredPb.Details_ChequebookMetadata{
							ChequebookMetadata: &alfredPb.ChequebookMetadata{
								OrderedAt:    now,
								CourieredVia: courierPartner,
								TrackingId:   trackingId,
								Charges: &types.Money{
									CurrencyCode: moneyPkg.RupeeCurrencyCode,
									Units:        118,
								},
								ChequeLeavesCount: 10,
							},
						},
					},
					DeletedAtUnix: 9,
				}, epifierrors.ErrRecordNotFound)
			},
			want: &alfredPb.ServiceRequest{
				Id:          id1,
				ActorId:     actor1,
				RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
				Status:      alfredPb.Status_STATUS_SUCCESS,
				Details: &alfredPb.Details{
					Metadata: &alfredPb.Details_ChequebookMetadata{
						ChequebookMetadata: &alfredPb.ChequebookMetadata{
							OrderedAt:    now,
							CourieredVia: courierPartner,
							TrackingId:   trackingId,
							Charges: &types.Money{
								CurrencyCode: moneyPkg.RupeeCurrencyCode,
								Units:        118,
							},
							ChequeLeavesCount: 10,
						},
					},
				},
				DeletedAtUnix: 9,
			},
			wantErr: nil,
		},
		{
			name: "error while syncing the request",
			args: args{
				ctx: context.Background(),
				sr: &alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     actor1,
					RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
					Status:      alfredPb.Status_STATUS_SUCCESS,
					Details: &alfredPb.Details{
						Metadata: &alfredPb.Details_ChequebookMetadata{
							ChequebookMetadata: &alfredPb.ChequebookMetadata{
								OrderedAt:    now,
								CourieredVia: courierPartner,
								TrackingId:   trackingId,
								Charges: &types.Money{
									CurrencyCode: moneyPkg.RupeeCurrencyCode,
									Units:        118,
								},
								ChequeLeavesCount: 10,
							},
						},
					},
					DeletedAtUnix: 9,
				},
			},
			mocks: func(mock *mockDependencies) {
				mock.mockRequestSynchronizer.EXPECT().SyncRequest(gomock.Any(), id1).Return(nil, epifierrors.ErrDowntimeExpected)
			},
			want:    nil,
			wantErr: epifierrors.ErrDowntimeExpected,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p, md := newProcessorWithMocks(t)
			if tt.mocks != nil {
				tt.mocks(md)
			}
			got, err := p.GetRequestStatusDetails(tt.args.ctx, tt.args.sr)
			if err != nil || tt.wantErr != nil {
				if !assert.Equal(t, tt.wantErr.Error(), err.Error()) {
					t.Errorf("GetRequestStatusDetails() error = %v, wantErr %v", err, tt.wantErr)
					return
				}

			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetRequestStatusDetails() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProcessor_GetAllRequestStatusDetails(t *testing.T) {
	type args struct {
		ctx       context.Context
		filters   *alfredPb.Filters
		pageToken *pagination.PageToken
		pageSize  uint32
		sortOrder alfredPb.SortOrder
	}
	tests := []struct {
		name            string
		args            args
		mocks           func(*mockDependencies, *sync.WaitGroup)
		wantServiceReqs []*alfredPb.ServiceRequest
		wantPageContext *rpc.PageContextResponse
		wantErr         bool
	}{
		{
			name: "request is in progress",
			args: args{
				ctx: context.Background(),
				filters: &alfredPb.Filters{
					ActorId:      actor1,
					RequestTypes: []alfredPb.RequestType{alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK},
					StatusList:   []alfredPb.Status{alfredPb.Status_STATUS_IN_PROGRESS},
				},
				pageSize:  1,
				sortOrder: alfredPb.SortOrder_SORT_ORDER_DESC,
			},
			mocks: func(mock *mockDependencies, wg *sync.WaitGroup) {
				wg.Add(1)
				mock.mockAlfredDao.EXPECT().GetPaginatedRequestDetails(context.Background(), actor1, alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK, []alfredPb.Status{alfredPb.Status_STATUS_IN_PROGRESS}, nil, uint32(1), alfredPb.SortOrder_SORT_ORDER_DESC).
					DoAndReturn(func(ctx context.Context, string2 string, r alfredPb.RequestType, statusList []alfredPb.Status, pg *pagination.PageToken, uint3 uint32, opts ...interface{}) ([]*alfredPb.ServiceRequest, *rpc.PageContextResponse, error) {
						wg.Done()
						return []*alfredPb.ServiceRequest{{
							Id:          id1,
							ActorId:     actor1,
							RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
							Status:      alfredPb.Status_STATUS_IN_PROGRESS,
							Details: &alfredPb.Details{
								Metadata: &alfredPb.Details_ChequebookMetadata{
									ChequebookMetadata: &alfredPb.ChequebookMetadata{
										Charges: &types.Money{
											CurrencyCode: moneyPkg.RupeeCurrencyCode,
											Units:        118,
										},
									},
								},
							},
						}}, nil, nil
					})
				mock.mockRequestSynchronizer.EXPECT().SyncRequest(gomock.Any(), id1).
					Return(&alfredPb.ServiceRequest{
						Id:          id1,
						ActorId:     actor1,
						RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
						Status:      alfredPb.Status_STATUS_IN_PROGRESS,
						Details: &alfredPb.Details{
							Metadata: &alfredPb.Details_ChequebookMetadata{
								ChequebookMetadata: &alfredPb.ChequebookMetadata{
									Charges: &types.Money{
										CurrencyCode: moneyPkg.RupeeCurrencyCode,
										Units:        118,
									},
									OrderedAt:         now,
									ChequeLeavesCount: 10,
								},
							},
						},
					}, nil)
			},
			wantServiceReqs: []*alfredPb.ServiceRequest{{
				Id:          id1,
				ActorId:     actor1,
				RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
				Status:      alfredPb.Status_STATUS_IN_PROGRESS,
				Details: &alfredPb.Details{
					Metadata: &alfredPb.Details_ChequebookMetadata{
						ChequebookMetadata: &alfredPb.ChequebookMetadata{
							OrderedAt:         now,
							ChequeLeavesCount: 10,
							Charges: &types.Money{
								CurrencyCode: moneyPkg.RupeeCurrencyCode,
								Units:        118,
							},
						},
					},
				},
			}},
			wantPageContext: nil,
			wantErr:         false,
		},
		{
			name: "fetch the success and in progress request in descending order",
			args: args{
				ctx: context.Background(),
				filters: &alfredPb.Filters{
					ActorId:      actor1,
					RequestTypes: []alfredPb.RequestType{alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK},
					StatusList:   []alfredPb.Status{alfredPb.Status_STATUS_IN_PROGRESS, alfredPb.Status_STATUS_SUCCESS},
				},
				pageSize:  10,
				sortOrder: alfredPb.SortOrder_SORT_ORDER_DESC,
			},
			mocks: func(mock *mockDependencies, wg *sync.WaitGroup) {
				wg.Add(3)
				mock.mockAlfredDao.EXPECT().GetPaginatedRequestDetails(context.Background(), actor1, alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK, []alfredPb.Status{alfredPb.Status_STATUS_IN_PROGRESS, alfredPb.Status_STATUS_SUCCESS}, nil, uint32(10), alfredPb.SortOrder_SORT_ORDER_DESC).
					Return([]*alfredPb.ServiceRequest{{
						Id:          id1,
						ActorId:     actor1,
						RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
						Status:      alfredPb.Status_STATUS_SUCCESS,
						Details: &alfredPb.Details{
							Metadata: &alfredPb.Details_ChequebookMetadata{
								ChequebookMetadata: &alfredPb.ChequebookMetadata{
									OrderedAt: timestamp.New(now.AsTime().Add(-1 * 24 * 100 * time.Hour)),
								},
							},
						},
						CreatedAt: timestamp.New(now.AsTime().Add(-1 * 24 * 100 * time.Hour)),
					}, {
						Id:          id2,
						ActorId:     actor1,
						RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
						Status:      alfredPb.Status_STATUS_SUCCESS,
						Details: &alfredPb.Details{
							Metadata: &alfredPb.Details_ChequebookMetadata{
								ChequebookMetadata: &alfredPb.ChequebookMetadata{
									OrderedAt: timestamp.New(now.AsTime().Add(-1 * 24 * 50 * time.Hour)),
								},
							},
						},
						CreatedAt: timestamp.New(now.AsTime().Add(-1 * 24 * 50 * time.Hour)),
					}, {
						Id:          id3,
						ActorId:     actor1,
						RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
						Status:      alfredPb.Status_STATUS_IN_PROGRESS,
						Details:     &alfredPb.Details{},
						CreatedAt:   timestamp.New(now.AsTime().Add(-1 * 24 * time.Hour)),
					}}, nil, nil)
				mock.mockRequestSynchronizer.EXPECT().SyncRequest(gomock.Any(), id1).
					DoAndReturn(func(ctx context.Context, id string) (*alfredPb.ServiceRequest, error) {
						wg.Done()
						return nil, epifierrors.ErrDowntimeExpected
					})
				mock.mockRequestSynchronizer.EXPECT().SyncRequest(gomock.Any(), id2).
					DoAndReturn(func(ctx context.Context, id string) (*alfredPb.ServiceRequest, error) {
						wg.Done()
						return nil, epifierrors.ErrDowntimeExpected
					})
				mock.mockRequestSynchronizer.EXPECT().SyncRequest(gomock.Any(), id3).
					DoAndReturn(func(ctx context.Context, id string) (*alfredPb.ServiceRequest, error) {
						wg.Done()
						return &alfredPb.ServiceRequest{
							Id:          id3,
							ActorId:     actor1,
							RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
							Status:      alfredPb.Status_STATUS_IN_PROGRESS,
							Details:     &alfredPb.Details{},
							CreatedAt:   timestamp.New(now.AsTime().Add(-1 * 24 * time.Hour)),
						}, nil
					})
			},
			wantServiceReqs: []*alfredPb.ServiceRequest{{
				Id:          id2,
				ActorId:     actor1,
				RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
				Status:      alfredPb.Status_STATUS_SUCCESS,
				Details: &alfredPb.Details{
					Metadata: &alfredPb.Details_ChequebookMetadata{
						ChequebookMetadata: &alfredPb.ChequebookMetadata{
							OrderedAt: timestamp.New(now.AsTime().Add(-1 * 24 * 50 * time.Hour)),
						},
					},
				},
				CreatedAt: timestamp.New(now.AsTime().Add(-1 * 24 * 50 * time.Hour)),
			}, {
				Id:          id1,
				ActorId:     actor1,
				RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
				Status:      alfredPb.Status_STATUS_SUCCESS,
				Details: &alfredPb.Details{
					Metadata: &alfredPb.Details_ChequebookMetadata{
						ChequebookMetadata: &alfredPb.ChequebookMetadata{
							OrderedAt: timestamp.New(now.AsTime().Add(-1 * 24 * 100 * time.Hour)),
						},
					},
				},
				CreatedAt: timestamp.New(now.AsTime().Add(-1 * 24 * 100 * time.Hour)),
			}},
			wantPageContext: nil,
			wantErr:         false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wg := &sync.WaitGroup{}
			p, md := newProcessorWithMocks(t)
			if tt.mocks != nil {
				tt.mocks(md, wg)
			}
			got, got1, err := p.GetAllRequestStatusDetails(tt.args.ctx, tt.args.filters, tt.args.pageToken, tt.args.pageSize, tt.args.sortOrder)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllRequestStatusDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.wantServiceReqs) {
				t.Errorf("GetAllRequestStatusDetails() got = %v, want %v", got, tt.wantServiceReqs)
			}
			if !reflect.DeepEqual(got1, tt.wantPageContext) {
				t.Errorf("GetAllRequestStatusDetails() got1 = %v, want %v", got1, tt.wantPageContext)
			}
		})
	}
}
