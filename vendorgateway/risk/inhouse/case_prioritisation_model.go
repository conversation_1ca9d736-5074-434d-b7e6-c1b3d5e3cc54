package inhouse

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"

	cmPb "github.com/epifi/gamma/api/risk/case_management"
	riskPb "github.com/epifi/gamma/api/vendorgateway/risk"
	inhousePb "github.com/epifi/gamma/api/vendors/inhouse"
	genConf "github.com/epifi/gamma/vendorgateway/config/genconf"
)

type CasePrioritisationModelReq struct {
	Req  *riskPb.GetCasePrioritisationScoreRequest
	Url  string
	Conf *genConf.CasePrioritisationModelConfig
}

type CasePrioritisationModelRes struct {
	conf *genConf.CasePrioritisationModelConfig
}

func (r *CasePrioritisationModelReq) Marshal() ([]byte, error) {
	var alerts []*inhousePb.GetCasePrioritisationScoreRequest_AlertWithRuleDetails
	for _, alertWithRule := range r.Req.GetAlertWithRuleDetails() {
		vendorAlert := r.mapAlert(alertWithRule.GetAlert())
		var vendorRule *inhousePb.GetCasePrioritisationScoreRequest_Rule
		var vendorExtendedRule *inhousePb.GetCasePrioritisationScoreRequest_ExtendedRule
		if alertWithRule.GetExtendedRule() != nil {
			vendorRule = r.mapRule(alertWithRule.GetExtendedRule().GetRule())
			vendorExtendedRule = r.mapExtendedRule(alertWithRule.GetExtendedRule())
		}

		alerts = append(alerts, &inhousePb.GetCasePrioritisationScoreRequest_AlertWithRuleDetails{
			Alert:        vendorAlert,
			Rule:         vendorRule,
			ExtendedRule: vendorExtendedRule,
		})
	}

	// Case Details addition - only initialize if original is not nil
	var caseDetails *inhousePb.GetCasePrioritisationScoreRequest_CaseDetails
	if r.Req.GetCaseDetails() != nil {
		caseDetails = &inhousePb.GetCasePrioritisationScoreRequest_CaseDetails{}
		caseDetails.CaseId = r.Req.GetCaseDetails().GetCaseId()
		for _, caseAlertWithRule := range r.Req.GetCaseDetails().GetAlertWithRuleDetails() {
			vendorAlert := r.mapAlert(caseAlertWithRule.GetAlert())
			var vendorRule *inhousePb.GetCasePrioritisationScoreRequest_Rule
			var vendorExtendedRule *inhousePb.GetCasePrioritisationScoreRequest_ExtendedRule
			if caseAlertWithRule.GetExtendedRule() != nil {
				vendorRule = r.mapRule(caseAlertWithRule.GetExtendedRule().GetRule())
				vendorExtendedRule = r.mapExtendedRule(caseAlertWithRule.GetExtendedRule())
			}

			caseDetails.AlertWithRuleDetails = append(caseDetails.AlertWithRuleDetails, &inhousePb.GetCasePrioritisationScoreRequest_AlertWithRuleDetails{
				Alert:        vendorAlert,
				Rule:         vendorRule,
				ExtendedRule: vendorExtendedRule,
			})
		}
	}

	vendorReq := &inhousePb.GetCasePrioritisationScoreRequest{
		ActorId:              r.Req.GetActorId(),
		RequestId:            r.Req.GetRequestId(),
		AlertWithRuleDetails: alerts,
		ModelVersion:         r.Req.GetModelVersion(),
		CaseDetails:          caseDetails,
	}

	bytes, err := json.Marshal(vendorReq)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal req to vendor: %w", err)
	}
	return bytes, nil
}

// mapAlert converts vendorgateway Alert to vendor Alert format
func (r *CasePrioritisationModelReq) mapAlert(alert *cmPb.Alert) *inhousePb.GetCasePrioritisationScoreRequest_Alert {
	if alert == nil {
		return nil
	}

	vendorAlert := &inhousePb.GetCasePrioritisationScoreRequest_Alert{
		Id:            alert.GetId(),
		CaseId:        alert.GetCaseId(),
		RuleId:        alert.GetRuleId(),
		BatchName:     alert.GetBatchName(),
		ActorId:       alert.GetActorId(),
		AccountId:     alert.GetAccountId(),
		EntityType:    alert.GetEntityType().String(),
		EntityId:      alert.GetEntityId(),
		Verdict:       alert.GetVerdict().String(),
		AccountType:   alert.GetAccountType().String(),
		HandlingType:  alert.GetHandlingType().String(),
		RulePrecision: alert.GetRulePrecision(),
	}

	// Handle timestamps
	if alert.GetCreatedAt() != nil {
		vendorAlert.CreatedAt = alert.GetCreatedAt().AsTime().Format("2006-01-02T15:04:05Z07:00")
	}
	if alert.GetUpdatedAt() != nil {
		vendorAlert.UpdatedAt = alert.GetUpdatedAt().AsTime().Format("2006-01-02T15:04:05Z07:00")
	}
	if alert.GetInitiatedAt() != nil {
		vendorAlert.InitiatedAt = alert.GetInitiatedAt().AsTime().Format("2006-01-02T15:04:05Z07:00")
	}

	// Handle repeated fields
	for _, reason := range alert.GetHandlingReasons() {
		vendorAlert.HandlingReasons = append(vendorAlert.HandlingReasons, reason.String())
	}

	// Handle meta details
	if alert.GetMetaDetails() != nil {
		vendorAlert.MetaDetails = r.mapAlertMetaDetails(alert.GetMetaDetails())
	}

	return vendorAlert
}

// mapAlertMetaDetails converts Alert MetaDetails
func (r *CasePrioritisationModelReq) mapAlertMetaDetails(metaDetails *cmPb.AlertMetaDetails) *inhousePb.GetCasePrioritisationScoreRequest_AlertMetaDetails {
	if metaDetails == nil {
		return nil
	}

	vendorMetaDetails := &inhousePb.GetCasePrioritisationScoreRequest_AlertMetaDetails{}

	for _, txnBlock := range metaDetails.GetTransactionBlocks() {
		vendorTxnBlock := &inhousePb.GetCasePrioritisationScoreRequest_TransactionBlock{
			Id:               txnBlock.GetId(),
			ActorId:          txnBlock.GetActorId(),
			AlertId:          txnBlock.GetAlertId(),
			AggregatedCredit: txnBlock.GetAggregatedCredit(),
			AggregatedDebit:  txnBlock.GetAggregatedDebit(),
			DurationSeconds:  txnBlock.GetDuration().GetSeconds(),
			BlockType:        txnBlock.GetBlockType().String(),
			TransactionIds:   txnBlock.GetTransactionIds(),
			DeletedAtUnix:    txnBlock.GetDeletedAtUnix(),
		}

		// Handle timestamps for transaction block
		if txnBlock.GetCreatedAt() != nil {
			vendorTxnBlock.CreatedAt = txnBlock.GetCreatedAt().AsTime().Format("2006-01-02T15:04:05Z07:00")
		}
		if txnBlock.GetUpdatedAt() != nil {
			vendorTxnBlock.UpdatedAt = txnBlock.GetUpdatedAt().AsTime().Format("2006-01-02T15:04:05Z07:00")
		}

		vendorMetaDetails.TransactionBlocks = append(vendorMetaDetails.TransactionBlocks, vendorTxnBlock)
	}

	return vendorMetaDetails
}

// mapRule converts vendorgateway Rule to vendor Rule format
func (r *CasePrioritisationModelReq) mapRule(rule *cmPb.Rule) *inhousePb.GetCasePrioritisationScoreRequest_Rule {
	if rule == nil {
		return nil
	}

	vendorRule := &inhousePb.GetCasePrioritisationScoreRequest_Rule{
		Id:                    rule.GetId(),
		Name:                  rule.GetName(),
		Version:               rule.GetVersion(),
		Description:           rule.GetDescription(),
		EvaluationMethod:      rule.GetEvaluationMethod().String(),
		Provenance:            rule.GetProvenance().String(),
		State:                 rule.GetState().String(),
		ExternalId:            rule.GetExternalId(),
		AssessedEntityType:    rule.GetAssessedEntityType().String(),
		TxnSuspectEntity:      rule.GetTxnSuspectEntity().String(),
		RuleGroup:             rule.GetRuleGroup().String(),
		AddedByEmail:          rule.GetAddedByEmail(),
		SeedPrecision:         rule.GetSeedPrecision(),
		ForceUseSeedPrecision: rule.GetForceUseSeedPrecision(),
		Tags:                  rule.GetTags(),
	}

	// Handle timestamps
	if rule.GetCreatedAt() != nil {
		vendorRule.CreatedAt = rule.GetCreatedAt().AsTime().Format("2006-01-02T15:04:05Z07:00")
	}
	if rule.GetUpdatedAt() != nil {
		vendorRule.UpdatedAt = rule.GetUpdatedAt().AsTime().Format("2006-01-02T15:04:05Z07:00")
	}
	if rule.GetDeletedAt() != nil {
		vendorRule.DeletedAt = rule.GetDeletedAt().AsTime().Format("2006-01-02T15:04:05Z07:00")
	}

	return vendorRule
}

// mapExtendedRule converts vendorgateway ExtendedRule to vendor ExtendedRule format
func (r *CasePrioritisationModelReq) mapExtendedRule(extendedRule *cmPb.ExtendedRule) *inhousePb.GetCasePrioritisationScoreRequest_ExtendedRule {
	if extendedRule == nil {
		return nil
	}

	vendorExtendedRule := &inhousePb.GetCasePrioritisationScoreRequest_ExtendedRule{
		Rule: r.mapRule(extendedRule.GetRule()),
	}

	// Map review type details
	for _, reviewTypeDetail := range extendedRule.GetReviewTypeDetails() {
		vendorReviewTypeDetail := &inhousePb.GetCasePrioritisationScoreRequest_ReviewTypeDetails{
			ReviewType: reviewTypeDetail.GetReviewType().String(),
		}
		vendorExtendedRule.ReviewTypeDetails = append(vendorExtendedRule.ReviewTypeDetails, vendorReviewTypeDetail)
	}

	return vendorExtendedRule
}

func (r *CasePrioritisationModelReq) HTTPMethod() string {
	return http.MethodPost
}

func (r *CasePrioritisationModelReq) URL() string {
	return r.Url
}

func (r *CasePrioritisationModelRes) Unmarshal(b []byte) (proto.Message, error) {
	// First unmarshal into the vendor's protobuf response
	vendorRes := &inhousePb.GetCasePrioritisationScoreResponse{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(b, vendorRes)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal vendor response %w", err)
	}
	logger.Info(context.Background(), "Raw response received: ", zap.String("raw_json", string(b)))

	// Create the final response using the protobuf message type
	res := &riskPb.GetCasePrioritisationScoreResponse{
		Status:       rpc.StatusOk(),
		RequestId:    vendorRes.GetRequestId(),
		Score:        vendorRes.GetScore(),
		ModelVersion: vendorRes.GetModelVersion(),
		ModelInfo: func() []*riskPb.ModelResponseInfo {
			var out []*riskPb.ModelResponseInfo
			for _, m := range vendorRes.GetModelInfo() {
				out = append(out, &riskPb.ModelResponseInfo{
					Name:  m.GetName(),
					Score: m.GetScore(),
				})
			}
			return out
		}(),
		RawVendorResponse: string(b),
	}
	return res, nil
}

func (r *CasePrioritisationModelReq) GetResponse() vendorapi.Response {
	return &CasePrioritisationModelRes{
		conf: r.Conf,
	}
}

func (r *CasePrioritisationModelRes) HandleHttpError(ctx context.Context, httpStatusCode int, responseBody []byte) (proto.Message, error) {
	logger.Error(ctx, "failure in getting case prioritisation score", zap.Int(logger.HTTP_STATUS, httpStatusCode))
	return &riskPb.GetCasePrioritisationScoreResponse{
		Status:            vendorapi.GetRpcStatusFromHttpCode(ctx, httpStatusCode),
		RawVendorResponse: string(responseBody),
	}, nil
}
