package ticket

import (
	"bytes"
	"context"
	"fmt"
	"math"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"github.com/golang/protobuf/jsonpb"
	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"

	crmPb "github.com/epifi/gamma/api/vendorgateway/crm"
	crmErr "github.com/epifi/gamma/api/vendorgateway/crm/errors"
	riskPb "github.com/epifi/gamma/api/vendorgateway/crm/risk"
	vendorFD "github.com/epifi/gamma/api/vendors/crm/freshdesk"
)

const (
	// FreshdeskResultPerPage Freshdesk does not support per page parameter in filter query, page size is fixed
	FreshdeskResultPerPage = 30
	FreshdeskMaxQueryLen   = 512
	FreshdeskDateLayout    = datetime.DATE_LAYOUT_YYYYMMDD
)

type GetTicketListReq struct {
	Method    string
	Req       *crmPb.GetTicketListRequest
	Url       string
	ApiKey    string
	CrmConfig *cfg.Crm
	Env       string
}

// Marshal provides the json of request data for API call
func (r *GetTicketListReq) Marshal() ([]byte, error) {
	return []byte{}, nil
}

// URL provides the URL to send the request to
func (r *GetTicketListReq) URL() string {
	reqData := url.Values{}

	_, page := getPaginationParams(r.Req.PageContext, FreshdeskResultPerPage)
	reqData.Set("page", page)

	filterQuery := `"` + getFilterQuery(r.Req, r.CrmConfig) + `"`
	reqData.Set("query", filterQuery)

	return fmt.Sprintf(`%s?%s`, r.Url, reqData.Encode())
}

// HTTPMethod returns the http method to use for the API call.
func (r *GetTicketListReq) HTTPMethod() string {
	return r.Method
}

func (r *GetTicketListReq) SetAuth(httpReq *http.Request) *http.Request {
	httpReq.SetBasicAuth(r.ApiKey, "")
	return httpReq
}

func getRiskFiltersQuery(filter *riskPb.RiskFilters, crmConfig *cfg.Crm) string {
	var query strings.Builder

	insertCondition := func(end bool, q string) {
		query.WriteString(q)
		if end {
			query.WriteString(" ) ")
		} else {
			query.WriteString(" OR ")
		}
	}

	insertBeforeField := func(fieldLen int) {
		if fieldLen != 0 && query.Len() != 0 {
			query.WriteString(" AND ")
		}
		if fieldLen != 0 {
			query.WriteString(" ( ")
		}
	}

	insertBeforeField(len(filter.GetPriorities()))
	for i, val := range filter.GetPriorities() {
		q := fmt.Sprintf("priority:%d", crmConfig.Freshdesk.Risk.PriorityEnumToValueMapping[val.String()])
		insertCondition(i == len(filter.GetPriorities())-1, q)
	}

	insertBeforeField(len(filter.GetReviewTypes()))
	for i, val := range filter.GetReviewTypes() {
		q := fmt.Sprintf("cf_review_type:'%s'", crmConfig.Freshdesk.Risk.ReviewTypeEnumToValueMapping[val.String()])
		insertCondition(i == len(filter.GetReviewTypes())-1, q)
	}

	insertBeforeField(len(filter.GetStatuses()))
	for i, val := range filter.GetStatuses() {
		q := fmt.Sprintf("status:%d", crmConfig.Freshdesk.Risk.StatusEnumToValueMapping[val.String()])
		insertCondition(i == len(filter.GetStatuses())-1, q)
	}

	insertBeforeField(len(filter.GetTags()))
	for i, val := range filter.GetTags() {
		q := fmt.Sprintf("tag:'%s'", val)
		insertCondition(i == len(filter.GetTags())-1, q)
	}

	query.WriteString(getAgentFilterQuery(query.Len() == 0, filter.GetAgentId()))

	query.WriteString(getSnoozedFilterQuery(query.Len() == 0, filter.GetSnoozed()))

	query.WriteString(getConfidenceScoreRangeFilterQuery(query.Len() == 0, filter.GetConfidenceScoreRange()))

	query.WriteString(getModel1ScoreRangeFilterQuery(query.Len() == 0, filter.GetModel1ScoreRange()))

	query.WriteString(getModel2ScoreRangeFilterQuery(query.Len() == 0, filter.GetModel2ScoreRange()))

	query.WriteString(getCreatedAtRangeFilterQuery(query.Len() == 0, filter.GetCreatedAtRange()))

	query.WriteString(getUpdatedAtRangeFilterQuery(query.Len() == 0, filter.GetUpdatedAtRange()))

	logger.InfoNoCtx("Processed Filters sent to freshdesk:", zap.Any("filters", filter), zap.Any("crmConfig", crmConfig), zap.Any("query", query.String()), zap.Any("rawQuery", query))

	return query.String()
}

func getAgentFilterQuery(firstFilter bool, agentId uint64) string {
	var query strings.Builder
	if !firstFilter {
		query.WriteString("AND ")
	}
	switch {
	case agentId == 0:
		query.WriteString("(agent_id:null)")
	case agentId != 0:
		query.WriteString(fmt.Sprintf("(agent_id:%d)", agentId))
	}
	return query.String()
}

func getSnoozedFilterQuery(firstFilter bool, snoozed commontypes.BooleanEnum) string {
	var query strings.Builder
	if snoozed != commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED && !firstFilter {
		query.WriteString(" AND ")
	}
	if snoozed == commontypes.BooleanEnum_FALSE {
		query.WriteString(fmt.Sprintf("(( cf_snoozed_till:<%d ) OR ( cf_snoozed_till:null))", timestamp.Now().GetSeconds()))
	}
	if snoozed == commontypes.BooleanEnum_TRUE {
		query.WriteString(fmt.Sprintf("( cf_snoozed_till:>%d )", timestamp.Now().GetSeconds()))
	}
	return query.String()
}

func getConfidenceScoreRangeFilterQuery(firstFilter bool, confidenceRange *riskPb.NumericRange) string {
	if confidenceRange == nil {
		return ""
	}

	var query strings.Builder
	if !firstFilter {
		query.WriteString(" AND ")
	}
	query.WriteString("(")
	switch {
	case confidenceRange.GetLower() < 0:
		query.WriteString(fmt.Sprintf(" (cf_confidence_score:null) OR ((cf_confidence_score:>%d)", int(confidenceRange.GetLower())))
	default:
		query.WriteString(fmt.Sprintf("((cf_confidence_score:>%d)", int(confidenceRange.GetLower())))
	}
	query.WriteString(fmt.Sprintf(" AND (cf_confidence_score:<%d OR cf_confidence_score:%d))", int(confidenceRange.GetUpper()), int(confidenceRange.GetUpper())))
	query.WriteString(")")
	return query.String()
}

//nolint:dupl
func getModel1ScoreRangeFilterQuery(firstFilter bool, model1ScoreRange *riskPb.NumericRange) string {
	if model1ScoreRange == nil {
		return ""
	}

	var query strings.Builder
	if !firstFilter {
		query.WriteString(" AND ")
	}
	query.WriteString("(")
	switch {
	case model1ScoreRange.GetLower() < 0:
		query.WriteString(fmt.Sprintf(" (cf_model1_score:null) OR ((cf_model1_score:>%d)", int(model1ScoreRange.GetLower())))
	default:
		query.WriteString(fmt.Sprintf("((cf_model1_score:>%d)", int(model1ScoreRange.GetLower())))
	}
	query.WriteString(fmt.Sprintf(" AND (cf_model1_score:<%d OR cf_model1_score:%d))", int(model1ScoreRange.GetUpper()), int(model1ScoreRange.GetUpper())))
	query.WriteString(")")
	return query.String()
}

//nolint:dupl
func getModel2ScoreRangeFilterQuery(firstFilter bool, model2ScoreRange *riskPb.NumericRange) string {
	if model2ScoreRange == nil {
		return ""
	}

	var query strings.Builder
	if !firstFilter {
		query.WriteString(" AND ")
	}
	query.WriteString("(")
	switch {
	case model2ScoreRange.GetLower() < 0:
		query.WriteString(fmt.Sprintf(" (cf_model2_score:null) OR ((cf_model2_score:>%d)", int(model2ScoreRange.GetLower())))
	default:
		query.WriteString(fmt.Sprintf("((cf_model2_score:>%d)", int(model2ScoreRange.GetLower())))
	}
	query.WriteString(fmt.Sprintf(" AND (cf_model2_score:<%d OR cf_model2_score:%d))", int(model2ScoreRange.GetUpper()), int(model2ScoreRange.GetUpper())))
	query.WriteString(")")
	return query.String()
}

func getCreatedAtRangeFilterQuery(firstFilter bool, createdAtRange *riskPb.TimestampRange) string {
	if createdAtRange == nil {
		return ""
	}

	var query strings.Builder
	if !firstFilter {
		query.WriteString(" AND ")
	}
	query.WriteString(fmt.Sprintf("(cf_created_at_unix:>%d AND cf_created_at_unix:<%d)",
		createdAtRange.GetFrom().GetSeconds(), createdAtRange.GetTo().GetSeconds()))
	return query.String()
}

func getUpdatedAtRangeFilterQuery(firstFilter bool, updatedAtRange *riskPb.TimestampRange) string {
	if updatedAtRange == nil {
		return ""
	}

	var query strings.Builder
	if !firstFilter {
		query.WriteString(" AND ")
	}
	query.WriteString(fmt.Sprintf("(updated_at:>'%s' AND updated_at:<'%s')",
		updatedAtRange.GetFrom().AsTime().Format("2006-01-02"),
		updatedAtRange.GetTo().AsTime().Format("2006-01-02")))
	return query.String()
}

func getFilterQuery(r *crmPb.GetTicketListRequest, CrmConfig *cfg.Crm) string {
	switch r.GetFilters().(type) {
	case *crmPb.GetTicketListRequest_RiskFilters:
		q := getRiskFiltersQuery(r.GetRiskFilters(), CrmConfig)
		return q
	}
	return ""
}

// TODO: Move this to VGTicketFactory monorail: https://monorail.pointz.in/p/fi-app/issues/detail?id=40593
func ValidateGetTicketListRequest(r *crmPb.GetTicketListRequest, CrmConfig *cfg.Crm) error {
	switch r.GetFilters().(type) {
	case *crmPb.GetTicketListRequest_RiskFilters:
		if r.GetCrmRequestHeader().GetUseCase() != crmPb.UseCase_USE_CASE_RISK_CASE_MANAGEMENT {
			return fmt.Errorf("ticket filter type and use case mismatch %w", crmErr.InvalidTicketForUseCaseErr)
		}
		query := getRiskFiltersQuery(r.GetRiskFilters(), CrmConfig)
		if len(query) == 0 || len(url.QueryEscape(query)) > FreshdeskMaxQueryLen {
			return fmt.Errorf("query length not in limit %w", crmErr.MandatoryFieldMissingErr)
		}
	default:
		return fmt.Errorf("unsupported filter type %w", crmErr.InvalidMessageErr)
	}
	return nil
}

func getPaginationParams(pageContextReq *rpc.PageContextRequest, maxTicketsPerPage int) (string, string) {
	// Freshdesk does not supper per page parameter in filter ticket request
	perPage := fmt.Sprintf("%d", int(math.Min(math.Max(1, float64(pageContextReq.GetPageSize())), float64(maxTicketsPerPage))))
	var page string
	switch pageContextReq.GetToken().(type) {
	case *rpc.PageContextRequest_BeforeToken:
		page = pageContextReq.GetBeforeToken()
	case *rpc.PageContextRequest_AfterToken:
		page = pageContextReq.GetAfterToken()
	}
	pageInt, err := strconv.ParseUint(page, 10, 32)
	if err != nil {
		logger.ErrorNoCtx("Unknown format for Page Context token", zap.String("page_no", page))
		return perPage, "1"
	}
	// Freshdesk page number starts from 1
	page = strconv.Itoa(int(math.Max(1.0, float64(pageInt))))
	return perPage, page
}

type GetTicketListResp struct {
	CrmConfig      *cfg.Crm
	Env            string
	UseCase        crmPb.UseCase
	Vendor         commonvgpb.Vendor
	PageContextReq *rpc.PageContextRequest
}

func (r *GetTicketListReq) GetResponse() vendorapi.Response {
	return &GetTicketListResp{
		CrmConfig:      r.CrmConfig,
		Env:            r.Env,
		UseCase:        r.Req.GetCrmRequestHeader().GetUseCase(),
		Vendor:         r.Req.GetHeader().GetVendor(),
		PageContextReq: r.Req.PageContext,
	}
}

func (r *GetTicketListResp) Unmarshal(b []byte) (proto.Message, error) {
	var ticketList []*crmPb.Ticket
	vendorResponse := &vendorFD.GetTicketListResponse{}

	unmarshaler := jsonpb.Unmarshaler{
		AllowUnknownFields: true,
	}

	if err := unmarshaler.Unmarshal(bytes.NewReader(b), vendorResponse); err != nil {
		logger.ErrorNoCtx("failed to unmarshal response", zap.Error(err))
		return &crmPb.GetTicketListResponse{Status: rpc.StatusInternal()}, nil
	}
	for _, vendorTicket := range vendorResponse.GetResults() {
		ticket, err := crmPb.FromVendorMessage(r.UseCase, vendorTicket, r.CrmConfig, r.Vendor)
		if err != nil {
			logger.ErrorNoCtx("failed to parse freshdesk ticket to ticket object", zap.Error(err))
			return &crmPb.GetTicketListResponse{Status: rpc.StatusInternal()}, nil
		}
		ticketList = append(ticketList, ticket)
	}
	pageContextResponse := getPaginationResponse(len(ticketList), FreshdeskResultPerPage, r.PageContextReq)
	return &crmPb.GetTicketListResponse{Status: rpc.StatusOk(), Tickets: ticketList, PageContext: pageContextResponse}, nil
}

func getPaginationResponse(resTicketsCount, maxTickets int, req *rpc.PageContextRequest) *rpc.PageContextResponse {
	response := &rpc.PageContextResponse{}
	perPage, page := getPaginationParams(req, maxTickets)
	pageInt, _ := strconv.ParseInt(page, 10, 0)
	perPageInt, _ := strconv.ParseInt(perPage, 10, 0)
	if pageInt == 1 {
		response.HasBefore = false
		response.BeforeToken = ""
	} else {
		response.HasBefore = true
		response.BeforeToken = strconv.Itoa(int(pageInt - 1))
	}
	if int64(resTicketsCount) < perPageInt {
		response.HasAfter = false
		response.AfterToken = ""
	} else {
		response.HasAfter = true
		response.AfterToken = strconv.Itoa(int(pageInt + 1))
	}
	return response
}

//nolint:dupl
func (r *GetTicketListResp) HandleHttpError(ctx context.Context, httpStatus int, b []byte) (proto.Message, error) {
	logger.Error(ctx, "error in get ticket list call", zap.Int("get ticket list error", httpStatus))
	return &crmPb.GetTicketListResponse{
		Status: vendorapi.GetRpcStatusFromHttpCode(ctx, httpStatus),
	}, nil
}
