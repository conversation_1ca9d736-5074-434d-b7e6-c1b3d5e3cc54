package moneyview

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/protobuf/encoding/protojson"

	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/vendorapi"
	moneyviewVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/moneyview"
	vendorMoneyviewPb "github.com/epifi/gamma/api/vendors/moneyview"
)

type GetCreateMvBlackBoxOfferRequest struct {
	BaseUrl string
	Req     *moneyviewVgPb.GenerateMvBlackBoxOfferRequest
}

type GetCreateMvBlackBoxOfferResponse struct {
}

func (r *GetCreateMvBlackBoxOfferRequest) HTTPMethod() string {
	return http.MethodPost
}

// todo need to change
func (r *GetCreateMvBlackBoxOfferRequest) URL() string {
	return r.BaseUrl + "blackbox/api/score/bb/getOfferV3"
}

func (r *GetCreateMvBlackBoxOfferRequest) GetResponse() vendorapi.Response {
	return &GetCreateMvBlackBoxOfferResponse{}
}

func (r *GetCreateMvBlackBoxOfferRequest) Marshal() ([]byte, error) {
	req := r.Req

	declaredIncome, _ := money.ToDecimal(r.Req.GetDeclaredIncome()).Float64()

	vendorReq := &vendorMoneyviewPb.GenerateMvBlackBoxOfferVendorRequest{
		LeadUserRef:    req.GetLeadUserRef(),
		LeadSource:     req.GetLeadSource(),
		DataFormat:     req.GetDataFormat(),
		Pincode:        req.GetPincode(),
		EmploymentType: empTypeEnumToStringMap[r.Req.GetEmploymentType()],
		DeclaredIncome: float64(declaredIncome),
		Dob:            datetime.DateToTime(r.Req.GetDob(), datetime.IST).Format(datetime.DATE_LAYOUT_YYYYMMDD),
		BureauProvider: req.GetBureauProvider(),
		PkgInfoData:    getPkgInfoDataFromRequest(req.GetPkgInfoData()),
		SmsData:        getSmsDataFromRequest(req.GetSmsData()),
		BureauDataStr:  req.GetRawBureauReport(),
	}
	return protojson.Marshal(vendorReq)

}

func (g GetCreateMvBlackBoxOfferResponse) Unmarshal(b []byte) (proto.Message, error) {
	response := &vendorMoneyviewPb.GenerateMvBlackBoxOfferVendorResponse{}
	unmarshaller := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err := unmarshaller.Unmarshal(b, response); err != nil {
		return nil, fmt.Errorf("error unmarshalling get lead status api response, err : %w", err)
	}

	return &moneyviewVgPb.GenerateMvBlackBoxOfferResponse{
		OfferDetails: &moneyviewVgPb.OfferDetails{
			LeadUserRef:      response.GetBody().GetLeadUserRef(),
			Status:           response.GetBody().GetStatus(),
			BestOfferAmount:  response.GetBody().GetBestOfferAmount(),
			BestOfferTenure:  response.GetBody().GetBestOfferTenure(),
			BestOfferRoi:     response.GetBody().GetBestOfferRoi(),
			ExpiryDate:       response.GetBody().GetExpiryDate(),
			Category:         response.GetBody().GetExpiryDate(),
			LeadSource:       response.GetBody().GetLeadSource(),
			MetaIncome:       response.GetBody().GetMetaIncome(),
			MetaTagName:      response.GetBody().GetMetaTagName(),
			EmploymentType:   response.GetBody().GetEmploymentType(),
			SummaryVariables: response.GetBody().GetSummaryVariables(),
		},
		Message: response.GetMessage(),
		Status:  rpc.StatusOk(),
	}, nil

}

func (r *GetCreateMvBlackBoxOfferRequest) RedactRequestBody(ctx context.Context, requestBody []byte, contentType string) ([]byte, error) {
	return nil, nil
}

func (r *GetCreateMvBlackBoxOfferResponse) RedactResponseBody(_ context.Context, b []byte, _ string) ([]byte, error) {
	return b, nil
}

func getSmsDataFromRequest(data *moneyviewVgPb.SmsData) *vendorMoneyviewPb.SmsData {
	smsData := &vendorMoneyviewPb.SmsData{}

	for _, sms := range data.GetSmsList() {
		smsData.SmsList = append(smsData.SmsList, &vendorMoneyviewPb.Sms{
			Msg:     sms.GetMsg(),
			Address: sms.GetAddress(),
			Date:    getEpochFromDate(sms.GetDate()),
		})
	}
	return smsData
}

func getPkgInfoDataFromRequest(data *moneyviewVgPb.PkgInfoData) *vendorMoneyviewPb.PkgInfoData {
	pkgInfoData := &vendorMoneyviewPb.PkgInfoData{}

	for _, pkgInfo := range data.GetPkgInfoDtoList() {
		pkgInfoData.PkgInfoDTOList = append(pkgInfoData.PkgInfoDTOList, &vendorMoneyviewPb.PkgInfo{
			PkgName:     pkgInfo.GetPkgName(),
			UserId:      pkgInfo.GetUserId(),
			DateCreated: getEpochFromDate(pkgInfo.GetDateCreated()),
		})
	}
	return pkgInfoData
}

func getEpochFromDate(d *date.Date) int64 {
	t := time.Date(
		int(d.Year),
		time.Month(d.Month),
		int(d.Day),
		0, 0, 0, 0, // Hour, minute, second, nanosecond
		time.UTC, // Use UTC for a standard epoch conversion
	)
	return t.Unix()
}
