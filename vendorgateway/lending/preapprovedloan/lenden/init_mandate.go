package lenden

import (
	"context"
	"fmt"
	"net/http"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/rpc/code"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"
	lendenPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
	vendorLendenPb "github.com/epifi/gamma/api/vendors/lenden"
	"github.com/epifi/gamma/vendorgateway/config"
)

// InitMandateRequest represents the request structure for initializing a mandate.
type InitMandateRequest struct {
	Req  *lendenPb.InitMandateRequest
	Conf *config.Lenden
	Cryptor
	LogRedactor
}

// InitMandateResponse represents the response structure for initializing a mandate.
type InitMandateResponse struct {
	Conf *config.Lenden
	Cryptor
	LogRedactor
	mandateType lendenPb.MandateType
}

func (c *InitMandateRequest) URL() string {
	return c.Conf.BaseUrl
}

func (c *InitMandateRequest) HTTPMethod() string {
	return http.MethodPost
}

func (c *InitMandateRequest) GetResponse() vendorapi.Response {
	return &InitMandateResponse{
		Conf:        c.Conf,
		Cryptor:     c.Cryptor,
		LogRedactor: c.LogRedactor,
		mandateType: c.Req.GetMandateType(),
	}
}

//nolint:dupl
func (c *InitMandateRequest) Marshal() ([]byte, error) {
	consentCodeList, err := getLendenConsentTypeStringList(c.Req.GetConsentCodeList())
	if err != nil {
		return nil, err
	}

	mandateType, err := getMandateTypeString(c.Req.GetMandateType())
	if err != nil {
		return nil, err
	}

	requestPayload := &vendorLendenPb.InitMandateRequest{
		Params: &vendorLendenPb.Params{
			// Required To be passed empty, as it's a mandatory field from LDC
		},
		Fields: &vendorLendenPb.Fields{
			// Required To be passed empty, as it's a mandatory field from LDC
		},
		Json: &vendorLendenPb.InitMandateRequestPayload{
			ProductId: c.Conf.ProductId,
			UserId:    c.Req.GetUserId(),
			LoanId:    c.Req.GetLoanId(),
			Type:      mandateType,
			ConsentData: &vendorLendenPb.ConsentDataRequest{
				CodeList:   consentCodeList,
				UserIp:     c.Req.GetUserIp(),
				DeviceId:   c.Req.GetDeviceId(),
				Latitude:   c.Req.GetLatitude(),
				Longitude:  c.Req.GetLongitude(),
				ConsentDtm: getConsentDTMString(c.Req.GetConsentTime()),
			},
		},
		Attributes: &vendorLendenPb.Attributes{
			Authorization: c.Conf.Authorization,
		},
		ApiCode: string(InternalApiCodeInitMandate),
	}

	// Step 2: Marshal the VendorRequest into JSON
	vendorReqBytes, err := protojson.Marshal(requestPayload)
	if err != nil {
		return nil, fmt.Errorf("error marshaling vendor request: %w", err)
	}

	// Step 3: Encrypt the JSON request
	encryptedReqBytes, err := c.Cryptor.Encrypt(vendorReqBytes)
	if err != nil {
		return nil, fmt.Errorf("error encrypting vendor request: %w", err)
	}

	// Step 4: Return the encrypted bytes
	return encryptedReqBytes, nil
}

//nolint:dupl
func (c *InitMandateResponse) Unmarshal(b []byte) (proto.Message, error) {
	unmarshaller := protojson.UnmarshalOptions{DiscardUnknown: true}
	encryptedResponse := vendorLendenPb.LendenEncryptedResponse{}
	if err := unmarshaller.Unmarshal(b, &encryptedResponse); err != nil {
		return nil, fmt.Errorf("error unmarshalling response body: %w", err)
	}
	encryptedResponsePayload := encryptedResponse.GetResponse().GetPayload()
	if encryptedResponsePayload == "" {
		return nil, errors.New("empty response received from vendor")
	}
	decryptedBytes, err := c.Cryptor.Decrypt([]byte(encryptedResponsePayload))
	if err != nil {
		return nil, fmt.Errorf("error decrypting response: %w", err)
	}
	responseWithWrapper := vendorLendenPb.InitMandateResponseWrapper{}
	if unmarshallErr := unmarshaller.Unmarshal(decryptedBytes, &responseWithWrapper); unmarshallErr != nil {
		return nil, fmt.Errorf("error unmarshaling response: %w", unmarshallErr)
	}
	vendorResponse := responseWithWrapper.GetResponse().GetResponseData()
	if responseWithWrapper.GetResponse().GetMessageCode() == MessageCodeEnachAlreadyCompleted.String() {
		return &lendenPb.InitMandateResponse{
			Status:        rpc.NewStatusWithoutDebug(uint32(lendenPb.InitMandateResponse_ENACH_ALREADY_COMPLETED), responseWithWrapper.GetResponse().GetMessage()),
			MandateAmount: vendorResponse.GetMandateAmount(),
			TrackingId:    vendorResponse.GetTrackingId(),
			Umrn:          vendorResponse.GetUmrn(),
		}, nil
	}

	var (
		amount     int64
		trackingId string
		validity   *timestampPb.Timestamp
	)
	switch c.mandateType {
	case lendenPb.MandateType_MANDATE_TYPE_NACH_MANDATE:
		amount = vendorResponse.GetMandateAmount()
		trackingId = vendorResponse.GetTrackingId()
		validity, err = datetime.ParseStringTimestampProtoInLocation(ENACHMandateURLExpiryTimeFormat, vendorResponse.GetLinks().GetUrlValidity(), datetime.IST)
		if err != nil {
			return nil, errors.Wrapf(err, "error parsing mandate URL validity: %s", vendorResponse.GetLinks().GetUrlValidity())
		}
	case lendenPb.MandateType_MANDATE_TYPE_UPI_MANDATE:
		amount = vendorResponse.GetAmount()
		trackingId = vendorResponse.GetLinks().GetMandateId()
		validity, err = datetime.ParseStringTimestampProtoInLocation(UPIMandateURLExpiryTimeFormat, vendorResponse.GetLinks().GetMandateValidity(), datetime.IST)
		if err != nil {
			return nil, errors.Wrapf(err, "error parsing mandate validity: %s", vendorResponse.GetLinks().GetMandateValidity())
		}
	default:
		return nil, errors.Errorf("unknown mandate type: %s", c.mandateType)
	}

	return &lendenPb.InitMandateResponse{
		Status:             rpc.StatusOk(),
		RedirectionUrl:     vendorResponse.GetLinks().GetUrl(),
		MandateUrlValidity: validity,
		TrackingId:         trackingId,
		MandateAmount:      amount,
		Umrn:               vendorResponse.GetUmrn(),
	}, nil
}

func (c *InitMandateResponse) HandleHttpError(ctx context.Context, httpStatus int, b []byte) (proto.Message, error) {
	logger.Error(ctx, "http error in lenden init mandate API", zap.String(logger.PAYLOAD, string(b)), zap.Any(logger.HTTP_STATUS, httpStatus))
	if httpStatus == http.StatusBadRequest {
		responseWithWrapper := vendorLendenPb.InitMandateResponseWrapper{}
		if err := (protojson.UnmarshalOptions{DiscardUnknown: true}).Unmarshal(b, &responseWithWrapper); err != nil {
			return nil, fmt.Errorf("error unmarshaling response: %w", err)
		}
		switch responseWithWrapper.GetResponse().GetMessageCode() {
		case MessageCodeInvalidLoanStatus.String():
			return &lendenPb.InitMandateResponse{Status: rpc.NewStatusWithoutDebug(uint32(lendenPb.InitMandateResponse_INVALID_LOAN_STATUS), responseWithWrapper.GetResponse().GetMessage())}, nil
		case MessageCodeMandateVerificationFailed.String():
			return &lendenPb.InitMandateResponse{Status: rpc.NewStatusWithoutDebug(uint32(lendenPb.InitMandateResponse_MANDATE_VERIFICATION_FAILED), responseWithWrapper.GetResponse().GetMessage())}, nil
		case MessageCodeOfferOrAccountDetailsNotFound.String():
			return &lendenPb.InitMandateResponse{Status: rpc.NewStatusWithoutDebug(uint32(lendenPb.InitMandateResponse_OFFER_OR_ACCOUNT_DETAILS_NOT_FOUND), responseWithWrapper.GetResponse().GetMessage())}, nil
		case MessageCodeBankAccountNotVerifiedMessage.String():
			return &lendenPb.InitMandateResponse{Status: rpc.NewStatusWithoutDebug(uint32(lendenPb.InitMandateResponse_BANK_ACCOUNT_NOT_VERIFIED), responseWithWrapper.GetResponse().GetMessage())}, nil
		case MessageCodeEnachAlreadyCompleted.String():
			return &lendenPb.InitMandateResponse{Status: rpc.NewStatusWithoutDebug(uint32(lendenPb.InitMandateResponse_ENACH_ALREADY_COMPLETED), responseWithWrapper.GetResponse().GetMessage())}, nil
		default:
			return &lendenPb.InitMandateResponse{Status: &rpc.Status{Code: uint32(code.Code_INTERNAL), ShortMessage: responseWithWrapper.GetMessage()}}, nil
		}
	}
	return &lendenPb.InitMandateResponse{
		Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error response: %s", string(b))),
	}, nil
}
