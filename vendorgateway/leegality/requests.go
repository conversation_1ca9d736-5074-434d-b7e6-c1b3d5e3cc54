package leegality

import (
	json2 "encoding/json"
	"net/http"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/vendorgateway/esign"
	"github.com/epifi/gamma/vendorgateway/config"
)

var (
	cfg, _ = config.Load()
)

const (
	XAuth = "X-Auth-Token"
)

type DefaultHeaderAdder struct{}

func (d *DefaultHeaderAdder) Add(req *http.Request) *http.Request {
	leegalitySecrets := config.LeegalitySecrets{}
	secretJson := cfg.Secrets.Ids[config.LeegalitySecret]
	err := json2.Unmarshal([]byte(secretJson), &leegalitySecrets)
	if err != nil {
		logger.ErrorNoCtx("Error unmarshalling secrets : " + err.Error())
		return nil
	}
	req.Header.Set(XAuth, leegalitySecrets.XAuthToken)
	return req
}

func GetLeegalitySecretProfileId(client esign.EsignRequestClient) string {
	leegalitySecrets := config.LeegalitySecrets{}
	secretJson := cfg.Secrets.Ids[config.LeegalitySecret]
	err := json2.Unmarshal([]byte(secretJson), &leegalitySecrets)
	if err != nil {
		logger.ErrorNoCtx("Error unmarshalling secrets : " + err.Error())
		return ""
	}
	switch client {
	case esign.EsignRequestClient_ESIGN_REQUEST_CLIENT_PRE_APPROVED_LOAN:
		return leegalitySecrets.PreApprovedLoansFederalProfileId
	case esign.EsignRequestClient_ESIGN_REQUEST_CLIENT_PRE_APPROVED_LOAN_V2:
		return leegalitySecrets.PreApprovedLoansFederalProfileIdV2
	case esign.EsignRequestClient_ESIGN_REQUEST_CLIENT_SAVINGS_ACCOUNT:
		return leegalitySecrets.SavingsAccountFederalProfileId
	default:
		// returning FederalLeegalityProfileId for backward compatibility
		return leegalitySecrets.FederalLeegalityProfileId
	}

}
