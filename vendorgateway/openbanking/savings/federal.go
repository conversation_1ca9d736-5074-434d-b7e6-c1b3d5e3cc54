package savings

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"

	"github.com/epifi/be-common/pkg/vendorapi"

	"github.com/epifi/gamma/api/typesv2"
	federalPkg "github.com/epifi/gamma/pkg/vendors/federal"

	"github.com/golang/protobuf/jsonpb"
	"github.com/golang/protobuf/ptypes"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/integer"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/vendorgateway/openbanking/savings"
	vendorfedral "github.com/epifi/gamma/api/vendors/federal"
	"github.com/epifi/gamma/vendorgateway/config"
	dynConf "github.com/epifi/gamma/vendorgateway/config/genconf"
	"github.com/epifi/gamma/vendorgateway/federal"
	vgFederal "github.com/epifi/gamma/vendorgateway/federal"
	federal3 "github.com/epifi/gamma/vendorgateway/openbanking/federal"
	savingsFederal "github.com/epifi/gamma/vendorgateway/openbanking/savings/federal"
	federal2 "github.com/epifi/gamma/vendorgateway/vendorapi/cryptor/federal"
)

var (
	CreateAccountInProgress rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatus(uint32(savings.CreateAccountResponse_SB_ACCOUNT_IN_PROCESS),
			"Account Creation is in Progress", "Account Creation is in Progress")
	}

	CheckAccountStatusInProgress rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatus(uint32(savings.CheckAccountStatusResponse_SB_ACCOUNT_IN_PROCESS),
			"Account Creation is in Progress", "Account Creation is in Progress")
	}

	RequestNotFound rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatus(uint32(savings.CheckAccountStatusResponse_DETAILS_NOT_FOUND),
			"request id not found",
			"request id not found",
		)
	}
	InvalidDeviceToken rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatus(uint32(savings.CheckAccountStatusResponse_INVALID_DEVICE_TOKEN),
			"invalid device token",
			"invalid device token",
		)
	}
	AccountStatusOpeningFailure rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatus(uint32(savings.CheckAccountStatusResponse_SAVINGS_ACCOUNT_FAILURE),
			"error opening savings account",
			"error opening savings account",
		)
	}
	DuplicateRequestId rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatus(uint32(savings.CheckAccountStatusResponse_DUPLICATE_REQUESTID),
			"duplicate request id",
			"duplicate request id",
		)
	}
	CreateAccountDuplicateRequestId rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatus(uint32(savings.CreateAccountResponse_DUPLICATE_REQUESTID),
			"duplicate request id",
			"duplicate request id",
		)
	}
)

var accountCreationErrorCodes = map[string]rpc.StatusFactory{
	"OBE0000": rpc.StatusOk,
	"OBE0001": rpc.StatusPermissionDenied,
	"OBE0002": rpc.StatusPermissionDenied,
	"OBE0003": CreateAccountDuplicateRequestId,
	"OBE0004": rpc.StatusInvalidArgument,
	"OBE0007": rpc.StatusInternal,
	"OBE0016": rpc.StatusInvalidArgument,
	"OBE0019": rpc.StatusInvalidArgument,
	"OBE0041": CreateAccountInProgress,
	"OBE0040": rpc.StatusUnknown, // TODO (keek): need to check when this will occur from federal
	"OBE0050": rpc.StatusInvalidArgument,
}
var AccountStatusErrorCodesMap = map[string]rpc.StatusFactory{
	"OBE0000": rpc.StatusOk,
	"000":     rpc.StatusOk,
	"SB_000":  rpc.StatusOk,
	"SB_001":  rpc.StatusInvalidArgument,
	"SB_002":  rpc.StatusInvalidArgument,
	"SB_003":  rpc.StatusPermissionDenied,
	"SB_004":  rpc.StatusPermissionDenied,
	"SB_005":  rpc.StatusInternal,
	"SB_006":  rpc.StatusInternal,
	"SB_013":  rpc.StatusInvalidArgument,
	"SB_011":  CheckAccountStatusInProgress,
	"SB_015":  rpc.StatusInvalidArgument,
	"OBE0022": rpc.StatusInternal,
	"OBE0067": RequestNotFound,
	"OBE0059": InvalidDeviceToken,
	"OBE0003": DuplicateRequestId,
}

const (
	TIME_LAYOUT                        = "02-01-2006"
	CREATE_ACCOUNT_API_ID              = "OB_SAV_ACC_OPENING"
	MINIUM_AGE_FOR_ADULTHOOD           = 18
	MAX_NOMINEE_ADDRESSLINE_LENGTH     = 80
	MAX_GUARDIAN_ADDRESSLINE_LENGTH    = 30
	AccountStatusResponseActionFailure = "failure"
	AccountFailureResponseCode         = "SB_012"
	AccountNoDetailsFound              = "OBE0067"
	DefaultSolId                       = "5555"
	NewSolId                           = "5556"
)

// map to maintain response code <> response reasonS mapping for failure scenarios
var (
	checkAccountStatusFailureResponseReasons = map[string][]string{
		AccountFailureResponseCode: {
			"E4155The CIF ID is suspended.acctCriteria.custId.cifId",
			"W0205Customer has another account.Cannot open account in this schemeacctCommonInfo.acctJointHolder.acctJointHolderLL.<rec_0>.customerId.custId",
			"W0205Account opening Not permitted - Customer Already Having Account in  BSBDA Scheme.acctCriteria.custId.cifId",
			"BSMThe Occupation Code FINSC is invalid.acctCommonInfo.acctClassMISC.acctOccpCode.refCode",
			"079The employee ID is invalid.acctCommonInfo.acctGeneralInfo.acctCustInfo.relativeStaffId.empId",
			"007The record is deleted.acctCommonInfo.acctGeneralInfo.acctCustInfo.relativeStaffId.empId",
			"W0205Customer has minimum kyc account.Cannot open other accountacctCommonInfo.acctJointHolder.acctJointHolderLL.<rec_0>.customerId.custId",
		},
		AccountNoDetailsFound: {
			"No Data found with given Details",
		},
	}
	sourceOfFundsEnumsToStrings = map[typesv2.SourceOfFunds]string{
		typesv2.SourceOfFunds_SOURCE_OF_FUNDS_UNSPECIFIED:      "OTH",
		typesv2.SourceOfFunds_BUSINESS_INCOME:                  "BI",
		typesv2.SourceOfFunds_PARENT_OR_SPOUSE_OR_SIBLINGS:     "PSS",
		typesv2.SourceOfFunds_AGRICULTURE:                      "AGRI",
		typesv2.SourceOfFunds_RETIREMENT_BENEFITS:              "RB",
		typesv2.SourceOfFunds_OTHER_SOURCE:                     "OTH",
		typesv2.SourceOfFunds_PERSONAL_SAVINGS:                 "PS",
		typesv2.SourceOfFunds_PROCEEDS_OF_SHARES_OR_INVESTMENT: "POS",
		typesv2.SourceOfFunds_RENTAL_INTEREST_DIVIDEND_INCOME:  "RI",
		typesv2.SourceOfFunds_SALARY:                           "SA",
	}
	purposeOfSAEnumsToStrings = map[typesv2.PurposeOfSavingsAccount]string{
		typesv2.PurposeOfSavingsAccount_PURPOSE_OF_SAVINGS_ACCOUNT_UNSPECIFIED: "OTH",
		typesv2.PurposeOfSavingsAccount_PURPOSE_OF_SAVINGS_ACCOUNT_SAVINGS:     "SAV",
		typesv2.PurposeOfSavingsAccount_PURPOSE_OF_SAVINGS_ACCOUNT_LOAN:        "IR",
		typesv2.PurposeOfSavingsAccount_PURPOSE_OF_SAVINGS_ACCOUNT_INVESTMENT:  "INV",
	}
)

func (s *Service) NewFederalRequestWithCtx(ctx context.Context) vendorapi.SyncRequestFactory {
	return func(req proto.Message) vendorapi.SyncRequest {
		return NewFederalRequest(ctx, req, s.conf)
	}
}

// NewFederalRequest creates a new request for federal bank APIs depending on type of proto message.
func NewFederalRequest(_ context.Context, req proto.Message, conf *dynConf.Config) vendorapi.SyncRequest {
	switch v := req.(type) {
	case *savings.CreateAccountRequest:
		return &createAccountReq{
			method:      "POST",
			req:         req.(*savings.CreateAccountRequest),
			url:         conf.Application().CreateAccountURL(),
			callBackUrl: conf.Application().CreateAccountCallBackUrl(),
			UseNewSolId: conf.Flags().UseNewSolID(),
			conf:        conf,
		}
	case *savings.CheckAccountStatusRequest:
		return checkAccountStatusReq{method: "POST", req: req.(*savings.CheckAccountStatusRequest),
			url: conf.Application().Federal().AccountCreationEnquiryStatusURL}
	case *savings.GetBalanceRequest:
		return &savingsFederal.GetBalanceReq{Method: "POST", Req: req.(*savings.GetBalanceRequest),
			Url: conf.Application().EnquireBalanceURL()}
	case *savings.GetOpeningBalanceRequest:
		if req.(*savings.GetOpeningBalanceRequest).GetIsClosingBalanceUserGroup() {
			return &savingsFederal.GetClosingBalanceRequest{Method: "POST", Req: req.(*savings.GetOpeningBalanceRequest),
				Url: conf.Application().Federal().ClosingBalanceURL}
		}
		return &savingsFederal.GetOpeningBalanceRequest{Method: "POST", Req: req.(*savings.GetOpeningBalanceRequest),
			Url: conf.Application().Federal().OpeningBalanceURL}
	case *savings.GetBalanceV1Request:
		return &savingsFederal.GetBalanceRequestV1{Method: "POST", Req: req.(*savings.GetBalanceV1Request),
			Url: conf.Application().Federal().EnquireBalanceV1URL}
	case *savings.UpdateNomineeRequest:
		return &savingsFederal.UpdateNomineeDetailsRequest{
			Method:          "POST",
			Req:             req.(*savings.UpdateNomineeRequest),
			Url:             conf.Application().Federal().UpdateNomineeUrl,
			Channel:         conf.Application().Federal().UpdateNomineeChannel,
			ClientSecretKey: conf.Secrets().Ids[config.ClientSecretKey],
			ClientIdKey:     conf.Secrets().Ids[config.ClientId]}
	default:
		log.Println("Unsupported request type", v)
		return nil
	}
}

// createAccountReq provides functionality for creating an account with Federal Bank's APIs.
type createAccountReq struct {
	*federal.DefaultHeaderAdder
	*federal2.DefaultPGPSecuredExchange

	method      string
	req         *savings.CreateAccountRequest
	url         string
	callBackUrl string
	UseNewSolId bool
	conf        *dynConf.Config
}

//nolint:funlen
func (c *createAccountReq) Marshal() ([]byte, error) {
	params := make(map[string]interface{})
	vgFederal.AddCommonRequestParams(params)

	request := c.req

	var nomineeDetails *vendorfedral.NomineeDetails
	var guardianDetails *vendorfedral.GuardianDetails

	nomineeOptedFlag := "N"
	if request.Nominee != nil {
		nomineeOptedFlag = "Y"

		dob, err := ptypes.Timestamp(request.Nominee.Dob)
		if err != nil {
			return nil, fmt.Errorf("failed to change time format for nominee DOB: %w", err)
		}
		nomineeAge := datetime.Age(dob)

		// nominee DOB is expected i DD-MM-YYYY format
		dobDay := strconv.Itoa(dob.Day())
		if len(dobDay) == 1 {
			dobDay = "0" + dobDay
		}
		dobMonth := strconv.Itoa(int(dob.Month()))
		if len(dobMonth) == 1 {
			dobMonth = "0" + dobMonth
		}
		dobYear := strconv.Itoa(dob.Year())
		nomineeDob := dobDay + "-" + dobMonth + "-" + dobYear

		nomineeMinorFlag := "N"
		if nomineeAge < MINIUM_AGE_FOR_ADULTHOOD {
			nomineeMinorFlag = "Y"
			if request.Nominee.GuardianInfo == nil {
				return nil, fmt.Errorf("failed to create savings account opening request: Gaurdian not present for minor nominee")
			}

			relationship := request.Nominee.GuardianInfo.Relationship
			guardianCode, err := federal3.GetGuardianRelationshipCode(relationship)
			if err != nil {
				return nil, fmt.Errorf("failed to map guardian relationship: %s to a matching Federal defined guardian relationship Code", relationship)
			}

			address := request.Nominee.GuardianInfo.ContactInfo.Address
			if len(address.AddressLines) == 0 || len(fieldSanitizer.SanitiseAddressComponent(address.AddressLines[0])) == 0 {
				return nil, fmt.Errorf("failed to create savings account opening request: No address line in guardian's address")
			}
			addressLine := TruncateIfExceedsLimit(address.AddressLines[0], MAX_GUARDIAN_ADDRESSLINE_LENGTH)

			guardianDetails = &vendorfedral.GuardianDetails{
				GuardianName:        fieldSanitizer.SanitiseFullNameString(request.Nominee.GuardianInfo.Name),
				GuardianCode:        guardianCode,
				GuardianAddress:     addressLine,
				GuardianCityCode:    address.Locality,
				GuardianStateCode:   address.AdministrativeArea,
				GuardianCountryCode: address.RegionCode,
				GuardianPinCode:     address.PostalCode,
			}
		}

		relationship := request.Nominee.Relationship
		nomineeRelationshipCode, err := federalPkg.GetNomineeRelationCode(relationship)
		if err != nil {
			return nil, fmt.Errorf("failed to map nominee relationship: %s to a matching Federal defined nominee relationship Code", relationship)
		}

		address := request.Nominee.ContactInfo.Address
		if !federalPkg.IsValidFederalNomineeAddress(nil, address.GetAddressLines()) {
			return nil, fmt.Errorf("failed to create savings account opening request: No address line in nominee's address")
		}
		FedCompliantAddressLines, err := federalPkg.GetAddressLinesForVendor(address.GetAddressLines(), federalPkg.CIF_MAX_NUMBER_OF_LINES, federalPkg.CIF_MAX_LINE_LENGTH)
		if err != nil {
			return nil, fmt.Errorf("failed to create savings account opening request: Error while getting address lines for vendor")

		}
		addressLine := TruncateIfExceedsLimit(FedCompliantAddressLines[0], MAX_NOMINEE_ADDRESSLINE_LENGTH)

		nomineeDetails = &vendorfedral.NomineeDetails{
			NomineeName:        fieldSanitizer.SanitiseFullNameString(request.Nominee.Name),
			NomineeRelation:    nomineeRelationshipCode,
			NomineeAge:         strconv.Itoa(nomineeAge),
			NomineeDob:         nomineeDob,
			NomineeMinorFlag:   nomineeMinorFlag,
			NomineeAddress:     addressLine,
			NomineeCityCode:    address.Locality,
			NomineeStateCode:   address.AdministrativeArea,
			NomineeCountryCode: address.RegionCode,
			NomineePinCode:     address.PostalCode,
			GuardianDetails:    guardianDetails,
		}
	}

	schemeCode := request.VendorSku
	// TODO(aditya): remove usage of kyc level
	if schemeCode == "" {
		// Scheme code based on min/full account
		switch request.KycLevel {
		case savings.KYCLevel_MIN_KYC:
			schemeCode = "35033"
		case savings.KYCLevel_FULL_KYC:
			schemeCode = "35032"
		default:
			logger.ErrorNoCtx(fmt.Sprintf("Unknown kyc level %v", request.KycLevel))
			return nil, fmt.Errorf("Unknown kyc level %v", request.KycLevel)
		}
	}

	solId := request.GetSolId()
	if solId == "" {
		return nil, status.Error(codes.InvalidArgument, "empty sol id")
	}

	req := &vendorfedral.OpenSavingsAccountRequest{
		RespUrl:           c.callBackUrl,
		SenderCode:        fmt.Sprintf("%v", params["SenderCode"]),
		ServiceAccessId:   fmt.Sprintf("%v", params["ServiceAccessId"]),
		ServiceAccessCode: fmt.Sprintf("%v", params["ServiceAccessCode"]),
		DeviceId:          request.GetDeviceDetails().GetDeviceId(),
		UserProfileId:     request.GetDeviceDetails().GetUserProfileId(),
		DeviceToken:       request.GetDeviceDetails().GetDeviceToken(),
		RequestId:         request.GetRequestId(),
		SchemCode:         schemeCode,
		SolId:             solId,
		CustId:            request.BankCustomerId[0],
		CustomerName:      fieldSanitizer.SanitiseFullNameString(request.GetCustomerName()),
		PreopenKitFlag:    "NEW",
		AccountNo:         request.GetAccountNo(),
		AccOpenDate:       time.Now().In(federal.Timezone).Format(TIME_LAYOUT),
		ModeOfOperation:   "SG",
		MobileNo:          request.GetMobileNo(),
		EmailId:           request.GetEmailId(),
		NoOfCustomers:     "1",
		NomineeOptedFlag:  nomineeOptedFlag,
		NomineeDetails:    nomineeDetails,
		CustIds:           "000",
	}

	if c.conf.Flags().UseNewFieldsInAccountCreation() {
		if request.GetAnnualTxnVolume() == nil {
			logger.ErrorNoCtx("mandatory field annualtransactionvolume is empty for account creation")
			return nil, status.Error(codes.InvalidArgument, "annualtransactionvolume is required")
		}
		req.SourceOfFunds = sourceOfFundsEnumsToStrings[request.GetSourceOfFunds()]
		req.AnnualTxnVolume = strconv.Itoa(int(request.GetAnnualTxnVolume().GetMaxValue()))
		req.AccPurpose = purposeOfSAEnumsToStrings[request.GetPurposeOfSavingsAccount()]

		scholarshipFlag := "N"
		if request.GetScholarshipFlag() {
			scholarshipFlag = "Y"
		}

		dbtFlag := "N"
		if request.GetDbtFlag() {
			dbtFlag = "Y"
		}
		req.ScholarshipFlag = scholarshipFlag
		req.DbtFlag = dbtFlag
		jsonData, err := c.convertAndMarshal(req)
		// TOdo(Bhabtosh): remove log post uat testing
		logger.InfoNoCtx("Request to Federal Bank", zap.String("request", string(jsonData)))
		return jsonData, err
	}
	marshaller := jsonpb.Marshaler{}
	mreq, err := marshaller.MarshalToString(&vendorfedral.CreateAccountRequest{SavingsAccReq: req})
	return []byte(mreq), err
}

func (c *createAccountReq) convertAndMarshal(protoReq *vendorfedral.OpenSavingsAccountRequest) ([]byte, error) {
	var nominee *NomineeDetails
	var guardian *GuardianDetails

	if protoReq.GetNomineeOptedFlag() == "Y" {
		if protoReq.GetNomineeDetails().GetNomineeMinorFlag() == "Y" {
			guardian = &GuardianDetails{
				GuardianName:        protoReq.GetNomineeDetails().GetGuardianDetails().GetGuardianName(),
				GuardianCode:        protoReq.GetNomineeDetails().GetGuardianDetails().GetGuardianCode(),
				GuardianAddress:     protoReq.GetNomineeDetails().GetGuardianDetails().GetGuardianAddress(),
				GuardianCityCode:    protoReq.GetNomineeDetails().GetGuardianDetails().GetGuardianCityCode(),
				GuardianStateCode:   protoReq.GetNomineeDetails().GetGuardianDetails().GetGuardianStateCode(),
				GuardianCountryCode: protoReq.GetNomineeDetails().GetGuardianDetails().GetGuardianCountryCode(),
				GuardianPinCode:     protoReq.GetNomineeDetails().GetGuardianDetails().GetGuardianPinCode(),
			}
		}
		nominee = &NomineeDetails{
			NomineeName:        protoReq.GetNomineeDetails().GetNomineeName(),
			NomineeRelation:    protoReq.GetNomineeDetails().GetNomineeRelation(),
			NomineeAge:         protoReq.GetNomineeDetails().GetNomineeAge(),
			NomineeDob:         protoReq.GetNomineeDetails().GetNomineeDob(),
			NomineeMinorFlag:   protoReq.GetNomineeDetails().GetNomineeMinorFlag(),
			NomineeAddress:     protoReq.GetNomineeDetails().GetNomineeAddress(),
			NomineeCityCode:    protoReq.GetNomineeDetails().GetNomineeCityCode(),
			NomineeStateCode:   protoReq.GetNomineeDetails().GetNomineeStateCode(),
			NomineeCountryCode: protoReq.GetNomineeDetails().GetNomineeCountryCode(),
			NomineePinCode:     protoReq.GetNomineeDetails().GetNomineePinCode(),
			GuardianDetails:    guardian,
		}
	}
	// Manually map Protobuf message to struct
	createAccountRequestInVendorSpecifiedOrder := CreateAccountReqInVendorSpecifiedOrder{
		RespUrl:           protoReq.GetRespUrl(),
		SenderCode:        protoReq.GetSenderCode(),
		ServiceAccessId:   protoReq.GetServiceAccessId(),
		ServiceAccessCode: protoReq.GetServiceAccessCode(),
		DeviceId:          protoReq.GetDeviceId(),
		UserProfileId:     protoReq.GetUserProfileId(),
		DeviceToken:       protoReq.GetDeviceToken(),
		RequestId:         protoReq.GetRequestId(),
		SchemCode:         protoReq.GetSchemCode(),
		SolId:             protoReq.GetSolId(),
		CustId:            protoReq.GetCustId(),
		CustomerName:      protoReq.GetCustomerName(),
		PreopenKitFlag:    protoReq.GetPreopenKitFlag(),
		AccountNo:         protoReq.GetAccountNo(),
		AccOpenDate:       protoReq.GetAccOpenDate(),
		ModeOfOperation:   protoReq.GetModeOfOperation(),
		MobileNumber:      protoReq.GetMobileNo(),
		EmailId:           protoReq.GetEmailId(),
		NoOfCustomers:     protoReq.GetNoOfCustomers(),
		NomineeOptedFlag:  protoReq.GetNomineeOptedFlag(),
		SourceOfFunds:     protoReq.GetSourceOfFunds(),
		ScholarshipFlag:   protoReq.GetScholarshipFlag(),
		DBT_Flag:          protoReq.GetDbtFlag(),
		AnnualTxnVolume:   protoReq.GetAnnualTxnVolume(),
		AccPurpose:        protoReq.GetAccPurpose(),
		Nominee_Details:   nominee,
		CustIds:           protoReq.GetCustIds(),
	}

	// Use a map to wrap it inside "SB_Acc_Opening_Request"
	wrappedRequest := map[string]interface{}{
		"SB_Acc_Opening_Request": createAccountRequestInVendorSpecifiedOrder,
	}
	return json.Marshal(wrappedRequest)
}

// URL provides the URL to send the request to
func (c *createAccountReq) URL() string {
	return c.url
}

// HTTPMethod returns the http method to use for the API call.
func (c *createAccountReq) HTTPMethod() string {
	return c.method
}

// GetResponse returns Response struct that can deserialize the vendor response
func (c *createAccountReq) GetResponse() vendorapi.Response {
	return createAccountRes{}
}

// This struct provides functionality for adapting to Federal bank's API.
type createAccountRes struct {
}

// Unmarshal converts the response received from federal bank's create account API into
// CreateAccountResponse proto.
//
//nolint:funlen
func (c createAccountRes) Unmarshal(b []byte) (proto.Message, error) {
	var createAccountResponse vendorfedral.CreateAccountResponse
	unmarshaller := jsonpb.Unmarshaler{}
	err := unmarshaller.Unmarshal(bytes.NewBuffer(b), &createAccountResponse)
	if err != nil {
		return nil, fmt.Errorf("could not parse JSON response: %w", err)
	}

	var status *rpc.Status
	var statusFactory = accountCreationErrorCodes[createAccountResponse.GetResponse()]
	if statusFactory == nil {
		status = rpc.StatusUnknown()
	} else {
		status = statusFactory()
	}

	return &savings.CreateAccountResponse{
		Status: status,
		VendorStatus: &commonvgpb.VendorStatus{
			Code:        createAccountResponse.GetResponse(),
			Description: createAccountResponse.GetReason(),
		},
	}, nil
}

// createAccountReq provides functionality for checking the status of an account with Federal Bank's APIs.
type checkAccountStatusReq struct {
	*federal.DefaultHeaderAdder
	*federal2.DefaultPGPSecuredExchange

	method string
	req    *savings.CheckAccountStatusRequest
	url    string
}

// Marshal provides the XML for Federal bank's check account status API call.
func (c checkAccountStatusReq) Marshal() ([]byte, error) {

	commonPayload := make(map[string]interface{})
	federal.AddCommonRequestParams(commonPayload)

	requestPayload := &vendorfedral.EnquiryStatusRequest{
		SenderCode:        commonPayload["SenderCode"].(string),
		ServiceAccessId:   commonPayload["ServiceAccessId"].(string),
		ServiceAccessCode: commonPayload["ServiceAccessCode"].(string),
		DeviceId:          c.req.GetDeviceDetails().GetDeviceId(),
		RequestId:         idgen.RandAlphaNumericString(32),
		ApiId:             CREATE_ACCOUNT_API_ID,
		OriginalRequestId: c.req.GetRequestId(),
		MobileNumber:      c.req.GetPhoneNumber().ToString(),
		DeviceToken:       c.req.GetDeviceDetails().GetDeviceToken(),
		UserProfileId:     c.req.GetDeviceDetails().GetUserProfileId(),
	}

	marshaller := jsonpb.Marshaler{}
	mreq, err := marshaller.MarshalToString(requestPayload)
	if err != nil {
		return nil, err
	}
	return []byte(mreq), nil
}

// URL provides the URL to send the request to
func (c checkAccountStatusReq) URL() string {
	return c.url
}

// HTTPMethod returns the http method to use for the API call.
func (c checkAccountStatusReq) HTTPMethod() string {
	return c.method
}

// GetResponse returns Response struct that can deserialize the vendor response
func (c checkAccountStatusReq) GetResponse() vendorapi.Response {
	return checkAccountStatusRes{}
}

// This struct provides functionality for adapting to Federal bank's API.
type checkAccountStatusRes struct {
}

// Unmarshal converts the response received from federal bank's check status API into
// CheckAccountStatusResponse proto.
func (c checkAccountStatusRes) Unmarshal(b []byte) (proto.Message, error) {

	m := &vendorfedral.EnquiryStatusAccountCreationResponse{}
	unmarshaller := jsonpb.Unmarshaler{}
	err := unmarshaller.Unmarshal(bytes.NewBuffer(b), m)
	if err != nil {
		return nil, err
	}
	response := m.GetAccountCreationDetails()
	status := c.evaluateRPCStatus(response)

	var createdAt *timestamppb.Timestamp
	if status.IsSuccess() {
		createdAt, err = datetime.ParseFederalTS(response.GetCreatedTime())
		if err != nil {
			logger.ErrorNoCtx("error parsing federal timestamp", zap.Error(err))
		}
	}
	vendorStatus := &commonvgpb.VendorStatus{}
	if response.GetResponseCode() != "" {
		vendorStatus.Code = response.GetResponseCode()
		vendorStatus.Description = response.GetResponseReason()
	} else {
		vendorStatus.Code = m.GetResponseCode()
		vendorStatus.Description = m.GetResponseReason()
	}
	return &savings.CheckAccountStatusResponse{
		AccountNumber: m.GetAccountCreationDetails().GetSavingAccountNumber(),
		Status:        status,
		CreatedAt:     createdAt,
		VendorStatus:  vendorStatus,
	}, nil
}

func (c checkAccountStatusRes) evaluateRPCStatus(response *vendorfedral.AccountCreationTransactionDetails) *rpc.Status {
	respCode := response.GetResponseCode()
	respAction := response.GetResponseAction()
	respReason := response.GetResponseReason()
	// match the response code
	// TODO(shubham) - check and make below check more generic for addition of more resp codes in future
	if strings.EqualFold(respCode, AccountFailureResponseCode) {
		// fail the request if combination of response action and response reason suffices, else consider inquire-able/InProgress
		if strings.Contains(strings.ToLower(respAction), AccountStatusResponseActionFailure) &&
			c.checkForAccountStatusFailureReason(respCode, respReason) {
			return AccountStatusOpeningFailure()
		}
		logger.InfoNoCtx("SB_012 without response action failure")
		return CheckAccountStatusInProgress()
	} else if strings.EqualFold(respCode, AccountNoDetailsFound) && c.checkForAccountStatusFailureReason(respCode, respReason) {
		return RequestNotFound()
	}
	statusFactory, ok := AccountStatusErrorCodesMap[respCode]
	if ok {
		return statusFactory()
	}
	return rpc.StatusUnknown()
}

func (c checkAccountStatusRes) checkForAccountStatusFailureReason(respCode, respReason string) bool {
	for _, reason := range checkAccountStatusFailureResponseReasons[respCode] {
		if strings.Contains(strings.ToLower(reason), strings.ToLower(respReason)) {
			return true
		}
	}
	return false
}

func TruncateIfExceedsLimit(addressLine string, lengthLimit int) string {
	addressLine = fieldSanitizer.SanitiseAddressComponent(addressLine)
	length := integer.Min(len(addressLine), lengthLimit)
	return addressLine[0:length]
}
