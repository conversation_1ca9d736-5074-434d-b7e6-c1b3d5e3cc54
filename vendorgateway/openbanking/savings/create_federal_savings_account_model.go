package savings

// bank has added DFDL validation and expects the fields in the request should follow a particular order as below
type CreateAccountReqInVendorSpecifiedOrder struct {
	RespUrl           string          `json:"RespUrl"`
	SenderCode        string          `json:"SenderCode"`
	ServiceAccessId   string          `json:"ServiceAccessId"`
	ServiceAccessCode string          `json:"ServiceAccessCode"`
	DeviceId          string          `json:"DeviceId"`
	UserProfileId     string          `json:"UserProfileId"`
	DeviceToken       string          `json:"DeviceToken"`
	RequestId         string          `json:"RequestId"`
	SchemCode         string          `json:"SchemCode"`
	SolId             string          `json:"SolId"`
	CustId            string          `json:"CustId"`
	CustomerName      string          `json:"CustomerName"`
	PreopenKitFlag    string          `json:"PreopenKitFlag"`
	AccountNo         string          `json:"AccountNo"`
	AccOpenDate       string          `json:"AccOpenDate"`
	ModeOfOperation   string          `json:"ModeOfOperation"`
	MobileNumber      string          `json:"Mobile_Number"`
	EmailId           string          `json:"EmailId"`
	NoOfCustomers     string          `json:"NoOfCustomers"`
	NomineeOptedFlag  string          `json:"NomineeOptedFlag"`
	SourceOfFunds     string          `json:"SourceOfFunds"`
	ScholarshipFlag   string          `json:"ScholarshipFlag"`
	DBT_Flag          string          `json:"DBT_Flag"`
	AnnualTxnVolume   string          `json:"AnnualTxnVolume"`
	AccPurpose        string          `json:"AccPurpose"`
	Nominee_Details   *NomineeDetails `json:"NomineeDetails,omitempty"`
	CustIds           string          `json:"CustIds"`
}

type NomineeDetails struct {
	NomineeName        string           `json:"NomineeName,omitempty"`
	NomineeRelation    string           `json:"NomineeRelation,omitempty"`
	NomineeAge         string           `json:"NomineeAge,omitempty"`
	NomineeDob         string           `json:"NomineeDOB,omitempty"`
	NomineeMinorFlag   string           `json:"NomineeMinorFlag,omitempty"`
	NomineeAddress     string           `json:"NomineeAddress,omitempty"`
	NomineeCityCode    string           `json:"NomineeCitCode,omitempty"`
	NomineeStateCode   string           `json:"NomineeStaCode,omitempty"`
	NomineeCountryCode string           `json:"NomineeConCode,omitempty"`
	NomineePinCode     string           `json:"NomineepinCode,omitempty"`
	GuardianDetails    *GuardianDetails `json:"GuardianDetails,omitempty"`
}

type GuardianDetails struct {
	GuardianName        string `json:"GuardianName,omitempty"`
	GuardianCode        string `json:"GuardianCode,omitempty"`
	GuardianAddress     string `json:"GuardianAddress,omitempty"`
	GuardianCityCode    string `json:"GuardianCitCode,omitempty"`
	GuardianStateCode   string `json:"GuardianStaCode,omitempty"`
	GuardianCountryCode string `json:"GuardianConCode,omitempty"`
	GuardianPinCode     string `json:"GuardianPinCode,omitempty"`
}
