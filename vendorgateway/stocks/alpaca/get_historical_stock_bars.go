package alpaca

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"time"

	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/vendorapi"
	stocksPb "github.com/epifi/gamma/api/vendorgateway/stocks"
	alpacaPb "github.com/epifi/gamma/api/vendors/alpaca"
	"github.com/epifi/gamma/vendorgateway/config"
)

type GetHistoricalStockBarsReq struct {
	Method string
	Req    *stocksPb.GetHistoricalStockBarsRequest
	Conf   *config.Alpaca
}

func (g *GetHistoricalStockBarsReq) Marshal() ([]byte, error) {
	return nil, nil
}

func (g *GetHistoricalStockBarsReq) SetAuth(r *http.Request) *http.Request {
	r.SetBasicAuth(g.Conf.Secret.Broker<PERSON><PERSON><PERSON>, g.Conf.Secret.BrokerApiSecret)
	return r
}

func (g *GetHistoricalStockBarsReq) URL() string {
	queryParams := url.Values{}
	queryParams.Set("timeframe", g.Req.GetTimeFrame())
	if g.Req.GetStartTime().IsValid() {
		queryParams.Set("start", g.Req.GetStartTime().AsTime().Format(time.RFC3339))
	}
	if g.Req.GetEndTime().IsValid() {
		queryParams.Set("end", g.Req.GetEndTime().AsTime().Format(time.RFC3339))
	}
	if g.Req.GetPageToken() != "" {
		queryParams.Set("page_token", g.Req.GetPageToken())
	}
	// set adjustment value to get stock prices adjusted for stock splits and dividends in response
	alpacaMarketDataSource := getMarketDataSourceForAlpaca(g.Req.GetMarketDataSource())
	if alpacaMarketDataSource != "" {
		queryParams.Set("feed", alpacaMarketDataSource)
	}
	queryParams.Set("adjustment", "all")
	queryParams.Set("limit", "10000")
	return fmt.Sprintf("%s/%s/stocks/%s/bars?%s", g.Conf.MarketApiHost, g.Conf.MarketApiVersion, g.Req.Symbol, queryParams.Encode())
}

func (g *GetHistoricalStockBarsReq) HTTPMethod() string {
	return g.Method
}

func (g *GetHistoricalStockBarsReq) ContentTypeString() string {
	return vendorapi.ContentTypeJSON
}

func (g *GetHistoricalStockBarsReq) GetResponse() vendorapi.Response {
	return &GetHistoricalStockBarsRes{}
}

type GetHistoricalStockBarsRes struct {
}

func (g *GetHistoricalStockBarsRes) Unmarshal(b []byte) (proto.Message, error) {
	alpacaHistoricalStockBars := &alpacaPb.HistoricalStockBars{}
	pj := protojson.UnmarshalOptions{DiscardUnknown: true}
	err := pj.Unmarshal(b, alpacaHistoricalStockBars)
	if err != nil {
		return nil, fmt.Errorf("error unmarshlling historical stock bars res: %w", err)
	}

	var stockBars []*stocksPb.Bar
	for _, bar := range alpacaHistoricalStockBars.GetStockBars() {
		stockBars = append(stockBars, &stocksPb.Bar{
			Ts:             bar.GetTs(),
			OpenPrice:      bar.GetOpenPrice(),
			HighPrice:      bar.GetHighPrice(),
			LowPrice:       bar.GetLowPrice(),
			ClosePrice:     bar.GetClosePrice(),
			Volume:         bar.GetVolume(),
			NumberOfTrades: bar.GetNumberOfTrades(),
			VolWtAvgPrice:  bar.GetVolWtAvgPrice(),
		})
	}

	return &stocksPb.GetHistoricalStockBarsResponse{
		Status:        rpc.StatusOk(),
		Bars:          stockBars,
		NextPageToken: alpacaHistoricalStockBars.GetNextPageToken(),
	}, nil
}

func (g *GetHistoricalStockBarsRes) HandleHttpError(ctx context.Context, httpStatus int, b []byte) (proto.Message, error) {
	return &stocksPb.GetHistoricalStockBarsResponse{
		Status: vendorapi.GetRpcStatusFromHttpCode(ctx, httpStatus),
	}, nil
}
