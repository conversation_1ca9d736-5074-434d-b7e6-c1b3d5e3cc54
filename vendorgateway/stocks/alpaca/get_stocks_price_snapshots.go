// nolint:funlen,depguard
package alpaca

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"

	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/vendorapi"
	stocksPb "github.com/epifi/gamma/api/vendorgateway/stocks"
	alpacaPb "github.com/epifi/gamma/api/vendors/alpaca"
	"github.com/epifi/gamma/vendorgateway/config"
)

type GetStocksPriceSnapshotsReq struct {
	Method string
	Req    *stocksPb.GetStocksPriceSnapshotsRequest
	Conf   *config.Alpaca
}

func (g *GetStocksPriceSnapshotsReq) Marshal() ([]byte, error) {
	return nil, nil
}

func (g *GetStocksPriceSnapshotsReq) SetAuth(r *http.Request) *http.Request {
	r.SetBasicAuth(g.Conf.Secret.BrokerApiKey, g.Conf.Secret.BrokerApiSecret)
	return r
}

func (g *GetStocksPriceSnapshotsReq) URL() string {
	queryParams := url.Values{}
	queryParams.Set("symbols", strings.Join(g.Req.GetSymbols(), ","))
	alpacaMarketDataSource := getMarketDataSourceForAlpaca(g.Req.GetMarketDataSource())
	if alpacaMarketDataSource != "" {
		queryParams.Set("feed", alpacaMarketDataSource)
	}
	return fmt.Sprintf("%v/%v/stocks/snapshots?%s", g.Conf.MarketApiHost, g.Conf.MarketApiVersion, queryParams.Encode())
}

func (g *GetStocksPriceSnapshotsReq) HTTPMethod() string {
	return g.Method
}

func (g *GetStocksPriceSnapshotsReq) ContentTypeString() string {
	return vendorapi.ContentTypeJSON
}

func (g *GetStocksPriceSnapshotsReq) GetResponse() vendorapi.Response {
	return &GetStocksPriceSnapshotsResp{}
}

func (g *GetStocksPriceSnapshotsReq) CanLogUnredactedEncryptedPayload() bool {
	return false
}

type GetStocksPriceSnapshotsResp struct {
}

func (g *GetStocksPriceSnapshotsResp) Unmarshal(b []byte) (proto.Message, error) {
	var jsonMessages map[string]*json.RawMessage
	err := json.Unmarshal(b, &jsonMessages)
	if err != nil {
		return nil, err
	}

	stockSnapshots := map[string]*stocksPb.GetStocksPriceSnapshotsResponse_StockPriceInfo{}
	for symbol, msg := range jsonMessages {
		if msg == nil {
			// vendor may send nil in response for some stocks, we are not sending nil here as we want to set some value in redis
			// and even if we are sending nil empty object is getting initialized in response by grpc
			stockSnapshots[symbol] = &stocksPb.GetStocksPriceSnapshotsResponse_StockPriceInfo{}
			continue
		}
		snapshot := &alpacaPb.StocksPriceSnapshots_StockPriceSnapshot{}
		um := protojson.UnmarshalOptions{DiscardUnknown: true}
		err = um.Unmarshal(*msg, snapshot)
		if err != nil {
			return nil, err
		}

		stockSnapshots[symbol] = &stocksPb.GetStocksPriceSnapshotsResponse_StockPriceInfo{
			LatestTrade: &stocksPb.Trade{
				Ts:              snapshot.GetLatestTrade().GetTs(),
				Exchange:        snapshot.GetLatestTrade().GetExchange(),
				TradePrice:      snapshot.GetLatestTrade().GetTradePrice(),
				TradeSize:       snapshot.GetLatestTrade().GetTradeSize(),
				TradeConditions: snapshot.GetLatestTrade().GetTradeConditions(),
				TradeId:         snapshot.GetLatestTrade().GetTradeId(),
				Tape:            snapshot.GetLatestTrade().GetTape(),
			},
			LatestQuote: &stocksPb.Quote{
				Ts:              snapshot.GetLatestQuote().GetTs(),
				AskExchange:     snapshot.GetLatestQuote().GetAskExchange(),
				AskPrice:        snapshot.GetLatestQuote().GetAskPrice(),
				AskSize:         snapshot.GetLatestQuote().GetAskSize(),
				BidExchange:     snapshot.GetLatestQuote().GetBidExchange(),
				BidPrice:        snapshot.GetLatestQuote().GetBidPrice(),
				BidSize:         snapshot.GetLatestQuote().GetBidSize(),
				QuoteConditions: snapshot.GetLatestQuote().GetQuoteConditions(),
				Tape:            snapshot.GetLatestQuote().GetTape(),
			},
			MinuteBar: &stocksPb.Bar{
				Ts:             snapshot.GetMinuteBar().GetTs(),
				OpenPrice:      snapshot.GetMinuteBar().GetOpenPrice(),
				HighPrice:      snapshot.GetMinuteBar().GetHighPrice(),
				LowPrice:       snapshot.GetMinuteBar().GetLowPrice(),
				ClosePrice:     snapshot.GetMinuteBar().GetClosePrice(),
				Volume:         snapshot.GetMinuteBar().GetVolume(),
				NumberOfTrades: snapshot.GetMinuteBar().GetNumberOfTrades(),
				VolWtAvgPrice:  snapshot.GetMinuteBar().GetVolWtAvgPrice(),
			},
			DailyBar: &stocksPb.Bar{
				Ts:             snapshot.GetDailyBar().GetTs(),
				OpenPrice:      snapshot.GetDailyBar().GetOpenPrice(),
				HighPrice:      snapshot.GetDailyBar().GetHighPrice(),
				LowPrice:       snapshot.GetDailyBar().GetLowPrice(),
				ClosePrice:     snapshot.GetDailyBar().GetClosePrice(),
				Volume:         snapshot.GetDailyBar().GetVolume(),
				NumberOfTrades: snapshot.GetDailyBar().GetNumberOfTrades(),
				VolWtAvgPrice:  snapshot.GetDailyBar().GetVolWtAvgPrice(),
			},
			PrevDailyBar: &stocksPb.Bar{
				Ts:             snapshot.GetPrevDailyBar().GetTs(),
				OpenPrice:      snapshot.GetPrevDailyBar().GetOpenPrice(),
				HighPrice:      snapshot.GetPrevDailyBar().GetHighPrice(),
				LowPrice:       snapshot.GetPrevDailyBar().GetLowPrice(),
				ClosePrice:     snapshot.GetPrevDailyBar().GetClosePrice(),
				Volume:         snapshot.GetPrevDailyBar().GetVolume(),
				NumberOfTrades: snapshot.GetPrevDailyBar().GetNumberOfTrades(),
				VolWtAvgPrice:  snapshot.GetPrevDailyBar().GetVolWtAvgPrice(),
			},
		}
	}

	return &stocksPb.GetStocksPriceSnapshotsResponse{
		Status:              rpc.StatusOk(),
		StockPriceSnapshots: stockSnapshots,
	}, nil
}

func (g *GetStocksPriceSnapshotsResp) HandleHttpError(ctx context.Context, httpStatus int, b []byte) (proto.Message, error) {
	return &stocksPb.GetStocksPriceSnapshotsResponse{
		Status: vendorapi.GetRpcStatusFromHttpCode(ctx, httpStatus),
	}, nil
}

func (g *GetStocksPriceSnapshotsResp) CanLogUnredactedEncryptedPayload() bool {
	return false
}
