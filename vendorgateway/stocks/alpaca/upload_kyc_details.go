package alpaca

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	types "github.com/epifi/gamma/api/typesv2"
	stocksPb "github.com/epifi/gamma/api/vendorgateway/stocks"
	alpacaPb "github.com/epifi/gamma/api/vendors/alpaca"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"
	"github.com/epifi/gamma/vendorgateway/config"
)

type UploadKYCDetailsReq struct {
	Method string
	Req    *stocksPb.UploadKYCDetailsRequest
	Conf   *config.Alpaca
}

func (g *UploadKYCDetailsReq) Marshal() ([]byte, error) {
	if g.Req.GetDetailsVerification() == nil {
		logger.ErrorNoCtx("DetailsVerification is nil for given user")
		return nil, errors.New("DetailsVerification is nil for given user")
	}

	if g.Req.GetDocumentVerification() == nil {
		logger.ErrorNoCtx("DocumentVerification is nil for given user")
		return nil, errors.New("DocumentVerification is nil for given user")
	}

	if g.Req.GetBackgroundVerification() == nil {
		logger.ErrorNoCtx("BackgroundVerification is nil for given user")
		return nil, errors.New("BackgroundVerification is nil for given user")
	}

	if g.Req.GetIdentityVerification() == nil {
		logger.ErrorNoCtx("IdentityVerification is nil for given user")
		return nil, errors.New("IdentityVerification is nil for given user")
	}

	if g.Req.GetDocumentVerification().GetDataComparisonDetails() == nil {
		logger.ErrorNoCtx("DataComparisonDetails is nil for given user")
		return nil, errors.New("DataComparisonDetails is nil for given user")
	}

	if g.Req.GetDocumentVerification().GetImageIntegrityDetails() == nil {
		logger.ErrorNoCtx("ImageIntegrityDetails is nil for given user")
		return nil, errors.New("ImageIntegrityDetails is nil for given user")
	}

	if g.Req.GetDocumentVerification().GetVisualAuthenticity() == nil {
		logger.ErrorNoCtx("VisualAuthenticity is nil for given user")
		return nil, errors.New("VisualAuthenticity is nil for given user")
	}

	if g.Req.GetProviderName() == nil {
		logger.ErrorNoCtx("ProviderName is nil for given user")
		return nil, errors.New("ProviderName is nil for given user")
	}

	approvalStatus, err := getApprovalStatusFromStr(g.Req.GetDetailsVerification().GetApprovalStatus())
	if err != nil {
		return nil, errors.Wrap(err, "unable to parse ApprovalStatus in UploadKycDetails")
	}
	nationality, err := getStrFromNationality(g.Req.GetDetailsVerification().GetNationality())
	if err != nil {
		return nil, errors.Wrap(err, "unable to parse Nationality in UploadKycDetails")
	}
	kyc := alpacaPb.KYC{
		Id:                 g.Req.GetDetailsVerification().GetId(),
		RiskScore:          g.Req.GetDetailsVerification().GetRiskScore(),
		RiskLevel:          g.Req.GetDetailsVerification().GetRiskLevel(),
		RiskCategories:     g.Req.GetDetailsVerification().GetRiskCategories(),
		ApplicantName:      g.Req.GetDetailsVerification().GetApplicantName(),
		EmailAddress:       g.Req.GetDetailsVerification().GetEmailAddress(),
		Nationality:        nationality,
		IdNumber:           g.Req.GetDetailsVerification().GetIdNumber(),
		DateOfBirth:        getStrFromDate(g.Req.GetDetailsVerification().GetDateOfBirth()),
		Address:            g.Req.GetDetailsVerification().GetAddress(),
		PostalCode:         g.Req.GetDetailsVerification().GetPostalCode(),
		CountryOfResidency: getStrFromCountryCode(g.Req.GetDetailsVerification().GetCountryOfResidency()),
		KycCompletedAt:     getStrFromTimestamp(g.Req.GetDetailsVerification().GetKycCompletedAt()),
		IpAddress:          g.Req.GetDetailsVerification().GetIpAddress(),
		CheckInitiatedAt:   getStrFromTimestamp(g.Req.GetDetailsVerification().GetCheckInitiatedAt()),
		CheckCompletedAt:   getStrFromTimestamp(g.Req.GetDetailsVerification().GetCheckCompletedAt()),
		ApprovalStatus:     approvalStatus,
		ApprovedBy:         g.Req.GetDetailsVerification().GetApprovedBy(),
		ApprovedReason:     g.Req.GetDetailsVerification().GetApprovalReason(),
		ApprovedAt:         getStrFromTimestamp(g.Req.GetDetailsVerification().GetApprovedAt()),
	}
	nationality, err = getStrFromNationality(g.Req.GetDocumentVerification().GetNationality())
	if err != nil {
		return nil, errors.Wrap(err, "unable to convert Nationality in UploadKycDetails")
	}
	documentType := getStrFromDocumentSubType(g.Req.GetDocumentVerification().GetDocumentType())
	document := alpacaPb.CIPDocument{
		Id:              g.Req.GetDocumentVerification().GetId(),
		Result:          getStrFromVerificationResult(g.Req.GetDocumentVerification().GetResult()),
		Status:          getStrFromVerificationStatus(g.Req.GetDocumentVerification().GetStatus()),
		CreatedAt:       getStrFromTimestamp(g.Req.GetDocumentVerification().GetCreatedAt()),
		DateOfBirth:     getStrFromDate(g.Req.GetDocumentVerification().GetDateOfBirth()),
		DocumentNumbers: g.Req.GetDocumentVerification().GetDocumentNumbers(),
		DocumentType:    documentType,
		FirstName:       g.Req.GetDocumentVerification().GetFirstName(),
		LastName:        g.Req.GetDocumentVerification().GetLastName(),
		IssuingCountry:  getStrFromCountryCode(g.Req.GetDocumentVerification().GetIssuingCountry()),
		Nationality:     nationality, // This represent nationality as per document
		AgeValidation:   getStrFromVerificationResult(g.Req.GetDocumentVerification().GetAgeValidation()),
		DataComparison:  getStrFromVerificationResult(g.Req.GetDocumentVerification().GetDataComparison()),
		ImageIntegrity:  getStrFromVerificationResult(g.Req.GetDocumentVerification().GetImageIntegrity()),
		DataComparisonBreakdown: &alpacaPb.DataComparisonBreakDown{
			DateOfBirth:   getStrFromVerificationResult(g.Req.GetDocumentVerification().GetDataComparisonDetails().GetDateOfBirth()),
			DateOfExpiry:  getStrFromVerificationResult(g.Req.GetDocumentVerification().GetDataComparisonDetails().GetDateOfExpiry()),
			FirstName:     getStrFromVerificationResult(g.Req.GetDocumentVerification().GetDataComparisonDetails().GetFirstName()),
			LastName:      getStrFromVerificationResult(g.Req.GetDocumentVerification().GetDataComparisonDetails().GetLastName()),
			AgeValidation: getStrFromVerificationResult(g.Req.GetDocumentVerification().GetDataComparisonDetails().GetAgeValidation()),
		},
		ImageIntegrityBreakdown: &alpacaPb.ImageIntegrityDetails{
			ColourPicture:     getStrFromVerificationResult(g.Req.GetDocumentVerification().GetImageIntegrityDetails().GetIsColourPicture()),
			DocumentQuality:   getStrFromVerificationResult(g.Req.GetDocumentVerification().GetImageIntegrityDetails().GetDocumentQuality()),
			ImageQuality:      getStrFromVerificationResult(g.Req.GetDocumentVerification().GetImageIntegrityDetails().GetImageQuality()),
			SupportedDocument: getStrFromVerificationResult(g.Req.GetDocumentVerification().GetImageIntegrityDetails().GetSupportedDocument()),
		},
		VisualAuthenticity: &alpacaPb.VisualAuthenticity{
			DigitalTampering:        getStrFromVerificationResult(g.Req.GetDocumentVerification().GetVisualAuthenticity().GetDigitalTampering()),
			FaceDetection:           getStrFromVerificationResult(g.Req.GetDocumentVerification().GetVisualAuthenticity().GetFaceDetection()),
			Fonts:                   getStrFromVerificationResult(g.Req.GetDocumentVerification().GetVisualAuthenticity().GetFontsCompare()),
			OriginalDocumentPresent: getStrFromVerificationResult(g.Req.GetDocumentVerification().GetVisualAuthenticity().GetOriginalDocumentProvided()),
			PictureFaceIntegrity:    getStrFromVerificationResult(g.Req.GetDocumentVerification().GetVisualAuthenticity().GetComparePhoto()),
			SecurityFeatures:        getStrFromVerificationResult(g.Req.GetDocumentVerification().GetVisualAuthenticity().GetSecurityFeatures()),
			Template:                getStrFromVerificationResult(g.Req.GetDocumentVerification().GetVisualAuthenticity().GetStandardFormat()),
		},
	}
	// It is required in certain case
	// Eg: When we are using document upload via camera and compare face
	var photo *alpacaPb.Photo
	if g.Req.GetFaceVerification() != nil {
		photo = &alpacaPb.Photo{
			Id:                 g.Req.GetFaceVerification().GetId(),
			Result:             getStrFromVerificationResult(g.Req.GetFaceVerification().GetResult()),
			Status:             getStrFromVerificationStatus(g.Req.GetFaceVerification().GetStatus()),
			CreatedAt:          getStrFromTimestamp(g.Req.GetFaceVerification().GetCreatedAt()),
			FaceComparison:     getStrFromVerificationResult(g.Req.GetFaceVerification().GetFaceComparison()),
			ImageIntegrity:     getStrFromVerificationResult(g.Req.GetFaceVerification().GetImageIntegrity()),
			VisualAuthenticity: getStrFromVerificationResult(g.Req.GetFaceVerification().GetVisualAuthenticity()),
		}
	}
	identity := alpacaPb.CIPIdentity{
		Id:          g.Req.GetIdentityVerification().GetId(),
		Result:      getStrFromVerificationResult(g.Req.GetIdentityVerification().GetResult()),
		Status:      getStrFromVerificationStatus(g.Req.GetIdentityVerification().GetStatus()),
		DateOfBirth: getStrFromVerificationResult(g.Req.GetDocumentVerification().GetDataComparisonDetails().GetDateOfBirth()),
		TaxId:       getStrFromVerificationResult(g.Req.GetDocumentVerification().GetResult()),
		CreatedAt:   getStrFromTimestamp(g.Req.GetIdentityVerification().GetCreatedAt()),
		TaxIdBreakdown: &alpacaPb.TaxIdBreakdown{
			VerificationMessage: g.Req.GetIdentityVerification().GetTaxIdBreakdown().GetVerificationMessage(),
		},
		MatchedAddress: getStrFromVerificationResult(stocksPb.VerificationResult_VERIFICATION_RESULT_CLEAR),
		MatchedAddresses: &alpacaPb.MatchedAddresses{
			VerificationMessage: "Documentary Address Verification - bank statement",
		},
	}
	watchlist := alpacaPb.Watchlist{
		Id:                       g.Req.GetBackgroundVerification().GetId(),
		Result:                   getStrFromVerificationResult(g.Req.GetBackgroundVerification().GetResult()),
		Status:                   getStrFromVerificationStatus(g.Req.GetBackgroundVerification().GetStatus()),
		PoliticallyExposedPerson: getStrFromVerificationResult(g.Req.GetBackgroundVerification().GetPoliticallyExposedPerson()),
		Sanction:                 getStrFromVerificationResult(g.Req.GetBackgroundVerification().GetSanction()),
		AdverseMedia:             getStrFromVerificationResult(g.Req.GetBackgroundVerification().GetAdverseMedia()),
		MonitoredLists:           getStrFromVerificationResult(g.Req.GetBackgroundVerification().GetMonitoredLists()),
		CreatedAt:                getStrFromTimestamp(g.Req.GetBackgroundVerification().GetCreatedAt()),
		Records:                  getBackgroundVerificationResponse(g.Req.GetBackgroundVerification().GetRecords()),
	}
	req := &alpacaPb.UploadCIPDetailRequest{
		ProviderName: g.Req.GetProviderName(),
		Kyc:          &kyc,
		Document:     &document,
		Photo:        photo,
		Identity:     &identity,
		Watchlist:    &watchlist,
	}
	return protojson.Marshal(req)
}

func getBackgroundVerificationResponse(verification *stocksPb.BackgroundVerificationRecord) *alpacaPb.BackgroundVerificationRecord {
	amlAction := ""
	if verification.GetAction() != stocksPb.AmlAction_AML_ACTION_UNSPECIFIED {
		amlAction = verification.GetAction().String()
	}
	pepStatus := ""
	if verification.GetPepStatus() != types.PoliticallyExposedStatus_POLITICALLY_EXPOSED_STATUS_UNSPECIFIED {
		pepStatus = verification.GetPepStatus().String()
	}
	return &alpacaPb.BackgroundVerificationRecord{
		MatchData:                  getMatchData(verification.GetMatchData()),
		Action:                     amlAction,
		ActionBy:                   verification.GetActionBy(),
		ActionReason:               verification.GetActionReason(),
		ActionTakenAt:              verification.GetActionTakenAt().AsTime().Format(time.RFC3339),
		PepStatus:                  pepStatus,
		PepClassification:          verification.GetPepClassification(),
		AdverseMediaClassification: verification.GetAdverseMediaClassification(),
	}
}

func (g *UploadKYCDetailsReq) URL() string {
	return fmt.Sprintf("%v/%v/accounts/%v/cip", g.Conf.BrokerApiHost, g.Conf.BrokerApiVersion, g.Req.GetAccountId())
}

func (g *UploadKYCDetailsReq) HTTPMethod() string {
	return g.Method
}
func (g *UploadKYCDetailsReq) SetAuth(r *http.Request) *http.Request {
	r.SetBasicAuth(g.Conf.Secret.BrokerApiKey, g.Conf.Secret.BrokerApiSecret)
	return r
}
func (g *UploadKYCDetailsReq) ContentTypeString() string {
	return vendorapi.ContentTypeJSON
}

func (g *UploadKYCDetailsReq) GetResponse() vendorapi.Response {
	return &UploadKYCDetailsResp{}
}

func (g *UploadKYCDetailsReq) CanLogUnredactedEncryptedPayload() bool {
	return true
}

type UploadKYCDetailsResp struct {
}

func (g *UploadKYCDetailsResp) Unmarshal(b []byte) (proto.Message, error) {
	res := &alpacaPb.UploadCIPDetailResponse{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(b, res)
	if err != nil {
		return nil, err
	}
	return &stocksPb.UploadKYCDetailsResponse{
		Status: rpc.StatusOk(),
		KycId:  res.GetId(),
	}, nil
}

func (g *UploadKYCDetailsResp) HandleHttpError(_ context.Context, httpStatus int, b []byte) (proto.Message, error) {
	return nil, errors.New(fmt.Sprintf("http error, http_status = %v, response = %v", httpStatus, string(b)))
}

func (g *UploadKYCDetailsResp) CanLogUnredactedEncryptedPayload() bool {
	return true
}
