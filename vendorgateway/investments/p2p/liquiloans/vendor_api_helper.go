//nolint:goconst
package liquiloans

//nolint:depguard
import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/money"
	accountsPb "github.com/epifi/gamma/api/accounts"
	p2pPb "github.com/epifi/gamma/api/vendorgateway/investments/p2p"
	liquiloansPb "github.com/epifi/gamma/api/vendors/liquiloans"

	"github.com/golang/protobuf/ptypes/timestamp"
	"github.com/pkg/errors"
	"google.golang.org/protobuf/types/known/timestamppb"
)

const (
	dateTimeLayout   = "2006-01-02 15:04:05"
	dateLayout       = "2006-01-02"
	InvestorNotFound = "Investor not found/Investor not mapped to the ifa"
)

var (
	liquiloansTZ = datetime.IST
	// liquiloansSupportedLayouts is a list of supported timestamp layout for liquiloans APIs
	liquiloansSupportedLayouts = []string{
		"2006-01-02",
		"2006-01-02 15:04:05",
		"2006-01-02 15:04:05.000000",
		"2006-01-02 15:04:05.000",
		time.RFC3339Nano,
	}
	InvestorIdNullErr            = fmt.Errorf("investor cannot be null")
	TxnSubTypeStringToTxnSubType = map[string]p2pPb.TransactionSubType{
		"AddMoney":                       p2pPb.TransactionSubType_TRANSACTION_SUB_TYPE_ADD_MONEY,
		"WithdrawMoney":                  p2pPb.TransactionSubType_TRANSACTION_SUB_TYPE_WITHDRAW_MONEY,
		"Reinvestment":                   p2pPb.TransactionSubType_TRANSACTION_SUB_TYPE_REINVESTMENT,
		"VirtualRedemption":              p2pPb.TransactionSubType_TRANSACTION_SUB_TYPE_VIRTUAL_REDEMPTION,
		"Penalty":                        p2pPb.TransactionSubType_TRANSACTION_SUB_TYPE_PENALTY,
		"MonthlyInterestPayout":          p2pPb.TransactionSubType_TRANSACTION_SUB_TYPE_MONTHLY_INTEREST_PAYOUT,
		"PrincipalAndInterestRedemption": p2pPb.TransactionSubType_TRANSACTION_SUB_TYPE_PRINCIPAL_AND_INTEREST_REDEMPTION,
		"SchemeSwitch":                   p2pPb.TransactionSubType_TRANSACTION_SUB_TYPE_SCHEME_SWITCH,
	}
	TxnSubSubTypeStringToCashLedgerTxnSubSubType = map[string]p2pPb.CashLedgerTransactionSubSubType{
		"Scheme Switch":                       p2pPb.CashLedgerTransactionSubSubType_CL_TXN_SS_TYPE_SCHEME_SWITCH,
		"Investment":                          p2pPb.CashLedgerTransactionSubSubType_CL_TXN_SS_TYPE_INVESTMENT,
		"Reinvestment (Principal & Interest)": p2pPb.CashLedgerTransactionSubSubType_CL_TXN_SS_TYPE_REINVESTMENT_PRINCIPAL_AND_INTEREST,
		"Reinvestment (Principal)":            p2pPb.CashLedgerTransactionSubSubType_CL_TXN_SS_TYPE_REINVESTMENT_PRINCIPAL,
		"Partial Repayment":                   p2pPb.CashLedgerTransactionSubSubType_CL_TXN_SS_TYPE_PARTIAL_REPAYMENT,
		"Monthly Interest Repayment":          p2pPb.CashLedgerTransactionSubSubType_CL_TXN_SS_TYPE_MONTHLY_INTEREST_REPAYMENT,
		"Full Repayment":                      p2pPb.CashLedgerTransactionSubSubType_CL_TXN_SS_TYPE_FULL_REPAYMENT,
		"Repayment":                           p2pPb.CashLedgerTransactionSubSubType_CL_TXN_SS_TYPE_ONLY_REPAYMENT,
		"SWP Repayment":                       p2pPb.CashLedgerTransactionSubSubType_CL_TXN_SS_TYPE_SWP_REPAYMENT,
		"Penalty":                             p2pPb.CashLedgerTransactionSubSubType_CL_TXN_SS_TYPE_PENALTY,
		"Maturity - Principal & Interest Repayment (Virtual)": p2pPb.CashLedgerTransactionSubSubType_CL_TXN_SS_TYPE_MATURITY_PRINCIPAL_AND_INTEREST_REPAYMENT_VIRTUAL,
		"Maturity - Principal & Interest Repayment (Actual)":  p2pPb.CashLedgerTransactionSubSubType_CL_TXN_SS_TYPE_MATURITY_PRINCIPAL_AND_INTEREST_REPAYMENT,
		"Maturity - Principal Repayment (Virtual)":            p2pPb.CashLedgerTransactionSubSubType_CL_TXN_SS_TYPE_MATURITY_PRINCIPAL_REPAYMENT_VIRTUAL,
		"Maturity - Interest Repayment (Actual)":              p2pPb.CashLedgerTransactionSubSubType_CL_TXN_SS_TYPE_MATURITY_INTEREST_REPAYMENT,
		"SchemeSwitch":                                        p2pPb.CashLedgerTransactionSubSubType_CL_TXN_SS_TYPE_SCHEME_SWITCH,
		"Exposure":                                            p2pPb.CashLedgerTransactionSubSubType_CL_TXN_SS_TYPE_EXPOSURE,
		"Relending (Principal & Interest)":                    p2pPb.CashLedgerTransactionSubSubType_CL_TXN_SS_TYPE_RELENDING_PRINCIPAL_AND_INTEREST,
		"Relending (Principal)":                               p2pPb.CashLedgerTransactionSubSubType_CL_TXN_SS_TYPE_RELENDING_PRINCIPAL,
	}

	txnModeStringToTxnMode = map[p2pPb.TransactionMode]string{
		p2pPb.TransactionMode_TRANSACTION_MODE_NEFT:       "NEFT",
		p2pPb.TransactionMode_TRANSACTION_MODE_IMPS:       "IMPS",
		p2pPb.TransactionMode_TRANSACTION_MODE_RTGS:       "RTGS",
		p2pPb.TransactionMode_TRANSACTION_MODE_NETBANKING: "NETBANKING",
	}
)

func calculateChecksum(data string, key string) string {
	sig := hmac.New(sha256.New, []byte(key))
	sig.Write([]byte(data))
	return hex.EncodeToString(sig.Sum(nil))
}

func getTransactionTypeString(transactionType p2pPb.TransactionType) (string, error) {
	switch transactionType {
	case p2pPb.TransactionType_TRANSACTION_TYPE_CREDIT:
		return "Credit", nil
	case p2pPb.TransactionType_TRANSACTION_TYPE_DEBIT:
		return "Debit", nil
	default:
		return "", fmt.Errorf("invalid transaction type: %v", transactionType.String())
	}
}

func getTransactionType(transactionType string) (p2pPb.TransactionType, error) {
	if transactionType == "" {
		return p2pPb.TransactionType_TRANSACTION_TYPE_UNSPECIFIED, nil
	}
	switch transactionType {
	case "Credit":
		return p2pPb.TransactionType_TRANSACTION_TYPE_CREDIT, nil
	case "Debit":
		return p2pPb.TransactionType_TRANSACTION_TYPE_CREDIT, nil
	default:
		return p2pPb.TransactionType_TRANSACTION_TYPE_UNSPECIFIED, fmt.Errorf("unable to map transaction type: %v", transactionType)
	}
}

func getTransactionSubType(transactionSubType string) (p2pPb.TransactionSubType, error) {
	if transactionSubType == "" {
		return p2pPb.TransactionSubType_TRANSACTION_SUB_TYPE_UNSPECIFIED, nil
	}
	txnSubType, ok := TxnSubTypeStringToTxnSubType[transactionSubType]
	if !ok {
		return p2pPb.TransactionSubType_TRANSACTION_SUB_TYPE_UNSPECIFIED, fmt.Errorf("unable to map transactionSubSubType: %v", transactionSubType)
	}
	return txnSubType, nil
}

func getTransactionSubSubType(subSubType string) (p2pPb.CashLedgerTransactionSubSubType, error) {
	if subSubType == "" {
		return p2pPb.CashLedgerTransactionSubSubType_CL_TXN_SS_TYPE_UNSPECIFIED, nil
	}
	txnSubSubType, ok := TxnSubSubTypeStringToCashLedgerTxnSubSubType[subSubType]
	if !ok {
		return p2pPb.CashLedgerTransactionSubSubType_CL_TXN_SS_TYPE_UNSPECIFIED, fmt.Errorf("unable to map transactionSubSubType: %v", subSubType)
	}
	return txnSubSubType, nil
}

func getTransactionMode(transactionMode string) (p2pPb.TransactionMode, error) {
	if transactionMode == "" {
		return p2pPb.TransactionMode_TRANSACTION_MODE_UNSPECIFIED, nil
	}
	switch transactionMode {
	case "NEFT":
		return p2pPb.TransactionMode_TRANSACTION_MODE_NEFT, nil
	case "IMPS":
		return p2pPb.TransactionMode_TRANSACTION_MODE_IMPS, nil
	case "RTGS":
		return p2pPb.TransactionMode_TRANSACTION_MODE_RTGS, nil
	case "Netbanking", "NETBANKING":
		return p2pPb.TransactionMode_TRANSACTION_MODE_NETBANKING, nil
	default:
		return p2pPb.TransactionMode_TRANSACTION_MODE_UNSPECIFIED, fmt.Errorf("unable to map transactionMode: %s", transactionMode)
	}
}

func getTransactionModeString(txnMode p2pPb.TransactionMode) (string, error) {
	if _, ok := txnModeStringToTxnMode[txnMode]; !ok {
		return "", fmt.Errorf("unable to map transaction: %s", txnMode.String())
	}
	return txnModeStringToTxnMode[txnMode], nil
}

func getApprovalStatus(approvalStatus string) (p2pPb.ApprovalStatus, error) {
	if approvalStatus == "" {
		return p2pPb.ApprovalStatus_APPROVAL_STATUS_UNSPECIFIED, nil
	}

	switch strings.ToLower(approvalStatus) {
	case "pending":
		return p2pPb.ApprovalStatus_APPROVAL_STATUS_PENDING, nil
	case "approved":
		return p2pPb.ApprovalStatus_APPROVAL_STATUS_APPROVED, nil
	case "rejected":
		return p2pPb.ApprovalStatus_APPROVAL_STATUS_REJECTED, nil
	case "hold":
		return p2pPb.ApprovalStatus_APPROVAL_STATUS_HOLD, nil
	default:
		return p2pPb.ApprovalStatus_APPROVAL_STATUS_UNSPECIFIED, fmt.Errorf("unable to map approvalStatus: %v", approvalStatus)
	}
}

func getTransactionStatus(transactionStatus string, executionDate string) (p2pPb.TransactionStatus, error) {
	if transactionStatus == "" {
		return p2pPb.TransactionStatus_TRANSACTION_STATUS_UNSPECIFIED, nil
	}

	txnStatus := strings.ToLower(transactionStatus)

	// https://epifi.slack.com/archives/C037P0MRX8R/p1660072636608149?thread_ts=**********.426219&cid=C037P0MRX8R
	// Consider txn as executed when Execution date is present with status "processed"
	if executionDate == "" && (txnStatus == "banking" || txnStatus == "processed") {
		txnStatus = "approved"
	}

	switch txnStatus {
	case "pending":
		return p2pPb.TransactionStatus_TRANSACTION_STATUS_PENDING, nil
	case "approved", "executed":
		return p2pPb.TransactionStatus_TRANSACTION_STATUS_APPROVED, nil
	case "rejected":
		return p2pPb.TransactionStatus_TRANSACTION_STATUS_REJECTED, nil
	case "banking":
		return p2pPb.TransactionStatus_TRANSACTION_STATUS_BANKING, nil
	case "processed":
		return p2pPb.TransactionStatus_TRANSACTION_STATUS_EXECUTED, nil
	case "progress":
		return p2pPb.TransactionStatus_TRANSACTION_STATUS_PROGRESS, nil
	default:
		return p2pPb.TransactionStatus_TRANSACTION_STATUS_UNSPECIFIED, fmt.Errorf("unable to map transactionStatus: %v", transactionStatus)
	}
}

func getInvestmentStatus(investmentStatus string) (p2pPb.InvestmentStatus, error) {
	if investmentStatus == "" {
		return p2pPb.InvestmentStatus_INVESTMENT_STATUS_UNSPECIFIED, nil
	}

	switch strings.ToLower(investmentStatus) {
	case "approved":
		return p2pPb.InvestmentStatus_INVESTMENT_STATUS_APPROVED, nil
	case "closed":
		return p2pPb.InvestmentStatus_INVESTMENT_STATUS_CLOSED, nil
	case "rejected":
		return p2pPb.InvestmentStatus_INVESTMENT_STATUS_REJECTED, nil
	case "schemeclosed":
		return p2pPb.InvestmentStatus_INVESTMENT_STATUS_CLOSED, nil
	case "pending":
		return p2pPb.InvestmentStatus_INVESTMENT_STATUS_PENDING, nil
	default:
		return p2pPb.InvestmentStatus_INVESTMENT_STATUS_UNSPECIFIED, fmt.Errorf("unable to map transactionStatus: %v", investmentStatus)
	}
}

// nolint:unparam
func parseStringTimeStampProtoWithNil(layout string, datetimeString string) (*timestamp.Timestamp, error) {
	if datetimeString == "" {
		return nil, nil
	}
	t, err := datetime.ParseStringTimeStampProto(layout, datetimeString)
	if err != nil {
		return nil, errors.Wrap(err, "unable to parse timestamp")
	}
	return t, nil
}

// parseLiquiloansTSWithNil parses time from a given string on a best effort basis by checking for all supported layouts
func parseLiquiloansTSWithNil(datetimeString string) (*timestamppb.Timestamp, error) {
	if datetimeString == "" {
		return nil, nil
	}
	for _, layout := range liquiloansSupportedLayouts {
		ts, err := datetime.ParseStringTimestampProtoInLocation(layout, datetimeString, liquiloansTZ)
		if err == nil {
			return ts, nil
		}
	}

	return nil, fmt.Errorf("unable to parse timestamp %s layout not supported for Liquiloans", datetimeString)
}

func getDocumentType(documentType p2pPb.DocumentType) (string, error) {
	switch documentType {
	case p2pPb.DocumentType_INVESTOR_AGREEMENT:
		return "Investor Agreement", nil
	case p2pPb.DocumentType_PAN_CARD:
		return "Pan Card", nil
	case p2pPb.DocumentType_DOCUMENT_TYPE_MATURITY_CONSENT:
		return "Maturity Consent", nil
	default:
		return "", fmt.Errorf("invalid document type: %v", documentType.String())
	}
}

func actionTaken(actionTaken p2pPb.ActionTaken) string {
	actionTakenString := ""
	switch actionTaken {
	case p2pPb.ActionTaken_ACTION_TAKEN_TAKEN:
		actionTakenString = "yes"
	case p2pPb.ActionTaken_ACTION_TAKEN_NOT_TAKEN:
		actionTakenString = "no"
	case p2pPb.ActionTaken_ACTION_TAKEN_ALL:
		actionTakenString = ""
	}
	return actionTakenString
}

func getPayoutType(payout string) (p2pPb.PayoutType, error) {
	var payoutType p2pPb.PayoutType
	var err error
	switch strings.ToLower(payout) {
	case "growth":
		payoutType = p2pPb.PayoutType_PAYOUT_TYPE_GROWTH
	case "monthly":
		payoutType = p2pPb.PayoutType_PAYOUT_TYPE_MONTHLY
	default:
		err = fmt.Errorf("invalid payout from vendor, report this %v", payout)
	}
	return payoutType, err
}

func getMaturityType(maturity string) (p2pPb.MaturityType, error) {
	var maturityType p2pPb.MaturityType
	var err error
	switch strings.ToLower(maturity) {
	case "principalandinterestreinvestment":
		maturityType = p2pPb.MaturityType_MATURITY_TYPE_PRINCIPAL_AND_INTEREST_REINVESTMENT
	case "principalreinvestmentandinterestredemption":
		maturityType = p2pPb.MaturityType_MATURITY_TYPE_PRINCIPAL_REINVESTMENT_AND_INTEREST_REDEMPTION
	case "principalandinterestredemption":
		maturityType = p2pPb.MaturityType_MATURITY_TYPE_PRINCIPAL_AND_INTEREST_REDEMPTION
	default:
		err = fmt.Errorf("invalid maturity type from vendor, report this %v", maturity)
	}
	return maturityType, err
}

func getActionStatus(action string) (p2pPb.ActionStatus, error) {
	var actionStatus p2pPb.ActionStatus
	var err error
	switch strings.ToLower(action) {
	case "yes":
		actionStatus = p2pPb.ActionStatus_ACTION_STATUS_TAKEN
	case "no":
		actionStatus = p2pPb.ActionStatus_ACTION_STATUS_NOT_TAKEN
	default:
		err = fmt.Errorf("invalid action status from vendor, report this %v", action)
	}
	return actionStatus, err
}

func getMaturityTransactionData(investorMaturityTransactionsData *liquiloansPb.GetMaturityTransactionsData) (*p2pPb.MaturityTransactionData, error) {
	actionTakenOn, err := parseLiquiloansTSWithNil(investorMaturityTransactionsData.GetActionTakenOn())
	if err != nil {
		return nil, fmt.Errorf("failed to parse string timestamp to proto: actionTakenOn, %w", err)
	}
	transactionDate, err := parseLiquiloansTSWithNil(investorMaturityTransactionsData.GetTransactionDate())
	if err != nil {
		return nil, fmt.Errorf("failed to parse string timestamp to proto: transactionDate, %w", err)
	}
	expiryDate, err := parseLiquiloansTSWithNil(investorMaturityTransactionsData.GetExpiryDate())
	if err != nil {
		return nil, fmt.Errorf("failed to parse string timestamp to proto: expiryDate, %w", err)
	}
	actionStatus, err := getActionStatus(investorMaturityTransactionsData.GetActionStatus())
	if err != nil {
		return nil, fmt.Errorf("failed to parse action status, %w", err)
	}
	payoutType, err := getPayoutType(investorMaturityTransactionsData.GetCurrentPayoutType())
	if err != nil {
		return nil, fmt.Errorf("failed to parse payout type, %w", err)
	}
	var maturityType p2pPb.MaturityType
	var newPayoutType p2pPb.PayoutType
	if actionStatus == p2pPb.ActionStatus_ACTION_STATUS_TAKEN {
		var err error
		maturityType, err = getMaturityType(investorMaturityTransactionsData.GetMaturityType())
		if err != nil {
			return nil, fmt.Errorf("failed to parse maturity type, %w", err)
		}
		if maturityType != p2pPb.MaturityType_MATURITY_TYPE_PRINCIPAL_AND_INTEREST_REDEMPTION {
			newPayoutType, err = getPayoutType(investorMaturityTransactionsData.GetNewPayoutType())
			if err != nil {
				return nil, fmt.Errorf("failed to parse new payout type, %w", err)
			}
		}
	}
	return &p2pPb.MaturityTransactionData{
		InvestorId:        strconv.Itoa(int(investorMaturityTransactionsData.GetInvestorId())),
		LinkToken:         investorMaturityTransactionsData.GetLinkToken(),
		ActionTakenOn:     actionTakenOn,
		DaysToExpire:      investorMaturityTransactionsData.GetDaysToExpire(),
		TransactionDate:   transactionDate,
		ExpiryDate:        expiryDate,
		InvestorName:      investorMaturityTransactionsData.GetInvestorName(),
		PortfolioId:       fmt.Sprint(investorMaturityTransactionsData.GetPortfolioId()),
		CurrentMhp:        investorMaturityTransactionsData.GetCurrentMhp(),
		CurrentPayoutType: payoutType,
		MaturityType:      maturityType,
		NewMhp:            investorMaturityTransactionsData.GetNewMhp(),
		Principal:         money.ParseFloat(investorMaturityTransactionsData.GetPrincipal(), money.RupeeCurrencyCode),
		NewPayoutType:     newPayoutType,
		ActionStatus:      actionStatus,
	}, nil
}

func getPayoutTypeString(payout p2pPb.PayoutType) (string, error) {
	var payoutType string
	var err error
	switch payout {
	case p2pPb.PayoutType_PAYOUT_TYPE_GROWTH:
		payoutType = "Growth"
	case p2pPb.PayoutType_PAYOUT_TYPE_MONTHLY:
		payoutType = "Monthly"
	default:
		err = fmt.Errorf("invalid payout type %v", payout)
	}
	return payoutType, err
}

func getMaturityTypeString(maturity p2pPb.MaturityType) (string, error) {
	var maturityType string
	var err error
	switch maturity {
	case p2pPb.MaturityType_MATURITY_TYPE_PRINCIPAL_AND_INTEREST_REINVESTMENT:
		maturityType = "PrincipalAndInterestReinvestment"
	case p2pPb.MaturityType_MATURITY_TYPE_PRINCIPAL_REINVESTMENT_AND_INTEREST_REDEMPTION:
		maturityType = "PrincipalReinvestmentAndInterestRedemption"
	case p2pPb.MaturityType_MATURITY_TYPE_PRINCIPAL_AND_INTEREST_REDEMPTION:
		maturityType = "PrincipalAndInterestRedemption"
	default:
		err = fmt.Errorf("invalid maturity type %v", maturity)
	}
	return maturityType, err
}

func boolToString(val bool) string {
	var stringVal string
	switch val {
	case true:
		stringVal = "Yes"
	case false:
		stringVal = "No"
	}
	return stringVal
}

func stringToBool(val string) (bool, error) {
	switch strings.ToLower(val) {
	case "yes":
		return true, nil
	case "no":
		return false, nil
	default:
		return false, fmt.Errorf("invalid string value %v", val)
	}
}

func getAccountTypeFromString(s string) accountsPb.Type {
	switch strings.ToLower(s) {
	case "savings":
		return accountsPb.Type_SAVINGS
	case "current":
		return accountsPb.Type_CURRENT
	default:
		return accountsPb.Type_TYPE_UNSPECIFIED
	}
}

func getKycStatusFromString(s string) (p2pPb.InvestorKycStatus, error) {
	switch strings.ToLower(s) {
	case "verified":
		return p2pPb.InvestorKycStatus_KYC_STATUS_VERIFIED, nil
	case "notverified":
		return p2pPb.InvestorKycStatus_KYC_STATUS_NOT_VERIFIED, nil
	default:
		return p2pPb.InvestorKycStatus_KYC_STATUS_UNSPECIFIED, fmt.Errorf("invalid kyc status %v", s)
	}
}
