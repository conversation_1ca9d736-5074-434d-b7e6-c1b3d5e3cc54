package fcm

import (
	"context"
	b64 "encoding/base64"
	"time"

	"firebase.google.com/go/messaging"
	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"

	commsPb "github.com/epifi/gamma/api/comms"
	fePb "github.com/epifi/gamma/api/frontend/fcm"
	fcmPb "github.com/epifi/gamma/api/vendorgateway/fcm"
	"github.com/epifi/gamma/pkg/deeplink"
	"github.com/epifi/gamma/vendorgateway/metrics"
)

type IFcmClient interface {
	Send(ctx context.Context, message *messaging.Message) (string, error)
}

type Service struct {
	msgClient      IFcmClient
	analyticsLabel string
}

func NewFCMService(msgClient IFcmClient, label string) *Service {
	return &Service{msgClient: msgClient, analyticsLabel: label}
}

const defaultSound = "default"

func (s *Service) SendMessage(ctx context.Context, req *fcmPb.SendMessageRequest) (*fcmPb.SendMessageResponse, error) {
	dataMap := req.GetNotificationMessage().GetCustomPayload().GetData()
	if dataMap == nil {
		dataMap = make(map[string]string)
	}
	if req.GetNotificationMessage().GetDeeplink() != nil {
		serializedDeeplink, err := deeplink.Generate(req.GetNotificationMessage().GetDeeplink())
		if err != nil {
			logger.Error(ctx, "cannot serialize deep link passed in request", zap.Any("request", req), zap.Error(err))
			return &fcmPb.SendMessageResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("cannot serialize deep link passed in request"),
			}, nil
		}
		dataMap[fePb.NotificationCustomKeys_DEEPLINK.String()] = *serializedDeeplink
	}
	ttl := time.Duration(req.GetNotificationMessage().GetAndroidConfig().GetTimeToLive()) * time.Second
	priority := "normal"
	if req.GetNotificationMessage().GetPriority() == commsPb.NotificationPriority_HIGH {
		priority = "high"
	}

	notificationPayload, err := proto.Marshal(req.GetNotificationMessage().GetNotification())
	if err != nil {
		logger.Error(ctx, "cannot convert notification message to bytes", zap.Error(err))
	}
	dataMap["notification_payload"] = b64.StdEncoding.EncodeToString(notificationPayload)
	analyticsLabel := s.analyticsLabel
	if req.GetAnalyticsLabel() != "" {
		analyticsLabel = req.GetAnalyticsLabel()
	}
	message := &messaging.Message{
		Notification: &messaging.Notification{
			Title:    req.GetNotificationMessage().GetTitle(),
			Body:     req.GetNotificationMessage().GetBody(),
			ImageURL: req.GetNotificationMessage().GetImageUrl(),
		},
		Token: req.GetDeviceToken(),
		Android: &messaging.AndroidConfig{
			Priority: priority,
			TTL:      &ttl,
		},
		Data:       dataMap,
		FCMOptions: &messaging.FCMOptions{AnalyticsLabel: analyticsLabel},
	}
	enrichMessageWithAPNS(ctx, req.GetNotificationMessage().GetNotification(), message)
	response, err := s.msgClient.Send(context.Background(), message)
	if err != nil {
		logger.Error(ctx, "cannot send notification via fcm", zap.Error(err))
		return getSendMessageResponseWithSubStatus(err), err
	}
	metrics.IncrementSentNotificationCount(req.GetHeader().GetVendor().String())
	return &fcmPb.SendMessageResponse{
		Status:         rpcPb.StatusOk(),
		SubStatus:      commsPb.CommsMessageSubStatus_COMMS_MESSAGE_SUB_STATUS_NOTIFICATION_DELIVERED,
		NotificationId: response,
	}, nil
}

// For Android notifications, we send entire notification payload in data object to FCM server
// Client will parse the entire payload from data map and render it.
// Rendering is different on Android and IOS
// While on android all types of notifications are supported without custom handling at backend
// It is not the case with IOS. For notifications like IN_APP_FULLSCREEN and BACKGROUND we need to
// trigger them as silent notifications which is done by setting content available value to 1
// For all other types of notifications, IOS needs the title and body in the APNS object
// enrichment for APNS config is done on best effort basis and no error is thrown since
// until we are sure of IOS notifications working or not we cannot break existing android functionality
func enrichMessageWithAPNS(ctx context.Context, notification *fePb.Notification, message *messaging.Message) {
	switch notification.GetNotificationType() {
	case fePb.NotificationType_IN_APP_FULLSCREEN, fePb.NotificationType_BACKGROUND:
		message.APNS = &messaging.APNSConfig{
			Payload: &messaging.APNSPayload{Aps: &messaging.Aps{ContentAvailable: true}},
		}
	// nolint:dupl
	case fePb.NotificationType_SYSTEM_TRAY:
		systemTrayTemplate := notification.GetSystemTrayTemplate()
		if systemTrayTemplate == nil {
			logger.Error(ctx, "cannot get system tray template from notification", zap.String("notification", notification.String()))
			break
		}
		message.APNS = &messaging.APNSConfig{
			Payload: &messaging.APNSPayload{
				Aps: &messaging.Aps{
					Alert: &messaging.ApsAlert{
						Title: systemTrayTemplate.GetCommonTemplateFields().GetTitle(),
						Body:  systemTrayTemplate.GetCommonTemplateFields().GetBody(),
					},
					Sound:    defaultSound,
					Category: notification.GetNotificationType().String(),
				},
			},
		}
	// nolint:dupl
	case fePb.NotificationType_FULL_SCREEN:
		fullScreenTemplate := notification.GetFullscreenTemplate()
		if fullScreenTemplate == nil {
			logger.Error(ctx, "cannot get fullscreen template from notification", zap.String("notification", notification.String()))
			break
		}
		message.APNS = &messaging.APNSConfig{
			Payload: &messaging.APNSPayload{
				Aps: &messaging.Aps{
					Alert: &messaging.ApsAlert{
						Title: fullScreenTemplate.GetCommonTemplateFields().GetTitle(),
						Body:  fullScreenTemplate.GetCommonTemplateFields().GetBody(),
					},
					Sound:    defaultSound,
					Category: notification.GetNotificationType().String(),
				},
			},
		}
	default:
		logger.Error(ctx, "unimplemented notification type", zap.String(logger.NOTIFICATION_TYPE, notification.GetNotificationType().String()))
		return
	}
}

func getSendMessageResponseWithSubStatus(err error) *fcmPb.SendMessageResponse {
	switch {
	case messaging.IsInternal(err):
		return &fcmPb.SendMessageResponse{
			Status:    rpcPb.StatusInternalWithDebugMsg("cannot send notification via fcm, internal error"),
			SubStatus: commsPb.CommsMessageSubStatus_COMMS_MESSAGE_SUB_STATUS_NOTIFICATION_INTERNAL_ERROR,
		}
	case messaging.IsInvalidAPNSCredentials(err):
		return &fcmPb.SendMessageResponse{
			Status:    rpcPb.StatusInvalidArgumentWithDebugMsg("cannot send notification via fcm, invalid apns credentials"),
			SubStatus: commsPb.CommsMessageSubStatus_COMMS_MESSAGE_SUB_STATUS_NOTIFICATION_INVALID_APNS_CREDENTIALS,
		}
	case messaging.IsInvalidArgument(err):
		return &fcmPb.SendMessageResponse{
			Status:    rpcPb.StatusInvalidArgumentWithDebugMsg("cannot send notification via fcm, invalid argument"),
			SubStatus: commsPb.CommsMessageSubStatus_COMMS_MESSAGE_SUB_STATUS_NOTIFICATION_INVALID_ARGUMENT,
		}
	case messaging.IsMessageRateExceeded(err):
		return &fcmPb.SendMessageResponse{
			Status:    rpcPb.StatusInternalWithDebugMsg("cannot send notification via fcm, message rate exceeded"),
			SubStatus: commsPb.CommsMessageSubStatus_COMMS_MESSAGE_SUB_STATUS_NOTIFICATION_MESSAGE_RATE_EXCEEDED,
		}
	case messaging.IsMismatchedCredential(err):
		return &fcmPb.SendMessageResponse{
			Status:    rpcPb.StatusInternalWithDebugMsg("cannot send notification via fcm, mismatched credential"),
			SubStatus: commsPb.CommsMessageSubStatus_COMMS_MESSAGE_SUB_STATUS_NOTIFICATION_MISMATCHED_CREDENTIAL,
		}
	case messaging.IsRegistrationTokenNotRegistered(err):
		return &fcmPb.SendMessageResponse{
			Status:    rpcPb.StatusRecordNotFoundWithDebugMsg("token not registered"),
			SubStatus: commsPb.CommsMessageSubStatus_COMMS_MESSAGE_SUB_STATUS_NOTIFICATION_REGISTRATION_TOKEN_NOT_REGISTERED,
		}
	case messaging.IsServerUnavailable(err):
		return &fcmPb.SendMessageResponse{
			Status:    rpcPb.StatusInternalWithDebugMsg("cannot send notification via fcm, server unavailable"),
			SubStatus: commsPb.CommsMessageSubStatus_COMMS_MESSAGE_SUB_STATUS_NOTIFICATION_SERVER_UNAVAILABLE,
		}
	case messaging.IsTooManyTopics(err):
		return &fcmPb.SendMessageResponse{
			Status:    rpcPb.StatusInternalWithDebugMsg("cannot send notification via fcm, too many topics"),
			SubStatus: commsPb.CommsMessageSubStatus_COMMS_MESSAGE_SUB_STATUS_NOTIFICATION_TOO_MANY_TOPICS,
		}
	case messaging.IsUnknown(err):
		return &fcmPb.SendMessageResponse{
			Status:    rpcPb.StatusInternalWithDebugMsg("cannot send notification via fcm, unknown error"),
			SubStatus: commsPb.CommsMessageSubStatus_COMMS_MESSAGE_SUB_STATUS_NOTIFICATION_UNKNOWN_ERROR,
		}
	default:
		return &fcmPb.SendMessageResponse{
			Status:    rpcPb.StatusInternalWithDebugMsg("cannot send notification via fcm"),
			SubStatus: commsPb.CommsMessageSubStatus_COMMS_MESSAGE_SUB_STATUS_UNSPECIFIED,
		}
	}
}

type MockFcmService struct{}

func NewMockFcmService() *MockFcmService {
	return &MockFcmService{}
}

func (m *MockFcmService) Send(ctx context.Context, message *messaging.Message) (string, error) {
	return "test-fcm-id", nil
}
