package processor

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/mask"

	cxDsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	leadsPb "github.com/epifi/gamma/api/leads"
	devPb "github.com/epifi/gamma/api/leads/developer"
	"github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/leads/dao"
	"github.com/epifi/gamma/leads/dao/model"
)

const (
	userLeadIDParam              = "id"
	userLeadClientRequestIDParam = "client_request_id"
	userLeadClientIDParam        = "client_id"
	userLeadMobileNumberParam    = "mobile_number"
	userLeadPanParam             = "pan"
	userLeadEmailParam           = "email"
	userLeadActorIDParam         = "actor_id"
)

type UserLeadProcessor struct {
	userLeadDao dao.UserLeadDao
}

func NewUserLeadProcessor(userLeadDao dao.UserLeadDao) *UserLeadProcessor {
	return &UserLeadProcessor{userLeadDao: userLeadDao}
}

func (p *UserLeadProcessor) FetchParamList(ctx context.Context, entity devPb.LeadsEntity) ([]*cxDsPb.ParameterMeta, error) {
	if entity != devPb.LeadsEntity_LEAD {
		return nil, fmt.Errorf("invalid entity type: %v for user lead processor", entity)
	}
	return []*cxDsPb.ParameterMeta{
		{
			Name:            userLeadIDParam,
			Label:           "Lead ID",
			Type:            cxDsPb.ParameterDataType_STRING,
			ParameterOption: cxDsPb.ParameterOption_OPTIONAL,
		},
		{
			Name:            userLeadClientRequestIDParam,
			Label:           "Client Request ID",
			Type:            cxDsPb.ParameterDataType_STRING,
			ParameterOption: cxDsPb.ParameterOption_OPTIONAL,
		},
		{
			Name:            userLeadClientIDParam,
			Label:           "Client ID",
			Type:            cxDsPb.ParameterDataType_STRING,
			ParameterOption: cxDsPb.ParameterOption_OPTIONAL,
		},
		{
			Name:            userLeadMobileNumberParam,
			Label:           "10 digit Mobile Number",
			Type:            cxDsPb.ParameterDataType_STRING,
			ParameterOption: cxDsPb.ParameterOption_OPTIONAL,
		},
		{
			Name:            userLeadPanParam,
			Label:           "PAN",
			Type:            cxDsPb.ParameterDataType_STRING,
			ParameterOption: cxDsPb.ParameterOption_OPTIONAL,
		},
		{
			Name:            userLeadEmailParam,
			Label:           "Email",
			Type:            cxDsPb.ParameterDataType_STRING,
			ParameterOption: cxDsPb.ParameterOption_OPTIONAL,
		},
		{
			Name:            userLeadActorIDParam,
			Label:           "Actor ID",
			Type:            cxDsPb.ParameterDataType_STRING,
			ParameterOption: cxDsPb.ParameterOption_OPTIONAL,
		},
	}, nil
}

func (p *UserLeadProcessor) FetchData(ctx context.Context, entity devPb.LeadsEntity, filters []*cxDsPb.Filter) (string, error) {
	if entity != devPb.LeadsEntity_LEAD {
		return "", fmt.Errorf("invalid entity type: %v for user lead processor", entity)
	}

	var id, clientRequestID, clientID, mobileNumber, pan, email, actorID string

	for _, filter := range filters {
		switch filter.GetParameterName() {
		case userLeadIDParam:
			if stringValue := filter.GetStringValue(); stringValue != "" {
				id = stringValue
			}
		case userLeadClientRequestIDParam:
			if stringValue := filter.GetStringValue(); stringValue != "" {
				clientRequestID = stringValue
			}
		case userLeadClientIDParam:
			if stringValue := filter.GetStringValue(); stringValue != "" {
				clientID = stringValue
			}
		case userLeadMobileNumberParam:
			if stringValue := filter.GetStringValue(); stringValue != "" {
				mobileNumber = stringValue
			}
		case userLeadPanParam:
			if stringValue := filter.GetStringValue(); stringValue != "" {
				pan = stringValue
			}
		case userLeadEmailParam:
			if stringValue := filter.GetStringValue(); stringValue != "" {
				email = stringValue
			}
		case userLeadActorIDParam:
			if stringValue := filter.GetStringValue(); stringValue != "" {
				actorID = stringValue
			}
		}
	}

	var userLeads []*leadsPb.UserLead

	switch {
	case id != "":
		lead, getErr := p.userLeadDao.GetById(ctx, id)
		if getErr != nil {
			return "", errors.Wrap(getErr, "failed to get user lead by id")
		}
		if lead != nil {
			userLeads = append(userLeads, lead)
		}
	case clientRequestID != "" && clientID != "":
		lead, getErr := p.userLeadDao.GetByClientReqIdAndClientId(ctx, clientRequestID, clientID)
		if getErr != nil {
			return "", errors.Wrap(getErr, "failed to get user lead by client_request_id and client_id")
		}
		if lead != nil {
			userLeads = append(userLeads, lead)
		}
	case mobileNumber != "" || pan != "" || email != "" || actorID != "":
		filterModel := &model.UserLeadFilter{
			MobileNumber: mobileNumber,
			PAN:          pan,
			Email:        email,
			ActorID:      actorID,
		}
		productTypes := []leadsPb.ProductType{leadsPb.ProductType_PRODUCT_TYPE_FI_PERSONAL_LOAN}
		leads, getErr := p.userLeadDao.GetUserLeadsByFilter(ctx, filterModel, productTypes, false)
		if getErr != nil {
			return "", errors.Wrap(getErr, "failed to get active user leads by filter")
		}
		userLeads = leads
	default:
		return "", errors.New("insufficient parameters to fetch user leads. Provide id, or (client_request_id and client_id), or at least one of (mobile_number, pan, email, actor_id)")
	}

	if len(userLeads) == 0 {
		return "[]", nil
	}

	return maskAndMarshalUserLeads(userLeads)
}

//nolint:gosec
func maskAndMarshalUserLeads(userLeads []*leadsPb.UserLead) (string, error) {
	for _, userLead := range userLeads {
		if userLead.GetPan() != "" {
			userLead.Pan = mask.GetMaskedString(mask.DontMaskFirstTwoAndLastTwoChars, userLead.GetPan())
		}
		if userLead.GetPersonalDetails() != nil {
			if userLead.GetPersonalDetails().GetCurrentAddress() != nil {
				beAddress := userLead.GetPersonalDetails().GetCurrentAddress().GetBeAddress()
				userLead.GetPersonalDetails().CurrentAddress = typesv2.GetFromBeAddress(mask.MaskAddressDetails(beAddress))
			}
			if userLead.GetPersonalDetails().GetEmploymentDetails() != nil {
				if userLead.GetPersonalDetails().GetEmploymentDetails().GetMonthlyIncome() != nil {
					userLead.GetPersonalDetails().GetEmploymentDetails().MonthlyIncome = mask.GetMaskedMoney(userLead.GetPersonalDetails().GetEmploymentDetails().GetMonthlyIncome())
				}
			}
			if userLead.GetPersonalDetails().GetDob() != nil {
				userLead.GetPersonalDetails().GetDob().Day = int32(mask.GetMaskedInt(userLead.GetPersonalDetails().GetDob().GetDay()))
				userLead.GetPersonalDetails().GetDob().Month = int32(mask.GetMaskedInt(userLead.GetPersonalDetails().GetDob().GetMonth()))
			}
		}
	}

	jsonData, err := json.Marshal(userLeads)
	if err != nil {
		return "", errors.Wrap(err, "failed to marshal user leads to json")
	}

	return string(jsonData), nil
}
