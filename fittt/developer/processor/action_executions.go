package processor

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	actionpb "github.com/epifi/gamma/api/fittt/action"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/fittt/developer"
	devPb "github.com/epifi/gamma/api/fittt/developer"
	"github.com/epifi/gamma/fittt/action_processor/dao"
)

const (
	actionExecutionIdParam = "execution_id"
	IdParam                = "id"
	limitParam             = "limit"
	actionTypesParam       = "action_types"
)

type DevActionExecutions struct {
	executionsDao dao.ActionExecutionDao
}

func NewDevActionExecutions(executionsDao dao.ActionExecutionDao) *DevActionExecutions {
	return &DevActionExecutions{executionsDao: executionsDao}
}

func (d *DevActionExecutions) FetchParamList(ctx context.Context, entity devPb.FITTTEntity) ([]*db_state.ParameterMeta, error) {
	var actionTypesOptions []string
	for t := range actionpb.ActionType_value {
		if t == actionpb.ActionType_ACTION_TYPE_UNSPECIFIED.String() {
			// skip unspecified as this isn't useful in sherlock db states filter
			continue
		}
		actionTypesOptions = append(actionTypesOptions, t)
	}
	paramList := []*db_state.ParameterMeta{
		{
			Name:            IdParam,
			Label:           "id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            actionExecutionIdParam,
			Label:           "execution_id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            actorIdParam,
			Label:           "actor_id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            actionTypesParam,
			Label:           "action_types",
			Type:            db_state.ParameterDataType_MULTI_SELECT_DROPDOWN,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
			Options:         actionTypesOptions,
		},
		{
			Name:            eventIdParam,
			Label:           "event_ids as JSON array",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            subscriptionIdParam,
			Label:           "Subscription ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            createdAfterParam,
			Label:           "created_after - RFC3339 format",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            createdBeforeParam,
			Label:           "created_before - RFC3339 format",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            limitParam,
			Label:           "limit",
			Type:            db_state.ParameterDataType_INTEGER,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
	}
	return paramList, nil
}

func (d *DevActionExecutions) FetchData(ctx context.Context, entity developer.FITTTEntity, filters []*db_state.Filter) (string, error) {
	id, execId, actorId, subsId, eventIDs, actionTypes, createdAfter, createdBefore, limit, err := d.getParams(ctx, filters)
	if err != nil {
		return "", err
	}
	var execs []*actionpb.ActionExecution
	logger.Info(ctx, "Action execution dev action", zap.Any(logger.EXECUTION_ID, execId), zap.Any("actorId", actorId),
		zap.Any("eventIDs", eventIDs), zap.Any("createdAfter", createdAfter),
		zap.Any("createdBefore", createdBefore), zap.Any("limit", limit))
	switch {
	case id != "":
		actionExec, err2 := d.executionsDao.GetByID(ctx, id)
		if err2 != nil {
			logger.Error(ctx, "error while fetching action execution from fit service",
				zap.String(logger.EXECUTION_ID, id), zap.Error(err2))
			return fmt.Sprintf("{\"error\": \"error while fetching action execution from fit service \", \"error\":\"%v\"}", err2.Error()), nil
		}
		execs = []*actionpb.ActionExecution{actionExec}
	case execId != "":
		actionExec, notFound, err2 := d.executionsDao.GetByExecutionID(ctx, execId)
		if err2 != nil || notFound {
			logger.Error(ctx, "error while fetching action execution from fit service",
				zap.String(logger.EXECUTION_ID, execId), zap.Error(err2))
			if notFound {
				return "{\"error\": \"error while fetching action execution from fit service \"}", nil
			}
			return fmt.Sprintf("{\"error\": \"error while fetching action execution from fit service \", \"error\":\"%v\"}", err2.Error()), nil
		}
		execs = []*actionpb.ActionExecution{actionExec}
	default:
		if actorId == "" {
			logger.Error(ctx, "both actor id and execution id cannot be empty")
			return "", fmt.Errorf("both actor id and execution id cannot be empty")
		}
		execs, _, err = d.executionsDao.GetExecutions(ctx, actorId, subsId, eventIDs, actionTypes, nil, createdAfter, createdBefore, nil,
			uint32(limit), nil)
		if err != nil {
			logger.Error(ctx, "error while fetching action execution from fit db", zap.Error(err))
			return fmt.Sprintf("{\"error\": \"error while fetching action execution from fit service \", \"error\":\"%v\"}", err.Error()), nil
		}
	}

	marshalOptions := protojson.MarshalOptions{}
	marshalOptions.UseEnumNumbers = false
	var elements []string
	for _, exec := range execs {
		e, err := marshalOptions.Marshal(exec)
		if err != nil {
			logger.Error(ctx, "cannot marshal action execution to json", zap.Error(err))
			return fmt.Sprintf("{\"error\": \"cannot marshal action execution to json \", \"error\":\"%v\"}", err.Error()), nil
		}
		elements = append(elements, string(e))
	}
	return "[" + strings.Join(elements, ",") + "]", nil
}

func (d *DevActionExecutions) getParams(ctx context.Context, filters []*db_state.Filter) (id string, execId string, actorId string,
	subsId string, eventIds []string, actionTypes []actionpb.ActionType, createdAfter *timestamppb.Timestamp, createdBefore *timestamppb.Timestamp, limit int, err error) {
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case IdParam:
			id = filter.GetStringValue()
		case actionExecutionIdParam:
			execId = filter.GetStringValue()
		case actorIdParam:
			actorId = filter.GetStringValue()
		case subscriptionIdParam:
			subsId = filter.GetStringValue()
		case eventIdParam:
			if filter.GetStringValue() == "" {
				continue
			}
			err := json.Unmarshal([]byte(filter.GetStringValue()), &eventIds)
			if err != nil {
				logger.Error(ctx, "failed to parse eventId", zap.Error(err))
				return "", "", "", "", nil, nil, nil, nil, 0, fmt.Errorf("invalid json array for event ids. %w", err)
			}
		case actionTypesParam:
			for _, val := range filter.GetMultiSelectDropdownFilter().DropdownValues {
				actionTypes = append(actionTypes, actionpb.ActionType(actionpb.ActionType_value[val]))
			}

		case createdAfterParam:
			if filter.GetStringValue() == "" {
				continue
			}
			t, err := time.Parse(time.RFC3339, filter.GetStringValue())
			if err != nil {
				logger.Error(ctx, "failed to parse createdAfter", zap.Error(err))
				return "", "", "", "", nil, nil, nil, nil, 0, fmt.Errorf("invalid RFC3339 format created after. %w", err)
			}
			createdAfter = timestamppb.New(t)
		case createdBeforeParam:
			if filter.GetStringValue() == "" {
				continue
			}
			t, err := time.Parse(time.RFC3339, filter.GetStringValue())
			if err != nil {
				logger.Error(ctx, "failed to parse createdBefore", zap.Error(err))
				return "", "", "", "", nil, nil, nil, nil, 0, fmt.Errorf("invalid RFC3339 format created before. %w", err)
			}
			createdBefore = timestamppb.New(t)
		case limitParam:
			limit = int(filter.GetIntegerValue())
		}
	}
	return
}
