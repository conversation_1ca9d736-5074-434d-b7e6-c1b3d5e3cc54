package notifications

import (
	"flag"
	"os"
	"testing"

	"github.com/epifi/gamma/investment/test"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer

// nolint
func TestMain(m *testing.M) {
	flag.Parse()

	conf, _, teardown := test.InitTestServer()

	svcTS = newSvcTestSuite(conf)

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
