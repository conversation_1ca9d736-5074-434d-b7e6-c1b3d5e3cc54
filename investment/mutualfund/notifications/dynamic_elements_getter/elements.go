package dynamic_elements_getter

import (
	"fmt"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	types "github.com/epifi/gamma/api/typesv2"
	accrualPkg "github.com/epifi/gamma/pkg/accrual"

	"github.com/google/uuid"

	"github.com/epifi/gamma/api/typesv2/ui"

	"github.com/epifi/gamma/api/accounts"
	dePb "github.com/epifi/gamma/api/dynamic_elements"
	deeplink2 "github.com/epifi/gamma/api/frontend/deeplink"
	fittClientState "github.com/epifi/gamma/api/frontend/fittt/clientstate"
	"github.com/epifi/gamma/api/frontend/investment/mutualfund/clientstates"
	"github.com/epifi/gamma/investment/mutualfund/notifications/actor_segmentation"
)

const (
	saveChangeDynamicElementTitle = "SAVE WHILE YOU SPEND"
	saveChangeDynamicElementBody  = "Invest spare change in a Mutual Fund every time you spend. Try now"
	saveChangeDynamicElementIcon  = "https://epifi-icons.pointz.in/investments/100rs-banner.png"

	startSmallDynamicElementTitle        = "ZERO COMMISSION INVESTING"
	startSmallDynamicElementTitleV2      = "BUILD AN INVESTMENT HABIT"
	startSmallDynamicElementBody         = "Start investing as small as ₹100 with our new collection!"
	startSmallDynamicElementBodyV2       = "Start a monthly SIP with just ₹100 in these top rated funds"
	startSmallDynamicElementIcon         = "https://epifi-icons.pointz.in/investments/100rs-banner.png"
	startSmallDynamicElementCollectionId = "MFCOLL11Wda9BHSQTM2gbsBkJ7SrPA=="

	tenOnlyDynamicElementTitle        = "BUILD AN INVESTMENT HABIT"
	tenOnlyDynamicElementBody         = "Set up a weekly SIP with just ₹10 with these top Navi Funds"
	tenOnlyDynamicElementIcon         = "https://epifi-icons.pointz.in/investments/100rs-banner.png"
	tenOnlyDynamicElementCollectionId = "MFCOLL221017pxCtEqYYTNmYVMBnIBTu1w=="

	growWealthDynamicElementTitle  = "GROW YOUR WEALTH"
	growWealthDynamicElementBody   = "Build your first 1 lakh by investing ₹500 every week in this mutual fund"
	growWealthDynamicElementIcon   = "https://epifi-icons.pointz.in/investments/coinStackWhitebackground.png"
	growWealthDynamicElementMfId   = "MF220528Hpqh+uocQ2mZdwC7+Ry+ZQ=="
	growWealthDynamicElementRuleId = "42286516-f4d1-421f-9c1c-1a7f65612e66"

	autoSaveWeeklyRuleId           = "7b251ad6-a653-4c8a-88fa-a3714b45b480"
	autoSaveMonthlyRuleId          = "bb093acb-fb94-4aa4-abe6-1d85a91feba2"
	notSureWhereToStart            = "NOT SURE WHERE TO BEGIN?"
	popularFundsDEBody             = "Find popular funds from SBI, Tata, Quant, Navi & more. Invest now!"
	popularFundsCollectionId       = "MFCOLL220705ppIeeb4rR+SkodNlVDAijg=="
	popularFundsCollectionBannerId = "https://epifi-icons.pointz.in/popular-funds-banner.png"

	buildInvestmentHabitDynamicElementTitle = "MAKE INVESTMENT A HABIT"
	buildInvestmentHabitDynamicElementBody  = "Beat the market with just INR 100. Set up a monthly SIP now"
	buildInvestmentHabitDynamicElementIcon  = "https://epifi-icons.pointz.in/investments/landing/investment_habit_coins.png"
	buildInvestmentHabitDynamicElementMfId  = "MF2205300P8I0iJMRYeW+q6trEt8JA=="

	buildInvestmentHabitDynamicElementTitleNAVI  = "BUILD AN INVESTMENT HABIT"
	buildInvestmentHabitDynamicElementBodyNAVI   = "Start a weekly ₹10 SIP in the NAVI Nifty 50 Index Fund"
	buildInvestmentHabitDynamicElementIconNAVI   = "https://epifi-icons.pointz.in/amc_logos/navi.png"
	buildInvestmentHabitDynamicElementMfIdNAVI   = "MF220628OSPney6ORlC24uB7Ej6h9g=="
	buildInvestmentHabitDynamicElementRuleIdNAVI = "42286516-f4d1-421f-9c1c-1a7f65612e66"

	saveTaxesDynamicElementTitle = "TAX PLANNING PENDING?"
	saveTaxesElementBody         = "Invest in ELSS funds - save taxes with lowest lock-in and higher returns"
	saveTaxesIconUrl             = "https://epifi-icons.pointz.in/investments/Pad.png"
	saveTaxesCollectionId        = "MFCOLL11q30wxq/uS0SO98y2Fqzr2w=="

	lowOnCashDynamicElementTitle = "LOW ON CASH?"
	lowOnCashDynamicElementBody  = "Set up a weekly SIP with just ₹10 with these top Navi Funds"
	lowOnCashDynamicElementUrl   = "https://epifi-icons.pointz.in/investments/cash_tilt.png"

	salaryReceivedDynamicElementTitle = "SALARY RECEIVED?"
	salaryReceivedDynamicElementBody  = "Start a monthly SIP with just ₹100 in these top rated funds"
	salaryReceivedDynamicElementUrl   = "https://epifi-icons.pointz.in/investments/Cash100(2).png"
	babyStepsCollectionId             = "MFCOLL11Wda9BHSQTM2gbsBkJ7SrPA=="

	jumpDynamicElementTitle = "POPULAR"
	jumpDynamicElementBody  = "Invest in Jump & get 2X more returns than your savings account"
	jumpDynamicElementUrl   = "https://epifi-icons.pointz.in/investments/landing/jump_blue_banner.png"

	goldFundsCollectionId       = "MFCOLL221031Uagf1nwFRKWrpP7sCf+gWg=="
	indexFundsCollectionId      = "MFCOLL220906o0vQpkttR/mBJopY5qP7Gw=="
	sectorFundsCollectionId     = "MFCOLL11eJYidI+bSOK8Mk2yHWjGhg=="
	highStakesColellectionId    = "MFCOLL220616OpSMFB48RmSti0YWVhvf8A=="
	investFestSDIconUrl         = "https://epifi-icons.pointz.in/investments/invest_fest_jar.png"
	investFestFDIconUrl         = "https://epifi-icons.pointz.in/investments/invest_fest_locker.png"
	fDBankIconUrl               = "https://epifi-icons.pointz.in/investments/fd-bank-locker.png"
	investFestMFIconUrl         = "https://epifi-icons.pointz.in/investments/invest_fest_mutual_funds.png"
	investFestJumpIconUrl       = "https://epifi-icons.pointz.in/investments/invest_fest_jump.png"
	investFestJumpBoosterUrl    = "https://epifi-icons.pointz.in/investments/invest_fest_jump.png"
	investFestGoldIconUrl       = "https://epifi-icons.pointz.in/investments/invest_fest_gold.png"
	investFestCashIconUrl       = "https://epifi-icons.pointz.in/investments/invest_fest_money.png"
	investFestStarShield        = "https://epifi-icons.pointz.in/investments/star_shield_invest_fest.png"
	investFestGraphIconUrl      = "https://epifi-icons.pointz.in/investments/invest_fest_graph.png"
	investLandingGraphIconUrl   = "https://epifi-icons.pointz.in/investments/invest-landing-graph.png"
	investFestIndexIconUrl      = "https://epifi-icons.pointz.in/investments/invest_fest_index.png"
	investFestCashStackIconUrl  = "https://epifi-icons.pointz.in/investments/stacks_cash_invest_fest.png"
	investFestCalendarIconUrl   = "https://epifi-icons.pointz.in/investments/calendar_invest_fest.png"
	investFestJumpUpto10IconUrl = "https://epifi-icons.pointz.in/investments/upto_10P_per_invest_fest.png"
)

//nolint:dupl
var (
	actorSegmentToDynamicElementMap = map[actor_segmentation.ActorSegment]*dePb.DynamicElement{
		actor_segmentation.ActorSegment_DEFAULT_SEGMENT:                   getMFLandingBannerInvestFest("ACE YOUR SHORT TERM GOALS", "Bills paid? Park cash in handpicked Ultra short duration funds", investFestMFIconUrl, investFestMFLanding26thFilterIds, "", false),
		actor_segmentation.ActorSegment_ACTOR_INVESTED:                    getMFLandingBannerInvestFest("ACE YOUR SHORT TERM GOALS", "Bills paid? Park cash in handpicked Ultra short duration funds", investFestMFIconUrl, investFestMFLanding26thFilterIds, "", false),
		actor_segmentation.ActorSegment_ACTOR_NOT_INVESTED_IN_MUTATLFUNDS: getMFLandingBannerInvestFest("ACE YOUR SHORT TERM GOALS", "Bills paid? Park cash in handpicked Ultra short duration funds", investFestMFIconUrl, investFestMFLanding26thFilterIds, "", false),
		actor_segmentation.ActorSegment_ACTOR_TRIED_BUT_FAILED_TO_INVEST:  getMFLandingBannerInvestFest("ACE YOUR SHORT TERM GOALS", "Bills paid? Park cash in handpicked Ultra short duration funds", investFestMFIconUrl, investFestMFLanding26thFilterIds, "", false),
	}

	NonWOBActorSegmentToDynamicElementMap = map[actor_segmentation.ActorSegment]*dePb.DynamicElement{
		actor_segmentation.ActorSegment_DEFAULT_SEGMENT:                   NonWOBBuildInvestmentHabitDynamicElementBanner,
		actor_segmentation.ActorSegment_ACTOR_INVESTED:                    NonWOBBuildInvestmentHabitDynamicElementBanner,
		actor_segmentation.ActorSegment_ACTOR_NOT_INVESTED_IN_MUTATLFUNDS: NonWOBBuildInvestmentHabitDynamicElementBanner,
		actor_segmentation.ActorSegment_ACTOR_TRIED_BUT_FAILED_TO_INVEST:  NonWOBBuildInvestmentHabitDynamicElementBanner,
	}

	saveChangeElementBanner = &dePb.DynamicElement{
		OwnerService:  types.ServiceName_INVESTMENT_SERVICE,
		Id:            uuid.New().String(),
		UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER,
		Content: &dePb.ElementContent{
			Content: &dePb.ElementContent_Banner{Banner: &dePb.BannerElementContent{
				Title:           saveChangeDynamicElementTitle,
				Body:            saveChangeDynamicElementBody,
				IconUrl:         saveChangeDynamicElementIcon,
				BackgroundColor: "#ECEEF0",
				Deeplink: &deeplink2.Deeplink{
					Screen: deeplink2.Screen_FIT_CUSTOMISE_RULE_SCREEN,
					ScreenOptions: &deeplink2.Deeplink_FitCustomiseRuleScreenOptions{
						FitCustomiseRuleScreenOptions: &deeplink2.FitCustomiseRuleScreenOptions{
							RuleId:   "f49d1c45-708f-49c7-988e-131050dbfe88",
							PageType: fittClientState.SubscriptionPageType_SUBSCRIPTION_PAGE_NEW,
						},
					},
				},
				TitleTextColor: "#548D7C",
				BodyTextColor:  "#282828",
			}},
		},
	}

	taxSaverFundsBanner = &dePb.DynamicElement{
		OwnerService:  types.ServiceName_INVESTMENT_SERVICE,
		Id:            uuid.New().String(),
		UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER,
		Content: &dePb.ElementContent{
			Content: &dePb.ElementContent_Banner{Banner: &dePb.BannerElementContent{
				Title:           saveTaxesDynamicElementTitle,
				Body:            saveTaxesElementBody,
				IconUrl:         saveTaxesIconUrl,
				BackgroundColor: "#ECEEF0",
				Deeplink: &deeplink2.Deeplink{
					Screen: deeplink2.Screen_MUTUAL_FUND_COLLECTIONS_LANDING_SCREEN,
					ScreenOptions: &deeplink2.Deeplink_MutualFundCollectionsLandingScreenOptions{
						MutualFundCollectionsLandingScreenOptions: &deeplink2.MutualFundCollectionsLandingScreenOptions{
							CollectionId:   saveTaxesCollectionId,
							CollectionType: clientstates.CollectionType_ADVANCED_FILTERS,
							EntryPoint:     clientstates.MutualFundListEntryPoint_MF_LIST_ENTRY_POINT_DIGEST,
						}},
				},
				TitleTextColor: "#548D7C",
				BodyTextColor:  "#282828",
			}},
		},
	}

	startSmallDynamicElementBanner = &dePb.DynamicElement{
		OwnerService:  types.ServiceName_INVESTMENT_SERVICE,
		Id:            uuid.New().String(),
		UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER,
		Content: &dePb.ElementContent{
			Content: &dePb.ElementContent_Banner{Banner: &dePb.BannerElementContent{
				Title:           startSmallDynamicElementTitleV2,
				Body:            startSmallDynamicElementBodyV2,
				IconUrl:         startSmallDynamicElementIcon,
				BackgroundColor: "#ECEEF0",
				Deeplink: &deeplink2.Deeplink{
					Screen: deeplink2.Screen_MUTUAL_FUND_COLLECTIONS_LANDING_SCREEN,
					ScreenOptions: &deeplink2.Deeplink_MutualFundCollectionsLandingScreenOptions{
						MutualFundCollectionsLandingScreenOptions: &deeplink2.MutualFundCollectionsLandingScreenOptions{
							CollectionId:   startSmallDynamicElementCollectionId,
							CollectionType: clientstates.CollectionType_ADVANCED_FILTERS,
							EntryPoint:     clientstates.MutualFundListEntryPoint_MF_LIST_ENTRY_POINT_DIGEST,
						}},
				},
				TitleTextColor: "#548D7C",
				BodyTextColor:  "#282828",
			}},
		},
	}

	tenOnlyDynamicElementBanner = &dePb.DynamicElement{
		OwnerService:  types.ServiceName_INVESTMENT_SERVICE,
		Id:            uuid.New().String(),
		UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER,
		Content: &dePb.ElementContent{
			Content: &dePb.ElementContent_Banner{Banner: &dePb.BannerElementContent{
				Title:           tenOnlyDynamicElementTitle,
				Body:            tenOnlyDynamicElementBody,
				IconUrl:         tenOnlyDynamicElementIcon,
				BackgroundColor: "#ECEEF0",
				Deeplink: &deeplink2.Deeplink{
					Screen: deeplink2.Screen_MUTUAL_FUND_COLLECTIONS_LANDING_SCREEN,
					ScreenOptions: &deeplink2.Deeplink_MutualFundCollectionsLandingScreenOptions{
						MutualFundCollectionsLandingScreenOptions: &deeplink2.MutualFundCollectionsLandingScreenOptions{
							CollectionId:   tenOnlyDynamicElementCollectionId,
							CollectionType: clientstates.CollectionType_ADVANCED_FILTERS,
							EntryPoint:     clientstates.MutualFundListEntryPoint_MF_LIST_ENTRY_POINT_DIGEST,
						}},
				},
				TitleTextColor: "#548D7C",
				BodyTextColor:  "#282828",
			}},
		},
	}

	popularFundsDynamicElementsBanner = &dePb.DynamicElement{
		OwnerService:  types.ServiceName_INVESTMENT_SERVICE,
		Id:            uuid.New().String(),
		UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER,
		Content: &dePb.ElementContent{
			Content: &dePb.ElementContent_Banner{Banner: &dePb.BannerElementContent{
				Title:           notSureWhereToStart,
				Body:            popularFundsDEBody,
				IconUrl:         popularFundsCollectionBannerId,
				BackgroundColor: "#ECEEF0",
				Deeplink: &deeplink2.Deeplink{
					Screen: deeplink2.Screen_MUTUAL_FUND_FILTERS_SCREEN,
					ScreenOptions: &deeplink2.Deeplink_MutualFundFiltersScreenOptions{
						MutualFundFiltersScreenOptions: &deeplink2.MutualFundFiltersScreenOptions{
							CollectionId: popularFundsCollectionId,
							EntryPoint:   clientstates.MutualFundListEntryPoint_MF_LIST_ENTRY_POINT_DIGEST,
						},
					},
				},
				TitleTextColor: "#548D7C",
				BodyTextColor:  "#282828",
			}},
		},
	}

	growWealthDynamicElementBanner = &dePb.DynamicElement{
		OwnerService:  types.ServiceName_INVESTMENT_SERVICE,
		Id:            uuid.New().String(),
		UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER,
		Content: &dePb.ElementContent{
			Content: &dePb.ElementContent_Banner{Banner: &dePb.BannerElementContent{
				Title:           growWealthDynamicElementTitle,
				Body:            growWealthDynamicElementBody,
				IconUrl:         growWealthDynamicElementIcon,
				BackgroundColor: "#ECEEF0",
				Deeplink:        mfDetailsDeeplink,
				TitleTextColor:  "#548D7C",
				BodyTextColor:   "#282828",
			}},
		},
	}

	mfDetailsDeeplink = &deeplink2.Deeplink{
		Screen: deeplink2.Screen_MUTUAL_FUND_DETAILS_SCREEN,
		ScreenOptions: &deeplink2.Deeplink_MutualFundDetailsScreenOptions{
			MutualFundDetailsScreenOptions: &deeplink2.MutualFundDetailsScreenOptions{
				MutualFundId: growWealthDynamicElementMfId,
				EntryPoint:   clientstates.MutualFundListEntryPoint_MF_LIST_ENTRY_POINT_DIGEST,
			},
		},
	}

	buildInvestmentHabitDynamicElementBannerWealthOnboarded = &dePb.DynamicElement{
		OwnerService:  types.ServiceName_INVESTMENT_SERVICE,
		Id:            uuid.New().String(),
		UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER,
		Content: &dePb.ElementContent{
			Content: &dePb.ElementContent_Banner{
				Banner: &dePb.BannerElementContent{
					Title:           buildInvestmentHabitDynamicElementTitleNAVI,
					Body:            buildInvestmentHabitDynamicElementBodyNAVI,
					IconUrl:         buildInvestmentHabitDynamicElementIconNAVI,
					BackgroundColor: "#ECEEF0",
					Deeplink: &deeplink2.Deeplink{
						Screen: deeplink2.Screen_FIT_CUSTOMISE_RULE_SCREEN,
						ScreenOptions: &deeplink2.Deeplink_FitCustomiseRuleScreenOptions{
							FitCustomiseRuleScreenOptions: &deeplink2.FitCustomiseRuleScreenOptions{
								RuleId:       buildInvestmentHabitDynamicElementRuleIdNAVI,
								MutualFundId: buildInvestmentHabitDynamicElementMfIdNAVI,
								PageType:     fittClientState.SubscriptionPageType_SUBSCRIPTION_PAGE_NEW,
							},
						},
					},
					TitleTextColor: "#548D7C",
					BodyTextColor:  "#282828",
				}},
		},
	}

	NonWOBBuildInvestmentHabitDynamicElementBanner = &dePb.DynamicElement{
		OwnerService:  types.ServiceName_INVESTMENT_SERVICE,
		Id:            uuid.New().String(),
		UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER,
		Content: &dePb.ElementContent{
			Content: &dePb.ElementContent_Banner{
				Banner: &dePb.BannerElementContent{
					Title:           buildInvestmentHabitDynamicElementTitleNAVI,
					Body:            buildInvestmentHabitDynamicElementBodyNAVI,
					IconUrl:         buildInvestmentHabitDynamicElementIconNAVI,
					BackgroundColor: "#ECEEF0",
					Deeplink: &deeplink2.Deeplink{
						Screen: deeplink2.Screen_MUTUAL_FUND_DETAILS_SCREEN,
						ScreenOptions: &deeplink2.Deeplink_MutualFundDetailsScreenOptions{
							MutualFundDetailsScreenOptions: &deeplink2.MutualFundDetailsScreenOptions{
								MutualFundId: buildInvestmentHabitDynamicElementMfIdNAVI,
								EntryPoint:   clientstates.MutualFundListEntryPoint_MF_LIST_ENTRY_POINT_DIGEST,
							},
						},
					},
					TitleTextColor: "#548D7C",
					BodyTextColor:  "#282828",
				}},
		},
	}

	salaryReceivedDynamicElement = &dePb.DynamicElement{
		OwnerService:  types.ServiceName_INVESTMENT_SERVICE,
		Id:            uuid.New().String(),
		UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER,
		Content: &dePb.ElementContent{
			Content: &dePb.ElementContent_Banner{
				Banner: &dePb.BannerElementContent{
					Title:           salaryReceivedDynamicElementTitle,
					Body:            salaryReceivedDynamicElementBody,
					IconUrl:         salaryReceivedDynamicElementUrl,
					BackgroundColor: "#ECEEF0",
					Deeplink: &deeplink2.Deeplink{
						Screen: deeplink2.Screen_MUTUAL_FUND_COLLECTIONS_LANDING_SCREEN,
						ScreenOptions: &deeplink2.Deeplink_MutualFundCollectionsLandingScreenOptions{
							MutualFundCollectionsLandingScreenOptions: &deeplink2.MutualFundCollectionsLandingScreenOptions{
								CollectionId:   babyStepsCollectionId,
								CollectionType: clientstates.CollectionType_MF_COLLECTION,
								EntryPoint:     clientstates.MutualFundListEntryPoint_MF_LIST_ENTRY_POINT_MF_LANDING,
							},
						},
					},
					TitleTextColor: "#548D7C",
					BodyTextColor:  "#282828",
				},
			},
		},
	}

	filterIds = []string{"NAVI_AMC", "INTERNATIONAL_EQUITY_SOL", "LARGE_CAP_INDEX", "OTHERS_INDEX"}

	lowOnCashDynamicElement = &dePb.DynamicElement{
		OwnerService:  types.ServiceName_INVESTMENT_SERVICE,
		Id:            uuid.New().String(),
		UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER,
		Content: &dePb.ElementContent{
			Content: &dePb.ElementContent_Banner{
				Banner: &dePb.BannerElementContent{
					Title:           lowOnCashDynamicElementTitle,
					Body:            lowOnCashDynamicElementBody,
					IconUrl:         lowOnCashDynamicElementUrl,
					BackgroundColor: "#ECEEF0",
					Deeplink: &deeplink2.Deeplink{
						Screen: deeplink2.Screen_MUTUAL_FUND_FILTERS_SCREEN,
						ScreenOptions: &deeplink2.Deeplink_MutualFundFiltersScreenOptions{
							MutualFundFiltersScreenOptions: &deeplink2.MutualFundFiltersScreenOptions{
								FilterIds:  filterIds,
								EntryPoint: clientstates.MutualFundListEntryPoint_MF_LIST_ENTRY_POINT_MF_LANDING,
							},
						},
					},
					TitleTextColor: "#548D7C",
					BodyTextColor:  "#282828",
				},
			},
		},
	}

	jumpBanner = &dePb.DynamicElement{
		OwnerService:  types.ServiceName_INVESTMENT_SERVICE,
		Id:            uuid.New().String(),
		UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER,
		Content: &dePb.ElementContent{
			Content: &dePb.ElementContent_Banner{Banner: &dePb.BannerElementContent{
				Title:           jumpDynamicElementTitle,
				Body:            jumpDynamicElementBody,
				IconUrl:         jumpDynamicElementUrl,
				BackgroundColor: "#C0DAE0",
				Deeplink: &deeplink2.Deeplink{
					Screen: deeplink2.Screen_P2P_INVESTMENT_DASHBOARD_SCREEN,
				},
				TitleTextColor: "#333333",
				BodyTextColor:  "#333333",
			}},
		},
	}

	hdfcIndexFund = &dePb.DynamicElement{
		OwnerService:  types.ServiceName_INVESTMENT_SERVICE,
		Id:            uuid.New().String(),
		UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER,
		Content: &dePb.ElementContent{
			Content: &dePb.ElementContent_Banner{Banner: &dePb.BannerElementContent{
				Title:           "POPULAR",
				Body:            "Be a Lakhpati in 3 years. Invest ₹2,500 monthly in this Index fund",
				IconUrl:         "https://epifi-icons.pointz.in/mutual-fund/icons/Add+Funds-1.png",
				BackgroundColor: "#ECEEF0",
				Deeplink: &deeplink2.Deeplink{
					Screen: deeplink2.Screen_MUTUAL_FUND_DETAILS_SCREEN,
					ScreenOptions: &deeplink2.Deeplink_MutualFundDetailsScreenOptions{
						MutualFundDetailsScreenOptions: &deeplink2.MutualFundDetailsScreenOptions{
							MutualFundId: "MF220528Hpqh+uocQ2mZdwC7+Ry+ZQ==",
							EntryPoint:   clientstates.MutualFundListEntryPoint_MF_LIST_ENTRY_POINT_Investment_LANDING,
						},
					},
				},
				TitleTextColor: "#548D7C",
				BodyTextColor:  "#282828",
			}},
		},
	}

	taxSaverFDBanner = &dePb.DynamicElement{
		OwnerService:  types.ServiceName_INVESTMENT_SERVICE,
		Id:            uuid.New().String(),
		UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER,
		Content: &dePb.ElementContent{
			Content: &dePb.ElementContent_Banner{Banner: &dePb.BannerElementContent{
				Title:           "SAVE YOUR TAXES",
				Body:            "Looking to save taxes? Invest in Tax Saver FD and earn 6.6% p.a",
				IconUrl:         "https://epifi-icons.pointz.in/investments/landing/locker.png",
				BackgroundColor: "#ECEEF0",
				Deeplink: &deeplink2.Deeplink{
					Screen: deeplink2.Screen_DEPOSIT_TEMPLATES_SCREEN,
					ScreenOptions: &deeplink2.Deeplink_DepositTemplatesScreenOptions{
						DepositTemplatesScreenOptions: &deeplink2.DepositTemplatesScreenOptions{
							DepositType: accounts.Type_FIXED_DEPOSIT,
						},
					},
				},
				TitleTextColor: "#548D7C",
				BodyTextColor:  "#282828",
			}},
		},
	}

	bumperReturnsFD = &dePb.DynamicElement{
		OwnerService:  types.ServiceName_INVESTMENT_SERVICE,
		Id:            uuid.New().String(),
		UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER,
		Content: &dePb.ElementContent{
			Content: &dePb.ElementContent_Banner{Banner: &dePb.BannerElementContent{
				Title:           "EARN HIGH RETURNS",
				Body:            "Invest in Bumper returns FD and get 7.25% p.a. interest",
				IconUrl:         "https://epifi-icons.pointz.in/investments/landing/locker.png",
				BackgroundColor: "#ECEEF0",
				Deeplink: &deeplink2.Deeplink{
					Screen: deeplink2.Screen_DEPOSIT_TEMPLATES_SCREEN,
					ScreenOptions: &deeplink2.Deeplink_DepositTemplatesScreenOptions{
						DepositTemplatesScreenOptions: &deeplink2.DepositTemplatesScreenOptions{
							DepositType: accounts.Type_FIXED_DEPOSIT,
						},
					},
				},
				TitleTextColor: "#548D7C",
				BodyTextColor:  "#282828",
			}},
		},
	}

	mfIndexInvestment = &dePb.DynamicElement{
		OwnerService:  types.ServiceName_INVESTMENT_SERVICE,
		Id:            uuid.New().String(),
		UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER,
		Content: &dePb.ElementContent{
			Content: &dePb.ElementContent_Banner{Banner: &dePb.BannerElementContent{
				Title:           "POPULAR",
				Body:            "Be a Lakhpati in 3 years. Invest ₹2,500 monthly in this Index fund",
				IconUrl:         "https://epifi-icons.pointz.in/mutual-fund/icons/Add+Funds-1.png",
				BackgroundColor: "#ECEEF0",
				Deeplink: &deeplink2.Deeplink{
					Screen: deeplink2.Screen_MUTUAL_FUND_DETAILS_SCREEN,
					ScreenOptions: &deeplink2.Deeplink_MutualFundDetailsScreenOptions{
						MutualFundDetailsScreenOptions: &deeplink2.MutualFundDetailsScreenOptions{
							MutualFundId: "MF220131bDIszoW/Q16HywNTpXRTRw==",
							EntryPoint:   clientstates.MutualFundListEntryPoint_MF_LIST_ENTRY_POINT_Investment_LANDING,
						},
					},
				},
				TitleTextColor: "#548D7C",
				BodyTextColor:  "#282828",
			}},
		},
	}

	fitIPL = &dePb.DynamicElement{
		OwnerService:  types.ServiceName_INVESTMENT_SERVICE,
		Id:            uuid.New().String(),
		UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER,
		Content: &dePb.ElementContent{
			Content: &dePb.ElementContent_Banner{Banner: &dePb.BannerElementContent{
				Title:           "IPL 2023",
				Body:            fmt.Sprintf(accrualPkg.ReplaceCoinWithPointIfApplicable("IPL is back! Save with IPL FIT rules & Win %d Fi-Coins.", nil), accrualPkg.ConvertFiCoinsToFiPoints(1000, false)),
				IconUrl:         "https://epifi-icons.pointz.in/investments/landing/FSL_1.png",
				BackgroundColor: "#ECEEF0",
				Deeplink: &deeplink2.Deeplink{
					Screen: deeplink2.Screen_FITT_SPORTS_CHALLENGE_HOME_SCREEN,
					ScreenOptions: &deeplink2.Deeplink_SportsChallengeHomeScreenOptions{
						SportsChallengeHomeScreenOptions: &deeplink2.FitttSportsChallengeHomeScreenOptions{
							TournamentId: "iplt20_2023",
						},
					},
				},
				TitleTextColor: "#548D7C",
				BodyTextColor:  "#282828",
			}},
		},
	}

	buildInvestmentHabit = &dePb.DynamicElement{
		OwnerService:  types.ServiceName_INVESTMENT_SERVICE,
		Id:            uuid.New().String(),
		UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER,
		Content: &dePb.ElementContent{
			Content: &dePb.ElementContent_Banner{Banner: &dePb.BannerElementContent{
				Title:           buildInvestmentHabitDynamicElementTitle,
				Body:            buildInvestmentHabitDynamicElementBody,
				IconUrl:         buildInvestmentHabitDynamicElementIcon,
				BackgroundColor: "#ECEEF0",
				Deeplink: &deeplink2.Deeplink{
					Screen: deeplink2.Screen_MUTUAL_FUND_DETAILS_SCREEN,
					ScreenOptions: &deeplink2.Deeplink_MutualFundDetailsScreenOptions{
						MutualFundDetailsScreenOptions: &deeplink2.MutualFundDetailsScreenOptions{
							MutualFundId: buildInvestmentHabitDynamicElementMfId,
							EntryPoint:   clientstates.MutualFundListEntryPoint_MF_LIST_ENTRY_POINT_HOME_LANDING,
						},
					},
				},
				TitleTextColor: "#548D7C",
				BodyTextColor:  "#282828",
			}},
		},
	}

	billPaymentsSD = &dePb.DynamicElement{
		OwnerService:  types.ServiceName_INVESTMENT_SERVICE,
		Id:            uuid.New().String(),
		UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER,
		Content: &dePb.ElementContent{
			Content: &dePb.ElementContent_Banner{Banner: &dePb.BannerElementContent{
				Title:           "SMART DEPOSIT",
				Body:            "Bills paid? Save for your goals using Jars while earning 6.8% returns p.a.",
				IconUrl:         "https://epifi-icons.pointz.in/investments/landing/CashAndCoin.png",
				BackgroundColor: "#ECEEF0",
				Deeplink: &deeplink2.Deeplink{
					Screen: deeplink2.Screen_DEPOSIT_TEMPLATES_SCREEN,
					ScreenOptions: &deeplink2.Deeplink_DepositTemplatesScreenOptions{
						DepositTemplatesScreenOptions: &deeplink2.DepositTemplatesScreenOptions{
							DepositType: accounts.Type_SMART_DEPOSIT,
						},
					},
				},
				TitleTextColor: "#548D7C",
				BodyTextColor:  "#282828",
			}},
		},
	}

	investFestSDBanner = &dePb.DynamicElement{
		OwnerService:  types.ServiceName_INVESTMENT_SERVICE,
		Id:            uuid.New().String(),
		UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER,
		Content: &dePb.ElementContent{
			Content: &dePb.ElementContent_Banner{Banner: &dePb.BannerElementContent{
				Title:           "TODAY - SMART DEPOSIT",
				Body:            "Automatically set aside money each month. Earn 6.8% p.a.",
				IconUrl:         investFestSDIconUrl,
				BackgroundColor: "#C5E9B2",
				Deeplink: &deeplink2.Deeplink{
					Screen: deeplink2.Screen_FIT_CUSTOMISE_RULE_SCREEN,
					ScreenOptions: &deeplink2.Deeplink_FitCustomiseRuleScreenOptions{
						FitCustomiseRuleScreenOptions: &deeplink2.FitCustomiseRuleScreenOptions{
							RuleId:   autoSaveMonthlyRuleId,
							PageType: fittClientState.SubscriptionPageType_SUBSCRIPTION_PAGE_NEW,
						},
					},
				},
				TitleTextColor: "#5D7D4C", // deep mint
				BodyTextColor:  "#333333",
			}},
		},
	}

	investFestFDBanner = &dePb.DynamicElement{
		OwnerService:  types.ServiceName_INVESTMENT_SERVICE,
		Id:            uuid.New().String(),
		UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER,
		Content: &dePb.ElementContent{
			Content: &dePb.ElementContent_Banner{Banner: &dePb.BannerElementContent{
				Title:           "TODAY - FIXED DEPOSIT",
				Body:            "Lock-in 7.25% p.a. returns for your annual bonus. Invest now",
				IconUrl:         investFestFDIconUrl,
				BackgroundColor: "#BBC8E9",
				Deeplink: &deeplink2.Deeplink{
					Screen: deeplink2.Screen_DEPOSIT_TEMPLATES_SCREEN,
					ScreenOptions: &deeplink2.Deeplink_DepositTemplatesScreenOptions{
						DepositTemplatesScreenOptions: &deeplink2.DepositTemplatesScreenOptions{
							DepositType: accounts.Type_FIXED_DEPOSIT,
						},
					},
				},
				TitleTextColor: "#4F71AB",
				BodyTextColor:  "#282828",
			}},
		},
	}

	investFestFDBanner2 = &dePb.DynamicElement{
		OwnerService:  types.ServiceName_INVESTMENT_SERVICE,
		Id:            uuid.New().String(),
		UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER,
		Content: &dePb.ElementContent{
			Content: &dePb.ElementContent_Banner{Banner: &dePb.BannerElementContent{
				Title:           "Popular",
				Body:            "Lock-in 7.25% p.a. returns. Invest in a Fixed Deposit now",
				IconUrl:         fDBankIconUrl,
				BackgroundColor: "#C5E9B2",
				Deeplink: &deeplink2.Deeplink{
					Screen: deeplink2.Screen_DEPOSIT_TEMPLATES_SCREEN,
					ScreenOptions: &deeplink2.Deeplink_DepositTemplatesScreenOptions{
						DepositTemplatesScreenOptions: &deeplink2.DepositTemplatesScreenOptions{
							DepositType: accounts.Type_FIXED_DEPOSIT,
						},
					},
				},
				TitleTextColor: "#5D7D4C",
				BodyTextColor:  "#333333",
			}},
		},
	}

	investFestMFBanner = &dePb.DynamicElement{
		OwnerService:  types.ServiceName_INVESTMENT_SERVICE,
		Id:            uuid.New().String(),
		UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER,
		Content: &dePb.ElementContent{
			Content: &dePb.ElementContent_Banner{Banner: &dePb.BannerElementContent{
				Title:           "TODAY - MUTUAL FUNDS",
				Body:            "Start a monthly SIP in these popular mutual funds starting ₹100",
				IconUrl:         investFestMFIconUrl,
				BackgroundColor: "#C0B7E1",
				Deeplink: &deeplink2.Deeplink{
					Screen: deeplink2.Screen_MUTUAL_FUND_FILTERS_SCREEN,
					ScreenOptions: &deeplink2.Deeplink_MutualFundFiltersScreenOptions{
						MutualFundFiltersScreenOptions: &deeplink2.MutualFundFiltersScreenOptions{
							CollectionId: popularFundsCollectionId,
							EntryPoint:   clientstates.MutualFundListEntryPoint_MF_LIST_ENTRY_POINT_DIGEST,
						},
					},
				},
				TitleTextColor: "#6F62A4",
				BodyTextColor:  "#333333",
			}},
		},
	}

	investFestMFLanding26thFilterIds = []string{"ULTRA_SHORT_DURATION_DEBT", "UPTO_POINT5_PER_EXPENSE", "1001_TO_5000CR_FUND_SIZE", "5001_TO_10KCR_FUND_SIZE", "GREATER_THAN_7_PER_YIELD_TO_MATURITY"}
	investFestMFLanding4thFilterIds  = []string{"1POINT1_TO_1POINT5PER_PER_EXPENSE", "POINT51_TO_1_PER_EXPENSE", "UPTO_POINT5_PER_EXPENSE",
		"EQUITY_FUND_TYPE", "VALUE_EQUITY"}
)

func getJumpBannerInvestFest(title, titleTextColor, body, bodyTextColor, bgColor, shadowColor, iconUrl string) *dePb.DynamicElement {
	return &dePb.DynamicElement{
		OwnerService:  types.ServiceName_INVESTMENT_SERVICE,
		Id:            uuid.New().String(),
		UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER_V2,
		Content: &dePb.ElementContent{
			Content: &dePb.ElementContent_BannerV2{BannerV2: &dePb.BannerElementContentV2{
				Title: &commontypes.Text{
					FontColor: titleTextColor,
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: title,
					},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS,
					},
					BgColor: bgColor,
				},
				Image: &commontypes.Image{
					ImageUrl: iconUrl,
				},
				Body: &commontypes.Text{
					FontColor: bodyTextColor,
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: body,
					},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
					},
				},
				BackgroundColor: &ui.BackgroundColour{
					Colour: &ui.BackgroundColour_BlockColour{
						BlockColour: bgColor,
					},
				},
				Deeplink: &deeplink2.Deeplink{
					Screen: deeplink2.Screen_P2P_INVESTMENT_DASHBOARD_SCREEN,
				},
				Shadows: []*ui.Shadow{
					{
						Height: 4,
						Colour: &ui.BackgroundColour{
							Colour: &ui.BackgroundColour_BlockColour{
								BlockColour: shadowColor,
							},
						},
					},
				},
			}},
		},
	}
}

func getMFLandingBannerInvestFest(title string, body string, iconUrl string, filterIds []string, collectionId string, isPresetCollection bool) *dePb.DynamicElement {

	var deeplink *deeplink2.Deeplink
	if isPresetCollection {
		deeplink = &deeplink2.Deeplink{
			Screen: deeplink2.Screen_MUTUAL_FUND_FILTERS_SCREEN,
			ScreenOptions: &deeplink2.Deeplink_MutualFundFiltersScreenOptions{
				MutualFundFiltersScreenOptions: &deeplink2.MutualFundFiltersScreenOptions{
					CollectionId: collectionId,
					EntryPoint:   clientstates.MutualFundListEntryPoint_MF_LIST_ENTRY_POINT_MF_LANDING,
				},
			},
		}
	} else if len(filterIds) != 0 {
		deeplink = &deeplink2.Deeplink{
			Screen: deeplink2.Screen_MUTUAL_FUND_FILTERS_SCREEN,
			ScreenOptions: &deeplink2.Deeplink_MutualFundFiltersScreenOptions{
				MutualFundFiltersScreenOptions: &deeplink2.MutualFundFiltersScreenOptions{
					FilterIds:  filterIds,
					EntryPoint: clientstates.MutualFundListEntryPoint_MF_LIST_ENTRY_POINT_MF_LANDING,
				},
			},
		}
	} else {
		deeplink = &deeplink2.Deeplink{
			Screen: deeplink2.Screen_MUTUAL_FUND_COLLECTIONS_LANDING_SCREEN,
			ScreenOptions: &deeplink2.Deeplink_MutualFundCollectionsLandingScreenOptions{
				MutualFundCollectionsLandingScreenOptions: &deeplink2.MutualFundCollectionsLandingScreenOptions{
					CollectionId:   collectionId,
					CollectionType: clientstates.CollectionType_MF_COLLECTION,
					EntryPoint:     clientstates.MutualFundListEntryPoint_MF_LIST_ENTRY_POINT_MF_LANDING,
				},
			},
		}

	}

	return &dePb.DynamicElement{
		OwnerService:  types.ServiceName_INVESTMENT_SERVICE,
		Id:            uuid.New().String(),
		UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER,
		Content: &dePb.ElementContent{
			Content: &dePb.ElementContent_Banner{Banner: &dePb.BannerElementContent{
				Title:           title,
				Body:            body,
				IconUrl:         iconUrl,
				BackgroundColor: "#ECEEF0",
				Deeplink:        deeplink,
				TitleTextColor:  "#548D7C",
				BodyTextColor:   "#282828",
			}},
		},
	}
}

func getSDBannerInvestLanding(title, titleTextColor, body, bodyTextColor, bgColor, iconUrl, fitRuleId string) *dePb.DynamicElement {

	investLandingSDBanner := &dePb.DynamicElement{
		OwnerService:  types.ServiceName_INVESTMENT_SERVICE,
		Id:            uuid.New().String(),
		UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER,
		Content: &dePb.ElementContent{
			Content: &dePb.ElementContent_Banner{Banner: &dePb.BannerElementContent{
				Title:           title,
				Body:            body,
				IconUrl:         iconUrl,
				BackgroundColor: bgColor,
				Deeplink: &deeplink2.Deeplink{
					Screen: deeplink2.Screen_FIT_CUSTOMISE_RULE_SCREEN,
					ScreenOptions: &deeplink2.Deeplink_FitCustomiseRuleScreenOptions{
						FitCustomiseRuleScreenOptions: &deeplink2.FitCustomiseRuleScreenOptions{
							RuleId:   fitRuleId,
							PageType: fittClientState.SubscriptionPageType_SUBSCRIPTION_PAGE_NEW,
						},
					},
				},
				TitleTextColor: titleTextColor,
				BodyTextColor:  bodyTextColor,
			}},
		},
	}

	return investLandingSDBanner
}

func getFDBannerInvestLanding(title, titleTextColor, body, bodyTextColor, bgColor, iconUrl string) *dePb.DynamicElement {
	return &dePb.DynamicElement{
		OwnerService:  types.ServiceName_INVESTMENT_SERVICE,
		Id:            uuid.New().String(),
		UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER,
		Content: &dePb.ElementContent{
			Content: &dePb.ElementContent_Banner{Banner: &dePb.BannerElementContent{
				Title:           title,
				Body:            body,
				IconUrl:         iconUrl,
				BackgroundColor: bgColor,
				Deeplink: &deeplink2.Deeplink{
					Screen: deeplink2.Screen_DEPOSIT_TEMPLATES_SCREEN,
					ScreenOptions: &deeplink2.Deeplink_DepositTemplatesScreenOptions{
						DepositTemplatesScreenOptions: &deeplink2.DepositTemplatesScreenOptions{
							DepositType: accounts.Type_FIXED_DEPOSIT,
						},
					},
				},
				TitleTextColor: titleTextColor,
				BodyTextColor:  bodyTextColor,
			}},
		},
	}
}

func getMFBannerInvestLanding(title, titleTextColor, body, bodyTextColor, bgColor, iconUrl, collectionId string, filterIds []string) *dePb.DynamicElement {
	if len(filterIds) == 0 {
		return &dePb.DynamicElement{
			OwnerService:  types.ServiceName_INVESTMENT_SERVICE,
			Id:            uuid.New().String(),
			UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
			StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER,
			Content: &dePb.ElementContent{
				Content: &dePb.ElementContent_Banner{Banner: &dePb.BannerElementContent{
					Title:           title,
					Body:            body,
					IconUrl:         iconUrl,
					BackgroundColor: bgColor,
					Deeplink: &deeplink2.Deeplink{
						Screen: deeplink2.Screen_MUTUAL_FUND_FILTERS_SCREEN,
						ScreenOptions: &deeplink2.Deeplink_MutualFundFiltersScreenOptions{
							MutualFundFiltersScreenOptions: &deeplink2.MutualFundFiltersScreenOptions{
								CollectionId: collectionId,
								EntryPoint:   clientstates.MutualFundListEntryPoint_MF_LIST_ENTRY_POINT_DIGEST,
							},
						},
					},
					TitleTextColor: titleTextColor,
					BodyTextColor:  bodyTextColor,
				}},
			},
		}
	} else {
		return &dePb.DynamicElement{
			OwnerService:  types.ServiceName_INVESTMENT_SERVICE,
			Id:            uuid.New().String(),
			UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
			StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER,
			Content: &dePb.ElementContent{
				Content: &dePb.ElementContent_Banner{Banner: &dePb.BannerElementContent{
					Title:           title,
					Body:            body,
					IconUrl:         iconUrl,
					BackgroundColor: bgColor,
					Deeplink: &deeplink2.Deeplink{
						Screen: deeplink2.Screen_MUTUAL_FUND_FILTERS_SCREEN,
						ScreenOptions: &deeplink2.Deeplink_MutualFundFiltersScreenOptions{
							MutualFundFiltersScreenOptions: &deeplink2.MutualFundFiltersScreenOptions{
								FilterIds:  filterIds,
								EntryPoint: clientstates.MutualFundListEntryPoint_MF_LIST_ENTRY_POINT_MF_LANDING,
							},
						},
					},
					TitleTextColor: titleTextColor,
					BodyTextColor:  bodyTextColor,
				}},
			},
		}

	}
}
