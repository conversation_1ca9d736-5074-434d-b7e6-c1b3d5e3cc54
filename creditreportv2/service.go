// nolint:gocritic
package credit_report

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	structPb "google.golang.org/protobuf/types/known/structpb"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	creditreportNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/creditreport"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/api/consent"
	creditReportPb "github.com/epifi/gamma/api/creditreportv2"
	"github.com/epifi/gamma/api/creditreportv2/consumer"
	"github.com/epifi/gamma/api/creditreportv2/payload"
	crWorkflowPb "github.com/epifi/gamma/api/creditreportv2/workflow"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	vgCreditReport "github.com/epifi/gamma/api/vendorgateway/credit_report"
	"github.com/epifi/gamma/api/vendormapping"
	"github.com/epifi/gamma/creditreportv2/config/genconf"
	"github.com/epifi/gamma/creditreportv2/dao"
	crDeeplink "github.com/epifi/gamma/creditreportv2/deeplink"
	wireTypes "github.com/epifi/gamma/creditreportv2/wire/types"
	"github.com/epifi/gamma/featurestore"
	fsModel "github.com/epifi/gamma/featurestore/model"
	pkgCreditReport "github.com/epifi/gamma/pkg/creditreport"
)

const (
	maxRetriesToInitiateReportPresenceCheck = 3
	creditReportNotFound                    = "credit report not present at vendor"
	consentNotGiven                         = "credit report download consent not given by user"
	defaultCreditReportFetchLimit           = 1
)

type CreditReportPresencePublisherType queue.Publisher
type Service struct {
	creditReportPb.UnimplementedCreditReportManagerServer
	conf                              *genconf.Config
	creditReportDao                   dao.CreditReportDao
	cibilReportFlattenDao             dao.CibilReportFlattenDao
	creditReportDownloadDao           dao.CreditReportDownloadDao
	creditReportUserSubscriptionDao   dao.CreditReportUserSubscriptionDetailDao
	creditReportsRawDao               dao.CreditReportsRawDao
	creditReportVerificationPublisher queue.Publisher
	creditReportPresencePublisher     CreditReportPresencePublisherType
	celestialClient                   celestialPb.CelestialClient
	txnExecutor                       storageV2.TxnExecutor
	consentClient                     consent.ConsentClient
	datetime                          datetime.Time
	onboardingClient                  onbPb.OnboardingClient
	userClient                        user.UsersClient
	vgCreditReportClient              vgCreditReport.CreditReportClient
	featureStoreFactory               featurestore.IFactory
	experianAnalyticsDao              dao.ExperianAnalyticsDao
	vendorMappingClient               vendormapping.VendorMappingServiceClient
}

func NewService(conf *genconf.Config,
	creditReportDao dao.CreditReportDao, creditReportDownloadDao dao.CreditReportDownloadDao,
	creditReportUserSubscriptionDao dao.CreditReportUserSubscriptionDetailDao,
	creditReportsRawDao dao.CreditReportsRawDao,
	creditReportVerificationPublisher wireTypes.CreditReportVerificationPublisher, creditReportPresencePublisher wireTypes.CreditReportPresencePublisher,
	celestialClient celestialPb.CelestialClient, txnExecutor storageV2.TxnExecutor, consentClient consent.ConsentClient,
	datetime datetime.Time, onboardingClient onbPb.OnboardingClient, userClient user.UsersClient,
	vgCreditReportClient vgCreditReport.CreditReportClient, cibilReportFlattenDao dao.CibilReportFlattenDao, featureStoreFactory featurestore.IFactory, experianAnalyticsDao dao.ExperianAnalyticsDao, vendorMappingClient vendormapping.VendorMappingServiceClient) *Service {
	return &Service{
		conf:                              conf,
		creditReportDao:                   creditReportDao,
		cibilReportFlattenDao:             cibilReportFlattenDao,
		creditReportDownloadDao:           creditReportDownloadDao,
		creditReportUserSubscriptionDao:   creditReportUserSubscriptionDao,
		creditReportsRawDao:               creditReportsRawDao,
		creditReportVerificationPublisher: creditReportVerificationPublisher,
		creditReportPresencePublisher:     creditReportPresencePublisher,
		celestialClient:                   celestialClient,
		txnExecutor:                       txnExecutor,
		consentClient:                     consentClient,
		datetime:                          datetime,
		onboardingClient:                  onboardingClient,
		userClient:                        userClient,
		vgCreditReportClient:              vgCreditReportClient,
		featureStoreFactory:               featureStoreFactory,
		experianAnalyticsDao:              experianAnalyticsDao,
		vendorMappingClient:               vendorMappingClient,
	}
}

var _ creditReportPb.CreditReportManagerServer = &Service{}

var (
	creditReportVerificationReportNotPresent rpc.StatusFactoryWithDebugMsg = func(debugMsg string) *rpc.Status {
		return rpc.NewStatus(
			uint32(creditReportPb.InitiateCreditReportVerificationResponse_CREDIT_REPORT_NOT_PRESENT),
			"Credit Report Not Found",
			debugMsg,
		)
	}
	creditReportConsentNotFound rpc.StatusFactoryWithDebugMsg = func(debugMsg string) *rpc.Status {
		return rpc.NewStatus(
			uint32(creditReportPb.InitiateCreditReportVerificationResponse_CONSENT_NOT_FOUND),
			"No consent to download report",
			debugMsg,
		)
	}
)

// GetCreditReport returns report presence status and report verification status wth credit report if present
// fe polling APIs will call this rpc to check status in both cases i.e. report presence check and report verification status
func (s *Service) GetCreditReport(ctx context.Context, request *creditReportPb.GetCreditReportRequest) (*creditReportPb.GetCreditReportResponse, error) {
	resp, err := s.creditReportDao.GetByActorId(ctx, request.GetActorId())
	if err != nil {
		if storageV2.IsRecordNotFoundError(err) {
			reportStatus, errReportStatus := s.getCreditReportDataAvailabilityStatus(ctx, request.GetActorId(), "", commonvgpb.Vendor_EXPERIAN)
			if errReportStatus != nil {
				if errors.Is(errReportStatus, epifierrors.ErrRecordNotFound) {
					logger.Error(ctx, "experian credit report or credit report download not found")
					return &creditReportPb.GetCreditReportResponse{
						Status: rpc.StatusRecordNotFound(),
					}, nil
				}
				logger.Error(ctx, "failed to get experian credit report data", zap.Error(errReportStatus))
				return &creditReportPb.GetCreditReportResponse{
					Status: rpc.StatusInternal(),
				}, nil
			}
			return &creditReportPb.GetCreditReportResponse{
				Status:                 rpc.StatusOk(),
				DataAvailabilityStatus: reportStatus,
			}, nil
		}
		logger.Error(ctx, "error in getting credit report", zap.String(logger.ACTOR_ID_V2, request.GetActorId()), zap.Error(err))
		return &creditReportPb.GetCreditReportResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	return &creditReportPb.GetCreditReportResponse{
		Status:             rpc.StatusOk(),
		PresenceStatus:     resp.GetPresenceStatus(),
		VerificationStatus: resp.GetVerificationStatus(),
		CreditReportData:   resp.GetCreditReportData(),
		DownloadConsent:    resp.GetDownloadConsent(),
		CreditReport:       resp,
	}, nil
}

// nolint:funlen
func (s *Service) GetCreditReports(ctx context.Context, req *creditReportPb.GetCreditReportsRequest) (*creditReportPb.GetCreditReportsResponse, error) {
	res := &creditReportPb.GetCreditReportsResponse{
		Status: rpc.StatusOk(),
	}
	res.CreditReports = make([]*creditReportPb.CreditReportDownloadDetails, 0)
	var err error
	var rawReports []*creditReportPb.CreditReportRaw
	limit := 1
	if req.GetLimit() != 0 {
		limit = int(req.GetLimit())
	}

	if len(req.GetVendor()) == 0 {
		res.CreditReports, err = s.getExperianCreditReports(ctx, req.GetActorId(), limit)
		switch {
		case errors.Is(err, epifierrors.ErrRecordNotFound):
			logger.Info(ctx, "no reports found for user", zap.String(logger.VENDOR, commonvgpb.Vendor_EXPERIAN.String()))
			res.Status = rpc.StatusRecordNotFound()
		case err != nil:
			logger.Error(ctx, "error while fetching Experian reports from db", zap.Error(err))
			res.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		}
		return res, nil
	}

	for _, vendor := range req.GetVendor() {
		switch vendor {
		case commonvgpb.Vendor_CIBIL:
			rawReports, err = s.creditReportsRawDao.GetByActorIdVendor(ctx, req.GetActorId(), commonvgpb.Vendor_CIBIL, limit)
			switch {
			case errors.Is(err, epifierrors.ErrRecordNotFound):
				logger.Info(ctx, "no reports found for user", zap.String(logger.VENDOR, commonvgpb.Vendor_CIBIL.String()))
				// continue to check for other vendors
				continue
			case err != nil:
				logger.Error(ctx, "error while fetching raw reports from db", zap.Error(err))
				res.Status = rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error while fetching raw reports from db: %s", err.Error()))
				return res, nil
			}

			for _, rawReport := range rawReports {
				res.CreditReports = append(res.GetCreditReports(), &creditReportPb.CreditReportDownloadDetails{
					CreditReportRaw: rawReport,
					CreatedAt:       rawReport.GetCreatedAt(),
					Vendor:          commonvgpb.Vendor_CIBIL,
				})
			}

		case commonvgpb.Vendor_EXPERIAN:
			var creditReportsExperian []*creditReportPb.CreditReportDownloadDetails
			creditReportsExperian, err = s.getExperianCreditReports(ctx, req.GetActorId(), limit)
			switch {
			case errors.Is(err, epifierrors.ErrRecordNotFound):
				logger.Info(ctx, "no reports found for user", zap.String(logger.VENDOR, commonvgpb.Vendor_EXPERIAN.String()))
				// continue to check for other vendors
				continue
			case err != nil:
				logger.Error(ctx, "error while fetching Experian reports from db", zap.Error(err))
				res.Status = rpc.StatusInternalWithDebugMsg(err.Error())
				return res, nil
			}
			for _, creditReport := range creditReportsExperian {
				res.CreditReports = append(res.GetCreditReports(), creditReport)
			}
		}
	}

	if len(res.GetCreditReports()) == 0 {
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	}

	return res, nil
}

func (s *Service) getExperianCreditReports(ctx context.Context, actorId string, limit int) ([]*creditReportPb.CreditReportDownloadDetails, error) {
	res := make([]*creditReportPb.CreditReportDownloadDetails, 0)
	reports, err := s.creditReportDao.GetReportsByActorId(ctx, actorId, limit)
	if err != nil {
		return nil, errors.Wrap(err, "error while fetching reports from db")
	}
	if len(reports) == 0 {
		reportStatus, errReportStatus := s.getCreditReportDataAvailabilityStatus(ctx, actorId, "", commonvgpb.Vendor_EXPERIAN)
		if errReportStatus != nil {
			return nil, errors.Wrap(errReportStatus, "error fetching experian credit report status")
		}
		return []*creditReportPb.CreditReportDownloadDetails{
			{
				Vendor:                             commonvgpb.Vendor_EXPERIAN,
				CreditReportDataAvailabilityStatus: reportStatus,
			},
		}, nil
	}

	for _, report := range reports {
		res = append(res, &creditReportPb.CreditReportDownloadDetails{
			CreditReportData:                   report.GetCreditReportDataRaw(),
			CreatedAt:                          report.GetCreatedAt(),
			Vendor:                             commonvgpb.Vendor_EXPERIAN,
			CreditReportDataAvailabilityStatus: creditReportPb.DataAvailabilityStatus_DATA_AVAILABILITY_STATUS_FOUND,
		})
	}

	return res, nil
}

// InitiateCreditReportPresenceCheck initiates check for credit report presence
// nolint:funlen
func (s *Service) InitiateCreditReportPresenceCheck(ctx context.Context, request *creditReportPb.InitiateCreditReportPresenceCheckRequest) (*creditReportPb.InitiateCreditReportPresenceCheckResponse, error) {
	actorId := request.GetActorId()
	res := &creditReportPb.InitiateCreditReportPresenceCheckResponse{}
	creditReportResp, err := s.creditReportDao.GetByActorId(ctx, actorId)
	if err != nil && !storageV2.IsRecordNotFoundError(err) {
		logger.Error(ctx, "error in getting credit report", zap.String(logger.ACTOR_ID_V2, request.GetActorId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	switch creditReportResp.GetPresenceStatus() {
	// skip if report is present or presence check status is in progress
	case creditReportPb.PresenceStatus_REPORT_PRESENCE_STATUS_IN_PROGRESS, creditReportPb.PresenceStatus_REPORT_PRESENCE_STATUS_PRESENT,
		creditReportPb.PresenceStatus_REPORT_PRESENCE_STATUS_AVAILABLE_BUT_UNAUTHENTICATED, creditReportPb.PresenceStatus_REPORT_PRESENCE_STATUS_ONLY_CREDIT_ENQUIRY_AVAILABLE:
		logger.Info(ctx, fmt.Sprintf("skipping report presence check for user, status: %v", creditReportResp.GetPresenceStatus()), zap.String(logger.ACTOR_ID_V2, request.GetActorId()))
		res.Status = rpc.StatusOk()
		return res, nil
	case creditReportPb.PresenceStatus_REPORT_PRESENCE_STATUS_UNSPECIFIED, creditReportPb.PresenceStatus_REPORT_PRESENCE_STATUS_NOT_PRESENT, creditReportPb.PresenceStatus_REPORT_PRESENCE_STATUS_MANUAL_INTERVENTION:
		logger.Info(ctx, fmt.Sprintf("proceeding to initiate credit report presence check presence status: %v", creditReportResp.GetPresenceStatus()))
	default:
		logger.Error(ctx, fmt.Sprintf("received unexpected presence status: %v", creditReportResp.GetPresenceStatus()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	txnErr := storageV2.RunCRDBIdempotentTxn(ctx, maxRetriesToInitiateReportPresenceCheck, func(txnCtx context.Context) error {
		// create entry if not present
		// ToDo: Check how creditReportResp can be nil without IsRecordNotFoundError error.
		if storageV2.IsRecordNotFoundError(err) || creditReportResp == nil {
			// paranoid log
			if creditReportResp == nil && !storageV2.IsRecordNotFoundError(err) {
				logger.Error(ctx, "err when creditReportResp is nil", zap.Error(err))
			}
			creditReportResp, err = s.creditReportDao.Create(ctx, &creditReportPb.CreditReport{
				ActorId:        actorId,
				PresenceStatus: creditReportPb.PresenceStatus_REPORT_PRESENCE_STATUS_IN_PROGRESS,
				Vendor:         commonvgpb.Vendor_EXPERIAN,
			})
			if err != nil {
				logger.Error(ctx, "error in creating credit report", zap.String(logger.ACTOR_ID_V2, request.GetActorId()), zap.Error(err))
				res.Status = rpc.StatusInternal()
				return err
			}
		} else {
			// mark presence status to IN_PROGRESS once packet is published successfully
			if creditReportResp == nil {
				logger.Error(ctx, "creditReportResp is nil")
			}
			creditReportResp.PresenceStatus = creditReportPb.PresenceStatus_REPORT_PRESENCE_STATUS_IN_PROGRESS
			if err = s.creditReportDao.UpdateById(ctx, creditReportResp, []creditReportPb.CreditReportFieldMask{creditReportPb.CreditReportFieldMask_CREDIT_REPORT_FIELD_PRESENCE_STATUS}); err != nil {
				logger.Info(ctx, "error updating credit report", zap.String(logger.ACTOR_ID_V2, request.GetActorId()), zap.Error(err))
				return err
			}
		}
		if err = s.publishMsgToCheckReportPresence(ctx, request); err != nil {
			logger.Error(ctx, "error in publishing report presence msg to queue", zap.String(logger.ACTOR_ID_V2, request.GetActorId()), zap.Error(err))
			return err
		}
		return nil
	})
	// indicates a non-retryable error
	if txnErr != nil {
		res.Status = rpc.StatusInternal()
		if storageV2.IsErrorAmbiguous(txnErr) {
			logger.Error(ctx, "ambiguous txn error: ", zap.Error(err))
			return res, nil
		}
		logger.Error(ctx, "failed to perform db update in transaction", zap.Error(err))
		return res, nil
	}
	res.Status = rpc.StatusOk()
	return res, nil
}

// InitiateCreditReportVerification fetches and validates credit report and publish packet to queue if required
func (s *Service) InitiateCreditReportVerification(ctx context.Context, request *creditReportPb.InitiateCreditReportVerificationRequest) (*creditReportPb.InitiateCreditReportVerificationResponse, error) {
	actorId := request.GetActorId()
	res := &creditReportPb.InitiateCreditReportVerificationResponse{}
	creditReportResp, err := s.creditReportDao.GetByActorId(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error in getting credit report", zap.String(logger.ACTOR_ID_V2, request.GetActorId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	switch {
	// check consent to download the report
	case !isReportDownloadConsentGiven(creditReportResp.GetDownloadConsent()):
		logger.Error(ctx, fmt.Sprintf("download consent not given by user: %v", creditReportResp.GetDownloadConsent()), zap.String(logger.ACTOR_ID_V2, request.GetActorId()))
		res.Status = creditReportConsentNotFound(consentNotGiven)
		return res, nil
	// skip if report is not present (when presence stage is enabled)
	case isPresenceEnabled(s.conf.CreditReportConfig()) && isReportNotPresent(creditReportResp.GetPresenceStatus()):
		logger.Error(ctx, "report not present for verification", zap.String(logger.ACTOR_ID_V2, request.GetActorId()))
		res.Status = creditReportVerificationReportNotPresent(creditReportNotFound)
		return res, nil
	// skip if verification is success or in progress
	case isVerificationSuccessOrInProgress(creditReportResp):
		logger.Info(ctx, fmt.Sprintf("skipping for verification for user, status: %v", creditReportResp.GetVerificationStatus()), zap.String(logger.ACTOR_ID_V2, request.GetActorId()))
		res.Status = rpc.StatusOk()
		return res, nil
	}
	if err := s.publishMsgToVerifyReport(ctx, request); err != nil {
		return &creditReportPb.InitiateCreditReportVerificationResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) RecordReportDownloadConsent(ctx context.Context, req *creditReportPb.RecordReportDownloadConsentRequest) (*creditReportPb.RecordReportDownloadConsentResponse, error) {
	res := &creditReportPb.RecordReportDownloadConsentResponse{}
	downloadConsent := creditReportPb.DownloadConsent_DOWNLOAD_CONSENT_NO_CONSENT
	if req.GetConsent() {
		downloadConsent = creditReportPb.DownloadConsent_DOWNLOAD_CONSENT_CONSENT_GIVEN
	}
	if err := s.upsertDownloadConsentInCreditReportTable(ctx, req.GetActorId(), downloadConsent); err != nil {
		res.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return res, nil
	}
	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) upsertDownloadConsentInCreditReportTable(ctx context.Context, actorId string, downloadConsent creditReportPb.DownloadConsent) error {
	if _, err := s.creditReportDao.GetByActorId(ctx, actorId); err != nil {
		if storageV2.IsRecordNotFoundError(err) {
			// create new credit report entry with consent and consent time
			if _, err = s.creditReportDao.Create(ctx, &creditReportPb.CreditReport{
				ActorId:              actorId,
				DownloadConsent:      downloadConsent,
				CreditConsentGivenAt: timestampPb.New(s.datetime.Now()),
				Vendor:               commonvgpb.Vendor_EXPERIAN,
			}); err != nil {
				logger.Error(ctx, "error creating credit report entry for actor", zap.Error(err))
				return err
			}
			return nil
		}
		logger.Error(ctx, "error getting credit report by actor id", zap.Error(err))
		return err
	}
	// update old entry with consent
	if err := s.creditReportDao.UpdateByActorId(ctx, &creditReportPb.CreditReport{
		ActorId:              actorId,
		DownloadConsent:      downloadConsent,
		CreditConsentGivenAt: timestampPb.New(s.datetime.Now()),
	}, []creditReportPb.CreditReportFieldMask{
		creditReportPb.CreditReportFieldMask_CREDIT_REPORT_FIELD_DOWNLOAD_CONSENT,
		creditReportPb.CreditReportFieldMask_CREDIT_REPORT_FIELD_CREDIT_CONSENT_GIVEN_AT,
	}); err != nil {
		logger.Error(ctx, "error updating credit report download consent", zap.Error(err))
		return err
	}
	return nil
}

func (s *Service) PurgeExperianDataForOldUsers(ctx context.Context, _ *creditReportPb.PurgeExperianDataForOldUsersRequest) (*creditReportPb.PurgeExperianDataForOldUsersResponse, error) {
	res := &creditReportPb.PurgeExperianDataForOldUsersResponse{
		Status:   rpc.StatusOk(),
		ActorIds: nil,
	}
	limit := 10000
	looplimit := 1000
	i := 0
	for {
		noOfActors, err := s.creditReportDao.DeleteColumnsForDataOlderThanTimestamp(ctx,
			time.Now().Add(time.Duration(-s.conf.ExperianDataStorageLimitInHrs())*time.Hour),
			[]creditReportPb.CreditReportFieldMask{creditReportPb.CreditReportFieldMask_CREDIT_REPORT_FIELD_CREDIT_REPORT_DATA,
				creditReportPb.CreditReportFieldMask_CREDIT_REPORT_FIELD_CREDIT_REPORT_DATA_RAW}, int32(limit))
		if err != nil {
			logger.Error(ctx, "failed to delete experian data older than timestamp", zap.Error(err))
			res.Status = rpc.StatusInternalWithDebugMsg(err.Error())
			return res, nil
		}
		if i >= looplimit || noOfActors < limit {
			break
		}
		i++
	}
	return res, nil
}

func (s *Service) StartDownloadProcess(ctx context.Context, req *creditReportPb.StartDownloadProcessRequest) (*creditReportPb.StartDownloadProcessResponse, error) {
	res := &creditReportPb.StartDownloadProcessResponse{}

	isDowntime := s.conf.DownTimeConfig().Get(req.GetVendor().String()).IsDownNow()
	if isDowntime {
		logger.Error(ctx, "can't call credit report service, downtime going on.", zap.String(logger.REQUEST_ID, req.GetRequestId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	existingCreditReportDownload, err := s.creditReportDownloadDao.GetByRequestId(ctx, req.GetRequestId())
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error in fetching credit report download details by request id", zap.Error(err),
			zap.String(logger.REQUEST_ID, req.GetRequestId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	if existingCreditReportDownload != nil {
		res.Status = rpc.StatusOk()
		res.ProcessStatus = existingCreditReportDownload.GetProcessStatus()
		res.ProcessSubStatus = existingCreditReportDownload.GetProcessSubStatus()
		res.NextAction = existingCreditReportDownload.GetNextAction()
		if res.GetNextAction().GetScreen() == deeplink.Screen_DEEP_LINK_URI_UNSPECIFIED {
			res.NextAction = crDeeplink.CreditReportPollStatusDeeplink(req.GetRequestId())
		}
		return res, nil
	}

	switch req.GetVendor() {
	case commonvgpb.Vendor_CIBIL:
		return s.startDownloadProcessForCibil(ctx, req)
	case commonvgpb.Vendor_EXPERIAN:
		return s.startDownloadProcessForExperian(ctx, req)
	default:
		logger.Error(ctx, "invalid vendor in StartDownloadProcess", zap.String(logger.VENDOR, req.GetVendor().String()))
		res.Status = rpc.StatusInvalidArgumentWithDebugMsg("unhandled vendor")
		return res, nil
	}
}

// nolint:funlen
func (s *Service) startDownloadProcessForExperian(ctx context.Context, req *creditReportPb.StartDownloadProcessRequest) (*creditReportPb.StartDownloadProcessResponse, error) {
	var (
		res                = &creditReportPb.StartDownloadProcessResponse{}
		otpAlreadyVerified bool
		otpInfo            *creditReportPb.OtpInfo
		isConsentTaken     bool
	)
	fetchType, err := s.getCreditReportFetchType(ctx, req)
	if err != nil {
		logger.Error(ctx, "failed to get credit report fetch type", zap.Error(err), zap.String(logger.REQUEST_ID, req.GetRequestId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	featureDetailsResp, err := s.onboardingClient.GetFeatureDetails(ctx, &onbPb.GetFeatureDetailsRequest{
		ActorId: req.GetActorId(),
		Feature: onbPb.Feature_FEATURE_FI_LITE,
	})
	if rpcErr := epifigrpc.RPCError(featureDetailsResp, err); rpcErr != nil {
		if featureDetailsResp.GetStatus().IsRecordNotFound() {
			logger.Info(ctx, "record not found for user", zap.Error(rpcErr))
		} else {
			logger.Error(ctx, "failed to get feature details for actor", zap.Error(rpcErr), zap.String(logger.REQUEST_ID, req.GetRequestId()))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
	}

	if (req.GetConsentDetails() != nil && req.GetProvenance() == creditReportPb.Provenance_PROVENANCE_CREDIT_CARD) ||
		req.GetProvenance() == creditReportPb.Provenance_PROVENANCE_D2H_ONBOARDING || req.GetProvenance() == creditReportPb.Provenance_PROVENANCE_SA_ONBOARDING ||
		req.GetProvenance() == creditReportPb.Provenance_PROVENANCE_LOAN_PRE_ELIGIBILITY ||
		req.GetProvenance() == creditReportPb.Provenance_PROVENANCE_WEB_CREDIT_REPORT_ANALYSER {
		isConsentTaken = true
	}

	// handling for skipping otp verification for credit card in case of otp already verified
	if (req.GetAuthInfo() != nil && req.GetProvenance() == creditReportPb.Provenance_PROVENANCE_CREDIT_CARD) ||
		req.GetProvenance() == creditReportPb.Provenance_PROVENANCE_D2H_ONBOARDING || req.GetProvenance() == creditReportPb.Provenance_PROVENANCE_SA_ONBOARDING || req.GetProvenance() == creditReportPb.Provenance_PROVENANCE_LOAN_PRE_ELIGIBILITY ||
		req.GetProvenance() == creditReportPb.Provenance_PROVENANCE_WEB_CREDIT_REPORT_ANALYSER || req.GetProvenance() == creditReportPb.Provenance_PROVENANCE_PERSONAL_LOAN_ATL || req.GetProvenance() == creditReportPb.Provenance_PROVENANCE_PERSONAL_LOAN {
		otpAlreadyVerified = true
		otpInfo = &creditReportPb.OtpInfo{
			RequestId:          req.GetAuthInfo().GetAuthRequestId(),
			OtpAlreadyVerified: otpAlreadyVerified,
		}
	}
	txnErr := s.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		creditReportDownload, err := s.creditReportDownloadDao.Create(txnCtx, &creditReportPb.CreditReportDownload{
			RequestId:        req.GetRequestId(),
			ActorId:          req.GetActorId(),
			Vendor:           req.GetVendor(),
			FetchType:        fetchType,
			ProcessStatus:    creditReportPb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_UNSPECIFIED,
			ProcessSubStatus: creditReportPb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_UNSPECIFIED,
			RedirectDeeplink: req.GetRedirectDeeplink(),
			Provenance:       req.GetProvenance(),
			OrchId:           uuid.New().String(),
			OtpInfo:          otpInfo,
			Details: &creditReportPb.Details{
				DownloadDetails: &creditReportPb.DownloadDetails{
					VendorDownloadDetails: &creditReportPb.DownloadDetails_ExperianDownloadDetails{
						ExperianDownloadDetails: &creditReportPb.ExperianDownloadDetails{
							AllowReportFetchWithoutPan: req.GetAllowWithoutPan(),
						},
					},
				},
			},
		})
		if err != nil {
			return fmt.Errorf("error in adding credit report download details to DB: %w", err)
		}
		payload, err := protojson.Marshal(&crWorkflowPb.DownloadCreditReportRequest{
			OrchId:                     creditReportDownload.GetOrchId(),
			FetchType:                  creditReportDownload.GetFetchType(),
			ClientReqId:                creditReportDownload.GetRequestId(),
			CreditReportDownloadId:     creditReportDownload.GetId(),
			RedirectDeeplink:           creditReportDownload.GetRedirectDeeplink(),
			IsFiLiteUser:               featureDetailsResp.GetIsFiLiteUser(),
			ActorId:                    req.GetActorId(),
			IsOtpAlreadyVerified:       otpAlreadyVerified,
			Pan:                        req.GetPan(),
			Provenance:                 req.GetProvenance(),
			IsConsentTaken:             isConsentTaken,
			Vendor:                     commonvgpb.Vendor_EXPERIAN,
			AllowReportFetchWithoutPan: req.GetAllowWithoutPan(),
		})
		if err != nil {
			return fmt.Errorf("error while marshaling workflow payload : %w", err)
		}
		err = s.initiateWorkflow(txnCtx, &celestialPb.ClientReqId{
			Id:     creditReportDownload.GetOrchId(),
			Client: workflowPb.Client_CREDIT_REPORT,
		}, req.GetActorId(), payload, workflowPb.Type_DOWNLOAD_CREDIT_REPORT, workflowPb.Version_V0)
		if err != nil {
			return fmt.Errorf("failed to initialize workflow: %w", err)
		}

		return nil
	})
	if txnErr != nil {
		logger.Error(ctx, "failed to start credit report download process", zap.Error(txnErr), zap.String(logger.REQUEST_ID, req.GetRequestId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	res.Status = rpc.StatusOk()
	res.NextAction = crDeeplink.CreditReportPollStatusDeeplink(req.GetRequestId())
	return res, nil
}

func (s *Service) startDownloadProcessForCibil(ctx context.Context, req *creditReportPb.StartDownloadProcessRequest) (*creditReportPb.StartDownloadProcessResponse, error) {
	var (
		res           = &creditReportPb.StartDownloadProcessResponse{}
		consentExists = false
		consentInfo   = &creditReportPb.ConsentInfo{}
		consentStatus creditReportPb.CreditReportConsentStatus
	)

	consResp, consErr := s.consentClient.FetchConsent(ctx, &consent.FetchConsentRequest{
		ConsentType: consent.ConsentType_CIBIL_REPORT_TNC,
		ActorId:     req.GetActorId(),
		Owner:       commontypes.Owner_OWNER_EPIFI_TECH,
	})
	err := epifigrpc.RPCError(consResp, consErr)
	switch {
	case err != nil:
		if consResp.GetStatus().IsRecordNotFound() {
			consentStatus = creditReportPb.CreditReportConsentStatus_CREDIT_REPORT_CONSENT_STATUS_UNAVAILABLE
			consentInfo = nil
			break
		}
		logger.Error(ctx, "error while getting consent", zap.Error(err))
		res.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return res, nil
	case time.Now().After(consResp.GetExpiresAt().AsTime()):
		consentStatus = creditReportPb.CreditReportConsentStatus_CREDIT_REPORT_CONSENT_STATUS_EXPIRED
		consentInfo = nil
	default:
		crd, crdErr := s.creditReportDownloadDao.GetLatestByActorIdAndVendor(ctx, req.GetActorId(), commonvgpb.Vendor_CIBIL)
		if crdErr != nil && !errors.Is(crdErr, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error in fetching credit report download details by actor id and vendor", zap.Error(crdErr))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		// Check if the consent ID is already used to download report.
		isUsed := false
		for _, id := range crd.GetConsentInfo().GetConsentIds() {
			if id == consResp.GetConsentId() {
				isUsed = true
				break
			}
		}
		// If the consent ID is not used to download the report, keep the status as active. Otherwise, if it is used to download the report, set the consent status as expired.
		if !isUsed {
			consentStatus = creditReportPb.CreditReportConsentStatus_CREDIT_REPORT_CONSENT_STATUS_ACTIVE
			consentExists = true
			consentInfo = &creditReportPb.ConsentInfo{
				ConsentStatus: creditReportPb.ConsentStatus_CONSENT_STATUS_GIVEN,
				ConsentReqId:  consResp.GetClientReqId(),
				ConsentIds:    []string{consResp.GetConsentId()},
			}
		} else {
			consentStatus = creditReportPb.CreditReportConsentStatus_CREDIT_REPORT_CONSENT_STATUS_EXPIRED
			consentInfo = nil
		}
	}

	fetchType, ok := consentStatusToFetchTypeMap[consentStatus]
	if !ok {
		res.Status = rpc.StatusInternalWithDebugMsg(fmt.Sprintf("unhandled consent status %s", consentStatus))
		return res, nil
	}

	txnErr := s.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		return s.createDataAndInitiateDownloadWfForCibil(txnCtx, req, fetchType, consentInfo, consentExists)
	})
	if txnErr != nil {
		logger.Error(ctx, "failed to start credit report download process", zap.Error(txnErr), zap.String(logger.REQUEST_ID, req.GetRequestId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	res.Status = rpc.StatusOk()
	res.NextAction = crDeeplink.CreditReportPollStatusDeeplink(req.GetRequestId())
	return res, nil
}

func (s *Service) createDataAndInitiateDownloadWfForCibil(txnCtx context.Context, req *creditReportPb.StartDownloadProcessRequest,
	fetchType creditReportPb.CreditReportFetchType, consentInfo *creditReportPb.ConsentInfo, consentExists bool) error {
	var subDetails *creditReportPb.CreditReportUserSubscriptionDetail
	var err, err2 error

	subDetails, err = s.creditReportUserSubscriptionDao.GetByActorIdAndVendor(txnCtx, req.GetActorId(), req.GetVendor())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		if subDetails, err2 = s.creditReportUserSubscriptionDao.Create(txnCtx, getNewCibilSubscriptionDetails(req.GetActorId(), req.GetVendor())); err2 != nil {
			return fmt.Errorf("error in adding credit report subscription details to DB: %w", err2)
		}
	case err != nil:
		return fmt.Errorf("error while getting user subscription details: %w", err)
	}

	newCrd, newErr := getNewCibilCreditReportDownload(req, fetchType, consentInfo, subDetails)
	if newErr != nil {
		return fmt.Errorf("error while populating credit report download details: %w", newErr)
	}

	crd, createErr := s.creditReportDownloadDao.Create(txnCtx, newCrd)
	if createErr != nil {
		return fmt.Errorf("error in adding credit report download details to DB: %w", createErr)
	}

	pl, marErr := protojson.Marshal(getDownloadCreditReportPayload(crd, req, consentExists))
	if marErr != nil {
		return fmt.Errorf("error while marshaling workflow payload : %w", marErr)
	}

	if initErr := s.initiateWorkflow(txnCtx, &celestialPb.ClientReqId{
		Id:     crd.GetOrchId(),
		Client: workflowPb.Client_CREDIT_REPORT,
	}, req.GetActorId(), pl, workflowPb.Type_DOWNLOAD_CREDIT_REPORT, workflowPb.Version_V0); initErr != nil {
		return fmt.Errorf("failed to initialize workflow: %w", initErr)
	}
	return nil
}

func getNewCibilCreditReportDownload(req *creditReportPb.StartDownloadProcessRequest, fetchType creditReportPb.CreditReportFetchType, consentInfo *creditReportPb.ConsentInfo, subDetails *creditReportPb.CreditReportUserSubscriptionDetail) (*creditReportPb.CreditReportDownload, error) {
	if subDetails.GetSubscriptionInfo().GetCibilSubscriptionInfo().GetCustomerId() == "" {
		return nil, errors.New("customer id is empty")
	}

	return &creditReportPb.CreditReportDownload{
		RequestId:        req.GetRequestId(),
		ActorId:          req.GetActorId(),
		Vendor:           req.GetVendor(),
		FetchType:        fetchType,
		ConsentInfo:      consentInfo,
		RedirectDeeplink: req.GetRedirectDeeplink(),
		Provenance:       req.GetProvenance(),
		OrchId:           uuid.NewString(),
		Details: &creditReportPb.Details{
			DownloadDetails: &creditReportPb.DownloadDetails{
				VendorDownloadDetails: &creditReportPb.DownloadDetails_CibilDownloadDetails{
					CibilDownloadDetails: &creditReportPb.CibilDownloadDetails{
						CustomerId: subDetails.GetSubscriptionInfo().GetCibilSubscriptionInfo().GetCustomerId(),
					},
				},
			},
		},
	}, nil
}

func getNewCibilSubscriptionDetails(actorId string, vendor commonvgpb.Vendor) *creditReportPb.CreditReportUserSubscriptionDetail {
	return &creditReportPb.CreditReportUserSubscriptionDetail{
		ActorId: actorId,
		Vendor:  vendor,
		SubscriptionInfo: &creditReportPb.SubscriptionInfo{
			Info: &creditReportPb.SubscriptionInfo_CibilSubscriptionInfo{
				CibilSubscriptionInfo: &creditReportPb.CibilSubscriptionInfo{
					CustomerId: uuid.NewString(),
				},
			},
		},
	}
}

func getDownloadCreditReportPayload(crd *creditReportPb.CreditReportDownload, req *creditReportPb.StartDownloadProcessRequest, consentExists bool) *crWorkflowPb.DownloadCreditReportRequest {
	return &crWorkflowPb.DownloadCreditReportRequest{
		OrchId:                 crd.GetOrchId(),
		CreditReportDownloadId: crd.GetId(),
		FetchType:              crd.GetFetchType(),
		ClientReqId:            crd.GetRequestId(),
		RedirectDeeplink:       crd.GetRedirectDeeplink(),
		ActorId:                req.GetActorId(),
		Pan:                    req.GetPan(),
		Provenance:             req.GetProvenance(),
		IsConsentTaken:         consentExists,
		Vendor:                 commonvgpb.Vendor_CIBIL,
		Name:                   req.GetName(),
	}
}

// nolint: funlen
func (s *Service) RecordUserConsent(ctx context.Context, req *creditReportPb.RecordUserConsentRequest) (*creditReportPb.RecordUserConsentResponse, error) {
	var res = &creditReportPb.RecordUserConsentResponse{}
	if req.GetConsentAction() == creditReportPb.ConsentAction_CONSENT_ACTION_UNSPECIFIED {
		logger.Error(ctx, "consent action cannot be unspecified", zap.String(logger.REQUEST_ID, req.GetRequestId()))
		res.Status = rpc.StatusInvalidArgument()
		return res, nil
	}
	// Credit report download entry should already exist for the given request Id. If it does not then we throw error.
	creditReportDownloadDetails, err := s.creditReportDownloadDao.GetByRequestId(ctx, req.GetRequestId())
	if err != nil {
		logger.Error(ctx, "failed to fetch credit report download details", zap.Error(err), zap.String(logger.REQUEST_ID, req.GetRequestId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	if creditReportDownloadDetails.GetId() == "" {
		logger.Error(ctx, "credit report download details id cannot be null", zap.String(logger.REQUEST_ID, req.GetRequestId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	if req.GetConsentAction() == creditReportPb.ConsentAction_CONSENT_ACTION_ACCEPTED {
		err = s.updateAcceptedConsent(ctx, req, creditReportDownloadDetails)
		if err != nil {
			logger.Error(ctx, "failed to update accepted consent", zap.Error(err), zap.String(logger.REQUEST_ID, req.GetRequestId()))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
	} else if req.GetConsentAction() == creditReportPb.ConsentAction_CONSENT_ACTION_REJECTED &&
		creditReportDownloadDetails.GetConsentInfo().GetConsentStatus() != creditReportPb.ConsentStatus_CONSENT_STATUS_REJECTED {
		err = s.creditReportDownloadDao.Update(ctx, &creditReportPb.CreditReportDownload{
			Id: creditReportDownloadDetails.GetId(),
			ConsentInfo: &creditReportPb.ConsentInfo{
				ConsentStatus: creditReportPb.ConsentStatus_CONSENT_STATUS_REJECTED,
			},
		}, []creditReportPb.CreditReportDownloadFieldMask{creditReportPb.CreditReportDownloadFieldMask_CREDIT_REPORT_DOWNLOAD_FIELD_CONSENT_INFO})
		if err != nil {
			logger.Error(ctx, "failed to update rejected consent info", zap.Error(err), zap.String(logger.REQUEST_ID, req.GetRequestId()))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
	}
	payloadInBytes, err := protojson.Marshal(&payload.ConsentActionSignalPayload{})
	if err != nil {
		logger.Error(ctx, "unable to marshal the ConsentActionSignalPayload payload", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	signalRes, err := s.celestialClient.SignalWorkflow(ctx, &celestialPb.SignalWorkflowRequest{
		Identifier: &celestialPb.SignalWorkflowRequest_ClientReqId{
			ClientReqId: &celestialPb.ClientReqId{
				Id:     creditReportDownloadDetails.GetOrchId(),
				Client: workflowPb.Client_CREDIT_REPORT,
			},
		},
		Payload:   payloadInBytes,
		SignalId:  string(creditreportNs.ConsentActionSignal),
		Ownership: commontypes.Ownership_EPIFI_TECH,
	})
	if rpcErr := epifigrpc.RPCError(signalRes, err); rpcErr != nil {
		// only logging error as workflow is also polling consent action
		logger.Error(ctx, "error while sending signal", zap.Error(rpcErr))
	}
	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) updateAcceptedConsent(ctx context.Context, req *creditReportPb.RecordUserConsentRequest,
	creditReportDownloadDetails *creditReportPb.CreditReportDownload) error {
	switch {
	case creditReportDownloadDetails.GetVendor() == commonvgpb.Vendor_CIBIL:
		if err := s.saveNewConsentDetailsForCibil(ctx, req, creditReportDownloadDetails); err != nil {
			return fmt.Errorf("failed to save new consent details")
		}
	case creditReportDownloadDetails.GetVendor() == commonvgpb.Vendor_EXPERIAN && creditReportDownloadDetails.GetConsentInfo().GetConsentReqId() == "":
		if err := s.saveNewConsentDetailsForExperian(ctx, req, creditReportDownloadDetails); err != nil {
			return fmt.Errorf("failed to save new consent details")
		}
	case creditReportDownloadDetails.GetVendor() == commonvgpb.Vendor_EXPERIAN && len(creditReportDownloadDetails.GetConsentInfo().GetConsentIds()) == 0:
		if err := s.saveConsentIdInConsentInfoFOrExperian(ctx, req, creditReportDownloadDetails); err != nil {
			return fmt.Errorf("failed to update consent id in consent info")
		}
	default:
		logger.Info(ctx, "Consent details already saved")
	}

	return nil
}

func (s *Service) saveNewConsentDetailsForCibil(ctx context.Context, req *creditReportPb.RecordUserConsentRequest, creditReportDownloadDetails *creditReportPb.CreditReportDownload) error {
	clientReqId := uuid.New().String()

	recordConsentRes, recordConsentErr := s.consentClient.RecordConsent(ctx, &consent.RecordConsentRequest{
		ConsentType: consent.ConsentType_CIBIL_REPORT_TNC,
		Version:     0,
		Device:      req.GetDevice(),
		ActorId:     req.GetActorId(),
		ClientReqId: clientReqId,
		ExpiresAt:   timestampPb.New(time.Now().Add(s.conf.CibilConsentExpiryInHours())),
		Owner:       commontypes.Owner_OWNER_EPIFI_TECH,
	})
	if grpcErr := epifigrpc.RPCError(recordConsentRes, recordConsentErr); grpcErr != nil {
		return fmt.Errorf("failed to record consent for credit report update type, err: %w", grpcErr)
	}
	if recordConsentRes.GetConsentId() == "" {
		return fmt.Errorf("empty consent id returned by recordConsent rpc")
	}

	if err := s.creditReportDownloadDao.Update(ctx, &creditReportPb.CreditReportDownload{
		Id: creditReportDownloadDetails.GetId(),
		ConsentInfo: &creditReportPb.ConsentInfo{
			ConsentIds:    []string{recordConsentRes.GetConsentId()},
			ConsentReqId:  clientReqId,
			ConsentStatus: creditReportPb.ConsentStatus_CONSENT_STATUS_GIVEN,
		},
	}, []creditReportPb.CreditReportDownloadFieldMask{
		creditReportPb.CreditReportDownloadFieldMask_CREDIT_REPORT_DOWNLOAD_FIELD_CONSENT_INFO,
	}); err != nil {
		return fmt.Errorf("failed to update credit report download consent info, err: %w", err)
	}
	return nil
}

// nolint: funlen
func (s *Service) saveNewConsentDetailsForExperian(ctx context.Context, req *creditReportPb.RecordUserConsentRequest, creditReportDownloadDetails *creditReportPb.CreditReportDownload) error {
	var (
		consentIds []string
	)
	clientReqId := uuid.New().String()
	consentInfo := &creditReportPb.ConsentInfo{
		ConsentReqId: clientReqId,
	}

	// Update client req id first
	err := s.creditReportDownloadDao.Update(ctx, &creditReportPb.CreditReportDownload{
		Id:          creditReportDownloadDetails.GetId(),
		ConsentInfo: consentInfo,
	}, []creditReportPb.CreditReportDownloadFieldMask{creditReportPb.CreditReportDownloadFieldMask_CREDIT_REPORT_DOWNLOAD_FIELD_CONSENT_INFO})
	if err != nil {
		return fmt.Errorf("failed to update credit report download consent info, err: %w", err)
	}

	isFiLiteUser, err := s.isFiLiteUser(ctx, req.GetActorId())
	if err != nil {
		return fmt.Errorf("failed to check if user is fi lite user : %w", err)
	}
	if !isFiLiteUser {
		// Create a consent for generated client req id
		recordConsentRes, recordConsentErr := s.consentClient.RecordConsent(ctx, &consent.RecordConsentRequest{
			ConsentType: consent.ConsentType_CREDIT_REPORT_UPDATED_TNC,
			Version:     0,
			Device:      req.GetDevice(),
			ActorId:     req.GetActorId(),
			ClientReqId: clientReqId,
			Owner:       commontypes.Owner_OWNER_EPIFI_TECH,
		})
		if grpcErr := epifigrpc.RPCError(recordConsentRes, recordConsentErr); grpcErr != nil {
			return fmt.Errorf("failed to record consent for credit report update type, err: %w", grpcErr)
		}
		if recordConsentRes.GetConsentId() == "" {
			return fmt.Errorf("empty consent id returned by recordConsent rpc")
		}
		consentIds = append(consentIds, recordConsentRes.GetConsentId())
	} else {
		consentsInfoRes, recordConsentsErr := s.recordUpdatedAndLimitedTimeTncConsents(ctx, req.GetActorId(), req.GetDevice(), clientReqId)
		if recordConsentsErr != nil {
			return fmt.Errorf("failed to record consent for fi lite user, err : %w", recordConsentsErr)
		}
		for _, info := range consentsInfoRes {
			if info.GetConsentId() == "" {
				return fmt.Errorf("empty consent id returned by recordConsent rpc")
			}
			consentIds = append(consentIds, info.GetConsentId())
		}
	}
	if len(consentIds) == 0 {
		return fmt.Errorf("no consent ids returned from consent service, failure")
	}
	// Update with consentId
	consentInfo = &creditReportPb.ConsentInfo{
		ConsentIds:    consentIds,
		ConsentReqId:  clientReqId,
		ConsentStatus: creditReportPb.ConsentStatus_CONSENT_STATUS_GIVEN,
	}
	err = s.creditReportDownloadDao.Update(ctx, &creditReportPb.CreditReportDownload{
		Id:          creditReportDownloadDetails.GetId(),
		ConsentInfo: consentInfo,
	}, []creditReportPb.CreditReportDownloadFieldMask{creditReportPb.CreditReportDownloadFieldMask_CREDIT_REPORT_DOWNLOAD_FIELD_CONSENT_INFO})
	if err != nil {
		return fmt.Errorf("failed to update credit report download consent info, err: %w", err)
	}

	return nil
}

// nolint: funlen
func (s *Service) saveConsentIdInConsentInfoFOrExperian(ctx context.Context, req *creditReportPb.RecordUserConsentRequest,
	creditReportDownloadDetails *creditReportPb.CreditReportDownload) error {
	consentReqId := creditReportDownloadDetails.GetConsentInfo().GetConsentReqId()
	consentDetails, err := s.consentClient.FetchConsentByReqId(ctx, &consent.FetchConsentByReqIdRequest{
		ClientReqId: consentReqId,
		Owner:       commontypes.Owner_OWNER_EPIFI_TECH,
	})
	consentIds := []string{
		consentDetails.GetConsentId(),
	}
	switch {
	case err != nil:
		return fmt.Errorf("failed to fetch consent details, err: %w", err)
	case consentDetails.GetStatus().IsRecordNotFound():
		isFiLiteUser, fiLiteCheckErr := s.isFiLiteUser(ctx, req.GetActorId())
		if fiLiteCheckErr != nil {
			return fmt.Errorf("failed to check if user is fi lite user : %w", fiLiteCheckErr)
		}
		if !isFiLiteUser {
			// Create an entry in Consent Table as it does not exist currently
			consentResp, consentErr := s.consentClient.RecordConsent(ctx, &consent.RecordConsentRequest{
				ConsentType: consent.ConsentType_CREDIT_REPORT_UPDATED_TNC,
				Version:     0,
				Device:      req.GetDevice(),
				ActorId:     req.GetActorId(),
				ClientReqId: consentReqId,
				Owner:       commontypes.Owner_OWNER_EPIFI_TECH,
			})
			if grpcErr := epifigrpc.RPCError(consentResp, consentErr); grpcErr != nil {
				return fmt.Errorf("failed to record consent for credit report update type, err: %w", grpcErr)
			}
			if consentResp.GetConsentId() == "" {
				return fmt.Errorf("consent Id cannot be null")
			}
			consentIds = []string{consentResp.GetConsentId()}
		} else {
			consentsInfoRes, consentsRecordErr := s.recordUpdatedAndLimitedTimeTncConsents(ctx, req.GetActorId(), req.GetDevice(), consentReqId)
			if consentsRecordErr != nil {
				return fmt.Errorf("failed to record consent for fi lite user : %w", consentsRecordErr)
			}
			consentIds = []string{}
			for _, info := range consentsInfoRes {
				if info.GetConsentId() == "" {
					return fmt.Errorf("empty consent id returned by recordConsent rpc")
				}
				consentIds = append(consentIds, info.GetConsentId())
			}
		}
	case !consentDetails.GetStatus().IsSuccess():
		return fmt.Errorf("failed to fetch consent details by req id")
	}
	if len(consentIds) == 0 {
		return fmt.Errorf("no consent ids returned from consent service, failure")
	}
	// Save consent id in Credit Report Consent Info
	consentInfo := &creditReportPb.ConsentInfo{
		ConsentIds:    consentIds,
		ConsentReqId:  consentReqId,
		ConsentStatus: creditReportPb.ConsentStatus_CONSENT_STATUS_GIVEN,
	}
	err = s.creditReportDownloadDao.Update(ctx, &creditReportPb.CreditReportDownload{
		Id:          creditReportDownloadDetails.GetId(),
		ConsentInfo: consentInfo,
	}, []creditReportPb.CreditReportDownloadFieldMask{creditReportPb.CreditReportDownloadFieldMask_CREDIT_REPORT_DOWNLOAD_FIELD_CONSENT_INFO})
	if err != nil {
		return fmt.Errorf("failed to update credit report download consent info, err: %w", err)
	}

	return nil
}

func (s *Service) GetNextActionForCreditReportDownload(ctx context.Context,
	req *creditReportPb.GetNextActionForCreditReportDownloadRequest) (res *creditReportPb.GetNextActionForCreditReportDownloadResponse, err error) {
	res = &creditReportPb.GetNextActionForCreditReportDownloadResponse{}
	actorIdFromCtx := epificontext.ActorIdFromContext(ctx)
	if strings.EqualFold(actorIdFromCtx, epificontext.UnknownId) {
		actorIdFromCtx = ""
	}
	crd, err := s.creditReportDownloadDao.GetByRequestId(ctx, req.GetClientRequestId())
	if err != nil {
		logger.Error(ctx, "error while fetching credit report download entry", zap.Error(err))
		if errors.Is(epifierrors.ErrRecordNotFound, err) {
			res.Status = rpc.StatusRecordNotFound()
		} else {
			res.Status = rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error while fetching credit report download entry : %s", err.Error()))
		}
		return res, nil
	}
	if crd.GetActorId() != "" && actorIdFromCtx != "" && crd.GetActorId() != actorIdFromCtx {
		logger.Error(ctx, fmt.Sprintf("actor id mismatch. want actor id : [%s], got actor id : [%s]", actorIdFromCtx, crd.GetActorId()))
		res.Status = rpc.StatusPermissionDenied()
		return res, nil
	}

	// in case next action is credit poll and process has failed, we exit to redirect deeplink so that user will
	// not be in an infinite polling loop
	if crd.GetProcessStatus() == creditReportPb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_FAILED &&
		crd.GetNextAction().GetScreen() == deeplink.Screen_CREDIT_REPORT_POLL_STATUS {
		crd.NextAction = crd.GetRedirectDeeplink()
		if err = s.creditReportDownloadDao.Update(ctx, crd, []creditReportPb.CreditReportDownloadFieldMask{
			creditReportPb.CreditReportDownloadFieldMask_CREDIT_REPORT_DOWNLOAD_FIELD_NEXT_ACTION,
		}); err != nil {
			logger.Error(ctx, "error while updating next action in credit report downloads", zap.Error(err))
		}
		return &creditReportPb.GetNextActionForCreditReportDownloadResponse{
			Status:     rpc.StatusOk(),
			NextAction: crd.GetRedirectDeeplink(),
		}, nil
	}

	res.NextAction = crd.GetNextAction()
	if res.GetNextAction() == nil {
		res.NextAction = crDeeplink.CreditReportPollStatusDeeplink(crd.GetRequestId())
	}
	if res.GetNextAction().GetScreen() == deeplink.Screen_REGISTER_CKYC {
		nextAction, err2 := s.handlePanDobScreenNextAction(ctx, crd)
		if err2 != nil {
			logger.Error(ctx, "failed to handle pan-dob screen next action", zap.Error(err))
			res.Status = rpc.StatusInternalWithDebugMsg(fmt.Sprint("failed to handle pan-dob screen next action"))
			return res, nil
		}
		res.NextAction = nextAction
	}
	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) GetLatestDownloadProcessDetails(ctx context.Context,
	req *creditReportPb.GetLatestDownloadProcessDetailsRequest) (res *creditReportPb.GetLatestDownloadProcessDetailsResponse, err error) {
	res = &creditReportPb.GetLatestDownloadProcessDetailsResponse{}
	filterOptions := []storageV2.FilterOption{dao.WithOrderByCreatedAtDescending(), dao.WithLimit(1)}
	if req.GetVendor() != commonvgpb.Vendor_VENDOR_UNSPECIFIED {
		filterOptions = append(filterOptions, dao.WithVendor(req.GetVendor()))
	}
	if req.GetProvenance() != creditReportPb.Provenance_PROVENANCE_UNSPECIFIED {
		filterOptions = append(filterOptions, dao.WithProvenance(req.GetProvenance()))
	}
	crdArr, err := s.creditReportDownloadDao.GetByActorId(ctx, req.GetActorId(), filterOptions...)
	if err != nil {
		logger.Error(ctx, "error while fetching credit report download entry", zap.Error(err))
		res.Status = rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error while fetching credit report download entry : %s", err.Error()))
		return res, nil
	}
	if len(crdArr) == 0 {
		logger.Info(ctx, "no credit report download records found for actor")
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	}
	crd := crdArr[0]
	return &creditReportPb.GetLatestDownloadProcessDetailsResponse{
		Status:           rpc.StatusOk(),
		RequestId:        crd.GetRequestId(),
		ProcessStatus:    crd.GetProcessStatus(),
		ProcessSubStatus: crd.GetProcessSubStatus(),
		NextAction:       crd.GetNextAction(),
		CreatedAt:        crd.GetCreatedAt(),
		DownloadedAt:     crd.GetDownloadedAt(),
	}, nil
}

func (s *Service) RecordAuthFlowCompletion(ctx context.Context,
	req *creditReportPb.RecordAuthFlowCompletionRequest) (res *creditReportPb.RecordAuthFlowCompletionResponse, err error) {
	res = &creditReportPb.RecordAuthFlowCompletionResponse{}
	actorIdFromCtx := epificontext.ActorIdFromContext(ctx)
	if strings.EqualFold(actorIdFromCtx, epificontext.UnknownId) {
		actorIdFromCtx = ""
	}
	var crd *creditReportPb.CreditReportDownload
	err = s.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		crd, err = s.creditReportDownloadDao.GetByRequestId(txnCtx, req.GetClientReqId(), dao.WithLock())
		if err != nil {
			return fmt.Errorf("error while fetching credit report download entry : %w", err)
		}
		if crd.GetProcessStatus() != creditReportPb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_OTP_VERIFICATION_INITIATED {
			logger.Info(txnCtx, "not updating entry as status is not otp verification init")
			return nil
		}
		if crd.GetActorId() != "" && actorIdFromCtx != "" && crd.GetActorId() != actorIdFromCtx {
			return epifierrors.ErrPermissionDenied
		}
		crd.NextAction = crDeeplink.CreditReportPollStatusDeeplink(crd.GetRequestId())
		err = s.creditReportDownloadDao.Update(txnCtx, &creditReportPb.CreditReportDownload{
			Id:         crd.GetId(),
			NextAction: crd.NextAction,
		}, []creditReportPb.CreditReportDownloadFieldMask{
			creditReportPb.CreditReportDownloadFieldMask_CREDIT_REPORT_DOWNLOAD_FIELD_NEXT_ACTION,
		})
		if err != nil {
			return fmt.Errorf("error while updating credit report download entry : %w", err)
		}
		return nil
	})
	if err != nil {
		logger.Error(ctx, "error while updating credit report entry", zap.Error(err), zap.String(logger.REQUEST_ID, req.GetClientReqId()))
		res.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return res, nil
	}
	payloadInBytes, err := protojson.Marshal(&payload.AuthFlowCompletionSignalPayload{})
	if err != nil {
		logger.Error(ctx, "unable to marshal the AuthFlowCompletionSignalPayload payload", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	signalRes, err := s.celestialClient.SignalWorkflow(ctx, &celestialPb.SignalWorkflowRequest{
		Identifier: &celestialPb.SignalWorkflowRequest_ClientReqId{
			ClientReqId: &celestialPb.ClientReqId{
				Id:     crd.GetOrchId(),
				Client: workflowPb.Client_CREDIT_REPORT,
			},
		},
		SignalId:  string(creditreportNs.AuthFlowCompletionSignal),
		Ownership: commontypes.Ownership_EPIFI_TECH,
		Payload:   payloadInBytes,
	})
	if rpcErr := epifigrpc.RPCError(signalRes, err); rpcErr != nil {
		// only logging error as workflow is also polling consent action
		logger.Error(ctx, "error while sending auth flow completion signal", zap.Error(rpcErr))
	}
	res.Status = rpc.StatusOk()
	res.NextAction = crd.GetNextAction()
	return res, nil
}

// nolint:dupl
func (s *Service) publishMsgToVerifyReport(ctx context.Context, request *creditReportPb.InitiateCreditReportVerificationRequest) error {
	msgId, err := s.creditReportVerificationPublisher.Publish(ctx, &consumer.ProcessReportVerificationRequest{
		ActorId:               request.GetActorId(),
		VerificationStartedAt: timestampPb.Now(),
	})
	if err != nil {
		logger.Error(ctx, "error in publishing report verification msg to queue", zap.String(logger.ACTOR_ID_V2, request.GetActorId()), zap.Error(err))
		return err
	}
	logger.Info(ctx, "report verification publish to queue successful", zap.String(logger.ACTOR_ID_V2, request.GetActorId()), zap.String(logger.QUEUE_MESSAGE_ID, msgId))
	return nil
}

// nolint:dupl
func (s *Service) publishMsgToCheckReportPresence(ctx context.Context, request *creditReportPb.InitiateCreditReportPresenceCheckRequest) error {
	msgId, err := s.creditReportPresencePublisher.Publish(ctx, &consumer.ProcessReportPresenceCheckRequest{
		ActorId:                request.GetActorId(),
		PresenceCheckStartedAt: timestampPb.Now(),
	})
	if err != nil {
		logger.Error(ctx, "error in publishing report presence check msg to queue", zap.String(logger.ACTOR_ID_V2, request.GetActorId()), zap.Error(err))
		return err
	}
	logger.Info(ctx, "report presence check publish to queue successful", zap.String(logger.ACTOR_ID_V2, request.GetActorId()), zap.String(logger.QUEUE_MESSAGE_ID, msgId))
	return nil
}

// isPresenceEnabled returns true if credit report presence check flag is enabled
func isPresenceEnabled(conf *genconf.CreditReportConfig) bool {
	return conf.CreditReportPresenceEnabled()
}

// isVerificationSuccessOrInProgress returns true if verification status is success or in progress
// not adding failure status to allow new msg to be published inorder to re-verify(after fixture or via dev action)
func isVerificationSuccessOrInProgress(resp *creditReportPb.CreditReport) bool {
	return resp.GetVerificationStatus() == creditReportPb.VerificationStatus_VERIFICATION_STATUS_IN_PROGRESS || resp.GetVerificationStatus() == creditReportPb.VerificationStatus_VERIFICATION_STATUS_SUCCESS
}

// isReportNotPresent returns true if report is not present
func isReportNotPresent(status creditReportPb.PresenceStatus) bool {
	return status == creditReportPb.PresenceStatus_REPORT_PRESENCE_STATUS_NOT_PRESENT ||
		status == creditReportPb.PresenceStatus_REPORT_PRESENCE_STATUS_AVAILABLE_BUT_UNAUTHENTICATED ||
		status == creditReportPb.PresenceStatus_REPORT_PRESENCE_STATUS_ONLY_CREDIT_ENQUIRY_AVAILABLE
}

// isReportDownloadConsentGiven returns true if user has given consent to download the report
func isReportDownloadConsentGiven(consent creditReportPb.DownloadConsent) bool {
	return consent == creditReportPb.DownloadConsent_DOWNLOAD_CONSENT_CONSENT_GIVEN
}

// This method returns with what type of consent should the credit report be fetched. Either a new consent fetch is required if no credit report exist or if consent has expired
// if consent has not expired then Existing consent can be used. And if consent is about to be expired then it can be extended.
func (s *Service) getCreditReportFetchType(ctx context.Context, req *creditReportPb.StartDownloadProcessRequest) (creditReportPb.CreditReportFetchType, error) {
	consentStatus, err := s.getCreditReportConsentStatus(ctx, req.GetActorId(), req.GetVendor())
	if err != nil {
		return creditReportPb.CreditReportFetchType_CREDIT_REPORT_FETCH_TYPE_UNSPECIFIED, fmt.Errorf("failed to get credit report consent status: %w", err)
	}

	fetchType, ok := consentStatusToFetchTypeMap[consentStatus]
	if !ok {
		return creditReportPb.CreditReportFetchType_CREDIT_REPORT_FETCH_TYPE_UNSPECIFIED, fmt.Errorf("unhandled consent status %v", consentStatus)
	}

	return fetchType, nil
}

// getCreditReportConsentStatus returns status of consent for an actor depending on consent parameters
func (s *Service) getCreditReportConsentStatus(ctx context.Context, actorId string, vendor commonvgpb.Vendor) (creditReportPb.CreditReportConsentStatus, error) {
	subscriptionDetails, err := s.creditReportUserSubscriptionDao.GetByActorIdAndVendor(ctx, actorId, vendor)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Info(ctx, "no record found for user subscription info")
			return creditReportPb.CreditReportConsentStatus_CREDIT_REPORT_CONSENT_STATUS_UNAVAILABLE, nil
		}
		return creditReportPb.CreditReportConsentStatus_CREDIT_REPORT_CONSENT_STATUS_UNSPECIFIED, fmt.Errorf("failed to fetch credit report subscription details: %w", err)
	}

	consentTakenAt := subscriptionDetails.GetSubscriptionInfo().GetExperianSubscriptionInfo().GetConsentTakenAt()
	consentExtendedAt := subscriptionDetails.GetSubscriptionInfo().GetExperianSubscriptionInfo().GetConsentExtensionDoneAt()
	hitId := subscriptionDetails.GetSubscriptionInfo().GetExperianSubscriptionInfo().GetHitId()

	if consentTakenAt == nil || hitId == "" {
		logger.Info(ctx, "consentTakenAt or hitId is null", zap.Any("ConsentTakenAt", consentTakenAt),
			zap.String("hitId", hitId))
		return creditReportPb.CreditReportConsentStatus_CREDIT_REPORT_CONSENT_STATUS_EXPIRED, nil
	}

	consentExtensionDuration := s.conf.CreditReportConfig().ExperianConsentConfig().ConsentExtension()
	consentExpiryDuration := s.conf.CreditReportConfig().ExperianConsentConfig().ConsentExpiry()
	consentTakenTime := consentTakenAt.AsTime().In(datetime.IST)
	durationSinceLastConsent := s.datetime.Since(consentTakenTime)

	logger.Info(ctx, "details used to calculate fetch type", zap.String("consentTakenAt", consentTakenAt.AsTime().String()),
		zap.String("consentExtendedAt", consentExtendedAt.AsTime().String()),
		zap.String("consentExtensionDuration", consentExtensionDuration.String()),
		zap.String("consentExpiryDuration", consentExpiryDuration.String()))

	if consentExtendedAt == nil {
		switch {
		case durationSinceLastConsent < consentExtensionDuration:
			logger.Info(ctx, "consent not extended. duration since last consent is less than consent extension duration")
			return creditReportPb.CreditReportConsentStatus_CREDIT_REPORT_CONSENT_STATUS_ACTIVE, nil
		case durationSinceLastConsent >= consentExtensionDuration && durationSinceLastConsent < consentExpiryDuration:
			logger.Info(ctx, "consent in extension range")
			return creditReportPb.CreditReportConsentStatus_CREDIT_REPORT_CONSENT_STATUS_EXTENSION_REQUIRED, nil
		default:
			logger.Info(ctx, "consent not extended. consent expired.")
			return creditReportPb.CreditReportConsentStatus_CREDIT_REPORT_CONSENT_STATUS_EXPIRED, nil
		}
	} else {
		durationSinceConsentExtension := s.datetime.Since(consentExtendedAt.AsTime().In(datetime.IST))
		if durationSinceConsentExtension < consentExpiryDuration {
			logger.Info(ctx, "consent extended. Duration since consent extension less than consent expiry")
			return creditReportPb.CreditReportConsentStatus_CREDIT_REPORT_CONSENT_STATUS_ACTIVE, nil
		} else {
			logger.Info(ctx, "consent extended. consent expired")
			return creditReportPb.CreditReportConsentStatus_CREDIT_REPORT_CONSENT_STATUS_EXPIRED, nil
		}
	}
}

func (s *Service) initiateWorkflow(ctx context.Context, clientReqId *celestialPb.ClientReqId, actorId string,
	payload []byte, workflowType workflowPb.Type, version workflowPb.Version) error {
	var qos celestialPb.QoS
	if cfg.IsNonProdEnv(s.conf.Application().Environment) && cfg.IsRemoteDebugEnabled() {
		qos = celestialPb.QoS_BEST_EFFORT
	}
	initiateWorkflowRes, err := s.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
		Params: &celestialPb.WorkflowCreationRequestParams{
			ActorId: actorId,
			Version: version,
			Type:    celestialPkg.GetTypeEnumFromProtoEnum(workflowType),
			Payload: payload,
			ClientReqId: &workflowPb.ClientReqId{
				Id:     clientReqId.GetId(),
				Client: clientReqId.GetClient(),
			},
			QualityOfService: qos,
		},
	})

	if te := epifigrpc.RPCError(initiateWorkflowRes, err); te != nil {
		return fmt.Errorf("error while initiating workflow for client req id %s, %w", clientReqId.GetId(), te)
	}
	return nil
}

// recordUpdatedAndLimitedTimeTncConsents records both updated tnc and limited time consent for credit report for a fi lite user
func (s *Service) recordUpdatedAndLimitedTimeTncConsents(ctx context.Context, actorId string, device *commontypes.Device,
	clientReqId string) ([]*consent.ConsentResponseInfo, error) {
	// Create 2 consent for fi lite user
	recordConsentRes, recordErr := s.consentClient.RecordConsents(ctx, &consent.RecordConsentsRequest{
		Consents: []*consent.ConsentRequestInfo{
			{
				ConsentType: consent.ConsentType_CREDIT_REPORT_UPDATED_TNC,
				ClientReqId: clientReqId,
			},
			{
				ConsentType: consent.ConsentType_CREDIT_REPORT_LIMITED_TIME_TNC,
				ClientReqId: uuid.New().String(),
			},
		},
		ActorId: actorId,
		Device:  device,
		Owner:   commontypes.Owner_OWNER_EPIFI_TECH,
	})
	if grpcErr := epifigrpc.RPCError(recordConsentRes, recordErr); grpcErr != nil {
		return nil, fmt.Errorf("failed to record consent for fi lite user, err: %w", grpcErr)
	}
	return recordConsentRes.GetConsentResponses(), nil
}

func (s *Service) isFiLiteUser(ctx context.Context, actorId string) (bool, error) {
	featureDetailsResp, err := s.onboardingClient.GetFeatureDetails(ctx, &onbPb.GetFeatureDetailsRequest{
		ActorId: actorId,
		Feature: onbPb.Feature_FEATURE_FI_LITE,
	})
	if rpcErr := epifigrpc.RPCError(featureDetailsResp, err); rpcErr != nil {
		if featureDetailsResp.GetStatus().IsRecordNotFound() {
			logger.Info(ctx, "record not found for user", zap.Error(rpcErr))
			return false, nil
		}
		return false, fmt.Errorf("failed to get feature details for actor : %w", rpcErr)
	}
	return featureDetailsResp.GetIsFiLiteUser(), nil
}

// handlePanDobScreenNextAction check if pan-dob is present for user, if present sends a signal to cs workflow which continues the consent flow
func (s *Service) handlePanDobScreenNextAction(ctx context.Context, crd *creditReportPb.CreditReportDownload) (*deeplink.Deeplink, error) {
	panDobPresent, err := s.isPanDobPresentForUser(ctx, crd.GetActorId())
	if err != nil {
		return nil, fmt.Errorf("error while checking if pan dob is present : %w", err)
	}
	if !panDobPresent {
		// crd next action here is pan-dob screen only
		return crd.GetNextAction(), nil
	}

	// at this stage signifies pan dob is present for user in db

	// update the next action in db to be polling screen
	err = s.creditReportDownloadDao.Update(ctx, &creditReportPb.CreditReportDownload{
		Id:         crd.GetId(),
		NextAction: crDeeplink.CreditReportPollStatusDeeplink(crd.GetRequestId()),
	}, []creditReportPb.CreditReportDownloadFieldMask{
		creditReportPb.CreditReportDownloadFieldMask_CREDIT_REPORT_DOWNLOAD_FIELD_NEXT_ACTION,
	})
	if err != nil {
		return nil, fmt.Errorf("error while updating credit report download entry : %w", err)
	}

	// signal to workflow that pan-dob submission is success
	payloadInBytes, err := protojson.Marshal(&payload.PanDobSubmittedSignalPayload{})
	if err != nil {
		return nil, fmt.Errorf("unable to marshal the AuthFlowCompletionSignalPayload payload : %w", err)
	}
	signalRes, err2 := s.celestialClient.SignalWorkflow(ctx, &celestialPb.SignalWorkflowRequest{
		Identifier: &celestialPb.SignalWorkflowRequest_ClientReqId{
			ClientReqId: &celestialPb.ClientReqId{
				Id:     crd.GetOrchId(),
				Client: workflowPb.Client_CREDIT_REPORT,
			},
		},
		SignalId:  string(creditreportNs.PanDobSubmittedActionSignal),
		Ownership: commontypes.Ownership_EPIFI_TECH,
		Payload:   payloadInBytes,
	})
	if rpcErr := epifigrpc.RPCError(signalRes, err2); rpcErr != nil {
		// only logging error as workflow is also polling pan-dob status
		logger.Error(ctx, "error while sending pan-dob submitted completion signal", zap.Error(rpcErr))
	}

	return crDeeplink.CreditReportPollStatusDeeplink(crd.GetRequestId()), nil
}

func (s *Service) isPanDobPresentForUser(ctx context.Context, actorId string) (bool, error) {
	userResp, err := s.userClient.GetUser(ctx, &user.GetUserRequest{
		Identifier: &user.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if rpcErr := epifigrpc.RPCError(userResp, err); rpcErr != nil {
		return false, fmt.Errorf("failed to get user by actor_id : %w", rpcErr)
	}
	userProfile := userResp.GetUser().GetProfile()
	return userProfile.GetPAN() != "" && userProfile.GetDateOfBirth() != nil, nil
}

// GetCreditReportConsentStatus evaluate credit report consent params and returns the consent status
func (s *Service) GetCreditReportConsentStatus(ctx context.Context, req *creditReportPb.GetCreditReportConsentStatusRequest) (*creditReportPb.GetCreditReportConsentStatusResponse, error) {
	res := &creditReportPb.GetCreditReportConsentStatusResponse{}
	consentStatus, err := s.getCreditReportConsentStatus(ctx, req.GetActorId(), req.GetVendor())
	if err != nil {
		logger.Error(ctx, "failed to get credit report consent status", zap.Error(err))
		res.Status = rpc.StatusInternalWithDebugMsg(fmt.Sprintf("failed to get credit report consent status: %v", err))
		return res, nil
	}
	res.CreditReportConsentStatus = consentStatus
	res.Status = rpc.StatusOk()
	return res, nil
}

// nolint: funlen
func (s *Service) SubmitAuthenticationAnswers(ctx context.Context, req *creditReportPb.SubmitAuthenticationAnswersRequest) (*creditReportPb.SubmitAuthenticationAnswersResponse, error) {
	crd, err := s.creditReportDownloadDao.GetByRequestId(ctx, req.GetClientRequestId())
	if err != nil {
		logger.Error(ctx, "error while fetching credit report download entry", zap.Error(err))
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &creditReportPb.SubmitAuthenticationAnswersResponse{
				Status: rpc.StatusRecordNotFoundWithDebugMsg("credit report download entry not found"),
			}, nil
		}
		return &creditReportPb.SubmitAuthenticationAnswersResponse{
			Status: rpc.StatusTransientFailureWithDebug(fmt.Sprintf("error while fetching credit report download entry : %s", err)),
		}, nil
	}

	authHelper := crd.GetDetails().GetDownloadDetails().GetCibilDownloadDetails()
	question := getAuthQuestionFromChallengeId(req.GetChallengeId(), authHelper.GetQuestionsResponse())
	if question == nil {
		logger.Error(ctx, "could not map challenge id to any auth question", zap.String(logger.ID, req.GetChallengeId()))
		return s.handleAuthTerminalStateForCibil(ctx, crd, nil, vgCreditReport.AtlasResponseStatus_ATLAS_RESPONSE_STATUS_FAILURE,
			getCibilTerminalError(nil, err, fmt.Sprintf("could not map challenge id to any auth question: %s", req.GetChallengeId())))
	}
	answerSpecificInput, mappingErr := getAuthSpecificInput(req)
	if mappingErr != nil {
		logger.Error(ctx, "could not map answer input to vg answer input", zap.Error(mappingErr))
		return s.handleAuthTerminalStateForCibil(ctx, crd, nil, vgCreditReport.AtlasResponseStatus_ATLAS_RESPONSE_STATUS_FAILURE,
			getCibilTerminalError(nil, err, fmt.Sprintf("could not map answer input to vg answer input : %s", mappingErr)))
	}
	vgResp, vgErr := s.vgCreditReportClient.AtlasVerifyAuthAnswers(ctx, &vgCreditReport.AtlasVerifyAuthAnswersRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_CIBIL,
		},
		ClientKey:                authHelper.GetCustomerId(),
		RequestKey:               "VAS" + uuid.NewString(),
		ChallengeConfigurationId: question.GetChallengeConfigurationId(),
		QuestionKey:              question.GetAuthQuestion().GetKey(),
		AnswerKey:                question.GetAuthQuestion().GetAnswerChoice().GetChoiceId(),
		AnswerSpecificInput:      answerSpecificInput.GetAnswerSpecificInput(),
	})
	err = epifigrpc.RPCError(vgResp, vgErr)
	atlasErr := vgResp.GetAtlasErrorResponse()
	switch {
	case atlasErr != nil && !pkgCreditReport.IsAtlasErrorPermanent(atlasErr):
		logger.Error(ctx, "error in verify auth answers", zap.Error(err))
		pkgCreditReport.LogAtlasError(ctx, atlasErr)
		return &creditReportPb.SubmitAuthenticationAnswersResponse{
			Status: rpc.StatusTransientFailureWithDebug(err.Error()),
		}, nil
	case err != nil:
		logger.Error(ctx, "error in verify auth answers", zap.Error(err))
		pkgCreditReport.LogAtlasError(ctx, atlasErr)
		return s.handleAuthTerminalStateForCibil(ctx, crd, vgResp, vgCreditReport.AtlasResponseStatus_ATLAS_RESPONSE_STATUS_FAILURE,
			getCibilTerminalError(atlasErr, err, "failed to verify auth answers"))
	}

	switch vgResp.GetVendorResponseStatus() {
	case vgCreditReport.AtlasResponseStatus_ATLAS_RESPONSE_STATUS_IN_PROGRESS, vgCreditReport.AtlasResponseStatus_ATLAS_RESPONSE_STATUS_PENDING:
		// update auth status as in progress in table
		crd.GetDetails().GetDownloadDetails().GetCibilDownloadDetails().AuthStatus = vgResp.GetVendorResponseStatus()
		crd.GetDetails().GetDownloadDetails().GetCibilDownloadDetails().AnswersResponses = append(crd.GetDetails().GetDownloadDetails().GetCibilDownloadDetails().AnswersResponses, vgResp)
		if err = s.creditReportDownloadDao.Update(ctx, crd, []creditReportPb.CreditReportDownloadFieldMask{
			creditReportPb.CreditReportDownloadFieldMask_CREDIT_REPORT_DOWNLOAD_FIELD_MASK_DETAILS,
		}); err != nil {
			logger.Error(ctx, "error while updating credit report download entry", zap.Error(err))
			return &creditReportPb.SubmitAuthenticationAnswersResponse{
				Status: rpc.StatusTransientFailureWithDebug(err.Error()),
			}, nil
		}
		return s.getAuthQuestionsAndNextAction(ctx, authHelper, crd)
	case vgCreditReport.AtlasResponseStatus_ATLAS_RESPONSE_STATUS_FAILURE, vgCreditReport.AtlasResponseStatus_ATLAS_RESPONSE_STATUS_SUCCESS:
		return s.handleAuthTerminalStateForCibil(ctx, crd, vgResp, vgResp.GetVendorResponseStatus(), nil)
	default:
		return &creditReportPb.SubmitAuthenticationAnswersResponse{
			Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("invalid atlas response status: %s", vgResp.GetVendorResponseStatus())),
		}, nil
	}
}

// getAuthQuestionsAndNextAction calls get authentication questions
// based on response, updates question and deeplink in downloads table
func (s *Service) getAuthQuestionsAndNextAction(ctx context.Context, authHelper *creditReportPb.CibilDownloadDetails, crd *creditReportPb.CreditReportDownload) (*creditReportPb.SubmitAuthenticationAnswersResponse, error) {
	getResp, getErr := s.vgCreditReportClient.AtlasGetAuthQuestions(ctx, &vgCreditReport.AtlasGetAuthQuestionsRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_CIBIL,
		},
		ClientKey:  authHelper.GetCustomerId(),
		RequestKey: "GAQ" + uuid.NewString(),
	})
	err := epifigrpc.RPCError(getResp, getErr)
	atlasErr := getResp.GetAtlasErrorResponse()
	switch {
	case atlasErr != nil && !pkgCreditReport.IsAtlasErrorPermanent(atlasErr):
		pkgCreditReport.LogAtlasError(ctx, atlasErr)
		logger.Error(ctx, "could not get auth questions", zap.String(logger.CREDIT_REPORT_DOWNLOAD_ID, crd.GetId()), zap.Error(err))
		return &creditReportPb.SubmitAuthenticationAnswersResponse{
			Status: rpc.StatusTransientFailureWithDebug(err.Error()),
		}, nil
	case err != nil:
		logger.Error(ctx, "could not get auth questions", zap.String(logger.CREDIT_REPORT_DOWNLOAD_ID, crd.GetId()), zap.Error(err))
		return s.handleAuthTerminalStateForCibil(ctx, crd, nil, vgCreditReport.AtlasResponseStatus_ATLAS_RESPONSE_STATUS_FAILURE,
			getCibilTerminalError(atlasErr, err, "failed to get auth questions"))
	// all question types except IDM_KBA (questionnaire) have been handled
	case getResp.GetQuestionType() == vgCreditReport.AtlasAuthQueue_ATLAS_AUTH_QUEUE_IDM_KBA:
		logger.Error(ctx, "atlas question type not handled", zap.String(logger.AUTH_TYPE, getResp.GetQuestionType().String()))
		return s.handleAuthTerminalStateForCibil(ctx, crd, nil, vgCreditReport.AtlasResponseStatus_ATLAS_RESPONSE_STATUS_FAILURE,
			getCibilTerminalError(atlasErr, err, fmt.Sprintf("%s question type is not handled", vgCreditReport.AtlasAuthQueue_ATLAS_AUTH_QUEUE_OTP_IDM_EMAIL)))
	}

	crd.GetDetails().GetDownloadDetails().GetCibilDownloadDetails().QuestionsResponse = append(crd.GetDetails().GetDownloadDetails().GetCibilDownloadDetails().GetQuestionsResponse(), getResp)
	crd.GetDetails().GetDownloadDetails().GetCibilDownloadDetails().AuthStatus = vgCreditReport.AtlasResponseStatus_ATLAS_RESPONSE_STATUS_UNSPECIFIED
	dl := crDeeplink.GetDeeplinkForAtlasGetQuestionsResponse(authHelper, getResp, crd.GetRequestId())
	crd.NextAction = dl
	if err = s.creditReportDownloadDao.Update(ctx, crd, []creditReportPb.CreditReportDownloadFieldMask{
		creditReportPb.CreditReportDownloadFieldMask_CREDIT_REPORT_DOWNLOAD_FIELD_NEXT_ACTION,
		creditReportPb.CreditReportDownloadFieldMask_CREDIT_REPORT_DOWNLOAD_FIELD_MASK_DETAILS,
	}); err != nil {
		logger.Error(ctx, "could not update download status", zap.String(logger.CREDIT_REPORT_DOWNLOAD_ID, crd.GetId()), zap.Error(err))
		return &creditReportPb.SubmitAuthenticationAnswersResponse{
			Status: rpc.StatusTransientFailureWithDebug(err.Error()),
		}, nil
	}

	return &creditReportPb.SubmitAuthenticationAnswersResponse{
		Status:     rpc.StatusOk(),
		NextAction: dl,
	}, nil
}

// handleAuthTerminalStateForCibil updates auth status as in failure in download table, sets the next action
// send authentication complete signal to the workflow
func (s *Service) handleAuthTerminalStateForCibil(ctx context.Context, crd *creditReportPb.CreditReportDownload, ansResp *vgCreditReport.AtlasVerifyAuthAnswersResponse, status vgCreditReport.AtlasResponseStatus, terminalError *creditReportPb.CibilTerminalError) (*creditReportPb.SubmitAuthenticationAnswersResponse, error) {
	crd.GetDetails().GetDownloadDetails().GetCibilDownloadDetails().AuthStatus = status
	if ansResp != nil {
		crd.GetDetails().GetDownloadDetails().GetCibilDownloadDetails().AnswersResponses = append(crd.GetDetails().GetDownloadDetails().GetCibilDownloadDetails().AnswersResponses, ansResp)
	}
	crd.NextAction = crDeeplink.CreditReportPollStatusDeeplink(crd.GetRequestId())
	if terminalError != nil {
		crd.GetDetails().GetDownloadDetails().GetCibilDownloadDetails().TerminalErrorReason = terminalError
	}
	if err := s.creditReportDownloadDao.Update(ctx, crd, []creditReportPb.CreditReportDownloadFieldMask{
		creditReportPb.CreditReportDownloadFieldMask_CREDIT_REPORT_DOWNLOAD_FIELD_NEXT_ACTION,
		creditReportPb.CreditReportDownloadFieldMask_CREDIT_REPORT_DOWNLOAD_FIELD_MASK_DETAILS,
	}); err != nil {
		logger.Error(ctx, "could not update download status", zap.String(logger.CREDIT_REPORT_DOWNLOAD_ID, crd.GetId()), zap.Error(err))
		return &creditReportPb.SubmitAuthenticationAnswersResponse{
			Status: rpc.StatusTransientFailureWithDebug(err.Error()),
		}, nil
	}

	payloadInBytes, payloadErr := protojson.Marshal(&payload.AuthFlowCompletionSignalPayload{})
	if payloadErr != nil {
		logger.Error(ctx, "unable to marshal the AuthFlowCompletionSignalPayload payload", zap.Error(payloadErr))
		return &creditReportPb.SubmitAuthenticationAnswersResponse{
			Status: rpc.StatusInternalWithDebugMsg(payloadErr.Error()),
		}, nil
	}
	signalRes, err2 := s.celestialClient.SignalWorkflow(ctx, &celestialPb.SignalWorkflowRequest{
		Identifier: &celestialPb.SignalWorkflowRequest_WorkflowReqId{
			WorkflowReqId: crd.GetOrchId(),
		},
		SignalId:  string(creditreportNs.AuthFlowCompletionSignal),
		Ownership: commontypes.Ownership_EPIFI_TECH,
		Payload:   payloadInBytes,
	})
	if rpcErr := epifigrpc.RPCError(signalRes, err2); rpcErr != nil {
		logger.Error(ctx, "error while auth completion signal", zap.Error(rpcErr))
	}

	return &creditReportPb.SubmitAuthenticationAnswersResponse{
		Status:     rpc.StatusOk(),
		NextAction: crDeeplink.CreditReportPollStatusDeeplink(crd.GetRequestId()),
	}, nil
}

func getCibilTerminalError(response *vgCreditReport.AtlasErrorResponse, err error, message string) *creditReportPb.CibilTerminalError {
	if response == nil && err != nil {
		message = message + ":" + err.Error()
	}
	return &creditReportPb.CibilTerminalError{
		AtlasErrorResponse: response,
		Message:            message,
	}
}

func getAuthQuestionFromChallengeId(challengeId string, questions []*vgCreditReport.AtlasGetAuthQuestionsResponse) *vgCreditReport.AtlasGetAuthQuestionsResponse {
	ques, _ := lo.Find(questions, func(item *vgCreditReport.AtlasGetAuthQuestionsResponse) bool {
		return item.GetChallengeConfigurationId() == challengeId
	})
	return ques
}

func getAuthSpecificInput(input *creditReportPb.SubmitAuthenticationAnswersRequest) (*vgCreditReport.AtlasVerifyAuthAnswersRequest, error) {
	req := &vgCreditReport.AtlasVerifyAuthAnswersRequest{}
	switch input.GetAnswerSpecificInput().(type) {
	case *creditReportPb.SubmitAuthenticationAnswersRequest_ResendOtp:
		req.AnswerSpecificInput = &vgCreditReport.AtlasVerifyAuthAnswersRequest_ResendOtp{
			ResendOtp: input.GetResendOtp(),
		}
	case *creditReportPb.SubmitAuthenticationAnswersRequest_SkipQuestion:
		req.AnswerSpecificInput = &vgCreditReport.AtlasVerifyAuthAnswersRequest_SkipQuestion{
			SkipQuestion: input.GetSkipQuestion(),
		}

	case *creditReportPb.SubmitAuthenticationAnswersRequest_UserInput:
		req.AnswerSpecificInput = &vgCreditReport.AtlasVerifyAuthAnswersRequest_UserInput{
			UserInput: input.GetUserInput(),
		}
	default:
		return nil, errors.Wrap(epifierrors.ErrInvalidArgument, "invalid answer input type")
	}
	return req, nil
}

// GetCibilReportWebUrl returns credit report url if it already exists and has not expired, else refreshes it from vendor.
func (s *Service) GetCibilReportWebUrl(ctx context.Context, req *creditReportPb.GetCibilReportWebUrlRequest) (*creditReportPb.GetCibilReportWebUrlResponse, error) {
	sub, err := s.creditReportUserSubscriptionDao.GetByActorIdAndVendor(ctx, req.GetActorId(), commonvgpb.Vendor_CIBIL)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &creditReportPb.GetCibilReportWebUrlResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "error while getting subscription details for actor", zap.Error(err))
		return &creditReportPb.GetCibilReportWebUrlResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	webInfo := sub.GetSubscriptionInfo().GetCibilSubscriptionInfo().GetWebUrlInfo()
	if webInfo.GetUrlRefreshTill().AsTime().Before(time.Now()) {
		return &creditReportPb.GetCibilReportWebUrlResponse{
			Status: rpc.StatusFailedPreconditionWithDebugMsg("report expired for user"),
		}, nil

	}

	vgResp, vgErr := s.vgCreditReportClient.AtlasGetProductWebUrl(ctx, &vgCreditReport.AtlasGetProductWebUrlRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_CIBIL,
		},
		ClientKey:  sub.GetSubscriptionInfo().GetCibilSubscriptionInfo().GetCustomerId(),
		RequestKey: "GPU" + uuid.NewString(),
	})
	if err = epifigrpc.RPCError(vgResp, vgErr); err != nil {
		pkgCreditReport.LogAtlasError(ctx, vgResp.GetAtlasErrorResponse())
		logger.Error(ctx, "error while getting product url", zap.Error(err))
		return &creditReportPb.GetCibilReportWebUrlResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while getting product url"),
		}, nil
	}

	sub.GetSubscriptionInfo().GetCibilSubscriptionInfo().WebUrlInfo = &creditReportPb.CibilWebUrlInfo{
		ProductWebUrl:      vgResp.GetWebUrl(),
		ProdutUrlFetchedAt: timestampPb.Now(),
		UrlRefreshTill:     sub.GetSubscriptionInfo().GetCibilSubscriptionInfo().GetWebUrlInfo().GetUrlRefreshTill(),
	}
	if err = s.creditReportUserSubscriptionDao.Update(ctx, sub, []creditReportPb.CreditReportUserSubscriptionDetailsFieldMask{
		creditReportPb.CreditReportUserSubscriptionDetailsFieldMask_CREDIT_REPORT_USER_SUBSCRIPTION_DETAIL_FIELD_SUBSCRIPTION_INFO,
	}); err != nil {
		logger.Error(ctx, "error while updating credit report subscription details", zap.Error(err))
		return &creditReportPb.GetCibilReportWebUrlResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while updating credit report subscription details"),
		}, nil
	}

	return &creditReportPb.GetCibilReportWebUrlResponse{
		Status: rpc.StatusOk(),
		Url:    sub.GetSubscriptionInfo().GetCibilSubscriptionInfo().GetWebUrlInfo().GetProductWebUrl(),
	}, nil
}

// GetCibilReportFeatures retrieves CIBIL report features for a given actor.
// It checks the report availability status and if available fetches features from the feature store.
// The function returns both the feature values and the report availability status.

// Edge cases:
// 1. If one download happened for No_HIT case, and then another got failed, then it will return failure case.
// solve - Create raw report entry in NO_HIT case as well.
func (s *Service) GetCibilReportFeatures(ctx context.Context, req *creditReportPb.GetCibilReportFeaturesRequest) (*creditReportPb.GetCibilReportFeaturesResponse, error) {
	crdDetails, err := s.getCibilCreditReportsRecentFirst(ctx, req.GetActorId(), defaultCreditReportFetchLimit)
	if err != nil {
		// If RPC is getting called when the data is not present then we should return record not found
		// with data availability status unspecified
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &creditReportPb.GetCibilReportFeaturesResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "error fetching credit reports", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &creditReportPb.GetCibilReportFeaturesResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while fetching raw report for the actor"),
		}, nil
	}
	crdDetail := crdDetails[0]

	// If report not found or entry not found, return early
	if crdDetail.GetCreditReportDataAvailabilityStatus() == creditReportPb.DataAvailabilityStatus_DATA_AVAILABILITY_STATUS_NOT_FOUND ||
		crdDetail.GetCreditReportDataAvailabilityStatus() == creditReportPb.DataAvailabilityStatus_DATA_AVAILABILITY_STATUS_UNSPECIFIED {
		return &creditReportPb.GetCibilReportFeaturesResponse{
			Status:                             rpc.StatusOk(),
			CreditReportDataAvailabilityStatus: crdDetail.GetCreditReportDataAvailabilityStatus(),
		}, nil

	}

	// Extract features from feature store
	featureValues, err := s.extractFeaturesFromStore(ctx, req.GetActorId(), req.GetFeaturesNames(), crdDetail.GetCreditReportRaw().GetRawReport())
	if err != nil {
		logger.Error(ctx, "error while fetching features from feature store", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &creditReportPb.GetCibilReportFeaturesResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while getting feature store client"),
		}, nil
	}

	return &creditReportPb.GetCibilReportFeaturesResponse{
		Status:                             rpc.StatusOk(),
		FeatureValueMap:                    featureValues,
		CreditReportDataAvailabilityStatus: crdDetail.GetCreditReportDataAvailabilityStatus(),
	}, nil
}

// extractFeaturesFromStore extracts features from the feature store for a given raw report
func (s *Service) extractFeaturesFromStore(ctx context.Context, actorId string, featureNames []string, rawReport []byte) (map[string]*structPb.Value, error) {
	featureStoreClient, err := s.featureStoreFactory.GetFeatureStoreClient(commonvgpb.Vendor_FENNEL_FEATURE_STORE)
	if err != nil {
		return nil, fmt.Errorf("error getting feature store client: %w", err)
	}

	userResp, err := s.userClient.GetUser(ctx, &user.GetUserRequest{
		Identifier: &user.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if rpcErr := epifigrpc.RPCError(userResp, err); rpcErr != nil {
		return nil, fmt.Errorf("error while getting user from userClient: %w", rpcErr)
	}
	if userResp.GetUser().GetProfile().GetPhoneNumber() == nil {
		return nil, fmt.Errorf("phone number not present in user profile: %w", err)
	}

	featureDataRes, err := featureStoreClient.ExtractFeature(ctx, &fsModel.ExtractFeatureRequest{
		FeatureNameList: featureNames,
		IdentifierList: []fsModel.Identifier{
			{
				ActorId:             actorId,
				PhoneNumber:         strconv.FormatUint(userResp.GetUser().GetProfile().GetPhoneNumber().GetNationalNumber(), 10),
				CreditReportDataRaw: string(rawReport),
			},
		},
		IdentifierTypeList: []fsModel.IdentifierType{fsModel.ACTOR_ID, fsModel.CREDIT_REPORT_DATA_RAW, fsModel.PHONE_NUMBER},
	})
	if err != nil {
		return nil, fmt.Errorf("error extracting features from feature store: %w", err)
	}

	valueMap := make(map[string]*structPb.Value)
	for _, data := range featureDataRes.FeaturesSetList[0].FeatureList {
		valueMap[data.Name] = data.Value
	}

	return valueMap, nil
}

// nolint:govet
func (s *Service) GetExperianAnalyticsDetails(ctx context.Context, req *creditReportPb.GetExperianAnalyticsDetailsRequest) (*creditReportPb.GetExperianAnalyticsDetailsResponse, error) {
	// in analytics db we can not use actor id directly we need to fetch firehose_id corresponding to that
	// actor id and then via that firehose id we can make queries
	getDPmappingResp, err := s.vendorMappingClient.GetDPMappingById(ctx, &vendormapping.GetDPMappingByIdRequest{
		Identifier: &vendormapping.GetDPMappingByIdRequest_ActorId{
			ActorId: req.GetActorId(),
		},
	})
	if err != nil {
		logger.Error(ctx, "failed to fetch firehose id", zap.String(logger.ACTOR_ID, req.GetActorId()))
		return &creditReportPb.GetExperianAnalyticsDetailsResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to fetch firehose id"),
		}, nil
	}
	g, gctx := errgroup.WithContext(ctx)
	var resp creditReportPb.GetExperianAnalyticsDetailsResponse
	for _, fieldMask := range req.GetFieldMaskList() {
		switch fieldMask {
		case creditReportPb.ExperianAnalyticsDetailsFieldMask_EXPERIAN_ANALYTICS_DETAILS_FIELD_MASK_RPT_EMAIL:
			g.Go(func() error {
				emails, err := s.experianAnalyticsDao.GetReportEmails(gctx, getDPmappingResp.GetFirehoseId())
				if err != nil {
					// Not returning error here so that even if some entries failed to fetch we can still fetch
					// the rest, same for rpt_address and rpt_phone
					logger.Error(gctx, "error while fetching experian analytics emails", zap.Error(err))
				}
				resp.ReportEmails = emails
				return nil
			})
		case creditReportPb.ExperianAnalyticsDetailsFieldMask_EXPERIAN_ANALYTICS_DETAILS_FIELD_MASK_RPT_PHONE:
			g.Go(func() error {
				phones, err := s.experianAnalyticsDao.GetReportPhones(gctx, getDPmappingResp.GetFirehoseId())
				if err != nil {
					logger.Error(gctx, "error while fetching experian analytics phone", zap.Error(err))
				}
				resp.ReportPhones = phones
				return nil
			})
		case creditReportPb.ExperianAnalyticsDetailsFieldMask_EXPERIAN_ANALYTICS_DETAILS_FIELD_MASK_RPT_ADDRESS:
			g.Go(func() error {
				addresses, err := s.experianAnalyticsDao.GetReportAddresses(gctx, getDPmappingResp.GetFirehoseId())
				if err != nil {
					logger.Error(gctx, "error while fetching experian analytics address", zap.Error(err))
				}
				resp.ReportAddresses = addresses
				return nil
			})
		}
	}
	if err = g.Wait(); err != nil {
		logger.Error(ctx, "failed to fecth details from experian analytics", zap.Error(err))
		resp.Status = rpc.StatusInternalWithDebugMsg("failed to fecth details from experian analytics")
		return &resp, nil
	}
	resp.Status = rpc.StatusOk()
	return &resp, nil
}

func (s *Service) GetDownloadProcessDetails(ctx context.Context, req *creditReportPb.GetDownloadProcessDetailsRequest) (res *creditReportPb.GetDownloadProcessDetailsResponse, err error) {
	res = &creditReportPb.GetDownloadProcessDetailsResponse{}

	crd, err := s.creditReportDownloadDao.GetByRequestId(ctx, req.GetRequestId())
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error while fetching credit report download entry from request id", zap.Error(err))
		res.Status = rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error while fetching credit report download entry form request id : %s", err.Error()))
		return res, nil
	}

	if errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "no credit report donwload process found for the given request id", zap.Any(logger.REQUEST_ID, req.GetRequestId()), zap.Error(err))
		res.Status = rpc.StatusRecordNotFoundWithDebugMsg(fmt.Sprintf("no credit report donwload process found for the given request id :%s, err: %s", req.GetRequestId(), err.Error()))
		return res, nil
	}

	return &creditReportPb.GetDownloadProcessDetailsResponse{
		Status:           rpc.StatusOk(),
		RequestId:        crd.GetRequestId(),
		ProcessStatus:    crd.GetProcessStatus(),
		ProcessSubStatus: crd.GetProcessSubStatus(),
		NextAction:       crd.GetNextAction(),
		CreatedAt:        crd.GetCreatedAt(),
	}, nil
}

func (s *Service) getCibilCreditReportsRecentFirst(ctx context.Context, actorId string, limit int) ([]*creditReportPb.CreditReportDownloadDetails, error) {
	// Get latest raw report ordered by created_at Desc
	// Data in raw report is only present when the report is downloaded
	rawReports, err := s.creditReportsRawDao.GetByActorIdVendor(ctx, actorId, commonvgpb.Vendor_CIBIL, limit)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return nil, errors.Wrap(err, "error while fetching raw reports from db")
	}

	// If raw report not found, then check and return the last download status
	if errors.Is(err, epifierrors.ErrRecordNotFound) {
		// Get latest report status
		reportStatus, err := s.getCreditReportDataAvailabilityStatus(ctx, actorId, "", commonvgpb.Vendor_CIBIL)
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				return nil, errors.Wrap(err, "cibil download data not found")
			}
			return nil, errors.Wrap(err, "error fetching credit report status")
		}

		return []*creditReportPb.CreditReportDownloadDetails{
			{
				Vendor:                             commonvgpb.Vendor_CIBIL,
				CreditReportDataAvailabilityStatus: reportStatus,
			},
		}, nil
	}
	// not applying any check because it's there at dao level and data is in descending order
	rawReport := rawReports[0]

	// Get latest report status
	reportStatus, err := s.getCreditReportDataAvailabilityStatus(ctx, actorId, rawReport.GetId(), commonvgpb.Vendor_CIBIL)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			// returning internal error
			return nil, errors.Wrap(epifierrors.ErrFailedPrecondition, "data much have been present with corresponding data")
		}
		return nil, errors.Wrap(err, "error fetching credit report status")
	}

	return []*creditReportPb.CreditReportDownloadDetails{
		{
			CreditReportRaw:                    rawReport,
			CreatedAt:                          rawReport.GetCreatedAt(),
			Vendor:                             rawReport.GetVendor(),
			CreditReportDataAvailabilityStatus: reportStatus,
		},
	}, nil
}

// getCreditReportDataAvailabilityStatus determines the availability status of a CIBIL report for an actor
func (s *Service) getCreditReportDataAvailabilityStatus(ctx context.Context, actorId string, crdId string, vendor commonvgpb.Vendor) (creditReportPb.DataAvailabilityStatus, error) {
	var crd *creditReportPb.CreditReportDownload
	var err error
	if crdId != "" {
		crd, err = s.creditReportDownloadDao.GetById(ctx, crdId)
	} else {
		crd, err = s.creditReportDownloadDao.GetLatestByActorIdAndVendor(ctx, actorId, vendor)
	}
	if err != nil {
		// even if its record not found, same error can be passed
		return creditReportPb.DataAvailabilityStatus_DATA_AVAILABILITY_STATUS_UNSPECIFIED, errors.Wrap(err, "error fetching credit report download details")
	}

	switch crd.GetProcessStatus() {
	case creditReportPb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_COMPLETED:
		switch crd.GetProcessSubStatus() {
		case creditReportPb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_REPORT_NOT_FOUND:
			return creditReportPb.DataAvailabilityStatus_DATA_AVAILABILITY_STATUS_NOT_FOUND, nil
		case creditReportPb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_NO_HISTORY:
			return creditReportPb.DataAvailabilityStatus_DATA_AVAILABILITY_STATUS_FOUND_WITHOUT_HISTORY, nil
		default:
			return creditReportPb.DataAvailabilityStatus_DATA_AVAILABILITY_STATUS_FOUND, nil
		}
	// Failed case is getting added to add support for the older cases before the introduction of NTC users here.
	case creditReportPb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_FAILED:
		switch crd.GetProcessSubStatus() {
		case creditReportPb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_REPORT_NOT_FOUND:
			return creditReportPb.DataAvailabilityStatus_DATA_AVAILABILITY_STATUS_NOT_FOUND, nil
		}
	}

	return creditReportPb.DataAvailabilityStatus_DATA_AVAILABILITY_STATUS_UNSPECIFIED, nil
}
