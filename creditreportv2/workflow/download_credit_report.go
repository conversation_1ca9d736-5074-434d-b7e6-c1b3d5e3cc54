// nolint: dupl, funlen
package workflow

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"fmt"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	creditreportNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/creditreport"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/consent"
	pb "github.com/epifi/gamma/api/creditreportv2"
	crActivityPb "github.com/epifi/gamma/api/creditreportv2/activity"
	crWorkflowPb "github.com/epifi/gamma/api/creditreportv2/workflow"
	"github.com/epifi/gamma/api/frontend/account/signup"
	"github.com/epifi/gamma/api/frontend/deeplink"
	crDeeplink "github.com/epifi/gamma/creditreportv2/deeplink"
	onboardingPkg "github.com/epifi/gamma/pkg/onboarding"
)

const (
	ownership                                = commontypes.Ownership_EPIFI_TECH
	minIosVersionForConsentScreenChanges     = 500
	minAndroidVersionForConsentScreenChanges = 345
)

// DownloadCreditReport contains workflow to download credit report of a user.
// The method is called by temporal, for processing the payment workflow logic and it also
// takes care of the retrying logic here.
func DownloadCreditReport(ctx workflow.Context, req *workflowPb.Request) error {
	wfReqID := workflow.GetInfo(ctx).WorkflowExecution.ID
	lg := workflow.GetLogger(ctx)

	wfProcessingParams := &activityPb.GetWorkflowProcessingParamsV2Response{}
	if err := activityPkg.Execute(ctx, epifitemporal.GetWorkflowProcessingParamsV2, wfProcessingParams, &activityPb.GetWorkflowProcessingParamsV2Request{
		RequestHeader: &activityPb.RequestHeader{
			Ownership: ownership,
		},
		WfReqId: wfReqID,
	}); err != nil {
		lg.Error("activity failed", zap.String(logger.ACTIVITY, string(epifitemporal.GetWorkflowProcessingParamsV2)), zap.Error(err))
		return err
	}
	clientReqId := wfProcessingParams.GetWfReqParams().GetClientReqId().GetId()

	var downloadReq crWorkflowPb.DownloadCreditReportRequest
	unmarshalOptions := protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}
	if err := unmarshalOptions.Unmarshal(wfProcessingParams.GetWfReqParams().GetPayload(), &downloadReq); err != nil {
		return fmt.Errorf("failed to unmarshal payload %w", err)
	}
	var err error
	switch downloadReq.GetVendor() {
	case commonvgpb.Vendor_CIBIL:
		switch downloadReq.GetFetchType() {
		case pb.CreditReportFetchType_CREDIT_REPORT_FETCH_TYPE_NEW_CONSENT, pb.CreditReportFetchType_CREDIT_REPORT_FETCH_TYPE_EXISTING:
			if err = downloadReportForCibil(ctx, wfReqID, clientReqId, &downloadReq); err != nil {
				return err
			}
		default:
			return fmt.Errorf("unsupported report fetch type: %s", downloadReq.GetFetchType().String())
		}
	default:
		switch downloadReq.GetFetchType() {
		case pb.CreditReportFetchType_CREDIT_REPORT_FETCH_TYPE_NEW_CONSENT:
			err = downloadReportUsingNewConsent(ctx, wfReqID, clientReqId, &downloadReq)
		case pb.CreditReportFetchType_CREDIT_REPORT_FETCH_TYPE_EXISTING:
			err = downloadReportUsingExistingConsent(ctx, wfReqID, clientReqId, &downloadReq)
		case pb.CreditReportFetchType_CREDIT_REPORT_FETCH_TYPE_CONSENT_EXTENSION:
			err = downloadReportAndExtendExistingConsent(ctx, wfReqID, clientReqId, &downloadReq)
		default:
			return fmt.Errorf("unsupported report fetch type: %s", downloadReq.GetFetchType().String())
		}
		if err != nil {
			return err
		}
	}
	if err = activityPkg.Execute(ctx, epifitemporal.PublishWorkflowUpdateEventV2, &activityPb.PublishWorkflowUpdateEventV2Response{}, &activityPb.PublishWorkflowUpdateEventV2Request{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: clientReqId,
			Ownership:   ownership,
		},
		WfReqId: wfReqID,
	}); err != nil {
		return fmt.Errorf("PublishWorkflowUpdateEvent activity failed: %w", err)
	}
	return nil
}

// downloadReportUsingNewConsent uses a fresh consent from the user to fetch their credit report.
func downloadReportUsingNewConsent(ctx workflow.Context, wfReqId, clientReqId string,
	downloadReq *crWorkflowPb.DownloadCreditReportRequest) error {
	var (
		continueProcess bool
		err             error
	)
	// --------------- RECORD_CONSENT STAGE --------------------
	if !downloadReq.GetIsConsentTaken() {
		continueProcess, err = performRecordConsentStage(ctx, wfReqId, clientReqId, downloadReq)
		if err != nil {
			return err
		}
		if !continueProcess {
			return nil
		}
	}

	// ---------------- PAN DOB STAGE ---------------

	v := workflow.GetVersion(ctx, "add-pan-dob-stage-for-fi-lite-users", workflow.DefaultVersion, 2)
	if downloadReq.GetIsFiLiteUser() && !downloadReq.GetAllowReportFetchWithoutPan() && ((downloadReq.GetPan() == "" && v > 1) || v == 1) {
		continueProcess, err = performPanDobRecordStage(ctx, wfReqId, clientReqId, downloadReq)
		if err != nil {
			return err
		}
		if !continueProcess {
			return nil
		}
	}

	// --------------- OTP STAGE --------------------
	if !downloadReq.GetIsOtpAlreadyVerified() {
		continueProcess, err = performOtpVerificationStage(ctx, wfReqId, clientReqId, downloadReq)
		if err != nil {
			return err
		}
		if !continueProcess {
			return nil
		}
	}
	// --------------- VENDOR_CALL STAGE --------------------
	return performVendorCallStage(ctx, wfReqId, clientReqId, downloadReq)

}

// downloadReportUsingExistingConsent uses consent that was previously given by the user to fetch their credit report.
func downloadReportUsingExistingConsent(ctx workflow.Context, wfReqId, clientReqId string,
	downloadReq *crWorkflowPb.DownloadCreditReportRequest) error {
	// --------------- VENDOR CALL STAGE --------------------
	return performVendorCallStage(ctx, wfReqId, clientReqId, downloadReq)
}

// downloadReportAndExtendExistingConsent uses existing consent that was previously given by the user to fetch their credit report.
// It additionally tries to take user's consent to extend their original consent by 6 more months.
func downloadReportAndExtendExistingConsent(ctx workflow.Context, wfReqId, clientReqId string,
	downloadReq *crWorkflowPb.DownloadCreditReportRequest) error {
	// --------------- RECORD_CONSENT STAGE --------------------
	continueProcess, err := performRecordConsentStage(ctx, wfReqId, clientReqId, downloadReq)
	if err != nil {
		return err
	}
	if !continueProcess {
		return nil
	}
	// --------------- VENDOR CALL STAGE --------------------
	return performVendorCallStage(ctx, wfReqId, clientReqId, downloadReq)
}

// performOtpVerificationStage executes OTP stage. It calls activities required to perform the OTP verification.
func performOtpVerificationStage(ctx workflow.Context, wfReqId, clientReqId string,
	downloadReq *crWorkflowPb.DownloadCreditReportRequest) (bool, error) {
	lg := workflow.GetLogger(ctx)
	if err := activityPkg.Execute(ctx, epifitemporal.InitiateWorkflowStageV2, &activityPb.InitiateWorkflowStageV2Response{}, &activityPb.InitiateWorkflowStageV2Request{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: clientReqId,
			Ownership:   ownership,
		},
		WfReqId: wfReqId,
		Stage:   workflowPb.Stage_OTP,
		Status:  stagePb.Status_INITIATED,
	}); err != nil {
		return false, fmt.Errorf("%s activity failed: %w", epifitemporal.InitiateWorkflowStageV2, err)
	}

	otpInitRes := &crActivityPb.InitiateOtpVerificationResponse{}
	if err := activityPkg.Execute(ctx, creditreportNs.InitiateOtpVerification, otpInitRes, &crActivityPb.InitiateOtpVerificationRequest{
		RequestHeader: &activityPb.RequestHeader{
			Ownership:   ownership,
			ClientReqId: clientReqId,
		},
		CreditReportDownloadId: downloadReq.GetCreditReportDownloadId(),
	}); err != nil {
		lg.Error("InitiateOtpVerification failed", zap.Error(err))
		if updateErr := updateDownloadProcessAndWorkflowStageStatus(ctx, wfReqId, clientReqId, workflowPb.Stage_OTP, stagePb.Status_FAILED,
			downloadReq.GetCreditReportDownloadId(), pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_FAILED,
			pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_OTP_VERIFICATION_INIT_FAILED,
			false, serverErrorWithRetryDeeplink(downloadReq.GetRedirectDeeplink()), nil); updateErr != nil {
			return false, fmt.Errorf("updateDownloadProcessAndWorkflowStageStatus failed : %w", updateErr)
		}
		return false, fmt.Errorf("%s activity failed: %w", creditreportNs.InitiateOtpVerification, err)
	}
	if otpInitRes.GetClientRequestId() == "" {
		return false, fmt.Errorf("client request id not present in init otp verification activity response")
	}

	actResp := &crActivityPb.CheckAuthFlowProcessStatusResponse{}
	statusCheckFuture, err := activityPkg.ExecuteAsync(ctx, creditreportNs.CheckAuthFlowProcessStatus, &crActivityPb.CheckAuthFlowProcessStatusRequest{
		RequestHeader: &activityPb.RequestHeader{
			Ownership:   ownership,
			ClientReqId: clientReqId,
		},
		ClientRequestId: otpInitRes.GetClientRequestId(),
	}, actResp)
	if err != nil {
		lg.Error("CheckAuthFlowProcessStatus activity initiation failed", zap.Error(err))
		if updateErr := updateDownloadProcessAndWorkflowStageStatus(ctx, wfReqId, clientReqId, workflowPb.Stage_OTP, stagePb.Status_FAILED,
			downloadReq.GetCreditReportDownloadId(), pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_FAILED,
			pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_OTP_VERIFICATION_FAILED,
			false, serverErrorWithRetryDeeplink(downloadReq.GetRedirectDeeplink()), nil); updateErr != nil {
			return false, fmt.Errorf("updateDownloadProcessAndWorkflowStageStatus failed : %w", updateErr)
		}
		return false, fmt.Errorf("%s activity initiation failed: %w", creditreportNs.CheckAuthFlowProcessStatus, err)
	}

	statusCheckFuture.AddFutureHandler(func(getErr error, actResp *crActivityPb.CheckAuthFlowProcessStatusResponse) {
		lg.Info("CheckAuthFlowProcessStatus future finished")
		err = getErr
	})
	statusCheckFuture.AddToSelector(ctx, workflow.NewSelector(ctx)).
		AddReceive(workflow.GetSignalChannel(ctx, string(creditreportNs.AuthFlowCompletionSignal)), func(c workflow.ReceiveChannel, more bool) {
			lg.Info("received auth flow completion signal")
			var payload []byte
			c.Receive(ctx, &payload)
		}).
		Select(ctx)
	if err != nil {
		lg.Error("CheckAuthFlowProcessStatus activity failed", zap.Error(err))
		substatus := pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_OTP_VERIFICATION_FAILED
		if epifitemporal.IsRetryableError(err) || epifitemporal.HasSignalReceivedTimedOut(err) {
			substatus = pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_OTP_VERIFICATION_EXPIRED
		}
		if updateErr := updateDownloadProcessAndWorkflowStageStatus(ctx, wfReqId, clientReqId, workflowPb.Stage_OTP, stagePb.Status_FAILED,
			downloadReq.GetCreditReportDownloadId(), pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_FAILED, substatus,
			false, serverErrorWithRetryDeeplink(downloadReq.GetRedirectDeeplink()), nil); updateErr != nil {
			return false, fmt.Errorf("updateDownloadProcessAndWorkflowStageStatus failed : %w", updateErr)
		}
		return false, fmt.Errorf("%s activity failed: %w", creditreportNs.CheckAuthFlowProcessStatus, err)
	}

	updateStatusRes := &crActivityPb.UpdateCreditReportStatusBasedOnAuthFlowStatusResponse{}
	if err = activityPkg.Execute(ctx, creditreportNs.UpdateCreditReportStatusBasedOnAuthFlowStatus, updateStatusRes, &crActivityPb.UpdateCreditReportStatusBasedOnAuthFlowStatusRequest{
		RequestHeader: &activityPb.RequestHeader{
			Ownership:   ownership,
			ClientReqId: clientReqId,
		},
		CreditReportDownloadId: downloadReq.GetCreditReportDownloadId(),
	}); err != nil {
		lg.Error("UpdateCreditReportStatusBasedOnAuthFlowStatus activity failed", zap.Error(err))
		if updateErr := updateDownloadProcessAndWorkflowStageStatus(ctx, wfReqId, clientReqId, workflowPb.Stage_OTP, stagePb.Status_FAILED,
			downloadReq.GetCreditReportDownloadId(), pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_FAILED,
			pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_OTP_VERIFICATION_FAILED,
			false, serverErrorWithRetryDeeplink(downloadReq.GetRedirectDeeplink()), nil); updateErr != nil {
			return false, fmt.Errorf("updateDownloadProcessAndWorkflowStageStatus failed : %w", updateErr)
		}
		return false, fmt.Errorf("%s activity failed: %w", creditreportNs.UpdateCreditReportStatusBasedOnAuthFlowStatus, err)
	}
	if updateStatusRes.GetProcessStatus() == pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_FAILED {
		lg.Error("otp verification wasn't successful")
		if err := activityPkg.Execute(ctx, epifitemporal.UpdateWorkflowStage, &activityPb.UpdateWorkflowStageResponse{}, &activityPb.UpdateWorkflowStageRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: clientReqId,
				Ownership:   ownership,
			},
			WfReqId: wfReqId,
			Stage:   workflowPb.Stage_OTP,
			Status:  stagePb.Status_FAILED,
		}); err != nil {
			return false, fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateWorkflowStage, err)
		}
		if err := activityPkg.Execute(ctx, epifitemporal.PublishWorkflowUpdateEventV2, &activityPb.PublishWorkflowUpdateEventV2Response{}, &activityPb.PublishWorkflowUpdateEventV2Request{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: clientReqId,
				Ownership:   ownership,
			},
			WfReqId: wfReqId,
		}); err != nil {
			return false, fmt.Errorf("PublishWorkflowUpdateEvent activity failed: %w", err)
		}
		return false, nil
	}

	if err := activityPkg.Execute(ctx, epifitemporal.UpdateWorkflowStage, &activityPb.UpdateWorkflowStageResponse{}, &activityPb.UpdateWorkflowStageRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: clientReqId,
			Ownership:   ownership,
		},
		WfReqId: wfReqId,
		Stage:   workflowPb.Stage_OTP,
		Status:  stagePb.Status_SUCCESSFUL,
	}); err != nil {
		return false, fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateWorkflowStage, err)
	}

	return true, nil
}

// nolint:funlen
func performPanDobRecordStage(ctx workflow.Context, wfReqId, clientReqId string, downloadReq *crWorkflowPb.DownloadCreditReportRequest) (bool, error) {
	lg := workflow.GetLogger(ctx)
	actorId := downloadReq.GetActorId()
	stage := creditreportNs.RecordPanDobStage
	// change workflow stage to RECORD_PAN_DOB initiated
	err := celestialPkg.InitiateWorkflowStage(ctx, stage, stagePb.Status_INITIATED)
	if err != nil {
		// NOTE: returning error here as this is an unexpected platform failure.  We expect an alert here
		return false, fmt.Errorf("failed to initiated workflow stage: %s: %w", stage, err)
	}

	// check if pan dob is present for actor or not
	getPanDobStatusRes := &crActivityPb.GetUserPanDobStatusResponse{}
	if getPanDobStatusErr := activityPkg.Execute(ctx, creditreportNs.GetUserPanDobStatus, getPanDobStatusRes, &crActivityPb.GetUserPanDobStatusRequest{
		RequestHeader: &activityPb.RequestHeader{
			Ownership:   ownership,
			ClientReqId: clientReqId,
		},
		ActorId: actorId,
	}); getPanDobStatusErr != nil {
		lg.Error("CheckUserPanDobPresence failed", zap.Error(getPanDobStatusErr))
		if updateErr := updateDownloadProcessAndWorkflowStageStatus(ctx, wfReqId, clientReqId, workflowPb.Stage_STAGE_UNSPECIFIED, stagePb.Status_FAILED,
			downloadReq.GetCreditReportDownloadId(), pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_FAILED,
			pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_PAN_DOB_CHECK_FAILED,
			false, serverErrorWithRetryDeeplink(downloadReq.GetRedirectDeeplink()), celestialPkg.GetStageEnumFromStage(stage)); updateErr != nil {
			return false, fmt.Errorf("updateDownloadProcessAndWorkflowStageStatus failed : %w", updateErr)
		}
		return false, fmt.Errorf("%s activity failed: %w", creditreportNs.GetUserPanDobStatus, err)
	}

	// if pan dob is present, mark the wf stage as successful and return
	if getPanDobStatusRes.GetIsPanDobPresent() {
		lg.Info("pan dob already present for actor")
		err = celestialPkg.UpdateWorkflowStage(ctx, stage, stagePb.Status_SUCCESSFUL)
		if err != nil {
			// NOTE: returning error here as this is an unexpected platform failure. We expect an alert here
			return false, fmt.Errorf("failed to update workflow stage to success: %s: %w", stage, err)
		}
		return true, nil
	}

	// if pan dob is not present, update the credit report download status as PAN_DOB_INITIATED
	// Change next action to pan-dob deeplink which will record user's pan-dob and redirects to CS polling screen later
	// GetNextAction rpc has logic to check if pan dob is present and change the download_report status accordingly

	var panDobScreenDeeplink *deeplink.Deeplink

	v := workflow.GetVersion(ctx, "nsdl-pan-dop-deeplink-stage", workflow.DefaultVersion, 1)
	switch v {
	case 1:
		nsdlPanDobDeeplinkRes := &crActivityPb.GetNSDLPanDobDeeplinkResponse{}
		if getNsdlPanDobDeeplinkErr := activityPkg.Execute(ctx, creditreportNs.GetNSDLPanDobDeeplink, nsdlPanDobDeeplinkRes, &crActivityPb.GetNSDLPanDobDeeplinkRequest{
			RequestHeader: &activityPb.RequestHeader{
				Ownership:   ownership,
				ClientReqId: clientReqId,
			},
			ActorId: actorId,
		}); getNsdlPanDobDeeplinkErr != nil {
			lg.Error("GetNSDLPanDobDeeplink activity failed", zap.Error(getNsdlPanDobDeeplinkErr))
			if updateErr := updateDownloadProcessAndWorkflowStageStatus(ctx, wfReqId, clientReqId, workflowPb.Stage_STAGE_UNSPECIFIED, stagePb.Status_FAILED,
				downloadReq.GetCreditReportDownloadId(), pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_FAILED,
				pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_PAN_DOB_CHECK_FAILED,
				false, serverErrorWithRetryDeeplink(downloadReq.GetRedirectDeeplink()), celestialPkg.GetStageEnumFromStage(stage)); updateErr != nil {
				return false, fmt.Errorf("updateDownloadProcessAndWorkflowStageStatus failed : %w", updateErr)
			}
			return false, fmt.Errorf("%s activity failed: %w", creditreportNs.GetNSDLPanDobDeeplink, getNsdlPanDobDeeplinkErr)
		}
		panDobScreenDeeplink = nsdlPanDobDeeplinkRes.GetNsdlPanDobDeeplink()
	default:
		panDobScreenDeeplink, err = PanDobScreenDeeplink()
		if err != nil {
			return false, fmt.Errorf("failed to complete pan dob stage for credit score flow : %w", err)
		}
	}

	updateStatusRes := &crActivityPb.UpdateCreditReportDownloadProcessStatusResponse{}
	if updateStatusErr := activityPkg.Execute(ctx, creditreportNs.UpdateCreditReportDownloadProcessStatus, updateStatusRes, &crActivityPb.UpdateCreditReportDownloadProcessStatusRequest{
		RequestHeader: &activityPb.RequestHeader{
			Ownership:   ownership,
			ClientReqId: clientReqId,
		},
		CreditReportDownloadId: downloadReq.GetCreditReportDownloadId(),
		Status:                 pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_RECORD_PAN_DOB_INITIATED,
		NextAction:             panDobScreenDeeplink,
	}); updateStatusErr != nil {
		lg.Error("UpdateCreditReportDownloadProcessStatus failed to init pan-dob", zap.Error(updateStatusErr))
		if updateErr := updateDownloadProcessAndWorkflowStageStatus(ctx, wfReqId, clientReqId, workflowPb.Stage_RECORD_CONSENT, stagePb.Status_FAILED,
			downloadReq.GetCreditReportDownloadId(), pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_FAILED,
			pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_RECORD_CONSENT_INIT_FAILED,
			false, serverErrorWithRetryDeeplink(downloadReq.GetRedirectDeeplink()), nil); updateErr != nil {
			return false, fmt.Errorf("updateDownloadProcessAndWorkflowStageStatus failed : %w", updateErr)
		}
		return false, fmt.Errorf("%s activity failed: %w", creditreportNs.UpdateCreditReportDownloadProcessStatus, err)
	}

	panDobPresentRes := &crActivityPb.CheckPanDobPresentResponse{}
	panDobStatusCheckFuture, err := activityPkg.ExecuteAsync(ctx, creditreportNs.CheckPanDobPresent, &crActivityPb.CheckPanDobPresentRequest{
		RequestHeader: &activityPb.RequestHeader{
			Ownership:   ownership,
			ClientReqId: clientReqId,
		},
		ActorId: actorId,
	}, panDobPresentRes)
	if err != nil {
		lg.Error("CheckPanDobPresent activity failed", zap.Error(err))
		if updateErr := updateDownloadProcessAndWorkflowStageStatus(ctx, wfReqId, clientReqId, workflowPb.Stage_STAGE_UNSPECIFIED, stagePb.Status_FAILED,
			downloadReq.GetCreditReportDownloadId(), pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_FAILED,
			pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_PAN_DOB_CHECK_FAILED,
			false, serverErrorWithRetryDeeplink(downloadReq.GetRedirectDeeplink()), celestialPkg.GetStageEnumFromStage(stage)); updateErr != nil {
			return false, fmt.Errorf("updateDownloadProcessAndWorkflowStageStatus failed : %w", updateErr)
		}
		return false, fmt.Errorf("%s activity failed: %w", creditreportNs.CheckPanDobPresent, err)
	}

	panDobStatusCheckFuture.AddFutureHandler(func(getErr error, actResp *crActivityPb.CheckPanDobPresentResponse) {
		err = getErr
	})

	panDobStatusCheckFuture.AddToSelector(ctx, workflow.NewSelector(ctx)).
		AddReceive(workflow.GetSignalChannel(ctx, string(creditreportNs.PanDobSubmittedActionSignal)), func(c workflow.ReceiveChannel, more bool) {
			c.Receive(ctx, nil)
			lg.Info("received pan dob submitted action signal")
		}).
		Select(ctx)
	if err != nil {
		lg.Error("CheckPanDobPresent async activity failed", zap.Error(err))
		substatus := pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_PAN_DOB_CHECK_FAILED
		if epifitemporal.IsRetryableError(err) || epifitemporal.HasSignalReceivedTimedOut(err) {
			substatus = pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_PAN_DOB_CHECK_EXPIRED
		}
		if updateErr := updateDownloadProcessAndWorkflowStageStatus(ctx, wfReqId, clientReqId, workflowPb.Stage_STAGE_UNSPECIFIED, stagePb.Status_FAILED,
			downloadReq.GetCreditReportDownloadId(), pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_FAILED, substatus,
			false, serverErrorWithRetryDeeplink(downloadReq.GetRedirectDeeplink()), celestialPkg.GetStageEnumFromStage(stage)); updateErr != nil {
			return false, fmt.Errorf("updateDownloadProcessAndWorkflowStageStatus failed : %w", updateErr)
		}
		return false, fmt.Errorf("%s activity failed: %w", creditreportNs.CheckPanDobPresent, err)
	}

	// reaching this point signifies pan-dob info is successfully saved

	if updateStatusErr := activityPkg.Execute(ctx, creditreportNs.UpdateCreditReportDownloadProcessStatus, &crActivityPb.UpdateCreditReportDownloadProcessStatusResponse{}, &crActivityPb.UpdateCreditReportDownloadProcessStatusRequest{
		RequestHeader: &activityPb.RequestHeader{
			Ownership:   ownership,
			ClientReqId: clientReqId,
		},
		CreditReportDownloadId: downloadReq.GetCreditReportDownloadId(),
		Status:                 pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_RECORD_PAN_DOB_COMPLETED,
		SubStatus:              pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_UNSPECIFIED,
		NextAction:             crDeeplink.CreditReportPollStatusDeeplink(downloadReq.GetClientReqId()),
	}); updateStatusErr != nil {
		lg.Error("UpdateCreditReportDownloadProcessStatus failed", zap.Error(updateStatusErr))
		if updateErr := updateDownloadProcessAndWorkflowStageStatus(ctx, wfReqId, clientReqId, workflowPb.Stage_RECORD_CONSENT, stagePb.Status_FAILED,
			downloadReq.GetCreditReportDownloadId(), pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_FAILED,
			pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_RECORD_CONSENT_INIT_FAILED,
			false, serverErrorWithRetryDeeplink(downloadReq.GetRedirectDeeplink()), nil); updateErr != nil {
			return false, fmt.Errorf("updateDownloadProcessAndWorkflowStageStatus failed : %w", updateErr)
		}
		return false, fmt.Errorf("%s activity failed: %w", creditreportNs.UpdateCreditReportDownloadProcessStatus, err)
	}

	err = celestialPkg.UpdateWorkflowStage(ctx, stage, stagePb.Status_SUCCESSFUL)
	if err != nil {
		// NOTE: returning error here as this is an unexpected platform failure. We expect an alert here
		return false, fmt.Errorf("failed to update workflow stage to success: %s: %w", stage, err)
	}
	return true, nil
}

func performRecordConsentStage(ctx workflow.Context, wfReqId, clientReqId string,
	downloadReq *crWorkflowPb.DownloadCreditReportRequest) (bool, error) {
	lg := workflow.GetLogger(ctx)
	if err := activityPkg.Execute(ctx, epifitemporal.InitiateWorkflowStageV2, &activityPb.InitiateWorkflowStageV2Response{}, &activityPb.InitiateWorkflowStageV2Request{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: clientReqId,
			Ownership:   ownership,
		},
		WfReqId: wfReqId,
		Stage:   workflowPb.Stage_RECORD_CONSENT,
		Status:  stagePb.Status_INITIATED,
	}); err != nil {
		return false, fmt.Errorf("%s activity failed: %w", epifitemporal.InitiateWorkflowStageV2, err)
	}

	platform, version := epificontext.AppPlatformAndVersion(ctx)
	consentInitRes := &crActivityPb.UpdateCreditReportDownloadProcessStatusResponse{}
	if err := activityPkg.Execute(ctx, creditreportNs.UpdateCreditReportDownloadProcessStatus, consentInitRes, &crActivityPb.UpdateCreditReportDownloadProcessStatusRequest{
		RequestHeader: &activityPb.RequestHeader{
			Ownership:   ownership,
			ClientReqId: clientReqId,
		},
		CreditReportDownloadId: downloadReq.GetCreditReportDownloadId(),
		Status:                 pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_RECORD_CONSENT_INITIATED,
		SubStatus:              pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_UNSPECIFIED,
		NextAction: crDeeplink.GetRecordConsentCreditReportDeeplink(downloadReq.GetProvenance(), downloadReq.GetClientReqId(), downloadReq.GetIsFiLiteUser(), downloadReq.GetVendor(), crDeeplink.AppVersionConstraints{
			Platform:          platform,
			CurrVersion:       version,
			MinIosVersion:     minIosVersionForConsentScreenChanges,
			MinAndroidVersion: minAndroidVersionForConsentScreenChanges,
		}),
	}); err != nil {
		lg.Error("InitiateRecordConsentStep failed", zap.Error(err))
		if updateErr := updateDownloadProcessAndWorkflowStageStatus(ctx, wfReqId, clientReqId, workflowPb.Stage_RECORD_CONSENT, stagePb.Status_FAILED,
			downloadReq.GetCreditReportDownloadId(), pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_FAILED,
			pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_RECORD_CONSENT_INIT_FAILED,
			false, serverErrorWithRetryDeeplink(downloadReq.GetRedirectDeeplink()), nil); updateErr != nil {
			return false, fmt.Errorf("updateDownloadProcessAndWorkflowStageStatus failed : %w", updateErr)
		}
		return false, fmt.Errorf("%s activity failed: %w", creditreportNs.UpdateCreditReportDownloadProcessStatus, err)
	}

	actRes := &crActivityPb.CheckRecordConsentStepProgressResponse{}
	statusCheckFuture, err := activityPkg.ExecuteAsync(ctx, creditreportNs.CheckRecordConsentStepProgress, &crActivityPb.CheckRecordConsentStepProgressRequest{
		RequestHeader: &activityPb.RequestHeader{
			Ownership:   ownership,
			ClientReqId: clientReqId,
		},
		CreditReportDownloadId: downloadReq.GetCreditReportDownloadId(),
	}, actRes)
	if err != nil {
		lg.Error("CheckRecordConsentStepProgress activity initiation failed", zap.Error(err))
		if updateErr := updateDownloadProcessAndWorkflowStageStatus(ctx, wfReqId, clientReqId, workflowPb.Stage_RECORD_CONSENT, stagePb.Status_FAILED,
			downloadReq.GetCreditReportDownloadId(), pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_FAILED,
			pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_RECORD_CONSENT_FAILED,
			false, serverErrorWithRetryDeeplink(downloadReq.GetRedirectDeeplink()), nil); updateErr != nil {
			return false, fmt.Errorf("updateDownloadProcessAndWorkflowStageStatus failed : %w", updateErr)
		}
		return false, fmt.Errorf("%s activity initiation failed: %w", creditreportNs.CheckRecordConsentStepProgress, err)
	}

	statusCheckFuture.AddFutureHandler(func(getErr error, actResp *crActivityPb.CheckRecordConsentStepProgressResponse) {
		err = getErr
	})

	statusCheckFuture.AddToSelector(ctx, workflow.NewSelector(ctx)).
		AddReceive(workflow.GetSignalChannel(ctx, string(creditreportNs.ConsentActionSignal)), func(c workflow.ReceiveChannel, more bool) {
			c.Receive(ctx, nil)
			lg.Info("received user consent action signal")
		}).
		Select(ctx)
	if err != nil {
		lg.Error("CheckRecordConsentStepProgress activity failed", zap.Error(err))
		substatus := pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_RECORD_CONSENT_FAILED
		if epifitemporal.IsRetryableError(err) || epifitemporal.HasSignalReceivedTimedOut(err) {
			substatus = pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_RECORD_CONSENT_EXPIRED
		}
		if updateErr := updateDownloadProcessAndWorkflowStageStatus(ctx, wfReqId, clientReqId, workflowPb.Stage_RECORD_CONSENT, stagePb.Status_FAILED,
			downloadReq.GetCreditReportDownloadId(), pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_FAILED, substatus,
			false, serverErrorWithRetryDeeplink(downloadReq.GetRedirectDeeplink()), nil); updateErr != nil {
			return false, fmt.Errorf("updateDownloadProcessAndWorkflowStageStatus failed : %w", updateErr)
		}
		return false, fmt.Errorf("%s activity failed: %w", creditreportNs.CheckRecordConsentStepProgress, err)
	}

	updateStatusRes := &crActivityPb.UpdateCreditReportStatusBasedOnConsentStatusResponse{}
	if err = activityPkg.Execute(ctx, creditreportNs.UpdateCreditReportStatusBasedOnConsentStatus, updateStatusRes, &crActivityPb.UpdateCreditReportStatusBasedOnConsentStatusRequest{
		RequestHeader: &activityPb.RequestHeader{
			Ownership:   ownership,
			ClientReqId: clientReqId,
		},
		CreditReportDownloadId: downloadReq.GetCreditReportDownloadId(),
	}); err != nil {
		lg.Error("UpdateCreditReportStatusBasedOnConsentStatus activity failed", zap.Error(err))
		if updateErr := updateDownloadProcessAndWorkflowStageStatus(ctx, wfReqId, clientReqId, workflowPb.Stage_RECORD_CONSENT, stagePb.Status_FAILED,
			downloadReq.GetCreditReportDownloadId(), pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_FAILED,
			pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_RECORD_CONSENT_FAILED,
			false, serverErrorWithRetryDeeplink(downloadReq.GetRedirectDeeplink()), nil); updateErr != nil {
			return false, fmt.Errorf("updateDownloadProcessAndWorkflowStageStatus failed : %w", updateErr)
		}
		return false, fmt.Errorf("%s activity failed: %w", creditreportNs.UpdateCreditReportStatusBasedOnConsentStatus, err)
	}
	if updateStatusRes.GetProcessStatus() == pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_FAILED {
		lg.Error("record consent step wasn't successful")
		if err := activityPkg.Execute(ctx, epifitemporal.UpdateWorkflowStage, &activityPb.UpdateWorkflowStageResponse{}, &activityPb.UpdateWorkflowStageRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: clientReqId,
				Ownership:   ownership,
			},
			WfReqId: wfReqId,
			Stage:   workflowPb.Stage_RECORD_CONSENT,
			Status:  stagePb.Status_FAILED,
		}); err != nil {
			return false, fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateWorkflowStage, err)
		}
		if err := activityPkg.Execute(ctx, epifitemporal.PublishWorkflowUpdateEventV2, &activityPb.PublishWorkflowUpdateEventV2Response{}, &activityPb.PublishWorkflowUpdateEventV2Request{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: clientReqId,
				Ownership:   ownership,
			},
			WfReqId: wfReqId,
		}); err != nil {
			return false, fmt.Errorf("PublishWorkflowUpdateEvent activity failed: %w", err)
		}
		return false, nil
	}

	if err := activityPkg.Execute(ctx, epifitemporal.UpdateWorkflowStage, &activityPb.UpdateWorkflowStageResponse{}, &activityPb.UpdateWorkflowStageRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: clientReqId,
			Ownership:   ownership,
		},
		WfReqId: wfReqId,
		Stage:   workflowPb.Stage_RECORD_CONSENT,
		Status:  stagePb.Status_SUCCESSFUL,
	}); err != nil {
		return false, fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateWorkflowStage, err)
	}
	return true, nil
}

func performVendorCallStage(ctx workflow.Context, wfReqId, clientReqId string,
	downloadReq *crWorkflowPb.DownloadCreditReportRequest) error {
	lg := workflow.GetLogger(ctx)

	if err := activityPkg.Execute(ctx, epifitemporal.InitiateWorkflowStageV2, &activityPb.InitiateWorkflowStageV2Response{}, &activityPb.InitiateWorkflowStageV2Request{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: clientReqId,
			Ownership:   ownership,
		},
		WfReqId: wfReqId,
		Stage:   workflowPb.Stage_VENDOR_CALL,
		Status:  stagePb.Status_INITIATED,
	}); err != nil {
		return fmt.Errorf("%s activity failed: %w", epifitemporal.InitiateWorkflowStageV2, err)
	}

	downloadRes := &crActivityPb.FetchCreditReportFromVendorResponse{}
	if err := activityPkg.Execute(ctx, creditreportNs.FetchCreditReportFromVendor, downloadRes, &crActivityPb.FetchCreditReportFromVendorRequest{
		RequestHeader: &activityPb.RequestHeader{
			Ownership:   ownership,
			ClientReqId: clientReqId,
		},
		CreditReportDownloadId: downloadReq.GetCreditReportDownloadId(),
		Pan:                    downloadReq.GetPan(),
	}); err != nil {
		lg.Error("FetchCreditReportFromVendor failed", zap.Error(err))
		if updateErr := updateDownloadProcessAndWorkflowStageStatus(ctx, wfReqId, clientReqId, workflowPb.Stage_VENDOR_CALL, stagePb.Status_FAILED,
			downloadReq.GetCreditReportDownloadId(), pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_FAILED,
			pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_REPORT_DOWNLOAD_FAILED,
			false, serverErrorWithRetryDeeplink(downloadReq.GetRedirectDeeplink()), nil); updateErr != nil {
			return updateErr
		}
		return fmt.Errorf("%s activity failed: %w", creditreportNs.FetchCreditReportFromVendor, err)
	}

	if downloadRes.GetProcessStatus() == pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_FAILED {
		lg.Error("credit report download wasn't successful")
		if err := activityPkg.Execute(ctx, epifitemporal.UpdateWorkflowStage, &activityPb.UpdateWorkflowStageResponse{}, &activityPb.UpdateWorkflowStageRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: clientReqId,
				Ownership:   ownership,
			},
			WfReqId: wfReqId,
			Stage:   workflowPb.Stage_VENDOR_CALL,
			Status:  stagePb.Status_FAILED,
		}); err != nil {
			return fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateWorkflowStage, err)
		}
		if err := activityPkg.Execute(ctx, epifitemporal.PublishWorkflowUpdateEventV2, &activityPb.PublishWorkflowUpdateEventV2Response{}, &activityPb.PublishWorkflowUpdateEventV2Request{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: clientReqId,
				Ownership:   ownership,
			},
			WfReqId: wfReqId,
		}); err != nil {
			return fmt.Errorf("PublishWorkflowUpdateEvent activity failed: %w", err)
		}
		return nil
	}

	if downloadReq.GetFetchType() == pb.CreditReportFetchType_CREDIT_REPORT_FETCH_TYPE_CONSENT_EXTENSION {
		extensionRes := &crActivityPb.ExtendCreditReportSubscriptionResponse{}
		if err := activityPkg.Execute(ctx, creditreportNs.ExtendCreditReportSubscription, extensionRes, &crActivityPb.ExtendCreditReportSubscriptionRequest{
			RequestHeader: &activityPb.RequestHeader{
				Ownership:   ownership,
				ClientReqId: clientReqId,
			},
			CreditReportDownloadId: downloadReq.GetCreditReportDownloadId(),
		}); err != nil {
			lg.Error("ExtendCreditReportSubscription failed", zap.Error(err))
		}
	}

	publishRes := &crActivityPb.PublishCreditReportDownloadEventResponse{}
	if err := activityPkg.Execute(ctx, creditreportNs.PublishCreditReportDownloadEvent, publishRes, &crActivityPb.PublishCreditReportDownloadEventRequest{
		RequestHeader: &activityPb.RequestHeader{
			Ownership:   ownership,
			ClientReqId: clientReqId,
		},
		CreditReportDownloadId: downloadReq.GetCreditReportDownloadId(),
		Status:                 pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_COMPLETED,
		SubStatus:              downloadRes.GetProcessSubStatus(),
	}); err != nil {
		lg.Error("failed to publish event for credit report download completion, PublishCreditReportDownloadEvent activity failed", zap.Error(err))
	}

	updateRes := &crActivityPb.UpdateCreditReportDownloadProcessStatusResponse{}
	if err := activityPkg.Execute(ctx, creditreportNs.UpdateCreditReportDownloadProcessStatus, updateRes, &crActivityPb.UpdateCreditReportDownloadProcessStatusRequest{
		RequestHeader: &activityPb.RequestHeader{
			Ownership:   ownership,
			ClientReqId: clientReqId,
		},
		CreditReportDownloadId: downloadReq.GetCreditReportDownloadId(),
		Status:                 pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_COMPLETED,
		SubStatus:              downloadRes.GetProcessSubStatus(),
		RedirectToCaller:       true,
	}); err != nil {
		lg.Error("failed to mark process as completed, UpdateCreditReportDownloadProcessStatus activity failed", zap.Error(err))
		if err := activityPkg.Execute(ctx, epifitemporal.UpdateWorkflowStage, &activityPb.UpdateWorkflowStageResponse{}, &activityPb.UpdateWorkflowStageRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: clientReqId,
				Ownership:   ownership,
			},
			WfReqId: wfReqId,
			Stage:   workflowPb.Stage_VENDOR_CALL,
			Status:  stagePb.Status_FAILED,
		}); err != nil {
			return fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateWorkflowStage, err)
		}
	}

	if err := activityPkg.Execute(ctx, epifitemporal.UpdateWorkflowStage, &activityPb.UpdateWorkflowStageResponse{}, &activityPb.UpdateWorkflowStageRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: clientReqId,
			Ownership:   ownership,
		},
		WfReqId: wfReqId,
		Stage:   workflowPb.Stage_VENDOR_CALL,
		Status:  stagePb.Status_SUCCESSFUL,
	}); err != nil {
		return fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateWorkflowStage, err)
	}

	return nil
}

// nolint:unparam
func updateDownloadProcessAndWorkflowStageStatus(ctx workflow.Context, wfReqId, clientReqId string, wfStage workflowPb.Stage,
	wfStatus stagePb.Status, downloadId string, status pb.CreditReportDownloadStatus,
	subStatus pb.CreditReportDownloadSubStatus, redirectToCaller bool, nextAction *deeplink.Deeplink, wfStageEnum *workflowPb.StageEnum) error {
	lg := workflow.GetLogger(ctx)
	updateRes := &crActivityPb.UpdateCreditReportDownloadProcessStatusResponse{}
	if err := activityPkg.Execute(ctx, creditreportNs.UpdateCreditReportDownloadProcessStatus, updateRes, &crActivityPb.UpdateCreditReportDownloadProcessStatusRequest{
		RequestHeader: &activityPb.RequestHeader{
			Ownership:   ownership,
			ClientReqId: clientReqId,
		},
		CreditReportDownloadId: downloadId,
		Status:                 status,
		SubStatus:              subStatus,
		RedirectToCaller:       redirectToCaller,
		NextAction:             nextAction,
	}); err != nil {
		lg.Error("UpdateCreditReportDownloadProcessStatus failed", zap.Error(err))
		if stgUpdateErr := activityPkg.Execute(ctx, epifitemporal.UpdateWorkflowStage, &activityPb.UpdateWorkflowStageResponse{}, &activityPb.UpdateWorkflowStageRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: clientReqId,
				Ownership:   ownership,
			},
			WfReqId:   wfReqId,
			Stage:     wfStage,
			StageEnum: wfStageEnum,
			Status:    stagePb.Status_FAILED,
		}); stgUpdateErr != nil {
			return fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateWorkflowStage, stgUpdateErr)
		}
		return fmt.Errorf("%s activity failed: %w", creditreportNs.UpdateCreditReportDownloadProcessStatus, err)
	}
	if err := activityPkg.Execute(ctx, epifitemporal.UpdateWorkflowStage, &activityPb.UpdateWorkflowStageResponse{}, &activityPb.UpdateWorkflowStageRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: clientReqId,
			Ownership:   ownership,
		},
		WfReqId:   wfReqId,
		Stage:     wfStage,
		Status:    wfStatus,
		StageEnum: wfStageEnum,
	}); err != nil {
		return fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateWorkflowStage, err)
	}
	if stagePb.IsTerminalStatus(wfStatus) {
		if err := activityPkg.Execute(ctx, epifitemporal.PublishWorkflowUpdateEventV2, &activityPb.PublishWorkflowUpdateEventV2Response{}, &activityPb.PublishWorkflowUpdateEventV2Request{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: clientReqId,
				Ownership:   ownership,
			},
			WfReqId: wfReqId,
		}); err != nil {
			return fmt.Errorf("PublishWorkflowUpdateEvent activity failed: %w", err)
		}
	}
	return nil
}

func recordConsentScreenDeeplink(reqId string, isFiLiteUser bool) *deeplink.Deeplink {
	var (
		body     string
		tncItems []*widget.CheckboxItem
	)
	if isFiLiteUser {
		tncItems = append(tncItems, &widget.CheckboxItem{
			Id: consent.ConsentType_CREDIT_REPORT_UPDATED_TNC.String(),
			DisplayText: &commontypes.Text{
				FontColor: "#878A8D",
				DisplayValue: &commontypes.Text_Html{
					Html: "I consent to epiFi receiving your credit information from Experian and agree to <a style='color: #00B899; text-decoration: none;' href=\"https://fi.money/assets/pages/experian-tnc\">Experian’s T&C</a> & <a style='color: #00B899; text-decoration: none;' href=\"https://fi.money/privacy\">Fi Privacy Policy</a>.",
				},
			},
			IsChecked: false,
		}, &widget.CheckboxItem{
			Id: consent.ConsentType_CREDIT_REPORT_LIMITED_TIME_TNC.String(),
			DisplayText: &commontypes.Text{
				FontColor: "#878A8D",
				DisplayValue: &commontypes.Text_Html{
					Html: "I understand this data will be available for <a style='color: #00B899; text-decoration: none;' href=\"https://fi.money/T&C\">90 days</a>.",
				},
			},
			IsChecked: false,
		})
	} else {
		body = "By proceeding, you consent to epiFi receiving your credit information from Experian and agree to <a style='color: #00B899; text-decoration: none;' href=\"https://fi.money/assets/pages/experian-tnc\">Experian’s T&C</a> and <a style='color: #00B899; text-decoration: none;' href=\"https://fi.money/privacy\">Fi Privacy Policy</a>."
	}

	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_REPORT_CONSENT_V2,
		ScreenOptions: &deeplink.Deeplink_CreditReportConsentV2ScreenOptions{
			CreditReportConsentV2ScreenOptions: &deeplink.CreditReportConsentV2ScreenOptions{
				ClientRequestId: reqId,
				Title:           "Check your latest score",
				SubTitle:        "Track your credit report <b>without impacting your score</b> and find if you're eligible for credit card & personal loan.",
				VendorDetail: &deeplink.VendorDetail{
					Text: "POWERED BY",
					VendorIconUrls: []string{
						"https://epifi-icons.pointz.in/credit_score/experian_logo_dark.png",
					},
				},
				Body: body,
				Cta: &deeplink.CreditReportConsentCta{
					Type: deeplink.CreditReportConsentCtaType_CREDIT_REPORT_CONSENT_CTA_TYPE_SLIDER,
					Text: "Swipe to get the score",
				},
				TncItems: tncItems,
			},
		},
	}
}

func serverErrorWithRetryDeeplink(redirectDeeplink *deeplink.Deeplink) *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_REPORT_DOWNLOAD_FAILURE_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CreditReportDownloadFailureScreenOptions{
			CreditReportDownloadFailureScreenOptions: &deeplink.CreditReportDownloadFailureScreenOptions{
				Title: "We faced a server error",
				Body:  "Uh-oh! We faced a server error while loading this page. Please try again.",
				Ctas: []*deeplink.Cta{
					{
						Type:         deeplink.Cta_DONE,
						Text:         "Go back",
						Status:       deeplink.Cta_CTA_STATUS_ENABLED,
						DisplayTheme: deeplink.Cta_PRIMARY,
					},
					{
						Type:         deeplink.Cta_RETRY,
						Text:         "Retry",
						Status:       deeplink.Cta_CTA_STATUS_ENABLED,
						DisplayTheme: deeplink.Cta_PRIMARY,
						Deeplink:     redirectDeeplink,
					},
				},
			},
		},
	}
}

func PanDobScreenDeeplink() (*deeplink.Deeplink, error) {
	redirectionDeeplinkStr := "{\"screen\":\"ANALYSER_SCREEN\",\"analyserScreenOptions\":{\"analyserName\":\"ANALYSER_NAME_CREDIT_SCORE\"}}"
	consents := []*deeplink.Consent{
		{
			Text: onboardingPkg.FiStorageTncTextV2,
			TextV2: &commontypes.Text{
				DisplayValue: &commontypes.Text_Html{
					Html: onboardingPkg.FiStorageTncTextV2,
				},
			},
			Consent: deeplink.ConsentTypeUrl_FI_PRIVACY_POLICY.String(),
			ConsentInfos: []*deeplink.Consent_ConsentInfo{
				{
					Title:       onboardingPkg.EligibilityChecksTitle,
					Description: onboardingPkg.EligibilityChecksDescription,
				},
			},
		},
	}
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_REGISTER_CKYC,
		ScreenOptions: &deeplink.Deeplink_RegisterCkycScreenOptions{
			RegisterCkycScreenOptions: &deeplink.RegisterCKYCScreenOptions{
				Title:      onboardingPkg.PanDobTitleForCreditReport,
				Subtitle:   onboardingPkg.PanDobSubtitleForCreditReport,
				IsEditable: commontypes.BooleanEnum_TRUE,
				EntryPoint: signup.EntryPoint_ENTRY_POINT_CREDIT_SCORE_ANALYSER.String(),
				Consents:   consents,
				BackAction: &deeplink.Deeplink{
					Screen: deeplink.Screen_HOME,
				},
				BackActionV2: &deeplink.BackAction{
					ShowButton: commontypes.BooleanEnum_TRUE,
					Deeplink: &deeplink.Deeplink{
						Screen: deeplink.Screen_HOME,
					},
				},
				Blob: []byte(redirectionDeeplinkStr),
			},
		},
	}, nil
}
