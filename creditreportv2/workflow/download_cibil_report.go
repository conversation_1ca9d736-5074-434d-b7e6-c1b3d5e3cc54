// nolint:goimports,govet
package workflow

import (
	"errors"
	"fmt"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	"github.com/epifi/be-common/pkg/epifitemporal/celestial"
	creditreportNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/creditreport"
	errors2 "github.com/pkg/errors"

	pb "github.com/epifi/gamma/api/creditreportv2"
	crActivityPb "github.com/epifi/gamma/api/creditreportv2/activity"
	crWorkflowPb "github.com/epifi/gamma/api/creditreportv2/workflow"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/creditreportv2/deeplink"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
)

func downloadReportForCibil(ctx workflow.Context, wfReqId, clientReqId string,
	downloadReq *crWorkflowPb.DownloadCreditReportRequest) error {
	// --------------- RECORD_CONSENT STAGE --------------------
	if !downloadReq.GetIsConsentTaken() {
		continueProcess, err := performRecordConsentStage(ctx, wfReqId, clientReqId, downloadReq)
		if err != nil {
			return err
		}
		if !continueProcess {
			return nil
		}
	}
	// ---------------- PAN DOB STAGE ---------------
	if downloadReq.GetIsFiLiteUser() {
		continueProcess, err := performPanDobRecordStage(ctx, wfReqId, clientReqId, downloadReq)
		if err != nil {
			return err
		}
		if !continueProcess {
			return nil
		}
	}

	// --------------- FULFILL OFFER STAGE --------------------
	if err := performFulfillOfferStage(ctx, wfReqId, clientReqId, downloadReq); err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			// In order to make the credit report WF success, not returning any error code from here.
			// This will come in the case of credit report not found for a user.
			return nil
		}
		return err
	}

	// --------------- CUSTOMER AUTHENTICATION STAGE --------------------
	if err := performCustomerAuthenticationStage(ctx, wfReqId, clientReqId, downloadReq); err != nil {
		return err
	}

	// --------------- GET REPORT STAGE --------------------
	if err := performGetReportStage(ctx, wfReqId, clientReqId, downloadReq); err != nil {
		return err
	}

	// --------------- SEND PRODUCT URL STAGE --------------------
	if err := performSendProductUrl(ctx, wfReqId, clientReqId, downloadReq); err != nil {
		return err
	}

	wfVersion := workflow.GetVersion(ctx, "cibil-publish-flattening-event", workflow.DefaultVersion, 1)
	if wfVersion == 1 {
		// publish packet to flatten Cibil report
		// Currently we are performing this step on best effort basis and not marking the workflow failed
		_ = publishForReportFlattening(ctx, wfReqId, clientReqId, downloadReq)
	}

	// --------------- COMPLETE WF STAGE --------------------
	return markCrDownloadWfSuccessStage(ctx, clientReqId, downloadReq)
}

func publishForReportFlattening(ctx workflow.Context, wfReqId, clientReqId string, downloadReq *crWorkflowPb.DownloadCreditReportRequest) error {
	lg := workflow.GetLogger(ctx)
	wfStage := creditreportNs.CrPublishFlattenCibilReportStage
	wfStageEnum := celestial.GetStageEnumFromStage(wfStage)

	if err := activityPkg.Execute(ctx, epifitemporal.InitiateWorkflowStageV2, &activityPb.InitiateWorkflowStageV2Response{}, &activityPb.InitiateWorkflowStageV2Request{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: clientReqId,
			Ownership:   ownership,
		},
		WfReqId:   wfReqId,
		Status:    stagePb.Status_INITIATED,
		StageEnum: wfStageEnum,
	}); err != nil {
		return fmt.Errorf("%s activity failed: %w", epifitemporal.InitiateWorkflowStageV2, err)
	}

	actRes := &crActivityPb.PublishFlattenCibilReportEventResponse{}
	if err := activityPkg.Execute(ctx, creditreportNs.PublishFlattenCibilReportEvent, actRes, &crActivityPb.PublishFlattenCibilReportEventRequest{
		RequestHeader: &activityPb.RequestHeader{
			Ownership:   ownership,
			ClientReqId: clientReqId,
		},
		CreditReportDownloadId: downloadReq.GetCreditReportDownloadId(),
	}); err != nil {
		lg.Error("failed to publish packet to flatten cibil report, activity: PublishFlattenCibilReportEvent", zap.Error(err))
		return err
	}

	_ = updateWorkflowStageAndPublishEventForSuccess(ctx, wfReqId, clientReqId, wfStage)
	return nil
}

func performFulfillOfferStage(ctx workflow.Context, wfReqId, clientReqId string, downloadReq *crWorkflowPb.DownloadCreditReportRequest) error {
	lg := workflow.GetLogger(ctx)
	wfStage := creditreportNs.CrDownloadFulFillOfferStage
	wfStageEnum := celestial.GetStageEnumFromStage(wfStage)

	if err := activityPkg.Execute(ctx, epifitemporal.InitiateWorkflowStageV2, &activityPb.InitiateWorkflowStageV2Response{}, &activityPb.InitiateWorkflowStageV2Request{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: clientReqId,
			Ownership:   ownership,
		},
		WfReqId:   wfReqId,
		Status:    stagePb.Status_INITIATED,
		StageEnum: wfStageEnum,
	}); err != nil {
		return fmt.Errorf("%s activity failed: %w", epifitemporal.InitiateWorkflowStageV2, err)
	}

	fulfillResp := &crActivityPb.FulFillOfferResponse{}
	if err := activityPkg.Execute(ctx, creditreportNs.CibilFulfillOffer, fulfillResp, &crActivityPb.FulFillOfferRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: clientReqId,
			Ownership:   ownership,
		},
		CreditReportDownloadId: downloadReq.GetCreditReportDownloadId(),
		Name:                   downloadReq.GetName(),
	}); err != nil {
		lg.Error("FulFillOffer failed", zap.Error(err))
		if updateErr := updateDownloadProcessAndWorkflowStageStatus(ctx, wfReqId, clientReqId, workflowPb.Stage_STAGE_UNSPECIFIED, stagePb.Status_FAILED,
			downloadReq.GetCreditReportDownloadId(), pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_FAILED,
			pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_FULFILL_OFFER_FAILED,
			false, serverErrorWithRetryDeeplink(downloadReq.GetRedirectDeeplink()), wfStageEnum); updateErr != nil {
			return updateErr
		}
		return fmt.Errorf("%s activity failed: %w", creditreportNs.CibilFulfillOffer, err)
	}

	// if fulfill offer was not completed or if cibil report was not found for a user, stop the workflow
	// nolint: dupl
	if fulfillResp.GetStatus() == pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_COMPLETED {
		if fulfillResp.GetSubStatus() == pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_REPORT_NOT_FOUND {
			if updateErr := updateDownloadProcessAndWorkflowStageStatus(ctx, wfReqId, clientReqId, workflowPb.Stage_STAGE_UNSPECIFIED, stagePb.Status_SUCCESSFUL,
				downloadReq.GetCreditReportDownloadId(), pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_COMPLETED,
				pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_REPORT_NOT_FOUND,
				true, deeplink.GetTerminalScreenWithExitDeeplink(fulfillResp.GetSubStatus(), downloadReq.GetRedirectDeeplink()), celestial.GetStageEnumFromStage(wfStage)); updateErr != nil {
				return updateErr
			}
			return errors2.Wrapf(epifierrors.ErrRecordNotFound, "%s activity success: %w", creditreportNs.CibilFulfillOffer, errors.New("report not found"))
		}
	}
	if fulfillResp.GetStatus() != pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_FULFILL_OFFER_COMPLETED {
		lg.Error("FulFillOffer was not successful")
		return updateWorkflowStageAndPublishEventForFailure(ctx, wfReqId, clientReqId, wfStage, downloadReq.GetCreditReportDownloadId(),
			fulfillResp.GetSubStatus(), downloadReq.GetRedirectDeeplink(), creditreportNs.CibilFulfillOffer, "fulfill offer not in completed state after activity")
	}

	return updateWorkflowStageAndPublishEventForSuccess(ctx, wfReqId, clientReqId, wfStage)
}

// nolint: funlen
func performCustomerAuthenticationStage(ctx workflow.Context, wfReqId, clientReqId string, downloadReq *crWorkflowPb.DownloadCreditReportRequest) error {
	lg := workflow.GetLogger(ctx)
	wfStage := creditreportNs.CrDownloadCustomerAuthStage
	wfStageEnum := celestial.GetStageEnumFromStage(wfStage)

	// initiate workflow stage
	if err := activityPkg.Execute(ctx, epifitemporal.InitiateWorkflowStageV2, &activityPb.InitiateWorkflowStageV2Response{}, &activityPb.InitiateWorkflowStageV2Request{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: clientReqId,
			Ownership:   ownership,
		},
		WfReqId:   wfReqId,
		Status:    stagePb.Status_INITIATED,
		StageEnum: wfStageEnum,
	}); err != nil {
		return fmt.Errorf("%s activity failed: %w", epifitemporal.InitiateWorkflowStageV2, err)
	}

	// initiate customer authentication via activity InitiateCustomerAuthentication
	initResp := &crActivityPb.InitiateCustomerAuthenticationResponse{}
	if err := activityPkg.Execute(ctx, creditreportNs.InitiateCustomerAuthentication, initResp, &crActivityPb.InitiateCustomerAuthenticationRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: clientReqId,
			Ownership:   ownership,
		},
		CreditReportDownloadId: downloadReq.GetCreditReportDownloadId(),
	}); err != nil {
		lg.Error("CibilCustomerAuthenticationInitiate failed", zap.Error(err))
		if updateErr := updateDownloadProcessAndWorkflowStageStatus(ctx, wfReqId, clientReqId, workflowPb.Stage_STAGE_UNSPECIFIED, stagePb.Status_FAILED,
			downloadReq.GetCreditReportDownloadId(), pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_FAILED,
			pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_CUSTOMER_AUTHENTICATION_FAILED,
			false, serverErrorWithRetryDeeplink(downloadReq.GetRedirectDeeplink()), wfStageEnum); updateErr != nil {
			return fmt.Errorf("updateDownloadProcessAndWorkflowStageStatus failed : %w", updateErr)
		}
		return fmt.Errorf("%s activity failed: %w", creditreportNs.InitiateCustomerAuthentication, err)
	}
	// make decision based on result of activity
	switch {
	case initResp.GetStatus() == pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_CUSTOMER_AUTHENTICATION_COMPLETED:
		return updateWorkflowStageAndPublishEventForSuccess(ctx, wfReqId, clientReqId, wfStage)
	case initResp.GetStatus() != pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_CUSTOMER_AUTHENTICATION_INITIATED:
		return updateWorkflowStageAndPublishEventForFailure(ctx, wfReqId, clientReqId, wfStage, downloadReq.GetCreditReportDownloadId(),
			initResp.GetSubStatus(), downloadReq.GetRedirectDeeplink(), creditreportNs.InitiateCustomerAuthentication, "")
	}

	// keep checking customer authentication status till the process is in terminal state by waiting for completion signal
	actResp := &crActivityPb.CheckCustomerAuthenticationStatusResponse{}
	statusCheckFuture, err := activityPkg.ExecuteAsync(ctx, creditreportNs.CheckCustomerAuthenticationStatus, &crActivityPb.CheckCustomerAuthenticationStatusRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: clientReqId,
			Ownership:   ownership,
		},
		CreditReportDownloadId: downloadReq.GetCreditReportDownloadId(),
	}, actResp)
	if err != nil {
		lg.Error("CheckCustomerAuthenticationStatus activity failed", zap.Error(err))
		if updateErr := updateDownloadProcessAndWorkflowStageStatus(ctx, wfReqId, clientReqId, workflowPb.Stage_STAGE_UNSPECIFIED, stagePb.Status_FAILED,
			downloadReq.GetCreditReportDownloadId(), pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_FAILED,
			pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_CUSTOMER_AUTHENTICATION_FAILED,
			false, serverErrorWithRetryDeeplink(downloadReq.GetRedirectDeeplink()), wfStageEnum); updateErr != nil {
			return fmt.Errorf("updateDownloadProcessAndWorkflowStageStatus failed : %w", updateErr)
		}
		return fmt.Errorf("%s activity initiation failed: %w", creditreportNs.CheckAuthFlowProcessStatus, err)
	}

	statusCheckFuture.AddFutureHandler(func(getErr error, actResp *crActivityPb.CheckCustomerAuthenticationStatusResponse) {
		lg.Info("CheckCustomerAuthenticationStatus future finished")
		err = getErr
	})
	statusCheckFuture.AddToSelector(ctx, workflow.NewSelector(ctx)).
		AddReceive(workflow.GetSignalChannel(ctx, string(creditreportNs.AuthFlowCompletionSignal)), func(c workflow.ReceiveChannel, more bool) {
			lg.Info("received auth flow completion signal")
			var payload []byte
			c.Receive(ctx, &payload)
		}).
		Select(ctx)
	if err != nil {
		lg.Error("CheckCustomerAuthenticationStatus`Response activity failed", zap.Error(err))
		substatus := pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_CUSTOMER_AUTHENTICATION_FAILED
		if epifitemporal.IsRetryableError(err) || epifitemporal.HasSignalReceivedTimedOut(err) {
			substatus = pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_CUSTOMER_AUTHENTICATION_EXPIRED
		}
		if updateErr := updateDownloadProcessAndWorkflowStageStatus(ctx, wfReqId, clientReqId, workflowPb.Stage_STAGE_UNSPECIFIED, stagePb.Status_FAILED,
			downloadReq.GetCreditReportDownloadId(), pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_FAILED, substatus,
			false, serverErrorWithRetryDeeplink(downloadReq.GetRedirectDeeplink()), wfStageEnum); updateErr != nil {
			return fmt.Errorf("updateDownloadProcessAndWorkflowStageStatus failed : %w", updateErr)
		}
		return fmt.Errorf("%s activity failed: %w", creditreportNs.CheckAuthFlowProcessStatus, err)
	}

	// customer authentication is in terminal state, change the relevant states in various places
	updateStatusRes := &crActivityPb.UpdateCreditReportStatusBasedOnAuthFlowStatusResponse{}
	if err = activityPkg.Execute(ctx, creditreportNs.UpdateCreditReportStatusBasedOnAuthFlowStatus, updateStatusRes, &crActivityPb.UpdateCreditReportStatusBasedOnAuthFlowStatusRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: clientReqId,
			Ownership:   ownership,
		},
		CreditReportDownloadId: downloadReq.GetCreditReportDownloadId(),
	}); err != nil {
		lg.Error("UpdateCreditReportStatusBasedOnAuthFlowStatus activity failed", zap.Error(err))
		if updateErr := updateDownloadProcessAndWorkflowStageStatus(ctx, wfReqId, clientReqId, workflowPb.Stage_STAGE_UNSPECIFIED, stagePb.Status_FAILED,
			downloadReq.GetCreditReportDownloadId(), pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_FAILED,
			pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_CUSTOMER_AUTHENTICATION_FAILED,
			false, serverErrorWithRetryDeeplink(downloadReq.GetRedirectDeeplink()), celestial.GetStageEnumFromStage(wfStage)); updateErr != nil {
			return fmt.Errorf("updateDownloadProcessAndWorkflowStageStatus failed : %w", updateErr)
		}
		return fmt.Errorf("%s activity failed: %w", creditreportNs.UpdateCreditReportStatusBasedOnAuthFlowStatus, err)
	}
	if updateStatusRes.GetProcessStatus() != pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_CUSTOMER_AUTHENTICATION_COMPLETED {
		lg.Error("customer authentication wasn't successful")
		// for v0 (first 100 users), we skip the failure screen and redirect them to the program.
		if updateErr := updateDownloadProcessAndWorkflowStageStatus(ctx, wfReqId, clientReqId, workflowPb.Stage_STAGE_UNSPECIFIED, stagePb.Status_FAILED,
			downloadReq.GetCreditReportDownloadId(), pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_FAILED,
			pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_CUSTOMER_AUTHENTICATION_FAILED,
			false, downloadReq.GetRedirectDeeplink(), wfStageEnum); updateErr != nil {
			return fmt.Errorf("updateDownloadProcessAndWorkflowStageStatus failed : %w", updateErr)
		}
		return fmt.Errorf("%s activity failed: %w", creditreportNs.CheckAuthFlowProcessStatus, err)
	}

	return updateWorkflowStageAndPublishEventForSuccess(ctx, wfReqId, clientReqId, wfStage)
}

// nolint: dupl
func performGetReportStage(ctx workflow.Context, wfReqId, clientReqId string, downloadReq *crWorkflowPb.DownloadCreditReportRequest) error {
	lg := workflow.GetLogger(ctx)
	wfStage := creditreportNs.CrDownloadGetReportStage
	wfStageEnum := celestial.GetStageEnumFromStage(wfStage)

	if err := activityPkg.Execute(ctx, epifitemporal.InitiateWorkflowStageV2, &activityPb.InitiateWorkflowStageV2Response{}, &activityPb.InitiateWorkflowStageV2Request{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: clientReqId,
			Ownership:   ownership,
		},
		WfReqId:   wfReqId,
		Status:    stagePb.Status_INITIATED,
		StageEnum: wfStageEnum,
	}); err != nil {
		return fmt.Errorf("%s activity failed: %w", epifitemporal.InitiateWorkflowStageV2, err)
	}

	getReportResp := &crActivityPb.FetchCreditReportFromVendorResponse{}
	if err := activityPkg.Execute(ctx, creditreportNs.FetchCreditReportFromVendor, getReportResp, &crActivityPb.FulFillOfferRequest{
		RequestHeader: &activityPb.RequestHeader{
			Ownership:   ownership,
			ClientReqId: clientReqId,
		},
		CreditReportDownloadId: downloadReq.GetCreditReportDownloadId(),
	}); err != nil {
		lg.Error("FetchCreditReportFromVendor failed", zap.Error(err))
		if updateErr := updateDownloadProcessAndWorkflowStageStatus(ctx, wfReqId, clientReqId, workflowPb.Stage_STAGE_UNSPECIFIED, stagePb.Status_FAILED,
			downloadReq.GetCreditReportDownloadId(), pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_FAILED,
			pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_REPORT_DOWNLOAD_FAILED,
			false, serverErrorWithRetryDeeplink(downloadReq.GetRedirectDeeplink()), celestial.GetStageEnumFromStage(wfStage)); updateErr != nil {
			return updateErr
		}
		return fmt.Errorf("%s activity failed: %w", creditreportNs.FetchCreditReportFromVendor, err)
	}

	// nolint: dupl
	if getReportResp.GetProcessSubStatus() == pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_REPORT_NOT_FOUND {
		if updateErr := updateDownloadProcessAndWorkflowStageStatus(ctx, wfReqId, clientReqId, workflowPb.Stage_STAGE_UNSPECIFIED, stagePb.Status_SUCCESSFUL,
			downloadReq.GetCreditReportDownloadId(), pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_COMPLETED,
			pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_REPORT_NOT_FOUND,
			true, deeplink.GetTerminalScreenWithExitDeeplink(getReportResp.GetProcessSubStatus(), downloadReq.GetRedirectDeeplink()), celestial.GetStageEnumFromStage(wfStage)); updateErr != nil {
			return updateErr
		}
		return errors2.Wrapf(epifierrors.ErrRecordNotFound, "%s activity success: %w", creditreportNs.CibilFulfillOffer, errors.New("report not found"))
	}
	if getReportResp.GetProcessStatus() != pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_REPORT_DOWNLOADED {
		lg.Error("credit report download wasn't successful")
		return updateWorkflowStageAndPublishEventForFailure(ctx, wfReqId, clientReqId, wfStage, downloadReq.GetCreditReportDownloadId(),
			getReportResp.GetProcessSubStatus(), downloadReq.GetRedirectDeeplink(), creditreportNs.FetchCreditReportFromVendor, "report download not in completed state after activity")
	}

	publishRes := &crActivityPb.PublishCreditReportDownloadEventResponse{}
	if err := activityPkg.Execute(ctx, creditreportNs.PublishCreditReportDownloadEvent, publishRes, &crActivityPb.PublishCreditReportDownloadEventRequest{
		RequestHeader: &activityPb.RequestHeader{
			Ownership:   ownership,
			ClientReqId: clientReqId,
		},
		CreditReportDownloadId: downloadReq.GetCreditReportDownloadId(),
		Status:                 pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_REPORT_DOWNLOADED,
	}); err != nil {
		lg.Error("failed to publish event for credit report download completion, PublishCreditReportDownloadEvent activity failed", zap.Error(err))
	}

	return updateWorkflowStageAndPublishEventForSuccess(ctx, wfReqId, clientReqId, wfStage)
}

// performSendProductUrl is a non-blocking stage from a product stand-point.
// Hence, sending no error in case failures are encountered
// nolint: dupl
func performSendProductUrl(ctx workflow.Context, wfReqId, clientReqId string, downloadReq *crWorkflowPb.DownloadCreditReportRequest) error {
	lg := workflow.GetLogger(ctx)
	wfStage := creditreportNs.CrDownloadSendProductUrlStage
	wfStageEnum := celestial.GetStageEnumFromStage(wfStage)

	if err := activityPkg.Execute(ctx, epifitemporal.InitiateWorkflowStageV2, &activityPb.InitiateWorkflowStageV2Response{}, &activityPb.InitiateWorkflowStageV2Request{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: clientReqId,
			Ownership:   ownership,
		},
		WfReqId:   wfReqId,
		Status:    stagePb.Status_INITIATED,
		StageEnum: wfStageEnum,
	}); err != nil {
		return fmt.Errorf("%s activity failed: %w", epifitemporal.InitiateWorkflowStageV2, err)
	}

	sendProductResp := &crActivityPb.SendProductUrlResponse{}
	if err := activityPkg.Execute(ctx, creditreportNs.SendProductUrl, sendProductResp, &crActivityPb.SendProductUrlRequest{
		RequestHeader: &activityPb.RequestHeader{
			Ownership:   ownership,
			ClientReqId: clientReqId,
		},
		CreditReportDownloadId: downloadReq.GetCreditReportDownloadId(),
	}); err != nil {
		lg.Error("SendProductUrl failed", zap.Error(err))
	}

	if sendProductResp.GetStatus() != pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_SEND_PRODUCT_URL_COMPLETED {
		lg.Error("send product url wasn't successful")
		_ = updateWorkflowStageAndPublishEventForFailure(ctx, wfReqId, clientReqId, wfStage, downloadReq.GetCreditReportDownloadId(),
			sendProductResp.GetSubStatus(), downloadReq.GetRedirectDeeplink(), creditreportNs.SendProductUrl, "send product url not in completed state after activity")
		return nil
	}

	_ = updateWorkflowStageAndPublishEventForSuccess(ctx, wfReqId, clientReqId, wfStage)
	return nil
}

func markCrDownloadWfSuccessStage(ctx workflow.Context, clientReqId string, downloadReq *crWorkflowPb.DownloadCreditReportRequest) error {
	lg := workflow.GetLogger(ctx)
	updateRes := &crActivityPb.UpdateCreditReportDownloadProcessStatusResponse{}

	if err := activityPkg.Execute(ctx, creditreportNs.UpdateCreditReportDownloadProcessStatus, updateRes, &crActivityPb.UpdateCreditReportDownloadProcessStatusRequest{
		RequestHeader: &activityPb.RequestHeader{
			Ownership:   ownership,
			ClientReqId: clientReqId,
		},
		CreditReportDownloadId: downloadReq.GetCreditReportDownloadId(),
		Status:                 pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_COMPLETED,
		SubStatus:              pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_UNSPECIFIED,
		RedirectToCaller:       true,
	}); err != nil {
		lg.Error("failed to mark process as completed, UpdateCreditReportDownloadProcessStatus activity failed", zap.Error(err))
	}
	return nil
}

func updateWorkflowStageAndPublishEventForSuccess(ctx workflow.Context, wfReqId string, clientReqId string, wfStage epifitemporal.Stage) error {
	if err := updateWorkflowStageAndPublishEvent(ctx, wfReqId, stagePb.Status_SUCCESSFUL, clientReqId, wfStage); err != nil {
		return err
	}
	return nil
}

func updateWorkflowStageAndPublishEventForFailure(ctx workflow.Context, wfReqId string, clientReqId string, wfStage epifitemporal.Stage, downloadId string,
	substatus pb.CreditReportDownloadSubStatus, redirectDl *deeplinkPb.Deeplink, activity epifitemporal.Activity, errorMsg string) error {
	if updateErr := updateDownloadProcessAndWorkflowStageStatus(ctx, wfReqId, clientReqId, workflowPb.Stage_STAGE_UNSPECIFIED, stagePb.Status_FAILED,
		downloadId, pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_FAILED, substatus,
		true, deeplink.GetTerminalScreenWithExitDeeplink(substatus, redirectDl), celestial.GetStageEnumFromStage(wfStage)); updateErr != nil {
		return updateErr
	}
	return fmt.Errorf("%s activity failed: %w", activity, errors.New(errorMsg))
}

func updateWorkflowStageAndPublishEvent(ctx workflow.Context, wfReqId string, wfStatus stagePb.Status, clientReqId string, wfStage epifitemporal.Stage) error {
	if err := activityPkg.Execute(ctx, epifitemporal.UpdateWorkflowStage, &activityPb.UpdateWorkflowStageResponse{}, &activityPb.UpdateWorkflowStageRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: clientReqId,
			Ownership:   ownership,
		},
		WfReqId:   wfReqId,
		Status:    wfStatus,
		StageEnum: celestial.GetStageEnumFromStage(wfStage),
	}); err != nil {
		return fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateWorkflowStage, err)
	}
	if err := activityPkg.Execute(ctx, epifitemporal.PublishWorkflowUpdateEventV2, &activityPb.PublishWorkflowUpdateEventV2Response{}, &activityPb.PublishWorkflowUpdateEventV2Request{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: clientReqId,
			Ownership:   ownership,
		},
		WfReqId: wfReqId,
	}); err != nil {
		return fmt.Errorf("PublishWorkflowUpdateEvent activity failed: %w", err)
	}
	return nil
}
