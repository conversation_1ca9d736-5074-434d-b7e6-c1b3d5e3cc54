Flags:
  TrimDebugMessageFromStatus: true

Logging:
  EnableLoggingToFile: true
  LogPath: "/var/log/jarvis/info.log"
  MaxSizeInMBs: 1024
  MaxBackups: 1

NonGolangEc2Services:
  - "sherlock"
  - "web"

ServiceRepoMap:
  # keep the keys lowercase. Config load turns it to lowercase by default.
  actor:
    Org: "epiFi"
    Name: "gamma"
  atlas:
    Org: "epiFi"
    Name: "gamma"
  auth:
    Org: "epiFi"
    Name: "gamma"
  babel:
    Org: "epiFi"
    Name: "babel"
  card:
    Org: "epiFi"
    Name: "gamma"
  casbin:
    Org: "epiFi"
    Name: "gamma"
  comms:
    Org: "epiFi"
    Name: "gamma"
  cx:
    Org: "epiFi"
    Name: "gamma"
  docs:
    Org: "epiFi"
    Name: "gamma"
  ds-wise-prioritise:
    Org: "epiFi"
    Name: "ds-wise-prioritise"
  emailparser:
    Org: "epiFi"
    Name: "EmailParser"
  frontend:
    Org: "epiFi"
    Name: "gamma"
  insights:
    Org: "epiFi"
    Name: "gamma"
  investment:
    Org: "epiFi"
    Name: "gamma"
  kyc:
    Org: "epiFi"
    Name: "gamma"
  nebula:
    Org: "epiFi"
    Name: "gamma"
  nlu-engine:
    Org: "EpiFi2"
    Name: "DnA-core"
  order:
    Org: "epiFi"
    Name: "gamma"
  rewards:
    Org: "epiFi"
    Name: "gamma"
  search:
    Org: "epiFi"
    Name: "gamma"
  sherlock:
    Org: "epiFi"
    Name: "Sherlock"
  simulator:
    Org: "epiFi"
    Name: "gamma"
  tokenizer:
    Org: "epiFi"
    Name: "gamma"
  test:
    Org: "epiFi"
    Name: "gamma"
  user:
    Org: "epiFi"
    Name: "gamma"
  vendordata:
    Org: "epiFi"
    Name: "gamma"
  vendorgateway:
    Org: "epiFi"
    Name: "gamma"
  vendorgateway-pci:
    Org: "epiFi"
    Name: "gamma"
  vendormapping:
    Org: "epiFi"
    Name: "gamma"
  vendornotification:
    Org: "epiFi"
    Name: "gamma"
  vnotificationgw:
    Org: "epiFi"
    Name: "gamma"
  httpgw:
    Org: "epiFi"
    Name: "gamma"
  web:
    Org: "epiFi"
    Name: "web"
  fittt:
    Org: "epiFi"
    Name: "gamma"
  entity-matcher:
    Org: "epiFi"
    Name: "entity-matcher"
  connectedaccount:
    Org: "epiFi"
    Name: "gamma"
  deploy-devdash-backend:
    Org: "epiFi"
    Name: "devdash-backend"
  ocular:
    Org: "epiFi"
    Name: "ocular"
  popular-faqs:
    Org: "epiFi"
    Name: "popular-faqs"
  transaction-categoriser:
    Org: "epiFi"
    Name: "transaction-categoriser"
  merchant:
    Org: "epiFi"
    Name: "dna-merchant-service"
  facematch:
    Org: "epiFi"
    Name: "inhouse-facematch"
  related-queries:
    Org: "epiFi"
    Name: "related-queries-ds-service"
  pay-worker:
    Org: "epiFi"
    Name: "gamma"
  onboarding-risk-detection:
    Org: "epiFi"
    Name: "onboarding-risk-detection"
  smart-parser:
    Org: "epiFi"
    Name: "smart-parser"
  liveness:
    Org: "epiFi"
    Name: "inhouse-liveness"
  recurring-payment-worker:
    Org: "epiFi"
    Name: "gamma"
  lending:
    Org: "epiFi"
    Name: "gamma"
  pre-approved-loan-worker:
    Org: "epiFi"
    Name: "gamma"
  cx-worker:
    Org: "epiFi"
    Name: "gamma"
  risk-worker:
    Org: "epiFi"
    Name: "gamma"
  upi-worker:
    Org: "epiFi"
    Name: "gamma"
  firefly-worker:
    Org: "epiFi"
    Name: "gamma"
  auth-worker:
    Org: "epiFi"
    Name: "gamma"
  credit-report-worker:
    Org: "epiFi"
    Name: "gamma"
  text-semantics:
    Org: "epiFi"
    Name: "text-semantics"
  usstocks-worker:
    Org: "epiFi"
    Name: "gamma"
  wealth-onboarding-worker:
    Org: "epiFi"
    Name: "gamma"
  nlu-entity-engine:
    Org: "epiFi"
    Name: "nlu-entity-engine"
  nlu-intent-engine:
    Org: "epiFi"
    Name: "nlu-intent-engine"
  bank-customer-worker:
    Org: "epiFi"
    Name: "gamma"
  card-worker:
    Org: "epiFi"
    Name: "gamma"
  p2pinvestment-worker:
    Org: "epiFi"
    Name: "gamma"
  nebula-worker:
    Org: "epiFi"
    Name: "gamma"
  smart-gpt:
    Org: "epiFi"
    Name: "smart-gpt"
  auror:
    Org: "epiFi"
    Name: "gringott"
  goblin:
    Org: "epiFi"
    Name: "gringott"
  sgapigateway:
    Org: "epiFi"
    Name: "gringott"
  sgexternalgateway:
    Org: "epiFi"
    Name: "gringott"
  sgvendorgateway:
    Org: "epiFi"
    Name: "gringott"
  central-growth:
    Org: "epiFi"
    Name: "gamma"
  collateralmgrtsp:
    Org: "epiFi"
    Name: "gamma"
  growth-infra:
    Org: "epiFi"
    Name: "gamma"
  onboarding:
    Org: "epiFi"
    Name: "gamma"
  pay:
    Org: "epiFi"
    Name: "gamma"
  universal:
    Org: "epiFi"
    Name: "gamma"
  userrisk:
    Org: "epiFi"
    Name: "gamma"
  wealthdmf:
    Org: "epiFi"
    Name: "gamma"
  deposit-worker:
    Org: "epiFi"
    Name: "gamma"
  health-engine-worker:
    Org: "epiFi"
    Name: "gamma"
  parser-worker:
    Org: "epiFi"
    Name: "gamma"
  rms-worker:
    Org: "epiFi"
    Name: "gamma"
  savings-worker:
    Org: "epiFi"
    Name: "gamma"
  geo:
    Org: "epiFi"
    Name: "red_indians"
  fortuna-ds:
    Org: "epiFi"
    Name: "fortuna-ds"
  loan-default:
    Org: "epiFi"
    Name: "credit-risk-ds"

Ec2ServiceList:
  staging:
    - "sherlock"
    - "web"
  demo: []
  qa:
    - "sherlock"
    - "web"
  uat:
    - "sherlock"
    - "web"
  prod:
    - "sherlock"
    - "web"

AwsAccountsToEnvMap:
  "staging":
    Tenant: "epifi-tech"
    Env: "staging"
  "qa":
    Tenant: "epifi-tech"
    Env: "qa"
  "prod":
    Tenant: "epifi-tech"
    Env: "prod"
  "uat":
    Tenant: "epifi-tech"
    Env: "uat"
  "stockguardian-nonprod":
    Tenant: "stockguardian"
    Env: "qa"
  "stockguardian-prod":
    Tenant: "stockguardian"
    Env: "prod"
QaFiCapitalJobSuffix: "_stockguardian"

EnvK8sServiceMap:
  data-dev: [ "ocular", "ds-wise-prioritise", "popular-faqs", "transaction-categoriser", "merchant",
              "facematch", "related-queries", "entity-matcher", "onboarding-risk-detection",
              "smart-parser","smart-gpt", "liveness", "text-semantics", "nlu-engine", "nlu-entity-engine",
              "nlu-intent-engine", "geo", "fortuna-ds", "loan-default", "babel"]
  data-prod: [ "ocular", "ds-wise-prioritise", "popular-faqs", "transaction-categoriser", "merchant", "facematch", "related-queries",
               "entity-matcher", "onboarding-risk-detection", "smart-parser", "liveness", "text-semantics", "nlu-engine",
               "nlu-entity-engine", "nlu-intent-engine", "geo", "fortuna-ds", "loan-default","smart-gpt", "babel"]
  staging: [ "pay-worker", "recurring-payment-worker", "cx-worker", "pre-approved-loan-worker","risk-worker","upi-worker",
             "firefly-worker","auth-worker", "credit-report-worker", "usstocks-worker", "wealth-onboarding-worker",
             "bank-customer-worker", "card-worker", "p2pinvestment-worker", "nebula-worker",
             "health-engine-worker", "deposit-worker", "savings-worker", "parser-worker","rms-worker"]
  demo: [ "auth-worker", "bank-customer-worker", "card-worker", "credit-report-worker","email-parser","firefly-worker",
        "nebula-worker","p2pinvestment-worker", "pay-worker", "pre-approved-loan-worker", "upi-worker",
          "health-engine-worker", "deposit-worker", "savings-worker", "parser-worker","rms-worker"]
  qa: [ "pay-worker", "recurring-payment-worker", "cx-worker", "pre-approved-loan-worker","risk-worker","upi-worker",
        "firefly-worker","auth-worker", "credit-report-worker", "usstocks-worker", "wealth-onboarding-worker",
        "bank-customer-worker", "card-worker", "p2pinvestment-worker", "nebula-worker",
        "health-engine-worker", "deposit-worker", "savings-worker", "parser-worker","rms-worker"]
  uat: [ "pay-worker", "recurring-payment-worker", "cx-worker", "pre-approved-loan-worker","risk-worker","upi-worker",
         "firefly-worker","auth-worker", "credit-report-worker", "usstocks-worker", "wealth-onboarding-worker",
         "bank-customer-worker", "card-worker", "p2pinvestment-worker", "nebula-worker",
         "health-engine-worker", "deposit-worker", "savings-worker", "parser-worker","rms-worker"]
  prod: [ "pay-worker", "recurring-payment-worker", "cx-worker", "pre-approved-loan-worker","risk-worker","upi-worker",
          "firefly-worker","auth-worker", "credit-report-worker", "usstocks-worker", "wealth-onboarding-worker",
          "bank-customer-worker", "card-worker", "p2pinvestment-worker", "nebula-worker",
          "health-engine-worker", "deposit-worker", "savings-worker", "parser-worker","rms-worker"]

ClusterArnMap:
  data-dev: "arn:aws:eks:ap-south-1:************:cluster/epifi-data-dev"
  data-prod: "arn:aws:eks:ap-south-1:************:cluster/epifi-data-prod"
  staging: "arn:aws:eks:ap-south-1:************:cluster/epifi-staging"
  demo: "arn:aws:eks:ap-south-1:************:cluster/epifi-demo"
  qa: "arn:aws:eks:ap-south-1:************:cluster/epifi-qa"
  uat: "arn:aws:eks:ap-south-1:************:cluster/epifi-uat"
  prod: "arn:aws:eks:ap-south-1:************:cluster/epifi-prod"

EnvTenantRoleArns:
  deploy:
    "epifi": "arn:aws:iam::************:role/"
  uat:
    "epifi": "arn:aws:iam::************:role/"
  qa:
    "epifi": "arn:aws:iam::************:role/"
    "stockguardian": "arn:aws:iam::************:role/"
  demo:
    "epifi": "arn:aws:iam::************:role/"
  staging:
    "epifi": "arn:aws:iam::************:role/"
  prod:
    "epifi": "arn:aws:iam::************:role/"
    "stockguardian": "arn:aws:iam::************:role/"
  data-prod:
    "epifi": "arn:aws:iam::************:role/"
  data-dev:
    "epifi": "arn:aws:iam::************:role/"
  main:
    "epifi": "arn:aws:iam::240673475153:role/"

ResourceArns:
  "epifi-non-prod-apks":
    "ARN": "epifi:aws:************:s3:epifi-non-prod-apks"
  "epifi-icons":
    "ARN": "epifi:aws:240673475153:s3:epifi-icons"
    "CDN": "https://epifi-icons.pointz.in"
  "epifi-cx":
    "ARN": "epifi:aws:************:s3:epifi-cx"
  "epifi-aml":
    "ARN": "epifi:aws:************:s3:epifi-aml"
  "epifi-dna-dev":
    "ARN": "epifi:aws:************:s3:epifi-dna-dev"
  "epifi-data-services-dev":
    "ARN": "epifi:aws:************:s3:epifi-data-services-dev"
  "epifi-rewards":
    "ARN": "epifi:aws:240673475153:s3:epifi-rewards"
  "epifi-salaryprogram":
    "ARN": "epifi:aws:************:s3:epifi-salaryprogram"
  "epifi-gamma-scripts-deploy":
    "ARN": "epifi:aws:************:s3:epifi-gamma-scripts-deploy"
  "epifi-onboarding-dev":
    "ARN": "epifi:aws:************:s3:epifi-onboarding-dev"

JenkinsUrlDeploy: "https://jenkins-deploy.pointz.in/job/V3_Deployment/job/"
JenkinsUrlDeployBase: "https://jenkins-deploy.pointz.in/"
JenkinsUrlProd: "https://jenkins-prod.pointz.in/job/V3_Deployment/job/"
JenkinsFrontendUrl: "https://jenkins-deploy.pointz.in/job/V3_Deployment/job/"
PCIServicesList: [ "tokenizer", "vendorgateway" ]
Region: "ap-south-1"
ProdAwsAccount: ************
StockGuardianProdAwsAccount: ************
EcsEnvs: [ "data-dev", "deploy" ]
DeployAwsAccount: ************
RoleName: "role_decrypt_deploy"
HostName: "localhost"
Port: 8080
GitOrg1: "epiFi"
GitOrg2: "EpiFi2"
NoOfAmisToShow: 10
JenkinsUsername: "<EMAIL>"

RealmConfigURL: "https://keycloak.pointz.in/realms/InternalNonProd"
ClientID: "jarvis-deploy"
TokenUrl: "https://keycloak.pointz.in/realms/InternalNonProd/protocol/openid-connect/token"
UserInfoUrl: "https://keycloak.pointz.in/realms/InternalNonProd/protocol/openid-connect/userinfo"

MaxPageSize: 3000

UserSegmentBucketLimit: 10
UseAttemptID: false
JarvisManager:
  EnableSelfApproval: true
JarvisFrontend:
  TicketStatusMap:
    - FORM_STATUS_DRAFT: "Draft"
    - FORM_STATUS_WAITING_FOR_APPROVAL: "Waiting for approval"
    - FORM_STATUS_DECLINED: "Declined"
    - FORM_STATUS_APPROVED: "Approved"
    - FORM_STATUS_AUTO_APPROVED: "Auto Approved"
  TicketTypeMap:
    - FORM_TYPE_PROD_S3_DATA_ACCESS_REQUEST: "Prod S3 Data Access Request"
    - FORM_TYPE_AWS_ACCESS_REQUEST: "Aws Access Request"
    - FORM_TYPE_JENKINS_ACCESS_REQUEST: "Jenkins Access Request"
  TicketVersionStatusMap:
    - TICKET_STATUS_DRAFT: "Draft"
    - TICKET_STATUS_WAITING_FOR_APPROVAL: "Waiting for approval"
    - TICKET_STATUS_DECLINED: "Declined"
    - TICKET_STATUS_APPROVED: "Approved"
    - TICKET_STATUS_AUTO_APPROVED: "Auto Approved"
  NewTicketPage:
    PageTitleText:
      Text: "New Ticket"
      TextVariant: 3
  EditTicketPage:
    PageTitleText:
      Text: "Edit Ticket"
      TextVariant: 3
  Ticket:
    FormLabelVariant: 1
    UserManualLink:
      Text: "user Manual to Create, Run and Analyse An Ticket"
      PagePath: ""
    TicketTitleLabel:
      Text: "Ticket Title*"
    TicketTitleTextInput:
      Placeholder: ""
      HintText: ""
      IsRequired: true
      IsEditable: true
    TicketDescriptionTextLabel:
      Text: "Description*"
    TicketDescriptionTextInput:
      Placeholder: ""
      HintText: ""
      IsRequired: true
      IsEditable: true
    TicketBucketNameLabel:
      Text: "Name of s3 bucket*"
    TicketBucketNameDropdownInput:
      Placeholder: ""
      Hint: ""
      IsRequired: true
      IsEditable: true
    TicketRoleNameLabel:
      Text: "Name of role*"
    TicketRoleNameDropdownInput:
      Placeholder: ""
      Hint: ""
      IsRequired: true
      IsEditable: true
    TicketEnvTextLabel:
      Text: "Env*"
    TicketEnvTextInput:
      Placeholder: ""
      HintText: ""
      IsRequired: true
      IsEditable: true
    TicketS3PathTextLabel:
      Text: "S3Path*"
    TicketS3PathTextInput:
      Placeholder: ""
      HintText: ""
      IsRequired: true
      IsEditable: true
    TicketReasonTextLabel:
      Text: "Reason*"
    TicketReasonTextInput:
      Placeholder: ""
      HintText: ""
      IsRequired: true
      IsEditable: true
    TicketFirstNameTextLabel:
      Text: "First Name*"
    TicketFirstNameTextInput:
      Placeholder: ""
      HintText: ""
      IsRequired: true
      IsEditable: true
    TicketLastNameTextLabel:
      Text: "Last Name*"
    TicketLastNameTextInput:
      Placeholder: ""
      HintText: ""
      IsRequired: true
      IsEditable: true
    TicketLabelsLabel:
      Text: "Labels"
      Hint: "Labels to identify the ticket"
    TicketLabelsTagsInput:
      Placeholder: ""
      IsRequired: false
      IsEditable: true
    TicketEmailTextLabel:
      Text: "Email of user requesting data*"
    TicketEmailTextInput:
      Placeholder: ""
      HintText: ""
      IsRequired: true
      IsEditable: true
    TicketCcLabel:
      Text: "Cc"
      Hint: "Cc email ids"
    TicketCcTagsInput:
      Placeholder: ""
      IsRequired: false
      IsEditable: true
    TicketReportingManagerTextLabel:
      Text: "Email of reporting manager of user requesting data*"
    TicketReportingManagerTextInput:
      Placeholder: ""
      HintText: ""
      IsRequired: true
      IsEditable: true
    TicketAreaLabel:
      Text: "BU*"
    TicketAreaDropdownInput:
      Hint: ""
      IsRequired: true
      IsEditable: true
    ApproverEmailLabel:
      Text: "Approver Email"
      Hint: "enter approver emails seperated by comma"
    ApproverEmailTextInput:
      Placeholder: "<EMAIL>, <EMAIL>"
      HintText: ""
      IsRequired: false
      IsEditable: true
    SubmitButton:
      ButtonType: 1
      LeftIconType: 0
      LeftIconUrl: ""
      LeftIconMaterialUiIconName: 0
      RightIconType: 0
      RightIconUrl: ""
      RightIconMaterialUiIconName: 0
      Text: "Submit"
      IsEnabled: true

  HomePage:
    HelloText:
      Text: "Hello"
      TextVariant: 2
    UserManual:
      Text: "User Manual to Create, run and analyse an ticket"
      TextVariant: 2
      PagePath: "LINK"
    MyTicketTitle:
      Prefix: "My Tickets ("
      Suffix: "tickets running)"
      TextVariant: 2
    AllTicketTitle:
      Prefix: "All Tickets ("
      Suffix: "tickets running)"
      TextVariant: 2
    TableHeader: ["Ticket", "Ticket Type", "Status", "BU", "Action" ]
    EditRequestText:
      ReviewText: "Review"
      EditText: "Edit"
      NoPendingReviewText: "No Pending Request"
      ProcessedText: "Processed"
    DefaultRowElementsTextVariant: 2
    AllMyTicketBtn:
      Text: "All My Tickets"
      IsEnabled: true
      FunctionName: "GetTicket"
    AllTicketBtn:
      Text: "All Tickets"
      IsEnabled: true
      FunctionName: "GetTicket"
    NewTicketBtn:
      Text: "New Ticket"
      IsEnabled: true
      FunctionName: "CreateNewTicket"

  ListTicketsPage:
    MyTicketTitle:
      Prefix: "My Tickets ("
      Suffix: "tickets running)"
      TextVariant: 3
    AllTicketTitle:
      Prefix: "All Tickets ("
      Suffix: "tickets running)"
      TextVariant: 3
    SearchFilterInput:
      PlaceHolder: "Search By Tags or Ticket Name or Variable. Example - Tag: Search bar, AskFi result page"
      IsRequired: true
      IsEditable: true
    # UserManual:
    #   Text: "User Manual to Create, run and analyse an ticket"
    #   TextVariant: 2
    #   PagePath: "LINK"
    TableHeader: [ "Ticket", "Ticket Type", "Status", "BU", "Action" ]
    EditRequestText:
      ReviewText: "Review"
      EditText: "Edit"
      NoPendingReviewText: "No Pending Request"
      ProcessedText: "Processed"
    DefaultRowElementsTextVariant: 2
    NewTicketBtn:
      Text: "New Ticket"
      IsEnabled: true
      FunctionName: "CreateNewTicket"
    SubmitBtn:
      Text: "Submit"
      IsEnabled: true
      FunctionName: "ListTicketsSubmit"
    ClearBtn:
      Text: "Clear"
      IsEnabled: true
      FunctionName: "ListTicketsClear"
  GetTicket:
    RedirectionPath: "/jarvis/ticket/view"
    RedirectionTicketNameQueryParam: "name"
    RedirectionTicketVersionIdQueryParam: "ticketVersId"
    TicketDetails:
      Title: "Ticket Name"
      Description: "Ticket Description"
      ApproverRole: "Role of Approver Required"
      TicketType: "Type of Ticket"
      Bucket: "Bucket Name"
      Path: "S3Path"
      Role: "Role"
      Env: "Env"
      Reason: "Reason"
      FirstName: "First Name"
      LastName: "Last Name"
      Labels: "Ticket Labels"
      Email: "Email of requesting user"
      Cc: "Cc"
      ReportingManager: "Reporting Manager"
      BU: "BU"
      RequestedReviewers: "Requested Reviewers"
      CreatedBy: "Created By"
      LastUpdatedBy: "Last Updated By"
      DeletedBy: "Deleted By"
      ProcessedBy: "Processed By"
      CreatedAt: "Created At"
      DeletedAt: "Deleted At"
      ProcessedAt: "Processed At"
      UpdatedAt: "Last Updated At"
      Status: "Ticket Status"
      Comments:           "Comments"
      DefaultTextVariant: 2
    VersioningTable:
      Headers:
        - "Version"
        - "Status"
        - "Edited By"
        - "Processed By"
      DefaultTextVariant: 2
    EditBtn:
      Text: "Edit"
      FunctionName: "edit"
      IsEnabled: true
    EventMetricsTables:
      Headers:
        - ""
        - "Impressions"
        - "No Event Specified"
        - "% change based on control group"
    ApproveBtn:
      Text: "Approve"
      FunctionName: "approve"
      IsEnabled: true
    ApproveAndRunBtn:
      Text: "Approve & Run"
      FunctionName: "approve"
      IsEnabled: true
    RejectBtn:
      Text: "Reject"
      FunctionName: "reject"
      IsEnabled: true

  TicketStatus:
    Enabled: "Running"
    Disabled: "Paused"
    Unspecified: "Not Running"
    Closed: "Complete/Closed"
    NotApproved: "Not Approved"

TenantEC2TagManagerRoleArnMap:
    "epifi": "arn:aws:iam::************:role/EC2TagManager"
    "stockguardian": "arn:aws:iam::************:role/EC2TagManager"

S3Expiration:
  Env:
    deploy: "2h"
    prod: "1h"
  Default: "10m"
  MaxLimit: "24h"

PlatformS3Bucket: "epifi-deploy-platform"
UserMappingFilePath: "user_mapping.json"
GithubWebhookConfig:
  SkipNotificationLabels:
    - "Slackbot"
  ProjectToNotificationsMap:
    "Approval Requests":
      "BlacklistedUsers":
        - "<EMAIL>"
