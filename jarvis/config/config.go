package config

//go:generate conf_gen github.com/epifi/gamma/jarvis/config Config

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"sync"
	"time"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/keycloak"

	testtenantep "github.com/epifi/gamma/testing/test_tenant/endpoint"
)

const (
	// TLS
	TlsCert             = "tlscert"
	TlsKey              = "tlskey"
	JenkinsToken        = "JenkinsToken"
	JenkinsTokenNonProd = "JenkinsTokenNonProd"
	GithubToken         = "GithubToken"
	KubeConfig          = "KubeConfig"
	SlackToken          = "SlackToken"
)

var (
	once   sync.Once
	config *Config
	err    error
)

var (
	_, b, _, _ = runtime.Caller(0)
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig()
	})

	if err != nil {
		return nil, err
	}
	return config, err
}

func loadConfig() (*Config, error) {
	// will be used only for test environment
	configDirPath := testEnvConfigDir()
	k, _, err := cfg.LoadConfigUsingKoanf(configDirPath, cfg.JARVIS_GRPC_SERVICE)
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}
	conf := &Config{}
	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}
	err = cfg.LoadAllSecretsV3(conf, conf.Application.Environment, conf.AWS.Region)
	if err != nil {
		return nil, err
	}
	keyToSecret, err := cfg.LoadSecrets(conf.Secrets.Ids, conf.Application.Environment, conf.AWS.Region)
	if err != nil {
		return nil, err
	}
	if err := updateDefaultConfig(conf, keyToSecret); err != nil {
		return nil, err
	}
	if cfg.IsTestTenantEnabled() {
		err := testtenantep.UpdateLocalhostEndpoint(&conf)
		if err != nil {
			return nil, err
		}
	}
	return conf, nil
}

// update the env variable
// update the default secret keys in the config with the secret values fetched from Secret manager
func updateDefaultConfig(c *Config, keyToSecret map[string]string) error {

	if err := readAndSetEnv(c); err != nil {
		return fmt.Errorf("failed to read and set env var: %w", err)
	}

	for k, v := range keyToSecret {
		if c.Secrets != nil {
			c.Secrets.Ids[k] = v
		}
	}
	return nil
}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config) error {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Name = val
	}

	if val, ok := os.LookupEnv("SERVER_PORT"); ok {
		intVal, err := strconv.Atoi(val)
		if err != nil {
			return fmt.Errorf("failed to convert port to int: %w", err)
		}

		c.Server.Port = intVal
	}

	return nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..", "values")
	return configPath
}

//go:generate conf_gen github.com/epifi/gamma/jarvis/config Config
type Config struct {
	Application     *Application
	Server          *Server
	Logging         *cfg.Logging
	ServerEndpoints *cfg.ServerEndpoints
	Secrets         *cfg.Secrets
	ServiceRepoMap  map[string]*Repository
	// NonGolangEc2Services is a list of services that are not written in golang but are deployed on ec2.
	NonGolangEc2Services        []string
	Ec2ServiceList              map[string][]string
	EnvK8sServiceMap            map[string][]string
	ClusterArnMap               map[string]string
	EnvTenantRoleArns           map[string]map[string]string
	ResourceArns                map[string]*S3Bucket
	S3Expiration                *S3Expiration
	PCIServicesList             []string
	JenkinsUrlDeploy            string
	JenkinsUrlDeployBase        string
	JenkinsUrlProd              string
	Region                      string
	ProdAwsAccount              int
	StockGuardianProdAwsAccount int
	EcsEnvs                     []string
	DeployAwsAccount            int
	RoleName                    string
	HostName                    string
	Port                        int
	GitOrg1                     string
	GitOrg2                     string
	NoOfAmisToShow              int

	JenkinsUrl             string
	JenkinsUrlBase         string
	JenkinsFrontendUrl     string
	JenkinsFrontendUrlBase string
	AwsAccount             string
	Envs                   []string
	// Stores the mapping between the aws account name and the corresponding
	// backend application environment which are deployed in that account.
	AwsAccountsToEnvMap  map[string]*EnvMetadata
	QaFiCapitalJobSuffix string

	JenkinsUsername string

	AWS *Aws

	RealmConfigURL                string
	ClientID                      string
	TokenUrl                      string
	UserInfoUrl                   string
	MaxPageSize                   uint32
	MinControlGroupRollOut        int32
	JarvisManager                 *JarvisManager
	JarvisFrontend                *JarvisFrontend
	UserSegmentBucketLimit        int
	UseAttemptID                  bool
	TenantEC2TagManagerRoleArnMap map[string]string
	Auth                          *Auth
	PlatformS3Bucket              string
	GithubApp                     *GithubApp
	UserMappingFilePath           string
	GithubWebhookConfig           *GithubWebhookConfig `dynamic:"true"`
}

type GithubApp struct {
	AppId          string `field:"GithubSecret" jsonPath:"appId"`
	InstallationId string `field:"GithubSecret" jsonPath:"installationId"`
	PrivateKey     string `field:"GithubSecret" jsonPath:"privateKey" valueType:"cert"`
	WebhookSecret  string `field:"GithubSecret" jsonPath:"webhookSecret"`
	GithubSecret   string `iam:"sm-read"`
}
type GithubWebhookConfig struct {
	ProjectToNotificationsMap        map[string]*ProjectToNotificationsMap
	SkipNotificationLabels           []string // Labels for which notification should be skipped
	BedrockModelId                   string
	AnthropicVersion                 string
	RelevantIssueConfidenceThreshold float64
	EnableAutoIssueLinking           bool     `dynamic:"true"` // to enable/disable auto linking related git issue tickets in PRs automatically
	WhitelistedUsers                 []string `dynamic:"true"` // If not empty, only these users will have auto issue linking enabled
	BlacklistedUsers                 []string `dynamic:"true"` // If WhitelistedUsers is empty, all users except these will have auto issue linking enabled
}
type ProjectToNotificationsMap struct {
	BlacklistedUsers []string // List of user emails who don't want notifications for this project
}

type Auth struct {
	Keycloak *keycloak.Config
	// Access token set by Jarvis will be consumed by other services like Quest, Feedback engine. Hence base domain is computed from
	// ApplicationURL of Keycloak config
	AccessTokenCookieDomain string
}

type Repository struct {
	Org  string
	Name string
}

type S3Bucket struct {
	ARN string
	CDN string
	// BucketPathRegexToResourceMappings is a list of regex to role mappings for the bucket.
	// First regex match is used to determine the role.
	BucketPathRegexToResourceMappings []*BucketPathRegexToRoleMappings
}

type BucketPathRegexToRoleMappings struct {
	PathRegex string
	Resource  string
}

type S3Expiration struct {
	Env      map[string]time.Duration
	Default  time.Duration
	MaxLimit time.Duration
}

type EnvMetadata struct {
	Tenant string
	Env    string
}

type Application struct {
	Environment string
	Name        string
	Namespace   string
}

type Server struct {
	Port             int
	HealthCheckPort  int
	UnsecureHttpPort int
	MetricPort       int
}

type Aws struct {
	Region string
}

type Flags struct {
	// A flag to determine if the debug message in Status is to be trimmed
	TrimDebugMessageFromStatus bool
}

type JarvisManager struct {
	RedisLockDuration  time.Duration
	TotalUsersFi       int32
	DisplayMetrics     bool
	UsePresto          bool
	ValidateEvents     bool
	EnableSelfApproval bool
}

type JarvisFrontend struct {
	NewTicketPage   *TicketPage
	EditTicketPage  *TicketPage
	Ticket          *Ticket
	HomePage        *HomePage
	ListTicketsPage *ListTicketsPage
	GetTicket       *GetTicket
	// map of proto enum string to display string
	TicketTypeMap          map[string]string
	TicketStatusMap        map[string]string
	TicketVersionStatusMap map[string]string
}

type TicketPage struct {
	PageTitleText *Text
}

type Text struct {
	Text        string
	TextVariant int
}

type Link struct {
	Text     string
	PagePath string
}

type Label struct {
	Text string
	Hint string
}

type TextInput struct {
	Placeholder string
	HintText    string
	IsRequired  bool
	IsEditable  bool
	Value       string
}

type DropdownInput struct {
	Hint       string
	IsRequired bool
	IsEditable bool
}

type CheckboxInput struct {
	Text       string
	Hint       string
	Value      bool
	IsEditable bool
}

type TagsInput struct {
	Placeholder string
	IsRequired  bool
	IsEditable  bool
}

type MultiSelectInput struct {
	IsRequired bool
	IsEditable bool
}

type MinMaxInput struct {
	MinLabel       string
	MaxLabel       string
	MinPlaceholder string
	MaxPlaceholder string
	IsRequired     bool
	IsEditable     bool
}

type Mark struct {
	Value int64
	Label string
}

type ProgressBarInput struct {
	Label             string
	LabelHint         string
	FilledLabel       string
	FilledLabelHint   string
	UnfilledLabel     string
	UnfilledLabelHint string
	Marks             []*Mark
	IsRequired        bool
	IsEditable        bool
}

type Button struct {
	ButtonType                  int
	LeftIconType                int
	LeftIconUrl                 string
	LeftIconMaterialUiIconName  int
	RightIconType               int
	RightIconUrl                string
	RightIconMaterialUiIconName int
	Text                        string
	IsEnabled                   bool
	FunctionName                string
}

type Ticket struct {
	TicketLabelVariant              int32
	UserManualLink                  *Link
	TicketTitleLabel                *Label
	TicketTitleTextInput            *TextInput
	TicketDescriptionTextLabel      *Label
	TicketDescriptionTextInput      *TextInput
	TicketBucketNameLabel           *Label
	TicketBucketNameDropdownInput   *DropdownInput
	TicketRoleNameLabel             *Label
	TicketRoleNameDropdownInput     *DropdownInput
	TicketEnvTextLabel              *Label
	TicketEnvTextInput              *TextInput
	TicketS3PathTextLabel           *Label
	TicketS3PathTextInput           *TextInput
	TicketReasonTextLabel           *Label
	TicketReasonTextInput           *TextInput
	TicketFirstNameTextLabel        *Label
	TicketFirstNameTextInput        *TextInput
	TicketLastNameTextLabel         *Label
	TicketLastNameTextInput         *TextInput
	TicketLabelsLabel               *Label
	TicketLabelsTagsInput           *TagsInput
	TicketEmailTextLabel            *Label
	TicketEmailTextInput            *TextInput
	TicketCcLabel                   *Label
	TicketCcTagsInput               *TagsInput
	TicketReportingManagerTextLabel *Label
	TicketReportingManagerTextInput *TextInput
	TicketAreaLabel                 *Label
	TicketAreaDropdownInput         *DropdownInput

	ApproverEmailLabel     *Label
	ApproverEmailTextInput *TextInput
	SubmitButton           *Button
}

type HomePage struct {
	HelloText                     *Text
	UserManual                    *UserManual
	MyTicketTitle                 *TicketTitle
	AllTicketTitle                *TicketTitle
	TableHeader                   []string
	DefaultRowElementsTextVariant int
	AllMyTicketBtn                *Button
	AllTicketBtn                  *Button
	NewTicketBtn                  *Button
	EditRequestText               *EditRequestText
}

type ListTicketsPage struct {
	MyTicketTitle                 *TicketTitle
	AllTicketTitle                *TicketTitle
	SearchFilterInput             *TagsInput
	TableHeader                   []string
	DefaultRowElementsTextVariant int
	NewTicketBtn                  *Button
	EditRequestText               *EditRequestText
	SubmitBtn                     *Button
	ClearBtn                      *Button
}

type GetTicket struct {
	RedirectionPath                      string
	RedirectionTicketNameQueryParam      string
	RedirectionTicketVersionIdQueryParam string
	TicketDetails                        *TicketDetails
	VersioningTable                      *Table
	EditBtn                              *Button
	ApproveBtn                           *Button
	RejectBtn                            *Button
}

type Table struct {
	Headers            []string
	DefaultTextVariant int
}

type Tables struct {
	Headers []string
}

type TicketDetails struct {
	Title              string
	Description        string
	Status             string
	ApproverRole       string
	TicketType         string
	Bucket             string
	Path               string
	Role               string
	Env                string
	Reason             string
	FirstName          string
	LastName           string
	Labels             string
	Email              string
	Cc                 string
	ReportingManager   string
	BU                 string
	RequestedReviewers string
	CreatedBy          string
	LastUpdatedBy      string
	DeletedBy          string
	ProcessedBy        string
	CreatedAt          string
	UpdatedAt          string
	DeletedAt          string
	ProcessedAt        string
	Comments           string
	DefaultTextVariant int
}

type AppVersionLabel struct {
	Text            string
	MinLabel        string
	MaxLabel        string
	UserGroupsLabel string
}

type UserManual struct {
	Text        string
	TextVariant int
	PagePath    string
}

type TicketTitle struct {
	Prefix      string
	Suffix      string
	TextVariant int
}

type EditRequestText struct {
	ReviewText          string
	EditText            string
	NoPendingReviewText string
	ProcessedText       string
}
