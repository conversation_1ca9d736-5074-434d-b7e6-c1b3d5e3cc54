package client

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/bradleyfalzon/ghinstallation/v2"
	gh "github.com/google/go-github/v53/github"

	jarvisconf "github.com/epifi/gamma/jarvis/config"
)

func NewGithubClient(conf *jarvisconf.Config) (*gh.Client, error) {
	appID, err := strconv.ParseInt(conf.GithubApp.AppId, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("failed to parse github app id: %w", err)
	}
	installationID, err := strconv.ParseInt(conf.GithubApp.InstallationId, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("failed to parse github installation id: %w", err)
	}

	tr := http.DefaultTransport
	itr, err := ghinstallation.New(tr, appID, installationID, []byte(conf.GithubApp.PrivateKey))
	if err != nil {
		return nil, fmt.Errorf("failed to create github installation transport: %w", err)
	}

	return gh.NewClient(&http.Client{Transport: itr}), nil
}
