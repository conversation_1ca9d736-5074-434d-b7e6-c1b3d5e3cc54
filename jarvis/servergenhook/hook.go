// nolint
package servergenhook

import (
	"context"
	"fmt"
	"net/http"
	"path/filepath"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/epifi/be-common/pkg/cfg/dynconf"
	"github.com/gorilla/mux"
	"github.com/pkg/errors"
	"github.com/rs/cors"
	"go.uber.org/zap"
	"golang.org/x/sync/errgroup"

	"github.com/epifi/be-common/pkg/cfg"
	cmdConf "github.com/epifi/be-common/pkg/cmd/config"
	cmdGenConf "github.com/epifi/be-common/pkg/cmd/config/genconf"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epifiserver"
	epifitemporalClient "github.com/epifi/be-common/pkg/epifitemporal/client"
	"github.com/epifi/be-common/pkg/logger"
	servergenconf "github.com/epifi/be-common/tools/servergen/config"

	"github.com/epifi/gamma/jarvis"
	"github.com/epifi/gamma/jarvis/auth"
	"github.com/epifi/gamma/jarvis/config"
	"github.com/epifi/gamma/jarvis/wire"
	types2 "github.com/epifi/gamma/jarvis/wire/types"
	yodaconf "github.com/epifi/gamma/platform/yoda/config"
	genconf2 "github.com/epifi/gamma/platform/yoda/config/genconf"
	yodawire "github.com/epifi/gamma/platform/yoda/wire"
)

// Middleware func type for chaining middleware
type Middleware func(http.HandlerFunc) http.HandlerFunc

func InitJarvisServer(
	ctx context.Context,
	jarvisMux *http.ServeMux,
	gconf *cmdGenConf.Config,
	awsConf aws.Config,
	jarvisPgdb types.JarvisPGDB,
	initNotifier chan<- cfg.ServerName) (func(), error) {

	cleanupFn := func() {}

	// Load static configuration
	conf, err := config.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err))
		return nil, err
	}

	temporalClient, err := epifitemporalClient.NewWorkflowClient(
		conf.Application.Namespace,
		false,
		gconf.Secrets().Ids[cmdConf.TemporalCodecAesKey],
	)
	if err != nil {
		logger.Error(ctx, "unable to create client", zap.Error(err))
		return nil, err
	}

	defer temporalClient.Close()

	g, grpCtx := errgroup.WithContext(context.Background())

	envServergenConfig := make(map[string]*servergenconf.Config)
	for _, env := range conf.Envs {
		confDir, err2 := cfg.GetConfigDir()
		if err2 != nil {
			return nil, errors.Wrap(err2, "failed to get config dir")
		}
		serverDefFilePath := filepath.Join(confDir, fmt.Sprintf(servergenconf.ServerDefConfigFile, env))
		defconf, err2 := servergenconf.Load(serverDefFilePath)
		if err2 != nil {
			logger.Error(ctx, "failed to load server definition config", zap.Error(err2))
			return nil, err2
		}
		envServergenConfig[env] = defconf
	}
	authService, err := auth.NewAuthService(ctx, conf)
	if err != nil {
		logger.Error(ctx, "failed to create auth service", zap.Error(err))
		return nil, err
	}
	jarvisSvc := wire.InitialiseJarvisService(conf, envServergenConfig)

	aws := wire.InitialiseAwsService(awsConf, conf, envServergenConfig)
	k8s := wire.InitialiseK8sService(awsConf, conf, envServergenConfig)
	jenkins := wire.InitialiseJenkinsService(conf, envServergenConfig)
	jarvisS3, err := wire.InitialiseJarvisS3Service(awsConf, conf)
	if err != nil {
		logger.Error(ctx, "Error while initializing jarvis s3 service", zap.Error(err))
		return nil, err
	}
	jarvisSecret := wire.InitialiseJarvisSecretService(conf)
	jarvisSso := wire.InitialiseJarvisSsoService(conf)
	consul := wire.InitialiseConsulService(conf)
	edith, err := wire.InitialiseEdithService(conf, jarvisPgdb)
	if err != nil {
		logger.Error(ctx, "Error while initializing edith service", zap.Error(err))
		return nil, err
	}
	frontend, err := wire.InitialiseFrontendService(conf, edith, &temporalClient)
	if err != nil {
		logger.Error(ctx, "Error while initializing frontend service", zap.Error(err))
		return nil, err
	}
	prDeploymentService := wire.InitialisePrDeploymentService(conf, temporalClient, awsConf, types2.SlackToken(conf.Secrets.Ids[config.SlackToken]))

	r := mux.NewRouter()

	// Define our middleware stack. These run in the order given
	stack := []jarvis.Middleware{
		PanicRecoveryHandler,
		jarvisSvc.MetricMiddleware,
		jarvisSvc.LogMiddleware,
		authService.AuthMiddleware,
	}

	jarvisMux.Handle("/", r)
	r.HandleFunc("/healthcheck", jarvisSvc.HealthCheck)

	// Register webhook endpoint
	webhookHandler, err := wire.InitialiseGithubWebhookService(ctx, conf, types2.SlackToken(conf.Secrets.Ids[config.SlackToken]), awsConf)
	if err != nil {
		logger.Error(ctx, "failed to initialize GitHub webhook service", zap.Error(err))
		return nil, errors.Wrap(err, "failed to initialize GitHub webhook service")
	}
	webhookSubrouter := r.PathPrefix("/webhook").Subrouter()
	webhookSubrouter.HandleFunc("/github", jarvisSvc.CompileMiddleware(webhookHandler.GitHubWebhookHandler, []jarvis.Middleware{PanicRecoveryHandler, jarvisSvc.MetricMiddleware, jarvisSvc.LogMiddleware}))

	appSubrouter := r.PathPrefix("/app").Subrouter()
	appSubrouter.HandleFunc("/applist", jarvisSvc.CompileMiddleware(jarvisSvc.GetAppList, stack))
	appSubrouter.HandleFunc("/ec2applist", jarvisSvc.CompileMiddleware(jarvisSvc.GetEc2AppList, stack))

	awsSubrouter := r.PathPrefix("/aws").Subrouter()
	awsSubrouter.HandleFunc("/checkaws", jarvisSvc.CompileMiddleware(aws.CheckAws, stack))
	awsSubrouter.HandleFunc("/getami", jarvisSvc.CompileMiddleware(aws.GetAwsAmis, stack))
	awsSubrouter.HandleFunc("/getamibytag", jarvisSvc.CompileMiddleware(aws.GetAwsAmisByTag, stack))
	awsSubrouter.HandleFunc("/getasgdetails", jarvisSvc.CompileMiddleware(aws.GetAsgDetails, stack))
	awsSubrouter.HandleFunc("/sharetoprod", jarvisSvc.CompileMiddleware(aws.ShareImageToProd, stack))
	awsSubrouter.HandleFunc("/sharetoprodv2", jarvisSvc.CompileMiddleware(aws.ShareImageToProdV2, stack))
	awsSubrouter.HandleFunc("/getdeployedami", jarvisSvc.CompileMiddleware(aws.GetDeployedAmi, stack))

	k8sSubrouter := r.PathPrefix("/k8s").Subrouter()
	k8sSubrouter.HandleFunc("/checkk8s", jarvisSvc.CompileMiddleware(k8s.CheckK8s, stack))
	k8sSubrouter.HandleFunc("/podslist", jarvisSvc.CompileMiddleware(k8s.PodsList, stack))
	k8sSubrouter.HandleFunc("/pods", jarvisSvc.CompileMiddleware(k8s.PodsProgress, stack))
	k8sSubrouter.HandleFunc("/poddetails", jarvisSvc.CompileMiddleware(k8s.PodDetails, stack))
	k8sSubrouter.HandleFunc("/images", jarvisSvc.CompileMiddleware(k8s.GetK8sImages, stack))
	k8sSubrouter.HandleFunc("/imagesbytag", jarvisSvc.CompileMiddleware(k8s.GetK8sImagesByTag, stack))

	jenkinsSubrouter := r.PathPrefix("/jenkins").Subrouter()
	jenkinsSubrouter.HandleFunc("/checkjenkins", jarvisSvc.CompileMiddleware(jenkins.CheckJenkins, stack))
	jenkinsSubrouter.HandleFunc("/progress", jarvisSvc.CompileMiddleware(jenkins.JenkinsJobProgress, stack))
	jenkinsSubrouter.HandleFunc("/status", jarvisSvc.CompileMiddleware(jenkins.JenkinsJobStatus, stack))
	jenkinsSubrouter.HandleFunc("/swap", jarvisSvc.CompileMiddleware(jenkins.JenkinsJobProgressSwap, stack))
	jenkinsSubrouter.HandleFunc("/logs", jarvisSvc.CompileMiddleware(jenkins.JenkinsJobLogs, stack))

	s3Subrouter := r.PathPrefix("/s3").Subrouter()
	s3Subrouter.HandleFunc("/checks3", jarvisSvc.CompileMiddleware(jarvisS3.CheckJarvisS3, stack))
	s3Subrouter.HandleFunc("/list", jarvisSvc.CompileMiddleware(jarvisS3.ListObjects, stack))
	s3Subrouter.HandleFunc("/generate_url", jarvisSvc.CompileMiddleware(jarvisS3.GeneratePresigned, stack))
	s3Subrouter.HandleFunc("/get_object_url", jarvisSvc.CompileMiddleware(jarvisS3.GetObjectUrlHandler, stack))

	consulSubrouter := r.PathPrefix("/consul").Subrouter()
	consulSubrouter.HandleFunc("/checkconsul", jarvisSvc.CompileMiddleware(consul.CheckConsul, stack))
	consulSubrouter.HandleFunc("/activecolor", jarvisSvc.CompileMiddleware(consul.ActiveColor, stack))

	secretSubrouter := r.PathPrefix("/secret").Subrouter()
	secretSubrouter.HandleFunc("/checksecret", jarvisSvc.CompileMiddleware(jarvisSecret.CheckJarvisSecret, stack))

	ssoSubrouter := r.PathPrefix("/sso").Subrouter()
	ssoSubrouter.HandleFunc("/checksso", jarvisSvc.CompileMiddleware(jarvisSso.CheckJarvisSso, stack))
	ssoSubrouter.HandleFunc("/test", jarvisSvc.CompileMiddleware(jarvisSso.TestFunction, stack))

	edithSubrouter := r.PathPrefix("/edith").Subrouter()
	edithSubrouter.HandleFunc("/checkedith", jarvisSvc.CompileMiddleware(edith.CheckEdith, stack))

	frontendSubrouter := r.PathPrefix("/frontend").Subrouter()
	frontendSubrouter.HandleFunc("/checkfrontend", jarvisSvc.CompileMiddleware(frontend.CheckFrontend, stack))
	frontendSubrouter.HandleFunc("/generatetest", jarvisSvc.CompileMiddleware(frontend.GenerateTestProto, stack))
	frontendSubrouter.HandleFunc("/listticketspage", jarvisSvc.CompileMiddleware(frontend.ListTicketsPage, stack))
	frontendSubrouter.HandleFunc("/getticketpage", jarvisSvc.CompileMiddleware(frontend.GetTicket, stack))
	frontendSubrouter.HandleFunc("/newticketpage", jarvisSvc.CompileMiddleware(frontend.NewTicketPage, stack))
	frontendSubrouter.HandleFunc("/createnewticket", jarvisSvc.CompileMiddleware(frontend.CreateNewTicket, stack))
	frontendSubrouter.HandleFunc("/editticketpage", jarvisSvc.CompileMiddleware(frontend.EditTicketPage, stack))
	frontendSubrouter.HandleFunc("/editticket", jarvisSvc.CompileMiddleware(frontend.EditTicket, stack))
	frontendSubrouter.HandleFunc("/updateticket", jarvisSvc.CompileMiddleware(frontend.UpdateTicketStatus, stack))

	frontendSubrouter.HandleFunc("/createnewticketv2", jarvisSvc.CompileMiddleware(frontend.CreateNewTicketV2, stack))
	frontendSubrouter.HandleFunc("/editticketv2", jarvisSvc.CompileMiddleware(frontend.EditTicketV2, stack))
	frontendSubrouter.HandleFunc("/updateticketv2", jarvisSvc.CompileMiddleware(frontend.UpdateTicketStatusV2, stack))

	// PR Deployment routes
	prDeploymentSubrouter := r.PathPrefix("/prdeployment").Subrouter()
	prDeploymentSubrouter.HandleFunc("/startworkflow", jarvisSvc.CompileMiddleware(prDeploymentService.StartPrDeploymentWorkflow, stack))
	prDeploymentSubrouter.HandleFunc("/stopworkflow", jarvisSvc.CompileMiddleware(prDeploymentService.StopPrDeploymentWorkflow, stack))
	prDeploymentSubrouter.HandleFunc("/getworkflowstatus", jarvisSvc.CompileMiddleware(prDeploymentService.GetPrDeploymentWorkflowStatus, stack))
	prDeploymentSubrouter.HandleFunc("/restartworkflow", jarvisSvc.CompileMiddleware(prDeploymentService.RestartPrDeploymentWorkflow, stack))
	prDeploymentSubrouter.HandleFunc("/signalworkflow", jarvisSvc.CompileMiddleware(prDeploymentService.HandleSignalWorkflow, stack))

	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "failed to get environment", zap.Error(err))
		return nil, errors.Wrap(err, "failed to get environment")
	}
	if env == cfg.DeployEnv {
		err = exposeYodaApis(grpCtx, r, awsConf)
		if err != nil {
			logger.Error(ctx, "failed to expose Yoda APIs", zap.Error(err))
			return nil, errors.Wrap(err, "failed to expose Yoda APIs")
		}
	}
	// Auth routes
	authSubrouter := r.PathPrefix("/auth").Subrouter()
	authSubrouter.HandleFunc("", authService.HandleAuth)
	authSubrouter.HandleFunc("/callback", authService.HandleCallback)
	authSubrouter.HandleFunc("/logout", authService.LogoutHandler)

	corsHandler := cors.New(gconf.HttpCorsOptions().Options()).Handler(jarvisMux)

	// Start http server
	unsecureHttpServer := epifiserver.NewHttpServer(conf.Server.Port, corsHandler)
	unsecureHttpServerClosure := epifiserver.StartHttpServer(grpCtx, g, unsecureHttpServer)
	healthCheckClosure := func() {}

	newMux := http.NewServeMux()
	metricsServer := epifiserver.NewHttpServer(conf.Server.MetricPort, newMux)
	epifiserver.RegisterMonitoringEndpoint(newMux)
	metricsServerClosure := epifiserver.StartHttpServer(grpCtx, g, metricsServer)
	initNotifier <- gconf.Name()
	// skip http servers since all the use-cases with this is not required for TEST_TENANT setup where
	// all servers are hosted in the same instance for short time for testing purpose.
	if !cfg.IsTestTenantEnabled() {
		// instantiate health check server
		healthCheckServer := epifiserver.NewHttpServer(conf.Server.HealthCheckPort, http.DefaultServeMux)
		epifiserver.RegisterHealthCheckEndpoint(http.DefaultServeMux, conf.Application.Name)
		healthCheckClosure = epifiserver.StartHttpServer(grpCtx, g, healthCheckServer)

	}
	// block till we get sig term or one of server crashes
	epifiserver.HandleGracefulShutdown(grpCtx, g, healthCheckClosure, unsecureHttpServerClosure, metricsServerClosure)
	return cleanupFn, nil
}

func PanicRecoveryHandler(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, req *http.Request) {
		defer func() {
			if r := recover(); r != nil {
				logger.Error(req.Context(), "Panic recovered", zap.Any("recovered", r))
				http.Error(w, "Internal Server Error", http.StatusInternalServerError)
			}
		}()
		next(w, req)
	}
}

func exposeYodaApis(ctx context.Context, r *mux.Router, awsConf aws.Config) error {
	yodaGenConf, err := dynconf.LoadConfig(yodaconf.Load, genconf2.NewConfig, cfg.YODA_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.YODA_SERVICE))
		return err
	}

	yodaChatServer, err := yodawire.InitializeYodaChatServer(ctx, yodaGenConf, awsConf)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}
	// Exposing Yoda APIs via Jarvis to enable Keycloak-based authentication control
	yodaSubrouter := r.PathPrefix("/yoda").Subrouter()
	yodaSubrouter.HandleFunc("/chat", yodaChatServer.ChatHandler)
	yodaSubrouter.HandleFunc("/chat/stream", yodaChatServer.ChatStreamHandler)
	return nil
}
