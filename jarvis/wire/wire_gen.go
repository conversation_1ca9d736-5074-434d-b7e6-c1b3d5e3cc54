// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"context"
	"fmt"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/bedrockruntime"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cmd/types"
	github2 "github.com/epifi/be-common/pkg/github"
	"github.com/epifi/be-common/pkg/storage/v2"
	config2 "github.com/epifi/be-common/tools/servergen/config"
	"github.com/epifi/gamma/jarvis"
	"github.com/epifi/gamma/jarvis/activity"
	"github.com/epifi/gamma/jarvis/aws"
	client2 "github.com/epifi/gamma/jarvis/client"
	"github.com/epifi/gamma/jarvis/config"
	"github.com/epifi/gamma/jarvis/config/genconf"
	"github.com/epifi/gamma/jarvis/config/worker"
	"github.com/epifi/gamma/jarvis/consul"
	"github.com/epifi/gamma/jarvis/dao/approver_list"
	"github.com/epifi/gamma/jarvis/dao/form"
	"github.com/epifi/gamma/jarvis/dao/ticket"
	"github.com/epifi/gamma/jarvis/edith"
	"github.com/epifi/gamma/jarvis/frontend"
	"github.com/epifi/gamma/jarvis/jenkins"
	"github.com/epifi/gamma/jarvis/k8s"
	"github.com/epifi/gamma/jarvis/prdeployment"
	"github.com/epifi/gamma/jarvis/s3"
	"github.com/epifi/gamma/jarvis/secret"
	"github.com/epifi/gamma/jarvis/sso"
	"github.com/epifi/gamma/jarvis/webhook/github"
	"github.com/epifi/gamma/jarvis/webhook/github/eventhandler"
	types2 "github.com/epifi/gamma/jarvis/wire/types"
	"github.com/slack-go/slack"
	"go.temporal.io/sdk/client"
	"path/filepath"
)

// Injectors from wire.go:

func InitialiseJarvisService(conf *config.Config, envServergenConfig map[string]*config2.Config) *jarvis.JarvisService {
	jarvisService := jarvis.NewJarvisService(conf, envServergenConfig)
	return jarvisService
}

func InitialiseAwsService(awsConfig aws.Config, conf *config.Config, envServergenConfig map[string]*config2.Config) *jarvisAws.AwsService {
	awsService := jarvisAws.NewAwsService(awsConfig, conf, envServergenConfig)
	return awsService
}

func InitialiseK8sService(awsConfig aws.Config, conf *config.Config, envServergenConfig map[string]*config2.Config) *k8s.K8sService {
	k8sService := k8s.NewK8sService(awsConfig, conf, envServergenConfig)
	return k8sService
}

func InitialiseJenkinsService(conf *config.Config, envServergenConfig map[string]*config2.Config) *jenkins.JenkinsService {
	jenkinsService := jenkins.NewJenkinsService(conf, envServergenConfig)
	return jenkinsService
}

func InitialiseJarvisS3Service(awsConfig aws.Config, conf *config.Config) (*jarvisS3.JarvisS3Service, error) {
	jarvisS3Service, err := jarvisS3.NewJarvisS3Service(awsConfig, conf)
	if err != nil {
		return nil, err
	}
	return jarvisS3Service, nil
}

func InitialiseJarvisSecretService(conf *config.Config) *jarvisSecret.JarvisSecretService {
	jarvisSecretService := jarvisSecret.NewJarvisSecretService(conf)
	return jarvisSecretService
}

func InitialiseJarvisSsoService(conf *config.Config) *jarvisSso.JarvisSsoService {
	jarvisSsoService := jarvisSso.NewJarvisSsoService(conf)
	return jarvisSsoService
}

func InitialiseConsulService(conf *config.Config) *consul.ConsulService {
	consulService := consul.NewConsulService(conf)
	return consulService
}

func InitialiseEdithService(conf *config.Config, db types.JarvisPGDB) (*edith.Service, error) {
	gormDB := types.JarvisPGDBGormProvider(db)
	uint32_2 := MaxPagSizeProvider(conf)
	formDaoPgdb := form.NewFormPgDb(gormDB, uint32_2)
	ticketDaoPgdb := ticket.NewTicketPgDb(gormDB, uint32_2)
	approverListDaoPgdb := approver_list.NewApproverListPgDb(gormDB, uint32_2)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	service, err := edith.NewService(conf, formDaoPgdb, ticketDaoPgdb, approverListDaoPgdb, gormTxnExecutor)
	if err != nil {
		return nil, err
	}
	return service, nil
}

func InitialiseFrontendService(conf *config.Config, edithService *edith.Service, temporalClient *client.Client) (*frontend.Service, error) {
	service, err := frontend.NewService(conf, edithService, temporalClient)
	if err != nil {
		return nil, err
	}
	return service, nil
}

func InitialisePrDeploymentService(conf *config.Config, temporalClient client.Client, awsConfig aws.Config, slackToken types2.SlackToken) *prdeployment.PrDeploymentService {
	s3Client := s3ClientProvider(awsConfig, conf)
	slackClient := slackClientProvider(slackToken)
	prDeploymentService := prdeployment.NewPrDeploymentService(temporalClient, conf, s3Client, slackClient)
	return prDeploymentService
}

func InitialiseActivityProcessor(jarvisService *jarvis.JarvisService, edithService *edith.Service, frontendService *frontend.Service, workerConf *worker.Config, slackToken types2.SlackToken, awsConfig aws.Config) (*activity.Processor, error) {
	context := contextProvider()
	jenkinsClient, err := jenkinsClientProvider(context, workerConf)
	if err != nil {
		return nil, err
	}
	awsService, err := awsServiceProvider(awsConfig, workerConf)
	if err != nil {
		return nil, err
	}
	slackClient := slackClientProvider(slackToken)
	processor, err := activity.NewProcessor(jarvisService, edithService, frontendService, jenkinsClient, awsService, slackClient, workerConf)
	if err != nil {
		return nil, err
	}
	return processor, nil
}

func InitialiseGithubWebhookService(ctx context.Context, conf *config.Config, slackToken types2.SlackToken, awsConfig aws.Config) (*github.GithubWebhookService, error) {
	slackClient := slackClientProvider(slackToken)
	iGithubApiWrapper, err := initialiseGithubApiWrapper(ctx, conf)
	if err != nil {
		return nil, err
	}
	issuesEventHandler, err := eventhandler.NewIssuesEventHandler(slackClient, iGithubApiWrapper, conf)
	if err != nil {
		return nil, err
	}
	genconfConfig := genConfigProvider(conf)
	bedrockruntimeClient := bedrockClientProvider(awsConfig)
	githubClient, err := client2.NewGithubClient(conf)
	if err != nil {
		return nil, err
	}
	pullRequestEventHandler, err := eventhandler.NewPullRequestEventHandler(slackClient, genconfConfig, bedrockruntimeClient, githubClient)
	if err != nil {
		return nil, err
	}
	v := eventHandlerProvider(issuesEventHandler, pullRequestEventHandler)
	githubWebhookService := github.NewGithubWebhookService(conf, v)
	return githubWebhookService, nil
}

// wire.go:

func MaxPagSizeProvider(conf *config.Config) uint32 {
	return conf.MaxPageSize
}

func jenkinsClientProvider(ctx context.Context, conf *worker.Config) (*jenkins.Client, error) {
	var password string
	if conf.Application.Environment == cfg.ProductionEnv {
		password = conf.Secrets.Ids[config.JenkinsToken]
	} else {
		password = conf.Secrets.Ids[config.JenkinsTokenNonProd]
	}
	return jenkins.NewClient(ctx, conf.JenkinsUrlBase, conf.JenkinsUsername, password)
}

func awsServiceProvider(awsConfig aws.Config, conf *worker.Config) (*jarvisAws.AwsService, error) {
	serverConf, err := worker.LoadServerConfig(conf)
	if err != nil {
		return nil, fmt.Errorf("failed to load server config: %w", err)
	}
	envServergenConfig := make(map[string]*config2.Config)
	for _, env := range conf.Envs {
		confDir, err := cfg.GetConfigDir()
		if err != nil {
			return nil, fmt.Errorf("failed to get config dir: %w", err)
		}
		serverDefFilePath := filepath.Join(confDir, fmt.Sprintf(config2.ServerDefConfigFile, env))
		defconf, err := config2.Load(serverDefFilePath)
		if err != nil {
			return nil, fmt.Errorf("failed to load servergen config for env %s: %w", env, err)
		}
		envServergenConfig[env] = defconf
	}
	return jarvisAws.NewAwsService(awsConfig, serverConf, envServergenConfig), nil
}

func contextProvider() context.Context {
	return context.Background()
}

func s3ClientProvider(awsConfig aws.Config, conf *config.Config) *s3.Client {
	return s3.NewClient(awsConfig, conf.PlatformS3Bucket)
}

func slackClientProvider(slackToken types2.SlackToken) *slack.Client {
	return slack.New(string(slackToken), slack.OptionDebug(false))
}

func eventHandlerProvider(
	issueHandler *eventhandler.IssuesEventHandler,
	prHandler *eventhandler.PullRequestEventHandler,
) map[string]eventhandler.EventHandler {
	return eventhandler.InitEventHandlerMap(issueHandler, prHandler)
}

func initialiseGithubApiWrapper(ctx context.Context, conf *config.Config) (github2.IGithubApiWrapper, error) {
	clientManager, err := github2.NewGitHubClientManager(ctx, conf.GithubApp.AppId, conf.GithubApp.PrivateKey, conf.GithubApp.InstallationId)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize GitHub client manager: %w", err)
	}
	return github2.NewGithubApiWrapper(clientManager), nil
}

func bedrockClientProvider(awsConfig aws.Config) *bedrockruntime.Client {
	return bedrockruntime.NewFromConfig(awsConfig)
}

func genConfigProvider(conf *config.Config) *genconf.Config {

	genConfig, _ := genconf.NewConfig()
	genConfig.Set(conf, false, nil)
	return genConfig
}
