package eventhandler

import "context"

type EventHandler interface {
	EventType() string
	Handle(ctx context.Context, body []byte) error
}

func InitEventHandlerMap(
	issueHandler *IssuesEventHandler,
	prHandler *PullRequestEventHandler,
) map[string]EventHandler {
	eventHandlers := make(map[string]EventHandler)
	eventHandlers["issues"] = issueHandler
	eventHandlers["pull_request"] = prHandler
	return eventHandlers
}
