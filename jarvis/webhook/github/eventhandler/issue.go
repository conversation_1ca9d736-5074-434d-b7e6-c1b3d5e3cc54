// nolint:gosec
package eventhandler

import (
	"context"
	"encoding/json"
	"fmt"

	githubpb "github.com/epifi/be-common/api/pkg/github"
	"github.com/epifi/be-common/pkg/github"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/samber/lo"
	"github.com/slack-go/slack"
	"go.uber.org/zap"

	jarvisconf "github.com/epifi/gamma/jarvis/config"
)

type IssuesEventHandler struct {
	slackClient              *slack.Client
	githubApiWrapper         github.IGithubApiWrapper
	conf                     *jarvisconf.Config
	githubUserNameToEmailMap map[string]string
}

func NewIssuesEventHandler(slackClient *slack.Client, githubApiWrapper github.IGithubApiWrapper, conf *jarvisconf.Config) (*IssuesEventHandler, error) {
	usernameToEmailMap, err := github.LoadGithubUserNameToEmailMap(conf.UserMappingFilePath)
	if err != nil {
		logger.ErrorNoCtx("Failed to load GitHub user name to email map", zap.Error(err))
		return nil, err
	}
	return &IssuesEventHandler{
		slackClient:              slackClient,
		githubApiWrapper:         githubApiWrapper,
		conf:                     conf,
		githubUserNameToEmailMap: usernameToEmailMap,
	}, nil
}

func (h *IssuesEventHandler) EventType() string { return "issues" }

func (h *IssuesEventHandler) Handle(ctx context.Context, body []byte) error {
	var request = &payload{}
	if err := json.Unmarshal(body, &request); err != nil {
		logger.ErrorNoCtx("Failed to parse issues payload", zap.Error(err))
		return err
	}

	switch request.Action {
	case "assigned":
		err := h.handleAssignedAction(ctx, request)
		if err != nil {
			logger.ErrorNoCtx("Failed to handle assigned action", zap.Error(err))
			return err
		}
	default:
		logger.InfoNoCtx("Ignoring action", zap.String("action", request.Action))
	}

	return nil
}

// constructSlackIssueAssignmentMsg builds the Slack message for an issue assignment
func (i *IssuesEventHandler) constructSlackIssueAssignmentMsg(ctx context.Context, assigner string, issueURL string, issueTitle string) string {
	assignerMention := ""
	if assigner != "" {
		assignerEmail, ok := i.githubUserNameToEmailMap[assigner]
		if !ok {
			logger.Error(ctx, "GitHub username not found in username-to-email map", zap.String("githubUsername", assigner))
			assignerMention = assigner
		} else {
			assignerUserId, err := i.slackClient.GetUserByEmail(assignerEmail)
			if err != nil {
				logger.Error(ctx, "Failed to find Slack user by email", zap.String("email", assignerEmail), zap.Error(err))
				assignerMention = assigner
			} else {
				assignerMention = "<@" + assignerUserId.ID + ">"
			}
		}
	}

	// Truncate issue title to 100 characters
	truncatedTitle := issueTitle
	if len(issueTitle) > 100 {
		truncatedTitle = issueTitle[:97] + "..."
	}

	msg := ""
	if assignerMention != "" {
		msg += assignerMention + " assigned you an <" + issueURL + "|issue>: *" + truncatedTitle + "* \n"
	} else {
		msg += "You have been assigned an <" + issueURL + "|issue>: *" + truncatedTitle + "* \n"
	}
	return msg
}

// sendSlackMessage sends a notification to the user (by GitHub username) via Slack DM
func (i *IssuesEventHandler) sendSlackMessage(githubUsername, msgToSend string) error {
	email, ok := i.githubUserNameToEmailMap[githubUsername]
	if !ok {
		logger.ErrorNoCtx("GitHub username not found in username-to-email map", zap.String("githubUsername", githubUsername))
		return fmt.Errorf("GitHub username not found in map: %s", githubUsername)
	}

	user, err := i.slackClient.GetUserByEmail(email)
	if err != nil {
		logger.ErrorNoCtx("Failed to find Slack user by email", zap.String("email", email), zap.Error(err))
		return err
	}

	channel := user.ID // Slack user ID for DM
	_, _, err = i.slackClient.PostMessage(channel, slack.MsgOptionText(msgToSend, false))
	if err != nil {
		logger.ErrorNoCtx("Failed to send Slack DM", zap.String("slackUserId", channel), zap.Error(err))
		return err
	}
	return nil
}

type payload struct {
	Action   string `json:"action"`
	Assignee *struct {
		Login string `json:"login"`
		Email string `json:"email,omitempty"`
	} `json:"assignee,omitempty"`

	Issue struct {
		Number  int    `json:"number"`
		Title   string `json:"title"`
		HTMLURL string `json:"html_url"`
		User    struct {
			Login string `json:"login"`
		} `json:"user"`
		Assignees []struct {
			Login string `json:"login"`
			Email string `json:"email,omitempty"`
		} `json:"assignees"`
		Labels []struct {
			Name string `json:"name"`
		} `json:"labels"`
	} `json:"issue"`

	Repository struct {
		Name  string `json:"name"`
		Owner struct {
			Login string `json:"login"`
		} `json:"owner"`
	} `json:"repository"`

	Sender struct {
		Login string `json:"login"`
	} `json:"sender"`
}

func (i *IssuesEventHandler) handleAssignedAction(ctx context.Context, body *payload) error {
	// Skip notification if the issue has a label in config.SkipNotificationLabels
	for _, label := range body.Issue.Labels {
		if lo.Contains(i.conf.GithubWebhookConfig.SkipNotificationLabels, label.Name) {
			logger.Info(ctx, "Skipping notification for configured label", zap.String("label", label.Name))
			return nil
		}
	}

	// Use the specific assignee from the payload instead of all assignees
	if body.Assignee == nil {
		logger.Info(ctx, "No assignee in payload, skipping notification")
		return nil
	}

	// Skip notification if the assignee is the same as the issue creator
	if body.Issue.User.Login == body.Assignee.Login {
		logger.Info(ctx, "Assignee is the same as issue creator, skipping notification", zap.String("assignee", body.Assignee.Login), zap.String("issueCreator", body.Issue.User.Login))
		return nil
	}

	projects, err := i.getProjectsForIssue(ctx, body.Repository.Owner.Login, body.Repository.Name, int32(body.Issue.Number))
	if err != nil {
		logger.Error(ctx, "Failed to get projects for issue", zap.Error(err))
		return err
	}

	if len(projects) == 0 {
		logger.Info(ctx, "No projects found for issue", zap.String("repo", body.Repository.Name), zap.Int("issueNumber", body.Issue.Number))
		msg := i.constructSlackIssueAssignmentMsg(ctx, body.Sender.Login, body.Issue.HTMLURL, body.Issue.Title)
		err := i.sendSlackMessage(body.Assignee.Login, msg)
		if err != nil {
			logger.Error(ctx, "Failed to send Slack message", zap.Error(err), zap.String("user", body.Assignee.Login), zap.String("repo", body.Repository.Name), zap.Int("issueNumber", body.Issue.Number))
			return fmt.Errorf("failed to send Slack message: %w", err)
		}
		return nil
	}

	// Step 1: Collect all unique users to notify (GitHub usernames)
	notifyUsers := make(map[string]struct{})

	for _, project := range projects {
		notifConfig, ok := i.conf.GithubWebhookConfig.ProjectToNotificationsMap[project]
		if !ok {
			logger.Info(ctx, "No notification config for project", zap.String("project", project))
		}

		// Check if the specific assignee is eligible for this project
		userEmail, ok := i.githubUserNameToEmailMap[body.Assignee.Login]
		if !ok {
			logger.Error(ctx, "GitHub username not found in username-to-email map", zap.String("githubUsername", body.Assignee.Login))
			continue
		}

		if notifConfig != nil && lo.Contains(notifConfig.BlacklistedUsers, userEmail) {
			logger.Info(ctx, "User is blacklisted for project, skipping notification", zap.String("user", body.Assignee.Login), zap.String("email", userEmail), zap.String("project", project))
			continue
		}
		notifyUsers[body.Assignee.Login] = struct{}{}
	}

	// Step 2: Send the Slack message to the assigned user
	msg := i.constructSlackIssueAssignmentMsg(ctx, body.Sender.Login, body.Issue.HTMLURL, body.Issue.Title)
	for user := range notifyUsers {
		err := i.sendSlackMessage(user, msg)
		if err != nil {
			logger.Error(ctx, "Failed to send Slack message", zap.Error(err), zap.String("user", user))
		} else {
			logger.Info(ctx, "Sent Slack message", zap.String("user", user), zap.String("repo", body.Repository.Name), zap.String("issue", body.Issue.Title))
		}
	}

	return nil
}

func (i *IssuesEventHandler) getProjectsForIssue(ctx context.Context, owner, repo string, issueNumber int32) ([]string, error) {
	resp, err := i.githubApiWrapper.GetProjectsForIssue(ctx, &githubpb.GetProjectsForIssueRequest{
		Owner:       owner,
		Repo:        repo,
		IssueNumber: issueNumber,
	})
	if err != nil {
		logger.Error(ctx, "Failed to get projects for issue", zap.Error(err))
		return nil, err
	}
	return resp.Projects, nil
}
