package eventhandler

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/bedrockruntime"
	gh "github.com/google/go-github/v53/github"
	"github.com/slack-go/slack"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"
	jarvisconf "github.com/epifi/gamma/jarvis/config/genconf"
)

var issueLinkRegex = regexp.MustCompile(`(?i)(?:(?:close|closes|closed|fix|fixes|fixed|resolve|resolves|resolved)\s*(?:#\d+|https://github\.com/epifi/tickets/issues/\d+|epifi/tickets#?\d+))|(?:https://github\.com/epifi/tickets/issues/\d+|epifi/tickets#?\d+)`)

type PullRequestEventHandler struct {
	slackClient   *slack.Client
	conf          *jarvisconf.Config
	bedrockClient *bedrockruntime.Client
	githubClient  *gh.Client
}

func NewPullRequestEventHandler(
	slackClient *slack.Client,
	conf *jarvisconf.Config,
	bedrockClient *bedrockruntime.Client,
	githubClient *gh.Client,
) (*PullRequestEventHandler, error) {
	return &PullRequestEventHandler{
		slackClient:   slackClient,
		conf:          conf,
		bedrockClient: bedrockClient,
		githubClient:  githubClient,
	}, nil
}

func (h *PullRequestEventHandler) EventType() string { return "pull_request" }

func (h *PullRequestEventHandler) Handle(ctx context.Context, body []byte) error {
	var request = &pullRequestPayload{}
	if err := json.Unmarshal(body, &request); err != nil {
		logger.Error(ctx, "Failed to parse pull_request payload", zap.Error(err))
		return err
	}

	switch request.Action {
	case "opened":
		err := h.handleOpenedAction(ctx, request)
		if err != nil {
			logger.Error(ctx, "Failed to handle opened action", zap.Error(err))
			return err
		}
	default:
		logger.Info(ctx, "Ignoring pull request action", zap.String("action", request.Action))
	}

	return nil
}

func (h *PullRequestEventHandler) handleOpenedAction(ctx context.Context, payload *pullRequestPayload) error {
	// Check if auto issue linking is enabled
	if !h.conf.GithubWebhookConfig().EnableAutoIssueLinking() {
		logger.Info(ctx, "Auto issue linking is disabled, skipping")
		return nil
	}

	prAuthor := payload.PullRequest.User.Login
	if prAuthor == "" {
		logger.Info(ctx, "PR author not found, skipping")
		return nil
	}

	// Check if the author is allowed based on whitelist/blacklist
	if !h.isAuthorAllowed(prAuthor) {
		logger.Info(ctx, "PR author is not allowed for auto issue linking", zap.String("author", prAuthor))
		return nil
	}

	if h.hasLinkedIssue(payload.PullRequest.Body) {
		logger.Info(ctx, "PR already has a linked issue, skipping")
		return nil
	}

	logger.Info(ctx, "Processing opened pull request",
		zap.String("pr_author", prAuthor),
		zap.String("pr_title", payload.PullRequest.Title),
		zap.Int("pr_number", payload.Number),
	)

	relevantIssue, err := h.getRelevantIssue(ctx, prAuthor, payload.PullRequest.Title, payload.PullRequest.Body)
	if err != nil {
		logger.Error(ctx, "Failed to get relevant issue", zap.Error(err), zap.String("pr_author", prAuthor))
		return err
	}

	if relevantIssue == "" {
		logger.Info(ctx, "No relevant issue found", zap.String("pr_author", prAuthor))
		return nil
	}

	logger.Info(ctx, "Found relevant issue, updating PR description",
		zap.String("issue_url", relevantIssue),
		zap.Int("pr_number", payload.Number),
	)

	newBody := fmt.Sprintf("Fixes %s\n\n%s", relevantIssue, payload.PullRequest.Body)
	_, _, err = h.githubClient.PullRequests.Edit(ctx,
		payload.Repository.Owner.Login,
		payload.Repository.Name,
		payload.Number,
		&gh.PullRequest{Body: &newBody},
	)

	if err != nil {
		logger.Error(ctx, "Failed to update PR description", zap.Error(err), zap.Int("pr_number", payload.Number))
		return err
	}

	return nil
}

func (h *PullRequestEventHandler) hasLinkedIssue(body string) bool {
	return issueLinkRegex.MatchString(body)
}

// isAuthorAllowed checks if the PR author is allowed for auto issue linking based on whitelist/blacklist
func (h *PullRequestEventHandler) isAuthorAllowed(author string) bool {
	// If whitelist is not empty, only allow authors in the whitelist
	whitelistedUsers := h.conf.GithubWebhookConfig().WhitelistedUsers()
	if whitelistedUsers.Len() > 0 {
		for i := 0; i < whitelistedUsers.Len(); i++ {
			if strings.EqualFold(author, whitelistedUsers.At(i)) {
				return true
			}
		}
		// Author not in whitelist
		return false
	}

	// If whitelist is empty, check blacklist
	blacklistedUsers := h.conf.GithubWebhookConfig().BlacklistedUsers()
	for i := 0; i < blacklistedUsers.Len(); i++ {
		if strings.EqualFold(author, blacklistedUsers.At(i)) {
			// Author is in blacklist
			return false
		}
	}

	// Author not in blacklist or whitelist is empty
	return true
}

// PRAnalysisResult represents the AI's analysis of a PR and potential matching issues
type PRAnalysisResult struct {
	PRIntent   string  `json:"pr_intent"`
	URL        string  `json:"url"`
	Reasoning  string  `json:"reasoning"`
	Confidence float64 `json:"confidence"`
	IsRelevant bool    `json:"is_relevant"`
}

// The descriptiveness check is now handled by the AI

func (h *PullRequestEventHandler) getRelevantIssue(ctx context.Context, author, prTitle, prBody string) (string, error) {
	// Get the list of candidate issues
	twoWeeksAgo := time.Now().Add(-14 * 24 * time.Hour)
	issues, _, err := h.githubClient.Issues.ListByRepo(ctx, "epiFi", "tickets", &gh.IssueListByRepoOptions{
		State:     "open",
		Assignee:  author,
		Sort:      "updated",
		Direction: "desc",
		Since:     twoWeeksAgo,
	})
	if err != nil {
		return "", fmt.Errorf("failed to list issues: %w", err)
	}
	if len(issues) == 0 {
		logger.Info(ctx, "No recently updated open issues found for author", zap.String("author", author))
		return "", nil
	}

	// Single AI call to analyze PR and find matching issue
	return h.findMatchingIssue(ctx, prTitle, prBody, issues)
}

func (h *PullRequestEventHandler) findMatchingIssue(ctx context.Context, prTitle, prBody string, issues []*gh.Issue) (string, error) {
	var issueDetails []string
	for _, issue := range issues {
		issueDetails = append(issueDetails, fmt.Sprintf("URL: %s\nTitle: %s\nBody: %s\n", issue.GetHTMLURL(), issue.GetTitle(), issue.GetBody()))
	}
	issueList := strings.Join(issueDetails, "\n---\n")

	systemPrompt := `You are an expert AI assistant tasked with linking GitHub Pull Requests to their corresponding issues. You must follow a strict, two-step evaluation process.

Step 1: Quality & Descriptiveness Gate
First, you MUST assess if the PR title and description are descriptive enough to even attempt a match. A PR fails this quality check if it meets any of the following criteria:

The title is generic and provides no context (e.g., "update", "fix", "changes", "wip", "refactor", "cleanup", "test").
The title and body are very short and lack specific nouns, feature names, or problem descriptions.
The content is too vague to determine a clear purpose.
If a PR fails this quality check, you MUST NOT attempt to find an issue. Your response should immediately indicate insufficient information.

Step 2: High-Confidence Issue Matching
If, AND ONLY IF, the PR passes the quality gate, proceed to find a matching issue. In this step, you must be extremely strict and conservative:

High Confidence Required: Only match if you are at least 90% confident the PR directly resolves or addresses an issue.
Look for Specifics: Find explicit connections, such as shared feature names, bug descriptions, error codes, or technical terms.
No Match is Better than a Bad Match: If you are not highly confident, do not suggest a link.

Respond with ONLY a JSON object with the following schema and absolutely no other text:
{
  "pr_intent": "<A concise 1-2 sentence summary of the PR's purpose, or 'INSUFFICIENT_INFORMATION' if unclear>",
  "url": "<URL of the most relevant issue, or empty string if none>",
  "reasoning": "<A brief explanation for your decision, including specific matching terms or concepts>",
  "confidence": <A float between 0.9 and 1.0 if relevant, 0.0 if not relevant>,
  "is_relevant": <true or false>
}`

	userPrompt := fmt.Sprintf(`Please analyze this pull request:

**Title:** %s

**Description:**
%s

Does this PR directly address any of the problems described in the following issues?

---
%s`, prTitle, prBody, issueList)

	payload, err := getBedrockPayload(userPrompt, systemPrompt, h.conf)
	if err != nil {
		return "", fmt.Errorf("failed to create bedrock payload: %w", err)
	}

	output, err := h.bedrockClient.InvokeModel(ctx, &bedrockruntime.InvokeModelInput{
		Body:        []byte(payload),
		ModelId:     aws.String(h.conf.GithubWebhookConfig().BedrockModelId()),
		Accept:      aws.String("application/json"),
		ContentType: aws.String("application/json"),
	})
	if err != nil {
		return "", fmt.Errorf("failed to invoke bedrock model: %w", err)
	}

	modelRes := &InvokeModelResponse{}
	if err := json.Unmarshal(output.Body, modelRes); err != nil {
		return "", fmt.Errorf("failed to unmarshal bedrock response: %w", err)
	}

	if len(modelRes.Content) == 0 {
		return "", fmt.Errorf("empty response from bedrock")
	}

	responseText := strings.TrimSpace(modelRes.Content[0].Text)
	responseText = strings.TrimPrefix(responseText, "```json")
	responseText = strings.TrimSuffix(responseText, "```")

	var result PRAnalysisResult
	if err := json.Unmarshal([]byte(responseText), &result); err != nil {
		return "", fmt.Errorf("failed to unmarshal AI response: %w", err)
	}

	// Log the AI's assessment
	logger.Info(ctx, "PR analysis results",
		zap.String("pr_intent", result.PRIntent),
		zap.Bool("is_relevant", result.IsRelevant),
		zap.Float64("confidence", result.Confidence),
		zap.String("reasoning", result.Reasoning),
		zap.String("url", result.URL),
	)

	// If the AI couldn't determine the PR's intent
	if result.PRIntent == "INSUFFICIENT_INFORMATION" {
		logger.Info(ctx, "AI determined PR title and description are not descriptive enough")
		return "", nil
	}

	// Return the matching issue URL if confidence threshold is met
	// Note: The AI has already been instructed to only return confidence >= 0.9 if relevant
	if result.IsRelevant && result.Confidence >= 0.9 {
		return result.URL, nil
	}

	return "", nil
}

type BedrockMessage struct {
	Role    string           `json:"role"`
	Content []BedrockContent `json:"content"`
}

type BedrockContent struct {
	Type string `json:"type"`
	Text string `json:"text"`
}

type BedrockRequestPayload struct {
	AnthropicVersion string           `json:"anthropic_version"`
	MaxTokens        int              `json:"max_tokens"`
	System           string           `json:"system"`
	Messages         []BedrockMessage `json:"messages"`
}

func getBedrockPayload(userPrompt, systemPrompt string, conf *jarvisconf.Config) (string, error) {
	payload := BedrockRequestPayload{
		AnthropicVersion: conf.GithubWebhookConfig().AnthropicVersion(),
		MaxTokens:        1000,
		System:           systemPrompt,
		Messages: []BedrockMessage{
			{
				Role: "user",
				Content: []BedrockContent{
					{
						Type: "text",
						Text: userPrompt,
					},
				},
			},
		},
	}
	b, err := json.Marshal(payload)
	if err != nil {
		return "", err
	}
	return string(b), nil
}

type pullRequestPayload struct {
	Action      string `json:"action"`
	Number      int    `json:"number"`
	PullRequest struct {
		HTMLURL string `json:"html_url"`
		Title   string `json:"title"`
		Body    string `json:"body"`
		User    struct {
			Login string `json:"login"`
		} `json:"user"`
	} `json:"pull_request"`
	Repository struct {
		Name  string `json:"name"`
		Owner struct {
			Login string `json:"login"`
		} `json:"owner"`
	} `json:"repository"`
}

type InvokeModelResponseContent struct {
	Text string `json:"text"`
	Type string `json:"type"`
}

type InvokeModelResponse struct {
	Content []InvokeModelResponseContent `json:"content"`
}
