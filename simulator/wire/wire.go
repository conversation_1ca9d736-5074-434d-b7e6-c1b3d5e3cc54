//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.
package wire

import (
	"database/sql"
	"time"

	"github.com/google/wire"
	"google.golang.org/grpc"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/crypto/rsa"

	celestialPb "github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/crypto"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/vendorapi"

	p2pPb "github.com/epifi/gamma/api/p2pinvestment"
	p2pDsPb "github.com/epifi/gamma/api/p2pinvestment/developer"
	savingsPb "github.com/epifi/gamma/api/savings"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/vendorgateway/sms"
	vnCcPb "github.com/epifi/gamma/api/vendornotification/creditcard"
	vnLoansAbflPb "github.com/epifi/gamma/api/vendornotification/lending/loans/abfl"
	fedPay "github.com/epifi/gamma/api/vendornotification/openbanking/payment/federal"
	vendorClient "github.com/epifi/gamma/api/vendors/http"
	"github.com/epifi/gamma/api/vendors/sftp"
	"github.com/epifi/gamma/simulator"
	ignosisSim "github.com/epifi/gamma/simulator/aa/analytics/ignosis"
	"github.com/epifi/gamma/simulator/aa/bouncycastle"
	"github.com/epifi/gamma/simulator/aa/finvu"
	"github.com/epifi/gamma/simulator/aa/onemoney"
	"github.com/epifi/gamma/simulator/app_screener/seon"
	"github.com/epifi/gamma/simulator/bbps/setu"
	"github.com/epifi/gamma/simulator/categorizer"
	"github.com/epifi/gamma/simulator/config"
	"github.com/epifi/gamma/simulator/config/genconf"
	"github.com/epifi/gamma/simulator/credit_report/cibil"
	"github.com/epifi/gamma/simulator/credit_report/experian"
	"github.com/epifi/gamma/simulator/creditcard/saven"
	"github.com/epifi/gamma/simulator/cx/freshdesk"
	"github.com/epifi/gamma/simulator/cx/ozonetel"
	"github.com/epifi/gamma/simulator/cx/watson_client"
	"github.com/epifi/gamma/simulator/dao"
	"github.com/epifi/gamma/simulator/dao/mutual_fund/impl"
	ussOrdersImpl "github.com/epifi/gamma/simulator/dao/usstocks/impl"
	"github.com/epifi/gamma/simulator/dl"
	"github.com/epifi/gamma/simulator/dobby"
	"github.com/epifi/gamma/simulator/dobby/onboarding"
	"github.com/epifi/gamma/simulator/docs"
	karzaEmpl "github.com/epifi/gamma/simulator/employment/karza"
	"github.com/epifi/gamma/simulator/epan"
	"github.com/epifi/gamma/simulator/esign"
	"github.com/epifi/gamma/simulator/extvalidate"
	"github.com/epifi/gamma/simulator/fennel"
	fittt "github.com/epifi/gamma/simulator/fittt/roanuz"
	"github.com/epifi/gamma/simulator/healthinsurance/onsurity"
	simulatorOpbProcessor "github.com/epifi/gamma/simulator/helper"
	"github.com/epifi/gamma/simulator/idvalidate"
	"github.com/epifi/gamma/simulator/incomeestimator"
	"github.com/epifi/gamma/simulator/inhousebre"
	"github.com/epifi/gamma/simulator/iplocation/ipstack"
	"github.com/epifi/gamma/simulator/itr"
	"github.com/epifi/gamma/simulator/kyc/federal"
	simKarza "github.com/epifi/gamma/simulator/kyc/karza"
	"github.com/epifi/gamma/simulator/lending/bre"
	creditCardFlows "github.com/epifi/gamma/simulator/lending/creditcard"
	creditCardDao "github.com/epifi/gamma/simulator/lending/creditcard/dao"
	creditLine "github.com/epifi/gamma/simulator/lending/creditline"
	"github.com/epifi/gamma/simulator/lending/preapprovedloan"
	preapprovedDao "github.com/epifi/gamma/simulator/lending/preapprovedloan/dao"
	"github.com/epifi/gamma/simulator/lending/securedloans/fiftyfin"
	securedloans "github.com/epifi/gamma/simulator/lending/securedloans/fiftyfin/dao"
	dbDataStore "github.com/epifi/gamma/simulator/lending/securedloans/fiftyfin/datastore/impl"
	inhouseLivPb "github.com/epifi/gamma/simulator/liveness/inhouse"
	"github.com/epifi/gamma/simulator/liveness/karza"
	"github.com/epifi/gamma/simulator/liveness/veri5"
	"github.com/epifi/gamma/simulator/location"
	federalNameCheck "github.com/epifi/gamma/simulator/namecheck/federal"
	"github.com/epifi/gamma/simulator/namecheck/inhouse"
	"github.com/epifi/gamma/simulator/ocr"
	karzaOcr "github.com/epifi/gamma/simulator/ocr/karza"
	"github.com/epifi/gamma/simulator/offers/dreamfolks"
	simLoylty "github.com/epifi/gamma/simulator/offers/loylty"
	"github.com/epifi/gamma/simulator/offers/qwikcilver"
	accountsDevSvc "github.com/epifi/gamma/simulator/openbanking/accounts/developer"
	federal2 "github.com/epifi/gamma/simulator/openbanking/accounts/federal"
	accountcb "github.com/epifi/gamma/simulator/openbanking/accounts/federal/consumer"
	federalAuth "github.com/epifi/gamma/simulator/openbanking/auth/federal"
	federalPartnerSDK "github.com/epifi/gamma/simulator/openbanking/auth/partnersdk/federal"
	m2pPartnerSDK "github.com/epifi/gamma/simulator/openbanking/auth/partnersdk/m2p"
	card "github.com/epifi/gamma/simulator/openbanking/card/federal"
	fedCard "github.com/epifi/gamma/simulator/openbanking/card/federal/consumer"
	federalChkbq "github.com/epifi/gamma/simulator/openbanking/chequebook/federal"
	"github.com/epifi/gamma/simulator/openbanking/customer"
	federalDeposit "github.com/epifi/gamma/simulator/openbanking/deposit/federal"
	depositConsumer "github.com/epifi/gamma/simulator/openbanking/deposit/federal/consumer"
	dmpDispute "github.com/epifi/gamma/simulator/openbanking/dispute/federal"
	federal4 "github.com/epifi/gamma/simulator/openbanking/enach/federal"
	enquiry "github.com/epifi/gamma/simulator/openbanking/enquiry/federal"
	fedB2CFundTransfer "github.com/epifi/gamma/simulator/openbanking/fundtransfer/b2c/federal"
	fedFundTransfer "github.com/epifi/gamma/simulator/openbanking/fundtransfer/federal"
	"github.com/epifi/gamma/simulator/openbanking/fundtransfer/federal/consumer"
	"github.com/epifi/gamma/simulator/openbanking/fundtransfer/internationalfundtransfer"
	federal3 "github.com/epifi/gamma/simulator/openbanking/lien/federal"
	fedSavings "github.com/epifi/gamma/simulator/openbanking/savings/federal"
	shippingPreference "github.com/epifi/gamma/simulator/openbanking/shipping_preference/federal"
	shippingPreferenceCb "github.com/epifi/gamma/simulator/openbanking/shipping_preference/federal/consumer"
	"github.com/epifi/gamma/simulator/openbanking/tiering"
	federalUpi "github.com/epifi/gamma/simulator/openbanking/upi/federal"
	"github.com/epifi/gamma/simulator/p2pinvestment/developer"
	"github.com/epifi/gamma/simulator/p2pinvestment/helper"
	"github.com/epifi/gamma/simulator/p2pinvestment/liquiloans"
	liquiloansDao "github.com/epifi/gamma/simulator/p2pinvestment/liquiloans/dao"
	federalPAN "github.com/epifi/gamma/simulator/pan/federal"
	mykarza "github.com/epifi/gamma/simulator/pan/karza"
	"github.com/epifi/gamma/simulator/phonenetwork"
	profileEvaluatorPb "github.com/epifi/gamma/simulator/profileevaluator"
	profileEvaluatorDao "github.com/epifi/gamma/simulator/profileevaluator/dao"
	"github.com/epifi/gamma/simulator/risk"
	"github.com/epifi/gamma/simulator/salaryprogram/leadsquared"
	"github.com/epifi/gamma/simulator/shipway"
	sms2 "github.com/epifi/gamma/simulator/sms"
	"github.com/epifi/gamma/simulator/sms/scienaptic"
	fedStandingInstruction "github.com/epifi/gamma/simulator/standinginstruction/federal"
	"github.com/epifi/gamma/simulator/tss"
	"github.com/epifi/gamma/simulator/usstocks/alpaca"
	vkycFederal "github.com/epifi/gamma/simulator/vkyc/federal"
	"github.com/epifi/gamma/simulator/wealth"
	"github.com/epifi/gamma/simulator/wealth/mutualfund/cams"
	"github.com/epifi/gamma/simulator/wealth/mutualfund/karvy"
	"github.com/epifi/gamma/simulator/wealth/mutualfund/mfcentral"
	"github.com/epifi/gamma/simulator/wealth/mutualfund/smallcase"
	"github.com/epifi/gamma/simulator/whatsapp"
	types2 "github.com/epifi/gamma/simulator/wire/types"
	"github.com/epifi/gamma/vendornotification/client"
)

func InitializeCustomerService(db *sql.DB, simDb types.SimulatorCRDB) *simulator.CustomerService {
	wire.Build(
		simulator.NewCustomerService,
		types.SimulatorCRDBGormDBProvider,
		dao.NewCustomerStorage,
		idgen.NewCryptoSeededSource,
		idgen.WireSet,
	)
	return &simulator.CustomerService{}
}

func InitializeBusinessRuleEngineService(dynConf *genconf.Config) *bre.Service {
	wire.Build(
		bre.NewService,
		getLendingGenConf,
	)
	return &bre.Service{}

}
func InitializeAccountService(db *sql.DB, simDb types.SimulatorCRDB) *simulator.AccountService {
	wire.Build(
		dao.WireSet,
		types.SimulatorCRDBGormDBProvider,
		dao.NewCustomerStorage,
		simulator.NewAccountService,
		idgen.NewCryptoSeededSource,
		idgen.WireSet,
	)
	return &simulator.AccountService{}
}

func InitializeFederalEKycService(smsClient sms.SMSClient, simDb types.SimulatorCRDB) *federal.EKYCService {
	wire.Build(
		federal.NewFederalEKYCService,
		dao.WireSet,
		types.SimulatorCRDBGormDBProvider,
	)
	return &federal.EKYCService{}
}

func stubFileConfProvider(conf *config.Config) string {
	return conf.Stubs["ckyc"]
}

func InitializeFederalCKycService(conf *config.Config, simDb types.SimulatorCRDB) *federal.CKycService {
	wire.Build(federal.NewCkycDB, federal.NewFederalCKycService,
		stubFileConfProvider,
		types.SimulatorCRDBGormDBProvider,
	)
	return &federal.CKycService{}
}

func InitializeOneMoneyAAService(simDb types.SimulatorCRDB, conf *config.Config, cc grpc.ClientConnInterface, dynConf *genconf.Config) *onemoney.Service {
	wire.Build(client.NewHttpClient, onemoney.NewOneMoneyDB, sms.NewSMSClient,
		types.SimulatorCRDBGormDBProvider,
		dao.WireSet,
		idgen.NewCryptoSeededSource,
		idgen.WireSet,
		simulatorOpbProcessor.WireSet,
		onemoney.NewService,
	)
	return &onemoney.Service{}
}

func InitializeFinvuAAService(simDb types.SimulatorCRDB, conf *config.Config, cc grpc.ClientConnInterface, dynConf *genconf.Config) *finvu.Service {
	wire.Build(client.NewHttpClient, onemoney.NewOneMoneyDB, sms.NewSMSClient,
		finvu.NewService,
		dao.WireSet,
		idgen.NewCryptoSeededSource,
		idgen.WireSet,
		simulatorOpbProcessor.WireSet,
		types.SimulatorCRDBGormDBProvider,
	)
	return &finvu.Service{}
}

func InitializeVeri5LivenessService() *veri5.LivenessService {
	wire.Build(veri5.NewLivenessService)
	return &veri5.LivenessService{}
}

func InitializeKarzaLivenessService(conf *config.Config) *karza.LivenessService {
	wire.Build(karza.NewLivenessService)
	return &karza.LivenessService{}
}

// config: {"s3": "Application().Liveness.LivenessBucketName"}
func InitializeInHouseLivenessService(s3 types2.LivenessS3Client, conf *config.Config, userClient userPb.UsersClient) *inhouseLivPb.InhouseLivenessService {
	wire.Build(inhouseLivPb.NewInhouseLivenessService, types2.LivenessS3ClientProvider)
	return &inhouseLivPb.InhouseLivenessService{}
}

func InitializeFederalSavingsService(simDb types.SimulatorCRDB) *fedSavings.SavingsService {
	wire.Build(fedSavings.NewSavingsService, dao.WireSet, types.SimulatorCRDBGormDBProvider)
	return &fedSavings.SavingsService{}
}

func InitialiseFederalFundTransferService(simDb types.SimulatorCRDB, db types2.SimulatorSqlDB, federalPaymentEnginePublisher types2.FederalPaymentEngineDelayPublisher, conf *config.Config, genConf *genconf.Config) (*fedFundTransfer.FundTransferService, error) {
	wire.Build(fedFundTransfer.NewFundTransferService, dao.WireSet,
		dao.NewCustomerStorage,
		types.SimulatorCRDBGormDBProvider,
		types2.SimulatorSqlDBProvider,
		types2.FederalPaymentEngineDelayPublisherProvider,
		idgen.NewClock,
		idgen.WireSet,
		loadTimeZoneLocation,
	)
	return &fedFundTransfer.FundTransferService{}, nil
}
func InitializeFederalPaymentEngineConsumer(simDb types.SimulatorCRDB, federalPaymentCallBackPublisher types2.FederalPaymentCallBackDelayPublisher, conf *config.Config) *consumer.PaymentEngine {
	wire.Build(
		consumer.NewPaymentEngine,
		idgen.NewClock,
		types.SimulatorCRDBGormDBProvider,
		idgen.WireSet,
		dao.WireSet,
		types2.FederalPaymentCallBackDelayPublisherProvider,
	)
	return &consumer.PaymentEngine{}
}

func InitializeFederalPaymentCallBackConsumer(simDb types.SimulatorCRDB, complaintPublisher types2.ComplaintStatusDelayPublisher) *consumer.PaymentCallBack {
	wire.Build(consumer.NewPaymentCallBack, client.NewHttpClient, dao.WireSet, types.SimulatorCRDBGormDBProvider, types2.ComplaintStatusDelayPublisherProvider)
	return &consumer.PaymentCallBack{}
}

func InitializeFederalCardProvisioningService(conf *config.Config, gConf *genconf.Config, federalCardCallbackPublisher types2.FederalCardCallBackPublisher, physicalDispatchCallbackPublisher card.FederalCardDispatchCallBackPublisher, simDb types.SimulatorCRDB) *card.CardProvisioningService {
	wire.Build(
		dao.WireSet,
		types.SimulatorCRDBGormDBProvider,
		card.NewCardProvisioningService,
		types2.FederalCardCallBackPublisherProvider,
		federalCardDataRsaCryptor,
		ProvideCardCreditBlockValidationConfig,
	)
	return &card.CardProvisioningService{}
}

func ProvideCardCreditBlockValidationConfig(gConf *genconf.Config) *genconf.CardCredBlockValidationParams {
	return gConf.CardCredBlockValidationParams()
}

func InitializeFederalCardCallBackConsumer() *fedCard.CardCallBack {
	wire.Build(fedCard.NewCardCallBack, client.NewHttpClient)
	return &fedCard.CardCallBack{}
}

func InitializeFederalShippingPreferenceService(updateShippingAddressPublisher types2.FederalShippingAddressUpdateDelayPublisher) *shippingPreference.ShippingPreferenceService {
	wire.Build(shippingPreference.NewShippingPreferenceService, types2.FederalShippingAddressUpdateDelayPublisherProvider)
	return &shippingPreference.ShippingPreferenceService{}
}

func InitializeFederalShippingAddressUpdateCallbackConsumer() *shippingPreferenceCb.ShippingAddressUpdateCallBackService {
	wire.Build(shippingPreferenceCb.NewShippingAddressUpdateCallBackService, client.NewHttpClient)
	return &shippingPreferenceCb.ShippingAddressUpdateCallBackService{}
}

func InitializeFederalPANService(conf *config.Config, dynConf *genconf.Config) *federalPAN.PANService {
	wire.Build(federalPAN.NewPANService)
	return &federalPAN.PANService{}
}

func InitializeFederalUNNameCheckService(conf *config.Config) *federalNameCheck.UNNameCheckService {
	wire.Build(federalNameCheck.NewUNNameCheckService)
	return &federalNameCheck.UNNameCheckService{}
}

func InitializeUpiService(
	dynConf *genconf.Config,
	simDb types.SimulatorCRDB,
	db *sql.DB,
	inboundTransactionNotificationPublisher types2.FederalInboundTransactionNotificationPublisher,
	upiCallBackPublisher types2.FederalUpiCallbackDelayPublisher,
	complaintStatus federalUpi.ComplaintPublisher,
	upiCallbackUrl config.UPICallbackUrl,
	addFundsConfig *config.AddFunds,
	inboundTxnNotificationConfig *config.InboundTxnNotification,
	inboundTxnNotificationSenderCode string,
	config *config.Config,
) *federalUpi.UpiService {
	wire.Build(federalUpi.NewUpiService, dao.WireSet, dao.NewCustomerStorage,
		types.SimulatorCRDBGormDBProvider,
		types2.FederalInboundTransactionNotificationPublisherProvider,
		types2.FederalUpiCallbackDelayPublisherProvider,
	)
	return &federalUpi.UpiService{}
}

func InitializeFederalAuthService(db types2.SimulatorSqlDB, simDb types.SimulatorCRDB, conf *config.Config) *federalAuth.AuthService {
	wire.Build(
		federalAuth.NewAuthService,
		dao.WireSet,
		client.NewHttpClient,
		idgen.NewCryptoSeededSource,
		idgen.WireSet,
		simulatorOpbProcessor.WireSet,
		types.SimulatorCRDBGormDBProvider,
		simulator.NewCustomerService,
		types2.SimulatorSqlDBProvider,
		dao.NewCustomerStorage,
	)
	return &federalAuth.AuthService{}
}

func InitializeKarzaEmploymentService() *karzaEmpl.EmploymentService {
	wire.Build(karzaEmpl.NewEmploymentService)
	return &karzaEmpl.EmploymentService{}
}

func InitializeFreshdeskService() *freshdesk.Service {
	wire.Build(freshdesk.NewService)
	return &freshdesk.Service{}
}

func InitializeOzonetelService() *ozonetel.Service {
	wire.Build(ozonetel.NewService)
	return &ozonetel.Service{}
}

func InitializeWatsonClientService() *watson_client.Service {
	wire.Build(watson_client.NewService)
	return &watson_client.Service{}
}

func InitializeFederalDepositService(simDb types.SimulatorCRDB, depositEnginePublisher types2.FederalDepositEnginePublisher, conf *config.Config) *federalDeposit.DepositService {
	wire.Build(federalDeposit.NewDepositService, dao.WireSet,
		types.SimulatorCRDBGormDBProvider,
		types2.FederalDepositEnginePublisherProvider,
	)
	return &federalDeposit.DepositService{}
}

func InitializeFederalDepositEngineConsumer(simDb types.SimulatorCRDB, depositCallbackPublisher types2.FederalDepositCallBackPublisher, conf *config.Config) *depositConsumer.DepositEngine {
	wire.Build(
		depositConsumer.NewDepositEngine,
		idgen.NewCryptoSeededSource,
		idgen.NewClock,
		idgen.WireSet,
		dao.WireSet,
		types.SimulatorCRDBGormDBProvider,
		types2.FederalDepositCallBackPublisherProvider,
	)
	return &depositConsumer.DepositEngine{}
}

func InitializeFederalDepositCallbackConsumer() *depositConsumer.DepositCallBack {
	wire.Build(depositConsumer.NewDepositCallback, client.NewHttpClient)
	return &depositConsumer.DepositCallBack{}
}

func InitializeFederalInboundTransactionNotificationConsumer(conf *config.Config) *consumer.InboundTransactionNotificationConsumer {
	wire.Build(consumer.NewInboundTransactionNotificationConsumer, client.NewHttpClient)
	return &consumer.InboundTransactionNotificationConsumer{}
}

func InitializeFederalPartnerSDKService(conf *config.Config, simDb types.SimulatorCRDB) *federalPartnerSDK.Service {
	wire.Build(
		getPartnerSdkMap,
		getPartnerSdkJavaUrl,
		federalPartnerSDK.NewService,
		dao.WireSet,
		types.SimulatorCRDBGormDBProvider,
	)
	return &federalPartnerSDK.Service{}
}

func InitializeM2PPartnerSDKService(conf *config.Config, simDb types.SimulatorCRDB) *m2pPartnerSDK.Service {
	wire.Build(
		getPartnerSdkMap,
		getPartnerSdkJavaUrl,
		m2pPartnerSDK.NewService,
		dao.WireSet,
		types.SimulatorCRDBGormDBProvider,
	)
	return &m2pPartnerSDK.Service{}
}

func InitialiseFederalB2CFundTransferService(simDb types.SimulatorCRDB, federalPaymentEnginePublisher types2.FederalPaymentEngineDelayPublisher, conf *config.Config) (*fedB2CFundTransfer.B2CFundTransferService, error) {
	wire.Build(fedB2CFundTransfer.NewB2CFundTransferService, dao.WireSet,
		types.SimulatorCRDBGormDBProvider,
		types2.FederalPaymentEngineDelayPublisherProvider,
		loadTimeZoneLocation,
	)
	return &fedB2CFundTransfer.B2CFundTransferService{}, nil
}

func InitializeFederalStandingInstructionService(simDb types.SimulatorCRDB, federalPaymentCallbackPublisher types2.FederalPaymentCallBackDelayPublisher, conf *config.Config) (*fedStandingInstruction.StandingInstructionService, error) {
	wire.Build(
		fedStandingInstruction.NewStandingInstructionService,
		idgen.NewClock,
		idgen.WireSet,
		dao.WireSet,
		types.SimulatorCRDBGormDBProvider,
		types2.FederalPaymentCallBackDelayPublisherProvider,
	)
	return &fedStandingInstruction.StandingInstructionService{}, nil
}

func InitialiseFederalAccountCreationService(db types2.SimulatorSqlDB, simDb types.SimulatorCRDB, callbackPublisher types2.FederalAccountsCallbackDelayPublisher, conf *genconf.Config,
	usersClient userPb.UsersClient, savingsClient savingsPb.SavingsClient) *federal2.AccountCreationService {
	wire.Build(
		dao.NewCustomerStorage,
		federal2.NewAccountCreationService,
		dao.WireSet,
		idgen.NewCryptoSeededSource,
		idgen.WireSet,
		simulatorOpbProcessor.WireSet,
		types.SimulatorCRDBGormDBProvider,
		types2.SimulatorSqlDBProvider,
		types2.FederalAccountsCallbackDelayPublisherProvider,
	)
	return &federal2.AccountCreationService{}
}

func InitializeEnquiryService(db types2.SimulatorSqlDB, simDb types.SimulatorCRDB, conf *config.Config) *enquiry.EnquiryService {
	wire.Build(
		dao.WireSet,
		dao.NewCustomerStorage,
		enquiry.NewEnquiryService,
		types.SimulatorCRDBGormDBProvider,
		types2.SimulatorSqlDBProvider,
		federalCardDataRsaCryptor,
	)
	return &enquiry.EnquiryService{}
}

func InitializeVKYCService(dynConf *genconf.Config, simDb types.SimulatorCRDB) *simKarza.VKYCService {
	wire.Build(
		simKarza.NewVKYCService,
		dao.NewEkycStorage,
		types.SimulatorCRDBGormDBProvider,
	)
	return &simKarza.VKYCService{}
}

func InitializeLoyltyService(cryptor crypto.Cryptor) *simLoylty.LoyltyService {
	wire.Build(
		simLoylty.NewLoyltyService,
	)
	return &simLoylty.LoyltyService{}
}

func InitiaLeadSquaredService() *leadsquared.LeadSquaredService {
	wire.Build(
		leadsquared.NewLeadSquaredService,
	)
	return &leadsquared.LeadSquaredService{}
}

func InitializeSmsService() *sms2.SmsService {
	wire.Build(sms2.NewSmsService)
	return &sms2.SmsService{}
}

func InitializeCallbackConsumer() *accountcb.AccountsCallback {
	wire.Build(accountcb.NewAccountsCallback, client.NewHttpClient)
	return &accountcb.AccountsCallback{}
}

func InitialiseFitttService(simDb types.SimulatorCRDB) *fittt.FitttService {
	wire.Build(
		dao.FitRoanuzResponsesDaoWireSet,
		fittt.NewFitttService,
		types.SimulatorCRDBGormDBProvider,
	)
	return &fittt.FitttService{}
}

func InitializeIpStackService() *ipstack.IPStackService {
	wire.Build(
		ipstack.NewIPStackService,
	)
	return &ipstack.IPStackService{}
}

func InitializeWhatsappService(aclDlrStatusCallbackUrl string) *whatsapp.WhatsappService {
	wire.Build(
		client.NewHttpClient,
		whatsapp.NewWhatsappService,
	)
	return &whatsapp.WhatsappService{}
}

func InitializeNamematchService() *inhouse.NameMatchService {
	wire.Build(
		inhouse.NewNamematchService,
	)
	return &inhouse.NameMatchService{}
}

func InitialiseShipwayService() *shipway.ShipwayService {
	wire.Build(
		shipway.NewShipwayService,
	)
	return &shipway.ShipwayService{}
}

func InitializeCreditReportService() *experian.Service {
	wire.Build(
		experian.NewCreditReportService,
	)
	return &experian.Service{}
}

func InitializeBcService() *bouncycastle.Service {
	wire.Build(bouncycastle.NewService)
	return &bouncycastle.Service{}
}

func InitialiseShipwayService2() *shipway.ShipwayService2 {
	wire.Build(
		shipway.NewShipwayService2,
	)
	return &shipway.ShipwayService2{}
}

func InitializeWealthCvlService(stubFilePath string, dynConf *genconf.Config) *wealth.CvlService {
	wire.Build(wealth.NewCvlService)
	return &wealth.CvlService{}
}

func InitializeWealthCkycService(stubFilePath string) *wealth.CkycService {
	wire.Build(wealth.NewCkycService)
	return &wealth.CkycService{}
}

func InitializeWealthNsdlService(stubFilePath string) *wealth.NsdlService {
	wire.Build(wealth.NewNsdlService)
	return &wealth.NsdlService{}
}

func InitializeWealthManchService(simDb types.SimulatorCRDB) *wealth.ManchService {
	wire.Build(
		wealth.WireSet,
		wealth.NewManchService,
		types.SimulatorCRDBGormDBProvider,
	)
	return &wealth.ManchService{}
}

func InitializeDLValidationService() *dl.DLService {
	wire.Build(dl.NewDLService)
	return &dl.DLService{}
}

func InitializeWealthKarzaService() *wealth.KarzaService {
	wire.Build(wealth.NewKarzaService)
	return &wealth.KarzaService{}
}

func InitializeSeonService() *seon.Service {
	wire.Build(seon.NewService)
	return &seon.Service{}
}

func InitializeIdValidateService() *idvalidate.IdValidateService {
	wire.Build(idvalidate.NewIdValidateService)
	return &idvalidate.IdValidateService{}
}

func InitializeCamsService(s3Client s3.S3Client) *cams.CamsService {
	wire.Build(cams.NewCamsService)
	return &cams.CamsService{}
}

func InitializeKarvyService() *karvy.KarvyService {
	wire.Build(karvy.NewKarvyService)
	return &karvy.KarvyService{}
}

// config: {"camsS3Client": "Application().Cams.CamsS3Bucket"}
func InitializeCamsConsumerService(camsS3Client types2.CamsS3Client, conf *config.Config) *cams.ConsumerService {
	wire.Build(cams.NewConsumerService, types2.CamsS3ClientProvider)
	return &cams.ConsumerService{}
}

func InitializeWealthDigilockerService(conf *config.Config) *wealth.DigilockerService {
	wire.Build(wealth.NewDigilockerService, getStubFilePath)
	return &wealth.DigilockerService{}
}

func InitializeInternationalFundTransfer(conf *genconf.Config) *internationalfundtransfer.InternationalFundTransferService {
	wire.Build(internationalfundtransfer.NewInternationalFundTransferService)
	return &internationalfundtransfer.InternationalFundTransferService{}
}

func InitializeLiquiloansService(simDb types.SimulatorCRDB, celestialClient celestialPb.CelestialClient, p2pBeClient p2pPb.P2PInvestmentClient, vnPayClient fedPay.PaymentClient) *liquiloans.Service {
	wire.Build(
		liquiloansDao.VendorResponseDaoWireSet,
		helper.CommonHelperWireSet,
		liquiloans.NewService,
		types.SimulatorCRDBGormDBProvider,
	)
	return &liquiloans.Service{}
}

func InitializePreApprovedLoanService(simDb types.SimulatorCRDB, db types2.SimulatorSqlDB, vnLoansAbflClient vnLoansAbflPb.AbflCallbackClient, conf *config.Config, userClient userPb.UsersClient, dynConf *genconf.Config) *preapprovedloan.Service {
	wire.Build(
		preapprovedDao.VendorResponseDaoWireSet,
		dao.WireSet,
		dao.NewCustomerStorage,
		preapprovedloan.NewService,
		types.SimulatorCRDBGormDBProvider,
		types2.SimulatorSqlDBProvider,
		getPlStubPathConfig,
		getLendingGenConf,
	)
	return &preapprovedloan.Service{}
}

func getPlStubPathConfig(conf *config.Config) map[string]string {
	return conf.PlStubs
}

func getLendingGenConf(dynConf *genconf.Config) *genconf.Lending {
	return dynConf.Lending()
}

func InitializeExternalValidateService() *extvalidate.Service {
	wire.Build(extvalidate.NewService)
	return &extvalidate.Service{}
}

func InitializeLocationService() *location.Service {
	wire.Build(location.NewService)
	return &location.Service{}
}

func InitializeSimulatorP2PDevService(simDb types.SimulatorCRDB, celestialClient celestialPb.CelestialClient, p2pDevClient p2pDsPb.DevP2PInvestmentClient, p2pBeClient p2pPb.P2PInvestmentClient, vnPayClient fedPay.PaymentClient) *developer.SimulatorP2PDevEntity {
	wire.Build(liquiloansDao.P2PVendorResponseWireSet, helper.CommonHelperWireSet, developer.NewSimulatorP2PDevService, types.SimulatorCRDBGormDBProvider)
	return &developer.SimulatorP2PDevEntity{}
}

func InitializePhoneNetworkService() *phonenetwork.Service {
	wire.Build(phonenetwork.NewService)
	return &phonenetwork.Service{}
}

func InitialiseInhouseRiskService() *risk.Service {
	wire.Build(risk.NewService)
	return &risk.Service{}
}

func InitializeAccountsDevService(simDb types.SimulatorCRDB) *accountsDevSvc.AccountsDevService {
	wire.Build(
		dao.WireSet,
		accountsDevSvc.NewAccountsDevService,
		types.SimulatorCRDBGormDBProvider,
	)
	return &accountsDevSvc.AccountsDevService{}
}

func InitializeUSStockService(conf *genconf.Config, simDb types.SimulatorCRDB, stubs map[string]string) *alpaca.StocksService {
	wire.Build(
		alpaca.NewStocksService,
		ussOrdersImpl.USSFundTransferWireSet,
		ussOrdersImpl.USSJournalWireSet,
		ussOrdersImpl.USSOrderWireSet,
		types.SimulatorCRDBGormDBProvider,
	)
	return &alpaca.StocksService{}
}

func InitializeEsignService() *esign.Service {
	wire.Build(
		esign.NewService)
	return &esign.Service{}
}

func InitializeProfileEvaluatorService(simDb types.SimulatorCRDB, gConf *genconf.Config) *profileEvaluatorPb.Service {
	wire.Build(
		profileEvaluatorDao.ProfileEvaluatorDaoWireSet,
		profileEvaluatorPb.NewService,
		types.SimulatorCRDBGormDBProvider,
	)
	return &profileEvaluatorPb.Service{}
}

func InitializeTssService(conf *genconf.Config) *tss.TssService {
	wire.Build(tss.NewTssService)
	return &tss.TssService{}
}

func InitializeChequebookService(dynConf *genconf.Config) *federalChkbq.Service {
	wire.Build(federalChkbq.NewService)
	return &federalChkbq.Service{}
}

func InitializeMFCentralService(conf *genconf.Config, handler *vendorapi.HTTPRequestHandler, db *gorm.DB) *mfcentral.Service {
	wire.Build(
		mfcentral.NewMFCentralService,
		impl.MfUsersDaoWireset,
		impl.MfRequestsDaoWireSet,
	)
	return &mfcentral.Service{}
}

func InitializeSmallcaseService(conf *genconf.Config, vendorApiHttpHandler *vendorapi.HTTPRequestHandler, db *gorm.DB) *smallcase.Service {
	wire.Build(
		client.NewHttpClient,
		smallcase.NewSmallCaseService,
		impl.MfUsersDaoWireset,
		impl.MfRequestsDaoWireSet,
	)
	return &smallcase.Service{}
}

func InitializeOpenbankingCustomerService(dynConf *genconf.Config, db types2.SimulatorSqlDB, simDb types.SimulatorCRDB, callbackPublisher types2.FederalAccountsCallbackDelayPublisher) *customer.CustomerService {
	wire.Build(
		client.NewHttpClient,
		customer.NewCustomerService,
		types.SimulatorCRDBGormDBProvider,
		types2.SimulatorSqlDBProvider,
		dao.NewCustomerStorage,
		idgen.NewCryptoSeededSource,
		idgen.WireSet,
		types2.FederalAccountsCallbackDelayPublisherProvider,
	)
	return &customer.CustomerService{}
}

func InitializeEPANService(dynConf *genconf.Config) *epan.Service {
	wire.Build(epan.NewService, client.NewHttpClient)
	return &epan.Service{}
}

func InitializeITRService(dynConf *genconf.Config) *itr.Service {
	wire.Build(itr.NewService, client.NewHttpClient)
	return &itr.Service{}
}

func InitializeDmpFederalDisputeService() *dmpDispute.Service {
	wire.Build(dmpDispute.NewService)
	return &dmpDispute.Service{}
}

func InitializeFederalLienService() *federal3.LienService {
	wire.Build(federal3.NewLienService)
	return &federal3.LienService{}
}

func InitialiseCreditCardService(simDb types.SimulatorCRDB, vnCcClient vnCcPb.CreditCardClient) *creditCardFlows.Service {
	wire.Build(
		creditCardDao.CreditCardVendorResponseDaoWireSet,
		creditCardFlows.NewService,
		types.SimulatorCRDBGormDBProvider,
	)
	return &creditCardFlows.Service{}
}

func InitialiseCreditLineService() *creditLine.Service {
	wire.Build(
		creditLine.NewService,
	)
	return &creditLine.Service{}
}

func InitialiseTxnCategorizerService(gconf *genconf.Config) *categorizer.TxnCategorizerService {
	wire.Build(
		dsClientProvider,
		vendorClient.NewHttpClient,
		categorizer.NewTxnCategorizerService,
	)
	return &categorizer.TxnCategorizerService{}
}

func dsClientProvider(gconf *genconf.Config) *cfg.HttpClient {
	return gconf.DsCategorizerClientConfig()
}

func InitialiseQwikcilverService() *qwikcilver.Service {
	return &qwikcilver.Service{}
}

func incomeEstimatorConfProvider(gconf *genconf.Config) *genconf.IncomeEstimator {
	return gconf.IncomeEstimator()
}

func InitialiseIncomeEstimatorService(gconf *genconf.Config,
	dobbyRedisStore types2.DobbyCacheStorage, userClient userPb.UsersClient) *incomeestimator.Service {
	wire.Build(
		incomeEstimatorConfProvider,
		incomeestimator.NewService,
	)
	return &incomeestimator.Service{}
}

func InitializeTieringService() *tiering.Service {
	wire.Build(tiering.NewService)
	return &tiering.Service{}
}

func InitializeEnachService(simDb types.SimulatorCRDB, conf *config.Config, inboundTransactionNotificationDelayPublisher types2.FederalInboundTransactionNotificationDelayPublisher) (*federal4.EnachService, error) {
	wire.Build(
		idgen.NewClock,
		idgen.WireSet,
		dao.WireSet,
		federal4.NewEnachService,
		types.SimulatorCRDBGormDBProvider,
		types2.FederalInboundTransactionNotificationDelayPublisherProvider,
		getEnachSftpClient,
	)
	return &federal4.EnachService{}, nil
}

func loadTimeZoneLocation(conf *config.Config) (*time.Location, error) {
	// getting time Zone location
	timeZoneLocation, locErr := time.LoadLocation(conf.PaymentProtocolDecisionParams.TimeZoneLocation)
	if locErr != nil {
		return nil, locErr
	}
	return timeZoneLocation, nil
}

func federalCardDataRsaCryptor(conf *config.Config) crypto.Cryptor {
	// create a pgp cryptor to be used for vendor communication encryption
	rsaCryptor := rsa.NewEncryptOnlyCryptor(
		conf.Secrets.Ids[config.EpifiFederalRsaPublicKey])
	return rsaCryptor
}

func getStubFilePath(conf *config.Config) string {
	return conf.Stubs["wealthonb"]
}

func getEnachSftpClient(conf *config.Config) (sftp.ISftp, error) {
	sftpClientConfig, err := sftp.NewClientConfigInsecure(conf.Secrets.Ids[config.CommonSftpUser], conf.Secrets.Ids[config.CommonSftpPass], conf.EnachConfig.PresentationSftpConn.Host, conf.EnachConfig.PresentationSftpConn.Port)
	if err != nil {
		return nil, err
	}

	return sftp.NewSftp(sftpClientConfig), nil
}

func getPartnerSdkMap(conf *config.Config) map[string]*config.PartnerSDK {
	return conf.Application.PartnerSdkKeyMap
}

func getPartnerSdkJavaUrl(conf *config.Config) string {
	return conf.Application.PartnerSDKJavaUrl
}

func InitializeFiftyfinService(simDb types.SimulatorCRDB, stubFilePath string) *fiftyfin.Service {
	wire.Build(
		securedloans.UserWireSet,
		dbDataStore.DataStoreWireSet,
		impl.MfUsersDaoWireset,
		fiftyfin.NewService,
		types.SimulatorCRDBGormDBProvider,
	)
	return &fiftyfin.Service{}
}

func InitialiseCibilService(gconf *genconf.Config) *cibil.Service {
	wire.Build(
		cibil.NewService,
	)
	return &cibil.Service{}
}

func InitKarzaService() *mykarza.KarzaService {
	wire.Build(mykarza.NewKarzaService)
	return &mykarza.KarzaService{}
}

func InitialiseDobbyService(gconf *genconf.Config, dobbyRedisStore types2.DobbyCacheStorage, userClient userPb.UsersClient) *dobby.Service {
	wire.Build(
		dobby.NewDobbyService,
		onboarding.NewVKYCFailProcessor,
		onboarding.NewIncomeEstimateProcessor,
		onboarding.NewPassLendabilityProcessor,
		onboarding.NewFailLendabilityProcessor,
	)
	return &dobby.Service{}

}

func InitialiseOCRService(gconf *genconf.Config) *ocr.Service {
	wire.Build(
		ocr.NewOCRSimulatorService,
	)
	return &ocr.Service{}
}

func InitialiseKarzaOCRService(gconf *genconf.Config) *karzaOcr.Service {
	wire.Build(
		karzaOcr.NewKarzaOCRSimulatorService,
	)
	return &karzaOcr.Service{}
}

func InitialiseFennelService(dobbyRedisStore types2.DobbyCacheStorage, userClient userPb.UsersClient) *fennel.Service {
	wire.Build(
		fennel.NewFennelService,
	)
	return &fennel.Service{}
}

func InitialiseDreamfolksService() *dreamfolks.Service {
	wire.Build(dreamfolks.NewService)
	return &dreamfolks.Service{}
}

func InitialiseFederalVKYCService() *vkycFederal.Service {
	wire.Build(vkycFederal.NewService)
	return &vkycFederal.Service{}
}

func InitialiseDocsService() *docs.Service {
	wire.Build(docs.NewService)
	return &docs.Service{}
}

func InitialiseOnsurityService(simDb types.SimulatorCRDB, gconf *genconf.Config) *onsurity.Service {
	wire.Build(
		onsurity.NewService,
		onsurity.OnsurityDaoWireSet,
		types.SimulatorCRDBGormDBProvider,
	)
	return &onsurity.Service{}
}

func InitialiseSetuService(gconf *genconf.Config) *setu.Service {
	wire.Build(
		setu.NewService,
	)
	return &setu.Service{}
}

func InitialiseScienapticService() *scienaptic.Service {
	wire.Build(scienaptic.NewService)
	return &scienaptic.Service{}
}

func InitialiseIgnosisAaService(stubs map[string]string) *ignosisSim.IgnosisAaService {
	wire.Build(
		ignosisSim.NewIgnosisAaService,
	)
	return &ignosisSim.IgnosisAaService{}
}

func InitializeSavenCreditCardService(conf *config.Config) (*saven.Service, error) {
	wire.Build(
		saven.NewService,
	)
	return &saven.Service{}, nil
}

func InitializeInhouseBreService() (*inhousebre.Service, error) {
	wire.Build(
		inhousebre.NewService,
	)
	return &inhousebre.Service{}, nil
}
