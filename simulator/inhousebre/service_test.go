package inhousebre

import (
	"testing"
)

func TestService_loadStub(t *testing.T) {
	tests := []struct {
		name    string
		wantErr bool
	}{
		{
			"happy case",
			false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{}
			if err := s.loadStub(); (err != nil) != tt.wantErr {
				t.Errorf("loadStub() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
