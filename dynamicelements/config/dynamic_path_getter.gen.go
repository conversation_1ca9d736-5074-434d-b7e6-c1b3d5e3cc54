// Code generated by tools/conf_gen/dynamic_conf_gen.go
package config

import (
	"fmt"
	"strings"
)

func (obj *Config) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "maximumelements":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.MaximumElements, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"MaximumElements\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.MaximumElements[dynamicFieldPath[1]], nil

		}
		return obj.MaximumElements, nil
	case "releaseconfig":
		return obj.ReleaseConfig.Get(dynamicFieldPath[1:])
	case "questsdk":
		return obj.QuestSdk.Get(dynamicFieldPath[1:])
	case "appupdatebannerconfig":
		return obj.AppUpdateBannerConfig.Get(dynamicFieldPath[1:])
	case "questvariables":
		return obj.QuestVariables.Get(dynamicFieldPath[1:])
	case "featurereleaseconfig":
		return obj.FeatureReleaseConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Config", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ReleaseConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isrestrictedreleaseenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsRestrictedReleaseEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsRestrictedReleaseEnabled, nil
	case "isenabledforplatform":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.IsEnabledForPlatform, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"IsEnabledForPlatform\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.IsEnabledForPlatform[dynamicFieldPath[1]], nil

		}
		return obj.IsEnabledForPlatform, nil
	case "isenabledforusergroup":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.IsEnabledForUserGroup, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"IsEnabledForUserGroup\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.IsEnabledForUserGroup[dynamicFieldPath[1]], nil

		}
		return obj.IsEnabledForUserGroup, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ReleaseConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *AppUpdateBannerConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "androidbumpversion":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AndroidBumpVersion\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AndroidBumpVersion, nil
	case "iosbumpversion":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IosBumpVersion\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IosBumpVersion, nil
	case "minsupportversionandroid":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinSupportVersionAndroid\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinSupportVersionAndroid, nil
	case "minsupportversionios":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinSupportVersionIos\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinSupportVersionIos, nil
	case "minsupportversionandroidforflexibleappupdate":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinSupportVersionAndroidForFlexibleAppUpdate\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinSupportVersionAndroidForFlexibleAppUpdate, nil
	case "minsupportversioniosforflexibleappupdate":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinSupportVersionIosForFlexibleAppUpdate\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinSupportVersionIosForFlexibleAppUpdate, nil
	case "externalredirectionurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ExternalRedirectionUrl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ExternalRedirectionUrl, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for AppUpdateBannerConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *QuestVariables) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "gtmpopup":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"GTMPopUp\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.GTMPopUp, nil
	case "homebanner":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"HomeBanner\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.HomeBanner, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for QuestVariables", strings.Join(dynamicFieldPath, "."))
	}
}
