Application:
  Environment: "staging"
  Name: "vendormapping"

Server:
  Ports:
    GrpcPort: 7007
    GrpcSecurePort: 9523
    HttpPort: 9999
    HttpPProfPort: 9990

EpifiDb:
  AppName: "vendormapping"
  StatementTimeout: 1s
  Name: "vendormapping"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

VendorMappingDb:
  AppName: "vendormapping"
  StatementTimeout: 1s
  Name: "vendormapping"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

Aws:
  Region: "ap-south-1"

Secrets:
  Ids:
    DbUsernamePassword: "staging/rds/postgres14"
    VendorMappingDbUsernamePassword: "staging/rds/postgres14"

Flags:
  EnableDoubleWrites: false
  TrimDebugMessageFromStatus: false
  UseVendorMappingDbAsPrimaryRead: false

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 10

VendorMappingConsentSyncSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-vm-consent-sync-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 6
      MaxAttempts: 10
      TimeUnit: "Minute"

AmplitudeConfig:
  AmplitudePercentageSent: 25

Tracing:
  Enable: true

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

ActorCreationEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-vendormapping-actor-creation-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 9
      TimeUnit: "Minute"
