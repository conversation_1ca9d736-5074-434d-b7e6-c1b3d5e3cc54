package developer

import (
	"context"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	cxDsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/user/developer"
)

type UserDevService struct {
	fac *DevFactory
}

func NewUserDevService(fac *DevFactory) *UserDevService {
	return &UserDevService{
		fac: fac,
	}
}

func (c *UserDevService) GetEntityList(ctx context.Context, req *cxDsPb.GetEntityListRequest) (*cxDsPb.GetEntityListResponse, error) {
	return &cxDsPb.GetEntityListResponse{
		Status: rpcPb.StatusOk(),
		EntityList: []string{
			developer.OnboardingEntity_ONBOARDING_STATE_ACTOR.String(),
			developer.OnboardingEntity_ONBOARDING_CROSS_VALIDATION.String(),
			developer.OnboardingEntity_USER.String(), developer.OnboardingEntity_USER_GROUP_MAPPING.String(),
			developer.OnboardingEntity_SHIPPING_PREFERENCE.String(),
			developer.OnboardingEntity_USER_CONTACT.String(),
			developer.OnboardingEntity_ONBOARDING_VENDOR_RESPONSES.String(),
			developer.OnboardingEntity_CREDIT_REPORT.String(),
			developer.OnboardingEntity_CONSENT.String(),
			developer.OnboardingEntity_CHANGE_FEED.String(),
			developer.OnboardingEntity_NOMINEE.String(),
			developer.OnboardingEntity_USER_INTEL.String(),
			developer.OnboardingEntity_CREDIT_REPORT_USER_SUBSCRIPTION.String(),
			developer.OnboardingEntity_CREDIT_REPORT_DOWNLOAD.String(),
			developer.OnboardingEntity_TEMP_CREDIT_REPORT_MANUAL_FETCH.String(),
			developer.OnboardingEntity_USER_DEVICE_PROPERTIES.String(),
			developer.OnboardingEntity_USER_PREFERENCES.String(),
		},
	}, nil
}

func (c *UserDevService) GetParameterList(ctx context.Context, req *cxDsPb.GetParameterListRequest) (*cxDsPb.GetParameterListResponse, error) {
	ent, ok := developer.OnboardingEntity_value[req.GetEntity()]
	if !ok {
		return &cxDsPb.GetParameterListResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("entity type passed is not available in user"),
		}, nil
	}
	paramFetcher, err := c.fac.getParameterListImpl(developer.OnboardingEntity(ent))
	if err != nil {
		logger.Error(ctx, "entity type passed implementation is not available in user")
		return &cxDsPb.GetParameterListResponse{Status: rpcPb.StatusInternal()}, nil
	}
	paramList, err := paramFetcher.FetchParamList(ctx, developer.OnboardingEntity(ent))
	if err != nil {
		logger.Error(ctx, "unable to fetch parameter list")
		return &cxDsPb.GetParameterListResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return &cxDsPb.GetParameterListResponse{
		Status:        rpcPb.StatusOk(),
		ParameterList: paramList,
	}, nil
}

func (c *UserDevService) GetData(ctx context.Context, req *cxDsPb.GetDataRequest) (*cxDsPb.GetDataResponse, error) {
	ent, ok := developer.OnboardingEntity_value[req.GetEntity()]
	if !ok {
		return &cxDsPb.GetDataResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("entity type passed is not available in user"),
		}, nil
	}
	dataFetcher, err := c.fac.getDataImpl(developer.OnboardingEntity(ent))
	if err != nil {
		logger.Error(ctx, "entity type passed implementation is not available in user")
		return &cxDsPb.GetDataResponse{Status: rpcPb.StatusInternal()}, nil
	}
	jsonResp, err := dataFetcher.FetchData(ctx, developer.OnboardingEntity(ent), req.GetFilters())
	if err != nil {
		logger.Error(ctx, "unable to get data", zap.Error(err))
		if storageV2.IsRecordNotFoundError(err) {
			return &cxDsPb.GetDataResponse{Status: rpcPb.StatusRecordNotFound()}, nil
		}
		return &cxDsPb.GetDataResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return &cxDsPb.GetDataResponse{
		Status:       rpcPb.StatusOk(),
		JsonResponse: jsonResp,
	}, nil
}
