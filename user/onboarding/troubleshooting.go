// nolint
package onboarding

import (
	"context"
	"fmt"
	"math"
	"strings"
	"time"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/auth/liveness"
	"github.com/epifi/gamma/api/consent"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/kyc"
	scrnrPb "github.com/epifi/gamma/api/screener"
	usersPb "github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/userintel"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	"github.com/epifi/gamma/api/vendorgateway/pan"
	vendorstorePb "github.com/epifi/gamma/api/vendorstore"
	"github.com/epifi/gamma/pkg/auth"
	"github.com/epifi/gamma/pkg/vendorstore"
	"github.com/epifi/gamma/user/config"
	"github.com/epifi/gamma/user/onboarding/dao"
	"github.com/epifi/gamma/user/onboarding/helper"
	"github.com/epifi/gamma/user/onboarding/pkg/constant"
	error2 "github.com/epifi/gamma/user/onboarding/pkg/error"
	"github.com/epifi/gamma/user/onboarding/stageproc"
)

const (
	onbTeamPOC = "@Bhabtosh / @onb-oncall"
)

var (
	stageTroubleshooters = func(s *StageTroubleshooter, stage onbPb.OnboardingStage) StageTroubleshootingProcessor {
		return map[onbPb.OnboardingStage]StageTroubleshootingProcessor{
			onbPb.OnboardingStage_REFERRAL_FINITE_CODE:               s.ReferralFiniteCodeProc,
			onbPb.OnboardingStage_TNC_CONSENT:                        s.TncConsentProc,
			onbPb.OnboardingStage_INTENT_SELECTION:                   s.IntentSelectionProc,
			onbPb.OnboardingStage_SAVINGS_INTRO_CONSENT:              s.SavingIntroConsentProc,
			onbPb.OnboardingStage_MOTHER_FATHER_NAME:                 s.MotherFatherNameProc,
			onbPb.OnboardingStage_VKYC:                               s.VkycProc,
			onbPb.OnboardingStage_PRE_CUSTOMER_CREATION_CHECK:        s.PreCustomerCreationCheckProc,
			onbPb.OnboardingStage_OPTIONAL_VKYC:                      s.OptionalVkycProc,
			onbPb.OnboardingStage_ADD_MONEY:                          s.AddmoneyProc,
			onbPb.OnboardingStage_INITIATE_CKYC:                      s.InitCkycProc,
			onbPb.OnboardingStage_KYC_AND_LIVENESS_COMPLETION:        s.KycLivenessProc,
			onbPb.OnboardingStage_KYC_DEDUPE_CHECK:                   s.KycDedupeCheckProc,
			onbPb.OnboardingStage_EKYC_NAME_DOB_VALIDATION:           s.EkycNameDobValidationProc,
			onbPb.OnboardingStage_CONFIRM_CARD_MAILING_ADDRESS:       s.CardAddressProc,
			onbPb.OnboardingStage_DEVICE_REGISTRATION:                s.DeviceRegistrationProc,
			onbPb.OnboardingStage_CUSTOMER_CREATION:                  s.CustomerCreationProc,
			onbPb.OnboardingStage_ACCOUNT_CREATION:                   s.AccountCreationProc,
			onbPb.OnboardingStage_DEDUPE_CHECK:                       s.DedupeCheckProc,
			onbPb.OnboardingStage_PAN_NAME_CHECK:                     s.PanNameCheckProc,
			onbPb.OnboardingStage_SHIPPING_ADDRESS_UPDATE:            s.ShippinAddressUpdateProc,
			onbPb.OnboardingStage_DOB_AND_PAN:                        s.DOBandPANProc,
			onbPb.OnboardingStage_CKYC:                               s.CKYCProc,
			onbPb.OnboardingStage_EKYC:                               s.EKYCProc,
			onbPb.OnboardingStage_LIVENESS:                           s.LivenessProc,
			onbPb.OnboardingStage_RISK_SCREENING:                     s.RiskScreenProc,
			onbPb.OnboardingStage_CARD_CREATION:                      s.CardCreationProcessor,
			onbPb.OnboardingStage_PRE_CUSTOMER_CREATION_DEDUPE_CHECK: s.PreCustDedupeProc,
			onbPb.OnboardingStage_APP_SCREENING:                      s.ScreenerProc,
			onbPb.OnboardingStage_UPI_CONSENT:                        s.upiConsentStage,
			onbPb.OnboardingStage_UN_NAME_CHECK:                      s.UNNameCheckProc,
			onbPb.OnboardingStage_KYC_NAME_DOB_VALIDATION:            s.KycNameDobValidationProc,
			onbPb.OnboardingStage_LOCATION_CHECK:                     s.LocationCheckProc,
			onbPb.OnboardingStage_BKYC:                               s.BKYCProc,
			onbPb.OnboardingStage_CREDIT_REPORT_CHECK:                s.CreditReportCheckProc,
			onbPb.OnboardingStage_AADHAR_MOBILE_VALIDATION:           s.AadharMobileValidationProc,
			onbPb.OnboardingStage_UPDATE_PROFILE_DETAILS:             s.UpdateProfileDetailsProc,
			onbPb.OnboardingStage_FI_LITE_RISK_SCREENING:             s.RiskScreenProc,
			onbPb.OnboardingStage_DEBIT_CARD_PIN_SETUP:               s.DebitCardPinSetupProcessor,
		}[stage]
	}

	livenessAdviceMap = map[liveness.LivenessStatus]string{
		liveness.LivenessStatus_LIVENESS_FACE_NOT_DETECTED:       "User's face not detected during liveness. Please ask user to be in front of plain background and well lit area.",
		liveness.LivenessStatus_LIVENESS_MULTIPLE_FACES_DETECTED: "Multiple face detected during liveness. Please ask user to be in front of plain background",
		liveness.LivenessStatus_LIVENESS_FACE_POORLY_DETECTED:    "User's face not detected properly during liveness. Please ask user to be in well lit room and face to be align to oval",
		liveness.LivenessStatus_LIVENESS_FACE_TOO_FAR:            "User's face too far during liveness. Please ask user to make sure his face is closer to the camera and aligned in the circle during recording.",
		liveness.LivenessStatus_LIVENESS_FACE_TOO_CLOSE:          "User's face too close during liveness.Please ask user to make usre his face is further from the camera and aligned in the circle during recording",
		liveness.LivenessStatus_LIVENESS_FACE_TOO_DARK:           "Users face is too dark during liveness. Please ask user to find a well lit area.",
		liveness.LivenessStatus_LIVENESS_FACE_TOO_BRIGHT:         "Users face is too Bright during liveness. Please ask user to find a well lit area with plain background",
		liveness.LivenessStatus_LIVENESS_NO_FACE_DETECTED:        "User's face not detected during liveness. Please ask user to be in front of plain background and well lit area.",
		liveness.LivenessStatus_LIVENESS_MANUALLY_FAILED:         "User's liveness is manually rejected by the RiskOps team. It might be due to KYC & liveness photo mismatch or the user might be trying to spoof.",
		liveness.LivenessStatus_LIVENESS_INVALID_VIDEO:           "There's an issue in user's device due to which we are receiving invalid video. Please report to the tech team.",
	}
)

type StageTroubleshootingRequest struct {
	Stage     onbPb.OnboardingStage
	Onb       *onbPb.OnboardingDetails
	NexAction *dlPb.Deeplink
}

type StageTroubleshootingResponse struct {
	Details *onbPb.StageTroubleshootingDetails
}

type StageTroubleshootingProcessor interface {
	StageTroubleshooting(context.Context, *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error)
}

type StageTroubleshooter struct {
	Conf                         *config.OnboardingConfig
	OnbDao                       dao.OnboardingDao
	InitCkycProc                 *InitiateCkycProcessor
	KycLivenessProc              *KycLivenessProcessor
	PanNameCheckProc             *PanNameCheckProcessor
	KycDedupeCheckProc           *KycDedupeCheckProcessor
	EkycNameDobValidationProc    *EkycNameDobValidationProcessor
	DeviceRegistrationProc       *DeviceRegistrationProcessor
	CustomerCreationProc         *CustomerCreationProcessor
	AccountCreationProc          *AccountCreationProcessor
	CardAddressProc              *CardAddressProcessor
	DedupeCheckProc              *DedupeCheckProcessor
	PreCustDedupeProc            *PreCustomerCreationDedupeCheckProcessor
	ShippinAddressUpdateProc     *ShippingAddressProcessor
	DOBandPANProc                *DOBandPAN
	CKYCProc                     *CKYCProcessor
	EKYCProc                     *EKYCProcessor
	LivenessProc                 *LivenessProcessor
	RiskScreenProc               *RiskScreenProc
	CardCreationProcessor        *CardCreationProcessor
	DebitCardPinSetupProcessor   *DebitCardPinSetupProcessor
	ScreenerProc                 *ScreenerProc
	userProcessor                helper.UserProcessor
	upiConsentStage              *upiConsent
	UNNameCheckProc              *UNNameCheckProc
	KycNameDobValidationProc     *KycNameDobValidationProcessor
	LocationCheckProc            *LocationCheckProcessor
	BKYCProc                     *BKYCProcessor
	CreditReportCheckProc        *CreditReportCheckProcessor
	ReferralFiniteCodeProc       *ReferralFiniteCodeProcessor
	TncConsentProc               *TncConsentProcessor
	IntentSelectionProc          *IntentSelectionProcessor
	SavingIntroConsentProc       *SavingIntroConsentProcessor
	MotherFatherNameProc         *MotherFatherNameProcessor
	VkycProc                     *VkycProcessor
	PreCustomerCreationCheckProc *PreCustomerCreationCheckProcessor
	OptionalVkycProc             *OptionalVkycProcessor
	AddmoneyProc                 *AddmoneyProcessor
	AadharMobileValidationProc   *AadharMobileValidationProcessor
	UpdateProfileDetailsProc     *UpdateProfileDetailsProcessor
}

func NewStageTroubleshooter(conf *config.OnboardingConfig, onbDao dao.OnboardingDao, initCKYCProc *InitiateCkycProcessor,
	kycLivProc *KycLivenessProcessor, kycDedupeCheckProc *KycDedupeCheckProcessor, ekycNameDobValidationProc *EkycNameDobValidationProcessor,
	deviceRegistrationProc *DeviceRegistrationProcessor, customerCreationProc *CustomerCreationProcessor,
	accountCreationProc *AccountCreationProcessor, cardAddressProc *CardAddressProcessor, dedupeCheckProc *DedupeCheckProcessor,
	preCustDedupeProc *PreCustomerCreationDedupeCheckProcessor, panNameCheckProcessor *PanNameCheckProcessor,
	shippingAddressUpdateProc *ShippingAddressProcessor, dobAndPanProc *DOBandPAN, ckycProc *CKYCProcessor, ekycProc *EKYCProcessor,
	livProc *LivenessProcessor, riskScreenProc *RiskScreenProc, ScreenerProc *ScreenerProc, upiConsentStage *upiConsent, cardCreationProc *CardCreationProcessor,
	debitCardPinSetProc *DebitCardPinSetupProcessor, userProcessor helper.UserProcessor, unNameCheckStageProc *UNNameCheckProc, kycNameDobValidationProc *KycNameDobValidationProcessor,
	bkycProc *BKYCProcessor, CreditReportCheckProc *CreditReportCheckProcessor, ReferralFiniteCodeProc *ReferralFiniteCodeProcessor, TncConsentProc *TncConsentProcessor,
	IntentSelectionProc *IntentSelectionProcessor, SavingIntroConsentProc *SavingIntroConsentProcessor, MotherFatherNameProc *MotherFatherNameProcessor, VkycProc *VkycProcessor,
	PreCustomerCreationCheckProc *PreCustomerCreationCheckProcessor, OptionalVkycProc *OptionalVkycProcessor, AddmoneyProc *AddmoneyProcessor,
	AadharMobileValidationProc *AadharMobileValidationProcessor, UpdateProfileDetailsProc *UpdateProfileDetailsProcessor,
) *StageTroubleshooter {
	return &StageTroubleshooter{
		Conf:                         conf,
		OnbDao:                       onbDao,
		InitCkycProc:                 initCKYCProc,
		KycLivenessProc:              kycLivProc,
		KycDedupeCheckProc:           kycDedupeCheckProc,
		EkycNameDobValidationProc:    ekycNameDobValidationProc,
		DeviceRegistrationProc:       deviceRegistrationProc,
		CustomerCreationProc:         customerCreationProc,
		AccountCreationProc:          accountCreationProc,
		CardAddressProc:              cardAddressProc,
		DedupeCheckProc:              dedupeCheckProc,
		PreCustDedupeProc:            preCustDedupeProc,
		PanNameCheckProc:             panNameCheckProcessor,
		ShippinAddressUpdateProc:     shippingAddressUpdateProc,
		DOBandPANProc:                dobAndPanProc,
		CKYCProc:                     ckycProc,
		EKYCProc:                     ekycProc,
		LivenessProc:                 livProc,
		RiskScreenProc:               riskScreenProc,
		CardCreationProcessor:        cardCreationProc,
		DebitCardPinSetupProcessor:   debitCardPinSetProc,
		ScreenerProc:                 ScreenerProc,
		userProcessor:                userProcessor,
		upiConsentStage:              upiConsentStage,
		UNNameCheckProc:              unNameCheckStageProc,
		KycNameDobValidationProc:     kycNameDobValidationProc,
		BKYCProc:                     bkycProc,
		CreditReportCheckProc:        CreditReportCheckProc,
		ReferralFiniteCodeProc:       ReferralFiniteCodeProc,
		TncConsentProc:               TncConsentProc,
		IntentSelectionProc:          IntentSelectionProc,
		SavingIntroConsentProc:       SavingIntroConsentProc,
		MotherFatherNameProc:         MotherFatherNameProc,
		VkycProc:                     VkycProc,
		PreCustomerCreationCheckProc: PreCustomerCreationCheckProc,
		OptionalVkycProc:             OptionalVkycProc,
		AddmoneyProc:                 AddmoneyProc,
		AadharMobileValidationProc:   AadharMobileValidationProc,
		UpdateProfileDetailsProc:     UpdateProfileDetailsProc,
	}
}

func (s *StageTroubleshooter) Troubleshoot(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {
	if req.Onb.GetStageDetails().GetStageMapping()[onbPb.OnboardingStage_RISK_SCREENING.String()].GetState() == onbPb.OnboardingState_FAILURE {
		riskStage := onbPb.OnboardingStage_RISK_SCREENING
		riskState := req.Onb.GetStageDetails().GetStageMapping()[onbPb.OnboardingStage_RISK_SCREENING.String()].GetState()
		return &StageTroubleshootingResponse{
			Details: &onbPb.StageTroubleshootingDetails{
				ActorId:    req.Onb.GetActorId(),
				Stage:      riskStage,
				State:      riskState,
				UpdatedAt:  stageInfo(req.Onb, onbPb.OnboardingStage_RISK_SCREENING).GetLastUpdatedAt(),
				Onb:        req.Onb,
				NextAction: req.NexAction,
				Advice:     "Unfortunately, you will not be able to onboard at this time.This decision was made based on the guidelines set forth by our internal policies and procedures.",
			},
		}, nil
	}
	if req.Stage == onbPb.OnboardingStage_EKYC && stageStatus(req.Onb, req.Stage) == onbPb.OnboardingState_RESET {
		tsRes, err := s.ekycReset(ctx, req)
		return tsRes, err
	}

	ts := stageTroubleshooters(s, req.Stage)
	if ts == nil {
		logger.Info(ctx, "stage troubleshooter not found", zap.String("flow", req.Stage.String()))
		return &StageTroubleshootingResponse{
			Details: &onbPb.StageTroubleshootingDetails{
				ActorId:    req.Onb.ActorId,
				Stage:      req.Stage,
				State:      stageStatus(req.Onb, req.Stage),
				UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
				Onb:        req.Onb,
				NextAction: req.NexAction,
			},
		}, nil
	}
	tsRes, err := ts.StageTroubleshooting(ctx, req)
	if tsRes == nil || tsRes.Details == nil || tsRes.Details.GetAdvice() == "" {
		logger.Info(ctx, "stage troubleshooter advice not found",
			zap.String("flow", req.Stage.String()),
		)
	}
	return tsRes, err
}

/***********************************************************************************
								INITIATE_CKYC
 **********************************************************************************/

type InitiateCkycProcessor struct{}

func NewInitiateCkycProcessor() *InitiateCkycProcessor {
	return &InitiateCkycProcessor{}
}

func (s *InitiateCkycProcessor) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {
	var (
		stage = onbPb.OnboardingStage_INITIATE_CKYC
		onb   = req.Onb
	)
	if req.Stage != stage {
		return nil, fmt.Errorf("stage mismatch in troubleshooter: %v", req.Stage)
	}

	advice := "user should not be stuck. Please report the issue to the Onboarding Engg Team."

	return &StageTroubleshootingResponse{
		Details: &onbPb.StageTroubleshootingDetails{
			ActorId:    onb.GetActorId(),
			Stage:      stage,
			State:      stageStatus(req.Onb, req.Stage),
			UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
			Onb:        onb,
			Advice:     advice,
			NextAction: req.NexAction,
			StageDebugInfo: &onbPb.StageTroubleshootingDetails_InitiateCkyc{
				InitiateCkyc: &onbPb.InitiateCkycDebugInfo{},
			},
		},
	}, nil
}

/***********************************************************************************
							KYC_AND_LIVENESS_COMPLETION
 **********************************************************************************/

type KycLivenessProcessor struct {
	kycClient      kyc.KycClient
	livenessClient liveness.LivenessClient
}

func NewKycLivenessProcessor(kycClient kyc.KycClient, livenessClient liveness.LivenessClient) *KycLivenessProcessor {
	return &KycLivenessProcessor{
		kycClient:      kycClient,
		livenessClient: livenessClient,
	}
}

func (s *KycLivenessProcessor) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {
	var (
		stage                   = onbPb.OnboardingStage_KYC_AND_LIVENESS_COMPLETION
		onb                     = req.Onb
		advice                  = ""
		livenessManuallyFailed  = false
		facematchManuallyFailed = false
	)
	if req.Stage != stage {
		return nil, fmt.Errorf("stage mismatch in troubleshooter: %v", req.Stage)
	}

	// get kyc data
	kycStatusRes, err := s.kycClient.CheckKYCStatus(ctx, &kyc.CheckKYCStatusRequest{
		ActorId:        onb.ActorId,
		IgnoreLiveness: true,
	})
	if err = epifigrpc.RPCError(kycStatusRes, err); err != nil {
		logger.Error(ctx, "onb kyc error in check kyc status", zap.Error(err))
		return nil, err
	}

	// process kyc failure reason
	failReason := ""
	if kycStatusRes.GetDataValidationMaxRetries() {
		failReason = "NAME_DOB_MAX_RETRIES"
		advice = "Please reset user's name dob retries and ask the user to try again with their correct name or dob"
	} else if kycStatusRes.GetFailureReason() != 0 {
		failReason = kycStatusRes.GetFailureReason().String()
		switch kycStatusRes.GetFailureReason() {
		case kyc.FailureType_EMPTY_ADDRESS:
			advice = "The address in their KYC record is invalid. User cannot onboard with us."
		case kyc.FailureType_DOB_MISMATCH:
			advice = "user's DOB didn't match with KYC record. ask them to try again with correct DOB"
		case kyc.FailureType_NAME_MISMATCH:
			advice = "user's name didn't match with KYC record. ask them to try again with correct name"
		}
	}

	// process kyc status
	var l1 string
	switch kycStatusRes.GetKycStatus() {
	case kyc.KycStatus_COMPLETED:
		l1 = "KYC COMPLETED"
	case kyc.KycStatus_EXPIRED:
		l1 = "KYC EXPIRED"
	case kyc.KycStatus_ERRORED:
		l1 = fmt.Sprintf("KYC FAILED: %v", failReason)
	}

	// check if liveness and facematch is manually failed
	// TODO(Pranshu): Add support for reason of liveness and facematch failed
	livenessAttemptId := kycStatusRes.GetLivenessRequestId()
	faceMatchAttemptIds := kycStatusRes.GetFmRequestIds()

	if livenessAttemptId != "" && faceMatchAttemptIds != nil {
		livenessResp, livErr := s.livenessClient.GetLivenessStatus(ctx, &liveness.GetLivenessStatusRequest{
			ActorId:   onb.GetActorId(),
			AttemptId: livenessAttemptId,
		})
		if te := epifigrpc.RPCError(livenessResp, livErr); te != nil {
			logger.Error(ctx, "Error in fetching liveness Status", zap.Error(te))
			return nil, te
		} else if livenessResp.GetLivenessStatus() == liveness.LivenessStatus_LIVENESS_MANUALLY_FAILED {
			livenessManuallyFailed = true
		}
		for _, faceMatchAttemptId := range faceMatchAttemptIds {
			facematchResp, faceErr := s.livenessClient.GetFaceMatchStatus(ctx, &liveness.GetFaceMatchStatusRequest{
				ActorId:   onb.GetActorId(),
				AttemptId: faceMatchAttemptId,
			})
			if te := epifigrpc.RPCError(facematchResp, faceErr); te != nil {
				logger.Error(ctx, "Error in fetching facematch status", zap.Error(te))
			} else if facematchResp.GetFaceMatchStatus() == liveness.FaceMatchStatus_FACE_MATCH_MANUALLY_FAILED {
				facematchManuallyFailed = true
				break
			}
		}

		// Add advice for
		// users stuck in EKYC INIT - Daksh

		// add advice if attempts are reached
		if kycStatusRes.GetLivenessStatus() == kyc.LivenessStatus_LIVENESS_STATUS_FAILED {
			if livenessAdviceMap[livenessResp.GetLivenessStatus()] != "" {
				advice = fmt.Sprintf("User exceeded max liveness attempt limit. Last attempt failure reason: %v", livenessAdviceMap[livenessResp.GetLivenessStatus()])
			} else {
				// if advice is not mapped with liveness Failure reason
				advice = "User exceeded max liveness attempt limit."
			}
		} else if advice == "" && req.NexAction.GetScreen() == dlPb.Screen_CHECK_LIVENESS {
			advice = livenessAdviceMap[livenessResp.GetLivenessStatus()]
		}
	}

	return &StageTroubleshootingResponse{
		Details: &onbPb.StageTroubleshootingDetails{
			ActorId:    onb.GetActorId(),
			Stage:      stage,
			State:      stageStatus(req.Onb, req.Stage),
			UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
			Onb:        onb,
			L1:         l1,
			L2:         fmt.Sprintf("LIVENESS STATUS: %v", kycStatusRes.GetLivenessStatus()),
			Advice:     advice,
			NextAction: req.NexAction,
			StageDebugInfo: &onbPb.StageTroubleshootingDetails_KycLiveness{
				KycLiveness: &onbPb.KycLivenessDebugInfo{
					KycLevel:                  kycStatusRes.GetKycLevel(),
					KycType:                   kycStatusRes.GetKycType(),
					KycStatus:                 kycStatusRes.GetKycStatus(),
					LivenessStatus:            kycStatusRes.GetLivenessStatus(),
					Expiry:                    kycStatusRes.GetExpiry(),
					FailureReason:             kycStatusRes.GetFailureReason(),
					DataValidationMaxRetries:  kycStatusRes.GetDataValidationMaxRetries(),
					IsLivenessManuallyFailed:  livenessManuallyFailed,
					IsFacematchManuallyFailed: facematchManuallyFailed,
				},
			},
		},
	}, nil
}

/***********************************************************************************
							CKYC
 **********************************************************************************/

type CKYCProcessor struct {
	kycClient kyc.KycClient
}

func NewCKYCProcessor(kycClient kyc.KycClient) *CKYCProcessor {
	return &CKYCProcessor{
		kycClient: kycClient,
	}
}

func (s *CKYCProcessor) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {
	var (
		stage      = onbPb.OnboardingStage_CKYC
		onb        = req.Onb
		advice     = ""
		failReason = ""
	)

	if req.Stage != stage {
		return nil, fmt.Errorf("stage mismatch in troubleshooter: %v", req.Stage)
	}

	// get kyc data
	kycStatusRes, err := s.kycClient.CheckKYCStatus(ctx, &kyc.CheckKYCStatusRequest{
		ActorId:        onb.ActorId,
		IgnoreLiveness: true,
	})
	if err = epifigrpc.RPCError(kycStatusRes, err); err != nil {
		logger.Error(ctx, "onb kyc error in check kyc status", zap.Error(err))
		return nil, err
	}

	if kycStatusRes.GetKycType() != kyc.KycType_CKYC {
		return nil, fmt.Errorf("KYC type mismatch. Expected: CKYC, Got: %v", kycStatusRes.GetKycType())
	}

	switch kycStatusRes.GetFailureReason() {
	case kyc.FailureType_CKYC_L_FLAG:
		failReason = kycStatusRes.GetFailureReason().String()
		advice = "User has Simplified Measure Account with another bank. User will need to complete KYC with the other bank and get their CKYC records updated."
	case kyc.FailureType_CKYC_S_FLAG:
		failReason = kycStatusRes.GetFailureReason().String()
		advice = "User has Small Account with another bank. User will need to complete KYC with the other bank and get their CKYC records updated."
	case kyc.FailureType_CKYC_O_FLAG:
		failReason = kycStatusRes.GetFailureReason().String()
		advice = "User has Aadhaar-OTP based account with another bank. User should get an option to do VKYC while onboarding."
	}

	// process kyc status
	var l1 string
	switch kycStatusRes.GetKycStatus() {
	case kyc.KycStatus_COMPLETED:
		l1 = "KYC COMPLETED"
	case kyc.KycStatus_EXPIRED:
		l1 = "KYC EXPIRED"
	case kyc.KycStatus_ERRORED:
		l1 = fmt.Sprintf("KYC FAILED: %v", failReason)
	}

	return &StageTroubleshootingResponse{
		Details: &onbPb.StageTroubleshootingDetails{
			ActorId:    onb.GetActorId(),
			Stage:      stage,
			State:      stageStatus(req.Onb, req.Stage),
			UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
			Onb:        onb,
			L1:         l1,
			Advice:     advice,
			NextAction: req.NexAction,
			StageDebugInfo: &onbPb.StageTroubleshootingDetails_KycLiveness{
				KycLiveness: &onbPb.KycLivenessDebugInfo{
					KycLevel:                 kycStatusRes.GetKycLevel(),
					KycType:                  kycStatusRes.GetKycType(),
					KycStatus:                kycStatusRes.GetKycStatus(),
					Expiry:                   kycStatusRes.GetExpiry(),
					FailureReason:            kycStatusRes.GetFailureReason(),
					DataValidationMaxRetries: kycStatusRes.GetDataValidationMaxRetries(),
				},
			},
		},
	}, nil
}

/***********************************************************************************
							EKYC
 **********************************************************************************/

type EKYCProcessor struct {
	kycClient kyc.KycClient
}

func NewEKYCProcessor(kycClient kyc.KycClient) *EKYCProcessor {
	return &EKYCProcessor{
		kycClient: kycClient,
	}
}

func (s *EKYCProcessor) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {
	var (
		stage      = onbPb.OnboardingStage_EKYC
		onb        = req.Onb
		advice     = ""
		failReason = ""
	)

	if req.Stage != stage {
		return nil, fmt.Errorf("stage mismatch in troubleshooter: %v", req.Stage)
	}

	if stageStatus(req.Onb, req.Stage) == onbPb.OnboardingState_INITIATED ||
		stageStatus(req.Onb, req.Stage) == onbPb.OnboardingState_UNSPECIFIED {
		advice = "User needs to complete Aadhaar verification on the app."
		return &StageTroubleshootingResponse{
			Details: &onbPb.StageTroubleshootingDetails{
				ActorId:    onb.GetActorId(),
				Stage:      stage,
				State:      stageStatus(req.Onb, req.Stage),
				UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
				Onb:        onb,
				L1:         "",
				Advice:     advice,
				NextAction: req.NexAction,
			},
		}, nil
	}

	// get kyc data
	kycStatusRes, err := s.kycClient.CheckKYCStatus(ctx, &kyc.CheckKYCStatusRequest{
		ActorId:        onb.ActorId,
		IgnoreLiveness: true,
	})
	if err = epifigrpc.RPCError(kycStatusRes, err); err != nil {
		logger.Error(ctx, "onb kyc error in check kyc status", zap.Error(err))
		return nil, err
	}

	if kycStatusRes.GetKycType() != kyc.KycType_EKYC {
		advice = "User has to complete Aadhaar verification and continue onboarding."
	}

	if kycStatusRes.GetDataValidationMaxRetries() {
		failReason = "NAME_DOB_MAX_RETRIES"
		advice = "Please reset user's name dob retries and ask the user to try again with their correct name or dob"
	} else if kycStatusRes.GetFailureReason() != 0 {
		failReason = kycStatusRes.GetFailureReason().String()
		switch kycStatusRes.GetFailureReason() {
		case kyc.FailureType_EMPTY_ADDRESS:
			advice = "The address in their Aadhaar record is invalid. User cannot onboard with us."
		case kyc.FailureType_DOB_MISMATCH:
			advice = "user's DOB didn't match with Aadhaar record. ask them to try again with correct DOB"
		case kyc.FailureType_NAME_MISMATCH:
			advice = "user's name didn't match with Aadhaar record. ask them to try again with correct name"
		case kyc.FailureType_VENDOR_VALIDATION_FAILED:
			advice = "user's details didn't match with Aadhaar record hence cannot onboard with us as of now."
		case kyc.FailureType_NAME_DOB_VALIDATION_FAILURE:
			advice = "The user is an existing federal customer. Their Aadhaar details mismatched " +
				"with their existing account at Federal. They cannot onboard with us at the moment."
		}
	}

	// process kyc status
	var l1 string
	switch kycStatusRes.GetKycStatus() {
	case kyc.KycStatus_COMPLETED:
		l1 = "KYC COMPLETED"
	case kyc.KycStatus_EXPIRED:
		l1 = "KYC EXPIRED"
	case kyc.KycStatus_ERRORED:
		l1 = fmt.Sprintf("KYC FAILED: %v", failReason)
	}

	return &StageTroubleshootingResponse{
		Details: &onbPb.StageTroubleshootingDetails{
			ActorId:    onb.GetActorId(),
			Stage:      stage,
			State:      stageStatus(req.Onb, req.Stage),
			UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
			Onb:        onb,
			L1:         l1,
			Advice:     advice,
			NextAction: req.NexAction,
			StageDebugInfo: &onbPb.StageTroubleshootingDetails_KycLiveness{
				KycLiveness: &onbPb.KycLivenessDebugInfo{
					KycLevel:                 kycStatusRes.GetKycLevel(),
					KycType:                  kycStatusRes.GetKycType(),
					KycStatus:                kycStatusRes.GetKycStatus(),
					Expiry:                   kycStatusRes.GetExpiry(),
					FailureReason:            kycStatusRes.GetFailureReason(),
					DataValidationMaxRetries: kycStatusRes.GetDataValidationMaxRetries(),
				},
			},
		},
	}, nil
}

/***********************************************************************************
							BKYC
 **********************************************************************************/

type BKYCProcessor struct {
	kycClient kyc.KycClient
}

func NewBKYCProcessor(kycClient kyc.KycClient) *BKYCProcessor {
	return &BKYCProcessor{
		kycClient: kycClient,
	}
}

func (s *BKYCProcessor) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {
	var (
		stage      = onbPb.OnboardingStage_BKYC
		onb        = req.Onb
		advice     = ""
		failReason = ""
	)

	if req.Stage != stage {
		return nil, fmt.Errorf("stage mismatch in troubleshooter: %v", req.Stage)
	}

	kycStatusRes, err := s.kycClient.CheckKYCStatus(ctx, &kyc.CheckKYCStatusRequest{
		ActorId:        onb.ActorId,
		IgnoreLiveness: true,
	})
	if err = epifigrpc.RPCError(kycStatusRes, err); err != nil {
		logger.Error(ctx, "onb kyc error in check kyc status", zap.Error(err))
		return nil, err
	}

	if kycStatusRes.GetDataValidationMaxRetries() {
		failReason = "NAME_DOB_MAX_RETRIES"
		advice = "Please reset user's name dob retries and ask the user to try again with their correct name or dob"
	} else if kycStatusRes.GetFailureReason() != 0 {
		failReason = kycStatusRes.GetFailureReason().String()
		switch kycStatusRes.GetFailureReason() {
		case kyc.FailureType_EMPTY_ADDRESS:
			advice = "The address in their Aadhaar record is invalid. User cannot onboard with us."
		case kyc.FailureType_DOB_MISMATCH:
			advice = "user's DOB didn't match with Aadhaar record. ask them to try again with correct DOB"
		case kyc.FailureType_NAME_MISMATCH:
			advice = "user's name didn't match with Aadhaar record. ask them to try again with correct name"
		case kyc.FailureType_VENDOR_VALIDATION_FAILED:
			advice = "user's details didn't match with Aadhaar record hence cannot onboard with us as of now."
		}
	}

	if stageStatus(req.Onb, req.Stage) == onbPb.OnboardingState_RESET {
		advice = "User's Aadhaar data has expired. User needs to complete Aadhaar verification on the app again to continue. If the user is stuck, please collect the exact error details and client logs."
	}

	// process kyc status
	var l1 string
	switch kycStatusRes.GetKycStatus() {
	case kyc.KycStatus_COMPLETED:
		l1 = "KYC COMPLETED"
	case kyc.KycStatus_EXPIRED:
		l1 = "KYC EXPIRED"
	case kyc.KycStatus_ERRORED:
		l1 = fmt.Sprintf("KYC FAILED: %v", failReason)
	}

	return &StageTroubleshootingResponse{
		Details: &onbPb.StageTroubleshootingDetails{
			ActorId:    onb.GetActorId(),
			Stage:      stage,
			State:      stageStatus(req.Onb, req.Stage),
			UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
			Onb:        onb,
			L1:         l1,
			Advice:     advice,
			NextAction: req.NexAction,
			StageDebugInfo: &onbPb.StageTroubleshootingDetails_KycLiveness{
				KycLiveness: &onbPb.KycLivenessDebugInfo{
					KycLevel:                 kycStatusRes.GetKycLevel(),
					KycType:                  kycStatusRes.GetKycType(),
					KycStatus:                kycStatusRes.GetKycStatus(),
					Expiry:                   kycStatusRes.GetExpiry(),
					FailureReason:            kycStatusRes.GetFailureReason(),
					DataValidationMaxRetries: kycStatusRes.GetDataValidationMaxRetries(),
				},
			},
		},
	}, nil

}

/***********************************************************************************
							Liveness
 **********************************************************************************/

type LivenessProcessor struct {
	kycClient      kyc.KycClient
	livenessClient liveness.LivenessClient
}

func NewLivenessProcessor(kycClient kyc.KycClient, livenessClient liveness.LivenessClient) *LivenessProcessor {
	return &LivenessProcessor{
		kycClient:      kycClient,
		livenessClient: livenessClient,
	}
}

func (s *LivenessProcessor) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {
	var (
		stage                   = onbPb.OnboardingStage_LIVENESS
		onb                     = req.Onb
		advice                  = ""
		livenessManuallyFailed  = false
		facematchManuallyFailed = false
	)

	if req.Stage != stage {
		return nil, fmt.Errorf("stage mismatch in troubleshooter: %v", req.Stage)
	}

	res, isDone, err := s.livenessV2Troubleshooting(ctx, req)
	if isDone {
		return res, err
	}

	// get kyc data
	kycStatusRes, err := s.kycClient.CheckKYCStatus(ctx, &kyc.CheckKYCStatusRequest{
		ActorId:        onb.ActorId,
		IgnoreLiveness: true,
	})
	if err = epifigrpc.RPCError(kycStatusRes, err); err != nil {
		logger.Error(ctx, "onb kyc error in check kyc status", zap.Error(err))
		return nil, err
	}
	if stageStatus(req.Onb, req.Stage) == onbPb.OnboardingState_RESET {
		advice = "User needs to do liveness again."
	}
	if stageStatus(req.Onb, req.Stage) == onbPb.OnboardingState_INITIATED {
		advice = "User needs to complete liveness."
	}
	// check if liveness and facematch is manually failed
	// TODO(Pranshu): Add support for reason of liveness and facematch failed
	livenessAttemptId := kycStatusRes.GetLivenessRequestId()
	faceMatchAttemptIds := kycStatusRes.GetFmRequestIds()

	if livenessAttemptId != "" && faceMatchAttemptIds != nil {
		livenessResp, livErr := s.livenessClient.GetLivenessStatus(ctx, &liveness.GetLivenessStatusRequest{
			ActorId:   onb.GetActorId(),
			AttemptId: livenessAttemptId,
		})
		if te := epifigrpc.RPCError(livenessResp, livErr); te != nil {
			logger.Error(ctx, "Error in fetching liveness Status", zap.Error(te))
			return nil, te
		} else if livenessResp.GetLivenessStatus() == liveness.LivenessStatus_LIVENESS_MANUALLY_FAILED {
			livenessManuallyFailed = true
		}
		for _, faceMatchAttemptId := range faceMatchAttemptIds {
			facematchResp, faceErr := s.livenessClient.GetFaceMatchStatus(ctx, &liveness.GetFaceMatchStatusRequest{
				ActorId:   onb.GetActorId(),
				AttemptId: faceMatchAttemptId,
			})
			if te := epifigrpc.RPCError(facematchResp, faceErr); te != nil {
				logger.Error(ctx, "Error in fetching facematch status", zap.Error(te))
			} else if facematchResp.GetFaceMatchStatus() == liveness.FaceMatchStatus_FACE_MATCH_MANUALLY_FAILED {
				facematchManuallyFailed = true
				break
			}
		}

		tempAdvice, ok := livenessAdviceMap[livenessResp.GetLivenessStatus()]
		if !ok {
			advice = "Unexpected liveness attempt status. Please get in touch with the engg team."
		} else {
			advice = tempAdvice
		}
	}

	return &StageTroubleshootingResponse{
		Details: &onbPb.StageTroubleshootingDetails{
			ActorId:    onb.GetActorId(),
			Stage:      stage,
			State:      stageStatus(req.Onb, req.Stage),
			UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
			Onb:        onb,
			Advice:     advice,
			NextAction: req.NexAction,
			StageDebugInfo: &onbPb.StageTroubleshootingDetails_KycLiveness{
				KycLiveness: &onbPb.KycLivenessDebugInfo{
					KycLevel:                  kycStatusRes.GetKycLevel(),
					KycType:                   kycStatusRes.GetKycType(),
					KycStatus:                 kycStatusRes.GetKycStatus(),
					LivenessStatus:            kycStatusRes.GetLivenessStatus(),
					IsFacematchManuallyFailed: facematchManuallyFailed,
					IsLivenessManuallyFailed:  livenessManuallyFailed,
				},
			},
		},
	}, nil
}

func (s *LivenessProcessor) livenessV2Troubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, bool, error) {
	getLSResp, getLSErr := s.livenessClient.GetLivenessSummary(ctx, &liveness.GetLivenessSummaryRequest{
		ActorId:      req.Onb.GetActorId(),
		RequestId:    auth.GetLivenessSummaryReqId(req.Onb),
		LivenessFlow: liveness.LivenessFlow_ONBOARDING,
	})
	if grpcErr := epifigrpc.RPCError(getLSResp, getLSErr); rpc.StatusFromError(grpcErr).IsRecordNotFound() {
		return nil, false, nil
	}

	livSumm := getLSResp.GetSummary()
	advice := ""

	if livSumm.GetStatus() == liveness.SummaryStatus_SUMMARY_RETRIES_EXHAUSTED {
		if isManuallyReviewed(livSumm) {
			advice = "Liveness has failed. Unfortunately, you will not be able to onboard at this time.This decision was made based on the guidelines set forth by our internal policies and procedures."
		} else {
			advice = "B2B Team: Check with Riskops to expediate the process. The liveness video is under manual verification by our team. Please wait for 24 hours. If the video gets approved, the user will be able to proceed. In the event of a rejection, the user will not be able to onboard on the app."
		}
	}

	return &StageTroubleshootingResponse{
		Details: &onbPb.StageTroubleshootingDetails{
			ActorId:    req.Onb.GetActorId(),
			Stage:      onbPb.OnboardingStage_LIVENESS,
			State:      stageStatus(req.Onb, req.Stage),
			UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
			Onb:        req.Onb,
			Advice:     advice,
			NextAction: req.NexAction,
			StageDebugInfo: &onbPb.StageTroubleshootingDetails_KycLiveness{
				KycLiveness: &onbPb.KycLivenessDebugInfo{
					IsFacematchManuallyFailed: livSumm.GetSummaryFacematchStatus() == liveness.SummaryFacematchStatus_SUMMARY_FACEMATCH_MANUALLY_FAILED,
					IsLivenessManuallyFailed:  livSumm.GetSummaryLivenessStatus() == liveness.SummaryLivenessStatus_SUMMARY_LIVENESS_MANUALLY_FAILED,
					IsLivenessPassed:          livSumm.GetStatus() == liveness.SummaryStatus_SUMMARY_PASSED,
				},
			},
		},
	}, true, nil
}

func isManuallyReviewed(summary *liveness.LivenessSummary) bool {
	return summary.GetSummaryLivenessStatus() == liveness.SummaryLivenessStatus_SUMMARY_LIVENESS_MANUALLY_PASSED ||
		summary.GetSummaryLivenessStatus() == liveness.SummaryLivenessStatus_SUMMARY_LIVENESS_MANUALLY_FAILED ||
		summary.GetSummaryFacematchStatus() == liveness.SummaryFacematchStatus_SUMMARY_FACEMATCH_MANUALLY_PASSED ||
		summary.GetSummaryFacematchStatus() == liveness.SummaryFacematchStatus_SUMMARY_FACEMATCH_MANUALLY_FAILED
}

/***********************************************************************************
							PAN_NAME_CHECK
 **********************************************************************************/

type PanNameCheckProcessor struct {
	userClient    usersPb.UsersClient
	userProcessor helper.UserProcessor
}

func NewPanNameCheckProcessor(userClient usersPb.UsersClient, userProcessor helper.UserProcessor) *PanNameCheckProcessor {
	return &PanNameCheckProcessor{
		userClient:    userClient,
		userProcessor: userProcessor,
	}
}

func (s *PanNameCheckProcessor) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {
	var (
		stage   = onbPb.OnboardingStage_PAN_NAME_CHECK
		onb     = req.Onb
		advice  = ""
		kycName = ""
		panName = ""
	)
	if req.Stage != stage {
		return nil, fmt.Errorf("stage mismatch in troubleshooter: %v", req.Stage)
	}

	if stageStatus(onb, stage) == onbPb.OnboardingState_MANUAL_INTERVENTION {
		advice = "Please connect with risk ops team for further assistance."
	}

	if stageStatus(onb, stage) == onbPb.OnboardingState_FAILURE {
		advice = "Name did not match with all the documents. The name should be same in all the documents."
	}
	user, err := s.userProcessor.GetUserByUserId(ctx, onb.GetUserId())
	if err != nil {
		logger.Error(ctx, "failed to get user by id", zap.Error(err))
		return nil, err
	}

	kycName = user.GetProfile().GetKycName().ToString()
	panName = user.GetProfile().GetPanName().ToString()

	metadata := onb.GetStageMetadata().GetPanNameCheck()

	return &StageTroubleshootingResponse{
		Details: &onbPb.StageTroubleshootingDetails{
			ActorId:    onb.GetActorId(),
			Stage:      stage,
			State:      stageStatus(req.Onb, req.Stage),
			UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
			Onb:        onb,
			L1:         fmt.Sprintf("Inhouse score: %v, Old logic score: %v", metadata.GetInhouseNameMatchScore(), metadata.GetOldNameMatchScore()),
			L2:         fmt.Sprintf("Aadhaar Name: %v, PAN Name: %v", kycName, panName),
			Advice:     advice,
			NextAction: req.NexAction,
			StageDebugInfo: &onbPb.StageTroubleshootingDetails_PanNameCheck{
				PanNameCheck: &onbPb.PanNameCheckDebugInfo{
					InhouseNameMatchPassed: metadata.GetInhouseNameMatchPassed(),
					InhouseNameMatchScore:  metadata.GetInhouseNameMatchScore(),
					OldNameMatchPassed:     metadata.GetOldNameMatchPassed(),
					OldNameMatchScore:      metadata.GetOldNameMatchScore(),
					ManualReviewAnnotation: onb.GetStageMetadata().GetPanManualReviewAnnotation(),
				},
			},
		},
	}, nil
}

/***********************************************************************************
							KYC_DEDUPE_CHECK
 **********************************************************************************/

type KycDedupeCheckProcessor struct {
	conf *config.OnboardingConfig
}

func NewKycDedupeCheckProcessor(conf *config.OnboardingConfig) *KycDedupeCheckProcessor {
	return &KycDedupeCheckProcessor{conf: conf}
}

func (s *KycDedupeCheckProcessor) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {
	var (
		stage        = onbPb.OnboardingStage_KYC_DEDUPE_CHECK
		onb          = req.Onb
		dedupeStatus = onb.GetStageMetadata().GetKycDedupeStatus()
	)
	if req.Stage != stage {
		return nil, fmt.Errorf("stage mismatch in troubleshooter: %v", req.Stage)
	}

	// advice for dedupe stuck users
	advice := dedupeAdvice(stageStatus(onb, stage), dedupeStatus)

	details := &onbPb.StageTroubleshootingDetails{
		ActorId:    onb.GetActorId(),
		Stage:      stage,
		State:      stageStatus(req.Onb, req.Stage),
		UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
		Onb:        onb,
		L1:         fmt.Sprintf("KYC_DEDUPE_STATUS: %v", dedupeStatus),
		Advice:     advice,
		NextAction: req.NexAction,
		StageDebugInfo: &onbPb.StageTroubleshootingDetails_KycDedupeCheck{
			KycDedupeCheck: &onbPb.KYCDedupeDebugInfo{
				KycDedupeStatus:     dedupeStatus,
				KycDedupeRetryCount: onb.GetStageMetadata().GetKycDedupeRetryCount(),
			},
		},
	}
	return &StageTroubleshootingResponse{
		Details: details,
	}, nil
}

/***********************************************************************************
							DEDUPE_CHECK
 **********************************************************************************/

type DedupeCheckProcessor struct {
	conf *config.OnboardingConfig
}

func NewDedupeCheckProcessor(conf *config.OnboardingConfig) *DedupeCheckProcessor {
	return &DedupeCheckProcessor{conf: conf}
}

func (s *DedupeCheckProcessor) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {
	var (
		stage        = onbPb.OnboardingStage_DEDUPE_CHECK
		onb          = req.Onb
		dedupeStatus = onb.GetStageMetadata().GetDedupeStatus()
	)
	if req.Stage != stage {
		return nil, fmt.Errorf("stage mismatch in troubleshooter: %v", req.Stage)
	}

	// advice for dedupe stuck users
	advice := dedupeAdvice(stageStatus(onb, stage), dedupeStatus)

	details := &onbPb.StageTroubleshootingDetails{
		ActorId:    onb.GetActorId(),
		Stage:      stage,
		State:      stageStatus(req.Onb, req.Stage),
		UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
		Onb:        onb,
		L1:         fmt.Sprintf("DEDUPE_STATUS: %v", dedupeStatus),
		Advice:     advice,
		NextAction: req.NexAction,
		StageDebugInfo: &onbPb.StageTroubleshootingDetails_DedupeCheck{
			DedupeCheck: &onbPb.DedupeDebugInfo{
				DedupeStatus: dedupeStatus,
			},
		},
	}
	return &StageTroubleshootingResponse{
		Details: details,
	}, nil
}

/***********************************************************************************
							PRE_CUSTOMER_CREATION_DEDUPE_CHECK
 **********************************************************************************/

type PreCustomerCreationDedupeCheckProcessor struct {
	conf *config.OnboardingConfig
}

func NewPreCustomerCreationDedupeCheckProcessor(conf *config.OnboardingConfig) *PreCustomerCreationDedupeCheckProcessor {
	return &PreCustomerCreationDedupeCheckProcessor{conf: conf}
}

func (s *PreCustomerCreationDedupeCheckProcessor) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {
	var (
		stage        = onbPb.OnboardingStage_PRE_CUSTOMER_CREATION_DEDUPE_CHECK
		onb          = req.Onb
		dedupeStatus = onb.GetStageMetadata().GetLatestDedupeStatus()
	)
	if req.Stage != stage {
		return nil, fmt.Errorf("stage mismatch in troubleshooter: %v", req.Stage)
	}

	advice := dedupeAdvice(stageStatus(onb, stage), dedupeStatus)

	details := &onbPb.StageTroubleshootingDetails{
		ActorId:    onb.GetActorId(),
		Stage:      stage,
		State:      stageStatus(req.Onb, req.Stage),
		UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
		Onb:        onb,
		L1:         fmt.Sprintf("DEDUPE STATUS: %v", dedupeStatus),
		Advice:     advice,
		NextAction: req.NexAction,
		StageDebugInfo: &onbPb.StageTroubleshootingDetails_KycDedupeCheck{
			KycDedupeCheck: &onbPb.KYCDedupeDebugInfo{
				KycDedupeStatus:     dedupeStatus,
				KycDedupeRetryCount: onb.GetStageMetadata().GetKycDedupeRetryCount(),
			},
		},
	}
	return &StageTroubleshootingResponse{
		Details: details,
	}, nil
}

/***********************************************************************************
							EKYC_NAME_DOB_VALIDATION
 **********************************************************************************/

type EkycNameDobValidationProcessor struct {
	conf *config.OnboardingConfig
}

func NewEkycNameDobValidationProcessor(conf *config.OnboardingConfig) *EkycNameDobValidationProcessor {
	return &EkycNameDobValidationProcessor{conf: conf}
}

func (s *EkycNameDobValidationProcessor) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {
	var (
		stage = onbPb.OnboardingStage_EKYC_NAME_DOB_VALIDATION
		onb   = req.Onb
	)
	if req.Stage != stage {
		return nil, fmt.Errorf("stage mismatch in troubleshooter: %v", req.Stage)
	}

	advice := ""
	if st := stageStatus(onb, stage); st == onbPb.OnboardingState_MANUAL_INTERVENTION {
		advice = "The user is an existing federal customer. Their Aadhaar details mismatched " +
			"with their existing account at Federal. They cannot onboard with us at the moment."
	}
	details := &onbPb.StageTroubleshootingDetails{
		ActorId:    onb.GetActorId(),
		Stage:      stage,
		State:      stageStatus(req.Onb, req.Stage),
		UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
		Onb:        onb,
		Advice:     advice,
		L1:         fmt.Sprintf("Failure Reason: %v", onb.GetStageMetadata().GetEkycNameDobValidation().GetFailureDesc()),
		L2:         fmt.Sprintf("Raw Response: %v", onb.GetStageMetadata().GetEkycNameDobValidation().GetRawResponse()),
		NextAction: req.NexAction,
		StageDebugInfo: &onbPb.StageTroubleshootingDetails_EkycNameDobValidation{
			EkycNameDobValidation: &onbPb.EkycNameDobValidationDebugInfo{
				Retries:     onb.GetStageMetadata().GetEkycNameDobValidation().GetRetries(),
				FailureDesc: onb.GetStageMetadata().GetEkycNameDobValidation().GetFailureDesc(),
				RawResponse: onb.GetStageMetadata().GetEkycNameDobValidation().GetRawResponse(),
			},
		},
	}
	return &StageTroubleshootingResponse{
		Details: details,
	}, nil
}

/***********************************************************************************
							KYC_NAME_DOB_VALIDATION
 **********************************************************************************/

type KycNameDobValidationProcessor struct {
	conf *config.OnboardingConfig
}

func NewKycNameDobValidationProcessor(conf *config.OnboardingConfig) *KycNameDobValidationProcessor {
	return &KycNameDobValidationProcessor{conf: conf}
}

func (s *KycNameDobValidationProcessor) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {
	var (
		stage = onbPb.OnboardingStage_KYC_NAME_DOB_VALIDATION
		onb   = req.Onb
	)
	if req.Stage != stage {
		return nil, fmt.Errorf("stage mismatch in troubleshooter: %v", req.Stage)
	}

	advice := ""
	if st := stageStatus(onb, stage); st == onbPb.OnboardingState_MANUAL_INTERVENTION {
		advice = "The user is an existing federal customer. Their Aadhaar details mismatched " +
			"with their existing account at Federal. They cannot onboard with us at the moment."
	}
	details := &onbPb.StageTroubleshootingDetails{
		ActorId:    onb.GetActorId(),
		Stage:      stage,
		State:      stageStatus(req.Onb, req.Stage),
		UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
		Onb:        onb,
		Advice:     advice,
		L1:         fmt.Sprintf("Failure Reason: %v", onb.GetStageMetadata().GetEkycNameDobValidation().GetFailureDesc()),
		L2:         fmt.Sprintf("Raw Response: %v", onb.GetStageMetadata().GetEkycNameDobValidation().GetRawResponse()),
		NextAction: req.NexAction,
		StageDebugInfo: &onbPb.StageTroubleshootingDetails_EkycNameDobValidation{
			EkycNameDobValidation: &onbPb.EkycNameDobValidationDebugInfo{
				Retries:     onb.GetStageMetadata().GetEkycNameDobValidation().GetRetries(),
				FailureDesc: onb.GetStageMetadata().GetEkycNameDobValidation().GetFailureDesc(),
				RawResponse: onb.GetStageMetadata().GetEkycNameDobValidation().GetRawResponse(),
			},
		},
	}
	return &StageTroubleshootingResponse{
		Details: details,
	}, nil
}

/***********************************************************************************
							LOCATION_CHECK
 **********************************************************************************/

type LocationCheckProcessor struct {
	conf *config.OnboardingConfig
}

func NewLocationCheckStage(conf *config.OnboardingConfig) *LocationCheckProcessor {
	return &LocationCheckProcessor{conf: conf}
}

func (s *LocationCheckProcessor) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {
	var (
		stage = onbPb.OnboardingStage_LOCATION_CHECK
		onb   = req.Onb
	)
	if req.Stage != stage {
		return nil, fmt.Errorf("stage mismatch in troubleshooter: %v", req.Stage)
	}

	details := &onbPb.StageTroubleshootingDetails{
		ActorId:    onb.GetActorId(),
		Stage:      req.Stage,
		State:      stageStatus(req.Onb, req.Stage),
		UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
		Onb:        onb,
		NextAction: req.NexAction,
		Advice:     "Unfortunately, we have stopped services in your current location. If you are travelling, retry signing up when you return home. Existing users will not be affected by this change.",
	}
	return &StageTroubleshootingResponse{
		Details: details,
	}, nil
}

/***********************************************************************************
							DEVICE_REGISTRATION
 **********************************************************************************/

type DeviceRegistrationProcessor struct {
	conf        *config.OnboardingConfig
	vendorStore vendorstore.VendorStore
}

func NewDeviceRegistrationProcessor(conf *config.OnboardingConfig, vendorStore vendorstore.VendorStore) *DeviceRegistrationProcessor {
	return &DeviceRegistrationProcessor{conf: conf, vendorStore: vendorStore}
}

func (s *DeviceRegistrationProcessor) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {
	var (
		stage  = onbPb.OnboardingStage_DEVICE_REGISTRATION
		onb    = req.Onb
		advice = ""
	)
	if req.Stage != stage {
		return nil, fmt.Errorf("stage mismatch in troubleshooter: %v", req.Stage)
	}
	vendorResponses, attemptsCount := s.GetDeviceRegistrationAttemptsCount(ctx, req.Onb.GetActorId(), s.vendorStore)

	// evaluate advice based on vendor responses
	mobileMismatch, noSMSReceived := false, false
	for _, vResp := range vendorResponses {
		if vResp.GetResponseDescription() == "Mobile number mismatch" {
			mobileMismatch = true
			break
		}
		if vResp.GetResponseDescription() == "No SMS received from the specific mob number or with the given payload" {
			noSMSReceived = true
			break
		}
	}
	if mobileMismatch {
		advice = "SMS sent by user from different phone number. Please ask the user to send SMS from registered phone number"
	} else if noSMSReceived {
		advice = "SMS not received by Federal. Please ask the user to check if SIM is there or they have enough balance " +
			"and check messenger if SMSes are being sent. If all are correct, ask them to try again after some time."
	} else if len(vendorResponses) == 0 {
		advice = "Check if the user is facing issues while sending SMS? Do they have proper network and balance? " +
			"Else, please get specific details of the issue."
	}

	var recentDevRegRes []string
	for _, vResp := range vendorResponses {
		recentDevRegRes = append(recentDevRegRes, vResp.ResponseCode)
	}
	details := &onbPb.StageTroubleshootingDetails{
		ActorId:    onb.GetActorId(),
		Stage:      stage,
		State:      stageStatus(req.Onb, req.Stage),
		UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
		Onb:        onb,
		L1:         strings.Join(recentDevRegRes, " | "),
		L2:         fmt.Sprintf("Attempts= %v", attemptsCount),
		NextAction: req.NexAction,
		Advice:     advice,
		StageDebugInfo: &onbPb.StageTroubleshootingDetails_DeviceRegistration{
			DeviceRegistration: &onbPb.DeviceRegistrationDebugInfo{
				VendorResponses: vendorResponses,
			},
		},
	}
	return &StageTroubleshootingResponse{
		Details: details,
	}, nil
}

func (s *DeviceRegistrationProcessor) GetDeviceRegistrationAttemptsCount(ctx context.Context, actorId string, vendorStore vendorstore.VendorStore) ([]*vendorstorePb.VendorResponse, int) {
	resp, err := vendorStore.GetByActorIdAndAPI(ctx, 50, 0, actorId, vendorstore.API_DEVICE_REGISTRATION)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("error in getting vendor response for api %v", vendorstore.API_DEVICE_REGISTRATION), zap.String(logger.ACTOR_ID_V2, actorId))
	}
	attemptsCount := len(resp)
	if attemptsCount > 10 {
		resp = resp[:10]
	}
	var vendorResp []*vendorstorePb.VendorResponse
	for _, vResp := range resp {
		vendorResp = append(vendorResp, &vendorstorePb.VendorResponse{
			ActorId:             vResp.GetActorId(),
			Vendor:              vResp.GetVendor(),
			StatusCode:          1,
			ResponseCode:        vResp.GetResponseCode(),
			ResponseDescription: vResp.GetResponseDescription(),
			Api:                 getAPI(ctx, vResp.GetApi()),
			RequestId:           vResp.GetRequestId(),
			TraceId:             vResp.GetTraceId(),
			CreatedAt:           timestamppb.New(vResp.GetCreatedAt()),
		})
	}
	return vendorResp, attemptsCount
}

/***********************************************************************************
							UPI_CONSENT
 **********************************************************************************/

type upiConsent struct {
	consent consent.ConsentClient
}

func NewUpiConsentStage(consent consent.ConsentClient) *upiConsent {
	return &upiConsent{consent: consent}

}

func (s *upiConsent) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {
	var (
		onb = req.Onb
	)

	details := &onbPb.StageTroubleshootingDetails{
		ActorId:    onb.GetActorId(),
		Stage:      req.Stage,
		State:      stageStatus(req.Onb, req.Stage),
		UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
		Onb:        onb,
		NextAction: req.NexAction,
		Advice:     "The user should be able to pass this stage. If the user is facing an issue on the screen, please collect client logs and ask for the exact error or the error code he is seeing on the screen.",
	}
	return &StageTroubleshootingResponse{
		Details: details,
	}, nil
}

/***********************************************************************************
							UN_NAME_CHECK
 **********************************************************************************/

type UNNameCheckProc struct{}

func NewUNNameCheckProc() *UNNameCheckProc {
	return &UNNameCheckProc{}
}

func (s *UNNameCheckProc) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {
	var (
		onb    = req.Onb
		advice = ""
	)

	if stageStatus(req.Onb, req.Stage) == onbPb.OnboardingState_FAILURE {
		advice = "User does not meet bank's digital onboarding criteria and hence cannot be onboarded on Fi. User can apply for Federal banking services by visiting branch and opening an account there."
	} else {
		advice = "User should not be blocked on this stage. Please get the error details of the user and escalate to Onboarding team."
	}

	details := &onbPb.StageTroubleshootingDetails{
		ActorId:    onb.GetActorId(),
		Stage:      req.Stage,
		State:      stageStatus(req.Onb, req.Stage),
		UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
		Onb:        onb,
		NextAction: req.NexAction,
		Advice:     advice,
	}
	return &StageTroubleshootingResponse{
		Details: details,
	}, nil
}

/***********************************************************************************
							CUSTOMER_CREATION
 **********************************************************************************/

type CustomerCreationProcessor struct {
	conf        *config.OnboardingConfig
	vendorStore vendorstore.VendorStore
}

func NewCustomerCreationProcessor(conf *config.OnboardingConfig, vendorStore vendorstore.VendorStore) *CustomerCreationProcessor {
	return &CustomerCreationProcessor{conf: conf, vendorStore: vendorStore}
}

func (s *CustomerCreationProcessor) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {
	var (
		stage  = onbPb.OnboardingStage_CUSTOMER_CREATION
		onb    = req.Onb
		advice = ""
	)
	if req.Stage != stage {
		return nil, fmt.Errorf("stage mismatch in troubleshooter: %v", req.Stage)
	}

	initVendorResponses := GetVendorStoreResponses(ctx, vendorstore.API_CUSTOMER_CREATION_INIT, onb, s.vendorStore)
	inquiryVendorResponses := GetVendorStoreResponses(ctx, vendorstore.API_CUSTOMER_CREATION_ENQUIRY, onb, s.vendorStore)
	callbackVendorResponses := GetVendorStoreResponses(ctx, vendorstore.API_CUSTOMER_CREATION_CALLBACK, onb, s.vendorStore)
	advice = accountSetupAdvice(initVendorResponses, inquiryVendorResponses, callbackVendorResponses)

	var l1 string
	var l2 string
	if len(initVendorResponses) > 0 {
		l1 += fmt.Sprintf("init: %v. ", initVendorResponses[0].ResponseDescription)
		l1 += fmt.Sprintf("requestID: %v ", initVendorResponses[0].GetRequestId())
	}
	if len(inquiryVendorResponses) > 0 {
		l1 += fmt.Sprintf("inq: %v. ", inquiryVendorResponses[0].ResponseDescription)
	}
	if len(callbackVendorResponses) > 0 {
		l2 = fmt.Sprintf("callback: %v", callbackVendorResponses[0].ResponseDescription)
	}

	if onb.GetPanAadharLinkageDetails().GetIsAadharDigitsHashMismatch().ToBool() ||
		onb.GetPanAadharLinkageDetails().GetPanAadhaarLinked() == commontypes.BooleanEnum_FALSE {
		advice = "PAN shared by the user isn't linked to the Aadhaar that was provided during eKYC. User can retry again post linking the pan to Aadhaar."
	}

	details := &onbPb.StageTroubleshootingDetails{
		ActorId:    onb.GetActorId(),
		Stage:      stage,
		State:      stageStatus(req.Onb, req.Stage),
		UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
		Onb:        onb,
		NextAction: req.NexAction,
		L1:         l1,
		L2:         l2,
		L3:         fmt.Sprintf("dedupe: %v", onb.GetStageMetadata().GetKycDedupeStatus()),
		Advice:     advice,
		StageDebugInfo: &onbPb.StageTroubleshootingDetails_CustomerCreation{
			CustomerCreation: &onbPb.CustomerCreationDebugInfo{
				DedupeStatus:            onb.GetStageMetadata().GetKycDedupeStatus(),
				InitVendorResponses:     initVendorResponses,
				InquiryVendorResponses:  inquiryVendorResponses,
				CallbackVendorResponses: callbackVendorResponses,
			},
		},
	}
	return &StageTroubleshootingResponse{
		Details: details,
	}, nil
}

/***********************************************************************************
							ACCOUNT_CREATION
 **********************************************************************************/

type AccountCreationProcessor struct {
	conf        *config.OnboardingConfig
	vendorStore vendorstore.VendorStore
}

func NewAccountCreationProcessor(conf *config.OnboardingConfig, vendorStore vendorstore.VendorStore) *AccountCreationProcessor {
	return &AccountCreationProcessor{conf: conf, vendorStore: vendorStore}
}

func (s *AccountCreationProcessor) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {
	var (
		stage = onbPb.OnboardingStage_ACCOUNT_CREATION
		onb   = req.Onb
	)
	if req.Stage != stage {
		return nil, fmt.Errorf("stage mismatch in troubleshooter: %v", req.Stage)
	}

	initVendorResponses := GetVendorStoreResponses(ctx, vendorstore.API_ACCOUNT_CREATION_INIT, onb, s.vendorStore)
	inquiryVendorResponses := GetVendorStoreResponses(ctx, vendorstore.API_ACCOUNT_CREATION_ENQUIRY, onb, s.vendorStore)
	callbackVendorResponses := GetVendorStoreResponses(ctx, vendorstore.API_ACCOUNT_CREATION_CALLBACK, onb, s.vendorStore)

	var l1 string
	var l2 string
	var advice string
	if len(initVendorResponses) > 0 {
		l1 += fmt.Sprintf("init: %v. ", initVendorResponses[0].ResponseDescription)
		l1 += fmt.Sprintf("requestID: %v ", initVendorResponses[0].GetRequestId())
	}
	if len(inquiryVendorResponses) > 0 {
		l1 += fmt.Sprintf("inq: %v. ", inquiryVendorResponses[0].ResponseDescription)
	}
	if len(callbackVendorResponses) > 0 {
		l2 = fmt.Sprintf("callback: %v", callbackVendorResponses[0].ResponseDescription)
	}
	switch {
	case strings.Contains(l1, "W0205Inoperative PAN"):
		advice = "Unfortunately, you will not be able to onboard at this time.This decision was made based on the guidelines set forth by our internal policies and procedures."
	case strings.Contains(l1, "W0205KYC not complied for the customer id in related party tab for the 1 Record"):
		advice = "User has an existing account with Federal and it is not KYC complied. Please redirect the User to Fed e Point >> KYC upload / Profile update (https://accountopen.federalbank.co.in/CustomerPortal/index) where the user can upload the KYC documents after following the on-screen instructions. Please wait for 72 hours after updating the KYC and then retry."
	case strings.Contains(l1, "W0205Customer has another account.Cannot open account in this schemeacctCommonInfo"):
		advice = "The customer has another joint account with Federal or one of the partners, like Jupiter. Can not open one more account with us as of now."
	case strings.Contains(l1, "E4221Enter the email type or change the dispatch mode in general"):
		advice = "User has an existing account with Federal and his/her email address is not linked to the account. Please ask the user to update the email address with Federal. Once the user updates the email address then escalate to FI engg team."
	default:
		advice = accountSetupAdvice(initVendorResponses, inquiryVendorResponses, callbackVendorResponses)
	}
	details := &onbPb.StageTroubleshootingDetails{
		Advice:     advice,
		ActorId:    onb.GetActorId(),
		Stage:      stage,
		State:      stageStatus(req.Onb, req.Stage),
		UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
		Onb:        onb,
		NextAction: req.NexAction,
		L1:         l1,
		L2:         l2,
		L3:         fmt.Sprintf("dedupe: %v", onb.GetStageMetadata().GetKycDedupeStatus()),
	}
	return &StageTroubleshootingResponse{
		Details: details,
	}, nil
}

/***********************************************************************************
							CONFIRM_CARD_MAILING_ADDRESS
 **********************************************************************************/

type CardAddressProcessor struct{}

func NewCardAdressProcessor() *CardAddressProcessor {
	return &CardAddressProcessor{}
}

func (s *CardAddressProcessor) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {
	var (
		stage  = onbPb.OnboardingStage_CONFIRM_CARD_MAILING_ADDRESS
		onb    = req.Onb
		advice = ""
	)
	if req.Stage != stage {
		return nil, fmt.Errorf("stage mismatch in troubleshooter: %v", req.Stage)
	}

	dcNameCheckInfo := onb.GetStageMetadata().GetDebitCardNameCheck()
	if dcNameCheckInfo.GetNameCheckRetryCount() >= constant.DebitCardNameMaxRetries {
		advice = "Reset debit card name dob retries and ask the user to try again with correct name"
	} else {
		advice = "No issues detected. Please ask detailed account of the issue user is facing."
	}

	details := &onbPb.StageTroubleshootingDetails{
		ActorId:    onb.GetActorId(),
		Stage:      stage,
		State:      stageStatus(req.Onb, req.Stage),
		UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
		Onb:        onb,
		NextAction: req.NexAction,
		L1:         fmt.Sprintf("retries: %v", dcNameCheckInfo.GetNameCheckRetryCount()),
		L2:         fmt.Sprintf("score: %v", dcNameCheckInfo.GetOldNameMatchScore()),
		Advice:     advice,
		StageDebugInfo: &onbPb.StageTroubleshootingDetails_ConfirmCardAddress{
			ConfirmCardAddress: &onbPb.CardAddressDebugInfo{
				InhouseNameMatchPassed: dcNameCheckInfo.GetInhouseNameMatchPassed(),
				InhouseNameMatchScore:  dcNameCheckInfo.GetInhouseNameMatchScore(),
				OldNameMatchPassed:     dcNameCheckInfo.GetOldNameMatchPassed(),
				OldNameMatchScore:      dcNameCheckInfo.GetOldNameMatchScore(),
				NameCheckRetryCount:    dcNameCheckInfo.GetNameCheckRetryCount(),
			},
		},
	}
	return &StageTroubleshootingResponse{
		Details: details,
	}, nil
}

/***********************************************************************************
							SHIPPING_ADDRESS_UPDATE
 **********************************************************************************/

type ShippingAddressProcessor struct {
	vendorStore vendorstore.VendorStore
}

func NewShippingAddressProcessor(vendorStore vendorstore.VendorStore) *ShippingAddressProcessor {
	return &ShippingAddressProcessor{
		vendorStore: vendorStore,
	}
}

func (s *ShippingAddressProcessor) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {

	var (
		stage = onbPb.OnboardingStage_SHIPPING_ADDRESS_UPDATE
		onb   = req.Onb
	)
	if req.Stage != stage {
		return nil, fmt.Errorf("stage mismatch in troubleshooter: %v", req.Stage)
	}

	initVendorResponses := GetVendorStoreResponses(ctx, vendorstore.API_SHIPPING_ADDRESS_UPDATE_INIT, onb, s.vendorStore)
	inquiryVendorResponses := GetVendorStoreResponses(ctx, vendorstore.API_SHIPPING_ADDRESS_UPDATE_ENQUIRY, onb, s.vendorStore)
	callbackVendorResponses := GetVendorStoreResponses(ctx, vendorstore.API_SHIPPING_ADDRESS_UPDATE_CALLBACK, onb, s.vendorStore)
	var l1 string
	var l2 string
	var l3 string
	var advice string
	if len(initVendorResponses) > 0 {
		l1 += fmt.Sprintf("init: %v. ", initVendorResponses[0].GetResponseDescription())
		l1 += fmt.Sprintf("requestID: %v", initVendorResponses[0].GetRequestId())
	}
	if len(inquiryVendorResponses) > 0 {
		l2 = fmt.Sprintf("inq: %v. ", inquiryVendorResponses[0].GetResponseDescription())
	}
	if len(callbackVendorResponses) > 0 {
		l3 = fmt.Sprintf("callback: %v", callbackVendorResponses[0].GetResponseDescription())
	}
	switch {
	case strings.Contains(l2, "Please ensure all the fields are entered for CIF KYC Compliance"):
		advice = "User has an existing account with Federal and it is not KYC complied. Please redirect the User to Fed e Point >> KYC upload / Profile update (https://accountopen.federalbank.co.in/CustomerPortal/index) where the user can upload the KYC documents after following the on-screen instructions. Please wait for 72 hours after updating the KYC and then retry."
	case strings.Contains(l2, "Eligible taxslab for age above 60 is"):
		advice = "These customers are existing Federal customers and need to visit Branch to rectify these issues."
	case strings.Contains(l2, "TDS Table Code is not an Exempted Code"):
		advice = "These customers are existing Federal customers and need to visit Branch to rectify these issues."
	default:
		advice = accountSetupAdvice(initVendorResponses, inquiryVendorResponses, callbackVendorResponses)
	}
	details := &onbPb.StageTroubleshootingDetails{
		ActorId:    onb.GetActorId(),
		Stage:      stage,
		State:      stageStatus(req.Onb, req.Stage),
		UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
		Onb:        onb,
		NextAction: req.NexAction,
		L1:         l1,
		L2:         l2,
		L3:         l3,
		Advice:     advice,
	}
	return &StageTroubleshootingResponse{
		Details: details,
	}, nil
}

/***********************************************************************************
							ONBOARDING_COMPLETE
 **********************************************************************************/

type OnbCompleteProcessor struct{}

func NewOnbCompleteProcessor() *OnbCompleteProcessor {
	return &OnbCompleteProcessor{}
}

func (s *OnbCompleteProcessor) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {
	var (
		stage = onbPb.OnboardingStage_ONBOARDING_COMPLETE
		onb   = req.Onb
	)
	if req.Stage != stage {
		return nil, fmt.Errorf("stage mismatch in troubleshooter: %v", req.Stage)
	}

	details := &onbPb.StageTroubleshootingDetails{
		ActorId:    onb.GetActorId(),
		Stage:      stage,
		State:      stageStatus(req.Onb, req.Stage),
		UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
		Onb:        onb,
		NextAction: req.NexAction,
		Advice:     "Onboarding complete. No issues detected",
	}
	return &StageTroubleshootingResponse{
		Details: details,
	}, nil
}

func GetVendorStoreResponses(ctx context.Context, api vendorstore.Api, onb *onbPb.OnboardingDetails, vendorStore vendorstore.VendorStore) []*vendorstorePb.VendorResponse {
	actorId := onb.GetActorId()
	var vendorResp []*vendorstorePb.VendorResponse
	resp, err := vendorStore.GetByActorIdAndAPI(ctx, 5, 0, actorId, api)
	// not returning error intentionally
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("error in getting vendor response for api %v", api), zap.String(logger.ACTOR_ID_V2, actorId))
	}
	for _, vResp := range resp {
		vendorResp = append(vendorResp, &vendorstorePb.VendorResponse{
			ActorId:             vResp.GetActorId(),
			Vendor:              vResp.GetVendor(),
			StatusCode:          1,
			ResponseCode:        vResp.GetResponseCode(),
			ResponseDescription: vResp.GetResponseDescription(),
			Api:                 getAPI(ctx, vResp.GetApi()),
			RequestId:           vResp.GetRequestId(),
			TraceId:             vResp.GetTraceId(),
			CreatedAt:           timestamppb.New(vResp.GetCreatedAt()),
		})
	}
	return vendorResp
}

/***********************************************************************************
							DOB_AND_PAN
 **********************************************************************************/

type DOBandPAN struct {
	userIntelClient userintel.UserIntelServiceClient
	usersClient     usersPb.UsersClient
}

func NewDOBandPANProcessor(userIntelClient userintel.UserIntelServiceClient, usersClient usersPb.UsersClient) *DOBandPAN {
	return &DOBandPAN{
		userIntelClient: userIntelClient,
		usersClient:     usersClient,
	}
}

func (s *DOBandPAN) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {
	var (
		onb = req.Onb
	)
	defaultAdvice := "Users below 21 years are not allowed and will be stuck on this stage. If any user above 21 years is stuck raise with onboarding engineering team with the exact error details."
	resp, errResp := s.userIntelClient.GetUserIntel(ctx, &userintel.GetUserIntelRequest{
		ActorId:   onb.GetActorId(),
		IntelType: userintel.IntelType_INTEL_TYPE_PAN_AADHAAR_LINK_STATUS,
	})
	if err := epifigrpc.RPCError(resp, errResp); err != nil {
		if !resp.GetStatus().IsRecordNotFound() {
			logger.Error(ctx, "error in fetching user's pan aadhaar linkage details", zap.Error(err))
		}
	}
	if resp.GetUserIntel().GetIntelData().GetPanAadhaarLinkStatusData().GetPanAadhaarLinkStatus() == pan.PanAadhaarLinkStatus_PAN_AADHAAR_LINK_STATUS_FALSE {
		defaultAdvice = "User's PAN and AADHAAR are not linked and hence cannot be allowed at this point in time. Ask the user to get their PAN and AADHAAR linked to proceed."
	}

	b2bResp, errResp := s.usersClient.GetB2BSalaryProgramVerificationStatus(ctx, &usersPb.GetB2BSalaryProgramVerificationStatusRequest{
		Identifier: &usersPb.GetB2BSalaryProgramVerificationStatusRequest_ActorId{
			ActorId: onb.GetActorId(),
		},
	})
	if err := epifigrpc.RPCError(b2bResp, errResp); err != nil {
		logger.Error(ctx, "error in fetching user's pan aadhaar linkage details", zap.Error(err))
	}
	// Adding this special handling to remove confusion that there is a whitelisting error.
	// TODO(Bhabtosh): Handle other failure cases as well by storing ValidateV2's response in vendorstore.
	if b2bResp.GetStatus().IsSuccess() && b2bResp.GetIsVerified() == true {
		defaultAdvice = "Raise with onboarding engineering team with the exact error displayed on the screen."
	}
	details := &onbPb.StageTroubleshootingDetails{
		ActorId:    onb.GetActorId(),
		Stage:      req.Stage,
		State:      stageStatus(req.Onb, req.Stage),
		UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
		Onb:        onb,
		NextAction: req.NexAction,
		Advice:     defaultAdvice,
	}
	return &StageTroubleshootingResponse{
		Details: details,
	}, nil
}

/***********************************************************************************
							RISK_SCREENING
 **********************************************************************************/

type RiskScreenProc struct{}

func NewRiskScreenProc() *RiskScreenProc {
	return &RiskScreenProc{}
}

func (s *RiskScreenProc) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {
	var (
		onb    = req.Onb
		advice = ""
	)
	switch {
	case stageStatus(req.Onb, req.Stage) == onbPb.OnboardingState_FAILURE:
		advice = "Unfortunately, you will not be able to onboard at this time.This decision was made based on the guidelines set forth by our internal policies and procedures."
	case stageStatus(req.Onb, req.Stage) == onbPb.OnboardingState_MANUAL_INTERVENTION:
		advice = "The details are under review by risk team and it should get updated within 24 hours. Beyond 24 hours please raise with Risk team."
	default:
		advice = "Please get further details from the user. Create a ticket and assign to Onboarding tech team."
	}

	return &StageTroubleshootingResponse{
		Details: &onbPb.StageTroubleshootingDetails{
			ActorId:    onb.GetActorId(),
			Stage:      req.Stage,
			State:      stageStatus(req.Onb, req.Stage),
			UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
			Onb:        onb,
			NextAction: req.NexAction,
			Advice:     advice,
		},
	}, nil
}

// TODO(aditya): move to vendorstore pkg
func getAPI(ctx context.Context, vApi vendorstore.Api) vendorstorePb.VendorAPI {
	switch vApi {
	case vendorstore.API_CUSTOMER_CREATION_INIT:
		return vendorstorePb.VendorAPI_API_CUSTOMER_CREATION_INIT
	case vendorstore.API_CUSTOMER_CREATION_ENQUIRY:
		return vendorstorePb.VendorAPI_API_CUSTOMER_CREATION_ENQUIRY
	case vendorstore.API_CUSTOMER_CREATION_CALLBACK:
		return vendorstorePb.VendorAPI_API_CUSTOMER_CREATION_CALLBACK
	case vendorstore.API_ACCOUNT_CREATION_ENQUIRY:
		return vendorstorePb.VendorAPI_API_ACCOUNT_CREATION_ENQUIRY
	case vendorstore.API_ACCOUNT_CREATION_CALLBACK:
		return vendorstorePb.VendorAPI_API_ACCOUNT_CREATION_CALLBACK
	case vendorstore.API_DEVICE_REGISTRATION:
		return vendorstorePb.VendorAPI_API_DEVICE_REGISTRATION
	case vendorstore.API_DEVICE_REGISTRATION_ENQUIRY:
		return vendorstorePb.VendorAPI_API_DEVICE_REGISTRATION_ENQUIRY
	case vendorstore.API_CREDIT_REPORT_PRESENCE:
		return vendorstorePb.VendorAPI_API_CREDIT_REPORT_PRESENCE
	case vendorstore.API_CREDIT_REPORT_FETCH:
		return vendorstorePb.VendorAPI_API_CREDIT_REPORT_FETCH
	default:
		logger.Info(ctx, fmt.Sprintf("unexpected api received %v", vApi))
		return vendorstorePb.VendorAPI_API_UNSPECIFIED
	}
}

/***********************************************************************************
							SCREENER
 **********************************************************************************/

type ScreenerProc struct {
	scrnrClient scrnrPb.ScreenerClient
}

func NewScreenerProc(scrnrClient scrnrPb.ScreenerClient) *ScreenerProc {
	return &ScreenerProc{
		scrnrClient: scrnrClient,
	}
}

func (s *ScreenerProc) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {
	var (
		onb    = req.Onb
		advice = ""
	)

	if stageStatus(req.Onb, req.Stage) == onbPb.OnboardingState_FAILURE {
		advice = "Screening failed. User can not onboard with us as of now."
	}
	if stageStatus(req.Onb, req.Stage) == onbPb.OnboardingState_MANUAL_INTERVENTION {
		advice = "Screening failed. User can not onboard with us as of now."
	}

	getSAResp, errGetSA := s.scrnrClient.GetScreenerAttemptsByActorId(ctx, &scrnrPb.GetScreenerAttemptsByActorIdRequest{
		ActorId:    onb.GetActorId(),
		CachedData: true,
	})
	if grpcErr := epifigrpc.RPCError(getSAResp, errGetSA); grpcErr != nil {
		return &StageTroubleshootingResponse{
			Details: &onbPb.StageTroubleshootingDetails{
				ActorId:    onb.GetActorId(),
				Stage:      req.Stage,
				State:      stageStatus(req.Onb, req.Stage),
				UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
				Onb:        onb,
				NextAction: req.NexAction,
				Advice:     advice,
			},
		}, nil
	}

	sa := getSAResp.GetScreenerAttempt()
	daysToExpiry := math.Ceil(sa.GetExpiry().AsTime().Sub(time.Now()).Hours() / 24)
	if daysToExpiry < 0 {
		daysToExpiry = 0
	}
	advice += fmt.Sprintf("We regret to inform you that we are unable to onboard you at this time. We couldn't verify your employment or income through available methods. However, we would like to inform you that you can try again after %v days, For students :\nStudent email is currently supported for a few colleges. In case your college/institute doesn't provide an email or is not among the approved list of colleges, you can try using other methods.\nIn case we're still not able to determine your eligibility, you can try again when your college is approved or once you start earning.", daysToExpiry)
	return &StageTroubleshootingResponse{
		Details: &onbPb.StageTroubleshootingDetails{
			ActorId:    onb.GetActorId(),
			Stage:      req.Stage,
			State:      stageStatus(req.Onb, req.Stage),
			UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
			Onb:        onb,
			NextAction: req.NexAction,
			Advice:     advice,
		},
	}, nil
}

/***********************************************************************************
							CARD_CREATION
 **********************************************************************************/

type CardCreationProcessor struct {
	vendorStore vendorstore.VendorStore
}

func NewCardCreationProcessor(vendorStore vendorstore.VendorStore) *CardCreationProcessor {
	return &CardCreationProcessor{
		vendorStore: vendorStore,
	}
}

func (s *CardCreationProcessor) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {

	var (
		stage = onbPb.OnboardingStage_CARD_CREATION
		onb   = req.Onb
	)
	if req.Stage != stage {
		return nil, fmt.Errorf("stage mismatch in troubleshooter: %v", req.Stage)
	}

	initVendorResponses := GetVendorStoreResponses(ctx, vendorstore.API_CREATE_CARD_INIT, onb, s.vendorStore)
	inquiryVendorResponses := GetVendorStoreResponses(ctx, vendorstore.API_CREATE_CARD_ENQUIRY, onb, s.vendorStore)
	callbackVendorResponses := GetVendorStoreResponses(ctx, vendorstore.API_CREATE_CARD_CALLBACK, onb, s.vendorStore)
	var l1 string
	var l2 string
	var l3 string
	var advice string

	advice = accountSetupAdvice(initVendorResponses, inquiryVendorResponses, callbackVendorResponses)

	if len(initVendorResponses) > 0 {
		l1 += fmt.Sprintf("init: %v. ", initVendorResponses[0].GetResponseDescription())
		l1 += fmt.Sprintf("requestID: %v ", initVendorResponses[0].GetRequestId())
	}
	if len(inquiryVendorResponses) > 0 {
		l2 = fmt.Sprintf("inq: %v. ", inquiryVendorResponses[0].GetResponseDescription())
	}
	if len(callbackVendorResponses) > 0 {
		l3 = fmt.Sprintf("callback: %v ", callbackVendorResponses[0].GetResponseDescription())
	}

	details := &onbPb.StageTroubleshootingDetails{
		ActorId:    onb.GetActorId(),
		Stage:      stage,
		State:      stageStatus(req.Onb, req.Stage),
		UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
		Onb:        onb,
		NextAction: req.NexAction,
		L1:         l1,
		L2:         l2,
		L3:         l3,
		Advice:     advice,
	}
	return &StageTroubleshootingResponse{
		Details: details,
	}, nil
}

/***********************************************************************************
							DEBIT_CARD_PIN_SETUP
 **********************************************************************************/

type DebitCardPinSetupProcessor struct{}

func NewDebitCardPinSetupProcessor() *DebitCardPinSetupProcessor {
	return &DebitCardPinSetupProcessor{}
}

func (s *DebitCardPinSetupProcessor) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {

	var (
		stage = onbPb.OnboardingStage_DEBIT_CARD_PIN_SETUP
		onb   = req.Onb
	)
	if req.Stage != stage {
		return nil, fmt.Errorf("stage mismatch in troubleshooter: %v", req.Stage)
	}

	return &StageTroubleshootingResponse{
		Details: &onbPb.StageTroubleshootingDetails{
			ActorId:    onb.GetActorId(),
			Stage:      req.Stage,
			State:      stageStatus(req.Onb, req.Stage),
			UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
			Onb:        onb,
			NextAction: req.NexAction,
			Advice:     "At this stage user needs to set the debit card pin to complete onboarding. User can not change device or phone or email before completing onboarding.",
		},
	}, nil
}

/***********************************************************************************
							CREDIT_REPORT_CHECK
 **********************************************************************************/

type CreditReportCheckProcessor struct {
	conf *config.OnboardingConfig
}

func NewCreditReportCheckStage(conf *config.OnboardingConfig) *CreditReportCheckProcessor {
	return &CreditReportCheckProcessor{conf: conf}
}

func (s *CreditReportCheckProcessor) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {
	var (
		stage  = onbPb.OnboardingStage_CREDIT_REPORT_CHECK
		onb    = req.Onb
		advice = ""
	)
	if req.Stage != stage {
		return nil, fmt.Errorf("stage mismatch in troubleshooter: %v", req.Stage)
	}

	if stageStatus(req.Onb, req.Stage) == onbPb.OnboardingState_FAILURE {
		advice = "Unfortunately, you will not be able to onboard at this time.This decision was made based on the guidelines set forth by our internal policies and procedures."
	}

	details := &onbPb.StageTroubleshootingDetails{
		ActorId:    onb.GetActorId(),
		Stage:      req.Stage,
		State:      stageStatus(req.Onb, req.Stage),
		UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
		Onb:        onb,
		NextAction: req.NexAction,
		Advice:     advice,
	}
	return &StageTroubleshootingResponse{
		Details: details,
	}, nil
}

/***********************************************************************************
							REFERRAL_FINITE_CODE
 **********************************************************************************/

type ReferralFiniteCodeProcessor struct {
	conf *config.OnboardingConfig
}

func NewReferralFiniteCodeStage(conf *config.OnboardingConfig) *ReferralFiniteCodeProcessor {
	return &ReferralFiniteCodeProcessor{conf: conf}
}

func (s *ReferralFiniteCodeProcessor) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {
	var (
		stage  = onbPb.OnboardingStage_REFERRAL_FINITE_CODE
		onb    = req.Onb
		advice = "At this stage user should not get stuck. User needs to enter referral code if any and continue onboarding."
	)
	if req.Stage != stage {
		return nil, fmt.Errorf("stage mismatch in troubleshooter: %v", req.Stage)
	}
	details := &onbPb.StageTroubleshootingDetails{
		ActorId:    onb.GetActorId(),
		Stage:      req.Stage,
		State:      stageStatus(req.Onb, req.Stage),
		UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
		Onb:        onb,
		NextAction: req.NexAction,
		Advice:     advice,
	}
	return &StageTroubleshootingResponse{
		Details: details,
	}, nil
}

/***********************************************************************************
							TNC_CONSENT
 **********************************************************************************/

type TncConsentProcessor struct {
	conf *config.OnboardingConfig
}

func NewTncConsentStage(conf *config.OnboardingConfig) *TncConsentProcessor {
	return &TncConsentProcessor{conf: conf}
}

func (s *TncConsentProcessor) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {
	var (
		stage  = onbPb.OnboardingStage_TNC_CONSENT
		onb    = req.Onb
		advice = "At this stage user should not get stuck. User needs to accept terms and condition to continue onboarding."
	)
	if req.Stage != stage {
		return nil, fmt.Errorf("stage mismatch in troubleshooter: %v", req.Stage)
	}

	details := &onbPb.StageTroubleshootingDetails{
		ActorId:    onb.GetActorId(),
		Stage:      req.Stage,
		State:      stageStatus(req.Onb, req.Stage),
		UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
		Onb:        onb,
		NextAction: req.NexAction,
		Advice:     advice,
	}
	return &StageTroubleshootingResponse{
		Details: details,
	}, nil
}

/***********************************************************************************
							INTENT_SELECTION
 **********************************************************************************/

type IntentSelectionProcessor struct {
	conf *config.OnboardingConfig
}

func NewIntentSelectionStage(conf *config.OnboardingConfig) *IntentSelectionProcessor {
	return &IntentSelectionProcessor{conf: conf}
}

func (s *IntentSelectionProcessor) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {
	var (
		stage  = onbPb.OnboardingStage_INTENT_SELECTION
		onb    = req.Onb
		advice = "At this stage user should not get stuck. User need to select intent to onboard like savings account or personal loan or credit card."
	)
	if req.Stage != stage {
		return nil, fmt.Errorf("stage mismatch in troubleshooter: %v", req.Stage)
	}

	details := &onbPb.StageTroubleshootingDetails{
		ActorId:    onb.GetActorId(),
		Stage:      req.Stage,
		State:      stageStatus(req.Onb, req.Stage),
		UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
		Onb:        onb,
		NextAction: req.NexAction,
		Advice:     advice,
	}
	return &StageTroubleshootingResponse{
		Details: details,
	}, nil
}

/***********************************************************************************
							SAVINGS_INTRO_CONSENT
 **********************************************************************************/

type SavingIntroConsentProcessor struct {
	conf *config.OnboardingConfig
}

func NewSavingIntroConsentStage(conf *config.OnboardingConfig) *SavingIntroConsentProcessor {
	return &SavingIntroConsentProcessor{conf: conf}
}

func (s *SavingIntroConsentProcessor) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {
	var (
		stage  = onbPb.OnboardingStage_SAVINGS_INTRO_CONSENT
		onb    = req.Onb
		advice = "At this stage user should not get stuck. User needs to accept terms and condition to continue onboarding."
	)
	if req.Stage != stage {
		return nil, fmt.Errorf("stage mismatch in troubleshooter: %v", req.Stage)
	}

	details := &onbPb.StageTroubleshootingDetails{
		ActorId:    onb.GetActorId(),
		Stage:      req.Stage,
		State:      stageStatus(req.Onb, req.Stage),
		UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
		Onb:        onb,
		NextAction: req.NexAction,
		Advice:     advice,
	}
	return &StageTroubleshootingResponse{
		Details: details,
	}, nil
}

/***********************************************************************************
							MOTHER_FATHER_NAME
 **********************************************************************************/

type MotherFatherNameProcessor struct {
	conf *config.OnboardingConfig
}

func NewMotherFatherNameStage(conf *config.OnboardingConfig) *MotherFatherNameProcessor {
	return &MotherFatherNameProcessor{conf: conf}
}

func (s *MotherFatherNameProcessor) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {
	var (
		stage  = onbPb.OnboardingStage_MOTHER_FATHER_NAME
		onb    = req.Onb
		advice = "At this stage user should not get stuck. User needs to enter parent's name as per Pan card to continue onboarding."
	)
	if req.Stage != stage {
		return nil, fmt.Errorf("stage mismatch in troubleshooter: %v", req.Stage)
	}

	details := &onbPb.StageTroubleshootingDetails{
		ActorId:    onb.GetActorId(),
		Stage:      req.Stage,
		State:      stageStatus(req.Onb, req.Stage),
		UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
		Onb:        onb,
		NextAction: req.NexAction,
		Advice:     advice,
	}
	return &StageTroubleshootingResponse{
		Details: details,
	}, nil
}

/***********************************************************************************
							VKYC
 **********************************************************************************/

type VkycProcessor struct {
	conf *config.OnboardingConfig
}

func NewVkycStage(conf *config.OnboardingConfig) *VkycProcessor {
	return &VkycProcessor{conf: conf}
}

func (s *VkycProcessor) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {
	var (
		stage  = onbPb.OnboardingStage_VKYC
		onb    = req.Onb
		advice = "Check VKYC troubleshooting section for more details."
	)
	if req.Stage != stage {
		return nil, fmt.Errorf("stage mismatch in troubleshooter: %v", req.Stage)
	}

	details := &onbPb.StageTroubleshootingDetails{
		ActorId:    onb.GetActorId(),
		Stage:      req.Stage,
		State:      stageStatus(req.Onb, req.Stage),
		UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
		Onb:        onb,
		NextAction: req.NexAction,
		Advice:     advice,
	}
	return &StageTroubleshootingResponse{
		Details: details,
	}, nil
}

/***********************************************************************************
							PRE_CUSTOMER_CREATION_CHECK
 **********************************************************************************/

type PreCustomerCreationCheckProcessor struct {
	conf *config.OnboardingConfig
}

func NewPreCustomerCreationCheckStage(conf *config.OnboardingConfig) *PreCustomerCreationCheckProcessor {
	return &PreCustomerCreationCheckProcessor{conf: conf}
}

func (s *PreCustomerCreationCheckProcessor) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {
	var (
		stage  = onbPb.OnboardingStage_PRE_CUSTOMER_CREATION_CHECK
		onb    = req.Onb
		advice = "At this stage user should not get stuck. If stuck then collect exact error details and escalate to onboarding team."
	)
	if req.Stage != stage {
		return nil, fmt.Errorf("stage mismatch in troubleshooter: %v", req.Stage)
	}

	if onb.GetStageMetadata().GetPreCustomerCreationMetadata().GetFailureReason() == onbPb.PreCustomerCreationMetadata_FAILURE_REASON_PAN_UNIQUENESS_CHECK {
		advice = "This PAN is already linked to different phone number and email. Please ask the user to use the registered phone number and email to continue."
	}
	details := &onbPb.StageTroubleshootingDetails{
		ActorId:    onb.GetActorId(),
		Stage:      req.Stage,
		State:      stageStatus(req.Onb, req.Stage),
		UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
		Onb:        onb,
		NextAction: req.NexAction,
		Advice:     advice,
	}
	return &StageTroubleshootingResponse{
		Details: details,
	}, nil
}

/***********************************************************************************
							OPTIONAL_VKYC
 **********************************************************************************/

type OptionalVkycProcessor struct {
	conf *config.OnboardingConfig
}

func NewOptionalVkycStage(conf *config.OnboardingConfig) *OptionalVkycProcessor {
	return &OptionalVkycProcessor{conf: conf}
}

func (s *OptionalVkycProcessor) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {
	var (
		stage  = onbPb.OnboardingStage_OPTIONAL_VKYC
		onb    = req.Onb
		advice = "Onboarding is complete. User can choose to complete VKYC now or skip and start using FI."
	)
	if req.Stage != stage {
		return nil, fmt.Errorf("stage mismatch in troubleshooter: %v", req.Stage)
	}

	details := &onbPb.StageTroubleshootingDetails{
		ActorId:    onb.GetActorId(),
		Stage:      req.Stage,
		State:      stageStatus(req.Onb, req.Stage),
		UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
		Onb:        onb,
		NextAction: req.NexAction,
		Advice:     advice,
	}
	return &StageTroubleshootingResponse{
		Details: details,
	}, nil
}

/***********************************************************************************
							ADD_MONEY
 **********************************************************************************/

type AddmoneyProcessor struct {
	conf *config.OnboardingConfig
}

func NewAddmoneyStage(conf *config.OnboardingConfig) *AddmoneyProcessor {
	return &AddmoneyProcessor{conf: conf}
}

func (s *AddmoneyProcessor) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {
	var (
		stage  = onbPb.OnboardingStage_ADD_MONEY
		onb    = req.Onb
		advice = "Onboarding is complete. User needs to add money to start using FI."
	)
	if req.Stage != stage {
		return nil, fmt.Errorf("stage mismatch in troubleshooter: %v", req.Stage)
	}

	details := &onbPb.StageTroubleshootingDetails{
		ActorId:    onb.GetActorId(),
		Stage:      req.Stage,
		State:      stageStatus(req.Onb, req.Stage),
		UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
		Onb:        onb,
		NextAction: req.NexAction,
		Advice:     advice,
	}
	return &StageTroubleshootingResponse{
		Details: details,
	}, nil
}

/***********************************************************************************
							AADHAR_MOBILE_VALIDATION
 **********************************************************************************/

type AadharMobileValidationProcessor struct {
	conf *config.OnboardingConfig
}

func NewAadharMobileValidationStage(conf *config.OnboardingConfig) *AadharMobileValidationProcessor {
	return &AadharMobileValidationProcessor{conf: conf}
}

func (s *AadharMobileValidationProcessor) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {
	var (
		stage  = onbPb.OnboardingStage_AADHAR_MOBILE_VALIDATION
		onb    = req.Onb
		advice = ""
	)
	if req.Stage != stage {
		return nil, fmt.Errorf("stage mismatch in troubleshooter: %v", req.Stage)
	}

	if stageStatus(onb, stage) == onbPb.OnboardingState_FAILURE {
		advice = "The mobile number used for onboarding on Fi app does not match with the mobile number linked to aadhaar. It is mandatory to use the phone number linked to Adhaar. User should be getting an option to retry with the phone number linked to his/her Adhaar."
	}

	details := &onbPb.StageTroubleshootingDetails{
		ActorId:    onb.GetActorId(),
		Stage:      req.Stage,
		State:      stageStatus(req.Onb, req.Stage),
		UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
		Onb:        onb,
		NextAction: req.NexAction,
		Advice:     advice,
	}
	return &StageTroubleshootingResponse{
		Details: details,
	}, nil
}

/***********************************************************************************
							UPDATE_PROFILE_DETAILS
 **********************************************************************************/

type UpdateProfileDetailsProcessor struct {
	conf *config.OnboardingConfig
}

func NewUpdateProfileDetailsStage(conf *config.OnboardingConfig) *UpdateProfileDetailsProcessor {
	return &UpdateProfileDetailsProcessor{conf: conf}
}

func (s *UpdateProfileDetailsProcessor) StageTroubleshooting(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {
	var (
		stage  = onbPb.OnboardingStage_UPDATE_PROFILE_DETAILS
		onb    = req.Onb
		advice = "If the user is stuck please reach out to the onboarding engineering team."
	)
	if req.Stage != stage {
		return nil, fmt.Errorf("stage mismatch in troubleshooter: %v", req.Stage)
	}

	details := &onbPb.StageTroubleshootingDetails{
		ActorId:    onb.GetActorId(),
		Stage:      req.Stage,
		State:      stageStatus(req.Onb, req.Stage),
		UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
		Onb:        onb,
		NextAction: req.NexAction,
		L2:         onb.GetStageMetadata().GetUpdateProfileDetailsMetadata().GetRequestId(),
		Advice:     advice,
	}
	return &StageTroubleshootingResponse{
		Details: details,
	}, nil
}

func dedupeAdvice(st onbPb.OnboardingState, dedupeSt customer.DedupeStatus) string {
	if st == onbPb.OnboardingState_SUCCESS || st == onbPb.OnboardingState_SKIPPED {
		return "stage is successful. user shouldn't be stuck here."
	}
	if st == onbPb.OnboardingState_RESET {
		return "The user is an existing federal customer with a different DOB. The user needs to retry with the correct DOB."
	}
	if st != onbPb.OnboardingState_MANUAL_INTERVENTION {
		return "Unexpected dedupe stage status. Please report to the Fi engg team with ticket id."
	}
	// only manual intervention stages should be left

	if dedupeSt == customer.DedupeStatus_BLOCKED {
		return fmt.Sprintf("This user can not onboard on FI as the Bank has put a restriction to onboard this user on Digital platform due to their internal policy.")
	}
	errorOpts := error2.DedupeStatusToDeeplinkDataMapping[dedupeSt]
	if errorOpts != nil {
		return fmt.Sprintf("Message for the user: %v. %v",
			errorOpts.Title, errorOpts.Subtitle)
	}
	return "Advice not found. Please report the issue with the engg team."
}

func (s *StageTroubleshooter) ekycReset(ctx context.Context, req *StageTroubleshootingRequest) (*StageTroubleshootingResponse, error) {
	var stuckStage onbPb.OnboardingStage
	for _, stage := range stageproc.SAOnboardingStagesOrder {
		stageMap := req.Onb.GetStageDetails().GetStageMapping()
		stageStartedAt := stageMap[stage.String()].GetStartedAt()
		if stageStartedAt == nil {
			continue
		}
		if stage == onbPb.OnboardingStage_ONBOARDING_COMPLETE {
			continue
		}
		stuckStage = stage
	}
	ts := stageTroubleshooters(s, stuckStage)
	if ts == nil {
		logger.Info(ctx, "stage troubleshooter not found", zap.String("flow", req.Stage.String()))
		return &StageTroubleshootingResponse{
			Details: &onbPb.StageTroubleshootingDetails{
				ActorId:    req.Onb.ActorId,
				Stage:      req.Stage,
				State:      stageStatus(req.Onb, req.Stage),
				UpdatedAt:  stageInfo(req.Onb, req.Stage).GetLastUpdatedAt(),
				Onb:        req.Onb,
				NextAction: req.NexAction,
			},
		}, nil
	}
	tsRes, err := ts.StageTroubleshooting(ctx, &StageTroubleshootingRequest{
		Stage:     stuckStage,
		Onb:       req.Onb,
		NexAction: req.NexAction,
	})
	if tsRes == nil || tsRes.Details == nil || tsRes.Details.GetAdvice() == "" {
		logger.Info(ctx, "stage troubleshooter advice not found",
			zap.String("flow", req.Stage.String()),
		)
	}
	tsRes.Details.Stage = req.Stage
	tsRes.Details.L3 += fmt.Sprintf(" Note: User's Aadhaar data has expired and user needs to complete Aadhaar verification on the app to see the latest details. Last stuck stage is: %v and below advice is for that", stuckStage)
	return tsRes, err
}

func accountSetupAdvice(initVendorResponses, inquiryVendorResponses, callbackVendorResponses []*vendorstorePb.VendorResponse) string {
	// if it's been less than 20 minutes, ask users to wait
	if len(callbackVendorResponses) == 0 &&
		len(initVendorResponses) > 0 &&
		len(inquiryVendorResponses) > 0 &&
		time.Since(initVendorResponses[0].GetCreatedAt().AsTime()) < 20*time.Minute {
		stuckDuration := time.Since(initVendorResponses[0].GetCreatedAt().AsTime()).Round(time.Minute)
		timeToEscalation := 20*time.Minute - stuckDuration
		// TODO(bhabtosh): add email template and remove onb-oncall dependency
		return fmt.Sprintf(`Account Creation has started at bank's end. It can take up to 20 minutes for this to happen.
Time elapsed: %v. Wait for %s before escalating.`, stuckDuration, timeToEscalation)
	}

	return fmt.Sprintf("User is stuck on this stage for more than 20 minutes, escalate to %s", onbTeamPOC)
}
