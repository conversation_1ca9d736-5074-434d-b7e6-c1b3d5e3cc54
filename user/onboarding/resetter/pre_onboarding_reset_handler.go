package resetter

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	actorPb "github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	livPb "github.com/epifi/gamma/api/auth/liveness"
	bankCustomerPb "github.com/epifi/gamma/api/bankcust"
	watsonPb "github.com/epifi/gamma/api/cx/watson"
	employmentPb "github.com/epifi/gamma/api/employment"
	vkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	omeglePb "github.com/epifi/gamma/api/omegle"
	"github.com/epifi/gamma/api/product"
	savingsPb "github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/user"
	userPb "github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/pkg/auth"
	"github.com/epifi/gamma/pkg/cx"
	onbDao "github.com/epifi/gamma/user/onboarding/dao"
	"github.com/epifi/gamma/user/onboarding/helper"
)

const onboardingIssueCategoryId = "c86c3d74-6a82-542b-bafe-7743a4721618"

type PreOnboardingResetHandler struct {
	onbDao           onbDao.OnboardingDao
	vkycClient       vkycPb.VKYCClient
	livenessClient   livPb.LivenessClient
	userProcessor    helper.UserProcessor
	savClient        savingsPb.SavingsClient
	kycHelper        helper.IKYCHelper
	Time             datetime.Time
	userClient       userPb.UsersClient
	actorClient      actorPb.ActorClient
	bcClient         bankCustomerPb.BankCustomerServiceClient
	authClient       authPb.AuthClient
	watsonClient     watsonPb.WatsonClient
	productClient    product.ProductClient
	employmentClient employmentPb.EmploymentClient
	omegleClient     omeglePb.OmegleClient
}

func NewPreOnboardingResetHandler(onbDao onbDao.OnboardingDao, vkycClient vkycPb.VKYCClient, livenessClient livPb.LivenessClient,
	userProcessor helper.UserProcessor, savClient savingsPb.SavingsClient, kycHelper helper.IKYCHelper, time datetime.Time,
	userClient userPb.UsersClient, actorClient actorPb.ActorClient, bcClient bankCustomerPb.BankCustomerServiceClient,
	authClient authPb.AuthClient, watsonClient watsonPb.WatsonClient, productClient product.ProductClient,
	employmentClient employmentPb.EmploymentClient, omegleClient omeglePb.OmegleClient) *PreOnboardingResetHandler {
	return &PreOnboardingResetHandler{
		onbDao:           onbDao,
		vkycClient:       vkycClient,
		livenessClient:   livenessClient,
		userProcessor:    userProcessor,
		savClient:        savClient,
		kycHelper:        kycHelper,
		Time:             time,
		userClient:       userClient,
		actorClient:      actorClient,
		bcClient:         bcClient,
		authClient:       authClient,
		watsonClient:     watsonClient,
		productClient:    productClient,
		employmentClient: employmentClient,
		omegleClient:     omegleClient,
	}
}

func (s *PreOnboardingResetHandler) IsResetAllowed(ctx context.Context, req *IsResetAllowedRequest) error {
	onb, err := s.onbDao.GetOnboardingDetailsByActor(ctx, req.GetActorId())
	if err != nil {
		if storagev2.IsRecordNotFoundError(err) {
			logger.Info(ctx, "record not found")
			return epifierrors.ErrRecordNotFound
		}
		logger.Error(ctx, "error in getting onboarding details", zap.Error(err))
		return err
	}

	if req.GetDeletionDetails().GetDeletionReason() == user.DeletionDetails_DELETION_REASON_AFU_CONFLICT_PHONE ||
		req.GetDeletionDetails().GetDeletionReason() == user.DeletionDetails_DELETION_REASON_AFU_CONFLICT_EMAIL ||
		req.GetDeletionDetails().GetDeletionReason() == user.DeletionDetails_DELETION_REASON_AFU_BEFORE_DEV_REG ||
		req.GetDeletionDetails().GetDeletionReason() == user.DeletionDetails_DELETION_REASON_AFU_BEFORE_CUSTOMER_CREATION {
	}

	if err = s.allowResetUser(ctx, onb); err != nil {
		if rpc.StatusFromError(err).IsPermissionDenied() {
			return epifierrors.ErrPermissionDenied
		}
		return err
	}
	return nil
}

func (s *PreOnboardingResetHandler) DeleteUserDetails(ctx context.Context, req *DeleteUserDetailsRequest) error {
	var (
		user *userPb.User
	)
	onb, err := s.onbDao.GetOnboardingDetailsByActor(ctx, req.GetActorId())
	if err != nil {
		if storagev2.IsRecordNotFoundError(err) {
			logger.Info(ctx, "record not found")
			return epifierrors.ErrRecordNotFound
		}
		logger.Error(ctx, "error in getting onboarding details", zap.Error(err))
		return err
	}
	if stageStatus(onb, onbPb.OnboardingStage_DEVICE_REGISTRATION).IsSuccessOrSkipped() {
		if errDeReg := s.permanentDeregisterDevice(ctx, onb.GetActorId()); errDeReg != nil {
			return errDeReg
		}
	}
	userId := onb.GetUserId()
	if userId != "" {
		// get user
		user, err = s.getUser(ctx, userId)
		if err != nil {
			return err
		}

		// user not already deleted
		if user != nil {
			if err = s.deleteTokens(ctx, user, req.GetDeletionDetails().GetDeletionReason()); err != nil {
				return err
			}

			if err = s.deleteUser(ctx, onb.GetUserId(), req.GetDeletionDetails()); err != nil {
				return err
			}

			if err = s.deleteBankCustomer(ctx, onb.GetActorId(), onb.GetVendor()); err != nil {
				return err
			}
		}
	}

	// resolve auto id tickets as onboarding is being reset
	if err = s.resolveIncident(ctx, onb.ActorId); err != nil {
		logger.Error(ctx, "error while resolving watson incident", zap.Error(err))
	}
	if err = deleteEmploymentData(ctx, s.employmentClient, req.GetActorId()); err != nil {
		return err
	}
	return nil
}

func (s *PreOnboardingResetHandler) allowResetUser(ctx context.Context, onb *onbPb.OnboardingDetails) error {
	if stageStatus(onb, onbPb.OnboardingStage_VKYC).IsSuccess() || stageStatus(onb, onbPb.OnboardingStage_RISK_SCREENING).IsFailure() ||
		stageStatus(onb, onbPb.OnboardingStage_RISK_SCREENING).IsManualIntervention() {
		logger.Info(ctx, "user cannot be deleted")
		return epifierrors.ErrPermissionDenied
	}

	if err := CheckNonResidentUser(ctx, s.userProcessor, s.omegleClient, onb); err != nil {
		return err
	}

	if err := s.vkycCheck(ctx, onb.GetActorId()); err != nil {
		return err
	}

	if err := s.livenessAndFmCheck(ctx, onb.GetActorId(), auth.GetLivenessSummaryReqId(onb)); err != nil {
		return err
	}

	inProgress, errCheck := s.userProcessor.IsCustomerCreationInProgress(ctx, onb.GetActorId())
	if errCheck != nil {
		return fmt.Errorf("failed to check customer creation status %w", errCheck)
	}
	if inProgress {
		logger.Info(ctx, "user cannot be deleted")
		return epifierrors.ErrPermissionDenied
	}

	getAccResp, errGetAcc := s.savClient.GetSavingsAccountEssentials(ctx, &savingsPb.GetSavingsAccountEssentialsRequest{
		Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
			ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
				ActorId:     onb.GetActorId(),
				PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
			},
		},
	})
	if grpcErr := epifigrpc.RPCError(getAccResp, errGetAcc); grpcErr != nil && !rpc.StatusFromError(grpcErr).IsRecordNotFound() {
		return grpcErr
	}
	account := getAccResp.GetAccount()
	if account != nil &&
		account.GetState() != savingsPb.State_FAILED {
		return epifierrors.ErrPermissionDenied
	}

	isActiveUser, errActiveUser := IsActiveProductUser(ctx, s.productClient, onb.GetActorId())
	if errActiveUser != nil {
		return errActiveUser
	}
	if isActiveUser {
		return epifierrors.ErrPermissionDenied
	}

	return nil
}

// vkycCheck returns permission denied if vkyc status is in approved, rejected or in review state
func (s *PreOnboardingResetHandler) vkycCheck(ctx context.Context, actorId string) error {
	vkycRes, err := s.vkycClient.GetVKYCStatus(ctx, &vkycPb.GetVKYCStatusRequest{
		ActorId: actorId,
	})
	if grpcErr := epifigrpc.RPCError(vkycRes, err); grpcErr != nil {
		if vkycRes.GetStatus().IsRecordNotFound() {
			logger.Debug(ctx, "vkyc record not found", zap.Error(grpcErr))
			return nil
		}
		logger.Error(ctx, "failed to fetch vkyc status", zap.Error(grpcErr))
		return grpcErr
	}

	vkycStatus := vkycRes.GetVkycSummary().GetStatus()
	if vkycStatus == vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_APPROVED ||
		vkycStatus == vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_REJECTED {
		logger.Info(ctx, "user cannot be deleted")
		return epifierrors.ErrPermissionDenied
	}

	if vkycStatus == vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_IN_REVIEW &&
		s.Time.Since(vkycRes.GetVkycSummary().GetUpdatedAt().AsTime()) <= 7*24*time.Hour { // TODO(Shivam): Make this config driven
		logger.Info(ctx, "user cannot be deleted")
		return epifierrors.ErrPermissionDenied
	}

	return nil
}

func (s *PreOnboardingResetHandler) livenessAndFmCheck(ctx context.Context, actorId string, summaryReqId string) error {
	getLivSummaryRes, errGetLivSummary := s.livenessClient.GetLivenessSummary(ctx, &livPb.GetLivenessSummaryRequest{
		ActorId:      actorId,
		RequestId:    summaryReqId,
		LivenessFlow: livPb.LivenessFlow_ONBOARDING,
	})
	if grpcErr := epifigrpc.RPCError(getLivSummaryRes, errGetLivSummary); grpcErr != nil && !rpc.StatusFromError(grpcErr).IsRecordNotFound() {
		logger.Error(ctx, "failed to get liveness summary", zap.Error(grpcErr))
		return grpcErr
	}

	livReqId := getLivSummaryRes.GetSummary().GetLivenessAttemptId()
	if livReqId == "" {
		kycStatusRes, err := s.kycHelper.CheckKYCStatus(ctx, actorId)
		if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "some error in CheckKYCStatus", zap.Error(err))
			return err
		}

		livReqId = kycStatusRes.GetLivenessOptions().GetRequestId()
	}

	if livReqId != "" {
		livStatusRes, errLivStatus := s.livenessClient.GetLivenessStatus(ctx, &livPb.GetLivenessStatusRequest{
			ActorId:   actorId,
			AttemptId: livReqId,
		})
		if grpcErr := epifigrpc.RPCError(livStatusRes, errLivStatus); grpcErr != nil {
			if !livStatusRes.GetStatus().IsRecordNotFound() {
				logger.Error(ctx, "some error in GetLivenessAttempts", zap.Error(grpcErr))
				return grpcErr
			}
		}

		if livStatusRes.GetLivenessStatus() == livPb.LivenessStatus_LIVENESS_MANUALLY_FAILED {
			logger.Info(ctx, "user cannot be deleted")
			return epifierrors.ErrPermissionDenied
		}
	}

	fmAttemptId := getLivSummaryRes.GetSummary().GetFacematchAttemptId()
	if fmAttemptId != "" {
		fmStatusRes, errFmStatus := s.livenessClient.GetFaceMatchStatus(ctx, &livPb.GetFaceMatchStatusRequest{
			ActorId:   actorId,
			AttemptId: fmAttemptId,
		})
		if grpcErr := epifigrpc.RPCError(fmStatusRes, errFmStatus); grpcErr != nil {
			if !fmStatusRes.GetStatus().IsRecordNotFound() {
				logger.Error(ctx, "some error in GetFaceMatchStatus", zap.Error(grpcErr))
				return grpcErr
			}
		}

		if fmStatusRes.GetFaceMatchStatus() == livPb.FaceMatchStatus_FACE_MATCH_MANUALLY_FAILED {
			logger.Info(ctx, "user cannot be deleted")
			return epifierrors.ErrPermissionDenied
		}
	}

	return nil
}

func (s *PreOnboardingResetHandler) permanentDeregisterDevice(ctx context.Context, actorId string) error {
	deActResp, errDeAct := s.authClient.DeactivateDevice(ctx, &authPb.DeactivateDeviceRequest{
		ActorId:          actorId,
		DeactivationType: authPb.DeactivateDeviceRequest_DEACTIVATION_TYPE_PERMANENT,
	})

	return epifigrpc.RPCError(deActResp, errDeAct)
}

func (s *PreOnboardingResetHandler) deleteActor(ctx context.Context, actorId string) error {
	delActor, err := s.actorClient.DeleteActor(ctx, &actorPb.DeleteActorRequest{
		ActorId: actorId,
	})
	if err = epifigrpc.RPCError(delActor, err); err != nil && !rpc.StatusFromError(err).IsRecordNotFound() {
		return err
	}
	return nil
}

func (s *PreOnboardingResetHandler) getUser(ctx context.Context, userId string) (*userPb.User, error) {
	userRes, err := s.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_Id{
			Id: userId,
		},
	})
	if err = epifigrpc.RPCError(userRes, err); err != nil && !rpc.StatusFromError(err).IsRecordNotFound() {
		return nil, err
	}
	return userRes.GetUser(), nil
}

func (s *PreOnboardingResetHandler) deleteTokens(ctx context.Context, user *userPb.User, deletionReason userPb.DeletionDetails_DeletionReason) error {
	tokenTypes := []authPb.TokenType{
		authPb.TokenType_ACCESS_TOKEN,
		authPb.TokenType_REFRESH_TOKEN,
	}
	// omitting refresh token deletion to prevent sign out as the user trying to perform AFU has the same phone number
	if deletionReason == userPb.DeletionDetails_DELETION_REASON_AFU_CONFLICT_PHONE {
		tokenTypes = []authPb.TokenType{
			authPb.TokenType_ACCESS_TOKEN,
		}
	}
	delAuthToken, err := s.authClient.UpdateToken(ctx, &authPb.UpdateTokenRequest{
		Status: authPb.UpdateTokenRequest_DELETE,
		Identifier: &authPb.UpdateTokenRequest_PhoneNumber{
			PhoneNumber: user.GetProfile().GetPhoneNumber(),
		},
		TokenTypes:          tokenTypes,
		TokenUpdationReason: authPb.TokenDeletionReason_TOKEN_DELETION_REASON_RESET_USER,
	})
	if err = epifigrpc.RPCError(delAuthToken, err); err != nil && !delAuthToken.GetStatus().IsRecordNotFound() {
		return fmt.Errorf("error in delete auth tokens: %v", err)
	}
	return nil
}

func (s *PreOnboardingResetHandler) deleteBankCustomer(ctx context.Context, actorId string, vendor commonvgpb.Vendor) error {
	delBankCustomer, err := s.bcClient.DeleteBankCustomer(ctx, &bankCustomerPb.DeleteBankCustomerRequest{
		ActorId: actorId,
		Vendor:  vendor,
	})
	if rpcErr := epifigrpc.RPCError(delBankCustomer, err); rpcErr != nil {
		if !delBankCustomer.GetStatus().IsRecordNotFound() {
			logger.Error(ctx, "error in deleting bank customer", zap.Error(rpcErr))
			return rpcErr
		}
		logger.Info(ctx, "bank customer record not found")
	}
	return nil
}

func (s *PreOnboardingResetHandler) deleteUser(ctx context.Context, userId string, deletionDetails *userPb.DeletionDetails) error {
	delUser, err := s.userClient.DeleteUser(ctx, &userPb.DeleteUserRequest{
		UserId:          userId,
		DeletionDetails: deletionDetails,
	})
	if err = epifigrpc.RPCError(delUser, err); err != nil {
		logger.Error(ctx, "error in deleting user", zap.Error(err))
		return err
	}
	return nil
}

// stageStatus extracts OnboardingState of input OnboardingStage from OnboardingDetails
func stageStatus(onb *onbPb.OnboardingDetails, stage onbPb.OnboardingStage) onbPb.OnboardingState {
	return onb.GetStageDetails().GetStageMapping()[stage.String()].GetState()
}

func (s *PreOnboardingResetHandler) resolveIncident(ctx context.Context, actorId string) error {
	if !cx.IsIncidentPresent(ctx, s.watsonClient, actorId, onboardingIssueCategoryId) {
		return nil
	}
	resp, ingErr := s.watsonClient.IngestEvent(ctx, &watsonPb.IngestEventRequest{
		EventType:       watsonPb.EventType_EVENT_TYPE_INCIDENT_RESOLUTION,
		Client:          types.ServiceName_ONBOARDING_SERVICE,
		ActorId:         actorId,
		ClientRequestId: generateClientRequestId(actorId),
		IssueCategoryId: "c86c3d74-6a82-542b-bafe-7743a4721618",
		IncidentCategory: &watsonPb.IncidentCategory{
			ProductCategory:        types.ProductCategory_PRODUCT_CATEGORY_ONBOARDING,
			ProductCategoryDetails: types.ProductCategoryDetails_PRODUCT_CATEGORY_DETAILS_ONBOARDING_ACCOUNT_OPENING_DELAYED,
		},
	})
	if err := epifigrpc.RPCError(resp, ingErr); err != nil {
		logger.Error(ctx, "error while resolving watson incident", zap.Error(err))
		return err
	}
	return nil
}

func generateClientRequestId(actorId string) string {
	clientRequestId := fmt.Sprintf("ACCTSETUP%s", actorId)
	return clientRequestId
}
