package stageproc

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	leadsPb "github.com/epifi/gamma/api/leads"

	inAppReferralEnumPb "github.com/epifi/gamma/api/inappreferral/enums"
	"github.com/epifi/gamma/pkg/feature/release"
	"github.com/epifi/gamma/user/onboarding/helper/deeplink"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/frontend/app/apputils"
	"github.com/epifi/be-common/pkg/logger"

	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/inappreferral"
	"github.com/epifi/gamma/api/user"
	userGroup "github.com/epifi/gamma/api/user/group"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/user/config/genconf"
	userEvents "github.com/epifi/gamma/user/events"
	"github.com/epifi/gamma/user/onboarding/dao"
	"github.com/epifi/gamma/user/onboarding/helper"
	"github.com/epifi/gamma/user/onboarding/metrics"
)

type IntentSelectionStage struct {
	onbConf         *genconf.OnboardingConfig
	userClient      user.UsersClient
	userGroupClient userGroup.GroupClient
	onbDao          dao.OnboardingDao
	dlHelper        helper.DeeplinkHelper
	timeClient      datetime.Time
	eventLogger     userEvents.EventLogger
	inAppRefClient  inappreferral.InAppReferralClient
	leadsClient     leadsPb.UserLeadSvcClient
}

func NewIntentSelectionStage(onbConf *genconf.OnboardingConfig, userClient user.UsersClient,
	onbDao dao.OnboardingDao, dlHelper helper.DeeplinkHelper, timeClient datetime.Time,
	eventLogger userEvents.EventLogger, inAppRefClient inappreferral.InAppReferralClient,
	userGroupClient userGroup.GroupClient, leadsClient leadsPb.UserLeadSvcClient,
) *IntentSelectionStage {
	return &IntentSelectionStage{
		onbConf:         onbConf,
		userClient:      userClient,
		userGroupClient: userGroupClient,
		onbDao:          onbDao,
		dlHelper:        dlHelper,
		eventLogger:     eventLogger,
		timeClient:      timeClient,
		inAppRefClient:  inAppRefClient,
		leadsClient:     leadsClient,
	}
}

var (
	acquisitionIntentToOnboardingIntentMap = map[user.AcquisitionIntent]onbPb.OnboardingIntent{
		user.AcquisitionIntent_ACQUISITION_INTENT_BANKING:         onbPb.OnboardingIntent_ONBOARDING_INTENT_FEDERAL_SAVINGS_ACCOUNT,
		user.AcquisitionIntent_ACQUISITION_INTENT_CREDIT_CARDS:    onbPb.OnboardingIntent_ONBOARDING_INTENT_CREDIT_CARD,
		user.AcquisitionIntent_ACQUISITION_INTENT_PERSONAL_LOANS:  onbPb.OnboardingIntent_ONBOARDING_INTENT_PERSONAL_LOANS,
		user.AcquisitionIntent_ACQUISITION_INTENT_NET_WORTH:       onbPb.OnboardingIntent_ONBOARDING_INTENT_NET_WORTH,
		user.AcquisitionIntent_ACQUISITION_INTENT_DEBIT_CARD:      onbPb.OnboardingIntent_ONBOARDING_INTENT_FEDERAL_SAVINGS_ACCOUNT,
		user.AcquisitionIntent_ACQUISITION_INTENT_WEALTH_ANALYSER: onbPb.OnboardingIntent_ONBOARDING_INTENT_WEALTH_ANALYSER,
	}
)

const (
	IntentScreenFeature = "feature_intent_selection"
)

func (s *IntentSelectionStage) StageProcessor(ctx context.Context, req *StageProcessorRequest) (*StageProcessorResponse, error) {
	onb := req.GetOnb()
	if getStageStatus(onb, onbPb.OnboardingStage_INTENT_SELECTION) != onbPb.OnboardingState_RESET && isUserPastCurrentStage(SAOnboardingStagesOrder, req.GetOnb()) {
		return nil, NoActionError
	}

	if req.GetOnb().GetStageMetadata().GetIntentSelectionMetadata().GetSelection() != onbPb.OnboardingIntent_ONBOARDING_INTENT_UNSPECIFIED {
		return nil, NoActionError
	}

	if !isIntentSelectionScreenEnabledForUser(ctx, req.GetOnb().GetActorId(), s.onbConf.IntentSelectionConfigV2()) {
		return nil, NoActionError
	}

	acIntent, acChannel, err := getIntentAndChannelFromAcquisitionInfo(ctx, s.onbConf, s.userClient, onb.GetActorId())
	if err != nil {
		return nil, errors.Wrap(err, "error in getIntentAndChannelFromAcquisitionInfo")
	}

	// if intent is not identified from acquisition info, we will try to get it from user group
	if acIntent == user.AcquisitionIntent_ACQUISITION_INTENT_UNSPECIFIED {
		acIntent, acChannel, err = getIntentAndChannelFromUserGroup(ctx, s.userClient, s.userGroupClient, onb.GetActorId())
		if err != nil {
			return nil, errors.Wrap(err, "error in getIntentAndChannelFromUserGroup")
		}
	}

	if shouldDirectUserToFiLite(ctx, s.onbConf, acIntent, acChannel) {
		s.logAndSendEventForDirectToHome(ctx, onb.GetActorId(), acIntent, acChannel, s.onbConf.DirectToFiLite().Variant(ctx))
		if updErr := s.updateFiLiteDetailsAndIntentStage(ctx, onb, onbPb.FiLiteSource_FI_LITE_SOURCE_DIRECT_TO_FI_LITE); updErr != nil {
			return nil, fmt.Errorf("error updating status to fi lite enabled : %w", updErr)
		}
		return &StageProcessorResponse{
			NextAction: &dlPb.Deeplink{
				Screen: dlPb.Screen_GET_NEXT_ONBOARDING_ACTION_API,
				ScreenOptions: &dlPb.Deeplink_GetNextOnboardingActionScreenOptions{
					GetNextOnboardingActionScreenOptions: &dlPb.GetNextOnboardingActionScreenOptions{
						Feature: onbPb.Feature_FEATURE_FI_LITE.String(),
					},
				},
			},
		}, nil
	}

	intentOverrideInfo := onb.GetStageMetadata().GetIntentSelectionMetadata().GetIntentOverrideInfo()
	if intentOverrideInfo == nil {
		intentOverrideInfo = &onbPb.IntentOverrideInfo{}
	}
	if shouldOverrideIntentForActiveLeads(acIntent) {
		// TODO (sayan): store the override info in the database - https://monorail.pointz.in/p/fi-app/issues/detail?id=97389
		overrideIntent := s.getForcedIntentForActiveLeads(ctx, onb.GetActorId())
		if overrideIntent != user.AcquisitionIntent_ACQUISITION_INTENT_UNSPECIFIED {
			acIntent = overrideIntent
			intentOverrideInfo.OverriddenByActiveLead = true
		}
	}

	// if we can get intent from acquisition channel, we will not show user the screen to select intent
	dl, err := s.handleAutoIntent(ctx, acIntent, acChannel, onb, intentOverrideInfo)
	if err != nil {
		return nil, err
	}
	if dl != nil {
		return &StageProcessorResponse{
			NextAction: dl,
		}, nil
	}

	if onb.GetFeature() != onbPb.Feature_FEATURE_UNSPECIFIED && onb.GetFeature() != onbPb.Feature_FEATURE_FI_LITE {
		logger.Info(ctx, "feature was already captured for the user, skipping intent selection")
		return nil, SkipStageError
	}

	isReferredUser, userClaimedFiniteCode := s.getUserReferralDetails(ctx, onb.GetActorId())
	if isReferredUser {
		acquisitionIntent := s.getUserAcquisitionIntentFromClaimedReferralCode(ctx, userClaimedFiniteCode)
		dl, err = s.handleAutoIntent(ctx, acquisitionIntent, user.AcquisitionChannel_ACQUISITION_CHANNEL_REFERRALS, onb, nil)
		if err != nil {
			logger.Error(ctx, "error in getting next action for refereed user", zap.Error(err))
			return nil, err
		}
		return &StageProcessorResponse{
			NextAction: dl,
		}, nil
	}

	dl, err = s.dlHelper.GetIntentSelectionDL(ctx, onb.GetActorId(), onbPb.IntentSelectionEntryPoint_INTENT_SELECTION_ENTRY_POINT_ONBOARDING_INTENT_SELECTION_STAGE.String())
	if err != nil {
		logger.Error(ctx, "failure while calling GetIntentSelectionDL", zap.Error(err))
		return nil, err
	}

	s.eventLogger.LogLoadedIntentScreenServer(ctx, onb.GetActorId(), dl)

	return &StageProcessorResponse{
		NextAction: dl,
	}, nil
}

func shouldOverrideIntentForActiveLeads(acqIntent user.AcquisitionIntent) bool {
	return acqIntent != user.AcquisitionIntent_ACQUISITION_INTENT_PERSONAL_LOANS
}

func (s *IntentSelectionStage) getForcedIntentForActiveLeads(ctx context.Context, actorId string) user.AcquisitionIntent {
	userRes, err := s.userClient.GetUser(ctx, &user.GetUserRequest{
		Identifier: &user.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if err = epifigrpc.RPCError(userRes, err); err != nil {
		logger.Error(ctx, "error in getting user info for active leads", zap.Error(err))
		return user.AcquisitionIntent_ACQUISITION_INTENT_UNSPECIFIED
	}

	activeLeadsRes, err := s.leadsClient.GetActiveLeads(ctx, &leadsPb.GetActiveLeadsRequest{
		PhoneNumber: userRes.GetUser().GetProfile().GetPhoneNumber(),
		Email:       userRes.GetUser().GetProfile().GetEmail(),
	})
	if err = epifigrpc.RPCError(activeLeadsRes, err); err != nil {
		if activeLeadsRes.GetStatus().IsRecordNotFound() {
			return user.AcquisitionIntent_ACQUISITION_INTENT_UNSPECIFIED
		}
		logger.Error(ctx, "error in getting active leads", zap.Error(err))
		return user.AcquisitionIntent_ACQUISITION_INTENT_UNSPECIFIED
	}
	if len(activeLeadsRes.GetProductTypeToActiveLeadMap()) == 0 {
		return user.AcquisitionIntent_ACQUISITION_INTENT_UNSPECIFIED
	}

	// PL product has highest priority, so if there is an active lead for PL product, override the acquisition intent tp PL
	if _, ok := activeLeadsRes.GetProductTypeToActiveLeadMap()[int32(leadsPb.ProductType_PRODUCT_TYPE_FI_PERSONAL_LOAN)]; ok {
		return user.AcquisitionIntent_ACQUISITION_INTENT_PERSONAL_LOANS
	}
	return user.AcquisitionIntent_ACQUISITION_INTENT_UNSPECIFIED
}

func (s *IntentSelectionStage) logAndSendEventForDirectToHome(ctx context.Context, actorId string, acIntent user.AcquisitionIntent, acChannel user.AcquisitionChannel, variant string) {
	logger.Info(ctx, "direct to fi lite enabled for user", zap.String(logger.ACTOR_ID_V2, actorId), zap.String("variant", variant))
	s.eventLogger.LogStartedDirectToHome(ctx, actorId, acIntent, acChannel, variant)
}

// getUserReferralDetails returns whether user is referred user and returns the claimed finite code if referred
func (s *IntentSelectionStage) getUserReferralDetails(ctx context.Context, actorId string) (bool, *inappreferral.FiniteCode) {
	resp, err := s.inAppRefClient.GetClaimedFiniteCodeForActor(ctx, &inappreferral.GetClaimedFiniteCodeForActorRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil && !rpc.StatusFromError(rpcErr).IsRecordNotFound() {
		logger.Error(ctx, "error in getting referral details", zap.Error(rpcErr))
		return false, nil
	}

	return resp.GetStatus().IsSuccess(), resp.GetFiniteCode()
}

func isIntentSelectionScreenEnabledForUser(ctx context.Context, actorId string, conf *genconf.IntentSelectionConfigV2) bool {
	return apputils.IsFeatureEnabledFromCtxDynamic(ctx, conf.IntentCollectionScreenFeatureConfig()) &&
		CheckReleaseStickinessConstraint(ctx, actorId, IntentScreenFeature, conf.IntentCollectionScreenPercentageRollout())
}

func (s *IntentSelectionStage) handleAutoIntent(ctx context.Context,
	acIntent user.AcquisitionIntent, acChannel user.AcquisitionChannel,
	onb *onbPb.OnboardingDetails, intentOverrideInfo *onbPb.IntentOverrideInfo) (*dlPb.Deeplink, error) {
	logger.Info(ctx, "acquisition auto intent fetched", zap.String(logger.STATUS, acIntent.String()))

	if onbIntent, ok := acquisitionIntentToOnboardingIntentMap[acIntent]; ok {
		metrics.RecordOnboardingAutoIntent(onbIntent, acChannel)

		switch onbIntent {
		case onbPb.OnboardingIntent_ONBOARDING_INTENT_FEDERAL_SAVINGS_ACCOUNT:
			return s.getNextActionForAutoIntent(ctx, onb, onbIntent, onbPb.Feature_FEATURE_SA, intentOverrideInfo)
		case onbPb.OnboardingIntent_ONBOARDING_INTENT_CREDIT_CARD:
			if !s.isIntentEnabled(ctx, onbIntent) {
				return nil, nil
			}
			return s.getNextActionForAutoIntent(ctx, onb, onbIntent, onbPb.Feature_FEATURE_CC, intentOverrideInfo)
		case onbPb.OnboardingIntent_ONBOARDING_INTENT_PERSONAL_LOANS:
			return s.getNextActionForAutoIntent(ctx, onb, onbIntent, onbPb.Feature_FEATURE_PL, intentOverrideInfo)
		case onbPb.OnboardingIntent_ONBOARDING_INTENT_NET_WORTH:
			return s.getNextActionForAutoIntent(ctx, onb, onbIntent, onbPb.Feature_FEATURE_FI_LITE, intentOverrideInfo)
		case onbPb.OnboardingIntent_ONBOARDING_INTENT_WEALTH_ANALYSER:
			return s.getNextActionForAutoIntent(ctx, onb, onbIntent, onbPb.Feature_FEATURE_WEALTH_ANALYSER, intentOverrideInfo)
		default:
			logger.Error(ctx, fmt.Sprintf("unexpected onboarding intent recieved: %v", onbIntent.String()))
			// making this non-blocking and letting user select intent
			return nil, nil
		}
	}

	return nil, nil
}

// getNextActionForAutoIntent updates intent and feature based on acquisition intent and returns the next action
func (s *IntentSelectionStage) getNextActionForAutoIntent(ctx context.Context, onb *onbPb.OnboardingDetails, onbIntent onbPb.OnboardingIntent, feature onbPb.Feature, intentOverrideInfo *onbPb.IntentOverrideInfo) (*dlPb.Deeplink, error) {
	if err := s.updateIntentAndFeature(ctx, onb, onbIntent, true, feature, intentOverrideInfo); err != nil {
		return nil, errors.Wrap(err, "error in updateIntentAndFeature")
	}
	s.eventLogger.LogSetOnboardingAutoIntentServer(ctx, onb.GetActorId(), onbIntent)
	switch onbIntent {
	case onbPb.OnboardingIntent_ONBOARDING_INTENT_NET_WORTH:
		if err := s.updateFiLiteDetailsAndIntentStage(ctx, onb, onbPb.FiLiteSource_FI_LITE_SOURCE_INTENT_SELECTION); err != nil {
			return nil, fmt.Errorf("error in updating fi lite details : %w", err)
		}
		return deeplink.NewActionToGetNextActionForFiLite(), nil
	default:
		return deeplink.NewActionToGetNextAction(), nil
	}
}

func (s *IntentSelectionStage) getUserAcquisitionIntentFromClaimedReferralCode(ctx context.Context, userClaimedFiniteCode *inappreferral.FiniteCode) user.AcquisitionIntent {
	if intent, ok := s.onbConf.IntentSelectionConfigV2().FiniteCodeToAcquisitionIntentMap()[userClaimedFiniteCode.GetCode()]; ok {
		logger.Info(ctx, "acquisition intent fetched from referral code used", zap.String("intent", intent.String()), zap.String(logger.FINITE_CODE, userClaimedFiniteCode.GetCode()))
		return intent
	}

	if userClaimedFiniteCode.GetType() == inAppReferralEnumPb.FiniteCodeType_D2H_REGULAR {
		logger.Info(ctx, "acquisition intent fetched from finite code type", zap.String("intent", user.AcquisitionIntent_ACQUISITION_INTENT_WEALTH_ANALYSER.String()),
			zap.String("finiteCodeType", userClaimedFiniteCode.GetType().String()), zap.String(logger.FINITE_CODE, userClaimedFiniteCode.GetCode()))
		return user.AcquisitionIntent_ACQUISITION_INTENT_WEALTH_ANALYSER
	}

	return user.AcquisitionIntent_ACQUISITION_INTENT_BANKING
}
func (s *IntentSelectionStage) updateFeature(ctx context.Context, onb *onbPb.OnboardingDetails, feature onbPb.Feature) error {
	onb.Feature = feature
	if err := s.onbDao.UpdateOnboardingDetailsByColumns(ctx, []onbPb.OnboardingDetailsFieldMask{
		onbPb.OnboardingDetailsFieldMask_FEATURE,
	}, onb); err != nil {
		logger.Error(ctx, "error in update onb details by columns", zap.Error(err))
		return err
	}
	return nil
}

func (s *IntentSelectionStage) updateIntentAndFeature(ctx context.Context, onb *onbPb.OnboardingDetails, intent onbPb.OnboardingIntent, autoIntent bool, feature onbPb.Feature, intentOverrideInfo *onbPb.IntentOverrideInfo) error {
	if onb.StageMetadata == nil {
		onb.StageMetadata = &onbPb.StageMetadata{}
	}
	if onb.StageMetadata.IntentSelectionMetadata == nil {
		onb.StageMetadata.IntentSelectionMetadata = &onbPb.IntentSelectionMetadata{
			Selection:          intent,
			AutoIntent:         autoIntent,
			IntentOverrideInfo: intentOverrideInfo,
		}
	}
	onb.Feature = feature

	if err := s.onbDao.UpdateOnboardingDetailsByColumns(ctx, []onbPb.OnboardingDetailsFieldMask{
		onbPb.OnboardingDetailsFieldMask_STAGE_METADATA,
		onbPb.OnboardingDetailsFieldMask_FEATURE,
	}, onb); err != nil {
		logger.Error(ctx, "error in update onb details by columns", zap.Error(err))
		return err
	}
	if feature == onbPb.Feature_FEATURE_CC {
		s.eventLogger.LogOnboardingCCJourneyStarted(ctx, onb)
	}
	return nil
}

func (s *IntentSelectionStage) updateFiLiteDetailsAndIntentStage(ctx context.Context, onb *onbPb.OnboardingDetails, source onbPb.FiLiteSource) error {
	_, updErr := s.onbDao.UpdateStatus(ctx, onb.GetOnboardingId(), onbPb.OnboardingStage_INTENT_SELECTION, onbPb.OnboardingState_SKIPPED)
	if updErr != nil {
		return fmt.Errorf("error updating intent selection stage status as skipped : %w", updErr)
	}

	if onb.FiLiteDetails == nil {
		onb.FiLiteDetails = &onbPb.FiLiteDetails{}
	}

	onb.FiLiteDetails.IsEnabled = commontypes.BooleanEnum_TRUE
	if onb.FiLiteDetails.AccessibilityEnabledAt == nil {
		onb.FiLiteDetails.AccessibilityEnabledAt = timestampPb.New(s.timeClient.Now())
	}
	onb.Feature = onbPb.Feature_FEATURE_FI_LITE
	onb.FiLiteDetails.FiLiteSource = source

	return s.onbDao.UpdateOnboardingDetailsByColumns(ctx, []onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_FEATURE, onbPb.OnboardingDetailsFieldMask_FI_LITE_DETAILS}, onb)
}

func (s *IntentSelectionStage) isIntentEnabled(ctx context.Context, onbIntent onbPb.OnboardingIntent) bool {
	intentConf, ok := s.onbConf.IntentSelectionConfigV2().IntentConfigMap().Load(onbIntent.String())
	if !ok {
		logger.Error(ctx, fmt.Sprintf("config not found for OnboardingIntent enum string : %v", onbIntent))
		return false
	}
	return apputils.IsFeatureEnabledFromCtxDynamic(ctx, intentConf.FeatureConfig())
}

// wealthAnalyserReleaseConstraint is a custom function to support fractional release of wealth analyser to external with a base of 100
// nolint: dupl
func wealthAnalyserReleaseConstraint(ctx context.Context, actorId string, rolloutPercentage int) bool {
	feature := "wealth_analyser_external_release"
	featureAndActor := fmt.Sprintf("%v%v", feature, actorId)
	num, err := release.GetHashNum(featureAndActor)
	if err != nil {
		logger.Error(ctx, "error while generating hash for actor", zap.Error(err))
		return false
	}
	logger.Debug(ctx, fmt.Sprintf("check %v split release stickiness: %v %v, %v, %v", feature, actorId, num, num%100,
		rolloutPercentage))
	if num%100 < uint64(rolloutPercentage) {
		logger.Debug(ctx, fmt.Sprintf("%v ReleaseStickinessConstraint evaluation returned true", feature))
		return true
	}
	return false
}
