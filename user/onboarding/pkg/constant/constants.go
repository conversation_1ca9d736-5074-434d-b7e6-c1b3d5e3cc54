// nolint
package constant

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"github.com/epifi/gamma/api/consent"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	onboardingPkg "github.com/epifi/gamma/pkg/onboarding"
)

// TODO(aditya): move constants from other files here
const (
	PartnerBank = commonvgpb.Vendor_FEDERAL_BANK

	StandardTimeLayout = "2006-01-02 15:04:05.000"

	// DebitCardNameMaxRetries denotes max retries given to a user
	// to enter a name to be printed on debit card that matches their KYC Name.
	DebitCardNameMaxRetries = 10

	/*
		Dedupe check error messages
	*/
	NriETBUnsupportedTitle                = "Support for Federal Bank customers coming soon!"
	NriETBUnsupportedSubtitle             = "Looks like you're in existing Federal Bank customer. Your phone number, email, or PAN is linked to an existing Federal Bank account. We're actively working on providing support for existing customers. Stay tuned—we'll notify you as soon as it's ready!"
	DedupeNRICheckFailureTitle            = "Looks like you are an NRI"
	DedupeNRICheckFailureMessage          = "Any Non-Resident Indian (NRI) with an existing Federal a/c currently does not have access to the services provided by Federal Bank through the Fi app"
	DedupeMinorCheckFailureTitle          = "Are you under the age of 18 years?"
	DedupeMinorCheckFailureMessage        = "Minors with Federal Bank accounts do not currently have access to the services provided by Federal Bank through the Fi app"
	DedupePartialKycFlow                  = "You already have min-KYC account with Federal Bank or its partners"
	DedupePartialKycFlowSubtitle          = "As soon as you complete your full-KYC procedure at Federal Bank — or one of the partners, like Jupiter — you can continue with Fi✅"
	DedupeMultipleCustomerIDsExistTitle   = "You have multiple customer IDs at Federal Bank"
	DedupeMultipleCustomerIDsExistMessage = "You can only have one customer ID. So, you currently don't have access to the services provided by Federal Bank through the Fi app. Please contact Federal Bank customer support for further help."
	DedupePhoneNumberMismatch             = "You already have an account with Federal Bank or its partners"
	DedupePhoneNumberMismatchSub          = "While signing up on Fi, please use the same phone number you've registered with on Federal or one of the partners, like Jupiter📱"
	DedupeDOBMismatch                     = "Looks like you have an existing Federal account with a different DOB. " +
		"Please update your Federal account or contact us if you want to try with different DOB."
	DedupePhoneNumberLinkedToMultipleAccounts = "Looks like your phone number is linked to multiple existing Federal Bank accounts — or one of the partners, like Jupiter. " +
		"Please contact the Federal Bank on 1800-425-1199 or 1800-420-1199 for assistance before you continue."
	DedupePhoneNumberLinkedToExistingAccount = "Looks like the phone number you are using is linked to another user's Federal Bank account — or one of the partners, like Jupiter. " +
		"Please contact the Federal Bank on 1800-425-1199 or 1800-420-1199 for assistance before you continue."
	NtbPhoneNumberLinkedToExistingAccount = "This number is linked to another Federal account. If user confirms that it is linked to his/her account only then might be the user's pan is not linked to the existing Federal account. The user needs to update the pan through Fednet for the existing account and then can continue on FI app."
	GenericDedupeCheckFailure             = "Looks like you already have a Federal Bank Account. " +
		"Please contact the Federal Bank on 1800-425-1199 or 1800-420-1199 for assistance."
	PhoneNumberMismatchTitle           = "Phone number already in use with Federal bank"
	DedupePartialProfileTitle          = "You already have a Savings Account with Federal Bank or its partners"
	DedupePartialProfileMsg            = "As soon as you update all your profile details at Federal Bank — or one of the partners, like Jupiter — you can continue with Fi ✅"
	DedupeBlockedUserWithDuesTitle     = "Have existing dues with Federal Bank?"
	DedupeBlockedUserWithDuesMsg       = "Clear your dues to get access to the services provided by Federal Bank through the Fi app"
	DedupeFailureImageURL              = "https://epifi-icons.pointz.in/onboarding/caveman.png"
	DedupeEmailMismatchTitle           = "Email already in use with Federal bank"
	DedupeEmailMismatchSubtitle        = "While signing up on Fi, please use the same email you've registered with on Federal or one of the partners, like Jupiter📱"
	DedupeEmailLinkedToExistingAccount = "Looks like the email address you are using is linked to another user's Federal Bank account — or one of the partners, like Jupiter. "
	DedupeKycNotValidTitle             = "You have an existing account with Federal for which KYC expired or due."
	DedupeKycNotValidSubTitle          = "If your existing KYC info is correct, SMS \"KYC Y\" to ********** from your registered mobile number. " +
		"<br><br>If your details like income, occupation, address have changed, please check the mail from Federal Bank with the subject \"Periodic Know Your Customer (KYC) Updation\" for more info." +
		"<br><br>Note: KYC updation takes 5-6 business hours! So, after you update KYC — consider waiting for a few hours before trying to log in."

	/*
		Liveness and KYC error messages
	*/
	KYCPermFail               = "Sorry! We couldn't verify your KYC details."
	EmptyAddressFailure       = "Your KYC record had insufficient address details. Please update your KYC and retry."
	CKYCConsentText           = "I allow Federal Bank to process these details for the purposes of completing KYC process and to download my CKYC records for eligibility checks."
	CKYCDefaultMsg            = "Sorry, there's an issue with your account opening."
	CKYCLFlag                 = "It seems that you already have Simplified Measure Account with another bank. RBI rules say that you can't open a new minimum account in that case. We are working on making it possible for you in a few days. We'll send you a notification as soon as we're ready."
	CKYCSFlag                 = "It seems that you already have Small Account with another bank. RBI rules say that you can't open a new minimum account in that case. We are working on making it possible for you in a few days. We'll send you a notification as soon as we're ready."
	CKYCOFlag                 = "It seems that you already have another Aadhaar-OTP based account with another bank. RBI rules say that you can't have more than one such account. We are working on making it possible for you in a few days. We'll send you a notification as soon as we're ready."
	MinorAgeFailureTitle      = "You’re a little early here ⏰"
	MinorAgeFailureSubtitle   = "Your KYC indicates that you’re not 18 yet. As per our policy, we can only provide you with a Federal savings account once you’re over 18."
	AgeDoesNotQualifyTitle    = "Uh oh! You don't meet our age criteria!"
	AgeDoesNotQualifySubtitle = "We have updated the age policy on the Fi app. Unfortunately, you cannot proceed right now. To retry, please return on your 21st birthday.<br><br>Existing users will not be affected by this change. Still want to open a Federal Bank savings account? Visit your nearest Federal Bank branch or check out their website for other channels"
	AgeDoesNotQualifyImageURL = "https://epifi-icons.pointz.in/onboarding/blocking_hurdle.png"

	/*
		KYC Name DOB Validation failure messages
	*/
	EKYCNameDOBValidationTitle    = "Your existing details with the Federal bank do not match with your KYC records."
	EKYCNameDOBValidationSubtitle = "Please contact your nearest branch to get the details updated as per your current KYC records before resuming on Fi. Please note that you may have an account directly with Federal Bank or via their fintech partners other than Fi."
	KYCDOBMismatchTitle           = "Uh oh! Date of birth match failed"
	KYCDOBMismatchSubtitle        = "Your date of birth on PAN card doesn't match with the one on Aadhaar. Please correct your details and try again."

	/*
		Risk Screening error message
	*/
	PinCodeNotAllowedIcon = "https://epifi-icons.pointz.in/risk_check/address_pin_code_blocked.png"

	GenericBlockingTitle    = "Unable to process account opening request"
	GenericBlockingSubtitle = "Your account cannot be opened at the moment. Thanks for your interest"
	GenericBlockingIcon     = "https://epifi-icons.pointz.in/onboarding/door_with_nails.png"

	CCRiskBlockingTitle            = "Sorry! Credit Card unavailable for you"
	CCRiskBlockingSubtitle         = "As we couldn't verify your details, we'll not be able to give you a Fi-Federal co-branded Credit Card."
	NonServiceablePinCodesTitle    = "We appreciate your interest"
	NonServiceablePinCodesSubtitle = "Unfortunately, we have stopped services in your current location. If you are travelling, retry signing up when you return home.<br><br>Existing users will not be affected by this change."
	NonServiceablePinCodesIcon     = "https://epifi-icons.pointz.in/onboarding/blocking_hurdle.png"

	NonIndianIPAddressNROnbPromptTitle    = "Looks like you're not in India"
	NonIndianIPAddressNROnbPromptSubtitle = "Are you looking to open an NRE/NRO account? Use your phone number from your country of residence instead of an Indian phone number. <br><br> If you want to open a regular savings account, please try again once you're back in India."
	NonIndianIPAddressNROnbPromptIcon     = "https://epifi-icons.pointz.in/onboarding/blocking_hurdle.png"

	UNNameCheckFailureTitle   = "Your profile doesn't meet the eligibility criteria of Federal Bank"
	UNNameCheckFailureMessage = "Per our partner bank guidelines, you will be unable to access services provided by Federal Bank through Fi"

	/*
		CTA texts
	*/
	CTACall     = "Call us"
	CTAChat     = "Chat with us"
	CTAFeedback = "Give feedback"

	CTAHeaderFeedback = "How was your experience?"
	CTAHeaderCX       = "Need further assistance?"

	CTAFAQIcon = "https://epifi-icons.pointz.in/onboarding/help_circle.png"

	DebitCardNameMismatchMaxRetries = "Sorry! We couldn’t verify your KYC details."

	// Liveness manual review messages
	LivenessManualReviewTitle    = "Verifying your video may take some time"
	LivenessManualReviewSubTitle = "We were unable to instantly verify the video that helps us ensure no one is impersonating you. We'll have to manually verify the video for you."
	LivenessManualReviewReason1  = "Your video had poor lighting"
	LivenessManualReviewReason2  = "We couldn't hear the OTP"
	LivenessManualReviewReason3  = "We couldn't see you clearly"
	LivenessManualReviewReason4  = "Other people were in the video"
	LivenessManualReviewFooter   = "We'll notify you in 1-3 business days when it's done 😇"

	// Liveness Failure messages
	LivenessFailureTitle    = "We cannot open an account for you"
	LivenessFailureSubTitle = "We were unable to verify your details."
	LivenessFailureReason   = "<b>This may be due to</b> <br><br> A mismatch between the details you shared & submitted documents <br> An issue with your verification video"
	DoorWithNailsImage      = "https://epifi-icons.pointz.in/onboarding/door_with_nails.png"

	// PANName mismatch manual review
	PanNameMismatchWaitingScreenImage     = "https://epifi-icons.pointz.in/kyc/pan_name_mismatch_waiting.png"
	PanNameMismatchWaitingScreenTitle     = "Your KYC details are under review"
	PanNameMismatchWaitingScreenSubtitle  = "We noticed a mismatch in your name between your PAN and other KYC details."
	PanNameMismatchWaitingScreenMessage   = "No worries, we’ll manually review this in 1-3 business days and notify you when it is done 😇"
	PanNameMismatchRejectionScreenTitle   = "KYC details mismatch"
	PanNameMismatchRejectionScreenMessage = "Due to a name mismatch between your PAN and other KYC details provided while signing up, you'll not be able to access the services provided by Federal Bank through the Fi app"
	PanNameMismatchRejectionScreenImage   = "https://epifi-icons.pointz.in/kyc/pan_name_mismatch_rejection.png"

	// PAN aadhar mismatch
	PanAadhaarMismatchTitle    = "Uh-oh, There was a mismatch in profile details"
	PanAadhaarMismatchSubtitle = "PAN shared by you isn't linked to the Aadhaar that you provided during eKYC."

	WAPreferenceMessage = "Send me important messages on WhatsApp"

	DeviceAlreadyRegisteredWithPan = "The PAN number you are trying to register with is already registered with us."

	// VKYC
	OutOfBizHoursIconUrl  = "https://epifi-icons.pointz.in/vkyc/outside_biz_hrs.png"
	OutOfBizHoursTitle    = "Uh oh! It’s outside business hours"
	OutOfBizHoursSubTitle = "Please come back during business hours to complete your KYC check with a banking RM. If you’d like, we can notify you when the RMs are back online 😇"
	NotifyMeCtaText       = "Notify Me"
	InReviewIconUrl       = "https://epifi-icons.pointz.in/vkyc/vkyc_review_card_ok.png"
	InReviewTitle         = "We’ve submitted your documents for review"
	InReviewSubTitle      = "Sit tight and we’ll notify you once document verification is complete in 3 business days ⏳ "
	RejectIconUrl         = "https://epifi-icons.pointz.in/vkyc/vkyc_auditor_rejected.png"
	RejectTitle           = "Uh-Oh! The bank’s auditor was unable to verify your KYC"
	RejectSubTitle        = "Sorry, as per regulations, you cannot open a Federal savings account right now."
	CallFailIconUrl       = "https://epifi-icons.pointz.in/vkyc/vkyc_call_fail.png"
	CallFailTitle         = "Your KYC document verification didn't pan out"
	CallFailSubTitle      = "No worries! Take some time to sort out your documents & retry submitting them.\n\nAs per bank regulations, you won’t be able to create a Federal savings account without completing this step."
	CallFailCTAText       = "Retry Video KYC Call"

	// vkyc landing page constants
	LandingPageTitle                = "<font color=#333333><b>Complete a video call to unlock top-tier features</b></font>"
	LandingPageDescription          = "It's a quick verification call & takes only 3 mins"
	LandingPageBackgroundColorHex   = "#ECEEF0"
	LandingPageBlockStarTitle       = "<font color=#333333><b>Unlock Lifetime Validity</b></font>"
	LandingPageBlockStarIcon        = "https://epifi-icons.pointz.in/vkyc/star.png"
	LandingPageBlockStarColorHex    = "#FFFFFF"
	LandingPageBlockDollarTitle     = "<font color=#333333><b>Unlimited balance, deposits, & transfers</b></font>"
	LandingPageBlockDollarIcon      = "https://epifi-icons.pointz.in/vkyc/dollarIcon.png"
	LandingPageBlockDollarColorHex  = "#FFFFFF"
	LandingPageBlockRocketTitle     = "<font color=#333333><b>Invest in commission free Mutual funds</b></font>"
	LandingPageBlockRocketIcon      = "https://epifi-icons.pointz.in/vkyc/rocketIcon.png"
	LandingPageBlockRocketColorHex  = "#FFFFFF"
	LandingPageBlockThunderTitle    = "<font color=#333333><b>Earn up to 10% interest with Jump</b></font>"
	LandingPageBlockThunderIcon     = "https://epifi-icons.pointz.in/vkyc/thunderIcon.png"
	LandingPageBlockThunderColorHex = "#FFFFFF"
	LandingPageCompleteCtaText      = "Continue"

	// Next to do screen (after debit card pin setup)
	NextToDoCtaText = "Continue"

	// Account setup error screen
	AccountSetupErrorTitle    = "Opening your account may take upto 3 days"
	AccountSetupErrorSubtitle = "It rarely happens — but due to unforeseen issues, our partner bank may take 1-3 days to open your account."
	AccountSetupErrorImage    = "https://epifi-icons.pointz.in/onboarding/baby_yoda.png"

	// T&C screen
	TnCTitle                     = "Understand our T&C"
	TnCSubtitle                  = "Written in a way humans can understand & relate to"
	TnCConsentIndianResidentText = "I agree to the above terms & confirm that I’m an Indian citizen, my country of tax residence is India and I’m not (nor am related to) a politically exposed person."
	TnCConsentPersonalDataText   = "I agree to share personal data with Fi and its partners, to be accessed, stored & used for providing services. "
	TnCConsentExperianText       = "I consent to Fi receiving & processing my credit information from Experian for 6 months. I also agree to <a style='color: #00B899; text-decoration: none;' href=\"https://fi.money/assets/pages/experian-tnc\">Experian TnC</a>"

	// T&C v2 screen
	TncV2Title                    = "User agreement"
	TncV2Subtitle                 = "To continue creating your profile,\nplease agree to the following"
	TncIndianResidentV2Title      = "Indian resident and tax payer"
	TncTermsAndConditionTitleV2   = "Terms and conditions"
	TncCreditInfoTitleV2          = "Credit information"
	TncTermsAndConditionImgV2     = "https://epifi-icons.s3.ap-south-1.amazonaws.com/onboarding/consent-chat.png"
	TncIndianResidentConsentV2    = "I confirm that I'm an Indian citizen; India is my country of tax residence, and I'm not (nor am I related to) a politically exposed person."
	TncTermsAndConditionConsentV2 = "I agree to <a style='color: #00B899; text-decoration: none;' href=\"%v\"> Fi T&C </a> and <a style='color: #00B899; text-decoration: none;' href=\"%v\"> Privacy Policy</a>. I also agree to share data with Fi and its partners to be processed for providing services."
	TncCreditInfoConsentV2        = "I consent to Fi receiving & processing my credit information from Experian for 6 months to check eligibility for Fi App and credit products. I also agree to <a style='color: #00B899; text-decoration: none;' href=\"https://fi.money/assets/pages/experian-tnc\">Experian TnC</a>"
	TncIndianResidentImgV2        = "https://epifi-icons.s3.ap-south-1.amazonaws.com/onboarding/consent-pan.png"
	TncCreditInfoImgV2            = "https://epifi-icons.s3.ap-south-1.amazonaws.com/onboarding/consent-credit.png"
	TncFiSecurityImgV2            = "https://epifi-icons.s3.ap-south-1.amazonaws.com/onboarding/consent-fisecurity.png"
	TncFiSecurityTitleV2          = "End-to-end encrypted"
	/*
		NRI T&C Screen
	*/
	ConsentText1 = "I understand that this NRE/NRO account opening is currently being tested as part of a pilot approved by the regulator and our partner bank. I consent to be part of the closed user group and provide necessary support and feedback (including undergoing additional KYC after account opening)."
	ConsentText2 = "I hereby accept and agree to the <a style='color: #00B899; text-decoration: none;' href=\"https://fi.money/tnc/fatca\">FATCA CRS Declaration and T&C</a>. I also confirm that I'm not resident of India and I'm not (nor am related to) a politically exposed person."
	ConsentText3 = "I agree to Fi Terms and also agree to share personal data to be accessed, stored & used for providing financial services."

	// Pan Aadhaar not linked
	PanAadhaarNotLinkedTitle    = "Uh-oh, There was a mismatch in profile details"
	PanAadhaarNotLinkedSubTitle = "PAN shared by you isn't linked to the Aadhaar that you provided during eKYC."

	// Connected Account in Screener
	PoweredByEpifiWealthIconURL = "https://epifi-icons.pointz.in/usstocks_images/epifi-wealth-powered.png"
	ClockIconURL                = "https://epifi-icons.pointz.in/onboarding/Clock.png"
	WarningIconURL              = "https://epifi-icons.pointz.in/onboarding/WarningError.png"
	CheckInProgressTitle        = "Verifying your bank account <br> may take up to 1 day"
	CheckInProgressDescription  = "We’re fetching your account data & <br> analysing it to see if Fi suits your needs<br><br><br><b>How do I access the Fi app faster?</b><br><br>You can explore other available verification methods on the previous screen. Choose one that you’re comfortable with"
	BgColorInProgress           = "#F4E7BF"
	BgColorFailed               = "#FAD0D0"

	// Credit Report in Screener
	CreditReportCheckInProgressTitle       = "Verifying your credit report <br> may take up to 1 day"
	CreditReportCheckInProgressDescription = "We’re fetching your credit report data & <br> analysing it to see if Fi suits your needs<br><br><br><b>How do I access the Fi app faster?</b><br><br>You can explore other available verification methods on the previous screen. Choose one that you’re comfortable with"

	EKYCDescription = "Our partner Federal Bank will verify your KYC with Aadhaar and open an account with a maximum <font color=#00B899>deposit limit</font> of ₹%v in a financial year. Fi does not store or use your Aadhaar details" +
		"<BR>You can always upgrade to a full-fledged account later"

	EKYCBottomSheetDesc = "Why is there a deposit limit?<BR>" +
		"As per partner bank's policies, Savings Account opened using Aadhaar-OTP based KYC cannot have deposits that exceed ₹%v in a financial year<BR><BR>" +
		"How do I remove the ₹%v limit?<BR>" +
		"You can do a 5-min Video KYC call with a Federal Bank RM on Fi app after your account is opened"

	EKYCConsentText          = "By proceeding, you consent to the above and agree that no other account has been or will be opened using OTP-based KYC with another Regulated Entity"
	CreditReportTncTextForCC = "I consent to Fi receiving & processing my credit information from Experian for 6 months. I also agree to <a style='color: #00B899; text-decoration: none;' href=\"https://fi.money/assets/pages/experian-tnc\">Experian TnC</a>"

	// Credit card constants
	CreditCardCampaignKey = "campaign"

	// update app constants
	UpdateAppTitle                    = "Update your Fi app to continue"
	UpdateAppSubtitle                 = "We've rolled out some new changes that are necessary to onboard with us. Please update your app"
	UpdateAppImageUrl                 = "https://epifi-icons.pointz.in/credit_card_images/ufo_ladder_image.png"
	InfoIconUrl                       = "https://epifi-icons.pointz.in/onboarding/info_icon.png"
	WarningUrl                        = "https://epifi-icons.s3.ap-south-1.amazonaws.com/onboarding/rekyc-warning-pink.png"
	TaxPayerTitle                     = `Taxpayer declaration <span style="color: #D65779;">*</span>`
	TaxPayerDescription               = "I confirm that I'm an Indian citizen; India is my country of tax residence."
	PepTitle                          = `PEP declaration <span style="color: #D65779;">*</span>`
	PepDescription                    = "I confirm that I'm not a politically exposed person or related to them"
	PepExplantory                     = "* A Politically Exposed Person (PEP) is someone performing important functions for the government, holding important positions in a political party, or is closely related to such an individual."
	AccessibilityTitle                = `Accessibility declaration <span style="color: #D65779;">*</span>`
	AccessibilityDescription          = "I am a differently abled person"
	AccessibilityInfoTitle            = "Accessibility declaration"
	AccessibilityInfoSubTitle         = "Differently abled individuals could indicate persons with any physical challenges, for example, visual or auditory impairments.\n\nUnfortunately, differently abled individuals cannot open a Federal Bank Savings Account through Fi. To continue opening a savings account in this case, it is recommended to visit the nearest Federal Bank branch."
	AccessibilityCtaHeader            = "* Currently, differently abled individuals cannot \nopen a Federal Bank Savings Account through Fi"
	ScholarshipTitle                  = "Scholarship declaration"
	ScholarshipDescription            = "I hereby confirm that this account will be used to receive any scholarship funds."
	ScholarshipInfoTitle              = "Scholarship declaration"
	ScholarshipInfoSubTitle           = "This declares that you will not be using this Savings Account to receive scholarship funds or to conduct any transaction related to your scholarships."
	DBTTitle                          = "Direct Benefit Transfer declaration"
	DBTDescription                    = "I confirm that this account will not be used for receipt of Direct Benefit Transfer"
	DBTInfoTitle                      = "Direct Benefit Transfer declaration"
	DBTInfoSubTitle                   = "Direct Benefit Transfer (DBT) is a system where the government directly deposits subsidies, pensions, or other benefits into a citizen’s bank account."
	MainCtaHeader                     = `<span style="color: #D65779;">*</span> indicates mandatory fields`
	ConfirmCardMailingAddressIcon     = "https://epifi-icons.pointz.in/onboarding/confirm_card_mailing_icon.png"
	ConfirmCardMailingTitle           = "Confirm the following details"
	ConfirmCardMailingSubTitle        = "This gets your savings account up & running "
	ConfirmCardMailingNameDescription = "This name will appear on your Debit Card. Ensure it matches with your KYC records."
	ConfirmCardMailingRadioTitle      = "Purpose for account opening"
)

var (
	CreditReportTncForCC = &dlPb.Consent{
		Text:    CreditReportTncTextForCC,
		TextV2:  commontypes.GetHtmlText(CreditReportTncTextForCC),
		Consent: consent.ConsentType_CREDIT_REPORT_TNC.String(),
	}
	CKYCConsent = &dlPb.Consent{
		Text:    CKYCConsentText,
		Consent: consent.ConsentType_cKYC.String(),
		TextV2:  commontypes.GetPlainStringText(CKYCConsentText),
	}
	FedStorageConsent = &dlPb.Consent{
		Text:    onboardingPkg.FiFederalTncText,
		TextV2:  commontypes.GetHtmlText(onboardingPkg.FiFederalTncText),
		Consent: consent.ConsentType_FED_STORAGE_POLICY.String(),
	}
)
