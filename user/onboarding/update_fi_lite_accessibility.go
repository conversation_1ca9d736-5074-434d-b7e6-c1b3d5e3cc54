package onboarding

import (
	"context"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"

	onbPb "github.com/epifi/gamma/api/user/onboarding"
)

func (s *Service) UpdateFiLiteAccessibility(ctx context.Context, req *onbPb.UpdateFiLiteAccessibilityRequest) (*onbPb.UpdateFiLiteAccessibilityResponse, error) {
	var (
		actId    = req.GetActorId()
		source   = req.GetSource()
		respFunc = func(status *rpc.Status) (*onbPb.UpdateFiLiteAccessibilityResponse, error) {
			return &onbPb.UpdateFiLiteAccessibilityResponse{
				Status: status,
			}, nil
		}
	)

	if actId == "" || source == onbPb.FiLiteSource_FI_LITE_SOURCE_UNSPECIFIED {
		return respFunc(rpc.StatusInternalWithDebugMsg("Mandatory parameters are missing from the request"))
	}

	onbDetails, err := s.onboardingDao.GetOnboardingDetailsByActor(ctx, actId)
	if err != nil {
		logger.Error(ctx, "failed to get onb details by actor id", zap.Error(err))
		return respFunc(rpc.StatusInternalWithDebugMsg(err.Error()))
	}

	onbDetails = s.populateFiLiteOnboardingDetails(ctx, onbDetails, req.GetIsEnabled(), req.GetSource(), timestampPb.New(s.Time.Now()))

	if errUpdate := s.onboardingDao.UpdateOnboardingDetailsByColumns(ctx, []onbPb.OnboardingDetailsFieldMask{
		onbPb.OnboardingDetailsFieldMask_FEATURE,
		onbPb.OnboardingDetailsFieldMask_FI_LITE_DETAILS,
	}, onbDetails); errUpdate != nil {
		logger.Error(ctx, "failed to update onb details", zap.Error(errUpdate))
		return respFunc(rpc.StatusInternalWithDebugMsg(errUpdate.Error()))
	}
	s.eventLogger.LogFiLiteUserConverted(ctx, onbDetails.GetActorId(), onbDetails.GetFiLiteDetails().GetFiLiteSource())
	return &onbPb.UpdateFiLiteAccessibilityResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (s *Service) populateFiLiteOnboardingDetails(ctx context.Context, onbDetails *onbPb.OnboardingDetails, isEnabled commontypes.BooleanEnum, source onbPb.FiLiteSource, timeNow *timestampPb.Timestamp) *onbPb.OnboardingDetails {
	onbDetails.Feature = s.getFeatureFromFiLiteSource(ctx, source)

	if onbDetails.GetFiLiteDetails() == nil {
		onbDetails.FiLiteDetails = &onbPb.FiLiteDetails{}
	}
	onbDetails.FiLiteDetails.IsEnabled = isEnabled
	onbDetails.FiLiteDetails.FiLiteSource = source
	if onbDetails.GetFiLiteDetails().GetAccessibilityEnabledAt() == nil {
		onbDetails.FiLiteDetails.AccessibilityEnabledAt = timeNow
	}
	return onbDetails
}

func (s *Service) getFeatureFromFiLiteSource(ctx context.Context, source onbPb.FiLiteSource) onbPb.Feature {
	var feature onbPb.Feature
	switch {
	case (source == onbPb.FiLiteSource_FI_LITE_SOURCE_PAN_DOB && s.dynConf.IsPanDOBDropOffToWealthAnalyserEnabled()) ||
		source == onbPb.FiLiteSource_FI_LITE_SOURCE_INTENT_SELECTION ||
		source == onbPb.FiLiteSource_FI_LITE_SOURCE_DROPPED_OFF_USER_TO_WEALTH_ANALYSER:
		feature = onbPb.Feature_FEATURE_WEALTH_ANALYSER
	case source == onbPb.FiLiteSource_FI_LITE_SOURCE_LOANS_ELIGIBILITY_CHECK:
		feature = onbPb.Feature_FEATURE_PL
	case source == onbPb.FiLiteSource_FI_LITE_SOURCE_SA_ONBOARDING_EXPIRED:
		feature = onbPb.Feature_FEATURE_FI_LITE
	case source == onbPb.FiLiteSource_FI_LITE_SOURCE_CREDIT_CARD_SDK_ONBOARDING:
		feature = onbPb.Feature_FEATURE_CC
	default:
		feature = onbPb.Feature_FEATURE_FI_LITE
	}
	logger.Info(ctx, "setting feature from drop_off", zap.String(logger.FEATURE, feature.String()), zap.String("filite_source", source.String()))
	return feature
}
