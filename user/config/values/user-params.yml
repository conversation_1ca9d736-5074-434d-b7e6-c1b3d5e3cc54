Flags:
  TrimDebugMessageFromStatus: true
  EnableUpdateUserComms: true
  EnableActiveProductsByPANCheck: false

QuestSdk:
  Disable: false

NewOnFi:
  Count: 4
  TimeWindow:
    Value: 2
    TimeUnit: "Hour"

FeatureReleaseConfig:
  FeatureConstraints:
    - FEATURE_ENABLE_NEW_CONSENT_SCREEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 99999
          MinIOSVersion: 99999
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 0
    - ONBOARDING_ADD_FUNDS_V2_2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100000
          MinIOSVersion: 100000
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # internal
    - FEATURE_ENABLE_ORDER_PHYSICAL_CARD_DURING_ONBOARDING:
        AppVersionConstraintConfig:
          MinAndroidVersion: 10000
          MinIOSVersion: 10000
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 0
    - FEATURE_ONBOARDING_PRE_ACCOUNT_CREATION_ADD_FUNDS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 10000
          MinIOSVersion: 100000
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 0
    - FEATURE_MF_IMPORT_WEALTH_BUILDER_ONBOARDING:
        AppVersionConstraintConfig:
          MinAndroidVersion: 422
          MinIOSVersion: 546
    - FEATURE_MIN_BALANCE_ACCOUNT_SCREEN_SDUI:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 37 # Pay_Experimental
    - FEATURE_SEND_SMS_DATA_WEALTH_BUILDER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 429
          MinIOSVersion: 100000

Onboarding:
  CreditReportExpirationDuration: 336h # 14 days
  ActorsWhitelistedForPreFunding:
    - "AC3w9TwrnCS5250207"
  ParentNamePrefillFromCKYCRolloutPercentage: 100
  UqudoCountryIdVerificationConfig:
    EnableTamperCheck: true
    ScreenDetectionThreshold: 30
    PrintDetectionThreshold: 30
    PhotoTamperingThreshold: 30
    IdExpiryThreshold: "2160h" # 3 months
    FaceMatchThreshold: 75
  NonResidentCrossValidationConfig:
    NameMatchScoreThreshold: 0.7
    FmPollingRetryConfig:
      Interval: 300 # default to using milliseconds
      MaxAttempts: 5
  ConfirmCardMailingAddress:
    EnableConfirmCardMailingAddressV2: false
    MaxKYCNameLenToSkipConfirmCardMailingAddr: 20
  ResetOnboardingJourney:
    OnboardingJourneyResetThresholdTime: '336h' # 14 days
  NextActionDecisionCacheConfig:
    IsCacheEnabled: true
    CacheTTL: "2160h" # 3 months
    NextActionDecisionCachePrefix: "usrAct:nxtActDesInf:"
  OnbDetailsCacheConfig:
    OnbDetailsTTLConfig:
      OnbDoneTTL: "6h"
      OnbInProgressTTL: "3h"
    OnbDetailsMinCacheConfig:
      OnbDoneMinTTL: "6h"
      OnbInProgressMinTTL: "5h"
      ActorToOnbTTL: "4h"
    IsCachingEnabled: true
  OrchestratorLockConfig:
    Timeout: 25s
    SleepWindow: 1000ms
  Flags:
    EnableEnsureCreditReportAvailabilityStage: true
    EnableHomeRedirectForSAOnbExpiredUsers: true
    IgnoreErrorsInGetDataForCrossValidationManualReview: false
    EnablePermissionStage:
      MinAndroidVersion: 426
      MinIOSVersion: 2714
      FallbackToEnableFeature: false
      DisableFeature: false
    UseNewLivenessFlow:
      MinAndroidVersion: 100000
      MinIOSVersion: 100000
      FallbackToEnableFeature: false
      DisableFeature: false
    BlockNrOnboarding: true
    AllowNRIDedupeUsers: true
    EnableGlobalIssuedPassportARNFlow: true
    EnableCkyc: true
    EnableGlobalIssuedPassportVerification:
      MinAndroidVersion: 418
      MinIOSVersion: 2624
      FallbackToEnableFeature: true
      DisableFeature: false
    EnableParentNamePrefillFromCKYC: true
    EnableInstalledAppsCheck: true
    EnableSMSParserDataVerificationStage: true
    EnableRiskCheckForNRUser: false
    EnableRiskScreeningForD2H: true
    SkipCountryIdVerification: true
    SkipLocationCheckForNROnboarding: false
    SkipPassportVerification: true
    EnableNonResidentOnboardingCrossValidation: true
    EnableGNOAOnError: true
    EnableUpdateProfileDetailsStage: true
    MarkCKYCSuccessWithoutCKYCDownload: true
    AddMoneyBalanceOptions:
      - Amount:
          CurrencyCode: "INR"
          Units: 2000
          Nanos: 0
        IsEnabled: true
      - Amount:
          CurrencyCode: "INR"
          Units: 2000
          Nanos: 0
        IsEnabled: true
      - Amount:
          CurrencyCode: "INR"
          Units: 2000
          Nanos: 0
        IsEnabled: true
      - Amount:
          CurrencyCode: "INR"
          Units: 2000
          Nanos: 0
        IsEnabled: true
      - Amount:
          CurrencyCode: "INR"
          Units: 2000
          Nanos: 0
        IsEnabled: true
      - Amount:
          CurrencyCode: "INR"
          Units: 2000
          Nanos: 0
        IsEnabled: true
      - Amount:
          CurrencyCode: "INR"
          Units: 2000
          Nanos: 0
        IsEnabled: true
      - Amount:
          CurrencyCode: "INR"
          Units: 2000
          Nanos: 0
        IsEnabled: true
      - Amount:
          CurrencyCode: "INR"
          Units: 2000
          Nanos: 0
        IsEnabled: true
      - Amount:
          CurrencyCode: "INR"
          Units: 2000
          Nanos: 0
        IsEnabled: true
    BlockCCUserForPANLinkage: false
    InhouseNamematchThreshold: 0.85
    InhouseNamematchFailureThreshold: 0.7
    AllowManualReviewUsers: true
    EnableAffluenceV2: true
    EnableSavingsIntroScreen:
      MinAndroidVersion: 0
      MinIOSVersion: 0
      FallbackToEnableFeature: true
      DisableFeature: false
    EnableSMSParserConsentScreen:
      MinAndroidVersion: 401
      MinIOSVersion: 99999
      FallbackToEnableFeature: true
      DisableFeature: false
      UnsupportedPlatforms: [ 2 ] # 2 is for iOS
    WealthAnalyserFeature:
      MinIOSVersion: 556
      MinAndroidVersion: 401
      FallbackToEnableFeature: true
      DisableFeature: false
    EnableContactPermissionInOnb:
      MinAndroidVersion: 406
      MinIOSVersion: 2595
      FallbackToEnableFeature: true
      DisableFeature: false
  ABFeatureReleaseConfig:
    FeatureConstraints:
      - REFERRAL_SCREEN_DURING_ONBOARDING_V1:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 99999
              MinIOSVersion: 99999
          Buckets:
            - ONE:
                Start: 0
                End: 99
      - ATT_IOS_PERMISSION_PROMPT:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 99999
              MinIOSVersion: 1 # enabling for all in iOS as it just sends a flag in screen-options
            StickyPercentageConstraintConfig:
              RolloutPercentage: 100
          Buckets:
            - ONE:
                Start: 0
                End: 99
      - AFFLUENT_USER_BONUS_TRANSITION_SCREEN:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 99999
              MinIOSVersion: 99999
          Buckets:
            - ONE:
                Start: 0
                End: 0
      - PRIORITIES_VKYC_OVER_ADD_FUNDS:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 99999
              MinIOSVersion: 99999
          Buckets:
            - ONE:
                Start: 0
                End: 0
      - VKYC_NEW_REVIEW_SCREEN:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 0
              MinIOSVersion: 0
          Buckets:
            - ONE:
                Start: 0
                End: 99
      - ONBOARDING_ADD_FUNDS_V2:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 99999
              MinIOSVersion: 99999
          Buckets:
            - ONE:
                Start: 0
                End: 0
      - AFFLUENT_USER_BONUS_TRANSITION_SCREEN_NON_REFEREE:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 99999
              MinIOSVersion: 99999
          Buckets:
            - ONE:
                Start: 0
                End: 0
      - PHONE_NUMBER_AS_REFERRAL_CODE:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 99999
              MinIOSVersion: 99999
          Buckets:
            - ONE:
                Start: 0
                End: 0
      - SCREENER_CHOICE_PAGE:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 0
              MinIOSVersion: 0
          Buckets:
            - ONE:
                Start: 0
                End: 99
  BlockOnboardingDueToUnlinkedPANAndAadhaar: true
  AddFundsConfig:
    ShowSkipCtaViaQuest: false
    SkipDurationViaQuest: "168h" # 7d
    ShowV2Page: true
    V2PageMinorVersion: 0
  EnableTriggerNROAccountCreation:
    MinAndroidVersion: 99999
    MinIOSVersion: 99999
    FallbackToEnableFeature: true
    DisableFeature: true
  PrefillParentNameFromPassportOCR:
    MinAndroidVersion: 0
    MinIOSVersion: 0
    FallbackToEnableFeature: true
    DisableFeature: false
  PassportVerificationConfig:
    IgnoreVendorError: false
    PassportExpiryThreshold: "2160h" # 3 months
    DetailsConfirmationFeatureConfig:
      MinAndroidVersion: 382
      MinIOSVersion: 541
      FallbackToEnableFeature: false
      DisableFeature: false
  StuckUserNudges:
    DEBIT_CARD_PIN_SETUP:
      - StuckDuration: "60m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Your debit card is waiting for you"
            Body: "Set a secret 4-digit PIN and start using your Visa Platinum Debit Card for online payments!🔑"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"

  CCFiliteStuckUserNudges:
    VKYC:
      - StuckDuration: "12h"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "📞 Complete your KYC verification call"
            Body: "Tap to complete the video KYC call to get your credit card. \n💡Keep your original PAN ready."
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "12h"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "🚨 90% done! 1 more step left..."
            Body: " Just finish a 3-minute video KYC call to verify yourself. Tap to start the call now! "
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"

      - StuckDuration: "12h"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Ready for your video call? 🤓 🤙"
            Body: "Complete your account creation by getting on a quick 3 min video KYC call with us."
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"

  KYCNameUpdateNewSubTitleMinAndroid: 79
  DurationToSkipAddFundsForAffluenceClasses:
    - ENTRY_1:
        IsEnabled: true
        AffluenceClass: 1
        Duration: 24h
    - ENTRY_2:
        IsEnabled: true
        AffluenceClass: 2
        Duration: 72h
    - ENTRY_3:
        IsEnabled: true
        AffluenceClass: 3
        Duration: 168h
  WebUrlsForSalaryB2BFlows:
    - FLOW_1:
        IsEnabled: true
        Url: "https://fi.money/signup"
  SimulateBalanceCheckFailureForAddFunds: false
  DedupeAPICacheTimeout: "2m"
  FiniteCodeFromAttributionParamsKey: "deep_link_sub5"
  TotalAmountViaOrderAddFunds: true
  MinAndroidVersionForManualBalanceRefreshOnbAddFunds: 99999
  MinIosVersionForManualBalanceRefreshOnbAddFunds: 99999
  ReferralOfferCodesDuringOnboarding:
    - CODE_1:
        IsEnabled: false
        BeforeAppliedTitle: "<font color='#313234'>FI200: </font><font color='#5d7d4c'>Get up to ₹200</font>"
        AfterAppliedTitle: "<font color='#313234'>\"FI200\" applied</font>"
        BeforeAppliedDesc: "When you add money to your account"
        AfterAppliedDesc: "Get up to ₹200 when you add money"
        BeforeAppliedIconUrl: "https://epifi-icons.pointz.in/referrals/onboarding-tag.png"
        AfterAppliedIconUrl: "https://epifi-icons.pointz.in/referrals/onboarding-success-check.png"
        Code: "FI200"
        UnderlyingFiniteCode: ""
    - CODE_2:
        IsEnabled: false
        BeforeAppliedTitle: "<font color='#313234'>FI200: </font><font color='#5d7d4c'>Get up to ₹200</font>"
        AfterAppliedTitle: "<font color='#313234'>\"FI200\" applied</font>"
        BeforeAppliedDesc: "When you add money to your account"
        AfterAppliedDesc: "Get up to ₹200 when you add money"
        BeforeAppliedIconUrl: "https://epifi-icons.pointz.in/referrals/onboarding-tag.png"
        AfterAppliedIconUrl: "https://epifi-icons.pointz.in/referrals/onboarding-success-check.png"
        Code: "FI200"
        UnderlyingFiniteCode: ""
  ReferralOfferCodesABReleaseConfig:
    FeatureConstraints:
      - FEATURE_UNSPECIFIED:
          ConstraintConfig:
            StickyPercentageConstraintConfig:
              RolloutPercentage: 0
          Buckets:
            - ONE:
                Start: 0
                End: 99
  ReferralOfferWidgetsDuringOnboarding:
    - FI200:
        IsEnabled: false
        LeftIcon: "https://epifi-icons.pointz.in/referrals/referrals_v1/onboarding/offer_widget/onb_offer_widget_ref.png"
        OfferText: "<font color ='#313234'>Offer applied! Get flat </font><font color='#5D7D4C'>₹200 </font><font color='#313234'>when you sign up</font>"
        BgColor: "#E7F7DE"
    - REGULAR:
        IsEnabled: false
        LeftIcon: "https://epifi-icons.pointz.in/referrals/referrals_v1/onboarding/offer_widget/onb_offer_widget_ref.png"
        OfferText: "<font color ='#313234'>Offer applied! Get rewards when you spend</font>"
        BgColor: "#E7F7DE"
  OfferWidgetOnbScreensViaFiniteCodeABReleaseConfig:
    FeatureConstraints:
      - FEATURE_UNSPECIFIED:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 99999
              MinIOSVersion: 99999
          Buckets:
            - ONE:
                Start: 0
                End: 99
  DurationToSkipAddMoney: 2400h
  EKYCCertUpgradeFeatureConfig:
    MinIOSVersion: 1
    MinAndroidVersion: 1
    FallbackToEnableFeature: true
  EditEmploymentInScreener:
    MinIOSVersion: 1
    MinAndroidVersion: 1
    FallbackToEnableFeature: false
    DisableFeature: true
  LivenessSummaryExpiryDuration: "720h" #30 days
  FiLiteRolloutPercentage: 1000
  IsPanDOBDropOffToWealthAnalyserEnabled: true
  DirectToFiLite:
    Enable: false
    Variant: ""
  OrderPhysicalDebitCardConfig:
    EnableViaQuest: true
    FreePhysicalDCRefereeRewardConfig:
      IsEnabled: true
      ActiveFrom: "2025-02-03T00:00:00+05:30"
      ActiveTill: "2030-12-31T23:59:59+05:30"
      QuestExpVariablePath: "inappreferral/UserApplicableReferralConstructVariant"
      QuestExpVariableValue: "VARIANT_1"
  PanValidateV2FeatureConfig:
    MinIOSVersion: 10
    MinAndroidVersion: 10
    FallbackToEnableFeature: true
    DisableFeature: false
  PanValidateV3FeatureConfig:
    MinIOSVersion: 30
    MinAndroidVersion: 30
    FallbackToEnableFeature: true
    DisableFeature: false
  ShowNewConsentInPanValidateV2:
    MinIOSVersion: 10000
    MinAndroidVersion: 10000
    FallbackToEnableFeature: false
    DisableFeature: true
  AddFundsRestrictionMap:
    ANDROID:
      IsRestricted: false
    IOS:
      IsRestricted: false

  AppScreeningConfig:
    ForceScreenerCheckTTL: "2m"
    UANCheckConfig:
      CreditScoreThreshold: 650
    EnableChatbotInChoiceScreen: true
    EnableFiLiteEntryPointInChoiceScreen: true

  AnnualSalaryUIView2:
    Hint: "Enter Annual Income"
    Title: "Enter Annual Income"
    MaxAmount: ********
    IntervalAmount: 10000

  IncomeDiscrepancyView:
    WarningText: "<font color=#A73F4B>The above details are not in usual range. Please update income or check the box below </font>"
    ConsentText: "<font color=#646464>These details are correct already. I’ll send <b>Proof of Income</b> to <EMAIL> from my Fi-registered email within 30 days. Accepted proofs include ITR, 6 mo Payslip, 6 mo Bank Statement, or Form 16</font>"

  MinKycMandatoryAddFundConfig:
    IsEnabled: true
    MinimumAmount:
      CurrencyCode: "INR"
      Units: 5000
      Nanos: 0

  RiskScreeningExpiry: 360h  # 15 * 24 hrs
  PhysicalCardChargesMailingAddressScreenParams:
    CurrentAddrCheckBoxText: "The address on my Aadhaar is my current address. Use it for all future communication purposes."
    NonAadharAddrCheckBoxText: "My Aadhaar & current address do not match. Please consider the above address as my current address and use the same for all future communication purposes"
    Title: "Confirm the name on your virtual debit card"
    TitleFontColor: "#333333"
    Subtitle: "This name will appear on your card"
    SubtitleFontColor: "#8D8D8D"
    PlaceholderForName: "NAME TO PRINT ON YOUR CARD"
    PlaceholderForAddress: "WHERE SHOULD WE SEND YOUR CARD"
    CtaText: "Next"
    ImageUrl: "https://epifi-icons.pointz.in/feature_benefits/sa/debit_card.png"
    CheckboxTextColor: "#333333"
    PlaceholderColor: "#A4A4A4"
    ContentColor: "#333333"
    DividerColor: "#ECEEF0"
    EditIconColor: "#646464"
    CardColor: "#F7F9FA"
    BackgroundColor: "#FFFFFF"

  SkipAddFundsUserGroups:
    - 22 #BRAND_STREET_ORG
    - 23 #AHMEDABAD_ROADLINES_ORG
    - 24 #SAAR_EDUCATION_ORG
    - 26 #SIMPLIFY_EDTECH_ORG
    - 29 #ALLROUND_FACILITIES

  AffluenceClassesEligibleForBonusTransitionScreen:
    - "AFFLUENCE_CLASS_CLASS_1": true
    - "AFFLUENCE_CLASS_CLASS_2": true
    - "AFFLUENCE_CLASS_CLASS_3": true
    - "AFFLUENCE_CLASS_CLASS_4": false
    - "AFFLUENCE_CLASS_CLASS_5": false
  SecureUsageGuidelineVersion: 1
  SecureUsageGuidelineConsentInterval: "2160h" # 3 months
  WalkthroughScreenConfig:
    FeatureConfig:
      MinIOSVersion: 589
      MinAndroidVersion: 425
      FallbackToEnableFeature: false
      DisableFeature: false
  SoftIntentSelectionConfig:
    PreOnboardingCompletionSoftIntentScreenConfig:
      Enabled: true
    PostOnboardingSoftIntentScreenConfig:
      Enabled: false
      WaitDuration: 73h
      MaxWaitDuration: 72h
    SoftIntentCollectionScreenFeatureConfig:
      MinIOSVersion: 589
      MinAndroidVersion: 425
      FallbackToEnableFeature: false
      DisableFeature: false
    SoftIntentCollectionScreenPercentageRollout: 100
    AllowedSoftIntents:
      ONBOARDING_SOFT_INTENT_CASHBACK_ON_UPI:
      ONBOARDING_SOFT_INTENT_EXPENSE_TRACKER:
      ONBOARDING_SOFT_INTENT_ZERO_FOREX_FEES:
      ONBOARDING_SOFT_INTENT_INTERNATIONAL_DEBIT_CARD:
      ONBOARDING_SOFT_INTENT_INVESTMENT_TRACKER:
      ONBOARDING_SOFT_INTENT_INVESTMENTS:
      ONBOARDING_SOFT_INTENT_LOANS_UP_TO_5L:
      ONBOARDING_SOFT_INTENT_LOANS_AGAINST_MUTUAL_FUNDS:
  IntentSelectionConfigV2:
    EnableDefaultIntentSelection: false
    IntentCollectionScreenFeatureConfig:
      MinIOSVersion: 250
      MinAndroidVersion: 250
      FallbackToEnableFeature: false
      DisableFeature: false
    PLIntentAgencies: ["netcore", "valueleaf","affinityveve","3dot14","adsfidelitin965"]
    FiniteCodeToAcquisitionIntentMap:
      "WEALTHGROW": 6 # ACQUISITION_INTENT_WEALTH_ANALYSER
    IntentCollectionScreenPercentageRollout: 100
    IntentConfigMap:
      ONBOARDING_INTENT_FEDERAL_SAVINGS_ACCOUNT:
        FeatureConfig:
          MinIOSVersion: 0
          MinAndroidVersion: 0
          FallbackToEnableFeature: false
          DisableFeature: false
        RolloutPercentage: 100
      ONBOARDING_INTENT_PERSONAL_LOANS:
        FeatureConfig:
          MinIOSVersion: 0
          MinAndroidVersion: 0
          FallbackToEnableFeature: false
          DisableFeature: false
        RolloutPercentage: 100
      ONBOARDING_INTENT_CREDIT_CARD:
        FeatureConfig:
          MinIOSVersion: 0
          MinAndroidVersion: 0
          FallbackToEnableFeature: false
          DisableFeature: false
        RolloutPercentage: 100
      ONBOARDING_INTENT_FI_LITE:
        FeatureConfig:
          MinIOSVersion: 0
          MinAndroidVersion: 0
          FallbackToEnableFeature: false
          DisableFeature: false
        RolloutPercentage: 100
      ONBOARDING_INTENT_NET_WORTH:
        FeatureConfig:
          MinIOSVersion: 0
          MinAndroidVersion: 0
          FallbackToEnableFeature: false
          DisableFeature: true
        RolloutPercentage: 100
      ONBOARDING_INTENT_WEALTH_ANALYSER:
        FeatureConfig:
          MinIOSVersion: 556
          MinAndroidVersion: 401
          FallbackToEnableFeature: false
          DisableFeature: false
        RolloutPercentage: 100
      ONBOARDING_INTENT_DEBIT_CARD:
        FeatureConfig:
          MinIOSVersion: 100
          MinAndroidVersion: 100
          FallbackToEnableFeature: false
          DisableFeature: false
        RolloutPercentage: 100

# Time should be in HH:MM 24-hour format
# It is quick fix for CSIS time and will be removed when CSIS service is up.
CSIS:
  IsCsisEnable: false
  StartTime: "01:30"
  EndTime: "03:30"

# This is the number of hours in 173 days (1 week less than 6 months) 173 * 24
ExperianDataStorageLimitInHrs: 4152

EpifiDb:
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

VKYC:
  EnablePassportFaceImageCheckForNRExpiry: true
  NRKYCExpiryForVKYC: "72h" # 3 days
  InstructionPageSkipOptionTime: 0 # in seconds
  LandingPageSkipOptionTime: 0 # in seconds
  EnableDemandManagement: false
  ShowAuditorAcceptedTileTime: "8h"
  EnableVKYCFlowV2:
    MinAndroidVersion: 442
    MinIOSVersion: 0
    FallbackToEnableFeature: false
    DisableFeature: false

CreditReportConfig:
  CreditReportPresenceEnabled: false
  DownloadWaitConfigForWealthBuilder:
    MaxAttemptsForCheckingDownloadStatus: 6
    SleepDurationBetweenEachAttempt: "500ms"

UserCacheConfig:
  IsCachingEnabled: true
  UserIdPrefix: "user_id_"
  CacheTTl: "1h"

MinimalUserCacheConfig:
  IsCachingEnabled: true
  CacheTTl: "720h" # 30 days

H3RankingFile: "data/h3ranking.csv"

RecordHashedContactLockTimeout: 30s
DedupeCacheExpiry: 60m
AccessRevokeCooldownDuration:
  ACCESS_REVOKE_REASON_COOLDOWN_UPI_PIN_RETRIES_EXCEEDED_POST_AFU: "5m"

UserDevicePropertiesCacheConfig:
  IsCacheEnabled: true
  CacheTTL: "15m"

