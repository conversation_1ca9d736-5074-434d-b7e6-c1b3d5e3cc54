package model

import (
	"time"

	gormv2 "gorm.io/gorm"

	piPb "github.com/epifi/gamma/api/paymentinstrument"
)

// PaymentInstrument (PI) is any instrument from which money can be credited to or debited from.
// So a PI can be an account, CC, DC, UPI vpa etc.
type PaymentInstrument struct {
	Id string

	// Represents type of PI
	Type piPb.PaymentInstrumentType

	// Holds the actual PI
	// e.g. account, CC, DC, UPI
	Identifier *Identifier

	// Official name belonging to this PI
	// This will always be empty in case of instruments linked to epifi user accounts
	// We’ll have to look for a name in whichever entity this PI belonged to. e.g. Account, Card etc.
	//
	// For non-epifi user accounts, this will be empty to begin with and can be fetched as required.
	// e.g. by depositing one rupee into account
	VerifiedName string

	// Different states of PI
	// This can be used for fraud/risk analysis. Default value will be `CREATED`.
	// This could also be used for UPI. Exact detailing TBD.
	State piPb.PaymentInstrumentState

	// Represents a map of capability (converted to string) of PI
	Capabilities *capabilities

	// Signifies PI issuer classification
	// Here, INTERNAL refers to the one's created by epiFi
	// and EXTERNAL refers to the one's those doesn't belong to epiFi
	IssuerClassification piPb.PaymentInstrumentIssuer

	// PI ownership - values denote who owns this PI
	// EPIFI_TECH = PI is in use of fi_user
	// EPIFI_WEALTH = PI is created and in use from aa service
	Ownership piPb.Ownership

	// timestamp at which the vpa was last verified at
	LastVerifiedAt *time.Time

	// Standard timestamp fields
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt gormv2.DeletedAt
}
