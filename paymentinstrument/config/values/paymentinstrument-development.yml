Application:
  Environment: "development"
  Name: "paymentinstrument"

Aws:
  Region: "ap-south-1"

RedisOptions:
  Options:
    Addr: "localhost:6379"
    Password: "" ## empty string for no password
    DB: 3

Server:
  Ports:
    GrpcPort: 8087
    GrpcSecurePort: 9514
    HttpPort: 9882
    HttpPProfPort: 9990

PaymentInstrumentDb:
  Host: "localhost"
  Port: 5432
  Name: "payment_instrument"
  DbType: "PGDB"
  SSLMode: "disable"
  AppName: "payment_instrument"
  SecretName: "{\"username\": \"root\", \"password\": \"\"}"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true


PiEventPublisher:
  TopicName: "pi-topic"

Flags:
  TrimDebugMessageFromStatus: false

AaTxnPurgePublisher:
  QueueName: "aa-txn-purge-queue"

AaAccountPiPurgeSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "aa-account-pi-purge-queue"
  # Exponential backoff till 2 min
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 7
      TimeUnit: "Second"

PiPurgeSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "pi-purge-queue"
  # Exponential backoff till 2 min
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 7
      TimeUnit: "Second"


AccountPiCacheConfig:
  IsCachingEnabled: false
  CacheTTl: "2m"

PiCacheConfig :
  IsCachingEnabled : false
  CacheTTl         : "2m"

