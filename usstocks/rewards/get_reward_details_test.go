package rewards_test

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"github.com/google/go-cmp/cmp/cmpopts"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/rpc"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	ussRewPb "github.com/epifi/gamma/api/usstocks/rewards"
)

func TestService_GetRewardDetails(t *testing.T) {
	type args struct {
		ctx context.Context
		req *ussRewPb.GetRewardDetailsRequest
	}
	tests := []struct {
		name           string
		args           *args
		setupMockCalls func(m *mockDependencies, a *args)
		want           *ussRewPb.GetRewardDetailsResponse
		wantErr        bool
	}{
		{
			name: "if user is eligible for reward",
			args: &args{
				ctx: context.Background(),
				req: &ussRewPb.GetRewardDetailsRequest{ActorId: "actor-id"},
			},
			setupMockCalls: func(m *mockDependencies, a *args) {
				m.rewardClient.EXPECT().GetRewardsByActorId(gomock.Any(), &rewardsPb.RewardsByActorIdRequest{
					ActorId: a.req.GetActorId(),
					Filter: &rewardsPb.RewardsByActorIdRequest_Filter{
						RewardOfferType: rewardsPb.RewardOfferType_USSTOCKS_ACTIVATION_REWARD_OFFER,
					},
					PageContext: &rpc.PageContextRequest{
						PageSize: 1,
					},
				}).Return(&rewardsPb.RewardsResponse{
					Status: rpc.StatusOk(),
					Rewards: []*rewardsPb.Reward{
						{
							Id:     "reward-id",
							Status: rewardsPb.RewardStatus_CREATED,
							RewardOptions: &rewardsPb.RewardOptions{
								Options: []*rewardsPb.RewardOption{
									{
										Option: &rewardsPb.RewardOption_UsstockReward{
											UsstockReward: &rewardsPb.USStockReward{
												Amount: &moneyPb.Money{
													CurrencyCode: moneyPkg.RupeeCurrencyCode,
													Units:        100,
												},
											}},
									},
								},
							},
						},
					},
				}, nil)
			},
			want: &ussRewPb.GetRewardDetailsResponse{
				Status: rpc.StatusOk(),
				RewardDetails: &ussRewPb.RewardDetails{
					RewardId: "reward-id",
					Status:   ussRewPb.RewardStatus_REWARD_STATUS_TO_BE_CLAIM,
				},
				DisplayDetails: &ussRewPb.StockRewardDisplayDetails{
					Options: []*ussRewPb.StockRewardOptionDetails{
						{StockName: conf.USStocksRewardDetails().Option1StockName()},
						{StockName: conf.USStocksRewardDetails().Option2StockName()},
					},
					Amount: &moneyPb.Money{
						CurrencyCode: moneyPkg.RupeeCurrencyCode,
						Units:        100,
					},
					BgLottieUrl: conf.USStocksRewardDetails().BgLottieUrl(),
				},
			},
			wantErr: false,
		},
		{
			name: "if reward is in processing state in both rewards and uss systems",
			args: &args{
				ctx: context.Background(),
				req: &ussRewPb.GetRewardDetailsRequest{ActorId: "actor-id"},
			},
			setupMockCalls: func(m *mockDependencies, a *args) {
				m.rewardClient.EXPECT().GetRewardsByActorId(gomock.Any(), &rewardsPb.RewardsByActorIdRequest{
					ActorId: a.req.GetActorId(),
					Filter: &rewardsPb.RewardsByActorIdRequest_Filter{
						RewardOfferType: rewardsPb.RewardOfferType_USSTOCKS_ACTIVATION_REWARD_OFFER,
					},
					PageContext: &rpc.PageContextRequest{
						PageSize: 1,
					},
				}).Return(&rewardsPb.RewardsResponse{
					Status: rpc.StatusOk(),
					Rewards: []*rewardsPb.Reward{
						{
							Id:     "reward-id",
							Status: rewardsPb.RewardStatus_PROCESSING_PENDING,
							RewardOptions: &rewardsPb.RewardOptions{
								Options: []*rewardsPb.RewardOption{
									{
										Option: &rewardsPb.RewardOption_UsstockReward{
											UsstockReward: &rewardsPb.USStockReward{
												Amount: &moneyPb.Money{
													CurrencyCode: moneyPkg.RupeeCurrencyCode,
													Units:        100,
												},
											}},
									},
								},
							},
						},
					},
				}, nil)
				m.rewardRequestDao.EXPECT().GetByActorAndRewardId(gomock.Any(), a.req.GetActorId(), "reward-id").Return(&ussRewPb.RewardRequest{
					ActorId: a.req.GetActorId(),
					Status:  ussRewPb.RewardRequestProcessingStatus_RRPS_ORDERS_CREATED,
					RewardFulfillmentDetails: &ussRewPb.RewardFulfillmentDetails{
						WalletOrderId:   "wallet-order-id",
						StockBuyOrderId: "stock-buy-order-id",
					},
					RewardId: "reward-id",
				}, nil)
			},
			want: &ussRewPb.GetRewardDetailsResponse{
				Status: rpc.StatusOk(),
				RewardDetails: &ussRewPb.RewardDetails{
					RewardId: "reward-id",
					Status:   ussRewPb.RewardStatus_REWARD_STATUS_PROCESSING,
					RewardRequest: &ussRewPb.RewardRequest{
						ActorId: "actor-id",
						Status:  ussRewPb.RewardRequestProcessingStatus_RRPS_ORDERS_CREATED,
						RewardFulfillmentDetails: &ussRewPb.RewardFulfillmentDetails{
							WalletOrderId:   "wallet-order-id",
							StockBuyOrderId: "stock-buy-order-id",
						},
						RewardId: "reward-id",
					},
				},
				DisplayDetails: &ussRewPb.StockRewardDisplayDetails{
					Options: []*ussRewPb.StockRewardOptionDetails{
						{StockName: conf.USStocksRewardDetails().Option1StockName()},
						{StockName: conf.USStocksRewardDetails().Option2StockName()},
					},
					Amount: &moneyPb.Money{
						CurrencyCode: moneyPkg.RupeeCurrencyCode,
						Units:        100,
					},
					BgLottieUrl: conf.USStocksRewardDetails().BgLottieUrl(),
				},
			},
			wantErr: false,
		},
		{
			name: "if reward is in processing state in rewards and but not in uss systems",
			args: &args{
				ctx: context.Background(),
				req: &ussRewPb.GetRewardDetailsRequest{ActorId: "actor-id"},
			},
			setupMockCalls: func(m *mockDependencies, a *args) {
				m.rewardClient.EXPECT().GetRewardsByActorId(gomock.Any(), &rewardsPb.RewardsByActorIdRequest{
					ActorId: a.req.GetActorId(),
					Filter: &rewardsPb.RewardsByActorIdRequest_Filter{
						RewardOfferType: rewardsPb.RewardOfferType_USSTOCKS_ACTIVATION_REWARD_OFFER,
					},
					PageContext: &rpc.PageContextRequest{
						PageSize: 1,
					},
				}).Return(&rewardsPb.RewardsResponse{
					Status: rpc.StatusOk(),
					Rewards: []*rewardsPb.Reward{
						{
							Id:     "reward-id",
							Status: rewardsPb.RewardStatus_PROCESSING_PENDING,
							RewardOptions: &rewardsPb.RewardOptions{
								Options: []*rewardsPb.RewardOption{
									{
										Option: &rewardsPb.RewardOption_UsstockReward{
											UsstockReward: &rewardsPb.USStockReward{
												Amount: &moneyPb.Money{
													CurrencyCode: moneyPkg.RupeeCurrencyCode,
													Units:        100,
												},
											}},
									},
								},
							},
						},
					},
				}, nil)
				m.rewardRequestDao.EXPECT().GetByActorAndRewardId(gomock.Any(), a.req.GetActorId(), "reward-id").Return(&ussRewPb.RewardRequest{
					ActorId:  a.req.GetActorId(),
					Status:   ussRewPb.RewardRequestProcessingStatus_RRPS_INITIATED,
					RewardId: "reward-id",
				}, nil)
			},
			want: &ussRewPb.GetRewardDetailsResponse{
				Status: rpc.StatusOk(),
				RewardDetails: &ussRewPb.RewardDetails{
					RewardId: "reward-id",
					Status:   ussRewPb.RewardStatus_REWARD_STATUS_TO_BE_CLAIM,
					RewardRequest: &ussRewPb.RewardRequest{
						ActorId:  "actor-id",
						Status:   ussRewPb.RewardRequestProcessingStatus_RRPS_INITIATED,
						RewardId: "reward-id",
					},
				},
				DisplayDetails: &ussRewPb.StockRewardDisplayDetails{
					Options: []*ussRewPb.StockRewardOptionDetails{
						{StockName: conf.USStocksRewardDetails().Option1StockName()},
						{StockName: conf.USStocksRewardDetails().Option2StockName()},
					},
					Amount: &moneyPb.Money{
						CurrencyCode: moneyPkg.RupeeCurrencyCode,
						Units:        100,
					},
					BgLottieUrl: conf.USStocksRewardDetails().BgLottieUrl(),
				},
			},
			wantErr: false,
		},
		{
			name: "if error while fetch reward from reward system",
			args: &args{
				ctx: context.Background(),
				req: &ussRewPb.GetRewardDetailsRequest{ActorId: "actor-id"},
			},
			setupMockCalls: func(m *mockDependencies, a *args) {
				m.rewardClient.EXPECT().GetRewardsByActorId(gomock.Any(), &rewardsPb.RewardsByActorIdRequest{
					ActorId: a.req.GetActorId(),
					Filter: &rewardsPb.RewardsByActorIdRequest_Filter{
						RewardOfferType: rewardsPb.RewardOfferType_USSTOCKS_ACTIVATION_REWARD_OFFER,
					},
					PageContext: &rpc.PageContextRequest{
						PageSize: 1,
					},
				}).Return(&rewardsPb.RewardsResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want: &ussRewPb.GetRewardDetailsResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "if user is ineligible for reward",
			args: &args{
				ctx: context.Background(),
				req: &ussRewPb.GetRewardDetailsRequest{ActorId: "actor-id"},
			},
			setupMockCalls: func(m *mockDependencies, a *args) {
				m.rewardClient.EXPECT().GetRewardsByActorId(gomock.Any(), &rewardsPb.RewardsByActorIdRequest{
					ActorId: a.req.GetActorId(),
					Filter: &rewardsPb.RewardsByActorIdRequest_Filter{
						RewardOfferType: rewardsPb.RewardOfferType_USSTOCKS_ACTIVATION_REWARD_OFFER,
					},
					PageContext: &rpc.PageContextRequest{
						PageSize: 1,
					},
				}).Return(&rewardsPb.RewardsResponse{
					Status:  rpc.StatusOk(),
					Rewards: []*rewardsPb.Reward{},
				}, nil)
			},
			want: &ussRewPb.GetRewardDetailsResponse{
				Status: rpc.StatusOk(),
				RewardDetails: &ussRewPb.RewardDetails{
					Status: ussRewPb.RewardStatus_REWARD_STATUS_INELIGIBLE_FOR_REWARD,
				},
			},
			wantErr: false,
		},
		{
			name: "if reward is expired",
			args: &args{
				ctx: context.Background(),
				req: &ussRewPb.GetRewardDetailsRequest{ActorId: "actor-id"},
			},
			setupMockCalls: func(m *mockDependencies, a *args) {
				m.rewardClient.EXPECT().GetRewardsByActorId(gomock.Any(), &rewardsPb.RewardsByActorIdRequest{
					ActorId: a.req.GetActorId(),
					Filter: &rewardsPb.RewardsByActorIdRequest_Filter{
						RewardOfferType: rewardsPb.RewardOfferType_USSTOCKS_ACTIVATION_REWARD_OFFER,
					},
					PageContext: &rpc.PageContextRequest{
						PageSize: 1,
					},
				}).Return(&rewardsPb.RewardsResponse{
					Status: rpc.StatusOk(),
					Rewards: []*rewardsPb.Reward{
						{
							Id:     "reward-id",
							Status: rewardsPb.RewardStatus_EXPIRED,
						},
					},
				}, nil)
			},
			want: &ussRewPb.GetRewardDetailsResponse{
				Status: rpc.StatusOk(),
				RewardDetails: &ussRewPb.RewardDetails{
					Status: ussRewPb.RewardStatus_REWARD_STATUS_INELIGIBLE_FOR_REWARD,
				},
			},
			wantErr: false,
		},
		{
			name: "if reward system give unexpected reward status",
			args: &args{
				ctx: context.Background(),
				req: &ussRewPb.GetRewardDetailsRequest{ActorId: "actor-id"},
			},
			setupMockCalls: func(m *mockDependencies, a *args) {
				m.rewardClient.EXPECT().GetRewardsByActorId(gomock.Any(), &rewardsPb.RewardsByActorIdRequest{
					ActorId: a.req.GetActorId(),
					Filter: &rewardsPb.RewardsByActorIdRequest_Filter{
						RewardOfferType: rewardsPb.RewardOfferType_USSTOCKS_ACTIVATION_REWARD_OFFER,
					},
					PageContext: &rpc.PageContextRequest{
						PageSize: 1,
					},
				}).Return(&rewardsPb.RewardsResponse{
					Status: rpc.StatusOk(),
					Rewards: []*rewardsPb.Reward{
						{
							Id:     "reward-id",
							Status: rewardsPb.RewardStatus_CLAWED_BACK,
						},
					},
				}, nil)
			},
			want: &ussRewPb.GetRewardDetailsResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svc, mock := getServiceWithMocks(t)
			tt.setupMockCalls(mock, tt.args)
			got, err := svc.GetRewardDetails(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRewardDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				cmpopts.IgnoreFields(ussRewPb.GetRewardDetailsResponse{}),
			}

			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("GetRewardDetails() got = %v,\n want %v", got, tt.want)
			}
		})
	}
}
