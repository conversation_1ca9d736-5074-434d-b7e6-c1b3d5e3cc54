package rewards

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	ussRewPb "github.com/epifi/gamma/api/usstocks/rewards"
	genConf "github.com/epifi/gamma/usstocks/config/genconf"
)

var (
	USSRewardStatusToRewardSystemStatusMap = map[rewardsPb.RewardStatus]ussRewPb.RewardStatus{
		rewardsPb.RewardStatus_PROCESSED:                      ussRewPb.RewardStatus_REWARD_STATUS_SUCCESS,
		rewardsPb.RewardStatus_LOCKED:                         ussRewPb.RewardStatus_REWARD_STATUS_LOCK,
		rewardsPb.RewardStatus_PROCESSING_PENDING:             ussRewPb.RewardStatus_REWARD_STATUS_PROCESSING,
		rewardsPb.RewardStatus_PROCESSING_IN_PROGRESS:         ussRewPb.RewardStatus_REWARD_STATUS_PROCESSING,
		rewardsPb.RewardStatus_PROCESSING_MANUAL_INTERVENTION: ussRewPb.RewardStatus_REWARD_STATUS_PROCESSING,
		rewardsPb.RewardStatus_PROCESSING_FAILED:              ussRewPb.RewardStatus_REWARD_STATUS_FAILED,
		rewardsPb.RewardStatus_CREATED:                        ussRewPb.RewardStatus_REWARD_STATUS_TO_BE_CLAIM,
	}
)

const RewardsByActorIdPageSize = 1

func (s *Service) GetRewardDetails(ctx context.Context, req *ussRewPb.GetRewardDetailsRequest) (*ussRewPb.GetRewardDetailsResponse, error) {
	rewardResp, err := s.rewardsClient.GetRewardsByActorId(ctx, &rewardsPb.RewardsByActorIdRequest{
		ActorId: req.GetActorId(),
		Filter: &rewardsPb.RewardsByActorIdRequest_Filter{
			RewardOfferType: rewardsPb.RewardOfferType_USSTOCKS_ACTIVATION_REWARD_OFFER,
		},
		PageContext: &rpc.PageContextRequest{
			PageSize: RewardsByActorIdPageSize,
		},
	})
	if err = epifigrpc.RPCError(rewardResp, err); err != nil {
		logger.Error(ctx, "error while getting data from rewards", zap.Error(err), zap.Any(logger.ACTOR_ID_V2, req.GetActorId()))
		return &ussRewPb.GetRewardDetailsResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	if len(rewardResp.GetRewards()) == 0 {
		return &ussRewPb.GetRewardDetailsResponse{
			Status: rpc.StatusOk(),
			RewardDetails: &ussRewPb.RewardDetails{
				Status: ussRewPb.RewardStatus_REWARD_STATUS_INELIGIBLE_FOR_REWARD,
			},
		}, nil
	}
	status, ok := USSRewardStatusToRewardSystemStatusMap[rewardResp.GetRewards()[0].GetStatus()]
	// this is check is added,so that we dont promise something as default
	// might be user have done some process for reward claim
	if !ok {
		logger.Error(ctx, "error while mapping status of reward", zap.Error(err), zap.Any(logger.ACTOR_ID_V2, req.GetActorId()), zap.String(logger.REWARD_STATUS, rewardResp.GetRewards()[0].GetStatus().String()))
		return &ussRewPb.GetRewardDetailsResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	displayDetails := &ussRewPb.StockRewardDisplayDetails{
		Options:     getStockRewardOptions(s.config.USStocksRewardDetails()),
		Amount:      rewardResp.GetRewards()[0].GetRewardOptions().GetOptions()[0].GetUsstockReward().GetAmount(),
		BgLottieUrl: s.config.USStocksRewardDetails().BgLottieUrl(),
	}
	// Reward would be in processing state only if we created corresponding uss orders which can be shown to the user
	// If orders are not created we would update the status back to 'REWARD_STATUS_TO_BE_CLAIM'
	var rewardReq *ussRewPb.RewardRequest
	if status == ussRewPb.RewardStatus_REWARD_STATUS_PROCESSING {
		rewardReq, err = s.rewardRequestDao.GetByActorAndRewardId(ctx, req.GetActorId(), rewardResp.GetRewards()[0].GetId())
		if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error while getting reward request", zap.Any(logger.ACTOR_ID_V2, req.GetActorId()), zap.Any(logger.REWARD_ID, rewardResp.GetRewards()[0].GetId()))
			return &ussRewPb.GetRewardDetailsResponse{
				Status: rpc.StatusInternal()}, nil
		}
		if rewardReq.GetRewardFulfillmentDetails().GetWalletOrderId() == "" ||
			rewardReq.GetRewardFulfillmentDetails().GetStockBuyOrderId() == "" {
			status = ussRewPb.RewardStatus_REWARD_STATUS_TO_BE_CLAIM
		}
	}
	return &ussRewPb.GetRewardDetailsResponse{
		Status: rpc.StatusOk(),
		RewardDetails: &ussRewPb.RewardDetails{
			RewardId:      rewardResp.GetRewards()[0].GetId(),
			Status:        status,
			RewardRequest: rewardReq,
		},
		DisplayDetails: displayDetails,
	}, nil
}

func getStockRewardOptions(rewardDetails *genConf.USStocksRewardDetails) []*ussRewPb.StockRewardOptionDetails {
	var rewardOptions []*ussRewPb.StockRewardOptionDetails
	if rewardDetails.Option1StockName() != "" {
		rewardOptions = append(rewardOptions, &ussRewPb.StockRewardOptionDetails{StockName: rewardDetails.Option1StockName()})
	}

	if rewardDetails.Option2StockName() != "" {
		rewardOptions = append(rewardOptions, &ussRewPb.StockRewardOptionDetails{StockName: rewardDetails.Option2StockName()})
	}
	return rewardOptions
}
