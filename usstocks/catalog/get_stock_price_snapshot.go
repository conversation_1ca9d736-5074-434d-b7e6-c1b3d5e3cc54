package catalog

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	pb "github.com/epifi/gamma/api/usstocks/catalog"
	vgStocksPb "github.com/epifi/gamma/api/vendorgateway/stocks"
	"github.com/epifi/gamma/usstocks/utils"
)

func (s *Service) GetStocksPriceSnapshotsBySymbols(ctx context.Context, req *pb.GetStocksPriceSnapshotsBySymbolsRequest) (*pb.GetStocksPriceSnapshotsBySymbolsResponse, error) {
	if req.GetStockSymbols() == nil {
		logger.Error(ctx, "no stock Symbols are present")
		return &pb.GetStocksPriceSnapshotsBySymbolsResponse{
			Status: rpc.StatusInvalidArgument(),
		}, nil
	}

	// getSnapshotDataFromCache returns stored cache data and symbol list of those stocks for which data is not present or data is old (need to refresh)
	stockPriceSnapshots, stockSymbolsNotPresent, oldSnapshotStockSymbols, err := s.getSnapshotDataFromCache(ctx, req.GetStockSymbols())
	if err != nil {
		logger.Error(ctx, "error in getSnapshotDataFromCache", zap.Error(err))
		return &pb.GetStocksPriceSnapshotsBySymbolsResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	// if for some stocks data need to be updated, then for those symbols will fetch data from vendor in background and returns existing data from cache
	if len(oldSnapshotStockSymbols) > 0 {
		goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
			_, err = s.getAndStoreSnapshots(ctx, oldSnapshotStockSymbols)
			if err != nil {
				logger.Error(ctx, "error in getAndStoreSnapshots", zap.Error(err))
				return
			}
		})
	}

	// if for all symbols data is present then return response
	if len(stockSymbolsNotPresent) == 0 {
		return &pb.GetStocksPriceSnapshotsBySymbolsResponse{
			Status:              rpc.StatusOk(),
			StockPriceSnapshots: stockPriceSnapshots,
		}, nil
	}

	// for remaining symbols for which data is not present, then fetch data from vendor
	snapshotsMapResp, err := s.getAndStoreSnapshots(ctx, stockSymbolsNotPresent)
	if err != nil {
		if errors.Is(err, epifierrors.ErrResourceExhausted) {
			logger.Error(ctx, "resource exhausted while storing snapshots", zap.Error(err))
			return &pb.GetStocksPriceSnapshotsBySymbolsResponse{Status: rpc.StatusResourceExhausted()}, nil
		}
		logger.Error(ctx, "error in getAndStoreSnapshots", zap.Error(err))
		return &pb.GetStocksPriceSnapshotsBySymbolsResponse{Status: rpc.StatusInternal()}, nil
	}

	for stockSymbol, snapResponse := range snapshotsMapResp {
		stockPriceSnapshots[stockSymbol] = snapResponse
	}

	return &pb.GetStocksPriceSnapshotsBySymbolsResponse{
		Status:              rpc.StatusOk(),
		StockPriceSnapshots: stockPriceSnapshots,
	}, nil
}

func (s *Service) getSnapshotDataFromCache(ctx context.Context, stockSymbols []string) (map[string]*vgStocksPb.GetStocksPriceSnapshotsResponse_StockPriceInfo, []string, []string, error) {
	// stockSymbolsNotPresent contains those stocks symbols for which snapshot price is not present in cache, while
	// oldSnapshotStockSymbols contains those stock symbols for which snapshot price is present but data is old
	var stockSymbolsNotPresent, oldSnapshotStockSymbols []string
	stockPriceSnapshots := make(map[string]*vgStocksPb.GetStocksPriceSnapshotsResponse_StockPriceInfo)

	// inside this loop first collects those snapshots which are already stored in cache
	for _, stockSymbol := range stockSymbols {
		spsCacheKey := fmt.Sprintf("%s:%s", utils.SPSCacheKeyPrefix, stockSymbol)

		marshalledSnapshot, cacheErr := s.usStocksCache.Get(ctx, spsCacheKey)
		if cacheErr != nil {
			if errors.Is(cacheErr, epifierrors.ErrRecordNotFound) {
				logger.Info(ctx, fmt.Sprintf("no cache found for key: %s", spsCacheKey))
				stockSymbolsNotPresent = append(stockSymbolsNotPresent, stockSymbol)
				continue
			} else {
				return nil, nil, nil, errors.Wrap(cacheErr, "error getting snapshot price from cache")
			}
		}

		cacheVal := &utils.StockPriceSnapshotCache{}
		err := json.Unmarshal([]byte(marshalledSnapshot), cacheVal)
		if err != nil {
			return nil, nil, nil, errors.Wrap(cacheErr, "failed to unmarshal cached value")
		}

		stockPriceSnapshots[stockSymbol] = cacheVal.StockPriceSnapshot
		if time.Since(cacheVal.LastUpdatedAt.AsTime()) > utils.SPSCacheRefreshFreq {
			oldSnapshotStockSymbols = append(oldSnapshotStockSymbols, stockSymbol)
		}
	}
	return stockPriceSnapshots, stockSymbolsNotPresent, oldSnapshotStockSymbols, nil
}

func (s *Service) getAndStoreSnapshots(ctx context.Context, stockSymbols []string) (map[string]*vgStocksPb.GetStocksPriceSnapshotsResponse_StockPriceInfo, error) {
	snapshotsMapResp, err := s.getSnapshotsFromVendor(ctx, stockSymbols)
	if err != nil {
		return nil, errors.Wrap(err, "error in getSnapshotsFromVendor")
	}
	if err = s.storeSnapshotsInCache(ctx, snapshotsMapResp); err != nil {
		return nil, errors.Wrap(err, "error during store snapshot data in cache")
	}
	return snapshotsMapResp, nil
}

// getSnapshotsFromVendor returns stock price snapshots from vendor side and store snapshot data in cache
func (s *Service) getSnapshotsFromVendor(ctx context.Context, stockSymbols []string) (map[string]*vgStocksPb.GetStocksPriceSnapshotsResponse_StockPriceInfo, error) {
	resp, err := s.vgStocksClient.GetStocksPriceSnapshots(ctx, &vgStocksPb.GetStocksPriceSnapshotsRequest{
		Header:           &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_ALPACA},
		Symbols:          stockSymbols,
		MarketDataSource: vgStocksPb.MarketDataSource_MARKET_DATA_SOURCE_IEX,
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		if resp.GetStatus().IsResourceExhausted() {
			return nil, errors.Wrap(epifierrors.ErrResourceExhausted, "resource exhausted while fetching stocks price snapshots from vendor")
		}
		logger.Error(ctx, "", zap.Error(te))
		return nil, errors.Wrap(te, "error in GetStocksPriceSnapshots")
	}
	return resp.GetStockPriceSnapshots(), nil
}

// storeSnapshotsInCache stores snapshot data in cache for each stockSymbol
func (s *Service) storeSnapshotsInCache(ctx context.Context, snapshotMap map[string]*vgStocksPb.GetStocksPriceSnapshotsResponse_StockPriceInfo) error {
	for stockSymbol, snapResponse := range snapshotMap {
		spsCacheKey := fmt.Sprintf("%s:%s", utils.SPSCacheKeyPrefix, stockSymbol)
		cacheVal := utils.GetStocksPriceSnapshotCache(snapResponse)
		newMarshalledSnapshotBytes, err := json.Marshal(cacheVal)
		if err != nil {
			return fmt.Errorf("error marshalling stock prices: %w", err)
		}

		err = s.usStocksCache.Set(ctx, spsCacheKey, string(newMarshalledSnapshotBytes), utils.SPSCacheTTL)
		if err != nil {
			return fmt.Errorf("error storing stock prices in cache: %w, stockSymbol: %v", err, stockSymbol)
		}
	}
	return nil
}
