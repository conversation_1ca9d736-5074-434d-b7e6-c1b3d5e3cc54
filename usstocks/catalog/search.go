package catalog

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"encoding/json"
	"strings"

	"github.com/olivere/elastic/v7"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	pb "github.com/epifi/gamma/api/usstocks/catalog"
	invPagination "github.com/epifi/gamma/investment/pagination"
	zincFlattener "github.com/epifi/gamma/pkg/zinc/flattener"
	"github.com/epifi/gamma/pkg/zinc/search"
)

var (
	// usStocksNameSearchFuzzyMatchFields is the list of fields that should be used
	// for fuzzy-matching and prefix-based searching of stocks using user-input text
	usStocksNameSearchFuzzyMatchFields = []string{
		"stockBasicDetails.name.shortName",
		"stockBasicDetails.name.standardName",
	}

	// usStocksNameSearchFuzzyMatchFields is the list of fields that should be used
	// for exact-matching when searching for stocks using user-input text
	usStocksNameSearchExactMatchFields = []string{
		"stockBasicDetails.description.longDescription",
	}
	// usStocksTermFieldsWithBoost defines fields which we want to run an exact match and the boost
	// that field should be given.
	// Boost is used to give higher weightage to the term query as compared to other match queries
	// In this case we want to give higher weightage to exact symbol match as compared to any other match query result
	usStocksTermFieldsWithBoost = map[string]float64{
		"symbol": 10.0,
	}
)

const (
	defaultSearchPageSize     = uint32(10)
	fuzzinessAuto             = "AUTO"
	scoreSortField            = "_score"
	nullValue                 = "NULL"
	defaultMinAppVersionValue = 0
)

// RangeQueryParams represents a parameter for range queries in ElasticSearch.
type RangeQueryParams struct {
	// QueryField specifies the field on which the range query is applied.
	QueryField string
	// QueryUpperBound stores the upper bound value for the range query.
	QueryUpperBound float64
	// QueryLowerBound stores the lower bound value for the range query.
	QueryLowerBound float64
}

func (s *Service) SearchStocks(ctx context.Context, req *pb.SearchStocksRequest) (*pb.SearchStocksResponse, error) {
	isSimilarStocksQuery := req.GetMinMarketCap() > 0 || req.GetMaxMarketCap() > 0 ||
		req.GetIndustryId() != "" || req.GetTrackingIndexName() != ""
	if len(req.GetStockIds()) == 0 && len(req.GetSearchText()) == 0 && !isSimilarStocksQuery {
		logger.Error(ctx, "at least one of search text, stock IDs or similar stocks query fields must be set")
		return &pb.SearchStocksResponse{Status: rpc.StatusInternalWithDebugMsg("at least one of search text, stock IDs or similar stocks query fields must be set")}, nil
	}

	// Step 1: Build the Query
	var query elastic.Query

	query = s.getTextSearchQuery(query, req)
	query = s.getFilterByIDsQuery(query, req)
	query = s.getFilterOutByIDsQuery(query, req)

	query, err := s.appendStocksVisibilityFilters(ctx, req.GetActorId(), query, req.GetVersion(), req.GetPlatform())
	if err != nil {
		logger.Error(ctx, "failed to append stocks visibility filters", zap.Error(err))
		return &pb.SearchStocksResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	query, err = getFilterByMarketCapRange(query, req)
	if err != nil {
		logger.Error(ctx, "error getting query with market cap range", zap.Error(err))
		return &pb.SearchStocksResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}
	query = getFilterByIndustryId(query, req)
	query = getFilterByTrackingIndex(query, req)

	esQueryString, fromToken, pageSize, err := s.buildESQuery(ctx, query, req)
	if err != nil {
		logger.Error(ctx, "failed to build elastic search query", zap.Error(err))
		return &pb.SearchStocksResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}
	logger.Debug(ctx, "search query", zap.String("esQueryString", esQueryString), zap.Uint32("fromToken", fromToken), zap.Uint32("pageSize", pageSize))

	// Step 2: Invoke search client (Zinc in this case) to execute the search query
	searchResult, count, err := s.searchClient.ExecuteSearchQuery(ctx, s.conf.CatalogSearchConfig().IndexName(), esQueryString)
	if err != nil {
		logger.Error(ctx, "failed to execute search query", zap.Error(err))
		return &pb.SearchStocksResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	// Step 3: Parse the response from zinc
	flattener, err := zincFlattener.GetFlattener(&pb.Stock{})
	if err != nil {
		logger.Error(ctx, "invalid flattener for us-stocks", zap.Error(err))
		return &pb.SearchStocksResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}
	stocks, err := search.GetProtoListFromSearchResult(ctx, searchResult, flattener)
	if err != nil {
		logger.Error(ctx, "failed to get Proto list from search results", zap.Any(logger.REQUEST, req), zap.Error(err))
		return &pb.SearchStocksResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}
	finalStockList, pageCtx, err := invPagination.NewSearchPageCtxResp(&invPagination.SearchPageToken{LastRowToken: fromToken}, stocks, pageSize)
	if err != nil {
		logger.Error(ctx, "failed to get page context response", zap.Error(err))
		return &pb.SearchStocksResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}
	logger.Debug(ctx, "search results page", zap.Int("numStocks", len(finalStockList)), zap.Any("pageContext", pageCtx))

	// TODO(Brijesh): Remove after DB integration
	stocksWithPerf, err := s.convertProtoToStocks(ctx, finalStockList)
	if err != nil {
		if errors.Is(err, epifierrors.ErrResourceExhausted) {
			logger.Error(ctx, "resource exhausted while adding performance to stocks", zap.Error(err))
			return &pb.SearchStocksResponse{Status: rpc.StatusResourceExhausted()}, nil
		}
		logger.Error(ctx, "error adding perf", zap.Error(err))
		return &pb.SearchStocksResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	return &pb.SearchStocksResponse{
		Status:      rpc.StatusOk(),
		PageContext: pageCtx,
		Stocks:      stocksWithPerf,
		TotalStocks: uint32(count),
	}, nil
}

func (s *Service) buildESQuery(ctx context.Context, query elastic.Query, req *pb.SearchStocksRequest) (string, uint32, uint32, error) {

	pageToken, err := invPagination.GetSearchPageToken(req.PageContext)
	if err != nil {
		logger.Error(ctx, "unable to get pagination token", zap.Any(logger.REQUEST, req), zap.Error(err))
		return "", 0, 0, err
	}

	var (
		fromToken uint32
	)
	if pageToken == nil {
		fromToken = 0
	} else {
		fromToken = pageToken.LastRowToken
	}

	pageSize := defaultSearchPageSize
	if req.GetPageContext().GetPageSize() > 0 {
		pageSize = req.GetPageContext().PageSize
	}

	srcQuery, err := query.Source()
	if err != nil {
		logger.Error(ctx, "error in query source", zap.Any(logger.REQUEST, req), zap.Error(err))
		return "", 0, 0, err
	}
	searchQ := struct {
		Query interface{} `json:"query"`
		From  uint32      `json:"from"`
		Size  uint32      `json:"size"`
		Sort  interface{} `json:"sort"`
	}{
		srcQuery,
		fromToken,
		pageSize + 1,
		s.getSortField(req.GetSortParam(), req.GetSortOrder()),
	}
	queryBytes, err := json.Marshal(searchQ)
	if err != nil {
		logger.Error(ctx, "error in building query for filtering", zap.Any("query", searchQ), zap.Error(err))
		return "", 0, 0, err
	}

	return string(queryBytes), fromToken, pageSize, nil
}

func (s *Service) getTextSearchQuery(query elastic.Query, req *pb.SearchStocksRequest) elastic.Query {
	searchText := strings.TrimSpace(req.SearchText)

	if len(req.GetSearchText()) == 0 {
		return query
	}
	res := make([]elastic.Query, 0)
	if query != nil {
		res = append(res, query)
	}

	// Build the search query
	for _, field := range usStocksNameSearchFuzzyMatchFields {
		res = append(res,
			elastic.NewMatchQuery(field, searchText).Fuzziness(fuzzinessAuto),
			elastic.NewPrefixQuery(field, strings.ToLower(searchText)),
		)
	}
	for _, field := range usStocksNameSearchExactMatchFields {
		res = append(res, elastic.NewMatchQuery(field, searchText).Fuzziness("0"))
	}
	// convert search text to capital for exact filter
	filterSearchText := strings.ToUpper(searchText)
	for field, boost := range usStocksTermFieldsWithBoost {
		res = append(res, elastic.NewTermQuery(field, filterSearchText).Boost(boost))
	}
	return elastic.NewBoolQuery().Must(elastic.NewBoolQuery().Should(res...).MinimumNumberShouldMatch(1))
}

func (s *Service) getFilterByIDsQuery(query elastic.Query, req *pb.SearchStocksRequest) elastic.Query {
	if len(req.GetStockIds()) == 0 {
		return query
	}
	res := make([]elastic.Query, 0)
	if query != nil {
		res = append(res, query)
	}
	res = append(res, elastic.NewBoolQuery().Must(elastic.NewTermsQuery("id", getInterfaceList(req.GetStockIds())...)))
	return elastic.NewBoolQuery().Must(res...)
}

func (s *Service) getFilterOutByIDsQuery(query elastic.Query, req *pb.SearchStocksRequest) elastic.Query {
	if len(req.GetExcludeStockIds()) == 0 {
		return query
	}
	res := make([]elastic.Query, 0)
	if query != nil {
		res = append(res, query)
	}
	res = append(res, elastic.NewBoolQuery().MustNot(elastic.NewTermsQuery("id", getInterfaceList(req.GetExcludeStockIds())...)))
	return elastic.NewBoolQuery().Must(res...)
}

func getFilterByMarketCapRange(query elastic.Query, req *pb.SearchStocksRequest) (elastic.Query, error) {
	if req.GetMinMarketCap() == 0 && req.GetMaxMarketCap() == 0 {
		return query, nil
	}
	res := make([]elastic.Query, 0)
	if query != nil {
		res = append(res, query)
	}
	marketCapRangeQuery, err := getRangedQueryFromFilter(&RangeQueryParams{
		QueryField:      "companyInfo.marketCap.marketCapValue.units",
		QueryUpperBound: float64(req.GetMaxMarketCap()),
		QueryLowerBound: float64(req.GetMinMarketCap()),
	})
	if err != nil {
		return nil, errors.Wrap(err, "error getting market cap query")
	}
	if marketCapRangeQuery != nil {
		res = append(res, marketCapRangeQuery)
	}
	return elastic.NewBoolQuery().Must(res...), nil
}

func getFilterByIndustryId(query elastic.Query, req *pb.SearchStocksRequest) elastic.Query {
	if req.GetIndustryId() == "" {
		return query
	}
	res := make([]elastic.Query, 0)
	if query != nil {
		res = append(res, query)
	}
	industryIdQuery := elastic.NewBoolQuery().Must(elastic.NewTermQuery("industryId", req.GetIndustryId()))
	res = append(res, industryIdQuery)
	return elastic.NewBoolQuery().Must(res...)
}

func getFilterByTrackingIndex(query elastic.Query, req *pb.SearchStocksRequest) elastic.Query {
	if req.GetTrackingIndexName() == "" {
		return query
	}
	res := make([]elastic.Query, 0)
	if query != nil {
		res = append(res, query)
	}
	trackingIndexQuery := elastic.NewBoolQuery().Must(elastic.NewTermQuery("etfPerformanceMetrics.trackingDetails.trackingIndex", req.GetTrackingIndexName()))
	res = append(res, trackingIndexQuery)
	return elastic.NewBoolQuery().Must(res...)
}

func (s *Service) getSortField(sortParam pb.SortOptionType, sortOrder pb.SearchStocksRequest_SortOrder) []interface{} {
	sortField := map[string]interface{}{getQueryFieldFromSortParam(sortParam): struct {
		Order string `json:"order"`
	}{Order: getSortOrder(sortOrder)}}
	finalSortField := make([]interface{}, 0)
	if sortParam == pb.SortOptionType_SORT_OPTION_TYPE_UNSPECIFIED || sortParam == pb.SortOptionType_SORT_OPTION_TYPE_RELEVANCY {
		finalSortField = append(finalSortField, scoreSortField, sortField)
	} else {
		finalSortField = append(finalSortField, sortField, scoreSortField)
	}

	return finalSortField
}

func getQueryFieldFromSortParam(sortParam pb.SortOptionType) string {
	switch sortParam {
	case pb.SortOptionType_SORT_OPTION_TYPE_MARKET_CAP:
		return "companyInfo.marketCap.marketCapValue.units"
	case pb.SortOptionType_SORT_OPTION_TYPE_PE_RATIO:
		return "financialInfo.latestValuationRatio.priceToEps"
	case pb.SortOptionType_SORT_OPTION_TYPE_TRACKING_ERROR:
		return "etfPerformanceMetrics.trackingDetails.trackingErrorOneYear"
	case pb.SortOptionType_SORT_OPTION_TYPE_EXPENSE_RATIO:
		return "etfFinancialInfo.expenseRatio.netExpenseRatio"
	default:
		// ToDO(Junaid): Confirm with aditya for default sort param, this is not supported yet
		return "stockBasicDetails.name.shortName"
	}
}

func getSortOrder(sortOrder pb.SearchStocksRequest_SortOrder) string {
	switch sortOrder {
	case pb.SearchStocksRequest_ASCENDING:
		return "asc"
	default:
		return "desc"
	}
}

func (s *Service) convertProtoToStocks(ctx context.Context, protos []proto.Message) ([]*pb.Stock, error) {
	stocks := make([]*pb.Stock, 0)
	for _, pMess := range protos {
		stock := pMess.(*pb.Stock)
		stocks = append(stocks, stock)
	}

	// TODO(Brijesh): Remove after DB integration
	err := s.addDailyPerformanceToStocks(ctx, stocks)
	if err != nil {
		logger.Error(ctx, "error adding perf", zap.Error(err))
		return nil, err
	}
	return stocks, nil
}

var getInterfaceList = func(in []string) (out []interface{}) {
	for idx := range in {
		out = append(out, in[idx])
	}
	return out
}

func (s *Service) appendStocksVisibilityFilters(ctx context.Context, actorId string, query elastic.Query, appVersion uint32, appPlatform commontypes.Platform) (elastic.Query, error) {
	res := make([]elastic.Query, 0)
	if query != nil {
		res = append(res, query)
	}

	// Append Platform and Version Check Query
	verQuery, err := getPlatformVersionCheckQuery(ctx, appPlatform, appVersion)
	if err != nil {
		return nil, errors.Wrap(err, "error in getting platform version check query")
	}
	if verQuery != nil {
		res = append(res, verQuery)
	}

	// Append User Group Check Query
	userGroups, err := s.getUserGroups(ctx, actorId)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get user group")
	}

	var userGroupsString []string
	for _, group := range userGroups {
		userGroupsString = append(userGroupsString, group.String())
	}

	res = append(res, getUserGroupQuery(userGroupsString))
	return elastic.NewBoolQuery().Must(res...), nil
}

// Needed to filter out stocks based on the app platform and version.
func getPlatformVersionCheckQuery(ctx context.Context, appPlatform commontypes.Platform, appVersion uint32) (elastic.Query, error) {
	supportedAppVersionField := getSupportedAppVersionQueryField(appPlatform)
	if len(supportedAppVersionField) == 0 {
		// Note: Error is ignored to support queries from website which do not have min app version requirements.
		logger.Info(ctx, "platform is not supported for app version filtering", zap.Uint32(logger.VERSION_ID, appVersion), zap.String(logger.PLATFORM, appPlatform.String()))
		return nil, nil
	}

	return getRangedQueryFromFilter(&RangeQueryParams{
		QueryField:      supportedAppVersionField,
		QueryUpperBound: float64(appVersion),
		QueryLowerBound: defaultMinAppVersionValue,
	})
}

/*
Needed to filter out stocks based on the actor's user groups.
For eg: we would not show 'INTERNAL' stocks to users not present in
'INTERNAL' user group
*/
func getUserGroupQuery(userGroups []string) elastic.Query {
	// By default, for all stocks that do not belong to any user groups, the value is initialized as NULL.
	// that's why for getting stocks assigning NULL value in the user groups.
	userGroups = append(userGroups, nullValue)
	return elastic.NewBoolQuery().Must(elastic.NewTermsQuery(zincFlattener.UsstocksAllowedUserGroupsKey, getInterfaceList(userGroups)...))
}

// getSupportedAppVersionQueryField returns the field of supported version value for given platform
func getSupportedAppVersionQueryField(appPlatform commontypes.Platform) string {
	switch appPlatform {
	case commontypes.Platform_ANDROID:
		return zincFlattener.UsstocksAndroidVersionKey
	case commontypes.Platform_IOS:
		return zincFlattener.UsstocksIosVersionKey
	default:
		return ""
	}
}

// getRangedQueryFromFilter generates a range query for the provided filterData based on the QueryField value,
// constraining the field's values to be within the range defined by QueryLowerBound and QueryUpperBound.
func getRangedQueryFromFilter(filterData *RangeQueryParams) (*elastic.BoolQuery, error) {
	type Field struct {
		Gte float64 `json:"gte"`
		Lte float64 `json:"lte"`
	}

	rangeMap := make(map[string]interface{})
	rangeMap[filterData.QueryField] = &Field{
		Gte: filterData.QueryLowerBound,
		Lte: filterData.QueryUpperBound,
	}
	rangeQuery := struct {
		Range map[string]interface{} `json:"range"`
	}{
		Range: rangeMap,
	}
	queryBytes, err := json.Marshal(rangeQuery)
	if err != nil {
		return nil, err
	}
	return elastic.NewBoolQuery().Must(elastic.NewRawStringQuery(string(queryBytes))), nil
}
