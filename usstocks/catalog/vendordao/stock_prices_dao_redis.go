package vendordao

import (
	"context"
	"fmt"
	"math/rand"
	"time"

	"github.com/google/wire"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"golang.org/x/exp/maps"
	"google.golang.org/protobuf/proto"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/usstocks/catalog"
	vgStocks "github.com/epifi/gamma/api/vendorgateway/stocks"
	genConf "github.com/epifi/gamma/usstocks/config/genconf"
	"github.com/epifi/gamma/usstocks/utils"
)

type StockPricesDaoRedis struct {
	conf             *genConf.Config
	usStocksVgClient vgStocks.StocksClient
	usStocksCache    cache.CacheStorage
}

func NewLatestStockPricesDaoRedis(
	conf *genConf.Config,
	usStocksVgClient vgStocks.StocksClient,
	usStocksCache cache.CacheStorage,
) *StockPricesDaoRedis {
	return &StockPricesDaoRedis{
		conf:             conf,
		usStocksVgClient: usStocksVgClient,
		usStocksCache:    usStocksCache,
	}
}

var StockPricesVendorDaoRedisWireSet = wire.NewSet(NewLatestStockPricesDaoRedis, wire.Bind(new(StockPricesDao), new(*StockPricesDaoRedis)))

func unmarshalStockPriceInCache(marshalledStockPriceInCache string) (*catalog.StockPriceInCache, error) {
	cachedStockPriceV1 := &catalog.StockPriceInCache{}
	err := proto.Unmarshal([]byte(marshalledStockPriceInCache), cachedStockPriceV1)
	if err != nil {
		return nil, err
	}
	return cachedStockPriceV1, nil
}

func (s *StockPricesDaoRedis) GetLatestPriceBySymbol(ctx context.Context, symbols []string) (map[string]*catalog.StockPriceInCache, error) {
	prices, err := s.getPriceBySymbol(ctx, symbols, LatestPrice)
	if err != nil {
		return nil, errors.Wrapf(err, "error getting latest price")
	}
	return prices, nil
}

func (s *StockPricesDaoRedis) GetPrevMarketDayClosePriceBySymbol(ctx context.Context, symbols []string) (map[string]*catalog.StockPriceInCache, error) {
	prices, err := s.getPriceBySymbol(ctx, symbols, PrevMarketClosePrice)
	if err != nil {
		return nil, errors.Wrapf(err, "error getting previous price")
	}
	return prices, nil
}

func (s *StockPricesDaoRedis) getPriceBySymbol(ctx context.Context, symbols []string, p PriceType) (map[string]*catalog.StockPriceInCache, error) {
	if len(symbols) == 0 {
		return nil, errors.New("no symbols to get price")
	}
	var (
		keys   []string
		prices = map[string]*catalog.StockPriceInCache{}
	)
	for _, symbol := range symbols {
		key, err := p.getCacheKey(symbol)
		if err != nil {
			return nil, errors.Wrapf(err, "error getting cache key for symbol: %s, price type: %s", symbol, p.String())
		}
		keys = append(keys, key)
	}
	marshalledPrices, err := s.usStocksCache.MultiGet(ctx, keys)
	if err != nil {
		return nil, fmt.Errorf("error getting marshalled prices from cache: %w", err)
	}
	var symbolsNotInCache, symbolsUpdatedLongAgo []string
	for i, marshalledPrice := range marshalledPrices {
		if marshalledPrice == "" {
			symbolsNotInCache = append(symbolsNotInCache, symbols[i])
			continue
		}
		var priceInCache *catalog.StockPriceInCache
		priceInCache, err = unmarshalStockPriceInCache(marshalledPrice)
		if err != nil {
			return nil, fmt.Errorf("error unmarshalling price in cache: %w", err)
		}
		if time.Now().After(priceInCache.GetUpdatedAt().AsTime().Add(utils.HistoricalDataAPIDelay)) {
			symbolsUpdatedLongAgo = append(symbolsUpdatedLongAgo, symbols[i])
		}
		prices[symbols[i]] = priceInCache
	}
	if len(symbolsUpdatedLongAgo) != 0 {
		// refresh prices for symbols present in cache since a long time, in the background
		refreshPricesAsyncFn := func(ctx context.Context) {
			_, setErr := s.setPricesInCacheFromSnapshot(ctx, symbolsUpdatedLongAgo, p)
			if setErr != nil {
				logger.Error(ctx, "error refreshing price in cache async", zap.Error(setErr))
			}
		}
		goroutine.Run(epificontext.CloneCtx(ctx), 2*time.Minute, refreshPricesAsyncFn)
	}
	if len(symbolsNotInCache) != 0 {
		pricesForSymbolsNotFound, setErr := s.setPricesInCacheFromSnapshot(ctx, symbolsNotInCache, p)
		if setErr != nil {
			return nil, errors.Wrapf(setErr, "error setting latest price in cache")
		}
		maps.Copy(prices, pricesForSymbolsNotFound)
	}
	return prices, nil
}

func (s *StockPricesDaoRedis) setPricesInCacheFromSnapshot(ctx context.Context, symbols []string, p PriceType) (map[string]*catalog.StockPriceInCache, error) {
	if len(symbols) == 0 {
		return nil, errors.New("no symbols to set price")
	}
	var (
		priceFetchingStartedAt           = time.Now()
		priceCacheKeys, priceCacheValues []string
		priceCacheExpirations            []time.Duration
		prices                           = map[string]*catalog.StockPriceInCache{}
	)
	snapshots, err := s.getLatestStockSnapshotFromVendorInBatches(ctx, symbols)
	if err != nil {
		return nil, fmt.Errorf("error getting latest stock snapshot: %w", err)
	}
	for symbol, snapshot := range snapshots {
		priceCacheRequirements, getErr := p.getPriceCacheRequirements(symbol, snapshot, priceFetchingStartedAt)
		if getErr != nil {
			return nil, errors.Wrapf(getErr, "error getting price cache requirements for symbol: %s", symbol)
		}
		marshalledPrice, marshallErr := proto.Marshal(priceCacheRequirements.Price)
		if marshallErr != nil {
			return nil, errors.Wrapf(marshallErr, "error marhsalling price from snapshot")
		}
		priceCacheKeys = append(priceCacheKeys, priceCacheRequirements.CacheKey)
		priceCacheValues = append(priceCacheValues, string(marshalledPrice))
		priceCacheExpirations = append(priceCacheExpirations, priceCacheRequirements.CacheExpiration)
		prices[symbol] = priceCacheRequirements.Price
	}
	if len(priceCacheKeys) != 0 {
		err = s.usStocksCache.MultiSet(ctx, priceCacheKeys, priceCacheValues, priceCacheExpirations)
		if err != nil {
			return nil, errors.Wrap(err, "error setting price")
		}
	}
	return prices, nil
}

func (s *StockPricesDaoRedis) getLatestStockSnapshotFromVendorInBatches(ctx context.Context, symbols []string) (map[string]*vgStocks.GetStocksPriceSnapshotsResponse_StockPriceInfo, error) {
	// nolint: gosec
	rand.New(rand.NewSource(time.Now().UnixMilli()))
	prices := make(map[string]*vgStocks.GetStocksPriceSnapshotsResponse_StockPriceInfo)
	var requestBatch []string
	for i, symbol := range symbols {
		requestBatch = append(requestBatch, symbol)
		if len(requestBatch) == s.conf.GetStocksPriceSnapshotsBatchConfig().BatchSize() || i == len(symbols)-1 {
			res, err := s.usStocksVgClient.GetStocksPriceSnapshots(ctx, &vgStocks.GetStocksPriceSnapshotsRequest{
				Header:           &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_ALPACA},
				Symbols:          requestBatch,
				MarketDataSource: vgStocks.MarketDataSource_MARKET_DATA_SOURCE_IEX,
			})
			if te := epifigrpc.RPCError(res, err); te != nil {
				if res.GetStatus().IsResourceExhausted() {
					return nil, errors.Wrap(epifierrors.ErrResourceExhausted, "resource exhausted while fetching stocks price snapshots in batches")
				}
				return nil, fmt.Errorf("error getting stock price snapshots: %w", te)
			}
			maps.Copy(prices, res.GetStockPriceSnapshots())
			requestBatch = nil
			// nolint:gosec
			// using math/rand is not a security issue here as we are ok using pseudorandom number generators
			jitter := time.Duration(1 + rand.Intn(s.conf.GetStocksPriceSnapshotsBatchConfig().JitterSeconds()))
			time.Sleep(jitter * time.Second)
		}
	}
	return prices, nil
}

func (s *StockPricesDaoRedis) SetLatestPriceBySymbol(ctx context.Context, prices map[string]*catalog.StockPriceInCache) error {
	var (
		latestPriceCacheKeys, latestPriceCacheValues []string
		latestPriceCacheTTLs                         []time.Duration
	)
	for symbol, price := range prices {
		cacheKey, err := LatestPrice.getCacheKey(symbol)
		if err != nil {
			return errors.Wrapf(err, "error getting latest price cache key for symbol: %s", symbol)
		}
		latestPriceCacheKeys = append(latestPriceCacheKeys, cacheKey)
		marshalledPrice, err := proto.Marshal(price)
		if err != nil {
			return fmt.Errorf("error marhsalling price update: %w", err)
		}
		latestPriceCacheValues = append(latestPriceCacheValues, string(marshalledPrice))
		latestPriceCacheTTLs = append(latestPriceCacheTTLs, TradePriceCacheTTL)
	}
	if len(latestPriceCacheKeys) > 0 {
		err := s.usStocksCache.MultiSet(ctx, latestPriceCacheKeys, latestPriceCacheValues, latestPriceCacheTTLs)
		if err != nil {
			return fmt.Errorf("error storing latest prices in cache: %w", err)
		}
	}
	return nil
}
