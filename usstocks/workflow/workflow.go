package workflow

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"fmt"
	"time"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	"github.com/epifi/be-common/pkg/epifitemporal/celestial"
	payNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/pay"
	usStocksNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/usstocks"
	"github.com/epifi/be-common/pkg/logger"

	iftPb "github.com/epifi/gamma/api/pay/internationalfundtransfer"
	payPayloadPb "github.com/epifi/gamma/api/pay/payload"
	"github.com/epifi/gamma/api/usstocks"
	usStocksActivityPb "github.com/epifi/gamma/api/usstocks/activity"
	orderPb "github.com/epifi/gamma/api/usstocks/order"
	"github.com/epifi/gamma/api/usstocks/payload"
	workflow2 "github.com/epifi/gamma/api/usstocks/workflow"
	vgUSStocks "github.com/epifi/gamma/api/vendorgateway/stocks"
)

// TODO(shubham.c): Take ownership as request header in workflow request

const (
	USSAlpacaOwnership = commontypes.Ownership_US_STOCKS_ALPACA
)

// nolint: dupl
func getBuyWorkflowProcessingReqParams(ctx workflow.Context) (*workflow2.BuyUSStocksRequest, error) {
	wfReqID := workflow.GetInfo(ctx).WorkflowExecution.ID
	wfProcessingParams := &activityPb.GetWorkflowProcessingParamsV2Response{}
	if err := activityPkg.Execute(ctx, epifitemporal.GetWorkflowProcessingParamsV2, wfProcessingParams, &activityPb.GetWorkflowProcessingParamsV2Request{
		RequestHeader: &activityPb.RequestHeader{
			Ownership: USSAlpacaOwnership,
		},
		WfReqId: wfReqID,
	}); err != nil {
		return nil, err
	}

	workflowReq := &workflow2.BuyUSStocksRequest{}
	if err := (protojson.UnmarshalOptions{DiscardUnknown: true}).Unmarshal(wfProcessingParams.GetWfReqParams().GetPayload(), workflowReq); err != nil {
		return nil, fmt.Errorf("failed to unmarshal payload %w", err)
	}

	return workflowReq, nil
}

func initiateWorkflowStage(ctx workflow.Context, stage workflowPb.Stage, status stagePb.Status, clientReqId, wfReqID string) error {
	if err := activityPkg.Execute(ctx, epifitemporal.InitiateWorkflowStageV2, &activityPb.InitiateWorkflowStageV2Response{},
		&activityPb.InitiateWorkflowStageV2Request{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: clientReqId,
				Ownership:   USSAlpacaOwnership,
			},
			WfReqId: wfReqID,
			Stage:   stage,
			Status:  status,
		}); err != nil {
		return fmt.Errorf("InitiateWorkflowStageV2 activity failure: %w", err)
	}
	return nil
}

func updateWorkflowStage(ctx workflow.Context, stage workflowPb.Stage, workflowStageStatus stagePb.Status, clientReqId, wfReqID string) error {
	// Step 3: Update workflow stage status
	if err2 := activityPkg.Execute(ctx, epifitemporal.UpdateWorkflowStage, &activityPb.UpdateWorkflowStageResponse{}, &activityPb.UpdateWorkflowStageRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: clientReqId,
			Ownership:   USSAlpacaOwnership,
		},
		WfReqId: wfReqID,
		Stage:   stage,
		Status:  workflowStageStatus,
	}); err2 != nil {
		return fmt.Errorf("UpdateWorkflowStage activity failed: %w", err2)
	}

	if celestial.IsTerminalStatus(workflowStageStatus) {
		if err := activityPkg.Execute(ctx, epifitemporal.PublishWorkflowUpdateEventV2, &activityPb.PublishWorkflowUpdateEventV2Response{}, &activityPb.PublishWorkflowUpdateEventV2Request{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: clientReqId,
				Ownership:   USSAlpacaOwnership,
			},
			WfReqId: wfReqID,
		}); err != nil {
			return fmt.Errorf("PublishWorkflowUpdateEvent activity failed: %w", err)
		}
	}
	return nil
}

func updateOrder(ctx workflow.Context, clientReqId string, order *orderPb.Order, fieldMasks []orderPb.OrderFieldMask) error {
	err := activityPkg.Execute(ctx, usStocksNs.UpdateOrder, &usStocksActivityPb.UpdateOrderResponse{}, &usStocksActivityPb.UpdateOrderRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: clientReqId,
			Ownership:   USSAlpacaOwnership,
		},
		OrderId:    clientReqId,
		Order:      order,
		FieldMasks: fieldMasks,
	})
	return err
}

func updateOrderUsingLocalActivity(ctx workflow.Context, clientReqId string, order *orderPb.Order, fieldMasks []orderPb.OrderFieldMask) error {
	err := activityPkg.ExecuteLocally(ctx, usStocksNs.UpdateOrder, &usStocksActivityPb.UpdateOrderResponse{}, &usStocksActivityPb.UpdateOrderRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: clientReqId,
			Ownership:   USSAlpacaOwnership,
		},
		OrderId:    clientReqId,
		Order:      order,
		FieldMasks: fieldMasks,
	})
	return err
}

func updateWfReqIdInOrder(ctx workflow.Context, clientReqId string) (stagePb.Status, error) {
	stage := usStocksNs.UpdateWfReqIdInOrderStage
	lg := workflow.GetLogger(ctx)
	wfReqID := workflow.GetInfo(ctx).WorkflowExecution.ID
	if err := celestial.InitiateWorkflowStage(ctx, stage, stagePb.Status_INITIATED); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("InitiateWorkflowStage activity failure: %w", err)
	}
	err := updateOrderUsingLocalActivity(ctx, clientReqId, &orderPb.Order{WfReqId: wfReqID}, []orderPb.OrderFieldMask{orderPb.OrderFieldMask_ORDER_FIELD_MASK_WF_REQ_ID})
	if err != nil {
		lg.Error("activity failed to execute locally", zap.String(logger.ACTIVITY, string(usStocksNs.UpdateOrder)), zap.Error(err))
	}
	workflowStageStatus := celestial.GetWorkflowStageStatusForErr(err)
	err = celestial.UpdateWorkflowStage(ctx, stage, workflowStageStatus)
	if err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage: %s: %w", stage, err)
	}
	return workflowStageStatus, nil
}

// nolint: dupl
func getSellWorkflowProcessingReqParams(ctx workflow.Context) (*workflow2.SellUSStocksRequest, error) {
	wfReqID := workflow.GetInfo(ctx).WorkflowExecution.ID
	wfProcessingParams := &activityPb.GetWorkflowProcessingParamsV2Response{}
	if err := activityPkg.Execute(ctx, epifitemporal.GetWorkflowProcessingParamsV2, wfProcessingParams, &activityPb.GetWorkflowProcessingParamsV2Request{
		RequestHeader: &activityPb.RequestHeader{
			Ownership: USSAlpacaOwnership,
		},
		WfReqId: wfReqID,
	}); err != nil {
		return nil, err
	}

	workflowReq := &workflow2.SellUSStocksRequest{}
	if err := (protojson.UnmarshalOptions{DiscardUnknown: true}).Unmarshal(wfProcessingParams.GetWfReqParams().GetPayload(), workflowReq); err != nil {
		return nil, fmt.Errorf("failed to unmarshal payload %w", err)
	}

	return workflowReq, nil
}

//nolint:dupl
func getAccountOpeningReqParams(ctx workflow.Context) (*workflow2.UsBrokerAccountOpeningWorkflowRequest, *workflowPb.ProcessingParams, error) {
	wfReqID := workflow.GetInfo(ctx).WorkflowExecution.ID
	wfProcessingParams := &activityPb.GetWorkflowProcessingParamsV2Response{}
	if err := activityPkg.Execute(ctx, epifitemporal.GetWorkflowProcessingParamsV2, wfProcessingParams, &activityPb.GetWorkflowProcessingParamsV2Request{
		RequestHeader: &activityPb.RequestHeader{
			Ownership: USSAlpacaOwnership,
		},
		WfReqId: wfReqID,
	}); err != nil {
		return nil, nil, err
	}

	workflowReq := &workflow2.UsBrokerAccountOpeningWorkflowRequest{}
	if err := (protojson.UnmarshalOptions{DiscardUnknown: true}).Unmarshal(wfProcessingParams.GetWfReqParams().GetPayload(), workflowReq); err != nil {
		return nil, nil, fmt.Errorf("failed to unmarshal payload %w", err)
	}

	return workflowReq, wfProcessingParams.GetWfReqParams(), nil
}

func getTrackOrderStageStatusForVgOrderStatus(orderStatus vgUSStocks.OrderStatus, err error) stagePb.Status {
	// stage status will be failed if workflow fails to track order status
	// in rest of the cases, workflow status will be success (order status: filled, expired, rejected)
	if err != nil {
		return celestial.GetWorkflowStageStatusForErr(err)
	}
	switch orderStatus {
	case vgUSStocks.OrderStatus_ORDER_STATUS_SUSPENDED:
		return stagePb.Status_FAILED
	case vgUSStocks.OrderStatus_ORDER_STATUS_FILLED, vgUSStocks.OrderStatus_ORDER_STATUS_EXPIRED, vgUSStocks.OrderStatus_ORDER_STATUS_REJECTED:
		return stagePb.Status_SUCCESSFUL
	case vgUSStocks.OrderStatus_ORDER_STATUS_CANCELED:
		return stagePb.Status_CANCELED
	default:
		return stagePb.Status_MANUAL_INTERVENTION
	}
}

// sendNotification func sends notification async, and also ignoring the error, so it will not affect workflow of buy or
// sell process due to send notification failure
func sendNotification(ctx workflow.Context, clientReqId string, notificationType usstocks.UsStockNotificationType) (
	epifitemporal.AsyncFuture[*usStocksActivityPb.SendNotificationResponse], error) {
	lg := workflow.GetLogger(ctx)
	resp := &usStocksActivityPb.SendNotificationResponse{}

	future, err := activityPkg.ExecuteAsync(ctx, usStocksNs.SendPushNotification, &usStocksActivityPb.SendNotificationRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: clientReqId,
			Ownership:   USSAlpacaOwnership,
		},
		UsStockNotificationType: notificationType,
	}, resp)
	if err != nil {
		lg.Error("error while sending push notification", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, clientReqId), zap.String(logger.NOTIFICATION_TYPE, notificationType.String()))
		return nil, err
	}
	return future, nil
}

// trackOrderStatus periodically polls the order status from vendor with some interval and simultaneously waits for push event for update in order state
// which ever among the poll or push is received first, is considered the truth and the workflow moves forward
func trackOrderStatus(ctx workflow.Context, clientReqId string, trackOrderStatusResp *usStocksActivityPb.TrackOrderStatusResponse) (stagePb.Status, error) {
	var workflowStageStatus stagePb.Status
	// Get Status for order from vendor Asynchronously by API call (polling of update)
	asyncActivity, err := activityPkg.ExecuteAsync(ctx, usStocksNs.TrackOrderStatus, &usStocksActivityPb.TrackOrderStatusRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: clientReqId,
			Ownership:   USSAlpacaOwnership,
		},
	}, trackOrderStatusResp)

	if err != nil {
		// since activity was not ale to start, returning error
		return stagePb.Status_STATUS_UNSPECIFIED, errors.Wrap(err, "failed to start async order status polling")
	}

	// futureHandler is responsible for handling order updates if they come from polling
	asyncActivity.AddFutureHandler(func(getErr error, trackOrderStatusResp *usStocksActivityPb.TrackOrderStatusResponse) {
		workflowStageStatus = getTrackOrderStageStatusForVgOrderStatus(trackOrderStatusResp.GetOrder().GetOrderStatus(), getErr)
	})
	// also waiting for signal from server sent events order updates observer
	signalPayload := &payload.VendorOrderUpdateSignalPayload{}
	sigChannel := epifitemporal.NewSignalChannel(ctx, usStocksNs.VendorOrderUpdateSignal, signalPayload)
	sigChannel.AddReceiverHandler(func(getErr error, resp *payload.VendorOrderUpdateSignalPayload) {
		workflowStageStatus = getTrackOrderStageStatusForVgOrderStatus(resp.GetOrder().GetOrderStatus(), getErr)
	})
	err = epifitemporal.ReceiveSignalWithFuture(ctx, asyncActivity, sigChannel, 24*100*time.Hour)
	if err != nil {
		return celestial.GetWorkflowStageStatusForErr(err), nil
	}
	// if we receive order status via order update sse signal
	// the populate the order object in activity response
	// since order object in used in subsequent line of code
	if trackOrderStatusResp.GetOrder() == nil && signalPayload.GetOrder() != nil {
		trackOrderStatusResp.Order = signalPayload.GetOrder()
	}
	return workflowStageStatus, nil
}

func updateWalletOrder(ctx workflow.Context, walletOrderId, clientReqId string, order *orderPb.WalletOrder, fieldMasks []orderPb.WalletOrderFieldMask) error {
	err := activityPkg.Execute(ctx, usStocksNs.UpdateWalletOrder, &usStocksActivityPb.UpdateWalletOrderResponse{}, &usStocksActivityPb.UpdateWalletOrderRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: clientReqId,
			Ownership:   USSAlpacaOwnership,
		},
		WalletOrderId: walletOrderId,
		WalletOrder:   order,
		FieldMasks:    fieldMasks,
	})
	return err
}

func updateWalletOrderUsingLocalActivity(ctx workflow.Context, walletOrderId, clientReqId string, order *orderPb.WalletOrder, fieldMasks []orderPb.WalletOrderFieldMask) error {
	err := activityPkg.ExecuteLocally(ctx, usStocksNs.UpdateWalletOrder, &usStocksActivityPb.UpdateWalletOrderResponse{}, &usStocksActivityPb.UpdateWalletOrderRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: clientReqId,
			Ownership:   USSAlpacaOwnership,
		},
		WalletOrderId: walletOrderId,
		WalletOrder:   order,
		FieldMasks:    fieldMasks,
	})
	return err
}

func updateWfReqIdInWalletOrder(ctx workflow.Context, clientReqId string) (stagePb.Status, error) {
	stage := usStocksNs.UpdateWfReqIdInWalletOrderStage
	lg := workflow.GetLogger(ctx)
	wfReqID := workflow.GetInfo(ctx).WorkflowExecution.ID
	if err := celestial.InitiateWorkflowStage(ctx, stage, stagePb.Status_INITIATED); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("InitiateWorkflowStage activity failure: %w", err)
	}
	err := updateWalletOrderUsingLocalActivity(ctx, clientReqId, clientReqId, &orderPb.WalletOrder{WfReqId: wfReqID}, []orderPb.WalletOrderFieldMask{orderPb.WalletOrderFieldMask_WALLET_ORDER_FIELD_MASK_WF_REQ_ID})
	if err != nil {
		lg.Error("activity failed to execute locally", zap.String(logger.ACTIVITY, string(usStocksNs.UpdateWalletOrder)), zap.Error(err))
	}
	workflowStageStatus := celestial.GetWorkflowStageStatusForErr(err)
	err = celestial.UpdateWorkflowStage(ctx, stage, workflowStageStatus)
	if err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage: %s: %w", stage, err)
	}
	return workflowStageStatus, nil
}

// Note: This func is not expected to send stage.Status_FAILED ever since that will lead to user's money not being refunded
func waitOnRefundReceivedSignal(ctx workflow.Context, wfReqID string, clientReqId string) (stagePb.Status, error) {
	stage := workflowPb.Stage_REFUND_PAYMENT
	// Step 1: Initiate workflow stage Handle Foreign Remittance Result
	if err := initiateWorkflowStage(ctx, stage, stagePb.Status_BLOCKED, clientReqId, wfReqID); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("InitiateWorkflowStageV2 activity failure: %w", err)
	}

	var workflowStageStatus stagePb.Status
	workflow.NewSelector(ctx).
		AddReceive(workflow.GetSignalChannel(ctx, string(payNs.ForeignFundTransferStatusSignal)), func(c workflow.ReceiveChannel, more bool) {
			var b []byte
			c.Receive(ctx, &b)
			refundSignal := &payPayloadPb.InternationalFundTransferStatusSignal{}
			if err := (protojson.UnmarshalOptions{DiscardUnknown: true}).Unmarshal(b, refundSignal); err != nil {
				workflow.GetLogger(ctx).Error("error in unmarshalling InternationalFundTransferStatusSignal", zap.Error(err))
				workflowStageStatus = stagePb.Status_MANUAL_INTERVENTION
				return
			}

			// marking workflow as manual intervention in case refund transaction did not resulted in success
			if refundSignal.GetStatus() == iftPb.InternationalFundTransferStatus_REFUND_SUCCESSFUL {
				workflowStageStatus = stagePb.Status_SUCCESSFUL
				return
			}
			workflowStageStatus = stagePb.Status_MANUAL_INTERVENTION
		}).
		AddFuture(workflow.NewTimer(ctx, 7*24*time.Hour), func(f workflow.Future) {
			workflowStageStatus = stagePb.Status_MANUAL_INTERVENTION
		}).
		Select(ctx)
	if err := updateWorkflowStage(ctx, stage, workflowStageStatus, clientReqId, wfReqID); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, err
	}
	return workflowStageStatus, nil
}

// function performs asynchronous activity execution for SendNotification activity
// after async execution is triggered, `waitOnActivityCompletion` flag is used to decide
// if the execution should wait on activity response or not
//
// the support is helpful for cases when workflow execution terminates immediately after triggering async activity execution
// If workflow execution terminates, activities associated with workflow also gets terminated
// so, the requirement is to wait on activity completion before workflow termination for terminal state PNs
// waitOnActivityCompletion should be requested as `true` for such cases
func sendOrderPNForNewWorkflowVersionOnly(ctx workflow.Context, clientReqId string,
	notificationType usstocks.UsStockNotificationType, waitOnActivityCompletion bool) {
	_ = workflow.GetVersion(ctx, "sending-order-update-pn", workflow.DefaultVersion, 1)

	pnFuture, err := sendNotification(ctx, clientReqId, notificationType)
	if err != nil {
		workflow.GetLogger(ctx).Error("error in sending async PN", zap.String(logger.CLIENT_REQUEST_ID, clientReqId), zap.Error(err))
		return
	}

	// ignoring PN future, as `waitOnActivityCompletion` flag is turned off
	if !waitOnActivityCompletion {
		return
	}

	pnFuture.AddFutureHandler(func(futureGetErr error, resp *usStocksActivityPb.SendNotificationResponse) {
		if futureGetErr != nil {
			workflow.GetLogger(ctx).Error("unable to get future from resp", zap.String(logger.CLIENT_REQUEST_ID, clientReqId), zap.Error(futureGetErr))
			return
		}
	})
	pnFuture.AddToSelector(ctx, workflow.NewSelector(ctx)).Select(ctx)
}
