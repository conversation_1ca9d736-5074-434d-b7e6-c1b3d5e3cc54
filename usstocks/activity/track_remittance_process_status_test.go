package activity_test

import (
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"github.com/google/uuid"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/gamma/api/usstocks"
	activityPb "github.com/epifi/gamma/api/usstocks/activity"
	"github.com/epifi/gamma/api/usstocks/order"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	usStocksNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/usstocks"
)

func TestProcessor_TrackRemittanceProcessStatus(t *testing.T) {
	act, md, assertTest := getProcessorWithMocks(t)
	defer assertTest()
	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(act)

	tests := []struct {
		name      string
		req       *activityPb.TrackRemittanceProcessStatusRequest
		wantMocks func(mocks *mockDependencies)
		wantErr   bool
		assertErr func(err error) bool
		wantRes   *activityPb.TrackRemittanceProcessStatusResponse
	}{
		{
			name: "wallet fund transfer success",
			req: &activityPb.TrackRemittanceProcessStatusRequest{
				SwiftTransferId: "swift-transfer-id",
			},
			wantMocks: func(mocks *mockDependencies) {
				mocks.remittanceProcessesDao.EXPECT().GetBySwiftTransferId(gomock.Any(),
					"swift-transfer-id", usstocks.RemittanceType_REMITTANCE_TYPE_OUTWARD,
					[]order.RemittanceProcessFieldMask{order.RemittanceProcessFieldMask_REMITTANCE_PROCESS_FIELD_MASK_STATUS}).
					Return(&order.RemittanceProcess{
						Id:              uuid.NewString(),
						WfReqId:         "wf-req-id",
						Vendor:          usstocks.Vendor_ALPACA,
						SwiftTransferId: "swift-transfer-id",
						Type:            usstocks.RemittanceType_REMITTANCE_TYPE_OUTWARD,
						Status:          usstocks.RemittanceProcessStatus_REMITTANCE_PROCESS_STATUS_SUCCESS,
						Details: &order.RemittanceDetails{Details: &order.RemittanceDetails_OutwardRemittanceDetails{
							OutwardRemittanceDetails: &order.OutwardRemittanceDetails{
								InternationalAccountPiId: "account-pi-id",
								IftBatchEntityId:         "entity-id",
							},
						}},
					}, nil)
			},
			wantErr: false,
			wantRes: &activityPb.TrackRemittanceProcessStatusResponse{
				Status: usstocks.RemittanceProcessStatus_REMITTANCE_PROCESS_STATUS_SUCCESS,
			},
		},
		{
			name: "error in db query in getting remittance process",
			req: &activityPb.TrackRemittanceProcessStatusRequest{
				SwiftTransferId: "swift-transfer-id",
			},
			wantMocks: func(mocks *mockDependencies) {
				mocks.remittanceProcessesDao.EXPECT().GetBySwiftTransferId(gomock.Any(),
					"swift-transfer-id", usstocks.RemittanceType_REMITTANCE_TYPE_OUTWARD,
					[]order.RemittanceProcessFieldMask{order.RemittanceProcessFieldMask_REMITTANCE_PROCESS_FIELD_MASK_STATUS}).
					Return(nil, epifierrors.ErrInvalidArgument)
			},
			wantErr:   true,
			wantRes:   nil,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "remittance process record not found",
			req: &activityPb.TrackRemittanceProcessStatusRequest{
				SwiftTransferId: "swift-transfer-id",
			},
			wantMocks: func(mocks *mockDependencies) {
				mocks.remittanceProcessesDao.EXPECT().GetBySwiftTransferId(gomock.Any(),
					"swift-transfer-id", usstocks.RemittanceType_REMITTANCE_TYPE_OUTWARD,
					[]order.RemittanceProcessFieldMask{order.RemittanceProcessFieldMask_REMITTANCE_PROCESS_FIELD_MASK_STATUS}).
					Return(nil, epifierrors.ErrRecordNotFound)
			},
			wantErr:   true,
			wantRes:   nil,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "remittance process in progress",
			req: &activityPb.TrackRemittanceProcessStatusRequest{
				SwiftTransferId: "swift-transfer-id",
			},
			wantMocks: func(mocks *mockDependencies) {
				mocks.remittanceProcessesDao.EXPECT().GetBySwiftTransferId(gomock.Any(),
					"swift-transfer-id", usstocks.RemittanceType_REMITTANCE_TYPE_OUTWARD,
					[]order.RemittanceProcessFieldMask{order.RemittanceProcessFieldMask_REMITTANCE_PROCESS_FIELD_MASK_STATUS}).
					Return(&order.RemittanceProcess{
						Id:              uuid.NewString(),
						WfReqId:         "wf-req-id",
						Vendor:          usstocks.Vendor_ALPACA,
						SwiftTransferId: "swift-transfer-id",
						Type:            usstocks.RemittanceType_REMITTANCE_TYPE_OUTWARD,
						Status:          usstocks.RemittanceProcessStatus_REMITTANCE_PROCESS_STATUS_INITIATED,
						Details: &order.RemittanceDetails{Details: &order.RemittanceDetails_OutwardRemittanceDetails{
							OutwardRemittanceDetails: &order.OutwardRemittanceDetails{
								InternationalAccountPiId: "account-pi-id",
								IftBatchEntityId:         "entity-id",
							},
						}},
					}, nil)
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
			wantRes:   nil,
		},
		{
			name: "remittance process failed",
			req: &activityPb.TrackRemittanceProcessStatusRequest{
				SwiftTransferId: "swift-transfer-id",
			},
			wantMocks: func(mocks *mockDependencies) {
				mocks.remittanceProcessesDao.EXPECT().GetBySwiftTransferId(gomock.Any(),
					"swift-transfer-id", usstocks.RemittanceType_REMITTANCE_TYPE_OUTWARD,
					[]order.RemittanceProcessFieldMask{order.RemittanceProcessFieldMask_REMITTANCE_PROCESS_FIELD_MASK_STATUS}).
					Return(&order.RemittanceProcess{
						Id:              uuid.NewString(),
						WfReqId:         "wf-req-id",
						Vendor:          usstocks.Vendor_ALPACA,
						SwiftTransferId: "swift-transfer-id",
						Type:            usstocks.RemittanceType_REMITTANCE_TYPE_OUTWARD,
						Status:          usstocks.RemittanceProcessStatus_REMITTANCE_PROCESS_STATUS_FAILED,
						Details: &order.RemittanceDetails{Details: &order.RemittanceDetails_OutwardRemittanceDetails{
							OutwardRemittanceDetails: &order.OutwardRemittanceDetails{
								InternationalAccountPiId: "account-pi-id",
								IftBatchEntityId:         "entity-id",
							},
						}},
					}, nil)
			},
			wantErr: false,
			wantRes: &activityPb.TrackRemittanceProcessStatusResponse{
				Status: usstocks.RemittanceProcessStatus_REMITTANCE_PROCESS_STATUS_FAILED,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.wantMocks(md)
			got, err := env.ExecuteActivity(usStocksNs.TrackRemittanceProcessStatus, tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("TrackRemittanceProcessStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr && !tt.assertErr(err) {
				t.Errorf("TrackRemittanceProcessStatus() error = %v, assertion failed", err)
				return
			}
			var gotRes *activityPb.TrackRemittanceProcessStatusResponse
			if got != nil {
				if err1 := got.Get(&gotRes); err1 != nil {
					t.Errorf("TrackRemittanceProcessStatus() error extracting response : %v", err1)
					return
				}
			}
			if diff := cmp.Diff(gotRes, tt.wantRes, protocmp.Transform()); (gotRes != nil || tt.wantRes != nil) && diff != "" {
				t.Errorf("TrackRemittanceProcessStatus() \nwantRes=%v \ngotRes=%v \n diff=%s", tt.wantRes, gotRes, diff)
				return
			}
			assertTest()
		})
	}
}
