package exchanger

import (
	"context"
	"errors"
	"fmt"
	"reflect"
	"sync"
	"testing"
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/async/waitgroup"

	casperPb "github.com/epifi/gamma/api/casper"

	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/epifi/gamma/casper/exchanger/dao"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/test"

	exchangerPb "github.com/epifi/gamma/api/casper/exchanger"
	mockDao "github.com/epifi/gamma/casper/test/mocks/exchanger/dao"
	mocks "github.com/epifi/gamma/casper/test/mocks/helper"
	slackHelperMocks "github.com/epifi/gamma/casper/test/mocks/helper/slack_helper"
)

func TestService_CreateExchangerOffer(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	// init mocks
	mockOfferDao := mockDao.NewMockIExchangerOfferDao(ctr)
	mockSlackHelperSvc := slackHelperMocks.NewMockISlackHelperSvc(ctr)

	createOfferReq := &exchangerPb.CreateExchangerOfferRequest{
		RedemptionCurrency: exchangerPb.ExchangerOfferRedemptionCurrency_EXCHANGER_OFFER_REDEMPTION_CURRENCY_FI_COINS,
		RedemptionPrice:    100,
		OfferDisplayDetails: &exchangerPb.ExchangerOfferDisplayDetails{
			Title:       "Let your Fi-Coins pay for stuff",
			Subtitle:    "Trade your Fi-Coins for cash",
			Desc:        "Win exciting cashback rewards",
			ImageUrl:    "image-url-1",
			TileBgColor: "#111111",
		},
		OfferOptionsConfig: &exchangerPb.ExchangerOfferOptionsConfig{},
		OfferAggregatesConfig: &exchangerPb.ExchangerOfferAggregatesConfig{
			DailyAllowedAttemptsPerUser: 10,
		},
		AdditionalDetails: &exchangerPb.ExchangerOfferAdditionalDetails{
			IsSalaryAccountExclusive: false,
			IsFiPoints:               commontypes.BooleanEnum_TRUE,
		},
	}

	offer1 := &exchangerPb.ExchangerOffer{
		RedemptionCurrency: exchangerPb.ExchangerOfferRedemptionCurrency_EXCHANGER_OFFER_REDEMPTION_CURRENCY_FI_COINS,
		RedemptionPrice:    100,
		OfferDisplayDetails: &exchangerPb.ExchangerOfferDisplayDetails{
			Title:       "Let your Fi-Coins pay for stuff",
			Subtitle:    "Trade your Fi-Coins for cash",
			Desc:        "Win exciting cashback rewards",
			ImageUrl:    "image-url-1",
			TileBgColor: "#111111",
		},
		OfferOptionsConfig: &exchangerPb.ExchangerOfferOptionsConfig{},
		OfferAggregatesConfig: &exchangerPb.ExchangerOfferAggregatesConfig{
			DailyAllowedAttemptsPerUser: 10,
		},
		TagsInfo: &casperPb.TagsInfo{
			Tags: []casperPb.TagName{casperPb.TagName_REDEMPTION_MECHANISM_CBR},
		},
		AdditionalDetails: &exchangerPb.ExchangerOfferAdditionalDetails{
			IsSalaryAccountExclusive: false,
			IsFiPoints:               commontypes.BooleanEnum_TRUE,
		},
	}

	exchangerSvc := NewService(nil, mockOfferDao, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, mockSlackHelperSvc, nil, nil, dyconf, nil, nil, nil)

	type args struct {
		req *exchangerPb.CreateExchangerOfferRequest
	}
	tests := []struct {
		name     string
		mockFunc func(wg *sync.WaitGroup)
		args     args
		want     *exchangerPb.CreateExchangerOfferResponse
		wantErr  bool
	}{
		{
			name: "Successful offer creation flow",
			mockFunc: func(wg *sync.WaitGroup) {
				wg.Add(1)
				mockOfferDao.EXPECT().Create(gomock.Any(), test.NewProtoArgMatcher(offer1)).Return(offer1, nil)
				mockSlackHelperSvc.EXPECT().
					SendMessage(gomock.Any(), gomock.Any(), gomock.Any(), offer1).
					DoAndReturn(func(arg0, arg1, arg2, arg3 interface{}) (r0 interface{}) {
						wg.Done()
						return nil
					}).Times(1)
			},
			args: args{
				req: createOfferReq,
			},
			want: &exchangerPb.CreateExchangerOfferResponse{
				Status:         rpc.StatusOk(),
				ExchangerOffer: offer1,
			},
			wantErr: false,
		},
		{
			name: "create offer dao call failed",
			mockFunc: func(_ *sync.WaitGroup) {
				mockOfferDao.EXPECT().Create(gomock.Any(), test.NewProtoArgMatcher(offer1)).Return(nil, errors.New("error persisting offer"))
			},
			args: args{
				req: createOfferReq,
			},
			want: &exchangerPb.CreateExchangerOfferResponse{
				Status: rpc.StatusInternalWithDebugMsg("error persisting offer"),
			},
			wantErr: false,
		},
	}
	// nolint: scopelint
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var wg sync.WaitGroup
			defer waitgroup.SafeWaitWithDefaultTimeout(&wg)

			// setup mock calls
			tt.mockFunc(&wg)

			got, err := exchangerSvc.CreateExchangerOffer(context.Background(), tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateExchangerOffer() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("CreateExchangerOffer() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_CreateExchangerOfferListing(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	// init mocks
	mockListingDao := mockDao.NewMockIExchangerOfferListingDao(ctr)

	createListingReq := &exchangerPb.CreateExchangerOfferListingRequest{
		ExchangerOfferId: "exchanger-offer-id-1",
		ActiveSince:      "2021-12-20T10:04:05+05:30",
		ActiveTill:       "2021-12-21T10:04:05+05:30",
		DisplaySince:     "2021-12-20T10:04:05+05:30",
		DisplayTill:      "2021-12-21T10:04:05+05:30",
	}

	listing1 := &exchangerPb.ExchangerOfferListing{
		ExchangerOfferId: "exchanger-offer-id-1",
		ActiveSince:      "2021-12-20T10:04:05+05:30",
		ActiveTill:       "2021-12-21T10:04:05+05:30",
		DisplaySince:     "2021-12-20T10:04:05+05:30",
		DisplayTill:      "2021-12-21T10:04:05+05:30",
	}

	exchangerSvc := NewService(mockListingDao, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, dyconf, nil, nil, nil)

	type args struct {
		req *exchangerPb.CreateExchangerOfferListingRequest
	}
	tests := []struct {
		name     string
		mockFunc func()
		args     args
		want     *exchangerPb.CreateExchangerOfferListingResponse
		wantErr  bool
	}{
		{
			name: "Successful listing creation flow",
			mockFunc: func() {
				mockListingDao.EXPECT().Create(gomock.Any(), test.NewProtoArgMatcher(listing1)).Return(listing1, nil)
			},
			args: args{
				req: createListingReq,
			},
			want: &exchangerPb.CreateExchangerOfferListingResponse{
				Status:                rpc.StatusOk(),
				ExchangerOfferListing: listing1,
			},
			wantErr: false,
		},
		{
			name: "create listing dao call failed",
			mockFunc: func() {
				mockListingDao.EXPECT().Create(gomock.Any(), test.NewProtoArgMatcher(listing1)).Return(nil, errors.New("error persisting listing"))
			},
			args: args{
				req: createListingReq,
			},
			want: &exchangerPb.CreateExchangerOfferListingResponse{
				Status: rpc.StatusInternalWithDebugMsg("error persisting listing"),
			},
			wantErr: false,
		},
	}
	// nolint: scopelint
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// setup mock calls
			tt.mockFunc()

			got, err := exchangerSvc.CreateExchangerOfferListing(context.Background(), tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateExchangerOfferListing() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("CreateExchangerOfferListing() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetExchangerOffers(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	// init mocks
	mockListingDao := mockDao.NewMockIExchangerOfferListingDao(ctr)
	mockOfferDao := mockDao.NewMockIExchangerOfferDao(ctr)
	mockUserHelperSvc := mocks.NewMockIUserHelperService(ctr)

	listing1 := &exchangerPb.ExchangerOfferListing{
		ExchangerOfferId: "exchanger-offer-id-1",
		ActiveSince:      "2021-12-20T10:04:05+05:30",
		ActiveTill:       "2021-12-21T10:04:05+05:30",
		DisplaySince:     "2021-12-20T10:04:05+05:30",
		DisplayTill:      "2021-12-21T10:04:05+05:30",
	}
	listing2 := &exchangerPb.ExchangerOfferListing{
		ExchangerOfferId: "exchanger-offer-id-2",
		ActiveSince:      "2021-12-20T10:04:05+05:30",
		ActiveTill:       "2021-12-21T10:04:05+05:30",
		DisplaySince:     "2021-12-20T10:04:05+05:30",
		DisplayTill:      "2021-12-21T10:04:05+05:30",
	}

	offer1 := &exchangerPb.ExchangerOffer{
		Id:                 "exchanger-offer-id-1",
		RedemptionCurrency: exchangerPb.ExchangerOfferRedemptionCurrency_EXCHANGER_OFFER_REDEMPTION_CURRENCY_FI_COINS,
		RedemptionPrice:    100,
		OfferDisplayDetails: &exchangerPb.ExchangerOfferDisplayDetails{
			Title:       "Let your Fi-Coins pay for stuff",
			Subtitle:    "Trade your Fi-Coins for cash",
			Desc:        "Win exciting cashback rewards",
			ImageUrl:    "image-url-1",
			TileBgColor: "#111111",
		},
		OfferOptionsConfig: &exchangerPb.ExchangerOfferOptionsConfig{},
		OfferAggregatesConfig: &exchangerPb.ExchangerOfferAggregatesConfig{
			DailyAllowedAttemptsPerUser: 10,
		},
		TagsInfo: &casperPb.TagsInfo{Tags: []casperPb.TagName{casperPb.TagName_REDEMPTION_MECHANISM_CBR, casperPb.TagName_REWARD_TYPE_CASH}},
	}
	offer2 := &exchangerPb.ExchangerOffer{
		Id:                 "exchanger-offer-id-2",
		RedemptionCurrency: exchangerPb.ExchangerOfferRedemptionCurrency_EXCHANGER_OFFER_REDEMPTION_CURRENCY_FI_COINS,
		RedemptionPrice:    100,
		OfferDisplayDetails: &exchangerPb.ExchangerOfferDisplayDetails{
			Title:       "Let your Fi-Coins pay for stuff",
			Subtitle:    "Trade your Fi-Coins for cash",
			Desc:        "Win exciting cashback rewards",
			ImageUrl:    "image-url-1",
			TileBgColor: "#111111",
		},
		OfferOptionsConfig: &exchangerPb.ExchangerOfferOptionsConfig{},
		OfferAggregatesConfig: &exchangerPb.ExchangerOfferAggregatesConfig{
			DailyAllowedAttemptsPerUser: 10,
		},
		TagsInfo: &casperPb.TagsInfo{Tags: []casperPb.TagName{casperPb.TagName_REDEMPTION_MECHANISM_CBR, casperPb.TagName_REWARD_TYPE_EGV}},
	}

	getActiveOffersReq := &exchangerPb.GetExchangerOffersRequest{
		ActorId: "actor-1",
	}

	getActiveOffersWithFiltersReq := &exchangerPb.GetExchangerOffersRequest{
		ActorId: "actor-1",
		Filters: &casperPb.CatalogFilters{Tags: []casperPb.TagName{casperPb.TagName_REWARD_TYPE_CASH}},
	}

	exchangerSvc := NewService(mockListingDao, mockOfferDao, nil, nil, nil, nil, nil, nil, nil, nil, nil, mockUserHelperSvc, nil, nil, nil, nil, dyconf, nil, nil, nil)

	type args struct {
		req *exchangerPb.GetExchangerOffersRequest
	}
	tests := []struct {
		name     string
		mockFunc func()
		args     args
		want     *exchangerPb.GetExchangerOffersResponse
		wantErr  bool
	}{
		{
			name: "Successfully fetch active offers",
			mockFunc: func() {
				mockListingDao.EXPECT().GetActiveListings(gomock.Any()).Return([]*exchangerPb.ExchangerOfferListing{listing1, listing2}, nil)
				mockOfferDao.EXPECT().GetByIds(gomock.Any(), []string{listing1.GetExchangerOfferId(), listing2.GetExchangerOfferId()}, gomock.Any()).Return([]*exchangerPb.ExchangerOffer{offer1, offer2}, nil)
				mockUserHelperSvc.EXPECT().IsActorUserInternal(gomock.Any(), gomock.Any()).Return(true, nil).AnyTimes()
			},
			args: args{
				req: getActiveOffersReq,
			},
			want: &exchangerPb.GetExchangerOffersResponse{
				Status:          rpc.StatusOk(),
				ExchangerOffers: []*exchangerPb.ExchangerOffer{offer1, offer2},
				OfferIdToListingMap: map[string]*exchangerPb.ExchangerOfferListing{
					"exchanger-offer-id-1": listing1,
					"exchanger-offer-id-2": listing2,
				},
			},
			wantErr: false,
		},
		{
			name: "Successfully fetch active offers with filters",
			mockFunc: func() {
				mockListingDao.EXPECT().GetActiveListings(gomock.Any()).Return([]*exchangerPb.ExchangerOfferListing{listing1, listing2}, nil)
				mockOfferDao.EXPECT().GetByIds(gomock.Any(), []string{listing1.GetExchangerOfferId(), listing2.GetExchangerOfferId()}, gomock.Any()).Return([]*exchangerPb.ExchangerOffer{offer1, offer2}, nil)
				mockUserHelperSvc.EXPECT().IsActorUserInternal(gomock.Any(), gomock.Any()).Return(true, nil).AnyTimes()
			},
			args: args{
				req: getActiveOffersWithFiltersReq,
			},
			want: &exchangerPb.GetExchangerOffersResponse{
				Status:          rpc.StatusOk(),
				ExchangerOffers: []*exchangerPb.ExchangerOffer{offer1},
				OfferIdToListingMap: map[string]*exchangerPb.ExchangerOfferListing{
					"exchanger-offer-id-1": listing1,
					"exchanger-offer-id-2": listing2,
				},
			},
			wantErr: false,
		},
		{
			name: "Empty actor id must successfully fetch all active offers",
			mockFunc: func() {
				mockListingDao.EXPECT().GetActiveListings(gomock.Any()).Return([]*exchangerPb.ExchangerOfferListing{listing1, listing2}, nil)
				mockOfferDao.EXPECT().GetByIds(gomock.Any(), []string{listing1.GetExchangerOfferId(), listing2.GetExchangerOfferId()}, gomock.Any()).Return([]*exchangerPb.ExchangerOffer{offer1, offer2}, nil)
			},
			args: args{
				req: &exchangerPb.GetExchangerOffersRequest{},
			},
			want: &exchangerPb.GetExchangerOffersResponse{
				Status:          rpc.StatusOk(),
				ExchangerOffers: []*exchangerPb.ExchangerOffer{offer1, offer2},
				OfferIdToListingMap: map[string]*exchangerPb.ExchangerOfferListing{
					"exchanger-offer-id-1": listing1,
					"exchanger-offer-id-2": listing2,
				},
			},
			wantErr: false,
		},
		{
			name: "fetch active listing dao call failed",
			mockFunc: func() {
				mockListingDao.EXPECT().GetActiveListings(gomock.Any()).Return(nil, errors.New("error fetching active listings"))
			},
			args: args{
				req: getActiveOffersReq,
			},
			want: &exchangerPb.GetExchangerOffersResponse{
				Status: rpc.StatusInternalWithDebugMsg("error fetching active listings"),
			},
			wantErr: false,
		},
		{
			name: "fetch offers by ids dao call failed",
			mockFunc: func() {
				mockListingDao.EXPECT().GetActiveListings(gomock.Any()).Return([]*exchangerPb.ExchangerOfferListing{listing1, listing2}, nil)
				mockOfferDao.EXPECT().GetByIds(gomock.Any(), []string{listing1.GetExchangerOfferId(), listing2.GetExchangerOfferId()}, gomock.Any()).Return(nil, errors.New("error fetching offers"))
			},
			args: args{
				req: getActiveOffersReq,
			},
			want: &exchangerPb.GetExchangerOffersResponse{
				Status: rpc.StatusInternalWithDebugMsg("error fetching offers"),
			},
			wantErr: false,
		},
	}
	// nolint: scopelint
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// setup mock calls
			tt.mockFunc()

			got, err := exchangerSvc.GetExchangerOffers(context.Background(), tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetExchangerOffers() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetExchangerOffers() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetExchangerOffersActorAttemptsCount(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockAttemptsDao := mockDao.NewMockIExchangerOfferActorAttemptDao(ctr)
	exchangerSvc := NewService(nil, nil, nil, mockAttemptsDao, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, dyconf, nil, nil, nil)

	currentDayStartTime := datetime.GetTimeAtStartOfTheDay(time.Now().In(datetime.IST))
	fromAttemptTimestamp := timestamppb.New(currentDayStartTime)
	toAttemptTimestamp := timestamppb.New(currentDayStartTime.Add(24 * time.Hour))

	type args struct {
		ctx context.Context
		req *exchangerPb.GetExchangerOffersActorAttemptsCountRequest
	}
	tests := []struct {
		name     string
		args     args
		mockFunc func()
		want     *exchangerPb.GetExchangerOffersActorAttemptsCountResponse
		wantErr  bool
	}{
		{
			name: "fetches actor attempts count for offers",
			args: args{
				ctx: context.Background(),
				req: &exchangerPb.GetExchangerOffersActorAttemptsCountRequest{
					ActorId:           "exchanger-actor-id-1",
					ExchangerOfferIds: []string{"exchanger-offer-1", "exchanger-offer-2"},
					FromAttemptedTime: fromAttemptTimestamp,
					ToAttemptedTime:   toAttemptTimestamp,
				},
			},
			mockFunc: func() {
				// mocking the DB query to return only the attempts count for one offer, i.e.
				// assuming only one offer's attempts exists for the actor.
				mockAttemptsDao.EXPECT().
					GetActorAttemptsCountForOffers(gomock.Any(), "exchanger-actor-id-1",
						[]string{"exchanger-offer-1", "exchanger-offer-2"},
						fromAttemptTimestamp.AsTime(), toAttemptTimestamp.AsTime()).
					Return([]*dao.ExchangerOfferAttemptsCount{
						{
							ExchangerOfferId: "exchanger-offer-1",
							Attempts:         2,
						},
					}, nil)
			},
			// The map will contain entries of all the offers for which the attempts count is asked
			// even if the DB query response doesn't contain all the offers' attempts count.
			want: &exchangerPb.GetExchangerOffersActorAttemptsCountResponse{
				Status: rpc.StatusOk(),
				OfferIdToAttemptsCountMap: map[string]int32{
					"exchanger-offer-1": 2,
					"exchanger-offer-2": 0,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mockFunc()

			got, err := exchangerSvc.GetExchangerOffersActorAttemptsCount(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetExchangerOffersActorAttemptsCount() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetExchangerOffersActorAttemptsCount() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetExchangerOrderById(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	correctReq := &exchangerPb.GetExchangerOrderByIdRequest{ExchangerOrderId: "exchanger-order-1"}
	correctResp := &exchangerPb.ExchangerOfferOrder{
		Id: "exchanger-order-1",
	}

	recNotFoundReq := &exchangerPb.GetExchangerOrderByIdRequest{ExchangerOrderId: "exchanger-order-2"}

	errReq := &exchangerPb.GetExchangerOrderByIdRequest{ExchangerOrderId: "exchanger-order-3"}

	// init mocks
	mockOrdersDao := mockDao.NewMockIExchangerOfferOrderDao(ctr)

	exchangerSvc := NewService(nil, nil, mockOrdersDao, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, dyconf, nil, nil, nil)

	type args struct {
		req *exchangerPb.GetExchangerOrderByIdRequest
	}
	tests := []struct {
		name       string
		setupMocks func()
		args       args
		want       *exchangerPb.GetExchangerOrderByIdResponse
		wantErr    bool
	}{
		{
			name: "should return exchanger order when dao doesn't return an error",
			setupMocks: func() {
				mockOrdersDao.EXPECT().GetById(context.Background(), "exchanger-order-1").Return(correctResp, nil)
			},
			args: args{req: correctReq},
			want: &exchangerPb.GetExchangerOrderByIdResponse{
				Status:         rpc.StatusOk(),
				ExchangerOrder: correctResp,
			},
			wantErr: false,
		},
		{
			name: "should return RecordNotFound status when dao returns ErrRecordNotFound error",
			setupMocks: func() {
				mockOrdersDao.EXPECT().GetById(context.Background(), "exchanger-order-2").Return(nil, epifierrors.ErrRecordNotFound)
			},
			args: args{req: recNotFoundReq},
			want: &exchangerPb.GetExchangerOrderByIdResponse{
				Status:         rpc.StatusRecordNotFound(),
				ExchangerOrder: nil,
			},
			wantErr: false,
		},
		{
			name: "should return StatusInternalWithDebugMsg when dao returns error other than ErrRecordNotFound",
			setupMocks: func() {
				mockOrdersDao.EXPECT().GetById(context.Background(), "exchanger-order-3").Return(nil, fmt.Errorf("errorMessage"))
			},
			args: args{req: errReq},
			want: &exchangerPb.GetExchangerOrderByIdResponse{
				Status:         rpc.StatusInternalWithDebugMsg("error while trying to fetch exchangerOrder by exchangerOrderId. err - errorMessage"),
				ExchangerOrder: nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// setup mock calls
			tt.setupMocks()

			got, err := exchangerSvc.GetExchangerOrderById(context.Background(), tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetExchangerOrderById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetExchangerOrderById() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_IncrementExchangerOfferInventory(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockExchangerOfferInventoryDao := mockDao.NewMockIExchangerOfferInventoryDao(ctr)
	s := NewService(nil, nil, nil, nil, nil, nil, mockExchangerOfferInventoryDao, nil, nil, nil, nil, nil, nil, nil, nil, nil, dyconf, nil, nil, nil)

	exchangerOfferInventory1 := &exchangerPb.ExchangerOfferInventory{
		Id:             "exchanger-offer-inventory-id-1",
		TotalCount:     15,
		AvailableCount: 10,
	}
	error1 := errors.New("error in updating row")

	type args struct {
		ctx context.Context
		req *exchangerPb.IncrementExchangerOfferInventoryRequest
	}
	tests := []struct {
		name       string
		setupMocks func()
		args       args
		want       *exchangerPb.IncrementExchangerOfferInventoryResponse
		wantErr    bool
	}{
		{
			name: "should increment exchanger offer inventory successfully",
			setupMocks: func() {
				mockExchangerOfferInventoryDao.EXPECT().
					IncrementInventory(gomock.Any(), "exchanger-offer-inventory-id-1", int32(10)).
					Return(exchangerOfferInventory1, nil)
			},
			args: args{
				ctx: context.Background(),
				req: &exchangerPb.IncrementExchangerOfferInventoryRequest{
					ExchangerOfferInventoryId: "exchanger-offer-inventory-id-1",
					IncrementCount:            10,
				},
			},
			want: &exchangerPb.IncrementExchangerOfferInventoryResponse{
				Status:                  rpc.StatusOk(),
				ExchangerOfferInventory: exchangerOfferInventory1,
			},
		},
		{
			name: "should throw internal error when failure in dao call",
			setupMocks: func() {
				mockExchangerOfferInventoryDao.EXPECT().
					IncrementInventory(gomock.Any(), "exchanger-offer-inventory-id-2", int32(15)).
					Return(nil, error1)
			},
			args: args{
				ctx: context.Background(),
				req: &exchangerPb.IncrementExchangerOfferInventoryRequest{
					ExchangerOfferInventoryId: "exchanger-offer-inventory-id-2",
					IncrementCount:            15,
				},
			},
			want: &exchangerPb.IncrementExchangerOfferInventoryResponse{
				Status:                  rpc.StatusInternalWithDebugMsg(error1.Error()),
				ExchangerOfferInventory: nil,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMocks()

			got, err := s.IncrementExchangerOfferInventory(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("IncrementExchangerOfferInventory() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("IncrementExchangerOfferInventory() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetExchangerOffersByFilters(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	// init mocks
	mockListingDao := mockDao.NewMockIExchangerOfferListingDao(ctr)
	mockOfferDao := mockDao.NewMockIExchangerOfferDao(ctr)
	mockUserHelperSvc := mocks.NewMockIUserHelperService(ctr)

	listing1 := &exchangerPb.ExchangerOfferListing{
		ExchangerOfferId: "exchanger-offer-id-1",
		ActiveSince:      "2021-12-20T10:04:05+05:30",
		ActiveTill:       "2021-12-21T10:04:05+05:30",
		DisplaySince:     "2021-12-20T10:04:05+05:30",
		DisplayTill:      "2021-12-21T10:04:05+05:30",
	}
	listing2 := &exchangerPb.ExchangerOfferListing{
		ExchangerOfferId: "exchanger-offer-id-2",
		ActiveSince:      "2021-12-20T10:04:05+05:30",
		ActiveTill:       "2021-12-21T10:04:05+05:30",
		DisplaySince:     "2021-12-20T10:04:05+05:30",
		DisplayTill:      "2021-12-21T10:04:05+05:30",
	}

	offer1 := &exchangerPb.ExchangerOffer{
		Id:                 "exchanger-offer-id-1",
		RedemptionCurrency: exchangerPb.ExchangerOfferRedemptionCurrency_EXCHANGER_OFFER_REDEMPTION_CURRENCY_FI_COINS,
		RedemptionPrice:    100,
		OfferDisplayDetails: &exchangerPb.ExchangerOfferDisplayDetails{
			Title:       "Let your Fi-Coins pay for stuff",
			Subtitle:    "Trade your Fi-Coins for cash",
			Desc:        "Win exciting cashback rewards",
			ImageUrl:    "image-url-1",
			TileBgColor: "#111111",
		},
		OfferOptionsConfig: &exchangerPb.ExchangerOfferOptionsConfig{},
		OfferAggregatesConfig: &exchangerPb.ExchangerOfferAggregatesConfig{
			DailyAllowedAttemptsPerUser: 10,
		},
		TagsInfo: &casperPb.TagsInfo{Tags: []casperPb.TagName{casperPb.TagName_REDEMPTION_MECHANISM_CBR, casperPb.TagName_REWARD_TYPE_CASH}},
	}
	offer2 := &exchangerPb.ExchangerOffer{
		Id:                 "exchanger-offer-id-2",
		RedemptionCurrency: exchangerPb.ExchangerOfferRedemptionCurrency_EXCHANGER_OFFER_REDEMPTION_CURRENCY_FI_COINS,
		RedemptionPrice:    100,
		OfferDisplayDetails: &exchangerPb.ExchangerOfferDisplayDetails{
			Title:       "Let your Fi-Coins pay for stuff",
			Subtitle:    "Trade your Fi-Coins for cash",
			Desc:        "Win exciting cashback rewards",
			ImageUrl:    "image-url-1",
			TileBgColor: "#111111",
		},
		OfferOptionsConfig: &exchangerPb.ExchangerOfferOptionsConfig{},
		OfferAggregatesConfig: &exchangerPb.ExchangerOfferAggregatesConfig{
			DailyAllowedAttemptsPerUser: 10,
		},
		TagsInfo: &casperPb.TagsInfo{Tags: []casperPb.TagName{casperPb.TagName_REDEMPTION_MECHANISM_CBR, casperPb.TagName_REWARD_TYPE_EGV}},
	}

	getActiveOffersReq := &exchangerPb.GetExchangerOffersByFiltersRequest{
		ActorId: "actor-1",
	}

	getActiveOffersWithFiltersReq := &exchangerPb.GetExchangerOffersByFiltersRequest{
		ActorId: "actor-1",
		Filters: &exchangerPb.ExchangerOfferFilters{
			CatalogFilters: &casperPb.CatalogFilters{Tags: []casperPb.TagName{casperPb.TagName_REWARD_TYPE_CASH}},
		},
	}

	type args struct {
		req *exchangerPb.GetExchangerOffersByFiltersRequest
	}
	tests := []struct {
		name     string
		mockFunc func()
		args     args
		want     *exchangerPb.GetExchangerOffersByFiltersResponse
		wantErr  bool
	}{
		{
			name: "Successfully fetch active offers",
			mockFunc: func() {
				mockListingDao.EXPECT().GetListingsByFilters(gomock.Any(), gomock.Any()).Return([]*exchangerPb.ExchangerOfferListing{listing1, listing2}, nil)
				mockOfferDao.EXPECT().GetByIds(gomock.Any(), []string{listing1.GetExchangerOfferId(), listing2.GetExchangerOfferId()}, gomock.Any()).Return([]*exchangerPb.ExchangerOffer{offer1, offer2}, nil)
				mockUserHelperSvc.EXPECT().IsActorUserInternal(gomock.Any(), gomock.Any()).Return(true, nil).AnyTimes()
			},
			args: args{
				req: getActiveOffersReq,
			},
			want: &exchangerPb.GetExchangerOffersByFiltersResponse{
				Status:          rpc.StatusOk(),
				ExchangerOffers: []*exchangerPb.ExchangerOffer{offer1, offer2},
				OfferIdToListingMap: map[string]*exchangerPb.ExchangerOfferListing{
					"exchanger-offer-id-1": listing1,
					"exchanger-offer-id-2": listing2,
				},
			},
			wantErr: false,
		},
		{
			name: "Successfully fetch active offers with filters",
			mockFunc: func() {
				mockListingDao.EXPECT().GetListingsByFilters(gomock.Any(), gomock.Any()).Return([]*exchangerPb.ExchangerOfferListing{listing1, listing2}, nil)
				mockOfferDao.EXPECT().GetByIds(gomock.Any(), []string{listing1.GetExchangerOfferId(), listing2.GetExchangerOfferId()}, gomock.Any()).Return([]*exchangerPb.ExchangerOffer{offer1, offer2}, nil)
				mockUserHelperSvc.EXPECT().IsActorUserInternal(gomock.Any(), gomock.Any()).Return(true, nil).AnyTimes()
			},
			args: args{
				req: getActiveOffersWithFiltersReq,
			},
			want: &exchangerPb.GetExchangerOffersByFiltersResponse{
				Status:          rpc.StatusOk(),
				ExchangerOffers: []*exchangerPb.ExchangerOffer{offer1},
				OfferIdToListingMap: map[string]*exchangerPb.ExchangerOfferListing{
					"exchanger-offer-id-1": listing1,
					"exchanger-offer-id-2": listing2,
				},
			},
			wantErr: false,
		},
		{
			name: "Empty actor id must successfully fetch all active offers",
			mockFunc: func() {
				mockListingDao.EXPECT().GetListingsByFilters(gomock.Any(), gomock.Any()).Return([]*exchangerPb.ExchangerOfferListing{listing1, listing2}, nil)
				mockOfferDao.EXPECT().GetByIds(gomock.Any(), []string{listing1.GetExchangerOfferId(), listing2.GetExchangerOfferId()}, gomock.Any()).Return([]*exchangerPb.ExchangerOffer{offer1, offer2}, nil)
			},
			args: args{
				req: &exchangerPb.GetExchangerOffersByFiltersRequest{},
			},
			want: &exchangerPb.GetExchangerOffersByFiltersResponse{
				Status:          rpc.StatusOk(),
				ExchangerOffers: []*exchangerPb.ExchangerOffer{offer1, offer2},
				OfferIdToListingMap: map[string]*exchangerPb.ExchangerOfferListing{
					"exchanger-offer-id-1": listing1,
					"exchanger-offer-id-2": listing2,
				},
			},
			wantErr: false,
		},
		{
			name: "fetch active listing dao call failed",
			mockFunc: func() {
				mockListingDao.EXPECT().GetListingsByFilters(gomock.Any(), gomock.Any()).Return(nil, errors.New("error fetching active listings"))
			},
			args: args{
				req: getActiveOffersReq,
			},
			want: &exchangerPb.GetExchangerOffersByFiltersResponse{
				Status: rpc.StatusInternalWithDebugMsg("error fetching active listings"),
			},
			wantErr: false,
		},
		{
			name: "fetch offers by ids dao call failed",
			mockFunc: func() {
				mockListingDao.EXPECT().GetListingsByFilters(gomock.Any(), gomock.Any()).Return([]*exchangerPb.ExchangerOfferListing{listing1, listing2}, nil)
				mockOfferDao.EXPECT().GetByIds(gomock.Any(), []string{listing1.GetExchangerOfferId(), listing2.GetExchangerOfferId()}, gomock.Any()).Return(nil, errors.New("error fetching offers"))
			},
			args: args{
				req: getActiveOffersReq,
			},
			want: &exchangerPb.GetExchangerOffersByFiltersResponse{
				Status: rpc.StatusInternalWithDebugMsg("error fetching offers"),
			},
			wantErr: false,
		},
	}
	// nolint: scopelint
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// setup mock calls
			tt.mockFunc()
			exchangerSvc := NewService(mockListingDao, mockOfferDao, nil, nil, nil, nil, nil, nil, nil, nil, nil, mockUserHelperSvc, nil, nil, nil, nil, dyconf, nil, nil, nil)
			got, err := exchangerSvc.GetExchangerOffersByFilters(context.Background(), tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetExchangerOffersByFiltersResponse() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetExchangerOffersByFiltersResponse() got = %v, want %v", got, tt.want)
			}
		})
	}
}
