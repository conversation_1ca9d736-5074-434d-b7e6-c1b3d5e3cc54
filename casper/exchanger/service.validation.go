package exchanger

import (
	"context"
	"fmt"
	"strings"

	"google.golang.org/protobuf/encoding/protojson"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"

	"github.com/epifi/gamma/api/casper"
	exchangerPb "github.com/epifi/gamma/api/casper/exchanger"
	segmentPb "github.com/epifi/gamma/api/segment"
	"github.com/epifi/gamma/casper/config/genconf"
	"github.com/epifi/gamma/casper/constants"
)

// validateExchangerOfferCreatePayload validates the payload for exchanger offer creation
func (s *Service) validateExchangerOfferCreatePayload(ctx context.Context, payload *exchangerPb.CreateExchangerOfferRequest) error {
	currEnv, err := cfg.GetEnvironment()
	if err != nil {
		return fmt.Errorf("error fetching environment: %w", err)
	}
	// not performing the validations for test env
	if cfg.IsTestEnv(currEnv) {
		return nil
	}

	payloadJson, err := protojson.Marshal(payload)
	if err != nil {
		return fmt.Errorf("error in marshalling CreateExchangerOfferRequest, err: %s", err)
	} else if strings.Contains(string(payloadJson), constants.DeprecatedBasePathForVisualAssets) {
		return fmt.Errorf("base path for visual assets is deprecated, please use %s", constants.BasePathForVisualAssets)
	}

	if err := s.validateExchangerOfferAggregatesConfig(ctx, payload.GetOfferAggregatesConfig()); err != nil {
		return fmt.Errorf("offer aggregates config validation failed: %w", err)
	}

	if err := s.validateExchangerOfferOptionsConfig(ctx, payload.GetOfferOptionsConfig()); err != nil {
		return fmt.Errorf("offer options config validation failed: %w", err)
	}

	if err := s.validateExchangerOfferGroup(ctx, payload.GetGroupId()); err != nil {
		return fmt.Errorf("group validation failed for the offer: %w", err)
	}

	if err := s.validateExchangerOfferSegmentExpression(ctx, payload.GetOfferDisplayDetails().GetSegmentExpression()); err != nil {
		return fmt.Errorf("validation for segmentExpression failed, err: %w", err)
	}

	if err := s.validateExchangerOfferTagsConfig(payload.GetRedemptionCurrency(), payload.GetManualTags(), payload.GetCategoryTag(), payload.GetSubCategoryTag()); err != nil {
		return fmt.Errorf("offer tags config validation failed: %w", err)
	}

	if err := s.validateAdditionalDetails(payload.GetRedemptionCurrency(), payload.GetAdditionalDetails()); err != nil {
		return fmt.Errorf("additional data validation failed: %w", err)
	}

	return nil
}

func (s *Service) validateExchangerOfferAggregatesConfig(_ context.Context, aggregatesConf *exchangerPb.ExchangerOfferAggregatesConfig) error {
	if aggregatesConf.GetDailyAllowedAttemptsPerUser() > s.dyconf.PayloadValidationInfo().ExchangerOfferValidationInfo().MaxAllowedDailyAttemptsPerUser() {
		return fmt.Errorf("DailyAllowedAttemptsPerUser exceeds the allowed threshold: %d", s.dyconf.PayloadValidationInfo().ExchangerOfferValidationInfo().MaxAllowedDailyAttemptsPerUser())
	}

	if aggregatesConf.GetUserLevelMonthlyRedemptionCap() > s.dyconf.PayloadValidationInfo().ExchangerOfferValidationInfo().UserLevelMonthlyRedemptionCapThreshold() {
		return fmt.Errorf("UserLevelMonthlyRedemptionCap exceeds the allowed threshold: %d", s.dyconf.PayloadValidationInfo().ExchangerOfferValidationInfo().UserLevelMonthlyRedemptionCapThreshold())
	}

	return nil
}

// validateExchangerOfferOptionsConfig validate the offer options config for each reward type
func (s *Service) validateExchangerOfferOptionsConfig(ctx context.Context, optionsConf *exchangerPb.ExchangerOfferOptionsConfig) error {
	for _, optionConfig := range optionsConf.GetOptionsConfig() {
		for _, rewardConfigUnit := range optionConfig.GetRewardConfigUnits() {
			switch rewardConfigUnit.GetRewardType() {
			case exchangerPb.RewardType_REWARD_TYPE_CASH:
				return s.validateCashRewardConfigUnit(ctx, rewardConfigUnit)
			case exchangerPb.RewardType_REWARD_TYPE_EGV:
				return s.validateEgvRewardConfigUnit(ctx, rewardConfigUnit)
			case exchangerPb.RewardType_REWARD_TYPE_PHYSICAL_MERCHANDISE:
				// todo: check for physical merch value and the inventory existence once merch value field is added
			default:
				// do nothing for now, i.e. no validations
			}
		}
	}

	return nil
}

// validateExchangerOfferSegmentExpression validate the segmentExpression in the offer
func (s *Service) validateExchangerOfferSegmentExpression(ctx context.Context, segmentExpression string) error {
	if segmentExpression == "" {
		return nil
	}
	isMemberOfExpressionsRes, err := s.segmentationServiceClient.IsMemberOfExpressions(ctx, &segmentPb.IsMemberOfExpressionsRequest{
		ActorId:              "",
		SegmentIdExpressions: []string{segmentExpression},
		LatestBy:             timestampPb.Now(),
	})
	if rpcErr := epifigrpc.RPCError(isMemberOfExpressionsRes, err); rpcErr != nil {
		return rpcErr
	}
	if isMemberOfExpressionsRes.GetSegmentExpressionMembershipMap()[segmentExpression].GetSegmentExpressionStatus() != segmentPb.SegmentExpressionStatus_OK {
		return fmt.Errorf("error in given segmentExpression")
	}
	return nil
}

// validateExchangerOfferGroup validates the presence of exchanger-offer-group for the given id
func (s *Service) validateExchangerOfferGroup(ctx context.Context, groupId string) error {
	if groupId == "" {
		return nil
	}

	eoGroups, err := s.groupsDao.GetGroupsByIds(ctx, []string{groupId})
	if err != nil || len(eoGroups) == 0 {
		return fmt.Errorf("error fetching exchanger-offer-group by the id: %s, err: %w, total-groups found: %d", groupId, err, len(eoGroups))
	}

	return nil
}

// validateCashRewardConfigUnit validates the cash reward config unit to make sure that the values are within the threshold for cash rewards
func (s *Service) validateCashRewardConfigUnit(_ context.Context, rewardConfigUnit *exchangerPb.ExchangerOfferOptionConfig_RewardConfigUnit) error {
	switch rewardConfigUnit.RewardUnitsConfig.(type) {
	case *exchangerPb.ExchangerOfferOptionConfig_RewardConfigUnit_RangeProbabilityConfig:
		rangeProbabilityConf := rewardConfigUnit.GetRangeProbabilityConfig()
		for _, configUnit := range rangeProbabilityConf.GetConfigUnits() {
			var configCheckErr error
			s.dyconf.PayloadValidationInfo().ExchangerOfferValidationInfo().CashThresholdConfigsMap().Range(func(_ string, conf *genconf.EoCashRewardUnitsConfigThreshold) bool {
				if !conf.IsEnabled() {
					return true
				}
				if configUnit.GetStart() < conf.Amount() {
					return true
				}

				// todo: check for fi-coins conversion ratio as well
				if int32(configUnit.GetPercentage()*10000) > int32(conf.MaxPercentageThresholdForAmount()*10000) {
					configCheckErr = fmt.Errorf("allowed cash config percentage: %f", conf.MaxPercentageThresholdForAmount())
					return false
				}

				return true
			})

			if configCheckErr != nil {
				return fmt.Errorf("cash config unit exceeds the threshold for the value: %d, err: %w", configUnit.GetStart(), configCheckErr)
			}
		}

	default:
		return fmt.Errorf("reward units config for cash not defined")
	}

	return nil
}

// validateEgvRewardConfigUnit validates the egv card value to make sure that it's within the given threshold
func (s *Service) validateEgvRewardConfigUnit(ctx context.Context, rewardConfigUnit *exchangerPb.ExchangerOfferOptionConfig_RewardConfigUnit) error {
	if rewardConfigUnit.GetExchangerOfferInventoryId() == "" {
		return fmt.Errorf("no inventory-id set for the EGV reward config unit")
	}
	if err := s.validateExchangerOfferInventory(ctx, rewardConfigUnit.GetExchangerOfferInventoryId()); err != nil {
		return fmt.Errorf("validation failed for exchanger offer inventory: %w", err)
	}

	switch rewardConfigUnit.RewardTypeSpecificConfig.(type) {
	case *exchangerPb.ExchangerOfferOptionConfig_RewardConfigUnit_EgvRewardConfig:
		egvSpecificConfig := rewardConfigUnit.GetEgvRewardConfig()
		var configCheckErr error
		s.dyconf.PayloadValidationInfo().ExchangerOfferValidationInfo().EgvThresholdConfigsMap().Range(func(_ string, conf *genconf.EoEgvRewardUnitsConfigThreshold) bool {
			if !conf.IsEnabled() {
				return true
			}
			if egvSpecificConfig.GetGiftCardValue() < int32(conf.Value()) {
				return true
			}

			// todo: check for fi-coins conversion ratio as well
			if int32(rewardConfigUnit.GetPercentage()*10000) > int32(conf.MaxPercentageThresholdForValue()*10000) {
				configCheckErr = fmt.Errorf("allowed EGV config percentage: %f", conf.MaxPercentageThresholdForValue())
				return false
			}

			return true
		})

		if configCheckErr != nil {
			return fmt.Errorf("egv config unit exceeds the threshold for the value: %d, err: %w", egvSpecificConfig.GetGiftCardValue(), configCheckErr)
		}
	default:
		return fmt.Errorf("egv reward config not defined under RewardTypeSpecificConfig")
	}

	return nil
}

// validateExchangerOfferInventory validates the presence of exchanger offer reward item inventory
func (s *Service) validateExchangerOfferInventory(ctx context.Context, inventoryId string) error {
	if inventoryId == "" {
		return nil
	}

	_, err := s.inventoryDao.GetById(ctx, inventoryId)
	if err != nil {
		return fmt.Errorf("error fetching exchanger-offer inventory by the id: %s, err: %w", inventoryId, err)
	}

	return nil
}

func (s *Service) validateUpdateExchangerOfferDisplayPayload(payload *exchangerPb.UpdateExchangerOfferDisplayRequest) error {
	currEnv, err := cfg.GetEnvironment()
	if err != nil {
		return fmt.Errorf("error fetching environment: %w", err)
	}
	// not performing the validations for test env
	if cfg.IsTestEnv(currEnv) {
		return nil
	}

	payloadJson, err := protojson.Marshal(payload)
	if err != nil {
		return fmt.Errorf("error in marshalling UpdateExchangerOfferDisplayRequest, err: %s", err)
	} else if strings.Contains(string(payloadJson), constants.DeprecatedBasePathForVisualAssets) {
		return fmt.Errorf("base path for visual assets is deprecated, please use %s", constants.BasePathForVisualAssets)
	}

	// sending currency as fi coins as that is the only currency in use currency so that if additional details is nil,
	// the is_fi_points bool should be set
	// todo: think of a better way to do these validations
	if err := s.validateAdditionalDetails(exchangerPb.ExchangerOfferRedemptionCurrency_EXCHANGER_OFFER_REDEMPTION_CURRENCY_FI_COINS, payload.GetNewAdditionalDetails()); err != nil {
		return fmt.Errorf("additional data validation failed: %w", err)
	}

	return nil
}

// nolint:unparam
func (s *Service) validateExchangerOfferTagsConfig(redemptionCurrency exchangerPb.ExchangerOfferRedemptionCurrency, manualTags []casper.TagName, categoryTag casper.CategoryTag, subCategoryTag casper.SubCategoryTag) error {
	if redemptionCurrency == exchangerPb.ExchangerOfferRedemptionCurrency_EXCHANGER_OFFER_REDEMPTION_CURRENCY_FI_COINS && categoryTag == casper.CategoryTag_CATEGORY_TAG_UNSPECIFIED {
		return fmt.Errorf("category tag should not be unspecified when offer redemption currency is fi coins")
	}
	return nil
}

func (s *Service) validateAdditionalDetails(redemptionCurrency exchangerPb.ExchangerOfferRedemptionCurrency, additionalDetails *exchangerPb.ExchangerOfferAdditionalDetails) error {
	if additionalDetails == nil {
		return nil
	}

	if redemptionCurrency == exchangerPb.ExchangerOfferRedemptionCurrency_EXCHANGER_OFFER_REDEMPTION_CURRENCY_FI_COINS && !additionalDetails.GetIsFiPoints().ToBool() {
		return fmt.Errorf("is fi points should be set when offer redemption currency is fi coins")
	}

	return nil
}
