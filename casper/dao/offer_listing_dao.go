package dao

import (
	"context"
	"fmt"
	"time"

	pkgTypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext"
	gormctxV2 "github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	casperPb "github.com/epifi/gamma/api/casper"
	"github.com/epifi/gamma/casper/dao/model"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	gormv2 "gorm.io/gorm"
)

var _ OfferListingDao = &OfferListingDaoImpl{}

type OfferListingDaoImpl struct {
	DB *gormv2.DB
}

func NewOfferListingDao(db pkgTypes.CasperPGDB) *OfferListingDaoImpl {
	return &OfferListingDaoImpl{DB: db}
}

func (o *OfferListingDaoImpl) CreateOfferListing(ctx context.Context, req *casperPb.CreateOfferListingRequest) (string, error) {
	defer metric_util.TrackDuration("casper/dao", "OfferListingDaoImpl", "CreateOfferListing", time.Now())
	// TODO(Vikas): Move to db lvl uuid creation or using idgen
	offerListingId := uuid.New().String()
	offerListing, err := model.NewOfferListing(req)
	if err != nil {
		return "", errors.Wrap(err, "unable to create listing model")
	}
	offerListing.Id = offerListingId
	db := o.DB.Session(&gormv2.Session{Context: epificontext.CloneCtx(ctx)})
	if err := db.Create(offerListing).Error; err != nil {
		return "", errors.Wrap(err, "failed to create offer listing")
	}
	return offerListingId, nil
}

func (o *OfferListingDaoImpl) UpdateOfferListing(ctx context.Context, req *casperPb.UpdateOfferListingRequest) error {
	defer metric_util.TrackDuration("casper/dao", "OfferListingDaoImpl", "UpdateOfferListing", time.Now())
	updatedOfferListing, err := model.GetUpdateOfferListing(req)
	if err != nil {
		return errors.Wrap(err, "unable to create listing model")
	}
	db := o.DB.Session(&gormv2.Session{Context: epificontext.CloneCtx(ctx)})
	res := db.Model(&model.OfferListing{}).Where("id=?", req.OfferListingId).Updates(updatedOfferListing)
	if res.Error != nil {
		return errors.Wrap(res.Error, "failed to update offer listing is deleted by id")
	}
	if res.RowsAffected == 0 {
		return errors.New("offer listing updated rows are 0")
	}
	return nil
}

// nolint:dupl
func (o *OfferListingDaoImpl) DeleteOfferListingById(ctx context.Context, id string) error {
	defer metric_util.TrackDuration("casper/dao", "OfferListingDaoImpl", "DeleteOfferListingById", time.Now())
	updatedOfferListing := &model.OfferListing{
		IsDeleted: true,
	}
	db := o.DB.Session(&gormv2.Session{Context: epificontext.CloneCtx(ctx)})
	res := db.Model(&model.OfferListing{}).Where("id=?", id).Updates(updatedOfferListing)
	if res.Error != nil {
		return errors.Wrap(res.Error, "failed to update offer listing is deleted by id")
	}
	if res.RowsAffected == 0 {
		return errors.New("offer listing updated rows are 0")
	}
	return nil
}

// nolint:dupl
func (o *OfferListingDaoImpl) GetOfferListingById(ctx context.Context, id string) (*casperPb.OfferListing, error) {
	defer metric_util.TrackDuration("casper/dao", "OfferListingDaoImpl", "GetOfferListingById", time.Now())
	offerListing := &model.OfferListing{}
	db := o.DB.Session(&gormv2.Session{Context: epificontext.CloneCtx(ctx)})
	res := db.First(offerListing, "id=?", id)
	if errors.Is(res.Error, gormv2.ErrRecordNotFound) {
		return nil, errors.New("no listing found by given id")
	}
	if res.Error != nil {
		return nil, errors.Wrap(res.Error, "failed to fetch offer listing by id")
	}
	return offerListing.GetProtoOfferListing(), nil
}

func (o *OfferListingDaoImpl) GetOfferListingsByOfferId(ctx context.Context, offerId string, filters *model.GetOfferListingsFilters) ([]*casperPb.OfferListing, error) {
	defer metric_util.TrackDuration("casper/dao", "OfferListingDaoImpl", "GetOfferListingsByOfferId", time.Now())
	if offerId == "" {
		return nil, fmt.Errorf("offerId param cannot be empty")
	}
	db := gormctxV2.FromContextOrDefault(ctx, o.DB)

	query := db.Where("offer_id=?", offerId)
	query = query.Where("is_deleted=?", filters.IsDeleted)

	if !filters.ActiveSince.IsZero() {
		query = query.Where("active_since <= ?", filters.ActiveSince)
	}
	if !filters.ActiveTill.IsZero() {
		query = query.Where("active_till > ?", filters.ActiveTill)
	}
	if !filters.DisplaySince.IsZero() {
		query = query.Where("display_since <= ?", filters.DisplaySince)
	}
	if !filters.DisplayTill.IsZero() {
		query = query.Where("display_till > ?", filters.DisplayTill)
	}

	query = query.Order("display_till desc")

	var offerListings []*model.OfferListing
	if err := query.Find(&offerListings).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch offer listings by offer id. err: %w", err)
	}
	if len(offerListings) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}

	offerListingProtos := make([]*casperPb.OfferListing, 0, len(offerListings))
	for _, offerListing := range offerListings {
		offerListingProto := offerListing.GetProtoOfferListing()
		offerListingProtos = append(offerListingProtos, offerListingProto)
	}

	return offerListingProtos, nil
}

func (o *OfferListingDaoImpl) GetActiveOfferListings(ctx context.Context) ([]*casperPb.OfferListing, error) {
	defer metric_util.TrackDuration("casper/dao", "OfferListingDaoImpl", "GetActiveOfferListings", time.Now())
	db := o.DB.Session(&gormv2.Session{Context: epificontext.CloneCtx(ctx)})
	query := db.Where("is_deleted=?", false)
	query = query.Where("display_since<=?", time.Now())
	query = query.Where("display_till>?", time.Now())
	var offerListings []*model.OfferListing
	if err := query.Find(&offerListings).Error; err != nil {
		return nil, errors.Wrap(err, "unable to fetch active offer listings")
	}
	var protoOfferListings []*casperPb.OfferListing
	for _, offerListing := range offerListings {
		protoOfferListings = append(protoOfferListings, offerListing.GetProtoOfferListing())
	}
	return protoOfferListings, nil
}

func (o *OfferListingDaoImpl) GetActiveOfferListingsByFilters(ctx context.Context, filters *model.GetActiveOfferListingFilters) (map[casperPb.OfferRedemptionMode][]*casperPb.OfferListing, error) {
	defer metric_util.TrackDuration("casper/dao", "OfferListingDaoImpl", "GetActiveOfferListingsByFilters", time.Now())
	if filters == nil || len(filters.RedemptionModes) == 0 {
		return nil, fmt.Errorf("all filters can't be empty")
	}

	type offerAndOfferListingModel struct {
		*model.OfferListing
		RedemptionMode casperPb.OfferRedemptionMode
	}

	db := gormctxV2.FromContextOrDefault(ctx, o.DB)

	query := db.Table("offer_listings as ol").
		// selecting only columns of offer_listings table
		Select("ol.*", "offers.redemption_mode").
		Joins("INNER JOIN offers on ol.offer_id = offers.id").
		// querying only active offer listings
		Where("ol.display_since <= ? and ol.display_till > ? and ol.is_deleted = ?", time.Now(), time.Now(), false)

	if len(filters.RedemptionModes) != 0 {
		query.Where("offers.redemption_mode in (?)", filters.RedemptionModes)
	}

	var offerAndOfferListingModelList []*offerAndOfferListingModel
	if err := query.Find(&offerAndOfferListingModelList).Error; err != nil {
		return nil, fmt.Errorf("unable to fetch active offer listings by filters, err: %w", err)
	}

	protoOfferListings := make(map[casperPb.OfferRedemptionMode][]*casperPb.OfferListing, len(offerAndOfferListingModelList))
	for _, offerAndOfferListing := range offerAndOfferListingModelList {
		protoOfferListings[offerAndOfferListing.RedemptionMode] = append(protoOfferListings[offerAndOfferListing.RedemptionMode], offerAndOfferListing.GetProtoOfferListing())
	}
	return protoOfferListings, nil
}

func (o *OfferListingDaoImpl) GetOfferListingsByFilters(ctx context.Context, filters *casperPb.OfferListingsFilters) ([]*casperPb.OfferListing, error) {
	defer metric_util.TrackDuration("casper/dao", "OfferListingDaoImpl", "GetOfferListingsByFilters", time.Now())
	if filters == nil || filters.GetToDate() == nil {
		return nil, fmt.Errorf("all filters can't be empty")
	}

	db := gormctxV2.FromContextOrDefault(ctx, o.DB)
	db = db.Table("offer_listings as ol").
		// selecting only columns of offer_listings table
		Select("ol.*").
		Joins("INNER JOIN offers on ol.offer_id = offers.id").
		// querying only offer listings which are in given time range
		Where("ol.display_till > ? and ol.is_deleted = ?", filters.GetToDate().AsTime(), false)

	if filters.GetFromDate() != nil {
		db = db.Where("ol.display_since <= ?", filters.GetFromDate().AsTime())
	}
	if len(filters.GetRedemptionModes()) != 0 {
		db = db.Where("offers.redemption_mode in (?)", filters.GetRedemptionModes())
	}
	db = db.Order("display_till desc")
	var offerListings []*model.OfferListing
	if err := db.Find(&offerListings).Error; err != nil {
		return nil, fmt.Errorf("unable to fetch active offer listings by filters, err: %w", err)
	}

	protoOfferListings := make([]*casperPb.OfferListing, len(offerListings))
	for i, offerListing := range offerListings {
		protoOfferListings[i] = offerListing.GetProtoOfferListing()
	}
	return protoOfferListings, nil
}
