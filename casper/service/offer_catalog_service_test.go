package service

import (
	"context"
	"errors"
	"os"
	"reflect"
	"sort"
	"sync"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/async/waitgroup"

	casperPb "github.com/epifi/gamma/api/casper"

	"github.com/epifi/gamma/casper/config/genconf"
	"github.com/epifi/gamma/casper/dao"
	"github.com/epifi/gamma/casper/test"
	"github.com/epifi/gamma/casper/test/mocks"
	daoMock "github.com/epifi/gamma/casper/test/mocks/dao"
	slackHelperMocks "github.com/epifi/gamma/casper/test/mocks/helper/slack_helper"
)

var dyconf *genconf.Config

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	var teardown func()
	_, dyconf, _, teardown = test.InitTestServer(false)
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

func TestOfferCatalogService_CreateOffer(t *testing.T) {
	req := &casperPb.CreateOfferRequest{
		Name:  "offer name",
		Desc:  "offer desc",
		Price: 100,
		Images: []*casperPb.OfferImage{{
			ImageType: casperPb.ImageType_BACKGROUND_IMAGE,
			Url:       "background url",
		}},
		Tnc: &casperPb.OfferTnc{
			TncList: []string{"tnc1", "tnc2"},
		},
		RedemptionMode: casperPb.OfferRedemptionMode_FI_COINS,
		VendorName:     casperPb.OfferVendor_LOYLTY,
		OfferType:      casperPb.OfferType_GIFT_CARD,
		VendorOfferMetadata: &casperPb.VendorOfferMetadata{
			VendorOfferMetadata: &casperPb.VendorOfferMetadata_LoyltyVendorOfferMetadata{
				LoyltyVendorOfferMetadata: &casperPb.LoyltyVendorOfferMetadata{
					SkuId:     "vendor-sku-id",
					ProductId: "vendor-offer-id",
				}}},
		OfferMetadata: &casperPb.OfferMetadata{
			OfferTypeSpecificMetadata: &casperPb.OfferMetadata_GiftCardMetadata{
				GiftCardMetadata: &casperPb.GiftCardOfferMetadata{GiftCardValue: 5},
			},
		},
		CategoryTag: casperPb.CategoryTag_CATEGORY_TAG_VOUCHERS,
		OfferAdditionalDetails: &casperPb.OfferAdditionalDetails{
			IsFiPoints: commontypes.BooleanEnum_TRUE,
		},
	}

	type args struct {
		ctx context.Context
		req *casperPb.CreateOfferRequest
	}
	tests := []struct {
		name       string
		setupMocks func(mockOfferCatalogDao *daoMock.MockOfferCatalogDao, mockSlackHelperSvc *slackHelperMocks.MockISlackHelperSvc, wg *sync.WaitGroup)
		args       args
		want       *casperPb.CreateOfferResponse
		wantErr    bool
	}{
		{
			name: "offer creation failed at db",
			setupMocks: func(mockOfferCatalogDao *daoMock.MockOfferCatalogDao, mockSlackHelperSvc *slackHelperMocks.MockISlackHelperSvc, wg *sync.WaitGroup) {
				mockOfferCatalogDao.EXPECT().CreateOffer(gomock.Any(), gomock.Any()).Return("", errors.New("offer creation failed"))
			},
			args: args{
				ctx: context.Background(),
				req: req,
			},
			want: &casperPb.CreateOfferResponse{
				Status: rpc.StatusInternalWithDebugMsg("offer creation failed"),
			},
			wantErr: false,
		},
		{
			name: "offer creation successful",
			setupMocks: func(mockOfferCatalogDao *daoMock.MockOfferCatalogDao, mockSlackHelperSvc *slackHelperMocks.MockISlackHelperSvc, wg *sync.WaitGroup) {
				wg.Add(1)
				mockOfferCatalogDao.EXPECT().CreateOffer(gomock.Any(), gomock.Any()).Return("offer-id", nil)
				mockOfferCatalogDao.EXPECT().
					GetOfferDetailsById(gomock.Any(), "offer-id").
					Return(&casperPb.Offer{Id: "offer-id"}, nil)
				mockSlackHelperSvc.EXPECT().
					SendMessage(gomock.Any(), gomock.Any(), gomock.Any(), &casperPb.Offer{Id: "offer-id"}).
					DoAndReturn(func(arg0, arg1, arg2, arg3 interface{}) (r0 interface{}) {
						wg.Done()
						return nil
					}).Times(1)
			},
			args: args{
				ctx: context.Background(),
				req: req,
			},
			want: &casperPb.CreateOfferResponse{
				Status:  rpc.StatusOk(),
				OfferId: "offer-id",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			mockOfferCatalogDao := daoMock.NewMockOfferCatalogDao(ctr)
			mockSlackHelperSvc := slackHelperMocks.NewMockISlackHelperSvc(ctr)
			defer ctr.Finish()

			var wg sync.WaitGroup
			defer waitgroup.SafeWaitWithDefaultTimeout(&wg)

			o := OfferCatalogService{
				catalogDao:  mockOfferCatalogDao,
				slackHelper: mockSlackHelperSvc,
				dyconf:      dyconf,
			}
			tt.setupMocks(mockOfferCatalogDao, mockSlackHelperSvc, &wg)

			got, err := o.CreateOffer(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateOffer() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CreateOffer() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestOfferCatalogService_DeleteOfferById(t *testing.T) {
	ctr := gomock.NewController(t)
	mockOfferCatalogDao := daoMock.NewMockOfferCatalogDao(ctr)
	defer ctr.Finish()

	req := &casperPb.DeleteOfferByIdRequest{
		OfferId: "offer-id",
	}
	type fields struct {
		catalogDao dao.OfferCatalogDao
	}
	type args struct {
		daoMock interface{}
		ctx     context.Context
		req     *casperPb.DeleteOfferByIdRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *casperPb.DeleteOfferByIdResponse
		wantErr bool
	}{
		{
			name:   "offer deletion failed at db",
			fields: fields{catalogDao: mockOfferCatalogDao},
			args: args{
				daoMock: mockOfferCatalogDao.EXPECT().DeleteOfferById(gomock.Any(), gomock.Any()).Return(errors.New("offer deletion failed")),
				ctx:     context.Background(),
				req:     req,
			},
			want: &casperPb.DeleteOfferByIdResponse{
				Status: rpc.StatusInternalWithDebugMsg("offer deletion failed"),
			},
			wantErr: false,
		},
		{
			name:   "offer deletion successful",
			fields: fields{catalogDao: mockOfferCatalogDao},
			args: args{
				daoMock: mockOfferCatalogDao.EXPECT().DeleteOfferById(gomock.Any(), gomock.Any()).Return(nil),
				ctx:     context.Background(),
				req:     req,
			},
			want: &casperPb.DeleteOfferByIdResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := OfferCatalogService{
				catalogDao: tt.fields.catalogDao,
				dyconf:     dyconf,
			}
			got, err := o.DeleteOfferById(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("DeleteOfferById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("DeleteOfferById() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestOfferCatalogService_GetOfferDetailsById(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockOfferCatalogDao := daoMock.NewMockOfferCatalogDao(ctr)
	mockVendor := mocks.NewMockIVendor(ctr)
	mockVendorFactory := mocks.NewMockIVendorFactory(ctr)
	mockVendorFactory.EXPECT().Get(gomock.Any()).AnyTimes().Return(mockVendor, nil)

	offer1 := &casperPb.Offer{
		Id:         "offer-id-1",
		Name:       "offer name",
		Desc:       "offer desc",
		Price:      100,
		VendorName: casperPb.OfferVendor_LOYLTY,
		OfferType:  casperPb.OfferType_GIFT_CARD,
		OfferMetadata: &casperPb.OfferMetadata{
			OfferTypeSpecificMetadata: &casperPb.OfferMetadata_GiftCardMetadata{
				GiftCardMetadata: &casperPb.GiftCardOfferMetadata{GiftCardValue: 5},
			},
		},
		Images: []*casperPb.OfferImage{{
			ImageType: casperPb.ImageType_BACKGROUND_IMAGE,
			Url:       "background url",
		}},
		Tnc: &casperPb.OfferTnc{
			TncList: []string{"tnc1", "tnc2"},
		},
		IsDeleted: false,
	}

	// offer not linked to any vendor
	offer2 := &casperPb.Offer{
		Id:         "offer-id-2",
		Name:       "offer name",
		Desc:       "offer desc",
		Price:      100,
		VendorName: casperPb.OfferVendor_UNSPECIFIED,
		OfferType:  casperPb.OfferType_CHARITY,
		OfferMetadata: &casperPb.OfferMetadata{
			OfferTypeSpecificMetadata: &casperPb.OfferMetadata_GiftCardMetadata{
				GiftCardMetadata: &casperPb.GiftCardOfferMetadata{GiftCardValue: 5},
			},
		},
		Images: []*casperPb.OfferImage{{
			ImageType: casperPb.ImageType_BACKGROUND_IMAGE,
			Url:       "background url",
		}},
		Tnc: &casperPb.OfferTnc{
			TncList: []string{"tnc1", "tnc2"},
		},
		IsDeleted: false,
	}

	type fields struct {
		catalogDao dao.OfferCatalogDao
	}
	type args struct {
		mockFunc func()
		ctx      context.Context
		req      *casperPb.GetOfferDetailsByIdRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *casperPb.GetOfferDetailsByIdResponse
		wantErr bool
	}{
		{
			name:   "get offer by id failed at db",
			fields: fields{catalogDao: mockOfferCatalogDao},
			args: args{
				mockFunc: func() {
					mockOfferCatalogDao.EXPECT().GetOfferDetailsById(gomock.Any(), offer1.GetId()).Return(nil, errors.New("error while fetching offer details"))
				},
				ctx: context.Background(),
				req: &casperPb.GetOfferDetailsByIdRequest{
					OfferId: offer1.GetId(),
				},
			},
			want: &casperPb.GetOfferDetailsByIdResponse{
				Status: rpc.StatusInternalWithDebugMsg("error while fetching offer details"),
			},
			wantErr: false,
		},
		{
			name:   "error during realtime vendor offer details update",
			fields: fields{catalogDao: mockOfferCatalogDao},
			args: args{
				mockFunc: func() {
					offers := []*casperPb.Offer{offer1}
					mockOfferCatalogDao.EXPECT().GetOfferDetailsById(gomock.Any(), offer1.GetId()).Return(offer1, nil)
					mockVendor.EXPECT().GetOfferWithUpdatedVendorOfferDetails(gomock.Any(), offers).Return(nil, errors.New("error getting update offer details"))
				},
				ctx: context.Background(),
				req: &casperPb.GetOfferDetailsByIdRequest{
					OfferId: offer1.GetId(),
				},
			},
			want: &casperPb.GetOfferDetailsByIdResponse{
				Status: rpc.StatusInternalWithDebugMsg("error getting updated offer details from vendor"),
			},
			wantErr: false,
		},
		{
			name:   "get offer by id successful (offer not linked to any vendor)",
			fields: fields{catalogDao: mockOfferCatalogDao},
			args: args{
				mockFunc: func() {
					mockOfferCatalogDao.EXPECT().GetOfferDetailsById(gomock.Any(), offer2.GetId()).Return(offer2, nil)
				},
				ctx: context.Background(),
				req: &casperPb.GetOfferDetailsByIdRequest{
					OfferId: offer2.GetId(),
				},
			},
			want: &casperPb.GetOfferDetailsByIdResponse{
				Status: rpc.StatusOk(),
				Offer:  offer2,
			},
			wantErr: false,
		},
		{
			name:   "get offer by id successful",
			fields: fields{catalogDao: mockOfferCatalogDao},
			args: args{
				mockFunc: func() {
					offers := []*casperPb.Offer{offer1}
					mockOfferCatalogDao.EXPECT().GetOfferDetailsById(gomock.Any(), offer1.GetId()).Return(offer1, nil)
					mockVendor.EXPECT().GetOfferWithUpdatedVendorOfferDetails(gomock.Any(), offers).Return(offers, nil)
				},
				ctx: context.Background(),
				req: &casperPb.GetOfferDetailsByIdRequest{
					OfferId: offer1.GetId(),
				},
			},
			want: &casperPb.GetOfferDetailsByIdResponse{
				Status: rpc.StatusOk(),
				Offer:  offer1,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// set up mocks
			tt.args.mockFunc()

			o := OfferCatalogService{
				catalogDao:    tt.fields.catalogDao,
				vendorFactory: mockVendorFactory,
				dyconf:        dyconf,
			}
			got, err := o.GetOfferDetailsById(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetOfferDetailsById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetOfferDetailsById() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestOfferCatalogService_GetBulkOfferDetailsByIds(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockOfferCatalogDao := daoMock.NewMockOfferCatalogDao(ctr)
	mockVendor := mocks.NewMockIVendor(ctr)
	mockVendorFactory := mocks.NewMockIVendorFactory(ctr)
	mockVendorFactory.EXPECT().Get(gomock.Any()).AnyTimes().Return(mockVendor, nil)

	offer1 := &casperPb.Offer{
		Id:         "offer-id-1",
		Name:       "offer name 1",
		Desc:       "offer desc 1",
		Price:      100,
		VendorName: casperPb.OfferVendor_LOYLTY,
		OfferType:  casperPb.OfferType_GIFT_CARD,
		OfferMetadata: &casperPb.OfferMetadata{
			OfferTypeSpecificMetadata: &casperPb.OfferMetadata_GiftCardMetadata{
				GiftCardMetadata: &casperPb.GiftCardOfferMetadata{GiftCardValue: 5},
			},
		},
		Images: []*casperPb.OfferImage{{
			ImageType: casperPb.ImageType_BACKGROUND_IMAGE,
			Url:       "background url",
		}},
		Tnc: &casperPb.OfferTnc{
			TncList: []string{"tnc1", "tnc2"},
		},
		IsDeleted: false,
	}

	offer2 := &casperPb.Offer{
		Id:         "offer-id-2",
		Name:       "offer name 2",
		Desc:       "offer desc 2",
		Price:      100,
		VendorName: casperPb.OfferVendor_LOYLTY,
		OfferType:  casperPb.OfferType_GIFT_CARD,
		OfferMetadata: &casperPb.OfferMetadata{
			OfferTypeSpecificMetadata: &casperPb.OfferMetadata_GiftCardMetadata{
				GiftCardMetadata: &casperPb.GiftCardOfferMetadata{GiftCardValue: 5},
			},
		},
		Images: []*casperPb.OfferImage{{
			ImageType: casperPb.ImageType_BACKGROUND_IMAGE,
			Url:       "background url",
		}},
		Tnc: &casperPb.OfferTnc{
			TncList: []string{"tnc1", "tnc2"},
		},
		IsDeleted: false,
	}

	// offer not linked to any vendor
	offer3 := &casperPb.Offer{
		Id:         "offer-id-3",
		Name:       "offer name 3",
		Desc:       "offer desc 3",
		Price:      100,
		VendorName: casperPb.OfferVendor_UNSPECIFIED,
		OfferType:  casperPb.OfferType_CHARITY,
		OfferMetadata: &casperPb.OfferMetadata{
			OfferTypeSpecificMetadata: &casperPb.OfferMetadata_GiftCardMetadata{
				GiftCardMetadata: &casperPb.GiftCardOfferMetadata{GiftCardValue: 5},
			},
		},
		Images: []*casperPb.OfferImage{{
			ImageType: casperPb.ImageType_BACKGROUND_IMAGE,
			Url:       "background url",
		}},
		Tnc: &casperPb.OfferTnc{
			TncList: []string{"tnc1", "tnc2"},
		},
		IsDeleted: false,
	}

	req := &casperPb.GetBulkOfferDetailsByIdsRequest{
		OfferIds:                      []string{offer1.GetId(), offer2.GetId()},
		WithUpdatedVendorOfferDetails: true,
	}

	type fields struct {
		catalogDao dao.OfferCatalogDao
	}
	type args struct {
		mockFunc func()
		ctx      context.Context
		req      *casperPb.GetBulkOfferDetailsByIdsRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *casperPb.GetBulkOfferDetailsByIdsResponse
		wantErr bool
	}{
		{
			name:   "get bulk offer by id failed at db",
			fields: fields{catalogDao: mockOfferCatalogDao},
			args: args{
				mockFunc: func() {
					mockOfferCatalogDao.EXPECT().GetBulkOfferDetailsByIds(gomock.Any(), gomock.Any()).Return(nil, errors.New("error while fetching offer details"))
				},
				ctx: context.Background(),
				req: req,
			},
			want: &casperPb.GetBulkOfferDetailsByIdsResponse{
				Status: rpc.StatusInternalWithDebugMsg("error while fetching offer details"),
			},
			wantErr: false,
		},
		{
			name:   "error during realtime vendor offer details update",
			fields: fields{catalogDao: mockOfferCatalogDao},
			args: args{
				mockFunc: func() {
					offers := []*casperPb.Offer{offer1, offer2}
					mockOfferCatalogDao.EXPECT().GetBulkOfferDetailsByIds(gomock.Any(), gomock.Any()).Return(offers, nil)
					mockVendor.EXPECT().GetOfferWithUpdatedVendorOfferDetails(gomock.Any(), offers).Return(nil, errors.New("error getting update offer details"))
				},
				ctx: context.Background(),
				req: req,
			},
			want: &casperPb.GetBulkOfferDetailsByIdsResponse{
				Status: rpc.StatusOk(),
				Offers: []*casperPb.Offer{},
			},
			wantErr: false,
		},
		{
			name:   "get bulk offer by ids successful 1 (get offers linked to vendor offers)",
			fields: fields{catalogDao: mockOfferCatalogDao},
			args: args{
				mockFunc: func() {
					offers := []*casperPb.Offer{offer1, offer2}
					mockOfferCatalogDao.EXPECT().GetBulkOfferDetailsByIds(gomock.Any(), gomock.Any()).Return(offers, nil)
					mockVendor.EXPECT().GetOfferWithUpdatedVendorOfferDetails(gomock.Any(), offers).Return(offers, nil)
				},
				ctx: context.Background(),
				req: req,
			},
			want: &casperPb.GetBulkOfferDetailsByIdsResponse{
				Status: rpc.StatusOk(),
				Offers: []*casperPb.Offer{offer1, offer2},
			},
			wantErr: false,
		},
		{
			name:   "get bulk offer by ids successful 2 (get offers not linked to vendor offers)",
			fields: fields{catalogDao: mockOfferCatalogDao},
			args: args{
				mockFunc: func() {
					offers := []*casperPb.Offer{offer3}
					mockOfferCatalogDao.EXPECT().GetBulkOfferDetailsByIds(gomock.Any(), gomock.Any()).Return(offers, nil)
				},
				ctx: context.Background(),
				req: req,
			},
			want: &casperPb.GetBulkOfferDetailsByIdsResponse{
				Status: rpc.StatusOk(),
				Offers: []*casperPb.Offer{offer3},
			},
			wantErr: false,
		},
		{
			name:   "get bulk offer by ids successful 3 (combination of vendor and non-vendor linked offers)",
			fields: fields{catalogDao: mockOfferCatalogDao},
			args: args{
				mockFunc: func() {
					offers := []*casperPb.Offer{offer1, offer2, offer3}
					mockOfferCatalogDao.EXPECT().GetBulkOfferDetailsByIds(gomock.Any(), gomock.Any()).Return(offers, nil)
					mockVendor.EXPECT().GetOfferWithUpdatedVendorOfferDetails(gomock.Any(), []*casperPb.Offer{offer1, offer2}).Return([]*casperPb.Offer{offer1, offer2}, nil)
				},
				ctx: context.Background(),
				req: req,
			},
			want: &casperPb.GetBulkOfferDetailsByIdsResponse{
				Status: rpc.StatusOk(),
				Offers: []*casperPb.Offer{offer1, offer2, offer3},
			},
			wantErr: false,
		},
		{
			name:   "get bulk offer by ids successful 4 (combination of vendor and non-vendor linked offers)",
			fields: fields{catalogDao: mockOfferCatalogDao},
			args: args{
				mockFunc: func() {
					offers := []*casperPb.Offer{offer1, offer2, offer3}
					mockOfferCatalogDao.EXPECT().GetBulkOfferDetailsByIds(gomock.Any(), gomock.Any()).Return(offers, nil)
					mockVendor.EXPECT().GetOfferWithUpdatedVendorOfferDetails(gomock.Any(), []*casperPb.Offer{offer1, offer2}).Return(nil, errors.New("error getting update offer details"))
				},
				ctx: context.Background(),
				req: req,
			},
			want: &casperPb.GetBulkOfferDetailsByIdsResponse{
				Status: rpc.StatusOk(),
				Offers: []*casperPb.Offer{offer3},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// set up mocks
			tt.args.mockFunc()

			o := OfferCatalogService{
				catalogDao:    tt.fields.catalogDao,
				vendorFactory: mockVendorFactory,
				dyconf:        dyconf,
			}
			got, err := o.GetBulkOfferDetailsByIds(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetBulkOfferDetailsByIds() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got.GetStatus().GetCode() != tt.want.GetStatus().GetCode() {
				t.Errorf("GetBulkOfferDetailsByIds() got status = %v, wantStatus %v", got.GetStatus().GetCode(), tt.want.GetStatus().GetCode())
				return
			}
			if !compareOfferList(got.GetOffers(), tt.want.GetOffers()) {
				t.Errorf("GetBulkOfferDetailsByIds() got offers = %v, want offer %v", got.GetOffers(), tt.want.GetOffers())
			}
		})
	}
}

// checks if actual and expected lists are equal or not
func compareOfferList(actual, expected []*casperPb.Offer) bool {
	if len(actual) != len(expected) {
		return false
	}
	// sorting both lists in a specific order so that values at
	// corresponding indexes in sorted list can be compared for equality.
	sort.Slice(actual, func(i, j int) bool {
		return actual[i].GetId() < actual[j].GetId()
	})
	sort.Slice(expected, func(i, j int) bool {
		return expected[i].GetId() < expected[j].GetId()
	})

	for idx := range actual {
		if !compareOffer(actual[idx], expected[idx]) {
			return false
		}
	}
	return true
}

// checks if actual and expected values are equal or not
func compareOffer(actual, expected *casperPb.Offer) bool {
	if actual != nil && expected != nil {
		expected.CreatedAt = actual.CreatedAt
		expected.UpdatedAt = actual.UpdatedAt
	}
	return proto.Equal(actual, expected)
}
