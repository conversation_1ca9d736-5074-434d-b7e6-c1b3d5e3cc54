package service

import (
	"context"
	"testing"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	casperPb "github.com/epifi/gamma/api/casper"
)

func TestOfferCatalogService_validateCreateOfferRequest(t *testing.T) {
	type args struct {
		ctx context.Context
		req *casperPb.CreateOfferRequest
	}
	tests := []struct {
		name string
		args args
		want []*casperPb.CreateOfferResponse_ValidationFailureInfo
	}{
		{
			name: "should throw error when vendor and offer type are not Compatible",
			args: args{ctx: context.Background(),
				req: &casperPb.CreateOfferRequest{
					VendorName: casperPb.OfferVendor_QWIKCILVER,
					OfferType:  casperPb.OfferType_POWER_UP,
					Tnc:        &casperPb.OfferTnc{TncList: []string{"dummy tnc 1, dummy tnc2"}},
					VendorOfferMetadata: &casperPb.VendorOfferMetadata{
						VendorOfferMetadata: &casperPb.VendorOfferMetadata_QwikcilverVendorOfferMetadata{
							QwikcilverVendorOfferMetadata: &casperPb.QwikcilverVendorOfferMetadata{
								SkuId: "CNPIN",
							},
						},
					},
				},
			},
			want: []*casperPb.CreateOfferResponse_ValidationFailureInfo{{
				FailureMessage: "invalid offer type for vendor,available OfferType are : [GIFT_CARD]",
			}},
		},
		{
			name: "should throw error when vendor and offer type are not compatible for other vendors",
			args: args{ctx: context.Background(),
				req: &casperPb.CreateOfferRequest{
					Price:      500,
					VendorName: casperPb.OfferVendor_BIGSMALL_OFFLINE,
					OfferType:  casperPb.OfferType_GIFT_CARD,
					Tnc:        &casperPb.OfferTnc{TncList: []string{"dummy tnc 1, dummy tnc2"}},
					VendorOfferMetadata: &casperPb.VendorOfferMetadata{
						VendorOfferMetadata: &casperPb.VendorOfferMetadata_OfflineVendorOfferMetadata{},
					},
					OfferMetadata: &casperPb.OfferMetadata{
						OfferTypeSpecificMetadata: &casperPb.OfferMetadata_GiftCardMetadata{
							GiftCardMetadata: &casperPb.GiftCardOfferMetadata{GiftCardValue: 50},
						},
					},
				},
			},
			want: []*casperPb.CreateOfferResponse_ValidationFailureInfo{{
				FailureMessage: "invalid offer type for vendor,available OfferType are : [SUBSCRIPTION CHARITY COUPON PHYSICAL_MERCHANDISE]",
			}},
		},
		{
			name: "should throw error when vendor and metadata are not compatible",
			args: args{ctx: context.Background(),
				req: &casperPb.CreateOfferRequest{
					Price:      500,
					VendorName: casperPb.OfferVendor_LOYLTY,
					OfferType:  casperPb.OfferType_GIFT_CARD,
					Tnc:        &casperPb.OfferTnc{TncList: []string{"dummy tnc 1, dummy tnc2"}},
					VendorOfferMetadata: &casperPb.VendorOfferMetadata{
						VendorOfferMetadata: &casperPb.VendorOfferMetadata_QwikcilverVendorOfferMetadata{},
					},
					OfferMetadata: &casperPb.OfferMetadata{
						OfferTypeSpecificMetadata: &casperPb.OfferMetadata_GiftCardMetadata{
							GiftCardMetadata: &casperPb.GiftCardOfferMetadata{GiftCardValue: 50},
						},
					},
				},
			},
			want: []*casperPb.CreateOfferResponse_ValidationFailureInfo{{
				FailureMessage: "invalid vendorMetaData,the LOYLTY vendor can have loylty_vendor_offer_metadata in vendorOfferMetaData",
			}},
		},
		{
			name: "should throw error when vendor and metadata are not compatible for other vendors",
			args: args{ctx: context.Background(),
				req: &casperPb.CreateOfferRequest{
					Price:      500,
					VendorName: casperPb.OfferVendor_BIGSMALL_OFFLINE,
					OfferType:  casperPb.OfferType_SUBSCRIPTION,
					Tnc:        &casperPb.OfferTnc{TncList: []string{"dummy tnc 1, dummy tnc2"}},
					VendorOfferMetadata: &casperPb.VendorOfferMetadata{
						VendorOfferMetadata: &casperPb.VendorOfferMetadata_QwikcilverVendorOfferMetadata{},
					},
					OfferMetadata: &casperPb.OfferMetadata{
						OfferTypeSpecificMetadata: &casperPb.OfferMetadata_GiftCardMetadata{
							GiftCardMetadata: &casperPb.GiftCardOfferMetadata{GiftCardValue: 50},
						},
					},
				},
			},
			want: []*casperPb.CreateOfferResponse_ValidationFailureInfo{{
				FailureMessage: "invalid vendorMetaData,the BIGSMALL_OFFLINE vendor can have offlineVendorOfferMetadata or {} in vendorOfferMetaDate",
			}},
		},
		{
			name: "should throw error when Tncs are not mentioned.",
			args: args{ctx: context.Background(),
				req: &casperPb.CreateOfferRequest{
					Price:      1000,
					VendorName: casperPb.OfferVendor_QWIKCILVER,
					OfferType:  casperPb.OfferType_GIFT_CARD,
					Tnc:        &casperPb.OfferTnc{TncList: []string{""}},
					VendorOfferMetadata: &casperPb.VendorOfferMetadata{
						VendorOfferMetadata: &casperPb.VendorOfferMetadata_QwikcilverVendorOfferMetadata{},
					},
					OfferMetadata: &casperPb.OfferMetadata{
						OfferTypeSpecificMetadata: &casperPb.OfferMetadata_GiftCardMetadata{
							GiftCardMetadata: &casperPb.GiftCardOfferMetadata{GiftCardValue: 50},
						},
					},
				},
			},
			want: []*casperPb.CreateOfferResponse_ValidationFailureInfo{{
				FailureMessage: "Tncs should not be Empty, please provide the Tncs",
			}},
		},
		{
			name: "should throw error when gift card value is not mentioned for offerType GIFT_CARD.",
			args: args{ctx: context.Background(),
				req: &casperPb.CreateOfferRequest{
					Price:      1000,
					VendorName: casperPb.OfferVendor_QWIKCILVER,
					OfferType:  casperPb.OfferType_GIFT_CARD,
					Tnc:        &casperPb.OfferTnc{TncList: []string{"dummy tnc 1, dummy tnc2"}},
					VendorOfferMetadata: &casperPb.VendorOfferMetadata{
						VendorOfferMetadata: &casperPb.VendorOfferMetadata_QwikcilverVendorOfferMetadata{},
					},
					OfferMetadata: &casperPb.OfferMetadata{
						OfferTypeSpecificMetadata: &casperPb.OfferMetadata_GiftCardMetadata{
							GiftCardMetadata: &casperPb.GiftCardOfferMetadata{},
						},
					},
				},
			},
			want: []*casperPb.CreateOfferResponse_ValidationFailureInfo{{
				FailureMessage: "giftCard value is not mentioned in giftCard metadata for offerType GIFT_CARD",
			}},
		},
		{
			name: "should throw error when merch value is not mentioned for offerType PHYSICAL_MERCHANDISE.",
			args: args{ctx: context.Background(),
				req: &casperPb.CreateOfferRequest{
					Price:      1000,
					VendorName: casperPb.OfferVendor_BIGSMALL_OFFLINE,
					OfferType:  casperPb.OfferType_PHYSICAL_MERCHANDISE,
					Tnc:        &casperPb.OfferTnc{TncList: []string{"dummy tnc 1, dummy tnc2"}},
					VendorOfferMetadata: &casperPb.VendorOfferMetadata{
						VendorOfferMetadata: &casperPb.VendorOfferMetadata_OfflineVendorOfferMetadata{},
					},
					OfferMetadata: &casperPb.OfferMetadata{
						OfferTypeSpecificMetadata: &casperPb.OfferMetadata_PhysicalMerchMetadata{
							PhysicalMerchMetadata: &casperPb.PhysicalMerchandiseOfferMetadata{},
						},
					},
				},
			},
			want: []*casperPb.CreateOfferResponse_ValidationFailureInfo{{
				FailureMessage: "merchValue value is not mentioned in physicalMerchandise metadata for offerType PHYSICAL_MERCHANDISE",
			}},
		},
		{
			name: "should throw an error when gitcard value greaterthan 50% of price",
			args: args{ctx: context.Background(),
				req: &casperPb.CreateOfferRequest{
					Price:          100,
					RedemptionMode: casperPb.OfferRedemptionMode_FI_COINS,
					VendorName:     casperPb.OfferVendor_QWIKCILVER,
					OfferType:      casperPb.OfferType_GIFT_CARD,
					Tnc:            &casperPb.OfferTnc{TncList: []string{"dummy tnc 1, dummy tnc2"}},
					VendorOfferMetadata: &casperPb.VendorOfferMetadata{
						VendorOfferMetadata: &casperPb.VendorOfferMetadata_QwikcilverVendorOfferMetadata{},
					},
					OfferMetadata: &casperPb.OfferMetadata{
						OfferTypeSpecificMetadata: &casperPb.OfferMetadata_GiftCardMetadata{
							GiftCardMetadata: &casperPb.GiftCardOfferMetadata{GiftCardValue: 100},
						},
					},
					ManualTags:  []casperPb.TagName{casperPb.TagName_SALARY_EXCLUSIVE_V2, casperPb.TagName_CREDIT_CARD_EXCLUSIVE},
					CategoryTag: casperPb.CategoryTag_CATEGORY_TAG_VOUCHERS,
					OfferAdditionalDetails: &casperPb.OfferAdditionalDetails{
						IsFiPoints: commontypes.BooleanEnum_TRUE,
					},
				},
			},
			want: []*casperPb.CreateOfferResponse_ValidationFailureInfo{{
				FailureMessage: "gift card or physical Merchandise value should not be more than 50% fiCoins spend for Exclusive and 5% fiCoins spend for normal offer.",
			}},
		},
		{
			name: "should not throw any error when valid parameters are passed",
			args: args{ctx: context.Background(),
				req: &casperPb.CreateOfferRequest{
					Price:      1000,
					VendorName: casperPb.OfferVendor_QWIKCILVER,
					OfferType:  casperPb.OfferType_GIFT_CARD,
					Tnc:        &casperPb.OfferTnc{TncList: []string{"dummy tnc 1, dummy tnc2"}},
					VendorOfferMetadata: &casperPb.VendorOfferMetadata{
						VendorOfferMetadata: &casperPb.VendorOfferMetadata_QwikcilverVendorOfferMetadata{},
					},
					OfferMetadata: &casperPb.OfferMetadata{
						OfferTypeSpecificMetadata: &casperPb.OfferMetadata_GiftCardMetadata{
							GiftCardMetadata: &casperPb.GiftCardOfferMetadata{GiftCardValue: 50},
						},
					},
				},
			},
			want: []*casperPb.CreateOfferResponse_ValidationFailureInfo{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := OfferCatalogService{
				dyconf: dyconf,
			}
			if got := o.validateCreateOfferRequest(tt.args.ctx, tt.args.req); !ResultEquals(got, tt.want) {
				t.Errorf("validateCreateOfferRequest() = %v, want %v", got, tt.want)
			}
		})
	}
}

func ResultEquals(got []*casperPb.CreateOfferResponse_ValidationFailureInfo, want []*casperPb.CreateOfferResponse_ValidationFailureInfo) bool {
	if len(got) != len(want) {
		return false
	}
	for idx, value := range got {
		if value.GetFailureMessage() != want[idx].GetFailureMessage() {
			return false
		}
	}
	return true
}

func Test_validateFiCoinValue(t *testing.T) {
	type args struct {
		offerValue  int32
		fiCoinPrice float32
		isExclusive bool
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "should return true when offer value is not greaterThan 5% of fiCoinPrice for non exclusive offer",
			args: args{
				offerValue:  100,
				fiCoinPrice: 2000,
				isExclusive: false,
			},
			want: true,
		},
		{
			name: "should return false when offer value is greaterThan 5% of fiCoinPrice for non exclusive offer",
			args: args{
				offerValue:  100,
				fiCoinPrice: 900,
				isExclusive: false,
			},
			want: false,
		},
		{
			name: "should return true when offer value is not greaterThan 50% of fiCoinPrice for exclusive offer",
			args: args{
				offerValue:  100,
				fiCoinPrice: 200,
				isExclusive: true,
			},
			want: true,
		},
		{
			name: "should return false when offer value is greaterThan 5% of fiCoinPrice for exclusive offer",
			args: args{
				offerValue:  100,
				fiCoinPrice: 190,
				isExclusive: true,
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := OfferCatalogService{
				dyconf: dyconf,
			}
			if got := o.validateFiCoinValue(tt.args.offerValue, tt.args.fiCoinPrice, tt.args.isExclusive); got != tt.want {
				t.Errorf("validateFiCoinValue() = %v, want %v", got, tt.want)
			}
		})
	}
}
