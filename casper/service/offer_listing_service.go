package service

import (
	"context"
	"fmt"
	"math"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/quest/types"
	"github.com/epifi/be-common/pkg/epifigrpc"

	redemptionPb "github.com/epifi/gamma/api/casper/redemption"
	segmentPb "github.com/epifi/gamma/api/segment"
	"github.com/epifi/gamma/casper/constants"
	"github.com/epifi/gamma/casper/dao/model"
	"github.com/epifi/gamma/casper/helper"
	questSdk "github.com/epifi/gamma/quest/sdk"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	casperPb "github.com/epifi/gamma/api/casper"
	"github.com/epifi/gamma/casper/dao"
	dao2 "github.com/epifi/gamma/casper/discount/dao"

	"go.uber.org/zap"
)

type OfferListingService struct {
	offerListingDao           dao.OfferListingDao
	offerCatalogService       casperPb.OfferCatalogServiceServer
	offerInventoryService     *OfferInventoryService
	discountsDao              dao2.IDiscountDao
	redemptionClient          redemptionPb.OfferRedemptionServiceClient
	questSdkClient            *questSdk.Client
	segmentationServiceClient segmentPb.SegmentationServiceClient
}

func NewOfferListingService(offerListingDao dao.OfferListingDao,
	offerCatalogService casperPb.OfferCatalogServiceServer,
	offerInventoryService *OfferInventoryService,
	discountsDao dao2.IDiscountDao,
	redemptionClient redemptionPb.OfferRedemptionServiceClient,
	questSdkClient *questSdk.Client,
	segmentationServiceClient segmentPb.SegmentationServiceClient) *OfferListingService {
	return &OfferListingService{
		offerListingDao:           offerListingDao,
		offerCatalogService:       offerCatalogService,
		offerInventoryService:     offerInventoryService,
		redemptionClient:          redemptionClient,
		discountsDao:              discountsDao,
		questSdkClient:            questSdkClient,
		segmentationServiceClient: segmentationServiceClient,
	}
}

var _ casperPb.OfferListingServiceServer = &OfferListingService{}

func (o *OfferListingService) CreateOfferListing(ctx context.Context, req *casperPb.CreateOfferListingRequest) (*casperPb.CreateOfferListingResponse, error) {

	offerListingId, err := o.offerListingDao.CreateOfferListing(ctx, req)
	if err != nil {
		logger.Error(ctx, "Error while creating offer listing", zap.Error(err), zap.Any("req", req))
		return &casperPb.CreateOfferListingResponse{
			Status: rpc.StatusInternalWithDebugMsg("Failed to create offer listing: " + err.Error()),
		}, nil
	}

	return &casperPb.CreateOfferListingResponse{
		Status:         rpc.StatusOk(),
		OfferListingId: offerListingId,
	}, nil
}

// validateOfferDetails fetches offer details and ensures the data is valid
func (o *OfferListingService) validateOfferListingDetails(ctx context.Context, req *casperPb.CreateOfferListingRequest) error {
	// Validate request and offer details
	if req == nil || req.GetOfferId() == "" {
		return errors.New("Request cannot be nil or missing OfferId")
	}

	// Fetch offer details
	offerDetailsResp, err := o.offerCatalogService.GetOfferDetailsById(ctx, &casperPb.GetOfferDetailsByIdRequest{
		OfferId: req.GetOfferId(),
	})
	if rpcErr := epifigrpc.RPCError(offerDetailsResp, err); rpcErr != nil {
		logger.Error(ctx, "Error while fetching offer details", zap.String("OfferId", req.GetOfferId()), zap.Error(rpcErr))
		return fmt.Errorf("Failed to fetch offer details: %w", rpcErr)
	}

	offer := offerDetailsResp.GetOffer()
	if offer == nil || offer.GetVendorOfferMetadata() == nil {
		logger.Error(ctx, "Offer details not found", zap.String("OfferId", req.GetOfferId()))
		return errors.New("Offer details not found")
	}

	// Validate Thriwe Vendor Offer Metadata
	vendorMetadata := offer.GetVendorOfferMetadata()
	if offer.GetVendorName() == casperPb.OfferVendor_THRIWE {
		if vendorMetadata.GetThriweVendorOfferMetadata() == nil {
			logger.Error(ctx, "ThriweVendorOfferMetadata not found", zap.String("OfferId", req.GetOfferId()))
			return errors.New("ThriweVendorOfferMetadata not found")
		}

		// Extract ExpiryDate and ActiveTill
		thriweExpiry := vendorMetadata.GetThriweVendorOfferMetadata().GetExpiryDate()
		activeTillStr := req.GetActiveTill()

		if thriweExpiry == nil || activeTillStr == "" {
			logger.Error(ctx, "ExpiryDate or ActiveTill is missing", zap.String("OfferId", req.GetOfferId()))
			return errors.New("ExpiryDate and ActiveTill cannot be nil")
		}

		// Convert timestamps
		thriweExpiryTime := thriweExpiry.AsTime()
		activeTillTime, err := time.Parse(time.RFC3339, activeTillStr)
		if err != nil {
			logger.Error(ctx, "Invalid ActiveTill format", zap.String("activeTill", activeTillStr), zap.Error(err))
			return errors.New("Invalid ActiveTill format. Use RFC3339 (e.g., 2025-12-31T23:59:59Z)")
		}

		// Validate ExpiryDate > ActiveTill
		if !thriweExpiryTime.After(activeTillTime) {
			logger.Error(ctx, "ExpiryDate must be greater than ActiveTill",
				zap.Time("ThriweExpiry", thriweExpiryTime), zap.Time("ActiveTill", activeTillTime))
			return errors.New("Thriwe expiry date must be greater than active till")
		}
	}
	return nil
}

func (o *OfferListingService) UpdateOfferListing(ctx context.Context, req *casperPb.UpdateOfferListingRequest) (*casperPb.UpdateOfferListingResponse, error) {
	if err := o.offerListingDao.UpdateOfferListing(ctx, req); err != nil {
		logger.Error(ctx, "error while updating offer listing by id", zap.Error(err), zap.Any("req", req))
		return &casperPb.UpdateOfferListingResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	return &casperPb.UpdateOfferListingResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (o *OfferListingService) DeleteOfferListing(ctx context.Context, req *casperPb.DeleteOfferListingRequest) (*casperPb.DeleteOfferListingResponse, error) {
	if err := o.offerListingDao.DeleteOfferListingById(ctx, req.OfferListingId); err != nil {
		logger.Error(ctx, "error while deleting offer listing by id", zap.Error(err), zap.Any("req", req))
		return &casperPb.DeleteOfferListingResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	return &casperPb.DeleteOfferListingResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (o *OfferListingService) GetOfferListingById(ctx context.Context, req *casperPb.GetOfferListingByIdRequest) (*casperPb.GetOfferListingByIdResponse, error) {
	offerListing, err := o.offerListingDao.GetOfferListingById(ctx, req.GetOfferListingId())
	if err != nil {
		logger.Error(ctx, "error while getting offer listing by id", zap.String("listingId", req.GetOfferListingId()), zap.Error(err))
		return &casperPb.GetOfferListingByIdResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	return &casperPb.GetOfferListingByIdResponse{
		Status:       rpc.StatusOk(),
		OfferListing: offerListing,
	}, nil
}

// GetDisplayActiveListingsForOffer will be used to fetch display active listings for an offer
func (o *OfferListingService) GetDisplayActiveListingsForOffer(ctx context.Context, req *casperPb.GetDisplayActiveListingsForOfferRequest) (*casperPb.GetDisplayActiveListingsForOfferResponse, error) {
	// fetch display active listings for given offer
	getOfferListingsFilters := &model.GetOfferListingsFilters{
		DisplaySince: time.Now(),
		DisplayTill:  time.Now(),
	}
	offerListings, err := o.offerListingDao.GetOfferListingsByOfferId(ctx, req.GetOfferId(), getOfferListingsFilters)
	if err != nil {
		logger.Error(ctx, "error while getting offer listings by offer id", zap.String(logger.OFFER_ID, req.GetOfferId()), zap.Error(err))
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &casperPb.GetDisplayActiveListingsForOfferResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		}
		return &casperPb.GetDisplayActiveListingsForOfferResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	return &casperPb.GetDisplayActiveListingsForOfferResponse{
		Status:        rpc.StatusOk(),
		OfferListings: offerListings,
	}, nil
}

// GetOffers returns offers and offer ids to listing map along with actor level inventories
// 1. Fetches active offer listings filtered by redemption mode
// 2. Fetches offer details corresponding to the active listings in bulk
// 3. Fetches user level inventory, user level attempts and discounts only for coin offers and defaults inventories for other redemption mode as 1
// todo: refactor and optimise this RPC
func (o *OfferListingService) GetOffers(ctx context.Context, req *casperPb.GetOffersRequest) (*casperPb.GetOffersResponse, error) {
	if req.GetRedemptionMode() == casperPb.OfferRedemptionMode_UNSPECIFIED_REDEMPTION_MODE {
		logger.Error(ctx, "redemption mode can't be unspecified")
		return &casperPb.GetOffersResponse{Status: rpc.StatusInvalidArgumentWithDebugMsg("redemption mode can't be unspecified")}, nil
	}

	redemptionModeToOfferListingsMap, err := o.offerListingDao.GetActiveOfferListingsByFilters(ctx, &model.GetActiveOfferListingFilters{
		RedemptionModes: []casperPb.OfferRedemptionMode{req.GetRedemptionMode()},
	})
	if err != nil {
		logger.Error(ctx, "error while getting active offer listing", zap.Error(err), zap.Any("req", req))
		return &casperPb.GetOffersResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	if len(redemptionModeToOfferListingsMap) == 0 {
		logger.Info(ctx, "no active offer listings found for redemption mode", zap.Any("redemptionMode", req.GetRedemptionMode()))
		return &casperPb.GetOffersResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil
	}

	var offerListings []*casperPb.OfferListing
	for _, listings := range redemptionModeToOfferListingsMap {
		offerListings = append(offerListings, listings...)
	}

	offersList, offerIdToListingMap, offerIdToAvailableUserLevelInventoryMap, offerIdToUserLevelAttemptsRemainingMap, offerIdToRemainingRedemptionsCountInCurrentMonthMap, err := o.getOffersAndListingsMap(ctx, req.GetActorId(), req.GetRedemptionMode(), casperPb.CardOfferType_CARD_OFFER_TYPE_UNSPECIFIED, req.GetFilters(), offerListings)
	if err != nil {
		logger.Error(ctx, "error while fetching offer details", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		return &casperPb.GetOffersResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	// TODO(vikas): add extra field at offer level to specify if offer is not currently active for coming soon feature
	return &casperPb.GetOffersResponse{
		Status:                                  rpc.StatusOk(),
		Offers:                                  offersList,
		OfferIdToListingMap:                     offerIdToListingMap,
		OfferIdToAvailableUserLevelInventoryMap: offerIdToAvailableUserLevelInventoryMap,
		OfferIdToUserLevelAttemptsRemainingMap:  offerIdToUserLevelAttemptsRemainingMap,
		OfferIdToRemainingMonthlyRedemptionsMap: offerIdToRemainingRedemptionsCountInCurrentMonthMap,
	}, nil
}

// getOffersAndListingsMap returns offers and offer ids to listing map along with actor level inventories
// 1. Takes offer listings filtered by redemption mode & other filters
// 2. Fetches offer details corresponding to the active listings in bulk
// 3. Fetches user level inventory, user level attempts and discounts only for coin offers and defaults inventories for other redemption mode as 1
// nolint: funlen, govet
func (o *OfferListingService) getOffersAndListingsMap(ctx context.Context, actorId string, redemptionMode casperPb.OfferRedemptionMode, cardOfferType casperPb.CardOfferType, filters *casperPb.CatalogFilters, offerListings []*casperPb.OfferListing) ([]*casperPb.Offer, map[string]*casperPb.OfferListing, map[string]int32, map[string]int32, map[string]uint32, error) {

	// todo(divyadeep): use getOfferIdToOfferListingMap to get map containing offerIds remaining after filtering of offers
	offerIdToListingMap := make(map[string]*casperPb.OfferListing, len(offerListings))
	for _, offerListing := range offerListings {
		offerIdToListingMap[offerListing.GetOfferId()] = offerListing
	}

	// fetch offer details given offerIds and redemption mode.
	offerIds := lo.Map(offerListings, func(offerListing *casperPb.OfferListing, index int) string { return offerListing.GetOfferId() })
	request := &casperPb.GetBulkOfferDetailsByIdsRequest{OfferIds: offerIds, RedemptionMode: redemptionMode, WithUpdatedVendorOfferDetails: true}
	offerDetailsRes, err := o.offerCatalogService.GetBulkOfferDetailsByIds(ctx, request)
	if rpcErr := epifigrpc.RPCError(offerDetailsRes, err); rpcErr != nil {
		logger.Error(ctx, "unable to get offer details", zap.Error(rpcErr), zap.Any("offerIds", request.GetOfferIds()), zap.Any(logger.RPC_STATUS, offerDetailsRes.GetStatus()))
		return nil, nil, nil, nil, nil, rpcErr
	}
	offersList := offerDetailsRes.GetOffers()

	// filter out all offers based on card offer type
	offersList = o.filterOutOffersByCardOfferType(offersList, cardOfferType)

	// filter out experimental offers which are not part of variant in which user lies
	offersList = o.filterOutExperimentalOffersNotApplicableForUser(ctx, offersList)

	// filter out segmented offers for users who are not a part fo the segment
	offersList = o.filterOutSegmentedOffersNotApplicableForUser(ctx, offersList, actorId)

	filteredOfferIdList := []string{}
	for _, offers := range offersList {
		filteredOfferIdList = append(filteredOfferIdList, offers.GetId())
	}

	offerIdToAvailableUserLevelInventoryMap := make(map[string]int32, len(offerListings))
	offerIdToUserLevelAttemptsRemainingMap := make(map[string]int32, len(offerListings))
	// offerIdToRemainingRedemptionsCountInCurrentMonthMap will only contain those
	// offerIds as keys that have a monthly limit defined
	offerIdToRemainingRedemptionsCountInCurrentMonthMap := map[string]uint32{}
	// ** fetching offer inventory and actor purchase counts only for fi coin and internal offers since other redemption modes do not use these inventories **
	if redemptionMode == casperPb.OfferRedemptionMode_FI_COINS || redemptionMode == casperPb.OfferRedemptionMode_INTERNAL {
		var offerInventoryIds []string
		var offerInventories []*casperPb.OfferInventory

		offerInventoryRes, err := o.offerInventoryService.GetOfferInventoryByOfferIds(ctx, &casperPb.GetOfferInventoryByOfferIdsRequest{
			OfferIds: filteredOfferIdList,
			ActorId:  actorId,
		})
		if grpcErr := epifigrpc.RPCError(offerInventoryRes, err); grpcErr != nil {
			logger.Error(ctx, "error while fetching offer inventories in bulk", zap.Error(grpcErr))
			return nil, nil, nil, nil, nil, fmt.Errorf("error while fetching offer inventories in bulk")
		}
		for _, offerId := range filteredOfferIdList {
			if offerInventory, found := offerInventoryRes.GetOfferIdToOfferInventoryMap()[offerId]; found {
				offerIdToAvailableUserLevelInventoryMap[offerId] = offerInventory.GetAvailableCount()
				offerInventories = append(offerInventories, offerInventory)
				offerInventoryIds = append(offerInventoryIds, offerInventory.GetId())
			} else {
				logger.Error(ctx, "unable to find offer inventory in map", zap.String(logger.OFFER_ID, offerId))
				continue
			}
		}

		// logic for getting user level attempts remaining, first get the number of times user has
		// purchased the offer from offerInventoryService, then get the max number of redemptions allowed per user,
		// user level attempts remaining = (max number of redemptions allowed per user) - (purchased count for user)
		// not using 'offerIdToAvailableUserLevelInventoryMap' as it sets the user level remaining attempts to availableCount,
		// when availableCount < userLevelAttemptsRemaining, so created a new logic for that as we want to handle the two cases differently
		getPurchaseCountForActorByOfferIdsResponse, err := o.offerInventoryService.GetPurchaseCountForActorByOfferInventoryIds(ctx,
			&casperPb.GetPurchaseCountForActorByOfferInventoryIdsRequest{
				OfferInventoryIds: offerInventoryIds,
				ActorId:           actorId,
			})
		if rpcErr := epifigrpc.RPCError(getPurchaseCountForActorByOfferIdsResponse, err); rpcErr != nil {
			logger.Error(ctx, "failed GetPurchaseCountForActorByOfferInventoryIds call", zap.Any("offerInventoryIds", offerInventoryIds), zap.String(logger.ACTOR_ID_V2, actorId), zap.Any(logger.RPC_STATUS, getPurchaseCountForActorByOfferIdsResponse.GetStatus()))
			return nil, nil, nil, nil, nil, fmt.Errorf("failed GetPurchaseCountForActorByOfferInventoryIds call")
		}

		offerIdsWithMonthlyCap := []string{}
		offerInventoryIdToUserLevelPurchaseCountMap := getPurchaseCountForActorByOfferIdsResponse.GetOfferInventoryIdToUserLevelPurchaseCountMap()
		for _, offerInventory := range offerInventories {
			purchaseCount := int32(0)
			userLevelPurchaseCountForOfferInventoryId, ok := offerInventoryIdToUserLevelPurchaseCountMap[offerInventory.GetId()]
			if ok {
				purchaseCount = userLevelPurchaseCountForOfferInventoryId
			}
			remainingAttemptsCount := math.Max(float64(offerInventory.GetMaxPerUserLimit()-purchaseCount), 0)
			offerIdToUserLevelAttemptsRemainingMap[offerInventory.GetOfferId()] = int32(remainingAttemptsCount)

			if offerInventory.GetUserLevelMonthlyRedemptionCap() != 0 {
				// if UserLevelMonthlyRedemptionCap is non-zero, we'll need to fetch redemptions count for the user done in this month
				offerIdsWithMonthlyCap = append(offerIdsWithMonthlyCap, offerInventory.GetOfferId())
			}
		}

		if len(offerIdsWithMonthlyCap) > 0 {
			redemptionCountsForActorAndOffersInMonth, err := o.offerInventoryService.GetRedemptionCountsForActorAndOffersInMonth(ctx, &casperPb.GetRedemptionCountsForActorAndOffersInMonthRequest{
				ActorId:        actorId,
				OfferIds:       offerIdsWithMonthlyCap,
				MonthTimestamp: timestampPb.Now(),
			})
			// we'll ignore RecordNotFound status responses as they imply that there's no
			// redemption for any of the given offers for the current month
			if !redemptionCountsForActorAndOffersInMonth.GetStatus().IsSuccess() && !redemptionCountsForActorAndOffersInMonth.GetStatus().IsRecordNotFound() || err != nil {
				logger.Error(ctx, "failed GetRedemptionCountsForActorAndOffersInMonth call", zap.Any("offerIdsWithMonthlyCap", offerIdsWithMonthlyCap), zap.String(logger.ACTOR_ID_V2, actorId), zap.Any(logger.RPC_STATUS, redemptionCountsForActorAndOffersInMonth.GetStatus()), zap.Error(err))
				return nil, nil, nil, nil, nil, fmt.Errorf("failed GetRedemptionCountsForActorAndOffersInMonth call")
			}
			for offerId, redemptionsCount := range redemptionCountsForActorAndOffersInMonth.GetOfferInventoryIdToRedemptionsCountMap() {
				offerIdToRemainingRedemptionsCountInCurrentMonthMap[offerId] = offerInventoryRes.GetOfferIdToOfferInventoryMap()[offerId].UserLevelMonthlyRedemptionCap - redemptionsCount
			}
		}
	} else if redemptionMode == casperPb.OfferRedemptionMode_FI_CARD || redemptionMode == casperPb.OfferRedemptionMode_FI_CREDIT_CARD {
		// for other redemption modes, we add a default inventory and attempt for each offer to 1
		// so if there is any frontend handling for card offer types, it will not break
		for _, offerListing := range offerListings {
			offerIdToAvailableUserLevelInventoryMap[offerListing.GetOfferId()] = 1
			offerIdToUserLevelAttemptsRemainingMap[offerListing.GetOfferId()] = 1
		}
	}

	if redemptionMode == casperPb.OfferRedemptionMode_FI_COINS {
		offerIdToOffersMap := make(map[string]*casperPb.Offer, len(offersList))
		for _, offer := range offersList {
			offerIdToOffersMap[offer.GetId()] = offer
		}

		// fetch discounts for orders
		discounts, err := o.discountsDao.GetDiscounts(ctx, "", filteredOfferIdList, time.Now(), time.Now())
		if err != nil {
			logger.Error(ctx, "error while fetching discounts for offerIds", zap.Error(err))
			return nil, nil, nil, nil, nil, fmt.Errorf("error while fetching discounts for offerIds")
		}
		for _, discount := range discounts {
			offer := offerIdToOffersMap[discount.GetOfferId()]
			discountedPrice, err := helper.GetDiscountedPrice(offer.GetPrice(), discount)
			if err != nil {
				logger.Error(
					ctx,
					"error while calculating discounted Price from original price and discount",
					zap.Error(err),
					zap.Float32("originalPrice", offer.GetPrice()),
					zap.String(logger.OFFER_ID, offer.GetId()),
					zap.String("discountId", discount.GetId()),
					zap.String("discountType", discount.GetDiscountType().String()),
					zap.Any("discountTypeSpecificMetadata", discount.DiscountTypeSpecificMetadata),
				)
				return nil, nil, nil, nil, nil, fmt.Errorf("error while calculating discounted Price from original price and discount")
			}
			offer.DiscountDetails = &casperPb.DiscountDetails{
				DiscountedPrice: discountedPrice,
				DisplayMode:     discount.GetDisplayDetails().GetDisplayMode(),
				DiscountEndTime: discount.GetActiveTill(),
			}

			// adding dynamic "Discounted" tag to the offer, as discount is live at this point in time
			offer.GetTagsInfo().Tags = append(offer.GetTagsInfo().Tags, casperPb.TagName_DISCOUNTED)
		}

		// add dynamic COMING_SOON tag wherever applicable
		if err = addComingSoonTagToApplicableOffers(offerListings, offerIdToOffersMap); err != nil {
			logger.WarnWithCtx(ctx, "error while adding Coming-soon tag to offers.", zap.Error(err))
		}

		// filter out power up offer if it is redeemed in the last cool off hours
		offersList = []*casperPb.Offer{}
		for _, offer := range offerIdToOffersMap {
			// todo (himanshu) : move this check to fe once designs are ready
			if offer.GetOfferType() == casperPb.OfferType_POWER_UP {
				coolOffHours := offer.GetOfferMetadata().GetPowerUpOfferMetadata().GetRedemptionRateLimitMetadata().GetCoolOffHours()
				if coolOffHours != 0 {
					isRedeemed, err := o.isCatalogOfferRedeemedAfterTime(ctx, actorId, offer.GetId(), time.Now().Add(-time.Duration(coolOffHours)*time.Hour))
					if err != nil {
						logger.Error(ctx, "error checking if power up offer is redeemed in the last cool off hours", zap.String(logger.OFFER_ID, offer.GetId()), zap.Error(err))
					}
					if isRedeemed {
						continue
					}
				}
			}
			offersList = append(offersList, offer)
		}
	}

	// filter offers out based on any filters that might be a part of the request, this needs to happen only after all dynamic tags are applied.
	if filters != nil {
		var (
			filteredOffersList []*casperPb.Offer
			tags               = filters.GetTags()
			categoryTags       = filters.GetCategoryTags()
		)
		for _, offer := range offersList {
			// if tagsFilter list is not empty, we omit the offer if it doesn't have tags matching any of the filters
			if (len(tags) > 0 && !helper.IsAnyCommonTagPresentInBothLists(filters.GetTags(), offer.GetTagsInfo().GetTags()) &&
				!helper.IsAnyCommonTagPresentInBothLists(filters.GetTags(), offer.GetTagsInfo().GetManualTags())) || (len(categoryTags) > 0 && !lo.Contains(categoryTags, offer.GetTagsInfo().GetCategoryTag())) {
				continue
			}
			filteredOffersList = append(filteredOffersList, offer)
		}
		offersList = filteredOffersList
	}
	return offersList, offerIdToListingMap, offerIdToAvailableUserLevelInventoryMap, offerIdToUserLevelAttemptsRemainingMap, offerIdToRemainingRedemptionsCountInCurrentMonthMap, nil
}

// GetOffersByFilters returns offers and offer ids to listing map along with actor level inventories
// 1. Fetches all offer listings filtered by redemption mode & time window
// 2. Fetches offer details corresponding to the active listings in bulk
// 3. Fetches user level inventory, user level attempts and discounts only for coin offers and defaults inventories for other redemption mode as 1
func (o *OfferListingService) GetOffersByFilters(ctx context.Context, req *casperPb.GetOffersByFiltersRequest) (*casperPb.GetOffersByFiltersResponse, error) {
	if req.GetOfferFilters().GetRedemptionMode() == casperPb.OfferRedemptionMode_UNSPECIFIED_REDEMPTION_MODE {
		logger.Error(ctx, "redemption mode can't be unspecified")
		return &casperPb.GetOffersByFiltersResponse{Status: rpc.StatusInvalidArgumentWithDebugMsg("redemption mode can't be unspecified")}, nil
	}
	var (
		fromDate = req.GetOfferFilters().GetFromTime()
		tillDate = req.GetOfferFilters().GetTillTime()
	)
	// if time window is nil, then take active listings (current time)
	if req.GetOfferFilters().GetFromTime() == nil && req.GetOfferFilters().GetTillTime() == nil {
		fromDate = timestampPb.Now()
		tillDate = timestampPb.Now()
	}

	offerListings, err := o.offerListingDao.GetOfferListingsByFilters(ctx, &casperPb.OfferListingsFilters{
		RedemptionModes: []casperPb.OfferRedemptionMode{req.GetOfferFilters().GetRedemptionMode()},
		FromDate:        fromDate,
		ToDate:          tillDate,
	})
	if err != nil {
		logger.Error(ctx, "error while getting active offer listing", zap.Error(err), zap.Any("req", req))
		return &casperPb.GetOffersByFiltersResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	offersList, offerIdToListingMap, offerIdToAvailableUserLevelInventoryMap, offerIdToUserLevelAttemptsRemainingMap, offerIdToRemainingRedemptionsCountInCurrentMonthMap, err := o.getOffersAndListingsMap(ctx, req.GetActorId(), req.GetOfferFilters().GetRedemptionMode(), req.GetOfferFilters().GetCardOfferType(), nil, offerListings)
	if err != nil {
		logger.Error(ctx, "error while fetching offer details", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		return &casperPb.GetOffersByFiltersResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	return &casperPb.GetOffersByFiltersResponse{
		Status:                                  rpc.StatusOk(),
		Offers:                                  offersList,
		OfferIdToListingMap:                     offerIdToListingMap,
		OfferIdToAvailableUserLevelInventoryMap: offerIdToAvailableUserLevelInventoryMap,
		OfferIdToUserLevelAttemptsRemainingMap:  offerIdToUserLevelAttemptsRemainingMap,
		OfferIdToRemainingMonthlyRedemptionsMap: offerIdToRemainingRedemptionsCountInCurrentMonthMap,
	}, nil
}

// addComingSoonTagToApplicableOffers adds COMING_SOON tag to all the offers that have their listing's display_since < current time < active_since
// i.e. the offers that are visible on the catalog but aren't redeemable currently as they will be active in the future.
// nolint: unparam
func addComingSoonTagToApplicableOffers(offerListings []*casperPb.OfferListing, offerIdToOfferMap map[string]*casperPb.Offer) error {
	for _, offerListing := range offerListings {
		// add COMING_SOON tag if offer isn't active yet, but is being displayed
		activeSince, err := time.Parse(time.RFC3339, offerListing.GetActiveSince())
		if err != nil {
			logger.Warn("unable to parse activeSince timestamp of offer listing for deciding whether to add COMING_SOON tag to offer, continuing.", zap.String("listingId", offerListing.GetId()), zap.Error(err))
			continue
		}
		displaySince, err := time.Parse(time.RFC3339, offerListing.GetDisplaySince())
		if err != nil {
			logger.Warn("unable to parse displaySince timestamp of offer listing for deciding whether to add COMING_SOON tag to offer, continuing.", zap.String("listingId", offerListing.GetId()), zap.Error(err))
			continue
		}

		if displaySince.Before(time.Now()) && time.Now().Before(activeSince) {
			if offer, ok := offerIdToOfferMap[offerListing.GetOfferId()]; ok {
				offer.TagsInfo.Tags = append(offer.TagsInfo.Tags, casperPb.TagName_COMING_SOON)
			}
		}
	}
	return nil
}

func (o *OfferListingService) isCatalogOfferRedeemedAfterTime(ctx context.Context, actorId string, catalogOfferId string, afterTime time.Time) (bool, error) {
	redeemedOffersRes, err := o.redemptionClient.GetRedeemedOffersForActor(ctx, &redemptionPb.GetRedeemedOffersForActorRequest{
		ActorId: actorId,
		PageContext: &rpc.PageContextRequest{
			PageSize: 1,
		},
		Filters: &redemptionPb.GetRedeemedOffersForActorRequest_Filters{
			OfferId:          catalogOfferId,
			FromDate:         timestampPb.New(afterTime),
			RedemptionStates: []redemptionPb.OfferRedemptionState{redemptionPb.OfferRedemptionState_OFFER_REDEMPTION_SUCCESSFUL},
		},
	})

	if rpcErr := epifigrpc.RPCError(redeemedOffersRes, err); rpcErr != nil {
		return false, fmt.Errorf("error while fetching redeemed offers for actor, err: %w, rpc status code: %s", err, redeemedOffersRes.GetStatus().String())
	}

	return len(redeemedOffersRes.GetRedeemedOffers()) > 0, nil
}

// nolint: funlen
func (o *OfferListingService) GetCardOffers(ctx context.Context, request *casperPb.GetCardOffersRequest) (*casperPb.GetCardOffersResponse, error) {
	// we'll fetch all card offers if redemptionMode isn't specified in request
	redemptionModesFilter := []casperPb.OfferRedemptionMode{
		casperPb.OfferRedemptionMode_FI_CARD,
		casperPb.OfferRedemptionMode_FI_CREDIT_CARD,
	}
	if request.GetRedemptionMode() != casperPb.OfferRedemptionMode_UNSPECIFIED_REDEMPTION_MODE {
		redemptionModesFilter = []casperPb.OfferRedemptionMode{
			request.GetRedemptionMode(),
		}
	}

	redemptionModeToOfferListingsMap, err := o.offerListingDao.GetActiveOfferListingsByFilters(ctx, &model.GetActiveOfferListingFilters{
		RedemptionModes: redemptionModesFilter,
	})
	if err != nil {
		logger.Error(ctx, "failed to fetch active listings", zap.Error(err))
		return &casperPb.GetCardOffersResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to fetch active listings"),
		}, nil
	}

	var activeListings []*casperPb.OfferListing
	for _, listings := range redemptionModeToOfferListingsMap {
		activeListings = append(activeListings, listings...)
	}

	// fetch offers for the listings
	offerIds := lo.Map(activeListings, func(offerListing *casperPb.OfferListing, index int) string { return offerListing.GetOfferId() })
	offerDetailsRes, err := o.offerCatalogService.GetBulkOfferDetailsByIds(ctx, &casperPb.GetBulkOfferDetailsByIdsRequest{OfferIds: offerIds})
	if rpcErr := epifigrpc.RPCError(offerDetailsRes, err); rpcErr != nil {
		logger.Error(ctx, "unable to get offer details", zap.Error(rpcErr), zap.Any("offerIds", offerIds), zap.Any(logger.RPC_STATUS, offerDetailsRes.GetStatus()))
		return &casperPb.GetCardOffersResponse{
			Status: rpc.StatusInternalWithDebugMsg(rpcErr.Error()),
		}, nil
	}

	offers := offerDetailsRes.GetOffers()

	// filter out experimental offers which are not part of variant in which user lies
	offers = o.filterOutExperimentalOffersNotApplicableForUser(ctx, offers)

	// filter offers out based on any filters that might be a part of the request, this needs to happen only after all dynamic tags are applied.
	filterTags := request.GetFilters().GetTags()
	if len(filterTags) > 0 {
		var filteredOffers []*casperPb.Offer
		for _, offer := range offers {
			// if tagsFilter list is not empty, we omit the offer if it doesn't have tags matching any of the filters
			if !helper.IsAnyCommonTagPresentInBothLists(filterTags, offer.GetTagsInfo().GetTags()) &&
				!helper.IsAnyCommonTagPresentInBothLists(filterTags, offer.GetTagsInfo().GetManualTags()) {
				continue
			}
			filteredOffers = append(filteredOffers, offer)
		}
		offers = filteredOffers
	} else if request.GetFiltersV2() != nil {
		var filteredOffers []*casperPb.Offer
		for _, offer := range offers {
			offerTags := append(offer.GetTagsInfo().GetTags(), offer.GetTagsInfo().GetManualTags()...)
			// if offerTags does not contain all the andTags, we omit the offer
			if len(request.GetFiltersV2().GetAndTags()) != 0 && !lo.Every(offerTags, request.GetFiltersV2().GetAndTags()) {
				continue
			}
			// if offerTags does not contain any of orTags, we omit the offer
			if len(request.GetFiltersV2().GetOrTags()) != 0 && !lo.Some(offerTags, request.GetFiltersV2().GetOrTags()) {
				continue
			}
			filteredOffers = append(filteredOffers, offer)
		}
		offers = filteredOffers
	}

	offerIdToListingMap, err := o.getOfferIdToOfferListingMap(offers, activeListings)
	if err != nil {
		return nil, err
	}

	return &casperPb.GetCardOffersResponse{
		Status:              rpc.StatusOk(),
		Offers:              offers,
		OfferIdToListingMap: offerIdToListingMap,
	}, nil
}

func (o *OfferListingService) getOfferIdToOfferListingMap(offers []*casperPb.Offer, offerListings []*casperPb.OfferListing) (map[string]*casperPb.OfferListing, error) {
	allOfferIdsToListingMap := make(map[string]*casperPb.OfferListing, len(offerListings))
	for _, offerListing := range offerListings {
		allOfferIdsToListingMap[offerListing.GetOfferId()] = offerListing
	}

	givenOfferIdsToListingMap := make(map[string]*casperPb.OfferListing)
	for _, offer := range offers {
		offerListing, present := allOfferIdsToListingMap[offer.GetId()]
		if !present {
			return nil, fmt.Errorf("listing not found for offerId. offerId: %s", offer)
		}
		givenOfferIdsToListingMap[offer.GetId()] = offerListing
	}

	return givenOfferIdsToListingMap, nil
}

func (o *OfferListingService) filterOutExperimentalOffersNotApplicableForUser(ctx context.Context, offersList []*casperPb.Offer) []*casperPb.Offer {
	return lo.Filter(offersList, func(offer *casperPb.Offer, _ int) bool {
		// if offer is not experimental, we retain the offer
		if !offer.GetAdditionalDetails().GetIsExperimental() {
			return true
		}
		// if offer is experimental, we retain only if it is applicable for user's variant
		return o.questSdkClient.EvaluateWithDefaultValue(ctx, &types.Variable{
			Path: fmt.Sprintf(constants.ExperimentalOfferVariablePathTemplate, offer.GetId()),
		}, false) == true
	})
}

// filterOutSegmentedOffersNotApplicableForUser filters out segmented offer not applicable for user
// if segment rpc call fails we will not show any segmented offer to the user
func (o *OfferListingService) filterOutSegmentedOffersNotApplicableForUser(ctx context.Context, offersList []*casperPb.Offer, actorId string) []*casperPb.Offer {
	segmentExpressions := []string{}
	for _, offer := range offersList {
		if offer.GetAdditionalDetails().GetSegmentExpression() != "" {
			segmentExpressions = append(segmentExpressions, offer.GetAdditionalDetails().GetSegmentExpression())
		}
	}
	if len(segmentExpressions) == 0 {
		return offersList
	}
	isMemberOfExpressionsRes, err := o.segmentationServiceClient.IsMemberOfExpressions(ctx, &segmentPb.IsMemberOfExpressionsRequest{
		ActorId:              actorId,
		SegmentIdExpressions: segmentExpressions,
		LatestBy:             timestampPb.Now(),
	})
	rpcErr := epifigrpc.RPCError(isMemberOfExpressionsRes, err)
	if rpcErr != nil {
		logger.Error(ctx, "error in IsMemberOfExpressions rpc", zap.Error(rpcErr), zap.Any("segmentExpressions", segmentExpressions))
	}

	return lo.Filter(offersList, func(offer *casperPb.Offer, _ int) bool {
		if rpcErr != nil {
			if offer.GetAdditionalDetails().GetSegmentExpression() != "" {
				return false
			}
			return true
		}
		if offer.GetAdditionalDetails().GetSegmentExpression() != "" && !isMemberOfExpressionsRes.GetSegmentExpressionMembershipMap()[offer.GetAdditionalDetails().GetSegmentExpression()].GetIsActorMember() {
			return false
		}
		return true
	})
}

func (o *OfferListingService) filterOutOffersByCardOfferType(offersList []*casperPb.Offer, cardOfferType casperPb.CardOfferType) []*casperPb.Offer {
	// if card offer type is unspecified return default offersList.
	if cardOfferType == casperPb.CardOfferType_CARD_OFFER_TYPE_UNSPECIFIED {
		return offersList
	}
	return lo.Filter(offersList, func(offer *casperPb.Offer, _ int) bool {
		// if offer redemption mode is of type FI_CARD return specified card offer types
		if offer.GetRedemptionMode() == casperPb.OfferRedemptionMode_FI_CARD && offer.GetAdditionalDetails().GetCardOfferType() == cardOfferType {
			return true
		}
		return false
	})
}
