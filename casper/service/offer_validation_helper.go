package service

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	casperPb "github.com/epifi/gamma/api/casper"
	segmentPb "github.com/epifi/gamma/api/segment"
	"github.com/epifi/gamma/casper/constants"
)

var vendorToOfferTypeMap = map[string][]string{
	"NO_OP_VENDOR": {"POWER_UP"},
	"VISTARA":      {"VISTARA_AIR_MILES"},
	"ITC":          {"CLUB_ITC_GREEN_POINTS"},
	"THRIWE":       {"THRIWE_BENEFITS_PACKAGE"},
	"LOYLTY":       {"GIFT_CARD", "CHARITY"},
	"QWIKCILVER":   {"GIFT_CARD"},
	"IN_HOUSE":     {"CMS_COUPON"},
	"DREAMFOLKS":   {"LOUNGE_ACCESS"},
	"DPANDA":       {"EXTERNAL_VENDOR"},
	"POSHVINE":     {"EXTERNAL_VENDOR"},
	"OTHERS":       {"SUBSCRIPTION", "CHARITY", "COUPON", "PHYSICAL_MERCHANDISE"},
}
var vendorToVendorMetaMap = map[string]string{
	"THRIWE":     "thriwe_vendor_offer_metadata",
	"LOYLTY":     "loylty_vendor_offer_metadata",
	"QWIKCILVER": "qwikcilver_vendor_offer_metadata",
	"IN_HOUSE":   "in_house_vendor_offer_metadata",
}

const thriweBenefitPkgNameCharacterLimit = 40

// nolint: funlen
func (o OfferCatalogService) validateCreateOfferRequest(ctx context.Context, req *casperPb.CreateOfferRequest) []*casperPb.CreateOfferResponse_ValidationFailureInfo {
	var validationErrors []string
	reqJson, err := protojson.Marshal(req)
	if err != nil {
		validationErrors = append(validationErrors, fmt.Sprintf("error in marshalling CreateOfferRequest, err: %s", err))
	} else if strings.Contains(string(reqJson), constants.DeprecatedBasePathForVisualAssets) {
		validationErrors = append(validationErrors, fmt.Sprintf("base path for visual assets is deprecated, please use %s", constants.BasePathForVisualAssets))
	}

	// OfferType and VendorName Mapping validation
	// some offline vendors are categorized as OTHERS and checking the eligible  OfferType in else loop
	if lo.Contains(lo.Keys(vendorToOfferTypeMap), req.GetVendorName().String()) {
		if !lo.Contains(vendorToOfferTypeMap[req.GetVendorName().String()], req.GetOfferType().String()) {
			validationErrors = append(validationErrors, fmt.Sprintf("invalid offer type for vendor,available OfferType are : %v", vendorToOfferTypeMap[req.GetVendorName().String()]))
		}
	} else {
		if !lo.Contains(vendorToOfferTypeMap["OTHERS"], req.GetOfferType().String()) {
			validationErrors = append(validationErrors, fmt.Sprintf("invalid offer type for vendor,available OfferType are : %v", vendorToOfferTypeMap["OTHERS"]))
		}
	}
	if req.GetVendorName() == casperPb.OfferVendor_THRIWE && len(req.GetVendorOfferMetadata().GetThriweVendorOfferMetadata().GetBenefitsPackageName()) > thriweBenefitPkgNameCharacterLimit {
		validationErrors = append(validationErrors, fmt.Sprintf("thriwe benefits package name exceeds character limit"))
	}
	// VendorName and VendorMetaDate Mapping Validation
	// for other offlineVendors,showing the error in else loop if metadata specified in vendorToVendorMetaMap is used
	if lo.Contains(lo.Keys(vendorToVendorMetaMap), req.GetVendorName().String()) {
		if !strings.Contains(req.GetVendorOfferMetadata().String(), vendorToVendorMetaMap[req.GetVendorName().String()]) {
			validationErrors = append(validationErrors, fmt.Sprintf("invalid vendorMetaData,the %v vendor can have %v in vendorOfferMetaData", req.GetVendorName().String(), vendorToVendorMetaMap[req.GetVendorName().String()]))
		}
	} else {
		for _, reservedVendorMeta := range vendorToVendorMetaMap {
			if strings.Contains(req.GetVendorOfferMetadata().String(), reservedVendorMeta) {
				validationErrors = append(validationErrors, fmt.Sprintf("invalid vendorMetaData,the %v vendor can have offlineVendorOfferMetadata or {} in vendorOfferMetaDate", req.GetVendorName().String()))
				break
			}
		}
	}

	if err := o.validateVendorMetadata(req.GetVendorName(), req.GetVendorOfferMetadata()); err != nil {
		validationErrors = append(validationErrors, err.Error())
	}

	// T&c should not be empty
	if len(req.GetTnc().GetTncList()) == 0 || req.GetTnc().GetTncList()[0] == "" {
		validationErrors = append(validationErrors, "Tncs should not be Empty, please provide the Tncs")
	}
	// validating gift card value or physical merchandise value.
	var offerValue int32
	if req.GetOfferType() == casperPb.OfferType_GIFT_CARD {
		offerValue = req.GetOfferMetadata().GetGiftCardMetadata().GetGiftCardValue()
		if offerValue == 0 {
			validationErrors = append(validationErrors, "giftCard value is not mentioned in giftCard metadata for offerType GIFT_CARD")
		}
	} else if req.GetOfferType() == casperPb.OfferType_PHYSICAL_MERCHANDISE {
		offerValue = req.GetOfferMetadata().GetPhysicalMerchMetadata().GetMerchValue()
		if offerValue == 0 {
			validationErrors = append(validationErrors, "merchValue value is not mentioned in physicalMerchandise metadata for offerType PHYSICAL_MERCHANDISE")
		}
	}
	hasExclusiveTags := lo.Contains(req.GetManualTags(), casperPb.TagName_SALARY_EXCLUSIVE_V2) || lo.Contains(req.GetManualTags(), casperPb.TagName_CREDIT_CARD_EXCLUSIVE) || lo.Contains(req.GetManualTags(), casperPb.TagName_EXCLUSIVE_OFFER)
	isExclusive := hasExclusiveTags || strings.Contains(req.GetOfferAdditionalDetails().GetDisplayConstraintExpression(), "IsUserSalaryProgramActive") || strings.Contains(req.GetOfferAdditionalDetails().GetDisplayConstraintExpression(), "IsUserCreditCardActive")
	if req.GetRedemptionMode() == casperPb.OfferRedemptionMode_FI_COINS && offerValue != 0 && req.GetPrice() != 0 {
		if res := o.validateFiCoinValue(offerValue, req.GetPrice(), isExclusive); !res {
			validationErrors = append(validationErrors, "gift card or physical Merchandise value should not be more than 50% fiCoins spend for Exclusive and 5% fiCoins spend for normal offer.")
		}
	}
	if req.GetOfferAdditionalDetails().GetSegmentExpression() != "" && (req.GetRedemptionMode() == casperPb.OfferRedemptionMode_FI_CARD || req.GetRedemptionMode() == casperPb.OfferRedemptionMode_FI_CREDIT_CARD) {
		validationErrors = append(validationErrors, "card offers does not support segment expression")
	}
	if req.GetOfferAdditionalDetails().GetSegmentExpression() != "" {
		isMemberOfExpressionsRes, err := o.segmentClient.IsMemberOfExpressions(ctx, &segmentPb.IsMemberOfExpressionsRequest{
			ActorId:              "",
			SegmentIdExpressions: []string{req.GetOfferAdditionalDetails().GetSegmentExpression()},
			LatestBy:             timestampPb.Now(),
		})
		if rpcErr := epifigrpc.RPCError(isMemberOfExpressionsRes, err); rpcErr != nil {
			validationErrors = append(validationErrors, fmt.Sprintf("error validating the segmentExpression, err: %s", rpcErr.Error()))
		}
		if isMemberOfExpressionsRes.GetSegmentExpressionMembershipMap()[req.GetOfferAdditionalDetails().GetSegmentExpression()].GetSegmentExpressionStatus() != segmentPb.SegmentExpressionStatus_OK {
			validationErrors = append(validationErrors, fmt.Sprintf("error in given segmentExpression"))
		}
	}

	// validation to have category tag when redemption mode is fi coins
	if req.GetRedemptionMode() == casperPb.OfferRedemptionMode_FI_COINS && req.GetCategoryTag() == casperPb.CategoryTag_CATEGORY_TAG_UNSPECIFIED {
		validationErrors = append(validationErrors, "category tag should not be unspecified when redemption mode is fi coins")
	}

	if req.GetRedemptionMode() == casperPb.OfferRedemptionMode_FI_COINS && !req.GetOfferAdditionalDetails().GetIsFiPoints().ToBool() {
		validationErrors = append(validationErrors, "is fi points should be true when redemption mode is fi coins")
	}

	if req.GetImages() != nil {
		for _, image := range req.GetImages() {
			if image.GetImageType() == casperPb.ImageType_UNSPECIFIED_IMAGE_TYPE {
				validationErrors = append(validationErrors, fmt.Sprintf("image type should not be unspecified, url: %v", image.GetUrl()))
			}
		}
	}

	if len(validationErrors) != 0 {
		logger.Error(ctx, "validation failures in create offer request", zap.Any(logger.REQUEST, req), zap.Strings("validationFailures", validationErrors))
		var validationFailureInfoList []*casperPb.CreateOfferResponse_ValidationFailureInfo
		for _, errString := range validationErrors {
			validationFailureInfoList = append(validationFailureInfoList, &casperPb.CreateOfferResponse_ValidationFailureInfo{
				FailureMessage: errString,
			})
		}
		return validationFailureInfoList
	}
	return nil
}

func (o OfferCatalogService) validateUpdateOfferDisplayRequest(ctx context.Context, req *casperPb.UpdateOfferDisplayRequest) []*casperPb.CreateOfferResponse_ValidationFailureInfo {
	var validationErrors []string
	reqJson, err := protojson.Marshal(req)
	if err != nil {
		validationErrors = append(validationErrors, fmt.Sprintf("error in marshalling UpdateOfferDisplayRequest, err: %s", err))
	} else if strings.Contains(string(reqJson), constants.DeprecatedBasePathForVisualAssets) {
		validationErrors = append(validationErrors, fmt.Sprintf("base path for visual assets is deprecated, please use %s", constants.BasePathForVisualAssets))
	}

	if req.GetImages() != nil {
		for _, image := range req.GetImages() {
			if image.GetImageType() == casperPb.ImageType_UNSPECIFIED_IMAGE_TYPE {
				validationErrors = append(validationErrors, fmt.Sprintf("image type should not be unspecified, url: %v", image.GetUrl()))
			}
		}
	}

	if req.GetOfferAdditionalDetails() != nil {
		if req.GetOfferAdditionalDetails().GetIsFiPoints() == 0 {
			validationErrors = append(validationErrors, "is fi points should not be unspecified")
		}
	}

	if len(validationErrors) != 0 {
		logger.Error(ctx, "validation failures in update offer display request", zap.Any(logger.REQUEST, req), zap.Strings("validationFailures", validationErrors))
		var validationFailureInfoList []*casperPb.CreateOfferResponse_ValidationFailureInfo
		for _, errString := range validationErrors {
			validationFailureInfoList = append(validationFailureInfoList, &casperPb.CreateOfferResponse_ValidationFailureInfo{
				FailureMessage: errString,
			})
		}
		return validationFailureInfoList
	}
	return nil
}

func (o OfferCatalogService) validateFiCoinValue(offerValue int32, fiCoinPrice float32, isExclusive bool) bool {
	/* gift card value or physical merchandise value should not be more than 50 % for Exclusive offers and
	5% for normal offers*/

	if !o.dyconf.PayloadValidationInfo().OfferValidationInfo().FiCoinConversionValidation().IsEnabled() {
		return true
	}
	if isExclusive {
		return 100*offerValue <= int32(50*fiCoinPrice)
	}
	return 100*offerValue <= int32(5*fiCoinPrice)
}

func (o OfferCatalogService) validateVendorMetadata(vendorName casperPb.OfferVendor, vendorOfferMetadata *casperPb.VendorOfferMetadata) error {
	if vendorName == casperPb.OfferVendor_THRIWE {
		thriweMetadata := vendorOfferMetadata.GetThriweVendorOfferMetadata()

		switch {
		case thriweMetadata.GetExpiryDate() == nil:
			return errors.New("expiry date cannot be empty")
		case thriweMetadata.GetBenefitsPackageId() == "":
			return errors.New("benefits package ID cannot be empty")
		case thriweMetadata.GetBenefitsPackageName() == "":
			return errors.New("benefits package name cannot be empty")
		}
	}
	return nil
}
