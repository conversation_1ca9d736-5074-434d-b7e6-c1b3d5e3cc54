// nolint:goimports
package insurancevendor

import (
	"context"
	"fmt"
	"strings"
	"time"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"go.uber.org/zap"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/salaryprogram/healthinsurance"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	onsurityVgPb "github.com/epifi/gamma/api/vendorgateway/onsurity"
	"github.com/epifi/gamma/salaryprogram/config"
)

type OnsurityVendor struct {
	conf             *config.Config
	userClient       userPb.UsersClient
	onsurityVgClient onsurityVgPb.OnSurityClient
}

const (
	OnsurityAppDeeplink        = "https://onsurity-deeplinks.app.link/Home"
	PlanNameOpalFam125         = "Opal Fam 125"
	PlanNameDiamondLite        = "Diamond Lite"
	PlanNameDiamondLite2       = "Diamond Lite 2"
	PlanNameDiamondLite4       = "Diamond Lite 4"
	PlanNameDiamondPlus1       = "Diamond Plus 1"
	PlanNameRubyFam125GPA      = "Ruby Fam 125 GPA"
	PlanNameOpalFam125GPA      = "Opal Fam 125 GPA"
	PlanType1A                 = "1A"
	PlanType2A                 = "2A"
	PlanType2A2C               = "2A+2C"
	OnsurityPolicyCancelReason = "Discontinue plan"
)

type HealthInsurancePlanInfo struct {
	Name string
	Type string
}

func GetHealthInsurancePolicyTypeToOnsurityPlanInfoMapping() map[healthinsurance.HealthInsurancePolicyType]HealthInsurancePlanInfo {
	return map[healthinsurance.HealthInsurancePolicyType]HealthInsurancePlanInfo{
		healthinsurance.HealthInsurancePolicyType_BASE_HEALTH_INSURANCE:      {Name: PlanNameOpalFam125, Type: PlanType1A},
		healthinsurance.HealthInsurancePolicyType_ONSURITY_OPD_WELLNESS_1A:   {Name: PlanNameDiamondLite, Type: PlanType1A},
		healthinsurance.HealthInsurancePolicyType_ONSURITY_OPD_WELLNESS_2A:   {Name: PlanNameDiamondLite2, Type: PlanType2A2C},
		healthinsurance.HealthInsurancePolicyType_ONSURITY_OPD_WELLNESS_2A2C: {Name: PlanNameDiamondLite4, Type: PlanType2A2C},
		healthinsurance.HealthInsurancePolicyType_ONSURITY_DIAMOND_PLUS_1A:   {Name: PlanNameDiamondPlus1, Type: PlanType1A},
		healthinsurance.HealthInsurancePolicyType_ONSURITY_GHI_GPA_RUBY_1A:   {Name: PlanNameRubyFam125GPA, Type: PlanType1A},
		healthinsurance.HealthInsurancePolicyType_ONSURITY_GHI_GPA_OPAL_1A:   {Name: PlanNameOpalFam125GPA, Type: PlanType1A},
	}
}

func GetPlanNameToHealthInsurancePolicyType() map[string]healthinsurance.HealthInsurancePolicyType {
	return map[string]healthinsurance.HealthInsurancePolicyType{
		"OPAL_FAM_125": healthinsurance.HealthInsurancePolicyType_BASE_HEALTH_INSURANCE,
		// OPAL_FAM_8 is not in use as of now, but keeping it for future reference
		"OPAL_FAM_8":       healthinsurance.HealthInsurancePolicyType_BASE_HEALTH_INSURANCE,
		"DIAMOND_LITE":     healthinsurance.HealthInsurancePolicyType_ONSURITY_OPD_WELLNESS_1A,
		"DIAMOND_LITE_2":   healthinsurance.HealthInsurancePolicyType_ONSURITY_OPD_WELLNESS_2A,
		"DIAMOND_LITE_4":   healthinsurance.HealthInsurancePolicyType_ONSURITY_OPD_WELLNESS_2A2C,
		"DIAMOND_PLUS_1":   healthinsurance.HealthInsurancePolicyType_ONSURITY_DIAMOND_PLUS_1A,
		"RUBY_FAM_125_GPA": healthinsurance.HealthInsurancePolicyType_ONSURITY_GHI_GPA_RUBY_1A,
		"OPAL_FAM_125_GPA": healthinsurance.HealthInsurancePolicyType_ONSURITY_GHI_GPA_OPAL_1A,
	}
}

func NewOnsurityVendor(conf *config.Config, userClient userPb.UsersClient, onsurityVgClient onsurityVgPb.OnSurityClient) *OnsurityVendor {
	return &OnsurityVendor{conf: conf, userClient: userClient, onsurityVgClient: onsurityVgClient}
}

func (o *OnsurityVendor) GetPolicyPurchaseUrl(ctx context.Context, policyIssuanceReq *healthinsurance.HealthInsurancePolicyIssuanceRequest) (PolicyPurchaseInfo, error) {
	userResp, userErr := o.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{
			ActorId: policyIssuanceReq.GetActorId(),
		},
	})
	if te := epifigrpc.RPCError(userResp, userErr); te != nil {
		logger.Error(ctx, "error while fetching user profile from user service", zap.Error(userErr), zap.String(logger.ACTOR_ID_V2, policyIssuanceReq.GetActorId()))
		return nil, errors.Wrap(te, "error while fetching user profile from user service")
	}
	userProfile := userResp.GetUser().GetProfile()
	return &OnsurityPolicyPurchaseInfo{
		Name:        userProfile.GetPanName(),
		DateOfBirth: userProfile.GetDateOfBirth(),
		PhoneNumber: userProfile.GetPhoneNumber(),
		Gender:      userProfile.GetKycGender(),
	}, nil
}

func (o *OnsurityVendor) GetIssuedPoliciesUrl(ctx context.Context, actorId string) (string, error) {
	return OnsurityAppDeeplink, nil
}

func (o *OnsurityVendor) CancelPolicyAutoRenewal(ctx context.Context, vendorPolicyId string, actorId string, healthInsPolicyType healthinsurance.HealthInsurancePolicyType) (*NewPolicyCancelResponse, error) {
	userResp, userErr := o.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if te := epifigrpc.RPCError(userResp, userErr); te != nil {
		logger.Error(ctx, "error while fetching user profile from user service in CancelPolicyAutoRenewal", zap.Error(userErr), zap.String(logger.ACTOR_ID_V2, actorId))
		return nil, errors.Wrap(te, "error while fetching user profile from user service in CancelPolicyAutoRenewal")
	}
	userPhoneNum := userResp.GetUser().GetProfile().GetPhoneNumber().ToStringNationalNumber()
	if len(userPhoneNum) == 0 {
		return nil, fmt.Errorf("phone number found to be empty for actorId: %s in CancelPolicyAutoRenewal", actorId)
	}

	memberPolicyDetailsRes, err := o.onsurityVgClient.DeactivateMemberApi(ctx, &onsurityVgPb.DeactivateMemberApiRequest{
		Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_ONSURITY},
		Member: &onsurityVgPb.Member{
			PhoneNumber:   userPhoneNum,
			PlanName:      GetHealthInsurancePolicyTypeToOnsurityPlanInfoMapping()[healthInsPolicyType].Name,
			PolicyType:    GetHealthInsurancePolicyTypeToOnsurityPlanInfoMapping()[healthInsPolicyType].Type,
			LeavingReason: OnsurityPolicyCancelReason,
			LeavingDate:   strings.ReplaceAll(time.Now().Format("02-01-2006"), "-", "/"),
		},
	})
	rpcErr := epifigrpc.RPCError(memberPolicyDetailsRes, err)
	return &NewPolicyCancelResponse{policyCancelResponseData: memberPolicyDetailsRes}, rpcErr
}

func (o *OnsurityVendor) GetPolicyPurchaseStatusAndDetails(ctx context.Context, vendorRequestId string, vendorPolicyId string) (*PolicyPurchaseDetails, error) {
	return nil, errors.New("unimplemented GetPolicyPurchaseStatusAndDetails method")
}

func (o *OnsurityVendor) ValidatePurchaseVerificationCallback(ctx context.Context, policyIssuanceReq *healthinsurance.HealthInsurancePolicyIssuanceRequest, verificationReqMetadata *healthinsurance.PolicyPurchaseVerificationRequestMetadata) error {
	return errors.New("unimplemented ValidatePurchaseVerificationCallback method")
}

func (o *OnsurityVendor) IssueNewPolicy(ctx context.Context, newPolicyIssuanceReq *healthinsurance.IssueNewPolicyRequest) (*NewPolicyIssuanceRequestDetails, error) {
	gender, genderConversionErr := convertGenderToStringOnsurity(newPolicyIssuanceReq.GetGender())
	if genderConversionErr != nil {
		return nil, genderConversionErr
	}
	healthInsurancePlanInfo, ok := GetHealthInsurancePolicyTypeToOnsurityPlanInfoMapping()[newPolicyIssuanceReq.GetPolicyType()]
	if !ok {
		return nil, fmt.Errorf("couldn't find the onsurity plan info given onsurity policy type: %v", newPolicyIssuanceReq.GetPolicyType())
	}
	memberInvitationRes, err := o.onsurityVgClient.MemberInvitationApi(ctx, &onsurityVgPb.MemberInvitationAPIRequest{
		Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_ONSURITY},
		Employee: &onsurityVgPb.EmployeeDetails{
			PhoneNumber: newPolicyIssuanceReq.GetPhoneNumber().ToStringNationalNumber(),
			Name:        newPolicyIssuanceReq.GetName().ToString(),
			Gender:      gender,
			Dob:         fmt.Sprintf("%v/%v/%v", fmt.Sprintf("%02d", newPolicyIssuanceReq.GetDateOfBirth().GetDay()), fmt.Sprintf("%02d", newPolicyIssuanceReq.GetDateOfBirth().GetMonth()), newPolicyIssuanceReq.GetDateOfBirth().GetYear()),
		},
		HealthPlan: &onsurityVgPb.HealthPlan{
			PlanName:   healthInsurancePlanInfo.Name,
			PolicyType: healthInsurancePlanInfo.Type,
		},
	})
	rpcErr := epifigrpc.RPCError(memberInvitationRes, err)
	return &NewPolicyIssuanceRequestDetails{OnsurityPolicyIssuanceRequestDetails: &OnsurityPolicyIssuanceRequestDetails{
		PolicyIssuanceRequestData: memberInvitationRes,
	}}, rpcErr
}

func (o *OnsurityVendor) GetIssuedPolicyDetails(ctx context.Context, actorId string) (*IssuedPolicyDetails, error) {
	userResp, userErr := o.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if te := epifigrpc.RPCError(userResp, userErr); te != nil {
		logger.Error(ctx, "error while fetching user profile from user service", zap.Error(userErr), zap.String(logger.ACTOR_ID_V2, actorId))
		return nil, errors.Wrap(te, "error while fetching user profile from user service")
	}
	userProfile := userResp.GetUser().GetProfile().GetPhoneNumber().ToStringNationalNumber()
	if len(userProfile) == 0 {
		return nil, fmt.Errorf("phone number found to be empty for actorId: %s", actorId)
	}

	memberPolicyDetailsRes, err := o.onsurityVgClient.MemberDetailsApi(ctx, &onsurityVgPb.MemberDetailsApiRequest{
		Header:       &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_ONSURITY},
		PhoneNumbers: []string{userProfile},
	})
	if rpcErr := epifigrpc.RPCError(memberPolicyDetailsRes, err); rpcErr != nil {
		return nil, rpcErr
	}

	issuedPolicyRedirectionUrl, err := o.GetIssuedPoliciesUrl(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error while fetching issued policy url", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
	}

	return &IssuedPolicyDetails{OnsurityIssuedPolicyDetails: &OnsurityIssuedPolicyDetails{
		IssuedPolicyDetails:          memberPolicyDetailsRes,
		IssuedPoliciesRedirectionUrl: issuedPolicyRedirectionUrl,
		PrimaryMemberUserDetails:     userResp.GetUser().GetProfile(),
	}}, nil
}

var _ IVendor = &OnsurityVendor{}

func convertGenderToStringOnsurity(gender types.Gender) (string, error) {
	switch gender {
	case types.Gender_MALE:
		return "Male", nil
	case types.Gender_FEMALE:
		return "Female", nil
	default:
		return "", errors.New("gender has not been specified correctly")
	}
}
