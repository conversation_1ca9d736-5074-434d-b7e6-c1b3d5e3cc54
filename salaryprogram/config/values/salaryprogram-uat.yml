Application:
  Environment: "uat"
  Name: "salaryprogram"

Server:
  Ports:
    GrpcPort: 8084
    GrpcSecurePort: 9516
    HttpPort: 9999

SalaryProgramDb:
  AppName: "salaryprogram"
  StatementTimeout: 5s
  Name: "salaryprogram"
  EnableDebug: false
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.uat-common-cache-redis.ud7kyq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 0

SalaryIdentificationOrderUpdateEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-salaryprogram-order-update-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 11
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 500
        Period: 1m
    Namespace: "salaryprogram"

SalaryLiteRecurringPaymentActionUpdateEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-salaryprogram-recurring-payment-action-update-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 500
        Period: 1m
    Namespace: "salaryprogram"

OnboardingStageUpdateEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-salaryprogram-onboarding-stage-update-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 500
        Period: 1m
    Namespace: "salaryprogram"

SalaryStatusUpdateEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-salaryprogram-salary-status-update-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 250
        Period: 30s
    Namespace: "salaryprogram"

EmploymentUpdateEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-salary-program-employment-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 500
        Period: 1m
    Namespace: "salaryprogram"


SalaryTxnDetectionEventPublisher:
  TopicName: "uat-salaryprogram-salary-txn-detection-topic"

SalaryProgramStatusUpdateEventPublisher:
  TopicName: "uat-salaryprogram-status-update-topic"

SalaryProgramNotificationCustomDelayPublisher:
  DestQueueName: "uat-salaryprogram-notification-delay-queue"
  OrchestratorSqsPublisher:
    QueueName: "uat-custom-delay-orchestrator-queue"

SalaryStatusUpdateEventSqsCustomDelayPublisher:
  DestQueueName: "uat-salaryprogram-salary-status-update-event-consumer-queue"
  OrchestratorSqsPublisher:
    QueueName: "uat-custom-delay-orchestrator-queue"

SalaryIdentificationOrderUpdateEventSqsPublisher:
  QueueName: "uat-salaryprogram-order-update-consumer-queue"

SalaryProgramNotificationSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-salaryprogram-notification-delay-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 5
      TimeUnit: "Minute"

HealthInsurancePollPolicyPurchaseStatusEventSqsPublisher:
  QueueName: "uat-salaryprogram-healthinsurance-poll-policy-purchase-status-queue"

HealthInsurancePollPolicyPurchaseStatusEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-salaryprogram-healthinsurance-poll-policy-purchase-status-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 30
      MaxAttempts: 30
      TimeUnit: "Second"

HealthInsurancePolicyIssuanceCompletionEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-salaryprogram-healthinsurance-policy-issuance-completion-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 30
      MaxAttempts: 30
      TimeUnit: "Second"

SalaryCxOpsSalaryVerEligibilityRefreshOrderUpdateEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-salaryprogram-ops-salary-ver-eligibility-refresh-order-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 11
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 500
        Period: 30s
    Namespace: "salaryprogram"

SalaryCxSalaryDetectionEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-salaryprogram-salary-detection-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 500
        Period: 1m
    Namespace: "salaryprogram"

EmployerPiMappingUpdateEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-employer-pi-mapping-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 500
        Period: 1m
    Namespace: "salaryprogram"

EmpIdToMinReqAmountForSalaryTxnDetectionInRs :
  "e3a90b86-1d0b-4385-ad0a-9fd64f964e23" : 5000 # Guru & Jana Employer

Secrets:
  Ids:
    DbCredentials: "uat/rds/postgres"
    RiskcovrySecrets: "uat/savings/riskcovry-secrets"
    RudderWriteKey: "uat/rudder/internal-writekey"

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Aws:
  Region: "ap-south-1"

Flags:
  TrimDebugMessageFromStatus: false

AaSalaryOrderUpdateEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-salaryprogram-aa-salary-order-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 5
      TimeUnit: "Minute"

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true
