Flags:
  TrimDebugMessageFromStatus: true

SalaryProgramDb:
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

SalaryProgramAwsBucket:
  BucketName: "epifi-salaryprogram"

RegFlowVersionToMandatoryStagesMap:
  1: # REGISTRATION_FLOW_VERSION_V1
    # stages are present in order in which they should be completed.
    - 1 # REGISTRATION_STAGE_EMPLOYMENT_CONFIRMATION
    - 2 # REGISTRATION_STAGE_FULL_KYC_COMPLETION

RegFlowTypeToMandatoryStagesMap:
  # stages are present in order in which they should be completed.
  1: # REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE
    - 1 # REGISTRATION_STAGE_EMPLOYMENT_CONFIRMATION
    - 2 # REGISTRATION_STAGE_FULL_KYC_COMPLETION
  2: # REGISTRATION_FLOW_TYPE_AA_SALARY
    - 3 # REGISTRATION_STAGE_AA_ACCOUNT_CONNECTED
    - 4 # REGISTRATION_STAGE_AA_ACCOUNT_INCOME_ESTIMATED

SalaryProgramNewActivationDuration: 1440h # 60days
AaSalaryNewActivationDuration: 720h # 30days
MinReqDurationSinceLastActivationForShowingGracePeriod: 960h # 40 days
AaSalaryMonthlyRewardEvaluationDate: 4
AaSalaryMaxRewardActivationAdvanceMonths: 2
MinReqDurationToCompleteASalaryCycle: 600h # 25days
ShouldRoundOffActivationTillTimeToEndOfDay: true
AaSalaryMinDurationSinceActivationToEnterGrace: 720h # 30 days

CurrentRegistrationFlowVersion : 1 # REGISTRATION_FLOW_VERSION_V1

GetRemitterInfoFromVgForTxnsBeforeTime : "2022-07-20T23:59:59+05:30"

MaxPossibleDelayForRemitterInfoAvailabilitySinceTxnTime: 1h
UpdateWhitelistedB2bUserBankAccDetailsAfterDuration: 5m # 3 days
DiscountedSalaryPercentageForReverification: 30
MinRequiredSalaryAmountForReverification: 10000

Tracing:
  Enable: false

NonIncomeRelatedTxnCategoryOntologyIds: [
  "cf22e28c-60f8-4a11-8d35-5dfca7d2839d",
  "42dc03e7-3756-4a4f-8e7e-7d625a9ca02e",
  "ca472d29-345e-42df-9e72-f049086635d8",
  "65ae8b75-258d-4176-838a-68bf79bdf2a0",
  "a9e3e865-4ca7-4866-8cc6-7707088843f4",
  "da014408-87c2-46ee-9888-5a2b3cb12566",
  "a4612765-9522-43f5-ac40-015982cb2272",
  "8725f90a-a092-46d8-8726-f142370db769",
  "3886d8a3-1e5e-4143-a750-0f824d300e1d",
  "382e94ee-d0f6-4d36-8cb7-ee70c11580b2",
]

EmployerIdBlacklistForSalaryTxnVerification: []

SalaryProgramNotifications:
  SalaryBenefitsActivatedNotification:
    Title: "Salary benefits activated ✅"
    Body: "Congrats! You've upgraded to a salary account. Tap to view benefits"
    Deeplink:
      Screen: 301 # salary benefits landing page
    DelayDurations: [1s]
  SalaryLiteBenefitsActivatedNotification:
    Title: "Benefits ▶️ Activated"
    Body: "Congratulations, your Salary Lite benefits are now active. Explore now"
    Deeplink:
      Screen: 301 # salary benefits landing page
    DelayDurations: [ 1s ]
  SalaryLiteMandateStatusSuccessNotification:
    Title: "Salary Lite Registration ✅"
    Body: "Your salary benefits will be activated once the amount is credited to your Savings Account."
    Deeplink:
      Screen: 301 # salary benefits landing page
    DelayDurations: [ 1s ]
  SalaryBenefitsInactivatedNotification:
    Title: "Salary benefits appear inactive!"
    Body: "Your salary has not arrived yet. Once it does, benefits will get activated 🎁"
    Deeplink:
      Screen: 301 # salary benefits landing page
    DelayDurations: [10s]
  SalaryLiteBenefitsInactivatedNotification:
    Title: "🚨 Benefits: Expired 🚨"
    Body: "Your Salary Lite benefits have expired. You may have to set up a new mandate or upgrade to the Salary Plan to continue enjoying these benefits"
    Deeplink:
      Screen: 301 # salary benefits landing page
    DelayDurations: [10s]
  SalaryBenefitsGracePeriodStartedNotification:
    # notification is not sent for full salary grace period, only rudder event is sent from notification service
    DelayDurations: [10s]
  SalaryLiteBenefitsGracePeriodStartedNotification:
    Title: "Benefits expiring 🚨"
    Body: "Your Salary Lite benefits will expire on %s. Check the mandate in your %s account to continue enjoying benefits" # Placeholders are corresponding to: activeTillDate, bankName
    Deeplink:
      Screen: 301 # salary benefits landing page
    DelayDurations: [10s]
  TxnVerificationReqRejectedNotification:
    Title: "We could not verify your salary details"
    Body: "Benefits will only get activated after your salary arrives in this Federal Bank account 🔍"
    Deeplink:
      Screen: 301 # salary benefits landing page
    DelayDurations: [1s]
  RegCompleteBenefitsNotActiveNotification:
    Title: "Get. Salary. Benefits. Now⏳"
    Body: "1) Share account details with your HR.\n2) Upgrade to a salary account.\n3) Gain benefits!"
    Deeplink:
      Screen: 301 # salary benefits landing page
    DelayDurations: [168h] # [7days]
  RegCompleteBenefitsNotActiveNotificationV1:
    Title: "You're soooo close to salary benefits 🎁"
    Body: "Want them? Share your Federal Bank account details with the HR team"
    Deeplink:
      Screen: 301 # salary benefits landing page
    DelayDurations: [360h] # [15days]
  RegCompleteBenefitsNotActiveNotificationV2:
    Title: "You've got (to) mail! 📩"
    Body: "Share your account details with your HR and get salary benefits!"
    Deeplink:
      Screen: 301 # salary benefits landing page
    DelayDurations: [672h] # [28days]
  EmpVerificationDoneKycNotInitNotification:
    Title: "You're almost there! 🏁"
    Body: "Want to unlock salary benefits quickly? Tap to complete your KYC"
    Deeplink:
      Screen: 304 # VKYC_STATUS_SCREEN
      VkycStatusScreenOptions:
        EntryPoint: 11 # salary program entry point
    DelayDurations: [72h] # [3days]
  RegInitiatedEmpVerificationNotDone:
    Title: "📝 Today's task: get salary rewards"
    Body: "Get benefits up to Rs 30,000 every year. Upgrade to Fi Salary Program!"
    Deeplink:
      Screen: 300 # salary intro page
    DelayDurations: [15m] # [15minutes]
  RegInitiatedEmpVerificationNotDoneV1:
    Title: "Make your salary earn for you! 😌"
    Body: "Upgrade to Fi salary program and earn rewards worth Rs 6000 every year!"
    Deeplink:
      Screen: 300 # salary intro page
    DelayDurations: [72h] # [3days]
  SalaryBankAccDetailsToBeUpdatedOnHrms:
    Title: "Fi Salary Program : You are eligible ✅"
    Body: "We’ll be moving your salary account to Fi in 3 days. Please update your details in case linked with EMIs & other payments."
    Deeplink:
      Screen: 301 # salary benefits landing page

HealthInsuranceConfig:
  NewPolicyIssuanceRequestExpiryDuration: 10m
  MaxPossiblePolicyIssuanceDelayForInProgressRequest: 5m
  RiskcovryVendorConfig:
    SSOBaseUrl: "https://fimoney.uat-riskcovry.com"
    SSOLinkExpiryDuration: 5m
    RiskcovryProductDetails:
      SUPER_TOP_UP_INSURANCE:
        PackageCode: "FIMONEY_SUPER_TOPUP"
        ProductCode: "CIGNA_SUPERTOPUP_FI"
      BASE_HEALTH_INSURANCE:
        PackageCode: "CIGNA_GHI_FI"
        ProductCode: "GHI_CIGNA_FI"

CancelledChequeSuperImposeTextsDetails:
  ACCOUNT_NUMBER:
    FontSize: 20
    MaxTextWidth: 240
    DrawPosition:
      PosX: 240
      PosY: 282
      Ax: 0.5
      Ay: 0.5
  ACCOUNT_HOLDER_NAME:
    FontSize: 20
    MaxTextWidth: 248
    DrawPosition:
      PosX: 1020
      PosY: 376
      Ax: 0.5
      Ay: 0.5

SalaryProgramAwsBucketFiles:
  CancelledChequePath: "cancelled_cheque.png"
  WhatIsFiPdfPath : "what_is_fi.pdf"

SalaryProgramEmailNotifications:
  RegCompletedEmailNotification:
    FromEmailName: "Fi Salary Benefits"
    FromEmailId: "<EMAIL>"
    AttachmentDisplayName:
      CANCELLED_CHEQUE: "Cancelled cheque.png"
      WHAT_IS_FI: "What is Fi.pdf"
    DelayDurations: [1s]

VerificationFailureReasonsCategoryToSubCategoriesMap:
  - 1: # EMPLOYER_IS_NOT_AN_ORG
      - 1 # EMPLOYER_NAME_SAME_AS_THE_ACCOUNT_HOLDER
      - 2 # EMPLOYER_NAME_AS_ANOTHER_INDIVIDUAL
  - 2: # REMITTER_IS_NOT_AN_ENTITY
      - 3 # REMITTER_NAME_SAME_AS_THE_ACCOUNT_HOLDER
      - 4 # REMITTER_NAME_AS_ANOTHER_INDIVIDUAL
  - 3: # NOT_PART_OF_EMPLOYER_DB
      - 5 # NOT_VERIFICABLE_BY_GSTIN_ALSO
      - 9 # OTHERS
  - 4: # INSUFFICIENT_INFO
      - 6 # REMITTER_NAME_NOT_AVAILABLE
      - 7 # PARTIAL_REMITTER_NAME
      - 8 # PAYMENT_PROTOCOL_IS_UNSPECIFIED
      - 9 # OTHERS
      - 10 # MULTIPLE_REMITTER_NAME
  - 5: # EMPLOYER_IS_A_FINTECH_COMPANY
      - 9 # OTHERS
      - 11 # MERCHANT_ACCOUNT_TRANSFER
  - 6: # REMITTER_IS_A_FINTECH_COMPANY
      - 11 # MERCHANT_ACCOUNT_TRANSFER
  - 7: # NOT_A_SALARY_TRANSACTION
      - 12 # EXPENSE_REIMBURSEMENT
      - 13 # PF_WITHDRAWAL
      - 9 # OTHERS
  - 8: # TRANSACTION_SOURCE_ISSUE
      - 14 # REMITTER_IS_A_LENDING_COMPANY
      - 15 # REMITTER_IS_A_FINANCIAL_INSTITUTION
      - 16 # INVESTMENT_WITHDRAWAL

SalaryProgramNudgeConfigs:
  RegistrationStartedButNotCompleted:
    NudgeId: ""
    NudgeDelay: 0s
  RegistrationCompletedButStatusInactive:
    NudgeId: ""
    NudgeDelay: 0s

SalaryProgramCxConfig:
  GetPossibleActorsForProactiveSalaryVerByOpsFilters:
    MinReqDurationFromLastVerificationForVerifiedByOpsCases: 600h #25days
    MinReqDurationFromLastVerificationForVerifiedBySystemCases: 840h #35days
    MaxAllowedDurationSinceRegCompletedForRegisteredAndActivationPendingCases: 1440h #60 days

SalaryTxnVerifierConfig:
  AllowedTxnPaymentProtocols:
    - 0 # UNSPECIFIED
    - 1 # INTRA_BANK
    - 2 # NEFT
    - 3 # IMPS
    - 4 # RTGS
  TxnProtocolsRequireRemitterInfoFromVg:
    - 1 # INTRA_BANK
    - 3 # IMPS
  FetchTxnRemitterInfoFromVg: false
  PerformUanLookupCheckForSoftBlacklistedEmployers: false
  MaxAllowedDurationSinceTxnTimeToFetchRemitterInfoFromVg: 120h # 5 days

SalaryLiteConfig:
  MandateActiveTimeInMonths: 36
  MinRequiredDelayForMandateStartTimeSinceCreation: 15s # keeping small value for testing
  NewActivationDuration: 1440h #60 days
  SalaryLiteMandateNonProdSimulatedRemitterIfscCode: "ONMG"

DynamicElementConfig:
  PromoWidgetConfig:
    SalaryB2BUserSegmentedPromoWidget:
      6593dcc0-f8a4-4d03-8453-a4c070cfb7a1:
    SalaryB2BUserDefaultPromoWidget:
      BenefitsActive:
        IsVisible: false
        ThreePointsWidgetRightHorizontalFlyerConfigs:
          - Point-1:
              PreText: "Get early salary"
              Text: "Up to ₹50K"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/Lighting-bold-three-point-widget.png"
              Deeplink:
                Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          - Point-2:
              PreText: "Free Health cover"
              Text: "Up to ₹20L"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/health-sign-bold-three-point-widget.png"
              Deeplink:
                Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          - Point-3:
              PreText: "Cash back"
              Text: "Up to ₹6,000"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/gift-box-three-point-widget.png"
              Deeplink:
                Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
        ThreePointsWidgetLeftVerticalFlyerConfig:
          Icon: "https://epifi-icons.pointz.in/salaryprogram/salary-benifits-three-point-widget.png"
          CtaDeeplink:
            Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          CtaText: "Upgrade now"
          IconBgColor: "#FFFAE0"
          IconFontColor: "#B88735"
          ContainerBgColor: "#FFFAE0"
        ThreePointsWidgetTitle: "Get up to ₹30K in benefits"
        FourPointsWidgetTitle: "Featured"
        FourPointsWidgetTextVisualElementCardTopSectionConfig:
          Icon: "https://epifi-icons.pointz.in/salaryprogram/salary-benifits-four-point-widge.png"
          CtaDeeplink:
            Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          CtaText: "Get a free upgrade"
          CtaFontColor: "#B88735"
          CtaBgColor: "#FFFAE0"
          ContainerBgColor: "#FFFAE0"
        FourPointsWidgetTextVisualElementCardMiddleSectionConfigs:
          - Point-1:
              PreText: "Get early salary"
              Text: "Up to ₹50K"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/Lighting-bold-four-point-widget-v1.png"
          - Point-2:
              PreText: "Free Health cover"
              Text: "Up to ₹20L"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/health-sign-bold-four-point-widget-v1.png"
          - Point-3:
              PreText: "Cash back"
              Text: "Up to ₹6,000"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/gift-box-four-point-widget-v1.png"
          - Point-4:
              PreText: "Savings Account"
              Text: "Zero balance"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/savings-bank-four-point-widget.png-v1.png"
      RegCompletedBenefitsInActive:
        IsVisible: true
        ThreePointsWidgetRightHorizontalFlyerConfigs:
          - Point-1:
              PreText: "Get early salary"
              Text: "Up to ₹50K"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/Lighting-bold-three-point-widget.png"
              Deeplink:
                Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          - Point-2:
              PreText: "Free Health cover"
              Text: "Up to ₹20L"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/health-sign-bold-three-point-widget.png"
              Deeplink:
                Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          - Point-3:
              PreText: "Cash back"
              Text: "Up to ₹6,000"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/gift-box-three-point-widget.png"
              Deeplink:
                Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
        ThreePointsWidgetLeftVerticalFlyerConfig:
          Icon: "https://epifi-icons.pointz.in/salaryprogram/salary-benifits-three-point-widget.png"
          CtaDeeplink:
            Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          CtaText: "Upgrade now"
        ThreePointsWidgetTitle: "Get up to ₹30K in benefits"
        FourPointsWidgetTitle: "Featured"
        FourPointsWidgetTextVisualElementCardTopSectionConfig:
          Icon: "https://epifi-icons.pointz.in/salaryprogram/salary-benifits-four-point-widge.png"
          CtaDeeplink:
            Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          CtaText: "Get a free upgrade"
          CtaFontColor: "#B88735"
          CtaBgColor: "#FFFAE0"
          ContainerBgColor: "#FFFAE0"
        FourPointsWidgetTextVisualElementCardMiddleSectionConfigs:
          - Point-1:
              PreText: "Get early salary"
              Text: "Up to ₹50K"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/Lighting-bold-four-point-widget-v1.png"
          - Point-2:
              PreText: "Free Health cover"
              Text: "Up to ₹20L"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/health-sign-bold-four-point-widget-v1.png"
          - Point-3:
              PreText: "Cash back"
              Text: "Up to ₹6,000"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/gift-box-four-point-widget-v1.png"
          - Point-4:
              PreText: "Savings Account"
              Text: "Zero balance"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/savings-bank-four-point-widget.png-v1.png"
      RegNotCompleted:
        IsVisible: true
        ThreePointsWidgetRightHorizontalFlyerConfigs:
          - Point-1:
              PreText: "Get early salary"
              Text: "Up to ₹50K"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/Lighting-bold-three-point-widget.png"
              Deeplink:
                Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          - Point-2:
              PreText: "Free Health cover"
              Text: "Up to ₹20L"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/health-sign-bold-three-point-widget.png"
              Deeplink:
                Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          - Point-3:
              PreText: "Cash back"
              Text: "Up to ₹6,000"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/gift-box-three-point-widget.png"
              Deeplink:
                Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
        ThreePointsWidgetLeftVerticalFlyerConfig:
          Icon: "https://epifi-icons.pointz.in/salaryprogram/salary-benifits-three-point-widget.png"
          CtaDeeplink:
            Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          CtaText: "Upgrade now"
        ThreePointsWidgetTitle: "Get up to ₹30K in benefits"
        FourPointsWidgetTitle: "Featured"
        FourPointsWidgetTextVisualElementCardTopSectionConfig:
          Icon: "https://epifi-icons.pointz.in/salaryprogram/salary-benifits-four-point-widge.png"
          CtaDeeplink:
            Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          CtaText: "Get a free upgrade"
          CtaFontColor: "#B88735"
          CtaBgColor: "#FFFAE0"
          ContainerBgColor: "#FFFAE0"
        FourPointsWidgetTextVisualElementCardMiddleSectionConfigs:
          - Point-1:
              PreText: "Get early salary"
              Text: "Up to ₹50K"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/Lighting-bold-four-point-widget-v1.png"
          - Point-2:
              PreText: "Free Health cover"
              Text: "Up to ₹20L"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/health-sign-bold-four-point-widget-v1.png"
          - Point-3:
              PreText: "Cash back"
              Text: "Up to ₹6,000"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/gift-box-four-point-widget-v1.png"
          - Point-4:
              PreText: "Savings Account"
              Text: "Zero balance"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/savings-bank-four-point-widget.png-v1.png"
      SalaryLiteRegCompleted:
        IsVisible: true
        ThreePointsWidgetRightHorizontalFlyerConfigs:
          - Point-1:
              PreText: "Get early salary"
              Text: "Up to ₹50K"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/Lighting-bold-three-point-widget.png"
              Deeplink:
                Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          - Point-2:
              PreText: "Free Health cover"
              Text: "Up to ₹20L"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/health-sign-bold-three-point-widget.png"
              Deeplink:
                Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          - Point-3:
              PreText: "Cash back"
              Text: "Up to ₹6,000"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/gift-box-three-point-widget.png"
              Deeplink:
                Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
        ThreePointsWidgetLeftVerticalFlyerConfig:
          Icon: "https://epifi-icons.pointz.in/salaryprogram/salary-benifits-three-point-widget.png"
          CtaDeeplink:
            Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          CtaText: "Upgrade now"
        ThreePointsWidgetTitle: "Get up to ₹30K in benefits"
        FourPointsWidgetTitle: "Featured"
        FourPointsWidgetTextVisualElementCardTopSectionConfig:
          Icon: "https://epifi-icons.pointz.in/salaryprogram/salary-benifits-four-point-widge.png"
          CtaDeeplink:
            Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          CtaText: "Get a free upgrade"
          CtaFontColor: "#B88735"
          CtaBgColor: "#FFFAE0"
          ContainerBgColor: "#FFFAE0"
        FourPointsWidgetTextVisualElementCardMiddleSectionConfigs:
          - Point-1:
              PreText: "Get early salary"
              Text: "Up to ₹50K"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/Lighting-bold-four-point-widget-v1.png"
          - Point-2:
              PreText: "Free Health cover"
              Text: "Up to ₹20L"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/health-sign-bold-four-point-widget-v1.png"
          - Point-3:
              PreText: "Cash back"
              Text: "Up to ₹6,000"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/gift-box-four-point-widget-v1.png"
          - Point-4:
              PreText: "Savings Account"
              Text: "Zero balance"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/savings-bank-four-point-widget.png-v1.png"
      SalaryLiteBenefitsActive:
        IsVisible: true
        ThreePointsWidgetRightHorizontalFlyerConfigs:
          - Point-1:
              PreText: "Get early salary"
              Text: "Up to ₹50K"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/Lighting-bold-three-point-widget.png"
              Deeplink:
                Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          - Point-2:
              PreText: "Free Health cover"
              Text: "Up to ₹20L"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/health-sign-bold-three-point-widget.png"
              Deeplink:
                Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          - Point-3:
              PreText: "Cash back"
              Text: "Up to ₹6,000"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/gift-box-three-point-widget.png"
              Deeplink:
                Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
        ThreePointsWidgetLeftVerticalFlyerConfig:
          Icon: "https://epifi-icons.pointz.in/salaryprogram/salary-benifits-three-point-widget.png"
          CtaDeeplink:
            Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          CtaText: "Upgrade now"
        ThreePointsWidgetTitle: "Get up to ₹30K in benefits"
        FourPointsWidgetTitle: "Featured"
        FourPointsWidgetTextVisualElementCardTopSectionConfig:
          Icon: "https://epifi-icons.pointz.in/salaryprogram/salary-benifits-four-point-widge.png"
          CtaDeeplink:
            Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          CtaText: "Get a free upgrade"
          CtaFontColor: "#B88735"
          CtaBgColor: "#FFFAE0"
          ContainerBgColor: "#FFFAE0"
        FourPointsWidgetTextVisualElementCardMiddleSectionConfigs:
          - Point-1:
              PreText: "Get early salary"
              Text: "Up to ₹50K"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/Lighting-bold-four-point-widget-v1.png"
          - Point-2:
              PreText: "Free Health cover"
              Text: "Up to ₹20L"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/health-sign-bold-four-point-widget-v1.png"
          - Point-3:
              PreText: "Cash back"
              Text: "Up to ₹6,000"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/gift-box-four-point-widget-v1.png"
          - Point-4:
              PreText: "Savings Account"
              Text: "Zero balance"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/savings-bank-four-point-widget.png-v1.png"
      SalaryLiteBenefitsActiveInGracePeriod:
        IsVisible: true
        ThreePointsWidgetRightHorizontalFlyerConfigs:
          - Point-1:
              PreText: "Get early salary"
              Text: "Up to ₹50K"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/Lighting-bold-three-point-widget.png"
              Deeplink:
                Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          - Point-2:
              PreText: "Free Health cover"
              Text: "Up to ₹20L"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/health-sign-bold-three-point-widget.png"
              Deeplink:
                Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          - Point-3:
              PreText: "Cash back"
              Text: "Up to ₹6,000"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/gift-box-three-point-widget.png"
              Deeplink:
                Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
        ThreePointsWidgetLeftVerticalFlyerConfig:
          Icon: "https://epifi-icons.pointz.in/salaryprogram/salary-benifits-three-point-widget.png"
          CtaDeeplink:
            Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          CtaText: "Upgrade now"
        ThreePointsWidgetTitle: "Get up to ₹30K in benefits"
        FourPointsWidgetTitle: "Featured"
        FourPointsWidgetTextVisualElementCardTopSectionConfig:
          Icon: "https://epifi-icons.pointz.in/salaryprogram/salary-benifits-four-point-widge.png"
          CtaDeeplink:
            Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          CtaText: "Get a free upgrade"
          CtaFontColor: "#B88735"
          CtaBgColor: "#FFFAE0"
          ContainerBgColor: "#FFFAE0"
        FourPointsWidgetTextVisualElementCardMiddleSectionConfigs:
          - Point-1:
              PreText: "Get early salary"
              Text: "Up to ₹50K"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/Lighting-bold-four-point-widget-v1.png"
          - Point-2:
              PreText: "Free Health cover"
              Text: "Up to ₹20L"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/health-sign-bold-four-point-widget-v1.png"
          - Point-3:
              PreText: "Cash back"
              Text: "Up to ₹6,000"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/gift-box-four-point-widget-v1.png"
          - Point-4:
              PreText: "Savings Account"
              Text: "Zero balance"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/savings-bank-four-point-widget.png-v1.png"
    SalaryB2CUserSegmentedPromoWidget:
      6593dcc0-f8a4-4d03-8453-a4c070cfb7a1:
    SalaryB2CUserDefaultPromoWidget:
      BenefitsActive:
        IsVisible: false
        ThreePointsWidgetRightHorizontalFlyerConfigs:
          - Point-1:
              PreText: "Get early salary"
              Text: "Up to ₹50K"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/Lighting-bold-three-point-widget.png"
              Deeplink:
                Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          - Point-2:
              PreText: "Free Health cover"
              Text: "Up to ₹20L"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/health-sign-bold-three-point-widget.png"
              Deeplink:
                Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          - Point-3:
              PreText: "Cash back"
              Text: "Up to ₹6,000"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/gift-box-three-point-widget.png"
              Deeplink:
                Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
        ThreePointsWidgetLeftVerticalFlyerConfig:
          Icon: "https://epifi-icons.pointz.in/salaryprogram/salary-benifits-three-point-widget.png"
          CtaDeeplink:
            Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          CtaText: "Upgrade now"
          IconBgColor: "#FFFAE0"
          IconFontColor: "#B88735"
          ContainerBgColor: "#FFFAE0"
        ThreePointsWidgetTitle: "Get up to ₹30K in benefits"
        FourPointsWidgetTitle: "Featured"
        FourPointsWidgetTextVisualElementCardTopSectionConfig:
          Icon: "https://epifi-icons.pointz.in/salaryprogram/salary-benifits-four-point-widge.png"
          CtaDeeplink:
            Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          CtaText: "Get a free upgrade"
          CtaFontColor: "#B88735"
          CtaBgColor: "#FFFAE0"
          ContainerBgColor: "#FFFAE0"
        FourPointsWidgetTextVisualElementCardMiddleSectionConfigs:
          - Point-1:
              PreText: "Get early salary"
              Text: "Up to ₹50K"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/Lighting-bold-four-point-widget-v1.png"
          - Point-2:
              PreText: "Free Health cover"
              Text: "Up to ₹20L"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/health-sign-bold-four-point-widget-v1.png"
          - Point-3:
              PreText: "Cash back"
              Text: "Up to ₹6,000"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/gift-box-four-point-widget-v1.png"
          - Point-4:
              PreText: "Savings Account"
              Text: "Zero balance"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/savings-bank-four-point-widget.png-v1.png"
      RegCompletedBenefitsInActive:
        IsVisible: false
        ThreePointsWidgetRightHorizontalFlyerConfigs:
          - Point-1:
              PreText: "Get early salary"
              Text: "Up to ₹50K"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/Lighting-bold-three-point-widget.png"
              Deeplink:
                Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          - Point-2:
              PreText: "Free Health cover"
              Text: "Up to ₹20L"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/health-sign-bold-three-point-widget.png"
              Deeplink:
                Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          - Point-3:
              PreText: "Cash back"
              Text: "Up to ₹6,000"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/gift-box-three-point-widget.png"
              Deeplink:
                Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
        ThreePointsWidgetLeftVerticalFlyerConfig:
          Icon: "https://epifi-icons.pointz.in/salaryprogram/salary-benifits-three-point-widget.png"
          CtaDeeplink:
            Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          CtaText: "Upgrade now"
        ThreePointsWidgetTitle: "Get up to ₹30K in benefits"
        FourPointsWidgetTitle: "Featured"
        FourPointsWidgetTextVisualElementCardTopSectionConfig:
          Icon: "https://epifi-icons.pointz.in/salaryprogram/salary-benifits-four-point-widge.png"
          CtaDeeplink:
            Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          CtaText: "Get a free upgrade"
          CtaFontColor: "#B88735"
          CtaBgColor: "#FFFAE0"
          ContainerBgColor: "#FFFAE0"
        FourPointsWidgetTextVisualElementCardMiddleSectionConfigs:
          - Point-1:
              PreText: "Get early salary"
              Text: "Up to ₹50K"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/Lighting-bold-four-point-widget-v1.png"
          - Point-2:
              PreText: "Free Health cover"
              Text: "Up to ₹20L"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/health-sign-bold-four-point-widget-v1.png"
          - Point-3:
              PreText: "Cash back"
              Text: "Up to ₹6,000"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/gift-box-four-point-widget-v1.png"
          - Point-4:
              PreText: "Savings Account"
              Text: "Zero balance"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/savings-bank-four-point-widget.png-v1.png"
      RegNotCompleted:
        IsVisible: false
        ThreePointsWidgetRightHorizontalFlyerConfigs:
          - Point-1:
              PreText: "Get early salary"
              Text: "Up to ₹50K"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/Lighting-bold-three-point-widget.png"
              Deeplink:
                Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          - Point-2:
              PreText: "Free Health cover"
              Text: "Up to ₹20L"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/health-sign-bold-three-point-widget.png"
              Deeplink:
                Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          - Point-3:
              PreText: "Cash back"
              Text: "Up to ₹6,000"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/gift-box-three-point-widget.png"
              Deeplink:
                Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
        ThreePointsWidgetLeftVerticalFlyerConfig:
          Icon: "https://epifi-icons.pointz.in/salaryprogram/salary-benifits-three-point-widget.png"
          CtaDeeplink:
            Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          CtaText: "Upgrade now"
        ThreePointsWidgetTitle: "Get up to ₹30K in benefits"
        FourPointsWidgetTitle: "Featured"
        FourPointsWidgetTextVisualElementCardTopSectionConfig:
          Icon: "https://epifi-icons.pointz.in/salaryprogram/salary-benifits-four-point-widge.png"
          CtaDeeplink:
            Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          CtaText: "Get a free upgrade"
          CtaFontColor: "#B88735"
          CtaBgColor: "#FFFAE0"
          ContainerBgColor: "#FFFAE0"
        FourPointsWidgetTextVisualElementCardMiddleSectionConfigs:
          - Point-1:
              PreText: "Get early salary"
              Text: "Up to ₹50K"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/Lighting-bold-four-point-widget-v1.png"
          - Point-2:
              PreText: "Free Health cover"
              Text: "Up to ₹20L"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/health-sign-bold-four-point-widget-v1.png"
          - Point-3:
              PreText: "Cash back"
              Text: "Up to ₹6,000"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/gift-box-four-point-widget-v1.png"
          - Point-4:
              PreText: "Savings Account"
              Text: "Zero balance"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/savings-bank-four-point-widget.png-v1.png"
      SalaryLiteRegCompleted:
        IsVisible: false
        ThreePointsWidgetRightHorizontalFlyerConfigs:
          - Point-1:
              PreText: "Get early salary"
              Text: "Up to ₹50K"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/Lighting-bold-three-point-widget.png"
              Deeplink:
                Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          - Point-2:
              PreText: "Free Health cover"
              Text: "Up to ₹20L"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/health-sign-bold-three-point-widget.png"
              Deeplink:
                Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          - Point-3:
              PreText: "Cash back"
              Text: "Up to ₹6,000"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/gift-box-three-point-widget.png"
              Deeplink:
                Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
        ThreePointsWidgetLeftVerticalFlyerConfig:
          Icon: "https://epifi-icons.pointz.in/salaryprogram/salary-benifits-three-point-widget.png"
          CtaDeeplink:
            Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          CtaText: "Upgrade now"
        ThreePointsWidgetTitle: "Get up to ₹30K in benefits"
        FourPointsWidgetTitle: "Featured"
        FourPointsWidgetTextVisualElementCardTopSectionConfig:
          Icon: "https://epifi-icons.pointz.in/salaryprogram/salary-benifits-four-point-widge.png"
          CtaDeeplink:
            Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          CtaText: "Get a free upgrade"
          CtaFontColor: "#B88735"
          CtaBgColor: "#FFFAE0"
          ContainerBgColor: "#FFFAE0"
        FourPointsWidgetTextVisualElementCardMiddleSectionConfigs:
          - Point-1:
              PreText: "Get early salary"
              Text: "Up to ₹50K"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/Lighting-bold-four-point-widget-v1.png"
          - Point-2:
              PreText: "Free Health cover"
              Text: "Up to ₹20L"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/health-sign-bold-four-point-widget-v1.png"
          - Point-3:
              PreText: "Cash back"
              Text: "Up to ₹6,000"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/gift-box-four-point-widget-v1.png"
          - Point-4:
              PreText: "Savings Account"
              Text: "Zero balance"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/savings-bank-four-point-widget.png-v1.png"
      SalaryLiteBenefitsActive:
        IsVisible: true
        ThreePointsWidgetRightHorizontalFlyerConfigs:
          - Point-1:
              PreText: "Get early salary"
              Text: "Up to ₹50K"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/Lighting-bold-three-point-widget.png"
              Deeplink:
                Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          - Point-2:
              PreText: "Free Health cover"
              Text: "Up to ₹20L"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/health-sign-bold-three-point-widget.png"
              Deeplink:
                Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          - Point-3:
              PreText: "Cash back"
              Text: "Up to ₹6,000"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/gift-box-three-point-widget.png"
              Deeplink:
                Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
        ThreePointsWidgetLeftVerticalFlyerConfig:
          Icon: "https://epifi-icons.pointz.in/salaryprogram/salary-benifits-three-point-widget.png"
          CtaDeeplink:
            Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          CtaText: "Upgrade now"
        ThreePointsWidgetTitle: "Get up to ₹30K in benefits"
        FourPointsWidgetTitle: "Featured"
        FourPointsWidgetTextVisualElementCardTopSectionConfig:
          Icon: "https://epifi-icons.pointz.in/salaryprogram/salary-benifits-four-point-widge.png"
          CtaDeeplink:
            Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          CtaText: "Get a free upgrade"
          CtaFontColor: "#B88735"
          CtaBgColor: "#FFFAE0"
          ContainerBgColor: "#FFFAE0"
        FourPointsWidgetTextVisualElementCardMiddleSectionConfigs:
          - Point-1:
              PreText: "Get early salary"
              Text: "Up to ₹50K"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/Lighting-bold-four-point-widget-v1.png"
          - Point-2:
              PreText: "Free Health cover"
              Text: "Up to ₹20L"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/health-sign-bold-four-point-widget-v1.png"
          - Point-3:
              PreText: "Cash back"
              Text: "Up to ₹6,000"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/gift-box-four-point-widget-v1.png"
          - Point-4:
              PreText: "Savings Account"
              Text: "Zero balance"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/savings-bank-four-point-widget.png-v1.png"
      SalaryLiteBenefitsActiveInGracePeriod:
        IsVisible: false
        ThreePointsWidgetRightHorizontalFlyerConfigs:
          - Point-1:
              PreText: "Get early salary"
              Text: "Up to ₹50K"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/Lighting-bold-three-point-widget.png"
              Deeplink:
                Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          - Point-2:
              PreText: "Free Health cover"
              Text: "Up to ₹20L"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/health-sign-bold-three-point-widget.png"
              Deeplink:
                Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          - Point-3:
              PreText: "Cash back"
              Text: "Up to ₹6,000"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/gift-box-three-point-widget.png"
              Deeplink:
                Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
        ThreePointsWidgetLeftVerticalFlyerConfig:
          Icon: "https://epifi-icons.pointz.in/salaryprogram/salary-benifits-three-point-widget.png"
          CtaDeeplink:
            Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          CtaText: "Upgrade now"
        ThreePointsWidgetTitle: "Get up to ₹30K in benefits"
        FourPointsWidgetTitle: "Featured"
        FourPointsWidgetTextVisualElementCardTopSectionConfig:
          Icon: "https://epifi-icons.pointz.in/salaryprogram/salary-benifits-four-point-widge.png"
          CtaDeeplink:
            Screen: "SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION"
          CtaText: "Get a free upgrade"
          CtaFontColor: "#B88735"
          CtaBgColor: "#FFFAE0"
          ContainerBgColor: "#FFFAE0"
        FourPointsWidgetTextVisualElementCardMiddleSectionConfigs:
          - Point-1:
              PreText: "Get early salary"
              Text: "Up to ₹50K"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/Lighting-bold-four-point-widget-v1.png"
          - Point-2:
              PreText: "Free Health cover"
              Text: "Up to ₹20L"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/health-sign-bold-four-point-widget-v1.png"
          - Point-3:
              PreText: "Cash back"
              Text: "Up to ₹6,000"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/gift-box-four-point-widget-v1.png"
          - Point-4:
              PreText: "Savings Account"
              Text: "Zero balance"
              Icon: "https://epifi-icons.pointz.in/salaryprogram/savings-bank-four-point-widget.png-v1.png"
    AASalaryB2CUserDefaultPromoWidget:
      IncomeEstimatedActivationPending:
        IsVisible: true
        ThreePointsWidgetRightHorizontalFlyerConfigs:
          - Point-1:
              PreText: "Enjoy"
              Text: "0 balance a/c"
              Icon: "https://epifi-icons.s3.ap-south-1.amazonaws.com/salaryprogram/Prime_fi_zero_balance.png"
              Deeplink:
                Screen: ""
          - Point-2:
              PreText: "Travel with"
              Text: "0 forex charges"
              Icon: "https://epifi-icons.s3.ap-south-1.amazonaws.com/salaryprogram/Prime_fi_zero_forex_charges.png"
              Deeplink:
                Screen: ""
          - Point-3:
              PreText: "Withdraw"
              Text: "Anytime!"
              Icon: "https://epifi-icons.s3.ap-south-1.amazonaws.com/salaryprogram/Prime_withdraw_anytime.png"
              Deeplink:
                Screen: ""
        ThreePointsWidgetLeftVerticalFlyerConfig:
          Icon: "https://epifi-icons.s3.ap-south-1.amazonaws.com/salaryprogram/Prime_3_Point_vertical_cashback.png"
          CtaDeeplink:
            Screen: ""
          CtaText: " Check access > "
          IconBgColor: "#E4F1F5"
          IconFontColor: "#2D5E6E"
          ContainerBgColor: "#E4F1F5"
        ThreePointsWidgetTitle: "The most rewarding savings account"
        FourPointsWidgetTitle: "Recommended"
        FourPointsWidgetTextVisualElementCardTopSectionConfig:
          Icon: "https://epifi-icons.s3.ap-south-1.amazonaws.com/salaryprogram/Prime_4_point_horizontal_income_estimated_activation_pending.png"
          CtaDeeplink:
            Screen: ""
          CtaText: "Have you made the cut?"
          CtaFontColor: "#2D5E6E"
          CtaBgColor: "#E4F1F5"
          ContainerBgColor: "#E4F1F5"
        FourPointsWidgetTextVisualElementCardMiddleSectionConfigs:
          - Point-1:
              PreText: "Min. balance"
              Text: "Nothing!"
              Icon: "https://epifi-icons.s3.ap-south-1.amazonaws.com/salaryprogram/Prime_4_point_mid_1_min_balance_nothing"
          - Point-2:
              PreText: "Per spend,"
              Text: "3% back"
              Icon: "https://epifi-icons.s3.ap-south-1.amazonaws.com/salaryprogram/Prime_4_point_mid_2_per_spend.png"
          - Point-3:
              PreText: "Enjoy "
              Text: "0 Forex"
              Icon: "https://epifi-icons.s3.ap-south-1.amazonaws.com/salaryprogram/Prime_4_point_mid_3_enjoy_forex.png"
          - Point-4:
              PreText: "Earn"
              Text: "2x Fi-Coins"
              Icon: "https://epifi-icons.s3.ap-south-1.amazonaws.com/salaryprogram/Prime_4_point_mid_4_earn_2x_fi_coins.png"
      IncomeEstimationPending:
        IsVisible: true
        ThreePointsWidgetRightHorizontalFlyerConfigs:
          - Point-1:
              PreText: "Get"
              Text: "2X Fi-Coins"
              Icon: "https://epifi-icons.s3.ap-south-1.amazonaws.com/salaryprogram/prime_fi_coins_vector.png"
              Deeplink:
                Screen: ""
          - Point-2:
              PreText: "Enjoy"
              Text: "0 Forex"
              Icon: "https://epifi-icons.s3.ap-south-1.amazonaws.com/salaryprogram/prime_globe_vector.png"
              Deeplink:
                Screen: ""
          - Point-3:
              PreText: "Savings a/c"
              Text: "0 Balance"
              Icon: "https://epifi-icons.s3.ap-south-1.amazonaws.com/salaryprogram/prime_star_vector.png"
              Deeplink:
                Screen: ""
        ThreePointsWidgetLeftVerticalFlyerConfig:
          Icon: "https://epifi-icons.s3.ap-south-1.amazonaws.com/salaryprogram/prime_widget_icon.png"
          LottieUrl: "https://epifi-icons.s3.ap-south-1.amazonaws.com/salaryprogram/prime_3_percent_cashback_lottie.json"
          CtaDeeplink:
            Screen: ""
          CtaText: "Upgrade now"
          IconBgColor: "#FFFFFF"
          IconFontColor: "#031833"
          ContainerBgColor: "#FFFFFF"
          LottieContainerBgColor: "#00FFFFFF"
        ThreePointsWidgetTitle: "Prime benefits"
        FourPointsWidgetTitle: "Recommended"
        FourPointsWidgetTextVisualElementCardTopSectionConfig:
          Icon: "https://epifi-icons.s3.ap-south-1.amazonaws.com/salaryprogram/Prime_4_point_top_logo.png"
          CtaDeeplink:
            Screen: ""
          CtaText: "Upgrade for free >"
          CtaFontColor: "#17478A"
          CtaBgColor: "#EFF2F6"
          ContainerBgColor: "#EFF2F6"
        FourPointsWidgetTextVisualElementCardMiddleSectionConfigs:
          - Point-1:
              PreText: "Rewards"
              Text: "2X Fi-Coins"
              Icon: "https://epifi-icons.s3.ap-south-1.amazonaws.com/salaryprogram/Prime_4_point_mid_1.png"
          - Point-2:
              PreText: "Fi-coins worth"
              Text: "₹500/month"
              Icon: "https://epifi-icons.s3.ap-south-1.amazonaws.com/salaryprogram/Prime_4_point_mid_2.png"
          - Point-3:
              PreText: "Free Debit Card"
              Text: "0 Forex Fee"
              Icon: "https://epifi-icons.s3.ap-south-1.amazonaws.com/salaryprogram/Prime_4_point_mid_3.png"
          - Point-4:
              PreText: "Savings Account"
              Text: "0 min balance"
              Icon: "https://epifi-icons.s3.ap-south-1.amazonaws.com/salaryprogram/Prime_4_point_mid_4.png"
    ActiveSegmentsForPromoWidgets: ["Tiering-USStocks"]
    SegmentUserDefaultPromoWidget:
      AddLessMoney:
        UserSegmentPromoWidget:
          IsVisible: false
          ThreePointsWidgetRightHorizontalFlyerConfigs:
            - Point-1:
                PreText: "Get rewards"
                Text: "3% per spend"
                Icon: "https://epifi-icons.s3.ap-south-1.amazonaws.com/salaryprogram/Prime_widget_segment_driven_3_point_1.png"
                Deeplink:
                  Screen: ""
            - Point-2:
                PreText: "Enjoy"
                Text: "0 balance a/c"
                Icon: "https://epifi-icons.s3.ap-south-1.amazonaws.com/salaryprogram/Prime_widget_segment_driven_3_Point_2.png"
                Deeplink:
                  Screen: ""
            - Point-3:
                PreText: "Withdraw"
                Text: "Anytime!"
                Icon: "https://epifi-icons.s3.ap-south-1.amazonaws.com/salaryprogram/Prime_widget_segment_driven_3_Point_3.png"
                Deeplink:
                  Screen: ""
          ThreePointsWidgetTitle: "Limited deal on Prime"
          ThreePointsWidgetLeftVerticalFlyerConfig:
            Icon: "https://epifi-icons.s3.ap-south-1.amazonaws.com/salaryprogram/Prime_widget_segment_driven_3_point_vertical.png"
            CtaDeeplink:
              Screen: ""
            CtaText: " Grab this deal > "
            IconBgColor: "#E4F1F5"
            IconFontColor: "#2D5E6E"
            ContainerBgColor: "#E4F1F5"
          FourPointsWidgetTitle: "Limited time deal"
          FourPointsWidgetTextVisualElementCardTopSectionConfig:
            Icon: "https://epifi-icons.s3.ap-south-1.amazonaws.com/salaryprogram/Prime_widget_secondary_add_less_money.png"
            CtaDeeplink:
              Screen: ""
            CtaText: "Grab this deal"
            CtaFontColor: "#2D5E6E"
            CtaBgColor: "#E4F1F5"
            ContainerBgColor: "#E4F1F5"
          FourPointsWidgetTextVisualElementCardMiddleSectionConfigs:
            - Point-1:
                PreText: "Add LESS"
                Text: "30% off"
                Icon: "https://epifi-icons.s3.ap-south-1.amazonaws.com/salaryprogram/Prime_segment_driven_add_less_money_point_1.png"
            - Point-2:
                PreText: "Per spend,"
                Text: "3% back"
                Icon: "https://epifi-icons.s3.ap-south-1.amazonaws.com/salaryprogram/Prime_segment_driven_add_less_money_point_2.png"
            - Point-3:
                PreText: "Min. balance "
                Text: "Zero!"
                Icon: "https://epifi-icons.s3.ap-south-1.amazonaws.com/salaryprogram/Prime_segment_driven_add_less_money_point_3.png"
            - Point-4:
                PreText: "Earn"
                Text: "2x Fi-Coins"
                Icon: "https://epifi-icons.s3.ap-south-1.amazonaws.com/salaryprogram/Prime_segment_driven_add_less_money_point_4.png"
        SegmentIdsForUserSegmentPromoWidget: ["9d2f51fe-7a36-43a6-8182-e748ba49a884"]
      ThriweCoupons:
        UserSegmentPromoWidget:
          IsVisible : false
          ThreePointsWidgetRightHorizontalFlyerConfigs:
            - Point-1:
                PreText: "Get rewards"
                Text: "3% per spend"
                Icon: "https://epifi-icons.s3.ap-south-1.amazonaws.com/salaryprogram/thriwe_widget_segment_driven_3_Point_1.png"
                Deeplink:
                  Screen: "REWARD_OFFER_DETAILS_SCREEN"
                  RewardOfferDetailsScreenOptions:
                    RewardOfferId: "77a74207-e713-470e-852a-3979ca91f634"
            - Point-2:
                PreText: "Enjoy"
                Text: "0 balance a/c"
                Icon: "https://epifi-icons.s3.ap-south-1.amazonaws.com/salaryprogram/thriwe_widget_segment_driven_3_Point_2.png"
                Deeplink:
                  Screen: "REWARD_OFFER_DETAILS_SCREEN"
                  RewardOfferDetailsScreenOptions:
                    RewardOfferId: "77a74207-e713-470e-852a-3979ca91f634"
            - Point-3:
                PreText: "Withdraw"
                Text: "Anytime!"
                Icon: "https://epifi-icons.s3.ap-south-1.amazonaws.com/salaryprogram/thriwe_widget_segment_driven_3_Point_3.png"
                Deeplink:
                  Screen: "REWARD_OFFER_DETAILS_SCREEN"
                  RewardOfferDetailsScreenOptions:
                    RewardOfferId: "77a74207-e713-470e-852a-3979ca91f634"
          ThreePointsWidgetTitle: "All things rewards"
          ThreePointsWidgetLeftVerticalFlyerConfig:
            Icon: "https://epifi-icons.s3.ap-south-1.amazonaws.com/salaryprogram/thriwe_widget_segment_driven_3_Vertical.png"
            CtaDeeplink:
              Screen: "REWARD_OFFER_DETAILS_SCREEN"
              RewardOfferDetailsScreenOptions:
                RewardOfferId: "77a74207-e713-470e-852a-3979ca91f634"
            CtaText: "Try Prime >"
            IconBgColor: "#E4F1F5"
            IconFontColor: "#2D5E6E"
            ContainerBgColor: "#E4F1F5"
          FourPointsWidgetTitle: "It's raining rewards!"
          FourPointsWidgetTextVisualElementCardTopSectionConfig:
            Icon: "https://epifi-icons.s3.ap-south-1.amazonaws.com/salaryprogram/Prime_widget_secondary_thriwe_coupons.png"
            CtaDeeplink:
              Screen: "REWARD_OFFER_DETAILS_SCREEN"
              RewardOfferDetailsScreenOptions:
                RewardOfferId: "77a74207-e713-470e-852a-3979ca91f634"
            CtaText: "Try Prime"
            CtaFontColor: "#2D5E6E"
            CtaBgColor: "#E4F1F5"
            ContainerBgColor: "#E4F1F5"
          FourPointsWidgetTextVisualElementCardMiddleSectionConfigs:
            - Point-1:
                PreText: "Deal-icious"
                Text: "₹500 off"
                Icon: "https://epifi-icons.s3.ap-south-1.amazonaws.com/salaryprogram/Prime_segment_driven_thriwe_coupon_point_1.png"
            - Point-2:
                PreText: "Splurge!"
                Text: "₹500 off"
                Icon: "https://epifi-icons.s3.ap-south-1.amazonaws.com/salaryprogram/Prime_segment_driven_thriwe_coupon_point_2.png"
            - Point-3:
                PreText: "Min. balance "
                Text: "Zero!"
                Icon: "https://epifi-icons.s3.ap-south-1.amazonaws.com/salaryprogram/Prime_segment_driven_thriwe_coupon_point_3.png"
            - Point-4:
                PreText: "Earn"
                Text: "2x Fi-Coins"
                Icon: "https://epifi-icons.s3.ap-south-1.amazonaws.com/salaryprogram/Prime_segment_driven_thriwe_coupon_point_4.png"
        SegmentIdsForUserSegmentPromoWidget: [ "ee8d15a6-805d-47d1-b0a4-e4afa31226dd" ]
      Tiering-USStocks:
        UserSegmentPromoWidget:
          IsVisible: false
          ThreePointsWidgetRightHorizontalFlyerConfigs:
            - Point-1:
                PreText: "Get rewards"
                Text: "2% per spend"
                Icon: "https://epifi-icons.pointz.in/salaryprogram/TieringXUSStocks_3_Point_1.png"
                Deeplink:
                  Screen: "REWARD_OFFER_DETAILS_SCREEN"
                  RewardOfferDetailsScreenOptions:
                    RewardOfferId: "3d5ee401-478b-4153-9009-017544b40409"
            - Point-2:
                PreText: "Travel guilt-free!"
                Text: "0 forex charge"
                Icon: "https://epifi-icons.pointz.in/salaryprogram/TieringXUSStocks_3_Point_2.png"
                Deeplink:
                  Screen: "REWARD_OFFER_DETAILS_SCREEN"
                  RewardOfferDetailsScreenOptions:
                    RewardOfferId: "3d5ee401-478b-4153-9009-017544b40409"
            - Point-3:
                PreText: "Get cash back"
                Text: "Upto ₹500"
                Icon: "https://epifi-icons.pointz.in/salaryprogram/TieringXUSStocks_3_Point_3.png"
                Deeplink:
                  Screen: "REWARD_OFFER_DETAILS_SCREEN"
                  RewardOfferDetailsScreenOptions:
                    RewardOfferId: "3d5ee401-478b-4153-9009-017544b40409"
          ThreePointsWidgetTitle: "Get Infinite for ₹̶5̶0̶k̶ ₹20k"
          ThreePointsWidgetLeftVerticalFlyerConfig:
            Icon: "https://epifi-icons.pointz.in/salaryprogram/TieringXUSStocks_Primary.png"
            CtaDeeplink:
              Screen: "REWARD_OFFER_DETAILS_SCREEN"
              RewardOfferDetailsScreenOptions:
                RewardOfferId: "3d5ee401-478b-4153-9009-017544b40409"
            CtaText: "Explore stocks >"
            IconBgColor: "#E4F1F5"
            IconFontColor: "#2D5E6E"
            ContainerBgColor: "#E4F1F5"
          FourPointsWidgetTitle: "Get Infinite for ₹̶5̶0̶k̶ ₹20k"
          FourPointsWidgetTextVisualElementCardTopSectionConfig:
            Icon: "https://epifi-icons.pointz.in/salaryprogram/TieringXUSStocks_Secondary.png"
            CtaDeeplink:
              Screen: "REWARD_OFFER_DETAILS_SCREEN"
              RewardOfferDetailsScreenOptions:
                RewardOfferId: "3d5ee401-478b-4153-9009-017544b40409"
            CtaText: "Claim upgrade"
            CtaFontColor: "#2D5E6E"
            CtaBgColor: "#E4F1F5"
            ContainerBgColor: "#E4F1F5"
          FourPointsWidgetTextVisualElementCardMiddleSectionConfigs:
            - Point-1:
                PreText: "Get free cash"
                Text: "upto ₹500"
                Icon: "https://epifi-icons.pointz.in/salaryprogram/TieringXUSStocks_4_point_1.png"
            - Point-2:
                PreText: "Invest only"
                Text: "5̶0̶k̶ ₹20k"
                Icon: "https://epifi-icons.pointz.in/salaryprogram/TieringXUSStocks_4_point_2.png"
            - Point-3:
                PreText: "Forex charge"
                Text: "ZERO"
                Icon: "https://epifi-icons.pointz.in/salaryprogram/TieringXUSStocks_4_point_3.png"
            - Point-4:
                PreText: "Per Spend"
                Text: "2% back"
                Icon: "https://epifi-icons.pointz.in/salaryprogram/TieringXUSStocks_4_point_4.png"
        SegmentIdsForUserSegmentPromoWidget: [ "4db91056-3cb9-4782-9a7e-3fc67e5152d4" ]

AaSalaryDefaultCriteriaName: "CRITERIA_NAME_C_TWO"

AaSalarySegmentCriteriaMap:

AaSalaryMinDurationForReactivationEligibility: 480h # 20 days

BlacklistedUsersFromSalaryProgram:
   AC210805LAdNEkkASDyetWJJnV7lrw==: true
  # users who want to move to b2c tiers
   AC56m3CSV2QXqxex5Ih/ByNQ231224==: true
   AC220212u+mVDZBJQBuiFb4bAFnRVg==: true
   AC230304DkU8CwNeRo68BNjH7Y5gZg==: true
   AC9kvvGXTARKiDAmL1jnbbDg240218==: true
   ACbHL1KpKcT2yTmS+Y7phJQw240509==: true
   AC220119d84qhIIORUSCQxylOW2v0w==: true

QuestSdk:
  Disable: true

AaSalaryReducedTransferCriteria:
  AaSalaryTransferSetupParamsReductionFlag: false
  AaSalaryReducedTransferCriteriaName: "CRITERIA_NAME_C_THREE"
  MinSalary: 80000

AmountRangeForSalaryBands:
  - MinAmount: 25000
    MaxAmount: 50000
    SalaryBand: "SALARY_BAND_1"
  - MinAmount: 50000
    MaxAmount: 75000
    SalaryBand: "SALARY_BAND_2"
  - MinAmount: 75000
    MaxAmount: 1000000000
    SalaryBand: "SALARY_BAND_3"

B2BAmountRangeForSalaryBands:
  - MinAmount: 0
    MaxAmount: 25000
    B2BSalaryBand: "B2B_SALARY_BAND_1"
  - MinAmount: 25000
    MaxAmount: 1000000000
    B2BSalaryBand: "B2B_SALARY_BAND_2"

PromoWidgetDisplayFlag: false

BypassSalaryVerificationChecksForPIs: [
   "PI210827H0XvlNkISiCOJlwOKMJz9w==",
   "PIDRH5RrEkKKCE250129_5feFb8zhrk",
   "PI2Qxxtdv7uKt1250129_5feFb8zhrk",
   "PI210621r5Hj1kUWSjqcjO3kj2Jfew==",
   "PI210901BEV1fp1aSW6UA/rc/Q3kfA==",
   "PI210702fRWFZaaLTNC8Gfi7CCi7Gg==",
   "PI210512vZCl+nj+TBa8w4T+GVEgDg==",
   "PIeob1+/b6TfWitpTv/NEpkg240629==",
   "PI8ouffifmWw4K241025_5feFb8zhrk",
   "PI220801q3eRMTj8SICn4FDRISFVEA==",
   "PIPW/rnUEHQJyB30UQOChI2g240108==",
]

