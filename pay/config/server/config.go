package server

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"sync"
	"time"

	"github.com/knadh/koanf"
	"github.com/pkg/errors"
	"google.golang.org/genproto/googleapis/type/money"

	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/pay/pgerrorcodes"
	config2 "github.com/epifi/be-common/pkg/rulerover/config"

	dePb "github.com/epifi/gamma/api/dynamic_elements"

	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	releaseConfig "github.com/epifi/gamma/pkg/feature/release/config"
	payPkg "github.com/epifi/gamma/pkg/pay"
)

const (
	// Name of the secret field in config (YAML file)
	// nolint: gosec
	IftMt199MessageAttachmentPasswordSecretId = "MT199MessageAttachmentPassword"
)

var (
	OwnershipToUseCase = map[commontypes.Ownership]commontypes.UseCase{
		commontypes.Ownership_LIQUILOANS_PL:      commontypes.UseCase_USE_CASE_PG,
		commontypes.Ownership_STOCK_GUARDIAN_TSP: commontypes.UseCase_USE_CASE_PG,
	}
)

var (
	once       sync.Once
	config     *Config
	configErr  error
	_, b, _, _ = runtime.Caller(0)
	kConf      *koanf.Koanf
)

func Load() (*Config, error) {
	once.Do(func() {
		config, kConf, configErr = loadConfig()
	})
	if configErr != nil {
		return nil, errors.Wrap(configErr, "failed to load config")
	}

	return config, nil
}

func loadConfig() (*Config, *koanf.Koanf, error) {
	conf := &Config{}
	// will be used only for test environment
	testConfigDirPath := testEnvConfigDir()

	// loads config from file
	k, _, err := cfg.LoadConfigUsingKoanf(testConfigDirPath, cfg.PAY_SERVICE)

	if err != nil {
		return nil, nil, fmt.Errorf("failed to load dynamic config: %w", err)
	}

	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, nil, fmt.Errorf("failed to refresh dymanic config: %w", err)
	}

	err = cfg.LoadAllSecretsV3(conf, conf.Application.Environment, conf.AWS.Region)
	if err != nil {
		return nil, nil, err
	}

	_, err = cfg.LoadSecretsAndPrepareDBConfig(conf.Secrets, conf.Application.Environment, conf.AWS.Region, conf.EpifiDb, conf.PayDb)
	if err != nil {
		return nil, nil, err
	}

	// TODO(Sundeep): Move the payment gateway error status mapping to vendorgateway after evaluating the changes.
	// The challenge with moving to VG currently is that, in pay service we still need to
	// do the reverse mapping to extract other info, hence it needs to accessed at both the places.
	err = pgerrorcodes.LoadPaymentGatewayErrorMappings(conf.RazorPayResponseCodesJson)
	if err != nil {
		return nil, nil, fmt.Errorf("error in loading razorpay error mappings from json: %w", err)
	}

	return conf, k, nil
}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config) error {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Name = val
	}

	if val, ok := os.LookupEnv("SERVER_PORT"); ok {
		intVal, err := strconv.Atoi(val)
		if err != nil {
			return fmt.Errorf("failed to convert port to int: %w", err)
		}

		c.Server.Ports.GrpcPort = intVal
	}

	return nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..", "values")
	return configPath
}

//go:generate conf_gen github.com/epifi/gamma/pay/config/server Config
type Config struct {
	Application               *Application
	RazorPayResponseCodesJson string
	Server                    *Server
	Logging                   *cfg.Logging
	EpifiDb                   *cfg.DB
	RedisOptions              *cfg.RedisOptions
	AWS                       *cfg.AWS
	Secrets                   *cfg.Secrets
	FundTransferParams        *FundTransferParams `dynamic:"true"`
	// PaymentEnquiryParams configurable parameters for payment enquiry
	PaymentEnquiryParams                      *PaymentEnquiryParams
	ExecutionReportGenerationParams           *ExecutionReportGenerationParams `dynamic:"true"`
	InternationalFundTransfer                 *InternationalFundTransfer       `dynamic:"true"`
	PayIncidentMgrOrderUpdateSubscriber       *cfg.SqsSubscriber               `dynamic:"true"`
	TransactionDetailedStatusUpdateSubscriber *cfg.SqsSubscriber               `dynamic:"true"`
	PayIncidentManager                        *PayIncidentManager              `dynamic:"true"`
	PinotIngestionDelay                       *PinotIngestionDelay             `dynamic:"true"`
	SignalWorkflowPublisher                   *cfg.SqsPublisher
	IFTProcessFileSubscriber                  *cfg.SqsSubscriber           `dynamic:"true"`
	FundTransferCelestialParams               *FundTransferCelestialParams `dynamic:"true"`
	VelocityRuleThresholdsMap                 map[string]VelocityRuleThresholds
	VelocityRuleAmountRangeMap                map[string]VelocityRuleAmountRange
	RulesToRuleGroupMap                       map[string][]string
	// Defines minimum duration a user needs to be a Fi user to perform purchase
	MinDurationRequiredForUserVintageCheck time.Duration   `dynamic:"true"`
	VersionSupport                         *VersionSupport `dynamic:"true"`
	InPaymentOrderUpdatePublisher          *cfg.SqsPublisher
	// List of non Tpap psp handles
	NonTpapPspHandles                         []string
	VendorToNameMap                           *VendorToNameMap
	FeatureReleaseConfig                      *releaseConfig.FeatureReleaseConfig `dynamic:"true"`
	UniqueATMActorId                          string                              `dynamic:"true"`
	PageSizeToFetchTxnForATMActor             int32                               `dynamic:"true"`
	PageSizeForChargeRelatedOrderAndTxn       uint32                              `dynamic:"true"`
	IFTRemittanceFileProcessingEventPublisher *cfg.SnsPublisher
	PostPaymentBanners                        []*Banner
	PayLandingScreenBanners                   []*Banner
	// Elements to show on QR scan screen (can be banners or progress bars)
	QrScreenElements *QrScreenElements
	// Configuration for tier-related mappings
	TierConfig                        *TierConfig        `dynamic:"true"`
	OrderUpdateSubscriber             *cfg.SqsSubscriber `dynamic:"true"`
	TxnBackfillBucketName             string             `iam:"s3-readwrite"`
	IncidentManagerParams             *payPkg.IncidentManagerParams
	PayDb                             *cfg.DB
	GrpcRatelimiterParams             *cfg.GrpcRateLimiterParams
	Tracing                           *cfg.Tracing
	MaximumActiveDaysForAutoIdTickets int32 `dynamic:"true"`
	// config to fetch tpap enablement based on entry point.
	// adding to enable emergency switch off of tpap for a particular feature
	TpapEntryPointSwitchConfigForSaUser         *releaseConfig.FeatureReleaseConfig `dynamic:"true"`
	TpapEntryPointSwitchConfigForNonSaUser      *releaseConfig.FeatureReleaseConfig `dynamic:"true"`
	GenerateSofLimitStrategiesValuesSubscriber  *cfg.SqsSubscriber                  `dynamic:"true"`
	GenerateSofLimitStrategiesValuesPublisher   *cfg.SqsPublisher
	PennyDropConfig                             *PennyDropConfig
	ProcessPaymentGatewayWebhookEventSubscriber *cfg.SqsSubscriber `dynamic:"true"`
	// This configuration outlines different combination of transaction status, statusCodePayer and statusCodePayee.
	// Using for Comparing transaction attributes with these combinations to decide whether incident type should be of DEBITED_AND_FAILED_TRANSACTION
	TransactionStatusDetailsCombinationsForDebitAndFailedIncident []*TransactionStatusDetails
	PgProgramToAuthSecretMap                                      map[string]*PgProgramToAuthSecret
	// PayOrderCacheConfig holds the configuration for caching related to order operations.
	PayOrderCacheConfig *PayOrderCacheConfig `dynamic:"true"`
	// PayTransactionCacheConfig holds the configuration for caching related to transaction operations.
	PayTransactionCacheConfig *PayTransactionCacheConfig `dynamic:"true"`
	// flag to indicate whether to enable resource provider based db instance provision or whether to
	// go with a static db instance
	// Assumption : Once the flag is turned on, it will not be turned off again. Since it will go through a
	// CUG, any occurring issues will be fixed within the CUG itself. Also, all the systems using this
	// have been made backward compatible. So the older prod entities will not see any issues. Possible consequences
	// can be that the data got written with entity segregation but fetched from the default
	// epifi db but since that scale will be low at the time of CUG, we would be able to fix it before going external
	EnableEntitySegregation bool `dynamic:"true"`
	// credentials for signing or de-signing of sensitive payload
	PayloadSigningCreds *PayloadSigningCreds
	// PgParams stores the configurations for payment gateway flows.
	PgParams *PgParams `dynamic:"true"`

	BaseRuleEngineConfig *config2.RuleEngineConfig `dynamic:"true"`
	// FeedbackEngineCustomEvaluatorRules stores the custom evaluator rules for feedback engine.
	FeedbackEngineCustomEvaluatorRules map[string]*FeedbackEngineCustomEvaluatorRule `dynamic:"true"`
	OrderUpdateEventPublisher          *cfg.SnsPublisher
	EnrichOrderConfig                  *EnrichOrderConfig `dynamic:"true"`
	// Enable logging account creation details for debugging sof status
	EnableSOFLogForDebugging bool `dynamic:"true"`
}

type EnrichOrderConfig struct {
	EnrichmentFromDcSwitchConfig *EnrichmentFromDcSwitchConfig `dynamic:"true"`
}

type EnrichmentFromDcSwitchConfig struct {
	AllowedTxnTimeDeviationBuffer time.Duration `dynamic:"true"`
}

type FeedbackEngineCustomEvaluatorRule struct {
	DurationForTxnEvaluation time.Duration `dynamic:"true"`
	MinSuccessCountThreshold int32         `dynamic:"true"`
	MinDebitAmount           int32         `dynamic:"true"`
}

type PayloadSigningCreds struct {
	// SigningKey is the key used to sign the payload
	SigningKeyJson string `iam:"sm-read"`
	SigningKey     string `field:"SigningKeyJson" jsonPath:"SigningKey"`
	IV             string `field:"SigningKeyJson" jsonPath:"IV"`
}

func (c *Config) GetEnvironment() string {
	return c.Application.Environment
}

func (c *Config) GetAwsRegion() string {
	return c.AWS.Region
}

// VendorPaymentDetails will store the details related to payment
// like actor, pi and bufferDuration(buffer time after initiate of payment to completion of payment)
// which is specific to vendor
type VendorPaymentDetails struct {
	ActorId        string
	PiId           string
	BufferDuration time.Duration
}

// QrScreenElement is an interface for different types of QR screen elements
type QrScreenElements struct {
	Banners      []*Banner
	ProgressBars []*ProgressBar
}

// TierConfig contains tier-related configuration mappings
type TierConfig struct {
	// Mapping of tiers to their icon URLs
	TierToIconMap map[string]string `dynamic:"true"`
}

// Banner implements QrScreenElement interface
type Banner struct {
	Title    *TextProperties
	Image    *ImageProperties
	BgColor  string
	Deeplink string
	// Data requirements for the banner
	DataRequirements *BannerDataRequirements
	Body             *TextProperties
	// common flag for android and ios
	IsEnabled bool `dynamic:"true"`
	// platform specific flag
	IsAndroidEnabled bool   `dynamic:"true"`
	IsIOSEnabled     bool   `dynamic:"true"`
	Identifier       string `dynamic:"true"`
}

func (b *Banner) GetElementType() dePb.ElementStructureType {
	return dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER_V3
}

// ProgressBar represents a progress bar element to be shown on QR screens
type ProgressBar struct {
	Title    *TextProperties
	BgColor  string
	Deeplink string
	// Data requirements for the progress bar
	DataRequirements *ProgressBarDataRequirements
	// Subtitle text to show below the progress bar
	Subtitle *dePb.ProgressBarCardContent_SubtitleContent
	// Color for the progress portion of the bar
	ProgressColor string
	// Color for the background of the bar
	BackgroundColor string
}

func (p *ProgressBar) GetElementType() dePb.ElementStructureType {
	return dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_PROGRESS_BAR_CARD
}

// ProgressBarDataRequirements specifies what data is needed for a progress bar
type ProgressBarDataRequirements struct {
	// Whether tiering data is needed
	RequiresTieringData bool
	// Configuration for rewards data requirements
	RewardsConfig *RewardsConfig
}

// RewardsConfig specifies rewards data requirements for progress bars
type RewardsConfig struct {
	// Whether rewards data is needed for this element
	RequiresRewardsData bool
	// Whether projected rewards data is needed for this element
	RequiresProjectedRewardsData bool
	// Time period for rewards data (monthly, weekly, etc.)
	TimePeriod string
	// Which reward type to use for progress calculation
	RewardTypeForProgress string
	// Whether to display the current value in subtitle
	DisplayCurrentValue bool
	// Whether to display the percentage progress in subtitle
	DisplayProgressPercent bool
}

// BannerDataRequirements specifies what data is needed for a banner
type BannerDataRequirements struct {
	// Whether tiering data is needed for this banner
	RequiresTieringData bool
	// Configuration for rewards data requirements
	RewardsConfig *BannerRewardsConfig
}

// BannerRewardsConfig specifies rewards data requirements for a banner
type BannerRewardsConfig struct {
	// Whether rewards data is needed for this banner
	RequiresRewardsData bool
	// Whether projected rewards data is needed for this banner
	RequiresProjectedRewardsData bool
	// Time period for rewards data (monthly, weekly, etc.)
	TimePeriod string
}

type TextProperties struct {
	Style string
	Text  string
	Color string
}
type ImageProperties struct {
	Url    string
	Height int32
	Width  int32
}

type PayIncidentManager struct {
	UpiPinFlowError           *UpiPinFlowError
	DefaultTransactionHandler *DefaultTransactionHandler `dynamic:"true"`
}

type DefaultTransactionHandler struct {
	ErrorCodeIncidentMap            map[string]string            `dynamic:"true"`
	IncidentProductCategoryMap      map[string]*IncidentCategory `dynamic:"true"`
	IncidentNotificationTemplateMap map[string]*IncidentNotificationTemplate
	IncidentTicketConfigMap         map[string]*IncidentTicketConfig `dynamic:"true"`
}

type UpiPinFlowError struct {
	ErrorCodeIncidentMap            map[string]string
	IncidentProductCategoryMap      map[string]*IncidentCategory
	IncidentNotificationTemplateMap map[string]*IncidentNotificationTemplate
	IncidentTicketConfigMap         map[string]*IncidentTicketConfig
}

type IncidentNotificationTemplate struct {
	IncidentCreationTemplate   *payPkg.NotificationTemplateParams
	IncidentResolutionTemplate *payPkg.NotificationTemplateParams
}

type IncidentCategory struct {
	ProductCategory        string `dynamic:"true"`
	ProductCategoryDetails string `dynamic:"true"`
	SubCategory            string `dynamic:"true"`
	// We Might store different issue category ids for different payment protocols.
	PaymentProtocolToIssueCategoryIdMap map[string]string `dynamic:"true"`
}

type IncidentTicketConfig struct {
	TicketDescription string `dynamic:"true"`
	TicketPriority    ticketPb.Priority
	TicketGroup       ticketPb.Group
}

type FundTransferCelestialParams struct {
	// param to check if fund transfer is enabled via celestial or not
	IsFundTransferViaCelestialRestricted bool `dynamic:"true"`
	// User groups for which fund transfer is enabled via celestial
	AllowedUserGrpForFundTransferViaCelestial []commontypes.UserGroup
	// Workflow version enabled for fund transfer celestial flow
	Version WorkflowVersion
}

type PaymentEnquiryParams struct {
	NotFoundMaxRetryDurationVendorMap map[string]*NotFoundMaxRetryDuration
	InProgressToSuccessMap            map[string]*InProgressToSuccess
}

type Application struct {
	Environment string
	Name        string
}

type Server struct {
	Ports *cfg.ServerPorts
}

type FundTransferParams struct {
	HardPreferredPaymentProtocol      paymentPb.PaymentProtocol
	DefaultFundTransferExpiryDuration time.Duration `dynamic:"true"`
	// map between sms type and the sms option version to be used
	SMSTypeToOptionVersionMap map[string]*cfg.SMSOptionVersion
	PaymentNotificationParams *PaymentNotificationParams
}

// notFoundMaxRetryDuration defines the max duration for which application should retry payment enquiry
// in case of vendor returns NOT FOUND response.
type NotFoundMaxRetryDuration struct {
	IntraBank time.Duration
	UPI       time.Duration
	NEFT      time.Duration
	RTGS      time.Duration
	IMPS      time.Duration
}

// inProgressToSuccess defines max duration for which payment should move in-progress transaction
// with specific error codes to success transaction.
type InProgressToSuccess struct {
	FiErrorCodes                 []string
	PaymentProtocolToDurationMap map[string]time.Duration
}

// ExecutionReportGenerationParams allow fetching workflows on the basis of different filters at regular intervals of ReportStalenessDuration
type ExecutionReportGenerationParams struct {
	ReportStalenessDuration time.Duration `dynamic:"true"`
}

type InternationalFundTransfer struct {
	S3Bucket                       string `iam:"s3-readwrite"`
	GstReportingInfo               *GstReportingInfo
	PoolInwardAccountPI            string `dynamic:"true"`
	UsStocksVendorPI               string `dynamic:"true"`
	SkipSofDocumentGenerationStage bool   `dynamic:"true"`

	// TODO(Brijesh): Remove after no IFT workflow is waiting on stock fulfilment signal
	// Deprecated: No need to wait for placement/fulfilment of stock trades when adding funds to USD wallet
	SkipOrderFulfilmentStage bool `dynamic:"true"`

	SherlockHost          string `dynamic:"true"`
	EnableFederalSherlock bool   `dynamic:"true"`
	// This is the domain of the federal sherlock as part of IFT migration
	FederalSherlockHost      string `dynamic:"true"`
	SherlockPath             string `dynamic:"true"`
	GenerateSwiftReportRetry int    `dynamic:"true"`
	GenerateSwiftReportSleep int    `dynamic:"true"`
	MaxPageSize              uint32
	// represent code for no data exist for pan
	NoDataExistForPANCode string `dynamic:"true"`
	// max no of records allowed in LRS file
	MaxLRSPageSize           int32 `dynamic:"true"`
	EnableLRSCheckFromVendor bool  `dynamic:"true"`
	// configurations for forex rates related things
	ForexRate                   *InternationalFundTransferForexRate `dynamic:"true"`
	DailyAggregateLimit         *money.Money
	AnnualAggregateLimit        *money.Money
	AccountVintageCheckDuration time.Duration `dynamic:"true"`
	AccountVintageTxnCount      int64         `dynamic:"true"`
	// represent account number of gst needs to be transfer
	IgstAccountNumber       string
	IgstAccountSolId        string
	CgstAccountNumber       string
	CgstAccountSolId        string
	SgstAccountNumber       string
	SgstAccountSolId        string
	TCSAccountNumber        string
	TCSAccountSolId         string
	OutwardPoolAccountSolId string
	OutwardPoolAccount      string
	// represent forex rate precision to used as forexRateId
	// Eg: helps in deciding precision of money while generating swift file
	ForexRateIdPrecision int64

	// NostroBankSwiftCode represents partner bank's account held in a foreign currency at another bank
	NostroBankSwiftCode string

	// skip pre check for swift file generation
	SkipPreCheckForSwiftFileGen bool `dynamic:"true"`

	// duration after which swift file should generated after lrs response file acknowledge
	DurationWaitForSwiftFileGen time.Duration `dynamic:"true"`

	// represent special instruction for usstocks
	// this is used by vendor to unique map transaction with epifi account
	// Eg: current special instruction represent epifi jit account number this txn represent
	// there might be case of epifi having account for reward for usstocks, so it might have different special instruction
	// todo might need to move better approach when we get multiple account and handling special instuction for it
	SpecialInstructionForUSStocks string
	// configurations for transaction amount suspected related things
	TransactionAmountConstraint *TransactionAmountConstraint `dynamic:"true"`
	// represent the cap on maximum limit usage from vendor api consumption
	ForexRateVendorAPIMaximumConsumptionAmountInUSD int64
	// controls if sof document based limit is check during international outward funds transfer
	IsSofBasedRemittanceLimitCheckEnabled bool `dynamic:"true"`
	// if set to true, cut off time defined for lrs check file generation will be used, else current state of the orders will be considered
	// i.e. all the orders placed before configured cutoff time will only be considered for file generation
	// eg: If cutoff time is 10:00, and file generation is requested at 01 Feb 09:00 am, cutoff timestamp would be 31 Jan 10:00
	// if file generation is requested at 01 Feb 11:00, cutoff timestamp would be 01 Feb 10:00
	GenerateLrsCheckFileUsingCutOffTime bool `dynamic:"true"`
	// flag to enable 2 new columns (sof_id and sof state) in swift file
	EnableSofStateColumnInSwiftFileFlag bool `dynamic:"true"`
	// flag to check if an old account is being used enough for transactions
	EnableMinTxnCountCheckForVintageAccounts bool `dynamic:"true"`
	// flag to enable lrs limits updatation via workflow
	EnableUpdateLrsLimitsWorkflowFlag bool `dynamic:"true"`
	// AuthFactorUpdateCoolOffPeriod determines the time duration till which international funds transfer will be blocked post
	// an auth factor update for the user
	// Note: Update the AuthFactorUpdateCoolOffPeriod in us stocks too when changing this config
	AuthFactorUpdateCoolOffPeriod time.Duration `dynamic:"true"`
}

type TransactionAmountConstraint struct {
	// if it is true then only will check suspected amount conditions
	IsEnabled bool `dynamic:"true"`
	// if given amount for buy usstocks is more than MaxPermissibleAmount then this trxn would be considered in suspicious
	MaxPermissibleAmount *money.Money
	// represents the maximum allow percentage of International Transaction Amount/ [Max (Last 5 Credit transaction Amount)]
	MaxTransactionAmountToCreditAmountPercentage int64 `dynamic:"true"`
	// represents the maximum allow percentage of International Transaction Amount/ Account Balance
	MaxTransactionAmountToCurrentBalancePercentage int64 `dynamic:"true"`
}

type InternationalFundTransferForexRate struct {
	// config related to UpdateForexRateAmountInUse RPC
	UpdateAmountInUse *ForexRateUpdateAmountInUse `dynamic:"true"`
	USD               *USDForexRate               `dynamic:"true"`
	// list of thresholds for sending alerts on forex rate consumption in percentage
	// alert is sent when a threshold is breached for first time
	// eg: [70, 90] would mean that 2 alerts will be sent when 70% of the total deal amount is consumed
	// and 90% of the total deal amount is consumed
	// If a single order bumps the deal consumption from 65% to 95%, then only 1 alert is expected to be sent
	ForexRateAlertThresholdsInPercent []int64 `dynamic:"true"`
	// ForexRateRefreshInterval will be used by MonitorForexRate workflow
	// It is the interval after which fresh value for amount_in_use is fetched from db and evaluated for sending alert
	// amount_in_use is updated on receiving order update signal
	// since there can be cases that forex deal update signal is missed due to platform failure, celestial service downtime etc
	// workflow should handle this by refreshing the forex rate object from db after defined interval
	ForexRateRefreshInterval time.Duration `dynamic:"true"`
	// if AllowVaryingRatesFromForexAPI value is true then will support multiple forex rate entries for different forex rate in DB for vendor API provenance
	// otherwise will throw error if from vendor getting different forex rate from existing forex rate in DB.
	AllowVaryingRatesFromForexAPI bool `dynamic:"true"`
}

type ForexRateUpdateAmountInUse struct {
	// if true, then client req id will be mandatory for UpdateForexRateAmountInUse RPC and maintains the idempotency
	EnableIdempotency bool `dynamic:"true"`
}

type USDForexRate struct {
	// "INR"
	CurrencyCode string `dynamic:"true"`
	// eg: "82"
	Units int64 `dynamic:"true"`
	// e.g: "510000000" for 0.51
	Nanos int32 `dynamic:"true"`
}

// Represent info required to fill in gst reporting file
type GstReportingInfo struct {
	// Represent the value need to be filled for gst reporting
	FilingOrganisation string
	SectionName        string
	InvoiceType        string
	InvoiceNumber      string
	Hsn                string
	ReverseCharge      string
	IsSez              string
	// represent state code of service provider help in deciding gst calculation
	// ref https://docs.google.com/spreadsheets/d/1vEqHl7txTLA1-hanHq2vpmqtoxMmLt_j/edit#gid=*********
	ServiceProviderStateCode string
	// represent min gst charge amount
	// reference: https://www.sc.com/in/important-information/service-tax-on-forex-transactions/
	MinTaxableGstAmount *money.Money
	// represent the pecentage of tax being calculated for igst,cgst and sgst
	CgstPercentage float32
	IgstPercentage float32
	SgstPercentage float32
	// TODO to handle these mappings and other fields here for new vendors
	// represent the code map of state map to vendor specific state code
	StateCode map[string]string

	// refers to a unique code that banks' branches are allotted.
	ServiceProviderSolId string
}

// PinotIngestionDelay tells maximum delay in pinot ingestion
type PinotIngestionDelay struct {
	DelayDuration time.Duration `dynamic:"true"`
}

type VersionSupport struct {
	// First app version to support SOF flow(connected account flow) in android
	MinAndroidAppVersionToSupportSofCheck uint32 `dynamic:"true"`
	// First app version to support SOF flow(connected account flow) in iOS
	MinIOSAppVersionToSupportSofCheck uint32 `dynamic:"true"`
	// First app version to support add new connected account flow in android
	MinAndroidAppVersionToSupportAddCA uint32 `dynamic:"true"`
	// First app version to support add new connected account flow in iOS
	MinIOSAppVersionToSupportAddCA uint32 `dynamic:"true"`
}

// VelocityRuleThresholds are the threshold values required for the rule evaluation
type VelocityRuleThresholds struct {
	ThresholdAmount *money.Money
	ThresholdCount  *int64
}

// VelocityRuleAmountRange : amount range for a given velocity rule
type VelocityRuleAmountRange struct {
	MinAmount int64
	MaxAmount int64
}

type WorkflowVersion string

func (w WorkflowVersion) ToWorkflowVersionEnum() workflowPb.Version {
	switch w {
	case "V1":
		return workflowPb.Version_V1
	default:
		return workflowPb.Version_V0
	}
}

type PaymentNotificationParams struct {
	HistoricPaymentSupportedTill time.Duration
	TransactionDebitSuccess      *payPkg.NotificationTemplateParams
	TransactionFailed            *payPkg.NotificationTemplateParams
	TransactionReversed          *payPkg.NotificationTemplateParams
}

type VendorToNameMap map[string]string

type PennyDropConfig struct {
	PennyDropPollRetryThreshold int32
	// PennyDropSourceAccountMapping maps penny drop provenance and vendor to its source account details.
	// for ex: map[PENNY_DROP_PROVENANCE_SECURED_CREDIT_CARDS_NTB_FLOW][FEDERAL]
	// maps the penny drop source account details for secured cc ntb flow with federal as the vendor.
	PennyDropSourceAccountMapping map[string]map[string]*PennyDropSourceAccountDetails
}

// PennyDropSourceAccountDetails will store the payment instrument id and the actor id of
// the bank's pool account which will be used for a penny drop flow. This is required to be passed
// in the fromActor field of the B2C fund transfer request.
type PennyDropSourceAccountDetails struct {
	ActorId             string
	PaymentInstrumentId string
	// Amount to be penny dropped.
	// The amount must be 1 INR and not to be changed.
	Amount *money.Money
}

// TransactionStatusDetails  will store the transaction status, statusCodePayer and statusCodePayee of a transaction.
// Used for comparing these details and create/resolve a ticket for DEBITED_AND_FAILED_TRANSACTION incident.
type TransactionStatusDetails struct {
	IsEnabled bool
	// DetailedApiStatus is used for comparing detailedApiStatus of transaction,
	// which basically stores the detailed status of RespPay If Present otherwise the latest detailed status as per created time.
	DetailedApiStatus string
	StatusCodePayer   string
	StatusCodePayee   string
}

type PgProgramToAuthSecret struct {
	AuthParam      string `iam:"sm-read"`
	AuthParamValue string `field:"AuthParam"`
}

type PayOrderCacheConfig struct {
	// this boolean will turn on/off cache layer
	IsCachingEnabled bool `dynamic:"true"`

	// UseCaseToCacheConfigMap will have use case mapped to cache config (ie, IsCachingEnabled and CacheTTL)
	UseCaseToCacheConfigMap map[string]*CacheConfig `dynamic:"true"`

	RedisOptions *cfg.RedisOptions
}

type PayTransactionCacheConfig struct {
	// this boolean will turn on/off cache layer
	IsCachingEnabled bool `dynamic:"true"`

	// UseCaseToCacheConfigMap will have use case mapped to cache config (ie, IsCachingEnabled and CacheTTL)
	UseCaseToCacheConfigMap map[string]*CacheConfig `dynamic:"true"`

	// We are only using this Redis Options in Worker. Server uses redis store.
	RedisOptions *cfg.RedisOptions
}

type CacheConfig struct {
	// switch to enable/disable cache
	IsCachingEnabled bool `dynamic:"true"`
	// duration for which cache is stored
	CacheTTL time.Duration `dynamic:"true"`
}

type PgParams struct {
	OneTimeFundTransferStatusWaitSignalTimeout     time.Duration `dynamic:"true"`
	RpExecutionFundTransferStatusWaitSignalTimeout time.Duration `dynamic:"true"`

	// OneTimePaymentOrderExpirationDuration stores the duration till which a payment can be attempted on the order.
	// If no payment attempts are made on the order even after this duration, then we mark the internal order as EXPIRED.
	OneTimePaymentOrderExpirationDuration map[string]time.Duration `dynamic:"true"`

	// Fields for creating adjustment order and transactions from paymentgateway.
	// generic actor id from which we are assuming the payment gateway adjustment
	// txn to be taking place. This will be generic wrt pg vendor
	PaymentGatewayAdjustmentActorId string
	// The actor to name, for which the adjustment transaction is being created. It is added only for
	// bookkeeping purposes, and is not mapped to any real user. It is called dummy as it is passed to ResolveActorTo
	// RPC as a place-holder since it has an input validation on length of actor name field.
	DummyPaymentGatewayAdjustmentActorToName string
	// mapping of ownership to generic pi id from which we are assuming the payment gateway adjustment
	// txn to be taking place. This will be generic wrt pg vendor and account ownership, since Payment instrument
	// entity is entity-segregated.
	PGAdjustmentOwnershipToPiMapping map[string]string
	// generic ActorId for cases when a refund is received for a vendor payment for which no internal transaction entity
	// exists. Refund transactions made to this actor id need to be tracked, and the AI would be to ensure that we don't
	// hit such cases.
	PaymentGatewayRefundGenericActorId string
	// the dummy actor name from which the refund is sent for a vendor payment for which no internal transaction entity
	// exists. It is called dummy as it is passed to ResolveActorFrom RPC as a place-holder since it has an input
	// validation on length of actor name field. Refund transactions made from this actor id need to be tracked, and the
	// AI would be to ensure that we don't hit such cases.
	DummyPaymentGatewayRefundGenericActorFromName string
	// Number of worker goroutines to use for enriching vendor payment/refund details in pgorderprocessor
	// This controls the number of concurrent vendor calls made to fetch the details for enrichment.
	VendorDetailsEnrichmentNumWorkers int `dynamic:"true"`
	// Controls the numbers of concurrent requests to enrich vendor payment/refund details,
	// that can be made in a given second.
	VendorDetailsEnrichmentApiRatelimit int `dynamic:"true"`
	// Controls whether PG mandate status need to be synced via webhooks
	EnableMandateStatusUpdateViaConsumer bool `dynamic:"true"`
}
