// Code generated by tools/conf_gen/dynamic_conf_gen.go
package worker

import (
	"fmt"
	"strings"
)

func (obj *Config) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "enableentitysegregation":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableEntitySegregation\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableEntitySegregation, nil
	case "isgstreportedwithnewinvoicenumber":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsGSTReportedWithNewInvoiceNumber\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsGSTReportedWithNewInvoiceNumber, nil
	case "application":
		return obj.Application.Get(dynamicFieldPath[1:])
	case "offappupiapplication":
		return obj.OffAppUpiApplication.Get(dynamicFieldPath[1:])
	case "billpayapplication":
		return obj.BillpayApplication.Get(dynamicFieldPath[1:])
	case "internationalfundtransfer":
		return obj.InternationalFundTransfer.Get(dynamicFieldPath[1:])
	case "billpayparams":
		return obj.BillpayParams.Get(dynamicFieldPath[1:])
	case "ordercacheconfig":
		return obj.OrderCacheConfig.Get(dynamicFieldPath[1:])
	case "payordercacheconfig":
		return obj.PayOrderCacheConfig.Get(dynamicFieldPath[1:])
	case "paytransactioncacheconfig":
		return obj.PayTransactionCacheConfig.Get(dynamicFieldPath[1:])
	case "pgparams":
		return obj.PgParams.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Config", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *InternationalFundTransfer) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "enablefederalsherlock":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableFederalSherlock\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableFederalSherlock, nil
	case "sherlockhost":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SherlockHost\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SherlockHost, nil
	case "federalsherlockhost":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FederalSherlockHost\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FederalSherlockHost, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for InternationalFundTransfer", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *BillpayParams) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "rechargepoolaccountactorid":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"RechargePoolAccountActorId\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.RechargePoolAccountActorId, nil
	case "rechargepoolaccountpiid":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"RechargePoolAccountPiId\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.RechargePoolAccountPiId, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for BillpayParams", strings.Join(dynamicFieldPath, "."))
	}
}
