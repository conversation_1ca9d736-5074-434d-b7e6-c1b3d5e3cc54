package federal

import (
	"flag"
	"os"
	"testing"

	"github.com/epifi/gamma/pay/test"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()

	var teardown func()
	// nolint:dogsled
	_, _, genConf, _, teardown := test.InitTestServer()

	svcTS = newFileContentTestSuite(genConf)
	exitCode := m.Run()

	teardown()
	os.Exit(exitCode)
}
