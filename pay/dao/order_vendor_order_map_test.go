package dao_test

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"reflect"
	"testing"
	"time"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	gormv2 "gorm.io/gorm"

	"github.com/epifi/be-common/pkg/datetime"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"

	orderPb "github.com/epifi/gamma/api/order"
	orderEnumsPb "github.com/epifi/gamma/api/order/enums"
	payServerConfig "github.com/epifi/gamma/pay/config/server"
	"github.com/epifi/gamma/pay/dao"
)

type OrderVendorOrderMapDaoTestSuite struct {
	db                     *gormv2.DB
	orderVendorOrderMapDao dao.OrderVendorOrderMapDao
	conf                   *payServerConfig.Config
	dbName                 string
}

func NewOrderVendorOrderMapDaoTestSuite(db *gormv2.DB, dao dao.OrderVendorOrderMapDao, conf *payServerConfig.Config, dbName string) OrderVendorOrderMapDaoTestSuite {
	return OrderVendorOrderMapDaoTestSuite{db: db, orderVendorOrderMapDao: dao, conf: conf, dbName: dbName}
}

var (
	ovomts               OrderVendorOrderMapDaoTestSuite
	orderVendorOrderMap1 = &orderPb.OrderVendorOrderMap{
		OrderId:       "ovom-order-1",
		VendorOrderId: "ovom-vendor-order-1",
		Vendor:        commonvgpb.Vendor_FEDERAL_BANK,
	}
	duplicateOvom = &orderPb.OrderVendorOrderMap{
		Id:            "a5004f18-5d52-4991-82a9-2a1e5010e903",
		OrderId:       "order-1",
		VendorOrderId: "vendor-order-1",
		Vendor:        commonvgpb.Vendor_FEDERAL_BANK,
		CreatedAt:     timestampPb.New(time.Date(2024, 03, 01, 0, 0, 0, 0, datetime.IST)),
		UpdatedAt:     timestampPb.New(time.Date(2024, 03, 01, 0, 0, 0, 0, datetime.IST)),
	}
	existingOvom = &orderPb.OrderVendorOrderMap{
		Id:                "a5004f18-5d52-4991-82a9-2a1e5010e902",
		OrderId:           "order-1",
		VendorOrderId:     "vendor-order-1",
		Vendor:            commonvgpb.Vendor_FEDERAL_BANK,
		DomainReferenceId: "domain-reference-id-1",
		OrderDirection:    orderEnumsPb.OrderDirection_ORDER_DIRECTION_FORWARD,
		CreatedAt:         timestampPb.New(time.Date(2024, 03, 01, 0, 0, 0, 0, datetime.IST)),
		UpdatedAt:         timestampPb.New(time.Date(2024, 03, 01, 0, 0, 0, 0, datetime.IST)),
	}
)

func TestOrderVendorOrderMapDaoCRDB_Create(t *testing.T) {
	pkgTest.PrepareScopedDatabase(t, ovomts.dbName, ovomts.db, affectedTestTables)

	tests := []struct {
		name    string
		ovom    *orderPb.OrderVendorOrderMap
		want    *orderPb.OrderVendorOrderMap
		wantErr bool
	}{
		{
			name:    "successfully create mapping",
			ovom:    orderVendorOrderMap1,
			want:    orderVendorOrderMap1,
			wantErr: false,
		},
		{
			name:    "duplicate entry exists",
			ovom:    duplicateOvom,
			want:    nil,
			wantErr: true,
		},
		{
			name:    "empty order vendor order mapping",
			ovom:    nil,
			want:    nil,
			wantErr: true,
		},
		{
			name: "empty order id in order vendor order mapping",
			ovom: &orderPb.OrderVendorOrderMap{
				OrderId:       "",
				VendorOrderId: "vendor-order-3",
				Vendor:        commonvgpb.Vendor_FEDERAL_BANK,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "empty vendor order id in order vendor order mapping",
			ovom: &orderPb.OrderVendorOrderMap{
				OrderId:       "order-4",
				VendorOrderId: "",
				Vendor:        commonvgpb.Vendor_FEDERAL_BANK,
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ovomts.orderVendorOrderMapDao.Create(context.Background(), tt.ovom)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != nil && tt.want != nil {
				tt.want.Id = got.GetId()
				tt.want.CreatedAt = got.GetCreatedAt()
				tt.want.UpdatedAt = got.GetUpdatedAt()
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Create() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestOrderVendorOrderMapDaoCRDB_GetOrderVendorOrderMap(t *testing.T) {
	pkgTest.PrepareScopedDatabase(t, ovomts.dbName, ovomts.db, affectedTestTables)

	tests := []struct {
		name       string
		ovom       *orderPb.OrderVendorOrderMap
		fieldMasks []orderEnumsPb.OrderVendorOrderMapFieldMask
		want       []*orderPb.OrderVendorOrderMap
		wantErr    bool
	}{
		{
			name: "successfully fetched mapping",
			ovom: &orderPb.OrderVendorOrderMap{
				OrderId:       "order-1",
				VendorOrderId: "vendor-order-1",
			},
			fieldMasks: []orderEnumsPb.OrderVendorOrderMapFieldMask{
				orderEnumsPb.OrderVendorOrderMapFieldMask_ORDER_VENDOR_ORDER_MAP_FIELD_MASK_ORDER_ID,
				orderEnumsPb.OrderVendorOrderMapFieldMask_ORDER_VENDOR_ORDER_MAP_FIELD_MASK_VENDOR_ORDER_ID,
			},
			want:    []*orderPb.OrderVendorOrderMap{existingOvom},
			wantErr: false,
		},
		{
			name: "empty filter list passed",
			ovom: &orderPb.OrderVendorOrderMap{
				OrderId:       "order-1",
				VendorOrderId: "vendor-order-1",
			},
			fieldMasks: []orderEnumsPb.OrderVendorOrderMapFieldMask{
				orderEnumsPb.OrderVendorOrderMapFieldMask_ORDER_VENDOR_ORDER_MAP_FIELD_MASK_UNSPECIFIED,
				orderEnumsPb.OrderVendorOrderMapFieldMask_ORDER_VENDOR_ORDER_MAP_FIELD_MASK_UNSPECIFIED,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "not found",
			ovom: &orderPb.OrderVendorOrderMap{
				OrderId:       "order-2",
				VendorOrderId: "vendor-order-1",
			},
			fieldMasks: []orderEnumsPb.OrderVendorOrderMapFieldMask{
				orderEnumsPb.OrderVendorOrderMapFieldMask_ORDER_VENDOR_ORDER_MAP_FIELD_MASK_ORDER_ID,
				orderEnumsPb.OrderVendorOrderMapFieldMask_ORDER_VENDOR_ORDER_MAP_FIELD_MASK_VENDOR_ORDER_ID,
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ovomts.orderVendorOrderMapDao.GetOrderVendorOrderMap(context.Background(), tt.ovom, tt.fieldMasks)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByOrderId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetByOrderId() got = %v, want %v", got, tt.want)
			}
		})
	}
}
