package tools

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"

	"github.com/epifi/be-common/mcp/pkg/logger"
	"github.com/epifi/be-common/tools/pkg/auth"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/pkg/errors"
)

const jarvisNonProdBaseURL = "https://jarvis-go.deploy.pointz.in"

func GetJenkinsJobLogs() mcp.Tool {
	return mcp.NewTool("GetJenkinsJobLogsTool",
		mcp.WithDescription("Tool to get <PERSON> job logs"),
		mcp.WithString("job_url", mcp.Description("job to fetch the logs for"), mcp.Required()),
		mcp.WithNumber("build_number", mcp.Description("build number to fetch the logs for"), mcp.Required()),
	)
}

func FetchJenkinsJobLogs(ctx context.Context, request mcp.CallToolRequest, log *logger.SafeLogger) (*mcp.CallToolResult, error) {
	log.Println("Fetching Jenkins job logs...")
	jobURL, err := getStringParam(request, "job_url")
	if err != nil {
		return nil, err
	}
	buildNumber, err := getIntParam(request, "build_number")
	if err != nil {
		return nil, err
	}
	if jobURL == "" || buildNumber == 0 {
		return nil, errors.New("job_url and build_number cannot be empty")
	}

	logs, err := fetchLogsFromJarvis(ctx, jobURL, buildNumber, log)
	if err != nil {
		log.Printf("Error fetching logs: %v", err)
		return nil, err
	}

	return &mcp.CallToolResult{
		Content: []mcp.Content{mcp.TextContent{
			Type: "text",
			Text: logs,
		}},
	}, nil
}

func fetchLogsFromJarvis(ctx context.Context, jobURL string, buildNumber int, log *logger.SafeLogger) (string, error) {
	env := "non-prod"
	if !strings.Contains(jobURL, "jenkins-deploy") {
		env = "prod"
	}
	url := fmt.Sprintf("%s/jenkins/logs?env=%s&job_url=%s&build_number=%d", jarvisNonProdBaseURL, env, jobURL, buildNumber)
	log.Printf("Fetching logs from Jarvis: %s", url)
	keycloakClient, err := auth.NewKeycloakAuthClient(
		ctx,
		"https://keycloak.pointz.in/realms/InternalNonProd",
		"lci-dev",
	)
	if err != nil {
		return "", fmt.Errorf("failed to create Keycloak auth client: %v", err)
	}
	// get a valid token
	keycloakToken, err := keycloakClient.GetValidToken(ctx)
	if err != nil {
		return "", fmt.Errorf("failed to get valid Keycloak token: %v", err)
	}
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	if err != nil {
		return "", errors.Wrap(err, "error creating request")
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("cookie", fmt.Sprintf("auth_token=%s", keycloakToken.AccessToken))

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return "", errors.Wrap(err, "error sending request to Jarvis")
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			log.Printf("Error closing response body: %v", err)
		}
	}(resp.Body)

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("jarvis returned non-OK status: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", errors.Wrap(err, "error reading response body")
	}

	return parseLogResponse(body)
}

func parseLogResponse(body []byte) (string, error) {
	var res struct {
		Response string `json:"response"`
		Data     struct {
			Logs string `json:"logs"`
		} `json:"data"`
	}

	if err := json.Unmarshal(body, &res); err != nil {
		return "", errors.Wrap(err, "error parsing JSON response")
	}

	if res.Response != "ok" {
		return "", fmt.Errorf("jarvis returned error response: %s", res.Response)
	}

	return res.Data.Logs, nil
}

func getStringParam(request mcp.CallToolRequest, key string) (string, error) {
	val, ok := request.GetArguments()[key]
	if !ok || val == nil {
		return "", fmt.Errorf("%s parameter is required", key)
	}
	strVal, ok := val.(string)
	if !ok {
		return "", fmt.Errorf("%s parameter must be a string", key)
	}
	return strVal, nil
}

func getIntParam(request mcp.CallToolRequest, key string) (int, error) {
	val, ok := request.GetArguments()[key]
	if !ok || val == nil {
		return 0, fmt.Errorf("%s parameter is required", key)
	}
	num, ok := val.(float64)
	if !ok {
		return 0, fmt.Errorf("%s parameter must be a number", key)
	}
	return int(num), nil
}
