package service_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"
	"reflect"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/timestamppb"

	dateTimeMocks "github.com/epifi/be-common/pkg/datetime/mocks"
	"github.com/epifi/be-common/pkg/epifierrors"
	mockEvents "github.com/epifi/be-common/pkg/events/mocks"
	mocks5 "github.com/epifi/be-common/pkg/idgen/mocks"
	lockMocks "github.com/epifi/be-common/pkg/lock/mocks"
	"github.com/epifi/be-common/pkg/pagination"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/gamma/api/accounts/operstatus"
	mocks3 "github.com/epifi/gamma/api/accounts/operstatus/mocks"
	actorPb "github.com/epifi/gamma/api/actor"
	actorMock "github.com/epifi/gamma/api/actor/mocks"
	"github.com/epifi/gamma/api/bankcust"
	bankCustMock "github.com/epifi/gamma/api/bankcust/mocks"
	inapptargetedcommsPb "github.com/epifi/gamma/api/comms/inapptargetedcomms"
	mocks2 "github.com/epifi/gamma/api/comms/inapptargetedcomms/mocks"
	"github.com/epifi/gamma/api/docs/esign"
	mocks4 "github.com/epifi/gamma/api/docs/esign/mocks"
	kycPb "github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/user"
	userPb "github.com/epifi/gamma/api/user"
	mockUser "github.com/epifi/gamma/api/user/mocks"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/accounts/status"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	errors2 "github.com/epifi/gamma/frontend/analyser/errors"
	"github.com/epifi/gamma/savings/service"

	"github.com/epifi/be-common/api/rpc"

	"github.com/epifi/gamma/api/vendorgateway/openbanking/header"
	"github.com/epifi/gamma/savings/config"
	"github.com/epifi/gamma/savings/test"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/proto"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"

	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/money"

	authPb "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/auth/mocks"
	pb "github.com/epifi/gamma/api/savings"
	typesPb "github.com/epifi/gamma/api/typesv2"
	vgSavingsPb "github.com/epifi/gamma/api/vendorgateway/openbanking/savings"
	vgSavingsMocks "github.com/epifi/gamma/api/vendorgateway/openbanking/savings/mocks"
	daoMocks "github.com/epifi/gamma/savings/dao/mocks"
)

var (
	conf          *config.Config
	activeAccount = &pb.Account{
		Id:                   "user-1",
		PrimaryAccountHolder: "Test-Savings-1",
	}
	fixturesSavings3 = &pb.Account{
		Id:                   "Test-Savings-3",
		AccountNo:            "**********",
		IfscCode:             "IFSC0001",
		PrimaryAccountHolder: "user-3",
		PhoneNumber: &commontypes.PhoneNumber{
			CountryCode:    0,
			NationalNumber: **********,
		},
		EmailId: "<EMAIL>",
		BalanceFromPartner: &moneyPb.Money{
			CurrencyCode: money.RupeeCurrencyCode,
			Units:        100,
			Nanos:        0,
		},
		PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
		State:       pb.State_CREATED,
		ActorId:     "actor-3",
	}
	fixturesSavings4 = &pb.Account{
		Id:                   "Test-Savings-4",
		AccountNo:            "**********",
		IfscCode:             "IFSC0001",
		PrimaryAccountHolder: "user-4",
		PhoneNumber: &commontypes.PhoneNumber{
			CountryCode:    0,
			NationalNumber: **********,
		},
		EmailId: "<EMAIL>",
		BalanceFromPartner: &moneyPb.Money{
			CurrencyCode: money.RupeeCurrencyCode,
			Units:        100,
			Nanos:        0,
		},
		PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
		State:       pb.State_CREATED,
		ActorId:     "actor-4",
	}
	fixturesSavings6 = &pb.Account{
		Id:                   "Test-Savings-4",
		AccountNo:            "**********",
		IfscCode:             "IFSC0001",
		PrimaryAccountHolder: "user-4",
		PhoneNumber: &commontypes.PhoneNumber{
			CountryCode:    0,
			NationalNumber: **********,
		},
		EmailId:     "<EMAIL>",
		PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
		State:       pb.State_CREATED,
		ActorId:     "actor-4",
	}
	updatedFixturesSavings3 = &pb.Account{
		Id:                   "Test-Savings-3",
		AccountNo:            "**********",
		IfscCode:             "IFSC0001",
		PrimaryAccountHolder: "user-3",
		BalanceFromPartner: &moneyPb.Money{
			CurrencyCode: money.RupeeCurrencyCode,
			Units:        100,
			Nanos:        0,
		},
		PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
		Constraints: &pb.AccountConstraints{
			AccessLevel:  pb.AccessLevel_ACCESS_LEVEL_PARTIAL_ACCESS,
			Restrictions: []pb.Restriction{pb.Restriction_RESTRICTION_CREDIT_FREEZE},
		},
		ActorId: "actor-3",
	}
	fixturesSavings3fullFreeze = &pb.Account{
		Id:                   "Test-Savings-3",
		AccountNo:            "**********",
		IfscCode:             "IFSC0001",
		PrimaryAccountHolder: "user-3",
		PhoneNumber: &commontypes.PhoneNumber{
			CountryCode:    0,
			NationalNumber: **********,
		},
		EmailId: "<EMAIL>",
		BalanceFromPartner: &moneyPb.Money{
			CurrencyCode: money.RupeeCurrencyCode,
			Units:        100,
			Nanos:        0,
		},
		PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
		State:       pb.State_CREATED,
		ActorId:     "actor-3",
	}
	updatedFixturesSavings3fullFreeze = &pb.Account{
		Id:                   "Test-Savings-3",
		AccountNo:            "**********",
		IfscCode:             "IFSC0001",
		PrimaryAccountHolder: "user-3",
		BalanceFromPartner: &moneyPb.Money{
			CurrencyCode: money.RupeeCurrencyCode,
			Units:        100,
			Nanos:        0,
		},
		PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
		Constraints: &pb.AccountConstraints{
			AccessLevel: pb.AccessLevel_ACCESS_LEVEL_NO_ACCESS,
		},
		ActorId: "actor-3",
	}
	updatedFixturesSavings4 = &pb.Account{
		Id:                   "Test-Savings-4",
		AccountNo:            "**********",
		IfscCode:             "IFSC0001",
		PrimaryAccountHolder: "user-4",
		BalanceFromPartner: &moneyPb.Money{
			CurrencyCode: money.RupeeCurrencyCode,
			Units:        100,
			Nanos:        0,
		},
		PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
		Constraints: &pb.AccountConstraints{
			AccessLevel:  pb.AccessLevel_ACCESS_LEVEL_PARTIAL_ACCESS,
			Restrictions: []pb.Restriction{pb.Restriction_RESTRICTION_CREDIT_FREEZE},
		},
		ActorId: "actor-4",
	}
	updatedFixturesSavings6 = &pb.Account{
		Id:                   "Test-Savings-4",
		AccountNo:            "**********",
		IfscCode:             "IFSC0001",
		PrimaryAccountHolder: "user-4",
		PartnerBank:          commonvgpb.Vendor_FEDERAL_BANK,
		State:                pb.State_CLOSED,
		ActorId:              "actor-4",
	}
)

type MockedDao struct {
	mock.Mock
}

func (m *MockedDao) GetPaginatedSaAccounts(ctx context.Context, pageToken *pagination.PageToken, pageSize uint32, fieldMasks []pb.AccountFieldMask, filterOptions ...storagev2.FilterOption) ([]*pb.Account, *rpc.PageContextResponse, error) {
	args := m.Called(ctx, pageToken, pageSize, fieldMasks, filterOptions)
	return args.Get(0).([]*pb.Account), args.Get(1).(*rpc.PageContextResponse), args.Error(2)
}

func (m *MockedDao) CreateAccount(ctx context.Context, account *pb.Account, retryInfo *pb.QueueRetryInfo) error {
	args := m.Called(ctx, account, retryInfo)
	return args.Error(0)
}

func (m *MockedDao) GetAccountById(ctx context.Context, id string) (*pb.Account, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*pb.Account), args.Error(1)
}

func (m *MockedDao) GetAccountByExternalId(ctx context.Context, acctNo string, ifsc string) (*pb.Account, error) {
	args := m.Called(ctx, acctNo, ifsc)
	return args.Get(0).(*pb.Account), args.Error(1)
}

func (m *MockedDao) GetAccountBalanceById(ctx context.Context, id string) (*moneyPb.Money, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*moneyPb.Money), args.Error(1)
}

func (m *MockedDao) GetAccountBalanceByExternalId(ctx context.Context, acctNo string, ifsc string) (*moneyPb.Money, error) {
	args := m.Called(ctx, acctNo, ifsc)
	return args.Get(0).(*moneyPb.Money), args.Error(1)
}

func (m *MockedDao) UpdateAccount(ctx context.Context, acct *pb.Account, updateMask []pb.AccountFieldMask) error {
	args := m.Called(ctx, acct)
	return args.Error(0)
}

func (m *MockedDao) GetAccountByPrimaryUserId(ctx context.Context, userId string) (*pb.Account, error) {
	// TODO(nitesh): implement this
	return nil, nil
}

func (m *MockedDao) GetAccountsByPrimaryUserIds(ctx context.Context, userIds []string) ([]*pb.Account, error) {
	args := m.Called(ctx, userIds)
	return args.Get(0).([]*pb.Account), args.Error(1)
}

func (m *MockedDao) GetAccountsByPrimaryUserId(ctx context.Context, userId string) ([]*pb.Account, error) {
	args := m.Called(ctx, userId)
	return args.Get(0).([]*pb.Account), args.Error(1)
}

func (m *MockedDao) GetAccountsByActorId(ctx context.Context, actorId string) ([]*pb.Account, error) {
	args := m.Called(ctx, actorId)
	return args.Get(0).([]*pb.Account), args.Error(1)
}

func (m *MockedDao) GetAccountWithRetryInfo(ctx context.Context, acctId string) (*pb.Account, *pb.QueueRetryInfo, error) {
	// TODO(nitesh): implement this
	return nil, nil, nil
}

func (m *MockedDao) UpdateAccountAndQRetryInfo(ctx context.Context, account *pb.Account, retryInfo *pb.QueueRetryInfo, updateMask []pb.AccountFieldMask) error {
	// TODO(nitesh): implement this
	return nil
}

func (m *MockedDao) GetAccountByRequestId(ctx context.Context, reqId string) (*pb.Account, error) {
	// TODO(keerthana): implement this
	return nil, nil
}

func (m *MockedDao) GetActiveAccountIds(ctx context.Context, createdBefore time.Time, createdAfter time.Time, limit int32, offset int64, sortDesc bool) ([]*pb.Account, error) {
	return nil, nil
}

func (m *MockedDao) GetAccountByActorId(ctx context.Context, actorId string) (*pb.Account, error) {
	args := m.Called(ctx, actorId)
	return args.Get(0).(*pb.Account), args.Error(1)
}

func (m *MockedDao) GetAccountByAccountNoAndBank(ctx context.Context, acctNo string, bank commonvgpb.Vendor) (*pb.Account, error) {
	args := m.Called(ctx, acctNo, bank)
	return args.Get(0).(*pb.Account), args.Error(1)
}

// Test Suite for savings service
type TestSuite struct {
	suite.Suite
	Dao           *MockedDao
	Service       pb.SavingsServer
	PrimaryUserId string
	IfscCode      string
	AccountNumber string
	AccountId     string
	ActorId       string
}

// Method to initialize test suite
// to be executed once before running all the tests
// contains logic to setup test server and DB connection
func (s *TestSuite) SetupSuite() {
	conf, _, _, _ = test.InitTestServer()
	mockDao := new(MockedDao)
	s.Dao = mockDao
	// TODO(nitesh): mock queueService and instantiate
	s.Service = service.NewSavingsService(mockDao, nil, idgen.NewDomainIdGenerator(idgen.NewClock()), nil, nil, nil, nil, nil, nil, conf, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)
	s.PrimaryUserId = "Test-User-1"
	s.IfscCode = "FED007"
	s.AccountNumber = "**********"
	s.AccountId = "SV123456789"
	s.ActorId = "actor-1"
}

// Entry point for test suite
func TestInit(t *testing.T) {
	suite.Run(t, new(TestSuite))
}

func (s *TestSuite) TestCreateAccount() {
	// TODO(nitesh): Implement this test when complete implementation of createAccount has been done
}

func (s *TestSuite) TestGetAccountByID() {
	resAcct := pb.Account{
		Id:                   s.AccountId,
		AccountNo:            s.AccountNumber,
		IfscCode:             s.IfscCode,
		PrimaryAccountHolder: s.PrimaryUserId,
		PhoneNumber: &commontypes.PhoneNumber{
			CountryCode:    91,
			NationalNumber: 123456,
		},
		BalanceFromPartner: money.ZeroINR().Pb,
		PartnerBank:        commonvgpb.Vendor_FEDERAL_BANK,
		State:              pb.State_INITIATED,
	}

	s.Dao.On("GetAccountById", context.Background(), s.AccountId).Return(&resAcct, nil)

	res, err := s.Service.GetAccount(context.Background(), &pb.GetAccountRequest{Identifier: &pb.GetAccountRequest_Id{Id: s.AccountId}})

	assert.Nil(s.T(), err)
	assert.Equal(s.T(), s.AccountId, res.Account.Id)
	assert.Equal(s.T(), s.PrimaryUserId, res.Account.PrimaryAccountHolder)
	assert.Equal(s.T(), s.AccountNumber, res.Account.AccountNo)
	assert.Equal(s.T(), s.IfscCode, res.Account.IfscCode)
	assert.Equal(s.T(), resAcct.State.String(), res.Account.State.String())
}

func (s *TestSuite) TestGetAccountByExternalId() {
	resAcct := pb.Account{
		Id:                   s.AccountId,
		AccountNo:            s.AccountNumber,
		IfscCode:             s.IfscCode,
		PrimaryAccountHolder: s.PrimaryUserId,
		PhoneNumber: &commontypes.PhoneNumber{
			CountryCode:    91,
			NationalNumber: 123456,
		},
		BalanceFromPartner: money.ZeroINR().Pb,
		PartnerBank:        commonvgpb.Vendor_FEDERAL_BANK,
		State:              pb.State_INITIATED,
	}

	s.Dao.On("GetAccountByExternalId", context.Background(), s.AccountNumber, s.IfscCode).Return(&resAcct, nil)

	res, err := s.Service.GetAccount(context.Background(), &pb.GetAccountRequest{Identifier: &pb.GetAccountRequest_ExternalId{ExternalId: &pb.BankAccountIdentifier{AccountNo: s.AccountNumber, IfscCode: s.IfscCode}}})

	assert.Nil(s.T(), err)
	assert.Equal(s.T(), s.AccountId, res.Account.Id)
	assert.Equal(s.T(), s.PrimaryUserId, res.Account.PrimaryAccountHolder)
	assert.Equal(s.T(), s.AccountNumber, res.Account.AccountNo)
	assert.Equal(s.T(), s.IfscCode, res.Account.IfscCode)
	assert.Equal(s.T(), resAcct.State.String(), res.Account.State.String())
}

func (s *TestSuite) TestGetAccountByActorId() {
	resAcct := pb.Account{
		Id:                   s.AccountId,
		AccountNo:            s.AccountNumber,
		IfscCode:             s.IfscCode,
		PrimaryAccountHolder: s.PrimaryUserId,
		PhoneNumber: &commontypes.PhoneNumber{
			CountryCode:    91,
			NationalNumber: 123456,
		},
		BalanceFromPartner: money.ZeroINR().Pb,
		PartnerBank:        commonvgpb.Vendor_FEDERAL_BANK,
		State:              pb.State_INITIATED,
		ActorId:            s.ActorId,
	}

	s.Dao.On("GetAccountByActorId", context.Background(), s.ActorId).Return(&resAcct, nil)

	res, err := s.Service.GetAccount(context.Background(), &pb.GetAccountRequest{Identifier: &pb.GetAccountRequest_ActorId{ActorId: s.ActorId}})

	assert.Nil(s.T(), err)
	assert.Equal(s.T(), s.AccountId, res.Account.Id)
	assert.Equal(s.T(), s.ActorId, res.Account.GetActorId())
	assert.Equal(s.T(), s.PrimaryUserId, res.Account.PrimaryAccountHolder)
	assert.Equal(s.T(), s.AccountNumber, res.Account.AccountNo)
	assert.Equal(s.T(), s.IfscCode, res.Account.IfscCode)
	assert.Equal(s.T(), resAcct.State.String(), res.Account.State.String())
}

func TestSavingsService_GetAccountBalance(t *testing.T) {
	ctr := gomock.NewController(t)
	mockAuthClient := mocks.NewMockAuthClient(ctr)
	mockVgSavingsClient := vgSavingsMocks.NewMockSavingsClient(ctr)
	mockSavingsDao := daoMocks.NewMockSavingsDao(ctr)
	mockLockManager := lockMocks.NewMockRwLockManager(ctr)
	mockILock := lockMocks.NewMockILock(ctr)
	savingsService := service.NewSavingsService(mockSavingsDao, nil, nil, nil, nil, mockVgSavingsClient, nil, mockAuthClient, nil, conf, nil, nil, nil, nil, nil, nil, nil, nil, nil, genconf, nil, nil, nil, nil, mockLockManager, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)

	testBalanceUpdateAt := timestamppb.Now()
	testDbUpdatedAt1 := timestamppb.New(testBalanceUpdateAt.AsTime().Add(-time.Second * 30)) // db updated 5 seconds ago
	testDbUpdatedAt2 := timestamppb.New(testBalanceUpdateAt.AsTime().Add(-time.Second * 5))  // db updated 30 seconds ago

	type mockGetDeviceAuth struct {
		enable bool
		req    *authPb.GetDeviceAuthRequest
		res    *authPb.GetDeviceAuthResponse
		err    error
	}

	type mockGetAccountById struct {
		enable bool
		id     string
		res    *pb.Account
		err    error
	}

	type mockGetAccountByExternalId struct {
		enable        bool
		accountNumber string
		ifsc          string
		res           *pb.Account
		err           error
	}

	type mockGetBalanceRequest struct {
		enable bool
		req    *vgSavingsPb.GetBalanceRequest
		res    *vgSavingsPb.GetBalanceResponse
		err    error
	}

	type mockUpdateBalance struct {
		enable      bool
		updatedAcct *pb.Account
		updateMask  []pb.AccountFieldMask
		err         error
	}

	type mockLockManagerLock struct {
		enable    bool
		accountId string
		lock      *lockMocks.MockILock
		err       error
	}

	tests := []struct {
		name                       string
		req                        *pb.GetAccountBalanceRequest
		mockGetDeviceAuth          mockGetDeviceAuth
		mockLockManagerLock        mockLockManagerLock
		mockGetAccountById         []mockGetAccountById
		mockGetAccountByExternalId []mockGetAccountByExternalId
		mockGetBalanceRequest      mockGetBalanceRequest
		mockUpdateBalance          mockUpdateBalance
		want                       *pb.GetAccountBalanceResponse
		wantErr                    bool
	}{
		{
			name: "got balance successfully by account Id",
			req: &pb.GetAccountBalanceRequest{
				Identifier: &pb.GetAccountBalanceRequest_Id{Id: "id-1"},
				ActorId:    "actor-1",
			},
			mockGetDeviceAuth: mockGetDeviceAuth{
				enable: true,
				req: &authPb.GetDeviceAuthRequest{
					ActorId: "actor-1",
				},
				res: &authPb.GetDeviceAuthResponse{
					Status:      rpc.StatusOk(),
					ActorId:     "actor-1",
					DeviceToken: "device-token-1",
					Device: &commontypes.Device{
						DeviceId: "device-id-1",
					},
				},
				err: nil,
			},
			mockLockManagerLock: mockLockManagerLock{
				enable:    true,
				accountId: "getAccountBalance_id-1",
				lock:      mockILock,
				err:       nil,
			},
			mockGetAccountById: []mockGetAccountById{
				{
					enable: true,
					id:     "id-1",
					res: &pb.Account{
						Id:        "id-1",
						AccountNo: "account-1",
						BalanceFromPartner: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        1000,
						},
						PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					},
					err: nil,
				},
				{
					enable: true,
					id:     "id-1",
					res: &pb.Account{
						Id:        "id-1",
						AccountNo: "account-1",
						BalanceFromPartner: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        1000,
						},
						PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					},
					err: nil,
				},
			},
			mockGetBalanceRequest: mockGetBalanceRequest{
				enable: true,
				req: &vgSavingsPb.GetBalanceRequest{
					Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
					Auth: &header.Auth{
						DeviceId:    "device-id-1",
						DeviceToken: "device-token-1",
					},
					AccountNumber: "account-1",
				},
				res: &vgSavingsPb.GetBalanceResponse{
					Status: rpc.StatusOk(),
					AvailableBalance: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        1000,
					},
					BalanceAt: testBalanceUpdateAt,
				},
				err: nil,
			},
			mockUpdateBalance: mockUpdateBalance{
				enable: true,
				updatedAcct: &pb.Account{
					Id:        "id-1",
					AccountNo: "account-1",
					BalanceFromPartner: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        1000,
					},
					PartnerBank:                 commonvgpb.Vendor_FEDERAL_BANK,
					BalanceFromPartnerUpdatedAt: testBalanceUpdateAt,
				},
				updateMask: []pb.AccountFieldMask{pb.AccountFieldMask_BALANCE_FROM_PARTNER, pb.AccountFieldMask_BALANCE_FROM_PARTNER_UPDATE_AT},
				err:        nil,
			},
			want: &pb.GetAccountBalanceResponse{
				Status: rpc.StatusOk(),
				AvailableBalance: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        1000,
				},
				LastUpdatedAt: testBalanceUpdateAt,
			},
		},
		{
			name: "got balance successfully by external Id",
			req: &pb.GetAccountBalanceRequest{
				Identifier: &pb.GetAccountBalanceRequest_ExternalId{ExternalId: &pb.BankAccountIdentifier{
					AccountNo: "account-2",
					IfscCode:  "FED001",
				}},
				ActorId: "actor-1",
			},
			mockGetDeviceAuth: mockGetDeviceAuth{
				enable: true,
				req: &authPb.GetDeviceAuthRequest{
					ActorId: "actor-1",
				},
				res: &authPb.GetDeviceAuthResponse{
					Status:      rpc.StatusOk(),
					ActorId:     "actor-1",
					DeviceToken: "device-token-1",
					Device: &commontypes.Device{
						DeviceId: "device-id-1",
					},
				},
				err: nil,
			},
			mockLockManagerLock: mockLockManagerLock{
				enable:    true,
				accountId: "getAccountBalance_id-2",
				lock:      mockILock,
				err:       nil,
			},
			mockGetAccountByExternalId: []mockGetAccountByExternalId{
				{
					enable:        true,
					accountNumber: "account-2",
					ifsc:          "FED001",
					res: &pb.Account{
						Id:        "id-2",
						AccountNo: "account-2",
						BalanceFromPartner: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        1000,
						},
						PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					},
					err: nil,
				},
				{
					enable:        true,
					accountNumber: "account-2",
					ifsc:          "FED001",
					res: &pb.Account{
						Id:        "id-2",
						AccountNo: "account-2",
						BalanceFromPartner: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        1000,
						},
						PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					},
					err: nil,
				},
			},
			mockGetBalanceRequest: mockGetBalanceRequest{
				enable: true,
				req: &vgSavingsPb.GetBalanceRequest{
					Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
					Auth: &header.Auth{
						DeviceId:    "device-id-1",
						DeviceToken: "device-token-1",
					},
					AccountNumber: "account-2",
				},
				res: &vgSavingsPb.GetBalanceResponse{
					Status: rpc.StatusOk(),
					AvailableBalance: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        1000,
					},
					BalanceAt: testBalanceUpdateAt,
				},
				err: nil,
			},
			mockUpdateBalance: mockUpdateBalance{
				enable: true,
				updatedAcct: &pb.Account{
					Id:        "id-2",
					AccountNo: "account-2",
					BalanceFromPartner: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        1000,
					},
					PartnerBank:                 commonvgpb.Vendor_FEDERAL_BANK,
					BalanceFromPartnerUpdatedAt: testBalanceUpdateAt,
				},
				updateMask: []pb.AccountFieldMask{pb.AccountFieldMask_BALANCE_FROM_PARTNER, pb.AccountFieldMask_BALANCE_FROM_PARTNER_UPDATE_AT},
				err:        nil,
			},
			want: &pb.GetAccountBalanceResponse{
				Status: rpc.StatusOk(),
				AvailableBalance: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        1000,
				},
				LastUpdatedAt: testBalanceUpdateAt,
			},
		},
		{
			name: "fail to fetch balance from vendor",
			req: &pb.GetAccountBalanceRequest{
				Identifier: &pb.GetAccountBalanceRequest_Id{Id: "id-1"},
				ActorId:    "actor-1",
			},
			mockGetDeviceAuth: mockGetDeviceAuth{
				enable: true,
				req: &authPb.GetDeviceAuthRequest{
					ActorId: "actor-1",
				},
				res: &authPb.GetDeviceAuthResponse{
					Status:      rpc.StatusOk(),
					ActorId:     "actor-1",
					DeviceToken: "device-token-1",
					Device: &commontypes.Device{
						DeviceId: "device-id-1",
					},
				},
				err: nil,
			},
			mockLockManagerLock: mockLockManagerLock{
				enable:    true,
				accountId: "getAccountBalance_id-1",
				lock:      mockILock,
				err:       nil,
			},
			mockGetAccountById: []mockGetAccountById{
				{
					enable: true,
					id:     "id-1",
					res: &pb.Account{
						Id:        "id-1",
						AccountNo: "account-1",
						BalanceFromPartner: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        1000,
						},
						PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					},
					err: nil,
				},
				{
					enable: true,
					id:     "id-1",
					res: &pb.Account{
						Id:        "id-1",
						AccountNo: "account-1",
						BalanceFromPartner: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        1000,
						},
						PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					},
					err: nil,
				},
			},
			mockGetBalanceRequest: mockGetBalanceRequest{
				enable: true,
				req: &vgSavingsPb.GetBalanceRequest{
					Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
					Auth: &header.Auth{
						DeviceId:    "device-id-1",
						DeviceToken: "device-token-1",
					},
					AccountNumber: "account-1",
				},
				res: &vgSavingsPb.GetBalanceResponse{
					Status: rpc.StatusInternal(),
				},
				err: nil,
			},
			want: &pb.GetAccountBalanceResponse{
				Status: rpc.StatusInternal(),
			},
		},
		{
			name: "Return balance from DB in case of GetAccountBalanceRequest_STALE and it balance was updated" +
				"in last 60 second",
			req: &pb.GetAccountBalanceRequest{
				Identifier:    &pb.GetAccountBalanceRequest_Id{Id: "id-1"},
				ActorId:       "actor-1",
				DataFreshness: pb.GetAccountBalanceRequest_STALE,
			},
			mockGetAccountById: []mockGetAccountById{
				{
					enable: true,
					id:     "id-1",
					res: &pb.Account{
						Id:        "id-1",
						AccountNo: "account-1",
						BalanceFromPartner: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        1000,
						},
						PartnerBank:                 commonvgpb.Vendor_FEDERAL_BANK,
						BalanceFromPartnerUpdatedAt: testDbUpdatedAt1,
					},
					err: nil,
				},
			},
			want: &pb.GetAccountBalanceResponse{
				Status: rpc.StatusOk(),
				AvailableBalance: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        1000,
				},
				LedgerBalance: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        1000,
				},
				LastUpdatedAt: testDbUpdatedAt1,
			},
		},
		{
			name: "Return balance from DB in case of GetAccountBalanceRequest_NEAR_REAL_TIME and it balance was updated" +
				"in last 10 second",
			req: &pb.GetAccountBalanceRequest{
				Identifier:    &pb.GetAccountBalanceRequest_Id{Id: "id-1"},
				ActorId:       "actor-1",
				DataFreshness: pb.GetAccountBalanceRequest_NEAR_REAL_TIME,
			},
			mockGetAccountById: []mockGetAccountById{
				{
					enable: true,
					id:     "id-1",
					res: &pb.Account{
						Id:        "id-1",
						AccountNo: "account-1",
						BalanceFromPartner: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        1000,
						},
						PartnerBank:                 commonvgpb.Vendor_FEDERAL_BANK,
						BalanceFromPartnerUpdatedAt: testDbUpdatedAt2,
					},
					err: nil,
				},
			},
			want: &pb.GetAccountBalanceResponse{
				Status: rpc.StatusOk(),
				AvailableBalance: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        1000,
				},
				LedgerBalance: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        1000,
				},
				LastUpdatedAt: testDbUpdatedAt2,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetDeviceAuth.enable {
				mockAuthClient.EXPECT().GetDeviceAuth(context.Background(), tt.mockGetDeviceAuth.req).
					Return(tt.mockGetDeviceAuth.res, tt.mockGetDeviceAuth.err)
			}
			if tt.mockLockManagerLock.enable {
				mockLockManager.EXPECT().Lock(gomock.Any(), tt.mockLockManagerLock.accountId, gomock.Any()).
					Return(tt.mockLockManagerLock.lock, tt.mockLockManagerLock.err)
				mockILock.EXPECT().Release(gomock.Any())
			}
			for _, mockGetAccountByIdEntry := range tt.mockGetAccountById {
				if mockGetAccountByIdEntry.enable {
					mockSavingsDao.EXPECT().GetAccountById(context.Background(), mockGetAccountByIdEntry.id).
						Return(mockGetAccountByIdEntry.res, mockGetAccountByIdEntry.err)
				}
			}
			for _, mockGetAccountByExternalIdEntry := range tt.mockGetAccountByExternalId {
				if mockGetAccountByExternalIdEntry.enable {
					mockSavingsDao.EXPECT().GetAccountByExternalId(context.Background(),
						mockGetAccountByExternalIdEntry.accountNumber, mockGetAccountByExternalIdEntry.ifsc).
						Return(mockGetAccountByExternalIdEntry.res, mockGetAccountByExternalIdEntry.err)
				}
			}
			if tt.mockGetBalanceRequest.enable {
				mockVgSavingsClient.EXPECT().GetBalance(context.Background(), tt.mockGetBalanceRequest.req).
					Return(tt.mockGetBalanceRequest.res, tt.mockGetBalanceRequest.err)
			}
			if tt.mockUpdateBalance.enable {
				mockSavingsDao.EXPECT().UpdateAccount(gomock.Any(), tt.mockUpdateBalance.updatedAcct, tt.mockUpdateBalance.updateMask).
					Return(tt.mockUpdateBalance.err)
			}
			got, err := savingsService.GetAccountBalance(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAccountBalance() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAccountBalance() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSavingsService_GetListOfActiveAccounts(t *testing.T) {
	ctr := gomock.NewController(t)
	mockSavingsDao := daoMocks.NewMockSavingsDao(ctr)
	savingsService := service.NewSavingsService(mockSavingsDao, nil, nil, nil, nil, nil, nil, nil, nil, conf, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)
	type mockGetActiveAccount struct {
		enable   bool
		res      []*pb.Account
		sortDesc bool
		err      error
	}
	tests := []struct {
		name                 string
		mockGetActiveAccount mockGetActiveAccount
		req                  *pb.GetListOfActiveAccountsRequest
		want                 *pb.GetListOfActiveAccountsResponse
		wantErr              bool
	}{
		{
			name: "Successfully fetch active account list created before",
			mockGetActiveAccount: mockGetActiveAccount{
				enable:   true,
				res:      []*pb.Account{activeAccount},
				sortDesc: true,
				err:      nil,
			},
			req: &pb.GetListOfActiveAccountsRequest{
				CreatedBefore: &timestamppb.Timestamp{
					Seconds: 456789,
				},
			},
			want: &pb.GetListOfActiveAccountsResponse{
				Status: rpc.StatusOk(),
				Account: []*pb.GetListOfActiveAccountsResponse_Account{
					{
						Id:                         activeAccount.GetId(),
						PrimaryAccountHolderUserId: activeAccount.GetPrimaryAccountHolder(),
					},
				},
				AccountList: []*pb.Account{activeAccount},
				NextOffset:  1,
			},
			wantErr: false,
		},
		{
			name: "no active account found",
			mockGetActiveAccount: mockGetActiveAccount{
				enable:   true,
				sortDesc: true,
				res:      nil,
				err:      epifierrors.ErrRecordNotFound,
			},
			req: &pb.GetListOfActiveAccountsRequest{
				CreatedBefore: &timestamppb.Timestamp{
					Seconds: 456789,
				},
			},
			want: &pb.GetListOfActiveAccountsResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "no active account found emtpy result set",
			mockGetActiveAccount: mockGetActiveAccount{
				enable:   true,
				sortDesc: true,
			},
			req: &pb.GetListOfActiveAccountsRequest{
				CreatedBefore: &timestamppb.Timestamp{
					Seconds: 456789,
				},
			},
			want: &pb.GetListOfActiveAccountsResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "transient error occurred while fetching active accounts list",
			mockGetActiveAccount: mockGetActiveAccount{
				enable:   true,
				sortDesc: true,
				res:      nil,
				err:      errors.New("unexpected failure occurred"),
			},
			req: &pb.GetListOfActiveAccountsRequest{
				CreatedBefore: &timestamppb.Timestamp{
					Seconds: 456789,
				},
			},
			want: &pb.GetListOfActiveAccountsResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "Successfully fetch active account list created after",
			mockGetActiveAccount: mockGetActiveAccount{
				enable: true,
				res:    []*pb.Account{activeAccount},
				err:    nil,
			},
			req: &pb.GetListOfActiveAccountsRequest{
				CreatedAfter: &timestamppb.Timestamp{
					Seconds: 456789,
				},
			},
			want: &pb.GetListOfActiveAccountsResponse{
				Status: rpc.StatusOk(),
				Account: []*pb.GetListOfActiveAccountsResponse_Account{
					{
						Id:                         activeAccount.GetId(),
						PrimaryAccountHolderUserId: activeAccount.GetPrimaryAccountHolder(),
					},
				},
				AccountList: []*pb.Account{activeAccount},
				NextOffset:  1,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetActiveAccount.enable {
				timeFn := func(ts *timestamppb.Timestamp) time.Time {
					if ts == nil {
						return time.Time{}
					}

					return ts.AsTime()
				}
				mockSavingsDao.EXPECT().GetActiveAccountIds(
					context.Background(),
					timeFn(tt.req.GetCreatedBefore()),
					timeFn(tt.req.GetCreatedAfter()),
					tt.req.GetPageSize(),
					tt.req.GetOffset(),
					tt.mockGetActiveAccount.sortDesc,
				).
					Return(tt.mockGetActiveAccount.res, tt.mockGetActiveAccount.err)
			}
			got, err := savingsService.GetListOfActiveAccounts(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetActiveAccountList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetActiveAccountList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSavingsService_GetAccountsList(t *testing.T) {
	ctr := gomock.NewController(t)
	mockSavingsDao := daoMocks.NewMockSavingsDao(ctr)
	savingsService := service.NewSavingsService(mockSavingsDao, nil, nil, nil, nil, nil, nil, nil, nil, conf, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)

	type mockGetAccountsByPrimaryUserIds struct {
		enable  bool
		userIds []string
		res     []*pb.Account
		err     error
	}

	tests := []struct {
		name                            string
		req                             *pb.GetAccountsListRequest
		mockGetAccountsByPrimaryUserIds mockGetAccountsByPrimaryUserIds
		want                            *pb.GetAccountsListResponse
		wantErr                         bool
	}{
		{
			name: "got list of accounts successfully by primary user Ids",
			req: &pb.GetAccountsListRequest{
				Identifier: &pb.GetAccountsListRequest_UserIds{UserIds: &pb.PrimaryUserIdentifier{
					PrimaryUserIds: []string{"user-4", "user-3"},
				}},
			},
			mockGetAccountsByPrimaryUserIds: mockGetAccountsByPrimaryUserIds{
				enable:  true,
				userIds: []string{"user-4", "user-3"},
				res: []*pb.Account{
					fixturesSavings3,
					fixturesSavings4,
				},
				err: nil,
			},
			want: &pb.GetAccountsListResponse{
				Status: rpc.StatusOk(),
				Accounts: []*pb.Account{
					fixturesSavings3,
					fixturesSavings4,
				},
			},
		},
		{
			name: "got only few accounts successfully by primary user Ids",
			req: &pb.GetAccountsListRequest{
				Identifier: &pb.GetAccountsListRequest_UserIds{UserIds: &pb.PrimaryUserIdentifier{
					PrimaryUserIds: []string{"xyz", "user-4", "user-3"},
				}},
			},
			mockGetAccountsByPrimaryUserIds: mockGetAccountsByPrimaryUserIds{
				enable:  true,
				userIds: []string{"xyz", "user-4", "user-3"},
				res: []*pb.Account{
					fixturesSavings3,
					fixturesSavings4,
				},
				err: nil,
			},
			want: &pb.GetAccountsListResponse{
				Status: rpc.StatusOk(),
				Accounts: []*pb.Account{
					fixturesSavings3,
					fixturesSavings4,
				},
			},
		},
		{
			name: "no accounts was found by primary user Ids",
			req: &pb.GetAccountsListRequest{
				Identifier: &pb.GetAccountsListRequest_UserIds{UserIds: &pb.PrimaryUserIdentifier{
					PrimaryUserIds: []string{"xyz"},
				}},
			},
			mockGetAccountsByPrimaryUserIds: mockGetAccountsByPrimaryUserIds{
				enable:  true,
				userIds: []string{"xyz"},
				res:     nil,
				err:     epifierrors.ErrRecordNotFound,
			},
			want: &pb.GetAccountsListResponse{
				Status:   rpc.StatusRecordNotFound(),
				Accounts: nil,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetAccountsByPrimaryUserIds.enable {
				mockSavingsDao.EXPECT().GetAccountsByPrimaryUserIds(context.Background(), tt.mockGetAccountsByPrimaryUserIds.userIds).
					Return(tt.mockGetAccountsByPrimaryUserIds.res, tt.mockGetAccountsByPrimaryUserIds.err)
			}
			got, err := savingsService.GetAccountsList(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAccountsList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAccountsList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSavingsService_UpdateAccount(t *testing.T) {
	actorId := "actor-id"
	type args struct {
		ctx context.Context
		req *pb.UpdateAccountRequest
	}
	type mockStruct struct {
		dao         *daoMocks.MockSavingsDao
		inAppComms  *mocks2.MockInAppTargetedCommsClient
		actorClient *actorMock.MockActorClient
	}
	tests := []struct {
		name      string
		args      args
		mockSetup func(*mockStruct)
		wantErr   error
		want      *pb.UpdateAccountResponse
	}{
		{
			name: "successfully updated by field mask - freeze account",
			args: args{
				ctx: context.Background(),
				req: &pb.UpdateAccountRequest{
					Identifier: &pb.UpdateAccountRequest_PrimaryAccountHolder{PrimaryAccountHolder: fixturesSavings3.GetPrimaryAccountHolder()},
					Constraints: &pb.AccountConstraints{
						AccessLevel:  pb.AccessLevel_ACCESS_LEVEL_PARTIAL_ACCESS,
						Restrictions: []pb.Restriction{pb.Restriction_RESTRICTION_CREDIT_FREEZE},
					},
					UpdateMask: []pb.AccountFieldMask{pb.AccountFieldMask_CONSTRAINTS},
				},
			},
			mockSetup: func(m *mockStruct) {
				m.dao.EXPECT().GetAccountByPrimaryUserId(gomock.Any(), fixturesSavings3.GetPrimaryAccountHolder()).
					Return(fixturesSavings3, nil)
				m.dao.EXPECT().UpdateAccount(gomock.Any(), updatedFixturesSavings3, []pb.AccountFieldMask{pb.AccountFieldMask_CONSTRAINTS}).
					Return(nil)
				m.actorClient.EXPECT().GetActorByEntityId(gomock.Any(), &actorPb.GetActorByEntityIdRequest{
					Type:     typesPb.Actor_USER,
					EntityId: fixturesSavings3.GetPrimaryAccountHolder(),
				}).Return(&actorPb.GetActorByEntityIdResponse{
					Status: rpc.StatusOk(),
					Actor: &typesPb.Actor{
						Id:       actorId,
						EntityId: fixturesSavings3.GetPrimaryAccountHolder(),
					},
				}, nil)
				m.inAppComms.EXPECT().AddTargetedCommsMapping(gomock.Any(), &inapptargetedcommsPb.AddTargetedCommsMappingRequest{
					TargetedCommsElementId: "test-banner",
					MappingDetailsList: []*inapptargetedcommsPb.MappingDetails{
						{
							MappingType: inapptargetedcommsPb.MappingType_MAPPING_TYPE_USER,
							MappedValue: actorId,
						},
					},
				}).Return(&inapptargetedcommsPb.AddTargetedCommsMappingResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			wantErr: nil,
			want: &pb.UpdateAccountResponse{
				Status:  rpc.StatusOk(),
				Account: updatedFixturesSavings3,
			},
		},
		{
			name: "successfully updated by field mask - unfreeze account",
			args: args{
				ctx: context.Background(),
				req: &pb.UpdateAccountRequest{
					Identifier: &pb.UpdateAccountRequest_PrimaryAccountHolder{PrimaryAccountHolder: fixturesSavings3fullFreeze.GetPrimaryAccountHolder()},
					Constraints: &pb.AccountConstraints{
						AccessLevel: pb.AccessLevel_ACCESS_LEVEL_NO_ACCESS,
					},
					UpdateMask: []pb.AccountFieldMask{pb.AccountFieldMask_CONSTRAINTS},
				},
			},
			mockSetup: func(m *mockStruct) {
				m.dao.EXPECT().GetAccountByPrimaryUserId(gomock.Any(), fixturesSavings3fullFreeze.GetPrimaryAccountHolder()).
					Return(fixturesSavings3fullFreeze, nil)
				m.actorClient.EXPECT().GetActorByEntityId(gomock.Any(), &actorPb.GetActorByEntityIdRequest{
					Type:     typesPb.Actor_USER,
					EntityId: fixturesSavings3fullFreeze.GetPrimaryAccountHolder(),
				}).Return(&actorPb.GetActorByEntityIdResponse{
					Status: rpc.StatusOk(),
					Actor: &typesPb.Actor{
						Id:       actorId,
						EntityId: fixturesSavings3fullFreeze.GetPrimaryAccountHolder(),
					},
				}, nil)
				m.inAppComms.EXPECT().DeleteTargetedCommsMapping(gomock.Any(), &inapptargetedcommsPb.DeleteTargetedCommsMappingRequest{
					MappingDetailsList: []*inapptargetedcommsPb.MappingDetails{
						{
							MappingType: inapptargetedcommsPb.MappingType_MAPPING_TYPE_USER,
							MappedValue: actorId,
						},
					},
					AppPlatformsList: []commontypes.Platform{commontypes.Platform_PLATFORM_UNSPECIFIED},
				}).Return(&inapptargetedcommsPb.DeleteTargetedCommsMappingResponse{
					Status: rpc.StatusOk(),
				}, nil)
				m.dao.EXPECT().UpdateAccount(gomock.Any(), updatedFixturesSavings3fullFreeze, []pb.AccountFieldMask{pb.AccountFieldMask_CONSTRAINTS}).
					Return(nil)
			},
			wantErr: nil,
			want: &pb.UpdateAccountResponse{
				Status:  rpc.StatusOk(),
				Account: updatedFixturesSavings3fullFreeze,
			},
		},
		{
			name: "no record found with identifier",
			args: args{
				ctx: context.Background(),
				req: &pb.UpdateAccountRequest{
					Identifier: &pb.UpdateAccountRequest_PrimaryAccountHolder{PrimaryAccountHolder: fixturesSavings3.GetPrimaryAccountHolder()},
					Constraints: &pb.AccountConstraints{
						AccessLevel:  pb.AccessLevel_ACCESS_LEVEL_PARTIAL_ACCESS,
						Restrictions: []pb.Restriction{pb.Restriction_RESTRICTION_CREDIT_FREEZE},
					},
					UpdateMask: []pb.AccountFieldMask{pb.AccountFieldMask_CONSTRAINTS},
				},
			},
			mockSetup: func(m *mockStruct) {
				m.dao.EXPECT().GetAccountByPrimaryUserId(gomock.Any(), fixturesSavings3.GetPrimaryAccountHolder()).
					Return(nil, epifierrors.ErrRecordNotFound)
			},
			wantErr: nil,
			want: &pb.UpdateAccountResponse{
				Status:  rpc.StatusRecordNotFound(),
				Account: nil,
			},
		},
		{
			name: "error while updating in db",
			args: args{
				ctx: context.Background(),
				req: &pb.UpdateAccountRequest{
					Identifier: &pb.UpdateAccountRequest_PrimaryAccountHolder{PrimaryAccountHolder: fixturesSavings4.GetPrimaryAccountHolder()},
					Constraints: &pb.AccountConstraints{
						AccessLevel:  pb.AccessLevel_ACCESS_LEVEL_PARTIAL_ACCESS,
						Restrictions: []pb.Restriction{pb.Restriction_RESTRICTION_CREDIT_FREEZE},
					},
					UpdateMask: []pb.AccountFieldMask{pb.AccountFieldMask_CONSTRAINTS},
				},
			},
			mockSetup: func(m *mockStruct) {
				m.dao.EXPECT().GetAccountByPrimaryUserId(gomock.Any(), fixturesSavings4.GetPrimaryAccountHolder()).
					Return(fixturesSavings4, nil)
				m.dao.EXPECT().UpdateAccount(gomock.Any(), updatedFixturesSavings4, []pb.AccountFieldMask{pb.AccountFieldMask_CONSTRAINTS}).
					Return(epifierrors.ErrContextCanceled)
			},
			wantErr: nil,
			want: &pb.UpdateAccountResponse{
				Status:  rpc.StatusInternal(),
				Account: nil,
			},
		},
		{
			name: "successfully updated by field mask - state",
			args: args{
				ctx: context.Background(),
				req: &pb.UpdateAccountRequest{
					Identifier: &pb.UpdateAccountRequest_PrimaryAccountHolder{PrimaryAccountHolder: fixturesSavings6.GetPrimaryAccountHolder()},
					State:      pb.State_CLOSED,
					UpdateMask: []pb.AccountFieldMask{pb.AccountFieldMask_STATE},
				},
			},
			mockSetup: func(m *mockStruct) {
				m.dao.EXPECT().GetAccountByPrimaryUserId(gomock.Any(), fixturesSavings6.GetPrimaryAccountHolder()).
					Return(fixturesSavings6, nil)
				m.dao.EXPECT().UpdateAccount(gomock.Any(), updatedFixturesSavings6, []pb.AccountFieldMask{pb.AccountFieldMask_STATE}).
					Return(nil)
			},
			wantErr: nil,
			want: &pb.UpdateAccountResponse{
				Status:  rpc.StatusOk(),
				Account: updatedFixturesSavings6,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			mockSavingsDao := daoMocks.NewMockSavingsDao(ctr)
			mockInAppComms := mocks2.NewMockInAppTargetedCommsClient(ctr)
			mockActor := actorMock.NewMockActorClient(ctr)
			ts := service.NewSavingsService(mockSavingsDao, nil, nil, nil, nil, nil, mockActor, nil, nil, conf, nil, nil, nil, mockInAppComms, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)
			defer func() {
				ctr.Finish()
				ts = nil
			}()
			tt.mockSetup(&mockStruct{
				dao:         mockSavingsDao,
				inAppComms:  mockInAppComms,
				actorClient: mockActor,
			})
			got, err := ts.UpdateAccount(tt.args.ctx, tt.args.req)
			assert.Equal(t, tt.wantErr, err)
			if err == nil {
				if !proto.Equal(got, tt.want) {
					t.Errorf("GetBankAccounts() \ngot = %v, \nwant= %v", got, tt.want)
				}
			}
		})
	}
}

func TestSavingsService_FetchOrCreateSignAttempt(t *testing.T) {
	actorId := "actor_id"
	signUrl := "https://signurl.com"
	accountFix := &pb.Account{
		AccountNo: "acc-no",
		Id:        "acc-id",
	}
	accountFixWithId := &pb.Account{
		AccountNo: "acc-no",
		Id:        "acc-id",
		SignInfo: &pb.SignInfo{
			ClientReqId: "savingsSign-1",
			Attempt:     1,
		},
	}
	operStatusLastData := &operstatus.GetOperationalStatusRequest{
		DataFreshness: operstatus.GetOperationalStatusRequest_DATA_FRESHNESS_RECENT,
		AccountIdentifier: &operstatus.GetOperationalStatusRequest_SavingsAccountId{
			SavingsAccountId: "acc-id",
		},
	}
	operStatusStale := &operstatus.GetOperationalStatusRequest{
		DataFreshness: operstatus.GetOperationalStatusRequest_DATA_FRESHNESS_10_MIN_STALE,
		AccountIdentifier: &operstatus.GetOperationalStatusRequest_SavingsAccountId{
			SavingsAccountId: "acc-id",
		},
	}
	type args struct {
		ctx context.Context
		req *pb.FetchOrCreateSignAttemptRequest
	}
	argsFix := args{
		ctx: context.Background(),
		req: &pb.FetchOrCreateSignAttemptRequest{
			ActorId: actorId,
		},
	}

	type mockStruct struct {
		dao             *daoMocks.MockSavingsDao
		mockOperStatus  *mocks3.MockOperationalStatusServiceClient
		mockIdGen       *mocks5.MockIdGenerator
		mockESign       *mocks4.MockESignClient
		mockUserClient  *mockUser.MockUsersClient
		mockBcClient    *bankCustMock.MockBankCustomerServiceClient
		mockEventBroker *mockEvents.MockBroker
		mockTimeClient  *dateTimeMocks.MockTime
	}
	setupMockCall := func(mockStruct *mockStruct, checkSignStatusResponse *esign.CheckESignStatusResponse) {
		accountFixWithId = &pb.Account{
			AccountNo: "acc-no",
			Id:        "acc-id",
			SignInfo: &pb.SignInfo{
				ClientReqId: "savingsSign-1",
				Attempt:     1,
			},
		}
		mockStruct.dao.EXPECT().GetAccountByActorId(context.Background(), actorId).Return(accountFixWithId, nil).Times(1)
		mockStruct.mockOperStatus.EXPECT().GetOperationalStatus(context.Background(), operStatusLastData).Return(&operstatus.GetOperationalStatusResponse{
			Status: rpc.StatusOk(),
		}, nil).Times(1)
		mockStruct.mockOperStatus.EXPECT().GetOperationalStatus(context.Background(), operStatusStale).Return(&operstatus.GetOperationalStatusResponse{
			Status: rpc.StatusOk(),
		}, nil).Times(1)
		mockStruct.mockESign.EXPECT().CheckESignStatus(context.Background(), &esign.CheckESignStatusRequest{
			ClientRequestId: "savingsSign-1",
			Client:          esign.EsignRequestClient_ESIGN_REQUEST_CLIENT_SAVINGS_ACCOUNT,
			Vendor:          esign.EsignRequestVendor_ESIGN_REQUEST_VENDOR_LEEGALITY,
		}).Return(checkSignStatusResponse, nil)
		mockStruct.mockUserClient.EXPECT().GetUser(context.Background(), &user.GetUserRequest{
			Identifier: &user.GetUserRequest_ActorId{
				ActorId: actorId,
			},
		}).Return(&user.GetUserResponse{
			Status: rpc.StatusOk(),
		}, nil).Times(1)
		mockStruct.mockBcClient.EXPECT().GetBankCustomer(context.Background(), &bankcust.GetBankCustomerRequest{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
			Identifier: &bankcust.GetBankCustomerRequest_ActorId{
				ActorId: actorId,
			},
		}).Return(&bankcust.GetBankCustomerResponse{
			Status: rpc.StatusOk(),
		}, nil).Times(1)
	}
	tests := []struct {
		name      string
		args      args
		want      *pb.FetchOrCreateSignAttemptResponse
		wantErr   error
		mockSetup func(*mockStruct)
	}{
		{
			name: "Missing actor id",
			args: args{
				ctx: context.Background(),
			},
			want: &pb.FetchOrCreateSignAttemptResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("Empty actor id"),
			},
			wantErr: nil,
		},
		{
			name: "Error in fetching account info",
			args: argsFix,
			want: &pb.FetchOrCreateSignAttemptResponse{
				Status: rpc.StatusFromErrorWithDefaultInternal(errors2.InvalidDateErr),
			},
			wantErr: nil,
			mockSetup: func(mockStruct *mockStruct) {
				mockStruct.dao.EXPECT().GetAccountByActorId(context.Background(), actorId).Return(nil, errors2.InvalidDateErr).Times(1)
			},
		},
		{
			name: "Error while fetching operation status db data",
			args: argsFix,
			want: &pb.FetchOrCreateSignAttemptResponse{
				Status: rpc.StatusFromErrorWithDefaultInternal(rpc.StatusAsError(rpc.StatusInvalidArgument())),
			},
			wantErr: nil,
			mockSetup: func(mockStruct *mockStruct) {
				mockStruct.dao.EXPECT().GetAccountByActorId(context.Background(), actorId).Return(accountFix, nil).Times(1)
				mockStruct.mockOperStatus.EXPECT().GetOperationalStatus(context.Background(), operStatusLastData).Return(&operstatus.GetOperationalStatusResponse{
					Status: rpc.StatusInvalidArgument(),
				}, nil).Times(1)
			},
		},
		{
			name: "Signature available with status db data",
			args: argsFix,
			want: &pb.FetchOrCreateSignAttemptResponse{
				Status:     rpc.StatusOk(),
				SignStatus: pb.SignStatus_SIGN_STATUS_ALREADY_AVAILABLE_WITH_BANK,
			},
			wantErr: nil,
			mockSetup: func(mockStruct *mockStruct) {
				mockStruct.dao.EXPECT().GetAccountByActorId(context.Background(), actorId).Return(accountFix, nil).Times(1)
				mockStruct.mockOperStatus.EXPECT().GetOperationalStatus(context.Background(), operStatusLastData).Return(&operstatus.GetOperationalStatusResponse{
					Status: rpc.StatusOk(),
					OperationalStatusInfo: &operstatus.OperationalStatusInfo{
						VendorResponse: &operstatus.OperationalStatusVendorResponse{
							VendorResponse: &operstatus.OperationalStatusVendorResponse_FederalAccountStatusEnquiryResponse{
								FederalAccountStatusEnquiryResponse: &status.AccountStatusEnquiryResponse{
									SignCount: 1,
								},
							},
						},
					},
				}, nil).Times(1)
			},
		},
		{
			name: "Error while fetching operation status stale data",
			args: argsFix,
			want: &pb.FetchOrCreateSignAttemptResponse{
				Status: rpc.StatusFromErrorWithDefaultInternal(rpc.StatusAsError(rpc.StatusInvalidArgument())),
			},
			wantErr: nil,
			mockSetup: func(mockStruct *mockStruct) {
				mockStruct.dao.EXPECT().GetAccountByActorId(context.Background(), actorId).Return(accountFix, nil).Times(1)
				mockStruct.mockOperStatus.EXPECT().GetOperationalStatus(context.Background(), operStatusLastData).Return(&operstatus.GetOperationalStatusResponse{
					Status: rpc.StatusOk(),
				}, nil).Times(1)
				mockStruct.mockOperStatus.EXPECT().GetOperationalStatus(context.Background(), operStatusStale).Return(&operstatus.GetOperationalStatusResponse{
					Status: rpc.StatusInvalidArgument(),
				}, nil).Times(1)
			},
		},
		{
			name: "Signature available with status as stale data",
			args: argsFix,
			want: &pb.FetchOrCreateSignAttemptResponse{
				Status:     rpc.StatusOk(),
				SignStatus: pb.SignStatus_SIGN_STATUS_ALREADY_AVAILABLE_WITH_BANK,
			},
			wantErr: nil,
			mockSetup: func(mockStruct *mockStruct) {
				mockStruct.dao.EXPECT().GetAccountByActorId(context.Background(), actorId).Return(accountFix, nil).Times(1)
				mockStruct.mockOperStatus.EXPECT().GetOperationalStatus(context.Background(), operStatusLastData).Return(&operstatus.GetOperationalStatusResponse{
					Status: rpc.StatusOk(),
				}, nil).Times(1)
				mockStruct.mockOperStatus.EXPECT().GetOperationalStatus(context.Background(), operStatusStale).Return(&operstatus.GetOperationalStatusResponse{
					Status: rpc.StatusOk(),
					OperationalStatusInfo: &operstatus.OperationalStatusInfo{
						VendorResponse: &operstatus.OperationalStatusVendorResponse{
							VendorResponse: &operstatus.OperationalStatusVendorResponse_FederalAccountStatusEnquiryResponse{
								FederalAccountStatusEnquiryResponse: &status.AccountStatusEnquiryResponse{
									SignCount: 1,
								},
							},
						},
					},
				}, nil).Times(1)
			},
		},
		{
			name: "Error while fetching operation status stale data",
			args: argsFix,
			want: &pb.FetchOrCreateSignAttemptResponse{
				Status: rpc.StatusFromErrorWithDefaultInternal(rpc.StatusAsError(rpc.StatusInvalidArgument())),
			},
			wantErr: nil,
			mockSetup: func(mockStruct *mockStruct) {
				mockStruct.dao.EXPECT().GetAccountByActorId(context.Background(), actorId).Return(accountFix, nil).Times(1)
				mockStruct.mockOperStatus.EXPECT().GetOperationalStatus(context.Background(), operStatusLastData).Return(&operstatus.GetOperationalStatusResponse{
					Status: rpc.StatusOk(),
				}, nil).Times(1)
				mockStruct.mockOperStatus.EXPECT().GetOperationalStatus(context.Background(), operStatusStale).Return(&operstatus.GetOperationalStatusResponse{
					Status: rpc.StatusInvalidArgument(),
				}, nil).Times(1)
			},
		},
		{
			name: "Error in updating client req id",
			args: argsFix,
			want: &pb.FetchOrCreateSignAttemptResponse{
				Status: rpc.StatusFromErrorWithDefaultInternal(errors2.InvalidDateErr),
			},
			wantErr: nil,
			mockSetup: func(mockStruct *mockStruct) {
				mockStruct.dao.EXPECT().GetAccountByActorId(context.Background(), actorId).Return(accountFix, nil).Times(1)
				mockStruct.mockOperStatus.EXPECT().GetOperationalStatus(context.Background(), operStatusLastData).Return(&operstatus.GetOperationalStatusResponse{
					Status: rpc.StatusOk(),
				}, nil).Times(1)
				mockStruct.mockOperStatus.EXPECT().GetOperationalStatus(context.Background(), operStatusStale).Return(&operstatus.GetOperationalStatusResponse{
					Status: rpc.StatusOk(),
				}, nil).Times(1)
				mockStruct.mockIdGen.EXPECT().GetInAlphaNumeric(gomock.Any()).Return("savingsSign-1").Times(1)
				mockStruct.dao.EXPECT().UpdateAccount(context.Background(), &pb.Account{
					AccountNo: "acc-no",
					Id:        "acc-id",
					SignInfo: &pb.SignInfo{
						ClientReqId: "savingsSign-1",
						Attempt:     1,
					},
				}, []pb.AccountFieldMask{
					pb.AccountFieldMask_SIGN_INFO,
				}).Return(errors2.InvalidDateErr).Times(1)
			},
		},
		{
			name: "Error while fetching user data",
			args: argsFix,
			want: &pb.FetchOrCreateSignAttemptResponse{
				Status: rpc.StatusFromErrorWithDefaultInternal(rpc.StatusAsError(rpc.StatusInternal())),
			},
			wantErr: nil,
			mockSetup: func(mockStruct *mockStruct) {
				mockStruct.dao.EXPECT().GetAccountByActorId(context.Background(), actorId).Return(accountFixWithId, nil).Times(1)
				mockStruct.mockOperStatus.EXPECT().GetOperationalStatus(context.Background(), operStatusLastData).Return(&operstatus.GetOperationalStatusResponse{
					Status: rpc.StatusOk(),
				}, nil).Times(1)
				mockStruct.mockOperStatus.EXPECT().GetOperationalStatus(context.Background(), operStatusStale).Return(&operstatus.GetOperationalStatusResponse{
					Status: rpc.StatusOk(),
				}, nil).Times(1)
				mockStruct.mockESign.EXPECT().CheckESignStatus(context.Background(), &esign.CheckESignStatusRequest{
					ClientRequestId: "savingsSign-1",
					Client:          esign.EsignRequestClient_ESIGN_REQUEST_CLIENT_SAVINGS_ACCOUNT,
					Vendor:          esign.EsignRequestVendor_ESIGN_REQUEST_VENDOR_LEEGALITY,
				}).Return(&esign.CheckESignStatusResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil).Times(1)
				mockStruct.mockUserClient.EXPECT().GetUser(context.Background(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_ActorId{
						ActorId: actorId,
					},
				}).Return(&user.GetUserResponse{
					Status: rpc.StatusInternal(),
				}, nil).Times(1)
			},
		},
		{
			name: "Error while fetching bank customer data",
			args: argsFix,
			want: &pb.FetchOrCreateSignAttemptResponse{
				Status: rpc.StatusFromErrorWithDefaultInternal(rpc.StatusAsError(rpc.StatusInternal())),
			},
			wantErr: nil,
			mockSetup: func(mockStruct *mockStruct) {
				mockStruct.dao.EXPECT().GetAccountByActorId(context.Background(), actorId).Return(accountFixWithId, nil).Times(1)
				mockStruct.mockOperStatus.EXPECT().GetOperationalStatus(context.Background(), operStatusLastData).Return(&operstatus.GetOperationalStatusResponse{
					Status: rpc.StatusOk(),
				}, nil).Times(1)
				mockStruct.mockOperStatus.EXPECT().GetOperationalStatus(context.Background(), operStatusStale).Return(&operstatus.GetOperationalStatusResponse{
					Status: rpc.StatusOk(),
				}, nil).Times(1)
				mockStruct.mockESign.EXPECT().CheckESignStatus(context.Background(), &esign.CheckESignStatusRequest{
					ClientRequestId: "savingsSign-1",
					Client:          esign.EsignRequestClient_ESIGN_REQUEST_CLIENT_SAVINGS_ACCOUNT,
					Vendor:          esign.EsignRequestVendor_ESIGN_REQUEST_VENDOR_LEEGALITY,
				}).Return(&esign.CheckESignStatusResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil).Times(1)
				mockStruct.mockUserClient.EXPECT().GetUser(context.Background(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_ActorId{
						ActorId: actorId,
					},
				}).Return(&user.GetUserResponse{
					Status: rpc.StatusOk(),
				}, nil).Times(1)
				mockStruct.mockBcClient.EXPECT().GetBankCustomer(context.Background(), &bankcust.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankcust.GetBankCustomerRequest_ActorId{
						ActorId: actorId,
					},
				}).Return(&bankcust.GetBankCustomerResponse{
					Status: rpc.StatusInternal(),
				}, nil).Times(1)
			},
		},
		{
			name: "Error  in initiate sign",
			args: argsFix,
			want: &pb.FetchOrCreateSignAttemptResponse{
				Status: rpc.StatusFromErrorWithDefaultInternal(rpc.StatusAsError(rpc.StatusInternal())),
			},
			wantErr: nil,
			mockSetup: func(mockStruct *mockStruct) {
				setupMockCall(mockStruct, &esign.CheckESignStatusResponse{
					Status: rpc.StatusRecordNotFound(),
				})
				mockStruct.mockESign.EXPECT().InitiateESign(context.Background(), gomock.Any()).Return(
					&esign.InitiateESignResponse{
						Status: rpc.StatusInternal(),
					}, nil).Times(1)
			},
		},
		{
			name: "Initiate sign successfully in case of entry not exist",
			args: argsFix,
			want: &pb.FetchOrCreateSignAttemptResponse{
				Status:     rpc.StatusOk(),
				SignStatus: pb.SignStatus_SIGN_STATUS_FIRST_ATTEMPT_CREATED,
				SignUrl:    signUrl,
				ExitUrl:    "https://fi.money/d2VhbHRoLWFhZGhhYXItZS1zaWdu",
			},
			wantErr: nil,
			mockSetup: func(mockStruct *mockStruct) {
				setupMockCall(mockStruct, &esign.CheckESignStatusResponse{
					Status: rpc.StatusRecordNotFound(),
				})
				mockStruct.mockESign.EXPECT().InitiateESign(context.Background(), gomock.Any()).Return(
					&esign.InitiateESignResponse{
						Status:  rpc.StatusOk(),
						SignUrl: signUrl,
					}, nil).Times(1)
			},
		},
		{
			name: "Return last status since last attempt was in created state",
			args: argsFix,
			want: &pb.FetchOrCreateSignAttemptResponse{
				Status:     rpc.StatusOk(),
				SignStatus: pb.SignStatus_SIGN_STATUS_LAST_ATTEMPT_IN_PROGRESS,
				SignUrl:    signUrl,
				ExitUrl:    "https://fi.money/d2VhbHRoLWFhZGhhYXItZS1zaWdu",
			},
			wantErr: nil,
			mockSetup: func(mockStruct *mockStruct) {
				mockStruct.dao.EXPECT().GetAccountByActorId(context.Background(), actorId).Return(accountFixWithId, nil).Times(1)
				mockStruct.mockOperStatus.EXPECT().GetOperationalStatus(context.Background(), operStatusLastData).Return(&operstatus.GetOperationalStatusResponse{
					Status: rpc.StatusOk(),
				}, nil).Times(1)
				mockStruct.mockOperStatus.EXPECT().GetOperationalStatus(context.Background(), operStatusStale).Return(&operstatus.GetOperationalStatusResponse{
					Status: rpc.StatusOk(),
				}, nil).Times(1)
				mockStruct.mockESign.EXPECT().CheckESignStatus(context.Background(), &esign.CheckESignStatusRequest{
					ClientRequestId: "savingsSign-1",
					Client:          esign.EsignRequestClient_ESIGN_REQUEST_CLIENT_SAVINGS_ACCOUNT,
					Vendor:          esign.EsignRequestVendor_ESIGN_REQUEST_VENDOR_LEEGALITY,
				}).Return(&esign.CheckESignStatusResponse{
					Status:      rpc.StatusOk(),
					ESignStatus: esign.EsignRequestStatus_ESIGN_REQUEST_STATUS_CREATED,
					SignUrl:     signUrl,
				}, nil).Times(1)
			},
		},
		{
			name: "Return last status since last attempt was in pending state",
			args: argsFix,
			want: &pb.FetchOrCreateSignAttemptResponse{
				Status:     rpc.StatusOk(),
				SignStatus: pb.SignStatus_SIGN_STATUS_LAST_ATTEMPT_IN_PROGRESS,
				SignUrl:    signUrl,
				ExitUrl:    "https://fi.money/d2VhbHRoLWFhZGhhYXItZS1zaWdu",
			},
			wantErr: nil,
			mockSetup: func(mockStruct *mockStruct) {
				mockStruct.dao.EXPECT().GetAccountByActorId(context.Background(), actorId).Return(accountFixWithId, nil).Times(1)
				mockStruct.mockOperStatus.EXPECT().GetOperationalStatus(context.Background(), operStatusLastData).Return(&operstatus.GetOperationalStatusResponse{
					Status: rpc.StatusOk(),
				}, nil).Times(1)
				mockStruct.mockOperStatus.EXPECT().GetOperationalStatus(context.Background(), operStatusStale).Return(&operstatus.GetOperationalStatusResponse{
					Status: rpc.StatusOk(),
				}, nil).Times(1)
				mockStruct.mockESign.EXPECT().CheckESignStatus(context.Background(), &esign.CheckESignStatusRequest{
					ClientRequestId: "savingsSign-1",
					Client:          esign.EsignRequestClient_ESIGN_REQUEST_CLIENT_SAVINGS_ACCOUNT,
					Vendor:          esign.EsignRequestVendor_ESIGN_REQUEST_VENDOR_LEEGALITY,
				}).Return(&esign.CheckESignStatusResponse{
					Status:      rpc.StatusOk(),
					ESignStatus: esign.EsignRequestStatus_ESIGN_REQUEST_STATUS_PENDING,
					SignUrl:     signUrl,
				}, nil)
			},
		},
		{
			name: "Initiate sign successfully since last attempt failed",
			args: argsFix,
			want: &pb.FetchOrCreateSignAttemptResponse{
				Status:     rpc.StatusOk(),
				SignStatus: pb.SignStatus_SIGN_STATUS_LAST_ATTEMPT_FAILED,
				SignUrl:    signUrl,
				ExitUrl:    "https://fi.money/d2VhbHRoLWFhZGhhYXItZS1zaWdu",
			},
			wantErr: nil,
			mockSetup: func(mockStruct *mockStruct) {
				setupMockCall(mockStruct, &esign.CheckESignStatusResponse{
					Status:      rpc.StatusOk(),
					ESignStatus: esign.EsignRequestStatus_ESIGN_REQUEST_STATUS_FAILED,
				})
				mockStruct.mockIdGen.EXPECT().GetInAlphaNumeric(gomock.Any()).Return("savingsSign-1").Times(1)
				mockStruct.mockESign.EXPECT().InitiateESign(context.Background(), gomock.Any()).Return(
					&esign.InitiateESignResponse{
						Status:  rpc.StatusOk(),
						SignUrl: signUrl,
					}, nil).Times(1)
				mockStruct.dao.EXPECT().UpdateAccount(context.Background(), &pb.Account{
					AccountNo: "acc-no",
					Id:        "acc-id",
					SignInfo: &pb.SignInfo{
						ClientReqId:     "savingsSign-1",
						LastClientReqId: "savingsSign-1",
						Attempt:         2,
					},
				}, []pb.AccountFieldMask{
					pb.AccountFieldMask_SIGN_INFO,
				}).Return(nil).Times(1)
				mockStruct.mockEventBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).MaxTimes(1)
			},
		},
		{
			name: "Return in review since last attempt is still in review",
			args: argsFix,
			want: &pb.FetchOrCreateSignAttemptResponse{
				Status:     rpc.StatusOk(),
				SignStatus: pb.SignStatus_SIGN_STATUS_SIGN_IN_REVIEW,
			},
			wantErr: nil,
			mockSetup: func(mockStruct *mockStruct) {
				mockStruct.dao.EXPECT().GetAccountByActorId(context.Background(), actorId).Return(accountFixWithId, nil).Times(1)
				mockStruct.mockOperStatus.EXPECT().GetOperationalStatus(context.Background(), operStatusLastData).Return(&operstatus.GetOperationalStatusResponse{
					Status: rpc.StatusOk(),
				}, nil).Times(1)
				mockStruct.mockOperStatus.EXPECT().GetOperationalStatus(context.Background(), operStatusStale).Return(&operstatus.GetOperationalStatusResponse{
					Status: rpc.StatusOk(),
				}, nil).Times(1)
				mockStruct.mockESign.EXPECT().CheckESignStatus(context.Background(), &esign.CheckESignStatusRequest{
					ClientRequestId: "savingsSign-1",
					Client:          esign.EsignRequestClient_ESIGN_REQUEST_CLIENT_SAVINGS_ACCOUNT,
					Vendor:          esign.EsignRequestVendor_ESIGN_REQUEST_VENDOR_LEEGALITY,
				}).Return(&esign.CheckESignStatusResponse{
					Status:      rpc.StatusOk(),
					ESignStatus: esign.EsignRequestStatus_ESIGN_REQUEST_STATUS_SUCCESS,
					SignedAt:    timestamppb.Now(),
				}, nil)
			},
		},
		{
			name: "New attempt created since last bank didn't approve",
			args: argsFix,
			want: &pb.FetchOrCreateSignAttemptResponse{
				Status:     rpc.StatusOk(),
				SignStatus: pb.SignStatus_SIGN_STATUS_LAST_ATTEMPT_FAILED_SINCE_PENDING_AT_BANK,
				SignUrl:    signUrl,
				ExitUrl:    "https://fi.money/d2VhbHRoLWFhZGhhYXItZS1zaWdu",
			},
			wantErr: nil,
			mockSetup: func(mockStruct *mockStruct) {
				setupMockCall(mockStruct, &esign.CheckESignStatusResponse{
					Status:      rpc.StatusOk(),
					ESignStatus: esign.EsignRequestStatus_ESIGN_REQUEST_STATUS_SUCCESS,
					SignedAt:    timestamppb.New(timestamppb.Now().AsTime().Add(-6 * 24 * time.Hour)),
				})
				mockStruct.mockIdGen.EXPECT().GetInAlphaNumeric(gomock.Any()).Return("savingsSign-1").Times(1)
				mockStruct.mockESign.EXPECT().InitiateESign(context.Background(), gomock.Any()).Return(
					&esign.InitiateESignResponse{
						Status:  rpc.StatusOk(),
						SignUrl: signUrl,
					}, nil).Times(1)
				mockStruct.dao.EXPECT().UpdateAccount(context.Background(), &pb.Account{
					AccountNo: "acc-no",
					Id:        "acc-id",
					SignInfo: &pb.SignInfo{
						ClientReqId:     "savingsSign-1",
						LastClientReqId: "savingsSign-1",
						Attempt:         2,
					},
				}, []pb.AccountFieldMask{
					pb.AccountFieldMask_SIGN_INFO,
				}).Return(nil).Times(1)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			mockSavingsDao := daoMocks.NewMockSavingsDao(ctr)
			mockOperationalStatus := mocks3.NewMockOperationalStatusServiceClient(ctr)
			mockESign := mocks4.NewMockESignClient(ctr)
			mockActor := actorMock.NewMockActorClient(ctr)
			mockIdgen := mocks5.NewMockIdGenerator(ctr)
			mockUser := mockUser.NewMockUsersClient(ctr)
			mockBcClient := bankCustMock.NewMockBankCustomerServiceClient(ctr)
			mockEventBroker := mockEvents.NewMockBroker(ctr)
			timeClient := dateTimeMocks.NewMockTime(ctr)
			ts := service.NewSavingsService(mockSavingsDao, nil, mockIdgen, mockUser, nil, nil, mockActor, nil, nil, conf, nil, nil, nil, nil, nil, nil, nil, mockBcClient, nil, genconf, nil, nil, mockOperationalStatus, mockESign, nil, nil, nil, nil, mockEventBroker, nil, nil, nil, nil, timeClient, nil, nil, nil)
			defer func() {
				ctr.Finish()
				ts = nil
			}()
			if tt.mockSetup != nil {
				tt.mockSetup(&mockStruct{
					dao:             mockSavingsDao,
					mockOperStatus:  mockOperationalStatus,
					mockIdGen:       mockIdgen,
					mockESign:       mockESign,
					mockUserClient:  mockUser,
					mockBcClient:    mockBcClient,
					mockEventBroker: mockEventBroker,
					mockTimeClient:  timeClient,
				})
			}
			got, err := ts.FetchOrCreateSignAttempt(tt.args.ctx, tt.args.req)
			if !errors.Is(err, tt.wantErr) {
				t.Errorf("FetchOrCreateSignAttempt() error = %v, wantErr %v", err, tt.wantErr)
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("FetchOrCreateSignAttempt() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSavingsService_GetSKUInfo(t *testing.T) {
	t.Parallel()
	now := time.Now()
	actorId := "act-id"
	userFix := &user.User{
		Id: "user-id",
		Profile: &user.Profile{
			PAN: "**********",
			DateOfBirth: &date.Date{
				Year:  1999,
				Month: 8,
				Day:   30,
			},
			PhoneNumber: &commontypes.PhoneNumber{
				CountryCode:    91,
				NationalNumber: **********,
			},
			Email: "<EMAIL>",
		},
	}
	nriUser := &user.User{
		Id: "user-id-nri",
		Profile: &user.Profile{
			PhoneNumber: &commontypes.PhoneNumber{
				CountryCode:    971, // UAE country code
				NationalNumber: **********,
			},
		},
	}
	bankCustMinKyc := &bankcust.BankCustomer{
		ActorId: actorId,
		Vendor:  commonvgpb.Vendor_FEDERAL_BANK,
		KycInfo: &bankcust.KYCInfo{
			KycLevel: kycPb.KYCLevel_MIN_KYC,
		},
		VendorCustomerId:          "12345",
		VendorCreationSucceededAt: timestamppb.New(now.Add(-4 * time.Hour)),
		FiCreationSucceededAt:     timestamppb.New(now.Add(-4 * time.Hour)),
	}
	bankCustFullKyc := &bankcust.BankCustomer{
		ActorId: actorId,
		Vendor:  commonvgpb.Vendor_FEDERAL_BANK,
		KycInfo: &bankcust.KYCInfo{
			KycLevel: kycPb.KYCLevel_FULL_KYC,
		},
		VendorCustomerId:          "12345",
		VendorCreationSucceededAt: timestamppb.New(now.Add(-4 * time.Hour)),
		FiCreationSucceededAt:     timestamppb.New(now.Add(-4 * time.Hour)),
	}
	bankCustRecentUser := &bankcust.BankCustomer{
		ActorId: actorId,
		Vendor:  commonvgpb.Vendor_FEDERAL_BANK,
		KycInfo: &bankcust.KYCInfo{
			KycLevel: kycPb.KYCLevel_FULL_KYC,
		},
		VendorCustomerId:          "12345",
		VendorCreationSucceededAt: timestamppb.New(now.Add(-2 * time.Hour)),
		FiCreationSucceededAt:     timestamppb.New(now.Add(-2 * time.Hour)),
	}
	bankCustNRI := &bankcust.BankCustomer{
		ActorId: actorId,
		Vendor:  commonvgpb.Vendor_FEDERAL_BANK,
		KycInfo: &bankcust.KYCInfo{
			KycLevel: kycPb.KYCLevel_FULL_KYC,
		},
		VendorCreationSucceededAt: timestamppb.New(now.Add(-4 * time.Hour)),
		FiCreationSucceededAt:     timestamppb.New(now.Add(-4 * time.Hour)),
		Source:                    bankcust.Source_SOURCE_NON_RESIDENT_SAVINGS_ACCOUNT_ONBOARDING,
	}

	type mockStruct struct {
		userClient *mockUser.MockUsersClient
		bcClient   *bankCustMock.MockBankCustomerServiceClient
		timeClient *dateTimeMocks.MockTime
	}
	type args struct {
		ctx                         context.Context
		user                        *userPb.User
		bankCustomer                *bankcust.BankCustomer
		reqSKU                      pb.SKU
		userAllowedToOpenAmbAccount bool
	}
	tests := map[string]struct {
		args               args
		want               pb.SKU
		errorAssertionFunc assert.ErrorAssertionFunc
		wantMocks          func(m *mockStruct, args args)
	}{
		"current SKU Level is INFINITY": {
			args: args{
				ctx:          context.Background(),
				user:         userFix,
				bankCustomer: bankCustFullKyc,
			},
			want:               pb.SKU_INFINITY,
			errorAssertionFunc: assert.NoError,
			wantMocks: func(m *mockStruct, args args) {
				m.timeClient.EXPECT().Now().Return(now)
				m.userClient.EXPECT().DedupeCheck(gomock.Any(), &userPb.DedupeCheckRequest{
					ActorId:     actorId,
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					PanNumber:   userFix.GetProfile().GetPAN(),
					PhoneNumber: userFix.GetProfile().GetPhoneNumber(),
					UserId:      userFix.GetId(),
					DateOfBirth: userFix.GetProfile().GetDateOfBirth(),
					EmailId:     userFix.GetProfile().GetEmail(),
					RequestType: userPb.DedupeCheckRequest_REQUEST_TYPE_BEST_EFFORT,
					DedupeFlow:  userPb.DedupeCheckRequest_FLOW_PRE_ACCOUNT_CREATION,
				}).Return(&user.DedupeCheckResponse{
					Status:                         rpc.StatusOk(),
					CustomersLinkedWithPhoneNumber: 1,
					DedupeStatus:                   customer.DedupeStatus_CUSTOMER_EXISTS,
				}, nil)
			},
		},
		"current SKU Level is SKU_REGULAR_FULL_KYC": {
			args: args{
				ctx:                         context.Background(),
				user:                        userFix,
				bankCustomer:                bankCustFullKyc,
				userAllowedToOpenAmbAccount: true,
			},
			want:               pb.SKU_REGULAR_FULL_KYC,
			errorAssertionFunc: assert.NoError,
			wantMocks: func(m *mockStruct, args args) {
				m.timeClient.EXPECT().Now().Return(now)
				m.userClient.EXPECT().DedupeCheck(gomock.Any(), &userPb.DedupeCheckRequest{
					ActorId:     actorId,
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					PanNumber:   userFix.GetProfile().GetPAN(),
					PhoneNumber: userFix.GetProfile().GetPhoneNumber(),
					UserId:      userFix.GetId(),
					DateOfBirth: userFix.GetProfile().GetDateOfBirth(),
					EmailId:     userFix.GetProfile().GetEmail(),
					RequestType: userPb.DedupeCheckRequest_REQUEST_TYPE_BEST_EFFORT,
					DedupeFlow:  userPb.DedupeCheckRequest_FLOW_PRE_ACCOUNT_CREATION,
				}).Return(&user.DedupeCheckResponse{
					Status:                         rpc.StatusOk(),
					CustomersLinkedWithPhoneNumber: 1,
					DedupeStatus:                   customer.DedupeStatus_CUSTOMER_EXISTS,
				}, nil)
			},
		},
		"current SKU Level is SKU_REGULAR_MIN_KYC": {
			args: args{
				ctx:                         context.Background(),
				user:                        userFix,
				bankCustomer:                bankCustMinKyc,
				userAllowedToOpenAmbAccount: true,
			},
			want:               pb.SKU_REGULAR_MIN_KYC,
			errorAssertionFunc: assert.NoError,
			wantMocks: func(m *mockStruct, args args) {
				m.timeClient.EXPECT().Now().Return(now)
				m.userClient.EXPECT().DedupeCheck(gomock.Any(), &userPb.DedupeCheckRequest{
					ActorId:     actorId,
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					PanNumber:   userFix.GetProfile().GetPAN(),
					PhoneNumber: userFix.GetProfile().GetPhoneNumber(),
					UserId:      userFix.GetId(),
					DateOfBirth: userFix.GetProfile().GetDateOfBirth(),
					EmailId:     userFix.GetProfile().GetEmail(),
					RequestType: userPb.DedupeCheckRequest_REQUEST_TYPE_BEST_EFFORT,
					DedupeFlow:  userPb.DedupeCheckRequest_FLOW_PRE_ACCOUNT_CREATION,
				}).Return(&user.DedupeCheckResponse{
					Status:                         rpc.StatusOk(),
					CustomersLinkedWithPhoneNumber: 1,
					DedupeStatus:                   customer.DedupeStatus_CUSTOMER_EXISTS,
				}, nil)
			},
		},
		"error in dedupe": {
			args: args{
				ctx:          context.Background(),
				user:         userFix,
				bankCustomer: bankCustMinKyc,
			},
			want:               pb.SKU_MIN_KYC,
			errorAssertionFunc: assert.Error,
			wantMocks: func(m *mockStruct, args args) {
				m.timeClient.EXPECT().Now().Return(now)
				m.userClient.EXPECT().DedupeCheck(gomock.Any(), &userPb.DedupeCheckRequest{
					ActorId:     actorId,
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					PanNumber:   userFix.GetProfile().GetPAN(),
					PhoneNumber: userFix.GetProfile().GetPhoneNumber(),
					UserId:      userFix.GetId(),
					DateOfBirth: userFix.GetProfile().GetDateOfBirth(),
					EmailId:     userFix.GetProfile().GetEmail(),
					RequestType: userPb.DedupeCheckRequest_REQUEST_TYPE_BEST_EFFORT,
					DedupeFlow:  userPb.DedupeCheckRequest_FLOW_PRE_ACCOUNT_CREATION,
				}).Return(nil, fmt.Errorf("some error"))
			},
		},
		"dedupe status partial etb": {
			args: args{
				ctx:          context.Background(),
				user:         userFix,
				bankCustomer: bankCustMinKyc,
			},
			want:               pb.SKU_MIN_KYC,
			errorAssertionFunc: assert.NoError,
			wantMocks: func(m *mockStruct, args args) {
				m.timeClient.EXPECT().Now().Return(now)
				m.userClient.EXPECT().DedupeCheck(gomock.Any(), &userPb.DedupeCheckRequest{
					ActorId:     actorId,
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					PanNumber:   userFix.GetProfile().GetPAN(),
					PhoneNumber: userFix.GetProfile().GetPhoneNumber(),
					UserId:      userFix.GetId(),
					DateOfBirth: userFix.GetProfile().GetDateOfBirth(),
					EmailId:     userFix.GetProfile().GetEmail(),
					RequestType: userPb.DedupeCheckRequest_REQUEST_TYPE_BEST_EFFORT,
					DedupeFlow:  userPb.DedupeCheckRequest_FLOW_PRE_ACCOUNT_CREATION,
				}).Return(&user.DedupeCheckResponse{
					Status:                         rpc.StatusOk(),
					CustomersLinkedWithPhoneNumber: 1,
					DedupeStatus:                   customer.DedupeStatus_CUSTOMER_EXISTS_PARTIAL_KYC,
				}, nil)
			},
		},
		"dedupe status customer exists but vendor id does not match": {
			args: args{
				ctx:          context.Background(),
				user:         userFix,
				bankCustomer: bankCustMinKyc,
			},
			want:               pb.SKU_MIN_KYC,
			errorAssertionFunc: assert.NoError,
			wantMocks: func(m *mockStruct, args args) {
				m.timeClient.EXPECT().Now().Return(now)
				m.userClient.EXPECT().DedupeCheck(gomock.Any(), &userPb.DedupeCheckRequest{
					ActorId:     actorId,
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					PanNumber:   userFix.GetProfile().GetPAN(),
					PhoneNumber: userFix.GetProfile().GetPhoneNumber(),
					UserId:      userFix.GetId(),
					DateOfBirth: userFix.GetProfile().GetDateOfBirth(),
					EmailId:     userFix.GetProfile().GetEmail(),
					RequestType: userPb.DedupeCheckRequest_REQUEST_TYPE_BEST_EFFORT,
					DedupeFlow:  userPb.DedupeCheckRequest_FLOW_PRE_ACCOUNT_CREATION,
				}).Return(&user.DedupeCheckResponse{
					Status:                         rpc.StatusOk(),
					CustomerId:                     "1234",
					CustomersLinkedWithPhoneNumber: 1,
					DedupeStatus:                   customer.DedupeStatus_CUSTOMER_EXISTS,
				}, nil)
			},
		},
		"dedupe status customer exists and vendor id matches": {
			args: args{
				ctx:          context.Background(),
				user:         userFix,
				bankCustomer: bankCustMinKyc,
			},
			want:               pb.SKU_INFINITY,
			errorAssertionFunc: assert.NoError,
			wantMocks: func(m *mockStruct, args args) {
				m.timeClient.EXPECT().Now().Return(now)
				m.userClient.EXPECT().DedupeCheck(gomock.Any(), &userPb.DedupeCheckRequest{
					ActorId:     actorId,
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					PanNumber:   userFix.GetProfile().GetPAN(),
					PhoneNumber: userFix.GetProfile().GetPhoneNumber(),
					UserId:      userFix.GetId(),
					DateOfBirth: userFix.GetProfile().GetDateOfBirth(),
					EmailId:     userFix.GetProfile().GetEmail(),
					RequestType: userPb.DedupeCheckRequest_REQUEST_TYPE_BEST_EFFORT,
					DedupeFlow:  userPb.DedupeCheckRequest_FLOW_PRE_ACCOUNT_CREATION,
				}).Return(&user.DedupeCheckResponse{
					Status:                         rpc.StatusOk(),
					CustomerId:                     "12345",
					CustomersLinkedWithPhoneNumber: 1,
					DedupeStatus:                   customer.DedupeStatus_CUSTOMER_EXISTS,
				}, nil)
				m.bcClient.EXPECT().UpdateKycLevel(gomock.Any(), &bankcust.UpdateKycLevelRequest{
					ActorId:            args.bankCustomer.GetActorId(),
					Vendor:             args.bankCustomer.GetVendor(),
					KycLevel:           kycPb.KYCLevel_FULL_KYC,
					KycLevelUpdateFlow: bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_DEDUPE_CUSTOMER,
				}).Return(&bankcust.UpdateKycLevelResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
		},
		"dedupe status customer exists phone mismatch": {
			args: args{
				ctx:          context.Background(),
				user:         userFix,
				bankCustomer: bankCustMinKyc,
			},
			want: pb.SKU_ACCOUNT_SKU_UNSPECIFIED,
			errorAssertionFunc: func(t assert.TestingT, err error, i ...interface{}) bool {
				return err != nil && err.Error() == "unsupported dedupe status before account creation"
			},
			wantMocks: func(m *mockStruct, args args) {
				m.timeClient.EXPECT().Now().Return(now)
				m.userClient.EXPECT().DedupeCheck(gomock.Any(), &userPb.DedupeCheckRequest{
					ActorId:     actorId,
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					PanNumber:   userFix.GetProfile().GetPAN(),
					PhoneNumber: userFix.GetProfile().GetPhoneNumber(),
					UserId:      userFix.GetId(),
					DateOfBirth: userFix.GetProfile().GetDateOfBirth(),
					EmailId:     userFix.GetProfile().GetEmail(),
					RequestType: userPb.DedupeCheckRequest_REQUEST_TYPE_BEST_EFFORT,
					DedupeFlow:  userPb.DedupeCheckRequest_FLOW_PRE_ACCOUNT_CREATION,
				}).Return(&user.DedupeCheckResponse{
					Status:                         rpc.StatusOk(),
					CustomerId:                     "1234",
					CustomersLinkedWithPhoneNumber: 1,
					DedupeStatus:                   customer.DedupeStatus_CUSTOMER_EXISTS_PHONE_MISMATCH,
				}, nil)
			},
		},
		"recent customer creation user": {
			args: args{
				ctx:          context.Background(),
				user:         userFix,
				bankCustomer: bankCustRecentUser,
			},
			want:               pb.SKU_INFINITY,
			errorAssertionFunc: assert.NoError,
			wantMocks: func(m *mockStruct, args args) {
				m.timeClient.EXPECT().Now().Return(now)
			},
		},
		"valid requested sku": {
			args: args{
				ctx:          context.Background(),
				user:         nriUser,
				bankCustomer: bankCustNRI,
				reqSKU:       pb.SKU_NRE_ORIGINAL,
			},
			want:               pb.SKU_NRE_ORIGINAL,
			errorAssertionFunc: assert.NoError,
		},
		"error in requested sku validation: invalid sku type": {
			args: args{
				ctx:          context.Background(),
				user:         userFix,
				bankCustomer: bankCustMinKyc,
				reqSKU:       pb.SKU_INFINITY,
			},
			want: pb.SKU_ACCOUNT_SKU_UNSPECIFIED,
			errorAssertionFunc: func(t assert.TestingT, err error, i ...interface{}) bool {
				return errors.Is(err, service.ErrValidateRequestedSKU)
			},
		},
		"error in requested sku validation: invalid phone number": {
			args: args{
				ctx:          context.Background(),
				user:         userFix,
				bankCustomer: bankCustMinKyc,
				reqSKU:       pb.SKU_NRE_ORIGINAL,
			},
			want: pb.SKU_ACCOUNT_SKU_UNSPECIFIED,
			errorAssertionFunc: func(t assert.TestingT, err error, i ...interface{}) bool {
				return errors.Is(err, service.ErrValidateRequestedSKU)
			},
		},
		"error in resident indian validation: invalid phone number": {
			args: args{
				ctx:          context.Background(),
				user:         nriUser,
				bankCustomer: bankCustMinKyc,
			},
			want: pb.SKU_ACCOUNT_SKU_UNSPECIFIED,
			errorAssertionFunc: func(t assert.TestingT, err error, i ...interface{}) bool {
				return err != nil && err.Error() == "resident indian validation failed: ph num isd code must be 91"
			},
		},
		"error in resident indian validation: invalid bank cust source": {
			args: args{
				ctx:          context.Background(),
				user:         userFix,
				bankCustomer: bankCustNRI,
			},
			want: pb.SKU_ACCOUNT_SKU_UNSPECIFIED,
			errorAssertionFunc: func(t assert.TestingT, err error, i ...interface{}) bool {
				return err != nil && err.Error() == "resident indian validation failed: cif source must be non nri"
			},
		},
	}
	for name, tt := range tests {
		name := name
		tt := tt
		t.Run(name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			mockUserClient := mockUser.NewMockUsersClient(ctrl)
			mockBcClient := bankCustMock.NewMockBankCustomerServiceClient(ctrl)
			timeClient := dateTimeMocks.NewMockTime(ctrl)
			s := service.NewSavingsService(nil, nil, nil, mockUserClient, nil, nil, nil, nil, nil, conf, nil, nil, nil, nil, nil, nil, nil, mockBcClient, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, timeClient, nil, nil, nil)
			if tt.wantMocks != nil {
				tt.wantMocks(&mockStruct{
					userClient: mockUserClient,
					bcClient:   mockBcClient,
					timeClient: timeClient,
				}, tt.args)
			}
			got, err := s.EvalSKUType(tt.args.ctx, tt.args.user, tt.args.bankCustomer, tt.args.reqSKU, tt.args.userAllowedToOpenAmbAccount)
			if !tt.errorAssertionFunc(t, err, fmt.Sprintf("EvalSKUType(%v, %v, %v)", tt.args.ctx, tt.args.user, tt.args.bankCustomer)) {
				assert.Failf(t, "unexpected error in EvalSKUType", "got error: %v", err)
			}
			assert.Equalf(t, tt.want, got, "EvalSKUType(%v, %v, %v)", tt.args.ctx, tt.args.user, tt.args.bankCustomer)
		})
	}
}

func TestSavingsService_GetSavingsAccountNominees(t *testing.T) {
	t.Parallel()

	type mockStruct struct {
		userClient *mockUser.MockUsersClient
		dao        *daoMocks.MockSavingsDao
	}

	type args struct {
		ctx context.Context
		req *pb.GetSavingsAccountNomineesRequest
	}

	tests := []struct {
		name               string
		args               args
		want               *pb.GetSavingsAccountNomineesResponse
		errorAssertionFunc assert.ErrorAssertionFunc
		wantMocks          func(m *mockStruct, args args)
	}{
		{
			name: "should return invalid argument when actor id is empty",
			args: args{
				ctx: context.Background(),
				req: &pb.GetSavingsAccountNomineesRequest{
					ActorId: "",
				},
			},
			want: &pb.GetSavingsAccountNomineesResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("Empty actor id"),
			},
			errorAssertionFunc: assert.NoError,
			wantMocks:          nil,
		},
		{
			name: "should return record not found when savings account does not exist for actor",
			args: args{
				ctx: context.Background(),
				req: &pb.GetSavingsAccountNomineesRequest{
					ActorId: "actor-123",
				},
			},
			want: &pb.GetSavingsAccountNomineesResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			errorAssertionFunc: assert.NoError,
			wantMocks: func(m *mockStruct, args args) {
				m.dao.EXPECT().GetAccountByActorId(args.ctx, "actor-123").
					Return(nil, epifierrors.ErrRecordNotFound)
			},
		},
		{
			name: "should return internal error when dao fails to fetch account",
			args: args{
				ctx: context.Background(),
				req: &pb.GetSavingsAccountNomineesRequest{
					ActorId: "actor-123",
				},
			},
			want: &pb.GetSavingsAccountNomineesResponse{
				Status: rpc.StatusInternal(),
			},
			errorAssertionFunc: assert.NoError,
			wantMocks: func(m *mockStruct, args args) {
				m.dao.EXPECT().GetAccountByActorId(args.ctx, "actor-123").
					Return(nil, errors.New("database error"))
			},
		},
		{
			name: "should return success with empty nominees when account has no nominee details",
			args: args{
				ctx: context.Background(),
				req: &pb.GetSavingsAccountNomineesRequest{
					ActorId: "actor-123",
				},
			},
			want: &pb.GetSavingsAccountNomineesResponse{
				Status: rpc.StatusOk(),
			},
			errorAssertionFunc: assert.NoError,
			wantMocks: func(m *mockStruct, args args) {
				m.dao.EXPECT().GetAccountByActorId(args.ctx, "actor-123").
					Return(&pb.Account{
						Id:             "savings-123",
						ActorId:        "actor-123",
						NomineeDetails: nil,
					}, nil)
			},
		},
		{
			name: "should return success with empty nominees when nominee details contains nil nominees",
			args: args{
				ctx: context.Background(),
				req: &pb.GetSavingsAccountNomineesRequest{
					ActorId: "actor-123",
				},
			},
			want: &pb.GetSavingsAccountNomineesResponse{
				Status: rpc.StatusOk(),
			},
			errorAssertionFunc: assert.NoError,
			wantMocks: func(m *mockStruct, args args) {
				m.dao.EXPECT().GetAccountByActorId(args.ctx, "actor-123").
					Return(&pb.Account{
						Id:      "savings-123",
						ActorId: "actor-123",
						NomineeDetails: &pb.NomineeDetails{
							Nominees: nil,
						},
					}, nil)
			},
		},
		{
			name: "should return success with empty nominees when nominee id is empty",
			args: args{
				ctx: context.Background(),
				req: &pb.GetSavingsAccountNomineesRequest{
					ActorId: "actor-123",
				},
			},
			want: &pb.GetSavingsAccountNomineesResponse{
				Status: rpc.StatusOk(),
			},
			errorAssertionFunc: assert.NoError,
			wantMocks: func(m *mockStruct, args args) {
				m.dao.EXPECT().GetAccountByActorId(args.ctx, "actor-123").
					Return(&pb.Account{
						Id:      "savings-123",
						ActorId: "actor-123",
						NomineeDetails: &pb.NomineeDetails{
							Nominees: &pb.Nominees{
								NomineeWithShares: []*pb.NomineeWithShare{
									{
										NomineeId:       "",
										PercentageShare: 100,
									},
								},
							},
						},
					}, nil)
			},
		},
		{
			name: "should return success with single nominee when account has one valid nominee",
			args: args{
				ctx: context.Background(),
				req: &pb.GetSavingsAccountNomineesRequest{
					ActorId: "actor-123",
				},
			},
			want: &pb.GetSavingsAccountNomineesResponse{
				Status: rpc.StatusOk(),
				Nominee: []*typesPb.Nominee{
					{
						Id:   "nominee-1",
						Name: "John Doe",
					},
				},
				SavingsAccountNominees: []*pb.SavingsAccountNominee{
					{
						Nominee: &typesPb.Nominee{
							Id:   "nominee-1",
							Name: "John Doe",
						},
						PercentageShare: 100,
					},
				},
			},
			errorAssertionFunc: assert.NoError,
			wantMocks: func(m *mockStruct, args args) {
				m.dao.EXPECT().GetAccountByActorId(args.ctx, "actor-123").
					Return(&pb.Account{
						Id:      "savings-123",
						ActorId: "actor-123",
						NomineeDetails: &pb.NomineeDetails{
							Nominees: &pb.Nominees{
								NomineeWithShares: []*pb.NomineeWithShare{
									{
										NomineeId:       "nominee-1",
										PercentageShare: 100,
									},
								},
							},
						},
					}, nil)

				m.userClient.EXPECT().GetNominee(args.ctx, &userPb.GetNomineeRequest{
					NomineeId: "nominee-1",
					ActorId:   "actor-123",
				}).Return(&userPb.GetNomineeResponse{
					Status: rpc.StatusOk(),
					Nominee: &typesPb.Nominee{
						Id:   "nominee-1",
						Name: "John Doe",
					},
				}, nil)
			},
		},
		{
			name: "should return success with multiple nominees when account has multiple valid nominees",
			args: args{
				ctx: context.Background(),
				req: &pb.GetSavingsAccountNomineesRequest{
					ActorId: "actor-123",
				},
			},
			want: &pb.GetSavingsAccountNomineesResponse{
				Status: rpc.StatusOk(),
				Nominee: []*typesPb.Nominee{
					{
						Id:   "nominee-1",
						Name: "John Doe",
					},
					{
						Id:   "nominee-2",
						Name: "Jane Smith",
					},
				},
				SavingsAccountNominees: []*pb.SavingsAccountNominee{
					{
						Nominee: &typesPb.Nominee{
							Id:   "nominee-1",
							Name: "John Doe",
						},
						PercentageShare: 60,
					},
					{
						Nominee: &typesPb.Nominee{
							Id:   "nominee-2",
							Name: "Jane Smith",
						},
						PercentageShare: 40,
					},
				},
			},
			errorAssertionFunc: assert.NoError,
			wantMocks: func(m *mockStruct, args args) {
				m.dao.EXPECT().GetAccountByActorId(args.ctx, "actor-123").
					Return(&pb.Account{
						Id:      "savings-123",
						ActorId: "actor-123",
						NomineeDetails: &pb.NomineeDetails{
							Nominees: &pb.Nominees{
								NomineeWithShares: []*pb.NomineeWithShare{
									{
										NomineeId:       "nominee-1",
										PercentageShare: 60,
									},
									{
										NomineeId:       "nominee-2",
										PercentageShare: 40,
									},
								},
							},
						},
					}, nil)

				m.userClient.EXPECT().GetNominee(args.ctx, &userPb.GetNomineeRequest{
					NomineeId: "nominee-1",
					ActorId:   "actor-123",
				}).Return(&userPb.GetNomineeResponse{
					Status: rpc.StatusOk(),
					Nominee: &typesPb.Nominee{
						Id:   "nominee-1",
						Name: "John Doe",
					},
				}, nil)

				m.userClient.EXPECT().GetNominee(args.ctx, &userPb.GetNomineeRequest{
					NomineeId: "nominee-2",
					ActorId:   "actor-123",
				}).Return(&userPb.GetNomineeResponse{
					Status: rpc.StatusOk(),
					Nominee: &typesPb.Nominee{
						Id:   "nominee-2",
						Name: "Jane Smith",
					},
				}, nil)
			},
		},
		{
			name: "should return success with nil nominee when user service returns nominee not found",
			args: args{
				ctx: context.Background(),
				req: &pb.GetSavingsAccountNomineesRequest{
					ActorId: "actor-123",
				},
			},
			want: &pb.GetSavingsAccountNomineesResponse{
				Status: rpc.StatusOk(),
				Nominee: []*typesPb.Nominee{
					nil, // This tests the current behavior where nil is added to the list when nominee not found
				},
				SavingsAccountNominees: []*pb.SavingsAccountNominee{
					{
						Nominee:         nil,
						PercentageShare: 100,
					},
				},
			},
			errorAssertionFunc: assert.NoError,
			wantMocks: func(m *mockStruct, args args) {
				m.dao.EXPECT().GetAccountByActorId(args.ctx, "actor-123").
					Return(&pb.Account{
						Id:      "savings-123",
						ActorId: "actor-123",
						NomineeDetails: &pb.NomineeDetails{
							Nominees: &pb.Nominees{
								NomineeWithShares: []*pb.NomineeWithShare{
									{
										NomineeId:       "nominee-1",
										PercentageShare: 100,
									},
								},
							},
						},
					}, nil)

				m.userClient.EXPECT().GetNominee(args.ctx, &userPb.GetNomineeRequest{
					NomineeId: "nominee-1",
					ActorId:   "actor-123",
				}).Return(&userPb.GetNomineeResponse{
					Status:  rpc.StatusOk(),
					Nominee: nil, // nominee not found but status is OK
				}, nil)
			},
		},
		{
			name: "should return internal error when user service fails with internal error",
			args: args{
				ctx: context.Background(),
				req: &pb.GetSavingsAccountNomineesRequest{
					ActorId: "actor-123",
				},
			},
			want: &pb.GetSavingsAccountNomineesResponse{
				Status: rpc.StatusInternal(),
			},
			errorAssertionFunc: assert.NoError,
			wantMocks: func(m *mockStruct, args args) {
				m.dao.EXPECT().GetAccountByActorId(args.ctx, "actor-123").
					Return(&pb.Account{
						Id:      "savings-123",
						ActorId: "actor-123",
						NomineeDetails: &pb.NomineeDetails{
							Nominees: &pb.Nominees{
								NomineeWithShares: []*pb.NomineeWithShare{
									{
										NomineeId:       "nominee-1",
										PercentageShare: 100,
									},
								},
							},
						},
					}, nil)

				m.userClient.EXPECT().GetNominee(args.ctx, &userPb.GetNomineeRequest{
					NomineeId: "nominee-1",
					ActorId:   "actor-123",
				}).Return(&userPb.GetNomineeResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
		},
		{
			name: "should return internal error when user service returns nil response",
			args: args{
				ctx: context.Background(),
				req: &pb.GetSavingsAccountNomineesRequest{
					ActorId: "actor-123",
				},
			},
			want: &pb.GetSavingsAccountNomineesResponse{
				Status: rpc.StatusInternal(),
			},
			errorAssertionFunc: assert.NoError,
			wantMocks: func(m *mockStruct, args args) {
				m.dao.EXPECT().GetAccountByActorId(args.ctx, "actor-123").
					Return(&pb.Account{
						Id:      "savings-123",
						ActorId: "actor-123",
						NomineeDetails: &pb.NomineeDetails{
							Nominees: &pb.Nominees{
								NomineeWithShares: []*pb.NomineeWithShare{
									{
										NomineeId:       "nominee-1",
										PercentageShare: 100,
									},
								},
							},
						},
					}, nil)

				m.userClient.EXPECT().GetNominee(args.ctx, &userPb.GetNomineeRequest{
					NomineeId: "nominee-1",
					ActorId:   "actor-123",
				}).Return(nil, errors.New("network error"))
			},
		},
		{
			name: "should return internal error when processing multiple nominees and one fails",
			args: args{
				ctx: context.Background(),
				req: &pb.GetSavingsAccountNomineesRequest{
					ActorId: "actor-123",
				},
			},
			want: &pb.GetSavingsAccountNomineesResponse{
				Status: rpc.StatusInternal(),
			},
			errorAssertionFunc: assert.NoError,
			wantMocks: func(m *mockStruct, args args) {
				m.dao.EXPECT().GetAccountByActorId(args.ctx, "actor-123").
					Return(&pb.Account{
						Id:      "savings-123",
						ActorId: "actor-123",
						NomineeDetails: &pb.NomineeDetails{
							Nominees: &pb.Nominees{
								NomineeWithShares: []*pb.NomineeWithShare{
									{
										NomineeId:       "nominee-1",
										PercentageShare: 60,
									},
									{
										NomineeId:       "nominee-2",
										PercentageShare: 40,
									},
								},
							},
						},
					}, nil)

				m.userClient.EXPECT().GetNominee(args.ctx, &userPb.GetNomineeRequest{
					NomineeId: "nominee-1",
					ActorId:   "actor-123",
				}).Return(&userPb.GetNomineeResponse{
					Status: rpc.StatusOk(),
					Nominee: &typesPb.Nominee{
						Id:   "nominee-1",
						Name: "John Doe",
					},
				}, nil)

				m.userClient.EXPECT().GetNominee(args.ctx, &userPb.GetNomineeRequest{
					NomineeId: "nominee-2",
					ActorId:   "actor-123",
				}).Return(&userPb.GetNomineeResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockUserClient := mockUser.NewMockUsersClient(ctrl)
			mockDao := daoMocks.NewMockSavingsDao(ctrl)

			s := service.NewSavingsService(
				mockDao,        // dao
				nil,            // savingsAggregationDao
				nil,            // generator
				mockUserClient, // userClient
				nil,            // producer
				nil,            // vgSavingsClient
				nil,            // actorClient
				nil,            // authClient
				nil,            // accountPiClient
				conf,           // conf
				nil,            // reconClient
				nil,            // userProcessor
				nil,            // updatedBalancePublisher
				nil,            // inAppCommsClient
				nil,            // redisClient
				nil,            // cbtDao
				nil,            // balHistoryRedisClient
				nil,            // bcClient
				nil,            // payClient
				nil,            // dynConf
				nil,            // pinotTxnAggregate
				nil,            // esTxnAggregate
				nil,            // operStatusClient
				nil,            // esignClient
				nil,            // lockManager
				nil,            // orderClient
				nil,            // saeDao
				nil,            // balanceChangeEventPub
				nil,            // eventBroker
				nil,            // accountBalanceClient
				nil,            // saClosureDao
				nil,            // saClosureDataProcessor
				nil,            // cacheStorage
				nil,            // timeClient
				nil,            // onboardingClient
				nil,            // paymentClient
				nil,            // externalAccountsClient
			)

			if tt.wantMocks != nil {
				tt.wantMocks(&mockStruct{
					userClient: mockUserClient,
					dao:        mockDao,
				}, tt.args)
			}

			got, err := s.GetSavingsAccountNominees(tt.args.ctx, tt.args.req)
			if !tt.errorAssertionFunc(t, err, fmt.Sprintf("GetSavingsAccountNominees(%v, %v)", tt.args.ctx, tt.args.req)) {
				assert.Failf(t, "unexpected error in GetSavingsAccountNominees", "got error: %v", err)
			}
			assert.Equal(t, tt.want, got, "GetSavingsAccountNominees(%v, %v)", tt.args.ctx, tt.args.req)
		})
	}
}
