package service

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	gmoney "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	storage "github.com/epifi/be-common/pkg/storage/v2"

	operationalStatusEnums "github.com/epifi/gamma/api/accounts/enums"
	"github.com/epifi/gamma/api/accounts/operstatus"
	orderPaymentPb "github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/api/paymentinstrument/account_pi"
	savingsPb "github.com/epifi/gamma/api/savings"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/pkg/savings"

	"go.uber.org/zap"
)

const (
	txnRetries                 = 3
	maxPanVerificationAttempts = 3
	panAttemptTTL              = 24 * time.Hour
	reVkycClosedParticular     = "closure proceed"
)

func (s *SavingsService) StoreClosedAccountBalTransferData(ctx context.Context, req *savingsPb.StoreClosedAccountBalTransferDataRequest) (*savingsPb.StoreClosedAccountBalTransferDataResponse, error) {
	cbt := req.GetData()

	if cbt.GetSavingsAccountId() == "" {
		return &savingsPb.StoreClosedAccountBalTransferDataResponse{
			Status: rpc.StatusInvalidArgument(),
		}, nil
	}

	if txnErr := storage.RunCRDBIdempotentTxn(ctx, txnRetries, func(ctx context.Context) error {
		existingEntries, dbErr := s.cbtDao.GetBySavingsAccountId(ctx, cbt.GetSavingsAccountId())
		if dbErr != nil && !errors.Is(dbErr, epifierrors.ErrRecordNotFound) {
			return dbErr
		}
		for _, record := range existingEntries {
			if isCbtEqual(record, cbt) {
				return epifierrors.ErrAlreadyExists
			}
		}

		if dbErr = s.cbtDao.Create(ctx, cbt); dbErr != nil {
			return dbErr
		}
		return nil
	}); txnErr != nil {
		switch {
		case errors.Is(txnErr, epifierrors.ErrAlreadyExists):
			return &savingsPb.StoreClosedAccountBalTransferDataResponse{
				Status: rpc.StatusAlreadyExists(),
			}, nil
		default:
			logger.Error(ctx, "txn error while storing closed account balance transfer data", zap.Error(txnErr))
			return &savingsPb.StoreClosedAccountBalTransferDataResponse{
				Status: rpc.StatusInternalWithDebugMsg(txnErr.Error()),
			}, nil
		}
	}

	return &savingsPb.StoreClosedAccountBalTransferDataResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func isCbtEqual(a, b *savingsPb.ClosedAccountBalanceTransfer) bool {
	return (a.GetSavingsAccountId() == b.GetSavingsAccountId()) &&
		(a.GetLastKnownBalance() == b.GetLastKnownBalance()) &&
		(a.GetLastKnownBalanceCapturedAt() == b.GetLastKnownBalanceCapturedAt()) &&
		(a.GetBalanceCapturedFromStatement() == b.GetBalanceCapturedFromStatement()) &&
		(a.GetBavId() == b.GetBavId()) &&
		(a.GetUtr() == b.GetUtr()) &&
		(a.GetDateOfTransfer() == b.GetDateOfTransfer()) &&
		(a.GetAmountTransferred() == b.GetAmountTransferred()) &&
		(a.GetTransactionStatus() == b.GetTransactionStatus()) &&
		(strings.EqualFold(a.GetTransactionFailureReason(), b.GetTransactionFailureReason()))
}

// StoreClosedAccountBalTransferDataFromStatement
// 1. checks if the closed account balance transfer data already exists
// 2. if the cbt data already has balance data, this rpc doesn't do any operation
// 3. fetches the closed account balance transfer data from the statement and stores in the db
func (s *SavingsService) StoreClosedAccountBalTransferDataFromStatement(ctx context.Context, req *savingsPb.StoreClosedAccountBalTransferDataFromStatementRequest) (*savingsPb.StoreClosedAccountBalTransferDataFromStatementResponse, error) {
	operStatusResp, operStatusErr := s.operStatusClient.GetOperationalStatus(ctx, &operstatus.GetOperationalStatusRequest{
		DataFreshness:     operstatus.GetOperationalStatusRequest_DATA_FRESHNESS_LAST_KNOWN,
		AccountIdentifier: &operstatus.GetOperationalStatusRequest_SavingsAccountId{SavingsAccountId: req.GetSavingsAccountId()},
	})
	if rpcErr := epifigrpc.RPCError(operStatusResp, operStatusErr); rpcErr != nil {
		logger.Error(ctx, "error while getting operational status", zap.Error(rpcErr))
		return &savingsPb.StoreClosedAccountBalTransferDataFromStatementResponse{
			Status: rpc.StatusInternalWithDebugMsg(rpcErr.Error()),
		}, nil
	}

	if operStatusResp.GetOperationalStatusInfo().GetOperationalStatus() != operationalStatusEnums.OperationalStatus_OPERATIONAL_STATUS_CLOSED {
		return &savingsPb.StoreClosedAccountBalTransferDataFromStatementResponse{
			Status: rpc.StatusFailedPreconditionWithDebugMsg("savings account is still active"),
		}, nil
	}

	cbts, getCbtErr := s.cbtDao.GetBySavingsAccountId(ctx, req.GetSavingsAccountId())
	if getCbtErr != nil && !errors.Is(getCbtErr, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error while getting saving account balance", zap.Error(getCbtErr))
		return &savingsPb.StoreClosedAccountBalTransferDataFromStatementResponse{
			Status: rpc.StatusInternalWithDebugMsg(getCbtErr.Error()),
		}, nil
	}

	// return if cbt with balance data already exists
	latestCbt := savings.GetLatestClosedAccountInfo(cbts)
	if latestCbt.HasClosedAccountBalanceData() {
		// todo: update this to debug log
		logger.Info(ctx, "cbt with balance data already exists for the account", zap.String(logger.ID, req.GetSavingsAccountId()))
		return &savingsPb.StoreClosedAccountBalTransferDataFromStatementResponse{
			Status: rpc.StatusOk(),
		}, nil
	}

	accountClosedAt := datetime.DateToTimeV2(operStatusResp.GetOperationalStatusInfo().GetAccountClosedAt(), datetime.IST)
	closedAccBalAmount, getClosedAccBalAmountErr := s.getClosedAccountPoolAccountTransferAmountFromStatement(ctx, req.GetSavingsAccountId(), accountClosedAt)
	if getClosedAccBalAmountErr != nil {
		logger.Error(ctx, "error while getting closed account balance amount", zap.Error(getClosedAccBalAmountErr))
		return &savingsPb.StoreClosedAccountBalTransferDataFromStatementResponse{
			Status: rpc.StatusInternalWithDebugMsg(getClosedAccBalAmountErr.Error()),
		}, nil
	}

	storeResp, storeErr := s.StoreClosedAccountBalTransferData(ctx, &savingsPb.StoreClosedAccountBalTransferDataRequest{
		Data: &savingsPb.ClosedAccountBalanceTransfer{
			SavingsAccountId:             req.GetSavingsAccountId(),
			BalanceCapturedFromStatement: closedAccBalAmount,
		},
	})
	return &savingsPb.StoreClosedAccountBalTransferDataFromStatementResponse{
		Status: storeResp.GetStatus(),
	}, storeErr
}

// method to fetch the pool account transfer amount of the closed account.
// before the account is closed, bank transfers the balance to the pool account.
func (s *SavingsService) getClosedAccountPoolAccountTransferAmountFromStatement(ctx context.Context, savingsAccountId string, accountClosedAt time.Time) (*gmoney.Money, error) {
	savingsAccountPiResp, getPiErr := s.accountPiClient.GetPiByAccountId(ctx, &account_pi.GetPiByAccountIdRequest{
		AccountId: savingsAccountId,
		PiTypes:   []paymentinstrument.PaymentInstrumentType{paymentinstrument.PaymentInstrumentType_BANK_ACCOUNT},
	})
	if rpcErr := epifigrpc.RPCError(savingsAccountPiResp, getPiErr); rpcErr != nil {
		return nil, fmt.Errorf("failed to get savings account PIs: %w", rpcErr)
	}

	if piCount := len(savingsAccountPiResp.GetPaymentInstruments()); piCount != 1 {
		return nil, fmt.Errorf("expected only one payment instrument for savings account id %s, got %d", savingsAccountId, piCount)
	}

	savingsAccountPi := savingsAccountPiResp.GetPaymentInstruments()[0].GetId()
	offset := int32(0)
	pageSize := int32(40)
	for {
		getTxnsByPiResp, getTxnsByPiErr := s.paymentClient.GetTxnsByPi(ctx, &orderPaymentPb.GetTxnsByPiRequest{
			PiFrom: savingsAccountPi,
			// adding buffer to account accountClosedAt since accountClosedAt has only
			FromTimestamp: timestamppb.New(accountClosedAt.Add(-time.Duration(s.dynConf.BufferDaysToFetchStatementForClosureBalance()) * 24 * time.Hour)),
			ToTimestamp:   timestamppb.New(accountClosedAt.Add(time.Duration(s.dynConf.BufferDaysToFetchStatementForClosureBalance()) * 24 * time.Hour)),
			PageSize:      pageSize,
			Offset:        offset,
			SortBy:        orderPaymentPb.TransactionFieldMask_CREATED_AT,
		})
		if rpcErr := epifigrpc.RPCError(getTxnsByPiResp, getTxnsByPiErr); rpcErr != nil && !getTxnsByPiResp.GetStatus().IsRecordNotFound() {
			return nil, fmt.Errorf("failed to get transactions by PI: %w", rpcErr)
		}

		if len(getTxnsByPiResp.GetTransactions()) == 0 {
			break
		}

		for _, txn := range getTxnsByPiResp.GetTransactions() {
			if txn.GetParticulars() != s.dynConf.DrOperativeParticularString() && !strings.HasSuffix(strings.ToLower(txn.GetParticulars()), reVkycClosedParticular) {
				continue
			}
			return txn.GetAmount(), nil
		}

		offset += pageSize
	}

	logger.Info(ctx, "no balance transfer transaction found in statement, returning 0 as the closed account balance",
		zap.String(logger.ID, savingsAccountId))
	return money.ZeroINR().GetPb(), nil
}

func (s *SavingsService) GetClosedAccountBalTransferData(ctx context.Context, req *savingsPb.GetClosedAccountBalTransferDataRequest) (*savingsPb.GetClosedAccountBalTransferDataResponse, error) {
	if req.GetSavingsAccountId() == "" {
		return &savingsPb.GetClosedAccountBalTransferDataResponse{
			Status: rpc.StatusInvalidArgument(),
		}, nil
	}

	cbts, err := s.cbtDao.GetBySavingsAccountId(ctx, req.GetSavingsAccountId())
	if errors.Is(err, epifierrors.ErrRecordNotFound) || (cbts != nil && len(cbts) == 0) {
		return &savingsPb.GetClosedAccountBalTransferDataResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil
	}
	if err != nil {
		logger.Error(ctx, "error while getting cbt", zap.Error(err))
		return &savingsPb.GetClosedAccountBalTransferDataResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	return &savingsPb.GetClosedAccountBalTransferDataResponse{
		Status:  rpc.StatusOk(),
		Entries: cbts,
	}, nil
}

func (s *SavingsService) UpdateClosedAccountBalTransferData(ctx context.Context, req *savingsPb.UpdateClosedAccountBalTransferDataRequest) (*savingsPb.UpdateClosedAccountBalTransferDataResponse, error) {
	if req.GetData().GetId() == "" || len(req.GetFieldMasks()) == 0 {
		return &savingsPb.UpdateClosedAccountBalTransferDataResponse{
			Status: rpc.StatusInvalidArgument(),
		}, nil
	}

	if err := s.cbtDao.UpdateByFieldMask(ctx, req.GetData(), req.GetFieldMasks()); err != nil {
		logger.Error(ctx, "error while updating by field mask", zap.Error(err), zap.String(logger.ID, req.GetData().GetId()))
		return &savingsPb.UpdateClosedAccountBalTransferDataResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	return &savingsPb.UpdateClosedAccountBalTransferDataResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (s *SavingsService) VerifyPanForAccountClosure(ctx context.Context, req *savingsPb.VerifyPanForAccountClosureRequest) (*savingsPb.VerifyPanForAccountClosureResponse, error) {
	if req.GetActorId() == "" || req.GetPan() == "" {
		return &savingsPb.VerifyPanForAccountClosureResponse{
			Status: rpc.NewStatus(uint32(savingsPb.VerifyPanForAccountClosureResponse_INVALID_ARGUMENT), "", ""),
		}, nil
	}

	userResp, userErr := s.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{
			ActorId: req.GetActorId(),
		},
	})
	if err := epifigrpc.RPCError(userResp, userErr); err != nil {
		logger.Error(ctx, "could not get user profile for actor", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &savingsPb.VerifyPanForAccountClosureResponse{
			Status: rpc.NewStatus(uint32(savingsPb.VerifyPanForAccountClosureResponse_INTERNAL), "", ""),
		}, nil
	}

	status, attemptsLeft := s.getPanVerificationStatus(ctx, req.GetActorId(), req.GetPan(), userResp.GetUser().GetProfile().GetPAN())
	return &savingsPb.VerifyPanForAccountClosureResponse{
		Status:       status,
		AttemptsLeft: int32(attemptsLeft),
	}, nil
}

func (s *SavingsService) getPanVerificationStatus(ctx context.Context, actorId string, enteredPan, userPan string) (*rpc.Status, int64) {
	attempts, err := s.getPanAttempts(ctx, actorId)
	if err != nil {
		return rpc.NewStatus(uint32(savingsPb.VerifyPanForAccountClosureResponse_INTERNAL), "", ""), 0
	}

	if attempts == maxPanVerificationAttempts {
		return getStatusByIncorrectAttempts(attempts), 0
	}
	if !strings.EqualFold(enteredPan, userPan) {
		incorrectAttempts := attempts + 1
		if err = s.incrementPanAttempt(ctx, incorrectAttempts, actorId); err != nil {
			return rpc.NewStatus(uint32(savingsPb.VerifyPanForAccountClosureResponse_INTERNAL), "", ""), 0
		}
		return getStatusByIncorrectAttempts(incorrectAttempts), maxPanVerificationAttempts - incorrectAttempts
	}
	return rpc.StatusOk(), 0
}

func (s *SavingsService) getPanAttempts(ctx context.Context, actorId string) (int64, error) {
	attemptsString, err := s.cacheStorage.Get(ctx, getPanVerificationKey(actorId))
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return 0, nil
		}
		logger.Error(ctx, "error in getting attempts", zap.Error(err))
		return 0, err
	}
	attempts, err := strconv.ParseInt(attemptsString, 10, 64)
	if err != nil {
		logger.Error(ctx, "error in parsing to int", zap.Error(err))
		return 0, err
	}
	return attempts, nil
}

func (s *SavingsService) incrementPanAttempt(ctx context.Context, attempts int64, actorId string) error {
	if err := s.cacheStorage.Set(ctx, getPanVerificationKey(actorId), fmt.Sprintf("%v", attempts), panAttemptTTL); err != nil {
		logger.Error(ctx, "error in getting attempts", zap.Error(err))
		return err
	}
	return nil
}

func getPanVerificationKey(actorId string) string {
	return cache.WebfePanVerificationAttemptsPrefix + actorId
}

func getStatusByIncorrectAttempts(incorrectAttempts int64) *rpc.Status {
	switch incorrectAttempts {
	case maxPanVerificationAttempts:
		return rpc.NewStatus(uint32(savingsPb.VerifyPanForAccountClosureResponse_PAN_INCORRECT_LOCKED), "", "")
	default:
		attemptsLeft := maxPanVerificationAttempts - incorrectAttempts
		if attemptsLeft == 1 {
			return rpc.NewStatus(uint32(savingsPb.VerifyPanForAccountClosureResponse_PAN_INCORRECT), "", "")
		}
		return rpc.NewStatus(uint32(savingsPb.VerifyPanForAccountClosureResponse_PAN_INCORRECT), "", "")
	}
}
