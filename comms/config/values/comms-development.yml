Application:
  Environment: "development"
  Name: "comms"
  IsSecureRedis: false

Server:
  Ports:
    GrpcPort: 8085
    GrpcSecurePort: 9506
    HttpPort: 9888
    HttpPProfPort: 9990
  EnablePoller: true

EpifiDb:
  AppName: "comms"
  StatementTimeout: 5s
  Host: "localhost"
  Port: 26257
  Username: "root"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "disable"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

CommsDb:
  AppName: "comms"
  StatementTimeout: 5s
  Name: "comms"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

# [DelayTime] sec delay before event is published to sms status queue
# delay is induced to avoid unnecessary status check calls to vendor
SmsStatusQueueDelay:
  DelayTime: 10s

# CutOffTime: Don't send SMS if SMS Creation Time is >= (CutOffTime) hours
# PublishDelayTime:
# Retry failed SMS every (PublishDelayTime) minutes, this delay is induced while publishing msg
# from wait queue to live queue
SmsRetry:
  CutOffTime: 12h
  PublishDelayTime: 2m

SmsSqsPublisher:
  QueueName: "comms-sms-queue"
SmsStatusUpdateSqsPublisher:
  QueueName: "comms-sms-status-update-queue"
SentSmsStatusSqsPublisher:
  QueueName: "comms-sms-wait-status-queue"
EmailSqsPublisher:
  QueueName: "comms-email-queue"
NotificationSqsPublisher:
  QueueName: "comms-notification-queue"
HighPriorityNotificationSqsPublisher:
  QueueName: "comms-high-priority-notification-queue"
NormalPriorityNotificationSqsPublisher:
  QueueName: "comms-normal-priority-notification-queue"
LowPriorityNotificationSqsPublisher:
  QueueName: "comms-low-priority-notification-queue"
WhatsappSqsPublisher:
  QueueName: "comms-whatsapp-queue"
NotificationUpdateSqsPublisher:
  QueueName: "comms-notification-update-queue"
WhatsappBotSqsPublisher:
  QueueName: "whatsapp-bot-queue"
EmailExtendedSqsPublisher:
  QueueName: "comms-email-extended-queue"
  BucketName: "epifi-dev-extended-sqs"

NotificationUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "comms-notification-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 50
        Period: 1m
    Namespace: "comms"

SmsSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "comms-sms-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~10 min post that regular interval is followed hourly for 2 hours
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 10
          MaxAttempts: 8
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 1
          MaxAttempts: 2
          TimeUnit: "Hour"
      MaxAttempts: 10
      CutOff: 8
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 50
        Period: 1m
    Namespace: "comms"

SmsStatusUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "comms-sms-status-update-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~10 min post that regular interval is followed every hour
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 60
          MaxAttempts: 5
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 1
          MaxAttempts: 25
          TimeUnit: "Hour"
      MaxAttempts: 30
      CutOff: 5
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 50
        Period: 1m
    Namespace: "comms"

SentSmsStatusSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "comms-sms-wait-status-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~10 min post that regular interval is followed every hour
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 60
          MaxAttempts: 5
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 1
          MaxAttempts: 25
          TimeUnit: "Hour"
      MaxAttempts: 30
      CutOff: 5
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 50
        Period: 1m
    Namespace: "comms"

EmailSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "comms-email-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 4
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 50
        Period: 1m
    Namespace: "comms"

NotificationSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "comms-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 4
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 50
        Period: 1m
    Namespace: "comms"

HighPriorityNotificationSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "comms-high-priority-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 4
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 50
        Period: 1m
    Namespace: "comms"

NormalPriorityNotificationSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "comms-normal-priority-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 4
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 50
        Period: 1m
    Namespace: "comms"

LowPriorityNotificationSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "comms-low-priority-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 30
      MaxAttempts: 10
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 50
        Period: 1m
    Namespace: "comms"

WhatsappSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "comms-whatsapp-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 4
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 50
        Period: 1m
    Namespace: "comms"

WhatsappUserRepliesSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "vn-acl-whatsapp-reply-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 4
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 50
        Period: 1m
    Namespace: "comms"

AclSmsCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "vn-acl-sms-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 4
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 50
        Period: 1m
    Namespace: "comms"

KaleyraSmsCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "vn-kaleyra-sms-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 4
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 50
        Period: 1m
    Namespace: "comms"

AclWhatsappCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "vn-acl-whatsapp-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 4
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 50
        Period: 1m
    Namespace: "comms"

GupshupWhatsappCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "comms-gupshup-whatsapp-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 4
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 50
        Period: 1m
    Namespace: "comms"

GupshupRcsCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "comms-gupshup-rcs-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 4
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 50
        Period: 1m
    Namespace: "comms"

NetCoreSmsCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "comms-netcore-sms-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 4
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 5
        Period: 1s
    Namespace: "comms"


AirtelSmsCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "comms-airtel-sms-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 4
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 40
        Period: 1s
    Namespace: "comms"

AirtelWhatsappCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "comms-airtel-whatsapp-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 4
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 40
        Period: 1s
    Namespace: "comms"

EmailCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "vn-email-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 4
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 50
        Period: 1m
    Namespace: "comms"

EmailExtendedSqsSubscriber:
  BucketName: "epifi-dev-extended-sqs"
  SqsSubscriber:
    StartOnServerStart: true
    NumWorkers: 1
    PollingDuration: 20
    MaxMessages: 10
    QueueName: "comms-email-extended-queue"
    RetryStrategy:
      ExponentialBackOff:
        BaseInterval: 1
        MaxAttempts: 4
        TimeUnit: "Second"

PinpointEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "dev-comms-pinpoint-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 1
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 50
        Period: 1m
    Namespace: "comms"


Aws:
  Region: "ap-south-1"

SmsProvider: TWILIO

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Flags:
  TrimDebugMessageFromStatus: false

Secrets:
  Ids:
    RudderWriteKey: "1eGkhVch5jk2oJBF9lrTEN4I3zB"
    GoogleCloudProfilingServiceAccountKey: "development"
    CommsDbUsernamePassword: "{\"username\": \"root\", \"password\": \"\"}"

SmsTemplates:
  ONBOARDING_OTP:
    VERSION_V1:
      Vendor: "ACL"
      Account: "EPIFI"
      VendorRanking:
        - VendorInfo:
            Name: "KALEYRA"
            Rank: 1
            Account: "EPIFI"
        - VendorInfo:
            Name: "ACL"
            Rank: 2
            Account: "EPIFI"
      Strategy:
        Name: "prev-attempt-based"
        PrevAttemptBased:
          TTL: 5
          Limit: 2
      EnableDynamicVendorSwitching: true
  WAITLIST_OTP:
    VERSION_V1:
      Vendor: "ACL"
      Account: "EPIFI"
      VendorRanking:
        - VendorInfo:
            Name: "KALEYRA"
            Rank: 1
            Account: "EPIFI"
        - VendorInfo:
            Name: "ACL"
            Rank: 2
            Account: "EPIFI"
      Strategy:
        Name: "prev-attempt-based"
        PrevAttemptBased:
          TTL: 5
          Limit: 2
      EnableDynamicVendorSwitching: true
  DEBIT_CARD_BLOCK:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL"
  DEBIT_CARD_NEW_CARD_ISSUANCE_SOFT_PIN:
    VERSION_V1:
      Vendor: "ACL"
      Account: "FEDERAL"
  DEBIT_CARD_NEW_CARD_ISSUANCE_SOFT_PIN_RE:
    VERSION_V1:
      Vendor: "ACL"
      Account: "FEDERAL"
  DEBIT_CARD_ON:
    VERSION_V1:
      Vendor: "ACL"
      Account: "FEDERAL"
  DEBIT_CARD_OFF:
    VERSION_V1:
      Vendor: "ACL"
      Account: "FEDERAL"
  DEBIT_CARD_ON_OFF_FAILURE:
    VERSION_V1:
      Vendor: "ACL"
      Account: "FEDERAL"
  DEBIT_CARD_INTERNATIONAL_ON:
    VERSION_V1:
      Vendor: "ACL"
      Account: "FEDERAL"
  DEBIT_CARD_INTERNATIONAL_OFF:
    VERSION_V1:
      Vendor: "ACL"
      Account: "FEDERAL"
  DEBIT_CARD_INTERNATIONAL_ON_OFF_FAILURE:
    VERSION_V1:
      Vendor: "ACL"
      Account: "FEDERAL"
  DEBIT_CARD_ECOM_ON:
    VERSION_V1:
      Vendor: "ACL"
      Account: "FEDERAL"
  DEBIT_CARD_ECOM_OFF:
    VERSION_V1:
      Vendor: "ACL"
      Account: "FEDERAL"
  DEBIT_CARD_ACTIVATE:
    VERSION_V1:
      Vendor: "ACL"
      Account: "FEDERAL"
  DEBIT_CARD_BLOCK_FAILURE:
    VERSION_V1:
      Vendor: "ACL"
      Account: "FEDERAL"
  DEBIT_CARD_DISPATCH:
    VERSION_V1:
      Vendor: "ACL"
      Account: "FEDERAL"
  DEBIT_CARD_INCORRECT_PIN_RETRIES:
    VERSION_V1:
      Vendor: "ACL"
      Account: "FEDERAL"
  DEBIT_CARD_FREEZE:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL"
  DEBIT_CARD_UNFREEZE:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL"
  DEBIT_CARD_CHANGE_ATM_PIN:
    VERSION_V1:
      Vendor: "ACL"
      Account: "FEDERAL"
  UPI_REGISTRATION:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL"
  CASH_WITHDRAWAL_ATM:
    VERSION_V1:
      Vendor: "ACL"
      Account: "FEDERAL"
  NEFT_DEBIT:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL"
  NEFT_CREDIT:
    VERSION_V1:
      Vendor: "ACL"
      Account: "FEDERAL"
  NEFT_CREDIT_OTHER_BANK:
    VERSION_V1:
  NEFT_CREDIT_CONFIRMATION:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL"
  POS_DEBIT:
    VERSION_V1:
      Vendor: "ACL"
      Account: "FEDERAL"
  POS_REVERSAL_CREDIT:
    VERSION_V1:
      Vendor: "ACL"
      Account: "FEDERAL"
  UNSUCCESSFUL_ATM_REVERSAL_CREDIT:
    VERSION_V1:
      Vendor: "ACL"
      Account: "FEDERAL"
  RTGS_CREDIT_CONFIRMATION:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL"
  RTGS_DEBIT:
    VERSION_V1:
      Vendor: "ACL"
      Account: "FEDERAL"
  CREDIT_CASH_DEPOSIT_MACHINE:
    VERSION_V1:
      Vendor: "ACL"
      Account: "FEDERAL"
  UPI_CREDIT:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL"
    VERSION_V2:
      Vendor: "AIRTEL"
      Account: "FEDERAL"
  UPI_DEBIT:
    VERSION_V1:
      Vendor: "ACL"
      Account: "FEDERAL"
    VERSION_V2:
      Vendor: "ACL"
      Account: "FEDERAL"
  COLLECT_REQUEST:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL"
  FAILED_TRANSACTION:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL"
  GENERIC_PI_CREDIT:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL"
    VERSION_V2:
      Vendor: "AIRTEL"
      Account: "FEDERAL"
  GENERIC_PI_DEBIT:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL"
    VERSION_V2:
      Vendor: "AIRTEL"
      Account: "FEDERAL"
  GENERIC_PI_DEBIT_UNCLEAR_BENEFICARY_DETAILS:
    VERSION_V1:
      Vendor: "ACL"
      Account: "FEDERAL"
  FD_OPEN:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL"
  SD_OPEN:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL"
    VERSION_V2:
      Vendor: "AIRTEL"
      Account: "FEDERAL"
  FD_SD_X_DAYS_BEFORE_MATURITY:
    VERSION_V1:
      Vendor: "ACL"
      Account: "FEDERAL"
  SD_X_DAYS_BEFORE_MATURITY:
    VERSION_V1:
      Vendor: "ACL"
      Account: "FEDERAL"
  ADD_FUNDS_SD:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL"
  FD_SD_CLOSURE:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL"
  INTEREST_PAID_IN_SB:
    VERSION_V1:
      Vendor: "ACL"
      Account: "FEDERAL"
  MOBILE_NUMBER_ADD:
    VERSION_V1:
      Vendor: "ACL"
      Account: "FEDERAL"
  MOBILE_NUMBER_MODIFY:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL"
  CARD_CONTROL_ON:
    VERSION_V1:
      Vendor: "ACL"
      Account: "FEDERAL"
  CARD_CONTROL_OFF:
    VERSION_V1:
      Vendor: "ACL"
      Account: "FEDERAL"
  VKYC_APPROVED:
    VERSION_V1:
      Vendor: "ACL"
      Account: "EPIFI"
  WAITLIST_ACCESS:
    VERSION_V1:
      Vendor: "ACL"
      Account: "EPIFI"
    VERSION_V2:
      Vendor: "ACL"
      Account: "EPIFI"
  VKYC_WITH_LIMIT:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  FINITE_CODE:
    VERSION_V1:
      Vendor: "ACL"
      Account: "EPIFI"
  CBO_FINITE_CODE:
    VERSION_V1:
      Vendor: "ACL"
      Account: "EPIFI"
  VKYC_SIX_WEEKS_BEFORE:
    VERSION_V1:
      Vendor: "ACL"
      Account: "EPIFI"
  VKYC_FOUR_WEEKS_BEFORE:
    VERSION_V1:
      Vendor: "ACL"
      Account: "EPIFI"
  VKYC_TEN_DAYS_BEFORE:
    VERSION_V1:
      Vendor: "ACL"
      Account: "EPIFI"
  PAN_REMINDER:
    VERSION_V1:
      Vendor: "ACL"
      Account: "EPIFI"
  EKYC_REMINDER:
    VERSION_V1:
      Vendor: "ACL"
      Account: "EPIFI"
  NAME_MISMATCH_UPDATE:
    VERSION_V1:
      Vendor: "ACL"
      Account: "EPIFI"
  LIVENESS_REMINDER:
    VERSION_V1:
      Vendor: "ACL"
      Account: "EPIFI"
  CBO_FINITE_CODE_REMINDER:
    VERSION_V1:
      Vendor: "ACL"
      Account: "EPIFI"
  NON_CBO_REMINDER_SMS:
    VERSION_V1:
      Vendor: "ACL"
      Account: "EPIFI"
  KYC_VALIDATION_FAILURE:
    VERSION_V1:
      Vendor: "ACL"
      Account: "EPIFI"
  DEBIT_CARD_DELIVERY:
    VERSION_V1:
      Vendor: "ACL"
      Account: "EPIFI"
  COMMUNITY_LOGIN_OTP:
    VERSION_V1:
      Vendor: "ACL"
      Account: "EPIFI"
  CARD_OUT_FOR_DELIVERY:
    VERSION_V1:
      Vendor: "ACL"
      Account: "EPIFI"
  CARD_DELIVERY_DELAY:
    VERSION_V1:
      Vendor: "ACL"
      Account: "EPIFI"
  CARD_DISPATCH_TIMELINE_INFO:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  ONBOARDING_KYC_COMPLETE:
    VERSION_V1:
      Vendor: "ACL"
      Account: "EPIFI"
  FIT_SMART_DEPOSIT_ADD_FUNDS:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "FEDERAL"
  CASH_WITHDRAWAL_ATM_FALLBACK:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL"
  POS_DEBIT_FALLBACK:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL"
  NEFT_DEBIT_FALLBACK:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL"
  NEFT_CREDIT_FALLBACK:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL"
  RTGS_DEBIT_FALLBACK:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL"
  INTEREST_PAID_IN_SB_FALLBACK:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL"
  MANDATE_RECEIVED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  MANDATE_APPROVED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  MANDATE_DECLINED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  MANDATE_CREATED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  MANDATE_EXECUTION_SUCCESSFUL:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  MANDATE_EXECUTION_FAILED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  MANDATE_REVOKED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  MANDATE_MODIFIED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  MANUAL_LIVENESS_PASSED:
    VERSION_V1:
      Vendor: "ACL"
      Account: "EPIFI"
  TRANSACTION_REVERSED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  SI_CREATED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  SI_DECLINED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  SI_EXECUTION_SUCCESSFUL:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  SI_EXECUTION_FAILED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  MANDATE_AUTHORISED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  MANDATE_ACCEPTANCE:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  MANDATE_PAUSED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  MANDATE_UNPAUSED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  MUTUAL_FUND_WITHDRAWAL_OTP:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  OB_VKYC_REMINDER_ONE:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  OB_VKYC_REMINDER_TWO:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  OB_SCREENER_VERIFICATION_REMINDER_ONE:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  OB_SCREENER_VERIFICATION_REMINDER_TWO:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  TOD_CHARGES_DEBIT:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  ECS_RETURN_CHARGES:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL"
  ATM_DECLINE_FEES:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL"
  DUPLICATE_CARD_FEE:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL"
  ATM_WITHDRAWAL_COMPLAINT_PENALTY:
    VERSION_V1:
      Vendor: "ACL"
      Account: "FEDERAL"
  INTERNATIONAL_ATM_CHARGES:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL"
  OTHER_BANK_ATM_USAGE_CHARGES:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL"
  VKYC_ACCOUNT_CLOSURE_MORE_THEN_ZERO_BALANCE_FORTY_FIVE_DAYS:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  VKYC_ACCOUNT_CLOSURE_MORE_THEN_ZERO_BALANCE_THIRTY_DAYS:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  VKYC_ACCOUNT_CLOSURE_MORE_THEN_ZERO_BALANCE_ELEVEN_DAYS:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  VKYC_ACCOUNT_CLOSURE_MORE_THEN_ZERO_BALANCE_FIVE_DAYS:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  VKYC_ACCOUNT_CLOSURE_MORE_THEN_ZERO_BALANCE_THREE_DAYS:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  VKYC_ACCOUNT_CLOSURE_MORE_THEN_ZERO_BALANCE_ONE_DAY:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  ONBOARDING_ACCOUNT_BLOCK_DAY_ZERO:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  ONBOARDING_ACCOUNT_UNBLOCK:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  WEALTH_ACCOUNT_NOMINEE_DECLARATION_OTP:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  MUTUAL_FUND_ONE_TIME_BUY_OTP:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  MUTUAL_FUND_REGISTER_SIP_OTP:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  ONBOARDING_DOB_AND_PAN_DROP_OFF:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  CREDIT_REPORT_DOWNLOAD_OTP_VERIFICATION:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  CREDIT_CARD_CROSS_BORDER_TRANSACTION_SUCCESS:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_ATM_TRANSACTION_FAILURE:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_ATM_TRANSACTION_SUCCESS:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_FAILED_TRANSACTION_REVERSAL_SUCCESS:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
    VERSION_V2:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_TRANSACTION_DECLINED:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
    VERSION_V2:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_TRANSACTION_SUCCESS:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
    VERSION_V2:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_INTERNATIONAL_TRANSACTIONS_DISABLED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_INTERNATIONAL_TRANSACTIONS_ENABLED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_POS_TRANSACTIONS_DISABLED:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_POS_TRANSACTIONS_ENABLED:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_CONTACTLESS_TRANSACTIONS_DISABLED:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_CONTACTLESS_TRANSACTIONS_ENABLED:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_ONLINE_TRANSACTIONS_DISABLED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_ONLINE_TRANSACTIONS_ENABLED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_EMI_CREATED:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_EMI_CLOSED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_EMI_PRE_CLOSED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_EMI_CANCELLED:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_REVISED_LIMIT_SMS:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
  CATEGORY_SPENDS_EXCEEDED_REMINDER_SMS:
    VERSION_V1:
      Vendor: "ACL"
    VERSION_V2:
      Vendor: "ACL"
  AMOUNT_SPENDS_EXCEEDED_REMINDER_SMS:
    VERSION_V1:
      Vendor: "ACL"
    VERSION_V2:
      Vendor: "ACL"
  CREDIT_CARD_BILL_PAYMENT_DUE_DATE_REMINDER_SMS:
    VERSION_V1:
      Vendor: "ACL"
    VERSION_V2:
      Vendor: "ACL"
  CREDIT_CARD_PIN_TRIES_EXCEEDED_FOR_TRANSACTIONS:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_PAYMENT_NOT_DONE_REMINDER_WITH_INTEREST_CHARGE:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_PAYMENT_NOT_DONE_REMINDER:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_BILL_SUCCESSFUL_REPAYMENT:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_BILL_REPAYMENT_DUE:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_STATEMENT_GENERATION:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_COMMUNICATE_TRANSACTION_CHARGES:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_REPLACEMENT:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_LIMIT_CHANGE_FAILURE:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_CARD_USAGE_CHANGE_FAILURE:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_CONTACTLESS_PURCHASE_LIMIT_CHANGED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_ONLINE_PURCHASE_LIMIT_CHANGED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_POS_PURCHASE_LIMIT_CHANGED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_UNFREEZING_FAILURE:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_FREEZING_FAILURE:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_UNFREEZING_SUCCESS:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_FREEZING_SUCCESS:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_PIN_CHANGE_SUCCESS:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_ACTIVATION_FAILURE:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_PHYSICAL_CARD_ACTIVATION_SUCCESS:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_DIGITAL_CARD_ACTIVATION_SUCCESS:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_ACTIVATION_INFORMATION:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_SHIPMENT_DELAY:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_DISPTACH_DELAY:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_DISPTACHED_WITH_TRACKING_NUMBER:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_ISSUED_WITH_CREDIT_LIMIT:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_ISSUED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_INCOMPLETE_APPLICATION_PROCESS:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_COMPLETE_VIDEO_KYC_FOR_CREDIT_CARD:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_OTP_FOR_CHANGING_CARD_PIN:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_REWARD_POINTS_CREDITED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_LIMIT_REACHING_THRESHOLD:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_TRANSACTION_DECLINED_WITH_REASON:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
    VERSION_V2:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
    VERSION_V3:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_JOINING_FEES:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_UNPAID_DUE_FEES:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_GENERIC_CREDIT:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
  UPI_PIN_SET_RESET:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  PL_LOAN_AGREEMENT_OTP:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  LOAN_CONTACTABILITY_ALTERNATE_PHONE_NUMBER_SMS:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  CIBIL_REPORT_SMS:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  CREDIT_CARD_NOT_ACTIVATED_SMS:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_CLOSURE_CONFIRMATION_SMS:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL_CREDIT_CARD"
  SECURED_CREDIT_CARD_SUCCESSFUL_FD_CREATION:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "FEDERAL_CREDIT_CARD"
  SECURED_CREDIT_CARD_FD_LIEN_MARKING_INTIMATION:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "FEDERAL_CREDIT_CARD"
  SECURED_CREDIT_CARD_FD_CLOSURE_CONFIRMATION:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "FEDERAL_CREDIT_CARD"
  SECURED_CREDIT_CARD_FD_CLOSURE_WARNING:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "FEDERAL_CREDIT_CARD"
  CREDIT_CARD_WEB_ELIGIBILITY_CHECK_LOGIN_OTP:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  CREDIT_CARD_WEB_ELIGIBILITY_APPROVED_SMS:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  CREDIT_CARD_WEB_ELIGIBILITY_REJECTED_SMS:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_FOREX_MARKUP_REFUND_RECEIVED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_UNABLE_TO_PROCESS_TRANSACTION:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_INCORRECT_PIN:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
    VERSION_V2:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_UNABLE_TO_AUTHORIZE_TRANSACTION:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
    VERSION_V2:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_CARD_EXPIRED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_ECOM_TRANSACTIONS_NOT_ENABLED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
    VERSION_V2:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_DAILY_TRANSACTIONS_AMT_LIMIT_REACHED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_POS_NOT_SUPPORTED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_PIN_TRIES_EXCEEDED_FOR_TRANSACTIONS:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
    VERSION_V2:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_DUPLICATE_TRANSACTION:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
    VERSION_V2:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_TRANSACTION_DECLINED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_TRANSACTION_TYPE_NOT_SUPPORTED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_INVALID_TRANSACTION:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
    VERSION_V2:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_INTERNATIONAL_TRANSACTIONS_NOT_ENABLED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
    VERSION_V2:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_CONTACTLESS_CARD_USAGE_NOT_ENABLED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
    VERSION_V2:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_INSUFFICIENT_FUNDS_FOR_TRANSACTION:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
    VERSION_V2:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_DAILY_WITHDRAWAL_LIMIT_REACHED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
    VERSION_V2:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_LOW_FUNDS_FOR_TRANSACTION:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
    VERSION_V2:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_INVALID_EXPIRY_DATE:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
    VERSION_V2:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_NFC_NOT_ENABLED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_PRM_DECLINED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
    VERSION_V2:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_CVV_ERROR:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
    VERSION_V2:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_DAILY_CONTACTLESS_PAYMENTS_LIMIT_EXCEEDED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
    VERSION_V2:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_CARD_OFF_FOR_TRANSACTIONS:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
    VERSION_V2:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_HOST_DOWN:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
    VERSION_V2:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_DOMESTIC_TRANSACTIONS_NOT_ENABLED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
    VERSION_V2:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  CALL_RECORDING_POST_RISK_USE_CASE:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  LAMF_LIEN_MARK_SUCCESS:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  LAMF_LOAN_DISBURSED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  LAMF_ALL_EMI_PAID_LOAN_CLOSURE_INITIATED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  LAMF_REPAYMENT_AUTO_RECOVERED_BY_BAJAJ:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  LAMF_REPAYMENT_AUTO_RECOVERED_BY_FI:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  LAMF_REPAYMENT_UPCOMING_EMI:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  LAMF_REPAYMENT_UPCOMING_EMI_LOW_BALANCE:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  LAMF_REPAYMENT_EMANDATE_BOUNCE:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  LAMF_REPAYMENT_PREPAYMENT_SUCCESS:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  CC_FI_LITE_PAN_DOB_DROP_OFF_2_HRS:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  CC_FI_LITE_PAN_DOB_DROP_OFF_120_HRS:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  SmsType_CC_FI_LITE_EKYC_DROP_OFF:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  SmsType_CC_VKYC_DROP_OFF:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  CREDIT_CARD_ELIGIBLE_WEB_FLOW_START_APPLICATION_SMS:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  CREDIT_CARD_ELIGIBLE_WEB_FLOW_COMPLETE_APPLICATION_SMS:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  FI_STORE_ORDER_DELIVERY_STATUS_UPDATE_SMS:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  RISK_OUTCALL_FORM_LOGIN_OTP:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  STOCKGUARDIAN_LOAN_APPLICATION_ESIGN_SMS:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"

  SALARY_PROGRAM_B2B_USER_WHITELISTED_SMS:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  NON_RESIDENT_ONBOARDING_OTP:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI_NR"
  DEBIT_CARD_INTERNATIONAL_ATM_WITHDRAWAL_LIMIT_SMS:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  CX_USER_CALL_DROPPED_SMS:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_CAF_NOT_FOUND:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_ATM_USAGE_NOT_ENABLED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_HOST_NOT_AVAILABLE:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_LOST_OR_STOLEN_CARD:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_AMOUNT_OVER_DAILY_MAX:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_INELIGIBLE_ACCOUNT:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_ELA:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_POS_USAGE_NOT_ENABLED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_UNAUTHORIZED_USAGE:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_SI_HUB_DECLINE:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_AMOUNT_OVER_WITHDRAWAL_LIMIT_POS:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_CAF_STATUS_DECLINE:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_FALLBACK_DECLINE:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_MESSAGE_EDIT_ERROR:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_DEST_NOT_AVAILABLE:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_ATC_CHECK_FAILURE:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_TOKEN_IN_APP_FLAG_OFF:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_NO_IDF_ERROR:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_SYSTEM_ERROR:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_ARQC_FAILURE:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_APPROVED_NO_BALANCES:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_INVALID_TXN_DATE:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_TO_BE_CAPTURED_IN_CAF:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_BAD_CARD_STATUS:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_RESERVED_B24_CODE:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_HSM_PARAM_ERROR:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_MAX_CREDIT_PER_REFUND:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_USAGE_LIMIT_EXCEEDED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_TOKEN_NFC_FLAG_OFF:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  RISKOPS_CF_SMS:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  DEBIT_CARD_FOREX_MARKUP_TXN:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  RISK_CREDIT_FREEZE_APPLIED_SMS:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  CHEQUE_CREDIT_PROCESSING_FINT:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL"
  CHEQUE_CREDIT_PROCESSING_FAILED_FINT:
    VERSION_V1:
      Vendor: "AIRTEL"
      Account: "FEDERAL"
  CREDIT_CARD_BLOCK_KYC_EXPIRY:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  CREDIT_CARD_UNBLOCK_KYC_COMPLETED:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "EPIFI"
  CREDIT_CARD_CX_SUPPORT_DETAILS_UPDATE_SMS:
    VERSION_V1:
      Vendor: "KALEYRA"
      Account: "FEDERAL"

EmailTemplates:
  LOAN_CONFIRMATION_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  LOANS_PAYMENT_FILE:
    VERSION_V1:
      Vendor: "AWS_SES"
  LAMF_LOAN_AMOUNT_DISBURSED:
    VERSION_V1:
      Vendor: "AWS_SES"
  LAMF_LOAN_CLOSED:
    VERSION_V1:
      Vendor: "AWS_SES"
  LAMF_LOAN_EMI_DUE_LOW_BALANCE:
    VERSION_V1:
      Vendor: "AWS_SES"
  LAMF_LOAN_EMI_OVERDUE:
    VERSION_V1:
      Vendor: "AWS_SES"
  JUMP_INVESTMENT_DELAYED:
    VERSION_V1:
      Vendor: "AWS_SES"
  JUMP_INVESTMENT_SUCCESS:
    VERSION_V1:
      Vendor: "AWS_SES"
  JUMP_INVESTMENT_FAILED:
    VERSION_V1:
      Vendor: "AWS_SES"
  JUMP_WITHDRAWAL_SUCCESS:
    VERSION_V1:
      Vendor: "AWS_SES"
  JUMP_WITHDRAWAL_DELAYED:
    VERSION_V1:
      Vendor: "AWS_SES"
  JUMP_WITHDRAWAL_FAILED_ACCOUNT_FROZEN:
    VERSION_V1:
      Vendor: "AWS_SES"
  PRE_CUSTOMER_CREATION_MIN_TO_FULL_KYC_CONVERSION:
    VERSION_V1:
      Vendor: "AWS_SES"
  JUMP_INVESTMENT_RENEWED:
    VERSION_V1:
      Vendor: "AWS_SES"
  JUMP_INVESTMENT_PAID_OUT:
    VERSION_V1:
      Vendor: "AWS_SES"
  JUMP_INVESTMENT_MATURING_SOON:
    VERSION_V1:
      Vendor: "AWS_SES"
  JUMP_ELIGIBILITY_CHECK_SUCCESS:
    VERSION_V1:
      Vendor: "AWS_SES"
  SHERLOCK_VERIFICATION_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  WAITLIST_USER_REJECTED:
    VERSION_V1:
      Vendor: "SEND_GRID"
  WAITLIST_USER_ACCEPTED:
    VERSION_V1:
      Vendor: "SEND_GRID"
  WAITLIST_USER_FINITE_CODE:
    VERSION_V1:
      Vendor: "SEND_GRID"
    VERSION_V2:
      Vendor: "SEND_GRID"
    VERSION_V3:
      Vendor: "SEND_GRID"
  WAITLIST_USER_CBO_REJECTED:
    VERSION_V1:
      Vendor: "SEND_GRID"
  WAITLIST_USER_CBO_ACCEPTED:
    VERSION_V1:
      Vendor: "SEND_GRID"
  WAITLIST_USER_APP_ACCESS:
    VERSION_V1:
      Vendor: "SEND_GRID"
  WAITLIST_USER_GMAIL_INPUT:
    VERSION_V1:
      Vendor: "SEND_GRID"
  ACCOUNT_STATEMENT_EMAIL:
    VERSION_V1:
      Vendor: "SEND_GRID"
    VERSION_V2:
      Vendor: "SEND_GRID"
  DEPOSIT_STATEMENT_EMAIL:
    VERSION_V1:
      Vendor: "SEND_GRID"
  VKYC_SCHEDULED_CALL:
    VERSION_V1:
      Vendor: "SEND_GRID"
  VKYC_SCHEDULED_CALL_REMINDER:
    VERSION_V1:
      Vendor: "SEND_GRID"
  WAITLIST_CBO_USER_FINITE_CODE:
    VERSION_V1:
      Vendor: "SEND_GRID"
    VERSION_V2:
      Vendor: "SEND_GRID"
    VERSION_V3:
      Vendor: "SEND_GRID"
  MIN_KYC_USER_WELCOME_EMAIL:
    VERSION_V1:
      Vendor: "SEND_GRID"
    VERSION_V2:
      Vendor: "SEND_GRID"
    VERSION_V3:
      Vendor: "SEND_GRID"
  FULL_KYC_USER_WELCOME_EMAIL:
    VERSION_V1:
      Vendor: "SEND_GRID"
    VERSION_V2:
      Vendor: "SEND_GRID"
  WAITLIST_CBO_SHORTLIST:
    VERSION_V1:
      Vendor: "SEND_GRID"
    VERSION_V2:
      Vendor: "SEND_GRID"
  WAITLIST_EXCLUSIVE_ACCESS_FINITE_CODE:
    VERSION_V1:
      Vendor: "SEND_GRID"
  WAITLIST_CBO_IOS_SHORTLIST:
    VERSION_V1:
      Vendor: "SEND_GRID"
  UN_NAME_CHECK_EMAIL:
    VERSION_v1:
      Vendor: "SEND_GRID"
  FINITE_CODE_REMINDER_EMAIL:
    VERSION_V1:
      Vendor: "SEND_GRID"
    VERSION_V2:
      Vendor: "SEND_GRID"
  DIRECT_ACCESS_FINITE_CODE:
    VERSION_V1:
      Vendor: "SEND_GRID"
  CBO_UNKNOWN_DEVICE_FINITE_CODE:
    VERSION_V1:
      Vendor: "SEND_GRID"
  ADD_FUND_SECOND_LEG_ALERT:
    VERSION_v1:
      Vendor: "SEND_GRID"
  WAITLIST_IOS_WELCOME_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  CBO_FEEDBACK_WELCOME_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  WORK_EMAIL_VERIFICATION:
    VERSION_V1:
      Vendor: "AWS_SES"
    VERSION_V2:
      Vendor: "AWS_SES"
  WAITLIST_IOS_APP_ACCESS:
    VERSION_V1:
      Vendor: "AWS_SES"
  WAITLIST_CBO_IOS_APP_ACCESS:
    VERSION_V1:
      Vendor: "AWS_SES"
  CX_AUTO_RESOLUTION_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  DEPOSIT_MATURING_MIN_KYC_T_MINUS_X:
    VERSION_V1:
      Vendor: "AWS_SES"
  DEPOSIT_MATURING_MIN_KYC_T_PLUS_X:
    VERSION_V1:
      Vendor: "AWS_SES"
  INTERNAL_FINITE_CODE_DUMP_SHARE:
    VERSION_V1:
      Vendor: "AWS_SES"
  CX_BULK_USER_DETAILS_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  CX_BULK_ACCOUNT_VALIDATIONS_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  POST_ACCOUNT_CLOSURE_FUND_TRANSFER_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  MIN_TO_FULL_KYC_CONVERSION_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  USER_STUCK_IN_VKYC_REVIEW_STATE_FEDERAL_EMAIL:
    VERSION_V1:
      VENDOR: "AWS_SES"
  MIN_KYC_ACCOUNT_CLOSURE_USER_VERIFIED:
    VERSION_V1:
      VENDOR: "AWS_SES"
  WORKFLOW_EXECUTION_REPORT_EMAIL:
    VERSION_V1:
      VENDOR: "AWS_SES"
  ISSUE_RESOLUTION_USER_FEEDBACK_EMAIL:
    VERSION_V1:
      VENDOR: "AWS_SES"
  P2P_RECON_FILE_EMAIL:
    VERSION_V1:
      VENDOR: "AWS_SES"
  SALARY_PROGRAM_REGISTRATION_COMPLETED_EMAIL:
    VERSION_V1:
      VENDOR: "AWS_SES"
  DAILY_TRADE_CONFIRMATION:
    VERSION_V1:
      VENDOR: "AWS_SES"
  USSTOCKS_MONTHLY_ACCOUNT_STATEMENT_REPORT:
    VERSION_V1:
      VENDOR: "AWS_SES"
  MF_TAX_STATEMENT_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  CX_WATSON_INCIDENT_CREATION_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
    VERSION_V2:
      Vendor: "AWS_SES"
  CX_WATSON_INCIDENT_CREATION_EMAIL_SHORT:
    VERSION_V1:
      Vendor: "AWS_SES"
  CX_WATSON_MIN_KYC_EXPIRY_BALANCE_REFUND_INCIDENT_CREATION_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  CREDIT_CARD_CONTROLS_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
    VERSION_V2:
      Vendor: "AWS_SES"
  CREDIT_CARD_TRANSACTION_STATUS_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
    VERSION_V2:
      Vendor: "AWS_SES"
    VERSION_V3:
      Vendor: "AWS_SES"
  MF_ONE_TIME_BUY_OTP:
    VERSION_V1:
      Vendor: "AWS_SES"
  MF_WITHDRAWAL_OTP:
    VERSION_V1:
      Vendor: "AWS_SES"
  MF_REGISTER_SIP_OTP:
    VERSION_V1:
      Vendor: "AWS_SES"
  CREDIT_CARD_STATEMENT_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  CATEGORY_SPENDS_EXCEEDED_REMINDER_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  CREDIT_CARD_BILL_PAYMENT_DUE_DATE_REMINDER_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  MF_CAPITAL_GAINS_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  CREDIT_TXN_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  RISK_USER_TM_AUTO_CF:
    VERSION_V1:
      Vendor: "AWS_SES"
  CREDIT_CARD_NOT_ACTIVATED_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  CREDIT_CARD_CLOSURE_CONFIRMATION_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  KYC_AGENT_WELCOME_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  GENIE_LOGIN_OTP:
    VERSION_V1:
      Vendor: "AWS_SES"
  DEBIT_CARD_FOREX_REFUND_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  SALARY_B2B_LEAD_WORK_EMAIL_VERIFICATION:
    VERSION_V1:
      Vendor: "AWS_SES"
  SALARY_B2B_LEAD_OTP_VERIFIED_SLOT_NOT_BOOKED:
    VERSION_V1:
      Vendor: "AWS_SES"
  SALARY_B2B_LEAD_OTP_VERIFIED_SLOT_BOOKED:
    VERSION_V1:
      Vendor: "AWS_SES"
  SALARY_B2B_LEAD_WORK_EMAIL_NOT_VERIFIED:
    VERSION_V1:
      Vendor: "AWS_SES"
  WEB_MIN_KYC_CLOSED_ACC_BALANCE_TRANSFER_OTP:
    VERSION_V1:
      Vendor: "AWS_SES"
  DISPUTE_ESCALATED_DMP_DISPUTE_STATUS_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  DISPUTE_RESOLVED_DMP_DISPUTE_STATUS_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  AUTHORIZED_DISPUTE_CHARGEBACK_ACCEPTED_DMP_DISPUTE_STATUS_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  AUTHORIZED_DISPUTE_CHARGEBACK_REJECTED_DMP_DISPUTE_STATUS_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  UNAUTHORIZED_DISPUTE_LIABILITY_ACCEPTED_DMP_DISPUTE_STATUS_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  UNAUTHORIZED_DISPUTE_LIABILITY_REJECTED_DMP_DISPUTE_STATUS_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  EMAIL_SAVINGS_ACCOUNT_SUMMARY:
    VERSION_V1:
      Vendor: "AWS_SES"
  EMAIL_UPDATE_FEDERAL:
    VERSION_V1:
      Vendor: "AWS_SES"
  RISK_OUTCALL_ATTEMPT_REMINDER:
    VERSION_V1:
      Vendor: "AWS_SES"
  CREDIT_CARD_WELCOME_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  CREDIT_CARD_ELIGIBLE_WEB_FLOW_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  SAVINGS_ACCOUNT_CLOSURE_REQUEST:
    VERSION_V1:
      Vendor: "AWS_SES"
  CLUB_ITC_GREEN_POINTS_TRANSFER_REQUEST_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  MAGNIFI_WELCOME_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  CREDIT_CARD_EMI_CREATION_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  CREDIT_CARD_EMI_CANCELLATION_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  CREDIT_CARD_EMI_PRE_CLOSURE_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  RISK_OUTCALL:
    VERSION_V1:
      Vendor: "AWS_SES"
  PUSH_APP_LOGS:
    VERSION_V1:
      VENDOR: "AWS_SES"
  RISKOPS_USER_ACC_DF:
    VERSION_V1:
      VENDOR: "AWS_SES"
  RISKOPS_USER_VKYC_CF:
    VERSION_V1:
      VENDOR: "AWS_SES"
  CREDIT_CARD_EMI_CLOSURE_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  RISKOPS_USER_M_KYC_PCF:
    VERSION_V1:
      VENDOR: "AWS_SES"
  LAMF_CX_LOAN_APPLICATION_CLOSURE_RESOLUTION_EMAIL:
    VERSION_V1:
      VENDOR: "AWS_SES"
  LAMF_CX_LOAN_APPLICATION_RETRY_RESOLUTION_EMAIL:
    VERSION_V1:
      VENDOR: "AWS_SES"
  LAMF_CX_LOAN_ACCOUNT_CLOSURE_RESOLUTION_EMAIL:
    VERSION_V1:
      VENDOR: "AWS_SES"
  DEBIT_CARD_AMC_CHARGES_REPORT_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  USS_TAX_DOCUMENT_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  JUMP_YEARLY_ACCOUNT_STATEMENT_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  MF_EXTERNAL_CAPITAL_GAINS_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  RISK_UNIFIED_LEA_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  RISKOPS_USER_MEDIUM_RISK_CF_FORM:
    VERSION_V1:
      Vendor: "AWS_SES"
  STOCKGUARDIAN_LOAN_APPLICATION_ESIGN_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  DEBIT_CARD_INTERNATIONAL_ATM_WITHDRAWAL_LIMIT_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  RISK_UNIFIED_LEA_LAYER_INVESTIGATION_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  TIERING_REWARDS_SUMMARY_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  CX_TICKET_RESOLUTION_CSAT_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"
  CC_KYC_COMPLIANCE_STATUS_CHANGE_EMAIL:
    VERSION_V1:
      Vendor: "AWS_SES"

WhatsappTemplates:
  WHATSAPP_TYPE_CARD_PRINTED:
    VERSION_V1:
      vendor: "ACL"
  WHATSAPP_TYPE_PHYSICAL_CARD_ORDER_SUCCESS:
    VERSION_V1:
      vendor: "ACL"
  WHATSAPP_TYPE_CARD_DISPATCHED:
    VERSION_V1:
      vendor: "ACL"
    VERSION_V2:
      vendor: "ACL"
  WHATSAPP_TYPE_CARD_OUT_FOR_DELIVERY:
    VERSION_V1:
      vendor: "ACL"
    VERSION_V2:
      vendor: "ACL"
  WHATSAPP_TYPE_CARD_DELIVERED:
    VERSION_V1:
      vendor: "ACL"
    VERSION_V2:
      vendor: "ACL"
  WHATSAPP_TYPE_CARD_RETURNED_TO_ORIGIN:
    VERSION_V1:
      vendor: "ACL"
  FIRST_DEFAULT_OPT_IN:
    VERSION_V1:
      Vendor: "ACL"
    VERSION_V2:
      Vendor: "ACL"
    VERSION_V3:
      Vendor: "ACL"
  OPT_OUT:
    VERSION_V1:
      Vendor: "ACL"
  OPT_BACK_IN:
    VERSION_V1:
      Vendor: "ACL"
  GENERIC_REPLY:
    VERSION_V1:
      Vendor: "ACL"
  FI_EARLY_ACCESS_CBO:
    VERSION_V1:
      Vendor: "ACL"
  FI_EARLY_ACCESS_NON_CBO:
    VERSION_V1:
      Vendor: "ACL"
  FI_NORMAL_ACCESS_WITHOUT_PLAYSTORE:
    VERSION_V1:
      Vendor: "ACL"
  FI_NORMAL_ACCESS_WITH_PLAYSTORE:
    VERSION_V1:
      Vendor: "ACL"
  FI_CBO_ACCESS_WITHOUT_PLAYSTORE:
    VERSION_V1:
      Vendor: "ACL"
  FI_CBO_ACCESS_WITH_PLAYSTORE:
    VERSION_V1:
      Vendor: "ACL"
  VKYC_FIRST_REMINDER:
    VERSION_V1:
      Vendor: "ACL"
  VKYC_SECOND_REMINDER:
    VERSION_V1:
      Vendor: "ACL"
  VKYC_THIRD_REMINDER:
    VERSION_V1:
      Vendor: "ACL"
  FI_EARLY_ACCESS_FINITE_CODE:
    VERSION_V1:
      Vendor: "ACL"
  FI_CBO_ACCESS_FINITE_CODE:
    VERSION_V1:
      Vendor: "ACL"
  EARLY_ACCESS_FINITE_CODE:
    VERSION_V1:
      Vendor: "ACL"
  EARLY_ACCESS_CBO_FINITE_CODE:
    VERSION_V1:
      Vendor: "ACL"
  FI_BOT_WELCOME:
    VERSION_V1:
      Vendor: "ACL"
  FI_BOT_STATUS_CBO_ADMIT:
    VERSION_V1:
      Vendor: "ACL"
  FI_BOT_STATUS_WAITLIST_ADMIT:
    VERSION_V1:
      Vendor: "ACL"
  FI_BOT_STATUS_WAITLIST_UNDER_CONSIDERATION:
    VERSION_V1:
      Vendor: "ACL"
  FI_BOT_STATUS_CBO_UNDER_CONSIDERATION:
    VERSION_V1:
      Vendor: "ACL"
  FI_BOT_STATUS_UNREGISTERED:
    VERSION_V1:
      Vendor: "ACL"
  FI_BOT_STATUS_ONBOARDING_STARTED:
    VERSION_V1:
      Vendor: "ACL"
  FI_BOT_STATUS_DIRECT_ADMIT:
    VERSION_V1:
      Vendor: "ACL"
  FI_BOT_GENERIC_RESPONSE_WAITLIST_USER:
    VERSION_V1:
      Vendor: "ACL"
  FI_BOT_HELP:
    VERSION_V1:
      Vendor: "ACL"
  FI_BOT_GENERIC_RESPONSE_ONBOARDED_USER:
    VERSION_V1:
      Vendor: "ACL"
  FI_BOT_GENERIC_RESPONSE_UNREGISTERED_USER:
    VERSION_V1:
      Vendor: "ACL"
  FI_BOT_STATUS_ONBOARDING_COMPLETED:
    VERSION_V1:
      Vendor: "ACL"
  FI_BOT_GENERIC_RESPONSE_ONBOARDING_STARTED:
    VERSION_V1:
      Vendor: "ACL"
  FI_BOT_HELLO_UNREGISTERED:
    VERSION_V1:
      Vendor: "ACL"
  FI_BOT_HELLO_WAITLIST:
    VERSION_V1:
      Vendor: "ACL"
  FI_BOT_HELLO_ONBOARDING_STARTED:
    VERSION_V1:
      Vendor: "ACL"
  FI_BOT_HELLO_ONBOARDING_COMPLETED:
    VERSION_V1:
      Vendor: "ACL"
  FI_BOT_BENEFITS:
    VERSION_V1:
      Vendor: "ACL"
  FI_BOT_FEATURES:
    VERSION_V1:
      Vendor: "ACL"
  VKYC_NEW_USER_REMINDER:
    VERSION_V1:
      Vendor: "ACL"
    VERSION_V2:
      Vendor: "ACL"
  VKYC_12M_VALIDITY_REMINDER:
    VERSION_V1:
      Vendor: "ACL"
  INVEST_IN_MF_REMINDER:
    VERSION_V1:
      Vendor: "ACL"
  OB_DOB_AND_PAN_DROP_OFF:
    VERSION_V1:
      Vendor: "ACL"
    VERSION_V2:
      Vendor: "ACL"
  WEB_ONBOARDING_COMPLETED:
    VERSION_V1:
      Vendor: "ACL"
  OB_VKYC_DROP_OFF_FIRST_REMINDER:
    VERSION_V1:
      Vendor: "ACL"
  OB_VKYC_DROP_OFF_SECOND_REMINDER:
    VERSION_V1:
      Vendor: "ACL"
  CATEGORY_SPENDS_EXCEEDED_REMINDER:
    VERSION_V1:
      Vendor: "ACL"
    VERSION_V2:
      Vendor: "ACL"
  AMOUNT_SPENDS_EXCEEDED_REMINDER:
    VERSION_V1:
      Vendor: "ACL"
    VERSION_V2:
      Vendor: "ACL"
  CREDIT_CARD_BILL_PAYMENT_DUE_DATE_REMINDER:
    VERSION_V1:
      Vendor: "ACL"
    VERSION_V2:
      Vendor: "ACL"
  EPAN_DOWNLOADED_FIRST_VKYC_REMINDER:
    VERSION_V1:
      Vendor: "ACL"
  EPAN_DOWNLOADED_SECOND_VKYC_REMINDER:
    VERSION_V1:
      Vendor: "ACL"
  EPAN_DOWNLOADED_THIRD_VKYC_REMINDER:
    VERSION_V1:
      Vendor: "ACL"
  JUMP_YEARLY_ACCOUNT_STATEMENT:
    VERSION_V1:
      Vendor: "ACL"
  WA_SAVINGS_ACCOUNT_SUMMARY:
    VERSION_V1:
      Vendor: "ACL"
  CC_BILL_REPAYMENT_REMINDER_ON_STATEMENT_GEN_DAY:
    VERSION_V1:
      Vendor: "ACL"
  CC_BILL_REPAYMENT_REMINDER_PAYMENT_DUE_TOMORROW:
    VERSION_V1:
      Vendor: "ACL"
  NEW_CC_BILL_REPAYMENT_REMINDER_ON_DUE_DAY:
    VERSION_V1:
      Vendor: "ACL"
  CC_BILL_REPAYMENT_REMINDER_PAYMENT_DUE_YESTERDAY:
    VERSION_V1:
      Vendor: "ACL"
  CC_BILL_REPAYMENT_REMINDER_AFTER_X_DUE_DAYS:
    VERSION_V1:
      Vendor: "ACL"
  CC_BILL_REPAYMENT_REMINDER_CREDIT_SCORE_IS_FALLING:
    VERSION_V1:
      Vendor: "ACL"
  LAMF_LOW_BALANCE_UPCOMING_EMI_REMINDER:
    VERSION_V1:
      Vendor: "ACL"
  LAMF_EMANDATE_BOUNCE_ALERT:
    VERSION_V1:
      Vendor: "ACL"
  LAMF_EMI_OVERDUE_REMINDER:
    VERSION_V1:
      Vendor: "ACL"
  WHATSAPP_TYPE_CC_ELIGIBLE_WEB_FLOW_COMPLETE_APPLICATION:
    VERSION_V1:
      Vendor: "ACL"
  WHATSAPP_TYPE_CC_ELIGIBLE_WEB_FLOW_APPLICATION_PENDING:
    VERSION_V1:
      Vendor: "ACL"
  WHATSAPP_TYPE_CC_FI_LITE_CARD_CREATION_SUCCESS:
    VERSION_V1:
      Vendor: "ACL"
  WHATSAPP_TYPE_DEBIT_CARD_INSUFFICIENT_FUNDS_FOR_TRANSACTION:
    VERSION_V1:
      Vendor: "ACL"
  WHATSAPP_TYPE_DEBIT_CARD_INTERNATIONAL_TRANSACTIONS_NOT_ENABLED:
    VERSION_V1:
      Vendor: "ACL"
  WHATSAPP_TYPE_DEBIT_CARD_CONTACTLESS_CARD_USAGE_NOT_ENABLED:
    VERSION_V1:
      Vendor: "ACL"
  WHATSAPP_TYPE_DEBIT_CARD_POS_NOT_ENABLED:
    VERSION_V1:
      Vendor: "ACL"
  WHATSAPP_TYPE_DEBIT_CARD_ATM_USAGE_NOT_ENABLED:
    VERSION_V1:
      Vendor: "ACL"
  WHATSAPP_TYPE_DEBIT_CARD_INTL_DAILY_ALLOWED_WITHDRAWAL_LIMIT_REACHED:
    VERSION_V1:
      Vendor: "ACL"
  WHATSAPP_TYPE_DEBIT_CARD_INTL_DAILY_MAX_WITHDRAWAL_LIMIT_REACHED:
    VERSION_V1:
      Vendor: "ACL"
  WHATSAPP_TYPE_RISKOPS_CF:
    VERSION_V1:
      Vendor: "ACL"
  WHATSAPP_TYPE_RISK_CREDIT_FREEZE:
    VERSION_V1:
      Vendor: "ACL"
  WHATSAPP_TYPE_CX_TICKET_RESOLUTION_CSAT:
    VERSION_V1:
      Vendor: "ACL"

WhatsappFromNumber: "918010921490"

Notifications:
  DefaultExpiryDuration: "48h"
  IsV2Enabled: true

FeatureFlags:
  FetchEmailTemplatesFromDb: true
  EnableWhatsappBot: false

RedisOptions:
  Options:
    Addr: "localhost:6379"
    Password: "" ## empty string for no password
    DB: 5

CommsNotificationCacheConfig:
  IsCachingEnabled: true
  NotificationKeyPrefix: "comms_notifications_in_app_"
  NotificationCountPrefix: "comms_notifications_count_in_app_"
  CacheDuration: "24h"

AwsPinpointEventsConfig:
  WhatsappVendor: "ACL"
  SmsVendor: "KALEYRA"
  SmsAccount: "EPIFI"

CommsValidatorConfig:
  EnableAccessRevokeCheck: true
  WhitelistedWhatsappTemplatesForBlockedUserMap:
    OPT_BACK_IN: true

Profiling:
  StackDriverProfiling:
    ProjectId: "development"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

CommsRetryLogConfig:
  IsRetryLoggingEnabled: true

CommsEmailConfig:
  WhiteListedEmailIds: [ ]
  IsWhiteListEmailIdCheckEnabled: true
  WhiteListedEmailIdsTTL: "604800s"
