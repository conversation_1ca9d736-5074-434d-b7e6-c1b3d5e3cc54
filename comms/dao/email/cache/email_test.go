package cache

import (
	"context"
	"errors"
	"flag"
	"os"
	"testing"
	"time"

	"github.com/golang/mock/gomock"

	mock_cache "github.com/epifi/be-common/pkg/cache/mocks"

	"github.com/epifi/gamma/comms/config/genconf"
	"github.com/epifi/gamma/comms/constant"
	emailModel "github.com/epifi/gamma/comms/model/email"
	"github.com/epifi/gamma/comms/test"
)

func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	_, genConf, _, teardown := test.InitTestServerForPGDB()
	ETS = EmailCacheTestSuite{
		genConf: genConf,
	}
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

type EmailCacheTestSuite struct {
	genConf *genconf.Config
}

var (
	mockEmailIdOne         = "<EMAIL>"
	mockEmailIdCacheKey    = constant.WhitelistedEmailIdPrefix + mockEmailIdOne
	mockEmailCacheSetError = errors.New("failed to set email in cache")
	mockEmailCacheGetError = errors.New("failed to Get email in cache")
	mockEmailKeyCacheVal   = ""
	ETS                    EmailCacheTestSuite
)

func TestEmail_AddEmailIdToWhiteList(t *testing.T) {
	type args struct {
		ctx     context.Context
		emailId *emailModel.WhiteListedEmailId
	}
	ctr := gomock.NewController(t)
	mockCache := mock_cache.NewMockCacheStorage(ctr)
	mockExpiryDuration, _ := time.ParseDuration(ETS.genConf.CommsEmailConfig().WhiteListedEmailIdsTTL())
	tests := []struct {
		name      string
		args      args
		mocks     []interface{}
		wantError bool
	}{
		{
			name: "add email id to cache failure",
			args: args{
				ctx:     context.Background(),
				emailId: &emailModel.WhiteListedEmailId{EmailId: mockEmailIdOne},
			},
			mocks: []interface{}{
				mockCache.EXPECT().Set(context.Background(), mockEmailIdCacheKey, mockEmailKeyCacheVal, mockExpiryDuration).Return(mockEmailCacheSetError),
			},
			wantError: true,
		}, {
			name: "add email id to cache success",
			args: args{
				ctx:     context.Background(),
				emailId: &emailModel.WhiteListedEmailId{EmailId: mockEmailIdOne},
			},
			mocks: []interface{}{
				mockCache.EXPECT().Set(context.Background(), mockEmailIdCacheKey, mockEmailKeyCacheVal, mockExpiryDuration).Return(nil),
			},
			wantError: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cacheStore := NewEmailDao(mockCache, ETS.genConf)
			err := cacheStore.AddEmailIdToWhiteList(tt.args.ctx, tt.args.emailId)
			if (err != nil) != tt.wantError {
				t.Errorf("AddEmailIdToWhiteList() error = %v, wantErr %v", err, tt.wantError)
				return
			}
		})
	}
}
func TestEmail_CheckIfEmailIdWhiteListed(t *testing.T) {
	type args struct {
		ctx     context.Context
		emailId *emailModel.WhiteListedEmailId
	}
	ctr := gomock.NewController(t)
	mockCache := mock_cache.NewMockCacheStorage(ctr)
	tests := []struct {
		name      string
		args      args
		mocks     []interface{}
		wantError bool
	}{
		{
			name: "Get email id from cache failure",
			args: args{
				ctx:     context.Background(),
				emailId: &emailModel.WhiteListedEmailId{EmailId: mockEmailIdOne},
			},
			mocks: []interface{}{
				mockCache.EXPECT().Get(context.Background(), mockEmailIdCacheKey).Return("", mockEmailCacheGetError),
			},
			wantError: true,
		}, {
			name: "Get email id from cache success",
			args: args{
				ctx:     context.Background(),
				emailId: &emailModel.WhiteListedEmailId{EmailId: mockEmailIdOne},
			},
			mocks: []interface{}{
				mockCache.EXPECT().Get(context.Background(), mockEmailIdCacheKey).Return(mockEmailKeyCacheVal, nil),
			},
			wantError: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cacheStore := NewEmailDao(mockCache, ETS.genConf)
			_, err := cacheStore.IsEmailIdWhiteListed(tt.args.ctx, tt.args.emailId)
			if (err != nil) != tt.wantError {
				t.Errorf("AddEmailIdToWhiteList() error = %v, wantErr %v", err, tt.wantError)
				return
			}
		})
	}
}
