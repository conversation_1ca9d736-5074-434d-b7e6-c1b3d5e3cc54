package essential

import (
	"context"
	"fmt"

	"github.com/epifi/gamma/api/user"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
)

// UserManager abstracts the fetching of a user entity from the case management entities.
type UserManager interface {
	// GetUserByCaseId get the user entity by the case id
	// fails with epifierrors.ErrInvalidArgument when case id is invalid
	// fails when could not able to fetch the actor
	// fails when not able to fetch the user from user service
	GetUserByCaseId(ctx context.Context, caseId string) (*user.User, error)
}

type UserManagerImpl struct {
	actorManager ActorManager
	userClient   user.UsersClient
}

var _ UserManager = &UserManagerImpl{}

func NewUserManagerImpl(actorManager ActorManager, userClient user.UsersClient) *UserManagerImpl {
	return &UserManagerImpl{
		actorManager: actor<PERSON><PERSON>ger,
		userClient:   userClient,
	}
}

func (u *UserManagerImpl) GetUserByCaseId(ctx context.Context, caseId string) (*user.User, error) {

	if len(caseId) == 0 {
		return nil, epifierrors.ErrInvalidArgument
	}

	actorResp, actorErr := u.actorManager.GetActorByCaseId(ctx, caseId)

	if actorErr != nil {
		return nil, fmt.Errorf("could not fetch the actor: %w", actorErr)
	}

	userResp, userErr := u.userClient.GetUser(ctx, &user.GetUserRequest{
		Identifier: &user.GetUserRequest_Id{
			Id: actorResp.GetEntityId(),
		},
	})

	if grpcErr := epifigrpc.RPCError(userResp, userErr); grpcErr != nil {
		return nil, fmt.Errorf("could not fetch user from user service %w", grpcErr)
	}

	return userResp.GetUser(), nil
}
