package processors

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/types/known/timestamppb"

	rpc "github.com/epifi/be-common/api/rpc"
	caseManagementPb "github.com/epifi/gamma/api/risk/case_management"
	cmEnumsPb "github.com/epifi/gamma/api/risk/case_management/enums"
	mockCaseManagement "github.com/epifi/gamma/api/risk/case_management/mocks"
)

type screenerReviewBatchProcessorMockedDependencies struct {
	mockCaseManagementClient *mockCaseManagement.MockCaseManagementClient
}

func newScreenerReviewBatchProcessorWithMocks(t *testing.T) (*ScreenerReviewBatchProcessor, *screenerReviewBatchProcessorMockedDependencies, func()) {
	ctr := gomock.NewController(t)

	mockCaseManagementClient := mockCaseManagement.NewMockCaseManagementClient(ctr)

	md := &screenerReviewBatchProcessorMockedDependencies{
		mockCaseManagementClient: mockCaseManagementClient,
	}

	svc := &ScreenerReviewBatchProcessor{
		caseManagementClient: mockCaseManagementClient,
		cfg:                  riskDependencies.GenConf,
	}

	return svc, md, ctr.Finish
}

func TestScreenerReviewBatchProcessor_ProcessPayload(t *testing.T) {
	type args struct {
		ctx         context.Context
		payloadType caseManagementPb.PayloadType
		inputs      []*Input
	}

	validInput1 := &Input{
		Payload: &caseManagementPb.RiskCase_ScreenerReview{
			ScreenerReview: &caseManagementPb.ScreenerReview{
				ActorId:           "test-actor-1",
				ScreenerAttemptId: "screener-attempt-123",
			},
		},
		BatchId: "batch-1",
		RuleIdentifier: &caseManagementPb.RuleIdentifier{
			Identifier: &caseManagementPb.RuleIdentifier_ExternalId{
				ExternalId: "test-rule-1",
			},
		},
		InitiatedAt: timestamppb.New(time.Now()),
	}

	validInput2 := &Input{
		Payload: &caseManagementPb.RiskCase_ScreenerReview{
			ScreenerReview: &caseManagementPb.ScreenerReview{
				ActorId:           "test-actor-2",
				ScreenerAttemptId: "screener-attempt-456",
			},
		},
		BatchId: "batch-1",
		RuleIdentifier: &caseManagementPb.RuleIdentifier{
			Identifier: &caseManagementPb.RuleIdentifier_ExternalId{
				ExternalId: "test-rule-2",
			},
		},
		InitiatedAt: timestamppb.New(time.Now()),
	}

	invalidPayloadInput := &Input{
		Payload: &caseManagementPb.RiskCase_UserReview{
			UserReview: &caseManagementPb.UserReview{
				ActorId: "test-actor-1",
			},
		},
		BatchId: "batch-1",
	}

	emptyActorIdInput := &Input{
		Payload: &caseManagementPb.RiskCase_ScreenerReview{
			ScreenerReview: &caseManagementPb.ScreenerReview{
				ActorId:           "",
				ScreenerAttemptId: "screener-attempt-123",
			},
		},
		BatchId: "batch-1",
	}

	tests := []struct {
		name       string
		args       args
		setupMocks func(mock *screenerReviewBatchProcessorMockedDependencies, args args)
		wantErr    int // number of expected failures
	}{
		{
			name: "invalid payload type",
			args: args{
				ctx:         context.Background(),
				payloadType: caseManagementPb.PayloadType_PAYLOAD_TYPE_USER_REVIEW,
				inputs:      []*Input{validInput1},
			},
			wantErr: 1,
		},
		{
			name: "invalid payload structure",
			args: args{
				ctx:         context.Background(),
				payloadType: caseManagementPb.PayloadType_PAYLOAD_TYPE_SCREENER,
				inputs:      []*Input{invalidPayloadInput},
			},
			wantErr: 1,
		},
		{
			name: "validation error - empty actor id",
			args: args{
				ctx:         context.Background(),
				payloadType: caseManagementPb.PayloadType_PAYLOAD_TYPE_SCREENER,
				inputs:      []*Input{emptyActorIdInput},
			},
			wantErr: 1,
		},
		{
			name: "some payloads invalid",
			args: args{
				ctx:         context.Background(),
				payloadType: caseManagementPb.PayloadType_PAYLOAD_TYPE_SCREENER,
				inputs:      []*Input{validInput1, invalidPayloadInput, validInput2},
			},
			setupMocks: func(mock *screenerReviewBatchProcessorMockedDependencies, args args) {
				// Should create alerts for valid inputs only
				mock.mockCaseManagementClient.EXPECT().
					CreateAlerts(gomock.Any(), gomock.Any()).
					Return(&caseManagementPb.CreateAlertsResponse{
						Status: rpc.StatusOk(),
					}, nil).
					Times(1) // One batch call for 2 valid inputs
			},
			wantErr: 1, // One invalid payload
		},
		{
			name: "CreateAlerts RPC failure",
			args: args{
				ctx:         context.Background(),
				payloadType: caseManagementPb.PayloadType_PAYLOAD_TYPE_SCREENER,
				inputs:      []*Input{validInput1, validInput2},
			},
			setupMocks: func(mock *screenerReviewBatchProcessorMockedDependencies, args args) {
				mock.mockCaseManagementClient.EXPECT().
					CreateAlerts(gomock.Any(), gomock.Any()).
					Return(nil, errors.New("network error")).
					Times(1)
			},
			wantErr: 2, // Both inputs should fail
		},
		{
			name: "successful batch processing",
			args: args{
				ctx:         context.Background(),
				payloadType: caseManagementPb.PayloadType_PAYLOAD_TYPE_SCREENER,
				inputs:      []*Input{validInput1, validInput2},
			},
			setupMocks: func(mock *screenerReviewBatchProcessorMockedDependencies, args args) {
				mock.mockCaseManagementClient.EXPECT().
					CreateAlerts(gomock.Any(), gomock.Any()).
					Return(&caseManagementPb.CreateAlertsResponse{
						Status: rpc.StatusOk(),
					}, nil).
					Times(1)
			},
			wantErr: 0,
		},
		{
			name: "batch size handling - multiple batches",
			args: args{
				ctx:         context.Background(),
				payloadType: caseManagementPb.PayloadType_PAYLOAD_TYPE_SCREENER,
				inputs: []*Input{
					validInput1, validInput2, // Will be processed together
					{
						Payload: &caseManagementPb.RiskCase_ScreenerReview{
							ScreenerReview: &caseManagementPb.ScreenerReview{
								ActorId:           "test-actor-3",
								ScreenerAttemptId: "screener-attempt-789",
							},
						},
						BatchId: "batch-1",
					}, // Will be processed in the final batch
				},
			},
			setupMocks: func(mock *screenerReviewBatchProcessorMockedDependencies, args args) {
				// Since we don't know the exact batch size from config, we'll expect at least one call
				mock.mockCaseManagementClient.EXPECT().
					CreateAlerts(gomock.Any(), gomock.Any()).
					Return(&caseManagementPb.CreateAlertsResponse{
						Status: rpc.StatusOk(),
					}, nil).
					AnyTimes() // Allow any number of batch calls
			},
			wantErr: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, mockedDeps, assertTest := newScreenerReviewBatchProcessorWithMocks(t)
			if tt.setupMocks != nil {
				tt.setupMocks(mockedDeps, tt.args)
			}
			failures := s.ProcessPayload(tt.args.ctx, tt.args.payloadType, tt.args.inputs)
			assert.Len(t, failures, tt.wantErr, "Expected %d failures, got %d", tt.wantErr, len(failures))
			assertTest()
		})
	}
}

func TestScreenerReviewPayloadsToRawAlerts(t *testing.T) {
	validInput := &Input{
		Payload: &caseManagementPb.RiskCase_ScreenerReview{
			ScreenerReview: &caseManagementPb.ScreenerReview{
				ActorId:           "test-actor-1",
				ScreenerAttemptId: "screener-attempt-123",
			},
		},
		BatchId: "batch-1",
		RuleIdentifier: &caseManagementPb.RuleIdentifier{
			Identifier: &caseManagementPb.RuleIdentifier_ExternalId{
				ExternalId: "test-rule-1",
			},
		},
		InitiatedAt: timestamppb.New(time.Now()),
	}

	invalidInput := &Input{
		Payload: &caseManagementPb.RiskCase_UserReview{
			UserReview: &caseManagementPb.UserReview{
				ActorId: "test-actor-1",
			},
		},
		BatchId: "batch-1",
	}

	tests := []struct {
		name           string
		inputs         []*Input
		expectedAlerts int
		expectedFails  int
	}{
		{
			name:           "valid input conversion",
			inputs:         []*Input{validInput},
			expectedAlerts: 1,
			expectedFails:  0,
		},
		{
			name:           "invalid input conversion",
			inputs:         []*Input{invalidInput},
			expectedAlerts: 0,
			expectedFails:  1,
		},
		{
			name:           "mixed inputs",
			inputs:         []*Input{validInput, invalidInput},
			expectedAlerts: 1,
			expectedFails:  1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			alerts, failures := screenerReviewPayloadsToRawAlerts(tt.inputs)
			assert.Len(t, alerts, tt.expectedAlerts, "Expected %d alerts, got %d", tt.expectedAlerts, len(alerts))
			assert.Len(t, failures, tt.expectedFails, "Expected %d failures, got %d", tt.expectedFails, len(failures))

			// Validate alert structure for valid inputs
			for _, alert := range alerts {
				assert.Equal(t, cmEnumsPb.EntityType_ENTITY_TYPE_SCREENER, alert.EntityType)
				assert.NotEmpty(t, alert.ActorId)
				assert.NotEmpty(t, alert.EntityId)
				assert.NotEmpty(t, alert.BatchName)
			}
		})
	}
}

func TestValidateScreenerReviewPayload(t *testing.T) {
	tests := []struct {
		name    string
		payload *caseManagementPb.ScreenerReview
		wantErr bool
	}{
		{
			name: "valid payload",
			payload: &caseManagementPb.ScreenerReview{
				ActorId:           "test-actor-1",
				ScreenerAttemptId: "screener-attempt-123",
			},
			wantErr: false,
		},
		{
			name: "empty actor id",
			payload: &caseManagementPb.ScreenerReview{
				ActorId:           "",
				ScreenerAttemptId: "screener-attempt-123",
			},
			wantErr: true,
		},
		{
			name: "empty screener attempt id",
			payload: &caseManagementPb.ScreenerReview{
				ActorId:           "test-actor-1",
				ScreenerAttemptId: "",
			},
			wantErr: true,
		},
		{
			name: "both fields empty",
			payload: &caseManagementPb.ScreenerReview{
				ActorId:           "",
				ScreenerAttemptId: "",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateScreenerReviewPayload(tt.payload)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
