package prioritisation

import (
	"context"
	"fmt"

	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/epifi/gamma/api/risk/case_management"
	prioritizationPb "github.com/epifi/gamma/api/risk/case_management/prioritization"
	"github.com/epifi/gamma/api/vendorgateway/risk"
	"github.com/epifi/gamma/risk/case_management/prioritisation/priotizer"
)

// Factory is the interface to fetch the factory implementation of the prioritization model.
// It provides abstraction over model decider and input parameter required for the prioritization
//
//go:generate mockgen -source=./factory.go -destination=./mocks/mock_factory.go -package=mocks
type Factory interface {
	GetPrioritizationModel(ctx context.Context, modelType prioritizationPb.PrioritizationModelType,
		actorId string, alerts []*case_management.AlertWithRuleDetails, caseDetails *risk.GetCasePrioritisationScoreRequest_CaseDetails) (priotizer.Prioritization,
		*prioritizationPb.InputParameter, error)
}

type FactoryImpl struct {
	precisionPrioritizationModel   priotizer.IPrecisionBasedPrioritization
	precisionPrioritizationDSModel priotizer.IDSModelBasedPrioritization
}

var _ Factory = &FactoryImpl{}

func NewFactoryImpl(precisionPrioritizationModel priotizer.IPrecisionBasedPrioritization, precisionPrioritizationDSModel priotizer.IDSModelBasedPrioritization) *FactoryImpl {
	return &FactoryImpl{
		precisionPrioritizationModel:   precisionPrioritizationModel,
		precisionPrioritizationDSModel: precisionPrioritizationDSModel,
	}
}

func (f FactoryImpl) GetPrioritizationModel(ctx context.Context, modelType prioritizationPb.PrioritizationModelType, actorId string,
	alerts []*case_management.AlertWithRuleDetails, caseDetails *risk.GetCasePrioritisationScoreRequest_CaseDetails) (priotizer.Prioritization, *prioritizationPb.InputParameter, error) {
	if modelType == prioritizationPb.PrioritizationModelType_PRIORITIZATION_MODEL_TYPE_UNSPECIFIED {
		return nil, nil, fmt.Errorf("invalid model type %w", epifierrors.ErrInvalidArgument)
	}
	switch modelType {
	case prioritizationPb.PrioritizationModelType_PRIORITIZATION_MODEL_TYPE_PRECISION:
		params := &prioritizationPb.InputParameter{
			Alerts:         alerts,
			ModelTypes:     []prioritizationPb.ModelTypeParameter_ModelType{},
			WatchListTypes: []prioritizationPb.WatchListTypeParameter_WatchListType{prioritizationPb.WatchListTypeParameter_WATCH_LIST_TYPE_ACTOR},
			ActorId:        actorId,
		}
		return f.precisionPrioritizationModel, params, nil
	case prioritizationPb.PrioritizationModelType_PRIORITIZATION_MODEL_TYPE_DS_PRECISION:
		params := &prioritizationPb.InputParameter{
			Alerts:      alerts,
			ActorId:     actorId,
			CaseDetails: caseDetails,
		}
		return f.precisionPrioritizationDSModel, params, nil
	default:
		return nil, nil, fmt.Errorf("could not find the prioritization model %w", epifierrors.ErrInvalidArgument)
	}
}
