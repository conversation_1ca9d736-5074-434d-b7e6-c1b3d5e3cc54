package prioritisation

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/epifi/gamma/api/risk/case_management"
	prioritizationPb "github.com/epifi/gamma/api/risk/case_management/prioritization"
	"github.com/epifi/gamma/api/vendorgateway/risk"
	"github.com/epifi/gamma/risk/case_management/prioritisation/priotizer"
	"github.com/epifi/gamma/risk/case_management/prioritisation/priotizer/mocks"
)

func Test_factoryImpl_GetPrioritizationModel(t *testing.T) {
	ctrl := gomock.NewController(t)
	ctx := context.Background()
	mockPrioritizationModel := mocks.NewMockPrioritization(ctrl)
	var (
		actorId = "actorId"
		alerts  = []*case_management.AlertWithRuleDetails{}
	)
	type args struct {
		modelType   prioritizationPb.PrioritizationModelType
		actorId     string
		alerts      []*case_management.AlertWithRuleDetails
		caseDetails *risk.GetCasePrioritisationScoreRequest_CaseDetails
	}
	tests := []struct {
		name      string
		args      args
		want      priotizer.Prioritization
		want1     *prioritizationPb.InputParameter
		wantErr   bool
		assertErr func(err error) bool
	}{
		{
			name:    "fails for invalid input",
			args:    args{},
			wantErr: true,
			assertErr: func(err error) bool {
				return errors.Is(err, epifierrors.ErrInvalidArgument)
			},
		},
		{
			name: "fails with invalid argument for legacy",
			args: args{
				modelType: prioritizationPb.PrioritizationModelType_PRIORITIZATION_MODEL_TYPE_LEGACY,
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return errors.Is(err, epifierrors.ErrInvalidArgument)
			},
		},
		{
			name: "success",
			args: args{
				modelType:   prioritizationPb.PrioritizationModelType_PRIORITIZATION_MODEL_TYPE_PRECISION,
				actorId:     actorId,
				alerts:      alerts,
				caseDetails: nil,
			},
			want: mockPrioritizationModel,
			want1: &prioritizationPb.InputParameter{
				Alerts:         alerts,
				WatchListTypes: []prioritizationPb.WatchListTypeParameter_WatchListType{prioritizationPb.WatchListTypeParameter_WATCH_LIST_TYPE_ACTOR},
				ModelTypes:     []prioritizationPb.ModelTypeParameter_ModelType{},
				ActorId:        actorId,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			f := NewFactoryImpl(mockPrioritizationModel, mockPrioritizationModel)
			got, got1, err := f.GetPrioritizationModel(ctx, tt.args.modelType, tt.args.actorId, tt.args.alerts, tt.args.caseDetails)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPrioritizationModel() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr && tt.assertErr != nil && !tt.assertErr(err) {
				t.Errorf("Get() error = %v assertion failed", err)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetPrioritizationModel() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("GetPrioritizationModel() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}
