// Code generated by MockGen. DO NOT EDIT.
// Source: ./factory.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	case_management "github.com/epifi/gamma/api/risk/case_management"
	prioritization "github.com/epifi/gamma/api/risk/case_management/prioritization"
	risk "github.com/epifi/gamma/api/vendorgateway/risk"
	priotizer "github.com/epifi/gamma/risk/case_management/prioritisation/priotizer"
	gomock "github.com/golang/mock/gomock"
)

// MockFactory is a mock of Factory interface.
type MockFactory struct {
	ctrl     *gomock.Controller
	recorder *MockFactoryMockRecorder
}

// MockFactoryMockRecorder is the mock recorder for MockFactory.
type MockFactoryMockRecorder struct {
	mock *MockFactory
}

// NewMockFactory creates a new mock instance.
func NewMockFactory(ctrl *gomock.Controller) *MockFactory {
	mock := &MockFactory{ctrl: ctrl}
	mock.recorder = &MockFactoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFactory) EXPECT() *MockFactoryMockRecorder {
	return m.recorder
}

// GetPrioritizationModel mocks base method.
func (m *MockFactory) GetPrioritizationModel(ctx context.Context, modelType prioritization.PrioritizationModelType, actorId string, alerts []*case_management.AlertWithRuleDetails, caseDetails *risk.GetCasePrioritisationScoreRequest_CaseDetails) (priotizer.Prioritization, *prioritization.InputParameter, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPrioritizationModel", ctx, modelType, actorId, alerts, caseDetails)
	ret0, _ := ret[0].(priotizer.Prioritization)
	ret1, _ := ret[1].(*prioritization.InputParameter)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetPrioritizationModel indicates an expected call of GetPrioritizationModel.
func (mr *MockFactoryMockRecorder) GetPrioritizationModel(ctx, modelType, actorId, alerts, caseDetails interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPrioritizationModel", reflect.TypeOf((*MockFactory)(nil).GetPrioritizationModel), ctx, modelType, actorId, alerts, caseDetails)
}
