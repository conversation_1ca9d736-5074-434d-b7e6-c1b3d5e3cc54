package priotizer

// nolint: goimports
import (
	"context"
	"fmt"

	"github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/google/uuid"
	"go.uber.org/zap"

	"github.com/epifi/gamma/api/risk/case_management/prioritization"
	"github.com/epifi/gamma/api/vendorgateway/risk"
	"github.com/epifi/gamma/risk/case_management/prioritisation/integrator"
)

const modelVersion = "v0"

type DSModelBasedPrioritization struct {
	ModelParameterIntegrator integrator.ParameterIntegrator
	VGRiskClient             risk.RiskClient
}

var _ IDSModelBasedPrioritization = &DSModelBasedPrioritization{}

func NewDSModelBasedPrioritization(modelParameterIntegrator integrator.ParameterIntegrator, vgRiskClient risk.RiskClient) *DSModelBasedPrioritization {
	return &DSModelBasedPrioritization{
		ModelParameterIntegrator: modelParameterIntegrator,
		VGRiskClient:             vgRiskClient,
	}
}

func (d *DSModelBasedPrioritization) GetConfidenceScore(ctx context.Context, params *prioritization.InputParameter) ([]*risk.ModelResponseInfo, float32, error) {
	if params == nil {
		return nil, 0, fmt.Errorf("invalid parameter %w", epifierrors.ErrInvalidArgument)
	}

	integratedParameter, integratedParameterErr := d.ModelParameterIntegrator.Get(ctx, params)
	if integratedParameterErr != nil {
		return nil, 0, fmt.Errorf("could not fetch the prioritization parameters, %w", integratedParameterErr)
	}

	reqId := uuid.NewString()
	request := &risk.GetCasePrioritisationScoreRequest{
		Header: &vendorgateway.RequestHeader{
			Vendor: vendorgateway.Vendor_IN_HOUSE,
		},
		ActorId:              params.GetActorId(),
		RequestId:            reqId,
		AlertWithRuleDetails: params.GetAlerts(),
		ModelVersion:         modelVersion,
		CaseDetails:          params.GetCaseDetails(),
	}

	getCasePrioritisationScoreRes, err := d.VGRiskClient.GetCasePrioritisationScore(ctx, request)
	if rpcErr := epifigrpc.RPCError(getCasePrioritisationScoreRes, err); rpcErr != nil {
		logger.Error(ctx, "error in VGRiskClient.GetCasePrioritisationScore call", zap.Error(rpcErr))
		return nil, 0, fmt.Errorf("error in VGRiskClient.GetCasePrioritisationScore call, err: %w", rpcErr)
	}

	modelResponse := getCasePrioritisationScoreRes.GetModelInfo()
	oldConfidenceScore := getCasePrioritisationScoreRes.GetScore()

	logger.Info(ctx, fmt.Sprintf("prioritization parameters %s", integratedParameter))
	// TODO discuss with DS team on how to handle when model names returned are same as well.
	// add a check to give error if response from model is not length 2
	if len(modelResponse) == 0 {
		logger.Error(ctx, "no model response for the field - `ModelResponseInfo`, empty array recieved. Will be populating model1Score, Model2Score with old ConfidenceScore")
		dummyModelResponseInfo := &risk.ModelResponseInfo{
			Name:  "Auto-Filled-Model-Name",
			Score: getCasePrioritisationScoreRes.GetScore(),
		}
		return []*risk.ModelResponseInfo{dummyModelResponseInfo, dummyModelResponseInfo}, 0, nil
	} else {
		for idx, modelResponseInfo := range modelResponse {
			logger.Info(ctx, "model response", zap.Any("model response", modelResponseInfo), zap.Int("response no:", idx+1))
		}
	}
	return modelResponse, oldConfidenceScore, nil
}
