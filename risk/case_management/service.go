package case_management

// nolint : goimports
import (
	"context"
	"fmt"
	"sort"
	"strconv"
	"sync"
	"time"

	"k8s.io/apimachinery/pkg/util/waitgroup"

	"github.com/google/uuid"
	"github.com/korovkin/limiter"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	protoJson "google.golang.org/protobuf/encoding/protojson"
	structPb "google.golang.org/protobuf/types/known/structpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"

	riskopsPb "github.com/epifi/gamma/api/cx/risk_ops"
	orderPb "github.com/epifi/gamma/api/order"
	caseManagementPb "github.com/epifi/gamma/api/risk/case_management"
	cmEnumsPb "github.com/epifi/gamma/api/risk/case_management/enums"
	formPb "github.com/epifi/gamma/api/risk/case_management/form"
	reviewPb "github.com/epifi/gamma/api/risk/case_management/review"
	cmWorkflowPb "github.com/epifi/gamma/api/risk/case_management/workflow"
	assignment "github.com/epifi/gamma/risk/case_management/case_assignment"
	"github.com/epifi/gamma/risk/case_management/casestore"
	"github.com/epifi/gamma/risk/case_management/dao"
	"github.com/epifi/gamma/risk/case_management/essential/aggregator"
	fiUserRelationship "github.com/epifi/gamma/risk/case_management/fi_user_relationship"
	"github.com/epifi/gamma/risk/case_management/form"
	formErrors "github.com/epifi/gamma/risk/case_management/form/errors"
	genConf "github.com/epifi/gamma/risk/config/genconf"
	"github.com/epifi/gamma/risk/txnhelper"
	wireTypes "github.com/epifi/gamma/risk/wire/types"
)

type Service struct {
	caseManagementPb.UnimplementedCaseManagementServer
	orderClient                       orderPb.OrderServiceClient
	txnHelper                         txnhelper.IRiskTransactionHelper
	caseStore                         casestore.CaseStore
	celestialClient                   celestialPb.CelestialClient
	reviewActionDao                   dao.ReviewAction
	alertDao                          dao.AlertDao
	aggregator                        aggregator.Aggregator
	commentDao                        dao.CommentDao
	allowedAnnotationDao              dao.AllowedAnnotationDao
	annotationDao                     dao.AnnotationDao
	ruleDao                           dao.RuleDao
	ruleProcessor                     RuleProcessor
	caseAllocator                     assignment.CaseAllocator
	fiUserRelationship                fiUserRelationship.FiUserRelationship
	formSubmissionEventPublisher      queue.Publisher
	formHandler                       form.Handler
	formDao                           dao.FormDao
	frmPgdbTxnExecutor                wireTypes.FrmPgdbTxnExecutor
	genConf                           *genConf.Config
	annotationTypeDao                 dao.AnnotationTypeDao
	uiElementAnnotationTypeMappingDao dao.UIElementAnnotationTypeMappingDao
	suggestedActionDao                dao.SuggestedActionDao
	ruleReviewTypeMappingsDao         dao.RuleReviewTypeMappingsDao
	transactionBlockDao               dao.TransactionBlockDao
	riskAnalyticsDao                  dao.RiskAnalyticsDao
}

func NewService(orderClient orderPb.OrderServiceClient, txnHelper txnhelper.IRiskTransactionHelper,
	caseStore casestore.CaseStore, celestialClient celestialPb.CelestialClient, reviewActionDao dao.ReviewAction,
	alertDao dao.AlertDao, aggregator aggregator.Aggregator, commentDao dao.CommentDao,
	allowedAnnotationDao dao.AllowedAnnotationDao, annotationDao dao.AnnotationDao, ruleDao dao.RuleDao,
	ruleProcessor RuleProcessor, caseAllocator assignment.CaseAllocator,
	fiUserRelationship fiUserRelationship.FiUserRelationship,
	formSubmissionEventPublisher wireTypes.FormSubmissionEventPublisher, formHandler form.Handler, formDao dao.FormDao,
	frmPgdbTxnExecutor wireTypes.FrmPgdbTxnExecutor, genConf *genConf.Config,
	annotationTypeDao dao.AnnotationTypeDao,
	uiElementAnnotationTypeMappingDao dao.UIElementAnnotationTypeMappingDao, suggestedActionDao dao.SuggestedActionDao,
	ruleReviewTypeMappingsDao dao.RuleReviewTypeMappingsDao, transactionBlockDao dao.TransactionBlockDao,
	riskAnalyticsDao dao.RiskAnalyticsDao) *Service {
	return &Service{
		orderClient:                       orderClient,
		txnHelper:                         txnHelper,
		caseStore:                         caseStore,
		reviewActionDao:                   reviewActionDao,
		alertDao:                          alertDao,
		aggregator:                        aggregator,
		celestialClient:                   celestialClient,
		commentDao:                        commentDao,
		allowedAnnotationDao:              allowedAnnotationDao,
		annotationDao:                     annotationDao,
		ruleDao:                           ruleDao,
		ruleProcessor:                     ruleProcessor,
		caseAllocator:                     caseAllocator,
		fiUserRelationship:                fiUserRelationship,
		formSubmissionEventPublisher:      formSubmissionEventPublisher,
		formHandler:                       formHandler,
		formDao:                           formDao,
		frmPgdbTxnExecutor:                frmPgdbTxnExecutor,
		genConf:                           genConf,
		annotationTypeDao:                 annotationTypeDao,
		uiElementAnnotationTypeMappingDao: uiElementAnnotationTypeMappingDao,
		suggestedActionDao:                suggestedActionDao,
		ruleReviewTypeMappingsDao:         ruleReviewTypeMappingsDao,
		transactionBlockDao:               transactionBlockDao,
		riskAnalyticsDao:                  riskAnalyticsDao,
	}
}

var _ caseManagementPb.CaseManagementServer = &Service{}

const (
	MaxConcurrencyLimitForAlertProcessing = 5
	MaxActionsInReviewDetails             = 100
	sixMonthDuration                      = 180 * 24 * time.Hour
)

var (
	ErrMarshallingAlertFailure     = errors.New("error while marshaling alert object")
	ErrProcessAlertWorkflowFailure = errors.New("error while triggering process alert workflow")
)

func (s *Service) GetTransactionDetailsForReview(ctx context.Context, req *caseManagementPb.GetTransactionDetailsForReviewRequest) (*caseManagementPb.GetTransactionDetailsForReviewResponse, error) {
	var txnDetails []*caseManagementPb.TransactionDetail
	actorId, err := s.getActorIdFromCaseId(ctx, req.GetCaseId())
	if err != nil {
		logger.Error(ctx, "error in getting actorId from caseId", zap.String(logger.CASE_ID, req.GetCaseId()), zap.Error(err))
		return &caseManagementPb.GetTransactionDetailsForReviewResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	orderReq, err := s.buildGetOrdersRequest(ctx, req, actorId)
	if err != nil {
		logger.Error(ctx, "error in building get order request", zap.String(logger.CASE_ID, req.GetCaseId()), zap.Error(err))
		return &caseManagementPb.GetTransactionDetailsForReviewResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	orderResp, err := s.orderClient.GetOrdersForActor(ctx, orderReq)
	if err = epifigrpc.RPCError(orderResp, err); err != nil {
		logger.Error(ctx, "error in get orders for actor", zap.Error(err), zap.String(logger.CASE_ID, req.GetCaseId()), zap.Error(err))
		if orderResp.GetStatus().IsRecordNotFound() {
			return &caseManagementPb.GetTransactionDetailsForReviewResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		}
		return &caseManagementPb.GetTransactionDetailsForReviewResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	txnDetails, err = s.getTxnDetailsFromOrders(ctx, orderResp.GetOrders(), actorId)
	if err != nil {
		logger.Error(ctx, "error in getting txn details from order", zap.Error(err))
		return &caseManagementPb.GetTransactionDetailsForReviewResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	pgCtx, err := getPageContextResponse(req.GetPageContext(), len(txnDetails))
	if err != nil {
		logger.Error(ctx, "error in getting page context response", zap.Error(err))
		return &caseManagementPb.GetTransactionDetailsForReviewResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	return &caseManagementPb.GetTransactionDetailsForReviewResponse{
		Status:             rpc.StatusOk(),
		TransactionDetails: txnDetails,
		PageContext:        pgCtx,
	}, nil
}

func getAccountingEntry(txnFromActorId, txnToActorId, actorId string) riskopsPb.AccountingEntry {
	switch {
	case txnFromActorId == actorId:
		return riskopsPb.AccountingEntry_DEBIT
	case txnToActorId == actorId:
		return riskopsPb.AccountingEntry_CREDIT
	default:
		return riskopsPb.AccountingEntry_ACCOUNTING_ENTRY_UNSPECIFIED
	}
}

func getReceiverType(order *orderPb.Order) string {
	if lo.Contains(order.GetTags(), orderPb.OrderTag_MERCHANT) {
		return receiverTypeP2M
	}

	return receiverTypeP2P
}

func transformStatusFilters(ctx context.Context, statuses []reviewPb.TransactionReviewOrderStatusFilter) []orderPb.GetOrdersForActorRequest_OrderStatusFilter {
	var statusFilters []orderPb.GetOrdersForActorRequest_OrderStatusFilter
	for _, status := range statuses {
		val, ok := orderStatusMap[status]
		if !ok {
			logger.Info(ctx, fmt.Sprintf("unexpected value for filter received: %v", status.String()))
			continue
		}
		statusFilters = append(statusFilters, val)
	}
	return statusFilters
}

func getOffsetFromPageContextReq(req *rpc.PageContextRequest) (int32, error) {
	switch {
	case req.GetBeforeToken() != "":
		res, err := strconv.ParseInt(req.GetBeforeToken(), 10, 32)
		if err != nil {
			return 0, err
		}
		return int32(res), nil
	case req.GetAfterToken() != "":
		res, err := strconv.ParseInt(req.GetAfterToken(), 10, 32)
		if err != nil {
			return 0, err
		}
		return int32(res), nil
	default:
		return 0, nil
	}
}

func getPageContextResponse(req *rpc.PageContextRequest, resultSize int) (*rpc.PageContextResponse, error) {
	var (
		currToken           string
		hasBefore, hasAfter bool
	)
	switch {
	case req.GetBeforeToken() != "":
		currToken = req.GetBeforeToken()
	case req.GetAfterToken() != "":
		currToken = req.GetAfterToken()
	default:
		if resultSize == int(req.GetPageSize()) {
			hasAfter = true
		}
		return &rpc.PageContextResponse{
			BeforeToken: "0",
			HasBefore:   false,
			AfterToken:  strconv.Itoa(int(req.GetPageSize())),
			HasAfter:    hasAfter,
		}, nil
	}

	currPageOffset, err := strconv.Atoi(currToken)
	if err != nil {
		return nil, err
	}

	beforeToken := 0
	if currPageOffset-int(req.GetPageSize()) > 0 {
		beforeToken = currPageOffset - int(req.GetPageSize())
	}

	afterToken := int(2*req.GetPageSize()) + beforeToken
	if currPageOffset == 0 {
		afterToken = int(req.GetPageSize())
	}

	if currPageOffset > 0 {
		hasBefore = true
	}

	if resultSize == int(req.GetPageSize()) {
		hasAfter = true
	}

	return &rpc.PageContextResponse{
		BeforeToken: strconv.FormatUint(uint64(beforeToken), 10),
		HasBefore:   hasBefore,
		AfterToken:  strconv.FormatUint(uint64(afterToken), 10),
		HasAfter:    hasAfter,
	}, nil

}

func (s *Service) buildGetOrdersRequest(ctx context.Context, req *caseManagementPb.GetTransactionDetailsForReviewRequest, actorId string) (*orderPb.GetOrdersForActorRequest, error) {
	var orderReq *orderPb.GetOrdersForActorRequest
	statusFilters := transformStatusFilters(ctx, req.GetFilters().GetStatuses())
	offset, err := getOffsetFromPageContextReq(req.GetPageContext())
	if err != nil {
		return nil, errors.Wrap(err, "invalid page context received")
	}

	orderReq = &orderPb.GetOrdersForActorRequest{
		ActorId:         actorId,
		TransactionType: accountingEntryMap[req.GetFilters().GetAccountingEntry()],
		FieldMask:       []orderPb.OrderFieldMask{orderPb.OrderFieldMask_ALL},
		StatusFilters:   statusFilters,
		SortBy:          req.GetSortBy(),
		FromTime:        req.GetFilters().GetFromTime(),
		ToTime:          req.GetFilters().GetToTime(),
		PageSize:        int32(req.GetPageContext().GetPageSize()),
		Offset:          offset,
	}

	return setDefaultValuesForGetOrdersRequest(orderReq), nil
}

func setDefaultValuesForGetOrdersRequest(req *orderPb.GetOrdersForActorRequest) *orderPb.GetOrdersForActorRequest {
	// setting fields to default value if empty, as order GetOrdersForActor returns internal if these fields are empty
	if req.GetPageSize() == 0 {
		req.PageSize = 10
	}
	if req.GetSortBy() == orderPb.OrderFieldMask_ORDER_FIELD_MASK_UNSPECIFIED {
		req.SortBy = orderPb.OrderFieldMask_CREATED_AT
	}
	if req.GetTransactionType() == orderPb.GetOrdersForActorRequest_TRANSACTION_TYPE_UNSPECIFIED {
		req.TransactionType = orderPb.GetOrdersForActorRequest_BOTH
	}
	if len(req.GetStatusFilters()) == 0 {
		req.StatusFilters = []orderPb.GetOrdersForActorRequest_OrderStatusFilter{
			orderPb.GetOrdersForActorRequest_SUCCESS,
			orderPb.GetOrdersForActorRequest_FAILED,
			orderPb.GetOrdersForActorRequest_MANUAL_INTERVENTION,
			orderPb.GetOrdersForActorRequest_IN_SETTLEMENT,
			orderPb.GetOrdersForActorRequest_IN_SETTLEMENT,
		}
	}

	return req
}

func (s *Service) getOrdersWithTransactions(ctx context.Context, orders []*orderPb.Order) (*orderPb.GetOrdersWithTransactionsResponse, error) {
	var orderIdentifiers []*orderPb.OrderIdentifier
	for _, order := range orders {
		orderIdentifiers = append(orderIdentifiers, &orderPb.OrderIdentifier{
			Identifier: &orderPb.OrderIdentifier_OrderId{
				OrderId: order.GetId(),
			},
		})
	}
	txnResp, err := s.orderClient.GetOrdersWithTransactions(ctx, &orderPb.GetOrdersWithTransactionsRequest{
		OrderIdentifiers: orderIdentifiers,
	})
	if err = epifigrpc.RPCError(txnResp, err); err != nil {
		return nil, errors.Wrap(err, "error in getting orders with transactions")
	}

	return txnResp, nil
}

func (s *Service) getActorIdFromCaseId(ctx context.Context, caseId string) (string, error) {
	caseResp, err := s.caseStore.GetCaseById(ctx, caseId, []*reviewPb.GetCaseOption{
		{
			Option: &reviewPb.GetCaseOption_DataFreshness{
				DataFreshness: cmEnumsPb.DataFreshness_DATA_FRESHNESS_NEAR_REAL_TIME,
			},
		},
	}...)
	if err != nil {
		return "", errors.Wrap(err, "error in get case by id")
	}

	return caseResp.GetActorId(), nil
}

func (s *Service) ListCases(ctx context.Context, req *caseManagementPb.ListCasesRequest) (*caseManagementPb.ListCasesResponse, error) {
	cases, pgCtxResp, err := s.caseStore.ListCases(ctx, req.GetFilters(), req.GetPageContext(), []reviewPb.CaseFieldMask{})

	if err != nil {
		logger.Error(ctx, "getting error in listing cases:", zap.Error(err))
	}
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		return &caseManagementPb.ListCasesResponse{Status: rpc.StatusRecordNotFound()}, nil
	case errors.Is(err, epifierrors.ErrResourceExhausted):
		return &caseManagementPb.ListCasesResponse{Status: rpc.StatusResourceExhausted()}, nil
	case err != nil:
		return &caseManagementPb.ListCasesResponse{Status: rpc.StatusInternal()}, nil
	}

	return &caseManagementPb.ListCasesResponse{
		Status:      rpc.StatusOk(),
		Cases:       cases,
		PageContext: pgCtxResp,
	}, nil
}

//nolint:funlen
func (s *Service) GetReviewDetails(ctx context.Context, req *caseManagementPb.GetReviewDetailsRequest) (
	*caseManagementPb.GetReviewDetailsResponse, error) {
	var (
		caseResp                     *reviewPb.Case
		actions                      []*reviewPb.Action
		alertsWithRule               []*caseManagementPb.AlertWithRuleDetails
		alerts                       []*caseManagementPb.Alert
		alertsErr, alertWithRulesErr error
		aggregatesWithRule           []*caseManagementPb.AlertAggregateForActorWithRule
	)

	caseStoreResp, caseErr := s.caseStore.GetCaseById(ctx, req.GetCaseId(), []*reviewPb.GetCaseOption{
		{
			Option: &reviewPb.GetCaseOption_DataFreshness{
				DataFreshness: cmEnumsPb.DataFreshness_DATA_FRESHNESS_NEAR_REAL_TIME,
			},
		},
	}...)
	if caseErr != nil {
		return s.handleError(ctx, req.GetCaseId(), caseErr, "error getting the case from id: ")
	}
	caseResp = caseStoreResp

	if isFieldMaskPresent(req.GetFieldMasks(), reviewPb.ReviewDetailsFieldMask_REVIEW_DETAILS_FIELD_MASK_ACTIONS) {
		actionsResp, err := s.reviewActionDao.GetByActorId(ctx, caseResp.GetActorId(), []reviewPb.ActionFieldMask{
			reviewPb.ActionFieldMask_ACTION_FIELD_MASK_ALL,
		}, MaxActionsInReviewDetails)
		if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error getting the actions for case: ", zap.Error(err),
				zap.String(logger.CASE_ID, req.GetCaseId()))
			return &caseManagementPb.GetReviewDetailsResponse{Status: rpc.StatusInternal()}, nil
		}
		actions = actionsResp
	}

	if isFieldMaskPresent(req.GetFieldMasks(), reviewPb.ReviewDetailsFieldMask_REVIEW_DETAILS_FIELD_MASK_ALERTS) {
		alerts, alertsErr = s.alertDao.GetByCaseId(ctx, req.GetCaseId())
		if alertsErr != nil {
			return s.handleError(ctx, req.GetCaseId(), alertsErr, "error getting the alerts for case: ")
		}
		alertsWithRule, alertWithRulesErr = s.aggregator.GetAlertsWithRule(ctx, alerts)
		if alertWithRulesErr != nil {
			return s.handleError(ctx, req.GetCaseId(), alertWithRulesErr, "error combining the alerts with rule: ")
		}
	}

	if isFieldMaskPresent(req.GetFieldMasks(), reviewPb.ReviewDetailsFieldMask_REVIEW_DETAILS_FIELD_MASK_ALERT_AGGREGATES) {
		aggregates, err := s.alertDao.AggregateForActor(ctx, caseResp.GetActorId(),
			dao.WithCreatedAtAfter(time.Now().Add(-1*sixMonthDuration)))
		switch {
		case errors.Is(err, epifierrors.ErrRecordNotFound):
			// Do nothing
		case err != nil:
			return s.handleError(ctx, req.GetCaseId(), err, "error getting the alert aggregate for actor: ")
		case err == nil:
			aggregatesWithRule, err = s.aggregator.GetAlertAggregatesWithRule(ctx, aggregates)
			if err != nil {
				return s.handleError(ctx, req.GetCaseId(), err, "error getting the rules for alert aggregates: ")
			}
		}
	}

	return &caseManagementPb.GetReviewDetailsResponse{
		Status:                          rpc.StatusOk(),
		Case:                            caseResp,
		Actions:                         actions,
		Alerts:                          alertsWithRule,
		AlertAggregatesForActorWithRule: aggregatesWithRule,
	}, nil
}

//nolint:dupl
func (s *Service) CreateComment(ctx context.Context, req *caseManagementPb.CreateCommentRequest) (
	*caseManagementPb.CreateCommentResponse, error) {

	_, createCommentErr := s.commentDao.Create(ctx, req.GetComment())
	if createCommentErr != nil {
		if errors.Is(createCommentErr, reviewPb.CommentValidationError{}) {
			return &caseManagementPb.CreateCommentResponse{Status: rpc.StatusInvalidArgument()}, nil
		}
		logger.Error(ctx, "getting error in creating comment:", zap.Error(createCommentErr))
		return &caseManagementPb.CreateCommentResponse{Status: rpc.StatusInternal()}, nil
	}
	return &caseManagementPb.CreateCommentResponse{Status: rpc.StatusOk()}, nil
}

func (s *Service) ListComments(ctx context.Context, req *caseManagementPb.ListCommentsRequest) (
	*caseManagementPb.ListCommentsResponse, error) {

	// For backward compatibility
	if req.GetCommentFilters() == nil && req.GetQuery() != nil {
		req.CommentFilters = &reviewPb.CommentFilters{
			Queries: []*reviewPb.CommentQuery{req.GetQuery()},
		}
	}

	switch {
	case len(req.GetCommentFilters().GetQueries()) == 0 && req.GetCommentFilters().GetActorId() == "":
		return s.getListCommentsFailureResponse(ctx,
			fmt.Errorf("at least one filter is required %w", epifierrors.ErrInvalidArgument))
	case len(req.GetCommentFilters().GetQueries()) > 1:
		return s.getListCommentsFailureResponse(ctx,
			fmt.Errorf("only one comment query is supported %w", epifierrors.ErrInvalidArgument))
	}

	var filteredCommentsList [][]*reviewPb.Comment

	if req.GetCommentFilters().GetQueries() != nil {
		comments, err := s.commentDao.GetByQuery(ctx, req.GetCommentFilters().GetQueries()[0])
		if err != nil {
			return s.getListCommentsFailureResponse(ctx, err)
		}
		filteredCommentsList = append(filteredCommentsList, comments)
	}

	if req.GetCommentFilters().GetActorId() != "" {
		caseIds, err := s.getActorCases(ctx, req.GetCommentFilters().GetActorId())
		if err != nil {
			return s.getListCommentsFailureResponse(ctx, err)
		}
		comments, err := s.commentDao.GetByCaseIds(ctx, caseIds)
		if err != nil {
			return s.getListCommentsFailureResponse(ctx, err)
		}
		filteredCommentsList = append(filteredCommentsList, comments)
	}

	return s.getListCommentsResponse(ctx, intersectionOfLists(filteredCommentsList))
}

func intersectionOfLists[T containsUUID](listOfLists [][]T) []T {
	var common []T
	for i, list := range listOfLists {
		if i == 0 {
			common = list
			continue
		}
		m1 := map[string]T{}
		for _, item := range list {
			m1[item.GetId()] = item
		}
		common = lo.Filter(common, func(item T, index int) bool {
			_, ok := m1[item.GetId()]
			return ok
		})
	}
	return common
}

type containsUUID interface {
	comparable
	GetId() string
}

func (s *Service) getListCommentsResponse(ctx context.Context, comments []*reviewPb.Comment) (
	*caseManagementPb.ListCommentsResponse, error) {
	if len(comments) == 0 {
		return s.getListCommentsFailureResponse(ctx, epifierrors.ErrRecordNotFound)
	}
	return &caseManagementPb.ListCommentsResponse{Status: rpc.StatusOk(), Comments: comments}, nil
}

func (s *Service) getListCommentsFailureResponse(ctx context.Context, err error) (*caseManagementPb.ListCommentsResponse, error) {
	switch {
	case errors.Is(err, epifierrors.ErrInvalidArgument):
		return &caseManagementPb.ListCommentsResponse{Status: rpc.StatusInvalidArgument()}, nil
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		return &caseManagementPb.ListCommentsResponse{Status: rpc.StatusRecordNotFound()}, nil
	default:
		logger.Error(ctx, "error while fetching comments", zap.Error(err))
		return &caseManagementPb.ListCommentsResponse{Status: rpc.StatusInternal()}, nil
	}
}

func (s *Service) getActorCases(ctx context.Context, actorId string) ([]string, error) {
	alerts, err := s.alertDao.GetByActorId(ctx, actorId, []caseManagementPb.AlertFieldMask{
		caseManagementPb.AlertFieldMask_ALERT_FIELD_MASK_ALL}, 100, dao.WithCaseIdPresent())
	if err != nil {
		return nil, fmt.Errorf("failed to fetch alerts %w", err)
	}
	var (
		caseIds []string
	)
	for _, alert := range alerts {
		caseIds = append(caseIds, alert.GetCaseId())
	}
	return lo.Uniq(caseIds), nil
}

func (s *Service) handleError(ctx context.Context, caseId string,
	err error, logString string) (*caseManagementPb.GetReviewDetailsResponse, error) {
	logger.Error(ctx, logString, zap.Error(err), zap.String(logger.CASE_ID, caseId))
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		return &caseManagementPb.GetReviewDetailsResponse{Status: rpc.StatusRecordNotFound()}, nil
	case errors.Is(err, epifierrors.ErrResourceExhausted):
		return &caseManagementPb.GetReviewDetailsResponse{Status: rpc.StatusResourceExhausted()}, nil
	}
	return &caseManagementPb.GetReviewDetailsResponse{Status: rpc.StatusInternal()}, nil
}

func isFieldMaskPresent(masks []reviewPb.ReviewDetailsFieldMask, mask reviewPb.ReviewDetailsFieldMask) bool {
	for _, m := range masks {
		if m == mask || m == reviewPb.ReviewDetailsFieldMask_REVIEW_DETAILS_FIELD_MASK_ALL {
			return true
		}
	}
	return false
}

func (s *Service) getTxnDetailsFromOrders(ctx context.Context, orders []*orderPb.Order, actorId string) ([]*caseManagementPb.TransactionDetail, error) {
	var txnDetails []*caseManagementPb.TransactionDetail
	txnResp, err := s.getOrdersWithTransactions(ctx, orders)
	if err != nil {
		logger.Error(ctx, "error in getting orders with transactions", zap.Error(err))
		return nil, err
	}

	for _, orderWithTxn := range txnResp.GetOrderWithTransactions() {
		accEntry := getAccountingEntry(orderWithTxn.GetOrder().GetFromActorId(), orderWithTxn.GetOrder().GetToActorId(), actorId)
		recType := getReceiverType(orderWithTxn.GetOrder())
		mcDetails, err := s.txnHelper.GetMerchantDetailsFromOrder(ctx, accEntry, orderWithTxn)
		if err != nil {
			logger.Info(ctx, "error in getting merchant details", zap.Error(err))
		}

		txnDetails = append(txnDetails, &caseManagementPb.TransactionDetail{
			OrderWithTransactions: orderWithTxn,
			MerchantDetails:       convertMerchantDetailsToProto(mcDetails),
			ReceiverType:          recType,
			AccountingEntry:       accEntry.String(),
		})
	}

	return txnDetails, nil
}

func convertMerchantDetailsToProto(mcDetails *txnhelper.MerchantDetails) *caseManagementPb.MerchantDetails {
	if mcDetails == nil {
		return nil
	}

	return &caseManagementPb.MerchantDetails{
		MccCode: mcDetails.MccCode,
	}
}

func (s *Service) PerformAction(ctx context.Context, req *caseManagementPb.PerformActionRequest) (*caseManagementPb.PerformActionResponse, error) {
	caseObj, err := s.caseStore.GetCaseById(ctx, req.GetCaseId())
	if err != nil {
		logger.Error(ctx, "getting error in case by id", zap.Error(err))
		return &caseManagementPb.PerformActionResponse{Status: rpc.StatusInternal()}, nil
	}
	// if new action is not allowed, we'll return from here
	// For now we will take action in LIEN even if the action is in progress, because the freeze action will be in progress
	if !caseObj.ShouldAllowNewActions() && req.GetActionType() != reviewPb.ActionType_ACTION_TYPE_ADD_LIEN {
		return &caseManagementPb.PerformActionResponse{Status: rpc.StatusFailedPrecondition()}, nil
	}
	workflowReqID := uuid.New().String()
	// initialise payload for workflow
	payloadBytes, err := protoJson.Marshal(&cmWorkflowPb.PerformReviewActionRequest{
		Case:         caseObj,
		ActionType:   req.GetActionType(),
		Parameters:   req.GetActionParameters(),
		Source:       req.GetSource(),
		AnalystEmail: req.GetAnalystEmail(),
		InitiatedAt:  timestamppb.Now(),
	})
	if err != nil {
		logger.Error(ctx, "error marshalling payload", zap.Error(err))
		return &caseManagementPb.PerformActionResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	// we are not changing the case status to action in progress for add lien action as it will be done by the freeze action
	// this will need to be rmoved when we have a separate option in sehlock only for add lien
	if req.GetActionType() != reviewPb.ActionType_ACTION_TYPE_ADD_LIEN {
		err = s.updateCaseStatusToActionInProgress(ctx, req.GetActionType(), caseObj)
		switch {
		case errors.Is(err, epifierrors.ErrInvalidArgument):
			logger.Error(ctx, "invalid argument passed while updating case status", zap.String(logger.ACTOR_ID_V2, caseObj.GetActorId()), zap.Error(err))
			return &caseManagementPb.PerformActionResponse{Status: rpc.StatusInvalidArgument()}, nil
		case err != nil:
			logger.Error(ctx, "error updating case object", zap.String(logger.ACTOR_ID_V2, caseObj.GetActorId()), zap.Error(err))
			return &caseManagementPb.PerformActionResponse{Status: rpc.StatusInternal()}, nil
		}
	}

	initiateWorkflowResp, err := s.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
		Params: &celestialPb.WorkflowCreationRequestParams{
			ActorId: caseObj.GetActorId(),
			Version: workflowPb.Version_V1,
			Type:    celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_RISK_PROCESS_REVIEW_ACTION),
			Payload: payloadBytes,
			ClientReqId: &workflowPb.ClientReqId{
				Id:     workflowReqID,
				Client: workflowPb.Client_RISK,
			},
			Ownership: commontypes.Ownership_EPIFI_TECH,
		},
	})
	if err = epifigrpc.RPCError(initiateWorkflowResp, err); err != nil {
		logger.Error(ctx, "error while creating a new workflow", zap.String(logger.ACTOR_ID_V2, caseObj.GetActorId()), zap.Error(err))
		// update case status back to in-review in case of workflow creation failure
		s.handleWorkflowCreationFailure(ctx, caseObj)
		return &caseManagementPb.PerformActionResponse{Status: rpc.StatusInternal()}, nil
	}

	return &caseManagementPb.PerformActionResponse{Status: rpc.StatusOk()}, nil
}

func (s *Service) updateCaseStatusToActionInProgress(ctx context.Context, actionType reviewPb.ActionType, caseObj *reviewPb.Case) error {
	// case status need not change to Status_STATUS_REVIEW_ACTION_IN_PROGRESS if it's not a blocking action
	if !actionType.IsBlockingAction() {
		logger.Error(ctx, "not changing status to REVIEW_ACTION_IN_PROGRESS for",
			zap.String(logger.CASE_ID, caseObj.GetId()), zap.String(logger.ACTION_TYPE, actionType.String()))
		return nil
	}
	// update the case object to action in progress in case store
	_, err := s.caseStore.UpdateCase(ctx, &reviewPb.Case{
		Id:     caseObj.GetId(),
		Status: reviewPb.Status_STATUS_REVIEW_ACTION_IN_PROGRESS,
	}, []reviewPb.CaseFieldMask{
		reviewPb.CaseFieldMask_CASE_FIELD_STATUS,
	})
	return err
}

func (s *Service) GetAllowedAnnotations(ctx context.Context, req *caseManagementPb.GetAllowedAnnotationsRequest) (
	*caseManagementPb.GetAllowedAnnotationsResponse, error) {

	resp, respErr := s.allowedAnnotationDao.GetByQuery(ctx, req.GetQuery())

	if respErr == nil {
		return &caseManagementPb.GetAllowedAnnotationsResponse{
			AllowedAnnotations: resp,
			Status:             rpc.StatusOk(),
		}, nil
	}
	if errors.Is(respErr, reviewPb.AllowedAnnotationQueryValidationError{}) {
		return &caseManagementPb.GetAllowedAnnotationsResponse{
			Status: rpc.StatusInvalidArgument(),
		}, nil
	}
	if errors.Is(respErr, epifierrors.ErrRecordNotFound) {
		return &caseManagementPb.GetAllowedAnnotationsResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil
	}
	logger.Error(ctx, "getting error in fetching the allowed annotations:", zap.Error(respErr))
	return &caseManagementPb.GetAllowedAnnotationsResponse{
		Status: rpc.StatusInternal(),
	}, nil
}

func (s *Service) CreateAlerts(ctx context.Context, req *caseManagementPb.CreateAlertsRequest) (*caseManagementPb.CreateAlertsResponse, error) {
	var (
		failureMutex = &sync.Mutex{}
		failures     []*caseManagementPb.CreateAlertsResponse_Failure
	)
	// Add max concurrency limit for processing the alerts
	limit := limiter.NewConcurrencyLimiter(MaxConcurrencyLimitForAlertProcessing)
	for _, a := range req.GetAlerts() {
		alert := a
		_, _ = limit.Execute(func() {
			if alert.GetInitiatedAt() == nil {
				alert.InitiatedAt = timestamppb.Now()
			}
			workflowReqID := uuid.New().String()
			// initialise payload for workflow
			payloadBytes, err := protoJson.Marshal(&cmWorkflowPb.ProcessAlertRequest{
				Alert: alert,
			})
			if err != nil {
				failures = appendFailureWithMutexLock(failureMutex, failures,
					&caseManagementPb.CreateAlertsResponse_Failure{Alert: alert, Reason: ErrMarshallingAlertFailure.Error()})
				logger.Error(ctx, "error marshalling process alert payload", zap.Error(err),
					zap.String(logger.BATCH_NAME, alert.GetBatchName()),
					zap.String(logger.ACTOR_ID_V2, alert.GetActorId()))
			}
			initiateWorkflowResp, err := s.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
				ActorId: alert.GetActorId(),
				Version: workflowPb.Version_V0,
				Type:    workflowPb.Type_RISK_PROCESS_ALERT,
				Payload: payloadBytes,
				ClientReqId: &celestialPb.ClientReqId{
					Id:     workflowReqID,
					Client: workflowPb.Client_RISK,
				},
				Ownership: commontypes.Ownership_EPIFI_TECH,
			})
			if err = epifigrpc.RPCError(initiateWorkflowResp, err); err != nil {
				failures = appendFailureWithMutexLock(failureMutex, failures,
					&caseManagementPb.CreateAlertsResponse_Failure{Alert: alert, Reason: ErrProcessAlertWorkflowFailure.Error()})
				logger.Error(ctx, "error while creating a new workflow",
					zap.String(logger.BATCH_NAME, alert.GetBatchName()),
					zap.String(logger.ACTOR_ID_V2, alert.GetActorId()), zap.Error(err))
			}
		})
	}
	_ = limit.WaitAndClose()
	return &caseManagementPb.CreateAlertsResponse{Status: rpc.StatusOk(), FailureCount: uint32(len(failures)), Failures: failures}, nil
}

// append the failure in thread safe way by taking mutex lock before append
func appendFailureWithMutexLock(failureMutex *sync.Mutex, failures []*caseManagementPb.CreateAlertsResponse_Failure, failure *caseManagementPb.CreateAlertsResponse_Failure) []*caseManagementPb.CreateAlertsResponse_Failure {
	// take mutex lock before appending
	failureMutex.Lock()
	failures = append(failures, failure)
	failureMutex.Unlock()
	return failures
}

//nolint:dupl
func (s *Service) CreateAnnotation(ctx context.Context, request *caseManagementPb.CreateAnnotationRequest) (
	*caseManagementPb.CreateAnnotationResponse, error) {
	_, annotationsErr := s.annotationDao.Create(ctx, request.GetAnnotation())
	if annotationsErr != nil {
		if errors.Is(annotationsErr, reviewPb.AnnotationValidationError{}) {
			return &caseManagementPb.CreateAnnotationResponse{Status: rpc.StatusInvalidArgument()}, nil
		}
		logger.Error(ctx, "getting error in creating annotations:", zap.Error(annotationsErr))
		return &caseManagementPb.CreateAnnotationResponse{Status: rpc.StatusInternal()}, nil
	}
	return &caseManagementPb.CreateAnnotationResponse{Status: rpc.StatusOk()}, nil
}

func (s *Service) ListAnnotations(ctx context.Context, req *caseManagementPb.ListAnnotationsRequest) (
	*caseManagementPb.ListAnnotationsResponse, error) {
	// For backward compatibility
	if req.GetAnnotationFilters() == nil && req.GetQuery() != nil {
		req.AnnotationFilters = &caseManagementPb.AnnotationFilters{
			Queries: []*caseManagementPb.AnnotationQuery{req.GetQuery()},
		}
	}

	switch {
	case len(req.GetAnnotationFilters().GetQueries()) == 0 && req.GetAnnotationFilters().GetActorId() == "":
		return s.getListAnnotationsFailureResponse(ctx,
			fmt.Errorf("at least one filter is required %w", epifierrors.ErrInvalidArgument))
	case len(req.GetAnnotationFilters().GetQueries()) > 1:
		return s.getListAnnotationsFailureResponse(ctx,
			fmt.Errorf("only one annotation query is supported %w", epifierrors.ErrInvalidArgument))
	}

	var filteredAnnotationsList [][]*reviewPb.Annotation

	if req.GetAnnotationFilters().GetQueries() != nil {
		annotations, err := s.annotationDao.GetByQuery(ctx, req.GetAnnotationFilters().GetQueries()[0])
		if err != nil {
			return s.getListAnnotationsFailureResponse(ctx, err)
		}
		filteredAnnotationsList = append(filteredAnnotationsList, annotations)
	}

	if req.GetAnnotationFilters().GetActorId() != "" {
		caseIds, err := s.getActorCases(ctx, req.GetAnnotationFilters().GetActorId())
		if err != nil {
			return s.getListAnnotationsFailureResponse(ctx, err)
		}
		annotations, err := s.annotationDao.GetByCaseIds(ctx, caseIds)
		if err != nil {
			return s.getListAnnotationsFailureResponse(ctx, err)
		}
		filteredAnnotationsList = append(filteredAnnotationsList, annotations)
	}

	return s.getListAnnotationsResponse(ctx, intersectionOfLists(filteredAnnotationsList))
}

func (s *Service) getListAnnotationsResponse(ctx context.Context, annotations []*reviewPb.Annotation) (*caseManagementPb.ListAnnotationsResponse, error) {
	if len(annotations) == 0 {
		return &caseManagementPb.ListAnnotationsResponse{Status: rpc.StatusRecordNotFound()}, nil
	}

	combinedAnnotations, combinedAnnotationsErr := s.aggregator.GetCombinedAnnotations(ctx, annotations)
	if combinedAnnotationsErr != nil {
		logger.Error(ctx, "getting error in combining annotations:", zap.Error(combinedAnnotationsErr))
		return &caseManagementPb.ListAnnotationsResponse{Status: rpc.StatusInternal()}, nil
	}

	return &caseManagementPb.ListAnnotationsResponse{Status: rpc.StatusOk(), Annotations: combinedAnnotations}, nil
}

func (s *Service) getListAnnotationsFailureResponse(ctx context.Context, err error) (*caseManagementPb.ListAnnotationsResponse, error) {
	switch {
	case errors.Is(err, epifierrors.ErrInvalidArgument):
		return &caseManagementPb.ListAnnotationsResponse{Status: rpc.StatusInvalidArgument()}, nil
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		return &caseManagementPb.ListAnnotationsResponse{Status: rpc.StatusRecordNotFound()}, nil
	default:
		logger.Error(ctx, "error while fetching annotations", zap.Error(err))
		return &caseManagementPb.ListAnnotationsResponse{Status: rpc.StatusInternal()}, nil
	}
}

func (s *Service) CreateRule(ctx context.Context, request *caseManagementPb.CreateRuleRequest) (
	*caseManagementPb.CreateRuleResponse, error) {

	if err := request.GetRule().Validate(); err != nil {
		return &caseManagementPb.CreateRuleResponse{Status: rpc.StatusInvalidArgument()}, nil
	}
	if len(request.GetRule().GetExternalId()) == 0 {
		return &caseManagementPb.CreateRuleResponse{Status: rpc.StatusInvalidArgument()}, nil
	}

	syncErr := s.ruleProcessor.SyncForExternalId(ctx, request.GetRule())
	if syncErr == nil {
		return &caseManagementPb.CreateRuleResponse{Status: rpc.StatusOk()}, nil
	}
	logger.Error(ctx, "getting error in syncing rule:", zap.Error(syncErr))
	return &caseManagementPb.CreateRuleResponse{Status: rpc.StatusInternal()}, nil
}

//nolint:funlen
func (s *Service) ListRules(ctx context.Context, request *caseManagementPb.ListRulesRequest) (
	*caseManagementPb.ListRulesResponse, error) {
	if len(request.GetExternalIds()) != 0 {
		rules, rulesErr := s.ruleDao.GetBulkByExternalId(ctx, request.GetExternalIds())
		if rulesErr == nil {
			return &caseManagementPb.ListRulesResponse{Status: rpc.StatusOk(), Rules: rules}, nil
		}
		if errors.Is(rulesErr, epifierrors.ErrInvalidArgument) {
			return &caseManagementPb.ListRulesResponse{Status: rpc.StatusInvalidArgument()}, nil
		}
		if errors.Is(rulesErr, epifierrors.ErrRecordNotFound) {
			return &caseManagementPb.ListRulesResponse{Status: rpc.StatusRecordNotFound()}, nil
		}
		logger.Error(ctx, "getting error in syncing rule:", zap.Error(rulesErr))
		return &caseManagementPb.ListRulesResponse{Status: rpc.StatusInternal()}, nil
	}

	var rules []*caseManagementPb.Rule
	switch request.FilterBy {
	case caseManagementPb.RuleFieldMask_RULE_FIELD_MASK_NAME:
		if request.GetRuleName() == "" {
			return &caseManagementPb.ListRulesResponse{Status: rpc.StatusInvalidArgumentWithDebugMsg("rule name is empty")}, nil
		}
		rule, err := s.ruleDao.GetLatestByName(ctx, request.GetRuleName())
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				return &caseManagementPb.ListRulesResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil
			}
			return &caseManagementPb.ListRulesResponse{Status: rpc.StatusInternal()}, nil
		}
		rules = []*caseManagementPb.Rule{rule}
	case caseManagementPb.RuleFieldMask_RULE_FIELD_MASK_RULE_GROUP:
		if request.GetRuleGroup() == caseManagementPb.RuleGroup_RULE_GROUP_UNSPECIFIED {
			return &caseManagementPb.ListRulesResponse{Status: rpc.StatusInvalidArgumentWithDebugMsg("rule group is unspecified")}, nil
		}
		rulesResp, err := s.ruleDao.GetByRuleGroup(ctx, request.GetRuleGroup())
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				return &caseManagementPb.ListRulesResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil
			}
			return &caseManagementPb.ListRulesResponse{Status: rpc.StatusInternal()}, nil
		}
		rules = rulesResp
	case caseManagementPb.RuleFieldMask_RULE_FIELD_MASK_EXTERNAL_ID:
		if request.GetExternalId() == "" {
			return &caseManagementPb.ListRulesResponse{Status: rpc.StatusInvalidArgumentWithDebugMsg("external id is empty")}, nil
		}
		rulesResp, err := s.ruleDao.GetBulkByExternalId(ctx, []string{request.GetExternalId()})
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				return &caseManagementPb.ListRulesResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil
			}
			return &caseManagementPb.ListRulesResponse{Status: rpc.StatusInternal()}, nil
		}
		rules = rulesResp
	case caseManagementPb.RuleFieldMask_RULE_FIELD_MASK_UNSPECIFIED:
		rulesResp, err := s.ruleDao.GetAll(ctx)
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				return &caseManagementPb.ListRulesResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil
			}
			return &caseManagementPb.ListRulesResponse{Status: rpc.StatusInternal()}, nil
		}
		rules = rulesResp
	default:
		return &caseManagementPb.ListRulesResponse{Status: rpc.StatusInvalidArgumentWithDebugMsg("unsupported rule field mask received")}, nil
	}

	return &caseManagementPb.ListRulesResponse{
		Status: rpc.StatusOk(),
		Rules:  sortRulesByCreatedAt(rules),
	}, nil

}

func sortRulesByCreatedAt(rules []*caseManagementPb.Rule) []*caseManagementPb.Rule {
	sort.SliceStable(rules, func(i, j int) bool {
		return rules[i].GetCreatedAt().AsTime().After(rules[j].GetCreatedAt().AsTime())
	})

	return rules
}

func (s *Service) UpdateRule(ctx context.Context, req *caseManagementPb.UpdateRuleRequest) (*caseManagementPb.UpdateRuleResponse, error) {
	_, err := s.ruleDao.Update(ctx, req.GetRule(), req.GetUpdateMasks())
	if err == nil {
		return &caseManagementPb.UpdateRuleResponse{Status: rpc.StatusOk()}, nil
	}
	logger.Error(ctx, "error updating rule", zap.Error(err), zap.String(logger.RULE_ID, req.GetRule().GetId()))
	if errors.Is(err, epifierrors.ErrInvalidArgument) {
		return &caseManagementPb.UpdateRuleResponse{Status: rpc.StatusInvalidArgument()}, nil
	}
	return &caseManagementPb.UpdateRuleResponse{Status: rpc.StatusInternal()}, nil
}

func (s *Service) CreateAllowedAnnotation(ctx context.Context, req *caseManagementPb.CreateAllowedAnnotationRequest) (*caseManagementPb.CreateAllowedAnnotationResponse, error) {
	_, err := s.allowedAnnotationDao.Create(ctx, req.GetAllowedAnnotation())
	if err != nil {
		logger.Error(ctx, "error in creating allowed annotation entry in dao", zap.Error(err))
		return &caseManagementPb.CreateAllowedAnnotationResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	return &caseManagementPb.CreateAllowedAnnotationResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (s *Service) UpdateCase(ctx context.Context, req *caseManagementPb.UpdateCaseRequest) (*caseManagementPb.UpdateCaseResponse, error) {
	caseStoreResp, err := s.caseStore.UpdateCase(ctx, req.GetCase(), req.GetUpdateMasks())
	if err != nil {
		logger.Error(ctx, "failed to update case", zap.Error(err))
		return &caseManagementPb.UpdateCaseResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	return &caseManagementPb.UpdateCaseResponse{
		Status: rpc.StatusOk(),
		Case:   caseStoreResp,
	}, nil
}

func (s *Service) handleWorkflowCreationFailure(ctx context.Context, caseObj *reviewPb.Case) {
	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		_, err := s.caseStore.UpdateCase(ctx, &reviewPb.Case{
			Id:     caseObj.GetId(),
			Status: reviewPb.Status_STATUS_IN_REVIEW,
		}, []reviewPb.CaseFieldMask{
			reviewPb.CaseFieldMask_CASE_FIELD_STATUS,
		})
		switch {
		case errors.Is(err, epifierrors.ErrInvalidArgument):
			logger.Error(ctx, "invalid argument passed while updating case status", zap.String(logger.ACTOR_ID_V2, caseObj.GetActorId()), zap.Error(err))
		case err != nil:
			logger.Error(ctx, "error updating case object", zap.String(logger.ACTOR_ID_V2, caseObj.GetActorId()), zap.Error(err))
		}
	})
}

func (s *Service) GetPrioritizedCase(ctx context.Context, req *caseManagementPb.GetPrioritizedCaseRequest) (*caseManagementPb.GetPrioritizedCaseResponse, error) {
	// todo: remove this after it is stable
	if len(req.GetReviewTypes()) == 0 {
		logger.Error(ctx, "issue in fetching reviewtype", zap.Any("reviewtype", string(req.GetReviewType())), zap.Any("reviewtypes", req.GetReviewTypes()))
		if req.GetReviewType() == reviewPb.ReviewType_REVIEW_TYPE_UNSPECIFIED {
			return &caseManagementPb.GetPrioritizedCaseResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		}
		req.ReviewTypes = append(req.GetReviewTypes(), req.GetReviewType())
	}
	// TODO: this is an extra computation, this computation should be moved to ReloadCases() in priority_case_store.go
	// TODO: also update snake case -> camel cases whereever necessary (variabke naming)
	cases, _, err := s.caseAllocator.GetEligibleCases(ctx, req.GetAnalystEmail(), req.GetReviewTypes(), 1)
	if err != nil {
		logger.Error(ctx, "failed to fetch eligible cases", zap.String(logger.AGENT_EMAIL, req.GetAnalystEmail()),
			zap.String(logger.REVIEW_TYPE, req.GetReviewType().String()), zap.Error(err))
	}
	if len(cases) == 0 {
		logger.Error(ctx, "no cases found for the following fields while fetching cases from crm", zap.String(logger.REVIEW_TYPE, req.GetReviewType().String()), zap.Any("review types", req.ReviewTypes))
	}
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound) || len(cases) == 0:
		return &caseManagementPb.GetPrioritizedCaseResponse{Status: rpc.StatusRecordNotFound()}, nil
	case errors.Is(err, epifierrors.ErrPermissionDenied):
		return &caseManagementPb.GetPrioritizedCaseResponse{Status: rpc.StatusPermissionDenied()}, nil
	case errors.Is(err, epifierrors.ErrInvalidArgument):
		return &caseManagementPb.GetPrioritizedCaseResponse{Status: rpc.StatusInvalidArgument()}, nil
	case errors.Is(err, epifierrors.ErrTransient):
		return &caseManagementPb.GetPrioritizedCaseResponse{Status: rpc.StatusTransientFailure()}, nil
	case err != nil:
		return &caseManagementPb.GetPrioritizedCaseResponse{Status: rpc.StatusInternal()}, nil
	}

	attemptedCaseId := cases[0].GetId()

	cases, err = s.caseAllocator.AssignCases(ctx, req.GetAnalystEmail(), []string{cases[0].GetId()}, []string{cases[0].GetModelSelected()})

	if len(cases) == 0 {
		logger.Error(ctx, fmt.Sprintf("failed to assign cases: %s", req.ReviewTypes),
			zap.String(logger.AGENT_EMAIL, req.GetAnalystEmail()),
			zap.Any("review types", req.ReviewTypes),
			zap.Error(err),
			zap.Bool("has_error", err != nil),
			zap.String("attempted_case_id", attemptedCaseId))
	}
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound) || len(cases) == 0:
		return &caseManagementPb.GetPrioritizedCaseResponse{Status: rpc.StatusRecordNotFound()}, nil
	case err != nil:
		return &caseManagementPb.GetPrioritizedCaseResponse{Status: rpc.StatusInternal()}, nil
	}
	// TODO: remove error logs once freshdesk is populated with enough cases, and we are not getting error for cases not found
	logger.Info(ctx, "Assigned case to analyst", zap.Any("caseId", cases[0].GetId()), zap.String(logger.AGENT_EMAIL, req.GetAnalystEmail()), zap.String(logger.REVIEW_TYPE, req.GetReviewType().String()), zap.Error(err), zap.Any("cases_length", len(cases)), zap.Any("model_selected", cases[0].GetModelSelected()), zap.Any("review_types", req.ReviewTypes))
	return &caseManagementPb.GetPrioritizedCaseResponse{Status: rpc.StatusOk(), Case: cases[0]}, nil
}

func (s *Service) ListSortedCases(ctx context.Context, req *caseManagementPb.ListSortedCasesRequest) (*caseManagementPb.ListSortedCasesResponse, error) {
	cases, pgCtxResp, err := s.caseStore.ListSortedCases(ctx, &casestore.ListSortedCasesRequest{
		Filters:            req.GetFilters(),
		SortBy:             req.GetSortBy(),
		PageContextRequest: req.GetPageContextRequest(),
		OptionalFields:     nil,
	})

	if err != nil {
		logger.Error(ctx, "getting error in listing sorted cases:", zap.Error(err))
	}
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		return &caseManagementPb.ListSortedCasesResponse{Status: rpc.StatusRecordNotFound()}, nil
	case errors.Is(err, epifierrors.ErrResourceExhausted):
		return &caseManagementPb.ListSortedCasesResponse{Status: rpc.StatusResourceExhausted()}, nil
	case err != nil:
		return &caseManagementPb.ListSortedCasesResponse{Status: rpc.StatusInternal()}, nil
	}

	return &caseManagementPb.ListSortedCasesResponse{
		Status:              rpc.StatusOk(),
		Cases:               cases,
		PageContextResponse: pgCtxResp,
	}, nil
}

func (s *Service) GetFiUserRelationship(ctx context.Context, req *caseManagementPb.GetFiUserRelationshipRequest) (
	*caseManagementPb.GetFiUserRelationshipResponse, error) {
	existingProduct, existingProductErr := s.fiUserRelationship.GetFiUserRelationshipUI(ctx, req.GetActorId())
	if existingProductErr == nil {
		return &caseManagementPb.GetFiUserRelationshipResponse{Status: rpc.StatusOk(), FiUserRelationship: existingProduct}, nil
	}
	logger.Error(ctx, "could not find the user's product relationship with fi", zap.Error(existingProductErr))
	if errors.Is(existingProductErr, epifierrors.ErrInvalidArgument) {
		return &caseManagementPb.GetFiUserRelationshipResponse{
			Status: rpc.StatusInvalidArgument(),
		}, nil
	}
	return &caseManagementPb.GetFiUserRelationshipResponse{
		Status: rpc.StatusInternal(),
	}, nil
}

func (s *Service) GetForm(ctx context.Context, req *caseManagementPb.GetFormRequest) (*caseManagementPb.GetFormResponse, error) {
	formObj, err := s.formHandler.GetForm(ctx, req.GetFormId(), []formPb.ExtendedFormFieldMask{
		formPb.ExtendedFormFieldMask_EXTENDED_FORM_FIELD_MASK_FORM,
		formPb.ExtendedFormFieldMask_EXTENDED_FORM_FIELD_MASK_QUESTIONS,
	})
	if err == nil {
		return &caseManagementPb.GetFormResponse{
			Status:    rpc.StatusOk(),
			Form:      formObj.GetForm(),
			Questions: formObj.GetQuestions(),
		}, nil
	}

	logger.Error(ctx, "failed to fetch form", zap.Error(err))
	switch {
	case errors.Is(err, formErrors.ErrFormNotFound):
		return &caseManagementPb.GetFormResponse{Status: rpc.StatusRecordNotFound()}, nil
	default:
		return &caseManagementPb.GetFormResponse{Status: rpc.StatusInternal()}, nil
	}
}

func (s *Service) SubmitForm(ctx context.Context, req *caseManagementPb.SubmitFormRequest) (*caseManagementPb.SubmitFormResponse, error) {
	err := s.formHandler.ValidateSubmission(ctx, req.GetFormId(), req.GetResponses())

	if err != nil {
		return s.handleValidateFormSubmissionFailure(ctx, err), nil
	}

	formObj, err := s.formHandler.GetForm(ctx, req.GetFormId(), []formPb.ExtendedFormFieldMask{
		formPb.ExtendedFormFieldMask_EXTENDED_FORM_FIELD_MASK_FORM,
	})
	if err != nil {
		logger.Error(ctx, "failed to fetch form", zap.Error(err))
		return &caseManagementPb.SubmitFormResponse{Status: rpc.StatusInternal()}, nil
	}
	if formObj.GetForm().GetStatus() == formPb.Status_STATUS_PROCESSING_SUBMISSION {
		return &caseManagementPb.SubmitFormResponse{Status: rpc.NewStatusWithoutDebug(
			uint32(caseManagementPb.SubmitFormResponse_ALREADY_SUBMITTED),
			caseManagementPb.SubmitFormResponse_ALREADY_SUBMITTED.String())}, nil
	}

	err = s.frmPgdbTxnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		return s.updateFormStatusAndPublishSubmissionEvent(ctx, req)
	})

	if err != nil {
		logger.Error(ctx, "failed to update form and publish event", zap.Error(err))
		return &caseManagementPb.SubmitFormResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	return &caseManagementPb.SubmitFormResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (s *Service) handleValidateFormSubmissionFailure(ctx context.Context, err error) *caseManagementPb.SubmitFormResponse {
	logger.Error(ctx, "failed to validate submission", zap.Error(err))

	switch {
	case errors.Is(err, formErrors.ErrFormNotFound):
		return &caseManagementPb.SubmitFormResponse{Status: rpc.StatusRecordNotFound()}
	case errors.Is(err, formErrors.ErrFormExpired):
		return &caseManagementPb.SubmitFormResponse{Status: rpc.NewStatusWithoutDebug(
			uint32(caseManagementPb.SubmitFormResponse_EXPIRED),
			caseManagementPb.SubmitFormResponse_EXPIRED.String())}
	case errors.Is(err, formErrors.ErrFormAlreadySubmitted):
		return &caseManagementPb.SubmitFormResponse{Status: rpc.NewStatusWithoutDebug(uint32(
			caseManagementPb.SubmitFormResponse_ALREADY_SUBMITTED),
			caseManagementPb.SubmitFormResponse_ALREADY_SUBMITTED.String())}
	case errors.Is(err, formErrors.ErrInvalidResponse):
		return &caseManagementPb.SubmitFormResponse{Status: rpc.StatusInvalidArgument()}
	default:
		return &caseManagementPb.SubmitFormResponse{Status: rpc.StatusInternal()}
	}
}

func (s *Service) ListForms(ctx context.Context, req *caseManagementPb.ListFormsRequest) (*caseManagementPb.ListFormsResponse, error) {
	var (
		extendedForms []*formPb.ExtendedForm
		formsMux      = sync.Mutex{}
	)

	forms, getFormsErr := s.getFormsForFilters(ctx, req.GetFilters())
	if getFormsErr != nil && !errors.Is(getFormsErr, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "failed to fetch forms", zap.Error(getFormsErr))
	}
	switch {
	case errors.Is(getFormsErr, epifierrors.ErrRecordNotFound):
		return &caseManagementPb.ListFormsResponse{Status: rpc.StatusRecordNotFound()}, nil
	case errors.Is(getFormsErr, epifierrors.ErrInvalidArgument):
		return &caseManagementPb.ListFormsResponse{Status: rpc.StatusInvalidArgument()}, nil
	case getFormsErr != nil:
		return &caseManagementPb.ListFormsResponse{Status: rpc.StatusInternal()}, nil
	}

	errGrp, grpCtx := errgroup.WithContext(ctx)
	for _, unsafeForm := range forms {
		formObj := unsafeForm
		errGrp.Go(func() error {
			extendedForm, err := s.formHandler.GetForm(grpCtx, formObj.GetId(), req.GetFieldMasks())
			if err != nil {
				return fmt.Errorf("failed to fetch extended form for form id: %s %w",
					formObj.GetId(), err)
			}
			formsMux.Lock()
			extendedForms = append(extendedForms, extendedForm)
			formsMux.Unlock()
			return nil
		})
	}

	if err := errGrp.Wait(); err != nil {
		logger.Error(ctx, "failed to fetch extended forms", zap.Error(err))
		return &caseManagementPb.ListFormsResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	return &caseManagementPb.ListFormsResponse{
		Forms:  extendedForms,
		Status: rpc.StatusOk(),
	}, nil
}

func (s *Service) updateFormStatusAndPublishSubmissionEvent(ctx context.Context, req *caseManagementPb.SubmitFormRequest) error {
	err := s.formDao.Update(ctx, &formPb.Form{
		Id:     req.GetFormId(),
		Status: formPb.Status_STATUS_PROCESSING_SUBMISSION,
	}, []formPb.FormFieldMask{formPb.FormFieldMask_FORM_FIELD_MASK_STATUS})
	if err != nil {
		return fmt.Errorf("failed to update form status to processing submission %w", err)
	}

	for _, response := range req.GetResponses() {
		response.CreatedAt = timestamppb.Now()
	}

	messageId, err := s.formSubmissionEventPublisher.Publish(ctx, &caseManagementPb.FormSubmissionEvent{
		FormId:            req.GetFormId(),
		QuestionResponses: req.GetResponses(),
	})
	if err != nil {
		return fmt.Errorf("failed to publish form submission event %w", err)
	}
	logger.Debug(ctx, "form submission event published", zap.String(logger.MESSAGE_ID, messageId),
		zap.String(logger.FORM_ID, req.GetFormId()))
	return nil
}

func (s *Service) getFormsForFilters(ctx context.Context, filters *formPb.FormFilters) ([]*formPb.Form, error) {
	var (
		forms []*formPb.Form
		err   error
	)

	switch v := filters.GetIdentifier().(type) {
	case *formPb.FormFilters_CaseId:
		forms, err = s.formDao.Get(ctx, formPb.FormFieldMask_FORM_FIELD_MASK_CASE_ID,
			&structPb.Value{Kind: &structPb.Value_StringValue{StringValue: v.CaseId}}, 0)
	case *formPb.FormFilters_ActorId:
		forms, err = s.formDao.Get(ctx, formPb.FormFieldMask_FORM_FIELD_MASK_ACTOR_ID,
			&structPb.Value{Kind: &structPb.Value_StringValue{StringValue: v.ActorId}}, 0)
	default:
		logger.Error(ctx, "invalid identifier", zap.Any(logger.IDENTIFIER_VALUE, v))
		return nil, fmt.Errorf("invalid identifier %w", epifierrors.ErrInvalidArgument)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to fetch forms %w", err)
	}

	return forms, nil
}

func (s *Service) GetAlerts(ctx context.Context, req *caseManagementPb.GetAlertsRequest) (*caseManagementPb.GetAlertsResponse, error) {
	alerts, alertsErr := s.alertDao.GetByActorId(ctx, req.GetActorId(),
		[]caseManagementPb.AlertFieldMask{caseManagementPb.AlertFieldMask_ALERT_FIELD_MASK_ALL}, int(req.GetLimit()))
	if alertsErr != nil {
		if errors.Is(alertsErr, epifierrors.ErrRecordNotFound) {
			return &caseManagementPb.GetAlertsResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "error while fetching alerts for the actor", zap.Error(alertsErr),
			zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &caseManagementPb.GetAlertsResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	alertsWithRule, alertWithRulesErr := s.aggregator.GetAlertsWithRule(ctx, alerts)
	if alertWithRulesErr != nil {
		logger.Error(ctx, "error while fetching rule details for alerts", zap.Error(alertWithRulesErr),
			zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &caseManagementPb.GetAlertsResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	return &caseManagementPb.GetAlertsResponse{
		Status: rpc.StatusOk(),
		Alerts: alertsWithRule,
	}, nil
}

func (s *Service) GetAllTags(ctx context.Context, req *caseManagementPb.GetAllTagsRequest) (*caseManagementPb.GetAllTagsResponse, error) {
	tags := s.genConf.RuleTags().ToStringArray()
	if len(tags) == 0 {
		return &caseManagementPb.GetAllTagsResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil
	}
	return &caseManagementPb.GetAllTagsResponse{
		Status: rpc.StatusOk(),
		Tags:   tags,
	}, nil
}

func (s *Service) CreateAnnotations(ctx context.Context, req *caseManagementPb.CreateAnnotationsRequest) (*caseManagementPb.CreateAnnotationsResponse, error) {
	_, err := s.annotationDao.BulkCreate(ctx, req.GetAnnotations())
	if err != nil {
		logger.Error(ctx, "failed to create annotations", zap.Error(err))
	}
	switch {
	case errors.Is(err, epifierrors.ErrInvalidArgument):
		return &caseManagementPb.CreateAnnotationsResponse{
			Status: rpc.StatusInvalidArgument(),
		}, nil
	case err != nil:
		return &caseManagementPb.CreateAnnotationsResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	return &caseManagementPb.CreateAnnotationsResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (s *Service) ListAllowedAnnotations(ctx context.Context, req *caseManagementPb.ListAllowedAnnotationsRequest) (
	*caseManagementPb.ListAllowedAnnotationsResponse, error) {
	switch v := req.GetFilters().GetFilter().(type) {
	case *reviewPb.AllowedAnnotationFilters_UiElement:
		annotations, err := s.fetchUIElementAllowedAnnotations(ctx, v.UiElement)
		if err != nil {
			logger.Error(ctx, "failed to fetch allowed annotations for ui element",
				zap.Any(logger.ELEMENT_ID, v.UiElement), zap.Error(err))
		}
		switch {
		case errors.Is(err, epifierrors.ErrRecordNotFound):
			return &caseManagementPb.ListAllowedAnnotationsResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		case err != nil:
			return &caseManagementPb.ListAllowedAnnotationsResponse{
				Status: rpc.StatusInternal(),
			}, nil
		}
		return &caseManagementPb.ListAllowedAnnotationsResponse{
			Status: rpc.StatusOk(),
			Response: &caseManagementPb.ListAllowedAnnotationsResponse_UiElementAllowedAnnotations{
				UiElementAllowedAnnotations: annotations,
			},
		}, nil
	default:
		logger.Error(ctx, "invalid filter", zap.Any(logger.FILTERS, req.GetFilters().GetFilter()))
		return &caseManagementPb.ListAllowedAnnotationsResponse{Status: rpc.StatusInternal()}, nil
	}
}

func (s *Service) fetchUIElementAllowedAnnotations(ctx context.Context, uiElement *reviewPb.UIElement) (
	*reviewPb.UIElementAllowedAnnotations, error) {
	mappings, err := s.uiElementAnnotationTypeMappingDao.GetByUIElement(ctx, uiElement)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch ui element annotation type mappings %w", err)
	}

	var typesIds []string
	lo.ForEach(mappings, func(item *reviewPb.UIElementAnnotationTypeMapping, _ int) {
		typesIds = append(typesIds, item.GetAnnotationTypeId())
	})
	typesIds = lo.Uniq(typesIds)

	annotationTypes, err := s.annotationTypeDao.BulkGetById(ctx, typesIds)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch annotation types by ids %w", err)
	}

	allowedAnnotations, err := s.allowedAnnotationDao.GetBulkByAnnotationTypeId(ctx, typesIds)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch allowed annotations by type ids %w", err)
	}

	var resp []*reviewPb.UIElementAllowedAnnotations_AnnotationTypeDetails
	for _, mapping := range mappings {
		annotationType, ok := lo.Find(annotationTypes, func(item *reviewPb.AllowedAnnotationType) bool {
			return item.GetId() == mapping.GetAnnotationTypeId()
		})
		if !ok {
			return nil, fmt.Errorf("annotation type not found for id %s %w",
				mapping.GetAnnotationTypeId(), epifierrors.ErrRecordNotFound)
		}

		annotations := lo.Filter(allowedAnnotations, func(item *reviewPb.AllowedAnnotation, _ int) bool {
			return item.GetAnnotationTypeId() == mapping.GetAnnotationTypeId()
		})

		resp = append(resp, &reviewPb.UIElementAllowedAnnotations_AnnotationTypeDetails{
			Mapping: mapping,
			TypeWithValues: &reviewPb.AnnotationTypeWithValues{
				Type:               annotationType,
				AllowedAnnotations: annotations,
			},
		})
	}
	return &reviewPb.UIElementAllowedAnnotations{AnnotationTypesDetails: resp}, nil
}

func (s *Service) GetFormsForActor(ctx context.Context, req *caseManagementPb.GetFormsForActorRequest) (*caseManagementPb.GetFormsForActorResponse, error) {
	forms, err := s.formHandler.GetFormsForActor(ctx, req.GetActorId(), int(req.GetLimit()))
	if err == nil {
		return &caseManagementPb.GetFormsForActorResponse{
			Status: rpc.StatusOk(),
			Form:   forms,
		}, nil
	}

	logger.Error(ctx, "failed to fetch form by ActorId", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		return &caseManagementPb.GetFormsForActorResponse{Status: rpc.StatusRecordNotFound()}, nil
	default:
		return &caseManagementPb.GetFormsForActorResponse{Status: rpc.StatusInternal()}, nil
	}
}

func (s *Service) CreateSuggestedActionForRule(ctx context.Context, req *caseManagementPb.CreateSuggestedActionForRuleRequest) (*caseManagementPb.CreateSuggestedActionForRuleResponse, error) {

	suggestedAction, err := s.suggestedActionDao.Create(ctx, &caseManagementPb.SuggestedAction{
		RuleId:              req.GetRuleId(),
		SuggestedActionType: req.GetSuggestedActionType(),
		ActionParameters:    req.GetActionParameters(),
	})
	if err != nil {
		logger.Error(ctx, "failed to create suggested action", zap.Error(err))
		return &caseManagementPb.CreateSuggestedActionForRuleResponse{Status: rpc.StatusInternal()}, nil
	}

	return &caseManagementPb.CreateSuggestedActionForRuleResponse{
		Status:          rpc.StatusOk(),
		SuggestedAction: suggestedAction,
	}, nil

}

func (s *Service) CreateRuleReviewTypeMapping(ctx context.Context, req *caseManagementPb.CreateRuleReviewTypeMappingRequest) (*caseManagementPb.CreateRuleReviewTypeMappingResponse, error) {
	_, err := s.ruleReviewTypeMappingsDao.Create(ctx, &caseManagementPb.RuleReviewTypeMapping{
		RuleId:     req.GetRuleId(),
		ReviewType: req.GetReviewType(),
	})
	if err != nil {
		logger.Error(ctx, "failed to create rule review type mapping", zap.Error(err))
		return &caseManagementPb.CreateRuleReviewTypeMappingResponse{Status: rpc.StatusInternal()}, nil
	}
	return &caseManagementPb.CreateRuleReviewTypeMappingResponse{
		Status: rpc.StatusOk(),
	}, nil
}

// GetRiskTransactionAggregatedMetrics returns aggregated transaction analytics metrics for an actor
func (s *Service) GetRiskTransactionAggregatedMetrics(ctx context.Context, req *caseManagementPb.GetRiskTransactionAggregatedMetricsRequest) (*caseManagementPb.GetRiskTransactionAggregatedMetricsResponse, error) {
	if req.GetActorId() == "" {
		logger.Error(ctx, "actor_id is required for GetRiskTransactionAggregatedMetrics")
		return &caseManagementPb.GetRiskTransactionAggregatedMetricsResponse{
			Status: rpc.StatusInvalidArgument(),
		}, nil
	}

	// Default to monthly aggregation if not specified
	aggregateOption := req.GetAggregateOption()
	if aggregateOption == caseManagementPb.AggregateOption_AGGREGATE_OPTION_UNSPECIFIED {
		aggregateOption = caseManagementPb.AggregateOption_AGGREGATE_OPTION_MONTH_OVER_MONTH
	}

	logger.Debug(
		ctx,
		"GetRiskTransactionAggregatedMetrics started",
		zap.String("actor_id", req.GetActorId()),
		zap.String("aggregate_option", aggregateOption.String()))

	// Execute all analytics queries concurrently for better performance
	var (
		creditCounts                    []*caseManagementPb.TimeAggregatedCount
		lifetimeP2PCreditCounterparties []*caseManagementPb.TimeAggregatedCount
		credits25kPlus                  []*caseManagementPb.TimeAggregatedCount
		errs                            = make([]error, 3)
	)

	var wg waitgroup.SafeWaitGroup
	_ = wg.Add(3)

	// Get credit counts
	goroutine.RunWithCtx(ctx, func(ctx context.Context) {
		defer wg.Done()
		result, err := s.riskAnalyticsDao.GetCreditCounts(ctx, req.GetActorId(), aggregateOption)
		if err != nil {
			logger.Error(ctx, "failed to get credit counts", zap.Error(err))
			errs[0] = err
			return
		}
		creditCounts = result
	})

	// Get lifetime new P2P credit counterparties
	goroutine.RunWithCtx(ctx, func(ctx context.Context) {
		defer wg.Done()
		result, err := s.riskAnalyticsDao.GetLifetimeNewP2PCreditCounterparties(ctx, req.GetActorId(), aggregateOption)
		if err != nil {
			logger.Error(ctx, "failed to get lifetime new P2P credit counterparties", zap.Error(err))
			errs[1] = err
			return
		}
		lifetimeP2PCreditCounterparties = result
	})

	// Get credits greater than 25k
	goroutine.RunWithCtx(ctx, func(ctx context.Context) {
		defer wg.Done()
		result, err := s.riskAnalyticsDao.GetCredits25kPlus(ctx, req.GetActorId(), aggregateOption)
		if err != nil {
			logger.Error(ctx, "failed to get credits 25k plus", zap.Error(err))
			errs[2] = err
			return
		}
		credits25kPlus = result
	})

	// Wait for all queries to complete
	wg.Wait()

	// Check for any errors
	for i, err := range errs {
		if err != nil {
			logger.Error(ctx, fmt.Sprintf("analytics query %d failed", i), zap.Error(err))
			return &caseManagementPb.GetRiskTransactionAggregatedMetricsResponse{
				Status: rpc.StatusInternal(),
			}, nil
		}
	}

	// Build the response
	response := &caseManagementPb.GetRiskTransactionAggregatedMetricsResponse{
		Status: rpc.StatusOk(),
		Metrics: &caseManagementPb.RiskTransactionAggregatedMetrics{
			CreditCounts:                    creditCounts,
			LifetimeP2PCreditCounterparties: lifetimeP2PCreditCounterparties,
			Credits_25KPlus:                 credits25kPlus,
			AggregationType:                 aggregateOption,
		},
	}

	logger.Info(ctx, "GetRiskTransactionAggregatedMetrics completed successfully",
		zap.String("actor_id", req.GetActorId()),
		zap.String("aggregate_option", aggregateOption.String()),
		zap.Int("credit_counts_periods", len(creditCounts)),
		zap.Int("lifetime_p2p_periods", len(lifetimeP2PCreditCounterparties)),
		zap.Int("credits_25k_plus_periods", len(credits25kPlus)))

	return response, nil
}
