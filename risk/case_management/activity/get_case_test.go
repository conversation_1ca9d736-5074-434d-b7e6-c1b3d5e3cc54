package activity

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	activityPb "github.com/epifi/gamma/api/risk/case_management/activity"
	"github.com/epifi/gamma/api/risk/case_management/review"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	riskNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/risk"
)

func TestProcessor_GetCase(t *testing.T) {
	var (
		caseId = "caseId"
	)
	type args struct {
		ctx context.Context
		req *activityPb.GetCaseRequest
	}
	tests := []struct {
		name      string
		mocks     func(mock *mockedDependencies, args args)
		args      args
		want      *activityPb.GetCaseResponse
		wantErr   bool
		assertErr func(err error) bool
	}{
		{
			name: "failed to get case when no case could be found",
			mocks: func(mock *mockedDependencies, args args) {
				mock.caseStore.EXPECT().GetCaseById(gomock.Any(), caseId).Return(nil, epifierrors.ErrRecordNotFound)
			},
			args: args{
				ctx: context.Background(),
				req: &activityPb.GetCaseRequest{CaseId: caseId},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "failed to get case when invalid argument",
			mocks: func(mock *mockedDependencies, args args) {
				mock.caseStore.EXPECT().GetCaseById(gomock.Any(), caseId).Return(nil, epifierrors.ErrInvalidArgument)
			},
			args: args{
				ctx: context.Background(),
				req: &activityPb.GetCaseRequest{CaseId: caseId},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "failed to fetch the case from case store",
			mocks: func(mock *mockedDependencies, args args) {
				mock.caseStore.EXPECT().GetCaseById(gomock.Any(), caseId).Return(nil, errors.New("some error"))
			},
			args: args{
				ctx: context.Background(),
				req: &activityPb.GetCaseRequest{CaseId: caseId},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "success",
			mocks: func(mock *mockedDependencies, args args) {
				mock.caseStore.EXPECT().GetCaseById(gomock.Any(), caseId).Return(&review.Case{}, nil)
			},
			args: args{
				ctx: context.Background(),
				req: &activityPb.GetCaseRequest{CaseId: caseId},
			},
			want: &activityPb.GetCaseResponse{
				Case: &review.Case{},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			proc, mockedDeps, assertTest := newProcessorWithMocks(t)
			defer assertTest()
			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(proc)
			if tt.mocks != nil {
				tt.mocks(mockedDeps, tt.args)
			}
			got := &activityPb.GetCaseResponse{}
			result, err := env.ExecuteActivity(riskNs.GetCase, tt.args.req)
			if result != nil && result.HasValue() {
				getErr := result.Get(&got)
				if getErr != nil {
					t.Errorf("GetCase() error = %v failed to fetch type value from convertible", err)
					return
				}
			}
			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("GetCase() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("GetCase() error = %v assertion failed", err)
				return
			case tt.want != nil && cmp.Diff(got, tt.want, protocmp.Transform()) != "":
				t.Errorf("GetCase() got = %v, want %v", got, tt.want)
			}
		})
	}
}
