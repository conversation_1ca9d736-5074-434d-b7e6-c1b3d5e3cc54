package casestore

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/epifi/gamma/api/vendorgateway/risk"

	caseManagementPb "github.com/epifi/gamma/api/risk/case_management"
	"github.com/epifi/gamma/api/risk/case_management/enums"
	reviewPb "github.com/epifi/gamma/api/risk/case_management/review"
	mock "github.com/epifi/gamma/risk/case_management/caseanalyser/mocks"
)

type caseBuilderMockedDependencies struct {
	caseAnalyser *mock.MockICaseAnalyser
}

func newCaseBuilderWithMocks(t *testing.T) (*CaseBuilder, *caseBuilderMockedDependencies) {
	ctr := gomock.NewController(t)
	md := &caseBuilderMockedDependencies{
		caseAnalyser: mock.NewMockICaseAnalyser(ctr),
	}

	svc := &CaseBuilder{
		caseAnalyser: md.caseAnalyser,
	}
	return svc, md
}

var (
	alertFixtureWithEntityTypeMissing = &caseManagementPb.Alert{
		Id:         "test-alert-1",
		AccountId:  "test-account-1",
		ActorId:    "test-actor-1",
		EntityType: enums.EntityType_ENTITY_TYPE_UNSPECIFIED,
		EntityId:   "test-txn-id",
	}
	alertFixture1 = &caseManagementPb.Alert{
		Id:         "test-alert-1",
		AccountId:  "test-account-1",
		ActorId:    "test-actor-1",
		EntityType: enums.EntityType_ENTITY_TYPE_TRANSACTION,
		EntityId:   "test-txn-id",
		BatchName:  "test-batch-1",
	}
	alertFixture2 = &caseManagementPb.Alert{
		Id:         "test-alert-2",
		AccountId:  "test-account-2",
		ActorId:    "test-actor-2",
		EntityType: enums.EntityType_ENTITY_TYPE_TRANSACTION,
		EntityId:   "test-txn-id",
		BatchName:  "test-batch-2",
		RuleId:     "rule-id-4",
		CaseId:     "caseId",
	}
	alertFixture3 = &caseManagementPb.Alert{
		Id:         "test-alert-3",
		AccountId:  "test-account-3",
		ActorId:    "test-actor-2",
		EntityType: enums.EntityType_ENTITY_TYPE_TRANSACTION,
		EntityId:   "test-txn-id",
		BatchName:  "test-batch-3",
		RuleId:     "rule-id-3",
		CaseId:     "caseId",
	}
	RuleWithScoreAboveCriticalPriorityThreshold = &caseManagementPb.Rule{
		Id:              "test-rule-1",
		Name:            "test-rule",
		ConfidenceScore: CriticalPriorityThreshold + 1,
		RuleGroup:       caseManagementPb.RuleGroup_RULE_GROUP_CRYPTO,
	}
	RuleWithScoreAboveHighPriorityThreshold = &caseManagementPb.Rule{
		Id:              "test-rule-1",
		Name:            "test-rule",
		ConfidenceScore: HighPriorityThreshold + 1,
		RuleGroup:       caseManagementPb.RuleGroup_RULE_GROUP_CRYPTO,
	}
	RuleWithScoreAboveMediumPriorityThreshold = &caseManagementPb.Rule{
		Id:              "test-rule-1",
		Name:            "test-rule",
		ConfidenceScore: MediumPriorityThreshold + 1,
		RuleGroup:       caseManagementPb.RuleGroup_RULE_GROUP_CRYPTO,
	}
	RuleWithScoreAboveLowPriorityThreshold = &caseManagementPb.Rule{
		Id:              "test-rule-1",
		Name:            "test-rule",
		ConfidenceScore: 10,
		RuleGroup:       caseManagementPb.RuleGroup_RULE_GROUP_CRYPTO,
	}
	RuleWithScoreAboveLowPriorityThresholdAndLargeRuleName = &caseManagementPb.Rule{
		Id:              "test-rule-1",
		Name:            "Payee activity exceeded - FullKYcd",
		ConfidenceScore: 10,
		RuleGroup:       caseManagementPb.RuleGroup_RULE_GROUP_CRYPTO,
	}
	CasePrioAlertTags = []string{"V1_50", "V2_60", "Case_priority_v1"}
)

func TestCaseBuilder_BuildNewCaseFromAlertAndRule(t *testing.T) {
	type args struct {
		ctx           context.Context
		alertWithRule *caseManagementPb.AlertWithRuleDetails
		optionals     *reviewPb.CaseBuilderOptions
	}
	tests := []struct {
		name      string
		mocks     func(mock *caseBuilderMockedDependencies, args args)
		args      args
		want      *reviewPb.Case
		wantErr   bool
		assertErr func(err error) bool
	}{
		{
			name:  "get review type failed due to entity type unspecified",
			mocks: func(mock *caseBuilderMockedDependencies, args args) {},
			args: args{
				ctx:           context.Background(),
				alertWithRule: &caseManagementPb.AlertWithRuleDetails{Alert: alertFixtureWithEntityTypeMissing, ExtendedRule: nil},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return errors.Is(err, caseManagementPb.ErrReviewTypeMappingNotFound)
			},
		},
		{
			name:  "get priority failed since is sample is set to true",
			mocks: func(mock *caseBuilderMockedDependencies, args args) {},
			args: args{
				ctx:           context.Background(),
				alertWithRule: &caseManagementPb.AlertWithRuleDetails{Alert: alertFixture1, ExtendedRule: nil},
				optionals: &reviewPb.CaseBuilderOptions{
					IsSample: true,
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Failed to fetch alert confidence score",
			mocks: func(mock *caseBuilderMockedDependencies, args args) {
				mock.caseAnalyser.EXPECT().EvaluateAlertConfidenceScore(context.Background(), args.alertWithRule).Return([]*risk.ModelResponseInfo{}, float32(0.0), CasePrioAlertTags, epifierrors.ErrInvalidArgument)
			},
			args: args{
				ctx:           context.Background(),
				alertWithRule: &caseManagementPb.AlertWithRuleDetails{Alert: alertFixture1, ExtendedRule: nil},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "success with priority critical",
			mocks: func(mock *caseBuilderMockedDependencies, args args) {
				mock.caseAnalyser.EXPECT().EvaluateAlertConfidenceScore(context.Background(), args.alertWithRule).Return([]*risk.ModelResponseInfo{{Name: "actor1", Score: 31}, {Name: "actor2", Score: 32}}, float32(0.0), CasePrioAlertTags, nil)
			},
			args: args{
				ctx:           context.Background(),
				alertWithRule: &caseManagementPb.AlertWithRuleDetails{Alert: alertFixture1, ExtendedRule: &caseManagementPb.ExtendedRule{Rule: RuleWithScoreAboveCriticalPriorityThreshold}},
				optionals: &reviewPb.CaseBuilderOptions{
					IsSample: false,
				},
			},
			want: &reviewPb.Case{
				Priority:    reviewPb.Priority_PRIORITY_CRITICAL,
				Status:      reviewPb.Status_STATUS_CREATED,
				IsSample:    commontypes.BooleanEnum_FALSE,
				ActorId:     alertFixture1.GetActorId(),
				ReviewType:  reviewPb.ReviewType_REVIEW_TYPE_TRANSACTION_REVIEW,
				Tags:        append([]string{"test-rule", caseManagementPb.RuleGroup_RULE_GROUP_CRYPTO.String(), alertFixture1.GetBatchName()}, CasePrioAlertTags...),
				Model1Score: 31,
				Model2Score: 32,
				Model1Name:  "actor1",
				Model2Name:  "actor2",
			},
			wantErr: false,
		},
		{
			name: "success with priority high",
			mocks: func(mock *caseBuilderMockedDependencies, args args) {
				mock.caseAnalyser.EXPECT().EvaluateAlertConfidenceScore(context.Background(), args.alertWithRule).Return([]*risk.ModelResponseInfo{{Name: "actor1", Score: 31}, {Name: "actor2", Score: 32}}, float32(0.0), CasePrioAlertTags, nil)
			},
			args: args{
				ctx:           context.Background(),
				alertWithRule: &caseManagementPb.AlertWithRuleDetails{Alert: alertFixture1, ExtendedRule: &caseManagementPb.ExtendedRule{Rule: RuleWithScoreAboveHighPriorityThreshold}},
				optionals: &reviewPb.CaseBuilderOptions{
					IsSample: false,
				},
			},
			want: &reviewPb.Case{
				Priority:    reviewPb.Priority_PRIORITY_HIGH,
				Status:      reviewPb.Status_STATUS_CREATED,
				IsSample:    commontypes.BooleanEnum_FALSE,
				ActorId:     alertFixture1.GetActorId(),
				ReviewType:  reviewPb.ReviewType_REVIEW_TYPE_TRANSACTION_REVIEW,
				Tags:        append([]string{"test-rule", caseManagementPb.RuleGroup_RULE_GROUP_CRYPTO.String(), alertFixture1.GetBatchName()}, CasePrioAlertTags...),
				Model1Score: 31,
				Model2Score: 32,
				Model1Name:  "actor1",
				Model2Name:  "actor2",
			},
			wantErr: false,
		},
		{
			name: "success with priority medium",
			mocks: func(mock *caseBuilderMockedDependencies, args args) {
				mock.caseAnalyser.EXPECT().EvaluateAlertConfidenceScore(context.Background(), args.alertWithRule).Return([]*risk.ModelResponseInfo{{Name: "actor1", Score: 31}, {Name: "actor2", Score: 32}}, float32(0.0), CasePrioAlertTags, nil)
			},
			args: args{
				ctx:           context.Background(),
				alertWithRule: &caseManagementPb.AlertWithRuleDetails{Alert: alertFixture1, ExtendedRule: &caseManagementPb.ExtendedRule{Rule: RuleWithScoreAboveMediumPriorityThreshold}},
				optionals: &reviewPb.CaseBuilderOptions{
					IsSample: false,
				},
			},
			want: &reviewPb.Case{
				Priority:    reviewPb.Priority_PRIORITY_MEDIUM,
				Status:      reviewPb.Status_STATUS_CREATED,
				IsSample:    commontypes.BooleanEnum_FALSE,
				ActorId:     alertFixture1.GetActorId(),
				ReviewType:  reviewPb.ReviewType_REVIEW_TYPE_TRANSACTION_REVIEW,
				Tags:        append([]string{"test-rule", caseManagementPb.RuleGroup_RULE_GROUP_CRYPTO.String(), alertFixture1.GetBatchName()}, CasePrioAlertTags...),
				Model1Score: 31,
				Model2Score: 32,
				Model1Name:  "actor1",
				Model2Name:  "actor2",
			},
			wantErr: false,
		},
		{
			name: "success with priority low",
			mocks: func(mock *caseBuilderMockedDependencies, args args) {
				mock.caseAnalyser.EXPECT().EvaluateAlertConfidenceScore(context.Background(), args.alertWithRule).Return([]*risk.ModelResponseInfo{{Name: "actor1", Score: 31}, {Name: "actor2", Score: 32}}, float32(0.0), CasePrioAlertTags, nil)
			},
			args: args{
				ctx:           context.Background(),
				alertWithRule: &caseManagementPb.AlertWithRuleDetails{Alert: alertFixture1, ExtendedRule: &caseManagementPb.ExtendedRule{Rule: RuleWithScoreAboveLowPriorityThreshold}},
				optionals: &reviewPb.CaseBuilderOptions{
					IsSample: false,
				},
			},
			want: &reviewPb.Case{
				Priority:    reviewPb.Priority_PRIORITY_LOW,
				Status:      reviewPb.Status_STATUS_CREATED,
				IsSample:    commontypes.BooleanEnum_FALSE,
				ActorId:     alertFixture1.GetActorId(),
				ReviewType:  reviewPb.ReviewType_REVIEW_TYPE_TRANSACTION_REVIEW,
				Tags:        append([]string{"test-rule", caseManagementPb.RuleGroup_RULE_GROUP_CRYPTO.String(), alertFixture1.GetBatchName()}, CasePrioAlertTags...),
				Model1Score: 31,
				Model2Score: 32,
				Model1Name:  "actor1",
				Model2Name:  "actor2",
			},
			wantErr: false,
		},
		{
			name: "success with priority low and tag trimmed",
			mocks: func(mock *caseBuilderMockedDependencies, args args) {
				mock.caseAnalyser.EXPECT().EvaluateAlertConfidenceScore(context.Background(), args.alertWithRule).Return([]*risk.ModelResponseInfo{{Name: "actor1", Score: 31}, {Name: "actor2", Score: 32}}, float32(0.0), CasePrioAlertTags, nil)
			},
			args: args{
				ctx:           context.Background(),
				alertWithRule: &caseManagementPb.AlertWithRuleDetails{Alert: alertFixture1, ExtendedRule: &caseManagementPb.ExtendedRule{Rule: RuleWithScoreAboveLowPriorityThresholdAndLargeRuleName}},
				optionals: &reviewPb.CaseBuilderOptions{
					IsSample: false,
				}},
			want: &reviewPb.Case{
				Priority:    reviewPb.Priority_PRIORITY_LOW,
				Status:      reviewPb.Status_STATUS_CREATED,
				IsSample:    commontypes.BooleanEnum_FALSE,
				ActorId:     alertFixture1.GetActorId(),
				ReviewType:  reviewPb.ReviewType_REVIEW_TYPE_TRANSACTION_REVIEW,
				Tags:        append([]string{"Payee activity exceeded - Full..", caseManagementPb.RuleGroup_RULE_GROUP_CRYPTO.String(), alertFixture1.GetBatchName()}, CasePrioAlertTags...),
				Model1Score: 31,
				Model2Score: 32,
				Model1Name:  "actor1",
				Model2Name:  "actor2",
			},
			wantErr: false,
		},
		{
			name: "success with priority low and tag trimmed, Rule Populated",
			mocks: func(mock *caseBuilderMockedDependencies, args args) {
				mock.caseAnalyser.EXPECT().EvaluateAlertConfidenceScore(context.Background(), args.alertWithRule).Return([]*risk.ModelResponseInfo{{Name: "actor1", Score: 31}, {Name: "actor2", Score: 32}}, float32(0.0), CasePrioAlertTags, nil)
			},
			args: args{
				ctx:           context.Background(),
				alertWithRule: &caseManagementPb.AlertWithRuleDetails{Alert: alertFixture1, Rule: RuleWithScoreAboveLowPriorityThresholdAndLargeRuleName},
				optionals: &reviewPb.CaseBuilderOptions{
					IsSample: false,
				}},
			want: &reviewPb.Case{
				Priority:    reviewPb.Priority_PRIORITY_LOW,
				Status:      reviewPb.Status_STATUS_CREATED,
				IsSample:    commontypes.BooleanEnum_FALSE,
				ActorId:     alertFixture1.GetActorId(),
				ReviewType:  reviewPb.ReviewType_REVIEW_TYPE_TRANSACTION_REVIEW,
				Tags:        append([]string{"Payee activity exceeded - Full..", caseManagementPb.RuleGroup_RULE_GROUP_CRYPTO.String(), alertFixture1.GetBatchName()}, CasePrioAlertTags...),
				Model1Score: 31,
				Model2Score: 32,
				Model1Name:  "actor1",
				Model2Name:  "actor2",
			},
			wantErr: false,
		},
		{
			name: "success with auto enabled true",
			mocks: func(mock *caseBuilderMockedDependencies, args args) {
				mock.caseAnalyser.EXPECT().EvaluateAlertConfidenceScore(context.Background(), args.alertWithRule).Return([]*risk.ModelResponseInfo{{Name: "actor1", Score: 31}, {Name: "actor2", Score: 32}}, float32(0.0), CasePrioAlertTags, nil)
			},
			args: args{
				ctx:           context.Background(),
				alertWithRule: &caseManagementPb.AlertWithRuleDetails{Alert: alertFixture1, Rule: RuleWithScoreAboveLowPriorityThresholdAndLargeRuleName},
				optionals: &reviewPb.CaseBuilderOptions{
					IsAutoAction: true,
					IsSample:     false,
				},
			},
			want: &reviewPb.Case{
				Priority:    reviewPb.Priority_PRIORITY_LOW,
				Status:      reviewPb.Status_STATUS_MARKED_FOR_AUTO_ACTION,
				IsSample:    commontypes.BooleanEnum_FALSE,
				ActorId:     alertFixture1.GetActorId(),
				ReviewType:  reviewPb.ReviewType_REVIEW_TYPE_TRANSACTION_REVIEW,
				Tags:        append([]string{"Payee activity exceeded - Full..", caseManagementPb.RuleGroup_RULE_GROUP_CRYPTO.String(), alertFixture1.GetBatchName()}, CasePrioAlertTags...),
				Model1Score: 31,
				Model2Score: 32,
				Model1Name:  "actor1",
				Model2Name:  "actor2",
			},
			wantErr: false,
		},
		{
			name: "success with wont review case",
			mocks: func(mock *caseBuilderMockedDependencies, args args) {
				mock.caseAnalyser.EXPECT().EvaluateAlertConfidenceScore(context.Background(), args.alertWithRule).Return([]*risk.ModelResponseInfo{{Name: "actor1", Score: 31}, {Name: "actor2", Score: 32}}, float32(0.0), CasePrioAlertTags, nil)
			},
			args: args{
				ctx:           context.Background(),
				alertWithRule: &caseManagementPb.AlertWithRuleDetails{Alert: alertFixture1, Rule: RuleWithScoreAboveLowPriorityThresholdAndLargeRuleName},
				optionals: &reviewPb.CaseBuilderOptions{
					CaseReviewLevel: &reviewPb.CaseReviewLevel{
						IsActive:        true,
						CaseReviewLevel: enums.CaseReviewLevel_CASE_ACTION_LEVEL_CREATE_WONT_REVIEW_CASE,
					},
				},
			},
			want: &reviewPb.Case{
				Priority:    reviewPb.Priority_PRIORITY_LOW,
				Status:      reviewPb.Status_STATUS_CLOSED,
				IsSample:    commontypes.BooleanEnum_FALSE,
				ActorId:     alertFixture1.GetActorId(),
				ReviewType:  reviewPb.ReviewType_REVIEW_TYPE_TRANSACTION_REVIEW,
				Tags:        append([]string{"Payee activity exceeded - Full..", caseManagementPb.RuleGroup_RULE_GROUP_CRYPTO.String(), alertFixture1.GetBatchName()}, CasePrioAlertTags...),
				Model1Score: 31,
				Model2Score: 32,
				Model1Name:  "actor1",
				Model2Name:  "actor2",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c, mockedDeps := newCaseBuilderWithMocks(t)
			if tt.mocks != nil {
				tt.mocks(mockedDeps, tt.args)
			}
			got, err := c.BuildNewCaseFromAlertAndRule(tt.args.ctx, tt.args.alertWithRule, tt.args.optionals)
			if (err != nil) != tt.wantErr {
				t.Errorf("BuildNewCaseFromAlertAndRule() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr && tt.assertErr != nil && !tt.assertErr(err) {
				t.Errorf("BuildNewCaseFromAlertAndRule() error = %v assertion failed", err)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("BuildNewCaseFromAlertAndRule() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCaseBuilder_BuildUpdatedCaseWithAlertAndRule(t *testing.T) {
	type args struct {
		ctx           context.Context
		currentCase   *reviewPb.Case
		alertWithRule *caseManagementPb.AlertWithRuleDetails
		optionals     *reviewPb.CaseBuilderOptions
	}
	tests := []struct {
		name    string
		mocks   func(mock *caseBuilderMockedDependencies, args args)
		args    args
		want    *reviewPb.Case
		wantErr bool
	}{
		{
			name:    "error due to nil case object",
			mocks:   func(mock *caseBuilderMockedDependencies, args args) {},
			args:    args{ctx: context.Background(), currentCase: nil, alertWithRule: &caseManagementPb.AlertWithRuleDetails{Alert: alertFixture1}},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Failed to fetch case confidence score",
			mocks: func(mock *caseBuilderMockedDependencies, args args) {
				mock.caseAnalyser.EXPECT().EvaluateCaseConfidenceScore(context.Background(), args.currentCase, args.alertWithRule).Return([]*risk.ModelResponseInfo{}, float32(0.0), CasePrioAlertTags, epifierrors.ErrInvalidArgument)
			},
			args: args{
				ctx:           context.Background(),
				currentCase:   &reviewPb.Case{Id: "1", Status: reviewPb.Status_STATUS_ASSIGNED, Priority: reviewPb.Priority_PRIORITY_HIGH, Tags: []string{"test1"}},
				alertWithRule: &caseManagementPb.AlertWithRuleDetails{Alert: alertFixture1, ExtendedRule: nil},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "success, with current case priority higher than new priority",
			mocks: func(mock *caseBuilderMockedDependencies, args args) {
				mock.caseAnalyser.EXPECT().EvaluateCaseConfidenceScore(context.Background(), args.currentCase, args.alertWithRule).Return([]*risk.ModelResponseInfo{{Name: "actor1", Score: 31}, {Name: "actor2", Score: 32}}, float32(0.0), CasePrioAlertTags, nil)
			},
			args: args{
				ctx:           context.Background(),
				currentCase:   &reviewPb.Case{Id: "1", Status: reviewPb.Status_STATUS_ASSIGNED, Priority: reviewPb.Priority_PRIORITY_HIGH, Tags: []string{"test1"}},
				alertWithRule: &caseManagementPb.AlertWithRuleDetails{Alert: alertFixture1, ExtendedRule: &caseManagementPb.ExtendedRule{Rule: RuleWithScoreAboveMediumPriorityThreshold}}},
			want: &reviewPb.Case{
				Id:          "1",
				Status:      reviewPb.Status_STATUS_ASSIGNED,
				Priority:    reviewPb.Priority_PRIORITY_HIGH,
				Tags:        append([]string{"test1", "test-rule", caseManagementPb.RuleGroup_RULE_GROUP_CRYPTO.String(), alertFixture1.GetBatchName()}, CasePrioAlertTags...),
				Model1Score: 31,
				Model2Score: 32,
				Model1Name:  "actor1",
				Model2Name:  "actor2"},
			wantErr: false,
		},
		{
			name: "success, with current case priority equal to new priority",
			mocks: func(mock *caseBuilderMockedDependencies, args args) {
				mock.caseAnalyser.EXPECT().EvaluateCaseConfidenceScore(context.Background(), args.currentCase, args.alertWithRule).Return([]*risk.ModelResponseInfo{{Name: "actor1", Score: 31}, {Name: "actor2", Score: 32}}, float32(0.0), CasePrioAlertTags, nil)
			},
			args: args{
				ctx:           context.Background(),
				currentCase:   &reviewPb.Case{Id: "1", Status: reviewPb.Status_STATUS_ASSIGNED, Priority: reviewPb.Priority_PRIORITY_MEDIUM, Tags: []string{"test1"}},
				alertWithRule: &caseManagementPb.AlertWithRuleDetails{Alert: alertFixture1, ExtendedRule: &caseManagementPb.ExtendedRule{Rule: RuleWithScoreAboveMediumPriorityThreshold}}},
			want: &reviewPb.Case{
				Id:          "1",
				Status:      reviewPb.Status_STATUS_ASSIGNED,
				Priority:    reviewPb.Priority_PRIORITY_MEDIUM,
				Tags:        append([]string{"test1", "test-rule", caseManagementPb.RuleGroup_RULE_GROUP_CRYPTO.String(), alertFixture1.GetBatchName()}, CasePrioAlertTags...),
				Model1Score: 31,
				Model2Score: 32,
				Model1Name:  "actor1",
				Model2Name:  "actor2",
			},
			wantErr: false,
		},
		{
			name: "success, with current case priority lower than new priority",
			mocks: func(mock *caseBuilderMockedDependencies, args args) {
				mock.caseAnalyser.EXPECT().EvaluateCaseConfidenceScore(context.Background(), args.currentCase, args.alertWithRule).Return([]*risk.ModelResponseInfo{{Name: "actor1", Score: 31}, {Name: "actor2", Score: 32}}, float32(0.0), CasePrioAlertTags, nil)
			},
			args: args{
				ctx:           context.Background(),
				currentCase:   &reviewPb.Case{Id: "1", Status: reviewPb.Status_STATUS_ASSIGNED, Priority: reviewPb.Priority_PRIORITY_LOW, Tags: []string{"test1"}},
				alertWithRule: &caseManagementPb.AlertWithRuleDetails{Alert: alertFixture1, ExtendedRule: &caseManagementPb.ExtendedRule{Rule: RuleWithScoreAboveMediumPriorityThreshold}}},
			want: &reviewPb.Case{
				Id:          "1",
				Status:      reviewPb.Status_STATUS_ASSIGNED,
				Priority:    reviewPb.Priority_PRIORITY_MEDIUM,
				Tags:        append([]string{"test1", "test-rule", caseManagementPb.RuleGroup_RULE_GROUP_CRYPTO.String(), alertFixture1.GetBatchName()}, CasePrioAlertTags...),
				Model1Score: 31,
				Model2Score: 32,
				Model1Name:  "actor1",
				Model2Name:  "actor2",
			},
			wantErr: false,
		},
		{
			name: "success, with Rule pupulated",
			mocks: func(mock *caseBuilderMockedDependencies, args args) {
				mock.caseAnalyser.EXPECT().EvaluateCaseConfidenceScore(context.Background(), args.currentCase, args.alertWithRule).Return([]*risk.ModelResponseInfo{{Name: "actor1", Score: 31}, {Name: "actor2", Score: 32}}, float32(0.0), CasePrioAlertTags, nil)
			},
			args: args{
				ctx:           context.Background(),
				currentCase:   &reviewPb.Case{Id: "1", Status: reviewPb.Status_STATUS_ASSIGNED, Priority: reviewPb.Priority_PRIORITY_LOW, Tags: []string{"test1"}},
				alertWithRule: &caseManagementPb.AlertWithRuleDetails{Alert: alertFixture1, Rule: RuleWithScoreAboveMediumPriorityThreshold}},
			want: &reviewPb.Case{
				Id:          "1",
				Status:      reviewPb.Status_STATUS_ASSIGNED,
				Priority:    reviewPb.Priority_PRIORITY_MEDIUM,
				Tags:        append([]string{"test1", "test-rule", caseManagementPb.RuleGroup_RULE_GROUP_CRYPTO.String(), alertFixture1.GetBatchName()}, CasePrioAlertTags...),
				Model1Score: 31,
				Model2Score: 32,
				Model1Name:  "actor1",
				Model2Name:  "actor2",
			},
			wantErr: false,
		},
		{
			name: "success, with auto action enabled",
			mocks: func(mock *caseBuilderMockedDependencies, args args) {
				mock.caseAnalyser.EXPECT().EvaluateCaseConfidenceScore(context.Background(), args.currentCase, args.alertWithRule).Return([]*risk.ModelResponseInfo{{Name: "actor1", Score: 31}, {Name: "actor2", Score: 32}}, float32(0.0), CasePrioAlertTags, nil)
			},
			args: args{
				ctx: context.Background(),
				currentCase: &reviewPb.Case{Id: "1", Status: reviewPb.Status_STATUS_CREATED, Priority: reviewPb.Priority_PRIORITY_LOW,
					Tags: []string{"test1"}},
				alertWithRule: &caseManagementPb.AlertWithRuleDetails{Alert: alertFixture1,
					Rule: RuleWithScoreAboveMediumPriorityThreshold},
				optionals: &reviewPb.CaseBuilderOptions{
					IsAutoAction: true,
				},
			},
			want: &reviewPb.Case{
				Id:          "1",
				Status:      reviewPb.Status_STATUS_MARKED_FOR_AUTO_ACTION,
				Priority:    reviewPb.Priority_PRIORITY_MEDIUM,
				Tags:        append([]string{"test1", "test-rule", caseManagementPb.RuleGroup_RULE_GROUP_CRYPTO.String(), alertFixture1.GetBatchName()}, CasePrioAlertTags...),
				Model1Score: 31,
				Model2Score: 32,
				Model1Name:  "actor1",
				Model2Name:  "actor2",
			},
			wantErr: false,
		},
		{
			name: "success with wont review case",
			mocks: func(mock *caseBuilderMockedDependencies, args args) {
				mock.caseAnalyser.EXPECT().EvaluateCaseConfidenceScore(context.Background(), args.currentCase, args.alertWithRule).Return([]*risk.ModelResponseInfo{{Name: "actor1", Score: 31}, {Name: "actor2", Score: 32}}, float32(0.0), CasePrioAlertTags, nil)
			},
			args: args{
				ctx: context.Background(),
				currentCase: &reviewPb.Case{Id: "1", Status: reviewPb.Status_STATUS_CREATED, Priority: reviewPb.Priority_PRIORITY_LOW,
					Tags: []string{"test1"}},
				alertWithRule: &caseManagementPb.AlertWithRuleDetails{Alert: alertFixture1,
					Rule: RuleWithScoreAboveMediumPriorityThreshold},
				optionals: &reviewPb.CaseBuilderOptions{
					IsSample: false,
					CaseReviewLevel: &reviewPb.CaseReviewLevel{
						IsActive:        true,
						CaseReviewLevel: enums.CaseReviewLevel_CASE_ACTION_LEVEL_CREATE_WONT_REVIEW_CASE,
					},
				},
			},
			want: &reviewPb.Case{
				Id:          "1",
				Status:      reviewPb.Status_STATUS_CLOSED,
				Priority:    reviewPb.Priority_PRIORITY_MEDIUM,
				Tags:        append([]string{"test1", "test-rule", caseManagementPb.RuleGroup_RULE_GROUP_CRYPTO.String(), alertFixture1.GetBatchName()}, CasePrioAlertTags...),
				Model1Score: 31,
				Model2Score: 32,
				Model1Name:  "actor1",
				Model2Name:  "actor2",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c, mockedDeps := newCaseBuilderWithMocks(t)
			if tt.mocks != nil {
				tt.mocks(mockedDeps, tt.args)
			}
			got, err := c.BuildUpdatedCaseWithAlertAndRule(tt.args.ctx, tt.args.currentCase, tt.args.alertWithRule,
				tt.args.optionals)
			if (err != nil) != tt.wantErr {
				t.Errorf("BuildUpdatedCaseWithAlertAndRule() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("BuildUpdatedCaseWithAlertAndRule() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_trimTags(t *testing.T) {
	smallTag1 := "abc"
	smallTag2 := "abcd"
	tagWithLen32 := "Payee activity exceeded - FullKY"
	tagWithLen34 := "Payee activity exceeded - FullKYcd"
	tagWithLen34Trimmed := "Payee activity exceeded - Full.."

	type args struct {
		tags []string
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		{
			name: "all tags with length less than 32",
			args: args{
				tags: []string{smallTag1, smallTag2, tagWithLen32},
			},
			want: []string{smallTag1, smallTag2, tagWithLen32},
		},
		{
			name: "list with 1 large tag",
			args: args{
				tags: []string{smallTag1, tagWithLen34, tagWithLen32},
			},
			want: []string{smallTag1, tagWithLen34Trimmed, tagWithLen32},
		},
		{
			name: "list with all large tags",
			args: args{
				tags: []string{tagWithLen34, tagWithLen34, tagWithLen34},
			},
			want: []string{tagWithLen34Trimmed, tagWithLen34Trimmed, tagWithLen34Trimmed},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := trimTags(tt.args.tags); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("trimTags() = %v, want %v", got, tt.want)
			}
		})
	}
}
