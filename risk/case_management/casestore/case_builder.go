package casestore

//go:generate mockgen -source=case_builder.go -destination=mocks/mock_case_builder.go -package=mock_casestore
import (
	"context"
	"fmt"
	"strings"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/pkg/errors"

	caseManagementPb "github.com/epifi/gamma/api/risk/case_management"
	reviewPb "github.com/epifi/gamma/api/risk/case_management/review"
	"github.com/epifi/gamma/risk/case_management/caseanalyser"
)

// ICaseBuilder interface will consist of methods related to building/initialising the case object using raw information
type ICaseBuilder interface {
	// BuildNewCaseFromAlertAndRule will build and return a case object using the alert and rule information provided
	// will return epifierrors.ErrInvalidArgument if passed parameters are not valid
	BuildNewCaseFromAlertAndRule(ctx context.Context, alertWithRule *caseManagementPb.AlertWithRuleDetails, options *reviewPb.CaseBuilderOptions) (*reviewPb.Case, error)
	// BuildUpdatedCaseWithAlertAndRule will update the required fields for appending given alert in the currentCase object passed
	// will return epifierrors.ErrInvalidArgument if passed parameters are not valid
	BuildUpdatedCaseWithAlertAndRule(ctx context.Context, currentCase *reviewPb.Case,
		alertWithRule *caseManagementPb.AlertWithRuleDetails, options *reviewPb.CaseBuilderOptions) (*reviewPb.Case, error)
}

type CaseBuilder struct {
	caseAnalyser caseanalyser.ICaseAnalyser
}

func NewCaseBuilder(caseAnalyser caseanalyser.ICaseAnalyser) *CaseBuilder {
	return &CaseBuilder{
		caseAnalyser: caseAnalyser,
	}
}

var _ ICaseBuilder = &CaseBuilder{}

const (
	CriticalPriorityThreshold = 70

	HighPriorityThreshold = 60

	MediumPriorityThreshold = 45
)

func (c *CaseBuilder) BuildNewCaseFromAlertAndRule(ctx context.Context,
	alertWithRule *caseManagementPb.AlertWithRuleDetails, options *reviewPb.CaseBuilderOptions) (*reviewPb.Case,
	error) {

	// To maintain backward compatability
	if alertWithRule.GetExtendedRule() == nil {
		alertWithRule.ExtendedRule = &caseManagementPb.ExtendedRule{Rule: alertWithRule.GetRule()}
	}

	reviewType, err := alertWithRule.GetReviewType()
	if err != nil {
		return nil, fmt.Errorf("error while getting review type %w", err)
	}

	priority, err := c.getCasePriority(alertWithRule, options.GetIsSample())
	if err != nil {
		return nil, errors.Wrap(err, "error while getting case priority")
	}

	tags, err := c.getTags(alertWithRule)
	if err != nil {
		return nil, errors.Wrap(err, "error while getting case tags")
	}

	caseStatus := reviewPb.Status_STATUS_CREATED
	switch {
	case options.GetIsAutoAction():
		caseStatus = reviewPb.Status_STATUS_MARKED_FOR_AUTO_ACTION
	case options.GetCaseReviewLevel().IsWontReviewCase():
		caseStatus = reviewPb.Status_STATUS_CLOSED
	}

	modelScores, confScore, casePrioTags, err := c.caseAnalyser.EvaluateAlertConfidenceScore(ctx, alertWithRule)
	if err != nil {
		return nil, errors.Wrap(err, "error while getting alert confidence score")
	}

	// append casePrioTags into tags
	tags = append(tags, casePrioTags...)

	res := &reviewPb.Case{
		Status:          caseStatus,
		IsSample:        commontypes.BoolToBooleanEnum(options.GetIsSample()),
		Priority:        priority,
		ActorId:         alertWithRule.GetAlert().GetActorId(),
		ReviewType:      reviewType,
		Tags:            tags,
		ConfidenceScore: confScore,
		Model1Name:      modelScores[0].GetName(),
		Model2Name:      modelScores[1].GetName(),
		Model1Score:     modelScores[0].GetScore(),
		Model2Score:     modelScores[1].GetScore(),
	}
	return res, nil
}

// BuildUpdatedCaseWithAlertAndRule will update priority and tags in the current case object based on the new alert
// details. It also updates the case if auto action is needed to apply on the case and does the processing related to
// it.
func (c *CaseBuilder) BuildUpdatedCaseWithAlertAndRule(ctx context.Context, currentCase *reviewPb.Case,
	alertWithRule *caseManagementPb.AlertWithRuleDetails, options *reviewPb.CaseBuilderOptions) (*reviewPb.Case, error) {
	// To maintain backward compatability
	if alertWithRule.GetExtendedRule() == nil {
		alertWithRule.ExtendedRule = &caseManagementPb.ExtendedRule{Rule: alertWithRule.GetRule()}
	}

	if currentCase == nil {
		return nil, fmt.Errorf("current case object can't be nil")
	}

	modelScores, confScore, casePrioTags, err := c.caseAnalyser.EvaluateCaseConfidenceScore(ctx, currentCase, alertWithRule)
	if err != nil {
		return nil, errors.Wrap(err, "error while getting case confidence score")
	}

	currentCase.ConfidenceScore = confScore
	currentCase.Model1Score = modelScores[0].GetScore()
	currentCase.Model2Score = modelScores[1].GetScore()
	currentCase.Model1Name = modelScores[0].GetName()
	currentCase.Model2Name = modelScores[1].GetName()

	// Get priority for new alert
	newPriority, err := c.getCasePriority(alertWithRule, false)
	if err != nil {
		return nil, errors.Wrap(err, "error while getting case priority")
	}
	// get tags for new alert
	newTags, err := c.getTags(alertWithRule)
	if err != nil {
		return nil, errors.Wrap(err, "error while getting case priority")
	}

	// We are filtering out old CASE_PRIO tags as we don't want multiple CASE_PRIO tags in the same case
	currentCaseTags := []string{}
	for _, tags := range currentCase.GetTags() {
		if strings.HasPrefix(tags, "CASE_PRIO_") {
			continue
		}
		currentCaseTags = append(currentCaseTags, tags)
	}
	currentCase.Tags = currentCaseTags

	// append casePrioTags into newTags
	newTags = append(newTags, casePrioTags...)

	// Append to existing list of tags in the case if more tags are present for current alert
	if len(newTags) != 0 {
		currentCase.Tags = append(currentCase.GetTags(), newTags...)
	}

	// If new priority is higher than existing case priority update that as well
	isNewPriorityHigher, err := newPriority.IsGreaterThan(currentCase.GetPriority())
	if err != nil {
		return nil, errors.Wrap(err, "error while comparing priorities")
	}

	switch {
	case options.GetIsAutoAction():
		currentCase.Status = reviewPb.Status_STATUS_MARKED_FOR_AUTO_ACTION
	case options.GetCaseReviewLevel().IsWontReviewCase():
		currentCase.Status = reviewPb.Status_STATUS_CLOSED
	}

	if isNewPriorityHigher {
		currentCase.Priority = newPriority
	}
	return currentCase, nil
}

// TODO(40416): As part of case creation eligibility refactor, move this method behind same decider interface
func (c *CaseBuilder) getCasePriority(alertWithRule *caseManagementPb.AlertWithRuleDetails, isSample bool) (reviewPb.Priority, error) {
	switch {
	case isSample:
		return reviewPb.Priority_PRIORITY_UNSPECIFIED, errors.New("get case priority is unimplemented for sample case")
	default:
		return c.getCasePriorityForLiveQueue(alertWithRule)
	}
}

func (c *CaseBuilder) getCasePriorityForLiveQueue(alertWithRule *caseManagementPb.AlertWithRuleDetails) (reviewPb.Priority, error) {
	switch {
	// deprecated logic, will remove post rule clean up
	case alertWithRule.GetExtendedRule().GetRule().GetConfidenceScore() >= CriticalPriorityThreshold:
		return reviewPb.Priority_PRIORITY_CRITICAL, nil
	case alertWithRule.GetExtendedRule().GetRule().GetConfidenceScore() >= HighPriorityThreshold:
		return reviewPb.Priority_PRIORITY_HIGH, nil
	case alertWithRule.GetExtendedRule().GetRule().GetConfidenceScore() >= MediumPriorityThreshold:
		return reviewPb.Priority_PRIORITY_MEDIUM, nil
	case alertWithRule.GetExtendedRule().GetRule().GetState() == caseManagementPb.RuleState_RULE_STATE_ACTIVE:
		return reviewPb.Priority_PRIORITY_HIGH, nil
	default:
		return reviewPb.Priority_PRIORITY_LOW, nil
	}
}

func (c *CaseBuilder) getTags(alertWithRule *caseManagementPb.AlertWithRuleDetails) ([]string, error) {
	var tags []string
	// Add rule name
	tags = append(tags, alertWithRule.GetExtendedRule().GetRule().GetName())

	// Add rule group if present
	if alertWithRule.GetExtendedRule().GetRule().GetRuleGroup() != caseManagementPb.RuleGroup_RULE_GROUP_UNSPECIFIED {
		tags = append(tags, alertWithRule.GetExtendedRule().GetRule().GetRuleGroup().String())
	}

	// Add batch name if present
	if alertWithRule.GetAlert().GetBatchName() != "" {
		tags = append(tags, alertWithRule.GetAlert().GetBatchName())
	}
	// trim tags since max supported length for individual tag is 32
	tags = trimTags(tags)
	return tags, nil
}

// trimTags will return a list of trimmed tags for given list
// Trimming logic is:
// - If length of tag is less than 32 tag will remain same
// - If length of tags is above 32, first 30 characters will be taken and ".." will be appended in the end
func trimTags(tags []string) []string {
	var trimmedTags []string
	for _, tag := range tags {
		if len(tag) > 32 {
			tag = tag[:30] + ".."
		}
		trimmedTags = append(trimmedTags, tag)
	}
	return trimmedTags
}
