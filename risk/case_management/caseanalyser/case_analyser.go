package caseanalyser

//go:generate mockgen -source=case_analyser.go -destination=mocks/mock_case_analyser.go -package=mock

import (
	"context"
	"fmt"
	"math/rand"
	"time"

	"github.com/epifi/be-common/pkg/epifierrors"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	caseManagementPb "github.com/epifi/gamma/api/risk/case_management"
	prioritizationPb "github.com/epifi/gamma/api/risk/case_management/prioritization"
	reviewPb "github.com/epifi/gamma/api/risk/case_management/review"
	"github.com/epifi/gamma/api/vendorgateway/risk"
	cmDao "github.com/epifi/gamma/risk/case_management/dao"
	"github.com/epifi/gamma/risk/case_management/essential/alert_with_rule"
	"github.com/epifi/gamma/risk/case_management/prioritisation"
	"github.com/epifi/gamma/risk/config/common"
)

const (
	percent   = 100
	newCaseId = "new_case_id"
)

// ICaseAnalyser is in abstraction to evaluate case metrics such as confidence score.
type ICaseAnalyser interface {
	// EvaluateCaseConfidenceScore computes confidence score for the case. It is mix of user onboarding risk score, triggered rules attached to the case etc.
	EvaluateCaseConfidenceScore(ctx context.Context, caseObj *reviewPb.Case, alertWithRule *caseManagementPb.AlertWithRuleDetails) ([]*risk.ModelResponseInfo, float32, []string, error)
	// EvaluateAlertConfidenceScore computes confidence score for the alert. It is mix of user onboarding risk score, triggered rule attached to the alert etc.
	EvaluateAlertConfidenceScore(ctx context.Context, alertWithRule *caseManagementPb.AlertWithRuleDetails) ([]*risk.ModelResponseInfo, float32, []string, error)
}

type CaseAnalyser struct {
	alertWithRuleManager  alert_with_rule.AlertWithRuleManager
	prioritizationFactory prioritisation.Factory
	conf                  *common.PrioritiserConfig
}

type CaseConfidenceMetricsWeights struct {
	RuleCount                    float32
	OnboardingRiskDetectionModel float32
}

// nolint: gochecknoinits
func init() {
	rand.Seed(time.Now().UnixNano())
}

func NewCaseAnalyser(alertWithRuleManager alert_with_rule.AlertWithRuleManager, prioritizationFactory prioritisation.Factory, conf *common.PrioritiserConfig) *CaseAnalyser {
	return &CaseAnalyser{
		alertWithRuleManager:  alertWithRuleManager,
		prioritizationFactory: prioritizationFactory,
		conf:                  conf,
	}
}

var _ ICaseAnalyser = &CaseAnalyser{}

func (c *CaseAnalyser) EvaluateCaseConfidenceScore(ctx context.Context, caseObj *reviewPb.Case, alertWithRule *caseManagementPb.AlertWithRuleDetails) ([]*risk.ModelResponseInfo, float32, []string, error) {
	return c.getConfidenceScoreForActor(ctx, caseObj.GetActorId(), alertWithRule, caseObj.GetId())
}

func (c *CaseAnalyser) EvaluateAlertConfidenceScore(ctx context.Context, alertWithRule *caseManagementPb.AlertWithRuleDetails) ([]*risk.ModelResponseInfo, float32, []string, error) {
	return c.getConfidenceScoreForActor(ctx, alertWithRule.GetAlert().GetActorId(), alertWithRule, newCaseId)
}

// nolint: gosec
func (c *CaseAnalyser) getConfidenceScoreForActor(ctx context.Context, actorId string, alertWithRule *caseManagementPb.AlertWithRuleDetails, caseId string) ([]*risk.ModelResponseInfo, float32, []string, error) {
	if len(actorId) == 0 {
		return nil, 0, nil, fmt.Errorf("invalid actor id %w", epifierrors.ErrRecordNotFound)
	}

	// Validate alertWithRule and its nested alert
	if alertWithRule == nil {
		return nil, 0, nil, fmt.Errorf("alertWithRule is nil %w", epifierrors.ErrInvalidArgument)
	}
	if alertWithRule.GetAlert() == nil {
		return nil, 0, nil, fmt.Errorf("alert within alertWithRule is nil %w", epifierrors.ErrInvalidArgument)
	}

	options := make([]storagev2.FilterOption, 0)
	// to get the alerts for last 15 days against the actor
	options = append(options, cmDao.WithCreatedAtAfter(time.Now().AddDate(0, 0, -15)))
	alerts, alertsErr := c.alertWithRuleManager.GetByActorId(ctx, actorId,
		[]caseManagementPb.AlertFieldMask{caseManagementPb.
			AlertFieldMask_ALERT_FIELD_MASK_ALL}, 100, options...)

	if alertsErr != nil {
		return nil, 0, nil, fmt.Errorf("failed to fetch alerts for the actor %w", alertsErr)
	}

	// Make Case Details object and add alert details
	currentCaseAlerts := caseManagementPb.AlertWithRuleDetailsList{
		alertWithRule,
	}
	// If we have an alert for an existing case, then we want all the cumulative alerts for that case.
	if caseId != newCaseId {
		for _, alert := range alerts {
			if alert != nil && alert.GetAlert() != nil {
				if alert.GetAlert().GetCaseId() == caseId && alert.GetAlert().GetId() != alertWithRule.GetAlert().GetId() {
					currentCaseAlerts = append(currentCaseAlerts, alert)
				}
			}
		}
	}
	caseDetails := risk.GetCasePrioritisationScoreRequest_CaseDetails{
		CaseId:               caseId,
		AlertWithRuleDetails: currentCaseAlerts,
	}

	// PrecisionModel is deprecated, hence not used, only DS model is used

	// Get DS model based score
	dsModel, dsParams, dsErr := c.prioritizationFactory.GetPrioritizationModel(ctx,
		prioritizationPb.PrioritizationModelType_PRIORITIZATION_MODEL_TYPE_DS_PRECISION, actorId, alerts, &caseDetails)
	if dsErr != nil {
		return nil, 0, nil, fmt.Errorf("failed to fetch DS prioritization model %w", dsErr)
	}

	dsScores, dsConfScore, dsScoreErr := dsModel.GetConfidenceScore(ctx, dsParams)
	// muting this error for now, will uncomment after the DS based prio model is stable
	// in this case the precisionScore will be used
	if dsScoreErr != nil {
		return nil, 0, nil, fmt.Errorf("failed to fetch DS confidence score %w", dsScoreErr)
	}

	tags := make([]string, 0)
	tags = append(tags, "a-b-model-tag")

	return dsScores, dsConfScore, tags, nil
}
