package caseanalyser

import (
	"context"
	"errors"
	"math"
	"testing"
	"time"

	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/pkg/epifierrors"

	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	caseManagementPb "github.com/epifi/gamma/api/risk/case_management"

	"github.com/epifi/gamma/api/risk/case_management/prioritization"
	"github.com/epifi/gamma/api/vendorgateway/risk"
	cmDao "github.com/epifi/gamma/risk/case_management/dao"
	"github.com/epifi/gamma/risk/case_management/essential/alert_with_rule/mocks"
	mocks2 "github.com/epifi/gamma/risk/case_management/prioritisation/mocks"
	"github.com/epifi/gamma/risk/case_management/prioritisation/priotizer"
	mocks3 "github.com/epifi/gamma/risk/case_management/prioritisation/priotizer/mocks"

	"github.com/epifi/gamma/risk/config/common"
)

func TestCaseAnalyser_getConfidenceScoreForActor(t *testing.T) {
	ctx := context.Background()
	ctrl := gomock.NewController(t)
	mockAlertWithRuleManager := mocks.NewMockAlertWithRuleManager(ctrl)
	mockPrioritizationFactory := mocks2.NewMockFactory(ctrl)
	mockPrioritization := mocks3.NewMockPrioritization(ctrl)
	type alertWithRuleArgs struct {
		actorId     string
		limit       int
		selectMasks []caseManagementPb.AlertFieldMask
		options     []storagev2.FilterOption
	}
	var (
		actorId             = "actorId"
		alertWithRuleParams = alertWithRuleArgs{
			actorId: actorId,
			limit:   100,
			selectMasks: []caseManagementPb.AlertFieldMask{caseManagementPb.
				AlertFieldMask_ALERT_FIELD_MASK_ALL},
			options: []storagev2.FilterOption{cmDao.WithCreatedAtAfter(time.Now().AddDate(0, 0, -15))},
		}
		alerts = []*caseManagementPb.AlertWithRuleDetails{
			&caseManagementPb.AlertWithRuleDetails{},
		}
		testAlertWithRule = &caseManagementPb.AlertWithRuleDetails{
			Alert: &caseManagementPb.Alert{
				Id:      "test-alert-1",
				ActorId: actorId,
				CaseId:  "test-case-id",
			},
		}
		someRandomErr = errors.New("some random error")
		modelParams   = &prioritization.InputParameter{
			ActorId: actorId,
		}
	)

	type args struct {
		actorId string
	}
	type mockAlertWithRuleManagerFields struct {
		enable bool
		req    alertWithRuleArgs
		want   []*caseManagementPb.AlertWithRuleDetails
		err    error
	}
	type mockPrioritizationFactoryFields struct {
		enable          bool
		actorId         string
		alerts          []*caseManagementPb.AlertWithRuleDetails
		caseDetails     interface{}
		modelType       prioritization.PrioritizationModelType
		want            priotizer.Prioritization
		wantInputParams *prioritization.InputParameter
		err             error
	}
	type mockPrioritizationFields struct {
		enable bool
		req    *prioritization.InputParameter
		want   []*risk.ModelResponseInfo
		err    error
	}
	type wantResp struct {
		arr       []*risk.ModelResponseInfo
		confScore float32
		tags      []string
	}
	tests := []struct {
		name                            string
		args                            args
		mockAlertWithRuleManagerFields  mockAlertWithRuleManagerFields
		mockPrioritizationFactoryFields []mockPrioritizationFactoryFields
		mockPrioritizationFields        mockPrioritizationFields
		mockPrioritizationFieldsTimes   int
		want                            wantResp
		wantErr                         bool
		assertErr                       func(err error) bool
	}{
		{
			name:    "fails for invalid actor id",
			args:    args{},
			want:    wantResp{arr: nil, confScore: 0, tags: nil},
			wantErr: true,
			assertErr: func(err error) bool {
				return errors.Is(err, epifierrors.ErrInvalidArgument)
			},
		},
		{
			name: "fails when alerts with rule manager throws the error",
			args: args{
				actorId: actorId,
			},
			mockAlertWithRuleManagerFields: mockAlertWithRuleManagerFields{
				enable: true,
				req:    alertWithRuleParams,
				want:   nil,
				err:    someRandomErr,
			},
			want:    wantResp{arr: nil, confScore: 0, tags: nil},
			wantErr: true,
		},
		{
			name: "fails when alerts with prioritization factory gives the error",
			args: args{
				actorId: actorId,
			},
			mockAlertWithRuleManagerFields: mockAlertWithRuleManagerFields{
				enable: true,
				req:    alertWithRuleParams,
				want:   alerts,
				err:    nil,
			},
			mockPrioritizationFactoryFields: []mockPrioritizationFactoryFields{
				{
					enable:          true,
					actorId:         actorId,
					alerts:          alerts,
					caseDetails:     gomock.Any(),
					modelType:       prioritization.PrioritizationModelType_PRIORITIZATION_MODEL_TYPE_DS_PRECISION,
					want:            nil,
					wantInputParams: nil,
					err:             someRandomErr,
				},
			},
			want:    wantResp{arr: nil, confScore: 0, tags: nil},
			wantErr: true,
		},
		{
			name: "fails when prioritization model gives the error",
			args: args{
				actorId: actorId,
			},
			mockAlertWithRuleManagerFields: mockAlertWithRuleManagerFields{
				enable: true,
				req:    alertWithRuleParams,
				want:   alerts,
				err:    nil,
			},
			mockPrioritizationFactoryFields: []mockPrioritizationFactoryFields{
				{
					enable:          true,
					actorId:         actorId,
					alerts:          alerts,
					caseDetails:     gomock.Any(),
					modelType:       prioritization.PrioritizationModelType_PRIORITIZATION_MODEL_TYPE_DS_PRECISION,
					want:            mockPrioritization,
					wantInputParams: modelParams,
					err:             nil,
				},
			},
			mockPrioritizationFields: mockPrioritizationFields{
				enable: true,
				req:    modelParams,
				want:   []*risk.ModelResponseInfo{},
				err:    someRandomErr,
			},
			mockPrioritizationFieldsTimes: 1,
			want:                          wantResp{arr: nil, confScore: 0, tags: nil},
			wantErr:                       true,
		},
		{
			name: "success",
			args: args{
				actorId: actorId,
			},
			mockAlertWithRuleManagerFields: mockAlertWithRuleManagerFields{
				enable: true,
				req:    alertWithRuleParams,
				want:   alerts,
				err:    nil,
			},
			mockPrioritizationFactoryFields: []mockPrioritizationFactoryFields{
				{
					enable:          true,
					actorId:         actorId,
					alerts:          alerts,
					caseDetails:     gomock.Any(),
					modelType:       prioritization.PrioritizationModelType_PRIORITIZATION_MODEL_TYPE_DS_PRECISION,
					want:            mockPrioritization,
					wantInputParams: modelParams,
					err:             nil,
				},
			},
			mockPrioritizationFields: mockPrioritizationFields{
				enable: true,
				req:    modelParams,
				want:   []*risk.ModelResponseInfo{{Name: "actor1", Score: 21}, {Name: "actor2", Score: 22}},
				err:    nil,
			},
			mockPrioritizationFieldsTimes: 1,
			want: wantResp{arr: []*risk.ModelResponseInfo{{Name: "actor1", Score: 21}, {Name: "actor2", Score: 22}},
				confScore: 0,
				tags:      []string{"a-b-model-tag"}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := NewCaseAnalyser(mockAlertWithRuleManager, mockPrioritizationFactory, &common.PrioritiserConfig{DSModelPrioritisationRolloutPercentage: 0.0})
			if tt.mockAlertWithRuleManagerFields.enable {
				mockAlertWithRuleManager.EXPECT().GetByActorId(ctx, tt.mockAlertWithRuleManagerFields.req.actorId,
					tt.mockAlertWithRuleManagerFields.req.selectMasks, tt.mockAlertWithRuleManagerFields.req.limit,
					gomock.Any()).
					Return(tt.mockAlertWithRuleManagerFields.want, tt.mockAlertWithRuleManagerFields.err)
			}
			for _, mockPrioritizationFactoryField := range tt.mockPrioritizationFactoryFields {
				if mockPrioritizationFactoryField.enable {
					mockPrioritizationFactory.EXPECT().GetPrioritizationModel(ctx, mockPrioritizationFactoryField.modelType,
						mockPrioritizationFactoryField.actorId, mockPrioritizationFactoryField.alerts, mockPrioritizationFactoryField.caseDetails).
						Return(mockPrioritizationFactoryField.want, mockPrioritizationFactoryField.wantInputParams,
							mockPrioritizationFactoryField.err).Times(1)
				}
			}

			if tt.mockPrioritizationFields.enable {
				mockPrioritization.EXPECT().GetConfidenceScore(ctx, tt.mockPrioritizationFields.req).
					Return(tt.mockPrioritizationFields.want, tt.want.confScore, tt.mockPrioritizationFields.err).Times(tt.mockPrioritizationFieldsTimes)
			}
			got, got2, _, err := c.getConfidenceScoreForActor(ctx, tt.args.actorId, testAlertWithRule, "test-case-id")
			if (err != nil) != tt.wantErr {
				t.Errorf("getConfidenceScoreForActor() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && !almostEqual(got, tt.want.arr) {
				t.Errorf("getConfidenceScoreForActor() got = %v, want %v", got, tt.want)
			}
			if !tt.wantErr && !almostEqualFloat(got2, tt.want.confScore) {
				t.Errorf("oldConfScore = %f, want %f", got2, tt.want.confScore)
			}
		})
	}
}

const float64EqualityThreshold = 1e-4

func almostEqual(a, b []*risk.ModelResponseInfo) bool {
	// return math.Abs(float64(a)-float64(b)) <= float64EqualityThreshold
	return math.Abs(float64(a[0].Score-b[0].Score)) < float64EqualityThreshold && math.Abs(float64(a[1].Score-b[1].Score)) < float64EqualityThreshold
}

func almostEqualFloat(a, b float32) bool {
	return math.Abs(float64(a-b)) < float64EqualityThreshold
}
