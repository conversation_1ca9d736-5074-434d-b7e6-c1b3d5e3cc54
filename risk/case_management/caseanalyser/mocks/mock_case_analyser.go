// Code generated by MockGen. DO NOT EDIT.
// Source: case_analyser.go

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	case_management "github.com/epifi/gamma/api/risk/case_management"
	review "github.com/epifi/gamma/api/risk/case_management/review"
	risk "github.com/epifi/gamma/api/vendorgateway/risk"
	gomock "github.com/golang/mock/gomock"
)

// MockICaseAnalyser is a mock of ICaseAnalyser interface.
type MockICaseAnalyser struct {
	ctrl     *gomock.Controller
	recorder *MockICaseAnalyserMockRecorder
}

// MockICaseAnalyserMockRecorder is the mock recorder for MockICaseAnalyser.
type MockICaseAnalyserMockRecorder struct {
	mock *MockICaseAnalyser
}

// NewMockICaseAnalyser creates a new mock instance.
func NewMockICaseAnalyser(ctrl *gomock.Controller) *MockICaseAnalyser {
	mock := &MockICaseAnalyser{ctrl: ctrl}
	mock.recorder = &MockICaseAnalyserMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICaseAnalyser) EXPECT() *MockICaseAnalyserMockRecorder {
	return m.recorder
}

// EvaluateAlertConfidenceScore mocks base method.
func (m *MockICaseAnalyser) EvaluateAlertConfidenceScore(ctx context.Context, alertWithRule *case_management.AlertWithRuleDetails) ([]*risk.ModelResponseInfo, float32, []string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EvaluateAlertConfidenceScore", ctx, alertWithRule)
	ret0, _ := ret[0].([]*risk.ModelResponseInfo)
	ret1, _ := ret[1].(float32)
	ret2, _ := ret[2].([]string)
	ret3, _ := ret[3].(error)
	return ret0, ret1, ret2, ret3
}

// EvaluateAlertConfidenceScore indicates an expected call of EvaluateAlertConfidenceScore.
func (mr *MockICaseAnalyserMockRecorder) EvaluateAlertConfidenceScore(ctx, alertWithRule interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EvaluateAlertConfidenceScore", reflect.TypeOf((*MockICaseAnalyser)(nil).EvaluateAlertConfidenceScore), ctx, alertWithRule)
}

// EvaluateCaseConfidenceScore mocks base method.
func (m *MockICaseAnalyser) EvaluateCaseConfidenceScore(ctx context.Context, caseObj *review.Case, alertWithRule *case_management.AlertWithRuleDetails) ([]*risk.ModelResponseInfo, float32, []string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EvaluateCaseConfidenceScore", ctx, caseObj, alertWithRule)
	ret0, _ := ret[0].([]*risk.ModelResponseInfo)
	ret1, _ := ret[1].(float32)
	ret2, _ := ret[2].([]string)
	ret3, _ := ret[3].(error)
	return ret0, ret1, ret2, ret3
}

// EvaluateCaseConfidenceScore indicates an expected call of EvaluateCaseConfidenceScore.
func (mr *MockICaseAnalyserMockRecorder) EvaluateCaseConfidenceScore(ctx, caseObj, alertWithRule interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EvaluateCaseConfidenceScore", reflect.TypeOf((*MockICaseAnalyser)(nil).EvaluateCaseConfidenceScore), ctx, caseObj, alertWithRule)
}
