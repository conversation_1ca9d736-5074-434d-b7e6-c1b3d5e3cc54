//go:generate mockgen -source=dao.go -destination=mocks/mock_dao.go -package=mocks
//go:generate dao_metrics_gen .
package dao

import (
	"context"

	"github.com/google/wire"
	structPb "google.golang.org/protobuf/types/known/structpb"

	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	cmPb "github.com/epifi/gamma/api/risk/case_management"
	"github.com/epifi/gamma/api/risk/case_management/enums"
	formPb "github.com/epifi/gamma/api/risk/case_management/form"
	caseReviewPb "github.com/epifi/gamma/api/risk/case_management/review"
)

var WireSet = wire.NewSet(
	NewRuleDao, wire.Bind(new(RuleDao), new(*RuleDaoCRDB)),
	NewAlertDao, wire.Bind(new(AlertDao), new(*AlertDaoCRDB)),
	NewReviewActionDao, wire.Bind(new(ReviewAction), new(*ReviewActionCRDB)),
	NewAllowedAnnotationDao, wire.Bind(new(AllowedAnnotationDao), new(*AllowedAnnotationDaoCRDB)),
	NewCommentDao, wire.Bind(new(CommentDao), new(*CommentDaoCRDB)),
	NewAnnotationDao, wire.Bind(new(AnnotationDao), new(*AnnotationDaoCRDB)),
	NewSuggestedActionDao, wire.Bind(new(SuggestedActionDao), new(*SuggestedActionDaoCRDB)),
	NewRiskyUserRequiredInfoDao, wire.Bind(new(RiskyUserRequiredInfoDao), new(*RiskyUserRequiredInfoDaoCRDB)),
	NewRiskyUserRequiredInfoMappingDao, wire.Bind(new(RiskyUserRequiredInfoMappingDao), new(*RiskyUserRequiredInfoMappingDaoCRDB)),
	NewRuleReviewTypeMappingsDao, wire.Bind(new(RuleReviewTypeMappingsDao), new(*RuleReviewTypeMappingsCRDB)),
	NewRulePrecisionDao, wire.Bind(new(RulePrecisionDao), new(*RulePrecisionDaoPGDB)),
	NewFormDao, wire.Bind(new(FormDao), new(*FormDaoPGDB)),
	NewQuestionResponseDao, wire.Bind(new(QuestionResponseDao), new(*QuestionResponseDaoPGDB)),
	NewQuestionDao, wire.Bind(new(QuestionDao), new(*QuestionDaoPGDB)),
	NewFormQuestionMappingDao, wire.Bind(new(FormQuestionMappingDao), new(*FormQuestionMappingDaoPGDB)),
	NewQuestionEntityMappingDao, wire.Bind(new(QuestionEntityMappingDao), new(*QuestionEntityMappingDaoPGDB)),
	NewAnnotationTypeDao, wire.Bind(new(AnnotationTypeDao), new(*AnnotationTypeDaoPGDB)),
	NewUIElementAnnotationTypeMappingDao, wire.Bind(new(UIElementAnnotationTypeMappingDao), new(*UIElementAnnotationTypeMappingDaoPGDB)),
	NewTransactionBlockDao, wire.Bind(new(TransactionBlockDao), new(*TransactionBlockDaoPGDB)),
)

var AnalyticsDaoWireSet = wire.NewSet(NewRiskAnalyticsBigqueryDBResourceProvider, wire.Bind(new(RiskAnalyticsDao), new(*RiskAnalyticsBigqueryDBResourceProvider)))

type ReviewAction interface {
	// Create -> create action against a case by adding to action's table
	Create(ctx context.Context, action *caseReviewPb.Action) (*caseReviewPb.Action, error)
	// ListActions -> will show list of actions performed against a case, along with filter options
	ListActions(ctx context.Context, selectMask []caseReviewPb.ActionFieldMask, options ...storagev2.FilterOption) ([]*caseReviewPb.Action, error)
	// GetByActorId returns actions for all cases with which actor is associated.
	// If limit is not specified, limit condition will be cancelled.
	// Returns epifierrors.ErrRecordNotFound if actions do not exist.
	// Returns epifierrors.ErrInvalidArgument if actor id is empty.
	GetByActorId(ctx context.Context, actorId string, selectMask []caseReviewPb.ActionFieldMask, limit int,
		options ...storagev2.FilterOption) ([]*caseReviewPb.Action, error)

	// Update -> update the action columns specified by masks.
	// Returns epifierrors.ErrInvalidArgument if update masks are empty.
	// Returns epifierrors.ErrRecordNotFound if action does not exist.
	Update(ctx context.Context, action *caseReviewPb.Action, updateMasks []caseReviewPb.ActionFieldMask) error
	// GetById -> return action for given id.
	// fails with epifierrors.ErrInvalidArgument id is empty
	// fails when epifierrors.ErrRecordNotFound when action not found for id.
	GetById(ctx context.Context, id string) (*caseReviewPb.Action, error)
}

type RuleDao interface {

	// CreateWithVersionUpgrade -> create new rule with version upgrade if the rule is already created with the same
	// name, If not it will create the rule with version 1.
	CreateWithVersionUpgrade(ctx context.Context, rule *cmPb.Rule) (*cmPb.Rule, error)

	// Update -> update the rules with the information provided
	// it only allows updates in RuleUpdateColumnNameMap variable
	Update(ctx context.Context, rule *cmPb.Rule, updateMask []cmPb.RuleFieldMask) (*cmPb.Rule, error)

	// GetBulkByExternalId -> get the rules based on the list of external id
	GetBulkByExternalId(ctx context.Context, externalIdList []string) ([]*cmPb.Rule, error)

	// GetBulkById -> get the rules based on the list of id
	GetBulkById(ctx context.Context, idList []string) ([]*cmPb.Rule, error)

	// GetLatestByName -> get the latest rule based on name
	GetLatestByName(ctx context.Context, name string) (*cmPb.Rule, error)

	// GetByRuleGroup returns all the rules belonging to a given rule group
	GetByRuleGroup(ctx context.Context, group cmPb.RuleGroup) ([]*cmPb.Rule, error)

	// GetAll returns all the rules present in the DB
	GetAll(ctx context.Context) ([]*cmPb.Rule, error)
}

type AlertDao interface {
	// Create -> creates the alert in the db
	// fails when there is an issue with the proto according to proto validations
	// fails when something wrong happens in db
	Create(ctx context.Context, alert *cmPb.Alert) (*cmPb.Alert, error)

	// Update -> update the alert with the information provided
	// only updates the filed mask defined in AlertUpdateColumnNameMap
	// fails when invalid update mask provided
	// fails when something wrong happens in updating into db
	Update(ctx context.Context, alert *cmPb.Alert, updateMask []cmPb.AlertFieldMask) (*cmPb.Alert, error)

	// GetByActorId -> gives all the alert related to actor id
	// will return epifierrors.ErrInvalidArgument if
	// nil/blank actor id passed OR field mask is empty OR limit is higher than max limit
	// fails with epifierrors.ErrRecordNotFound when it could not find the alert for an actorId
	// fails when db gives the error while fetching the records from db
	// maximum of MaxRecord record will be returned from here, If limit is not passed limit of MaxRecord will be applied by default
	GetByActorId(ctx context.Context, actorId string, selectMask []cmPb.AlertFieldMask, limit int, options ...storagev2.FilterOption) ([]*cmPb.Alert, error)

	// GetByCaseId -> gives all the alert related to case id
	// fails when nil/blank case id provided
	// fails with epifierrors.ErrRecordNotFound when it could not find the alert for a case id
	// fails when db gives the error while fetching the records from db
	// maximum of MaxRecord record will be returned from here
	GetByCaseId(ctx context.Context, caseId string, options ...storagev2.FilterOption) ([]*cmPb.Alert, error)

	// GetByAccountId -> gives all the alert related to account id
	// fails when nil/blank account id provided
	// fails with epifierrors.ErrRecordNotFound when it could not find the alert for an account id
	// fails when db gives the error while fetching the records from db
	// maximum of MaxRecord record will be returned from here
	GetByAccountId(ctx context.Context, accountId string, options ...storagev2.FilterOption) ([]*cmPb.Alert, error)

	// GetByEntity -> gives all the alert related to entity id
	// fails when nil/blank case id provided
	// fails with epifierrors.ErrRecordNotFound when it could not find the alert for an entity id
	// fails when db gives the error while fetching the records from db
	// maximum of MaxRecord record will be returned from here
	GetByEntity(ctx context.Context, entityType enums.EntityType, entityId string, options ...storagev2.FilterOption) ([]*cmPb.Alert, error)

	// AggregateForActor aggregates alerts for the actor on case id, rule id and creation date.
	// Fails with epifierrors.ErrRecordNotFound if alerts do not exist with given filters.
	// Fails with epifierrors.ErrInvalidArgument if actor id is empty.
	AggregateForActor(ctx context.Context, actorId string, options ...storagev2.FilterOption) ([]*cmPb.AlertAggregateForActor, error)
}

type CommentDao interface {

	// Create -> creates the comment in the db
	// fails when there is an issue with the proto according to proto validations
	// fails when something wrong happens in db
	Create(ctx context.Context, comment *caseReviewPb.Comment) (*caseReviewPb.Comment, error)

	// GetByQuery -> get all the comments for an entity
	// fails when parameters are invalid
	// fails with epifierrors.ErrRecordNotFound when there is no record found for the entity
	// fails when something went wrong while fetching the data from db
	GetByQuery(ctx context.Context, query *caseReviewPb.CommentQuery) ([]*caseReviewPb.Comment, error)

	// GetByCaseIds -> get comments for all case ids
	// fails with epifierrors.ErrInvalidArgument when case id list is empty or more than 100.
	// fails with epifierrors.ErrRecordNotFound when no comments found against cases
	// Comments are sorted by most recently created first.
	GetByCaseIds(ctx context.Context, caseIds []string) ([]*caseReviewPb.Comment, error)
}

type AnnotationDao interface {
	// Create -> creates the annotation in the db
	// fails when there is an issue with the proto according to proto validations
	// fails when something wrong happens in db
	Create(ctx context.Context, annotation *caseReviewPb.Annotation) (*caseReviewPb.Annotation, error)

	// GetByQuery -> get all the annotations for an entity
	// fails when parameters are invalid
	// fails with epifierrors.ErrRecordNotFound when there is no record found for the entity
	// fails when something went wrong while fetching the data from db
	GetByQuery(ctx context.Context, query *cmPb.AnnotationQuery) ([]*caseReviewPb.Annotation, error)

	// GetByCaseIds -> get all annotation linked to the cases
	// Fails with epifierrors.ErrInvalidArgument when case id list is empty or more than 100.
	// Fails with epifierrors.ErrRecordNotFound when there are no annotations against the cases
	// Annotations are sorted by most recently created first.
	GetByCaseIds(ctx context.Context, caseIds []string) ([]*caseReviewPb.Annotation, error)

	// BulkCreate -> creates bulk annotation in the db
	// fails with epifierrors.ErrInvalidArgument if proto validation fails or input is empty.
	BulkCreate(ctx context.Context, annotations []*caseReviewPb.Annotation) ([]*caseReviewPb.Annotation, error)
}

type AllowedAnnotationDao interface {
	// Create -> create allowed annotations in the system
	// fails when proto validations gives error
	// fails when unable to convert to annotation type
	// fails when there is a db error while inserting the data into db
	// fails when could not able to convert back to proto
	Create(ctx context.Context, allowedAnnotation *caseReviewPb.AllowedAnnotation) (*caseReviewPb.AllowedAnnotation,
		error)

	// GetBulkById -> get the allowed annotation by id list
	// fails for the invalid id
	// fails when db returns the error
	// fails with epifierrors.ErrRecordNotFound when it could not find the record
	// fails when model conversion to proto doesn't succeed
	GetBulkById(ctx context.Context, id []string) ([]*caseReviewPb.AllowedAnnotation, error)

	// GetByQuery -> get the allowed annotation based on query parameter
	// fails when proto validations gives the error on query object
	// fails when it could not able to convert to the valid annotation type
	// fails when db gives the error
	// fails with epifierrors.ErrRecordNotFound when it could not find the record
	// fails when it is not able to convert it to proto object
	// gives max of AllowedAnnotationMaxRecord objects in return
	GetByQuery(ctx context.Context, query *caseReviewPb.AllowedAnnotationQuery) ([]*caseReviewPb.AllowedAnnotation,
		error)

	// GetBulkByAnnotationTypeId -> returns allowed annotations by annotation type ids.
	// fails with epifierrors.ErrInvalidArgument for empty ids list.
	// fails with epifierrors.ErrRecordNotFound if annotations not found for ids.
	GetBulkByAnnotationTypeId(ctx context.Context, id []string) ([]*caseReviewPb.AllowedAnnotation, error)
}

type SuggestedActionDao interface {
	// Create -> create suggested actions in the system
	// fails when proto validations gives error
	// fails when there is a db error while inserting the data into db
	Create(ctx context.Context, suggestedAction *cmPb.SuggestedAction) (*cmPb.SuggestedAction, error)
	// GetByRuleID -> get the suggested actions against the rule id
	// fails with epifierrors.ErrInvalidArgument when rule id is invalid
	// fails with epifierrors.ErrRecordNotFound when no record found
	// fails when we get some db error while fetching the data
	GetByRuleID(ctx context.Context, ruleId string) ([]*cmPb.SuggestedAction, error)
}

type RuleReviewTypeMappingsDao interface {
	// Create creates rule review type mapping in db
	// epifierrors.ErrAlreadyExists error if review type already exists against rule
	Create(ctx context.Context, ruleReviewTypeMapping *cmPb.RuleReviewTypeMapping) (*cmPb.RuleReviewTypeMapping, error)
	// GetByRuleId returns review types details against rule id
	// epifierrors.ErrRecordNotFound Error if mapping does not exist
	GetByRuleId(ctx context.Context, ruleId string) ([]*cmPb.RuleReviewTypeMapping, error)
	// Delete soft deletes input rule review type mapping
	// epifierrors.ErrRecordNotFound Error if input mapping does not exist or already deleted
	Delete(ctx context.Context, ruleReviewTypeMapping *cmPb.RuleReviewTypeMapping) error
}

type RiskyUserRequiredInfoDao interface {
	// Create -> create risky user required info in the system
	// fails with epifierrors.ErrInvalidArgument when proto validations gives error
	// fails when there is a db error while inserting the data into db
	Create(ctx context.Context, riskyUserRequiredInfo *cmPb.RiskyUserRequiredInfo) (*cmPb.RiskyUserRequiredInfo, error)
	// GetByIds -> gets all the required info by list of ids
	// fails with epifierrors.ErrInvalidArgument when no ids are given
	// fails when db fails while fetching the data from db
	// fails when epifierrors.ErrRecordNotFound when no record found
	GetByIds(ctx context.Context, ids []string) ([]*cmPb.RiskyUserRequiredInfo, error)
}

type RiskyUserRequiredInfoMappingDao interface {
	// Create -> create risky user required info in the system
	// fails with epifierrors.ErrInvalidArgument when proto validations gives error
	// fails when there is a db error while inserting the data into db
	Create(ctx context.Context, riskyUserRequiredInfoMapping *cmPb.RiskyUserRequiredInfoMapping) (*cmPb.
		RiskyUserRequiredInfoMapping, error)
	// GetByEntity -> gets all the required mapping for the entity
	// fails with epifierrors.ErrInvalidArgument when invalid inputs are given for entity type and entity id
	// fails when db fails while fetching the data from db
	// fails when epifierrors.ErrRecordNotFound when no record found
	GetByEntity(ctx context.Context, entityType cmPb.RiskyUserRequiredInfoMapping_EntityType, entityId string) ([]*cmPb.
		RiskyUserRequiredInfoMapping, error)
}

type RulePrecisionDao interface {
	// CreateWithVersionUpgrade -> create new rule precision with version upgrade if the rule precision is already
	// created with the same rule id, If not it will create the rule precision with version 1.
	// fails with the epifierrors.ErrInvalidArgument if it gets the invalid proto object
	// fails when underlying db operation errors out
	CreateWithVersionUpgrade(ctx context.Context, rulePrecision *cmPb.RulePrecision) (*cmPb.RulePrecision, error)

	// GetLatestByRuleId gets the rule precision for given rule id
	// fails with epifierrors.ErrInvalidArgument if it gets the invalid rule id
	// fails when underlying db layer errored out in fetching the rule precision
	// fails when epifierrors.ErrRecordNotFound when could not find the rule precision
	GetLatestByRuleId(ctx context.Context, ruleId string) (*cmPb.RulePrecision, error)
}

type QuestionEntityMappingDao interface {
	// Create -> create question entity mapping
	// fails with epifierrors.ErrInvalidArgument when validation fails.
	// fails with epifierrors.ErrAlreadyExists if mapping is already present.
	Create(ctx context.Context, mapping *formPb.QuestionEntityMapping) (
		*formPb.QuestionEntityMapping, error)
	// GetByEntity -> gets all the required mapping for the entity
	// fails with epifierrors.ErrInvalidArgument either entity type is unspecified or entity id is empty.
	// fails when epifierrors.ErrRecordNotFound when no record found
	GetByEntity(ctx context.Context, entityType formPb.EntityType, entityId string,
		options ...storagev2.FilterOption) (
		[]*formPb.QuestionEntityMapping, error)
}

type QuestionDao interface {
	// CreateWithVersionUpgrade -> create question with version 1 if question does not exist
	// else creates question with a higher version.
	// fails with epifierrors.ErrInvalidArgument when question validation fails.
	// fails with epifierrors.ErrAlreadyExists if question with given code already exists.
	CreateWithVersionUpgrade(ctx context.Context, question *formPb.Question) (*formPb.Question, error)
	// BulkGet -> filters questions by given field and bulk values.
	// Result is sorted by code and version in descending order.
	// Field can be any one of QuestionIndexedFields.
	// fails with epifierrors.ErrInvalidArgument if the field is unspecified or the value list is empty.
	// fails when epifierrors.ErrRecordNotFound when no question is found.
	BulkGet(ctx context.Context, field formPb.QuestionFieldMask, value *structPb.Value,
		options ...storagev2.FilterOption) ([]*formPb.Question, error)
}

type FormDao interface {
	// Create -> creates form.
	// fails with epifierrors.ErrInvalidArgument when form validation fails.
	Create(ctx context.Context, formObj *formPb.Form) (*formPb.Form, error)
	// Update -> update the form columns specified by masks
	// Fails with epifierrors.ErrInvalidArgument when form validation fails.
	// Returns epifierrors.ErrInvalidArgument if update masks are empty.
	// Returns epifierrors.ErrRecordNotFound if form does not exist.
	Update(ctx context.Context, formObj *formPb.Form, updateMasks []formPb.FormFieldMask) error
	// Get -> filters forms by given field and returns results in descending order of creation time.
	// Field can be any one of FormIndexedFields.
	// fails with epifierrors.ErrInvalidArgument if either field or value is invalid.
	// fails when epifierrors.ErrRecordNotFound when no question is found
	Get(ctx context.Context, field formPb.FormFieldMask, value *structPb.Value, limit int,
		options ...storagev2.FilterOption) ([]*formPb.Form, error)
}

type QuestionResponseDao interface {
	// BulkCreate -> creates user responses for questions in bulk.
	// fails with epifierrors.ErrInvalidArgument when validation fails for any one of the responses.
	// fails with epifierrors.ErrAlreadyExists if response already exists for any question of form.
	BulkCreate(ctx context.Context, responses []*formPb.QuestionResponse) ([]*formPb.QuestionResponse, error)
	// GetByFormId -> filters responses by given form id.
	// fails with epifierrors.ErrInvalidArgument if form id is empty.
	// fails when epifierrors.ErrRecordNotFound when no response is found.
	GetByFormId(ctx context.Context, formId string, options ...storagev2.FilterOption) (
		[]*formPb.QuestionResponse, error)
}

type FormQuestionMappingDao interface {
	// Create -> creates form question mapping
	// fails with epifierrors.ErrInvalidArgument when validation fails.
	// fails with epifierrors.ErrAlreadyExists if mapping is already present.
	Create(ctx context.Context, mapping *formPb.FormQuestionMapping) (
		*formPb.FormQuestionMapping, error)
	// GetByFormId -> filters mappings by given form id.
	// fails with epifierrors.ErrInvalidArgument if form id is empty.
	// fails when epifierrors.ErrRecordNotFound when no mapping is found.
	GetByFormId(ctx context.Context, formId string, options ...storagev2.FilterOption) (
		[]*formPb.FormQuestionMapping, error)
}

type AnnotationTypeDao interface {
	// BulkGetById -> returns allowed annotations types for ids.
	// fails with epifierrors.ErrInvalidArgument if ids list is empty.
	// fails when epifierrors.ErrRecordNotFound when no type is found for ids.
	BulkGetById(ctx context.Context, ids []string) (
		[]*caseReviewPb.AllowedAnnotationType,
		error)
}

type UIElementAnnotationTypeMappingDao interface {
	// GetByUIElement -> returns mappings for input ui element.
	// fails with epifierrors.ErrInvalidArgument if ui element is unspecified or element name is empty
	// fails when epifierrors.ErrRecordNotFound when no mapping is found.
	GetByUIElement(ctx context.Context, uiElement *caseReviewPb.UIElement) (
		[]*caseReviewPb.UIElementAnnotationTypeMapping,
		error)
}

type TransactionBlockDao interface {
	// Create -> creates a new transaction block in the database
	// fails with epifierrors.ErrInvalidArgument when validation fails
	// fails when there is a db error while inserting the data
	Create(ctx context.Context, transactionBlock *cmPb.TransactionBlock) (*cmPb.TransactionBlock, error)

	// Update -> updates the transaction block with the information provided
	// fails with epifierrors.ErrInvalidArgument when validation fails
	// fails with epifierrors.ErrRecordNotFound when transaction block does not exist
	Update(ctx context.Context, transactionBlock *cmPb.TransactionBlock) (*cmPb.TransactionBlock, error)

	// GetById -> returns a transaction block by its ID
	// fails with epifierrors.ErrInvalidArgument when ID is empty
	// fails with epifierrors.ErrRecordNotFound when transaction block does not exist
	GetById(ctx context.Context, id string) (*cmPb.TransactionBlock, error)

	// GetByActorId -> returns all transaction blocks for a given actor ID
	// fails with epifierrors.ErrInvalidArgument when actor ID is empty
	// fails with epifierrors.ErrRecordNotFound when no transaction blocks exist for the actor
	GetByActorId(ctx context.Context, actorId string, blockType string, limit int, options ...storagev2.FilterOption) ([]*cmPb.TransactionBlock, error)

	// GetByAlertId -> returns all transaction blocks for a given alert ID
	// fails with epifierrors.ErrInvalidArgument when alert ID is empty
	// fails with epifierrors.ErrRecordNotFound when no transaction blocks exist for the alert
	GetByAlertId(ctx context.Context, alertId string, blockType string, limit int, options ...storagev2.FilterOption) ([]*cmPb.TransactionBlock, error)
}

// RiskAnalyticsDao - Interface used for querying BigQuery for risk analytics data
type RiskAnalyticsDao interface {
	// GetCreditCounts returns the total count of successful credit transactions for an actor
	// aggregated by the specified aggregation type for the past 1 year
	// Returns a slice of TimeAggregatedCount with time period and count information
	GetCreditCounts(ctx context.Context, actorId string, aggregateOption cmPb.AggregateOption) ([]*cmPb.TimeAggregatedCount, error)

	// GetLifetimeNewP2PCreditCounterparties returns the count of lifetime new P2P credit counterparties for an actor
	// A counterparty is considered "new" if they haven't appeared in any previous transactions (credit or debit) with the user
	// aggregated by the specified aggregation type for the past 1 year
	// Returns a slice of TimeAggregatedCount with time period and distinct new counterparty count information
	GetLifetimeNewP2PCreditCounterparties(ctx context.Context, actorId string, aggregateOption cmPb.AggregateOption) ([]*cmPb.TimeAggregatedCount, error)

	// GetCredits25kPlus returns the count of successful credit transactions greater than 25k for an actor
	// aggregated by the specified aggregation type for the past 1 year
	// Returns a slice of TimeAggregatedCount with time period and count information
	GetCredits25kPlus(ctx context.Context, actorId string, aggregateOption cmPb.AggregateOption) ([]*cmPb.TimeAggregatedCount, error)
}
