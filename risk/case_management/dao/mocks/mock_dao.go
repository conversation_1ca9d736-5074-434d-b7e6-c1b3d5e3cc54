// Code generated by MockGen. DO NOT EDIT.
// Source: dao.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	case_management "github.com/epifi/gamma/api/risk/case_management"
	enums "github.com/epifi/gamma/api/risk/case_management/enums"
	form "github.com/epifi/gamma/api/risk/case_management/form"
	review "github.com/epifi/gamma/api/risk/case_management/review"
	gomock "github.com/golang/mock/gomock"
	structpb "google.golang.org/protobuf/types/known/structpb"
)

// MockReviewAction is a mock of ReviewAction interface.
type MockReviewAction struct {
	ctrl     *gomock.Controller
	recorder *MockReviewActionMockRecorder
}

// MockReviewActionMockRecorder is the mock recorder for MockReviewAction.
type MockReviewActionMockRecorder struct {
	mock *MockReviewAction
}

// NewMockReviewAction creates a new mock instance.
func NewMockReviewAction(ctrl *gomock.Controller) *MockReviewAction {
	mock := &MockReviewAction{ctrl: ctrl}
	mock.recorder = &MockReviewActionMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReviewAction) EXPECT() *MockReviewActionMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockReviewAction) Create(ctx context.Context, action *review.Action) (*review.Action, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, action)
	ret0, _ := ret[0].(*review.Action)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockReviewActionMockRecorder) Create(ctx, action interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockReviewAction)(nil).Create), ctx, action)
}

// GetByActorId mocks base method.
func (m *MockReviewAction) GetByActorId(ctx context.Context, actorId string, selectMask []review.ActionFieldMask, limit int, options ...storagev2.FilterOption) ([]*review.Action, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, actorId, selectMask, limit}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByActorId", varargs...)
	ret0, _ := ret[0].([]*review.Action)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorId indicates an expected call of GetByActorId.
func (mr *MockReviewActionMockRecorder) GetByActorId(ctx, actorId, selectMask, limit interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, actorId, selectMask, limit}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorId", reflect.TypeOf((*MockReviewAction)(nil).GetByActorId), varargs...)
}

// GetById mocks base method.
func (m *MockReviewAction) GetById(ctx context.Context, id string) (*review.Action, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", ctx, id)
	ret0, _ := ret[0].(*review.Action)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockReviewActionMockRecorder) GetById(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockReviewAction)(nil).GetById), ctx, id)
}

// ListActions mocks base method.
func (m *MockReviewAction) ListActions(ctx context.Context, selectMask []review.ActionFieldMask, options ...storagev2.FilterOption) ([]*review.Action, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, selectMask}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListActions", varargs...)
	ret0, _ := ret[0].([]*review.Action)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListActions indicates an expected call of ListActions.
func (mr *MockReviewActionMockRecorder) ListActions(ctx, selectMask interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, selectMask}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListActions", reflect.TypeOf((*MockReviewAction)(nil).ListActions), varargs...)
}

// Update mocks base method.
func (m *MockReviewAction) Update(ctx context.Context, action *review.Action, updateMasks []review.ActionFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, action, updateMasks)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockReviewActionMockRecorder) Update(ctx, action, updateMasks interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockReviewAction)(nil).Update), ctx, action, updateMasks)
}

// MockRuleDao is a mock of RuleDao interface.
type MockRuleDao struct {
	ctrl     *gomock.Controller
	recorder *MockRuleDaoMockRecorder
}

// MockRuleDaoMockRecorder is the mock recorder for MockRuleDao.
type MockRuleDaoMockRecorder struct {
	mock *MockRuleDao
}

// NewMockRuleDao creates a new mock instance.
func NewMockRuleDao(ctrl *gomock.Controller) *MockRuleDao {
	mock := &MockRuleDao{ctrl: ctrl}
	mock.recorder = &MockRuleDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRuleDao) EXPECT() *MockRuleDaoMockRecorder {
	return m.recorder
}

// CreateWithVersionUpgrade mocks base method.
func (m *MockRuleDao) CreateWithVersionUpgrade(ctx context.Context, rule *case_management.Rule) (*case_management.Rule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateWithVersionUpgrade", ctx, rule)
	ret0, _ := ret[0].(*case_management.Rule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateWithVersionUpgrade indicates an expected call of CreateWithVersionUpgrade.
func (mr *MockRuleDaoMockRecorder) CreateWithVersionUpgrade(ctx, rule interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateWithVersionUpgrade", reflect.TypeOf((*MockRuleDao)(nil).CreateWithVersionUpgrade), ctx, rule)
}

// GetAll mocks base method.
func (m *MockRuleDao) GetAll(ctx context.Context) ([]*case_management.Rule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAll", ctx)
	ret0, _ := ret[0].([]*case_management.Rule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAll indicates an expected call of GetAll.
func (mr *MockRuleDaoMockRecorder) GetAll(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAll", reflect.TypeOf((*MockRuleDao)(nil).GetAll), ctx)
}

// GetBulkByExternalId mocks base method.
func (m *MockRuleDao) GetBulkByExternalId(ctx context.Context, externalIdList []string) ([]*case_management.Rule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBulkByExternalId", ctx, externalIdList)
	ret0, _ := ret[0].([]*case_management.Rule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBulkByExternalId indicates an expected call of GetBulkByExternalId.
func (mr *MockRuleDaoMockRecorder) GetBulkByExternalId(ctx, externalIdList interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBulkByExternalId", reflect.TypeOf((*MockRuleDao)(nil).GetBulkByExternalId), ctx, externalIdList)
}

// GetBulkById mocks base method.
func (m *MockRuleDao) GetBulkById(ctx context.Context, idList []string) ([]*case_management.Rule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBulkById", ctx, idList)
	ret0, _ := ret[0].([]*case_management.Rule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBulkById indicates an expected call of GetBulkById.
func (mr *MockRuleDaoMockRecorder) GetBulkById(ctx, idList interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBulkById", reflect.TypeOf((*MockRuleDao)(nil).GetBulkById), ctx, idList)
}

// GetByRuleGroup mocks base method.
func (m *MockRuleDao) GetByRuleGroup(ctx context.Context, group case_management.RuleGroup) ([]*case_management.Rule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByRuleGroup", ctx, group)
	ret0, _ := ret[0].([]*case_management.Rule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByRuleGroup indicates an expected call of GetByRuleGroup.
func (mr *MockRuleDaoMockRecorder) GetByRuleGroup(ctx, group interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByRuleGroup", reflect.TypeOf((*MockRuleDao)(nil).GetByRuleGroup), ctx, group)
}

// GetLatestByName mocks base method.
func (m *MockRuleDao) GetLatestByName(ctx context.Context, name string) (*case_management.Rule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestByName", ctx, name)
	ret0, _ := ret[0].(*case_management.Rule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestByName indicates an expected call of GetLatestByName.
func (mr *MockRuleDaoMockRecorder) GetLatestByName(ctx, name interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestByName", reflect.TypeOf((*MockRuleDao)(nil).GetLatestByName), ctx, name)
}

// Update mocks base method.
func (m *MockRuleDao) Update(ctx context.Context, rule *case_management.Rule, updateMask []case_management.RuleFieldMask) (*case_management.Rule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, rule, updateMask)
	ret0, _ := ret[0].(*case_management.Rule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Update indicates an expected call of Update.
func (mr *MockRuleDaoMockRecorder) Update(ctx, rule, updateMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockRuleDao)(nil).Update), ctx, rule, updateMask)
}

// MockAlertDao is a mock of AlertDao interface.
type MockAlertDao struct {
	ctrl     *gomock.Controller
	recorder *MockAlertDaoMockRecorder
}

// MockAlertDaoMockRecorder is the mock recorder for MockAlertDao.
type MockAlertDaoMockRecorder struct {
	mock *MockAlertDao
}

// NewMockAlertDao creates a new mock instance.
func NewMockAlertDao(ctrl *gomock.Controller) *MockAlertDao {
	mock := &MockAlertDao{ctrl: ctrl}
	mock.recorder = &MockAlertDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAlertDao) EXPECT() *MockAlertDaoMockRecorder {
	return m.recorder
}

// AggregateForActor mocks base method.
func (m *MockAlertDao) AggregateForActor(ctx context.Context, actorId string, options ...storagev2.FilterOption) ([]*case_management.AlertAggregateForActor, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, actorId}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AggregateForActor", varargs...)
	ret0, _ := ret[0].([]*case_management.AlertAggregateForActor)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AggregateForActor indicates an expected call of AggregateForActor.
func (mr *MockAlertDaoMockRecorder) AggregateForActor(ctx, actorId interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, actorId}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AggregateForActor", reflect.TypeOf((*MockAlertDao)(nil).AggregateForActor), varargs...)
}

// Create mocks base method.
func (m *MockAlertDao) Create(ctx context.Context, alert *case_management.Alert) (*case_management.Alert, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, alert)
	ret0, _ := ret[0].(*case_management.Alert)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockAlertDaoMockRecorder) Create(ctx, alert interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockAlertDao)(nil).Create), ctx, alert)
}

// GetByAccountId mocks base method.
func (m *MockAlertDao) GetByAccountId(ctx context.Context, accountId string, options ...storagev2.FilterOption) ([]*case_management.Alert, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, accountId}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByAccountId", varargs...)
	ret0, _ := ret[0].([]*case_management.Alert)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByAccountId indicates an expected call of GetByAccountId.
func (mr *MockAlertDaoMockRecorder) GetByAccountId(ctx, accountId interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, accountId}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByAccountId", reflect.TypeOf((*MockAlertDao)(nil).GetByAccountId), varargs...)
}

// GetByActorId mocks base method.
func (m *MockAlertDao) GetByActorId(ctx context.Context, actorId string, selectMask []case_management.AlertFieldMask, limit int, options ...storagev2.FilterOption) ([]*case_management.Alert, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, actorId, selectMask, limit}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByActorId", varargs...)
	ret0, _ := ret[0].([]*case_management.Alert)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorId indicates an expected call of GetByActorId.
func (mr *MockAlertDaoMockRecorder) GetByActorId(ctx, actorId, selectMask, limit interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, actorId, selectMask, limit}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorId", reflect.TypeOf((*MockAlertDao)(nil).GetByActorId), varargs...)
}

// GetByCaseId mocks base method.
func (m *MockAlertDao) GetByCaseId(ctx context.Context, caseId string, options ...storagev2.FilterOption) ([]*case_management.Alert, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, caseId}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByCaseId", varargs...)
	ret0, _ := ret[0].([]*case_management.Alert)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByCaseId indicates an expected call of GetByCaseId.
func (mr *MockAlertDaoMockRecorder) GetByCaseId(ctx, caseId interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, caseId}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByCaseId", reflect.TypeOf((*MockAlertDao)(nil).GetByCaseId), varargs...)
}

// GetByEntity mocks base method.
func (m *MockAlertDao) GetByEntity(ctx context.Context, entityType enums.EntityType, entityId string, options ...storagev2.FilterOption) ([]*case_management.Alert, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, entityType, entityId}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByEntity", varargs...)
	ret0, _ := ret[0].([]*case_management.Alert)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByEntity indicates an expected call of GetByEntity.
func (mr *MockAlertDaoMockRecorder) GetByEntity(ctx, entityType, entityId interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, entityType, entityId}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByEntity", reflect.TypeOf((*MockAlertDao)(nil).GetByEntity), varargs...)
}

// Update mocks base method.
func (m *MockAlertDao) Update(ctx context.Context, alert *case_management.Alert, updateMask []case_management.AlertFieldMask) (*case_management.Alert, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, alert, updateMask)
	ret0, _ := ret[0].(*case_management.Alert)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Update indicates an expected call of Update.
func (mr *MockAlertDaoMockRecorder) Update(ctx, alert, updateMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockAlertDao)(nil).Update), ctx, alert, updateMask)
}

// MockCommentDao is a mock of CommentDao interface.
type MockCommentDao struct {
	ctrl     *gomock.Controller
	recorder *MockCommentDaoMockRecorder
}

// MockCommentDaoMockRecorder is the mock recorder for MockCommentDao.
type MockCommentDaoMockRecorder struct {
	mock *MockCommentDao
}

// NewMockCommentDao creates a new mock instance.
func NewMockCommentDao(ctrl *gomock.Controller) *MockCommentDao {
	mock := &MockCommentDao{ctrl: ctrl}
	mock.recorder = &MockCommentDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCommentDao) EXPECT() *MockCommentDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockCommentDao) Create(ctx context.Context, comment *review.Comment) (*review.Comment, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, comment)
	ret0, _ := ret[0].(*review.Comment)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockCommentDaoMockRecorder) Create(ctx, comment interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockCommentDao)(nil).Create), ctx, comment)
}

// GetByCaseIds mocks base method.
func (m *MockCommentDao) GetByCaseIds(ctx context.Context, caseIds []string) ([]*review.Comment, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByCaseIds", ctx, caseIds)
	ret0, _ := ret[0].([]*review.Comment)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByCaseIds indicates an expected call of GetByCaseIds.
func (mr *MockCommentDaoMockRecorder) GetByCaseIds(ctx, caseIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByCaseIds", reflect.TypeOf((*MockCommentDao)(nil).GetByCaseIds), ctx, caseIds)
}

// GetByQuery mocks base method.
func (m *MockCommentDao) GetByQuery(ctx context.Context, query *review.CommentQuery) ([]*review.Comment, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByQuery", ctx, query)
	ret0, _ := ret[0].([]*review.Comment)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByQuery indicates an expected call of GetByQuery.
func (mr *MockCommentDaoMockRecorder) GetByQuery(ctx, query interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByQuery", reflect.TypeOf((*MockCommentDao)(nil).GetByQuery), ctx, query)
}

// MockAnnotationDao is a mock of AnnotationDao interface.
type MockAnnotationDao struct {
	ctrl     *gomock.Controller
	recorder *MockAnnotationDaoMockRecorder
}

// MockAnnotationDaoMockRecorder is the mock recorder for MockAnnotationDao.
type MockAnnotationDaoMockRecorder struct {
	mock *MockAnnotationDao
}

// NewMockAnnotationDao creates a new mock instance.
func NewMockAnnotationDao(ctrl *gomock.Controller) *MockAnnotationDao {
	mock := &MockAnnotationDao{ctrl: ctrl}
	mock.recorder = &MockAnnotationDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAnnotationDao) EXPECT() *MockAnnotationDaoMockRecorder {
	return m.recorder
}

// BulkCreate mocks base method.
func (m *MockAnnotationDao) BulkCreate(ctx context.Context, annotations []*review.Annotation) ([]*review.Annotation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BulkCreate", ctx, annotations)
	ret0, _ := ret[0].([]*review.Annotation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BulkCreate indicates an expected call of BulkCreate.
func (mr *MockAnnotationDaoMockRecorder) BulkCreate(ctx, annotations interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BulkCreate", reflect.TypeOf((*MockAnnotationDao)(nil).BulkCreate), ctx, annotations)
}

// Create mocks base method.
func (m *MockAnnotationDao) Create(ctx context.Context, annotation *review.Annotation) (*review.Annotation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, annotation)
	ret0, _ := ret[0].(*review.Annotation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockAnnotationDaoMockRecorder) Create(ctx, annotation interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockAnnotationDao)(nil).Create), ctx, annotation)
}

// GetByCaseIds mocks base method.
func (m *MockAnnotationDao) GetByCaseIds(ctx context.Context, caseIds []string) ([]*review.Annotation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByCaseIds", ctx, caseIds)
	ret0, _ := ret[0].([]*review.Annotation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByCaseIds indicates an expected call of GetByCaseIds.
func (mr *MockAnnotationDaoMockRecorder) GetByCaseIds(ctx, caseIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByCaseIds", reflect.TypeOf((*MockAnnotationDao)(nil).GetByCaseIds), ctx, caseIds)
}

// GetByQuery mocks base method.
func (m *MockAnnotationDao) GetByQuery(ctx context.Context, query *case_management.AnnotationQuery) ([]*review.Annotation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByQuery", ctx, query)
	ret0, _ := ret[0].([]*review.Annotation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByQuery indicates an expected call of GetByQuery.
func (mr *MockAnnotationDaoMockRecorder) GetByQuery(ctx, query interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByQuery", reflect.TypeOf((*MockAnnotationDao)(nil).GetByQuery), ctx, query)
}

// MockAllowedAnnotationDao is a mock of AllowedAnnotationDao interface.
type MockAllowedAnnotationDao struct {
	ctrl     *gomock.Controller
	recorder *MockAllowedAnnotationDaoMockRecorder
}

// MockAllowedAnnotationDaoMockRecorder is the mock recorder for MockAllowedAnnotationDao.
type MockAllowedAnnotationDaoMockRecorder struct {
	mock *MockAllowedAnnotationDao
}

// NewMockAllowedAnnotationDao creates a new mock instance.
func NewMockAllowedAnnotationDao(ctrl *gomock.Controller) *MockAllowedAnnotationDao {
	mock := &MockAllowedAnnotationDao{ctrl: ctrl}
	mock.recorder = &MockAllowedAnnotationDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAllowedAnnotationDao) EXPECT() *MockAllowedAnnotationDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockAllowedAnnotationDao) Create(ctx context.Context, allowedAnnotation *review.AllowedAnnotation) (*review.AllowedAnnotation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, allowedAnnotation)
	ret0, _ := ret[0].(*review.AllowedAnnotation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockAllowedAnnotationDaoMockRecorder) Create(ctx, allowedAnnotation interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockAllowedAnnotationDao)(nil).Create), ctx, allowedAnnotation)
}

// GetBulkByAnnotationTypeId mocks base method.
func (m *MockAllowedAnnotationDao) GetBulkByAnnotationTypeId(ctx context.Context, id []string) ([]*review.AllowedAnnotation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBulkByAnnotationTypeId", ctx, id)
	ret0, _ := ret[0].([]*review.AllowedAnnotation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBulkByAnnotationTypeId indicates an expected call of GetBulkByAnnotationTypeId.
func (mr *MockAllowedAnnotationDaoMockRecorder) GetBulkByAnnotationTypeId(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBulkByAnnotationTypeId", reflect.TypeOf((*MockAllowedAnnotationDao)(nil).GetBulkByAnnotationTypeId), ctx, id)
}

// GetBulkById mocks base method.
func (m *MockAllowedAnnotationDao) GetBulkById(ctx context.Context, id []string) ([]*review.AllowedAnnotation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBulkById", ctx, id)
	ret0, _ := ret[0].([]*review.AllowedAnnotation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBulkById indicates an expected call of GetBulkById.
func (mr *MockAllowedAnnotationDaoMockRecorder) GetBulkById(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBulkById", reflect.TypeOf((*MockAllowedAnnotationDao)(nil).GetBulkById), ctx, id)
}

// GetByQuery mocks base method.
func (m *MockAllowedAnnotationDao) GetByQuery(ctx context.Context, query *review.AllowedAnnotationQuery) ([]*review.AllowedAnnotation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByQuery", ctx, query)
	ret0, _ := ret[0].([]*review.AllowedAnnotation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByQuery indicates an expected call of GetByQuery.
func (mr *MockAllowedAnnotationDaoMockRecorder) GetByQuery(ctx, query interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByQuery", reflect.TypeOf((*MockAllowedAnnotationDao)(nil).GetByQuery), ctx, query)
}

// MockSuggestedActionDao is a mock of SuggestedActionDao interface.
type MockSuggestedActionDao struct {
	ctrl     *gomock.Controller
	recorder *MockSuggestedActionDaoMockRecorder
}

// MockSuggestedActionDaoMockRecorder is the mock recorder for MockSuggestedActionDao.
type MockSuggestedActionDaoMockRecorder struct {
	mock *MockSuggestedActionDao
}

// NewMockSuggestedActionDao creates a new mock instance.
func NewMockSuggestedActionDao(ctrl *gomock.Controller) *MockSuggestedActionDao {
	mock := &MockSuggestedActionDao{ctrl: ctrl}
	mock.recorder = &MockSuggestedActionDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSuggestedActionDao) EXPECT() *MockSuggestedActionDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockSuggestedActionDao) Create(ctx context.Context, suggestedAction *case_management.SuggestedAction) (*case_management.SuggestedAction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, suggestedAction)
	ret0, _ := ret[0].(*case_management.SuggestedAction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockSuggestedActionDaoMockRecorder) Create(ctx, suggestedAction interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockSuggestedActionDao)(nil).Create), ctx, suggestedAction)
}

// GetByRuleID mocks base method.
func (m *MockSuggestedActionDao) GetByRuleID(ctx context.Context, ruleId string) ([]*case_management.SuggestedAction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByRuleID", ctx, ruleId)
	ret0, _ := ret[0].([]*case_management.SuggestedAction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByRuleID indicates an expected call of GetByRuleID.
func (mr *MockSuggestedActionDaoMockRecorder) GetByRuleID(ctx, ruleId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByRuleID", reflect.TypeOf((*MockSuggestedActionDao)(nil).GetByRuleID), ctx, ruleId)
}

// MockRuleReviewTypeMappingsDao is a mock of RuleReviewTypeMappingsDao interface.
type MockRuleReviewTypeMappingsDao struct {
	ctrl     *gomock.Controller
	recorder *MockRuleReviewTypeMappingsDaoMockRecorder
}

// MockRuleReviewTypeMappingsDaoMockRecorder is the mock recorder for MockRuleReviewTypeMappingsDao.
type MockRuleReviewTypeMappingsDaoMockRecorder struct {
	mock *MockRuleReviewTypeMappingsDao
}

// NewMockRuleReviewTypeMappingsDao creates a new mock instance.
func NewMockRuleReviewTypeMappingsDao(ctrl *gomock.Controller) *MockRuleReviewTypeMappingsDao {
	mock := &MockRuleReviewTypeMappingsDao{ctrl: ctrl}
	mock.recorder = &MockRuleReviewTypeMappingsDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRuleReviewTypeMappingsDao) EXPECT() *MockRuleReviewTypeMappingsDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockRuleReviewTypeMappingsDao) Create(ctx context.Context, ruleReviewTypeMapping *case_management.RuleReviewTypeMapping) (*case_management.RuleReviewTypeMapping, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, ruleReviewTypeMapping)
	ret0, _ := ret[0].(*case_management.RuleReviewTypeMapping)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockRuleReviewTypeMappingsDaoMockRecorder) Create(ctx, ruleReviewTypeMapping interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockRuleReviewTypeMappingsDao)(nil).Create), ctx, ruleReviewTypeMapping)
}

// Delete mocks base method.
func (m *MockRuleReviewTypeMappingsDao) Delete(ctx context.Context, ruleReviewTypeMapping *case_management.RuleReviewTypeMapping) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, ruleReviewTypeMapping)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockRuleReviewTypeMappingsDaoMockRecorder) Delete(ctx, ruleReviewTypeMapping interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockRuleReviewTypeMappingsDao)(nil).Delete), ctx, ruleReviewTypeMapping)
}

// GetByRuleId mocks base method.
func (m *MockRuleReviewTypeMappingsDao) GetByRuleId(ctx context.Context, ruleId string) ([]*case_management.RuleReviewTypeMapping, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByRuleId", ctx, ruleId)
	ret0, _ := ret[0].([]*case_management.RuleReviewTypeMapping)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByRuleId indicates an expected call of GetByRuleId.
func (mr *MockRuleReviewTypeMappingsDaoMockRecorder) GetByRuleId(ctx, ruleId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByRuleId", reflect.TypeOf((*MockRuleReviewTypeMappingsDao)(nil).GetByRuleId), ctx, ruleId)
}

// MockRiskyUserRequiredInfoDao is a mock of RiskyUserRequiredInfoDao interface.
type MockRiskyUserRequiredInfoDao struct {
	ctrl     *gomock.Controller
	recorder *MockRiskyUserRequiredInfoDaoMockRecorder
}

// MockRiskyUserRequiredInfoDaoMockRecorder is the mock recorder for MockRiskyUserRequiredInfoDao.
type MockRiskyUserRequiredInfoDaoMockRecorder struct {
	mock *MockRiskyUserRequiredInfoDao
}

// NewMockRiskyUserRequiredInfoDao creates a new mock instance.
func NewMockRiskyUserRequiredInfoDao(ctrl *gomock.Controller) *MockRiskyUserRequiredInfoDao {
	mock := &MockRiskyUserRequiredInfoDao{ctrl: ctrl}
	mock.recorder = &MockRiskyUserRequiredInfoDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRiskyUserRequiredInfoDao) EXPECT() *MockRiskyUserRequiredInfoDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockRiskyUserRequiredInfoDao) Create(ctx context.Context, riskyUserRequiredInfo *case_management.RiskyUserRequiredInfo) (*case_management.RiskyUserRequiredInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, riskyUserRequiredInfo)
	ret0, _ := ret[0].(*case_management.RiskyUserRequiredInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockRiskyUserRequiredInfoDaoMockRecorder) Create(ctx, riskyUserRequiredInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockRiskyUserRequiredInfoDao)(nil).Create), ctx, riskyUserRequiredInfo)
}

// GetByIds mocks base method.
func (m *MockRiskyUserRequiredInfoDao) GetByIds(ctx context.Context, ids []string) ([]*case_management.RiskyUserRequiredInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByIds", ctx, ids)
	ret0, _ := ret[0].([]*case_management.RiskyUserRequiredInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByIds indicates an expected call of GetByIds.
func (mr *MockRiskyUserRequiredInfoDaoMockRecorder) GetByIds(ctx, ids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByIds", reflect.TypeOf((*MockRiskyUserRequiredInfoDao)(nil).GetByIds), ctx, ids)
}

// MockRiskyUserRequiredInfoMappingDao is a mock of RiskyUserRequiredInfoMappingDao interface.
type MockRiskyUserRequiredInfoMappingDao struct {
	ctrl     *gomock.Controller
	recorder *MockRiskyUserRequiredInfoMappingDaoMockRecorder
}

// MockRiskyUserRequiredInfoMappingDaoMockRecorder is the mock recorder for MockRiskyUserRequiredInfoMappingDao.
type MockRiskyUserRequiredInfoMappingDaoMockRecorder struct {
	mock *MockRiskyUserRequiredInfoMappingDao
}

// NewMockRiskyUserRequiredInfoMappingDao creates a new mock instance.
func NewMockRiskyUserRequiredInfoMappingDao(ctrl *gomock.Controller) *MockRiskyUserRequiredInfoMappingDao {
	mock := &MockRiskyUserRequiredInfoMappingDao{ctrl: ctrl}
	mock.recorder = &MockRiskyUserRequiredInfoMappingDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRiskyUserRequiredInfoMappingDao) EXPECT() *MockRiskyUserRequiredInfoMappingDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockRiskyUserRequiredInfoMappingDao) Create(ctx context.Context, riskyUserRequiredInfoMapping *case_management.RiskyUserRequiredInfoMapping) (*case_management.RiskyUserRequiredInfoMapping, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, riskyUserRequiredInfoMapping)
	ret0, _ := ret[0].(*case_management.RiskyUserRequiredInfoMapping)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockRiskyUserRequiredInfoMappingDaoMockRecorder) Create(ctx, riskyUserRequiredInfoMapping interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockRiskyUserRequiredInfoMappingDao)(nil).Create), ctx, riskyUserRequiredInfoMapping)
}

// GetByEntity mocks base method.
func (m *MockRiskyUserRequiredInfoMappingDao) GetByEntity(ctx context.Context, entityType case_management.RiskyUserRequiredInfoMapping_EntityType, entityId string) ([]*case_management.RiskyUserRequiredInfoMapping, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByEntity", ctx, entityType, entityId)
	ret0, _ := ret[0].([]*case_management.RiskyUserRequiredInfoMapping)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByEntity indicates an expected call of GetByEntity.
func (mr *MockRiskyUserRequiredInfoMappingDaoMockRecorder) GetByEntity(ctx, entityType, entityId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByEntity", reflect.TypeOf((*MockRiskyUserRequiredInfoMappingDao)(nil).GetByEntity), ctx, entityType, entityId)
}

// MockRulePrecisionDao is a mock of RulePrecisionDao interface.
type MockRulePrecisionDao struct {
	ctrl     *gomock.Controller
	recorder *MockRulePrecisionDaoMockRecorder
}

// MockRulePrecisionDaoMockRecorder is the mock recorder for MockRulePrecisionDao.
type MockRulePrecisionDaoMockRecorder struct {
	mock *MockRulePrecisionDao
}

// NewMockRulePrecisionDao creates a new mock instance.
func NewMockRulePrecisionDao(ctrl *gomock.Controller) *MockRulePrecisionDao {
	mock := &MockRulePrecisionDao{ctrl: ctrl}
	mock.recorder = &MockRulePrecisionDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRulePrecisionDao) EXPECT() *MockRulePrecisionDaoMockRecorder {
	return m.recorder
}

// CreateWithVersionUpgrade mocks base method.
func (m *MockRulePrecisionDao) CreateWithVersionUpgrade(ctx context.Context, rulePrecision *case_management.RulePrecision) (*case_management.RulePrecision, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateWithVersionUpgrade", ctx, rulePrecision)
	ret0, _ := ret[0].(*case_management.RulePrecision)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateWithVersionUpgrade indicates an expected call of CreateWithVersionUpgrade.
func (mr *MockRulePrecisionDaoMockRecorder) CreateWithVersionUpgrade(ctx, rulePrecision interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateWithVersionUpgrade", reflect.TypeOf((*MockRulePrecisionDao)(nil).CreateWithVersionUpgrade), ctx, rulePrecision)
}

// GetLatestByRuleId mocks base method.
func (m *MockRulePrecisionDao) GetLatestByRuleId(ctx context.Context, ruleId string) (*case_management.RulePrecision, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestByRuleId", ctx, ruleId)
	ret0, _ := ret[0].(*case_management.RulePrecision)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestByRuleId indicates an expected call of GetLatestByRuleId.
func (mr *MockRulePrecisionDaoMockRecorder) GetLatestByRuleId(ctx, ruleId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestByRuleId", reflect.TypeOf((*MockRulePrecisionDao)(nil).GetLatestByRuleId), ctx, ruleId)
}

// MockQuestionEntityMappingDao is a mock of QuestionEntityMappingDao interface.
type MockQuestionEntityMappingDao struct {
	ctrl     *gomock.Controller
	recorder *MockQuestionEntityMappingDaoMockRecorder
}

// MockQuestionEntityMappingDaoMockRecorder is the mock recorder for MockQuestionEntityMappingDao.
type MockQuestionEntityMappingDaoMockRecorder struct {
	mock *MockQuestionEntityMappingDao
}

// NewMockQuestionEntityMappingDao creates a new mock instance.
func NewMockQuestionEntityMappingDao(ctrl *gomock.Controller) *MockQuestionEntityMappingDao {
	mock := &MockQuestionEntityMappingDao{ctrl: ctrl}
	mock.recorder = &MockQuestionEntityMappingDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockQuestionEntityMappingDao) EXPECT() *MockQuestionEntityMappingDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockQuestionEntityMappingDao) Create(ctx context.Context, mapping *form.QuestionEntityMapping) (*form.QuestionEntityMapping, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, mapping)
	ret0, _ := ret[0].(*form.QuestionEntityMapping)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockQuestionEntityMappingDaoMockRecorder) Create(ctx, mapping interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockQuestionEntityMappingDao)(nil).Create), ctx, mapping)
}

// GetByEntity mocks base method.
func (m *MockQuestionEntityMappingDao) GetByEntity(ctx context.Context, entityType form.EntityType, entityId string, options ...storagev2.FilterOption) ([]*form.QuestionEntityMapping, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, entityType, entityId}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByEntity", varargs...)
	ret0, _ := ret[0].([]*form.QuestionEntityMapping)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByEntity indicates an expected call of GetByEntity.
func (mr *MockQuestionEntityMappingDaoMockRecorder) GetByEntity(ctx, entityType, entityId interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, entityType, entityId}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByEntity", reflect.TypeOf((*MockQuestionEntityMappingDao)(nil).GetByEntity), varargs...)
}

// MockQuestionDao is a mock of QuestionDao interface.
type MockQuestionDao struct {
	ctrl     *gomock.Controller
	recorder *MockQuestionDaoMockRecorder
}

// MockQuestionDaoMockRecorder is the mock recorder for MockQuestionDao.
type MockQuestionDaoMockRecorder struct {
	mock *MockQuestionDao
}

// NewMockQuestionDao creates a new mock instance.
func NewMockQuestionDao(ctrl *gomock.Controller) *MockQuestionDao {
	mock := &MockQuestionDao{ctrl: ctrl}
	mock.recorder = &MockQuestionDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockQuestionDao) EXPECT() *MockQuestionDaoMockRecorder {
	return m.recorder
}

// BulkGet mocks base method.
func (m *MockQuestionDao) BulkGet(ctx context.Context, field form.QuestionFieldMask, value *structpb.Value, options ...storagev2.FilterOption) ([]*form.Question, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, field, value}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BulkGet", varargs...)
	ret0, _ := ret[0].([]*form.Question)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BulkGet indicates an expected call of BulkGet.
func (mr *MockQuestionDaoMockRecorder) BulkGet(ctx, field, value interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, field, value}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BulkGet", reflect.TypeOf((*MockQuestionDao)(nil).BulkGet), varargs...)
}

// CreateWithVersionUpgrade mocks base method.
func (m *MockQuestionDao) CreateWithVersionUpgrade(ctx context.Context, question *form.Question) (*form.Question, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateWithVersionUpgrade", ctx, question)
	ret0, _ := ret[0].(*form.Question)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateWithVersionUpgrade indicates an expected call of CreateWithVersionUpgrade.
func (mr *MockQuestionDaoMockRecorder) CreateWithVersionUpgrade(ctx, question interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateWithVersionUpgrade", reflect.TypeOf((*MockQuestionDao)(nil).CreateWithVersionUpgrade), ctx, question)
}

// MockFormDao is a mock of FormDao interface.
type MockFormDao struct {
	ctrl     *gomock.Controller
	recorder *MockFormDaoMockRecorder
}

// MockFormDaoMockRecorder is the mock recorder for MockFormDao.
type MockFormDaoMockRecorder struct {
	mock *MockFormDao
}

// NewMockFormDao creates a new mock instance.
func NewMockFormDao(ctrl *gomock.Controller) *MockFormDao {
	mock := &MockFormDao{ctrl: ctrl}
	mock.recorder = &MockFormDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFormDao) EXPECT() *MockFormDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockFormDao) Create(ctx context.Context, formObj *form.Form) (*form.Form, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, formObj)
	ret0, _ := ret[0].(*form.Form)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockFormDaoMockRecorder) Create(ctx, formObj interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockFormDao)(nil).Create), ctx, formObj)
}

// Get mocks base method.
func (m *MockFormDao) Get(ctx context.Context, field form.FormFieldMask, value *structpb.Value, limit int, options ...storagev2.FilterOption) ([]*form.Form, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, field, value, limit}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Get", varargs...)
	ret0, _ := ret[0].([]*form.Form)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockFormDaoMockRecorder) Get(ctx, field, value, limit interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, field, value, limit}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockFormDao)(nil).Get), varargs...)
}

// Update mocks base method.
func (m *MockFormDao) Update(ctx context.Context, formObj *form.Form, updateMasks []form.FormFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, formObj, updateMasks)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockFormDaoMockRecorder) Update(ctx, formObj, updateMasks interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockFormDao)(nil).Update), ctx, formObj, updateMasks)
}

// MockQuestionResponseDao is a mock of QuestionResponseDao interface.
type MockQuestionResponseDao struct {
	ctrl     *gomock.Controller
	recorder *MockQuestionResponseDaoMockRecorder
}

// MockQuestionResponseDaoMockRecorder is the mock recorder for MockQuestionResponseDao.
type MockQuestionResponseDaoMockRecorder struct {
	mock *MockQuestionResponseDao
}

// NewMockQuestionResponseDao creates a new mock instance.
func NewMockQuestionResponseDao(ctrl *gomock.Controller) *MockQuestionResponseDao {
	mock := &MockQuestionResponseDao{ctrl: ctrl}
	mock.recorder = &MockQuestionResponseDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockQuestionResponseDao) EXPECT() *MockQuestionResponseDaoMockRecorder {
	return m.recorder
}

// BulkCreate mocks base method.
func (m *MockQuestionResponseDao) BulkCreate(ctx context.Context, responses []*form.QuestionResponse) ([]*form.QuestionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BulkCreate", ctx, responses)
	ret0, _ := ret[0].([]*form.QuestionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BulkCreate indicates an expected call of BulkCreate.
func (mr *MockQuestionResponseDaoMockRecorder) BulkCreate(ctx, responses interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BulkCreate", reflect.TypeOf((*MockQuestionResponseDao)(nil).BulkCreate), ctx, responses)
}

// GetByFormId mocks base method.
func (m *MockQuestionResponseDao) GetByFormId(ctx context.Context, formId string, options ...storagev2.FilterOption) ([]*form.QuestionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, formId}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByFormId", varargs...)
	ret0, _ := ret[0].([]*form.QuestionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByFormId indicates an expected call of GetByFormId.
func (mr *MockQuestionResponseDaoMockRecorder) GetByFormId(ctx, formId interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, formId}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByFormId", reflect.TypeOf((*MockQuestionResponseDao)(nil).GetByFormId), varargs...)
}

// MockFormQuestionMappingDao is a mock of FormQuestionMappingDao interface.
type MockFormQuestionMappingDao struct {
	ctrl     *gomock.Controller
	recorder *MockFormQuestionMappingDaoMockRecorder
}

// MockFormQuestionMappingDaoMockRecorder is the mock recorder for MockFormQuestionMappingDao.
type MockFormQuestionMappingDaoMockRecorder struct {
	mock *MockFormQuestionMappingDao
}

// NewMockFormQuestionMappingDao creates a new mock instance.
func NewMockFormQuestionMappingDao(ctrl *gomock.Controller) *MockFormQuestionMappingDao {
	mock := &MockFormQuestionMappingDao{ctrl: ctrl}
	mock.recorder = &MockFormQuestionMappingDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFormQuestionMappingDao) EXPECT() *MockFormQuestionMappingDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockFormQuestionMappingDao) Create(ctx context.Context, mapping *form.FormQuestionMapping) (*form.FormQuestionMapping, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, mapping)
	ret0, _ := ret[0].(*form.FormQuestionMapping)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockFormQuestionMappingDaoMockRecorder) Create(ctx, mapping interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockFormQuestionMappingDao)(nil).Create), ctx, mapping)
}

// GetByFormId mocks base method.
func (m *MockFormQuestionMappingDao) GetByFormId(ctx context.Context, formId string, options ...storagev2.FilterOption) ([]*form.FormQuestionMapping, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, formId}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByFormId", varargs...)
	ret0, _ := ret[0].([]*form.FormQuestionMapping)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByFormId indicates an expected call of GetByFormId.
func (mr *MockFormQuestionMappingDaoMockRecorder) GetByFormId(ctx, formId interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, formId}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByFormId", reflect.TypeOf((*MockFormQuestionMappingDao)(nil).GetByFormId), varargs...)
}

// MockAnnotationTypeDao is a mock of AnnotationTypeDao interface.
type MockAnnotationTypeDao struct {
	ctrl     *gomock.Controller
	recorder *MockAnnotationTypeDaoMockRecorder
}

// MockAnnotationTypeDaoMockRecorder is the mock recorder for MockAnnotationTypeDao.
type MockAnnotationTypeDaoMockRecorder struct {
	mock *MockAnnotationTypeDao
}

// NewMockAnnotationTypeDao creates a new mock instance.
func NewMockAnnotationTypeDao(ctrl *gomock.Controller) *MockAnnotationTypeDao {
	mock := &MockAnnotationTypeDao{ctrl: ctrl}
	mock.recorder = &MockAnnotationTypeDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAnnotationTypeDao) EXPECT() *MockAnnotationTypeDaoMockRecorder {
	return m.recorder
}

// BulkGetById mocks base method.
func (m *MockAnnotationTypeDao) BulkGetById(ctx context.Context, ids []string) ([]*review.AllowedAnnotationType, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BulkGetById", ctx, ids)
	ret0, _ := ret[0].([]*review.AllowedAnnotationType)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BulkGetById indicates an expected call of BulkGetById.
func (mr *MockAnnotationTypeDaoMockRecorder) BulkGetById(ctx, ids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BulkGetById", reflect.TypeOf((*MockAnnotationTypeDao)(nil).BulkGetById), ctx, ids)
}

// MockUIElementAnnotationTypeMappingDao is a mock of UIElementAnnotationTypeMappingDao interface.
type MockUIElementAnnotationTypeMappingDao struct {
	ctrl     *gomock.Controller
	recorder *MockUIElementAnnotationTypeMappingDaoMockRecorder
}

// MockUIElementAnnotationTypeMappingDaoMockRecorder is the mock recorder for MockUIElementAnnotationTypeMappingDao.
type MockUIElementAnnotationTypeMappingDaoMockRecorder struct {
	mock *MockUIElementAnnotationTypeMappingDao
}

// NewMockUIElementAnnotationTypeMappingDao creates a new mock instance.
func NewMockUIElementAnnotationTypeMappingDao(ctrl *gomock.Controller) *MockUIElementAnnotationTypeMappingDao {
	mock := &MockUIElementAnnotationTypeMappingDao{ctrl: ctrl}
	mock.recorder = &MockUIElementAnnotationTypeMappingDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUIElementAnnotationTypeMappingDao) EXPECT() *MockUIElementAnnotationTypeMappingDaoMockRecorder {
	return m.recorder
}

// GetByUIElement mocks base method.
func (m *MockUIElementAnnotationTypeMappingDao) GetByUIElement(ctx context.Context, uiElement *review.UIElement) ([]*review.UIElementAnnotationTypeMapping, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByUIElement", ctx, uiElement)
	ret0, _ := ret[0].([]*review.UIElementAnnotationTypeMapping)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByUIElement indicates an expected call of GetByUIElement.
func (mr *MockUIElementAnnotationTypeMappingDaoMockRecorder) GetByUIElement(ctx, uiElement interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByUIElement", reflect.TypeOf((*MockUIElementAnnotationTypeMappingDao)(nil).GetByUIElement), ctx, uiElement)
}

// MockTransactionBlockDao is a mock of TransactionBlockDao interface.
type MockTransactionBlockDao struct {
	ctrl     *gomock.Controller
	recorder *MockTransactionBlockDaoMockRecorder
}

// MockTransactionBlockDaoMockRecorder is the mock recorder for MockTransactionBlockDao.
type MockTransactionBlockDaoMockRecorder struct {
	mock *MockTransactionBlockDao
}

// NewMockTransactionBlockDao creates a new mock instance.
func NewMockTransactionBlockDao(ctrl *gomock.Controller) *MockTransactionBlockDao {
	mock := &MockTransactionBlockDao{ctrl: ctrl}
	mock.recorder = &MockTransactionBlockDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTransactionBlockDao) EXPECT() *MockTransactionBlockDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockTransactionBlockDao) Create(ctx context.Context, transactionBlock *case_management.TransactionBlock) (*case_management.TransactionBlock, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, transactionBlock)
	ret0, _ := ret[0].(*case_management.TransactionBlock)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockTransactionBlockDaoMockRecorder) Create(ctx, transactionBlock interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockTransactionBlockDao)(nil).Create), ctx, transactionBlock)
}

// GetByActorId mocks base method.
func (m *MockTransactionBlockDao) GetByActorId(ctx context.Context, actorId, blockType string, limit int, options ...storagev2.FilterOption) ([]*case_management.TransactionBlock, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, actorId, blockType, limit}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByActorId", varargs...)
	ret0, _ := ret[0].([]*case_management.TransactionBlock)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorId indicates an expected call of GetByActorId.
func (mr *MockTransactionBlockDaoMockRecorder) GetByActorId(ctx, actorId, blockType, limit interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, actorId, blockType, limit}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorId", reflect.TypeOf((*MockTransactionBlockDao)(nil).GetByActorId), varargs...)
}

// GetByAlertId mocks base method.
func (m *MockTransactionBlockDao) GetByAlertId(ctx context.Context, alertId, blockType string, limit int, options ...storagev2.FilterOption) ([]*case_management.TransactionBlock, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, alertId, blockType, limit}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByAlertId", varargs...)
	ret0, _ := ret[0].([]*case_management.TransactionBlock)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByAlertId indicates an expected call of GetByAlertId.
func (mr *MockTransactionBlockDaoMockRecorder) GetByAlertId(ctx, alertId, blockType, limit interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, alertId, blockType, limit}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByAlertId", reflect.TypeOf((*MockTransactionBlockDao)(nil).GetByAlertId), varargs...)
}

// GetById mocks base method.
func (m *MockTransactionBlockDao) GetById(ctx context.Context, id string) (*case_management.TransactionBlock, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", ctx, id)
	ret0, _ := ret[0].(*case_management.TransactionBlock)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockTransactionBlockDaoMockRecorder) GetById(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockTransactionBlockDao)(nil).GetById), ctx, id)
}

// Update mocks base method.
func (m *MockTransactionBlockDao) Update(ctx context.Context, transactionBlock *case_management.TransactionBlock) (*case_management.TransactionBlock, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, transactionBlock)
	ret0, _ := ret[0].(*case_management.TransactionBlock)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Update indicates an expected call of Update.
func (mr *MockTransactionBlockDaoMockRecorder) Update(ctx, transactionBlock interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockTransactionBlockDao)(nil).Update), ctx, transactionBlock)
}

// MockRiskAnalyticsDao is a mock of RiskAnalyticsDao interface.
type MockRiskAnalyticsDao struct {
	ctrl     *gomock.Controller
	recorder *MockRiskAnalyticsDaoMockRecorder
}

// MockRiskAnalyticsDaoMockRecorder is the mock recorder for MockRiskAnalyticsDao.
type MockRiskAnalyticsDaoMockRecorder struct {
	mock *MockRiskAnalyticsDao
}

// NewMockRiskAnalyticsDao creates a new mock instance.
func NewMockRiskAnalyticsDao(ctrl *gomock.Controller) *MockRiskAnalyticsDao {
	mock := &MockRiskAnalyticsDao{ctrl: ctrl}
	mock.recorder = &MockRiskAnalyticsDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRiskAnalyticsDao) EXPECT() *MockRiskAnalyticsDaoMockRecorder {
	return m.recorder
}

// GetCreditCounts mocks base method.
func (m *MockRiskAnalyticsDao) GetCreditCounts(ctx context.Context, actorId string, aggregateOption case_management.AggregateOption) ([]*case_management.TimeAggregatedCount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCreditCounts", ctx, actorId, aggregateOption)
	ret0, _ := ret[0].([]*case_management.TimeAggregatedCount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCreditCounts indicates an expected call of GetCreditCounts.
func (mr *MockRiskAnalyticsDaoMockRecorder) GetCreditCounts(ctx, actorId, aggregateOption interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCreditCounts", reflect.TypeOf((*MockRiskAnalyticsDao)(nil).GetCreditCounts), ctx, actorId, aggregateOption)
}

// GetCredits25kPlus mocks base method.
func (m *MockRiskAnalyticsDao) GetCredits25kPlus(ctx context.Context, actorId string, aggregateOption case_management.AggregateOption) ([]*case_management.TimeAggregatedCount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCredits25kPlus", ctx, actorId, aggregateOption)
	ret0, _ := ret[0].([]*case_management.TimeAggregatedCount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCredits25kPlus indicates an expected call of GetCredits25kPlus.
func (mr *MockRiskAnalyticsDaoMockRecorder) GetCredits25kPlus(ctx, actorId, aggregateOption interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCredits25kPlus", reflect.TypeOf((*MockRiskAnalyticsDao)(nil).GetCredits25kPlus), ctx, actorId, aggregateOption)
}

// GetLifetimeNewP2PCreditCounterparties mocks base method.
func (m *MockRiskAnalyticsDao) GetLifetimeNewP2PCreditCounterparties(ctx context.Context, actorId string, aggregateOption case_management.AggregateOption) ([]*case_management.TimeAggregatedCount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLifetimeNewP2PCreditCounterparties", ctx, actorId, aggregateOption)
	ret0, _ := ret[0].([]*case_management.TimeAggregatedCount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLifetimeNewP2PCreditCounterparties indicates an expected call of GetLifetimeNewP2PCreditCounterparties.
func (mr *MockRiskAnalyticsDaoMockRecorder) GetLifetimeNewP2PCreditCounterparties(ctx, actorId, aggregateOption interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLifetimeNewP2PCreditCounterparties", reflect.TypeOf((*MockRiskAnalyticsDao)(nil).GetLifetimeNewP2PCreditCounterparties), ctx, actorId, aggregateOption)
}
