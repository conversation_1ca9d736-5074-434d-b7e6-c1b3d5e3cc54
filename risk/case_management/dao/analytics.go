// nolint: goimports
package dao

import (
	"context"
	"errors"
	"fmt"
	"time"

	"cloud.google.com/go/bigquery"

	"go.uber.org/zap"
	"google.golang.org/api/iterator"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/pkg/storage/v2/analytics"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	cmPb "github.com/epifi/gamma/api/risk/case_management"
)

type RiskAnalyticsBigqueryDBResourceProvider struct {
	dbResourceProvider *storagev2.DBResourceProviderV2[*analytics.BigqueryInternalClient]
}

func NewRiskAnalyticsBigqueryDBResourceProvider(bqDBResourceProvider analytics.BigqueryDBResourceProvider) *RiskAnalyticsBigqueryDBResourceProvider {
	return &RiskAnalyticsBigqueryDBResourceProvider{dbResourceProvider: bqDBResourceProvider}
}

var _ RiskAnalyticsDao = &RiskAnalyticsBigqueryDBResourceProvider{}

// Helper function to generate date aggregation clauses based on AggregateOption
// Now works with the CTE structure where date components are pre-computed
func getDateAggregationClause(aggregateOption cmPb.AggregateOption) (selectClause, orderByClause string) {
	switch aggregateOption {
	case cmPb.AggregateOption_AGGREGATE_OPTION_WEEK_OVER_WEEK:
		return `
			year,
			week_of_year,
			NULL as month,
			NULL as day,
			CONCAT(CAST(year AS STRING), '-W', FORMAT('%02d', week_of_year)) as time_period_label, -- format: YYYY-Www (e.g., 2025-W05)
		`, `year, week_of_year`

	case cmPb.AggregateOption_AGGREGATE_OPTION_DAY_WISE:
		return `
			year,
			month,
			day,
			NULL as week_of_year,
			CONCAT(CAST(year AS STRING), '-', FORMAT('%02d', month), '-', FORMAT('%02d', day)) as time_period_label, -- format: YYYY-MM-DD (e.g., 2025-02-07)
		`, `year, month, day`

	default: // AGGREGATE_OPTION_MONTH_OVER_MONTH
		return `
			year,
			month,
			NULL as day,
			NULL as week_of_year,
			CONCAT(CAST(year AS STRING), '-', FORMAT('%02d', month)) as time_period_label, -- format: YYYY-MM (e.g., 2025-02)
		`, `year, month`
	}
}

// BigQueryTimeAggregatedResult represents the structure of our BigQuery result rows
type BigQueryTimeAggregatedResult struct {
	Year            bigquery.NullInt64  `bigquery:"year"`
	WeekOfYear      bigquery.NullInt64  `bigquery:"week_of_year"`
	Month           bigquery.NullInt64  `bigquery:"month"`
	Day             bigquery.NullInt64  `bigquery:"day"`
	TimePeriodLabel bigquery.NullString `bigquery:"time_period_label"`
	MetricValue     bigquery.NullInt64  `bigquery:"metric_value"`
}

// parseQueryResults parses BigQuery results into TimeAggregatedCount objects
func (r *RiskAnalyticsBigqueryDBResourceProvider) parseQueryResults(
	ctx context.Context,
	resultIter *bigquery.RowIterator,
	methodName string,
	actorId string,
) ([]*cmPb.TimeAggregatedCount, error) {
	var results []*cmPb.TimeAggregatedCount

	for {
		var row BigQueryTimeAggregatedResult
		err := resultIter.Next(&row)
		if errors.Is(err, iterator.Done) {
			logger.Info(ctx, fmt.Sprintf("BigQuery %s iteration done", methodName))
			break
		}
		if err != nil {
			logger.Error(ctx, fmt.Sprintf("error while scanning the %s query output", methodName), zap.Error(err))
			return nil, err
		}

		timeAggregatedCount := &cmPb.TimeAggregatedCount{}

		// Parse year (always present)
		var year int32
		if row.Year.Valid {
			year = int32(row.Year.Int64) //nolint:gosec
		}

		// Parse metric value
		if row.MetricValue.Valid {
			timeAggregatedCount.Count = row.MetricValue.Int64
		}

		// Parse time_period_label
		if row.TimePeriodLabel.Valid {
			timeAggregatedCount.TimePeriodLabel = row.TimePeriodLabel.StringVal
		}

		// Set the appropriate time key based on which fields are present
		// Weekly aggregation: year and week_of_year are populated
		//nolint:gocritic,gosec
		if row.WeekOfYear.Valid && !row.Month.Valid && !row.Day.Valid {
			week := int32(row.WeekOfYear.Int64)

			// Set the oneof time_key for weekly aggregation
			timeAggregatedCount.Time = &cmPb.TimeAggregatedCount_WeekKey{
				WeekKey: &cmPb.WeekKey{
					Year:  year,
					Month: 0, // Added this for future week of month feature instead of the week of the year feature.
					Week:  week,
				},
			}
		} else if row.Day.Valid && row.Month.Valid {
			// Daily aggregation: year, month, and day are populated
			month := int32(row.Month.Int64) //nolint:gosec
			day := int32(row.Day.Int64)     //nolint:gosec

			// Set the oneof time_key for daily aggregation
			timeAggregatedCount.Time = &cmPb.TimeAggregatedCount_DayKey{
				DayKey: &cmPb.DayKey{
					Year:  year,
					Month: month,
					Day:   day,
				},
			}
		} else if row.Month.Valid {
			// Monthly aggregation: year and month are populated
			month := int32(row.Month.Int64) //nolint:gosec

			// Set the oneof time_key for monthly aggregation
			timeAggregatedCount.Time = &cmPb.TimeAggregatedCount_MonthKey{
				MonthKey: &cmPb.MonthKey{
					Year:  year,
					Month: month,
				},
			}
		}

		logger.Debug(ctx, fmt.Sprintf("BigQuery %s scan result", methodName), zap.Any("result", timeAggregatedCount))
		results = append(results, timeAggregatedCount)
	}

	logger.Info(ctx, fmt.Sprintf("%s completed", methodName),
		zap.String("actor_id", actorId),
		zap.Int("periods_count", len(results)))

	return results, nil
}

// GetCreditCounts returns the total count of successful credit transactions for an actor
// aggregated by the specified aggregation type for the past 1 year
// nolint:dupl
func (r *RiskAnalyticsBigqueryDBResourceProvider) GetCreditCounts(ctx context.Context, actorId string, aggregateOption cmPb.AggregateOption) ([]*cmPb.TimeAggregatedCount, error) {
	defer metric_util.TrackDuration("risk/case_management/dao", "RiskAnalyticsBigqueryDBResourceProvider", "GetCreditCounts", time.Now())

	if actorId == "" {
		return nil, fmt.Errorf("actor_id cannot be empty")
	}

	// Clone the context with timeout for long-running query
	ctx, cancelFn := context.WithTimeout(epificontext.CloneCtx(ctx), 10*time.Minute)
	defer cancelFn()

	internalClient, err := r.dbResourceProvider.GetResourceForOwner(commontypes.Owner_OWNER_EPIFI_TECH)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("error in get bigquery client for given resource provider %s", commontypes.Owner_OWNER_EPIFI_TECH.String()), zap.Error(err))
		return nil, err
	}

	// Get dynamic aggregation clauses
	selectClause, orderByClause := getDateAggregationClause(aggregateOption)

	// Updated SQL query with improved date filtering and aggregation
	sqlQuery := fmt.Sprintf(`
		WITH date_aggregated AS (
			SELECT
				EXTRACT(YEAR FROM DATE(COALESCE(order_created_at_ist, credited_at_ist))) as year,
				EXTRACT(WEEK FROM DATE(COALESCE(order_created_at_ist, credited_at_ist))) as week_of_year,
				EXTRACT(MONTH FROM DATE(COALESCE(order_created_at_ist, credited_at_ist))) as month,
				EXTRACT(DAY FROM DATE(COALESCE(order_created_at_ist, credited_at_ist))) as day,
				COUNT(*) as metric_value
			FROM %s.pay_transactions
			WHERE (actor_to_id = '%s' or actor_from_id = '%s')
				AND credit_debit = 'Credit'
				AND transaction_status = 'SUCCESS'
				AND DATE(COALESCE(order_created_at_ist, credited_at_ist)) >= DATE_TRUNC(DATE_SUB(CURRENT_DATE(), INTERVAL 1 YEAR), MONTH)
				AND COALESCE(order_created_at_ist, credited_at_ist) IS NOT NULL
			GROUP BY
				EXTRACT(YEAR FROM DATE(COALESCE(order_created_at_ist, credited_at_ist))),
				EXTRACT(WEEK FROM DATE(COALESCE(order_created_at_ist, credited_at_ist))),
				EXTRACT(MONTH FROM DATE(COALESCE(order_created_at_ist, credited_at_ist))),
				EXTRACT(DAY FROM DATE(COALESCE(order_created_at_ist, credited_at_ist)))
		)
		SELECT
			%s
			SUM(metric_value) as metric_value
		FROM date_aggregated
		GROUP BY 1, 2
		ORDER BY %s`,
		internalClient.Dataset(),
		actorId, actorId,
		selectClause,
		orderByClause,
	)

	// Log the query for debugging
	logger.Info(ctx, fmt.Sprintf("sql query submitted to bigquery for GetCreditCounts: %s", sqlQuery),
		zap.String("aggregation_type", aggregateOption.String()))

	job, err := internalClient.AsyncQuery(ctx, sqlQuery)
	if err != nil {
		logger.Error(ctx, "unable to submit job to bigquery for GetCreditCounts", zap.Error(err))
		return nil, err
	}
	logger.Info(ctx, fmt.Sprintf("Submitted GetCreditCounts job to bigquery, id %s", job.ID()))

	resultIter, err := job.Read(ctx)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("error while polling on bigquery job %s for GetCreditCounts", job.ID()), zap.Error(err))
		return nil, err
	}

	return r.parseQueryResults(ctx, resultIter, "GetCreditCounts", actorId)
}

// GetCredits25kPlus returns the count of successful credit transactions greater than 25k for an actor
// aggregated by the specified aggregation type for the past 1 year
// nolint:dupl
func (r *RiskAnalyticsBigqueryDBResourceProvider) GetCredits25kPlus(ctx context.Context, actorId string, aggregateOption cmPb.AggregateOption) ([]*cmPb.TimeAggregatedCount, error) {
	defer metric_util.TrackDuration("risk/case_management/dao", "RiskAnalyticsBigqueryDBResourceProvider", "GetCredits25kPlus", time.Now())

	if actorId == "" {
		return nil, fmt.Errorf("actor_id cannot be empty")
	}

	// Clone the context with timeout for long-running query
	ctx, cancelFn := context.WithTimeout(epificontext.CloneCtx(ctx), 10*time.Minute)
	defer cancelFn()

	internalClient, err := r.dbResourceProvider.GetResourceForOwner(commontypes.Owner_OWNER_EPIFI_TECH)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("error in get bigquery client for given resource provider %s", commontypes.Owner_OWNER_EPIFI_TECH.String()), zap.Error(err))
		return nil, err
	}

	// Get dynamic aggregation clauses
	selectClause, orderByClause := getDateAggregationClause(aggregateOption)

	// Updated SQL query with improved date filtering and 25k+ amount filtering
	sqlQuery := fmt.Sprintf(`
		WITH date_aggregated AS (
			SELECT
				EXTRACT(YEAR FROM DATE(COALESCE(order_created_at_ist, credited_at_ist))) as year,
				EXTRACT(WEEK FROM DATE(COALESCE(order_created_at_ist, credited_at_ist))) as week_of_year,
				EXTRACT(MONTH FROM DATE(COALESCE(order_created_at_ist, credited_at_ist))) as month,
				EXTRACT(DAY FROM DATE(COALESCE(order_created_at_ist, credited_at_ist))) as day,
				COUNT(*) as metric_value
			FROM %s.pay_transactions
			WHERE (actor_to_id = '%s' or actor_from_id = '%s')
				AND credit_debit = 'Credit'
				AND transaction_status = 'SUCCESS'
				AND CAST(transaction_amount AS FLOAT64) > 25000.0
				AND DATE(COALESCE(order_created_at_ist, credited_at_ist)) >= DATE_TRUNC(DATE_SUB(CURRENT_DATE(), INTERVAL 1 YEAR), MONTH)
				AND COALESCE(order_created_at_ist, credited_at_ist) IS NOT NULL
			GROUP BY
				EXTRACT(YEAR FROM DATE(COALESCE(order_created_at_ist, credited_at_ist))),
				EXTRACT(WEEK FROM DATE(COALESCE(order_created_at_ist, credited_at_ist))),
				EXTRACT(MONTH FROM DATE(COALESCE(order_created_at_ist, credited_at_ist))),
				EXTRACT(DAY FROM DATE(COALESCE(order_created_at_ist, credited_at_ist)))
		)
		SELECT
			%s
			SUM(metric_value) as metric_value
		FROM date_aggregated
		GROUP BY 1, 2
		ORDER BY %s`,
		internalClient.Dataset(),
		actorId, actorId,
		selectClause,
		orderByClause,
	)

	// Log the query for debugging
	logger.Info(ctx, fmt.Sprintf("sql query submitted to bigquery for GetCredits25kPlus: %s", sqlQuery),
		zap.String("aggregation_type", aggregateOption.String()))

	job, err := internalClient.AsyncQuery(ctx, sqlQuery)
	if err != nil {
		logger.Error(ctx, "unable to submit job to bigquery for GetCredits25kPlus", zap.Error(err))
		return nil, err
	}
	logger.Info(ctx, fmt.Sprintf("Submitted GetCredits25kPlus job to bigquery, id %s", job.ID()))

	resultIter, err := job.Read(ctx)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("error while polling on bigquery job %s for GetCredits25kPlus", job.ID()), zap.Error(err))
		return nil, err
	}

	return r.parseQueryResults(ctx, resultIter, "GetCredits25kPlus", actorId)
}

// GetLifetimeNewP2PCreditCounterparties returns the count of lifetime new P2P credit counterparties for an actor
// A counterparty is considered "new" if they haven't appeared in any previous transactions (credit or debit) with the user
// aggregated by the specified aggregation type for the past 1 year
func (r *RiskAnalyticsBigqueryDBResourceProvider) GetLifetimeNewP2PCreditCounterparties(ctx context.Context, actorId string, aggregateOption cmPb.AggregateOption) ([]*cmPb.TimeAggregatedCount, error) {
	defer metric_util.TrackDuration("risk/case_management/dao", "RiskAnalyticsBigqueryDBResourceProvider", "GetLifetimeNewP2PCreditCounterparties", time.Now())

	if actorId == "" {
		return nil, fmt.Errorf("actor_id cannot be empty")
	}

	// Clone the context with timeout for long-running query
	ctx, cancelFn := context.WithTimeout(epificontext.CloneCtx(ctx), 10*time.Minute)
	defer cancelFn()

	internalClient, err := r.dbResourceProvider.GetResourceForOwner(commontypes.Owner_OWNER_EPIFI_TECH)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("error in get bigquery client for given resource provider %s", commontypes.Owner_OWNER_EPIFI_TECH.String()), zap.Error(err))
		return nil, err
	}

	// Get dynamic aggregation clauses
	selectClause, orderByClause := getDateAggregationClause(aggregateOption)

	// Updated complex query to find new P2P credit counterparties with improved lifetime tracking
	// A counterparty is "new" if their first interaction with the target account was a credit
	sqlQuery := fmt.Sprintf(`
		WITH all_lifetime_interactions AS (
			-- Get ALL lifetime interactions with the target account (not just last 1 year)
			SELECT
				CASE
					WHEN actor_to_id = '%s' THEN actor_from_id
					ELSE actor_to_id
				END as counterparty_id,
				COALESCE(order_created_at_ist, credited_at_ist) as txn_time,
				credit_debit,
				p2p_p2m,
				transaction_status
			FROM %s.pay_transactions
			WHERE (actor_from_id = '%s' OR actor_to_id = '%s')
				AND transaction_status = 'SUCCESS'
				AND p2p_p2m = 'P2P'
				AND COALESCE(order_created_at_ist, credited_at_ist) IS NOT NULL
				-- Add date filter for performance - extend beyond 1 year to capture all lifetime data
				AND DATE(COALESCE(order_created_at_ist, credited_at_ist)) >= '2020-01-01' -- Adjust this date based on your data history
		),
		first_interactions AS (
			-- Find each counterparty's first interaction with the target account
			SELECT
				counterparty_id,
				MIN(txn_time) as first_interaction_date
			FROM all_lifetime_interactions
			GROUP BY counterparty_id
		),
		first_interaction_details AS (
			-- Get the details of each counterparty's first interaction
			SELECT
				fi.counterparty_id,
				fi.first_interaction_date,
				ali.credit_debit
			FROM first_interactions fi
			JOIN all_lifetime_interactions ali ON fi.counterparty_id = ali.counterparty_id
				AND fi.first_interaction_date = ali.txn_time
		),
		new_credit_counterparties AS (
			-- Filter for counterparties whose first interaction was a credit TO the target account
			SELECT
				counterparty_id,
				first_interaction_date,
				EXTRACT(YEAR FROM DATE(first_interaction_date)) as year,
				EXTRACT(MONTH FROM DATE(first_interaction_date)) as month,
				EXTRACT(DAY FROM DATE(first_interaction_date)) as day,
				EXTRACT(WEEK FROM DATE(first_interaction_date)) as week_of_year
			FROM first_interaction_details
			WHERE credit_debit = 'Credit' -- First interaction was a credit TO the target account
		),
		date_aggregated AS (
			-- Aggregate by time periods, but only for display period (last 1 year)
			SELECT
				year,
				month,
				day,
				week_of_year,
				COUNT(DISTINCT counterparty_id) as metric_value
			FROM new_credit_counterparties
			WHERE DATE(first_interaction_date) >= DATE_TRUNC(DATE_SUB(CURRENT_DATE(), INTERVAL 1 YEAR), MONTH)
			GROUP BY year, month, day, week_of_year
		)
		SELECT
			%s
			sum(metric_value) as metric_value
		FROM date_aggregated
		GROUP BY 1,2
		ORDER BY %s`,
		actorId,
		internalClient.Dataset(),
		actorId, actorId,
		selectClause,
		orderByClause,
	)

	// Log the query for debugging
	logger.Info(ctx, fmt.Sprintf("sql query submitted to bigquery for GetLifetimeNewP2PCreditCounterparties: %s", sqlQuery),
		zap.String("aggregation_type", aggregateOption.String()))

	job, err := internalClient.AsyncQuery(ctx, sqlQuery)
	if err != nil {
		logger.Error(ctx, "unable to submit job to bigquery for GetLifetimeNewP2PCreditCounterparties", zap.Error(err))
		return nil, err
	}
	logger.Info(ctx, fmt.Sprintf("Submitted GetLifetimeNewP2PCreditCounterparties job to bigquery, id %s", job.ID()))

	resultIter, err := job.Read(ctx)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("error while polling on bigquery job %s for GetLifetimeNewP2PCreditCounterparties", job.ID()), zap.Error(err))
		return nil, err
	}

	return r.parseQueryResults(ctx, resultIter, "GetLifetimeNewP2PCreditCounterparties", actorId)
}
