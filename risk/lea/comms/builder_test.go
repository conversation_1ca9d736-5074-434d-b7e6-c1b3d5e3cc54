package comms

import (
	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"

	commsPb "github.com/epifi/gamma/api/comms"
	riskPb "github.com/epifi/gamma/api/risk"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/be-common/pkg/epifierrors"
	mockEssential "github.com/epifi/gamma/risk/essential/mocks"
	mockRemarks "github.com/epifi/gamma/risk/lea/remark/mocks"
)

type builderDependencies struct {
	userManager *mockEssential.MockUserManager
	infoBuilder *mockRemarks.MockInfoBuilder
}

func newBuilderWithMocks(t *testing.T) (*Builder, *builderDependencies, func()) {
	ctr := gomock.NewController(t)
	userManager := mockEssential.NewMockUserManager(ctr)
	builder := mockRemarks.NewMockInfoBuilder(ctr)

	md := &builderDependencies{
		userManager: userManager,
		infoBuilder: builder,
	}

	svc := NewBuilder(userManager, riskDependencies.GenConf, builder)

	return svc, md, func() {
		ctr.Finish()
	}
}

func TestBuilder_BuildLEAContactEmailMessage(t *testing.T) {
	ctx := context.Background()
	type args struct {
		complaintWithLEAInfo *riskPb.ExtendedLEAComplaint
	}
	var (
		leaComplaintWithInfo = &riskPb.ExtendedLEAComplaint{
			Complaint: &riskPb.LEAComplaint{
				ActorId: "actorId",
			},
		}
		user = &userPb.User{
			Id: "id",
			Profile: &userPb.Profile{
				Email: "<EMAIL>",
			},
		}
		message = &commsPb.EmailMessage{
			FromEmailId: "<EMAIL>",
			ToEmailId:   user.GetProfile().GetEmail(),
			EmailOption: &commsPb.EmailOption{
				Option: &commsPb.EmailOption_RiskLeaContactInfoOption{
					RiskLeaContactInfoOption: nil,
				},
			},
		}
	)
	tests := []struct {
		name    string
		args    args
		mocks   func(mocks *builderDependencies)
		want    *commsPb.EmailMessage
		wantErr bool
	}{
		{
			name: "failed to fetch user",
			args: args{
				complaintWithLEAInfo: leaComplaintWithInfo,
			},
			wantErr: true,
			mocks: func(mocks *builderDependencies) {
				mocks.userManager.EXPECT().GetByActorId(ctx, leaComplaintWithInfo.GetComplaint().GetActorId()).
					Return(nil, epifierrors.ErrRecordNotFound)
			},
		},
		{
			name: "failed to fetch email option",
			args: args{
				complaintWithLEAInfo: leaComplaintWithInfo,
			},
			wantErr: true,
			mocks: func(mocks *builderDependencies) {
				mocks.userManager.EXPECT().GetByActorId(ctx, leaComplaintWithInfo.GetComplaint().GetActorId()).
					Return(user, nil)
				mocks.infoBuilder.EXPECT().BuildFollowUpLEAContactEmail(ctx, leaComplaintWithInfo, commsPb.TemplateVersion_VERSION_V1).
					Return(nil, epifierrors.ErrInvalidArgument)
			},
		},
		{
			name: "success",
			args: args{
				complaintWithLEAInfo: leaComplaintWithInfo,
			},
			mocks: func(mocks *builderDependencies) {
				mocks.userManager.EXPECT().GetByActorId(ctx, leaComplaintWithInfo.GetComplaint().GetActorId()).
					Return(user, nil)
				mocks.infoBuilder.EXPECT().BuildFollowUpLEAContactEmail(ctx, leaComplaintWithInfo, commsPb.TemplateVersion_VERSION_V1).
					Return(nil, nil)
			},
			want: message,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, mockedDeps, assertTest := newBuilderWithMocks(t)
			if tt.mocks != nil {
				tt.mocks(mockedDeps)
			}
			got, err := s.BuildLEAContactEmail(ctx, tt.args.complaintWithLEAInfo)
			if (err != nil) != tt.wantErr {
				t.Errorf("BuildLEAContactEmail() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BuildLEAContactEmail() got = %v, want %v", got, tt.want)
			}
			assertTest()
		})
	}
}
