package dao

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"google.golang.org/genproto/googleapis/type/date"

	"gorm.io/gorm"

	pkgTest "github.com/epifi/be-common/pkg/test/v2"

	riskEnums "github.com/epifi/gamma/api/risk/enums"
	mnrlPb "github.com/epifi/gamma/api/risk/mnrl"
	"github.com/epifi/gamma/risk/config"
)

type MnrlReportDaoTestSuite struct {
	db            *gorm.DB
	mnrlReportDao MnrlReportDao
	conf          *config.Config
}

var (
	mnrlDTS MnrlReportDaoTestSuite
	_       = &mnrlPb.MnrlReport{
		MobileNumber:        "9876543210",
		LsaName:             riskEnums.LsaName_LSA_NAME_DELHI,
		TspName:             "Test TSP",
		DateOfDisconnection: &date.Date{Year: 2024, Month: 1, Day: 15},
		ActionReasons:       "Test action reasons",
		Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
	}
	mnrlReportTestTables = []string{"mnrl_reports"}
)

func TestMnrlReportPGDB_Upsert(t *testing.T) {
	pkgTest.TruncateTestDatabaseTables(t, mnrlDTS.db, mnrlDTS.conf.FRMPgdb.GetName(), mnrlReportTestTables)
	tests := []struct {
		name        string
		mnrlReport  *mnrlPb.MnrlReport
		wantErr     bool
		assertError func(error) bool
	}{
		{
			name: "successfully creates new mnrl report",
			mnrlReport: &mnrlPb.MnrlReport{
				MobileNumber:        "9876543210",
				LsaName:             riskEnums.LsaName_LSA_NAME_DELHI,
				TspName:             "Test TSP",
				DateOfDisconnection: &date.Date{Year: 2024, Month: 1, Day: 15},
				ActionReasons:       "Test action reasons",
				Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
			},
			wantErr: false,
		},
		{
			name: "successfully upserts existing mnrl report with updated fields",
			mnrlReport: &mnrlPb.MnrlReport{
				MobileNumber:        "9876543210",                      // existing mobile number
				LsaName:             riskEnums.LsaName_LSA_NAME_MUMBAI, // Updated LSA
				TspName:             "Updated TSP",                     // Updated TSP
				DateOfDisconnection: &date.Date{Year: 2024, Month: 1, Day: 16},
				ActionReasons:       "Updated action reasons",
				Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_DOT_FAKE,
			},
			wantErr: false,
		},
		{
			name: "fails due to missing required fields (database constraint)",
			mnrlReport: &mnrlPb.MnrlReport{
				MobileNumber: "6666666666",
				// Missing LsaName, TspName, DateOfDisconnection, ActionReasons, Usecase
			},
			wantErr: true,
		},
		{
			name: "fails due to empty mobile number",
			mnrlReport: &mnrlPb.MnrlReport{
				MobileNumber:        "", // Empty mobile number
				LsaName:             riskEnums.LsaName_LSA_NAME_DELHI,
				TspName:             "Test TSP",
				DateOfDisconnection: &date.Date{Year: 2024, Month: 1, Day: 15},
				ActionReasons:       "Test action reasons",
				Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
			},
			wantErr: true,
			assertError: func(err error) bool {
				return err != nil && assert.Contains(t, err.Error(), "mobile number is required")
			},
		},
		{
			name: "fails due to empty tsp name",
			mnrlReport: &mnrlPb.MnrlReport{
				MobileNumber:        "9876543210",
				LsaName:             riskEnums.LsaName_LSA_NAME_DELHI,
				TspName:             "", // Empty tsp name
				DateOfDisconnection: &date.Date{Year: 2024, Month: 1, Day: 15},
				ActionReasons:       "Test action reasons",
				Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
			},
			wantErr: true,
			assertError: func(err error) bool {
				return err != nil && assert.Contains(t, err.Error(), "tsp name is required")
			},
		},
		{
			name: "fails due to nil date of disconnection",
			mnrlReport: &mnrlPb.MnrlReport{
				MobileNumber:        "9876543210",
				LsaName:             riskEnums.LsaName_LSA_NAME_DELHI,
				TspName:             "Test TSP",
				DateOfDisconnection: nil, // Nil date of disconnection
				ActionReasons:       "Test action reasons",
				Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
			},
			wantErr: true,
			assertError: func(err error) bool {
				return err != nil && assert.Contains(t, err.Error(), "date of disconnection is required")
			},
		},
		{
			name: "fails due to empty action reasons",
			mnrlReport: &mnrlPb.MnrlReport{
				MobileNumber:        "9876543210",
				LsaName:             riskEnums.LsaName_LSA_NAME_DELHI,
				TspName:             "Test TSP",
				DateOfDisconnection: &date.Date{Year: 2024, Month: 1, Day: 15},
				ActionReasons:       "", // Empty action reasons
				Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
			},
			wantErr: true,
			assertError: func(err error) bool {
				return err != nil && assert.Contains(t, err.Error(), "action reasons is required")
			},
		},
		{
			name:       "fails due to nil input",
			mnrlReport: nil,
			wantErr:    true,
			assertError: func(err error) bool {
				return err != nil && assert.Contains(t, err.Error(), "mnrl report is required")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := mnrlDTS.mnrlReportDao.Upsert(context.Background(), tt.mnrlReport)

			if (err != nil) != tt.wantErr {
				t.Errorf("Upsert() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.assertError != nil && err != nil {
				assert.True(t, tt.assertError(err), "Error does not match expected type: %v", err)
			}

			if err == nil {
				assert.NotNil(t, result)
				if tt.mnrlReport != nil {
					assert.Equal(t, tt.mnrlReport.GetMobileNumber(), result.GetMobileNumber())
					assert.Equal(t, tt.mnrlReport.GetLsaName(), result.GetLsaName())
					assert.Equal(t, tt.mnrlReport.GetTspName(), result.GetTspName())
					assert.Equal(t, tt.mnrlReport.GetActionReasons(), result.GetActionReasons())
					assert.Equal(t, tt.mnrlReport.GetUsecase(), result.GetUsecase())
				}
			}
		})
	}
}

func TestMnrlReportPGDB_UpsertInBatch(t *testing.T) {
	pkgTest.TruncateTestDatabaseTables(t, mnrlDTS.db, mnrlDTS.conf.FRMPgdb.GetName(), mnrlReportTestTables)
	tests := []struct {
		name         string
		mnrlReports  []*mnrlPb.MnrlReport
		wantErr      bool
		assertError  func(error) bool
		setupData    []*mnrlPb.MnrlReport // Data to setup before test
		validateData func(t *testing.T)   // Custom validation function
	}{
		{
			name: "successfully creates multiple new mnrl reports",
			mnrlReports: []*mnrlPb.MnrlReport{
				{
					MobileNumber:        "1111111111",
					LsaName:             riskEnums.LsaName_LSA_NAME_DELHI,
					TspName:             "Test TSP 1",
					DateOfDisconnection: &date.Date{Year: 2024, Month: 1, Day: 15},
					ActionReasons:       "Test action reasons 1",
					Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
				},
				{
					MobileNumber:        "2222222222",
					LsaName:             riskEnums.LsaName_LSA_NAME_KARNATAKA,
					TspName:             "Test TSP 2",
					DateOfDisconnection: &date.Date{Year: 2024, Month: 1, Day: 16},
					ActionReasons:       "Test action reasons 2",
					Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_DOT_FAKE,
				},
				{
					MobileNumber:        "3333333333",
					LsaName:             riskEnums.LsaName_LSA_NAME_MUMBAI,
					TspName:             "Test TSP 3",
					DateOfDisconnection: &date.Date{Year: 2024, Month: 1, Day: 17},
					ActionReasons:       "Test action reasons 3",
					Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_NONCOMPLIANT,
				},
			},
			wantErr: false,
		},
		{
			name: "successfully handles mixed new and existing records",
			setupData: []*mnrlPb.MnrlReport{
				{
					MobileNumber:        "6666666666",
					LsaName:             riskEnums.LsaName_LSA_NAME_DELHI,
					TspName:             "Existing TSP",
					DateOfDisconnection: &date.Date{Year: 2024, Month: 1, Day: 15},
					ActionReasons:       "Existing reasons",
					Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
				},
			},
			mnrlReports: []*mnrlPb.MnrlReport{
				{
					MobileNumber:        "6666666666",                      // Existing record
					LsaName:             riskEnums.LsaName_LSA_NAME_MUMBAI, // Updated
					TspName:             "Updated Existing TSP",
					DateOfDisconnection: &date.Date{Year: 2024, Month: 1, Day: 20},
					ActionReasons:       "Updated existing reasons",
					Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_DOT_FAKE,
				},
				{
					MobileNumber:        "7777777777", // New record
					LsaName:             riskEnums.LsaName_LSA_NAME_KARNATAKA,
					TspName:             "New TSP",
					DateOfDisconnection: &date.Date{Year: 2024, Month: 1, Day: 21},
					ActionReasons:       "New reasons",
					Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_NONCOMPLIANT,
				},
			},
			wantErr: false,
		},
		{
			name: "fails due to missing required fields in some records",
			mnrlReports: []*mnrlPb.MnrlReport{
				{
					MobileNumber:        "8888888888",
					LsaName:             riskEnums.LsaName_LSA_NAME_DELHI,
					TspName:             "Valid TSP",
					DateOfDisconnection: &date.Date{Year: 2024, Month: 1, Day: 15},
					ActionReasons:       "Valid reasons",
					Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
				},
				{
					MobileNumber: "9999999999",
					// Missing required fields: LsaName, TspName, DateOfDisconnection, ActionReasons, Usecase
				},
			},
			wantErr: true,
		},
		{
			name: "fails due to empty mobile number in batch",
			mnrlReports: []*mnrlPb.MnrlReport{
				{
					MobileNumber:        "1111111111",
					LsaName:             riskEnums.LsaName_LSA_NAME_DELHI,
					TspName:             "Valid TSP",
					DateOfDisconnection: &date.Date{Year: 2024, Month: 1, Day: 15},
					ActionReasons:       "Valid reasons",
					Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
				},
				{
					MobileNumber:        "", // Empty mobile number
					LsaName:             riskEnums.LsaName_LSA_NAME_DELHI,
					TspName:             "Test TSP",
					DateOfDisconnection: &date.Date{Year: 2024, Month: 1, Day: 15},
					ActionReasons:       "Test reasons",
					Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
				},
			},
			wantErr: true,
			assertError: func(err error) bool {
				return err != nil && assert.Contains(t, err.Error(), "mobile number is required at index 1")
			},
		},
		{
			name: "fails due to empty tsp name in batch",
			mnrlReports: []*mnrlPb.MnrlReport{
				{
					MobileNumber:        "1111111111",
					LsaName:             riskEnums.LsaName_LSA_NAME_DELHI,
					TspName:             "Valid TSP",
					DateOfDisconnection: &date.Date{Year: 2024, Month: 1, Day: 15},
					ActionReasons:       "Valid reasons",
					Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
				},
				{
					MobileNumber:        "2222222222",
					LsaName:             riskEnums.LsaName_LSA_NAME_DELHI,
					TspName:             "", // Empty tsp name
					DateOfDisconnection: &date.Date{Year: 2024, Month: 1, Day: 15},
					ActionReasons:       "Test reasons",
					Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
				},
			},
			wantErr: true,
			assertError: func(err error) bool {
				return err != nil && assert.Contains(t, err.Error(), "tsp name is required at index 1")
			},
		},
		{
			name: "fails due to nil date of disconnection in batch",
			mnrlReports: []*mnrlPb.MnrlReport{
				{
					MobileNumber:        "1111111111",
					LsaName:             riskEnums.LsaName_LSA_NAME_DELHI,
					TspName:             "Valid TSP",
					DateOfDisconnection: &date.Date{Year: 2024, Month: 1, Day: 15},
					ActionReasons:       "Valid reasons",
					Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
				},
				{
					MobileNumber:        "2222222222",
					LsaName:             riskEnums.LsaName_LSA_NAME_DELHI,
					TspName:             "Test TSP",
					DateOfDisconnection: nil, // Nil date of disconnection
					ActionReasons:       "Test reasons",
					Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
				},
			},
			wantErr: true,
			assertError: func(err error) bool {
				return err != nil && assert.Contains(t, err.Error(), "date of disconnection is required at index 1")
			},
		},
		{
			name: "fails due to empty action reasons in batch",
			mnrlReports: []*mnrlPb.MnrlReport{
				{
					MobileNumber:        "1111111111",
					LsaName:             riskEnums.LsaName_LSA_NAME_DELHI,
					TspName:             "Valid TSP",
					DateOfDisconnection: &date.Date{Year: 2024, Month: 1, Day: 15},
					ActionReasons:       "Valid reasons",
					Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
				},
				{
					MobileNumber:        "2222222222",
					LsaName:             riskEnums.LsaName_LSA_NAME_DELHI,
					TspName:             "Test TSP",
					DateOfDisconnection: &date.Date{Year: 2024, Month: 1, Day: 15},
					ActionReasons:       "", // Empty action reasons
					Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
				},
			},
			wantErr: true,
			assertError: func(err error) bool {
				return err != nil && assert.Contains(t, err.Error(), "action reasons is required at index 1")
			},
		},
		{
			name: "fails due to nil record in batch",
			mnrlReports: []*mnrlPb.MnrlReport{
				{
					MobileNumber:        "1111111111",
					LsaName:             riskEnums.LsaName_LSA_NAME_DELHI,
					TspName:             "Valid TSP",
					DateOfDisconnection: &date.Date{Year: 2024, Month: 1, Day: 15},
					ActionReasons:       "Valid reasons",
					Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
				},
				nil, // Nil record
			},
			wantErr: true,
			assertError: func(err error) bool {
				return err != nil && assert.Contains(t, err.Error(), "mnrl report is required at index 1")
			},
		},
		{
			name: "successfully handles large batch of records",
			mnrlReports: func() []*mnrlPb.MnrlReport {
				var reports []*mnrlPb.MnrlReport
				for i := 0; i < 10; i++ {
					reports = append(reports, &mnrlPb.MnrlReport{
						MobileNumber:        fmt.Sprintf("100000000%d", i),
						LsaName:             riskEnums.LsaName_LSA_NAME_DELHI,
						TspName:             fmt.Sprintf("TSP %d", i),
						DateOfDisconnection: &date.Date{Year: 2024, Month: 1, Day: int32(15 + i)},
						ActionReasons:       fmt.Sprintf("Action reasons %d", i),
						Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
					})
				}
				return reports
			}(),
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup test data if provided
			if tt.setupData != nil {
				for _, report := range tt.setupData {
					_, err := mnrlDTS.mnrlReportDao.Upsert(context.Background(), report)
					require.NoError(t, err, "Failed to setup test data")
				}
			}

			// Execute the batch upsert
			result, err := mnrlDTS.mnrlReportDao.UpsertInBatch(context.Background(), tt.mnrlReports)

			// Validate error expectations
			if (err != nil) != tt.wantErr {
				t.Errorf("UpsertInBatch() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// Validate custom error assertions if provided
			if tt.assertError != nil && err != nil {
				assert.True(t, tt.assertError(err), "Error does not match expected type: %v", err)
			}

			// Validate successful results
			if err == nil {
				assert.NotNil(t, result)
				assert.Len(t, result, len(tt.mnrlReports), "Result length should match input length")

				// Validate each result matches the corresponding input
				for i, inputReport := range tt.mnrlReports {
					if i < len(result) {
						outputReport := result[i]
						assert.Equal(t, inputReport.GetMobileNumber(), outputReport.GetMobileNumber())
						assert.Equal(t, inputReport.GetLsaName(), outputReport.GetLsaName())
						assert.Equal(t, inputReport.GetTspName(), outputReport.GetTspName())
						assert.Equal(t, inputReport.GetActionReasons(), outputReport.GetActionReasons())
						assert.Equal(t, inputReport.GetUsecase(), outputReport.GetUsecase())
					}
				}

				// Run custom validation if provided
				if tt.validateData != nil {
					tt.validateData(t)
				}
			}
		})
	}
}
