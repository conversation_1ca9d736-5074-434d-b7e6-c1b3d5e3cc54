package model

import (
	"time"

	"github.com/epifi/be-common/pkg/nulltypes"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/gamma/api/risk/enums"
	mnrlPb "github.com/epifi/gamma/api/risk/mnrl"
)

type MnrlSuspectedFlaggedMobile struct {
	Id                  string               `gorm:"type:uuid;default:gen_random_uuid()"`
	MobileNumber        string               `gorm:"column:mobile_number"`
	Reason              nulltypes.NullString `gorm:"column:reason"`
	ReverifiedAt        nulltypes.NullTime   `gorm:"column:reverified_at"`
	Usecase             nulltypes.NullString `gorm:"column:usecase"`
	LsaCode             nulltypes.NullString `gorm:"column:lsa_code"`
	TspName             nulltypes.NullString `gorm:"column:tsp_name"`
	SensitivityIndex    string               `gorm:"column:sensitivity_index"`
	DistributionDetails nulltypes.NullString `gorm:"column:distribution_details"`
	CreatedAt           time.Time            `gorm:"column:created_at"`
	UpdatedAt           time.Time            `gorm:"column:updated_at"`
	DeletedAtUnix       int64                `gorm:"column:deleted_at_unix;default:0"`
}

func NewMnrlSuspectedFlaggedMobileModel(suspectedFlagged *mnrlPb.MnrlSuspectedFlaggedMobile) *MnrlSuspectedFlaggedMobile {
	model := &MnrlSuspectedFlaggedMobile{
		Id:                  suspectedFlagged.GetId(),
		MobileNumber:        suspectedFlagged.GetMobileNumber(),
		Reason:              nulltypes.NewNullString(suspectedFlagged.GetReason()),
		Usecase:             nulltypes.NewNullString(suspectedFlagged.GetUsecase().String()),
		LsaCode:             nulltypes.NewNullString(suspectedFlagged.GetLsaCode().String()),
		TspName:             nulltypes.NewNullString(suspectedFlagged.GetTspName()),
		SensitivityIndex:    suspectedFlagged.GetSensitivityIndex().String(),
		DistributionDetails: nulltypes.NewNullString(suspectedFlagged.GetDistributionDetails()),
	}

	// Convert protobuf timestamps to nulltypes.NullTime
	if suspectedFlagged.GetReverifiedAt() != nil {
		model.ReverifiedAt = nulltypes.NewNullTime(suspectedFlagged.GetReverifiedAt().AsTime())
	}
	if suspectedFlagged.GetCreatedAt() != nil {
		model.CreatedAt = suspectedFlagged.GetCreatedAt().AsTime()
	}
	if suspectedFlagged.GetUpdatedAt() != nil {
		model.UpdatedAt = suspectedFlagged.GetUpdatedAt().AsTime()
	}

	return model
}

func GetMnrlSuspectedFlaggedMobilePb(model *MnrlSuspectedFlaggedMobile) *mnrlPb.MnrlSuspectedFlaggedMobile {
	// Parse sensitivity index from string to enum
	var sensitivityIndex enums.SensitivityIndex
	switch model.SensitivityIndex {
	case "SENSITIVITY_MEDIUM":
		sensitivityIndex = enums.SensitivityIndex_SENSITIVITY_INDEX_MEDIUM
	case "SENSITIVITY_HIGH":
		sensitivityIndex = enums.SensitivityIndex_SENSITIVITY_INDEX_HIGH
	case "SENSITIVITY_VERY_HIGH":
		sensitivityIndex = enums.SensitivityIndex_SENSITIVITY_INDEX_VERY_HIGH
	default:
		sensitivityIndex = enums.SensitivityIndex_SENSITIVITY_INDEX_UNSPECIFIED
	}

	proto := &mnrlPb.MnrlSuspectedFlaggedMobile{
		Id:                  model.Id,
		MobileNumber:        model.MobileNumber,
		Reason:              model.Reason.GetValue(),
		Usecase:             parseMnrlUsecase(model.Usecase.GetValue()),
		LsaCode:             parseLsaName(model.LsaCode.GetValue()),
		TspName:             model.TspName.GetValue(),
		SensitivityIndex:    sensitivityIndex,
		DistributionDetails: model.DistributionDetails.GetValue(),
		CreatedAt:           timestampPb.New(model.CreatedAt),
		UpdatedAt:           timestampPb.New(model.UpdatedAt),
	}

	// Handle nullable timestamp field
	if model.ReverifiedAt.Valid {
		proto.ReverifiedAt = timestampPb.New(model.ReverifiedAt.Time)
	}

	return proto
}
