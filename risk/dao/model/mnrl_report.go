package model

import (
	"time"

	"github.com/epifi/be-common/pkg/nulltypes"

	"google.golang.org/genproto/googleapis/type/date"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	riskEnums "github.com/epifi/gamma/api/risk/enums"
	mnrlPb "github.com/epifi/gamma/api/risk/mnrl"
)

type MnrlReport struct {
	Id                  string               `gorm:"type:uuid;default:gen_random_uuid()"`
	MobileNumber        string               `gorm:"column:mobile_number"`
	LsaName             string               `gorm:"column:lsa_name"`
	TspName             string               `gorm:"column:tsp_name"`
	DateOfDisconnection nulltypes.NullTime   `gorm:"column:date_of_disconnection"`
	ActionReasons       nulltypes.NullString `gorm:"column:action_reasons"`
	Usecase             nulltypes.NullString `gorm:"column:usecase"`
	CreatedAt           time.Time            `gorm:"column:created_at"`
	UpdatedAt           time.Time            `gorm:"column:updated_at"`
	DeletedAtUnix       int64                `gorm:"column:deleted_at_unix;default:0"`
}

func NewMnrlReportModel(report *mnrlPb.MnrlReport) *MnrlReport {
	model := &MnrlReport{
		Id:            report.GetId(),
		MobileNumber:  report.GetMobileNumber(),
		LsaName:       report.GetLsaName().String(),
		TspName:       report.GetTspName(),
		ActionReasons: nulltypes.NewNullString(report.GetActionReasons()),
		Usecase:       nulltypes.NewNullString(report.GetUsecase().String()),
	}

	// Convert protobuf timestamps to nulltypes.NullTime
	if report.GetDateOfDisconnection() != nil {
		// Convert google.type.Date to time.Time
		dateTime := time.Date(
			int(report.GetDateOfDisconnection().GetYear()),
			time.Month(report.GetDateOfDisconnection().GetMonth()),
			int(report.GetDateOfDisconnection().GetDay()),
			0, 0, 0, 0, time.UTC,
		)
		model.DateOfDisconnection = nulltypes.NewNullTime(dateTime)
	} else {
		model.DateOfDisconnection = nulltypes.NewNullTime(time.Date(1970, 1, 1, 0, 0, 0, 0, time.UTC))
	}

	if report.GetCreatedAt() != nil {
		model.CreatedAt = report.GetCreatedAt().AsTime()
	}
	if report.GetUpdatedAt() != nil {
		model.UpdatedAt = report.GetUpdatedAt().AsTime()
	}

	return model
}

func GetMnrlReportPb(model *MnrlReport) *mnrlPb.MnrlReport {
	proto := &mnrlPb.MnrlReport{
		Id:            model.Id,
		MobileNumber:  model.MobileNumber,
		LsaName:       parseLsaName(model.LsaName),
		TspName:       model.TspName,
		ActionReasons: model.ActionReasons.GetValue(),
		Usecase:       parseMnrlUsecase(model.Usecase.GetValue()),
		CreatedAt:     timestampPb.New(model.CreatedAt),
		UpdatedAt:     timestampPb.New(model.UpdatedAt),
	}

	// Handle nullable timestamp fields
	if model.DateOfDisconnection.Valid {
		// Convert time.Time to google.type.Date with bounds checking
		year := model.DateOfDisconnection.Time.Year()
		month := model.DateOfDisconnection.Time.Month()
		day := model.DateOfDisconnection.Time.Day()

		// Validate bounds before conversion to prevent integer overflow
		if year < 1 || year > 9999 {
			year = 1970
		}
		if month < 1 || month > 12 {
			month = 1
		}
		if day < 1 || day > 31 {
			day = 1
		}
		// safe conversion after bounds checking for day month and year
		proto.DateOfDisconnection = &date.Date{
			Year:  int32(year),  //nolint:gosec
			Month: int32(month), //nolint:gosec
			Day:   int32(day),   //nolint:gosec
		}
	}

	return proto
}

// parseLsaName converts string to LsaName enum
func parseLsaName(lsaNameStr string) riskEnums.LsaName {
	if lsaNameStr == "" {
		return riskEnums.LsaName_LSA_NAME_UNSPECIFIED
	}
	if value, exists := riskEnums.LsaName_value[lsaNameStr]; exists {
		return riskEnums.LsaName(value)
	}
	return riskEnums.LsaName_LSA_NAME_UNSPECIFIED
}

// parseMnrlUsecase converts string to MnrlUsecase enum
func parseMnrlUsecase(usecaseStr string) riskEnums.MnrlUsecase {
	if usecaseStr == "" {
		return riskEnums.MnrlUsecase_MNRL_USECASE_UNSPECIFIED
	}
	if value, exists := riskEnums.MnrlUsecase_value[usecaseStr]; exists {
		return riskEnums.MnrlUsecase(value)
	}
	return riskEnums.MnrlUsecase_MNRL_USECASE_UNSPECIFIED
}
