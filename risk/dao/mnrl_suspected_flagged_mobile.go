package dao

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	mnrlPb "github.com/epifi/gamma/api/risk/mnrl"
	"github.com/epifi/gamma/risk/dao/model"
)

type MnrlSuspectedFlaggedMobilePGDB struct {
	db *gorm.DB
}

func NewMnrlSuspectedFlaggedMobilePGDB(db types.FRMPGDB) *MnrlSuspectedFlaggedMobilePGDB {
	return &MnrlSuspectedFlaggedMobilePGDB{db: db}
}

var _ MnrlSuspectedFlaggedMobileDao = &MnrlSuspectedFlaggedMobilePGDB{}

func (m *MnrlSuspectedFlaggedMobilePGDB) Upsert(ctx context.Context, mnrlSuspectedFlaggedMobile *mnrlPb.MnrlSuspectedFlaggedMobile) (*mnrlPb.MnrlSuspectedFlaggedMobile, error) {
	defer metric_util.TrackDuration("risk/dao", "MnrlSuspectedFlaggedMobilePGDB", "Upsert", time.Now())

	// Validate all required fields are not empty
	if mnrlSuspectedFlaggedMobile == nil {
		return nil, fmt.Errorf("mnrl suspected flagged mobile is required: %w", epifierrors.ErrInvalidArgument)
	}
	if mnrlSuspectedFlaggedMobile.GetMobileNumber() == "" {
		return nil, fmt.Errorf("mobile number is required: %w", epifierrors.ErrInvalidArgument)
	}
	if mnrlSuspectedFlaggedMobile.GetReason() == "" {
		return nil, fmt.Errorf("reason is required: %w", epifierrors.ErrInvalidArgument)
	}
	if mnrlSuspectedFlaggedMobile.GetReverifiedAt() == nil {
		return nil, fmt.Errorf("reverified at is required: %w", epifierrors.ErrInvalidArgument)
	}
	if mnrlSuspectedFlaggedMobile.GetTspName() == "" {
		return nil, fmt.Errorf("tsp name is required: %w", epifierrors.ErrInvalidArgument)
	}

	mnrlSuspectedFlaggedModel := model.NewMnrlSuspectedFlaggedMobileModel(mnrlSuspectedFlaggedMobile)
	db := gormctxv2.FromContextOrDefault(ctx, m.db)
	err := db.Clauses(clause.OnConflict{
		Columns: []clause.Column{
			{Name: "mobile_number"}, // Conflict on mobile_number column
		},
		DoUpdates: clause.AssignmentColumns([]string{
			"reason",
			"reverified_at",
			"usecase",
			"lsa_code",
			"tsp_name",
			"sensitivity_index",
			"distribution_details",
			"updated_at",
		}),
	}).Create(&mnrlSuspectedFlaggedModel).Error

	if err != nil {
		return nil, fmt.Errorf("error upserting mnrl suspected flagged in db: %w", err)
	}
	return mnrlSuspectedFlaggedMobile, nil
}

func (m *MnrlSuspectedFlaggedMobilePGDB) UpsertInBatch(ctx context.Context, mnrlSuspectedFlaggedMobiles []*mnrlPb.MnrlSuspectedFlaggedMobile) ([]*mnrlPb.MnrlSuspectedFlaggedMobile, error) {
	defer metric_util.TrackDuration("risk/dao", "MnrlSuspectedFlaggedMobilePGDB", "UpsertInBatch", time.Now())

	// Validate all required fields are not empty for each record
	for i, mnrlSuspectedFlaggedMobile := range mnrlSuspectedFlaggedMobiles {
		if mnrlSuspectedFlaggedMobile == nil {
			return nil, fmt.Errorf("mnrl suspected flagged mobile is required at index %d: %w", i, epifierrors.ErrInvalidArgument)
		}
		if mnrlSuspectedFlaggedMobile.GetMobileNumber() == "" {
			return nil, fmt.Errorf("mobile number is required at index %d: %w", i, epifierrors.ErrInvalidArgument)
		}
		if mnrlSuspectedFlaggedMobile.GetReason() == "" {
			return nil, fmt.Errorf("reason is required at index %d: %w", i, epifierrors.ErrInvalidArgument)
		}
		if mnrlSuspectedFlaggedMobile.GetReverifiedAt() == nil {
			return nil, fmt.Errorf("reverified at is required at index %d: %w", i, epifierrors.ErrInvalidArgument)
		}
		if mnrlSuspectedFlaggedMobile.GetTspName() == "" {
			return nil, fmt.Errorf("tsp name is required at index %d: %w", i, epifierrors.ErrInvalidArgument)
		}
	}

	var mnrlSuspectedFlaggedModels []*model.MnrlSuspectedFlaggedMobile
	for _, mnrlSuspectedFlaggedMobile := range mnrlSuspectedFlaggedMobiles {
		mnrlSuspectedFlaggedModels = append(mnrlSuspectedFlaggedModels, model.NewMnrlSuspectedFlaggedMobileModel(mnrlSuspectedFlaggedMobile))
	}
	db := gormctxv2.FromContextOrDefault(ctx, m.db)
	err := db.Clauses(clause.OnConflict{
		Columns: []clause.Column{
			{Name: "mobile_number"}, // Conflict on mobile_number column
		},
		DoUpdates: clause.AssignmentColumns([]string{
			"reason",
			"reverified_at",
			"usecase",
			"lsa_code",
			"tsp_name",
			"sensitivity_index",
			"distribution_details",
			"updated_at",
		}),
	}).Create(&mnrlSuspectedFlaggedModels).Error

	if err != nil {
		return nil, fmt.Errorf("error upserting mnrl suspected flagged in db: %w", err)
	}
	return mnrlSuspectedFlaggedMobiles, nil
}
