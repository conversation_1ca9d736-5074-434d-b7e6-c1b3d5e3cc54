package dao

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	pkgTest "github.com/epifi/be-common/pkg/test/v2"

	riskEnums "github.com/epifi/gamma/api/risk/enums"
	mnrlPb "github.com/epifi/gamma/api/risk/mnrl"
	"github.com/epifi/gamma/risk/config"
)

type MnrlSuspectedFlaggedMobileDaoTestSuite struct {
	db                      *gorm.DB
	mnrlSuspectedFlaggedDao MnrlSuspectedFlaggedMobileDao
	conf                    *config.Config
}

var (
	mnrlSuspectedFlaggedMobileDTS MnrlSuspectedFlaggedMobileDaoTestSuite
	_                             = &mnrlPb.MnrlSuspectedFlaggedMobile{
		MobileNumber:        "9876543210",
		Reason:              "Test reason for suspected flagged",
		ReverifiedAt:        timestamppb.New(time.Now().Add(24 * time.Hour)),
		Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
		LsaCode:             riskEnums.LsaName_LSA_NAME_DELHI,
		TspName:             "Test TSP",
		SensitivityIndex:    riskEnums.SensitivityIndex_SENSITIVITY_INDEX_HIGH,
		DistributionDetails: "Test distribution details",
	}
	mnrlSuspectedFlaggedMobileTestTables = []string{"mnrl_suspected_flagged_mobiles"}
)

func TestMnrlSuspectedFlaggedMobilePGDB_Upsert(t *testing.T) {
	pkgTest.TruncateTestDatabaseTables(t, mnrlSuspectedFlaggedMobileDTS.db, mnrlSuspectedFlaggedMobileDTS.conf.FRMPgdb.GetName(), mnrlSuspectedFlaggedMobileTestTables)

	tests := []struct {
		name                       string
		mnrlSuspectedFlaggedMobile *mnrlPb.MnrlSuspectedFlaggedMobile
		wantErr                    bool
		assertError                func(error) bool
	}{
		{
			name: "successfully creates new mnrl suspected flagged",
			mnrlSuspectedFlaggedMobile: &mnrlPb.MnrlSuspectedFlaggedMobile{
				MobileNumber:        "9876543210",
				Reason:              "Test reason for suspected flagged",
				ReverifiedAt:        timestamppb.New(time.Now().Add(24 * time.Hour)),
				Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
				LsaCode:             riskEnums.LsaName_LSA_NAME_DELHI,
				TspName:             "Test TSP",
				SensitivityIndex:    riskEnums.SensitivityIndex_SENSITIVITY_INDEX_HIGH,
				DistributionDetails: "Test distribution details",
			},
			wantErr: false,
		},
		{
			name: "successfully upserts existing mnrl suspected flagged with updated fields",
			mnrlSuspectedFlaggedMobile: &mnrlPb.MnrlSuspectedFlaggedMobile{
				MobileNumber:        "9876543210",
				Reason:              "Updated reason for suspected flagged",
				ReverifiedAt:        timestamppb.New(time.Now().Add(25 * time.Hour)),
				Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_NONCOMPLIANT,
				LsaCode:             riskEnums.LsaName_LSA_NAME_MUMBAI,
				TspName:             "Updated TSP",
				SensitivityIndex:    riskEnums.SensitivityIndex_SENSITIVITY_INDEX_VERY_HIGH,
				DistributionDetails: "Updated distribution details",
			},
			wantErr: false,
		},
		{
			name: "fails due to missing required fields (database constraint)",
			mnrlSuspectedFlaggedMobile: &mnrlPb.MnrlSuspectedFlaggedMobile{
				MobileNumber: "6666666666",
				// Missing required fields: Reason, ReverifiedAt, Usecase, LsaCode, TspName, SensitivityIndex
			},
			wantErr: true,
		},
		{
			name: "fails due to empty mobile number",
			mnrlSuspectedFlaggedMobile: &mnrlPb.MnrlSuspectedFlaggedMobile{
				MobileNumber:     "", // Empty mobile number
				Reason:           "Test reason",
				ReverifiedAt:     timestamppb.New(time.Now().Add(24 * time.Hour)),
				Usecase:          riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
				LsaCode:          riskEnums.LsaName_LSA_NAME_DELHI,
				TspName:          "Test TSP",
				SensitivityIndex: riskEnums.SensitivityIndex_SENSITIVITY_INDEX_HIGH,
			},
			wantErr: true,
			assertError: func(err error) bool {
				return err != nil && assert.Contains(t, err.Error(), "mobile number is required")
			},
		},
		{
			name: "fails due to empty reason",
			mnrlSuspectedFlaggedMobile: &mnrlPb.MnrlSuspectedFlaggedMobile{
				MobileNumber:     "9876543210",
				Reason:           "", // Empty reason
				ReverifiedAt:     timestamppb.New(time.Now().Add(24 * time.Hour)),
				Usecase:          riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
				LsaCode:          riskEnums.LsaName_LSA_NAME_DELHI,
				TspName:          "Test TSP",
				SensitivityIndex: riskEnums.SensitivityIndex_SENSITIVITY_INDEX_HIGH,
			},
			wantErr: true,
			assertError: func(err error) bool {
				return err != nil && assert.Contains(t, err.Error(), "reason is required")
			},
		},
		{
			name: "fails due to nil reverified_at",
			mnrlSuspectedFlaggedMobile: &mnrlPb.MnrlSuspectedFlaggedMobile{
				MobileNumber:     "9876543210",
				Reason:           "Test reason",
				ReverifiedAt:     nil, // Nil reverified_at
				Usecase:          riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
				LsaCode:          riskEnums.LsaName_LSA_NAME_DELHI,
				TspName:          "Test TSP",
				SensitivityIndex: riskEnums.SensitivityIndex_SENSITIVITY_INDEX_HIGH,
			},
			wantErr: true,
			assertError: func(err error) bool {
				return err != nil && assert.Contains(t, err.Error(), "reverified at is required")
			},
		},
		{
			name: "fails due to empty tsp_name",
			mnrlSuspectedFlaggedMobile: &mnrlPb.MnrlSuspectedFlaggedMobile{
				MobileNumber:     "9876543210",
				Reason:           "Test reason",
				ReverifiedAt:     timestamppb.New(time.Now().Add(24 * time.Hour)),
				Usecase:          riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
				LsaCode:          riskEnums.LsaName_LSA_NAME_DELHI,
				TspName:          "", // Empty tsp_name
				SensitivityIndex: riskEnums.SensitivityIndex_SENSITIVITY_INDEX_HIGH,
			},
			wantErr: true,
			assertError: func(err error) bool {
				return err != nil && assert.Contains(t, err.Error(), "tsp name is required")
			},
		},
		{
			name:                       "fails due to nil input",
			mnrlSuspectedFlaggedMobile: nil,
			wantErr:                    true,
			assertError: func(err error) bool {
				return err != nil && assert.Contains(t, err.Error(), "mnrl suspected flagged mobile is required")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := mnrlSuspectedFlaggedMobileDTS.mnrlSuspectedFlaggedDao.Upsert(context.Background(), tt.mnrlSuspectedFlaggedMobile)

			if (err != nil) != tt.wantErr {
				t.Errorf("Upsert() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.assertError != nil && err != nil {
				assert.True(t, tt.assertError(err), "Error does not match expected type: %v", err)
			}

			if err == nil {
				assert.NotNil(t, result)
				if tt.mnrlSuspectedFlaggedMobile != nil {
					assert.Equal(t, tt.mnrlSuspectedFlaggedMobile.GetMobileNumber(), result.GetMobileNumber())
					assert.Equal(t, tt.mnrlSuspectedFlaggedMobile.GetReason(), result.GetReason())
					assert.Equal(t, tt.mnrlSuspectedFlaggedMobile.GetUsecase(), result.GetUsecase())
					assert.Equal(t, tt.mnrlSuspectedFlaggedMobile.GetLsaCode(), result.GetLsaCode())
					assert.Equal(t, tt.mnrlSuspectedFlaggedMobile.GetTspName(), result.GetTspName())
					assert.Equal(t, tt.mnrlSuspectedFlaggedMobile.GetSensitivityIndex(), result.GetSensitivityIndex())
					assert.Equal(t, tt.mnrlSuspectedFlaggedMobile.GetDistributionDetails(), result.GetDistributionDetails())
				}
			}
		})
	}
}

func TestMnrlSuspectedFlaggedMobilePGDB_UpsertInBatch(t *testing.T) {
	pkgTest.TruncateTestDatabaseTables(t, mnrlSuspectedFlaggedMobileDTS.db, mnrlSuspectedFlaggedMobileDTS.conf.FRMPgdb.GetName(), mnrlSuspectedFlaggedMobileTestTables)
	tests := []struct {
		name                        string
		mnrlSuspectedFlaggedMobiles []*mnrlPb.MnrlSuspectedFlaggedMobile
		wantErr                     bool
		assertError                 func(error) bool
		setupData                   []*mnrlPb.MnrlSuspectedFlaggedMobile // Data to setup before test
		validateData                func(t *testing.T)                   // Custom validation function
	}{
		{
			name: "successfully creates multiple new mnrl suspected flagged mobiles",
			mnrlSuspectedFlaggedMobiles: []*mnrlPb.MnrlSuspectedFlaggedMobile{
				{
					MobileNumber:        "1111111111",
					Reason:              "Test reason 1 for suspected flagged",
					ReverifiedAt:        timestamppb.New(time.Now().Add(24 * time.Hour)),
					Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
					LsaCode:             riskEnums.LsaName_LSA_NAME_DELHI,
					TspName:             "Test TSP 1",
					SensitivityIndex:    riskEnums.SensitivityIndex_SENSITIVITY_INDEX_HIGH,
					DistributionDetails: "Test distribution details 1",
				},
				{
					MobileNumber:        "2222222222",
					Reason:              "Test reason 2 for suspected flagged",
					ReverifiedAt:        timestamppb.New(time.Now().Add(25 * time.Hour)),
					Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_DOT_FAKE,
					LsaCode:             riskEnums.LsaName_LSA_NAME_KARNATAKA,
					TspName:             "Test TSP 2",
					SensitivityIndex:    riskEnums.SensitivityIndex_SENSITIVITY_INDEX_MEDIUM,
					DistributionDetails: "Test distribution details 2",
				},
				{
					MobileNumber:        "3333333333",
					Reason:              "Test reason 3 for suspected flagged",
					ReverifiedAt:        timestamppb.New(time.Now().Add(26 * time.Hour)),
					Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_NONCOMPLIANT,
					LsaCode:             riskEnums.LsaName_LSA_NAME_MUMBAI,
					TspName:             "Test TSP 3",
					SensitivityIndex:    riskEnums.SensitivityIndex_SENSITIVITY_INDEX_VERY_HIGH,
					DistributionDetails: "Test distribution details 3",
				},
			},
			wantErr: false,
		},
		{
			name: "successfully handles mixed new and existing records",
			setupData: []*mnrlPb.MnrlSuspectedFlaggedMobile{
				{
					MobileNumber:        "6666666666",
					Reason:              "Existing reason",
					ReverifiedAt:        timestamppb.New(time.Now().Add(24 * time.Hour)),
					Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
					LsaCode:             riskEnums.LsaName_LSA_NAME_DELHI,
					TspName:             "Existing TSP",
					SensitivityIndex:    riskEnums.SensitivityIndex_SENSITIVITY_INDEX_HIGH,
					DistributionDetails: "Existing distribution details",
				},
			},
			mnrlSuspectedFlaggedMobiles: []*mnrlPb.MnrlSuspectedFlaggedMobile{
				{
					MobileNumber:        "6666666666",              // Existing record
					Reason:              "Updated existing reason", // Updated
					ReverifiedAt:        timestamppb.New(time.Now().Add(29 * time.Hour)),
					Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_DOT_FAKE,            // Updated
					LsaCode:             riskEnums.LsaName_LSA_NAME_MUMBAI,                      // Updated
					TspName:             "Updated Existing TSP",                                 // Updated
					SensitivityIndex:    riskEnums.SensitivityIndex_SENSITIVITY_INDEX_VERY_HIGH, // Updated
					DistributionDetails: "Updated existing distribution details",
				},
				{
					MobileNumber:        "7777777777", // New record
					Reason:              "New reason",
					ReverifiedAt:        timestamppb.New(time.Now().Add(30 * time.Hour)),
					Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_NONCOMPLIANT,
					LsaCode:             riskEnums.LsaName_LSA_NAME_KARNATAKA,
					TspName:             "New TSP",
					SensitivityIndex:    riskEnums.SensitivityIndex_SENSITIVITY_INDEX_MEDIUM,
					DistributionDetails: "New distribution details",
				},
			},
			wantErr: false,
		},
		{
			name: "fails due to missing required fields in some records",
			mnrlSuspectedFlaggedMobiles: []*mnrlPb.MnrlSuspectedFlaggedMobile{
				{
					MobileNumber:        "8888888888",
					Reason:              "Valid reason",
					ReverifiedAt:        timestamppb.New(time.Now().Add(24 * time.Hour)),
					Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
					LsaCode:             riskEnums.LsaName_LSA_NAME_DELHI,
					TspName:             "Valid TSP",
					SensitivityIndex:    riskEnums.SensitivityIndex_SENSITIVITY_INDEX_HIGH,
					DistributionDetails: "Valid distribution details",
				},
				{
					MobileNumber: "9999999999",
					// Missing required fields: Reason, ReverifiedAt, Usecase, LsaCode, TspName, SensitivityIndex
				},
			},
			wantErr: true,
		},
		{
			name: "fails due to empty mobile number in batch",
			mnrlSuspectedFlaggedMobiles: []*mnrlPb.MnrlSuspectedFlaggedMobile{
				{
					MobileNumber:        "1111111111",
					Reason:              "Valid reason",
					ReverifiedAt:        timestamppb.New(time.Now().Add(24 * time.Hour)),
					Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
					LsaCode:             riskEnums.LsaName_LSA_NAME_DELHI,
					TspName:             "Valid TSP",
					SensitivityIndex:    riskEnums.SensitivityIndex_SENSITIVITY_INDEX_HIGH,
					DistributionDetails: "Valid distribution details",
				},
				{
					MobileNumber:        "", // Empty mobile number
					Reason:              "Test reason",
					ReverifiedAt:        timestamppb.New(time.Now().Add(24 * time.Hour)),
					Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
					LsaCode:             riskEnums.LsaName_LSA_NAME_DELHI,
					TspName:             "Test TSP",
					SensitivityIndex:    riskEnums.SensitivityIndex_SENSITIVITY_INDEX_HIGH,
					DistributionDetails: "Test distribution details",
				},
			},
			wantErr: true,
			assertError: func(err error) bool {
				return err != nil && assert.Contains(t, err.Error(), "mobile number is required at index 1")
			},
		},
		{
			name: "fails due to empty reason in batch",
			mnrlSuspectedFlaggedMobiles: []*mnrlPb.MnrlSuspectedFlaggedMobile{
				{
					MobileNumber:        "1111111111",
					Reason:              "Valid reason",
					ReverifiedAt:        timestamppb.New(time.Now().Add(24 * time.Hour)),
					Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
					LsaCode:             riskEnums.LsaName_LSA_NAME_DELHI,
					TspName:             "Valid TSP",
					SensitivityIndex:    riskEnums.SensitivityIndex_SENSITIVITY_INDEX_HIGH,
					DistributionDetails: "Valid distribution details",
				},
				{
					MobileNumber:        "2222222222",
					Reason:              "", // Empty reason
					ReverifiedAt:        timestamppb.New(time.Now().Add(24 * time.Hour)),
					Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
					LsaCode:             riskEnums.LsaName_LSA_NAME_DELHI,
					TspName:             "Test TSP",
					SensitivityIndex:    riskEnums.SensitivityIndex_SENSITIVITY_INDEX_HIGH,
					DistributionDetails: "Test distribution details",
				},
			},
			wantErr: true,
			assertError: func(err error) bool {
				return err != nil && assert.Contains(t, err.Error(), "reason is required at index 1")
			},
		},
		{
			name: "fails due to nil reverified_at in batch",
			mnrlSuspectedFlaggedMobiles: []*mnrlPb.MnrlSuspectedFlaggedMobile{
				{
					MobileNumber:        "1111111111",
					Reason:              "Valid reason",
					ReverifiedAt:        timestamppb.New(time.Now().Add(24 * time.Hour)),
					Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
					LsaCode:             riskEnums.LsaName_LSA_NAME_DELHI,
					TspName:             "Valid TSP",
					SensitivityIndex:    riskEnums.SensitivityIndex_SENSITIVITY_INDEX_HIGH,
					DistributionDetails: "Valid distribution details",
				},
				{
					MobileNumber:        "2222222222",
					Reason:              "Test reason",
					ReverifiedAt:        nil, // Nil reverified_at
					Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
					LsaCode:             riskEnums.LsaName_LSA_NAME_DELHI,
					TspName:             "Test TSP",
					SensitivityIndex:    riskEnums.SensitivityIndex_SENSITIVITY_INDEX_HIGH,
					DistributionDetails: "Test distribution details",
				},
			},
			wantErr: true,
			assertError: func(err error) bool {
				return err != nil && assert.Contains(t, err.Error(), "reverified at is required at index 1")
			},
		},
		{
			name: "fails due to empty tsp_name in batch",
			mnrlSuspectedFlaggedMobiles: []*mnrlPb.MnrlSuspectedFlaggedMobile{
				{
					MobileNumber:        "1111111111",
					Reason:              "Valid reason",
					ReverifiedAt:        timestamppb.New(time.Now().Add(24 * time.Hour)),
					Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
					LsaCode:             riskEnums.LsaName_LSA_NAME_DELHI,
					TspName:             "Valid TSP",
					SensitivityIndex:    riskEnums.SensitivityIndex_SENSITIVITY_INDEX_HIGH,
					DistributionDetails: "Valid distribution details",
				},
				{
					MobileNumber:        "2222222222",
					Reason:              "Test reason",
					ReverifiedAt:        timestamppb.New(time.Now().Add(24 * time.Hour)),
					Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
					LsaCode:             riskEnums.LsaName_LSA_NAME_DELHI,
					TspName:             "", // Empty tsp_name
					SensitivityIndex:    riskEnums.SensitivityIndex_SENSITIVITY_INDEX_HIGH,
					DistributionDetails: "Test distribution details",
				},
			},
			wantErr: true,
			assertError: func(err error) bool {
				return err != nil && assert.Contains(t, err.Error(), "tsp name is required at index 1")
			},
		},
		{
			name: "fails due to nil record in batch",
			mnrlSuspectedFlaggedMobiles: []*mnrlPb.MnrlSuspectedFlaggedMobile{
				{
					MobileNumber:        "1111111111",
					Reason:              "Valid reason",
					ReverifiedAt:        timestamppb.New(time.Now().Add(24 * time.Hour)),
					Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
					LsaCode:             riskEnums.LsaName_LSA_NAME_DELHI,
					TspName:             "Valid TSP",
					SensitivityIndex:    riskEnums.SensitivityIndex_SENSITIVITY_INDEX_HIGH,
					DistributionDetails: "Valid distribution details",
				},
				nil, // Nil record
			},
			wantErr: true,
			assertError: func(err error) bool {
				return err != nil && assert.Contains(t, err.Error(), "mnrl suspected flagged mobile is required at index 1")
			},
		},
		{
			name: "successfully handles large batch of records",
			mnrlSuspectedFlaggedMobiles: func() []*mnrlPb.MnrlSuspectedFlaggedMobile {
				var mobiles []*mnrlPb.MnrlSuspectedFlaggedMobile
				for i := 0; i < 10; i++ {
					mobiles = append(mobiles, &mnrlPb.MnrlSuspectedFlaggedMobile{
						MobileNumber:        fmt.Sprintf("100000000%d", i),
						Reason:              fmt.Sprintf("Reason %d for suspected flagged", i),
						ReverifiedAt:        timestamppb.New(time.Now().Add(time.Duration(24+i) * time.Hour)),
						Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
						LsaCode:             riskEnums.LsaName_LSA_NAME_DELHI,
						TspName:             fmt.Sprintf("TSP %d", i),
						SensitivityIndex:    riskEnums.SensitivityIndex_SENSITIVITY_INDEX_HIGH,
						DistributionDetails: fmt.Sprintf("Distribution details %d", i),
					})
				}
				return mobiles
			}(),
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup test data if provided
			if tt.setupData != nil {
				for _, mobile := range tt.setupData {
					_, err := mnrlSuspectedFlaggedMobileDTS.mnrlSuspectedFlaggedDao.Upsert(context.Background(), mobile)
					require.NoError(t, err, "Failed to setup test data")
				}
			}

			// Execute the batch upsert
			result, err := mnrlSuspectedFlaggedMobileDTS.mnrlSuspectedFlaggedDao.UpsertInBatch(context.Background(), tt.mnrlSuspectedFlaggedMobiles)

			// Validate error expectations
			if (err != nil) != tt.wantErr {
				t.Errorf("UpsertInBatch() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// Validate custom error assertions if provided
			if tt.assertError != nil && err != nil {
				assert.True(t, tt.assertError(err), "Error does not match expected type: %v", err)
			}

			// Validate successful results
			if err == nil {
				assert.NotNil(t, result)
				assert.Len(t, result, len(tt.mnrlSuspectedFlaggedMobiles), "Result length should match input length")

				// Validate each result matches the corresponding input
				for i, inputMobile := range tt.mnrlSuspectedFlaggedMobiles {
					if i < len(result) {
						outputMobile := result[i]
						assert.Equal(t, inputMobile.GetMobileNumber(), outputMobile.GetMobileNumber())
						assert.Equal(t, inputMobile.GetReason(), outputMobile.GetReason())
						assert.Equal(t, inputMobile.GetUsecase(), outputMobile.GetUsecase())
						assert.Equal(t, inputMobile.GetLsaCode(), outputMobile.GetLsaCode())
						assert.Equal(t, inputMobile.GetTspName(), outputMobile.GetTspName())
						assert.Equal(t, inputMobile.GetSensitivityIndex(), outputMobile.GetSensitivityIndex())
						assert.Equal(t, inputMobile.GetDistributionDetails(), outputMobile.GetDistributionDetails())
					}
				}

				// Run custom validation if provided
				if tt.validateData != nil {
					tt.validateData(t)
				}
			}
		})
	}
}
