package dao

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	mnrlPb "github.com/epifi/gamma/api/risk/mnrl"
	"github.com/epifi/gamma/risk/dao/model"
)

type MnrlReportPGDB struct {
	db *gorm.DB
}

func NewMnrlReportPGDB(db types.FRMPGDB) *MnrlReportPGDB {
	return &MnrlReportPGDB{db: db}
}

var _ MnrlReportDao = &MnrlReportPGDB{}

func (m *MnrlReportPGDB) Upsert(ctx context.Context, mnrlReport *mnrlPb.MnrlReport) (*mnrlPb.MnrlReport, error) {
	defer metric_util.TrackDuration("risk/dao", "MnrlReportPGDB", "Upsert", time.Now())

	// Validate all required fields are not empty
	if mnrlReport == nil {
		return nil, fmt.Errorf("mnrl report is required: %w", epifierrors.ErrInvalidArgument)
	}
	if mnrlReport.GetMobileNumber() == "" {
		return nil, fmt.Errorf("mobile number is required: %w", epifierrors.ErrInvalidArgument)
	}
	if mnrlReport.GetTspName() == "" {
		return nil, fmt.Errorf("tsp name is required: %w", epifierrors.ErrInvalidArgument)
	}
	if mnrlReport.GetDateOfDisconnection() == nil {
		return nil, fmt.Errorf("date of disconnection is required: %w", epifierrors.ErrInvalidArgument)
	}
	if mnrlReport.GetActionReasons() == "" {
		return nil, fmt.Errorf("action reasons is required: %w", epifierrors.ErrInvalidArgument)
	}

	mnrlReportModel := model.NewMnrlReportModel(mnrlReport)
	db := gormctxv2.FromContextOrDefault(ctx, m.db)
	err := db.Clauses(clause.OnConflict{
		Columns: []clause.Column{
			{Name: "mobile_number"}, // Conflict on mobile_number column
		},
		DoUpdates: clause.AssignmentColumns([]string{
			"lsa_name",
			"tsp_name",
			"date_of_disconnection",
			"action_reasons",
			"usecase",
			"updated_at",
		}),
	}).Create(&mnrlReportModel).Error

	if err != nil {
		return nil, fmt.Errorf("error upserting mnrl report in db: %w", err)
	}
	return mnrlReport, nil
}

func (m *MnrlReportPGDB) UpsertInBatch(ctx context.Context, mnrlReports []*mnrlPb.MnrlReport) ([]*mnrlPb.MnrlReport, error) {
	defer metric_util.TrackDuration("risk/dao", "MnrlReportPGDB", "UpsertInBatch", time.Now())

	// Validate all required fields are not empty for each record
	for i, mnrlReport := range mnrlReports {
		if mnrlReport == nil {
			return nil, fmt.Errorf("mnrl report is required at index %d: %w", i, epifierrors.ErrInvalidArgument)
		}
		if mnrlReport.GetMobileNumber() == "" {
			return nil, fmt.Errorf("mobile number is required at index %d: %w", i, epifierrors.ErrInvalidArgument)
		}
		if mnrlReport.GetTspName() == "" {
			return nil, fmt.Errorf("tsp name is required at index %d: %w", i, epifierrors.ErrInvalidArgument)
		}
		if mnrlReport.GetDateOfDisconnection() == nil {
			return nil, fmt.Errorf("date of disconnection is required at index %d: %w", i, epifierrors.ErrInvalidArgument)
		}
		if mnrlReport.GetActionReasons() == "" {
			return nil, fmt.Errorf("action reasons is required at index %d: %w", i, epifierrors.ErrInvalidArgument)
		}
	}

	var mnrlReportModels []*model.MnrlReport
	for _, mnrlReport := range mnrlReports {
		mnrlReportModels = append(mnrlReportModels, model.NewMnrlReportModel(mnrlReport))
	}
	db := gormctxv2.FromContextOrDefault(ctx, m.db)
	err := db.Clauses(clause.OnConflict{
		Columns: []clause.Column{
			{Name: "mobile_number"}, // Conflict on mobile_number column
		},
		DoUpdates: clause.AssignmentColumns([]string{
			"lsa_name",
			"tsp_name",
			"date_of_disconnection",
			"action_reasons",
			"usecase",
			"updated_at",
		}),
	}).Create(&mnrlReportModels).Error

	if err != nil {
		return nil, fmt.Errorf("error upserting mnrl report in db: %w", err)
	}
	return mnrlReports, nil
}
