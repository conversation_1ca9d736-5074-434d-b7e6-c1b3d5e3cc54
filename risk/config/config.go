package config

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"sync"
	"time"

	"github.com/knadh/koanf"
	"github.com/mitchellh/mapstructure"

	"github.com/epifi/be-common/pkg/cfg"

	escalationPb "github.com/epifi/gamma/api/risk/case_management/escalation"
	"github.com/epifi/gamma/risk/config/common"

	"github.com/pkg/errors"
)

const (
	BUCKET_REDLIST          = "bucketredlist"
	EncryptorData           = "EncryptorData"
	FRMPgdbUsernamePassword = "FRMPgdbUsernamePassword" // nolint: gosec
)

var (
	once       sync.Once
	config     *Config
	err        error
	_, b, _, _ = runtime.Caller(0)
	// DefaultUnmarshallingConfig to keep viper and koanf unmarshalling on same page
	DefaultUnmarshallingConfig = func(o interface{}) koanf.UnmarshalConf {
		return koanf.UnmarshalConf{
			DecoderConfig: &mapstructure.DecoderConfig{
				DecodeHook: mapstructure.ComposeDecodeHookFunc(
					mapstructure.StringToTimeDurationHookFunc(),
					mapstructure.StringToSliceHookFunc(","),
					mapstructure.StringToTimeHookFunc(time.RFC3339),
				),
				WeaklyTypedInput: true,
				Result:           o,
			},
		}
	}
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig()
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to load config")
	}

	return config, nil
}

func loadConfig() (*Config, error) {
	conf := &Config{}
	// will be used only for test environment
	testConfigDirPath := testEnvConfigDir()

	// loads config from file
	k, _, err := cfg.LoadConfigUsingKoanf(testConfigDirPath, cfg.RISK_SERVICE)

	if err != nil {
		return nil, fmt.Errorf("failed to load dynamic config: %w", err)
	}

	err = k.UnmarshalWithConf("", conf, DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to refresh dymanic config: %w", err)
	}

	// Load all secrets and db certs for EPIFI db
	keyToSecret, err := cfg.LoadAllSecrets(conf.EpifiDb, conf.Secrets.Ids, conf.Application.Environment, conf.AWS.Region)
	if err != nil {
		return nil, err
	}

	var (
		frmDBKeyToSecret map[string]string
	)

	// Load db certs for FRM DB
	frmKeyToIdMap := cfg.AddCrdbSslCertSecretIds(conf.FRMDb, conf.Secrets.Ids)
	frmDBKeyToSecret, err = cfg.LoadSecrets(frmKeyToIdMap, conf.Application.Environment, conf.AWS.Region)
	if err != nil {
		return nil, errors.Wrap(err, "failed to load secrets for FRMDb")
	}

	// Update the secret values in config for frm pgdb
	if conf.IsFRMPgdbEnabled {
		// Load certs for FRM PGDB
		frmPGDBKeyToIdMap := cfg.AddPgdbSslCertSecretIds(conf.FRMPgdb, conf.Secrets.Ids)
		frmPGDBKeyToSecret, err := cfg.LoadSecrets(frmPGDBKeyToIdMap, conf.Application.Environment, conf.AWS.Region)
		if err != nil {
			return nil, errors.Wrap(err, "failed to load secrets for FRM PGDB")
		}
		err = updateDefaultConfigForFRMPgdb(conf, frmPGDBKeyToSecret)
		if err != nil {
			return nil, fmt.Errorf("failed to update default config for frm pgdb %w", err)
		}
	}

	// Update the secret values in config
	if err := updateDefaultConfig(conf, keyToSecret, frmDBKeyToSecret); err != nil {
		return nil, err
	}

	return conf, nil
}

// update the DB endpoint and port for all dbs
// update the env variable
// update the default secret keys in the config with the secret values fetched from Secret manager
func updateDefaultConfig(c *Config, keyToSecret map[string]string, frmKeyToSecret map[string]string) error {
	dbServerEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_CRDB)
	// Update db endpoints for all dbs used
	cfg.UpdateDbEndpointInConfig(c.EpifiDb, dbServerEndpoint)
	cfg.UpdateDbEndpointInConfig(c.FRMDb, dbServerEndpoint)

	if err := readAndSetEnv(c); err != nil {
		return fmt.Errorf("failed to read and set env var: %w", err)
	}

	// update all application and db secret values
	cfg.UpdateSecretValues(c.EpifiDb, c.Secrets, keyToSecret)
	cfg.UpdateSecretValues(c.FRMDb, c.Secrets, frmKeyToSecret)
	return nil
}

func updateDefaultConfigForFRMPgdb(c *Config, frmPGDBKeyToSecret map[string]string) error {
	serverEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_RDS)
	cfg.UpdateDbEndpointInConfig(c.FRMPgdb, serverEndpoint)
	cfg.UpdatePGDBSecretValues(c.FRMPgdb, c.Secrets, frmPGDBKeyToSecret)
	if c.Application.Environment == cfg.TestEnv || c.Application.Environment == cfg.DevelopmentEnv {
		cfg.UpdateDbUsernamePasswordInConfig(c.FRMPgdb, c.Secrets.Ids[FRMPgdbUsernamePassword])
		return nil
	}
	if _, ok := frmPGDBKeyToSecret[FRMPgdbUsernamePassword]; !ok {
		return errors.New("failed to fetch db username password for frm postgres db from secrets manager")
	}
	cfg.UpdateDbUsernamePasswordInConfig(c.FRMPgdb, frmPGDBKeyToSecret[FRMPgdbUsernamePassword])
	return nil
}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config) error {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Name = val
	}

	if val, ok := os.LookupEnv("SERVER_PORT"); ok {
		intVal, err := strconv.Atoi(val)
		if err != nil {
			return fmt.Errorf("failed to convert port to int: %w", err)
		}

		c.Server.Ports.GrpcPort = intVal
	}

	if val, ok := os.LookupEnv("DB_HOST"); ok {
		c.EpifiDb.Host = val
		c.FRMDb.Host = val
	}

	if val, ok := os.LookupEnv("PGDB_HOST"); ok {
		c.FRMPgdb.Host = val
	}

	return nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..", "values")
	return configPath
}

//go:generate conf_gen github.com/epifi/gamma/risk/config Config
type Config struct {
	Application *Application
	Server      *Server
	EpifiDb     *cfg.DB

	FeatureFlags *FeatureFlags `dynamic:"true"`

	HunterRollOutPercentage                         int `dynamic:"true"`
	FRMDb                                           *cfg.DB
	FRMPgdb                                         *cfg.DB
	AWS                                             *Aws
	ProcessRedListUpdateSqsSubscriber               *cfg.SqsSubscriber        `dynamic:"true"`
	ProcessSyncLeaActorsSqsSubscriber               *cfg.SqsSubscriber        `dynamic:"true"`
	ProcessRiskCasesIngestEventBEQueueSubscriber    *cfg.SqsSubscriber        `dynamic:"true"`
	ProcessRiskCasesIngestEventDataQueueSubscriber  *cfg.SqsSubscriber        `dynamic:"true" iam:"external-consumer"`
	ProcessRiskAlertIngestEventDSQueueSubscriber    *cfg.SqsSubscriber        `dynamic:"true" iam:"external-consumer"`
	ProcessCallRoutingEventSqsSubscriber            *cfg.SqsSubscriber        `dynamic:"true"`
	ProcessDisputeUploadSqsSubscriber               *cfg.SqsSubscriber        `dynamic:"true"`
	ProcessFormSubmissionEventSqsSubscriber         *cfg.SqsSubscriber        `dynamic:"true"`
	OnboardingVelocityConfig                        *OnboardingVelocityConfig `dynamic:"true"`
	RiskAlertIngestionQueueSubscriber               *cfg.SqsSubscriber        `dynamic:"true"`
	RiskBatchRuleEngineEventQueueSubscriber         *cfg.SqsSubscriber        `dynamic:"true" iam:"external-consumer"`
	RiskCXTicketUpdateEventQueueSubscriber          *cfg.SqsSubscriber        `dynamic:"true"`
	RiskAccountOperationStatusUpdateQueueSubscriber *cfg.SqsSubscriber        `dynamic:"true"`
	ProcessRiskSignalEventQueueSubscriber           *cfg.SqsSubscriber        `dynamic:"true" iam:"external-consumer"`
	ProcessMnrlReportSqsSubscriber                  *cfg.SqsSubscriber        `dynamic:"true"`
	ProcessMnrlSuspectedFlaggedMobileSqsSubscriber  *cfg.SqsSubscriber        `dynamic:"true"`
	IsFRMPgdbEnabled                                bool
	// Stores decimal precision for which we perform Geo-Location Lat-Long check
	RedListLatLongPrecisions []int
	TestFlag                 bool `dynamic:"true"`

	OrderUpdateTxnMonitoringSubscriber *cfg.SqsSubscriber `dynamic:"true"`
	TxnMonitoringConfig                *TxnMonitoringConfig
	Secrets                            *cfg.Secrets
	Tracing                            *cfg.Tracing
	CMConsumerConfig                   *CMConsumerConfig `dynamic:"true"`
	BlockingFlowErrorReportConfig      *BlockingErrorReportConfig
	Profile                            *Profile       `dynamic:"true"`
	RedList                            *RedListConfig `dynamic:"true"`
	LeaActorsPublisher                 *cfg.SqsPublisher
	LEAComplaint                       *LEAComplaintConfig
	CasePriorityQueue                  *CasePriorityQueuesConfig `dynamic:"true"`
	CaseStore                          *common.CaseStoreConfig
	UpiVPANameMatchingCheckConfig      *UpiVPANameMatchCheckConfig `dynamic:"true"`

	AffluenceCheckConfig *AffluenceCheckConfig `dynamic:"true"`
	ScreenerConfig       *ScreenerConfig       `dynamic:"true"`

	JunkEmailCheckConfig             *JunkEmailChecksConfig            `dynamic:"true"`
	LocationModelCheck               *LocationModelCheckConfig         `dynamic:"true"`
	DynamicElementsConfig            *DynamicElementsConfig            `dynamic:"true"`
	InstalledAppsCheckConfig         *InstalledAppsCheckConfig         `dynamic:"true"`
	CreditReportAffluenceClassConfig *CreditReportAffluenceClassConfig `dynamic:"true"`
	CrossVideoFacematchCheckConfig   *CrossVideoFacematchCheckConfig   `dynamic:"true"`
	SAUnreviewedAlertsCheckConfig    *SAUnreviewedAlertsCheckConfig    `dynamic:"true"`

	// stores the mapping between the fi product and their respective segment id that is used to identify if user has a
	// relationship with the fi for that particular product.
	FIProductToSegmentMap map[string]string
	// stores various variables determining contact associations
	ContactAssociationsConfig *ContactAssociationsConfig
	// contact association screener check config
	ContactAssociationsCheckConfig *ContactAssociationsCheckConfig `dynamic:"true"`
	InvestigationEmail             *common.InvestigationEmailConfig

	Escalation          *EscalationConfig
	RiskBankActionComms *common.RiskBankActionComms
	// common bank actions config
	RiskBankAction                     *common.RiskBankAction
	DronapayRuleHitCallbackPublisher   *cfg.SqsPublisher
	FormSubmissionEventPublisher       *cfg.SqsPublisher
	AlertsPublisher                    *cfg.SqsPublisher
	RuleHitCallbackSubscriber          *cfg.SqsSubscriber `dynamic:"true"`
	BatchRuleEngineS3                  *BatchRuleEngineS3Config
	ProfileBannerForSherlock           *ProfileBannerForSherlock           `dynamic:"true"`
	ValidateSavingsAccountStatusConfig *ValidateSavingsAccountStatusConfig `dynamic:"true"`
	RuleTags                           []string                            `dynamic:"true"`
	WebForm                            *WebFormConfig
	LowContactCountCheckConfig         *LowContactCountCheckConfig `dynamic:"true"`
	VPNPresenceCheckConfig             *VPNPresenceCheckConfig     `dynamic:"true"`
	UnifiedLEAComplaintConfig          *common.UnifiedLeaComplaintConfig
	WebFormConfig                      *common.WebFormConfig
	// added dummy PercentageRolloutReleaseConfig to help with risk/essential/release/release.go
	PercentageRolloutReleaseConfig               *PercentageRolloutReleaseConfig               `dynamic:"true"`
	IPAddressRedListCheckConfig                  *IPAddressRedListCheckConfig                  `dynamic:"true"`
	ContactAssociationAndRiskyProfileCheckConfig *ContactAssociationAndRiskyProfileCheckConfig `dynamic:"true"`
}

type Application struct {
	Environment string
	Name        string
}

type Server struct {
	Ports        *cfg.ServerPorts
	EnablePoller bool
}

type Aws struct {
	Region string
	S3     *S3
}

type S3 struct {
	BucketNames map[string]string `iam:"s3-readwrite"`
}

type OnboardingVelocityConfig struct {
	BucketExpiry       time.Duration `dynamic:"true"`
	QueryRangeDuration time.Duration `dynamic:"true"`
	Threshold          int64         `dynamic:"true"`
	D2HThreshold       int64         `dynamic:"true"`
	BucketPrecision    int           `dynamic:"true"`
}

type AesTxnMonitoringEncryption struct {
	EncryptorKey string

	EncryptorIv string
}

type TxnMonitoringConfig struct {
	// This will be used as a datetime object to back-fill older entities created in risk evaluator like dronapay.
	// So all the entities which got created before the configured date will be updated again in vendor.
	UpdateEntityDate time.Time

	// This will be the batch size for getting contacts from the user contact server to calculate the contact signal
	ContactSignalBatchSize uint32

	// This will be the max iteration value for contact signal calculations. This will determine the total no.
	// of contacts we would need to check to calculate the contact signals
	// So total no of contacts we would iterate would be equal to ContactSignalBatchSize * ContactSignalsMaxIteration
	ContactSignalsMaxIteration int

	// Wait Threshold to sync transaction to FRM
	// post this time we will not retry in case of category not found and will proceed with sync
	ThresholdForSyncWithoutCategory time.Duration
}

// CMConsumerConfig defines Case management consumer config
type CMConsumerConfig struct {
	// This will be the batch size to create alerts from raw alerts
	CreateAlertsBatchSize uint32
	// CaseManagementRollOutPercentage defines Case management flow roll out percentage for payload types
	// map[Payload Type]RollOut Percentage
	CaseManagementRollOutPercentage map[string]uint64 `dynamic:"true"`
	// ProcessActiveRuleAlerts controls whether active rule alerts are processed
	// If false, active rule alerts will not trigger workflows and will be treated like shadow rules
	ProcessActiveRuleAlerts bool `dynamic:"true"`
}

type BlockingErrorReportConfig struct {
	ReportName  string
	ToEmailId   string
	FromEmailId string
}

// Profile defines config for profile service
type Profile struct {
	// Profile service will not consider pending reviews older than FetchReviewsPeriod (days)
	FetchReviewsPeriod time.Duration `dynamic:"true"`
}

type RedListConfig struct {
	// Onboarding state risk score threshold to sync with risk evaluator entity
	OnboardingStateThreshold uint32 `dynamic:"true"`
	// risk score threshold for High risk atm list to sync with risk evaluator entity
	AtmHighRiskThreshold uint32 `dynamic:"true"`
	// risk score threshold for Medium risk atm list to sync with risk evaluator entity
	AtmMediumRiskThreshold uint32 `dynamic:"true"`
}

type AffluenceCheckConfig struct {
	IsEnabled bool `dynamic:"true"`
	// if users onboarding ds score is above this affluence check will be failed
	LowAffluenceClassDSScoreThreshold int `dynamic:"true"`
	// if users is self-employed and  onboarding ds score is above this affluence check will be failed
	LowAffluenceClassAndSelfEmployedDSScoreThreshold int `dynamic:"true"`

	// Key will be affluence check enum as string and
	// value contains score to be sent back from processor if the given check fails
	// score will be between the range 0 to 100
	AffluenceCheckScoreMapping map[string]int `dynamic:"true"`
}

// CasePriorityQueuesConfig defines configs for case priority queues
type CasePriorityQueuesConfig struct {
	LiveQueueTags            []string                 `dynamic:"true"`
	TransactionQueueTags     []string                 `dynamic:"true"`
	PostOnboardingQueueTags  []string                 `dynamic:"true"`
	EscalationQueueTags      []string                 `dynamic:"true"`
	QueueCapacityMap         map[string]int           `dynamic:"true"`
	QueueTTLMap              map[string]time.Duration `dynamic:"true"`
	CachedCaseStaleThreshold time.Duration            `dynamic:"true"`
	ModelSelectionRates      *ModelSelectionRates     `dynamic:"true"`
}

type ModelSelectionRates struct {
	Model1Rate               int8 `dynamic:"true"` // stores the percentage of cases to be selected for assignment based on model1 score
	Model2Rate               int8 `dynamic:"true"` // stores the percentage of cases to be selected for assignment based on model2 score
	CgRate                   int8 `dynamic:"true"` // stores the percentage of cases to be selected for assignment randomly
	UseModelRateForSelection bool `dynamic:"true"` // Feature flag for AB-testing, if turned on, above 3 model rates will be used
}

type UpiVPANameMatchCheckConfig struct {
	IsEnabled bool `dynamic:"true"`
	// Complete mismatch score, passed when there are no UPI matches
	CompleteMismatchScore float32
	// Failure score for mismatched cases, since for now we are keeping static
	FailureScore float32
}

type JunkEmailChecksConfig struct {
	IsEnabled bool `dynamic:"true"`

	// Key will be JunkEmail check enum as string and
	// value contains score to be assigned if the given check fails
	// score will be in the range 0 to 100
	JunkEmailCheckScoreMapping map[string]int `dynamic:"true"`
}

type ScreenerConfig struct {
	// map contains criteria to risk check config
	// key is screener criteria enum as string and value contains a struct with required config
	CriteriaToRiskChecksConfigMap map[string]*RiskChecksConfig `dynamic:"true"`
	// map contains criteria to screener actions threshold config mapping
	// key is screener criteria enum as string and value contains a struct with action thresholds
	CriteriaToRiskActionThresholdMap map[string]*ScreenerActionThresholds `dynamic:"true"`
	// map[RiskParam]StoredResultTTL.
	// value is default TTL of stored result and can be overridden at criteria level.
	// A non-positive TTL signifies stored result can't be used for check
	// i.e. check should be reevaluated for each run.
	RiskCheckStoredResultDefaultTTLMap map[string]time.Duration `dynamic:"true"`
	// key is criteria enum as string and value contains a struct with rule config
	CriteriaToRuleConfigMap map[string]*ScreenerRuleConfig
	// Defines the time threshold for a screener run to finish,
	// if screener attempt status is still in progress post this threshold, it should be considered invalid
	ScreenerAttemptMaxRunTime time.Duration `dynamic:"true"`
}

type ScreenerActionThresholds struct {
	// If any risk checks performed for user has higher score than this, user's screener will be failed with auto-block action
	AutoBlockThreshold int32 `dynamic:"true"`
	// If any risk checks performed for user has higher score than this, user's screener will be failed and sent for manual review
	ManualReviewThreshold int32 `dynamic:"true"`
	// Additional check that is performed post normal checks are done
	ThresholdToTipManualReviewToAutoBlock *ThresholdToTipManualReviewToAutoBlock `dynamic:"true"`
}

type ThresholdToTipManualReviewToAutoBlock struct {
	IsEnabled                bool `dynamic:"true"`
	Threshold                int  `dynamic:"true"`
	FailureScore             float32
	ScreenerChecksOfInterest []string
}

type RiskChecksConfig struct {
	// map contains risk param to bool mapping
	// bool value indicates whether given risk param check is enabled for current entrypoint
	RiskParamConfigMap map[string]*RiskParamConfig `dynamic:"true"`
}

type RiskParamConfig struct {
	IsEnabled   bool `dynamic:"true"`
	IsMandatory bool `dynamic:"true"`
	// Any value other than zero overrides default TTL for check.
	// A negative TTL signifies stored result can't be used for check i.e.
	// check should be reevaluated for each run.
	StoredResultTTL time.Duration `dynamic:"true"`
}

type LocationModelCheckConfig struct {
	IsEnabled bool `dynamic:"true"`

	// Key will be risk severity enum as string and
	// value contains score to be sent back from processor if the given check fails
	// score will be between the range 0 to 100
	RiskSeverityToScoreMap map[string]int `dynamic:"true"`
}

type DynamicElementsConfig struct {
	IsEnabled bool `dynamic:"true"`
	// map to keep dynamic element banner content for each type of freeze applied against user
	FreezeStatusToBannerConfigMap map[string]*BannerConfig
	OutcallBanner                 *BannerConfig
	// Threshold duration before which LEA compliant won't be considered to display banner
	// This ia check to avoid displaying stale LEA data to user
	DurationToCheckPreviousLEAComplaints time.Duration
}

type PopupCTAConfig struct {
	CTAImageURL     string
	Title           string
	Body            string
	IconUrl         string
	BackgroundColor string
	DismissCTAText  string
	// CTA to redirect to a screen
	RedirectCTA    *BasicCTA
	NotifyLaterCTA *BasicCTA
}

type BannerConfig struct {
	FontColor               string
	Title                   string
	BodyColor               string
	ImageURL                string
	PopupCTAConfig          *PopupCTAConfig
	PopupCTAWithFormsConfig *PopupCTAConfig
	IndicatorSelectedColor  string
	IndicatorDefaultColor   string
}

type BasicCTA struct {
	Title     string
	FontColor string
	BodyColor string
}

type InstalledAppsCheckConfig struct {
	IsEnabled bool `dynamic:"true"`
	// List of social apps
	SocialApps []string `dynamic:"true"`
	// Tipping point for less social app count
	MinRequiredSocialAppCount int32 `dynamic:"true"`
	// Minimum required count of non system apps
	MinRequiredNonSystemAppCount int32 `dynamic:"true"`
	// Key will be installed app sub check enum as string and
	// value contains score to be assigned if the given check fails
	// score will be in the range 0 to 100
	CheckToScoreMap map[string]int `dynamic:"true"`
	// We need to validate with previous failures for this rule
	// For this duration previous attempts for same rule will be fetched, and if there are
	// failures, user will be failed again
	DurationToCheckForPreviousFailures time.Duration
}

type PercentageRolloutReleaseConfig struct {
	FeatureName       string `dynamic:"true"`
	RolloutPercentage int    `dynamic:"true"`
}

type LEAComplaintConfig struct {
	ReviewExternalRuleId string
	// Do not create case if complaint audit date is before cut off time
	// i.e. old complaints are uploaded.
	AuditDateCutOffForCaseCreation time.Duration
}

type ContactAssociationsConfig struct {
	// This will be the batch size for getting contacts from the user contact server to calculate the contact signal
	ContactSignalBatchSize uint32

	// This will be the max iteration value for contact signal calculations. This will determine the total no.
	// of contacts we would need to check to calculate the contact signals
	// So total no of contacts we would iterate would be equal to ContactSignalBatchSize * ContactSignalsMaxIteration
	ContactSignalsMaxIteration int
}

type ScreenerRuleConfig struct {
	ExternalRuleIdForAutoBlock    string
	ExternalRuleIdForManualReview string
}

type ValidateSavingsAccountStatusConfig struct {
	IsEnabled        bool `dynamic:"true"`
	FailedCheckScore float32
}

type ContactAssociationsCheckConfig struct {
	IsEnabled bool `dynamic:"true"`
	// the current logic being
	//	block if ingress  LEA count >= IngressMaxAssociationsWithLEAThreshold
	//	block if egress LEA count >= EgressMaxAssociationsWithLEAThreshold
	//	manual review if ingress LEA count >= IngressManualReviewForLEAThreshold
	//	manual review if ingress blocked count >= IngressManualReviewForBlockedThreshold
	// if not then we will mark the contact association check as PASSED
	IngressMaxAssociationsWithLEAThreshold int
	IngressManualReviewForLEAThreshold     int
	IngressManualReviewForBlockedThreshold int
	EgressMaxAssociationsWithLEAThreshold  int
	FailedCheckScore                       float32
	ManualReviewFailCheckScore             float32
}

type IPAddressRedListCheckConfig struct {
	IsEnabled bool `dynamic:"true"`
	// score to be returned for the case to get into manual review
	ManualReviewScore float32
	// min res list score of the IP to send for manual review
	MinRedListScoreToSendForManualReview float32
}

type ContactAssociationAndRiskyProfileCheckConfig struct {
	IsEnabled bool `dynamic:"true"`
	// score to be returned for the case to get into manual review
	ManualReviewScore float32
}

type FeatureFlags struct {
	EnablePGDBForTxnMonitoring bool `dynamic:"true"`
}

type EscalationConfig struct {
	// contains mapping of review type enums to config of questions to be asked to users
	QuestionsConf map[string]*EscalationQuestionConfig

	EscalationEventPublisher *cfg.SqsPublisher
}

type EscalationQuestionConfig struct {

	// common set of question to be asked based on review type irrespective of employment type
	CommonQuestions []*EscalationQuestion

	// Set of employment type specific questions to be asked to users
	// key is employment type enum as string
	EmploymentSpecificQuestions map[string][]*EscalationQuestion

	DefaultEmploymentQuestions []*EscalationQuestion
}

type EscalationQuestion struct {
	QuestionText string

	// escalationPb.FieldType enum as string
	FieldType string

	FieldOptions *EscalationQuestionFieldOptions
}

type EscalationQuestionFieldOptions struct {
	DropdownFieldOptions *escalationPb.DropdownFieldOptions
}

type CreditReportAffluenceClassConfig struct {
	IsEnabled bool `dynamic:"true"`
	// credit score below this will be considered invalid
	InvalidCreditScoreThreshold int32
	// users below this threshold will be marked as failed
	FailCreditScoreThreshold int32
	// users above this credit score threshold will be passed
	PassCreditScoreThreshold int32
	// failure score
	FailureScore float32
	// income threshold below which we'll consider a bad affluence
	BadAffluenceIncomeThreshold int64
	// failed as borrower has intentionally chosen not to repay the loan
	// config is absolute suits failure, above this value will be marked for fail
	SuitFiledWilfulDefault int
	// failed as debt is written off, means that the lender has given up on trying
	// to collect the debt and has written it off as a loss
	// config is absolute suits failure, above this value will be marked for fail
	SuitFiledWillfulDefaultWrittenOff int
}

type BatchRuleEngineS3Config struct {
	BucketName string `iam:"s3-readwrite"`
}

type ProfileBannerForSherlock struct {
	IsEnabled bool `dynamic:"true"`
	// under a subcategory, how many elements we need to display
	MaxBannersForType int
}

type CrossVideoFacematchCheckConfig struct {
	IsEnabled bool `dynamic:"true"`
	// cutoff from DS team below which liveness is marked as failed
	LivenessScoreCutoff float32
	// cutoff from DS team below which facematch is marked as failed
	FaceMatchScoreCutoff float32
	// failure score to be passed back in case of check failure
	// should be in range of [0, 1]
	FailureScore float32
}

type SAUnreviewedAlertsCheckConfig struct {
	IsEnabled                    bool          `dynamic:"true"`
	HighPrecisionRuleHitDuration time.Duration `dynamic:"true"`
	LowPrecisionRuleHitDuration  time.Duration `dynamic:"true"`
	HighPrecisionRuleThreshold   float32
}

type WebFormConfig struct {
	BaseUrl string
}

type LowContactCountCheckConfig struct {
	// If contact count is equal or below this threshold,
	// user will be failed and sent for manual review by the check
	LowContactCountManualReviewThreshold int `dynamic:"true"`
	// If contact count is equal or below this threshold,
	// user will be passed by the check but a potential risk flag will be returned
	LowContactCountFlagThreshold int `dynamic:"true"`
}

type VPNPresenceCheckConfig struct {
	IsEnabled bool `dynamic:"true"`
}
