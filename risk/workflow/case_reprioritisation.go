package workflow

import (
	"errors"
	"fmt"
	"time"

	workflowPkg "github.com/epifi/be-common/pkg/epifitemporal/workflow"

	riskNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/risk"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"

	activityPb "github.com/epifi/gamma/api/risk/case_management/activity"
	riskEnums "github.com/epifi/gamma/api/risk/enums"
	workflowPb "github.com/epifi/gamma/api/risk/workflow"
)

const (
	// Activity timeout for grouping tickets
	GroupTicketsActivityTimeout = 60 * time.Second
	MaxBulkUpdateBatchSize      = 100 // Maximum number of tickets allowed by FD to update in a single bulk update call
)

// ModelType represents which model (1 or 2) the score group belongs to
type ModelType int

const (
	Model1            ModelType = 1
	Model2            ModelType = 2
	BulkUpdateLockKey           = "risk-bulk-update-tickets-cache-lock-key"
)

// ScoreGroup represents a group of tickets with the same score and model
type ScoreGroup struct {
	Score     float32   `json:"score"`
	ModelName string    `json:"model_name"`
	ModelType ModelType `json:"model_type"`
	TicketIds []string  `json:"ticket_ids"`
}

// CaseReprioritisation orchestrates the complete case reprioritization workflow
// Process: CRM fetch → ML scoring → grouping → bulk updates
func CaseReprioritisation(ctx workflow.Context, req *workflowPb.CaseReprioritisationRequest) (*workflowPb.CaseReprioritisationResponse, error) {
	logger := workflow.GetLogger(ctx)
	logger.Info("Starting case reprioritisation workflow",
		zap.Bool("dry_run", req.GetDryRun()))

	response := &workflowPb.CaseReprioritisationResponse{}

	// Step 1: Fetch tickets from CRM
	ticketsS3Url := ""
	var err error
	if !req.GetIsManualUpload() {
		ticketsS3Url, err = fetchTicketsFromCRM(ctx, req)
		if err != nil {
			return response, err
		}
	} else {
		if req.GetTicketsS3Url() == "" {
			logger.Error("S3 Tickets Url is empty in case of manual upload")
			return nil, errors.New("failed to get S3 URL for a manual upload")
		}
		ticketsS3Url = req.GetTicketsS3Url()
	}

	// Step 2: Get confidence scores for all tickets
	ticketsWithScores, erroredTickets, err := getScoresForAllTickets(ctx, ticketsS3Url)
	if err != nil {
		return response, err
	}

	logger.Debug("Successfully processed tickets for scoring",
		zap.Int("tickets_with_scores", len(ticketsWithScores)),
		zap.Int("errored_tickets", len(erroredTickets)))

	// Step 3: Group tickets by score and model for both Model1 and Model2
	model1Groups, model2Groups := groupTicketsByScoreForBothModels(ctx, ticketsWithScores)

	logger.Debug("Successfully grouped tickets by score",
		zap.Int("model1_groups", len(model1Groups)),
		zap.Int("model2_groups", len(model2Groups)))

	// Step 4: Bulk update tickets in each group - Process Model1 first, then Model2
	allErroredTickets := append([]string{}, erroredTickets...)
	if !req.GetDryRun() {
		// Phase 1: Process all Model1 score groups
		logger.Info("Starting Phase 1: Processing Model1 score groups", zap.Int("groups", len(model1Groups)))
		for _, group := range model1Groups {
			allErroredTicketsInGroup := processBulkUpdateInBatches(ctx, group)
			allErroredTickets = append(allErroredTickets, allErroredTicketsInGroup...)
		}

		// Phase 2: Process all Model2 score groups
		logger.Info("Starting Phase 2: Processing Model2 score groups", zap.Int("groups", len(model2Groups)))
		for _, group := range model2Groups {
			allErroredTicketsInGroup := processBulkUpdateInBatches(ctx, group)
			allErroredTickets = append(allErroredTickets, allErroredTicketsInGroup...)
		}
	} else {
		logger.Info("Dry run mode - skipping actual score updates")
	}

	logger.Info("Case reprioritisation workflow completed",
		zap.Int("total_processed", len(ticketsWithScores)),
		zap.Int("total_errored", len(allErroredTickets)))

	return response, nil
}

// fetchTicketsFromCRM retrieves all unassigned tickets from Freshdesk CRM
func fetchTicketsFromCRM(ctx workflow.Context, req *workflowPb.CaseReprioritisationRequest) (string, error) {
	logger := workflow.GetLogger(ctx)
	logger.Info("Fetching tickets from CRM")

	var response activityPb.GetCasesFromFreshdeskResponse
	err := activityPkg.Execute(ctx, riskNs.GetAllFreshdeskTickets, &response, &activityPb.GetCasesFromFreshdeskRequest{
		RequestHeader: &activity.RequestHeader{},
		Filters: &activityPb.CaseFetchFilters{
			UnassignedOnly: req.GetFilters().GetUnassignedOnly(),
			Status:         activityPb.Status_STATUS_CREATED,
			CreatedAfter:   req.GetFilters().GetCreatedAfter(),
			UpdatedAfter:   req.GetFilters().GetUpdatedAfter(),
			ReviewTypes:    req.GetFilters().GetReviewTypes(),
		},
	})

	if err != nil {
		return "", fmt.Errorf("GetAllFreshdeskTickets activity failed: %w", err)
	}

	return response.GetS3Path(), nil
}

// getScoresForAllTickets runs GetScoresForCases workflow to get ML confidence scores
func getScoresForAllTickets(ctx workflow.Context, s3Path string) ([]*workflowPb.TicketWithScore, []string, error) {
	logger := workflow.GetLogger(ctx)
	logger.Info("Getting confidence scores for all tickets")

	// Generate UUID for child workflow ID
	childWorkflowId, err := epifitemporal.GenerateUUID(ctx)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to generate UUID for child workflow: %w", err)
	}

	childWorkflowOptions := workflow.ChildWorkflowOptions{
		WorkflowID: childWorkflowId,
	}

	ctx = workflow.WithChildOptions(ctx, childWorkflowOptions)

	// Generate batch ID
	batchId, err := epifitemporal.GenerateUUID(ctx)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to generate UUID for batch: %w", err)
	}

	var scoringResponse workflowPb.GetScoresForCasesResponse
	err = workflowPkg.ExecuteChildWorkflow(ctx, childWorkflowId, riskNs.GetScoresForCases, &workflowPb.GetScoresForCasesRequest{
		S3Path:               s3Path,
		BatchId:              batchId,
		MaxConcurrentDsCalls: 30,
	}, &scoringResponse)

	if err != nil {
		return nil, nil, fmt.Errorf("GetScoresForCases workflow failed: %w", err)
	}

	return scoringResponse.GetTicketsWithScores(), scoringResponse.GetErroredTicketIds(), nil
}

// groupTicketsByScoreForBothModels groups tickets by score+model for both Model1 and Model2
func groupTicketsByScoreForBothModels(ctx workflow.Context, ticketsWithScores []*workflowPb.TicketWithScore) ([]ScoreGroup, []ScoreGroup) {
	logger := workflow.GetLogger(ctx)
	logger.Debug("Grouping tickets by score and model for both Model1 and Model2")

	// Group tickets by Model1
	model1Groups := groupTicketsByScoreForModel(ctx, ticketsWithScores, Model1)

	// Group tickets by Model2
	model2Groups := groupTicketsByScoreForModel(ctx, ticketsWithScores, Model2)

	logger.Debug("Successfully grouped tickets for both models",
		zap.Int("model1_groups", len(model1Groups)),
		zap.Int("model2_groups", len(model2Groups)))

	return model1Groups, model2Groups
}

// groupTicketsByScoreForModel groups tickets by score+model for a specific model type
func groupTicketsByScoreForModel(ctx workflow.Context, ticketsWithScores []*workflowPb.TicketWithScore, modelType ModelType) []ScoreGroup {
	logger := workflow.GetLogger(ctx)
	logger.Debug("Grouping tickets by score for specific model", zap.Int("model_type", int(modelType)))

	// Use map with composite key to directly build groups
	type groupKey struct {
		score     float32
		modelName string
	}
	scoreGroupsMap := make(map[groupKey][]string)

	// Group tickets by score and model for the specified model type
	for _, ticket := range ticketsWithScores {
		if ticket == nil {
			continue
		}

		var score float32
		var modelName string

		// Get score and model name based on model type
		switch modelType {
		case Model1:
			score = float32(int64(ticket.GetModel1Score()*100)) / 100
			modelName = ticket.GetModel1Name()
		case Model2:
			score = float32(int64(ticket.GetModel2Score()*100)) / 100
			modelName = ticket.GetModel2Name()
		default:
			logger.Warn("Unknown model type", zap.Int("model_type", int(modelType)))
			continue
		}

		if modelName == "" {
			logger.Debug("Adding default model name to tickets",
				zap.String("ticket_id", ticket.GetTicketId()),
				zap.Float32("score", score),
				zap.String("model_name", modelName),
				zap.Int("model_type", int(modelType)))
			modelName = fmt.Sprintf("default_model_name_%d", modelType)
		}

		// Use struct as map key - no string manipulation needed
		key := groupKey{score: score, modelName: modelName}
		scoreGroupsMap[key] = append(scoreGroupsMap[key], ticket.GetTicketId())
	}

	// Convert map directly to ScoreGroup structs
	scoreGroups := make([]ScoreGroup, 0, len(scoreGroupsMap))
	for key, ticketIds := range scoreGroupsMap {
		scoreGroups = append(scoreGroups, ScoreGroup{
			Score:     key.score,
			ModelName: key.modelName,
			ModelType: modelType,
			TicketIds: ticketIds,
		})
	}

	logger.Debug("Successfully grouped tickets for model",
		zap.Int("model_type", int(modelType)),
		zap.Int("groups", len(scoreGroups)))

	return scoreGroups
}

// processBulkUpdate executes BulkUpdateWorkflow for a specific score group
func processBulkUpdate(ctx workflow.Context, workflowID string, ticketIDs []string, modelType ModelType, modelName string, score float32) ([]string, error) {
	logger := workflow.GetLogger(ctx)
	logger.Info("Processing bulk update for score group",
		zap.Int("case_count", len(ticketIDs)),
		zap.Int("model_type", int(modelType)))

	childWorkflowOptions := workflow.ChildWorkflowOptions{
		WorkflowID: workflowID,
	}

	ctx = workflow.WithChildOptions(ctx, childWorkflowOptions)

	// Create the bulk update request based on model type
	bulkUpdateReq := &workflowPb.BulkUpdateWorkflowRequest{
		GroupKey:  BulkUpdateLockKey,
		TicketIds: ticketIDs,
	}

	// Set the appropriate model fields based on ModelType
	switch modelType {
	case Model1:
		bulkUpdateReq.Model1Name = modelName
		bulkUpdateReq.Model1Score = score
		bulkUpdateReq.ConfidenceScore = score
		bulkUpdateReq.UpdateFieldMask = append(bulkUpdateReq.UpdateFieldMask, riskEnums.TicketBulkUpdateFieldMask_MODEL_1_SCORE, riskEnums.TicketBulkUpdateFieldMask_MODEL_1_NAME, riskEnums.TicketBulkUpdateFieldMask_CONFIDENCE_SCORE)
		logger.Debug("Setting Model1 fields for bulk update",
			zap.String("model1_name", modelName),
			zap.Float32("model1_score", score))
	case Model2:
		bulkUpdateReq.Model2Name = modelName
		bulkUpdateReq.Model2Score = score
		bulkUpdateReq.UpdateFieldMask = append(bulkUpdateReq.UpdateFieldMask, riskEnums.TicketBulkUpdateFieldMask_MODEL_2_SCORE, riskEnums.TicketBulkUpdateFieldMask_MODEL_2_NAME)
		logger.Debug("Setting Model2 fields for bulk update",
			zap.String("model2_name", modelName),
			zap.Float32("model2_score", score))
	default:
		return ticketIDs, fmt.Errorf("unknown model type for %d", modelType)
	}

	var bulkUpdateResponse workflowPb.BulkUpdateWorkflowResponse
	err := workflowPkg.ExecuteChildWorkflow(ctx, workflowID, riskNs.BulkUpdateWorkflow, bulkUpdateReq, &bulkUpdateResponse)

	if err != nil {
		logger.Error("BulkUpdateWorkflow failed",
			zap.Int("model_type", int(modelType)),
			zap.Error(err))
		return ticketIDs, fmt.Errorf("BulkUpdateWorkflow failed for group %w", err)
	}

	logger.Info("BulkUpdateWorkflow completed successfully",
		zap.Int("model_type", int(modelType)),
		zap.String("job_id", bulkUpdateResponse.GetJobId()),
		zap.Int("successful_tickets", len(bulkUpdateResponse.GetSuccessfulTicketIds())),
		zap.Int("errored_tickets", len(bulkUpdateResponse.GetErroredTicketIds())))

	return bulkUpdateResponse.GetErroredTicketIds(), nil
}

// processBulkUpdateInBatches processes a score group by breaking it into batches and executing bulk updates
func processBulkUpdateInBatches(ctx workflow.Context, group ScoreGroup) []string {
	logger := workflow.GetLogger(ctx)
	var allErroredTickets []string

	// Process tickets in batches
	for i := 0; i < len(group.TicketIds); i += int(MaxBulkUpdateBatchSize) {
		end := i + int(MaxBulkUpdateBatchSize)
		if end > len(group.TicketIds) {
			end = len(group.TicketIds)
		}
		batchTicketIds := group.TicketIds[i:end]
		batchIndex := i / int(MaxBulkUpdateBatchSize)

		// Generate workflow ID with proper nomenclature: WF-{workflow-type}-{model}-{score}-{batch}-{timestamp}
		workflowID := fmt.Sprintf("WFREPRIO-M%d-S%f-B%d-%d",
			group.ModelType, group.Score, batchIndex, workflow.Now(ctx).UnixNano())

		logger.Debug("Processing bulk update batch",
			zap.String("workflow_id", workflowID),
			zap.Int("model_type", int(group.ModelType)),
			zap.Float32("score", group.Score),
			zap.Int("batch_index", batchIndex),
			zap.Int("batch_size", len(batchTicketIds)))

		erroredTicketsInBatch, err := processBulkUpdate(ctx, workflowID, batchTicketIds, group.ModelType, group.ModelName, group.Score)
		if err != nil {
			logger.Error("Failed to process bulk update batch",
				zap.Error(err),
				zap.String("workflow_id", workflowID),
				zap.Int("model_type", int(group.ModelType)),
				zap.Float32("score", group.Score),
				zap.Int("batch_index", batchIndex))
			allErroredTickets = append(allErroredTickets, batchTicketIds...)
			continue
		}
		allErroredTickets = append(allErroredTickets, erroredTicketsInBatch...)
	}

	return allErroredTickets
}
