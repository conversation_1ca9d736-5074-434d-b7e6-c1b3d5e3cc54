//go:build wireinject
// +build wireinject

package wire

import (
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/epifi/be-common/pkg/storage/v2/analytics"
	"github.com/google/wire"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"

	derivedattributes2 "github.com/epifi/gamma/api/creditreportv2/derivedattributes"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/user/obfuscator"
	vgScienapticPb "github.com/epifi/gamma/api/vendorgateway/scienaptic"
	"github.com/epifi/gamma/risk/lien"

	"github.com/epifi/gamma/risk/tagger"
	"github.com/epifi/gamma/risk/tagger/txn_taggers"

	depositPb "github.com/epifi/gamma/api/deposit"
	salaryProgramPb "github.com/epifi/gamma/api/salaryprogram"
	escalationHandling "github.com/epifi/gamma/risk/case_management/escalation/handling"
	escalationChecks "github.com/epifi/gamma/risk/case_management/escalation/handling/checks"

	"github.com/epifi/gamma/risk/whitelist"

	"github.com/epifi/be-common/pkg/queue"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	payPb "github.com/epifi/gamma/api/pay"
	actionExclusion "github.com/epifi/gamma/risk/case_management/action/exclusion"
	"github.com/epifi/gamma/risk/case_management/form"
	"github.com/epifi/gamma/risk/case_management/form/questionnaire"
	questionnaireValidator "github.com/epifi/gamma/risk/case_management/form/questionnaire/validator"

	actionEvaluators "github.com/epifi/gamma/risk/case_management/action/processor/evaluators"

	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/counter/tsaggr"
	datetimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/lock"

	celestialPb "github.com/epifi/be-common/api/celestial"

	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	operStatusPb "github.com/epifi/gamma/api/accounts/operstatus"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth"
	livPb "github.com/epifi/gamma/api/auth/liveness"
	"github.com/epifi/gamma/api/auth/location"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/categorizer"
	commsPb "github.com/epifi/gamma/api/comms"
	creditReportV2Pb "github.com/epifi/gamma/api/creditreportv2"
	cxTicketPb "github.com/epifi/gamma/api/cx/ticket"
	"github.com/epifi/gamma/api/employment"
	inAppReferralPb "github.com/epifi/gamma/api/inappreferral"
	kycPb "github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/merchant"
	"github.com/epifi/gamma/api/order"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	preApprovedLoanPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/product"
	riskPb "github.com/epifi/gamma/api/risk"
	caseManagementPb "github.com/epifi/gamma/api/risk/case_management"
	profilePb "github.com/epifi/gamma/api/risk/profile"
	redlistPb "github.com/epifi/gamma/api/risk/redlist"
	savingsPb "github.com/epifi/gamma/api/savings"
	screenerPb "github.com/epifi/gamma/api/screener"
	"github.com/epifi/gamma/api/segment"
	tieringPb "github.com/epifi/gamma/api/tiering"
	upiPb "github.com/epifi/gamma/api/upi"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/contact"
	userLoc "github.com/epifi/gamma/api/user/location"
	beOnbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/userintel"
	vgCrmPb "github.com/epifi/gamma/api/vendorgateway/crm"
	freshdeskPb "github.com/epifi/gamma/api/vendorgateway/cx/freshdesk"
	fennelPb "github.com/epifi/gamma/api/vendorgateway/fennel"
	"github.com/epifi/gamma/api/vendorgateway/namecheck"
	vglienPb "github.com/epifi/gamma/api/vendorgateway/openbanking/lien"
	vgRiskPb "github.com/epifi/gamma/api/vendorgateway/risk"
	vgTxnMonitoringPb "github.com/epifi/gamma/api/vendorgateway/transactionmonitoring/dronapay"
	vmPb "github.com/epifi/gamma/api/vendormapping"
	"github.com/epifi/gamma/featurestore"
	featureStoreWire "github.com/epifi/gamma/featurestore/wire"
	pqPkg "github.com/epifi/gamma/pkg/persistentqueue"
	pkgUser "github.com/epifi/gamma/pkg/user"
	"github.com/epifi/gamma/risk"
	"github.com/epifi/gamma/risk/accountstatus"
	riskActivity "github.com/epifi/gamma/risk/activity"
	bankActionComms "github.com/epifi/gamma/risk/bankactions/comms"
	"github.com/epifi/gamma/risk/case_management"
	actionProcessor "github.com/epifi/gamma/risk/case_management/action/processor"
	cmActivity "github.com/epifi/gamma/risk/case_management/activity"
	"github.com/epifi/gamma/risk/case_management/alert"
	alertHandling "github.com/epifi/gamma/risk/case_management/alert/handling"
	"github.com/epifi/gamma/risk/case_management/analyststore"
	"github.com/epifi/gamma/risk/case_management/auto_action"
	assignment "github.com/epifi/gamma/risk/case_management/case_assignment"
	priorityCaseStore "github.com/epifi/gamma/risk/case_management/case_assignment/priority_case_store"
	"github.com/epifi/gamma/risk/case_management/case_operation"
	"github.com/epifi/gamma/risk/case_management/caseanalyser"
	"github.com/epifi/gamma/risk/case_management/casestore"
	"github.com/epifi/gamma/risk/case_management/consumer"
	alertConsumer "github.com/epifi/gamma/risk/case_management/consumer/alert"
	alertProcessor "github.com/epifi/gamma/risk/case_management/consumer/alert/processor"
	batchRuleEngine "github.com/epifi/gamma/risk/case_management/consumer/batchruleengine"
	"github.com/epifi/gamma/risk/case_management/consumer/processors"
	caseManagementDao "github.com/epifi/gamma/risk/case_management/dao"
	cmDao "github.com/epifi/gamma/risk/case_management/dao"
	"github.com/epifi/gamma/risk/case_management/essential"
	"github.com/epifi/gamma/risk/case_management/essential/aggregator"
	"github.com/epifi/gamma/risk/case_management/essential/alert_with_rule"
	cxEssesntial "github.com/epifi/gamma/risk/case_management/essential/cx"
	essentialSegment "github.com/epifi/gamma/risk/case_management/essential/segment"
	"github.com/epifi/gamma/risk/case_management/exclusion"
	exclusionProcessors "github.com/epifi/gamma/risk/case_management/exclusion/processor"
	fiUserRelationship "github.com/epifi/gamma/risk/case_management/fi_user_relationship"
	"github.com/epifi/gamma/risk/case_management/helper"
	outcallComms "github.com/epifi/gamma/risk/case_management/outcall/comms"
	"github.com/epifi/gamma/risk/case_management/prioritisation"
	"github.com/epifi/gamma/risk/case_management/prioritisation/fetcher"
	"github.com/epifi/gamma/risk/case_management/prioritisation/integrator"
	"github.com/epifi/gamma/risk/case_management/prioritisation/priotizer"
	"github.com/epifi/gamma/risk/case_management/prioritisation/provider"
	"github.com/epifi/gamma/risk/config"
	"github.com/epifi/gamma/risk/config/common"
	"github.com/epifi/gamma/risk/config/genconf"
	riskWorkerConfig "github.com/epifi/gamma/risk/config/worker"
	"github.com/epifi/gamma/risk/dao"
	"github.com/epifi/gamma/risk/developer"
	"github.com/epifi/gamma/risk/developer/processor"
	"github.com/epifi/gamma/risk/dynamicelements"
	riskEssential "github.com/epifi/gamma/risk/essential"
	essentialAuth "github.com/epifi/gamma/risk/essential/auth"
	federalCustomer "github.com/epifi/gamma/risk/essential/federal_customer"
	"github.com/epifi/gamma/risk/lea"
	leaComms "github.com/epifi/gamma/risk/lea/comms"
	leaComplaint "github.com/epifi/gamma/risk/lea/complaint"
	"github.com/epifi/gamma/risk/lea/narration"
	leaRemark "github.com/epifi/gamma/risk/lea/remark"
	unifiedLEA "github.com/epifi/gamma/risk/lea/unified_lea"
	unifiedLeaComms "github.com/epifi/gamma/risk/lea/unified_lea/comms"
	mnrlConsumer "github.com/epifi/gamma/risk/mnrl/consumer"
	"github.com/epifi/gamma/risk/profile"
	"github.com/epifi/gamma/risk/profile/connections"
	profileConsumer "github.com/epifi/gamma/risk/profile/consumer"
	"github.com/epifi/gamma/risk/redlist"
	"github.com/epifi/gamma/risk/riskparam"
	riskParamFactory "github.com/epifi/gamma/risk/riskparam/factory"
	"github.com/epifi/gamma/risk/riskparam/fetchers"
	riskParamProcessors "github.com/epifi/gamma/risk/riskparam/processors"
	"github.com/epifi/gamma/risk/screener"
	"github.com/epifi/gamma/risk/screener/alertgen"
	"github.com/epifi/gamma/risk/screener/attemptmanager"
	"github.com/epifi/gamma/risk/screener/attemptvalidator"
	"github.com/epifi/gamma/risk/screener/checkmanager"
	"github.com/epifi/gamma/risk/screener/evaluator"
	"github.com/epifi/gamma/risk/screener/executor"
	"github.com/epifi/gamma/risk/txn_monitoring"
	txnMonitoring "github.com/epifi/gamma/risk/txn_monitoring"
	callbackConsumer "github.com/epifi/gamma/risk/txn_monitoring/callback"
	dronapayCallback "github.com/epifi/gamma/risk/txn_monitoring/callback/dronapay"
	txnConsumer "github.com/epifi/gamma/risk/txn_monitoring/consumer"
	"github.com/epifi/gamma/risk/txnhelper"
	wireTypes "github.com/epifi/gamma/risk/wire/types"
)

func GormProvider(db types.EpifiCRDB) *gorm.DB {
	return db
}

func InitialiseRiskService(db types.EpifiCRDB, frmDb types.FRMCRDB, frmPgdb types.FRMPGDB, userClient user.UsersClient, actorClient actor.ActorClient, locClient userLoc.LocationClient,
	locationClient location.LocationClient, authClient auth.AuthClient, riskVgClient vgRiskPb.RiskClient,
	inAppReferralClient inAppReferralPb.InAppReferralClient, conf *config.Config, livClient livPb.LivenessClient,
	kycClient kycPb.KycClient, client savingsPb.SavingsClient, celestialClient celestialPb.CelestialClient, redListClient redlistPb.RedListClient,
	nameCheckClient namecheck.UNNameCheckClient, empClient employment.EmploymentClient, uiClient userintel.UserIntelServiceClient,
	genconf *genconf.Config, crClient creditReportV2Pb.CreditReportManagerClient, commsClient commsPb.CommsClient,
	derivedAttrClient derivedattributes2.DerivedAttributesManagerClient, bcClient bankcust.BankCustomerServiceClient, onbClient beOnbPb.OnboardingClient,
	leaActorsPublisher wireTypes.LeaActorsPublisher, upiClient upiPb.UPIClient, oprStatusServiceClient operStatusPb.OperationalStatusServiceClient,
	screenerClient screenerPb.ScreenerClient, tieringClient tieringPb.TieringClient, caseManagementClient caseManagementPb.CaseManagementClient,
	userContactClient contact.ContactClient, redisClient types.UserRedisStore, preApprovedLoanClient preApprovedLoanPb.PreApprovedLoanClient, obfuscatorClient obfuscator.ObfuscatorClient) *risk.Service {
	wire.Build(
		idgen.NewClock,
		idgen.WireSet,
		dao.WireSet,
		cmDao.WireSet,
		datetimePkg.WireDefaultTimeSet,
		risk.NewService,
		riskParamProcessors.NewOnbRiskModelProc,
		riskParamProcessors.NewRedListFiniteCodeProc,
		riskParamProcessors.NewOnboardingEKYCNumberMismatch,
		riskParamProcessors.NewRedListGeoPinCodeProc,
		riskParamProcessors.NewRedListGeoLatLongProc,
		riskParamProcessors.NewRedListRiskyDeviceProc,
		riskParamProcessors.NewRedListKYCPinCodeProc,
		riskParamProcessors.NewOnbVelocityProc,
		riskParamProcessors.NewEPFOScreenerRiskProc,
		riskParamProcessors.NewRedListEmailIdProc,
		riskParamProcessors.NewRedListPhoneNumberProc,
		riskParamProcessors.NewHunterRiskProc,
		riskParamProcessors.NewIncomeEmploymentDiscrepancy,
		riskParamProcessors.NewAFURiskModelProc,
		riskParamProcessors.NewAffluenceClassProc,
		riskParamProcessors.NewUpiVpaNameMatchProc,
		riskParamProcessors.NewJunkEmailProc,
		riskParamProcessors.NewLocationRiskModelProc,
		riskParamProcessors.NewInstalledAppsCheckProcessor,
		riskParamProcessors.NewCrossVideoLivenessFacematchProcessor,
		riskParamProcessors.NewLeaContactAssociation,
		riskParamProcessors.NewPLFiLiteOnbRiskModelProc,
		riskParamProcessors.NewCreditScoreAffluenceClassProc,
		riskParamProcessors.NewSAUnreviewedAlertsCheckProcessor,
		riskParamProcessors.NewLowContactCountProc,
		riskParamProcessors.NewVPNPresenceProc,
		riskParamProcessors.NewIpAddress,
		riskParamProcessors.NewOnbRiskModelProcV1,
		riskParamProcessors.NewContactAssociationAndRiskyProfileProc,
		aggregator.WireSet,
		wire.NewSet(essential.NewRuleManagerImpl, wire.Bind(new(essential.RuleManager), new(*essential.RuleManagerImpl))),
		riskparam.EmailHelperWireSet,
		wire.NewSet(riskParamFactory.NewRiskParamProcessorFactory, wire.Bind(new(riskParamFactory.IRiskParamProcessorFactory), new(*riskParamFactory.RiskParamProcessorFactory))),
		wire.NewSet(screener.NewUserRiskScreener, wire.Bind(new(screener.IUserRiskScreener), new(*screener.UserRiskScreener))),
		executor.Wireset,
		wire.NewSet(riskEssential.NewUserDevicePropertyManager, wire.Bind(new(riskEssential.UserDevicePropertyManager),
			new(*riskEssential.UserDevicePropertyManagerImpl))),
		wire.NewSet(essentialAuth.NewFacematchManager, wire.Bind(new(essentialAuth.FacematchManager),
			new(*essentialAuth.FacematchManagerImpl))),
		wire.NewSet(essentialAuth.NewLivenessManager, wire.Bind(new(essentialAuth.LivenessManager),
			new(*essentialAuth.LivenessManagerImpl))),
		wire.NewSet(riskEssential.NewContactAssociations, wire.Bind(new(riskEssential.IContactAssociations),
			new(*riskEssential.ContactAssociations))),
		narration.NarrationProcessorWireSet,
		lea.ComplaintManagerWireSet,
		unifiedLeaComms.CollateBannersWireSet,
		wire.NewSet(alert.NewAlertManagerImpl, wire.Bind(new(alert.AlertManager), new(*alert.AlertManagerImpl))),
		dynamicelements.NewProviderFactory,
		dynamicelements.NewFreezeBannerProvider,
		dynamicelements.NewLeaBannerProvider,
		dynamicelements.NewOutcallBannerProvider,
		wire.NewSet(accountstatus.NewOperationalStatusFetcher, wire.Bind(new(accountstatus.Fetcher), new(*accountstatus.OperationalStatusFetcher))),
		wire.NewSet(riskEssential.NewUserManagerImpl, wire.Bind(new(riskEssential.UserManager), new(*riskEssential.UserManagerImpl))),
		wire.NewSet(riskEssential.NewActorManagerImpl, wire.Bind(new(riskEssential.ActorManager), new(*riskEssential.ActorManagerImpl))),
		screener.ActorScreenerWireSet,
		alertgen.WireSet,
		attemptmanager.WireSet,
		attemptvalidator.WireSet,
		checkmanager.WireSet,
		evaluator.WireSet,
		alertgen.BuilderWireSet,
		leaComms.SenderWireSet,
		leaComplaint.SourceProcessorWireSet,
		wire.NewSet(cache.NewRedisCacheStorage, wire.Bind(new(cache.CacheStorage),
			new(*cache.RedisCacheStorage))),
		riskParamFactory.WireSet,
		fetchers.NewVpaNameListFetcher,
		tsaggr.TimeSeriesAggregatorWireSet,
		types.UserRedisStoreRedisClientProvider,
		FrmPgdbTxnExecutorProvider,
		tagger.WireSet,
		txn_taggers.NewFamilyTransferTagger,
		txn_taggers.NewTxnTaggerFactory,
		unifiedLEA.UnifiedLEAComplaintManagerWireSet,
		WebFormConfigProvider,
	)
	return &risk.Service{}
}

func InitializeLeaService(frmPgdb types.FRMPGDB, userClient user.UsersClient, client savingsPb.SavingsClient, bcClient bankcust.BankCustomerServiceClient,
	depositClient depositPb.DepositClient, commsClient commsPb.CommsClient, actorClient actor.ActorClient, genConf *genconf.Config, serviceClient operStatusPb.OperationalStatusServiceClient, config2 *config.Config, celestialClient celestialPb.CelestialClient) *lea.Service {
	wire.Build(
		dao.WireSet,
		lea.NewService,
		unifiedLeaComms.CollateCommsWireSet,
		unifiedLeaComms.RefineWireSet,
		wire.NewSet(accountstatus.NewOperationalStatusFetcher, wire.Bind(new(accountstatus.Fetcher), new(*accountstatus.OperationalStatusFetcher))),
		UnifiedLeaComplaintConfigProvider,
		riskEssential.ActorManagerWireSet,
	)
	return &lea.Service{}
}

func InitialiseRedListService(db types.EpifiCRDB, frmPGDB types.FRMPGDB, payclient payPb.PayClient, riskClient riskPb.RiskClient,
	savingsClient savingsPb.SavingsClient, txnMonitoringClient vgTxnMonitoringPb.TransactionRiskDronaPayClient,
	txnRiskScoreClient riskPb.TxnRiskScoreServiceClient, conf *config.Config, genConf *genconf.Config) *redlist.Service {
	wire.Build(
		dao.WireSet,
		redlist.NewRedListService,
		GormProvider,
		wire.NewSet(redlist.NewRedListProcessorFactoryImpl, wire.Bind(new(redlist.IRedListProcessorFactory),
			new(*redlist.RedListProcessorFactoryImpl))),
		redlist.RedListWireSet,
		txn_monitoring.NewDataPusher,
		FeatureFlagsProvider,
	)
	return &redlist.Service{}
}

func InitialiseWhiteListService(db types.FRMPGDB) *whitelist.Service {
	wire.Build(
		dao.WireSet,
		whitelist.NewWhiteListService,
	)
	return &whitelist.Service{}
}

func FeatureFlagsProvider(genConf *genconf.Config) *genconf.FeatureFlags {
	return genConf.FeatureFlags()
}

func InitialiseRiskConsumer(db types.EpifiCRDB, frmPGDB types.FRMPGDB, awsConf aws.Config, payclient payPb.PayClient, riskClient riskPb.RiskClient,
	savingsClient savingsPb.SavingsClient, txnMonitoringClient vgTxnMonitoringPb.TransactionRiskDronaPayClient,
	txnRiskScoreClient riskPb.TxnRiskScoreServiceClient, conf *config.Config, genConf *genconf.Config,
) *risk.Consumer {
	wire.Build(
		dao.WireSet,
		risk.NewConsumer,
		FeatureFlagsProvider,
		txn_monitoring.NewDataPusher,
		redlist.NewRedListConsumer,
		redlist.RedListWireSet,
		GormProvider,
	)

	return &risk.Consumer{}
}

func InitializeActivityProcessor(
	db types.EpifiCRDB,
	frmPGDB types.FRMPGDB,
	frmCRDB types.FRMCRDB,
	savingsClient savingsPb.SavingsClient,
	userClient user.UsersClient,
	actorClient actor.ActorClient,
	authClient auth.AuthClient,
	operationalStatusClient operStatusPb.OperationalStatusServiceClient,
	conf *riskWorkerConfig.Config,
	accountStatusApiFlag accountstatus.UseNewOperationalStatusAPIFlag,
	balanceClient accountBalancePb.BalanceClient,
	riskClient riskPb.RiskClient,
	redisClient *redis.Client,
	caseManagementClient caseManagementPb.CaseManagementClient,
	client vglienPb.LienClient,
	freshdeskClient freshdeskPb.FreshdeskClient,
	crmClient vgCrmPb.CRMClient,
	s3Client wireTypes.CaseReprioritizationS3Client,
	lockRedisStore wireTypes.LockRedisStore,
	vmClient vmPb.VendorMappingServiceClient,
	redListClient redlistPb.RedListClient,
	vgRiskClient vgRiskPb.RiskClient,
	fennelClient fennelPb.FennelFeatureStoreClient,
	scienapticClient vgScienapticPb.ScienapticClient,
	caseStoreConfig *common.CaseStoreConfig,
) *riskActivity.Processor {
	wire.Build(
		riskActivity.NewProcessor,
		bankActionComms.BuilderFactoryWireSet,
		bankActionComms.NewFreezeActionCommsBuilder,
		bankActionComms.NewUnfreezeActionCommsBuilder,
		bankActionComms.NewLEAActionCommsBuilder,
		dao.WireSet,
		caseManagementDao.WireSet,
		riskEssential.ActorManagerWireSet,
		accountstatus.ProvideFetcherImplementation,
		leaRemark.InfoBuilderWireSet,
		lea.ComplaintManagerWireSet,
		bankActionCommsWorkerConfig,
		WebFormWorkerConfigProvider,
		wire.NewSet(cache.NewRedisCacheStorage, wire.Bind(new(cache.CacheStorage),
			new(*cache.RedisCacheStorage))),
		unifiedLeaComms.RefineWireSet,
		UnifiedLeaComplaintWorkerConfigProvider,
		unifiedLeaComms.CollateCommsWireSet,
		lien.LienManagerWireSet,
		lock.RedisStatelessLockManagerWireSet,
		//wireTypes.LockRedisStoreClientProvider,
		wire.NewSet(priotizer.NewPrecisionBasedPrioritization, wire.Bind(new(priotizer.IPrecisionBasedPrioritization),
			new(*priotizer.PrecisionBasedPrioritization))),
		wire.NewSet(priotizer.NewDSModelBasedPrioritization, wire.Bind(new(priotizer.IDSModelBasedPrioritization),
			new(*priotizer.DSModelBasedPrioritization))),
		wire.NewSet(aggregator.NewAggregatorImpl, wire.Bind(new(aggregator.Aggregator),
			new(*aggregator.AggregatorImpl))),
		wire.NewSet(prioritisation.NewFactoryImpl, wire.Bind(new(prioritisation.Factory),
			new(*prioritisation.FactoryImpl))),
		wire.NewSet(alert_with_rule.NewAlertWithRuleManagerImpl, wire.Bind(new(alert_with_rule.AlertWithRuleManager),
			new(*alert_with_rule.AlertWithRuleManagerImpl))),
		casestore.CaseStoreWireSet,
		wire.NewSet(integrator.NewParameterIntegratorImpl, wire.Bind(new(integrator.ParameterIntegrator),
			new(*integrator.ParameterIntegratorImpl))),
		wire.NewSet(fetcher.NewPrecisionFetcherImpl, wire.Bind(new(fetcher.PrecisionFetcher),
			new(*fetcher.PrecisionFetcherImpl))),
		wire.NewSet(fetcher.NewOccurrenceFetcherImpl, wire.Bind(new(fetcher.OccurrenceFetcher),
			new(*fetcher.OccurrenceFetcherImpl))),
		wire.NewSet(analyststore.NewExternalCRMAnalystStore, wire.Bind(new(analyststore.AnalystStore),
			new(*analyststore.ExternalCRMAnalystStore))),
		provider.NewRulePrecisionProvider,
		provider.NewModelPrecisionProvider,
		provider.NewWatchlistPrecisionProvider,
		provider.NewUserProfileModelPrecisionProvider,
		provider.NewRuleOccurrenceProvider,
		provider.NewDefaultOccurrenceProvider,
		wire.NewSet(provider.GetPrecisionProviders),
		wire.NewSet(provider.GetOccurrenceProviders),
		featureStoreWire.InitializeFeatureStore,
		IFeatureStoreProvider,
	)
	return &riskActivity.Processor{}
}

func InitializeDevService(db types.EpifiCRDB, frmDb types.FRMCRDB, frmPGDB types.FRMPGDB) *developer.RiskDevService {
	wire.Build(
		dao.WireSet,
		caseManagementDao.WireSet,
		developer.NewDevFactory,
		developer.NewRiskDevService,
		processor.NewRiskBankActionProcessor,
		processor.NewRiskDataProcessor,
		processor.NewRiskAlertProcessor,
		processor.NewReviewActionProcessor,
		processor.NewScreenerAttemptsProcessor,
		processor.NewFormProcessor,
		processor.NewUnifiedLeaComplaintProcessor,
		processor.NewLienRequestProcessor,
		questionnaireValidator.QuestionnaireValidatorWireSet,
		questionnaire.GeneratorWireSet,
	)
	return &developer.RiskDevService{}
}

func InitialiseTxnRiskScoreService(db types.EpifiCRDB, frmPGDB types.FRMPGDB, genConf *genconf.Config) *risk.TxnScoreService {
	wire.Build(
		dao.WireSet,
		FeatureFlagsProvider,
		risk.NewTxnRiskScoreService,
	)

	return &risk.TxnScoreService{}
}

func InitialiseTxnMonitoringService(
	db types.EpifiCRDB,
	frmDb types.FRMCRDB,
	frmPGDB types.FRMPGDB,
	locationClient location.LocationClient,
	config *config.Config,
	piClient piPb.PiClient,
	actorClient actor.ActorClient,
	onboardingClient beOnbPb.OnboardingClient,
	kycClient kycPb.KycClient,
	livenessClient livPb.LivenessClient,
	vgTxnMonitoringClient vgTxnMonitoringPb.TransactionRiskDronaPayClient,
	userClient user.UsersClient,
	txnRiskScoreClient riskPb.TxnRiskScoreServiceClient,
	empClient employment.EmploymentClient,
	refClient inAppReferralPb.InAppReferralClient,
	merchantClient merchant.MerchantServiceClient,
	genConfig *genconf.Config,
	userContactClient contact.ContactClient,
	savingClient savingsPb.SavingsClient,
	caseManagementClient caseManagementPb.CaseManagementClient,
	bankCustClient bankcust.BankCustomerServiceClient,
	txnCategorizerClient categorizer.TxnCategorizerClient,
	nameCheckClient namecheck.UNNameCheckClient,
) *txnConsumer.TxnMonitoringService {
	wire.Build(
		dao.WireSet,
		tagger.WireSet,
		txn_taggers.NewFamilyTransferTagger,
		txn_taggers.NewTxnTaggerFactory,
		FeatureFlagsProvider,
		txnMonitoring.NewCollector,
		txnMonitoring.NewDataPusher,
		txnMonitoring.NewDecider,
		wire.NewSet(txnMonitoring.NewResponseProcessorImpl, wire.Bind(new(txn_monitoring.ResponseProcessor),
			new(*txnMonitoring.ResponseProcessorImpl))),
		txnConsumer.NewTxnMonitoringService,
		wire.NewSet(federalCustomer.NewFederalCustomerManager, wire.Bind(new(federalCustomer.FederalCustomerManager),
			new(*federalCustomer.FederalCustomerManagerImpl))),
	)

	return &txnConsumer.TxnMonitoringService{}
}

func InitialiseTxnMonitoringCallbackService(
	caseManagementClient caseManagementPb.CaseManagementClient,
	genConf *genconf.Config,
) *callbackConsumer.TxnMonitoringCallbackService {
	wire.Build(
		callbackConsumer.BuilderWireSet,
		callbackConsumer.NewTxnMonitoringCallbackService,
	)
	return &callbackConsumer.TxnMonitoringCallbackService{}
}

// config: {"batchRuleEngineS3Client": "BatchRuleEngineS3().BucketName"}
func InitialiseCaseManagementConsumer(epifiCRDB types.EpifiCRDB, livenessClient livPb.LivenessClient, caseManagementClient caseManagementPb.CaseManagementClient,
	config *config.Config, genConf *genconf.Config, commsClient commsPb.CommsClient, actorClient actor.ActorClient,
	frmPGDB types.FRMPGDB, frmCRDB types.FRMCRDB, redisClient types.UserRedisStore, alertsPublisher wireTypes.AlertsPublisher,
	batchRuleEngineS3Client wireTypes.BatchRuleEngineS3Client, celestialClient celestialPb.CelestialClient, savingsClient savingsPb.SavingsClient,
	userClient user.UsersClient, cxTicketClient cxTicketPb.TicketClient, pClient paymentPb.PaymentClient) *consumer.CaseManagementConsumerService {
	wire.Build(
		caseManagementDao.WireSet,
		consumer.NewCaseManagementConsumerService,
		types.EpifiCRDBGormDBProvider,
		types.UserRedisStoreRedisClientProvider,
		wire.NewSet(consumer.NewProcessorFactory, wire.Bind(new(consumer.IProcessorFactory), new(*consumer.Factory))),
		wire.NewSet(alertConsumer.NewProcessorFactory, wire.Bind(new(alertConsumer.IProcessorFactory), new(*alertConsumer.Factory))),
		processors.NewLivenessProcessor,
		processors.NewUserReviewProcessor,
		processors.NewLivenessSampleReviewProcessor,
		processors.NewTransactionReviewBatchProcessor,
		processors.NewUserReviewBatchProcessor,
		processors.NewScreenerReviewBatchProcessor,
		alertProcessor.NewDSBatchProcessor,
		cxEssesntial.TicketManagerWireSet,
		wire.NewSet(aggregator.NewAggregatorImpl, wire.Bind(new(aggregator.Aggregator),
			new(*aggregator.AggregatorImpl))),
		wire.NewSet(case_management.NewRuleProcessorImpl, wire.Bind(new(case_management.RuleProcessor), new(*case_management.RuleProcessorImpl))),
		wire.NewSet(helper.NewPersistentQueueHelper, wire.Bind(new(helper.IPersistentQueueHelper), new(*helper.PersistentQueueHelper))),
		pqPkg.NewPersistentQueue,
		riskEssential.ActorManagerWireSet,
		leaRemark.InfoBuilderWireSet,
		lea.ComplaintManagerWireSet,
		wire.NewSet(cache.NewRedisCacheStorage, wire.Bind(new(cache.CacheStorage),
			new(*cache.RedisCacheStorage))),
		bankActionComms.NewFreezeActionCommsBuilder,
		bankActionComms.NewUnfreezeActionCommsBuilder,
		bankActionComms.NewLEAActionCommsBuilder,
		bankActionComms.BuilderFactoryWireSet,
		RiskBankActionConfigProvider,
		dao.WireSet,
		batchRuleEngine.ProcessorWireSet,
		form.HandlerWireSet,
		FrmPgdbTxnExecutorProvider,
		WebFormConfigProvider,
	)
	return &consumer.CaseManagementConsumerService{}
}

func InitialiseCaseManagementService(orderClient order.OrderServiceClient, piClient piPb.PiClient,
	merchantClient merchant.MerchantServiceClient, crmClient vgCrmPb.CRMClient,
	vmClient vmPb.VendorMappingServiceClient, db types.FRMCRDB, celestialClient celestialPb.CelestialClient, segmentServiceClient segment.SegmentationServiceClient,
	genConf *genconf.Config, redisClient types.UserRedisStore, userClient user.UsersClient, productClient product.ProductClient,
	formSubmissionEventPublisher wireTypes.FormSubmissionEventPublisher, frmPgdb types.FRMPGDB, salaryProgramClient salaryProgramPb.SalaryProgramClient,
	employmentClient employment.EmploymentClient, cxTicketClient cxTicketPb.TicketClient, bqDbResourceProvider analytics.BigqueryDBResourceProvider) *case_management.Service {
	wire.Build(
		caseManagementDao.WireSet,
		types.UserRedisStoreRedisClientProvider,
		wire.NewSet(txnhelper.New, wire.Bind(new(txnhelper.IRiskTransactionHelper), new(*txnhelper.RiskTransactionHelper))),
		wire.NewSet(aggregator.NewAggregatorImpl, wire.Bind(new(aggregator.Aggregator),
			new(*aggregator.AggregatorImpl))),
		wire.NewSet(case_management.NewRuleProcessorImpl, wire.Bind(new(case_management.RuleProcessor), new(*case_management.RuleProcessorImpl))),
		wire.NewSet(assignment.NewPriorityCaseAllocator, wire.Bind(new(assignment.CaseAllocator),
			new(*assignment.PriorityCaseAllocator))),
		wire.NewSet(priorityCaseStore.NewCasePriorityCacheStore, wire.Bind(new(priorityCaseStore.PriorityCaseStore),
			new(*priorityCaseStore.PriorityCaseCacheStore))),
		wire.NewSet(analyststore.NewExternalCRMAnalystStore, wire.Bind(new(analyststore.AnalystStore),
			new(*analyststore.ExternalCRMAnalystStore))),
		wire.NewSet(cache.NewRedisCacheStorage, wire.Bind(new(cache.CacheStorage),
			new(*cache.RedisCacheStorage))),
		wire.NewSet(essentialSegment.NewMembershipManagerImpl, wire.Bind(new(essentialSegment.MembershipManager),
			new(*essentialSegment.MembershipManagerImpl))),
		wire.NewSet(fiUserRelationship.NewFiUserRelationshipImpl,
			wire.Bind(new(fiUserRelationship.FiUserRelationship), new(*fiUserRelationship.FiUserRelationshipImpl))),
		wire.NewSet(riskEssential.NewUserGroupManagerImpl, wire.Bind(new(riskEssential.UserGroupManager), new(*riskEssential.UserGroupManagerImpl))),
		lock.DefaultLockMangerWireSet,
		case_management.NewService,
		casestore.CaseStoreWireSet,
		caseStoreConfigProvider,
		fIProductToSegmentMapConfigProvider,
		form.HandlerWireSet,
		FrmPgdbTxnExecutorProvider,
		pkgUser.WireSet,
		cxEssesntial.TicketManagerWireSet,
		cmDao.AnalyticsDaoWireSet,
	)
	return &case_management.Service{}
}

// As EscalationEventPublisher config is not there in config files, initialisation code is commented and nil is getting passed
// If we define type then it should be present in config, so adding WA because
// It requires full cleanup from case_management.NewService
func nilEscalationEventPublisherProvider() queue.Publisher {
	return nil
}

func InitializeCaseManagementActivityProcessor(
	frmDb types.FRMCRDB,
	epifiCrdb types.EpifiCRDB,
	frmPgdb types.FRMPGDB,
	conf *riskWorkerConfig.Config,
	redisRLClient wireTypes.RedisRLClient,
	redisClient wireTypes.RedisClient,
	crmClient vgCrmPb.CRMClient,
	vmClient vmPb.VendorMappingServiceClient,
	celestialClient celestialPb.CelestialClient,
	actorClient actor.ActorClient,
	savingsClient savingsPb.SavingsClient,
	onboardingClient beOnbPb.OnboardingClient,
	livClient livPb.LivenessClient,
	usersClient user.UsersClient,
	userContactClient contact.ContactClient,
	authClient auth.AuthClient,
	segmentServiceClient segment.SegmentationServiceClient,
	accountStatusApiFlag accountstatus.UseNewOperationalStatusAPIFlag,
	operationalStatusClient operStatusPb.OperationalStatusServiceClient,
	balanceClient accountBalancePb.BalanceClient,
	redListClient redlistPb.RedListClient,
	fennelClient fennelPb.FennelFeatureStoreClient,
	scienapticClient vgScienapticPb.ScienapticClient,
	commsClient commsPb.CommsClient,
	productClient product.ProductClient,
	salaryProgramClient salaryProgramPb.SalaryProgramClient,
	employmentClient employment.EmploymentClient,
	cxTicketClient cxTicketPb.TicketClient,
	vgRiskClient vgRiskPb.RiskClient,
	caseManagementClient caseManagementPb.CaseManagementClient,
) *cmActivity.Processor {
	wire.Build(
		dao.WireSet,
		cmDao.WireSet,
		RedisRLClientProvider,
		lock.DefaultLockMangerWireSet,
		wire.NewSet(casestore.NewCaseBuilder, wire.Bind(new(casestore.ICaseBuilder), new(*casestore.CaseBuilder))),
		casestore.CaseStoreWireSet,
		InitializeRedisCacheStorage,
		wire.NewSet(alert.NewAlertManagerImpl, wire.Bind(new(alert.AlertManager), new(*alert.AlertManagerImpl))),
		wire.NewSet(exclusion.NewExclusionProcessorFactory, wire.Bind(new(exclusion.IProcessorFactory), new(*exclusion.Factory))),
		wire.NewSet(case_management.NewRuleProcessorImpl, wire.Bind(new(case_management.RuleProcessor), new(*case_management.RuleProcessorImpl))),
		wire.NewSet(essential.NewActorManagerImpl, wire.Bind(new(essential.ActorManager),
			new(*essential.ActorManagerImpl))),
		wire.NewSet(essential.NewUserManagerImpl, wire.Bind(new(essential.UserManager), new(*essential.UserManagerImpl))),
		wire.NewSet(essential.NewRuleManagerImpl, wire.Bind(new(essential.RuleManager),
			new(*essential.RuleManagerImpl))),
		wire.NewSet(auto_action.NewQuestionBuilderImpl, wire.Bind(new(auto_action.QuestionBuilder),
			new(*auto_action.QuestionBuilderImpl))),
		wire.NewSet(essential.NewRiskyUserRequiredInfoManagerImpl,
			wire.Bind(new(essential.RiskyUserRequiredInfoManager),
				new(*essential.RiskyUserRequiredInfoManagerImpl))),
		wire.NewSet(auto_action.NewSenderEmailFromWorkerConfig, wire.Bind(new(auto_action.SenderEmailIdProvider),
			new(*auto_action.SenderEmailFromWorkerConfig))),
		wire.NewSet(exclusionProcessors.NewConfigProviderFromWorkerConfig, wire.Bind(new(exclusionProcessors.ConfigProvider),
			new(*exclusionProcessors.ConfigProviderFromWorkerConfig))),
		exclusionProcessors.NewAccountStatusReviewExclusionProcessor,
		exclusionProcessors.NewSegmentBasedExclusionProcessor,
		exclusionProcessors.NewLatestPassedCaseBasedExclusionProcessor,
		exclusionProcessors.NewActionInProgressExclusionProcessor,
		provider.NewRulePrecisionProvider,
		provider.NewModelPrecisionProvider,
		provider.NewWatchlistPrecisionProvider,
		provider.NewUserProfileModelPrecisionProvider,
		provider.NewRuleOccurrenceProvider,
		provider.NewDefaultOccurrenceProvider,
		wire.NewSet(provider.GetPrecisionProviders),
		wire.NewSet(provider.GetOccurrenceProviders),
		featureStoreWire.InitializeFeatureStore,
		IFeatureStoreProvider,
		escalationChecks.NewAccountStatusChecker,
		escalationChecks.NewExistingCaseChecker,
		escalationChecks.NewExistingFormChecker,
		escalationHandling.EscalationHandlingTypeWireSet,
		escalationHandling.EscalationChecksFactoryWireSet,
		wire.NewSet(alert_with_rule.NewAlertWithRuleManagerImpl, wire.Bind(new(alert_with_rule.AlertWithRuleManager),
			new(*alert_with_rule.AlertWithRuleManagerImpl))),
		wire.NewSet(fetcher.NewPrecisionFetcherImpl, wire.Bind(new(fetcher.PrecisionFetcher),
			new(*fetcher.PrecisionFetcherImpl))),
		wire.NewSet(fetcher.NewOccurrenceFetcherImpl, wire.Bind(new(fetcher.OccurrenceFetcher),
			new(*fetcher.OccurrenceFetcherImpl))),
		wire.NewSet(integrator.NewParameterIntegratorImpl, wire.Bind(new(integrator.ParameterIntegrator),
			new(*integrator.ParameterIntegratorImpl))),
		wire.NewSet(priotizer.NewPrecisionBasedPrioritization, wire.Bind(new(priotizer.IPrecisionBasedPrioritization),
			new(*priotizer.PrecisionBasedPrioritization))),
		wire.NewSet(priotizer.NewDSModelBasedPrioritization, wire.Bind(new(priotizer.IDSModelBasedPrioritization),
			new(*priotizer.DSModelBasedPrioritization))),
		wire.NewSet(prioritisation.NewFactoryImpl, wire.Bind(new(prioritisation.Factory),
			new(*prioritisation.FactoryImpl))),
		auto_action.NewFreezeActionProcessor,
		auto_action.NewOutCallActionProcessor,
		wire.NewSet(exclusion.NewConsolidatorImpl, wire.Bind(new(exclusion.Consolidator),
			new(*exclusion.ConsolidatorImpl))),
		wire.NewSet(case_operation.NewMoveToReviewManagerImpl, wire.Bind(new(case_operation.MoveToReviewManager),
			new(*case_operation.MoveToReviewManagerImpl))),
		wire.NewSet(auto_action.ProvideRiskActionProcessors),
		wire.NewSet(auto_action.NewAutoActionProcessorImpl, wire.Bind(new(auto_action.AutoActionProcessor),
			new(*auto_action.AutoActionProcessorImpl))),
		wire.NewSet(aggregator.NewAggregatorImpl, wire.Bind(new(aggregator.Aggregator), new(*aggregator.AggregatorImpl))),
		wire.NewSet(caseanalyser.NewCaseAnalyser, wire.Bind(new(caseanalyser.ICaseAnalyser), new(*caseanalyser.CaseAnalyser))),
		wire.NewSet(analyststore.NewExternalCRMAnalystStore, wire.Bind(new(analyststore.AnalystStore),
			new(*analyststore.ExternalCRMAnalystStore))),
		wire.NewSet(screener.NewVerdictProcessor, wire.Bind(new(screener.VerdictProcessor), new(*screener.VerdictProcessorImpl))),
		cmActivity.NewProcessor,
		caseStoreWorkerConfigProvider,
		PrioritiserConfigWorkerConfigProvider,
		accountstatus.ProvideFetcherImplementation,
		outcallComms.ReminderWireSet,
		OutcallConfigProvider,
		CaseManagementPlatformConfigProvider,
		InvestigationEmailConfigProvider,
		wire.NewSet(riskEssential.NewUserManagerImpl, wire.Bind(new(riskEssential.UserManager), new(*riskEssential.UserManagerImpl))),
		wire.NewSet(riskEssential.NewActorManagerImpl, wire.Bind(new(riskEssential.ActorManager), new(*riskEssential.ActorManagerImpl))),
		alertHandling.ConsolidatorWireSet,
		AlertHandlingConfigProvider,
		BankActionsWorkerConfigProvider,
		wire.NewSet(actionProcessor.NewEvaluatorImpl, wire.Bind(new(actionProcessor.Evaluator),
			new(*actionProcessor.EvaluatorImpl))),
		wire.NewSet(actionProcessor.NewActionEvaluatorFactory, wire.Bind(new(actionProcessor.IEvaluatorsFactory), new(*actionProcessor.EvaluatorFactory))),
		actionEvaluators.NewUserRelationshipImpl,
		actionEvaluators.NewRandomSampleImpl,
		actionEvaluators.NewWhitelistMemberEvaluator,
		wire.NewSet(essentialSegment.NewMembershipManagerImpl, wire.Bind(new(essentialSegment.MembershipManager),
			new(*essentialSegment.MembershipManagerImpl))),
		wire.NewSet(fiUserRelationship.NewFiUserRelationshipImpl,
			wire.Bind(new(fiUserRelationship.FiUserRelationship), new(*fiUserRelationship.FiUserRelationshipImpl))),
		wire.NewSet(riskEssential.NewUserGroupManagerImpl, wire.Bind(new(riskEssential.UserGroupManager), new(*riskEssential.UserGroupManagerImpl))),
		fIProductToSegmentMapWorkerConfigProvider,
		questionnaire.GeneratorWireSet,
		questionnaireValidator.QuestionnaireValidatorWireSet,
		FrmPgdbTxnExecutorProvider,
		actionExclusion.ConsolidatorWireSet,
		cxEssesntial.TicketManagerWireSet,
		pkgUser.WireSet,
		bankActionComms.NewFreezeActionCommsBuilder,
		bankActionComms.NewUnfreezeActionCommsBuilder,
		bankActionComms.NewLEAActionCommsBuilder,
		bankActionComms.BuilderFactoryWireSet,
		WebFormWorkerConfigProvider,
		RiskBankActionWorkerConfigProvider,
		leaRemark.InfoBuilderWireSet,
		lea.ComplaintManagerWireSet,
	)
	return &cmActivity.Processor{}
}

func caseStoreConfigProvider(genConf *genconf.Config) *common.CaseStoreConfig {
	return genConf.CaseStore()
}

func fIProductToSegmentMapWorkerConfigProvider(conf *riskWorkerConfig.Config) map[string]string {
	return conf.FIProductToSegmentMap
}

func fIProductToSegmentMapConfigProvider(genConf *genconf.Config) map[string]string {
	return genConf.FIProductToSegmentMap()
}

func caseStoreWorkerConfigProvider(conf *riskWorkerConfig.Config) *common.CaseStoreConfig {
	return conf.CaseStore
}
func PrioritiserConfigWorkerConfigProvider(conf *riskWorkerConfig.Config) *common.PrioritiserConfig {
	return conf.PrioritiserConfig
}

func bankActionCommsWorkerConfig(conf *riskWorkerConfig.Config) *common.RiskBankActionComms {
	return conf.RiskBankActionComms
}

func RedisClientProvider(client wireTypes.RedisClient) *redis.Client {
	return client
}

func RedisRLClientProvider(client wireTypes.RedisRLClient) *redis.Client {
	return client
}

func InitializeRedisCacheStorage(client wireTypes.RedisClient) cache.CacheStorage {
	wire.Build(
		RedisClientProvider,
		wire.NewSet(cache.NewRedisCacheStorage, wire.Bind(new(cache.CacheStorage),
			new(*cache.RedisCacheStorage))),
	)
	return &cache.RedisCacheStorage{}
}

func InitialiseProfileService(frmDb types.FRMCRDB,
	epifiCrdb types.EpifiCRDB,
	config *genconf.Config,
	savingsClient savingsPb.SavingsClient,
	staticConfig *config.Config,
	oprStatusServiceClient operStatusPb.OperationalStatusServiceClient,
	userContactClient contact.ContactClient,
	inAppReferralClient inAppReferralPb.InAppReferralClient,
	userClient user.UsersClient,
	actorClient actor.ActorClient,
	celestialClient celestialPb.CelestialClient,
	frmPGDB types.FRMPGDB, cxTicketClient cxTicketPb.TicketClient) *profile.Service {
	wire.Build(
		dao.WireSet,
		cmDao.WireSet,
		profile.NewService,
		wire.NewSet(riskEssential.NewUserManagerImpl, wire.Bind(new(riskEssential.UserManager), new(*riskEssential.UserManagerImpl))),
		wire.NewSet(riskEssential.NewActorManagerImpl, wire.Bind(new(riskEssential.ActorManager), new(*riskEssential.ActorManagerImpl))),
		wire.NewSet(accountstatus.NewOperationalStatusFetcher, wire.Bind(new(accountstatus.Fetcher), new(*accountstatus.OperationalStatusFetcher))),
		wire.NewSet(connections.NewReferralFetcherImpl, wire.Bind(new(connections.ReferralFetcher), new(*connections.ReferralFetcherImpl))),
		wire.NewSet(connections.NewContactsFetcherImpl, wire.Bind(new(connections.ContactFetcher), new(*connections.ContactsFetcherImpl))),
		wire.NewSet(connections.NewHelperImpl, wire.Bind(new(connections.Helper), new(*connections.HelperImpl))),
		lea.ComplaintManagerWireSet,
		form.HandlerWireSet,
		cxEssesntial.TicketManagerWireSet,
		UnifiedLeaComplaintConfigProvider,
		WebFormConfigProvider,
		FrmPgdbTxnExecutorProvider,
		unifiedLEA.UnifiedLEAComplaintManagerWireSet,
	)
	return &profile.Service{}
}

func InitialiseProfileConsumer(
	frmPGDB types.FRMPGDB,
	epifiCrdb types.EpifiCRDB,
	profileClient profilePb.ProfileClient,
	savingsClient savingsPb.SavingsClient,
	riskClient riskPb.RiskClient,
) *profileConsumer.ProfileConsumerService {
	wire.Build(
		dao.WireSet,
		profileConsumer.NewProfileService,
	)
	return &profileConsumer.ProfileConsumerService{}
}

func InitialiseMnrlConsumer(frmPGDB types.FRMPGDB, conf *config.Config) *mnrlConsumer.Consumer {
	wire.Build(
		dao.WireSet,
		mnrlConsumer.NewService,
		mnrlConsumer.NewConsumer,
	)
	return &mnrlConsumer.Consumer{}
}

func InitializeDronapayRuleHitCallbackService(DronapayRuleHitCallbackPublisher dronapayCallback.DronapayRuleHitCallbackPublisher) *dronapayCallback.Service {
	wire.Build(
		dronapayCallback.NewService,
	)
	return &dronapayCallback.Service{}
}

func IFeatureStoreProvider(store *featurestore.FeatureStore) featurestore.IFeatureStore {
	return store
}

func OutcallConfigProvider(conf *riskWorkerConfig.Config) *common.OutcallConfig {
	return conf.Outcall
}

func CaseManagementPlatformConfigProvider(conf *riskWorkerConfig.Config) *common.CaseManagementPlatformConfig {
	return conf.CaseManagementPlatform
}

func InvestigationEmailConfigProvider(conf *riskWorkerConfig.Config) *common.InvestigationEmailConfig {
	return conf.InvestigationEmail
}

func BankActionsConfigProvider(genConf *genconf.Config) *common.RiskBankAction {
	return genConf.RiskBankAction()
}

func RiskBankActionConfigProvider(conf *config.Config) *common.RiskBankActionComms {
	return conf.RiskBankActionComms
}

func RiskBankActionWorkerConfigProvider(conf *riskWorkerConfig.Config) *common.RiskBankActionComms {
	return conf.RiskBankActionComms
}

func AlertHandlingConfigProvider(conf *riskWorkerConfig.Config) *common.AlertHandlingConfig {
	return conf.AlertHandling
}

func BankActionsWorkerConfigProvider(conf *riskWorkerConfig.Config) *common.RiskBankAction {
	return conf.RiskBankAction
}

func WebFormWorkerConfigProvider(conf *riskWorkerConfig.Config) *common.WebFormConfig {
	return conf.WebFormConfig
}

func WebFormConfigProvider(conf *config.Config) *common.WebFormConfig {
	return conf.WebFormConfig
}

func FrmPgdbTxnExecutorProvider(db types.FRMPGDB) wireTypes.FrmPgdbTxnExecutor {
	return storageV2.NewGormTxnExecutor(db)
}

func UnifiedLeaComplaintWorkerConfigProvider(conf *riskWorkerConfig.Config) *common.UnifiedLeaComplaintConfig {
	return conf.UnifiedLEAComplaintConfig
}

func UnifiedLeaComplaintConfigProvider(conf *config.Config) *common.UnifiedLeaComplaintConfig {
	return conf.UnifiedLEAComplaintConfig
}
