// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/counter/tsaggr"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/lock"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/api/accounts/balance"
	"github.com/epifi/gamma/api/accounts/operstatus"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/auth/liveness"
	location2 "github.com/epifi/gamma/api/auth/location"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/categorizer"
	"github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/creditreportv2"
	"github.com/epifi/gamma/api/creditreportv2/derivedattributes"
	"github.com/epifi/gamma/api/cx/ticket"
	"github.com/epifi/gamma/api/deposit"
	"github.com/epifi/gamma/api/employment"
	"github.com/epifi/gamma/api/inappreferral"
	"github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/merchant"
	"github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/product"
	risk3 "github.com/epifi/gamma/api/risk"
	"github.com/epifi/gamma/api/risk/case_management"
	profile2 "github.com/epifi/gamma/api/risk/profile"
	"github.com/epifi/gamma/api/risk/redlist"
	"github.com/epifi/gamma/api/salaryprogram"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/screener"
	"github.com/epifi/gamma/api/segment"
	"github.com/epifi/gamma/api/tiering"
	"github.com/epifi/gamma/api/upi"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/contact"
	"github.com/epifi/gamma/api/user/location"
	"github.com/epifi/gamma/api/user/obfuscator"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/userintel"
	"github.com/epifi/gamma/api/vendorgateway/crm"
	"github.com/epifi/gamma/api/vendorgateway/cx/freshdesk"
	"github.com/epifi/gamma/api/vendorgateway/fennel"
	"github.com/epifi/gamma/api/vendorgateway/namecheck"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/lien"
	"github.com/epifi/gamma/api/vendorgateway/risk"
	"github.com/epifi/gamma/api/vendorgateway/scienaptic"
	"github.com/epifi/gamma/api/vendorgateway/transactionmonitoring/dronapay"
	"github.com/epifi/gamma/api/vendormapping"
	"github.com/epifi/gamma/featurestore"
	"github.com/epifi/gamma/featurestore/wire"
	"github.com/epifi/gamma/pkg/persistentqueue"
	user2 "github.com/epifi/gamma/pkg/user"
	risk2 "github.com/epifi/gamma/risk"
	"github.com/epifi/gamma/risk/accountstatus"
	"github.com/epifi/gamma/risk/activity"
	comms5 "github.com/epifi/gamma/risk/bankactions/comms"
	case_management2 "github.com/epifi/gamma/risk/case_management"
	"github.com/epifi/gamma/risk/case_management/action/exclusion"
	processors4 "github.com/epifi/gamma/risk/case_management/action/exclusion/processors"
	processor4 "github.com/epifi/gamma/risk/case_management/action/processor"
	"github.com/epifi/gamma/risk/case_management/action/processor/evaluators"
	activity2 "github.com/epifi/gamma/risk/case_management/activity"
	"github.com/epifi/gamma/risk/case_management/alert"
	"github.com/epifi/gamma/risk/case_management/alert/handling"
	"github.com/epifi/gamma/risk/case_management/alert/handling/checks"
	"github.com/epifi/gamma/risk/case_management/alert/handling/collector"
	"github.com/epifi/gamma/risk/case_management/analyststore"
	"github.com/epifi/gamma/risk/case_management/auto_action"
	"github.com/epifi/gamma/risk/case_management/case_assignment"
	"github.com/epifi/gamma/risk/case_management/case_assignment/priority_case_store"
	"github.com/epifi/gamma/risk/case_management/case_operation"
	"github.com/epifi/gamma/risk/case_management/caseanalyser"
	"github.com/epifi/gamma/risk/case_management/casestore"
	consumer2 "github.com/epifi/gamma/risk/case_management/consumer"
	alert2 "github.com/epifi/gamma/risk/case_management/consumer/alert"
	processor2 "github.com/epifi/gamma/risk/case_management/consumer/alert/processor"
	"github.com/epifi/gamma/risk/case_management/consumer/batchruleengine"
	processors3 "github.com/epifi/gamma/risk/case_management/consumer/processors"
	dao2 "github.com/epifi/gamma/risk/case_management/dao"
	handling2 "github.com/epifi/gamma/risk/case_management/escalation/handling"
	checks2 "github.com/epifi/gamma/risk/case_management/escalation/handling/checks"
	essential2 "github.com/epifi/gamma/risk/case_management/essential"
	"github.com/epifi/gamma/risk/case_management/essential/aggregator"
	"github.com/epifi/gamma/risk/case_management/essential/alert_with_rule"
	"github.com/epifi/gamma/risk/case_management/essential/cx"
	segment2 "github.com/epifi/gamma/risk/case_management/essential/segment"
	exclusion2 "github.com/epifi/gamma/risk/case_management/exclusion"
	processor3 "github.com/epifi/gamma/risk/case_management/exclusion/processor"
	"github.com/epifi/gamma/risk/case_management/fi_user_relationship"
	"github.com/epifi/gamma/risk/case_management/form"
	"github.com/epifi/gamma/risk/case_management/form/questionnaire"
	"github.com/epifi/gamma/risk/case_management/form/questionnaire/validator"
	"github.com/epifi/gamma/risk/case_management/form/submission"
	"github.com/epifi/gamma/risk/case_management/helper"
	comms6 "github.com/epifi/gamma/risk/case_management/outcall/comms"
	"github.com/epifi/gamma/risk/case_management/prioritisation"
	"github.com/epifi/gamma/risk/case_management/prioritisation/fetcher"
	"github.com/epifi/gamma/risk/case_management/prioritisation/integrator"
	"github.com/epifi/gamma/risk/case_management/prioritisation/priotizer"
	"github.com/epifi/gamma/risk/case_management/prioritisation/provider"
	"github.com/epifi/gamma/risk/config"
	"github.com/epifi/gamma/risk/config/common"
	"github.com/epifi/gamma/risk/config/genconf"
	"github.com/epifi/gamma/risk/config/worker"
	"github.com/epifi/gamma/risk/dao"
	"github.com/epifi/gamma/risk/developer"
	"github.com/epifi/gamma/risk/developer/processor"
	"github.com/epifi/gamma/risk/dynamicelements"
	"github.com/epifi/gamma/risk/essential"
	auth2 "github.com/epifi/gamma/risk/essential/auth"
	comms4 "github.com/epifi/gamma/risk/essential/comms"
	"github.com/epifi/gamma/risk/essential/federal_customer"
	"github.com/epifi/gamma/risk/lea"
	comms3 "github.com/epifi/gamma/risk/lea/comms"
	"github.com/epifi/gamma/risk/lea/complaint"
	"github.com/epifi/gamma/risk/lea/narration"
	"github.com/epifi/gamma/risk/lea/remark"
	processors2 "github.com/epifi/gamma/risk/lea/remark/processors"
	"github.com/epifi/gamma/risk/lea/unified_lea"
	comms2 "github.com/epifi/gamma/risk/lea/unified_lea/comms"
	lien2 "github.com/epifi/gamma/risk/lien"
	"github.com/epifi/gamma/risk/profile"
	"github.com/epifi/gamma/risk/profile/connections"
	consumer3 "github.com/epifi/gamma/risk/profile/consumer"
	redlist2 "github.com/epifi/gamma/risk/redlist"
	"github.com/epifi/gamma/risk/riskparam"
	"github.com/epifi/gamma/risk/riskparam/factory"
	"github.com/epifi/gamma/risk/riskparam/fetchers"
	"github.com/epifi/gamma/risk/riskparam/processors"
	screener2 "github.com/epifi/gamma/risk/screener"
	"github.com/epifi/gamma/risk/screener/alertgen"
	"github.com/epifi/gamma/risk/screener/attemptmanager"
	"github.com/epifi/gamma/risk/screener/attemptvalidator"
	"github.com/epifi/gamma/risk/screener/checkmanager"
	"github.com/epifi/gamma/risk/screener/evaluator"
	"github.com/epifi/gamma/risk/screener/executor"
	"github.com/epifi/gamma/risk/tagger"
	"github.com/epifi/gamma/risk/tagger/txn_taggers"
	"github.com/epifi/gamma/risk/txn_monitoring"
	"github.com/epifi/gamma/risk/txn_monitoring/callback"
	dronapay2 "github.com/epifi/gamma/risk/txn_monitoring/callback/dronapay"
	"github.com/epifi/gamma/risk/txn_monitoring/consumer"
	"github.com/epifi/gamma/risk/txnhelper"
	"github.com/epifi/gamma/risk/whitelist"
	types2 "github.com/epifi/gamma/risk/wire/types"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

// Injectors from wire.go:

func InitialiseRiskService(db types.EpifiCRDB, frmDb types.FRMCRDB, frmPgdb types.FRMPGDB, userClient user.UsersClient, actorClient actor.ActorClient, locClient location.LocationClient, locationClient location2.LocationClient, authClient auth.AuthClient, riskVgClient risk.RiskClient, inAppReferralClient inappreferral.InAppReferralClient, conf *config.Config, livClient liveness.LivenessClient, kycClient kyc.KycClient, client savings.SavingsClient, celestialClient celestial.CelestialClient, redListClient redlist.RedListClient, nameCheckClient namecheck.UNNameCheckClient, empClient employment.EmploymentClient, uiClient userintel.UserIntelServiceClient, genconf2 *genconf.Config, crClient creditreportv2.CreditReportManagerClient, commsClient comms.CommsClient, derivedAttrClient derivedattributes.DerivedAttributesManagerClient, bcClient bankcust.BankCustomerServiceClient, onbClient onboarding.OnboardingClient, leaActorsPublisher types2.LeaActorsPublisher, upiClient upi.UPIClient, oprStatusServiceClient operstatus.OperationalStatusServiceClient, screenerClient screener.ScreenerClient, tieringClient tiering.TieringClient, caseManagementClient case_management.CaseManagementClient, userContactClient contact.ContactClient, redisClient types.UserRedisStore, preApprovedLoanClient preapprovedloan.PreApprovedLoanClient, obfuscatorClient obfuscator.ObfuscatorClient) *risk2.Service {
	riskDataDaoCRDB := dao.NewRiskDataDao(db)
	leaComplaintCRDB := dao.NewLeaComplaintDao(frmDb)
	riskBankActionsCRDB := dao.NewRiskBankActionsDao(db)
	livenessManagerImpl := auth2.NewLivenessManager(livClient)
	facematchManagerImpl := auth2.NewFacematchManager(livClient, livenessManagerImpl)
	userDevicePropertyManagerImpl := essential.NewUserDevicePropertyManager(userClient, locationClient, genconf2, authClient)
	onbRiskModelProc := processors.NewOnbRiskModelProc(conf, userClient, actorClient, locationClient, authClient, riskVgClient, inAppReferralClient, livClient, kycClient, crClient, uiClient, derivedAttrClient, empClient, facematchManagerImpl, userDevicePropertyManagerImpl, livenessManagerImpl)
	onboardingEKYCNumberMismatch := processors.NewOnboardingEKYCNumberMismatch(kycClient)
	client2 := types.UserRedisStoreRedisClientProvider(redisClient)
	defaultTime := datetime.NewDefaultTime()
	redisSortedSetStore := tsaggr.NewRedisSortedSetStore(client2, defaultTime)
	onbVelocityProc := processors.NewOnbVelocityProc(genconf2, userClient, locationClient, redisSortedSetStore, defaultTime)
	redListKYCPinCodeProc := processors.NewRedListKYCPinCodeProc(redListClient, actorClient, userClient, preApprovedLoanClient)
	redListGeoPinCodeProc := processors.NewRedListGeoPinCodeProc(redListClient, userClient, locClient)
	redListGeoLatLongProc := processors.NewRedListGeoLatLongProc(conf, redListClient, userClient, locationClient)
	redListFiniteCodeProc := processors.NewRedListFiniteCodeProc(redListClient, actorClient, inAppReferralClient)
	redListRiskyDeviceProc := processors.NewRedListRiskyDeviceProc(redListClient, authClient, userDevicePropertyManagerImpl)
	actorManagerImpl := essential.NewActorManagerImpl(actorClient)
	userManagerImpl := essential.NewUserManagerImpl(actorManagerImpl, userClient, userContactClient, actorClient)
	redListEmailIdProc := processors.NewRedListEmailIdProc(redListClient, authClient, userManagerImpl)
	redListPhoneNumberProc := processors.NewRedListPhoneNumberProc(redListClient, authClient, userManagerImpl)
	epfoScreenerRiskProc := processors.NewEPFOScreenerRiskProc(nameCheckClient, empClient, actorClient, userClient)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	hunterRiskProc := processors.NewHunterRiskProc(uiClient, domainIdGenerator, genconf2)
	incomeEmploymentDiscrepancy := processors.NewIncomeEmploymentDiscrepancy(empClient, domainIdGenerator)
	afuRiskModelProc := processors.NewAFURiskModelProc(conf, userClient, actorClient, locationClient, authClient, riskVgClient, inAppReferralClient, screenerClient, livClient, kycClient, crClient, tieringClient, uiClient, bcClient, empClient, derivedAttrClient, onbClient, riskDataDaoCRDB, facematchManagerImpl, userDevicePropertyManagerImpl, livenessManagerImpl)
	affluenceClassProc := processors.NewAffluenceClassProc(onbRiskModelProc, uiClient, empClient, genconf2, riskDataDaoCRDB)
	upiVpaNameMatchProc := processors.NewUpiVpaNameMatchProc(upiClient, nameCheckClient, userClient, genconf2)
	emailHelper := riskparam.NewEmailHelper()
	junkEmailProc := processors.NewJunkEmailProc(userClient, emailHelper, genconf2)
	locationRiskModelProc := processors.NewLocationRiskModelProc(userClient, riskVgClient, locationClient, locClient, genconf2)
	installedAppsCheckProcessor := processors.NewInstalledAppsCheckProcessor(userDevicePropertyManagerImpl, genconf2, riskDataDaoCRDB)
	unifiedLeaComplaintPGDB := dao.NewUnifiedLeaComplaintPGDB(frmPgdb)
	unifiedLEAComplaintManager := unified_lea.NewUnifiedLEAComplaintManager(unifiedLeaComplaintPGDB)
	operationalStatusFetcher := accountstatus.NewOperationalStatusFetcher(oprStatusServiceClient, client)
	contactAssociations := essential.NewContactAssociations(unifiedLEAComplaintManager, userContactClient, genconf2, userClient, operationalStatusFetcher, riskBankActionsCRDB)
	contactAssociationProc := processors.NewLeaContactAssociation(contactAssociations, userClient, genconf2)
	plFiLiteOnbRiskModelProc := processors.NewPLFiLiteOnbRiskModelProc(conf, authClient, riskVgClient, inAppReferralClient, livClient, crClient, uiClient, derivedAttrClient, empClient, facematchManagerImpl, userDevicePropertyManagerImpl, livenessManagerImpl, preApprovedLoanClient, userManagerImpl, nameCheckClient, userClient)
	creditScoreAffluenceClassProc := processors.NewCreditScoreAffluenceClassProc(uiClient, crClient, genconf2)
	crossVideoLivenessFacematchProc := processors.NewCrossVideoLivenessFacematchProcessor(livenessManagerImpl, livClient, genconf2)
	alertDaoCRDB := dao2.NewAlertDao(frmDb)
	ruleDaoCRDB := dao2.NewRuleDao(frmDb)
	allowedAnnotationDaoCRDB := dao2.NewAllowedAnnotationDao(frmDb)
	reviewActionCRDB := dao2.NewReviewActionDao(frmDb)
	aggregatorImpl := aggregator.NewAggregatorImpl(ruleDaoCRDB, allowedAnnotationDaoCRDB, reviewActionCRDB)
	rulePrecisionDaoPGDB := dao2.NewRulePrecisionDao(frmPgdb)
	ruleManagerImpl := essential2.NewRuleManagerImpl(alertDaoCRDB, ruleDaoCRDB, rulePrecisionDaoPGDB)
	saUnreviewedAlertsCheckProcessor := processors.NewSAUnreviewedAlertsCheckProcessor(alertDaoCRDB, aggregatorImpl, ruleManagerImpl, genconf2)
	lowContactCountProc := processors.NewLowContactCountProc(userContactClient, genconf2)
	vpnPresenceProc := processors.NewVPNPresenceProc(livenessManagerImpl, genconf2)
	ipAddressProc := processors.NewIpAddress(userClient, redListClient, obfuscatorClient, genconf2)
	onbRiskModelProcV1 := processors.NewOnbRiskModelProcV1(conf, userClient, actorClient, locationClient, authClient, riskVgClient, inAppReferralClient, livClient, kycClient, crClient, uiClient, derivedAttrClient, empClient, facematchManagerImpl, userDevicePropertyManagerImpl, livenessManagerImpl)
	contactAssociationAndRiskyProfileProc := processors.NewContactAssociationAndRiskyProfileProc(userClient, contactAssociations, onbClient, locClient, crClient, empClient, genconf2)
	riskParamProcessorFactory := factory.NewRiskParamProcessorFactory(onbRiskModelProc, onboardingEKYCNumberMismatch, onbVelocityProc, redListKYCPinCodeProc, redListGeoPinCodeProc, redListGeoLatLongProc, redListFiniteCodeProc, redListRiskyDeviceProc, redListEmailIdProc, redListPhoneNumberProc, epfoScreenerRiskProc, hunterRiskProc, incomeEmploymentDiscrepancy, afuRiskModelProc, affluenceClassProc, upiVpaNameMatchProc, junkEmailProc, locationRiskModelProc, installedAppsCheckProcessor, contactAssociationProc, plFiLiteOnbRiskModelProc, creditScoreAffluenceClassProc, crossVideoLivenessFacematchProc, saUnreviewedAlertsCheckProcessor, lowContactCountProc, vpnPresenceProc, ipAddressProc, onbRiskModelProcV1, contactAssociationAndRiskyProfileProc)
	executorImpl := executor.NewExecutor(riskParamProcessorFactory, genconf2)
	userRiskScreener := screener2.NewUserRiskScreener(genconf2, riskDataDaoCRDB, executorImpl)
	webFormConfig := WebFormConfigProvider(conf)
	freezeBannerProvider := dynamicelements.NewFreezeBannerProvider(operationalStatusFetcher, genconf2, caseManagementClient, webFormConfig)
	formDaoPGDB := dao2.NewFormDao(frmPgdb)
	outcallBannerProvider := dynamicelements.NewOutcallBannerProvider(formDaoPGDB, genconf2)
	collateBannersImpl := comms2.NewCollateBanners(genconf2)
	leaBannerProvider := dynamicelements.NewLeaBannerProvider(collateBannersImpl)
	providerFactory := dynamicelements.NewProviderFactory(freezeBannerProvider, outcallBannerProvider, leaBannerProvider)
	screenerAttemptAlertBuilderImpl := alertgen.NewScreenerAttemptAlertBuilder(genconf2)
	alertGeneratorImpl := alertgen.NewAlertGenerator(caseManagementClient, screenerAttemptAlertBuilderImpl)
	screenerAttemptPGDB := dao.NewScreenerAttemptPGDB(frmPgdb)
	attemptManagerImpl := attemptmanager.NewAttemptManager(screenerAttemptPGDB, defaultTime)
	evaluatorImpl := evaluator.NewEvaluator(genconf2)
	vpaNameListFetcher := fetchers.NewVpaNameListFetcher(upiClient)
	fetcherFactoryImpl := factory.NewFetcherFactory(vpaNameListFetcher)
	screenerAttemptRiskDataMappingPGDB := dao.NewScreenerAttemptRiskDataMappingPGDB(frmPgdb)
	checkManagerImpl := checkmanager.NewCheckManager(genconf2, riskDataDaoCRDB, fetcherFactoryImpl, screenerAttemptRiskDataMappingPGDB)
	validatorImpl := attemptvalidator.New(genconf2)
	actorScreenerImpl := screener2.NewActorRiskScreener(executorImpl, alertGeneratorImpl, attemptManagerImpl, evaluatorImpl, checkManagerImpl, validatorImpl)
	leaComplaintNarrationPGDB := dao.NewLEAComplaintNarrationDao(frmPgdb)
	ruleReviewTypeMappingsCRDB := dao2.NewRuleReviewTypeMappingsDao(frmDb)
	alertManagerImpl := alert.NewAlertManagerImpl(alertDaoCRDB, ruleReviewTypeMappingsCRDB)
	processor := narration.NewProcessor(caseManagementClient, leaComplaintNarrationPGDB, leaComplaintCRDB, alertManagerImpl)
	leaComplaintSourcePGDB := dao.NewLEAComplaintSourceDao(frmPgdb)
	complaintManager := lea.NewComplaintManager(leaComplaintCRDB, leaComplaintSourcePGDB)
	disputePGDB := dao.NewDisputeDao(frmPgdb)
	npciProcessor := processors2.NewNpciProcessor()
	federalProcessor := processors2.NewFederalProcessor()
	otherBankProcessor := processors2.NewOtherBankProcessor()
	remarkFactory := remark.NewProcessorFactory(npciProcessor, federalProcessor, otherBankProcessor)
	leaDescriptor := remark.NewRemarkService(remarkFactory)
	redisCacheStorage := cache.NewRedisCacheStorage(client2)
	infoBuilderImpl := remark.NewInfoBuilderImpl(leaDescriptor, redisCacheStorage)
	builder := comms3.NewBuilder(userManagerImpl, genconf2, infoBuilderImpl)
	sender := comms4.NewSender(commsClient, userManagerImpl)
	commsSender := comms3.NewSender(builder, sender)
	sourceProcessor := complaint.NewSourceProcessor(leaComplaintCRDB, commsSender, leaComplaintSourcePGDB, redisCacheStorage)
	frmPgdbTxnExecutor := FrmPgdbTxnExecutorProvider(frmPgdb)
	bureauIdDetailsPGDB := dao.NewBureauIdDetailsDao(frmPgdb)
	familyTransferTagger := txn_taggers.NewFamilyTransferTagger(nameCheckClient, userClient)
	txnTaggerFactory := txn_taggers.NewTxnTaggerFactory(familyTransferTagger)
	txnTagMappingPGDB := dao.NewTxnTagMappingDao(frmPgdb)
	taggerImpl := tagger.NewTagger(txnTaggerFactory, txnTagMappingPGDB)
	service := risk2.NewService(conf, riskDataDaoCRDB, leaComplaintCRDB, riskBankActionsCRDB, client, celestialClient, actorClient, commsClient, leaActorsPublisher, riskParamProcessorFactory, userRiskScreener, providerFactory, caseManagementClient, actorScreenerImpl, processor, genconf2, complaintManager, disputePGDB, sourceProcessor, checkManagerImpl, screenerAttemptPGDB, frmPgdbTxnExecutor, bureauIdDetailsPGDB, taggerImpl, txnTagMappingPGDB, operationalStatusFetcher, unifiedLEAComplaintManager)
	return service
}

func InitializeLeaService(frmPgdb types.FRMPGDB, userClient user.UsersClient, client savings.SavingsClient, bcClient bankcust.BankCustomerServiceClient, depositClient deposit.DepositClient, commsClient comms.CommsClient, actorClient actor.ActorClient, genConf *genconf.Config, serviceClient operstatus.OperationalStatusServiceClient, config2 *config.Config, celestialClient celestial.CelestialClient) *lea.Service {
	unifiedLeaComplaintPGDB := dao.NewUnifiedLeaComplaintPGDB(frmPgdb)
	operationalStatusFetcher := accountstatus.NewOperationalStatusFetcher(serviceClient, client)
	unifiedLeaComplaintConfig := UnifiedLeaComplaintConfigProvider(config2)
	actorManagerImpl := essential.NewActorManagerImpl(actorClient)
	refineImpl := comms2.NewRefineImpl(unifiedLeaComplaintPGDB, operationalStatusFetcher, unifiedLeaComplaintConfig, actorManagerImpl, client)
	collateCommsImpl := comms2.NewCollateComms(actorClient, unifiedLeaComplaintConfig)
	service := lea.NewService(client, unifiedLeaComplaintPGDB, bcClient, depositClient, refineImpl, collateCommsImpl, commsClient, actorClient, celestialClient, config2)
	return service
}

func InitialiseRedListService(db types.EpifiCRDB, frmPGDB types.FRMPGDB, payclient pay.PayClient, riskClient risk3.RiskClient, savingsClient savings.SavingsClient, txnMonitoringClient dronapay.TransactionRiskDronaPayClient, txnRiskScoreClient risk3.TxnRiskScoreServiceClient, conf *config.Config, genConf *genconf.Config) *redlist2.Service {
	gormDB := GormProvider(db)
	redListCRDB := dao.NewRedListDao(gormDB)
	hashedContactProcessor := redlist2.NewHashedContactProc(redListCRDB)
	featureFlags := FeatureFlagsProvider(genConf)
	riskEvaluatorEntityImpl := dao.NewRiskEvaluatorEntityDao(featureFlags, db, frmPGDB)
	dataPusher := txn_monitoring.NewDataPusher(riskEvaluatorEntityImpl, txnMonitoringClient, txnRiskScoreClient)
	latLongProcessor := redlist2.NewLatLongProc(redListCRDB, dataPusher, genConf)
	pinCodeProcessor := redlist2.NewPinCodeProc(redListCRDB, dataPusher, genConf)
	finiteCodeProcessor := redlist2.NewFiniteCodeProc(redListCRDB)
	deviceProcessor := redlist2.NewDeviceProc(redListCRDB)
	atmProcessor := redlist2.NewAtmProc(redListCRDB, dataPusher, genConf)
	merchantsProcessor := redlist2.NewMerchantsProc(redListCRDB, dataPusher, genConf)
	onboardingStateProcessor := redlist2.NewOnboardingStateProc(redListCRDB, dataPusher, genConf)
	watchlistActorProcessor := redlist2.NewWatchListActorProc(redListCRDB)
	phoneNumberProcessor := redlist2.NewPhoneNumberProcessor(redListCRDB)
	deviceIdProcessor := redlist2.NewDeviceIdProcessor(redListCRDB)
	ipAddressProcessor := redlist2.NewIPAddressProcessor(redListCRDB)
	riskyMerchantProcessor := redlist2.NewRiskyMerchantProcessor(redListCRDB)
	redListProcessorFactoryImpl := redlist2.NewRedListProcessorFactoryImpl(hashedContactProcessor, latLongProcessor, pinCodeProcessor, finiteCodeProcessor, deviceProcessor, atmProcessor, merchantsProcessor, onboardingStateProcessor, watchlistActorProcessor, phoneNumberProcessor, deviceIdProcessor, ipAddressProcessor, riskyMerchantProcessor)
	service := redlist2.NewRedListService(redListCRDB, redListProcessorFactoryImpl)
	return service
}

func InitialiseWhiteListService(db types.FRMPGDB) *whitelist.Service {
	whiteListPGDB := dao.NewWhiteListDao(db)
	service := whitelist.NewWhiteListService(whiteListPGDB)
	return service
}

func InitialiseRiskConsumer(db types.EpifiCRDB, frmPGDB types.FRMPGDB, awsConf aws.Config, payclient pay.PayClient, riskClient risk3.RiskClient, savingsClient savings.SavingsClient, txnMonitoringClient dronapay.TransactionRiskDronaPayClient, txnRiskScoreClient risk3.TxnRiskScoreServiceClient, conf *config.Config, genConf *genconf.Config) *risk2.Consumer {
	gormDB := GormProvider(db)
	redListCRDB := dao.NewRedListDao(gormDB)
	hashedContactProcessor := redlist2.NewHashedContactProc(redListCRDB)
	featureFlags := FeatureFlagsProvider(genConf)
	riskEvaluatorEntityImpl := dao.NewRiskEvaluatorEntityDao(featureFlags, db, frmPGDB)
	dataPusher := txn_monitoring.NewDataPusher(riskEvaluatorEntityImpl, txnMonitoringClient, txnRiskScoreClient)
	latLongProcessor := redlist2.NewLatLongProc(redListCRDB, dataPusher, genConf)
	pinCodeProcessor := redlist2.NewPinCodeProc(redListCRDB, dataPusher, genConf)
	finiteCodeProcessor := redlist2.NewFiniteCodeProc(redListCRDB)
	deviceProcessor := redlist2.NewDeviceProc(redListCRDB)
	atmProcessor := redlist2.NewAtmProc(redListCRDB, dataPusher, genConf)
	merchantsProcessor := redlist2.NewMerchantsProc(redListCRDB, dataPusher, genConf)
	onboardingStateProcessor := redlist2.NewOnboardingStateProc(redListCRDB, dataPusher, genConf)
	watchlistActorProcessor := redlist2.NewWatchListActorProc(redListCRDB)
	phoneNumberProcessor := redlist2.NewPhoneNumberProcessor(redListCRDB)
	deviceIdProcessor := redlist2.NewDeviceIdProcessor(redListCRDB)
	ipAddressProcessor := redlist2.NewIPAddressProcessor(redListCRDB)
	riskyMerchantProcessor := redlist2.NewRiskyMerchantProcessor(redListCRDB)
	redListConsumer := redlist2.NewRedListConsumer(hashedContactProcessor, latLongProcessor, pinCodeProcessor, finiteCodeProcessor, deviceProcessor, atmProcessor, merchantsProcessor, onboardingStateProcessor, watchlistActorProcessor, phoneNumberProcessor, deviceIdProcessor, ipAddressProcessor, riskyMerchantProcessor, genConf)
	consumer := risk2.NewConsumer(redListConsumer, awsConf, payclient, riskClient, savingsClient)
	return consumer
}

func InitializeActivityProcessor(db types.EpifiCRDB, frmPGDB types.FRMPGDB, frmCRDB types.FRMCRDB, savingsClient savings.SavingsClient, userClient user.UsersClient, actorClient actor.ActorClient, authClient auth.AuthClient, operationalStatusClient operstatus.OperationalStatusServiceClient, conf *worker.Config, accountStatusApiFlag accountstatus.UseNewOperationalStatusAPIFlag, balanceClient balance.BalanceClient, riskClient risk3.RiskClient, redisClient *redis.Client, caseManagementClient case_management.CaseManagementClient, client lien.LienClient, freshdeskClient freshdesk.FreshdeskClient, crmClient crm.CRMClient, s3Client types2.CaseReprioritizationS3Client, lockRedisStore types2.LockRedisStore, vmClient vendormapping.VendorMappingServiceClient, redListClient redlist.RedListClient, vgRiskClient risk.RiskClient, fennelClient fennel.FennelFeatureStoreClient, scienapticClient scienaptic.ScienapticClient, caseStoreConfig *common.CaseStoreConfig) *activity.Processor {
	riskBankActionsCRDB := dao.NewRiskBankActionsDao(db)
	accountstatusFetcher := accountstatus.ProvideFetcherImplementation(savingsClient, operationalStatusClient, balanceClient, accountStatusApiFlag)
	npciProcessor := processors2.NewNpciProcessor()
	federalProcessor := processors2.NewFederalProcessor()
	otherBankProcessor := processors2.NewOtherBankProcessor()
	remarkFactory := remark.NewProcessorFactory(npciProcessor, federalProcessor, otherBankProcessor)
	leaDescriptor := remark.NewRemarkService(remarkFactory)
	redisCacheStorage := cache.NewRedisCacheStorage(redisClient)
	infoBuilderImpl := remark.NewInfoBuilderImpl(leaDescriptor, redisCacheStorage)
	leaComplaintCRDB := dao.NewLeaComplaintDao(frmCRDB)
	leaComplaintSourcePGDB := dao.NewLEAComplaintSourceDao(frmPGDB)
	complaintManager := lea.NewComplaintManager(leaComplaintCRDB, leaComplaintSourcePGDB)
	riskBankActionComms := bankActionCommsWorkerConfig(conf)
	webFormConfig := WebFormWorkerConfigProvider(conf)
	freezeActionCommsBuilder := comms5.NewFreezeActionCommsBuilder(actorClient, riskBankActionComms, savingsClient, webFormConfig)
	leaActionCommsBuilder := comms5.NewLEAActionCommsBuilder(riskBankActionComms, infoBuilderImpl, leaDescriptor, redisCacheStorage, complaintManager)
	unfreezeActionCommsBuilder := comms5.NewUnfreezeActionCommsBuilder(actorClient, riskBankActionComms)
	factoryImpl := comms5.NewBuilderFactory(freezeActionCommsBuilder, leaActionCommsBuilder, unfreezeActionCommsBuilder)
	unifiedLeaComplaintPGDB := dao.NewUnifiedLeaComplaintPGDB(frmPGDB)
	unifiedLeaComplaintConfig := UnifiedLeaComplaintWorkerConfigProvider(conf)
	actorManagerImpl := essential.NewActorManagerImpl(actorClient)
	refineImpl := comms2.NewRefineImpl(unifiedLeaComplaintPGDB, accountstatusFetcher, unifiedLeaComplaintConfig, actorManagerImpl, savingsClient)
	collateCommsImpl := comms2.NewCollateComms(actorClient, unifiedLeaComplaintConfig)
	lienRequestPGDB := dao.NewLienRequestDao(frmPGDB)
	lienManagerImpl := lien2.NewLienManagerImpl(client, lienRequestPGDB, riskBankActionsCRDB)
	uuidGenerator := idgen.NewUuidGenerator()
	redisStatelessLockManager := lock.NewRedisStatelessLockManager(redisClient, uuidGenerator)
	rulePrecisionDaoPGDB := dao2.NewRulePrecisionDao(frmPGDB)
	rulePrecisionProvider := provider.NewRulePrecisionProvider(rulePrecisionDaoPGDB)
	riskDataDaoCRDB := dao.NewRiskDataDao(db)
	userProfileModelPrecisionProvider := provider.NewUserProfileModelPrecisionProvider(riskDataDaoCRDB)
	watchlistPrecisionProvider := provider.NewWatchlistPrecisionProvider(redListClient)
	featureStore := wire.InitializeFeatureStore(fennelClient, scienapticClient, userClient)
	iFeatureStore := IFeatureStoreProvider(featureStore)
	modelPrecisionProvider := provider.NewModelPrecisionProvider(iFeatureStore)
	v := provider.GetPrecisionProviders(rulePrecisionProvider, userProfileModelPrecisionProvider, watchlistPrecisionProvider, modelPrecisionProvider)
	precisionFetcherImpl := fetcher.NewPrecisionFetcherImpl(v)
	ruleOccurrenceProvider := provider.NewRuleOccurrenceProvider()
	defaultOccurrenceProvider := provider.NewDefaultOccurrenceProvider()
	v2 := provider.GetOccurrenceProviders(ruleOccurrenceProvider, defaultOccurrenceProvider)
	occurrenceFetcherImpl := fetcher.NewOccurrenceFetcherImpl(v2)
	parameterIntegratorImpl := integrator.NewParameterIntegratorImpl(precisionFetcherImpl, occurrenceFetcherImpl)
	precisionBasedPrioritization := priotizer.NewPrecisionBasedPrioritization(parameterIntegratorImpl)
	dsModelBasedPrioritization := priotizer.NewDSModelBasedPrioritization(parameterIntegratorImpl, vgRiskClient)
	prioritisationFactoryImpl := prioritisation.NewFactoryImpl(precisionBasedPrioritization, dsModelBasedPrioritization)
	alertDaoCRDB := dao2.NewAlertDao(frmCRDB)
	ruleDaoCRDB := dao2.NewRuleDao(frmCRDB)
	allowedAnnotationDaoCRDB := dao2.NewAllowedAnnotationDao(frmCRDB)
	reviewActionCRDB := dao2.NewReviewActionDao(frmCRDB)
	aggregatorImpl := aggregator.NewAggregatorImpl(ruleDaoCRDB, allowedAnnotationDaoCRDB, reviewActionCRDB)
	alertWithRuleManagerImpl := alert_with_rule.NewAlertWithRuleManagerImpl(alertDaoCRDB, aggregatorImpl)
	externalCRMAnalystStore := analyststore.NewExternalCRMAnalystStore(crmClient)
	externalCRMCaseStore := casestore.NewExternalCRMCaseStore(crmClient, vmClient, caseStoreConfig, externalCRMAnalystStore)
	cachedCaseStore := casestore.NewCachedCaseStore(redisCacheStorage, externalCRMCaseStore)
	caseStoreImplFactory := casestore.NewCaseStoreImplFactory(cachedCaseStore, externalCRMCaseStore)
	processor := activity.NewProcessor(riskBankActionsCRDB, savingsClient, userClient, actorClient, authClient, conf, accountstatusFetcher, infoBuilderImpl, leaDescriptor, riskClient, complaintManager, redisCacheStorage, factoryImpl, refineImpl, caseManagementClient, collateCommsImpl, lienManagerImpl, freshdeskClient, crmClient, s3Client, redisStatelessLockManager, prioritisationFactoryImpl, alertWithRuleManagerImpl, caseStoreImplFactory, alertDaoCRDB)
	return processor
}

func InitializeDevService(db types.EpifiCRDB, frmDb types.FRMCRDB, frmPGDB types.FRMPGDB) *developer.RiskDevService {
	riskBankActionsCRDB := dao.NewRiskBankActionsDao(db)
	riskBankActionProcessor := processor.NewRiskBankActionProcessor(riskBankActionsCRDB)
	riskDataDaoCRDB := dao.NewRiskDataDao(db)
	riskDataProcessor := processor.NewRiskDataProcessor(riskDataDaoCRDB)
	alertDaoCRDB := dao2.NewAlertDao(frmDb)
	riskAlertProcessor := processor.NewRiskAlertProcessor(alertDaoCRDB)
	reviewActionCRDB := dao2.NewReviewActionDao(frmDb)
	reviewActionProcessor := processor.NewReviewActionProcessor(reviewActionCRDB)
	screenerAttemptPGDB := dao.NewScreenerAttemptPGDB(frmPGDB)
	screenerAttemptProcessor := processor.NewScreenerAttemptsProcessor(screenerAttemptPGDB)
	questionEntityMappingDaoPGDB := dao2.NewQuestionEntityMappingDao(frmPGDB)
	questionDaoPGDB := dao2.NewQuestionDao(frmPGDB)
	generatorImpl := questionnaire.NewGeneratorImpl(questionEntityMappingDaoPGDB, questionDaoPGDB)
	questionnaireValidatorImpl := validator.NewQuestionnaireValidatorImpl()
	formProcessor := processor.NewFormProcessor(generatorImpl, questionnaireValidatorImpl)
	unifiedLeaComplaintPGDB := dao.NewUnifiedLeaComplaintPGDB(frmPGDB)
	unifiedLeaComplaintProcessor := processor.NewUnifiedLeaComplaintProcessor(unifiedLeaComplaintPGDB)
	lienRequestPGDB := dao.NewLienRequestDao(frmPGDB)
	lienRequestProcessor := processor.NewLienRequestProcessor(lienRequestPGDB)
	devFactory := developer.NewDevFactory(riskBankActionProcessor, riskDataProcessor, riskAlertProcessor, reviewActionProcessor, screenerAttemptProcessor, formProcessor, unifiedLeaComplaintProcessor, lienRequestProcessor)
	riskDevService := developer.NewRiskDevService(devFactory)
	return riskDevService
}

func InitialiseTxnRiskScoreService(db types.EpifiCRDB, frmPGDB types.FRMPGDB, genConf *genconf.Config) *risk2.TxnScoreService {
	featureFlags := FeatureFlagsProvider(genConf)
	txnRiskScoreImpl := dao.NewTxnRiskScoreDao(featureFlags, db, frmPGDB)
	txnRiskScoreDetailImpl := dao.NewTxnRiskScoreDetailDao(featureFlags, db, frmPGDB)
	txnScoreService := risk2.NewTxnRiskScoreService(txnRiskScoreImpl, txnRiskScoreDetailImpl)
	return txnScoreService
}

func InitialiseTxnMonitoringService(db types.EpifiCRDB, frmDb types.FRMCRDB, frmPGDB types.FRMPGDB, locationClient location2.LocationClient, config2 *config.Config, piClient paymentinstrument.PiClient, actorClient actor.ActorClient, onboardingClient onboarding.OnboardingClient, kycClient kyc.KycClient, livenessClient liveness.LivenessClient, vgTxnMonitoringClient dronapay.TransactionRiskDronaPayClient, userClient user.UsersClient, txnRiskScoreClient risk3.TxnRiskScoreServiceClient, empClient employment.EmploymentClient, refClient inappreferral.InAppReferralClient, merchantClient merchant.MerchantServiceClient, genConfig *genconf.Config, userContactClient contact.ContactClient, savingClient savings.SavingsClient, caseManagementClient case_management.CaseManagementClient, bankCustClient bankcust.BankCustomerServiceClient, txnCategorizerClient categorizer.TxnCategorizerClient, nameCheckClient namecheck.UNNameCheckClient) *consumer.TxnMonitoringService {
	leaComplaintCRDB := dao.NewLeaComplaintDao(frmDb)
	federalCustomerManagerImpl := federal_customer.NewFederalCustomerManager(bankCustClient)
	familyTransferTagger := txn_taggers.NewFamilyTransferTagger(nameCheckClient, userClient)
	txnTaggerFactory := txn_taggers.NewTxnTaggerFactory(familyTransferTagger)
	txnTagMappingPGDB := dao.NewTxnTagMappingDao(frmPGDB)
	taggerImpl := tagger.NewTagger(txnTaggerFactory, txnTagMappingPGDB)
	collector := txn_monitoring.NewCollector(leaComplaintCRDB, locationClient, config2, piClient, actorClient, onboardingClient, kycClient, livenessClient, vgTxnMonitoringClient, userClient, empClient, refClient, merchantClient, userContactClient, federalCustomerManagerImpl, txnCategorizerClient, taggerImpl)
	featureFlags := FeatureFlagsProvider(genConfig)
	riskEvaluatorEntityImpl := dao.NewRiskEvaluatorEntityDao(featureFlags, db, frmPGDB)
	decider := txn_monitoring.NewDecider(riskEvaluatorEntityImpl, genConfig)
	dataPusher := txn_monitoring.NewDataPusher(riskEvaluatorEntityImpl, vgTxnMonitoringClient, txnRiskScoreClient)
	responseProcessorImpl := txn_monitoring.NewResponseProcessorImpl(txnRiskScoreClient, caseManagementClient)
	txnMonitoringService := consumer.NewTxnMonitoringService(config2, actorClient, collector, decider, dataPusher, responseProcessorImpl, savingClient, caseManagementClient)
	return txnMonitoringService
}

func InitialiseTxnMonitoringCallbackService(caseManagementClient case_management.CaseManagementClient, genConf *genconf.Config) *callback.TxnMonitoringCallbackService {
	alertCallbackBuilderImpl := callback.NewAlertCallbackBuilder(genConf, caseManagementClient)
	txnMonitoringCallbackService := callback.NewTxnMonitoringCallbackService(caseManagementClient, alertCallbackBuilderImpl)
	return txnMonitoringCallbackService
}

// config: {"batchRuleEngineS3Client": "BatchRuleEngineS3().BucketName"}
func InitialiseCaseManagementConsumer(epifiCRDB types.EpifiCRDB, livenessClient liveness.LivenessClient, caseManagementClient case_management.CaseManagementClient, config2 *config.Config, genConf *genconf.Config, commsClient comms.CommsClient, actorClient actor.ActorClient, frmPGDB types.FRMPGDB, frmCRDB types.FRMCRDB, redisClient types.UserRedisStore, alertsPublisher types2.AlertsPublisher, batchRuleEngineS3Client types2.BatchRuleEngineS3Client, celestialClient celestial.CelestialClient, savingsClient savings.SavingsClient, userClient user.UsersClient, cxTicketClient ticket.TicketClient, pClient payment.PaymentClient) *consumer2.CaseManagementConsumerService {
	db := types.EpifiCRDBGormDBProvider(epifiCRDB)
	persistentQueue := persistentqueue.NewPersistentQueue(db)
	persistentQueueHelper := helper.NewPersistentQueueHelper(persistentQueue)
	livenessProcessor := processors3.NewLivenessProcessor(livenessClient, persistentQueueHelper)
	userReviewProcessor := processors3.NewUserReviewProcessor(persistentQueueHelper)
	livenessSampleReviewProcessor := processors3.NewLivenessSampleReviewProcessor(livenessClient, persistentQueueHelper)
	transactionReviewBatchProcessor := processors3.NewTransactionReviewBatchProcessor(caseManagementClient, config2)
	userReviewBatchProcessor := processors3.NewUserReviewBatchProcessor(caseManagementClient, persistentQueueHelper, genConf)
	screenerReviewBatchProcessor := processors3.NewScreenerReviewBatchProcessor(caseManagementClient, genConf)
	consumerFactory := consumer2.NewProcessorFactory(livenessProcessor, userReviewProcessor, livenessSampleReviewProcessor, transactionReviewBatchProcessor, userReviewBatchProcessor, screenerReviewBatchProcessor)
	dsBatchProcessor := processor2.NewDSBatchProcessor(caseManagementClient)
	alertFactory := alert2.NewProcessorFactory(dsBatchProcessor)
	riskBankActionComms := RiskBankActionConfigProvider(config2)
	webFormConfig := WebFormConfigProvider(config2)
	freezeActionCommsBuilder := comms5.NewFreezeActionCommsBuilder(actorClient, riskBankActionComms, savingsClient, webFormConfig)
	npciProcessor := processors2.NewNpciProcessor()
	federalProcessor := processors2.NewFederalProcessor()
	otherBankProcessor := processors2.NewOtherBankProcessor()
	remarkFactory := remark.NewProcessorFactory(npciProcessor, federalProcessor, otherBankProcessor)
	leaDescriptor := remark.NewRemarkService(remarkFactory)
	client := types.UserRedisStoreRedisClientProvider(redisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	infoBuilderImpl := remark.NewInfoBuilderImpl(leaDescriptor, redisCacheStorage)
	leaComplaintCRDB := dao.NewLeaComplaintDao(frmCRDB)
	leaComplaintSourcePGDB := dao.NewLEAComplaintSourceDao(frmPGDB)
	complaintManager := lea.NewComplaintManager(leaComplaintCRDB, leaComplaintSourcePGDB)
	leaActionCommsBuilder := comms5.NewLEAActionCommsBuilder(riskBankActionComms, infoBuilderImpl, leaDescriptor, redisCacheStorage, complaintManager)
	unfreezeActionCommsBuilder := comms5.NewUnfreezeActionCommsBuilder(actorClient, riskBankActionComms)
	factoryImpl := comms5.NewBuilderFactory(freezeActionCommsBuilder, leaActionCommsBuilder, unfreezeActionCommsBuilder)
	riskBankActionsCRDB := dao.NewRiskBankActionsDao(epifiCRDB)
	actorManagerImpl := essential.NewActorManagerImpl(actorClient)
	alertBuilderImpl := batchruleengine.NewAlertBuilderImpl()
	processorImpl := batchruleengine.NewProcessorImpl(alertBuilderImpl, alertsPublisher, batchRuleEngineS3Client)
	formQuestionMappingDaoPGDB := dao2.NewFormQuestionMappingDao(frmPGDB)
	formDaoPGDB := dao2.NewFormDao(frmPGDB)
	questionEntityMappingDaoPGDB := dao2.NewQuestionEntityMappingDao(frmPGDB)
	questionDaoPGDB := dao2.NewQuestionDao(frmPGDB)
	generatorImpl := questionnaire.NewGeneratorImpl(questionEntityMappingDaoPGDB, questionDaoPGDB)
	questionResponseDaoPGDB := dao2.NewQuestionResponseDao(frmPGDB)
	getterImpl := questionnaire.NewGetterImpl(formQuestionMappingDaoPGDB, formDaoPGDB, generatorImpl, questionResponseDaoPGDB, questionDaoPGDB)
	responseValidatorImpl := validator.NewResponseValidatorImpl()
	responseProcessorImpl := questionnaire.NewResponseProcessorImpl(responseValidatorImpl, questionResponseDaoPGDB, formQuestionMappingDaoPGDB, questionDaoPGDB)
	validatorImpl := submission.NewValidatorImpl(responseProcessorImpl)
	frmPgdbTxnExecutor := FrmPgdbTxnExecutorProvider(frmPGDB)
	ticketManagerImpl := cx.NewTicketManagerImpl(cxTicketClient)
	submissionProcessorImpl := submission.NewProcessorImpl(validatorImpl, responseProcessorImpl, frmPgdbTxnExecutor, formDaoPGDB, celestialClient, ticketManagerImpl, userClient)
	handlerImpl := form.NewHandlerImpl(getterImpl, formDaoPGDB, validatorImpl, submissionProcessorImpl)
	transactionBlockDaoPGDB := dao2.NewTransactionBlockDao(frmPGDB)
	ruleDaoCRDB := dao2.NewRuleDao(frmCRDB)
	alertDaoCRDB := dao2.NewAlertDao(frmCRDB)
	annotationDaoCRDB := dao2.NewAnnotationDao(frmCRDB)
	ruleReviewTypeMappingsCRDB := dao2.NewRuleReviewTypeMappingsDao(frmCRDB)
	allowedAnnotationDaoCRDB := dao2.NewAllowedAnnotationDao(frmCRDB)
	reviewActionCRDB := dao2.NewReviewActionDao(frmCRDB)
	aggregatorImpl := aggregator.NewAggregatorImpl(ruleDaoCRDB, allowedAnnotationDaoCRDB, reviewActionCRDB)
	suggestedActionDaoCRDB := dao2.NewSuggestedActionDao(frmPGDB)
	ruleProcessorImpl := case_management2.NewRuleProcessorImpl(ruleDaoCRDB, annotationDaoCRDB, ruleReviewTypeMappingsCRDB, aggregatorImpl, suggestedActionDaoCRDB)
	caseManagementConsumerService := consumer2.NewCaseManagementConsumerService(consumerFactory, alertFactory, config2, factoryImpl, riskBankActionsCRDB, commsClient, actorManagerImpl, processorImpl, handlerImpl, celestialClient, formDaoPGDB, transactionBlockDaoPGDB, ruleDaoCRDB, alertDaoCRDB, ruleProcessorImpl, genConf, actorClient, pClient)
	return caseManagementConsumerService
}

func InitialiseCaseManagementService(orderClient order.OrderServiceClient, piClient paymentinstrument.PiClient, merchantClient merchant.MerchantServiceClient, crmClient crm.CRMClient, vmClient vendormapping.VendorMappingServiceClient, db types.FRMCRDB, celestialClient celestial.CelestialClient, segmentServiceClient segment.SegmentationServiceClient, genConf *genconf.Config, redisClient types.UserRedisStore, userClient user.UsersClient, productClient product.ProductClient, formSubmissionEventPublisher types2.FormSubmissionEventPublisher, frmPgdb types.FRMPGDB, salaryProgramClient salaryprogram.SalaryProgramClient, employmentClient employment.EmploymentClient, cxTicketClient ticket.TicketClient) *case_management2.Service {
	riskTransactionHelper := txnhelper.New(piClient, merchantClient, orderClient)
	client := types.UserRedisStoreRedisClientProvider(redisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	caseStoreConfig := caseStoreConfigProvider(genConf)
	externalCRMAnalystStore := analyststore.NewExternalCRMAnalystStore(crmClient)
	externalCRMCaseStore := casestore.NewExternalCRMCaseStore(crmClient, vmClient, caseStoreConfig, externalCRMAnalystStore)
	cachedCaseStore := casestore.NewCachedCaseStore(redisCacheStorage, externalCRMCaseStore)
	caseStoreImplFactory := casestore.NewCaseStoreImplFactory(cachedCaseStore, externalCRMCaseStore)
	reviewActionCRDB := dao2.NewReviewActionDao(db)
	alertDaoCRDB := dao2.NewAlertDao(db)
	ruleDaoCRDB := dao2.NewRuleDao(db)
	allowedAnnotationDaoCRDB := dao2.NewAllowedAnnotationDao(db)
	aggregatorImpl := aggregator.NewAggregatorImpl(ruleDaoCRDB, allowedAnnotationDaoCRDB, reviewActionCRDB)
	commentDaoCRDB := dao2.NewCommentDao(db)
	annotationDaoCRDB := dao2.NewAnnotationDao(db)
	ruleReviewTypeMappingsCRDB := dao2.NewRuleReviewTypeMappingsDao(db)
	suggestedActionDaoCRDB := dao2.NewSuggestedActionDao(frmPgdb)
	ruleProcessorImpl := case_management2.NewRuleProcessorImpl(ruleDaoCRDB, annotationDaoCRDB, ruleReviewTypeMappingsCRDB, aggregatorImpl, suggestedActionDaoCRDB)
	clock := lock.NewRealClockProvider()
	redisRwLock := lock.NewRedisRwLock(client, clock)
	priorityCaseCacheStore := priority_case_store.NewCasePriorityCacheStore(genConf, redisCacheStorage, caseStoreImplFactory, redisRwLock)
	priorityCaseAllocator := assignment.NewPriorityCaseAllocator(caseStoreImplFactory, priorityCaseCacheStore, externalCRMAnalystStore, genConf)
	v := fIProductToSegmentMapConfigProvider(genConf)
	membershipManagerImpl := segment2.NewMembershipManagerImpl(segmentServiceClient)
	userGroupManagerImpl := essential.NewUserGroupManagerImpl(userClient)
	userAttributesFetcherImpl := user2.NewUserAttributesFetcherImpl(userClient)
	fiUserRelationshipImpl := fi_user_relationship.NewFiUserRelationshipImpl(v, membershipManagerImpl, userGroupManagerImpl, productClient, salaryProgramClient, employmentClient, userAttributesFetcherImpl)
	formQuestionMappingDaoPGDB := dao2.NewFormQuestionMappingDao(frmPgdb)
	formDaoPGDB := dao2.NewFormDao(frmPgdb)
	questionEntityMappingDaoPGDB := dao2.NewQuestionEntityMappingDao(frmPgdb)
	questionDaoPGDB := dao2.NewQuestionDao(frmPgdb)
	generatorImpl := questionnaire.NewGeneratorImpl(questionEntityMappingDaoPGDB, questionDaoPGDB)
	questionResponseDaoPGDB := dao2.NewQuestionResponseDao(frmPgdb)
	getterImpl := questionnaire.NewGetterImpl(formQuestionMappingDaoPGDB, formDaoPGDB, generatorImpl, questionResponseDaoPGDB, questionDaoPGDB)
	responseValidatorImpl := validator.NewResponseValidatorImpl()
	responseProcessorImpl := questionnaire.NewResponseProcessorImpl(responseValidatorImpl, questionResponseDaoPGDB, formQuestionMappingDaoPGDB, questionDaoPGDB)
	validatorImpl := submission.NewValidatorImpl(responseProcessorImpl)
	frmPgdbTxnExecutor := FrmPgdbTxnExecutorProvider(frmPgdb)
	ticketManagerImpl := cx.NewTicketManagerImpl(cxTicketClient)
	processorImpl := submission.NewProcessorImpl(validatorImpl, responseProcessorImpl, frmPgdbTxnExecutor, formDaoPGDB, celestialClient, ticketManagerImpl, userClient)
	handlerImpl := form.NewHandlerImpl(getterImpl, formDaoPGDB, validatorImpl, processorImpl)
	annotationTypeDaoPGDB := dao2.NewAnnotationTypeDao(frmPgdb)
	uiElementAnnotationTypeMappingDaoPGDB := dao2.NewUIElementAnnotationTypeMappingDao(frmPgdb)
	transactionBlockDaoPGDB := dao2.NewTransactionBlockDao(frmPgdb)
	service := case_management2.NewService(orderClient, riskTransactionHelper, caseStoreImplFactory, celestialClient, reviewActionCRDB, alertDaoCRDB, aggregatorImpl, commentDaoCRDB, allowedAnnotationDaoCRDB, annotationDaoCRDB, ruleDaoCRDB, ruleProcessorImpl, priorityCaseAllocator, fiUserRelationshipImpl, formSubmissionEventPublisher, handlerImpl, formDaoPGDB, frmPgdbTxnExecutor, genConf, annotationTypeDaoPGDB, uiElementAnnotationTypeMappingDaoPGDB, suggestedActionDaoCRDB, ruleReviewTypeMappingsCRDB, transactionBlockDaoPGDB)
	return service
}

func InitializeCaseManagementActivityProcessor(frmDb types.FRMCRDB, epifiCrdb types.EpifiCRDB, frmPgdb types.FRMPGDB, conf *worker.Config, redisRLClient types2.RedisRLClient, redisClient types2.RedisClient, crmClient crm.CRMClient, vmClient vendormapping.VendorMappingServiceClient, celestialClient celestial.CelestialClient, actorClient actor.ActorClient, savingsClient savings.SavingsClient, onboardingClient onboarding.OnboardingClient, livClient liveness.LivenessClient, usersClient user.UsersClient, userContactClient contact.ContactClient, authClient auth.AuthClient, segmentServiceClient segment.SegmentationServiceClient, accountStatusApiFlag accountstatus.UseNewOperationalStatusAPIFlag, operationalStatusClient operstatus.OperationalStatusServiceClient, balanceClient balance.BalanceClient, redListClient redlist.RedListClient, fennelClient fennel.FennelFeatureStoreClient, scienapticClient scienaptic.ScienapticClient, commsClient comms.CommsClient, productClient product.ProductClient, salaryProgramClient salaryprogram.SalaryProgramClient, employmentClient employment.EmploymentClient, cxTicketClient ticket.TicketClient, vgRiskClient risk.RiskClient, caseManagementClient case_management.CaseManagementClient) *activity2.Processor {
	reviewActionCRDB := dao2.NewReviewActionDao(frmDb)
	ruleDaoCRDB := dao2.NewRuleDao(frmDb)
	alertDaoCRDB := dao2.NewAlertDao(frmDb)
	commentDaoCRDB := dao2.NewCommentDao(frmDb)
	ruleReviewTypeMappingsCRDB := dao2.NewRuleReviewTypeMappingsDao(frmDb)
	annotationDaoCRDB := dao2.NewAnnotationDao(frmDb)
	allowedAnnotationDaoCRDB := dao2.NewAllowedAnnotationDao(frmDb)
	aggregatorImpl := aggregator.NewAggregatorImpl(ruleDaoCRDB, allowedAnnotationDaoCRDB, reviewActionCRDB)
	suggestedActionDaoCRDB := dao2.NewSuggestedActionDao(frmPgdb)
	ruleProcessorImpl := case_management2.NewRuleProcessorImpl(ruleDaoCRDB, annotationDaoCRDB, ruleReviewTypeMappingsCRDB, aggregatorImpl, suggestedActionDaoCRDB)
	client := RedisRLClientProvider(redisRLClient)
	clock := lock.NewRealClockProvider()
	uuidGenerator := idgen.NewUuidGenerator()
	redisLockManager := lock.NewRedisLockManager(client, clock, uuidGenerator)
	cacheStorage := InitializeRedisCacheStorage(redisClient)
	caseStoreConfig := caseStoreWorkerConfigProvider(conf)
	externalCRMAnalystStore := analyststore.NewExternalCRMAnalystStore(crmClient)
	externalCRMCaseStore := casestore.NewExternalCRMCaseStore(crmClient, vmClient, caseStoreConfig, externalCRMAnalystStore)
	cachedCaseStore := casestore.NewCachedCaseStore(cacheStorage, externalCRMCaseStore)
	caseStoreImplFactory := casestore.NewCaseStoreImplFactory(cachedCaseStore, externalCRMCaseStore)
	alertWithRuleManagerImpl := alert_with_rule.NewAlertWithRuleManagerImpl(alertDaoCRDB, aggregatorImpl)
	rulePrecisionDaoPGDB := dao2.NewRulePrecisionDao(frmPgdb)
	rulePrecisionProvider := provider.NewRulePrecisionProvider(rulePrecisionDaoPGDB)
	riskDataDaoCRDB := dao.NewRiskDataDao(epifiCrdb)
	userProfileModelPrecisionProvider := provider.NewUserProfileModelPrecisionProvider(riskDataDaoCRDB)
	watchlistPrecisionProvider := provider.NewWatchlistPrecisionProvider(redListClient)
	featureStore := wire.InitializeFeatureStore(fennelClient, scienapticClient, usersClient)
	iFeatureStore := IFeatureStoreProvider(featureStore)
	modelPrecisionProvider := provider.NewModelPrecisionProvider(iFeatureStore)
	v := provider.GetPrecisionProviders(rulePrecisionProvider, userProfileModelPrecisionProvider, watchlistPrecisionProvider, modelPrecisionProvider)
	precisionFetcherImpl := fetcher.NewPrecisionFetcherImpl(v)
	ruleOccurrenceProvider := provider.NewRuleOccurrenceProvider()
	defaultOccurrenceProvider := provider.NewDefaultOccurrenceProvider()
	v2 := provider.GetOccurrenceProviders(ruleOccurrenceProvider, defaultOccurrenceProvider)
	occurrenceFetcherImpl := fetcher.NewOccurrenceFetcherImpl(v2)
	parameterIntegratorImpl := integrator.NewParameterIntegratorImpl(precisionFetcherImpl, occurrenceFetcherImpl)
	precisionBasedPrioritization := priotizer.NewPrecisionBasedPrioritization(parameterIntegratorImpl)
	dsModelBasedPrioritization := priotizer.NewDSModelBasedPrioritization(parameterIntegratorImpl, vgRiskClient)
	factoryImpl := prioritisation.NewFactoryImpl(precisionBasedPrioritization, dsModelBasedPrioritization)
	prioritiserConfig := PrioritiserConfigWorkerConfigProvider(conf)
	caseAnalyser := caseanalyser.NewCaseAnalyser(alertWithRuleManagerImpl, factoryImpl, prioritiserConfig)
	caseBuilder := casestore.NewCaseBuilder(caseAnalyser)
	riskBankActionsCRDB := dao.NewRiskBankActionsDao(epifiCrdb)
	ruleManagerImpl := essential2.NewRuleManagerImpl(alertDaoCRDB, ruleDaoCRDB, rulePrecisionDaoPGDB)
	riskyUserRequiredInfoDaoCRDB := dao2.NewRiskyUserRequiredInfoDao(frmDb)
	riskyUserRequiredInfoMappingDaoCRDB := dao2.NewRiskyUserRequiredInfoMappingDao(frmDb)
	riskyUserRequiredInfoManagerImpl := essential2.NewRiskyUserRequiredInfoManagerImpl(ruleManagerImpl, riskyUserRequiredInfoDaoCRDB, riskyUserRequiredInfoMappingDaoCRDB)
	questionBuilderImpl := auto_action.NewQuestionBuilderImpl()
	actorManagerImpl := essential2.NewActorManagerImpl(caseStoreImplFactory, actorClient)
	userManagerImpl := essential2.NewUserManagerImpl(actorManagerImpl, usersClient)
	senderEmailFromWorkerConfig := auto_action.NewSenderEmailFromWorkerConfig(conf)
	freezeActionProcessor := auto_action.NewFreezeActionProcessor(caseStoreImplFactory, riskyUserRequiredInfoManagerImpl, questionBuilderImpl, userManagerImpl, senderEmailFromWorkerConfig)
	formDaoPGDB := dao2.NewFormDao(frmPgdb)
	outCallActionProcessor := auto_action.NewOutCallActionProcessor(formDaoPGDB)
	v3 := auto_action.ProvideRiskActionProcessors(freezeActionProcessor, outCallActionProcessor)
	v4 := fIProductToSegmentMapWorkerConfigProvider(conf)
	membershipManagerImpl := segment2.NewMembershipManagerImpl(segmentServiceClient)
	userGroupManagerImpl := essential.NewUserGroupManagerImpl(usersClient)
	userAttributesFetcherImpl := user2.NewUserAttributesFetcherImpl(usersClient)
	fiUserRelationshipImpl := fi_user_relationship.NewFiUserRelationshipImpl(v4, membershipManagerImpl, userGroupManagerImpl, productClient, salaryProgramClient, employmentClient, userAttributesFetcherImpl)
	existingProductsProcessor := processors4.NewExistingProductsProcessor(fiUserRelationshipImpl)
	exclusionFactory := exclusion.NewExclusionProcessorFactory(existingProductsProcessor)
	consolidatorImpl := exclusion.NewConsolidatorImpl(exclusionFactory)
	autoActionProcessorImpl := auto_action.NewAutoActionProcessorImpl(caseStoreImplFactory, alertDaoCRDB, suggestedActionDaoCRDB, v3, consolidatorImpl, actorManagerImpl, riskBankActionsCRDB)
	accountstatusFetcher := accountstatus.ProvideFetcherImplementation(savingsClient, operationalStatusClient, balanceClient, accountStatusApiFlag)
	accountStatusReviewExclusionProcessor := processor3.NewAccountStatusReviewExclusionProcessor(accountstatusFetcher)
	configProviderFromWorkerConfig := processor3.NewConfigProviderFromWorkerConfig(conf)
	segmentBasedExclusionProcessor := processor3.NewSegmentBasedExclusionProcessor(segmentServiceClient, configProviderFromWorkerConfig)
	latestPassedCaseBasedExclusionProcessor := processor3.NewLatestPassedCaseBasedExclusionProcessor(alertDaoCRDB, reviewActionCRDB, ruleDaoCRDB)
	riskBankAction := BankActionsWorkerConfigProvider(conf)
	actionInProgressExclusionProcessor := processor3.NewActionInProgressExclusionProcessor(riskBankActionsCRDB, riskBankAction)
	factory2 := exclusion2.NewExclusionProcessorFactory(accountStatusReviewExclusionProcessor, segmentBasedExclusionProcessor, latestPassedCaseBasedExclusionProcessor, actionInProgressExclusionProcessor)
	exclusionConsolidatorImpl := exclusion2.NewConsolidatorImpl(actorManagerImpl, factory2)
	moveToReviewManagerImpl := case_operation.NewMoveToReviewManagerImpl(caseStoreImplFactory)
	alertManagerImpl := alert.NewAlertManagerImpl(alertDaoCRDB, ruleReviewTypeMappingsCRDB)
	screenerAttemptPGDB := dao.NewScreenerAttemptPGDB(frmPgdb)
	verdictProcessorImpl := screener2.NewVerdictProcessor(screenerAttemptPGDB, alertDaoCRDB)
	essentialActorManagerImpl := essential.NewActorManagerImpl(actorClient)
	essentialUserManagerImpl := essential.NewUserManagerImpl(essentialActorManagerImpl, usersClient, userContactClient, actorClient)
	outcallConfig := OutcallConfigProvider(conf)
	investigationEmailConfig := InvestigationEmailConfigProvider(conf)
	sender := comms6.NewSender(commsClient, essentialUserManagerImpl, outcallConfig, investigationEmailConfig)
	caseManagementPlatformConfig := CaseManagementPlatformConfigProvider(conf)
	reminderImpl := comms6.NewReminder(sender, actorManagerImpl, commentDaoCRDB, outcallConfig, caseManagementPlatformConfig)
	collectorImpl := collector.NewCollectorImpl(accountstatusFetcher, caseStoreImplFactory, riskBankActionsCRDB, reviewActionCRDB)
	alertHandlingConfig := AlertHandlingConfigProvider(conf)
	basicRuleChecks := checks.NewBasicRuleChecks(alertHandlingConfig)
	accountStatus := checks.NewAccountStatus(riskBankActionsCRDB, alertHandlingConfig)
	ruleInCoolOff := checks.NewRuleInCoolOff(alertDaoCRDB, alertHandlingConfig)
	autoAction := checks.NewAutoAction(ruleProcessorImpl)
	negativeBankActionInProgress := checks.NewNegativeBankActionInProgress(riskBankActionsCRDB, alertHandlingConfig, riskBankAction)
	whiteListPGDB := dao.NewWhiteListDao(frmPgdb)
	whitelistCheck := checks.NewWhitelistCheck(whiteListPGDB)
	actorCheck := checks.NewActorCheck(actorClient)
	handlingConsolidatorImpl := handling.NewConsolidatorImpl(collectorImpl, basicRuleChecks, accountStatus, ruleInCoolOff, autoAction, negativeBankActionInProgress, whitelistCheck, actorCheck)
	userRelationshipEvaluator := evaluators.NewUserRelationshipImpl(fiUserRelationshipImpl, externalCRMAnalystStore)
	randomSampleEvaluator := evaluators.NewRandomSampleImpl(conf, externalCRMAnalystStore)
	whitelistMemberEvaluator := evaluators.NewWhitelistMemberEvaluator(whiteListPGDB, externalCRMAnalystStore)
	evaluatorFactory := processor4.NewActionEvaluatorFactory(userRelationshipEvaluator, randomSampleEvaluator, whitelistMemberEvaluator)
	evaluatorImpl := processor4.NewEvaluatorImpl(evaluatorFactory)
	questionEntityMappingDaoPGDB := dao2.NewQuestionEntityMappingDao(frmPgdb)
	questionDaoPGDB := dao2.NewQuestionDao(frmPgdb)
	generatorImpl := questionnaire.NewGeneratorImpl(questionEntityMappingDaoPGDB, questionDaoPGDB)
	formQuestionMappingDaoPGDB := dao2.NewFormQuestionMappingDao(frmPgdb)
	frmPgdbTxnExecutor := FrmPgdbTxnExecutorProvider(frmPgdb)
	questionnaireValidatorImpl := validator.NewQuestionnaireValidatorImpl()
	accountStatusChecker := checks2.NewAccountStatusChecker(accountstatusFetcher, reviewActionCRDB)
	existingCaseChecker := checks2.NewExistingCaseChecker(alertManagerImpl, caseStoreImplFactory)
	riskBankActionComms := RiskBankActionWorkerConfigProvider(conf)
	webFormConfig := WebFormWorkerConfigProvider(conf)
	freezeActionCommsBuilder := comms5.NewFreezeActionCommsBuilder(actorClient, riskBankActionComms, savingsClient, webFormConfig)
	npciProcessor := processors2.NewNpciProcessor()
	federalProcessor := processors2.NewFederalProcessor()
	otherBankProcessor := processors2.NewOtherBankProcessor()
	remarkFactory := remark.NewProcessorFactory(npciProcessor, federalProcessor, otherBankProcessor)
	leaDescriptor := remark.NewRemarkService(remarkFactory)
	infoBuilderImpl := remark.NewInfoBuilderImpl(leaDescriptor, cacheStorage)
	leaComplaintCRDB := dao.NewLeaComplaintDao(frmDb)
	leaComplaintSourcePGDB := dao.NewLEAComplaintSourceDao(frmPgdb)
	complaintManager := lea.NewComplaintManager(leaComplaintCRDB, leaComplaintSourcePGDB)
	leaActionCommsBuilder := comms5.NewLEAActionCommsBuilder(riskBankActionComms, infoBuilderImpl, leaDescriptor, cacheStorage, complaintManager)
	unfreezeActionCommsBuilder := comms5.NewUnfreezeActionCommsBuilder(actorClient, riskBankActionComms)
	commsFactoryImpl := comms5.NewBuilderFactory(freezeActionCommsBuilder, leaActionCommsBuilder, unfreezeActionCommsBuilder)
	existingFormChecker := checks2.NewExistingFormChecker(caseManagementClient, riskBankActionsCRDB, commsFactoryImpl, commsClient, usersClient)
	checksFactoryImpl := handling2.NewCheckFactoryImpl(accountStatusChecker, existingCaseChecker, existingFormChecker)
	handlingTypeDeciderImpl := handling2.NewHandlingTypeDeciderImpl(checksFactoryImpl)
	ticketManagerImpl := cx.NewTicketManagerImpl(cxTicketClient)
	activityProcessor := activity2.NewProcessor(conf, reviewActionCRDB, ruleDaoCRDB, alertDaoCRDB, commentDaoCRDB, ruleReviewTypeMappingsCRDB, ruleProcessorImpl, redisLockManager, caseStoreImplFactory, caseBuilder, celestialClient, riskBankActionsCRDB, actorClient, savingsClient, onboardingClient, livClient, autoActionProcessorImpl, authClient, exclusionConsolidatorImpl, moveToReviewManagerImpl, alertManagerImpl, verdictProcessorImpl, reminderImpl, aggregatorImpl, handlingConsolidatorImpl, evaluatorImpl, formDaoPGDB, generatorImpl, formQuestionMappingDaoPGDB, frmPgdbTxnExecutor, essentialUserManagerImpl, commsClient, questionnaireValidatorImpl, ruleManagerImpl, handlingTypeDeciderImpl, ticketManagerImpl, accountstatusFetcher, usersClient)
	return activityProcessor
}

func InitializeRedisCacheStorage(client types2.RedisClient) cache.CacheStorage {
	redisClient := RedisClientProvider(client)
	redisCacheStorage := cache.NewRedisCacheStorage(redisClient)
	return redisCacheStorage
}

func InitialiseProfileService(frmDb types.FRMCRDB, epifiCrdb types.EpifiCRDB, config2 *genconf.Config, savingsClient savings.SavingsClient, staticConfig *config.Config, oprStatusServiceClient operstatus.OperationalStatusServiceClient, userContactClient contact.ContactClient, inAppReferralClient inappreferral.InAppReferralClient, userClient user.UsersClient, actorClient actor.ActorClient, celestialClient celestial.CelestialClient, frmPGDB types.FRMPGDB, cxTicketClient ticket.TicketClient) *profile.Service {
	riskBankActionsCRDB := dao.NewRiskBankActionsDao(epifiCrdb)
	alertDaoCRDB := dao2.NewAlertDao(frmDb)
	operationalStatusFetcher := accountstatus.NewOperationalStatusFetcher(oprStatusServiceClient, savingsClient)
	leaComplaintCRDB := dao.NewLeaComplaintDao(frmDb)
	leaComplaintSourcePGDB := dao.NewLEAComplaintSourceDao(frmPGDB)
	complaintManager := lea.NewComplaintManager(leaComplaintCRDB, leaComplaintSourcePGDB)
	unifiedLeaComplaintPGDB := dao.NewUnifiedLeaComplaintPGDB(frmPGDB)
	unifiedLeaComplaintConfig := UnifiedLeaComplaintConfigProvider(staticConfig)
	helperImpl := connections.NewHelperImpl(riskBankActionsCRDB, unifiedLeaComplaintPGDB, unifiedLeaComplaintConfig)
	actorManagerImpl := essential.NewActorManagerImpl(actorClient)
	userManagerImpl := essential.NewUserManagerImpl(actorManagerImpl, userClient, userContactClient, actorClient)
	contactsFetcherImpl := connections.NewContactsFetcherImpl(userContactClient, helperImpl, userManagerImpl)
	referralFetcherImpl := connections.NewReferralFetcherImpl(inAppReferralClient, helperImpl)
	formQuestionMappingDaoPGDB := dao2.NewFormQuestionMappingDao(frmPGDB)
	formDaoPGDB := dao2.NewFormDao(frmPGDB)
	questionEntityMappingDaoPGDB := dao2.NewQuestionEntityMappingDao(frmPGDB)
	questionDaoPGDB := dao2.NewQuestionDao(frmPGDB)
	generatorImpl := questionnaire.NewGeneratorImpl(questionEntityMappingDaoPGDB, questionDaoPGDB)
	questionResponseDaoPGDB := dao2.NewQuestionResponseDao(frmPGDB)
	getterImpl := questionnaire.NewGetterImpl(formQuestionMappingDaoPGDB, formDaoPGDB, generatorImpl, questionResponseDaoPGDB, questionDaoPGDB)
	responseValidatorImpl := validator.NewResponseValidatorImpl()
	responseProcessorImpl := questionnaire.NewResponseProcessorImpl(responseValidatorImpl, questionResponseDaoPGDB, formQuestionMappingDaoPGDB, questionDaoPGDB)
	validatorImpl := submission.NewValidatorImpl(responseProcessorImpl)
	frmPgdbTxnExecutor := FrmPgdbTxnExecutorProvider(frmPGDB)
	ticketManagerImpl := cx.NewTicketManagerImpl(cxTicketClient)
	processorImpl := submission.NewProcessorImpl(validatorImpl, responseProcessorImpl, frmPgdbTxnExecutor, formDaoPGDB, celestialClient, ticketManagerImpl, userClient)
	handlerImpl := form.NewHandlerImpl(getterImpl, formDaoPGDB, validatorImpl, processorImpl)
	webFormConfig := WebFormConfigProvider(staticConfig)
	unifiedLEAComplaintManager := unified_lea.NewUnifiedLEAComplaintManager(unifiedLeaComplaintPGDB)
	service := profile.NewService(riskBankActionsCRDB, savingsClient, alertDaoCRDB, config2, operationalStatusFetcher, complaintManager, contactsFetcherImpl, referralFetcherImpl, handlerImpl, webFormConfig, unifiedLEAComplaintManager)
	return service
}

func InitialiseProfileConsumer(frmPGDB types.FRMPGDB, epifiCrdb types.EpifiCRDB, profileClient profile2.ProfileClient, savingsClient savings.SavingsClient, riskClient risk3.RiskClient) *consumer3.ProfileConsumerService {
	riskBankActionsCRDB := dao.NewRiskBankActionsDao(epifiCrdb)
	unifiedLeaComplaintPGDB := dao.NewUnifiedLeaComplaintPGDB(frmPGDB)
	profileConsumerService := consumer3.NewProfileService(savingsClient, riskClient, riskBankActionsCRDB, unifiedLeaComplaintPGDB)
	return profileConsumerService
}

func InitializeDronapayRuleHitCallbackService(DronapayRuleHitCallbackPublisher dronapay2.DronapayRuleHitCallbackPublisher) *dronapay2.Service {
	service := dronapay2.NewService(DronapayRuleHitCallbackPublisher)
	return service
}

// wire.go:

func GormProvider(db types.EpifiCRDB) *gorm.DB {
	return db
}

func FeatureFlagsProvider(genConf *genconf.Config) *genconf.FeatureFlags {
	return genConf.FeatureFlags()
}

// As EscalationEventPublisher config is not there in config files, initialisation code is commented and nil is getting passed
// If we define type then it should be present in config, so adding WA because
// It requires full cleanup from case_management.NewService
func nilEscalationEventPublisherProvider() queue.Publisher {
	return nil
}

func caseStoreConfigProvider(genConf *genconf.Config) *common.CaseStoreConfig {
	return genConf.CaseStore()
}

func fIProductToSegmentMapWorkerConfigProvider(conf *worker.Config) map[string]string {
	return conf.FIProductToSegmentMap
}

func fIProductToSegmentMapConfigProvider(genConf *genconf.Config) map[string]string {
	return genConf.FIProductToSegmentMap()
}

func caseStoreWorkerConfigProvider(conf *worker.Config) *common.CaseStoreConfig {
	return conf.CaseStore
}

func PrioritiserConfigWorkerConfigProvider(conf *worker.Config) *common.PrioritiserConfig {
	return conf.PrioritiserConfig
}

func bankActionCommsWorkerConfig(conf *worker.Config) *common.RiskBankActionComms {
	return conf.RiskBankActionComms
}

func RedisClientProvider(client types2.RedisClient) *redis.Client {
	return client
}

func RedisRLClientProvider(client types2.RedisRLClient) *redis.Client {
	return client
}

func IFeatureStoreProvider(store *featurestore.FeatureStore) featurestore.IFeatureStore {
	return store
}

func OutcallConfigProvider(conf *worker.Config) *common.OutcallConfig {
	return conf.Outcall
}

func CaseManagementPlatformConfigProvider(conf *worker.Config) *common.CaseManagementPlatformConfig {
	return conf.CaseManagementPlatform
}

func InvestigationEmailConfigProvider(conf *worker.Config) *common.InvestigationEmailConfig {
	return conf.InvestigationEmail
}

func BankActionsConfigProvider(genConf *genconf.Config) *common.RiskBankAction {
	return genConf.RiskBankAction()
}

func RiskBankActionConfigProvider(conf *config.Config) *common.RiskBankActionComms {
	return conf.RiskBankActionComms
}

func RiskBankActionWorkerConfigProvider(conf *worker.Config) *common.RiskBankActionComms {
	return conf.RiskBankActionComms
}

func AlertHandlingConfigProvider(conf *worker.Config) *common.AlertHandlingConfig {
	return conf.AlertHandling
}

func BankActionsWorkerConfigProvider(conf *worker.Config) *common.RiskBankAction {
	return conf.RiskBankAction
}

func WebFormWorkerConfigProvider(conf *worker.Config) *common.WebFormConfig {
	return conf.WebFormConfig
}

func WebFormConfigProvider(conf *config.Config) *common.WebFormConfig {
	return conf.WebFormConfig
}

func FrmPgdbTxnExecutorProvider(db types.FRMPGDB) types2.FrmPgdbTxnExecutor {
	return storagev2.NewGormTxnExecutor(db)
}

func UnifiedLeaComplaintWorkerConfigProvider(conf *worker.Config) *common.UnifiedLeaComplaintConfig {
	return conf.UnifiedLEAComplaintConfig
}

func UnifiedLeaComplaintConfigProvider(conf *config.Config) *common.UnifiedLeaComplaintConfig {
	return conf.UnifiedLEAComplaintConfig
}
