package consumer

import (
	"context"
	"errors"
	"os"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/logger"

	riskEnums "github.com/epifi/gamma/api/risk/enums"
	mnrlPb "github.com/epifi/gamma/api/risk/mnrl"
	mockDao "github.com/epifi/gamma/risk/dao/mocks"
)

// TestMain initializes test components, runs tests and exits
func TestMain(m *testing.M) {
	// Initialize logger for tests
	logger.Init("test")

	exitCode := m.Run()
	os.Exit(exitCode)
}

func TestService_ProcessMnrlReport(t *testing.T) {
	type args struct {
		ctx   context.Context
		event *mnrlPb.MnrlReportIngestEvent
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mnrlReportDaoMock *mockDao.MockMnrlReportDao)
		wantErr    bool
	}{
		{
			name: "success - single report",
			args: args{
				ctx: context.Background(),
				event: &mnrlPb.MnrlReportIngestEvent{
					MnrlReports: []*mnrlPb.MnrlReport{
						{
							MobileNumber:        "9876543210",
							LsaName:             riskEnums.LsaName_LSA_NAME_DELHI,
							TspName:             "Test TSP",
							DateOfDisconnection: &date.Date{Year: 2024, Month: 1, Day: 15},
							ActionReasons:       "Test action reasons",
							Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
						},
					},
				},
			},
			setupMocks: func(mnrlReportDaoMock *mockDao.MockMnrlReportDao) {
				mnrlReportDaoMock.EXPECT().
					UpsertInBatch(gomock.Any(), gomock.Any()).
					Return(nil, nil).
					Times(1)
			},
			wantErr: false,
		},
		{
			name: "success - multiple reports",
			args: args{
				ctx: context.Background(),
				event: &mnrlPb.MnrlReportIngestEvent{
					MnrlReports: []*mnrlPb.MnrlReport{
						{
							MobileNumber:        "9876543210",
							LsaName:             riskEnums.LsaName_LSA_NAME_DELHI,
							TspName:             "Test TSP 1",
							DateOfDisconnection: &date.Date{Year: 2024, Month: 1, Day: 15},
							ActionReasons:       "Test action reasons 1",
							Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
						},
						{
							MobileNumber:        "9876543211",
							LsaName:             riskEnums.LsaName_LSA_NAME_MUMBAI,
							TspName:             "Test TSP 2",
							DateOfDisconnection: &date.Date{Year: 2024, Month: 1, Day: 16},
							ActionReasons:       "Test action reasons 2",
							Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_DOT_FAKE,
						},
					},
				},
			},
			setupMocks: func(mnrlReportDaoMock *mockDao.MockMnrlReportDao) {
				mnrlReportDaoMock.EXPECT().
					UpsertInBatch(gomock.Any(), gomock.Any()).
					Return(nil, nil).
					Times(1)
			},
			wantErr: false,
		},
		{
			name: "success - empty reports",
			args: args{
				ctx: context.Background(),
				event: &mnrlPb.MnrlReportIngestEvent{
					MnrlReports: []*mnrlPb.MnrlReport{},
				},
			},
			setupMocks: func(mnrlReportDaoMock *mockDao.MockMnrlReportDao) {
				// No expectations for empty reports
			},
			wantErr: false,
		},
		{
			name: "error - dao upsert fails for single report",
			args: args{
				ctx: context.Background(),
				event: &mnrlPb.MnrlReportIngestEvent{
					MnrlReports: []*mnrlPb.MnrlReport{
						{
							MobileNumber:        "9876543210",
							LsaName:             riskEnums.LsaName_LSA_NAME_DELHI,
							TspName:             "Test TSP",
							DateOfDisconnection: &date.Date{Year: 2024, Month: 1, Day: 15},
							ActionReasons:       "Test action reasons",
							Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
						},
					},
				},
			},
			setupMocks: func(mnrlReportDaoMock *mockDao.MockMnrlReportDao) {
				// Batch upsert fails, fallback to individual upsert
				mnrlReportDaoMock.EXPECT().
					UpsertInBatch(gomock.Any(), gomock.Any()).
					Return(nil, errors.New("database error")).
					Times(1)
				mnrlReportDaoMock.EXPECT().
					Upsert(gomock.Any(), gomock.Any()).
					Return(nil, errors.New("database error")).
					Times(1)
			},
			wantErr: true,
		},
		{
			name: "partial success - some reports succeed, some fail",
			args: args{
				ctx: context.Background(),
				event: &mnrlPb.MnrlReportIngestEvent{
					MnrlReports: []*mnrlPb.MnrlReport{
						{
							MobileNumber:        "9876543210",
							LsaName:             riskEnums.LsaName_LSA_NAME_DELHI,
							TspName:             "Test TSP 1",
							DateOfDisconnection: &date.Date{Year: 2024, Month: 1, Day: 15},
							ActionReasons:       "Test action reasons 1",
							Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
						},
						{
							MobileNumber:        "9876543211",
							LsaName:             riskEnums.LsaName_LSA_NAME_MUMBAI,
							TspName:             "Test TSP 2",
							DateOfDisconnection: &date.Date{Year: 2024, Month: 1, Day: 16},
							ActionReasons:       "Test action reasons 2",
							Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_DOT_FAKE,
						},
						{
							MobileNumber:        "9876543212",
							LsaName:             riskEnums.LsaName_LSA_NAME_KARNATAKA,
							TspName:             "Test TSP 3",
							DateOfDisconnection: &date.Date{Year: 2024, Month: 1, Day: 17},
							ActionReasons:       "Test action reasons 3",
							Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_NONCOMPLIANT,
						},
					},
				},
			},
			setupMocks: func(mnrlReportDaoMock *mockDao.MockMnrlReportDao) {
				// Batch upsert fails, fallback to individual upserts
				mnrlReportDaoMock.EXPECT().
					UpsertInBatch(gomock.Any(), gomock.Any()).
					Return(nil, errors.New("database error")).
					Times(1)
				// First individual upsert succeeds
				mnrlReportDaoMock.EXPECT().
					Upsert(gomock.Any(), gomock.Any()).
					Return(nil, nil).
					Times(1)
				// Second individual upsert fails
				mnrlReportDaoMock.EXPECT().
					Upsert(gomock.Any(), gomock.Any()).
					Return(nil, errors.New("database error")).
					Times(1)
				// Third individual upsert succeeds
				mnrlReportDaoMock.EXPECT().
					Upsert(gomock.Any(), gomock.Any()).
					Return(nil, nil).
					Times(1)
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mnrlReportDaoMock := mockDao.NewMockMnrlReportDao(ctrl)
			mnrlSuspectedFlaggedMobileDaoMock := mockDao.NewMockMnrlSuspectedFlaggedMobileDao(ctrl)

			// Setup mocks
			tt.setupMocks(mnrlReportDaoMock)

			// Create service with mocked dependencies
			service := NewService(mnrlReportDaoMock, mnrlSuspectedFlaggedMobileDaoMock)

			// Execute the method
			err := service.ProcessMnrlReport(tt.args.ctx, tt.args.event)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestService_ProcessMnrlSuspectedFlaggedMobile(t *testing.T) {
	type args struct {
		ctx   context.Context
		event *mnrlPb.MnrlSuspectedFlaggedMobileIngestEvent
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mnrlSuspectedFlaggedMobileDaoMock *mockDao.MockMnrlSuspectedFlaggedMobileDao)
		wantErr    bool
	}{
		{
			name: "success - single mobile with IST timestamp conversion",
			args: args{
				ctx: context.Background(),
				event: &mnrlPb.MnrlSuspectedFlaggedMobileIngestEvent{
					MnrlSuspectedFlaggedMobiles: []*mnrlPb.MnrlSuspectedFlaggedMobile{
						{
							MobileNumber:        "1234567890",
							Reason:              "Test reason for suspected flagged",
							ReverifiedAt:        timestamppb.New(time.Date(2025, 6, 24, 16, 58, 30, 0, time.UTC)),
							Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
							LsaCode:             riskEnums.LsaName_LSA_NAME_DELHI,
							TspName:             "Test TSP",
							SensitivityIndex:    riskEnums.SensitivityIndex_SENSITIVITY_INDEX_HIGH,
							DistributionDetails: "Test distribution details",
						},
					},
				},
			},
			setupMocks: func(mnrlSuspectedFlaggedMobileDaoMock *mockDao.MockMnrlSuspectedFlaggedMobileDao) {
				mnrlSuspectedFlaggedMobileDaoMock.EXPECT().
					UpsertInBatch(gomock.Any(), gomock.Any()).
					Return(nil, nil).
					Times(1)
			},
			wantErr: false,
		},
		{
			name: "success - multiple mobiles with IST timestamp conversion",
			args: args{
				ctx: context.Background(),
				event: &mnrlPb.MnrlSuspectedFlaggedMobileIngestEvent{
					MnrlSuspectedFlaggedMobiles: []*mnrlPb.MnrlSuspectedFlaggedMobile{
						{
							MobileNumber:        "1234567890",
							Reason:              "Test reason 1 for suspected flagged",
							ReverifiedAt:        timestamppb.New(time.Date(2025, 6, 24, 16, 58, 30, 0, time.UTC)),
							Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
							LsaCode:             riskEnums.LsaName_LSA_NAME_DELHI,
							TspName:             "Test TSP 1",
							SensitivityIndex:    riskEnums.SensitivityIndex_SENSITIVITY_INDEX_HIGH,
							DistributionDetails: "Test distribution details 1",
						},
						{
							MobileNumber:        "0987654321",
							Reason:              "Test reason 2 for suspected flagged",
							ReverifiedAt:        timestamppb.New(time.Date(2025, 6, 24, 12, 0, 0, 0, time.UTC)),
							Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_DOT_FAKE,
							LsaCode:             riskEnums.LsaName_LSA_NAME_KARNATAKA,
							TspName:             "Test TSP 2",
							SensitivityIndex:    riskEnums.SensitivityIndex_SENSITIVITY_INDEX_MEDIUM,
							DistributionDetails: "Test distribution details 2",
						},
					},
				},
			},
			setupMocks: func(mnrlSuspectedFlaggedMobileDaoMock *mockDao.MockMnrlSuspectedFlaggedMobileDao) {
				mnrlSuspectedFlaggedMobileDaoMock.EXPECT().
					UpsertInBatch(gomock.Any(), gomock.Any()).
					Return(nil, nil).
					Times(1)
			},
			wantErr: false,
		},
		{
			name: "success - mobile without reverified_at timestamp",
			args: args{
				ctx: context.Background(),
				event: &mnrlPb.MnrlSuspectedFlaggedMobileIngestEvent{
					MnrlSuspectedFlaggedMobiles: []*mnrlPb.MnrlSuspectedFlaggedMobile{
						{
							MobileNumber:        "1234567890",
							Reason:              "Test reason for suspected flagged",
							Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
							LsaCode:             riskEnums.LsaName_LSA_NAME_DELHI,
							TspName:             "Test TSP",
							SensitivityIndex:    riskEnums.SensitivityIndex_SENSITIVITY_INDEX_HIGH,
							DistributionDetails: "Test distribution details",
							// No ReverifiedAt timestamp
						},
					},
				},
			},
			setupMocks: func(mnrlSuspectedFlaggedMobileDaoMock *mockDao.MockMnrlSuspectedFlaggedMobileDao) {
				mnrlSuspectedFlaggedMobileDaoMock.EXPECT().
					UpsertInBatch(gomock.Any(), gomock.Any()).
					Return(nil, nil).
					Times(1)
			},
			wantErr: false,
		},
		{
			name: "success - empty mobiles",
			args: args{
				ctx: context.Background(),
				event: &mnrlPb.MnrlSuspectedFlaggedMobileIngestEvent{
					MnrlSuspectedFlaggedMobiles: []*mnrlPb.MnrlSuspectedFlaggedMobile{},
				},
			},
			setupMocks: func(mnrlSuspectedFlaggedMobileDaoMock *mockDao.MockMnrlSuspectedFlaggedMobileDao) {
				// No expectations for empty mobiles
			},
			wantErr: false,
		},
		{
			name: "error - dao upsert fails for single mobile",
			args: args{
				ctx: context.Background(),
				event: &mnrlPb.MnrlSuspectedFlaggedMobileIngestEvent{
					MnrlSuspectedFlaggedMobiles: []*mnrlPb.MnrlSuspectedFlaggedMobile{
						{
							MobileNumber:        "1234567890",
							Reason:              "Test reason for suspected flagged",
							ReverifiedAt:        timestamppb.New(time.Date(2025, 6, 24, 16, 58, 30, 0, time.UTC)),
							Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
							LsaCode:             riskEnums.LsaName_LSA_NAME_DELHI,
							TspName:             "Test TSP",
							SensitivityIndex:    riskEnums.SensitivityIndex_SENSITIVITY_INDEX_HIGH,
							DistributionDetails: "Test distribution details",
						},
					},
				},
			},
			setupMocks: func(mnrlSuspectedFlaggedMobileDaoMock *mockDao.MockMnrlSuspectedFlaggedMobileDao) {
				// Batch upsert fails, fallback to individual upsert
				mnrlSuspectedFlaggedMobileDaoMock.EXPECT().
					UpsertInBatch(gomock.Any(), gomock.Any()).
					Return(nil, errors.New("database error")).
					Times(1)
				mnrlSuspectedFlaggedMobileDaoMock.EXPECT().
					Upsert(gomock.Any(), gomock.Any()).
					Return(nil, errors.New("database error")).
					Times(1)
			},
			wantErr: true,
		},
		{
			name: "partial success - some mobiles succeed, some fail",
			args: args{
				ctx: context.Background(),
				event: &mnrlPb.MnrlSuspectedFlaggedMobileIngestEvent{
					MnrlSuspectedFlaggedMobiles: []*mnrlPb.MnrlSuspectedFlaggedMobile{
						{
							MobileNumber:        "1234567890",
							Reason:              "Test reason 1 for suspected flagged",
							ReverifiedAt:        timestamppb.New(time.Date(2025, 6, 24, 16, 58, 30, 0, time.UTC)),
							Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_LEA,
							LsaCode:             riskEnums.LsaName_LSA_NAME_DELHI,
							TspName:             "Test TSP 1",
							SensitivityIndex:    riskEnums.SensitivityIndex_SENSITIVITY_INDEX_HIGH,
							DistributionDetails: "Test distribution details 1",
						},
						{
							MobileNumber:        "0987654321",
							Reason:              "Test reason 2 for suspected flagged",
							ReverifiedAt:        timestamppb.New(time.Date(2025, 6, 24, 12, 0, 0, 0, time.UTC)),
							Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_DOT_FAKE,
							LsaCode:             riskEnums.LsaName_LSA_NAME_KARNATAKA,
							TspName:             "Test TSP 2",
							SensitivityIndex:    riskEnums.SensitivityIndex_SENSITIVITY_INDEX_MEDIUM,
							DistributionDetails: "Test distribution details 2",
						},
						{
							MobileNumber:        "5555555555",
							Reason:              "Test reason 3 for suspected flagged",
							ReverifiedAt:        timestamppb.New(time.Date(2025, 6, 24, 10, 0, 0, 0, time.UTC)),
							Usecase:             riskEnums.MnrlUsecase_MNRL_USECASE_NONCOMPLIANT,
							LsaCode:             riskEnums.LsaName_LSA_NAME_MUMBAI,
							TspName:             "Test TSP 3",
							SensitivityIndex:    riskEnums.SensitivityIndex_SENSITIVITY_INDEX_VERY_HIGH,
							DistributionDetails: "Test distribution details 3",
						},
					},
				},
			},
			setupMocks: func(mnrlSuspectedFlaggedMobileDaoMock *mockDao.MockMnrlSuspectedFlaggedMobileDao) {
				// Batch upsert fails, fallback to individual upserts
				mnrlSuspectedFlaggedMobileDaoMock.EXPECT().
					UpsertInBatch(gomock.Any(), gomock.Any()).
					Return(nil, errors.New("database error")).
					Times(1)
				// First individual upsert succeeds
				mnrlSuspectedFlaggedMobileDaoMock.EXPECT().
					Upsert(gomock.Any(), gomock.Any()).
					Return(nil, nil).
					Times(1)
				// Second individual upsert fails
				mnrlSuspectedFlaggedMobileDaoMock.EXPECT().
					Upsert(gomock.Any(), gomock.Any()).
					Return(nil, errors.New("database error")).
					Times(1)
				// Third individual upsert succeeds
				mnrlSuspectedFlaggedMobileDaoMock.EXPECT().
					Upsert(gomock.Any(), gomock.Any()).
					Return(nil, nil).
					Times(1)
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mnrlReportDaoMock := mockDao.NewMockMnrlReportDao(ctrl)
			mnrlSuspectedFlaggedMobileDaoMock := mockDao.NewMockMnrlSuspectedFlaggedMobileDao(ctrl)

			// Setup mocks
			tt.setupMocks(mnrlSuspectedFlaggedMobileDaoMock)

			// Create service with mocked dependencies
			service := NewService(mnrlReportDaoMock, mnrlSuspectedFlaggedMobileDaoMock)

			// Execute the method
			err := service.ProcessMnrlSuspectedFlaggedMobile(tt.args.ctx, tt.args.event)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestNewService(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mnrlReportDaoMock := mockDao.NewMockMnrlReportDao(ctrl)
	mnrlSuspectedFlaggedMobileDaoMock := mockDao.NewMockMnrlSuspectedFlaggedMobileDao(ctrl)

	// Test service creation
	service := NewService(mnrlReportDaoMock, mnrlSuspectedFlaggedMobileDaoMock)

	// Assert service is not nil and dependencies are properly set
	require.NotNil(t, service)
	assert.NotNil(t, service.mnrlReportDao)
	assert.NotNil(t, service.mnrlSuspectedFlaggedMobileDao)
	assert.Equal(t, mnrlReportDaoMock, service.mnrlReportDao)
	assert.Equal(t, mnrlSuspectedFlaggedMobileDaoMock, service.mnrlSuspectedFlaggedMobileDao)
}
