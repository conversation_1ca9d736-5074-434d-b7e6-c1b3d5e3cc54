package mnrl

import (
	"flag"
	"os"
	"testing"

	"github.com/epifi/gamma/risk/test"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	_, teardown = test.InitTestServerWithoutDB()
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
