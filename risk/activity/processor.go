package activity

import (
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/lock"

	operationalStatusPb "github.com/epifi/gamma/api/accounts/operstatus"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth"
	riskpb "github.com/epifi/gamma/api/risk"
	caseManagementPb "github.com/epifi/gamma/api/risk/case_management"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/user"
	crmPb "github.com/epifi/gamma/api/vendorgateway/crm"
	freshdeskPb "github.com/epifi/gamma/api/vendorgateway/cx/freshdesk"
	"github.com/epifi/gamma/risk/accountstatus"
	bankActionComms "github.com/epifi/gamma/risk/bankactions/comms"
	dao2 "github.com/epifi/gamma/risk/case_management/dao"
	"github.com/epifi/gamma/risk/case_management/essential/alert_with_rule"
	"github.com/epifi/gamma/risk/case_management/prioritisation"

	"github.com/epifi/gamma/risk/case_management/casestore"
	riskWorkerConfig "github.com/epifi/gamma/risk/config/worker"
	"github.com/epifi/gamma/risk/dao"
	"github.com/epifi/gamma/risk/lea"
	leaRemark "github.com/epifi/gamma/risk/lea/remark"
	leaComms "github.com/epifi/gamma/risk/lea/unified_lea/comms"
	"github.com/epifi/gamma/risk/lien"
	wireTypes "github.com/epifi/gamma/risk/wire/types"
)

type Processor struct {
	riskClient                    riskpb.RiskClient
	riskBankActionDao             dao.RiskBankActionsDao
	savingsClient                 savingsPb.SavingsClient
	userClient                    user.UsersClient
	actorClient                   actor.ActorClient
	authClient                    auth.AuthClient
	operationalStatusClient       operationalStatusPb.OperationalStatusServiceClient
	infoBuilder                   leaRemark.InfoBuilder
	leaRemark                     leaRemark.Remarks
	conf                          *riskWorkerConfig.Config
	accountStatusFetcher          accountstatus.Fetcher
	leaComplaintManager           lea.IComplaintManager
	cacheStorage                  cache.CacheStorage
	bankActionCommsBuilderFactory bankActionComms.BuilderFactory
	refine                        leaComms.Refine
	caseManagementClient          caseManagementPb.CaseManagementClient
	collateComms                  leaComms.CollateComms
	lienManager                   lien.LienManager
	freshdeskClient               freshdeskPb.FreshdeskClient
	crmClient                     crmPb.CRMClient
	s3Client                      wireTypes.CaseReprioritizationS3Client
	distributedLockManager        lock.StatelessLockManager
	prioritizationFactory         prioritisation.Factory
	alertWithRuleManager          alert_with_rule.AlertWithRuleManager
	caseStore                     casestore.CaseStore
	alertDao                      dao2.AlertDao
}

func NewProcessor(
	riskBankActionDao dao.RiskBankActionsDao,
	savingsClient savingsPb.SavingsClient,
	userClient user.UsersClient,
	actorClient actor.ActorClient,
	authClient auth.AuthClient,
	conf *riskWorkerConfig.Config,
	accountStatusFetcher accountstatus.Fetcher,
	infoBuilder leaRemark.InfoBuilder,
	leaRemark leaRemark.Remarks,
	riskClient riskpb.RiskClient,
	leaComplaintManager lea.IComplaintManager,
	cacheStorage cache.CacheStorage,
	bankActionCommsBuilderFactory bankActionComms.BuilderFactory,
	refine leaComms.Refine,
	caseManagementClient caseManagementPb.CaseManagementClient,
	collateComms leaComms.CollateComms,
	lienManager lien.LienManager,
	freshdeskClient freshdeskPb.FreshdeskClient,
	crmClient crmPb.CRMClient,
	s3Client wireTypes.CaseReprioritizationS3Client,
	distributedLockManager lock.StatelessLockManager,
	prioritizationFactory prioritisation.Factory,
	alertWithRuleManager alert_with_rule.AlertWithRuleManager,
	caseStore casestore.CaseStore,
	alertDao dao2.AlertDao,
) *Processor {
	return &Processor{
		conf:                          conf,
		riskClient:                    riskClient,
		riskBankActionDao:             riskBankActionDao,
		savingsClient:                 savingsClient,
		userClient:                    userClient,
		actorClient:                   actorClient,
		authClient:                    authClient,
		accountStatusFetcher:          accountStatusFetcher,
		infoBuilder:                   infoBuilder,
		leaRemark:                     leaRemark,
		leaComplaintManager:           leaComplaintManager,
		cacheStorage:                  cacheStorage,
		bankActionCommsBuilderFactory: bankActionCommsBuilderFactory,
		refine:                        refine,
		caseManagementClient:          caseManagementClient,
		collateComms:                  collateComms,
		lienManager:                   lienManager,
		freshdeskClient:               freshdeskClient,
		crmClient:                     crmClient,
		s3Client:                      s3Client,
		distributedLockManager:        distributedLockManager,
		prioritizationFactory:         prioritizationFactory,
		alertWithRuleManager:          alertWithRuleManager,
		caseStore:                     caseStore,
		alertDao:                      alertDao,
	}
}
