package activity_test

import (
	"os"
	"testing"

	mockCache "github.com/epifi/be-common/pkg/cache/mocks"
	mockLock "github.com/epifi/be-common/pkg/lock/mocks"
	"github.com/epifi/be-common/pkg/logger"

	mockS3 "github.com/epifi/be-common/pkg/aws/v2/s3/mocks"

	epifitemporalLogging "github.com/epifi/be-common/pkg/epifitemporal/logging"
	epifitemporalTest "github.com/epifi/be-common/pkg/epifitemporal/test"

	actorMocks "github.com/epifi/gamma/api/actor/mocks"
	authMocks "github.com/epifi/gamma/api/auth/mocks"
	mockCasemanagement "github.com/epifi/gamma/api/risk/case_management/mocks"
	riskMocks "github.com/epifi/gamma/api/risk/mocks"
	"github.com/epifi/gamma/api/savings/mocks"
	userMocks "github.com/epifi/gamma/api/user/mocks"
	crmMocks "github.com/epifi/gamma/api/vendorgateway/crm/mocks"
	freshdeskMocks "github.com/epifi/gamma/api/vendorgateway/cx/freshdesk/mocks"
	"github.com/epifi/gamma/risk/activity"
	bankActionCommsMocks "github.com/epifi/gamma/risk/bankactions/comms/mocks"
	casestoreMocks "github.com/epifi/gamma/risk/case_management/casestore/mocks"
	alertWithRuleMocks "github.com/epifi/gamma/risk/case_management/essential/alert_with_rule/mocks"
	prioritizationMocks "github.com/epifi/gamma/risk/case_management/prioritisation/mocks"
	riskWorkerConfig "github.com/epifi/gamma/risk/config/worker"
	daoMocks "github.com/epifi/gamma/risk/dao/mocks"
	mockLea "github.com/epifi/gamma/risk/lea/mocks"
	remarkMocks "github.com/epifi/gamma/risk/lea/remark/mocks"
	mockComms "github.com/epifi/gamma/risk/lea/unified_lea/comms/mocks"
	mockLien "github.com/epifi/gamma/risk/lien/mocks"
	"github.com/epifi/gamma/risk/test"

	cmDaoMocks "github.com/epifi/gamma/risk/case_management/dao/mocks"

	mock_accountstatus "github.com/epifi/gamma/risk/test/mocks/accountstatus"

	"github.com/golang/mock/gomock"
)

var (
	wts  epifitemporalTest.WorkflowTestSuite
	conf *riskWorkerConfig.Config
)

type mockedDependencies struct {
	riskBankActionDao      *daoMocks.MockRiskBankActionsDao
	savingsClient          *mocks.MockSavingsClient
	actorClient            *actorMocks.MockActorClient
	userClient             *userMocks.MockUsersClient
	authClient             *authMocks.MockAuthClient
	accountStatusFetcher   *mock_accountstatus.MockFetcher
	riskClient             *riskMocks.MockRiskClient
	infoBuilder            *remarkMocks.MockInfoBuilder
	leaRemarks             *remarkMocks.MockRemarks
	leaComplaintManager    *mockLea.MockIComplaintManager
	cacheStorage           *mockCache.MockCacheStorage
	builderFactory         *bankActionCommsMocks.MockBuilderFactory
	builder                *bankActionCommsMocks.MockBuilder
	refine                 *mockComms.MockRefine
	caseManagementClient   *mockCasemanagement.MockCaseManagementClient
	collateComms           *mockComms.MockCollateComms
	mockLienManager        *mockLien.MockLienManager
	freshdeskClient        *freshdeskMocks.MockFreshdeskClient
	crmClient              *crmMocks.MockCRMClient
	s3Client               *mockS3.MockS3Client
	distributedLockManager *mockLock.MockStatelessLockManager
	// Added new mocks for GetConfidenceScore activity
	prioritizationFactory *prioritizationMocks.MockFactory
	alertWithRuleManager  *alertWithRuleMocks.MockAlertWithRuleManager
	caseStore             *casestoreMocks.MockCaseStore
	mockAlertsDao         *cmDaoMocks.MockAlertDao
}

func newProcessorWithMocks(t *testing.T) (*activity.Processor, *mockedDependencies, func()) {
	ctr := gomock.NewController(t)
	mockRiskClient := riskMocks.NewMockRiskClient(ctr)
	mockRiskBankActionDao := daoMocks.NewMockRiskBankActionsDao(ctr)
	mockSavingsClient := mocks.NewMockSavingsClient(ctr)
	mockActorClient := actorMocks.NewMockActorClient(ctr)
	mockUserClient := userMocks.NewMockUsersClient(ctr)
	mockAuthClient := authMocks.NewMockAuthClient(ctr)
	mockAccountStatusFetcher := mock_accountstatus.NewMockFetcher(ctr)
	mockInfoBuilder := remarkMocks.NewMockInfoBuilder(ctr)
	mockRemarks := remarkMocks.NewMockRemarks(ctr)
	mockLEAComplaintManager := mockLea.NewMockIComplaintManager(ctr)
	mockCacheStorage := mockCache.NewMockCacheStorage(ctr)
	mockBuilderFactory := bankActionCommsMocks.NewMockBuilderFactory(ctr)
	mockBuilder := bankActionCommsMocks.NewMockBuilder(ctr)
	mockRefine := mockComms.NewMockRefine(ctr)
	caseManagementClient := mockCasemanagement.NewMockCaseManagementClient(ctr)
	mockCollateComms := mockComms.NewMockCollateComms(ctr)
	mockLienManager := mockLien.NewMockLienManager(ctr)
	mockFreshdeskClient := freshdeskMocks.NewMockFreshdeskClient(ctr)
	mockCrmClient := crmMocks.NewMockCRMClient(ctr)
	mockS3Client := mockS3.NewMockS3Client(ctr)
	mockDistributedLockManager := mockLock.NewMockStatelessLockManager(ctr)
	// Create new mocks for GetConfidenceScore activity
	mockPrioritizationFactory := prioritizationMocks.NewMockFactory(ctr)
	mockAlertWithRuleManager := alertWithRuleMocks.NewMockAlertWithRuleManager(ctr)
	mockCaseStore := casestoreMocks.NewMockCaseStore(ctr)
	mockAlertsDao := cmDaoMocks.NewMockAlertDao(ctr)

	md := &mockedDependencies{
		riskBankActionDao:      mockRiskBankActionDao,
		savingsClient:          mockSavingsClient,
		actorClient:            mockActorClient,
		userClient:             mockUserClient,
		authClient:             mockAuthClient,
		accountStatusFetcher:   mockAccountStatusFetcher,
		infoBuilder:            mockInfoBuilder,
		riskClient:             mockRiskClient,
		leaRemarks:             mockRemarks,
		leaComplaintManager:    mockLEAComplaintManager,
		cacheStorage:           mockCacheStorage,
		builderFactory:         mockBuilderFactory,
		builder:                mockBuilder,
		refine:                 mockRefine,
		caseManagementClient:   caseManagementClient,
		collateComms:           mockCollateComms,
		mockLienManager:        mockLienManager,
		freshdeskClient:        mockFreshdeskClient,
		crmClient:              mockCrmClient,
		s3Client:               mockS3Client,
		distributedLockManager: mockDistributedLockManager,
		prioritizationFactory:  mockPrioritizationFactory,
		alertWithRuleManager:   mockAlertWithRuleManager,
		caseStore:              mockCaseStore,
		mockAlertsDao:          mockAlertsDao,
	}
	act := activity.NewProcessor(mockRiskBankActionDao, mockSavingsClient, mockUserClient, mockActorClient,
		mockAuthClient, conf, mockAccountStatusFetcher, mockInfoBuilder, mockRemarks, mockRiskClient,
		mockLEAComplaintManager, mockCacheStorage, mockBuilderFactory, mockRefine, caseManagementClient,
		mockCollateComms, mockLienManager, mockFreshdeskClient, mockCrmClient, mockS3Client,
		mockDistributedLockManager, mockPrioritizationFactory, mockAlertWithRuleManager, mockCaseStore, mockAlertsDao)

	return act, md, func() {
		ctr.Finish()
	}
}

func TestMain(m *testing.M) {
	var teardown func()
	conf, teardown = test.InitTestWorkerWithoutDB()
	wts.SetLogger(epifitemporalLogging.NewZapAdapter(logger.Log))
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
