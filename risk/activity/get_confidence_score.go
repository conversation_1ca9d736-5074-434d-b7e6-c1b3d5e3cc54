package activity

import (
	"context"
	"fmt"

	caseManagementPb "github.com/epifi/gamma/api/risk/case_management"
	activityPb "github.com/epifi/gamma/api/risk/case_management/activity"
	prioritizationPb "github.com/epifi/gamma/api/risk/case_management/prioritization"
	"github.com/epifi/gamma/api/vendorgateway/risk"
)

// GetConfidenceScore gets scores from DS model only for a ticket using the prioritization factory
func (p *Processor) GetConfidenceScore(ctx context.Context, req *activityPb.GetConfidenceScoreRequest) (*activityPb.GetConfidenceScoreResponse, error) {
	// Step 1: Get actor ID from ticket ID (treating ticket ID as case ID)
	actorId, err := p.getActorIdFromTicketId(ctx, req.GetCaseId())
	if err != nil {
		return &activityPb.GetConfidenceScoreResponse{
			Success:      false,
			ErrorMessage: fmt.Sprintf("failed to get actor ID from ticket ID: %v", err),
		}, nil
	}

	// Step 2: Get alerts with rules for the actor
	alerts, err := p.getAlertsWithRules(ctx, actorId)
	if err != nil {
		return &activityPb.GetConfidenceScoreResponse{
			Success:      false,
			ErrorMessage: fmt.Sprintf("failed to get alerts with rules: %v", err),
		}, nil
	}

	// Step 3: Get DS model scores only
	var caseAlerts []*caseManagementPb.AlertWithRuleDetails
	// Get all the alerts which are associated with the case
	for _, alert := range alerts {
		if alert.GetAlert().GetCaseId() == req.GetCaseId() {
			caseAlerts = append(caseAlerts, alert)
		}
	}
	caseDetails := risk.GetCasePrioritisationScoreRequest_CaseDetails{
		CaseId:               req.GetCaseId(),
		AlertWithRuleDetails: caseAlerts,
	}
	dsModelScores, err := p.getDSModelScores(ctx, actorId, alerts, &caseDetails)
	if err != nil {
		return &activityPb.GetConfidenceScoreResponse{
			Success:      false,
			ErrorMessage: fmt.Sprintf("failed to get DS model scores: %v", err),
		}, nil
	}

	// Step 4: Return DS model scores as model1 and model2 (first and second elements)
	response := &activityPb.GetConfidenceScoreResponse{
		Success: true,
	}

	if len(dsModelScores) > 0 {
		response.Model1Name = dsModelScores[0].GetName()
		response.Model1Score = dsModelScores[0].GetScore()
	}
	if len(dsModelScores) > 1 {
		response.Model2Name = dsModelScores[1].GetName()
		response.Model2Score = dsModelScores[1].GetScore()
	}

	return response, nil
}

// getActorIdFromTicketId maps ticket ID to actor ID using case store
func (p *Processor) getActorIdFromTicketId(ctx context.Context, ticketId string) (string, error) {
	// Get alerts by case ID (ticket ID is same as case ID)
	alerts, err := p.alertDao.GetByCaseId(ctx, ticketId)
	if err != nil {
		return "", fmt.Errorf("failed to get alert by case ID: %w", err)
	}
	var actorId string
	for _, alert := range alerts {
		if alert.GetActorId() != "" {
			actorId = alert.GetActorId()
			return actorId, nil
		}
	}
	return "", fmt.Errorf("failed to get actor from alerts: %w", err)
}

// getAlertsWithRules gets alerts with rules for an actor using the alertWithRuleManager pattern
func (p *Processor) getAlertsWithRules(ctx context.Context, actorId string) ([]*caseManagementPb.AlertWithRuleDetails, error) {
	// Create filters for last 15 days of alerts
	filters := []caseManagementPb.AlertFieldMask{caseManagementPb.AlertFieldMask_ALERT_FIELD_MASK_ALL}

	// Use alertWithRuleManager to get alerts with rules
	alerts, err := p.alertWithRuleManager.GetByActorId(ctx, actorId, filters, 0)
	if err != nil {
		return nil, fmt.Errorf("failed to get alerts with rules: %w", err)
	}

	return alerts, nil
}

// getDSModelScores gets scores from DS model only using prioritization factory
func (p *Processor) getDSModelScores(ctx context.Context, actorId string, alerts []*caseManagementPb.AlertWithRuleDetails, caseDetails *risk.GetCasePrioritisationScoreRequest_CaseDetails) ([]*risk.ModelResponseInfo, error) {
	// Call prioritization factory to get DS model scores only
	model, modelParams, err := p.prioritizationFactory.GetPrioritizationModel(
		ctx,
		prioritizationPb.PrioritizationModelType_PRIORITIZATION_MODEL_TYPE_DS_PRECISION,
		actorId,
		alerts,
		caseDetails,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get DS prioritization model: %w", err)
	}

	// Calculate DS model scores
	dsModelScores, _, err := model.GetConfidenceScore(ctx, modelParams)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate DS model scores: %w", err)
	}

	return dsModelScores, nil
}
