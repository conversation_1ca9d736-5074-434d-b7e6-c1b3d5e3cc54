package home_trending

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/async/waitgroup"

	"context"
	"math/rand"
	"strings"
	"sync"
	"time"

	"github.com/google/wire"

	"github.com/epifi/be-common/pkg/colors"

	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/networth"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/feature/release"
	"github.com/epifi/gamma/search/constant"
	"github.com/epifi/gamma/search/dao/parser"
	"github.com/epifi/gamma/search/events"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/casper"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/search/experiments"
	"github.com/epifi/gamma/api/frontend/search/meta"
	"github.com/epifi/gamma/api/merchant"
	"github.com/epifi/gamma/search/common"
	"github.com/epifi/gamma/search/dao"
	"github.com/epifi/gamma/search/dao/home_trending"
	daoIndex "github.com/epifi/gamma/search/dao/index"
	"github.com/epifi/gamma/search/elasticsearch"
)

var WireHomeTrendingChipsProcessor = wire.NewSet(NewHomeTrendingQueriesImpl, wire.Bind(new(Processor), new(*HomeTrendingQueriesImpl)))

const (
	askFiHomeMagnifier = "https://epifi-icons.pointz.in/quick-link-icons/askfi_home_search.png"
	networthIconUrl    = "https://epifi-icons.pointz.in/search/quick-links/networth.png"
	maxTopicsThreshold = 3
	maxFACount         = 50 // the maximum number of txn to be used

	// minFrequencyThreshold is the minimum frequency that a merchant should be
	// repeated in order for it to be considered for scoring algorithm
	minFrequencyThreshold = 1

	// maxTopicsThresholdForMerchants defines the maximum number of topics to be
	// considered after scoring algorithm
	maxTopicsThresholdForMerchants = 3
)

var (
	displayCategoryToCasperTag = map[string][]casper.TagName{
		"FOOD DRINKS GROCERIES": {casper.TagName_FOOD_AND_DINING},
		"FOOD DRINKS":           {casper.TagName_FOOD_AND_DINING},
		"FOOD ORDERS":           {casper.TagName_FOOD_AND_DINING},
		"ENTERTAINMENT":         {casper.TagName_ENTERTAINMENT},
		"TRAVEL":                {casper.TagName_TRAVEL, casper.TagName_FOOD_AND_DINING},
		"TRAVEL COMMUTE":        {casper.TagName_TRAVEL, casper.TagName_FOOD_AND_DINING},
		"TRAVEL VACATION":       {casper.TagName_TRAVEL, casper.TagName_FOOD_AND_DINING},
		"TRAVEL TRANSPORTATION": {casper.TagName_TRAVEL, casper.TagName_FOOD_AND_DINING},
		"PERSONAL CARE":         {casper.TagName_HEALTH},
		"GROCERIES ESSENTIALS":  {casper.TagName_GROCERY},
		"GROCERIES":             {casper.TagName_GROCERY},
		"GADGETS":               {casper.TagName_ELECTRONICS},
		"GADGET":                {casper.TagName_ELECTRONICS},
		"FASHION":               {casper.TagName_LIFESTYLE, casper.TagName_TRAVEL},
		"CLOTHING ACCESSORIES":  {casper.TagName_LIFESTYLE, casper.TagName_TRAVEL},
	}
)

type Processor interface {
	BuildHomeTrendingChips(ctx context.Context, actorID string, variant experiments.HomeSearchBarChipType) (homeTrendingQueries []*home_trending.HomeTrendingQuery, err error)
}

type HomeTrendingQueriesImpl struct {
	txnSearcher         elasticsearch.TxnRepoSearcherI
	txnOntologySearcher elasticsearch.TxnOntologiesIndexSearcherI
	merchantClient      merchant.MerchantServiceClient
	casperClient        casper.OfferListingServiceClient
	releaseEvaluator    release.IEvaluator
}

func NewHomeTrendingQueriesImpl(
	merchantClient merchant.MerchantServiceClient,
	txnOntologySearcher elasticsearch.TxnOntologiesIndexSearcherI,
	txnSearcher elasticsearch.TxnRepoSearcherI,
	casperClient casper.OfferListingServiceClient,
	releaseEvaluator release.IEvaluator,
) *HomeTrendingQueriesImpl {
	return &HomeTrendingQueriesImpl{
		txnSearcher:         txnSearcher,
		txnOntologySearcher: txnOntologySearcher,
		merchantClient:      merchantClient,
		casperClient:        casperClient,
		releaseEvaluator:    releaseEvaluator,
	}
}

func (h *HomeTrendingQueriesImpl) BuildHomeTrendingChips(ctx context.Context, actorID string, variant experiments.HomeSearchBarChipType) (homeTrendingQueries []*home_trending.HomeTrendingQuery, err error) {
	return h.buildPersonalizedChips(ctx, actorID)
}

func (h *HomeTrendingQueriesImpl) getFoodSpendsChip() *home_trending.HomeTrendingQuery {
	return &home_trending.HomeTrendingQuery{
		DisplayQuery:   "food spends",
		DisplayIconURL: askFiHomeMagnifier,
		Deeplink: buildHomeChipDeeplink("food spends", []*meta.TransactionFilter{
			{
				FilterTypes:       []meta.FilterType{meta.FilterType_CATEGORY},
				DisplayCategories: []string{"Food & Drinks"},
				QueryIntent:       constant.IntentTransactionsDetails,
				TxnDirection:      constant.TxnDirectionAll,
			},
		}),
	}
}

func (h *HomeTrendingQueriesImpl) getIfscChip() *home_trending.HomeTrendingQuery {
	return &home_trending.HomeTrendingQuery{
		DisplayQuery:   "ifsc",
		DisplayIconURL: askFiHomeMagnifier,
		Deeplink:       buildHomeChipDeeplink("ifsc", nil),
	}
}

func (h *HomeTrendingQueriesImpl) getMySpendsChip() *home_trending.HomeTrendingQuery {
	return &home_trending.HomeTrendingQuery{
		DisplayQuery:   "my spends",
		DisplayIconURL: askFiHomeMagnifier,
		Deeplink:       buildHomeChipDeeplink("my spends", nil),
	}
}

func (h *HomeTrendingQueriesImpl) getForm16Chip() *home_trending.HomeTrendingQuery {
	return &home_trending.HomeTrendingQuery{
		DisplayQuery:   "what is form 16",
		DisplayIconURL: askFiHomeMagnifier,
		Deeplink:       buildHomeChipDeeplink("what is form 16", nil),
	}
}

func (h *HomeTrendingQueriesImpl) getNetWorthChip(ctx context.Context, actorId string) (chip *home_trending.HomeTrendingQuery, isReleased bool) {
	isNetWorthReleased, err := h.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_NETWORTH_DASHBOARD).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "failed to evaluate release config for networth dashboard")
		return nil, false
	}
	if !isNetWorthReleased {
		return nil, false
	}
	return &home_trending.HomeTrendingQuery{
		DisplayQuery:   "Track Investments",
		DisplayIconURL: networthIconUrl,
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_NET_WORTH_HUB_SCREEN,
			ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&networth.NetWorthHubScreenOptions{
				LoadingNessage: &commontypes.Text{
					FontColor: colors.ColorOnDarkHighEmphasis,
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: "Taking you to Net Worth",
					},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_HEADLINE_L,
					},
				},
			}),
		},
	}, true
}

// buildPersonalizedChips builds a list of personalized home trending queries based on the user's transaction history.
// It first fetches the user's past debit transactions from the Elasticsearch
// If the fetch fails, it logs the error and continues with the function.
// It then calls the getCategoryChip and getMerchantChip functions in separate goroutines to build personalized category and merchant chips.
// If either of these calls fail, it logs the error and skips adding the corresponding chip to the list.
// The function also adds fixed chips for IFSC lookup, Form 16, and MySpends to the list.
// It returns the list of personalized home trending queries and any error that occurs during the function.
func (h *HomeTrendingQueriesImpl) buildPersonalizedChips(ctx context.Context, actorID string) ([]*home_trending.HomeTrendingQuery, error) {
	var homeTrendingQueries []*home_trending.HomeTrendingQuery
	// show networth chip at the first position
	networthChip, isNetworthReleased := h.getNetWorthChip(ctx, actorID)
	if isNetworthReleased {
		homeTrendingQueries = append(homeTrendingQueries, networthChip)
	}

	esResult, err := h.txnSearcher.FetchPastNDebitTxnsFromActorID(ctx, actorID, maxFACount)
	if err != nil {
		logger.Error(ctx, "error while fetching past transactions from ES", zap.Error(err))
	}

	lastNTxns := common.BuildTxnInfoFromEsResp(ctx, esResult)

	if len(lastNTxns) != 0 && err == nil {
		chipsChans := make([]chan *home_trending.HomeTrendingQuery, 3)
		var wg sync.WaitGroup
		wg.Add(3)
		// Create separate channels for each goroutine
		for i := 0; i < 3; i++ {
			chipsChans[i] = make(chan *home_trending.HomeTrendingQuery, 1)
		}

		goroutine.Run(ctx, time.Millisecond*300, func(ctx context.Context) {
			defer wg.Done()
			chip := h.getMerchantChip(ctx, lastNTxns)
			if chip == nil {
				chip = h.getMerchantSpendsFallbackChip(lastNTxns)
			}
			chipsChans[0] <- chip
		})

		goroutine.Run(ctx, time.Millisecond*300, func(ctx context.Context) {
			defer wg.Done()
			// chip := h.getMerchantOffers(ctx, actorID, lastNTxns)
			// if chip == nil {
			//	chipsChans[1] <- nil
			//	return
			// }
			chipsChans[1] <- nil
		})

		goroutine.Run(ctx, time.Millisecond*300, func(ctx context.Context) {
			defer wg.Done()
			chip := h.getCategoryChip(ctx, lastNTxns)
			// fallback for category spends chip
			if chip == nil {
				chip = h.getCategorySpendsFallbackChip()
			}
			chipsChans[2] <- chip
		})

		waitgroup.SafeWaitWithDefaultTimeout(&wg)

		// Collect the chips from channels in the correct order
		for _, chipsChan := range chipsChans {
			chip := <-chipsChan
			if chip != nil {
				homeTrendingQueries = append(homeTrendingQueries, chip)
			}
			close(chipsChan)
		}
	}

	homeTrendingQueries = append(homeTrendingQueries, []*home_trending.HomeTrendingQuery{
		h.getForm16Chip(),
	}...)

	if len(lastNTxns) > 0 {
		latestTxnTimestamp := lastNTxns[0].UpdatedAt
		createdAtTime, err := time.Parse(parser.ESTimeFormat, latestTxnTimestamp)
		if err != nil {
			logger.Error(ctx, "error in parsing createdAtTime")
			homeTrendingQueries = append(homeTrendingQueries, h.getMySpendsChip())
		} else {
			today := time.Now().Weekday()
			if today == time.Saturday && IsWithinCurrentWeek(time.Now(), createdAtTime) {
				homeTrendingQueries = append(homeTrendingQueries, h.getSpendsThisWeekChip())
			}
		}
	} else {
		homeTrendingQueries = append(homeTrendingQueries, h.getMySpendsChip())
	}

	return homeTrendingQueries, nil
}

func IsWithinCurrentWeek(now time.Time, t time.Time) bool {
	startOfWeek := now.AddDate(0, 0, -int(now.Weekday()))
	endOfWeek := startOfWeek.AddDate(0, 0, 7)
	return (t.Equal(startOfWeek) || t.After(startOfWeek)) && t.Before(endOfWeek)
}

func (h *HomeTrendingQueriesImpl) getSpendsThisWeekChip() *home_trending.HomeTrendingQuery {
	return &home_trending.HomeTrendingQuery{
		DisplayQuery:   "spends this week",
		DisplayIconURL: askFiHomeMagnifier,
		Deeplink:       buildHomeChipDeeplink("spends this week", nil),
	}
}

func (h *HomeTrendingQueriesImpl) getCategorySpendsFallbackChip() *home_trending.HomeTrendingQuery {
	return &home_trending.HomeTrendingQuery{
		DisplayQuery:   "invest",
		DisplayIconURL: askFiHomeMagnifier,
		Deeplink:       buildHomeChipDeeplink("invest", nil),
	}
}

func (h *HomeTrendingQueriesImpl) getMerchantSpendsFallbackChip(lastNTxns []daoIndex.Transaction) *home_trending.HomeTrendingQuery {
	accDetailsAskFiQueries := []string{"upi id", "my account details", "ifsc code"}
	if len(lastNTxns) > 0 {
		accDetailsAskFiQueries = append(accDetailsAskFiQueries, "my spends")
	}
	rand.Seed(time.Now().UnixNano())
	randIdx := rand.Intn(len(accDetailsAskFiQueries))
	// add one more merchant spends
	return &home_trending.HomeTrendingQuery{
		DisplayQuery:   accDetailsAskFiQueries[randIdx],
		DisplayIconURL: askFiHomeMagnifier,

		Deeplink: buildHomeChipDeeplink(accDetailsAskFiQueries[randIdx], nil),
	}
}

func (h *HomeTrendingQueriesImpl) getMerchantOffers(ctx context.Context, actorID string, lastNTxns []daoIndex.Transaction) *home_trending.HomeTrendingQuery {
	topicsWithScores := common.GetCategoryTopicWithScoreFromTransactions(lastNTxns, maxTopicsThreshold)
	if topicsWithScores == nil || len(topicsWithScores) == 0 {
		return nil
	}
	score := topicsWithScores[common.GetRandomIndex(len(topicsWithScores))]
	displayCats, err := h.txnOntologySearcher.GetDisplayCategoryFromOntologyIds(ctx, []string{score.CategoryID})
	if err != nil {
		logger.Error(ctx, "Error while fetching display category from ontology ID", zap.Error(err))
		return nil
	}
	if displayCats == nil || len(displayCats) == 0 {
		return nil
	}

	var tags []casper.TagName
	for _, eachDisplayCat := range displayCats {
		displayCatKey := strings.ToUpper(eachDisplayCat)
		tags = append(tags, displayCategoryToCasperTag[displayCatKey]...)
	}

	// get mapping to casper tags from display category
	getOffersResp, getOffersErr := h.casperClient.GetOffers(ctx, &casper.GetOffersRequest{
		RedemptionMode: casper.OfferRedemptionMode_FI_CARD,
		ActorId:        actorID,
		Filters: &casper.CatalogFilters{
			Tags: tags,
		},
	})
	if rpcErr := epifigrpc.RPCError(getOffersResp, getOffersErr); rpcErr != nil {
		if getOffersResp.GetStatus().IsRecordNotFound() {
			logger.WarnWithCtx(ctx, "no active offers found", zap.String("redemptionMode", casper.OfferRedemptionMode_FI_CARD.String()), zap.String(logger.ACTOR_ID_V2, actorID))
			return nil
		}
		logger.Error(ctx, "error while getting offers from casper client", zap.Error(rpcErr))
		return nil
	}
	merchantName := ""
	if len(getOffersResp.GetOffers()) > 0 {
		merchantName = getRandomMerchantName(getOffersResp.GetOffers())
		return &home_trending.HomeTrendingQuery{
			DisplayQuery:   common.CapitaliseFirstCharOfString(merchantName) + " " + "offers",
			DisplayIconURL: askFiHomeMagnifier,
			Deeplink:       buildHomeChipDeeplink(merchantName+" offers", nil),
		}
	}
	return nil
}

func getRandomMerchantName(merchantOffers []*casper.Offer) string {
	return merchantOffers[common.GetRandomIndex(len(merchantOffers))].GetAdditionalDetails().GetBrandName()
}

func (h *HomeTrendingQueriesImpl) getCategoryChip(ctx context.Context, lastNTxns []daoIndex.Transaction) *home_trending.HomeTrendingQuery {
	topicsWithScores := common.GetCategoryTopicWithScoreFromTransactions(lastNTxns, maxTopicsThreshold)
	if topicsWithScores == nil || len(topicsWithScores) == 0 {
		return nil
	}
	score := topicsWithScores[common.GetRandomIndex(len(topicsWithScores))]
	displayCats, err := h.txnOntologySearcher.GetDisplayCategoryFromOntologyIds(ctx, []string{score.CategoryID})
	if err != nil {
		logger.Error(ctx, "Error while fetching display category from ontology ID", zap.Error(err))
		return nil
	}
	if displayCats == nil || len(displayCats) == 0 {
		return nil
	}
	if categoryName, ok := dao.EsCategoryToDisplayCat[strings.ToUpper(displayCats[0])]; ok {
		return &home_trending.HomeTrendingQuery{
			DisplayQuery:   strings.ToLower(getCategoryDisplayQuery(categoryName, "Spends")),
			DisplayIconURL: askFiHomeMagnifier,
			Deeplink: buildHomeChipDeeplink(categoryName+" transactions", []*meta.TransactionFilter{
				{
					FilterTypes:       []meta.FilterType{meta.FilterType_CATEGORY},
					DisplayCategories: []string{categoryName},
					QueryIntent:       constant.IntentTransactionsDetails,
					TxnDirection:      constant.TxnDirectionAll,
				},
			}),
		}
	}
	return nil
}

func getCategoryDisplayQuery(categoryName string, suffix string) string {
	synonyms := map[string]string{
		"Books & Publications": "books spends",
		"Cash":                 "cash withdrawal",
		"Credit Card & Loan":   "Credit Card & Loan",
		"Deposits":             "deposit transactions",
		"Food & Drinks":        "food spends",
		"Groceries":            "grocery spends",
		"Housing & Bills":      "housing spends",
		"Money Transfer":       "money transfers",
		"Self Transfer":        "self transfers",
		"Sports & Games":       "sports spends",
		"Travel & Vacation":    "travel spends",
		"Wallet & Payment":     "wallet spends",
	}
	if synonym, ok := synonyms[categoryName]; ok {
		return synonym
	}
	return common.CapitaliseFirstCharOfString(categoryName) + " " + suffix
}

func (h *HomeTrendingQueriesImpl) getMerchantChip(ctx context.Context, lastNTxns []daoIndex.Transaction) *home_trending.HomeTrendingQuery {
	var actorIDs []string
	for _, eachTxn := range lastNTxns {
		actorIDs = append(actorIDs, eachTxn.PayeeActorId)
	}
	// Removes all duplicates and removes whose frequency is less than minFrequencyThreshold
	uniqueActorIDs := common.FilterByMinFrequencyThreshold(actorIDs, minFrequencyThreshold)

	merchantsResp, getMerchantsErr := h.merchantClient.GetKnownMerchantByActorIds(ctx, &merchant.GetKnownMerchantByActorIdsRequest{ActorIds: uniqueActorIDs})
	if _err := epifigrpc.RPCError(merchantsResp, getMerchantsErr); _err != nil {
		logger.Error(ctx, "Error while fetching merchants from GetMerchants", zap.Error(_err))
		return nil
	}
	merchantIDtoMerchantMap := merchantsResp.GetActorsMerchantMap()
	topicsWithScores := common.GetMerchantTopicWithScoreFromTransactions(
		common.FilterNonMerchantsFromTxns(lastNTxns, merchantIDtoMerchantMap),
		maxTopicsThresholdForMerchants)
	if topicsWithScores == nil || len(topicsWithScores) == 0 {
		return nil
	}
	// following line picks a random among top maxTopicsThresholdForMerchants merchants
	score := topicsWithScores[common.GetRandomIndex(len(topicsWithScores))]
	merchantName := strings.ToLower(merchantIDtoMerchantMap[score.MerchantID].String())
	merchantName = common.CleanupMerchantName(merchantName)
	if len(merchantName) == 0 {
		return nil
	}

	return &home_trending.HomeTrendingQuery{
		DisplayQuery:   common.CapitaliseFirstCharOfString(merchantName) + " " + "spends",
		DisplayIconURL: askFiHomeMagnifier,
		Deeplink: buildHomeChipDeeplink(merchantName+" transactions", []*meta.TransactionFilter{
			{
				FilterTypes: []meta.FilterType{meta.FilterType_MERCHANT},
				SecondaryActorNames: []string{
					merchantName,
				},
				QueryIntent:  constant.IntentTransactionsDetails,
				TxnDirection: constant.TxnDirectionAll,
			},
		}),
	}
}

func buildHomeChipDeeplink(query string, transactionFilters []*meta.TransactionFilter) *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_ASK_FI_LANDING_PAGE,
		ScreenOptions: &deeplink.Deeplink_AskFiLandingPageOptions{
			AskFiLandingPageOptions: &deeplink.AskFiLandingPageOptions{
				Query:              query,
				TransactionFilters: transactionFilters,
				Source:             events.Source_HomeChips,
			},
		},
	}
}
