// nolint:depguard
package skill_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/ptypes/timestamp"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"
	accountsPb "github.com/epifi/gamma/api/accounts"
	depositPb "github.com/epifi/gamma/api/deposit"
	mocks2 "github.com/epifi/gamma/api/deposit/mocks"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/search/widget"
	searchPb "github.com/epifi/gamma/api/search"
	searchEnumsPb "github.com/epifi/gamma/api/search/enums"
	skillPb "github.com/epifi/gamma/api/search/skill"
	"github.com/epifi/gamma/api/search/summary"
	"github.com/epifi/gamma/pkg/onboarding"
	"github.com/epifi/gamma/search/common"
	"github.com/epifi/gamma/search/config"
	"github.com/epifi/gamma/search/constant"
	"github.com/epifi/gamma/search/dao/search_auth"
	"github.com/epifi/gamma/search/dao/summary_v2"
	"github.com/epifi/gamma/search/entity"
	"github.com/epifi/gamma/search/skill"
)

func TestDepositSkill_BuildSearchResult(t *testing.T) {
	logger.Init("test")
	type fields struct {
		configFetcher                    skill.SkillConfigGet
		MinCompatibleVersionForSummaryV2 *config.MinVersionForSummaryV2
	}
	type mockListDepositAccounts struct {
		resp *depositPb.ListDepositAccountsResponse
		err  error
	}
	tests := []struct {
		name                          string
		fields                        fields
		mockedListDepositAccountsResp *mockListDepositAccounts
		want                          []*searchPb.SearchResultUnit
	}{
		{
			name: "Deposit Skill test",
			fields: fields{
				configFetcher: skillConfigFetcher,
			},
			mockedListDepositAccountsResp: &mockListDepositAccounts{
				resp: &depositPb.ListDepositAccountsResponse{
					Status: rpc.StatusOk(),
					Accounts: []*depositPb.DepositAccount{
						{
							Type: accountsPb.Type_FIXED_DEPOSIT,
							DepositIcon: &commontypes.Image{
								ImageUrl: "IconUrl",
							},
							Name:          "TestFixedDeposit",
							AccountNumber: "************",
							Id:            "FixedDepositId",
							MaturityDate: &timestamp.Timestamp{
								Seconds: ************,
							},
							PrincipalAmount: &moneyPb.Money{
								CurrencyCode: "INR",
								Units:        10000,
								Nanos:        0,
							},
						},
						{
							Type: accountsPb.Type_SMART_DEPOSIT,
							DepositIcon: &commontypes.Image{
								ImageUrl: "IconUrl",
							},
							Name:          "TestSmartDeposit",
							AccountNumber: "************",
							Id:            "SmartDepositId",
							MaturityDate: &timestamp.Timestamp{
								Seconds: ************,
							},
							PrincipalAmount: &moneyPb.Money{
								CurrencyCode: "INR",
								Units:        10000,
								Nanos:        0,
							},
						},
					},
				},
				err: nil,
			},
			want: []*searchPb.SearchResultUnit{
				{
					RespType: searchEnumsPb.ResponseType_SUMMARY_V2,
					Data: &searchPb.SearchResultUnit_SummaryV2Response{
						SummaryV2Response: &summary.SummaryV2{
							CardTitle: "SMART DEPOSIT",
							SummaryRows: []*widget.SummaryRow{
								{
									ChatheadImageContent: &widget.SummaryRow_ImgUrl{
										ImgUrl: "IconUrl",
									},
									Heading:      "TestSmartDeposit",
									SubHeading_1: "************",
									SubHeading_2: "MATURES ON 19 NOV, 5170",
									SummaryRowRightElement: &widget.SummaryRowRightElement{
										Text: "₹10,000.00",
									},
									RowPadding: common.SummaryNonHeadingRowPadding,
								},
							},
							DeepLinkElements: []*widget.DeepLinkElement{
								{
									Link: &deeplink.Deeplink{
										Screen: deeplink.Screen_DEPOSIT_ACCOUNT_DETAILS,
										ScreenOptions: &deeplink.Deeplink_DepositDetailsScreenOptions{
											DepositDetailsScreenOptions: &deeplink.DepositAccountDetailsScreenOptions{
												AccountId:   "SmartDepositId",
												DepositType: accountsPb.Type_SMART_DEPOSIT,
											},
										},
									},
									Text:     summary_v2.SeeDetails,
									CtaTheme: widget.CtaDisplayTheme_PRIMARY,
									CtaType:  widget.CTAType_DEEPLINK,
								},
							},
							TabName: constant.FiTabName,
						},
					},
					WidgetDisplay: searchEnumsPb.WidgetDisplay_FI_TXNS,
					TabName:       "Fi",
				},
				{
					RespType: searchEnumsPb.ResponseType_SUMMARY_V2,
					Data: &searchPb.SearchResultUnit_SummaryV2Response{
						SummaryV2Response: &summary.SummaryV2{
							CardTitle: "FIXED DEPOSIT",
							SummaryRows: []*widget.SummaryRow{
								{
									ChatheadImageContent: &widget.SummaryRow_ImgUrl{
										ImgUrl: "IconUrl",
									},
									Heading:      "TestFixedDeposit",
									SubHeading_1: "************",
									SubHeading_2: "MATURES ON 19 NOV, 5170",
									SummaryRowRightElement: &widget.SummaryRowRightElement{
										Text: "₹10,000.00",
									},
									RowPadding: common.SummaryNonHeadingRowPadding,
								},
							},
							DeepLinkElements: []*widget.DeepLinkElement{
								{
									Link: &deeplink.Deeplink{
										Screen: deeplink.Screen_DEPOSIT_ACCOUNT_DETAILS,
										ScreenOptions: &deeplink.Deeplink_DepositDetailsScreenOptions{
											DepositDetailsScreenOptions: &deeplink.DepositAccountDetailsScreenOptions{
												AccountId:   "FixedDepositId",
												DepositType: accountsPb.Type_FIXED_DEPOSIT,
											},
										},
									},
									Text:     summary_v2.SeeDetails,
									CtaTheme: widget.CtaDisplayTheme_PRIMARY,
									CtaType:  widget.CTAType_DEEPLINK,
								},
							},
							TabName: constant.FiTabName,
						},
					},
					WidgetDisplay: searchEnumsPb.WidgetDisplay_FI_TXNS,
					TabName:       "Fi",
				},
			},
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			mockDepositClient := mocks2.NewMockDepositClient(ctrl)
			mockDepositClient.EXPECT().ListDepositAccounts(gomock.Any(), gomock.Any()).Return(tt.mockedListDepositAccountsResp.resp, tt.mockedListDepositAccountsResp.err).Times(2)
			i := skill.NewDepositSkill("TestActorId", tt.fields.configFetcher, mockDepositClient, true)
			skillConfig := skill.Config{
				SkillName: skillPb.SkillName_DEPOSIT,
				DeviceInfo: search_auth.DeviceInfo{
					AppVersion: 1000,
					Platform:   commontypes.Platform_IOS,
				},
				MinCompatibleVersionForSummaryV2: &config.MinVersionForSummaryV2{
					Android: 0,
					IOS:     0,
				},
			}
			if err := i.AddConfigValues(context.Background(), skillConfig); err != nil {
				t.Errorf("error in fetching config for skill: %v", err)
			}
			got, _ := i.BuildSearchResult(context.Background())
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Expected: %v, Got: %v", tt.want, got)
			}
		})
	}
}

func TestDepositSkill_BuildFiLiteSearchResult(t *testing.T) {
	savingsAccountDeeplink, _ := onboarding.GetSABenefitsScreen(context.Background())
	type fields struct {
		actorId                          string
		configFetcher                    skill.SkillConfigGet
		isInternal                       bool
		deviceInfo                       search_auth.DeviceInfo
		MinCompatibleVersionForSummaryV2 *config.MinVersionForSummaryV2
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []*searchPb.SearchResultUnit
		want1  *entity.CurrActorSearchCtxImpl
	}{
		{
			name: "success",
			fields: fields{
				actorId:       "TestActorId",
				configFetcher: skillConfigFetcher,
				isInternal:    true,
				MinCompatibleVersionForSummaryV2: &config.MinVersionForSummaryV2{
					Android: 0,
					IOS:     0,
				},
				deviceInfo: search_auth.DeviceInfo{
					AppVersion: 1000,
					Platform:   commontypes.Platform_IOS,
				},
			},
			args: args{
				ctx: context.Background(),
			},
			want: []*searchPb.SearchResultUnit{
				{
					RespType: searchEnumsPb.ResponseType_SUMMARY_V2,
					Data: &searchPb.SearchResultUnit_SummaryV2Response{
						SummaryV2Response: &summary.SummaryV2{
							CardTitle: "",
							SummaryRows: []*widget.SummaryRow{
								{
									ContentType: widget.ContentType_TEXT,
									Content: &widget.SummaryRow_TextContent{
										TextContent: &widget.TextContent{
											Text: constant.DepositSkillFiLiteSummary,
										},
									},
								},
							},
							DeepLinkElements: []*widget.DeepLinkElement{
								{
									Link:     savingsAccountDeeplink,
									Text:     constant.AccountInfoOpenSavingsAccountCtaText,
									CtaTheme: widget.CtaDisplayTheme_PRIMARY,
									CtaType:  widget.CTAType_DEEPLINK,
								},
							},
							TabName: constant.FiTabName,
						},
					},
					WidgetDisplay: searchEnumsPb.WidgetDisplay_FI_TXNS,
					TabName:       constant.FiTabName,
				},
			},
			want1: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			mockDepositClient := mocks2.NewMockDepositClient(ctrl)
			depositSkill := skill.NewDepositSkill(tt.fields.actorId, tt.fields.configFetcher, mockDepositClient, tt.fields.isInternal)
			skillConfig := skill.Config{
				SkillName:                        skillPb.SkillName_DEPOSIT,
				DeviceInfo:                       tt.fields.deviceInfo,
				MinCompatibleVersionForSummaryV2: tt.fields.MinCompatibleVersionForSummaryV2,
			}
			if err := depositSkill.AddConfigValues(context.Background(), skillConfig); err != nil {
				t.Errorf("error in fetching config for skill: %v", err)
			}

			got, got1 := depositSkill.BuildFiLiteSearchResult(tt.args.ctx)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Expected: %v, Got: %v", tt.want, got)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("Expected: %v, Got: %v", tt.want1, got1)
			}
		})
	}
}
