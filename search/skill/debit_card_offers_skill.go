package skill

import (
	"context"
	"fmt"
	"regexp"
	"strings"

	"go.uber.org/zap"
	"golang.org/x/text/cases"
	"golang.org/x/text/language"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/casper"
	searchWidgetPb "github.com/epifi/gamma/api/frontend/search/widget"
	searchPb "github.com/epifi/gamma/api/search"
	actionbarPb "github.com/epifi/gamma/api/search/actionbar"
	skillPb "github.com/epifi/gamma/api/search/skill"
	summaryPb "github.com/epifi/gamma/api/search/summary"
	"github.com/epifi/gamma/pkg/onboarding"
	"github.com/epifi/gamma/search/common"
	"github.com/epifi/gamma/search/config"
	"github.com/epifi/gamma/search/constant"
	"github.com/epifi/gamma/search/dao/parser"
	"github.com/epifi/gamma/search/dao/search_auth"
	"github.com/epifi/gamma/search/dao/summary_v2"
	"github.com/epifi/gamma/search/entity"
)

type DebitCardOffersSkill struct {
	actorId                          string
	configFetcher                    SkillConfigGet
	searchResultConfig               *skillPb.SearchResultConfig
	query                            string
	parserResponse                   *parser.QueryParserResponseV3
	offerListingClient               casper.OfferListingServiceClient
	isInternal                       bool
	deviceInfo                       search_auth.DeviceInfo
	MinCompatibleVersionForSummaryV2 *config.MinVersionForSummaryV2
}

func NewDebitCardOffersSkill(
	actorId string,
	query string,
	configFetcher SkillConfigGet,
	offerListingClient casper.OfferListingServiceClient,
	parserResponse *parser.QueryParserResponseV3,
	isInternal bool) *DebitCardOffersSkill {
	return &DebitCardOffersSkill{
		actorId:            actorId,
		configFetcher:      configFetcher,
		query:              query,
		parserResponse:     parserResponse,
		offerListingClient: offerListingClient,
		isInternal:         isInternal,
	}
}

func (d *DebitCardOffersSkill) AddConfigValues(ctx context.Context, config Config) error {
	var (
		currSkillConfig *skillPb.SearchResultConfig
		err             error
	)
	// fetch the config for skill
	if currSkillConfig, err = d.configFetcher.FetchSkillConfig(ctx, config.SkillName); err != nil {
		return err
	}
	d.searchResultConfig = currSkillConfig
	d.deviceInfo = config.DeviceInfo
	if config.MinCompatibleVersionForSummaryV2 != nil {
		d.MinCompatibleVersionForSummaryV2 = config.MinCompatibleVersionForSummaryV2
	}
	return nil
}

func (d *DebitCardOffersSkill) AddEntityData(ctx context.Context) {
}

func (d *DebitCardOffersSkill) GetFinancialActivity(ctx context.Context, getFinancialActivityRequest *GetFinancialActivityRequest) (*actionbarPb.QuickInfoResponse, error) {
	return nil, nil
}

//nolint:dupl
func (d *DebitCardOffersSkill) BuildFiLiteSearchResult(ctx context.Context) ([]*searchPb.SearchResultUnit, *entity.CurrActorSearchCtxImpl) {
	var summaryV2Row *summaryPb.SummaryV2

	fiLiteSummaryText := fmt.Sprintf(constant.FiLiteFeatureNotAvailableTemplate, "offers")

	// open savings account deeplink
	savingsAccountDeeplink, err := onboarding.GetSABenefitsScreen(ctx)
	if err != nil {
		summaryV2Row = common.GetSummaryV2FromText(fiLiteSummaryText, constant.FiTabName)
	} else {
		deeplinkElementList := []*searchWidgetPb.DeepLinkElement{
			{
				Link:     savingsAccountDeeplink,
				Text:     constant.AccountInfoOpenSavingsAccountCtaText,
				CtaTheme: searchWidgetPb.CtaDisplayTheme_PRIMARY,
				CtaType:  searchWidgetPb.CTAType_DEEPLINK,
			},
		}
		summaryV2Row = common.GetSummaryV2FromTextAndDeeplink(fiLiteSummaryText, constant.FiTabName, deeplinkElementList)
	}
	return []*searchPb.SearchResultUnit{common.GetSearchResultUnitFromSummaryV2(summaryV2Row, constant.FiTabName)}, nil
}

func (d *DebitCardOffersSkill) BuildSearchResult(ctx context.Context) ([]*searchPb.SearchResultUnit, *entity.CurrActorSearchCtxImpl) {
	if common.IsDeviceSummaryV2Compatible(d.deviceInfo, d.MinCompatibleVersionForSummaryV2, d.isInternal) {
		return d.buildDebitCardOffersSkillSearchResult(ctx), nil
	}
	return nil, nil
}

//nolint:funlen
func (d *DebitCardOffersSkill) buildDebitCardOffersSkillSearchResult(ctx context.Context) []*searchPb.SearchResultUnit {
	var (
		offerDetailsList []*summary_v2.OfferDetails
		heading, iconUrl string
	)
	getOffersResp, offersErr := d.offerListingClient.GetOffers(ctx, &casper.GetOffersRequest{
		ActorId:        d.actorId,
		RedemptionMode: casper.OfferRedemptionMode_FI_CARD,
	})
	if rpcErr := epifigrpc.RPCError(getOffersResp, offersErr); rpcErr != nil {
		if getOffersResp.GetStatus().IsRecordNotFound() {
			logger.WarnWithCtx(ctx, "no active offers found", zap.String("redemptionMode", casper.OfferRedemptionMode_FI_CARD.String()), zap.String(logger.ACTOR_ID_V2, d.actorId))
			return nil
		}
		logger.Error(ctx, "error occurred while calling GetOffers RPC", zap.String(logger.ACTOR_ID_V2, d.actorId), zap.Error(rpcErr))
		return nil
	}
	if len(getOffersResp.GetOffers()) == 0 {
		return common.GetDebitCardOffersNotFoundSummaryV2("")
	}

	tokens := CleanupAndTokenizeQuery(d.query, []string{"offer", "offers"})
	if len(d.parserResponse.GetEntities().GetActor()) > 0 {
		tokens = append(tokens, d.parserResponse.GetEntities().GetActor()...)
	}

	offerIdToScore := make(map[string]int)

	maxScore := 0
	maxScoreOfferId := ""

	// This below part scores each offer against all the tokens
	// The offer which has the highest score will be shown to the user.
	for _, currentOffer := range getOffersResp.GetOffers() {
		offerIdToScore[currentOffer.GetId()] =
			WordScoreInSentence(tokens, strings.ToLower(currentOffer.GetName())) +
				WordScoreInSentence(tokens, strings.ToLower(currentOffer.GetDesc())) +
				WordScoreInSentence(tokens, strings.ToLower(currentOffer.GetAdditionalDetails().GetBrandName()))
		if offerIdToScore[currentOffer.GetId()] > maxScore {
			maxScore = offerIdToScore[currentOffer.GetId()]
			maxScoreOfferId = currentOffer.GetId()
		}
	}

	if len(maxScoreOfferId) > 0 {
		for _, currentOffer := range getOffersResp.GetOffers() {
			if strings.EqualFold(currentOffer.GetId(), maxScoreOfferId) {
				offerDetailsList = append(offerDetailsList, &summary_v2.OfferDetails{
					OfferDescription: currentOffer.GetName() + "\n" + currentOffer.GetDesc(),
					OfferCode:        currentOffer.GetOfferMetadata().GetCouponMetadata().GetCouponCodeV2(),
				})
				if heading == "" {
					if len(d.parserResponse.GetEntities().GetActor()) > 0 {
						heading = cases.Title(language.English).String(d.parserResponse.GetEntities().GetActor()[0])
					} else {
						heading = "DEBIT CARD"
					}
				}
				if iconUrl == "" {
					for _, currentOfferImage := range currentOffer.GetImages() {
						if currentOfferImage.GetImageType() == casper.ImageType_BRAND_IMAGE {
							iconUrl = currentOfferImage.GetUrl()
						}
					}
					if iconUrl == "" {
						iconUrl = common.DefaultIconForDebitCardOffers
					}
				}
			}
		}
		if offerDetailsList == nil {
			return common.GetDebitCardOffersNotFoundSummaryV2(cases.Title(language.English).String(d.parserResponse.GetEntities().GetActor()[0]))
		}
		return common.GetDebitCardOffersSummaryV2(&summary_v2.CardOffersSummaryV2Params{
			IconUrl:       iconUrl,
			Heading:       heading,
			OfferParams:   offerDetailsList,
			CountOfOffers: len(offerDetailsList),
			DeviceInfo:    d.deviceInfo,
		})
	}

	for idx := 0; idx < common.MaximumDebitCardOffersToShow && idx < len(getOffersResp.GetOffers()); idx += 1 {
		offerDetailsList = append(offerDetailsList, &summary_v2.OfferDetails{
			OfferDescription: getOffersResp.GetOffers()[idx].GetName() + "\n" + getOffersResp.GetOffers()[idx].GetDesc(),
			OfferCode:        getOffersResp.GetOffers()[idx].GetOfferMetadata().GetCouponMetadata().GetCouponCodeV2(),
		})
	}
	return common.GetDebitCardOffersSummaryV2(&summary_v2.CardOffersSummaryV2Params{
		IconUrl:       common.DefaultIconForDebitCardOffers,
		Heading:       summary_v2.DebitCardOffersHeading,
		OfferParams:   offerDetailsList,
		CountOfOffers: len(getOffersResp.GetOffers()),
		DeviceInfo:    d.deviceInfo,
	})
}

// WordScoreInSentence calculates the score of queries on offerDetail
// It is a simple frequency based scoring
func WordScoreInSentence(queries []string, offerDetail string) int {
	totalScore := 0
	for _, eachQuery := range queries {
		totalScore += strings.Count(offerDetail, eachQuery)
	}
	return totalScore
}

// CleanupAndTokenizeQuery cleans up non-alphanumeric characters from the string
// and then splits it into tokens
func CleanupAndTokenizeQuery(query string, excludedWords []string) []string {
	nonAlphanumericRegex := regexp.MustCompile(`[^a-zA-Z0-9 ]+`)
	cleanedQuery := nonAlphanumericRegex.ReplaceAllString(query, "")
	words := strings.Fields(strings.ToLower(cleanedQuery))
	var tokenizedWords []string
	for _, word := range words {
		isExcluded := false
		for _, excludedWord := range excludedWords {
			if word == excludedWord {
				isExcluded = true
				break
			}
		}
		if !isExcluded {
			tokenizedWords = append(tokenizedWords, word)
		}
	}
	return tokenizedWords
}
