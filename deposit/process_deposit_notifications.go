package deposit

import (
	"context"
	"errors"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"

	vendorPkg "github.com/epifi/gamma/pkg/vendors/federal"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	actorPb "github.com/epifi/gamma/api/actor"
	depositPb "github.com/epifi/gamma/api/deposit"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/deposit/metrics"
	"github.com/epifi/gamma/pkg/pay"
)

// RPC to process inbound notification received for a deposit account
// Inbound notifications are received for both savings and deposit account. This RPC is responsible for handling all
// inbound notifications received for deposit account.
//
// This RPC will be called for all sorts of deposit notifications and will internally call the appropriate method to
// process each individual deposit notification type, i.e. deposit creation, closure, add funds, etc.
// TODO(harish): handle deposit inbounds for different scenarios in different methods/RPC
func (s *Service) ProcessDepositInboundNotifications(ctx context.Context, req *depositPb.ProcessDepositInboundNotificationsRequest) (
	*depositPb.ProcessDepositInboundNotificationsResponse, error) {
	var (
		res = &depositPb.ProcessDepositInboundNotificationsResponse{}
	)
	logger.Info(ctx, "received deposit inbound notification",
		zap.String(logger.PARTNER_TXN_ID, req.GetTransactionDetails().GetId()))

	// if deposit notification belongs to deposit interest credit, create an order and txn
	if req.GetPaymentProtocol() == paymentPb.PaymentProtocol_INTRA_BANK && req.GetTransactionDetails().GetTxnCategory() == depositPb.TransactionCategory_DEPOSIT_INTEREST_CREDIT {
		logger.Info(ctx, "processing deposit interest credit notifications")
		err := s.DepositInboundNotificationProcessor.ProcessInterestCreditNotification(ctx, req.GetTransactionDetails(), req.GetPartnerBank())
		if err != nil {
			if errors.Is(err, epifierrors.ErrTransient) {
				res.Status = rpc.StatusTransientFailure()
				return res, nil
			}
			logger.Error(ctx, "failed to process deposit interest inbound notifications, %w", zap.Error(err))
			res.Status = rpc.StatusInternalWithDebugMsg(err.Error())
			return res, nil
		}
		res.Status = rpc.StatusOk()
		return res, nil
	}

	// if deposit notification doesn't belong to external deposit add funds, ignore for now.
	if req.GetPaymentProtocol() != paymentPb.PaymentProtocol_IMPS && req.GetPaymentProtocol() != paymentPb.PaymentProtocol_NEFT {
		logger.Info(ctx, "ignoring deposit inbound notification")
		res.Status = rpc.StatusOk()
		return res, nil
	} else {
		// if notification is for deposit account's externally added funds, create an order with generic PI
		res.Status = s.processExternalAddFundsInbound(ctx, req.GetTransactionDetails(), req.GetPartnerBank(), req.GetSource())
		return res, nil
	}
}

func (s *Service) processExternalAddFundsInbound(
	ctx context.Context,
	txnDetails *depositPb.InboundNotificationTxnDetails,
	partnerBank commonvgpb.Vendor,
	source depositPb.Source,
) *rpc.Status {
	piFrom := pay.GenericPiId
	remarks := "received funds from external account"

	// All Deposit Accounts have a single IFSC code for now. In the future, if multiple ifsc codes needs to be supported,
	// vendor has to give more identifiers in the inbound notification. Monorail: https://monorail.pointz.in/p/fi-app/issues/detail?id=56134
	depositAccountIfscCode, err := vendorPkg.GetIfscCodeForVendor(partnerBank)
	if err != nil {
		logger.Error(ctx, "failed to get ifscCode for vendor",
			zap.String(logger.VENDOR, partnerBank.String()),
			zap.Error(err),
		)
		return rpc.StatusInternal()

	}

	depositAccountNumber := txnDetails.GetAccountNumber()
	depositAccount, err := s.DepositAccountDao.GetByAccountNumberAndIfsc(ctx, depositAccountNumber, depositAccountIfscCode)
	if err != nil {
		logger.Error(ctx, "failed to get deposit account",
			zap.String(logger.ACCOUNT_NUMBER, txnDetails.GetAccountNumber()),
			zap.Error(err),
		)
		return rpc.StatusRecordNotFound()
	}
	piTo, err := s.getPiByAccountNumberAndIfsc(ctx, depositAccountNumber, depositAccountIfscCode)
	if err != nil {
		logger.Error(ctx, "failed to fetch PI by account number and ifsc",
			zap.Error(err))
		return rpc.StatusInternal()
	}

	resolveActorFromResponse, err := s.actorClient.ResolveActorFrom(ctx, &actorPb.ResolveActorFromRequest{
		ActorTo:       depositAccount.GetActorId(),
		PiFrom:        piFrom,
		ActorFromName: pay.DefaultActorName,
	})
	if err = epifigrpc.RPCError(resolveActorFromResponse, err); err != nil {
		logger.Error(ctx, "error occurred while resolving ActorFrom",
			zap.Error(err),
		)
		return rpc.StatusInternal()
	}
	fromActor := resolveActorFromResponse.GetActorFrom()

	rawNotificationDetails := make(map[string]*paymentPb.NotificationDetails)
	if txnDetails != nil {
		var valueDate *timestamppb.Timestamp = nil
		if txnDetails.GetValueDate() != nil {
			valueDate = timestamppb.New(*datetime.DateToTime(txnDetails.GetValueDate(), datetime.IST))
		}

		rawNotificationDetails[paymentPb.AccountingEntryType_CREDIT.String()] = &paymentPb.NotificationDetails{
			Particulars:   txnDetails.GetParticular(),
			BatchSerialId: txnDetails.GetBatchSerialId(),
			ValueDate:     valueDate,
		}
	}
	utr := txnDetails.GetId()
	// for IMPS/NEFT txns using reference number as UTR rather than the date+tranId one
	// all internal deposit add funds txns are stored as debit txns in our system wrt to savings account, but
	// this external deposit add funds txn is stored as a credit one wrt deposit account.
	orderTags := []orderPb.OrderTag{orderPb.OrderTag_DEPOSIT, depositTypeToOrderTagMap[depositAccount.Type]}
	_, orderId, txnId, err := s.createNoOpOrderWithTxn(ctx, piFrom, piTo.GetId(), depositAccount.GetActorId(),
		fromActor, remarks, utr, txnDetails.GetBatchSerialId(), txnDetails.GetCbsId(), txnDetails.GetAmount(),
		orderTags, txnDetails.GetTimestamp(),
		nil, txnDetails.GetTimestamp(), source, metrics.DepositAddFunds, rawNotificationDetails)
	if err != nil {
		logger.Error(ctx, "failed to create order to external add funds", zap.Error(err))
		return rpc.StatusInternal()
	}

	logger.Info(ctx, "successfully processed deposit external add funds inbound",
		zap.String(logger.ORDER_ID, orderId),
		zap.String(logger.TXN_ID, txnId),
		zap.String(logger.ACTOR_ID_V2, depositAccount.GetActorId()))

	return rpc.StatusOk()
}
