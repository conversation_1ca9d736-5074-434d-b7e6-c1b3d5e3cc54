package model

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"time"

	"github.com/epifi/be-common/pkg/nulltypes"

	livPb "github.com/epifi/gamma/api/auth/liveness"

	"google.golang.org/protobuf/types/known/timestamppb"
)

type LivenessSummary struct {
	Id                     string `gorm:"type:uuid;default:gen_random_uuid()"`
	ActorID                string
	RequestID              string
	LivenessFlow           livPb.LivenessFlow
	LivenessAttemptID      string
	FacematchAttemptID     string
	FacematchImage         *livPb.FacematchInfo
	ClientRequestParams    *livPb.LivenessSummary_ClientRequestParams
	MaxAttempts            int
	AttemptsCount          int
	Status                 livPb.SummaryStatus
	CreatedAt              time.Time
	UpdatedAt              time.Time
	SummaryLivenessStatus  livPb.SummaryLivenessStatus
	SummaryFacematchStatus livPb.SummaryFacematchStatus
	ForceManualReview      commontypes.BooleanEnum
	ExpireAt               nulltypes.NullTime
}

// NewLivenessSummaryModel convert livenessSummary proto to liveness summary model
func NewLivenessSummaryModel(ls *livPb.LivenessSummary) *LivenessSummary {
	var expireAt nulltypes.NullTime
	if ls.GetExpireAt().IsValid() {
		expireAt = nulltypes.NewNullTime(ls.GetExpireAt().AsTime())
	}
	return &LivenessSummary{
		Id:                     ls.Id,
		ActorID:                ls.ActorId,
		RequestID:              ls.RequestId,
		LivenessFlow:           ls.LivenessFlow,
		LivenessAttemptID:      ls.LivenessAttemptId,
		FacematchAttemptID:     ls.FacematchAttemptId,
		FacematchImage:         ls.FacematchInfo,
		ClientRequestParams:    ls.ClientRequestParams,
		MaxAttempts:            int(ls.MaxAttempts),
		AttemptsCount:          int(ls.AttemptsCount),
		Status:                 ls.Status,
		SummaryLivenessStatus:  ls.SummaryLivenessStatus,
		SummaryFacematchStatus: ls.SummaryFacematchStatus,
		ForceManualReview:      ls.ForceManualReview,
		ExpireAt:               expireAt,
	}
}

// GetProto - Converts the given liveness summary model to LivenessSummary proto
func (ls *LivenessSummary) GetProto() *livPb.LivenessSummary {
	if ls == nil {
		return nil
	}

	return &livPb.LivenessSummary{
		Id:                     ls.Id,
		ActorId:                ls.ActorID,
		RequestId:              ls.RequestID,
		LivenessFlow:           ls.LivenessFlow,
		LivenessAttemptId:      ls.LivenessAttemptID,
		FacematchAttemptId:     ls.FacematchAttemptID,
		FacematchInfo:          ls.FacematchImage,
		ClientRequestParams:    ls.ClientRequestParams,
		MaxAttempts:            int32(ls.MaxAttempts),
		AttemptsCount:          int32(ls.AttemptsCount),
		Status:                 ls.Status,
		SummaryLivenessStatus:  ls.SummaryLivenessStatus,
		SummaryFacematchStatus: ls.SummaryFacematchStatus,
		ForceManualReview:      ls.ForceManualReview,
		ExpireAt:               ls.ExpireAt.GetProto(),
		CreatedAt:              timestamppb.New(ls.CreatedAt),
	}
}
