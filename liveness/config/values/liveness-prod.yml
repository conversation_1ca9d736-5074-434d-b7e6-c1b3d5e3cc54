Application:
  Environment: "prod"
  Name: "liveness"

EpifiDb:
  DbType: "CRDB"
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: false
  SSLMode: "verify-full"
  SSLRootCert: "prod/cockroach/ca.crt"
  SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 20
  MaxIdleConn: 5
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

Aws:
  S3:
    LivenessBucketName: "epifi-prod-liveness"
    UserBucketName: "epifi-prod-users"
    NrBucketName: "epifi-prod-nrusers"

LivenessManualReviewPublisher:
  TopicName: "prod-liveness-manual-review-topic"

LivenessSummaryCompletedEventPublisher:
  TopicName: "prod-liveness-summary-completed-event-topic"

OnboardingLivenessRuleId: "3c49be64-92c3-4091-a70f-335aa922c51b"
NROnboardingLivenessRuleId: "3d063cd6-ee54-4e09-803e-56f268beb39b"
AFULivenessExternalRuleId: "AFU_LIVENESS_1"

Flags:
  UseNewLivenessFlowForDeviceBiometric:
    MinAndroidVersion: 100000
    MinIOSVersion: 100000
    UnsupportedPlatforms: [ 2 ]
    FallbackToEnableFeature: false
    DisableFeature: true
  DisableLivenessAttemptChangeFeed: true
