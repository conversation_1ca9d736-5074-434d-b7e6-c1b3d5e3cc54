// nolint: dogsled
package test

import (
	"context"
	"github.com/epifi/gamma/nudge/ruleengine"
	"github.com/epifi/gamma/nudge/ruleengine/fact/common"
	invFac "github.com/epifi/gamma/nudge/ruleengine/fact/investment"
	"os"
	"testing"

	"github.com/epifi/gamma/api/investment"
	nudgeInvPb "github.com/epifi/gamma/api/investment/aggregator/events/nudges"
	nudgePb "github.com/epifi/gamma/api/nudge"
	rms "github.com/epifi/gamma/api/rms/manager"
	"github.com/epifi/gamma/nudge/test"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	_, _, _, _, teardown := test.InitTestServerV2(false)
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

func TestRuleEngine_EvaluateExit(t *testing.T) {
	type args struct {
		ctx  context.Context
		fact common.IFact
	}
	tests := []struct {
		name    string
		args    args
		want    bool
		wantErr bool
	}{
		{
			name: "test mutual fund investment event",
			args: args{
				ctx: context.Background(),
				fact: &invFac.InvestmentFact{
					CommonFact: &common.CommonFact{
						Nudge: &nudgePb.Nudge{
							ExitExpression: "OrderType == 'BUY' && Instrument == 'MUTUAL_FUNDS' && Frequency == 'ONE_TIME' && Amount >= 100",
						},
					},
					InvestmentData: &nudgeInvPb.InvestmentEventData{InvestmentData: &nudgeInvPb.InvestmentEventData_OrderData{OrderData: &nudgeInvPb.OrderData{
						Instrument: investment.InvestmentInstrumentType_MUTUAL_FUNDS,
						Type:       nudgeInvPb.OrderType_BUY,
						Frequency:  nudgeInvPb.OrderFrequency_ONE_TIME,
						Amount:     float32(100),
						Scheme:     "1234",
					}}},
				},
			},
			wantErr: false,
			want:    true,
		},
		{
			name: "test mutual fund investment event: evaluate to false",
			args: args{
				ctx: context.Background(),
				fact: &invFac.InvestmentFact{
					CommonFact: &common.CommonFact{
						Nudge: &nudgePb.Nudge{
							ExitExpression: "OrderType == 'BUY' && Instrument == 'MUTUAL_FUNDS' && Frequency == 'ONE_TIME' && Amount < 100",
						},
					},
					InvestmentData: &nudgeInvPb.InvestmentEventData{InvestmentData: &nudgeInvPb.InvestmentEventData_OrderData{OrderData: &nudgeInvPb.OrderData{
						Instrument: investment.InvestmentInstrumentType_MUTUAL_FUNDS,
						Type:       nudgeInvPb.OrderType_BUY,
						Frequency:  nudgeInvPb.OrderFrequency_ONE_TIME,
						Amount:     float32(100),
						Scheme:     "1234",
					}}},
				},
			},
			wantErr: false,
			want:    false,
		},
		{
			name: "test mutual fund subscription event",
			args: args{
				ctx: context.Background(),
				fact: &invFac.InvestmentFact{
					CommonFact: &common.CommonFact{
						Nudge: &nudgePb.Nudge{
							ExitExpression: "RuleType == 'RULE_TYPE_AUTO_INVEST_DAILY' && Instrument == 'MUTUAL_FUNDS'",
						},
					},
					InvestmentData: &nudgeInvPb.InvestmentEventData{InvestmentData: &nudgeInvPb.InvestmentEventData_SubscriptionData{SubscriptionData: &nudgeInvPb.SubscriptionData{
						Instrument: investment.InvestmentInstrumentType_MUTUAL_FUNDS,
						RuleType:   rms.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_INVEST_DAILY,
					}}},
				},
			},
			wantErr: false,
			want:    true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &ruleengine.RuleEngine{}
			got, err := r.EvaluateExit(tt.args.ctx, tt.args.fact)
			if (err != nil) != tt.wantErr {
				t.Errorf("EvaluateExit() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("EvaluateExit() got = %v, want %v", got, tt.want)
			}
		})
	}
}
