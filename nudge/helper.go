package nudge

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/samber/lo"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	nudgePb "github.com/epifi/gamma/api/nudge"
	segmentPb "github.com/epifi/gamma/api/segment"
)

func (s *Service) getNudgeFromCreateNudgeRequest(ctx context.Context, req *nudgePb.CreateNudgeRequest) (*nudgePb.Nudge, error) {
	activeSince, err := time.Parse(time.RFC3339, req.GetActiveSince())
	if err != nil {
		return nil, err
	}

	activeTill, err := time.Parse(time.RFC3339, req.GetActiveTill())
	if err != nil {
		return nil, err
	}

	displaySince, err := s.getNudgeDisplaySince(ctx, req.GetEntrySegmentExpression(), activeSince, req.GetExitEvent())
	if err != nil {
		logger.Error(ctx, "error getting nudge display since", zap.Error(err), zap.String("entryDisplaySegmentExpression", req.GetEntrySegmentExpression()), zap.Time("activeSince", activeSince))
		return nil, fmt.Errorf("error getting nudge display since: %w", err)
	}
	displayTill := activeTill
	// creating nudges in active state in all envs except prod
	status := nudgePb.NudgeStatus_NUDGE_STATUS_ACTIVE
	if s.conf.Application.Environment == cfg.ProductionEnv || s.conf.Application.Environment == cfg.TestEnv {
		status = nudgePb.NudgeStatus_NUDGE_STATUS_CREATED
	}

	nudge := &nudgePb.Nudge{
		Description:                  req.GetDescription(),
		EntryEvent:                   req.GetEntryEvent(),
		EntrySegmentExpression:       req.GetEntrySegmentExpression(),
		EntryEventExpression:         req.GetEntryEventExpression(),
		ExitEvent:                    req.GetExitEvent(),
		ExitExpression:               req.GetExitExpression(),
		AutoDismissDurationInSeconds: req.GetAutoDismissDurationInSeconds(),
		SnoozeDurationInSeconds:      req.GetSnoozeDurationInSeconds(),
		ActiveSince:                  timestampPb.New(activeSince),
		ActiveTill:                   timestampPb.New(activeTill),
		DisplaySince:                 timestampPb.New(displaySince),
		DisplayTill:                  timestampPb.New(displayTill),
		SetUpBy:                      req.GetSetUpBy(),
		Area:                         req.GetArea(),
		Category:                     req.GetCategory(),
		SubCategory:                  req.GetSubCategory(),
		Urgency:                      req.GetUrgency(),
		UserInactionConsequence:      req.GetUserInactionConsequence(),
		Screens:                      req.GetScreens(),
		DisplayConfigs:               req.GetDisplayConfigs(),
		Deeplink:                     req.GetDeeplink(),
		Status:                       status,
		IsEntryActivationEventDriven: req.GetIsEntryActivationEventDriven(),
		NudgeType:                    req.GetNudgeType(),
		UserType:                     req.GetUserType(),
		AdditionalDetails:            req.GetAdditionalDetails(),
	}

	return nudge, nil
}

// getNudgeDisplaySince checks the following conditions to determine the display since time for a nudge:
// 1. If there are no dynamic segments, it returns the active since time as display since time.
// 2. If there are dynamic segments, it sets the display since time to the next day's 12:00 PM IST.
// NOTE:
// 1. This is done to ensure that the nudges are displayed only after the dynamic segments are updated which is usually done at 10:00 AM IST every day.
// 2. This is done because we may end up showing nudges to users whose exit event was triggered between dynamic segment update time and the nudge creation time
// which will lead to users seeing nudges which have been completed.
func (s *Service) getNudgeDisplaySince(ctx context.Context, entryDisplaySegmentExpression string, activeSince time.Time, exitEvent nudgePb.NudgeEventDataType) (time.Time, error) {
	var (
		segmentIds   []string
		displaySince = activeSince
	)
	reMatches := regexp.MustCompile(`IsMember\('(.+?)'\)`).FindAllStringSubmatch(entryDisplaySegmentExpression, -1)
	for _, reMatch := range reMatches {
		segmentIds = append(segmentIds, reMatch[1])
	}

	// Directly return activeSince if no segments are found or if the exit event is not defined
	if len(segmentIds) == 0 || lo.Contains([]nudgePb.NudgeEventDataType{
		nudgePb.NudgeEventDataType_NUDGE_EVENT_DATA_TYPE_UNSPECIFIED,
		nudgePb.NudgeEventDataType_NUDGE_EXIT_EVENT_UNSPECIFIED,
	}, exitEvent) {
		// If no segments are found, return the active since time as display since time
		return activeSince, nil
	}

	segmentTypesResp, err := s.segmentClient.GetSegmentTypes(ctx, &segmentPb.GetSegmentTypesRequest{
		SegmentIds: segmentIds,
	})
	if rpcError := epifigrpc.RPCError(segmentTypesResp, err); rpcError != nil {
		logger.Error(ctx, "error getting segment type", zap.Error(rpcError), zap.Any("entryDisplaySegmentExpressionSegmentIds", segmentIds))
		return time.Time{}, fmt.Errorf("error getting segment type: %w", rpcError)
	}

	for _, segmentType := range segmentTypesResp.GetSegmentTypes() {
		if segmentType == segmentPb.SegmentType_DYNAMIC {
			// set display since to next day's 12:00 PM IST
			activeSince = activeSince.In(datetime.IST)
			displaySince = time.Date(activeSince.Year(), activeSince.Month(), activeSince.Day()+1, 12, 0, 0, 0, datetime.IST)
			break
		}
	}
	return displaySince, nil
}

func getNudgeFromEditNudgeRequest(req *nudgePb.EditNudgeRequest) (*nudgePb.Nudge, error) {
	activeTill, err := time.Parse(time.RFC3339, req.GetActiveTill())
	if err != nil {
		return nil, err
	}

	displayTill, err := time.Parse(time.RFC3339, req.GetDisplayTill())
	if err != nil {
		return nil, err
	}

	nudge := &nudgePb.Nudge{
		Id:                           req.GetId(),
		AutoDismissDurationInSeconds: req.GetAutoDismissDurationInSeconds(),
		SnoozeDurationInSeconds:      req.GetSnoozeDurationInSeconds(),
		ActiveTill:                   timestampPb.New(activeTill),
		DisplayTill:                  timestampPb.New(displayTill),
		Area:                         req.GetArea(),
		Category:                     req.GetCategory(),
		SubCategory:                  req.GetSubCategory(),
		Urgency:                      req.GetUrgency(),
		UserInactionConsequence:      req.GetUserInactionConsequence(),
		Screens:                      req.GetScreens(),
		DisplayConfigs:               req.GetDisplayConfigs(),
		Deeplink:                     req.GetDeeplink(),
		AdditionalDetails:            req.GetAdditionalDetails(),
	}

	return nudge, nil
}

func getNudgeInstance(nudge *nudgePb.Nudge, actorNudge *nudgePb.ActorNudge, chosenDisplay *nudgePb.NudgeDisplay) *nudgePb.NudgeInstance {
	return &nudgePb.NudgeInstance{
		Id:                      getNudgeInstanceId(actorNudge),
		NudgeId:                 nudge.GetId(),
		ActorNudgeId:            actorNudge.GetId(),
		Area:                    nudge.GetArea(),
		Category:                nudge.GetCategory(),
		SubCategory:             nudge.GetSubCategory(),
		Urgency:                 nudge.GetUrgency(),
		UserInactionConsequence: nudge.GetUserInactionConsequence(),
		NudgeDisplay:            chosenDisplay,
		Deeplink:                nudge.GetDeeplink(),
		NudgeType:               nudge.GetNudgeType(),
		EntrySegmentExpression:  nudge.GetEntrySegmentExpression(),
		DisplayCycleStartedAt:   actorNudge.GetDisplayCycleStartedAt(),
		NumDisplayCycles:        actorNudge.GetNumDisplayCycles(),
		AdditionalDetails:       nudge.GetAdditionalDetails(),
		ActorNudgeStatus:        actorNudge.GetStatus(),
	}
}

func getNudgeInstanceId(actorNudge *nudgePb.ActorNudge) string {
	return actorNudge.GetId()
}

func GetActorNudgeIdFromNudgeInstanceId(nudgeInstanceId string) string {
	return nudgeInstanceId
}

func (s *Service) validateCreateNudgeRequest(ctx context.Context, req *nudgePb.Nudge) error {
	// check area category compatibility
	switch req.GetArea() {
	case nudgePb.NudgeArea_NUDGE_AREA_PAYMENTS:
		if !lo.Contains([]nudgePb.NudgeCategory{nudgePb.NudgeCategory_NUDGE_CATEGORY_UPI, nudgePb.NudgeCategory_NUDGE_CATEGORY_BILL_PAY, nudgePb.NudgeCategory_NUDGE_CATEGORY_BANK_TRANSFERS, nudgePb.NudgeCategory_NUDGE_CATEGORY_ENACH}, req.GetCategory()) {
			return fmt.Errorf("category: %s is not a part of area: %s", req.GetCategory().String(), req.GetArea().String())
		}
	case nudgePb.NudgeArea_NUDGE_AREA_ACQUISITION:
		if !lo.Contains([]nudgePb.NudgeCategory{nudgePb.NudgeCategory_NUDGE_CATEGORY_REFERRAL, nudgePb.NudgeCategory_NUDGE_CATEGORY_FIRST_ADD_FUNDS}, req.GetCategory()) {
			return fmt.Errorf("category: %s is not a part of area: %s", req.GetCategory().String(), req.GetArea().String())
		}
	case nudgePb.NudgeArea_NUDGE_AREA_ONBOARDING:
		if !lo.Contains([]nudgePb.NudgeCategory{nudgePb.NudgeCategory_NUDGE_CATEGORY_SA_ONBOARDING, nudgePb.NudgeCategory_NUDGE_CATEGORY_RE_ONBOARDING, nudgePb.NudgeCategory_NUDGE_CATEGORY_VKYC_UPGRADE}, req.GetCategory()) {
			return fmt.Errorf("category: %s is not a part of area: %s", req.GetCategory().String(), req.GetArea().String())
		}
	case nudgePb.NudgeArea_NUDGE_AREA_CARDS:
		if !lo.Contains([]nudgePb.NudgeCategory{nudgePb.NudgeCategory_NUDGE_CATEGORY_DEBIT_CARD, nudgePb.NudgeCategory_NUDGE_CATEGORY_AMPLIFI_CREDIT_CARD, nudgePb.NudgeCategory_NUDGE_CATEGORY_SIMPLIFI_CREDIT_CARD, nudgePb.NudgeCategory_NUDGE_CATEGORY_MAGNIFI_CREDIT_CARD}, req.GetCategory()) {
			return fmt.Errorf("category: %s is not a part of area: %s", req.GetCategory().String(), req.GetArea().String())
		}
	case nudgePb.NudgeArea_NUDGE_AREA_LOANS:
		if !lo.Contains([]nudgePb.NudgeCategory{nudgePb.NudgeCategory_NUDGE_CATEGORY_PERSONAL_LOAN, nudgePb.NudgeCategory_NUDGE_CATEGORY_LAMF}, req.GetCategory()) {
			return fmt.Errorf("category: %s is not a part of area: %s", req.GetCategory().String(), req.GetArea().String())
		}
	case nudgePb.NudgeArea_NUDGE_AREA_CX:
		if !lo.Contains([]nudgePb.NudgeCategory{nudgePb.NudgeCategory_NUDGE_CATEGORY_CX}, req.GetCategory()) {
			return fmt.Errorf("category: %s is not a part of area: %s", req.GetCategory().String(), req.GetArea().String())
		}
	case nudgePb.NudgeArea_NUDGE_AREA_WEALTH:
		if !lo.Contains([]nudgePb.NudgeCategory{nudgePb.NudgeCategory_NUDGE_CATEGORY_US_STOCKS, nudgePb.NudgeCategory_NUDGE_CATEGORY_JUMP, nudgePb.NudgeCategory_NUDGE_CATEGORY_AUTOSAVE, nudgePb.NudgeCategory_NUDGE_CATEGORY_MUTUAL_FUNDS, nudgePb.NudgeCategory_NUDGE_CATEGORY_FIXED_DEPOSITS, nudgePb.NudgeCategory_NUDGE_CATEGORY_SMART_DEPOSITS}, req.GetCategory()) {
			return fmt.Errorf("category: %s is not a part of area: %s", req.GetCategory().String(), req.GetArea().String())
		}
	case nudgePb.NudgeArea_NUDGE_AREA_SAVINGS_ACCOUNT:
		if !lo.Contains([]nudgePb.NudgeCategory{nudgePb.NudgeCategory_NUDGE_CATEGORY_PLUS, nudgePb.NudgeCategory_NUDGE_CATEGORY_INFINITE, nudgePb.NudgeCategory_NUDGE_CATEGORY_SALARY, nudgePb.NudgeCategory_NUDGE_CATEGORY_ADD_FUNDS, nudgePb.NudgeCategory_NUDGE_CATEGORY_ACCOUNT_CLOSURE}, req.GetCategory()) {
			return fmt.Errorf("category: %s is not a part of area: %s", req.GetCategory().String(), req.GetArea().String())
		}
	case nudgePb.NudgeArea_NUDGE_AREA_REWARDS:
		if !lo.Contains([]nudgePb.NudgeCategory{nudgePb.NudgeCategory_NUDGE_CATEGORY_EARN_REWARDS, nudgePb.NudgeCategory_NUDGE_CATEGORY_CLAIM_REWARDS, nudgePb.NudgeCategory_NUDGE_CATEGORY_SPEND_FI_COINS}, req.GetCategory()) {
			return fmt.Errorf("category: %s is not a part of area: %s", req.GetCategory().String(), req.GetArea().String())
		}
	case nudgePb.NudgeArea_NUDGE_AREA_RISK:
		if !lo.Contains([]nudgePb.NudgeCategory{nudgePb.NudgeCategory_NUDGE_CATEGORY_ACCOUNT_CLOSURE}, req.GetCategory()) {
			return fmt.Errorf("category: %s is not a part of area: %s", req.GetCategory().String(), req.GetArea().String())
		}
	case nudgePb.NudgeArea_NUDGE_AREA_ACCOUNT_AGGREGATOR:
		if !lo.Contains([]nudgePb.NudgeCategory{nudgePb.NudgeCategory_NUDGE_CATEGORY_CONNECTED_ACCOUNTS}, req.GetCategory()) {
			return fmt.Errorf("category: %s is not a part of area: %s", req.GetCategory().String(), req.GetArea().String())
		}
	case nudgePb.NudgeArea_NUDGE_AREA_DEMYSTIFY:
		if !lo.Contains([]nudgePb.NudgeCategory{nudgePb.NudgeCategory_NUDGE_CATEGORY_ASK_FI, nudgePb.NudgeCategory_NUDGE_CATEGORY_ANALYSERS, nudgePb.NudgeCategory_NUDGE_CATEGORY_INSIGHTS, nudgePb.NudgeCategory_NUDGE_CATEGORY_NETWORTH, nudgePb.NudgeCategory_NUDGE_CATEGORY_REMINDERS}, req.GetCategory()) {
			return fmt.Errorf("category: %s is not a part of area: %s", req.GetCategory().String(), req.GetArea().String())
		}
	default:
		return fmt.Errorf("area: %s is not supported", req.GetArea().String())
	}

	// check if number of characters in title and subtitle are within the limit
	for _, nudgeDisplay := range req.GetDisplayConfigs().GetNudgeDisplays() {
		if len(nudgeDisplay.GetSubtitle()) == 0 {
			if nudgePb.IsNudgeOfBannerType(req.GetNudgeType()) && len(nudgeDisplay.GetTitle()) > 60 {
				return fmt.Errorf("title '%s' is breaching allowed characters limit of 60", nudgeDisplay.GetTitle())
			} else if !nudgePb.IsNudgeOfBannerType(req.GetNudgeType()) && len(nudgeDisplay.GetTitle()) > 50 {
				return fmt.Errorf("title '%s' is breaching allowed characters limit of 50", nudgeDisplay.GetTitle())
			}
		} else {
			if len(nudgeDisplay.GetTitle()) > 35 {
				return fmt.Errorf("title '%s' is breaching allowed characters limit of 35", nudgeDisplay.GetTitle())
			}
			if len(nudgeDisplay.GetSubtitle()) > 37 {
				return fmt.Errorf("subtitle '%s' is breaching allowed characters limit of 37", nudgeDisplay.GetSubtitle())
			}
		}
	}

	// check if active since is valid or not
	currentTime := time.Now()
	if req.GetActiveSince().AsTime().Before(currentTime) {
		logger.Error(ctx, "active since cannot be in the past", zap.Time("currentTime", currentTime), zap.Time("activeSince", req.GetActiveSince().AsTime()))
		return fmt.Errorf("active since cannot be in the past")
	}

	// check if nudge entry segment expression is valid or not
	res, err := s.segmentClient.IsMemberOfExpressions(ctx, &segmentPb.IsMemberOfExpressionsRequest{
		ActorId:              "",
		SegmentIdExpressions: []string{req.EntrySegmentExpression},
		LatestBy:             timestampPb.Now(),
	})
	if e := epifigrpc.RPCError(res, err); e != nil {
		return fmt.Errorf("segmentClient.IsMemberOfExpressions() call is failing, err: %w", e)
	}

	if segmentExpressionMembership, found := res.GetSegmentExpressionMembershipMap()[req.EntrySegmentExpression]; found {
		if segmentExpressionMembership.GetSegmentExpressionStatus() != segmentPb.SegmentExpressionStatus_OK {
			return fmt.Errorf("one or more segments do not exists in the given expression")
		}
	} else {
		return fmt.Errorf("EntrySegmentExpression not found in SegmentExpressionMembershipMap")
	}

	// nudge user type cannot be unspecified
	if req.GetUserType() == nudgePb.NudgeUserType_NUDGE_USER_TYPE_UNSPECIFIED {
		return fmt.Errorf("nudge user type cannot be unspecified")
	}

	// check if exit expression is valid (exit event is rudder event)
	if req.GetExitEvent() == nudgePb.NudgeEventDataType_RUDDER_EVENT {
		if req.GetIsEntryActivationEventDriven() {
			return fmt.Errorf("rudder event not allowed while isEntryActivationEventDriven is true")
		}
		eventName, isExitExpressionValid := rudderEventExpressionPreValidationAndEventNameExtraction(req.GetExitExpression())
		if isExitExpressionValid {
			_, ok := s.conf.RudderEventKafkaConsumerGroup.Whitelist.AllowedValues[eventName]
			if !ok {
				return fmt.Errorf("event name is not whitelisted")
			}
		} else {
			return fmt.Errorf("invalid nudge exit expression")
		}
	}

	// check if entry expression is valid (for entry event is rudder event)
	if req.GetEntryEvent() == nudgePb.NudgeEventDataType_RUDDER_EVENT {
		eventName, isExitExpressionValid := rudderEventExpressionPreValidationAndEventNameExtraction(req.GetEntryEventExpression())
		if isExitExpressionValid {
			if !lo.Contains(s.conf.AllowedRudderEventsForNudgeEntryEvents, eventName) {
				return fmt.Errorf("event name is not whitelisted for entry events")
			}
		} else {
			return fmt.Errorf("invalid nudge entry expression")
		}
	}

	return nil
}

func rudderEventExpressionPreValidationAndEventNameExtraction(inputString string) (string, bool) {
	// Define the regex pattern
	pattern := `^EVENT_NAME\s*==\s*'([^']*)'(?:\s*&&\s*!?HasEventProperty\([^)]*\))*$`

	// Compile the regex pattern
	re := regexp.MustCompile(pattern)

	// Match the regex against the input string
	matchingValidExpressions := re.FindStringSubmatch(inputString)

	// Check if the regex matched and captured the event name
	if len(matchingValidExpressions) > 1 {
		// Extract the event name from the first capture group
		eventName := strings.TrimSpace(matchingValidExpressions[1])
		return eventName, true
	}

	// Return false if the input string doesn't match the pattern
	return "", false
}
