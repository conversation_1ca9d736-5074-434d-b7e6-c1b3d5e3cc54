package steps

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/consent/mocks"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/wealthonboarding/release"
	mock_evaluator "github.com/epifi/gamma/wealthonboarding/test/mocks/release"
)

func TestRecordWealthMITCConsent_Perform(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx        context.Context
		onbDetails *woPb.OnboardingDetails
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockConsentClient *mocks.MockConsentClient, mockEvaluator *mock_evaluator.MockIEvaluator)
		want       *StepExecutionResponse
		wantErr    bool
	}{
		{
			name: "success, record wealth mitc consent not required",
			args: args{
				ctx: context.Background(),
				onbDetails: &woPb.OnboardingDetails{
					ActorId:   "actor_id",
					CreatedAt: timestamp.New(time.Date(2025, 7, 26, 0, 0, 0, 0, time.Local)),
				},
			},
			setupMocks: func(mockConsentClient *mocks.MockConsentClient, mockEvaluator *mock_evaluator.MockIEvaluator) {},
			want: &StepExecutionResponse{
				NextOnboardingStep: woPb.OnboardingStep_ONBOARDING_STEP_CONSOLIDATED_MANUAL_REVIEW,
				CurrentStepDetails: &woPb.OnboardingStepDetails{
					Step:   woPb.OnboardingStep_ONBOARDING_STEP_USER_MITC_CONSENT,
					Status: woPb.OnboardingStepStatus_STEP_STATUS_SKIPPED,
					Metadata: &woPb.OnboardingStepMetadata{
						StepMetaData: &woPb.OnboardingStepMetadata_NoMetadata{},
					},
				},
				OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
				IsLastStep:       false,
			},
		},
		{
			name: "failed to fetch consent details, internal error",
			args: args{
				ctx: context.Background(),
				onbDetails: &woPb.OnboardingDetails{
					ActorId:   "actor_id",
					CreatedAt: timestamp.New(time.Date(2025, 5, 1, 0, 0, 0, 0, time.Local)),
				},
			},
			setupMocks: func(mockConsentClient *mocks.MockConsentClient, mockEvaluator *mock_evaluator.MockIEvaluator) {
				mockConsentClient.EXPECT().FetchConsent(gomock.Any(), &consent.FetchConsentRequest{
					ActorId:     "actor_id",
					ConsentType: consent.ConsentType_FI_WEALTH_MITC_TNC,
					Owner:       commontypes.Owner_OWNER_EPIFI_TECH,
				}).Return(&consent.FetchConsentResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want: &StepExecutionResponse{
				NextOnboardingStep: woPb.OnboardingStep_ONBOARDING_STEP_USER_MITC_CONSENT,
				CurrentStepDetails: &woPb.OnboardingStepDetails{
					Step:      woPb.OnboardingStep_ONBOARDING_STEP_USER_MITC_CONSENT,
					Status:    woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE,
					SubStatus: woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_CONSENT_FETCH_FAILED,
					Metadata: &woPb.OnboardingStepMetadata{
						StepMetaData: &woPb.OnboardingStepMetadata_NoMetadata{},
					},
				},
				OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
				IsLastStep:       false,
			},
			wantErr: true,
		},
		{
			name: "success, consent already recorded",
			args: args{
				ctx: context.Background(),
				onbDetails: &woPb.OnboardingDetails{
					ActorId:   "actor_id",
					CreatedAt: timestamp.New(time.Date(2025, 5, 1, 0, 0, 0, 0, time.Local)),
				},
			},
			setupMocks: func(mockConsentClient *mocks.MockConsentClient, mockEvaluator *mock_evaluator.MockIEvaluator) {
				mockConsentClient.EXPECT().FetchConsent(gomock.Any(), &consent.FetchConsentRequest{
					ActorId:     "actor_id",
					ConsentType: consent.ConsentType_FI_WEALTH_MITC_TNC,
					Owner:       commontypes.Owner_OWNER_EPIFI_TECH,
				}).Return(&consent.FetchConsentResponse{
					Status:      rpc.StatusOk(),
					ClientReqId: "consent_client_req_id",
				}, nil)
			},
			want: &StepExecutionResponse{
				NextOnboardingStep: woPb.OnboardingStep_ONBOARDING_STEP_CONSOLIDATED_MANUAL_REVIEW,
				CurrentStepDetails: &woPb.OnboardingStepDetails{
					Step:   woPb.OnboardingStep_ONBOARDING_STEP_USER_MITC_CONSENT,
					Status: woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					Metadata: &woPb.OnboardingStepMetadata{
						StepMetaData: &woPb.OnboardingStepMetadata_NoMetadata{},
					},
				},
				OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
				IsLastStep:       false,
			},
		},
		{
			name: "failed to check if flag enabled, internal error",
			args: args{
				ctx: context.Background(),
				onbDetails: &woPb.OnboardingDetails{
					ActorId:   "actor_id",
					CreatedAt: timestamp.New(time.Date(2025, 5, 1, 0, 0, 0, 0, time.Local)),
				},
			},
			setupMocks: func(mockConsentClient *mocks.MockConsentClient, mockEvaluator *mock_evaluator.MockIEvaluator) {
				mockConsentClient.EXPECT().FetchConsent(gomock.Any(), &consent.FetchConsentRequest{
					ActorId:     "actor_id",
					ConsentType: consent.ConsentType_FI_WEALTH_MITC_TNC,
					Owner:       commontypes.Owner_OWNER_EPIFI_TECH,
				}).Return(&consent.FetchConsentResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(woPb.WealthOnboardingFeature_WEALTH_ONBOARDING_FEATURE_MITC_CONTENT).WithActorId("actor_id")).Return(false, nil, errors.New("error"))
			},
			want: &StepExecutionResponse{
				NextOnboardingStep: woPb.OnboardingStep_ONBOARDING_STEP_USER_MITC_CONSENT,
				CurrentStepDetails: &woPb.OnboardingStepDetails{
					Step:      woPb.OnboardingStep_ONBOARDING_STEP_USER_MITC_CONSENT,
					Status:    woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE,
					SubStatus: woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_CONSENT_FETCH_FAILED,
					Metadata: &woPb.OnboardingStepMetadata{
						StepMetaData: &woPb.OnboardingStepMetadata_NoMetadata{},
					},
				},
				OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
				IsLastStep:       false,
			},
			wantErr: true,
		},
		{
			name: "success, record wealth mitc consent onboarding step is required and not completed, flag disabled",
			args: args{
				ctx: context.Background(),
				onbDetails: &woPb.OnboardingDetails{
					ActorId:   "actor_id",
					CreatedAt: timestamp.New(time.Date(2025, 5, 1, 0, 0, 0, 0, time.Local)),
				},
			},
			setupMocks: func(mockConsentClient *mocks.MockConsentClient, mockEvaluator *mock_evaluator.MockIEvaluator) {
				mockConsentClient.EXPECT().FetchConsent(gomock.Any(), &consent.FetchConsentRequest{
					ActorId:     "actor_id",
					ConsentType: consent.ConsentType_FI_WEALTH_MITC_TNC,
					Owner:       commontypes.Owner_OWNER_EPIFI_TECH,
				}).Return(&consent.FetchConsentResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(woPb.WealthOnboardingFeature_WEALTH_ONBOARDING_FEATURE_MITC_CONTENT).WithActorId("actor_id")).Return(false, nil, nil)
			},
			want: &StepExecutionResponse{
				NextOnboardingStep: woPb.OnboardingStep_ONBOARDING_STEP_USER_MITC_CONSENT,
				CurrentStepDetails: &woPb.OnboardingStepDetails{
					Step:   woPb.OnboardingStep_ONBOARDING_STEP_USER_MITC_CONSENT,
					Status: woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS,
					Metadata: &woPb.OnboardingStepMetadata{
						StepMetaData: &woPb.OnboardingStepMetadata_NoMetadata{},
					},
				},
				OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
				IsLastStep:       false,
			},
		},
		{
			name: "success, record wealth mitc consent onboarding step is required and not completed, flag enabled",
			args: args{
				ctx: context.Background(),
				onbDetails: &woPb.OnboardingDetails{
					ActorId:   "actor_id",
					CreatedAt: timestamp.New(time.Date(2025, 5, 1, 0, 0, 0, 0, time.Local)),
				},
			},
			setupMocks: func(mockConsentClient *mocks.MockConsentClient, mockEvaluator *mock_evaluator.MockIEvaluator) {
				mockConsentClient.EXPECT().FetchConsent(gomock.Any(), &consent.FetchConsentRequest{
					ActorId:     "actor_id",
					ConsentType: consent.ConsentType_FI_WEALTH_MITC_TNC,
					Owner:       commontypes.Owner_OWNER_EPIFI_TECH,
				}).Return(&consent.FetchConsentResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(woPb.WealthOnboardingFeature_WEALTH_ONBOARDING_FEATURE_MITC_CONTENT).WithActorId("actor_id")).Return(true, nil, nil)
			},
			want: &StepExecutionResponse{
				NextOnboardingStep: woPb.OnboardingStep_ONBOARDING_STEP_USER_MITC_CONSENT,
				CurrentStepDetails: &woPb.OnboardingStepDetails{
					Step:   woPb.OnboardingStep_ONBOARDING_STEP_USER_MITC_CONSENT,
					Status: woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS,
					Metadata: &woPb.OnboardingStepMetadata{
						StepMetaData: &woPb.OnboardingStepMetadata_NoMetadata{},
					},
				},
				Deeplink:         getMITCConsentScreenDeeplink(),
				OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
				IsLastStep:       false,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			mockConsentClient := mocks.NewMockConsentClient(ctrl)
			mockEvaluator := mock_evaluator.NewMockIEvaluator(ctrl)

			r := NewRecordWealthMITCConsent(conf, mockConsentClient, mockEvaluator)
			tt.setupMocks(mockConsentClient, mockEvaluator)
			got, err := r.Perform(tt.args.ctx, tt.args.onbDetails)
			if (err != nil) != tt.wantErr {
				t.Errorf("RecordWealthMITCConsent Perform() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			got.CurrentStepDetails.CompletedAt = nil
			if diff := cmp.Diff(tt.want, got, protocmp.Transform()); diff != "" {
				t.Errorf("RecordWealthMITCConsent Perform() (-want +got):\n%s", diff)
			}
		})
	}
}
