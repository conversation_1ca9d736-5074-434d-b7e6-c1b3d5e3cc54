package steps

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/consent"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/assetandanalysis"
	typesPb "github.com/epifi/gamma/api/typesv2/ui"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/wealthonboarding/config"
	"github.com/epifi/gamma/wealthonboarding/helper"
	"github.com/epifi/gamma/wealthonboarding/release"
)

type RecordWealthMITCConsent struct {
	conf              *config.Config
	consentClient     consent.ConsentClient
	releaseEvaluator  release.IEvaluator
	currentStep       woPb.OnboardingStep
	nextStepOnSuccess woPb.OnboardingStep
}

func NewRecordWealthMITCConsent(conf *config.Config, consentClient consent.ConsentClient, releaseEvaluator release.IEvaluator) *RecordWealthMITCConsent {
	return &RecordWealthMITCConsent{
		conf:              conf,
		consentClient:     consentClient,
		releaseEvaluator:  releaseEvaluator,
		currentStep:       woPb.OnboardingStep_ONBOARDING_STEP_USER_MITC_CONSENT,
		nextStepOnSuccess: woPb.OnboardingStep_ONBOARDING_STEP_CONSOLIDATED_MANUAL_REVIEW,
	}
}

func (r *RecordWealthMITCConsent) Perform(ctx context.Context, details *woPb.OnboardingDetails) (*StepExecutionResponse, error) {
	// check if new advisory agreement MITC consent is required or not
	isRequired := r.checkIfWealthMITCConsentRequired(details)
	if !isRequired {
		logger.Info(ctx, "updated user consent step is not required for the user")
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_SKIPPED, r.currentStep, r.nextStepOnSuccess), nil
	}

	// checking if user has already provided 'FI_WEALTH_MITC_TNC' consent
	// if provided this step is completed for the user
	consentResp, consentErr := r.consentClient.FetchConsent(ctx, &consent.FetchConsentRequest{
		ActorId:     details.GetActorId(),
		ConsentType: consent.ConsentType_FI_WEALTH_MITC_TNC,
		Owner:       commontypes.Owner_OWNER_EPIFI_TECH,
	})
	if rpcErr := epifigrpc.RPCError(consentResp, consentErr); rpcErr != nil && !consentResp.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "failed to fetch consent for user", zap.Error(rpcErr))
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, r.currentStep, r.nextStepOnSuccess).
			WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_CONSENT_FETCH_FAILED), fmt.Errorf("failed to additional step details for step: %v : %w", r.currentStep, rpcErr)
	}

	// no consent found for actor, will redirect user to wealth mitc consent screen if released
	if consentResp.GetStatus().IsRecordNotFound() {
		var deeplink *deeplinkPb.Deeplink
		// TODO: Add app update deeplink once client release to 100%
		nomineeModificationEnabled, _, evalErr := r.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(woPb.WealthOnboardingFeature_WEALTH_ONBOARDING_FEATURE_MITC_CONTENT).WithActorId(details.GetActorId()))
		if evalErr != nil {
			logger.Error(ctx, "error evaluating wealth onboarding feature for mitc content", zap.Error(evalErr))
			return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE, r.currentStep, r.nextStepOnSuccess).
				WithStepSubStatus(woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_CONSENT_FETCH_FAILED), fmt.Errorf("failed to additional step details for step: %v : %w", r.currentStep, evalErr)
		}
		if nomineeModificationEnabled {
			deeplink = getMITCConsentScreenDeeplink()
		}
		return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS, r.currentStep, r.nextStepOnSuccess).WithDeeplink(deeplink), nil
	}
	logger.Info(ctx, "updated user consent step is completed for the user")
	return GetStepExecutionResponse(details, woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED, r.currentStep, r.nextStepOnSuccess), nil
}

// wealth mitc consent is required only for users started wealth onboarding before 'AdvisoryAgreementUpdateAt' time
func (r *RecordWealthMITCConsent) checkIfWealthMITCConsentRequired(onboardingDetails *woPb.OnboardingDetails) bool {
	return onboardingDetails.GetCreatedAt().AsTime().Before(r.conf.AdvisoryAgreement.AdvisoryAgreementUpdateAt)
}

func getMITCConsentScreenDeeplink() *deeplinkPb.Deeplink {
	screenOptionsV2 := deeplinkv3.GetScreenOptionV2WithoutError(&assetandanalysis.GenericScrollableRecordConsentScreenOptions{
		Icon:     commontypes.GetVisualElementFromUrlHeightAndWidth(helper.LockCheckIcon, 96, 75),
		Heading:  commontypes.GetTextFromStringFontColourFontStyle(helper.MITCHeading, colors.ColorSnow, commontypes.FontStyle_HEADLINE_L).WithAlignment(commontypes.Text_ALIGNMENT_CENTER),
		Title:    commontypes.GetTextFromStringFontColourFontStyle(helper.MITCTitle, colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_HEADLINE_M).WithAlignment(commontypes.Text_ALIGNMENT_CENTER),
		SubTitle: commontypes.GetTextFromStringFontColourFontStyle(helper.MITCSubtitle, colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_SUBTITLE_XS).WithAlignment(commontypes.Text_ALIGNMENT_CENTER),
		ContentData: []*typesPb.IconTextComponent{
			typesPb.NewITC().
				WithLeftImageUrlHeightAndWidth(helper.BlackInfoIcon, 24, 24).
				WithTexts(commontypes.GetTextFromHtmlStringFontColourFontStyle(helper.MITCInfoContent, colors.ColorOnLightHighEmphasis, commontypes.FontStyle_SUBTITLE_S).WithAlignment(commontypes.Text_ALIGNMENT_LEFT)).
				WithContainerBackgroundColor(colors.ColorSupportingJade100).WithContainerCornerRadius(12).WithContainerPaddingSymmetrical(16, 12),
			typesPb.NewITC().
				WithTexts(commontypes.GetTextFromStringFontColourFontStyle(helper.MITCContentReview, colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_SUBTITLE_S).WithAlignment(commontypes.Text_ALIGNMENT_LEFT)),
			typesPb.NewITC().
				WithTexts(commontypes.GetTextFromHtmlStringFontColourFontStyle(helper.MITCContent, colors.ColorSnow, commontypes.FontStyle_SUBTITLE_S).WithAlignment(commontypes.Text_ALIGNMENT_LEFT)).
				WithContainerBackgroundColor(colors.ColorNight).WithContainerCornerRadius(20).WithContainerPaddingSymmetrical(16, 16),
			typesPb.NewITC().
				WithTexts(commontypes.GetTextFromHtmlStringFontColourFontStyle(helper.MITCFooterContent, colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_SUBTITLE_S).WithAlignment(commontypes.Text_ALIGNMENT_LEFT)),
		},
		ConsentIds: []string{consent.ConsentType_FI_WEALTH_MITC_TNC.String()},
		SwipeButton: &typesPb.SwipeButton{
			DisplayString: commontypes.GetTextFromStringFontColourFontStyle("I Agree", colors.ColorSnow, commontypes.FontStyle_BUTTON_M),
			BgColor:       colors.ColorForest,
		},
	})
	return &deeplinkPb.Deeplink{
		Screen:          deeplinkPb.Screen_WEALTH_GENERIC_RECORD_CONSENT,
		ScreenOptionsV2: screenOptionsV2,
	}
}
