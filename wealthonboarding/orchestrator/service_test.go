package orchestrator

import (
	"context"
	"fmt"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/timestamppb"

	oncev2Mocks "github.com/epifi/be-common/pkg/counter/once/v2/mocks"
	eventMock "github.com/epifi/be-common/pkg/events/mocks"
	lockMocks "github.com/epifi/be-common/pkg/lock/mocks"
	queueMock "github.com/epifi/be-common/pkg/queue/mocks"
	"github.com/epifi/be-common/pkg/retry"
	retryMock "github.com/epifi/be-common/pkg/retry/mocks"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	testv2 "github.com/epifi/be-common/pkg/test/v2"

	"github.com/epifi/gamma/api/frontend/deeplink"
	mocks2 "github.com/epifi/gamma/api/user/mocks"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/wealthonboarding/config"
	daoImpl "github.com/epifi/gamma/wealthonboarding/dao/impl"
	"github.com/epifi/gamma/wealthonboarding/orchestrator/orchestrator_model"
	"github.com/epifi/gamma/wealthonboarding/orchestrator/steps"
	daoMocks "github.com/epifi/gamma/wealthonboarding/test/mocks/dao"
	mockOrchestrator "github.com/epifi/gamma/wealthonboarding/test/mocks/orchestrator"
	mockSteps "github.com/epifi/gamma/wealthonboarding/test/mocks/orchestrator/steps"
	releaseEvaluatorMocks "github.com/epifi/gamma/wealthonboarding/test/mocks/release"
	mock_user "github.com/epifi/gamma/wealthonboarding/test/mocks/user"
)

var onboardingDetails1 = &woPb.OnboardingDetails{
	ActorId:        "dummy-actor-orchestrator",
	Status:         woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
	CurrentStep:    woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
	OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
	Metadata:       &woPb.OnboardingMetadata{},
}

var onboardingDetails2 = &woPb.OnboardingDetails{
	ActorId:        "dummy-actor-orchestrator",
	Status:         woPb.OnboardingStatus_ONBOARDING_STATUS_PENDING,
	CurrentStep:    woPb.OnboardingStep_ONBOARDING_STEP_USER_CONSENT,
	OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
	Metadata:       &woPb.OnboardingMetadata{},
}

var onboardingDetailsManualIntervention = &woPb.OnboardingDetails{
	ActorId:        "dummy-actor-orchestrator",
	Status:         woPb.OnboardingStatus_ONBOARDING_STATUS_MANUAL_INTERVENTION_NEEDED,
	CurrentStep:    woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
	OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
	Metadata:       &woPb.OnboardingMetadata{},
}

var onboardingDetailsFutureScope = &woPb.OnboardingDetails{
	ActorId:        "dummy-actor-orchestrator",
	Status:         woPb.OnboardingStatus_ONBOARDING_STATUS_FUTURE_SCOPE,
	CurrentStep:    woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
	OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
	Metadata:       &woPb.OnboardingMetadata{},
}

var odPanVerificationManualIntervention = &woPb.OnboardingDetails{
	ActorId:        "dummy-actor-orchestrator",
	Status:         woPb.OnboardingStatus_ONBOARDING_STATUS_MANUAL_INTERVENTION_NEEDED,
	CurrentStep:    woPb.OnboardingStep_ONBOARDING_STEP_PAN_VERIFICATION,
	OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
	Metadata:       &woPb.OnboardingMetadata{},
}

var odPanVerificationFutureScope = &woPb.OnboardingDetails{
	ActorId:        "dummy-actor-orchestrator",
	Status:         woPb.OnboardingStatus_ONBOARDING_STATUS_FUTURE_SCOPE,
	CurrentStep:    woPb.OnboardingStep_ONBOARDING_STEP_PAN_VERIFICATION,
	OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
	Metadata:       &woPb.OnboardingMetadata{},
}

var odPanVerificationInProgress = &woPb.OnboardingDetails{
	ActorId:        "dummy-actor-orchestrator",
	Status:         woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
	CurrentStep:    woPb.OnboardingStep_ONBOARDING_STEP_PAN_VERIFICATION,
	OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
	Metadata:       &woPb.OnboardingMetadata{},
}

var odKraFetchInProgress = &woPb.OnboardingDetails{
	ActorId:        "dummy-actor-orchestrator",
	Status:         woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
	CurrentStep:    woPb.OnboardingStep_ONBOARDING_STEP_FETCH_KYC_INFO_FROM_KRA,
	OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
	Metadata:       &woPb.OnboardingMetadata{},
}

var odKraFetchManualIntervention = &woPb.OnboardingDetails{
	ActorId:        "dummy-actor-orchestrator",
	Status:         woPb.OnboardingStatus_ONBOARDING_STATUS_MANUAL_INTERVENTION_NEEDED,
	CurrentStep:    woPb.OnboardingStep_ONBOARDING_STEP_FETCH_KYC_INFO_FROM_KRA,
	OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
	Metadata:       &woPb.OnboardingMetadata{},
}

var odKraFetchFutureScope = &woPb.OnboardingDetails{
	ActorId:        "dummy-actor-orchestrator",
	Status:         woPb.OnboardingStatus_ONBOARDING_STATUS_FUTURE_SCOPE,
	CurrentStep:    woPb.OnboardingStep_ONBOARDING_STEP_FETCH_KYC_INFO_FROM_KRA,
	OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
	Metadata:       &woPb.OnboardingMetadata{},
}

var odTncInProgress = &woPb.OnboardingDetails{
	ActorId:        "dummy-actor-orchestrator",
	Status:         woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
	CurrentStep:    woPb.OnboardingStep_ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP,
	OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
	Metadata:       &woPb.OnboardingMetadata{},
}

var onboardingComplete = &woPb.OnboardingDetails{
	ActorId:        "dummy-actor-orchestrator",
	Status:         woPb.OnboardingStatus_ONBOARDING_STATUS_COMPLETED,
	CurrentStep:    woPb.OnboardingStep_ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP,
	OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
	Metadata:       &woPb.OnboardingMetadata{},
}

func TestService_Orchestrate(t *testing.T) {
	ctr := gomock.NewController(t)

	mockStep := mockSteps.NewMockIStep(ctr)
	mockFactory := mockOrchestrator.NewMockIHandlerFactory(ctr)
	mockUser := mock_user.NewMockIService(ctr)
	mockDoOnce := oncev2Mocks.NewMockDoOnce(ctr)
	brokerMock := eventMock.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	mockDoOnce.EXPECT().IsDone(gomock.Any(), gomock.Any()).Return(true, nil).AnyTimes() // do-once tasks are tested separately
	idempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(daoTestSuite.db)
	mockLock := lockMocks.NewMockILock(ctr)
	mockLock.EXPECT().Release(gomock.Any()).Return(nil).AnyTimes()
	mockLockManager := lockMocks.NewMockILockManager(ctr)
	mockLockManager.EXPECT().GetLock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockLock, nil).AnyTimes()
	mockReleaseEvaluator := releaseEvaluatorMocks.NewMockIEvaluator(ctr)
	mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(false, nil, nil).AnyTimes()
	mockUserClient := mocks2.NewMockUsersClient(ctr)

	type args struct {
		ctx     context.Context
		actorId string
		onbType woPb.OnboardingType
	}
	tests := []struct {
		name      string
		args      args
		want      *woPb.OnboardingDetails
		want1     *deeplink.Deeplink
		wantErr   bool
		mocksFunc func()
	}{
		{
			name: "user consent deeplink",
			args: args{
				ctx:     context.Background(),
				actorId: "dummy-actor-orchestrator",
				onbType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
			},
			want: onboardingDetails2,
			want1: &deeplink.Deeplink{
				Screen: deeplink.Screen_WEALTH_ONBOARDING_STATUS_SCREEN,
			},
			wantErr: false,
			mocksFunc: func() {
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				ser := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS,
					woPb.OnboardingStep_ONBOARDING_STEP_USER_CONSENT,
					woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
				)
				ser.Deeplink = &deeplink.Deeplink{
					Screen: deeplink.Screen_WEALTH_ONBOARDING_STATUS_SCREEN,
				}
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(ser, nil)
			},
		},
		{
			name: "data collection deeplink",
			args: args{
				ctx:     context.Background(),
				actorId: "dummy-actor-orchestrator",
				onbType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
			},
			want: onboardingDetails1,
			want1: &deeplink.Deeplink{
				Screen: deeplink.Screen_WEALTH_ONBOARDING_STATUS_SCREEN,
			},
			wantErr: false,
			mocksFunc: func() {
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				userConsentExecutionResponse := steps.GetStepExecutionResponse(
					onboardingDetails2,
					woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					woPb.OnboardingStep_ONBOARDING_STEP_USER_CONSENT,
					woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(userConsentExecutionResponse, nil)
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				ser := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS,
					woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
					woPb.OnboardingStep_ONBOARDING_STEP_PAN_VERIFICATION,
				)
				ser.Deeplink = &deeplink.Deeplink{
					Screen: deeplink.Screen_WEALTH_ONBOARDING_STATUS_SCREEN,
				}
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(ser, nil)
			},
		},
		{
			name: "data collection in progress with no deeplink",
			args: args{
				ctx:     context.Background(),
				actorId: "dummy-actor-orchestrator",
				onbType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
			},
			want:    onboardingDetails1,
			want1:   nil,
			wantErr: false,
			mocksFunc: func() {
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				userConsentExecutionResponse := steps.GetStepExecutionResponse(
					onboardingDetails2,
					woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					woPb.OnboardingStep_ONBOARDING_STEP_USER_CONSENT,
					woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(userConsentExecutionResponse, nil)
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				ser := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS,
					woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
					woPb.OnboardingStep_ONBOARDING_STEP_PAN_VERIFICATION,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(ser, nil)
			},
		},
		{
			name: "data collection returns manual intervention",
			args: args{
				ctx:     context.Background(),
				actorId: "dummy-actor-orchestrator",
				onbType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
			},
			want:    onboardingDetailsManualIntervention,
			want1:   nil,
			wantErr: false,
			mocksFunc: func() {
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				userConsentExecutionResponse := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					woPb.OnboardingStep_ONBOARDING_STEP_USER_CONSENT,
					woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(userConsentExecutionResponse, nil)
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				ser := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_MANUAL_INTERVENTION_NEEDED,
					woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
					woPb.OnboardingStep_ONBOARDING_STEP_PAN_VERIFICATION,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(ser, nil)
			},
		},
		{
			name: "data collection return future scope",
			args: args{
				ctx:     context.Background(),
				actorId: "dummy-actor-orchestrator",
				onbType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
			},
			want:    onboardingDetailsFutureScope,
			want1:   nil,
			wantErr: false,
			mocksFunc: func() {
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				userConsentExecutionResponse := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					woPb.OnboardingStep_ONBOARDING_STEP_USER_CONSENT,
					woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(userConsentExecutionResponse, nil)
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				ser := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE,
					woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
					woPb.OnboardingStep_ONBOARDING_STEP_PAN_VERIFICATION,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(ser, nil)
			},
		},
		{
			name: "pan verification manual intervention",
			args: args{
				ctx:     context.Background(),
				actorId: "dummy-actor-orchestrator",
				onbType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
			},
			want:    odPanVerificationManualIntervention,
			want1:   nil,
			wantErr: false,
			mocksFunc: func() {
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				userConsentExecutionResponse := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					woPb.OnboardingStep_ONBOARDING_STEP_USER_CONSENT,
					woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(userConsentExecutionResponse, nil)
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				dataCollectionExecutionResponse := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
					woPb.OnboardingStep_ONBOARDING_STEP_PAN_VERIFICATION,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(dataCollectionExecutionResponse, nil)
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				panVerificationExecutionResponse := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_MANUAL_INTERVENTION_NEEDED,
					woPb.OnboardingStep_ONBOARDING_STEP_PAN_VERIFICATION,
					woPb.OnboardingStep_ONBOARDING_STEP_FETCH_KYC_INFO_FROM_KRA,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(panVerificationExecutionResponse, nil)
			},
		},
		{
			name: "pan verification future scope",
			args: args{
				ctx:     context.Background(),
				actorId: "dummy-actor-orchestrator",
				onbType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
			},
			want:    odPanVerificationFutureScope,
			want1:   nil,
			wantErr: false,
			mocksFunc: func() {
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				userConsentExecutionResponse := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					woPb.OnboardingStep_ONBOARDING_STEP_USER_CONSENT,
					woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(userConsentExecutionResponse, nil)
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				dataCollectionExecutionResponse := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
					woPb.OnboardingStep_ONBOARDING_STEP_PAN_VERIFICATION,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(dataCollectionExecutionResponse, nil)
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				panVerificationExecutionResponse := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE,
					woPb.OnboardingStep_ONBOARDING_STEP_PAN_VERIFICATION,
					woPb.OnboardingStep_ONBOARDING_STEP_FETCH_KYC_INFO_FROM_KRA,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(panVerificationExecutionResponse, nil)
			},
		},
		{
			name: "pan verification in progress",
			args: args{
				ctx:     context.Background(),
				actorId: "dummy-actor-orchestrator",
				onbType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
			},
			want:    odPanVerificationInProgress,
			want1:   nil,
			wantErr: false,
			mocksFunc: func() {
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				userConsentExecutionResponse := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					woPb.OnboardingStep_ONBOARDING_STEP_USER_CONSENT,
					woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(userConsentExecutionResponse, nil)
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				dataCollectionExecutionResponse := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
					woPb.OnboardingStep_ONBOARDING_STEP_PAN_VERIFICATION,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(dataCollectionExecutionResponse, nil)
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				panVerificationExecutionResponse := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS,
					woPb.OnboardingStep_ONBOARDING_STEP_PAN_VERIFICATION,
					woPb.OnboardingStep_ONBOARDING_STEP_FETCH_KYC_INFO_FROM_KRA,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(panVerificationExecutionResponse, nil)
			},
		},
		{
			name: "fetch kra in progress",
			args: args{
				ctx:     context.Background(),
				actorId: "dummy-actor-orchestrator",
				onbType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
			},
			want:    odKraFetchInProgress,
			want1:   nil,
			wantErr: false,
			mocksFunc: func() {
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				userConsentExecutionResponse := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					woPb.OnboardingStep_ONBOARDING_STEP_USER_CONSENT,
					woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(userConsentExecutionResponse, nil)
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				dataCollectionExecutionResponse := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
					woPb.OnboardingStep_ONBOARDING_STEP_PAN_VERIFICATION,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(dataCollectionExecutionResponse, nil)
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				panVerificationExecutionResponse := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					woPb.OnboardingStep_ONBOARDING_STEP_PAN_VERIFICATION,
					woPb.OnboardingStep_ONBOARDING_STEP_FETCH_KYC_INFO_FROM_KRA,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(panVerificationExecutionResponse, nil)
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				kraFetchExecutionResponse := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS,
					woPb.OnboardingStep_ONBOARDING_STEP_FETCH_KYC_INFO_FROM_KRA,
					woPb.OnboardingStep_ONBOARDING_STEP_COLLECT_MISSING_PERSONAL_INFO,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(kraFetchExecutionResponse, nil)
			},
		},
		{
			name: "fetch kra manual intervention",
			args: args{
				ctx:     context.Background(),
				actorId: "dummy-actor-orchestrator",
				onbType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
			},
			want:    odKraFetchManualIntervention,
			want1:   nil,
			wantErr: false,
			mocksFunc: func() {
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				userConsentExecutionResponse := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					woPb.OnboardingStep_ONBOARDING_STEP_USER_CONSENT,
					woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(userConsentExecutionResponse, nil)
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				dataCollectionExecutionResponse := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
					woPb.OnboardingStep_ONBOARDING_STEP_PAN_VERIFICATION,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(dataCollectionExecutionResponse, nil)
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				panVerificationExecutionResponse := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					woPb.OnboardingStep_ONBOARDING_STEP_PAN_VERIFICATION,
					woPb.OnboardingStep_ONBOARDING_STEP_FETCH_KYC_INFO_FROM_KRA,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(panVerificationExecutionResponse, nil)
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				kraFetchExecutionResponse := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_MANUAL_INTERVENTION_NEEDED,
					woPb.OnboardingStep_ONBOARDING_STEP_FETCH_KYC_INFO_FROM_KRA,
					woPb.OnboardingStep_ONBOARDING_STEP_COLLECT_MISSING_PERSONAL_INFO,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(kraFetchExecutionResponse, nil)
			},
		},
		{
			name: "fetch kra future scope",
			args: args{
				ctx:     context.Background(),
				actorId: "dummy-actor-orchestrator",
				onbType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
			},
			want:    odKraFetchFutureScope,
			want1:   nil,
			wantErr: false,
			mocksFunc: func() {
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				userConsentExecutionResponse := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					woPb.OnboardingStep_ONBOARDING_STEP_USER_CONSENT,
					woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(userConsentExecutionResponse, nil)
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				dataCollectionExecutionResponse := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
					woPb.OnboardingStep_ONBOARDING_STEP_PAN_VERIFICATION,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(dataCollectionExecutionResponse, nil)
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				panVerificationExecutionResponse := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					woPb.OnboardingStep_ONBOARDING_STEP_PAN_VERIFICATION,
					woPb.OnboardingStep_ONBOARDING_STEP_FETCH_KYC_INFO_FROM_KRA,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(panVerificationExecutionResponse, nil)
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				kraFetchExecutionResponse := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE,
					woPb.OnboardingStep_ONBOARDING_STEP_FETCH_KYC_INFO_FROM_KRA,
					woPb.OnboardingStep_ONBOARDING_STEP_COLLECT_MISSING_PERSONAL_INFO,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(kraFetchExecutionResponse, nil)
			},
		},
		{
			name: "kra fetch completed(customer data already present with kra), tnc step return deeplink",
			args: args{
				ctx:     context.Background(),
				actorId: "dummy-actor-orchestrator",
				onbType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
			},
			want: odTncInProgress,
			want1: &deeplink.Deeplink{
				Screen: deeplink.Screen_WEALTH_ONBOARDING_AGREEMENT_CONFIRMATION_SCREEN,
				ScreenOptions: &deeplink.Deeplink_WealthOnboardingSignAgreementScreenOptions{
					WealthOnboardingSignAgreementScreenOptions: &deeplink.WealthOnboardingSignAgreementScreenOptions{
						TncUrl: "TncURL",
					},
				},
			},
			wantErr: false,
			mocksFunc: func() {
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				userConsentExecutionResponse := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					woPb.OnboardingStep_ONBOARDING_STEP_USER_CONSENT,
					woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(userConsentExecutionResponse, nil)
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				dataCollectionExecutionResponse := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
					woPb.OnboardingStep_ONBOARDING_STEP_PAN_VERIFICATION,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(dataCollectionExecutionResponse, nil)
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				panVerificationExecutionResponse := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					woPb.OnboardingStep_ONBOARDING_STEP_PAN_VERIFICATION,
					woPb.OnboardingStep_ONBOARDING_STEP_FETCH_KYC_INFO_FROM_KRA,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(panVerificationExecutionResponse, nil)
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				kraFetchExecutionResponse := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					woPb.OnboardingStep_ONBOARDING_STEP_FETCH_KYC_INFO_FROM_KRA,
					woPb.OnboardingStep_ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(kraFetchExecutionResponse, nil)
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				tncExecutionResponse := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS,
					woPb.OnboardingStep_ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP,
					woPb.OnboardingStep_ONBOARDING_STEP_UNSPECIFIED,
				)
				tncExecutionResponse.Deeplink = &deeplink.Deeplink{
					Screen: deeplink.Screen_WEALTH_ONBOARDING_AGREEMENT_CONFIRMATION_SCREEN,
					ScreenOptions: &deeplink.Deeplink_WealthOnboardingSignAgreementScreenOptions{
						WealthOnboardingSignAgreementScreenOptions: &deeplink.WealthOnboardingSignAgreementScreenOptions{
							TncUrl: "TncURL",
						},
					},
				}
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(tncExecutionResponse, nil)
			},
		},
		{
			name: "onboarding complete",
			args: args{
				ctx:     context.Background(),
				actorId: "dummy-actor-orchestrator",
				onbType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
			},
			want:    onboardingComplete,
			want1:   nil,
			wantErr: false,
			mocksFunc: func() {
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				userConsentExecutionResponse := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					woPb.OnboardingStep_ONBOARDING_STEP_USER_CONSENT,
					woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(userConsentExecutionResponse, nil)
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				dataCollectionExecutionResponse := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
					woPb.OnboardingStep_ONBOARDING_STEP_PAN_VERIFICATION,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(dataCollectionExecutionResponse, nil)
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				panVerificationExecutionResponse := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					woPb.OnboardingStep_ONBOARDING_STEP_PAN_VERIFICATION,
					woPb.OnboardingStep_ONBOARDING_STEP_FETCH_KYC_INFO_FROM_KRA,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(panVerificationExecutionResponse, nil)
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				kraFetchExecutionResponse := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					woPb.OnboardingStep_ONBOARDING_STEP_FETCH_KYC_INFO_FROM_KRA,
					woPb.OnboardingStep_ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(kraFetchExecutionResponse, nil)
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				tncExecutionResponse := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					woPb.OnboardingStep_ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP,
					woPb.OnboardingStep_ONBOARDING_STEP_USER_MITC_CONSENT,
				).WithIsLastStep(true)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(tncExecutionResponse, nil)
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				missingDataCollectionRes := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					woPb.OnboardingStep_ONBOARDING_STEP_COLLECT_MISSING_PERSONAL_INFO,
					woPb.OnboardingStep_ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP,
				).WithIsLastStep(true)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(missingDataCollectionRes, nil)
				mockUser.EXPECT().CreateOrUpdateUser(gomock.Any(), gomock.Any(), nil).Return(nil)

				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				nomineeModificationRes := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					woPb.OnboardingStep_ONBOARDING_STEP_UPDATE_NOMINEE_DETAILS,
					woPb.OnboardingStep_ONBOARDING_STEP_ADVISORY_AGREEMENT,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(nomineeModificationRes, nil)

				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				mitcConsentStep := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					woPb.OnboardingStep_ONBOARDING_STEP_USER_MITC_CONSENT,
					woPb.OnboardingStep_ONBOARDING_STEP_UNSPECIFIED,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(mitcConsentStep, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mocksFunc()
			testv2.PrepareRandomScopedCrdbDatabase(t, daoTestSuite.conf.EpifiDb, daoTestSuite.dbName, daoTestSuite.db, AffectedTestTables, &initialiseSameDbOnce)
			onboardingDetailsDao := daoImpl.NewCrdbOnboardingDetailsDao(daoTestSuite.db)
			onboardingStepDetailsDao := daoImpl.NewCrdbOnboardingStepDetailsDao(daoTestSuite.db)
			s := &Service{
				onboardingDetailsDao:     onboardingDetailsDao,
				onboardingStepDetailsDao: onboardingStepDetailsDao,
				handlerFactory:           mockFactory,
				userService:              mockUser,
				conf:                     conf,
				eventBroker:              brokerMock,
				doOnce:                   mockDoOnce,
				idempotentTxnExecutor:    idempotentTxnExecutor,
				distributedLockManager:   mockLockManager,
				releaseEvaluator:         mockReleaseEvaluator,
				userClient:               mockUserClient,
			}
			got, got1, err := s.Orchestrate(tt.args.ctx, &orchestrator_model.OrchestrateRequest{
				ActorId: tt.args.actorId,
				OnbType: tt.args.onbType,
			})
			if (err != nil) != tt.wantErr {
				t.Errorf("Orchestrate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&woPb.OnboardingDetails{}, "created_at", "updated_at", "completed_at", "id"),
			}
			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("Orchestrate() mismatch (-want +got):\n%s", diff)
			}
			if diff := cmp.Diff(tt.want1, got1, protocmp.Transform()); diff != "" {
				t.Errorf("Orchestrate() mismatch (-want +got):\n%s", diff)
			}
		})
	}
}

func TestService_OrchestrateNomineeDeclarationFeature(t *testing.T) {
	ctr := gomock.NewController(t)

	mockLock := lockMocks.NewMockILock(ctr)
	mockLock.EXPECT().Release(gomock.Any()).Return(nil).AnyTimes()
	mockLockManager := lockMocks.NewMockILockManager(ctr)
	mockLockManager.EXPECT().GetLock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockLock, nil).AnyTimes()
	mockOnbDetailsDao := daoMocks.NewMockOnboardingDetailsDao(ctr)
	mockFactory := mockOrchestrator.NewMockIHandlerFactory(ctr)
	mockStep := mockSteps.NewMockIStep(ctr)

	nomineeDeeplink := &deeplink.Deeplink{
		Screen: deeplink.Screen_WEALTH_ONBOARDING_CAPTURE_MISSING_DATA_SCREEN,
		ScreenOptions: &deeplink.Deeplink_WealthOnboardingCaptureMissingDataScreenOptions{
			WealthOnboardingCaptureMissingDataScreenOptions: &deeplink.WealthOnboardingCaptureMissingDataScreenOptions{
				MissingData: []*deeplink.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus{
					{
						MissingDataDeeplink: &deeplink.Deeplink{
							Screen: deeplink.Screen_COLLECT_NOMINEE_DETAILS_SCREEN,
							ScreenOptions: &deeplink.Deeplink_CollectNomineeDetailsScreenOptions{
								CollectNomineeDetailsScreenOptions: &deeplink.CollectNomineeDetailsScreenOptions{
									Title:       "Choose a nominee",
									MaxNominees: 1,
									OptOutText:  "I want to continue without a nominee",
								},
							},
						},
						Status:      deeplink.WealthOnboardingDataStatus_WEALTH_ONBOARDING_DATA_STATUS_ENABLED,
						DisplayText: "Nominee details",
					},
				},
			},
		},
	}

	type args struct {
		ctx     context.Context
		actorId string
		onbType woPb.OnboardingType
	}
	tests := []struct {
		name       string
		args       args
		want       *woPb.OnboardingDetails
		want1      *deeplink.Deeplink
		wantErr    bool
		setupMocks func()
	}{
		{
			name: "collect nominee for onboarding completed users",
			args: args{
				ctx:     context.Background(),
				actorId: "dummy-actor-orchestrator",
				onbType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
			},
			want:  onboardingComplete,
			want1: nomineeDeeplink,
			setupMocks: func() {
				mockOnbDetailsDao.EXPECT().GetOrCreate(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(onboardingComplete, false, nil)
				mockFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				ser := steps.GetStepExecutionResponse(
					onboardingComplete,
					woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS,
					woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
					woPb.OnboardingStep_ONBOARDING_STEP_PAN_VERIFICATION,
				)
				ser.Deeplink = nomineeDeeplink
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(ser, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMocks()
			s := &Service{
				conf:                   conf,
				onboardingDetailsDao:   mockOnbDetailsDao,
				distributedLockManager: mockLockManager,
				handlerFactory:         mockFactory,
			}
			got, got1, err := s.Orchestrate(tt.args.ctx, &orchestrator_model.OrchestrateRequest{
				ActorId: tt.args.actorId,
				OnbType: tt.args.onbType,
			})
			if (err != nil) != tt.wantErr {
				t.Errorf("Orchestrate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&woPb.OnboardingDetails{}, "created_at", "updated_at", "completed_at", "id"),
			}
			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("Orchestrate() mismatch (-want +got):\n%s", diff)
			}

			if diff := cmp.Diff(tt.want1, got1, protocmp.Transform()); diff != "" {
				t.Errorf("Orchestrate() mismatch (-want +got):\n%s", diff)
			}
		})
	}
}

func TestService_OrchestrateParallel(t *testing.T) {
	ctr := gomock.NewController(t)

	mockLockManager := lockMocks.NewMockILockManager(ctr)

	type args struct {
		ctx     context.Context
		actorId string
		onbType woPb.OnboardingType
	}
	tests := []struct {
		name      string
		args      args
		wantErr   bool
		wantOD    *woPb.OnboardingDetails
		wantDl    *deeplink.Deeplink
		mocksFunc func()
	}{
		{
			name: "throw error if lock not acquired",
			args: args{
				ctx:     context.Background(),
				actorId: "dummy-actor-orchestrator",
				onbType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
			},
			wantOD: &woPb.OnboardingDetails{
				ActorId:        "dummy-actor-orchestrator",
				Status:         woPb.OnboardingStatus_ONBOARDING_STATUS_PENDING,
				CurrentStep:    woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
				OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
				Metadata:       &woPb.OnboardingMetadata{},
			},
			wantDl: &deeplink.Deeplink{Screen: deeplink.Screen_WEALTH_ONBOARDING_LANDING_SCREEN},
			mocksFunc: func() {
				mockLockManager.EXPECT().GetLock(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("lock already present"))
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mocksFunc()
			testv2.PrepareRandomScopedCrdbDatabase(t, daoTestSuite.conf.EpifiDb, daoTestSuite.dbName, daoTestSuite.db, AffectedTestTables, &initialiseSameDbOnce)
			onboardingDetailsDao := daoImpl.NewCrdbOnboardingDetailsDao(daoTestSuite.db)
			onboardingStepDetailsDao := daoImpl.NewCrdbOnboardingStepDetailsDao(daoTestSuite.db)
			s := &Service{
				onboardingDetailsDao:     onboardingDetailsDao,
				onboardingStepDetailsDao: onboardingStepDetailsDao,
				conf:                     conf,
				distributedLockManager:   mockLockManager,
			}
			_, _, err := s.Orchestrate(tt.args.ctx, &orchestrator_model.OrchestrateRequest{
				ActorId: tt.args.actorId,
				OnbType: tt.args.onbType,
			})
			if (err != nil) != tt.wantErr {
				t.Errorf("Orchestrate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestGetNotificationConfigsForCurrentUserState(t *testing.T) {
	type args struct {
		cfg            *config.Config
		onbDetails     *woPb.OnboardingDetails
		onbStepDetails *woPb.OnboardingStepDetails
	}
	sampleOnbDetails := &woPb.OnboardingDetails{CurrentStep: woPb.OnboardingStep_ONBOARDING_STEP_DOWNLOAD_KRA_DOC}
	sampleOnbStepDetails := &woPb.OnboardingStepDetails{
		Status:    woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS,
		SubStatus: woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_EMPTY_DOWNLOAD_API_RESPONSE,
	}
	tests := []struct {
		name string
		args args
		want []*config.DelayedNotification
	}{
		{
			name: "return notifications as per config",
			args: args{
				cfg:            conf,
				onbDetails:     sampleOnbDetails,
				onbStepDetails: sampleOnbStepDetails,
			},
			want: []*config.DelayedNotification{
				conf.UserComms[0].Notifications[0],
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := GetNotificationConfigsForCurrentUserState(tt.args.cfg, tt.args.onbDetails, tt.args.onbStepDetails)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetNotificationConfigsForCurrentUserState() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_setupUserNotifications(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockDoOnce := oncev2Mocks.NewMockDoOnce(ctrl)
	mockDelayPublisher := queueMock.NewMockDelayPublisher(ctrl)

	type args struct {
		ctx            context.Context
		onbDetails     *woPb.OnboardingDetails
		onbStepDetails *woPb.OnboardingStepDetails
	}
	sampleOnbDetails := &woPb.OnboardingDetails{
		ActorId:        "dummy-actor-orchestrator",
		Status:         woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
		CurrentStep:    woPb.OnboardingStep_ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP,
		OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
	}
	sampleOnbStepDetails := &woPb.OnboardingStepDetails{
		Step:      woPb.OnboardingStep_ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP,
		Status:    woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS,
		SubStatus: woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_UNSPECIFIED,
	}
	tests := []struct {
		name       string
		args       args
		wantErr    bool
		setupMocks func()
	}{
		{
			name: "setup notification if present in config and not done",
			args: args{
				ctx:            context.Background(),
				onbDetails:     sampleOnbDetails,
				onbStepDetails: sampleOnbStepDetails,
			},
			wantErr: false,
			setupMocks: func() {
				mockDoOnce.EXPECT().IsDone(gomock.Any(), gomock.Any()).Return(false, nil).Times(2)
				mockDelayPublisher.EXPECT().PublishWithDelay(gomock.Any(), gomock.Any(), gomock.Any()).Return("msg-id", nil).Times(2)
				mockDoOnce.EXPECT().Do(gomock.Any(), gomock.Any()).Return(nil).Times(2)
			},
		},
		{
			name: "do not send notification if already done",
			args: args{
				ctx:            context.Background(),
				onbDetails:     sampleOnbDetails,
				onbStepDetails: sampleOnbStepDetails,
			},
			wantErr: false,
			setupMocks: func() {
				mockDoOnce.EXPECT().IsDone(gomock.Any(), gomock.Any()).Return(true, nil).Times(2)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				conf:                    conf,
				userCommsDelayPublisher: mockDelayPublisher,
				doOnce:                  mockDoOnce,
			}
			tt.setupMocks()
			if err := s.setupUserNotifications(tt.args.ctx, tt.args.onbDetails, tt.args.onbStepDetails); (err != nil) != tt.wantErr {
				t.Errorf("setupUserNotifications() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestService_SetupRetry(t *testing.T) {
	samplePrevRetryTimeBeforeNow := time.Now().Add(-1 * time.Hour)
	samplePrevRetryTimeAfterNow := time.Now().Add(1 * time.Hour)
	ctr := gomock.NewController(t)
	mockStepsRetryDelayPublisher := queueMock.NewMockDelayPublisher(ctr)
	mockRetryStrategy := retryMock.NewMockRetryStrategy(ctr)
	type args struct {
		ctx      context.Context
		rs       retry.RetryStrategy
		seRes    *steps.StepExecutionResponse
		stepData *woPb.OnboardingStepDetails
		fm       []woPb.OnboardingStepDetailsFieldMask
		od       *woPb.OnboardingDetails
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func()
		want       []woPb.OnboardingStepDetailsFieldMask
	}{
		{
			name: "setup first time retry if none exist",
			args: args{
				ctx:   context.Background(),
				rs:    mockRetryStrategy,
				seRes: &steps.StepExecutionResponse{CurrentStepDetails: &woPb.OnboardingStepDetails{}},
			},
			setupMocks: func() {
				mockRetryStrategy.EXPECT().GetNextRetryIntervalDuration(gomock.Any()).Return(6 * time.Hour)
				mockRetryStrategy.EXPECT().IsMaxRetryMet(gomock.Any()).Return(false)
				mockStepsRetryDelayPublisher.EXPECT().PublishWithDelay(gomock.Any(), gomock.Any(), gomock.Any()).Return("", nil)
			},
			want: []woPb.OnboardingStepDetailsFieldMask{
				woPb.OnboardingStepDetailsFieldMask_ONBOARDING_STEP_DETAILS_FIELD_MASK_NUM_ATTEMPTS,
				woPb.OnboardingStepDetailsFieldMask_ONBOARDING_STEP_DETAILS_FIELD_MASK_EXPECTED_RETRY_AT,
			},
		},
		{
			name: "setup retry if prev retry time already exists and has passed",
			args: args{
				ctx:      context.Background(),
				rs:       mockRetryStrategy,
				seRes:    &steps.StepExecutionResponse{CurrentStepDetails: &woPb.OnboardingStepDetails{}},
				stepData: &woPb.OnboardingStepDetails{ExpectedRetryAt: timestamppb.New(samplePrevRetryTimeBeforeNow)},
			},
			setupMocks: func() {
				mockRetryStrategy.EXPECT().GetNextRetryIntervalDuration(gomock.Any()).Return(6 * time.Hour)
				mockRetryStrategy.EXPECT().IsMaxRetryMet(gomock.Any()).Return(false)
				timeUntilNextRetryTime := time.Until(samplePrevRetryTimeBeforeNow.Add(6 * time.Hour).Add(10 * time.Second).Round(time.Second)).Round(time.Second)
				mockStepsRetryDelayPublisher.EXPECT().PublishWithDelay(gomock.Any(), gomock.Any(), timeUntilNextRetryTime).Return("", nil)
			},
			want: []woPb.OnboardingStepDetailsFieldMask{
				woPb.OnboardingStepDetailsFieldMask_ONBOARDING_STEP_DETAILS_FIELD_MASK_NUM_ATTEMPTS,
				woPb.OnboardingStepDetailsFieldMask_ONBOARDING_STEP_DETAILS_FIELD_MASK_EXPECTED_RETRY_AT,
			},
		},
		{
			name: "do not retry if prev retry time is in future",
			args: args{
				ctx:      context.Background(),
				rs:       mockRetryStrategy,
				seRes:    &steps.StepExecutionResponse{CurrentStepDetails: &woPb.OnboardingStepDetails{}},
				stepData: &woPb.OnboardingStepDetails{ExpectedRetryAt: timestamppb.New(samplePrevRetryTimeAfterNow)},
			},
			setupMocks: func() {
				mockRetryStrategy.EXPECT().GetNextRetryIntervalDuration(gomock.Any()).Return(6 * time.Hour)
			},
			want: nil,
		},
		{
			name: "do no retry if num of retries exhausted",
			args: args{
				ctx:   context.Background(),
				rs:    mockRetryStrategy,
				seRes: &steps.StepExecutionResponse{CurrentStepDetails: &woPb.OnboardingStepDetails{}},
			},
			setupMocks: func() {
				mockRetryStrategy.EXPECT().GetNextRetryIntervalDuration(gomock.Any()).Return(6 * time.Hour)
				mockRetryStrategy.EXPECT().IsMaxRetryMet(gomock.Any()).Return(true)
			},
			want: []woPb.OnboardingStepDetailsFieldMask{
				woPb.OnboardingStepDetailsFieldMask_ONBOARDING_STEP_DETAILS_FIELD_MASK_NUM_ATTEMPTS,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				stepsRetryDelayPublisher: mockStepsRetryDelayPublisher,
			}
			tt.setupMocks()
			if got := s.setupRetry(tt.args.ctx, tt.args.rs, tt.args.seRes, tt.args.stepData, tt.args.fm, tt.args.od); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("setupRetry() = %v, want %v", got, tt.want)
			}
		})
	}
}
