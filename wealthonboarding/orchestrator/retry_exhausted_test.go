package orchestrator

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/rpc"
	eventMocks "github.com/epifi/be-common/pkg/events/mocks"
	lockMocks "github.com/epifi/be-common/pkg/lock/mocks"
	testV2 "github.com/epifi/be-common/pkg/test/v2"

	"github.com/epifi/gamma/api/auth"
	authMocks "github.com/epifi/gamma/api/auth/mocks"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/wealthonboarding"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	daoImpls "github.com/epifi/gamma/wealthonboarding/dao/impl"
	"github.com/epifi/gamma/wealthonboarding/orchestrator/orchestrator_model"
	"github.com/epifi/gamma/wealthonboarding/orchestrator/steps"
	daoMocks "github.com/epifi/gamma/wealthonboarding/test/mocks/dao"
	orchestratorMocks "github.com/epifi/gamma/wealthonboarding/test/mocks/orchestrator"
	mockSteps "github.com/epifi/gamma/wealthonboarding/test/mocks/orchestrator/steps"
	userMocks "github.com/epifi/gamma/wealthonboarding/test/mocks/user"
)

func TestService_OrchestrateRetriesExhausted(t *testing.T) {
	ctrl := gomock.NewController(t)

	mockStep := mockSteps.NewMockIStep(ctrl)
	mockStepHandlerFactory := orchestratorMocks.NewMockIHandlerFactory(ctrl)
	mockAuthClient := authMocks.NewMockAuthClient(ctrl)
	mockUser := userMocks.NewMockIService(ctrl)
	brokerMock := eventMocks.NewMockBroker(ctrl)
	mockAuthClient.EXPECT().GetDeviceDetails(gomock.Any(), gomock.Any()).Return(&auth.GetDeviceDetailsResponse{Status: rpc.StatusOk(), Device: &commontypes.Device{Platform: commontypes.Platform_ANDROID, AppVersion: 1000}}, nil).AnyTimes()
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	mockLock := lockMocks.NewMockILock(ctrl)
	mockLock.EXPECT().Release(gomock.Any()).Return(nil).AnyTimes()
	mockLockManager := lockMocks.NewMockILockManager(ctrl)
	mockLockManager.EXPECT().GetLock(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockLock, nil).AnyTimes() // distributed locking test separately
	mockOnbStepDetailsDao := daoMocks.NewMockOnboardingStepDetailsDao(ctrl)

	type args struct {
		ctx     context.Context
		actorId string
		onbType wealthonboarding.OnboardingType
	}
	tests := []struct {
		name      string
		args      args
		want      *woPb.OnboardingDetails
		want1     *deeplink.Deeplink
		wantErr   bool
		mocksFunc func()
	}{
		{
			name: "change onboarding status if step retries are exhausted",
			args: args{
				ctx:     context.Background(),
				actorId: "dummy-actor-orchestrator",
				onbType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
			},
			want: &woPb.OnboardingDetails{
				ActorId:        "dummy-actor-orchestrator",
				Status:         woPb.OnboardingStatus_ONBOARDING_STATUS_MANUAL_INTERVENTION_NEEDED,
				CurrentStep:    woPb.OnboardingStep_ONBOARDING_STEP_USER_CONSENT,
				OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
				Metadata:       &woPb.OnboardingMetadata{},
			},
			want1:   nil,
			wantErr: false,
			mocksFunc: func() {
				mockOnbStepDetailsDao.EXPECT().GetOrCreate(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&woPb.OnboardingStepDetails{
					OnboardingDetailsId: "randomId", Step: woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION, Status: woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE,
					NumAttempts: 23,
				}, false, nil)
				mockStepHandlerFactory.EXPECT().GetStepHandler(gomock.Any(), gomock.Any()).Return(mockStep, nil)
				dataCollectionExecutionResponse := steps.GetStepExecutionResponse(
					onboardingDetails1,
					woPb.OnboardingStepStatus_STEP_STATUS_TRANSIENT_FAILURE,
					woPb.OnboardingStep_ONBOARDING_STEP_DATA_COLLECTION,
					woPb.OnboardingStep_ONBOARDING_STEP_PAN_VERIFICATION,
				)
				mockStep.EXPECT().Perform(gomock.Any(), gomock.Any()).Return(dataCollectionExecutionResponse, nil)
				mockOnbStepDetailsDao.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mocksFunc()
			testV2.PrepareRandomScopedCrdbDatabase(t, daoTestSuite.conf.EpifiDb, daoTestSuite.dbName, daoTestSuite.db, AffectedTestTables, &initialiseSameDbOnce)
			onboardingDetailsDao := daoImpls.NewCrdbOnboardingDetailsDao(daoTestSuite.db)
			s := &Service{
				onboardingDetailsDao:     onboardingDetailsDao,
				onboardingStepDetailsDao: mockOnbStepDetailsDao,
				handlerFactory:           mockStepHandlerFactory,
				conf:                     conf,
				userService:              mockUser,
				eventBroker:              brokerMock,
				distributedLockManager:   mockLockManager,
				idempotentTxnExecutor:    idempotentTxnExecutor,
			}
			got, got1, err := s.Orchestrate(tt.args.ctx, &orchestrator_model.OrchestrateRequest{
				ActorId: tt.args.actorId,
				OnbType: tt.args.onbType,
			})
			if (err != nil) != tt.wantErr {
				t.Errorf("Orchestrate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&woPb.OnboardingDetails{}, "created_at", "updated_at", "completed_at", "id"),
			}
			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("Orchestrate() mismatch (-want +got):\n%s", diff)
			}
			if diff := cmp.Diff(tt.want1, got1, protocmp.Transform()); diff != "" {
				t.Errorf("Orchestrate() mismatch (-want +got):\n%s", diff)
			}
		})
	}
}
