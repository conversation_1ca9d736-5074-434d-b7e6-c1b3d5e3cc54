package wealthonboarding

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"sync"

	cmap "github.com/orcaman/concurrent-map"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	protoJson "google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/timestamppb"

	celestialPb "github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	actorPb "github.com/epifi/gamma/api/actor"
	invProfile "github.com/epifi/gamma/api/investment/profile"
	rmsPb "github.com/epifi/gamma/api/rms/manager"
	types "github.com/epifi/gamma/api/typesv2"
	usersPb "github.com/epifi/gamma/api/user"
	digilockerVgPb "github.com/epifi/gamma/api/vendorgateway/wealth/digilocker"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	workflow2 "github.com/epifi/gamma/api/wealthonboarding/workflow"
	"github.com/epifi/gamma/wealthonboarding/ckyc_helper"
	"github.com/epifi/gamma/wealthonboarding/config"
	"github.com/epifi/gamma/wealthonboarding/dao"
	"github.com/epifi/gamma/wealthonboarding/helper"
	"github.com/epifi/gamma/wealthonboarding/kra_data"
	"github.com/epifi/gamma/wealthonboarding/ocr"
	"github.com/epifi/gamma/wealthonboarding/orchestrator"
	"github.com/epifi/gamma/wealthonboarding/orchestrator/orchestrator_model"
	"github.com/epifi/gamma/wealthonboarding/release"
	repairData "github.com/epifi/gamma/wealthonboarding/repair_data"
	"github.com/epifi/gamma/wealthonboarding/troubleshooting"
	"github.com/epifi/gamma/wealthonboarding/user"
)

var (
	validOnbStatusChangeMap [][]bool

	_ = func() struct{} { // nolint: unparam
		validOnbStatusChangeMap = make([][]bool, len(woPb.OnboardingStatus_value))
		for i := range validOnbStatusChangeMap {
			validOnbStatusChangeMap[i] = make([]bool, len(woPb.OnboardingStatus_value))
		}
		validOnbStatusChangeMap[woPb.OnboardingStatus_ONBOARDING_STATUS_MANUAL_INTERVENTION_NEEDED][woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS] = true
		validOnbStatusChangeMap[woPb.OnboardingStatus_ONBOARDING_STATUS_FUTURE_SCOPE][woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS] = true
		validOnbStatusChangeMap[woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS][woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS] = true
		validOnbStatusChangeMap[woPb.OnboardingStatus_ONBOARDING_STATUS_FAILED][woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS] = true
		return struct{}{}
	}()
)

type Service struct {
	woPb.UnimplementedWealthOnboardingServer
	orchestratorService      orchestrator.IService
	s3                       s3.S3Client
	conf                     *config.Config
	onboardingDetailsDao     dao.OnboardingDetailsDao
	onboardingStepDetailsDao dao.OnboardingStepDetailsDao
	docHelper                helper.DocumentHelper
	troubleshootHandler      troubleshooting.IHandlerFactory
	digilockerVgClient       digilockerVgPb.DigilockerClient
	kraData                  kra_data.KraData
	userService              user.IService
	repairProvider           *repairData.RepairProvider
	ckycHelper               ckyc_helper.ICkycHelper
	ocrHelper                ocr.IOcrHelper
	eventBroker              events.Broker
	idempotentTxnExecutor    storagev2.IdempotentTxnExecutor
	actorClient              actorPb.ActorClient
	userClient               usersPb.UsersClient
	releaseEvaluator         release.IEvaluator
	rmsRuleManagerClient     rmsPb.RuleManagerClient
	celestialClient          celestialPb.CelestialClient
	invProfileClient         invProfile.InvestmentProfileServiceClient
	commonHelper             helper.ICommonHelper
}

func NewService(
	orchestratorService orchestrator.IService,
	s3 s3.S3Client,
	conf *config.Config,
	onboardingDetailsDao dao.OnboardingDetailsDao,
	onboardingStepDetailsDao dao.OnboardingStepDetailsDao,
	docHelper helper.DocumentHelper,
	troubleshootHandler troubleshooting.IHandlerFactory,
	digilockerVgClient digilockerVgPb.DigilockerClient,
	userService user.IService,
	kraData kra_data.KraData,
	repairProvider *repairData.RepairProvider,
	ckycHelper ckyc_helper.ICkycHelper,
	ocrHelper ocr.IOcrHelper,
	eventBroker events.Broker,
	idempotentTxnExecutor storagev2.IdempotentTxnExecutor,
	actorClient actorPb.ActorClient,
	userClient usersPb.UsersClient,
	releaseEvaluator release.IEvaluator,
	rmsRuleManagerClient rmsPb.RuleManagerClient,
	celestialClient celestialPb.CelestialClient,
	invProfileClient invProfile.InvestmentProfileServiceClient,
	commonHelper helper.ICommonHelper,
) *Service {
	return &Service{
		orchestratorService:      orchestratorService,
		s3:                       s3,
		conf:                     conf,
		onboardingDetailsDao:     onboardingDetailsDao,
		onboardingStepDetailsDao: onboardingStepDetailsDao,
		docHelper:                docHelper,
		troubleshootHandler:      troubleshootHandler,
		digilockerVgClient:       digilockerVgClient,
		kraData:                  kraData,
		userService:              userService,
		repairProvider:           repairProvider,
		ckycHelper:               ckycHelper,
		ocrHelper:                ocrHelper,
		eventBroker:              eventBroker,
		idempotentTxnExecutor:    idempotentTxnExecutor,
		actorClient:              actorClient,
		userClient:               userClient,
		releaseEvaluator:         releaseEvaluator,
		rmsRuleManagerClient:     rmsRuleManagerClient,
		celestialClient:          celestialClient,
		invProfileClient:         invProfileClient,
		commonHelper:             commonHelper,
	}
}

var _ woPb.WealthOnboardingServer = &Service{}

func (s *Service) GetOnboardingStatus(ctx context.Context, req *woPb.GetOnboardingStatusRequest) (*woPb.GetOnboardingStatusResponse, error) {
	ctx = epificontext.CtxWithActorId(ctx, req.GetActorId())
	ctx, ctxErr := getCtxWithDevicePlatformVersion(ctx, s.userClient, req.GetActorId())
	if ctxErr != nil {
		logger.Error(ctx, "error getting ctx with device and platform")
		return &woPb.GetOnboardingStatusResponse{Status: rpc.StatusInternal()}, nil
	}
	onbType := woPb.OnboardingType_ONBOARDING_TYPE_WEALTH
	if req.GetOnboardingType() != woPb.OnboardingType_ONBOARDING_TYPE_UNSPECIFIED {
		onbType = req.GetOnboardingType()
	}
	od, odErr := s.onboardingDetailsDao.GetByActorIdAndOnbType(ctx, req.GetActorId(), onbType)
	if odErr != nil {
		if errors.Is(odErr, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "no wealth onboarding details record found for actor", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			return &woPb.GetOnboardingStatusResponse{Status: rpc.StatusRecordNotFound()}, nil
		}
		logger.Error(ctx, "error getting onboarding details", zap.Error(odErr), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &woPb.GetOnboardingStatusResponse{Status: rpc.StatusInternal()}, nil
	}

	// Any steps/dependencies that are triggered post completion of wealth onboarding are added below to prevent sending wrong status to clients
	wealthOnbStatus := od.GetStatus()

	if req.GetOnboardingType() != woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT && wealthOnbStatus == woPb.OnboardingStatus_ONBOARDING_STATUS_COMPLETED {
		// if user has zero or more than 1 nominees in EPIFI TECH and hasn't explicitly chosen a nominee for their EPIFI WEALTH account, their onboarding isn't complete
		if len(od.GetMetadata().GetPersonalDetails().GetNominees()) != 1 &&
			od.GetMetadata().GetCustomerProvidedData().GetNomineeDeclarationDetails().GetChoice() == commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED {
			logger.Info(ctx, "nominee not declared by user", zap.String(logger.ACTOR_ID_V2, od.GetActorId()))
			wealthOnbStatus = woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS
		}
		if od.GetMetadata().GetPersonalDetails().GetAdvisoryAgreementDetails().GetSignedAgreementS3Path() == "" {
			logger.Info(ctx, "advisory agreement not collected", zap.String(logger.ACTOR_ID_V2, od.GetActorId()))
			wealthOnbStatus = woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS
		}
	}
	return &woPb.GetOnboardingStatusResponse{
		Status:           rpc.StatusOk(),
		OnboardingId:     od.GetId(),
		OnboardingStatus: wealthOnbStatus,
		CurrentStep:      od.GetCurrentStep(),
	}, nil
}

func (s *Service) GetInvestmentOnbStatus(ctx context.Context, req *woPb.GetInvestmentOnbStatusRequest) (*woPb.GetInvestmentOnbStatusResponse, error) {
	invOnbRes := cmap.New()
	var wg sync.WaitGroup
	for _, id := range req.GetActorIds() {
		wg.Add(1)
		// Copying into separate variable to pass to go routine
		// so that the value does not change when go routine is executed
		actorId := id
		goroutine.RunWithCtx(ctx, func(ctx context.Context) {
			defer wg.Done()
			goRoutineCtx, ctxErr := getCtxWithDevicePlatformVersion(ctx, s.userClient, actorId)
			if ctxErr != nil {
				// we are not returning here since we can still send the status present in db
				logger.Error(ctx, "error getting ctx with device and platform", zap.Error(ctxErr))
			} else {
				// Note: Always use goRoutineCtx everywhere below inside routine
				od1, _, err := s.orchestratorService.Orchestrate(goRoutineCtx, &orchestrator_model.OrchestrateRequest{
					ActorId: actorId,
					OnbType: woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT,
				})
				if err != nil {
					// we are not returning here since we can still send the status present in db
					logger.Error(goRoutineCtx, "failed to orchestrate investment onboarding for actor", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
				} else {
					s.repairDataWithCondition(goRoutineCtx, od1)
				}
			}
			od, odErr := s.onboardingDetailsDao.GetByActorIdAndOnbType(ctx, actorId, woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT)
			if odErr != nil {
				logger.Error(ctx, "error in getting onboarding details", zap.Error(odErr))
				return
			}
			invOnbRes.Set(actorId, &woPb.InvestmentOnbData{
				OnboardingStatus: od.GetStatus(),
				OnboardingStep:   od.GetCurrentStep(),
			})
		})
	}
	// Wait function blocks until all go routines have returned
	//nocustomlint:waitgroup
	wg.Wait()
	invResMap := map[string]*woPb.InvestmentOnbData{}
	for _, id := range req.GetActorIds() {
		invRes, ok := invOnbRes.Get(id)
		if !ok {
			logger.Error(ctx, "error while fetching inv_onb_res from concurrent map", zap.String(logger.ACTOR_ID_V2, id))
			return &woPb.GetInvestmentOnbStatusResponse{Status: rpc.StatusInternal()}, nil
		}
		invResMap[id] = invRes.(*woPb.InvestmentOnbData)
	}
	return &woPb.GetInvestmentOnbStatusResponse{
		Status:            rpc.StatusOk(),
		InvestmentOnbInfo: invResMap,
	}, nil
}

func (s *Service) UpdateOnbStatus(ctx context.Context, req *woPb.UpdateOnbStatusRequest) (*woPb.UpdateOnbStatusResponse, error) {
	if req.GetOnboardingType() == woPb.OnboardingType_ONBOARDING_TYPE_UNSPECIFIED {
		return &woPb.UpdateOnbStatusResponse{
			Status: rpc.StatusInvalidArgument(),
		}, nil
	}
	od, err := s.onboardingDetailsDao.GetByActorIdAndOnbType(ctx, req.ActorId, req.GetOnboardingType())
	if err != nil {
		logger.Error(ctx, "error while fetching onboarding details for actor", zap.Error(err))
		return &woPb.UpdateOnbStatusResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	if od.GetCurrentStep() == woPb.OnboardingStep_ONBOARDING_STEP_PAN_VERIFICATION && req.GetOnboardingStatus() == woPb.OnboardingStatus_ONBOARDING_STATUS_COMPLETED {
		updateErr := s.updatePanNameMismatchStepStatus(ctx, od)
		if updateErr != nil {
			logger.Error(ctx, "error while fetching onboarding details for actor", zap.Error(err))
			return &woPb.UpdateOnbStatusResponse{
				Status: rpc.StatusInternal(),
			}, nil
		}
		return &woPb.UpdateOnbStatusResponse{Status: rpc.StatusOk()}, nil
	}

	if !validOnbStatusChangeMap[od.GetStatus()][req.GetOnboardingStatus()] && s.conf.Flags.EnableStepStatusUpdateValidations {
		logger.Info(ctx, "Onboarding status update not valid")
		return &woPb.UpdateOnbStatusResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("Onboarding status update request is not valid"),
		}, nil
	}

	if txnErr := s.idempotentTxnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		// update onboarding status
		od.Status = req.GetOnboardingStatus()
		uErr := s.onboardingDetailsDao.Update(txnCtx, od, []woPb.OnboardingDetailsFieldMask{
			woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_STATUS,
		})
		if uErr != nil {
			return errors.Wrap(uErr, "error while updating onboarding status")
		}

		// marking current step execution stale
		onbStepDetails, stepErr := s.onboardingStepDetailsDao.GetByOnboardingDetailsIdAndStep(txnCtx, od.GetId(), od.GetCurrentStep())
		if stepErr != nil {
			return errors.Wrap(stepErr, "error while fetching onboarding step details")
		}
		onbStepDetails.StaledAt = timestamppb.Now()
		uErr = s.onboardingStepDetailsDao.UpdateById(txnCtx, onbStepDetails, []woPb.OnboardingStepDetailsFieldMask{
			woPb.OnboardingStepDetailsFieldMask_ONBOARDING_STEP_DETAILS_FIELD_MASK_STALED_AT,
		})
		if uErr != nil {
			return errors.Wrap(uErr, "error while updating onboarding step status")
		}
		return nil
	}); txnErr != nil {
		logger.Error(ctx, "error in txn", zap.Error(txnErr))
		return &woPb.UpdateOnbStatusResponse{Status: rpc.StatusInternal()}, nil
	}
	return &woPb.UpdateOnbStatusResponse{Status: rpc.StatusOk()}, nil
}

// TODO(Brijesh): Handle docket-verified fresh KYC users in InitiateDocumentExtraction and GetDocument RPCs

func (s *Service) InitiateDocumentExtraction(ctx context.Context, req *woPb.InitiateDocumentExtractionRequest) (*woPb.InitiateDocumentExtractionResponse, error) {
	if req.GetDocumentType() != types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN {
		logger.Error(ctx, fmt.Sprintf("unsupported document type %v", req.GetDocumentType()), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &woPb.InitiateDocumentExtractionResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("unsupported document type " + req.GetDocumentType().String()),
		}, nil
	}
	// if document is already present
	panS3Path, err := s.getOCRVerifiedPANFromOnboardingDetails(ctx, req.GetActorId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error getting document from onboarding details", zap.Error(err))
			return &woPb.InitiateDocumentExtractionResponse{Status: rpc.NewStatus(uint32(woPb.InitiateDocumentExtractionResponse_DOCKET_NOT_DOWNLOADED), "docket is not available", "")}, nil
		}
		logger.Error(ctx, "error getting pan s3 path from onboarding details", zap.Error(err))
		return &woPb.InitiateDocumentExtractionResponse{Status: rpc.StatusInternal()}, nil
	}

	if panS3Path != "" {
		url, signedURLErr := s.docHelper.GetSignedUrl(ctx, panS3Path, true, s.conf.S3Conf.Bucket, s.conf.CvlKra.PdfExpiryTime)
		if signedURLErr != nil {
			logger.Error(ctx, "error getting signed url for pan doc", zap.Error(signedURLErr))
			return &woPb.InitiateDocumentExtractionResponse{Status: rpc.StatusInternalWithDebugMsg(signedURLErr.Error())}, nil
		}
		return &woPb.InitiateDocumentExtractionResponse{Status: rpc.StatusOk(), DocumentUrl: url}, nil
	}

	preInvOnbDetails, err := s.onboardingDetailsDao.GetByActorIdAndOnbType(ctx, req.GetActorId(), woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT)
	if err != nil {
		logger.Error(ctx, "error getting pre-investment onboarding details for actor", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &woPb.InitiateDocumentExtractionResponse{Status: rpc.NewStatus(uint32(woPb.InitiateDocumentExtractionResponse_DOCKET_NOT_DOWNLOADED), "docket is not available", "")}, nil
		}
		return &woPb.InitiateDocumentExtractionResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	if preInvOnbDetails.GetCurrentStep() != woPb.OnboardingStep_ONBOARDING_STEP_DOWNLOAD_KRA_DOC || preInvOnbDetails.GetStatus() != woPb.OnboardingStatus_ONBOARDING_STATUS_COMPLETED {
		// if step is not DOWNLOAD_KRA_DOC or status is not COMPLETE means kra docket is not available in those cases we will send permanent error
		logger.Error(ctx, "KRA docket not available for user")
		return &woPb.InitiateDocumentExtractionResponse{
			Status: rpc.NewStatus(uint32(woPb.InitiateDocumentExtractionResponse_DOCKET_NOT_DOWNLOADED), "docket is not available", ""),
		}, nil
	}

	wfReq := &workflow2.DocumentExtractionRequest{
		DocumentType: req.GetDocumentType(),
		ActorId:      req.GetActorId(),
	}

	payload, err := protoJson.Marshal(wfReq)
	if err != nil {
		logger.Error(ctx, "unable to get payload for workflow", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		return &woPb.InitiateDocumentExtractionResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	celestialRes, err := s.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
		ActorId: req.GetActorId(),
		Type:    workflow.Type_WEALTH_ONBOARDING_DOCUMENT_EXTRACTION,
		Payload: payload,
		ClientReqId: &celestialPb.ClientReqId{
			Id:     req.GetDocumentType().String() + "_" + req.GetActorId(),
			Client: workflow.Client_WEALTH_ONBOARDING,
		},
		Ownership: commontypes.Ownership_EPIFI_WEALTH,
	})
	if te := epifigrpc.RPCError(celestialRes, err); te != nil {
		logger.Error(ctx, "unable to initiate workflow", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(te))
		if celestialRes.GetStatus().IsAlreadyExists() {
			return &woPb.InitiateDocumentExtractionResponse{
				Status: rpc.StatusAlreadyExistsWithDebugMsg(te.Error()),
			}, nil
		}
		return &woPb.InitiateDocumentExtractionResponse{
			Status: rpc.StatusInternalWithDebugMsg(te.Error()),
		}, nil
	}

	return &woPb.InitiateDocumentExtractionResponse{Status: rpc.StatusOk()}, nil
}

func (s *Service) GetDocument(ctx context.Context, req *woPb.GetDocumentRequest) (*woPb.GetDocumentResponse, error) {
	switch req.GetDocumentType() {
	case types.DocumentProofType_DOCUMENT_PROOF_TYPE_PAN:
		return s.getPanDocument(ctx, req)
	case types.DocumentProofType_DOCUMENT_PROOF_TYPE_INVESTMENT_ADVISORY_AGREEMENT:
		return s.getAdvisoryAgreement(ctx, req)
	default:
		logger.Error(ctx, fmt.Sprintf("unsupported document type %v", req.GetDocumentType()), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &woPb.GetDocumentResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("unsupported document type " + req.GetDocumentType().String()),
		}, nil
	}
}

func (s *Service) GetOrCreateUser(ctx context.Context, req *woPb.GetOrCreateUserRequest) (*woPb.GetOrCreateUserResponse, error) {
	usr, err := s.userService.GetByActorId(ctx, req.GetActorId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			userDetails, errCreate := s.commonHelper.CreateAndPopulateUserPersonalDetails(ctx, req.GetActorId())
			if errCreate != nil {
				logger.Error(ctx, "error while populating user in wealth onboarding", zap.Error(errCreate))
				return &woPb.GetOrCreateUserResponse{
					Status: rpc.StatusInternal(),
				}, nil
			}
			return &woPb.GetOrCreateUserResponse{
				Status:      rpc.StatusOk(),
				UserDetails: userDetails,
			}, nil
		}
		logger.Error(ctx, "error while fetching user by actor id", zap.Error(err))
		return &woPb.GetOrCreateUserResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	userDetails, err := s.commonHelper.ValidateAndUpdateUserPersonalDetails(ctx, usr)
	if err != nil {
		logger.Error(ctx, "error while validating and updating user details", zap.Error(err))
		return &woPb.GetOrCreateUserResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	return &woPb.GetOrCreateUserResponse{
		Status:      rpc.StatusOk(),
		UserDetails: userDetails,
	}, nil
}

func (s *Service) getAdvisoryAgreement(ctx context.Context, req *woPb.GetDocumentRequest) (*woPb.GetDocumentResponse, error) {
	od, odErr := s.onboardingDetailsDao.GetByActorIdAndOnbType(ctx, req.GetActorId(), woPb.OnboardingType_ONBOARDING_TYPE_WEALTH)
	if odErr != nil {
		if errors.Is(odErr, epifierrors.ErrRecordNotFound) {
			return &woPb.GetDocumentResponse{Status: rpc.NewStatus(uint32(woPb.GetDocumentResponse_NOT_FOUND), "wealth onboarding is not done", "")}, nil
		}
		logger.Error(ctx, "error in getting onboarding details", zap.Error(odErr), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &woPb.GetDocumentResponse{Status: rpc.StatusInternal()}, nil
	}
	s3Path := od.GetMetadata().GetPersonalDetails().GetAdvisoryAgreementDetails().GetSignedAgreementS3Path()
	if s3Path == "" {
		return &woPb.GetDocumentResponse{Status: rpc.NewStatus(uint32(woPb.GetDocumentResponse_NOT_FOUND), "document is not available", "")}, nil
	}
	url, signedURLErr := s.docHelper.GetSignedUrl(ctx, s3Path, true, s.conf.S3Conf.Bucket, s.conf.CvlKra.PdfExpiryTime)
	if signedURLErr != nil {
		logger.Error(ctx, "error getting signed url for advisory agreement doc", zap.Error(signedURLErr), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &woPb.GetDocumentResponse{Status: rpc.StatusInternalWithDebugMsg(signedURLErr.Error())}, nil
	}
	return &woPb.GetDocumentResponse{Status: rpc.StatusOk(), DocumentUrl: url}, nil
}

func (s *Service) getPanDocument(ctx context.Context, req *woPb.GetDocumentRequest) (*woPb.GetDocumentResponse, error) {
	panS3Path, err := s.getOCRVerifiedPANFromOnboardingDetails(ctx, req.GetActorId())
	if err != nil {
		logger.Error(ctx, "error getting pan s3 path from onboarding details", zap.Error(err))
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &woPb.GetDocumentResponse{Status: rpc.NewStatus(uint32(woPb.GetDocumentResponse_NOT_FOUND), "no wealth/pre-investment onboarding details found", "")}, nil
		}
		return &woPb.GetDocumentResponse{Status: rpc.StatusInternal()}, nil
	}

	if panS3Path != "" {
		url, signedURLErr := s.docHelper.GetSignedUrl(ctx, panS3Path, true, s.conf.S3Conf.Bucket, s.conf.CvlKra.PdfExpiryTime)
		if signedURLErr != nil {
			logger.Error(ctx, "error getting signed url for pan doc", zap.Error(signedURLErr))
			return &woPb.GetDocumentResponse{Status: rpc.StatusInternalWithDebugMsg(signedURLErr.Error())}, nil
		}
		return &woPb.GetDocumentResponse{Status: rpc.StatusOk(), DocumentUrl: url}, nil
	}

	preInvOnbDetails, err := s.onboardingDetailsDao.GetByActorIdAndOnbType(ctx, req.GetActorId(), woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT)
	if err != nil {
		logger.Error(ctx, "error getting pre-investment onboarding details for actor", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &woPb.GetDocumentResponse{Status: rpc.NewStatus(uint32(woPb.GetDocumentResponse_NOT_FOUND), "KRA docket not available for user", "KRA docket not available for user")}, nil
		}
		return &woPb.GetDocumentResponse{Status: rpc.StatusInternal()}, nil
	}
	if preInvOnbDetails.GetCurrentStep() != woPb.OnboardingStep_ONBOARDING_STEP_DOWNLOAD_KRA_DOC || preInvOnbDetails.GetStatus() != woPb.OnboardingStatus_ONBOARDING_STATUS_COMPLETED {
		// if step is not DOWNLOAD_KRA_DOC or status is not COMPLETE means kra docket is not available in those cases we will send permanent error
		logger.Error(ctx, "KRA docket not available for user")
		return &woPb.GetDocumentResponse{Status: rpc.NewStatus(uint32(woPb.GetDocumentResponse_NOT_FOUND), "KRA docket not available for user", "KRA docket not available for user")}, nil
	}

	wfStatusRes, err := s.celestialClient.GetWorkflowStatus(ctx, &celestialPb.GetWorkflowStatusRequest{
		Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{ClientRequestId: &celestialPb.ClientReqId{
			// we do not want to extract same document again if we have already extracted the document for same actor
			Id:     req.GetDocumentType().String() + "_" + req.GetActorId(),
			Client: workflow.Client_WEALTH_ONBOARDING,
		}},
		Ownership: commontypes.Ownership_EPIFI_WEALTH,
	})
	if err = epifigrpc.RPCError(wfStatusRes, err); err != nil {
		logger.Error(ctx, "error getting workflow status", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		if wfStatusRes.GetStatus().IsRecordNotFound() {
			// since there is no workflow there is no document extraction in progress
			return &woPb.GetDocumentResponse{Status: rpc.NewStatus(uint32(woPb.GetDocumentResponse_NOT_STARTED), "workflow not found", err.Error())}, nil
		}
		return &woPb.GetDocumentResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	switch wfStatusRes.GetWorkflowRequest().GetStatus() {
	case stagePb.Status_SUCCESSFUL, stagePb.Status_FAILED:
		logger.Error(ctx, "workflow is successful but file is not found", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &woPb.GetDocumentResponse{
			Status: rpc.NewStatus(uint32(woPb.GetDocumentResponse_NOT_FOUND), "file not found", "file not found"),
		}, nil
	case stagePb.Status_CREATED, stagePb.Status_INITIATED, stagePb.Status_MANUAL_INTERVENTION:
		logger.Error(ctx, "document extraction in progress", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &woPb.GetDocumentResponse{
			Status: rpc.NewStatus(uint32(woPb.GetDocumentResponse_OPERATION_IN_PROGRESS), "trying to fetch the file", "trying to fetch the file"),
		}, nil
	default:
		logger.Error(ctx, "unexpected status of document extraction workflow", zap.String(logger.STATUS, wfStatusRes.GetWorkflowRequest().GetStatus().String()), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &woPb.GetDocumentResponse{
			Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("unknown workflow state %v", wfStatusRes.GetWorkflowRequest().GetStatus())),
		}, nil
	}
}

func (s *Service) getOCRVerifiedPANFromOnboardingDetails(ctx context.Context, actorId string) (string, error) {
	wealthOnbDetails, err := s.onboardingDetailsDao.GetByActorIdAndOnbType(ctx, actorId, woPb.OnboardingType_ONBOARDING_TYPE_WEALTH)
	if err != nil {
		return "", errors.Wrap(err, "error getting wealth onb details")
	}
	panS3Path := getOCRVerifiedPANS3Path(wealthOnbDetails)
	if panS3Path != "" {
		return panS3Path, nil
	}
	preInvOnbDetails, err := s.onboardingDetailsDao.GetByActorIdAndOnbType(ctx, actorId, woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT)
	if err != nil {
		return "", errors.Wrap(err, "error getting pre inv onb details")
	}
	panS3Path = getOCRVerifiedPANS3Path(preInvOnbDetails)
	return panS3Path, nil
}

func getOCRVerifiedPANS3Path(onbDetails *woPb.OnboardingDetails) string {
	var panS3Path string
	kraDocketExtractedPAN := onbDetails.GetMetadata().GetPersonalDetails().GetKraDocketPan()
	if len(kraDocketExtractedPAN.GetDoc().GetS3Paths()) > 0 {
		panS3Path = kraDocketExtractedPAN.GetDoc().GetS3Paths()[len(kraDocketExtractedPAN.GetDoc().GetS3Paths())-1]
	}
	ckycPAN := onbDetails.GetMetadata().GetPersonalDetails().GetCkycPan()
	if len(ckycPAN.GetDoc().GetS3Paths()) > 0 {
		panS3Path = ckycPAN.GetDoc().GetS3Paths()[len(ckycPAN.GetDoc().GetS3Paths())-1]
	}
	return panS3Path
}

func (s *Service) updatePanNameMismatchStepStatus(ctx context.Context, od *woPb.OnboardingDetails) error {

	onbStepDetails, err1 := s.onboardingStepDetailsDao.GetByOnboardingDetailsIdAndStep(ctx, od.GetId(), od.GetCurrentStep())

	if err1 != nil {
		logger.Error(ctx, "unable to get onboardingStepDetails", zap.Error(err1))
		return errors.Wrap(err1, "error getting onboarding step details")
	}
	if panVerifyTxnErr := s.idempotentTxnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		onbStepDetails.Status = woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED
		od.Status = woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS
		od.CurrentStep = woPb.OnboardingStep_ONBOARDING_STEP_FETCH_KYC_INFO_FROM_KRA

		statusUpdateErr := s.onboardingStepDetailsDao.UpdateById(ctx, onbStepDetails, []woPb.OnboardingStepDetailsFieldMask{
			woPb.OnboardingStepDetailsFieldMask_ONBOARDING_STEP_DETAILS_FIELD_MASK_STATUS,
		})
		if statusUpdateErr != nil {
			return errors.Wrap(statusUpdateErr, "error while updating onboarding step status")
		}

		statusUpdateErr = s.onboardingDetailsDao.Update(ctx, od, []woPb.OnboardingDetailsFieldMask{
			woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_STATUS,
			woPb.OnboardingDetailsFieldMask_ONBOARDING_DETAILS_FIELD_MASK_CURRENT_STEP,
		})
		if statusUpdateErr != nil {
			return errors.Wrap(statusUpdateErr, "error while updating onboarding details status")
		}

		return nil
	}); panVerifyTxnErr != nil {
		logger.Error(ctx, "error in txn", zap.Error(panVerifyTxnErr))
		return errors.Wrap(panVerifyTxnErr, "Error in executing db txn")
	}
	return nil
}
