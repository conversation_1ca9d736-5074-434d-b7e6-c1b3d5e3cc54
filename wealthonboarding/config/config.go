package config

import (
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"sync"
	"time"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/cfg"
)

var (
	once   sync.Once
	config *Config
	err    error
)

var (
	_, b, _, _ = runtime.Caller(0)
)

const (
	RudderWriteKey              = "RudderWriteKey"
	_DocketSignOfficerSignature = "OfficerSignature"
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig()
	})
	if err != nil {
		return nil, err
	}
	return config, err
}

func loadConfig() (*Config, error) {
	// will be used only for test environment
	configDirPath := testEnvConfigDir()
	conf := &Config{}
	// loads config from file
	k, _, err := cfg.LoadConfigUsingKoanf(configDirPath, cfg.WEALTH_ONBOARDING_SERVICE)

	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}

	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	keyToSecret, err := cfg.LoadAllSecrets(conf.EpifiDb, conf.Secrets.Ids, conf.Application.Environment, conf.AWS.Region)
	if err != nil {
		return nil, err
	}

	if err := updateDefaultConfig(conf, keyToSecret); err != nil {
		return nil, err
	}

	if err := loadLocalFiles(conf.AgreementPdf, configDirPath); err != nil {
		fmt.Println("unable to read local files", err)
	}

	return conf, nil
}

// update the DB endpoint and port
// update the env variable
// update the default secret keys in the config with the secret values fetched from Secret manager
func updateDefaultConfig(c *Config, keyToSecret map[string]string) error {
	dbServerEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_CRDB)
	cfg.UpdateDbEndpointInConfig(c.EpifiDb, dbServerEndpoint)

	if err := readAndSetEnv(c); err != nil {
		return fmt.Errorf("failed to read and set env var: %w", err)
	}

	cfg.UpdateSecretValues(c.EpifiDb, c.Secrets, keyToSecret)

	// updating service specific secrets
	c.KraDocketSignConfig.OfficerSignature = c.Secrets.Ids[_DocketSignOfficerSignature]
	return nil
}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config) error {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Name = val
	}

	if val, ok := os.LookupEnv("SERVER_PORT"); ok {
		intVal, err := strconv.Atoi(val)
		if err != nil {
			return fmt.Errorf("failed to convert port to int: %w", err)
		}

		c.Server.Ports.GrpcPort = intVal
	}

	if val, ok := os.LookupEnv("DB_HOST"); ok {
		c.EpifiDb.Host = val
	}

	return nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..", "values")
	return configPath
}

func loadLocalFiles(lf *LocalFile, configPath string) error {
	if lf == nil || lf.RelativePath == "" {
		return nil
	}
	absFilePath := fmt.Sprintf("%s/%s", configPath, lf.RelativePath)
	fb, err := ioutil.ReadFile(filepath.Clean(absFilePath))
	if err != nil {
		return errors.Wrap(err, fmt.Sprintf("failed to read file: %s", absFilePath))
	}
	lf.Data = fb
	return nil
}

type Config struct {
	Application                           *Application
	Server                                *Server
	EpifiDb                               *cfg.DB
	AWS                                   *cfg.AWS
	RudderStack                           *cfg.RudderStackBroker
	Secrets                               *cfg.Secrets
	RefreshFaceMatchStatusSqsPublisher    *cfg.SqsPublisher
	RefreshFaceMatchStatusSqsSubscriber   *cfg.SqsSubscriber
	UpdateTxnStateSqsPublisher            *cfg.SqsPublisher
	UpdateTxnStateSqsSubscriber           *cfg.SqsSubscriber
	InitWealthOnboardingSubscriber        *cfg.SqsSubscriber
	WealthAuthFactorUpdateEventSubscriber *cfg.SqsSubscriber
	VendorRequestProducer                 *cfg.KinesisProducer
	Flags                                 *Flags
	FetchFMStatusInitialDelayDuration     time.Duration
	UpdateTxnStateDelayDuration           time.Duration
	MinNameMatchScore                     float64
	CvlKra                                *CvlKra
	AgreementPdf                          *LocalFile
	S3Conf                                *S3Conf
	VendorRequestS3Conf                   *S3Conf
	Digio                                 *Digio
	LivenessConfig                        *LivenessConf
	KraDocketSignConfig                   *KraDocketSignConfig
	EnableNameMatch                       bool
	StageDescriptionMapping               map[string]string
	OCRThresholdScoreConfig               map[string]float64
	DaysToExpiry                          int
	// delay publisher and subscriber for steps retry
	WealthOnboardingStepsRetrySqsCustomDelayPublisher *cfg.SqsCustomDelayQueuePublisher
	WealthOnboardingStepsRetryDelaySqsSubscriber      *cfg.SqsSubscriber
	StepsRetryStrategy                                map[string]map[string]map[string]*cfg.RetryParams
	FeatureControlledReleaseConfig                    map[string]*ConstraintConfig
	NotifyMeFix                                       *NotifyMeFix
	// digilocker configs
	DigilockerConfig                *DigilockerConfig
	KraDowntime                     *Downtime
	Tracing                         *cfg.Tracing
	Profiling                       *cfg.Profiling
	LambdaFunctions                 *LambdaFunctions
	MaxPanUploadAttempts            int
	NotificationIcons               *NotificationIcons
	UserComms                       []*UserComm
	UserCommsPublisher              *cfg.SqsPublisher
	UserCommsSubscriber             *cfg.SqsSubscriber
	AuthFactorUpdateWealthPublisher *cfg.SnsPublisher
	InhouseNameMatchThreshold       float32
	AdvisoryAgreement               *AdvisoryAgreement
	Manch                           *Manch
	// id required to identify the investments faq section in help
	// this is required to direct the users to help section of wealth onboarding/investments
	InvestmentsFaqCategoryId string
	// max number of times a user can attempt to submit pan-dob in wealth onboarding
	MaxPanDobSubmitAttempts int
	DisableCKYC             bool
}

type AdvisoryAgreement struct {
	UnsignedDocS3Path         string
	AdvisoryAgreementUpdateAt time.Time
}

type Application struct {
	Environment string
	Name        string
}

type Server struct {
	Ports *cfg.ServerPorts
}

type Flags struct {
	// A flag to determine if the debug message in Status is to be trimmed
	TrimDebugMessageFromStatus bool
	VendorRequestStore         string
	// flag to enable validations on wealth on-boarding step status update
	EnableStepStatusUpdateValidations bool

	// Validate bank account by using Bank's API directly or other processes like
	// dropping a penny in the user's bank account and checking its transfer status.
	EnableBankAccountValidation bool
}

type CvlKra struct {
	// upload path of user's kra data at cvl's sftp server
	UploadPath string
	// expiry time of the pdf generated for a user by docs service in s3
	PdfExpiryTime int64
	// verification time required by cvl in hours
	VerificationTimeInHours int64
}

type LocalFile struct {
	// path relative to config path
	RelativePath string
	Data         []byte
}

type S3Conf struct {
	Bucket                 string `iam:"s3-readwrite"`
	LivenessPath           string
	DownloadedKraDocS3Path string
}

type Digio struct {
	// BaseSignPath is baseUrl for signing documents via Digio
	BaseSignPath string
}

type LivenessConf struct {
	OTPThreshold              float32
	LivenessThreshold         float32
	LivenessStalenessDuration time.Duration
}

type KraDocketSignConfig struct {
	OfficerName        string
	EmployeeCode       string
	OfficerDesignation string
	IntermediaryCode   string
	OfficerSignature   string
	CallBackUrl        string
}

type ConstraintConfig struct {
	AppVersionConstraintConfig       *AppVersionConstraintConfig
	StickyPercentageConstraintConfig *StickyPercentageConstraintConfig
	UserGroupConstraintConfig        *UserGroupConstraintConfig
	AppPlatformConstraintConfig      *AppPlatformConstraintConfig
}

type AppVersionConstraintConfig struct {
	MinAndroidVersion int
	MinIOSVersion     int
}

type StickyPercentageConstraintConfig struct {
	RolloutPercentage int
}

type UserGroupConstraintConfig struct {
	UserGroup string
}

type AppPlatformConstraintConfig struct {
	Android bool
	Ios     bool
}

type NotifyMeFix struct {
	MaxAndroidVersion int
	MaxIOSVersion     int
}

type DigilockerConfig struct {
	ClientId    string
	LoginUrl    string
	CallbackUrl string
}

type Downtime struct {
	// time format "2006-01-02 15:04:05.000" (time zone IST)
	DowntimeFrom string
	DowntimeTo   string
}

type LambdaFunctions struct {
	ImageConverterName string
}

type NotificationIcons struct {
	WarningIconUrl string
	SuccessIconUrl string
	FiBankIconUrl  string
}

type UserComm struct {
	Step          string
	Status        string
	SubStatus     string
	Notifications []*DelayedNotification
}

type DelayedNotification struct {
	Campaign         string
	NotificationData *NotificationData
	Delay            time.Duration
}

type NotificationData struct {
	Title   string
	Body    string
	IconUrl string
}

type Manch struct {
	// template key for manch vendor which identifies esign attributes like position on the page, pages to apply the sign etc
	// template key for KYC docket document - sign on all pages
	DocketTemplateKey string
	// template key for advisory agreement document - sign only on last page
	AdvisoryAgreementTemplateKey string
}
