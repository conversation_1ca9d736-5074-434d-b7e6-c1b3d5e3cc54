package wealthonboarding

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/epifi/gamma/api/employment"
	deeplinkpb "github.com/epifi/gamma/api/frontend/deeplink"
	clientstate "github.com/epifi/gamma/api/frontend/wealthonboarding/clientstate"
	types "github.com/epifi/gamma/api/typesv2"
	userpb "github.com/epifi/gamma/api/user"
	mockUser "github.com/epifi/gamma/api/user/mocks"
	"github.com/epifi/gamma/api/vendors/wealth"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	userPb "github.com/epifi/gamma/api/wealthonboarding/user"
	"github.com/epifi/gamma/wealthonboarding/dao"
	"github.com/epifi/gamma/wealthonboarding/helper"
	"github.com/epifi/gamma/wealthonboarding/orchestrator"
	"github.com/epifi/gamma/wealthonboarding/release"
	mockDao "github.com/epifi/gamma/wealthonboarding/test/mocks/dao"
	orchestratorMocks "github.com/epifi/gamma/wealthonboarding/test/mocks/orchestrator"
	releaseEvaluatorMocks "github.com/epifi/gamma/wealthonboarding/test/mocks/release"
	userMocks "github.com/epifi/gamma/wealthonboarding/test/mocks/user"
	"github.com/epifi/gamma/wealthonboarding/user"
)

func TestService_GetInvestmentDataV2(t *testing.T) {
	ctr := gomock.NewController(t)
	mockUserClient := mockUser.NewMockUsersClient(ctr)
	mockUserClient.EXPECT().GetUserDeviceProperties(gomock.Any(), gomock.Any()).Return(&userpb.GetUserDevicePropertiesResponse{Status: rpc.StatusOk(), UserDevicePropertyList: []*userpb.UserDeviceProperty{
		{
			DeviceProperty: types.DeviceProperty_DEVICE_PROP_APP_VERSION_INFO,
			PropertyValue: &types.PropertyValue{PropValue: &types.PropertyValue_AppVersionInfo{
				AppVersionInfo: &types.AppVersionInfo{
					Platform:       commontypes.Platform_ANDROID,
					AppVersionCode: 1000,
				},
			}},
		},
	}}, nil).AnyTimes()
	type args struct {
		ctx                      context.Context
		req                      *woPb.GetInvestmentDataV2Request
		onboardingDetailsDao     dao.OnboardingDetailsDao
		onboardingStepDetailsDao dao.OnboardingStepDetailsDao
		userService              user.IService
		orcSvc                   orchestrator.IService
		releaseEvaluator         release.IEvaluator
	}
	sampleUser := &userPb.User{
		Id:      "12345",
		ActorId: "AC220525h9NnvYRgQbWSBIl5vqKL9Q==",
		PersonalDetails: &userPb.PersonalDetails{
			Name:   &commontypes.Name{FirstName: "ram", LastName: "sahu"},
			Gender: types.Gender_MALE,
			PhoneNumber: &commontypes.PhoneNumber{
				NationalNumber: *********,
				CountryCode:    91,
			},
			PermanentAddress: &types.PostalAddress{
				AdministrativeArea: "xyz",
				Locality:           "xyz",
				Sublocality:        "xyz",
			},
			Dob: &date.Date{
				Year:  1999,
				Month: 10,
				Day:   24,
			},
			Nationality: types.Nationality_NATIONALITY_INDIAN,
			Email:       "<EMAIL>",
		},
	}
	sampleOnbDetails := woPb.OnboardingDetails{
		Id:      "82938aac-7dbf-4190-843c-94b1cbce95b6",
		ActorId: "AC220525h9NnvYRgQbWSBIl5vqKL9Q==",
		Metadata: &woPb.OnboardingMetadata{
			IsFreshKra: false,
			KraData: &woPb.KraData{
				DownloadedData: &woPb.KraDownloadedData{PanDetails: &woPb.KraDownloadedData_DownloadedPanDetails{
					PermAddress: &types.PostalAddress{
						AdministrativeArea: "xyz",
						Locality:           "xyz",
						Sublocality:        "xyz",
					},
				}},
			},
			PersonalDetails: &woPb.PersonalDetails{NomineeDeclarationDetails: &woPb.NomineeDeclarationDetails{Choice: commontypes.BooleanEnum_FALSE}},
		},
	}
	sampleCompletedOnbDetails := woPb.OnboardingDetails{
		Id:      "82938aac-7dbf-4190-843c-94b1cbce95b6",
		ActorId: "AC220525h9NnvYRgQbWSBIl5vqKL9Q==",
		Metadata: &woPb.OnboardingMetadata{
			IsFreshKra: false,
			KraData: &woPb.KraData{
				DownloadedData: &woPb.KraDownloadedData{PanDetails: &woPb.KraDownloadedData_DownloadedPanDetails{
					PermAddress: &types.PostalAddress{
						AdministrativeArea: "xyz",
						Locality:           "xyz",
						Sublocality:        "xyz",
					},
				}},
			},
			PersonalDetails: &woPb.PersonalDetails{NomineeDeclarationDetails: &woPb.NomineeDeclarationDetails{Choice: commontypes.BooleanEnum_FALSE}},
		},
		Status: woPb.OnboardingStatus_ONBOARDING_STATUS_COMPLETED,
	}
	samplePreInvDetailsV2 := &woPb.PreInvestmentDetailsV2{
		InvestmentDetails: &woPb.PreInvestmentDetail{
			CustomerName: &commontypes.Name{FirstName: "ram", LastName: "sahu"},
			Dob: &date.Date{
				Year:  1999,
				Month: 10,
				Day:   24,
			},
			Address: &types.PostalAddress{
				AdministrativeArea: "xyz",
				Locality:           "xyz",
				Sublocality:        "xyz",
			},
			Nationality: types.Nationality_NATIONALITY_INDIAN,
			EmailId:     "<EMAIL>",
			MobileNo: &commontypes.PhoneNumber{
				CountryCode:    91,
				NationalNumber: *********,
			},
			Nominee: nil,
			Gender:  types.Gender_MALE,

			CustomerConsent: true,
			BankDetails:     nil,
			AddressType:     types.AddressType_PERMANENT,
			EmploymentData: &woPb.EmploymentData{
				EmploymentType: employment.EmploymentType_SALARIED,
			},
			NomineeDeclarationDetails: &woPb.NomineeDeclarationDetails{Choice: commontypes.BooleanEnum_FALSE},
		},
	}
	sampleOnbDetailsWithAadhaarValidationPending := &woPb.OnboardingDetails{
		Id:      "82938aac-7dbf-4190-843c-94b1cbce95b6",
		ActorId: "AC220525h9NnvYRgQbWSBIl5vqKL9Q==",
		Metadata: &woPb.OnboardingMetadata{
			IsFreshKra: true,
			PoaDetails: &types.DocumentProof{ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR},
			UploadKraDocData: &woPb.UploadKraDocData{UploadAttempts: []*woPb.UploadKraDocData_UploadAttempt{{
				AttemptId: "randomId",
				PanEnquiry: &wealth.PanEnquiry{
					CreatedDate: timestamppb.New(time.Date(2022, 11, 25, 0, 0, 0, 0, time.UTC)),
					Status:      wealth.KraStatus_KRA_STATUS_KRA_VERIFIED,
				},
			}}},
		},
		CurrentStep: woPb.OnboardingStep_ONBOARDING_STEP_UPLOAD_DOCKET,
		Status:      woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
	}
	aadhaarValidationPendingDeeplink := &deeplinkpb.Deeplink{
		Screen: deeplinkpb.Screen_WEALTH_ONBOARDING_ERROR_SCREEN,
		ScreenOptions: &deeplinkpb.Deeplink_WealthOnboardingErrorScreenOptions{
			WealthOnboardingErrorScreenOptions: &deeplinkpb.WealthOnboardingErrorScreenOptions{
				IllustrationUrl: helper.ManualVerificationIllustrationURL,
				Title:           "CVL KRA is verifying your KYC documents",
				Description:     "It takes 2 working days. Expect an SMS/email to validate your Aadhaar",
				Cta:             &deeplinkpb.Cta{Text: "Ok", Deeplink: &deeplinkpb.Deeplink{Screen: deeplinkpb.Screen_HOME}},
			},
		},
	}

	tests := []struct {
		name    string
		args    args
		want    *woPb.GetInvestmentDataV2Response
		wantErr bool
	}{
		{
			name: "send deeplink to collect PAN when DOB mismatches",
			args: args{
				ctx: context.Background(),
				req: &woPb.GetInvestmentDataV2Request{
					ActorIds: []string{"AC220525h9NnvYRgQbWSBIl5vqKL9Q=="},
				},
				onboardingDetailsDao: func() dao.OnboardingDetailsDao {
					mockOnbDetailsDao := mockDao.NewMockOnboardingDetailsDao(ctr)
					mockOnbDetailsDao.EXPECT().GetByActorIdAndOnbType(gomock.Any(), gomock.Any(), gomock.Any()).Return(&sampleOnbDetails, nil).AnyTimes()
					mockOnbDetailsDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
					return mockOnbDetailsDao
				}(),
				onboardingStepDetailsDao: func() dao.OnboardingStepDetailsDao {
					mockOnbStepDetailsDao := mockDao.NewMockOnboardingStepDetailsDao(ctr)
					mockOnbStepDetailsDao.EXPECT().GetByOnboardingDetailsIdAndStep(gomock.Any(), gomock.Any(), gomock.Any()).Return(&woPb.OnboardingStepDetails{
						Id:        "12345",
						SubStatus: woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_EMPTY_DOWNLOAD_API_RESPONSE,
						Status:    woPb.OnboardingStepStatus_STEP_STATUS_MANUAL_INTERVENTION_NEEDED,
					}, nil).AnyTimes()
					mockOnbStepDetailsDao.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
					return mockOnbStepDetailsDao
				}(),
				orcSvc: func() orchestrator.IService {
					mockOrcSvc := orchestratorMocks.NewMockIService(ctr)
					mockOrcSvc.EXPECT().Orchestrate(gomock.Any(), gomock.Any()).Return(&sampleOnbDetails, &deeplinkpb.Deeplink{
						Screen: deeplinkpb.Screen_WEALTH_ONBOARDING_CAPTURE_MISSING_DATA_SCREEN,
						ScreenOptions: &deeplinkpb.Deeplink_WealthOnboardingCaptureMissingDataScreenOptions{
							WealthOnboardingCaptureMissingDataScreenOptions: &deeplinkpb.WealthOnboardingCaptureMissingDataScreenOptions{},
						},
					}, nil).AnyTimes()
					return mockOrcSvc
				}(),
				releaseEvaluator: func() release.IEvaluator {
					mockEvaluator := releaseEvaluatorMocks.NewMockIEvaluator(ctr)
					mockEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(woPb.WealthOnboardingFeature_WEALTH_ONBOARDING_FEATURE_UPDATE_NOMINEE_DETAILS).WithActorId("AC220525h9NnvYRgQbWSBIl5vqKL9Q==")).Return(true, nil, nil).Times(1)
					return mockEvaluator
				}(),
			},
			want: &woPb.GetInvestmentDataV2Response{
				Status: rpc.StatusOk(),
				InvestmentDetailInfo: map[string]*woPb.PreInvestmentDetailsV2{"AC220525h9NnvYRgQbWSBIl5vqKL9Q==": {
					FailureType:   woPb.FailureType_FAILURE_TYPE_NOT_RETRYABLE,
					FailureReason: woPb.FailureReason_FAILURE_REASON_DOB_MISMATCH,
					NextStep: &deeplinkpb.Deeplink{
						Screen: deeplinkpb.Screen_WEALTH_ONBOARDING_CAPTURE_MISSING_DATA_SCREEN,
						ScreenOptions: &deeplinkpb.Deeplink_WealthOnboardingCaptureMissingDataScreenOptions{
							WealthOnboardingCaptureMissingDataScreenOptions: &deeplinkpb.WealthOnboardingCaptureMissingDataScreenOptions{
								Flow:            clientstate.WealthFlow_WEALTH_FLOW_INVESTMENT,
								Title:           "You're almost there!",
								Description:     "Finish this step to create an account for all your investments",
								IllustrationUrl: helper.FinishLineFlagIllustrationURL,
							},
						},
					},
				}},
			},
			wantErr: false,
		},
		{
			name: "return investment details without any deeplink when all data is correct for not fresh kra user",
			args: args{
				ctx: context.Background(),
				req: &woPb.GetInvestmentDataV2Request{
					ActorIds: []string{"AC220525h9NnvYRgQbWSBIl5vqKL9Q=="},
				},
				onboardingDetailsDao: func() dao.OnboardingDetailsDao {
					mockOnbDetailsDao := mockDao.NewMockOnboardingDetailsDao(ctr)
					mockOnbDetailsDao.EXPECT().GetByActorIdAndOnbType(gomock.Any(), gomock.Any(), woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT).Return(&sampleOnbDetails, nil).AnyTimes()
					mockOnbDetailsDao.EXPECT().GetByActorIdAndOnbType(gomock.Any(), gomock.Any(), woPb.OnboardingType_ONBOARDING_TYPE_WEALTH).Return(&sampleCompletedOnbDetails, nil).AnyTimes()
					return mockOnbDetailsDao
				}(),
				onboardingStepDetailsDao: func() dao.OnboardingStepDetailsDao {
					mockOnbStepDetailsDao := mockDao.NewMockOnboardingStepDetailsDao(ctr)
					mockOnbStepDetailsDao.EXPECT().GetByOnboardingDetailsIdAndStep(gomock.Any(), gomock.Any(), gomock.Any()).Return(&woPb.OnboardingStepDetails{
						Id:        "12345",
						SubStatus: woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_CKYC_ACCOUNT_TYPE_S,
						Status:    woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					}, nil).AnyTimes()
					return mockOnbStepDetailsDao
				}(),
				userService: func() user.IService {
					mockUserservice := userMocks.NewMockIService(ctr)
					mockUserservice.EXPECT().GetByActorId(gomock.Any(), gomock.Any()).Return(sampleUser, nil).AnyTimes()
					return mockUserservice
				}(),
			},
			want: &woPb.GetInvestmentDataV2Response{
				Status: rpc.StatusOk(),
				InvestmentDetailInfo: map[string]*woPb.PreInvestmentDetailsV2{"AC220525h9NnvYRgQbWSBIl5vqKL9Q==": {
					InvestmentDetails: &woPb.PreInvestmentDetail{
						CustomerName: &commontypes.Name{FirstName: "ram", LastName: "sahu"},
						Dob: &date.Date{
							Year:  1999,
							Month: 10,
							Day:   24,
						},
						Address: &types.PostalAddress{
							AdministrativeArea: "xyz",
							Locality:           "xyz",
							Sublocality:        "xyz",
						},
						Nationality: types.Nationality_NATIONALITY_INDIAN,
						EmailId:     "<EMAIL>",
						MobileNo: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: *********,
						},
						Nominee:         nil,
						CustomerConsent: true,
						BankDetails:     nil,
						AddressType:     types.AddressType_PERMANENT,
						EmploymentData: &woPb.EmploymentData{
							EmploymentType: employment.EmploymentType_SELF_EMPLOYED,
						},
						NomineeDeclarationDetails: &woPb.NomineeDeclarationDetails{Choice: commontypes.BooleanEnum_FALSE},
					},
				}},
			},
			wantErr: false,
		},
		{
			name: "send retryable error when user has not started wealth onboarding at all",
			args: args{
				ctx: context.Background(),
				req: &woPb.GetInvestmentDataV2Request{
					ActorIds: []string{"AC220525h9NnvYRgQbWSBIl5vqKL9Q=="},
				},
				onboardingStepDetailsDao: func() dao.OnboardingStepDetailsDao {
					mockOnbStepDetailsDao := mockDao.NewMockOnboardingStepDetailsDao(ctr)
					mockOnbStepDetailsDao.EXPECT().GetByOnboardingDetailsIdAndStep(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound).AnyTimes()
					return mockOnbStepDetailsDao
				}(),
				onboardingDetailsDao: func() dao.OnboardingDetailsDao {
					mockOnbDetailsDao := mockDao.NewMockOnboardingDetailsDao(ctr)
					mockOnbDetailsDao.EXPECT().GetByActorIdAndOnbType(gomock.Any(), gomock.Any(), gomock.Any()).Return(&sampleOnbDetails, nil).AnyTimes()
					return mockOnbDetailsDao
				}(),
			},
			want: &woPb.GetInvestmentDataV2Response{
				Status: rpc.StatusOk(),
				InvestmentDetailInfo: map[string]*woPb.PreInvestmentDetailsV2{"AC220525h9NnvYRgQbWSBIl5vqKL9Q==": {
					InvestmentDetails: nil,
					FailureType:       woPb.FailureType_FAILURE_TYPE_RETRYABLE,
					FailureReason:     woPb.FailureReason_FAILURE_REASON_UNSPECIFIED,
				}},
			},
			wantErr: false,
		},
		{
			name: "trigger pre-investment onboarding when user has started wealth onboarding but not pre-investment onboarding",
			args: args{
				ctx: context.Background(),
				req: &woPb.GetInvestmentDataV2Request{
					ActorIds: []string{"AC220525h9NnvYRgQbWSBIl5vqKL9Q=="},
				},
				onboardingDetailsDao: func() dao.OnboardingDetailsDao {
					mockOnbDetailsDao := mockDao.NewMockOnboardingDetailsDao(ctr)
					mockOnbDetailsDao.EXPECT().GetByActorIdAndOnbType(gomock.Any(), gomock.Any(), woPb.OnboardingType_ONBOARDING_TYPE_WEALTH).Return(&sampleCompletedOnbDetails, nil).AnyTimes()
					mockOnbDetailsDao.EXPECT().GetByActorIdAndOnbType(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
					mockOnbDetailsDao.EXPECT().GetByActorIdAndOnbType(gomock.Any(), gomock.Any(), gomock.Any()).Return(&woPb.OnboardingDetails{
						Id:      "82938aac-7dbf-4190-843c-94b1cbce95b6",
						ActorId: "AC220525h9NnvYRgQbWSBIl5vqKL9Q==",
						Metadata: &woPb.OnboardingMetadata{
							IsFreshKra: true,
						},
					}, nil).AnyTimes()
					mockOnbDetailsDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
					return mockOnbDetailsDao
				}(),
				orcSvc: func() orchestrator.IService {
					mockOrcSvc := orchestratorMocks.NewMockIService(ctr)
					mockOrcSvc.EXPECT().Orchestrate(gomock.Any(), gomock.Any()).Return(&sampleOnbDetails, &deeplinkpb.Deeplink{
						Screen: deeplinkpb.Screen_WEALTH_ONBOARDING_CAPTURE_MISSING_DATA_SCREEN,
						ScreenOptions: &deeplinkpb.Deeplink_WealthOnboardingCaptureMissingDataScreenOptions{
							WealthOnboardingCaptureMissingDataScreenOptions: &deeplinkpb.WealthOnboardingCaptureMissingDataScreenOptions{},
						},
					}, nil).AnyTimes()
					return mockOrcSvc
				}(),
				userService: func() user.IService {
					mockUserservice := userMocks.NewMockIService(ctr)
					mockUserservice.EXPECT().GetByActorId(gomock.Any(), gomock.Any()).Return(sampleUser, nil).AnyTimes()
					return mockUserservice
				}(),
				releaseEvaluator: func() release.IEvaluator {
					mockEvaluator := releaseEvaluatorMocks.NewMockIEvaluator(ctr)
					mockEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(woPb.WealthOnboardingFeature_WEALTH_ONBOARDING_FEATURE_UPDATE_NOMINEE_DETAILS).WithActorId("AC220525h9NnvYRgQbWSBIl5vqKL9Q==")).Return(true, nil, nil).Times(1)
					return mockEvaluator
				}(),
			},
			want: &woPb.GetInvestmentDataV2Response{
				Status:               rpc.StatusOk(),
				InvestmentDetailInfo: map[string]*woPb.PreInvestmentDetailsV2{"AC220525h9NnvYRgQbWSBIl5vqKL9Q==": samplePreInvDetailsV2},
			},
			wantErr: false,
		},
		{
			name: "return investment details without any deeplink when all data is correct for fresh kra user",
			args: args{
				ctx: context.Background(),
				req: &woPb.GetInvestmentDataV2Request{
					ActorIds: []string{"AC220525h9NnvYRgQbWSBIl5vqKL9Q=="},
				},
				onboardingDetailsDao: func() dao.OnboardingDetailsDao {
					mockOnbDetailsDao := mockDao.NewMockOnboardingDetailsDao(ctr)
					mockOnbDetailsDao.EXPECT().GetByActorIdAndOnbType(gomock.Any(), gomock.Any(), woPb.OnboardingType_ONBOARDING_TYPE_WEALTH).Return(&sampleCompletedOnbDetails, nil).AnyTimes()
					mockOnbDetailsDao.EXPECT().GetByActorIdAndOnbType(gomock.Any(), gomock.Any(), woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT).Return(&woPb.OnboardingDetails{
						Id:      "82938aac-7dbf-4190-843c-94b1cbce95b6",
						ActorId: "AC220525h9NnvYRgQbWSBIl5vqKL9Q==",
						Metadata: &woPb.OnboardingMetadata{
							IsFreshKra: true,
						},
					}, nil).AnyTimes()
					return mockOnbDetailsDao
				}(),
				userService: func() user.IService {
					mockUserservice := userMocks.NewMockIService(ctr)
					mockUserservice.EXPECT().GetByActorId(gomock.Any(), gomock.Any()).Return(sampleUser, nil).AnyTimes()
					return mockUserservice
				}(),
			},
			want: &woPb.GetInvestmentDataV2Response{
				Status:               rpc.StatusOk(),
				InvestmentDetailInfo: map[string]*woPb.PreInvestmentDetailsV2{"AC220525h9NnvYRgQbWSBIl5vqKL9Q==": samplePreInvDetailsV2},
			},
			wantErr: false,
		},
		{
			name: "return non-retryable error when user has aadhaar as address proof which is not validated by KRA",
			args: args{
				ctx: context.Background(),
				req: &woPb.GetInvestmentDataV2Request{
					ActorIds: []string{"AC220525h9NnvYRgQbWSBIl5vqKL9Q=="},
				},
				onboardingDetailsDao: func() dao.OnboardingDetailsDao {
					mockOnbDetailsDao := mockDao.NewMockOnboardingDetailsDao(ctr)
					mockOnbDetailsDao.EXPECT().GetByActorIdAndOnbType(gomock.Any(), gomock.Any(), woPb.OnboardingType_ONBOARDING_TYPE_WEALTH).Return(&sampleCompletedOnbDetails, nil).AnyTimes()
					mockOnbDetailsDao.EXPECT().GetByActorIdAndOnbType(gomock.Any(), gomock.Any(), woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT).Return(sampleOnbDetailsWithAadhaarValidationPending, nil).AnyTimes()
					mockOnbDetailsDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
					return mockOnbDetailsDao
				}(),
				orcSvc: func() orchestrator.IService {
					mockOrcSvc := orchestratorMocks.NewMockIService(ctr)
					mockOrcSvc.EXPECT().Orchestrate(gomock.Any(), gomock.Any()).Return(sampleOnbDetailsWithAadhaarValidationPending, aadhaarValidationPendingDeeplink, nil).AnyTimes()
					return mockOrcSvc
				}(),
				releaseEvaluator: func() release.IEvaluator {
					mockEvaluator := releaseEvaluatorMocks.NewMockIEvaluator(ctr)
					mockEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(woPb.WealthOnboardingFeature_WEALTH_ONBOARDING_FEATURE_UPDATE_NOMINEE_DETAILS).WithActorId("AC220525h9NnvYRgQbWSBIl5vqKL9Q==")).Return(true, nil, nil).Times(1)
					return mockEvaluator
				}(),
			},
			want: &woPb.GetInvestmentDataV2Response{
				Status: rpc.StatusOk(),
				InvestmentDetailInfo: map[string]*woPb.PreInvestmentDetailsV2{"AC220525h9NnvYRgQbWSBIl5vqKL9Q==": {
					FailureType:   woPb.FailureType_FAILURE_TYPE_NOT_RETRYABLE,
					FailureReason: woPb.FailureReason_FAILURE_REASON_AADHAAR_POA_NOT_VALIDATED,
					NextStep:      aadhaarValidationPendingDeeplink,
				}},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				onboardingDetailsDao:     tt.args.onboardingDetailsDao,
				onboardingStepDetailsDao: tt.args.onboardingStepDetailsDao,
				userService:              tt.args.userService,
				orchestratorService:      tt.args.orcSvc,
				idempotentTxnExecutor:    idempotentTxnExecutor,
				conf:                     conf,
				userClient:               mockUserClient,
				releaseEvaluator:         tt.args.releaseEvaluator,
			}
			got, err := s.GetInvestmentDataV2(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetInvestmentDataV2() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(tt.want, got, protocmp.Transform()); diff != "" {
				t.Errorf("GetInvestmentDataV2() mismatch (-want +got):\n%s", diff)
			}
		})
	}
}
