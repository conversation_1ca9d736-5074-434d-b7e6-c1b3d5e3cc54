package upi_test

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/gamma/api/accounts"
	orderPb "github.com/epifi/gamma/api/order"
	domainPb "github.com/epifi/gamma/api/order/domain"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	upiPb "github.com/epifi/gamma/api/upi"
	vgUPIPb "github.com/epifi/gamma/api/vendorgateway/openbanking/upi"
	"github.com/epifi/gamma/pkg/pay"
)

func TestService_CheckAndUpdateAddFundsCollectPaymentStatus(t *testing.T) {
	addFundsPaymentPayload := &paymentPb.AddFundsCollect{
		PaymentDetails: &paymentPb.PaymentDetails{
			PiTo: "pi-1",
		},
	}
	marshalledPayload, err := protojson.Marshal(addFundsPaymentPayload)
	assert.Nil(t, err)

	upiService, mocks, deferFun := getUpiTestServiceWithMock(t)
	mocks.brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	defer deferFun()

	type mockGetOrderWithTransactions struct {
		enable bool
		req    *orderPb.GetOrderWithTransactionsRequest
		res    *orderPb.GetOrderWithTransactionsResponse
		err    error
	}

	type mockReqCheckTxnStatus struct {
		enable bool
		want   *vgUPIPb.ReqCheckTxnStatusResponse
		err    error
	}

	type mockUpdateTransaction struct {
		req  *paymentPb.UpdateTransactionRequest
		want *paymentPb.UpdateTransactionResponse
		err  error
	}

	type mockIsNewAddFundsEnabled struct {
		enable  bool
		actorId string
		want    bool
		err     error
	}

	type mockGetTransaction struct {
		enable bool
		req    *paymentPb.GetTransactionRequest
		res    *paymentPb.GetTransactionResponse
		err    error
	}
	type mockGetPiById struct {
		enable bool
		times  int
		req    *piPb.GetPiByIdRequest
		want   *piPb.GetPiByIdResponse
		err    error
	}

	type mockGetLatestByReqId struct {
		enable     bool
		reqId      string
		upiReqInfo *upiPb.UpiRequestInfo
		err        error
	}

	tests := []struct {
		name                         string
		req                          *domainPb.ProcessPaymentRequest
		res                          *domainPb.ProcessPaymentResponse
		mockReqCheckTxnStatus        mockReqCheckTxnStatus
		mockUpdateTransaction        []mockUpdateTransaction
		mockGetOrderWithTransactions mockGetOrderWithTransactions
		mockGetPiById                mockGetPiById
		mockGetTransaction           mockGetTransaction
		mockIsNewAddFundsEnabled     mockIsNewAddFundsEnabled
		mockGetLatestByReqId         mockGetLatestByReqId
		wantErr                      bool
	}{
		{
			name: "got status successfully and updated txn",
			req: &domainPb.ProcessPaymentRequest{
				RequestHeader: &domainPb.DomainRequestHeader{
					ClientRequestId: "order-id",
				},
				Payload: marshalledPayload,
			},
			res: &domainPb.ProcessPaymentResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{
					Status: domainPb.DomainProcessingStatus_SUCCESS,
				},
			},
			mockGetLatestByReqId: mockGetLatestByReqId{
				enable: true,
				reqId:  "req-1",
				err:    epifierrors.ErrRecordNotFound,
			},
			mockGetOrderWithTransactions: mockGetOrderWithTransactions{
				enable: true,
				req:    &orderPb.GetOrderWithTransactionsRequest{OrderId: "order-id"},
				res: &orderPb.GetOrderWithTransactionsResponse{
					Status: rpc.StatusOk(),
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-id", Workflow: orderPb.OrderWorkflow_ADD_FUNDS_COLLECT, OrderPayload: marshalledPayload},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-1",
								Status:          paymentPb.TransactionStatus_INITIATED,
								PaymentProtocol: paymentPb.PaymentProtocol_UPI,
								PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
								PiFrom:          "pi-external",
								PiTo:            pay.FederalPoolAccountPiId,
								Amount: &moneyPb.Money{
									CurrencyCode: "INR",
									Units:        100,
								},
							},
						},
					},
				},
			},
			mockIsNewAddFundsEnabled: mockIsNewAddFundsEnabled{
				enable:  true,
				actorId: "actor-1",
				want:    false,
				err:     nil,
			},
			mockGetPiById: mockGetPiById{
				enable: true,
				times:  1,
				req:    &piPb.GetPiByIdRequest{Id: "pi-1"},
				want: &piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{Vpa: "internal@fede"},
						},
						Type: piPb.PaymentInstrumentType_UPI,
					},
				},
			},
			mockReqCheckTxnStatus: mockReqCheckTxnStatus{
				enable: true,
				want: &vgUPIPb.ReqCheckTxnStatusResponse{
					Status: rpc.StatusOk(),
					Ref: []*vgUPIPb.ReqCheckTxnStatusResponse_Ref{
						{
							Type:         vgUPIPb.ReqCheckTxnStatusResponse_Ref_PAYER,
							SeqNum:       "1",
							Vpa:          "test@okaxis",
							SettAmount:   "100.0",
							SettCurrency: "INR",
							AccountDetails: &upiPb.CustomerAccountDetails{
								AccountNumber: "**********",
								Ifsc:          "FDRL0005555",
								Type:          accounts.Type_SAVINGS,
							},
							Name: "external user",
						},
						{
							Type:         vgUPIPb.ReqCheckTxnStatusResponse_Ref_PAYEE,
							SeqNum:       "1",
							Vpa:          "addfi.**********@fede",
							SettAmount:   "100.0",
							SettCurrency: "INR",
							AccountDetails: &upiPb.CustomerAccountDetails{
								AccountNumber: "**********",
								Ifsc:          "FDRL0005555",
								Type:          accounts.Type_SAVINGS,
							},
							Name: "internal user",
						},
					},
					RawStatusCode:        "00",
					RawStatusDescription: "Success",
				},
			},
			mockUpdateTransaction: []mockUpdateTransaction{
				{
					req: &paymentPb.UpdateTransactionRequest{
						Transaction: &paymentPb.Transaction{
							Id:              "txn-1",
							Status:          paymentPb.TransactionStatus_SUCCESS,
							PaymentProtocol: paymentPb.PaymentProtocol_UPI,
							PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
							PiFrom:          "pi-external",
							PiTo:            pay.FederalPoolAccountPiId,
							Amount: &moneyPb.Money{
								CurrencyCode: "INR",
								Units:        100,
							},
							DetailedStatus: &paymentPb.TransactionDetailedStatus{
								DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
									{
										State:                paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
										RawStatusCode:        "00",
										RawStatusDescription: "Success",
										Api:                  paymentPb.TransactionDetailedStatus_DetailedStatus_UPI_PAYMENT_ENQUIRY,
									},
								},
							},
						},
						ReqInfo: &paymentPb.PaymentRequestInformation{
							ReqId: "req-1",
							UpiInfo: &paymentPb.PaymentRequestInformation_UPI{
								CustomerAccountInfo: &upiPb.CustomerAccountDetails{
									AccountNumber: "**********",
									Ifsc:          "FDRL0005555",
									Type:          accounts.Type_SAVINGS,
								},
							},
						},
						FieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_DETAILED_STATUS, paymentPb.TransactionFieldMask_STATUS,
							paymentPb.TransactionFieldMask_PAYMENT_REQ_INFO},
					},
					want: &paymentPb.UpdateTransactionResponse{
						Status: rpc.StatusOk(),
					},
					err: nil,
				},
			},
			mockGetTransaction: mockGetTransaction{
				enable: true,
				req: &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_TransactionId{
						TransactionId: "txn-1",
					},
					GetReqInfo: true,
				},
				res: &paymentPb.GetTransactionResponse{
					Status: rpc.StatusOk(),
					Transaction: &paymentPb.Transaction{
						Id:              "txn-1",
						Status:          paymentPb.TransactionStatus_INITIATED,
						PaymentProtocol: paymentPb.PaymentProtocol_UPI,
						PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
						PiFrom:          "pi-external",
						PiTo:            pay.FederalPoolAccountPiId,
						Amount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        100,
						},
					},
					ReqInfo: &paymentPb.PaymentRequestInformation{
						ReqId: "req-1",
					},
				},
			},
		},
		{
			name: "update txn to failed state",
			req: &domainPb.ProcessPaymentRequest{
				RequestHeader: &domainPb.DomainRequestHeader{
					ClientRequestId: "order-id",
				},
				Payload: marshalledPayload,
			},
			res: &domainPb.ProcessPaymentResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{
					Status: domainPb.DomainProcessingStatus_PERMANENT_FAILURE,
				},
			},
			mockGetLatestByReqId: mockGetLatestByReqId{
				enable: true,
				reqId:  "req-1",
				err:    epifierrors.ErrRecordNotFound,
			},
			mockGetOrderWithTransactions: mockGetOrderWithTransactions{
				enable: true,
				req:    &orderPb.GetOrderWithTransactionsRequest{OrderId: "order-id"},
				res: &orderPb.GetOrderWithTransactionsResponse{
					Status: rpc.StatusOk(),
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-id", Workflow: orderPb.OrderWorkflow_ADD_FUNDS_COLLECT, OrderPayload: marshalledPayload},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-1",
								Status:          paymentPb.TransactionStatus_INITIATED,
								PaymentProtocol: paymentPb.PaymentProtocol_UPI,
								PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
								PiFrom:          "pi-external",
								PiTo:            pay.FederalPoolAccountPiId,
								Amount: &moneyPb.Money{
									CurrencyCode: "INR",
									Units:        100,
								},
							},
						},
					},
				},
			},
			mockIsNewAddFundsEnabled: mockIsNewAddFundsEnabled{
				enable:  true,
				actorId: "actor-1",
				want:    false,
				err:     nil,
			},
			mockGetPiById: mockGetPiById{
				enable: true,
				times:  1,
				req:    &piPb.GetPiByIdRequest{Id: "pi-1"},
				want: &piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{Vpa: "internal@fede"},
						},
						Type: piPb.PaymentInstrumentType_UPI,
					},
				},
			},
			mockReqCheckTxnStatus: mockReqCheckTxnStatus{
				enable: true,
				want: &vgUPIPb.ReqCheckTxnStatusResponse{
					Status: rpc.NewStatus(uint32(vgUPIPb.ReqCheckTxnStatusResponse_BUSINESS_FAILURE), "", ""),
					Ref: []*vgUPIPb.ReqCheckTxnStatusResponse_Ref{
						{
							Type:         vgUPIPb.ReqCheckTxnStatusResponse_Ref_PAYER,
							SeqNum:       "1",
							Vpa:          "test@okaxis",
							SettAmount:   "100.0",
							SettCurrency: "INR",
							AccountDetails: &upiPb.CustomerAccountDetails{
								AccountNumber: "**********",
								Ifsc:          "FDRL0005555",
								Type:          accounts.Type_SAVINGS,
							},
							Name: "external user",
						},
						{
							Type:         vgUPIPb.ReqCheckTxnStatusResponse_Ref_PAYEE,
							SeqNum:       "1",
							Vpa:          "addfi.**********@fede",
							SettAmount:   "100.0",
							SettCurrency: "INR",
							AccountDetails: &upiPb.CustomerAccountDetails{
								AccountNumber: "**********",
								Ifsc:          "FDRL0005555",
								Type:          accounts.Type_SAVINGS,
							},
							Name: "internal user",
						},
					},
					RawStatusCode:        "00",
					RawStatusDescription: "Success",
				},
			},
			mockUpdateTransaction: []mockUpdateTransaction{
				{
					req: &paymentPb.UpdateTransactionRequest{
						Transaction: &paymentPb.Transaction{
							Id:              "txn-1",
							Status:          paymentPb.TransactionStatus_FAILED,
							PaymentProtocol: paymentPb.PaymentProtocol_UPI,
							PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
							PiFrom:          "pi-external",
							PiTo:            pay.FederalPoolAccountPiId,
							Amount: &moneyPb.Money{
								CurrencyCode: "INR",
								Units:        100,
							},
							DetailedStatus: &paymentPb.TransactionDetailedStatus{
								DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
									{
										State:                paymentPb.TransactionDetailedStatus_DetailedStatus_FAILURE,
										RawStatusCode:        "00",
										RawStatusDescription: "Success",
										ErrorCategory:        paymentPb.TransactionDetailedStatus_DetailedStatus_USER,
										Api:                  paymentPb.TransactionDetailedStatus_DetailedStatus_UPI_PAYMENT_ENQUIRY,
									},
								},
							},
						},
						ReqInfo: &paymentPb.PaymentRequestInformation{
							ReqId: "req-1",
							UpiInfo: &paymentPb.PaymentRequestInformation_UPI{
								CustomerAccountInfo: &upiPb.CustomerAccountDetails{
									AccountNumber: "**********",
									Ifsc:          "FDRL0005555",
									Type:          accounts.Type_SAVINGS,
								},
							},
						},
						FieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_DETAILED_STATUS, paymentPb.TransactionFieldMask_STATUS,
							paymentPb.TransactionFieldMask_PAYMENT_REQ_INFO},
					},
					want: &paymentPb.UpdateTransactionResponse{
						Status: rpc.StatusOk(),
					},
					err: nil,
				},
			},
			mockGetTransaction: mockGetTransaction{
				enable: true,
				req: &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_TransactionId{
						TransactionId: "txn-1",
					},
					GetReqInfo: true,
				},
				res: &paymentPb.GetTransactionResponse{
					Status: rpc.StatusOk(),
					Transaction: &paymentPb.Transaction{
						Id:              "txn-1",
						Status:          paymentPb.TransactionStatus_INITIATED,
						PaymentProtocol: paymentPb.PaymentProtocol_UPI,
						PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
						PiFrom:          "pi-external",
						PiTo:            pay.FederalPoolAccountPiId,
						Amount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        100,
						},
					},
					ReqInfo: &paymentPb.PaymentRequestInformation{
						ReqId: "req-1",
					},
				},
			},
		},
		{
			name: "handle unavailable gracefully",
			req: &domainPb.ProcessPaymentRequest{
				RequestHeader: &domainPb.DomainRequestHeader{
					ClientRequestId: "order-id",
				},
				Payload: marshalledPayload,
			},
			res: &domainPb.ProcessPaymentResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{
					Status: domainPb.DomainProcessingStatus_IN_PROGRESS,
				},
			},
			mockGetLatestByReqId: mockGetLatestByReqId{
				enable: true,
				reqId:  "req-1",
				err:    epifierrors.ErrRecordNotFound,
			},
			mockGetOrderWithTransactions: mockGetOrderWithTransactions{
				enable: true,
				req:    &orderPb.GetOrderWithTransactionsRequest{OrderId: "order-id"},
				res: &orderPb.GetOrderWithTransactionsResponse{
					Status: rpc.StatusOk(),
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-id", Workflow: orderPb.OrderWorkflow_ADD_FUNDS_COLLECT, OrderPayload: marshalledPayload},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-1",
								Status:          paymentPb.TransactionStatus_INITIATED,
								PaymentProtocol: paymentPb.PaymentProtocol_UPI,
								PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
								PiFrom:          "pi-external",
								PiTo:            pay.FederalPoolAccountPiId,
								Amount: &moneyPb.Money{
									CurrencyCode: "INR",
									Units:        100,
								},
							},
						},
					},
				},
			},
			mockIsNewAddFundsEnabled: mockIsNewAddFundsEnabled{
				enable:  true,
				actorId: "actor-1",
				want:    false,
				err:     nil,
			},
			mockGetPiById: mockGetPiById{
				enable: true,
				times:  1,
				req:    &piPb.GetPiByIdRequest{Id: "pi-1"},
				want: &piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{Vpa: "internal@fede"},
						},
						Type: piPb.PaymentInstrumentType_UPI,
					},
				},
			},
			mockGetTransaction: mockGetTransaction{
				enable: true,
				req: &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_TransactionId{
						TransactionId: "txn-1",
					},
					GetReqInfo: true,
				},
				res: &paymentPb.GetTransactionResponse{
					Status: rpc.StatusOk(),
					Transaction: &paymentPb.Transaction{
						Id:              "txn-1",
						Status:          paymentPb.TransactionStatus_INITIATED,
						PaymentProtocol: paymentPb.PaymentProtocol_UPI,
						PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
						PiFrom:          "pi-external",
						PiTo:            pay.FederalPoolAccountPiId,
						Amount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        100,
						},
					},
					ReqInfo: &paymentPb.PaymentRequestInformation{
						ReqId: "req-1",
					},
				},
			},
			mockReqCheckTxnStatus: mockReqCheckTxnStatus{
				enable: true,
				want: &vgUPIPb.ReqCheckTxnStatusResponse{
					Status: rpc.StatusUnavailable(),
				},
			},
		},
		{
			name: "should return permanent failure for record not found as last attempt",
			req: &domainPb.ProcessPaymentRequest{
				RequestHeader: &domainPb.DomainRequestHeader{
					ClientRequestId: "order-id",
					IsLastAttempt:   true,
				},
				Payload: marshalledPayload,
			},
			res: &domainPb.ProcessPaymentResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{
					Status: domainPb.DomainProcessingStatus_PERMANENT_FAILURE,
				},
			},
			mockGetLatestByReqId: mockGetLatestByReqId{
				enable: true,
				reqId:  "req-1",
				err:    epifierrors.ErrRecordNotFound,
			},
			mockGetOrderWithTransactions: mockGetOrderWithTransactions{
				enable: true,
				req:    &orderPb.GetOrderWithTransactionsRequest{OrderId: "order-id"},
				res: &orderPb.GetOrderWithTransactionsResponse{
					Status: rpc.StatusOk(),
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-id", Workflow: orderPb.OrderWorkflow_ADD_FUNDS_COLLECT, OrderPayload: marshalledPayload},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-1",
								Status:          paymentPb.TransactionStatus_INITIATED,
								PaymentProtocol: paymentPb.PaymentProtocol_UPI,
								PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
								PiFrom:          "pi-external",
								PiTo:            pay.FederalPoolAccountPiId,
								Amount: &moneyPb.Money{
									CurrencyCode: "INR",
									Units:        100,
								},
							},
						},
					},
				},
			},
			mockIsNewAddFundsEnabled: mockIsNewAddFundsEnabled{
				enable:  true,
				actorId: "actor-1",
				want:    false,
				err:     nil,
			},
			mockGetPiById: mockGetPiById{
				enable: true,
				times:  1,
				req:    &piPb.GetPiByIdRequest{Id: "pi-1"},
				want: &piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{Vpa: "internal@fede"},
						},
						Type: piPb.PaymentInstrumentType_UPI,
					},
				},
			},
			mockGetTransaction: mockGetTransaction{
				enable: true,
				req: &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_TransactionId{
						TransactionId: "txn-1",
					},
					GetReqInfo: true,
				},
				res: &paymentPb.GetTransactionResponse{
					Status: rpc.StatusOk(),
					Transaction: &paymentPb.Transaction{
						Id:              "txn-1",
						Status:          paymentPb.TransactionStatus_INITIATED,
						PaymentProtocol: paymentPb.PaymentProtocol_UPI,
						PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
						PiFrom:          "pi-external",
						PiTo:            pay.FederalPoolAccountPiId,
						Amount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        100,
						},
					},
					ReqInfo: &paymentPb.PaymentRequestInformation{
						ReqId: "req-1",
					},
				},
			},
			mockReqCheckTxnStatus: mockReqCheckTxnStatus{
				enable: true,
				want: &vgUPIPb.ReqCheckTxnStatusResponse{
					Status: rpc.StatusRecordNotFound(),
				},
			},
			mockUpdateTransaction: []mockUpdateTransaction{
				{
					req: &paymentPb.UpdateTransactionRequest{
						Transaction: &paymentPb.Transaction{
							Id:              "txn-1",
							Status:          paymentPb.TransactionStatus_FAILED,
							PaymentProtocol: paymentPb.PaymentProtocol_UPI,
							PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
							PiFrom:          "pi-external",
							PiTo:            pay.FederalPoolAccountPiId,
							Amount: &moneyPb.Money{
								CurrencyCode: "INR",
								Units:        100,
							},
						},
						FieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_STATUS},
					},
					want: &paymentPb.UpdateTransactionResponse{
						Status: rpc.StatusOk(),
					},
					err: nil,
				},
			},
		},
		{
			name: "move txn to manual intervention",
			req: &domainPb.ProcessPaymentRequest{
				RequestHeader: &domainPb.DomainRequestHeader{
					ClientRequestId: "order-id",
					IsLastAttempt:   true,
				},
				Payload: marshalledPayload,
			},
			res: &domainPb.ProcessPaymentResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{
					Status: domainPb.DomainProcessingStatus_IN_PROGRESS,
				},
			},
			mockGetLatestByReqId: mockGetLatestByReqId{
				enable: true,
				reqId:  "req-1",
				err:    epifierrors.ErrRecordNotFound,
			},
			mockGetOrderWithTransactions: mockGetOrderWithTransactions{
				enable: true,
				req:    &orderPb.GetOrderWithTransactionsRequest{OrderId: "order-id"},
				res: &orderPb.GetOrderWithTransactionsResponse{
					Status: rpc.StatusOk(),
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{Id: "order-id", Workflow: orderPb.OrderWorkflow_ADD_FUNDS_COLLECT, OrderPayload: marshalledPayload},
						Transactions: []*paymentPb.Transaction{
							{
								Id:              "txn-1",
								Status:          paymentPb.TransactionStatus_INITIATED,
								PaymentProtocol: paymentPb.PaymentProtocol_UPI,
								PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
								PiFrom:          "pi-external",
								PiTo:            pay.FederalPoolAccountPiId,
								Amount: &moneyPb.Money{
									CurrencyCode: "INR",
									Units:        100,
								},
							},
						},
					},
				},
			},
			mockIsNewAddFundsEnabled: mockIsNewAddFundsEnabled{
				enable:  true,
				actorId: "actor-1",
				want:    false,
				err:     nil,
			},
			mockGetPiById: mockGetPiById{
				enable: true,
				times:  1,
				req:    &piPb.GetPiByIdRequest{Id: "pi-1"},
				want: &piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{Vpa: "internal@fede"},
						},
						Type: piPb.PaymentInstrumentType_UPI,
					},
				},
			},
			mockReqCheckTxnStatus: mockReqCheckTxnStatus{
				enable: true,
				want: &vgUPIPb.ReqCheckTxnStatusResponse{
					Status: rpc.ExtendedStatusInProgress(),
					Ref: []*vgUPIPb.ReqCheckTxnStatusResponse_Ref{
						{
							Type:         vgUPIPb.ReqCheckTxnStatusResponse_Ref_PAYER,
							SeqNum:       "1",
							Vpa:          "test@okaxis",
							SettAmount:   "100.0",
							SettCurrency: "INR",
							AccountDetails: &upiPb.CustomerAccountDetails{
								AccountNumber: "**********",
								Ifsc:          "FDRL0005555",
								Type:          accounts.Type_SAVINGS,
							},
							Name: "external user",
						},
						{
							Type:         vgUPIPb.ReqCheckTxnStatusResponse_Ref_PAYEE,
							SeqNum:       "1",
							Vpa:          "addfi.**********@fede",
							SettAmount:   "100.0",
							SettCurrency: "INR",
							AccountDetails: &upiPb.CustomerAccountDetails{
								AccountNumber: "**********",
								Ifsc:          "FDRL0005555",
								Type:          accounts.Type_SAVINGS,
							},
							Name: "internal user",
						},
					},
					RawStatusCode:        "00",
					RawStatusDescription: "Success",
				},
			},
			mockUpdateTransaction: []mockUpdateTransaction{
				{
					req: &paymentPb.UpdateTransactionRequest{
						Transaction: &paymentPb.Transaction{
							Id:              "txn-1",
							Status:          paymentPb.TransactionStatus_IN_PROGRESS,
							PaymentProtocol: paymentPb.PaymentProtocol_UPI,
							PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
							PiFrom:          "pi-external",
							PiTo:            pay.FederalPoolAccountPiId,
							Amount: &moneyPb.Money{
								CurrencyCode: "INR",
								Units:        100,
							},
							DetailedStatus: &paymentPb.TransactionDetailedStatus{
								DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
									{
										State:                paymentPb.TransactionDetailedStatus_DetailedStatus_IN_PROGRESS,
										RawStatusCode:        "00",
										RawStatusDescription: "Success",
										Api:                  paymentPb.TransactionDetailedStatus_DetailedStatus_UPI_PAYMENT_ENQUIRY,
									},
								},
							},
						},
						ReqInfo: &paymentPb.PaymentRequestInformation{
							ReqId: "req-1",
							UpiInfo: &paymentPb.PaymentRequestInformation_UPI{
								CustomerAccountInfo: &upiPb.CustomerAccountDetails{
									AccountNumber: "**********",
									Ifsc:          "FDRL0005555",
									Type:          accounts.Type_SAVINGS,
								},
							},
						},
						FieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_DETAILED_STATUS, paymentPb.TransactionFieldMask_STATUS,
							paymentPb.TransactionFieldMask_PAYMENT_REQ_INFO},
					},
					want: &paymentPb.UpdateTransactionResponse{
						Status: rpc.StatusOk(),
					},
					err: nil,
				},
				{
					req: &paymentPb.UpdateTransactionRequest{
						Transaction: &paymentPb.Transaction{
							Id:              "txn-1",
							Status:          paymentPb.TransactionStatus_MANUAL_INTERVENTION,
							PaymentProtocol: paymentPb.PaymentProtocol_UPI,
							PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
							PiFrom:          "pi-external",
							PiTo:            pay.FederalPoolAccountPiId,
							Amount: &moneyPb.Money{
								CurrencyCode: "INR",
								Units:        100,
							},
							DetailedStatus: &paymentPb.TransactionDetailedStatus{
								DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
									{
										State:                paymentPb.TransactionDetailedStatus_DetailedStatus_IN_PROGRESS,
										RawStatusCode:        "00",
										RawStatusDescription: "Success",
										Api:                  paymentPb.TransactionDetailedStatus_DetailedStatus_UPI_PAYMENT_ENQUIRY,
									},
								},
							},
						},
						FieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_STATUS},
					},
					want: &paymentPb.UpdateTransactionResponse{
						Status: rpc.StatusOk(),
					},
					err: nil,
				},
			},
			mockGetTransaction: mockGetTransaction{
				enable: true,
				req: &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_TransactionId{
						TransactionId: "txn-1",
					},
					GetReqInfo: true,
				},
				res: &paymentPb.GetTransactionResponse{
					Status: rpc.StatusOk(),
					Transaction: &paymentPb.Transaction{
						Id:              "txn-1",
						Status:          paymentPb.TransactionStatus_INITIATED,
						PaymentProtocol: paymentPb.PaymentProtocol_UPI,
						PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
						PiFrom:          "pi-external",
						PiTo:            pay.FederalPoolAccountPiId,
						Amount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        100,
						},
					},
					ReqInfo: &paymentPb.PaymentRequestInformation{
						ReqId: "req-1",
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetOrderWithTransactions.enable {
				mocks.mockOrderClient.EXPECT().GetOrderWithTransactions(context.Background(), tt.mockGetOrderWithTransactions.req).
					Return(tt.mockGetOrderWithTransactions.res, tt.mockGetOrderWithTransactions.err)
			}
			if tt.mockReqCheckTxnStatus.enable {
				mocks.mockVgUpiClient.EXPECT().ReqCheckURNTxnStatus(gomock.Any(), gomock.Any()).
					Return(tt.mockReqCheckTxnStatus.want, tt.mockReqCheckTxnStatus.err)
			}
			for _, mockUpdate := range tt.mockUpdateTransaction {
				mocks.mockPaymentClient.EXPECT().UpdateTransaction(context.Background(), pay.NewUpdateTxnRpcArgMatcher(mockUpdate.req)).
					Return(mockUpdate.want, mockUpdate.err)
			}
			if tt.mockGetTransaction.enable {
				mocks.mockPaymentClient.EXPECT().GetTransaction(context.Background(), tt.mockGetTransaction.req).
					Return(tt.mockGetTransaction.res, tt.mockGetTransaction.err)
			}
			if tt.mockIsNewAddFundsEnabled.enable {
				mocks.mockUpiOnboardingProcessor.EXPECT().IsNewAddFundsVpaEnabledForActor(context.Background(), gomock.Any()).
					Return(tt.mockIsNewAddFundsEnabled.want)
			}
			if tt.mockGetPiById.enable {
				mocks.mockPIClient.EXPECT().GetPiById(context.Background(), tt.mockGetPiById.req).
					Return(tt.mockGetPiById.want, tt.mockGetPiById.err).Times(tt.mockGetPiById.times)
			}
			if tt.mockGetLatestByReqId.enable {
				mocks.mockUpiReqInfoDao.EXPECT().GetLatestByReqId(context.Background(), tt.mockGetLatestByReqId.reqId).
					Return(tt.mockGetLatestByReqId.upiReqInfo, tt.mockGetLatestByReqId.err)
			}
			got, err := upiService.CheckAndUpdateAddFundsCollectPaymentStatus(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetOrCreateAddFundsTransaction() got err: %v, wantErr: %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.res) {
				t.Errorf("GetOrCreateAddFundsTransaction() got: %v, want: %v", got, tt.res)
				return
			}

		})
	}
}
