package consumer

import (
	"context"
	"fmt"

	"github.com/epifi/be-common/pkg/epifierrors"

	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/be-common/pkg/queue"
)

// createTxn creates transaction by calling `CreateTransaction` RPC of payment service
func (s *Service) createTxn(ctx context.Context, req *paymentPb.CreateTransactionRequest) (*paymentPb.Transaction, error) {
	res, err := s.paymentClient.CreateTransaction(ctx, req)
	switch {
	case err != nil:
		return nil, fmt.Errorf("CreateTransaction RPC failed: %v: %w", err, queue.ErrRPC)
	case !res.Status.IsSuccess():
		return nil, fmt.Errorf("for unexpected response from CreateTransaction RPC: %v: %w", res.GetStatus(), queue.ErrTransient)
	default:
		return res.GetTransaction(), nil
	}
}

// createOrderAndTxn creates order and then transaction with the same created order id
// It calls `CreateOrderAndTransaction` RPC of order service internally which makes sure order and txn are created in
// a single DB transaction.
// nolint:unparam
func (s *Service) createOrderAndTxn(ctx context.Context, req *orderPb.CreateOrderWithTransactionRequest) (*paymentPb.Transaction, *orderPb.Order, error) {

	res, err := s.orderClient.CreateOrderWithTransaction(ctx, req)
	switch {
	case err != nil:
		return nil, nil, fmt.Errorf("CreateOrderAndTransaction RPC failed: %w", epifierrors.ErrTransient)
	case res.GetStatus().IsAlreadyExists():
		// due to utr collisions for off app transactions, we might get order/transaction already exists
		// returning permanent error here as retrying the packet will not help
		return nil, nil, fmt.Errorf("order and Trasnaction already exists: utr: %s :%w", req.GetTransactionParam().GetUtr(), epifierrors.ErrPermanent)
	case !res.Status.IsSuccess():
		return nil, nil, fmt.Errorf("CreateOrderAndTransaction returned %s: %w", res.GetStatus(), epifierrors.ErrTransient)

	default:
		return res.GetTransaction(), res.GetOrder(), nil
	}
}

// updateOrder updates order using underlying `UpdateOrder`
func (s *Service) updateOrder(ctx context.Context, order *orderPb.Order, updateMask []orderPb.OrderFieldMask) error {
	res, err := s.orderClient.UpdateOrder(ctx, &orderPb.UpdateOrderRequest{Order: order, FieldMasks: updateMask})

	switch {
	case err != nil:
		return fmt.Errorf("UpdateOrder RPC failed: %v: %w", err, queue.ErrRPC)
	case !res.GetStatus().IsSuccess():
		return fmt.Errorf("UpdateOrder RPC not successful: %v: %w", res.GetStatus(), queue.ErrTransient)
	}

	return nil
}

// updateOrderAndCreateTxn updates order's status and from actor id and creates transaction associated with the order
func (s *Service) updateOrderAndCreateTxn(ctx context.Context, payerActorId string, order *orderPb.Order,
	txnReq *paymentPb.CreateTransactionRequest) (*paymentPb.Transaction, error) {
	txnReq.OrderId = order.Id

	// TODO(nitesh): how can we maintain atomicity ?? May be make create Txn Idempotent and then perform the operation
	// in a transactional block
	// TODO(nitesh): consider having and RPC to create txn and update order
	// Ref- https://github.com/epiFi/gamma/pull/2341#discussion_r478892768

	// Update of order is done first as the update RPC contains necessary validations
	// if they pass i.e. update is successful, then we go ahead and create transaction
	order.Status = orderPb.TxnToOrderStatusMap[txnReq.GetStatus()]
	order.FromActorId = payerActorId
	err := s.updateOrder(ctx, order, []orderPb.OrderFieldMask{orderPb.OrderFieldMask_STATUS, orderPb.OrderFieldMask_FROM_ACTOR_ID})
	if err != nil {
		return nil, err
	}

	txn, err := s.createTxn(ctx, txnReq)
	if err != nil {
		return nil, err
	}

	return txn, nil
}
