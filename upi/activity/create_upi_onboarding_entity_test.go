package activity_test

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"errors"
	"fmt"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	celestialPb "github.com/epifi/be-common/api/celestial"
	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	upiNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/upi"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	upiActivityPb "github.com/epifi/gamma/api/upi/activity"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
	upiOnboardingEnumsPb "github.com/epifi/gamma/api/upi/onboarding/enums"
	upiPayloadPb "github.com/epifi/gamma/api/upi/payload"
	"github.com/epifi/gamma/upi/dao"
)

const (
	layout = "2006.01.02"
)

var (
	payload, _ = protojson.Marshal(&upiPayloadPb.LinkUpiAccountInfo{
		AccountId: "account-id-1",
		ActorId:   "actor-id-1",
		ClientId: &celestialPb.ClientReqId{
			Id:     "client-req-id-1",
			Client: workflowPb.Client_USER_APP,
		},
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
	})

	delinkPayload, _ = protojson.Marshal(&upiPayloadPb.DelinkUpiAccountWithVendorInfo{
		AccountId: "account-id-1",
		ActorId:   "actor-id-1",
		ClientId: &celestialPb.ClientReqId{
			Id:     "client-req-id-1",
			Client: workflowPb.Client_USER_APP,
		},
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
	})
)

type ValidateCreateOnbDetails struct {
	want *upiOnboardingPb.UpiOnboardingDetail
}

func newValidateCreateOnbDetails(want *upiOnboardingPb.UpiOnboardingDetail) *ValidateCreateOnbDetails {
	return &ValidateCreateOnbDetails{want: want}
}

func (c *ValidateCreateOnbDetails) Matches(x interface{}) bool {
	got, ok := x.(*upiOnboardingPb.UpiOnboardingDetail)
	if !ok {
		return false
	}

	c.want.Payload = got.Payload
	return proto.Equal(c.want, got)
}

func (c *ValidateCreateOnbDetails) String() string {
	return fmt.Sprintf("want: %v", c.want)
}

func TestProcessor_CreateUpiOnboardingEntityForLinking(t *testing.T) {
	act, md, assertTest := newProcessorWithMocks(t)
	defer assertTest()
	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(act)

	type args struct {
		req *activityPb.Request
	}

	type mockGetByClientRequestId struct {
		enable       bool
		clientReqId  string
		upiOnbDetail *upiOnboardingPb.UpiOnboardingDetail
		err          error
	}

	type mockGetLatestByAccountId struct {
		enable        bool
		accountId     string
		filterOptions []storagev2.FilterOption
		want          *upiOnboardingPb.UpiOnboardingDetail
		err           error
	}

	type mockCreate struct {
		enable       bool
		upiOnbDetail *upiOnboardingPb.UpiOnboardingDetail
		err          error
	}

	tests := []struct {
		name                     string
		args                     args
		mockGetByClientRequestId mockGetByClientRequestId
		mockGetLatestByAccountId mockGetLatestByAccountId
		mockCreate               mockCreate
		wantErr                  bool
		assertErr                func(err error) bool
	}{
		{
			name: "successfully created upi onboarding detail entity for linking",
			args: args{
				req: &activityPb.Request{
					ClientReqId: "client-req-id-1",
					Payload:     payload,
				},
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				err:         epifierrors.ErrRecordNotFound,
			},
			mockGetLatestByAccountId: mockGetLatestByAccountId{
				enable:    true,
				accountId: "account-id-1",
				filterOptions: []storagev2.FilterOption{
					dao.WithVendorFilter(commonvgpb.Vendor_FEDERAL_BANK),
				},
				want: nil,
				err:  epifierrors.ErrRecordNotFound,
			},
			mockCreate: mockCreate{
				enable: true,
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_LINK,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
				},
			},
			wantErr: false,
		},
		{
			name: "failed to create upi onboarding detail entity because validation failed as account is already linked",
			args: args{
				req: &activityPb.Request{
					ClientReqId: "client-req-id-1",
					Payload:     payload,
				},
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				err:         epifierrors.ErrRecordNotFound,
			},
			mockGetLatestByAccountId: mockGetLatestByAccountId{
				enable:    true,
				accountId: "account-id-1",
				filterOptions: []storagev2.FilterOption{
					dao.WithVendorFilter(commonvgpb.Vendor_FEDERAL_BANK),
				},
				want: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_LINK,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_SUCCESSFUL,
				},
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "failed to create upi onboarding detail entity because validation failed as delink is in progress",
			args: args{
				req: &activityPb.Request{
					ClientReqId: "client-req-id-1",
					Payload:     payload,
				},
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				err:         epifierrors.ErrRecordNotFound,
			},
			mockGetLatestByAccountId: mockGetLatestByAccountId{
				enable:    true,
				accountId: "account-id-1",
				filterOptions: []storagev2.FilterOption{
					dao.WithVendorFilter(commonvgpb.Vendor_FEDERAL_BANK),
				},
				want: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DELINK,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
				},
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "successfully created upi onboarding detail entity as it was delinked previously",
			args: args{
				req: &activityPb.Request{
					ClientReqId: "client-req-id-1",
					Payload:     payload,
				},
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				err:         epifierrors.ErrRecordNotFound,
			},
			mockGetLatestByAccountId: mockGetLatestByAccountId{
				enable:    true,
				accountId: "account-id-1",
				filterOptions: []storagev2.FilterOption{
					dao.WithVendorFilter(commonvgpb.Vendor_FEDERAL_BANK),
				},
				want: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DELINK,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_SUCCESSFUL,
				},
			},
			mockCreate: mockCreate{
				enable: true,
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_LINK,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
				},
			},
			wantErr: false,
		},
		{
			name: "successfully created upi onboarding detail entity as previous linking failed",
			args: args{
				req: &activityPb.Request{
					ClientReqId: "client-req-id-1",
					Payload:     payload,
				},
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				err:         epifierrors.ErrRecordNotFound,
			},
			mockGetLatestByAccountId: mockGetLatestByAccountId{
				enable:    true,
				accountId: "account-id-1",
				filterOptions: []storagev2.FilterOption{
					dao.WithVendorFilter(commonvgpb.Vendor_FEDERAL_BANK),
				},
				want: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_LINK,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_FAILED,
				},
			},
			mockCreate: mockCreate{
				enable: true,
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_LINK,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
				},
			},
			wantErr: false,
		},
		{
			name: "failed to creat upi onboarding detail entity because creation failed due to some DB issue",
			args: args{
				req: &activityPb.Request{
					ClientReqId: "client-req-id-1",
					Payload:     payload,
				},
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				err:         epifierrors.ErrRecordNotFound,
			},
			mockGetLatestByAccountId: mockGetLatestByAccountId{
				enable:    true,
				accountId: "account-id-1",
				filterOptions: []storagev2.FilterOption{
					dao.WithVendorFilter(commonvgpb.Vendor_FEDERAL_BANK),
				},
				want: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",

					Action: upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_LINK,
					Status: upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_FAILED,
				},
			},
			mockCreate: mockCreate{
				enable: true,
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_LINK,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
				},
				err: errors.New("db issue"),
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "failed to creat upi onboarding detail entity because creation failed due to invalid args",
			args: args{
				req: &activityPb.Request{
					ClientReqId: "client-req-id-1",
					Payload:     payload,
				},
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				err:         epifierrors.ErrRecordNotFound,
			},
			mockGetLatestByAccountId: mockGetLatestByAccountId{
				enable:    true,
				accountId: "account-id-1",
				filterOptions: []storagev2.FilterOption{
					dao.WithVendorFilter(commonvgpb.Vendor_FEDERAL_BANK),
				},
				want: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_LINK,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_FAILED,
				},
			},
			mockCreate: mockCreate{
				enable: true,
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_LINK,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
				},
				err: epifierrors.ErrInvalidArgument,
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "upi onboarding detail entity already exists for given client req id",
			args: args{
				req: &activityPb.Request{
					ClientReqId: "client-req-id-1",
					Payload:     payload,
				},
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_LINK,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
				},
			},
			wantErr: false,
		},
		{
			name: "upi onboarding detail entity already exists in unexpected state",
			args: args{
				req: &activityPb.Request{
					ClientReqId: "client-req-id-1",
					Payload:     payload,
				},
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_LINK,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_SUCCESSFUL,
				},
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetByClientRequestId.enable {
				md.upiOnboardingDetailDao.EXPECT().GetByClientRequestId(gomock.Any(),
					tt.mockGetByClientRequestId.clientReqId).
					Return(tt.mockGetByClientRequestId.upiOnbDetail, tt.mockGetByClientRequestId.err)
			}

			if tt.mockGetLatestByAccountId.enable {
				md.upiOnboardingDetailDao.EXPECT().GetLatestByAccountId(gomock.Any(),
					tt.mockGetLatestByAccountId.accountId, gomock.Any()).
					Return(tt.mockGetLatestByAccountId.want, tt.mockGetLatestByAccountId.err)
			}

			if tt.mockCreate.enable {
				md.upiOnboardingDetailDao.EXPECT().Create(gomock.Any(), tt.mockCreate.upiOnbDetail).
					Return(tt.mockCreate.err)
			}

			if _, err := env.ExecuteActivity(upiNs.CreateUpiOnboardingEntityForLinking,
				tt.args.req); (err != nil) != tt.wantErr {
				t.Errorf("CreateUpiOnboardingEntityForLinking() error = %v, wantErr %v", err, tt.wantErr)
			} else if tt.wantErr && !tt.assertErr(err) {
				t.Errorf("CreateUpiOnboardingEntityForLinking() error = %v assertion failed", err)
				return
			}
			assertTest()
		})
	}
}

func TestProcessor_CreateUpiOnboardingEntityForDelinking(t *testing.T) {
	act, md, assertTest := newProcessorWithMocks(t)
	defer assertTest()
	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(act)

	type args struct {
		req *upiActivityPb.CreateUpiOnboardingEntityForDelinkingRequest
	}

	type mockGetByClientRequestId struct {
		enable       bool
		clientReqId  string
		upiOnbDetail *upiOnboardingPb.UpiOnboardingDetail
		err          error
	}

	type mockGetLatestByAccountId struct {
		enable        bool
		accountId     string
		filterOptions []storagev2.FilterOption
		want          *upiOnboardingPb.UpiOnboardingDetail
		err           error
	}

	type mockCreate struct {
		enable       bool
		upiOnbDetail *upiOnboardingPb.UpiOnboardingDetail
		err          error
	}

	tests := []struct {
		name                     string
		args                     args
		mockGetByClientRequestId mockGetByClientRequestId
		mockGetLatestByAccountId mockGetLatestByAccountId
		mockCreate               mockCreate
		wantErr                  bool
		assertErr                func(err error) bool
	}{
		{
			name: "successfully created upi onboarding detail entity for delinking",
			args: args{
				req: &upiActivityPb.CreateUpiOnboardingEntityForDelinkingRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "client-req-id-1",
						Payload:     delinkPayload,
					},
				},
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				err:         epifierrors.ErrRecordNotFound,
			},
			mockGetLatestByAccountId: mockGetLatestByAccountId{
				enable:    true,
				accountId: "account-id-1",
				filterOptions: []storagev2.FilterOption{
					dao.WithVendorFilter(commonvgpb.Vendor_FEDERAL_BANK),
				},
				want: nil,
				err:  epifierrors.ErrRecordNotFound,
			},
			mockCreate: mockCreate{
				enable: true,
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DELINK,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
				},
			},
			wantErr: false,
		},
		{
			name: "failed to create upi onboarding detail entity because validation failed as account is already delinked",
			args: args{
				req: &upiActivityPb.CreateUpiOnboardingEntityForDelinkingRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "client-req-id-1",
						Payload:     delinkPayload,
					},
				},
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				err:         epifierrors.ErrRecordNotFound,
			},
			mockGetLatestByAccountId: mockGetLatestByAccountId{
				enable:    true,
				accountId: "account-id-1",
				filterOptions: []storagev2.FilterOption{
					dao.WithVendorFilter(commonvgpb.Vendor_FEDERAL_BANK),
				},
				want: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DELINK,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_SUCCESSFUL,
				},
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "failed to create upi onboarding detail entity because validation failed as link is in progress",
			args: args{
				req: &upiActivityPb.CreateUpiOnboardingEntityForDelinkingRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "client-req-id-1",
						Payload:     delinkPayload,
					},
				},
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				err:         epifierrors.ErrRecordNotFound,
			},
			mockGetLatestByAccountId: mockGetLatestByAccountId{
				enable:    true,
				accountId: "account-id-1",
				filterOptions: []storagev2.FilterOption{
					dao.WithVendorFilter(commonvgpb.Vendor_FEDERAL_BANK),
				},
				want: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_LINK,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
				},
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "successfully created upi onboarding detail entity as it was linked previously",
			args: args{
				req: &upiActivityPb.CreateUpiOnboardingEntityForDelinkingRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "client-req-id-1",
						Payload:     delinkPayload,
					},
				},
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				err:         epifierrors.ErrRecordNotFound,
			},
			mockGetLatestByAccountId: mockGetLatestByAccountId{
				enable:    true,
				accountId: "account-id-1",
				filterOptions: []storagev2.FilterOption{
					dao.WithVendorFilter(commonvgpb.Vendor_FEDERAL_BANK),
				},
				want: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_LINK,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_SUCCESSFUL,
				},
			},
			mockCreate: mockCreate{
				enable: true,
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DELINK,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
				},
			},
			wantErr: false,
		},
		{
			name: "successfully created upi onboarding detail entity as previous delinking failed",
			args: args{
				req: &upiActivityPb.CreateUpiOnboardingEntityForDelinkingRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "client-req-id-1",
						Payload:     delinkPayload,
					},
				},
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				err:         epifierrors.ErrRecordNotFound,
			},
			mockGetLatestByAccountId: mockGetLatestByAccountId{
				enable:    true,
				accountId: "account-id-1",
				filterOptions: []storagev2.FilterOption{
					dao.WithVendorFilter(commonvgpb.Vendor_FEDERAL_BANK),
				},
				want: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DELINK,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_FAILED,
				},
			},
			mockCreate: mockCreate{
				enable: true,
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DELINK,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
				},
			},
			wantErr: false,
		},
		{
			name: "failed to create upi onboarding detail entity because creation failed due to some DB issue",
			args: args{
				req: &upiActivityPb.CreateUpiOnboardingEntityForDelinkingRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "client-req-id-1",
						Payload:     delinkPayload,
					},
				},
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				err:         epifierrors.ErrRecordNotFound,
			},
			mockGetLatestByAccountId: mockGetLatestByAccountId{
				enable:    true,
				accountId: "account-id-1",
				filterOptions: []storagev2.FilterOption{
					dao.WithVendorFilter(commonvgpb.Vendor_FEDERAL_BANK),
				},
				want: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",

					Action: upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DELINK,
					Status: upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_FAILED,
				},
			},
			mockCreate: mockCreate{
				enable: true,
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DELINK,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
				},
				err: errors.New("db issue"),
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "upi onboarding detail entity already exists for given client req id",
			args: args{
				req: &upiActivityPb.CreateUpiOnboardingEntityForDelinkingRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "client-req-id-1",
						Payload:     delinkPayload,
					},
				},
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_LINK,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
				},
			},
			wantErr: false,
		},
		{
			name: "upi onboarding detail entity already exists in unexpected state",
			args: args{
				req: &upiActivityPb.CreateUpiOnboardingEntityForDelinkingRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "client-req-id-1",
						Payload:     delinkPayload,
					},
				},
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_LINK,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_SUCCESSFUL,
				},
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "successfully created upi onboarding detail entity for delinking when last action related to upi number is completed",
			args: args{
				req: &upiActivityPb.CreateUpiOnboardingEntityForDelinkingRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "client-req-id-1",
						Payload:     delinkPayload,
					},
				},
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				err:         epifierrors.ErrRecordNotFound,
			},
			mockGetLatestByAccountId: mockGetLatestByAccountId{
				enable:    true,
				accountId: "account-id-1",
				filterOptions: []storagev2.FilterOption{
					dao.WithVendorFilter(commonvgpb.Vendor_FEDERAL_BANK),
				},
				want: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_UPI_NUMBER_LINK,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_SUCCESSFUL,
				},
			},
			mockCreate: mockCreate{
				enable: true,
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DELINK,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
				},
			},
			wantErr: false,
		},
		{
			name: "failed to create upi onboarding detail entity because some upi number action ws in progress",
			args: args{
				req: &upiActivityPb.CreateUpiOnboardingEntityForDelinkingRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "client-req-id-1",
						Payload:     delinkPayload,
					},
				},
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				err:         epifierrors.ErrRecordNotFound,
			},
			mockGetLatestByAccountId: mockGetLatestByAccountId{
				enable:    true,
				accountId: "account-id-1",
				filterOptions: []storagev2.FilterOption{
					dao.WithVendorFilter(commonvgpb.Vendor_FEDERAL_BANK),
				},
				want: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_UPI_NUMBER_LINK,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
				},
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetByClientRequestId.enable {
				md.upiOnboardingDetailDao.EXPECT().GetByClientRequestId(gomock.Any(),
					tt.mockGetByClientRequestId.clientReqId).
					Return(tt.mockGetByClientRequestId.upiOnbDetail, tt.mockGetByClientRequestId.err)
			}

			if tt.mockGetLatestByAccountId.enable {
				md.upiOnboardingDetailDao.EXPECT().GetLatestByAccountId(gomock.Any(),
					tt.mockGetLatestByAccountId.accountId, gomock.Any()).
					Return(tt.mockGetLatestByAccountId.want, tt.mockGetLatestByAccountId.err)
			}

			if tt.mockCreate.enable {
				md.upiOnboardingDetailDao.EXPECT().Create(gomock.Any(), tt.mockCreate.upiOnbDetail).
					Return(tt.mockCreate.err)
			}

			if _, err := env.ExecuteActivity(upiNs.CreateUpiOnboardingEntityForDelinking,
				tt.args.req); (err != nil) != tt.wantErr {
				t.Errorf("CreateUpiOnboardingEntityForDelinking() error = %v, wantErr %v", err, tt.wantErr)
			} else if tt.wantErr && !tt.assertErr(err) {
				t.Errorf("CreateUpiOnboardingEntityForDelinking() error = %v assertion failed", err)
				return
			}
			assertTest()
		})
	}

}

func TestProcessor_CreateUpiOnboardingEntity(t *testing.T) {
	act, md, assertTest := newProcessorWithMocks(t)
	defer assertTest()
	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(act)

	type mockGetByClientRequestId struct {
		enable       bool
		clientReqId  string
		upiOnbDetail *upiOnboardingPb.UpiOnboardingDetail
		err          error
	}

	type mockGetLatestByAccountId struct {
		enable        bool
		accountId     string
		filterOptions []storagev2.FilterOption
		want          *upiOnboardingPb.UpiOnboardingDetail
		err           error
	}

	type mockCreate struct {
		enable       bool
		upiOnbDetail *upiOnboardingPb.UpiOnboardingDetail
		err          error
	}

	tests := []struct {
		name                     string
		req                      *upiActivityPb.CreateUpiOnboardingEntityRequest
		mockGetByClientRequestId mockGetByClientRequestId
		mockGetLatestByAccountId mockGetLatestByAccountId
		mockCreate               mockCreate
		wantErr                  bool
		assertErr                func(err error) bool
	}{
		{
			name: "successfully created upi onboarding detail entity for linking upi number",
			req: &upiActivityPb.CreateUpiOnboardingEntityRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id-1",
				},
				AccountId: "account-id-1",
				ActorId:   "actor-id-1",
				Vendor:    commonvgpb.Vendor_FEDERAL_BANK,
				Action:    upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_UPI_NUMBER_LINK,
				Vpa:       "upi@vpa",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				err:         epifierrors.ErrRecordNotFound,
			},
			mockGetLatestByAccountId: mockGetLatestByAccountId{
				enable:    true,
				accountId: "account-id-1",
				filterOptions: []storagev2.FilterOption{
					dao.WithVendorFilter(commonvgpb.Vendor_FEDERAL_BANK),
				},
				want: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					Vpa:         "upi@vpa",
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_LINK,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_SUCCESSFUL,
				},
			},
			mockCreate: mockCreate{
				enable: true,
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					Vpa:         "upi@vpa",
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_UPI_NUMBER_LINK,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
				},
			},
			wantErr: false,
		},
		{
			name: "failed to create upi onboarding detail entity for linking upi number as some action was already in progress",
			req: &upiActivityPb.CreateUpiOnboardingEntityRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id-1",
				},
				AccountId: "account-id-1",
				ActorId:   "actor-id-1",
				Vendor:    commonvgpb.Vendor_FEDERAL_BANK,
				Action:    upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_UPI_NUMBER_LINK,
				Vpa:       "upi@vpa",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				err:         epifierrors.ErrRecordNotFound,
			},
			mockGetLatestByAccountId: mockGetLatestByAccountId{
				enable:    true,
				accountId: "account-id-1",
				filterOptions: []storagev2.FilterOption{
					dao.WithVendorFilter(commonvgpb.Vendor_FEDERAL_BANK),
				},
				want: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_UPI_NUMBER_LINK,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_IN_PROGRESS,
				},
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "failed to create upi onboarding detail entity for linking upi number as account was delinked",
			req: &upiActivityPb.CreateUpiOnboardingEntityRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id-1",
				},
				AccountId: "account-id-1",
				ActorId:   "actor-id-1",
				Vendor:    commonvgpb.Vendor_FEDERAL_BANK,
				Action:    upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_UPI_NUMBER_LINK,
				Vpa:       "upi@vpa",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				err:         epifierrors.ErrRecordNotFound,
			},
			mockGetLatestByAccountId: mockGetLatestByAccountId{
				enable:    true,
				accountId: "account-id-1",
				filterOptions: []storagev2.FilterOption{
					dao.WithVendorFilter(commonvgpb.Vendor_FEDERAL_BANK),
				},
				want: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DELINK,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_SUCCESSFUL,
				},
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "failed to create upi onboarding detail entity for linking upi number as no onboarding detail was found for upi account",
			req: &upiActivityPb.CreateUpiOnboardingEntityRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id-1",
				},
				AccountId: "account-id-1",
				ActorId:   "actor-id-1",
				Vendor:    commonvgpb.Vendor_FEDERAL_BANK,
				Action:    upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_UPI_NUMBER_LINK,
				Vpa:       "upi@vpa",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				err:         epifierrors.ErrRecordNotFound,
			},
			mockGetLatestByAccountId: mockGetLatestByAccountId{
				enable:    true,
				accountId: "account-id-1",
				filterOptions: []storagev2.FilterOption{
					dao.WithVendorFilter(commonvgpb.Vendor_FEDERAL_BANK),
				},
				err: epifierrors.ErrRecordNotFound,
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "failed to create upi onboarding detail entity for linking upi number as GetLatestByAccountId dao call failed",
			req: &upiActivityPb.CreateUpiOnboardingEntityRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id-1",
				},
				AccountId: "account-id-1",
				ActorId:   "actor-id-1",
				Vendor:    commonvgpb.Vendor_FEDERAL_BANK,
				Action:    upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_UPI_NUMBER_LINK,
				Vpa:       "upi@vpa",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				err:         epifierrors.ErrRecordNotFound,
			},
			mockGetLatestByAccountId: mockGetLatestByAccountId{
				enable:    true,
				accountId: "account-id-1",
				filterOptions: []storagev2.FilterOption{
					dao.WithVendorFilter(commonvgpb.Vendor_FEDERAL_BANK),
				},
				err: errors.New("db fluctuations"),
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "successfully created upi onboarding detail entity for delinking upi number",
			req: &upiActivityPb.CreateUpiOnboardingEntityRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id-1",
				},
				AccountId: "account-id-1",
				ActorId:   "actor-id-1",
				Vendor:    commonvgpb.Vendor_FEDERAL_BANK,
				Action:    upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_UPI_NUMBER_DELINK,
				Vpa:       "upi@vpa",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				err:         epifierrors.ErrRecordNotFound,
			},
			mockGetLatestByAccountId: mockGetLatestByAccountId{
				enable:    true,
				accountId: "account-id-1",
				filterOptions: []storagev2.FilterOption{
					dao.WithVendorFilter(commonvgpb.Vendor_FEDERAL_BANK),
				},
				want: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					Vpa:         "upi@vpa",
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_LINK,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_SUCCESSFUL,
				},
			},
			mockCreate: mockCreate{
				enable: true,
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					Vpa:         "upi@vpa",
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_UPI_NUMBER_DELINK,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
				},
				err: errors.New("db fluctuations"),
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "failed to create upi onboarding detail entity for delinking upi number as no onboarding detail was found for upi account",
			req: &upiActivityPb.CreateUpiOnboardingEntityRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id-1",
				},
				AccountId: "account-id-1",
				ActorId:   "actor-id-1",
				Vendor:    commonvgpb.Vendor_FEDERAL_BANK,
				Action:    upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_UPI_NUMBER_DELINK,
				Vpa:       "upi@vpa",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				err:         epifierrors.ErrRecordNotFound,
			},
			mockGetLatestByAccountId: mockGetLatestByAccountId{
				enable:    true,
				accountId: "account-id-1",
				filterOptions: []storagev2.FilterOption{
					dao.WithVendorFilter(commonvgpb.Vendor_FEDERAL_BANK),
				},
				err: epifierrors.ErrRecordNotFound,
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "upi onboarding detail entity already exists for given client req id",
			req: &upiActivityPb.CreateUpiOnboardingEntityRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id-1",
				},
				AccountId: "account-id-1",
				ActorId:   "actor-id-1",
				Vendor:    commonvgpb.Vendor_FEDERAL_BANK,
				Action:    upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_UPI_NUMBER_LINK,
				Vpa:       "upi@vpa",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_LINK,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
				},
			},
			wantErr: false,
		},
		{
			name: "upi onboarding detail entity already exists in unexpected state",
			req: &upiActivityPb.CreateUpiOnboardingEntityRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id-1",
				},
				AccountId: "account-id-1",
				ActorId:   "actor-id-1",
				Vendor:    commonvgpb.Vendor_FEDERAL_BANK,
				Action:    upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_UPI_NUMBER_LINK,
				Vpa:       "upi@vpa",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_LINK,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_SUCCESSFUL,
				},
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "successfully created upi onboarding detail entity for activating international payments",
			req: &upiActivityPb.CreateUpiOnboardingEntityRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id-1",
				},
				AccountId: "account-id-1",
				ActorId:   "actor-id-1",
				Vendor:    commonvgpb.Vendor_FEDERAL_BANK,
				Action:    upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_INTERNATIONAL_PAYMENTS,
				Vpa:       "upi@vpa",
				Payload:   createPayloadForInternationalPaymentsActivation(),
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				err:         epifierrors.ErrRecordNotFound,
			},
			mockGetLatestByAccountId: mockGetLatestByAccountId{
				enable:    true,
				accountId: "account-id-1",
				filterOptions: []storagev2.FilterOption{
					dao.WithVendorFilter(commonvgpb.Vendor_FEDERAL_BANK),
				},
				want: nil,
				err:  epifierrors.ErrRecordNotFound,
			},
			mockCreate: mockCreate{
				enable: true,
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Vpa:         "upi@vpa",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_INTERNATIONAL_PAYMENTS,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
					Payload:     createPayloadForInternationalPaymentsActivation(),
				},
			},
			wantErr: false,
		},
		{
			name: "failed to create upi onboarding detail entity because validation failed as deactivation of international payments is in progress",
			req: &upiActivityPb.CreateUpiOnboardingEntityRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id-1",
				},
				AccountId: "account-id-1",
				ActorId:   "actor-id-1",
				Vendor:    commonvgpb.Vendor_FEDERAL_BANK,
				Action:    upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_INTERNATIONAL_PAYMENTS,
				Vpa:       "upi@vpa",
				Payload:   createPayloadForInternationalPaymentsActivation(),
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				err:         epifierrors.ErrRecordNotFound,
			},
			mockGetLatestByAccountId: mockGetLatestByAccountId{
				enable:    true,
				accountId: "account-id-1",
				filterOptions: []storagev2.FilterOption{
					dao.WithVendorFilter(commonvgpb.Vendor_FEDERAL_BANK),
				},
				want: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DEACTIVATE_INTERNATIONAL_PAYMENTS,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
				},
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "successfully created upi onboarding detail entity as it was international payments was deactivated previously",
			req: &upiActivityPb.CreateUpiOnboardingEntityRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id-1",
				},
				AccountId: "account-id-1",
				ActorId:   "actor-id-1",
				Vendor:    commonvgpb.Vendor_FEDERAL_BANK,
				Action:    upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_INTERNATIONAL_PAYMENTS,
				Vpa:       "upi@vpa",
				Payload:   createPayloadForInternationalPaymentsActivation(),
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				err:         epifierrors.ErrRecordNotFound,
			},
			mockGetLatestByAccountId: mockGetLatestByAccountId{
				enable:    true,
				accountId: "account-id-1",
				filterOptions: []storagev2.FilterOption{
					dao.WithVendorFilter(commonvgpb.Vendor_FEDERAL_BANK),
				},
				want: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DEACTIVATE_INTERNATIONAL_PAYMENTS,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_SUCCESSFUL,
				},
			},
			mockCreate: mockCreate{
				enable: true,
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					Vpa:         "upi@vpa",
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_INTERNATIONAL_PAYMENTS,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
					Payload:     createPayloadForInternationalPaymentsActivation(),
				},
			},
			wantErr: false,
		},
		{
			name: "successfully created upi onboarding detail entity as previous activation failed",
			req: &upiActivityPb.CreateUpiOnboardingEntityRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id-1",
				},
				AccountId: "account-id-1",
				ActorId:   "actor-id-1",
				Vendor:    commonvgpb.Vendor_FEDERAL_BANK,
				Action:    upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_INTERNATIONAL_PAYMENTS,
				Vpa:       "upi@vpa",
				Payload:   createPayloadForInternationalPaymentsActivation(),
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				err:         epifierrors.ErrRecordNotFound,
			},
			mockGetLatestByAccountId: mockGetLatestByAccountId{
				enable:    true,
				accountId: "account-id-1",
				filterOptions: []storagev2.FilterOption{
					dao.WithVendorFilter(commonvgpb.Vendor_FEDERAL_BANK),
				},
				want: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_INTERNATIONAL_PAYMENTS,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_FAILED,
				},
			},
			mockCreate: mockCreate{
				enable: true,
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Vpa:         "upi@vpa",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_INTERNATIONAL_PAYMENTS,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
					Payload:     createPayloadForInternationalPaymentsActivation(),
				},
			},
			wantErr: false,
		},
		{
			name: "failed to creat upi onboarding detail entity because creation failed due to some DB issue",
			req: &upiActivityPb.CreateUpiOnboardingEntityRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id-1",
				},
				AccountId: "account-id-1",
				ActorId:   "actor-id-1",
				Vendor:    commonvgpb.Vendor_FEDERAL_BANK,
				Action:    upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_INTERNATIONAL_PAYMENTS,
				Vpa:       "upi@vpa",
				Payload:   createPayloadForInternationalPaymentsActivation(),
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				err:         epifierrors.ErrRecordNotFound,
			},
			mockGetLatestByAccountId: mockGetLatestByAccountId{
				enable:    true,
				accountId: "account-id-1",
				filterOptions: []storagev2.FilterOption{
					dao.WithVendorFilter(commonvgpb.Vendor_FEDERAL_BANK),
				},
				want: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",

					Action: upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_INTERNATIONAL_PAYMENTS,
					Status: upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_FAILED,
				},
			},
			mockCreate: mockCreate{
				enable: true,
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Vpa:         "upi@vpa",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_INTERNATIONAL_PAYMENTS,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
					Payload:     createPayloadForInternationalPaymentsActivation(),
				},
				err: errors.New("db issue"),
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "failed to creat upi onboarding detail entity because creation failed due to invalid args",
			req: &upiActivityPb.CreateUpiOnboardingEntityRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id-1",
				},
				AccountId: "account-id-1",
				ActorId:   "actor-id-1",
				Vendor:    commonvgpb.Vendor_FEDERAL_BANK,
				Action:    upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_INTERNATIONAL_PAYMENTS,
				Vpa:       "upi@vpa",
				Payload:   createPayloadForInternationalPaymentsActivation(),
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				err:         epifierrors.ErrRecordNotFound,
			},
			mockGetLatestByAccountId: mockGetLatestByAccountId{
				enable:    true,
				accountId: "account-id-1",
				filterOptions: []storagev2.FilterOption{
					dao.WithVendorFilter(commonvgpb.Vendor_FEDERAL_BANK),
				},
				want: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_INTERNATIONAL_PAYMENTS,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_FAILED,
				},
			},
			mockCreate: mockCreate{
				enable: true,
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Vpa:         "upi@vpa",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_INTERNATIONAL_PAYMENTS,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
					Payload:     createPayloadForInternationalPaymentsActivation(),
				},
				err: epifierrors.ErrInvalidArgument,
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "upi onboarding detail entity already exists for given client req id",
			req: &upiActivityPb.CreateUpiOnboardingEntityRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id-1",
				},
				AccountId: "account-id-1",
				ActorId:   "actor-id-1",
				Vendor:    commonvgpb.Vendor_FEDERAL_BANK,
				Action:    upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_INTERNATIONAL_PAYMENTS,
				Vpa:       "upi@vpa",
				Payload:   createPayloadForInternationalPaymentsActivation(),
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_INTERNATIONAL_PAYMENTS,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
				},
			},
			wantErr: false,
		},
		{
			name: "upi onboarding detail entity already exists in unexpected state",
			req: &upiActivityPb.CreateUpiOnboardingEntityRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id-1",
				},
				AccountId: "account-id-1",
				ActorId:   "actor-id-1",
				Vendor:    commonvgpb.Vendor_FEDERAL_BANK,
				Action:    upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_INTERNATIONAL_PAYMENTS,
				Vpa:       "upi@vpa",
				Payload:   createPayloadForInternationalPaymentsActivation(),
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_INTERNATIONAL_PAYMENTS,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_SUCCESSFUL,
				},
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "successfully created upi onboarding detail entity for activating upi lite",
			req: &upiActivityPb.CreateUpiOnboardingEntityRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id-1",
				},
				AccountId: "account-id-1",
				ActorId:   "actor-id-1",
				Vendor:    commonvgpb.Vendor_FEDERAL_BANK,
				Action:    upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE,
				Vpa:       "upi@vpa",
				Payload:   &upiOnboardingPb.UpiOnboardingDetailsPayload{ActivateUpiLitePayload: &upiOnboardingPb.ActivateUpiLitePayload{}},
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				err:         epifierrors.ErrRecordNotFound,
			},
			mockGetLatestByAccountId: mockGetLatestByAccountId{
				enable:    true,
				accountId: "account-id-1",
				filterOptions: []storagev2.FilterOption{
					dao.WithVendorFilter(commonvgpb.Vendor_FEDERAL_BANK),
					dao.WithUpiAccountActionFilter([]upiOnboardingEnumsPb.UpiOnboardingAction{upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE,
						upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DEACTIVATE_UPI_LITE}),
				},
				want: nil,
				err:  epifierrors.ErrRecordNotFound,
			},
			mockCreate: mockCreate{
				enable: true,
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Vpa:         "upi@vpa",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
					Payload:     createPayloadForInternationalPaymentsActivation(),
				},
			},
			wantErr: false,
		},
		{
			name: "failed to create upi onboarding detail entity because validation failed as deactivation of upi lite is in progress",
			req: &upiActivityPb.CreateUpiOnboardingEntityRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id-1",
				},
				AccountId: "account-id-1",
				ActorId:   "actor-id-1",
				Vendor:    commonvgpb.Vendor_FEDERAL_BANK,
				Action:    upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE,
				Vpa:       "upi@vpa",
				Payload:   &upiOnboardingPb.UpiOnboardingDetailsPayload{ActivateUpiLitePayload: &upiOnboardingPb.ActivateUpiLitePayload{}},
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				err:         epifierrors.ErrRecordNotFound,
			},
			mockGetLatestByAccountId: mockGetLatestByAccountId{
				enable:    true,
				accountId: "account-id-1",
				filterOptions: []storagev2.FilterOption{
					dao.WithVendorFilter(commonvgpb.Vendor_FEDERAL_BANK),
					dao.WithUpiAccountActionFilter([]upiOnboardingEnumsPb.UpiOnboardingAction{upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE,
						upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DEACTIVATE_UPI_LITE}),
				},
				want: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DEACTIVATE_UPI_LITE,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_IN_PROGRESS,
				},
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "successfully created upi onboarding detail entity for deactivate upi lite as upi lite was activated successfully previously",
			req: &upiActivityPb.CreateUpiOnboardingEntityRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id-1",
				},
				AccountId: "account-id-1",
				ActorId:   "actor-id-1",
				Vendor:    commonvgpb.Vendor_FEDERAL_BANK,
				Action:    upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DEACTIVATE_UPI_LITE,
				Vpa:       "upi@vpa",
				Payload:   &upiOnboardingPb.UpiOnboardingDetailsPayload{ActivateUpiLitePayload: &upiOnboardingPb.ActivateUpiLitePayload{}},
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				err:         epifierrors.ErrRecordNotFound,
			},
			mockGetLatestByAccountId: mockGetLatestByAccountId{
				enable:    true,
				accountId: "account-id-1",
				filterOptions: []storagev2.FilterOption{
					dao.WithVendorFilter(commonvgpb.Vendor_FEDERAL_BANK),
					dao.WithUpiAccountActionFilter([]upiOnboardingEnumsPb.UpiOnboardingAction{upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE,
						upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DEACTIVATE_UPI_LITE}),
				},
				want: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_SUCCESSFUL,
				},
			},
			mockCreate: mockCreate{
				enable: true,
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					Vpa:         "upi@vpa",
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DEACTIVATE_UPI_LITE,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
					Payload:     createPayloadForInternationalPaymentsActivation(),
				},
			},
			wantErr: false,
		},
		{
			name: "successfully created upi onboarding detail entity as previous lite activation failed",
			req: &upiActivityPb.CreateUpiOnboardingEntityRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id-1",
				},
				AccountId: "account-id-1",
				ActorId:   "actor-id-1",
				Vendor:    commonvgpb.Vendor_FEDERAL_BANK,
				Action:    upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE,
				Vpa:       "upi@vpa",
				Payload:   &upiOnboardingPb.UpiOnboardingDetailsPayload{ActivateUpiLitePayload: &upiOnboardingPb.ActivateUpiLitePayload{}},
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				err:         epifierrors.ErrRecordNotFound,
			},
			mockGetLatestByAccountId: mockGetLatestByAccountId{
				enable:    true,
				accountId: "account-id-1",
				filterOptions: []storagev2.FilterOption{
					dao.WithVendorFilter(commonvgpb.Vendor_FEDERAL_BANK),
					dao.WithUpiAccountActionFilter([]upiOnboardingEnumsPb.UpiOnboardingAction{upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE,
						upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DEACTIVATE_UPI_LITE}),
				},
				want: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_FAILED,
				},
			},
			mockCreate: mockCreate{
				enable: true,
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Vpa:         "upi@vpa",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
					Payload:     createPayloadForInternationalPaymentsActivation(),
				},
			},
			wantErr: false,
		},
		{
			name: "failed to create upi onboarding detail entity for upi lite activation because validation failed as previous deactivation failed",
			req: &upiActivityPb.CreateUpiOnboardingEntityRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id-1",
				},
				AccountId: "account-id-1",
				ActorId:   "actor-id-1",
				Vendor:    commonvgpb.Vendor_FEDERAL_BANK,
				Action:    upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE,
				Vpa:       "upi@vpa",
				Payload:   &upiOnboardingPb.UpiOnboardingDetailsPayload{ActivateUpiLitePayload: &upiOnboardingPb.ActivateUpiLitePayload{}},
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id-1",
				err:         epifierrors.ErrRecordNotFound,
			},
			mockGetLatestByAccountId: mockGetLatestByAccountId{
				enable:    true,
				accountId: "account-id-1",
				filterOptions: []storagev2.FilterOption{
					dao.WithVendorFilter(commonvgpb.Vendor_FEDERAL_BANK),
					dao.WithUpiAccountActionFilter([]upiOnboardingEnumsPb.UpiOnboardingAction{upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE,
						upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DEACTIVATE_UPI_LITE}),
				},
				want: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId:   "account-id-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ClientReqId: "client-req-id-1",
					Action:      upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DEACTIVATE_UPI_LITE,
					Status:      upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_FAILED,
				},
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetByClientRequestId.enable {
				md.upiOnboardingDetailDao.EXPECT().GetByClientRequestId(gomock.Any(),
					tt.mockGetByClientRequestId.clientReqId).
					Return(tt.mockGetByClientRequestId.upiOnbDetail, tt.mockGetByClientRequestId.err)
			}

			if tt.mockGetLatestByAccountId.enable {
				md.upiOnboardingDetailDao.EXPECT().GetLatestByAccountId(gomock.Any(),
					tt.mockGetLatestByAccountId.accountId, gomock.Any()).
					Return(tt.mockGetLatestByAccountId.want, tt.mockGetLatestByAccountId.err)
			}

			if tt.mockCreate.enable {
				md.upiOnboardingDetailDao.EXPECT().Create(gomock.Any(), newValidateCreateOnbDetails(tt.mockCreate.upiOnbDetail)).
					Return(tt.mockCreate.err)
			}

			if _, err := env.ExecuteActivity(upiNs.CreateUpiOnboardingEntity,
				tt.req); (err != nil) != tt.wantErr {
				t.Errorf("CreateUpiOnboardingEntity() error = %v, wantErr %v", err, tt.wantErr)
			} else if tt.wantErr && !tt.assertErr(err) {
				t.Errorf("CreateUpiOnboardingEntity() error = %v assertion failed", err)
				return
			}
			assertTest()
		})
	}
}

func createPayloadForInternationalPaymentsActivation() *upiOnboardingPb.UpiOnboardingDetailsPayload {
	activatedAt, _ := datetime.ParseStringTimeStampProto(layout, "2022-01-03")
	expireAt, _ := datetime.ParseStringTimeStampProto(layout, "2022-04-03")
	return &upiOnboardingPb.UpiOnboardingDetailsPayload{
		ActivateInternationalPaymentsPayload: &upiOnboardingPb.ActivateInternationalPaymentsPayload{
			InternationalPaymentsActivatedAt: activatedAt,
			InternationalPaymentsExpiresAt:   expireAt,
		},
	}
}
