package onboarding

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
	upiOnboardingEnumsPb "github.com/epifi/gamma/api/upi/onboarding/enums"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
)

var (
	workflowStatusToUpiOnboardingStatus = map[stagePb.Status]upiOnboardingEnumsPb.UpiOnboardingStatus{
		stagePb.Status_FAILED:              upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_FAILED,
		stagePb.Status_SUCCESSFUL:          upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_SUCCESSFUL,
		stagePb.Status_MANUAL_INTERVENTION: upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_MANUAL_INTERVENTION,
		stagePb.Status_INITIATED:           upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_IN_PROGRESS,
		stagePb.Status_CREATED:             upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
		stagePb.Status_PENDING:             upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_IN_PROGRESS,
		stagePb.Status_BLOCKED:             upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_IN_PROGRESS,
	}
)

// GetUpiAccountsActionStatus fetches the account linking/delinking status for given accounts
// It takes list of account details as request
// it fetches status of triggered workflows using client request id present in account details
// it doesn't returns error in case one/some of the accounts from given account list fails to be linked.delinked
func (s *Service) GetUpiAccountsActionStatus(ctx context.Context, req *upiOnboardingPb.GetUpiAccountsActionStatusRequest) (*upiOnboardingPb.GetUpiAccountsActionStatusResponse, error) {
	var (
		res                      = &upiOnboardingPb.GetUpiAccountsActionStatusResponse{}
		clientReqIdActionInfoMap = map[string]*upiOnboardingPb.UpiAccountActionInfo{}
		accountActionInfo        *upiOnboardingPb.UpiAccountActionInfo
		err                      error
	)

	for _, clientId := range req.GetClientIds() {

		accountActionInfo, res.AccountActionType, err = s.getAccountActionInfo(ctx, clientId)
		if accountActionInfo != nil {
			clientReqIdActionInfoMap[clientId.GetId()] = accountActionInfo
		}
		if err != nil {
			logger.Error(ctx, "failure while fetching account action info for given client request id", zap.String(logger.CLIENT_REQUEST_ID, clientId.GetId()), zap.Error(err))

			res.ClientReqIdActionInfoMap = clientReqIdActionInfoMap
			res.Status = rpcPb.StatusFromError(err)
			return res, nil
		}
	}

	res.ClientReqIdActionInfoMap = clientReqIdActionInfoMap
	res.Status = rpcPb.StatusOk()
	return res, nil
}

// getWorkflowStatus - fetches workflow request for given client request id and returns account action info and error (if any)
func (s *Service) getAccountActionInfo(ctx context.Context, clientId *celestialPb.ClientReqId) (*upiOnboardingPb.UpiAccountActionInfo, upiOnboardingPb.GetUpiAccountsActionStatusResponse_AccountActionType, error) {
	var (
		accountActionStatus upiOnboardingEnumsPb.UpiOnboardingStatus
		accountActionType   upiOnboardingPb.GetUpiAccountsActionStatusResponse_AccountActionType
		upiOnbDetail        *upiOnboardingPb.UpiOnboardingDetail
		accountId           string
		vpa                 string
	)

	resp, err := s.celestialClient.GetWorkflowStatus(ctx, &celestialPb.GetWorkflowStatusRequest{
		Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{
			ClientRequestId: clientId,
		},
	})
	switch {
	case err != nil:
		return nil, accountActionType, fmt.Errorf("error while fetching workflow request for given client request id %v %w", err, rpcPb.StatusAsError(rpcPb.StatusInternal()))
	case resp.GetStatus().IsRecordNotFound():
		return nil, accountActionType, fmt.Errorf("workflow request not found for given client request id %w", rpcPb.StatusAsError(rpcPb.StatusRecordNotFound()))
	case !resp.GetStatus().IsSuccess():
		return nil, accountActionType, fmt.Errorf("non success status while fetching workflow request %s %w", resp.GetStatus().String(), rpcPb.StatusAsError(rpcPb.StatusInternal()))
	}

	if resp.GetWorkflowRequest().GetStatus() == stagePb.Status_SUCCESSFUL && resp.GetWorkflowRequest().GetStage() != workflowPb.Stage_NOTIFICATION && resp.GetWorkflowRequest().GetStage() != workflowPb.Stage_DELINK {
		return &upiOnboardingPb.UpiAccountActionInfo{
			Status: upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_IN_PROGRESS,
		}, accountActionType, nil
	}

	accountActionStatus = workflowStatusToUpiOnboardingStatus[resp.GetWorkflowRequest().GetStatus()]

	if resp.GetWorkflowRequest().GetType() == workflowPb.Type_LINK_UPI_ACCOUNT && accountActionStatus == upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_SUCCESSFUL {
		upiOnbDetail, err = s.fetchUpiOnbaordingDetail(ctx, clientId.GetId())
		if err != nil {
			return nil, accountActionType, err
		}
		accountId = upiOnbDetail.GetAccountId()
		vpa = upiOnbDetail.GetVpa()
	}

	if s.isVpaMigrationFlow(ctx, upiOnbDetail) {
		accountActionType = upiOnboardingPb.GetUpiAccountsActionStatusResponse_ACCOUNT_ACTION_TYPE_VPA_MIGRATION
	}

	return &upiOnboardingPb.UpiAccountActionInfo{
		AccountId: accountId,
		Status:    accountActionStatus,
		Vpa:       vpa,
	}, accountActionType, nil
}

func (s *Service) isVpaMigrationFlow(ctx context.Context, upiOnboardingDetail *upiOnboardingPb.UpiOnboardingDetail) bool {
	if upiOnboardingDetail == nil || upiOnboardingDetail.GetAction() != upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_LINK {
		return false
	}

	upiAccount, err := s.upiAccountDao.GetById(ctx, upiOnboardingDetail.GetAccountId())
	if err != nil {
		logger.Error(ctx, "error while checking if account id belongs to internal Tpap account",
			zap.String(logger.ACCOUNT_ID, upiOnboardingDetail.GetAccountId()), zap.Error(err))

		return false
	}

	return upiAccount.IsInternal()
}

// fetchUpiOnbaordingDetail - fetches the upi onboarding detail for the given client-req-id
func (s *Service) fetchUpiOnbaordingDetail(ctx context.Context, clientReqId string) (*upiOnboardingPb.UpiOnboardingDetail, error) {
	upiOnbDetail, err := s.upiOnbDao.GetByClientRequestId(ctx, clientReqId)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		return nil, fmt.Errorf("record not found failure while fetching upi onb detail for given client request id %w", rpcPb.StatusAsError(rpcPb.StatusRecordNotFound()))
	case errors.Is(err, epifierrors.ErrInvalidArgument):
		return nil, fmt.Errorf("invalid argument passed while fetching upi onb detail for given client request id %w", rpcPb.StatusAsError(rpcPb.StatusInvalidArgument()))
	case err != nil:
		return nil, fmt.Errorf("failure while fetching upi onb detail for given client request id %w", rpcPb.StatusAsError(rpcPb.StatusInternal()))
	}

	return upiOnbDetail, nil
}
