package upi_test

import (
	"context"
	"testing"

	gormv2 "gorm.io/gorm"

	queueMocks "github.com/epifi/be-common/pkg/queue/mocks"
	pkgTest2 "github.com/epifi/be-common/pkg/test/v2"

	"github.com/golang/mock/gomock"

	eventMock "github.com/epifi/be-common/pkg/events/mocks"

	"github.com/epifi/gamma/upi"

	"github.com/epifi/gamma/upi/config"
	"github.com/epifi/gamma/upi/dao"
)

type urnTestSuite struct {
	pspKeyDao dao.PspKeyDao
	entryDao  dao.VerifiedAddressEntryDao
	config    *config.Config
	db        *gormv2.DB
	dbName    string
}

func NewUrnTestSuite(keyDao dao.PspKeyDao, entryDao dao.VerifiedAddressEntryDao, config *config.Config, db *gormv2.DB, dbName string) *urnTestSuite {
	return &urnTestSuite{
		pspKeyDao: keyDao,
		entryDao:  entryDao,
		config:    config,
		db:        db,
		dbName:    dbName,
	}
}

var (
	uts                   *urnTestSuite
	urnWithOutSign        = "Helloworld"
	sign                  = "MGUCMQC+yCzmUYhEPZ/2N0t2Lj1WEunSsg4pEPRK4HjQadpChuV3Vk7dLD29w+VcpNBeZdICMEC7CdU+5aQee/Fg/JPZ6zdnbHp+d4uGoOrKcI+anfbgwxr9/ZFTlz23KTXtDMPZIQ=="
	upiAffectedTestTables = []string{"psp_keys", "verified_address_entries"}
)

func TestService_VerifySignature(t *testing.T) {
	ctr := gomock.NewController(t)
	brokerMock := eventMock.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	mockPinAttemptsExceededEventSnsPublisher := queueMocks.NewMockPublisher(ctr)
	s := upi.NewService(
		nil,
		nil,
		nil,
		nil,
		nil,
		nil,
		nil,
		uts.config,
		uts.pspKeyDao,
		uts.entryDao,
		nil,
		nil,
		nil,
		nil,
		nil,
		nil,
		brokerMock,
		nil,
		nil,
		nil,
		nil,
		nil,
		nil,
		nil,
		nil,
		nil,
		nil,
		nil,
		nil,
		nil,
		nil,
		nil,
		mockPinAttemptsExceededEventSnsPublisher,
		nil,
		nil,
		nil,
		nil,
	)

	type args struct {
		urnWithOutSign string
		urnParamsMap   map[string][]string
	}
	tests := []struct {
		name    string
		args    args
		want    bool
		wantErr bool
	}{
		{
			name: "Verified using PSP org id for a non merchant",
			args: args{
				urnParamsMap: map[string][]string{
					"pa":    {"test@okaxis"},
					"orgid": {"400021"},
					"sign":  {sign},
				},
				urnWithOutSign: urnWithOutSign,
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "P2P signed QR share by fed",
			args: args{
				urnParamsMap: map[string][]string{
					"pa":    {"919446688321@fedepsp"},
					"orgid": {"159049"},
					"sign":  {"MEUCIFy8k1HAVg7sUhlVPFb0lnch83L5BaakxMhj7FnEEWIBAiEAisbVgTNRQF7wLc8EE7hdI2esKHxOr2cwDdJvTiMn9Qs="},
				},
				urnWithOutSign: "upi://pay?pa=919446688321@fedepsp&pn=GZHZM  O  QKGWLR&tid=FBLadfb66c8519a483c934c01a453db389d&mc=0000&tr=&tn=test&am=5&purpose=00&orgid=159049&cu=INR&appid=&refUrl=&mode=02&appname=Lotza",
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "signed qr shared by fed with change in payload",
			args: args{
				urnParamsMap: map[string][]string{
					"pa":    {"919446688321@fedepsp"},
					"orgid": {"159049"},
					"sign":  {"MEUCIFy8k1HAVg7sUhlVPFb0lnch83L5BaakxMhj7FnEEWIBAiEAisbVgTNRQF7wLc8EE7hdI2esKHxOr2cwDdJvTiMn9Qs="},
				},
				// one space is removed to change the payload
				urnWithOutSign: "upi://pay?pa=919446688321@fedepsp&pn=GZHZM  O QKGWLR&tid=FBLadfb66c8519a483c934c01a453db389d&mc=0000&tr=&tn=test&am=5&purpose=00&orgid=159049&cu=INR&appid=&refUrl=&mode=02&appname=Lotza",
			},
			want:    false,
			wantErr: true,
		},
		{
			name: "psp org id is not available in db",
			args: args{
				urnParamsMap: map[string][]string{
					"pa":    {"pmcares@sbi"},
					"orgid": {"159002"},
					"sign":  {"MEUCIArAf9pTa2tO9TUF2Y5//RZMSmy4IfdyNuBLcg3yq9LzAiEA5yImT7ObOtkwRiS+lzH2689QINxZi/kii4haMM8EZgk="},
				},
				urnWithOutSign: "upi://pay?pa=pmcares@sbi&pn=PM%20CARES&mc=9405&tr=&tn=&am=&cu=INR&url=&mode=02&purpose=00&orgid=159002",
			},
			want:    false,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			// Clean database, run migrations and load fixtures
			pkgTest2.PrepareRandomScopedCrdbDatabase(t, uts.config.EpifiDb, uts.dbName, uts.db, upiAffectedTestTables, &initialiseSameDbOnce)

			got, err := s.VerifySignature(context.Background(), tt.args.urnWithOutSign, tt.args.urnParamsMap)
			if (err != nil) != tt.wantErr {
				t.Errorf("VerifySignature() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("VerifySignature() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_IsMerchantVerified(t *testing.T) {
	ctr := gomock.NewController(t)
	brokerMock := eventMock.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	mockPinAttemptsExceededEventSnsPublisher := queueMocks.NewMockPublisher(ctr)
	s := upi.NewService(
		nil,
		nil,
		nil,
		nil,
		nil,
		nil,
		nil,
		uts.config,
		uts.pspKeyDao,
		uts.entryDao,
		nil,
		nil,
		nil,
		nil,
		nil,
		nil,
		brokerMock,
		nil,
		nil,
		nil,
		nil,
		nil,
		nil,
		nil,
		nil,
		nil,
		nil,
		nil,
		nil,
		nil,
		nil,
		nil,
		mockPinAttemptsExceededEventSnsPublisher,
		nil,
		nil,
		nil,
		nil,
	)

	type args struct {
		urnParamsMap map[string][]string
	}
	tests := []struct {
		name    string
		args    args
		want    bool
		wantErr bool
	}{
		{
			name: "Merchant VPA is present, but the signature is invalid",
			args: args{
				urnParamsMap: map[string][]string{
					"pa":    {"test-urn@okaxis"},
					"orgid": {""},
					"sign":  {sign},
				},
			},
			want:    false,
			wantErr: true,
		},
		{
			name: "Merchant VPA not present in List Vae",
			args: args{
				urnParamsMap: map[string][]string{
					"pa":    {"test@okaxis"},
					"orgid": {"400021"},
					"sign":  {sign},
				},
			},
			want:    false,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			// Clean database, run migrations and load fixtures
			pkgTest2.PrepareRandomScopedCrdbDatabase(t, uts.config.EpifiDb, uts.dbName, uts.db, upiAffectedTestTables, &initialiseSameDbOnce)

			got, err := s.IsMerchantVerified(context.Background(), "", tt.args.urnParamsMap)
			if (err != nil) != tt.wantErr {
				t.Errorf("IsMerchantVerified() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("IsMerchantVerified() got = %v, want %v", got, tt.want)
			}
		})
	}
}
