package timeline_test

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	actorPb "github.com/epifi/gamma/api/actor"
	actorMocks "github.com/epifi/gamma/api/actor/mocks"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	accountPiMocks "github.com/epifi/gamma/api/paymentinstrument/account_pi/mocks"
	timelinePb "github.com/epifi/gamma/api/timeline"
	timelineMocks "github.com/epifi/gamma/api/timeline/mocks"
	upiPb "github.com/epifi/gamma/api/upi"
	"github.com/epifi/gamma/api/vendors"
	piHelperMocks "github.com/epifi/gamma/upi/helper/paymentinstrument/mocks"
	timelineHelper "github.com/epifi/gamma/upi/helper/timeline"
)

func TestService_ResolveTimeline(t *testing.T) {
	t.Skip("Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	payerCustInfo := &upiPb.Customer{
		PaymentAddress: "payer@okaxis",
		Name:           "payer name",
		Type:           upiPb.CustomerType_PERSON,
	}

	payerPi := &piPb.PaymentInstrument{
		Id:   "pi-1",
		Type: piPb.PaymentInstrumentType_UPI,
		Identifier: &piPb.PaymentInstrument_Upi{
			Upi: &piPb.Upi{
				Vpa: payerCustInfo.PaymentAddress,
			}},
		VerifiedName: payerCustInfo.Name,
	}

	payeeCustInfo := &upiPb.Customer{
		PaymentAddress: "payee@okaxis",
		Name:           "payee name",
		Type:           upiPb.CustomerType_PERSON,
	}

	payeePi := &piPb.PaymentInstrument{
		Id:   "pi-2",
		Type: piPb.PaymentInstrumentType_UPI,
		Identifier: &piPb.PaymentInstrument_Upi{
			Upi: &piPb.Upi{
				Vpa: payeeCustInfo.PaymentAddress,
			}},
		VerifiedName: payeeCustInfo.Name,
	}

	mockActorClient := actorMocks.NewMockActorClient(ctr)
	mockTimelineClient := timelineMocks.NewMockTimelineServiceClient(ctr)
	mockAccountPiClient := accountPiMocks.NewMockAccountPIRelationClient(ctr)
	mockPiHelperSvc := piHelperMocks.NewMockPIHelper(ctr)

	timelineHelperSvc := timelineHelper.NewService(mockActorClient, mockTimelineClient, mockAccountPiClient, mockPiHelperSvc)

	type mockCreatePI struct {
		enable   bool
		customer *upiPb.Customer
		pi       *piPb.PaymentInstrument
		err      error
	}
	type mockGetPiFromVPA struct {
		enable bool
		vpa    string
		pi     *piPb.PaymentInstrument
		err    error
	}
	type mockGetByPiId struct {
		enable bool
		req    *accountPiPb.GetByPiIdRequest
		res    *accountPiPb.GetByPiIdResponse
		err    error
	}
	type mockResolveActorFrom struct {
		enable bool
		req    *actorPb.ResolveActorFromRequest
		res    *actorPb.ResolveActorFromResponse
		err    error
	}
	type mockResolveActorTo struct {
		enable bool
		req    *actorPb.ResolveActorToRequest
		res    *actorPb.ResolveActorToResponse
		err    error
	}
	type mockCreateTimeline struct {
		enable bool
		req    *timelinePb.CreateRequest
		res    *timelinePb.CreateResponse
		err    error
	}
	type args struct {
		ctx       context.Context
		payerInfo *upiPb.Customer
		payeeInfo *upiPb.Customer
		reqType   string
	}
	tests := []struct {
		name                 string
		args                 args
		mockCreatePI         mockCreatePI
		mockGetPiFromVPA     mockGetPiFromVPA
		mockGetByPiId        mockGetByPiId
		mockResolveActorFrom mockResolveActorFrom
		mockResolveActorTo   mockResolveActorTo
		mockCreateTimeline   mockCreateTimeline
		want                 *timelineHelper.ResolveTimelineResult
		wantErr              bool
		wantErrType          error
	}{
		{
			name: "resolve timeline for incoming PAY from external actor",
			args: args{
				ctx:       context.Background(),
				payerInfo: payerCustInfo,
				payeeInfo: payeeCustInfo,
				reqType:   vendors.UPITxnTypePay,
			},
			mockCreatePI: mockCreatePI{
				enable:   true,
				customer: payerCustInfo,
				pi:       payerPi,
				err:      nil,
			},
			mockGetPiFromVPA: mockGetPiFromVPA{
				enable: true,
				vpa:    payeeCustInfo.PaymentAddress,
				pi:     payeePi,
				err:    nil,
			},
			mockGetByPiId: mockGetByPiId{
				enable: true,
				req:    &accountPiPb.GetByPiIdRequest{PiId: payeePi.Id},
				res: &accountPiPb.GetByPiIdResponse{
					Status:  rpc.StatusOk(),
					ActorId: "internal-actor",
				},
				err: nil,
			},
			mockResolveActorFrom: mockResolveActorFrom{
				enable: true,
				req: &actorPb.ResolveActorFromRequest{
					ActorTo:       "internal-actor",
					PiFrom:        payerPi.Id,
					ActorFromName: payerCustInfo.Name,
				},
				res: &actorPb.ResolveActorFromResponse{
					Status:    rpc.StatusOk(),
					ActorFrom: "external-actor",
				},
				err: nil,
			},
			mockCreateTimeline: mockCreateTimeline{
				enable: true,
				req: &timelinePb.CreateRequest{
					PrimaryActorId:     "external-actor",
					SecondaryActorId:   "internal-actor",
					PrimaryActorName:   payerCustInfo.Name,
					SecondaryActorName: payeeCustInfo.Name,
				},
				res: &timelinePb.CreateResponse{
					Status:   rpc.StatusOk(),
					Timeline: nil,
				},
				err: nil,
			},
			want: &timelineHelper.ResolveTimelineResult{
				PayerActorId: "external-actor",
				PayeeActorId: "internal-actor",
				PayerPiId:    payerPi.Id,
				PayeePiId:    payeePi.Id,
			},
		},
		{
			name: "resolve timeline for incoming Collect from external actor",
			args: args{
				ctx:       context.Background(),
				payerInfo: payerCustInfo,
				payeeInfo: payeeCustInfo,
				reqType:   vendors.UPITxnTypeCollect,
			},
			mockCreatePI: mockCreatePI{
				enable:   true,
				customer: payeeCustInfo,
				pi:       payeePi,
				err:      nil,
			},
			mockGetPiFromVPA: mockGetPiFromVPA{
				enable: true,
				vpa:    payerCustInfo.PaymentAddress,
				pi:     payerPi,
				err:    nil,
			},
			mockGetByPiId: mockGetByPiId{
				enable: true,
				req:    &accountPiPb.GetByPiIdRequest{PiId: payerPi.Id},
				res: &accountPiPb.GetByPiIdResponse{
					Status:  rpc.StatusOk(),
					ActorId: "internal-actor",
				},
				err: nil,
			},
			mockResolveActorTo: mockResolveActorTo{
				enable: true,
				req: &actorPb.ResolveActorToRequest{
					ActorFrom:   "internal-actor",
					PiTo:        payeePi.Id,
					ActorToName: payeeCustInfo.Name,
				},
				res: &actorPb.ResolveActorToResponse{
					Status:  rpc.StatusOk(),
					ActorTo: "external-actor",
				},
				err: nil,
			},
			mockCreateTimeline: mockCreateTimeline{
				enable: true,
				req: &timelinePb.CreateRequest{
					PrimaryActorId:     "internal-actor",
					SecondaryActorId:   "external-actor",
					PrimaryActorName:   payerCustInfo.Name,
					SecondaryActorName: payeeCustInfo.Name,
				},
				res: &timelinePb.CreateResponse{
					Status:   rpc.StatusOk(),
					Timeline: nil,
				},
				err: nil,
			},
			want: &timelineHelper.ResolveTimelineResult{
				PayerActorId: "internal-actor",
				PayeeActorId: "external-actor",
				PayerPiId:    payerPi.Id,
				PayeePiId:    payeePi.Id,
			},
		},
		{
			name: "return permanent failure in case unable to resolve to internal actor",
			args: args{
				ctx:       context.Background(),
				payerInfo: payerCustInfo,
				payeeInfo: payeeCustInfo,
				reqType:   vendors.UPITxnTypePay,
			},
			mockCreatePI: mockCreatePI{
				enable:   true,
				customer: payerCustInfo,
				pi:       payerPi,
				err:      nil,
			},
			mockGetPiFromVPA: mockGetPiFromVPA{
				enable: true,
				vpa:    payeeCustInfo.PaymentAddress,
				pi:     payeePi,
				err:    nil,
			},
			mockGetByPiId: mockGetByPiId{
				enable: true,
				req:    &accountPiPb.GetByPiIdRequest{PiId: payeePi.Id},
				res: &accountPiPb.GetByPiIdResponse{
					Status: rpc.StatusRecordNotFound(),
				},
				err: nil,
			},
			wantErr:     true,
			wantErrType: epifierrors.ErrPermanent,
		},
		{
			name: "should return transient failure in case actor resolution fails",
			args: args{
				ctx:       context.Background(),
				payerInfo: payerCustInfo,
				payeeInfo: payeeCustInfo,
				reqType:   vendors.UPITxnTypePay,
			},
			mockCreatePI: mockCreatePI{
				enable:   true,
				customer: payerCustInfo,
				pi:       payerPi,
				err:      nil,
			},
			mockGetPiFromVPA: mockGetPiFromVPA{
				enable: true,
				vpa:    payeeCustInfo.PaymentAddress,
				pi:     payeePi,
				err:    nil,
			},
			mockGetByPiId: mockGetByPiId{
				enable: true,
				req:    &accountPiPb.GetByPiIdRequest{PiId: payeePi.Id},
				res: &accountPiPb.GetByPiIdResponse{
					Status:  rpc.StatusOk(),
					ActorId: "internal-actor",
				},
				err: nil,
			},
			mockResolveActorFrom: mockResolveActorFrom{
				enable: true,
				req: &actorPb.ResolveActorFromRequest{
					ActorTo:       "internal-actor",
					PiFrom:        payerPi.Id,
					ActorFromName: payerCustInfo.Name,
				},
				res: &actorPb.ResolveActorFromResponse{
					Status: rpc.StatusInternal(),
				},
				err: nil,
			},
			wantErr:     true,
			wantErrType: epifierrors.ErrTransient,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetPiFromVPA.enable {
				mockPiHelperSvc.EXPECT().GetPIFromVpa(context.Background(), tt.mockGetPiFromVPA.vpa).
					Return(tt.mockGetPiFromVPA.pi, tt.mockGetPiFromVPA.err)
			}
			if tt.mockCreatePI.enable {
				mockPiHelperSvc.EXPECT().CreateUPIPi(context.Background(), tt.mockCreatePI.customer, true).
					Return(tt.mockCreatePI.pi, tt.mockCreatePI.err)
			}
			if tt.mockGetByPiId.enable {
				mockAccountPiClient.EXPECT().GetByPiId(context.Background(), tt.mockGetByPiId.req).
					Return(tt.mockGetByPiId.res, tt.mockGetByPiId.err)
			}
			if tt.mockResolveActorFrom.enable {
				mockActorClient.EXPECT().ResolveActorFrom(context.Background(), tt.mockResolveActorFrom.req).
					Return(tt.mockResolveActorFrom.res, tt.mockResolveActorFrom.err)
			}
			if tt.mockResolveActorTo.enable {
				mockActorClient.EXPECT().ResolveActorTo(context.Background(), tt.mockResolveActorTo.req).
					Return(tt.mockResolveActorTo.res, tt.mockResolveActorTo.err)
			}
			if tt.mockCreateTimeline.enable {
				mockTimelineClient.EXPECT().Create(context.Background(), tt.mockCreateTimeline.req).
					Return(tt.mockCreateTimeline.res, tt.mockCreateTimeline.err)
			}

			got, err := timelineHelperSvc.ResolveTimeline(
				tt.args.ctx,
				tt.args.payerInfo,
				tt.args.payeeInfo,
				tt.args.reqType,
				true,
				true,
			)
			if (err != nil) != tt.wantErr {
				t.Errorf("ResolveTimeline() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ResolveTimeline() got = %v, want %v", got, tt.want)
				return
			}
			if tt.wantErr && tt.wantErrType != nil && !errors.Is(err, tt.wantErrType) {
				t.Errorf("ResolveTimeline() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestService_ResolveTimeline_New(t *testing.T) {
	t.Parallel()
	// init logger for tests to avoid nil logger panics from logger.Info
	logger.Init("test")

	payerCustInfo := &upiPb.Customer{
		PaymentAddress: "payer@okaxis",
		Name:           "payer name",
		Type:           upiPb.CustomerType_PERSON,
	}

	payerPiWithName := &piPb.PaymentInstrument{
		Id:   "pi-1",
		Type: piPb.PaymentInstrumentType_UPI,
		Identifier: &piPb.PaymentInstrument_Upi{
			Upi: &piPb.Upi{Vpa: payerCustInfo.PaymentAddress},
		},
		VerifiedName: payerCustInfo.Name,
	}

	payerPiWithoutName := &piPb.PaymentInstrument{
		Id:   "pi-1",
		Type: piPb.PaymentInstrumentType_UPI,
		Identifier: &piPb.PaymentInstrument_Upi{
			Upi: &piPb.Upi{Vpa: payerCustInfo.PaymentAddress},
		},
		VerifiedName: "",
	}

	payeeCustInfo := &upiPb.Customer{
		PaymentAddress: "payee@okaxis",
		Name:           "payee name",
		Type:           upiPb.CustomerType_PERSON,
	}

	payeePi := &piPb.PaymentInstrument{
		Id:   "pi-2",
		Type: piPb.PaymentInstrumentType_UPI,
		Identifier: &piPb.PaymentInstrument_Upi{
			Upi: &piPb.Upi{Vpa: payeeCustInfo.PaymentAddress},
		},
		VerifiedName: payeeCustInfo.Name,
	}

	type args struct {
		ctx       context.Context
		payerInfo *upiPb.Customer
		payeeInfo *upiPb.Customer
		reqType   string
	}

	tests := []struct {
		name               string
		args               args
		setup              func(mockPiHelperSvc *piHelperMocks.MockPIHelper, mockAccountPiClient *accountPiMocks.MockAccountPIRelationClient, mockActorClient *actorMocks.MockActorClient, mockTimelineClient *timelineMocks.MockTimelineServiceClient)
		want               *timelineHelper.ResolveTimelineResult
		wantErr            bool
		wantErrIsPermanent bool
	}{
		{
			name: "should use payer name from PI when PI has name in PAY flow",
			args: args{
				ctx:       context.Background(),
				payerInfo: payerCustInfo,
				payeeInfo: payeeCustInfo,
				reqType:   vendors.UPITxnTypePay,
			},
			setup: func(mockPiHelperSvc *piHelperMocks.MockPIHelper, mockAccountPiClient *accountPiMocks.MockAccountPIRelationClient, mockActorClient *actorMocks.MockActorClient, mockTimelineClient *timelineMocks.MockTimelineServiceClient) {
				mockPiHelperSvc.EXPECT().CreateUPIPi(context.Background(), payerCustInfo, true).Return(payerPiWithName, nil)
				mockPiHelperSvc.EXPECT().GetPIFromVpa(context.Background(), payeeCustInfo.PaymentAddress).Return(payeePi, nil)
				mockAccountPiClient.EXPECT().GetByPiId(context.Background(), &accountPiPb.GetByPiIdRequest{PiId: payeePi.Id}).Return(&accountPiPb.GetByPiIdResponse{
					Status:  rpc.StatusOk(),
					ActorId: "internal-actor",
				}, nil)
				mockActorClient.EXPECT().ResolveActorFrom(context.Background(), &actorPb.ResolveActorFromRequest{
					ActorTo:       "internal-actor",
					PiFrom:        payerPiWithName.Id,
					ActorFromName: payerCustInfo.Name, // same as payerPiWithName.VerifiedName
				}).Return(&actorPb.ResolveActorFromResponse{Status: rpc.StatusOk(), ActorFrom: "external-actor"}, nil)
				mockTimelineClient.EXPECT().Create(context.Background(), &timelinePb.CreateRequest{
					PrimaryActorId:     "external-actor",
					SecondaryActorId:   "internal-actor",
					PrimaryActorName:   payerCustInfo.Name,
					SecondaryActorName: payeeCustInfo.Name,
				}).Return(&timelinePb.CreateResponse{Status: rpc.StatusOk()}, nil)
			},
			want: &timelineHelper.ResolveTimelineResult{
				PayerActorId: "external-actor",
				PayeeActorId: "internal-actor",
				PayerPiId:    payerPiWithName.Id,
				PayeePiId:    payeePi.Id,
				PayerName:    payerCustInfo.Name,
				PayeeName:    payeeCustInfo.Name,
			},
		},
		{
			name: "should fall back to payerInfo.Name when PI name is empty in PAY flow",
			args: args{
				ctx:       context.Background(),
				payerInfo: payerCustInfo,
				payeeInfo: payeeCustInfo,
				reqType:   vendors.UPITxnTypePay,
			},
			setup: func(mockPiHelperSvc *piHelperMocks.MockPIHelper, mockAccountPiClient *accountPiMocks.MockAccountPIRelationClient, mockActorClient *actorMocks.MockActorClient, mockTimelineClient *timelineMocks.MockTimelineServiceClient) {
				mockPiHelperSvc.EXPECT().CreateUPIPi(context.Background(), payerCustInfo, true).Return(payerPiWithoutName, nil)
				mockPiHelperSvc.EXPECT().GetPIFromVpa(context.Background(), payeeCustInfo.PaymentAddress).Return(payeePi, nil)
				mockAccountPiClient.EXPECT().GetByPiId(context.Background(), &accountPiPb.GetByPiIdRequest{PiId: payeePi.Id}).Return(&accountPiPb.GetByPiIdResponse{
					Status:  rpc.StatusOk(),
					ActorId: "internal-actor",
				}, nil)
				mockActorClient.EXPECT().ResolveActorFrom(context.Background(), &actorPb.ResolveActorFromRequest{
					ActorTo:       "internal-actor",
					PiFrom:        payerPiWithoutName.Id,
					ActorFromName: payerCustInfo.Name, // fallback should be used
				}).Return(&actorPb.ResolveActorFromResponse{Status: rpc.StatusOk(), ActorFrom: "external-actor"}, nil)
				mockTimelineClient.EXPECT().Create(context.Background(), &timelinePb.CreateRequest{
					PrimaryActorId:     "external-actor",
					SecondaryActorId:   "internal-actor",
					PrimaryActorName:   payerCustInfo.Name,
					SecondaryActorName: payeeCustInfo.Name,
				}).Return(&timelinePb.CreateResponse{Status: rpc.StatusOk()}, nil)
			},
			want: &timelineHelper.ResolveTimelineResult{
				PayerActorId: "external-actor",
				PayeeActorId: "internal-actor",
				PayerPiId:    payerPiWithoutName.Id,
				PayeePiId:    payeePi.Id,
				PayerName:    payerCustInfo.Name,
				PayeeName:    payeeCustInfo.Name,
			},
		},
		{
			name: "should resolve actors and create timeline for COLLECT flow",
			args: args{
				ctx:       context.Background(),
				payerInfo: payerCustInfo,
				payeeInfo: payeeCustInfo,
				reqType:   vendors.UPITxnTypeCollect,
			},
			setup: func(mockPiHelperSvc *piHelperMocks.MockPIHelper, mockAccountPiClient *accountPiMocks.MockAccountPIRelationClient, mockActorClient *actorMocks.MockActorClient, mockTimelineClient *timelineMocks.MockTimelineServiceClient) {
				mockPiHelperSvc.EXPECT().CreateUPIPi(context.Background(), payeeCustInfo, true).Return(payeePi, nil)
				mockPiHelperSvc.EXPECT().GetPIFromVpa(context.Background(), payerCustInfo.PaymentAddress).Return(payerPiWithName, nil)
				mockAccountPiClient.EXPECT().GetByPiId(context.Background(), &accountPiPb.GetByPiIdRequest{PiId: payerPiWithName.Id}).Return(&accountPiPb.GetByPiIdResponse{
					Status:  rpc.StatusOk(),
					ActorId: "internal-actor",
				}, nil)
				mockActorClient.EXPECT().ResolveActorTo(context.Background(), &actorPb.ResolveActorToRequest{
					ActorFrom:   "internal-actor",
					PiTo:        payeePi.Id,
					ActorToName: payeeCustInfo.Name,
				}).Return(&actorPb.ResolveActorToResponse{Status: rpc.StatusOk(), ActorTo: "external-actor"}, nil)
				mockTimelineClient.EXPECT().Create(context.Background(), &timelinePb.CreateRequest{
					PrimaryActorId:     "internal-actor",
					SecondaryActorId:   "external-actor",
					PrimaryActorName:   payerCustInfo.Name,
					SecondaryActorName: payeeCustInfo.Name,
				}).Return(&timelinePb.CreateResponse{Status: rpc.StatusOk()}, nil)
			},
			want: &timelineHelper.ResolveTimelineResult{
				PayerActorId: "internal-actor",
				PayeeActorId: "external-actor",
				PayerPiId:    payerPiWithName.Id,
				PayeePiId:    payeePi.Id,
				PayerName:    payerCustInfo.Name,
				PayeeName:    payeeCustInfo.Name,
			},
		},
		{
			name: "should return permanent error when txn type is unknown",
			args: args{
				ctx:       context.Background(),
				payerInfo: payerCustInfo,
				payeeInfo: payeeCustInfo,
				reqType:   "UNKNOWN",
			},
			setup: func(_ *piHelperMocks.MockPIHelper, _ *accountPiMocks.MockAccountPIRelationClient, _ *actorMocks.MockActorClient, _ *timelineMocks.MockTimelineServiceClient) {
			},
			wantErr:            true,
			wantErrIsPermanent: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			mockActorClient := actorMocks.NewMockActorClient(ctr)
			mockTimelineClient := timelineMocks.NewMockTimelineServiceClient(ctr)
			mockAccountPiClient := accountPiMocks.NewMockAccountPIRelationClient(ctr)
			mockPiHelperSvc := piHelperMocks.NewMockPIHelper(ctr)

			svc := timelineHelper.NewService(mockActorClient, mockTimelineClient, mockAccountPiClient, mockPiHelperSvc)

			tt.setup(mockPiHelperSvc, mockAccountPiClient, mockActorClient, mockTimelineClient)
			got, err := svc.ResolveTimeline(tt.args.ctx, tt.args.payerInfo, tt.args.payeeInfo, tt.args.reqType, true, true)
			if (err != nil) != tt.wantErr {
				t.Fatalf("ResolveTimeline() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.wantErr {
				if tt.wantErrIsPermanent && !errors.Is(err, epifierrors.ErrPermanent) {
					t.Fatalf("ResolveTimeline() error = %v, want permanent error", err)
				}
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Fatalf("ResolveTimeline() got = %+v, want %+v", got, tt.want)
			}
		})
	}
}
