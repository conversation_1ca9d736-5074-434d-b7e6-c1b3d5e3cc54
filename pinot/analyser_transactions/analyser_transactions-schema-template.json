{"schemaName": "{{.<PERSON><PERSON><PERSON>}}", "primaryKeyColumns": ["transaction_id", "credit_debit"], "dimensionFieldSpecs": [{"name": "transaction_id", "dataType": "STRING"}, {"name": "order_id", "dataType": "STRING"}, {"name": "provenance", "dataType": "STRING"}, {"name": "ui_entry_point", "dataType": "STRING"}, {"name": "from_actor_id", "dataType": "STRING"}, {"name": "from_actor_type", "dataType": "STRING"}, {"name": "from_pi", "dataType": "STRING"}, {"name": "from_pi_type", "dataType": "STRING"}, {"name": "from_bank_account_type", "dataType": "STRING"}, {"name": "from_psp_handle", "dataType": "STRING"}, {"name": "from_bank_name", "dataType": "STRING"}, {"name": "from_account_id", "dataType": "STRING"}, {"name": "from_merchant_id", "dataType": "STRING"}, {"name": "from_merchant_name", "dataType": "STRING"}, {"name": "to_actor_id", "dataType": "STRING"}, {"name": "to_actor_type", "dataType": "STRING"}, {"name": "to_pi", "dataType": "STRING"}, {"name": "to_pi_type", "dataType": "STRING"}, {"name": "to_bank_account_type", "dataType": "STRING"}, {"name": "to_bank_name", "dataType": "STRING"}, {"name": "to_psp_handle", "dataType": "STRING"}, {"name": "to_account_id", "dataType": "STRING"}, {"name": "to_merchant_id", "dataType": "STRING"}, {"name": "to_merchant_name", "dataType": "STRING"}, {"name": "order_workflow", "dataType": "STRING"}, {"name": "transaction_remarks", "dataType": "STRING"}, {"name": "payment_protocol", "dataType": "STRING"}, {"name": "partner_bank", "dataType": "STRING"}, {"name": "tags", "dataType": "STRING", "singleValueField": false}, {"name": "order_status", "dataType": "STRING"}, {"name": "transaction_status", "dataType": "STRING"}, {"name": "credit_debit", "dataType": "STRING"}, {"name": "derived_status", "dataType": "STRING"}, {"name": "ontology_ids", "dataType": "STRING", "singleValueField": false}, {"name": "display_categories", "dataType": "STRING", "singleValueField": false}, {"name": "l0_ontologies", "dataType": "STRING", "singleValueField": false}, {"name": "from_derived_entity_id", "dataType": "STRING"}, {"name": "to_derived_entity_id", "dataType": "STRING"}, {"name": "from_actor_tier", "dataType": "STRING"}], "metricFieldSpecs": [{"name": "amount", "dataType": "LONG"}], "dateTimeFieldSpecs": [{"name": "order_created_at_unix", "dataType": "LONG", "format": "1:MILLISECONDS:EPOCH", "granularity": "1:MILLISECONDS"}, {"name": "transaction_created_at_unix", "dataType": "LONG", "format": "1:MILLISECONDS:EPOCH", "granularity": "1:MILLISECONDS"}, {"name": "expire_at_unix", "dataType": "LONG", "format": "1:MILLISECONDS:EPOCH", "granularity": "1:MILLISECONDS"}, {"name": "updated_at_unix", "dataType": "LONG", "format": "1:MILLISECONDS:EPOCH", "granularity": "1:MILLISECONDS"}, {"name": "view_updated_at_unix", "dataType": "LONG", "format": "1:MILLISECONDS:EPOCH", "granularity": "1:MILLISECONDS"}, {"name": "executed_at_unix", "dataType": "LONG", "format": "1:MILLISECONDS:EPOCH", "granularity": "1:MILLISECONDS"}, {"name": "executed_at_date_unix_ist", "dataType": "LONG", "format": "1:MILLISECONDS:EPOCH", "granularity": "1:MILLISECONDS"}]}