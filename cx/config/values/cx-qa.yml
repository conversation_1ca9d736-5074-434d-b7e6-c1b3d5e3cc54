Application:
  Environment: "qa"
  Name: "cx"
  IsSecureRedis: true

Server:
  Ports:
    GrpcPort: 8095
    GrpcSecurePort: 9508
    HttpPort: 9999
    HttpPProfPort: 9990
    HttpSecurePort: 9785

EpifiDb:
  AppName: "cx"
  StatementTimeout: 1s
  Name: "sherlock"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

Aws:
  Region: "ap-south-1"

Cognito:
  UserPoolId: "ap-south-1_9aHcUtHvX"
  ClientId: "4al2jarhht539emhtj7rpb9hfs"

EmailVerification:
  VerificationUrl: "https://web.qa.pointz.in/customer-auth/email-verification"
  FromEmail: "<EMAIL>"
  FromEmailName: "Fi support"

MobilePromptVerification:
  NotificationTitle: "Verify Yourself"
  NotificationBody: "Please choose yes if the request was initiated by you"
  Validity: 300 #validity of mobile prompt in seconds

AuthFactorRetryLimit:
  DOB: 3
  MobilePrompt: 3
  EmailVerification: 3
  TransactionAmount: 3
  LastFivePanCharacters: 3
  PermanentAddressPinCode: 3
  Default: 3

CustomerAuth:
  AuthValidityDuration: "10m"
  EmailValidityDuration: "5m"
  MobilePromptValidityDuration: "2m"
  MaxResetCount: 1
  IsInAppNotificationEnabled: true

FreshdeskTicketPublisher:
  QueueName: "qa-freshdesk-ticket-queue"

FreshdeskTicketSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-freshdesk-ticket-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

FreshdeskContactPublisher:
  QueueName: "qa-freshdesk-contact-queue"

FreshdeskContactSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-freshdesk-contact-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

DisputePublisher:
  QueueName: "qa-cx-dispute-queue"

DisputeSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-cx-dispute-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

DisputeCreateTicketPublisher:
  QueueName: "qa-cx-dispute-create-ticket-queue"

DisputeCreateTicketSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-cx-dispute-create-ticket-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

DisputeUpdateTicketPublisher:
  QueueName: "qa-cx-dispute-update-ticket-queue"

DisputeUpdateTicketSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-cx-dispute-update-ticket-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 1     # Keeping cumulative limit across relevant queues below freshdesk Update API (PUT) rate limit of 160 per minute
        Period: 6s  # 10 per minute
    Namespace: "cx"

DisputeAddNoteTicketPublisher:
  QueueName: "qa-cx-dispute-add-note-ticket-queue"

DisputeAddNoteTicketSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-cx-dispute-add-note-ticket-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

DisputeExternalPublisher:
  QueueName: "qa-cx-dispute-events-external-queue"

WatsonIncidentReportingPublisher:
  QueueName: "qa-cx-watson-incident-reporting-queue"

WatsonIncidentReportingSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-cx-watson-incident-reporting-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 80 # Keeping it below freshdesk Create API (POST) rate limit of 160 per minute
        Period: 1m
    Namespace: "cx"

WatsonIncidentResolutionPublisher:
  QueueName: "qa-cx-watson-incident-resolution-queue"

WatsonIncidentResolutionSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-cx-watson-incident-resolution-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 1      # Keeping cumulative limit across relevant queues below freshdesk Update API (PUT) rate limit of 160 per minute
        Period: 3s   # 20 per minute
    Namespace: "cx"

WatsonTicketEventPublisher:
  QueueName: "qa-cx-watson-ticket-event-queue"

WatsonTicketEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-cx-watson-ticket-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 100
        Period: 1m
    Namespace: "cx"

WatsonCreateTicketSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-cx-watson-create-ticket-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 100
        Period: 1m
    Namespace: "cx"

CrmIssueTrackerIntegrationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-cx-crm-issue-tracker-integration-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 100
        Period: 1s
    Namespace: "cx"

CrmIssueTrackerIntegrationPublisher:
  QueueName: "qa-cx-crm-issue-tracker-integration-queue"


RMSEventPublisher:
  QueueName: "qa-rms-event-queue"

RewardsManualGiveawayEventPublisher:
  QueueName: "qa-rewards-manual-giveaway-event-queue"

DevActionPublisher:
  QueueName: "qa-dev-action-delay-queue"

IFTFileProcessorEventPublisher:
  QueueName: "qa-pay-international-fund-transfer-process-file-queue"

DevActionSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-dev-action-delay-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 60
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

FreshdeskTicketDataEventSubscriberFifo:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "dev-fd-events-cx-queue.fifo"
  QueueOwnerAccountId: "************"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

FreshdeskTicketDataEventSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "dev-cx-freshdesk-events"
  QueueOwnerAccountId: "************"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

AuditLog:
  DefaultLimit: 6

Sherlock:
  SherlockCallbackURL: "https://qa-sherlock.pointz.in/external-api/v1/customer-auth"
  ClientMaxIdleConns: 10
  ClientIdleConnTimeout: 30

Transaction:
  ToleranceValue: 10
  NumTxnToFetch: 3
  PageSize: 20

Comms:
  PageSize: 20

Secrets:
  Ids:
    SherlockApiKey: "qa/cx/sherlock-internal-api-key"
    DbUsernamePassword: "qa/rds/postgres/sherlock"
    FreshchatAppId: "qa/cx/freshchat-sdk-app-id"
    FreshchatAppKey: "qa/cx/freshchat-sdk-app-key"
    RudderWriteKey: "qa/rudder/internal-writekey"
    GoogleCloudProfilingServiceAccountKey: "qa/gcloud/profiling-service-account-key"
    FederalPoolAccountNo: "qa/cx/federal-pool-acc-no"
    MonorailServiceAccountKey: "qa/monorail/service-account-private-key"
    AirflowUsernamePassword: "qa/data/airflow"
    KafkaCredentials: "qa/kafka/cx"
    KafkaCaCertificate: "qa/kafka/cert"
    IFTReportsSlackBotOauthToken: "qa/ift/slack-bot-oauth-token"
    StrapiApiKey: "qa/cx/strapi"

Dispute:
  DefaultDisputeConfigVersion: "DISPUTE_CONFIG_VERSION_V6"
  IsRestrictedReleaseEnabledForConfigVersion: true
  DisputeConfigVersionAndPlatformToReleaseConfMap:
    DISPUTE_CONFIG_VERSION_V2:
      ANDROID:
        IsGroupCheckEnabled: false
        MinAppVersion: 0
        MaxAppVersion: **********
      IOS:
        IsGroupCheckEnabled: false
        MinAppVersion: 0
        MaxAppVersion: **********
    DISPUTE_CONFIG_VERSION_V3:
      ANDROID:
        IsGroupCheckEnabled: false
        MinAppVersion: 180
        MaxAppVersion: **********
      IOS:
        IsGroupCheckEnabled: false
        MinAppVersion: 180
        MaxAppVersion: **********
    DISPUTE_CONFIG_VERSION_V4:
      ANDROID:
        IsGroupCheckEnabled: false
        MinAppVersion: 185
        MaxAppVersion: **********
      IOS:
        IsGroupCheckEnabled: false
        MinAppVersion: 275
        MaxAppVersion: **********
    DISPUTE_CONFIG_VERSION_V5:
      ANDROID:
        IsGroupCheckEnabled: false
        MinAppVersion: 233
        MaxAppVersion: **********
      IOS:
        IsGroupCheckEnabled: false
        MinAppVersion: 1320
        MaxAppVersion: **********
  S3BucketName: "epifi-federal-disputes"
  MaxThresholdDurationForEscalation: "2h"
  MaxAttemptCountForReverseProcessing: 5
  IsGetNextQuestionsForAppV2Enabled: true
  IsGetNextQuestionsV2Enabled: true

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.qa-common-cache-redis.rxzivf.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    # on qa we are using common redis cluster with a different db
    DB: 0
  HystrixCommand:
    CommandName: "cx_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 500
      ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

AppLog:
  LogTTL: "360h"
  MaxLogCountPerUser: 5
  # RPC size limit is 4 MB. So using 3.5MB as chunk size
  LogChunkSize: 3500000


Flags:
  TrimDebugMessageFromStatus: false

Payout:
  StatusCheckDelay: "30m"
PayoutStatusCheckPublisher:
  QueueName: "qa-cx-payout-status-check-event-queue"
PayoutStatusCheckSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-cx-payout-status-check-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

WaitlistSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-freelancer-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

RateLimit:
  MaxRequestsPerMinPerUser: 1000
  MaxRequestPerMinPerUserPerApiDefault: 100
  # replace all occurrences of . and / with _ in the full method name before adding it here
  # need to do this because . and / are interpreted differently by config loader and should not be used in key
  MaxRequestPerMinPerUserPerApiMap:
    _cx_app_log_AppLog_GetLogsData: 10000
    _cx_developer_ticket_summary_TicketSummary_GetTicketDetails: 20
    _cx_data_collector_preapprovedloan_PreApprovedLoan_GetLoanDetails: 10
    _cx_data_collector_preapprovedloan_PreApprovedLoan_GetLoanUserDetails: 10


RlConfig:
  ResourceMap:
    sherlock_user:
      Rate: 1000
      Period: 1m
    _cx_app_log_AppLog_GetLogsData:
      Rate: 1000
      Period: 1m
    _cx_developer_ticket_summary_TicketSummary_GetTicketDetails:
      Rate: 20
      Period: 1m
    api_default:
      Rate: 30
      Period: 1m
  Namespace: "cx"

UsePkgRateLimiter: false

CallRecording:
  CallRecordingBucketName: "epifi-data-prod-ozonetel-call-recs"
  CallTranscriptionBucketName: "epifi-ozonetel-transcription"

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

OrderConfig:
  TxnCountForLastNTxns: 5

LivenessVideoConfig:
  S3BucketName: "epifi-qa-liveness"

ProcessTicketJobConfig:
  MaxTicketsThresholdMap:
    ONBOARDING: 10
    RE_ONBOARDING: 10
    UPI_PINSET: 10
    DEBIT_CARD: 10
  JobStatsEmailParam:
    FromEmailId: "<EMAIL>"
    FromEmailName: "Process Ticket Automation non-prod"
    ReceiverMailIdList:
      ONBOARDING:
        ReceiverMailInfo1:
          EmailName: "Diparth"
          EmailId: "<EMAIL>"
        ReceiverMailInfo2:
          EmailName: "Sachin"
          EmailId: "<EMAIL>"
        ReceiverMailInfo3:
          EmailName: "Hardik"
          EmailId: "<EMAIL>"
    EmailMsg:
      ONBOARDING: "Please find troubleshooting details in attachment."
  NumberOfDays: 89

MaxCountThresholdForFetchingBulkUserInfo: 500

BulkUserInfoViaEmailConfig:
  MaxCountThreshold: 10000

UpiDisputeAutoUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-cx-upi-dispute-auto-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

AuthValidation:
  SkipAuthForTestAutomationAgent: true
  TestAutomationAgentEmails: ["<EMAIL>"]
  MethodListForSkippingAccessControlValidation: [
      "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
  ]

UpdateTicketSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-cx-update-ticket-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 80    # Keeping cumulative limit across relevant queues below freshdesk Update API (PUT) rate limit of 160 per minute
        Period: 1m
    Namespace: "cx"

UpdateTicketPublisher:
  QueueName: "qa-cx-update-ticket-queue"

UploadCreditMISToVendorPublisher:
  QueueName: "qa-mf-upload-credit-mis-non-prod-queue"

CreateTicketSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-cx-create-ticket-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

CreateTicketPublisher:
  QueueName: "qa-cx-create-ticket-queue"

BulkTicketJobConfig:
  MaxTicketThreshold: 1000

Tracing:
  Enable: true

ChatBotConfig:
  ActorIdsEnabledForForceNuggetChatbot:
    ACqdnjXp2KtS250304: true # Amitkumar
    AC2ncosiYMRP250711: true # Kunal (nugget team)
  NuggetNamespace: "NUGGET_FIMONEY"
  IsForceFallbackToDefaultEnabled: true
  DefaultInAppChatView: "IN_APP_CHAT_VIEW_TYPE_SENSEFORTH_WEBVIEW"
  MaxTimeDurationThresholdForLastSuccessfulSessionTime: "30m"
  SenseforthChatInitInfo:
    WebViewURLMap:
      ANDROID: "https://epifi-chatbot-frontend-cx-uat-builds.s3.ap-south-1.amazonaws.com/chatsdk/v1/index.html?userAgent=android"
      IOS: "https://epifi-chatbot-frontend-cx-uat-builds.s3.ap-south-1.amazonaws.com/chatsdk/v1/index.html?userAgent=ios"
  IsFreshChatExperimentEnabled: true
  ActorIdsEnabledFreshchatIssueTreeExperiment: ["ACqdnjXp2KtS250304"]
  NuggetDeeplinkUri: "epifi://unified-support/builder?flowType=ticketing&omniTicketingFlow=true"

OzonetelCallEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-vn-ozonetel-call-details-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

TicketReconciliationEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-cx-ticket-data-reconciliation-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 3
      TimeUnit: "Minute"

FreshchatEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-vn-freshchat-action-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 100
        Period: 1s
    Namespace: "cx"

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

CelestialSignalWorkflowPublisher:
  QueueName: "qa-celestial-signal-workflow-queue"

AccountFreezeStatusConfig:
  CreditFreezeBannerElementId: "9da4d3ab-b7f0-4a71-bdbc-49e05bca8652"

SherlockFeedbackDetailsConfig:
  PageSize: 20

RiskCasePublisher:
  QueueName: "qa-risk-cases-ingestion-queue"

RiskDisputePublisher:
  QueueName: "qa-risk-dispute-upload-queue"

RiskS3Config:
  BucketName: "epifi-qa-risk"

DataS3Config:
  BucketName: "epifi-raw-dev"
  S3Prefix: "qa/data/vendor/segmentation_service"
  StaticSegmentSrcFolderPath: "manual_dump/static_segment"
  StaticSegmentDestFolderPath: "static_segments"

InternationalFundTransfer:
  EnableLRSCheckFromVendor: false
  DocumentsBucketName: "epifi-qa-pay-international-fund-transfer"

SalaryOpsConfig:
  SalaryProgramS3BucketName: "epifi-salaryprogram"

AirflowConfig:
  TriggerDagUrl: "http://10.6.35.53:8080/api/v1/dags/%s/dagRuns"

RewardsOrderUpdateEventQueuePublisher:
  QueueName : "qa-rewards-order-update-queue"

RewardsCreditCardTxnEventQueuePublisher:
  QueueName: "qa-rewards-credit-card-txn-event-queue"

CallRoutingEventPublisher:
  TopicName: "qa-cx-call-routing-event-topic"

TicketUpdateEventPublisher:
  TopicName: "qa-cx-ticket-update-event-topic"

CreateTicketEventPublisher:
  TopicName: "qa-cx-ticket-create-event-topic"

FederalEscalationCreateEventPublisher:
  QueueName: "qa-cx-escalation-creation-queue"

FederalEscalationUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-cx-escalation-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

FederalEscalationCreationEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-cx-escalation-creation-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

CasperItcDownloadFileQueuePublisher:
  QueueName: "qa-casper-itc-download-file-queue"

OrderUpdateEventForTxnCategorizationPublisher:
  QueueName: "qa-categorizer-update-order-queue"


AATxnCategorizationPublisher:
  QueueName: "qa-categorizer-aa-txn-queue"

CCTxnCategorizationPublisher:
  QueueName: "qa-categorizer-cc-transaction-event-queue"

RudderEventKafkaConsumerGroup:
  StartOnServerStart: true
  GroupID: "cx_activity_qa"
  # Kafka only has one non-prod env so same brokers and topic across all non-prod env.
  Brokers:
    - "kafka-1.data-dev.pointz.in:9094"
    - "kafka-2.data-dev.pointz.in:9094"
    - "kafka-3.data-dev.pointz.in:9094"
  Topic: "qa.events.tech"
  RateLimitConfig:
    RedisOptions:
      IsSecureRedis: true
      Options:
        Addr: "master.qa-common-cache-redis.rxzivf.aps1.cache.amazonaws.com:6379"
        Password: ""
        DB: 0
    ResourceMap:
      consumer_group:
        Rate: 100
        Period: 1s
    Namespace: "cx"

ErrorActivityConfig:
  IsPipingErrorEventToWatsonEnabled: false

S3EventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-cx-s3-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

StrapiConfig:
  BaseURL: "https://strapi.qa.pointz.in/api/"
  ApiKey: "qa/cx/strapi"
  HttpClientConfig:
    Transport:
      DialContext:
        Timeout: 30s
        KeepAlive: 30s
      TLSHandshakeTimeout: 10s
      MaxIdleConns: 100
      IdleConnTimeout: 90s
    Timeout: 10s

RiskOutcallFormRolloutConfig:
  WhitelistedQuestionnaireTemplates: ["QUESTIONNAIRE_TEMPLATE_HIGH_CREDIT_AMOUNT",
  "QUESTIONNAIRE_TEMPLATE_HIGH_CREDIT_COUNT",
  "QUESTIONNAIRE_TEMPLATE_HIGH_CREDIT_COUNT_AND_AMOUNT",
  "QUESTIONNAIRE_TEMPLATE_STOP_BUSINESS_ACTIVITY_ACK"]
  MaxFormsPerUser: 1
  WhitelistedReviewTypes: ["REVIEW_TYPE_TRANSACTION_REVIEW"]

CXFreshdeskTicketBaseURL: "https://ficaretesting.freshdesk.com/a/tickets/%s"

WatchlistReasons:
  - "WATCHLIST_REASON_RISKY_AFU_REVIEW"
  - "WATCHLIST_REASON_RISKY_TM_REVIEW"
  - "WATCHLIST_REASON_UNFROZEN_BUSINESS_ACTIVITY"
  - "WATCHLIST_REASON_INACTIVE_ACCOUNT"
  - "WATCHLIST_REASON_FAILED_TRX_VERIFICATION"
  - "WATCHLIST_REASON_LEA_WITH_NOC"

FeatureReleaseConfig:
  FeatureConstraints:
    FEATURE_CX_CALL_IVR:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CX_CALL_BLOCKER:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 14 # CX_INTERNAL
    FEATURE_REPORT_FRAUD_IN_DISPUTE_BOTTOM_SHEET:
      AppVersionConstraintConfig:
        MinAndroidVersion: 402
        MinIOSVersion: 2549
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_FRESHCHAT_CHATBOT_SDK_ENABLED:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL

S3BucketNameForFileGenerator:
  CamsS3Bucket: "epifi-qa-mutualfund"
  KarvyS3Bucket: "epifi-qa-mutualfund-karvy"

SalaryProgramLeadManagementConfig:
  SalaryProgramS3BucketName: "epifi-qa-salaryprogram"
  SalaryProgramB2BS3BucketName: "epifi-qa-salaryprogram-b2b"
  LeadDetailsExcelSheetPathB2B: "LeadDetails.xlsx"

FederalEscalationConfig:
  FederalEscalationAttachmentBucketName: "epifi-qa-cx-ticket-attachments"
  QueueId: "300000412618875"
  QPHRateLimit: 500

EpifiIconS3Config:
  BucketName: "epifi-icons"

IssueConfigServiceConfig:
  IsCacheEnabled: true
  UseNewCategoryMappingForLLMScreen: true
  IssueCategoryCreatedFromTime: "2025-06-19T00:00:00Z"
  IssueCategoryCreatedToTime: "2026-06-15T00:00:00Z"

EsConfig:
  RoleArn: ""
  ESEndpoint: ""
