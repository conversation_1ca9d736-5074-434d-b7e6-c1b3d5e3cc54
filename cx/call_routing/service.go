package call_routing

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"sort"
	"strconv"
	"time"

	"github.com/epifi/be-common/pkg/async/goroutine"

	helper2 "github.com/epifi/gamma/cx/call_routing/helper"
	cxGenConf "github.com/epifi/gamma/cx/config/genconf"
	cxHelper "github.com/epifi/gamma/cx/helper"
	"github.com/epifi/gamma/cx/metrics"
	wireTypes "github.com/epifi/gamma/cx/wire/types"
	"github.com/epifi/gamma/pkg/obfuscator"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	actorPb "github.com/epifi/gamma/api/actor"
	callRoutingPb "github.com/epifi/gamma/api/cx/call_routing"
	callRoutingEventPb "github.com/epifi/gamma/api/cx/call_routing/event"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/cx/config"

	"github.com/pkg/errors"
	"go.uber.org/zap"
)

type Service struct {
	usersClient               userPb.UsersClient
	actorClient               actorPb.ActorClient
	userGroupClient           userGroupPb.GroupClient
	conf                      *cxGenConf.Config
	callRoutingHelper         helper2.ICallRoutingHelper
	callRoutingEventPublisher wireTypes.CallRoutingEventPublisher
}

func NewCallRoutingService(usersClient userPb.UsersClient, actorClient actorPb.ActorClient,
	userGroupClient userGroupPb.GroupClient, conf *cxGenConf.Config, callRoutingHelper helper2.ICallRoutingHelper, callRoutingEventPublisher wireTypes.CallRoutingEventPublisher) *Service {
	return &Service{
		usersClient:               usersClient,
		actorClient:               actorClient,
		userGroupClient:           userGroupClient,
		conf:                      conf,
		callRoutingHelper:         callRoutingHelper,
		callRoutingEventPublisher: callRoutingEventPublisher,
	}
}

var _ callRoutingPb.CallRoutingServer = &Service{}
var (
	featureDisabledForActorErr    = errors.New("the feature is not enabled for the user")
	featureDisabledForPlatformErr = errors.New("the feature is not enabled for platform")
	featureDisabledGloballyErr    = errors.New("the feature is not yet enabled")
)

func (s *Service) GetRoutingChannelForUser(ctx context.Context, req *callRoutingPb.GetRoutingChannelForUserRequest) (*callRoutingPb.GetRoutingChannelForUserResponse, error) {
	// check if we need to play pre-recorded message to the actor on best effort basis
	callRecordingIdentifier, err := s.callRoutingHelper.GetRecordingIdForPhoneNumber(ctx, req.GetPhoneNumber(), req.GetMonitorUcid())
	if err != nil {
		logger.Error(ctx, "error while checking if actor is eligible for any pre-recorded message", zap.Error(err))
	}
	// if the actor is eligible for any particular pre-recorded message, its corresponding id will be returned
	// as per discussion with ozonetel, at a time only the recording id field or (priority + routing channel) fields
	// will be populated from our end
	// if the recording id field is populated, the pre-recoreded message will be played to the user
	// and the call will be dropped
	if callRecordingIdentifier != callRoutingPb.RecordingIdentifier_RECORDING_IDENTIFIER_UNSPECIFIED {
		// record metrics for the returned recording id
		metrics.RecordRecordingId(callRecordingIdentifier.String())
		// publish common call routing event for a prerecorded message if enabled in config
		// so that other services can trigger async flows based on the event
		// for example : risk service sending comms whenever a risk related recorded message is returned
		if s.conf.CallRoutingConfig().IsRecordedMessageEventEnabled() {
			s.PublishRecordingIdEvent(ctx, callRecordingIdentifier, req.GetPhoneNumber())
		}
		logger.Info(ctx, "actor is played pre-recorded message and call ends", zap.String("recordingIdentifier", callRecordingIdentifier.String()), zap.String(logger.PHONE_NUMBER_HASH, obfuscator.HashedPhoneNum(req.PhoneNumber)))
		// return the recording identifier
		return &callRoutingPb.GetRoutingChannelForUserResponse{
			Status:      rpcPb.StatusOk(),
			RecordingId: callRecordingIdentifier,
		}, nil
	}
	routingChannel, routingErr := s.callRoutingHelper.GetRoutingChannelForPhoneNumber(ctx, req.GetPhoneNumber())
	if routingErr != nil {
		logger.Error(ctx, "error while identifying routing channel for phone number", zap.Error(routingErr))
		return &callRoutingPb.GetRoutingChannelForUserResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	metrics.RecordRoutingChannelCount(routingChannel.String())
	logger.Info(ctx, "actor is routed based on the routing channel", zap.String("routingChannel", routingChannel.String()), zap.String(logger.PHONE_NUMBER_HASH, obfuscator.HashedPhoneNum(req.PhoneNumber)))
	resp := &callRoutingPb.GetRoutingChannelForUserResponse{
		Status:         rpcPb.StatusOk(),
		RoutingChannel: routingChannel,
	}

	// get language preference for user
	// if error is encountered then log the error
	// since it might happen that user has not set the call language preference yet
	callLanguagePrefList, callLangErr := s.callRoutingHelper.GetCallLanguagePreferenceForPhoneNumber(ctx, req.GetPhoneNumber())
	if callLangErr != nil {
		logger.Error(ctx, "error while fetching call lang pref list for user", zap.Error(callLangErr))
	} else {
		resp.LanguagePreferenceList = callLanguagePrefList
	}

	// fetch priority for given routing channel and phone number
	// if error is encountered then log the error and set default priority value from config
	// errors encountered in this step should not block entire rpc flow
	// since routing channel is already identified and failure in this step would only affect prioritization of user within that routing channel
	// evaluate only if priority routing flag is set, keeping this behind a flag so segment service is not called on prod
	if s.conf.CallRoutingConfig().IsPriorityRoutingEnabled() {
		priority, priorityErr := s.callRoutingHelper.GetCallPriorityByRoutingChannelAndPhoneNumber(ctx, routingChannel, req.GetPhoneNumber())
		if priorityErr != nil {
			logger.Error(ctx, "error while fetching priority for user", zap.Error(priorityErr))
		}
		resp.Priority = int32(priority)
		logger.Info(ctx, "Priority for user is fetched", zap.Int("priority", priority), zap.String(logger.PHONE_NUMBER_HASH, obfuscator.HashedPhoneNum(req.PhoneNumber)))
		// log custom metrics for routing channel and priority
		metrics.RecordCallRoutingChannelAndPriority(routingChannel.String(), strconv.Itoa(int(priority)))
	}
	// if the actor needs to go through call IVR, populating the version field as 1 because
	// as per our discussion with vendor, if this field is 0, then no IVR will be used before connecting to agent
	// but if the field is populated with value 1, IVR flow will be utilized before connecting to agent
	resp.Version = 1
	return resp, nil
}

func (s *Service) getCacheKeyForAuthFactor(actorId string) string {
	return fmt.Sprintf(s.conf.CustomerAuth().AuthFactorCacheKey(), actorId)
}

// nolint:funlen
func (s *Service) CheckCallLangPrefAndGetLangOptions(ctx context.Context, req *callRoutingPb.CheckCallLangPrefAndGetLangOptionsRequest) (*callRoutingPb.CheckCallLangPrefAndGetLangOptionsResponse, error) {
	// Check for controlled release conditions
	restrictErr := s.checkReleaseRestrictions(ctx, req.GetActorId(), s.conf.CallRoutingConfig().CallLangPrefReleaseConfig())
	if restrictErr != nil {
		if errors.Is(restrictErr, featureDisabledForPlatformErr) || errors.Is(restrictErr, featureDisabledForActorErr) ||
			errors.Is(restrictErr, featureDisabledGloballyErr) {
			logger.Debug(ctx, "Call language preference feature is disabled from config")
			return &callRoutingPb.CheckCallLangPrefAndGetLangOptionsResponse{
				Status: rpcPb.StatusPermissionDenied(),
			}, nil
		}
		logger.Error(ctx, "Call language preference restricted for this user / app platform", zap.String(logger.ACTOR_ID, req.GetActorId()),
			zap.String(logger.PLATFORM, epificontext.AppPlatformFromContext(ctx).String()), zap.Error(restrictErr))
		return &callRoutingPb.CheckCallLangPrefAndGetLangOptionsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while evaluating release restrictions on service usage"),
		}, nil
	}

	// Check if call language preference is set for this user
	userPrefResp, err := s.usersClient.GetUserPreferences(ctx, &userPb.GetUserPreferencesRequest{
		ActorId:         req.GetActorId(),
		PreferenceTypes: []userPb.PreferenceType{userPb.PreferenceType_PREFERENCE_TYPE_PREFERRED_CALL_LANGUAGE}})
	if te := epifigrpc.RPCError(userPrefResp, err); te != nil && !userPrefResp.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error in calling user service for call language preference", zap.Error(te))
		return &callRoutingPb.CheckCallLangPrefAndGetLangOptionsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error in calling user service"),
		}, nil
	}
	// Read from config: The list of supported languages which user can set a preference
	langPrefNameList := s.conf.CallRoutingConfig().CallLangPreferencesList()
	langPrefNameStrList := cxHelper.ConvertROArrToStringArr(langPrefNameList)
	sort.Strings(langPrefNameStrList)
	langPrefList := convertLangNameToEnumList(ctx, langPrefNameStrList)
	// Read from config: The list of unsupported languages which user can suggest us to support in future
	langSuggestionNameList := s.conf.CallRoutingConfig().CallLangSuggestionsList()
	langSuggestionNameStrList := cxHelper.ConvertROArrToStringArr(langSuggestionNameList)
	sort.Strings(langSuggestionNameStrList)
	langSuggestionList := convertLangNameToEnumList(ctx, langSuggestionNameStrList)

	if len(langPrefList) == 0 {
		logger.Error(ctx, "failed to get language preferences to show")
		return &callRoutingPb.CheckCallLangPrefAndGetLangOptionsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("failed to get language preferences to show"),
		}, nil
	}
	// If user preference is not set, return list of options from which the user can select call languages
	if userPrefResp.GetStatus().IsRecordNotFound() {
		return &callRoutingPb.CheckCallLangPrefAndGetLangOptionsResponse{
			Status:            rpcPb.StatusOk(),
			PreferenceOptions: langPrefList,
			SuggestionOptions: langSuggestionList,
		}, nil
	}
	selectedUserPrefs := getSelectedUserPrefsFromUserPrefResp(userPrefResp, userPb.PreferenceType_PREFERENCE_TYPE_PREFERRED_CALL_LANGUAGE)
	// Return selected_prefs as well if the user preference for preferred_call_language is already set.
	return &callRoutingPb.CheckCallLangPrefAndGetLangOptionsResponse{
		Status:            rpcPb.StatusOk(),
		PreferenceOptions: langPrefList,
		SuggestionOptions: langSuggestionList,
		SelectedUserPrefs: selectedUserPrefs,
	}, nil
}

func getSelectedUserPrefsFromUserPrefResp(resp *userPb.GetUserPreferencesResponse, preferenceType userPb.PreferenceType) []types.Language {
	for _, pref := range resp.GetUserPreferences() {
		if pref.GetPreferenceType() == preferenceType {
			return pref.GetPreferenceValue().GetPreferredCallLanguage().GetPreferredLanguages()
		}
	}
	return nil
}

func convertLangNameToEnumList(ctx context.Context, langNameList []string) []types.Language {
	var langEnumList []types.Language
	for _, langName := range langNameList {
		lang, ok := types.Language_value[langName]
		if !ok {
			logger.Error(ctx, "invalid language name read from config", zap.String("Language", langName))
			continue
		}
		langEnumList = append(langEnumList, types.Language(lang))
	}
	return langEnumList
}

func (s *Service) checkReleaseRestrictions(ctx context.Context, actorId string, releaseConf *config.ReleaseConfig) error {
	// check if restricted release is not enabled
	if releaseConf == nil || !releaseConf.IsRestrictedReleaseEnabled {
		return featureDisabledGloballyErr
	}
	// check if dynamic elements is enabled for the app platform
	appPlatform := epificontext.AppPlatformFromContext(ctx)
	if appPlatform == commontypes.Platform_PLATFORM_UNSPECIFIED {
		return errors.New("app platform unspecified")
	}
	if !releaseConf.IsEnabledForPlatform[appPlatform.String()] {
		return featureDisabledForPlatformErr
	}
	// get the list of user groups which this user belongs to
	userGroupList, err := s.GetUserGroupsForActor(ctx, actorId)
	if err != nil {
		return errors.Wrap(err, "error while identifying user group for actor")
	}
	// check if the user belongs to one of enabled user group categories in config
	for _, group := range userGroupList {
		isEnabled := releaseConf.IsEnabledForUserGroup[group.String()]
		if isEnabled {
			return nil
		}
	}
	return featureDisabledForActorErr
}

func (s *Service) GetUserGroupsForActor(ctx context.Context, actorId string) ([]commontypes.UserGroup, error) {
	entityRes, err := s.actorClient.GetEntityDetailsByActorId(ctx, &actorPb.GetEntityDetailsByActorIdRequest{ActorId: actorId})
	if err = epifigrpc.RPCError(entityRes, err); err != nil {
		return nil, errors.Wrap(err, "error while fetching entity details using actor id")
	}
	userRes, userErr := s.usersClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_Id{Id: entityRes.GetEntityId()},
	})
	if err := epifigrpc.RPCError(userRes, userErr); err != nil {
		return nil, errors.Wrap(err, "error while fetching user details")
	}
	userGrpRes, userGrpErr := s.userGroupClient.GetGroupsMappedToEmail(ctx,
		&userGroupPb.GetGroupsMappedToEmailRequest{Email: userRes.GetUser().GetProfile().GetEmail()})
	if err := epifigrpc.RPCError(userGrpRes, userGrpErr); err != nil {
		return nil, errors.Wrap(err, "error while fetching user group details")
	}
	return userGrpRes.GetGroups(), nil
}

func buildRecordingIdEvent(actorId string, recordingIdentifier callRoutingPb.RecordingIdentifier) *callRoutingEventPb.CallRoutingEvent {
	return &callRoutingEventPb.CallRoutingEvent{
		CallRoutingEventType: callRoutingPb.CallRoutingEventType_CALL_ROUTING_EVENT_TYPE_PRE_RECORDED_MESSAGE,
		Event: &callRoutingEventPb.CallRoutingEvent_PreRecordedMessageEvent{
			PreRecordedMessageEvent: &callRoutingEventPb.PreRecordedMessageEvent{
				ActorId:             actorId,
				RecordingIdentifier: recordingIdentifier,
			},
		},
	}
}

// PublishRecordingIdEvent publishes the message to the topic after we have shortlisted
// an actor to greet with a pre-recorded message
func (s *Service) PublishRecordingIdEvent(ctx context.Context, recordingIdentifier callRoutingPb.RecordingIdentifier, phoneNumber *commontypes.PhoneNumber) {
	goroutine.Run(ctx, 30*time.Second, func(ctx2 context.Context) {
		getMinimalUserResp, err := s.usersClient.GetMinimalUser(ctx2, &userPb.GetMinimalUserRequest{
			Identifier: &userPb.GetMinimalUserRequest_PhoneNumber{
				PhoneNumber: phoneNumber,
			},
		})
		// if there is an error while fetching actor id from phone number
		/// we will just log the error and let the goroutine to exit
		if te := epifigrpc.RPCError(getMinimalUserResp, err); te != nil {
			logger.Error(ctx, "error while fetching user details for publishing recording id event", zap.Error(te), zap.String(logger.PHONE_NUMBER_HASH, obfuscator.HashedPhoneNum(phoneNumber)), zap.String(logger.ACTOR_ID_V2, getMinimalUserResp.GetMinimalUser().GetActorId()))
		} else {
			// otherwise we would try to publish the call recording event
			event := buildRecordingIdEvent(getMinimalUserResp.GetMinimalUser().GetActorId(), recordingIdentifier)
			messageId, messageErr := s.callRoutingEventPublisher.Publish(ctx2, event)
			// if there is an error while publishing the message just log the error
			if messageErr != nil {
				logger.Error(ctx2, "error while publishing call routing event", zap.Error(messageErr), zap.String("recordingIdentifier", event.GetPreRecordedMessageEvent().GetRecordingIdentifier().String()), zap.String(logger.ACTOR_ID_V2, getMinimalUserResp.GetMinimalUser().GetActorId()))
			} else {
				logger.Info(ctx2, "call routing event published", zap.String(logger.QUEUE_MESSAGE_ID, messageId), zap.String("callRoutingEventType", event.GetCallRoutingEventType().String()), zap.String(logger.ACTOR_ID_V2, getMinimalUserResp.GetMinimalUser().GetActorId()))
			}
		}
	})
}
