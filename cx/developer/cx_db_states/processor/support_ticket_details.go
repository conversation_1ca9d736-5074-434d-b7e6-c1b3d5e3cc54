package processor

import (
	"context"
	"strings"
	"time"

	"github.com/epifi/gamma/api/cx/developer"
	"github.com/epifi/gamma/api/cx/developer/db_state"
	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	"github.com/epifi/gamma/cx/ticket/dao"
	"github.com/epifi/be-common/pkg/logger"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
)

type DevSupportTicketDetails struct {
	supportTicketDao dao.ISupportTicketDao
}

func NewDevSupportTicketDetails(supportTicketDao dao.ISupportTicketDao) *DevSupportTicketDetails {
	return &DevSupportTicketDetails{
		supportTicketDao: supportTicketDao,
	}
}

const (
	ActorIdList         = "actor_id_list"
	StatusList          = "status_list"
	SourceList          = "source_list"
	ProductCategoryList = "product_category_list"
	IdentifierType      = "identifier_type"
	IdentifierValue     = "identifier_value"
	FromDate            = "from_date"
	ToDate              = "to_date"
	ActiveAfterDate     = "active_after_date"
	// 30 days
	MaxAllowedTimeRange = 30 * 24 * time.Hour
)

//nolint:funlen
func (d *DevSupportTicketDetails) FetchParamList(ctx context.Context, entity developer.CXEntity) ([]*db_state.ParameterMeta, error) {
	var statusOptions []string
	for key := range ticketPb.Status_value {
		statusOptions = append(statusOptions, key)
	}
	var sourceOptions []string
	for key := range ticketPb.Source_value {
		sourceOptions = append(sourceOptions, key)
	}
	var productCategoryOptions []string
	for key := range ticketPb.ProductCategory_value {
		productCategoryOptions = append(productCategoryOptions, key)
	}
	var identifierOptions []string
	for key := range ticketPb.UserIdentifierType_value {
		identifierOptions = append(identifierOptions, key)
	}
	paramList := []*db_state.ParameterMeta{
		{
			Name:            TicketId,
			Label:           "Ticket ID",
			Type:            db_state.ParameterDataType_INTEGER,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            ActorIdList,
			Label:           "Actor Id list (comma separated)",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            StatusList,
			Label:           "Status List",
			Type:            db_state.ParameterDataType_MULTI_SELECT_DROPDOWN,
			Options:         statusOptions,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            SourceList,
			Label:           "Source List",
			Type:            db_state.ParameterDataType_MULTI_SELECT_DROPDOWN,
			Options:         sourceOptions,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            ProductCategoryList,
			Label:           "Product Category List",
			Type:            db_state.ParameterDataType_MULTI_SELECT_DROPDOWN,
			Options:         productCategoryOptions,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            IdentifierType,
			Label:           "Identifier Type",
			Type:            db_state.ParameterDataType_DROPDOWN,
			Options:         identifierOptions,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            IdentifierValue,
			Label:           "Identifier Value",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            FromDate,
			Label:           "From Date",
			Type:            db_state.ParameterDataType_TIMESTAMP,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            ToDate,
			Label:           "To Date",
			Type:            db_state.ParameterDataType_TIMESTAMP,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            ActiveAfterDate,
			Label:           "Active after Date (i.e. tickets in open state post this date)",
			Type:            db_state.ParameterDataType_TIMESTAMP,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
	}
	return paramList, nil
}

//nolint:funlen
func (d *DevSupportTicketDetails) FetchData(ctx context.Context, entity developer.CXEntity, filters []*db_state.Filter) (string, error) {
	var ticketId int64
	ticketFilters := &ticketPb.TicketFilters{}
	mar := protojson.MarshalOptions{UseEnumNumbers: false, EmitUnpopulated: true, Multiline: true}

	for _, filter := range filters {
		switch filter.GetParameterName() {
		case TicketId:
			ticketId = filter.GetIntegerValue()
		case ActorIdList:
			ticketFilters.ActorIdList = strings.Split(filter.GetStringValue(), ",")
			// trimming spaces to remove any spaces between commas
			for idx := range ticketFilters.GetActorIdList() {
				ticketFilters.ActorIdList[idx] = strings.TrimSpace(ticketFilters.GetActorIdList()[idx])
			}
		case StatusList:
			for _, v := range filter.GetMultiSelectDropdownFilter().GetDropdownValues() {
				val, ok := ticketPb.Status_value[v]
				if !ok {
					return "invalid status value passed in filters", nil
				}
				ticketFilters.StatusList = append(ticketFilters.StatusList, ticketPb.Status(val))
			}
		case SourceList:
			for _, v := range filter.GetMultiSelectDropdownFilter().GetDropdownValues() {
				val, ok := ticketPb.Source_value[v]
				if !ok {
					return "invalid source value passed in filters", nil
				}
				ticketFilters.SourceList = append(ticketFilters.SourceList, ticketPb.Source(val))
			}
		case ProductCategoryList:
			for _, v := range filter.GetMultiSelectDropdownFilter().GetDropdownValues() {
				val, ok := ticketPb.ProductCategory_value[v]
				if !ok {
					return "invalid product category value passed in filters", nil
				}
				ticketFilters.ProductCategoryList = append(ticketFilters.ProductCategoryList, ticketPb.ProductCategory(val))
			}
		case IdentifierType:
			val, ok := ticketPb.UserIdentifierType_value[filter.GetDropdownValue()]
			if !ok {
				return "invalid indentifier type value passed in filters", nil
			}
			ticketFilters.IdentifierType = ticketPb.UserIdentifierType(val)
		case IdentifierValue:
			ticketFilters.IdentifierValue = filter.GetStringValue()
		case FromDate:
			ticketFilters.FromTime = filter.GetTimestamp()
		case ToDate:
			ticketFilters.ToTime = filter.GetTimestamp()
		case ActiveAfterDate:
			ticketFilters.ActiveAfterTime = filter.GetTimestamp()
		}
	}
	// fetch single ticket details if id is passed
	if ticketId != 0 {
		details, err := d.supportTicketDao.GetById(ctx, ticketId)
		if err != nil {
			logger.Error(ctx, "error while fetching ticket details from db", zap.Error(err))
			return "error while fetching ticket details using ticket id", nil
		}
		details.RemoveSensitiveFields()
		jsonResp, err := mar.Marshal(details)
		if err != nil {
			return "{\"error\": \"error in marshal response \"}", nil
		}
		return string(jsonResp), nil
	}
	// from and to data are mandatory if ticket id is not passed in request
	if ticketFilters.GetFromTime() == nil || ticketFilters.GetToTime() == nil {
		return "from and to date are mandatory if ticket id is not passed", nil
	}
	// max date range check
	if ticketFilters.GetFromTime().AsTime().Add(MaxAllowedTimeRange).Before(ticketFilters.GetToTime().AsTime()) {
		return "max time range can't be more than 30 days", nil
	}
	totalCount, err := d.supportTicketDao.GetTicketCount(ctx, ticketFilters)
	if err != nil {
		logger.Error(ctx, "error while fetching ticket details from db", zap.Error(err))
		return "error while fetching ticket details from db", nil
	}
	// we will return only 30 ticket record from here, will need pagination support in db states for returning all
	ticketDetailsList, err := d.supportTicketDao.GetAllTickets(ctx, ticketFilters, nil, 30)
	if err != nil {
		logger.Error(ctx, "error while fetching ticket details from db", zap.Error(err))
		return "error while fetching ticket details from db", nil
	}
	return d.TicketDetailsResponse(ctx, ticketDetailsList, totalCount)
}

func (d *DevSupportTicketDetails) TicketDetailsResponse(ctx context.Context, detailList []*ticketPb.TicketDetails, totalCount int64) (string, error) {
	for _, ticketDetail := range detailList {
		ticketDetail.RemoveSensitiveFields()
	}
	resp := &developer.DevTicketDetailsListResponse{
		TotalCount:        totalCount,
		TicketDetailsList: detailList,
	}
	mar := protojson.MarshalOptions{UseEnumNumbers: false, EmitUnpopulated: true, Multiline: true}
	jsonResp, err := mar.Marshal(resp)
	if err != nil {
		return "", err
	}
	return string(jsonResp), err
}
