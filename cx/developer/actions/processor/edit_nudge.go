//nolint:all
package processor

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	actionPb "github.com/epifi/gamma/api/cx/developer/actions"
	dsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/frontend/deeplink"
	nudgePb "github.com/epifi/gamma/api/nudge"
)

type EditNudge struct {
	NudgeClient nudgePb.NudgeServiceClient
}

func NewEditNudge(nudgeClient nudgePb.NudgeServiceClient) *EditNudge {
	return &EditNudge{NudgeClient: nudgeClient}
}

func (s *EditNudge) FetchParamList(ctx context.Context, action actionPb.DeveloperActions, devActionMeta *actionPb.DevActionMeta) ([]*dsPb.ParameterMeta, error) {
	parameterList := []*dsPb.ParameterMeta{
		{
			Name:            Id,
			Label:           "Nudge Id",
			Type:            dsPb.ParameterDataType_STRING,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
		},
		{
			Name:            AutoDismissalTimePeriod,
			Label:           "Auto dismissal time period in days",
			Type:            dsPb.ParameterDataType_INTEGER,
			ParameterOption: dsPb.ParameterOption_OPTIONAL,
		},
		{
			Name:            SnoozeTime,
			Label:           "Snooze time in days",
			Type:            dsPb.ParameterDataType_INTEGER,
			ParameterOption: dsPb.ParameterOption_OPTIONAL,
		},
		{
			Name:            ActiveTill,
			Label:           "Active till",
			Type:            dsPb.ParameterDataType_STRING,
			ParameterOption: dsPb.ParameterOption_OPTIONAL,
		},
		{
			Name:            DisplayTill,
			Label:           "Display till",
			Type:            dsPb.ParameterDataType_STRING,
			ParameterOption: dsPb.ParameterOption_OPTIONAL,
		},
		{
			Name:            Area,
			Label:           "Area",
			Type:            dsPb.ParameterDataType_DROPDOWN,
			ParameterOption: dsPb.ParameterOption_OPTIONAL,
			Options: lo.Filter(lo.Keys(nudgePb.NudgeArea_value), func(val string, _ int) bool {
				return !lo.Contains([]string{nudgePb.NudgeArea_NUDGE_AREA_UNSPECIFIED.String(), nudgePb.NudgeArea_NUDGE_AREA_ACCOUNT_STATUS.String(), nudgePb.NudgeArea_NUDGE_AREA_INVESTMENTS.String(), nudgePb.NudgeArea_NUDGE_AREA_INSIGHTS.String(), nudgePb.NudgeArea_NUDGE_AREA_SALARY_ACCOUNT.String()}, val)
			}),
		},
		{
			Name:            Category,
			Label:           "Category",
			Type:            dsPb.ParameterDataType_DROPDOWN,
			ParameterOption: dsPb.ParameterOption_OPTIONAL,
			Options: lo.Filter(lo.Keys(nudgePb.NudgeCategory_value), func(val string, _ int) bool {
				return !lo.Contains([]string{nudgePb.NudgeCategory_NUDGE_CATEGORY_UNSPECIFIED.String(), nudgePb.NudgeCategory_ONBOARDING.String(), nudgePb.NudgeCategory_SUGGESTED_ACTION.String(), nudgePb.NudgeCategory_FEATURE_PROMOTIONS.String(), nudgePb.NudgeCategory_NUDGE_CATEGORY_CREDIT_CARD.String(), nudgePb.NudgeCategory_NUDGE_CATEGORY_SAVINGS_ACCOUNT.String(), nudgePb.NudgeCategory_NUDGE_CATEGORY_ACCOUNT_TIER.String(), nudgePb.NudgeCategory_NUDGE_CATEGORY_KYC.String(), nudgePb.NudgeCategory_NUDGE_CATEGORY_DC_OFFER.String(), nudgePb.NudgeCategory_NUDGE_CATEGORY_CC_OFFER.String(), nudgePb.NudgeCategory_NUDGE_CATEGORY_EARN_FI_COINS.String(), nudgePb.NudgeCategory_NUDGE_CATEGORY_SALARY_ACCOUNT.String()}, val)
			}),
		},
		{
			Name:            SubCategory,
			Label:           "Sub-category",
			Type:            dsPb.ParameterDataType_DROPDOWN,
			ParameterOption: dsPb.ParameterOption_OPTIONAL,
			Options: lo.Filter(lo.Keys(nudgePb.NudgeSubCategory_value), func(val string, _ int) bool {
				return val != "NUDGE_SUB_CATEGORY_UNSPECIFIED"
			}),
		},
		{
			Name:            Urgency,
			Label:           "Urgency",
			Type:            dsPb.ParameterDataType_DROPDOWN,
			ParameterOption: dsPb.ParameterOption_OPTIONAL,
			Options: lo.Filter(lo.Keys(nudgePb.NudgeUrgency_value), func(val string, _ int) bool {
				return val != "NUDGE_URGENCY_UNSPECIFIED"
			}),
		},
		{
			Name:            UserInactionConsequence,
			Label:           "User Inaction Consequence",
			Type:            dsPb.ParameterDataType_DROPDOWN,
			ParameterOption: dsPb.ParameterOption_OPTIONAL,
			Options: lo.Filter(lo.Keys(nudgePb.NudgeUserInactionConsequence_value), func(val string, _ int) bool {
				return val != "NUDGE_USER_INACTION_CONSEQUENCE_UNSPECIFIED"
			}),
		},
		{
			Name:            DisplayInScreens,
			Label:           "Display in screens",
			Type:            dsPb.ParameterDataType_MULTI_SELECT_DROPDOWN,
			ParameterOption: dsPb.ParameterOption_OPTIONAL,
			Options: []string{
				deeplink.Screen_HOME.String(),
				deeplink.Screen_INVESTMENT_LANDING_SCREEN.String(),
				deeplink.Screen_MUTUAL_FUND_DETAILS_SCREEN.String(),
				deeplink.Screen_DC_DASHBOARD_V2_SCREEN.String(),
			},
		},
		{
			Name:            DisplayConfigs,
			Label:           "Display configs",
			Type:            dsPb.ParameterDataType_STRING,
			ParameterOption: dsPb.ParameterOption_OPTIONAL,
		},
		{
			Name:            LoadScreen,
			Label:           "Load screen",
			Type:            dsPb.ParameterDataType_STRING,
			ParameterOption: dsPb.ParameterOption_OPTIONAL,
		},
		{
			Name:            NudgeAdditionalDetails,
			Label:           "Nudge Additional Details",
			Type:            dsPb.ParameterDataType_STRING,
			ParameterOption: dsPb.ParameterOption_OPTIONAL,
		},
	}
	return parameterList, nil
}

func (s *EditNudge) ExecuteAction(ctx context.Context, action actionPb.DeveloperActions, filters []*dsPb.Filter, devActionMeta *actionPb.DevActionMeta) (string, error) {
	if len(filters) == 0 {
		return "", errors.New("filter cannot be nil")
	}
	var (
		id                      = ""
		autoDismissalTimeInSec  = uint64(0)
		snoozeTime              = uint64(0)
		activeTill              = time.Time{}.Format(time.RFC3339)
		displayTill             = time.Time{}.Format(time.RFC3339)
		area                    = nudgePb.NudgeArea_NUDGE_AREA_UNSPECIFIED
		category                = nudgePb.NudgeCategory_NUDGE_CATEGORY_UNSPECIFIED
		subCategory             = nudgePb.NudgeSubCategory_NUDGE_SUB_CATEGORY_UNSPECIFIED
		urgency                 = nudgePb.NudgeUrgency_NUDGE_URGENCY_UNSPECIFIED
		userInactionConsequence = nudgePb.NudgeUserInactionConsequence_NUDGE_USER_INACTION_CONSEQUENCE_UNSPECIFIED
		displayScreens          []deeplink.Screen
		configs                 = &nudgePb.NudgeDisplays{}
		loadScreen              = &deeplink.Deeplink{}
		additionalDetails       = &nudgePb.NudgeAdditionalDetails{}
	)
	updateFieldMasks := make([]nudgePb.NudgeUpdateFieldMask, 0)
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case Id:
			id = filter.GetStringValue()
		case AutoDismissalTimePeriod:
			autoDismissalTimeInSec = uint64(filter.GetIntegerValue()) * 86400
			updateFieldMasks = append(updateFieldMasks, nudgePb.NudgeUpdateFieldMask_NUDGE_UPDATE_FIELD_MASK_AUTO_DISMISS_DURATION_IN_SECONDS)
		case SnoozeTime:
			snoozeTime = uint64(filter.GetIntegerValue()) * 86400
			updateFieldMasks = append(updateFieldMasks, nudgePb.NudgeUpdateFieldMask_NUDGE_UPDATE_FIELD_MASK_SNOOZE_DURATION_IN_SECONDS)
		case ActiveTill:
			activeTill = filter.GetStringValue()
			updateFieldMasks = append(updateFieldMasks, nudgePb.NudgeUpdateFieldMask_NUDGE_UPDATE_FIELD_MASK_ACTIVE_TILL)
		case DisplayTill:
			displayTill = filter.GetStringValue()
			updateFieldMasks = append(updateFieldMasks, nudgePb.NudgeUpdateFieldMask_NUDGE_UPDATE_FIELD_MASK_DISPLAY_TILL)
		case Area:
			area = nudgePb.NudgeArea(nudgePb.NudgeArea_value[filter.GetDropdownValue()])
			updateFieldMasks = append(updateFieldMasks, nudgePb.NudgeUpdateFieldMask_NUDGE_UPDATE_FIELD_MASK_AREA)
		case Category:
			category = nudgePb.NudgeCategory(nudgePb.NudgeCategory_value[filter.GetDropdownValue()])
			updateFieldMasks = append(updateFieldMasks, nudgePb.NudgeUpdateFieldMask_NUDGE_UPDATE_FIELD_MASK_CATEGORY)
		case SubCategory:
			subCategory = nudgePb.NudgeSubCategory(nudgePb.NudgeSubCategory_value[filter.GetDropdownValue()])
			updateFieldMasks = append(updateFieldMasks, nudgePb.NudgeUpdateFieldMask_NUDGE_UPDATE_FIELD_MASK_SUB_CATEGORY)
		case Urgency:
			urgency = nudgePb.NudgeUrgency(nudgePb.NudgeUrgency_value[filter.GetDropdownValue()])
			updateFieldMasks = append(updateFieldMasks, nudgePb.NudgeUpdateFieldMask_NUDGE_UPDATE_FIELD_MASK_URGENCY)
		case UserInactionConsequence:
			userInactionConsequence = nudgePb.NudgeUserInactionConsequence(nudgePb.NudgeUserInactionConsequence_value[filter.GetDropdownValue()])
			updateFieldMasks = append(updateFieldMasks, nudgePb.NudgeUpdateFieldMask_NUDGE_UPDATE_FIELD_MASK_USER_INACTION_CONSEQUENCE)
		case DisplayInScreens:
			for _, screen := range filter.GetMultiSelectDropdownFilter().GetDropdownValues() {
				displayScreens = append(displayScreens, deeplink.Screen(deeplink.Screen_value[screen]))
			}
			updateFieldMasks = append(updateFieldMasks, nudgePb.NudgeUpdateFieldMask_NUDGE_UPDATE_FIELD_MASK_SCREENS)
		case DisplayConfigs:
			err := protojson.Unmarshal([]byte(filter.GetStringValue()), configs)
			if err != nil {
				logger.Error(ctx, "Invalid display configs", zap.Error(err))
				return "", status.Error(codes.InvalidArgument, "Invalid display configs")
			}
			updateFieldMasks = append(updateFieldMasks, nudgePb.NudgeUpdateFieldMask_NUDGE_UPDATE_FIELD_MASK_DISPLAY_CONFIGS)
		case LoadScreen:
			err := protojson.Unmarshal([]byte(filter.GetStringValue()), loadScreen)
			if err != nil {
				logger.Error(ctx, "Invalid load screen", zap.Error(err))
				return "", status.Error(codes.InvalidArgument, "invalid load screen")
			}
			updateFieldMasks = append(updateFieldMasks, nudgePb.NudgeUpdateFieldMask_NUDGE_UPDATE_FIELD_MASK_DEEPLINK)
		case NudgeAdditionalDetails:
			err := protojson.Unmarshal([]byte(filter.GetStringValue()), additionalDetails)
			if err != nil {
				logger.Error(ctx, "invalid additional details", zap.Error(err))
				return "", status.Error(codes.InvalidArgument, "invalid additional details")
			}
			updateFieldMasks = append(updateFieldMasks, nudgePb.NudgeUpdateFieldMask_NUDGE_UPDATE_FIELD_MASK_ADDITIONAL_DETAILS)
		}
	}

	req := &nudgePb.EditNudgeRequest{
		Id:                           id,
		AutoDismissDurationInSeconds: autoDismissalTimeInSec,
		SnoozeDurationInSeconds:      snoozeTime,
		ActiveTill:                   activeTill,
		DisplayTill:                  displayTill,
		Area:                         area,
		Category:                     category,
		SubCategory:                  subCategory,
		Urgency:                      urgency,
		UserInactionConsequence:      userInactionConsequence,
		Screens:                      displayScreens,
		DisplayConfigs:               configs,
		Deeplink:                     loadScreen,
		AdditionalDetails:            additionalDetails,
		Mask:                         updateFieldMasks,
	}

	res, err := s.NudgeClient.EditNudge(ctx, req)
	if err = epifigrpc.RPCError(res, err); err != nil {
		logger.Error(ctx, "failed to edit nudge", zap.Error(err))
		return "", fmt.Errorf("failed to edit nudge: %w", err)
	}
	// returned marshalled response
	marshalledRes, err := protojson.Marshal(res)
	if err != nil {
		logger.Error(ctx, "error marshalling edit nudge response", zap.Error(err))
		return "", fmt.Errorf("error marshalling edit nudge response: %w", err)
	}
	return string(marshalledRes), nil
}
