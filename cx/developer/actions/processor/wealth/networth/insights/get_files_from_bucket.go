// nolint:dupl
package insights

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	actionPb "github.com/epifi/gamma/api/cx/developer/actions"
	dsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	networthBePb "github.com/epifi/gamma/api/insights/networth"
	"github.com/epifi/gamma/cx/config/genconf"
)

type GetFilesFromBucket struct {
	genConf        *genconf.Config
	networthClient networthBePb.NetWorthClient
}

func NewGetFilesFromBucket(genConf *genconf.Config, networthClient networthBePb.NetWorthClient) *GetFilesFromBucket {
	return &GetFilesFromBucket{
		genConf:        genConf,
		networthClient: networthClient,
	}
}

func (c *GetFilesFromBucket) FetchParamList(ctx context.Context, action actionPb.DeveloperActions, devActionMeta *actionPb.DevActionMeta) ([]*dsPb.ParameterMeta, error) {
	paramList := []*dsPb.ParameterMeta{
		{
			Name:            "s3-paths",
			Label:           "Comma separated s3 paths",
			Type:            dsPb.ParameterDataType_STRING,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
		},
		{
			Name:            "bucket-name",
			Label:           "bucket name",
			Type:            dsPb.ParameterDataType_DROPDOWN,
			Options:         []string{fmt.Sprintf("epifi-%v-wealth-insights", c.genConf.Application().Environment)},
			ParameterOption: dsPb.ParameterOption_MANDATORY,
		},
	}
	return paramList, nil
}

func (c *GetFilesFromBucket) ExecuteAction(ctx context.Context, action actionPb.DeveloperActions, filters []*dsPb.Filter, devActionMeta *actionPb.DevActionMeta) (string, error) {
	if len(filters) == 0 {
		return "", errors.New("filter cannot be nil")
	}

	var filePath, bucketName string
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case "s3-paths":
			filePath = filter.GetStringValue()
		case "bucket-name":
			bucketName = filter.GetDropdownValue()
		default:
			logger.Error(ctx, "invalid filter", zap.String("filter", filter.String()))
			return "", status.Error(codes.InvalidArgument, "invalid filter")
		}
	}
	var marshalledRes []byte
	filePaths := strings.Split(filePath, ",")
	resp, err := c.networthClient.GetFilesFromBucket(ctx, &networthBePb.GetFilesFromBucketRequest{
		FilePaths:  filePaths,
		BucketName: bucketName,
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		logger.Error(ctx, "error in calling GetFileFromBucket", zap.Error(te))
		marshalledRes = []byte(te.Error())
	} else {
		marshalledRes, err = json.Marshal(resp)
		if err != nil {
			logger.Error(ctx, "error marshalling GetFileFromBucket response", zap.Error(err))
			marshalledRes = []byte(err.Error())
		}
	}
	return string(marshalledRes), nil
}
