//nolint:all
package processor

import (
	"context"
	"encoding/json"
	"errors"

	actionPb "github.com/epifi/gamma/api/cx/developer/actions"
	dsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	preApprovedLoanCxPb "github.com/epifi/gamma/api/preapprovedloan/cx"
	preApprovedLoanSimPb "github.com/epifi/gamma/api/simulator/lending/preapprovedloan"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

const (
	ApplicationID = "application_id"
	LoanStatus    = "loan_status"
	PhoneNumber   = "phone_number"
)

type PreApprovedLoanUpdateStatus struct {
	preApprovedLoanSimClient preApprovedLoanSimPb.PreApprovedLoanClient
}

func NewPreApprovedLoanUpdateStatus(preApprovedLoanSimClient preApprovedLoanSimPb.PreApprovedLoanClient) *PreApprovedLoanUpdateStatus {
	return &PreApprovedLoanUpdateStatus{
		preApprovedLoanSimClient: preApprovedLoanSimClient,
	}
}

func (p *PreApprovedLoanUpdateStatus) FetchParamList(ctx context.Context, action actionPb.DeveloperActions, devActionMeta *actionPb.DevActionMeta) ([]*dsPb.ParameterMeta, error) {
	verdictTypes := []string{preApprovedLoanCxPb.Verdict_VERDICT_PASS.String(), preApprovedLoanCxPb.Verdict_VERDICT_FAIL.String()}
	paramList := []*dsPb.ParameterMeta{
		{
			Name:            ApplicationID,
			Label:           "Application ID",
			Type:            dsPb.ParameterDataType_STRING,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
		},
		{
			Name:            LoanStatus,
			Label:           "Loan Status",
			Type:            dsPb.ParameterDataType_DROPDOWN,
			Options:         verdictTypes,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
		},
		{
			Name:            PhoneNumber,
			Label:           "Phone Number",
			Type:            dsPb.ParameterDataType_STRING,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
		},
	}
	return paramList, nil
}

func (p *PreApprovedLoanUpdateStatus) ExecuteAction(ctx context.Context, action actionPb.DeveloperActions, filters []*dsPb.Filter, devActionMeta *actionPb.DevActionMeta) (string, error) {
	if len(filters) == 0 {
		return "", errors.New("filter cannot be nil")
	}

	var (
		applicationID, phNum string
	)
	var loanStatus preApprovedLoanCxPb.Verdict
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case ApplicationID:
			applicationID = filter.GetStringValue()
		case PhoneNumber:
			phNum = filter.GetStringValue()
		case LoanStatus:
			loanStatus = preApprovedLoanCxPb.Verdict(preApprovedLoanCxPb.Verdict_value[filter.GetDropdownValue()])
		default:
			logger.Error(ctx, "invalid filter", zap.String("filter", filter.String()))
			return "", status.Error(codes.InvalidArgument, "invalid filter")
		}
	}

	devAction := preApprovedLoanSimPb.DevActionMockRequest_DENY
	if loanStatus == preApprovedLoanCxPb.Verdict_VERDICT_PASS {
		devAction = preApprovedLoanSimPb.DevActionMockRequest_APPROVE
	}

	res, err := p.preApprovedLoanSimClient.DevActionMock(ctx, &preApprovedLoanSimPb.DevActionMockRequest{
		ApplicationId: applicationID,
		LoanDevAction: devAction,
		PhoneNumber:   phNum,
	})
	if er := epifigrpc.RPCError(res, err); er != nil {
		logger.Error(ctx, "error updating loan application status", zap.Error(er))
		return "Error updating loan application status", nil
	}

	// returned marshalled response
	marshalledRes, err := json.Marshal(res)
	if err != nil {
		logger.Error(ctx, "error while marshalling response", zap.Error(err))
		return "Error while marshalling response", nil
	}
	return string(marshalledRes), nil
}
