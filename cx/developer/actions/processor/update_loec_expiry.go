package processor

import (
	"context"
	"fmt"
	"strings"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	actionPb "github.com/epifi/gamma/api/cx/developer/actions"
	dsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	preapprovedloanPb "github.com/epifi/gamma/api/preapprovedloan"
	preapprovedloanCxPb "github.com/epifi/gamma/api/preapprovedloan/cx"
)

const (
	loecIds = "loec_ids"
	vendor  = "vendor"
)

type ExpireLOEC struct {
	preapprovedloanCxClient preapprovedloanCxPb.CxClient
}

func NewExpireLOEC(preapprovedloanCxClient preapprovedloanCxPb.CxClient) *ExpireLOEC {
	return &ExpireLOEC{
		preapprovedloanCxClient: preapprovedloanCxClient,
	}
}

func getVendorList() []string {
	var vendorsStr []string
	for i, v := range preapprovedloanPb.Vendor_name {
		if i == 0 {
			continue
		}
		vendorsStr = append(vendorsStr, v)
	}
	return vendorsStr
}

func (u *ExpireLOEC) FetchParamList(ctx context.Context, action actionPb.DeveloperActions, devActionMeta *actionPb.DevActionMeta) ([]*dsPb.ParameterMeta, error) {
	vendorList := getVendorList()
	paramList := []*dsPb.ParameterMeta{
		{
			Name:            loecIds,
			Label:           "LOEC IDs (comma separated)",
			Type:            dsPb.ParameterDataType_STRING,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
		},
		{
			Name:            vendor,
			Label:           "Vendor",
			Type:            dsPb.ParameterDataType_DROPDOWN,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
			Options:         vendorList,
		},
	}
	return paramList, nil
}

func (u *ExpireLOEC) ExecuteAction(ctx context.Context, action actionPb.DeveloperActions, filters []*dsPb.Filter, devActionMeta *actionPb.DevActionMeta) (string, error) {
	if len(filters) == 0 {
		return "", status.Error(codes.InvalidArgument, "filter cannot be nil")
	}

	var (
		loecIDsStr string
		vendorStr  string
	)

	for _, filter := range filters {
		switch filter.GetParameterName() {
		case loecIds:
			loecIDsStr = strings.TrimSpace(filter.GetStringValue())
		case vendor:
			vendorStr = strings.TrimSpace(filter.GetDropdownValue())
		default:
			logger.Error(ctx, "invalid filter", zap.String("filter", filter.String()))
			return "", status.Error(codes.InvalidArgument, fmt.Sprintf("invalid filter: %s", filter.String()))
		}
	}

	if loecIDsStr == "" {
		return "", status.Error(codes.InvalidArgument, "loec IDs cannot be empty")
	}
	if vendorStr == "" {
		return "", status.Error(codes.InvalidArgument, "vendor cannot be empty")
	}

	// Split comma separated LOEC IDs
	loecIDs := strings.Split(loecIDsStr, ",")
	for i, id := range loecIDs {
		loecIDs[i] = strings.TrimSpace(id)
	}

	// Convert vendor string to enum value
	vendorValue, ok := preapprovedloanPb.Vendor_value[vendorStr]
	if !ok {
		return "", status.Error(codes.InvalidArgument, fmt.Sprintf("invalid vendor: %s", vendorStr))
	}

	// Call the RPC
	resp, err := u.preapprovedloanCxClient.ExpireLoec(ctx, &preapprovedloanCxPb.ExpireLoecRequest{
		LoecIds: loecIDs,
		Vendor:  preapprovedloanPb.Vendor(vendorValue),
	})
	if grpcErr := epifigrpc.RPCError(resp, err); grpcErr != nil {
		logger.Error(ctx, "error updating LOEC expiry", zap.Error(grpcErr))
		return "", grpcErr
	}

	marshalOptions := protojson.MarshalOptions{}
	marshalOptions.UseEnumNumbers = false

	resJson, err := marshalOptions.Marshal(resp)
	if err != nil {
		logger.Error(ctx, "cannot marshal response to json", zap.Error(err))
		// since action has been done do not return error
		return "", nil
	}
	return string(resJson), nil
}
