//nolint:all
package processor

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"bytes"
	"context"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"

	tcPb "github.com/epifi/gamma/api/comms/inapptargetedcomms"
	actionPb "github.com/epifi/gamma/api/cx/developer/actions"
	"github.com/epifi/gamma/api/cx/developer/db_state"
	dePb "github.com/epifi/gamma/api/dynamic_elements"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	typesUiPb "github.com/epifi/gamma/api/typesv2/ui"
	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/be-common/pkg/epifigrpc"

	"github.com/golang/protobuf/jsonpb"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type AddHomePromoBannerElement struct {
	inAppTcClient tcPb.InAppTargetedCommsClient
}

func NewAddHomePromoBannerElement(inAppTcClient tcPb.InAppTargetedCommsClient) *AddHomePromoBannerElement {
	return &AddHomePromoBannerElement{inAppTcClient: inAppTcClient}
}

const (
	NewPromoBannerCSV = "promo_banner_csv"
	BGColourTheme     = "bg_colour_theme"
	TceTitleV2        = "title_v2"
	Jade              = "jade"
	Ocean             = "ocean"
	Berry             = "berry"
	Lemon             = "lemon"
	Indigo            = "indigo"
	Moss              = "moss"
	Amber             = "amber"
	Cherry            = "cherry"
	Black             = "black"
	yesOption         = "Yes"
	noOption          = "No"
)

var (
	listOfThemes = []string{Jade, Ocean, Berry, Lemon, Indigo, Moss, Amber, Cherry, Berry}

	textColourMapping = map[string]string{
		Jade:   "#004E2D",
		Ocean:  "#0D3641",
		Berry:  "#2C2B6E",
		Lemon:  "#A87A2C",
		Indigo: "#002D6A",
		Moss:   "#37522A",
		Amber:  "#C0723D",
		Cherry: "#6D3149",
		Black:  "#FFFFFF",
	}
	bgColourMapping = map[string]*typesUiPb.BackgroundColour{
		Jade:   typesUiPb.GetRadialGradient([]string{"#A8E0D3", "#A8E0D3"}),
		Ocean:  typesUiPb.GetRadialGradient([]string{"#BCDCE7", "#BCDCE7"}),
		Berry:  typesUiPb.GetRadialGradient([]string{"#CBC6DE", "#CBC6DE"}),
		Lemon:  typesUiPb.GetRadialGradient([]string{"#E8D9A9", "#E8D9A9"}),
		Indigo: typesUiPb.GetRadialGradient([]string{"#C1CCE1", "#C1CCE1"}),
		Moss:   typesUiPb.GetRadialGradient([]string{"#D5E6CE", "#D5E6CE"}),
		Amber:  typesUiPb.GetRadialGradient([]string{"#F6D8AA", "#F6D8AA"}),
		Cherry: typesUiPb.GetRadialGradient([]string{"#F0BECE", "#F0BECE"}),
		Black:  typesUiPb.GetRadialGradient([]string{"#202222", "#202222"}),
	}
	timerBgColourMapping = map[string]string{
		Jade:   "#6BCDB6",
		Ocean:  "#9DC2D0",
		Berry:  "#A9A2C9",
		Lemon:  "#D2B660",
		Indigo: "#98ABCD",
		Moss:   "#AFD2A2",
		Amber:  "#E8AD62",
		Cherry: "#E795AE",
		Black:  "#1D3631",
	}
	indicatorSelectedColourMapping = map[string]*typesUiPb.BackgroundColour{
		Jade:   typesUiPb.GetBlockColor("#00B899"),
		Ocean:  typesUiPb.GetBlockColor("#6294A6"),
		Berry:  typesUiPb.GetBlockColor("#6F62A4"),
		Lemon:  typesUiPb.GetBlockColor("#BC9C3D"),
		Indigo: typesUiPb.GetBlockColor("#4F71AB"),
		Moss:   typesUiPb.GetBlockColor("#86BA6F"),
		Amber:  typesUiPb.GetBlockColor("#D48647"),
		Cherry: typesUiPb.GetBlockColor("#D65779"),
		Black:  typesUiPb.GetBlockColor("#FFFFFF"),
	}
)

//nolint:funlen
func (a *AddHomePromoBannerElement) FetchParamList(ctx context.Context, action actionPb.DeveloperActions, devActionMeta *actionPb.DevActionMeta) ([]*db_state.ParameterMeta, error) {
	paramList := []*db_state.ParameterMeta{
		{
			Name:            NewPromoBannerCSV,
			Label:           "Please upload promo banner document as per the doc: https://docs.google.com/spreadsheets/d/1SudQpyz4D1A68WlHVAIamfNEyb-IHt3V_jFojaXMXAI/edit?usp=sharing",
			Type:            db_state.ParameterDataType_FILE,
			ParameterOption: db_state.ParameterOption_MANDATORY,
		},
	}
	return paramList, nil
}

//nolint:funlen
func (a *AddHomePromoBannerElement) ExecuteAction(ctx context.Context, action actionPb.DeveloperActions, filters []*db_state.Filter, devActionMeta *actionPb.DevActionMeta) (string, error) {
	var (
		utilityType               dePb.ElementUtilityType
		structureType             dePb.ElementStructureType
		title                     string
		iconImageV2               *commontypes.Image
		visualElementFullBanner   *commontypes.VisualElement
		shadowsList               []*typesUiPb.Shadow
		ctaList                   []*dePb.DynamicElementCta
		tceDeeplink               *deeplinkPb.Deeplink
		tceDeeplinkString         string
		startTime, endTime        *timestamppb.Timestamp
		visibilityState           tcPb.VisibilityState
		campaignName, treatmentId string
		rows                      [][]string
		err                       error
		showTimer                 bool
		colourTheme               string
		experimentId              string
		variantId                 string
		area                      tcPb.ElementArea
		subArea                   tcPb.ElementSubArea
		conversionEvent           string
		userTags                  []tcPb.UserTag
		segmentExpression         string
		minAppVersion             = make(map[string]int32)
		maxAppVersion             = make(map[string]int32)
	)
	unmarshaler := jsonpb.Unmarshaler{AllowUnknownFields: true}
	protoUnmarshaller := protojson.UnmarshalOptions{DiscardUnknown: true}
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case NewPromoBannerCSV:
			csvReader := csv.NewReader(bytes.NewReader(filter.GetFile().GetContent()))
			rows, err = csvReader.ReadAll()
			if err != nil {
				return "", status.Error(codes.InvalidArgument, "could not read the csv file: "+err.Error())
			}
		}
	}

	for index, row := range rows {
		if len(row) < 2 {
			// return invalid input in row number
			return "", status.Error(codes.InvalidArgument, fmt.Sprintf("invalid number of values in row number: %v", index))
		}
		fieldName := row[0]
		v := row[1]

		switch fieldName {
		case TceUtilityType:
			val, ok := dePb.ElementUtilityType_value[v]
			if !ok {
				return "", status.Error(codes.InvalidArgument, "invalid utility type")
			}
			utilityType = dePb.ElementUtilityType(val)
		case TceStructureType:
			val, ok := dePb.ElementStructureType_value[v]
			if !ok {
				return "", status.Error(codes.InvalidArgument, "invalid structure type")
			}
			structureType = dePb.ElementStructureType(val)
		case TceTitleV2:
			title = v
		case TceIconImageV2:
			iconImageV2Str := v
			iconImageV2 = &commontypes.Image{}
			if err := protoUnmarshaller.Unmarshal([]byte(iconImageV2Str), iconImageV2); err != nil {
				return "", status.Error(codes.InvalidArgument, "error unmarshalling icon image for banner v2: "+err.Error())
			}
		case TceVisualElementFullBanner:
			visualElementFullBannerStr := v
			visualElementFullBanner = &commontypes.VisualElement{}
			if err := protoUnmarshaller.Unmarshal([]byte(visualElementFullBannerStr), visualElementFullBanner); err != nil {
				return "", status.Error(codes.InvalidArgument, "error unmarshalling visual element full banner for banner v2: "+err.Error())
			}
		case BGColourTheme:
			// Updating colour theme to be used in text, indicator and timer
			colourTheme = v
			if !lo.Contains(listOfThemes, colourTheme) {
				return "", status.Error(codes.InvalidArgument, "invalid colour theme")
			}
		case TceShadows:
			// to unmarshall an array of Shadow jsons
			jsonDecoder := json.NewDecoder(bytes.NewReader([]byte(v)))
			// read open bracket
			_, err := jsonDecoder.Token()
			if err != nil {
				return "", status.Error(codes.InvalidArgument, "invalid shadows list: "+err.Error())
			}
			// Unmarshal each object separately and append to list
			for jsonDecoder.More() {
				shadow := typesUiPb.Shadow{}
				err = unmarshaler.UnmarshalNext(jsonDecoder, &shadow)
				if err != nil {
					return "", status.Error(codes.InvalidArgument, "invalid shadows list: "+err.Error())
				}
				shadowsList = append(shadowsList, &shadow)
			}
		case TceCtaList:
			// to unmarshall an array of CTA jsons
			jsonDecoder := json.NewDecoder(bytes.NewReader([]byte(v)))
			// read open bracket
			_, err := jsonDecoder.Token()
			if err != nil {
				return "", status.Error(codes.InvalidArgument, "invalid CTA list: "+err.Error())
			}
			// Unmarshal each object separately and append to list
			for jsonDecoder.More() {
				cta := dePb.DynamicElementCta{}
				err = unmarshaler.UnmarshalNext(jsonDecoder, &cta)
				if err != nil {
					return "", status.Error(codes.InvalidArgument, "invalid CTA list: "+err.Error())
				}
				ctaList = append(ctaList, &cta)
			}
		case TceDeeplink:
			val, ok := deeplinkPb.Screen_value[v]
			if !ok {
				return "", status.Error(codes.InvalidArgument, "invalid Deeplink screen for banner")
			}
			if val != 0 {
				if tceDeeplink != nil {
					return "", status.Error(codes.InvalidArgument, "only one of the two fields (Deeplink / Deeplink String) must be given")
				}
				tceDeeplink = &deeplinkPb.Deeplink{}
				tceDeeplink.Screen = deeplinkPb.Screen(val)
			}
		case TceDeeplinkString:
			tceDeeplinkString = v
			if tceDeeplinkString != "" {
				if tceDeeplink != nil {
					return "", status.Error(codes.InvalidArgument, "only one of the two fields (Deeplink / Deeplink String) must be given")
				}
				tceDeeplink = &deeplinkPb.Deeplink{}
				if err := protoUnmarshaller.Unmarshal([]byte(tceDeeplinkString), tceDeeplink); err != nil {
					return "", status.Error(codes.InvalidArgument, "error unmarshalling Deeplink String: "+err.Error())
				}
			}
		case TceStartTime:
			t, err := time.Parse(time.RFC3339, strings.TrimSpace(v))
			if err != nil {
				return "", status.Error(codes.InvalidArgument, "error parsing Start time string: "+err.Error())
			}
			startTime = timestamppb.New(t)
		case TceEndTime:
			t, err := time.Parse(time.RFC3339, strings.TrimSpace(v))
			if err != nil {
				return "", status.Error(codes.InvalidArgument, "error parsing End time string: "+err.Error())
			}
			endTime = timestamppb.New(t)
		case TceVisibilityState:
			val, ok := tcPb.VisibilityState_value[v]
			if !ok {
				return "", status.Error(codes.InvalidArgument, "invalid visibility state passed in filters")
			}
			visibilityState = tcPb.VisibilityState(val)
		case TceCampaignName:
			campaignName = v
		case TceTreatmentId:
			treatmentId = v
		case ShowTimer:
			showTimer = yesNoToBool(v)
		case TceExperimentId:
			experimentId = v
		case TceVariantId:
			variantId = v
		case TceElementArea:
			val, ok := tcPb.ElementArea_value[v]
			if !ok || val == 0 {
				return "", status.Error(codes.InvalidArgument, "invalid area state passed in filters")
			}
			area = tcPb.ElementArea(val)
		case TceElementSubArea:
			val, ok := tcPb.ElementSubArea_value[v]
			if !ok || val == 0 {
				return "", status.Error(codes.InvalidArgument, "invalid sub area state passed in filters")
			}
			subArea = tcPb.ElementSubArea(val)
		case TceConversionEvent:
			if v == "" {
				return "", status.Error(codes.InvalidArgument, "conversion event should not be empty")
			}
			conversionEvent = v
		case TceUserTags:
			val, ok := tcPb.UserTag_value[v]
			if !ok {
				return "", status.Error(codes.InvalidArgument, "invalid User Tag")
			}
			userTags = append(userTags, tcPb.UserTag(val))
		case TceSegmentExpression:
			segmentExpression = v
		case TceAndroidMinAppVersion:
			appVersion, err := strconv.Atoi(v)
			if err != nil {
				cxLogger.Error(ctx, "error while converting string to int for app version", zap.Error(err))
				return "", errors.Wrap(err, "failed to validate android app min version")
			}
			if appVersion != 0 {
				minAppVersion[commontypes.Platform_ANDROID.String()] = int32(appVersion)
			}
		case TceAndroidMaxAppVersion:
			appVersion, err := strconv.Atoi(v)
			if err != nil {
				cxLogger.Error(ctx, "error while converting string to int for app version", zap.Error(err))
				return "", errors.Wrap(err, "failed to validate android app max version")
			}
			if appVersion != 0 {
				maxAppVersion[commontypes.Platform_ANDROID.String()] = int32(appVersion)
			}
		case TceIOSMinAppVersion:
			appVersion, err := strconv.Atoi(v)
			if err != nil {
				cxLogger.Error(ctx, "error while converting string to int for app version", zap.Error(err))
				return "", errors.Wrap(err, "failed to validate ios app min version")
			}
			if appVersion != 0 {
				minAppVersion[commontypes.Platform_IOS.String()] = int32(appVersion)
			}
		case TceIOSMaxAppVersion:
			appVersion, err := strconv.Atoi(v)
			if err != nil {
				cxLogger.Error(ctx, "error while converting string to int for app version", zap.Error(err))
				return "", errors.Wrap(err, "failed to validate ios app max version")
			}
			if appVersion != 0 {
				maxAppVersion[commontypes.Platform_IOS.String()] = int32(appVersion)
			}
		}
	}

	// set colours according to specified theme
	var timeCounter *dePb.TimeCounterParams
	if showTimer {
		timeCounter = &dePb.TimeCounterParams{
			ShowTimeCounter: true,
			TextParams:      &commontypes.Text{FontColor: "#FFFFFF", FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS}},
			BgColour:        timerBgColourMapping[colourTheme],
		}
	}

	var elementContent *dePb.ElementContent
	switch structureType {
	case dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER_V2:
		elementContent = &dePb.ElementContent{
			Content: &dePb.ElementContent_BannerV2{BannerV2: &dePb.BannerElementContentV2{
				Title:                           commontypes.GetPlainStringText(title).WithFontStyle(commontypes.FontStyle_HEADLINE_M).WithFontColor(textColourMapping[colourTheme]),
				Image:                           iconImageV2,
				VisualElement:                   commontypes.GetVisualElementImageFromUrl(iconImageV2.GetImageUrl()).WithProperties(&commontypes.VisualElementProperties{Height: 92, Width: 92}),
				VisualElementFullBanner:         visualElementFullBanner,
				BackgroundColor:                 bgColourMapping[colourTheme],
				CtaList:                         ctaList,
				Deeplink:                        tceDeeplink,
				Shadows:                         shadowsList,
				IndicatorDefaultColor:           typesUiPb.GetBlockColor("#FFFFFF"),
				IndicatorSelectedColor:          indicatorSelectedColourMapping[colourTheme],
				TimeCounterParams:               timeCounter,
				BannerElementContentV2UiVariant: dePb.BannerElementContentV2_BANNER_ELEMENT_CONTENT_V2_UI_VARIANT_V2,
			}},
		}
	default:
		return "", status.Error(codes.Unimplemented, "structure type not supported yet")
	}

	tcElement := &tcPb.InAppTargetedCommsElement{
		UtilityType:     utilityType,
		StructureType:   structureType,
		Content:         elementContent,
		StartTime:       startTime,
		EndTime:         endTime,
		VisibilityState: visibilityState,
		MetaData: &tcPb.ElementMetaData{
			CampaignName:    campaignName,
			TreatmentId:     treatmentId,
			ExperimentId:    experimentId,
			VariantId:       variantId,
			Area:            area,
			SubArea:         subArea,
			ConversionEvent: conversionEvent,
		},
		UserTags:          userTags,
		SegmentExpression: segmentExpression,
		Screens:           []deeplinkPb.Screen{deeplinkPb.Screen_HOME},
		ScreensMeta:       []string{"VERSION_V2_SECTION_BODY"},
		AppDetails: &tcPb.AppDetails{
			PlatformToMinSupportedAppVersionMap: minAppVersion,
			PlatformToMaxSupportedAppVersionMap: maxAppVersion,
		},
	}
	resp, err := a.inAppTcClient.AddTargetedCommsElement(ctx, &tcPb.AddTargetedCommsElementRequest{TargetedCommsElement: tcElement})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		cxLogger.Error(ctx, "error while adding targeted comms element", zap.Error(err))
		return "", err
	}
	marshalOptions := protojson.MarshalOptions{UseEnumNumbers: false, EmitUnpopulated: true, Multiline: true}
	resStr, err := marshalOptions.Marshal(resp)
	if err != nil {
		// since action is done, do not return error
		return "", nil
	}
	return string(resStr), nil
}
