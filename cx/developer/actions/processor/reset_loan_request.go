package processor

import (
	"context"
	"fmt"
	"strings"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	actionPb "github.com/epifi/gamma/api/cx/developer/actions"
	dsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/preapprovedloan"
	preapprovedloanCxPb "github.com/epifi/gamma/api/preapprovedloan/cx"
)

const (
	loanRequestIds = "loan_request_ids"
	lrStatus       = "lr_status"
	lseStatus      = "lse_status"
)

type ResetLoanRequest struct {
	preapprovedloanCxClient preapprovedloanCxPb.CxClient
}

func NewResetLoanRequest(
	preapprovedloanCxClient preapprovedloanCxPb.CxClient,
) *ResetLoanRequest {
	return &ResetLoanRequest{
		preapprovedloanCxClient: preapprovedloanCxClient,
	}
}

func (u *ResetLoanRequest) FetchParamList(ctx context.Context, action actionPb.DeveloperActions, devActionMeta *actionPb.DevActionMeta) ([]*dsPb.ParameterMeta, error) {
	var lrStatusOptions []string
	for _, v := range preapprovedloan.LoanRequestStatus_name {
		lrStatusOptions = append(lrStatusOptions, v)
	}
	var lseStatusOptions []string
	for _, v := range preapprovedloan.LoanStepExecutionStatus_name {
		lseStatusOptions = append(lseStatusOptions, v)
	}
	vendorList := getVendorList()
	paramList := []*dsPb.ParameterMeta{
		{
			Name:            loanRequestIds,
			Label:           "Loan Request IDs (comma separated)",
			Type:            dsPb.ParameterDataType_STRING,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
		},
		{
			Name:            vendor,
			Label:           "Vendor",
			Type:            dsPb.ParameterDataType_DROPDOWN,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
			Options:         vendorList,
		},
		{
			Name:            lrStatus,
			Label:           "Loan Request Status",
			Type:            dsPb.ParameterDataType_DROPDOWN,
			Options:         lrStatusOptions,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
		},
		{
			Name:            lseStatus,
			Label:           "Loan Step Execution Status",
			Type:            dsPb.ParameterDataType_DROPDOWN,
			Options:         lseStatusOptions,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
		},
	}
	return paramList, nil
}

func (u *ResetLoanRequest) ExecuteAction(ctx context.Context, action actionPb.DeveloperActions, filters []*dsPb.Filter, devActionMeta *actionPb.DevActionMeta) (string, error) {
	if len(filters) == 0 {
		return "", errors.New("filter cannot be nil")
	}

	var (
		loanRequestIDsStr string
		vendorStr         string
		lrStatusStr       string
		lseStatusStr      string
	)

	for _, filter := range filters {
		switch filter.GetParameterName() {
		case loanRequestIds:
			loanRequestIDsStr = filter.GetStringValue()
		case vendor:
			vendorStr = strings.TrimSpace(filter.GetDropdownValue())
		case lrStatus:
			lrStatusStr = filter.GetDropdownValue()
		case lseStatus:
			lseStatusStr = filter.GetDropdownValue()
		default:
			logger.Error(ctx, "invalid filter", zap.String("filter", filter.String()))
			return "", status.Error(codes.InvalidArgument, "invalid filter")
		}
	}

	if loanRequestIDsStr == "" {
		return "", status.Error(codes.InvalidArgument, "loan request IDs cannot be empty")
	}
	if vendorStr == "" {
		return "", status.Error(codes.InvalidArgument, "vendor cannot be empty")
	}
	if lrStatusStr == "" {
		return "", status.Error(codes.InvalidArgument, "lr_status is mandatory and cannot be empty")
	}
	if lseStatusStr == "" {
		return "", status.Error(codes.InvalidArgument, "lse_status is mandatory and cannot be empty")
	}

	// Split comma separated loan request IDs
	loanRequestIDs := strings.Split(loanRequestIDsStr, ",")
	for i, id := range loanRequestIDs {
		loanRequestIDs[i] = strings.TrimSpace(id)
	}

	// Convert vendor string to enum
	vendorEnum, ok := preapprovedloan.Vendor_value[vendorStr]
	if !ok {
		return "", status.Error(codes.InvalidArgument, fmt.Sprintf("invalid vendor: %s", vendorStr))
	}

	// Parse lr_status, error if not valid
	lrStatusEnum, ok := preapprovedloan.LoanRequestStatus_value[lrStatusStr]
	if !ok {
		return "", status.Error(codes.InvalidArgument, fmt.Sprintf("invalid lr_status: %s", lrStatusStr))
	}
	// Parse lse_status, error if not valid
	lseStatusEnum, ok := preapprovedloan.LoanStepExecutionStatus_value[lseStatusStr]
	if !ok {
		return "", status.Error(codes.InvalidArgument, fmt.Sprintf("invalid lse_status: %s", lseStatusStr))
	}

	resp, err := u.preapprovedloanCxClient.ResetLoanRequest(ctx, &preapprovedloanCxPb.ResetLoanRequestRequest{
		LoanRequestIds: loanRequestIDs,
		Vendor:         preapprovedloan.Vendor(vendorEnum),
		LrStatus:       preapprovedloan.LoanRequestStatus(lrStatusEnum),
		LseStatus:      preapprovedloan.LoanStepExecutionStatus(lseStatusEnum),
	})
	if grpcErr := epifigrpc.RPCError(resp, err); grpcErr != nil {
		logger.Error(ctx, "error updating lr/lse status", zap.Error(grpcErr))
		return "", grpcErr
	}

	marshalOptions := protojson.MarshalOptions{}
	marshalOptions.UseEnumNumbers = false

	resJson, err := marshalOptions.Marshal(resp)
	if err != nil {
		logger.Error(ctx, "cannot marshal response to json", zap.Error(err))
		// since action has been done do not return error
		return "", nil
	}
	return string(resJson), nil
}
