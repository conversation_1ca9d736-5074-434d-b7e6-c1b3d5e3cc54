//nolint:all
package processor

import (
	"bytes"
	"context"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"io"
	"time"

	s3types "github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/epifi/be-common/pkg/aws/v2/s3"

	actionPb "github.com/epifi/gamma/api/cx/developer/actions"
	dsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/cx/config"
	cxLogger "github.com/epifi/gamma/cx/logger"
)

const (
	marketingCampaignUsersFile     = "Upload File"
	marketingCampaignUsersFileName = "Enter File Name"
	s3FileName                     = "mktg_campaign_user_details.csv"
)

type UploadMarketingCampaignUsersList struct {
	cxS3Client s3.S3Client
	cxConf     *config.Config
}

func NewUploadMarketingCampaignUsersList(cxS3Client s3.S3Client, cxConf *config.Config) *UploadMarketingCampaignUsersList {
	return &UploadMarketingCampaignUsersList{
		cxS3Client: cxS3Client,
		cxConf:     cxConf,
	}
}

func (u *UploadMarketingCampaignUsersList) FetchParamList(ctx context.Context, action actionPb.DeveloperActions, devActionMeta *actionPb.DevActionMeta) ([]*dsPb.ParameterMeta, error) {
	paramList := []*dsPb.ParameterMeta{
		{
			Name:            marketingCampaignUsersFileName,
			Label:           "Enter File Name",
			Type:            dsPb.ParameterDataType_STRING,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
		},
		{
			Name:            marketingCampaignUsersFile,
			Label:           "Upload File",
			Type:            dsPb.ParameterDataType_FILE,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
		},
	}

	return paramList, nil
}

func (u *UploadMarketingCampaignUsersList) ExecuteAction(ctx context.Context, action actionPb.DeveloperActions, filters []*dsPb.Filter, devActionMeta *actionPb.DevActionMeta) (string, error) {

	res := struct {
		Message string
	}{}

	var fileName string
	var usersFile *dsPb.File

	for _, filter := range filters {
		switch filter.GetParameterName() {
		case marketingCampaignUsersFileName:
			fileName = filter.GetStringValue()
		case marketingCampaignUsersFile:
			usersFile = filter.GetFile()
		default:
			cxLogger.Error(ctx, "invalid filter", zap.String("filter", filter.String()))
			return "", status.Error(codes.InvalidArgument, "invalid filter")
		}
	}

	if fileName == "" || usersFile == nil {
		cxLogger.Error(ctx, fmt.Sprintf("file name  %s, and file cannot be empty", fileName))
		return "", status.Error(codes.InvalidArgument, "file cannot be empty")
	}

	cxLogger.Debug(ctx, fmt.Sprintf("file content: %s", usersFile.GetContent()))

	err := parseCsv(ctx, bytes.NewReader(usersFile.GetContent()))
	if err != nil {
		cxLogger.Error(ctx, "error in parsing the csv", zap.Error(err))
		return "", status.Error(codes.Internal, err.Error())
	}

	timestampString := time.Now().Format("2006-01-02")
	s3FilePath := fmt.Sprintf("%s/%s/%s", u.cxConf.CxS3Config.MarketingCampaignUsersFolderName, timestampString, s3FileName)
	cxLogger.Debug(ctx, "uploading csv file to S3 bucket", zap.Any("s3path", s3FilePath))

	// upload file to s3
	s3Err := u.cxS3Client.Write(ctx, s3FilePath, usersFile.GetContent(), string(s3types.ObjectCannedACLBucketOwnerFullControl))
	if s3Err != nil {
		cxLogger.Error(ctx, fmt.Sprintf("error while uploading file to s3 bucket, fileName :%s", fileName), zap.Error(err))
		return "", status.Error(codes.Internal, "error while uploading file to s3 bucket")
	}

	res.Message = fmt.Sprintf(" %s CSV is successfully uploaded to S3 Bucket", fileName)
	resStr, err := json.Marshal(&res)
	if err != nil {
		// since action has been done do not return error
		return "", errors.Wrap(err, "CSV is successfully uploaded but there is error while marshaling Message")
	}

	return string(resStr), nil
}

func parseCsv(ctx context.Context, data io.Reader) error {

	reader := csv.NewReader(data)
	records, err := reader.ReadAll()
	if err != nil {
		return errors.Wrap(err, "error while reading csv with ReadAll")
	}

	headerOfFirstColumn := records[0][0]
	allowedHeaders := getAllowedHeaders()

	cxLogger.Debug(ctx, fmt.Sprintf("records: %s , allowedHeaders: %s , headerOfFirstColumn: %s", records, allowedHeaders, headerOfFirstColumn))

	ok := isPresent(allowedHeaders, headerOfFirstColumn)
	if !ok {
		return errors.New("invalid header name")
	}

	return nil
}

func getAllowedHeaders() []string {
	return []string{"ActorId", "UserId", "Address", "PhoneNumber", "AccountNumber"}
}

// isPresent checks if a string is present in a slice
func isPresent(s []string, str string) bool {

	for _, v := range s {
		if v == str {
			return true
		}
	}
	return false
}
