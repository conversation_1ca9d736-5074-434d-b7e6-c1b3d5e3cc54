package chart

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/analyser/enums"
	txnAggregatesPb "github.com/epifi/gamma/api/analyser/txnaggregates"
	authPb "github.com/epifi/gamma/api/auth"
	chartPb "github.com/epifi/gamma/api/cx/risk_ops/chart"
	employmentPb "github.com/epifi/gamma/api/employment"
	caseManagementPb "github.com/epifi/gamma/api/risk/case_management"
	reviewPb "github.com/epifi/gamma/api/risk/case_management/review"
	savingsPb "github.com/epifi/gamma/api/savings"
	tieringPb "github.com/epifi/gamma/api/tiering/pinot"
	webuiPb "github.com/epifi/gamma/api/typesv2/webui"
	"github.com/epifi/gamma/cx/config/genconf"
)

type Service struct {
	chartPb.UnimplementedChartServiceServer
	txnAggregateClient   txnAggregatesPb.TxnAggregatesClient
	caseManagementClient caseManagementPb.CaseManagementClient
	savingsClient        savingsPb.SavingsClient
	employmentClient     employmentPb.EmploymentClient
	authClient           authPb.AuthClient
	conf                 *genconf.Config
	eodBalanceClient     tieringPb.EODBalanceClient
}

func NewService(txnAggregateClient txnAggregatesPb.TxnAggregatesClient, caseManagementClient caseManagementPb.CaseManagementClient,
	savingsClient savingsPb.SavingsClient, employmentClient employmentPb.EmploymentClient, authClient authPb.AuthClient, conf *genconf.Config, eodBalanceClient tieringPb.EODBalanceClient) *Service {
	return &Service{
		txnAggregateClient:   txnAggregateClient,
		caseManagementClient: caseManagementClient,
		savingsClient:        savingsClient,
		employmentClient:     employmentClient,
		authClient:           authClient,
		conf:                 conf,
		eodBalanceClient:     eodBalanceClient,
	}
}

var _ chartPb.ChartServiceServer = &Service{}

const (
	CreditDebitGraphTitle            = "Lifetime Credit X Debit Trend"
	IncomeReferenceLineLabel         = "Average Income"
	MinIncomeReferenceLineLabel      = "Min Average Income"
	MaxIncomeReferenceLineLabel      = "Max Average Income"
	AfuAttempts                      = "AFU Attempts"
	MaxAfuCountLimit                 = 30
	EodBalanceChartTitle             = "Last 30 days EOD balance"
	Last30DaysAvgEodBalanceLineLabel = "Last 30 days avg EOD balance"
	Last60DaysAvgEodBalanceLineLabel = "Last 60 days avg EOD balance"
	_30dayTimeDuration               = 30 * 24 * time.Hour
	_60dayTimeDuration               = 60 * 24 * time.Hour
)

// nolint: funlen
func (s *Service) GetCharts(ctx context.Context, req *chartPb.GetChartsRequest) (*chartPb.GetChartsResponse, error) {
	if !s.conf.RiskConfig().EnableBackendDrivenCharts() {
		return &chartPb.GetChartsResponse{
			Status: rpcPb.StatusRecordNotFound(),
		}, nil
	}

	reviewDetailsRes, err := s.caseManagementClient.GetReviewDetails(ctx, &caseManagementPb.GetReviewDetailsRequest{
		CaseId: req.GetCaseId(),
		FieldMasks: []reviewPb.ReviewDetailsFieldMask{
			reviewPb.ReviewDetailsFieldMask_REVIEW_DETAILS_FIELD_MASK_CASE,
		},
	})
	if err = epifigrpc.RPCError(reviewDetailsRes, err); err != nil {
		logger.Error(ctx, "failed to fetch case details", zap.Error(err))
		return &chartPb.GetChartsResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	txnAggregatesReq, err := s.buildTxnAggregateReq(ctx, req, reviewDetailsRes.GetCase().GetActorId())
	if err != nil {
		logger.Error(ctx, "failed to build txn aggregates request", zap.Error(err))
		return &chartPb.GetChartsResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	resp, err := s.txnAggregateClient.GetTimeAggregatesV2(ctx, txnAggregatesReq)
	if err = epifigrpc.RPCError(resp, err); err != nil {
		logger.Error(ctx, "failed to fetch txn aggregates", zap.Error(err))
		return &chartPb.GetChartsResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	labelToAttemptCountMap, afuErr := s.getAfuAttemptsAnnotation(ctx, reviewDetailsRes.GetCase().GetActorId())
	if afuErr != nil {
		logger.Error(ctx, "failed to get number of afu attempts", zap.Error(afuErr))
		return &chartPb.GetChartsResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	incomeReferenceLines, err := s.getAverageIncomeReferenceLine(ctx, reviewDetailsRes.GetCase().GetActorId())
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "failed to fetch average income for given actor", zap.Error(err))
		return &chartPb.GetChartsResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	charts := s.buildChartsFromTimeAggregates(ctx, &BuildBarChartReq{
		Aggregates:           resp.GetAggregates(),
		LabelToAnnotationMap: labelToAttemptCountMap,
		IncomeReferenceLines: incomeReferenceLines,
	})

	if s.conf.EnableBalanceMetricsOnCaseManagement() {
		historicalAvgEodBalanceRes, err := s.getHistoricalAvgEodBalance(ctx, reviewDetailsRes.GetCase().GetActorId())
		if err != nil {
			return &chartPb.GetChartsResponse{
				Status: rpcPb.StatusInternalWithDebugMsg(err.Error()),
			}, nil
		}
		charts = append(charts, s.buildChartsFromEodBalanceAggregate(historicalAvgEodBalanceRes.LastThirtyDayEodBalanceHistory, float32(historicalAvgEodBalanceRes.ThirtyDayEodBalanceAvg), float32(historicalAvgEodBalanceRes.SixtyDayEodBalanceAvg)))
	}

	return &chartPb.GetChartsResponse{
		Status: rpcPb.StatusOk(),
		Charts: charts,
	}, nil
}

func (s *Service) buildTxnAggregateReq(ctx context.Context, req *chartPb.GetChartsRequest, actorId string) (*txnAggregatesPb.GetTimeAggregatesV2Request, error) {
	account, err := s.getSavingsAccount(ctx, actorId)
	if err != nil {
		return nil, fmt.Errorf("error while fetching savings account details, %w", err)
	}

	return &txnAggregatesPb.GetTimeAggregatesV2Request{
		ActorId:  actorId,
		FromTime: account.GetCreationInfo().GetFiCreationSucceededAt(),
		ToTime:   timestamp.Now(),
		Metrics: []*txnAggregatesPb.TxnMetric{
			{
				Type:   enums.AggregateType_AGGREGATE_TYPE_SUM,
				Column: txnAggregatesPb.TxnAggregationColumn_TXN_AGGREGATION_COLUMN_AMOUNT,
			},
			{
				Type:   enums.AggregateType_AGGREGATE_TYPE_COUNT,
				Column: txnAggregatesPb.TxnAggregationColumn_TXN_AGGREGATION_COLUMN_ALL,
			},
		},
		GroupByDimensions: []txnAggregatesPb.GroupByDimension{
			txnAggregatesPb.GroupByDimension_TIME_DIMENSION_MONTH,
			txnAggregatesPb.GroupByDimension_GROUP_BY_DIMENSION_ACCOUNTING_ENTRY,
		},
		AccountIds: &txnAggregatesPb.AccountIds{
			FiAccountIds: []string{account.GetId()},
		},
	}, nil
}

func (s *Service) getSavingsAccount(ctx context.Context, actorId string) (*savingsPb.Account, error) {
	resp, err := s.savingsClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
		Identifier: &savingsPb.GetAccountRequest_ActorId{
			ActorId: actorId,
		},
	})
	switch {
	case err != nil:
		return nil, fmt.Errorf("failed to fetch savings account %w", err)
	case resp.GetAccount() == nil:
		return nil, fmt.Errorf("account not found for actor id %w", epifierrors.ErrRecordNotFound)
	}

	return resp.GetAccount(), nil
}

func (s *Service) buildChartsFromEodBalanceAggregate(lastThirtyDayEodBalanceHistory []*savingsPb.BalanceByDate, thirtyDayEodBalanceAvg float32, sixtyDayEodBalanceAvg float32) *chartPb.Chart {

	chartDataPoints := []*chartPb.ChartDataPoint{}

	for _, eodBalance := range lastThirtyDayEodBalanceHistory {
		chartDataPoints = append(chartDataPoints, &chartPb.ChartDataPoint{
			LabelValue: &chartPb.ChartDataPoint_Date{
				Date: eodBalance.GetDate(),
			},
			Values: []*chartPb.SeriesValue{
				{
					Value: float32(eodBalance.GetEodBalance().GetUnits()),
				},
			},
		})
	}

	barChart := &chartPb.BarChart{
		DataPoints: chartDataPoints,
		ReferenceLines: []*chartPb.ReferenceLine{
			{
				Label: Last30DaysAvgEodBalanceLineLabel,
				Value: thirtyDayEodBalanceAvg,
			},
			{
				Label: Last60DaysAvgEodBalanceLineLabel,
				Value: sixtyDayEodBalanceAvg,
			},
		},
	}

	return &chartPb.Chart{
		Title: &chartPb.Title{
			Text: EodBalanceChartTitle,
		},
		ChartType: chartPb.ChartType_CHART_TYPE_BAR,
		ChartData: &chartPb.ChartData{
			Data: &chartPb.ChartData_BarChart{
				BarChart: barChart,
			},
		},
		XAxisDefaultZoomLevel: chartPb.XAxisDefaultZoomLevel_X_AXIS_DEFAULT_ZOOM_LEVEL_DAILY,
	}
}

func (s *Service) buildChartsFromTimeAggregates(ctx context.Context, req *BuildBarChartReq) []*chartPb.Chart {
	barChart := s.getBarChartFromTimeAggregates(ctx, req)

	return []*chartPb.Chart{
		{
			Title: &chartPb.Title{
				Text: CreditDebitGraphTitle,
			},
			ChartType: chartPb.ChartType_CHART_TYPE_BAR,
			ChartData: &chartPb.ChartData{
				Data: &chartPb.ChartData_BarChart{
					BarChart: barChart,
				},
			},
		},
	}
}

func (s *Service) getBarChartFromTimeAggregates(_ context.Context, req *BuildBarChartReq) *chartPb.BarChart {
	labelToSeriesValuesMap := map[string][]*chartPb.SeriesValue{}
	labelToDateValueMap := map[string]*date.Date{}
	labelToAnnotationMap := map[string][]*chartPb.DataPointAnnotation{}

	for _, aggregate := range req.Aggregates {
		label := getLabelForDate(aggregate.GetTimeDurationV2().GetDate())
		labelToDateValueMap[label] = getDateValue(aggregate.GetTimeDurationV2())

		seriesValue := getSeriesValuesForTimeAggregate(aggregate)
		labelToSeriesValuesMap[label] = append(labelToSeriesValuesMap[label], seriesValue)

		if req.LabelToAnnotationMap[label] != nil {
			labelToAnnotationMap[label] = append(labelToAnnotationMap[label], req.LabelToAnnotationMap[label])
		}
	}
	barChart := &chartPb.BarChart{
		DataPoints:     []*chartPb.ChartDataPoint{},
		ReferenceLines: req.IncomeReferenceLines,
	}

	for label, seriesValues := range labelToSeriesValuesMap {
		barChart.DataPoints = append(barChart.DataPoints, &chartPb.ChartDataPoint{
			Label: label,
			LabelValue: &chartPb.ChartDataPoint_Date{
				Date: labelToDateValueMap[label],
			},
			Values:      seriesValues,
			Annotations: labelToAnnotationMap[label],
		})
	}
	return barChart
}

func getDateValue(timeDuration *txnAggregatesPb.TimeDuration) *date.Date {
	if timeDuration.GetDate() != nil {
		timeDuration.GetDate().Day = 1 // Explicitly setting day as start of month as quick fix for a UI issue
	}
	return timeDuration.GetDate()
}

func getSeriesValuesForTimeAggregate(aggregate *txnAggregatesPb.Aggregate) *chartPb.SeriesValue {
	return &chartPb.SeriesValue{
		SeriesLabel: aggregate.GetAccountingEntry().String(),
		Value:       float32(aggregate.GetSumAmount().GetUnits()),
	}
}

func getLabelForDate(date *date.Date) string {
	return fmt.Sprintf("%d-%d", date.GetMonth(), date.GetYear())
}

// GetHeatMap returns a heatmap table for risk transaction analytics
func (s *Service) GetHeatMap(ctx context.Context, req *chartPb.GetHeatMapRequest) (*chartPb.GetHeatMapResponse, error) {
	if req == nil || req.GetCaseId() == "" {
		logger.Error(ctx, "invalid request: case_id is required")
		return &chartPb.GetHeatMapResponse{
			Status: rpcPb.StatusInvalidArgument(),
		}, nil
	}

	// Get the actor_id from the case
	caseDetails, err := s.caseManagementClient.GetReviewDetails(ctx, &caseManagementPb.GetReviewDetailsRequest{
		CaseId: req.GetCaseId(),
		FieldMasks: []reviewPb.ReviewDetailsFieldMask{
			reviewPb.ReviewDetailsFieldMask_REVIEW_DETAILS_FIELD_MASK_CASE,
		},
	})
	if err = epifigrpc.RPCError(caseDetails, err); err != nil {
		logger.Error(ctx, "failed to get case details for heatmap", zap.Error(err))
		return &chartPb.GetHeatMapResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	actorId := caseDetails.GetCase().GetActorId()
	if actorId == "" {
		logger.Error(ctx, "actor_id not found in case details")
		return &chartPb.GetHeatMapResponse{
			Status: rpcPb.StatusInvalidArgument(),
		}, nil
	}

	// Default to monthly aggregation if not specified (and for now we only support monthly view in UI)
	aggregateOption := req.GetAggregateOption()
	if aggregateOption == chartPb.AggregateOption_AGGREGATE_OPTION_UNSPECIFIED {
		aggregateOption = chartPb.AggregateOption_AGGREGATE_OPTION_MONTH_OVER_MONTH
	}

	// Convert chartPb.AggregateOption to caseManagementPb.AggregateOption
	caseManagementAggregateOption := caseManagementPb.AggregateOption_AGGREGATE_OPTION_MONTH_OVER_MONTH
	switch aggregateOption {
	case chartPb.AggregateOption_AGGREGATE_OPTION_WEEK_OVER_WEEK:
		caseManagementAggregateOption = caseManagementPb.AggregateOption_AGGREGATE_OPTION_WEEK_OVER_WEEK
	case chartPb.AggregateOption_AGGREGATE_OPTION_DAY_WISE:
		caseManagementAggregateOption = caseManagementPb.AggregateOption_AGGREGATE_OPTION_DAY_WISE
	}

	logger.Info(ctx, "GetHeatMap started",
		zap.String("case_id", req.GetCaseId()),
		zap.String("actor_id", actorId),
		zap.String("aggregate_option", aggregateOption.String()))

	// Call the risk transaction aggregated metrics service
	metricsReq := &caseManagementPb.GetRiskTransactionAggregatedMetricsRequest{
		ActorId:         actorId,
		AggregateOption: caseManagementAggregateOption,
	}
	if req.GetFilterAfterDate() != nil {
		metricsReq.FilterAfterDate = req.GetFilterAfterDate()
	}

	metricsResp, err := s.caseManagementClient.GetRiskTransactionAggregatedMetrics(ctx, metricsReq)
	if err = epifigrpc.RPCError(metricsResp, err); err != nil {
		logger.Error(ctx, "failed to get risk transaction aggregated metrics", zap.Error(err))
		return &chartPb.GetHeatMapResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	// Convert metrics to heatmap table (ensure last 12 months with zeros for missing periods)
	table, err := s.convertMetricsToHeatMapTable(ctx, metricsResp.GetMetrics())
	if err != nil {
		logger.Error(ctx, "failed to convert metrics to heatmap table", zap.Error(err))
		return &chartPb.GetHeatMapResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	logger.Info(ctx, "GetHeatMap completed successfully",
		zap.String("case_id", req.GetCaseId()),
		zap.String("actor_id", actorId),
		zap.Int("table_rows", len(table.GetTableRows())))

	return &chartPb.GetHeatMapResponse{
		Status: rpcPb.StatusOk(),
		Table:  table,
	}, nil
}

// convertMetricsToHeatMapTable converts risk transaction metrics to a heatmap table format
func (s *Service) convertMetricsToHeatMapTable(ctx context.Context, metrics *caseManagementPb.RiskTransactionAggregatedMetrics) (*webuiPb.Table, error) {
	if metrics == nil {
		return nil, errors.New("metrics cannot be nil")
	}

	// Build last 12 months window (oldest to newest) irrespective of missing data
	type monthCol struct {
		key   string
		label string
	}
	var last12 []monthCol
	now := time.Now()
	for i := 11; i >= 0; i-- {
		t := now.AddDate(0, -i, 0)
		key := fmt.Sprintf("%04d-%02d", t.Year(), t.Month()) // matches time_period_label from risk analytics (YYYY-MM)
		label := t.Format("Jan 2006")                        // display friendly label
		last12 = append(last12, monthCol{key: key, label: label})
	}

	// Create table headers
	headers := []*webuiPb.TableHeader{
		{
			Label:     "Metric",
			HeaderKey: "metric",
			IsVisible: true,
		},
	}

	// Add time period columns
	for _, col := range last12 {
		headers = append(headers, &webuiPb.TableHeader{
			Label:     col.label,
			HeaderKey: col.key,
			IsVisible: true,
		})
	}

	// Create data rows for each metric type
	rows := []*webuiPb.TableRow{}

	// Helper function to create a row for a metric
	createMetricRow := func(metricName string, data []*caseManagementPb.TimeAggregatedCount) *webuiPb.TableRow {
		headerKeyCellMap := make(map[string]*webuiPb.TableCell)

		// Set metric name
		headerKeyCellMap["metric"] = &webuiPb.TableCell{
			DataType: webuiPb.TableCell_DATA_TYPE_STRING,
			ValueV2: &webuiPb.TableCell_StringValue{
				StringValue: metricName,
			},
		}

		// Set values for each time period
		for _, col := range last12 {
			headerKeyCellMap[col.key] = &webuiPb.TableCell{
				DataType: webuiPb.TableCell_DATA_TYPE_STRING,
				ValueV2: &webuiPb.TableCell_StringValue{
					StringValue: "0", // Default value
				},
			}
		}

		// Fill in actual values
		for _, count := range data {
			if count.GetTimePeriodLabel() != "" {
				if _, ok := headerKeyCellMap[count.GetTimePeriodLabel()]; ok {
					headerKeyCellMap[count.GetTimePeriodLabel()] = &webuiPb.TableCell{
						DataType: webuiPb.TableCell_DATA_TYPE_STRING,
						ValueV2: &webuiPb.TableCell_StringValue{
							StringValue: fmt.Sprintf("%d", count.GetCount()),
						},
					}
				}
			}
		}

		return &webuiPb.TableRow{
			HeaderKeyCellMap: headerKeyCellMap,
		}
	}

	// Add rows for each metric type
	rows = append(
		rows,
		createMetricRow("Credit Counts", metrics.GetCreditCounts()),
		createMetricRow("Lifetime New P2P Senders", metrics.GetLifetimeP2PCreditCounterparties()),
		createMetricRow("Credits > 25k", metrics.GetCredits_25KPlus()))

	table := &webuiPb.Table{
		TableName:    "Risk Transaction Analytics Heatmap",
		TableHeaders: headers,
		TableRows:    rows,
	}

	logger.Info(ctx, "Successfully created heatmap table",
		zap.Int("header_count", len(headers)),
		zap.Int("row_count", len(rows)))

	return table, nil
}
