package chart

import (
	"context"
	"flag"
	"os"
	"reflect"
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"google.golang.org/genproto/googleapis/type/date"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"

	authPb "github.com/epifi/gamma/api/auth"
	afuPb "github.com/epifi/gamma/api/auth/afu"
	authPbMock "github.com/epifi/gamma/api/auth/mocks"
	chartPb "github.com/epifi/gamma/api/cx/risk_ops/chart"
	employmentPb "github.com/epifi/gamma/api/employment"
	employmentMocks "github.com/epifi/gamma/api/employment/mocks"
	"github.com/epifi/gamma/api/frontend/account/screening"
	caseManagementPb "github.com/epifi/gamma/api/risk/case_management"
	caseManagementMocks "github.com/epifi/gamma/api/risk/case_management/mocks"
	reviewPb "github.com/epifi/gamma/api/risk/case_management/review"
	savingsPb "github.com/epifi/gamma/api/savings"
	savingsMock "github.com/epifi/gamma/api/savings/mocks"
	tieringPb "github.com/epifi/gamma/api/tiering/pinot"
	mockTieringPinot "github.com/epifi/gamma/api/tiering/pinot/mocks"
	"github.com/epifi/gamma/api/typesv2"
	webuiPb "github.com/epifi/gamma/api/typesv2/webui"
	"github.com/epifi/gamma/cx/test"
)

var (
	actorId = "actorId"
)

func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	//nolint:dogsled
	_, _, _, teardown = test.InitTestServer(false)

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

func TestService_getAverageSalaryReferenceLine(t *testing.T) {
	t.Parallel()
	ctx := context.Background()
	ctr := gomock.NewController(t)
	employmentClientMock := employmentMocks.NewMockEmploymentClient(ctr)
	type args struct {
		ctx     context.Context
		actorId string
	}
	tests := []struct {
		name     string
		args     args
		mockFunc func(employmentClientMock *employmentMocks.MockEmploymentClient)
		want     []*chartPb.ReferenceLine
		wantErr  bool
	}{
		{
			name: "fails with some random error",
			args: args{
				ctx:     ctx,
				actorId: actorId,
			},
			mockFunc: func(employmentClientMock *employmentMocks.MockEmploymentClient) {
				employmentClientMock.EXPECT().GetEmploymentInfo(ctx, &employmentPb.GetEmploymentInfoRequest{ActorId: actorId}).
					Return(&employmentPb.GetEmploymentInfoResponse{Status: rpcPb.StatusInternal()}, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "success with average salary reference line",
			args: args{
				ctx:     ctx,
				actorId: actorId,
			},
			mockFunc: func(employmentClientMock *employmentMocks.MockEmploymentClient) {
				employmentClientMock.EXPECT().GetEmploymentInfo(ctx, &employmentPb.GetEmploymentInfoRequest{ActorId: actorId}).Return(&employmentPb.GetEmploymentInfoResponse{
					Status: rpcPb.StatusOk(),
					EmploymentData: &employmentPb.EmploymentData{
						EmploymentInfo: &employmentPb.EmploymentInfo{
							AnnualSalary: &screening.AnnualSalary{
								Absolute: 320000,
							},
						},
					},
				}, nil)
			},
			want: []*chartPb.ReferenceLine{{
				Label: IncomeReferenceLineLabel,
				Value: 26666.666,
			}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				employmentClient: employmentClientMock,
			}
			if tt.mockFunc != nil {
				tt.mockFunc(employmentClientMock)
			}
			got, err := s.getAverageIncomeReferenceLine(tt.args.ctx, tt.args.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("getAverageSalaryReferenceLine() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getAverageSalaryReferenceLine() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_getAfuAttemptsAnnotation(t *testing.T) {
	t.Parallel()
	ctx := context.Background()
	ctr := gomock.NewController(t)
	authClientMock := authPbMock.NewMockAuthClient(ctr)
	type args struct {
		ctx     context.Context
		actorId string
	}
	tests := []struct {
		name     string
		args     args
		mockFunc func(authClientMock *authPbMock.MockAuthClient)
		want     map[string]*chartPb.DataPointAnnotation
		wantErr  bool
	}{
		{
			name: "fails with internal server error",
			args: args{
				ctx:     ctx,
				actorId: actorId,
			},
			mockFunc: func(authClientMock *authPbMock.MockAuthClient) {
				authClientMock.EXPECT().GetAuthFactorUpdatesForActor(ctx, &authPb.GetAuthFactorUpdatesForActorRequest{ActorId: actorId, OverallStatus: afuPb.OverallStatus_OVERALL_STATUS_COMPLETED, Count: MaxAfuCountLimit}).
					Return(&authPb.GetAuthFactorUpdatesForActorResponse{
						Status: rpcPb.StatusInternal(),
					}, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "success with afu attempts in one month",
			args: args{
				ctx:     ctx,
				actorId: actorId,
			},
			mockFunc: func(authClientMock *authPbMock.MockAuthClient) {
				authClientMock.EXPECT().GetAuthFactorUpdatesForActor(ctx, &authPb.GetAuthFactorUpdatesForActorRequest{
					ActorId:       actorId,
					OverallStatus: afuPb.OverallStatus_OVERALL_STATUS_COMPLETED,
					Count:         MaxAfuCountLimit,
				}).Return(&authPb.GetAuthFactorUpdatesForActorResponse{
					Status: rpcPb.StatusOk(),
					AuthFactorUpdates: []*afuPb.AuthFactorUpdate{
						{CreatedAt: timestamp.New(time.Unix(1714521600, 0)), OverallStatus: afuPb.OverallStatus_OVERALL_STATUS_COMPLETED},
						{CreatedAt: timestamp.New(time.Unix(1714525600, 0)), OverallStatus: afuPb.OverallStatus_OVERALL_STATUS_COMPLETED},
						{CreatedAt: timestamp.New(time.Unix(1714525600, 0)), OverallStatus: afuPb.OverallStatus_OVERALL_STATUS_IN_PROGRESS},
					},
				}, nil)
			},
			want: map[string]*chartPb.DataPointAnnotation{
				"5-2024": &chartPb.DataPointAnnotation{
					Label: AfuAttempts,
					Value: 2,
				},
			},
			wantErr: false,
		},
		{
			name: "success with multiple afu attempts in multiple months",
			args: args{
				ctx:     ctx,
				actorId: actorId,
			},
			mockFunc: func(authClientMock *authPbMock.MockAuthClient) {
				authClientMock.EXPECT().GetAuthFactorUpdatesForActor(ctx, &authPb.GetAuthFactorUpdatesForActorRequest{
					ActorId:       actorId,
					OverallStatus: afuPb.OverallStatus_OVERALL_STATUS_COMPLETED,
					Count:         MaxAfuCountLimit,
				}).Return(&authPb.GetAuthFactorUpdatesForActorResponse{
					Status: rpcPb.StatusOk(),
					AuthFactorUpdates: []*afuPb.AuthFactorUpdate{
						{CreatedAt: timestamp.New(time.Unix(1714521600, 0)), OverallStatus: afuPb.OverallStatus_OVERALL_STATUS_COMPLETED},
						{CreatedAt: timestamp.New(time.Unix(1714525600, 0)), OverallStatus: afuPb.OverallStatus_OVERALL_STATUS_COMPLETED},
						{CreatedAt: timestamp.New(time.Unix(1719792000, 0)), OverallStatus: afuPb.OverallStatus_OVERALL_STATUS_COMPLETED},
						{CreatedAt: timestamp.New(time.Unix(1722470400, 0)), OverallStatus: afuPb.OverallStatus_OVERALL_STATUS_COMPLETED},
						{CreatedAt: timestamp.New(time.Unix(1722470400, 0)), OverallStatus: afuPb.OverallStatus_OVERALL_STATUS_FAILED},
					},
				}, nil)
			},
			want: map[string]*chartPb.DataPointAnnotation{
				"5-2024": &chartPb.DataPointAnnotation{
					Label: AfuAttempts,
					Value: 2,
				},
				"7-2024": &chartPb.DataPointAnnotation{
					Label: AfuAttempts,
					Value: 1,
				},
				"8-2024": &chartPb.DataPointAnnotation{
					Label: AfuAttempts,
					Value: 1,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				authClient: authClientMock,
			}
			if tt.mockFunc != nil {
				tt.mockFunc(authClientMock)
			}
			got, err := s.getAfuAttemptsAnnotation(tt.args.ctx, tt.args.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("getAfuAttemptsAnnotation() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getAfuAttemptsAnnotation() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_getHistoricalAvgEodBalance(t *testing.T) {
	t.Parallel()

	var (
		ctx           = context.Background()
		actorId1      = "actorId1"
		balanceByDate = []*savingsPb.BalanceByDate{
			{
				Date: &date.Date{
					Year:  2024,
					Month: 10,
					Day:   1,
				},
				EodBalance: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        10,
				},
			},
		}
	)

	type args struct {
		ctx     context.Context
		actorId string
	}
	tests := []struct {
		name    string
		args    args
		mock    func(mockSavingsClient *savingsMock.MockSavingsClient, mockEODBalanceClient *mockTieringPinot.MockEODBalanceClient)
		want    *HistoricalAvgEodBalanceRes
		wantErr bool
	}{
		{
			name: "success",
			args: args{
				ctx:     ctx,
				actorId: actorId1,
			},
			mock: func(mockSavingsClient *savingsMock.MockSavingsClient, mockEODBalanceClient *mockTieringPinot.MockEODBalanceClient) {
				mockEODBalanceClient.EXPECT().GetAverageEODBalanceInDateRangeDayWise(gomock.Any(), gomock.Any()).Return(&tieringPb.GetAverageEODBalanceInDateRangeDayWiseResponse{
					Status: rpcPb.StatusOk(),
					DayWiseEodBalance: []*tieringPb.GetAverageEODBalanceInDateRangeDayWiseResponse_EODBalanceByDate{
						{
							Balance: 10,
							Date: &typesv2.Date{
								Year:  2024,
								Month: 10,
								Day:   1,
							},
						},
					},
				}, nil)
				mockEODBalanceClient.EXPECT().GetAverageEODBalanceInDateRange(gomock.Any(), gomock.Any()).Return(&tieringPb.GetAverageEODBalanceInDateRangeResponse{
					Status:     rpcPb.StatusOk(),
					AvgBalance: 10,
				}, nil)
				mockEODBalanceClient.EXPECT().GetAverageEODBalanceInDateRange(gomock.Any(), gomock.Any()).Return(&tieringPb.GetAverageEODBalanceInDateRangeResponse{
					Status:     rpcPb.StatusOk(),
					AvgBalance: 10,
				}, nil)
			},
			want: &HistoricalAvgEodBalanceRes{
				LastThirtyDayEodBalanceHistory: balanceByDate,
				SixtyDayEodBalanceAvg:          10,
				ThirtyDayEodBalanceAvg:         10,
			},
			wantErr: false,
		},
		{
			name: "should fail when GetAverageEODBalanceInDateRangeDayWise call fails",
			args: args{
				ctx:     ctx,
				actorId: actorId1,
			},
			mock: func(mockSavingsClient *savingsMock.MockSavingsClient, mockEODBalanceClient *mockTieringPinot.MockEODBalanceClient) {
				mockEODBalanceClient.EXPECT().GetAverageEODBalanceInDateRangeDayWise(gomock.Any(), gomock.Any()).Return(&tieringPb.GetAverageEODBalanceInDateRangeDayWiseResponse{
					Status: rpcPb.StatusInternal(),
				}, nil)
				mockEODBalanceClient.EXPECT().GetAverageEODBalanceInDateRange(gomock.Any(), gomock.Any()).Return(&tieringPb.GetAverageEODBalanceInDateRangeResponse{
					Status:     rpcPb.StatusOk(),
					AvgBalance: 10,
				}, nil)
				mockEODBalanceClient.EXPECT().GetAverageEODBalanceInDateRange(gomock.Any(), gomock.Any()).Return(&tieringPb.GetAverageEODBalanceInDateRangeResponse{
					Status:     rpcPb.StatusOk(),
					AvgBalance: 10,
				}, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should fail when GetAverageEODBalanceInDateRange call fails",
			args: args{
				ctx:     ctx,
				actorId: actorId1,
			},
			mock: func(mockSavingsClient *savingsMock.MockSavingsClient, mockEODBalanceClient *mockTieringPinot.MockEODBalanceClient) {
				mockEODBalanceClient.EXPECT().GetAverageEODBalanceInDateRangeDayWise(gomock.Any(), gomock.Any()).Return(&tieringPb.GetAverageEODBalanceInDateRangeDayWiseResponse{
					Status: rpcPb.StatusOk(),
					DayWiseEodBalance: []*tieringPb.GetAverageEODBalanceInDateRangeDayWiseResponse_EODBalanceByDate{
						{
							Balance: 10,
							Date: &typesv2.Date{
								Year:  2024,
								Month: 10,
								Day:   1,
							},
						},
					},
				}, nil)
				mockEODBalanceClient.EXPECT().GetAverageEODBalanceInDateRange(gomock.Any(), gomock.Any()).Return(&tieringPb.GetAverageEODBalanceInDateRangeResponse{
					Status: rpcPb.StatusInternal(),
				}, nil)
				mockEODBalanceClient.EXPECT().GetAverageEODBalanceInDateRange(gomock.Any(), gomock.Any()).Return(&tieringPb.GetAverageEODBalanceInDateRangeResponse{
					Status:     rpcPb.StatusOk(),
					AvgBalance: 10,
				}, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should success when GetAverageEODBalanceInDateRangeDayWise gives record not found",
			args: args{
				ctx:     ctx,
				actorId: actorId1,
			},
			mock: func(mockSavingsClient *savingsMock.MockSavingsClient, mockEODBalanceClient *mockTieringPinot.MockEODBalanceClient) {
				mockEODBalanceClient.EXPECT().GetAverageEODBalanceInDateRangeDayWise(gomock.Any(), gomock.Any()).Return(&tieringPb.GetAverageEODBalanceInDateRangeDayWiseResponse{
					Status: rpcPb.StatusRecordNotFound(),
				}, nil)
				mockEODBalanceClient.EXPECT().GetAverageEODBalanceInDateRange(gomock.Any(), gomock.Any()).Return(&tieringPb.GetAverageEODBalanceInDateRangeResponse{
					Status:     rpcPb.StatusOk(),
					AvgBalance: 10,
				}, nil)
				mockEODBalanceClient.EXPECT().GetAverageEODBalanceInDateRange(gomock.Any(), gomock.Any()).Return(&tieringPb.GetAverageEODBalanceInDateRangeResponse{
					Status:     rpcPb.StatusOk(),
					AvgBalance: 10,
				}, nil)
			},
			want: &HistoricalAvgEodBalanceRes{
				SixtyDayEodBalanceAvg:  10,
				ThirtyDayEodBalanceAvg: 10,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {

		ctr := gomock.NewController(t)
		mockSavingsClient := savingsMock.NewMockSavingsClient(ctr)
		mockEODBalanceClient := mockTieringPinot.NewMockEODBalanceClient(ctr)

		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				savingsClient:    mockSavingsClient,
				eodBalanceClient: mockEODBalanceClient,
			}
			tt.mock(mockSavingsClient, mockEODBalanceClient)
			got, err := s.getHistoricalAvgEodBalance(tt.args.ctx, tt.args.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("getHistoricalAvgEodBalance() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getHistoricalAvgEodBalance() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetHeatMap(t *testing.T) {
	t.Parallel()
	ctx := context.Background()

	tests := []struct {
		name           string
		req            *chartPb.GetHeatMapRequest
		mockSetup      func(*caseManagementMocks.MockCaseManagementClient)
		expectedStatus *rpcPb.Status
		expectedError  bool
		validate       func(*testing.T, *chartPb.GetHeatMapResponse)
	}{
		{
			name: "success with monthly aggregation",
			req: &chartPb.GetHeatMapRequest{
				CaseId:          "test-case-123",
				AggregateOption: chartPb.AggregateOption_AGGREGATE_OPTION_MONTH_OVER_MONTH,
			},
			mockSetup: func(mockClient *caseManagementMocks.MockCaseManagementClient) {
				// Mock GetReviewDetails call
				mockClient.EXPECT().GetReviewDetails(ctx, &caseManagementPb.GetReviewDetailsRequest{
					CaseId: "test-case-123",
					FieldMasks: []reviewPb.ReviewDetailsFieldMask{
						reviewPb.ReviewDetailsFieldMask_REVIEW_DETAILS_FIELD_MASK_CASE,
					},
				}).Return(&caseManagementPb.GetReviewDetailsResponse{
					Status: rpcPb.StatusOk(),
					Case: &reviewPb.Case{
						ActorId: "test-actor-123",
					},
				}, nil)

				// Use current month for testing to ensure it appears in the last 12 months
				now := time.Now()
				currentMonth := now.Format("2006-01")
				previousMonth := now.AddDate(0, -1, 0).Format("2006-01")

				// Mock GetRiskTransactionAggregatedMetrics call
				mockClient.EXPECT().GetRiskTransactionAggregatedMetrics(ctx, &caseManagementPb.GetRiskTransactionAggregatedMetricsRequest{
					ActorId:         "test-actor-123",
					AggregateOption: caseManagementPb.AggregateOption_AGGREGATE_OPTION_MONTH_OVER_MONTH,
				}).Return(&caseManagementPb.GetRiskTransactionAggregatedMetricsResponse{
					Status: rpcPb.StatusOk(),
					Metrics: &caseManagementPb.RiskTransactionAggregatedMetrics{
						CreditCounts: []*caseManagementPb.TimeAggregatedCount{
							{
								Count:           15,
								TimePeriodLabel: currentMonth,
								Time: &caseManagementPb.TimeAggregatedCount_MonthKey{
									MonthKey: &caseManagementPb.MonthKey{
										Year:  int32(now.Year()),
										Month: int32(now.Month()),
									},
								},
							},
							{
								Count:           22,
								TimePeriodLabel: previousMonth,
								Time: &caseManagementPb.TimeAggregatedCount_MonthKey{
									MonthKey: &caseManagementPb.MonthKey{
										Year:  int32(now.AddDate(0, -1, 0).Year()),
										Month: int32(now.AddDate(0, -1, 0).Month()),
									},
								},
							},
						},
						LifetimeP2PCreditCounterparties: []*caseManagementPb.TimeAggregatedCount{
							{
								Count:           3,
								TimePeriodLabel: currentMonth,
								Time: &caseManagementPb.TimeAggregatedCount_MonthKey{
									MonthKey: &caseManagementPb.MonthKey{
										Year:  int32(now.Year()),
										Month: int32(now.Month()),
									},
								},
							},
						},
						Credits_25KPlus: []*caseManagementPb.TimeAggregatedCount{
							{
								Count:           2,
								TimePeriodLabel: currentMonth,
								Time: &caseManagementPb.TimeAggregatedCount_MonthKey{
									MonthKey: &caseManagementPb.MonthKey{
										Year:  int32(now.Year()),
										Month: int32(now.Month()),
									},
								},
							},
						},
						AggregationType: caseManagementPb.AggregateOption_AGGREGATE_OPTION_MONTH_OVER_MONTH,
					},
				}, nil)
			},
			expectedStatus: rpcPb.StatusOk(),
			expectedError:  false,
			validate: func(t *testing.T, resp *chartPb.GetHeatMapResponse) {
				assert.NotNil(t, resp.GetTable())
				assert.Equal(t, "Risk Transaction Analytics Heatmap", resp.GetTable().GetTableName())

				// Should have 13 headers (1 metric name + 12 months)
				assert.Len(t, resp.GetTable().GetTableHeaders(), 13)

				// First header should be "Metric"
				assert.Equal(t, "Metric", resp.GetTable().GetTableHeaders()[0].GetLabel())
				assert.Equal(t, "metric", resp.GetTable().GetTableHeaders()[0].GetHeaderKey())

				// Should have 3 rows (one for each metric type)
				assert.Len(t, resp.GetTable().GetTableRows(), 3)

				// Validate first row (Credit Counts)
				firstRow := resp.GetTable().GetTableRows()[0]
				metricCell := firstRow.GetHeaderKeyCellMap()["metric"]
				assert.Equal(t, "Credit Counts", metricCell.GetStringValue())

				// Validate that data is populated correctly for current month
				now := time.Now()
				currentMonth := now.Format("2006-01")
				previousMonth := now.AddDate(0, -1, 0).Format("2006-01")

				if currentMonthCell, exists := firstRow.GetHeaderKeyCellMap()[currentMonth]; exists {
					assert.Equal(t, "15", currentMonthCell.GetStringValue())
				}
				if previousMonthCell, exists := firstRow.GetHeaderKeyCellMap()[previousMonth]; exists {
					assert.Equal(t, "22", previousMonthCell.GetStringValue())
				}

				// Validate second row (P2P Counterparties)
				secondRow := resp.GetTable().GetTableRows()[1]
				p2pMetricCell := secondRow.GetHeaderKeyCellMap()["metric"]
				assert.Equal(t, "Lifetime New P2P Senders", p2pMetricCell.GetStringValue())

				// Validate third row (Credits > 25k)
				thirdRow := resp.GetTable().GetTableRows()[2]
				credits25kMetricCell := thirdRow.GetHeaderKeyCellMap()["metric"]
				assert.Equal(t, "Credits > 25k", credits25kMetricCell.GetStringValue())
			},
		},
		{
			name: "success with weekly aggregation",
			req: &chartPb.GetHeatMapRequest{
				CaseId:          "test-case-456",
				AggregateOption: chartPb.AggregateOption_AGGREGATE_OPTION_WEEK_OVER_WEEK,
			},
			mockSetup: func(mockClient *caseManagementMocks.MockCaseManagementClient) {
				mockClient.EXPECT().GetReviewDetails(ctx, gomock.Any()).Return(&caseManagementPb.GetReviewDetailsResponse{
					Status: rpcPb.StatusOk(),
					Case: &reviewPb.Case{
						ActorId: "test-actor-456",
					},
				}, nil)

				mockClient.EXPECT().GetRiskTransactionAggregatedMetrics(ctx, &caseManagementPb.GetRiskTransactionAggregatedMetricsRequest{
					ActorId:         "test-actor-456",
					AggregateOption: caseManagementPb.AggregateOption_AGGREGATE_OPTION_WEEK_OVER_WEEK,
				}).Return(&caseManagementPb.GetRiskTransactionAggregatedMetricsResponse{
					Status: rpcPb.StatusOk(),
					Metrics: &caseManagementPb.RiskTransactionAggregatedMetrics{
						CreditCounts:                    []*caseManagementPb.TimeAggregatedCount{},
						LifetimeP2PCreditCounterparties: []*caseManagementPb.TimeAggregatedCount{},
						Credits_25KPlus:                 []*caseManagementPb.TimeAggregatedCount{},
						AggregationType:                 caseManagementPb.AggregateOption_AGGREGATE_OPTION_WEEK_OVER_WEEK,
					},
				}, nil)
			},
			expectedStatus: rpcPb.StatusOk(),
			expectedError:  false,
			validate: func(t *testing.T, resp *chartPb.GetHeatMapResponse) {
				assert.NotNil(t, resp.GetTable())
				assert.Len(t, resp.GetTable().GetTableRows(), 3)

				// All cells should have default "0" values since no data was provided
				for _, row := range resp.GetTable().GetTableRows() {
					for headerKey, cell := range row.GetHeaderKeyCellMap() {
						if headerKey != "metric" {
							assert.Equal(t, "0", cell.GetStringValue())
						}
					}
				}
			},
		},
		{
			name: "failure with empty case_id",
			req: &chartPb.GetHeatMapRequest{
				CaseId:          "",
				AggregateOption: chartPb.AggregateOption_AGGREGATE_OPTION_MONTH_OVER_MONTH,
			},
			mockSetup: func(mockClient *caseManagementMocks.MockCaseManagementClient) {
				// No mock calls expected
			},
			expectedStatus: rpcPb.StatusInvalidArgument(),
			expectedError:  false,
			validate: func(t *testing.T, resp *chartPb.GetHeatMapResponse) {
				assert.Nil(t, resp.GetTable())
			},
		},
		{
			name: "failure when GetReviewDetails fails",
			req: &chartPb.GetHeatMapRequest{
				CaseId:          "invalid-case-123",
				AggregateOption: chartPb.AggregateOption_AGGREGATE_OPTION_MONTH_OVER_MONTH,
			},
			mockSetup: func(mockClient *caseManagementMocks.MockCaseManagementClient) {
				mockClient.EXPECT().GetReviewDetails(ctx, gomock.Any()).Return(&caseManagementPb.GetReviewDetailsResponse{
					Status: rpcPb.StatusRecordNotFound(),
				}, nil)
			},
			expectedStatus: rpcPb.StatusInternal(),
			expectedError:  false,
			validate: func(t *testing.T, resp *chartPb.GetHeatMapResponse) {
				assert.Nil(t, resp.GetTable())
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockCaseManagementClient := caseManagementMocks.NewMockCaseManagementClient(ctrl)
			tt.mockSetup(mockCaseManagementClient)

			service := &Service{
				caseManagementClient: mockCaseManagementClient,
			}

			resp, err := service.GetHeatMap(ctx, tt.req)

			if tt.expectedError {
				assert.Error(t, err)
				return
			}

			require.NoError(t, err)
			assert.NotNil(t, resp)
			assert.Equal(t, tt.expectedStatus.Code, resp.GetStatus().Code)

			if tt.validate != nil {
				tt.validate(t, resp)
			}
		})
	}
}

// TestService_convertMetricsToHeatMapTable tests the helper function for converting metrics to table
func TestService_convertMetricsToHeatMapTable(t *testing.T) {
	t.Parallel()
	ctx := context.Background()
	service := &Service{}

	tests := []struct {
		name     string
		metrics  *caseManagementPb.RiskTransactionAggregatedMetrics
		validate func(*testing.T, *webuiPb.Table, error)
	}{
		{
			name: "success with mixed data across months",
			metrics: func() *caseManagementPb.RiskTransactionAggregatedMetrics {
				now := time.Now()
				currentMonth := now.Format("2006-01")
				twoMonthsAgo := now.AddDate(0, -2, 0).Format("2006-01")
				threeMonthsAgo := now.AddDate(0, -3, 0).Format("2006-01")

				return &caseManagementPb.RiskTransactionAggregatedMetrics{
					CreditCounts: []*caseManagementPb.TimeAggregatedCount{
						{
							Count:           15,
							TimePeriodLabel: currentMonth,
						},
						{
							Count:           22,
							TimePeriodLabel: threeMonthsAgo, // Skip intermediate months to test sparse data
						},
					},
					LifetimeP2PCreditCounterparties: []*caseManagementPb.TimeAggregatedCount{
						{
							Count:           3,
							TimePeriodLabel: twoMonthsAgo, // Only this month has data
						},
					},
					Credits_25KPlus: []*caseManagementPb.TimeAggregatedCount{
						{
							Count:           1,
							TimePeriodLabel: currentMonth,
						},
						{
							Count:           5,
							TimePeriodLabel: twoMonthsAgo,
						},
					},
				}
			}(),
			validate: func(t *testing.T, table *webuiPb.Table, err error) {
				require.NoError(t, err)
				assert.NotNil(t, table)
				assert.Equal(t, "Risk Transaction Analytics Heatmap", table.GetTableName())

				// Should have 13 headers (1 metric + 12 months)
				assert.Len(t, table.GetTableHeaders(), 13)

				// Should have 3 rows
				assert.Len(t, table.GetTableRows(), 3)

				// Test Credit Counts row
				creditRow := table.GetTableRows()[0]
				assert.Equal(t, "Credit Counts", creditRow.GetHeaderKeyCellMap()["metric"].GetStringValue())

				// Check that the specific data is populated for the right time periods
				foundCredits15 := false
				foundCredits22 := false
				for _, cell := range creditRow.GetHeaderKeyCellMap() {
					if cell.GetStringValue() == "15" {
						foundCredits15 = true
					}
					if cell.GetStringValue() == "22" {
						foundCredits22 = true
					}
				}
				assert.True(t, foundCredits15, "Should find credit count of 15 somewhere in the table")
				assert.True(t, foundCredits22, "Should find credit count of 22 somewhere in the table")

				// Test P2P row
				p2pRow := table.GetTableRows()[1]
				assert.Equal(t, "Lifetime New P2P Senders", p2pRow.GetHeaderKeyCellMap()["metric"].GetStringValue())

				// Test 25k+ row
				credits25kRow := table.GetTableRows()[2]
				assert.Equal(t, "Credits > 25k", credits25kRow.GetHeaderKeyCellMap()["metric"].GetStringValue())
			},
		},
		{
			name: "success with empty metrics",
			metrics: &caseManagementPb.RiskTransactionAggregatedMetrics{
				CreditCounts:                    []*caseManagementPb.TimeAggregatedCount{},
				LifetimeP2PCreditCounterparties: []*caseManagementPb.TimeAggregatedCount{},
				Credits_25KPlus:                 []*caseManagementPb.TimeAggregatedCount{},
			},
			validate: func(t *testing.T, table *webuiPb.Table, err error) {
				require.NoError(t, err)
				assert.NotNil(t, table)

				// All cells should have "0" values
				for _, row := range table.GetTableRows() {
					for headerKey, cell := range row.GetHeaderKeyCellMap() {
						if headerKey != "metric" {
							assert.Equal(t, "0", cell.GetStringValue())
						}
					}
				}
			},
		},
		{
			name:    "failure with nil metrics",
			metrics: nil,
			validate: func(t *testing.T, table *webuiPb.Table, err error) {
				require.Error(t, err)
				assert.Nil(t, table)
				assert.Contains(t, err.Error(), "metrics cannot be nil")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			table, err := service.convertMetricsToHeatMapTable(ctx, tt.metrics)
			tt.validate(t, table, err)
		})
	}
}
