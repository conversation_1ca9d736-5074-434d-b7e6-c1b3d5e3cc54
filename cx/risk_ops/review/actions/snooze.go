package actions

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	actionPb "github.com/epifi/gamma/api/cx/developer/actions"
	dsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/cx/risk_ops"
	cmPb "github.com/epifi/gamma/api/risk/case_management"
	reviewPb "github.com/epifi/gamma/api/risk/case_management/review"
	onboardingPb "github.com/epifi/gamma/api/user/onboarding"
	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/gamma/cx/risk_ops/review/annotations"
	"github.com/epifi/gamma/cx/risk_ops/review/comments"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	goUtils "github.com/epifi/be-common/pkg/go_utils"
)

const (
	snoozeReasonLabel                   = "Reason for Snooze"
	snoozeTill                          = "SnoozeTill"
	snoozeTillLabel                     = "Snooze duration in hours(1-720)"
	snoozeComments                      = "SnoozeComments"
	snoozeCommentsLabel                 = "Snooze Comments"
	sendReminderCommsInOutcallFlow      = "sendReminderCommsInOutcallFlow"
	sendReminderCommsInOutcallFlowLabel = "Send reminder comms to user?"
	maxSnoozeHours                      = 720
)

type Snooze struct {
	caseManagementClient cmPb.CaseManagementClient
	onboardingClient     onboardingPb.OnboardingClient
	commentsHelper       comments.ICommentsHelper
	annotationsHelper    annotations.IAnnotationsHelper
}

func (s *Snooze) FetchFormFields(ctx context.Context, actionInput *ActionInput) ([]*risk_ops.FormField, error) {
	// TODO implement me
	return nil, epifierrors.ErrUnimplemented
}

func NewSnooze(caseManagementClient cmPb.CaseManagementClient, onboardingClient onboardingPb.OnboardingClient,
	commentsHelper comments.ICommentsHelper, annotationsHelper annotations.IAnnotationsHelper) *Snooze {
	return &Snooze{
		caseManagementClient: caseManagementClient,
		onboardingClient:     onboardingClient,
		commentsHelper:       commentsHelper,
		annotationsHelper:    annotationsHelper,
	}
}

type SnoozeParameters struct {
	snoozeTill                     *timestampPb.Timestamp
	annotations                    []*reviewPb.AllowedAnnotation
	snoozeComment                  string
	analystEmail                   string
	sendReminderCommsInOutcallFlow bool
}

func (s *Snooze) IsPermitted(ctx context.Context, actionInput *ActionInput) (bool, error) {
	return actionInput.GetCaseDetails().GetReviewType() != reviewPb.ReviewType_REVIEW_TYPE_ESCALATION_REVIEW, nil
}

func (s *Snooze) Perform(ctx context.Context, actionInput *ActionInput, actionParameters []*dsPb.Filter) ([]*actionPb.ActionResponse, error) {
	snoozeParameters, err := s.getSnoozeParameters(ctx, actionInput, actionParameters)
	if err != nil {
		return nil, fmt.Errorf("failed to parse snooze parameters %w", err)
	}

	// Add snooze comments to case
	err = s.commentsHelper.AddCommentForCase(ctx, actionInput.GetCaseDetails().GetId(), reviewPb.CaseCommentType_CASE_COMMENT_TYPE_SNOOZE,
		snoozeParameters.snoozeComment, snoozeParameters.analystEmail)
	if err != nil {
		return nil, fmt.Errorf("error while adding snooze comments for the case, %w", err)
	}

	// Add reason as annotations for case
	err = s.annotationsHelper.CreateAnnotations(ctx, snoozeParameters.annotations,
		actionInput.GetCaseDetails(), snoozeParameters.analystEmail)
	if err != nil {
		return nil, fmt.Errorf("error while adding snooze annotations for the case, %w", err)
	}

	performActionReq, err := s.buildPerformActionRequest(actionInput.GetCaseDetails(), snoozeParameters)
	if err != nil {
		return nil, fmt.Errorf("build perform action request failed for snooze. case id: %s", actionInput.GetCaseDetails().GetId())
	}
	resp, err := s.caseManagementClient.PerformAction(ctx, performActionReq)
	if err = epifigrpc.RPCError(resp, err); err != nil {
		return nil, fmt.Errorf("failed to perform action %w", err)
	}

	res, err := protojson.Marshal(resp)
	if err != nil {
		cxLogger.Error(ctx, "cannot marshal perform action response to json", zap.Error(err))
		return []*actionPb.ActionResponse{}, nil
	}
	return []*actionPb.ActionResponse{
		{
			ActionResponseType: actionPb.ActionResponseType_JSON,
			ActionResponse: &actionPb.ActionResponse_JsonResp{
				JsonResp: string(res),
			},
		},
	}, nil
}

func (s *Snooze) FetchFormElements(ctx context.Context, actionInput *ActionInput) ([]*dsPb.ParameterMeta, error) {
	reasons, err := s.annotationsHelper.GetAllowedAnnotationValues(ctx, reviewPb.ReviewEntityType_REVIEW_ENTITY_TYPE_CASE,
		&reviewPb.AnnotationType{AnnotationType: &reviewPb.AnnotationType_CaseAnnotationType{CaseAnnotationType: s.getCaseAnnotationType(actionInput)}})
	if err != nil {
		return nil, fmt.Errorf("failed to fetch snooze reasons %w", err)
	}
	elements := []*dsPb.ParameterMeta{
		{
			Name:            reqReasonName,
			Label:           snoozeReasonLabel,
			Type:            dsPb.ParameterDataType_DROPDOWN,
			Options:         reasons,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
		},
		{
			Name:            snoozeTill,
			Label:           snoozeTillLabel,
			Type:            dsPb.ParameterDataType_INTEGER,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
		},
		{
			Name:            snoozeComments,
			Label:           snoozeCommentsLabel,
			Type:            dsPb.ParameterDataType_MULTILINE_TEXT,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
		},
	}
	if actionInput.GetCaseDetails().GetStatus() == reviewPb.Status_STATUS_PENDING_USER_INFO {
		elements = append(elements, &dsPb.ParameterMeta{
			Name:            sendReminderCommsInOutcallFlow,
			Label:           sendReminderCommsInOutcallFlowLabel,
			Type:            dsPb.ParameterDataType_DROPDOWN,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
			Options:         boolOptions,
		})
	}
	return elements, nil
}

func (s *Snooze) getSnoozeParameters(ctx context.Context, actionInput *ActionInput, actionParameters []*dsPb.Filter) (*SnoozeParameters, error) {
	snoozeParameters := &SnoozeParameters{}
	for _, actionParameter := range actionParameters {
		switch actionParameter.GetParameterName() {
		case snoozeTill:
			if actionParameter.GetIntegerValue() > int64(maxSnoozeHours) || actionParameter.GetIntegerValue() <= 0 {
				return nil, fmt.Errorf("case can't be snoozed for %d hours, limit is: %d", actionParameter.GetIntegerValue(), maxSnoozeHours)
			}
			snoozeParameters.snoozeTill = timestampPb.New(time.Now().Add(time.Duration(actionParameter.GetIntegerValue()) * time.Hour))
		case reqReasonName:
			annotations, err := s.annotationsHelper.GetAllowedAnnotationsByValues(ctx, reviewPb.ReviewEntityType_REVIEW_ENTITY_TYPE_CASE,
				&reviewPb.AnnotationType{AnnotationType: &reviewPb.AnnotationType_CaseAnnotationType{CaseAnnotationType: s.getCaseAnnotationType(actionInput)}},
				[]string{actionParameter.GetDropdownValue()})
			if err != nil {
				return nil, fmt.Errorf("failed to get allowed annotation against values %w", err)
			}
			snoozeParameters.annotations = annotations
		case snoozeComments:
			snoozeParameters.snoozeComment = actionParameter.GetStringValue()
		case sendReminderCommsInOutcallFlow:
			snoozeParameters.sendReminderCommsInOutcallFlow = goUtils.TitleToEnum[commontypes.BooleanEnum](
				actionParameter.GetDropdownValue(), commontypes.BooleanEnum_value).ToBool()
		default:
			return nil, fmt.Errorf("unknown action parameters for action: %s", reviewPb.ActionType_ACTION_TYPE_SNOOZE)
		}
	}
	snoozeParameters.analystEmail = actionInput.GetHeader().GetAgentEmail()
	return snoozeParameters, nil
}

func (s *Snooze) buildPerformActionRequest(caseDetails *reviewPb.Case, actionParameters *SnoozeParameters) (*cmPb.PerformActionRequest, error) {
	if actionParameters == nil {
		return nil, fmt.Errorf("empty action paramaters passed for snooze case id: %s", caseDetails.GetId())
	}
	performActionReq := &cmPb.PerformActionRequest{
		CaseId:       caseDetails.GetId(),
		ActionType:   reviewPb.ActionType_ACTION_TYPE_SNOOZE,
		AnalystEmail: actionParameters.analystEmail,
		Source:       reviewPb.ActionSource_ACTION_SOURCE_REVIEW_FLOW,
		ActionParameters: &reviewPb.ActionParameters{
			Parameter: &reviewPb.ActionParameters_SnoozeParameters{
				SnoozeParameters: s.buildSnoozeActionParameters(caseDetails, actionParameters),
			},
		},
	}
	return performActionReq, nil
}

func (s *Snooze) getCaseAnnotationType(actionInput *ActionInput) reviewPb.CaseAnnotationType {
	switch actionInput.GetCaseDetails().GetStatus() {
	case reviewPb.Status_STATUS_PENDING_USER_INFO:
		return reviewPb.CaseAnnotationType_CASE_ANNOTATION_TYPE_SNOOZE_OUTCALL
	default:
		return reviewPb.CaseAnnotationType_CASE_ANNOTATION_TYPE_SNOOZE_REVIEW
	}
}

func (s *Snooze) buildSnoozeActionParameters(caseDetails *reviewPb.Case,
	actionParameters *SnoozeParameters) *reviewPb.SnoozeParameters {
	parameters := &reviewPb.SnoozeParameters{}
	parameters.SnoozeTill = actionParameters.snoozeTill
	if caseDetails.GetStatus() == reviewPb.Status_STATUS_PENDING_USER_INFO &&
		actionParameters.sendReminderCommsInOutcallFlow {
		parameters.Options = &reviewPb.SnoozeParameters_OutcallOptions_{
			OutcallOptions: &reviewPb.SnoozeParameters_OutcallOptions{
				SendOutcallReminderComms: true,
			},
		}
	}
	return parameters
}
