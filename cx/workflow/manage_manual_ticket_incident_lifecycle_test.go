package workflow_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	json "google.golang.org/protobuf/encoding/protojson"

	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/gamma/api/comms"
	stageWiseCommsPb "github.com/epifi/gamma/api/cx/manual_ticket_stage_wise_comms"
	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	"github.com/epifi/gamma/api/frontend/fcm"

	cxPayloadPb "github.com/epifi/gamma/api/cx/payload"
	watsonPb "github.com/epifi/gamma/api/cx/watson"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	cxActivityPb "github.com/epifi/gamma/api/cx/activity"
	celestialActivityV2 "github.com/epifi/gamma/celestial/activity/v2"
	cxActivity "github.com/epifi/gamma/cx/activity"
	cxWorkflow "github.com/epifi/gamma/cx/workflow"
	"github.com/epifi/be-common/pkg/epifitemporal"
	cxNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/cx"
)

func TestCxManageManualTicketIncidentLifecycleIncidentLifecycle(t *testing.T) {
	t.Parallel()
	const (
		defaultWorkflowID	= "default-test-workflow-id"
		ownership		= commontypes.Ownership_EPIFI_TECH
	)
	var (
		clientReqId	= uuid.New().String()
		incident1	= &watsonPb.Incident{
			Id: "incident-1",
		}
		ticket1	= &ticketPb.Ticket{
			Id:	1,
			Status:	ticketPb.Status_STATUS_RESOLVED,
		}
		manualTicketIncidentLifecycleBytes1, _	= json.Marshal(&cxPayloadPb.ManualTicketIncidentLifecycle{Incident: incident1, Ticket: ticket1})
		sampleTicketStatusMap			= map[string]*stageWiseCommsPb.ManualTicketStatusCommsConfig{
			"STATUS_RESOLVED": {
				IsCommsEnabled:	true,
				CommsInterval:	"2s",
				MaxNumOfComms:	1,
				CommsDetails: []*watsonPb.CommsDetail{
					{
						Detail: &watsonPb.CommsDetail_Notification{
							Notification: &comms.NotificationMessage{
								ShouldSkipDeviceRegistrationCheck:	commontypes.BooleanEnum_TRUE,
								Notification: &fcm.Notification{
									NotificationType:	fcm.NotificationType_SYSTEM_TRAY,
									NotificationTemplates: &fcm.Notification_SystemTrayTemplate{
										SystemTrayTemplate: &fcm.SystemTrayTemplate{
											CommonTemplateFields: &fcm.CommonTemplateFields{
												Title:	"Random title",
												Body:	"{#ticket_id#} is your Ticket ID.\nTap to view the ticket.",
											},
										},
									},
								},
							},
						},
					},
					{
						Detail: &watsonPb.CommsDetail_Notification{
							Notification: &comms.NotificationMessage{
								ShouldSkipDeviceRegistrationCheck:	commontypes.BooleanEnum_TRUE,
								Notification: &fcm.Notification{
									NotificationType:	fcm.NotificationType_SYSTEM_TRAY,
									NotificationTemplates: &fcm.Notification_SystemTrayTemplate{
										SystemTrayTemplate: &fcm.SystemTrayTemplate{
											CommonTemplateFields: &fcm.CommonTemplateFields{
												Title:	"Random title 2",
												Body:	"{#ticket_id#} is your Ticket ID.\nTap to view the ticket.",
											},
										},
									},
								},
							},
						},
					},
				},
				CommsDelay:	"2s",
			},
			"STATUS_OPEN": {
				IsCommsEnabled:	true,
				CommsInterval:	"2s",
				MaxNumOfComms:	1,
				CommsDetails: []*watsonPb.CommsDetail{
					{
						Detail: &watsonPb.CommsDetail_Notification{
							Notification: &comms.NotificationMessage{
								ShouldSkipDeviceRegistrationCheck:	commontypes.BooleanEnum_TRUE,
								Notification: &fcm.Notification{
									NotificationType:	fcm.NotificationType_SYSTEM_TRAY,
									NotificationTemplates: &fcm.Notification_SystemTrayTemplate{
										SystemTrayTemplate: &fcm.SystemTrayTemplate{
											CommonTemplateFields: &fcm.CommonTemplateFields{
												Title:	"Random title",
												Body:	"{#ticket_id#} is your Ticket ID.\nTap to view the ticket.",
											},
										},
									},
								},
							},
						},
					},
					{
						Detail: &watsonPb.CommsDetail_Notification{
							Notification: &comms.NotificationMessage{
								ShouldSkipDeviceRegistrationCheck:	commontypes.BooleanEnum_TRUE,
								Notification: &fcm.Notification{
									NotificationType:	fcm.NotificationType_SYSTEM_TRAY,
									NotificationTemplates: &fcm.Notification_SystemTrayTemplate{
										SystemTrayTemplate: &fcm.SystemTrayTemplate{
											CommonTemplateFields: &fcm.CommonTemplateFields{
												Title:	"Random title 2",
												Body:	"{#ticket_id#} is your Ticket ID.\nTap to view the ticket.",
											},
										},
									},
								},
							},
						},
					},
				},
				CommsDelay:	"2s",
			},
			"STATUS_WAITING_ON_CUSTOMER": {
				IsCommsEnabled:	true,
				CommsInterval:	"2s",
				MaxNumOfComms:	1,
				CommsDetails: []*watsonPb.CommsDetail{
					{
						Detail: &watsonPb.CommsDetail_Notification{
							Notification: &comms.NotificationMessage{
								ShouldSkipDeviceRegistrationCheck:	commontypes.BooleanEnum_TRUE,
								Notification: &fcm.Notification{
									NotificationType:	fcm.NotificationType_SYSTEM_TRAY,
									NotificationTemplates: &fcm.Notification_SystemTrayTemplate{
										SystemTrayTemplate: &fcm.SystemTrayTemplate{
											CommonTemplateFields: &fcm.CommonTemplateFields{
												Title:	"Random title",
												Body:	"{#ticket_id#} is your Ticket ID.\nTap to view the ticket.",
											},
										},
									},
								},
							},
						},
					},
					{
						Detail: &watsonPb.CommsDetail_Notification{
							Notification: &comms.NotificationMessage{
								ShouldSkipDeviceRegistrationCheck:	commontypes.BooleanEnum_TRUE,
								Notification: &fcm.Notification{
									NotificationType:	fcm.NotificationType_SYSTEM_TRAY,
									NotificationTemplates: &fcm.Notification_SystemTrayTemplate{
										SystemTrayTemplate: &fcm.SystemTrayTemplate{
											CommonTemplateFields: &fcm.CommonTemplateFields{
												Title:	"Random title 2",
												Body:	"{#ticket_id#} is your Ticket ID.\nTap to view the ticket.",
											},
										},
									},
								},
							},
						},
					},
				},
				CommsDelay:	"2s",
			},
		}
		sampleSLABreachConfig	= &stageWiseCommsPb.ManualTicketStatusCommsConfig{
			IsCommsEnabled:	true,
			CommsInterval:	"2h",
			MaxNumOfComms:	2,
			CommsDetails: []*watsonPb.CommsDetail{
				{
					Detail: &watsonPb.CommsDetail_Notification{
						Notification: &comms.NotificationMessage{
							ShouldSkipDeviceRegistrationCheck:	commontypes.BooleanEnum_TRUE,
							Notification: &fcm.Notification{
								NotificationType:	fcm.NotificationType_SYSTEM_TRAY,
								NotificationTemplates: &fcm.Notification_SystemTrayTemplate{
									SystemTrayTemplate: &fcm.SystemTrayTemplate{
										CommonTemplateFields: &fcm.CommonTemplateFields{
											Title:	"sla breached",
											Body:	"{#ticket_id#} is your Ticket ID.\nTap to view the ticket.",
										},
									},
								},
							},
						},
					},
				},
			},
			CommsDelay:	"2h",
		}
	)
	type mockGetWorkflowProcessingParamsV2 struct {
		enable	bool
		req	*activityPb.GetWorkflowProcessingParamsV2Request
		res	*activityPb.GetWorkflowProcessingParamsV2Response
		err	error
	}
	type mockInitiateWorkflowStageV2 struct {
		enable	bool
		req	*activityPb.InitiateWorkflowStageV2Request
		err	error
	}
	type mockGetManualTicketCommsConfigActivity struct {
		enable		bool
		req		*cxActivityPb.GetManualTicketCommsConfigRequest
		res		*cxActivityPb.GetManualTicketCommsConfigResponse
		processingDelay	time.Duration
		err		error
	}
	type mockSendManualTicketCommsActivity struct {
		enable		bool
		req		*cxActivityPb.SendManualTicketCommsRequest
		res		*cxActivityPb.SendManualTicketCommsResponse
		processingDelay	time.Duration
		err		error
	}
	type mockSendTicketResolutionSlaBreachedCommsActivity struct {
		enable		bool
		req		*cxActivityPb.SendTicketResolutionSlaBreachedCommsRequest
		res		*cxActivityPb.SendTicketResolutionSlaBreachedCommsResponse
		processingDelay	time.Duration
		err		error
	}
	type mockSignalWorkflow struct {
		enable		bool
		delay		time.Duration
		signal		[]byte
		signalName	epifitemporal.Signal
	}
	type mockResolveManualTicketIncidentActivity struct {
		enable		bool
		req		*cxActivityPb.ResolveManualTicketIncidentRequest
		res		*cxActivityPb.ResolveManualTicketIncidentResponse
		processingDelay	time.Duration
		err		error
	}
	type mockUpdateWorkflowStage struct {
		enable	bool
		req	*activityPb.UpdateWorkflowStageRequest
		res	*activityPb.UpdateWorkflowStageResponse
		err	error
	}
	type mockPublishWorkflowUpdateEventV2 struct {
		enable	bool
		req	*activityPb.PublishWorkflowUpdateEventV2Request
		err	error
	}
	tests := []struct {
		name							string
		req							*workflowPb.Request
		mockGetWorkflowProcessingParamsV2			mockGetWorkflowProcessingParamsV2
		mockInitiateWorkflowStageV2				[]mockInitiateWorkflowStageV2
		mockGetManualTicketCommsConfigActivity			mockGetManualTicketCommsConfigActivity
		mockSendManualTicketCommsActivity			mockSendManualTicketCommsActivity
		mockSendTicketResolutionSlaBreachedCommsActivity	mockSendTicketResolutionSlaBreachedCommsActivity
		mockResolveManualTicketIncidentActivity			mockResolveManualTicketIncidentActivity
		mockSignalWorkflow					[]mockSignalWorkflow
		mockUpdateWorkflowStage					[]mockUpdateWorkflowStage
		mockPublishWorkflowUpdateEventV2			[]mockPublishWorkflowUpdateEventV2
		wantErr							bool
	}{
		{
			name:	"error: GetWorkflowProcessingParamsV2 failed",
			req:	&workflowPb.Request{},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable:	true,
				req: &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId:	defaultWorkflowID,
				},
				err:	epifitemporal.NewPermanentError(errors.New("test error")),
			},
			wantErr:	true,
		},
		{
			name:	"error: failed to unmarshal wf req params payload",
			req:	&workflowPb.Request{},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable:	true,
				req: &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId:	defaultWorkflowID,
				},
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						ClientReqId: &workflowPb.ClientReqId{
							Id: clientReqId,
						},
					},
				},
				err:	nil,
			},
			wantErr:	true,
		},
		{
			name:	"error: GetManualTicketCommsConfig activity failed",
			req:	&workflowPb.Request{},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable:	true,
				req: &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId:	defaultWorkflowID,
				},
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						ClientReqId: &workflowPb.ClientReqId{
							Id: clientReqId,
						},
						Payload:	manualTicketIncidentLifecycleBytes1,
					},
				},
				err:	nil,
			},
			mockGetManualTicketCommsConfigActivity: mockGetManualTicketCommsConfigActivity{
				enable:	true,
				req: &cxActivityPb.GetManualTicketCommsConfigRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId:	clientReqId,
						Ownership:	ownership,
					},
					Incident:	incident1,
					TicketStatus:	ticket1.GetStatus(),
				},
				err:	epifitemporal.NewPermanentError(errors.New("test error")),
			},
			wantErr:	true,
		},
		{
			name:	"error: initiateWorkflowStage for send ticket status change comms stage failed",
			req:	&workflowPb.Request{},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable:	true,
				req: &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId:	defaultWorkflowID,
				},
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						ClientReqId: &workflowPb.ClientReqId{
							Id: clientReqId,
						},
						Payload:	manualTicketIncidentLifecycleBytes1,
					},
				},
				err:	nil,
			},
			mockGetManualTicketCommsConfigActivity: mockGetManualTicketCommsConfigActivity{
				enable:	true,
				req: &cxActivityPb.GetManualTicketCommsConfigRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId:	clientReqId,
						Ownership:	ownership,
					},
					Incident:	incident1,
					TicketStatus:	ticket1.GetStatus(),
				},
				res: &cxActivityPb.GetManualTicketCommsConfigResponse{
					ResponseHeader:	nil,
					CommsConfig: &stageWiseCommsPb.ManualTicketStageBasedCommsDetails{
						TicketStatusCommsConfigMap:	sampleTicketStatusMap,
						SlaBreachCommsConfig:		sampleSLABreachConfig,
					},
				},
				err:	nil,
			},
			mockInitiateWorkflowStageV2: []mockInitiateWorkflowStageV2{{
				enable:	true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId:	clientReqId,
						Ownership:	ownership,
					},
					WfReqId:	defaultWorkflowID,
					Stage:		workflowPb.Stage_CX_MANUAL_TICKET_STATUS_CHANGE_COMMS,
					Status:		stagePb.Status_INITIATED,
				},
				err:	epifitemporal.NewPermanentError(errors.New("test error")),
			}},
			wantErr:	true,
		},
		{
			name:	"error : SendManualTicketComms with last comms succeeded but error while initiating incident resolution stage",
			req:	&workflowPb.Request{},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable:	true,
				req: &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId:	defaultWorkflowID,
				},
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						ClientReqId: &workflowPb.ClientReqId{
							Id: clientReqId,
						},
						Payload:	manualTicketIncidentLifecycleBytes1,
					},
				},
				err:	nil,
			},
			mockGetManualTicketCommsConfigActivity: mockGetManualTicketCommsConfigActivity{
				enable:	true,
				req: &cxActivityPb.GetManualTicketCommsConfigRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId:	clientReqId,
						Ownership:	ownership,
					},
					Incident:	incident1,
					TicketStatus:	ticket1.GetStatus(),
				},
				res: &cxActivityPb.GetManualTicketCommsConfigResponse{
					ResponseHeader:	nil,
					CommsConfig: &stageWiseCommsPb.ManualTicketStageBasedCommsDetails{
						TicketStatusCommsConfigMap:	sampleTicketStatusMap,
						SlaBreachCommsConfig:		sampleSLABreachConfig,
					},
				},
				err:	nil,
			},
			mockInitiateWorkflowStageV2: []mockInitiateWorkflowStageV2{{
				enable:	true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId:	clientReqId,
						Ownership:	ownership,
					},
					WfReqId:	defaultWorkflowID,
					Stage:		workflowPb.Stage_CX_MANUAL_TICKET_STATUS_CHANGE_COMMS,
					Status:		stagePb.Status_INITIATED,
				},
				err:	nil,
			},
				{
					enable:	true,
					req: &activityPb.InitiateWorkflowStageV2Request{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId:	clientReqId,
							Ownership:	ownership,
						},
						WfReqId:	defaultWorkflowID,
						Stage:		workflowPb.Stage_CX_MANUAL_TICKET_INCIDENT_RESOLUTION,
						Status:		stagePb.Status_INITIATED,
					},
					err:	epifitemporal.NewPermanentError(errors.New("test error")),
				},
			},
			mockSendManualTicketCommsActivity: mockSendManualTicketCommsActivity{
				enable:	true,
				req: &cxActivityPb.SendManualTicketCommsRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId:	clientReqId,
						Ownership:	ownership,
					},
					Incident:	incident1,
					CommsType:	watsonPb.CommsType_COMMS_TYPE_MANUAL_TICKET_STATUS_CHANGE,
					ManualTicketStatusChangeCommsMeta: &watsonPb.ManualTicketStatusChangeCommsMeta{
						CurStatus:	ticketPb.Status_STATUS_RESOLVED,
						CommsNumber:	1,
					},
					TicketId:	ticket1.GetId(),
				},
				res:			nil,
				processingDelay:	0,
				err:			nil,
			},
			wantErr:	true,
		},
		{
			name:	"success : SendManualTicketComms with last comms succeeded but error in incident resolution stage, workflow moved to manual intervention",
			req:	&workflowPb.Request{},
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable:	true,
				req: &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: ownership,
					},
					WfReqId:	defaultWorkflowID,
				},
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						ClientReqId: &workflowPb.ClientReqId{
							Id: clientReqId,
						},
						Payload:	manualTicketIncidentLifecycleBytes1,
					},
				},
				err:	nil,
			},
			mockGetManualTicketCommsConfigActivity: mockGetManualTicketCommsConfigActivity{
				enable:	true,
				req: &cxActivityPb.GetManualTicketCommsConfigRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId:	clientReqId,
						Ownership:	ownership,
					},
					Incident:	incident1,
					TicketStatus:	ticket1.GetStatus(),
				},
				res: &cxActivityPb.GetManualTicketCommsConfigResponse{
					ResponseHeader:	nil,
					CommsConfig: &stageWiseCommsPb.ManualTicketStageBasedCommsDetails{
						TicketStatusCommsConfigMap:	sampleTicketStatusMap,
						SlaBreachCommsConfig:		sampleSLABreachConfig,
					},
				},
				err:	nil,
			},
			mockInitiateWorkflowStageV2: []mockInitiateWorkflowStageV2{
				{
					enable:	true,
					req: &activityPb.InitiateWorkflowStageV2Request{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId:	clientReqId,
							Ownership:	ownership,
						},
						WfReqId:	defaultWorkflowID,
						Stage:		workflowPb.Stage_CX_MANUAL_TICKET_STATUS_CHANGE_COMMS,
						Status:		stagePb.Status_INITIATED,
					},
					err:	nil,
				},
				{
					enable:	true,
					req: &activityPb.InitiateWorkflowStageV2Request{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId:	clientReqId,
							Ownership:	ownership,
						},
						WfReqId:	defaultWorkflowID,
						Stage:		workflowPb.Stage_CX_MANUAL_TICKET_INCIDENT_RESOLUTION,
						Status:		stagePb.Status_INITIATED,
					},
					err:	nil,
				},
			},
			mockSendManualTicketCommsActivity: mockSendManualTicketCommsActivity{
				enable:	true,
				req: &cxActivityPb.SendManualTicketCommsRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId:	clientReqId,
						Ownership:	ownership,
					},
					Incident:	incident1,
					CommsType:	watsonPb.CommsType_COMMS_TYPE_MANUAL_TICKET_STATUS_CHANGE,
					ManualTicketStatusChangeCommsMeta: &watsonPb.ManualTicketStatusChangeCommsMeta{
						CurStatus:	ticketPb.Status_STATUS_RESOLVED,
						CommsNumber:	1,
					},
					TicketId:	ticket1.GetId(),
				},
				res:			&cxActivityPb.SendManualTicketCommsResponse{},
				processingDelay:	0,
				err:			nil,
			},
			mockResolveManualTicketIncidentActivity: mockResolveManualTicketIncidentActivity{
				enable:	true,
				req: &cxActivityPb.ResolveManualTicketIncidentRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId:	clientReqId,
						Ownership:	ownership,
					},
					IncidentId:	incident1.GetId(),
				},
				res:			nil,
				processingDelay:	0,
				err:			epifitemporal.NewPermanentError(errors.New("error while resolving incident")),
			},
			wantErr:	true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			env := wts.NewTestWorkflowEnvironment()
			env.RegisterActivity(&celestialActivityV2.Processor{})
			env.RegisterActivity(&cxActivity.Processor{})

			if tt.mockGetWorkflowProcessingParamsV2.enable {
				env.OnActivity(
					string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, tt.mockGetWorkflowProcessingParamsV2.req,
				).Return(tt.mockGetWorkflowProcessingParamsV2.res, tt.mockGetWorkflowProcessingParamsV2.err)
			}

			for _, mockInitiateWorkflowStage := range tt.mockInitiateWorkflowStageV2 {
				if mockInitiateWorkflowStage.enable {
					env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, mockInitiateWorkflowStage.req).
						Return(mockInitiateWorkflowStage.err)
				}
			}
			for _, mockSignal := range tt.mockSignalWorkflow {
				if mockSignal.enable {
					env.RegisterDelayedCallback(func() {
						err := env.SignalWorkflowByID(defaultWorkflowID, string(mockSignal.signalName), mockSignal.signal)
						if err != nil {
							t.Errorf("failed to send signal %s to workflow: %v", mockSignal.signalName, err)
						}
					}, mockSignal.delay)
				}
			}
			for _, mockUpdateWfStageStatus := range tt.mockUpdateWorkflowStage {
				if mockUpdateWfStageStatus.enable {
					env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, mockUpdateWfStageStatus.req).
						Return(mockUpdateWfStageStatus.err)
				}
			}
			for _, mockPublishWfUpdateEvent := range tt.mockPublishWorkflowUpdateEventV2 {
				if mockPublishWfUpdateEvent.enable {
					env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, mockPublishWfUpdateEvent.req).
						Return(mockPublishWfUpdateEvent.err)
				}
			}
			if tt.mockGetManualTicketCommsConfigActivity.enable {
				env.OnActivity(string(cxNs.GetManualTicketCommsConfig), mock.Anything, tt.mockGetManualTicketCommsConfigActivity.req).
					Return(tt.mockGetManualTicketCommsConfigActivity.res, tt.mockGetManualTicketCommsConfigActivity.err)
			}
			if tt.mockSendManualTicketCommsActivity.enable {
				env.OnActivity(string(cxNs.SendManualTicketComms), mock.Anything, tt.mockSendManualTicketCommsActivity.req).
					Return(tt.mockSendManualTicketCommsActivity.res, tt.mockSendManualTicketCommsActivity.err)
			}
			if tt.mockSendTicketResolutionSlaBreachedCommsActivity.enable {
				env.OnActivity(string(cxNs.SendTicketResolutionSlaBreachedComms), mock.Anything, tt.mockSendTicketResolutionSlaBreachedCommsActivity.req).
					Return(tt.mockSendTicketResolutionSlaBreachedCommsActivity.res, tt.mockSendTicketResolutionSlaBreachedCommsActivity.err)
			}
			if tt.mockResolveManualTicketIncidentActivity.enable {
				env.OnActivity(string(cxNs.ResolveManualTicketIncident), mock.Anything, tt.mockResolveManualTicketIncidentActivity.req).
					Return(tt.mockResolveManualTicketIncidentActivity.res, tt.mockResolveManualTicketIncidentActivity.err)
			}
			env.ExecuteWorkflow(cxWorkflow.CxManageManualTicketIncidentLifecycle, tt.req)

			assert.True(t, env.IsWorkflowCompleted())

			if (env.GetWorkflowError() != nil) != tt.wantErr {
				t.Errorf("ManageManualTicketIncidentLifecycle() error = %v, wantErr %v", env.GetWorkflowError(), tt.wantErr)
			}
			env.AssertExpectations(t)
		})
	}
}
