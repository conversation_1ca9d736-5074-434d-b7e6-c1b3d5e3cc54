package serviceprovider

import (
	"google.golang.org/genproto/googleapis/type/date"

	"github.com/epifi/be-common/api/typesv2/common"

	"github.com/epifi/gamma/api/typesv2/webui"
)

type PanType string

const (
	PHYSICAL_PAN PanType = "PAN"
	EPAN                 = "EPAN"
)

type PanDetails struct {
	Name         *common.Name
	DateOfBirth  *date.Date
	GuardianName *common.Name
	PanNumber    string
	PanType      PanType
}

func getOcrTableDataRow(headerKeyCellMap map[string]string) *webui.TableRow {
	webUiheaderKeyCellMap := make(map[string]*webui.TableCell)
	for key, value := range headerKeyCellMap {
		webUiheaderKeyCellMap[key] = &webui.TableCell{
			DataType: webui.TableCell_DATA_TYPE_STRING,
			ValueV2:  &webui.TableCell_StringValue{StringValue: value},
		}
	}
	return &webui.TableRow{
		HeaderKeyCellMap: webUiheaderKeyCellMap,
	}
}
