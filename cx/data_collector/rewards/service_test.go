package rewards

import (
	"context"
	"flag"
	"fmt"
	"os"
	"reflect"
	"testing"
	"time"

	"github.com/epifi/gamma/cx/data_collector/helper"

	"github.com/golang/protobuf/ptypes/timestamp" // nolint: depguard
	"github.com/google/go-cmp/cmp"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/genproto/googleapis/type/postaladdress"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/testing/protocmp"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	pkgMoney "github.com/epifi/be-common/pkg/money"

	casperPb "github.com/epifi/gamma/api/casper"
	exchangerPb "github.com/epifi/gamma/api/casper/exchanger"
	evrPb "github.com/epifi/gamma/api/casper/external_vendor_redemption"
	mocks2 "github.com/epifi/gamma/api/casper/external_vendor_redemption/mocks"
	"github.com/epifi/gamma/api/casper/redemption"
	cxPb "github.com/epifi/gamma/api/cx"
	caPb "github.com/epifi/gamma/api/cx/customer_auth"
	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	rewardsProjectionPb "github.com/epifi/gamma/api/rewards/projector"
	"github.com/epifi/gamma/api/rewards/projector/mocks"
	rewardOfferPb "github.com/epifi/gamma/api/rewards/rewardoffers"
	"github.com/epifi/gamma/api/typesv2/webui"
	"github.com/epifi/gamma/cx/test"

	"github.com/golang/mock/gomock"

	mockExchanger "github.com/epifi/gamma/api/casper/exchanger/mocks"
	mockCasper "github.com/epifi/gamma/api/casper/mocks"
	mockCasperRedemption "github.com/epifi/gamma/api/casper/redemption/mocks"
	rePb "github.com/epifi/gamma/api/cx/data_collector/rewards"
	mockRewardsGenerator "github.com/epifi/gamma/api/rewards/mocks"
	mockRewardOffers "github.com/epifi/gamma/api/rewards/rewardoffers/mocks"
	mockUser "github.com/epifi/gamma/api/user/mocks"
	mockAuthEngine "github.com/epifi/gamma/cx/test/mocks/customer_auth/auth_engine"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	// nolint: dogsled
	_, _, _, teardown = test.InitTestServer(false)
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

var (
	getRewardsRequest1 = &rePb.GetRewardOffersRequest{
		Header: &cxPb.Header{
			AgentEmail:  "<EMAIL>",
			AccessToken: "test_token",
			TicketId:    1,
		},
	}
	getOffersRequest1 = &rePb.GetOffersRequest{
		Header: &cxPb.Header{
			AgentEmail:  "<EMAIL>",
			AccessToken: "test_token",
			TicketId:    1,
		},
		RedemptionMode: rePb.OfferRedemptionMode_FI_CARD,
	}
	rewardsErrorResponse = &rewardOfferPb.GetRewardOffersResponse{
		Status: rpcPb.StatusInternal(),
	}
	offersErrorResponse = &casperPb.GetOffersByFiltersResponse{
		Status: rpcPb.StatusInternal(),
	}
	rewardsServiceEmptyListResponse = &rewardOfferPb.GetRewardOffersResponse{
		Status:       rpcPb.StatusOk(),
		RewardOffers: []*rewardOfferPb.RewardOffer{},
	}
	offersEmptyListResponse = &casperPb.GetOffersByFiltersResponse{
		Status:      rpcPb.StatusOk(),
		Offers:      []*casperPb.Offer{},
		PageContext: &casperPb.PageContextResponse{},
	}
	offersServiceSuccessResponse = &casperPb.GetOffersByFiltersResponse{
		Status: rpcPb.StatusOk(),
		Offers: []*casperPb.Offer{
			{
				Id:             "test-offer-1",
				Name:           "test offer",
				Desc:           "test desc",
				Price:          10,
				RedemptionMode: casperPb.OfferRedemptionMode_FI_CARD,
				Tnc: &casperPb.OfferTnc{
					TncList: []string{"term1", "term2"},
				},
				AdditionalDetails: &casperPb.OfferAdditionalDetails{
					OfferDetails: []string{"det1", "det2"},
					HowToRedeem:  []string{"step1", "step2"},
				},
			},
			{
				Id:             "test-offer-2",
				Name:           "test offer 2",
				Desc:           "test desc 2",
				Price:          11,
				RedemptionMode: casperPb.OfferRedemptionMode_FI_CARD,
				Tnc: &casperPb.OfferTnc{
					TncList: []string{"term1", "term2"},
				},
				AdditionalDetails: &casperPb.OfferAdditionalDetails{
					OfferDetails: []string{"det1", "det2"},
					HowToRedeem:  []string{"step1", "step2"},
				},
			},
		},
		OfferIdToListingMap: map[string]*casperPb.OfferListing{
			"test-offer-1": {
				ActiveSince:  "2024-03-26T18:30:01.000000+05:30",
				ActiveTill:   "2024-03-26T18:30:01.000000+05:30",
				DisplaySince: "2024-03-26T18:30:01.000000+05:30",
				DisplayTill:  "2024-03-26T18:30:01.000000+05:30",
			},
			"test-offer-2": {
				ActiveSince:  "2024-03-26T18:30:01.000000+05:30",
				ActiveTill:   "2024-03-26T18:30:01.000000+05:30",
				DisplaySince: "2024-03-26T18:30:01.000000+05:30",
				DisplayTill:  "2024-03-26T18:30:01.000000+05:30",
			},
		},
	}
	offerSuccessResponse = &rePb.GetOffersResponse{
		Status: rpcPb.StatusOk(),
		Offers: []*rePb.Offer{
			{
				Id:             "test-offer-1",
				Name:           "test offer",
				Desc:           "test desc",
				Price:          10,
				RedemptionMode: rePb.OfferRedemptionMode_FI_CARD,
				TncList:        []string{"term1", "term2"},
				OfferDetails:   []string{"det1", "det2"},
				HowToRedeem:    []string{"step1", "step2"},
				ActiveSince: &timestampPb.Timestamp{
					Seconds: **********,
				},
				ActiveTill: &timestampPb.Timestamp{
					Seconds: **********,
				},
				DisplaySince: &timestampPb.Timestamp{
					Seconds: **********,
				},
				DisplayTill: &timestampPb.Timestamp{
					Seconds: **********,
				},
				AttachEntityMeta: &ticketPb.AttachEntityMeta{
					Meta: &ticketPb.AttachEntityMeta_OfferMeta{
						OfferMeta: &ticketPb.OfferMeta{
							OfferId:          "test-offer-1",
							OfferName:        "test offer",
							OfferDescription: "test desc",
						},
					},
				},
			},
			{
				Id:             "test-offer-2",
				Name:           "test offer 2",
				Desc:           "test desc 2",
				Price:          11,
				RedemptionMode: rePb.OfferRedemptionMode_FI_CARD,
				TncList:        []string{"term1", "term2"},
				OfferDetails:   []string{"det1", "det2"},
				HowToRedeem:    []string{"step1", "step2"},
				ActiveSince: &timestampPb.Timestamp{
					Seconds: **********,
				},
				ActiveTill: &timestampPb.Timestamp{
					Seconds: **********,
				},
				DisplaySince: &timestampPb.Timestamp{
					Seconds: **********,
				},
				DisplayTill: &timestampPb.Timestamp{
					Seconds: **********,
				},
				AttachEntityMeta: &ticketPb.AttachEntityMeta{
					Meta: &ticketPb.AttachEntityMeta_OfferMeta{
						OfferMeta: &ticketPb.OfferMeta{
							OfferId:          "test-offer-2",
							OfferName:        "test offer 2",
							OfferDescription: "test desc 2",
						},
					},
				},
			},
		},
		PageContext: &cxPb.PageContextResponse{},
	}
	rewardsServiceSuccessResponse = &rewardOfferPb.GetRewardOffersResponse{
		Status: rpcPb.StatusOk(),
		RewardOffers: []*rewardOfferPb.RewardOffer{
			{
				Id: "test-reward",
				DisplayMeta: &rewardOfferPb.DisplayMeta{
					Tncs:         []string{"term1", "term2"},
					Steps:        []string{"step1", "step2"},
					Title:        "title",
					ActionDesc:   "sample description",
					DisplaySince: "2024-03-26T18:30:01.000000+05:30",
					DisplayTill:  "2024-03-26T18:30:01.000000+05:30",
				},
				RewardMeta: &rewardOfferPb.RewardMeta{
					IsImplicitLockingDisabled: true,
				},
				ActiveSince: "2024-03-26T18:30:01.000000+05:30",
				ActiveTill:  "2024-03-26T18:30:01.000000+05:30",
			},
			{
				Id: "test-reward-2",
				DisplayMeta: &rewardOfferPb.DisplayMeta{
					Tncs:         []string{"term10", "term11"},
					Steps:        []string{"step10", "step11"},
					Title:        "title new",
					ActionDesc:   "sample description new",
					DisplaySince: "2024-03-26T18:30:01.000000+05:30",
					DisplayTill:  "2024-03-26T18:30:01.000000+05:30",
				},
				ActiveSince: "2024-03-26T18:30:01.000000+05:30",
				ActiveTill:  "2024-03-26T18:30:01.000000+05:30",
			},
		},
	}
	rewardsServiceSuccessResponse1 = &rewardOfferPb.GetRewardOffersResponse{
		Status: rpcPb.StatusOk(),
		RewardOffers: []*rewardOfferPb.RewardOffer{
			{
				Id: "test-reward-4",
				DisplayMeta: &rewardOfferPb.DisplayMeta{
					Tncs:         []string{"term1", "term2"},
					Steps:        []string{"step1", "step2"},
					Title:        "title",
					ActionDesc:   "sample description",
					DisplaySince: "2024-03-26T18:30:01.000000+05:30",
					DisplayTill:  "2024-03-26T18:30:01.000000+05:30",
				},
				RewardMeta: &rewardOfferPb.RewardMeta{
					IsImplicitLockingDisabled: true,
				},
				ActiveSince: "2024-03-26T18:30:01.000000+05:30",
				ActiveTill:  "2024-03-26T18:30:01.000000+05:30",
				ActionType:  "MANUAL_GIVEAWAY",
			},
			{
				Id: "test-reward-5",
				DisplayMeta: &rewardOfferPb.DisplayMeta{
					Tncs:         []string{"term10", "term11"},
					Steps:        []string{"step10", "step11"},
					Title:        "title new",
					ActionDesc:   "sample description new",
					DisplaySince: "2024-03-26T18:30:01.000000+05:30",
					DisplayTill:  "2024-03-26T18:30:01.000000+05:30",
				},
				ActiveSince: "2024-03-26T18:30:01.000000+05:30",
				ActiveTill:  "2024-03-26T18:30:01.000000+05:30",
			},
		},
	}
	RewardsSuccessResponse = &rePb.GetRewardOffersResponse{
		Status:           rpcPb.StatusOk(),
		SherlockDeepLink: nil,
		RewardOffers: []*rePb.RewardOffer{
			{
				Id:    "test-reward",
				Tncs:  []string{"term1", "term2"},
				Steps: []string{"step1", "step2"},
				TncsV1: &webui.LabelValue{
					Label: "Terms and Conditions",
					Value: []string{"term1", "term2"},
				},
				StepsV1: &webui.LabelValue{
					Label: "Steps to Redeem",
					Value: []string{"step1", "step2"},
				},
				ActionDesc: "sample description",
				Title:      "title",
				AttachEntityMeta: &ticketPb.AttachEntityMeta{
					Meta: &ticketPb.AttachEntityMeta_RewardOfferMeta{
						RewardOfferMeta: &ticketPb.RewardOfferMeta{
							RewardOfferId:          "test-reward",
							RewardOfferName:        "title",
							RewardOfferDescription: "sample description",
						},
					},
				},
				ActiveSince: &timestampPb.Timestamp{
					Seconds: **********,
				},
				ActiveTill: &timestampPb.Timestamp{
					Seconds: **********,
				},
				DisplaySince: &timestampPb.Timestamp{
					Seconds: **********,
				},
				DisplayTill: &timestampPb.Timestamp{
					Seconds: **********,
				},
			},
			{
				Id:    "test-reward-2",
				Tncs:  []string{"term10", "term11"},
				Steps: []string{"step10", "step11"},
				TncsV1: &webui.LabelValue{
					Label: "Terms and Conditions",
					Value: []string{"term10", "term11", "reward will be locked if user's account is Min KYC or Fi-lite"},
				},
				StepsV1: &webui.LabelValue{
					Label: "Steps to Redeem",
					Value: []string{"step10", "step11"},
				},
				ActiveSince: &timestampPb.Timestamp{
					Seconds: **********,
				},
				ActiveTill: &timestampPb.Timestamp{
					Seconds: **********,
				},
				DisplaySince: &timestampPb.Timestamp{
					Seconds: **********,
				},
				DisplayTill: &timestampPb.Timestamp{
					Seconds: **********,
				},
				ActionDesc: "sample description new",
				Title:      "title new",
				AttachEntityMeta: &ticketPb.AttachEntityMeta{
					Meta: &ticketPb.AttachEntityMeta_RewardOfferMeta{
						RewardOfferMeta: &ticketPb.RewardOfferMeta{
							RewardOfferId:          "test-reward-2",
							RewardOfferName:        "title new",
							RewardOfferDescription: "sample description new",
						},
					},
				},
			},
		},
	}
	RewardsSuccessResponse1 = &rePb.GetRewardOffersResponse{
		Status:           rpcPb.StatusOk(),
		SherlockDeepLink: nil,
		RewardOffers: []*rePb.RewardOffer{
			{
				Id:    "test-reward",
				Tncs:  []string{"term1", "term2"},
				Steps: []string{"step1", "step2"},
				TncsV1: &webui.LabelValue{
					Label: "Terms and Conditions",
					Value: []string{"term1", "term2"},
				},
				StepsV1: &webui.LabelValue{
					Label: "Steps to Redeem",
					Value: []string{"step1", "step2"},
				},
				ActionDesc: "sample description",
				Title:      "title",
				ActiveSince: &timestampPb.Timestamp{
					Seconds: **********,
				},
				ActiveTill: &timestampPb.Timestamp{
					Seconds: **********,
				},
				DisplaySince: &timestampPb.Timestamp{
					Seconds: **********,
				},
				DisplayTill: &timestampPb.Timestamp{
					Seconds: **********,
				},
				AttachEntityMeta: &ticketPb.AttachEntityMeta{
					Meta: &ticketPb.AttachEntityMeta_RewardOfferMeta{
						RewardOfferMeta: &ticketPb.RewardOfferMeta{
							RewardOfferId:          "test-reward",
							RewardOfferName:        "title",
							RewardOfferDescription: "sample description",
						},
					},
				},
			},
			{
				Id:    "test-reward-2",
				Tncs:  []string{"term10", "term11"},
				Steps: []string{"step10", "step11"},
				TncsV1: &webui.LabelValue{
					Label: "Terms and Conditions",
					Value: []string{"term10", "term11", "reward will be locked if user's account is Min KYC or Fi-lite"},
				},
				StepsV1: &webui.LabelValue{
					Label: "Steps to Redeem",
					Value: []string{"step10", "step11"},
				},
				ActionDesc: "sample description new",
				Title:      "title new",
				ActiveSince: &timestampPb.Timestamp{
					Seconds: **********,
				},
				ActiveTill: &timestampPb.Timestamp{
					Seconds: **********,
				},
				DisplaySince: &timestampPb.Timestamp{
					Seconds: **********,
				},
				DisplayTill: &timestampPb.Timestamp{
					Seconds: **********,
				},
				AttachEntityMeta: &ticketPb.AttachEntityMeta{
					Meta: &ticketPb.AttachEntityMeta_RewardOfferMeta{
						RewardOfferMeta: &ticketPb.RewardOfferMeta{
							RewardOfferId:          "test-reward-2",
							RewardOfferName:        "title new",
							RewardOfferDescription: "sample description new",
						},
					},
				},
			},
			{
				Id:    "test-reward-5",
				Tncs:  []string{"term10", "term11"},
				Steps: []string{"step10", "step11"},
				TncsV1: &webui.LabelValue{
					Label: "Terms and Conditions",
					Value: []string{"term10", "term11", "reward will be locked if user's account is Min KYC or Fi-lite"},
				},
				StepsV1: &webui.LabelValue{
					Label: "Steps to Redeem",
					Value: []string{"step10", "step11"},
				},
				ActionDesc: "sample description new",
				Title:      "title new",
				ActiveSince: &timestampPb.Timestamp{
					Seconds: **********,
				},
				ActiveTill: &timestampPb.Timestamp{
					Seconds: **********,
				},
				DisplaySince: &timestampPb.Timestamp{
					Seconds: **********,
				},
				DisplayTill: &timestampPb.Timestamp{
					Seconds: **********,
				},
				AttachEntityMeta: &ticketPb.AttachEntityMeta{
					Meta: &ticketPb.AttachEntityMeta_RewardOfferMeta{
						RewardOfferMeta: &ticketPb.RewardOfferMeta{
							RewardOfferId:          "test-reward-5",
							RewardOfferName:        "title new",
							RewardOfferDescription: "sample description new",
						},
					},
				},
			},
		},
	}
	sherlockDeepLink1 = &caPb.SherlockDeepLink{
		Screen: caPb.Screen_AUTH_SCREEN,
	}
	getExchangerOffersRequest = &rePb.GetExchangerOffersRequest{
		Header: &cxPb.Header{
			AgentEmail:  "<EMAIL>",
			AccessToken: "test_token",
			TicketId:    1,
		},
	}
	exchangerOffersErrorResponse = &exchangerPb.GetExchangerOffersByFiltersResponse{
		Status: rpcPb.StatusInternal(),
	}
	exchangerOffersServiceEmptyListResponse = &exchangerPb.GetExchangerOffersByFiltersResponse{
		Status:          rpcPb.StatusOk(),
		ExchangerOffers: []*exchangerPb.ExchangerOffer{},
	}
	exchangerOffersServiceSuccessResponse = &exchangerPb.GetExchangerOffersByFiltersResponse{
		Status: rpcPb.StatusOk(),
		ExchangerOffers: []*exchangerPb.ExchangerOffer{
			{
				Id:                 "test-exchanger-offer-id",
				RedemptionCurrency: exchangerPb.ExchangerOfferRedemptionCurrency_EXCHANGER_OFFER_REDEMPTION_CURRENCY_FI_COINS,
				RedemptionPrice:    1.0,
				OfferDisplayDetails: &exchangerPb.ExchangerOfferDisplayDetails{
					Title:             "test-exchanger-offer-title",
					Subtitle:          "test-exchanger-offer-subtitle",
					Desc:              "test-exchanger-offer-desc",
					ImageUrl:          "test-exchanger-offer-img-url",
					TileBgColor:       "test-exchanger-offer-title-bg-color",
					DisplayRank:       0,
					InfoBannerTitle:   "test-exchanger-offer-info-banner-title",
					InfoBannerBgColor: "test-exchanger-offer-info-banner-bg-color",
					InfoBannerIconUrl: "test-exchanger-offer-info-banner-icon-url",
					HowToRedeem:       []string{"step-1", "step-2"},
					Tnc:               []string{"tnc-1", "tnc-2"},
				},
				OfferOptionsConfig: &exchangerPb.ExchangerOfferOptionsConfig{
					OptionsConfig:           nil,
					DefaultDecideTimeInSecs: 0,
				},
				OfferAggregatesConfig: &exchangerPb.ExchangerOfferAggregatesConfig{DailyAllowedAttemptsPerUser: 10},
				ExternalId:            "test-exchanger-offer-external-id",
				CreatedAt: &timestampPb.Timestamp{
					Seconds: 1,
					Nanos:   0,
				},
				UpdatedAt: &timestampPb.Timestamp{
					Seconds: 2,
					Nanos:   0,
				},
			},
			{
				Id:                 "test-exchanger-offer-id-2",
				RedemptionCurrency: exchangerPb.ExchangerOfferRedemptionCurrency_EXCHANGER_OFFER_REDEMPTION_CURRENCY_FI_COINS,
				RedemptionPrice:    2.0,
				OfferDisplayDetails: &exchangerPb.ExchangerOfferDisplayDetails{
					Title:             "test-exchanger-offer-title-2",
					Subtitle:          "test-exchanger-offer-subtitle-2",
					Desc:              "test-exchanger-offer-desc-2",
					ImageUrl:          "test-exchanger-offer-img-url-2",
					TileBgColor:       "test-exchanger-offer-title-bg-color-2",
					DisplayRank:       2,
					InfoBannerTitle:   "test-exchanger-offer-info-banner-title-2",
					InfoBannerBgColor: "test-exchanger-offer-info-banner-bg-color-2",
					InfoBannerIconUrl: "test-exchanger-offer-info-banner-icon-url-2",
					HowToRedeem:       []string{"step-1", "step-2"},
					Tnc:               []string{"tnc-1", "tnc-2"},
				},
				OfferOptionsConfig: &exchangerPb.ExchangerOfferOptionsConfig{
					OptionsConfig:           nil,
					DefaultDecideTimeInSecs: 0,
				},
				OfferAggregatesConfig: &exchangerPb.ExchangerOfferAggregatesConfig{DailyAllowedAttemptsPerUser: 10},
				ExternalId:            "test-exchanger-offer-external-id-2",
				CreatedAt: &timestampPb.Timestamp{
					Seconds: 1,
					Nanos:   0,
				},
				UpdatedAt: &timestampPb.Timestamp{
					Seconds: 2,
					Nanos:   0,
				},
			},
		},
		OfferIdToListingMap: map[string]*exchangerPb.ExchangerOfferListing{
			"test-exchanger-offer-id": {
				ActiveSince:  "2024-03-26T18:30:01.000000+05:30",
				ActiveTill:   "2024-03-26T18:30:01.000000+05:30",
				DisplaySince: "2024-03-26T18:30:01.000000+05:30",
				DisplayTill:  "2024-03-26T18:30:01.000000+05:30"},
			"test-exchanger-offer-id-2": {
				ActiveSince:  "2024-03-26T18:30:01.000000+05:30",
				ActiveTill:   "2024-03-26T18:30:01.000000+05:30",
				DisplaySince: "2024-03-26T18:30:01.000000+05:30",
				DisplayTill:  "2024-03-26T18:30:01.000000+05:30",
			},
		},
	}
	exchangerOffersSuccessResponse = &rePb.GetExchangerOffersResponse{
		Status: rpcPb.StatusOk(),
		ExchangerOffers: []*rePb.ExchangerOffer{
			{
				Id:             "test-exchanger-offer-id",
				Name:           "test-exchanger-offer-title",
				Desc:           "test-exchanger-offer-desc",
				Price:          1.0,
				RedemptionMode: rePb.OfferRedemptionMode_FI_COINS,
				TncList:        []string{"tnc-1", "tnc-2"},
				ActiveSince: &timestampPb.Timestamp{
					Seconds: **********,
				},
				ActiveTill: &timestampPb.Timestamp{
					Seconds: **********,
				},
				DisplaySince: &timestampPb.Timestamp{
					Seconds: **********,
				},
				DisplayTill: &timestampPb.Timestamp{
					Seconds: **********,
				},
				AttachEntityMeta: &ticketPb.AttachEntityMeta{
					Meta: &ticketPb.AttachEntityMeta_ExchangerOfferMeta{
						ExchangerOfferMeta: &ticketPb.ExchangerOfferMeta{
							ExchangerOfferId:          "test-exchanger-offer-id",
							ExchangerOfferTitle:       "test-exchanger-offer-title",
							ExchangerOfferDescription: "test-exchanger-offer-desc",
						},
					},
				},
			},
			{
				Id:             "test-exchanger-offer-id-2",
				Name:           "test-exchanger-offer-title-2",
				Desc:           "test-exchanger-offer-desc-2",
				Price:          2.0,
				RedemptionMode: rePb.OfferRedemptionMode_FI_COINS,
				TncList:        []string{"tnc-1", "tnc-2"},
				ActiveSince: &timestampPb.Timestamp{
					Seconds: **********,
				},
				ActiveTill: &timestampPb.Timestamp{
					Seconds: **********,
				},
				DisplaySince: &timestampPb.Timestamp{
					Seconds: **********,
				},
				DisplayTill: &timestampPb.Timestamp{
					Seconds: **********,
				},
				AttachEntityMeta: &ticketPb.AttachEntityMeta{
					Meta: &ticketPb.AttachEntityMeta_ExchangerOfferMeta{
						ExchangerOfferMeta: &ticketPb.ExchangerOfferMeta{
							ExchangerOfferId:          "test-exchanger-offer-id-2",
							ExchangerOfferTitle:       "test-exchanger-offer-title-2",
							ExchangerOfferDescription: "test-exchanger-offer-desc-2",
						},
					},
				},
			},
		},
	}
	getExchangerOfferOrdersRequest = &rePb.GetExchangerOfferOrdersRequest{
		Header: &cxPb.Header{
			AgentEmail:  "<EMAIL>",
			AccessToken: "test_token",
			TicketId:    1,
		},
		RedemptionState: 0,
		FromDate:        nil,
		UptoDate:        nil,
		PageContext:     nil,
	}
	exchangerOfferOrdersErrorResponse = &exchangerPb.GetExchangerOfferOrdersForActorResponse{
		Status: rpcPb.StatusInternal(),
	}
	exchangerOfferOrdersServiceEmptyListResponse = &exchangerPb.GetExchangerOfferOrdersForActorResponse{
		Status:               rpcPb.StatusOk(),
		ExchangerOfferOrders: []*exchangerPb.ExchangerOfferOrder{},
	}
	exchangerOffersOrdersServiceSuccessResponse = &exchangerPb.GetExchangerOfferOrdersForActorResponse{
		Status: rpcPb.StatusOk(),
		ExchangerOfferOrders: []*exchangerPb.ExchangerOfferOrder{
			{
				Id:                           "test-exchanger-offer-order-id",
				ActorId:                      "test-exchanger-offer-order-actor-id",
				ExchangerOfferId:             "test-exchanger-offer-order-exchanger-offer-id",
				ExchangerOfferActorAttemptId: "test-exchanger-offer-order-attempt-id",
				State:                        0,
				Options: &exchangerPb.ExchangerOfferOptions{
					Options: []*exchangerPb.ExchangerOfferOption{
						{Id: "test-exchanger-offer-order-option-id"},
					},
					DefaultDecideTimeInSecs: 0,
				},
				ChosenOption: &exchangerPb.ExchangerOfferOption{Id: "test-exchanger-offer-order-option-id"},
				ExternalId:   "external-id",
				CreatedAt: &timestampPb.Timestamp{
					Seconds: 1,
					Nanos:   0,
				},
				UpdatedAt: &timestampPb.Timestamp{
					Seconds: 1,
					Nanos:   0,
				},
			},
		},
		PageContext: &rpcPb.PageContextResponse{
			HasBefore:   false,
			HasAfter:    true,
			BeforeToken: "before-token",
			AfterToken:  "after-token",
		},
	}
	exchangerOffersOrdersSuccessResponse = &rePb.GetExchangerOfferOrdersResponse{
		Status: rpcPb.StatusOk(),
		ExchangerOfferOrders: []*exchangerPb.ExchangerOfferOrder{
			{
				Id:                           "test-exchanger-offer-order-id",
				ActorId:                      "test-exchanger-offer-order-actor-id",
				ExchangerOfferId:             "test-exchanger-offer-order-exchanger-offer-id",
				ExchangerOfferActorAttemptId: "test-exchanger-offer-order-attempt-id",
				State:                        0,
				Options: &exchangerPb.ExchangerOfferOptions{
					Options: []*exchangerPb.ExchangerOfferOption{
						{Id: "test-exchanger-offer-order-option-id"},
					},
					DefaultDecideTimeInSecs: 0,
				},
				ChosenOption: &exchangerPb.ExchangerOfferOption{Id: "test-exchanger-offer-order-option-id"},
				ExternalId:   "external-id",
				CreatedAt: &timestampPb.Timestamp{
					Seconds: 1,
					Nanos:   0,
				},
				UpdatedAt: &timestampPb.Timestamp{
					Seconds: 1,
					Nanos:   0,
				},
			},
		},
		PageContext: &cxPb.PageContextResponse{
			HasBefore:   false,
			HasAfter:    true,
			BeforeToken: "before-token",
			AfterToken:  "after-token",
		},
		ExchangerOfferOrderCxWrapperList: []*rePb.ExchangerOfferOrderCxWrapper{
			{
				ExchangerOfferOrder: &exchangerPb.ExchangerOfferOrder{
					Id:                           "test-exchanger-offer-order-id",
					ActorId:                      "test-exchanger-offer-order-actor-id",
					ExchangerOfferId:             "test-exchanger-offer-order-exchanger-offer-id",
					ExchangerOfferActorAttemptId: "test-exchanger-offer-order-attempt-id",
					State:                        0,
					Options: &exchangerPb.ExchangerOfferOptions{
						Options: []*exchangerPb.ExchangerOfferOption{
							{Id: "test-exchanger-offer-order-option-id"},
						},
						DefaultDecideTimeInSecs: 0,
					},
					ChosenOption: &exchangerPb.ExchangerOfferOption{Id: "test-exchanger-offer-order-option-id"},
					ExternalId:   "external-id",
					CreatedAt: &timestampPb.Timestamp{
						Seconds: 1,
						Nanos:   0,
					},
					UpdatedAt: &timestampPb.Timestamp{
						Seconds: 1,
						Nanos:   0,
					},
				},
				AttachEntityMeta: &ticketPb.AttachEntityMeta{
					Meta: &ticketPb.AttachEntityMeta_ExchangerOfferOrderMeta{
						ExchangerOfferOrderMeta: &ticketPb.ExchangerOfferOrderMeta{
							ExchangerOrderId: "test-exchanger-offer-order-id",
						},
					},
				},
			},
		},
	}
	getRedeemedOffersRequest = &rePb.GetRedeemedOffersRequest{
		Header: &cxPb.Header{
			AgentEmail:  "<EMAIL>",
			AccessToken: "test_token",
			TicketId:    1,
		},
	}
	redeemedOffersErrorResponse = &redemption.GetRedeemedOffersForActorResponse{
		Status: rpcPb.StatusInternal(),
	}
	redeemedOffersServiceEmptyListResponse = &redemption.GetRedeemedOffersForActorResponse{
		Status:         rpcPb.StatusOk(),
		RedeemedOffers: []*redemption.RedeemedOffer{},
	}
	redeemedOffersServiceSuccessResponse = &redemption.GetRedeemedOffersForActorResponse{
		Status: rpcPb.StatusOk(),
		RedeemedOffers: []*redemption.RedeemedOffer{
			{
				Id:                  "redeemed-offer-id",
				ActorId:             "redeemed-offer-actor-id",
				RedemptionRequestId: "request-id",
				OfferId:             "",
				OfferType:           casperPb.OfferType_PHYSICAL_MERCHANDISE,
				RedemptionState:     redemption.OfferRedemptionState_OFFER_REDEMPTION_INITIATED,
				RedeemedOfferDetails: &redemption.RedeemedOffer_RedeemedOfferDetails{
					OfferTypeSpecificDetails: &redemption.RedeemedOffer_RedeemedOfferDetails_PhysicalMerchandiseDetails{
						PhysicalMerchandiseDetails: &casperPb.PhysicalMerchandiseDetails{
							ShippingAddress: &postaladdress.PostalAddress{
								Revision:           0,
								RegionCode:         "test-region-code",
								LanguageCode:       "test-language-code",
								PostalCode:         "test-postal-code",
								SortingCode:        "test-sorting-code",
								AdministrativeArea: "test-administrative-area",
								Locality:           "test-locality",
								Sublocality:        "test-sublocality",
								AddressLines:       []string{"test-address-line-1", "test-address-line-2"},
								Recipients:         []string{"test-recipients-1"},
								Organization:       "test-organization",
							},
						},
					},
					EncryptionKeyId: "key-id",
				},
			},
			{
				Id:                   "test-redeemed-offer-id-2",
				ActorId:              "test-redeemed-offer-actor-id-2",
				RedemptionRequestId:  "test-request-id-2",
				OfferId:              "test-offer-id-2",
				OfferType:            0,
				RedemptionState:      redemption.OfferRedemptionState_OFFER_REDEMPTION_INITIATED,
				RedeemedOfferDetails: nil,
			},
		},
		PageContext: &rpcPb.PageContextResponse{
			HasBefore:   false,
			HasAfter:    true,
			BeforeToken: "before-token",
			AfterToken:  "after-token",
		},
	}
	redeemedOffersSuccessResponse = &rePb.GetRedeemedOffersResponse{
		Status: rpcPb.StatusOk(),
		PageContext: &cxPb.PageContextResponse{
			HasBefore:   false,
			HasAfter:    true,
			BeforeToken: "before-token",
			AfterToken:  "after-token",
		},
		RedeemedOffers: []*redemption.RedeemedOffer{
			redeemedOfferFixture1,
			redeemedOfferFixture2,
		},
		SherlockDeepLink: nil,
		RedeemedOfferCxWrapperList: []*rePb.RedeemedOfferCxWrapper{
			{
				RedeemedOffer: redeemedOfferFixture1,
				AttachEntityMeta: &ticketPb.AttachEntityMeta{
					Meta: &ticketPb.AttachEntityMeta_RedeemedOfferMeta{
						RedeemedOfferMeta: &ticketPb.RedeemedOfferMeta{
							RedeemedOfferId: "redeemed-offer-id",
						},
					},
				},
			},
			{
				RedeemedOffer: redeemedOfferFixture2,
				AttachEntityMeta: &ticketPb.AttachEntityMeta{
					Meta: &ticketPb.AttachEntityMeta_RedeemedOfferMeta{
						RedeemedOfferMeta: &ticketPb.RedeemedOfferMeta{
							RedeemedOfferId: "test-redeemed-offer-id-2",
						},
					},
				},
			},
		},
	}

	redeemedOfferFixture1 = &redemption.RedeemedOffer{
		Id:                  "redeemed-offer-id",
		ActorId:             "redeemed-offer-actor-id",
		RedemptionRequestId: "request-id",
		OfferId:             "",
		OfferType:           casperPb.OfferType_PHYSICAL_MERCHANDISE,
		RedemptionState:     redemption.OfferRedemptionState_OFFER_REDEMPTION_INITIATED,
		RedeemedOfferDetails: &redemption.RedeemedOffer_RedeemedOfferDetails{
			OfferTypeSpecificDetails: &redemption.RedeemedOffer_RedeemedOfferDetails_PhysicalMerchandiseDetails{
				PhysicalMerchandiseDetails: &casperPb.PhysicalMerchandiseDetails{
					ShippingAddress: &postaladdress.PostalAddress{
						PostalCode:   "test-postal-code",
						AddressLines: []string{"test-***"},
					},
				},
			},
			EncryptionKeyId: "key-id",
		},
	}

	redeemedOfferFixture2 = &redemption.RedeemedOffer{
		Id:                   "test-redeemed-offer-id-2",
		ActorId:              "test-redeemed-offer-actor-id-2",
		RedemptionRequestId:  "test-request-id-2",
		OfferId:              "test-offer-id-2",
		OfferType:            0,
		RedemptionState:      redemption.OfferRedemptionState_OFFER_REDEMPTION_INITIATED,
		RedeemedOfferDetails: nil,
	}
	rewardOffer1 = &rewardOfferPb.RewardOffer{
		Id:        "offer-1",
		OfferType: rewardsPb.RewardOfferType_INFINITE_OR_SALARY_TIER_2_PERCENT_CASHBACK_OFFER,
	}
	projection1 = &rewardsProjectionPb.Projection{
		OfferId: "offer-1",
		Id:      "proj-1",
		ProjectedOptions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
			{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200},
		}},
		RewardContributions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
			{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200},
		}},
	}
	projection2 = &rewardsProjectionPb.Projection{
		OfferId: "offer-1",
		Id:      "proj-2",
		ProjectedOptions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
			{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 700},
		}},
		RewardContributions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
			{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 300},
		}},
	}
	projection3 = &rewardsProjectionPb.Projection{
		OfferId: "offer-1",
		Id:      "proj-3",
		ProjectedOptions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
			{RewardType: rewardsPb.RewardType_CASH, RewardUnits: 100},
		}},
		RewardContributions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
			{RewardType: rewardsPb.RewardType_CASH, RewardUnits: 100},
		}},
	}
	fiStoreRedemption1 = &evrPb.FiStoreRedemption{
		Id:                          "123",
		ActorId:                     "actor-1",
		Vendor:                      evrPb.Vendor_DPANDA,
		VendorRefId:                 "ref-12892",
		ProductId:                   "product-1",
		PaymentInstrumentIdentifier: "09090",
		ProductPrice: &money.Money{
			CurrencyCode: pkgMoney.RupeeCurrencyCode,
			Units:        1000,
		},
		SpentCashUnits: &money.Money{
			CurrencyCode: pkgMoney.RupeeCurrencyCode,
			Units:        800,
		},
		SpentFiCoinUnits: 2000,
		OrderStatus:      evrPb.OrderStatus_ORDER_STATUS_CONFIRMED,
		Category:         evrPb.Category_CATEGORY_ECOM,
		RedemptionMetaData: &evrPb.RedemptionMetaData{
			ProductName: "product-name-1",
			BrandName:   "brand-name-1",
			SubCategory: "sub-category-1",
			Quantity:    2,
		},
		OrderTimestamp: &timestamp.Timestamp{Seconds: 1643306013},
	}
	fiStoreRedemption2 = &evrPb.FiStoreRedemption{
		Id:                          "124",
		ActorId:                     "actor-1",
		Vendor:                      evrPb.Vendor_DPANDA,
		VendorRefId:                 "ref-12892",
		ProductId:                   "product-2",
		PaymentInstrumentIdentifier: "09090",
		ProductPrice: &money.Money{
			CurrencyCode: pkgMoney.RupeeCurrencyCode,
			Units:        1000,
		},
		SpentCashUnits: &money.Money{
			CurrencyCode: pkgMoney.RupeeCurrencyCode,
			Units:        800,
		},
		SpentFiCoinUnits: 2000,
		OrderStatus:      evrPb.OrderStatus_ORDER_STATUS_DELIVERED,
		Category:         evrPb.Category_CATEGORY_ECOM,
		RedemptionMetaData: &evrPb.RedemptionMetaData{
			ProductName:       "product-name-2",
			BrandName:         "brand-name-1",
			SubCategory:       "sub-category-1",
			Quantity:          2,
			OrderTrackingLink: "tracking-link",
		},
		OrderTimestamp: &timestamp.Timestamp{Seconds: 1643306013},
	}
)

func TestService_GetRewardOffers(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mAuthEngine := mockAuthEngine.NewMockIAuthEngine(ctr)
	mRewardsOffersClient := mockRewardOffers.NewMockRewardOffersClient(ctr)
	mOfferClient := mockCasper.NewMockOfferListingServiceClient(ctr)
	mRewardsClient := mockRewardsGenerator.NewMockRewardsGeneratorClient(ctr)
	mOfferRedemptionClient := mockCasperRedemption.NewMockOfferRedemptionServiceClient(ctr)
	mExchangerOfferClient := mockExchanger.NewMockExchangerOfferServiceClient(ctr)
	mockUsersClient := mockUser.NewMockUsersClient(ctr)

	defer func() {
		ctr.Finish()
	}()
	type args struct {
		mocks []interface{}
		ctx   context.Context
		req   *rePb.GetRewardOffersRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *rePb.GetRewardOffersResponse
		wantErr bool
	}{
		{
			name: "auth engine returns action required true",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(),
						getRewardsRequest1.GetHeader(), gomock.Any()).Return(true, sherlockDeepLink1),
				},
				req: getRewardsRequest1,
			},
			want: &rePb.GetRewardOffersResponse{
				Status:           rpcPb.StatusOk(),
				SherlockDeepLink: sherlockDeepLink1,
			},
			wantErr: false,
		},
		{
			name: "error while connecting to rewards service",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(),
						getRewardsRequest1.GetHeader(), gomock.Any()).Return(false, nil),
					mRewardsOffersClient.EXPECT().GetRewardOffers(context.Background(), gomock.Any()).Return(nil, fmt.Errorf("failed to connect")),
				},
				req: getRewardsRequest1,
			},
			want:    &rePb.GetRewardOffersResponse{Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch rewards")},
			wantErr: false,
		},
		{
			name: "error returned by rewards service",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(),
						getRewardsRequest1.GetHeader(), gomock.Any()).Return(false, nil),
					mRewardsOffersClient.EXPECT().GetRewardOffers(context.Background(), gomock.Any()).Return(rewardsErrorResponse, nil),
				},
				req: getRewardsRequest1,
			},
			want:    &rePb.GetRewardOffersResponse{Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch rewards")},
			wantErr: false,
		},
		{
			name: "success but empty list",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(),
						getRewardsRequest1.GetHeader(), gomock.Any()).Return(false, nil),
					mRewardsOffersClient.EXPECT().GetRewardOffers(context.Background(), gomock.Any()).Return(rewardsServiceEmptyListResponse, nil).Times(2),
				},
				req: getRewardsRequest1,
			},
			want: &rePb.GetRewardOffersResponse{
				Status: rpcPb.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(),
						getRewardsRequest1.GetHeader(), gomock.Any()).Return(false, nil),
					mRewardsOffersClient.EXPECT().GetRewardOffers(context.Background(), gomock.Any()).Return(rewardsServiceSuccessResponse, nil),
					mRewardsOffersClient.EXPECT().GetRewardOffers(context.Background(), gomock.Any()).Return(rewardsServiceEmptyListResponse, nil),
				},
				req: getRewardsRequest1,
			},
			want:    RewardsSuccessResponse,
			wantErr: false,
		},
		{
			name: "success when there are no non-visible reward offers",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(),
						getRewardsRequest1.GetHeader(), gomock.Any()).Return(false, nil),
					mRewardsOffersClient.EXPECT().GetRewardOffers(context.Background(), gomock.Any()).Return(rewardsServiceSuccessResponse, nil),
					mRewardsOffersClient.EXPECT().GetRewardOffers(context.Background(), gomock.Any()).Return(rewardsServiceEmptyListResponse, nil),
				},
				req: getRewardsRequest1,
			},
			want:    RewardsSuccessResponse,
			wantErr: false,
		},
		{
			name: "success when tere are non-visible reward offers",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(),
						getRewardsRequest1.GetHeader(), gomock.Any()).Return(false, nil),
					mRewardsOffersClient.EXPECT().GetRewardOffers(context.Background(), gomock.Any()).Return(rewardsServiceSuccessResponse, nil),
					mRewardsOffersClient.EXPECT().GetRewardOffers(context.Background(), gomock.Any()).Return(rewardsServiceSuccessResponse1, nil),
				},
				req: getRewardsRequest1,
			},
			want:    RewardsSuccessResponse1,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewService(mAuthEngine, mRewardsOffersClient, mOfferClient, mOfferRedemptionClient, mRewardsClient, mExchangerOfferClient, mockUsersClient, nil, nil, nil, nil)
			got, err := s.GetRewardOffers(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRewardOffers() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetRewardOffers() \ngot = %v\nwant= %v", got, tt.want)
			}
		})
	}
}

func TestService_GetOffers(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mAuthEngine := mockAuthEngine.NewMockIAuthEngine(ctr)
	mRewardsOffersClient := mockRewardOffers.NewMockRewardOffersClient(ctr)
	mOfferClient := mockCasper.NewMockOfferListingServiceClient(ctr)
	mRewardsClient := mockRewardsGenerator.NewMockRewardsGeneratorClient(ctr)
	mOfferRedemptionClient := mockCasperRedemption.NewMockOfferRedemptionServiceClient(ctr)
	mExchangerOfferClient := mockExchanger.NewMockExchangerOfferServiceClient(ctr)
	mockUsersClient := mockUser.NewMockUsersClient(ctr)

	defer func() {
		ctr.Finish()
	}()
	type args struct {
		mocks []interface{}
		ctx   context.Context
		req   *rePb.GetOffersRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *rePb.GetOffersResponse
		wantErr bool
	}{
		{
			name: "auth engine returns action required true",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(),
						getRewardsRequest1.GetHeader(), gomock.Any()).Return(true, sherlockDeepLink1),
				},
				req: getOffersRequest1,
			},
			want: &rePb.GetOffersResponse{
				Status:           rpcPb.StatusOk(),
				SherlockDeepLink: sherlockDeepLink1,
			},
			wantErr: false,
		},
		{
			name: "error while connecting to offers listing service",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(),
						getRewardsRequest1.GetHeader(), gomock.Any()).Return(false, nil),
					mOfferClient.EXPECT().GetOffersByFilters(context.Background(), gomock.Any()).Return(nil, fmt.Errorf("failed to connect")),
				},
				req: getOffersRequest1,
			},
			want:    &rePb.GetOffersResponse{Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch offers")},
			wantErr: false,
		},
		{
			name: "error returned by offers listing service",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(),
						getRewardsRequest1.GetHeader(), gomock.Any()).Return(false, nil),
					mOfferClient.EXPECT().GetOffersByFilters(context.Background(), gomock.Any()).Return(offersErrorResponse, nil),
				},
				req: getOffersRequest1,
			},
			want:    &rePb.GetOffersResponse{Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch offers")},
			wantErr: false,
		},
		{
			name: "success but empty list",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(),
						getRewardsRequest1.GetHeader(), gomock.Any()).Return(false, nil),
					mOfferClient.EXPECT().GetOffersByFilters(context.Background(), gomock.Any()).Return(offersEmptyListResponse, nil),
				},
				req: getOffersRequest1,
			},
			want: &rePb.GetOffersResponse{
				Status:      rpcPb.StatusOk(),
				PageContext: &cxPb.PageContextResponse{},
			},
			wantErr: false,
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(),
						getRewardsRequest1.GetHeader(), gomock.Any()).Return(false, nil),
					mOfferClient.EXPECT().GetOffersByFilters(context.Background(), gomock.Any()).Return(offersServiceSuccessResponse, nil),
				},
				req: getOffersRequest1,
			},
			want:    offerSuccessResponse,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewService(mAuthEngine, mRewardsOffersClient, mOfferClient, mOfferRedemptionClient, mRewardsClient, mExchangerOfferClient, mockUsersClient, nil, nil, nil, nil)
			got, err := s.GetOffers(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetOffers() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetOffers()\n got = %v\nwant = %v", got, tt.want)
			}
		})
	}
}

func TestService_GetExchangerOffers(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mAuthEngine := mockAuthEngine.NewMockIAuthEngine(ctr)
	mRewardsOffersClient := mockRewardOffers.NewMockRewardOffersClient(ctr)
	mOfferClient := mockCasper.NewMockOfferListingServiceClient(ctr)
	mRewardsClient := mockRewardsGenerator.NewMockRewardsGeneratorClient(ctr)
	mOfferRedemptionClient := mockCasperRedemption.NewMockOfferRedemptionServiceClient(ctr)
	mExchangerOfferClient := mockExchanger.NewMockExchangerOfferServiceClient(ctr)
	mockUsersClient := mockUser.NewMockUsersClient(ctr)

	defer func() {
		ctr.Finish()
	}()
	type args struct {
		mocks []interface{}
		ctx   context.Context
		req   *rePb.GetExchangerOffersRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *rePb.GetExchangerOffersResponse
		wantErr bool
	}{
		{
			name: "auth engine returns action required true",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(),
						getExchangerOffersRequest.GetHeader(), gomock.Any()).Return(true, sherlockDeepLink1),
				},
				req: getExchangerOffersRequest,
			},
			want: &rePb.GetExchangerOffersResponse{
				Status:           rpcPb.StatusOk(),
				SherlockDeepLink: sherlockDeepLink1,
			},
			wantErr: false,
		},
		{
			name: "error while connecting to exchanger offer service",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(),
						getExchangerOffersRequest.GetHeader(), gomock.Any()).Return(false, nil),
					mExchangerOfferClient.EXPECT().GetExchangerOffersByFilters(context.Background(), gomock.Any()).Return(nil, fmt.Errorf("failed to connect")),
				},
				req: getExchangerOffersRequest,
			},
			want:    &rePb.GetExchangerOffersResponse{Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch exchanger offers")},
			wantErr: false,
		},
		{
			name: "error returned by exchanger offers service",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(),
						getExchangerOffersRequest.GetHeader(), gomock.Any()).Return(false, nil),
					mExchangerOfferClient.EXPECT().GetExchangerOffersByFilters(context.Background(), gomock.Any()).Return(exchangerOffersErrorResponse, nil),
				},
				req: getExchangerOffersRequest,
			},
			want:    &rePb.GetExchangerOffersResponse{Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch exchanger offers")},
			wantErr: false,
		},
		{
			name: "success but empty list",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(),
						getExchangerOffersRequest.GetHeader(), gomock.Any()).Return(false, nil),
					mExchangerOfferClient.EXPECT().GetExchangerOffersByFilters(context.Background(), gomock.Any()).Return(exchangerOffersServiceEmptyListResponse, nil),
				},
				req: getExchangerOffersRequest,
			},
			want: &rePb.GetExchangerOffersResponse{
				Status: rpcPb.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(),
						getExchangerOffersRequest.GetHeader(), gomock.Any()).Return(false, nil),
					mExchangerOfferClient.EXPECT().GetExchangerOffersByFilters(context.Background(), gomock.Any()).Return(exchangerOffersServiceSuccessResponse, nil),
				},
				req: getExchangerOffersRequest,
			},
			want:    exchangerOffersSuccessResponse,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewService(mAuthEngine, mRewardsOffersClient, mOfferClient, mOfferRedemptionClient, mRewardsClient, mExchangerOfferClient, mockUsersClient, nil, nil, nil, nil)
			got, err := s.GetExchangerOffers(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetExchangerOffers() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetExchangerOffers()\n got = %v\nwant = %v", got, tt.want)
			}
		})
	}
}

func TestService_GetExchangerOfferOrders(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mAuthEngine := mockAuthEngine.NewMockIAuthEngine(ctr)
	mRewardsOffersClient := mockRewardOffers.NewMockRewardOffersClient(ctr)
	mOfferClient := mockCasper.NewMockOfferListingServiceClient(ctr)
	mRewardsClient := mockRewardsGenerator.NewMockRewardsGeneratorClient(ctr)
	mOfferRedemptionClient := mockCasperRedemption.NewMockOfferRedemptionServiceClient(ctr)
	mExchangerOfferClient := mockExchanger.NewMockExchangerOfferServiceClient(ctr)
	mockUsersClient := mockUser.NewMockUsersClient(ctr)

	defer func() {
		ctr.Finish()
	}()
	type args struct {
		mocks []interface{}
		ctx   context.Context
		req   *rePb.GetExchangerOfferOrdersRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *rePb.GetExchangerOfferOrdersResponse
		wantErr bool
	}{
		{
			name: "auth engine returns action required true",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(),
						getExchangerOfferOrdersRequest.GetHeader(), gomock.Any()).Return(true, sherlockDeepLink1),
				},
				req: getExchangerOfferOrdersRequest,
			},
			want: &rePb.GetExchangerOfferOrdersResponse{
				Status:           rpcPb.StatusOk(),
				SherlockDeepLink: sherlockDeepLink1,
			},
			wantErr: false,
		},
		{
			name: "error while connecting to exchanger offer order service",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(),
						getExchangerOfferOrdersRequest.GetHeader(), gomock.Any()).Return(false, nil),
					mExchangerOfferClient.EXPECT().GetExchangerOfferOrdersForActor(context.Background(), gomock.Any()).Return(nil, fmt.Errorf("failed to connect")),
				},
				req: getExchangerOfferOrdersRequest,
			},
			want:    &rePb.GetExchangerOfferOrdersResponse{Status: rpcPb.StatusInternalWithDebugMsg("error while fetching exchanger offer order details from exchanger offer order service")},
			wantErr: false,
		},
		{
			name: "error returned by exchanger offers service",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(),
						getExchangerOfferOrdersRequest.GetHeader(), gomock.Any()).Return(false, nil),
					mExchangerOfferClient.EXPECT().GetExchangerOfferOrdersForActor(context.Background(), gomock.Any()).Return(exchangerOfferOrdersErrorResponse, nil),
				},
				req: getExchangerOfferOrdersRequest,
			},
			want:    &rePb.GetExchangerOfferOrdersResponse{Status: rpcPb.StatusInternalWithDebugMsg("error while fetching exchanger offer order details from exchanger offer order service")},
			wantErr: false,
		},
		{
			name: "success but empty list",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(),
						getExchangerOfferOrdersRequest.GetHeader(), gomock.Any()).Return(false, nil),
					mExchangerOfferClient.EXPECT().GetExchangerOfferOrdersForActor(context.Background(), gomock.Any()).Return(exchangerOfferOrdersServiceEmptyListResponse, nil),
				},
				req: getExchangerOfferOrdersRequest,
			},
			want: &rePb.GetExchangerOfferOrdersResponse{
				Status: rpcPb.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(),
						getExchangerOfferOrdersRequest.GetHeader(), gomock.Any()).Return(false, nil),
					mExchangerOfferClient.EXPECT().GetExchangerOfferOrdersForActor(context.Background(), gomock.Any()).Return(exchangerOffersOrdersServiceSuccessResponse, nil),
				},
				req: getExchangerOfferOrdersRequest,
			},
			want:    exchangerOffersOrdersSuccessResponse,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewService(mAuthEngine, mRewardsOffersClient, mOfferClient, mOfferRedemptionClient, mRewardsClient, mExchangerOfferClient, mockUsersClient, nil, nil, nil, nil)
			got, err := s.GetExchangerOfferOrders(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetExchangerOfferOrders() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetExchangerOfferOrders() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetRedeemedOffers(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mAuthEngine := mockAuthEngine.NewMockIAuthEngine(ctr)
	mRewardsOffersClient := mockRewardOffers.NewMockRewardOffersClient(ctr)
	mOfferClient := mockCasper.NewMockOfferListingServiceClient(ctr)
	mRewardsClient := mockRewardsGenerator.NewMockRewardsGeneratorClient(ctr)
	mOfferRedemptionClient := mockCasperRedemption.NewMockOfferRedemptionServiceClient(ctr)
	mExchangerOfferClient := mockExchanger.NewMockExchangerOfferServiceClient(ctr)
	mockUsersClient := mockUser.NewMockUsersClient(ctr)

	defer func() {
		ctr.Finish()
	}()
	type args struct {
		mocks []interface{}
		ctx   context.Context
		req   *rePb.GetRedeemedOffersRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *rePb.GetRedeemedOffersResponse
		wantErr bool
	}{
		{
			name: "auth engine returns action required true",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(),
						getRedeemedOffersRequest.GetHeader(), gomock.Any()).Return(true, sherlockDeepLink1),
				},
				req: getRedeemedOffersRequest,
			},
			want: &rePb.GetRedeemedOffersResponse{
				Status:           rpcPb.StatusOk(),
				SherlockDeepLink: sherlockDeepLink1,
			},
			wantErr: false,
		},
		{
			name: "error while connecting to redemption service",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(),
						getRedeemedOffersRequest.GetHeader(), gomock.Any()).Return(false, nil),
					mOfferRedemptionClient.EXPECT().GetRedeemedOffersForActor(context.Background(), gomock.Any()).Return(nil, fmt.Errorf("error while fetching offer details from offer redemption service")),
				},
				req: getRedeemedOffersRequest,
			},
			want:    &rePb.GetRedeemedOffersResponse{Status: rpcPb.StatusInternalWithDebugMsg("error while fetching offer details from offer redemption service")},
			wantErr: false,
		},
		{
			name: "error returned by redemption service",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(),
						getRedeemedOffersRequest.GetHeader(), gomock.Any()).Return(false, nil),
					mOfferRedemptionClient.EXPECT().GetRedeemedOffersForActor(context.Background(), gomock.Any()).Return(redeemedOffersErrorResponse, nil),
				},
				req: getRedeemedOffersRequest,
			},
			want:    &rePb.GetRedeemedOffersResponse{Status: rpcPb.StatusInternalWithDebugMsg("error while fetching offer details from offer redemption service")},
			wantErr: false,
		},
		{
			name: "success but empty list",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(),
						getRedeemedOffersRequest.GetHeader(), gomock.Any()).Return(false, nil),
					mOfferRedemptionClient.EXPECT().GetRedeemedOffersForActor(context.Background(), gomock.Any()).Return(redeemedOffersServiceEmptyListResponse, nil),
				},
				req: getRedeemedOffersRequest,
			},
			want: &rePb.GetRedeemedOffersResponse{
				Status:         rpcPb.StatusOk(),
				PageContext:    &cxPb.PageContextResponse{},
				RedeemedOffers: []*redemption.RedeemedOffer{},
			},
			wantErr: false,
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(),
						getRedeemedOffersRequest.GetHeader(), gomock.Any()).Return(false, nil),
					mOfferRedemptionClient.EXPECT().GetRedeemedOffersForActor(context.Background(), gomock.Any()).Return(redeemedOffersServiceSuccessResponse, nil),
				},
				req: getRedeemedOffersRequest,
			},
			want:    redeemedOffersSuccessResponse,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewService(mAuthEngine, mRewardsOffersClient, mOfferClient, mOfferRedemptionClient, mRewardsClient, mExchangerOfferClient, mockUsersClient, nil, nil, nil, nil)
			got, err := s.GetRedeemedOffers(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRedeemedOffers() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetRedeemedOffers() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetRewardDetails(t *testing.T) {
	t.Parallel()
	fromTime := time.Now().In(datetime.IST).AddDate(0, 0, -3)
	tillTime := time.Now()
	type args struct {
		ctx context.Context
		req *rePb.GetRewardDetailsRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockProjectionsClient *mocks.MockProjectorServiceClient, mockRewardOffersClient *mockRewardOffers.MockRewardOffersClient)
		want       *rePb.GetRewardDetailsResponse
		wantErr    bool
	}{
		{
			name: "invalid arg",
			args: args{
				ctx: context.Background(),
				req: &rePb.GetRewardDetailsRequest{
					FromDate:      nil,
					ToDate:        nil,
					ExternalTxnId: "",
				},
			},
			setupMocks: func(mockProjectionsClient *mocks.MockProjectorServiceClient, mockRewardOffersClient *mockRewardOffers.MockRewardOffersClient) {
			},
			want: &rePb.GetRewardDetailsResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("Either date range or external txn id is mandatory"),
			},
			wantErr: false,
		},
		{
			name: "internal error",
			args: args{
				ctx: context.Background(),
				req: &rePb.GetRewardDetailsRequest{
					RewardOfferType: "REWARD_OFFER_TYPES_OPTIONS_INFINITE_OR_SALARY_TIER_2_PERCENT_CASHBACK_OFFER",
					FromDate:        timestampPb.New(fromTime),
					ToDate:          timestampPb.New(tillTime),
					ExternalTxnId:   "",
				},
			},
			setupMocks: func(mockProjectionsClient *mocks.MockProjectorServiceClient, mockRewardOffersClient *mockRewardOffers.MockRewardOffersClient) {
				mockProjectionsClient.EXPECT().GetRewardsProjections(gomock.Any(), gomock.Any()).Return(&rewardsProjectionPb.GetRewardsProjectionsResponse{
					Status: rpcPb.StatusInternal(),
				}, nil)
			},
			want: &rePb.GetRewardDetailsResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error while fetching reward details"),
			},
			wantErr: false,
		},
		{
			name: "success on ref id",
			args: args{
				ctx: context.Background(),
				req: &rePb.GetRewardDetailsRequest{
					RewardOfferType: "REWARD_OFFER_TYPES_OPTIONS_INFINITE_OR_SALARY_TIER_2_PERCENT_CASHBACK_OFFER",
					ExternalTxnId:   "1234-ref-id",
				},
			},
			setupMocks: func(mockProjectionsClient *mocks.MockProjectorServiceClient, mockRewardOffersClient *mockRewardOffers.MockRewardOffersClient) {
				mockProjectionsClient.EXPECT().GetRewardsProjections(gomock.Any(), gomock.Any()).Return(&rewardsProjectionPb.GetRewardsProjectionsResponse{
					Status: rpcPb.StatusOk(),
					Projections: &rewardsProjectionPb.GetRewardsProjectionsResponse_IndividualProjections_{
						IndividualProjections: &rewardsProjectionPb.GetRewardsProjectionsResponse_IndividualProjections{
							Projections: []*rewardsProjectionPb.Projection{projection1, projection2, projection3},
						},
					},
					PageCtxResponse: nil,
				}, nil)
				mockRewardOffersClient.EXPECT().GetRewardOffersByIds(gomock.Any(), &rewardOfferPb.GetRewardOffersByIdsRequest{Ids: []string{rewardOffer1.GetId()}}).Return(&rewardOfferPb.GetRewardOffersByIdsResponse{
					Status:       rpcPb.StatusOk(),
					RewardOffers: []*rewardOfferPb.RewardOffer{rewardOffer1},
				}, nil)
			},
			want: &rePb.GetRewardDetailsResponse{
				Status:             rpcPb.StatusOk(),
				RewardDetailsTable: getRewardDetailsTable([]*rewardsProjectionPb.Projection{projection1, projection2, projection3}, map[string]*rewardOfferPb.RewardOffer{rewardOffer1.GetId(): rewardOffer1}),
			},
			wantErr: false,
		},
		{
			name: "success on time window",
			args: args{
				ctx: context.Background(),
				req: &rePb.GetRewardDetailsRequest{
					RewardOfferType: "REWARD_OFFER_TYPES_OPTIONS_INFINITE_OR_SALARY_TIER_2_PERCENT_CASHBACK_OFFER",
					FromDate:        timestampPb.New(fromTime),
					ToDate:          timestampPb.New(tillTime),
					ExternalTxnId:   "",
				},
			},
			setupMocks: func(mockProjectionsClient *mocks.MockProjectorServiceClient, mockRewardOffersClient *mockRewardOffers.MockRewardOffersClient) {
				mockProjectionsClient.EXPECT().GetRewardsProjections(gomock.Any(), gomock.Any()).Return(&rewardsProjectionPb.GetRewardsProjectionsResponse{
					Status: rpcPb.StatusOk(),
					Projections: &rewardsProjectionPb.GetRewardsProjectionsResponse_IndividualProjections_{
						IndividualProjections: &rewardsProjectionPb.GetRewardsProjectionsResponse_IndividualProjections{
							Projections: []*rewardsProjectionPb.Projection{projection1, projection2},
						},
					},
					PageCtxResponse: &rpcPb.PageContextResponse{
						BeforeToken: "before-token",
						HasBefore:   true,
						AfterToken:  "after-token",
						HasAfter:    true,
					},
				}, nil).Times(1)
				mockProjectionsClient.EXPECT().GetRewardsProjections(gomock.Any(), gomock.Any()).Return(&rewardsProjectionPb.GetRewardsProjectionsResponse{
					Status: rpcPb.StatusOk(),
					Projections: &rewardsProjectionPb.GetRewardsProjectionsResponse_IndividualProjections_{
						IndividualProjections: &rewardsProjectionPb.GetRewardsProjectionsResponse_IndividualProjections{
							Projections: []*rewardsProjectionPb.Projection{projection3},
						},
					},
					PageCtxResponse: &rpcPb.PageContextResponse{
						BeforeToken: "before-token",
						HasBefore:   true,
						AfterToken:  "",
						HasAfter:    false,
					},
				}, nil).Times(1)
				mockRewardOffersClient.EXPECT().GetRewardOffersByIds(gomock.Any(), &rewardOfferPb.GetRewardOffersByIdsRequest{Ids: []string{rewardOffer1.GetId()}}).Return(&rewardOfferPb.GetRewardOffersByIdsResponse{
					Status:       rpcPb.StatusOk(),
					RewardOffers: []*rewardOfferPb.RewardOffer{rewardOffer1},
				}, nil)
			},
			want: &rePb.GetRewardDetailsResponse{
				Status:             rpcPb.StatusOk(),
				RewardDetailsTable: getRewardDetailsTable([]*rewardsProjectionPb.Projection{projection1, projection2, projection3}, map[string]*rewardOfferPb.RewardOffer{rewardOffer1.GetId(): rewardOffer1}),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			mockProjectionsClient := mocks.NewMockProjectorServiceClient(ctr)
			mockRewardOffersClient := mockRewardOffers.NewMockRewardOffersClient(ctr)
			defer func() {
				ctr.Finish()
			}()

			tt.setupMocks(mockProjectionsClient, mockRewardOffersClient)

			s := NewService(nil, mockRewardOffersClient, nil, nil, nil, nil, nil, mockProjectionsClient, nil, nil, nil)
			got, err := s.GetRewardDetails(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRewardDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("GetRewardDetails() diff = %s", diff)
			}
		})
	}
}

func TestService_GetFiStoreRedemptionsDetails(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	defer func() {
		ctr.Finish()
	}()

	fromTime := time.Now().In(datetime.IST).AddDate(0, 0, -3)
	tillTime := time.Now()
	type args struct {
		ctx context.Context
		req *rePb.GetFiStoreRedemptionsDetailsRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(client *mocks2.MockExternalVendorRedemptionServiceClient)
		want       *rePb.GetFiStoreRedemptionsDetailsResponse
		wantErr    bool
	}{
		{
			name: "should return internal error, when GetFiStoreRedemptions rpc returns any error",
			args: args{
				ctx: context.Background(),
				req: &rePb.GetFiStoreRedemptionsDetailsRequest{
					FromDate: timestampPb.New(fromTime),
					ToDate:   timestampPb.New(tillTime),
					Category: evrPb.Category_CATEGORY_ECOM,
				},
			},
			setupMocks: func(client *mocks2.MockExternalVendorRedemptionServiceClient) {
				client.EXPECT().GetFiStoreRedemptions(gomock.Any(), gomock.Any()).Return(&evrPb.GetFiStoreRedemptionsResponse{
					Status: rpcPb.StatusInternalWithDebugMsg("error while fetching fi store redemptions"),
				}, nil)
			},
			want: &rePb.GetFiStoreRedemptionsDetailsResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error while fetching fi store redemptions"),
			},
			wantErr: false,
		},
		{
			name: "should return success, when GetFiStoreRedemptions rpc is successful",
			args: args{
				ctx: context.Background(),
				req: &rePb.GetFiStoreRedemptionsDetailsRequest{
					FromDate: timestampPb.New(fromTime),
					ToDate:   timestampPb.New(tillTime),
					Category: evrPb.Category_CATEGORY_ECOM,
				},
			},
			setupMocks: func(client *mocks2.MockExternalVendorRedemptionServiceClient) {
				client.EXPECT().GetFiStoreRedemptions(gomock.Any(), gomock.Any()).Return(&evrPb.GetFiStoreRedemptionsResponse{
					Status:      rpcPb.StatusOk(),
					Redemptions: []*evrPb.FiStoreRedemption{fiStoreRedemption1, fiStoreRedemption2},
				}, nil)
			},
			want: &rePb.GetFiStoreRedemptionsDetailsResponse{
				Status:                    rpcPb.StatusOk(),
				PageContext:               helper.ConvertToCxPageContext(nil),
				FiStoreRewardDetailsTable: getFiStoreRedemptionsDetailsTable([]*evrPb.FiStoreRedemption{fiStoreRedemption1, fiStoreRedemption2}),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockExternalVendorRedemptionClient := mocks2.NewMockExternalVendorRedemptionServiceClient(ctr)
			tt.setupMocks(mockExternalVendorRedemptionClient)
			s := NewService(nil, nil, nil, nil, nil, nil, nil, nil, mockExternalVendorRedemptionClient, nil, nil)
			got, err := s.GetFiStoreRedemptionsDetails(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFiStoreRedemptionsDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("GetFiStoreRedemptionsDetails() diff = %s", diff)
			}
		})
	}
}
