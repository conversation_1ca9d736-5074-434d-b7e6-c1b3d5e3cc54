package transaction

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	mocks3 "github.com/epifi/gamma/api/recurringpayment/enach/mocks"
	"github.com/epifi/gamma/api/upi/mandate/mocks"

	types "github.com/epifi/gamma/api/typesv2"

	"context"
	"flag"
	"fmt"
	"os"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"
	"github.com/pkg/errors"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/golang/mock/gomock"

	rpcPb "github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/gamma/api/cx"
	caPb "github.com/epifi/gamma/api/cx/customer_auth"
	tPb "github.com/epifi/gamma/api/cx/data_collector/transaction"
	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/paymentinstrument"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/webui"
	cxConfig "github.com/epifi/gamma/cx/config"
	mocks2 "github.com/epifi/gamma/cx/data_collector/helper/mocks"
	"github.com/epifi/gamma/cx/test"
	mockAuthEngine "github.com/epifi/gamma/cx/test/mocks/customer_auth/auth_engine"
	mockHelper "github.com/epifi/gamma/cx/test/mocks/data_collector/helper"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	conf, _, _, teardown := test.InitTestServer(false)
	tdcTS = TxnDataCollectorTestSuite{
		orderConfig: conf.OrderConfig,
		txnConfig:   conf.Transaction,
	}
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

type TxnDataCollectorTestSuite struct {
	orderConfig *cxConfig.OrderConfig
	txnConfig   *cxConfig.Transaction
}

var (
	tdcTS            TxnDataCollectorTestSuite
	merchantDetails1 = &tPb.MerchantDetails{
		MerchantId: "test-to",
		MerchantName: &commontypes.Name{
			FirstName:  "first",
			MiddleName: "middle",
			LastName:   "last",
		},
	}
	orderWithTransactionA = &tPb.OrderWithTransaction{
		OrderId:         "orderId",
		OrderExternalId: "XXXXXXerID",
		Utr:             "utrNo",
		FromActorId:     "test-from",
		ToActorId:       "test-to",
		TransactionType: tPb.TransactionType_DEBIT,
		OrderTags:       []string{"MERCHANT"},
		MerchantDetails: merchantDetails1,
		TxnStatus:       paymentPb.TransactionStatus_SUCCESS.String(),
		PartnerBank:     "VENDOR_UNSPECIFIED",
		OrderProvenance: "ORDER_PROVENANCE_UNSPECIFIED",
		OrderWorkflow:   "ORDER_WORKFLOW_UNSPECIFIED",
		PaymentProtocol: typesPb.PaymentProtocol_UPI.String(),
		ProtocolStatus:  "PROTOCOL_STATUS_UNSPECIFIED",
		DetailedStatus:  &tPb.TransactionDetailedStatus{},
		InternalTxnId:   "INT-TX-1",
		ToPiDetails: &tPb.PIDetails{
			PiType: paymentinstrument.PaymentInstrumentType_UPI.String(),
		},
		FromPiDetails: &tPb.PIDetails{
			PiType: paymentinstrument.PaymentInstrumentType_UPI.String(),
		},
		Amount: &money.Money{
			CurrencyCode: "INR",
			Units:        1200,
			Nanos:        *********,
		},
		AttachEntityMeta: &ticketPb.AttachEntityMeta{
			Meta: &ticketPb.AttachEntityMeta_OrderWithTransactionMeta{
				OrderWithTransactionMeta: &ticketPb.OrderWithTransactionMeta{
					TransactionId: "INT-TX-1",
				},
			},
		},

		TxnCreatedAt: timestamppb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, datetime.IST)),
	}
	sherlockDeeplink1 = &caPb.SherlockDeepLink{Screen: caPb.Screen_MODERATELY_SENSITIVE_SCREEN}
	orderWithTxnList1 = []*tPb.OrderWithTransaction{orderWithTransactionA}
)

func TestService_GetFirstOrLastNTransactions(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mAuthEngine := mockAuthEngine.NewMockIAuthEngine(ctr)
	mTxnDataCollectorHelper := mockHelper.NewMockITransactionsDataCollectorHelper(ctr)
	mockTxnCategoryHelper := mocks2.NewMockTxnCategoryDataCollectorHelper(ctr)
	mockUpiMandateClient := mocks.NewMockMandateServiceClient(ctr)
	mockEnachMandateClient := mocks3.NewMockEnachServiceClient(ctr)

	defer func() {
		ctr.Finish()
	}()
	type args struct {
		mocks []interface{}
		ctx   context.Context
		req   *tPb.GetFirstOrLastNTransactionsRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *tPb.GetFirstOrLastNTransactionsResponse
		wantErr bool
	}{
		{
			name: "Auth required",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(), gomock.Any(), gomock.Any()).
						Return(true, sherlockDeeplink1),
				},
			},
			want: &tPb.GetFirstOrLastNTransactionsResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeeplink1},
		},
		{
			name: "failed to fetch merchant details from actor service: record not found",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(), gomock.Any(), gomock.Any()).
						Return(false, nil),
					mTxnDataCollectorHelper.EXPECT().GetFirstOrLastNTransactions(context.Background(), gomock.Any(), tPb.GetFirstOrLastNTransactionsRequest_FIRST, 5).
						Return(nil, errors.Wrap(epifierrors.ErrRecordNotFound, "test error")),
				},
				req: &tPb.GetFirstOrLastNTransactionsRequest{
					Header: &cx.Header{
						Actor: &types.Actor{
							Id: "actor-id1",
						},
					},
					Type: tPb.GetFirstOrLastNTransactionsRequest_FIRST,
				},
			},
			want: &tPb.GetFirstOrLastNTransactionsResponse{
				Status: rpcPb.StatusRecordNotFoundWithDebugMsg("test error: record not found"),
			},
		},
		{
			name: "failed to fetch merchant details from actor service",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(), gomock.Any(), gomock.Any()).
						Return(false, nil),
					mTxnDataCollectorHelper.EXPECT().GetFirstOrLastNTransactions(context.Background(), gomock.Any(), tPb.GetFirstOrLastNTransactionsRequest_FIRST, 5).
						Return(nil, errors.New("test error")),
				},
				req: &tPb.GetFirstOrLastNTransactionsRequest{
					Header: &cx.Header{
						Actor: &types.Actor{
							Id: "actor-id1",
						},
					},
					Type: tPb.GetFirstOrLastNTransactionsRequest_FIRST,
				},
			},
			want: &tPb.GetFirstOrLastNTransactionsResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("test error"),
			},
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(), gomock.Any(), gomock.Any()).
						Return(false, nil),
					mTxnDataCollectorHelper.EXPECT().GetFirstOrLastNTransactions(context.Background(), gomock.Any(), tPb.GetFirstOrLastNTransactionsRequest_FIRST, 5).
						Return(orderWithTxnList1, nil),
					mockTxnCategoryHelper.EXPECT().GetCategoriesForOrders(gomock.Any(), "actor-id1", []string{"orderId"}).Return(
						map[string][]string{
							"orderId": {"category1", "category2"},
						}, nil),
				},
				req: &tPb.GetFirstOrLastNTransactionsRequest{
					Header: &cx.Header{
						Actor: &types.Actor{
							Id: "actor-id1",
						},
					},
					Type: tPb.GetFirstOrLastNTransactionsRequest_FIRST,
				},
			},
			want: &tPb.GetFirstOrLastNTransactionsResponse{
				Status:               rpcPb.StatusOk(),
				OrderWithTransaction: orderWithTxnList1,
				TransactionDetails: &webui.Table{
					TableName: "First or Last 5 Transactions",
					TableHeaders: []*webui.TableHeader{
						{Label: "Fi Txn ID", HeaderKey: "fiTxnId"},
						{Label: "UTR no/ URN", HeaderKey: "utr"},
						{Label: "Amount", HeaderKey: "amount"},
						{Label: "Incoming / Outgoing", HeaderKey: "incomingOutgoing"},
						{Label: "Txn Mode", HeaderKey: "txnMode"},
						{Label: "Status", HeaderKey: "status"},
						{Label: "From Instr", HeaderKey: "fromInstr"},
						{Label: "To Instr", HeaderKey: "toInstr"},
						{Label: "Created At", HeaderKey: "createdAt"},
						{Label: "Order Tags", HeaderKey: "orderTags"},
						{Label: "Merchant", HeaderKey: "merchant"},
						{Label: "DS Categories", HeaderKey: "dsCategories"},
					},
					Actions: []*webui.CTA{
						{Label: "view-details"},
						{Label: "view-dispute-details"},
						{Label: "raise-dispute"},
					},
					TableRows: []*webui.TableRow{
						{
							HeaderKeyToCellValueMap: map[string]string{
								"fiTxnId":          "XXXXXXerID",
								"utr":              "utrNo",
								"amount":           "INR 1200.30",
								"incomingOutgoing": "Outgoing",
								"txnMode":          "UPI",
								"status":           "SUCCESS",
								"fromInstr":        "UPI",
								"toInstr":          "UPI",
								"createdAt":        "2023-01-01 00:00:00 +0530 IST",
								"orderTags":        "MERCHANT",
								"merchant":         "first middle last",
								"dsCategories":     "category1,category2",
							},
						},
					},
				},
			},
		},
		{
			name: "success with no txn categories",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(), gomock.Any(), gomock.Any()).
						Return(false, nil),
					mTxnDataCollectorHelper.EXPECT().GetFirstOrLastNTransactions(context.Background(), gomock.Any(), tPb.GetFirstOrLastNTransactionsRequest_FIRST, 5).
						Return(orderWithTxnList1, nil),
					mockTxnCategoryHelper.EXPECT().GetCategoriesForOrders(gomock.Any(), "actor-id1", []string{"orderId"}).Return(
						map[string][]string{}, nil),
				},
				req: &tPb.GetFirstOrLastNTransactionsRequest{
					Header: &cx.Header{
						Actor: &types.Actor{
							Id: "actor-id1",
						},
					},
					Type: tPb.GetFirstOrLastNTransactionsRequest_FIRST,
				},
			},
			want: &tPb.GetFirstOrLastNTransactionsResponse{
				Status:               rpcPb.StatusOk(),
				OrderWithTransaction: orderWithTxnList1,
				TransactionDetails: &webui.Table{
					TableName: "First or Last 5 Transactions",
					TableHeaders: []*webui.TableHeader{
						{Label: FiTxnIdLabel, HeaderKey: FiTxnIdKey},
						{Label: UtrLabel, HeaderKey: UtrKey},
						{Label: AmountLabel, HeaderKey: AmountKey},
						{Label: IncomingOutgoingLabel, HeaderKey: IncomingOutgoingKey},
						{Label: TxnModeLabel, HeaderKey: TxnModeKey},
						{Label: StatusLabel, HeaderKey: StatusKey},
						{Label: FromInstrLabel, HeaderKey: FromInstrKey},
						{Label: ToInstrLabel, HeaderKey: ToInstrKey},
						{Label: CreatedAtLabel, HeaderKey: CreatedAtKey},
						{Label: OrderTagsLabel, HeaderKey: OrderTagsKey},
						{Label: MerchantLabel, HeaderKey: MerchantKey},
						{Label: DsCategoriesLabel, HeaderKey: DsCategoriesKey},
					},
					Actions: []*webui.CTA{
						{Label: ViewDetailsCtaLabel},
						{Label: ViewDisputeDetailsCtaLabel},
						{Label: RaiseDisputeCtaLabel},
					},
					TableRows: []*webui.TableRow{
						{
							HeaderKeyToCellValueMap: map[string]string{
								FiTxnIdKey:          "XXXXXXerID",
								UtrKey:              "utrNo",
								AmountKey:           "INR 1200.30",
								IncomingOutgoingKey: "Outgoing",
								TxnModeKey:          "UPI",
								StatusKey:           "SUCCESS",
								FromInstrKey:        "UPI",
								ToInstrKey:          "UPI",
								CreatedAtKey:        "2023-01-01 00:00:00 +0530 IST",
								OrderTagsKey:        "MERCHANT",
								MerchantKey:         "first middle last",
								DsCategoriesKey:     "",
							},
						},
					},
				},
			},
		},
		{
			name: "success with no orders",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(), gomock.Any(), gomock.Any()).
						Return(false, nil),
					mTxnDataCollectorHelper.EXPECT().GetFirstOrLastNTransactions(context.Background(), gomock.Any(), tPb.GetFirstOrLastNTransactionsRequest_FIRST, 5).
						Return([]*tPb.OrderWithTransaction{}, nil),
					mockTxnCategoryHelper.EXPECT().GetCategoriesForOrders(gomock.Any(), "actor-id1", nil).Return(nil, nil),
				},
				req: &tPb.GetFirstOrLastNTransactionsRequest{
					Header: &cx.Header{
						Actor: &types.Actor{
							Id: "actor-id1",
						},
					},
					Type: tPb.GetFirstOrLastNTransactionsRequest_FIRST,
				},
			},
			want: &tPb.GetFirstOrLastNTransactionsResponse{
				Status:               rpcPb.StatusOk(),
				OrderWithTransaction: []*tPb.OrderWithTransaction{},
				TransactionDetails: &webui.Table{
					TableName: "First or Last 5 Transactions",
					TableHeaders: []*webui.TableHeader{
						{Label: "Fi Txn ID", HeaderKey: "fiTxnId"},
						{Label: "UTR no/ URN", HeaderKey: "utr"},
						{Label: "Amount", HeaderKey: "amount"},
						{Label: "Incoming / Outgoing", HeaderKey: "incomingOutgoing"},
						{Label: "Txn Mode", HeaderKey: "txnMode"},
						{Label: "Status", HeaderKey: "status"},
						{Label: "From Instr", HeaderKey: "fromInstr"},
						{Label: "To Instr", HeaderKey: "toInstr"},
						{Label: "Created At", HeaderKey: "createdAt"},
						{Label: "Order Tags", HeaderKey: "orderTags"},
						{Label: "Merchant", HeaderKey: "merchant"},
						{Label: "DS Categories", HeaderKey: "dsCategories"},
					},
					Actions: []*webui.CTA{
						{Label: "view-details"},
						{Label: "view-dispute-details"},
						{Label: "raise-dispute"},
					},
					TableRows: []*webui.TableRow{},
				},
			},
		},
		{
			name: "failed to get txn categories",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(), gomock.Any(), gomock.Any()).
						Return(false, nil),
					mTxnDataCollectorHelper.EXPECT().GetFirstOrLastNTransactions(context.Background(), gomock.Any(), tPb.GetFirstOrLastNTransactionsRequest_FIRST, 5).
						Return(orderWithTxnList1, nil),
					mockTxnCategoryHelper.EXPECT().GetCategoriesForOrders(gomock.Any(), "actor-id1", []string{"orderId"}).Return(
						nil, fmt.Errorf("error")),
				},
				req: &tPb.GetFirstOrLastNTransactionsRequest{
					Header: &cx.Header{
						Actor: &types.Actor{
							Id: "actor-id1",
						},
					},
					Type: tPb.GetFirstOrLastNTransactionsRequest_FIRST,
				},
			},
			want: &tPb.GetFirstOrLastNTransactionsResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("Unable to get ds categories for the orders"),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewService(nil, mAuthEngine, nil, nil, nil, tdcTS.orderConfig, mTxnDataCollectorHelper, mockTxnCategoryHelper, nil, mockUpiMandateClient, mockEnachMandateClient)
			got, gotErr := s.GetFirstOrLastNTransactions(tt.args.ctx, tt.args.req)
			if (gotErr != nil) != tt.wantErr {
				t.Errorf("GetFirstOrLastNTransactions() gotErr :%v wantErr :%v", gotErr, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("GetFirstOrLastNTransactions() got = %v \n want %v \n diff %v", got, tt.want, diff)
			}
		})
	}
}
