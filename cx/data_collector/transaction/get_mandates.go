package transaction

import (
	"context"
	"fmt"

	cmap "github.com/orcaman/concurrent-map"

	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	tPb "github.com/epifi/gamma/api/cx/data_collector/transaction"
	recurringPay "github.com/epifi/gamma/api/recurringpayment"
	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	"github.com/epifi/gamma/api/typesv2/webui"
	upiMandatePb "github.com/epifi/gamma/api/upi/mandate"
	cxLogger "github.com/epifi/gamma/cx/logger"
)

func (s *Service) GetMandates(ctx context.Context, req *tPb.GetMandatesRequest) (*tPb.GetMandatesResponse, error) {
	var (
		res                          = &tPb.GetMandatesResponse{}
		recurringPaymentForActorReq  *recurringPay.GetRecurringPaymentsForActorRequest
		recurringPaymentForActorResp *recurringPay.GetRecurringPaymentsForActorResponse
		err                          error
	)

	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here

	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		res.Status = rpcPb.StatusOk()
		res.SherlockDeepLink = sherlockDeepLink
		return res, nil
	}

	if req.GetHeader().GetActor() == nil {
		res.Status = rpcPb.StatusInvalidArgumentWithDebugMsg("actor info not present in header")
		return res, nil
	}

	recurringPaymentForActorReq = &recurringPay.GetRecurringPaymentsForActorRequest{
		CurrentActorId: req.GetHeader().GetActor().GetId(),
		PageSize:       s.txnConfig.PageSizeForExistingMandates,
		Descending:     true,
		StartTimestamp: timestampPb.Now(),
	}

	recurringPaymentForActorResp, err = s.recurringPaymentsClient.GetRecurringPaymentsForActor(ctx, recurringPaymentForActorReq)
	if err = epifigrpc.RPCError(recurringPaymentForActorResp, err); err != nil {
		if recurringPaymentForActorResp.GetStatus().IsRecordNotFound() {
			res.Status = rpcPb.StatusRecordNotFoundWithDebugMsg("no mandates found for the given actor")
			return res, nil
		}

		cxLogger.Error(ctx, "unable to get active recurring payments for given actor", zap.Error(err))
		res.Status = rpcPb.StatusInternalWithDebugMsg("unable to get active recurring payments for given actor")
		return res, nil
	}

	res, err = s.createExistingMandatesResponse(ctx, recurringPaymentForActorResp.GetRecurringPayments())
	if err != nil {
		cxLogger.Error(ctx, "unable to create table view for get existing mandates response", zap.Error(err))
		res.Status = rpcPb.StatusInternalWithDebugMsg("unable to create table view for get existing mandates response")
		return res, nil
	}

	res.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) createExistingMandatesResponse(ctx context.Context, mandatesList []*recurringPay.GetRecurringPaymentsForActorResponse_RecurringPaymentTile) (
	*tPb.GetMandatesResponse, error) {
	var (
		getActiveMandatesResp = &tPb.GetMandatesResponse{}
		tableRows             []*webui.TableRow
		err                   error
	)

	getActiveMandatesResp.MandateDetails = createExistingMandatesTable()

	tableRows, err = s.createExistingMandateRowData(ctx, mandatesList)
	if err != nil {
		return nil, fmt.Errorf("failed to populate data in table rows: %w", err)
	}
	getActiveMandatesResp.MandateDetails.TableRows = tableRows

	return getActiveMandatesResp, nil
}

func createExistingMandatesTable() *webui.Table {
	return &webui.Table{
		TableName: ExistingMandatesTableName,
		TableHeaders: []*webui.TableHeader{
			{Label: MerchantNameLabel, HeaderKey: MerchantNameKey},
			{Label: MandateLimitLabel, HeaderKey: MandateLimitKey},
			{Label: StartDateLabel, HeaderKey: StartDateKey},
			{Label: EndDateLabel, HeaderKey: EndDateKey},
			{Label: MandateFrequencyLabel, HeaderKey: MandateFrequencyKey},
			{Label: MandateStateLabel, HeaderKey: MandateStateKey},
			{Label: MandateTypeLabel, HeaderKey: MandateTypeKey},
			{Label: MandateExternalIdLabel, HeaderKey: MandateExternalIdKey},
			{Label: MandateReferenceLabel, HeaderKey: MandateReferenceKey},
		},
	}
}

func (s *Service) createExistingMandateRowData(ctx context.Context, mandatesList []*recurringPay.GetRecurringPaymentsForActorResponse_RecurringPaymentTile) (
	[]*webui.TableRow, error) {

	var tableRows []*webui.TableRow
	if len(mandatesList) == 0 {
		cxLogger.Info(ctx, "no mandates found to create table rows")
		return tableRows, nil
	}

	mandateRows := cmap.New()
	g, gCtx := errgroup.WithContext(ctx)

	for _, mandate := range mandatesList {
		mandate := mandate
		g.Go(func() error {
			amount, err := s.convertMoneyToString(mandate.GetAmount())
			if err != nil {
				cxLogger.Error(gCtx, "failed to convert money to string for mandate",
					zap.String(logger.RECURRING_PAYMENT_ID, mandate.GetId()),
					zap.Error(err))
				return fmt.Errorf("failed to convert money to string for mandate id: %s, %w", mandate.GetId(), err)
			}

			mandateReference := s.getMandateReferenceNumber(gCtx, mandate.GetId(), mandate.GetType())

			tableRow := &webui.TableRow{
				HeaderKeyCellMap: map[string]*webui.TableCell{
					MandateExternalIdKey: createTableCell(mandate.GetExternalId(), nil),
					MerchantNameKey:      createTableCell(mandate.GetName(), nil),
					MandateLimitKey:      createTableCell(amount, nil),
					StartDateKey:         createTableCell(getStartDateString(mandate), nil),
					EndDateKey:           createTableCell(getEndDateString(mandate), nil),
					MandateFrequencyKey:  createTableCell(mandate.GetAllowedFrequency().String(), nil),
					MandateStateKey:      createTableCell(mandate.GetState().String(), nil),
					MandateTypeKey:       createTableCell(mandate.GetType().String(), nil),
					MandateReferenceKey:  createTableCell(mandateReference, nil),
				},
			}
			mandateRows.Set(mandate.GetId(), tableRow)
			return nil
		})
	}

	if err := g.Wait(); err != nil {
		return nil, err
	}

	if mandateRows.Count() == 0 {
		cxLogger.Info(ctx, "mandate concurrent map is empty after processing")
		return tableRows, nil
	}

	// Collect results in the order of the original mandatesList
	for _, mandate := range mandatesList {
		if val, ok := mandateRows.Get(mandate.GetId()); ok {
			if row, isRow := val.(*webui.TableRow); isRow {
				tableRows = append(tableRows, row)
			}
		}
	}

	return tableRows, nil
}

// getMandateReferenceNumber fetches the appropriate reference number based on mandate type
// TODO: handling for SI and DC mandates can be added later if required
func (s *Service) getMandateReferenceNumber(ctx context.Context, recurringPaymentId string, mandateType recurringPay.RecurringPaymentType) string {
	switch mandateType {
	case recurringPay.RecurringPaymentType_UPI_MANDATES:
		return s.getUpiMandateUmn(ctx, recurringPaymentId)
	case recurringPay.RecurringPaymentType_ENACH_MANDATES:
		return s.getEnachMandateUmrn(ctx, recurringPaymentId)
	default:
		return "N/A"
	}
}

// getUpiMandateUmn fetches UMN for UPI mandates
func (s *Service) getUpiMandateUmn(ctx context.Context, recurringPaymentId string) string {
	mandateDetailsRes, err := s.upiMandateClient.GetMandateDetails(ctx, &upiMandatePb.GetMandateDetailsRequest{
		RecurringPaymentId: recurringPaymentId,
	})
	if rpcErr := epifigrpc.RPCError(mandateDetailsRes, err); rpcErr != nil {
		cxLogger.Error(ctx, "failed to fetch UPI mandate details", zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId), zap.Error(rpcErr))
		return "Error fetching UMN"
	}

	if mandateDetailsRes.GetUmn() == "" {
		return "UMN not available"
	}
	return mandateDetailsRes.GetUmn()
}

// getEnachMandateUmrn fetches UMRN for ENACH mandates
func (s *Service) getEnachMandateUmrn(ctx context.Context, recurringPaymentId string) string {
	enachMandateRes, err := s.enachMandateClient.GetEnachMandate(ctx, &enachPb.GetEnachMandateRequest{
		Identifier: &enachPb.GetEnachMandateRequest_RecurringPaymentId{
			RecurringPaymentId: recurringPaymentId,
		},
	})
	if rpcErr := epifigrpc.RPCError(enachMandateRes, err); rpcErr != nil {
		cxLogger.Error(ctx, "failed to fetch ENACH mandate details", zap.String(logger.RECURRING_PAYMENT_ID, recurringPaymentId), zap.Error(rpcErr))
		return "Error fetching UMRN"
	}

	if enachMandateRes.GetEnachMandate().GetUmrn() == "" {
		return "UMRN not available"
	}
	return enachMandateRes.GetEnachMandate().GetUmrn()
}

//nolint:unparam
func createTableCell(cellValue string, cellStyle *webui.Style) *webui.TableCell {
	return &webui.TableCell{
		Value: cellValue,
		Style: cellStyle,
	}
}

func getEndDateString(mandate *recurringPay.GetRecurringPaymentsForActorResponse_RecurringPaymentTile) string {
	if mandate.GetInterval() != nil && mandate.GetInterval().GetEndTime() != nil {
		return mandate.GetInterval().GetEndTime().AsTime().In(datetime.IST).String()
	}
	return "Not Available"
}

func getStartDateString(mandate *recurringPay.GetRecurringPaymentsForActorResponse_RecurringPaymentTile) string {
	if mandate.GetInterval() != nil && mandate.GetInterval().GetStartTime() != nil {
		return mandate.GetInterval().GetStartTime().AsTime().In(datetime.IST).String()
	}
	return "Not Available"
}
