package transaction

import (
	"context"
	"fmt"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/proto"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/gamma/api/cx"
	tPb "github.com/epifi/gamma/api/cx/data_collector/transaction"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	recurringPay "github.com/epifi/gamma/api/recurringpayment"
	mocks3 "github.com/epifi/gamma/api/recurringpayment/enach/mocks"
	"github.com/epifi/gamma/api/recurringpayment/enums"
	mockRecurPaymentClient "github.com/epifi/gamma/api/recurringpayment/mocks"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/webui"
	"github.com/epifi/gamma/api/upi/mandate/mocks"
	mocks2 "github.com/epifi/gamma/cx/data_collector/helper/mocks"
	mockAuthEngine "github.com/epifi/gamma/cx/test/mocks/customer_auth/auth_engine"
	mockHelper "github.com/epifi/gamma/cx/test/mocks/data_collector/helper"
)

var (
	upcomingTxnsList = &recurringPay.UpcomingTransaction{
		EntityId: "id-1",
		MinAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        245,
		},
		MaxAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        345,
		},
		EntityName: "merchant",
		MinTime:    timestampPb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, datetime.EST5EDT)),
		MaxTime:    timestampPb.New(time.Date(2023, 2, 1, 0, 0, 0, 0, datetime.EST5EDT)),
	}

	getUpcomingTxnsResp = &recurringPay.GetUpcomingTransactionsResponse{
		Status:       rpcPb.StatusOk(),
		UpcomingTxns: []*recurringPay.UpcomingTransaction{upcomingTxnsList},
	}
)

type getUpcomingTxnsArgsMatcher struct {
	want *recurringPay.GetUpcomingTransactionsRequest
}

func newGetUpcomingTxnsArgsMatcherArgsMatcher(want *recurringPay.GetUpcomingTransactionsRequest) *getUpcomingTxnsArgsMatcher {
	return &getUpcomingTxnsArgsMatcher{want: want}
}

func (c *getUpcomingTxnsArgsMatcher) Matches(x interface{}) bool {
	got, ok := x.(*recurringPay.GetUpcomingTransactionsRequest)
	if !ok {
		return false
	}

	c.want.FromTime = got.GetFromTime()
	c.want.ToTime = got.GetToTime()
	return proto.Equal(c.want, got)
}

func (c *getUpcomingTxnsArgsMatcher) String() string {
	return fmt.Sprintf("want: %v", c.want)
}

func TestService_GetUpcomingTransactions(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mAuthEngine := mockAuthEngine.NewMockIAuthEngine(ctr)
	mTxnDataCollectorHelper := mockHelper.NewMockITransactionsDataCollectorHelper(ctr)
	mockTxnCategoryHelper := mocks2.NewMockTxnCategoryDataCollectorHelper(ctr)
	mockrecurringPaymentsClient := mockRecurPaymentClient.NewMockRecurringPaymentServiceClient(ctr)
	mockUpiMandateClient := mocks.NewMockMandateServiceClient(ctr)
	mockEnachMandateClient := mocks3.NewMockEnachServiceClient(ctr)

	defer func() {
		ctr.Finish()
	}()
	type args struct {
		mocks []interface{}
		ctx   context.Context
		req   *tPb.GetUpcomingTransactionsRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *tPb.GetUpcomingTransactionsResponse
		wantErr bool
	}{
		{
			name: "Auth required",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(), gomock.Any(), gomock.Any()).
						Return(true, nil),
				},
			},
			want: &tPb.GetUpcomingTransactionsResponse{Status: rpcPb.StatusOk()},
		},
		{
			name: "actor detail is nil",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(), gomock.Any(), gomock.Any()).
						Return(false, nil),
				},
				req: &tPb.GetUpcomingTransactionsRequest{
					Header: &cx.Header{
						Actor: nil,
					},
				},
			},
			want: &tPb.GetUpcomingTransactionsResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("actor info not present in header"),
			},
		},
		{
			name: "Got error from GetUpcomingTransactions backend rpc",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(), gomock.Any(), gomock.Any()).
						Return(false, nil),
					mockrecurringPaymentsClient.EXPECT().GetUpcomingTransactions(context.Background(), newGetUpcomingTxnsArgsMatcherArgsMatcher(
						&recurringPay.GetUpcomingTransactionsRequest{
							ActorId:         "actor-id1",
							ToTime:          timestampPb.New(datetime.TimeFromNow(MaxNoOfMonthsForWhichFutureTxnIsShown * time.Hour)),
							AccountingEntry: paymentPb.AccountingEntryType_DEBIT,
							Source: []enums.UpcomingTransactionSource{
								enums.UpcomingTransactionSource_DS,
								enums.UpcomingTransactionSource_FITTT,
							},
						})).
						Return(nil, errors.New("test error")),
				},
				req: &tPb.GetUpcomingTransactionsRequest{
					Header: &cx.Header{
						Actor: &types.Actor{
							Id: "actor-id1",
						},
					},
				},
			},
			want: &tPb.GetUpcomingTransactionsResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("unable to get upcoming transactions for given actor"),
			},
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(), gomock.Any(), gomock.Any()).
						Return(false, nil),
					mockrecurringPaymentsClient.EXPECT().GetUpcomingTransactions(context.Background(), newGetUpcomingTxnsArgsMatcherArgsMatcher(&recurringPay.GetUpcomingTransactionsRequest{
						ActorId:         "actor-id1",
						ToTime:          timestampPb.New(datetime.TimeFromNow(MaxNoOfMonthsForWhichFutureTxnIsShown * time.Hour)),
						AccountingEntry: paymentPb.AccountingEntryType_DEBIT,
						Source: []enums.UpcomingTransactionSource{
							enums.UpcomingTransactionSource_DS,
							enums.UpcomingTransactionSource_FITTT,
						},
					})).
						Return(getUpcomingTxnsResp, nil),
				},
				req: &tPb.GetUpcomingTransactionsRequest{
					Header: &cx.Header{
						Actor: &types.Actor{
							Id: "actor-id1",
						},
					},
				},
			},
			want: &tPb.GetUpcomingTransactionsResponse{
				Status: rpcPb.StatusOk(),
				UpcomingTransactionDetails: &webui.Table{
					TableName: "Upcoming Transactions",
					TableHeaders: []*webui.TableHeader{
						{Label: UpcomingTxnsDebitDateLabel, HeaderKey: UpcomingTxnsDebitDateKey},
						{Label: MerchantNameLabel, HeaderKey: MerchantNameKey},
						{Label: AmountLabel, HeaderKey: AmountKey},
						{Label: ModeLabel, HeaderKey: ModeKey},
						{Label: UpcomingTxnsTypeLabel, HeaderKey: UpcomingTxnsTypeKey},
					},
					TableRows: []*webui.TableRow{
						{
							HeaderKeyCellMap: map[string]*webui.TableCell{
								UpcomingTxnsDebitDateKey: createTableCell("1 Jan - 1 Feb", nil),
								MerchantNameKey:          createTableCell("merchant", nil),
								AmountKey:                createTableCell("INR 245.00 - INR 345.00", nil),
							},
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewService(nil, mAuthEngine, nil, nil, nil, tdcTS.orderConfig, mTxnDataCollectorHelper, mockTxnCategoryHelper, mockrecurringPaymentsClient, mockUpiMandateClient, mockEnachMandateClient)
			got, gotErr := s.GetUpcomingTransactions(tt.args.ctx, tt.args.req)
			if (gotErr != nil) != tt.wantErr {
				t.Errorf("GetUpcomingTransactions() gotErr :%v wantErr :%v", gotErr, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(tt.want, got) {
				t.Errorf("GetUpcomingTransactions() \n got = %v \n want = %v", got, tt.want)
			}
		})
	}
}
