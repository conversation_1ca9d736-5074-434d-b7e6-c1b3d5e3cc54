package transaction

const (
	FiTxnIdLabel               = "Fi Txn ID"
	UtrLabel                   = "UTR no/ URN"
	AmountLabel                = "Amount"
	IncomingOutgoingLabel      = "Incoming / Outgoing"
	TxnModeLabel               = "Txn Mode"
	StatusLabel                = "Status"
	FromInstrLabel             = "From Instr"
	ToInstrLabel               = "To Instr"
	CreatedAtLabel             = "Created At"
	OrderTagsLabel             = "Order Tags"
	MerchantLabel              = "Merchant"
	DsCategoriesLabel          = "DS Categories"
	MerchantNameLabel          = "Merchant Name"
	MandateLimitLabel          = "Mandate Limit"
	StartDateLabel             = "Start Date"
	EndDateLabel               = "End Date"
	MandateFrequencyLabel      = "Frequency"
	MandateStateLabel          = "State (Cancelled/Revoked/Created)"
	CancelButtonLabel          = "Cancel Button for wherever applicable"
	UpcomingTxnsDebitDateLabel = "Expected Date of Debit"
	ModeLabel                  = "Mode"
	UpcomingTxnsTypeLabel      = "Type \n(EMI/SIP) - For Later"
	MandateTypeLabel           = "Type of Mandate"
	MandateExternalIdLabel     = "Mandate External ID\n(visible to user in app)"
	MandateReferenceLabel      = "UMRN/UMN ID"

	FiTxnIdKey               = "fiTxnId"
	UtrKey                   = "utr"
	AmountKey                = "amount"
	IncomingOutgoingKey      = "incomingOutgoing"
	TxnModeKey               = "txnMode"
	StatusKey                = "status"
	FromInstrKey             = "fromInstr"
	ToInstrKey               = "toInstr"
	CreatedAtKey             = "createdAt"
	OrderTagsKey             = "orderTags"
	MerchantKey              = "merchant"
	DsCategoriesKey          = "dsCategories"
	MerchantNameKey          = "merchantName"
	MandateLimitKey          = "mandateLimit"
	StartDateKey             = "startDate"
	EndDateKey               = "endDate"
	MandateFrequencyKey      = "frequency"
	MandateStateKey          = "state"
	CancelButtonKey          = "cancelButton"
	UpcomingTxnsDebitDateKey = "upcomingTxnsDebitDate"
	ModeKey                  = "mode"
	UpcomingTxnsTypeKey      = "type"
	MandateTypeKey           = "mandateType"
	MandateExternalIdKey     = "externalId"
	MandateReferenceKey      = "mandateReference"

	ViewDetailsCtaLabel        = "view-details"
	ViewDisputeDetailsCtaLabel = "view-dispute-details"
	RaiseDisputeCtaLabel       = "raise-dispute"

	FirstOrLastNTxnsTableName     = "First or Last 5 Transactions"
	ExistingMandatesTableName     = "Existing Mandates"
	UpcomingTransactionsTableName = "Upcoming Transactions"
)
