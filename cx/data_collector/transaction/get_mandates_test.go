package transaction

import (
	"context"
	"fmt"
	"reflect"
	"testing"
	"time"

	mocks3 "github.com/epifi/gamma/api/recurringpayment/enach/mocks"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/upi/mandate/mocks"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/proto"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/gamma/api/cx"
	tPb "github.com/epifi/gamma/api/cx/data_collector/transaction"
	recurringPay "github.com/epifi/gamma/api/recurringpayment"
	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	mockRecurPaymentClient "github.com/epifi/gamma/api/recurringpayment/mocks"
	"github.com/epifi/gamma/api/typesv2/webui"
	upiMandatePb "github.com/epifi/gamma/api/upi/mandate"
	mocks2 "github.com/epifi/gamma/cx/data_collector/helper/mocks"
	mockAuthEngine "github.com/epifi/gamma/cx/test/mocks/customer_auth/auth_engine"
	mockHelper "github.com/epifi/gamma/cx/test/mocks/data_collector/helper"
)

var (
	recurringPaymentsList = &recurringPay.GetRecurringPaymentsForActorResponse_RecurringPaymentTile{
		Id:               "id-1",
		ExternalId:       "ext-id-1",
		Type:             recurringPay.RecurringPaymentType_ENACH_MANDATES,
		State:            recurringPay.RecurringPaymentState_CREATION_INITIATED,
		AllowedFrequency: recurringPay.AllowedFrequency_BI_MONTHLY,
		Amount: &money.Money{
			CurrencyCode: "INR",
			Units:        245,
		},
		Name:              "merchant",
		CreationTimestamp: timestampPb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, datetime.IST)),
		Expiry:            timestampPb.New(time.Date(2025, 1, 1, 0, 0, 0, 0, datetime.IST)),
	}

	upiRecurringPaymentsList = &recurringPay.GetRecurringPaymentsForActorResponse_RecurringPaymentTile{
		Id:               "upi-id-1",
		ExternalId:       "upi-ext-id-1",
		Type:             recurringPay.RecurringPaymentType_UPI_MANDATES,
		State:            recurringPay.RecurringPaymentState_CREATION_INITIATED,
		AllowedFrequency: recurringPay.AllowedFrequency_MONTHLY,
		Amount: &money.Money{
			CurrencyCode: "INR",
			Units:        500,
		},
		Name:              "UPI Merchant",
		CreationTimestamp: timestampPb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, datetime.IST)),
		Expiry:            timestampPb.New(time.Date(2025, 1, 1, 0, 0, 0, 0, datetime.IST)),
	}

	getRecurringPaymentResp = &recurringPay.GetRecurringPaymentsForActorResponse{
		Status:            rpcPb.StatusOk(),
		RecurringPayments: []*recurringPay.GetRecurringPaymentsForActorResponse_RecurringPaymentTile{recurringPaymentsList},
	}

	getUpiRecurringPaymentResp = &recurringPay.GetRecurringPaymentsForActorResponse{
		Status:            rpcPb.StatusOk(),
		RecurringPayments: []*recurringPay.GetRecurringPaymentsForActorResponse_RecurringPaymentTile{upiRecurringPaymentsList},
	}
)

type getRecurringPaymentsForActorArgsMatcher struct {
	want *recurringPay.GetRecurringPaymentsForActorRequest
}

func newGetRecurringPaymentsForActorArgsMatcher(want *recurringPay.GetRecurringPaymentsForActorRequest) *getRecurringPaymentsForActorArgsMatcher {
	return &getRecurringPaymentsForActorArgsMatcher{want: want}
}

func (c *getRecurringPaymentsForActorArgsMatcher) Matches(x interface{}) bool {
	got, ok := x.(*recurringPay.GetRecurringPaymentsForActorRequest)
	if !ok {
		return false
	}

	c.want.StartTimestamp = got.GetStartTimestamp()
	return proto.Equal(c.want, got)
}

func (c *getRecurringPaymentsForActorArgsMatcher) String() string {
	return fmt.Sprintf("want: %v", c.want)
}

func TestService_GetMandates(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mAuthEngine := mockAuthEngine.NewMockIAuthEngine(ctr)
	mTxnDataCollectorHelper := mockHelper.NewMockITransactionsDataCollectorHelper(ctr)
	mockTxnCategoryHelper := mocks2.NewMockTxnCategoryDataCollectorHelper(ctr)
	mockrecurringPaymentsClient := mockRecurPaymentClient.NewMockRecurringPaymentServiceClient(ctr)
	mockUpiMandateClient := mocks.NewMockMandateServiceClient(ctr)
	mockEnachMandateClient := mocks3.NewMockEnachServiceClient(ctr)

	defer func() {
		ctr.Finish()
	}()
	type args struct {
		mocks []interface{}
		ctx   context.Context
		req   *tPb.GetMandatesRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *tPb.GetMandatesResponse
		wantErr bool
	}{
		{
			name: "Auth required",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(), gomock.Any(), gomock.Any()).
						Return(true, nil),
				},
			},
			want: &tPb.GetMandatesResponse{Status: rpcPb.StatusOk()},
		},
		{
			name: "actor detail is nil",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(), gomock.Any(), gomock.Any()).
						Return(false, nil),
				},
				req: &tPb.GetMandatesRequest{
					Header: &cx.Header{
						Actor: nil,
					},
				},
			},
			want: &tPb.GetMandatesResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("actor info not present in header"),
			},
		},
		{
			name: "Got error from GetRecurringPaymentsForActor rpc",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(), gomock.Any(), gomock.Any()).
						Return(false, nil),
					mockrecurringPaymentsClient.EXPECT().GetRecurringPaymentsForActor(context.Background(), newGetRecurringPaymentsForActorArgsMatcher(
						&recurringPay.GetRecurringPaymentsForActorRequest{
							CurrentActorId: "actor-id1",
							PageSize:       tdcTS.txnConfig.PageSizeForExistingMandates,
							Descending:     true,
							StartTimestamp: timestampPb.Now(),
						})).
						Return(nil, errors.New("test error")),
				},
				req: &tPb.GetMandatesRequest{
					Header: &cx.Header{
						Actor: &types.Actor{
							Id: "actor-id1",
						},
					},
				},
			},
			want: &tPb.GetMandatesResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("unable to get active recurring payments for given actor"),
			},
		},
		{
			name: "Got record-not-found from GetRecurringPaymentsForActor rpc",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(), gomock.Any(), gomock.Any()).
						Return(false, nil),
					mockrecurringPaymentsClient.EXPECT().GetRecurringPaymentsForActor(context.Background(), newGetRecurringPaymentsForActorArgsMatcher(
						&recurringPay.GetRecurringPaymentsForActorRequest{
							CurrentActorId: "actor-id1",
							PageSize:       tdcTS.txnConfig.PageSizeForExistingMandates,
							Descending:     true,
							StartTimestamp: timestampPb.Now(),
						})).
						Return(&recurringPay.GetRecurringPaymentsForActorResponse{Status: rpcPb.StatusRecordNotFound()}, nil),
				},
				req: &tPb.GetMandatesRequest{
					Header: &cx.Header{
						Actor: &types.Actor{
							Id: "actor-id1",
						},
					},
				},
			},
			want: &tPb.GetMandatesResponse{
				Status: rpcPb.StatusRecordNotFoundWithDebugMsg("no mandates found for the given actor"),
			},
		},
		{
			name: "success with ENACH mandate",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(), gomock.Any(), gomock.Any()).
						Return(false, nil),
					mockrecurringPaymentsClient.EXPECT().GetRecurringPaymentsForActor(context.Background(), newGetRecurringPaymentsForActorArgsMatcher(&recurringPay.GetRecurringPaymentsForActorRequest{
						CurrentActorId: "actor-id1",
						PageSize:       tdcTS.txnConfig.PageSizeForExistingMandates,
						Descending:     true,
						StartTimestamp: timestampPb.Now(),
					})).
						Return(getRecurringPaymentResp, nil),
					mockEnachMandateClient.EXPECT().GetEnachMandate(gomock.Any(), &enachPb.GetEnachMandateRequest{
						Identifier: &enachPb.GetEnachMandateRequest_RecurringPaymentId{
							RecurringPaymentId: "id-1",
						},
					}).Return(&enachPb.GetEnachMandateResponse{
						Status: rpcPb.StatusOk(),
						EnachMandate: &enachPb.EnachMandate{
							Umrn: "UMRN12345",
						},
					}, nil),
				},
				req: &tPb.GetMandatesRequest{
					Header: &cx.Header{
						Actor: &types.Actor{
							Id: "actor-id1",
						},
					},
				},
			},
			want: &tPb.GetMandatesResponse{
				Status: rpcPb.StatusOk(),
				MandateDetails: &webui.Table{
					TableName: "Existing Mandates",
					TableHeaders: []*webui.TableHeader{
						{Label: MerchantNameLabel, HeaderKey: MerchantNameKey},
						{Label: MandateLimitLabel, HeaderKey: MandateLimitKey},
						{Label: StartDateLabel, HeaderKey: StartDateKey},
						{Label: EndDateLabel, HeaderKey: EndDateKey},
						{Label: MandateFrequencyLabel, HeaderKey: MandateFrequencyKey},
						{Label: MandateStateLabel, HeaderKey: MandateStateKey},
						{Label: MandateTypeLabel, HeaderKey: MandateTypeKey},
						{Label: MandateExternalIdLabel, HeaderKey: MandateExternalIdKey},
						{Label: MandateReferenceLabel, HeaderKey: MandateReferenceKey},
					},
					TableRows: []*webui.TableRow{
						{
							HeaderKeyCellMap: map[string]*webui.TableCell{
								MandateExternalIdKey: createTableCell("ext-id-1", nil),
								MerchantNameKey:      createTableCell("merchant", nil),
								StartDateKey:         createTableCell("Not Available", nil),
								EndDateKey:           createTableCell("Not Available", nil),
								MandateFrequencyKey:  createTableCell("BI_MONTHLY", nil),
								MandateStateKey:      createTableCell("CREATION_INITIATED", nil),
								MandateLimitKey:      createTableCell("INR 245.00", nil),
								MandateTypeKey:       createTableCell("ENACH_MANDATES", nil),
								MandateReferenceKey:  createTableCell("UMRN12345", nil),
							},
						},
					},
				},
			},
		},
		{
			name: "success with UPI mandate",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(), gomock.Any(), gomock.Any()).
						Return(false, nil),
					mockrecurringPaymentsClient.EXPECT().GetRecurringPaymentsForActor(context.Background(), newGetRecurringPaymentsForActorArgsMatcher(&recurringPay.GetRecurringPaymentsForActorRequest{
						CurrentActorId: "actor-id2",
						PageSize:       tdcTS.txnConfig.PageSizeForExistingMandates,
						Descending:     true,
						StartTimestamp: timestampPb.Now(),
					})).
						Return(getUpiRecurringPaymentResp, nil),
					mockUpiMandateClient.EXPECT().GetMandateDetails(gomock.Any(), &upiMandatePb.GetMandateDetailsRequest{
						RecurringPaymentId: "upi-id-1",
					}).Return(&upiMandatePb.GetMandateDetailsResponse{
						Status: rpcPb.StatusOk(),
						Umn:    "UMN67890",
					}, nil),
				},
				req: &tPb.GetMandatesRequest{
					Header: &cx.Header{
						Actor: &types.Actor{
							Id: "actor-id2",
						},
					},
				},
			},
			want: &tPb.GetMandatesResponse{
				Status: rpcPb.StatusOk(),
				MandateDetails: &webui.Table{
					TableName: "Existing Mandates",
					TableHeaders: []*webui.TableHeader{
						{Label: MerchantNameLabel, HeaderKey: MerchantNameKey},
						{Label: MandateLimitLabel, HeaderKey: MandateLimitKey},
						{Label: StartDateLabel, HeaderKey: StartDateKey},
						{Label: EndDateLabel, HeaderKey: EndDateKey},
						{Label: MandateFrequencyLabel, HeaderKey: MandateFrequencyKey},
						{Label: MandateStateLabel, HeaderKey: MandateStateKey},
						{Label: MandateTypeLabel, HeaderKey: MandateTypeKey},
						{Label: MandateExternalIdLabel, HeaderKey: MandateExternalIdKey},
						{Label: MandateReferenceLabel, HeaderKey: MandateReferenceKey},
					},
					TableRows: []*webui.TableRow{
						{
							HeaderKeyCellMap: map[string]*webui.TableCell{
								MandateExternalIdKey: createTableCell("upi-ext-id-1", nil),
								MerchantNameKey:      createTableCell("UPI Merchant", nil),
								StartDateKey:         createTableCell("Not Available", nil),
								EndDateKey:           createTableCell("Not Available", nil),
								MandateFrequencyKey:  createTableCell("MONTHLY", nil),
								MandateStateKey:      createTableCell("CREATION_INITIATED", nil),
								MandateLimitKey:      createTableCell("INR 500.00", nil),
								MandateTypeKey:       createTableCell("UPI_MANDATES", nil),
								MandateReferenceKey:  createTableCell("UMN67890", nil),
							},
						},
					},
				},
			},
		},
		{
			name: "UPI mandate client error - should return error fetching UMN",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(), gomock.Any(), gomock.Any()).
						Return(false, nil),
					mockrecurringPaymentsClient.EXPECT().GetRecurringPaymentsForActor(context.Background(), newGetRecurringPaymentsForActorArgsMatcher(&recurringPay.GetRecurringPaymentsForActorRequest{
						CurrentActorId: "actor-id3",
						PageSize:       tdcTS.txnConfig.PageSizeForExistingMandates,
						Descending:     true,
						StartTimestamp: timestampPb.Now(),
					})).
						Return(getUpiRecurringPaymentResp, nil),
					mockUpiMandateClient.EXPECT().GetMandateDetails(gomock.Any(), &upiMandatePb.GetMandateDetailsRequest{
						RecurringPaymentId: "upi-id-1",
					}).Return(nil, errors.New("UPI mandate service error")),
				},
				req: &tPb.GetMandatesRequest{
					Header: &cx.Header{
						Actor: &types.Actor{
							Id: "actor-id3",
						},
					},
				},
			},
			want: &tPb.GetMandatesResponse{
				Status: rpcPb.StatusOk(),
				MandateDetails: &webui.Table{
					TableName: "Existing Mandates",
					TableHeaders: []*webui.TableHeader{
						{Label: MerchantNameLabel, HeaderKey: MerchantNameKey},
						{Label: MandateLimitLabel, HeaderKey: MandateLimitKey},
						{Label: StartDateLabel, HeaderKey: StartDateKey},
						{Label: EndDateLabel, HeaderKey: EndDateKey},
						{Label: MandateFrequencyLabel, HeaderKey: MandateFrequencyKey},
						{Label: MandateStateLabel, HeaderKey: MandateStateKey},
						{Label: MandateTypeLabel, HeaderKey: MandateTypeKey},
						{Label: MandateExternalIdLabel, HeaderKey: MandateExternalIdKey},
						{Label: MandateReferenceLabel, HeaderKey: MandateReferenceKey},
					},
					TableRows: []*webui.TableRow{
						{
							HeaderKeyCellMap: map[string]*webui.TableCell{
								MandateExternalIdKey: createTableCell("upi-ext-id-1", nil),
								MerchantNameKey:      createTableCell("UPI Merchant", nil),
								StartDateKey:         createTableCell("Not Available", nil),
								EndDateKey:           createTableCell("Not Available", nil),
								MandateFrequencyKey:  createTableCell("MONTHLY", nil),
								MandateStateKey:      createTableCell("CREATION_INITIATED", nil),
								MandateLimitKey:      createTableCell("INR 500.00", nil),
								MandateTypeKey:       createTableCell("UPI_MANDATES", nil),
								MandateReferenceKey:  createTableCell("Error fetching UMN", nil),
							},
						},
					},
				},
			},
		},
		{
			name: "ENACH mandate client error - should return error fetching UMRN",
			args: args{
				ctx: context.Background(),
				mocks: []interface{}{
					mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(context.Background(), gomock.Any(), gomock.Any()).
						Return(false, nil),
					mockrecurringPaymentsClient.EXPECT().GetRecurringPaymentsForActor(context.Background(), newGetRecurringPaymentsForActorArgsMatcher(&recurringPay.GetRecurringPaymentsForActorRequest{
						CurrentActorId: "actor-id4",
						PageSize:       tdcTS.txnConfig.PageSizeForExistingMandates,
						Descending:     true,
						StartTimestamp: timestampPb.Now(),
					})).
						Return(getRecurringPaymentResp, nil),
					mockEnachMandateClient.EXPECT().GetEnachMandate(gomock.Any(), &enachPb.GetEnachMandateRequest{
						Identifier: &enachPb.GetEnachMandateRequest_RecurringPaymentId{
							RecurringPaymentId: "id-1",
						},
					}).Return(nil, errors.New("ENACH mandate service error")),
				},
				req: &tPb.GetMandatesRequest{
					Header: &cx.Header{
						Actor: &types.Actor{
							Id: "actor-id4",
						},
					},
				},
			},
			want: &tPb.GetMandatesResponse{
				Status: rpcPb.StatusOk(),
				MandateDetails: &webui.Table{
					TableName: "Existing Mandates",
					TableHeaders: []*webui.TableHeader{
						{Label: MerchantNameLabel, HeaderKey: MerchantNameKey},
						{Label: MandateLimitLabel, HeaderKey: MandateLimitKey},
						{Label: StartDateLabel, HeaderKey: StartDateKey},
						{Label: EndDateLabel, HeaderKey: EndDateKey},
						{Label: MandateFrequencyLabel, HeaderKey: MandateFrequencyKey},
						{Label: MandateStateLabel, HeaderKey: MandateStateKey},
						{Label: MandateTypeLabel, HeaderKey: MandateTypeKey},
						{Label: MandateExternalIdLabel, HeaderKey: MandateExternalIdKey},
						{Label: MandateReferenceLabel, HeaderKey: MandateReferenceKey},
					},
					TableRows: []*webui.TableRow{
						{
							HeaderKeyCellMap: map[string]*webui.TableCell{
								MandateExternalIdKey: createTableCell("ext-id-1", nil),
								MerchantNameKey:      createTableCell("merchant", nil),
								StartDateKey:         createTableCell("Not Available", nil),
								EndDateKey:           createTableCell("Not Available", nil),
								MandateFrequencyKey:  createTableCell("BI_MONTHLY", nil),
								MandateStateKey:      createTableCell("CREATION_INITIATED", nil),
								MandateLimitKey:      createTableCell("INR 245.00", nil),
								MandateTypeKey:       createTableCell("ENACH_MANDATES", nil),
								MandateReferenceKey:  createTableCell("Error fetching UMRN", nil),
							},
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewService(nil, mAuthEngine, tdcTS.txnConfig, nil, nil, tdcTS.orderConfig, mTxnDataCollectorHelper, mockTxnCategoryHelper, mockrecurringPaymentsClient, mockUpiMandateClient, mockEnachMandateClient)
			got, gotErr := s.GetMandates(tt.args.ctx, tt.args.req)
			if (gotErr != nil) != tt.wantErr {
				t.Errorf("GetMandates() gotErr :%v wantErr :%v", gotErr, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(tt.want, got) {
				t.Errorf("GetMandates() \n got = %v \n want = %v", got, tt.want)
			}
		})
	}
}
