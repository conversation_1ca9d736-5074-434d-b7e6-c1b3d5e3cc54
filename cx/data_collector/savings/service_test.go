package savings

import (
	"context"
	"flag"
	"fmt"
	"os"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	rpcPb "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	cxPb "github.com/epifi/gamma/api/cx"
	caPb "github.com/epifi/gamma/api/cx/customer_auth"
	savingsDataCollectorPb "github.com/epifi/gamma/api/cx/data_collector/savings"
	"github.com/epifi/gamma/api/savings"
	mockSavings "github.com/epifi/gamma/api/savings/mocks"
	"github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	mockUser "github.com/epifi/gamma/api/user/mocks"
	"github.com/epifi/gamma/cx/test"
	mockAuthEngine "github.com/epifi/gamma/cx/test/mocks/customer_auth/auth_engine"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	// nolint: dogsled
	_, _, _, teardown = test.InitTestServer(false)
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

func TestService_GetSavingsAccountNominees(t *testing.T) {
	t.Parallel()

	var (
		testActorId = "test-actor-123"

		getSavingsAccountNomineesRequest = &savingsDataCollectorPb.GetSavingsAccountNomineesRequest{
			Header: &cxPb.Header{
				AgentEmail:  "<EMAIL>",
				AccessToken: "test_token",
				TicketId:    1,
				Actor: &typesv2.Actor{
					Id: testActorId,
				},
			},
		}

		getSavingsAccountNomineesRequestWithoutActor = &savingsDataCollectorPb.GetSavingsAccountNomineesRequest{
			Header: &cxPb.Header{
				AgentEmail:  "<EMAIL>",
				AccessToken: "test_token",
				TicketId:    1,
			},
		}

		sherlockDeepLink = &caPb.SherlockDeepLink{
			Screen: caPb.Screen_AUTH_SCREEN,
		}

		// Mock response from savings service (unredacted)
		savingsAccountNomineesSuccessResponse = &savings.GetSavingsAccountNomineesResponse{
			Status: rpcPb.StatusOk(),
			SavingsAccountNominees: []*savings.SavingsAccountNominee{
				{
					Nominee: &typesv2.Nominee{
						Id:      "nominee-1",
						ActorId: testActorId,
						Name:    "John Doe",
						Pan:     "**********",
						ContactInfo: &typesv2.ContactInfo{
							PhoneNumber: &common.PhoneNumber{
								CountryCode:    91,
								NationalNumber: **********,
							},
							EmailId: "<EMAIL>",
							Address: &typesv2.PostalAddress{
								AddressLines:       []string{"123 Main Street", "Apartment 4B"},
								Locality:           "Mumbai",
								Sublocality:        "Andheri West",
								AdministrativeArea: "Maharashtra",
								PostalCode:         "400058",
								RegionCode:         "IN",
							},
						},
						NomineeDocument: &typesv2.NomineeDocument{
							DocumentNumber: "ABCD123456789",
						},
					},
					PercentageShare: 50,
				},
				{
					Nominee: &typesv2.Nominee{
						Id:      "nominee-2",
						ActorId: testActorId,
						Name:    "Jane Smith",
						Pan:     "**********",
						ContactInfo: &typesv2.ContactInfo{
							PhoneNumber: &common.PhoneNumber{
								CountryCode:    91,
								NationalNumber: 8765432109,
							},
							EmailId: "<EMAIL>",
							Address: &typesv2.PostalAddress{
								AddressLines:       []string{"456 Oak Avenue", "Building C, Floor 2"},
								Locality:           "Delhi",
								Sublocality:        "Connaught Place",
								AdministrativeArea: "Delhi",
								PostalCode:         "110001",
								RegionCode:         "IN",
							},
						},
						NomineeDocument: &typesv2.NomineeDocument{
							DocumentNumber: "EFGH987654321",
						},
					},
					PercentageShare: 50,
				},
			},
		}

		savingsAccountNomineesErrorResponse = &savings.GetSavingsAccountNomineesResponse{
			Status: rpcPb.StatusInternal(),
		}

		savingsAccountNomineesNotFoundResponse = &savings.GetSavingsAccountNomineesResponse{
			Status: rpcPb.StatusRecordNotFound(),
		}

		// Mock response from user service (unredacted)
		nomineesSuccessResponse = &userPb.GetNomineesResponse{
			Status: rpcPb.StatusOk(),
			Nominees: []*typesv2.Nominee{
				{
					Id:      "nominee-attempt-1",
					ActorId: testActorId,
					Name:    "John Doe",
					Pan:     "**********",
					ContactInfo: &typesv2.ContactInfo{
						PhoneNumber: &common.PhoneNumber{
							CountryCode:    91,
							NationalNumber: **********,
						},
						EmailId: "<EMAIL>",
						Address: &typesv2.PostalAddress{
							AddressLines:       []string{"789 Pine Road", "Society Block A"},
							Locality:           "Bangalore",
							Sublocality:        "Koramangala",
							AdministrativeArea: "Karnataka",
							PostalCode:         "560034",
							RegionCode:         "IN",
						},
					},
					NomineeDocument: &typesv2.NomineeDocument{
						DocumentNumber: "IJKL567890123",
					},
				},
				{
					Id:      "nominee-attempt-2",
					ActorId: testActorId,
					Name:    "Jane Smith",
					Pan:     "**********",
					ContactInfo: &typesv2.ContactInfo{
						PhoneNumber: &common.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 8234567890,
						},
						EmailId: "<EMAIL>",
						Address: &typesv2.PostalAddress{
							AddressLines:       []string{"321 Cedar Lane", "Tower B, Unit 15"},
							Locality:           "Chennai",
							Sublocality:        "T Nagar",
							AdministrativeArea: "Tamil Nadu",
							PostalCode:         "600017",
							RegionCode:         "IN",
						},
					},
					NomineeDocument: &typesv2.NomineeDocument{
						DocumentNumber: "MNOP345678901",
					},
				},
			},
		}

		nomineesErrorResponse = &userPb.GetNomineesResponse{
			Status: rpcPb.StatusInternal(),
		}

		nomineesNotFoundResponse = &userPb.GetNomineesResponse{
			Status: rpcPb.StatusRecordNotFound(),
		}

		// Expected redacted savings account nominees for response
		expectedRedactedSavingsAccountNominees = []*savings.SavingsAccountNominee{
			{
				Nominee: &typesv2.Nominee{
					Id:      "nominee-1",
					ActorId: testActorId,
					Name:    "John Doe",   // Name is no longer redacted
					Pan:     "XXXXXX234F", // DontMaskLastFourChars: ********** -> XXXXXX234F
					ContactInfo: &typesv2.ContactInfo{
						PhoneNumber: &common.PhoneNumber{
							CountryCode:    91,
							NationalNumber: **********, // GetMaskedPhoneNumberV2: ********** -> ********** (keeps last 4 digits)
						},
						EmailId: "<EMAIL>", // DontMaskFirstTwoAndLastTwoChars for local part: john.doe -> joXXXXoe
						Address: &typesv2.PostalAddress{
							AddressLines:       []string{"123 Main*******", "Apartm******"}, // maskHalf: keeps first half
							Locality:           "Mum***",                                    // maskHalf: Mumbai -> Mum***
							Sublocality:        "Andher******",                              // maskHalf: Andheri West -> Andher******
							AdministrativeArea: "Maharashtra",                               // Leave unmasked for visibility
							PostalCode:         "XX0058",                                    // DontMaskLastFourChars: 400058 -> XX0058
							RegionCode:         "IN",
						},
					},
					NomineeDocument: &typesv2.NomineeDocument{
						DocumentNumber: "XXXXXXXXX6789", // DontMaskLastFourChars: ABCD123456789 -> XXXXXXXXX6789
					},
				},
				PercentageShare: 50,
			},
			{
				Nominee: &typesv2.Nominee{
					Id:      "nominee-2",
					ActorId: testActorId,
					Name:    "Jane Smith", // Name is no longer redacted
					Pan:     "XXXXXX678K", // DontMaskLastFourChars: ********** -> XXXXXX678K
					ContactInfo: &typesv2.ContactInfo{
						PhoneNumber: &common.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 1000002109, // GetMaskedPhoneNumberV2: 8765432109 -> 1000002109 (keeps last 4 digits)
						},
						EmailId: "<EMAIL>", // DontMaskFirstTwoAndLastTwoChars for local part: jane.smith -> jaXXXXXXth
						Address: &typesv2.PostalAddress{
							AddressLines:       []string{"456 Oak*******", "Building C*********"}, // maskHalf: keeps first half
							Locality:           "Del**",                                           // maskHalf: Delhi -> Del**
							Sublocality:        "Connaugh*******",                                 // maskHalf: Connaught Place -> Connaugh*******
							AdministrativeArea: "Delhi",                                           // Leave unmasked for visibility
							PostalCode:         "XX0001",                                          // DontMaskLastFourChars: 110001 -> XX0001
							RegionCode:         "IN",
						},
					},
					NomineeDocument: &typesv2.NomineeDocument{
						DocumentNumber: "XXXXXXXXX4321", // DontMaskLastFourChars: EFGH987654321 -> XXXXXXXXX4321
					},
				},
				PercentageShare: 50,
			},
		}

		// Expected redacted nominee attempts for response
		expectedRedactedNomineeAttempts = []*typesv2.Nominee{
			{
				Id:      "nominee-attempt-1",
				ActorId: testActorId,
				Name:    "John Doe",   // Name is no longer redacted
				Pan:     "XXXXXX876P", // DontMaskLastFourChars: ********** -> XXXXXX876P
				ContactInfo: &typesv2.ContactInfo{
					PhoneNumber: &common.PhoneNumber{
						CountryCode:    91,
						NationalNumber: 1000006789, // GetMaskedPhoneNumberV2: ********** -> 1000006789 (keeps last 4 digits)
					},
					EmailId: "<EMAIL>", // DontMaskFirstTwoAndLastTwoChars for local part: john.attempt -> joXXXXXXXXpt
					Address: &typesv2.PostalAddress{
						AddressLines:       []string{"789 Pin******", "Society *******"}, // maskHalf: keeps first half
						Locality:           "Banga****",                                  // maskHalf: Bangalore -> Banga****
						Sublocality:        "Korama*****",                                // maskHalf: Koramangala -> Korama*****
						AdministrativeArea: "Karnataka",                                  // Leave unmasked for visibility
						PostalCode:         "XX0034",                                     // DontMaskLastFourChars: 560034 -> XX0034
						RegionCode:         "IN",
					},
				},
				NomineeDocument: &typesv2.NomineeDocument{
					DocumentNumber: "XXXXXXXXX0123", // DontMaskLastFourChars: IJKL567890123 -> XXXXXXXXX0123
				},
			},
			{
				Id:      "nominee-attempt-2",
				ActorId: testActorId,
				Name:    "Jane Smith", // Name is no longer redacted
				Pan:     "XXXXXX468U", // DontMaskLastFourChars: ********** -> XXXXXX468U
				ContactInfo: &typesv2.ContactInfo{
					PhoneNumber: &common.PhoneNumber{
						CountryCode:    91,
						NationalNumber: 1000007890, // GetMaskedPhoneNumberV2: 8234567890 -> 1000007890 (keeps last 4 digits)
					},
					EmailId: "<EMAIL>", // DontMaskFirstTwoAndLastTwoChars for local part: jane.attempt -> jaXXXXXXXXpt
					Address: &typesv2.PostalAddress{
						AddressLines:       []string{"321 Ced*******", "Tower B,********"}, // maskHalf: keeps first half
						Locality:           "Chen***",                                      // maskHalf: Chennai -> Chen***
						Sublocality:        "T Na***",                                      // maskHalf: T Nagar -> T Na***
						AdministrativeArea: "Tamil Nadu",                                   // Leave unmasked for visibility
						PostalCode:         "XX0017",                                       // DontMaskLastFourChars: 600017 -> XX0017
						RegionCode:         "IN",
					},
				},
				NomineeDocument: &typesv2.NomineeDocument{
					DocumentNumber: "XXXXXXXXX8901", // DontMaskLastFourChars: MNOP345678901 -> XXXXXXXX8901
				},
			},
		}

		expectedSuccessResponse = &savingsDataCollectorPb.GetSavingsAccountNomineesResponse{
			Status:                 rpcPb.StatusOk(),
			SavingsAccountNominees: expectedRedactedSavingsAccountNominees,
			NomineeAttempts:        expectedRedactedNomineeAttempts,
		}
	)

	type mockStruct struct {
		mAuthEngine    *mockAuthEngine.MockIAuthEngine
		mSavingsClient *mockSavings.MockSavingsClient
		mUserClient    *mockUser.MockUsersClient
	}

	type args struct {
		ctx context.Context
		req *savingsDataCollectorPb.GetSavingsAccountNomineesRequest
	}

	tests := []struct {
		name      string
		args      args
		want      *savingsDataCollectorPb.GetSavingsAccountNomineesResponse
		wantErr   bool
		wantMocks func(m *mockStruct)
	}{
		{
			name: "should return auth deep link when auth action required",
			args: args{
				ctx: context.Background(),
				req: getSavingsAccountNomineesRequest,
			},
			want: &savingsDataCollectorPb.GetSavingsAccountNomineesResponse{
				Status:           rpcPb.StatusOk(),
				SherlockDeepLink: sherlockDeepLink,
			},
			wantErr: false,
			wantMocks: func(m *mockStruct) {
				m.mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(
					context.Background(),
					getSavingsAccountNomineesRequest.GetHeader(),
					getSavingsAccountNomineesRequest.GetHeader().GetInformationLevel(),
				).Return(true, sherlockDeepLink)
			},
		},
		{
			name: "should return invalid argument when actor missing in header",
			args: args{
				ctx: context.Background(),
				req: getSavingsAccountNomineesRequestWithoutActor,
			},
			want: &savingsDataCollectorPb.GetSavingsAccountNomineesResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("actor information not present in header"),
			},
			wantErr: false,
			wantMocks: func(m *mockStruct) {
				m.mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(
					context.Background(),
					getSavingsAccountNomineesRequestWithoutActor.GetHeader(),
					getSavingsAccountNomineesRequestWithoutActor.GetHeader().GetInformationLevel(),
				).Return(false, nil)
			},
		},
		{
			name: "should return internal error when savings service connection fails",
			args: args{
				ctx: context.Background(),
				req: getSavingsAccountNomineesRequest,
			},
			want: &savingsDataCollectorPb.GetSavingsAccountNomineesResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error in GetSavingsAccountNominees func"),
			},
			wantErr: false,
			wantMocks: func(m *mockStruct) {
				m.mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(
					context.Background(),
					getSavingsAccountNomineesRequest.GetHeader(),
					getSavingsAccountNomineesRequest.GetHeader().GetInformationLevel(),
				).Return(false, nil)
				m.mSavingsClient.EXPECT().GetSavingsAccountNominees(
					context.Background(),
					&savings.GetSavingsAccountNomineesRequest{
						ActorId: testActorId,
					},
				).Return(nil, fmt.Errorf("failed to connect"))
			},
		},
		{
			name: "should return internal error when savings service returns error",
			args: args{
				ctx: context.Background(),
				req: getSavingsAccountNomineesRequest,
			},
			want: &savingsDataCollectorPb.GetSavingsAccountNomineesResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error in GetSavingsAccountNominees func"),
			},
			wantErr: false,
			wantMocks: func(m *mockStruct) {
				m.mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(
					context.Background(),
					getSavingsAccountNomineesRequest.GetHeader(),
					getSavingsAccountNomineesRequest.GetHeader().GetInformationLevel(),
				).Return(false, nil)
				m.mSavingsClient.EXPECT().GetSavingsAccountNominees(
					context.Background(),
					&savings.GetSavingsAccountNomineesRequest{
						ActorId: testActorId,
					},
				).Return(savingsAccountNomineesErrorResponse, nil)
			},
		},
		{
			name: "should return not found when savings account nominees not found",
			args: args{
				ctx: context.Background(),
				req: getSavingsAccountNomineesRequest,
			},
			want: &savingsDataCollectorPb.GetSavingsAccountNomineesResponse{
				Status: rpcPb.StatusRecordNotFoundWithDebugMsg("Savings account nominees not found"),
			},
			wantErr: false,
			wantMocks: func(m *mockStruct) {
				m.mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(
					context.Background(),
					getSavingsAccountNomineesRequest.GetHeader(),
					getSavingsAccountNomineesRequest.GetHeader().GetInformationLevel(),
				).Return(false, nil)
				m.mSavingsClient.EXPECT().GetSavingsAccountNominees(
					context.Background(),
					&savings.GetSavingsAccountNomineesRequest{
						ActorId: testActorId,
					},
				).Return(savingsAccountNomineesNotFoundResponse, nil)
			},
		},
		{
			name: "should return internal error when user service connection fails",
			args: args{
				ctx: context.Background(),
				req: getSavingsAccountNomineesRequest,
			},
			want: &savingsDataCollectorPb.GetSavingsAccountNomineesResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error in GetNominees func"),
			},
			wantErr: false,
			wantMocks: func(m *mockStruct) {
				m.mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(
					context.Background(),
					getSavingsAccountNomineesRequest.GetHeader(),
					getSavingsAccountNomineesRequest.GetHeader().GetInformationLevel(),
				).Return(false, nil)
				m.mSavingsClient.EXPECT().GetSavingsAccountNominees(
					context.Background(),
					&savings.GetSavingsAccountNomineesRequest{
						ActorId: testActorId,
					},
				).Return(savingsAccountNomineesSuccessResponse, nil)
				m.mUserClient.EXPECT().GetNominees(
					context.Background(),
					&userPb.GetNomineesRequest{
						ActorId: testActorId,
					},
				).Return(nil, fmt.Errorf("failed to connect"))
			},
		},
		{
			name: "should return internal error when user service returns error",
			args: args{
				ctx: context.Background(),
				req: getSavingsAccountNomineesRequest,
			},
			want: &savingsDataCollectorPb.GetSavingsAccountNomineesResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error in GetNominees func"),
			},
			wantErr: false,
			wantMocks: func(m *mockStruct) {
				m.mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(
					context.Background(),
					getSavingsAccountNomineesRequest.GetHeader(),
					getSavingsAccountNomineesRequest.GetHeader().GetInformationLevel(),
				).Return(false, nil)
				m.mSavingsClient.EXPECT().GetSavingsAccountNominees(
					context.Background(),
					&savings.GetSavingsAccountNomineesRequest{
						ActorId: testActorId,
					},
				).Return(savingsAccountNomineesSuccessResponse, nil)
				m.mUserClient.EXPECT().GetNominees(
					context.Background(),
					&userPb.GetNomineesRequest{
						ActorId: testActorId,
					},
				).Return(nomineesErrorResponse, nil)
			},
		},
		{
			name: "should return savings account nominees when nominee attempts not found",
			args: args{
				ctx: context.Background(),
				req: getSavingsAccountNomineesRequest,
			},
			want: &savingsDataCollectorPb.GetSavingsAccountNomineesResponse{
				Status:                 rpcPb.StatusOk(),
				SavingsAccountNominees: expectedRedactedSavingsAccountNominees,
				NomineeAttempts:        nil, // No nominee attempts found
			},
			wantErr: false,
			wantMocks: func(m *mockStruct) {
				m.mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(
					context.Background(),
					getSavingsAccountNomineesRequest.GetHeader(),
					getSavingsAccountNomineesRequest.GetHeader().GetInformationLevel(),
				).Return(false, nil)
				m.mSavingsClient.EXPECT().GetSavingsAccountNominees(
					context.Background(),
					&savings.GetSavingsAccountNomineesRequest{
						ActorId: testActorId,
					},
				).Return(savingsAccountNomineesSuccessResponse, nil)
				m.mUserClient.EXPECT().GetNominees(
					context.Background(),
					&userPb.GetNomineesRequest{
						ActorId: testActorId,
					},
				).Return(nomineesNotFoundResponse, nil)
			},
		},
		{
			name: "should return success with nominees and attempts when all services succeed",
			args: args{
				ctx: context.Background(),
				req: getSavingsAccountNomineesRequest,
			},
			want:    expectedSuccessResponse,
			wantErr: false,
			wantMocks: func(m *mockStruct) {
				m.mAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(
					context.Background(),
					getSavingsAccountNomineesRequest.GetHeader(),
					getSavingsAccountNomineesRequest.GetHeader().GetInformationLevel(),
				).Return(false, nil)
				m.mSavingsClient.EXPECT().GetSavingsAccountNominees(
					context.Background(),
					&savings.GetSavingsAccountNomineesRequest{
						ActorId: testActorId,
					},
				).Return(savingsAccountNomineesSuccessResponse, nil)
				m.mUserClient.EXPECT().GetNominees(
					context.Background(),
					&userPb.GetNomineesRequest{
						ActorId: testActorId,
					},
				).Return(nomineesSuccessResponse, nil)
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			mAuthEngine := mockAuthEngine.NewMockIAuthEngine(ctr)
			mSavingsClient := mockSavings.NewMockSavingsClient(ctr)
			mUserClient := mockUser.NewMockUsersClient(ctr)

			if tt.wantMocks != nil {
				tt.wantMocks(&mockStruct{
					mAuthEngine:    mAuthEngine,
					mSavingsClient: mSavingsClient,
					mUserClient:    mUserClient,
				})
			}

			s := NewService(mAuthEngine, mSavingsClient, mUserClient)
			got, err := s.GetSavingsAccountNominees(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetSavingsAccountNominees() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetSavingsAccountNominees() \ngot = %v\nwant= %v", got, tt.want)
			}
		})
	}
}
