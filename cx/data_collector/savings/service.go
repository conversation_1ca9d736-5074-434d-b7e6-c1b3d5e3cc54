package savings

import (
	"context"
	"errors"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	savingsDataCollectorPb "github.com/epifi/gamma/api/cx/data_collector/savings"
	"github.com/epifi/gamma/api/savings"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/cx/customer_auth/auth_engine"
	cxLogger "github.com/epifi/gamma/cx/logger"
)

type Service struct {
	authEngine    auth_engine.IAuthEngine
	savingsClient savings.SavingsClient
	userClient    userPb.UsersClient
}

func NewService(authEngine auth_engine.IAuthEngine, savingsClient savings.SavingsClient, userClient userPb.UsersClient) *Service {
	return &Service{
		authEngine:    authEngine,
		savingsClient: savingsClient,
		userClient:    userClient,
	}
}

var _ savingsDataCollectorPb.SavingsServer = &Service{}

func (s *Service) GetSavingsAccountNominees(ctx context.Context, req *savingsDataCollectorPb.GetSavingsAccountNomineesRequest) (*savingsDataCollectorPb.GetSavingsAccountNomineesResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	if isActionRequired {
		logger.WarnWithCtx(ctx, "auth action required to show information")
		return &savingsDataCollectorPb.GetSavingsAccountNomineesResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	if req.GetHeader().GetActor() == nil {
		cxLogger.Info(ctx, "actor information not present in header")
		return &savingsDataCollectorPb.GetSavingsAccountNomineesResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("actor information not present in header"),
		}, nil
	}

	// GetSavingsAccountNominees rpc is used to get the current active nominees of savings account
	resp, err := s.savingsClient.GetSavingsAccountNominees(ctx, &savings.GetSavingsAccountNomineesRequest{
		ActorId: req.GetHeader().GetActor().GetId(),
	})
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		cxLogger.Error(ctx, "failed to get savings account nominees", zap.Error(rpcErr))
		if errors.Is(rpcErr, rpcPb.StatusAsError(rpcPb.StatusRecordNotFound())) {
			return &savingsDataCollectorPb.GetSavingsAccountNomineesResponse{Status: rpcPb.StatusRecordNotFoundWithDebugMsg("Savings account nominees not found")}, nil
		}
		return &savingsDataCollectorPb.GetSavingsAccountNomineesResponse{Status: rpcPb.StatusInternalWithDebugMsg("error in GetSavingsAccountNominees func")}, nil
	}

	// GetNominees rpc is used to get all the nominee creation attempts ( not necessarily sa nominee creation attempts )
	getNomineesResp, err := s.userClient.GetNominees(ctx, &userPb.GetNomineesRequest{
		ActorId: req.GetHeader().GetActor().GetId(),
	})
	rpcErr := epifigrpc.RPCError(getNomineesResp, err)
	switch {
	case rpcErr != nil && getNomineesResp.GetStatus().IsRecordNotFound():
		// Gracefully handling RNF for nominees attempt to propagate saving account nominees
		cxLogger.Error(ctx, "no nominees attempts found for actor", zap.String(logger.ACTOR_ID_V2, req.GetHeader().GetActor().GetId()), zap.Error(rpcErr))
	case rpcErr != nil:
		cxLogger.Error(ctx, "failed to get nominees attempts", zap.Error(rpcErr))
		return &savingsDataCollectorPb.GetSavingsAccountNomineesResponse{Status: rpcPb.StatusInternalWithDebugMsg("error in GetNominees func")}, nil
	}

	return &savingsDataCollectorPb.GetSavingsAccountNomineesResponse{
		Status:                 resp.GetStatus(),
		SavingsAccountNominees: redactSavingsAccountNominees(resp.GetSavingsAccountNominees()),
		NomineeAttempts:        redactNomineeAttempts(getNomineesResp.GetNominees()),
	}, nil
}
