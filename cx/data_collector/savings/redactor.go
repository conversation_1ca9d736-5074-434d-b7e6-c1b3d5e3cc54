package savings

import (
	"math"
	"strings"

	"github.com/epifi/be-common/pkg/mask"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/typesv2"
)

func maskHalf(s string) string {
	if len(s) == 0 {
		return ""
	}
	n := int(math.Ceil(float64(len(s)) / 2))
	return s[:n] + strings.Repeat("*", len(s)-n)
}

func redactEmail(email string) string {
	if email == "" {
		return ""
	}
	parts := strings.Split(email, "@")
	if len(parts) == 2 {
		maskedLocal := mask.GetMaskedString(mask.DontMaskFirstTwoAndLastTwoChars, parts[0])
		return maskedLocal + "@" + parts[1]
	}
	return mask.GetMaskedString(mask.MaskAllChars, email)
}

func redactAddress(addr *typesv2.PostalAddress) *typesv2.PostalAddress {
	if addr == nil {
		return nil
	}
	if lines := addr.GetAddressLines(); lines != nil {
		var maskedLines []string
		for _, line := range lines {
			maskedLines = append(maskedLines, maskHalf(line))
		}
		addr.AddressLines = maskedLines
	}
	addr.Locality = maskHalf(addr.GetLocality())
	addr.Sublocality = maskHalf(addr.GetSublocality())
	// Leave addr.AdministrativeArea unmasked for visibility
	addr.PostalCode = mask.GetMaskedString(mask.DontMaskLastFourChars, addr.GetPostalCode())
	return addr
}

func redactContactInfo(ci *typesv2.ContactInfo) *typesv2.ContactInfo {
	if ci == nil {
		return nil
	}
	if ci.GetPhoneNumber() != nil {
		ci.PhoneNumber = mask.GetMaskedPhoneNumberV2(ci.GetPhoneNumber())
	}
	ci.EmailId = redactEmail(ci.GetEmailId())
	ci.Address = redactAddress(ci.GetAddress())
	return ci
}

func redactGuardianInfo(gi *typesv2.GuardianInfo) *typesv2.GuardianInfo {
	if gi == nil {
		return nil
	}
	gi.Name = maskHalf(gi.GetName())
	gi.ContactInfo = redactContactInfo(gi.GetContactInfo())
	return gi
}

func redactNominee(nominee *typesv2.Nominee) *typesv2.Nominee {
	if nominee == nil {
		return nil
	}
	nominee.Pan = mask.GetMaskedString(mask.DontMaskLastFourChars, nominee.GetPan()) // Partial mask for backward compatibility

	if nominee.GetNomineeDocument() != nil {
		nominee.NomineeDocument.DocumentNumber = mask.GetMaskedString(mask.DontMaskLastFourChars, nominee.GetNomineeDocument().GetDocumentNumber())
	}

	nominee.ContactInfo = redactContactInfo(nominee.GetContactInfo())
	nominee.GuardianInfo = redactGuardianInfo(nominee.GetGuardianInfo())

	return nominee
}

func redactSavingsAccountNominees(nominees []*savings.SavingsAccountNominee) []*savings.SavingsAccountNominee {
	for _, san := range nominees {
		san.Nominee = redactNominee(san.GetNominee())
	}
	return nominees
}

func redactNomineeAttempts(nominees []*typesv2.Nominee) []*typesv2.Nominee {
	for i := range nominees {
		nominees[i] = redactNominee(nominees[i])
	}
	return nominees
}
