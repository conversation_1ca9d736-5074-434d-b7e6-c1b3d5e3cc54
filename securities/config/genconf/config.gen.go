// Code generated by tools/conf_gen/conf_gen.go
package genconf

import (
	"fmt"
	"reflect"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	questtypes "github.com/epifi/be-common/api/quest/types"
	cfg "github.com/epifi/be-common/pkg/cfg"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	gencfg "github.com/epifi/be-common/pkg/cfg/genconf"
	syncmap "github.com/epifi/be-common/pkg/syncmap"
	questsdk "github.com/epifi/be-common/quest/sdk"
	helper "github.com/epifi/be-common/tools/conf_gen/helper"
	config "github.com/epifi/gamma/securities/config"
)

type Config struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_StockCatalogRefreshSubscriber       *gencfg.SqsSubscriber
	_HistoricalPricesRedisCacheConfig    *HistoricalPricesRedisCacheConfig
	_SecuritiesCatalogAdditionSubscriber *gencfg.SqsSubscriber
	_SecuritiesHistoricalPriceSubscriber *gencfg.SqsSubscriber
	_AddNewSecuritiesConfig              *AddNewSecuritiesConfig
	_Application                         *config.Application
	_Server                              *config.Server
	_Logging                             *cfg.Logging
	_StocksDb                            *cfg.DB
	_Secrets                             *cfg.Secrets
	_AWS                                 *cfg.AWS
	_StockCatalogRefreshPublisher        *cfg.SqsPublisher
	_SecuritiesCatalogAdditionPublisher  *cfg.SqsPublisher
	_AddNewSecuritiesPublisher           *cfg.SqsPublisher
	_SecuritiesHistoricalPricePublisher  *cfg.SqsPublisher
}

func (obj *Config) StockCatalogRefreshSubscriber() *gencfg.SqsSubscriber {
	return obj._StockCatalogRefreshSubscriber
}
func (obj *Config) HistoricalPricesRedisCacheConfig() *HistoricalPricesRedisCacheConfig {
	return obj._HistoricalPricesRedisCacheConfig
}
func (obj *Config) SecuritiesCatalogAdditionSubscriber() *gencfg.SqsSubscriber {
	return obj._SecuritiesCatalogAdditionSubscriber
}
func (obj *Config) SecuritiesHistoricalPriceSubscriber() *gencfg.SqsSubscriber {
	return obj._SecuritiesHistoricalPriceSubscriber
}
func (obj *Config) AddNewSecuritiesConfig() *AddNewSecuritiesConfig {
	return obj._AddNewSecuritiesConfig
}
func (obj *Config) Application() *config.Application {
	return obj._Application
}
func (obj *Config) Server() *config.Server {
	return obj._Server
}
func (obj *Config) Logging() *cfg.Logging {
	return obj._Logging
}
func (obj *Config) StocksDb() *cfg.DB {
	return obj._StocksDb
}
func (obj *Config) Secrets() *cfg.Secrets {
	return obj._Secrets
}
func (obj *Config) AWS() *cfg.AWS {
	return obj._AWS
}
func (obj *Config) StockCatalogRefreshPublisher() *cfg.SqsPublisher {
	return obj._StockCatalogRefreshPublisher
}
func (obj *Config) SecuritiesCatalogAdditionPublisher() *cfg.SqsPublisher {
	return obj._SecuritiesCatalogAdditionPublisher
}
func (obj *Config) AddNewSecuritiesPublisher() *cfg.SqsPublisher {
	return obj._AddNewSecuritiesPublisher
}
func (obj *Config) SecuritiesHistoricalPricePublisher() *cfg.SqsPublisher {
	return obj._SecuritiesHistoricalPricePublisher
}

type HistoricalPricesRedisCacheConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_Ttl int64
}

func (obj *HistoricalPricesRedisCacheConfig) Ttl() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._Ttl))
}

type AddNewSecuritiesConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_MaximumPageNum int64
	_PageSize       int64
	// Adding this to unblock indian stocks for testing as GetStocks for US stocks is taking 3-4 seconds
	_ExchangeType      string
	_ExchangeTypeMutex *sync.RWMutex
}

func (obj *AddNewSecuritiesConfig) MaximumPageNum() int {
	return int(atomic.LoadInt64(&obj._MaximumPageNum))
}
func (obj *AddNewSecuritiesConfig) PageSize() int {
	return int(atomic.LoadInt64(&obj._PageSize))
}

// Adding this to unblock indian stocks for testing as GetStocks for US stocks is taking 3-4 seconds
func (obj *AddNewSecuritiesConfig) ExchangeType() string {
	obj._ExchangeTypeMutex.RLock()
	defer obj._ExchangeTypeMutex.RUnlock()
	return obj._ExchangeType
}

func NewConfig() (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_StockCatalogRefreshSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._StockCatalogRefreshSubscriber = _StockCatalogRefreshSubscriber
	helper.AddFieldSetters("stockcatalogrefreshsubscriber", _fieldSetters, _setters)
	_HistoricalPricesRedisCacheConfig, _fieldSetters := NewHistoricalPricesRedisCacheConfig()
	_obj._HistoricalPricesRedisCacheConfig = _HistoricalPricesRedisCacheConfig
	helper.AddFieldSetters("historicalpricesrediscacheconfig", _fieldSetters, _setters)
	_SecuritiesCatalogAdditionSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._SecuritiesCatalogAdditionSubscriber = _SecuritiesCatalogAdditionSubscriber
	helper.AddFieldSetters("securitiescatalogadditionsubscriber", _fieldSetters, _setters)
	_SecuritiesHistoricalPriceSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._SecuritiesHistoricalPriceSubscriber = _SecuritiesHistoricalPriceSubscriber
	helper.AddFieldSetters("securitieshistoricalpricesubscriber", _fieldSetters, _setters)
	_AddNewSecuritiesConfig, _fieldSetters := NewAddNewSecuritiesConfig()
	_obj._AddNewSecuritiesConfig = _AddNewSecuritiesConfig
	helper.AddFieldSetters("addnewsecuritiesconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func NewConfigWithQuest(questFieldPath string) (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_StockCatalogRefreshSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._StockCatalogRefreshSubscriber = _StockCatalogRefreshSubscriber
	helper.AddFieldSetters("stockcatalogrefreshsubscriber", _fieldSetters, _setters)
	_HistoricalPricesRedisCacheConfig, _fieldSetters := NewHistoricalPricesRedisCacheConfig()
	_obj._HistoricalPricesRedisCacheConfig = _HistoricalPricesRedisCacheConfig
	helper.AddFieldSetters("historicalpricesrediscacheconfig", _fieldSetters, _setters)
	_SecuritiesCatalogAdditionSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._SecuritiesCatalogAdditionSubscriber = _SecuritiesCatalogAdditionSubscriber
	helper.AddFieldSetters("securitiescatalogadditionsubscriber", _fieldSetters, _setters)
	_SecuritiesHistoricalPriceSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._SecuritiesHistoricalPriceSubscriber = _SecuritiesHistoricalPriceSubscriber
	helper.AddFieldSetters("securitieshistoricalpricesubscriber", _fieldSetters, _setters)
	_AddNewSecuritiesConfig, _fieldSetters := NewAddNewSecuritiesConfig()
	_obj._AddNewSecuritiesConfig = _AddNewSecuritiesConfig
	helper.AddFieldSetters("addnewsecuritiesconfig", _fieldSetters, _setters)

	return _obj, _setters
}

func (obj *Config) Init() {
	newObj, _ := NewConfig()
	*obj = *newObj
}

func (obj *Config) SetQuestSDK(questSdk questsdk.Client) {
}

func (obj *Config) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Config) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	return vars, nil
}

func (obj *Config) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Config)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Config) setDynamicField(v *config.Config, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "stockcatalogrefreshsubscriber":
		return obj._StockCatalogRefreshSubscriber.Set(v.StockCatalogRefreshSubscriber, true, path)
	case "historicalpricesrediscacheconfig":
		return obj._HistoricalPricesRedisCacheConfig.Set(v.HistoricalPricesRedisCacheConfig, true, path)
	case "securitiescatalogadditionsubscriber":
		return obj._SecuritiesCatalogAdditionSubscriber.Set(v.SecuritiesCatalogAdditionSubscriber, true, path)
	case "securitieshistoricalpricesubscriber":
		return obj._SecuritiesHistoricalPriceSubscriber.Set(v.SecuritiesHistoricalPriceSubscriber, true, path)
	case "addnewsecuritiesconfig":
		return obj._AddNewSecuritiesConfig.Set(v.AddNewSecuritiesConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Config) setDynamicFields(v *config.Config, dynamic bool, path []string) (err error) {

	err = obj._StockCatalogRefreshSubscriber.Set(v.StockCatalogRefreshSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._HistoricalPricesRedisCacheConfig.Set(v.HistoricalPricesRedisCacheConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._SecuritiesCatalogAdditionSubscriber.Set(v.SecuritiesCatalogAdditionSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._SecuritiesHistoricalPriceSubscriber.Set(v.SecuritiesHistoricalPriceSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AddNewSecuritiesConfig.Set(v.AddNewSecuritiesConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Config) setStaticFields(v *config.Config) error {

	obj._Application = v.Application
	obj._Server = v.Server
	obj._Logging = v.Logging
	obj._StocksDb = v.StocksDb
	obj._Secrets = v.Secrets
	obj._AWS = v.AWS
	obj._StockCatalogRefreshPublisher = v.StockCatalogRefreshPublisher
	obj._SecuritiesCatalogAdditionPublisher = v.SecuritiesCatalogAdditionPublisher
	obj._AddNewSecuritiesPublisher = v.AddNewSecuritiesPublisher
	obj._SecuritiesHistoricalPricePublisher = v.SecuritiesHistoricalPricePublisher
	return nil
}

func NewHistoricalPricesRedisCacheConfig() (_obj *HistoricalPricesRedisCacheConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &HistoricalPricesRedisCacheConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["ttl"] = _obj.SetTtl
	return _obj, _setters
}

func (obj *HistoricalPricesRedisCacheConfig) Init() {
	newObj, _ := NewHistoricalPricesRedisCacheConfig()
	*obj = *newObj
}

func (obj *HistoricalPricesRedisCacheConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *HistoricalPricesRedisCacheConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.HistoricalPricesRedisCacheConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *HistoricalPricesRedisCacheConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *HistoricalPricesRedisCacheConfig) setDynamicField(v *config.HistoricalPricesRedisCacheConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "ttl":
		return obj.SetTtl(v.Ttl, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *HistoricalPricesRedisCacheConfig) setDynamicFields(v *config.HistoricalPricesRedisCacheConfig, dynamic bool, path []string) (err error) {

	err = obj.SetTtl(v.Ttl, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *HistoricalPricesRedisCacheConfig) setStaticFields(v *config.HistoricalPricesRedisCacheConfig) error {

	return nil
}

func (obj *HistoricalPricesRedisCacheConfig) SetTtl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *HistoricalPricesRedisCacheConfig.Ttl", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._Ttl, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "Ttl")
	}
	return nil
}

func NewAddNewSecuritiesConfig() (_obj *AddNewSecuritiesConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &AddNewSecuritiesConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["maximumpagenum"] = _obj.SetMaximumPageNum
	_setters["pagesize"] = _obj.SetPageSize
	_setters["exchangetype"] = _obj.SetExchangeType
	_obj._ExchangeTypeMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *AddNewSecuritiesConfig) Init() {
	newObj, _ := NewAddNewSecuritiesConfig()
	*obj = *newObj
}

func (obj *AddNewSecuritiesConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *AddNewSecuritiesConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.AddNewSecuritiesConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *AddNewSecuritiesConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *AddNewSecuritiesConfig) setDynamicField(v *config.AddNewSecuritiesConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "maximumpagenum":
		return obj.SetMaximumPageNum(v.MaximumPageNum, true, nil)
	case "pagesize":
		return obj.SetPageSize(v.PageSize, true, nil)
	case "exchangetype":
		return obj.SetExchangeType(v.ExchangeType, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *AddNewSecuritiesConfig) setDynamicFields(v *config.AddNewSecuritiesConfig, dynamic bool, path []string) (err error) {

	err = obj.SetMaximumPageNum(v.MaximumPageNum, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetPageSize(v.PageSize, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetExchangeType(v.ExchangeType, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *AddNewSecuritiesConfig) setStaticFields(v *config.AddNewSecuritiesConfig) error {

	return nil
}

func (obj *AddNewSecuritiesConfig) SetMaximumPageNum(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *AddNewSecuritiesConfig.MaximumPageNum", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._MaximumPageNum, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MaximumPageNum")
	}
	return nil
}
func (obj *AddNewSecuritiesConfig) SetPageSize(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *AddNewSecuritiesConfig.PageSize", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._PageSize, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "PageSize")
	}
	return nil
}
func (obj *AddNewSecuritiesConfig) SetExchangeType(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AddNewSecuritiesConfig.ExchangeType", reflect.TypeOf(val))
	}
	obj._ExchangeTypeMutex.Lock()
	defer obj._ExchangeTypeMutex.Unlock()
	obj._ExchangeType = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "ExchangeType")
	}
	return nil
}
