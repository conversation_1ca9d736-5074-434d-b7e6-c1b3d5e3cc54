package brecaller

import (
	"context"
	"errors"

	"github.com/google/wire"

	"github.com/epifi/gamma/api/preapprovedloan"
	commonGenConf "github.com/epifi/gamma/preapprovedloan/config/common/genconf"
)

type IFactory interface {
	GetBreCaller(ctx context.Context, req *GetBreCallerRequest) (IBreCaller, error)
}

type GetBreCallerRequest struct {
	Loec              *preapprovedloan.LoanOfferEligibilityCriteria
	IsVendorHardOffer bool
}

type Factory struct {
	lendenBreCaller  *LendenBreCaller
	epifiBreCaller   *EpifiBreCaller
	fedBreCaller     *FederalBreCaller
	mvBlackBoxCaller *MoneyViewBlackBoxCaller
	flag             *commonGenConf.Flags
}

var FactoryWireSet = wire.NewSet(NewFactory, wire.Bind(new(IFactory), new(*Factory)))

func NewFactory(lendenBreCaller *LendenBreCaller, epifiBreCaller *EpifiBreCaller, fedBreCaller *FederalBreCaller, mvBlackBoxCaller *MoneyViewBlackBoxCaller, flag *commonGenConf.Flags) *Factory {
	return &Factory{
		lendenBreCaller:  lendenBreCaller,
		epifiBreCaller:   epifiBreCaller,
		fedBreCaller:     fedBreCaller,
		mvBlackBoxCaller: mvBlackBoxCaller,
		flag:             flag,
	}
}

func (f *Factory) GetBreCaller(ctx context.Context, req *GetBreCallerRequest) (IBreCaller, error) {
	switch {
	case req.Loec.GetVendor() == preapprovedloan.Vendor_LENDEN:
		return f.lendenBreCaller, nil
	case req.Loec.GetVendor() == preapprovedloan.Vendor_FEDERAL:
		return f.fedBreCaller, nil
	case req.Loec.GetVendor() == preapprovedloan.Vendor_MONEYVIEW:
		if f.flag.MoveMoneyViewFinalBreCallToBlackBox() {
			return f.mvBlackBoxCaller, nil
		}
		return f.epifiBreCaller, nil

	default:
		if req.IsVendorHardOffer {
			return nil, errors.New("bre caller vendor not supported for hard offer")
		}
		return f.epifiBreCaller, nil
	}
}
