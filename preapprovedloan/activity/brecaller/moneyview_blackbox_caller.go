// nolint:funlen,staticcheck,unparam
package brecaller

import (
	"context"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	creditReport "github.com/epifi/gamma/api/creditreportv2"
	palpb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/typesv2"
	mv "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/moneyview"
	palVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/moneyview"
	"github.com/epifi/gamma/pkg/feature/release"
	"github.com/epifi/gamma/preapprovedloan/helper"
	"github.com/epifi/gamma/preapprovedloan/userdata"
)

const (
	leadSource      = "FI-PA"
	dateFormat      = "fi-cibil-dtc-json"
	bureauProvider  = "bb-pa-cibil"
	timestampFormat = "2006-01-02T15:04:05.000Z"
)

type MoneyViewBlackBoxCaller struct {
	rpcHelper          *helper.RpcHelper
	mvVgClient         palVgPb.MoneyviewClient
	userDataProvider   userdata.IUserDataProvider
	releaseEvaluator   release.IEvaluator
	creditReportClient creditReport.CreditReportManagerClient
}

func NewMoneyViewBlackBoxCaller(
	rpcHelper *helper.RpcHelper,
	mvVgClient palVgPb.MoneyviewClient,
	userDataProvider userdata.IUserDataProvider,
	releaseEvaluator release.IEvaluator,
	creditReportClient creditReport.CreditReportManagerClient,
) *MoneyViewBlackBoxCaller {
	return &MoneyViewBlackBoxCaller{
		rpcHelper:          rpcHelper,
		mvVgClient:         mvVgClient,
		userDataProvider:   userDataProvider,
		releaseEvaluator:   releaseEvaluator,
		creditReportClient: creditReportClient,
	}
}

func (m *MoneyViewBlackBoxCaller) CheckAndGetLoanOffer(ctx context.Context, req *CheckAndGetLoanOfferRequest) (*CheckAndGetLoanOfferResponse, error) {

	userData, err := m.userDataProvider.GetDefaultUserData(ctx, &userdata.GetDefaultUserDataRequest{ActorId: req.Loec.GetActorId()})
	if err != nil {
		return nil, errors.Wrap(err, "error in getting user data")
	}

	employmentType := mv.EmploymentType_EMPLOYMENT_TYPE_SALARIED
	if userData.EmploymentDetails.EmploymentType == typesv2.EmploymentType_EMPLOYMENT_TYPE_SELF_EMPLOYED {
		employmentType = mv.EmploymentType_EMPLOYMENT_TYPE_SELF_EMPLOYED
	}

	vgGenerateBlackBoxOfferRequest := &mv.GenerateMvBlackBoxOfferRequest{
		Header:          &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_MONEYVIEW},
		LeadUserRef:     uuid.New().String(),
		LeadSource:      leadSource,
		DataFormat:      dateFormat,
		Pincode:         userData.GetAddress().GetPostalCode(),
		EmploymentType:  employmentType,
		DeclaredIncome:  userData.GetEmploymentDetails().GetMonthlyIncome(),
		Dob:             userData.GetGivenDateOfBirth(),
		BureauProvider:  bureauProvider,
		RawBureauReport: "",
	}

	crResp, crErr := m.creditReportClient.GetCreditReports(ctx, &creditReport.GetCreditReportsRequest{
		ActorId: req.Loec.GetActorId(),
		Vendor:  []commonvgpb.Vendor{commonvgpb.Vendor_CIBIL},
	})
	if grpcErr := epifigrpc.RPCError(crResp, crErr); grpcErr != nil {
		logger.Error(ctx, "error in fetching credit report", zap.Error(grpcErr))
		return nil, errors.Wrap(grpcErr, "error in fetching credit report")
	}

	if len(crResp.GetCreditReports()) > 0 {
		stringrepo := string(crResp.GetCreditReports()[0].GetCreditReportRaw().GetRawReport())
		vgGenerateBlackBoxOfferRequest.RawBureauReport = stringrepo
	} else {
		logger.Info(ctx, "credit report is not present for this user", zap.String(logger.ACTOR_ID_V2, req.Loec.GetActorId()), zap.Any("creditReportResp", crResp))
		return nil, errors.New("credit report is not present for this user")
	}

	vgGenerateBlackBoxOfferResponse, err := m.mvVgClient.GenerateMvBlackBoxOffer(ctx, vgGenerateBlackBoxOfferRequest)
	if te := epifigrpc.RPCError(vgGenerateBlackBoxOfferResponse, err); te != nil {
		return nil, errors.Wrap(te, "failed to call Mv blackBox")
	}

	if strings.EqualFold(vgGenerateBlackBoxOfferResponse.GetOfferDetails().GetStatus(), "REJECTED") {
		updateRejectedLoec, updateRejectedLoecFieldMask := getModifiedLoec(req.Loec, palpb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_REJECTED, palpb.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_VENDOR_BLACK_BOX)

		return &CheckAndGetLoanOfferResponse{
			UpdatedLoec:     updateRejectedLoec,
			UpdateFieldMask: updateRejectedLoecFieldMask,
		}, nil
	}

	vgSaveBlackBoxOfferRequest := &mv.SaveMvBlackBoxOfferRequest{
		Header:           &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_MONEYVIEW},
		LeadUserRef:      vgGenerateBlackBoxOfferResponse.GetOfferDetails().GetLeadUserRef(),
		Status:           vgGenerateBlackBoxOfferResponse.GetOfferDetails().GetStatus(),
		BestOfferAmount:  vgGenerateBlackBoxOfferResponse.GetOfferDetails().GetBestOfferAmount(),
		BestOfferTenure:  vgGenerateBlackBoxOfferResponse.GetOfferDetails().GetBestOfferTenure(),
		BestOfferRoi:     vgGenerateBlackBoxOfferResponse.GetOfferDetails().GetBestOfferRoi(),
		ExpiryDate:       vgGenerateBlackBoxOfferResponse.GetOfferDetails().GetExpiryDate(),
		Category:         vgGenerateBlackBoxOfferResponse.GetOfferDetails().GetCategory(),
		LeadSource:       vgGenerateBlackBoxOfferResponse.GetOfferDetails().GetLeadSource(),
		MetaIncome:       vgGenerateBlackBoxOfferResponse.GetOfferDetails().GetMetaIncome(),
		MetaTagName:      vgGenerateBlackBoxOfferResponse.GetOfferDetails().GetMetaTagName(),
		EmploymentType:   vgGenerateBlackBoxOfferResponse.GetOfferDetails().GetEmploymentType(),
		SummaryVariables: vgGenerateBlackBoxOfferResponse.GetOfferDetails().GetSummaryVariables(),
	}

	vgSaveBlackBoxOfferResponse, err := m.mvVgClient.SaveMvBlackBoxOffer(ctx, vgSaveBlackBoxOfferRequest)
	if te := epifigrpc.RPCError(vgSaveBlackBoxOfferResponse, err); te != nil {
		return nil, errors.Wrap(te, "failed to save Mv Black box offer ")
	}

	loanOffer, err := createLoanOfferFromVendorResponse(ctx, req.Loec, vgGenerateBlackBoxOfferResponse)
	if err != nil {
		return nil, errors.Wrap(err, "error in getting loan offer from vendor response")
	}
	updatedLoec, loecFieldMasks := getModifiedLoec(req.Loec, palpb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_APPROVED, palpb.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_VENDOR_BLACK_BOX)

	return &CheckAndGetLoanOfferResponse{
		UpdatedLoec:     updatedLoec,
		UpdateFieldMask: loecFieldMasks,
		LoanOffer:       loanOffer,
	}, nil

}

func getModifiedLoec(loec *palpb.LoanOfferEligibilityCriteria, status palpb.LoanOfferEligibilityCriteriaStatus, subStatus palpb.LoanOfferEligibilityCriteriaSubStatus) (*palpb.LoanOfferEligibilityCriteria, []palpb.LoanOfferEligibilityCriteriaFieldMask) {
	loec.Status = status
	loec.SubStatus = subStatus
	loecFieldMasks := []palpb.LoanOfferEligibilityCriteriaFieldMask{
		palpb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_STATUS,
		palpb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_SUB_STATUS,
	}
	return loec, loecFieldMasks
}

// todo : changes the default values after confirming with product
func createLoanOfferFromVendorResponse(ctx context.Context, loec *palpb.LoanOfferEligibilityCriteria, response *palVgPb.GenerateMvBlackBoxOfferResponse) (*palpb.LoanOffer, error) {

	validTill, err := datetime.ParseStringTimeStampProto(timestampFormat, response.GetOfferDetails().GetExpiryDate())
	if err != nil {
		logger.Error(ctx, "error in converting string to timestamp", zap.Error(err))
		return nil, errors.Wrap(err, "error in converting string to timestamp")
	}

	return &palpb.LoanOffer{
		ActorId:       loec.GetActorId(),
		VendorOfferId: response.GetOfferDetails().GetLeadUserRef(),
		Vendor:        loec.GetVendor(),
		OfferConstraints: &palpb.OfferConstraints{
			MinLoanAmount:   moneyPkg.ParseFloat(10000.0, "INR"),
			MaxLoanAmount:   moneyPkg.ParseFloat(response.GetOfferDetails().GetBestOfferAmount(), "INR"),
			MaxEmiAmount:    moneyPkg.ParseFloat(10000.0, "INR"),
			MinTenureMonths: 1,
			MaxTenureMonths: response.GetOfferDetails().GetBestOfferTenure(),
		},
		ProcessingInfo: &palpb.OfferProcessingInfo{
			Gst: 18.0,
			InterestRate: []*palpb.RangeData{
				{
					Start: 0,
					End:   1000000,
					Value: &palpb.RangeData_Percentage{
						Percentage: 22.0,
					},
				},
			},
			ProcessingFee: []*palpb.RangeData{
				{
					Start: 0,
					End:   1000000,
					Value: &palpb.RangeData_Percentage{
						Percentage: 3.0,
					},
				},
			},
		},
		ValidSince:                     timestampPb.New(time.Now().In(datetime.IST)),
		ValidTill:                      validTill,
		LoanOfferEligibilityCriteriaId: loec.GetId(),
		LoanProgram:                    loec.GetLoanProgram(),
	}, nil
}

func (m *MoneyViewBlackBoxCaller) FetchHardVendorLoanOffer(ctx context.Context, req *CheckAndGetLoanOfferRequest) (*CheckAndGetLoanOfferResponse, error) {
	panic("implement me")
}
