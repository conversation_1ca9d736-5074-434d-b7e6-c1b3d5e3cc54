package fiftyfin

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	onceV2 "github.com/epifi/be-common/pkg/counter/once/v2"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/api/typesv2/account"
	fiftyfinPb "github.com/epifi/gamma/api/vendorgateway/lending/securedloans/fiftyfin"
	palActivity "github.com/epifi/gamma/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/events"
	"github.com/epifi/gamma/preapprovedloan/helper"
	"github.com/epifi/gamma/preapprovedloan/metrics"
)

const (
	EsignMandateDeeplinkRetryBackoff = 2000
	LinkGenerationMaxAcceptableDelay = 3 * time.Hour
)

var loanProcessExpiryRestartStages = []palPb.LoanStepExecutionStepName{
	palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KFS,
	palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_E_AGREEMENT,
	palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_MANDATE,
}

func (p *Processor) FiftyfinGetApplicationProcessStatus(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	lg := activity.GetLogger(ctx)
	actRes, actErr := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep:      lse,
			LseFieldMasks: []palPb.LoanStepExecutionFieldMask{},
		}

		isStageCompleted, stageFailed := false, false
		existingLoan, existingLoanErr := p.getExistingLoanDetail(ctx, req.GetLoanStep(), "", "")
		if existingLoanErr != nil {
			lg.Error("Error fetching existing loan detail", zap.Error(existingLoanErr))
			return nil, p.wrapErrWithCustomKey(epifierrors.ErrTransient, existingLoanErr.Error())
		}

		// TODO(bhumij): Check for mandate link expiry and how to handle it

		switch req.GetLoanStep().GetStepName() {
		case palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_MANDATE:
			isStageCompleted = existingLoan.GetData().GetEMandateCompleted()
		case palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_E_AGREEMENT:
			isStageCompleted = existingLoan.GetData().GetESignCompleted()
		case palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KFS:
			isStageCompleted = existingLoan.GetData().GetKfsCompleted()
			stageFailed = existingLoan.GetData().GetKfsRejected()
		case palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_DRAWDOWN:
			isStageCompleted = existingLoan.GetData().GetProcessCompleted()
		default:
			lg.Error("Cannot figure out status from group stage", zap.String("Stage", req.GetLoanStep().GetGroupStage().String()))
			return res, p.wrapErrWithCustomKey(epifierrors.ErrTransient, "cannot figure out status from group stage")
		}

		if isStageCompleted {
			return res, nil
		}
		if stageFailed && req.GetLoanStep().GetStepName() == palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KFS {
			res.LoanStep.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
			res.LoanStep.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_USER_REJECTED_KFS
			res.LseFieldMasks = append(res.GetLseFieldMasks(), palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS)
			return res, nil
		}
		loanReq, err := p.loanRequestDao.GetById(ctx, lse.GetRefId())
		if err != nil {
			lg.Error("error while fetching loan request by id", zap.Error(err))
			return nil, p.wrapErrWithCustomKey(epifierrors.ErrTransient, "error while fetching loan request by id")
		}
		if lo.Contains(loanProcessExpiryRestartStages, lse.GetStepName()) {
			var restartLoan bool
			restartLoan, res.LoanStep, res.LseFieldMasks = p.handleVendorLoanProcessExpiry(ctx, loanReq, res.GetLoanStep(), res.GetLseFieldMasks())
			if restartLoan {
				return res, nil
			}
		}
		return nil, p.wrapErrWithCustomKey(epifierrors.ErrTransient, fmt.Sprintf("status for stage: %s is not updated", req.GetLoanStep().GetGroupStage().String()))
	})

	return actRes, actErr
}

func (p *Processor) getExistingLoanDetail(ctx context.Context, lse *palPb.LoanStepExecution, vendorApplicantIdStr, loanIdStr string) (*fiftyfinPb.FetchExistingLoanDetailsResponse, error) {
	if vendorApplicantIdStr == "" {
		loanApplicantRes, loanApplicantErr := p.loanApplicantDao.GetByActorId(ctx, lse.GetActorId())
		if loanApplicantErr != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error while getting loan applicant for the user : %s", loanApplicantErr))
		}
		vendorApplicantIdStr = loanApplicantRes.GetVendorApplicantId()
	}
	vendorApplicantId, vaiErr := p.convertStringToInt32(vendorApplicantIdStr)
	if vaiErr != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error while converting vendor applicant id to int : %s", vaiErr))
	}
	if loanIdStr == "" {
		loanRequest, lrErr := p.loanRequestDao.GetById(ctx, lse.GetRefId())
		if lrErr != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error getting loan request from orchestration id : %s", lrErr))
		}
		loanIdStr = loanRequest.GetVendorRequestId()
	}
	loanId, liErr := p.convertStringToInt32(loanIdStr)
	if liErr != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error converting loanId to integer : %s", liErr))
	}
	existingLoanResp, existingLoanErr := p.ffVgClient.FetchExistingLoanDetails(ctx, &fiftyfinPb.FetchExistingLoanDetailsRequest{
		LoanId: loanId,
		UserId: vendorApplicantId,
	})
	if rpcErr := epifigrpc.RPCError(existingLoanResp, existingLoanErr); rpcErr != nil {
		return nil, rpcErr
	}
	return existingLoanResp, nil
}

// nolint:funlen
func (p *Processor) FiftyfinGetEsignMandatePage(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	lg := activity.GetLogger(ctx)
	actRes, actErr := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep: lse,
		}
		loanApplicantRes, loanApplicantErr := p.loanApplicantDao.GetByActorId(ctx, lse.GetActorId())
		if loanApplicantErr != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error while getting loan applicant for the user : %s", loanApplicantErr.Error()))
		}
		loanRequest, lrErr := p.loanRequestDao.GetById(ctx, req.GetLoanStep().GetRefId())
		if lrErr != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error getting loan request from orchestration id : %s", lrErr))
		}
		existingLoan, existingLoanErr := p.getExistingLoanDetail(ctx, req.GetLoanStep(), loanApplicantRes.GetVendorApplicantId(), loanRequest.GetVendorRequestId())
		if existingLoanErr != nil {
			lg.Error("Error fetching existing loan detail", zap.Error(existingLoanErr))
			return nil, p.wrapErrWithCustomKey(epifierrors.ErrTransient, existingLoanErr.Error())
		}
		appVersionData, err := p.rpcHelper.FetchAppVersionInfo(ctx, req.GetLoanStep().GetActorId())
		if err != nil {
			lg.Error("error in getting the app version from user service", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetLoanStep().GetActorId()))
			return nil, errors.Wrap(epifierrors.ErrTransient, "error in getting the app version from user service")
		}
		// when shouldOpenInExternalTab is true, ios app shows a blank screen to the user
		// so, we're disabling this flag for ios. Anyway, ios cannot open external browser tabs
		shouldOpenInExternalTab := appVersionData.GetPlatform() != commontypes.Platform_IOS

		var nextScreen *deeplink.Deeplink
		switch lse.GetStepName() {
		case palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KFS:
			if existingLoan.GetData().GetKfsCompleted() {
				lg.Info("KFS already completed")
				return res, nil
			}
			if existingLoan.GetData().GetKfsLink() == "" {
				lg.Error("kfs url not ready yet")
				p.recordLinkGenerationDelays(ctx, loanRequest, lse)
				return nil, p.wrapErrWithCustomKey(epifierrors.ErrTransient, "kfs url not ready yet")
			}
			kfsScreen, kfsErr := p.deeplinkProvider.GetKfsScreenDeeplink(
				p.deeplinkProvider.GetLoanHeader(),
				req.GetLoanStep().GetRefId(),
				existingLoan.GetData().GetKfsLink(),
				EsignMandateDeeplinkRetryBackoff,
				shouldOpenInExternalTab,
			)
			if kfsErr != nil {
				return nil, p.wrapErrWithCustomKey(epifierrors.ErrTransient, kfsErr.Error())
			}
			progressScreen, err := p.deeplinkProvider.GetApplicationProgressScreen(ctx, p.deeplinkProvider.GetLoanHeader(), lse.GetActorId(), lse.GetStepName(), kfsScreen)
			if err != nil {
				return nil, p.wrapErrWithCustomKey(epifierrors.ErrTransient, err.Error())
			}
			nextScreen = progressScreen
		case palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_E_AGREEMENT:
			link := existingLoan.GetData().GetESignLink()
			var err error
			if link == "" {
				lg.Error("e-sign url not ready yet, calling regenerate api")
				link, err = p.regenerateAndFetchLink(ctx, loanRequest, lse, loanApplicantRes)
				if err != nil {
					lg.Error("error while generating e-sign loan document link", zap.Error(err))
					p.recordLinkGenerationDelays(ctx, loanRequest, lse)
					return nil, err
				}
			}
			eSignScreen, esErr := p.deeplinkProvider.GetEsignScreenDeeplink(
				p.deeplinkProvider.GetLoanHeader(),
				req.GetLoanStep().GetRefId(),
				link,
				EsignMandateDeeplinkRetryBackoff,
				shouldOpenInExternalTab,
			)
			if esErr != nil {
				return nil, p.wrapErrWithCustomKey(epifierrors.ErrTransient, esErr.Error())
			}
			nextScreen = eSignScreen
		case palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_MANDATE:
			link := existingLoan.GetData().GetEMandateLink()
			var err error
			if link == "" {
				lg.Error("e-mandate url not ready yet, calling regenerate api")
				link, err = p.regenerateAndFetchLink(ctx, loanRequest, lse, loanApplicantRes)
				if err != nil {
					lg.Error("error while generating mandate loan document link", zap.Error(err))
					p.recordLinkGenerationDelays(ctx, loanRequest, lse)
					return nil, err
				}
			}
			savingsAccount, err := p.rpcHelper.GetSavingsAccountDetails(ctx, req.GetLoanStep().GetActorId(), commonvgpb.Vendor_FEDERAL_BANK, account.AccountProductOffering_APO_REGULAR)
			if err != nil {
				lg.Error("failed to get bank account details", zap.Error(err))
				return nil, p.wrapErrWithCustomKey(epifierrors.ErrTransient, err.Error())
			}
			accountNum := savingsAccount.GetAccountNo()
			mandateScreen, msErr := p.deeplinkProvider.GetMandateIntroScreenDeeplink(
				p.deeplinkProvider.GetLoanHeader(),
				req.GetLoanStep().GetRefId(),
				accountNum[len(accountNum)-4:], // last 4 digits
				link,
				EsignMandateDeeplinkRetryBackoff,
				appVersionData.GetPlatform(),
			)
			if msErr != nil {
				return nil, p.wrapErrWithCustomKey(epifierrors.ErrTransient, msErr.Error())
			}
			nextScreen = mandateScreen
		default:
			lg.Error("cannot figure out which screen to show for current group stage", zap.String("Stage", req.GetLoanStep().GetGroupStage().String()))
			return nil, p.wrapErrWithCustomKey(epifierrors.ErrTransient, "cannot figure out which screen to show for current group stage")
		}
		// update LR next action to mandate intro screen
		updateLrErr := palActivity.UpdateNextActionInLoanRequest(ctx, p.loanRequestDao, res.GetLoanStep().GetRefId(), nextScreen)
		if updateLrErr != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, updateLrErr.Error())
		}
		// record time taken in doc link generation
		metrics.RecordLoanProcessStepDuration(loanRequest.GetVendor(), loanRequest.GetLoanProgram(), loanRequest.GetType(), lse.GetStepName(), metrics.DocLinkGeneration, lse.GetCreatedAt().AsTime())
		p.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), events.NewLoanProcessTaskCompleted(lse.GetActorId(), loanRequest.GetVendor(), loanRequest.GetLoanProgram(), lse.GetStepName(), loanRequest.GetType(), events.DocLinkGeneration))
		return res, nil
	})
	return actRes, actErr
}

func (p *Processor) recordLinkGenerationDelays(ctx context.Context, lr *palPb.LoanRequest, lse *palPb.LoanStepExecution) {
	lg := activity.GetLogger(ctx)
	currTime := time.Now()
	stepStartTime := lse.GetCreatedAt().AsTime()
	delay := currTime.Sub(stepStartTime)
	if delay < LinkGenerationMaxAcceptableDelay {
		return
	}
	ownership := helper.GetPalOwnership(lr.GetVendor())
	doOnceMgr, doOnceErr := p.multiDbDOnceMgr.GetDoOnceManagerForOwnership(ownership, onceV2.NewDoOnce)
	if doOnceErr != nil {
		lg.Error("Error getting doOnceMgr for ownership for recording link generation delays", zap.String(logger.OWNERSHIP, ownership.String()), zap.Error(doOnceErr))
		return
	}
	err := doOnceMgr.DoOnceFn(ctx, fmt.Sprintf("link-generation-delay-%s", lse.GetId()), func() error {
		lg.Info("delay in lamf loan document link generation", zap.String(logger.ACTOR_ID_V2, lr.GetActorId()), zap.String("lrId", lr.GetId()), zap.String("lseId", lse.GetId()))
		metrics.RecordLoanProcessStepErrorCount(lr.GetVendor(), lr.GetLoanProgram(), lr.GetType(), lse.GetStepName(), metrics.DocLinkGeneration, metrics.LinkNotGenerated)
		return nil
	})
	if err != nil {
		lg.Error("error in do once task while recording link generation delay", zap.Error(err))
		return
	}
}

func (p *Processor) regenerateAndFetchLink(ctx context.Context, lr *palPb.LoanRequest, lse *palPb.LoanStepExecution, la *palPb.LoanApplicant) (string, error) {
	docType := fiftyfinPb.LoanDocumentType_LOAN_DOCUMENT_TYPE_UNSPECIFIED
	switch lse.GetStepName() {
	case palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_E_AGREEMENT:
		docType = fiftyfinPb.LoanDocumentType_LOAN_DOCUMENT_TYPE_E_SIGN
	case palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_MANDATE:
		docType = fiftyfinPb.LoanDocumentType_LOAN_DOCUMENT_TYPE_MANDATE
	case palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KFS:
		docType = fiftyfinPb.LoanDocumentType_LOAN_DOCUMENT_TYPE_KFS
	default:
		return "", errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("unhandled loan step for generating loan document link : %s", lse.GetStepName().String()))
	}
	linkRegenRes, err := p.ffVgClient.RegenerateLoanDocumentLink(ctx, &fiftyfinPb.RegenerateLoanDocumentLinkRequest{
		UserId:  la.GetVendorApplicantId(),
		LoanId:  lr.GetVendorRequestId(),
		DocType: docType,
	})
	if rpcErr := epifigrpc.RPCError(linkRegenRes, err); rpcErr != nil {
		return "", errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error regenerating %s doc link : %s", docType.String(), rpcErr.Error()))
	}
	existingLoanRes, err := p.getExistingLoanDetail(ctx, lse, la.GetVendorApplicantId(), lr.GetVendorRequestId())
	if err != nil {
		return "", err
	}
	var link string
	switch lse.GetStepName() {
	case palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_E_AGREEMENT:
		link = existingLoanRes.GetData().GetESignLink()
	case palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_MANDATE:
		link = existingLoanRes.GetData().GetEMandateLink()
	case palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KFS:
		link = existingLoanRes.GetData().GetKfsLink()
	default:
		return "", errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("unhandled loan step for returning correct loan link type : %s", lse.GetStepName().String()))
	}
	if link == "" {
		return "", errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("%s link not generated even after successful link regenerate call", docType.String()))
	}
	return link, nil
}
