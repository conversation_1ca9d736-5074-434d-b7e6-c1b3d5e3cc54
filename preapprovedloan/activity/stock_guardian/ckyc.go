// nolint:gocritic,funlen,goimports
package stock_guardian

import (
	"context"
	"fmt"
	"strings"

	s3types "github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/epifitemporal"
	loggerPkg "github.com/epifi/be-common/pkg/logger"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/api/preapprovedloan/enums"
	sgApplicationPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/application"
	sKycPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/kyc"
	sgMatrixPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/matrix"
	palActivity "github.com/epifi/gamma/preapprovedloan/activity"
	deeplinkPal "github.com/epifi/gamma/preapprovedloan/deeplink"
	dlProvider "github.com/epifi/gamma/preapprovedloan/deeplink/provider"
)

const dobLayout = "02 January 2006"

func (p *Processor) SgInitiateKyc(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep:      lse,
			LseFieldMasks: []palPb.LoanStepExecutionFieldMask{},
		}
		lg := activity.GetLogger(ctx)

		// fetch loan request
		lr, lrErr := p.loanRequestDao.GetById(ctx, lse.GetRefId())
		if lrErr != nil {
			lg.Error("error in lr get by id", zap.Error(lrErr))
			if errors.Is(lrErr, epifierrors.ErrRecordNotFound) {
				palActivity.MarkLoanStepFail(lse)
				return res, nil
			}
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in lr get by id, err: %v", lrErr))
		}

		initKycResp, initKycErr := p.sgApplicationApiGateway.InitiateKyc(ctx, &sgApplicationPb.InitiateKycRequest{
			LoanHeader: &sgApplicationPb.LoanHeader{
				ClientId: clientId,
			},
			ApplicationId: lr.GetVendorRequestId(),
		})
		if te := epifigrpc.RPCError(initKycResp, initKycErr); te != nil && !p.isKycAlreadyDone(initKycResp.GetStatus()) {
			lg.Error("failed to init kyc from SG", zap.Error(te))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("unable to initiate kyc from SG, err: %v", te))
		}

		isKycAlreadyDone := p.isKycAlreadyDone(initKycResp.GetStatus())
		if initKycResp.GetKycApplicationId() == "" && !isKycAlreadyDone {
			lg.Error("kyc application id can't be empty")
			return nil, errors.Wrap(epifierrors.ErrTransient, "kyc application id can't be empty")
		}

		if lr.GetDetails().GetApplicationDetails() == nil {
			lr.GetDetails().ApplicationDetails = &palPb.ApplicationDetails{}
		}
		if lr.GetDetails().GetApplicationDetails().GetKycDetails() == nil {
			lr.GetDetails().GetApplicationDetails().KycDetails = &palPb.KycDetails{}
		}

		lr.GetDetails().GetApplicationDetails().GetKycDetails().VendorKycRequestId = initKycResp.GetKycApplicationId()
		lr.GetDetails().GetApplicationDetails().GetKycDetails().IsKycAlreadyDonePreviously = isKycAlreadyDone

		if updateErr := p.loanRequestDao.Update(ctx, lr, []palPb.LoanRequestFieldMask{palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_DETAILS}); updateErr != nil {
			lg.Error("failed to update loan request", zap.Error(updateErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update loan request, err: %v", updateErr))
		}

		lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS
		if updateErr := p.loanStepExecutionDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS,
		}); updateErr != nil {
			lg.Error("failed to update lse", zap.Error(updateErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update lse, err: %v", updateErr))
		}

		return res, nil
	})
	return actRes, actErr
}

func (p *Processor) SgGetOTPVerificationStatus(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		if p.config.Flags().IsSgDigilockerEnabled() {
			palActivity.MarkLoanStepSuccess(lse)
			lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_SKIPPED
			return &palActivityPb.PalActivityResponse{
				LoanStep: lse,
				LseFieldMasks: []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
					palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS},
				IsSyncTaskDone: true,
			}, nil
		}

		lg := activity.GetLogger(ctx)
		// fetch loan request
		lr, lrErr := p.loanRequestDao.GetById(ctx, lse.GetRefId())
		if lrErr != nil {
			lg.Error("error in lr get by id", zap.Error(lrErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in lr get by id, err: %v", lrErr))
		}

		res, err := p.getOTPVerificationStatus(ctx, lse, lr)
		if err != nil {
			lg.Error("error waiting for download", zap.Error(err))
			if errors.Is(err, epifierrors.ErrPermanent) {
				return &palActivityPb.PalActivityResponse{
					LoanStep:      lse,
					LseFieldMasks: []palPb.LoanStepExecutionFieldMask{},
				}, nil
			}
			return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
		}
		if len(res.lseFieldMasks) != 0 {
			err = p.loanStepExecutionDao.Update(ctx, lse, res.lseFieldMasks)
			if err != nil {
				return nil, errors.Wrapf(err, "error updating lse sub status to user temporary blocked: %s", lse.GetId())
			}
		}
		if res.nextAction != nil {
			return &palActivityPb.PalActivityResponse{LoanStep: lse, NextAction: res.nextAction},
				epifitemporal.NewTransientError(errors.Errorf("otp not verified yet, keep sending next action"))
		}
		lg.Info("no next action in otp verification, either successful or failed")
		return &palActivityPb.PalActivityResponse{LoanStep: lse}, nil
	})
	return actRes, actErr
}

type verifyOTPResponse struct {
	lse           *palPb.LoanStepExecution
	lseFieldMasks []palPb.LoanStepExecutionFieldMask
	nextAction    *deeplinkPb.Deeplink
}

// TODO(Brijesh): Refactor to separate DB updates and deeplink generation
func (p *Processor) getOTPVerificationStatus(ctx context.Context, lse *palPb.LoanStepExecution, lr *palPb.LoanRequest) (*verifyOTPResponse, error) {
	logger := activity.GetLogger(ctx)
	kycApplicationId := lr.GetDetails().GetApplicationDetails().GetKycDetails().GetVendorKycRequestId()

	// Skipping Digilocker KYC stage as goblin application must be waiting on the digilocker step
	skipDigilocker, skipDigilockerErr := p.sgMatrixApiGateway.SkipCustomerApplicationStage(ctx, &sgMatrixPb.SkipCustomerApplicationStageRequest{
		CustomerApplicationId: kycApplicationId,
		Stage:                 sgMatrixPb.Stage_STAGE_DIGILOCKER_KYC,
	})
	if te := epifigrpc.RPCError(skipDigilocker, skipDigilockerErr); te != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get SkipCustomerApplicationStages, err: %v", te))
	}

	applicationStatusRes, err := p.sgMatrixApiGateway.GetCustomerApplicationStatus(ctx,
		&sgMatrixPb.GetCustomerApplicationStatusRequest{
			ApplicationIdentifier: &sgMatrixPb.GetCustomerApplicationStatusRequest_ApplicationId{
				ApplicationId: kycApplicationId,
			},
		},
	)
	if err = epifigrpc.RPCError(applicationStatusRes, err); err != nil {
		return nil, errors.Wrapf(err, "error getting KYC application status: %s", kycApplicationId)
	}
	switch applicationStatusRes.GetStageStatus() {
	case sgMatrixPb.StageStatus_STAGE_STATUS_IN_PROGRESS:
		switch applicationStatusRes.GetNextAction() {
		case sgMatrixPb.Action_ACTION_CKYC_OTP_VERIFICATION:
			lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_OTP_VERIFICATION_PENDING
			return &verifyOTPResponse{
				lse:           lse,
				lseFieldMasks: []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS},
				nextAction: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_APPLICATION_CONFIRMATION_VIA_OTP_SCREEN,
					ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanApplicationConfirmationViaOtpScreenOptions{
						PreApprovedLoanApplicationConfirmationViaOtpScreenOptions: &deeplinkPb.PreApprovedLoanApplicationConfirmationViaOtpScreenOptions{
							LoanRequestId: lr.GetId(),
							LoanHeader: deeplinkPal.GetFeLoanHeaderByBeLoanHeader(&palPb.LoanHeader{
								LoanProgram: lr.GetLoanProgram(),
								Vendor:      lr.GetVendor(),
							}),
							LoanStepExecutionId: lse.GetId(),
							DoAutoRead:          false,
							OtpFlow:             pal_enums.OtpFlow_OTP_FLOW_CKYC_OTP_VERIFCATION,
						},
					},
				},
			}, nil
		case sgMatrixPb.Action_ACTION_CKYC_OTP_VERIFICATION_USER_TEMP_BLOCKED:
			lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_USER_TEMPORARY_BLOCKED
			return &verifyOTPResponse{
				lse:           lse,
				lseFieldMasks: []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS},
				nextAction:    &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN},
			}, nil
		default:
			logger.Error("no next action received for KYC stage in progress, assuming user has taken all actions", zap.String(loggerPkg.ACTION_TYPE, applicationStatusRes.GetNextAction().String()))
			lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_OTP_VERIFICATION_SUCCESSFUL
			return &verifyOTPResponse{
				lse:           lse,
				lseFieldMasks: []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS},
			}, nil
		}
	case sgMatrixPb.StageStatus_STAGE_STATUS_FAILED:
		// TODO (@anupam): this is a bucket failure, add proper substatus according to the reasons, also pipe this reasons from SG
		lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_KYC_REJECTED
		lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED
		return &verifyOTPResponse{
			lse: lse,
			lseFieldMasks: []palPb.LoanStepExecutionFieldMask{
				palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
				palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
			},
		}, nil
	case sgMatrixPb.StageStatus_STAGE_STATUS_SUCCESS:
		lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_OTP_VERIFICATION_SUCCESSFUL
		return &verifyOTPResponse{
			lse:           lse,
			lseFieldMasks: []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS},
		}, nil
	default:
		logger.Error("unexpected stage status received for CKYC", zap.String(loggerPkg.STAGE_STATUS, applicationStatusRes.GetStageStatus().String()))
		return nil, errors.Errorf("unexpected stage status received for CKYC: %s", applicationStatusRes.GetStageStatus().String())
	}
}

// nolint:dupl,gocritic
func (p *Processor) SgCkycStatusAndSetVerificationScreen(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep: lse,
		}
		lg := activity.GetLogger(ctx)

		// If digilocker is enabled, then skip ckyc step
		if p.config.Flags().IsSgDigilockerEnabled() {
			palActivity.MarkLoanStepSuccess(lse)
			lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_SKIPPED
			res.LseFieldMasks = append(res.GetLseFieldMasks(), palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
				palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS)
			return res, nil
		}

		// fetch loan request
		lr, lrErr := p.loanRequestDao.GetById(ctx, lse.GetRefId())
		if lrErr != nil {
			lg.Error("error in lr get by id", zap.Error(lrErr))
			if errors.Is(lrErr, epifierrors.ErrRecordNotFound) {
				palActivity.MarkLoanStepFail(lse)
				return res, nil
			}
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in lr get by id, err: %v", lrErr))
		}
		kycData := lr.GetDetails().GetApplicationDetails().GetKycDetails()

		kycApplicationId := kycData.GetVendorKycRequestId()
		kycDataResp := &sKycPb.GetKYCDataResponse{}
		var kycDataErr error
		// we will not do the ckyc if the kyc is already done previously
		if !kycData.GetIsKycAlreadyDonePreviously() {
			gcasResp, gcasErr := p.sgMatrixApiGateway.GetCustomerApplicationStatus(ctx, &sgMatrixPb.GetCustomerApplicationStatusRequest{
				ApplicationIdentifier: &sgMatrixPb.GetCustomerApplicationStatusRequest_ApplicationId{
					ApplicationId: kycApplicationId,
				},
			})
			if te := epifigrpc.RPCError(gcasResp, gcasErr); te != nil {
				errString := "error in sgMatrixApiGateway.GetCustomerApplicationStatus call"
				lg.Error(errString, zap.Error(te))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get customer application status, err: %v", te))
			}

			// fetch ckyc status
			kycStageStatus, kycStageErr := p.getKycStageStatusFromVendor(ctx, kycApplicationId, sgMatrixPb.Stage_STAGE_CKYC)
			if kycStageErr != nil {
				lg.Error("kyc stage status is not in success stage yet", zap.Error(kycStageErr))
				return nil, kycStageErr
			}
			if kycStageStatus != sgMatrixPb.StageStatus_STAGE_STATUS_SUCCESS && kycStageStatus != sgMatrixPb.StageStatus_STAGE_STATUS_SKIPPED {
				lg.Error("ckyc stage is not in success stage yet", zap.String("kyc_stage_status", kycStageStatus.String()))
				return nil, errors.Wrap(epifierrors.ErrTransient, "ckyc stage is not in success stage yet!")
			}

			// fetch ckyc data
			kycDataResp, kycDataErr = p.sgKycApiGateway.GetKYCData(ctx, &sKycPb.GetKYCDataRequest{
				Identifier: &sKycPb.KYCRequestIdentifier{
					Identifier: &sKycPb.KYCRequestIdentifier_ApplicationId{
						ApplicationId: kycApplicationId,
					},
				},
			})
			if te := epifigrpc.RPCError(kycDataResp, kycDataErr); te != nil {
				lg.Error("failed to get kyc data", zap.Error(te))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get kyc data, err: %v", te))
			}
		} else {
			// fetching loan applicant
			loanApplicant, loanApplicantErr := p.loanApplicantDao.GetByActorIdAndVendorAndLoanProgramAndProgramVersion(ctx, lr.GetActorId(), lr.GetVendor(), lr.GetLoanProgram(), enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1)
			if loanApplicantErr != nil {
				lg.Error("failed to fetch loan applicant", zap.Error(loanApplicantErr))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch loan applicant, err: %v", loanApplicantErr))
			}

			// fetch ckyc data
			kycDataResp, kycDataErr = p.sgKycApiGateway.GetKYCData(ctx, &sKycPb.GetKYCDataRequest{
				Identifier: &sKycPb.KYCRequestIdentifier{
					Identifier: &sKycPb.KYCRequestIdentifier_ApplicantId{
						ApplicantId: loanApplicant.GetVendorApplicantId(),
					},
				},
			})
			if te := epifigrpc.RPCError(kycDataResp, kycDataErr); te != nil {
				lg.Error("failed to get kyc data", zap.Error(te))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get kyc data, err: %v", te))
			}
		}

		updatedLr, updateLrErr := p.updateKycDataInLr(ctx, lr, kycData.GetIsKycAlreadyDonePreviously())
		if updateLrErr != nil {
			lg.Error("failed to update in Lr", zap.Error(updateLrErr))
			return nil, errors.Wrapf(epifierrors.ErrTransient, "failed to update in Lr, err: %v", updateLrErr)
		}
		lr = updatedLr

		// upload image to s3
		imageS3Path, err := p.writeCkycImageToS3(ctx, kycDataResp.GetKycData().GetUserImage().GetImageDataBase64(), lr.GetVendorRequestId())
		if err != nil {
			lg.Error("failed to upload to s3", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
		}

		if lse.GetDetails() == nil {
			lse.Details = &palPb.LoanStepExecutionDetails{}
		}
		if lse.GetDetails().GetCkycStepData() == nil {
			lse.Details.Details = &palPb.LoanStepExecutionDetails_CkycStepData{
				CkycStepData: &palPb.CkycStepData{},
			}
		}

		lse.GetDetails().GetCkycStepData().CkycImagePath = imageS3Path
		lse.GetDetails().GetCkycStepData().CkycId = kycDataResp.GetKycData().GetKycDocumentNumber()
		lse.GetDetails().GetCkycStepData().PAN = kycDataResp.GetKycData().GetPersonalData().GetPan()
		lse.GetDetails().GetCkycStepData().Name = kycDataResp.GetKycData().GetPersonalData().GetName()
		lse.GetDetails().GetCkycStepData().Dob = ConvertCommonToDate(kycDataResp.GetKycData().GetPersonalData().GetDob())
		lse.GetDetails().GetCkycStepData().CorrespondenceAddress = ConvertCommonToPostalAddress(kycDataResp.GetKycData().GetPersonalData().GetCorrespondenceAddress())
		lse.GetDetails().GetCkycStepData().PermanentAddress = ConvertCommonToPostalAddress(kycDataResp.GetKycData().GetPersonalData().GetPermanentAddress())
		lse.GetDetails().GetCkycStepData().KycAddress = ConvertCommonToPostalAddress(kycDataResp.GetKycData().GetPersonalData().GetPermanentAddress())

		if updateErr := p.loanStepExecutionDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS,
		}); updateErr != nil {
			lg.Error("failed to update lse", zap.Error(updateErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update lse, err: %v", updateErr))
		}

		// set review details screeen
		ckycData := lse.GetDetails().GetCkycStepData()

		deeplinkProvider := p.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplinkPal.GetDeeplinkProviderRequest{Vendor: lr.GetVendor(), LoanProgram: lr.GetLoanProgram()})
		getReviewScreenDeeplinkV2Request := &dlProvider.GetReviewScreenDeeplinkV2Request{
			LoanHeader:            deeplinkProvider.GetLoanHeader(),
			LoanReqId:             lr.GetId(),
			Name:                  ckycData.GetName(),
			Dob:                   strings.ToUpper(datetime.DateToString(ckycData.GetDob(), dobLayout, datetime.IST)),
			Pan:                   ckycData.GetPAN(),
			CorrespondenceAddress: ckycData.GetCorrespondenceAddress(),
			PermanentAddress:      ckycData.GetPermanentAddress(),
			OVDAddress:            ckycData.GetKycAddress(),
			CkycId:                ckycData.GetCkycId(),
		}
		nextAction, err := deeplinkProvider.GetKycReviewScreenDeeplinkV2(ctx, getReviewScreenDeeplinkV2Request)
		if err != nil {
			lg.Info("error in getting next action for getReviewScreenDeeplink", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, "error in getting next action for getReviewScreenDeeplink")
		}
		updateLrErr = p.updateNextActionInLoanRequest(ctx, res.GetLoanStep().GetRefId(), nextAction)
		if updateLrErr != nil {
			lg.Error("failed to update lr next action", zap.Error(updateLrErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update lr next action, err: %v", updateLrErr))
		}

		return res, nil
	})
	return actRes, actErr
}

// SgGetCkycUserVerificationStatus contains a user action status check and no permanent failures are there
// Todo(Anupam: Revisit after prod): move this user action signal via sync proxy
func (p *Processor) SgGetCkycUserVerificationStatus(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep: lse,
		}
		lg := activity.GetLogger(ctx)

		// If digilocker is enabled, then skip ckyc step
		if p.config.Flags().IsSgDigilockerEnabled() {
			palActivity.MarkLoanStepSuccess(lse)
			lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_SKIPPED
			res.LseFieldMasks = append(res.GetLseFieldMasks(), palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
				palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS)
			return res, nil
		}

		if lse.GetSubStatus() != palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_DETAILS_VERIFIED {
			lg.Error("waiting on user to verify kyc details")
			return nil, errors.Wrap(epifierrors.ErrTransient, "waiting on user to verify kyc details")
		}

		palActivity.MarkLoanStepSuccess(lse)
		if updateErr := p.loanStepExecutionDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
		}); updateErr != nil {
			lg.Error("failed to update lse", zap.Error(updateErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update lse, err: %v", updateErr))
		}
		return res, nil
	})
	return actRes, actErr
}

// it will write the image to s3 and return the s3 path, else return empty string
func (p *Processor) writeCkycImageToS3(ctx context.Context, ckycImage string, applicantVrId string) (string, error) {
	awsDestinationPath := fmt.Sprintf("stockguardian/ckyc_images/%v", applicantVrId)

	s3Err := p.s3Client.Write(ctx, awsDestinationPath, []byte(ckycImage), string(s3types.ObjectCannedACLBucketOwnerFullControl))
	if s3Err != nil {
		return "", errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in uploading image to s3, err: %v", s3Err))
	}

	return awsDestinationPath, nil
}

// it will write the image to s3 and return the s3 path, else return empty string
func (p *Processor) writeKycImageToS3(ctx context.Context, digilockerImage string, applicantVrId string) (string, error) {
	awsDestinationPath := fmt.Sprintf("stockguardian/kyc_images/%v", applicantVrId)

	s3Err := p.s3Client.Write(ctx, awsDestinationPath, []byte(digilockerImage), string(s3types.ObjectCannedACLBucketOwnerFullControl))
	if s3Err != nil {
		return "", errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in uploading image to s3, err: %v", s3Err))
	}

	return awsDestinationPath, nil
}

func (p *Processor) isKycAlreadyDone(status *rpc.Status) bool {
	if status.GetCode() == uint32(sgApplicationPb.InitiateKycResponse_Status_value[sgApplicationPb.InitiateKycResponse_KYC_ALREADY_COMPLETED.String()]) {
		return true
	}
	return false
}
