// nolint: staticcheck
package stock_guardian

import (
	"context"
	"fmt"
	"strings"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/epifitemporal"
	"github.com/epifi/gamma/api/preapprovedloan/enums"
	sKycPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/kyc"
	sgMatrixPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/matrix"
	types "github.com/epifi/gamma/api/typesv2"
	location2 "github.com/epifi/gamma/api/user/location"
	dlProvider "github.com/epifi/gamma/preapprovedloan/deeplink/provider"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	palActivity "github.com/epifi/gamma/preapprovedloan/activity"
	deeplinkPal "github.com/epifi/gamma/preapprovedloan/deeplink"
)

// nolint:gocritic
func (p *Processor) SgKycDocumentDownload(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep: lse,
		}
		lg := activity.GetLogger(ctx)

		// If digilocker is not enabled, then skip
		if !p.config.Flags().IsSgDigilockerEnabled() {
			palActivity.MarkLoanStepSuccess(lse)
			lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_SKIPPED
			res.LseFieldMasks = append(res.GetLseFieldMasks(), palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
				palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS)
			return res, nil
		}

		// fetch loan request
		lr, lrErr := p.loanRequestDao.GetById(ctx, lse.GetRefId())
		if lrErr != nil {
			lg.Error("error in lr get by id", zap.Error(lrErr))
			if errors.Is(lrErr, epifierrors.ErrRecordNotFound) {
				palActivity.MarkLoanStepFail(lse)
				return res, nil
			}
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in lr get by id, err: %v", lrErr))
		}
		kycDetails := lr.GetDetails().GetApplicationDetails().GetKycDetails()

		// If kyc already done, then skip this step
		if kycDetails.GetIsKycAlreadyDonePreviously() {
			_, err := p.updateKycDataInLr(ctx, lr, true)
			if err != nil {
				lg.Error("error in updating kyc data in lr", zap.Error(err))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in updating kyc data in lr, err: %v", err))
			}
			// mark LSE in progress
			lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS
			res.LseFieldMasks = append(res.GetLseFieldMasks(), palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS)
			return res, nil
		}

		kycApplicationId := kycDetails.GetVendorKycRequestId()
		gcasResp, gcasErr := p.sgMatrixApiGateway.GetCustomerApplicationStatus(ctx, &sgMatrixPb.GetCustomerApplicationStatusRequest{
			ApplicationIdentifier: &sgMatrixPb.GetCustomerApplicationStatusRequest_ApplicationId{
				ApplicationId: kycApplicationId,
			},
		})
		if te := epifigrpc.RPCError(gcasResp, gcasErr); te != nil {
			errString := "error in sgMatrixApiGateway.GetCustomerApplicationStatus call"
			lg.Error(errString, zap.Error(te))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get customer application status, err: %v", te))
		}

		// Todo(Anupam): Make this behind a abstraction to get the kyc stage status not digilocker or ckyc wise
		// fetch ckyc status
		ckycStageStatus, kycStageErr := p.getKycStageStatusFromVendor(ctx, kycApplicationId, sgMatrixPb.Stage_STAGE_CKYC)
		if kycStageErr != nil && !errors.Is(kycStageErr, epifierrors.ErrRecordNotFound) {
			lg.Error("kyc stage status is not in success stage yet", zap.Error(kycStageErr))
			return nil, kycStageErr
		}
		// fetch digilocker status
		digilockerKycStageStatus, kycStageErr := p.getKycStageStatusFromVendor(ctx, kycApplicationId, sgMatrixPb.Stage_STAGE_DIGILOCKER_KYC)
		if kycStageErr != nil && !errors.Is(kycStageErr, epifierrors.ErrRecordNotFound) {
			lg.Error("kyc stage status is not in success stage yet", zap.Error(kycStageErr))
			return nil, kycStageErr
		}

		deeplinkProvider := p.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplinkPal.GetDeeplinkProviderRequest{Vendor: lr.GetVendor(), LoanProgram: lr.GetLoanProgram()})

		// If any kyc is not done, then take user to do KYC through webview
		if ckycStageStatus != sgMatrixPb.StageStatus_STAGE_STATUS_SUCCESS && digilockerKycStageStatus != sgMatrixPb.StageStatus_STAGE_STATUS_SUCCESS {
			lg.Info("kyc stage is not in success stage yet", zap.String("ckyc_stage_status", ckycStageStatus.String()), zap.String("digilocker_stage_status", digilockerKycStageStatus.String()))
			// Take kyc webview poll deeplink
			kycIntroScreenDl, err := deeplinkPal.GetWebviewPollDeeplink(
				deeplinkProvider.GetLoanHeader(), p.getKycUrl(kycApplicationId), lse.GetRefId(),
				false,
				90000,
				[]string{"https://fi.money/d2VhbHRoLWFhZGhhYXItZS1zaWdu"},
			)
			if err != nil {
				lg.Error("error getting kyc intro screen deeplink", zap.Error(err))
				return nil, epifitemporal.NewTransientError(errors.Wrap(err, "error getting kyc intro screen deeplink"))
			}

			return &palActivityPb.PalActivityResponse{LoanStep: lse, NextAction: kycIntroScreenDl},
				epifitemporal.NewTransientError(errors.Errorf("kyc not completed yet, keep asking for kyc status"))
		}

		updatedLr, err := p.updateKycDataInLr(ctx, lr, kycDetails.GetIsKycAlreadyDonePreviously())
		if err != nil {
			lg.Error("error in updating kyc data in lr", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in updating kyc data in lr, err: %v", err))
		}
		lr = updatedLr
		res.IsSyncTaskDone = true
		return res, nil
	})
	return actRes, actErr
}

// nolint:gocritic
func (p *Processor) SgKycDataVerification(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep: lse,
		}
		lg := activity.GetLogger(ctx)

		// If digilocker is not enabled, then skip
		if !p.config.Flags().IsSgDigilockerEnabled() {
			palActivity.MarkLoanStepSuccess(lse)
			lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_SKIPPED
			res.LseFieldMasks = append(res.GetLseFieldMasks(), palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
				palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS)
			return res, nil
		}

		// fetch loan request
		lr, lrErr := p.loanRequestDao.GetById(ctx, lse.GetRefId())
		if lrErr != nil {
			lg.Error("error in lr get by id", zap.Error(lrErr))
			if errors.Is(lrErr, epifierrors.ErrRecordNotFound) {
				palActivity.MarkLoanStepFail(lse)
				return res, nil
			}
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in lr get by id, err: %v", lrErr))
		}

		deeplinkProvider := p.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplinkPal.GetDeeplinkProviderRequest{Vendor: lr.GetVendor(), LoanProgram: lr.GetLoanProgram()})
		if lse.GetSubStatus() == palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_KYC_DETAILS_VERIFIED {
			// update current pincode in matrix for positive address confirmation
			kycDetails := lr.GetDetails().GetApplicationDetails().GetKycDetails()
			if !kycDetails.GetIsKycAlreadyDonePreviously() {
				err := p.updateCurrentPincodeInMatrix(ctx, kycDetails.GetLocationToken(), kycDetails.GetVendorKycRequestId())
				if err != nil {
					lg.Info("error in updating the current pincode in matrix", zap.Error(err))
					return nil, errors.Wrapf(epifierrors.ErrTransient, "error in updating the current pincode in matrix, err: %v", err)
				}
			}

			palActivity.MarkLoanStepSuccess(lse)
			res.IsSyncTaskDone = true
			return res, nil
		}

		// set review details screeen
		kycData := lr.GetDetails().GetApplicationDetails().GetKycDetails()
		getReviewScreenDeeplinkV2Request := &dlProvider.GetReviewScreenDeeplinkV2Request{
			LoanHeader:            deeplinkProvider.GetLoanHeader(),
			LoanReqId:             lr.GetId(),
			Name:                  kycData.GetPersonalData().GetName(),
			Dob:                   strings.ToUpper(datetime.DateToString(ConvertCommonToDate(kycData.GetPersonalData().GetDob()), dobLayout, datetime.IST)),
			Pan:                   kycData.GetPersonalData().GetPan(),
			CorrespondenceAddress: ConvertCommonToPostalAddress(kycData.GetPersonalData().GetCorrespondenceAddress()),
			PermanentAddress:      ConvertCommonToPostalAddress(kycData.GetPersonalData().GetPermanentAddress()),
			OVDAddress:            ConvertCommonToPostalAddress(kycData.GetPersonalData().GetKycAddress()),
			CkycId:                kycData.GetKycDocumentNumber(),
			KycTYpe:               kycData.GetKycType(),
			KycDocumentNumber:     kycData.GetKycDocumentNumber(),
		}
		nextAction, err := deeplinkProvider.GetKycReviewScreenDeeplinkV2(ctx, getReviewScreenDeeplinkV2Request)
		if err != nil {
			lg.Info("error in getting next action for getReviewScreenDeeplink", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, "error in getting next action for getReviewScreenDeeplink")
		}
		res.NextAction = nextAction
		return res, errors.Wrap(epifierrors.ErrTransient, "kyc data is not being verified yet by user")
	})
	return actRes, actErr
}

func (p *Processor) updateKycDataInLr(ctx context.Context, lr *palPb.LoanRequest, isKycAlreadyDonePreviously bool) (*palPb.LoanRequest, error) {
	// fetch kyc data
	kycDataResp := &sKycPb.GetKYCDataResponse{}
	var kycDataErr error
	if isKycAlreadyDonePreviously {
		loanApplicant, loanApplicantErr := p.loanApplicantDao.GetByActorIdAndVendorAndLoanProgramAndProgramVersion(ctx, lr.GetActorId(), lr.GetVendor(), lr.GetLoanProgram(), enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1)
		if loanApplicantErr != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch loan applicant, err: %v", loanApplicantErr))
		}

		// fetch kyc data
		kycDataResp, kycDataErr = p.sgKycApiGateway.GetKYCData(ctx, &sKycPb.GetKYCDataRequest{
			Identifier: &sKycPb.KYCRequestIdentifier{
				Identifier: &sKycPb.KYCRequestIdentifier_ApplicantId{
					ApplicantId: loanApplicant.GetVendorApplicantId(),
				},
			},
		})
		if te := epifigrpc.RPCError(kycDataResp, kycDataErr); te != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get kyc data, err: %v", te))
		}
	} else {
		kycDataResp, kycDataErr = p.sgKycApiGateway.GetKYCData(ctx, &sKycPb.GetKYCDataRequest{
			Identifier: &sKycPb.KYCRequestIdentifier{
				Identifier: &sKycPb.KYCRequestIdentifier_ApplicationId{
					ApplicationId: lr.GetDetails().GetApplicationDetails().GetKycDetails().GetVendorKycRequestId(),
				},
			},
		})
	}
	if te := epifigrpc.RPCError(kycDataResp, kycDataErr); te != nil {
		return nil, errors.Wrapf(te, "failed to get kyc data")
	}

	if lr.GetDetails().GetApplicationDetails().GetKycDetails().GetPersonalData() == nil {
		lr.GetDetails().GetApplicationDetails().GetKycDetails().PersonalData = &palPb.KycDetails_PersonalDetails{}
	}

	// upload image to s3
	imageS3Path, err := p.writeKycImageToS3(ctx, kycDataResp.GetKycData().GetUserImage().GetImageDataBase64(), lr.GetVendorRequestId())
	if err != nil {
		return nil, errors.Wrapf(err, "failed to write ckyc image to s3")
	}

	lseKycType := convertSgKycDataTypeToLseKycType(kycDataResp.GetDataType())
	if lseKycType == palPb.KycType_KYC_TYPE_UNSPECIFIED {
		return nil, errors.New("kyc data type is not supported")
	}

	lr.GetDetails().GetApplicationDetails().GetKycDetails().KycType = lseKycType
	lr.GetDetails().GetApplicationDetails().GetKycDetails().UserImagePath = imageS3Path
	lr.GetDetails().GetApplicationDetails().GetKycDetails().KycDocumentNumber = kycDataResp.GetKycData().GetKycDocumentNumber()

	lr.GetDetails().GetApplicationDetails().GetKycDetails().GetPersonalData().Pan = kycDataResp.GetKycData().GetPersonalData().GetPan()
	lr.GetDetails().GetApplicationDetails().GetKycDetails().GetPersonalData().Name = kycDataResp.GetKycData().GetPersonalData().GetName()
	lr.GetDetails().GetApplicationDetails().GetKycDetails().GetPersonalData().Dob = kycDataResp.GetKycData().GetPersonalData().GetDob()
	lr.GetDetails().GetApplicationDetails().GetKycDetails().GetPersonalData().CorrespondenceAddress = kycDataResp.GetKycData().GetPersonalData().GetCorrespondenceAddress()
	lr.GetDetails().GetApplicationDetails().GetKycDetails().GetPersonalData().PermanentAddress = kycDataResp.GetKycData().GetPersonalData().GetPermanentAddress()
	lr.GetDetails().GetApplicationDetails().GetKycDetails().GetPersonalData().KycAddress = kycDataResp.GetKycData().GetPersonalData().GetPermanentAddress()

	if updateErr := p.loanRequestDao.Update(ctx, lr, []palPb.LoanRequestFieldMask{
		palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_DETAILS,
	}); updateErr != nil {
		return nil, errors.Wrap(updateErr, "failed to update lr")
	}

	return lr, nil
}

func convertSgKycDataTypeToLseKycType(dataType sKycPb.KycVendorDataType) palPb.KycType {
	switch dataType {
	case sKycPb.KycVendorDataType_KYC_VENDOR_DATA_TYPE_CKYC_DOWNLOAD:
		return palPb.KycType_KYC_TYPE_CKYC
	case sKycPb.KycVendorDataType_KYC_VENDOR_DATA_TYPE_DIGILOCKER_AADHAR_XML:
		return palPb.KycType_KYC_TYPE_DIGILOCKER
	default:
		return palPb.KycType_KYC_TYPE_UNSPECIFIED
	}
}

func (p *Processor) getKycUrl(kycApplicationId string) string {
	return fmt.Sprintf(p.config.SgKycUrl()+"%s", kycApplicationId)
}

func (p *Processor) updateCurrentPincodeInMatrix(ctx context.Context, locationToken string, customerApplicationId string) error {
	addressResp, err := p.userLocationClient.FetchAndStoreAddressForIdentifier(ctx, &location2.FetchAndStoreAddressForIdentifierRequest{
		IdentifierType:  types.IdentifierType_IDENTIFIER_TYPE_LOCATION,
		IdentifierValue: locationToken,
	})
	if rpcErr := epifigrpc.RPCError(addressResp, err); rpcErr != nil && !addressResp.GetStatus().IsRecordNotFound() {
		return errors.Wrap(rpcErr, "error in getting the address from the location token")
	}

	pincode := addressResp.GetAddress().GetPostalCode()
	if addressResp.GetStatus().IsRecordNotFound() {
		pincode = "00"
	}

	resp, err := p.sgMatrixApiGateway.UpdateCustomerApplicationDetails(ctx, &sgMatrixPb.UpdateCustomerApplicationDetailsRequest{
		ApplicationId: customerApplicationId,
		ApplicationData: &sgMatrixPb.UpdateCustomerApplicationDetailsRequest_UserCurrentPincode{
			UserCurrentPincode: pincode,
		},
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		return errors.Wrap(err, "error in updating pincode in customer application")
	}
	return nil
}
