// Code generated by MockGen. DO NOT EDIT.
// Source: IOrchestrator.go

// Package preeligibility is a generated GoMock package.
package preeligibility

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIOrchestrator is a mock of IOrchestrator interface.
type MockIOrchestrator struct {
	ctrl     *gomock.Controller
	recorder *MockIOrchestratorMockRecorder
}

// MockIOrchestratorMockRecorder is the mock recorder for MockIOrchestrator.
type MockIOrchestratorMockRecorder struct {
	mock *MockIOrchestrator
}

// NewMockIOrchestrator creates a new mock instance.
func NewMockIOrchestrator(ctrl *gomock.Controller) *MockIOrchestrator {
	mock := &MockIOrchestrator{ctrl: ctrl}
	mock.recorder = &MockIOrchestratorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIOrchestrator) EXPECT() *MockIOrchestratorMockRecorder {
	return m.recorder
}

// Perform mocks base method.
func (m *MockIOrchestrator) Perform(ctx context.Context, req *OrchestratorReq) (*OrchestratorResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Perform", ctx, req)
	ret0, _ := ret[0].(*OrchestratorResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Perform indicates an expected call of Perform.
func (mr *MockIOrchestratorMockRecorder) Perform(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Perform", reflect.TypeOf((*MockIOrchestrator)(nil).Perform), ctx, req)
}

// Start mocks base method.
func (m *MockIOrchestrator) Start(ctx context.Context, actorId, webUrlQuery string) (*OrchestratorResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Start", ctx, actorId, webUrlQuery)
	ret0, _ := ret[0].(*OrchestratorResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Start indicates an expected call of Start.
func (mr *MockIOrchestratorMockRecorder) Start(ctx, actorId, webUrlQuery interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Start", reflect.TypeOf((*MockIOrchestrator)(nil).Start), ctx, actorId, webUrlQuery)
}
