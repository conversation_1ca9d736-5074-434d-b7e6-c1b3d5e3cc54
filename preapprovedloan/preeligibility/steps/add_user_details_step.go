package steps

import (
	"context"
	"fmt"
	"net/url"
	"time"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/frontend/deeplink"
	leadsPb "github.com/epifi/gamma/api/leads"
	"github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/user"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/webfe/deeplink_screen_option"
	"github.com/epifi/gamma/api/webfe/deeplink_screen_option/loans"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/preapprovedloan/dao"
	"github.com/epifi/gamma/preapprovedloan/helper"
	"github.com/epifi/gamma/preapprovedloan/userdata"
)

// UserDetailsSubStepType represents the type of sub-step in the user details collection process
type userDetailsSubStepType int

const (
	// UserDetailsSubStepBasicDetails represents the basic user details collection sub-step
	UserDetailsSubStepBasicDetails userDetailsSubStepType = iota
	// UserDetailsSubStepEmploymentDetails represents the employment details collection sub-step
	UserDetailsSubStepEmploymentDetails
)

type AddUserDetailsStep struct {
	userClient       user.UsersClient
	rpcHelper        *helper.RpcHelper
	userDataProvider userdata.IUserDataProvider
	leadsClient      leadsPb.UserLeadSvcClient
	loanRequestsDao  dao.LoanRequestsDao
}

var subSteps = []userDetailsSubStepType{
	UserDetailsSubStepBasicDetails,
	UserDetailsSubStepEmploymentDetails,
}

func NewAddUserDetailsStep(
	userClient userPb.UsersClient,
	rpcHelper *helper.RpcHelper,
	userDataProvider userdata.IUserDataProvider,
	leadsClient leadsPb.UserLeadSvcClient,
	loanRequestsDao dao.LoanRequestsDao,
) *AddUserDetailsStep {
	return &AddUserDetailsStep{
		userClient:       userClient,
		rpcHelper:        rpcHelper,
		userDataProvider: userDataProvider,
		leadsClient:      leadsClient,
		loanRequestsDao:  loanRequestsDao,
	}
}

// Execute implements the Step interface
func (s *AddUserDetailsStep) Execute(ctx context.Context, req *StepExecutionReq) (*StepExecutionResp, error) {
	var stepExecutionResp = &StepExecutionResp{
		Lse: req.GetLse(),
	}

	// Handle specific request types if details are provided
	if req.GetBasicUserDetails() != nil {
		err := s.collectUserBasicDetails(ctx, req)
		if err != nil {
			return nil, err
		}
	}
	if req.GetEmploymentDetails() != nil {
		err := s.collectEmploymentDetails(ctx, req)
		if err != nil {
			return nil, err
		}
	}

	// Execute each sub-step in sequence
	// Check in order of details collection
	// if the details exist, continue
	// if details don't exist, send the next action to collect corresponding details
	userData, err := s.userDataProvider.GetDefaultUserData(ctx, &userdata.GetDefaultUserDataRequest{ActorId: req.GetActorId()})
	if err != nil {
		return nil, errors.Wrap(err, "failed to get user details")
	}

	lrId := req.GetLse().GetRefId()

	// Get loan request details to extract webUrlQuery
	var webUrlQuery string
	loanRequest, err := s.loanRequestsDao.GetById(ctx, lrId)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get loan request details")
	} else {
		webUrlQuery = loanRequest.GetDetails().GetWebUrlQuery()
	}

	for _, subStep := range subSteps {
		switch subStep {
		case UserDetailsSubStepBasicDetails:
			if !s.checkBasicDetails(userData) {
				stepExecutionResp.NextAction = &deeplink.Deeplink{
					Screen: deeplink.Screen_WEB_LOANS_ELIGIBILITY_PAN_FORM_SCREEN,
					ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(
						&loans.WebLoansEligibilityPanFormScreenOptions{
							Content: &deeplink_screen_option.CommonOptions{
								Text:                      "Add your PAN details",
								SubText:                   "This won't affect your credit score",
								ImageUrl:                  "https://epifi-icons.pointz.in/webfe/loans/pan.webp",
								ShowEndToEndEncryptedText: true,
								CurrentStep:               2,
								Progress:                  50,
								HasHeader:                 true,
								ButtonText:                "Continue",
							},
							RequestId: req.GetReqId(),
						},
					),
				}
				stepExecutionResp.GetLse().Status = preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS
				stepExecutionResp.LseUpdateMasks = append(stepExecutionResp.GetLseUpdateMasks(), preapprovedloan.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS)
				return stepExecutionResp, nil
			}
			// Run lead creation in goroutine
			goroutine.Run(ctx, 30*time.Second, func(ctx context.Context) {
				err := s.createLeadAsync(ctx, userData, req.GetLse().GetOrchId(), webUrlQuery)
				if err != nil {
					logger.Error(ctx, errors.Wrap(err, "failed to create lead").Error())
				}
			})

		case UserDetailsSubStepEmploymentDetails:
			if !s.checkEmploymentDetails(userData) ||
				!s.checkLoanCommunicationAddress(userData) {
				stepExecutionResp.NextAction = &deeplink.Deeplink{
					Screen: deeplink.Screen_WEB_LOANS_ELIGIBILITY_EMPLOYMENT_FORM_SCREEN,
					ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(
						&loans.WebLoansEligibilityEmploymentFormScreenOptions{
							Content: &deeplink_screen_option.CommonOptions{
								Text:                      "Finishing things up...",
								SubText:                   "This will help us accurately check your borrowing potential!",
								ImageUrl:                  "https://epifi-icons.pointz.in/webfe/loans/paper-plane.webp",
								ShowEndToEndEncryptedText: true,
								CurrentStep:               2,
								Progress:                  100,
								HasHeader:                 true,
								ButtonText:                "Get my loan offer",
							},
							RequestId: req.GetReqId(),
							EmploymentTypeOptions: []*loans.WebLoansEligibilityEmploymentFormScreenOptions_EmploymentTypeOptions{
								{Type: typesv2.EmploymentType_EMPLOYMENT_TYPE_SALARIED, Title: "Salaried"},
								{Type: typesv2.EmploymentType_EMPLOYMENT_TYPE_BUSINESS_OWNER, Title: "Business Owner / Entrepreneur"},
								{Type: typesv2.EmploymentType_EMPLOYMENT_TYPE_OTHERS, Title: "Other"},
							},
						},
					),
				}
				stepExecutionResp.GetLse().Status = preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS
				stepExecutionResp.LseUpdateMasks = append(stepExecutionResp.GetLseUpdateMasks(), preapprovedloan.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS)
				return stepExecutionResp, nil
			}
		default:
			return nil, errors.Errorf("unknown AddUserDetails sub-step type: %v", subStep)
		}
	}

	// All sub-steps completed successfully, set the status to success
	stepExecutionResp.GetLse().Status = preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
	stepExecutionResp.LseUpdateMasks = append(stepExecutionResp.GetLseUpdateMasks(), preapprovedloan.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS)
	return stepExecutionResp, nil
}

// checkBasicDetails verifies if user has provided basic details are present and valid
func (s *AddUserDetailsStep) checkBasicDetails(userData *userdata.GetDefaultUserDataResponse) bool {
	return userData.GetPan() != "" &&
		userData.GetBestName() != nil &&
		userData.GetGivenDateOfBirth() != nil
}

// checkEmploymentDetails verifies if employment details are present and valid
func (s *AddUserDetailsStep) checkEmploymentDetails(userData *userdata.GetDefaultUserDataResponse) bool {
	if empDetails := userData.GetEmploymentDetails(); empDetails != nil {
		return empDetails.GetEmploymentType() != typesv2.EmploymentType_EMPLOYMENT_TYPE_UNSPECIFIED &&
			empDetails.GetMonthlyIncome() != nil
	}
	return false
}

// checkLoanCommunicationAddress verifies if loan communication address exists with pin code
func (s *AddUserDetailsStep) checkLoanCommunicationAddress(userData *userdata.GetDefaultUserDataResponse) bool {
	if addr := userData.GetAddress(); addr != nil {
		return addr.GetPostalCode() != ""
	}
	return false
}

// extractUrlParameter extracts the given query parameter value from the URL
func extractUrlParameter(urlStr, paramName string) (string, error) {
	queryParams, err := url.ParseQuery(urlStr)
	if err != nil {
		return "", err
	}
	return queryParams.Get(paramName), nil
}

// createLeadAsync creates a lead asynchronously for the given user data
func (s *AddUserDetailsStep) createLeadAsync(ctx context.Context, userData *userdata.GetDefaultUserDataResponse, orchId string, webUrlQuery string) error {
	if webUrlQuery == "" {
		logger.Info(ctx, "skipping lead creation because web url is empty")
		return nil
	}
	source, err := extractUrlParameter(webUrlQuery, "utm_source")
	if err != nil {
		return fmt.Errorf("error extracting utm_source parameter: %w", err)
	}
	if source != "dsa_individual" && source != "dsa_large" {
		logger.Info(ctx, "skipping lead creation because source is not DSA")
		return nil
	}
	campaign, err := extractUrlParameter(webUrlQuery, "utm_campaign")
	if err != nil {
		return fmt.Errorf("error extracting utm_campaign parameter: %w", err)
	}
	if campaign == "" {
		logger.Info(ctx, "skipping lead creation because campaign is empty")
		return nil
	}
	utmContent, err := extractUrlParameter(webUrlQuery, "utm_content")
	if err != nil {
		return fmt.Errorf("error extracting utm_content parameter: %w", err)
	}
	// Create lead request
	createLeadReq := &leadsPb.CreateLeadRequest{
		ClientRequestId: orchId,
		ProductType:     leadsPb.ProductType_PRODUCT_TYPE_FI_PERSONAL_LOAN,
		ClientId:        campaign,
		Pan:             userData.GetPan(),
		Email:           userData.GetEmail(),
		PhoneNumber:     userData.GetMobileNumber(),
		PersonalDetails: &leadsPb.PersonalDetails{
			Name: userData.GetBestName(),
			Dob:  userData.GetGivenDateOfBirth(),
		},
		AdditionalDetails: &leadsPb.AdditionalDetails{
			UtmParameters: &leadsPb.UtmParameters{
				UtmContent: utmContent,
			},
		},
	}
	// Call CreateLead
	createLeadRes, err := s.leadsClient.CreateLead(ctx, createLeadReq)
	if rpcErr := epifigrpc.RPCError(createLeadRes, err); rpcErr != nil {
		return errors.Wrap(rpcErr, "error in creating lead asynchronously")
	}
	return nil
}
