// nolint:goimports
package userdata

import (
	"context"
	userpb "github.com/epifi/gamma/api/user"

	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/gamma/api/typesv2"
)

//go:generate mockgen -source=IUserDataProvider.go -destination=mocks/mock_user_data_provider.go

// IUserDataProvider is an interface for fetching user data.This interface is created because user data can be stored in multiple
// places.
type IUserDataProvider interface {
	GetDefaultUserData(ctx context.Context, req *GetDefaultUserDataRequest) (*GetDefaultUserDataResponse, error)
}

type GetDefaultUserDataRequest struct {
	ActorId string
}

type GetDefaultUserDataResponse struct {
	GivenName         *common.Name
	GivenDateOfBirth  *date.Date
	PAN               string
	PanName           *common.Name
	GivenGender       typesv2.Gender
	MobileNumber      *common.PhoneNumber
	Email             string
	MaritalStatus     typesv2.MaritalStatus
	EmploymentDetails *EmploymentDetails
	Address           *typesv2.PostalAddress
	ResidenceDetails  *userpb.DataVerificationDetail_ResidenceDetails
}

type EmploymentDetails struct {
	EmploymentType   typesv2.EmploymentType
	OrganizationName string
	MonthlyIncome    *money.Money
	WorkEmail        string
	WorkAddress      *typesv2.PostalAddress
	AnnualRevenue    *money.Money
	GSTIN            string
}

func (e *EmploymentDetails) GetEmploymentType() typesv2.EmploymentType {
	if e != nil {
		return e.EmploymentType
	}
	return typesv2.EmploymentType_EMPLOYMENT_TYPE_UNSPECIFIED
}

func (e *EmploymentDetails) GetOrganizationName() string {
	if e != nil {
		return e.OrganizationName
	}
	return ""
}

func (e *EmploymentDetails) GetMonthlyIncome() *money.Money {
	if e != nil {
		return e.MonthlyIncome
	}
	return nil
}

func (e *EmploymentDetails) GetWorkEmail() string {
	if e != nil {
		return e.WorkEmail
	}
	return ""
}

func (e *EmploymentDetails) GetWorkAddress() *typesv2.PostalAddress {
	if e != nil {
		return e.WorkAddress
	}
	return nil
}

func (e *EmploymentDetails) GetAnnualRevenue() *money.Money {
	if e != nil {
		return e.AnnualRevenue
	}
	return nil
}

func (e *EmploymentDetails) GetGSTIN() string {
	if e != nil {
		return e.GSTIN
	}
	return ""
}

func (x *GetDefaultUserDataResponse) GetMobileNumber() *common.PhoneNumber {
	if x != nil {
		return x.MobileNumber
	}
	return nil
}

func (x *GetDefaultUserDataResponse) GetGivenDateOfBirth() *date.Date {
	if x != nil {
		return x.GivenDateOfBirth
	}
	return nil
}

func (x *GetDefaultUserDataResponse) GetGivenName() *common.Name {
	if x != nil {
		return x.GivenName
	}
	return nil
}

func (x *GetDefaultUserDataResponse) GetPan() string {
	if x != nil {
		return x.PAN
	}
	return ""
}

func (x *GetDefaultUserDataResponse) GetGivenGender() typesv2.Gender {
	if x != nil {
		return x.GivenGender
	}
	return typesv2.Gender_GENDER_UNSPECIFIED
}

func (x *GetDefaultUserDataResponse) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *GetDefaultUserDataResponse) GetEmploymentDetails() *EmploymentDetails {
	if x != nil {
		return x.EmploymentDetails
	}
	return nil
}

func (x *GetDefaultUserDataResponse) GetAddress() *typesv2.PostalAddress {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *GetDefaultUserDataResponse) GetResidenceDetails() *userpb.DataVerificationDetail_ResidenceDetails {
	if x != nil {
		return x.ResidenceDetails
	}
	return nil
}

func (x *GetDefaultUserDataResponse) GetMaritalStatus() typesv2.MaritalStatus {
	if x != nil {
		return x.MaritalStatus
	}
	return typesv2.MaritalStatus_UNSPECIFIED
}

func (x *GetDefaultUserDataResponse) GetPanName() *common.Name {
	if x != nil {
		return x.PanName
	}
	return nil
}

func (x *GetDefaultUserDataResponse) GetBestName() *common.Name {
	if x.GetPanName() != nil {
		return x.GetPanName()
	}
	if x.GetGivenName() != nil {
		return x.GetGivenName()
	}
	return nil
}
