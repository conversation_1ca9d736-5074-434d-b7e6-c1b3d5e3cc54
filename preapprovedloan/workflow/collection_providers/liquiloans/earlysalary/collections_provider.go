package earlysalary

import (
	"github.com/pkg/errors"

	"github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	preApprovedLoanNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"

	"go.temporal.io/sdk/workflow"
)

type LlEsCollectionPaymentExecutor struct {
	*stages.CollectionExecutorsPreProcessStage
	*stages.CollectionExecutorsPostProcessStage
}

func NewLlEsCollectionPaymentExecutor() *LlEsCollectionPaymentExecutor {
	return &LlEsCollectionPaymentExecutor{}
}

var _ stages.ICollectionPaymentExecutor = &LlEsCollectionPaymentExecutor{}

func (p *LlEsCollectionPaymentExecutor) PerformPayment(ctx workflow.Context, req *stages.PerformPaymentRequest) (*stages.PerformPaymentResponse, error) {
	res := &stages.PerformPaymentResponse{}
	lse := req.LoanStep
	// Executing SI activity to perform the execution of the si amount from the user
	actRes := &palActivityPb.ExecuteSiResponse{}
	executeSiErr := activityPkg.Execute(ctx, preApprovedLoanNs.ExecuteSi, actRes, &palActivityPb.ExecuteSiRequest{
		RequestHeader: req.RequestHeader,
		AccountId:     req.AccountId,
		SiAmount:      req.Amount,
		LoanStep:      req.LoanStep,
	})
	if executeSiErr != nil {
		lse.Status = preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED
		res.LoanStep = lse
		return res, errors.Wrap(executeSiErr, "si execution activity failed")
	}

	res.LoanStep = actRes.GetLoanStep()
	return res, nil
}

type LlEsCollectionReconcileExecutor struct {
	*stages.CollectionExecutorsPreProcessStage
	*stages.CollectionExecutorsPostProcessStage
}

func NewLlEsCollectionReconcileExecutor() *LlEsCollectionReconcileExecutor {
	return &LlEsCollectionReconcileExecutor{}
}

var _ stages.ICollectionReconcileExecutor = &LlEsCollectionReconcileExecutor{}

func (p *LlEsCollectionReconcileExecutor) PerformReconcile(ctx workflow.Context, req *stages.PerformReconcileRequest) (*stages.PerformReconcileResponse, error) {
	lse := req.LoanStep
	res := &stages.PerformReconcileResponse{}

	actRes := &palActivityPb.CreateLoanActivityResponse{}
	createLaErr := activityPkg.Execute(ctx, preApprovedLoanNs.CreateLoanActivity, actRes, &palActivityPb.CreateLoanActivityRequest{
		LoanStep:                 lse,
		LoanActivityType:         req.LoanActivityType,
		PaymentLoanStepExecution: req.PaymentLoanStep,
	})
	if createLaErr != nil {
		lse.Status = preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED
		res.LoanStep = lse
		return res, errors.Wrap(createLaErr, "loan activity creation activity failed")
	}

	// fetching LIP LMS data and refreshing for getting previous outstanding data
	lmsRefreshAndSaveApiReconcileResp := &palActivityPb.LmsRefreshAndSaveApiReconcileResponse{}
	lmsRefreshAndSaveApiReconcileErr := activityPkg.Execute(ctx, preApprovedLoanNs.LmsRefreshAndSaveApiReconcile, lmsRefreshAndSaveApiReconcileResp, &palActivityPb.LmsRefreshAndSaveApiReconcileRequest{
		LoanStep:         lse,
		LoanActivityType: req.LoanActivityType,
		LoanHeader:       req.LoanHeader,
		LoanAccountId:    req.LoanAccountId,
		PaymentLoanStep:  req.PaymentLoanStep,
	})
	if lmsRefreshAndSaveApiReconcileErr != nil {
		lse.Status = preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED
		res.LoanStep = lse
		return res, errors.Wrap(lmsRefreshAndSaveApiReconcileErr, "fetching LMS LIP data and refreshing activity failed")
	}

	// refreshing LMS data and syncing vendor with the payment
	refreshLmsFetchAndEvaluationReconcileResp := &palActivityPb.RefreshLmsFetchAndEvaluationReconcileResponse{}
	refreshLmsFetchAndEvaluationReconcileErr := activityPkg.Execute(ctx, preApprovedLoanNs.RefreshLmsFetchAndEvaluationReconcile, refreshLmsFetchAndEvaluationReconcileResp, &palActivityPb.RefreshLmsFetchAndEvaluationReconcileRequest{
		LoanStep:                  lse,
		LoanActivityType:          req.LoanActivityType,
		LoanHeader:                req.LoanHeader,
		PaymentLoanStepExecution:  req.PaymentLoanStep,
		LoanAccountId:             req.LoanAccountId,
		PreviousOutstandingAmount: lmsRefreshAndSaveApiReconcileResp.GetOutstandingAmount(),
	})
	if refreshLmsFetchAndEvaluationReconcileErr != nil {
		lse.Status = preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED
		res.LoanStep = lse
		return res, errors.Wrap(refreshLmsFetchAndEvaluationReconcileErr, "refresh LMS and calling save api activity failed")
	}

	res.LoanStep = refreshLmsFetchAndEvaluationReconcileResp.GetLoanStep()
	return res, nil
}
