package impl

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"fmt"

	"go.temporal.io/sdk/log"
	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	"github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	workflow2 "github.com/epifi/gamma/api/preapprovedloan/workflow"
	palTypesPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	deeplink2 "github.com/epifi/gamma/preapprovedloan/deeplink"
	helper2 "github.com/epifi/gamma/preapprovedloan/deeplink/provider/helper"
	"github.com/epifi/gamma/preapprovedloan/helper"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers/fiftyfin/lamf/syncproxy"
	"github.com/epifi/gamma/preapprovedloan/workflow/v2/processor"
)

type MutualFundNftPostProcessor struct {
}

var _ processor.PostProcessor = &MutualFundNftPostProcessor{}

func NewMutualFundNftPostProcessor() *MutualFundNftPostProcessor {
	return &MutualFundNftPostProcessor{}
}

func (n *MutualFundNftPostProcessor) PerformCompletionTasks(ctx workflow.Context, req *processor.PerformCompletionTasksRequest) error {
	lg := workflow.GetLogger(ctx)

	lr, err := n.updateLoanRequestStatus(ctx, req)
	if err != nil {
		return fmt.Errorf("failed to update loan request status for nft post processor: %w", err)
	}

	n.sendSyncSignalToCallerWorkflow(ctx, lg, lr)
	n.updateLrNextAction(ctx, lg, lr)

	return nil
}

// This method will update the loan request only if existing status of loan request is non-terminal
func (n *MutualFundNftPostProcessor) updateLoanRequestStatus(ctx workflow.Context, req *processor.PerformCompletionTasksRequest) (*palPb.LoanRequest, error) {
	actReq := &palActivityPb.GetLoanRequestActivityRequest{
		OrchId: req.GetClientRefId(),
	}
	actRes := &palActivityPb.GetLoanRequestActivityResponse{}
	actErr := activityPkg.Execute(ctx, palNs.GetLoanRequest, actRes, actReq)
	if actErr != nil {
		return nil, fmt.Errorf("failed to get loan request: %w", actErr)
	}
	lr := actRes.GetLoanRequest()

	// If existing loan request status is not in terminal status then update it.
	if !helper.IsLrTerminalStatus(lr.GetStatus()) {
		updateLrActReq := &palActivityPb.UpdateLoanRequestActivityV2Request{
			LoanRequestStatus: req.GetLoanRequestStatus(),
			OrchId:            lr.GetOrchId(),
		}
		updateLrActRes := &palActivityPb.UpdateLoanRequestActivityV2Response{}
		updateLrActErr := activityPkg.Execute(ctx, palNs.UpdateLoanRequestV2, updateLrActRes, updateLrActReq)
		if updateLrActErr != nil {
			return nil, fmt.Errorf("failed to update loan request status: %w", updateLrActErr)
		}
		lr.Status = req.GetLoanRequestStatus()
	}

	return lr, nil
}

func (n *MutualFundNftPostProcessor) sendSyncSignalToCallerWorkflow(ctx workflow.Context, lg log.Logger, lr *palPb.LoanRequest) {
	callerWfId := lr.GetDetails().GetNftDetails().GetFiftyfin().GetCallerWorkflowId()
	if callerWfId != "" {
		// send sync proxy signal
		lg.Info("sending proxy request for nft completion")
		err := syncproxy.SendRequest(ctx, callerWfId, &workflow2.NftProcessCompletionProxyRequest{
			LoanRequestStatus: lr.GetStatus(),
		})
		if err != nil {
			lg.Error("failed to send proxy request for nft completion", zap.Error(err))
			return
		}

		// receive sync proxy response
		lg.Info("receiving proxy response for nft completion")
		_, resErr := syncproxy.ReceiveResponse(ctx)
		if resErr != nil {
			lg.Error("failed to receive proxy response for nft completion", zap.Error(err))
			return
		}
	}
}

func (n *MutualFundNftPostProcessor) updateLrNextAction(ctx workflow.Context, lg log.Logger, lr *palPb.LoanRequest) {
	var lrNextAction *deeplink.Deeplink
	switch lr.GetStatus() {
	case palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CANCELLED, palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED:
		var err error
		lrNextAction, err = getNftFailureScreenDeeplink(lr.GetVendor(), lr.GetLoanProgram(), lr.GetRedirectLink())
		if err != nil {
			lg.Error("failed to get nft failure screen", zap.Error(err))
			lrNextAction = lr.GetRedirectLink()
		}
	default:
		lrNextAction = lr.GetRedirectLink()
	}

	updateActReq := &palActivityPb.UpdateLRNextActionRequest{
		LoanRequestId: lr.GetId(),
		NextAction:    lrNextAction,
	}
	updateActRes := &palActivityPb.PalActivityResponse{}
	localActVer := workflow.GetVersion(ctx, "move-lightweight-activities-to-local", workflow.DefaultVersion, 1)
	if localActVer != 1 {
		updateErr := activityPkg.Execute(ctx, palNs.UpdateLRNextAction, updateActRes, updateActReq)
		if updateErr != nil {
			lg.Error("UpdateLoanStepExecution activity failed", zap.Error(updateErr))
		}
	} else {
		updateErr := activityPkg.ExecuteLocally(ctx, palNs.UpdateLRNextAction, updateActRes, updateActReq)
		if updateErr != nil {
			lg.Error("UpdateLoanStepExecution activity failed", zap.Error(updateErr))
		}
	}
}

func getNftFailureScreenDeeplink(vendor palPb.Vendor, loanProgram palPb.LoanProgram, redirectDeeplink *deeplink.Deeplink) (*deeplink.Deeplink, error) {
	feLoanHeader := deeplink2.GetFeLoanHeaderByBeLoanHeader(&palPb.LoanHeader{
		LoanProgram: loanProgram,
		Vendor:      vendor,
	})
	dl, err := deeplinkv3.GetDeeplinkV3(deeplink.Screen_LOANS_FAILURE_SCREEN, &palTypesPb.LoansFailureScreen{
		LoanHeader: feLoanHeader,
		Cta: &deeplink.Button{
			Text: helper2.GetText("Back", "#FFFFFF", "#00B899", commontypes.FontStyle_BUTTON_M),
			Cta: &deeplink.Cta{
				Deeplink:     redirectDeeplink,
				Status:       deeplink.Cta_CTA_STATUS_ENABLED,
				Type:         deeplink.Cta_DONE,
				DisplayTheme: deeplink.Cta_PRIMARY,
				Text:         "Back",
			},
			Padding: helper2.GetButtonPadding(12, 24, 12, 24),
			Margin:  helper2.GetButtonMargin(16, 24, 24, 24),
		},
		Components: []*palTypesPb.LoansFailureScreenComponent{
			{
				Component: &palTypesPb.LoansFailureScreenComponent_TitleWithImage{
					TitleWithImage: &palTypesPb.VisualElementTitleSubtitleComponent{
						Component: &widget.VisualElementTitleSubtitleElement{
							VisualElement:   commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/preapprovedloan/warning_icon_red.png").WithProperties(&commontypes.VisualElementProperties{Width: 72, Height: 72}),
							TitleText:       commontypes.GetPlainStringText("Linking unsuccessful").WithFontStyle(commontypes.FontStyle_HEADLINE_L).WithFontColor("#000000"),
							SubtitleText:    commontypes.GetPlainStringText(" We faced an issue while linking some of your Mutual Funds. Please try again.").WithFontStyle(commontypes.FontStyle_BODY_3_PARA).WithFontColor("#929599"),
							BackgroundColor: "#FFFFFF",
						},
						TopMargin: 72,
					},
				},
			},
		},
	})
	if err != nil {
		return nil, fmt.Errorf("error while generating nft failure screen : %w", err)
	}

	return dl, nil
}
