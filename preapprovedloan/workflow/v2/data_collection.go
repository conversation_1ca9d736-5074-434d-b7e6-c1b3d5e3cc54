// nolint: funlen,dupl
package v2

import (
	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"

	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	"github.com/epifi/be-common/pkg/logger"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palWorkflowPb "github.com/epifi/gamma/api/preapprovedloan/workflow"
	"github.com/epifi/gamma/preapprovedloan/helper"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers/epifitech"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"
)

func LoanDataCollection(ctx workflow.Context, _ *workflowPb.Request) error {
	lg := workflow.GetLogger(ctx)
	wfReqID := workflow.GetInfo(ctx).WorkflowExecution.ID

	var payload palWorkflowPb.LoanDataCollectionPayload
	wfProcessingParams, err := celestialPkg.GetWorkflowProcessingReqParams(ctx, &payload)
	if err != nil {
		lg.Error("failed to fetch workflow processing params", zap.Error(err))
		return err
	}

	ctx = epificontext.WorkflowContextWithOwnership(ctx, helper.GetPalOwnership(payload.GetLoanHeader().GetVendor()))

	updateErr := updateLoanRequest(ctx, wfProcessingParams.GetClientReqId().GetId(), palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_PENDING)
	if updateErr != nil {
		lg.Error("failed to update LR", zap.Error(updateErr))
		return updateErr
	}

	// 1. Perform Data collection group stage
	provider := epifitech.NewDataCollectionProviderV2(&payload)
	groupStagesRes, err := provider.GetGroupStages(ctx, nil)
	if err != nil {
		lg.Error("failed to get group stages from data collection provider", zap.Error(err))
		return err
	}

	flowName := palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_ELIGIBILITY
	for _, gStage := range groupStagesRes.GroupStages {
		stagesRes, err := provider.GetStages(ctx, &providers.GetStagesRequest{
			GroupStage:  gStage,
			ClientRefId: wfProcessingParams.GetClientReqId().GetId(),
		})
		if err != nil {
			lg.Error("failed to fetch stages for a group stage in data collection workflow", zap.String("groupStage", gStage.String()), zap.Error(err))
			return err
		}
		for _, stage := range stagesRes.Stages {
			err = runStage(ctx, stage, wfReqID, wfProcessingParams, gStage, payload.GetLoanHeader(), flowName, payload.GetProvenance())
			if err != nil {
				return err
			}
		}
	}

	updateErr = updateLoanRequest(ctx, wfProcessingParams.GetClientReqId().GetId(), palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS)
	if updateErr != nil {
		lg.Error("failed to update LR", zap.Error(updateErr))
		return updateErr
	}

	return nil
}

func runStage(ctx workflow.Context, stage stages.IStage, wfReqID string, wfProcessingParams *workflowPb.ProcessingParams,
	gStage palPb.GroupStage, loanHeader *palPb.LoanHeader, flowName palPb.LoanStepExecutionFlow, provenance palPb.Provenance) error {
	lg := workflow.GetLogger(ctx)
	preProcessRes, preProcessErr := stage.PreProcess(ctx, &stages.PreProcessRequest{
		WfReqId:            wfReqID,
		WfProcessingParams: wfProcessingParams,
		StageName:          stage.GetName(),
		GroupStage:         gStage,
		CelestialStage:     stage.GetCelestialStage(),
		Vendor:             loanHeader.GetVendor(),
		LoanProgram:        loanHeader.GetLoanProgram(),
		FlowName:           flowName,
		LoanHeader:         loanHeader,
		Provenance:         provenance,
	})
	if preProcessErr != nil {
		lg.Error("failed to perform stage pre-processing", zap.String("flowName", flowName.String()), zap.String("stage", stage.GetName().String()), zap.String(logger.CLIENT_REQUEST_ID, wfProcessingParams.GetClientReqId().GetId()), zap.Error(preProcessErr))
		return preProcessErr
	}

	performRes, performErr := stage.Perform(ctx, &stages.PerformRequest{
		WfReqId:            wfReqID,
		WfProcessingParams: wfProcessingParams,
		Request:            preProcessRes,
		Vendor:             loanHeader.GetVendor(),
		LoanProgram:        loanHeader.GetLoanProgram(),
	})
	if performErr != nil {
		lg.Error("failed to perform stage execution", zap.String("flowName", flowName.String()), zap.String("stage", stage.GetName().String()), zap.String(logger.CLIENT_REQUEST_ID, wfProcessingParams.GetClientReqId().GetId()), zap.Error(performErr))
		// not returning from here to perform post process
	}

	_, postProcessErr := stage.PostProcess(ctx, &stages.PostProcessRequest{
		Request:            performRes,
		WfReqId:            wfReqID,
		WfProcessingParams: wfProcessingParams,
		CelestialStage:     stage.GetCelestialStage(),
		Vendor:             loanHeader.GetVendor(),
		LoanProgram:        loanHeader.GetLoanProgram(),
		Provenance:         provenance,
	})
	if postProcessErr != nil {
		lrStatus := palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED
		if epifitemporal.IsRetryableError(postProcessErr) {
			lrStatus = palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_MANUAL_INTERVENTION
		}
		updateErr := updateLoanRequest(ctx, wfProcessingParams.GetClientReqId().GetId(), lrStatus)
		if updateErr != nil {
			lg.Error("failed to update LR", zap.Error(updateErr))
			return updateErr
		}
		lg.Error("failed to perform post processing or stage failed", zap.String("stage", stage.GetName().String()), zap.Error(postProcessErr))
		return postProcessErr
	}
	return nil
}
