package v2

import (
	"fmt"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	preApprovedLoanNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/helper"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"
)

type executeGroupStagesAndStagesRequest struct {
	wfReqId            string
	wfProcessingParams *workflowPb.ProcessingParams
	loanHeader         *palPb.LoanHeader
	vendorProvider     providers.IVendorProvider
	flowName           palPb.LoanStepExecutionFlow
}

// nolint: funlen
func executeGroupStages(ctx workflow.Context, req *executeGroupStagesAndStagesRequest) error {
	lg := workflow.GetLogger(ctx)
	ctx = epificontext.WorkflowContextWithOwnership(ctx, helper.GetPalOwnership(req.loanHeader.GetVendor()))
	// update loan request to initiated state
	updateErr := updateLoanRequest(ctx, req.wfProcessingParams.GetClientReqId().GetId(), palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_PENDING)
	if updateErr != nil {
		lg.Error("failed to update LR", zap.Error(updateErr))
		return updateErr
	}

	// Step2: Get group stages to be performed
	groupStagesRes, err := req.vendorProvider.GetGroupStages(ctx, nil)
	if err != nil {
		lg.Error("failed to get group stages from vendor provider", zap.Error(err))
		return err
	}
	// Step3: For each group stage, execute individual stages
	for _, gStage := range groupStagesRes.GroupStages {
		stagesRes, err := req.vendorProvider.GetStages(ctx, &providers.GetStagesRequest{GroupStage: gStage})
		if err != nil {
			lg.Error("failed to fetch stages for a group stage", zap.Error(err))
			return err
		}
		for _, stage := range stagesRes.Stages {
			// Note that stage Id in perform will be generated in PreProcess
			preProcessRes, preProcessErr := stage.PreProcess(ctx, &stages.PreProcessRequest{
				WfReqId:            req.wfReqId,
				WfProcessingParams: req.wfProcessingParams,
				StageName:          stage.GetName(),
				GroupStage:         gStage,
				CelestialStage:     stage.GetCelestialStage(),
				Vendor:             req.loanHeader.GetVendor(),
				LoanProgram:        req.loanHeader.GetLoanProgram(),
				FlowName:           req.flowName,
				LoanHeader:         req.loanHeader,
			})
			if preProcessErr != nil {
				lg.Error("failed to perform stage pre-processing", zap.String("stage", stage.GetName().String()), zap.Error(preProcessErr))
				return preProcessErr
			}
			performRes, performErr := stage.Perform(ctx, &stages.PerformRequest{
				WfReqId:            req.wfReqId,
				WfProcessingParams: req.wfProcessingParams,
				Request:            preProcessRes,
				Vendor:             req.loanHeader.GetVendor(),
				LoanProgram:        req.loanHeader.GetLoanProgram(),
			})
			if performErr != nil {
				lg.Error("failed to perform stage execution", zap.String("stage", stage.GetName().String()), zap.Error(performErr))
				// not returning from here to perform post process
			}
			_, postProcessErr := stage.PostProcess(ctx, &stages.PostProcessRequest{
				Request:            performRes,
				WfReqId:            req.wfReqId,
				WfProcessingParams: req.wfProcessingParams,
				CelestialStage:     stage.GetCelestialStage(),
				Vendor:             req.loanHeader.GetVendor(),
				LoanProgram:        req.loanHeader.GetLoanProgram(),
			})
			if postProcessErr != nil {
				lrStatus := palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED
				if epifitemporal.IsRetryableError(postProcessErr) {
					lrStatus = palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_MANUAL_INTERVENTION
				}
				updateErr = updateLoanRequest(ctx, req.wfProcessingParams.GetClientReqId().GetId(), lrStatus)
				if updateErr != nil {
					lg.Error("failed to update LR", zap.Error(updateErr))
					return updateErr
				}
				lg.Error("failed to perform post processing or stage failed", zap.String("stage", stage.GetName().String()), zap.Error(postProcessErr))
				return postProcessErr
			}
		}
	}

	updateErr = updateLoanRequest(ctx, req.wfProcessingParams.GetClientReqId().GetId(), palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS)
	if updateErr != nil {
		lg.Error("failed to update LR", zap.Error(updateErr))
		return updateErr
	}
	return nil
}

func updateLoanRequest(ctx workflow.Context, clientRequestID string, status palPb.LoanRequestStatus) error {
	lrUpdatePayload := &palActivityPb.UpdateLoanRequestActivityRequest{
		LoanRequestStatus: status,
	}
	reqPayload, err := protojson.Marshal(lrUpdatePayload)
	if err != nil {
		return fmt.Errorf("failed to marshall update loan request payload, %w", err)
	}
	actReq := &activityPb.Request{
		ClientReqId: clientRequestID,
		Payload:     reqPayload,
	}
	actRes := &activityPb.Response{}
	localActVer := workflow.GetVersion(ctx, "move-lightweight-activities-to-local", workflow.DefaultVersion, 1)
	if localActVer != 1 {
		updateErr := activityPkg.Execute(ctx, preApprovedLoanNs.UpdateLoanRequest, actRes, actReq)
		if updateErr != nil {
			return fmt.Errorf("UpdateLoanRequest activity failed: %w", updateErr)
		}
	} else {
		updateErr := activityPkg.ExecuteLocally(ctx, preApprovedLoanNs.UpdateLoanRequest, actRes, actReq)
		if updateErr != nil {
			return fmt.Errorf("UpdateLoanRequest activity failed: %w", updateErr)
		}
	}
	return nil
}
