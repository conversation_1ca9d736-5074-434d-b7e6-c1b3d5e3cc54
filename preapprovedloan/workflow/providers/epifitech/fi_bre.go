//nolint:dupl
package epifitech

import (
	"go.temporal.io/sdk/workflow"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"
)

type FiBre struct {
	*stages.CommonPaymentPreProcessStage
	*stages.CommonPaymentPostProcessStage
}

func NewFiBre() stages.IStage {
	return &FiBre{}
}

var _ stages.IStage = &FiBre{}

func (l *FiBre) Perform(ctx workflow.Context, req *stages.PerformRequest) (*stages.PerformResponse, error) {
	res := &stages.PerformResponse{
		LoanStep: req.Request.GetLoanStep(),
	}
	actReq := &palActivityPb.PalActivityRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.Request.GetLoanStep().GetOrchId(),
		},
		LoanStep:    req.Request.GetLoanStep(),
		Vendor:      req.Vendor,
		LoanProgram: req.LoanProgram,
	}
	actRes := &palActivityPb.PalActivityResponse{}

	actErr := activityPkg.Execute(ctx, palNs.MultiPolicyFiBre, actRes, actReq)

	// update next action to nil in both failure and success scenarios so that the control goes back to LOPE to decide next action
	updateReq := &palActivityPb.UpdateLRNextActionRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
			Ownership:   epificontext.OwnershipFromContext(ctx),
		},
		LoanRequestId: req.Request.GetLoanStep().GetRefId(),
		NextAction:    nil,
	}

	updateRes := &palActivityPb.PalActivityResponse{}
	localActVer := workflow.GetVersion(ctx, "move-lightweight-activities-to-local", workflow.DefaultVersion, 1)
	if localActVer != 1 {
		updateErr := activityPkg.Execute(ctx, palNs.UpdateLRNextAction, updateRes, updateReq)
		if providers.IsActivityError(ctx, updateRes.GetLoanStep(), nil, res, updateErr, false) {
			return res, updateErr
		}
	} else {
		updateErr := activityPkg.ExecuteLocally(ctx, palNs.UpdateLRNextAction, updateRes, updateReq)
		if providers.IsActivityError(ctx, updateRes.GetLoanStep(), nil, res, updateErr, false) {
			return res, updateErr
		}
	}

	if providers.IsActivityError(ctx, actRes.GetLoanStep(), actRes.GetLseFieldMasks(), res, actErr, false) {
		return res, actErr
	}
	res.SkipNextActionUpdate = true
	res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
	return res, nil
}

func (l *FiBre) GetName() palPb.LoanStepExecutionStepName {
	return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CHECK_FI_BRE
}

func (l *FiBre) GetCelestialStage() epifitemporal.Stage {
	return palNs.StageFiBre
}
