// nolint:dupl
package realtime

import (
	"fmt"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"
	emptyPb "google.golang.org/protobuf/types/known/emptypb"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	palNamespace "github.com/epifi/gamma/preapprovedloan/workflow/namespace"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"

	"go.temporal.io/sdk/workflow"
)

type PennyDrop struct {
	*stages.CommonPreProcessStage
	*stages.CommonPostProcessStage
}

func NewPennyDrop() *PennyDrop {
	return &PennyDrop{}
}

var _ stages.IStage = &PennyDrop{}

const (
	maxAddBankDetailsWaitTimeHrs = 120
	maxAddBankingDetailsRetries  = 3
)

func (ca *PennyDrop) Perform(ctx workflow.Context, req *stages.PerformRequest) (*stages.PerformResponse, error) {
	res := &stages.PerformResponse{
		LoanStep: req.Request.GetLoanStep(),
	}

	actRes := &palActivityPb.PalActivityResponse{}
	actReq := providers.GetActivityRequest(req, req.LoanProgram)
	retryNumber := 1
	for retryNumber <= maxAddBankingDetailsRetries {
		updatedLseDetails := actRes.GetLoanStep().GetDetails()
		if updatedLseDetails.GetOnboardingData().GetBankingDetails() != nil {
			updatedLseDetails.GetOnboardingData().BankingDetails = nil
		}

		updateReq := &palActivityPb.UpdateLoanStepExecutionRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
				Ownership:   epificontext.OwnershipFromContext(ctx),
			},
			LoanStep: &palPb.LoanStepExecution{
				Id:      req.Request.GetLoanStep().GetId(),
				Details: updatedLseDetails,
				Status:  palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
				// TODO: no traces for failed cases because of inplace update of OrchId. Need to fix this
				// Fix to be done with @himanshu and @govind changes for Mandate and Penny drop common module
				OrchId: uuid.New().String(),
			},
			LseFieldMasks: []palPb.LoanStepExecutionFieldMask{
				palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_ORCH_ID,
				palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS,
				palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
			},
		}
		updateRes := &palActivityPb.PalActivityResponse{}
		localActVer := workflow.GetVersion(ctx, "move-lightweight-activities-to-local", workflow.DefaultVersion, 1)
		if localActVer != 1 {
			err := activityPkg.Execute(ctx, palNs.UpdateLoanStepExecution, updateRes, updateReq)
			if providers.IsActivityError(ctx, updateRes.GetLoanStep(), nil, res, err, false) {
				return res, err
			}
		} else {
			err := activityPkg.ExecuteLocally(ctx, palNs.UpdateLoanStepExecution, updateRes, updateReq)
			if providers.IsActivityError(ctx, updateRes.GetLoanStep(), nil, res, err, false) {
				return res, err
			}
		}

		actErr := activityPkg.Execute(ctx, palNamespace.FedSetupPennyDropIntroScreen, actRes, actReq)
		if providers.IsActivityError(ctx, actRes.GetLoanStep(), actRes.GetLseFieldMasks(), res, actErr, false) {
			return res, actErr
		}

		bankErr := ca.updateBankingDetails(ctx, req, res)
		if bankErr != nil {
			if res.GetLoanStep().GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED && retryNumber < maxAddBankingDetailsRetries {
				retryNumber++
				continue
			}
			return res, bankErr
		}

		actErr = activityPkg.Execute(ctx, palNamespace.FedInitiatePennyDrop, actRes, actReq)
		if providers.IsActivityError(ctx, actRes.GetLoanStep(), actRes.GetLseFieldMasks(), res, actErr, false) {
			return res, actErr
		}

		actRes, actErr = providers.PerformActionInSyncV2(ctx, &providers.SyncPolledStageRequestV2{
			ActivityName:           palNamespace.FedPennyDropStatus,
			ActivityRequest:        actReq,
			ExecuteAsyncInParallel: true,
			StageRequest:           req,
			TimeoutDuration:        72 * time.Hour,
		})
		if providers.IsActivityError(ctx, actRes.GetLoanStep(), actRes.GetLseFieldMasks(), res, actErr, false) {
			if res.GetLoanStep().GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED && retryNumber < maxAddBankingDetailsRetries {
				retryNumber++
				continue
			}
			return res, actErr
		}

		break
	}

	res.LoanStep = actRes.GetLoanStep()
	res.LseFieldMasks = actRes.GetLseFieldMasks()
	return res, nil
}

func (ca *PennyDrop) GetName() palPb.LoanStepExecutionStepName {
	return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PENNY_DROP
}

func (ca *PennyDrop) GetCelestialStage() epifitemporal.Stage {
	return palNs.StagePennyDrop
}

func (ca *PennyDrop) updateBankingDetails(ctx workflow.Context, req *stages.PerformRequest, res *stages.PerformResponse) error {
	actReq := &palActivityPb.PalActivityRequest{
		LoanStep:    req.Request.GetLoanStep(),
		Vendor:      req.Vendor,
		LoanProgram: req.LoanProgram,
	}
	actRes := &palActivityPb.PalActivityResponse{}

	bankingConfirmationFuture, err := activityPkg.ExecuteAsync(ctx, palNs.GetBankingStatus, actReq, actRes)
	if err != nil {
		return fmt.Errorf("failed to execute GetBankingStatus activity: %w", err)
	}

	var errorToReturn error
	bankingConfirmationFuture.AddFutureHandler(func(futureGetErr error, resp *palActivityPb.PalActivityResponse) {
		if futureGetErr != nil {
			workflow.GetLogger(ctx).Info("banking details confirmation future finished with error", zap.Error(futureGetErr))
			errorToReturn = futureGetErr
		}
	})

	signalPayload := &emptyPb.Empty{}
	sigChannel := epifitemporal.NewSignalChannel(ctx, palNs.LoanApplicationBankingDetailsSignal, signalPayload)
	sigChannel.AddReceiverHandler(func(getErr error, payload *emptyPb.Empty) {
		workflow.GetLogger(ctx).Info("LoanApplicationBankingDetailsSignal signal returned first")
		if getErr != nil {
			workflow.GetLogger(ctx).Info("LoanApplicationBankingDetailsSignal signal processing failed", zap.Error(getErr))
			errorToReturn = getErr
		}
	})

	err = epifitemporal.ReceiveSignalWithFuture(ctx, bankingConfirmationFuture, sigChannel, maxAddBankDetailsWaitTimeHrs*time.Hour)
	if err != nil {
		workflow.GetLogger(ctx).Info("LoanApplicationBankingDetailsSignal receive signal with future processing failed", zap.Error(err))
		return err
	}

	if errorToReturn != nil {
		if epifitemporal.IsRetryableError(errorToReturn) {
			res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED
		}
		return errorToReturn
	}

	return nil
}
