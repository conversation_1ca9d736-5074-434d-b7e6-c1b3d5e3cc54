// nolint: dupl,funlen
package realtime

import (
	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"

	"go.temporal.io/sdk/workflow"
)

const (
	maxWaitTimeForAddressDetailsStageInHrs = 72
)

// AddAddressDetails represents the stage where we will take address from the user and persist the details at our end.
type AddAddressDetails struct {
	*stages.CommonPreProcessStage
	*stages.CommonPostProcessStage
}

func NewAddressDetails() *AddAddressDetails {
	return &AddAddressDetails{}
}

var _ stages.IStage = &AddAddressDetails{}

func (ca *AddAddressDetails) Perform(ctx workflow.Context, req *stages.PerformRequest) (*stages.PerformResponse, error) {
	res := &stages.PerformResponse{
		LoanStep: req.Request.GetLoanStep(),
	}

	// step1: set the next action to address details screen
	updateReq := &palActivityPb.UpdateLRNextActionRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
			Ownership:   epificontext.OwnershipFromContext(ctx),
		},
		LoanRequestId: req.Request.GetLoanStep().GetRefId(),
		NextAction: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_PRE_APPROVED_ADDRESS_CONFIRMATION_SCREEN,
		},
	}
	updateRes := &palActivityPb.PalActivityResponse{}
	localActVer := workflow.GetVersion(ctx, "move-lightweight-activities-to-local", workflow.DefaultVersion, 1)
	if localActVer != 1 {
		updateErr := activityPkg.Execute(ctx, palNs.UpdateLRNextAction, updateRes, updateReq)
		if providers.IsActivityError(ctx, updateRes.GetLoanStep(), nil, res, updateErr, false) {
			return res, updateErr
		}
	} else {
		updateErr := activityPkg.ExecuteLocally(ctx, palNs.UpdateLRNextAction, updateRes, updateReq)
		if providers.IsActivityError(ctx, updateRes.GetLoanStep(), nil, res, updateErr, false) {
			return res, updateErr
		}
	}

	// step2: wait for the sync proxy signal and execute the activity to update the details in lse when signal is received
	syncActivityReq := &palActivityPb.PalActivityRequest{
		LoanStep:    req.Request.GetLoanStep(),
		Vendor:      req.Vendor,
		LoanProgram: req.LoanProgram,
	}
	syncActivityRes := &palActivityPb.PalActivityResponse{}
	syncExecError := providers.PerformActionInSync(ctx, &providers.SyncPolledStageRequest{
		SyncActivityDetails: &providers.ActivityDetails{
			Name:         palNs.FedAddAddressDetails,
			SyncRequest:  syncActivityReq,
			SyncResponse: syncActivityRes,
		},
		ExecuteAsyncInParallel: false,
		StageName:              req.Request.GetLoanStep().GetStepName(),
		WfProcessingParams:     req.WfProcessingParams,
		LoanRequestId:          req.Request.GetLoanStep().GetRefId(),
		Vendor:                 req.Vendor,
		LoanProgram:            req.LoanProgram,
		TimeoutDurationInMins:  maxWaitTimeForAddressDetailsStageInHrs * 60,
	})
	if providers.IsActivityError(ctx, syncActivityRes.GetLoanStep(), nil, res, syncExecError, false) {
		return res, syncExecError
	}

	return res, nil
}

func (ca *AddAddressDetails) GetName() palPb.LoanStepExecutionStepName {
	return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS
}

func (ca *AddAddressDetails) GetCelestialStage() epifitemporal.Stage {
	return palNs.StageAddress
}
