package realtime

import (
	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"

	"go.temporal.io/sdk/workflow"
)

const (
	maxWaitTimeForEmploymentDetailsStageInHrs = 72
)

// EmploymentDetails represents the stage where we will take employment from the user and persist the details at our end.
type EmploymentDetails struct {
	*stages.CommonPreProcessStage
	*stages.CommonPostProcessStage
}

func NewEmploymentDetails() *EmploymentDetails {
	return &EmploymentDetails{}
}

var _ stages.IStage = &EmploymentDetails{}

func (ca *EmploymentDetails) Perform(ctx workflow.Context, req *stages.PerformRequest) (*stages.PerformResponse, error) {
	res := &stages.PerformResponse{
		LoanStep: req.Request.GetLoanStep(),
	}

	// step1: set the next action to employment details screen
	updateReq := &palActivityPb.UpdateLRNextActionRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
			Ownership:   epificontext.OwnershipFromContext(ctx),
		},
		LoanRequestId: req.Request.GetLoanStep().GetRefId(),
		NextAction: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_PRE_APPROVED_EMPLOYMENT_DETAILS_SCREEN,
		},
	}
	updateRes := &palActivityPb.PalActivityResponse{}
	localActVer := workflow.GetVersion(ctx, "move-lightweight-activities-to-local", workflow.DefaultVersion, 1)
	if localActVer != 1 {
		updateErr := activityPkg.Execute(ctx, palNs.UpdateLRNextAction, updateRes, updateReq)
		if providers.IsActivityError(ctx, updateRes.GetLoanStep(), nil, res, updateErr, false) {
			return res, updateErr
		}
	} else {
		updateErr := activityPkg.ExecuteLocally(ctx, palNs.UpdateLRNextAction, updateRes, updateReq)
		if providers.IsActivityError(ctx, updateRes.GetLoanStep(), nil, res, updateErr, false) {
			return res, updateErr
		}
	}

	// step2: wait for the sync proxy signal and execute the activity to update the details in lse when signal is received
	actSyncReq := &palActivityPb.PalActivityRequest{
		LoanStep:    req.Request.GetLoanStep(),
		Vendor:      req.Vendor,
		LoanProgram: req.LoanProgram,
	}
	actSyncRes := &palActivityPb.PalActivityResponse{}
	syncExecError := providers.PerformActionInSync(ctx, &providers.SyncPolledStageRequest{
		SyncActivityDetails: &providers.ActivityDetails{
			Name:         palNs.FedAddEmploymentDetails,
			SyncRequest:  actSyncReq,
			SyncResponse: actSyncRes,
		},
		ExecuteAsyncInParallel: false,
		StageName:              req.Request.GetLoanStep().GetStepName(),
		WfProcessingParams:     req.WfProcessingParams,
		LoanRequestId:          req.Request.GetLoanStep().GetRefId(),
		Vendor:                 req.Vendor,
		LoanProgram:            req.LoanProgram,
		TimeoutDurationInMins:  maxWaitTimeForEmploymentDetailsStageInHrs * 60,
	})
	if providers.IsActivityError(ctx, actSyncRes.GetLoanStep(), nil, res, syncExecError, false) {
		return res, syncExecError
	}

	return res, nil
}

func (ca *EmploymentDetails) GetName() palPb.LoanStepExecutionStepName {
	return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_EMPLOYMENT
}

func (ca *EmploymentDetails) GetCelestialStage() epifitemporal.Stage {
	return palNs.StageEmployment
}
