// nolint: dupl
package abfl

import (
	"fmt"
	"time"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"
)

const (
	maxEmploymentWaitTimeHrs = 120
)

// Employment represents the stage which needs a user to enter details for Employment
type Employment struct {
	*stages.CommonPreProcessStage
	*stages.CommonPostProcessStage
}

func NewEmployment() *Employment {
	return &Employment{}
}

var _ stages.IStage = &Employment{}

func (ca *Employment) Perform(ctx workflow.Context, req *stages.PerformRequest) (*stages.PerformResponse, error) {
	res := &stages.PerformResponse{
		LoanStep: req.Request.GetLoanStep(),
	}

	updateReq := &palActivityPb.UpdateLRNextActionRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
			Ownership:   epificontext.OwnershipFromContext(ctx),
		},
		LoanRequestId: req.Request.GetLoanStep().GetRefId(),
		NextAction: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_PRE_APPROVED_EMPLOYMENT_DETAILS_SCREEN,
		},
	}

	updateRes := &palActivityPb.PalActivityResponse{}
	localActVer := workflow.GetVersion(ctx, "move-lightweight-activities-to-local", workflow.DefaultVersion, 1)
	if localActVer != 1 {
		updateErr := activityPkg.Execute(ctx, palNs.UpdateLRNextAction, updateRes, updateReq)
		if providers.IsActivityError(ctx, updateRes.GetLoanStep(), nil, res, updateErr, false) {
			return res, updateErr
		}
	} else {
		updateErr := activityPkg.ExecuteLocally(ctx, palNs.UpdateLRNextAction, updateRes, updateReq)
		if providers.IsActivityError(ctx, updateRes.GetLoanStep(), nil, res, updateErr, false) {
			return res, updateErr
		}
	}

	empErr := ca.performAddEmploymentDetails(ctx, req)
	if empErr != nil {
		if epifitemporal.IsRetryableError(empErr) {
			res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED
		}
		return res, empErr
	}

	res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
	return res, nil
}

func (ca *Employment) GetName() palPb.LoanStepExecutionStepName {
	return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_EMPLOYMENT
}

func (ca *Employment) GetCelestialStage() epifitemporal.Stage {
	return palNs.StageEmployment
}

func (ca *Employment) performAddEmploymentDetails(ctx workflow.Context, req *stages.PerformRequest) error {
	actReq := &palActivityPb.PalActivityRequest{
		LoanStep:    req.Request.GetLoanStep(),
		Vendor:      req.Vendor,
		LoanProgram: req.LoanProgram,
	}
	actRes := &palActivityPb.PalActivityResponse{}
	employmentDetailsConfirmationFuture, err := activityPkg.ExecuteAsync(ctx, palNs.GetEmploymentStatus, actReq, actRes)
	if err != nil {
		return fmt.Errorf("failed to execute GetAddressStatus activity: %w", err)
	}

	var errorToReturn error
	employmentDetailsConfirmationFuture.AddFutureHandler(func(futureGetErr error, resp *palActivityPb.PalActivityResponse) {
		workflow.GetLogger(ctx).Info("employment details confirmation future finished")
		if futureGetErr != nil {
			workflow.GetLogger(ctx).Info("employment details confirmation future finished with error", zap.Error(futureGetErr))
			errorToReturn = futureGetErr
		}
	})
	signalPayload := &palActivityPb.LoanApplicationESignVerificationSignalPayload{}
	sigChannel := epifitemporal.NewSignalChannel(ctx, palNs.EmploymentConfirmationSignal, signalPayload)
	sigChannel.AddReceiverHandler(func(getErr error, payload *palActivityPb.LoanApplicationESignVerificationSignalPayload) {
		workflow.GetLogger(ctx).Info("EmploymentConfirmationSignal signal returned first")
		if getErr != nil {
			workflow.GetLogger(ctx).Info("EmploymentConfirmationSignal signal processing failed", zap.Error(getErr))
			errorToReturn = getErr
		}
	})

	err = epifitemporal.ReceiveSignalWithFuture(ctx, employmentDetailsConfirmationFuture, sigChannel, maxEmploymentWaitTimeHrs*time.Hour)
	if err != nil {
		workflow.GetLogger(ctx).Info("EmploymentConfirmationSignal receive signal with future processing failed", zap.Error(err))
		return err
	}
	if errorToReturn != nil {
		return errorToReturn
	}
	return nil
}
