// nolint: dupl, funlen
package abfl

import (
	"fmt"
	"time"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"
)

// Reference represents the stage which needs a user to enter details for their 2 references
type Reference struct {
	*stages.CommonPreProcessStage
	*stages.CommonPostProcessStage
}

func NewReference() *Reference {
	return &Reference{}
}

var _ stages.IStage = &Reference{}

func (ca *Reference) Perform(ctx workflow.Context, req *stages.PerformRequest) (*stages.PerformResponse, error) {
	res := &stages.PerformResponse{
		LoanStep: req.Request.GetLoanStep(),
	}

	updateReq := &palActivityPb.UpdateLRNextActionRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
			Ownership:   epificontext.OwnershipFromContext(ctx),
		},
		LoanRequestId: req.Request.GetLoanStep().GetRefId(),
		NextAction: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_LOANS_FORM_DETAILS_SCREEN,
		},
	}

	updateRes := &palActivityPb.PalActivityResponse{}
	localActVer := workflow.GetVersion(ctx, "move-lightweight-activities-to-local", workflow.DefaultVersion, 1)
	if localActVer != 1 {
		updateErr := activityPkg.Execute(ctx, palNs.UpdateLRNextAction, updateRes, updateReq)
		if providers.IsActivityError(ctx, updateRes.GetLoanStep(), nil, res, updateErr, false) {
			return res, updateErr
		}
	} else {
		updateErr := activityPkg.ExecuteLocally(ctx, palNs.UpdateLRNextAction, updateRes, updateReq)
		if providers.IsActivityError(ctx, updateRes.GetLoanStep(), nil, res, updateErr, false) {
			return res, updateErr
		}
	}

	addReferenceErr := ca.performAddReferenceDetails(ctx, req)
	if addReferenceErr != nil {
		if epifitemporal.IsRetryableError(addReferenceErr) {
			res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED
		}
		return res, addReferenceErr
	}

	v := workflow.GetVersion(ctx, "abfl_save_common_details_move", workflow.DefaultVersion, 1)
	if v < 1 {
		actReq := &palActivityPb.PalActivityRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: req.Request.GetLoanStep().GetOrchId(),
			},
			LoanStep:    req.Request.GetLoanStep(),
			Vendor:      req.Vendor,
			LoanProgram: req.LoanProgram,
		}
		actRes := &palActivityPb.PalActivityResponse{}
		actErr := activityPkg.Execute(ctx, palNs.AbflAddCommonDetails, actRes, actReq)
		if providers.IsActivityError(ctx, actRes.GetLoanStep(), nil, res, actErr, false) {
			return res, actErr
		}
	}

	res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
	return res, nil
}

func (ca *Reference) GetName() palPb.LoanStepExecutionStepName {
	return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_REFERENCES
}

func (ca *Reference) GetCelestialStage() epifitemporal.Stage {
	return palNs.StageAddReferences
}

func (ca *Reference) performAddReferenceDetails(ctx workflow.Context, req *stages.PerformRequest) error {
	actReq := &palActivityPb.PalActivityRequest{
		LoanStep:    req.Request.GetLoanStep(),
		Vendor:      req.Vendor,
		LoanProgram: req.LoanProgram,
	}
	actRes := &palActivityPb.PalActivityResponse{}
	referenceDetailsConfirmationFuture, err := activityPkg.ExecuteAsync(ctx, palNs.GetReferencesAdditionStatus, actReq, actRes)
	if err != nil {
		return fmt.Errorf("failed to execute GetReferencesAdditionStatus activity: %w", err)
	}

	var errorToReturn error
	referenceDetailsConfirmationFuture.AddFutureHandler(func(futureGetErr error, resp *palActivityPb.PalActivityResponse) {
		workflow.GetLogger(ctx).Info("reference details confirmation future finished")
		if futureGetErr != nil {
			workflow.GetLogger(ctx).Info("reference details confirmation future finished with error", zap.Error(futureGetErr))
			errorToReturn = futureGetErr
		}
	})
	signalPayload := &palActivityPb.LoanApplicationESignVerificationSignalPayload{}
	sigChannel := epifitemporal.NewSignalChannel(ctx, palNs.LoanApplicationAddReferencesSignal, signalPayload)
	sigChannel.AddReceiverHandler(func(getErr error, payload *palActivityPb.LoanApplicationESignVerificationSignalPayload) {
		workflow.GetLogger(ctx).Info("LoanApplicationAddReferencesSignal signal returned first")
		if getErr != nil {
			workflow.GetLogger(ctx).Info("LoanApplicationAddReferencesSignal signal processing failed", zap.Error(getErr))
			errorToReturn = getErr
		}
	})

	err = epifitemporal.ReceiveSignalWithFuture(ctx, referenceDetailsConfirmationFuture, sigChannel, maxEmploymentWaitTimeHrs*time.Hour)
	if err != nil {
		workflow.GetLogger(ctx).Info("Add References receive signal with future processing failed", zap.Error(err))
		return err
	}
	if errorToReturn != nil {
		return errorToReturn
	}

	return nil
}
