// nolint:funlen
package lamf

import (
	"fmt"
	"sort"
	"time"

	"go.temporal.io/sdk/log"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	"github.com/epifi/be-common/pkg/logger"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	workflow2 "github.com/epifi/gamma/api/preapprovedloan/workflow"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers/fiftyfin/lamf/syncproxy"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"

	"go.temporal.io/sdk/workflow"
)

const (
	mfPortoflioExpiryInHrs                  = 2*time.Hour + 30*time.Minute
	loanVerificationUserActionWaitTimeInHrs = 30 * 24 * time.Hour
)

// LoanDetailsVerification represents the stage which verifies if loan details selected by the user can be serviced or not.
type LoanDetailsVerificationV2 struct {
	*stages.CommonPreProcessStage
	*stages.CommonPostProcessStage
	*commonStageHelper
}

func NewLoanDetailsVerificationV2() *LoanDetailsVerificationV2 {
	return &LoanDetailsVerificationV2{}
}

var _ stages.IStage = &LoanDetailsVerificationV2{}

func (f *LoanDetailsVerificationV2) Perform(ctx workflow.Context, req *stages.PerformRequest) (*stages.PerformResponse, error) {
	res := &stages.PerformResponse{
		LoanStep: req.Request.GetLoanStep(),
	}
	lg := workflow.GetLogger(ctx)
	LoansFundVerificationVersionFlag := workflow.GetVersion(ctx, "loans-fund-verification-successful-v2", workflow.DefaultVersion, 1)

	for {
		verifyReq := f.newFiftyfinFundVerificationRequest(req)
		verifyRes := &palActivityPb.FiftyfinFundVerificationResponse{}
		actErr := activityPkg.Execute(ctx, palNs.FiftyfinFundVerificationV2, verifyRes, verifyReq)
		if providers.IsActivityErrorV2(ctx, res, actErr, providers.FiftyfinFundVerificationResponseErrParams(verifyRes)) {
			lg.Error("error in FiftyfinFundVerification activity", zap.Error(actErr))
			return res, actErr
		}

		if verifyRes.GetFundVerificationSuccessful() {
			lg.Info("successfully completed fund verification for user")
			if LoansFundVerificationVersionFlag == 1 {
				res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
				return res, nil
			} else if !f.shouldFetchMfPortfolio(ctx, res.GetLoanStep()) {
				res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
				return res, nil
			}
		} else {
			lg.Error("fund verification failed for user. Waiting for user action")
			userActionResp, err := f.getUserActionResponse(ctx, req, res, lg)
			if err != nil {
				return res, err
			}
			switch userActionResp {
			case USER_ACTION_RESPONSE_RESTART_STAGE:
				// This action will lead to a stage restart and loan verification step will start again.
				// This will happen when user tried portfolio fetch twice but still does not have sufficient mutual funds to avail the loan amount.
				res.NextGroupStage = palPb.GroupStage_GROUP_STAGE_VERIFY_LOAN_DETAILS
				res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
				return res, nil
			case USER_ACTION_RESPONSE_RESTART_VERIFICATION:
				// This will happen when we get signal that there is some change in state. And we will trigger the loan verification activity again
				// which generate the mf loan screen again if user does not have sufficient mutual funds to pledge.
				// Cases where this will happen is when user completes nft flow, and we need to generate the screen again.
				continue
			case USER_ACTION_RESPONSE_CONTINUE_VERIFICATION:
				// continue to fetch fiftyfin portfolio, which is needed to check if user has sufficient funds or not.
				// This will get triggered when both the nft flows are complete or the waiting time on intermediate mf link screen is complete and
				// user continues to portfolio fetch
			default:
				lg.Error(fmt.Sprintf("unknown userActionResponse: %d", userActionResp))
			}
		}

		fetchPfReq := f.newFiftyfinFetchMfPortfolioRequest(req, true, false, true, true)
		fetchPfRes := &palActivityPb.FiftyfinFetchMfPortfolioResponse{}
		actErr = activityPkg.Execute(ctx, palNs.FiftyfinFetchMfPortfolio, fetchPfRes, fetchPfReq)
		if providers.IsActivityErrorV2(ctx, res, actErr, providers.FiftyfinFetchMfPortfolioResponseErrParams(fetchPfRes)) {
			lg.Error("error in FiftyfinFetchMfPortfolio activity", zap.Error(actErr))
			// return to stop the workflow perform at this state
			// returning res to pass loan step in post process for updating in loan step (failure/cancelled cases)
			// returning error to log the error and then pass to post process
			return res, actErr
		}
	}
}

// nolint:dupl
func (f *LoanDetailsVerificationV2) waitForUserAction(ctx workflow.Context, req *stages.PerformRequest) (*palActivityPb.PalActivityResponse, *userActionDetails, error) {
	lg := workflow.GetLogger(ctx)
	var (
		errorToReturn error
		userAction    = &userActionDetails{}
	)

	actReq := f.getActivityRequest(req)
	actRes := &palActivityPb.PalActivityResponse{}
	waitForUserActionFuture, err := activityPkg.ExecuteAsync(ctx, palNs.FiftyfinWaitForLoanVerifyUserAction, actReq, actRes)
	if err != nil {
		return actRes, userAction, fmt.Errorf("failed to execute FiftyfinWaitForLoanVerifyUserAction async activity: %w", err)
	}
	waitForUserActionFuture.AddFutureHandler(func(waitForUserActionErr error, resp *palActivityPb.PalActivityResponse) {
		lg.Info("waitForVerifyLoanUserAction future finished")
		actRes = resp
		if waitForUserActionErr != nil {
			lg.Info("FiftyfinWaitForLoanVerifyUserAction activity async processing failed", zap.Error(waitForUserActionErr))
			errorToReturn = waitForUserActionErr
		} else {
			userAction.RecordUserAction = actRes.GetLoanStep().GetDetails().GetLoanDetailsVerificationData().GetLamf().GetUserAction().GetIdentifier()
		}
	})

	childCtx, cancel := workflow.WithCancel(ctx)
	defer cancel()
	defer waitForUserActionFuture.Cancel()

	selector := workflow.NewSelector(ctx).
		AddReceive(workflow.GetSignalChannel(ctx, string(palNs.LoanApplicationLoanDetailsVerificationSignal)), func(c workflow.ReceiveChannel, more bool) {
			var payload []byte
			c.Receive(ctx, &payload)
			lg.Info("received loan verification signal")
			var loanVerificationPayload = &workflow2.LoanApplicationLoanDetailsVerificationSignalPayload{}
			err = protojson.Unmarshal(payload, loanVerificationPayload)
			if err != nil {
				errorToReturn = err
			}
			userAction.RecordUserAction = loanVerificationPayload.GetUserActionIdentifier()
		}).
		AddReceive(workflow.GetSignalChannel(ctx, string(palNs.NftProcessCompletionProxyRequestSignal)), func(c workflow.ReceiveChannel, more bool) {
			var proxyReq = &workflow2.NftProcessCompletionProxyRequest{}
			c.Receive(ctx, &proxyReq)
			lg.Info("received nft proxy request signal", zap.String(logger.WORKFLOW, proxyReq.GetCallingWorkflowId()))
			userAction.NftCompletionSignalResp = proxyReq
		}).
		AddReceive(ctx.Done(), func(c workflow.ReceiveChannel, more bool) {
			errorToReturn = fmt.Errorf("loan verification cancelled while waiting for user action")
		}).
		AddFuture(workflow.NewTimer(childCtx, loanVerificationUserActionWaitTimeInHrs), func(f workflow.Future) {
			errorToReturn = fmt.Errorf("loan verification timeout while waiting for user action")
		})
	waitForUserActionFuture.AddToSelector(ctx, selector).Select(ctx)

	if errorToReturn != nil {
		return actRes, userAction, errorToReturn
	}
	return actRes, userAction, nil
}

func (f *LoanDetailsVerificationV2) getUserActionResponse(ctx workflow.Context, req *stages.PerformRequest, res *stages.PerformResponse, lg log.Logger) (UserActionResponse, error) {
	var userActionResponse UserActionResponse
	for {
		waitActRes, userAction, err := f.waitForUserAction(ctx, req)
		if providers.IsActivityErrorV2(ctx, res, err, providers.PalActivityResponseErrParams(waitActRes).WithMarkRetryableErrsAsFailed(true)) {
			lg.Error("error while waiting for user's action on fund verification failure screen", zap.Error(err))
			return userActionResponse, err
		}
		if userAction == nil {
			userActionResponse = USER_ACTION_RESPONSE_RESTART_VERIFICATION
			break
		}

		var exitLoop bool
		switch {
		case userAction.NftCompletionSignalResp != nil:
			if userAction.NftCompletionSignalResp.GetLoanRequestStatus() == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS {
				// set polling screen as next action only if the nft flow was success
				pollScreen, pollScreenErr := deeplink.GetApplicationStatusPollScreenWithCustomMsgDeepLinkV2(res.GetLoanStep().GetRefId(), "Please wait", "#000000", "", &palPb.LoanHeader{
					LoanProgram: req.LoanProgram,
					Vendor:      req.Vendor,
				})
				if pollScreenErr != nil {
					lg.Error("failed to get polling screen for nft completion", zap.Error(pollScreenErr))
					return userActionResponse, fmt.Errorf("failed to get polling screen for nft completion: %w", pollScreenErr)
				}

				updatesReq := &palActivityPb.UpdateLRNextActionRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
						Ownership:   epificontext.OwnershipFromContext(ctx),
					},
					LoanRequestId: req.Request.GetLoanStep().GetRefId(),
					NextAction:    pollScreen,
				}
				updateRes := &palActivityPb.PalActivityResponse{}
				localActVer := workflow.GetVersion(ctx, "move-lightweight-activities-to-local", workflow.DefaultVersion, 1)
				if localActVer != 1 {
					updateErr := activityPkg.Execute(ctx, palNs.UpdateLRNextAction, updateRes, updatesReq)
					if providers.IsActivityErrorV2(ctx, res, updateErr, providers.PalActivityResponseErrParams(updateRes).WithMarkRetryableErrsAsFailed(true)) {
						lg.Error("error in updating LR next action", zap.Error(updateErr))
						return userActionResponse, updateErr
					}
				} else {
					updateErr := activityPkg.ExecuteLocally(ctx, palNs.UpdateLRNextAction, updateRes, updatesReq)
					if providers.IsActivityErrorV2(ctx, res, updateErr, providers.PalActivityResponseErrParams(updateRes).WithMarkRetryableErrsAsFailed(true)) {
						lg.Error("error in updating LR next action", zap.Error(updateErr))
						return userActionResponse, updateErr
					}
				}
			}
			// send nft completion sync response
			err = syncproxy.SendResponse(ctx, userAction.NftCompletionSignalResp.GetCallingWorkflowId(), &workflow2.NftProcessCompletionProxyResponse{})
			if err != nil {
				lg.Error("failed to send nft completion proxy response", zap.Error(err))
			}

			if userAction.NftCompletionSignalResp.GetLoanRequestStatus() == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS {
				userActionResponse = USER_ACTION_RESPONSE_RESTART_VERIFICATION
				exitLoop = true
				break
			}
			continue
		case userAction.RecordUserAction == palPb.RecordUserActionIdentifier_RECORD_USER_ACTION_IDENTIFIER_LAMF_LINK_MF_SCREEN_RESTART_STAGE:
			if restartErr := f.processRestartStageUserAction(ctx, res); restartErr != nil {
				lg.Error("failed to process restart stage user action", zap.Error(restartErr))
				return userActionResponse, restartErr
			}
			userActionResponse = USER_ACTION_RESPONSE_RESTART_STAGE
			exitLoop = true
		case userAction.RecordUserAction == palPb.RecordUserActionIdentifier_RECORD_USER_ACTION_IDENTIFIER_LAMF_LINK_MF_SCREEN_VERIFY_MF:
			userActionResponse = USER_ACTION_RESPONSE_CONTINUE_VERIFICATION
			exitLoop = true
		default:
			lg.Error(fmt.Sprintf("unidentified user action"))
			userActionResponse = USER_ACTION_RESPONSE_RESTART_VERIFICATION
			exitLoop = true
		}

		if exitLoop {
			break
		}
	}

	return userActionResponse, nil
}

func (f *LoanDetailsVerificationV2) shouldFetchMfPortfolio(ctx workflow.Context, lse *palPb.LoanStepExecution) bool {
	var shouldFetchMfPortfolio = true
	sort.Slice(lse.GetDetails().GetLoanDetailsVerificationData().GetLamf().GetPfFetch_Data(), func(i, j int) bool {
		return lse.GetDetails().GetLoanDetailsVerificationData().GetLamf().GetPfFetch_Data()[i].GetCompletionTime().AsTime().After(lse.GetDetails().GetLoanDetailsVerificationData().GetLamf().GetPfFetch_Data()[j].GetCompletionTime().AsTime())
	})
	for _, pfFetchDetails := range lse.GetDetails().GetLoanDetailsVerificationData().GetLamf().GetPfFetch_Data() {
		var isPfFetchExpired = workflow.Now(ctx).Sub(pfFetchDetails.GetCompletionTime().AsTime()) > mfPortoflioExpiryInHrs
		if pfFetchDetails.GetIsFetchSuccess() && !isPfFetchExpired {
			shouldFetchMfPortfolio = false
			break
		}
	}
	return shouldFetchMfPortfolio
}

func (f *LoanDetailsVerificationV2) processRestartStageUserAction(ctx workflow.Context, res *stages.PerformResponse) error {
	updateReq := &palActivityPb.UpdateLoanStepExecutionRequest{
		LoanStep: &palPb.LoanStepExecution{
			Id:        res.GetLoanStep().GetId(),
			Status:    palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS,
			SubStatus: palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_LOAN_VERIFICATION_UNSUCCESSFUL,
		},
		LseFieldMasks: []palPb.LoanStepExecutionFieldMask{
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
		},
	}
	updateRes := &palActivityPb.PalActivityResponse{}
	localActVer := workflow.GetVersion(ctx, "move-lightweight-activities-to-local", workflow.DefaultVersion, 1)
	if localActVer != 1 {
		actErr := activityPkg.Execute(ctx, palNs.UpdateLoanStepExecution, updateRes, updateReq)
		if providers.IsActivityErrorV2(ctx, res, actErr, providers.PalActivityResponseErrParams(updateRes)) {
			return fmt.Errorf("failed to execute update lse status to loan verification unsuccessful: %w", actErr)
		}
	} else {
		actErr := activityPkg.ExecuteLocally(ctx, palNs.UpdateLoanStepExecution, updateRes, updateReq)
		if providers.IsActivityErrorV2(ctx, res, actErr, providers.PalActivityResponseErrParams(updateRes)) {
			return fmt.Errorf("failed to execute update lse status to loan verification unsuccessful: %w", actErr)
		}
	}

	return nil
}

func (f *LoanDetailsVerificationV2) GetName() palPb.LoanStepExecutionStepName {
	return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_VERIFY_LOAN_DETAILS
}

func (f *LoanDetailsVerificationV2) GetCelestialStage() epifitemporal.Stage {
	return palNs.StageVerifyLoanDetails
}

type userActionDetails struct {
	RecordUserAction        palPb.RecordUserActionIdentifier
	NftCompletionSignalResp *workflow2.NftProcessCompletionProxyRequest
}

type UserActionResponse int32

const (
	USER_ACTION_RESPONSE_UNSPECIFIED = 0 + iota
	USER_ACTION_RESPONSE_RESTART_STAGE
	USER_ACTION_RESPONSE_RESTART_VERIFICATION
	USER_ACTION_RESPONSE_CONTINUE_VERIFICATION
)
