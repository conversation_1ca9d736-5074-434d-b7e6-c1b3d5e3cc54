package lamf

import (
	"fmt"
	"time"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
	emptyPb "google.golang.org/protobuf/types/known/emptypb"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"
)

const maxESignSignalWaitingTimeHrs = 168
const EsignPollingScreenCustomMsg = "Verifying E-sign status"

// FiftyfinEsign represents stage which orchestrates user e-signing the agreement
type FiftyfinEsign struct {
	*stages.CommonPreProcessStage
	*stages.CommonPostProcessStage
	*commonStageHelper
}

func NewFiftyfinEsign() *FiftyfinEsign {
	return &FiftyfinEsign{}
}

var _ stages.IStage = &FiftyfinEsign{}

func (f *FiftyfinEsign) GetName() palPb.LoanStepExecutionStepName {
	return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_E_AGREEMENT
}

func (f *FiftyfinEsign) GetCelestialStage() epifitemporal.Stage {
	return palNs.StageESign
}

func (f *FiftyfinEsign) Perform(ctx workflow.Context, req *stages.PerformRequest) (*stages.PerformResponse, error) {
	res := &stages.PerformResponse{
		LoanStep: req.Request.GetLoanStep(),
	}

	actReq := f.getActivityRequest(req)
	actRes := &palActivityPb.PalActivityResponse{}
	actErr := activityPkg.Execute(ctx, palNs.FiftyfinGetEsignMandatePage, actRes, actReq)
	if providers.IsActivityErrorV2(ctx, res, actErr, providers.PalActivityResponseErrParams(actRes)) {
		// return to stop the workflow perform at this state
		// returning res to pass loan step in post process for updating in loan step (failure/cancelled cases)
		// returning error to log the error and then pass to post process
		return res, actErr
	}

	actRes, esignValidateErr := f.performESignValidation(ctx, req)
	if providers.IsActivityErrorV2(ctx, res, esignValidateErr, providers.PalActivityResponseErrParams(actRes)) {
		// generic error screen is set in post process
		return res, esignValidateErr
	}
	if res.GetLoanStep().GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS &&
		res.GetLoanStep().GetSubStatus() == palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_LOAN_APPLICATION_EXPIRED {
		res.NextGroupStage = palPb.GroupStage_GROUP_STAGE_RESET_VENDOR_LOAN_APPLICATION
		return res, nil
	}

	nextAction, dlErr := deeplink.GetApplicationStatusPollScreenWithCustomMsgDeepLinkV2(req.Request.GetLoanStep().GetRefId(), EsignPollingScreenCustomMsg, "#333333", "", f.GetLoanHeader())
	if dlErr != nil {
		return nil, fmt.Errorf("error while generating application status poll deeplink %w", dlErr)
	}
	// set next screen to polling screen
	updatesReq := &palActivityPb.UpdateLRNextActionRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
			Ownership:   epificontext.OwnershipFromContext(ctx),
		},
		LoanRequestId: req.Request.GetLoanStep().GetRefId(),
		NextAction:    nextAction,
	}

	updateRes := &palActivityPb.PalActivityResponse{}
	localActVer := workflow.GetVersion(ctx, "move-lightweight-activities-to-local", workflow.DefaultVersion, 1)
	if localActVer != 1 {
		updateErr := activityPkg.Execute(ctx, palNs.UpdateLRNextAction, updateRes, updatesReq)
		if providers.IsActivityErrorV2(ctx, res, updateErr, providers.PalActivityResponseErrParams(updateRes)) {
			workflow.GetLogger(ctx).Info("error in updating LR next action", zap.Error(updateErr))
			return res, updateErr
		}
	} else {
		updateErr := activityPkg.ExecuteLocally(ctx, palNs.UpdateLRNextAction, updateRes, updatesReq)
		if providers.IsActivityErrorV2(ctx, res, updateErr, providers.PalActivityResponseErrParams(updateRes)) {
			workflow.GetLogger(ctx).Info("error in updating LR next action", zap.Error(updateErr))
			return res, updateErr
		}
	}

	res.LseFieldMasks = []palPb.LoanStepExecutionFieldMask{
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
	}
	res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
	return res, nil
}

func (f *FiftyfinEsign) getActivityRequest(req *stages.PerformRequest) *palActivityPb.PalActivityRequest {
	return &palActivityPb.PalActivityRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.Request.GetLoanStep().GetOrchId(),
		},
		LoanStep:    req.Request.GetLoanStep(),
		Vendor:      req.Vendor,
		LoanProgram: req.LoanProgram,
	}
}

// nolint: dupl
func (f *FiftyfinEsign) performESignValidation(ctx workflow.Context, req *stages.PerformRequest) (*palActivityPb.PalActivityResponse, error) {
	var errorToReturn error
	lg := workflow.GetLogger(ctx)
	actReq := f.getActivityRequest(req)
	actRes := &palActivityPb.PalActivityResponse{}
	getReviewDetailsFut, err := activityPkg.ExecuteAsync(ctx, palNs.FiftyfinGetApplicationProcessStatus, actReq, actRes)
	if err != nil {
		return actRes, err
	}
	getReviewDetailsFut.AddFutureHandler(func(esignSetupErr error, resp *palActivityPb.PalActivityResponse) {
		lg.Info("getReviewDetailsFut future finished")
		actRes = resp
		if esignSetupErr != nil {
			lg.Error("FiftyfinGetApplicationProcessStatus activity async processing failed for esign", zap.Error(esignSetupErr))
			errorToReturn = fmt.Errorf("FiftyfinGetApplicationProcessStatus activity async processing failed for esign: %w", esignSetupErr)
		}
	})

	// signal channel waiting for signal from vendor indicating esign final status reached
	signalPayload := &emptyPb.Empty{}
	sigChan := epifitemporal.NewSignalChannel(ctx, palNs.LoanApplicationESignVerificationSignal, signalPayload)
	sigChan.AddReceiverHandler(func(getErr error, payload *emptyPb.Empty) {
		lg.Info("LoanApplicationESignVerificationSignal signal recieved first")
		if getErr != nil {
			lg.Error("LoanApplicationESignVerificationSignal signal processing failed", zap.Error(getErr))
			errorToReturn = fmt.Errorf("LoanApplicationESignVerificationSignal signal processing failed: %w", getErr)
		}
	})

	// TODO(bhumij): Based on testing with client, we can change signal payload, if we need something from there
	err = epifitemporal.ReceiveSignalWithFuture(ctx, getReviewDetailsFut, sigChan, maxESignSignalWaitingTimeHrs*time.Hour)
	if err != nil {
		lg.Error("error configuring receiving signal with future for e-sign setup status", zap.Error(err))
		return actRes, fmt.Errorf("error configuring receiving signal with future for e-sign setup status: %w", err)
	}

	if errorToReturn != nil {
		return actRes, errorToReturn
	}

	return actRes, nil
}
