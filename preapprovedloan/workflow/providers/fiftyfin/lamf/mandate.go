package lamf

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"fmt"
	"time"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
	emptyPb "google.golang.org/protobuf/types/known/emptypb"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"
)

const maxMandateSignalWaitingTimeHrs = 168

// FiftyfinMandate represents stage which orchestrates user e-Mandate
type FiftyfinMandate struct {
	*stages.CommonPreProcessStage
	*stages.CommonPostProcessStage
	*commonStageHelper
}

func NewFiftyfinMandate() *FiftyfinMandate {
	return &FiftyfinMandate{}
}

var _ stages.IStage = &FiftyfinMandate{}

func (f *FiftyfinMandate) GetName() palPb.LoanStepExecutionStepName {
	return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_MANDATE
}

func (f *FiftyfinMandate) GetCelestialStage() epifitemporal.Stage {
	return palNs.StageMandate
}

func (f *FiftyfinMandate) Perform(ctx workflow.Context, req *stages.PerformRequest) (*stages.PerformResponse, error) {
	res := &stages.PerformResponse{
		LoanStep: req.Request.GetLoanStep(),
	}

	v := workflow.GetVersion(ctx, "user-risk-check-in-mandate", workflow.DefaultVersion, 1)
	if v == 1 {
		_, userRiskErr := userRiskCheck(ctx, req)
		if userRiskErr != nil && !epifitemporal.IsRetryableError(userRiskErr) {
			workflow.GetLogger(ctx).Error("user risk check failed, unpledging funds", zap.Error(userRiskErr))
			unpledgeFundsReq := f.newFiftyfinVoidLoanRequest(req, commontypes.BoolToBooleanEnum(false), commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED)
			unpledgeFundsRes := &palActivityPb.FiftyfinVoidLoanResponse{}
			actErr := activityPkg.Execute(ctx, palNs.FiftyfinVoidLoan, unpledgeFundsRes, unpledgeFundsReq)
			if providers.IsActivityErrorV2(ctx, res, actErr, providers.FiftyfinVoidLoanResponseErrParams(unpledgeFundsRes)) {
				workflow.GetLogger(ctx).Error("error in FiftyfinVoidLoan activity", zap.Error(actErr))
				return res, actErr
			}
			res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED
			res.GetLoanStep().SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_RISKY_USER_CHECK
			res.LseFieldMasks = append(res.LseFieldMasks, palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS)
			return res, nil
		}
		// NOTE: ignoring retry exhausted failures as it's very likely user is not risky and risky users will get blocked later anyway.
		// if providers.IsActivityErrorV2(ctx, res, userRiskErr, providers.PalActivityResponseErrParams(userRiskRes)) {
		//	return res, userRiskErr
		// }
	}

	actReq := f.getActivityRequest(req)
	actRes := &palActivityPb.PalActivityResponse{}
	actErr := activityPkg.Execute(ctx, palNs.FiftyfinGetEsignMandatePage, actRes, actReq)
	if providers.IsActivityErrorV2(ctx, res, actErr, providers.PalActivityResponseErrParams(actRes)) {
		// return to stop the workflow perform at this state
		// returning res to pass loan step in post process for updating in loan step (failure/cancelled cases)
		// returning error to log the error and then pass to post process
		return res, actErr
	}

	actRes, mandateErr := f.performEMandateValidation(ctx, req)
	if providers.IsActivityErrorV2(ctx, res, mandateErr, providers.PalActivityResponseErrParams(actRes)) {
		// generic error screen will be set in post process
		return res, mandateErr
	}
	if res.GetLoanStep().GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS &&
		res.GetLoanStep().GetSubStatus() == palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_LOAN_APPLICATION_EXPIRED {
		res.NextGroupStage = palPb.GroupStage_GROUP_STAGE_RESET_VENDOR_LOAN_APPLICATION
		return res, nil
	}

	nextAction, dlErr := deeplink.GetApplicationStatusPollScreenWithCustomMsgDeepLinkV2(req.Request.GetLoanStep().GetRefId(), "Verifying E-mandate status", "#333333", "", &palPb.LoanHeader{
		LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_LAMF,
		Vendor:      palPb.Vendor_FIFTYFIN,
	})
	if dlErr != nil {
		return nil, fmt.Errorf("error while generating application status poll deeplink: %w", dlErr)
	}
	// set next screen to polling screen
	updatesReq := &palActivityPb.UpdateLRNextActionRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
			Ownership:   epificontext.OwnershipFromContext(ctx),
		},
		LoanRequestId: req.Request.GetLoanStep().GetRefId(),
		NextAction:    nextAction,
	}

	updateRes := &palActivityPb.PalActivityResponse{}
	localActVer := workflow.GetVersion(ctx, "move-lightweight-activities-to-local", workflow.DefaultVersion, 1)
	if localActVer != 1 {
		updateErr := activityPkg.Execute(ctx, palNs.UpdateLRNextAction, updateRes, updatesReq)
		if providers.IsActivityErrorV2(ctx, res, updateErr, providers.PalActivityResponseErrParams(updateRes)) {
			workflow.GetLogger(ctx).Info("error in updating LR next action", zap.Error(updateErr))
			return res, updateErr
		}
	} else {
		updateErr := activityPkg.ExecuteLocally(ctx, palNs.UpdateLRNextAction, updateRes, updatesReq)
		if providers.IsActivityErrorV2(ctx, res, updateErr, providers.PalActivityResponseErrParams(updateRes)) {
			workflow.GetLogger(ctx).Info("error in updating LR next action", zap.Error(updateErr))
			return res, updateErr
		}
	}

	res.LseFieldMasks = []palPb.LoanStepExecutionFieldMask{
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
	}
	res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
	return res, nil
}

// nolint:dupl
func (f *FiftyfinMandate) performEMandateValidation(ctx workflow.Context, req *stages.PerformRequest) (*palActivityPb.PalActivityResponse, error) {
	var errorToReturn error
	lg := workflow.GetLogger(ctx)
	actReq := f.getActivityRequest(req)
	actRes := &palActivityPb.PalActivityResponse{}
	getReviewDetailsFut, err := activityPkg.ExecuteAsync(ctx, palNs.FiftyfinGetApplicationProcessStatus, actReq, actRes)
	if err != nil {
		return actRes, err
	}
	getReviewDetailsFut.AddFutureHandler(func(mandateSetupErr error, resp *palActivityPb.PalActivityResponse) {
		lg.Info("getReviewDetailsFut future finished")
		actRes = resp
		if mandateSetupErr != nil {
			lg.Error("FiftyfinGetApplicationProcessStatus activity async processing failed for mandate", zap.Error(mandateSetupErr))
			errorToReturn = fmt.Errorf("FiftyfinGetApplicationProcessStatus activity async processing failed for mandate: %w", mandateSetupErr)
		}
	})

	// signal channel waiting for signal from vendor indicating kyc review final status reached
	signalPayload := &emptyPb.Empty{}
	sigChan := epifitemporal.NewSignalChannel(ctx, palNs.LoanApplicationMandateCompletionSignal, signalPayload)
	sigChan.AddReceiverHandler(func(getErr error, payload *emptyPb.Empty) {
		lg.Info("LoanApplicationMandateCompletionSignal signal recieved first")
		if getErr != nil {
			lg.Error("LoanApplicationMandateCompletionSignal signal processing failed", zap.Error(getErr))
			errorToReturn = fmt.Errorf("LoanApplicationMandateCompletionSignal signal processing failed: %w", getErr)
		}
	})

	// TODO(bhumij): Based on testing with client, we can change signal payload, if we need something from there
	err = epifitemporal.ReceiveSignalWithFuture(ctx, getReviewDetailsFut, sigChan, maxMandateSignalWaitingTimeHrs*time.Hour)
	if err != nil {
		lg.Error("error configuring receiving signal with future for e-mandate setup status", zap.Error(err))
		return actRes, fmt.Errorf("error configuring receiving signal with future for e-mandate setup status: %w", err)
	}

	if errorToReturn != nil {
		return actRes, errorToReturn
	}

	return actRes, nil
}
