package lamf

import (
	"fmt"
	"time"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
	emptyPb "google.golang.org/protobuf/types/known/emptypb"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/pkg/epificontext"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers"

	"github.com/epifi/be-common/pkg/epifitemporal"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"
)

const maxApplicationCompletionSignalWaitingTimeHrs = 168

// FiftyfinDrawdown represents the stage checks for user loan process completion and shows success screen
type FiftyfinDrawdown struct {
	*stages.CommonPreProcessStage
	*stages.CommonPostProcessStage
	*commonStageHelper
}

func NewFiftyfinDrawdown() *FiftyfinDrawdown {
	return &FiftyfinDrawdown{}
}

var _ stages.IStage = &FiftyfinDrawdown{}

func (f *FiftyfinDrawdown) Perform(ctx workflow.Context, req *stages.PerformRequest) (*stages.PerformResponse, error) {
	res := &stages.PerformResponse{
		LoanStep: req.Request.GetLoanStep(),
	}

	nextAction, dlErr := deeplink.GetApplicationStatusPollScreenWithCustomMsgDeepLinkV2(req.Request.GetLoanStep().GetRefId(), "Processing your loan application", "#333333", "", f.GetLoanHeader())
	if dlErr != nil {
		return nil, fmt.Errorf("error while generating application status poll deeplink %w", dlErr)
	}
	// set next screen to polling screen
	updateProcessingScreenReq := &palActivityPb.UpdateLRNextActionRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
			Ownership:   epificontext.OwnershipFromContext(ctx),
		},
		LoanRequestId: req.Request.GetLoanStep().GetRefId(),
		NextAction:    nextAction,
	}
	updateProcessingScreenRes := &palActivityPb.PalActivityResponse{}
	localActVer := workflow.GetVersion(ctx, "move-lightweight-activities-to-local", workflow.DefaultVersion, 1)
	if localActVer != 1 {
		updateErr := activityPkg.Execute(ctx, palNs.UpdateLRNextAction, updateProcessingScreenRes, updateProcessingScreenReq)
		if providers.IsActivityErrorV2(ctx, res, updateErr, providers.PalActivityResponseErrParams(updateProcessingScreenRes)) {
			workflow.GetLogger(ctx).Info("error in updating LR next action", zap.Error(updateErr))
			return res, updateErr
		}
	} else {
		updateErr := activityPkg.ExecuteLocally(ctx, palNs.UpdateLRNextAction, updateProcessingScreenRes, updateProcessingScreenReq)
		if providers.IsActivityErrorV2(ctx, res, updateErr, providers.PalActivityResponseErrParams(updateProcessingScreenRes)) {
			workflow.GetLogger(ctx).Info("error in updating LR next action", zap.Error(updateErr))
			return res, updateErr
		}
	}

	actRes, completionErr := f.validateProcessCompletion(ctx, req)
	if providers.IsActivityErrorV2(ctx, res, completionErr, providers.PalActivityResponseErrParams(actRes)) {
		return res, completionErr
	}

	// update next action to success screen
	actReq := f.getActivityRequest(req)
	actRes = &palActivityPb.PalActivityResponse{}
	updateSuccessScreenErr := activityPkg.Execute(ctx, palNs.FiftyfinSetDrawdownSuccessScreen, actRes, actReq)
	if providers.IsActivityErrorV2(ctx, res, updateSuccessScreenErr, providers.PalActivityResponseErrParams(actRes)) {
		workflow.GetLogger(ctx).Info("error in updating LR next action to drawdown success screen", zap.Error(updateSuccessScreenErr))
		return res, updateSuccessScreenErr
	}

	res.LseFieldMasks = []palPb.LoanStepExecutionFieldMask{
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
	}
	res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
	res.NextAction = actRes.GetNextAction()
	return res, nil
}

func (f *FiftyfinDrawdown) GetName() palPb.LoanStepExecutionStepName {
	return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_DRAWDOWN
}

func (f *FiftyfinDrawdown) GetCelestialStage() epifitemporal.Stage {
	return palNs.StageDrawDown
}

// nolint: dupl
func (f *FiftyfinDrawdown) validateProcessCompletion(ctx workflow.Context, req *stages.PerformRequest) (*palActivityPb.PalActivityResponse, error) {
	var errorToReturn error
	lg := workflow.GetLogger(ctx)

	// process sync call to vendor
	actReq := f.getActivityRequest(req)
	actRes := &palActivityPb.PalActivityResponse{}
	applicationProcessStatusFut, err := activityPkg.ExecuteAsync(ctx, palNs.FiftyfinGetApplicationProcessStatus, actReq, actRes)
	if err != nil {
		return actRes, err
	}
	applicationProcessStatusFut.AddFutureHandler(func(processCompletionErr error, resp *palActivityPb.PalActivityResponse) {
		lg.Info("applicationProcessStatusFut future finished")
		actRes = resp
		if processCompletionErr != nil {
			lg.Error("FiftyfinGetApplicationProcessStatus activity async processing failed for mandate", zap.Error(processCompletionErr))
			errorToReturn = fmt.Errorf("FiftyfinGetApplicationProcessStatus activity async processing failed for mandate: %w", processCompletionErr)
		}
	})

	// signal channel waiting for signal from vendor indicating loan application final status reached
	signalPayload := &emptyPb.Empty{}
	sigChan := epifitemporal.NewSignalChannel(ctx, palNs.LoanApplicationCompletionSignal, signalPayload)
	sigChan.AddReceiverHandler(func(getErr error, payload *emptyPb.Empty) {
		lg.Info("LoanApplicationCompletionSignal signal recieved first")
		if getErr != nil {
			lg.Error("LoanApplicationCompletionSignal signal processing failed", zap.Error(getErr))
			errorToReturn = fmt.Errorf("LoanApplicationCompletionSignal signal processing failed: %w", getErr)
		}
	})

	// TODO(bhumij): Based on testing with client, we can change signal payload, if we need something from there
	err = epifitemporal.ReceiveSignalWithFuture(ctx, applicationProcessStatusFut, sigChan, maxApplicationCompletionSignalWaitingTimeHrs*time.Hour)
	if err != nil {
		lg.Error("error configuring receiving signal with future for loan application completion status", zap.Error(err))
		return actRes, fmt.Errorf("error configuring receiving signal with future for loan application completion status: %w", err)
	}
	if errorToReturn != nil {
		return actRes, errorToReturn
	}

	return actRes, nil
}
