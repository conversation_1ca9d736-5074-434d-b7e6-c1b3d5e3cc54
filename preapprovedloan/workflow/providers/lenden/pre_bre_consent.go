//nolint:dupl,goimports
package lenden

import (
	"fmt"
	"time"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/emptypb"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers/helper"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"
)

type PreBreConsent struct {
	*stages.CommonPreProcessStage
	*stages.CommonPostProcessStage
	*helper.ActivityHelper
}

func NewPreBreConsent() *PreBreConsent {
	return &PreBreConsent{}
}

func (s *PreBreConsent) Perform(ctx workflow.Context, req *stages.PerformRequest) (*stages.PerformResponse, error) {
	res := &stages.PerformResponse{
		LoanStep:      req.Request.GetLoanStep(),
		LseFieldMasks: []palPb.LoanStepExecutionFieldMask{},
	}

	updateReq := &palActivityPb.UpdateLRNextActionRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
			Ownership:   common.Ownership_EPIFI_TECH,
		},
		LoanRequestId: req.Request.GetLoanStep().GetRefId(),
		NextAction: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_LOANS_CONSENT_V2_SCREEN,
		},
		LoecOwner: palPb.Vendor_LENDEN,
	}
	updateRes := &palActivityPb.PalActivityResponse{}
	localActVer := workflow.GetVersion(ctx, "move-lightweight-activities-to-local", workflow.DefaultVersion, 1)
	if localActVer != 1 {
		updateErr := activityPkg.Execute(ctx, palNs.UpdateLRNextAction, updateRes, updateReq)
		if providers.IsActivityError(ctx, updateRes.GetLoanStep(), nil, res, updateErr, false) {
			workflow.GetLogger(ctx).Error("error in updating next action to loans consent screen", zap.Error(updateErr))
			return res, updateErr
		}
	} else {
		updateErr := activityPkg.ExecuteLocally(ctx, palNs.UpdateLRNextAction, updateRes, updateReq)
		if providers.IsActivityError(ctx, updateRes.GetLoanStep(), nil, res, updateErr, false) {
			workflow.GetLogger(ctx).Error("error in updating next action to loans consent screen", zap.Error(updateErr))
			return res, updateErr
		}
	}
	actErr := s.performCollectPreBreConsent(ctx, req)
	if providers.IsActivityError(ctx, nil, nil, res, actErr, true) {
		return res, actErr
	}
	res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
	res.LseFieldMasks = append(res.LseFieldMasks, palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS)
	return res, nil

}

func (cr *PreBreConsent) GetName() palPb.LoanStepExecutionStepName {
	return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PRE_BRE_CONSENT
}

func (ca *PreBreConsent) GetCelestialStage() epifitemporal.Stage {
	return palNs.PreBreConsent
}

func (s *PreBreConsent) performCollectPreBreConsent(ctx workflow.Context, req *stages.PerformRequest) error {
	actReq := s.GetActivityRequest(req, req.LoanProgram)
	actRes := &palActivityPb.PalActivityResponse{}
	collectConsentFuture, err := activityPkg.ExecuteAsync(ctx, palNs.LendenPreBreConsentStatus, actReq, actRes)
	if err != nil {
		return fmt.Errorf("failed to execute collectConsentFuture activity: %w", err)
	}
	var errorToReturn error
	collectConsentFuture.AddFutureHandler(func(futureGetErr error, resp *palActivityPb.PalActivityResponse) {
		if futureGetErr != nil {
			workflow.GetLogger(ctx).Info("collectConsentFuture future finished with error", zap.Error(futureGetErr))
			errorToReturn = futureGetErr
		}
	})
	signalPayload := &emptypb.Empty{}
	sigChannel := epifitemporal.NewSignalChannel(ctx, palNs.PreBreConsentSignal, signalPayload)
	sigChannel.AddReceiverHandler(func(getErr error, payload *emptypb.Empty) {
		workflow.GetLogger(ctx).Info("LendenPreBreConsentSignal signal returned first")
		if getErr != nil {
			workflow.GetLogger(ctx).Info("LendenPreBreConsentSignal signal processing failed", zap.Error(getErr))
			errorToReturn = getErr
		}
	})

	err = epifitemporal.ReceiveSignalWithFuture(ctx, collectConsentFuture, sigChannel, maxAddressWaitTimeHrs*time.Hour)
	if err != nil {
		workflow.GetLogger(ctx).Info("LendenPreBreConsentSignal receive signal with future processing failed", zap.Error(err))
		return err
	}
	if errorToReturn != nil {
		return errorToReturn
	}
	return nil
}
