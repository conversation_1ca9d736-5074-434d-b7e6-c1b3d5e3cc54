//nolint:dupl,goimports
package lenden

import (
	"fmt"
	"time"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/emptypb"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers/helper"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"
)

const (
	maxAddressWaitTimeHrs = 10
)

type PreBreLoanDataCollection struct {
	*stages.HardOfferSkipApplicationStagePreProcess
	*stages.CommonPostProcessStage
	*helper.ActivityHelper
}

func NewPreBreLoanDataCollection() *PreBreLoanDataCollection {
	return &PreBreLoanDataCollection{}
}

func (s *PreBreLoanDataCollection) Perform(ctx workflow.Context, req *stages.PerformRequest) (*stages.PerformResponse, error) {
	res := &stages.PerformResponse{
		LoanStep:      req.Request.GetLoanStep(),
		LseFieldMasks: []palPb.LoanStepExecutionFieldMask{},
	}

	wfVersionCheckHardOfferPreProcess := workflow.GetVersion(ctx, "lenden-new-check-hard-offer-pre-process", workflow.DefaultVersion, 1)
	if wfVersionCheckHardOfferPreProcess == 1 {
		// if lse is already marked as success, skip this stage
		skip := providers.CheckToSkipStage(res.LoanStep)
		if skip {
			return res, nil
		}
	}

	updateReq := &palActivityPb.UpdateLRNextActionRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
			Ownership:   common.Ownership_EPIFI_TECH,
		},
		LoanRequestId: req.Request.GetLoanStep().GetRefId(),
		NextAction: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_LOANS_OFFER_INTRO_SCREEN,
		},
		LoecOwner: palPb.Vendor_LENDEN,
	}
	updateRes := &palActivityPb.PalActivityResponse{}
	localActVer := workflow.GetVersion(ctx, "move-lightweight-activities-to-local", workflow.DefaultVersion, 1)
	if localActVer != 1 {
		updateErr := activityPkg.Execute(ctx, palNs.UpdateLRNextAction, updateRes, updateReq)
		if providers.IsActivityError(ctx, updateRes.GetLoanStep(), nil, res, updateErr, false) {
			workflow.GetLogger(ctx).Error("error in updating next action to LOANS_OFFER_DETAILS_SCREEN", zap.Error(updateErr))
			return res, updateErr
		}
	} else {
		updateErr := activityPkg.ExecuteLocally(ctx, palNs.UpdateLRNextAction, updateRes, updateReq)
		if providers.IsActivityError(ctx, updateRes.GetLoanStep(), nil, res, updateErr, false) {
			workflow.GetLogger(ctx).Error("error in updating next action to LOANS_OFFER_DETAILS_SCREEN", zap.Error(updateErr))
			return res, updateErr
		}
	}

	actErr := s.performCollectPreBreLoanData(ctx, req)
	if providers.IsActivityError(ctx, nil, nil, res, actErr, true) {
		return res, actErr
	}
	res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
	res.LseFieldMasks = append(res.LseFieldMasks, palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS)
	return res, nil
}

func (s *PreBreLoanDataCollection) GetName() palPb.LoanStepExecutionStepName {
	return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PRE_BRE_DATA_FETCH_LOAN_DETAILS
}

func (s *PreBreLoanDataCollection) GetCelestialStage() epifitemporal.Stage {
	return palNs.PreBreLoanDataCollection
}

func (s *PreBreLoanDataCollection) performCollectPreBreLoanData(ctx workflow.Context, req *stages.PerformRequest) error {
	actReq := s.GetActivityRequest(req, req.LoanProgram)
	actRes := &palActivityPb.PalActivityResponse{}
	collectLoanDataFuture, err := activityPkg.ExecuteAsync(ctx, palNs.LendenPreBreLoanDataCollectionStatus, actReq, actRes)
	if err != nil {
		return fmt.Errorf("failed to execute PollLSEInProgressStatus activity: %w", err)
	}
	var errorToReturn error
	collectLoanDataFuture.AddFutureHandler(func(futureGetErr error, resp *palActivityPb.PalActivityResponse) {
		if futureGetErr != nil {
			workflow.GetLogger(ctx).Info("collectLoanDataFuture future finished with error", zap.Error(futureGetErr))
			errorToReturn = futureGetErr
		}
	})
	signalPayload := &emptypb.Empty{}
	sigChannel := epifitemporal.NewSignalChannel(ctx, palNs.PreBreLoanDataCollectionSignal, signalPayload)
	sigChannel.AddReceiverHandler(func(getErr error, payload *emptypb.Empty) {
		workflow.GetLogger(ctx).Info("LendenPreBreLoanDataCollectionSignal signal returned first")
		if getErr != nil {
			workflow.GetLogger(ctx).Info("LendenPreBreLoanDataCollectionSignal signal processing failed", zap.Error(getErr))
			errorToReturn = getErr
		}
	})

	err = epifitemporal.ReceiveSignalWithFuture(ctx, collectLoanDataFuture, sigChannel, maxAddressWaitTimeHrs*time.Hour)
	if err != nil {
		workflow.GetLogger(ctx).Info("LendenPreBreLoanDataCollectionSignal receive signal with future processing failed", zap.Error(err))
		return err
	}
	if errorToReturn != nil {
		return errorToReturn
	}

	return nil
}
