package stockguardian

import (
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/be-common/pkg/epifitemporal"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"

	"go.temporal.io/sdk/workflow"
)

type EmploymentCheck struct {
	*stages.CommonPreProcessStage
	*stages.CommonPostProcessStage
}

const EmploymentCheckMaxRetryCount = 3

func NewEmploymentCheck() *EmploymentCheck {
	return &EmploymentCheck{}
}

var _ stages.IStage = &EmploymentCheck{}

// nolint:dupl
func (e *EmploymentCheck) Perform(ctx workflow.Context, req *stages.PerformRequest) (*stages.PerformResponse, error) {
	lg := workflow.GetLogger(ctx)
	res := &stages.PerformResponse{
		LoanStep: req.Request.GetLoanStep(),
		LseFieldMasks: []palPb.LoanStepExecutionFieldMask{
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
		},
	}
	lseStatus := palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS
	retryCount := 0

	for lseStatus != palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS {
		actReq := providers.GetActivityRequest(req, req.LoanProgram)
		actRes := &palActivityPb.PalActivityResponse{}
		if retryCount >= EmploymentCheckMaxRetryCount {
			lg.Error(fmt.Sprintf("employment check retry exhausted. RetryCount: %v, MaxRetryCount: %v", retryCount, EmploymentCheckMaxRetryCount))
			res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED
			res.GetLoanStep().SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_MANUAL_CHECK_FAILED
			res.LseFieldMasks = append(res.LseFieldMasks,
				palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
			)
			break
		}
		retryCount += 1

		actRes, actErr := providers.PerformActionInSyncV2(ctx, &providers.SyncPolledStageRequestV2{
			ActivityName:           palNs.SgInitiateEmploymentCheck,
			ActivityRequest:        actReq,
			ExecuteAsyncInParallel: true,
			StageRequest:           req,
			TimeoutDuration:        72 * time.Hour,
		})
		if providers.IsActivityError(ctx, actRes.GetLoanStep(), actRes.GetLseFieldMasks(), res, actErr, true) {
			return res, actErr
		}
		if actRes.GetLoanStep().GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS {
			return res, nil
		}

		actRes, actErr = providers.PerformActionInSyncV2(ctx, &providers.SyncPolledStageRequestV2{
			ActivityName:           palNs.SgEmploymentCheckStatus,
			ActivityRequest:        actReq,
			ExecuteAsyncInParallel: true,
			StageRequest:           req,
			TimeoutDuration:        72 * time.Hour,
		})
		if providers.IsActivityError(ctx, actRes.GetLoanStep(), actRes.GetLseFieldMasks(), res, actErr, true) {
			lg.Error("Failed to execute SgEmploymentCheckStatus", zap.Error(actErr), zap.String(logger.STATUS, actRes.GetLoanStep().GetStatus().String()), zap.String(logger.SUB_STATUS, actRes.GetLoanStep().GetSubStatus().String()))
		}
		lseStatus = actRes.GetLoanStep().GetStatus()
	}
	return res, nil
}

func (e *EmploymentCheck) GetName() palPb.LoanStepExecutionStepName {
	return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_EMPLOYMENT_CHECK
}

func (e *EmploymentCheck) GetCelestialStage() epifitemporal.Stage {
	return palNs.StageEmploymentCheck
}
