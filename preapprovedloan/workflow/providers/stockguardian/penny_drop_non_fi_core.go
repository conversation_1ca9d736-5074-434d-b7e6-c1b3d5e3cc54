// nolint:dupl
package stockguardian

import (
	"fmt"
	"time"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
	emptyPb "google.golang.org/protobuf/types/known/emptypb"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	palNamespace "github.com/epifi/gamma/preapprovedloan/workflow/namespace"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"
)

type PennyDropNonFiCore struct {
	*stages.CommonPreProcessStage
	*stages.CommonPostProcessStage
}

func NewPennyDropNonFiCore() *PennyDropNonFiCore {
	return &PennyDropNonFiCore{}
}

var _ stages.IStage = &PennyDropNonFiCore{}

const (
	maxAddBankDetailsWaitTimeHrs = 120
	maxAddBankingDetailsRetries  = 3
)

func (ca *PennyDropNonFiCore) Perform(ctx workflow.Context, req *stages.PerformRequest) (*stages.PerformResponse, error) {
	res := &stages.PerformResponse{
		LoanStep: req.Request.GetLoanStep(),
	}

	actRes := &palActivityPb.PalActivityResponse{}
	retryNumber := 1
	for retryNumber <= maxAddBankingDetailsRetries {
		updatedLseDetails := actRes.GetLoanStep().GetDetails()
		if updatedLseDetails.GetOnboardingData().GetBankingDetails() != nil {
			updatedLseDetails.GetOnboardingData().BankingDetails = nil
		}

		updateReq := &palActivityPb.UpdateLoanStepExecutionRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
				Ownership:   epificontext.OwnershipFromContext(ctx),
			},
			LoanStep: &palPb.LoanStepExecution{
				Id:      req.Request.GetLoanStep().GetId(),
				Details: updatedLseDetails,
			},
			LseFieldMasks: []palPb.LoanStepExecutionFieldMask{
				palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS,
			},
		}
		updateRes := &palActivityPb.PalActivityResponse{}
		localActVer := workflow.GetVersion(ctx, "move-lightweight-activities-to-local", workflow.DefaultVersion, 1)
		if localActVer != 1 {
			err := activityPkg.Execute(ctx, palNs.UpdateLoanStepExecution, updateRes, updateReq)
			if providers.IsActivityError(ctx, updateRes.GetLoanStep(), nil, res, err, false) {
				return res, err
			}
		} else {
			err := activityPkg.ExecuteLocally(ctx, palNs.UpdateLoanStepExecution, updateRes, updateReq)
			if providers.IsActivityError(ctx, updateRes.GetLoanStep(), nil, res, err, false) {
				return res, err
			}
		}

		nextAction := &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_PL_BANKING_DETAILS_SCREEN,
		}

		updateLrReq := &palActivityPb.UpdateLRNextActionRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
				Ownership:   epificontext.OwnershipFromContext(ctx),
			},
			LoanRequestId: req.Request.GetLoanStep().GetRefId(),
			NextAction:    nextAction,
			LoanStep:      req.Request.GetLoanStep(),
		}

		updateLrRes := &palActivityPb.PalActivityResponse{}
		if localActVer != 1 {
			updateErr := activityPkg.Execute(ctx, palNs.UpdateLRNextAction, updateLrRes, updateLrReq)
			if providers.IsActivityError(ctx, updateLrRes.GetLoanStep(), nil, res, updateErr, false) {
				return res, updateErr
			}
		} else {
			updateErr := activityPkg.ExecuteLocally(ctx, palNs.UpdateLRNextAction, updateLrRes, updateLrReq)
			if providers.IsActivityError(ctx, updateLrRes.GetLoanStep(), nil, res, updateErr, false) {
				return res, updateErr
			}
		}

		bankErr := ca.updateBankingDetailsAtVendor(ctx, req, res)
		if bankErr != nil {
			if res.GetLoanStep().GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED && retryNumber < maxAddBankingDetailsRetries {
				retryNumber++
				continue
			}
			return res, bankErr
		}

		actReq := providers.GetActivityRequest(req, req.LoanProgram)
		actErr := activityPkg.Execute(ctx, palNs.SgInitiatePennyDrop, actRes, actReq)
		if providers.IsActivityError(ctx, actRes.GetLoanStep(), actRes.GetLseFieldMasks(), res, actErr, false) {
			return res, actErr
		}

		actReq = providers.GetActivityRequest(req, req.LoanProgram)
		actRes = &palActivityPb.PalActivityResponse{}
		actErr = activityPkg.Execute(ctx, palNs.SgPennyDropStatus, actRes, actReq)
		if providers.IsActivityError(ctx, actRes.GetLoanStep(), actRes.GetLseFieldMasks(), res, actErr, false) {
			if res.GetLoanStep().GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED && retryNumber < maxAddBankingDetailsRetries {
				retryNumber++
				continue
			}
			return res, actErr
		}

		break
	}

	res.LoanStep = actRes.GetLoanStep()
	res.LseFieldMasks = actRes.GetLseFieldMasks()
	return res, nil
}

func (ca *PennyDropNonFiCore) GetName() palPb.LoanStepExecutionStepName {
	return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PENNY_DROP
}

func (ca *PennyDropNonFiCore) GetCelestialStage() epifitemporal.Stage {
	return palNs.StagePennyDrop
}

func (ca *PennyDropNonFiCore) updateBankingDetailsAtVendor(ctx workflow.Context, req *stages.PerformRequest, res *stages.PerformResponse) error {
	actReq := &palActivityPb.PalActivityRequest{
		LoanStep:    req.Request.GetLoanStep(),
		Vendor:      req.Vendor,
		LoanProgram: req.LoanProgram,
	}
	actRes := &palActivityPb.PalActivityResponse{}

	bankingConfirmationFuture, err := activityPkg.ExecuteAsync(ctx, palNs.GetBankingStatus, actReq, actRes)
	if err != nil {
		return fmt.Errorf("failed to execute GetBankingStatus activity: %w", err)
	}

	var errorToReturn error
	bankingConfirmationFuture.AddFutureHandler(func(futureGetErr error, resp *palActivityPb.PalActivityResponse) {
		if futureGetErr != nil {
			workflow.GetLogger(ctx).Info("banking details confirmation future finished with error", zap.Error(futureGetErr))
			errorToReturn = futureGetErr
		}
	})

	signalPayload := &emptyPb.Empty{}
	sigChannel := epifitemporal.NewSignalChannel(ctx, palNs.LoanApplicationBankingDetailsSignal, signalPayload)
	sigChannel.AddReceiverHandler(func(getErr error, payload *emptyPb.Empty) {
		workflow.GetLogger(ctx).Info("LoanApplicationBankingDetailsSignal signal returned first")
		if getErr != nil {
			workflow.GetLogger(ctx).Info("LoanApplicationBankingDetailsSignal signal processing failed", zap.Error(getErr))
			errorToReturn = getErr
		}
	})

	err = epifitemporal.ReceiveSignalWithFuture(ctx, bankingConfirmationFuture, sigChannel, maxAddBankDetailsWaitTimeHrs*time.Hour)
	if err != nil {
		workflow.GetLogger(ctx).Info("LoanApplicationBankingDetailsSignal receive signal with future processing failed", zap.Error(err))
		return err
	}

	if errorToReturn != nil {
		if epifitemporal.IsRetryableError(errorToReturn) {
			res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED
		}
		return errorToReturn
	}

	actReq = &palActivityPb.PalActivityRequest{
		LoanStep:    req.Request.GetLoanStep(),
		Vendor:      req.Vendor,
		LoanProgram: req.LoanProgram,
	}
	actRes = &palActivityPb.PalActivityResponse{}
	// Step3: execute the add banking details activity
	actErr := activityPkg.Execute(ctx, palNamespace.UpdateBankingDetailsAtSg, actRes, actReq)
	res.LseFieldMasks = actRes.GetLseFieldMasks()
	if providers.IsActivityError(ctx, actRes.GetLoanStep(), nil, res, actErr, false) {
		return actErr
	}
	return nil
}
