package providers

import (
	"time"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	"github.com/epifi/be-common/pkg/logger"
	palNsGamma "github.com/epifi/gamma/preapprovedloan/workflow/namespace"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palFeEnumsPb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	palWfPb "github.com/epifi/gamma/api/preapprovedloan/workflow"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
	"github.com/epifi/gamma/preapprovedloan/workflow/proxy"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"
)

// ISyncActivityResponse needs to be implemented by any activity that needs to be executed within syncProxy wrapper
// activity that needs to be executed in sync needs to have the following fields:
// 1. IsSyncTaskDone : to check if we want to execute this activity in sync again or not, basically if task is compete(in terminal state), we
// will return this as false so that workflow proceeds with the further execution
// 2. NextAction : next action that needs to be given to client for navigation
type ISyncActivityResponse interface {
	GetIsSyncTaskDone() bool
	GetNextAction() *deeplinkPb.Deeplink
	proto.Message
}

// ISyncActivityRequest needs to be implemented by an activity request type to consume the payload coming with the signal triggering the activity.
// In the V0 version we have not mandated the "PayloadData" field in the activity request. so support is added to run the activity with both the request types i.e.
// Request and ISyncActivityRequest
// It is expected to use ISyncActivityRequest from now whether signal payload is needed or not.
type ISyncActivityRequest interface {
	SetSignalPayload(data *palWfPb.CommonData)
	GetPayloadData() *palWfPb.CommonData
	proto.Message
}

type ActivityDetails struct {
	Name                   epifitemporal.Activity
	Request, AsyncResponse proto.Message
	SyncResponse           ISyncActivityResponse
	SyncRequest            ISyncActivityRequest
}

type SyncPolledStageRequest struct {
	SyncActivityDetails *ActivityDetails
	// AsyncActivityDetails to be passed if caller wants to run an async activity in parallel as well, along with ExecuteAsyncInParallel flag
	// if this is not nil, main workflow will wait on a async future along with signal from sync proxy workflow (on-demand action)
	// this will allow to complete the task in background and not depend solely on on-demand action call
	AsyncActivityDetails   *ActivityDetails
	ExecuteAsyncInParallel bool
	StageName              palPb.LoanStepExecutionStepName
	WfProcessingParams     *workflowPb.ProcessingParams
	LoanRequestId          string
	Vendor                 palPb.Vendor
	LoanProgram            palPb.LoanProgram
	TimeoutDurationInMins  int64
}

// IsActivityError can be used for general stage implementation, after every activity.
// Need to add loan step from activity response in loan step, res as initialized initially in perform and activity error in err
// if activity doesn't response with a loan step, using loan step from perform request
// error not nil represents manual intervention state and logging
// if business logic failure or cancelled status, treating it also as error.
func IsActivityError(ctx workflow.Context, loanStep *palPb.LoanStepExecution, lseUpdateFieldMask []palPb.LoanStepExecutionFieldMask, res *stages.PerformResponse, err error, markNonRetryableAsExpired bool) bool {
	if loanStep != nil {
		res.LoanStep = loanStep
		if v := workflow.GetVersion(ctx, "lse-field-mask-update-from-isActivityError-function", workflow.DefaultVersion, 1); v == 1 {
			res.LseFieldMasks = lo.Uniq(append(res.LseFieldMasks, lseUpdateFieldMask...))
		}
	}
	if err != nil && loanStep == nil && res.GetLoanStep() != nil {
		// if error is retryable ( e.g. in case retries get exhausted ), and if stage wants to mark it as expired, set the relevant LSE status
		// if error is non-retryable, mark the status of LSE as failed
		// in case of activity retry exhaustion, activity will return with transient error and activity response will be nil
		if epifitemporal.IsRetryableError(err) && markNonRetryableAsExpired {
			res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED
		} else if !epifitemporal.IsRetryableError(err) {
			res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED
		}
	}
	if err != nil {
		return true
	}
	if loanStep.GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED ||
		loanStep.GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CANCELLED {
		return true
	}
	return false
}

func IsActivityErrorV2(ctx workflow.Context, res *stages.PerformResponse, err error, params *ActivityErrParams) bool {
	fillResFromParams(ctx, res, params)
	if params.GetLse().GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED ||
		params.GetLse().GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CANCELLED ||
		params.GetLse().GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED ||
		params.GetLse().GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION {
		return true
	}
	v := workflow.GetVersion(ctx, "activity-error-v2-refactor", workflow.DefaultVersion, 2)
	if v >= 2 {
		// we can only update the loan step status if it's present in res and not present in params.
		canUpdateLseStatus := res.GetLoanStep() != nil && params.GetLse() == nil
		if err != nil && canUpdateLseStatus {
			if epifitemporal.IsRetryableError(err) {
				// for retryable errors, only update the status to EXPIRED if MarkRetryableErrsAsFailed param is set to true.
				// otherwise workflow should move to MANUAL_INTERVENTION state.
				if params.GetMarkRetryableErrsAsFailed() {
					res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED
				}
			} else {
				res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED
			}
		}
	} else {
		// mark failed for all error cases
		markFailed := err != nil && res.GetLoanStep() != nil && params.GetLse() == nil
		// if flag is false, exclude retryable failures
		if !params.GetMarkRetryableErrsAsFailed() {
			markFailed = markFailed && !epifitemporal.IsRetryableError(err)
		}
		if markFailed {
			res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED
		}
	}
	if err != nil {
		return true
	}
	return false
}

func fillResFromParams(_ workflow.Context, res *stages.PerformResponse, params *ActivityErrParams) {
	if params.GetLse() != nil {
		res.LoanStep = params.GetLse()
	}
	if params.GetNextAction() != nil {
		res.NextAction = params.GetNextAction()
	}
	res.LseFieldMasks = params.GetLseFieldMask()
}

// PerformActionInSync allows sync execution of an activity through sync proxy workflow.
// This will make the workflow wait on a syncProxy signal, and a timeout for the waiting.
// Optionally we can also pass details for additional activity that we want to execute in Async fashion.
// Ideally additional async activity should only be performed in case task can be completed in background
// without waiting on user action, in such cases, async activity can update the status if the loan step.
func PerformActionInSync(ctx workflow.Context, req *SyncPolledStageRequest) error {
	lg := workflow.GetLogger(ctx)
	if req == nil || req.SyncActivityDetails == nil {
		lg.Error("incorrect req params for performing action in sync")
		return errors.New("request or syncRequest Params empty for perform action in sync")
	}
	for {
		var actErr error
		var asyncExecutionSuccess bool

		signalPayload := palWfPb.ProxyRequestPayload{}
		sigChannel := epifitemporal.NewSignalChannel(ctx, palNs.LoansProxyRequestSignal, &signalPayload)
		sigChannel.AddReceiverHandler(func(getErr error, payload *palWfPb.ProxyRequestPayload) {
			if req.SyncActivityDetails.SyncRequest != nil {
				req.SyncActivityDetails.SyncRequest.SetSignalPayload(signalPayload.GetPayloadData())
			}
			lg.Info("sync proxy workflow signal received")
		})

		// if async activity needs to be executed in parallel, start the async execution and wait on the future along with signal and timer.
		// in case aync execution is not needed, wait on signal and timer alone
		if req.ExecuteAsyncInParallel {
			// start the async activity execution if flag in the request is set to true
			asyncFuture, asyncExecErr := activityPkg.ExecuteAsync(ctx, req.AsyncActivityDetails.Name, req.AsyncActivityDetails.Request, req.AsyncActivityDetails.AsyncResponse)
			if asyncExecErr != nil {
				lg.Error("failed to execute Async activity in sync stage, Activity:%v, Err:%w", req.AsyncActivityDetails.Name, asyncExecErr)
				return asyncExecErr
			}
			asyncFuture.AddFutureHandler(func(futureGetErr error, resp proto.Message) {
				if futureGetErr != nil {
					lg.Info("async future finished with error:", zap.Error(futureGetErr))
					actErr = futureGetErr
				}
				asyncExecutionSuccess = true
			})
			err := epifitemporal.ReceiveSignalWithFuture(ctx, asyncFuture, sigChannel, time.Duration(req.TimeoutDurationInMins)*time.Minute)
			if err != nil {
				lg.Error("error in sync execution of the stage", zap.String(logger.STAGE, req.StageName.String()), zap.Error(err))
				return err
			}
		} else {
			err := epifitemporal.ReceiveSignal(ctx, sigChannel, time.Duration(req.TimeoutDurationInMins)*time.Minute)
			if err != nil {
				lg.Error("sync proxy signal receive wait timed out", zap.String(logger.STAGE, req.StageName.String()), zap.Error(err))
				return err
			}
		}

		// async execution got success, we do not need to wait for sync proxy signal, return from here
		if asyncExecutionSuccess {
			if actErr != nil {
				return actErr
			}
			break
		}

		err := activityPkg.ExecuteLocally(ctx, req.SyncActivityDetails.Name, req.SyncActivityDetails.SyncResponse, req.SyncActivityDetails.SyncRequest)
		if err != nil {
			lg.Error("error executing activity of stage", zap.String(logger.STAGE, req.StageName.String()), zap.Error(err), zap.String(logger.ACTIVITY, string(req.SyncActivityDetails.Name)))
			err2 := proxy.SendResponse(ctx, signalPayload.GetCallingWorkflowId(), &palWfPb.ProxyResponsePayload{
				Error: err.Error(),
			})
			if err2 != nil {
				lg.Error("error in sending error response to proxy-workflow", zap.String(logger.STAGE, req.StageName.String()), zap.Error(err2))
			}
			continue
		}

		err = proxy.SendResponse(ctx, signalPayload.GetCallingWorkflowId(), &palWfPb.ProxyResponsePayload{
			NextAction: req.SyncActivityDetails.SyncResponse.GetNextAction(),
		})
		if err != nil {
			lg.Error("error in sending response to proxy-workflow", zap.String(logger.STAGE, req.StageName.String()))
			continue
		}

		// sync activity executed successfully with IsSyncTaskDone as true, return from here
		if req.SyncActivityDetails.SyncResponse.GetIsSyncTaskDone() {
			break
		}
		continue
	}
	return nil
}

type SyncPolledStageRequestV2 struct {
	ActivityName    epifitemporal.Activity
	ActivityRequest *palActivityPb.PalActivityRequest
	// ActivityName will be run as async activity in parallel as well if ExecuteAsyncInParallel is true
	// and main workflow will wait on async future along with signal from sync proxy workflow (on-demand action)
	// this will allow to complete the task in background and not depend solely on on-demand action call
	ExecuteAsyncInParallel bool
	StageRequest           *stages.PerformRequest
	TimeoutDuration        time.Duration
}

// nolint:funlen
// PerformActionInSyncV2 executes the provided activity in async and waits for sync proxy signal.
// The same activity is then run as a regular function inside ExecuteInSync upon receiving signal from syncproxy workflow and the response is signaled back to syncproxy workflow
// This allows us to use the same Activity implementation to run in sync (on demand) and async (polling based on retry strategy).
// We are returning a response pointer from this function because the response can either be from async or sync activity execution.
// NOTE: Activity implementation should return the appropriate screen to be shown while the task is in progress along with transient error.
//
// NOTE 2: Currently this only supports activities which use PalActivityRequest and PalActivityResponse,
// this is because we have to know the type to request and response to be able to call the activity as a regular function inside ExecuteInSync.
// One way to extend this to support other types is to write the activities with abstract request and response type and cast to a concrete type within
func PerformActionInSyncV2(ctx workflow.Context, req *SyncPolledStageRequestV2) (*palActivityPb.PalActivityResponse, error) {
	lg := workflow.GetLogger(ctx)
	if req == nil {
		return nil, errors.New("request or syncRequest Params empty for perform action in sync")
	}

	var err error
	// we are using different response pointers for sync and async activity execution to avoid any race condition
	// retRes will be assigned the response of the activity execution that finishes first
	var retRes *palActivityPb.PalActivityResponse

	err = setSyncStatus(ctx, req.ActivityRequest.GetLoanStep().GetRefId(), req.ActivityRequest.GetVendor(), true)
	if err != nil {
		return nil, errors.Wrap(err, "error in setting sync status")
	}

	for {
		signalPayload := palWfPb.ProxyRequestPayload{}
		sigChannel := epifitemporal.NewSignalChannel(ctx, palNs.LoansProxyRequestSignal, &signalPayload)
		sigChannel.AddReceiverHandler(func(getErr error, payload *palWfPb.ProxyRequestPayload) {
			req.ActivityRequest.SetSignalPayload(signalPayload.GetPayloadData())
		})

		var asyncActErr error
		var asyncExecutionSuccess bool
		// if async activity needs to be executed in parallel, start the async execution and wait on the future along with signal and timer.
		// in case async execution is not needed, wait on signal and timer alone
		if req.ExecuteAsyncInParallel {
			asyncRes := &palActivityPb.PalActivityResponse{}
			asyncFuture, asyncExecErr := activityPkg.ExecuteAsync(ctx, req.ActivityName, req.ActivityRequest, asyncRes)
			if asyncExecErr != nil {
				return nil, errors.Wrap(asyncExecErr, "failed to execute Async activity in sync stage")
			}
			asyncFuture.AddFutureHandler(func(futureGetErr error, resp *palActivityPb.PalActivityResponse) {
				lg.Info("syncproxy async future finished", zap.String(logger.ACTIVITY, req.ActivityName.String()))
				asyncActErr = futureGetErr
				asyncExecutionSuccess = true
				retRes = asyncRes
			})
			err = epifitemporal.ReceiveSignalWithFuture(ctx, asyncFuture, sigChannel, req.TimeoutDuration)
			if err != nil {
				return nil, errors.Wrap(err, "error in ReceiveSignalWithFuture")
			}
		} else {
			err = epifitemporal.ReceiveSignal(ctx, sigChannel, req.TimeoutDuration)
			if err != nil {
				if epifitemporal.HasSignalReceivedTimedOut(err) {
					return nil, errors.Wrap(epifitemporal.NewTransientError(err), "error in ReceiveSignalWithTimedOut")
				}
				return nil, errors.Wrap(err, "error in ReceiveSignal")
			}
		}

		// async execution got success, we do not need to wait for sync proxy signal, return from here
		if asyncExecutionSuccess {
			if asyncActErr != nil {
				return nil, asyncActErr
			}
			break
		}

		syncRes := &palActivityPb.PalActivityResponse{}
		req.ActivityRequest.SyncActivityName = req.ActivityName.String()
		// we will use the same request to run the async activity as a regular function inside ExecuteInSync
		// we should not return error if the sync activity execution fails, as the async activity still needs to be executed in parallel
		err = activityPkg.ExecuteLocally(ctx, palNs.ExecuteInSync, syncRes, req.ActivityRequest)
		if err != nil {
			lg.Error("error executing ExecuteInSync of stage", zap.String(logger.STAGE, req.StageRequest.Request.GetLoanStep().GetStepName().String()), zap.Error(err))
			err2 := proxy.SendResponse(ctx, signalPayload.GetCallingWorkflowId(), &palWfPb.ProxyResponsePayload{
				Error: err.Error(),
			})
			if err2 != nil {
				lg.Error("error in sending error response to proxy-workflow", zap.String(logger.STAGE, req.StageRequest.Request.GetLoanStep().GetStepName().String()), zap.Error(err2))
			}
			continue
		}

		err = proxy.SendResponse(ctx, signalPayload.GetCallingWorkflowId(), &palWfPb.ProxyResponsePayload{
			NextAction: syncRes.GetNextAction(),
		})
		if err != nil {
			lg.Error("error in sending response to proxy-workflow", zap.String(logger.STAGE, req.StageRequest.Request.GetLoanStep().GetStepName().String()))
			continue
		}

		if syncRes.GetIsSyncTaskDone() {
			lg.Info("syncproxy sync execution done", zap.String(logger.ACTIVITY, req.ActivityName.String()))
			retRes = syncRes
			break
		}
		continue
	}

	// TODO: If the next stage is also running in sync proxy mode, then we can avoid showing the polling screen by sending sync proxy signal again to the workflow from service layer
	err = setSyncStatus(ctx, req.ActivityRequest.GetLoanStep().GetRefId(), req.ActivityRequest.GetVendor(), false)
	if err != nil {
		return nil, errors.Wrap(err, "error in setting sync status")
	}

	// after this point, the workflow will not be waiting on sync proxy signal
	// if the next action in LR is the sync polling screen that we have set at the start, client will try to initiate a sync proxy workflow which will fail
	// that is why we are setting the next action to async polling screen in LR by default here
	err = setNextActionFromActResOrDefault(ctx, retRes, req.StageRequest)
	if err != nil {
		return nil, errors.Wrap(err, "error in setting next action")
	}

	return retRes, nil
}

func setSyncStatus(ctx workflow.Context, lrId string, vendor palPb.Vendor, isInSyncMode bool) error {
	syncUpdateVersion := workflow.GetVersion(ctx, "sync-update-version", workflow.DefaultVersion, 1)
	if syncUpdateVersion != 1 {
		return nil
	}
	syncUpdateReq := &palActivityPb.UpdateSyncStatusRequest{
		LrId:         lrId,
		Vendor:       vendor,
		IsInSyncMode: isInSyncMode,
	}
	syncUpdateRes := &palActivityPb.UpdateSyncStatusResponse{}
	localActVer := workflow.GetVersion(ctx, "move-lightweight-activities-to-local", workflow.DefaultVersion, 1)
	if localActVer != 1 {
		err := activityPkg.Execute(ctx, palNsGamma.UpdateSyncStatus, syncUpdateRes, syncUpdateReq)
		if err != nil {
			return errors.Wrap(err, "error in UpdateSyncStatus activity")
		}
	} else {
		err := activityPkg.ExecuteLocally(ctx, palNsGamma.UpdateSyncStatus, syncUpdateRes, syncUpdateReq)
		if err != nil {
			return errors.Wrap(err, "error in UpdateSyncStatus activity")
		}
	}
	return nil
}

func setNextActionFromActResOrDefault(ctx workflow.Context, res *palActivityPb.PalActivityResponse, req *stages.PerformRequest) error {
	var nextAction *deeplinkPb.Deeplink
	if res.GetNextAction() != nil {
		nextAction = res.GetNextAction()
	} else {
		nextAction = deeplink.GetApplicationStatusPollDeeplinkWithParams(&palFeEnumsPb.LoanHeader{
			LoanProgram: deeplink.GetFeLoanProgramFromBe(req.LoanProgram),
			Vendor:      deeplink.GetPalFeVendorFromBe(req.Vendor),
		}, req.Request.GetLoanStep().GetRefId(), nil)
	}
	updateReq := &palActivityPb.UpdateLRNextActionRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
			Ownership:   epificontext.OwnershipFromContext(ctx),
		},
		LoanRequestId: req.Request.GetLoanStep().GetRefId(),
		NextAction:    nextAction,
	}

	updateRes := &palActivityPb.PalActivityResponse{}
	localActVer := workflow.GetVersion(ctx, "move-lightweight-activities-to-local", workflow.DefaultVersion, 1)
	if localActVer != 1 {
		return activityPkg.Execute(ctx, palNs.UpdateLRNextAction, updateRes, updateReq)
	} else {
		return activityPkg.ExecuteLocally(ctx, palNs.UpdateLRNextAction, updateRes, updateReq)
	}
}

// GetActivityRequest will help in getting the common type of request for activities
func GetActivityRequest(req *stages.PerformRequest, loanProgram palPb.LoanProgram) *palActivityPb.PalActivityRequest {
	return &palActivityPb.PalActivityRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.Request.GetLoanStep().GetOrchId(),
		},
		LoanStep:    req.Request.GetLoanStep(),
		Vendor:      req.Vendor,
		LoanProgram: loanProgram,
	}
}

func CheckToSkipStage(lse *palPb.LoanStepExecution) bool {
	return lse != nil &&
		lse.GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS &&
		lse.GetSubStatus() == palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_SKIPPED
}
