//nolint:funlen,dupl
package filite

import (
	"fmt"
	"time"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
	emptyPb "google.golang.org/protobuf/types/known/emptypb"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	preApprovedLoanNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"
)

// BankingDetails represents the stage which needs a user to enter details for bank
type BankingDetails struct {
	*stages.CommonPreProcessStage
	*stages.CommonPostProcessStage
}

func NewBankingDetails() *BankingDetails {
	return &BankingDetails{}
}

var _ stages.IStage = &BankingDetails{}

// nolint: funlen
func (ca *BankingDetails) Perform(ctx workflow.Context, req *stages.PerformRequest) (*stages.PerformResponse, error) {
	res := &stages.PerformResponse{
		LoanStep: req.Request.GetLoanStep(),
	}

	nextAction := &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_PL_BANKING_DETAILS_SCREEN,
	}

	updateReq := &palActivityPb.UpdateLRNextActionRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
			Ownership:   epificontext.OwnershipFromContext(ctx),
		},
		LoanRequestId: req.Request.GetLoanStep().GetRefId(),
		NextAction:    nextAction,
	}

	updateRes := &palActivityPb.PalActivityResponse{}
	localActVer := workflow.GetVersion(ctx, "move-lightweight-activities-to-local", workflow.DefaultVersion, 1)
	if localActVer != 1 {
		updateErr := activityPkg.Execute(ctx, preApprovedLoanNs.UpdateLRNextAction, updateRes, updateReq)
		if providers.IsActivityError(ctx, updateRes.GetLoanStep(), nil, res, updateErr, false) {
			return res, updateErr
		}
	} else {
		updateErr := activityPkg.ExecuteLocally(ctx, preApprovedLoanNs.UpdateLRNextAction, updateRes, updateReq)
		if providers.IsActivityError(ctx, updateRes.GetLoanStep(), nil, res, updateErr, false) {
			return res, updateErr
		}
	}

	bankErr := ca.performAddBankingDetails(ctx, req, res)
	if bankErr != nil {
		return res, bankErr
	}

	return res, nil
}

func (ca *BankingDetails) GetName() palPb.LoanStepExecutionStepName {
	return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_BANKING_DETAILS
}

func (ca *BankingDetails) GetCelestialStage() epifitemporal.Stage {
	return palNs.StageBankingDetails
}

func (ca *BankingDetails) performAddBankingDetails(ctx workflow.Context, req *stages.PerformRequest, res *stages.PerformResponse) error {
	actReq := &palActivityPb.PalActivityRequest{
		LoanStep:    req.Request.GetLoanStep(),
		Vendor:      req.Vendor,
		LoanProgram: req.LoanProgram,
	}
	actRes := &palActivityPb.PalActivityResponse{}

	bankingConfirmationFuture, err := activityPkg.ExecuteAsync(ctx, preApprovedLoanNs.GetBankingStatus, actReq, actRes)
	if err != nil {
		return fmt.Errorf("failed to execute GetBankingStatus activity: %w", err)
	}

	var errorToReturn error
	bankingConfirmationFuture.AddFutureHandler(func(futureGetErr error, resp *palActivityPb.PalActivityResponse) {
		if futureGetErr != nil {
			workflow.GetLogger(ctx).Info("banking details confirmation future finished with error", zap.Error(futureGetErr))
			errorToReturn = futureGetErr
		}
	})

	signalPayload := &emptyPb.Empty{}
	sigChannel := epifitemporal.NewSignalChannel(ctx, palNs.LoanApplicationBankingDetailsSignal, signalPayload)
	sigChannel.AddReceiverHandler(func(getErr error, payload *emptyPb.Empty) {
		workflow.GetLogger(ctx).Info("LoanApplicationDetailsVerificationSignal signal returned first")
		if getErr != nil {
			workflow.GetLogger(ctx).Info("LoanApplicationDetailsVerificationSignal signal processing failed", zap.Error(getErr))
			errorToReturn = getErr
		}
	})

	err = epifitemporal.ReceiveSignalWithFuture(ctx, bankingConfirmationFuture, sigChannel, maxAddDetailsWaitTimeHrs*time.Hour)
	if err != nil {
		workflow.GetLogger(ctx).Info("LoanApplicationDetailsVerificationSignal receive signal with future processing failed", zap.Error(err))
		return err
	}

	if errorToReturn != nil {
		if epifitemporal.IsRetryableError(errorToReturn) {
			res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED
		}
		return errorToReturn
	}

	actReq = &palActivityPb.PalActivityRequest{
		LoanStep:    req.Request.GetLoanStep(),
		Vendor:      req.Vendor,
		LoanProgram: req.LoanProgram,
	}
	actRes = &palActivityPb.PalActivityResponse{}
	// Step3: execute the add banking details activity
	actErr := activityPkg.Execute(ctx, preApprovedLoanNs.AddBankingDetails, actRes, actReq)
	res.LseFieldMasks = actRes.GetLseFieldMasks()
	if providers.IsActivityError(ctx, actRes.GetLoanStep(), nil, res, actErr, false) {
		return actErr
	}
	return nil
}
