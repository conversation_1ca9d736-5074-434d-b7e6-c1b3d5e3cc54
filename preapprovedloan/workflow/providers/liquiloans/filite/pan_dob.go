package filite

import (
	"time"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
	emptyPb "google.golang.org/protobuf/types/known/emptypb"
)

const (
	maxWaitTime = 120
)

// PanDob represents the stage which creates a loan applicant with Liquiloans and at Fi.
type PanDob struct {
	*stages.CommonPreProcessStage
	*stages.CommonPostProcessStage
}

func NewPanDob() *PanDob {
	return &PanDob{}
}

var _ stages.IStage = &PanDob{}

func (ca *PanDob) Perform(ctx workflow.Context, req *stages.PerformRequest) (*stages.PerformResponse, error) {
	res := &stages.PerformResponse{
		LoanStep: req.Request.GetLoanStep(),
	}

	// Step1: execute Pan-Dob check activity to check if user data is present in db
	actReq := &palActivityPb.PalActivityRequest{
		LoanStep:    req.Request.GetLoanStep(),
		Vendor:      req.Vendor,
		LoanProgram: req.LoanProgram,
	}
	actRes := &palActivityPb.PalActivityResponse{}
	actErr := activityPkg.Execute(ctx, palNs.PanDobCheck, actRes, actReq)
	if providers.IsActivityError(ctx, actRes.GetLoanStep(), nil, res, actErr, false) {
		return res, actErr
	}

	// Step2: execute the AddPanDobStatus in async to poll the status of user input and wait for the signal as well.
	// once details are added by the user, set the next action to polling screen
	addDetailsErr := ca.checkPanDobStatus(ctx, req, res)
	if addDetailsErr != nil {
		return res, addDetailsErr
	}

	res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
	return res, nil
}

func (ca *PanDob) GetName() palPb.LoanStepExecutionStepName {
	return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PAN_AND_DOB
}

func (ca *PanDob) GetCelestialStage() epifitemporal.Stage {
	return palNs.StagePanAndDob
}

// nolint:funlen
func (ca *PanDob) checkPanDobStatus(ctx workflow.Context, req *stages.PerformRequest, res *stages.PerformResponse) error {
	actReq := &palActivityPb.PalActivityRequest{
		LoanStep:    req.Request.GetLoanStep(),
		Vendor:      req.Vendor,
		LoanProgram: req.LoanProgram,
	}
	actRes := &palActivityPb.PalActivityResponse{}
	addPanDobCheckFuture, err := activityPkg.ExecuteAsync(ctx, palNs.CheckPanDobStatus, actReq, actRes)
	if err != nil {
		return err
	}
	var errorToReturn error
	addPanDobCheckFuture.AddFutureHandler(func(addPanDobCheckErr error, actRes *palActivityPb.PalActivityResponse) {
		workflow.GetLogger(ctx).Info("addPanDobCheck future finished")
		if addPanDobCheckErr != nil {
			workflow.GetLogger(ctx).Error("IdfcPanStatus activity async processing failed", zap.Error(addPanDobCheckErr))
			errorToReturn = addPanDobCheckErr
		}
	})

	// signal channel waiting for a signal from BE service to get the Pan Dob Added status
	signalPayload := &emptyPb.Empty{}
	sigChannel := epifitemporal.NewSignalChannel(ctx, palNs.LoanEligibilityDetailsAddedSignal, signalPayload)
	wfVersion := workflow.GetVersion(ctx, "update-pan-dob-details-signal", workflow.DefaultVersion, 1)
	if wfVersion == 1 {
		sigChannel = epifitemporal.NewSignalChannel(ctx, palNs.LoanApplicationPanVerificationSignal, signalPayload)
	}
	sigChannel.AddReceiverHandler(func(getErr error, payload *emptyPb.Empty) {
		workflow.GetLogger(ctx).Info("LoanEligibilityDetailsAddedSignal signal returned first")
		if getErr != nil {
			workflow.GetLogger(ctx).Error("LoanEligibilityDetailsAddedSignal signal processing failed", zap.Error(getErr))
			errorToReturn = getErr
		}
	})

	err = epifitemporal.ReceiveSignalWithFuture(ctx, addPanDobCheckFuture, sigChannel, maxWaitTime*time.Hour)
	if err != nil {
		workflow.GetLogger(ctx).Error("LoanEligibilityDetailsAddedSignal signal processing failed", zap.Error(err))
		return err
	}
	if errorToReturn != nil {
		if epifitemporal.IsRetryableError(errorToReturn) {
			res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED
		}
		return errorToReturn
	}

	// set the next action to polling screen
	// TODO(Shivansh) check if next action needs to be updated.
	updateReq := &palActivityPb.UpdateLRNextActionRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
			Ownership:   epificontext.OwnershipFromContext(ctx),
		},
		LoanRequestId: req.Request.GetLoanStep().GetRefId(),
		NextAction: deeplink.GetApplicationStatusPollScreenDeepLink(req.Request.GetLoanStep().GetRefId(), deeplink.GetFeLoanHeaderByBeLoanHeader(&palPb.LoanHeader{
			LoanProgram: req.LoanProgram,
			Vendor:      req.Vendor,
		})),
	}

	updateRes := &palActivityPb.PalActivityResponse{}
	localActVer := workflow.GetVersion(ctx, "move-lightweight-activities-to-local", workflow.DefaultVersion, 1)
	if localActVer != 1 {
		updateErr := activityPkg.Execute(ctx, palNs.UpdateLRNextAction, updateRes, updateReq)
		if providers.IsActivityError(ctx, updateRes.GetLoanStep(), nil, res, updateErr, false) {
			workflow.GetLogger(ctx).Info("error in updating LR next action", zap.Error(updateErr))
			return updateErr
		}
	} else {
		updateErr := activityPkg.ExecuteLocally(ctx, palNs.UpdateLRNextAction, updateRes, updateReq)
		if providers.IsActivityError(ctx, updateRes.GetLoanStep(), nil, res, updateErr, false) {
			workflow.GetLogger(ctx).Info("error in updating LR next action", zap.Error(updateErr))
			return updateErr
		}
	}

	return nil
}
