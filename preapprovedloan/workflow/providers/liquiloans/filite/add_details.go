//nolint:funlen,dupl
package filite

import (
	"fmt"
	"time"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"
)

const (
	maxAddDetailsWaitTimeHrs = 120
)

// LlFlPlAddDetails represents the stage which needs a user to enter details for address and employment
type LlFlPlAddDetails struct {
	*stages.CommonPreProcessStage
	*stages.CommonPostProcessStage
}

func NewLlFlPlAddDetails() *LlFlPlAddDetails {
	return &LlFlPlAddDetails{}
}

var _ stages.IStage = &LlFlPlAddDetails{}

func (ca *LlFlPlAddDetails) Perform(ctx workflow.Context, req *stages.PerformRequest) (*stages.PerformResponse, error) {
	res := &stages.PerformResponse{
		LoanStep: req.Request.GetLoanStep(),
	}

	updateReq := &palActivityPb.UpdateLRNextActionRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
			Ownership:   epificontext.OwnershipFromContext(ctx),
		},
		LoanRequestId: req.Request.GetLoanStep().GetRefId(),
		NextAction: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_PRE_APPROVED_ADDRESS_CONFIRMATION_SCREEN,
		},
	}

	updateRes := &palActivityPb.PalActivityResponse{}
	localActVer := workflow.GetVersion(ctx, "move-lightweight-activities-to-local", workflow.DefaultVersion, 1)
	if localActVer != 1 {
		updateErr := activityPkg.Execute(ctx, palNs.UpdateLRNextAction, updateRes, updateReq)
		if providers.IsActivityError(ctx, updateRes.GetLoanStep(), nil, res, updateErr, false) {
			return res, updateErr
		}
	} else {
		updateErr := activityPkg.ExecuteLocally(ctx, palNs.UpdateLRNextAction, updateRes, updateReq)
		if providers.IsActivityError(ctx, updateRes.GetLoanStep(), nil, res, updateErr, false) {
			return res, updateErr
		}
	}

	addErr := ca.performAddAddressDetails(ctx, req)
	if addErr != nil {
		if epifitemporal.IsRetryableError(addErr) {
			res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED
		}
		return res, addErr
	}

	updateReq = &palActivityPb.UpdateLRNextActionRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
			Ownership:   epificontext.OwnershipFromContext(ctx),
		},
		LoanRequestId: req.Request.GetLoanStep().GetRefId(),
		NextAction: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_PRE_APPROVED_EMPLOYMENT_DETAILS_SCREEN,
		},
	}

	updateRes = &palActivityPb.PalActivityResponse{}
	if localActVer != 1 {
		updateErr := activityPkg.Execute(ctx, palNs.UpdateLRNextAction, updateRes, updateReq)
		if providers.IsActivityError(ctx, updateRes.GetLoanStep(), nil, res, updateErr, false) {
			return res, updateErr
		}
	} else {
		updateErr := activityPkg.ExecuteLocally(ctx, palNs.UpdateLRNextAction, updateRes, updateReq)
		if providers.IsActivityError(ctx, updateRes.GetLoanStep(), nil, res, updateErr, false) {
			return res, updateErr
		}
	}

	empErr := ca.performAddEmploymentDetails(ctx, req, res)
	if empErr != nil {
		if epifitemporal.IsRetryableError(empErr) {
			res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED
		}
		return res, empErr
	}

	res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
	return res, nil
}

func (ca *LlFlPlAddDetails) GetName() palPb.LoanStepExecutionStepName {
	return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS
}

func (ca *LlFlPlAddDetails) GetCelestialStage() epifitemporal.Stage {
	return palNs.StageAddDetails
}

func (ca *LlFlPlAddDetails) performAddAddressDetails(ctx workflow.Context, req *stages.PerformRequest) error {
	actReq := &palActivityPb.PalActivityRequest{
		LoanStep:    req.Request.GetLoanStep(),
		Vendor:      req.Vendor,
		LoanProgram: req.LoanProgram,
	}
	actRes := &palActivityPb.PalActivityResponse{}

	addressConfirmationFuture, err := activityPkg.ExecuteAsync(ctx, palNs.LLGetAddressStatus, actReq, actRes)
	if err != nil {
		return fmt.Errorf("failed to execute GetAddressStatus activity: %w", err)
	}

	var errorToReturn error
	addressConfirmationFuture.AddFutureHandler(func(futureGetErr error, resp *palActivityPb.PalActivityResponse) {
		if futureGetErr != nil {
			workflow.GetLogger(ctx).Info("address details confirmation future finished with error", zap.Error(futureGetErr))
			errorToReturn = futureGetErr
		}
	})
	signalPayload := &palActivityPb.LoanApplicationESignVerificationSignalPayload{}
	sigChannel := epifitemporal.NewSignalChannel(ctx, palNs.AddressConfirmationSignal, signalPayload)
	sigChannel.AddReceiverHandler(func(getErr error, payload *palActivityPb.LoanApplicationESignVerificationSignalPayload) {
		workflow.GetLogger(ctx).Info("AddressConfirmationSignal signal returned first")
		if getErr != nil {
			workflow.GetLogger(ctx).Info("AddressConfirmationSignal signal processing failed", zap.Error(getErr))
			errorToReturn = getErr
		}
	})

	err = epifitemporal.ReceiveSignalWithFuture(ctx, addressConfirmationFuture, sigChannel, maxAddDetailsWaitTimeHrs*time.Hour)
	if err != nil {
		workflow.GetLogger(ctx).Info("AddressConfirmationSignal receive signal with future processing failed", zap.Error(err))
		return err
	}
	if errorToReturn != nil {
		return errorToReturn
	}

	return nil
}

func (ca *LlFlPlAddDetails) performAddEmploymentDetails(ctx workflow.Context, req *stages.PerformRequest, res *stages.PerformResponse) error {
	actReq := &palActivityPb.PalActivityRequest{
		LoanStep:    req.Request.GetLoanStep(),
		Vendor:      req.Vendor,
		LoanProgram: req.LoanProgram,
	}
	actRes := &palActivityPb.PalActivityResponse{}
	employmentDetailsConfirmationFuture, err := activityPkg.ExecuteAsync(ctx, palNs.LLGetEmploymentStatus, actReq, actRes)
	if err != nil {
		return fmt.Errorf("failed to execute GetAddressStatus activity: %w", err)
	}

	var errorToReturn error
	employmentDetailsConfirmationFuture.AddFutureHandler(func(futureGetErr error, resp *palActivityPb.PalActivityResponse) {
		workflow.GetLogger(ctx).Info("employment details confirmation future finished")
		if futureGetErr != nil {
			workflow.GetLogger(ctx).Info("employment details confirmation future finished with error", zap.Error(futureGetErr))
			errorToReturn = futureGetErr
		}
	})
	signalPayload := &palActivityPb.LoanApplicationESignVerificationSignalPayload{}
	sigChannel := epifitemporal.NewSignalChannel(ctx, palNs.EmploymentConfirmationSignal, signalPayload)
	sigChannel.AddReceiverHandler(func(getErr error, payload *palActivityPb.LoanApplicationESignVerificationSignalPayload) {
		workflow.GetLogger(ctx).Info("EmploymentConfirmationSignal signal returned first")
		if getErr != nil {
			workflow.GetLogger(ctx).Info("EmploymentConfirmationSignal signal processing failed", zap.Error(getErr))
			errorToReturn = getErr
		} else {
			// setting loan step  in actRes, bcoz if signal received first before activity retry, all fields in actRes will be nil,
			// and for such cases, if we try to access any field of LoanStep from actRes it will give nil pointer exception
			actRes.LoanStep = req.Request.GetLoanStep()
			actRes.LoanStep.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
		}
	})

	err = epifitemporal.ReceiveSignalWithFuture(ctx, employmentDetailsConfirmationFuture, sigChannel, maxAddDetailsWaitTimeHrs*time.Hour)
	if err != nil {
		workflow.GetLogger(ctx).Info("EmploymentConfirmationSignal receive signal with future processing failed", zap.Error(err))
		return err
	}
	if errorToReturn != nil {
		return errorToReturn
	}

	res.LseFieldMasks = actRes.GetLseFieldMasks()
	res.LoanStep = actRes.GetLoanStep()
	return nil
}
