package preapprovedloan

import (
	"fmt"
	"time"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
	emptyPb "google.golang.org/protobuf/types/known/emptypb"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"
)

const (
	maxRevisedApplicationSignalWaitTimeHrs = 48
)

// LLFetchOffer represents the stage which fetches the latest offer from the vendor
type LLFetchOffer struct {
	*stages.CommonPreProcessStage
	*stages.CommonPostProcessStage
}

func NewLLFetchOffer() *LLFetchOffer {
	return &LLFetchOffer{}
}

var _ stages.IStage = &LLFetchOffer{}

func (ca *LLFetchOffer) Perform(ctx workflow.Context, req *stages.PerformRequest) (*stages.PerformResponse, error) {
	res := &stages.PerformResponse{
		LoanStep:      req.Request.GetLoanStep(),
		LseFieldMasks: []palPb.LoanStepExecutionFieldMask{},
	}

	// Step1: execute fetch loan offer activity
	actReq := &palActivityPb.PalActivityRequest{
		LoanStep:    req.Request.GetLoanStep(),
		Vendor:      req.Vendor,
		LoanProgram: req.LoanProgram,
	}
	actRes := &palActivityPb.PalActivityResponse{}

	actErr := activityPkg.Execute(ctx, palNs.LLCheckApplicantStatus, actRes, actReq)
	res.LseFieldMasks = append(res.LseFieldMasks, actRes.GetLseFieldMasks()...)
	if IsActivityError(actRes.GetLoanStep(), res, actErr, false) {
		return res, actErr
	}

	actReq = &palActivityPb.PalActivityRequest{
		LoanStep:    req.Request.GetLoanStep(),
		Vendor:      req.Vendor,
		LoanProgram: req.LoanProgram,
	}
	actRes = &palActivityPb.PalActivityResponse{}
	actErr = activityPkg.Execute(ctx, palNs.LLFetchOffer, actRes, actReq)
	res.LseFieldMasks = append(res.LseFieldMasks, actRes.GetLseFieldMasks()...)
	if IsActivityError(actRes.GetLoanStep(), res, actErr, false) {
		return res, actErr
	}

	if res.GetLoanStep().GetSubStatus() == palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_LOAN_DETAILS_REVISED {
		handleSignalTimeoutVersion := workflow.GetVersion(ctx, "fetch-offer-revised-details-handle-signal-timeout-error", workflow.DefaultVersion, 1)
		revisedErr := ca.performRevisedLoanDetails(ctx, req, res, handleSignalTimeoutVersion)
		if revisedErr != nil {
			// TODO: remove after prod push
			wfVersion := workflow.GetVersion(ctx, "fetch-offer-revised-details-retryable-error", workflow.DefaultVersion, 1)
			if wfVersion == 1 {
				if epifitemporal.IsRetryableError(revisedErr) {
					res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED
				}
			}
			if handleSignalTimeoutVersion == 1 {
				if epifitemporal.HasSignalReceivedTimedOut(revisedErr) {
					res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED
				}
			}
			return res, revisedErr
		}
	}

	return res, nil
}

func (ca *LLFetchOffer) GetName() palPb.LoanStepExecutionStepName {
	return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER
}

func (ca *LLFetchOffer) GetCelestialStage() epifitemporal.Stage {
	return palNs.StageOfferFetch
}

// nolint: funlen
func (ca *LLFetchOffer) performRevisedLoanDetails(ctx workflow.Context, req *stages.PerformRequest, res *stages.PerformResponse, handleSignalTimeoutVersion workflow.Version) error {
	lg := workflow.GetLogger(ctx)
	// in version 1, next action is set in the loan request as part of the LLFetchOffer activity
	// this is done because the next action is different in the version 1 and changes can be made to the deeplink without the need for workflow versioning
	wfVersion := workflow.GetVersion(ctx, "ll-realtime-offer-update-revised-loan-details", workflow.DefaultVersion, 1)
	if wfVersion == workflow.DefaultVersion {
		updateReq := &palActivityPb.UpdateLRNextActionRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
				Ownership:   epificontext.OwnershipFromContext(ctx),
			},
			LoanRequestId: req.Request.GetLoanStep().GetRefId(),
			NextAction:    &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_PL_UPDATED_RATE_SCREEN},
		}

		updateRes := &palActivityPb.PalActivityResponse{}
		localActVer := workflow.GetVersion(ctx, "move-lightweight-activities-to-local", workflow.DefaultVersion, 1)
		if localActVer != 1 {
			updateErr := activityPkg.Execute(ctx, palNs.UpdateLRNextAction, updateRes, updateReq)
			if IsActivityError(updateRes.GetLoanStep(), res, updateErr, false) {
				return updateErr
			}
		} else {
			updateErr := activityPkg.ExecuteLocally(ctx, palNs.UpdateLRNextAction, updateRes, updateReq)
			if IsActivityError(updateRes.GetLoanStep(), res, updateErr, false) {
				return updateErr
			}
		}
	}

	// in the new version we are not polling the status of LSE along with waiting for signal
	// the LSE status is not being updated from any other process because the workflow signal is reliable and
	// workflow itself will update the status after receiving the signal
	if handleSignalTimeoutVersion == 1 {
		signalPayload := &emptyPb.Empty{}
		sigChannel := epifitemporal.NewSignalChannel(ctx, palNs.RevisedLoanApplicationConfirmationSignal, signalPayload)
		var errorToReturn error
		sigChannel.AddReceiverHandler(func(getErr error, payload *emptyPb.Empty) {
			if getErr != nil {
				lg.Error("RevisedLoanApplicationConfirmation signal processing failed", zap.Error(getErr))
				errorToReturn = getErr
			}
		})
		err := epifitemporal.ReceiveSignal(ctx, sigChannel, maxRevisedApplicationSignalWaitTimeHrs*time.Hour)
		if err != nil {
			lg.Error("RevisedLoanApplicationConfirmation signal receive failed", zap.Error(err))
			return err
		}
		if errorToReturn != nil {
			return errorToReturn
		}
	} else {
		actReq := &palActivityPb.PalActivityRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: req.Request.GetLoanStep().GetOrchId(),
			},
			LoanStep:    req.Request.GetLoanStep(),
			Vendor:      req.Vendor,
			LoanProgram: req.LoanProgram,
		}
		actRes := &palActivityPb.PalActivityResponse{}

		signalPayload := &emptyPb.Empty{}
		getRevisedLoanDetailsStatusFuture, err := activityPkg.ExecuteAsync(ctx, palNs.LLAsyncStepTerminalStatusCheck, actReq, actRes)
		if err != nil {
			return fmt.Errorf("failed to initiate async activity execution: %s: %w", palNs.LLAsyncStepTerminalStatusCheck, err)
		}

		var errorToReturn error
		getRevisedLoanDetailsStatusFuture.AddFutureHandler(func(getErr error, res *palActivityPb.PalActivityResponse) {
			lg.Debug("verification status activity returned first")

			if getErr != nil {
				lg.Error("LLAsyncStepTerminalStatusCheck activity async processing failed", zap.Error(getErr))
				errorToReturn = getErr
			}
		})
		sigChannel := epifitemporal.NewSignalChannel(ctx, palNs.RevisedLoanApplicationConfirmationSignal, signalPayload)
		sigChannel.AddReceiverHandler(func(getErr error, payload *emptyPb.Empty) {
			lg.Debug("signal returned first")

			if getErr != nil {
				lg.Error("RevisedLoanApplicationConfirmation signal processing failed", zap.Error(getErr))
				errorToReturn = getErr
			}
		})

		err = epifitemporal.ReceiveSignalWithFuture(ctx, getRevisedLoanDetailsStatusFuture, sigChannel, maxRevisedApplicationSignalWaitTimeHrs*time.Hour)
		if err != nil {
			lg.Error("ReceiveSignalWithFuture failed", zap.Error(err))
			return err
		}

		if errorToReturn != nil {
			return errorToReturn
		}
	}
	res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
	return nil
}
