// This file contains all stage implementations related to adding details of a loan applicant on the liquiloans
// and Fi platform.

package preapprovedloan

import (
	"fmt"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	preApprovedLoanNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"
)

// LLAddDetails represents the stage which needs a user to enter details for address and employment
type LLAddDetails struct {
	*stages.CommonPreProcessStage
	*stages.CommonPostProcessStage
}

func NewLLAddDetails() *LLAddDetails {
	return &LLAddDetails{}
}

var _ stages.IStage = &LLAddDetails{}

func (ca *LLAddDetails) Perform(ctx workflow.Context, req *stages.PerformRequest) (*stages.PerformResponse, error) {
	res := &stages.PerformResponse{
		LoanStep: req.Request.GetLoanStep(),
	}

	nextAction := &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_PRE_APPROVED_ADDRESS_CONFIRMATION_SCREEN,
	}
	if req.LoanProgram == palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY {
		nextAction.Screen = deeplinkPb.Screen_EARLY_SALARY_ADDRESS_CONFIRMATION_SCREEN
	}
	updateReq := &palActivityPb.UpdateLRNextActionRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
			Ownership:   epificontext.OwnershipFromContext(ctx),
		},
		LoanRequestId: req.Request.GetLoanStep().GetRefId(),
		NextAction:    nextAction,
	}

	updateRes := &palActivityPb.PalActivityResponse{}
	localActVer := workflow.GetVersion(ctx, "move-lightweight-activities-to-local", workflow.DefaultVersion, 1)
	if localActVer != 1 {
		updateErr := activityPkg.Execute(ctx, preApprovedLoanNs.UpdateLRNextAction, updateRes, updateReq)
		if IsActivityError(updateRes.GetLoanStep(), res, updateErr, false) {
			return res, updateErr
		}
	} else {
		updateErr := activityPkg.ExecuteLocally(ctx, preApprovedLoanNs.UpdateLRNextAction, updateRes, updateReq)
		if IsActivityError(updateRes.GetLoanStep(), res, updateErr, false) {
			return res, updateErr
		}
	}

	addErr := ca.performAddAddressDetails(ctx, req, res)
	if addErr != nil {
		if epifitemporal.IsRetryableError(addErr) {
			res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED
		}
		return res, addErr
	}

	var empErr error
	if req.LoanProgram == palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY {
		res, empErr = ca.performAddEmploymentDetailsWithSalary(ctx, req, res)
	} else {
		empErr = ca.performAddEmploymentDetails(ctx, req, res)
	}
	if empErr != nil {
		return res, empErr
	}

	return res, nil
}

func (ca *LLAddDetails) GetName() palPb.LoanStepExecutionStepName {
	return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS
}

func (ca *LLAddDetails) GetCelestialStage() epifitemporal.Stage {
	return preApprovedLoanNs.StageAddDetails
}

func (ca *LLAddDetails) performAddAddressDetails(ctx workflow.Context, req *stages.PerformRequest, res *stages.PerformResponse) error {
	// Step2. Set the workflow status as blocked
	var workflowStatus = stagePb.Status_BLOCKED
	err := celestialPkg.UpdateWorkflowStage(ctx, ca.GetCelestialStage(), workflowStatus)
	if err != nil {
		return fmt.Errorf("failed to update workflow stage: %s: %w", ca.GetCelestialStage(), err)
	}

	actReq := &palActivityPb.PalActivityRequest{
		LoanStep:    req.Request.GetLoanStep(),
		Vendor:      req.Vendor,
		LoanProgram: req.LoanProgram,
	}
	actRes := &palActivityPb.PalActivityResponse{}

	addressConfirmationFuture, err := activityPkg.ExecuteAsync(ctx, preApprovedLoanNs.LLGetAddressStatus, actReq, actRes)
	if err != nil {
		return fmt.Errorf("failed to execute GetAddressStatus activity: %w", err)
	}

	var errorToReturn error
	addressConfirmationFuture.AddFutureHandler(func(futureGetErr error, resp *palActivityPb.PalActivityResponse) {
		if futureGetErr != nil {
			workflow.GetLogger(ctx).Info("address details confirmation future finished with error", zap.Error(futureGetErr))
			errorToReturn = futureGetErr
		}
	})

	// Onboarding stage is blocked until the user confirms the address and pre-approved-loan service sends us a signal or
	// max waiting time is passed
	addressConfirmationFuture.AddToSelector(ctx, workflow.NewSelector(ctx)).
		AddReceive(workflow.GetSignalChannel(ctx, string(preApprovedLoanNs.AddressConfirmationSignal)), func(c workflow.ReceiveChannel, more bool) {
			c.Receive(ctx, nil)
		}).
		Select(ctx)

	if errorToReturn != nil {
		return errorToReturn
	}

	// Step3. unblock the workflow and set the status as initiated
	workflowStatus = stagePb.Status_INITIATED
	err = celestialPkg.UpdateWorkflowStage(ctx, ca.GetCelestialStage(), workflowStatus)
	if err != nil {
		return fmt.Errorf("failed to update workflow stage: %s: %w", ca.GetCelestialStage(), err)
	}

	actReq = &palActivityPb.PalActivityRequest{
		LoanStep:    req.Request.GetLoanStep(),
		Vendor:      req.Vendor,
		LoanProgram: req.LoanProgram,
	}
	actRes = &palActivityPb.PalActivityResponse{}
	// Step4: execute the add address details activity
	actErr := activityPkg.Execute(ctx, preApprovedLoanNs.LLAddAddressDetails, actRes, actReq)
	if IsActivityError(actRes.GetLoanStep(), res, actErr, false) {
		return actErr
	}
	return nil
}

func (ca *LLAddDetails) performAddEmploymentDetails(ctx workflow.Context, req *stages.PerformRequest, res *stages.PerformResponse) error {
	// Step1. Set the workflow status as blocked and wait for employment details confirmation
	workflowStatus := stagePb.Status_BLOCKED
	err := celestialPkg.UpdateWorkflowStage(ctx, ca.GetCelestialStage(), workflowStatus)
	if err != nil {
		return fmt.Errorf("failed to update workflow stage: %s: %w", ca.GetCelestialStage(), err)
	}

	actReq := &palActivityPb.PalActivityRequest{
		LoanStep:    req.Request.GetLoanStep(),
		Vendor:      req.Vendor,
		LoanProgram: req.LoanProgram,
	}
	actRes := &palActivityPb.PalActivityResponse{}
	employmentDetailsConfirmationFuture, err := activityPkg.ExecuteAsync(ctx, preApprovedLoanNs.LLGetEmploymentStatus, actReq, actRes)
	if err != nil {
		return fmt.Errorf("failed to execute GetAddressStatus activity: %w", err)
	}

	var errorToReturn error
	employmentDetailsConfirmationFuture.AddFutureHandler(func(futureGetErr error, resp *palActivityPb.PalActivityResponse) {
		workflow.GetLogger(ctx).Info("employment details confirmation future finished")
		if futureGetErr != nil {
			workflow.GetLogger(ctx).Info("employment details confirmation future finished with error", zap.Error(futureGetErr))
			errorToReturn = futureGetErr
		}
	})

	// Onboarding stage is blocked until the user confirms the employment details and pre-approved-loan service sends us a signal or
	// max waiting time is passed
	employmentDetailsConfirmationFuture.AddToSelector(ctx, workflow.NewSelector(ctx)).
		AddReceive(workflow.GetSignalChannel(ctx, string(preApprovedLoanNs.EmploymentConfirmationSignal)), func(c workflow.ReceiveChannel, more bool) {
			c.Receive(ctx, nil)
		}).
		Select(ctx)

	if errorToReturn != nil {
		if epifitemporal.IsRetryableError(errorToReturn) {
			res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED
		}
		return errorToReturn
	}

	// Step2. unblock the workflow and set the status as initiated
	workflowStatus = stagePb.Status_INITIATED
	err = celestialPkg.UpdateWorkflowStage(ctx, ca.GetCelestialStage(), workflowStatus)
	if err != nil {
		return fmt.Errorf("failed to update workflow stage: %s: %w", ca.GetCelestialStage(), err)
	}

	actReq = &palActivityPb.PalActivityRequest{
		LoanStep:    req.Request.GetLoanStep(),
		Vendor:      req.Vendor,
		LoanProgram: req.LoanProgram,
	}
	actRes = &palActivityPb.PalActivityResponse{}
	// Step3: execute add employment details activity after the confirmation
	actErr := activityPkg.Execute(ctx, preApprovedLoanNs.LLAddEmploymentDetails, actRes, actReq)
	if IsActivityError(actRes.GetLoanStep(), res, actErr, false) {
		return actErr
	}
	res.LseFieldMasks = actRes.GetLseFieldMasks()
	res.LoanStep = actRes.GetLoanStep()
	return nil
}

func (ca *LLAddDetails) performAddEmploymentDetailsWithSalary(ctx workflow.Context, req *stages.PerformRequest, res *stages.PerformResponse) (*stages.PerformResponse, error) {
	actReq := &palActivityPb.PalActivityRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.Request.GetLoanStep().GetOrchId(),
		},
		LoanStep:    req.Request.GetLoanStep(),
		Vendor:      req.Vendor,
		LoanProgram: req.LoanProgram,
	}
	actRes := &palActivityPb.PalActivityResponse{}
	actErr := activityPkg.Execute(ctx, preApprovedLoanNs.LLAddEmploymentDetailsWithSalary, actRes, actReq)
	if IsActivityError(actRes.GetLoanStep(), res, actErr, false) {
		// return to stop the workflow perform at this state
		// returning res to pass loan step in post process for updating in loan step (failure/cancelled cases)
		// returning error to log the error and then pass to post process
		return res, actErr
	}
	res.LseFieldMasks = actRes.GetLseFieldMasks()
	if res.GetLoanStep().GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED {
		res.NextAction = &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_ERROR_SCREEN}
	}
	return res, nil
}
