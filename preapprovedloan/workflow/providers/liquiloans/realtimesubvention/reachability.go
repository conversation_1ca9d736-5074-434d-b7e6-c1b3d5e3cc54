package realtimesubvention

import (
	"go.uber.org/zap"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"

	"go.temporal.io/sdk/workflow"
)

const (
	maxWaitTimeForAddressDetailsStageInHrs = 72
)

type AddressV2 struct {
	*stages.CommonPreProcessStage
	*stages.CommonPostProcessStage
}

func NewAddressV2() *AddressV2 {
	return &AddressV2{}
}

var _ stages.IStage = &AddressV2{}

func (ca *AddressV2) Perform(ctx workflow.Context, req *stages.PerformRequest) (*stages.PerformResponse, error) {
	res := &stages.PerformResponse{
		LoanStep: req.Request.GetLoanStep(),
	}

	updateReq := &palActivityPb.UpdateLRNextActionRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
			Ownership:   epificontext.OwnershipFromContext(ctx),
		},
		LoanRequestId: req.Request.GetLoanStep().GetRefId(),
		NextAction: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_LOAN_ADDRESS_VERIFICATION_INTRO_SCREEN,
		},
	}

	updateRes := &palActivityPb.PalActivityResponse{}
	localActVer := workflow.GetVersion(ctx, "move-lightweight-activities-to-local", workflow.DefaultVersion, 1)
	if localActVer != 1 {
		updateErr := activityPkg.Execute(ctx, palNs.UpdateLRNextAction, updateRes, updateReq)
		if providers.IsActivityError(ctx, updateRes.GetLoanStep(), updateRes.GetLseFieldMasks(), res, updateErr, false) {
			workflow.GetLogger(ctx).Error("error in updating next action to PRE_APPROVED_ADDRESS_VERIFICATION_INTRO_SCREEN", zap.Error(updateErr))
			return res, updateErr
		}
	} else {
		updateErr := activityPkg.ExecuteLocally(ctx, palNs.UpdateLRNextAction, updateRes, updateReq)
		if providers.IsActivityError(ctx, updateRes.GetLoanStep(), updateRes.GetLseFieldMasks(), res, updateErr, false) {
			workflow.GetLogger(ctx).Error("error in updating next action to PRE_APPROVED_ADDRESS_VERIFICATION_INTRO_SCREEN", zap.Error(updateErr))
			return res, updateErr
		}
	}

	// step2: wait for the sync proxy signal and execute the activity to update the details in lse when signal is received
	syncActivityReq := &palActivityPb.PalActivityRequest{
		LoanStep:    req.Request.GetLoanStep(),
		Vendor:      req.Vendor,
		LoanProgram: req.LoanProgram,
	}
	syncActivityRes := &palActivityPb.PalActivityResponse{}
	syncExecError := providers.PerformActionInSync(ctx, &providers.SyncPolledStageRequest{
		SyncActivityDetails: &providers.ActivityDetails{
			Name:         palNs.AddAddressDetailsV2,
			SyncRequest:  syncActivityReq,
			SyncResponse: syncActivityRes,
		},
		ExecuteAsyncInParallel: false,
		StageName:              req.Request.GetLoanStep().GetStepName(),
		WfProcessingParams:     req.WfProcessingParams,
		LoanRequestId:          req.Request.GetLoanStep().GetRefId(),
		Vendor:                 req.Vendor,
		LoanProgram:            req.LoanProgram,
		TimeoutDurationInMins:  maxWaitTimeForAddressDetailsStageInHrs * 60,
	})
	if providers.IsActivityError(ctx, syncActivityRes.GetLoanStep(), syncActivityRes.GetLseFieldMasks(), res, syncExecError, false) {
		return res, syncExecError
	}

	res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
	return res, nil
}

func (ca *AddressV2) GetName() palPb.LoanStepExecutionStepName {
	return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADDRESS
}

func (ca *AddressV2) GetCelestialStage() epifitemporal.Stage {
	return palNs.StageAddressV2
}
