package stages

import (
	"fmt"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/workflow"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	preApprovedLoanNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/metrics"
	helper "github.com/epifi/gamma/preapprovedloan/workflow"
)

type ICollectionFactory interface {
	GetCollectionStrategyExecutor(ctx workflow.Context, req *GetCollectionStrategyExecutorRequest) (*GetCollectionStrategyExecutorResponse, error)
	GetCollectionPaymentAndReconcileExecutor(ctx workflow.Context, req *GetVendorLoanProgramCollectionExecutorRequest) (*GetVendorLoanProgramCollectionExecutorResponse, error)
}

type GetCollectionStrategyExecutorRequest struct {
	LoanHeader *palPb.LoanHeader
}
type GetCollectionStrategyExecutorResponse struct {
	Executor ICollectionStrategy
}

type GetVendorLoanProgramCollectionExecutorRequest struct {
	LoanHeader *palPb.LoanHeader
}
type GetVendorLoanProgramCollectionExecutorResponse struct {
	PaymentExecutor   ICollectionPaymentExecutor
	ReconcileExecutor ICollectionReconcileExecutor
}

type ICollectionStrategy interface {
	Execute(workflow.Context, *CollectionStrategyExecuteRequest) (*CollectionStrategyExecuteResponse, error)
}

type CollectionStrategyExecuteRequest struct {
	LoanHeader       *palPb.LoanHeader
	RepaymentBreakup []*palPb.RepaymentBreakupData
	AccountId        string
	ClientId         string
}
type CollectionStrategyExecuteResponse struct {
}

type CollectionExecutorsPreProcessStage struct {
}
type CollectionExecutorsPostProcessStage struct {
}
type CollectionExecutorsPreProcessRequest struct {
	WfReqId            string
	WfProcessingParams *workflowPb.ProcessingParams
	CelestialStage     epifitemporal.Stage
	LoanHeader         *palPb.LoanHeader
	StepName           palPb.LoanStepExecutionStepName
}
type CollectionExecutorsPreProcessResponse struct {
	WfReqId            string
	WfProcessingParams *workflowPb.ProcessingParams
	CelestialStage     epifitemporal.Stage
	LoanHeader         *palPb.LoanHeader
	LoanStep           *palPb.LoanStepExecution
}
type CollectionExecutorsPostProcessRequest struct {
	LoanStep              *palPb.LoanStepExecution
	WfReqId               string
	WfProcessingParams    *workflowPb.ProcessingParams
	CelestialStage        epifitemporal.Stage
	StepName              palPb.LoanStepExecutionStepName
	LoanHeader            *palPb.LoanHeader
	PaymentOrReconcileErr error
}
type CollectionExecutorsPostProcessResponse struct {
	WfReqId            string
	WfProcessingParams *workflowPb.ProcessingParams
	CelestialStage     epifitemporal.Stage
	LoanHeader         *palPb.LoanHeader
}

type ICollectionPaymentExecutor interface {
	PreProcess(ctx workflow.Context, req *CollectionExecutorsPreProcessRequest) (*CollectionExecutorsPreProcessResponse, error)
	PerformPayment(workflow.Context, *PerformPaymentRequest) (*PerformPaymentResponse, error)
	PostProcess(ctx workflow.Context, req *CollectionExecutorsPostProcessRequest) (*CollectionExecutorsPostProcessResponse, error)
}

type ICollectionReconcileExecutor interface {
	PreProcess(ctx workflow.Context, req *CollectionExecutorsPreProcessRequest) (*CollectionExecutorsPreProcessResponse, error)
	PerformReconcile(workflow.Context, *PerformReconcileRequest) (*PerformReconcileResponse, error)
	PostProcess(ctx workflow.Context, req *CollectionExecutorsPostProcessRequest) (*CollectionExecutorsPostProcessResponse, error)
}

func (cs *CollectionExecutorsPreProcessStage) PreProcess(ctx workflow.Context, req *CollectionExecutorsPreProcessRequest) (*CollectionExecutorsPreProcessResponse, error) {
	var (
		res = &CollectionExecutorsPreProcessResponse{}
	)

	actReq := &palActivityPb.CreateLoanStepExecutionRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
			Ownership:   epificontext.OwnershipFromContext(ctx),
		},
		StepName: req.StepName,
		Flow:     palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_COLLECTIONS,
	}

	actRes := &palActivityPb.CreateLoanStepExecutionResponse{}
	createLseErr := activityPkg.Execute(ctx, preApprovedLoanNs.CreateLoanStepExecution, actRes, actReq)
	if createLseErr != nil {
		return nil, errors.Wrap(createLseErr, "child workflow lse creation failed")
	}

	// instrument loan execution step initiated metric for improving visibility.
	metrics.RecordLoanStepExecutionStatusUpdate(req.LoanHeader.GetVendor().String(), req.LoanHeader.GetLoanProgram().String(),
		palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_COLLECTIONS.String(), req.StepName.String(), palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED.String(),
		palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED.String(), palPb.Provenance_PROVENANCE_UNSPECIFIED.String())

	res.LoanStep = actRes.GetLoanStep()
	return res, nil

	// TODO
	// 1. Emit event
}

func (cs *CollectionExecutorsPostProcessStage) PostProcess(ctx workflow.Context, req *CollectionExecutorsPostProcessRequest) (*CollectionExecutorsPostProcessResponse, error) {
	res := &CollectionExecutorsPostProcessResponse{}
	var lseFieldMasks []palPb.LoanStepExecutionFieldMask

	workflowStageStatus := helper.GetWorkflowStageStatusFromStatus(req.LoanStep.GetStatus())

	if req.LoanStep != nil {
		lseFieldMasks = append(lseFieldMasks, palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS)
		switch workflowStageStatus {
		case stagePb.Status_MANUAL_INTERVENTION:
			req.LoanStep.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION
		case stagePb.Status_SUCCESSFUL,
			stagePb.Status_FAILED:
			req.LoanStep.CompletedAt = timestampPb.Now()
			lseFieldMasks = append(lseFieldMasks, palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_COMPLETED_AT)
		}

		// instrument granular status updates for loan execution step execution for improving visibility.
		metrics.RecordLoanStepExecutionStatusUpdate(req.LoanHeader.GetVendor().String(), req.LoanHeader.GetLoanProgram().String(),
			req.LoanStep.GetFlow().String(), req.LoanStep.GetStepName().String(), req.LoanStep.GetStatus().String(), req.LoanStep.GetSubStatus().String(),
			palPb.Provenance_PROVENANCE_UNSPECIFIED.String())

		actReq := &palActivityPb.UpdateLoanStepExecutionRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
				Ownership:   epificontext.OwnershipFromContext(ctx),
			},
			LoanStep:      req.LoanStep,
			LseFieldMasks: lseFieldMasks,
		}
		actRes := &palActivityPb.PalActivityResponse{}
		updateErr := activityPkg.Execute(ctx, preApprovedLoanNs.UpdateLoanStepExecution, actRes, actReq)
		if updateErr != nil {
			return nil, fmt.Errorf("UpdateLoanStepExecution activity failed: %w", updateErr)
		}
	}

	if req.PaymentOrReconcileErr != nil {
		return nil, fmt.Errorf("perform error: %w", req.PaymentOrReconcileErr)
	}

	if workflowStageStatus == stagePb.Status_SUCCESSFUL {
		return res, nil
	}

	if workflowStageStatus == stagePb.Status_MANUAL_INTERVENTION {
		return nil, errors.New("needs manual intervention")
	}

	// non-successful state so sending error as need to fail the workflow
	return nil, fmt.Errorf("workflow stage failed, terminating workflow, WfReqId: %v, failure: %s", req.WfReqId, workflowStageStatus.String())

	// TODO
	// 1. Emit Event
}
