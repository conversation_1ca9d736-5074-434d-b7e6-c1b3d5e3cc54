package stages

import (
	"fmt"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	preApprovedLoanNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	"github.com/epifi/be-common/pkg/logger"
	palpb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/events"
	"github.com/epifi/gamma/preapprovedloan/metrics"
	helper "github.com/epifi/gamma/preapprovedloan/workflow"
)

type CommonPaymentPreProcessStage struct {
}

// PreProcess - does the common pre-processing needed to be done before every stage execution. It involves the following:
// 1. Initialization of workflow stage
// 2. Creation of loan step execution for the stage

//nolint:funlen,dupl
func (cs *CommonPaymentPreProcessStage) PreProcess(ctx workflow.Context, req *PreProcessRequest) (*PreProcessResponse, error) {
	var (
		err error
		res = &PreProcessResponse{}
	)

	err = celestialPkg.InitiateWorkflowStage(ctx, req.CelestialStage, stagePb.Status_INITIATED)
	if err != nil {
		return nil, fmt.Errorf("failed to initiated workflow stage: %s: %w", req.CelestialStage, err)
	}

	actReq := &palActivityPb.CreateLoanStepExecutionRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
			Ownership:   epificontext.OwnershipFromContext(ctx),
		},
		StepName: req.StageName,
		Flow:     req.FlowName,
	}

	actRes := &palActivityPb.CreateLoanStepExecutionResponse{}
	localActVer := workflow.GetVersion(ctx, "move-lightweight-activities-to-local", workflow.DefaultVersion, 1)
	if localActVer != 1 {
		err = activityPkg.Execute(ctx, preApprovedLoanNs.CreateLoanStepExecution, actRes, actReq)
		if err != nil {
			return nil, err
		}
	} else {
		err = activityPkg.ExecuteLocally(ctx, preApprovedLoanNs.CreateLoanStepExecution, actRes, actReq)
		if err != nil {
			return nil, err
		}
	}

	// instrument loan execution step initiated metric for improving visibility.
	metrics.RecordLoanStepExecutionStatusUpdate(req.GetVendor(), req.GetLoanProgram(), req.FlowName.String(), req.StageName.String(),
		palpb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED.String(), palpb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED.String(),
		palpb.Provenance_PROVENANCE_UNSPECIFIED.String())

	vendor := req.GetVendor()
	loanProgram := req.GetLoanProgram()
	eventReq := &palActivityPb.EmitEventRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
			Ownership:   epificontext.OwnershipFromContext(ctx),
		},
		Lse:         actRes.GetLoanStep(),
		EventName:   events.StageInitiated,
		Vendor:      vendor,
		LoanProgram: loanProgram,
	}
	eventRes := &palActivityPb.EmitEventResponse{}
	lg := workflow.GetLogger(ctx)
	if localActVer != 1 {
		eventErr := activityPkg.Execute(ctx, preApprovedLoanNs.EmitEvent, eventRes, eventReq)
		if eventErr != nil {
			lg.Info("activity to emit request events failed ", eventReq.GetEventName(), eventReq.GetLse().GetStepName())
		}
	} else {
		eventErr := activityPkg.ExecuteLocally(ctx, preApprovedLoanNs.EmitEvent, eventRes, eventReq)
		if eventErr != nil {
			lg.Info("activity to emit request events failed ", eventReq.GetEventName(), eventReq.GetLse().GetStepName())
		}
	}

	res.loanStep = actRes.GetLoanStep()
	return res, nil
}

type CommonPaymentPostProcessStage struct {
}

// PostProcess - does the common post-processing needed to be done after every stage execution. It involves the following:
// 1. Updating the workflow stage status
// 2. Publishing workflow update event if workflow status is terminal
// 3. Update loan step execution status

//nolint:funlen,dupl
func (cs *CommonPaymentPostProcessStage) PostProcess(ctx workflow.Context, req *PostProcessRequest) (*PostProcessResponse, error) {
	res := &PostProcessResponse{}

	workflowStageStatus := helper.GetWorkflowStageStatusFromStatus(req.Request.GetLoanStep().GetStatus())
	err := celestialPkg.UpdateWorkflowStage(ctx, req.CelestialStage, workflowStageStatus)
	if err != nil {
		// NOTE: returning error here as this is an unexpected platform failure. We expect an alert here
		return nil, fmt.Errorf("failed to update workflow stage: %s: %w", req.CelestialStage, err)
	}

	if req.Request.GetLoanStep() != nil {
		req.Request.LseFieldMasks = append(req.Request.GetLseFieldMasks(), palpb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS)
		switch workflowStageStatus {
		case stagePb.Status_MANUAL_INTERVENTION:
			req.Request.GetLoanStep().Status = palpb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION
		case stagePb.Status_SUCCESSFUL,
			stagePb.Status_FAILED:
			req.Request.GetLoanStep().CompletedAt = timestampPb.Now()
			req.Request.LseFieldMasks = append(req.Request.GetLseFieldMasks(), palpb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_COMPLETED_AT)
		default:
			return nil, fmt.Errorf("unknown pre-pay workflow status for lse:%v", req.Request.GetLoanStep())
		}

		loanStep := req.Request.GetLoanStep()
		// instrument granular status updates for loan execution step execution for improving visibility.
		metrics.RecordLoanStepExecutionStatusUpdate(req.Vendor.String(), req.LoanProgram.String(), loanStep.GetFlow().String(),
			loanStep.GetStepName().String(), loanStep.GetStatus().String(), loanStep.GetSubStatus().String(),
			palpb.Provenance_PROVENANCE_UNSPECIFIED.String())

		actReq := &palActivityPb.UpdateLoanStepExecutionRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
				Ownership:   epificontext.OwnershipFromContext(ctx),
			},
			LoanStep:      req.Request.GetLoanStep(),
			LseFieldMasks: req.Request.GetLseFieldMasks(),
		}
		actRes := &palActivityPb.PalActivityResponse{}
		localActVer := workflow.GetVersion(ctx, "move-lightweight-activities-to-local", workflow.DefaultVersion, 1)
		if localActVer != 1 {
			updateErr := activityPkg.Execute(ctx, preApprovedLoanNs.UpdateLoanStepExecution, actRes, actReq)
			if updateErr != nil {
				return nil, fmt.Errorf("UpdateLoanStepExecution activity failed: %w", updateErr)
			}
		} else {
			updateErr := activityPkg.ExecuteLocally(ctx, preApprovedLoanNs.UpdateLoanStepExecution, actRes, actReq)
			if updateErr != nil {
				return nil, fmt.Errorf("UpdateLoanStepExecution activity failed: %w", updateErr)
			}
		}
	}

	vendor := req.GetVendor().String()
	loanProgram := req.GetLoanProgram().String()
	eventReq := &palActivityPb.EmitEventRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
			Ownership:   epificontext.OwnershipFromContext(ctx),
		},
		Lse:         req.Request.GetLoanStep(),
		EventName:   events.StageCompleted,
		Vendor:      vendor,
		LoanProgram: loanProgram,
	}
	eventRes := &palActivityPb.EmitEventResponse{}
	lg := workflow.GetLogger(ctx)
	localActVer := workflow.GetVersion(ctx, "move-lightweight-activities-to-local", workflow.DefaultVersion, 1)
	if localActVer != 1 {
		eventErr := activityPkg.Execute(ctx, preApprovedLoanNs.EmitEvent, eventRes, eventReq)
		if eventErr != nil {
			lg.Info("activity to emit response events failed ", eventReq.GetEventName(), eventReq.GetLse().GetStepName())
		}
	} else {
		eventErr := activityPkg.ExecuteLocally(ctx, preApprovedLoanNs.EmitEvent, eventRes, eventReq)
		if eventErr != nil {
			lg.Info("activity to emit response events failed ", eventReq.GetEventName(), eventReq.GetLse().GetStepName())
		}
	}
	// adding this log to debug the issue in case stage failures spikes
	lg.Info("failure in payment stage execution", zap.String(logger.LOAN_PROGRAM, req.LoanProgram.String()), zap.String(logger.VENDOR, req.Vendor.String()),
		zap.String(logger.STAGE, req.Request.GetLoanStep().GetStepName().String()), zap.String(logger.STATUS, req.Request.GetLoanStep().GetStatus().String()),
		zap.String(logger.SUB_STATUS, req.Request.GetLoanStep().GetSubStatus().String()), zap.String(logger.CLIENT_REQUEST_ID, req.WfProcessingParams.GetClientReqId().GetId()))
	// workflow stage status was wrongly handled, we were returning an error both in permanent failure and manual intervention status update cases which on the caller side was getting interpreted as permanent failure for all cases so an inprogress request can also get marked as failed in the existing flow.
	// to fix this, returning a permanent failure only for terminal failure status updates and a transient error for unhandled (including MANUAL_INTERVENTION) status updates so that caller can distinguish between transient and permanent failures to take appropriate action.
	if v := workflow.GetVersion(ctx, "fix-payment-post-process-stage-status-handling", workflow.DefaultVersion, 1); v >= 1 {
		switch workflowStageStatus {
		case stagePb.Status_SUCCESSFUL:
			return res, nil
		case stagePb.Status_FAILED:
			return nil, epifitemporal.NewPermanentError(errors.New("workflow stage moved to a permanent failed state"))
		default:
			return nil, epifitemporal.NewTransientError(errors.New("workflow stage is not in terminal success or failed state"))
		}
	} else {
		switch workflowStageStatus {
		case stagePb.Status_SUCCESSFUL:
			return res, nil
		case stagePb.Status_MANUAL_INTERVENTION:
			return nil, epifitemporal.NewTransientError(errors.New("manual intervention"))
		default:
			// non-successful state so sending error as need to fail the workflow
			return nil, fmt.Errorf("workflow stage failed, terminating workflow, WfReqId: %v, failure: %s", req.WfReqId, workflowStageStatus.String())
		}
	}
}
