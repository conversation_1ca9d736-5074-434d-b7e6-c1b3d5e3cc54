package stages

import (
	"fmt"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	preApprovedLoanNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	"github.com/epifi/be-common/pkg/logger"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palFeEnumsPb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	palpb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	dlHelper "github.com/epifi/gamma/preapprovedloan/deeplink"
	"github.com/epifi/gamma/preapprovedloan/events"
	"github.com/epifi/gamma/preapprovedloan/metrics"
	helper "github.com/epifi/gamma/preapprovedloan/workflow"
)

type CommonPreProcessStage struct {
}

func (x *PreProcessRequest) GetVendor() string {
	if x != nil {
		return x.Vendor.String()
	}
	return ""
}

func (x *PreProcessRequest) GetLoanProgram() string {
	if x != nil {
		return x.LoanProgram.String()
	}
	return ""
}

func (x *PreProcessRequest) GetLoanHeader() *palpb.LoanHeader {
	if x != nil {
		return x.LoanHeader
	}
	return nil
}

func (x *PostProcessRequest) GetVendor() palpb.Vendor {
	if x != nil {
		return x.Vendor
	}
	return palpb.Vendor_VENDOR_UNSPECIFIED
}

func (x *PostProcessRequest) GetLoanProgram() palpb.LoanProgram {
	if x != nil {
		return x.LoanProgram
	}
	return palpb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED
}

// nolint:funlen
// PreProcess - does the common pre-processing needed to be done before every stage execution. It involves the following:
// 1. Initialization of workflow stage
// 2. Creation of loan step execution for the stage
func (cs *CommonPreProcessStage) PreProcess(ctx workflow.Context, req *PreProcessRequest) (*PreProcessResponse, error) {
	var (
		err error
		res = &PreProcessResponse{}
	)

	celRemVer := workflow.GetVersion(ctx, "remove-celestial-stage-activities", workflow.DefaultVersion, 1)
	if celRemVer != 1 {
		err = celestialPkg.InitiateWorkflowStage(ctx, req.CelestialStage, stagePb.Status_INITIATED)
		if err != nil {
			return nil, fmt.Errorf("failed to initiated workflow stage: %s: %w", req.CelestialStage, err)
		}
	}

	actReq := &palActivityPb.CreateLoanStepExecutionRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
			Ownership:   epificontext.OwnershipFromContext(ctx),
		},
		StepName:   req.StageName,
		Flow:       req.FlowName,
		GroupStage: req.GroupStage,
	}

	actRes := &palActivityPb.CreateLoanStepExecutionResponse{}
	localActVer := workflow.GetVersion(ctx, "move-lightweight-activities-to-local", workflow.DefaultVersion, 1)
	if localActVer != 1 {
		err = activityPkg.Execute(ctx, preApprovedLoanNs.CreateLoanStepExecution, actRes, actReq)
		if err != nil {
			return nil, err
		}
	} else {
		err = activityPkg.ExecuteLocally(ctx, preApprovedLoanNs.CreateLoanStepExecution, actRes, actReq)
		if err != nil {
			return nil, err
		}
	}

	// instrument loan execution step initiated metric for improving visibility.
	if !workflow.IsReplaying(ctx) {
		metrics.RecordLoanStepExecutionStatusUpdate(req.GetVendor(), req.GetLoanProgram(), req.FlowName.String(), req.StageName.String(), palpb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED.String(), palpb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED.String())
	}

	v := workflow.GetVersion(ctx, "add_polling_screen_in_lr_next_action", workflow.DefaultVersion, 2)
	if v <= 1 || !req.SkipUpdateNextAction {
		updateActReq := &palActivityPb.UpdateLRNextActionRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
				Ownership:   epificontext.OwnershipFromContext(ctx),
			},
			LoanRequestId: actRes.GetLoanStep().GetRefId(),
			LoanStep:      actRes.GetLoanStep(),
			// status poll for both pre-approved loan and early salary as placeholder
			NextAction: &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_APPLICATION_STATUS_POLL_SCREEN},
		}
		updateActRes := &palActivityPb.PalActivityResponse{}
		if localActVer != 1 {
			updateErr := activityPkg.Execute(ctx, preApprovedLoanNs.UpdateLRNextAction, updateActRes, updateActReq)
			if updateErr != nil {
				return nil, fmt.Errorf("UpdateLoanStepExecution activity failed: %w", updateErr)
			}
		} else {
			updateErr := activityPkg.ExecuteLocally(ctx, preApprovedLoanNs.UpdateLRNextAction, updateActRes, updateActReq)
			if updateErr != nil {
				return nil, fmt.Errorf("UpdateLoanStepExecution activity failed: %w", updateErr)
			}
		}
	}

	vendor := req.GetVendor()
	loanProgram := req.GetLoanProgram()
	eventReq := &palActivityPb.EmitEventRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
			Ownership:   epificontext.OwnershipFromContext(ctx),
		},
		Lse:         actRes.GetLoanStep(),
		EventName:   events.StageInitiated,
		Vendor:      vendor,
		LoanProgram: loanProgram,
	}
	eventRes := &palActivityPb.EmitEventResponse{}
	lg := workflow.GetLogger(ctx)
	if localActVer != 1 {
		eventErr := activityPkg.Execute(ctx, preApprovedLoanNs.EmitEvent, eventRes, eventReq)
		if eventErr != nil {
			lg.Error("activity to emit request events failed", eventReq.GetEventName(), eventReq.GetLse().GetStepName())
		}
	} else {
		eventErr := activityPkg.ExecuteLocally(ctx, preApprovedLoanNs.EmitEvent, eventRes, eventReq)
		if eventErr != nil {
			lg.Error("activity to emit request events failed", eventReq.GetEventName(), eventReq.GetLse().GetStepName())
		}
	}

	// logging stage initiated to build kibana waterfall for realtime tracking
	// TODO(team): remove duplicate logging once more clarity on if not indexed fields can be used for dashboard
	logMessage := fmt.Sprintf("stage initiated: %v, loan program: %v, vendor: %v", string(req.CelestialStage), req.LoanProgram, req.Vendor)
	lg.Info(logMessage, zap.String("stage name", string(req.CelestialStage)),
		zap.String("loanProgram", req.LoanProgram.String()), zap.String("vendor", req.Vendor.String()))

	res.loanStep = actRes.GetLoanStep()
	return res, nil

}

type CommonPostProcessStage struct {
}

// nolint:funlen,dupl
// PostProcess - does the common post-processing needed to be done after every stage execution. It involves the following:
// 1. Updating the workflow stage status
// 2. Publishing workflow update event if workflow status is terminal
// 3. Update loan step execution status
func (cs *CommonPostProcessStage) PostProcess(ctx workflow.Context, req *PostProcessRequest) (*PostProcessResponse, error) {
	res := &PostProcessResponse{}
	lg := workflow.GetLogger(ctx)
	var err error
	workflowStageStatus := helper.GetWorkflowStageStatusFromStatus(req.Request.GetLoanStep().GetStatus())
	celRemVer := workflow.GetVersion(ctx, "remove-celestial-stage-activities", workflow.DefaultVersion, 1)
	if celRemVer != 1 {
		err = celestialPkg.UpdateWorkflowStage(ctx, req.CelestialStage, workflowStageStatus)
		if err != nil {
			// NOTE: returning error here as this is an unexpected platform failure. We expect an alert here
			return nil, fmt.Errorf("failed to update workflow stage: %s: %w", req.CelestialStage, err)
		}
	}

	if req.Request.GetLoanStep() != nil {
		loanStep := req.Request.GetLoanStep()

		req.Request.LseFieldMasks = append(req.Request.GetLseFieldMasks(), palpb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS)
		switch workflowStageStatus {
		case stagePb.Status_MANUAL_INTERVENTION:
			req.Request.GetLoanStep().Status = palpb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION
		case stagePb.Status_SUCCESSFUL,
			stagePb.Status_FAILED:
			req.Request.GetLoanStep().CompletedAt = timestampPb.Now()
			req.Request.LseFieldMasks = append(req.Request.GetLseFieldMasks(), palpb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_COMPLETED_AT)
		}

		// instrument granular status updates for loan execution step execution for improving visibility.
		if !workflow.IsReplaying(ctx) {
			metrics.RecordLoanStepExecutionStatusUpdate(req.Vendor.String(), req.LoanProgram.String(), loanStep.GetFlow().String(), loanStep.GetStepName().String(), loanStep.GetStatus().String(), loanStep.GetSubStatus().String())
		}

		actReq := &palActivityPb.UpdateLoanStepExecutionRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
				Ownership:   epificontext.OwnershipFromContext(ctx),
			},
			LoanStep:      req.Request.GetLoanStep(),
			LseFieldMasks: req.Request.GetLseFieldMasks(),
		}
		actRes := &palActivityPb.PalActivityResponse{}
		localActVer := workflow.GetVersion(ctx, "move-lightweight-activities-to-local", workflow.DefaultVersion, 1)
		if localActVer != 1 {
			updateErr := activityPkg.Execute(ctx, preApprovedLoanNs.UpdateLoanStepExecution, actRes, actReq)
			if updateErr != nil {
				return nil, fmt.Errorf("UpdateLoanStepExecution activity failed: %w", updateErr)
			}
		} else {
			updateErr := activityPkg.ExecuteLocally(ctx, preApprovedLoanNs.UpdateLoanStepExecution, actRes, actReq)
			if updateErr != nil {
				return nil, fmt.Errorf("UpdateLoanStepExecution activity failed: %w", updateErr)
			}
		}
	}

	// deactivate the loan offer if needed
	deactivateOfferChangeVersion := workflow.GetVersion(ctx, "deactivate_loan_offer_based_on_lse", workflow.DefaultVersion, 1)
	if deactivateOfferChangeVersion == 1 {
		deactivateOfferReq := &palActivityPb.DeactivateLoanOfferRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
				Ownership:   epificontext.OwnershipFromContext(ctx),
			},
			LoanStep: req.Request.GetLoanStep(),
		}
		deactivateOfferRes := &palActivityPb.DeactivateLoanOfferResponse{}
		localActVer := workflow.GetVersion(ctx, "move-lightweight-activities-to-local", workflow.DefaultVersion, 1)
		if localActVer != 1 {
			deactivateOfferErr := activityPkg.Execute(ctx, preApprovedLoanNs.DeactivateLoanOffer, deactivateOfferRes, deactivateOfferReq)
			if deactivateOfferErr != nil {
				lg.Error("activity to deactivate loan offer failed ", zap.Error(deactivateOfferErr))
			}
		} else {
			deactivateOfferErr := activityPkg.ExecuteLocally(ctx, preApprovedLoanNs.DeactivateLoanOffer, deactivateOfferRes, deactivateOfferReq)
			if deactivateOfferErr != nil {
				lg.Error("activity to deactivate loan offer failed ", zap.Error(deactivateOfferErr))
			}
		}
	}

	lrNextActionUpdateVersion := workflow.GetVersion(ctx, "lr_next_action_update_version", workflow.DefaultVersion, 1)
	if lrNextActionUpdateVersion == 0 {
		err = updateLrNextActionV0(ctx, req, workflowStageStatus)
		if err != nil {
			lg.Error("failed to update LR next action, v0", zap.Error(err))
			return nil, fmt.Errorf("UpdateLoanStepExecution v0 activity failed, err: %w, WfReqId: %v, status: %s", err, req.WfReqId, workflowStageStatus.String())
		}
	} else {
		err = updateLrNextActionV1(ctx, req, workflowStageStatus)
		if err != nil {
			lg.Error("failed to update LR next action, v1", zap.Error(err))
			return nil, fmt.Errorf("UpdateLoanStepExecution v1 activity failed, err: %w, WfReqId: %v, status: %s", err, req.WfReqId, workflowStageStatus.String())
		}
	}

	vendor := req.GetVendor().String()
	loanProgram := req.GetLoanProgram().String()
	eventReq := &palActivityPb.EmitEventRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
			Ownership:   epificontext.OwnershipFromContext(ctx),
		},
		Lse:         req.Request.GetLoanStep(),
		EventName:   events.StageCompleted,
		Vendor:      vendor,
		LoanProgram: loanProgram,
	}
	eventRes := &palActivityPb.EmitEventResponse{}
	localActVer := workflow.GetVersion(ctx, "move-lightweight-activities-to-local", workflow.DefaultVersion, 1)
	if localActVer != 1 {
		eventErr := activityPkg.Execute(ctx, preApprovedLoanNs.EmitEvent, eventRes, eventReq)
		if eventErr != nil {
			lg.Info("activity to emit response events failed ", eventReq.GetEventName(), eventReq.GetLse().GetStepName())
		}
	} else {
		eventErr := activityPkg.ExecuteLocally(ctx, preApprovedLoanNs.EmitEvent, eventRes, eventReq)
		if eventErr != nil {
			lg.Info("activity to emit response events failed ", eventReq.GetEventName(), eventReq.GetLse().GetStepName())
		}
	}

	if workflowStageStatus == stagePb.Status_SUCCESSFUL {
		// logging stage initiated to build kibana waterfall for realtime tracking
		// TODO(team): remove duplicate logging once more clarity on if not indexed fields can be used for dashboard
		logMessage := fmt.Sprintf("stage completed successfully: %v, loan program: %v, vendor: %v", string(req.CelestialStage), req.LoanProgram, req.Vendor)
		lg.Info(logMessage, zap.String("stage name", string(req.CelestialStage)),
			zap.String("loanProgram", req.LoanProgram.String()), zap.String("vendor", req.Vendor.String()))

		return res, nil
	}

	// adding this log to debug the issue in case stage failures spikes
	lg.Info("failure in stage execution", zap.String(logger.LOAN_PROGRAM, req.LoanProgram.String()), zap.String(logger.VENDOR, req.Vendor.String()),
		zap.String(logger.STAGE, req.Request.GetLoanStep().GetStepName().String()), zap.String(logger.STATUS, req.Request.GetLoanStep().GetStatus().String()),
		zap.String(logger.SUB_STATUS, req.Request.GetLoanStep().GetSubStatus().String()), zap.String(logger.CLIENT_REQUEST_ID, req.WfProcessingParams.GetClientReqId().GetId()))

	if workflowStageStatus == stagePb.Status_MANUAL_INTERVENTION {
		return nil, epifitemporal.NewTransientError(errors.New("manual intervention"))
	}

	// non-successful state so sending error as need to fail the workflow
	return nil, fmt.Errorf("workflow stage failed, terminating workflow, WfReqId: %v, failure: %s", req.WfReqId, workflowStageStatus.String())
}

func updateLrNextActionV0(ctx workflow.Context, req *PostProcessRequest, workflowStageStatus stagePb.Status) error {
	var nextAction *deeplinkPb.Deeplink
	var err error
	lh := &palFeEnumsPb.LoanHeader{
		LoanProgram: dlHelper.GetFeLoanProgramFromBe(req.GetLoanProgram()),
		Vendor:      dlHelper.GetPalFeVendorFromBe(req.GetVendor()),
	}
	// default handling to set next action in post process according to the response LSE status.
	if workflowStageStatus == stagePb.Status_FAILED || workflowStageStatus == stagePb.Status_MANUAL_INTERVENTION {
		nextAction, err = dlHelper.GetApplicationFailureDeeplink(lh, req.Request.GetLoanStep(), workflowStageStatus)
		if err != nil {
			return fmt.Errorf("error while generating application failure deeplink : %w", err)
		}
	}
	if workflowStageStatus == stagePb.Status_SUCCESSFUL {
		nextAction = &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_APPLICATION_STATUS_POLL_SCREEN}
	}
	actReq := &palActivityPb.UpdateLRNextActionRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
			Ownership:   epificontext.OwnershipFromContext(ctx),
		},
		LoanRequestId: req.Request.GetLoanStep().GetRefId(),
		NextAction:    nextAction,
		LoanStep:      req.Request.GetLoanStep(),
	}
	// in case, any stage wants to have default handling to set in next action, any stage can return respective deeplink,
	// which overwrites the default handling
	if req.Request.GetNextAction() != nil {
		actReq.NextAction = req.Request.GetNextAction()
	}
	actRes := &palActivityPb.PalActivityResponse{}
	// SkipNextActionUpdate gives any stage the feasibility to not update the next action and can set it via any activity
	v := workflow.GetVersion(ctx, "update_lr_next_action_post_process_skip_check", workflow.DefaultVersion, 1)
	if req.Request != nil && !req.Request.GetSkipNextActionUpdate() || v == 0 {
		localActVer := workflow.GetVersion(ctx, "move-lightweight-activities-to-local", workflow.DefaultVersion, 1)
		if localActVer != 1 {
			updateErr := activityPkg.Execute(ctx, preApprovedLoanNs.UpdateLRNextAction, actRes, actReq)
			if updateErr != nil {
				return fmt.Errorf("UpdateLoanStepExecution activity failed: %w", updateErr)
			}
		} else {
			updateErr := activityPkg.ExecuteLocally(ctx, preApprovedLoanNs.UpdateLRNextAction, actRes, actReq)
			if updateErr != nil {
				return fmt.Errorf("UpdateLoanStepExecution activity failed: %w", updateErr)
			}
		}
	}
	return nil
}

//nolint:funlen
func updateLrNextActionV1(ctx workflow.Context, req *PostProcessRequest, workflowStageStatus stagePb.Status) error {
	if req.Request == nil {
		return nil
	}
	if req.Request.GetSkipNextActionUpdate() {
		return nil
	}
	actReq := &palActivityPb.UpdateLRNextActionRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
			Ownership:   epificontext.OwnershipFromContext(ctx),
		},
		LoanRequestId: req.Request.GetLoanStep().GetRefId(),
		LoanStep:      req.Request.GetLoanStep(),
	}
	switch workflowStageStatus {
	case stagePb.Status_SUCCESSFUL:
		actReq.NextAction = &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_APPLICATION_STATUS_POLL_SCREEN}
		// in case, any stage wants to have default handling to set in next action, any stage can return respective deeplink,
		// which overwrites the default handling
		if req.Request.GetNextAction() != nil {
			actReq.NextAction = req.Request.GetNextAction()
		}
		actRes := &palActivityPb.PalActivityResponse{}
		localActVer := workflow.GetVersion(ctx, "move-lightweight-activities-to-local", workflow.DefaultVersion, 1)
		if localActVer != 1 {
			updateErr := activityPkg.Execute(ctx, preApprovedLoanNs.UpdateLRNextAction, actRes, actReq)
			if updateErr != nil {
				return fmt.Errorf("UpdateLoanStepExecution activity failed: %w", updateErr)
			}
		} else {
			updateErr := activityPkg.ExecuteLocally(ctx, preApprovedLoanNs.UpdateLRNextAction, actRes, actReq)
			if updateErr != nil {
				return fmt.Errorf("UpdateLoanStepExecution activity failed: %w", updateErr)
			}
		}
	case stagePb.Status_FAILED:
		// in case, any stage wants to have default handling to set in next action, any stage can return respective deeplink,
		// which overwrites the default handling
		if req.Request.GetNextAction() != nil {
			actReq.NextAction = req.Request.GetNextAction()
		}
		actRes := &palActivityPb.PalActivityResponse{}
		updateErr := activityPkg.Execute(ctx, preApprovedLoanNs.GetAlternateOfferOrFailNextAction, actRes, actReq)
		if updateErr != nil {
			return fmt.Errorf("UpdateLoanStepExecution activity failed: %w", updateErr)
		}
	case stagePb.Status_MANUAL_INTERVENTION:
		lh := &palFeEnumsPb.LoanHeader{
			LoanProgram: dlHelper.GetFeLoanProgramFromBe(req.GetLoanProgram()),
			Vendor:      dlHelper.GetPalFeVendorFromBe(req.GetVendor()),
		}
		nextAction, err := dlHelper.GetApplicationFailureDeeplink(lh, req.Request.GetLoanStep(), workflowStageStatus)
		if err != nil {
			return fmt.Errorf("error while generating application failure deeplink : %w", err)
		}
		actReq.NextAction = nextAction
		// in case, any stage wants to have default handling to set in next action, any stage can return respective deeplink,
		// which overwrites the default handling
		if req.Request.GetNextAction() != nil {
			actReq.NextAction = req.Request.GetNextAction()
		}
		actRes := &palActivityPb.PalActivityResponse{}
		localActVer := workflow.GetVersion(ctx, "move-lightweight-activities-to-local", workflow.DefaultVersion, 1)
		if localActVer != 1 {
			updateErr := activityPkg.Execute(ctx, preApprovedLoanNs.UpdateLRNextAction, actRes, actReq)
			if updateErr != nil {
				return fmt.Errorf("UpdateLoanStepExecution activity failed: %w", updateErr)
			}
		} else {
			updateErr := activityPkg.ExecuteLocally(ctx, preApprovedLoanNs.UpdateLRNextAction, actRes, actReq)
			if updateErr != nil {
				return fmt.Errorf("UpdateLoanStepExecution activity failed: %w", updateErr)
			}
		}
	}
	return nil
}
