// nolint:dupl
package helper

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/gamma/api/frontend/analytics"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	palFeEnumsPb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	palEnums "github.com/epifi/gamma/api/preapprovedloan/enums"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/pan"
	palTypesPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	uiTypesV2 "github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/cx/chat"
	"github.com/epifi/gamma/frontend/preapprovedloan/ui"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

const (
	chevronDownIcon            = "https://epifi-icons.pointz.in/preapprovedloan/data-collections-screens/chevron-down.png"
	unionUspIcon               = "https://epifi-icons.pointz.in/preapprovedloan/data-collections-screens/privacy-usp-icon.png"
	mockPanImg                 = "https://epifi-icons.pointz.in/preapprovedloan/data-collections-screens/mock-pan-img.png"
	otherDetailsScreenHeroIcon = "https://epifi-icons.pointz.in/preapprovedloan/data-collections-screens/other-details-screen-i-icon.png"
	employmentBagIcon          = "https://epifi-icons.pointz.in/preapprovedloan/data-collections-screens/employment-bag.png"
	addressPostboxIcon         = "https://epifi-icons.pointz.in/preapprovedloan/data-collections-screens/address-postbox.png"
	loanReqMoneyGlobeIcon      = "https://epifi-icons.pointz.in/preapprovedloan/data-collections-screens/loan-requirement-money-globe.png"
	buildingUspIcon            = "https://epifi-icons.pointz.in/preapprovedloan/data-collections-screens/building-usp-icon.png"
	usersUspIcon               = "https://epifi-icons.pointz.in/preapprovedloan/data-collections-screens/users-usp-icon.png"
	fileUspIcon                = "https://epifi-icons.pointz.in/preapprovedloan/data-collections-screens/file-usp-icon.png"
	timerUspIcon               = "https://epifi-icons.pointz.in/preapprovedloan/data-collections-screens/timer-usp-icon.png"
	offersUspIcon              = "https://epifi-icons.pointz.in/preapprovedloan/data-collections-screens/offers-usp-icon.png"
	sparkleUspIcon             = "https://epifi-icons.pointz.in/preapprovedloan/data-collections-screens/sparkle-usp-icon.png"
	lightningUspIcon           = "https://epifi-icons.pointz.in/preapprovedloan/data-collections-screens/lightning-usp-icon.png"
	loansUspIcon               = "https://epifi-icons.pointz.in/preapprovedloan/data-collections-screens/loans-usp-icon.png"
	pinUspIcon                 = "https://epifi-icons.pointz.in/preapprovedloan/data-collections-screens/pin-usp-icon.png"
)

// MaritalStatusOption represents a marital status option with its enum and display text
type MaritalStatusOption struct {
	Enum        typesPb.MaritalStatus
	DisplayText string
}

// MaritalStatusOptions is the list of available marital status options
var MaritalStatusOptions = [...]MaritalStatusOption{
	{typesPb.MaritalStatus_MARRIED, "Married"},
	{typesPb.MaritalStatus_UNMARRIED, "Single"},
	{typesPb.MaritalStatus_MARITAL_STATUS_OTHERS, "Divorced"},
}

// GenderOption represents a gender option with its enum and display text
type GenderOption struct {
	Enum        commontypes.Gender
	DisplayText string
}

// GenderOptions is the list of available gender options
var GenderOptions = []GenderOption{
	{commontypes.Gender_MALE, "Male"},
	{commontypes.Gender_FEMALE, "Female"},
	{commontypes.Gender_OTHER, "Others"},
}

// LoanPurposeOption represents a loan purpose option with its enum and display text
type LoanPurposeOption struct {
	Enum        palEnums.LoanPurpose
	DisplayText string
}

// LoanPurposeOptions is the list of available loan purpose options
var LoanPurposeOptions = []LoanPurposeOption{
	{palEnums.LoanPurpose_LOAN_PURPOSE_HOME_RENOVATION, "Home renovation"},
	{palEnums.LoanPurpose_LOAN_PURPOSE_TRAVEL, "Travel"},
	{palEnums.LoanPurpose_LOAN_PURPOSE_EDUCATION, "Education/Upskilling"},
	{palEnums.LoanPurpose_LOAN_PURPOSE_WEDDING, "Wedding"},
	{palEnums.LoanPurpose_LOAN_PURPOSE_MEDICAL, "Medical emergency"},
	{palEnums.LoanPurpose_LOAN_PURPOSE_RELOCATION, "Relocation"},
	{palEnums.LoanPurpose_LOAN_PURPOSE_CONSOLIDATE_EXISTING_LOANS, "Consolidate existing loans"},
	{palEnums.LoanPurpose_LOAN_PURPOSE_INVESTMENT, "Investment"},
	{palEnums.LoanPurpose_LOAN_PURPOSE_OTHER, "Miscellanous expenses"},
}

// EmploymentTypeOption represents an employment type option with its enum and display text
type EmploymentTypeOption struct {
	Enum        typesPb.EmploymentType
	DisplayText string
}

// EmploymentTypeOptions is the list of available employment type options
var EmploymentTypeOptions = []EmploymentTypeOption{
	{typesPb.EmploymentType_EMPLOYMENT_TYPE_SALARIED, "Salaried Professional"},
	{typesPb.EmploymentType_EMPLOYMENT_TYPE_SELF_EMPLOYED, "Self Employed"},
	{typesPb.EmploymentType_EMPLOYMENT_TYPE_RETIRED, "Retired"},
	{typesPb.EmploymentType_EMPLOYMENT_TYPE_HOMEMAKER, "Homemaker"},
	{typesPb.EmploymentType_EMPLOYMENT_TYPE_UNEMPLOYED, "Unemployed"},
	{typesPb.EmploymentType_EMPLOYMENT_TYPE_OTHERS, "Others"},
}

// AddressTypeOption represents an address type option with its enum and display text
type AddressTypeOption struct {
	Enum        typesPb.ResidenceType
	DisplayText string
}

// AddressTypeOptions is the list of available address type options
var AddressTypeOptions = []AddressTypeOption{
	{typesPb.ResidenceType_RESIDENCE_TYPE_RENTED, "Rented"},
	{typesPb.ResidenceType_RESIDENCE_TYPE_OWNED, "Owned"},
	{typesPb.ResidenceType_RESIDENCE_TYPE_PAYING_GUEST, "PG"},
	{typesPb.ResidenceType_RESIDENCE_TYPE_EMPLOYER_PROVIDED, "Office Provided"},
}

const (
	eligibilityChatbotType        = "loans_eligibility"
	chatbotComponentIdentifierKey = "cp_component_identifier"
)

func GetChatbotEntryPointDeeplink(screenMetadata map[string]string) *deeplinkPb.Deeplink {
	metadata := map[string]string{
		chat.NuggetBot: "true",
		chat.BotType:   eligibilityChatbotType,
	}
	for k, v := range screenMetadata {
		metadata[k] = v
	}
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_CHAT_WITH_US_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_ChatWithUsScreenOptions{
			ChatWithUsScreenOptions: &deeplinkPb.ChatWithUsScreenOptions{
				ChatViewType: typesPb.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_NUGGET_CHATBOT_SDK,
				Metadata:     metadata,
			},
		},
	}
}

func getChatbotEntryPoint(lh *palFeEnumsPb.LoanHeader) *palTypesPb.ToolbarRightCta {
	return &palTypesPb.ToolbarRightCta{
		Icon:              commontypes.GetVisualElementFromUrlHeightAndWidth(ui.GreyMessageIcon, 24, 24),
		Title:             commontypes.GetTextFromStringFontColourFontStyle("Confused? Ask our expert", "#929599", commontypes.FontStyle_SUBTITLE_S),
		Deeplink:          GetChatbotEntryPointDeeplink(map[string]string{chatbotComponentIdentifierKey: lh.GetEventData().GetComponentIdentifier()}),
		BorderColor:       "#6BCDB6",
		BgColor:           "#FFFFFF",
		TitleDelaySeconds: 5,
	}
}

func GetPanDetailsDeeplink(lh *palFeEnumsPb.LoanHeader, lrId string, showFields []palEnums.FieldId, isHelpBotEnabled bool) *deeplinkPb.Deeplink {
	screenOptions := &palTypesPb.LoansFormEntryScreenOptions{
		Header: &deeplink_screen_option.ScreenOptionHeader{
			FeedbackEngineInfo: &header.FeedbackEngineInfo{
				FlowIdDetails: &header.FlowIdentifierDetails{
					FlowIdentifierType: typesPb.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_DROP_OFF,
					FlowIdentifier:     typesPb.FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_PAN_DETAILS.String(),
				},
			},
		},
		LoanHeader:          lh,
		LoanRequestId:       lrId,
		AnalyticsScreenName: analytics.AnalyticsScreenName_LOANS_FORM_DETAILS_SCREEN,
		TopBanner: &uiTypesV2.TopSection{
			Title:    GetText("Share your PAN details", "#313234", "", commontypes.FontStyle_HEADLINE_XL),
			Subtitle: GetText("This will help us fast track your loan", "#929599", "", commontypes.FontStyle_BODY_S),
			Image:    commontypes.GetVisualElementFromUrlHeightAndWidth(mockPanImg, 58, 86),
			BgColor: widget.GetLinearGradientBackgroundColour(90, []*widget.ColorStop{
				{Color: "#FFFFFF", StopPercentage: 0},
				{Color: "#F2FEFF", StopPercentage: 100},
			}),
			ProgressBarVisualisation: getDataCollectionProgressBar(5, 3),
		},
		UspCarousel: &uiTypesV2.USPCarouselComponent{
			BgColor: widget.GetLinearGradientBackgroundColour(271, []*widget.ColorStop{
				{Color: "#ECFBFF", StopPercentage: 0},
				{Color: "#ECFBFF", StopPercentage: 100},
			}),
			UspMessages: []*uiTypesV2.IconTextComponent{
				getUspITC("Quick identity check", lightningUspIcon),
				getUspITC("Eligibility check in under a minute", unionUspIcon),
				getUspITC("35 Lakh Indians trust Fi", usersUspIcon),
			},
		},
		FormFields: getPanDetailsFormFields(showFields),
		PrimaryCta: getDefaultPrimaryCta("Continue"),
	}
	if isHelpBotEnabled {
		screenOptions.TopRightCta = getChatbotEntryPoint(lh)
	}

	return deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LOANS_FORM_ENTRY_SCREEN, screenOptions)
}

func GetOtherDetailsDeeplink(lh *palFeEnumsPb.LoanHeader, lrId string, showFields []palEnums.FieldId, isHelpBotEnabled bool) *deeplinkPb.Deeplink {
	screenOptions := &palTypesPb.LoansFormEntryScreenOptions{
		Header: &deeplink_screen_option.ScreenOptionHeader{
			FeedbackEngineInfo: &header.FeedbackEngineInfo{
				FlowIdDetails: &header.FlowIdentifierDetails{
					FlowIdentifierType: typesPb.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_DROP_OFF,
					FlowIdentifier:     typesPb.FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_OTHER_DETAILS.String(),
				},
			},
		},
		LoanHeader:          lh,
		LoanRequestId:       lrId,
		AnalyticsScreenName: analytics.AnalyticsScreenName_LOANS_FORM_DETAILS_SCREEN,
		TopBanner: &uiTypesV2.TopSection{
			Title:    GetText("Other details", "#313234", "", commontypes.FontStyle_HEADLINE_XL),
			Subtitle: GetText("This will help optimise the experience for you ", "#929599", "", commontypes.FontStyle_BODY_S),
			Image:    commontypes.GetVisualElementFromUrlHeightAndWidth(otherDetailsScreenHeroIcon, 120, 120),
			BgColor: widget.GetLinearGradientBackgroundColour(90, []*widget.ColorStop{
				{Color: "#FFFFFF", StopPercentage: 0},
				{Color: "#F2FEFF", StopPercentage: 100},
			}),
			ProgressBarVisualisation: getDataCollectionProgressBar(5, 2),
		},
		UspCarousel: &uiTypesV2.USPCarouselComponent{
			BgColor: widget.GetLinearGradientBackgroundColour(271, []*widget.ColorStop{
				{Color: "#ECFBFF", StopPercentage: 0},
				{Color: "#ECFBFF", StopPercentage: 100},
			}),
			UspMessages: []*uiTypesV2.IconTextComponent{
				getUspITC("We match offers to your profile", loansUspIcon),
				getUspITC("Fast & secure verification", unionUspIcon),
				getUspITC("Few more details to your offer!", fileUspIcon),
			},
		},
		FormFields: getOtherDetailsFormFields(showFields),
		PrimaryCta: getDefaultPrimaryCta("Continue"),
	}
	if isHelpBotEnabled {
		screenOptions.TopRightCta = getChatbotEntryPoint(lh)
	}

	return deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LOANS_FORM_ENTRY_SCREEN, screenOptions)
}

func GetAddressDetailsDeeplink(lh *palFeEnumsPb.LoanHeader, lrId string, isHelpBotEnabled bool) *deeplinkPb.Deeplink {
	screenOptions := &palTypesPb.LoansFormEntryScreenOptions{
		Header: &deeplink_screen_option.ScreenOptionHeader{
			FeedbackEngineInfo: &header.FeedbackEngineInfo{
				FlowIdDetails: &header.FlowIdentifierDetails{
					FlowIdentifierType: typesPb.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_DROP_OFF,
					FlowIdentifier:     typesPb.FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_ADDRESS_DETAILS.String(),
				},
			},
		},
		LoanHeader:          lh,
		LoanRequestId:       lrId,
		AnalyticsScreenName: analytics.AnalyticsScreenName_LOANS_FORM_DETAILS_SCREEN,
		TopBanner: &uiTypesV2.TopSection{
			Title:    GetText("Where do you live?", "#313234", "", commontypes.FontStyle_HEADLINE_XL),
			Subtitle: GetText("Provide details of your current address", "#929599", "", commontypes.FontStyle_BODY_S),
			Image:    commontypes.GetVisualElementFromUrlHeightAndWidth(addressPostboxIcon, 120, 120),
			BgColor: widget.GetLinearGradientBackgroundColour(90, []*widget.ColorStop{
				{Color: "#FFFFFF", StopPercentage: 0},
				{Color: "#F2FEFF", StopPercentage: 100},
			}),
			ProgressBarVisualisation: getDataCollectionProgressBar(5, 0),
		},
		UspCarousel: &uiTypesV2.USPCarouselComponent{
			BgColor: widget.GetLinearGradientBackgroundColour(271, []*widget.ColorStop{
				{Color: "#ECFBFF", StopPercentage: 0},
				{Color: "#ECFBFF", StopPercentage: 100},
			}),
			UspMessages: []*uiTypesV2.IconTextComponent{
				getUspITC("Available in 2000+ cities", pinUspIcon),
				getUspITC("Relevant offers, based on where you are", offersUspIcon),
				getUspITC("Just a minute, promise!", timerUspIcon),
			},
		},
		FormFields: []*palTypesPb.CompositeFormField{
			{
				FieldType: palEnums.FieldId_PINCODE.String(),
				Field: &palTypesPb.CompositeFormField_IntegerInputFormField_{
					IntegerInputFormField: &palTypesPb.CompositeFormField_IntegerInputFormField{},
				},
				BorderColor:      "#D6D9DD",
				Label:            GetText("PINCODE", "#A4A4A4", "", commontypes.FontStyle_OVERLINE_XS_CAPS),
				Hint:             GetText("Pincode", "#929599", "", commontypes.FontStyle_SUBTITLE_M),
				EnteredTextStyle: GetText("", "#313234", "", commontypes.FontStyle_SUBTITLE_M),
			},
			// {
			// 	FieldType: palEnums.FieldId_ADDRESS_TYPE.String(),
			// 	Field: &palTypesPb.CompositeFormField_SelectableFormField_{
			// 		SelectableFormField: &palTypesPb.CompositeFormField_SelectableFormField{
			// 			ActionIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(chevronDownIcon, 28, 28),
			// 			Deeplink: deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LOANS_OPTION_SELECTION_BOTTOM_SHEET, &palTypesPb.LoansOptionSelectionScreenOption{
			// 				Title: GetText("Type of address", "#262728", "", commontypes.FontStyle_SUBTITLE_2),
			// 				OptionView: &uiTypesV2.OptionSelectionView{
			// 					Items: getAddressTypeOptions(),
			// 				},
			// 				PrimaryCta: &deeplinkPb.Cta{
			// 					Type: deeplinkPb.Cta_CUSTOM,
			// 					Text: "Submit",
			// 				},
			// 			}),
			// 		},
			// 	},
			// 	BorderColor:             "#D6D9DD",
			// 	Label:                   GetText("TYPE OF ADDRESS", "#A4A4A4", "", commontypes.FontStyle_OVERLINE_XS_CAPS),
			// 	Hint:                    GetText("Type of Address", "#929599", "", commontypes.FontStyle_SUBTITLE_M),
			// 	EnteredTextStyle:        GetText("", "#313234", "", commontypes.FontStyle_SUBTITLE_M),
			// 	AdditionalFetchRequired: true,
			// },
		},
		PrimaryCta:         getDefaultPrimaryCta("Continue"),
		IsLocationRequired: true,
	}
	if isHelpBotEnabled {
		screenOptions.TopRightCta = getChatbotEntryPoint(lh)
	}

	return deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LOANS_FORM_ENTRY_SCREEN, screenOptions)
}

func GetLoanRequirementDeeplink(lh *palFeEnumsPb.LoanHeader, lrId string, isHelpBotEnabled bool) *deeplinkPb.Deeplink {
	screenOptions := &palTypesPb.LoansFormEntryScreenOptions{
		Header: &deeplink_screen_option.ScreenOptionHeader{
			FeedbackEngineInfo: &header.FeedbackEngineInfo{
				FlowIdDetails: &header.FlowIdentifierDetails{
					FlowIdentifierType: typesPb.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_DROP_OFF,
					FlowIdentifier:     typesPb.FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_LOAN_REQUIREMENT_DETAILS.String(),
				},
			},
		},
		LoanHeader:          lh,
		LoanRequestId:       lrId,
		AnalyticsScreenName: analytics.AnalyticsScreenName_LOANS_FORM_DETAILS_SCREEN,
		TopBanner: &uiTypesV2.TopSection{
			Title:    GetText("Tell us about your requirement", "#313234", "", commontypes.FontStyle_HEADLINE_XL),
			Subtitle: GetText("To match you with the right lenders", "#929599", "", commontypes.FontStyle_BODY_S),
			Image:    commontypes.GetVisualElementFromUrlHeightAndWidth(loanReqMoneyGlobeIcon, 96, 109),
			BgColor: widget.GetLinearGradientBackgroundColour(90, []*widget.ColorStop{
				{Color: "#FFFFFF", StopPercentage: 0},
				{Color: "#F2FEFF", StopPercentage: 100},
			}),
			ProgressBarVisualisation: getDataCollectionProgressBar(5, 4),
		},
		UspCarousel: &uiTypesV2.USPCarouselComponent{
			BgColor: widget.GetLinearGradientBackgroundColour(271, []*widget.ColorStop{
				{Color: "#ECFBFF", StopPercentage: 0},
				{Color: "#ECFBFF", StopPercentage: 100},
			}),
			UspMessages: []*uiTypesV2.IconTextComponent{
				getUspITC("Get instant access to multiple loan offers", lightningUspIcon),
				getUspITC("Helps us match the right loan for you", sparkleUspIcon),
				getUspITC("Just two steps to go before offer!", timerUspIcon),
			},
		},
		FormFields: []*palTypesPb.CompositeFormField{
			// {
			// 	FieldType: palEnums.FieldId_LOAN_PURPOSE.String(),
			// 	Field: &palTypesPb.CompositeFormField_SelectableFormField_{
			// 		SelectableFormField: &palTypesPb.CompositeFormField_SelectableFormField{
			// 			ActionIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(chevronDownIcon, 28, 28),
			// 			Deeplink: deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LOANS_OPTION_SELECTION_BOTTOM_SHEET, &palTypesPb.LoansOptionSelectionScreenOption{
			// 				Title: GetText("Purpose of loan", "#262728", "", commontypes.FontStyle_SUBTITLE_2),
			// 				OptionView: &uiTypesV2.OptionSelectionView{
			// 					Items: getLoanPurposeOptions(),
			// 				},
			// 				PrimaryCta: &deeplinkPb.Cta{
			// 					Type: deeplinkPb.Cta_CUSTOM,
			// 					Text: "Submit",
			// 				},
			// 			}),
			// 		},
			// 	},
			// 	BorderColor:      "#D6D9DD",
			// 	Label:            GetText("PURPOSE OF LOAN", "#A4A4A4", "", commontypes.FontStyle_OVERLINE_XS_CAPS),
			// 	Hint:             GetText("Purpose of Loan", "#929599", "", commontypes.FontStyle_SUBTITLE_M),
			// 	EnteredTextStyle: GetText("", "#313234", "", commontypes.FontStyle_SUBTITLE_M),
			// },
			{
				FieldType: palEnums.FieldId_DESIRED_LOAN_AMOUNT.String(),
				Field: &palTypesPb.CompositeFormField_AmountSelectionFormField_{
					AmountSelectionFormField: &palTypesPb.CompositeFormField_AmountSelectionFormField{
						IsAmountEditable: true,
						MinAmount:        &typesPb.Money{CurrencyCode: "INR", Units: 15000},
						MaxAmount:        &typesPb.Money{CurrencyCode: "INR", Units: 1000000},
						Deeplink: GetIncomeSelectionDeeplink(&GetIncomeSelectionDeeplinkRequest{
							Lh:    lh,
							Title: "Desired loan amount",
							IncomeFrequencyLabel: &commontypes.Text{
								DisplayValue: &commontypes.Text_PlainString{PlainString: ""},
							},
							MinAmount: &typesPb.Money{CurrencyCode: "INR", Units: 15000},
							MaxAmount: &typesPb.Money{CurrencyCode: "INR", Units: 1000000},
						}),
					},
				},
				BorderColor:      "#D6D9DD",
				Label:            GetText("DESIRED AMOUNT", "#A4A4A4", "", commontypes.FontStyle_OVERLINE_XS_CAPS),
				Hint:             GetText("Desired amount", "#929599", "", commontypes.FontStyle_SUBTITLE_M),
				EnteredTextStyle: GetText("", "#313234", "", commontypes.FontStyle_SUBTITLE_M),
			},
		},
		PrimaryCta: getDefaultPrimaryCta("Continue"),
	}
	if isHelpBotEnabled {
		screenOptions.TopRightCta = getChatbotEntryPoint(lh)
	}

	return deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LOANS_FORM_ENTRY_SCREEN, screenOptions)
}

func GetEmploymentDetailsDeeplink(lh *palFeEnumsPb.LoanHeader, lrId string, isHelpBotEnabled bool) *deeplinkPb.Deeplink {
	screenOptions := &palTypesPb.LoansFormEntryScreenOptions{
		Header: &deeplink_screen_option.ScreenOptionHeader{
			FeedbackEngineInfo: &header.FeedbackEngineInfo{
				FlowIdDetails: &header.FlowIdentifierDetails{
					FlowIdentifierType: typesPb.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_DROP_OFF,
					FlowIdentifier:     typesPb.FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_EMPLOYMENT_DETAILS.String(),
				},
			},
		},
		LoanHeader:          lh,
		LoanRequestId:       lrId,
		AnalyticsScreenName: analytics.AnalyticsScreenName_LOANS_FORM_DETAILS_SCREEN,
		TopBanner: &uiTypesV2.TopSection{
			Title:    GetText("Add your employment details", "#313234", "", commontypes.FontStyle_HEADLINE_XL),
			Subtitle: GetText("To get you the best loan offers", "#929599", "", commontypes.FontStyle_BODY_S),
			Image:    commontypes.GetVisualElementFromUrlHeightAndWidth(employmentBagIcon, 120, 120),
			BgColor: widget.GetLinearGradientBackgroundColour(90, []*widget.ColorStop{
				{Color: "#FFFFFF", StopPercentage: 0},
				{Color: "#F2FEFF", StopPercentage: 100},
			}),
			ProgressBarVisualisation: getDataCollectionProgressBar(5, 1),
		},
		UspCarousel: &uiTypesV2.USPCarouselComponent{
			BgColor: widget.GetLinearGradientBackgroundColour(271, []*widget.ColorStop{
				{Color: "#ECFBFF", StopPercentage: 0},
				{Color: "#ECFBFF", StopPercentage: 100},
			}),
			UspMessages: []*uiTypesV2.IconTextComponent{
				getUspITC("Verify employment for eligibility", buildingUspIcon),
				getUspITC("Almost there – your offer is next!", unionUspIcon),
				getUspITC("35 Lakh Indians trust Fi", usersUspIcon),
			},
		},
		FormFields: []*palTypesPb.CompositeFormField{
			{
				FieldType: palEnums.FieldId_EMPLOYMENT_TYPE.String(),
				Field: &palTypesPb.CompositeFormField_SelectableFormField_{
					SelectableFormField: &palTypesPb.CompositeFormField_SelectableFormField{
						ActionIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(chevronDownIcon, 28, 28),
						Deeplink: deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LOANS_OPTION_SELECTION_BOTTOM_SHEET, &palTypesPb.LoansOptionSelectionScreenOption{
							Title: GetText("Select employment", "#262728", "", commontypes.FontStyle_SUBTITLE_2),
							OptionView: &uiTypesV2.OptionSelectionView{
								Items: getEmploymentTypeOptions(),
							},
							PrimaryCta: &deeplinkPb.Cta{
								Type: deeplinkPb.Cta_CUSTOM,
								Text: "Submit",
							},
						}),
					},
				},
				BorderColor:             "#D6D9DD",
				Label:                   GetText("EMPLOYMENT TYPE", "#A4A4A4", "", commontypes.FontStyle_OVERLINE_XS_CAPS),
				Hint:                    GetText("Employment Type", "#929599", "", commontypes.FontStyle_SUBTITLE_M),
				EnteredTextStyle:        GetText("", "#313234", "", commontypes.FontStyle_SUBTITLE_M),
				AdditionalFetchRequired: true,
			},
		},
		PrimaryCta: getDefaultPrimaryCta("Continue"),
	}
	if isHelpBotEnabled {
		screenOptions.TopRightCta = getChatbotEntryPoint(lh)
	}

	return deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LOANS_FORM_ENTRY_SCREEN, screenOptions)
}

// getGenderOptions returns a list of OptionSelectionItem for gender selection
func getGenderOptions() []*uiTypesV2.OptionSelectionItem {
	var items []*uiTypesV2.OptionSelectionItem
	for _, opt := range GenderOptions {
		items = append(items, &uiTypesV2.OptionSelectionItem{
			Id:         int64(opt.Enum),
			Identifier: opt.Enum.String(),
			OptionValue: &uiTypesV2.IconTextComponent{
				Texts: []*commontypes.Text{
					GetText(opt.DisplayText, "#646464", "", commontypes.FontStyle_SUBTITLE_3),
				},
			},
		})
	}
	return items
}

// getMaritalStatusOptions returns a list of OptionSelectionItem for marital status selection
func getMaritalStatusOptions() []*uiTypesV2.OptionSelectionItem {
	var items []*uiTypesV2.OptionSelectionItem
	for _, opt := range MaritalStatusOptions {
		items = append(items, &uiTypesV2.OptionSelectionItem{
			Id:         int64(opt.Enum),
			Identifier: opt.Enum.String(),
			OptionValue: &uiTypesV2.IconTextComponent{
				Texts: []*commontypes.Text{
					GetText(opt.DisplayText, "#646464", "", commontypes.FontStyle_SUBTITLE_3),
				},
			},
		})
	}
	return items
}

// getLoanPurposeOptions returns a list of OptionSelectionItem for loan purpose selection
func getLoanPurposeOptions() []*uiTypesV2.OptionSelectionItem {
	var items []*uiTypesV2.OptionSelectionItem
	for _, opt := range LoanPurposeOptions {
		items = append(items, &uiTypesV2.OptionSelectionItem{
			Id:         int64(opt.Enum),
			Identifier: opt.Enum.String(),
			OptionValue: &uiTypesV2.IconTextComponent{
				Texts: []*commontypes.Text{
					GetText(opt.DisplayText, "#646464", "", commontypes.FontStyle_SUBTITLE_3),
				},
			},
		})
	}
	return items
}

// getEmploymentTypeOptions returns a list of OptionSelectionItem for employment type selection
func getEmploymentTypeOptions() []*uiTypesV2.OptionSelectionItem {
	var items []*uiTypesV2.OptionSelectionItem
	for _, opt := range EmploymentTypeOptions {
		items = append(items, &uiTypesV2.OptionSelectionItem{
			Id:         int64(opt.Enum),
			Identifier: opt.Enum.String(),
			OptionValue: &uiTypesV2.IconTextComponent{
				Texts: []*commontypes.Text{
					GetText(opt.DisplayText, "#646464", "", commontypes.FontStyle_SUBTITLE_3),
				},
			},
		})
	}
	return items
}

// getAddressTypeOptions returns a list of OptionSelectionItem for address type selection
func getAddressTypeOptions() []*uiTypesV2.OptionSelectionItem {
	var items []*uiTypesV2.OptionSelectionItem
	for _, opt := range AddressTypeOptions {
		items = append(items, &uiTypesV2.OptionSelectionItem{
			Id:         int64(opt.Enum),
			Identifier: opt.Enum.String(),
			OptionValue: &uiTypesV2.IconTextComponent{
				Texts: []*commontypes.Text{
					GetText(opt.DisplayText, "#646464", "", commontypes.FontStyle_SUBTITLE_3),
				},
			},
		})
	}
	return items
}

// getPanDetailsFormFields returns form fields based on visibility options
func getPanDetailsFormFields(showFields []palEnums.FieldId) []*palTypesPb.CompositeFormField {
	var formFields []*palTypesPb.CompositeFormField

	for _, field := range showFields {
		if field == palEnums.FieldId_NAME {
			formFields = append(formFields, &palTypesPb.CompositeFormField{
				FieldType: palEnums.FieldId_NAME.String(),
				Field: &palTypesPb.CompositeFormField_StringInputFormField_{
					StringInputFormField: &palTypesPb.CompositeFormField_StringInputFormField{
						DefaultValue: "",
					},
				},
				BorderColor:      "#D6D9DD",
				Label:            GetText("NAME AS ON PAN CARD", "#A4A4A4", "", commontypes.FontStyle_OVERLINE_XS_CAPS),
				Hint:             GetText("Name as on PAN Card", "#929599", "", commontypes.FontStyle_SUBTITLE_M),
				EnteredTextStyle: GetText("", "#313234", "", commontypes.FontStyle_SUBTITLE_M),
			})
		}

		if field == palEnums.FieldId_DOB {
			formFields = append(formFields, &palTypesPb.CompositeFormField{
				FieldType: palEnums.FieldId_DOB.String(),
				Field: &palTypesPb.CompositeFormField_DateInputFormField_{
					DateInputFormField: &palTypesPb.CompositeFormField_DateInputFormField{},
				},
				BorderColor:      "#D6D9DD",
				Label:            GetText("DATE OF BIRTH (DD/MM/YYYY)", "#A4A4A4", "", commontypes.FontStyle_OVERLINE_XS_CAPS),
				Hint:             GetText("Date of birth (DD/MM/YYYY)", "#929599", "", commontypes.FontStyle_SUBTITLE_M),
				EnteredTextStyle: GetText("", "#313234", "", commontypes.FontStyle_SUBTITLE_M),
			})
		}

		if field == palEnums.FieldId_PAN {
			formFields = append(formFields, &palTypesPb.CompositeFormField{
				FieldType: palEnums.FieldId_PAN.String(),
				Field: &palTypesPb.CompositeFormField_StringInputFormField_{
					StringInputFormField: &palTypesPb.CompositeFormField_StringInputFormField{
						DefaultValue: "",
					},
				},
				BorderColor:      "#D6D9DD",
				Label:            GetText("PAN NUMBER", "#A4A4A4", "", commontypes.FontStyle_OVERLINE_XS_CAPS),
				Hint:             GetText("PAN Number", "#929599", "", commontypes.FontStyle_SUBTITLE_M),
				EnteredTextStyle: GetText("", "#313234", "", commontypes.FontStyle_SUBTITLE_M),
				BottomHelperCta: &uiTypesV2.IconTextComponent{
					Texts: []*commontypes.Text{
						GetText("I can't remember my PAN", "#00B899", "", commontypes.FontStyle_BODY_XS),
					},
					Deeplink: deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_HELPER_BOTTOM_SHEET, &pan.HelperBottomSheetScreenOptions{
						Title:     GetText("Find PAN details in your emails", "#333333", "", commontypes.FontStyle_HEADLINE_XL),
						Subtitle:  GetText("ITR notifications contain PAN number. Look for the following email in your inbox.", "#8D8D8D", "", commontypes.FontStyle_BODY_S),
						HeroImage: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/itr-intimation-email-search", 168, 364).WithImageType(commontypes.ImageType_PNG),
						CopyTags: []*pan.HelperBottomSheetScreenOptions_CopyTags{
							{
								Label:     GetText("Email Sender", "#929599", "", commontypes.FontStyle_HEADLINE_XS),
								CopyValue: GetText("<EMAIL>", "#313234", "", commontypes.FontStyle_HEADLINE_XS),
								CopyIcon:  commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/onboarding/add_funds_v2/copy.png", 20, 20).WithImageType(commontypes.ImageType_PNG),
								BgColor:   widget.GetBlockBackgroundColour("#FFFFFF"),
							},
						},
						Ctas: []*deeplinkPb.Cta{
							{
								Type:         deeplinkPb.Cta_DONE,
								Text:         "Cancel",
								DisplayTheme: deeplinkPb.Cta_SECONDARY,
							},
							{
								Type: deeplinkPb.Cta_CUSTOM,
								Text: "Open email app",
								Deeplink: &deeplinkPb.Deeplink{
									Screen: deeplinkPb.Screen_OPEN_EMAIL_APP_ACTION,
								},
								DisplayTheme: deeplinkPb.Cta_PRIMARY,
							},
						},
					}),
				},
			})
		}
	}

	return formFields
}

// getOtherDetailsFormFields returns form fields based on visibility options
func getOtherDetailsFormFields(showFields []palEnums.FieldId) []*palTypesPb.CompositeFormField {
	var formFields []*palTypesPb.CompositeFormField

	for _, field := range showFields {
		if field == palEnums.FieldId_GENDER {
			formFields = append(formFields, &palTypesPb.CompositeFormField{
				FieldType: palEnums.FieldId_GENDER.String(),
				Field: &palTypesPb.CompositeFormField_SelectableFormField_{
					SelectableFormField: &palTypesPb.CompositeFormField_SelectableFormField{
						ActionIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(chevronDownIcon, 28, 28),
						Deeplink: deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LOANS_OPTION_SELECTION_BOTTOM_SHEET, &palTypesPb.LoansOptionSelectionScreenOption{
							Title: GetText("Gender", "#262728", "", commontypes.FontStyle_SUBTITLE_2),
							OptionView: &uiTypesV2.OptionSelectionView{
								Items: getGenderOptions(),
							},
							PrimaryCta: &deeplinkPb.Cta{
								Type: deeplinkPb.Cta_CUSTOM,
								Text: "Submit",
							},
						}),
					},
				},
				BorderColor:      "#D6D9DD",
				Label:            GetText("GENDER", "#A4A4A4", "", commontypes.FontStyle_OVERLINE_XS_CAPS),
				Hint:             GetText("Gender", "#929599", "", commontypes.FontStyle_SUBTITLE_M),
				EnteredTextStyle: GetText("", "#313234", "", commontypes.FontStyle_SUBTITLE_M),
			})
		}

		if field == palEnums.FieldId_MARITAL_STATUS {
			formFields = append(formFields, &palTypesPb.CompositeFormField{
				FieldType: palEnums.FieldId_MARITAL_STATUS.String(),
				Field: &palTypesPb.CompositeFormField_SelectableFormField_{
					SelectableFormField: &palTypesPb.CompositeFormField_SelectableFormField{
						ActionIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(chevronDownIcon, 28, 28),
						Deeplink: deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LOANS_OPTION_SELECTION_BOTTOM_SHEET, &palTypesPb.LoansOptionSelectionScreenOption{
							Title: GetText("Marital Status", "#262728", "", commontypes.FontStyle_SUBTITLE_2),
							OptionView: &uiTypesV2.OptionSelectionView{
								Items: getMaritalStatusOptions(),
							},
							PrimaryCta: &deeplinkPb.Cta{
								Type: deeplinkPb.Cta_CUSTOM,
								Text: "Submit",
							},
						}),
					},
				},
				BorderColor:      "#D6D9DD",
				Label:            GetText("MARITAL STATUS", "#A4A4A4", "", commontypes.FontStyle_OVERLINE_XS_CAPS),
				Hint:             GetText("Marital Status", "#929599", "", commontypes.FontStyle_SUBTITLE_M),
				EnteredTextStyle: GetText("", "#313234", "", commontypes.FontStyle_SUBTITLE_M),
			})
		}
	}

	return formFields
}

func getUspITC(uspText, uspIcon string) *uiTypesV2.IconTextComponent {
	return &uiTypesV2.IconTextComponent{
		Texts: []*commontypes.Text{
			GetText(uspText, "#6294A6", "", commontypes.FontStyle_SUBTITLE_S),
		},
		LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(uspIcon, 24, 24),
		LeftImgTxtPadding: 12,
	}
}

// nolint:unparam
// getDefaultPrimaryCta returns the default primary CTA configuration with custom text
func getDefaultPrimaryCta(text string) *deeplinkPb.Cta {
	return &deeplinkPb.Cta{
		Type:         deeplinkPb.Cta_CUSTOM,
		Text:         text,
		DisplayTheme: deeplinkPb.Cta_PRIMARY,
		Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
	}
}

type GetIncomeSelectionDeeplinkRequest struct {
	Lh                   *palFeEnumsPb.LoanHeader
	DefaultInitialAmount *typesPb.Money
	Title                string
	BottomMessage        string
	IncomeFrequencyLabel *commontypes.Text
	MinAmount            *typesPb.Money
	MaxAmount            *typesPb.Money
}

func GetIncomeSelectionDeeplink(req *GetIncomeSelectionDeeplinkRequest) *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_PRE_APPROVED_INCOME_SELECTION_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_PreApprovedIncomeSelectionScreenOptions{
			PreApprovedIncomeSelectionScreenOptions: &deeplinkPb.PreApprovedIncomeSelectionScreenOptions{
				Title:                req.Title,
				BottomMessage:        req.BottomMessage,
				IncomeFrequencyLabel: req.IncomeFrequencyLabel,
				LoanHeader:           req.Lh,
				MinValue:             req.MinAmount,
				MaxValue:             req.MaxAmount,
				SubmitCta: &deeplinkPb.Button{
					Text: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: "Done",
						},
						FontColor: "#FFFFFF",
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_BUTTON_M,
						},
					},
				},
			},
		},
	}
}

// GetMaritalStatusDisplayText returns the display text for a given marital status enum
func GetMaritalStatusDisplayText(status typesPb.MaritalStatus) string {
	for _, opt := range MaritalStatusOptions {
		if opt.Enum == status {
			return opt.DisplayText
		}
	}
	return status.String() // fallback to enum string if not found
}

// GetGenderDisplayText returns the display text for a given gender enum
func GetGenderDisplayText(gender commontypes.Gender) string {
	for _, opt := range GenderOptions {
		if opt.Enum == gender {
			return opt.DisplayText
		}
	}
	return gender.String() // fallback to enum string if not found
}

// GetLoanPurposeDisplayText returns the display text for a given loan purpose enum
func GetLoanPurposeDisplayText(purpose palEnums.LoanPurpose) string {
	for _, opt := range LoanPurposeOptions {
		if opt.Enum == purpose {
			return opt.DisplayText
		}
	}
	return purpose.String() // fallback to enum string if not found
}

// GetEmploymentTypeDisplayText returns the display text for a given employment type enum
func GetEmploymentTypeDisplayText(empType typesPb.EmploymentType) string {
	for _, opt := range EmploymentTypeOptions {
		if opt.Enum == empType {
			return opt.DisplayText
		}
	}
	return empType.String() // fallback to enum string if not found
}

// GetAddressTypeDisplayText returns the display text for a given residence type enum
func GetAddressTypeDisplayText(residenceType typesPb.ResidenceType) string {
	for _, opt := range AddressTypeOptions {
		if opt.Enum == residenceType {
			return opt.DisplayText
		}
	}
	return residenceType.String() // fallback to enum string if not found
}

// nolint: unparam
func getDataCollectionProgressBar(numberOfProgressBar int, numberOfSuccessfulProgressBar int) []*uiTypesV2.ProgressVisualisation {
	if numberOfSuccessfulProgressBar > numberOfProgressBar {
		// handling edge case where successful progress bars exceed total progress bars
		numberOfSuccessfulProgressBar = numberOfProgressBar
	}

	var progressBars []*uiTypesV2.ProgressVisualisation

	for i := 0; i < numberOfProgressBar; i++ {
		color := "#D9F4F3" // default color
		if i < numberOfSuccessfulProgressBar {
			color = "#007A56" // success color
		}

		progressVisualisationObj := &uiTypesV2.ProgressVisualisation{
			CurrentProgress:  0,
			MaxProgress:      100,
			TrackWidth:       3,
			ProgressBarWidth: 3,
			TrackColor: &widget.BackgroundColour{
				Colour: &widget.BackgroundColour_BlockColour{
					BlockColour: color,
				},
			},
			VisualisationType: uiTypesV2.ProgressVisualisationType_PROGRESS_VISUALISATION_TYPE_LINE,
			Visualisation: &uiTypesV2.ProgressVisualisation_LineProgressVisualisation{
				LineProgressVisualisation: &uiTypesV2.LineProgressVisualisation{
					ProgressBarHeight: 3,
				},
			},
		}

		progressBars = append(progressBars, progressVisualisationObj)
	}

	return progressBars
}
