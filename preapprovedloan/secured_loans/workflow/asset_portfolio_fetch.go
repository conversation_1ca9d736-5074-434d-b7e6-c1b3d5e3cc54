package workflow

import (
	"fmt"

	"go.temporal.io/sdk/log"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	preApprovedLoanNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	"github.com/epifi/be-common/pkg/logger"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/preapprovedloan/activity"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	securedLoansPb "github.com/epifi/gamma/api/preapprovedloan/secured_loans/workflow"
	"github.com/epifi/gamma/preapprovedloan/events"
	"github.com/epifi/gamma/preapprovedloan/helper"
	"github.com/epifi/gamma/preapprovedloan/secured_loans/workflow/stage_executor_providers"
	"github.com/epifi/gamma/preapprovedloan/secured_loans/workflow/stage_executor_providers/providers"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
)

// AssetPortfolioFetch - workflow to orchestrate an asset portfolio fetch for a user. This workflow can be used to fetch portfolios for multiple asset types
// and from different vendors based on the request parameters.

// nolint:funlen,dupl,govet
func AssetPortfolioFetch(ctx workflow.Context, _ *workflowPb.Request) error {
	_ = workflow.GetVersion(ctx, "activity-error-v2-refactor", workflow.DefaultVersion, 2)
	mfcPortfolioFetchVersion := workflow.GetVersion(ctx, "mfc-portfolio-fetch-mandatory", workflow.DefaultVersion, 1)

	lg := workflow.GetLogger(ctx)
	// wfReqID := workflow.GetInfo(ctx).WorkflowExecution.ID

	var payload securedLoansPb.FetchAssetPortfolioPayload
	wfProcessingParams, err := celestialPkg.GetWorkflowProcessingReqParams(ctx, &payload)
	if err != nil {
		lg.Error("failed to fetch workflow processing params", zap.Error(err))
		return handleWorkflowError(ctx, err, wfProcessingParams.GetClientReqId().GetId(), epifitemporal.Stage(workflowPb.Stage_STAGE_UNSPECIFIED.String()))
	}
	ctx = epificontext.WorkflowContextWithOwnership(ctx, helper.GetPalOwnership(payload.GetVendor()))

	// update portfolio_fetch_request to pending state
	updateErr := updateLoanRequest(ctx, wfProcessingParams.GetClientReqId().GetId(), palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_PENDING)
	if updateErr != nil {
		lg.Error("failed to update PFR", zap.Error(updateErr))
		return handleWorkflowError(ctx, err, wfProcessingParams.GetClientReqId().GetId(), epifitemporal.Stage(workflowPb.Stage_STAGE_UNSPECIFIED.String()))
	}

	// initiate the new celestial stage for pre-closure of loan
	err = celestialPkg.InitiateWorkflowStage(ctx, palNs.StagePortfolioFetch, stagePb.Status_INITIATED)
	if err != nil {
		lg.Error("error in initiating celestial stage", zap.Error(err), zap.String(logger.STAGE, string(palNs.StagePortfolioFetch)))
		return handleWorkflowError(ctx, err, wfProcessingParams.GetClientReqId().GetId(), epifitemporal.Stage(workflowPb.Stage_STAGE_UNSPECIFIED.String()))
	}

	createLseRes, err := createPortfolioFetchLse(ctx, wfProcessingParams)
	if err != nil {
		lg.Error("error while creating loan step execution entry", zap.Error(err))
		return handleWorkflowError(ctx, err, wfProcessingParams.GetClientReqId().GetId(), palNs.StagePortfolioFetch)
	}

	portfolioFetchExecutor, err := stage_executor_providers.GetPortfolioFetchStageExecutor(ctx, stage_executor_providers.GetPortfolioFetchStageExecutorRequest{
		Vendor:      payload.GetVendor(),
		LoanProgram: payload.GetLoanProgram(),
	})
	if err != nil {
		lg.Error("failed to get the stage executor", zap.Error(err))
		return handleWorkflowError(ctx, errors.Wrap(err, "failed to get the stage executor"), wfProcessingParams.GetClientReqId().GetId(), palNs.StagePortfolioFetch)
	}

	if mfcPortfolioFetchVersion == 1 {
		return assetPortfolioFetchWithMfc(ctx, lg, wfProcessingParams, createLseRes, portfolioFetchExecutor, &payload)
	} else {
		return assetPortfolioFetchV1(ctx, lg, wfProcessingParams, createLseRes, portfolioFetchExecutor, &payload)
	}
}

// nolint:govet
func assetPortfolioFetchWithMfc(
	ctx workflow.Context,
	lg log.Logger,
	wfProcessingParams *workflowPb.ProcessingParams,
	createLseRes *palActivityPb.CreateLoanStepExecutionResponse,
	portfolioFetchExecutor providers.IPortfolioFetchExecutor,
	payload *securedLoansPb.FetchAssetPortfolioPayload,
) error {
	portfolioFetchSuccessEventVersion := workflow.GetVersion(ctx, "portfolio-fetch-success-event", workflow.DefaultVersion, 1)
	actReq := &palActivityPb.GetLoanRequestActivityRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: wfProcessingParams.GetClientReqId().GetId(),
		},
		LoanRequestId: createLseRes.GetLoanStep().GetRefId(),
	}
	actRes := &palActivityPb.GetLoanRequestActivityResponse{}
	actErr := activityPkg.Execute(ctx, palNs.GetLoanRequest, actRes, actReq)
	if actErr != nil {
		return fmt.Errorf("failure in GetLoanRequest activity: %w", actErr)
	}
	portfolioFetchDetails := actRes.GetLoanRequest().GetDetails().GetPortfolioFetchDetails().GetFiftyfinLamfDetails()

	if !portfolioFetchDetails.GetSkipAccountDetailUpdate() {
		// Step1 : fetch the executor for OnboardOrUpdateUser stage and execute that stage
		_, err := portfolioFetchExecutor.OnboardOrUpdateUser(ctx, &providers.OnboardOrUpdateUserRequest{
			OrchId:      wfProcessingParams.GetClientReqId().GetId(),
			LoanStep:    createLseRes.GetLoanStep(),
			Vendor:      payload.GetVendor(),
			LoanProgram: payload.GetLoanProgram(),
		})
		if err != nil {
			lg.Error("failed to execute the onboardUser stage", zap.Error(err))
			return handleWorkflowError(ctx, err, wfProcessingParams.GetClientReqId().GetId(), palNs.StagePortfolioFetch)
		}
	}

	// Step2 : fetch the executor for FetchPortfolio stage and execute that stage
	_, err := portfolioFetchExecutor.FetchPortfolio(ctx, &providers.FetchPortfolioRequest{
		OrchId:                wfProcessingParams.GetClientReqId().GetId(),
		LoanStep:              createLseRes.GetLoanStep(),
		Vendor:                payload.GetVendor(),
		LoanProgram:           payload.GetLoanProgram(),
		PortfolioFetchDetails: portfolioFetchDetails,
	})
	if err != nil {
		// not returning from here as we need to mark the workflow to terminal state
		lg.Error("failed to execute the FetchPortfolio stage", zap.Error(err))
	}

	stageErr := handleWorkflowError(ctx, err, wfProcessingParams.GetClientReqId().GetId(), palNs.StagePortfolioFetch)
	if stageErr != nil {
		return stageErr
	}

	if portfolioFetchSuccessEventVersion == 1 {
		eventReq := &palActivityPb.EmitEventRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: wfProcessingParams.GetClientReqId().GetId(),
				Ownership:   epificontext.OwnershipFromContext(ctx),
			},
			Lse:         createLseRes.GetLoanStep(),
			EventName:   events.StageCompleted,
			Vendor:      actRes.GetLoanRequest().GetVendor().String(),
			LoanProgram: actRes.GetLoanRequest().GetLoanProgram().String(),
		}
		eventRes := &palActivityPb.EmitEventResponse{}
		localActVer := workflow.GetVersion(ctx, "move-lightweight-activities-to-local", workflow.DefaultVersion, 1)
		if localActVer != 1 {
			eventErr := activityPkg.Execute(ctx, preApprovedLoanNs.EmitEvent, eventRes, eventReq)
			if eventErr != nil {
				lg.Info("activity to emit portfolio fetch response events failed", eventReq.GetEventName(), eventReq.GetLse().GetStepName())
			}
		} else {
			eventErr := activityPkg.ExecuteLocally(ctx, preApprovedLoanNs.EmitEvent, eventRes, eventReq)
			if eventErr != nil {
				lg.Info("activity to emit portfolio fetch response events failed", eventReq.GetEventName(), eventReq.GetLse().GetStepName())
			}
		}
	}

	return nil
}

// nolint:govet
func assetPortfolioFetchV1(
	ctx workflow.Context,
	lg log.Logger,
	wfProcessingParams *workflowPb.ProcessingParams,
	createLseRes *palActivityPb.CreateLoanStepExecutionResponse,
	portfolioFetchExecutor providers.IPortfolioFetchExecutor,
	payload *securedLoansPb.FetchAssetPortfolioPayload,
) error {
	// Step1 : fetch the executor for OnboardOrUpdateUser stage and execute that stage
	_, err := portfolioFetchExecutor.OnboardOrUpdateUser(ctx, &providers.OnboardOrUpdateUserRequest{
		OrchId:      wfProcessingParams.GetClientReqId().GetId(),
		LoanStep:    createLseRes.GetLoanStep(),
		Vendor:      payload.GetVendor(),
		LoanProgram: payload.GetLoanProgram(),
	})
	if err != nil {
		lg.Error("failed to execute the onboardUser stage", zap.Error(err))
		return handleWorkflowError(ctx, err, wfProcessingParams.GetClientReqId().GetId(), palNs.StagePortfolioFetch)
	}

	// Step2 : fetch the executor for FetchPortfolio stage and execute that stage
	_, err = portfolioFetchExecutor.FetchPortfolio(ctx, &providers.FetchPortfolioRequest{
		OrchId:      wfProcessingParams.GetClientReqId().GetId(),
		LoanStep:    createLseRes.GetLoanStep(),
		Vendor:      payload.GetVendor(),
		LoanProgram: payload.GetLoanProgram(),
	})
	if err != nil {
		// not returning from here as we need to mark the workflow to terminal state
		lg.Error("failed to execute the FetchPortfolio stage", zap.Error(err))
	}

	return handleWorkflowError(ctx, err, wfProcessingParams.GetClientReqId().GetId(), palNs.StagePortfolioFetch)
}

func updateLoanRequest(ctx workflow.Context, orchId string, status palPb.LoanRequestStatus) error {
	actReq := &activity.UpdateLoanRequestActivityV2Request{
		LoanRequestStatus: status,
		OrchId:            orchId,
	}
	actRes := &activity.UpdateLoanRequestActivityV2Response{}
	updateErr := activityPkg.Execute(ctx, preApprovedLoanNs.UpdateLoanRequestV2, actRes, actReq)
	if updateErr != nil {
		return fmt.Errorf("UpdateLoanRequestV2 activity failed: %w", updateErr)
	}
	return nil
}

func createPortfolioFetchLse(ctx workflow.Context, wfParams *workflowPb.ProcessingParams) (*palActivityPb.CreateLoanStepExecutionResponse, error) {
	actReq := &palActivityPb.CreateLoanStepExecutionRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: wfParams.GetClientReqId().GetId(),
			Ownership:   epificontext.OwnershipFromContext(ctx),
		},
		StepName: palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PORTFOLIO_FETCH,
		Flow:     palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_PORTFOLIO_FETCH,
	}

	var err error
	actRes := &palActivityPb.CreateLoanStepExecutionResponse{}
	localActVer := workflow.GetVersion(ctx, "move-lightweight-activities-to-local", workflow.DefaultVersion, 1)
	if localActVer != 1 {
		err = activityPkg.Execute(ctx, preApprovedLoanNs.CreateLoanStepExecution, actRes, actReq)
		if err != nil {
			return nil, errors.Wrap(err, "lse creation failed")
		}
	} else {
		err = activityPkg.ExecuteLocally(ctx, preApprovedLoanNs.CreateLoanStepExecution, actRes, actReq)
		if err != nil {
			return nil, errors.Wrap(err, "lse creation failed")
		}
	}

	// create lse details object
	updateReq := &palActivityPb.UpdateLoanStepExecutionRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: wfParams.GetClientReqId().GetId(),
			Ownership:   epificontext.OwnershipFromContext(ctx),
		},
		LoanStep: &palPb.LoanStepExecution{
			Id: actRes.GetLoanStep().GetId(),
			Details: &palPb.LoanStepExecutionDetails{
				Details: &palPb.LoanStepExecutionDetails_ApplicantData{
					ApplicantData: &palPb.ApplicantData{},
				},
			},
		},
		LseFieldMasks: []palPb.LoanStepExecutionFieldMask{
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS,
		},
	}
	updateRes := &palActivityPb.PalActivityResponse{}
	if localActVer != 1 {
		err = activityPkg.Execute(ctx, preApprovedLoanNs.UpdateLoanStepExecution, updateRes, updateReq)
		if err != nil {
			return nil, errors.Wrap(err, "lse update failed")
		}
	} else {
		err = activityPkg.ExecuteLocally(ctx, preApprovedLoanNs.UpdateLoanStepExecution, updateRes, updateReq)
		if err != nil {
			return nil, errors.Wrap(err, "lse update failed")
		}
	}

	return actRes, nil
}

// to handle error in any stage execution. In case of error, this will mark the loan request and celestial stage to terminal state
func handleWorkflowError(ctx workflow.Context, err error, lrOrchId string, celestialStage epifitemporal.Stage) error {
	workflowReqStatus := getWorkflowStatus(err)

	updateWfErr := celestialPkg.UpdateWorkflowStage(ctx, celestialStage, workflowReqStatus)
	if updateWfErr != nil {
		return fmt.Errorf("failed to update workflow stage: %s: %w", celestialStage, updateWfErr)
	}

	// update the loan request with the proper status
	updateErr := updateLoanRequest(ctx, lrOrchId, getLrStatus(err))
	if updateErr != nil {
		return fmt.Errorf("failed to update loan requets status: %s: %w", getLrStatus(err), updateErr)
	}

	if workflowReqStatus == stagePb.Status_SUCCESSFUL {
		return nil
	} else {
		// non-successful state so sending error as need to fail the workflow
		return fmt.Errorf("workflow stage failed, terminating workflow, failure: %s", workflowReqStatus.String())
	}
}

func getWorkflowStatus(err error) stagePb.Status {
	if err == nil {
		return stagePb.Status_SUCCESSFUL
	} else {
		return stagePb.Status_FAILED
	}
}

func getLrStatus(err error) palPb.LoanRequestStatus {
	switch {
	case err == nil:
		return palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS
	case epifitemporal.IsRetryableError(err):
		return palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CANCELLED
	default:
		return palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED
	}
}
