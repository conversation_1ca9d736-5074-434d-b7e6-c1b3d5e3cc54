package provider

import (
	"context"
	"errors"
	"fmt"
	"strings"

	pkgErrors "github.com/pkg/errors"
	"go.uber.org/zap"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/preapprovedloan/config/common"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/savings"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/preapprovedloan/config/common/genconf"
	"github.com/epifi/gamma/preapprovedloan/helper"
	multiDbProvider "github.com/epifi/gamma/preapprovedloan/multidb_provider"
	"github.com/epifi/gamma/preapprovedloan/recommendation_engine/eligibility_evaluator"
	"github.com/epifi/gamma/preapprovedloan/recommendation_engine/eligibility_evaluator/providers"
	priorityProviderFactory "github.com/epifi/gamma/preapprovedloan/recommendation_engine/priority_provider"
	pb "github.com/epifi/gamma/preapprovedloan/recommendation_engine/priority_provider/providers"
)

type LoanOptionRecommender struct {
	multiDbProvider             multiDbProvider.IMultiDbProvider
	priorityProviderFactory     priorityProviderFactory.IPriorityProviderFactory
	eligibilityEvaluatorFactory eligibility_evaluator.EligibilityEvaluatorFactory
	rpcHelper                   *helper.RpcHelper
	onbClient                   onbPb.OnboardingClient
	vendorProgramConfig         *genconf.VendorProgramLevelFeature
	savingsClient               savings.SavingsClient
	lopeOverrideConfig          *genconf.LopeOverrideConfig
}

func NewLoanOptionRecommender(
	multiDbProvider multiDbProvider.IMultiDbProvider,
	priorityProviderFactory priorityProviderFactory.IPriorityProviderFactory,
	eligibilityEvaluatorFactory eligibility_evaluator.EligibilityEvaluatorFactory,
	rpcHelper *helper.RpcHelper,
	onbClient onbPb.OnboardingClient,
	vendorProgramConfig *genconf.VendorProgramLevelFeature,
	savingsClient savings.SavingsClient,
	lopeOverrideConfig *genconf.LopeOverrideConfig,
) *LoanOptionRecommender {
	return &LoanOptionRecommender{
		multiDbProvider:             multiDbProvider,
		priorityProviderFactory:     priorityProviderFactory,
		eligibilityEvaluatorFactory: eligibilityEvaluatorFactory,
		rpcHelper:                   rpcHelper,
		onbClient:                   onbClient,
		vendorProgramConfig:         vendorProgramConfig,
		savingsClient:               savingsClient,
		lopeOverrideConfig:          lopeOverrideConfig,
	}
}

func (r *LoanOptionRecommender) checkUserProperty(ctx context.Context, actorId string) (bool, bool, error) {
	userFeatRes, err := helper.GetUserFeatureProperty(ctx, actorId, r.onbClient, r.savingsClient)
	if err != nil {
		return false, false, fmt.Errorf("failed to get user feature property, err: %w", err)
	}

	// Commenting this out because DSAs is calling landing provider in bulk and internal api is getting rate limit touched
	// if userFeatRes.IsFiSAHolder {
	//	isRiskyUser, riskErr := r.rpcHelper.IsRiskyUser(ctx, actorId)
	//	if riskErr != nil {
	//		return false, false, fmt.Errorf("%w. failed to check risk profile for user", riskErr)
	//	}
	//	return isRiskyUser, false, nil
	// }
	return false, !userFeatRes.IsFiSAHolder, nil
}

// filterHardOfferOptions filters offers to keep only HARD offers from the same vendor if any HARD offer exists
// Picks the first priority vendor if multiple HARD offer vendors are present in the list
func (r *LoanOptionRecommender) filterHardOfferOptions(loanOptions []*palPb.LoanOption) []*palPb.LoanOption {
	// First, check if any HARD offer is present, pick the first priority vendor
	hasHardOffer := false
	var hardOfferVendor palPb.Vendor

	for _, lo := range loanOptions {
		if lo.GetLoanOffer().GetLoanOfferType() == palPb.LoanOfferType_LOAN_OFFER_TYPE_HARD {
			hasHardOffer = true
			hardOfferVendor = lo.GetLoanHeader().GetVendor()
			break
		}
	}

	// If a HARD offer exists, filter to keep only HARD offers from the same vendor
	if hasHardOffer {
		var filteredOptions []*palPb.LoanOption
		for _, lo := range loanOptions {
			if lo.GetLoanOffer().GetLoanOfferType() == palPb.LoanOfferType_LOAN_OFFER_TYPE_HARD && lo.GetLoanHeader().GetVendor() == hardOfferVendor {
				filteredOptions = append(filteredOptions, lo)
			}
		}
		return filteredOptions
	}

	// If no HARD offer, return original list
	return loanOptions
}

func (r *LoanOptionRecommender) GetCombinedRankedLoanOptions(ctx context.Context, req *GetCombinedRankedLoanOptionsRequest) (*GetCombinedRankedLoanOptionsResponse, error) {
	if req.LoanType == palPb.LoanType_LOAN_TYPE_UNSPECIFIED {
		// for backward compatibility
		req.LoanType = palPb.LoanType_LOAN_TYPE_PERSONAL
	}
	loanOptionList, err := r.getLoanOptionAccToLoanHeaderPriority(ctx, req.ActorId, req.LoanType)
	if err != nil {
		logger.Error(ctx, "error in getting loanOptions", zap.Error(err))
		return nil, err
	}
	// Filter offers to keep only HARD offers from the same vendor if any HARD offer exists
	// Picks the first priority vendor if multiple HARD offer vendors are present in the list
	loanOptionList = r.filterHardOfferOptions(loanOptionList)
	// for users coming first time having no offers and no loec returning empty loan Option list
	return &GetCombinedRankedLoanOptionsResponse{
		LoanOptions: loanOptionList,
	}, nil

}

// Helper function to get a consistent ID from LoanOption for tracking
func getLoanOptionID(option *palPb.LoanOption) string {
	switch option.GetLoanOptionType().(type) {
	case *palPb.LoanOption_LoanOffer:
		return option.GetLoanOffer().GetId()
	case *palPb.LoanOption_EligibilityHeader:
		return strings.Join([]string{option.GetEligibilityHeader().GetVendor().String(), option.GetEligibilityHeader().GetLoanProgram().String()}, ":")
	}
	return "" // Return empty if no ID can be found
}

func (r *LoanOptionRecommender) reOrderLoanOptionBasedOnLoanPriority(options []*palPb.LoanOption, isNonFiCore bool) []*palPb.LoanOption {
	loanPriorityList := r.getLoanPriorityList(isNonFiCore)
	finalLoanOptions := make([]*palPb.LoanOption, 0)
	addedOptionIDs := make(map[string]bool) // To track added options by their ID

	// Pass 1: Add options based on priority
	for _, lp := range loanPriorityList {
		for _, option := range options {
			optionID := getLoanOptionID(option)
			if optionID == "" || addedOptionIDs[optionID] { // Skip if no ID or already added
				continue
			}

			matchesPriority := false
			switch option.GetLoanOptionType().(type) {
			case *palPb.LoanOption_LoanOffer:
				if lp.IsOffer && option.GetLoanOffer() != nil &&
					option.GetLoanOffer().GetVendor().String() == lp.Vendor &&
					option.GetLoanOffer().GetLoanProgram().String() == lp.LoanProgram {
					if lp.LoanOfferType == "" || option.GetLoanOffer().GetLoanOfferType().String() == lp.LoanOfferType {
						matchesPriority = true
					}
				}
			case *palPb.LoanOption_EligibilityHeader:
				if lp.IsEligibility && option.GetEligibilityHeader() != nil &&
					option.GetEligibilityHeader().GetVendor().String() == lp.Vendor &&
					option.GetEligibilityHeader().GetLoanProgram().String() == lp.LoanProgram {
					matchesPriority = true
				}
			}

			if matchesPriority {
				finalLoanOptions = append(finalLoanOptions, option)
				addedOptionIDs[optionID] = true
			}
		}
	}

	// Pass 2: Add any remaining options that weren't prioritized but are unique
	for _, option := range options {
		optionID := getLoanOptionID(option)
		if optionID != "" && !addedOptionIDs[optionID] {
			finalLoanOptions = append(finalLoanOptions, option)
			addedOptionIDs[optionID] = true // Mark as added to avoid duplicates from this pass if any
		}
	}

	return finalLoanOptions
}

func (r *LoanOptionRecommender) getLoanPriorityList(isNonFiCore bool) []*common.LoanOptionPriority {
	if isNonFiCore {
		return r.lopeOverrideConfig.LoanPriorityOrderNonFiCore()
	}
	return r.lopeOverrideConfig.LoanPriorityOrderFiCore()
}

// Used to keep a track we don't add repeated LoanOption while filtering
// true mean, a duplicate is found
// false mean, no duplicate is found and we add it in map.
func checkLoanOptionDuplicate(option *palPb.LoanOption, checker map[string]*palPb.LoanOption) bool {
	idToCheck := getLoanOptionID(option)

	if idToCheck == "" {
		return false
	}

	if _, found := checker[idToCheck]; found {
		return true // Duplicate found
	}

	// Not a duplicate, add to checker map
	checker[idToCheck] = option
	return false // Not a duplicate
}

func createLoanOption(res *providers.EvaluateLoanEligibilityResponse, loanOffer *palPb.LoanOffer) *palPb.LoanOption {
	if res.IsLoanOfferAvailable() {
		return &palPb.LoanOption{
			LoanOptionType: &palPb.LoanOption_LoanOffer{LoanOffer: loanOffer},
		}
	}

	return &palPb.LoanOption{
		LoanOptionType: &palPb.LoanOption_EligibilityHeader{
			EligibilityHeader: res.GetEligibilityLoanHeader(),
		},
	}
}

func (r *LoanOptionRecommender) getLoanOptionAccToLoanHeaderPriority(ctx context.Context, actorId string, loanType palPb.LoanType) ([]*palPb.LoanOption, error) {
	offersMap := make(map[string][]*palPb.LoanOffer)

	riskyUser, isNonFiCoreUser, userErr := r.checkUserProperty(ctx, actorId)
	if userErr != nil {
		return nil, userErr
	}
	if riskyUser {
		return nil, nil
	}

	if loanType == palPb.LoanType_LOAN_TYPE_UNSPECIFIED {
		return nil, errors.New("no loan type unspecified for actor id: " + actorId)
	}
	origOwnership := epificontext.OwnershipFromContext(ctx)
	defer func() {
		ctx = epificontext.WithOwnership(ctx, origOwnership)
	}()
	// setting ownership to zero value so that CheckAndGetLoanOffersForActor will check for offers in all DBs
	ctx = epificontext.WithOwnership(ctx, commontypes.Ownership_EPIFI_TECH)
	// fetching all active offers for non risky users from different Dbs and storing in map[vendor:loanProgram]
	offers, offerErr := r.multiDbProvider.CheckAndGetLoanOffersForActor(ctx, &multiDbProvider.CheckAndGetLoanOffersForActorRequest{
		ActorId:      actorId,
		LoanPrograms: helper.GetLoanProgramsForLoanType(loanType),
	})
	if offerErr != nil && !errors.Is(offerErr, epifierrors.ErrRecordNotFound) {
		return nil, pkgErrors.Wrap(offerErr, "error fetching pl loan offer for actor")
	}
	if offers != nil {
		for _, lo := range offers.Offers {
			key := helper.GetRecommendationEngineOffersKey(lo.GetVendor(), lo.GetLoanProgram())
			offersMap[key] = append(offersMap[key], lo)
		}
	}

	priorityProvider, err := r.priorityProviderFactory.GetPriorityProvider(ctx, &priorityProviderFactory.GetPriorityProviderFactoryRequest{
		IsNonFiCoreUser: isNonFiCoreUser,
		LoanType:        loanType,
	})
	if err != nil {
		return nil, pkgErrors.Wrap(err, "error fetching priority provider factory")
	}

	loanHeaderPrioritisationRequest := pb.GetLoanHeaderPrioritisationRequest{
		ShouldCheckAvailability: true,
		ActorId:                 actorId,
	}
	loanHeaders, err := priorityProvider.GetLoanHeaderPrioritisation(ctx, loanHeaderPrioritisationRequest)
	if err != nil {
		return nil, fmt.Errorf("failed to get loan header prioritisation error : %w", err)
	}

	combinedLoanOptions := make([]*palPb.LoanOption, 0)
	loanOptionDedupeChecker := make(map[string]*palPb.LoanOption)
	for _, lh := range loanHeaders {

		isDowntime := r.vendorProgramConfig.DownTimeConfig().Get(lh.GetVendor().String()).IsDownNow()
		if isDowntime {
			continue
		}

		provider, elErr := r.eligibilityEvaluatorFactory.GetEligibilityEvaluatorProvider(lh, isNonFiCoreUser)
		if elErr != nil {
			return nil, elErr
		}

		key := helper.GetRecommendationEngineOffersKey(lh.GetVendor(), lh.GetLoanProgram())

		if los, found := offersMap[key]; found {
			for _, lo := range los {
				loeRes, loeErr := provider.EvaluateLoanEligibility(ctx, &providers.EvaluateLoanEligibilityRequest{
					LoanOffer:       lo,
					ActorId:         actorId,
					IsNonFiCoreUser: isNonFiCoreUser,
				}, lh)
				if loeErr != nil {
					return nil, loeErr
				}
				if loeRes != nil && (loeRes.IsLoanOfferAvailable() || loeRes.ShouldCheckLoanEligibility()) {
					loanOption := createLoanOption(loeRes, lo)
					if !checkLoanOptionDuplicate(loanOption, loanOptionDedupeChecker) {
						combinedLoanOptions = append(combinedLoanOptions, loanOption)
					}
				}
			}
		} else {
			loeRes, loeErr := provider.EvaluateLoanEligibility(ctx, &providers.EvaluateLoanEligibilityRequest{
				ActorId:         actorId,
				IsNonFiCoreUser: isNonFiCoreUser,
			}, lh)
			if loeErr != nil {
				return nil, loeErr
			}
			if loeRes != nil && loeRes.ShouldCheckLoanEligibility() {
				loanOption := createLoanOption(loeRes, nil)
				if !checkLoanOptionDuplicate(loanOption, loanOptionDedupeChecker) {
					combinedLoanOptions = append(combinedLoanOptions, loanOption)
				}
			}
		}

	}

	newLoanOptionList := r.reOrderLoanOptionBasedOnLoanPriority(combinedLoanOptions, isNonFiCoreUser)
	return newLoanOptionList, nil
}
