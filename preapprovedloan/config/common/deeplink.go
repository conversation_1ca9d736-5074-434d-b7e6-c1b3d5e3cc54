package common

import releaseConfig "github.com/epifi/gamma/pkg/feature/release/config"

type DeeplinkConfig struct {
	IsInitiateMandateEnrichmentEnabled bool `dynamic:"true" ,quest:"variable"`
	// IsAlternateAccountFlowEnabled flag determines if alternate account flow is enabled for the user
	// this variable is marked as quest for AB experimentation
	IsAlternateAccountFlowEnabled bool `dynamic:"true" ,quest:"variable"`
	// whether the mandate needs to be done in the app with web view or in the external browser
	// For Android
	OpenAbflMandateUrlViaExternal    bool `dynamic:"true"`
	OpenAbflDigilockerUrlViaExternal bool `dynamic:"true"`
	// For Ios
	OpenAbflDigilockerUrlViaExternalForIos bool `dynamic:"true"`
	OpenAbflMandateUrlViaExternalForIos    bool `dynamic:"true"`
	IsLoanDetailsSelectionV2FlowEnabled    bool `dynamic:"true" ,quest:"variable"`
	// IsAlternateAccountFlowEnabledForLL flag determines if alternate account flow is enabled for the user IN liquiloans journey
	// this variable is marked as quest for AB experimentation
	IsAlternateAccountFlowEnabledForLL bool `dynamic:"true" ,quest:"variable"`
	// A/B variable for LAMF experimentation
	ChangeButtonTextDetailsPage bool `dynamic:"true" ,quest:"variable"`

	OpenIdfcVkycUrlViaExternalForAndroid bool `dynamic:"true"`
	OpenIdfcVkycUrlViaExternalForIos     bool `dynamic:"true"`
	// this config is extended version of IsLoanDetailsSelectionV2FlowEnabled, make sure to change the value at both places whenever needed.
	LoanDetailsSelectionV2Flow               *LoanDetailsSelectionV2Flow               `dynamic:"true" ,quest:"component"`
	OfferDetailsV3Config                     *OfferDetailsV3Config                     `dynamic:"true" ,quest:"component"`
	AbflReferencesAppVersionConstraintConfig *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	InfoItemV3MinVersion                     *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`

	FedKfsExitUrl string `dynamic:"true"`
}

type OfferDetailsV3Config struct {
	IsEnabled                  bool                                      `dynamic:"true" ,quest:"variable"`
	AppVersionConstraintConfig *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	VendorLoanProgramMap       map[string]bool                           `dynamic:"true"`
	SkipAmountSelectionScreen  bool                                      `dynamic:"true" ,quest:"variable"`
	ShowInterestRate           bool                                      `dynamic:"true" ,quest:"variable"`
	ShowZeroPreClosureTag      bool                                      `dynamic:"true" ,quest:"variable"`
}

type CreditReportConfig struct {
	// UseCreditReportV2 tells lending servers if we want to use creditreportv2 code or user/credit_report code
	UseCreditReportV2 bool `dynamic:"true"`
	// StaleExperianReportThresholdDays defines the number of days after which a credit report is considered stale
	// and will be refreshed when accessed again.
	StaleExperianReportThresholdDays int64 `dynamic:"true"`
}

// LoanDetailsSelectionV2Flow will have all the config to determine whether new loans selection screens are used or not.
// It will all the related configs to enable A/B experimentation on loan selection journey, e.g. skip loader screen, show reward tab or not etc...
type LoanDetailsSelectionV2Flow struct {
	IsEnabled bool `dynamic:"true" ,quest:"variable"`
	// vendor and loan program level config. For vendors where single program is live (e.g: Federal, IDFC and ABFL), vendor name will used to enable the flow
	// for others, loan programs in the list will be checked and used to enable the flow
	EnableLoanPrograms      []string           `dynamic:"true"`
	DefaultAmountPercentage map[string]float64 `dynamic:"true"`
}
