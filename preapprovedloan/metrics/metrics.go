package metrics

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"time"

	"github.com/prometheus/client_golang/prometheus"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
)

// PreApprovedLoanMetrics represents a collection of metrics to be registered on a
// Prometheus metrics registry for Loan service custom-metrics.
type PreApprovedLoanMetrics struct {
	loanDisbursementCount                *prometheus.CounterVec
	loanEmiPaymentCount                  *prometheus.CounterVec
	loanStepExecutionStatusUpdateCounter *prometheus.CounterVec
	// loanProcessStepDurationSeconds and loanProcessStepDurationHours metrics can be used to record time duration taken
	// by any small step in a loan process.
	loanProcessStepDurationSeconds *prometheus.HistogramVec
	loanProcessStepDurationHours   *prometheus.HistogramVec
	// loanProcessStepErrorCount metric can be used to record count of different errors encountered  in a loan process step
	loanProcessStepErrorCount *prometheus.CounterVec
	// mandateStatusCount metric can be used to record mandate status count across vendor/programs, bank and app platform
	mandateStatusCount *prometheus.CounterVec
	// loanPrePaymentCount metric will be used to record pre-payment transactions across vendor/programs, pre-payment type
	loanPrePaymentCount *prometheus.CounterVec
	// loanOfferCreationMetrics will be used to record and track the number of offers generated for each vendor, program and for each offer type
	loanOfferCreationMetrics *prometheus.CounterVec
}

var preapprovedloanMetricsRecorder = initialisePreapprovedLoanMetrics()

const (
	NotificationForSavings     = "NOTIFICATION_FOR_SAVINGS"
	NotificationForLoanAccount = "NOTIFICATION_FOR_LOAN_ACCOUNT"
)

func initialisePreapprovedLoanMetrics() *PreApprovedLoanMetrics {
	loanMetrics := &PreApprovedLoanMetrics{
		loanDisbursementCount: prometheus.NewCounterVec(prometheus.CounterOpts{
			Name: "loan_disbursement_transaction",
			Help: "Total number of disbursement transactions created through notification",
		}, []string{"source"}),
		loanEmiPaymentCount: prometheus.NewCounterVec(prometheus.CounterOpts{
			Name: "loan_emi_payment_transaction",
			Help: "Total number of emi installment transactions created through notification",
		}, []string{"source"}),
		loanStepExecutionStatusUpdateCounter: prometheus.NewCounterVec(prometheus.CounterOpts{
			Name: "loan_step_execution_status_update_total",
			Help: "Total count of loan execution step status updates",
		}, []string{"vendor", "loan_program", "flow_name", "step_name", "status", "sub_status", "provenance"}),
		// status denotes the updated status of loan step execution.
		// sub_status denotes the updated sub-status of loan step execution.

		loanProcessStepDurationSeconds: prometheus.NewHistogramVec(prometheus.HistogramOpts{
			Name:    "loan_process_step_duration_in_seconds",
			Help:    "can be used to record time taken (in seconds) by any step in a loan process",
			Buckets: prometheus.ExponentialBuckets(0.2, 2, 12),
		}, []string{"vendor", "loan_program", "process_type", "step_name", "process_name"}),
		loanProcessStepDurationHours: prometheus.NewHistogramVec(prometheus.HistogramOpts{
			Name:    "loan_process_step_duration_in_hours",
			Help:    "can be used to record time taken (in hours) by any step in a loan process",
			Buckets: prometheus.ExponentialBuckets(0.2, 2, 11),
		}, []string{"vendor", "loan_program", "process_type", "step_name", "process_name"}),
		loanProcessStepErrorCount: prometheus.NewCounterVec(prometheus.CounterOpts{
			Name: "loan_process_step_error_count",
			Help: "Total count of loan execution step status updates",
		}, []string{"vendor", "loan_program", "process_type", "step_name", "process_name", "error"}),
		mandateStatusCount: prometheus.NewCounterVec(prometheus.CounterOpts{
			Name: "mandate_status_count",
			Help: "can be used to record mandate status across vendor/loan_program/bank/platform",
		}, []string{"vendor", "loan_program", "bank_name", "app_platform", "status"}),
		loanPrePaymentCount: prometheus.NewCounterVec(prometheus.CounterOpts{
			Name: "loan_pre_payment_count",
			Help: "Total count of loan prepayment across type/status/vendor/program",
		}, []string{"type", "status", "vendor", "program"}),
		loanOfferCreationMetrics: prometheus.NewCounterVec(prometheus.CounterOpts{
			Name: "loan_offer_creation_count",
			Help: "Number of offers generated for each vendor, program and for each offer type",
		}, []string{"vendor", "loan_program", "offer_type"}),
	}
	// register all metrics
	prometheus.MustRegister(loanMetrics.loanDisbursementCount)
	prometheus.MustRegister(loanMetrics.loanEmiPaymentCount)
	prometheus.MustRegister(loanMetrics.loanStepExecutionStatusUpdateCounter)
	prometheus.MustRegister(loanMetrics.loanProcessStepDurationHours)
	prometheus.MustRegister(loanMetrics.loanProcessStepDurationSeconds)
	prometheus.MustRegister(loanMetrics.loanProcessStepErrorCount)
	prometheus.MustRegister(loanMetrics.mandateStatusCount)
	prometheus.MustRegister(loanMetrics.loanOfferCreationMetrics)

	return loanMetrics
}

func RecordLoanOfferCreation(vendor palPb.Vendor, loanProgram palPb.LoanProgram, loType palPb.LoanOfferType) {
	preapprovedloanMetricsRecorder.loanOfferCreationMetrics.WithLabelValues(vendor.String(), loanProgram.String(), loType.String()).Inc()
}

func RecordPrePaymentTxn(prepayType, status, vendor, program string) {
	preapprovedloanMetricsRecorder.loanPrePaymentCount.WithLabelValues(prepayType, status, vendor, program).Inc()
}

func RecordLoanDisbursementTxn(source string) {
	preapprovedloanMetricsRecorder.loanDisbursementCount.WithLabelValues(source).Inc()
}

func RecordEmiPaymentTxn(source string) {
	preapprovedloanMetricsRecorder.loanEmiPaymentCount.WithLabelValues(source).Inc()
}

func RecordLoanStepExecutionStatusUpdate(vendor, loanProgram, flow, stepName, status, subStatus, provenance string) {
	preapprovedloanMetricsRecorder.loanStepExecutionStatusUpdateCounter.WithLabelValues(vendor, loanProgram, flow, stepName, status, subStatus, provenance).Inc()
}

func RecordLoanProcessStepDuration(vendor palPb.Vendor, loanProgram palPb.LoanProgram, processType palPb.LoanRequestType,
	stepName palPb.LoanStepExecutionStepName, processName LoanProcessStepName, startTime time.Time) {
	currTime := time.Now()
	duration := currTime.Sub(startTime)
	preapprovedloanMetricsRecorder.loanProcessStepDurationSeconds.WithLabelValues(vendor.String(), loanProgram.String(),
		processType.String(), stepName.String(), string(processName)).Observe(duration.Seconds())
	preapprovedloanMetricsRecorder.loanProcessStepDurationHours.WithLabelValues(vendor.String(), loanProgram.String(),
		processType.String(), stepName.String(), string(processName)).Observe(duration.Hours())
}

func RecordLoanProcessStepErrorCount(vendor palPb.Vendor, loanProgram palPb.LoanProgram, processType palPb.LoanRequestType,
	stepName palPb.LoanStepExecutionStepName, processName LoanProcessStepName, errorType ErrorType) {
	preapprovedloanMetricsRecorder.loanProcessStepErrorCount.WithLabelValues(vendor.String(), loanProgram.String(), processType.String(),
		stepName.String(), string(processName), string(errorType)).Inc()
}

func RecordMandateStatusCount(vendor palPb.Vendor, loanProgram palPb.LoanProgram, bankName string, appPlatform commontypes.Platform, status string) {
	preapprovedloanMetricsRecorder.mandateStatusCount.WithLabelValues(vendor.String(), loanProgram.String(), bankName, appPlatform.String(), status).Inc()
}
