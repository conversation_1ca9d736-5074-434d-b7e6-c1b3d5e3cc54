package lenden

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/types/known/timestamppb"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifigrpc"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/preapprovedloan/enums"
	"github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	lendenVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
	"github.com/epifi/gamma/preapprovedloan/dao"
	"github.com/epifi/gamma/preapprovedloan/helper"
)

const (
	// MandateSoftExpiryDuration is the duration after which a mandate setup URL can't be opened to continue setting up mandate.
	// If the URL has already been opened and is being used to set up mandate, the user can continue using it
	// and has until the MandateHardExpiryDuration to complete setting up mandate.
	MandateSoftExpiryDuration = time.Minute * 5

	// MandateHardExpiryDuration is the duration after which a mandate setup URL can't be used and a new one
	// must be generated to set up mandate, if needed.
	MandateHardExpiryDuration = time.Minute * 15
)

type MandateSetupProcessor struct {
	loanApplicantDao     dao.LoanApplicantDao
	loanStepExecutionDao dao.LoanStepExecutionsDao
	loanRequestDao       dao.LoanRequestsDao
	rpcHelper            *helper.RpcHelper
	userClient           userPb.UsersClient
	lendenVgClient       lendenVgPb.LendenClient
	preferUPIMandateType bool
}

type MandateInfo struct {
	IsCompleted bool
	Lse         *palPb.LoanStepExecution
}

func NewMandateSetupProcessor(
	loanApplicantDao dao.LoanApplicantDao,
	lendenVgClient lendenVgPb.LendenClient,
	loanStepExecutionDao dao.LoanStepExecutionsDao,
	loanRequestDao dao.LoanRequestsDao,
	rpcHelper *helper.RpcHelper,
	userClient userPb.UsersClient,
	preferUPIMandateType bool,
) *MandateSetupProcessor {
	return &MandateSetupProcessor{
		loanApplicantDao:     loanApplicantDao,
		lendenVgClient:       lendenVgClient,
		loanStepExecutionDao: loanStepExecutionDao,
		loanRequestDao:       loanRequestDao,
		rpcHelper:            rpcHelper,
		userClient:           userClient,
		preferUPIMandateType: preferUPIMandateType,
	}
}

type MandateStatus int

const (
	MandateStatusUnknown MandateStatus = iota

	// MandateStatusPreRequisitesPending When an action needs to be done before setting up a payment mandate
	// like validating bank account details with lender
	MandateStatusPreRequisitesPending

	// MandateStatusInProgress When pre-requisites for starting mandate setup are complete and the setup has been started
	MandateStatusInProgress

	// MandateStatusAlreadyCompleted When the mandate has already been set up (most probably during a previous loan account opening orchestration)
	MandateStatusAlreadyCompleted
)

func (s MandateStatus) String() string {
	return []string{"Unknown", "PreRequisitesPending", "InProgress", "AlreadyCompleted"}[s]
}

type InitiateMandateResponse struct {
	Status MandateStatus
	Data   *MandateData
}

type MandateData struct {
	TrackingId     string
	URL            string
	URLValidity    *timestamppb.Timestamp
	URLGeneratedAt *timestamppb.Timestamp
	Type           enums.MandateType
}

// StartMandateSetup initiates the mandate setup process with the vendor for the given LoanStepExecution (LSE).
// It fetches required applicant, loan, and user/device details, then calls the vendor API to start mandate setup.
// Returns the initiation status and mandate data, or an error if the process fails or pre-requisites are not met.
func (p *MandateSetupProcessor) StartMandateSetup(ctx context.Context, lse *palPb.LoanStepExecution) (*InitiateMandateResponse, error) {
	applicant, err := p.loanApplicantDao.GetByActorId(ctx, lse.GetActorId())
	if err != nil {
		return nil, errors.Wrap(err, "error getting applicant")
	}
	lr, err := p.loanRequestDao.GetById(ctx, lse.GetRefId())
	if err != nil {
		return nil, errors.Wrap(err, "error getting loan request")
	}
	userIpAddress, err := p.rpcHelper.FetchIpAddress(ctx, lse.GetActorId())
	if err != nil {
		return nil, errors.Wrap(err, "error getting user ip address")
	}
	userDP, udpErr := p.userClient.GetUserDeviceProperties(ctx, &userPb.GetUserDevicePropertiesRequest{
		ActorId:       lse.GetActorId(),
		PropertyTypes: []typesv2.DeviceProperty{typesv2.DeviceProperty_DEVICE_PROP_DEVICE_ID},
	})
	if te := epifigrpc.RPCError(userDP, udpErr); te != nil {
		return nil, errors.Wrapf(te, "error getting device id")
	}
	deviceId := userDP.GetPropValue(typesv2.DeviceProperty_DEVICE_PROP_DEVICE_ID).GetDeviceId()
	mandateType := p.getMandateSetupModeForInitiatingMandate()
	ldcMandateType, err := getMandateTypeForLDC(mandateType)
	if err != nil {
		return nil, errors.Wrap(err, "error getting mandate type for LDC")
	}
	res, err := p.lendenVgClient.InitMandate(ctx, &lendenVgPb.InitMandateRequest{
		Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_LENDEN},
		LoanId: lr.GetVendorRequestId(),
		UserId: applicant.GetVendorApplicantId(),
		ConsentCodeList: []lendenVgPb.ConsentType{
			lendenVgPb.ConsentType_CONSENT_TYPE_MANDATE,
		},
		MandateType: ldcMandateType,
		UserIp:      userIpAddress,
		DeviceId:    deviceId,
		// TODO(Brijesh): Fetch appropriate consent and use its timestamp
		ConsentTime: timestamppb.Now(),
	})
	if err = epifigrpc.RPCError(res, err); err != nil {
		switch res.GetStatus().GetCode() {
		case uint32(lendenVgPb.InitMandateResponse_ENACH_ALREADY_COMPLETED):
			if res.GetTrackingId() == "" {
				return nil, errors.Errorf("expected non-empty tracking id when e-NACH setup is already completed")
			}
			return &InitiateMandateResponse{
				Status: MandateStatusAlreadyCompleted,
				Data:   &MandateData{TrackingId: res.GetTrackingId()},
			}, nil
		case uint32(lendenVgPb.InitMandateResponse_OFFER_OR_ACCOUNT_DETAILS_NOT_FOUND),
			uint32(lendenVgPb.InitMandateResponse_BANK_ACCOUNT_NOT_VERIFIED):
			return &InitiateMandateResponse{
				Status: MandateStatusPreRequisitesPending,
			}, nil
		default:
			return nil, errors.Wrapf(err, "error initiating mandate with lender")
		}
	}
	return &InitiateMandateResponse{
		Status: MandateStatusInProgress,
		Data: &MandateData{
			TrackingId:  res.GetTrackingId(),
			URL:         res.GetRedirectionUrl(),
			URLValidity: res.GetMandateUrlValidity(),
			// A vendor call to get the URL isn't going to take more than 30 seconds.
			// Hence, assuming the URL was generated 30 seconds ago.
			// This will ensure the logic to check if the URL is expired or not on our end based on this timestamp
			// never returns false positives.
			// Otherwise, we might end up returning URLs that are expired.
			URLGeneratedAt: timestamppb.New(time.Now().Add(-30 * time.Second)),
			Type:           mandateType,
		},
	}, nil
}

// CheckAndUpdateMandateCompletion queries the vendor for the current mandate status for the given LoanStepExecution (LSE).
// If the mandate is completed, it updates the LSE with completion info and returns true with the updated LSE.
// If the mandate is not completed (failed, expired, or in progress), it returns false and the current LSE.
// Returns an error if the status is unexpected or if any step fails.
func (p *MandateSetupProcessor) CheckAndUpdateMandateCompletion(ctx context.Context, lse *palPb.LoanStepExecution) (bool, *palPb.LoanStepExecution, error) {
	applicant, err := p.loanApplicantDao.GetByActorId(ctx, lse.GetActorId())
	if err != nil {
		return false, nil, errors.Wrap(err, "error getting applicant")
	}
	mandateType := getMandateSetupModeForCheckingMandateStatus(lse.GetDetails().GetMandateData().GetMandateType())
	ldcMandateType, err := getMandateTypeForLDC(mandateType)
	if err != nil {
		return false, nil, errors.Wrap(err, "error getting mandate type for LDC")
	}
	mandateStatusRes, err := p.lendenVgClient.CheckMandateStatus(ctx, &lendenVgPb.CheckMandateStatusRequest{
		UserId:      applicant.GetVendorApplicantId(),
		TrackingId:  lse.GetDetails().GetMandateData().GetMerchantTxnId(),
		MandateType: ldcMandateType,
	})
	if err = epifigrpc.RPCError(mandateStatusRes, err); err != nil {
		return false, nil, errors.Wrap(err, "error checking mandate status")
	}
	switch mandateStatusRes.GetMandateStatus() {
	case lendenVgPb.MandateStatus_MANDATE_STATUS_COMPLETED:
		data := &MandateData{TrackingId: mandateStatusRes.TrackingId}
		lse, err = p.updateLseWithMandateCompletionInfo(ctx, lse, data)
		if err != nil {
			return false, nil, errors.Wrap(err, "error updating LSE with mandate completion info")
		}
		return true, lse, nil
	case lendenVgPb.MandateStatus_MANDATE_STATUS_FAILED,
		lendenVgPb.MandateStatus_MANDATE_STATUS_EXPIRED,
		lendenVgPb.MandateStatus_MANDATE_STATUS_IN_PROGRESS:
		return false, lse, nil
	default:
		return false, nil, errors.Errorf("unexpected mandate status: %s", mandateStatusRes.GetMandateStatus())
	}
}

// FetchOrRefreshMandateStatus fetches the latest mandate setup status and information for the given LoanStepExecution (LSE).
// If no mandate has been initiated, it will initiate one. If the mandate is failed, expired, or the link is expired, it will attempt to re-initiate the mandate setup.
// Returns MandateInfo indicating whether the mandate is completed and the updated LSE.
func (p *MandateSetupProcessor) FetchOrRefreshMandateStatus(ctx context.Context, lse *palPb.LoanStepExecution) (*MandateInfo, error) {
	if lse.GetDetails().GetMandateData().GetMerchantTxnId() == "" {
		initMandateRes, err := p.StartMandateSetup(ctx, lse)
		if err != nil {
			return nil, errors.Wrap(err, "error getting mandate initiation info")
		}
		switch initMandateRes.Status {
		case MandateStatusAlreadyCompleted:
			if initMandateRes.Data.TrackingId == "" {
				return nil, errors.New("expected non-empty tracking id when e-NACH already completed")
			}
			lse, err = p.updateLseWithMandateCompletionInfo(ctx, lse, initMandateRes.Data)
			if err != nil {
				return nil, errors.Wrap(err, "error updating mandate")
			}
			return &MandateInfo{IsCompleted: true, Lse: lse}, nil
		case MandateStatusPreRequisitesPending:
			return &MandateInfo{IsCompleted: false, Lse: lse}, nil
		case MandateStatusInProgress:
			lse, err = p.updateLseWithMandateInProgressInfo(ctx, lse, initMandateRes)
			if err != nil {
				return nil, errors.Wrap(err, "error updating LSE with mandate in progress info")
			}
			return &MandateInfo{IsCompleted: false, Lse: lse}, nil
		default:
			return nil, errors.Errorf("unhandled mandate status: %s", initMandateRes.Status)
		}
	}
	applicant, err := p.loanApplicantDao.GetByActorId(ctx, lse.GetActorId())
	if err != nil {
		return nil, errors.Wrap(err, "error getting applicant")
	}
	mandateType := getMandateSetupModeForCheckingMandateStatus(lse.GetDetails().GetMandateData().GetMandateType())
	ldcMandateType, err := getMandateTypeForLDC(mandateType)
	if err != nil {
		return nil, errors.Wrap(err, "error getting mandate type for LDC")
	}
	mandateStatusRes, err := p.lendenVgClient.CheckMandateStatus(ctx, &lendenVgPb.CheckMandateStatusRequest{
		UserId:      applicant.GetVendorApplicantId(),
		TrackingId:  lse.GetDetails().GetMandateData().GetMerchantTxnId(),
		MandateType: ldcMandateType,
	})
	if err = epifigrpc.RPCError(mandateStatusRes, err); err != nil {
		return nil, errors.Wrap(err, "error checking mandate status")
	}
	switch mandateStatusRes.GetMandateStatus() {
	case lendenVgPb.MandateStatus_MANDATE_STATUS_COMPLETED:
		data := &MandateData{TrackingId: mandateStatusRes.TrackingId}
		lse, err = p.updateLseWithMandateCompletionInfo(ctx, lse, data)
		if err != nil {
			return nil, errors.Wrap(err, "error updating LSE with mandate completion info")
		}
		return &MandateInfo{IsCompleted: true, Lse: lse}, nil
	case lendenVgPb.MandateStatus_MANDATE_STATUS_FAILED,
		lendenVgPb.MandateStatus_MANDATE_STATUS_EXPIRED:
		var isMandateCompleted bool
		isMandateCompleted, lse, err = p.reInitiateMandateSetup(ctx, lse)
		if err != nil {
			return nil, errors.Wrap(err, "error re-initiating mandate setup after status failure")
		}
		if isMandateCompleted {
			return &MandateInfo{IsCompleted: true, Lse: lse}, nil
		}
		if lse.GetDetails().GetMandateData().GetUrl() == "" {
			return nil, errors.New("expected non-empty mandate URL after re-initiating mandate setup")
		}
		return &MandateInfo{IsCompleted: false, Lse: lse}, nil
	case lendenVgPb.MandateStatus_MANDATE_STATUS_IN_PROGRESS:
		mandateLinkExpired := time.Now().After(lse.GetDetails().GetMandateData().GetUrlGeneratedAt().AsTime().Add(MandateHardExpiryDuration))
		if mandateLinkExpired {
			var isMandateCompleted bool
			isMandateCompleted, lse, err = p.reInitiateMandateSetup(ctx, lse)
			if err != nil {
				return nil, errors.Wrap(err, "error re-initiating mandate setup after link expiry or URL age")
			}
			if isMandateCompleted {
				return &MandateInfo{IsCompleted: true, Lse: lse}, nil
			}
			if lse.GetDetails().GetMandateData().GetUrl() == "" {
				return nil, errors.New("expected non-empty mandate URL after re-initiating mandate setup")
			}
			return &MandateInfo{IsCompleted: false, Lse: lse}, nil
		}
		return &MandateInfo{IsCompleted: false, Lse: lse}, nil
	default:
		return nil, errors.Errorf("unexpected mandate status: %s", mandateStatusRes.GetMandateStatus())
	}
}

func (p *MandateSetupProcessor) updateLseWithMandateCompletionInfo(ctx context.Context, lse *palPb.LoanStepExecution, mandateData *MandateData) (*palPb.LoanStepExecution, error) {
	if lse.GetDetails().GetMandateData() == nil {
		lse.GetDetails().Details = &palPb.LoanStepExecutionDetails_MandateData{MandateData: &palPb.MandateData{}}
	}
	if mandateData.TrackingId != "" {
		lse.GetDetails().GetMandateData().MerchantTxnId = mandateData.TrackingId
	}
	if mandateData.URL != "" {
		lse.GetDetails().GetMandateData().Url = mandateData.URL
	}
	if mandateData.URLValidity.AsTime() != time.Unix(0, 0) {
		lse.GetDetails().GetMandateData().MandateLinkExpiry = mandateData.URLValidity
	}
	if mandateData.Type != enums.MandateType_MANDATE_TYPE_UNSPECIFIED {
		lse.GetDetails().GetMandateData().MandateType = mandateData.Type
	}
	lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
	lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_SETUP_SUCCESSFUL
	err := p.loanStepExecutionDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS,
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
	})
	if err != nil {
		return nil, errors.Wrap(err, "error updating LSE")
	}
	return lse, nil
}

func (p *MandateSetupProcessor) reInitiateMandateSetup(ctx context.Context, lse *palPb.LoanStepExecution) (bool, *palPb.LoanStepExecution, error) {
	initiateMandateRes, err := p.StartMandateSetup(ctx, lse)
	if err != nil {
		return false, nil, errors.Wrap(err, "error getting initiation info")
	}
	switch initiateMandateRes.Status {
	case MandateStatusInProgress:
		lse, err = p.updateLseWithMandateInProgressInfo(ctx, lse, initiateMandateRes)
		if err != nil {
			return false, nil, errors.Wrap(err, "error updating LSE with mandate in progress info")
		}
		return false, lse, nil
	case MandateStatusAlreadyCompleted:
		lse, err = p.updateLseWithMandateCompletionInfo(ctx, lse, initiateMandateRes.Data)
		if err != nil {
			return false, nil, errors.Wrap(err, "error updating LSE with mandate completion info")
		}
		return true, lse, nil
	default:
		return false, nil, errors.Errorf("unexpected mandate status: %s", initiateMandateRes.Status)
	}
}

func (p *MandateSetupProcessor) updateLseWithMandateInProgressInfo(
	ctx context.Context,
	lse *palPb.LoanStepExecution,
	initiateMandateRes *InitiateMandateResponse,
) (*palPb.LoanStepExecution, error) {
	if initiateMandateRes.Data.TrackingId == "" {
		return nil, errors.Errorf("expected non-empty mandate setup tracking id with in progress status")
	}
	if initiateMandateRes.Data.URL == "" {
		return nil, errors.Errorf("expected non-empty mandate setup url with in progress status")
	}
	if lse.GetDetails().GetMandateData() == nil {
		lse.GetDetails().Details = &palPb.LoanStepExecutionDetails_MandateData{MandateData: &palPb.MandateData{}}
	}
	lse.GetDetails().GetMandateData().MerchantTxnId = initiateMandateRes.Data.TrackingId
	lse.GetDetails().GetMandateData().Url = initiateMandateRes.Data.URL
	lse.GetDetails().GetMandateData().UrlGeneratedAt = initiateMandateRes.Data.URLGeneratedAt
	if initiateMandateRes.Data.URLValidity.AsTime() != time.Unix(0, 0) {
		lse.GetDetails().GetMandateData().MandateLinkExpiry = initiateMandateRes.Data.URLValidity
	}
	if initiateMandateRes.Data.Type != enums.MandateType_MANDATE_TYPE_UNSPECIFIED {
		lse.GetDetails().GetMandateData().MandateType = initiateMandateRes.Data.Type
	}
	lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS
	lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_LINK_FETCHED
	err := p.loanStepExecutionDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS,
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
	})
	if err != nil {
		return nil, errors.Wrap(err, "error updating LSE")
	}
	return lse, nil
}

func (p *MandateSetupProcessor) getMandateSetupModeForInitiatingMandate() enums.MandateType {
	if p.preferUPIMandateType {
		return enums.MandateType_MANDATE_TYPE_UPI
	}
	return enums.MandateType_MANDATE_TYPE_E_NACH
}

// The type of mandate to use for checking the mandate status
// should be the same as the one that was used for initiating the mandate,
// regardless of the current state of the flag to prefer UPI mandate type.
// For backward compatibility, in LSEs created before mandate type was supported,
// the LSE wouldn't have a mandate type, hence default to e-NACH.
func getMandateSetupModeForCheckingMandateStatus(lseMandateType enums.MandateType) enums.MandateType {
	if lseMandateType != enums.MandateType_MANDATE_TYPE_UNSPECIFIED {
		return lseMandateType
	}
	return enums.MandateType_MANDATE_TYPE_E_NACH
}

func getMandateTypeForLDC(mandateType enums.MandateType) (lendenVgPb.MandateType, error) {
	switch mandateType {
	case enums.MandateType_MANDATE_TYPE_E_NACH:
		return lendenVgPb.MandateType_MANDATE_TYPE_NACH_MANDATE, nil
	case enums.MandateType_MANDATE_TYPE_UPI:
		return lendenVgPb.MandateType_MANDATE_TYPE_UPI_MANDATE, nil
	default:
		return 0, errors.Errorf("unknown mandate type: %s", mandateType)
	}
}
