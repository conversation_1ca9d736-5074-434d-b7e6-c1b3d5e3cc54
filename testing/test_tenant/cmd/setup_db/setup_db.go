package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"strings"

	"github.com/epifi/be-common/pkg/logger"

	testtenant "github.com/epifi/gamma/testing/test_tenant"

	"github.com/epifi/be-common/pkg/cfg"

	cmdcfg "github.com/epifi/be-common/pkg/cmd/config"
)

func main() {
	log.SetFlags(log.Lshortfile | log.LstdFlags)
	logger.Init(cfg.TestEnv)

	// Add exclude servers flag
	var excludeServersArgs string
	flag.StringVar(&excludeServersArgs, "exclude-servers", "", "List of excluded servers for database setup")
	flag.Parse()

	// Parse exclude servers from environment variable if not provided via flag
	if excludeServersArgs == "" {
		excludeServersArgs = os.Getenv("EXCLUDE_SERVERS")
	}

	var excludeServers []string
	if excludeServersArgs != "" {
		excludeServers = strings.Split(excludeServersArgs, ",")
		log.Printf("Excluding servers: %v", excludeServers)
	}

	var env string
	var region string

	dbMap := make(map[string]*cfg.DB)
	servers, err := cfg.GetServers()
	if err != nil {
		log.Fatal(fmt.Errorf("failed to get servers: %w", err))
	}
	for _, server := range servers {
		// Check if server should be excluded
		serverName := string(server)
		isExcluded := false
		for _, excludedServer := range excludeServers {
			if excludedServer == serverName {
				isExcluded = true
				break
			}
		}
		if isExcluded {
			log.Printf("Skipping excluded server: %s", server)
			continue
		}

		conf, err := cmdcfg.Load(server)
		if err != nil {
			log.Fatal(fmt.Errorf("failed to load config: %w", err))
		}
		if env == "" || region == "" {
			env = conf.Environment
			region = conf.AWS.Region
		}

		for k, db := range conf.Databases {
			dbMap[k] = db
		}
		if conf.DBConfigMap != nil {
			for k, db := range conf.DBConfigMap {
				dbMap[k] = db
			}
		}
		if conf.UseCaseDBConfigMap != nil {
			for k, db := range conf.UseCaseDBConfigMap {
				dbMap[k] = db
			}
		}
	}
	fmt.Printf("Got db list , len : %d", len(dbMap))
	fmt.Println("Db map", dbMap)

	err = testtenant.SetupDBsForTest(env, region, dbMap)
	if err != nil {
		log.Fatal(fmt.Errorf("failed to setup DBs: %w", err))
	}
	log.Println("Successfully setup dbs")
}
