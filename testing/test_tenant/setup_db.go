package testtenant

import (
	"errors"
	"fmt"
	"io/ioutil"
	"os"
	"path"
	"path/filepath"
	"regexp"
	"runtime"
	"strings"
	"time"

	"github.com/mohae/deepcopy"

	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/be-common/pkg/cfg"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/logger"
)

var (
	_, b, _, _     = runtime.Caller(0)
	currentDirPath = filepath.Dir(b)
)

const (
	relativeMigrationPath    = "../../db/%s/latest.sql"
	relativeFixturePath      = "../../db/%s/fixture.sql"
	relativeGroupFixturePath = "../../../db/%s/fixtures/%s/fixture.sql"
)

var crdbRootUserSecrets map[string]string

func SetupDBsForTest(env string, region string, dbs map[string]*cfg.DB) error {
	for k, db := range dbs {
		if strings.EqualFold(db.Name, "postgres") {
			continue
		}
		isCRDB := (db.DbType == cfg.CRDB) || strings.HasSuffix(k, "CRDB")
		err := SetupDB(env, region, db, isCRDB)
		if err != nil {
			return err
		}
		if db.GormV2.EnableMultiDBSupport {
			dbResolverList := db.GormV2.DBResolverList
			for _, dbResolver := range dbResolverList {
				pgDsn := dbResolver.DbDsn
				err = SetupPGDB(env, region, pgDsn)
				if err != nil {
					return err
				}
			}
		}
	}
	return nil
}

// nolint:funlen,govet
func getGormConn(env string, region string, conf *cfg.DB, isCRDB bool) (db *gorm.DB, err error) {
	var dbConf *cfg.DB
	if isCRDB {

		if conf.GormV2.EnableMultiDBSupport {
			for _, dbResolver := range conf.GormV2.DBResolverList {
				dbResolver.DbDsn.SecretName = "qa/rds/postgres"
			}
		}

		defaultCRDBConf := &cfg.DB{
			DbType:   "CRDB",
			Host:     conf.Host,
			Port:     conf.Port,
			Username: "root",
			//nocustomlint:usegetter
			Name:                    conf.Name,
			SSLMode:                 conf.SSLMode,
			EnableDebug:             conf.EnableDebug,
			SSLRootCert:             env + "/cockroach/ca.crt",
			SSLClientCert:           env + "/cockroach/client.root",
			SSLClientKey:            env + "/cockroach/client.root",
			MaxOpenConn:             conf.MaxOpenConn,
			MaxIdleConn:             conf.MaxIdleConn,
			MaxConnTtl:              conf.MaxConnTtl,
			StatementTimeout:        conf.StatementTimeout,
			IdleInTxnSessionTimeout: conf.IdleInTxnSessionTimeout,
			GormV1:                  conf.GormV1,
			GormV2:                  conf.GormV2,
		}

		secretIds := make(map[string]string)
		secretIds = cfg.AddCrdbSslCertSecretIds(defaultCRDBConf, secretIds)
		crdbRootUserSecrets, err = cfg.LoadSecrets(secretIds, env, region)
		if err != nil {
			return nil, err
		}

		cfg.UpdateCRDBSecretValues(defaultCRDBConf, crdbRootUserSecrets)
		dbConf = defaultCRDBConf
	} else {
		dbConf = conf
		dbConf.SecretName = "qa/rds/postgres"
		dbConf.Username = ""
		dbConf.Password = ""

		secretIds := make(map[string]string)
		secretIds = addPgdbSecretIds(dbConf, secretIds)
		keyToSecret, err := cfg.LoadSecrets(secretIds, env, region)
		if err != nil {
			return nil, err
		}
		err = cfg.UpdatePGDBSecretValuesV3(dbConf, keyToSecret)
		if err != nil {
			return nil, err
		}
	}
	dbConf.InitTracer = false

	db, err = storagev2.NewGormDB(dbConf)
	if err != nil {
		return nil, err
	}
	return db, nil
}

// added this util to include only secretIDs, as some secrets are already populated.
func addPgdbSecretIds(pgdbCfg *cfg.DB, keyToId map[string]string) map[string]string {
	if keyToId == nil {
		keyToId = make(map[string]string)
	}
	if pgdbCfg.SecretName != "" {
		keyToId[cfg.GetPgdbSecretKeysName(pgdbCfg.GetName())] = pgdbCfg.SecretName
	}
	return keyToId
}

// SetupDB assumes that the DB is postgres if isCRDB is false
// nolint:funlen
func SetupDB(env string, region string, dbConf *cfg.DB, isCRDB bool) error {
	dbConfOrig := deepcopy.Copy(dbConf).(*cfg.DB)
	defaultDbConf := deepcopy.Copy(dbConf).(*cfg.DB)
	defaultDbConf.StatementTimeout = 400 * time.Second
	if isCRDB {
		defaultDbConf.Name = "defaultdb"
	} else {
		defaultDbConf.Name = "postgres"
	}

	defaultDb, err := getGormConn(env, region, defaultDbConf, isCRDB)
	if err != nil {
		return err
	}

	if isCRDB {
		err = DropCRDB(defaultDb, dbConf.GetName())
		if err != nil {
			logger.ErrorNoCtx("Error dropping crdb", zap.String("dbName", dbConf.GetName()), zap.Error(err))
			return err
		}
		err = CreateCRDB(defaultDb, dbConf.GetName())
		if err != nil {
			logger.ErrorNoCtx("Error creating crdb", zap.String("dbName", dbConf.GetName()), zap.Error(err))
			return err
		}

	} else {
		err = DropPGDB(defaultDb, dbConf.GetName())
		if err != nil {
			logger.ErrorNoCtx("Error dropping pgdb", zap.String("dbName", dbConf.GetName()), zap.Error(err))
			return err
		}
		err = CreatePGDB(defaultDb, dbConf.GetName())
		if err != nil {
			logger.ErrorNoCtx("Error creating pgdb", zap.String("dbName", dbConf.GetName()), zap.Error(err))
			return err
		}
	}
	db, err := getGormConn(env, region, dbConf, isCRDB)
	if err != nil {
		return err
	}
	pgdbAsAdmin := db
	if !isCRDB {
		defaultDbConf.Name = dbConf.Name
		pgdbAsAdmin, err = getGormConn(env, region, defaultDbConf, false)
		if err != nil {
			logger.ErrorNoCtx("failed to get pgdb conn", zap.String("dbName", defaultDbConf.GetName()), zap.Error(err))
			return err
		}
	}
	if isCRDB {
		err = grantCRDBPrivileges(defaultDb, db, dbConf)
		if err != nil {
			logger.ErrorNoCtx("Error granting permissions to crdb", zap.String("dbName", dbConf.GetName()), zap.Error(err))
			return err
		}
	} else {
		err = grantPGDBAdminPrivileges(defaultDb, pgdbAsAdmin, defaultDbConf, dbConf)
		if err != nil {
			logger.ErrorNoCtx("Error granting permissions to pgdb", zap.String("dbName", dbConf.GetName()), zap.Error(err))
			return err
		}

		err = grantPGDBUserPrivileges(defaultDb, pgdbAsAdmin, defaultDbConf, dbConf, dbConfOrig)
		if err != nil {
			logger.ErrorNoCtx("Error granting permissions to pgdb", zap.String("dbName", dbConf.GetName()), zap.Error(err))
			return err
		}
	}

	// backup and restore epifidb data from preonboarded users docker image
	if os.Getenv("USE_PREONBOARDED_DATA") == "true" && (dbConf.GetName() == fmt.Sprintf("epifi_%s", cfg.GetTestTenant()) || dbConf.GetName() == fmt.Sprintf("simulator_%s", cfg.GetTestTenant())) {
		logger.InfoNoCtx("Using s3 backup to restore db tables")
		restoreTablesFromS3(db, dbConf)
	} else {

		//nocustomlint:usegetter
		err = LoadSchema(pgdbAsAdmin, dbConf.Name)
		if err != nil {
			return err
		}
		//nocustomlint:usegetter
		err = AddFixtureData(pgdbAsAdmin, dbConf.Name)
		if err != nil && !errors.Is(err, os.ErrNotExist) {
			// skip reporting error if the fixture file doesn't exist
			return err
		}
	}
	return nil

}

// SetupPGDB It's used for handling multidb support, assuming that only PGDB will be added
// nolint:funlen
func SetupPGDB(env string, region string, pgDsn *cfg.PgDsn) error {
	dbConf := cfg.GetDBConfFromPgDsnConf(pgDsn)
	dbConfOrig := deepcopy.Copy(dbConf).(*cfg.DB)
	defaultDbConf := deepcopy.Copy(dbConf).(*cfg.DB)
	defaultDbConf.StatementTimeout = 400 * time.Second
	defaultDbConf.Name = "postgres"

	defaultDb, err := getGormConn(env, region, defaultDbConf, false)
	if err != nil {
		return err
	}

	err = DropPGDB(defaultDb, dbConf.GetName())
	if err != nil {
		logger.ErrorNoCtx("Error dropping db", zap.String("dbName", dbConf.GetName()), zap.Error(err))
		return err
	}
	err = CreatePGDB(defaultDb, dbConf.GetName())
	if err != nil {
		logger.ErrorNoCtx("Error creating db", zap.String("dbName", dbConf.GetName()), zap.Error(err))
		return err
	}

	defaultDbConf.Name = dbConf.Name
	pgdbAsAdmin, err := getGormConn(env, region, defaultDbConf, false)
	if err != nil {
		logger.ErrorNoCtx("failed to get db conn", zap.String("dbName", defaultDbConf.GetName()), zap.Error(err))
		return err
	}
	err = grantPGDBAdminPrivileges(defaultDb, pgdbAsAdmin, defaultDbConf, dbConf)
	if err != nil {
		logger.ErrorNoCtx("Error granting permissions to db", zap.String("dbName", dbConf.GetName()), zap.Error(err))
		return err
	}

	err = grantPGDBUserPrivileges(defaultDb, pgdbAsAdmin, defaultDbConf, dbConf, dbConfOrig)
	if err != nil {
		logger.ErrorNoCtx("Error granting permissions to db", zap.String("dbName", dbConf.GetName()), zap.Error(err))
		return err
	}

	if os.Getenv("USE_PREONBOARDED_DATA") != "true" {
		//nocustomlint:usegetter
		err = LoadSchema(pgdbAsAdmin, dbConf.Name)
		if err != nil {
			return err
		}
		//nocustomlint:usegetter
		err = AddFixtureData(pgdbAsAdmin, dbConf.Name)
		if err != nil && !errors.Is(err, os.ErrNotExist) {
			// skip reporting error if the fixture file doesn't exist
			return err
		}
	}
	return nil

}

func restoreTablesFromS3(db *gorm.DB, dbConf *cfg.DB) {
	type restoreResult struct {
		JobId             int
		Status            string
		FractionCompleted float64
		Rows              int
		IndexEntries      int
		Bytes             int
	}
	// This data is uploaded by https://jenkins-deploy.pointz.in/job/Scripts/job/Backend/job/OnboardUsersForAcceptance/
	result := &restoreResult{}
	dbname := dbConf.GetName()
	//nocustomlint:usegetter
	if err := db.Raw(fmt.Sprintf("RESTORE table %s.* FROM LATEST IN 's3://epifi-%s-crdb/smoke-test-tenant-preonboarded/%s_backup?AUTH=implicit' WITH into_db='%s';", dbname, os.Getenv("ENVIRONMENT"), dbConf.Name, dbname)).Scan(result).Error; err != nil {
		logger.Panic("Error restoring db from S3", zap.Error(err))
	}

	logger.InfoNoCtx("Tables restored from S3", zap.Any("result", result))
}

func CreateCRDB(db *gorm.DB, name string) error {
	if err := db.Exec("CREATE DATABASE IF NOT EXISTS " + name).Error; err != nil {
		logger.ErrorNoCtx("failed to create database", zap.String("name", name), zap.Error(err))
		return err
	}

	return nil
}

func grantCRDBPrivileges(defaultdb, db *gorm.DB, conf *cfg.DB) error {
	if err := defaultdb.Exec(fmt.Sprintf("GRANT CONNECT on database %s to %s", conf.GetName(), conf.Username)).Error; err != nil {
		logger.ErrorNoCtx("failed to grant privileges", zap.Error(err))
		return err
	}

	if err := db.Exec(fmt.Sprintf("ALTER DEFAULT PRIVILEGES GRANT SELECT, INSERT, DELETE, UPDATE on tables to %s", conf.Username)).Error; err != nil {
		logger.ErrorNoCtx("failed to grant privileges", zap.Error(err))
		return err
	}
	return nil
}

func grantPGDBAdminPrivileges(defaultdb, db *gorm.DB, adminUserConf, conf *cfg.DB) error {
	if err := defaultdb.Exec(fmt.Sprintf("GRANT CONNECT on database %s to %s", conf.GetName(), conf.Username)).Error; err != nil {
		logger.ErrorNoCtx("failed to grant privileges", zap.Error(err))
		return err
	}

	if err := db.Exec(fmt.Sprintf("ALTER DEFAULT PRIVILEGES FOR USER %s GRANT SELECT, INSERT, DELETE, UPDATE ON TABLES TO %s", adminUserConf.Username, conf.Username)).Error; err != nil {
		logger.ErrorNoCtx("failed to grant privileges", zap.Error(err))
		return err
	}

	if err := db.Exec(fmt.Sprintf("ALTER DEFAULT PRIVILEGES FOR USER %s GRANT USAGE, SELECT ON SEQUENCES TO %s", adminUserConf.Username, conf.Username)).Error; err != nil {
		logger.ErrorNoCtx("failed to grant privileges", zap.Error(err))
		return err
	}
	return nil
}

func grantPGDBUserPrivileges(defaultdb, db *gorm.DB, adminUserConf, conf *cfg.DB, origConf *cfg.DB) error {
	pgdbUser := origConf.Username

	if err := defaultdb.Exec(fmt.Sprintf("GRANT CONNECT on database %s to \"%s\"", conf.GetName(), pgdbUser)).Error; err != nil {
		logger.ErrorNoCtx("failed to grant privileges", zap.Error(err))
		return err
	}

	if err := db.Exec(fmt.Sprintf("ALTER DEFAULT PRIVILEGES FOR USER %s GRANT SELECT, INSERT, DELETE, UPDATE ON TABLES TO \"%s\"", adminUserConf.Username, pgdbUser)).Error; err != nil {
		logger.ErrorNoCtx("failed to grant privileges", zap.Error(err))
		return err
	}

	if err := db.Exec(fmt.Sprintf("ALTER DEFAULT PRIVILEGES FOR USER %s GRANT USAGE, SELECT ON SEQUENCES TO \"%s\"", adminUserConf.Username, pgdbUser)).Error; err != nil {
		logger.ErrorNoCtx("failed to grant privileges", zap.Error(err))
		return err
	}
	return nil
}

func DropCRDB(db *gorm.DB, name string) error {
	if !strings.Contains(name, "_"+cfg.GetTestTenant()) {
		return fmt.Errorf("db name doesn't include _%v string", cfg.GetTestTenant())
	}
	if err := db.Exec("DROP DATABASE IF EXISTS " + name).Error; err != nil {
		logger.ErrorNoCtx("failed to create database", zap.String("name", name), zap.Error(err))
		return err
	}
	return nil
}

func CreatePGDB(db *gorm.DB, name string) error {
	if err := db.Exec("CREATE DATABASE " + name).Error; err != nil {
		logger.ErrorNoCtx("failed to create database", zap.String("name", name), zap.Error(err))
		return err
	}
	return nil
}

func DropPGDB(db *gorm.DB, name string) error {
	if !strings.Contains(name, "_"+cfg.GetTestTenant()) {
		return fmt.Errorf("db name doesn't include _%v string", cfg.GetTestTenant())
	}
	err := db.Exec("SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE pg_stat_activity.datname =" + "'" + name + "'  AND pid <> pg_backend_pid()").Error
	if err != nil {
		logger.ErrorNoCtx("Error while closing db conn:", zap.Error(err))
		return err
	} else {
		logger.InfoNoCtx(fmt.Sprintf("Closed %s db conn, if existed", name))
	}
	if err = db.Exec("DROP DATABASE IF EXISTS " + name).Error; err != nil {
		logger.ErrorNoCtx("failed to drop database", zap.String("name", name), zap.Error(err))
		return err
	}
	return nil
}

func LoadSchema(db *gorm.DB, srcDBName string) error {
	st := time.Now()
	defer func() {
		logger.InfoNoCtx("schema load time.....", zap.String("srcDBName", srcDBName), zap.Duration("dur", time.Since(st)))
	}()
	var relativeMigrationPathWithDB string
	grpDbName := storagev2.GetGroupedDbNameBySubDbName(srcDBName)
	if grpDbName != "" {
		relativeMigrationPathWithDB = fmt.Sprintf(relativeMigrationPath, grpDbName)
	} else {
		relativeMigrationPathWithDB = fmt.Sprintf(relativeMigrationPath, srcDBName)
	}
	inpSqlPath := path.Join(currentDirPath, relativeMigrationPathWithDB)

	// Load schema file
	schemaSql, err := ioutil.ReadFile(filepath.Clean(inpSqlPath))
	if err != nil {
		logger.ErrorNoCtx("can't read schema file", zap.Error(err))
		return fmt.Errorf("can't read schema file : %w", err)
	}
	// Apply DB Schema
	if err = db.Exec(string(schemaSql)).Error; err != nil {
		logger.ErrorNoCtx("can't load schema", zap.Error(err), zap.String("db", srcDBName))
		return fmt.Errorf("can't load schema : %w", err)
	}
	logger.InfoNoCtx("loaded schema", zap.String("srcDBName", srcDBName))
	refreshMaterialisedView(db, schemaSql)
	return nil
}

func refreshMaterialisedView(db *gorm.DB, schemaSql []byte) {
	matches := regexp.MustCompile(`CREATE MATERIALIZED VIEW public.(\w+)`).FindAllStringSubmatch(string(schemaSql), -1)
	for _, match := range matches {
		viewName := match[1]
		if err := db.Exec("REFRESH MATERIALIZED VIEW public." + viewName).Error; err != nil {
			logger.ErrorNoCtx("Error refreshing materialized view", zap.String("name", viewName))
		}
	}
}

func AddFixtureData(db *gorm.DB, srcDBName string) error {
	st := time.Now()
	defer func() {
		logger.InfoNoCtx("fixture load time.....", zap.String("srcDBName", srcDBName), zap.String("srcDBName", srcDBName), zap.Duration("dur", time.Since(st)))
	}()

	var relativeFixturePathWithDB string
	grpDbName := storagev2.GetGroupedDbNameBySubDbName(srcDBName)
	if grpDbName != "" {
		relativeFixturePathWithDB = fmt.Sprintf(relativeGroupFixturePath, grpDbName, srcDBName)
	} else {
		relativeFixturePathWithDB = fmt.Sprintf(relativeFixturePath, srcDBName)
	}
	inpSqlPath := path.Join(currentDirPath, relativeFixturePathWithDB)

	// Load schema file
	schemaSql, err := ioutil.ReadFile(filepath.Clean(inpSqlPath))
	if err != nil {
		logger.ErrorNoCtx("can't read fixture file", zap.Error(err))
		return fmt.Errorf("can't read fixture file : %w", err)
	}

	// Apply DB Schema
	if err = db.Exec(string(schemaSql)).Error; err != nil {
		logger.ErrorNoCtx("can't load fixture", zap.Error(err))
		return fmt.Errorf("can't load fixture : %w", err)
	}
	logger.InfoNoCtx("loaded fixture", zap.String("srcDBName", srcDBName))
	return nil
}
