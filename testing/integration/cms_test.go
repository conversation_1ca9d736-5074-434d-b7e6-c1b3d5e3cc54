package integration

import (
	"testing"

	"github.com/epifi/gamma/testing/integration/app"

	"github.com/epifi/be-common/pkg/owners"
)

func CmsFlows(t *testing.T) {
	defer Recover(t)

	cts := app.NewCmsTestSuite(cmsServiceClient)

	t.Run("cms test", func(t *testing.T) {
		RunSubtest(t, "cms process order happy flow", func(t *testing.T) {
			defer Recover(t)
			cts.TestCms_ProcessOrderHappyFlow(t)
		})
	})
}

func CmsTestCases() []*TestCase {
	return []*TestCase{
		{
			TestProperty: &TestProperty{
				Description: "This is test for cms flows",
				BU:          owners.BUSINESS_UNIT_GROWTH_AND_EXPERIENCE,
				Scope:       []Scope{Smoke, Acceptance},
			},
			Test: CmsFlows,
		},
	}
}
