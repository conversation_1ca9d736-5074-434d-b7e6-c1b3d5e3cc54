package config

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"sync"
	"time"

	"github.com/samber/lo"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifitemporal"
)

var (
	once   sync.Once
	config *Config
	err    error
)

const (
	Integration            = "integration"
	SmokeTestTenantService = "smoke-test-tenant"
)

var (
	_, b, _, _     = runtime.Caller(0)
	packageDirPath = filepath.Dir(b)
)

func Load(serviceName string) (*Config, error) {
	once.Do(func() {
		config, err = loadConfig(serviceName)
	})

	if err != nil {
		return nil, err
	}
	return config, err
}

func loadConfig(serviceName string) (*Config, error) {
	k, _, err := cfg.LoadConfigUsingKoanf(packageDirPath, cfg.ServiceName(serviceName))
	if err != nil {
		return nil, fmt.Errorf("failed to load viper config: %w", err)
	}

	conf := &Config{}
	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, errors.Wrap(err, "failed to refresh dynamic config")
	}

	// Apply overrides from the env variables
	if err = readAndSetEnv(conf); err != nil {
		return nil, fmt.Errorf("failed to read and set env var: %w", err)
	}

	if conf.Flags.UseLocalServer {
		dbConfigList := make([]*cfg.DB, 0)
		for _, dbCfg := range conf.Databases {
			if dbCfg.GormV2 == nil {
				dbCfg.GormV2 = &cfg.GormV2Conf{
					LogLevelGormV2:        "ERROR",
					SlowQueryLogThreshold: 5 * time.Second,
					UseInsecureLog:        true,
				}
			}
			if dbCfg.Username == "" {
				dbCfg.Username = "root"
			}
			dbCfg.MaxOpenConn = 5
			dbCfg.MaxIdleConn = 2
			dbCfg.EnableDebug = true
			dbConfigList = append(dbConfigList, dbCfg)
		}

		if err = cfg.UpdateDbEndpointInConfigV2(dbConfigList...); err != nil {
			return nil, err
		}
		return conf, nil
	}

	dbMap := conf.Databases
	allDbConfigs := []*cfg.DB{
		dbMap[EpifiDb],
		dbMap[SimulatorDb],
		dbMap[EpifiWealthDb],
		dbMap[CreditCardPgDb],
		dbMap[AuthDb],
		dbMap[KycDb],
		dbMap[PayDb],
		dbMap[TieringDb],
		dbMap[ConnectedAccountDb],
		dbMap[CommsDb],
		dbMap[RecurringPaymentDb],
		dbMap[P2PInvestmentLiquiloansDb],
		dbMap[ActorDb],
		dbMap[TimelineDb],
		dbMap[PaymentInstrumentDb],
		dbMap[FrmCrdb],
	}

	// Add loan database configs to the slice
	for _, dbConfig := range conf.LoansDbConfigMap.GetOwnershipToDbConfigMap() {
		allDbConfigs = append(allDbConfigs, dbConfig)
	}

	_, err = cfg.LoadSecretsAndPrepareDBConfig(conf.Secrets, conf.Application.Environment, conf.AWS.Region, allDbConfigs...)
	if err != nil {
		return nil, fmt.Errorf("failed to load secret and prepare db config: %w", err)
	}

	return conf, nil
}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config) error {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Name = val
	}

	if val, ok := os.LookupEnv("SERVER_PORT"); ok {
		intVal, err := strconv.Atoi(val)
		if err != nil {
			return fmt.Errorf("failed to convert port to int: %w", err)
		}

		c.Server.Port = intVal
	}

	var crdbHostEndpoint, pgdbHostEndpoint string

	if val, ok := os.LookupEnv("DB_HOST"); ok {
		crdbHostEndpoint = val
	}

	if val, ok := os.LookupEnv("PGDB_HOST"); ok {
		pgdbHostEndpoint = val
	}

	if val := os.Getenv("USE_PRELOADED_IMAGES"); val == "TRUE" {
		c.UsePreloadedData.UseImages = true
	}

	if acceptance, ok := os.LookupEnv("ACCEPTANCE_TEST"); ok && acceptance == "TRUE" {
		c.Flags.SkipWaitForDBStart = false
	}

	populateHostEndpointInDbOwnershipMapConfig(crdbHostEndpoint, pgdbHostEndpoint, c.LoansDbConfigMap.GetOwnershipToDbConfigMap())

	populateHostEndpointInDbConfigs(crdbHostEndpoint, pgdbHostEndpoint, c.Databases[EpifiDb], c.Databases[SimulatorDb], c.Databases[EpifiWealthDb], c.Databases[CreditCardPgDb], c.Databases[P2PInvestmentLiquiloansDb], c.Databases[AuthDb], c.Databases[ConnectedAccountDb], c.Databases[KycDb], c.Databases[PayDb], c.Databases[TieringDb], c.Databases[CommsDb], c.Databases[FrmCrdb])

	return nil
}

func populateHostEndpointInDbOwnershipMapConfig(crdbEndpoint, pgdbEndpoint string, dbConfigMap map[commontypes.Ownership]*cfg.DB) {
	dbConfigs := lo.MapToSlice(dbConfigMap, func(_ commontypes.Ownership, dbConf *cfg.DB) *cfg.DB {
		return dbConf
	})
	populateHostEndpointInDbConfigs(crdbEndpoint, pgdbEndpoint, dbConfigs...)
}

func populateHostEndpointInDbConfigs(crdbEndpoint, pgdbEndpoint string, dbConfigs ...*cfg.DB) {
	for _, dbConf := range dbConfigs {
		switch dbConf.DbType {
		case cfg.PGDB:
			dbConf.Host = pgdbEndpoint
		case cfg.CRDB:
			dbConf.Host = crdbEndpoint
		}
	}
}

type Config struct {
	Application                 *application
	Server                      *server
	AWS                         *cfg.AWS
	Flags                       *Flags
	UserPool                    *UserPoolConfig
	Databases                   map[string]*cfg.DB
	LoansDbConfigMap            *cfg.DbConfigMap
	RedisCluster                map[string]*cfg.RedisOptions
	EmbeddedServers             map[cfg.ServerName]bool
	PreOnboardedPhoneNumbers    []uint64
	PayPreOnboardedPhoneNumbers []uint64
	UsePreloadedData            *UsePreloadedData
	S3                          *S3
	Secrets                     *cfg.Secrets

	// list of workers for which workflow replayer is enabled as part of smoke tests
	// only the workflows hosted as part of these workers can be replayed to test
	// against non-determinism.
	WorkflowReplayerEnabledWorkers []epifitemporal.Worker
	PgdbConnectionFlags            *PgdbConnectionFlags
	DbConnectionAliases            *DbConnectionAliases
	ServerInfo                     []*ServerInfo
	TemporalWorkerInfo             []*TemporalWorkerInfo
	WaitBeforeHealthCheck          time.Duration
	SlackConfig                    *SlackConfig
}

type DbConnectionAliases struct {
	CreditCardPgdbConnAlias        string
	ActorPgdbConnAlias             string
	PaymentInstrumentPgdbConnAlias string
}

type PgdbConnectionFlags struct {
	UsePgdbConnectionForCcDb bool
}

type S3 struct {
	UsersBucketName string
}

type application struct {
	Environment            string
	Name                   string
	AndroidClientSignature string
	// Android Client ID registered with Google OAuth service
	OAuthAndroidClientID string
	// iOS Client ID registered with Google OAuth service
	OAuthIOSClientID              string
	UserAccessTokenSigningMethod  *TokenSigningMethod
	UserRefreshTokenSigningMethod *TokenSigningMethod
	RelativeLivenessVideoFilePath string
	NPCIDeviceBindingLimitPerDay  int64
	AaBaseUrl                     string
}

type TokenSigningMethod struct {
	// Token validity in seconds
	Duration int
	// Inactivity timer after which the token would be marked as inactive/expired
	InActivityTimer int
	// Signing key
	Key string
	// Hashing algo used in JWT signing
	Algo string
}

type server struct {
	Port            int
	HealthCheckPort int
}

type Flags struct {
	// A flag to determine if the debug message in Status is to be trimmed
	TrimDebugMessageFromStatus bool

	// Flag for running tests on local faster.
	// If the flag is set to true, initial one time setup
	// is skipped including dropping & recreating db,
	// installing Goreman, localstack check.
	// It assumes that initial setup is done - db has
	// tables, localstack is up and Goreman is installed.
	SkipOneTimeSetup bool

	// Flag to skip wait time for DB start
	// No waiting is required when testing in local as DB is always up and running
	// Waiting is required when using images in jobs and workflows
	SkipWaitForDBStart bool

	// Skip DB init of rewardsDb, casperDb & accrualDb
	SkipNonBankingDBInit bool

	// Skip DB initialisation of Pinot
	InitialisePinotDB bool

	// print logs to stdout instead of acceptance/output
	PrintLogsToStdout bool

	// enable preparation and initialisation of credit card db
	EnableCreditCardDbInit bool

	EnableFRMDBInit bool

	// Enable initialization of p2p-investment DB
	EnableP2PInvestmentDbInit bool

	// defines if the data is preloaded or not.
	// if the data is preloaded we will not truncated the db while setup
	IsDataPreloaded bool

	UseLocalServer bool

	// all, none, or a list of servers to install
	InstallServers []string
}

type UserPoolConfig struct {
	PoolSize          int
	AllocationTimeout time.Duration
}

type UsePreloadedData struct {
	// If true, downloads data dump from S3 and applies them to the corresponding DBs
	// Rest of the DBs uses fixtures from local
	IsEnabled bool
	// This flag is enabled only if USE_PRELOADED_IMAGES env variable is equal to TRUE. This is to enable using images with preloaded data.
	// For DBs not available in the image, fixtures are used to populate the DB
	UseImages bool
}

type ServerInfo struct {
	Name            string
	HealthCheckPort int
	HttpHealthCheck bool // This flag is added in case we need to do http based health checks (for eg. vnotificationgw)
	IsOptional      bool // if true, health check  is skipped for the server
	IsSecureConn    bool
}

type TemporalWorkerInfo struct {
	Name                   string
	HealthCheckPort        int
	IsOptional             bool // if true, health check  is skipped for the server
	SkipBinaryInstallation bool
}

type SlackConfig struct {
	SendSlackNotification        bool
	SlackChannelID               string
	BuildReleaseQaSlackChannelID string
}

const (
	EpifiDb                   = "EpifiDb"
	ActorDb                   = "ActorDb"
	AuthDb                    = "AuthDb"
	SimulatorDb               = "SimulatorDb"
	EpifiWealthDb             = "EpifiWealthDb"
	P2PInvestmentLiquiloansDb = "P2PInvestmentLiquiloansDb"
	KycDb                     = "KycDb"
	PayDb                     = "PayDb"
	RecurringPaymentDb        = "RecurringPaymentDb"
	TimelineDb                = "TimelineDb"
	PaymentInstrumentDb       = "PaymentInstrumentDb"
	TieringDb                 = "TieringDb"
	CommsDb                   = "CommsDb"
	CreditCardPgDb            = "CreditCardPgDb"
	ConnectedAccountDb        = "ConnectedAccountDb"
	FrmPgdb                   = "FRMPgdb"
	FrmCrdb                   = "FRMDB"

	ActorRedis              = "ActorRedis"
	UserRedis               = "UserRedis"
	PiRedis                 = "PiRedis"
	SavingsLedgerReconRedis = "SavingsLedgerReconRedis"
	TimelineRedis           = "TimelineRedis"
	SavingsRedis            = "SavingsRedis"
	DevRegRedis             = "DevRegRedis"
	LendingRedis            = "LendingRedis"
	CardRedis               = "CardRedis"
	SlackToken              = "SlackToken"
)
