//go:build quest
// +build quest

package quest

import (
	"fmt"
	"math/rand"
	"os"
	"testing"
	"time"

	"github.com/epifi/gamma/api/segment"

	cmdcfg "github.com/epifi/be-common/pkg/cmd/config"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/cache"

	redisV9 "github.com/redis/go-redis/v9"
	gormv2 "gorm.io/gorm"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cmd/config/genconf"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/storage"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/gamma/api/quest/frontend"
	"github.com/epifi/gamma/api/quest/manager"
	"github.com/epifi/gamma/quest/config"
)

var (
	questPgdb          *gormv2.DB
	questFeClient      frontend.FrontendClient
	questManagerClient manager.ManagerClient
	segmentClient      segment.SegmentationServiceClient
	questRedisClient   *redisV9.Client
	questCache         cache.CacheStorage
	conf               *config.Config
)

func initServerClients() func() {
	questConn := epifigrpc.NewConnByServiceSmokeTest(cfg.QUEST_SERVICE)
	questFeClient = frontend.NewFrontendClient(questConn)
	questManagerClient = manager.NewManagerClient(questConn)
	segmentConn := epifigrpc.NewConnByServiceSmokeTest(cfg.SEGMENT_SERVICE)
	segmentClient = segment.NewSegmentationServiceClient(segmentConn)
	return func() {
		epifigrpc.CloseConn(questConn)
	}
}

func InitTest() (*gormv2.DB, *redisV9.Client, *config.Config, func()) {
	// Init config
	var err error
	conf, err = config.Load()
	if err != nil {
		logger.Fatal(fmt.Sprintf("failed to load config: %v", err))
	}
	gconf, err := genconf.Load(cfg.NEBULA_SERVER)
	if err != nil {
		logger.Fatal(fmt.Sprintf("failed to load dynamic conf %v", err))
	}
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Fatal("failed to get env", zap.Error(err))
	}
	questRedisClient = storage.NewRedisClientFromConfig(gconf.RedisClusters()[cmdcfg.QuestRedisStore], gconf.Tracing().Enable)
	questCache = cache.NewRedisCacheStorage(questRedisClient)
	if cfg.IsTestTenantEnabled() {
		logger.Fatal("test tenant setup is no longer supported")
	}

	// Init db connection
	questPgdb, err = storagev2.NewPostgresDBWithConfig(gconf.Databases()[cmdcfg.QuestPGDB], gconf.Tracing().Enable)
	if err != nil {
		logger.Fatal(fmt.Sprintf("failed to connect to db: %v", err))
	}
	// Init server clients
	closeClientsConn := initServerClients()
	return questPgdb, questRedisClient, conf, func() {
		_ = logger.Log.Sync()
		closeClientsConn()
	}
}

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	logger.Init(cfg.TestEnv)
	rand.Seed(time.Now().UTC().UnixNano())

	var teardown func()
	questPgdb, questRedisClient, conf, teardown = InitTest()
	logger.InfoNoCtx("Ended Setup")
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
