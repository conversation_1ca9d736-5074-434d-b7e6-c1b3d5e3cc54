package integration

import (
	"fmt"
	"reflect"
	"regexp"
	"runtime"
	"strings"
	"testing"

	"github.com/epifi/be-common/pkg/owners"

	"golang.org/x/exp/slices"
)

var (
	TestPattern    string
	SubtestPattern string
	BuArgs         string
	ScopeArgs      string
	BUs            []string
	Scopes         []string
)

// TestProperty defines properties for a test case.
type TestProperty struct {
	Description string
	BU          owners.BusinessUnit
	Scope       []Scope
}

// TestCase represents a single test case.
type TestCase struct {
	TestProperty *TestProperty
	Test         func(t *testing.T)
}

// RunSubtest executes a subtest conditionally based on SubtestPattern.
// This should be used instead of t.Run when subtest filtering is needed.
func RunSubtest(t *testing.T, name string, f func(t *testing.T)) bool {
	if SubtestPattern == ".*" || SubtestPattern == "" {
		// If pattern is default, run all subtests
		return t.Run(name, f)
	}

	// Check if subtest name matches the pattern
	matched, err := regexp.MatchString(SubtestPattern, name)
	if err != nil {
		fmt.Printf("Subtest pattern regex error: %v\n", err)
		return false
	}

	if matched {
		return t.Run(name, f)
	}

	// Skip this subtest if it doesn't match
	fmt.Printf("Skipping subtest '%s' (doesn't match pattern '%s')\n", name, SubtestPattern)
	return true
}

func RunTestCases(t *testing.T, testcases []*TestCase) {
	for _, tc := range testcases {

		testName, err := GetTestName(tc.Test)
		if err != nil {
			fmt.Println("unable to get test name: ", err)
		}

		tp := tc.TestProperty
		testBu := tp.BU
		testScope := tp.Scope

		isBuPresent := (len(BUs) == 0) || slices.Contains(BUs, string(testBu))

		isScopePresent := len(Scopes) == 0
		for _, sc := range testScope {
			if slices.Contains(Scopes, string(sc)) {
				isScopePresent = true
				break
			}
		}

		m, err := regexp.Match(TestPattern, []byte(testName))
		if err != nil {
			fmt.Println("Test name regex is incorrect: ", err)
			continue
		}

		if m && isBuPresent && isScopePresent {
			t.Run(testName, func(t *testing.T) {
				tc.Test(t)
			})
		}
	}
}

func GetTestName(testFunc interface{}) (string, error) {
	fnPtr := reflect.ValueOf(testFunc).Pointer()

	fn := runtime.FuncForPC(fnPtr)
	if fn == nil {
		return "", fmt.Errorf("unable to get Test Name")
	}
	fullName := fn.Name()
	parts := strings.Split(fullName, ".")
	if len(parts) == 0 {
		return "", fmt.Errorf("unexpected format of the function name")
	}

	// Return the last part, which is the function name
	return parts[len(parts)-1], nil
}

type Scope string

const (
	Smoke      Scope = "smoke"
	Acceptance Scope = "acceptance"
)
