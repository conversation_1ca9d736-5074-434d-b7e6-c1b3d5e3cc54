// nolint: gosec
package integration

import (
	"context"
	"database/sql"
	"flag"
	"fmt"
	"io/ioutil"
	"math/rand"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/epifi/be-common/pkg/owners"

	"github.com/slack-go/slack"

	ownerconfig "github.com/epifi/be-common/pkg/owners/config"

	operationalStatusPb "github.com/epifi/gamma/api/accounts/operstatus"
	cmPb "github.com/epifi/gamma/api/risk/case_management"
	beSavingsPb "github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"

	fePalPb "github.com/epifi/gamma/api/frontend/preapprovedloan"
	bePalPb "github.com/epifi/gamma/api/preapprovedloan"

	"google.golang.org/grpc"

	// nolint:depguard
	"github.com/stretchr/testify/require"
	workflowSvcPb "go.temporal.io/api/workflowservice/v1"
	"go.uber.org/zap"
	"golang.org/x/exp/slices"
	gormv2 "gorm.io/gorm"

	"github.com/epifi/gamma/testing/integration/app"
	"github.com/epifi/gamma/testing/integration/cluster"
	"github.com/epifi/gamma/testing/integration/config"
	"github.com/epifi/gamma/testing/integration/local"

	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/storage"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/be-common/pkg/epifitemporal"
	epifitemporalTest "github.com/epifi/be-common/pkg/epifitemporal/test"

	accrualPb "github.com/epifi/gamma/api/accrual"
	authPb "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/bankcust"
	cardProvPb "github.com/epifi/gamma/api/card/provisioning"
	casperPb "github.com/epifi/gamma/api/casper"
	exchangerPb "github.com/epifi/gamma/api/casper/exchanger"
	cmsPb "github.com/epifi/gamma/api/cms"
	beCaPb "github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/frontend/account/screening"
	feSignupPb "github.com/epifi/gamma/api/frontend/account/signup"
	feUpiPb "github.com/epifi/gamma/api/frontend/account/upi"
	authOrchPb "github.com/epifi/gamma/api/frontend/auth/orchestrator"
	feCardPb "github.com/epifi/gamma/api/frontend/card"
	feCaPb "github.com/epifi/gamma/api/frontend/connected_account"
	feConsentPb "github.com/epifi/gamma/api/frontend/consent"
	creditreportpb "github.com/epifi/gamma/api/frontend/credit_report"
	feDepositPb "github.com/epifi/gamma/api/frontend/deposit"
	feFireflyPb "github.com/epifi/gamma/api/frontend/firefly"
	feP2pInvPb "github.com/epifi/gamma/api/frontend/p2pinvestment"
	fePayPb "github.com/epifi/gamma/api/frontend/pay"
	feTransactionPb "github.com/epifi/gamma/api/frontend/pay/transaction"
	feRecurringPaymentPb "github.com/epifi/gamma/api/frontend/recurringpayment"
	feRewardsPb "github.com/epifi/gamma/api/frontend/rewards"
	feSalaryPgPb "github.com/epifi/gamma/api/frontend/salaryprogram"
	feSavingsPb "github.com/epifi/gamma/api/frontend/savings"
	feTieringPb "github.com/epifi/gamma/api/frontend/tiering"
	feTimelinePb "github.com/epifi/gamma/api/frontend/timeline"
	feUpiOnboardingPb "github.com/epifi/gamma/api/frontend/upi/onboarding"
	feUserPb "github.com/epifi/gamma/api/frontend/user"
	feWoPb "github.com/epifi/gamma/api/frontend/wealthonboarding"
	orderPb "github.com/epifi/gamma/api/investment/mutualfund/order"
	"github.com/epifi/gamma/api/investment/mutualfund/order/filegenerator"
	schedulerPb "github.com/epifi/gamma/api/investment/mutualfund/order/scheduler"
	paymentPb "github.com/epifi/gamma/api/investment/mutualfund/payment_handler"
	p2pInvPb "github.com/epifi/gamma/api/p2pinvestment"
	accountPIPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	beRPPb "github.com/epifi/gamma/api/recurringpayment"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	projectorPb "github.com/epifi/gamma/api/rewards/projector"
	rewardOffersPb "github.com/epifi/gamma/api/rewards/rewardoffers"
	beSalaryPb "github.com/epifi/gamma/api/salaryprogram"
	beTieringPb "github.com/epifi/gamma/api/tiering"
	userPb "github.com/epifi/gamma/api/user"
	vnRpFederalPb "github.com/epifi/gamma/api/vendornotification/openbanking/recurringpayment/federal"
	waitlistPb "github.com/epifi/gamma/api/waitlist"
	beWoPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/investment/mutualfund/order/filegenerator/dao"
	"github.com/epifi/gamma/scripts/crud/userdata"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
)

var (
	_, b, _, _     = runtime.Caller(0)
	currentDirPath = filepath.Dir(b)
)

var (
	conf *config.Config
	once sync.Once
	c    *cluster.Cluster
	// TODO(Brijesh): Remove V2 suffix from var names
	epifiDbV2                      *gormv2.DB
	actorPgdb                      *gormv2.DB
	authPgdb                       *gormv2.DB
	recurringPaymentDb             *gormv2.DB
	simulatorDb                    *gormv2.DB
	epifiWealthDbV2                *gormv2.DB
	p2pInvestmentLiquiloansDb      *gormv2.DB
	creditCardDb                   *gormv2.DB
	kycDb                          *gormv2.DB
	payDb                          *gormv2.DB
	tieringDb                      *gormv2.DB
	commsDb                        *gormv2.DB
	timelineDb                     *gormv2.DB
	paymentInstrumentDb            *gormv2.DB
	frmDb                          *gormv2.DB
	userPool                       chan *app.PooledUser
	aaBaseUrl                      string
	userRedisCacheStorage          *cache.RedisCacheStorage
	actorRedisCacheStorage         *cache.RedisCacheStorage
	piRedisCacheStorage            *cache.RedisCacheStorage
	savingsLedgerReconCacheStorage *cache.RedisCacheStorage
	timelineRedisCacheStorage      *cache.RedisCacheStorage
	savingsRedisCacheStorage       *cache.RedisCacheStorage
	devRegRedisCacheStorage        *cache.RedisCacheStorage
	lendingRedisCacheStorage       *cache.RedisCacheStorage
	connectedAccountPgdb           *gormv2.DB
	dbConns                        map[string]*gormv2.DB
	loansDbResourcesTeardown       func()
	loansDbResourceProvider        *storagev2.DBResourceProvider[*gormv2.DB]
)

var (
	simSqlDbV2                   *sql.DB
	epifiSqlDb                   *sql.DB
	epifiWealthSqlDb             *sql.DB
	p2pInvestmentLiquiloansSqlDb *sql.DB
	ccSqlDb                      *sql.DB
	actorSqlDb                   *sql.DB
	recurringPaymentSqlDb        *sql.DB
	timelineSqlDb                *sql.DB
	paymentInstrumentSqlDb       *sql.DB
	frmSqlDb                     *sql.DB
)

var (
	feSignupClient            feSignupPb.SignupClient
	feSavingsClient           feSavingsPb.SavingsClient
	feConsentClient           feConsentPb.ConsentClient
	feWealthClient            feWoPb.WealthOnboardingClient
	beWealthClient            beWoPb.WealthOnboardingClient
	feUserClient              feUserPb.UserClient
	feTimelineClient          feTimelinePb.TimelineServiceClient
	feDepositClient           feDepositPb.DepositClient
	feTxnClient               feTransactionPb.TransactionClient
	feSimulationClient        fePayPb.SimulationClient
	feCardClient              feCardPb.CardClient
	cardProvClient            cardProvPb.CardProvisioningClient
	feUpiClient               feUpiPb.UPIClient
	feRewardsClient           feRewardsPb.RewardsClient
	waitlistClient            waitlistPb.WaitlistClient
	authClient                authPb.AuthClient
	accrualClient             accrualPb.AccrualClient
	offerCatalogClient        casperPb.OfferCatalogServiceClient
	offerInventoryClient      casperPb.OfferInventoryServiceClient
	exchangerOfferSvcClient   exchangerPb.ExchangerOfferServiceClient
	rewardOffersClient        rewardOffersPb.RewardOffersClient
	projectionsClient         projectorPb.ProjectorServiceClient
	rewardGeneratorClient     rewardsPb.RewardsGeneratorClient
	feConnectedAccountClient  feCaPb.ConnectedAccountClient
	beConnectedAccountClient  beCaPb.ConnectedAccountClient
	feScreeningClient         screening.ScreeningClient
	userClient                userPb.UsersClient
	feRecurringPaymentClient  feRecurringPaymentPb.RecurringPaymentServiceClient
	OrderManagerClient        orderPb.OrderManagerClient
	schedulerClient           schedulerPb.SchedulerClient
	PaymentHandlerClient      paymentPb.PaymentHandlerClient
	fileGenerationAttemptDao  dao.FileGenerationAttemptDao
	fileGeneratorClient       filegenerator.FileGeneratorClient
	feUpiOnboardingClient     feUpiOnboardingPb.OnboardingClient
	feSalaryProgramClient     feSalaryPgPb.SalaryProgramClient
	beSalaryProgramClient     beSalaryPb.SalaryProgramClient
	beRecurringPaymentClient  beRPPb.RecurringPaymentServiceClient
	feTieringClient           feTieringPb.TieringClient
	beTieringClient           beTieringPb.TieringClient
	feFireflyClient           feFireflyPb.FireflyClient
	authOrchClient            authOrchPb.OrchestratorClient
	temporalWorkflowSvcClient workflowSvcPb.WorkflowServiceClient
	cmsServiceClient          cmsPb.CmsServiceClient
	beBankCustClient          bankcust.BankCustomerServiceClient
	bePIClient                accountPIPb.AccountPIRelationClient
	vendorNotifClient         vnRpFederalPb.RecurringPaymentClient
	feP2pInvClient            feP2pInvPb.P2PInvestmentClient
	p2pInvClient              p2pInvPb.P2PInvestmentClient
	fePalClient               fePalPb.PreApprovedLoanClient
	bePalClient               bePalPb.PreApprovedLoanClient
	feCreditReportClient      creditreportpb.CreditReportClient
	cmClient                  cmPb.CaseManagementClient
	beSavingsClient           beSavingsPb.SavingsClient
	operationalStatusClient   operationalStatusPb.OperationalStatusServiceClient
)

var (
	MaxRetryCount                     = 10
	RetryInterval                     = 3 * time.Second
	workerToWorkflowReplayerConfigMap map[epifitemporal.Worker]*epifitemporalTest.WorkflowReplayerConfig
)

var (
	feConn                 *grpc.ClientConn
	authConn               *grpc.ClientConn
	accrualConn            *grpc.ClientConn
	casperConn             *grpc.ClientConn
	rewardsConn            *grpc.ClientConn
	cmsConn                *grpc.ClientConn
	connectedAccountConn   *grpc.ClientConn
	userConn               *grpc.ClientConn
	investmentConn         *grpc.ClientConn
	simulatorConn          *grpc.ClientConn
	wealthOnbConn          *grpc.ClientConn
	salaryProgramConn      *grpc.ClientConn
	beRecurringPaymentConn *grpc.ClientConn
	tieringConn            *grpc.ClientConn
	temporalFEConn         *grpc.ClientConn
	bankCustBeConn         *grpc.ClientConn
	vendorNotifConn        *grpc.ClientConn
	piConn                 *grpc.ClientConn
	cardConn               *grpc.ClientConn
	palServiceConn         *grpc.ClientConn
	savingsConn            *grpc.ClientConn
)

func initServerClients() func() {
	feConn = epifigrpc.NewConnByService(cfg.FRONTEND_SERVICE)
	authConn = epifigrpc.NewConnByService(cfg.AUTH_SERVICE)
	accrualConn = epifigrpc.NewConnByService(cfg.ACCRUAL_SERVICE)
	casperConn = epifigrpc.NewConnByService(cfg.CASPER_SERVICE)
	rewardsConn = epifigrpc.NewConnByService(cfg.REWARD_SERVICE)
	cmsConn = epifigrpc.NewConnByService(cfg.CMS_SERVICE)
	connectedAccountConn = epifigrpc.NewConnByService(cfg.CONNECTED_ACC_SERVICE)
	userConn = epifigrpc.NewConnByService(cfg.USER_SERVICE)
	investmentConn = epifigrpc.NewConnByService(cfg.INVESTMENT_SERVICE)
	simulatorConn = epifigrpc.NewConnByService(cfg.SIMULATOR_GRPC_SERVICE)
	wealthOnbConn = epifigrpc.NewConnByService(cfg.WEALTH_ONBOARDING_SERVICE)
	salaryProgramConn = epifigrpc.NewConnByService(cfg.SALARY_PROGRAM_SERVICE)
	beRecurringPaymentConn = epifigrpc.NewConnByService(cfg.RECURRING_PAYMENT_SERVICE)
	tieringConn = epifigrpc.NewConnByService(cfg.TIERING_SERVICE)
	temporalFEConn = epifigrpc.NewServerConn("", cfg.TEMPORAL_SERVER)
	bankCustBeConn = epifigrpc.NewConnByService(cfg.BANK_CUSTOMER_SERVICE)
	vendorNotifConn = epifigrpc.NewConnByService(cfg.VENDOR_NOTIFI_SERVICE)
	piConn = epifigrpc.NewConnByService(cfg.PAYMENT_INSTRUMENT_SERVICE)
	cardConn = epifigrpc.NewConnByService(cfg.CARD_SERVICE)
	palServiceConn = epifigrpc.NewConnByService(cfg.PRE_APPROVED_LOAN_SERVICE)
	accountsConn := epifigrpc.NewConnByServiceSmokeTest(cfg.ACCOUNTS_SERVICE)
	savingsConn = epifigrpc.NewConnByService(cfg.SAVINGS_SERVICE)
	userRiskConn := epifigrpc.NewConnByService(cfg.RISK_SERVICE)

	feSignupClient = feSignupPb.NewSignupClient(feConn)
	feSavingsClient = feSavingsPb.NewSavingsClient(feConn)
	feConsentClient = feConsentPb.NewConsentClient(feConn)
	feUserClient = feUserPb.NewUserClient(feConn)
	feTimelineClient = feTimelinePb.NewTimelineServiceClient(feConn)
	feDepositClient = feDepositPb.NewDepositClient(feConn)
	feTxnClient = feTransactionPb.NewTransactionClient(feConn)
	feSimulationClient = fePayPb.NewSimulationClient(feConn)
	feCardClient = feCardPb.NewCardClient(feConn)
	cardProvClient = cardProvPb.NewCardProvisioningClient(cardConn)
	feUpiClient = feUpiPb.NewUPIClient(feConn)
	feRewardsClient = feRewardsPb.NewRewardsClient(feConn)
	feConnectedAccountClient = feCaPb.NewConnectedAccountClient(feConn)
	feScreeningClient = screening.NewScreeningClient(feConn)
	feWealthClient = feWoPb.NewWealthOnboardingClient(feConn)
	feRecurringPaymentClient = feRecurringPaymentPb.NewRecurringPaymentServiceClient(feConn)
	feUpiOnboardingClient = feUpiOnboardingPb.NewOnboardingClient(feConn)
	feSalaryProgramClient = feSalaryPgPb.NewSalaryProgramClient(feConn)
	feTieringClient = feTieringPb.NewTieringClient(feConn)
	feP2pInvClient = feP2pInvPb.NewP2PInvestmentClient(feConn)
	fePalClient = fePalPb.NewPreApprovedLoanClient(feConn)
	feCreditReportClient = creditreportpb.NewCreditReportClient(feConn)

	authClient = authPb.NewAuthClient(authConn)
	accrualClient = accrualPb.NewAccrualClient(accrualConn)
	offerCatalogClient = casperPb.NewOfferCatalogServiceClient(casperConn)
	offerInventoryClient = casperPb.NewOfferInventoryServiceClient(casperConn)
	exchangerOfferSvcClient = exchangerPb.NewExchangerOfferServiceClient(casperConn)
	rewardOffersClient = rewardOffersPb.NewRewardOffersClient(rewardsConn)
	rewardGeneratorClient = rewardsPb.NewRewardsGeneratorClient(rewardsConn)
	projectionsClient = projectorPb.NewProjectorServiceClient(rewardsConn)
	beConnectedAccountClient = beCaPb.NewConnectedAccountClient(connectedAccountConn)
	userClient = userPb.NewUsersClient(userConn)
	beSalaryProgramClient = beSalaryPb.NewSalaryProgramClient(salaryProgramConn)
	beTieringClient = beTieringPb.NewTieringClient(tieringConn)
	bePalClient = bePalPb.NewPreApprovedLoanClient(palServiceConn)

	OrderManagerClient = orderPb.NewOrderManagerClient(investmentConn)
	schedulerClient = schedulerPb.NewSchedulerClient(investmentConn)
	PaymentHandlerClient = paymentPb.NewPaymentHandlerClient(investmentConn)
	fileGenerationAttemptDao = dao.NewFileGenerationAttemptCrdb(epifiWealthDbV2)
	fileGeneratorClient = filegenerator.NewFileGeneratorClient(investmentConn)
	beWealthClient = beWoPb.NewWealthOnboardingClient(wealthOnbConn)
	beRecurringPaymentClient = beRPPb.NewRecurringPaymentServiceClient(beRecurringPaymentConn)
	p2pInvClient = p2pInvPb.NewP2PInvestmentClient(investmentConn)

	feFireflyClient = feFireflyPb.NewFireflyClient(feConn)
	authOrchClient = authOrchPb.NewOrchestratorClient(feConn)

	temporalWorkflowSvcClient = workflowSvcPb.NewWorkflowServiceClient(temporalFEConn)

	cmsServiceClient = cmsPb.NewCmsServiceClient(cmsConn)
	beBankCustClient = bankcust.NewBankCustomerServiceClient(bankCustBeConn)

	vendorNotifClient = vnRpFederalPb.NewRecurringPaymentClient(vendorNotifConn)
	bePIClient = accountPiPb.NewAccountPIRelationClient(piConn)

	cmClient = cmPb.NewCaseManagementClient(userRiskConn)
	operationalStatusClient = operationalStatusPb.NewOperationalStatusServiceClient(accountsConn)
	beSavingsClient = beSavingsPb.NewSavingsClient(savingsConn)

	return func() {
		epifigrpc.CloseConn(feConn)
		epifigrpc.CloseConn(authConn)
		epifigrpc.CloseConn(accrualConn)
		epifigrpc.CloseConn(casperConn)
		epifigrpc.CloseConn(rewardsConn)
		epifigrpc.CloseConn(cmsConn)
		epifigrpc.CloseConn(connectedAccountConn)
		epifigrpc.CloseConn(userConn)
		epifigrpc.CloseConn(investmentConn)
		epifigrpc.CloseConn(salaryProgramConn)
		epifigrpc.CloseConn(simulatorConn)
		epifigrpc.CloseConn(beRecurringPaymentConn)
		epifigrpc.CloseConn(tieringConn)
		epifigrpc.CloseConn(temporalFEConn)
		epifigrpc.CloseConn(vendorNotifConn)
		epifigrpc.CloseConn(piConn)
		epifigrpc.CloseConn(cardConn)
		epifigrpc.CloseConn(userRiskConn)
		epifigrpc.CloseConn(savingsConn)
		epifigrpc.CloseConn(accountsConn)
	}
}

func initWorkflowReplayerConfigMap(smokeTestConfig *config.Config) error {
	workerToWorkflowReplayerConfigMap = make(map[epifitemporal.Worker]*epifitemporalTest.WorkflowReplayerConfig)

	for _, worker := range smokeTestConfig.WorkflowReplayerEnabledWorkers {
		replayerConfig, err := epifitemporalTest.LoadWorkflowReplayerConfig(worker)
		if err != nil {
			return err
		}

		workerToWorkflowReplayerConfigMap[worker] = replayerConfig
	}

	return nil
}

// getPooledUser creates a new user once in a lifetime of a smoke suite run
// It can further be expanded to getPooledUser concept we have in acceptance to enable concurrency in tests
func GetPooledUser(ctx context.Context, a *require.Assertions) (*app.PooledUser, func()) {
	once.Do(func() {
		a1 := require.New(&PooledUserAllocTestingT{})
		ctx1 := context.Background()

		for idx := 0; idx < conf.UserPool.PoolSize; idx++ {
			// we've explicitly disable async pooled user addition because we're getting the error
			// "TransactionRetryWithProtoRefreshError: TransactionRetryError: retry txn (RETRY_SERIALIZABLE)"
			// when we try to delete 2 users concurrently in createCleanUserParams.
			// It's probably because orders & timeline queries remove actors to and from and
			// these 2 users had transactions with each other, affecting each others deletion
			addPoolUser(ctx1, a1)
		}
	})

	logger.Info(ctx, "Waiting for a user")
	var pooledUser *app.PooledUser
	select {
	case pooledUser = <-userPool:
	case <-time.After(conf.UserPool.AllocationTimeout):
		logger.Error(ctx, fmt.Sprintf("PooledUser allocation failed even after waiting for %v seconds", conf.UserPool.AllocationTimeout))
	}
	a.NotNil(pooledUser)

	logger.Info(ctx, fmt.Sprintf("Allocated a user %s", pooledUser.Data.Phone.String()))

	return pooledUser, func() {
		userPool <- pooledUser
		logger.Info(ctx, fmt.Sprintf("Released a user %s", pooledUser.Data.Phone.String()))
	}

}

// GetTestUser chooses between pooled user and phone number user based on the --phone-number flag.
// If phone number is provided, it uses the existing user, otherwise falls back to the preloaded user pool.
func GetTestUser(ctx context.Context, a *require.Assertions) (*app.PooledUser, func()) {
	if phoneNumber != "" && phoneNumber != "null" {
		return GetExistingUser(ctx, a, phoneNumber)
	}
	return GetPooledUser(ctx, a)
}

// GetExistingUser fetches an existing user by phone number and creates auth header
func GetExistingUser(ctx context.Context, a *require.Assertions, phoneNumberStr string) (*app.PooledUser, func()) {
	// Parse 10-digit Indian phone number (e.g., "9876543210")
	if len(phoneNumberStr) != 10 {
		a.FailNow("Invalid phone number format. Expected 10-digit Indian phone number (e.g., 9876543210)")
	}

	nationalNumber, err := strconv.ParseUint(phoneNumberStr, 10, 64)
	a.NoError(err, "Failed to parse phone number")

	phone := &commontypes.PhoneNumber{
		CountryCode:    91, // India
		NationalNumber: nationalNumber,
	}

	// Get dependencies
	deps, closeFunc := app.NewOnbDeps(ctx, a, dbConns)
	defer closeFunc()

	// Fetch actor and user details
	actor, user := app.GetUserDetails(ctx, deps, phone)

	// Fetch user device properties
	userDeviceProperties := app.GetUserDeviceProperties(ctx, deps, actor.GetId())

	// Fetch user device auth
	userDeviceAuth := app.GetUserDeviceAuth(ctx, deps, actor.GetId())

	// Fetch user sim id
	simSubId, err := strconv.ParseInt(userDeviceAuth.GetSimId(), 10, 32)
	a.NoError(err, "Failed to parse sim id")

	// Fetch latest access token
	accessToken := app.GetUserLatestAccessToken(ctx, deps, actor.GetId())

	// Build UserData structure
	userData := &app.UserData{
		Name: &commontypes.Name{
			FirstName: user.GetProfile().GetName().GetFirstName(),
			LastName:  user.GetProfile().GetName().GetLastName(),
		},
		Phone: phone,
		Device: &commontypes.Device{
			Manufacturer: userDeviceProperties.GetPropValue(types.DeviceProperty_DEVICE_PROP_DEVICE_MODEL_INFO).GetDeviceModelInfo().GetManufacturer(),
			Model:        userDeviceProperties.GetPropValue(types.DeviceProperty_DEVICE_PROP_DEVICE_MODEL_INFO).GetDeviceModelInfo().GetModel(),
			HwVersion:    userDeviceProperties.GetPropValue(types.DeviceProperty_DEVICE_PROP_DEVICE_MODEL_INFO).GetDeviceModelInfo().GetHwVersion(),
			SwVersion:    userDeviceProperties.GetPropValue(types.DeviceProperty_DEVICE_PROP_DEVICE_MODEL_INFO).GetDeviceModelInfo().GetSwVersion(),
			OsApiVersion: userDeviceProperties.GetPropValue(types.DeviceProperty_DEVICE_PROP_DEVICE_MODEL_INFO).GetDeviceModelInfo().GetOsApiVersion(),
			DeviceId:     userDeviceProperties.GetPropValue(types.DeviceProperty_DEVICE_PROP_DEVICE_ID).GetDeviceId(),
			LatLng:       app.RandIndiaLatLng(),
			AppVersion:   userDeviceProperties.GetPropValue(types.DeviceProperty_DEVICE_PROP_APP_VERSION_INFO).GetAppVersionInfo().GetAppVersionCode(),
			Platform:     userDeviceProperties.GetPropValue(types.DeviceProperty_DEVICE_PROP_DEVICE_MODEL_INFO).GetDeviceModelInfo().GetPlatform(),
		},
		Pan:            user.GetProfile().GetPAN(),
		Video:          []byte{1, 2, 3}, // Default test video
		Email:          user.GetProfile().GetEmail(),
		OAuthIDToken:   string(app.GetClaims(user.GetProfile().GetEmail())),
		SafetyNetToken: "ValidToken",
		SimSubIds:      []int32{int32(simSubId)},
		IsRealUser:     true,
	}

	// Generate auth header for existing user
	authHeader := app.GetRequestHeaderWithExistingAccessToken(ctx, deps, userData, accessToken)

	return &app.PooledUser{
			Data:          userData,
			RequestHeader: authHeader,
		}, func() {
			// No-op for existing users since they don't need to be released back to pool
		}
}

func addPoolUser(ctx context.Context, a *require.Assertions) {
	userData := createCleanUserParams(app.OverrideUserParams{
		Pan:       app.EKYCPAN,
		FirstName: "DedupeCustomer",
	})
	logger.Info(ctx, "adding user to the pool", zap.Any("phone_number", userData.Phone))

	livenessVideoFilePath := filepath.Join(currentDirPath, conf.Application.RelativeLivenessVideoFilePath)
	v, err := ioutil.ReadFile(livenessVideoFilePath)
	a.NoError(err, "Failed to read video file")
	userData.Video = v
	deps, f := app.NewOnbDeps(ctx, require.New(&PooledUserAllocTestingT{}), dbConns)
	defer f()
	authHeader := app.FullKYCDedupeUser(ctx, deps, userData)
	logger.Info(ctx, "added user to the pool", zap.Any("phone_number", userData.Phone))
	app.SkipOnboardingStage(ctx, authHeader, feSignupClient, a, feSignupPb.SkipOnboardingStageRequest_ADD_MONEY)
	app.NextAction(ctx, a, feSignupClient, authHeader)
	userPool <- &app.PooledUser{
		Data:          userData,
		RequestHeader: authHeader,
	}
}

func InitTest() func() {
	// Init config
	var err error
	conf, err = config.Load(config.Integration)
	if err != nil {
		logger.Fatal(fmt.Sprintf("failed to load config: %v", err))
	}

	// Init redis client
	initRedisClient()

	// Init db connection
	initDBConn()

	var closureFunc = func() {}
	if cfg.IsTestTenantEnabled() {
		logger.Fatal("test tenant setup is no longer supported")
	}

	var closeClientsConn func()

	// Init server clients
	closeClientsConn = initServerClients()
	if !conf.Flags.UseLocalServer {
		err = initWorkflowReplayerConfigMap(conf)
		if err != nil {
			logger.Fatal("failed to load workflow replayer config map", zap.Error(err))
		}
	}

	dbConns = map[string]*gormv2.DB{
		config.EpifiDb:            epifiDbV2,
		config.SimulatorDb:        simulatorDb,
		config.CreditCardPgDb:     creditCardDb,
		config.AuthDb:             authPgdb,
		config.KycDb:              kycDb,
		config.PayDb:              payDb,
		config.TieringDb:          tieringDb,
		config.CommsDb:            commsDb,
		config.RecurringPaymentDb: recurringPaymentDb,
		config.FrmCrdb:            frmDb,
	}

	userPool = make(chan *app.PooledUser, conf.UserPool.PoolSize)

	if conf.Flags.UseLocalServer {
		conf.Flags.SkipNonBankingDBInit = true
		conf.ServerInfo = local.RequiredServers
		ts, td := local.SetupWithConfig(conf)
		teardown = td
		c = ts.Cluster
		dbConns = ts.DbConns.DbConns
	}

	return func() {
		_ = logger.Log.Sync()
		_ = simSqlDbV2.Close()
		_ = epifiSqlDb.Close()
		_ = epifiWealthSqlDb.Close()
		_ = p2pInvestmentLiquiloansSqlDb.Close()
		_ = ccSqlDb.Close()
		_ = actorSqlDb.Close()
		_ = recurringPaymentSqlDb.Close()
		_ = timelineSqlDb.Close()
		_ = paymentInstrumentSqlDb.Close()
		_ = frmSqlDb.Close()
		closureFunc()
		closeClientsConn()
		if !conf.Flags.UseLocalServer {
			loansDbResourcesTeardown()
		}
	}
}

func initDBConn() {
	// Init db connection
	dbMap := conf.Databases
	var err error
	simulatorDb, err = storagev2.NewCRDBWithConfig(dbMap[config.SimulatorDb], false)
	if err != nil {
		logger.Fatal(fmt.Sprintf("failed to connect to db: %v", err))
	}
	simSqlDbV2, _ = simulatorDb.DB()

	epifiDbV2, err = storagev2.NewCRDBWithConfig(dbMap[config.EpifiDb], false)
	if err != nil {
		logger.Fatal(fmt.Sprintf("failed to connect to db: %v", err))
	}
	epifiSqlDb, _ = epifiDbV2.DB()

	actorPgdb, err = storagev2.NewGormDB(dbMap[config.ActorDb])
	if err != nil {
		logger.Fatal(fmt.Sprintf("failed to connect to db: %v", err))
	}
	actorSqlDb, _ = actorPgdb.DB()

	authPgdb, err = storagev2.NewGormDB(dbMap[config.AuthDb])
	if err != nil {
		logger.Fatal(fmt.Sprintf("failed to connect to db: %v", err))
	}

	recurringPaymentDb, err = storagev2.NewGormDB(dbMap[config.RecurringPaymentDb])
	if err != nil {
		logger.Fatal(fmt.Sprintf("failed to connect to db: %v", err))
	}
	recurringPaymentSqlDb, _ = recurringPaymentDb.DB()

	timelineDb, err = storagev2.NewGormDB(dbMap[config.TimelineDb])
	if err != nil {
		logger.Fatal(fmt.Sprintf("failed to connect to timeline db,err: %v", err))
	}
	timelineSqlDb, _ = timelineDb.DB()

	paymentInstrumentDb, err = storagev2.NewGormDB(dbMap[config.PaymentInstrumentDb])
	if err != nil {
		logger.Fatal(fmt.Sprintf("failed to connect to paymentInstrumentDb db,err: %v", err))
	}
	paymentInstrumentSqlDb, _ = paymentInstrumentDb.DB()

	epifiWealthDbV2, err = storagev2.NewCRDBWithConfig(dbMap[config.EpifiWealthDb], false)
	if err != nil {
		logger.Fatal(fmt.Sprintf("failed to connect to db: %v", err))
	}
	epifiWealthSqlDb, _ = epifiWealthDbV2.DB()

	p2pInvestmentLiquiloansDb, err = storagev2.NewCRDBWithConfig(dbMap[config.P2PInvestmentLiquiloansDb], false)
	if err != nil {
		logger.Fatal(fmt.Sprintf("failed to connect to db: %v", err))
	}
	p2pInvestmentLiquiloansSqlDb, _ = p2pInvestmentLiquiloansDb.DB()

	creditCardDb, err = storagev2.NewPostgresDBWithConfig(dbMap[config.CreditCardPgDb], false)
	if err != nil {
		logger.Fatal(fmt.Sprintf("failed to connect to credit card db: %v", err))
	}
	ccSqlDb, _ = creditCardDb.DB()

	connectedAccountPgdb, err = storagev2.NewPostgresDBWithConfig(dbMap[config.ConnectedAccountDb], false)
	if err != nil {
		logger.Fatal(fmt.Sprintf("failed to connect to connected account DB: %v", err))
	}

	kycDb, err = storagev2.NewPostgresDBWithConfig(dbMap[config.KycDb], false)
	if err != nil {
		logger.Fatal(fmt.Sprintf("failed to connect to kyc db: %v", err))
	}

	payDb, err = storagev2.NewPostgresDBWithConfig(dbMap[config.PayDb], false)
	if err != nil {
		logger.Fatal(fmt.Sprintf("failed to connect to pay db: %v", err))
	}

	tieringDb, err = storagev2.NewPostgresDBWithConfig(dbMap[config.TieringDb], false)
	if err != nil {
		logger.Fatal(fmt.Sprintf("failed to connect to tiering db: %v", err))
	}

	commsDb, err = storagev2.NewPostgresDBWithConfig(dbMap[config.CommsDb], false)
	if err != nil {
		logger.Fatal(fmt.Sprintf("failed to connect to comms db: %v", err))
	}

	frmDb, err = storagev2.NewGormDB(dbMap[config.FrmCrdb])
	if err != nil {
		logger.Fatal(fmt.Sprintf("failed to connect to frm db %v", err))
	}
	frmSqlDb, _ = frmDb.DB()

	if !conf.Flags.UseLocalServer {
		loansDbResourceProvider, _, loansDbResourcesTeardown, err = storagev2.NewDBResourceProvider(conf.LoansDbConfigMap.GetOwnershipToDbConfigMap(), false)
		if err != nil {
			logger.Panic("error initializing loans db", zap.Error(err))
		}
	}
}

func initRedisClient() {
	redisMap := conf.RedisCluster
	userRedisClient := storage.NewRedisClientFromConfig(redisMap[config.UserRedis], false)
	userRedisCacheStorage = cache.NewRedisCacheStorage(userRedisClient)

	actorRedisClient := storage.NewRedisClientFromConfig(redisMap[config.ActorRedis], false)
	actorRedisCacheStorage = cache.NewRedisCacheStorage(actorRedisClient)

	piRedisClient := storage.NewRedisClientFromConfig(redisMap[config.PiRedis], false)
	piRedisCacheStorage = cache.NewRedisCacheStorage(piRedisClient)

	savingsLedgerReconRedisClient := storage.NewRedisClientFromConfig(redisMap[config.SavingsLedgerReconRedis], false)
	savingsLedgerReconCacheStorage = cache.NewRedisCacheStorage(savingsLedgerReconRedisClient)

	timelineRedisClient := storage.NewRedisClientFromConfig(redisMap[config.TimelineRedis], false)
	timelineRedisCacheStorage = cache.NewRedisCacheStorage(timelineRedisClient)

	savingsRedisClient := storage.NewRedisClientFromConfig(redisMap[config.SavingsRedis], false)
	savingsRedisCacheStorage = cache.NewRedisCacheStorage(savingsRedisClient)

	devRegRedisClient := storage.NewRedisClientFromConfig(redisMap[config.DevRegRedis], false)
	devRegRedisCacheStorage = cache.NewRedisCacheStorage(devRegRedisClient)

	lendingRedisClient := storage.NewRedisClientFromConfig(redisMap[config.LendingRedis], false)
	lendingRedisCacheStorage = cache.NewRedisCacheStorage(lendingRedisClient)

	aaBaseUrl = conf.Application.AaBaseUrl
}

var teardown func()

var (
	buildNumber        string
	isSlackAlertReq    bool
	includeServersArgs string
	excludeServersArgs string
	includeServers     []string
	excludeServers     []string
	phoneNumber        string
)

func initCustomFlags() {
	flag.StringVar(&TestPattern, "test-pattern", ".*", "Name/Regex of testcase")
	flag.StringVar(&SubtestPattern, "subtest-pattern", ".*", "Name/Regex of subtest within testcase")
	flag.StringVar(&BuArgs, "bu", "", "List of BuArgs")
	flag.StringVar(&ScopeArgs, "scope", "", "List of scopes")
	flag.StringVar(&buildNumber, "build-no", "", "Jenkins job build number")
	flag.BoolVar(&isSlackAlertReq, "slack-alert", false, "Send slack alert for failures")
	flag.StringVar(&includeServersArgs, "include-servers", "", "List of included servers for embedded server")
	flag.StringVar(&excludeServersArgs, "exclude-servers", "", "List of excluded servers for embedded server")
	flag.StringVar(&phoneNumber, "phone-number", "", "Use existing phone number instead of preloaded users")
	flag.Parse()

	if BuArgs != "" {
		BUs = strings.Split(BuArgs, ",")
	}
	if ScopeArgs != "" {
		Scopes = strings.Split(ScopeArgs, ",")
	}
	if includeServersArgs != "" {
		includeServers = strings.Split(includeServersArgs, ",")
	}
	excludeServers = strings.Split(excludeServersArgs, ",")
}

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	initCustomFlags()

	logger.Init("test")
	rand.Seed(time.Now().UTC().UnixNano())

	teardown = InitTest()
	logger.InfoNoCtx("Ended Setup")

	exitCode := ExecuteSmokeTest(m, teardown)

	handleSlackAlert()
	os.Exit(exitCode)
}

func Recover(t *testing.T) {
	if r := recover(); r != nil {
		logger.ErrorNoCtx("recovered from panic", zap.Any("recoverMsg", r))
		t.Error(r)
	}
}

// ExecuteSmokeTest executes smoke tests and perform teardown gracefully
// irrespective of test panic.
func ExecuteSmokeTest(m *testing.M, teardown func()) int {
	defer func() {

		close(userPool)

		for pooledUser := range userPool {
			// delete data for the pooled user if it was created
			if pooledUser != nil && pooledUser.Data != nil && pooledUser.Data.Phone != nil {
				if err := userdata.DeleteWealthData(pooledUser.Data.Phone, epifiDbV2, simulatorDb, epifiWealthDbV2, nil, actorPgdb); err != nil {
					logger.ErrorNoCtx("error deleting wealth data for pooled user", zap.Error(err))
					return
				}
				userdata.DeleteUserData(epifiDbV2, actorPgdb, timelineDb, paymentInstrumentDb, simulatorDb, pooledUser.Data.Phone, userRedisCacheStorage, actorRedisCacheStorage, piRedisCacheStorage, savingsLedgerReconCacheStorage, timelineRedisCacheStorage, savingsRedisCacheStorage, devRegRedisCacheStorage)
			}
		}

		teardown()
	}()

	return m.Run()
}

func TestSignup(t *testing.T) {
	defer Recover(t)
	a := require.New(t)
	ctx := context.Background()

	_, releaseUserFn := GetPooledUser(ctx, a)
	releaseUserFn()

	logger.InfoNoCtx("done")
}

func TestServerHealthCheck(t *testing.T) {
}

func TestExecutor(t *testing.T) {
	testCases := AllRegisteredTestCases()
	RunTestCases(t, testCases)
}

func handleSlackAlert() {
	if isSlackAlertReq {
		env, err := cfg.GetEnvironment()
		if err != nil {
			logger.Fatal(fmt.Sprintf("failed to get environment: %v", err))
		}

		// sending smoke test failure alert in a build-release-qa channel for smoke test failure
		isSmokeScope := (len(Scopes) == 0) || slices.Contains(Scopes, string(Smoke))
		var slackClient *slack.Client
		if cfg.IsQaEnv(env) {
			slackClient = slack.New(conf.Secrets.Ids[config.SlackToken], slack.OptionDebug(true))
		}
		if cfg.IsQaEnv(env) && isSmokeScope {
			logger.InfoNoCtx("send smoke fail slack alert")
			sendSmokeSlackAlert(context.Background(), conf, slackClient)
		}
	}
}

func sendSmokeSlackAlert(ctx context.Context, conf *config.Config, slackClient *slack.Client) {
	if !conf.SlackConfig.SendSlackNotification {
		return
	}

	ownerCfg, err := ownerconfig.Load()
	if err != nil {
		logger.ErrorNoCtx("Error in loading owners config", zap.Error(err))
	}

	testMap := AllRegisteredTestCasesMap()
	failedTests, err := LogProcess(testMap)
	if err != nil {
		fmt.Println("Failed to process log")
	}

	buFailedTests := getBuFailedTestMap(failedTests)

	if len(failedTests) > 0 {
		message := fmt.Sprintf("Smoke Tests have failed :rocket-down:!")
		var preText strings.Builder
		slackAttachmentFileds := make([]slack.AttachmentField, 0)

		for bu, tc := range buFailedTests {
			buDetails, ok := ownerCfg.BusinessUnits[bu]
			if !ok {
				fmt.Println("Bu not found")
			}
			preText.WriteString(fmt.Sprintf("<!subteam^%s|%s> ", buDetails.OnCallGroupId, buDetails.OncallUserHandle) + " ")
			var attachField slack.AttachmentField
			attachField.Title = "BU : " + string(bu)
			attachField.Value = "Failed Tests : " + strings.Join(tc, ", ")

			slackAttachmentFileds = append(slackAttachmentFileds, attachField)

		}
		smokeJenkinsJobUrl := "https://jenkins-deploy.pointz.in/job/Scripts/job/Backend/job/smoke-test-v2/"
		if buildNumber != "" {
			smokeJenkinsJobUrl += buildNumber + "/console"
		}
		preText.WriteString(" URL: " + smokeJenkinsJobUrl)
		slackAttachment := slack.Attachment{
			Text:    message,
			Pretext: preText.String(),
			Fields:  slackAttachmentFileds,
		}

		respChannel, respTimestamp, err := slackClient.PostMessage(conf.SlackConfig.BuildReleaseQaSlackChannelID, slack.MsgOptionAttachments(slackAttachment))
		if err != nil {
			logger.Error(ctx, "error posting message on slack", zap.Error(err))
			return
		}
		logger.Info(ctx, fmt.Sprintf("Alert was sent to %s at %s", respChannel, respTimestamp))
	}

}

func getBuFailedTestMap(failedTests []*FailedTestsDetails) map[owners.BusinessUnit][]string {
	buFailedTests := make(map[owners.BusinessUnit][]string)
	for _, ft := range failedTests {
		bu := ft.TestCase.TestProperty.BU
		buFailedTests[bu] = append(buFailedTests[bu], ft.TestName)
	}

	return buFailedTests
}

// PooledUserAllocTestingT implements TestingT interface for require.Assertions injection
type PooledUserAllocTestingT struct{}

func (*PooledUserAllocTestingT) Errorf(format string, args ...interface{}) {
	fmt.Println("pooled user allocation failed assertion:", fmt.Sprintf(format, args...))
}

func (*PooledUserAllocTestingT) FailNow() {
	panic("pooled user allocation failed")
}
