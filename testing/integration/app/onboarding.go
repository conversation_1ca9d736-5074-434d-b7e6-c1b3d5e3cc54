// nolint:funlen,depguard,ineffassign,testifylint
package app

import (
	"context"
	cryprand "crypto/rand"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"math/big"
	"math/rand"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"testing"
	"time"

	"github.com/golang/protobuf/ptypes/timestamp"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	latLngPb "google.golang.org/genproto/googleapis/type/latlng"
	"google.golang.org/genproto/googleapis/type/postaladdress"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/anypb"
	"google.golang.org/protobuf/types/known/structpb"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	gormv2 "gorm.io/gorm"

	"github.com/epifi/be-common/pkg/async/waitgroup"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/gamma/api/card/control"
	"github.com/epifi/gamma/api/creditreportv2"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/pan"
	"github.com/epifi/gamma/api/typesv2/form"

	actorModel "github.com/epifi/gamma/actor/dao/model"
	actorPb "github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	livenessPb "github.com/epifi/gamma/api/auth/liveness"
	authOrchPb "github.com/epifi/gamma/api/auth/orchestrator"
	"github.com/epifi/gamma/api/bankcust"
	bankCustPb "github.com/epifi/gamma/api/bankcust"
	compliancePb "github.com/epifi/gamma/api/bankcust/compliance"
	beCaPb "github.com/epifi/gamma/api/connected_account"
	consentPb "github.com/epifi/gamma/api/consent"
	cxVkycCallPb "github.com/epifi/gamma/api/cx/data_collector/vkyccall"
	employmentPb "github.com/epifi/gamma/api/employment"
	"github.com/epifi/gamma/api/frontend/account/screening"
	"github.com/epifi/gamma/api/frontend/account/screening/uistate"
	"github.com/epifi/gamma/api/frontend/account/signup"
	alfredFePb "github.com/epifi/gamma/api/frontend/alfred"
	feAuthPb "github.com/epifi/gamma/api/frontend/auth"
	feLivPb "github.com/epifi/gamma/api/frontend/auth/liveness"
	feAuthOrchPb "github.com/epifi/gamma/api/frontend/auth/orchestrator"
	feBcPb "github.com/epifi/gamma/api/frontend/bank_customer"
	"github.com/epifi/gamma/api/frontend/card"
	feCaPb "github.com/epifi/gamma/api/frontend/connected_account"
	"github.com/epifi/gamma/api/frontend/consent"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/docs"
	"github.com/epifi/gamma/api/frontend/fcm"
	"github.com/epifi/gamma/api/frontend/genie"
	"github.com/epifi/gamma/api/frontend/header"
	fevkycpb "github.com/epifi/gamma/api/frontend/kyc/vkyc"
	feMediaPb "github.com/epifi/gamma/api/frontend/media"
	fePanPb "github.com/epifi/gamma/api/frontend/pan"
	fePayPb "github.com/epifi/gamma/api/frontend/pay"
	feTransactionPb "github.com/epifi/gamma/api/frontend/pay/transaction"
	"github.com/epifi/gamma/api/frontend/referral"
	"github.com/epifi/gamma/api/frontend/savings"
	"github.com/epifi/gamma/api/frontend/user"
	"github.com/epifi/gamma/api/frontend/user/onboarding"
	fevkyccallpb "github.com/epifi/gamma/api/frontend/vkyccall"
	"github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/kyc/agent"
	kycdocspb "github.com/epifi/gamma/api/kyc/docs"
	beVkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	"github.com/epifi/gamma/api/omegle"
	panPb "github.com/epifi/gamma/api/pan"
	"github.com/epifi/gamma/api/risk"
	redlistPb "github.com/epifi/gamma/api/risk/redlist"
	beSavingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/screener"
	dobbyPb "github.com/epifi/gamma/api/simulator/dobby"
	types "github.com/epifi/gamma/api/typesv2"
	docs2 "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/docs"
	formPkg "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/form"
	vkyc2 "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/vkyc"
	userPb "github.com/epifi/gamma/api/user"
	beUserGrpPb "github.com/epifi/gamma/api/user/group"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/useractions"
	vgPbCustomer "github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	header2 "github.com/epifi/gamma/api/vendorgateway/openbanking/header"
	beVkycCallPb "github.com/epifi/gamma/api/vkyccall"
	"github.com/epifi/gamma/auth/dao"
	authModel "github.com/epifi/gamma/auth/dao/model"
	fepkg "github.com/epifi/gamma/pkg/frontend/msg"
	"github.com/epifi/gamma/pkg/vendorstore"
	ekycDao "github.com/epifi/gamma/simulator/dao"
	"github.com/epifi/gamma/simulator/dao/model"
	"github.com/epifi/gamma/testing/integration/config"
	integrationType "github.com/epifi/gamma/testing/integration/pkg/types"
	userModel "github.com/epifi/gamma/user/dao/model"
)

const (
	poolSize            int = 2
	minKycUserPoolSize  int = 5
	fullKycUserPoolSize int = 5

	// poll config
	PollMaxRetries    = 40
	PollMaxRetriesLow = 10
	PollInterval      = 3 * time.Second
	// AccountCreationPollInterval has larger poll time to avoid smoke test failures
	AccountCreationPollInterval = 4 * time.Second

	LivenessRetryExpiry = 32 * time.Second
	EKYCExpiry          = 100 * time.Second
	VKYCEKYCExpiry      = 55 * time.Second
	BKYCExpiry          = 100 * time.Second

	/*
		App config
	*/
	KYCNameDOBMaxRetries     = 3
	EKYCPAN                  = "**********" // PAN number to be used for EKYC flow
	CtsProfileMatchFailToken = "CtsProfileMatchFailToken"

	// Coordinates for Ghaziabad, India
	LatGzbIndia = 21.7679
	LngGzbIndia = 78.8718

	UAECountryCode = 971
)

var (

	// default correct dob for ckyc
	userDob = &date.Date{Day: 18, Month: 1, Year: 1998}

	preOnboardedPhoneNumbers = []uint64{
		**********,
		**********,
		**********,
		**********,
		**********,
		**********,
		**********,
		**********,
		**********,
		**********,
	}

	names = []*commontypes.Name{
		{
			FirstName: "Byomkesh",
			LastName:  "Bakshi",
		},
		{
			FirstName: "Sherlock",
			LastName:  "Holmes",
		},
		{
			FirstName: "Naruto",
			LastName:  "Uzumaki",
		},
		{
			FirstName: "Miles",
			LastName:  "Morales",
		},
		{
			FirstName: "MonkeyD",
			LastName:  "Luffy",
		},
	}

	RandIndiaLatLng = func() *latLngPb.LatLng {
		return &latLngPb.LatLng{
			Latitude:  LatGzbIndia,
			Longitude: LngGzbIndia,
		}
	}

	// AcqInfoForSAIntent is acquisition info for savings account (banking) intent. Campaign with `_sa` suffix is used to identify it.
	// If LoginWithOAuth is called with this acquisition info, the user will be onboarded for savings account, skipping intent stage.
	AcqInfoForSAIntent = func() *signup.AcquisitionInfo {
		payload, _ := structpb.NewStruct(map[string]interface{}{
			"campaign": "random_campaign_sa",
			"af_prt":   "some partner",
		})
		return &signup.AcquisitionInfo{
			AppsflyerOnConversionDataSuccessPayload: payload,
		}
	}
)

type UserWithProperties struct {
	overrideUserParams OverrideUserParams
	userData           *UserData
	// Flag to claim Finite code during onboarding
	claimFiniteCode bool
	// this will be used if ClaimFiniteCode is set as TRUE
	finiteCode string

	// kyc level of the onboarded user
	kycLevel kyc.KYCLevel

	addFundsDuringOnboarding bool
	addFundsAmount           int64
	loginOptions             *LoginOpts
	apoUnspecified           bool
}

type OnboardUserPropertyOptions func(o *UserWithProperties)

func FiniteCodeOption(code string) func(o *UserWithProperties) {
	return func(o *UserWithProperties) {
		o.claimFiniteCode = true
		o.finiteCode = code
	}
}

func KycLevelOption(kycLevel kyc.KYCLevel) func(o *UserWithProperties) {
	return func(o *UserWithProperties) {
		o.kycLevel = kycLevel
	}
}

func AddFundsOption(funds int64) func(o *UserWithProperties) {
	return func(o *UserWithProperties) {
		o.addFundsDuringOnboarding = true
		o.addFundsAmount = funds
	}
}

func OverrideUserParamsOption(override OverrideUserParams) func(o *UserWithProperties) {
	return func(o *UserWithProperties) {
		o.overrideUserParams = override
		o.userData = CreateUserParams(override)
	}
}

func NewUserForOnboardingWithProperties(opts ...OnboardUserPropertyOptions) *UserWithProperties {
	var newUser = &UserWithProperties{
		loginOptions: &LoginOpts{
			AcquisitionInfo: AcqInfoForSAIntent(),
		},
	}
	for _, option := range opts {
		option(newUser)
	}
	return newUser
}

func APOUnspecifiedOption(markUnspecified bool) func(o *UserWithProperties) {
	return func(o *UserWithProperties) {
		o.apoUnspecified = markUnspecified
	}
}

func (o *UserWithProperties) OnboardUserForSavingsAccount(ctx context.Context, dep *OnbDep) {
	authHeader, _ := UserWithAccessToken(ctx, dep, o.userData, o.loginOptions)
	nextAction := PollNextAction(ctx, dep, authHeader)
	if o.claimFiniteCode {
		ClaimFiniteCode(ctx, dep.Assert, dep.InAppReferralClient, authHeader, o.finiteCode)
	} else {
		SkipOnboardingStage(ctx, authHeader, dep.SignupClient, dep.Assert, signup.SkipOnboardingStageRequest_REFERRAL_FINITE_CODE)
	}
	nextAction = PollNextAction(ctx, dep, authHeader)
	// Record user consent
	// Check that next action is TnC
	ScreenEquals(dep.Assert, deeplink.Screen_CONSENT, nextAction.GetScreen())
	RecordFiConsent(ctx, dep.Assert, dep.ConsentClient, authHeader, ConsentList)
	nextAction = NextAction(ctx, dep.Assert, dep.SignupClient, authHeader)
	resp, err := dep.SignupClient.SetOnboardingIntent(ctx, &signup.SetOnboardingIntentRequest{
		Req:              authHeader,
		IntentIdentifier: onbPb.OnboardingIntent_ONBOARDING_INTENT_FEDERAL_SAVINGS_ACCOUNT.String(),
	})
	dep.Assert.NoError(fepkg.FeRPCError(resp, err))
	nextAction = PollNextAction(ctx, dep, authHeader)

	// Record user consent
	// Check that next action is SA_DECLARATION
	ScreenEquals(dep.Assert, deeplink.Screen_SA_DECLARATION, nextAction.GetScreen())
	RecordConsent(ctx, dep.Assert, dep.ConsentClient, authHeader, []string{consentPb.ConsentType_CONSENT_INDIAN_RESIDENCY.String(), consentPb.ConsentType_CONSENT_NOT_POLITICALLY_EXPOSED.String(), consentPb.ConsentType_CONSENT_TYPE_DISABILITY.String()})
	PollNextAction(ctx, dep, authHeader)

	CallAndAssertSavePanDob(ctx, dep.Assert, dep, authHeader, o.userData.Pan, o.userData.PanName, o.userData.DOB)
	nextAction = PollNextAction(ctx, dep, authHeader)

	ScreenEquals(dep.Assert, deeplink.Screen_EMPLOYMENT_DECLARATION, nextAction.GetScreen())
	DoPassScreenerWithIncomeEst(ctx, dep, authHeader, o.userData)
	nextAction = DoEmploymentStage(ctx, dep, authHeader, o.userData)
	nextAction = PollNextAction(ctx, dep, authHeader)

	ScreenEquals(dep.Assert, deeplink.Screen_PARENTS_NAME_GETTER, nextAction.GetScreen())
	CollectParentsName(ctx, dep, o.userData, authHeader)
	nextAction = PollNextAction(ctx, dep, authHeader)

	if o.kycLevel == kyc.KYCLevel_FULL_KYC {
		o.userData.Name.FirstName = "DedupeCustomer"
		AddCustomerToSimulator(ctx, dep.Assert, dep.SimDB, dep.CustomerClient, o.userData)
	}
	SkipOnboardingStage(ctx, authHeader, dep.SignupClient, dep.Assert, signup.SkipOnboardingStageRequest_BKYC)
	nextAction = PollNextAction(ctx, dep, authHeader)
	ScreenEquals(dep.Assert, deeplink.Screen_START_EKYC, nextAction.GetScreen())
	PerformEKYCWithoutOnb(ctx, dep, o.userData, authHeader)
	nextAction = PollNextAction(ctx, dep, authHeader)

	// Liveness Stage
	lo := WaitForLivenessNextAction(ctx, dep, authHeader, nextAction)
	CheckLivenessVideo(ctx, dep.Assert, dep.SignupClient, authHeader, o.userData.Video, lo.GetAttemptId())

	// Perform onboarding from the given next action
	nextAction = PollNextAction(ctx, dep, authHeader)
	nextAction = DoOnboardingFromAction(ctx, dep, authHeader, nextAction, o.userData)
	// nextAction = PollNextAction(ctx, dep, authHeader)

	// Add funds during onboarding
	ScreenEquals(dep.Assert, deeplink.Screen_ONBOARDING_ADD_MONEY, nextAction.GetScreen())

	if o.apoUnspecified {
		errSql := dep.DB.Exec(fmt.Sprintf("UPDATE savings_accounts SET sku_info = sku_info - 'accountProductOffering' WHERE phone_number = '%v'", o.userData.Phone.ToString())).Error
		dep.Assert.NoError(errSql, "error while update sku_info to unspecified")
	}

	if o.addFundsDuringOnboarding {
		AddFundForSingleUser(ctx, authHeader, o.addFundsAmount, fePayPb.OrderStatus_PAYMENT_SUCCESS, dep)
	} else {
		SkipOnboardingStage(ctx, authHeader, dep.SignupClient, dep.Assert, signup.SkipOnboardingStageRequest_ADD_MONEY)
	}
	nextAction = PollNextAction(ctx, dep, authHeader)

	if nextAction.GetScreen() == deeplink.Screen_VKYC_STATUS_SCREEN {
		SkipOnboardingStage(ctx, authHeader, dep.SignupClient, dep.Assert, signup.SkipOnboardingStageRequest_OPTIONAL_VKYC)
	}

	nextAction = PollNextAction(ctx, dep, authHeader)

	if nextAction.GetScreen() == deeplink.Screen_GENERIC_RECORD_CONSENT {
		RecordConsent(ctx, dep.Assert, dep.ConsentClient, authHeader, []string{consentPb.ConsentType_SECURE_USAGE_GUIDELINES.String()})
	}

	nextAction = PollNextAction(ctx, dep, authHeader)

	ScreenEquals(dep.Assert, deeplink.Screen_HOME, nextAction.GetScreen())
}

// Onboarding Dependencies
type OnbDep struct {
	Assert                   *require.Assertions
	DB                       *gormv2.DB
	DBConns                  map[string]*gormv2.DB
	CardControlClient        control.CardControlClient
	SignupClient             signup.SignupClient
	ConsentClient            consent.ConsentClient
	UserClient               user.UserClient
	SavingsClient            savings.SavingsClient
	CardClient               card.CardClient
	InAppReferralClient      referral.ReferralClient
	ScreeningClient          screening.ScreeningClient
	UserBEClient             userPb.UsersClient
	UserActionClient         useractions.UserActionsClient
	LivenessClient           livenessPb.LivenessClient
	AuthClient               authPb.AuthClient
	PanClient                panPb.PanClient
	FePanClient              fePanPb.PanClient
	ActorClient              actorPb.ActorClient
	BankcustClient           bankCustPb.BankCustomerServiceClient
	FeVKYCClient             fevkycpb.VkycClient
	FeGenieClient            genie.GenieClient
	BeKycAgentClient         agent.KycAgentServiceClient
	RedListClient            redlistPb.RedListClient
	OnbClient                onbPb.OnboardingClient
	BeKycClient              kyc.KycClient
	fcmClient                fcm.FCMClient
	BeUGClient               beUserGrpPb.GroupClient
	VKYCClient               beVkycPb.VKYCClient
	BeSavingsClient          beSavingsPb.SavingsClient
	CustomerClient           vgPbCustomer.CustomerClient
	FeAlfredClient           alfredFePb.AlfredClient
	FeBcClient               feBcPb.BankCustomerClient
	FeTxnClient              feTransactionPb.TransactionClient
	FeSimulationClient       fePayPb.SimulationClient
	FeAuthClient             feAuthPb.AuthClient
	SimDB                    *gormv2.DB
	FeDocsClient             docs.DocsClient
	VendorStore              vendorstore.VendorStore
	ComplianceClient         compliancePb.ComplianceClient
	ScreenerClient           screener.ScreenerClient
	UserGroupClient          beUserGrpPb.GroupClient
	feConnectedAccountClient feCaPb.ConnectedAccountClient
	beConnectedAccountClient beCaPb.ConnectedAccountClient
	FeAuthOrchClient         feAuthOrchPb.OrchestratorClient
	AuthOrchClient           authOrchPb.OrchestratorClient
	DobbyClient              dobbyPb.DobbyClient
	FeLivClient              feLivPb.LivenessClient
	FeMediaClient            feMediaPb.MediaClient
	BeOmegleClient           omegle.OmegleClient
	DocExtractionClient      kycdocspb.DocExtractionClient
	BeVkycCallClient         beVkycCallPb.VkycCallClient
	CxVkycCallClient         cxVkycCallPb.VkycCallClient
	FeVkycCallClient         fevkyccallpb.VkycCallClient
	CrClient                 creditreportv2.CreditReportManagerClient
	BeEmploymentClient       employmentPb.EmploymentClient
}

// struct for passing additional parameters to DoOnboardingFromAction
type OnbActionMetadata struct {
	ShippingAddressType types.AddressType
}

type DeferFunc func()

// UserData is a simple struct for grouping all signup related data used for a user in a test
type UserData struct {
	Name   *commontypes.Name
	Device *commontypes.Device
	Phone  *commontypes.PhoneNumber
	// bytes of the video to be used for liveness checks
	Video        []byte
	Pan          string
	OAuthIDToken string
	// Attestation token
	SafetyNetToken string
	// JWT token built from attestation token
	DeviceIntegrityToken string
	DOB                  *date.Date
	Email                string
	// employment type is optional by default it will take salaried
	EmploymentType uistate.EmploymentType
	// masked ekyc mobile number
	MaskedEKYCNumber string
	SimSubIds        []int32
	SalaryB2BUser    bool
	PanName          *commontypes.Name
	// this flag is used to indicate if the user is real or test
	IsRealUser bool
}

// PooledUser contains a preprovisioned user along with the reqH that can be used on behalf of the user in tests
// This struct is not be used directly. To get a PooledUser please use the getPooledUser function.
type PooledUser struct {
	Data          *UserData
	RequestHeader *header.RequestHeader
}

type PooledUserWithRelease struct {
	User          *PooledUser
	ReleaseUserFn func()
}

type releaseUser func()

var (
	initUserPool sync.Once

	userPool        = make(chan *PooledUser, poolSize)
	minKycUserPool  = make(chan *PooledUser, minKycUserPoolSize)
	fullKycUserPool = make(chan *PooledUser, fullKycUserPoolSize)

	// global var to generate next unique user who has CKYC
	ckycUserCount int32
	simId         int32

	ActionAfterCreateSavingsAccount = deeplink.Screen_HOME
	ActionAfterKYC                  = deeplink.Screen_CREATE_ACCOUNT
)

// nolint:funlen
func NewOnbDeps(ctx context.Context, assert *require.Assertions, dbConns map[string]*gormv2.DB) (*OnbDep, DeferFunc) {
	storageV2.InitDefaultCRDBTransactionExecutor(dbConns[config.EpifiDb])
	feConn := epifigrpc.NewConnByService(cfg.FRONTEND_SERVICE)
	userConn := epifigrpc.NewConnByService(cfg.USER_SERVICE)
	riskConn := epifigrpc.NewConnByService(cfg.RISK_SERVICE)
	authConn := epifigrpc.NewConnByService(cfg.AUTH_SERVICE)
	panConn := epifigrpc.NewConnByService(cfg.PAN_SERVICE)
	actorConn := epifigrpc.NewConnByService(cfg.ACTOR_SERVICE)
	kycConn := epifigrpc.NewConnByService(cfg.KYC_SERVICE)
	savingsConn := epifigrpc.NewConnByService(cfg.SAVINGS_SERVICE)
	vgConn := epifigrpc.NewConnByService(cfg.VENDOR_GATEWAY_SERVICE)
	connectedAccountConn := epifigrpc.NewConnByService(cfg.CONNECTED_ACC_SERVICE)
	simulatorConn := epifigrpc.NewConnByService(cfg.SIMULATOR_GRPC_SERVICE)
	bankcustConn := epifigrpc.NewConnByService(cfg.BANK_CUSTOMER_SERVICE)
	cxConn := epifigrpc.NewConnByService(cfg.CX_SERVICE)
	vkycCallConn := epifigrpc.NewConnByService(cfg.VKYC_CALL_SERVICE)
	crConn := epifigrpc.NewConnByService(cfg.CREDIT_REPORT_SERVICE)
	cardConn := epifigrpc.NewConnByService(cfg.CARD_SERVICE)
	employmentConn := epifigrpc.NewConnByService(cfg.EMPLOYMENT_SERVICE)
	livenessConn := epifigrpc.NewConnByService(cfg.LIVENESS_SERVICE)

	closeConnFunc := func() {
		epifigrpc.CloseConn(feConn)
		epifigrpc.CloseConn(userConn)
		epifigrpc.CloseConn(authConn)
		epifigrpc.CloseConn(panConn)
		epifigrpc.CloseConn(actorConn)
		epifigrpc.CloseConn(kycConn)
		epifigrpc.CloseConn(savingsConn)
		epifigrpc.CloseConn(vgConn)
		epifigrpc.CloseConn(simulatorConn)
		epifigrpc.CloseConn(bankcustConn)
		epifigrpc.CloseConn(vkycCallConn)
		epifigrpc.CloseConn(cxConn)
		epifigrpc.CloseConn(crConn)
		epifigrpc.CloseConn(cardConn)
		epifigrpc.CloseConn(employmentConn)
		epifigrpc.CloseConn(livenessConn)
	}

	feSignupClient := signup.NewSignupClient(feConn)
	feMediaClient := feMediaPb.NewMediaClient(feConn)
	feSavingsClient := savings.NewSavingsClient(feConn)
	feConsentClient := consent.NewConsentClient(feConn)
	feGenieClient := genie.NewGenieClient(feConn)
	feUserClient := user.NewUserClient(feConn)
	cardClient := card.NewCardClient(feConn)
	inAppReferralClient := referral.NewReferralClient(feConn)
	ScreeningClient := screening.NewScreeningClient(feConn)
	userClent := userPb.NewUsersClient(userConn)
	uaClient := useractions.NewUserActionsClient(bankcustConn)
	livClient := livenessPb.NewLivenessClient(livenessConn)
	authClient := authPb.NewAuthClient(authConn)
	panClient := panPb.NewPanClient(panConn)
	fePanClient := fePanPb.NewPanClient(feConn)
	actorClient := actorPb.NewActorClient(actorConn)
	bcClient := bankCustPb.NewBankCustomerServiceClient(bankcustConn)
	feVkycClient := fevkycpb.NewVkycClient(feConn)
	beKycAgentClient := agent.NewKycAgentServiceClient(kycConn)
	beVKYCClient := beVkycPb.NewVKYCClient(kycConn)
	redListClient := redlistPb.NewRedListClient(riskConn)
	onbClient := onbPb.NewOnboardingClient(userConn)
	beKycClient := kyc.NewKycClient(kycConn)
	feFcmClient := fcm.NewFCMClient(feConn)
	beUGClient := beUserGrpPb.NewGroupClient(userConn)
	beSavingsClient := beSavingsPb.NewSavingsClient(savingsConn)
	customerClient := vgPbCustomer.NewCustomerClient(vgConn)
	feAlfredClient := alfredFePb.NewAlfredClient(feConn)
	feBcClient := feBcPb.NewBankCustomerClient(feConn)
	feTxnClient := feTransactionPb.NewTransactionClient(feConn)
	feSimulationClient := fePayPb.NewSimulationClient(feConn)
	feDocClient := docs.NewDocsClient(feConn)
	feAuthClient := feAuthPb.NewAuthClient(feConn)
	complianceClient := compliancePb.NewComplianceClient(bankcustConn)
	screenerClient := screener.NewScreenerClient(bankcustConn)
	userGroupClient := beUserGrpPb.NewGroupClient(userConn)
	feConnectedAccountClient := feCaPb.NewConnectedAccountClient(feConn)
	beConnectedAccountClient := beCaPb.NewConnectedAccountClient(connectedAccountConn)
	feAuthOrchClient := feAuthOrchPb.NewOrchestratorClient(feConn)
	authOrchClient := authOrchPb.NewOrchestratorClient(authConn)
	dobbyClient := dobbyPb.NewDobbyClient(simulatorConn)
	feLivClient := feLivPb.NewLivenessClient(feConn)
	beOmegleClient := omegle.NewOmegleClient(kycConn)
	docExtractionClient := kycdocspb.NewDocExtractionClient(kycConn)
	beVkycCallClient := beVkycCallPb.NewVkycCallClient(vkycCallConn)
	cxVkycCallClient := cxVkycCallPb.NewVkycCallClient(cxConn)
	feVkycCallClient := fevkyccallpb.NewVkycCallClient(feConn)
	crClient := creditreportv2.NewCreditReportManagerClient(crConn)
	cardControlClient := control.NewCardControlClient(cardConn)
	employmentClient := employmentPb.NewEmploymentClient(employmentConn)

	deps := &OnbDep{
		Assert:                   assert,
		DBConns:                  dbConns,
		DB:                       dbConns[config.EpifiDb],
		CardControlClient:        cardControlClient,
		SignupClient:             feSignupClient,
		ConsentClient:            feConsentClient,
		UserClient:               feUserClient,
		SavingsClient:            feSavingsClient,
		CardClient:               cardClient,
		InAppReferralClient:      inAppReferralClient,
		ScreeningClient:          ScreeningClient,
		UserBEClient:             userClent,
		UserActionClient:         uaClient,
		LivenessClient:           livClient,
		AuthClient:               authClient,
		PanClient:                panClient,
		FePanClient:              fePanClient,
		ActorClient:              actorClient,
		BankcustClient:           bcClient,
		FeVKYCClient:             feVkycClient,
		FeGenieClient:            feGenieClient,
		BeKycAgentClient:         beKycAgentClient,
		RedListClient:            redListClient,
		OnbClient:                onbClient,
		BeKycClient:              beKycClient,
		fcmClient:                feFcmClient,
		BeUGClient:               beUGClient,
		VKYCClient:               beVKYCClient,
		BeSavingsClient:          beSavingsClient,
		CustomerClient:           customerClient,
		FeAlfredClient:           feAlfredClient,
		FeBcClient:               feBcClient,
		FeTxnClient:              feTxnClient,
		FeSimulationClient:       feSimulationClient,
		FeDocsClient:             feDocClient,
		FeAuthClient:             feAuthClient,
		SimDB:                    dbConns[config.SimulatorDb],
		VendorStore:              vendorstore.NewVendorStore(vendorstore.NewVendorResponseDAO(dbConns[config.EpifiDb])),
		ComplianceClient:         complianceClient,
		ScreenerClient:           screenerClient,
		UserGroupClient:          userGroupClient,
		feConnectedAccountClient: feConnectedAccountClient,
		beConnectedAccountClient: beConnectedAccountClient,
		FeAuthOrchClient:         feAuthOrchClient,
		AuthOrchClient:           authOrchClient,
		DobbyClient:              dobbyClient,
		FeLivClient:              feLivClient,
		FeMediaClient:            feMediaClient,
		BeOmegleClient:           beOmegleClient,
		DocExtractionClient:      docExtractionClient,
		BeVkycCallClient:         beVkycCallClient,
		CxVkycCallClient:         cxVkycCallClient,
		FeVkycCallClient:         feVkycCallClient,
		CrClient:                 crClient,
		BeEmploymentClient:       employmentClient,
	}
	return deps, closeConnFunc
}

// getPooledUser gets a shared user with CKYC and account provisioning completed.
// The caller is expected to release the user by deferring the function
// returned. Callers should not modify any user details and the auth header.
// Deprecated: use GetPreloadedUser instead
func GetPooledUser(a *require.Assertions, db *gormv2.DB, conf *config.Config) (*PooledUser, releaseUser) {
	if conf.Flags.IsDataPreloaded {
		return GetPreLoadedPooledUser(a, db)
	}
	ctx := context.Background()
	ctx = epificontext.CtxWithAppPlatform(ctx, commontypes.Platform_ANDROID)
	InitializeUserPool(ctx, map[string]*gormv2.DB{
		config.EpifiDb: db,
	}, conf)
	logger.Info(ctx, "Waiting for a user")
	var u *PooledUser
	var wait = 120 * time.Second
	select {
	case u = <-userPool:
	case <-time.After(wait):
		logger.Error(ctx, fmt.Sprintf("PooledUser allocation failed even after waiting for %v seconds", wait))
	}
	a.NotNil(u)
	logger.Info(ctx, fmt.Sprintf("Allocated a user %v", *u))
	return u, func() {
		userPool <- u
		logger.Info(ctx, fmt.Sprintf("Released a user %v", *u))
	}
}

func GetAllPooledUsers(a *require.Assertions, db *gormv2.DB, conf *config.Config) []*PooledUserWithRelease {
	if conf.Flags.IsDataPreloaded {
		return GetAllPreloadedPooledUsers(a, db)
	}
	var res []*PooledUserWithRelease
	for i := 0; i < poolSize; i++ {
		pu, rl := GetPooledUser(a, db, conf)
		res = append(res, &PooledUserWithRelease{User: pu, ReleaseUserFn: rl})
	}
	return res
}
func GetNPooledUsers(ctx context.Context, a *require.Assertions, db *gormv2.DB, conf *config.Config, numUsers int) ([]*PooledUserWithRelease, error) {
	if numUsers > poolSize {
		return nil, fmt.Errorf("Request for %v users cannot not be fulfilled. Pool size is %v", numUsers, poolSize)
	}
	var res []*PooledUserWithRelease
	for i := 0; i < numUsers; i++ {
		pu, rl := GetPooledUser(a, db, conf)
		res = append(res, &PooledUserWithRelease{User: pu, ReleaseUserFn: rl})
	}
	return res, nil
}

// GetPreLoadedPooledUser returns a onboarded user already preloaded in the db
func GetPreLoadedPooledUser(a *require.Assertions, db *gormv2.DB) (*PooledUser, releaseUser) {
	ctx := context.Background()
	var u *PooledUser
	var wait = 120 * time.Second
	select {
	case u = <-userPool:
	case <-time.After(wait):
		logger.Error(ctx, fmt.Sprintf("PooledUser allocation failed even after waiting for %v seconds", wait))
	}
	a.NotNil(u)
	logger.Info(ctx, fmt.Sprintf("Allocated a user %v", *u))
	return u, func() {
		userPool <- u
		logger.Info(ctx, fmt.Sprintf("Released a user %v", *u))
	}
}

// GetAllPreloadedPooledUsers returns all the prelaoded onboarded users
func GetAllPreloadedPooledUsers(a *require.Assertions, db *gormv2.DB) []*PooledUserWithRelease {
	var res []*PooledUserWithRelease
	for i := 0; i < poolSize; i++ {
		pu, rl := GetPreLoadedPooledUser(a, db)
		res = append(res, &PooledUserWithRelease{User: pu, ReleaseUserFn: rl})
	}
	return res

}

func GetPreloadedUser(ctx context.Context, t *testing.T, userType integrationType.UserType) *PooledUserWithRelease {
	var wait = 120 * time.Second
	switch userType {
	case integrationType.SAOnboardedMinKyc:
		return getSAOnboardedMinKycUser(ctx, t, wait)
	case integrationType.SAOnboardedFullKyc:
		return getSAOnboardedFullKycUser(ctx, t, wait)
	case integrationType.SAOnboarded:
		return getSAOnboardedUser(ctx, t, wait)
	default:
		logger.Panic(fmt.Sprintf("invalid user type %v", userType))
		return nil
	}
}

func getSAOnboardedUser(ctx context.Context, t *testing.T, wait time.Duration) *PooledUserWithRelease {
	var (
		u     *PooledUser
		level kyc.KYCLevel
	)

	a := require.New(t)
	select {
	case u = <-minKycUserPool:
		level = kyc.KYCLevel_MIN_KYC
	case u = <-fullKycUserPool:
		level = kyc.KYCLevel_FULL_KYC
	case <-time.After(wait):
		logger.Error(ctx, fmt.Sprintf("PooledUser allocation failed even after waiting for %v seconds", wait))
	}
	a.NotNil(u)
	logger.Info(ctx, fmt.Sprintf("Allocated a user %v", *u))
	return &PooledUserWithRelease{
		User: u,
		ReleaseUserFn: func() {
			if level == kyc.KYCLevel_MIN_KYC {
				minKycUserPool <- u
			} else {
				fullKycUserPool <- u
			}
			logger.Info(ctx, fmt.Sprintf("Released a user %v", *u))
		},
	}
}

// nolint:dupl
func getSAOnboardedMinKycUser(ctx context.Context, t *testing.T, wait time.Duration) *PooledUserWithRelease {
	a := require.New(t)
	var u *PooledUser
	select {
	case u = <-minKycUserPool:
	case <-time.After(wait):
		logger.Error(ctx, fmt.Sprintf("PooledUser allocation failed even after waiting for %v seconds", wait))
	}
	a.NotNil(u)
	logger.Info(ctx, fmt.Sprintf("Allocated a user %v", *u))
	return &PooledUserWithRelease{
		User: u,
		ReleaseUserFn: func() {
			minKycUserPool <- u
			logger.Info(ctx, fmt.Sprintf("Released a user %v", *u))
		},
	}
}

// nolint:dupl
func getSAOnboardedFullKycUser(ctx context.Context, t *testing.T, wait time.Duration) *PooledUserWithRelease {
	a := require.New(t)
	var u *PooledUser
	select {
	case u = <-fullKycUserPool:
	case <-time.After(wait):
		logger.Error(ctx, fmt.Sprintf("PooledUser allocation failed even after waiting for %v seconds", wait))
	}
	a.NotNil(u)
	logger.Info(ctx, fmt.Sprintf("Allocated a user %v", *u))
	return &PooledUserWithRelease{
		User: u,
		ReleaseUserFn: func() {
			fullKycUserPool <- u
			logger.Info(ctx, fmt.Sprintf("Released a user %v", *u))
		},
	}
}

// Initializes a user poll from preloaded users in db
func InitializePreLoadedUserPool(ctx context.Context, dbConns map[string]*gormv2.DB) {
	t := &testing.T{}
	a1 := require.New(t)
	dataList := CreatePreloadedUserParams(dbConns, preOnboardedPhoneNumbers)
	onbDeps, closeConnFunc := NewOnbDeps(ctx, require.New(t), dbConns)
	defer closeConnFunc()
	for _, data := range dataList {
		reqH, _ := UserWithAccessToken(ctx, onbDeps, data)
		actor, _ := GetUserDetails(ctx, onbDeps, data.Phone)
		bankCustRes, err := onbDeps.BankcustClient.GetBankCustomer(ctx, &bankCustPb.GetBankCustomerRequest{
			Identifier: &bankCustPb.GetBankCustomerRequest_ActorId{
				ActorId: actor.GetId(),
			},
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		})
		a1.Nil(epifigrpc.RPCError(bankCustRes, err))
		switch bankCustRes.GetBankCustomer().GetKycInfo().GetKycLevel() {
		case kyc.KYCLevel_FULL_KYC:
			logger.InfoNoCtx(fmt.Sprintf("%v : added to full kyc user pool", actor.GetId()))
			fullKycUserPool <- &PooledUser{
				Data:          data,
				RequestHeader: reqH,
			}
		case kyc.KYCLevel_MIN_KYC:
			logger.InfoNoCtx(fmt.Sprintf("%v : added to min kyc user pool", actor.GetId()))
			minKycUserPool <- &PooledUser{
				Data:          data,
				RequestHeader: reqH,
			}
		default:
			logger.Panic(fmt.Sprintf("invalid KYC level for actor %v", actor.GetId()))
		}
	}
	logger.InfoNoCtx("preloading users")
}

// CreatePreloadedUserParams fetches user data from db for preloading already onboarded users
// nolint
func CreatePreloadedUserParams(dbConns map[string]*gormv2.DB, preOnboardedPhoneNumbers []uint64) []*UserData {
	var (
		userData              []*UserData
		phoneNumberList       = preOnboardedPhoneNumbers
		phoneNumberStringList []string
		userIds               []string
		actorIds              []string
		users                 []*userModel.User
		actors                []*actorModel.Actor
		devices               []*authModel.DeviceRegistration
		userIdToActorMap      = make(map[string]*actorModel.Actor)
		actorIdToDeviceReg    = make(map[string]*authModel.DeviceRegistration)
		phToUserMap           = make(map[uint64]*userModel.User)
		db                    = dbConns[config.EpifiDb]
		actorDb               = dbConns[config.ActorDb]
	)

	for _, ph := range phoneNumberList {
		phoneNumberStringList = append(phoneNumberStringList, "91"+strconv.FormatUint(ph, 10))
	}
	if err := db.Exec("select * from users where computed_phone_number in (?)", phoneNumberStringList).Find(&users).Error; err != nil {
		logger.Panic("error fetching users from db", zap.Error(err))
	}

	for _, onboardedUser := range users {
		userIds = append(userIds, onboardedUser.Id)
		if onboardedUser.Profile != nil && onboardedUser.Profile.PhoneNumber != nil {
			phToUserMap[onboardedUser.Profile.PhoneNumber.NationalNumber] = onboardedUser
		}
	}
	if err := actorDb.Raw("select * from actors where entity_id in (?)", userIds).Find(&actors).Error; err != nil {
		logger.Panic("error fetching actors from db", zap.Error(err))
	}

	for _, onboardedActor := range actors {
		actorIds = append(actorIds, onboardedActor.Id)
		userIdToActorMap[onboardedActor.EntityId.String] = onboardedActor
	}
	if err := db.Exec("select * from device_registrations where actor_id in (?)", actorIds).Find(&devices).Error; err != nil {
		logger.Panic("error fetching actors from db", zap.Error(err))
	}
	for _, device := range devices {
		actorIdToDeviceReg[device.ActorId] = device
	}

	for _, ph := range phoneNumberList {
		onboardedUser, ok := phToUserMap[ph]
		if !ok {
			continue
		}
		onboardedActor, ok := userIdToActorMap[onboardedUser.Id]
		if !ok {
			continue
		}
		device, ok := actorIdToDeviceReg[onboardedActor.Id]
		if !ok {
			continue
		}
		userDataParam := &UserData{
			Device: &commontypes.Device{
				Manufacturer: "testManufacturer",
				Model:        "testModel",
				HwVersion:    "testHwVersion",
				SwVersion:    "testSwVersion",
				OsApiVersion: "testOsApiVersion",
				DeviceId:     device.DeviceId,
				LatLng:       RandIndiaLatLng(),
				AppVersion:   79,
				Platform:     commontypes.Platform_ANDROID,
			},

			Phone: onboardedUser.Profile.PhoneNumber,
			Name: &commontypes.Name{
				FirstName: "Byomkesh",
				LastName:  "Bakshi",
			},
			// The simulator accepts this as a valid value
			Pan:            onboardedUser.Profile.PAN,
			Video:          []byte{1, 2, 3},
			OAuthIDToken:   string(GetClaims(onboardedUser.Profile.Email)),
			SafetyNetToken: "ValidToken",
		}

		userData = append(userData, userDataParam)
	}

	logger.InfoNoCtx("total number of onboarded users found", zap.Int("user", len(userData)))
	return userData
}

func InitializeUserPool(ctx context.Context, dbConns map[string]*gormv2.DB, conf *config.Config) {
	initUserPool.Do(func() {
		if conf.Flags.IsDataPreloaded {
			InitializePreLoadedUserPool(ctx, dbConns)
			return
		}
		// TODO(aditya): hotfix for making tests pass on CI. Figure out a better solution.
		t := &testing.T{}
		a := require.New(t)
		onbDeps, closeConnFunc := NewOnbDeps(ctx, a, dbConns)
		defer closeConnFunc()
		minKycDataList := make([]*UserData, 0)
		fullKycDataList := make([]*UserData, 0)
		if len(conf.PreOnboardedPhoneNumbers) < minKycUserPoolSize+fullKycUserPoolSize {
			logger.Fatal("length of preOnboardedNumbers is less than required size")
		}
		logger.InfoNoCtx("add users to pool")
		// Using first minKycUserPoolSize of numbers from PreOnboardedPhoneNumbers to create min KYC users
		for i := 0; i < minKycUserPoolSize; i++ {
			minKycDataList = append(minKycDataList, CreateUserParams(OverrideUserParams{
				PhoneNoPref: int(preOnboardedPhoneNumbers[i]),
			}))
		}
		// Using rest of the numbers to create full KYC users
		for i := 0; i < fullKycUserPoolSize; i++ {
			fullKycDataList = append(fullKycDataList, CreateUserParams(OverrideUserParams{
				PhoneNoPref: int(preOnboardedPhoneNumbers[i+minKycUserPoolSize]),
			}))
		}

		var wg sync.WaitGroup
		for i := 0; i < minKycUserPoolSize; i++ {
			wg.Add(1)
			iLocal := i
			goroutine.RunWithCtx(ctx, func(ctx context.Context) {
				defer wg.Done()
				addPooledUser(ctx, iLocal, onbDeps, minKycDataList[iLocal])
			})
		}
		waitgroup.SafeWait(&wg, 30*time.Minute)
		for i := 0; i < fullKycUserPoolSize; i++ {
			wg.Add(1)
			iLocal := i
			goroutine.RunWithCtx(ctx, func(ctx context.Context) {
				defer wg.Done()
				reqH := CreateFullKycSavingsAccountViaBKYC(ctx, onbDeps, fullKycDataList[iLocal])
				fullKycUserPool <- &PooledUser{
					Data:          fullKycDataList[iLocal],
					RequestHeader: reqH,
				}
			})
		}
		waitgroup.SafeWait(&wg, 30*time.Minute)
	})
}

func addPooledUser(ctx context.Context, index int, deps *OnbDep, data *UserData) {
	// to avoid duplicate index error when creating vpa
	data.Name = names[index]
	reqH := CreateSavingsAccountForUser(ctx, deps, data)
	SkipOnboardingStage(ctx, reqH, deps.SignupClient, deps.Assert, signup.SkipOnboardingStageRequest_ADD_MONEY)
	NextAction(ctx, deps.Assert, deps.SignupClient, reqH)
	minKycUserPool <- &PooledUser{
		Data:          data,
		RequestHeader: reqH,
	}
}

func SkipOnboardingStage(ctx context.Context, reqH *header.RequestHeader, sc signup.SignupClient, a *require.Assertions, stage signup.SkipOnboardingStageRequest_Stage) {
	resp, err := sc.SkipOnboardingStage(ctx, &signup.SkipOnboardingStageRequest{
		Req:   reqH,
		Stage: stage,
	})
	a.NoError(err, fmt.Sprintf("error in skip onboarding stage: %v", err))
	a.Equal(rpc.StatusOk().GetCode(), resp.GetRespHeader().GetStatus().GetCode(), fmt.Sprintf("skip onboarding res: %v", resp.String()))
}

func PollAuthStatusScreen(ctx context.Context, reqH *header.RequestHeader, deps *OnbDep, expectedScreen deeplink.Screen, clientReqId string) *deeplink.Deeplink {
	for attempt := 1; attempt <= PollMaxRetries; attempt++ {
		res, err := deps.FeAuthOrchClient.GetAuthFlowStatus(ctx, &feAuthOrchPb.GetAuthFlowStatusRequest{
			Req:             reqH,
			ClientRequestId: clientReqId,
		})
		deps.Assert.NoError(epifigrpc.RPCError(res.GetRespHeader(), err))
		logger.Info(ctx, fmt.Sprintf("GetAuthFlowStatus next action : %v", res.GetNextAction()))
		if res.GetNextAction().GetScreen() == expectedScreen {
			return res.GetNextAction()
		}
		time.Sleep(2 * time.Second)
	}
	return nil
}

func PollFELivenessSummaryStatus(ctx context.Context, reqH *header.RequestHeader, deps *OnbDep, expectedScreen deeplink.Screen, clientReqId string) *deeplink.Deeplink {
	for attempt := 1; attempt <= PollMaxRetries; attempt++ {
		res, err := deps.FeAuthOrchClient.GetLivenessSummaryStatus(ctx, &feAuthOrchPb.GetLivenessSummaryStatusRequest{
			Req:             reqH,
			ClientRequestId: clientReqId,
			LivenessFlow:    deeplink.LivenessFlow_DEVICE_BIOMETRIC,
		})
		logger.Info(ctx, fmt.Sprintf("GetLivenessSummaryStatusResponse : %v", res))
		deps.Assert.NoError(epifigrpc.RPCError(res.GetRespHeader(), err))
		if res.GetNextAction().GetScreen() == expectedScreen {
			return res.GetNextAction()
		}
		time.Sleep(2 * time.Second)
	}
	return nil
}

// UserWithRefreshToken onboards the user till phone number verification step
func UserWithRefreshToken(ctx context.Context, dep *OnbDep, aUser *UserData) (*header.RequestHeader, *signup.VerifyOtpResponse) {
	authDao := dao.NewAuthDao(dep.DB)
	appName := commontypes.AppName_APP_NAME_FI_ORIGINAL
	reqH := &header.RequestHeader{
		Auth: &header.AuthHeader{
			Device:         aUser.Device,
			SafetyNetToken: aUser.SafetyNetToken,
		},
		Platform:       header.Platform_ANDROID,
		AppVersionCode: aUser.Device.GetAppVersion(),
		AppVersionName: "AppVersionName",
		AppName:        appName,
	}

	resp := GenerateOTP(ctx, dep.Assert, dep.SignupClient, reqH, aUser.Phone)
	respVerify := VerifyOTP(ctx, dep.Assert, authDao, resp.GetToken(), dep.SignupClient, reqH, aUser.Phone)
	return reqH, respVerify
}

// UserWithAccessToken onboards the user till email verification step
func UserWithAccessToken(ctx context.Context, dep *OnbDep, aUser *UserData, loginOpts ...*LoginOpts) (*header.RequestHeader, *signup.LoginWithOAuthResponse) {
	reqH, respVerify := UserWithRefreshToken(ctx, dep, aUser)
	respAddOAuth := AddOAuthAccount(ctx, dep, reqH, respVerify.GetRefreshToken(), aUser, loginOpts...)
	logger.Info(ctx, "addoauth response", zap.Any("response", respAddOAuth))
	accessToken := respAddOAuth.GetAccessToken()
	dep.Assert.NotEmpty(accessToken)
	reqH.GetAuth().AuthToken = &header.AuthHeader_AccessToken{AccessToken: accessToken}
	return reqH, respAddOAuth
}

func UserStuckInRiskScreening(ctx context.Context, dep *OnbDep, isBlocked bool) (*header.RequestHeader, *UserData, *deeplink.Deeplink) {
	mailId := idgen.RandAlphaNumericString(10) + "<EMAIL>"
	aUser := CreateUserParams([]OverrideUserParams{
		{
			Pan:        EKYCPAN,
			MailIdPref: mailId,
		},
	}...)
	// Red list email to get the user stuck at risk screening for manual review
	score := float32(80)
	if isBlocked {
		score = 100
	}
	redListEmail(ctx, dep, mailId, score)

	reqH, _ := DoEKYC(ctx, dep, aUser)
	nextAction := PollNextAction(ctx, dep, reqH)
	lo := WaitForLivenessNextAction(ctx, dep, reqH, nextAction)
	CheckLivenessVideo(ctx, dep.Assert, dep.SignupClient, reqH, aUser.Video, lo.GetAttemptId())
	nextAction = PollNextAction(ctx, dep, reqH)
	dep.Assert.Equal(deeplink.Screen_DETAILED_ERROR_VIEW_SCREEN.String(), nextAction.GetScreen().String())
	return reqH, aUser, nextAction
}

// GetNewAccessToken for user
func NewAccessTokenForUser(ctx context.Context, dep *OnbDep, aUser *UserData, reqH *header.RequestHeader) *signup.LoginWithOAuthResponse {
	tokenRes, err := dep.AuthClient.GetTokenDetails(ctx, &authPb.GetTokenDetailsRequest{
		IdentifierOneof: &authPb.GetTokenDetailsRequest_PhoneNumber{
			PhoneNumber: aUser.Phone,
		},
		TokenTypes: []authPb.TokenType{
			authPb.TokenType_REFRESH_TOKEN,
		},
		Limit:          1,
		IncludeDeleted: false,
	})

	dep.Assert.Nil(err)
	dep.Assert.Equal(rpc.StatusOk().GetCode(), tokenRes.GetStatus().GetCode(), fmt.Sprintf("getTokenDetails res: %v", tokenRes.String()))
	dep.Assert.NotEqual(len(tokenRes.GetTokenDetails()), 0)

	respAddOAuth := AddOAuthAccount(ctx, dep, reqH, tokenRes.GetTokenDetails()[0].GetToken(), aUser)
	logger.Info(ctx, "addoauth response", zap.Any("response", respAddOAuth))
	accessToken := respAddOAuth.GetAccessToken()
	dep.Assert.NotEmpty(accessToken)
	reqH.Auth.AuthToken = &header.AuthHeader_AccessToken{AccessToken: accessToken}
	return respAddOAuth
}

// CreateSavingsAccountForUser gets a user onboarded till savings account creation
func CreateSavingsAccountForUser(ctx context.Context, dep *OnbDep, userData *UserData) *header.RequestHeader {
	reqH, _ := DoEKYCAndLiveness(ctx, dep, userData)
	AddShippingAddress(ctx, dep.Assert, dep.UserClient, reqH)
	legalNameRes := GetLegalName(ctx, dep.Assert, dep.UserClient, reqH)
	ConfirmCardPreference(ctx, dep.Assert, dep.UserClient, reqH, types.AddressType_SHIPPING, legalNameRes.GetLegalName())
	RegisterDevice(ctx, dep.Assert, dep.SignupClient, reqH, false, userData.SimSubIds[0], dep, userData)
	acRes := CreateAccountSignup(ctx, dep.Assert, dep.SignupClient, reqH, dep, userData)
	SetCardPin(ctx, dep.Assert, dep.CardClient, reqH, acRes.GetCardDetails().GetCardId(), "", card.UIEntryPoint_ONBOARDING)
	// Since card pin set is async call, we can proceed only after success pin set status
	CardPinSetUpStatus(ctx, dep.Assert, dep.CardClient, reqH, acRes.GetCardDetails().GetCardId())
	return reqH
}

func CreateFullKycSavingsAccountViaBKYC(ctx context.Context, deps *OnbDep, userData *UserData) *header.RequestHeader {
	agent := CreateUserParams()
	agentReqH := CreateAgentWithAccessToken(ctx, deps, agent)

	aUser := userData
	userReqH, _ := DoEKYC(ctx, deps, aUser)
	nextAction := PollNextAction(ctx, deps, userReqH)
	// Now we perform BKYC for the user. Ideally a user would land on the BKYC consent page via a deeplink send to him
	// through comms.
	handshakeInfo := GetHeaderWithHandshakeInfo(ctx, agentReqH, userReqH, deps, aUser)
	GetUserDetailsOnAgentApp(ctx, deps, agentReqH, handshakeInfo)
	PerformBKYC(ctx, deps, agentReqH, userReqH, handshakeInfo)
	lo := WaitForLivenessNextAction(ctx, deps, userReqH, nextAction)
	CheckLivenessVideo(ctx, deps.Assert, deps.SignupClient, userReqH, aUser.Video, lo.GetAttemptId())
	nextAction = PollNextAction(ctx, deps, userReqH)
	nextAction = DoOnboardingFromAction(ctx, deps, userReqH, nextAction, aUser)
	ScreenEquals(deps.Assert, deeplink.Screen_HOME, nextAction.GetScreen())
	return userReqH
}

func GetClaims(mailId string) []byte {
	var claims struct {
		Email         string `json:"email"`
		EmailVerified bool   `json:"email_verified"`
		Name          string `json:"name"`
		Picture       string `json:"picture"`
		GivenName     string `json:"given_name"`
		FamilyName    string `json:"family_name"`
		Locale        string `json:"locale"`
		Issuer        string
	}
	claims.GivenName = "first"
	claims.FamilyName = "last"
	claims.Email = mailId
	c, _ := json.Marshal(claims)
	return c
}

type OverrideUserParams struct {
	MailIdPref      string
	PhoneNoPref     int
	CountryCodePref int
	Pan             string
	FirstName       string
	DOB             *date.Date
	LatLong         *latLngPb.LatLng
	DeviceId        string
	LivenessVideo   []byte
	SalaryB2BUser   bool
}

// CreateUserParams creates test data for creating unique users during a test. It generates random email id,
// phone number and a "special" OAuthToken that has the OAuth claims marshaled in it. The same can be unmarshalled by
// the OAuth verifier in test environments. This provides control to the test clients over the email id used for a test
// user.
func CreateUserParams(userDataPref ...OverrideUserParams) *UserData {
	// randomise lat lon to avoid device_locations_lat_long_uniq_idx constraint violation
	rand.Seed(time.Now().UnixNano())
	// nolint:gosec
	indiaLatLng := RandIndiaLatLng()
	lat, lon := indiaLatLng.GetLatitude(), indiaLatLng.GetLongitude()
	mailId := idgen.RandAlphaNumericString(10) + "<EMAIL>"
	count := atomic.AddInt32(&ckycUserCount, 1)
	pan := fmt.Sprintf("TESPT%04dT", count)
	firstName := "Byomkesh"
	dob := userDob
	salaryB2BUser := false

	// For Fixing smoke test on QA (https://jenkins-deploy.pointz.in/job/Scripts/job/Backend/job/smoke-test-v2/255/console)
	randInt, err := cryprand.Int(cryprand.Reader, big.NewInt(9000))
	if err != nil {
		fmt.Println("error while random number generator :", err)
		randInt = big.NewInt(0)
	}
	r := strconv.Itoa(int(randInt.Int64()) + 1000)
	phInt, _ := strconv.Atoi(fmt.Sprintf("23%v%v", pan[5:9], r))
	deviceId := idgen.RandAlphaNumericString(15)
	livVideo := []byte{1, 2, 3}
	countryCode := uint32(91)
	if len(userDataPref) > 0 {
		if userDataPref[0].MailIdPref != "" {
			mailId = userDataPref[0].MailIdPref
		}
		if userDataPref[0].PhoneNoPref != 0 {
			phInt = userDataPref[0].PhoneNoPref
		}
		if userDataPref[0].CountryCodePref != 0 {
			countryCode = uint32(userDataPref[0].CountryCodePref)
		}
		if userDataPref[0].Pan != "" {
			pan = userDataPref[0].Pan
		}
		if userDataPref[0].FirstName != "" {
			firstName = userDataPref[0].FirstName
		}
		if userDataPref[0].DOB != nil {
			dob = userDataPref[0].DOB
		}
		if userDataPref[0].LatLong != nil {
			if userDataPref[0].LatLong.GetLatitude() != 0 {
				lat = userDataPref[0].LatLong.GetLatitude()
			}
			if userDataPref[0].LatLong.GetLongitude() != 0 {
				lon = userDataPref[0].LatLong.GetLongitude()
			}
		}
		if userDataPref[0].DeviceId != "" {
			deviceId = userDataPref[0].DeviceId
		}
		if userDataPref[0].LivenessVideo != nil {
			livVideo = userDataPref[0].LivenessVideo
		}
		salaryB2BUser = userDataPref[0].SalaryB2BUser
	}
	c := GetClaims(mailId)
	ph := uint64(phInt)
	// log caller with user info for debugging
	_, src, line, _ := runtime.Caller(1)
	logger.DebugNoCtx(
		fmt.Sprintf("Dequeue user with phone number %v", ph),
		zap.String("caller", fmt.Sprintf("%v:%v", src, line)),
	)

	return &UserData{
		SimSubIds: []int32{
			atomic.AddInt32(&simId, 1), atomic.AddInt32(&simId, 1),
		},
		Device: &commontypes.Device{
			Manufacturer: "testManufacturer",
			Model:        "testModel",
			HwVersion:    "testHwVersion",
			SwVersion:    "testSwVersion",
			OsApiVersion: "69",
			DeviceId:     deviceId,
			LatLng:       &latLngPb.LatLng{Latitude: lat, Longitude: lon},
			AppVersion:   200,
			Platform:     commontypes.Platform_ANDROID,
		},

		Phone: &commontypes.PhoneNumber{
			CountryCode:    countryCode,
			NationalNumber: ph,
		},
		Email: mailId,
		Name: &commontypes.Name{
			FirstName: firstName,
			LastName:  "Bakshi",
		},
		// The simulator accepts this as a valid value
		Pan:            pan,
		Video:          livVideo,
		OAuthIDToken:   string(c),
		SafetyNetToken: "ValidToken",
		DOB:            dob,
		SalaryB2BUser:  salaryB2BUser,
		PanName: &commontypes.Name{
			FirstName: "Jolly",
			LastName:  "Joseph",
		},
	}
}

func ParentsNameGetter(ctx context.Context, a *require.Assertions, feUserClient user.UserClient, reqH *header.RequestHeader) *deeplink.Deeplink {
	response, err := feUserClient.UpdateMotherFatherName(ctx, &user.UpdateMotherFatherNameRequest{Req: reqH, MotherName: "Mother Name", FatherName: "Father Name"})
	a.Nil(err)
	a.Equal(rpc.StatusOk().GetCode(), response.GetStatus().GetCode())
	return response.NextAction
}

// CreateNominee for the actor.
// We have chosen a nominee who is a minor, hence, guardian details are also added
func CreateNominee(ctx context.Context, a *require.Assertions, feUserClient user.UserClient, reqH *header.RequestHeader, userName string) *user.CreateNomineeResponse {
	// verifies failure conditions for nominee creation
	VerifyNomineeValidity(ctx, a, feUserClient, reqH, userName)

	logger.Info(ctx, "Creating nominee for user")
	contactInfo := &types.ContactInfo{
		PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9999900000},
		EmailId:     "<EMAIL>",
		Address: &types.PostalAddress{
			AdministrativeArea: "Karnataka",
			Locality:           "North Banglore",
			AddressLines:       []string{"01, Hogwarts School of Witchcraft and Wizardry"},
			PostalCode:         "testPostalCode",
		},
		AddressType: types.AddressType_PERMANENT,
	}

	guardianInfo := &types.GuardianInfo{
		Relationship: types.RelationType_MOTHER,
		Name:         "Voldemort",
		ContactInfo:  contactInfo,
	}

	nominee := &types.Nominee{
		Relationship: types.RelationType_BROTHER,
		Name:         "Tom Riddle",
		Dob:          &timestamp.Timestamp{Seconds: **********},
		ContactInfo:  contactInfo,
		GuardianInfo: guardianInfo,
	}

	// explicitly testing with both auth header & request header
	response, err := feUserClient.CreateNominee(ctx, &user.CreateNomineeRequest{
		Req:     reqH,
		Nominee: nominee,
	})
	a.Nil(err)
	a.Equal(rpc.StatusOk().GetCode(), response.GetStatus().GetCode())
	a.NotNil(response.NomineeId)
	return response
}

// Verifies failure conditions for nominee creation
func VerifyNomineeValidity(ctx context.Context, a *require.Assertions, feUserClient user.UserClient, reqH *header.RequestHeader, userName string) {
	logger.Info(ctx, "Verifying nominee validity")

	logger.Debug(ctx, "Verifying failure condition - Nominee cannot be same as account holder ")
	nominee := &types.Nominee{
		Relationship: types.RelationType_FATHER_IN_LAW,
		Name:         userName,
		Dob:          &timestamp.Timestamp{Seconds: *********},
		ContactInfo: &types.ContactInfo{
			Address: &types.PostalAddress{
				PostalCode: "test-postalcode",
				AddressLines: []string{
					"420",
					"Epifi",
					"Apartment",
				},
			},
			PhoneNumber: &commontypes.PhoneNumber{
				NationalNumber: **********,
				CountryCode:    91,
			},
		},
	}
	response, err := feUserClient.CreateNominee(ctx, &user.CreateNomineeRequest{Req: reqH, Nominee: nominee})
	a.Nil(err)
	a.Equal(uint32(user.CreateNomineeResponse_FAILED_PRECONDITION_NOMINEE_SAME_AS_ACCOUNT_HOLDER), response.GetStatus().GetCode())

	logger.Debug(ctx, "Verifying failure condition - Nominee's guardian cannot be same as account holder ")
	guardianInfo := &types.GuardianInfo{
		Name:         userName,
		Relationship: types.RelationType_DE_FACTO_GUARDIAN,
	}
	nominee = &types.Nominee{
		Relationship: types.RelationType_MOTHER,
		Name:         "Tom Riddle",
		Dob:          &timestamp.Timestamp{Seconds: **********},
		GuardianInfo: guardianInfo,
		ContactInfo: &types.ContactInfo{
			Address: &types.PostalAddress{
				PostalCode: "test-postalcode",
				AddressLines: []string{
					"420",
					"Epifi",
					"Apartment",
				},
			},
			PhoneNumber: &commontypes.PhoneNumber{
				NationalNumber: **********,
				CountryCode:    91,
			},
		},
	}
	response, err = feUserClient.CreateNominee(ctx, &user.CreateNomineeRequest{Req: reqH, Nominee: nominee})
	a.Nil(err)
	a.Equal(uint32(user.CreateNomineeResponse_FAILED_PRECONDITION_GUARDIAN_SAME_AS_ACCOUNT_HOLDER), response.GetStatus().GetCode())
}

func DoPassScreenerWithIncomeEst(ctx context.Context, dep *OnbDep, reqH *header.RequestHeader, userData *UserData) {
	// salary b2b users have their screener stage skipped after employment declaration so assertion will be different
	if userData != nil && userData.SalaryB2BUser {
		return
	}
	PassIncomeEstimatorForUser(ctx, dep, userData)
	DoCreditScoreVerificationStage(ctx, dep, reqH, userData)
}

func RecordFiConsent(ctx context.Context, a *require.Assertions, feConsentClient consent.ConsentClient, reqH *header.RequestHeader, consentList []consent.ConsentType, consents ...string) {
	response, err := feConsentClient.RecordConsent(ctx, &consent.RecordConsentRequest{
		Req:             reqH,
		ConsentTypeList: consentList,
		Consents:        consents,
	})
	a.NoError(err)
	a.Equal(rpc.StatusOk().GetCode(), response.GetStatus().GetCode(), fmt.Sprintf("consent res: %v", response))
	resp, errResp := feConsentClient.RecordWAConsent(ctx, &consent.RecordWAConsentRequest{
		Req:          reqH,
		ConsentGiven: commontypes.BooleanEnum_TRUE,
	})
	a.NoError(errResp)
	a.Equal(rpc.StatusOk().GetCode(), resp.GetStatus().GetCode(), fmt.Sprintf("RecordWAConsent res: %v", resp))
}

func RecordConsent(ctx context.Context, a *require.Assertions, feConsentClient consent.ConsentClient, reqH *header.RequestHeader, consentList []string) {
	response, err := feConsentClient.RecordConsent(ctx, &consent.RecordConsentRequest{
		Req:      reqH,
		Consents: consentList,
	})
	a.NoError(err)
	a.Equal(rpc.StatusOk().GetCode(), response.GetStatus().GetCode(), fmt.Sprintf("consent res: %v", response))
}

func CallAndAssertSavePanDob(ctx context.Context, a *require.Assertions, dep *OnbDep, reqH *header.RequestHeader, panNo string, panName *commontypes.Name, dob *date.Date) *signup.SavePanAndDobResponse {
	// TODO(aditya): get screen options also in the signature and populate entry point
	res, err := CallSavePanDob(ctx, dep, reqH, panNo, panName, dob)
	a.NoError(err, fmt.Sprintf("error in save pan dob: %v", err))
	a.Equal(rpc.StatusOk().GetCode(), res.GetRespHeader().GetStatus().GetCode(), fmt.Sprintf("save pan dob res: %v", res.String()))
	return res
}

func CallSavePanDob(ctx context.Context, dep *OnbDep, reqH *header.RequestHeader, panNo string, panName *commontypes.Name, dob *date.Date) (*signup.SavePanAndDobResponse, error) {
	return dep.SignupClient.SavePanAndDob(ctx, &signup.SavePanAndDobRequest{
		Req:        reqH,
		PanNumber:  panNo,
		Dob:        dob,
		Name:       panName,
		EntryPoint: signup.EntryPoint_ENTRY_POINT_ONBOARDING.String(),
		Blob:       nil,
		Consents: []string{
			consentPb.ConsentType_cKYC.String(),
			consentPb.ConsentType_FED_STORAGE_POLICY.String(),
		},
	})
}

func DoEmploymentStage(ctx context.Context, dep *OnbDep, reqH *header.RequestHeader, aUser *UserData) *deeplink.Deeplink {
	a := dep.Assert
	employmentType := uistate.EmploymentType_SALARIED
	if aUser != nil && aUser.EmploymentType != uistate.EmploymentType_EMPLOYMENT_TYPE_UNSPECIFIED {
		employmentType = aUser.EmploymentType
	}
	processEmpRes, processResErr := dep.ScreeningClient.ProcessEmploymentData(ctx, &screening.ProcessEmploymentDataRequest{
		Req:            reqH,
		EmploymentType: employmentType,
		AnnualSalary: &screening.AnnualSalary{
			Range: &screening.AnnualSalaryRange{
				MinValue: 100000,
				MaxValue: 1000000,
			},
		},
		OccupationType: employmentPb.OccupationType_OCCUPATION_TYPE_ENGINEERING.String(),
	})
	a.Nil(fepkg.FeRPCError(processEmpRes, processResErr), "error in process employment data: %v", processEmpRes)

	for i := 0; i < PollMaxRetriesLow; i++ {
		checkEmpRes, checkEmpErr := dep.ScreeningClient.CheckEmploymentVerificationStatus(ctx, &screening.CheckEmploymentVerificationStatusRequest{Req: reqH})
		a.Nil(fepkg.FeRPCError(checkEmpRes, checkEmpErr), "error in check employment status: %v", checkEmpRes)
		if checkEmpRes.GetNextAction().GetScreen() == deeplink.Screen_EMPLOYMENT_VERIFICATION_STATUS {
			logger.Info(ctx, fmt.Sprintf("retrying employment verification status check, i: %v | resp: %v", i, checkEmpRes))
			time.Sleep(PollInterval)
			continue
		}
		return checkEmpRes.GetNextAction()
	}
	a.Fail("retries exhausted for check employment verification status")
	return nil
}

/*
DoTnCPanEmploymentStages performs actions for following stages
onbPb.OnboardingStage_TNC_CONSENT,
onbPb.OnboardingStage_DOB_AND_PAN,
onbPb.OnboardingStage_EMPLOYMENT_VERIFICATION,
*/
func DoTnCPanEmploymentStages(ctx context.Context, dep *OnbDep, reqH *header.RequestHeader, aUser *UserData) *deeplink.Deeplink {
	// T&C Stage
	RecordFiConsent(ctx, dep.Assert, dep.ConsentClient, reqH, ConsentList)

	RecordConsent(ctx, dep.Assert, dep.ConsentClient, reqH, []string{consentPb.ConsentType_CONSENT_INDIAN_RESIDENCY.String(), consentPb.ConsentType_CONSENT_NOT_POLITICALLY_EXPOSED.String(), consentPb.ConsentType_CONSENT_TYPE_DISABILITY.String()})

	// PAN DOB Stage
	res := CallAndAssertSavePanDob(ctx, dep.Assert, dep, reqH, aUser.Pan, aUser.PanName, aUser.DOB)

	// Employment declaration Stage
	dep.Assert.Equal(deeplink.Screen_EMPLOYMENT_DECLARATION.String(), res.GetNextAction().GetScreen().String(), "screen after pan dob stage")
	return DoEmploymentStage(ctx, dep, reqH, aUser)
}

func DoCreditScoreVerificationStage(ctx context.Context, dep *OnbDep, reqH *header.RequestHeader, _ *UserData) *deeplink.Deeplink {
	a := dep.Assert
	startCRRes, startCRErr := dep.ScreeningClient.GetConsentAndVerifyCreditReport(ctx, &screening.GetConsentAndVerifyCreditReportRequest{
		Req:     reqH,
		Consent: true,
	})
	a.Nil(fepkg.FeRPCError(startCRRes, startCRErr), "error in GetConsentAndVerifyCreditReport: %v", startCRRes)

	for i := 0; i < PollMaxRetries; i++ {
		checkCRRes, checkCRErr := dep.ScreeningClient.CheckCreditReportVerificationStatus(ctx, &screening.CheckCreditReportVerificationStatusRequest{Req: reqH})
		a.Nil(fepkg.FeRPCError(checkCRRes, checkCRErr), "error in CheckCreditReportVerificationStatus: %v", checkCRRes)
		if checkCRRes.GetNextAction().GetScreen() == deeplink.Screen_CREDIT_REPORT_VERIFICATION_STATUS {
			logger.Info(ctx, fmt.Sprintf("retrying CheckCreditReportVerificationStatus, i: %v | resp: %v", i, checkCRRes))
			time.Sleep(PollInterval)
			continue
		}
		return checkCRRes.GetNextAction()
	}
	a.Fail("retries exhausted for check credit report verification status")
	return nil
}

type LoginOpts struct {
	// RPC Status in LoginWithOAuthResponse will be asserted against this code if provided; defaults to rpc.StatusOk()
	ExpectedRPCStatusCode *rpc.Status
	ExpectedErrorViewCode int

	// With D2H coming into the picture, this is required to force the user into SA onboarding flow in smoke tests.
	// Sample acquisition info for savings account (banking) intent. Campaign with `_sa` suffix is used to identify it.
	// &signup.AcquisitionInfo{
	//   AppsflyerOnConversionDataSuccessPayload: struct.NewStruct(map[string]interface{}{
	//     "campaign": "random_campaign_sa",
	//     "af_prt": "some partner" })}
	AcquisitionInfo *signup.AcquisitionInfo
}

func AddOAuthAccount(ctx context.Context, deps *OnbDep, reqH *header.RequestHeader, refreshToken string, data *UserData, loginOpts ...*LoginOpts) *signup.LoginWithOAuthResponse {
	var (
		respOAuth = &signup.LoginWithOAuthResponse{}
		err       error
	)

	// Setting options to default LoginWithOAuth options. will be overridden later in the flow.
	opts := &LoginOpts{
		ExpectedRPCStatusCode: rpc.StatusOk(),
		AcquisitionInfo:       nil,
	}

	// override opts from loginOpts
	if len(loginOpts) > 0 {
		overrideOpts := loginOpts[0]
		if overrideOpts.ExpectedRPCStatusCode != nil {
			opts.ExpectedRPCStatusCode = overrideOpts.ExpectedRPCStatusCode
		}
		if overrideOpts.AcquisitionInfo != nil {
			opts.AcquisitionInfo = overrideOpts.AcquisitionInfo
		}
	}

	if refreshToken != "" {
		reqH.GetAuth().AuthToken = &header.AuthHeader_RefreshToken{
			RefreshToken: refreshToken,
		}
	}

	// LoginWithOAuth is a flaky API. It fails often because user service is failing with "connection refused" error,
	// despite having health checks before starting the tests. Hypothesis is that User Server starts accepting
	// requests much later after Health check returns OK response. So, added retries.
	for i := 0; i < PollMaxRetriesLow; i++ {
		respOAuth, err = deps.SignupClient.LoginWithOAuth(
			ctx,
			&signup.LoginWithOAuthRequest{
				Req: reqH,
				OauthInfo: &signup.OAuthInfo{
					OauthToken:    data.OAuthIDToken,
					OauthProvider: signup.OAuthProvider_GOOGLE,
				},
				UserInfo: &signup.LoginWithOAuthRequest_UserInfo{
					GmailName:       data.Name,
					AcquisitionInfo: opts.AcquisitionInfo,
				},
				LoginDeviceInfo: &signup.LoginDeviceInfo{
					AndroidSimSubIds: data.SimSubIds,
				},
			})
		if grpcErr := fepkg.FeRPCError(respOAuth, err); grpcErr != nil && opts.ExpectedRPCStatusCode.GetCode() == rpc.StatusOk().GetCode() {
			if respOAuth.GetRespHeader().GetStatus().IsPermissionDenied() && respOAuth.GetDeviceIntegrityInfo().GetDeviceIntegrityNonce() != "" {
				integrityToken := VerifyDeviceIntegrity(ctx, &VerifyDeviceIntegrityParams{
					Deps:                deps,
					UserData:            data,
					TokenType:           types.DeviceIntegrityTokenType_DEVICE_INTEGRITY_TOKEN_TYPE_PLAY_INTEGRITY,
					RequestHeader:       reqH,
					AllowHighRiskDevice: commontypes.BooleanEnum_FALSE,
					ExpectedResult:      authPb.Result_RESULT_PASSED,
				})
				reqH.GetAuth().DeviceIntegrity = &header.DeviceIntegrity{
					DeviceIntegrityToken: integrityToken,
				}
				reqH.GetAuth().SafetyNetToken = ""
			}
			time.Sleep(PollInterval)
			fmt.Printf("Retrying Add OAuth as it failed with error: %v\n", grpcErr)
			continue
		}
		break
	}
	deps.Assert.NoError(err, fmt.Sprintf("error in addoReq: %v", err))
	deps.Assert.Equal(opts.ExpectedRPCStatusCode.GetCode(), respOAuth.GetRespHeader().GetStatus().GetCode(), fmt.Sprintf("addoauth res: %v", respOAuth.String()))
	if opts.ExpectedErrorViewCode != 0 {
		errorViewCode := respOAuth.GetRespHeader().GetErrorView().GetFullScreenErrorView().GetErrorCode()
		deps.Assert.Equal(opts.ExpectedErrorViewCode, errorViewCode, fmt.Sprintf("addoauth error view code: %v", errorViewCode))
	}

	// todo(saiteja): think of a better solve
	// to handle for cases where actor is already created and we want device integrity token
	// avoids code duplication in AFU tests
	if reqH.GetAuth().DeviceIntegrity == nil || reqH.GetAuth().GetDeviceIntegrity().DeviceIntegrityToken == "" {
		integrityToken := data.DeviceIntegrityToken
		if integrityToken == "" {
			integrityToken = VerifyDeviceIntegrity(ctx, &VerifyDeviceIntegrityParams{
				Deps:                deps,
				UserData:            data,
				TokenType:           types.DeviceIntegrityTokenType_DEVICE_INTEGRITY_TOKEN_TYPE_PLAY_INTEGRITY,
				RequestHeader:       reqH,
				AllowHighRiskDevice: commontypes.BooleanEnum_FALSE,
				ExpectedResult:      authPb.Result_RESULT_PASSED,
			})
		}
		reqH.GetAuth().DeviceIntegrity = &header.DeviceIntegrity{
			DeviceIntegrityToken: integrityToken,
		}
		reqH.GetAuth().SafetyNetToken = ""
	}
	return respOAuth
}

func VerifyOTP(ctx context.Context, a *require.Assertions, authDao *dao.AuthDao, token string, sc signup.SignupClient,
	reqH *header.RequestHeader, phoneNumber *commontypes.PhoneNumber) *signup.VerifyOtpResponse {
	// Get OTP from DB.
	otp, err := authDao.GetOtpByToken(ctx, token)
	a.NoError(err)

	// Verify OTP
	// VerifyOTP is a flaky API. It fails often because user service is failing with "connection refused" error,
	// despite having health checks before starting the tests. Hypothesis is that User Server starts accepting
	// requests much later after Health check returns OK response. So, added retries.
	var respVerify *signup.VerifyOtpResponse
	for i := 0; i < PollMaxRetriesLow; i++ {
		respVerify, err = sc.VerifyOtp(
			ctx,
			&signup.VerifyOtpRequest{
				Req:         reqH,
				PhoneNumber: phoneNumber,
				Token:       token,
				Otp:         otp.Otp,
			},
		)
		if err = fepkg.FeRPCError(respVerify, err); err != nil {
			time.Sleep(PollInterval)
			fmt.Printf("Retrying Verify OTP as it failed with error: %v\n", err)
			continue
		}
		break
	}
	a.NoError(err, fmt.Sprintf("Verify OTP failed with error: %v", respVerify.GetRespHeader().GetStatus()))
	logger.Info(ctx, "", zap.String("RefreshToken: ", respVerify.RefreshToken))
	return respVerify
}

func CheckLiveness(
	ctx context.Context,
	a *require.Assertions,
	sc signup.SignupClient,
	reqH *header.RequestHeader,
	livReqId string,
	video []byte,
	dontWaitForCreateAccount bool,
	allowErrorInLiveness ...bool,
) {
	CheckLivenessWithNextAction(
		ctx,
		a, sc, reqH, livReqId, video, dontWaitForCreateAccount,
		deeplink.Screen_CREATE_ACCOUNT, allowErrorInLiveness...,
	)
}

func CheckLivenessVideo(
	ctx context.Context,
	a *require.Assertions,
	sc signup.SignupClient,
	reqH *header.RequestHeader,
	video []byte,
	livReqId string,
	allowErrorInLiveness ...bool,
) {
	logger.Info(ctx, "Starting liveness for requestID", zap.String(logger.REQUEST_ID, livReqId))
	livClient, err := sc.CheckLiveness(ctx)
	a.NoError(err, fmt.Sprintf("error in check liveness: %v", err))
	a.NoError(
		livClient.Send(
			&signup.CheckLivenessRequest{
				Req: reqH,
				MetadataVideoChunks: &signup.CheckLivenessRequest_Options{
					Options: &signup.LivenessOptions{AttemptId: livReqId},
				},
			},
		),
	)
	a.NoError(
		livClient.Send(
			&signup.CheckLivenessRequest{
				Req:                 reqH,
				MetadataVideoChunks: &signup.CheckLivenessRequest_VideoChunk{VideoChunk: video},
			},
		),
	)
	a.NoError(
		livClient.Send(
			&signup.CheckLivenessRequest{
				Req:                 reqH,
				MetadataVideoChunks: &signup.CheckLivenessRequest_ImageFrame{ImageFrame: 1},
			},
		),
	)
	a.NoError(
		livClient.Send(
			&signup.CheckLivenessRequest{
				Req: reqH,
				MetadataVideoChunks: &signup.CheckLivenessRequest_PassiveImageFrames{
					PassiveImageFrames: &signup.CheckLivenessRequest_Frames{Frames: []int64{5, 10, 20, 30, 40, 50, 60, 100}},
				},
			},
		),
	)
	respLiv, err := livClient.CloseAndRecv()
	if len(allowErrorInLiveness) > 0 && allowErrorInLiveness[0] {
		return
	}
	a.NoError(err)
	a.Equal(rpc.StatusOk().Code, respLiv.Status.Code)
}

func CheckLivenessWithNextAction(
	ctx context.Context,
	a *require.Assertions,
	sc signup.SignupClient,
	reqH *header.RequestHeader,
	livReqId string,
	video []byte,
	dontWaitForCreateAccount bool,
	actionAfterLiveness deeplink.Screen,
	allowErrorInLiveness ...bool,
) {
	CheckLivenessVideo(ctx, a, sc, reqH, video, livReqId, allowErrorInLiveness...)
	if dontWaitForCreateAccount {
		time.Sleep(time.Second * 3)
		return
	}

	var nextAction *deeplink.Deeplink
	for i := 0; i < PollMaxRetries; i++ {
		time.Sleep(PollInterval)
		nextAction = NextAction(ctx, a, sc, reqH)

		// for following screens, polling is done.
		if nextAction.GetScreen() != deeplink.Screen_CHECK_LIVENESS &&
			nextAction.GetScreen() != deeplink.Screen_GET_NEXT_ONBOARDING_ACTION_API {
			// break if not polling screen.
			break
		}
	}

	a.Equal(actionAfterLiveness.String(), nextAction.GetScreen().String(),
		fmt.Sprintf("next action after liveness polling: %v", nextAction),
	)
}

func GenerateOTP(
	ctx context.Context,
	a *require.Assertions,
	sc signup.SignupClient,
	reqH *header.RequestHeader,
	phoneNumber *commontypes.PhoneNumber,
) *signup.GenerateOtpResponse {
	resp, err := sc.GenerateOtp(
		ctx,
		&signup.GenerateOtpRequest{
			Req:         reqH,
			PhoneNumber: phoneNumber,
		},
	)
	startTime := time.Now()
	for err != nil && time.Since(startTime) < 10*time.Second {
		resp, err = sc.GenerateOtp(
			ctx,
			&signup.GenerateOtpRequest{
				Req:         reqH,
				PhoneNumber: phoneNumber,
			},
		)
		time.Sleep(time.Second)
	}
	logger.Info(ctx, "generate otp response", zap.Any("resp", resp), zap.Error(err))
	a.NotEmpty(resp.GetToken(), "Token not received in Generate OTP call")
	// Generate OTP can throw error since phone number in user pool is not valid
	// Hence not checking error at this step since generate otp will return error in case
	// of sms not sent to user's device. In case there was some actual error in generate otp
	// the next step verify otp will fail so it is safe to do this.
	// a.NoError(err)
	// a.Equal(rpc.StatusOk().Code, resp.Status.Code, "status GenerateOtp ", resp.Status.String())
	return resp
}

func ClaimFiniteCode(
	ctx context.Context,
	a *require.Assertions,
	referralClient referral.ReferralClient,
	reqH *header.RequestHeader,
	finiteCode string,
) {
	resp, err := referralClient.ClaimFiniteCode(
		ctx,
		&referral.ClaimFiniteCodeRequest{
			Req:        reqH,
			FiniteCode: finiteCode,
		},
	)
	a.NoError(err, fmt.Sprintf("error in ClaimFiniteCode: %v", err))
	a.Equal(rpc.StatusOk().Code, resp.GetRespHeader().GetStatus().Code, resp.GetRespHeader().GetStatus())
}

func RegisterFCMToken(ctx context.Context, a *require.Assertions, fcmClient fcm.FCMClient, reqH *header.RequestHeader) {
	fcmResp, errResp := fcmClient.RegisterDevice(ctx, &fcm.RegisterDeviceRequest{
		Req:         reqH,
		DeviceToken: "device-token",
	})
	a.Nil(epifigrpc.RPCError(fcmResp, errResp))
	a.Equal(rpc.StatusOk().GetCode(), fcmResp.GetStatus().GetCode(),
		fmt.Sprintf("RegisterDevice resp status: %v", fcmResp.GetStatus().String()),
	)
}

func RegisterDevice(ctx context.Context, a *require.Assertions, sc signup.SignupClient,
	reqH *header.RequestHeader, isAccountAccessible bool, simId int32, dep *OnbDep, user *UserData) *signup.RegisterDeviceResponse {
	smsResp, err := sc.GetDeviceRegistrationSms(ctx, &signup.GetDeviceRegistrationSmsRequest{Req: reqH})
	a.NoError(err, fmt.Sprintf("error in GetDeviceRegistrationSms: %v", err))
	a.Equal(rpc.StatusOk().Code, smsResp.GetStatus().Code,
		fmt.Sprintf("GetDeviceRegistrationSms resp status: %v", smsResp.GetStatus().String()),
	)
	var regResp *signup.RegisterDeviceResponse = nil
	registerDeviceRequest := &signup.RegisterDeviceRequest{
		Req:        reqH,
		SmsContent: smsResp.SmsContent,
		AndroidSimSubId: &types.NullInt32{
			Value: simId,
			Valid: true,
		},
	}

	// Simulator has failure logic to simulate real world. So retries are required here
	for i := 0; i < 7; i++ {
		// adding sleep, such that sms is acknowledged in the meantime.
		time.Sleep(5 * time.Second)
		regResp, err = sc.RegisterDevice(ctx, registerDeviceRequest)
		a.NoError(err, fmt.Sprintf("error in RegisterDevice: %v", err))
		if !regResp.GetRespHeader().GetStatus().IsSuccess() {
			logger.Info(ctx, fmt.Sprintf("Retrying device registration as response received: %v", regResp.String()))
		} else {
			break
		}
	}
	a.True(regResp.GetRespHeader().GetStatus().IsSuccess(),
		fmt.Sprintf("RegisterDevice resp status: %v", regResp.GetStatus().String()),
	)

	logger.Info(ctx, "resp for device reg", zap.String("resp", regResp.String()))

	// Fetch new access token after RegisterDevice
	logger.Info(ctx, "creating new token after successful device registration")
	_ = NewAccessTokenForUser(ctx, dep, user, reqH)

	return regResp
}

func CreateAccountSignup(ctx context.Context, a *require.Assertions, signupClient signup.SignupClient, reqH *header.RequestHeader, dep *OnbDep, aUser *UserData) *signup.CheckAccountSetupStatusResponse {
	setupResp, err := signupClient.AccountSetup(ctx, &signup.AccountSetupRequest{
		Req: reqH,
	})
	a.NoError(err)
	a.Equal(rpc.StatusOk().Code, setupResp.GetRespHeader().GetStatus().GetCode(),
		fmt.Sprintf("acct setup res: %v", setupResp.String()),
	)
	logger.Info(ctx, "resp for account setup", zap.String("resp", setupResp.String()))
	i := 0
	statusResp := &signup.CheckAccountSetupStatusResponse{}
	setupPercentage := 0
	isAccessTokenExpired := false
	for ; i < PollMaxRetries && setupPercentage != 100; i++ {
		statusResp, err = signupClient.CheckAccountSetupStatus(ctx, &signup.CheckAccountSetupStatusRequest{
			Req:      reqH,
			MetaBlob: setupResp.MetaBlob,
		})
		if err != nil {
			st, _ := status.FromError(err)
			isAccessTokenExpired = true
			a.Equal(codes.Unauthenticated, st.Code(), fmt.Sprintf("unexpected error %v", err))
			// create new token upon successful account creation
			logger.Info(ctx, "creating new token after successful account creation")
			_ = NewAccessTokenForUser(ctx, dep, aUser, reqH)
			time.Sleep(PollInterval)
			continue
		} else {
			a.NoError(err)
		}
		logger.Info(ctx, fmt.Sprintf("just print %v %v", statusResp, err))
		setupPercentage = int(statusResp.OnboardingProgressPercent)
		if setupPercentage != 100 {
			logger.Info(ctx, "Provisioning of customer, account and card in progress", zap.String("statusresp", statusResp.String()))
		} else {
			logger.Info(ctx, "Provisioning of customer, account and card successful", zap.String("statusresp", statusResp.String()))
		}
		time.Sleep(AccountCreationPollInterval)
	}
	if setupPercentage != 100 && statusResp.GetNextAction().GetScreen().String() == deeplink.Screen_CHECK_LIVENESS.String() {
		// to handle for liveness stage expiry
		lo := AssertLivenessNextAction(dep.Assert, statusResp.GetNextAction())
		CheckLivenessWithNextAction(
			ctx, dep.Assert, dep.SignupClient, reqH, lo.GetAttemptId(), aUser.Video,
			false, deeplink.Screen_ONBOARDING_SET_DEBIT_CARD_PIN,
		)
		_ = NewAccessTokenForUser(ctx, dep, aUser, reqH)
		return CreateAccountSignup(ctx, a, signupClient, reqH, dep, aUser)
	}
	a.Equal(100, setupPercentage, "Account setup status: %v%%; response: %v", setupPercentage, statusResp)
	a.NotEmpty(statusResp.GetAccountDetails().GetAccountId(), "savings a/c id is empty")
	a.NotEmpty(statusResp.GetAccountDetails().GetAccountNumber(), "card a/c number is empty")
	a.NotEmpty(statusResp.GetCardDetails().GetCardId(), "card id is empty")
	a.NotEmpty(statusResp.GetCardDetails().GetCardInfo().GetMaskedCardNumber(), "masked card number is empty")
	a.NotEmpty(statusResp.GetCardDetails().GetCardInfo().GetExpiry(), "card expiry is empty")
	a.NotEmpty(statusResp.GetCardDetails().GetCardInfo().GetName(), "card name is empty")
	a.NotEmpty(statusResp.GetCardDetails().GetPinSetTokenExpireAt())
	// Calling CheckAccountSetupStatus again after reaching 100% status to assert for access token expiry
	// Its possible that access token is expired and account setup reaches 100% in same RPC call for which we need to create a new access token
	// Below code handles this race condition
	if !isAccessTokenExpired {
		_, err = signupClient.CheckAccountSetupStatus(ctx, &signup.CheckAccountSetupStatusRequest{
			Req:      reqH,
			MetaBlob: setupResp.GetMetaBlob(),
		})

		st, _ := status.FromError(err)
		a.Equal(codes.Unauthenticated, st.Code(), fmt.Sprintf("unexpected error %v", err))
		// create new token upon successful account creation
		logger.Info(ctx, "creating new token after successful account creation")
		_ = NewAccessTokenForUser(ctx, dep, aUser, reqH)
	}
	return statusResp
}

func ConfirmAFU(
	ctx context.Context, a *require.Assertions, sc signup.SignupClient, reqH *header.RequestHeader, reqId, credBlock string) *signup.ConfirmAuthFactorUpdateResponse {
	resp, err := sc.ConfirmAuthFactorUpdate(ctx, &signup.ConfirmAuthFactorUpdateRequest{
		Req:                reqH,
		CredBlockRequestId: reqId,
		CredBlock:          credBlock,
	})
	a.NoError(err, fmt.Sprintf("error in confirm afu: %v", err))
	a.Equal(rpc.StatusOk().Code, resp.Status.Code, resp.GetStatus())
	return resp
}

func GetRequestHeaderWithExistingAccessToken(ctx context.Context, dep *OnbDep, aUser *UserData, accessToken string) *header.RequestHeader {
	return &header.RequestHeader{
		Auth: &header.AuthHeader{
			AuthToken:      &header.AuthHeader_AccessToken{AccessToken: accessToken},
			Device:         aUser.Device,
			SafetyNetToken: aUser.SafetyNetToken,
		},
		AppVersionCode: aUser.Device.GetAppVersion(),
		AppVersionName: "DummyVersionName",
		AppName:        commontypes.AppName_APP_NAME_FI_ORIGINAL,
	}
}

func GetAFUStatus(ctx context.Context, a *require.Assertions, sc signup.SignupClient,
	reqH *header.RequestHeader, afuId string) (*signup.CheckAuthFactorUpdateStatusResponse, error) {
	resp, err := sc.CheckAuthFactorUpdateStatus(ctx, &signup.CheckAuthFactorUpdateStatusRequest{
		Req:   reqH,
		AfuId: afuId,
	})
	if err != nil {
		return nil, err
	}
	a.NoError(err, fmt.Sprintf("error in CheckAuthFactorUpdateStatus : %v", err))
	return resp, nil
}

func GetCardDeliveryAddresses(ctx context.Context, a *require.Assertions, feUserClient user.UserClient, reqH *header.RequestHeader) *user.GetCardDeliveryAddressesResponse {
	resp, err := feUserClient.GetCardDeliveryAddresses(ctx, &user.GetCardDeliveryAddressesRequest{Req: reqH})
	a.NoError(err)
	a.Equal(rpc.StatusOk().Code, resp.Status.Code)

	numberOfAddresses := len(resp.AddressesWithType)
	for i := 0; i < numberOfAddresses; i++ {
		addressWithType := resp.AddressesWithType[i]
		a.Equal(IsValidAddress(addressWithType.Address), true)
	}
	return resp
}

func VerifyPinCodeDetails(ctx context.Context, a *require.Assertions, feUserClient user.UserClient, reqH *header.RequestHeader) {
	logger.Debug(ctx, "fetching city, state for pin code")

	resp, err := feUserClient.GetPinCodeAreas(ctx, &onboarding.GetPinCodeAreasRequest{
		Req:     reqH,
		PinCode: "122001",
	})
	expectedRes := &onboarding.GetPinCodeAreasResponse_Area{
		City:  "Gurgaon",
		State: "Haryana",
	}
	a.NoError(err)
	a.Equal(1, len(resp.Areas))
	a.Truef(proto.Equal(expectedRes, resp.Areas[0]), fmt.Sprintf("got %s, want: Gurgaon, Haryana", resp.Areas[0].String()))
}

func AddShippingAddress(ctx context.Context, a *require.Assertions, feUserClient user.UserClient, reqH *header.RequestHeader, opts ...interface{}) {
	logger.Debug(ctx, "adding shipping address")

	shippingAddress := &types.PostalAddress{
		RegionCode:         "India",
		PostalCode:         "560001",
		AdministrativeArea: "Karnataka",
		Locality:           "Bangalore",
		AddressLines:       []string{"23 Hudson Lane", "Near MG Road Metro Station"},
	}

	addressType := types.AddressType_MAILING
	if len(opts) > 0 {
		addressType = opts[0].(types.AddressType)
	}
	resp, err := feUserClient.AddAddress(ctx, &user.AddAddressRequest{
		Req:     reqH,
		Type:    addressType,
		Address: shippingAddress,
	})

	a.NoError(err)
	a.Equal(rpc.StatusOk().Code, resp.Status.Code)
}

func GetLegalName(ctx context.Context, a *require.Assertions, feUserClient user.UserClient, reqH *header.RequestHeader) *user.GetLegalNameResponse {
	resp, err := feUserClient.GetLegalName(ctx, &user.GetLegalNameRequest{Req: reqH})
	a.NoError(err)
	a.Equal(rpc.StatusOk().Code, resp.Status.Code)

	return resp
}

func ConfirmCardPreference(ctx context.Context, a *require.Assertions, feUserClient user.UserClient, reqH *header.RequestHeader,
	addressType types.AddressType, debitCardName *commontypes.Name) *user.ConfirmCardPreferencesResponse {

	resp, err := feUserClient.ConfirmCardPreferences(ctx, &user.ConfirmCardPreferencesRequest{
		Req:           reqH,
		DebitCardName: debitCardName,
		SkipNameCheck: false,
		AddressType:   addressType,
	})

	a.NoError(err)
	a.Equal(rpc.StatusOk().Code, resp.Status.Code)
	return resp
}

func NextAction(ctx context.Context, a *require.Assertions, client signup.SignupClient, reqH *header.RequestHeader) *deeplink.Deeplink {
	resp, err := client.GetNextOnboardingAction(ctx, &signup.GetNextOnboardingActionRequest{
		Req:               reqH,
		OnboardingFeature: onbPb.Feature_FEATURE_FI_LITE.String(),
	})
	a.NoError(err, fmt.Sprintf("error in next action: %v", err))
	a.Equal(rpc.StatusOk().Code, resp.Status.Code, fmt.Sprintf("next action res: %v", resp.String()))
	return resp.NextAction
}

func StartUserAction(ctx context.Context, client useractions.UserActionsClient, actorId string, actionGroup useractions.ActionGroup) error {
	resp, err := client.StartUserActions(ctx, &useractions.StartUserActionsRequest{
		ActorId:     actorId,
		ActionGroup: actionGroup,
		RequesterMetadata: &useractions.RequesterMetadata{
			Requester: "test-requester",
			Remarks:   "test-remarks",
		},
	})

	er := epifigrpc.RPCError(resp, err)

	return er
}

func UpdateAppAccessRevokeStatus(ctx context.Context, userClient userPb.UsersClient, actorId string, accessRevokeStatus userPb.AccessRevokeStatus, reason userPb.AccessRevokeReason, restoreReason userPb.AccessRestoreReason) error {
	userResp, err := userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})

	if er := epifigrpc.RPCError(userResp, err); er != nil {
		logger.Error(ctx, "error in get user in app access revoke", zap.Error(er))
		return er
	}
	logger.Debug(ctx, "fetched user for app access revoke")

	user := userResp.GetUser()
	user.AccessRevokeDetails = &userPb.AccessRevokeDetails{
		AccessRevokeStatus: accessRevokeStatus,
		Reason:             reason,
		RestoreReason:      restoreReason,
		Remarks:            "useractions acceptance test",
		UpdatedBy:          "test",
		UpdatedAt:          timestampPb.New(time.Now()),
	}

	userUpdResp, err := userClient.UpdateUser(ctx, &userPb.UpdateUserRequest{
		User: user,
		UpdateMask: []userPb.UserFieldMask{
			userPb.UserFieldMask_ACCESS_REVOKE_DETAILS,
		},
	})
	if er := epifigrpc.RPCError(userUpdResp, err); er != nil {
		logger.Error(ctx, "error in update user in app access revoke", zap.Error(er))
		return er
	}

	return nil
}

func NextActionWithOnbDep(ctx context.Context, dep *OnbDep, reqH *header.RequestHeader) *deeplink.Deeplink {
	return NextAction(ctx, dep.Assert, dep.SignupClient, reqH)
}

// PollNextAction polls next action till a non-polling screen is received and returns the non-polling next action.
// opts[0] = polling screens []deeplink.Screen
func PollNextAction(ctx context.Context, dep *OnbDep, reqH *header.RequestHeader, opts ...interface{}) *deeplink.Deeplink {
	return PollNextActionWithoutDeps(ctx, dep.Assert, dep.SignupClient, reqH, opts...)
}

func PollNextActionWithoutDeps(ctx context.Context, a *require.Assertions, client signup.SignupClient, reqH *header.RequestHeader, opts ...interface{}) *deeplink.Deeplink {
	var (
		nextAction  *deeplink.Deeplink
		pollScreens = []deeplink.Screen{deeplink.Screen_GET_NEXT_ONBOARDING_ACTION_API}
	)
	if len(opts) > 0 {
		customPollScreens := opts[0].([]deeplink.Screen)
		pollScreens = append(pollScreens, customPollScreens...)
	}

	for i := 0; i < PollMaxRetries; i++ {
		time.Sleep(PollInterval)
		nextAction = NextAction(ctx, a, client, reqH)

		// check if poll screen
		isPollScreen := false
		for _, pollScreen := range pollScreens {
			if nextAction.GetScreen() == pollScreen {
				isPollScreen = true
				break
			}
		}

		// break if not polling screen.
		if !isPollScreen {
			break
		}
	}
	return nextAction
}

func ScreenEquals(assert *require.Assertions, expected, actual deeplink.Screen) {
	assert.Equal(expected.String(), actual.String())
}

func AssertLivenessNextAction(a *require.Assertions, nextAction *deeplink.Deeplink) *deeplink.CheckLivenessScreenOptions {
	ScreenEquals(a, deeplink.Screen_CHECK_LIVENESS, nextAction.GetScreen())
	switch req := nextAction.GetScreenOptions().(type) {
	case *deeplink.Deeplink_CheckLivenessScreenOptions:
		return req.CheckLivenessScreenOptions
	default:
		a.Fail(fmt.Sprintf("expected action CHECK_LIVENESS options, got %v", req))
	}
	return nil
}

func WaitForLivenessNextAction(ctx context.Context, deps *OnbDep, reqH *header.RequestHeader, nextAction *deeplink.Deeplink) *deeplink.CheckLivenessScreenOptions {
	if nextAction == nil || nextAction.GetScreen() == deeplink.Screen_GET_NEXT_ONBOARDING_ACTION_API {
		nextAction = PollNextAction(ctx, deps, reqH)
	}
	return AssertLivenessNextAction(deps.Assert, nextAction)
}

func GetKYCRecord(ctx context.Context, a *require.Assertions, client signup.SignupClient, reqH *header.RequestHeader) *signup.GetKYCRecordResponse {
	resp, err := client.GetKYCRecord(ctx, &signup.GetKYCRecordRequest{
		Req: reqH,
	})
	a.NoError(err)
	a.Equal(rpc.StatusOk().Code, resp.Status.Code)
	a.NotNil(resp.GetRecord())
	a.NotEmpty(resp.GetSubHeading())
	return resp
}

func AssertAuthTokenExpired(ctx context.Context, a *require.Assertions, client signup.SignupClient, reqH *header.RequestHeader) {
	res, err := client.GetNextOnboardingAction(ctx, &signup.GetNextOnboardingActionRequest{
		Req: reqH,
	})
	a.Error(err)
	st, _ := status.FromError(err)
	a.Equal(codes.Unauthenticated, st.Code(), epifigrpc.RPCError(res, err).Error())
}

func InitiateEKYC(ctx context.Context, dep *OnbDep, reqH *header.RequestHeader, ekycSource ...string) {
	ekycSourceStr := ""
	if len(ekycSource) != 0 {
		ekycSourceStr = ekycSource[0]
	}
	res, err := dep.SignupClient.InitiateEKYC(ctx, &signup.InitiateEKYCRequest{
		Req:           reqH,
		EkycSourceStr: ekycSourceStr,
	})
	dep.Assert.NoError(err, fmt.Sprintf("error in ekyc: %v", err))
	dep.Assert.NotNil(res.GetStatus())
	dep.Assert.Equal(rpc.StatusOk().Code, res.Status.Code, fmt.Sprintf("ekyc res: %v", res.String()))
	dep.Assert.NotEmpty(res.GetSecurityToken())
}

func UploadEKYCRecord(ctx context.Context, dep *OnbDep, reqH *header.RequestHeader, photo []byte, userData *UserData) *signup.UploadEKYCRecordResponse {
	txnId := uuid.New().String()[:29]
	ekycSimDao := ekycDao.NewEkycStorage(dep.SimDB)
	_, err := ekycSimDao.Create(&model.Ekyc{
		EkycRrn: txnId,
	})
	if err != nil {
		logger.Error(ctx, "error in creating ekyc entry in simulator", zap.Error(err))
		dep.Assert.Nil(err, "error in adding simulator ekycs entry: %v", err)
	}
	if photo == nil {
		photo = []byte{2, 2, 2}
	}
	maskedEKYCPhoneNumber := ""
	name := "Byomkesh Bakshi"
	if userData != nil {
		if userData.MaskedEKYCNumber != "" {
			maskedEKYCPhoneNumber = userData.MaskedEKYCNumber
		}
		if userData.Pan == "**********" {
			name = "Big Name Boy Byomkesh Bakshi"
		}
	}
	res2, errResp := dep.SignupClient.UploadEKYCRecord(ctx, &signup.UploadEKYCRecordRequest{
		Req: reqH,
		Payload: &signup.EKYCResponse{
			Status:        "Y",
			TransactionId: txnId,
			Record: &signup.EKYCRecord{
				Pincode:    "201012",
				Postoffice: "",
				Gender:     "F",
				Locality:   "Marathalli",
				Vtcname:    "",
				Photo:      base64.StdEncoding.EncodeToString(photo),
				Careof:     "",
				Phone:      "1231231231",
				Dob:        "18-01-1998",
				Street:     "Shaitan Gali",
				District:   "BANGALORE",
				Houseno:    "420",
				State:      "KARNATAKA",
				Landmark:   "",
				Email:      "",
				Name:       name,
			},
		},
		MaskedRegisteredPhoneNumber: maskedEKYCPhoneNumber,
	})
	fmt.Printf("upload ekyc response: %v", res2.String())
	dep.Assert.NoError(errResp, fmt.Sprintf("error in upload ekyc: %v", errResp))
	dep.Assert.NotNil(res2.GetStatus())
	dep.Assert.Equal(rpc.StatusOk().GetCode(), res2.GetStatus().GetCode(), fmt.Sprintf("upload ekyc res: %v", res2.String()))
	return res2
}

func IsValidAddress(address *types.PostalAddress) bool {
	isValidEntity := func(entity string, value string) bool {
		value = strings.ToUpper(value)
		if value == "NOT AVAILABLE" || value == "NA" || value == "N/A" || value == "" {
			logger.Warn("invalid value for entity: ", zap.String("entity", entity), zap.String("value", value))
			return false
		}
		return true
	}

	if !isValidEntity("RegionCode", address.RegionCode) || !isValidEntity("AdministrativeArea", address.AdministrativeArea) ||
		!isValidEntity("PostalCode", address.PostalCode) {
		return false
	}
	if len(address.AddressLines) == 0 || !isValidEntity("0th AddressLine", address.AddressLines[0]) {
		return false
	}
	if !isValidEntity("Locality", address.Locality) && !isValidEntity("Sublocality", address.Sublocality) {
		return false
	}
	return true
}

// TODO (keerthana): Remove this and add entry to simulator directly
func AddCustomerToSimulator(ctx context.Context, a *require.Assertions, simDb *gormv2.DB, vgClient vgPbCustomer.CustomerClient, data *UserData) {
	// Preparing duplicate customer in simulator
	ekycRRN := idgen.RandAlphaNumericString(10)
	customer := &vgPbCustomer.CreateCustomerRequest{
		CustomerCreationFlow: vgPbCustomer.CustomerCreationFlow_CUSTOMER_CREATION_FLOW_MIN_EKYC,
		Header:               &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
		RequestId:            "u4958793",
		Name:                 data.Name,
		SolId:                "1001",
		DateOfBirth: &date.Date{
			Year:  1996,
			Month: 3,
			Day:   17,
		},
		PermanentAddress: &postaladdress.PostalAddress{
			Revision:           0,
			RegionCode:         "",
			LanguageCode:       "",
			PostalCode:         "",
			SortingCode:        "",
			AdministrativeArea: "",
			Locality:           "",
			Sublocality:        "",
			AddressLines:       []string{"addressline1", "addressline2"},
			Recipients:         nil,
			Organization:       "",
		},
		CurrentAddress: &postaladdress.PostalAddress{
			Revision:           0,
			RegionCode:         "",
			LanguageCode:       "",
			PostalCode:         "",
			SortingCode:        "",
			AdministrativeArea: "",
			Locality:           "",
			Sublocality:        "",
			AddressLines:       []string{"addressline1", "addressline2"},
			Recipients:         nil,
			Organization:       "",
		},
		Gender:      1,
		PhoneNumber: data.Phone,
		PanNumber:   data.Pan,
		Email:       "<EMAIL>",
		FatherName:  "shanmugam",
		MotherName:  "renuka",
		IdentityProof: &vgPbCustomer.ProofDetails{
			Type:     1,
			IdNumber: "kfshfkds",
			IdIssueDate: &date.Date{
				Year:  1999,
				Month: 10,
				Day:   10,
			},
			IdExpiryDate: &date.Date{
				Year:  2099,
				Month: 10,
				Day:   10,
			},
		},
		AddressProof: &vgPbCustomer.ProofDetails{
			Type:     1,
			IdNumber: "kfshfkds",
			IdIssueDate: &date.Date{
				Year:  1999,
				Month: 10,
				Day:   10,
			},
			IdExpiryDate: &date.Date{
				Year:  2099,
				Month: 10,
				Day:   10,
			},
		},
		DeviceDetails: &header2.Auth{
			DeviceId:      "829502458",
			DeviceToken:   "DeviceToken83495",
			EncryptedPin:  "",
			UserProfileId: "42950389",
			CustomerId:    "123456",
		},
		UidNo: ekycRRN,
	}
	ekycSimDao := ekycDao.NewEkycStorage(simDb)
	_, err := ekycSimDao.Create(&model.Ekyc{
		EkycRrn: ekycRRN,
		StageStatus: &model.StageStatus{
			IsNameDOBValidationDone:       true,
			IsAadhaarMobileValidationDone: true,
		},
	})
	if err != nil {
		logger.Error(ctx, "error in creating ekyc entry in simulator", zap.Error(err))
		a.Nil(err, "error in adding simulator ekycs entry: %v", err)
	}
	resp, err := vgClient.CreateCustomer(ctx, customer)
	a.Nil(epifigrpc.RPCError(resp, err))
}

func CheckIfCustomerWasDedupe(ctx context.Context, a *require.Assertions, userClient userPb.UsersClient, bcClient bankCustPb.BankCustomerServiceClient,
	phoneNumber *commontypes.PhoneNumber) {

	userResp, errResp := userClient.GetUser(ctx,
		&userPb.GetUserRequest{
			Identifier: &userPb.GetUserRequest_PhoneNumber{PhoneNumber: phoneNumber}})

	a.Nil(epifigrpc.RPCError(userResp, errResp))

	bcResp, errResp := bcClient.GetBankCustomer(ctx, &bankCustPb.GetBankCustomerRequest{
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankCustPb.GetBankCustomerRequest_UserId{
			UserId: userResp.GetUser().GetId(),
		},
	})

	a.Nil(epifigrpc.RPCError(bcResp, errResp))

	a.Equal(bankCustPb.CustomerCreationState_CUSTOMER_CREATION_STATE_DEDUPE_CUSTOMER, bcResp.GetBankCustomer().GetVendorMetadata().GetFederalMetadata().GetCustomerCreationState())
	a.NotNil(bcResp.GetBankCustomer().GetDedupeInfo().GetVendorCustomerId())
	a.NotNil(bcResp.GetBankCustomer().GetName())
	a.NotEqual(bcResp.GetBankCustomer().GetDedupeInfo().GetOriginalKycLevelWithVendor(), kyc.KYCLevel_UNSPECIFIED)
}

func SignOut(ctx context.Context, dep *OnbDep, reqH *header.RequestHeader) {
	signOutRes, err := dep.SignupClient.SignOut(ctx, &signup.SignOutRequest{
		Req: reqH,
	})
	dep.Assert.Nil(err, fmt.Sprintf("error in signout: %v", err))
	dep.Assert.Equal(rpc.StatusOk().Code, signOutRes.Status.Code, fmt.Sprintf("signout res: %v", signOutRes))
}

func AccountSetup(ctx context.Context, dep *OnbDep, reqH *header.RequestHeader, action *deeplink.Deeplink, userData *UserData) (*deeplink.Deeplink, *signup.CheckAccountSetupStatusResponse) {
	var (
		nextAction *deeplink.Deeplink
		acctRes    *signup.CheckAccountSetupStatusResponse
	)
	opts := action.GetScreenOptions().(*deeplink.Deeplink_SavingsAccountSetupProgress)
	step := opts.SavingsAccountSetupProgress.Step

	switch step {
	case deeplink.SavingsAccountSetupProgress_REGISTER_DEVICE:
		RegisterDevice(ctx, dep.Assert, dep.SignupClient, reqH, false, userData.SimSubIds[0], dep, userData)
		nextAction = NextAction(ctx, dep.Assert, dep.SignupClient, reqH)
		ScreenEquals(dep.Assert, deeplink.Screen_SAVINGS_ACCOUNT_SETUP_PROGRESS, nextAction.GetScreen())
		nextStep := nextAction.ScreenOptions.(*deeplink.Deeplink_SavingsAccountSetupProgress).SavingsAccountSetupProgress.Step
		dep.Assert.Equal(deeplink.SavingsAccountSetupProgress_ACCOUNT_SETUP, nextStep)

	case deeplink.SavingsAccountSetupProgress_ACCOUNT_SETUP:
		acctRes = CreateAccountSignup(ctx, dep.Assert, dep.SignupClient, reqH, dep, userData)
		ScreenEquals(dep.Assert, deeplink.Screen_ONBOARDING_SET_DEBIT_CARD_PIN, acctRes.GetNextAction().GetScreen())
		nextActionOptions := acctRes.GetNextAction().ScreenOptions.(*deeplink.Deeplink_DebitCardPinSetOptions).DebitCardPinSetOptions
		AssertDebitCardPinSetOptions(dep.Assert, nextActionOptions)
		nextAction = NextAction(ctx, dep.Assert, dep.SignupClient, reqH)
		ScreenEquals(dep.Assert, deeplink.Screen_ONBOARDING_SET_DEBIT_CARD_PIN, nextAction.GetScreen())
		cardPinOptions := nextAction.ScreenOptions.(*deeplink.Deeplink_DebitCardPinSetOptions).DebitCardPinSetOptions
		AssertDebitCardPinSetOptions(dep.Assert, cardPinOptions)
		dep.Assert.Equal(acctRes.GetAccountDetails().GetAccountId(), cardPinOptions.AccountId)
		dep.Assert.Equal(acctRes.GetAccountDetails().GetAccountNumber(), cardPinOptions.AccountNumber)
		dep.Assert.Equal(acctRes.GetCardDetails().GetCardId(), cardPinOptions.CardId)

	default:
		dep.Assert.Failf("invalid SavingsAccountSetupProgress_Step: %v", step.String())
	}

	return nextAction, acctRes
}

// AssertDebitCardPinSetOptions method helps in checking required parameters for debit card pin set screen options
func AssertDebitCardPinSetOptions(a *require.Assertions, cardPinOptions *deeplink.DebitCardPinSetScreenOptions) {
	a.NotEmpty(cardPinOptions.AccountId, "card a/c id is empty")
	a.NotEmpty(cardPinOptions.AccountNumber, "card a/c number is empty")
	a.NotEmpty(cardPinOptions.CardId, "card id is empty")
	a.NotEmpty(cardPinOptions.MaskedCardNumber, "masked card number is empty")
	a.NotEmpty(cardPinOptions.Expiry, "card expiry is empty")
	a.NotEmpty(cardPinOptions.Name, "card name is empty")
	a.NotEmpty(cardPinOptions.GetPinSetTokenExpireAt())
}

// DoOnboardingFromAction is a convenience method that takes in the auth header & action to
// perform onboarding step by step till the debit card pin set up. This method helps in
// avoiding redundant assertion of onboarding steps.
// nolint: funlen
func DoOnboardingFromAction(ctx context.Context, dep *OnbDep, reqH *header.RequestHeader, fromAction *deeplink.Deeplink, userData *UserData, metadata ...OnbActionMetadata) *deeplink.Deeplink {
	var nextAction *deeplink.Deeplink
	switch fromAction.GetScreen() {
	case deeplink.Screen_HOME:
		nextAction = fromAction
	case deeplink.Screen_CHECK_LIVENESS:
		lo := AssertLivenessNextAction(dep.Assert, fromAction)
		// Do liveness and assert next action after liveness to be EKYC
		CheckLivenessVideo(ctx, dep.Assert, dep.SignupClient, reqH, userData.Video, lo.GetAttemptId())
		nextAction = PollNextAction(ctx, dep, reqH, []deeplink.Screen{deeplink.Screen_CHECK_LIVENESS, deeplink.Screen_GET_NEXT_ONBOARDING_ACTION_API})
	case deeplink.Screen_CONFIRM_CARD_MAILING_ADDRESS:
		deliveryAddresses := GetCardDeliveryAddresses(ctx, dep.Assert, dep.UserClient, reqH)
		legalNameResp := GetLegalName(ctx, dep.Assert, dep.UserClient, reqH)
		numOfAddresses := len(deliveryAddresses.AddressesWithType)

		VerifyPinCodeDetails(ctx, dep.Assert, dep.UserClient, reqH)

		// setting default address type as MAILING
		addressType := types.AddressType_MAILING
		if len(metadata) > 0 {
			addressType = metadata[0].ShippingAddressType
		}
		// address being manually added by the user for shipping. This address is saved in DB with type 'SHIPPING'
		AddShippingAddress(ctx, dep.Assert, dep.UserClient, reqH, addressType)

		// GetCardDeliveryAddresses is called again to verify if the manually added shipping address is displayed to the user or not
		deliveryAddresses = GetCardDeliveryAddresses(ctx, dep.Assert, dep.UserClient, reqH)
		numOfAddressesWithShipping := len(deliveryAddresses.AddressesWithType)
		if len(metadata) > 0 {
			if metadata[0].ShippingAddressType == types.AddressType_SHIPPING {
				dep.Assert.Equal(numOfAddresses+1, numOfAddressesWithShipping)
			}
		} else {
			dep.Assert.Equal(numOfAddresses, numOfAddressesWithShipping)
		}
		cardPreferencesResp := ConfirmCardPreference(ctx, dep.Assert, dep.UserClient, reqH, types.AddressType_SHIPPING, legalNameResp.GetLegalName())
		nextAction = cardPreferencesResp.GetNextAction()
		dep.Assert.Equal(deeplink.Screen_CREATE_ACCOUNT.String(), nextAction.GetScreen().String())

	case deeplink.Screen_CREATE_ACCOUNT:
		RegisterDevice(ctx, dep.Assert, dep.SignupClient, reqH, false, userData.SimSubIds[0], dep, userData)
		nextAction = NextAction(ctx, dep.Assert, dep.SignupClient, reqH)
		ScreenEquals(dep.Assert, deeplink.Screen_SAVINGS_ACCOUNT_SETUP_PROGRESS, nextAction.GetScreen())
		nextStep := nextAction.ScreenOptions.(*deeplink.Deeplink_SavingsAccountSetupProgress).SavingsAccountSetupProgress.Step
		dep.Assert.Equal(deeplink.SavingsAccountSetupProgress_ACCOUNT_SETUP, nextStep)

	case deeplink.Screen_SAVINGS_ACCOUNT_SETUP_PROGRESS:
		var acctRes *signup.CheckAccountSetupStatusResponse
		nextAction, acctRes = AccountSetup(ctx, dep, reqH, fromAction, userData)

		// performing set debit card pin here as there is a dependency on previous action
		// This will be fixed after https://monorail.pointz.in/p/fi-app/issues/detail?id=3082 is fixed
		if acctRes != nil {
			cardPinRes := SetCardPin(ctx, dep.Assert, dep.CardClient, reqH, acctRes.GetCardDetails().GetCardId(), "", card.UIEntryPoint_ONBOARDING)
			nextAction = cardPinRes.GetNextAction()
			ScreenEquals(dep.Assert, deeplink.Screen_DEBIT_CARD_PIN_SETUP_STATUS, nextAction.GetScreen())

			// debit pin card setup status
			pinSetupStatusResp := CardPinSetUpStatus(ctx, dep.Assert, dep.CardClient, reqH, acctRes.GetCardDetails().GetCardId())
			nextAction = pinSetupStatusResp.GetNextAction()
			nextAction = NextAction(ctx, dep.Assert, dep.SignupClient, reqH)
		}

	case deeplink.Screen_ONBOARDING_SET_DEBIT_CARD_PIN:
		// TODO(anand): (monorail #3082) update once an API for Card ID is integrated with client
		dep.Assert.Fail("Screen_ONBOARDING_SET_DEBIT_CARD_PIN is unsupported")

	case deeplink.Screen_DEBIT_CARD_PIN_SETUP_STATUS:
		// TODO(anand): integrate this step in onboarding service
		dep.Assert.Fail("Screen_ONBOARDING_SET_DEBIT_CARD_PIN is unsupported")

	case deeplink.Screen_ONBOARDING_ADD_MONEY:
		// Not evaluating this case for all as this step takes considerable time.
		// It is tested in TestOnboardingE2E in sanity_test.go
		nextAction = fromAction

	case deeplink.Screen_GET_NEXT_ONBOARDING_ACTION_API:
		nextAction = PollNextAction(ctx, dep, reqH)
		if nextAction.GetScreen() == deeplink.Screen_GET_NEXT_ONBOARDING_ACTION_API {
			dep.Assert.Fail("Stuck on GET_NEXT_ONBOARDING_ACTION_API screen")
			return nextAction
		}

	case deeplink.Screen_INFO_ACKNOWLEDGEMENT_SCREEN:
		screenOptions := fromAction.GetScreenOptions().(*deeplink.Deeplink_InfoAcknowledgementScreenOptions)
		if len(screenOptions.InfoAcknowledgementScreenOptions.GetCtas()) > 0 &&
			screenOptions.InfoAcknowledgementScreenOptions.GetCtas()[0].GetDeeplink().GetScreen() == deeplink.Screen_ONBOARDING_ADD_MONEY {
			return screenOptions.InfoAcknowledgementScreenOptions.GetCtas()[0].GetDeeplink()
		}

	case deeplink.Screen_GENERIC_RECORD_CONSENT:
		RecordConsent(ctx, dep.Assert, dep.ConsentClient, reqH, []string{consentPb.ConsentType_SECURE_USAGE_GUIDELINES.String()})
		nextAction = NextAction(ctx, dep.Assert, dep.SignupClient, reqH)

	default:
		dep.Assert.Fail(fmt.Sprintf("invalid action received in DoOnboardingFromAction: %v", fromAction.String()))
	}
	if nextAction.GetScreen() == deeplink.Screen_ONBOARDING_ADD_MONEY ||
		nextAction.GetScreen() == deeplink.Screen_HOME {
		return nextAction
	}
	logger.InfoNoCtx(fmt.Sprintf(
		"DoOnboardingFromAction %v -> %v", fromAction.GetScreen().String(), nextAction.GetScreen().String()),
		zap.String("fromAction", fromAction.String()), zap.String("nextAction", nextAction.String()),
	)
	return DoOnboardingFromAction(ctx, dep, reqH, nextAction, userData)
}

func GetUserDetails(ctx context.Context, dep *OnbDep, ph *commontypes.PhoneNumber) (*types.Actor, *userPb.User) {
	getUserRes, err := dep.UserBEClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_PhoneNumber{
			PhoneNumber: ph,
		},
	})
	dep.Assert.Nil(err, "error in get user: %v", err)
	dep.Assert.Equal(rpc.StatusOk().Code, getUserRes.GetStatus().GetCode(),
		"non-ok status in get user: %v", getUserRes.GetStatus())
	dep.Assert.NotNil(getUserRes.GetUser())

	getActorRes, err := dep.ActorClient.GetActorByEntityId(ctx, &actorPb.GetActorByEntityIdRequest{
		Type:     types.Actor_USER,
		EntityId: getUserRes.GetUser().GetId(),
	})
	dep.Assert.Nil(err, "error in get actor: %v", err)
	dep.Assert.Equal(rpc.StatusOk().Code, getActorRes.GetStatus().GetCode(),
		"non-ok status in get actor: %v", getActorRes.GetStatus())
	dep.Assert.NotNil(getActorRes.GetActor())

	return getActorRes.GetActor(), getUserRes.GetUser()
}

func GetUserDeviceProperties(ctx context.Context, dep *OnbDep, actorId string) *userPb.GetUserDevicePropertiesResponse {
	getUserDevicePropertiesRes, err := dep.UserBEClient.GetUserDeviceProperties(ctx, &userPb.GetUserDevicePropertiesRequest{
		ActorId: actorId,
		PropertyTypes: []types.DeviceProperty{
			types.DeviceProperty_DEVICE_PROP_DEVICE_MODEL_INFO,
			types.DeviceProperty_DEVICE_PROP_DEVICE_ID,
			types.DeviceProperty_DEVICE_PROP_APP_VERSION_INFO,
		},
	})
	dep.Assert.Nil(err, "error in get user device properties: %v", err)
	dep.Assert.Equal(rpc.StatusOk().Code, getUserDevicePropertiesRes.GetStatus().GetCode(),
		"non-ok status in get user device properties: %v", getUserDevicePropertiesRes.GetStatus())
	dep.Assert.NotNil(getUserDevicePropertiesRes.GetUserDevicePropertyList())

	return getUserDevicePropertiesRes
}

func GetUserDeviceAuth(ctx context.Context, dep *OnbDep, actorId string) *authPb.GetDeviceAuthResponse {
	getDeviceAuthRes, err := dep.AuthClient.GetDeviceAuth(ctx, &authPb.GetDeviceAuthRequest{
		ActorId: actorId,
	})
	dep.Assert.Nil(err, "error in get user device auth: %v", err)
	dep.Assert.Equal(rpc.StatusOk().Code, getDeviceAuthRes.GetStatus().GetCode(),
		"non-ok status in get user device auth: %v", getDeviceAuthRes.GetStatus())
	dep.Assert.NotNil(getDeviceAuthRes.GetSimId(), "device sim id is nil")

	return getDeviceAuthRes
}

func GetUserLatestAccessToken(ctx context.Context, dep *OnbDep, actorId string) string {
	tokenStoresPgdb := dao.NewTokenStoresPgdb(dep.DBConns[config.AuthDb], nil)
	token, err := tokenStoresPgdb.GetLatestTokenByActorIdAndTokenType(ctx, actorId, authPb.TokenType_ACCESS_TOKEN, false)
	dep.Assert.Nil(err, "error in get latest token by actor id and token type: %v", err)
	dep.Assert.NotNil(token.Token)

	return token.Token
}

func ResetUser(ctx context.Context, dep *OnbDep, onbClient onbPb.OnboardingClient, actorId string) {
	res, err := onbClient.ResetUser(ctx, &onbPb.ResetUserRequest{
		ActorId: actorId,
		DeletionDetails: &userPb.DeletionDetails{
			DeletionReason: userPb.DeletionDetails_DELETION_REASON_SCRIPT,
		},
	})
	dep.Assert.Nil(err, "error in reset user: %v", err)
	dep.Assert.Equal(rpc.StatusOk().Code, res.GetStatus().GetCode(),
		"non-ok status in reset user: %v", res.GetStatus())
}

// DoEKYC returns user with ekyc data uploaded
func DoEKYC(ctx context.Context, dep *OnbDep, userData *UserData) (*header.RequestHeader, *deeplink.Deeplink) {
	reqH, _ := NewUserWithScreenerPassed(ctx, dep, userData)

	// save parents names and nominee details
	nextAction := CollectParentsName(ctx, dep, userData, reqH)

	nextAction = PollNextAction(ctx, dep, reqH)
	ScreenEquals(dep.Assert, deeplink.Screen_START_EKYC, nextAction.GetScreen())

	// assert kyc level
	_, ok := nextAction.ScreenOptions.(*deeplink.Deeplink_StartEkycOptions)
	dep.Assert.True(ok)

	// Do EKYC
	InitiateEKYC(ctx, dep, reqH)
	_ = UploadEKYCRecord(ctx, dep, reqH, nil, userData)
	return reqH, PollNextAction(ctx, dep, reqH)
}

// DoEKYCAndLiveness returns user with ekyc data uploaded and liveness done
func DoEKYCAndLiveness(ctx context.Context, dep *OnbDep, userData *UserData) (*header.RequestHeader, *deeplink.Deeplink) {
	reqH, nextAction := DoEKYC(ctx, dep, userData)
	nextAction = PollNextActionWithoutDeps(ctx, dep.Assert, dep.SignupClient, reqH)

	lo := WaitForLivenessNextAction(ctx, dep, reqH, nextAction)
	// Do liveness and assert next action after liveness to be EKYC
	CheckLivenessVideo(ctx, dep.Assert, dep.SignupClient, reqH, userData.Video, lo.GetAttemptId())
	return reqH, PollNextAction(ctx, dep, reqH, []deeplink.Screen{deeplink.Screen_CHECK_LIVENESS, deeplink.Screen_GET_NEXT_ONBOARDING_ACTION_API})
}

// DoEKYCAndLiveness returns user with ekyc data uploaded and liveness done
func DoEKYCAndLivenessAfterExpiry(ctx context.Context, dep *OnbDep, userData *UserData, reqH *header.RequestHeader) (*header.RequestHeader, *deeplink.Deeplink) {
	// Do EKYC
	_, nextAction := PerformEKYCWithoutOnb(ctx, dep, userData, reqH)

	lo := WaitForLivenessNextAction(ctx, dep, reqH, nextAction)
	// Do liveness and assert next action after liveness to be EKYC
	CheckLivenessVideo(ctx, dep.Assert, dep.SignupClient, reqH, userData.Video, lo.GetAttemptId())
	return reqH, PollNextAction(ctx, dep, reqH, []deeplink.Screen{deeplink.Screen_CHECK_LIVENESS, deeplink.Screen_GET_NEXT_ONBOARDING_ACTION_API})
}

func PerformEKYCWithoutOnb(ctx context.Context, dep *OnbDep, userData *UserData, reqH *header.RequestHeader) (*header.RequestHeader, *deeplink.Deeplink) {
	nextAction := PollNextAction(ctx, dep, reqH)
	ScreenEquals(dep.Assert, deeplink.Screen_START_EKYC, nextAction.GetScreen())

	// assert kyc level
	_, ok := nextAction.ScreenOptions.(*deeplink.Deeplink_StartEkycOptions)
	dep.Assert.True(ok)

	// Do EKYC
	InitiateEKYC(ctx, dep, reqH)
	UploadEKYCRecord(ctx, dep, reqH, nil, userData)
	nextAction = PollNextAction(ctx, dep, reqH)
	return reqH, nextAction
}

func UploadEpan(ctx context.Context, dep *OnbDep, reqH *header.RequestHeader, uploadEpanScreenOptions *docs2.UploadFileScreenOptions, pan string) *deeplink.Deeplink {
	byteData, _ := base64.StdEncoding.DecodeString("/9j/4AAQSkZJ")

	uploadEpan, err := dep.FeDocsClient.UploadFile(ctx, &docs.UploadFileRequest{
		Req:         reqH,
		Data:        byteData,
		Password:    pan,
		ClientReqId: uploadEpanScreenOptions.GetClientRequestId(),
		Blob:        uploadEpanScreenOptions.GetBlob(),
	})
	dep.Assert.NoError(err)
	dep.Assert.Equal(rpc.StatusOk().GetCode(), uploadEpan.GetRespHeader().GetStatus().GetCode())
	return uploadEpan.GetNextAction()
}

// nolint: funlen
func PerformVKYCWithoutOnboardingUser(ctx context.Context, dep *OnbDep, reqH *header.RequestHeader, entryPoint string, userData *UserData, actorId string, simDb *gormv2.DB) (*header.RequestHeader, *deeplink.Deeplink) {
	ekycRrn, err := GetEkycRrn(ctx, dep, actorId)
	dep.Assert.NoError(err)
	dep.Assert.NotEmpty(ekycRrn, "ekyc rrn should not be empty")
	simDb.Exec("insert into ekycs (ekyc_rrn) values (?)", ekycRrn)
	vkycStatusResp, _ := dep.FeVKYCClient.GetVKYCStatus(ctx, &fevkycpb.GetVKYCStatusRequest{
		Req:           reqH,
		EntryPointStr: entryPoint,
	})
	ScreenEquals(dep.Assert, deeplink.Screen_VKYC_INSTRUCTIONS_V2, vkycStatusResp.GetDeeplink().GetScreen())

	vkycNextActionResp, err := dep.FeVKYCClient.GetVKYCNextAction(ctx, &fevkycpb.GetVKYCNextActionRequest{
		Req:             reqH,
		ClientLastState: beVkycPb.VKYCClientState_VKYC_CLIENT_STATE_INSTRUCTIONS.String(),
		EntryPoint:      entryPoint,
	})
	dep.Assert.NoError(err)
	dep.Assert.Equal(rpc.StatusOk().GetCode(), vkycNextActionResp.GetRespHeader().GetStatus().GetCode())
	ScreenEquals(dep.Assert, deeplink.Screen_VKYC_INSTRUCTIONS_OVERLAY, vkycNextActionResp.GetNextAction().GetScreen())

	screenOptions := &vkyc2.VkycInstructionsOverlayScreenOptions{}
	err = anypb.UnmarshalTo(vkycNextActionResp.GetNextAction().GetScreenOptionsV2(), screenOptions, proto.UnmarshalOptions{})
	dep.Assert.NoError(err)
	fmt.Println(vkycStatusResp, screenOptions)
	dep.Assert.Equal(beVkycPb.VKYCClientState_VKYC_CLIENT_STATE_EPAN_SELECTION.String(), screenOptions.GetOverlayPageBlocks()[1].GetCtas()[0].GetDeeplink().GetGetVkycNextActionApiScreenOptions().GetClientLastState())

	vkycNextActionResp, err = dep.FeVKYCClient.GetVKYCNextAction(ctx, &fevkycpb.GetVKYCNextActionRequest{
		Req:             reqH,
		ClientLastState: beVkycPb.VKYCClientState_VKYC_CLIENT_STATE_EPAN_SELECTION.String(),
		EntryPoint:      entryPoint,
	})
	dep.Assert.NoError(err)
	dep.Assert.Equal(rpc.StatusOk().GetCode(), vkycNextActionResp.GetRespHeader().GetStatus().GetCode())
	dep.Assert.Equal(deeplink.Screen_UPLOAD_FILE.String(), vkycNextActionResp.GetNextAction().GetScreen().String())
	uploadEpanScreenOptions := &docs2.UploadFileScreenOptions{}
	err = anypb.UnmarshalTo(vkycNextActionResp.GetNextAction().GetScreenOptionsV2(), uploadEpanScreenOptions, proto.UnmarshalOptions{})
	dep.Assert.NoError(err)
	fmt.Println(vkycNextActionResp, uploadEpanScreenOptions)

	nextAction := UploadEpan(ctx, dep, reqH, uploadEpanScreenOptions, userData.Pan)

	dep.Assert.Equal(deeplink.Screen_GET_VKYC_NEXT_ACTION_API.String(), nextAction.GetScreen().String())
	dep.Assert.Equal(beVkycPb.VKYCClientState_VKYC_CLIENT_STATE_EPAN_ENTRY.String(), nextAction.GetGetVkycNextActionApiScreenOptions().GetClientLastState())

	vkycNextActionResp, err = dep.FeVKYCClient.GetVKYCNextAction(ctx, &fevkycpb.GetVKYCNextActionRequest{
		Req:             reqH,
		ClientLastState: beVkycPb.VKYCClientState_VKYC_CLIENT_STATE_EPAN_ENTRY.String(),
		EntryPoint:      entryPoint,
	})
	dep.Assert.NoError(fepkg.FeRPCError(vkycNextActionResp, err))
	ScreenEquals(dep.Assert, deeplink.Screen_USER_DETAILS_FORM, vkycNextActionResp.GetNextAction().GetScreen())

	updateFormDetailsResp, err := dep.UserClient.UpdateFormDetails(ctx, &user.UpdateFormDetailsRequest{
		Req:    reqH,
		Source: formPkg.UpdateUserDetailsFlow_UPDATE_USER_DETAILS_FLOW_CONFIRM_VKYC_DETAILS.String(),
	})
	dep.Assert.NoError(fepkg.FeRPCError(updateFormDetailsResp, err))
	userDetailsFormScreenOptions := &formPkg.UserDetailsFormScreenOptions{}
	err = anypb.UnmarshalTo(vkycNextActionResp.GetNextAction().GetScreenOptionsV2(), userDetailsFormScreenOptions, proto.UnmarshalOptions{})
	dep.Assert.NoError(err)
	dep.Assert.Equal(beVkycPb.VKYCClientState_VKYC_CLIENT_STATE_CONFIRM_DETAILS.String(), userDetailsFormScreenOptions.GetNextAction().GetGetVkycNextActionApiScreenOptions().GetClientLastState())

	// client will pass the entry point from persistent storage, on client
	vkycNextActionResp, err = dep.FeVKYCClient.GetVKYCNextAction(ctx, &fevkycpb.GetVKYCNextActionRequest{
		Req:             reqH,
		ClientLastState: beVkycPb.VKYCClientState_VKYC_CLIENT_STATE_CONFIRM_DETAILS.String(),
		EntryPoint:      entryPoint,
	})
	dep.Assert.NoError(fepkg.FeRPCError(vkycNextActionResp, err))
	ScreenEquals(dep.Assert, deeplink.Screen_UPDATE_CALL_INFO_API, vkycNextActionResp.GetNextAction().GetScreen())

	updateCallInfoResp, err := dep.FeVKYCClient.UpdateCallInfo(ctx, &fevkycpb.UpdateCallInfoRequest{
		Req: reqH,
		CallInfoMetadata: &fevkycpb.VKYCCallInfoMetadata{
			WebviewVersion: "112.0.5615.136",
			BrowserVersion: "113.0.5672.76",
			EntryPointStr:  entryPoint,
		},
		Retries: 0,
	})
	dep.Assert.NoError(fepkg.FeRPCError(updateCallInfoResp, err))
	ScreenEquals(dep.Assert, deeplink.Screen_GET_VKYC_NEXT_ACTION_API, updateCallInfoResp.GetNextAction().GetScreen())

	vkycNextActionResp, _ = dep.FeVKYCClient.GetVKYCNextAction(ctx, &fevkycpb.GetVKYCNextActionRequest{
		Req:             reqH,
		ClientLastState: beVkycPb.VKYCClientState_VKYC_CLIENT_STATE_UPDATE_CALL_INFO.String(),
		EntryPoint:      entryPoint,
	})
	ScreenEquals(dep.Assert, deeplink.Screen_VKYC_USER_WAITING_SCREEN, vkycNextActionResp.GetNextAction().GetScreen())

	for i := 0; i < PollMaxRetries; i++ {
		// client will call GetCallStatus to fetch the latest call status
		getCallStatusResp, _ := dep.FeVKYCClient.GetCallStatus(ctx, &fevkycpb.GetCallStatusRequest{
			Req:       reqH,
			AttemptId: vkycNextActionResp.GetNextAction().GetVkycUserWaitingScreenOptions().GetAttemptId(),
		})
		// ensure call status should be in finished state
		if getCallStatusResp.GetCallStatus() == fevkycpb.CallStatus_CALL_FINISHED {
			break
		}
		time.Sleep(PollInterval)
	}

	// approve vkyc
	anActor, _ := GetUserDetails(ctx, dep, userData.Phone)
	_, _ = dep.VKYCClient.ApproveVKYC(ctx, &beVkycPb.ApproveVKYCRequest{
		ActorId: anActor.GetId(),
	})

	// poll till user upgraded to full kyc
	for i := 0; i < PollMaxRetries; i++ {
		bankCustResp, _ := dep.BankcustClient.GetBankCustomer(ctx, &bankCustPb.GetBankCustomerRequest{
			Identifier: &bankCustPb.GetBankCustomerRequest_ActorId{
				ActorId: anActor.GetId(),
			},
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		})
		if bankCustResp.GetBankCustomer().GetKycInfo().GetKycLevel() == kyc.KYCLevel_FULL_KYC {
			break
		}
		time.Sleep(1 * time.Second)
	}
	nextAction = PollNextAction(ctx, dep, reqH)
	return reqH, nextAction
}

func GetUserFullProfile(ctx context.Context, dep *OnbDep, reqH *header.RequestHeader) *user.GetUserFullProfileResponse {
	userProfileRes, err := dep.UserClient.GetUserFullProfile(ctx, &user.GetUserFullProfileRequest{
		Req: reqH,
	})
	dep.Assert.Nil(err)
	dep.Assert.Equal(int(rpc.StatusOk().GetCode()), int(userProfileRes.GetStatus().GetCode()),
		fmt.Sprintf("GetUserFullProfile Response: %v", userProfileRes.String()),
	)
	return userProfileRes
}

func UserWithTnCPANEmploymentDone(ctx context.Context, dep *OnbDep, userData *UserData) (*header.RequestHeader, *deeplink.Deeplink) {
	reqH, _ := UserWithAccessToken(ctx, dep, userData, &LoginOpts{
		AcquisitionInfo: AcqInfoForSAIntent(),
	})
	// Check that next action is TnC
	nextAction := PollNextAction(ctx, dep, reqH)
	if nextAction.GetScreen().String() != deeplink.Screen_CONSENT.String() {
		SkipOnboardingStage(ctx, reqH, dep.SignupClient, dep.Assert, signup.SkipOnboardingStageRequest_REFERRAL_FINITE_CODE)
	}
	nextAction = PollNextAction(ctx, dep, reqH)

	// Record user consent
	dep.Assert.Equal(deeplink.Screen_CONSENT.String(), nextAction.GetScreen().String())
	nextAction = DoTnCPanEmploymentStages(ctx, dep, reqH, userData)
	return reqH, nextAction
}

func NewUserWithScreenerPassed(ctx context.Context, dep *OnbDep, userData *UserData) (*header.RequestHeader, *deeplink.Deeplink) {
	reqH, nextAction := UserWithTnCPANEmploymentDone(ctx, dep, userData)
	// salary b2b users have their screener stage skipped after employment declaration so assertion will be different
	if userData != nil && userData.SalaryB2BUser {
		return reqH, nextAction
	}
	PassIncomeEstimatorForUser(ctx, dep, userData)
	nextAction = DoCreditScoreVerificationStage(ctx, dep, reqH, userData)
	return reqH, nextAction
}

func redListEmail(ctx context.Context, dep *OnbDep, email string, score float32) {
	res, err := dep.RedListClient.UpsertRedLister(ctx, &redlistPb.UpsertRedListerRequest{
		Members: []*risk.RedLister{
			{
				Key: &risk.RedListPair{
					Category: risk.RedListCategory_REDLIST_CATEGORY_EMAIL,
					Value: &risk.RedListPair_Email{
						Email: email,
					},
				},
				RiskScore: score,
				Reason: &risk.RedListerReason{
					Source:    risk.RedListerSource_RISK_ANALYST_INTERNAL,
					RawReason: "Acceptance test",
				},
				Metadata: &risk.Metadata{
					ManualUpdateMetadata: &risk.Metadata_ManualUpdateMetadata{
						UpdatedByEmail: "<EMAIL>",
						UpdateReason:   "Test",
					},
				},
			},
		},
	})
	dep.Assert.NoError(epifigrpc.RPCError(res, err), fmt.Sprintf("error in upserting email %v", epifigrpc.RPCError(res, err)))
}

// nolint:dupl
func TestOnboardingIntentFiLite(ctx context.Context, dep *OnbDep, aUser *UserData) {
	reqH, respAddOAuth := UserWithAccessToken(ctx, dep, aUser)

	nextAction := respAddOAuth.GetNextAction()
	if nextAction.GetScreen().String() != deeplink.Screen_CONSENT.String() {
		SkipOnboardingStage(ctx, reqH, dep.SignupClient, dep.Assert, signup.SkipOnboardingStageRequest_REFERRAL_FINITE_CODE)
		nextAction = NextAction(ctx, dep.Assert, dep.SignupClient, reqH)
	}

	dep.Assert.Equal(deeplink.Screen_CONSENT.String(), nextAction.GetScreen().String())

	var consentTypeList []consent.ConsentType
	consentTypeList = append(consentTypeList, consent.ConsentType_FI_TNC, consent.ConsentType_FED_TNC, consent.ConsentType_FI_PRIVACY_POLICY, consent.ConsentType_FI_WEALTH_TNC)

	// record consent
	RecordFiConsent(ctx, dep.Assert, dep.ConsentClient, reqH, consentTypeList)

	nextAction = NextAction(ctx, dep.Assert, dep.SignupClient, reqH)
	dep.Assert.Equal(deeplink.Screen_ONBOARDING_INTENT_SELECTION.String(), nextAction.GetScreen().String())

	resp, err := dep.SignupClient.SetOnboardingIntent(ctx, &signup.SetOnboardingIntentRequest{
		Req:              reqH,
		IntentIdentifier: onbPb.OnboardingIntent_ONBOARDING_INTENT_FI_LITE.String(),
	})
	dep.Assert.NoError(fepkg.FeRPCError(resp, err))

	nextAction = NextAction(ctx, dep.Assert, dep.SignupClient, reqH)
	ScreenEquals(dep.Assert, deeplink.Screen_HOME, nextAction.GetScreen())
}

// nolint:dupl
func TestOnboardingIntentFedSavingsAccount(ctx context.Context, dep *OnbDep, aUser *UserData) {
	reqH, respAddOAuth := UserWithAccessToken(ctx, dep, aUser)

	nextAction := respAddOAuth.GetNextAction()
	if nextAction.GetScreen().String() != deeplink.Screen_CONSENT.String() {
		SkipOnboardingStage(ctx, reqH, dep.SignupClient, dep.Assert, signup.SkipOnboardingStageRequest_REFERRAL_FINITE_CODE)
		nextAction = NextAction(ctx, dep.Assert, dep.SignupClient, reqH)
	}

	dep.Assert.Equal(deeplink.Screen_CONSENT.String(), nextAction.GetScreen().String())

	var consentTypeList []consent.ConsentType
	consentTypeList = append(consentTypeList, consent.ConsentType_FI_TNC, consent.ConsentType_FI_PRIVACY_POLICY)

	// record consent
	RecordFiConsent(ctx, dep.Assert, dep.ConsentClient, reqH, consentTypeList)

	nextAction = NextAction(ctx, dep.Assert, dep.SignupClient, reqH)
	dep.Assert.Equal(deeplink.Screen_ONBOARDING_INTENT_SELECTION.String(), nextAction.GetScreen().String())

	resp, err := dep.SignupClient.SetOnboardingIntent(ctx, &signup.SetOnboardingIntentRequest{
		Req:              reqH,
		IntentIdentifier: onbPb.OnboardingIntent_ONBOARDING_INTENT_FEDERAL_SAVINGS_ACCOUNT.String(),
	})
	dep.Assert.NoError(fepkg.FeRPCError(resp, err))

	nextAction = NextAction(ctx, dep.Assert, dep.SignupClient, reqH)
	ScreenEquals(dep.Assert, deeplink.Screen_ONBOARDING_SAVINGS_ACCOUNT_INTRO, nextAction.GetScreen())

	consentTypeList = append([]consent.ConsentType{}, consent.ConsentType_FED_TNC)

	// record consent for savings account
	RecordFiConsent(ctx, dep.Assert, dep.ConsentClient, reqH, consentTypeList)
	nextAction = NextAction(ctx, dep.Assert, dep.SignupClient, reqH)
	dep.Assert.Equal(deeplink.Screen_PAN_VERIFICATION.String(), nextAction.GetScreen().String())
	screenOptionsV2 := &pan.PanVerificationScreenOptions{}
	err = nextAction.GetScreenOptionsV2().UnmarshalTo(screenOptionsV2)
	dep.Assert.NoError(err)
}

// MarkBankCustomerFailed will mark the bank customer entity with the given failure type. If the bank customer entity does not exist, it will skip update.
func MarkBankCustomerFailed(ctx context.Context, deps *OnbDep, aUser *UserData, vendor commonvgpb.Vendor, failureType bankCustPb.FailureType) {
	_, userRes := GetUserDetails(ctx, deps, aUser.Phone)
	resp, errResp := deps.BankcustClient.FailBankCustomer(ctx, &bankCustPb.FailBankCustomerRequest{
		Vendor:      vendor,
		ActorId:     userRes.GetActorId(),
		FailureType: failureType,
	})
	err := epifigrpc.RPCError(resp, errResp)
	deps.Assert.Nil(err, fmt.Sprintf("got error: %v", err))
	bcResp, errResp := deps.BankcustClient.GetBankCustomer(ctx, &bankCustPb.GetBankCustomerRequest{
		Vendor: vendor,
		Identifier: &bankCustPb.GetBankCustomerRequest_UserId{
			UserId: userRes.GetId(),
		},
	})
	err = epifigrpc.RPCError(bcResp, errResp)
	deps.Assert.Nil(err, fmt.Sprintf("got error: %v", err))
	deps.Assert.Equal(bankcust.Status_STATUS_FAILED, bcResp.GetBankCustomer().GetStatus())
	deps.Assert.Equal(bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_FAILED, bcResp.GetBankCustomer().GetVendorMetadata().GetFederalMetadata().GetCustomerCreationState())
	deps.Assert.Equal(failureType, bcResp.GetBankCustomer().GetFailureReason().GetFailureType())
}

func GetEkycRrn(ctx context.Context, dep *OnbDep, actorId string) (string, error) {
	ekycRrn := ""
	bcResp, err := dep.BankcustClient.GetBankCustomer(ctx, &bankCustPb.GetBankCustomerRequest{
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankCustPb.GetBankCustomerRequest_ActorId{
			ActorId: actorId,
		},
	})
	dep.Assert.NoError(err)
	dep.Assert.Equal(rpc.StatusOk().GetCode(), bcResp.GetStatus().GetCode())
	if !bcResp.GetStatus().IsRecordNotFound() &&
		bcResp.GetBankCustomer().GetVendorMetadata().GetFederalMetadata().GetEkycRrnNo() != "" {
		ekycRrn = bcResp.GetBankCustomer().GetVendorMetadata().GetFederalMetadata().GetEkycRrnNo()
		logger.Info(ctx, "returning ekyc rrn number from bank customer", zap.String(logger.STATUS, ekycRrn))
		return ekycRrn, nil
	}

	kycResp, err := dep.BeKycClient.GetKYCRecord(ctx, &kyc.GetKYCRecordRequest{
		ActorId: actorId,
	})
	dep.Assert.NoError(err)
	dep.Assert.Equal(rpc.StatusOk().GetCode(), kycResp.GetStatus().GetCode())
	ekycRrn = kycResp.GetKycRecord().GetUidReferenceKey()
	logger.Info(ctx, "returning ekyc rrn number from KYCRecord", zap.String(logger.STATUS, ekycRrn))
	return ekycRrn, nil
}

func CollectParentsName(ctx context.Context, deps *OnbDep, userData *UserData, reqH *header.RequestHeader) *deeplink.Deeplink {
	nextAction := PollNextAction(ctx, deps, reqH)
	ScreenEquals(deps.Assert, deeplink.Screen_PARENTS_NAME_GETTER, nextAction.GetScreen())
	_, userRes := GetUserDetails(ctx, deps, userData.Phone)
	CreateNominee(ctx, deps.Assert, deps.UserClient, reqH, userRes.GetProfile().GetPanName().ToString())
	ParentsNameGetter(ctx, deps.Assert, deps.UserClient, reqH)
	return PollNextActionWithoutDeps(ctx, deps.Assert, deps.SignupClient, reqH)
}

// AddFundForSingleUser adds funds for a single user
func AddFundForSingleUser(ctx context.Context, reqH *header.RequestHeader, amountToBeAdded int64, orderStatus fePayPb.OrderStatus, dep *OnbDep) string {
	eligibleAccRes := getEligiblePayAccounts(ctx, reqH, dep)
	createURNRes, err := dep.FeTxnClient.CreateURNOrder(ctx, &feTransactionPb.CreateURNOrderRequest{
		Req:            reqH,
		PayeeAccountId: eligibleAccRes.GetAccountAttributes()[0].AccountId,
		Amount: &types.Money{
			CurrencyCode: money.RupeeCurrencyCode,
			Units:        amountToBeAdded,
		},
		InitiationMode: feTransactionPb.CreateURNOrderRequest_INTENT,
		UiEntryPoint:   feTransactionPb.UIEntryPoint_UI_ENTRY_POINT_ONBOARD_ADD_FUNDS_PRE_FUNDING,
	})

	dep.Assert.NoError(err)
	dep.Assert.Equal(rpc.StatusOk().GetCode(), createURNRes.GetStatus().GetCode())
	dep.Assert.NotEmpty(createURNRes.GetUrn())
	dep.Assert.NotEmpty(createURNRes.GetOrderId())

	simulateAddFundsRes, err := dep.FeSimulationClient.SimulateAddFunds(ctx, &fePayPb.SimulateAddFundsRequest{
		Req:     reqH,
		OrderId: createURNRes.GetOrderId(),
	})
	dep.Assert.NoError(err)
	dep.Assert.Equal(rpc.StatusOk().GetCode(), simulateAddFundsRes.GetStatus().GetCode())

	paymentStatus := fePayPb.OrderStatus_ORDER_STATUS_UNSPECIFIED
	for i := 0; i < PollMaxRetries; i++ {
		orderStatusRes, err := dep.FeTxnClient.GetOrderStatus(ctx, &feTransactionPb.GetOrderStatusRequest{
			Req:     reqH,
			OrderId: createURNRes.GetOrderId(),
		})
		dep.Assert.NoError(err)

		paymentStatus = orderStatusRes.GetOrder().GetStatus()

		if paymentStatus == fePayPb.OrderStatus_PAYMENT_FAILED ||
			paymentStatus == fePayPb.OrderStatus_PAYMENT_SUCCESS {
			break
		}

		logger.Info(ctx, "add funds payment not in terminal state", zap.String(logger.ORDER_ID, createURNRes.GetOrderId()),
			zap.String("order-status", paymentStatus.String()))
		time.Sleep(PollInterval)
	}

	dep.Assert.Equal(orderStatus, paymentStatus, "payment status mismatch, returned state = %s want = %s", paymentStatus.String(), orderStatus)
	return createURNRes.GetOrderId()
}

// getEligiblePayAccounts returns eligible pay account for the actor, the account is then used to initialize payment
func getEligiblePayAccounts(ctx context.Context, auth *header.RequestHeader, dep *OnbDep) *feTransactionPb.GetEligibleAccountsForPaymentResponse {

	eligibleAccRes, err := dep.FeTxnClient.GetEligibleAccountsForPayment(ctx, &feTransactionPb.GetEligibleAccountsForPaymentRequest{
		Req: auth,
	})
	dep.Assert.NoError(err)
	dep.Assert.Equal(rpc.StatusOk().Code, eligibleAccRes.GetStatus().GetCode())
	dep.Assert.NotEmpty(eligibleAccRes.GetAccountAttributes())
	return eligibleAccRes
}

func FullKYCDedupeUser(ctx context.Context, deps *OnbDep, userData *UserData) *header.RequestHeader {
	AddCustomerToSimulator(ctx, deps.Assert, deps.SimDB, deps.CustomerClient, userData)
	reqH, nextAction := DoEKYCAndLiveness(ctx, deps, userData)
	// Validate if customer was detected as dedupe customer
	CheckIfCustomerWasDedupe(ctx, deps.Assert, deps.UserBEClient, deps.BankcustClient, userData.Phone)

	// onboard the user
	SkipOnboardingStage(ctx, reqH, deps.SignupClient, deps.Assert, signup.SkipOnboardingStageRequest_ADD_MONEY)
	nextAction = DoOnboardingFromAction(ctx, deps, reqH, nextAction, userData)
	nextAction = PollNextAction(ctx, deps, reqH)
	ScreenEquals(deps.Assert, deeplink.Screen_HOME, nextAction.GetScreen())
	return reqH
}

// VerifyDeviceIntegrity gets nonce, constructs attestation token based on expected result and returns device integrity token
func VerifyDeviceIntegrity(ctx context.Context, req *VerifyDeviceIntegrityParams) string {
	userData := req.UserData
	deps := req.Deps
	reqH := req.RequestHeader
	getNonceRes, err := deps.FeAuthClient.GetDeviceIntegrityNonce(ctx, &feAuthPb.GetDeviceIntegrityNonceRequest{
		Req: reqH,
	})
	deps.Assert.Equal(getNonceRes.GetRespHeader().GetStatus().GetCode(), rpc.StatusOk().GetCode())
	deps.Assert.Nil(err, "error in getting device integrity nonce")
	deps.Assert.NotNil(getNonceRes.GetNonce(), "empty nonce")

	// todo(saiteja): construct attestation token based on result
	verifyRes, err := deps.FeAuthClient.VerifyDeviceIntegrity(ctx, &feAuthPb.VerifyDeviceIntegrityRequest{
		AttestationToken:    userData.SafetyNetToken,
		Nonce:               getNonceRes.GetNonce(),
		TokenType:           req.TokenType,
		Req:                 reqH,
		AllowHighRiskDevice: req.AllowHighRiskDevice,
	})
	if req.ExpectedErrorMessage == "" {
		deps.Assert.Nil(fepkg.FeRPCError(verifyRes, err), "error in verify device integrity response")
		deps.Assert.NotNil(verifyRes.GetDeviceIntegrityToken(), "empty device integrity token")
	} else {
		st, ok := status.FromError(err)
		logger.InfoNoCtx(fmt.Sprintf("err %v ", err.Error()))
		logger.InfoNoCtx(fmt.Sprintf("status %v %v", st.Code(), st.Message()))
		deps.Assert.True(ok)
	}

	return verifyRes.GetDeviceIntegrityToken()
}

func PollCustomerCreationStatus(ctx context.Context, deps *OnbDep, userData *UserData, expectedState bankCustPb.Status) {
	anActor, _ := GetUserDetails(ctx, deps, userData.Phone)
	actorId := anActor.GetId()
	bcResp, errResp := deps.BankcustClient.GetBankCustomer(ctx, &bankCustPb.GetBankCustomerRequest{
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankCustPb.GetBankCustomerRequest_ActorId{
			ActorId: actorId,
		},
	})
	for i := 0; i < PollMaxRetriesLow; i++ {
		if err := epifigrpc.RPCError(bcResp, errResp); err != nil {
			deps.Assert.Failf("failed to fetch bank customer", "err received: %v", err)
		}
		if bcResp.GetBankCustomer().GetStatus() == expectedState {
			return
		}
		time.Sleep(PollInterval)
		bcResp, errResp = deps.BankcustClient.GetBankCustomer(ctx, &bankCustPb.GetBankCustomerRequest{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
			Identifier: &bankCustPb.GetBankCustomerRequest_ActorId{
				ActorId: actorId,
			},
		})
	}
	deps.Assert.Equal(expectedState, bcResp.GetBankCustomer().GetStatus(), "expected bank customer state: %v, got: %v", expectedState.String(), bcResp.GetBankCustomer().GetStatus().String())
}

func TestPanAutofill(ctx context.Context, dep *OnbDep, userData *UserData) {
	var (
		maxPollRetries = 10
	)

	// T&C Stage
	reqH, nextAction := UserWithTnCDone(ctx, dep, userData)
	dep.Assert.Equal(deeplink.Screen_PAN_VERIFICATION.String(), nextAction.GetScreen().String())

	screenOptions, unmarshalErr := nextAction.GetScreenOptionsV2().UnmarshalNew()
	dep.Assert.Nil(unmarshalErr)
	_, convertible := screenOptions.(*pan.PanVerificationScreenOptions)
	dep.Assert.Equal(true, convertible)

	actorId := GetActorIdFromAccessToken(ctx, dep, reqH)

	onbResp, err := dep.OnbClient.GetDetails(ctx, &onbPb.GetDetailsRequest{
		ActorId: actorId,
		Vendor:  commonvgpb.Vendor_FEDERAL_BANK,
	})

	dep.Assert.Nil(err)
	dep.Assert.Equal(rpc.StatusOk().GetCode(), onbResp.GetStatus().GetCode())

	for i := 0; i < maxPollRetries; i++ {
		resp, _ := dep.CrClient.GetDownloadProcessDetails(ctx, &creditreportv2.GetDownloadProcessDetailsRequest{
			RequestId: onbResp.GetDetails().GetStageMetadata().GetCreditReportFetchMetadata().GetClientReqIdFetchWithoutPan(),
		})

		if resp.GetProcessStatus() == creditreportv2.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_COMPLETED {
			logger.Info(ctx, "credit report download completed")
			break
		}
		logger.Info(ctx, "credit report download not in completed state, polling")
		time.Sleep(5 * time.Second)
	}

	formDetailsResp, ffdErr := dep.UserClient.FetchFormDetails(ctx, &user.FetchFormDetailsRequest{
		Req:      reqH,
		Source:   formPkg.UpdateUserDetailsFlow_UPDATE_USER_DETAILS_FLOW_FEDERAL_ONBOARDING_PAN.String(),
		FieldIds: []string{form.FieldIdentifier_FIELD_IDENTIFIER_PAN.String(), form.FieldIdentifier_FIELD_IDENTIFIER_PAN_DATE_OF_BIRTH.String(), form.FieldIdentifier_FIELD_IDENTIFIER_PAN_NAME.String()},
	})
	dep.Assert.Nil(ffdErr)
	dep.Assert.Equal(rpc.StatusOk().GetCode(), formDetailsResp.GetRespHeader().GetStatus().GetCode())

	// check if autofilled values are not empty
	pan, present := formDetailsResp.GetValues()[form.FieldIdentifier_FIELD_IDENTIFIER_PAN.String()]
	dep.Assert.True(present)
	dep.Assert.NotEmpty(pan.GetStringValue())

	dob, present := formDetailsResp.GetValues()[form.FieldIdentifier_FIELD_IDENTIFIER_PAN_DATE_OF_BIRTH.String()]
	dep.Assert.True(present)
	dep.Assert.NotEmpty(dob.GetDateValue().String())

	panName, present := formDetailsResp.GetValues()[form.FieldIdentifier_FIELD_IDENTIFIER_PAN_NAME.String()]
	dep.Assert.True(present)
	dep.Assert.NotEmpty(panName.GetStringValue())

}

func GetActorIdFromAccessToken(ctx context.Context, dep *OnbDep, reqH *header.RequestHeader) string {
	validateAuthRes, err := dep.AuthClient.ValidateToken(ctx, &authPb.ValidateTokenRequest{
		Token:     reqH.GetAuth().GetAccessToken(),
		TokenType: authPb.TokenType_ACCESS_TOKEN,
	})

	dep.Assert.Nil(err)
	dep.Assert.Equal(rpc.StatusOk().GetCode(), validateAuthRes.GetStatus().GetCode())
	dep.Assert.NotEmpty(validateAuthRes.GetActorId())
	return validateAuthRes.GetActorId()
}

func UserWithTnCDone(ctx context.Context, dep *OnbDep, userData *UserData) (*header.RequestHeader, *deeplink.Deeplink) {
	reqH, _ := UserWithAccessToken(ctx, dep, userData, &LoginOpts{
		AcquisitionInfo: AcqInfoForSAIntent(),
	})
	// Check that next action is TnC
	nextAction := PollNextAction(ctx, dep, reqH)
	if nextAction.GetScreen().String() != deeplink.Screen_CONSENT.String() {
		SkipOnboardingStage(ctx, reqH, dep.SignupClient, dep.Assert, signup.SkipOnboardingStageRequest_REFERRAL_FINITE_CODE)
	}
	nextAction = PollNextAction(ctx, dep, reqH)

	// Record user consent
	dep.Assert.Equal(deeplink.Screen_CONSENT.String(), nextAction.GetScreen().String())
	RecordFiConsent(ctx, dep.Assert, dep.ConsentClient, reqH, ConsentList)

	nextAction = PollNextAction(ctx, dep, reqH)
	dep.Assert.Equal(deeplink.Screen_PAN_VERIFICATION.String(), nextAction.GetScreen().String())

	return reqH, nextAction
}
