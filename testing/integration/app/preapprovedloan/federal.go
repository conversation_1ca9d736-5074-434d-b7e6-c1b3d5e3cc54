//nolint:all
package preapprovedloan

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/idgen"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	"github.com/epifi/gamma/api/frontend/preapprovedloan"
	palEnumFePb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	bePalPb "github.com/epifi/gamma/api/preapprovedloan"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	"github.com/epifi/gamma/testing/integration/app"
)

func (ts *PlTestSuite) TestFederalLoanApplication_HappyFlow(t *testing.T, userAuth *header.RequestHeader, a *require.Assertions, userData *app.UserData) {
	ctx := context.WithValue(context.Background(), epificontext.CtxTraceKey, uuid.New().String())

	actorId := ts.getActorIdUsingAuthHeader(t, userAuth)

	userAuth.Auth.GetDevice().AppVersion = 1000
	userAuth.Auth.GetDevice().Platform = commontypes.Platform_ANDROID

	loanHeader := &palEnumFePb.LoanHeader{
		LoanProgram: palEnumFePb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
		Vendor:      palEnumFePb.Vendor_FEDERAL_BANK,
	}

	currentTime := time.Now()

	loec, loecErr := ts.CreateLoanOfferEligibilityCriteria(ctx, &bePalPb.LoanOfferEligibilityCriteria{
		ActorId:                actorId,
		Vendor:                 bePalPb.Vendor_FEDERAL,
		Status:                 bePalPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_APPROVED,
		SubStatus:              bePalPb.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_UNSPECIFIED,
		LoanProgram:            bePalPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
		VendorResponse:         &bePalPb.VendorResponse{},
		BatchId:                uuid.NewString(),
		CreatedAt:              timestampPb.Now(),
		UpdatedAt:              timestampPb.Now(),
		ExpiredAt:              timestampPb.New(time.Now().Add(24 * time.Hour)),
		PolicyParams:           &bePalPb.PolicyParams{ExecutionInfo: &bePalPb.ExecutionInfo{}},
		DataRequirementDetails: &bePalPb.DataRequirementDetails{},
	})
	assert.Nil(t, loecErr, "error creating loan offer eligibility criteria for LENDEN")
	t.Logf("Loec Id: %v", loec.GetId())

	minLoanAmount, maxLoanAmount, maxEmiAmount := moneyPkg.AmountINR(50000).GetPb(), moneyPkg.AmountINR(250000).GetPb(), moneyPkg.AmountINR(15000).GetPb()
	loanOffer, createOfferErr := ts.CreateLoanOffer(ctx, &bePalPb.LoanOffer{
		ActorId:       actorId,
		VendorOfferId: idgen.RandAlphaNumericString(10),
		Vendor:        bePalPb.Vendor_FEDERAL,
		OfferConstraints: &bePalPb.OfferConstraints{
			MinLoanAmount:   minLoanAmount,
			MaxLoanAmount:   maxLoanAmount,
			MaxEmiAmount:    maxEmiAmount,
			MaxTenureMonths: 36,
			MinTenureMonths: 6,
		},
		LoanProgram: bePalPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
		ProcessingInfo: &bePalPb.OfferProcessingInfo{
			ProcessingFee: []*bePalPb.RangeData{
				{
					Value: &bePalPb.RangeData_Percentage{Percentage: 2},
					Start: minLoanAmount.GetUnits(),
					End:   maxLoanAmount.GetUnits(),
				},
			},
			InterestRate: []*bePalPb.RangeData{
				{
					Value: &bePalPb.RangeData_Percentage{Percentage: 14},
					Start: minLoanAmount.GetUnits(),
					End:   maxLoanAmount.GetUnits(),
				},
			},
			Gst: 18,
		},
		ValidSince:                     timestampPb.New(currentTime),
		ValidTill:                      timestampPb.New(currentTime.Add(120 * time.Minute)),
		LoanOfferEligibilityCriteriaId: loec.GetId(),
	})

	assert.Nil(t, createOfferErr, "error creation loan offer")

	// deactivate the loan offer once the test is complete to enable testing another loan application flow for the same user.
	defer func() {
		_ = ts.DeactivateLoanOffer(ctx, bePalPb.Vendor_FEDERAL, loanOffer.GetId())
	}()

	// validate the loan landing screen info
	landingInfoRes := ts.getPlLandingInfo(ctx, t, userAuth, &palEnumFePb.LoanHeader{LoanProgram: palEnumFePb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN})
	assert.Equal(t, deeplinkPb.Screen_LOANS_OFFER_INTRO_SCREEN, landingInfoRes.GetDeeplink().GetScreen())

	// validate the get offer details, without user data
	offerDetailsRes := ts.getOfferDetails(ctx, t, &preapprovedloan.GetOfferDetailsRequest{
		Req:        userAuth,
		LoanHeader: loanHeader,
	})
	assert.Equal(t, deeplinkPb.Screen_LOANS_OFFER_DETAILS_SCREEN, offerDetailsRes.GetDeeplink().GetScreen())
	loansOfferDetailsSO := preapprovedloans.LoansOfferDetailsScreenOptions{}
	_ = offerDetailsRes.GetDeeplink().GetScreenOptionsV2().UnmarshalTo(&loansOfferDetailsSO)
	assert.NotEqual(t, loansOfferDetailsSO.GetOfferId(), "")
	// validate the get offer details, with user selected data
	offerDetailsRes = ts.getOfferDetails(ctx, t, &preapprovedloan.GetOfferDetailsRequest{
		Req:        userAuth,
		LoanHeader: loanHeader,
		OfferId:    loansOfferDetailsSO.GetOfferId(),
		UserInput: &preapprovedloan.GetOfferDetailsRequest_UserInput{
			LoanAmount:     types.GetFromBeMoney(moneyPkg.AmountINR(78000).GetPb()),
			TenureInMonths: 18,
		},
	})

	// tenureOrEMiselectionComp -> default tenure
	// collapsedAmountSelection -> loan amount
	assert.Equal(t, deeplinkPb.Screen_LOANS_OFFER_DETAILS_SCREEN, offerDetailsRes.GetDeeplink().GetScreen())
	_ = offerDetailsRes.GetDeeplink().GetScreenOptionsV2().UnmarshalTo(&loansOfferDetailsSO)
	assert.NotEqual(t, loansOfferDetailsSO.GetOfferId(), "")
	tenure := int32(12)
	loanAmount := loansOfferDetailsSO.GetAmountSelectionSection().GetCollapsedViewAmount()
	for _, loanPlanOption := range loansOfferDetailsSO.GetLoanPlanSelectionSection().GetLoanPlanOptions() {
		loansCustomPlanSO := preapprovedloans.LoansCustomPlanSelectionBottomSheet{}
		err := loanPlanOption.GetDeeplink().GetScreenOptionsV2().UnmarshalTo(&loansOfferDetailsSO)
		if err == nil && loansCustomPlanSO.GetTenureSelectionComponent() != nil {
			tenure = loansCustomPlanSO.GetTenureSelectionComponent().GetDefaultTenureInMonths()
		}
	}
	// validate the get application details rpc
	appDetailsRes := ts.getApplicationDetails(ctx, t, &preapprovedloan.GetApplicationDetailsRequest{
		Req:            userAuth,
		OfferId:        loansOfferDetailsSO.GetOfferId(),
		LoanAmount:     loanAmount,
		TenureInMonths: tenure,
		LoanHeader:     loanHeader,
	})
	assert.Equal(t, deeplinkPb.Screen_LOAN_APPLICATION_DETAIL_SCREEN, appDetailsRes.GetDeeplink().GetScreen())
	applicationDetailsV2 := preapprovedloans.LoanApplicationDetailsScreen{}
	_ = appDetailsRes.GetDeeplink().GetScreenOptionsV2().UnmarshalTo(&applicationDetailsV2)
	assert.NotEqual(t, applicationDetailsV2.GetOfferId(), "")
	// validate the call for apply for loan rpc
	applyForLoanRes := ts.applyForLoan(ctx, t, &preapprovedloan.ApplyForLoanRequest{
		Req:            userAuth,
		OfferId:        applicationDetailsV2.GetOfferId(),
		LoanAmount:     applicationDetailsV2.GetLoanAmount(),
		TenureInMonths: applicationDetailsV2.GetTenureInMonths(),
		LoanHeader:     loanHeader,
	})
	assert.Equal(t, deeplinkPb.Screen_PRE_APPROVED_LOAN_APPLICATION_STATUS_POLL_SCREEN, applyForLoanRes.GetDeeplink().GetScreen())

	var deeplinkReceived *deeplinkPb.Deeplink

	// validate the call for apply for loan. poll for the max attempts, if any other screen comes in response, break
	_ = applyForLoanRes.GetDeeplink().GetPreApprovedLoanApplicationStatusPollScreenOptions().GetLoanRequestId()
	for i := 0; i < maxLoanApplicationPollCount; i++ {
		applicationStatusRes := ts.getApplicationStatus(ctx, t, userAuth, loanHeader, applyForLoanRes.GetDeeplink().GetPreApprovedLoanApplicationStatusPollScreenOptions().GetLoanRequestId())
		if applicationStatusRes.GetDeeplink().GetScreen() != deeplinkPb.Screen_PRE_APPROVED_LOAN_APPLICATION_STATUS_POLL_SCREEN {
			deeplinkReceived = applicationStatusRes.GetDeeplink()
			break
		}
		time.Sleep(2 * time.Second)
	}

	assert.Equal(t, deeplinkPb.Screen_AUTH_STATUS_POLL_SCREEN, deeplinkReceived.GetScreen())
	time.Sleep(5 * time.Second)
	// validate the call for auth status rpc
	deeplinkReceived = ts.pollAuthStatus(ctx, t, deeplinkReceived.GetAuthStatusPollScreenOptions().GetClientRequestId(), userAuth, deeplinkPb.Screen_AUTH_LIVENESS_SUMMARY_STATUS_POLL_SCREEN)
	assert.Equal(t, deeplinkPb.Screen_AUTH_LIVENESS_SUMMARY_STATUS_POLL_SCREEN, deeplinkReceived.GetScreen())
	authLivenessSummaryScreenOptions := deeplinkReceived.GetAuthLivenessSummaryStatusPollScreenOptions()
	deeplinkReceived = ts.pollLivenessSummaryStatus(ctx, t, userAuth, deeplinkReceived.GetAuthLivenessSummaryStatusPollScreenOptions().GetClientRequestId(), deeplinkPb.Screen_CHECK_LIVENESS)
	assert.Equal(t, deeplinkPb.Screen_CHECK_LIVENESS, deeplinkReceived.GetScreen())

	// mark the auth as success after 5 secs
	time.Sleep(5 * time.Second)
	app.CheckLivenessVideo(ctx, a, ts.signupClient, userAuth, userData.Video, deeplinkReceived.GetCheckLivenessScreenOptions().GetAttemptId(), true)

	// poll liveness summary again
	time.Sleep(5 * time.Second)
	deeplinkReceived = ts.pollLivenessSummaryStatus(ctx, t, userAuth, authLivenessSummaryScreenOptions.GetClientRequestId(), deeplinkPb.Screen_AUTH_STATUS_POLL_SCREEN)
	assert.Equal(t, deeplinkPb.Screen_AUTH_STATUS_POLL_SCREEN, deeplinkReceived.GetScreen())

	deeplinkReceived = ts.pollAuthStatus(ctx, t, deeplinkReceived.GetAuthStatusPollScreenOptions().GetClientRequestId(), userAuth, deeplinkPb.Screen_PRE_APPROVED_LOAN_APPLICATION_STATUS_POLL_SCREEN)
	assert.Equal(t, deeplinkPb.Screen_PRE_APPROVED_LOAN_APPLICATION_STATUS_POLL_SCREEN, deeplinkReceived.GetScreen())
	// revisit the sleep time, as in background, we have a lot of stages in btw which intermittently
	// can take longer than 5 seconds and might false fail this test
	// TODO (@prasoon): add handling for e-signing
	time.Sleep(180 * time.Second) // waiting for e-signing to complete

	deeplinkReceived = ts.pollLoansStatusScreen(ctx, t, applyForLoanRes.GetDeeplink().GetPreApprovedLoanApplicationStatusPollScreenOptions().GetLoanRequestId(), userAuth, loanHeader, deeplinkPb.Screen_PRE_APPROVED_LOAN_APPLICATION_STATUS_SCREEN)
	assert.Equal(t, deeplinkPb.Screen_PRE_APPROVED_LOAN_APPLICATION_STATUS_SCREEN, deeplinkReceived.GetScreen())
	assert.Equal(t, deeplinkReceived.GetPreApprovedLoanApplicationStatusScreenOptions().GetLoanHeader().GetLoanProgram(), palEnumFePb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN)
	assert.Equal(t, deeplinkReceived.GetPreApprovedLoanApplicationStatusScreenOptions().GetLoanHeader().GetVendor(), palEnumFePb.Vendor_FEDERAL_BANK)
	// delete the loans data
	err := ts.deleteLoansData(ctx, t, userAuth, loanHeader, bePalPb.Vendor_FEDERAL)
	assert.Nil(t, err)
}
