// nolint:dupl,unparam
package offerwidget

import (
	"context"
	"fmt"
	"math"
	"sync"
	"time"

	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/anypb"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	beNetWorthPb "github.com/epifi/gamma/api/insights/networth"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/async/waitgroup"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	cardPb "github.com/epifi/gamma/api/card"
	cpPb "github.com/epifi/gamma/api/card/provisioning"
	beCasperPb "github.com/epifi/gamma/api/casper"
	beFireflyPb "github.com/epifi/gamma/api/firefly"
	feCardPb "github.com/epifi/gamma/api/frontend/card"
	deepLinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	feHomePb "github.com/epifi/gamma/api/frontend/home"
	fePb "github.com/epifi/gamma/api/frontend/rewards"
	"github.com/epifi/gamma/api/segment"
	types "github.com/epifi/gamma/api/typesv2"
	typesPb "github.com/epifi/gamma/api/typesv2"
	dcScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/debitcard"
	rewardsScreenOptionsV2 "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/rewards"
	"github.com/epifi/gamma/api/typesv2/ui"
	typesUi "github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/behaviors"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/sections"
	onboardingPb "github.com/epifi/gamma/api/user/onboarding"

	"github.com/epifi/gamma/frontend/pkg/common"
	cardFePkg "github.com/epifi/gamma/frontend/pkg/debitcard"
	"github.com/epifi/gamma/frontend/pkg/featureflags"
	"github.com/epifi/gamma/frontend/rewards/tags"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/feature/release"
	pkgUser "github.com/epifi/gamma/pkg/user"
	questSdk "github.com/epifi/gamma/quest/sdk"
)

type HomeCardOffersWidgetGenerator struct {
	fireflyClient             beFireflyPb.FireflyClient
	mappingsManager           *MappingsManager
	cpClient                  cpPb.CardProvisioningClient
	releaseEvaluator          release.IEvaluator
	segmentationServiceClient segment.SegmentationServiceClient
	onboardingClient          onboardingPb.OnboardingClient
	questSdkClient            *questSdk.Client
	userAttributeFetcher      pkgUser.UserAttributesFetcher
	networthClient            beNetWorthPb.NetWorthClient
}

func NewHomeCardOffersWidgetGenerator(mappingsManager *MappingsManager, fireflyClient beFireflyPb.FireflyClient, cpClient cpPb.CardProvisioningClient, releaseEvaluator release.IEvaluator, segmentationServiceClient segment.SegmentationServiceClient, onboardingClient onboardingPb.OnboardingClient, questSdkClient *questSdk.Client, userAttributeFetcher pkgUser.UserAttributesFetcher, networthClient beNetWorthPb.NetWorthClient) *HomeCardOffersWidgetGenerator {
	return &HomeCardOffersWidgetGenerator{
		fireflyClient:             fireflyClient,
		mappingsManager:           mappingsManager,
		cpClient:                  cpClient,
		releaseEvaluator:          releaseEvaluator,
		segmentationServiceClient: segmentationServiceClient,
		onboardingClient:          onboardingClient,
		questSdkClient:            questSdkClient,
		userAttributeFetcher:      userAttributeFetcher,
		networthClient:            networthClient,
	}
}

const (
	cardWidth                  int32 = 136
	cardShadowHeight           int32 = 4
	cardCornerRadius           int32 = 19
	cardInterimSpacing         int32 = 12
	cardInnerHorizontalSpacing int32 = 12
	// Card top image
	cardTopImageWidth  int32 = 136
	cardTopImageHeight int32 = 72
	// Card title
	cardTitleTopMargin int32 = 24
)

var (
	// these are prod segment IDs
	includedSegmentIDsPhysicalDCOfferWidgetExperiment = []string{"2ca12435-2329-48c7-87bc-71eedd219b7a", "91f52116-f3b1-4041-8bf3-c5935e0c93a0"}
	excludedSegmentIDsPhysicalDCOfferWidgetExperiment = []string{"6d188aa7-45b2-4eb0-bb1d-0d826d6c695f"}
)

func (h *HomeCardOffersWidgetGenerator) GetOfferTypes(ctx context.Context) []OfferType {
	return []OfferType{DebitCardOffers, CreditCardOffers}
}

func (h *HomeCardOffersWidgetGenerator) GetTitle(ctx context.Context) *commontypes.Text {
	if h.mappingsManager.isV2CardOfferWidgetEnabled(ctx) {
		// adding this specific check due to incorrect title placement before this version
		platform, version := epificontext.AppPlatformAndVersion(ctx)
		if platform == commontypes.Platform_ANDROID && version <= 430 {
			return nil
		}
	}

	return &commontypes.Text{
		DisplayValue: &commontypes.Text_PlainString{
			PlainString: "Card Offers",
		},
		FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_M},
	}
}

// nolint:funlen
func (h *HomeCardOffersWidgetGenerator) GetTabsData(ctx context.Context, actorId string, fiCoinsBalance uint32, rewardsCount uint32, offers *GetTabsDataOffersParam, creditCardTypeId string, appPlatform commontypes.Platform, appVersionCode uint32, sessionId string,
	originScreen deepLinkPb.Screen) (*typesUi.Filter, map[string]*fePb.GetRewardsAndOffersWidgetResponse_TabData, bool) {
	isFeatureHomeDesignEnhancementsEnabled := featureflags.IsFeatureHomeDesignEnhancementsEnabled(ctx, &featureflags.IsFeatureHomeDesignEnhancementsEnabledRequest{
		ActorId: actorId,
		ExternalDeps: &common.ExternalDependencies{
			Evaluator:            h.releaseEvaluator,
			OnboardingClient:     h.onboardingClient,
			QuestSdkClient:       h.questSdkClient,
			UserAttributeFetcher: h.userAttributeFetcher,
			NetWorthClient:       h.networthClient,
		},
	})
	if h.mappingsManager.isV2CardOfferWidgetEnabled(ctx) {
		return h.getTabsDataV3(ctx, actorId, offers, creditCardTypeId, appPlatform, appVersionCode, originScreen, isFeatureHomeDesignEnhancementsEnabled)
	}
	return h.getTabsData(ctx, actorId, offers, creditCardTypeId, appPlatform, appVersionCode, isFeatureHomeDesignEnhancementsEnabled)
}

func (h *HomeCardOffersWidgetGenerator) GetBottomCta(appPlatform commontypes.Platform, appVersionCode uint32) *ui.IconTextComponent {
	return nil
}

// nolint:funlen
func (h *HomeCardOffersWidgetGenerator) getTabsData(ctx context.Context, actorId string, offers *GetTabsDataOffersParam, creditCardTypeId string, appPlatform commontypes.Platform, appVersionCode uint32, isFeatureHomeDesignEnhancementsEnabled bool) (*typesUi.Filter, map[string]*fePb.GetRewardsAndOffersWidgetResponse_TabData, bool) {
	var (
		tabsFilterType = getTabsFilterType(isFeatureHomeDesignEnhancementsEnabled)
		// generate tabs
		tabs = []*typesUi.Tab{
			{
				Id:          creditCardOffersTabId,
				ActiveCta:   h.mappingsManager.GetActiveStateTabByFilterType(creditCardOffersTabText, tabsFilterType),
				InactiveCta: h.mappingsManager.GetInactiveStateTabByFilterType(creditCardOffersTabText, tabsFilterType),
				BorderColor: feHomePb.GetHomeWidgetBorderColor(),
			},
			{
				Id:          debitCardOffersTabId,
				ActiveCta:   h.mappingsManager.GetActiveStateTabByFilterType(debitCardOffersTabText, tabsFilterType),
				InactiveCta: h.mappingsManager.GetInactiveStateTabByFilterType(debitCardOffersTabText, tabsFilterType),
				BorderColor: feHomePb.GetHomeWidgetBorderColor(),
			},
		}
		// generate tab data map
		creditCardOffersTabData []*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card
		debitCardOffersTabData  []*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card
	)

	// construct tab data for credit card offers tab
	homeFiCreditCardOffers := h.mappingsManager.GetFiCardOfferCards(ctx, offers.BeCreditCardOffers, tags.RenderLocationHomeScreen, appPlatform, appVersionCode)
	maxFiCreditCardOffers := int32(math.Min(float64(len(homeFiCreditCardOffers)), 10))
	creditCardOffersTabData = homeFiCreditCardOffers[:maxFiCreditCardOffers]
	//
	creditCardOffersDl := &deepLinkPb.Deeplink{
		Screen: deepLinkPb.Screen_CARD_OFFERS_CATALOG_SCREEN,
		ScreenOptions: &deepLinkPb.Deeplink_CardOffersCatalogScreenOptions{
			CardOffersCatalogScreenOptions: &deepLinkPb.CardOffersCatalogScreenOptions{
				CardType:   deepLinkPb.CardOffersCatalogScreenOptions_CREDIT_CARD,
				CardTypeId: creditCardTypeId,
			},
		},
	}

	creditCardOffersTabData = append(creditCardOffersTabData, h.mappingsManager.GetViewAllOffersCard(viewAllCardOffersImageUrl, creditCardOffersDl))

	// construct tab data for debit card offers tab
	homeFiDebitCardOffers := h.mappingsManager.GetFiCardOfferCards(ctx, offers.BeDebitCardOffers, tags.RenderLocationHomeScreen, appPlatform, appVersionCode)

	// construct primary card offer widget for debit card
	var wg sync.WaitGroup
	physicalDebitCardOfferCard := &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card{}
	wg.Add(1)
	goroutine.Run(ctx, 500*time.Millisecond, func(ctx context.Context) {
		defer wg.Done()
		physicalDebitCardOfferCard = h.getPhysicalDebitCardOfferCard(ctx, actorId, appPlatform, appVersionCode, isFeatureHomeDesignEnhancementsEnabled)
	})
	waitgroup.SafeWait(&wg, 500*time.Millisecond)
	if physicalDebitCardOfferCard.GetCard() != nil {
		homeFiDebitCardOffers = append([]*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card{physicalDebitCardOfferCard}, homeFiDebitCardOffers...)
	}
	maxFiDebitCardOffers := int32(math.Min(float64(len(homeFiDebitCardOffers)), 10))
	debitCardOffersTabData = homeFiDebitCardOffers[:maxFiDebitCardOffers]

	debitCardOffersDl := &deepLinkPb.Deeplink{
		Screen: deepLinkPb.Screen_CARD_OFFERS_CATALOG_SCREEN,
		ScreenOptions: &deepLinkPb.Deeplink_CardOffersCatalogScreenOptions{
			CardOffersCatalogScreenOptions: &deepLinkPb.CardOffersCatalogScreenOptions{
				CardType:   deepLinkPb.CardOffersCatalogScreenOptions_DEBIT_CARD,
				CardTypeId: fePb.CardTypeId_DEBIT_CARD_ID.String(),
			},
		},
	}
	//
	debitCardOffersTabData = append(debitCardOffersTabData, h.mappingsManager.GetViewAllOffersCard(viewAllCardOffersImageUrl, debitCardOffersDl))

	tabDataMap := map[string]*fePb.GetRewardsAndOffersWidgetResponse_TabData{
		creditCardOffersTabId: {
			TopCta: h.mappingsManager.GetSeeAllCta("SEE ALL", creditCardOffersDl, isFeatureHomeDesignEnhancementsEnabled),
			Cards:  creditCardOffersTabData,
		},
		debitCardOffersTabId: {
			TopCta: h.mappingsManager.GetSeeAllCta("SEE ALL", debitCardOffersDl, isFeatureHomeDesignEnhancementsEnabled),
			Cards:  debitCardOffersTabData,
		},
	}

	filter := &typesUi.Filter{
		Tabs:              tabs,
		FilterType:        tabsFilterType,
		DefaultTabSection: creditCardOffersTabId,
	}

	isUserCreditCardActive, err := h.isUserCreditCardActive(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error while checking if user is credit card active", zap.Error(err))
		return filter, tabDataMap, true
	}
	if isUserCreditCardActive {
		return filter, tabDataMap, true
	}

	filter.DefaultTabSection = debitCardOffersTabId

	return filter, tabDataMap, true
}

// nolint:funlen,unused
func (h *HomeCardOffersWidgetGenerator) getTabsDataV2(ctx context.Context, actorId string, offers *GetTabsDataOffersParam, creditCardTypeId string, appPlatform commontypes.Platform, appVersionCode uint32, originScreen deepLinkPb.Screen, isFeatureHomeDesignEnhancementsEnabled bool) (*typesUi.Filter,
	map[string]*fePb.GetRewardsAndOffersWidgetResponse_TabData, bool) {

	var (
		tabsFilterType = getTabsFilterType(isFeatureHomeDesignEnhancementsEnabled)
		// generate tabs
		tabs = []*typesUi.Tab{
			{
				Id:          creditCardOffersTabId,
				ActiveCta:   h.mappingsManager.GetActiveStateTabByFilterType(creditCardOffersTabText, tabsFilterType),
				InactiveCta: h.mappingsManager.GetInactiveStateTabByFilterType(creditCardOffersTabText, tabsFilterType),
				BorderColor: feHomePb.GetHomeWidgetBorderColor(),
			},
			{
				Id:          debitCardOffersTabId,
				ActiveCta:   h.mappingsManager.GetActiveStateTabByFilterType(debitCardOffersTabText, tabsFilterType),
				InactiveCta: h.mappingsManager.GetInactiveStateTabByFilterType(debitCardOffersTabText, tabsFilterType),
				BorderColor: feHomePb.GetHomeWidgetBorderColor(),
			},
		}
		// generate tab data map
		creditCardOffersTabData []*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card
		debitCardOffersTabData  []*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card
	)

	creditCardOffersDl := &deepLinkPb.Deeplink{
		Screen: deepLinkPb.Screen_CARD_OFFERS_CATALOG_SCREEN,
		ScreenOptions: &deepLinkPb.Deeplink_CardOffersCatalogScreenOptions{
			CardOffersCatalogScreenOptions: &deepLinkPb.CardOffersCatalogScreenOptions{
				CardType:   deepLinkPb.CardOffersCatalogScreenOptions_CREDIT_CARD,
				CardTypeId: creditCardTypeId,
			},
		},
	}

	// construct primary card offer widget for debit card
	var wg sync.WaitGroup
	physicalDebitCardOfferCard := &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card{}
	wg.Add(1)
	goroutine.Run(ctx, 500*time.Millisecond, func(ctx context.Context) {
		defer wg.Done()
		physicalDebitCardOfferCard = h.getPhysicalDebitCardOfferCard(ctx, actorId, appPlatform, appVersionCode, isFeatureHomeDesignEnhancementsEnabled)
	})
	waitgroup.SafeWait(&wg, 500*time.Millisecond)
	if physicalDebitCardOfferCard.GetCard() != nil {
		debitCardOffersTabData = append([]*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card{physicalDebitCardOfferCard}, debitCardOffersTabData...)
	}

	debitCardOffersDl := &deepLinkPb.Deeplink{
		Screen: deepLinkPb.Screen_CARD_OFFERS_CATALOG_SCREEN,
		ScreenOptions: &deepLinkPb.Deeplink_CardOffersCatalogScreenOptions{
			CardOffersCatalogScreenOptions: &deepLinkPb.CardOffersCatalogScreenOptions{
				CardType:   deepLinkPb.CardOffersCatalogScreenOptions_DEBIT_CARD,
				CardTypeId: fePb.CardTypeId_DEBIT_CARD_ID.String(),
			},
		},
	}

	for _, offer := range offers.BeDebitCardOffers {
		var offerImages []string
		for _, img := range offer.GetImages() {
			if img.GetImageType() == beCasperPb.ImageType_BRAND_IMAGE && img.GetUrl() != "" {
				offerImages = append(offerImages, img.GetUrl())
				break
			}
		}

		title := offer.GetName()
		if offer.GetAdditionalDetails().GetHomeTitle() != "" {
			title = offer.GetAdditionalDetails().GetHomeTitle()
		}

		deeplink := &deepLinkPb.Deeplink{
			Screen: deepLinkPb.Screen_CARD_OFFER_DETAILS_SCREEN,
			ScreenOptions: &deepLinkPb.Deeplink_CardOfferDetailsScreenOptions{
				CardOfferDetailsScreenOptions: &deepLinkPb.CardOfferDetailsScreenOptions{OfferId: offer.GetId()},
			},
		}

		allTags := append(offer.GetTagsInfo().GetTags(), offer.GetTagsInfo().GetManualTags()...)
		displayTags, _, err := h.mappingsManager.tagsManager.GetTagsDetailsOrderedByPriority(ctx, allTags, tags.RenderLocationOffersWidget)
		if err != nil {
			logger.Error(ctx, "error while getting tag display details", zap.Error(err))
			continue
		}
		var displayTag *anypb.Any
		if len(displayTags) > 0 {
			logger.DebugNoCtx("display tag details", zap.Any("tag", displayTags[0]))
			displayTag = getAnyWithoutError(displayTags[0])
		}

		leftMargin := offerWidgetCardDefaultLeftMargin
		// leftMargin is zero for the first card
		if len(debitCardOffersTabData) == 0 {
			leftMargin = 0
		}

		debitCardOffersTabData = append(debitCardOffersTabData, h.mappingsManager.getGenericOfferCard(
			offer.GetId(),
			DebitCardOffersAnalytics,
			title,
			colorSnow,
			offerImages,
			displayTag,
			deeplink, leftMargin,
			originScreen,
			isFeatureHomeDesignEnhancementsEnabled))
	}

	for _, offer := range offers.BeCreditCardOffers {
		var imgs []string
		for _, img := range offer.GetImages() {
			if img.GetImageType() == beCasperPb.ImageType_BRAND_IMAGE && img.GetUrl() != "" {
				imgs = append(imgs, img.GetUrl())
				break
			}
		}

		title := offer.GetName()
		if offer.GetAdditionalDetails().GetHomeTitle() != "" {
			title = offer.GetAdditionalDetails().GetHomeTitle()
		}

		deeplink := &deepLinkPb.Deeplink{
			Screen: deepLinkPb.Screen_CARD_OFFER_DETAILS_SCREEN,
			ScreenOptions: &deepLinkPb.Deeplink_CardOfferDetailsScreenOptions{
				CardOfferDetailsScreenOptions: &deepLinkPb.CardOfferDetailsScreenOptions{OfferId: offer.GetId()},
			},
		}

		allTags := append(offer.GetTagsInfo().GetTags(), offer.GetTagsInfo().GetManualTags()...)
		displayTags, _, err := h.mappingsManager.tagsManager.GetTagsDetailsOrderedByPriority(ctx, allTags, tags.RenderLocationOffersWidget)
		if err != nil {
			logger.Error(ctx, "error while getting tag display details", zap.Error(err))
			continue
		}
		var displayTag *anypb.Any
		if len(displayTags) > 0 {
			logger.DebugNoCtx("display tag details", zap.Any("tag", displayTags[0]))
			displayTag = getAnyWithoutError(displayTags[0])
		}

		leftMargin := offerWidgetCardDefaultLeftMargin
		// leftMargin is zero for the first card
		if len(creditCardOffersTabData) == 0 {
			leftMargin = 0
		}

		creditCardOffersTabData = append(creditCardOffersTabData, h.mappingsManager.getGenericOfferCard(
			offer.GetId(),
			CreditCardOffersAnalytics,
			title,
			colorSnow,
			imgs,
			displayTag,
			deeplink, leftMargin,
			originScreen,
			isFeatureHomeDesignEnhancementsEnabled))
	}

	creditCardOffersTabData = lo.Subset(creditCardOffersTabData, 0, 10)
	creditCardOffersTabData = append(creditCardOffersTabData, h.mappingsManager.GetSduiViewAllOffersCard(
		CreditCardOffersAnalytics,
		viewAllCardOffersV2ImageUrl,
		creditCardOffersDl,
		originScreen,
		isFeatureHomeDesignEnhancementsEnabled))

	debitCardOffersTabData = lo.Subset(debitCardOffersTabData, 0, 10)
	debitCardOffersTabData = append(debitCardOffersTabData, h.mappingsManager.GetSduiViewAllOffersCard(
		DebitCardOffersAnalytics,
		viewAllCardOffersV2ImageUrl,
		debitCardOffersDl,
		originScreen,
		isFeatureHomeDesignEnhancementsEnabled))

	tabDataMap := map[string]*fePb.GetRewardsAndOffersWidgetResponse_TabData{
		creditCardOffersTabId: {
			TopCta: h.mappingsManager.GetSeeAllCta("SEE ALL", creditCardOffersDl, isFeatureHomeDesignEnhancementsEnabled),
			Cards:  creditCardOffersTabData,
		},
		debitCardOffersTabId: {
			TopCta: h.mappingsManager.GetSeeAllCta("SEE ALL", debitCardOffersDl, isFeatureHomeDesignEnhancementsEnabled),
			Cards:  debitCardOffersTabData,
		},
	}

	filter := &typesUi.Filter{
		Tabs:              tabs,
		FilterType:        tabsFilterType,
		DefaultTabSection: creditCardOffersTabId,
	}

	isUserCreditCardActive, err := h.isUserCreditCardActive(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error while checking if user is credit card active", zap.Error(err))
		return filter, tabDataMap, true
	}
	if isUserCreditCardActive {
		return filter, tabDataMap, true
	}

	filter.DefaultTabSection = debitCardOffersTabId

	return filter, tabDataMap, true
}

func (h *HomeCardOffersWidgetGenerator) getTabsDataV3(ctx context.Context, actorId string, offers *GetTabsDataOffersParam, creditCardTypeId string, appPlatform commontypes.Platform, appVersionCode uint32, originScreen deepLinkPb.Screen, isFeatureHomeDesignEnhancementsEnabled bool) (*typesUi.Filter, map[string]*fePb.GetRewardsAndOffersWidgetResponse_TabData, bool) {
	isUserCreditCardActive, err := h.isUserCreditCardActive(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error while checking if user is credit card active", zap.Error(err))
		return h.getDefaultCardOffersTabsData(ctx, actorId, offers, creditCardTypeId, appPlatform, appVersionCode, originScreen, isUserCreditCardActive, isFeatureHomeDesignEnhancementsEnabled)
	}
	if isUserCreditCardActive {
		return h.getDefaultCardOffersTabsData(ctx, actorId, offers, creditCardTypeId, appPlatform, appVersionCode, originScreen, isUserCreditCardActive, isFeatureHomeDesignEnhancementsEnabled)
	}
	return h.getDebitCardUserTabsData(ctx, actorId, offers, creditCardTypeId, appPlatform, appVersionCode, originScreen, isUserCreditCardActive, isFeatureHomeDesignEnhancementsEnabled)
}

// nolint:funlen
func (h *HomeCardOffersWidgetGenerator) getDefaultCardOffersTabsData(ctx context.Context, actorId string, offers *GetTabsDataOffersParam, creditCardTypeId string, appPlatform commontypes.Platform, appVersionCode uint32, originScreen deepLinkPb.Screen, isUserCreditCardActive bool, isFeatureHomeDesignEnhancementsEnabled bool) (*typesUi.Filter, map[string]*fePb.GetRewardsAndOffersWidgetResponse_TabData, bool) {

	var (
		tabsFilterType = getTabsFilterType(isFeatureHomeDesignEnhancementsEnabled)
		// generate tabs
		tabs = []*typesUi.Tab{
			{
				Id:          creditCardOffersTabId,
				ActiveCta:   h.mappingsManager.GetActiveStateTabByFilterType(creditCardOffersTabText, tabsFilterType),
				InactiveCta: h.mappingsManager.GetInactiveStateTabByFilterType(creditCardOffersTabText, tabsFilterType),
				BorderColor: feHomePb.GetHomeWidgetBorderColor(),
			},
			{
				Id:          debitCardOffersTabId,
				ActiveCta:   h.mappingsManager.GetActiveStateTabByFilterType(debitCardOffersTabText, tabsFilterType),
				InactiveCta: h.mappingsManager.GetInactiveStateTabByFilterType(debitCardOffersTabText, tabsFilterType),
				BorderColor: feHomePb.GetHomeWidgetBorderColor(),
			},
		}
		// generate tab data map
		creditCardOffersTabData []*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card
		debitCardOffersTabData  []*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card
	)

	creditCardOffersDl := &deepLinkPb.Deeplink{
		Screen: deepLinkPb.Screen_CARD_OFFERS_CATALOG_SCREEN,
		ScreenOptions: &deepLinkPb.Deeplink_CardOffersCatalogScreenOptions{
			CardOffersCatalogScreenOptions: &deepLinkPb.CardOffersCatalogScreenOptions{
				CardType:   deepLinkPb.CardOffersCatalogScreenOptions_CREDIT_CARD,
				CardTypeId: creditCardTypeId,
			},
		},
	}

	// construct primary card offer widget for debit card
	physicalDebitCardOfferCard := h.getPhysicalDebitCardOfferCard(ctx, actorId, appPlatform, appVersionCode, isFeatureHomeDesignEnhancementsEnabled)
	if physicalDebitCardOfferCard.GetCard() != nil {
		debitCardOffersTabData = append([]*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card{physicalDebitCardOfferCard}, debitCardOffersTabData...)
	}

	debitCardOffersDl := &deepLinkPb.Deeplink{
		Screen: deepLinkPb.Screen_CARD_OFFERS_CATALOG_SCREEN,
		ScreenOptions: &deepLinkPb.Deeplink_CardOffersCatalogScreenOptions{
			CardOffersCatalogScreenOptions: &deepLinkPb.CardOffersCatalogScreenOptions{
				CardType:   deepLinkPb.CardOffersCatalogScreenOptions_DEBIT_CARD,
				CardTypeId: fePb.CardTypeId_DEBIT_CARD_ID.String(),
			},
		},
	}

	for _, offer := range offers.BeDebitCardOffers {
		sduiDebitCardOffer := h.getSduiCardOfferCard(ctx, offer, getLeftMarginForCard(len(debitCardOffersTabData)), originScreen, DebitCardOffersAnalytics, isFeatureHomeDesignEnhancementsEnabled)
		if sduiDebitCardOffer != nil {
			debitCardOffersTabData = append(debitCardOffersTabData, sduiDebitCardOffer)
		}
	}

	for _, offer := range offers.BeCreditCardOffers {
		sduiDebitCardOffer := h.getSduiCardOfferCard(ctx, offer, getLeftMarginForCard(len(creditCardOffersTabData)), originScreen, CreditCardOffersAnalytics, isFeatureHomeDesignEnhancementsEnabled)
		if sduiDebitCardOffer != nil {
			creditCardOffersTabData = append(creditCardOffersTabData, sduiDebitCardOffer)
		}
	}

	creditCardOffersTabData = lo.Subset(creditCardOffersTabData, 0, 10)
	creditCardOffersTabData = append(creditCardOffersTabData, h.mappingsManager.GetSduiViewAllOffersCard(
		CreditCardOffersAnalytics,
		viewAllCardOffersV2ImageUrl,
		creditCardOffersDl,
		originScreen,
		isFeatureHomeDesignEnhancementsEnabled))

	debitCardOffersTabData = lo.Subset(debitCardOffersTabData, 0, 10)
	debitCardOffersTabData = append(debitCardOffersTabData, h.mappingsManager.GetSduiViewAllOffersCard(
		DebitCardOffersAnalytics,
		viewAllCardOffersV2ImageUrl,
		debitCardOffersDl,
		originScreen,
		isFeatureHomeDesignEnhancementsEnabled))

	tabDataMap := map[string]*fePb.GetRewardsAndOffersWidgetResponse_TabData{
		creditCardOffersTabId: {
			TopCta: h.mappingsManager.GetSeeAllCta("SEE ALL", creditCardOffersDl, isFeatureHomeDesignEnhancementsEnabled),
			Cards:  creditCardOffersTabData,
		},
		debitCardOffersTabId: {
			TopCta: h.mappingsManager.GetSeeAllCta("SEE ALL", debitCardOffersDl, isFeatureHomeDesignEnhancementsEnabled),
			Cards:  debitCardOffersTabData,
		},
	}

	filter := &typesUi.Filter{
		Tabs:              tabs,
		FilterType:        tabsFilterType,
		DefaultTabSection: debitCardOffersTabId,
	}

	if isUserCreditCardActive {
		filter.DefaultTabSection = creditCardOffersTabId
	}

	return filter, tabDataMap, true
}

func (h *HomeCardOffersWidgetGenerator) getDebitCardUserTabsData(ctx context.Context, actorId string, offers *GetTabsDataOffersParam, creditCardTypeId string, appPlatform commontypes.Platform, appVersionCode uint32, originScreen deepLinkPb.Screen, isUserCreditCardActive bool, isFeatureHomeDesignEnhancementsEnabled bool) (*typesUi.Filter, map[string]*fePb.GetRewardsAndOffersWidgetResponse_TabData, bool) {
	var (
		tabsFilterType = getTabsFilterType(isFeatureHomeDesignEnhancementsEnabled)
		// generate tabs
		tabs = []*typesUi.Tab{
			{
				Id:          domesticDebitCardOffersTabId,
				ActiveCta:   h.mappingsManager.GetActiveStateTabByFilterType(domesticDebitCardOffersTabText, tabsFilterType),
				InactiveCta: h.mappingsManager.GetInactiveStateTabByFilterType(domesticDebitCardOffersTabText, tabsFilterType),
				BorderColor: feHomePb.GetHomeWidgetBorderColor(),
			},
			{
				Id:          internationalDebitCardOffersTabId,
				ActiveCta:   h.mappingsManager.GetActiveStateTabByFilterType(internationalDebitCardOffersTabText, tabsFilterType),
				InactiveCta: h.mappingsManager.GetInactiveStateTabByFilterType(internationalDebitCardOffersTabText, tabsFilterType),
				BorderColor: feHomePb.GetHomeWidgetBorderColor(),
			},
		}

		// generate tab data map
		internationalDebitCardOffersTabData []*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card
		domesticDebitCardOffersTabData      []*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card
	)

	// construct primary card offer widget for debit card
	physicalDebitCardOfferCard := h.getPhysicalDebitCardOfferCard(ctx, actorId, appPlatform, appVersionCode, isFeatureHomeDesignEnhancementsEnabled)
	if physicalDebitCardOfferCard.GetCard() != nil {
		domesticDebitCardOffersTabData = append([]*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card{physicalDebitCardOfferCard}, domesticDebitCardOffersTabData...)
	}

	domesticDebitCardOffersDl := &deepLinkPb.Deeplink{
		Screen: deepLinkPb.Screen_CARD_OFFERS_CATALOG_SCREEN,
		ScreenOptions: &deepLinkPb.Deeplink_CardOffersCatalogScreenOptions{
			CardOffersCatalogScreenOptions: &deepLinkPb.CardOffersCatalogScreenOptions{
				CardType:   deepLinkPb.CardOffersCatalogScreenOptions_DEBIT_CARD,
				CardTypeId: fePb.CardTypeId_DEBIT_CARD_ID.String(),
			},
		},
	}

	for _, offer := range offers.BeDebitCardOffers {
		manualTags := offer.GetTagsInfo().GetManualTags()
		if lo.Contains(manualTags, beCasperPb.TagName_INTERNATIONAL) {
			sduiInternationalDebitCardOffer := h.getSduiCardOfferCard(ctx, offer, getLeftMarginForCard(len(internationalDebitCardOffersTabData)), originScreen, DebitCardOffersAnalytics, isFeatureHomeDesignEnhancementsEnabled)
			if sduiInternationalDebitCardOffer != nil {
				internationalDebitCardOffersTabData = append(internationalDebitCardOffersTabData, sduiInternationalDebitCardOffer)
			}
		} else {
			sduiDomesticDebitCardOffer := h.getSduiCardOfferCard(ctx, offer, getLeftMarginForCard(len(domesticDebitCardOffersTabData)), originScreen, DebitCardOffersAnalytics, isFeatureHomeDesignEnhancementsEnabled)
			if sduiDomesticDebitCardOffer != nil {
				domesticDebitCardOffersTabData = append(domesticDebitCardOffersTabData, sduiDomesticDebitCardOffer)
			}
		}
	}

	internationalDebitCardOffersTabData = lo.Subset(internationalDebitCardOffersTabData, 0, 10)
	internationalDebitCardOffersTabData = append(internationalDebitCardOffersTabData, h.mappingsManager.GetSduiViewAllOffersCard(
		DebitCardOffersAnalytics,
		viewAllCardOffersV2ImageUrl,
		domesticDebitCardOffersDl,
		originScreen,
		isFeatureHomeDesignEnhancementsEnabled))

	domesticDebitCardOffersTabData = lo.Subset(domesticDebitCardOffersTabData, 0, 10)
	domesticDebitCardOffersTabData = append(domesticDebitCardOffersTabData, h.mappingsManager.GetSduiViewAllOffersCard(
		DebitCardOffersAnalytics,
		viewAllCardOffersV2ImageUrl,
		domesticDebitCardOffersDl,
		originScreen,
		isFeatureHomeDesignEnhancementsEnabled))

	ctaText := "SEE ALL"
	if appPlatform == commontypes.Platform_ANDROID && appVersionCode <= 430 {
		ctaText = "ALL OFFERS"
	}
	tabDataMap := map[string]*fePb.GetRewardsAndOffersWidgetResponse_TabData{
		domesticDebitCardOffersTabId: {
			TopCta: h.mappingsManager.GetSeeAllCta(ctaText, domesticDebitCardOffersDl, isFeatureHomeDesignEnhancementsEnabled),
			Cards:  domesticDebitCardOffersTabData,
		},
		internationalDebitCardOffersTabId: {
			TopCta: h.mappingsManager.GetSeeAllCta(ctaText, domesticDebitCardOffersDl, isFeatureHomeDesignEnhancementsEnabled),
			Cards:  internationalDebitCardOffersTabData,
		},
	}

	filter := &typesUi.Filter{
		Tabs:              tabs,
		FilterType:        tabsFilterType,
		DefaultTabSection: domesticDebitCardOffersTabId,
	}

	return filter, tabDataMap, true
}

func (h *HomeCardOffersWidgetGenerator) getSduiCardOfferCard(ctx context.Context, offer *beCasperPb.Offer, leftMargin int, originScreen deepLinkPb.Screen, offerTypeAnalytics OfferTypeAnalytics, isFeatureHomeDesignEnhancementsEnabled bool) *fePb.GetRewardsAndOffersWidgetResponse_TabData_Card {
	var bgImageUrl, brandLogoUrl string
	for _, img := range offer.GetImages() {
		switch img.GetImageType() {
		case beCasperPb.ImageType_BACKGROUND_IMAGE:
			bgImageUrl = img.GetUrl()
		case beCasperPb.ImageType_BRAND_IMAGE:
			brandLogoUrl = img.GetUrl()
		}
	}

	title := offer.GetName()
	if offer.GetAdditionalDetails().GetHomeTitle() != "" {
		title = offer.GetAdditionalDetails().GetHomeTitle()
	}

	deeplink := &deepLinkPb.Deeplink{
		Screen: deepLinkPb.Screen_CARD_OFFER_DETAILS_SCREEN,
		ScreenOptions: &deepLinkPb.Deeplink_CardOfferDetailsScreenOptions{
			CardOfferDetailsScreenOptions: &deepLinkPb.CardOfferDetailsScreenOptions{OfferId: offer.GetId()},
		},
	}

	allTags := append(offer.GetTagsInfo().GetTags(), offer.GetTagsInfo().GetManualTags()...)
	displayTags, _, err := h.mappingsManager.tagsManager.GetTagsDetailsOrderedByPriority(ctx, allTags, tags.RenderLocationCardOffersWidget)
	if err != nil {
		logger.Error(ctx, "error while getting tag display details", zap.Error(err))
		return nil
	}
	var displayTag *anypb.Any
	if len(displayTags) > 0 {
		logger.DebugNoCtx("display tag details", zap.Any("tag", displayTags[0]))
		displayTag = getAnyWithoutError(displayTags[0])
	}

	return h.mappingsManager.getGenericOfferCardV2(
		offer.GetId(),
		offerTypeAnalytics,
		title,
		colorSnow,
		brandLogoUrl,
		bgImageUrl,
		displayTag,
		deeplink, leftMargin,
		originScreen,
		isFeatureHomeDesignEnhancementsEnabled)
}

func (h *HomeCardOffersWidgetGenerator) getPhysicalDCSegmentOffer(ctx context.Context, actorId string, cardTitleString string) string {
	// changing the card text for a segment of users as part of a campaign
	segmentsIds := includedSegmentIDsPhysicalDCOfferWidgetExperiment
	segmentsIds = append(segmentsIds, excludedSegmentIDsPhysicalDCOfferWidgetExperiment...)
	if h.mappingsManager.dyconf.Card().F30IssuanceFeeRefundContent().IsEnabled() {
		segmentsIds = append(segmentsIds, h.mappingsManager.dyconf.Card().F30IssuanceFeeRefundContent().SegmentIds()...)
	}
	if len(segmentsIds) == 0 {
		return cardTitleString
	}
	userSegmentRes, err := h.segmentationServiceClient.IsMember(ctx, &segment.IsMemberRequest{
		ActorId:    actorId,
		SegmentIds: segmentsIds,
	})
	switch {
	case err != nil:
		logger.Error(ctx, "error checking with Segmentation Service for actor's segment IDs", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
	case epifigrpc.RPCError(userSegmentRes, err) != nil:
		logger.Error(ctx, "failed invoking segment service for validation of segmentIds", zap.Error(epifigrpc.RPCError(userSegmentRes, err)), zap.String(logger.ACTOR_ID_V2, actorId))
	default:
		isUserPartOfPhysicalDCOfferIncludedSegments := false
		isUserPartOfPhysicalDCOfferExcludedSegments := false
		segmentMembershipMap := userSegmentRes.GetSegmentMembershipMap()

		if h.mappingsManager.dyconf.Card().F30IssuanceFeeRefundContent().IsEnabled() {
			// give segmentsDebitCardF30IssuanceFeeRefund higher priority
			for _, segmentObj := range h.mappingsManager.dyconf.Card().F30IssuanceFeeRefundContent().SegmentIds() {
				if segmentMembershipMap[segmentObj].GetSegmentStatus() == segment.SegmentStatus_SEGMENT_INSTANCE_FOUND && segmentMembershipMap[segmentObj].GetIsActorMember() {
					return debitCardF30IssuanceFeeRefundWidgetText
				}
			}
		}
		for _, segmentObj := range includedSegmentIDsPhysicalDCOfferWidgetExperiment {
			if segmentMembershipMap[segmentObj].GetSegmentStatus() == segment.SegmentStatus_SEGMENT_INSTANCE_FOUND && segmentMembershipMap[segmentObj].GetIsActorMember() {
				isUserPartOfPhysicalDCOfferIncludedSegments = true
				break
			}
		}
		for _, segmentObj := range excludedSegmentIDsPhysicalDCOfferWidgetExperiment {
			if segmentMembershipMap[segmentObj].GetSegmentStatus() == segment.SegmentStatus_SEGMENT_INSTANCE_FOUND && segmentMembershipMap[segmentObj].GetIsActorMember() {
				isUserPartOfPhysicalDCOfferExcludedSegments = true
				break
			}
		}
		if isUserPartOfPhysicalDCOfferIncludedSegments && !isUserPartOfPhysicalDCOfferExcludedSegments {
			cardTitleString = physicalDCOfferWidgetExperimentCardTitleText
		}
	}
	return cardTitleString
}

// nolint:funlen
func (h *HomeCardOffersWidgetGenerator) getPhysicalDebitCardOfferCard(ctx context.Context, actorId string, _ commontypes.Platform, _ uint32, isFeatureHomeDesignEnhancementsEnabled bool) *fePb.GetRewardsAndOffersWidgetResponse_TabData_Card {
	if !h.checkFeatureReleaseConstraints(ctx, typesPb.Feature_DEBIT_CARD_OFFER_WIDGET_HOME, actorId) {
		return nil
	}
	if !h.mappingsManager.isDebitCardOfferWidgetHomeEnabled(ctx) {
		return nil
	}

	var (
		cardTitleString string
		cardImageUrl    string

		cardDeeplink                     *deepLinkPb.Deeplink
		internationalSpendsRewardOfferId = h.mappingsManager.dyconf.RewardsFrontendMeta().DebitCardRewardsConfig().InternationalSpendsRewardOfferId()
		// Adding 2 variables to set card background & shadow colour
		cardBgColour     = colorSnow
		cardShadowColour = "#6BCDB6"
		margin           = cardShadowHeight
		border           properties.BorderProperty
	)

	if isFeatureHomeDesignEnhancementsEnabled {
		margin = 0
		border = properties.BorderProperty{
			BorderColor:     cardShadowColour,
			CornerRadius:    cardCornerRadius,
			BorderThickness: 1,
		}
	}

	// check card form
	savedCards, err := h.cpClient.GetCardGroups(ctx, &cpPb.GetCardGroupsRequest{
		Actor:               &typesPb.Actor{Id: actorId},
		GetAll:              false,
		NumGroups:           1,
		AscOrderCreatedTime: false,
	})
	if te := epifigrpc.RPCError(savedCards, err); te != nil {
		logger.Error(ctx, "error while fetching card groups in fe", zap.Error(te))
		return nil
	}
	if len(savedCards.GetCards()) == 0 {
		logger.WarnWithCtx(ctx, "no card present for the user")
		return nil
	}

	physicalDispatchStatus, dispatchStatusErr := h.cpClient.GetPhysicalCardDispatchStatus(ctx, &cpPb.GetPhysicalCardDispatchStatusRequest{
		CardId: savedCards.GetCards()[0].GetId(),
	})
	if dispatchStatusErr != nil {
		logger.Error(ctx, "error in GetPhysicalCardDispatchStatus()",
			zap.Error(dispatchStatusErr), zap.String(logger.CARD_ID, savedCards.GetCards()[0].GetId()))
		return nil
	}

	switch {
	case physicalDispatchStatus.GetStatus().GetCode() == uint32(cpPb.GetPhysicalCardDispatchStatusResponse_IN_PROGRESS) ||
		physicalDispatchStatus.GetStatus().GetCode() == uint32(cpPb.GetPhysicalCardDispatchStatusResponse_PAYMENT_IN_PROGRESS):

		logger.Debug(ctx, "dispatch request already in progress", zap.String(logger.CARD_ID, savedCards.GetCards()[0].GetId()))
		cardTitleString = dcInternationalSpendsRewardOfferText
		cardImageUrl = dcInternationalSpendsRewardOfferImageUrl
		cardDeeplink = deeplinkV3.GetDeeplinkV3WithoutError(deepLinkPb.Screen_REWARD_OFFER_DETAILS_SCREEN, &rewardsScreenOptionsV2.RewardOfferDetailsScreenOptions{
			RewardOfferId: internationalSpendsRewardOfferId,
		})

	case savedCards.GetCards()[0].GetForm() == cardPb.CardForm_DIGITAL:
		cardTitleString = "Order a 0 forex Debit Card!"
		cardImageUrl = "https://epifi-icons.pointz.in/cards/home-widget/order-card.png"

		// Change widget details for the quest experiment
		if h.mappingsManager.dyconf.Card().OrderPhysicalDCQuest().IsEnabled(ctx) {
			cardTitleString = h.mappingsManager.dyconf.Card().OrderPhysicalDCQuest().HomeWidgetTitleString(ctx)
			cardImageUrl = h.mappingsManager.dyconf.Card().OrderPhysicalDCQuest().HomeWidgetImageUrl(ctx)
			cardBgColour = h.mappingsManager.dyconf.Card().OrderPhysicalDCQuest().HomeWidgetBgColour(ctx)
			cardShadowColour = h.mappingsManager.dyconf.Card().OrderPhysicalDCQuest().HomeWidgetShadowColour(ctx)
		}

		// check if user is eligible for an offer
		cardTitleString = h.getPhysicalDCSegmentOffer(ctx, actorId, cardTitleString)

		var dlErr error
		cardDeeplink, dlErr = h.getPhysicalCardOrderScreenDl(ctx, actorId)
		if dlErr != nil {
			logger.Error(ctx, "error in getting physical card order deeplink", zap.Error(dlErr))
			return nil
		}

	case savedCards.GetCards()[0].GetForm() == cardPb.CardForm_PHYSICAL:
		// fetch physical card activation status
		activationInfo, err := h.cpClient.GetPhysicalCardActivationInfo(ctx, &cpPb.GetPhysicalCardActivationInfoRequest{
			Identifier: &cpPb.GetPhysicalCardActivationInfoRequest_CardId{CardId: savedCards.GetCards()[0].GetId()},
		})
		if te := epifigrpc.RPCError(activationInfo, err); te != nil {
			logger.Error(ctx, "error while fetching physical card activation info in fe", zap.Error(te))
			return nil
		}

		switch {
		case activationInfo.GetCardActivationStatus() == cpPb.GetPhysicalCardActivationInfoResponse_CARD_ACTIVATION_STATUS_ACTIVATED:
			cardTitleString = dcInternationalSpendsRewardOfferText
			cardImageUrl = dcInternationalSpendsRewardOfferImageUrl
			cardDeeplink = deeplinkV3.GetDeeplinkV3WithoutError(deepLinkPb.Screen_REWARD_OFFER_DETAILS_SCREEN, &rewardsScreenOptionsV2.RewardOfferDetailsScreenOptions{
				RewardOfferId: internationalSpendsRewardOfferId,
			})

		default:
			// fetch tracking delivery status
			shipmentTrackingInfo, err := h.cpClient.GetCardShipmentTrackingDetails(ctx, &cpPb.GetCardShipmentTrackingDetailsRequest{CardId: savedCards.GetCards()[0].GetId()})
			if te := epifigrpc.RPCError(shipmentTrackingInfo, err); te != nil {
				logger.Error(ctx, "error while fetching delivery tracking info in fe", zap.Error(te))
				return nil
			}
			switch shipmentTrackingInfo.GetTrackingDetails().GetDeliveryState() {
			case cpPb.CardTrackingDeliveryState_DELIVERED:
				cardTitleString = "Activate your physical \nDebit Card!"
				cardImageUrl = "https://epifi-icons.pointz.in/cards/home-widget/activate-card.png"

				// construct debit card activation deeplink
				cardDeeplink, _ = cardFePkg.GetCardActivationLandingScreenDeeplink(savedCards.GetCards()[0].GetId(), true)

			case cpPb.CardTrackingDeliveryState_RETURNED_TO_ORIGIN,
				cpPb.CardTrackingDeliveryState_CARD_TRACKING_DELIVERY_STATE_UNSPECIFIED:
				cardTitleString = dcInternationalSpendsRewardOfferText
				cardImageUrl = dcInternationalSpendsRewardOfferImageUrl
				cardDeeplink = deeplinkV3.GetDeeplinkV3WithoutError(deepLinkPb.Screen_REWARD_OFFER_DETAILS_SCREEN, &rewardsScreenOptionsV2.RewardOfferDetailsScreenOptions{
					RewardOfferId: internationalSpendsRewardOfferId,
				})

			default:
				cardTitleString = "Track your physical Debit Card"
				cardImageUrl = "https://epifi-icons.pointz.in/cards/home-widget/track-card-delivery.png"

				// construct debit card tracking deeplink
				cardDeeplink = &deepLinkPb.Deeplink{
					Screen: deepLinkPb.Screen_DEBIT_CARD_TRACKING_SCREEN,
					ScreenOptions: &deepLinkPb.Deeplink_DebitCardTrackingScreenOptions{
						DebitCardTrackingScreenOptions: &deepLinkPb.DebitCardTrackingScreenOptions{
							CardId: savedCards.GetCards()[0].GetId(),
						},
					},
				}
			}

			// override in case of tracking request status is not available,since in this tracking info will also not be available
			if shipmentTrackingInfo.GetTrackingDetails().GetRequestState() == cpPb.CardTrackingRequestState_CARD_TRACKING_REQUEST_STATE_UNSPECIFIED {
				cardTitleString = dcInternationalSpendsRewardOfferText
				cardImageUrl = dcInternationalSpendsRewardOfferImageUrl
				cardDeeplink = deeplinkV3.GetDeeplinkV3WithoutError(deepLinkPb.Screen_REWARD_OFFER_DETAILS_SCREEN, &rewardsScreenOptionsV2.RewardOfferDetailsScreenOptions{
					RewardOfferId: internationalSpendsRewardOfferId,
				})
			}
		}

	default:
		logger.Error(ctx, "unexpected error state reached while constructing physical card offer widget in home",
			zap.String(logger.CARD_FORM, savedCards.GetCards()[0].GetForm().String()))
		return nil
	}

	// currently no reward-offers are configured on physical card international spends
	if cardDeeplink.GetScreen() == deepLinkPb.Screen_REWARD_OFFER_DETAILS_SCREEN {
		return nil
	}

	var (
		cardHeight         int32 = 162
		rightMargin        int32 = cardInterimSpacing
		cardTitleFontStyle       = commontypes.FontStyle_HEADLINE_4
	)
	if h.mappingsManager.isV2CardOfferWidgetEnabled(ctx) {
		cardHeight = offerWidgetCardHeight
		rightMargin = 0
		cardTitleFontStyle = commontypes.FontStyle_SUBTITLE_S
	}

	physicalDebitCardOfferCard := &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card{
		Card: &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card_GenericOfferCard_{
			GenericOfferCard: &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card_GenericOfferCard{
				Section: &sections.Section{
					// Main card section
					Content: &sections.Section_DepthWiseListSection{
						DepthWiseListSection: &sections.DepthWiseListSection{
							InteractionBehaviors: []*behaviors.InteractionBehavior{
								{
									Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
										OnClickBehavior: &behaviors.OnClickBehavior{
											Action: getAnyWithoutError(cardDeeplink),
										},
									},
								},
							},
							VisualProperties: []*properties.VisualProperty{
								{
									Properties: &properties.VisualProperty_ContainerProperty{
										ContainerProperty: &properties.ContainerProperty{
											Size: &properties.Size{
												Width: &properties.Size_Dimension{
													Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
													ExactValue: cardWidth,
												},
												Height: &properties.Size_Dimension{
													Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
													ExactValue: cardHeight + margin,
												},
											},
											Border:  &border,
											BgColor: &widget.BackgroundColour{Colour: &widget.BackgroundColour_BlockColour{BlockColour: cardShadowColour}},
											Corner: &properties.CornerProperty{
												TopLeftCornerRadius:  cardCornerRadius,
												TopRightCornerRadius: cardCornerRadius,
												BottomLeftCorner:     cardCornerRadius,
												BottomRightCorner:    cardCornerRadius,
											},
											Margin: &properties.PaddingProperty{
												Right: rightMargin,
											},
										},
									},
								},
							},
							Components: []*components.Component{
								{
									// Card content section
									Content: getAnyWithoutError(
										&sections.VerticalListSection{
											HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_CENTER_HORIZONTALLY,
											VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_TOP,
											VisualProperties: []*properties.VisualProperty{
												{
													Properties: &properties.VisualProperty_ContainerProperty{
														ContainerProperty: &properties.ContainerProperty{
															Size: &properties.Size{
																Width: &properties.Size_Dimension{
																	Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
																	ExactValue: cardWidth,
																},
																Height: &properties.Size_Dimension{
																	Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
																	ExactValue: cardHeight,
																},
															},
															//Border: &border,
															BgColor: &widget.BackgroundColour{Colour: &widget.BackgroundColour_BlockColour{BlockColour: cardBgColour}},
															Margin: &properties.PaddingProperty{
																Bottom: margin,
															},
															Corner: &properties.CornerProperty{
																TopLeftCornerRadius:  cardCornerRadius,
																TopRightCornerRadius: cardCornerRadius,
																BottomLeftCorner:     cardCornerRadius,
																BottomRightCorner:    cardCornerRadius,
															},
														},
													},
												},
											},
											Components: []*components.Component{
												{
													// Card top image
													Content: getAnyWithoutError(
														&commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{Image: &commontypes.VisualElement_Image{
															Source: &commontypes.VisualElement_Image_Url{Url: cardImageUrl},
															Properties: &commontypes.VisualElementProperties{
																Width:  cardTopImageWidth,
																Height: cardTopImageHeight,
															},
															ImageType: commontypes.ImageType_PNG,
														}}}),
												},
												{
													// Card title
													Content: getAnyWithoutError(&sections.HorizontalListSection{
														VisualProperties: []*properties.VisualProperty{
															{
																Properties: &properties.VisualProperty_ContainerProperty{
																	ContainerProperty: &properties.ContainerProperty{
																		Margin: &properties.PaddingProperty{
																			Left:  cardInnerHorizontalSpacing,
																			Right: cardInnerHorizontalSpacing,
																			Top:   cardTitleTopMargin,
																		},
																	},
																},
															},
														},
														Components: []*components.Component{
															{
																Content: getAnyWithoutError(
																	&commontypes.Text{
																		FontColor:    colorNeutralsNight,
																		DisplayValue: &commontypes.Text_PlainString{PlainString: cardTitleString},
																		FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: cardTitleFontStyle},
																		Alignment:    commontypes.Text_ALIGNMENT_LEFT,
																	},
																),
															},
														},
													}),
												},
											},
										}),
								},
							},
						},
					},
				},
			},
		},
	}
	return physicalDebitCardOfferCard
}

func (h *HomeCardOffersWidgetGenerator) isUserCreditCardActive(ctx context.Context, actorId string) (bool, error) {
	ccResp, ccErr := h.fireflyClient.IsCreditCardUser(ctx, &beFireflyPb.IsCreditCardUserRequest{ActorId: actorId})
	if rErr := epifigrpc.RPCError(ccResp, ccErr); rErr != nil {
		return false, fmt.Errorf("error fetching credit card status for the user, : %w", ccErr)
	}

	// checking if user has credit cards through new flow
	if ccResp.GetIsCreditCardUser() {
		return true, nil
	}

	return false, nil
}

func (h *HomeCardOffersWidgetGenerator) checkFeatureReleaseConstraints(ctx context.Context, feature types.Feature, actorId string) bool {
	isFeatureEnabled, err := h.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(feature).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "error evaluating feature release constraints", zap.String(logger.FEATURE, feature.String()), zap.Error(err))
		isFeatureEnabled = false
	}
	return isFeatureEnabled
}

// getPhysicalCardOrderScreenDl - returns the deeplink for the physical debit card order page
func (h *HomeCardOffersWidgetGenerator) getPhysicalCardOrderScreenDl(ctx context.Context, actorId string) (*deepLinkPb.Deeplink, error) {
	// check if rpc based redirection is enabled for the order card page
	isRpcBasedRedirectionEnabled := h.checkFeatureReleaseConstraints(ctx, types.Feature_FEATURE_DC_DASHBOARD_V2_SCREEN, actorId)
	if isRpcBasedRedirectionEnabled {
		return deeplinkV3.GetDeeplinkV3(deepLinkPb.Screen_DEBIT_CARD_RPC_BASED_REDIRECTION, &dcScreenOptionsPb.DebitCardRpcBasedRedirectionScreenOption{
			RpcParams: &dcScreenOptionsPb.DebitCardRpcBasedRedirectionScreenOption_FetchPhysicalCardChargesForUserRequest{
				FetchPhysicalCardChargesForUserRequest: &feCardPb.FetchPhysicalCardChargesForUserRequest{},
			},
		})
	}

	// call rpc to get the order card page dl of rpc based redirection is not enabled
	fetchPhysicalCardChargesResp, err := h.cpClient.FetchPhysicalCardChargesForUser(ctx, &cpPb.FetchPhysicalCardChargesForUserRequest{
		ActorId: actorId,
	})
	if te := epifigrpc.RPCError(fetchPhysicalCardChargesResp, err); te != nil {
		if fetchPhysicalCardChargesResp.GetStatus().GetCode() == uint32(cpPb.FetchPhysicalCardChargesForUserResponse_VKYC_REJECTED) ||
			fetchPhysicalCardChargesResp.GetStatus().IsFailedPrecondition() {
			return nil, nil
		}
		return nil, fmt.Errorf("error fetching physical dispatch charges for user: %w", te)
	}
	return fetchPhysicalCardChargesResp.GetNextAction(), nil
}

func getLeftMarginForCard(index int) int {
	if index == 0 {
		return 0
	}
	return offerWidgetCardDefaultLeftMargin
}
