// nolint:funlen,unused,unparam
package offerwidget

import (
	"context"
	"fmt"
	"sort"
	"strconv"
	"strings"

	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/anypb"
	anyPb "google.golang.org/protobuf/types/known/anypb"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"

	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/frontend/app/apputils"
	"github.com/epifi/be-common/pkg/logger"

	beCasperPb "github.com/epifi/gamma/api/casper"
	deepLinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	fePb "github.com/epifi/gamma/api/frontend/rewards"
	rewardsFrontendPkgPb "github.com/epifi/gamma/api/frontend/rewards/pkg"
	rewardsScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/rewards"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/analytics"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/behaviors"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/sections"
	"github.com/epifi/gamma/frontend/rewards/constants"

	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/home"
	"github.com/epifi/gamma/frontend/p2pinvestment/helper"
	rewardsFrontendPkg "github.com/epifi/gamma/frontend/rewards/pkg"
	"github.com/epifi/gamma/frontend/rewards/tags"
	"github.com/epifi/gamma/pkg/accrual"
)

type MappingsManager struct {
	tagsManager tags.IManager
	dyconf      *genconf.Config
}

func NewMappingsManager(
	tagsManager tags.IManager,
	dyconf *genconf.Config,
) *MappingsManager {
	return &MappingsManager{
		tagsManager: tagsManager,
		dyconf:      dyconf,
	}
}

func (m *MappingsManager) GetFiCardOfferCards(ctx context.Context, offers []*beCasperPb.Offer, renderLocation tags.RenderLocation, appPlatform commontypes.Platform, appVersionCode uint32) []*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card {
	var fiCardOfferTiles = make([]*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card, 0)
	for _, offer := range offers {
		var (
			deeplink      *deepLinkPb.Deeplink
			cardImageType = beCasperPb.ImageType_BRAND_IMAGE
		)
		// todo (yuvraj): remove this version checks post force upgrade
		if appPlatform == commontypes.Platform_ANDROID && appVersionCode >= m.dyconf.RewardsFrontendMeta().MinAndroidAppVersionSupportingCardOfferDetailsV2() ||
			appPlatform == commontypes.Platform_IOS && appVersionCode >= m.dyconf.RewardsFrontendMeta().MinIosAppVersionSupportingCardOfferDetailsV2() {
			deeplink = &deepLinkPb.Deeplink{
				Screen: deepLinkPb.Screen_CARD_OFFER_DETAILS_SCREEN,
				ScreenOptions: &deepLinkPb.Deeplink_CardOfferDetailsScreenOptions{
					CardOfferDetailsScreenOptions: &deepLinkPb.CardOfferDetailsScreenOptions{OfferId: offer.GetId()},
				},
			}
		} else {
			deeplink = &deepLinkPb.Deeplink{
				Screen: deepLinkPb.Screen_DEBIT_CARD_OFFER_DETAILS_SCREEN,
				ScreenOptions: &deepLinkPb.Deeplink_DebitCardOfferDetailsScreenOptions{
					DebitCardOfferDetailsScreenOptions: &deepLinkPb.DebitCardOfferDetailsScreenOptions{OfferId: offer.GetId()},
				},
			}
		}
		// use home title if set, else use offer name
		// todo (himanshu) using homeTitle for now, will rename this to widgetTitle since this is no longer home specific
		title := offer.GetName()
		if offer.GetAdditionalDetails().GetHomeTitle() != "" {
			title = offer.GetAdditionalDetails().GetHomeTitle()
		}

		if offer.GetAdditionalDetails().GetTileImageContentType() == commontypes.ImageContentType_IMAGE_CONTENT_TYPE_PRODUCT {
			cardImageType = beCasperPb.ImageType_PROMO_IMAGE
		}
		// use brand image always for card offer widget tile
		var imageUrl string
		for _, image := range offer.GetImages() {
			if image.GetImageType() == cardImageType {
				imageUrl = image.GetUrl()
				break
			}
		}

		var offerType string
		if offer.GetRedemptionMode() == beCasperPb.OfferRedemptionMode_FI_CARD {
			offerType = offerTypeDebitCard
		} else {
			offerType = offerTypeCreditCard
		}

		// fetch relevant tags for the fi card offer and set the highest priority tag on tile
		var tag *ui.IconTextComponent
		tagsList, _, err := m.tagsManager.GetTagsDetailsOrderedByPriority(ctx, offer.GetTagsInfo().GetTags(), renderLocation)
		if err != nil {
			// muting the error since we don't want the rpc to fail because of tags
			logger.Error(ctx, "error while getting tags details", zap.Error(err))
		}
		if len(tagsList) > 0 {
			tag = tagsList[0]
		}

		// build card offer widget tile
		fiCardOfferTile := &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card{
			// todo (himanshu) using CatalogOfferHome for now, will rename this to CatalogOfferWidget since this is no longer home specific
			Card: &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card_CatalogOfferHome{
				CatalogOfferHome: &fePb.CatalogOfferHome{
					Title: &commontypes.Text{
						FontColor: "#FFFFFF",
						FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: title,
						},
					},
					Image: &commontypes.Image{
						ImageUrl: imageUrl,
					},
					BgColor: &ui.BackgroundColour{
						Colour: &ui.BackgroundColour_BlockColour{
							BlockColour: offer.GetAdditionalDetails().GetBgColor(),
						},
					},
					Shadows: []*ui.Shadow{
						{
							Height: 4,
							Colour: &ui.BackgroundColour{
								Colour: &ui.BackgroundColour_BlockColour{
									BlockColour: offer.GetAdditionalDetails().GetBgColor(),
								},
							},
						},
						{
							Height: 4,
							Colour: &ui.BackgroundColour{
								Colour: &ui.BackgroundColour_BlockColour{
									BlockColour: "#000000",
								},
							},
							Opacity: 10,
						},
					},
					Deeplink: deeplink,
					Tag:      tag,
					BrandName: &commontypes.Text{
						FontColor: "#80FFFFFF",
						FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: offer.GetAdditionalDetails().GetBrandName(),
						},
					},
					ImageContentType: offer.GetAdditionalDetails().GetTileImageContentType(),
					OfferId:          offer.GetId(),
					OfferType:        offerType,
				},
			},
		}
		fiCardOfferTiles = append(fiCardOfferTiles, fiCardOfferTile)
	}
	return fiCardOfferTiles
}

func (m *MappingsManager) GetFiCoinOfferAndExchangerOfferCards(ctx context.Context, catalogOffersV1 []*fePb.CatalogOfferV1) ([]*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card, []*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card) {
	fiCoinOfferTiles := make([]*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card, 0)
	exchangerOfferTiles := make([]*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card, 0)

	for _, catalogOfferV1 := range catalogOffersV1 {
		switch offer := catalogOfferV1.OfferData.(type) {
		case *fePb.CatalogOfferV1_Offer:
			fiCoinOfferTile := m.getFiCoinOfferCard(offer.Offer)
			if fiCoinOfferTile != nil {
				fiCoinOfferTiles = append(fiCoinOfferTiles, fiCoinOfferTile)
			}
		case *fePb.CatalogOfferV1_ExchangerOffer:
			exchangerOfferTile := m.getExchangerOfferCard(offer.ExchangerOffer)
			if exchangerOfferTile != nil {
				exchangerOfferTiles = append(exchangerOfferTiles, exchangerOfferTile)
			}
		}
	}
	return fiCoinOfferTiles, exchangerOfferTiles
}

func (m *MappingsManager) GetYourRewardsCard(fiCoinsBalance int) *fePb.GetRewardsAndOffersWidgetResponse_TabData_Card {
	return &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card{
		Card: &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card_YourRewardsCard_{
			YourRewardsCard: &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card_YourRewardsCard{
				Title: &commontypes.Text{
					FontColor: "#EAD8A3",
					FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_M},
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: accrual.ReplaceCoinWithPointIfApplicable("Your\nFi Coins"),
					},
				},
				Image: &commontypes.Image{
					ImageUrl: "https://epifi-icons.pointz.in/rewards/2coin-small.png",
				},
				BgColor: &ui.BackgroundColour{
					Colour: &ui.BackgroundColour_RadialGradient{
						RadialGradient: &ui.RadialGradient{
							Center: &ui.CenterCoordinates{
								CenterX: 0,
								CenterY: 0,
							},
							OuterRadius: 20,
							Colours:     []string{"#D3B250", "#AC7C44"},
						},
					},
				},
				Shadow: &ui.Shadow{
					Height: 4,
					Colour: &ui.BackgroundColour{
						Colour: &ui.BackgroundColour_BlockColour{
							BlockColour: "#996931",
						},
					},
				},
				Cta: &ui.IconTextComponent{
					RightIcon: &commontypes.Image{
						ImageUrl: rightChevronWhite,
					},
					Deeplink: &deepLinkPb.Deeplink{
						Screen: deepLinkPb.Screen_MY_REWARDS_SCREEN,
					},
				},
				FiCoinsBalance: &ui.IconTextComponent{
					LeftIcon: &commontypes.Image{
						ImageUrl: "https://epifi-icons.pointz.in/rewards/ficoin-symbol.png",
					},
					Texts: []*commontypes.Text{
						{
							FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_NUMBER_M},
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: convertBalanceToReadableString(fiCoinsBalance),
							},
						},
					},
				},
			},
		},
	}
}

func (m *MappingsManager) GetYourRewardsCardV2ForCatalogOffersTab(fiCoinsBalance uint32, unclaimedRewardsCount uint32) *fePb.GetRewardsAndOffersWidgetResponse_TabData_Card {
	yourRewardsCardV2 := &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card{
		Card: &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card_YourRewardsCardV2_{
			YourRewardsCardV2: &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card_YourRewardsCardV2{
				TopSection: &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card_YourRewardsCardV2_TopSection{
					Header: &commontypes.Text{
						FontColor: "#80FFFFFF",
						FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_XS},
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: accrual.ReplaceCoinWithPointIfApplicable("My Fi-Coins"),
						},
					},
					Body: &ui.IconTextComponent{
						LeftIcon: &commontypes.Image{
							ImageUrl: "https://epifi-icons.pointz.in/rewards/home/<USER>",
							Width:    10,
							Height:   20,
						},
						Texts: []*commontypes.Text{
							{
								FontColor: "#FFFFFF",
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_NUMBER_M},
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: convertBalanceToReadableString(int(fiCoinsBalance)),
								},
							},
						},
						RightIcon: &commontypes.Image{
							ImageUrl: rightChevronWhite,
						},
					},
					Deeplink: &deepLinkPb.Deeplink{
						Screen: deepLinkPb.Screen_MY_REWARDS_SCREEN,
					},
					BgColor: &ui.BackgroundColour{
						Colour: &ui.BackgroundColour_RadialGradient{
							RadialGradient: &ui.RadialGradient{
								Center: &ui.CenterCoordinates{
									CenterX: 0,
									CenterY: 0,
								},
								OuterRadius: 20,
								Colours:     []string{"#D3B250", "#AC7C44"},
							},
						},
					},
					Shadow: &ui.Shadow{
						Height: 4,
						Colour: &ui.BackgroundColour{
							Colour: &ui.BackgroundColour_BlockColour{
								BlockColour: "#7B5B30",
							},
						},
					},
				},
				BottomSection: &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card_YourRewardsCardV2_BottomSection{
					Image: &commontypes.Image{
						ImageType: commontypes.ImageType_PNG,
						ImageUrl:  "https://epifi-icons.pointz.in/rewards/home/<USER>",
						Width:     32,
						Height:    32,
					},
					Header: &commontypes.Text{
						FontColor: "#80FFFFFF",
						FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_XS},
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: "Ways to earn",
						},
					},
					Body: []*ui.IconTextComponent{
						{
							Texts: []*commontypes.Text{
								{
									FontColor: "#FFFFFF",
									FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_XS},
									DisplayValue: &commontypes.Text_PlainString{
										PlainString: "Money-Plants",
									},
								},
							},
							RightIcon: &commontypes.Image{
								ImageUrl: rightChevronWhite,
							},
						},
						{
							Texts: []*commontypes.Text{
								{
									FontColor: "#FFFFFF",
									FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_XS},
									DisplayValue: &commontypes.Text_PlainString{
										PlainString: "cashback",
									},
								},
							},
							RightIcon: &commontypes.Image{
								ImageUrl: rightChevronWhite,
							},
						},
					},
					Deeplink: &deepLinkPb.Deeplink{
						Screen: deepLinkPb.Screen_REWARDS_WAYS_TO_EARN,
					},
					BgColor: &ui.BackgroundColour{
						Colour: &ui.BackgroundColour_RadialGradient{
							RadialGradient: &ui.RadialGradient{
								Center: &ui.CenterCoordinates{
									CenterX: 0,
									CenterY: 0,
								},
								OuterRadius: 20,
								Colours:     []string{"#D3B250", "#AC7C44"},
							},
						},
					},
					Shadow: &ui.Shadow{
						Height: 4,
						Colour: &ui.BackgroundColour{
							Colour: &ui.BackgroundColour_BlockColour{
								BlockColour: "#7B5B30",
							},
						},
					},
				},
			},
		},
	}

	if unclaimedRewardsCount != 0 {
		// to avoid user interface from breaking due to 3 digits
		if unclaimedRewardsCount > 99 {
			unclaimedRewardsCount = 99
		}
		yourRewardsCardV2.GetYourRewardsCardV2().GetTopSection().Badge = &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					FontColor: "#FFFFFF",
					FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: strconv.Itoa(int(unclaimedRewardsCount)),
					},
				},
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:      "#00B899",
				CornerRadius: 10,
				Height:       20,
				Width:        20,
			},
		}
	}

	return yourRewardsCardV2
}

func (m *MappingsManager) GetYourRewardsCardV3(fiCoinsBalance uint32, unclaimedRewardsCount uint32, isFeatureHomeDesignEnhancementsEnabled bool) *fePb.GetRewardsAndOffersWidgetResponse_TabData_Card {
	var (
		badge                         *ui.IconTextComponent
		bottomSectionTickerTexts      []*ui.IconTextComponent
		bottomSectionTexts            = []string{accrual.ReplaceCoinWithPointIfApplicable("Earn more\nFi-Coins"), "Earn more\nCashbacks"}
		shadowColor                   = "#D9DEE3"
		topSectionTitleColor          = colorNeutralSteel
		topSectionSubtitleColor       = colorNeutralsNight
		bottomSectionTickerTextsColor = colorNeutralLead
		topSectionBgColor             = widget.GetBlockBackgroundColour(colorSnow)
		bottomSectionBgColor          = widget.GetBlockBackgroundColour(colorNeutralIvory)
		bottomSectionIcon             = "https://epifi-icons.pointz.in/rewards/home/<USER>"
	)

	if isFeatureHomeDesignEnhancementsEnabled {
		topSectionBgColor = widget.GetBlockBackgroundColour(colorNeutralIvory)
	}

	if unclaimedRewardsCount != 0 {
		// to avoid user interface from breaking due to 3 digits
		if unclaimedRewardsCount > 99 {
			unclaimedRewardsCount = 99
		}
		badge = &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					FontColor: colorSnow,
					FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: strconv.Itoa(int(unclaimedRewardsCount)),
					},
				},
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:      colorFiGreen,
				CornerRadius: 10,
				Height:       20,
				Width:        20,
			},
		}
	}

	for _, bottomSectionText := range bottomSectionTexts {
		bottomSectionTickerTexts = append(bottomSectionTickerTexts, &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					FontColor: bottomSectionTickerTextsColor,
					FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_XS_CAPS},
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: bottomSectionText,
					},
				},
			},
		})
	}

	return &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card{
		Card: &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card_YourRewardsCardV3_{
			YourRewardsCardV3: &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card_YourRewardsCardV3{
				TopSection: &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card_YourRewardsCardV3_TopSection{
					Title: &commontypes.Text{
						FontColor: topSectionTitleColor,
						FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_XS},
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: accrual.ReplaceCoinWithPointIfApplicable("Fi-Coins"),
						},
					},
					Subtitle: commontypes.GetTextFromStringFontColourFontStyle(convertBalanceToReadableString(int(fiCoinsBalance)), topSectionSubtitleColor, commontypes.FontStyle_NUMBER_M),
					Icon:     commontypes.GetVisualElementFromUrlHeightAndWidth(accrual.ReturnApplicableValue("https://epifi-icons.pointz.in/rewards/home/<USER>", constants.FiPoints3dWithBg, nil, true).(string), 24, 24),
					Badge:    badge,
					Deeplink: &deepLinkPb.Deeplink{
						Screen: deepLinkPb.Screen_MY_REWARDS_SCREEN,
					},
					BgColor: topSectionBgColor,
					Shadow: &widget.Shadow{
						Height: 4,
						Colour: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{
								BlockColour: shadowColor,
							},
						},
					},
				},
				BottomSection: &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card_YourRewardsCardV3_BottomSection{
					Icon:        commontypes.GetVisualElementFromUrlHeightAndWidth(bottomSectionIcon, 20, 20),
					TickerTexts: bottomSectionTickerTexts,
					Deeplink: &deepLinkPb.Deeplink{
						Screen: deepLinkPb.Screen_REWARDS_WAYS_TO_EARN,
					},
					BgColor: bottomSectionBgColor,
					Shadow: &widget.Shadow{
						Height: 4,
						Colour: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{
								BlockColour: shadowColor,
							},
						},
					},
				},
			},
		},
	}
}

func (m *MappingsManager) GetYourRewardsCardV2ForCardOffersTab() *fePb.GetRewardsAndOffersWidgetResponse_TabData_Card {
	return &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card{
		Card: &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card_YourRewardsCardV2_{
			YourRewardsCardV2: &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card_YourRewardsCardV2{
				TopSection: &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card_YourRewardsCardV2_TopSection{
					Body: &ui.IconTextComponent{
						LeftIcon: &commontypes.Image{
							ImageUrl: "https://epifi-icons.pointz.in/rewards/home/<USER>",
							Width:    20,
							Height:   20,
						},
						Texts: []*commontypes.Text{
							{
								FontColor: "#FFFFFF",
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_XS},
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "All card\noffers",
								},
							},
						},
						RightIcon: &commontypes.Image{
							ImageUrl: rightChevronWhite,
						},
					},
					Deeplink: &deepLinkPb.Deeplink{
						Screen: deepLinkPb.Screen_CARD_OFFERS_CATALOG_SCREEN,
					},
					BgColor: &ui.BackgroundColour{
						Colour: &ui.BackgroundColour_RadialGradient{
							RadialGradient: &ui.RadialGradient{
								Center: &ui.CenterCoordinates{
									CenterX: 0,
									CenterY: 0,
								},
								OuterRadius: 20,
								Colours:     []string{"#D3B250", "#AC7C44"},
							},
						},
					},
					Shadow: &ui.Shadow{
						Height: 4,
						Colour: &ui.BackgroundColour{
							Colour: &ui.BackgroundColour_BlockColour{
								BlockColour: "#7B5B30",
							},
						},
					},
				},
				BottomSection: &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card_YourRewardsCardV2_BottomSection{
					Image: &commontypes.Image{
						ImageType: commontypes.ImageType_PNG,
						ImageUrl:  "https://epifi-icons.pointz.in/rewards/home/<USER>",
						Width:     32,
						Height:    32,
					},
					Header: &commontypes.Text{
						FontColor: "#80FFFFFF",
						FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_XS},
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: "Ways to earn",
						},
					},
					Body: []*ui.IconTextComponent{
						{
							Texts: []*commontypes.Text{
								{
									FontColor: "#FFFFFF",
									FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_XS},
									DisplayValue: &commontypes.Text_PlainString{
										PlainString: "rewards",
									},
								},
							},
							RightIcon: &commontypes.Image{
								ImageUrl: rightChevronWhite,
							},
						},
					},
					Deeplink: &deepLinkPb.Deeplink{
						Screen: deepLinkPb.Screen_REWARDS_WAYS_TO_EARN,
					},
					BgColor: &ui.BackgroundColour{
						Colour: &ui.BackgroundColour_RadialGradient{
							RadialGradient: &ui.RadialGradient{
								Center: &ui.CenterCoordinates{
									CenterX: 0,
									CenterY: 0,
								},
								OuterRadius: 20,
								Colours:     []string{"#D3B250", "#AC7C44"},
							},
						},
					},
					Shadow: &ui.Shadow{
						Height: 4,
						Colour: &ui.BackgroundColour{
							Colour: &ui.BackgroundColour_BlockColour{
								BlockColour: "#7B5B30",
							},
						},
					},
				},
			},
		},
	}
}

func (m *MappingsManager) GetViewAllOffersCard(imageUrl string, deeplink *deepLinkPb.Deeplink) *fePb.GetRewardsAndOffersWidgetResponse_TabData_Card {
	return &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card{
		Card: &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card_ViewAllOffersCard_{
			ViewAllOffersCard: &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card_ViewAllOffersCard{
				Title: &commontypes.Text{
					FontColor: "#606265",
					FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_NUMBER_M},
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: "View all",
					},
				},
				Image: &commontypes.Image{ImageUrl: imageUrl},
				BgColor: &ui.BackgroundColour{
					Colour: &ui.BackgroundColour_RadialGradient{
						RadialGradient: &ui.RadialGradient{
							Center: &ui.CenterCoordinates{
								CenterX: 0,
								CenterY: 0,
							},
							OuterRadius: 20,
							Colours:     []string{"#E3E7EC", "#B2B5B9"},
						},
					},
				},
				Shadow: &ui.Shadow{
					Height: 4,
					Colour: &ui.BackgroundColour{
						Colour: &ui.BackgroundColour_BlockColour{
							BlockColour: "#B2B5B9",
						},
					},
				},
				Cta: &ui.IconTextComponent{
					RightIcon: &commontypes.Image{
						ImageUrl: rightChevronGrey,
					},
					Deeplink: deeplink,
				},
			},
		},
	}
}

func (m *MappingsManager) GetSduiViewAllOffersCard(offerType OfferTypeAnalytics, imageUrl string, deeplink *deepLinkPb.Deeplink, originScreen deepLinkPb.Screen, isFeatureHomeDesignEnhancementsEnabled bool) *fePb.GetRewardsAndOffersWidgetResponse_TabData_Card {

	var (
		margin = int32(4)
	)

	if isFeatureHomeDesignEnhancementsEnabled {
		margin = 0
	}

	return &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card{
		Card: &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card_GenericOfferCard_{
			GenericOfferCard: &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card_GenericOfferCard{
				Section: &sections.Section{
					Content: &sections.Section_DepthWiseListSection{
						DepthWiseListSection: &sections.DepthWiseListSection{
							IsScrollable:     false,
							VisualProperties: m.getGenericOfferCardVisualProps(12, 20, isFeatureHomeDesignEnhancementsEnabled, colorFog),
							Alignment:        sections.DepthWiseListSection_TOP_CENTER,
							InteractionBehaviors: []*behaviors.InteractionBehavior{
								{
									Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
										OnClickBehavior: &behaviors.OnClickBehavior{
											Action: getAnyWithoutError(deeplink),
										},
									},
									AnalyticsEvent: getOfferCardAnalyticsEvent(
										getGenericOfferCardClickedEventName(offerType),
										"",
										deeplink,
										ViewAllCardSubcomponent,
										originScreen,
									),
								},
							},
							Components: []*components.Component{
								{
									Content: getAnyWithoutError(&sections.VerticalListSection{
										IsScrollable:        false,
										HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
										VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_TOP,
										Components: []*components.Component{
											{
												Content: getAnyWithoutError(&sections.HorizontalListSection{
													IsScrollable: false,
													Components: []*components.Component{
														{
															Content: getAnyWithoutError(commontypes.GetVisualElementFromUrlHeightAndWidth(imageUrl, 104, 116)),
														},
													},
													VisualProperties: []*properties.VisualProperty{
														{
															Properties: &properties.VisualProperty_ContainerProperty{
																ContainerProperty: &properties.ContainerProperty{
																	Size: &properties.Size{
																		Width: &properties.Size_Dimension{
																			Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
																			ExactValue: offerWidgetCardWidth,
																		},
																		Height: &properties.Size_Dimension{
																			Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
																			ExactValue: 104,
																		},
																	},
																	Margin: &properties.PaddingProperty{
																		Top: 0,
																	},
																},
															},
														},
													},
													VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_CENTER_VERTICALLY,
													HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_END,
												}),
											},
											{
												Content: getAnyWithoutError(&sections.HorizontalListSection{
													IsScrollable: false,
													Components: []*components.Component{
														{
															Content: getAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyle("View all", "#6A6D70", commontypes.FontStyle_SUBTITLE_S)),
														},
														{
															Content: getAnyWithoutError(commontypes.GetVisualElementFromUrlHeightAndWidth(rightChevronGrey, 16, 16)),
														},
													},
													VisualProperties: []*properties.VisualProperty{
														{
															Properties: &properties.VisualProperty_ContainerProperty{
																ContainerProperty: &properties.ContainerProperty{
																	Size: &properties.Size{
																		Width: &properties.Size_Dimension{
																			Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
																			ExactValue: offerWidgetCardWidth,
																		},
																		Height: &properties.Size_Dimension{
																			Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
																		},
																	},
																	Padding: &properties.PaddingProperty{
																		Left: 15,
																	},
																	Margin: &properties.PaddingProperty{
																		Top: 12,
																		// Left: 12,
																	},
																	Position: nil,
																},
															},
														},
													},
													VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_CENTER_VERTICALLY,
													HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_START,
												}),
											},
										},
										VisualProperties: []*properties.VisualProperty{
											{
												Properties: &properties.VisualProperty_ContainerProperty{
													ContainerProperty: &properties.ContainerProperty{
														Size: &properties.Size{
															Width: &properties.Size_Dimension{
																Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
																ExactValue: offerWidgetCardWidth,
															},
															Height: &properties.Size_Dimension{
																Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
																ExactValue: 152,
															},
														},
														BgColor: widget.GetBlockBackgroundColour(colorSnow),
														Corner: &properties.CornerProperty{
															TopLeftCornerRadius:  20,
															TopRightCornerRadius: 20,
															BottomLeftCorner:     20,
															BottomRightCorner:    20,
														},
														Margin: &properties.PaddingProperty{
															Bottom: margin,
														},
														Position: nil,
													},
												},
											},
										},
									},
									),
								},
							},
							LoadBehavior: &behaviors.LifecycleBehavior{
								Behavior:       &behaviors.LifecycleBehavior_LoadBehavior{},
								AnalyticsEvent: getOfferCardAnalyticsEvent(getGenericOfferCardLoadedEventName(offerType), "", deeplink, ViewAllCardSubcomponent, originScreen),
							},
							VisibleBehavior: &behaviors.LifecycleBehavior{
								Behavior:       &behaviors.LifecycleBehavior_ViewedBehavior{},
								AnalyticsEvent: getOfferCardAnalyticsEvent(getGenericOfferCardViewedEventName(offerType), "", deeplink, ViewAllCardSubcomponent, originScreen),
							},
						},
					},
				},
			},
		},
	}
}

func (m *MappingsManager) GetSeeAllCta(ctaText string, deeplink *deepLinkPb.Deeplink, isFeatureHomeDesignEnhancementsEnabled bool) *ui.IconTextComponent {
	itc := ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(ctaText, "#646464", commontypes.FontStyle_OVERLINE_XS_CAPS)).
		WithRightImageUrl(rightChevronGrey).
		WithDeeplink(deeplink)

	if isFeatureHomeDesignEnhancementsEnabled {
		itc.Texts = []*commontypes.Text{
			commontypes.GetTextFromStringFontColourFontStyle(ctaText, "#00B899", commontypes.FontStyle_OVERLINE_2XS_CAPS),
		}
		itc.RightImgTxtPadding = 4
		itc.ContainerProperties = &ui.IconTextComponent_ContainerProperties{
			BgColor:       "#DCF3EE",
			CornerRadius:  24,
			Height:        28,
			Width:         80,
			LeftPadding:   15,
			RightPadding:  8,
			TopPadding:    4,
			BottomPadding: 4,
		}
		itc.RightVisualElement = commontypes.GetVisualElementFromUrlHeightAndWidth(rightChevronGreen, 18, 18)
	}

	return itc
}

// nolint:dupl
func (m *MappingsManager) getFiCoinOfferCard(offer *fePb.Offer) *fePb.GetRewardsAndOffersWidgetResponse_TabData_Card {
	// if offer is un-redeemable, we skip
	// if offer.GetDisplayDetails().GetUnredeemableOfferLabel() != nil {
	//	return nil
	// }

	// use home title if set, else use offer details title
	title := offer.GetDisplayDetails().GetOfferTitle()
	if offer.GetDisplayDetails().GetHomeTitle() != "" {
		title = offer.GetDisplayDetails().GetHomeTitle()
	}

	// set tile image url as either brand or product image based on tile image content type of the offer
	// fallback to brand image if image content type is not specified
	var tileImageUrl, brandImageUrl, productImageUrl string
	for _, image := range offer.GetDisplayDetails().GetImages() {
		switch image.GetImageType() {
		case fePb.ImageType_BACKGROUND_IMAGE:
			productImageUrl = image.GetUrl()
		case fePb.ImageType_BRAND_IMAGE:
			brandImageUrl = image.GetUrl()
		default:
			logger.ErrorNoCtx("unspecified image type for the offer", zap.String(logger.OFFER_ID, offer.GetId()))
		}
	}
	switch offer.GetDisplayDetails().GetTileImageContentType() {
	case commontypes.ImageContentType_IMAGE_CONTENT_TYPE_LOGO, commontypes.ImageContentType_IMAGE_CONTENT_TYPE_UNSPECIFIED:
		tileImageUrl = brandImageUrl
	case commontypes.ImageContentType_IMAGE_CONTENT_TYPE_PRODUCT:
		tileImageUrl = productImageUrl
	}

	// use the highest priority tag, if exists
	var tag *ui.IconTextComponent
	if len(offer.Tags) > 0 {
		tag = offer.Tags[0]
	}

	offerCard := &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card{
		Card: &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card_CatalogOfferHome{
			CatalogOfferHome: &fePb.CatalogOfferHome{
				Title: &commontypes.Text{
					FontColor: "#FFFFFF",
					FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: title,
					},
				},
				Image: &commontypes.Image{
					ImageUrl: tileImageUrl,
				},
				BgColor: &ui.BackgroundColour{
					Colour: &ui.BackgroundColour_BlockColour{
						BlockColour: offer.GetDisplayDetails().GetBgColor(),
					},
				},
				Shadows: []*ui.Shadow{
					{
						Height: 4,
						Colour: &ui.BackgroundColour{
							Colour: &ui.BackgroundColour_BlockColour{
								BlockColour: offer.GetDisplayDetails().GetBgColor(),
							},
						},
					},
					{
						Height: 4,
						Colour: &ui.BackgroundColour{
							Colour: &ui.BackgroundColour_BlockColour{
								BlockColour: "#000000",
							},
						},
						Opacity: 10,
					},
				},
				Deeplink: &deepLinkPb.Deeplink{
					Screen: deepLinkPb.Screen_OFFERS_LANDING_SCREEN,
					ScreenOptions: &deepLinkPb.Deeplink_OfferCatalogScreenOptions{
						OfferCatalogScreenOptions: &deepLinkPb.OfferCatalogScreenOptions{
							DisplayFirstOfferIds: []string{offer.GetId()},
						},
					},
				},
				Tag: tag,
				BrandName: &commontypes.Text{
					FontColor: "#80FFFFFF",
					FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: offer.GetDisplayDetails().GetBrandName(),
					},
				},
				ImageContentType: offer.GetDisplayDetails().GetTileImageContentType(),
				OfferId:          offer.GetId(),
				OfferType:        offerTypeNonCbr,
			},
		},
	}

	// we'll show a timer if offer has one, otherwise we'll show the applied tag of highest priority
	if offer.GetDisplayDetails().GetCtaLabel().GetCountdownTimer().GetCountdownTillTime() != nil {
		offerCard.GetCatalogOfferHome().TagV2 = &fePb.CatalogOfferHome_CountdownTimer{
			CountdownTimer: &fePb.CountdownTimer{
				Text:              helper.GetText(catalogOfferCountdownTimerText, colorSnow, commontypes.FontStyle_OVERLINE_2XS_CAPS),
				Icon:              commontypes.GetVisualElementImageFromUrl(catalogOfferCountdownTimerImageUrl),
				CountdownTillTime: offer.GetDisplayDetails().GetCtaLabel().GetCountdownTimer().GetCountdownTillTime(),
				BgColor:           &widget.BackgroundColour{Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#33FFFFFF"}},
			},
		}
	} else {
		offerCard.GetCatalogOfferHome().TagV2 = &fePb.CatalogOfferHome_AppliedTag{
			AppliedTag: tag,
		}
	}

	return offerCard
}

// nolint:dupl
func (m *MappingsManager) getExchangerOfferCard(exchangerOffer *fePb.ExchangerOfferWidget) *fePb.GetRewardsAndOffersWidgetResponse_TabData_Card {
	// if exchanger offer is un-redeemable, we skip
	// if exchangerOffer.GetDisplayDetails().GetUnredeemableOfferLabel() != nil {
	//	return nil
	// }

	// use home title if set, else use offer details title
	title := exchangerOffer.GetDisplayDetails().GetTitle()
	if exchangerOffer.GetDisplayDetails().GetHomeTitle() != "" {
		title = exchangerOffer.GetDisplayDetails().GetHomeTitle()
	}

	var tileImageUrl, tileBgColor string
	// set tile image url as either brand or product image based on tile image content type of the exchanger offer
	// fallback to brand image if image content type is not specified
	switch exchangerOffer.GetDisplayDetails().GetTileImageContentType() {
	case commontypes.ImageContentType_IMAGE_CONTENT_TYPE_LOGO, commontypes.ImageContentType_IMAGE_CONTENT_TYPE_UNSPECIFIED:
		tileImageUrl = exchangerOffer.GetDisplayDetails().GetInfoBannerIconUrl()
		tileBgColor = exchangerOffer.GetDisplayDetails().GetInfoBannerBgColor()
	case commontypes.ImageContentType_IMAGE_CONTENT_TYPE_PRODUCT:
		tileImageUrl = exchangerOffer.GetDisplayDetails().GetImageUrl()
		tileBgColor = exchangerOffer.GetDisplayDetails().GetBgColor()
	}

	// use the highest priority tag, if exists
	var tag *ui.IconTextComponent
	if len(exchangerOffer.Tags) > 0 {
		tag = exchangerOffer.Tags[0]
	}

	offerCard := &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card{
		Card: &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card_CatalogOfferHome{
			CatalogOfferHome: &fePb.CatalogOfferHome{
				Title: &commontypes.Text{
					FontColor: "#FFFFFF",
					FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: title,
					},
				},
				Image: &commontypes.Image{
					ImageUrl: tileImageUrl,
				},
				BgColor: &ui.BackgroundColour{
					Colour: &ui.BackgroundColour_BlockColour{
						BlockColour: tileBgColor,
					},
				},
				Shadows: []*ui.Shadow{
					{
						Height: 4,
						Colour: &ui.BackgroundColour{
							Colour: &ui.BackgroundColour_BlockColour{
								BlockColour: exchangerOffer.GetDisplayDetails().GetInfoBannerBgColor(),
							},
						},
					},
					{
						Height: 4,
						Colour: &ui.BackgroundColour{
							Colour: &ui.BackgroundColour_BlockColour{
								BlockColour: "#000000",
							},
						},
						Opacity: 10,
					},
				},
				Deeplink: &deepLinkPb.Deeplink{
					Screen: deepLinkPb.Screen_OFFERS_LANDING_SCREEN,
					ScreenOptions: &deepLinkPb.Deeplink_OfferCatalogScreenOptions{
						OfferCatalogScreenOptions: &deepLinkPb.OfferCatalogScreenOptions{
							DisplayFirstOfferIds: []string{exchangerOffer.GetId()},
						},
					},
				},
				Tag: tag,
				BrandName: &commontypes.Text{
					FontColor: "#80FFFFFF",
					FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: exchangerOffer.GetDisplayDetails().GetBrandName(),
					},
				},
				ImageContentType: exchangerOffer.GetDisplayDetails().GetTileImageContentType(),
				OfferId:          exchangerOffer.GetId(),
				OfferType:        offerTypeCbr,
			},
		},
	}

	// we'll show a timer if offer has one, otherwise we'll show the applied tag of highest priority
	if exchangerOffer.GetDisplayDetails().GetCtaLabel().GetCountdownTimer().GetCountdownTillTime() != nil {
		offerCard.GetCatalogOfferHome().TagV2 = &fePb.CatalogOfferHome_CountdownTimer{
			CountdownTimer: &fePb.CountdownTimer{
				Text:              helper.GetText(catalogOfferCountdownTimerText, colorSnow, commontypes.FontStyle_SUBTITLE_S),
				Icon:              commontypes.GetVisualElementImageFromUrl(catalogOfferCountdownTimerImageUrl),
				CountdownTillTime: exchangerOffer.GetDisplayDetails().GetCtaLabel().GetCountdownTimer().GetCountdownTillTime(),
				BgColor:           &widget.BackgroundColour{Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#33FFFFFF"}},
			},
		}
	} else {
		offerCard.GetCatalogOfferHome().TagV2 = &fePb.CatalogOfferHome_AppliedTag{
			AppliedTag: tag,
		}
	}

	return offerCard
}

func (m *MappingsManager) getGenericOfferCard(
	offerId string,
	offerType OfferTypeAnalytics,
	title string,
	bgColor string,
	images []string,
	tag *anyPb.Any,
	deeplink *deepLinkPb.Deeplink,
	leftMargin int,
	originScreen deepLinkPb.Screen,
	isFeatureHomeDesignEnhancementsEnabled bool,
) *fePb.GetRewardsAndOffersWidgetResponse_TabData_Card {
	var horizontalImgComponents []*components.Component
	for _, img := range lo.Slice(images, 0, 3) {
		horizontalImgComponents = append(horizontalImgComponents, &components.Component{
			Content: getAnyWithoutError(commontypes.GetVisualElementFromUrlHeightAndWidth(img, 42, 42)),
		})
	}

	var (
		margin = int32(4)
	)

	if isFeatureHomeDesignEnhancementsEnabled {
		margin = 0
	}

	return &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card{
		Card: &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card_GenericOfferCard_{
			GenericOfferCard: &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card_GenericOfferCard{
				Section: &sections.Section{
					Content: &sections.Section_DepthWiseListSection{
						DepthWiseListSection: &sections.DepthWiseListSection{
							IsScrollable:     false,
							VisualProperties: m.getGenericOfferCardVisualProps(leftMargin, 20, isFeatureHomeDesignEnhancementsEnabled, colorFog),
							Alignment:        sections.DepthWiseListSection_TOP_CENTER,
							InteractionBehaviors: []*behaviors.InteractionBehavior{
								{
									Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
										OnClickBehavior: &behaviors.OnClickBehavior{
											Action: getAnyWithoutError(deeplink),
										},
									},
									AnalyticsEvent: getOfferCardAnalyticsEvent(getGenericOfferCardClickedEventName(offerType), offerId, deeplink, OffersCardSubcomponent, originScreen),
								},
							},
							Components: []*components.Component{
								{
									Content: getAnyWithoutError(&sections.VerticalListSection{
										IsScrollable:        false,
										HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
										VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_SPACE_BETWEEN,
										Components: []*components.Component{
											{
												Content: tag,
											},
											{
												Content: getAnyWithoutError(&sections.HorizontalListSection{
													IsScrollable:          false,
													Components:            horizontalImgComponents,
													VisualProperties:      nil,
													VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_CENTER_VERTICALLY,
													HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_START,
													ListElementOverlapProps: &properties.ListElementOverlapProps{
														OverlapDevicePixels:       12,
														OverlapPaddingPixels:      2,
														OverlapCornerRadiusPixels: 50,
														PaddingBgColor:            widget.GetBlockBackgroundColour(bgColor),
													},
												}),
											},
											{
												Content: getAnyWithoutError(&commontypes.Text{
													FontColor: colorNeutralsNight,
													DisplayValue: &commontypes.Text_PlainString{
														PlainString: title,
													},
													FontStyle: &commontypes.Text_StandardFontStyle{
														StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
													},
													Alignment: commontypes.Text_ALIGNMENT_LEFT,
													MaxLines:  2,
												}),
											},
										},
										VisualProperties: []*properties.VisualProperty{
											{
												Properties: &properties.VisualProperty_ContainerProperty{
													ContainerProperty: &properties.ContainerProperty{
														Size: &properties.Size{
															Width: &properties.Size_Dimension{
																Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
																ExactValue: offerWidgetCardWidth,
															},
															Height: &properties.Size_Dimension{
																Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
																ExactValue: offerWidgetCardHeight,
															},
														},
														BgColor: widget.GetBlockBackgroundColour(bgColor),
														Corner: &properties.CornerProperty{
															TopLeftCornerRadius:  20,
															TopRightCornerRadius: 20,
															BottomLeftCorner:     20,
															BottomRightCorner:    20,
														},
														Padding: &properties.PaddingProperty{
															Left:   12,
															Top:    16,
															Right:  12,
															Bottom: 16,
														},
														Margin: &properties.PaddingProperty{
															Bottom: margin,
														},
														Position: nil,
													},
												},
											},
										},
									},
									),
								},
							},
							LoadBehavior: &behaviors.LifecycleBehavior{
								Behavior: &behaviors.LifecycleBehavior_LoadBehavior{},
								AnalyticsEvent: getOfferCardAnalyticsEvent(
									getGenericOfferCardLoadedEventName(offerType),
									offerId,
									deeplink,
									OffersCardSubcomponent,
									originScreen),
							},
							VisibleBehavior: &behaviors.LifecycleBehavior{
								Behavior: &behaviors.LifecycleBehavior_ViewedBehavior{},
								AnalyticsEvent: getOfferCardAnalyticsEvent(
									getGenericOfferCardViewedEventName(offerType),
									offerId,
									deeplink,
									OffersCardSubcomponent,
									originScreen,
								),
							},
						},
					},
				},
			},
		},
	}
}

// figma: https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=26303-43792&t=SKbteM1ThJNSdZxf-1
func (m *MappingsManager) getGenericOfferCardV2(
	offerId string,
	offerType OfferTypeAnalytics,
	title string,
	bgColor string,
	brandLogoUrl string,
	bgImageUrl string,
	tag *anyPb.Any,
	deeplink *deepLinkPb.Deeplink,
	leftMargin int,
	originScreen deepLinkPb.Screen,
	isFeatureHomeDesignEnhancementsEnabled bool,
) *fePb.GetRewardsAndOffersWidgetResponse_TabData_Card {
	// Structure:
	//
	// DepthWiseListSection
	//  - VerticalListSection
	//     - DepthWiseListSection
	//		  - HorizontalListSection
	// 		     - VisualElement (Background Image)
	//		  - VerticalListSection
	//			 - HorizontalListSection
	//				- VisualElement (Brand Logo)
	// 		  - VerticalListSection
	//			 - HorizontalListSection
	// 				- Tag
	//	   - HorizontalListSection
	//        - Text (Title)

	var (
		margin = int32(4)
	)

	if isFeatureHomeDesignEnhancementsEnabled {
		margin = 0
	}

	return &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card{
		Card: &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card_GenericOfferCard_{
			GenericOfferCard: &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card_GenericOfferCard{
				Section: &sections.Section{
					Content: &sections.Section_DepthWiseListSection{
						DepthWiseListSection: &sections.DepthWiseListSection{
							IsScrollable:     false,
							VisualProperties: m.getGenericOfferCardVisualProps(leftMargin, 16, isFeatureHomeDesignEnhancementsEnabled, colorFog),
							Alignment:        sections.DepthWiseListSection_TOP_CENTER,
							InteractionBehaviors: []*behaviors.InteractionBehavior{
								{
									Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
										OnClickBehavior: &behaviors.OnClickBehavior{
											Action: getAnyWithoutError(deeplink),
										},
									},
									AnalyticsEvent: getOfferCardAnalyticsEvent(getGenericOfferCardClickedEventName(offerType), offerId, deeplink, OffersCardSubcomponent, originScreen),
								},
							},
							Components: []*components.Component{
								{
									Content: getAnyWithoutError(&sections.VerticalListSection{
										IsScrollable:        false,
										HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_CENTER_HORIZONTALLY,
										VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_TOP,
										VisualProperties: []*properties.VisualProperty{
											{
												Properties: &properties.VisualProperty_ContainerProperty{
													ContainerProperty: properties.GetContainerProperty().
														WithBlockBgColor(bgColor).
														WithAllCornerRadii(16, 16, 16, 16).
														WithPadding(4, 4, 4, 4).
														WithMargin(0, 0, 0, margin).
														WithHeight(properties.Size_Dimension_DIMENSION_TYPE_EXACT, offerWidgetCardHeight).
														WithWidth(properties.Size_Dimension_DIMENSION_TYPE_EXACT, offerWidgetCardWidth),
												},
											},
										},
										Components: []*components.Component{
											{
												Content: getAnyWithoutError(&sections.DepthWiseListSection{
													VisualProperties: []*properties.VisualProperty{
														{
															Properties: &properties.VisualProperty_ContainerProperty{
																ContainerProperty: properties.GetContainerProperty().
																	WithBlockBgColor(colors.ColorMonochromeChalk).
																	WithAllCornerRadii(14, 14, 14, 14).
																	WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0).
																	WithHeight(properties.Size_Dimension_DIMENSION_TYPE_EXACT, 80),
															},
														},
													},
													Components: []*components.Component{
														{
															Content: getAnyWithoutError(&sections.HorizontalListSection{
																HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_END,
																VisualProperties: []*properties.VisualProperty{
																	{
																		Properties: &properties.VisualProperty_ContainerProperty{
																			ContainerProperty: properties.GetContainerProperty().
																				WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0).
																				WithHeight(properties.Size_Dimension_DIMENSION_TYPE_EXACT, 80),
																		},
																	}},
																Components: []*components.Component{
																	getVisualElementComponent(bgImageUrl, 80, 98, commontypes.ImageType_PNG),
																},
															}),
														},
														{
															Content: getAnyWithoutError(&sections.VerticalListSection{
																VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_TOP,
																VisualProperties: []*properties.VisualProperty{
																	{
																		Properties: &properties.VisualProperty_ContainerProperty{
																			ContainerProperty: properties.GetContainerProperty().
																				WithPadding(8, 8, 0, 0).
																				WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0).
																				WithHeight(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0),
																		},
																	},
																},
																Components: []*components.Component{
																	{
																		Content: getAnyWithoutError(&sections.HorizontalListSection{
																			Components: []*components.Component{getVisualElementComponent(brandLogoUrl, 34, 34, commontypes.ImageType_PNG)},
																			VisualProperties: []*properties.VisualProperty{
																				{
																					Properties: &properties.VisualProperty_ContainerProperty{
																						ContainerProperty: properties.GetContainerProperty().
																							WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0),
																					},
																				},
																			},
																			VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_TOP,
																			HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_START,
																		}),
																	},
																},
															}),
														},
														{
															Content: getAnyWithoutError(&sections.VerticalListSection{
																Components: []*components.Component{
																	{
																		Content: getAnyWithoutError(&sections.HorizontalListSection{
																			Components: []*components.Component{
																				{
																					Content: tag,
																				},
																			},
																			VisualProperties: []*properties.VisualProperty{
																				{
																					Properties: &properties.VisualProperty_ContainerProperty{
																						ContainerProperty: properties.GetContainerProperty().
																							WithPadding(0, 4, 4, 0).
																							WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0).
																							WithHeight(properties.Size_Dimension_DIMENSION_TYPE_WRAP, 0),
																					},
																				},
																			},
																			HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_END,
																		}),
																	},
																},
																VisualProperties: []*properties.VisualProperty{
																	{
																		Properties: &properties.VisualProperty_ContainerProperty{
																			ContainerProperty: properties.GetContainerProperty().
																				WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0).
																				WithHeight(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0),
																		},
																	},
																},
																VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_TOP,
															}),
														},
													},
												}),
											},
											{
												Content: getAnyWithoutError(&sections.HorizontalListSection{
													IsScrollable: false,
													Components: []*components.Component{
														{
															Content: getAnyWithoutError(&commontypes.Text{
																FontColor: "#282828",
																DisplayValue: &commontypes.Text_PlainString{
																	PlainString: title,
																},
																FontStyle: &commontypes.Text_StandardFontStyle{
																	StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
																},
																Alignment: commontypes.Text_ALIGNMENT_LEFT,
																MaxLines:  2,
															}),
														},
													},
													VisualProperties: []*properties.VisualProperty{
														{
															Properties: &properties.VisualProperty_ContainerProperty{
																ContainerProperty: properties.GetContainerProperty().
																	WithPadding(8, 8, 8, 0).
																	WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0).
																	WithHeight(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0),
															},
														},
													},
													VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_TOP,
													HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_START,
												}),
											},
										},
									},
									),
								},
							},
							LoadBehavior: &behaviors.LifecycleBehavior{
								Behavior: &behaviors.LifecycleBehavior_LoadBehavior{},
								AnalyticsEvent: getOfferCardAnalyticsEvent(
									getGenericOfferCardLoadedEventName(offerType),
									offerId,
									deeplink,
									OffersCardSubcomponent,
									originScreen),
							},
							VisibleBehavior: &behaviors.LifecycleBehavior{
								Behavior: &behaviors.LifecycleBehavior_ViewedBehavior{},
								AnalyticsEvent: getOfferCardAnalyticsEvent(
									getGenericOfferCardViewedEventName(offerType),
									offerId,
									deeplink,
									OffersCardSubcomponent,
									originScreen,
								),
							},
						},
					},
				},
			},
		},
	}
}

// v3 card: https://www.figma.com/design/Sqs3y3hNKojUuX0RDbLcg3/%F0%9F%93%8D-Home-Workfile-3?node-id=12984-28271&t=IC84HZQ67O7uJVTt-1
// nolint:funlen
func (m *MappingsManager) getGenericOfferCardV3(
	offerId string,
	offerType OfferTypeAnalytics,
	title string,
	subtitle string,
	bgColor string,
	bgImageUrl string,
	tag *anyPb.Any,
	deeplink *deepLinkPb.Deeplink,
	leftMargin int,
	originScreen deepLinkPb.Screen,
	isFeatureHomeDesignEnhancementsEnabled bool,
) *fePb.GetRewardsAndOffersWidgetResponse_TabData_Card {
	var (
		margin = int32(4)
	)

	if isFeatureHomeDesignEnhancementsEnabled {
		margin = 0
	}

	return &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card{
		Card: &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card_GenericOfferCard_{
			GenericOfferCard: &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card_GenericOfferCard{
				Section: &sections.Section{
					Content: &sections.Section_DepthWiseListSection{
						DepthWiseListSection: &sections.DepthWiseListSection{
							IsScrollable:     false,
							VisualProperties: m.getGenericOfferCardVisualProps(leftMargin, 20, isFeatureHomeDesignEnhancementsEnabled, colorFog),
							Alignment:        sections.DepthWiseListSection_TOP_CENTER,
							InteractionBehaviors: []*behaviors.InteractionBehavior{
								{
									Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
										OnClickBehavior: &behaviors.OnClickBehavior{
											Action: getAnyWithoutError(deeplink),
										},
									},
									AnalyticsEvent: getOfferCardAnalyticsEvent(getGenericOfferCardClickedEventName(offerType), offerId, deeplink, OffersCardSubcomponent, originScreen),
								},
							},
							Components: []*components.Component{
								{
									Content: getAnyWithoutError(&sections.VerticalListSection{
										IsScrollable:        false,
										HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_CENTER_HORIZONTALLY,
										VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_TOP,
										VisualProperties: []*properties.VisualProperty{
											{
												Properties: &properties.VisualProperty_ContainerProperty{
													ContainerProperty: properties.GetContainerProperty().
														WithBlockBgColor(bgColor).
														WithAllCornerRadii(20, 20, 20, 20).
														WithMargin(0, 0, 0, margin).
														WithHeight(properties.Size_Dimension_DIMENSION_TYPE_EXACT, offerWidgetCardHeight).
														WithWidth(properties.Size_Dimension_DIMENSION_TYPE_EXACT, offerWidgetCardWidth),
												},
											},
										},
										Components: []*components.Component{
											{
												Content: getAnyWithoutError(&sections.HorizontalListSection{
													HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_START,
													VisualProperties: []*properties.VisualProperty{
														{
															Properties: &properties.VisualProperty_ContainerProperty{
																ContainerProperty: properties.GetContainerProperty().
																	WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0).
																	WithHeight(properties.Size_Dimension_DIMENSION_TYPE_WRAP, 0).
																	WithMargin(0, 12, 0, 0).
																	WithPadding(12, 0, 12, 0),
															},
														},
													},
													Components: []*components.Component{
														{
															Content: tag,
														},
													},
												}),
											},
											{
												Content: getAnyWithoutError(&sections.HorizontalListSection{
													HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_START,
													VisualProperties: []*properties.VisualProperty{
														{
															Properties: &properties.VisualProperty_ContainerProperty{
																ContainerProperty: properties.GetContainerProperty().
																	WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0).
																	WithHeight(properties.Size_Dimension_DIMENSION_TYPE_WRAP, 0).
																	WithMargin(0, 8, 0, 0).
																	WithPadding(12, 0, 12, 0),
															},
														},
													},
													Components: []*components.Component{
														{
															Content: getAnyWithoutError(
																commontypes.GetTextFromStringFontColourFontStyleFontAlignment(title, colorNeutralsNight, commontypes.FontStyle_HEADLINE_M, commontypes.Text_ALIGNMENT_LEFT).
																	WithMaxLines(1),
															),
														},
													},
												}),
											},
											{
												Content: getAnyWithoutError(&sections.HorizontalListSection{
													HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_START,
													VisualProperties: []*properties.VisualProperty{
														{
															Properties: &properties.VisualProperty_ContainerProperty{
																ContainerProperty: properties.GetContainerProperty().
																	WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0).
																	WithHeight(properties.Size_Dimension_DIMENSION_TYPE_WRAP, 0).
																	WithPadding(12, 0, 12, 0),
															},
														},
													},
													Components: []*components.Component{
														{
															Content: getAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyleFontAlignment(subtitle, colors.ColorMonochromeAsh, commontypes.FontStyle_OVERLINE_3XS_CAPS, commontypes.Text_ALIGNMENT_LEFT).
																WithMaxLines(1),
															),
														},
													},
												}),
											},
											{
												Content: getAnyWithoutError(&sections.HorizontalListSection{
													HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_END,
													VisualProperties: []*properties.VisualProperty{
														{
															Properties: &properties.VisualProperty_ContainerProperty{
																ContainerProperty: properties.GetContainerProperty().
																	WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0).
																	WithHeight(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0).
																	WithMargin(0, 16, 0, 0),
															},
														},
													},
													Components: []*components.Component{
														getVisualElementComponent(bgImageUrl, 74, 136, commontypes.ImageType_PNG),
													},
												}),
											},
										},
									}),
								},
							},
							LoadBehavior: &behaviors.LifecycleBehavior{
								Behavior: &behaviors.LifecycleBehavior_LoadBehavior{},
								AnalyticsEvent: getOfferCardAnalyticsEvent(
									getGenericOfferCardLoadedEventName(offerType),
									offerId,
									deeplink,
									OffersCardSubcomponent,
									originScreen),
							},
							VisibleBehavior: &behaviors.LifecycleBehavior{
								Behavior: &behaviors.LifecycleBehavior_ViewedBehavior{},
								AnalyticsEvent: getOfferCardAnalyticsEvent(
									getGenericOfferCardViewedEventName(offerType),
									offerId,
									deeplink,
									OffersCardSubcomponent,
									originScreen,
								),
							},
						},
					},
				},
			},
		},
	}
}

// v4 card (top offer card): https://www.figma.com/design/Sqs3y3hNKojUuX0RDbLcg3/%F0%9F%93%8D-Home-Workfile-3?node-id=12984-28271&t=IC84HZQ67O7uJVTt-1
// nolint:funlen,gosec
func (m *MappingsManager) getGenericOfferCardV4(
	offerId string,
	offerType OfferTypeAnalytics,
	title string,
	bgColor string,
	imageUrl string,
	tag *anyPb.Any,
	deeplink *deepLinkPb.Deeplink,
	leftMargin int,
	originScreen deepLinkPb.Screen,
	isFeatureHomeDesignEnhancementsEnabled bool,
) *fePb.GetRewardsAndOffersWidgetResponse_TabData_Card {
	var (
		margin = int32(4)
	)

	// Create the container property with conditional border
	containerProperty := properties.GetContainerProperty().
		WithBlockBgColor("#DDD5A0").
		WithAllCornerRadii(20, 20, 20, 20).
		WithMargin(int32(leftMargin), 0, 0, 0).
		WithWidth(properties.Size_Dimension_DIMENSION_TYPE_EXACT, offerWidgetCardWidth)

	// Add border conditionally
	if isFeatureHomeDesignEnhancementsEnabled {
		margin = 0
		containerProperty = containerProperty.WithBorder("#DDD5A0", 20, 1).
			WithHeight(properties.Size_Dimension_DIMENSION_TYPE_EXACT, offerWidgetCardHeight)
	} else {
		containerProperty = containerProperty.WithHeight(properties.Size_Dimension_DIMENSION_TYPE_EXACT, offerWidgetCardHeight+offerWidgetCardShadowHeight)
	}

	return &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card{
		Card: &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card_GenericOfferCard_{
			GenericOfferCard: &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card_GenericOfferCard{
				Section: &sections.Section{
					Content: &sections.Section_DepthWiseListSection{
						DepthWiseListSection: &sections.DepthWiseListSection{
							IsScrollable: false,
							VisualProperties: []*properties.VisualProperty{
								{
									Properties: &properties.VisualProperty_ContainerProperty{
										ContainerProperty: containerProperty,
									},
								},
							},
							Alignment: sections.DepthWiseListSection_TOP_CENTER,
							InteractionBehaviors: []*behaviors.InteractionBehavior{
								{
									Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
										OnClickBehavior: &behaviors.OnClickBehavior{
											Action: getAnyWithoutError(deeplink),
										},
									},
									AnalyticsEvent: getOfferCardAnalyticsEvent(getGenericOfferCardClickedEventName(offerType), offerId, deeplink, OffersCardSubcomponent, originScreen),
								},
							},
							Components: []*components.Component{
								{
									Content: getAnyWithoutError(&sections.VerticalListSection{
										IsScrollable:        false,
										HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_CENTER_HORIZONTALLY,
										VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_TOP,
										VisualProperties: []*properties.VisualProperty{
											{
												Properties: &properties.VisualProperty_ContainerProperty{
													ContainerProperty: properties.GetContainerProperty().
														WithBlockBgColor(bgColor).
														WithAllCornerRadii(20, 20, 20, 20).
														WithMargin(0, 0, 0, margin).
														WithHeight(properties.Size_Dimension_DIMENSION_TYPE_EXACT, offerWidgetCardHeight).
														WithWidth(properties.Size_Dimension_DIMENSION_TYPE_EXACT, offerWidgetCardWidth),
												},
											},
										},
										Components: []*components.Component{
											{
												Content: getAnyWithoutError(&sections.HorizontalListSection{
													HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_START,
													VisualProperties: []*properties.VisualProperty{
														{
															Properties: &properties.VisualProperty_ContainerProperty{
																ContainerProperty: properties.GetContainerProperty().
																	WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0).
																	WithHeight(properties.Size_Dimension_DIMENSION_TYPE_WRAP, 0).
																	WithMargin(0, 12, 0, 0).
																	WithPadding(12, 0, 12, 0),
															},
														},
													},
													Components: []*components.Component{
														{
															Content: tag,
														},
													},
												}),
											},
											{
												Content: getAnyWithoutError(&sections.HorizontalListSection{
													HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_START,
													VisualProperties: []*properties.VisualProperty{
														{
															Properties: &properties.VisualProperty_ContainerProperty{
																ContainerProperty: properties.GetContainerProperty().
																	WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0).
																	WithHeight(properties.Size_Dimension_DIMENSION_TYPE_WRAP, 0).
																	WithMargin(0, 8, 0, 0).
																	WithPadding(12, 0, 12, 0),
															},
														},
													},
													Components: []*components.Component{
														{
															Content: getAnyWithoutError(
																commontypes.GetTextFromStringFontColourFontStyleFontAlignment(title, colorNeutralsNight, commontypes.FontStyle_HEADLINE_M, commontypes.Text_ALIGNMENT_LEFT).
																	WithMaxLines(2),
															),
														},
													},
												}),
											},
											{
												Content: getAnyWithoutError(&sections.HorizontalListSection{
													HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_START,
													VisualProperties: []*properties.VisualProperty{
														{
															Properties: &properties.VisualProperty_ContainerProperty{
																ContainerProperty: properties.GetContainerProperty().
																	WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0).
																	WithHeight(properties.Size_Dimension_DIMENSION_TYPE_WRAP, 0).
																	WithPadding(12, 0, 12, 0).
																	WithMargin(0, 10, 0, 0),
															},
														},
													},
													Components: []*components.Component{
														getVisualElementComponent(imageUrl, 46, 46, commontypes.ImageType_PNG),
													},
												}),
											},
										},
									}),
								},
							},
							LoadBehavior: &behaviors.LifecycleBehavior{
								Behavior: &behaviors.LifecycleBehavior_LoadBehavior{},
								AnalyticsEvent: getOfferCardAnalyticsEvent(
									getGenericOfferCardLoadedEventName(offerType),
									offerId,
									deeplink,
									OffersCardSubcomponent,
									originScreen),
							},
							VisibleBehavior: &behaviors.LifecycleBehavior{
								Behavior: &behaviors.LifecycleBehavior_ViewedBehavior{},
								AnalyticsEvent: getOfferCardAnalyticsEvent(
									getGenericOfferCardViewedEventName(offerType),
									offerId,
									deeplink,
									OffersCardSubcomponent,
									originScreen,
								),
							},
						},
					},
				},
			},
		},
	}
}

// nolint: gosec
func (m *MappingsManager) getGenericOfferCardVisualProps(leftMargin int, cornerRadius int32, isFeatureHomeDesignEnhancementsEnabled bool, borderColor string) []*properties.VisualProperty {
	var genericOfferCardHeight int32
	if isFeatureHomeDesignEnhancementsEnabled {
		genericOfferCardHeight = offerWidgetCardHeight
	} else {
		genericOfferCardHeight = offerWidgetCardHeight + offerWidgetCardShadowHeight
	}
	containerProperty := &properties.ContainerProperty{
		Size: &properties.Size{
			Width: &properties.Size_Dimension{
				Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
				ExactValue: offerWidgetCardWidth,
			},
			Height: &properties.Size_Dimension{
				Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
				ExactValue: genericOfferCardHeight,
			},
		},
		BgColor: widget.GetBlockBackgroundColour("#D9DEE3"),
		Corner: &properties.CornerProperty{
			TopLeftCornerRadius:  cornerRadius,
			TopRightCornerRadius: cornerRadius,
			BottomLeftCorner:     cornerRadius,
			BottomRightCorner:    cornerRadius,
		},
		Margin: &properties.PaddingProperty{
			Left: int32(leftMargin),
		},
		Position: nil,
	}

	// Add border property conditionally
	if isFeatureHomeDesignEnhancementsEnabled {
		containerProperty.Border = &properties.BorderProperty{
			BorderThickness: 1,
			BorderColor:     borderColor, // Light gray border color
			CornerRadius:    cornerRadius,
		}
	}

	return []*properties.VisualProperty{
		{
			Properties: &properties.VisualProperty_ContainerProperty{
				ContainerProperty: containerProperty,
			},
		},
	}
}

func getVisualElementComponent(imageUrl string, height int32, width int32, imageType commontypes.ImageType) *components.Component {
	return &components.Component{
		Content: getAnyWithoutError(commontypes.GetVisualElementImageFromUrl(imageUrl).WithImageType(imageType).WithProperties(&commontypes.VisualElementProperties{Height: height, Width: width})),
	}
}

func (m *MappingsManager) getCountdownTimerTag(countdownTillTime *timestampPb.Timestamp) *components.CountdownTimerTag {
	return &components.CountdownTimerTag{
		Text:              commontypes.GetTextFromStringFontColourFontStyle("ENDS IN", "#D2AC3D", commontypes.FontStyle_OVERLINE_3XS_CAPS),
		Icon:              commontypes.GetVisualElementFromUrlHeightAndWidth(OffersWidgetCountdownTimerImageUrl, 12, 12),
		CountdownTillTime: countdownTillTime,
		BgColor:           widget.GetBlockBackgroundColour("#FFFCEB"),
	}
}

// getActiveStateTab fetches active state of a tab on offers widget
func (m *MappingsManager) GetActiveStateTabByFilterType(tabTitle string, filterType ui.Filter_FilterType) *ui.IconTextComponent {
	switch filterType {
	case ui.Filter_FILTER_TYPE_SWITCH:
		return ui.NewITC().
			WithTexts(commontypes.GetTextFromStringFontColourFontStyle(tabTitle, home.MonochromeNight, commontypes.FontStyle_HEADLINE_M)).
			WithContainerBackgroundColor(home.Snow)
	case ui.Filter_FILTER_TYPE_CHIPS:
		return ui.NewITC().
			WithTexts(commontypes.GetTextFromStringFontColourFontStyleFontAlignment(tabTitle, home.MonochromeNight, commontypes.FontStyle_HEADLINE_M, commontypes.Text_ALIGNMENT_CENTER)).
			WithContainerBackgroundColor(home.Chalk).
			WithContainerPaddingSymmetrical(10, 4).
			WithBorder(home.Chalk, 1)
	}
	return nil
}

// getInactiveStateTab fetches inactive state of a tab on offers widget
func (m *MappingsManager) GetInactiveStateTabByFilterType(tabTitle string, filterType ui.Filter_FilterType) *ui.IconTextComponent {
	switch filterType {
	case ui.Filter_FILTER_TYPE_SWITCH:
		return ui.NewITC().
			WithTexts(commontypes.GetTextFromStringFontColourFontStyle(tabTitle, home.Lead, commontypes.FontStyle_HEADLINE_M)).
			WithContainerBackgroundColor(home.Chalk)
	case ui.Filter_FILTER_TYPE_CHIPS:
		return ui.NewITC().
			WithTexts(commontypes.GetTextFromStringFontColourFontStyleFontAlignment(tabTitle, home.Slate, commontypes.FontStyle_HEADLINE_M, commontypes.Text_ALIGNMENT_CENTER)).
			WithContainerBackgroundColor(home.Snow).
			WithContainerPaddingSymmetrical(10, 4).
			WithBorder(home.Chalk, 1)
	}
	return nil
}

// v2: https://www.figma.com/file/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?type=design&node-id=4287%3A13360&mode=design&t=axNsfJHNc0Hy2NQM-1
func (m *MappingsManager) isV2OfferWidgetEnabled(ctx context.Context) bool {
	return apputils.IsFeatureEnabledFromCtxDynamic(ctx, m.dyconf.RewardsFrontendMeta().CatalogOffersV2WidgetConfig().FeatureConfig())
}

func (m *MappingsManager) isDebitCardOfferWidgetHomeEnabled(ctx context.Context) bool {
	return m.dyconf.RewardsFrontendMeta().IsDebitCardOfferWidgetHomeEnabled(ctx)
}

// v2: https://www.figma.com/file/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?type=design&node-id=4287%3A13360&mode=design&t=axNsfJHNc0Hy2NQM-1
func (m *MappingsManager) isV2CardOfferWidgetEnabled(ctx context.Context) bool {
	return apputils.IsFeatureEnabledFromCtxDynamic(ctx, m.dyconf.RewardsFrontendMeta().CardOffersV2WidgetFeatureConfig())
}

// getSpecialDealOfferCard return discounted offer card if present else return first catalog offer card
func (m *MappingsManager) getSpecialDealOfferCard(ctx context.Context, offerType OfferTypeAnalytics, offersParam *GetTabsDataOffersParam, originScreen deepLinkPb.Screen, leftMargin int, useV4OfferCard bool, isFeatureHomeDesignEnhancementsEnabled bool) *fePb.GetRewardsAndOffersWidgetResponse_TabData_Card {
	var (
		catalogOffers                 = offersParam.FeCatalogOffers
		catalogOfferAdditionalDetails = offersParam.AdditionalDetails.CatalogOffersAdditionalDetails
	)
	if len(catalogOffers) == 0 {
		return nil
	}

	for _, catalogOffer := range catalogOffers {
		_, ok := catalogOffer.GetOfferData().(*fePb.CatalogOfferV1_Offer)
		// currently discount is not supported on exchanger offers
		if !ok {
			continue
		}

		timerTagCountDownTillTime := catalogOffer.GetOffer().GetDisplayDetails().GetCtaLabel().GetCountdownTimer().GetCountdownTillTime()

		if timerTagCountDownTillTime != nil {
			imageUrl := getBrandImageUrlFromFiCoinOffer(catalogOffer.GetOffer().GetDisplayDetails().GetImages())
			tag := getAnyWithoutError(m.getCountdownTimerTag(timerTagCountDownTillTime))
			dl := m.getSpecialDealOfferCardDeeplink(ctx, catalogOffer.GetOffer().GetId(), catalogOfferAdditionalDetails)

			return m.getGenericOfferCard(
				catalogOffer.GetOffer().GetId(),
				offerType,
				catalogOffer.GetOffer().GetDisplayDetails().GetHomeTitle(),
				"#FFF8CE",
				[]string{imageUrl},
				tag,
				dl, leftMargin,
				originScreen,
				isFeatureHomeDesignEnhancementsEnabled)
		}
	}

	/******** return first catalog offer if discounted offer not present *******/
	catalogOffer := catalogOffers[0]

	var imgUrl, offerId, title string
	switch offer := catalogOffer.GetOfferData().(type) {
	case *fePb.CatalogOfferV1_Offer:
		imgUrl = getBrandImageUrlFromFiCoinOffer(offer.Offer.GetDisplayDetails().GetImages())
		offerId = offer.Offer.GetId()
		title = offer.Offer.GetDisplayDetails().GetHomeTitle()
	case *fePb.CatalogOfferV1_ExchangerOffer:
		imgUrl = offer.ExchangerOffer.GetDisplayDetails().GetInfoBannerIconUrl()
		offerId = offer.ExchangerOffer.GetId()
		title = offer.ExchangerOffer.GetDisplayDetails().GetHomeTitle()
	}

	dl := m.getSpecialDealOfferCardDeeplink(ctx, offerId, catalogOfferAdditionalDetails)

	// using CAMPAIGN_SPECIAL tag for showing TOP OFFER tag on home offers in new widget
	// Avoiding addition of new tag just for display tag
	displayTags, _, err := m.tagsManager.GetTagsDetailsOrderedByPriority(ctx, []beCasperPb.TagName{beCasperPb.TagName_CAMPAIGN_SPECIAL}, tags.RenderLocationOffersWidget)
	if err != nil {
		logger.Error(ctx, "error while getting tag display details", zap.String("tag", beCasperPb.TagName_CAMPAIGN_SPECIAL.String()))
	}

	var displayTag *anypb.Any
	if len(displayTags) > 0 {
		logger.DebugNoCtx("display tag details", zap.Any("tag", displayTags[0]))
		displayTag = getAnyWithoutError(displayTags[0])
	}

	if useV4OfferCard {
		return m.getGenericOfferCardV4(
			offerId,
			offerType,
			title,
			"#FFF8CE",
			imgUrl,
			displayTag,
			dl, leftMargin,
			originScreen,
			isFeatureHomeDesignEnhancementsEnabled)
	}

	return m.getGenericOfferCard(
		offerId,
		offerType,
		title,
		"#FFF8CE",
		[]string{imgUrl},
		displayTag,
		dl, leftMargin,
		originScreen,
		isFeatureHomeDesignEnhancementsEnabled)
}

func (m *MappingsManager) getSpecialDealOfferCardDeeplink(ctx context.Context, offerId string, catalogOffersAdditionalDetails *rewardsFrontendPkg.CatalogOffersAdditionalDetails) *deepLinkPb.Deeplink {
	if catalogOffersAdditionalDetails.GetOfferIdToBeOfferMap() != nil {
		beOffer, ok := catalogOffersAdditionalDetails.GetOfferIdToBeOfferMap()[offerId]
		if ok && beOffer.GetVendorName() == beCasperPb.OfferVendor_POSHVINE && rewardsFrontendPkg.IsOffersCatalogPageV2Enabled(ctx, m.dyconf) {
			return &deepLinkPb.Deeplink{
				Screen: deepLinkPb.Screen_CATALOG_OFFER_REDEMPTION_BOTTOM_SHEET,
				ScreenOptionsV2: getAnyWithoutError(&rewardsScreenOptionsPb.CatalogOfferRedemptionBottomSheetScreenOptions{
					BottomSheetData: &rewardsScreenOptionsPb.CatalogOfferRedemptionBottomSheetScreenOptions_OfferRedemptionCustomActionBottomSheetData{
						OfferRedemptionCustomActionBottomSheetData: &rewardsScreenOptionsPb.OfferRedemptionCustomActionBottomSheetData{
							CustomAction: &rewardsFrontendPkgPb.CustomAction{
								ActionType: rewardsFrontendPkgPb.CustomAction_MAKE_API_CALL,
								ActionApi:  rewardsFrontendPkgPb.CustomAction_GET_REDEEM_OFFER_INPUT_SCREEN,
								ActionData: &rewardsFrontendPkgPb.CustomAction_GetRedeemOfferInputScreenApiActionData_{
									GetRedeemOfferInputScreenApiActionData: &rewardsFrontendPkgPb.CustomAction_GetRedeemOfferInputScreenApiActionData{
										OfferId: offerId,
									},
								},
							},
						},
					},
				}),
			}
		}
	}

	return &deepLinkPb.Deeplink{
		Screen: deepLinkPb.Screen_OFFERS_LANDING_SCREEN,
		ScreenOptions: &deepLinkPb.Deeplink_OfferCatalogScreenOptions{
			OfferCatalogScreenOptions: &deepLinkPb.OfferCatalogScreenOptions{
				DisplayFirstOfferIds: []string{offerId},
			},
		},
	}
}

// nolint:dupl
func (m *MappingsManager) getOffersV2WidgetCards(ctx context.Context, fiCoinsBalance uint32, rewardsCount uint32, offersParam *GetTabsDataOffersParam, originScreen deepLinkPb.Screen, showYourRewardsCard bool, isFeatureHomeDesignEnhancementsEnabled bool) []*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card {
	var (
		cards                    []*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card
		firstOfferCardLeftMargin = offerWidgetCardDefaultLeftMargin - 2
	)

	// your rewards card
	if showYourRewardsCard {
		cards = append(cards, m.GetYourRewardsCardV3(fiCoinsBalance, rewardsCount, isFeatureHomeDesignEnhancementsEnabled))
	} else {
		firstOfferCardLeftMargin = 0
	}

	// getSpecialDealOfferCard return discounted offer card if present else return first catalog offer card
	specialDealOfferCard := m.getSpecialDealOfferCard(ctx, CategoryCardOffersAnalytics, offersParam, originScreen, firstOfferCardLeftMargin, false, isFeatureHomeDesignEnhancementsEnabled)
	if specialDealOfferCard != nil {
		cards = append(cards, specialDealOfferCard)
		firstOfferCardLeftMargin = offerWidgetCardDefaultLeftMargin
	}

	// offer cards
	cards = append(cards, m.getOffersV2WidgetCategoryCards(ctx, offersParam.FeCatalogOffers, firstOfferCardLeftMargin, originScreen, isFeatureHomeDesignEnhancementsEnabled)...)

	// view all card
	cards = append(cards, m.GetSduiViewAllOffersCard(
		CategoryCardOffersAnalytics,
		viewAllConversionOffersImageUrl,
		&deepLinkPb.Deeplink{
			Screen: deepLinkPb.Screen_OFFERS_LANDING_SCREEN,
		},
		originScreen,
		isFeatureHomeDesignEnhancementsEnabled))

	return cards
}

// nolint:dupl,ineffassign
func (m *MappingsManager) getOffersV3WidgetCards(ctx context.Context, fiCoinsBalance, rewardsCount uint32, offersParam *GetTabsDataOffersParam, originScreen deepLinkPb.Screen, appPlatform commontypes.Platform, showYourRewardsCard bool, isFeatureHomeDesignEnhancementsEnabled bool) []*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card {
	var (
		cards []*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card
	)

	var firstOfferCardLeftMargin = offerWidgetCardDefaultLeftMargin - 6
	// for android there a default right margin of 6dp for first card
	// for ios default right margin is added only when rewards count is > 0
	if appPlatform == commontypes.Platform_IOS && rewardsCount == 0 {
		firstOfferCardLeftMargin = offerWidgetCardDefaultLeftMargin
	}

	// your rewards card
	if showYourRewardsCard {
		cards = append(cards, m.GetYourRewardsCardV3(fiCoinsBalance, rewardsCount, isFeatureHomeDesignEnhancementsEnabled))
	} else {
		firstOfferCardLeftMargin = 0
	}

	// getSpecialDealOfferCard return discounted offer card if present else return first catalog offer card
	specialDealOfferCard := m.getSpecialDealOfferCard(ctx, CategoryCardOffersAnalytics, offersParam, originScreen, firstOfferCardLeftMargin, true, isFeatureHomeDesignEnhancementsEnabled)
	if specialDealOfferCard != nil {
		cards = append(cards, specialDealOfferCard)
	}
	if len(cards) > 0 {
		firstOfferCardLeftMargin = offerWidgetCardDefaultLeftMargin
	}

	// offer cards
	cards = append(cards, m.getOffersV3WidgetCategoryCards(ctx, offersParam.FeCatalogOffers, firstOfferCardLeftMargin, originScreen, isFeatureHomeDesignEnhancementsEnabled)...)

	// view all card
	cards = append(cards, m.GetSduiViewAllOffersCard(
		CategoryCardOffersAnalytics,
		viewAllConversionOffersImageUrl,
		&deepLinkPb.Deeplink{
			Screen: deepLinkPb.Screen_OFFERS_LANDING_SCREEN,
		},
		originScreen,
		isFeatureHomeDesignEnhancementsEnabled))

	return cards
}

// getOffersV2WidgetCategoryCards fetch category/tag-specific (ex- power-up, play and win etc) cards for offers v2 widget
func (m *MappingsManager) getOffersV2WidgetCategoryCards(ctx context.Context, catalogOffers []*fePb.CatalogOfferV1, firstCardLeftMargin int, originScreen deepLinkPb.Screen, isFeatureHomeDesignEnhancementsEnabled bool) []*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card {
	var cards []*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card

	type cardConfig struct {
		tag    string
		config *genconf.CatalogCardDisplayConfig
	}

	var cardsConfig []*cardConfig
	m.dyconf.RewardsFrontendMeta().CatalogOffersV2WidgetConfig().TagToCardDisplayDetailsMap().Range(func(tag string, config *genconf.CatalogCardDisplayConfig) (continueRange bool) {
		cardsConfig = append(cardsConfig, &cardConfig{
			tag:    tag,
			config: config,
		})

		return true
	})

	sort.Slice(cardsConfig, func(i, j int) bool {
		return cardsConfig[i].config.Rank() < cardsConfig[j].config.Rank()
	})

	// add all offer categories cards
	for _, cardDisplayConfig := range cardsConfig {

		filteredCatalogOffers := getFilteredCatalogOffers(cardDisplayConfig.tag, catalogOffers)
		if len(filteredCatalogOffers) == 0 {
			continue
		}

		var offerImages []string
		for _, catalogOffer := range filteredCatalogOffers {
			if len(offerImages) == 3 {
				break
			}

			var imgUrl string
			switch offer := catalogOffer.GetOfferData().(type) {
			case *fePb.CatalogOfferV1_Offer:
				imgUrl = getBrandImageUrlFromFiCoinOffer(offer.Offer.GetDisplayDetails().GetImages())
			case *fePb.CatalogOfferV1_ExchangerOffer:
				imgUrl = offer.ExchangerOffer.GetDisplayDetails().GetInfoBannerIconUrl()
			}

			if imgUrl != "" {
				offerImages = append(offerImages, imgUrl)
			}
		}

		if len(offerImages) == 0 {
			continue
		}

		displayTags, _, err := m.tagsManager.GetGenericTagsDetailsOrderedByPriority(ctx, []string{cardDisplayConfig.tag}, tags.RenderLocationOffersWidget)
		if err != nil {
			logger.Error(ctx, "error while getting tag display details", zap.String("tag", cardDisplayConfig.tag))
			continue
		}
		var displayTag *anypb.Any
		if len(displayTags) > 0 {
			logger.DebugNoCtx("display tag details", zap.Any("tag", displayTags[0]))
			displayTag = getAnyWithoutError(displayTags[0])
		}

		leftMargin := offerWidgetCardDefaultLeftMargin
		if len(cards) == 0 {
			leftMargin = firstCardLeftMargin
		}

		cards = append(cards, m.getGenericOfferCard(
			cardDisplayConfig.tag,
			CategoryCardOffersAnalytics,
			cardDisplayConfig.config.Title(),
			colorSnow,
			offerImages,
			displayTag,
			&deepLinkPb.Deeplink{
				Screen: deepLinkPb.Screen_OFFERS_LANDING_SCREEN,
				ScreenOptions: &deepLinkPb.Deeplink_OfferCatalogScreenOptions{
					OfferCatalogScreenOptions: &deepLinkPb.OfferCatalogScreenOptions{
						TagFilters: []string{cardDisplayConfig.tag},
					},
				},
			}, leftMargin,
			originScreen,
			isFeatureHomeDesignEnhancementsEnabled))

	}

	return cards
}

// getOffersV2WidgetCategoryCards fetch category/tag-specific (ex- power-up, play and win etc) cards for offers v2 widget
func (m *MappingsManager) getOffersV3WidgetCategoryCards(ctx context.Context, catalogOffers []*fePb.CatalogOfferV1, firstCardLeftMargin int, originScreen deepLinkPb.Screen, isFeatureHomeDesignEnhancementsEnabled bool) []*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card {
	var cards []*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card

	type cardConfig struct {
		tag    string
		config *genconf.CatalogCardDisplayConfig
	}

	var cardsConfig []*cardConfig
	m.dyconf.RewardsFrontendMeta().CatalogOffersV2WidgetConfig().TagToCardDisplayDetailsMap().Range(func(tag string, config *genconf.CatalogCardDisplayConfig) (continueRange bool) {
		cardsConfig = append(cardsConfig, &cardConfig{
			tag:    tag,
			config: config,
		})

		return true
	})

	sort.Slice(cardsConfig, func(i, j int) bool {
		return cardsConfig[i].config.Rank() < cardsConfig[j].config.Rank()
	})

	// add all offer categories cards
	for _, cardDisplayConfig := range cardsConfig {
		var subtitle string
		filteredCatalogOffers := getFilteredCatalogOffers(cardDisplayConfig.tag, catalogOffers)
		if len(filteredCatalogOffers) == 0 {
			continue
		}

		subtitle = fmt.Sprintf("%d OFFERS", len(filteredCatalogOffers))

		displayTags, _, err := m.tagsManager.GetGenericTagsDetailsOrderedByPriority(ctx, []string{cardDisplayConfig.tag}, tags.RenderLocationOffersWidget)
		if err != nil {
			logger.Error(ctx, "error while getting tag display details", zap.String("tag", cardDisplayConfig.tag))
			continue
		}

		var displayTag *anypb.Any
		if len(displayTags) > 0 {
			logger.DebugNoCtx("display tag details", zap.Any("tag", displayTags[0]))
			displayTag = getAnyWithoutError(displayTags[0])
		}

		leftMargin := offerWidgetCardDefaultLeftMargin
		if len(cards) == 0 {
			leftMargin = firstCardLeftMargin
		}

		cards = append(cards, m.getGenericOfferCardV3(
			cardDisplayConfig.tag,
			CategoryCardOffersAnalytics,
			cardDisplayConfig.config.Title(),
			subtitle,
			colorSnow,
			cardDisplayConfig.config.ImgUrl(),
			displayTag,
			&deepLinkPb.Deeplink{
				Screen: deepLinkPb.Screen_OFFERS_LANDING_SCREEN,
				ScreenOptions: &deepLinkPb.Deeplink_OfferCatalogScreenOptions{
					OfferCatalogScreenOptions: &deepLinkPb.OfferCatalogScreenOptions{
						TagFilters: []string{cardDisplayConfig.tag},
					},
				},
			}, leftMargin,
			originScreen,
			isFeatureHomeDesignEnhancementsEnabled))

	}

	return cards
}

// nolint: dupl
func (m *MappingsManager) getShopTabCards(ctx context.Context, fiCoinsBalance uint32, rewardsCount uint32, offersParam *GetTabsDataOffersParam, originScreen deepLinkPb.Screen, isFeatureHomeDesignEnhancementsEnabled bool) []*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card {
	var (
		cards                   []*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card
		trimFirstCardLeftMargin = true
	)

	// your rewards card
	cards = append(cards, m.GetYourRewardsCardV3(fiCoinsBalance, rewardsCount, isFeatureHomeDesignEnhancementsEnabled))

	logger.DebugNoCtx("widget start of map")

	// getSpecialDealOfferCard return discounted offer card if present else return first catalog offer card
	specialDealOfferCard := m.getSpecialDealOfferCard(ctx, CategoryCardOffersAnalytics, offersParam, originScreen, offerWidgetCardDefaultLeftMargin-2, false, isFeatureHomeDesignEnhancementsEnabled)
	if specialDealOfferCard != nil {
		cards = append(cards, specialDealOfferCard)
		trimFirstCardLeftMargin = false
	}

	// offer cards
	cards = append(cards, m.getShopTabOfferCards(ctx, offersParam.FeCatalogOffers, trimFirstCardLeftMargin, originScreen, isFeatureHomeDesignEnhancementsEnabled)...)

	// view all card
	cards = append(cards, m.GetSduiViewAllOffersCard(
		CategoryCardOffersAnalytics,
		viewAllShoppingOffersImageUrl,
		&deepLinkPb.Deeplink{
			Screen: deepLinkPb.Screen_OFFERS_LANDING_SCREEN,
		},
		originScreen,
		isFeatureHomeDesignEnhancementsEnabled))

	return cards
}

func (m *MappingsManager) getShopTabOfferCards(ctx context.Context, catalogOffers []*fePb.CatalogOfferV1, trimFirstCardLeftMargin bool, originScreen deepLinkPb.Screen, isFeatureHomeDesignEnhancementsEnabled bool) []*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card {
	var cards []*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card

	type cardConfig struct {
		tag    string
		config *genconf.CatalogCardDisplayConfig
	}

	var cardsConfig []*cardConfig
	m.dyconf.RewardsFrontendMeta().CatalogOffersV2WidgetConfig().ShopOffersTagToCardDisplayDetailsMap().Range(func(tag string, config *genconf.CatalogCardDisplayConfig) (continueRange bool) {
		cardsConfig = append(cardsConfig, &cardConfig{
			tag:    tag,
			config: config,
		})

		return true
	})

	sort.Slice(cardsConfig, func(i, j int) bool {
		return cardsConfig[i].config.Rank() < cardsConfig[j].config.Rank()
	})

	// add all offer categories cards
	for _, cardDisplayConfig := range cardsConfig {
		logger.DebugNoCtx("widget inside map", zap.String("tag", cardDisplayConfig.tag))

		filteredOffers := getFilteredFiCoinOffers(cardDisplayConfig.tag, catalogOffers)
		if len(filteredOffers) == 0 {
			continue
		}

		var offerImages []string
		for _, offer := range filteredOffers {
			if len(offerImages) == 3 {
				break
			}
			imgUrl := getBrandImageUrlFromFiCoinOffer(offer.GetDisplayDetails().GetImages())
			if imgUrl != "" && strings.Contains(imgUrl, "epifi-icons") {
				offerImages = append(offerImages, imgUrl)
			}
		}

		if len(offerImages) == 0 {
			continue
		}

		displayTags, _, err := m.tagsManager.GetGenericTagsDetailsOrderedByPriority(ctx, []string{cardDisplayConfig.tag}, tags.RenderLocationOffersWidget)
		if err != nil {
			logger.Error(ctx, "error while getting tag display details", zap.Error(err))
			continue
		}
		var displayTag *anypb.Any
		if len(displayTags) > 0 {
			logger.DebugNoCtx("display tag details", zap.Any("tag", displayTags[0]))
			displayTag = getAnyWithoutError(displayTags[0])
		}

		leftMargin := offerWidgetCardDefaultLeftMargin
		// gap b/w yourRewardsCard and first card is more than the default, so reducing the margin
		if len(cards) == 0 && trimFirstCardLeftMargin {
			leftMargin -= 2
		}

		title := cardDisplayConfig.config.Title()
		if cardDisplayConfig.tag == "CATEGORY_TAG_SHOPPING" {
			title = accrual.ReplaceCoinWithPointIfApplicable(fmt.Sprintf("Start shopping with %d Fi-Coins", accrual.ConvertFiCoinsToFiPointsIfApplicable(150, false, nil)))
		}

		cards = append(cards, m.getGenericOfferCard(
			cardDisplayConfig.tag,
			CategoryCardOffersAnalytics,
			title,
			colorSnow,
			offerImages,
			displayTag,
			&deepLinkPb.Deeplink{
				Screen: deepLinkPb.Screen_OFFERS_LANDING_SCREEN,
				ScreenOptions: &deepLinkPb.Deeplink_OfferCatalogScreenOptions{
					OfferCatalogScreenOptions: &deepLinkPb.OfferCatalogScreenOptions{
						TagFilters: []string{cardDisplayConfig.tag},
					},
				},
			}, leftMargin,
			originScreen, isFeatureHomeDesignEnhancementsEnabled))
	}

	return cards
}

func (m *MappingsManager) getConvertTabCards(ctx context.Context, fiCoinsBalance uint32, rewardsCount uint32, catalogOffers []*fePb.CatalogOfferV1, originScreen deepLinkPb.Screen, isFeatureHomeDesignEnhancementsEnabled bool) []*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card {
	var cards []*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card

	// your rewards card
	cards = append(cards, m.GetYourRewardsCardV3(fiCoinsBalance, rewardsCount, isFeatureHomeDesignEnhancementsEnabled))

	// offer cards
	cards = append(cards, m.getConvertTabOfferCards(ctx, catalogOffers, originScreen, isFeatureHomeDesignEnhancementsEnabled)...)

	// view all card
	cards = append(cards, m.GetSduiViewAllOffersCard(
		CatalogOffersAnalytics,
		viewAllConversionOffersImageUrl,
		&deepLinkPb.Deeplink{
			Screen: deepLinkPb.Screen_OFFERS_LANDING_SCREEN,
		},
		originScreen,
		isFeatureHomeDesignEnhancementsEnabled))

	return cards
}

func (m *MappingsManager) getConvertTabOfferCards(ctx context.Context, catalogOffers []*fePb.CatalogOfferV1, originScreen deepLinkPb.Screen, isFeatureHomeDesignEnhancementsEnabled bool) []*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card {
	var cards []*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card

	logger.DebugNoCtx("widget start of map")

	type cardConfig struct {
		tag    string
		config *genconf.CatalogCardDisplayConfig
	}

	var cardsConfig []*cardConfig
	m.dyconf.RewardsFrontendMeta().CatalogOffersV2WidgetConfig().ConvertOffersTagToCardDisplayDetailsMap().Range(func(tag string, config *genconf.CatalogCardDisplayConfig) (continueRange bool) {
		cardsConfig = append(cardsConfig, &cardConfig{
			tag:    tag,
			config: config,
		})

		return true
	})

	sort.Slice(cardsConfig, func(i, j int) bool {
		return cardsConfig[i].config.Rank() < cardsConfig[j].config.Rank()
	})

	// add all offer categories cards
	for _, cardDisplayConfig := range cardsConfig {
		logger.DebugNoCtx("widget inside map", zap.String("tag", cardDisplayConfig.tag))

		filteredCatalogOffers := getFilteredCatalogOffers(cardDisplayConfig.tag, catalogOffers)
		if len(filteredCatalogOffers) == 0 {
			continue
		}

		var offerImages []string
		for _, catalogOffer := range filteredCatalogOffers {
			if len(offerImages) == 3 {
				break
			}

			var imgUrl string
			switch offer := catalogOffer.GetOfferData().(type) {
			case *fePb.CatalogOfferV1_Offer:
				imgUrl = getBrandImageUrlFromFiCoinOffer(offer.Offer.GetDisplayDetails().GetImages())
			case *fePb.CatalogOfferV1_ExchangerOffer:
				imgUrl = offer.ExchangerOffer.GetDisplayDetails().GetInfoBannerIconUrl()
			}

			if imgUrl != "" {
				offerImages = append(offerImages, imgUrl)
			}
		}

		if len(offerImages) == 0 {
			continue
		}

		displayTags, _, err := m.tagsManager.GetGenericTagsDetailsOrderedByPriority(ctx, []string{cardDisplayConfig.tag}, tags.RenderLocationOffersWidget)
		if err != nil {
			logger.Error(ctx, "error while getting tag display details", zap.String("tag", cardDisplayConfig.tag))
			continue
		}
		var displayTag *anypb.Any
		if len(displayTags) > 0 {
			logger.DebugNoCtx("display tag details", zap.Any("tag", displayTags[0]))
			displayTag = getAnyWithoutError(displayTags[0])
		}

		leftMargin := offerWidgetCardDefaultLeftMargin
		if len(cards) == 0 {
			leftMargin -= 2
		}

		cards = append(cards, m.getGenericOfferCard(
			cardDisplayConfig.tag,
			CategoryCardOffersAnalytics,
			cardDisplayConfig.config.Title(),
			colorSnow,
			offerImages,
			displayTag,
			&deepLinkPb.Deeplink{
				Screen: deepLinkPb.Screen_OFFERS_LANDING_SCREEN,
				ScreenOptions: &deepLinkPb.Deeplink_OfferCatalogScreenOptions{
					OfferCatalogScreenOptions: &deepLinkPb.OfferCatalogScreenOptions{
						TagFilters: []string{cardDisplayConfig.tag},
					},
				},
			}, leftMargin,
			originScreen, isFeatureHomeDesignEnhancementsEnabled))

	}

	return cards
}

func getFilteredFiCoinOffers(filterTag string, catalogOffers []*fePb.CatalogOfferV1) []*fePb.Offer {
	var filteredOffers []*fePb.Offer
	var offerIds string
	for _, catalogOffer := range catalogOffers {
		offer, ok := catalogOffer.GetOfferData().(*fePb.CatalogOfferV1_Offer)
		if !ok {
			continue
		}
		offerIds += fmt.Sprintf("'%s',", catalogOffer.GetOffer().GetId())
		logger.DebugNoCtx("catalog offer tags", zap.Any("tags", catalogOffer.GetAllTags()), zap.String("filterTag", filterTag))

		if !lo.Contains(catalogOffer.GetAllTags(), filterTag) && catalogOffer.GetCategoryTag() != filterTag {
			continue
		}

		if offer.Offer != nil {
			filteredOffers = append(filteredOffers, offer.Offer)
		}
	}

	logger.DebugNoCtx("catalog offer ids", zap.String("ids", offerIds))

	return filteredOffers
}

func getFilteredCatalogOffers(filterTag string, catalogOffers []*fePb.CatalogOfferV1) []*fePb.CatalogOfferV1 {
	var filteredOffers []*fePb.CatalogOfferV1
	for _, catalogOffer := range catalogOffers {
		if !lo.Contains(catalogOffer.GetAllTags(), filterTag) && catalogOffer.GetCategoryTag() != filterTag {
			continue
		}

		filteredOffers = append(filteredOffers, catalogOffer)
	}

	return filteredOffers
}

// Returns an Analytics event object constructed for the given offer id, using the eventName and other properties
func getOfferCardAnalyticsEvent(eventName string, offerId string, deeplink *deepLinkPb.Deeplink, subcomponentName OfferCardSubcomponentName, originScreen deepLinkPb.Screen) *analytics.AnalyticsEvent {
	return &analytics.AnalyticsEvent{
		EventName: eventName,
		Properties: map[string]string{
			"origin_screen":      originScreen.String(),
			"offer_id":           offerId,
			"redirection_screen": deeplink.GetScreen().String(),
			"subcomponent_name":  string(subcomponentName),
		},
	}
}

// Returns the event name to be used in client offer card clicks, based on the Offer widget type
func getGenericOfferCardClickedEventName(offerType OfferTypeAnalytics) string {
	var eventName string
	switch offerType {
	case DebitCardOffersAnalytics, CreditCardOffersAnalytics:
		eventName = "ClickedOffersWidget"
	default:
		eventName = "ClickedRewardsWidget"
	}
	return eventName
}

// Returns the event name to be used in client offer card is loaded in the Ui, based on the Offer widget type
func getGenericOfferCardLoadedEventName(offerType OfferTypeAnalytics) string {
	var eventName string
	switch offerType {
	case DebitCardOffersAnalytics, CreditCardOffersAnalytics:
		eventName = "LoadedOffersWidget"
	default:
		eventName = "LoadedRewardsWidget"
	}
	return eventName
}

// Returns the event name to be used in client offer card is loaded in the Ui, based on the Offer widget type
func getGenericOfferCardViewedEventName(offerType OfferTypeAnalytics) string {
	var eventName string
	switch offerType {
	case DebitCardOffersAnalytics, CreditCardOffersAnalytics:
		eventName = "ViewedOffersWidget"
	default:
		eventName = "ViewedRewardsWidget"
	}
	return eventName
}

func getAnyWithoutError(msg proto.Message) *anyPb.Any {
	res, _ := anyPb.New(msg)
	return res
}
