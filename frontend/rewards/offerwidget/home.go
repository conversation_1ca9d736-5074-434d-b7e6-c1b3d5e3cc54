// nolint:dupl
package offerwidget

import (
	"context"
	"math"

	feHomePb "github.com/epifi/gamma/api/frontend/home"
	beNetWorthPb "github.com/epifi/gamma/api/insights/networth"
	"github.com/epifi/gamma/frontend/pkg/common"
	"github.com/epifi/gamma/frontend/pkg/featureflags"
	"github.com/epifi/gamma/pkg/feature/release"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/epifi/be-common/pkg/cfg"

	deepLinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	fePb "github.com/epifi/gamma/api/frontend/rewards"
	"github.com/epifi/gamma/api/typesv2/ui"
	typesUi "github.com/epifi/gamma/api/typesv2/ui"

	onboardingPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/frontend/home"
	"github.com/epifi/gamma/frontend/rewards/tags"
	"github.com/epifi/gamma/pkg/accrual"
	pkgUser "github.com/epifi/gamma/pkg/user"
	questSdk "github.com/epifi/gamma/quest/sdk"
)

type HomeOfferWidgetGenerator struct {
	mappingsManager      *MappingsManager
	releaseEvaluator     release.IEvaluator
	onboardingClient     onboardingPb.OnboardingClient
	questSdkClient       *questSdk.Client
	userAttributeFetcher pkgUser.UserAttributesFetcher
	networthClient       beNetWorthPb.NetWorthClient
}

func NewHomeOfferWidgetGenerator(
	mappingsManager *MappingsManager,
	releaseEvaluator release.IEvaluator,
	onboardingClient onboardingPb.OnboardingClient,
	questSdkClient *questSdk.Client,
	userAttributeFetcher pkgUser.UserAttributesFetcher,
	networthClient beNetWorthPb.NetWorthClient,
) *HomeOfferWidgetGenerator {
	return &HomeOfferWidgetGenerator{
		mappingsManager:      mappingsManager,
		releaseEvaluator:     releaseEvaluator,
		onboardingClient:     onboardingClient,
		questSdkClient:       questSdkClient,
		userAttributeFetcher: userAttributeFetcher,
		networthClient:       networthClient,
	}
}

func (h *HomeOfferWidgetGenerator) GetOfferTypes(ctx context.Context) []OfferType {
	return []OfferType{CatalogOffers, DebitCardOffers, CreditCardOffers}
}

func (h *HomeOfferWidgetGenerator) GetTitle(ctx context.Context) *commontypes.Text {
	newHomeUiRevamp := cfg.IsFeatureEnabledOnPlatform(ctx, h.mappingsManager.dyconf.HomeRevampParams().HomeLayoutUIRevamp())
	if newHomeUiRevamp {
		return nil
	}
	return &commontypes.Text{
		DisplayValue: &commontypes.Text_PlainString{
			PlainString: "Rewards & Offers",
		},
		FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_M},
	}
}

// nolint:funlen
func (h *HomeOfferWidgetGenerator) GetTabsData(ctx context.Context, actorId string, fiCoinsBalance uint32, rewardsCount uint32, offers *GetTabsDataOffersParam, creditCardTypeId string, appPlatform commontypes.Platform, appVersionCode uint32, sessionId string, originScreen deepLinkPb.Screen) (*typesUi.Filter, map[string]*fePb.GetRewardsAndOffersWidgetResponse_TabData, bool) {
	// generate tabs
	tabs := []*typesUi.Tab{
		{
			Id:          catalogOffersTabId,
			ActiveCta:   h.getActiveStateTab(ctx, catalogOffersTabText, fiCoinsTabOffersWidgetImageUrl),
			InactiveCta: h.getInactiveStateTab(ctx, catalogOffersTabText, fiCoinsTabOffersWidgetImageUrl),
			BorderColor: feHomePb.GetHomeWidgetBorderColor(),
		},
		{
			Id:          cardOffersTabId,
			ActiveCta:   h.getActiveStateTab(ctx, cardOffersTabText, debitCardTabOffersWidgetImageUrl),
			InactiveCta: h.getInactiveStateTab(ctx, cardOffersTabText, debitCardTabOffersWidgetImageUrl),
			BorderColor: feHomePb.GetHomeWidgetBorderColor(),
		},
	}

	// generate tab data map
	var (
		homeFiCardOffers          []*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card
		homeCatalogOffers         []*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card
		cardOffersCatalogDeeplink = &deepLinkPb.Deeplink{
			Screen: deepLinkPb.Screen_DEBIT_CARD_OFFERS_HOME_SCREEN,
		}
		isFeatureHomeDesignEnhancementsEnabled = featureflags.IsFeatureHomeDesignEnhancementsEnabled(ctx, &featureflags.IsFeatureHomeDesignEnhancementsEnabledRequest{
			ActorId: actorId,
			ExternalDeps: &common.ExternalDependencies{
				Evaluator:            h.releaseEvaluator,
				OnboardingClient:     h.onboardingClient,
				QuestSdkClient:       h.questSdkClient,
				UserAttributeFetcher: h.userAttributeFetcher,
				NetWorthClient:       h.networthClient,
			},
		})
	)

	// convert to card offers for widget and keep first n offers
	homeFiDebitCardOffers := h.mappingsManager.GetFiCardOfferCards(ctx, offers.BeDebitCardOffers, tags.RenderLocationHomeScreen, appPlatform, appVersionCode)
	maxFiCardOffers := int32(math.Min(float64(len(homeFiDebitCardOffers)), 10))
	homeFiCardOffers = homeFiDebitCardOffers[:maxFiCardOffers]

	// todo (yuvraj): refactor this post force upgrade
	if appPlatform == commontypes.Platform_ANDROID && appVersionCode >= h.mappingsManager.dyconf.RewardsFrontendMeta().MinAndroidAppVersionSupportingCardOfferCatalogV2() ||
		appPlatform == commontypes.Platform_IOS && appVersionCode >= h.mappingsManager.dyconf.RewardsFrontendMeta().MinIosAppVersionSupportingCardOfferCatalogV2() {
		cardOffersCatalogDeeplink = &deepLinkPb.Deeplink{
			Screen: deepLinkPb.Screen_CARD_OFFERS_CATALOG_SCREEN,
		}
	}

	// populate credit card offers if supported on the app
	if appPlatform == commontypes.Platform_ANDROID && appVersionCode >= h.mappingsManager.dyconf.RewardsFrontendMeta().MinAndroidAppVersionSupportingCreditCardOffersOnHome() ||
		appPlatform == commontypes.Platform_IOS && appVersionCode >= h.mappingsManager.dyconf.RewardsFrontendMeta().MinIosAppVersionSupportingCreditCardOffersOnHome() {
		homeFiCreditCardOffers := h.mappingsManager.GetFiCardOfferCards(ctx, offers.BeCreditCardOffers, tags.RenderLocationHomeScreen, appPlatform, appVersionCode)
		maxFiCardOffers = int32(math.Min(float64(len(homeFiDebitCardOffers)+len(homeFiCreditCardOffers)), 10))

		var creditCardOfferIter, debitCardOfferIter int
		var fiCardOfferCards []*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card
		for i := 0; i < int(maxFiCardOffers); i++ {
			// adding debit card and credit card offers alternatively
			switch i % 2 {
			case 0:
				if debitCardOfferIter < len(homeFiDebitCardOffers) {
					fiCardOfferCards = append(fiCardOfferCards, homeFiDebitCardOffers[debitCardOfferIter])
					debitCardOfferIter++
				}
			case 1:
				if creditCardOfferIter < len(homeFiCreditCardOffers) {
					fiCardOfferCards = append(fiCardOfferCards, homeFiCreditCardOffers[creditCardOfferIter])
					creditCardOfferIter++
				}
			}
		}
		homeFiCardOffers = fiCardOfferCards
	}

	// convert to catalog offers for widget and interleave fi coin and exchanger offers according to config
	homeFiCoinOffers, homeExchangerOffers := h.mappingsManager.GetFiCoinOfferAndExchangerOfferCards(ctx, offers.FeCatalogOffers)
	var fiCoinOfferIter, exchangerOfferIter int
	// todo (himanshu) move this to config if needed
	orderedOfferTypes := []string{offerTypeNonCbr, offerTypeCbr, offerTypeNonCbr, offerTypeCbr, offerTypeNonCbr, offerTypeCbr, offerTypeNonCbr, offerTypeCbr, offerTypeNonCbr, offerTypeCbr}
	for i := 0; i < len(orderedOfferTypes); i++ {
		offerType := orderedOfferTypes[i]
		switch offerType {
		case offerTypeNonCbr:
			if fiCoinOfferIter < len(homeFiCoinOffers) {
				homeCatalogOffers = append(homeCatalogOffers, homeFiCoinOffers[fiCoinOfferIter])
				fiCoinOfferIter++
			}
		case offerTypeCbr:
			if exchangerOfferIter < len(homeExchangerOffers) {
				homeCatalogOffers = append(homeCatalogOffers, homeExchangerOffers[exchangerOfferIter])
				exchangerOfferIter++
			}
		}
	}

	// construct tab data for catalog offers
	catalogOffersTabData := make([]*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card, 0)
	if appPlatform == commontypes.Platform_ANDROID && appVersionCode >= h.mappingsManager.dyconf.RewardsFrontendMeta().MinAndroidAppVersionSupportingYourRewardsCardV2() ||
		appPlatform == commontypes.Platform_IOS && appVersionCode >= h.mappingsManager.dyconf.RewardsFrontendMeta().MinIosAppVersionSupportingYourRewardsCardV2() {
		catalogOffersTabData = append(catalogOffersTabData, h.mappingsManager.GetYourRewardsCardV2ForCatalogOffersTab(fiCoinsBalance, rewardsCount))
	} else {
		catalogOffersTabData = append(catalogOffersTabData, h.mappingsManager.GetYourRewardsCard(int(fiCoinsBalance)))
	}
	catalogOffersTabData = append(catalogOffersTabData, homeCatalogOffers...)
	catalogOffersTabData = append(catalogOffersTabData, h.mappingsManager.GetViewAllOffersCard(viewAllCatalogOffersImageUrl, &deepLinkPb.Deeplink{
		Screen: deepLinkPb.Screen_OFFERS_LANDING_SCREEN,
	}))

	// construct tab data for card offers
	cardOffersTabData := make([]*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card, 0)
	if appPlatform == commontypes.Platform_ANDROID && appVersionCode >= h.mappingsManager.dyconf.RewardsFrontendMeta().MinAndroidAppVersionSupportingYourRewardsCardV2() ||
		appPlatform == commontypes.Platform_IOS && appVersionCode >= h.mappingsManager.dyconf.RewardsFrontendMeta().MinIosAppVersionSupportingYourRewardsCardV2() {
		cardOffersTabData = append(cardOffersTabData, h.mappingsManager.GetYourRewardsCardV2ForCardOffersTab())
	} else {
		cardOffersTabData = append(cardOffersTabData, h.mappingsManager.GetYourRewardsCard(int(fiCoinsBalance)))
	}
	cardOffersTabData = append(cardOffersTabData, homeFiCardOffers...)
	cardOffersTabData = append(cardOffersTabData, h.mappingsManager.GetViewAllOffersCard(viewAllCardOffersImageUrl, cardOffersCatalogDeeplink))

	tabDataMap := map[string]*fePb.GetRewardsAndOffersWidgetResponse_TabData{
		catalogOffersTabId: {
			TopCta: h.mappingsManager.GetSeeAllCta("MY REWARDS", &deepLinkPb.Deeplink{Screen: deepLinkPb.Screen_MY_REWARDS_SCREEN}, isFeatureHomeDesignEnhancementsEnabled),
			Cards:  catalogOffersTabData,
		},
		cardOffersTabId: {
			TopCta: h.mappingsManager.GetSeeAllCta("MY REWARDS", &deepLinkPb.Deeplink{Screen: deepLinkPb.Screen_MY_REWARDS_SCREEN}, isFeatureHomeDesignEnhancementsEnabled),
			Cards:  cardOffersTabData,
		},
	}

	filter := &typesUi.Filter{
		Tabs:              tabs,
		FilterType:        getTabsFilterType(isFeatureHomeDesignEnhancementsEnabled),
		DefaultTabSection: catalogOffersTabId,
	}

	return filter, tabDataMap, true
}

func (h *HomeOfferWidgetGenerator) GetBottomCta(appPlatform commontypes.Platform, appVersionCode uint32) *ui.IconTextComponent {
	if appPlatform == commontypes.Platform_ANDROID && appVersionCode >= h.mappingsManager.dyconf.RewardsFrontendMeta().MinAndroidAppVersionSupportingYourRewardsCardV2() ||
		appPlatform == commontypes.Platform_IOS && appVersionCode >= h.mappingsManager.dyconf.RewardsFrontendMeta().MinIosAppVersionSupportingYourRewardsCardV2() {
		return nil
	}
	return &ui.IconTextComponent{
		LeftIcon: &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/rewards/clipboard.png"},
		Texts: []*commontypes.Text{
			{
				FontColor:    colorMonochromeLead,
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
				DisplayValue: &commontypes.Text_PlainString{PlainString: accrual.ReplaceCoinWithPointIfApplicable("Learn how to earn more Fi Coins", nil)},
			},
		},
		RightIcon: &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/rewards/chevron-right-deep-lemon.png"},
		Deeplink:  &deepLinkPb.Deeplink{Screen: deepLinkPb.Screen_REWARDS_WAYS_TO_EARN},
		ContainerProperties: &ui.IconTextComponent_ContainerProperties{
			BgColor:      colorPastelLemon,
			CornerRadius: 20,
		},
	}
}

// getActiveStateTab fetches active state of a tab on rewards and offers widget
func (h *HomeOfferWidgetGenerator) getActiveStateTab(ctx context.Context, displayText string, imgUrl string) *ui.IconTextComponent {
	newHomeUiRevamp := cfg.IsFeatureEnabledOnPlatform(ctx, h.mappingsManager.dyconf.HomeRevampParams().HomeLayoutUIRevamp())
	bgColor := home.Snow
	fontStyle := commontypes.FontStyle_HEADLINE_M
	fontColor := home.MonochromeNight
	if !newHomeUiRevamp {
		fontColor = colorNight
		fontStyle = commontypes.FontStyle_SUBTITLE_XS
		bgColor = colorSnow
	}
	iconTextComponent := &ui.IconTextComponent{
		Texts: []*commontypes.Text{
			{
				FontColor:    fontColor,
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: fontStyle},
				DisplayValue: &commontypes.Text_PlainString{PlainString: displayText},
			},
		},
		LeftImgTxtPadding: 4,
		ContainerProperties: &ui.IconTextComponent_ContainerProperties{
			BgColor: bgColor,
		},
	}
	if !newHomeUiRevamp {
		iconTextComponent.LeftIcon = &commontypes.Image{
			ImageUrl: imgUrl,
			Width:    18,
			Height:   18,
		}
	}
	return iconTextComponent
}

// getInactiveStateTab fetches inactive state of a tab on rewards and offers widget
func (h *HomeOfferWidgetGenerator) getInactiveStateTab(ctx context.Context, displayText string, imgUrl string) *ui.IconTextComponent {
	newHomeUiRevamp := cfg.IsFeatureEnabledOnPlatform(ctx, h.mappingsManager.dyconf.HomeRevampParams().HomeLayoutUIRevamp())
	bgColor := home.Chalk
	fontStyle := commontypes.FontStyle_HEADLINE_M
	fontColor := home.Lead
	if !newHomeUiRevamp {
		fontColor = colorLead
		fontStyle = commontypes.FontStyle_SUBTITLE_XS
		bgColor = colorFog
	}
	iconTextComponent := &ui.IconTextComponent{
		Texts: []*commontypes.Text{
			{
				FontColor:    fontColor,
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: fontStyle},
				DisplayValue: &commontypes.Text_PlainString{PlainString: displayText},
			},
		},
		LeftImgTxtPadding: 4,
		ContainerProperties: &ui.IconTextComponent_ContainerProperties{
			BgColor: bgColor,
		},
	}
	if !newHomeUiRevamp {
		iconTextComponent.LeftIcon = &commontypes.Image{
			ImageUrl: imgUrl,
			Width:    18,
			Height:   18,
		}
	}
	return iconTextComponent
}
