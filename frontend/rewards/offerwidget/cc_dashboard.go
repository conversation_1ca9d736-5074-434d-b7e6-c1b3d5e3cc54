// nolint:dupl
package offerwidget

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"

	deepLinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	fePb "github.com/epifi/gamma/api/frontend/rewards"
	"github.com/epifi/gamma/api/typesv2/ui"
	typesUi "github.com/epifi/gamma/api/typesv2/ui"
	accrualPkg "github.com/epifi/gamma/pkg/accrual"
)

type CCDashboardOfferWidgetGenerator struct {
	mappingsManager *MappingsManager
}

func NewCCDashboardOfferWidgetGenerator(mappingsManager *MappingsManager) *CCDashboardOfferWidgetGenerator {
	return &CCDashboardOfferWidgetGenerator{
		mappingsManager: mappingsManager,
	}
}

func (c *CCDashboardOfferWidgetGenerator) GetOfferTypes(ctx context.Context) []OfferType {
	return []OfferType{CatalogOffers}
}

func (c *CCDashboardOfferWidgetGenerator) GetTitle(ctx context.Context) *commontypes.Text {
	return &commontypes.Text{
		DisplayValue: &commontypes.Text_PlainString{
			PlainString: accrualPkg.ReplaceCoinWithPointIfApplicable("Spend Fi-Coins here", nil),
		},
		FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
	}
}

func (c *CCDashboardOfferWidgetGenerator) GetTabsData(ctx context.Context, actorId string, fiCoinsBalance uint32, rewardsCount uint32, offers *GetTabsDataOffersParam, creditCardTypeId string, appPlatform commontypes.Platform, appVersionCode uint32, sessionId string,
	originScreen deepLinkPb.Screen) (*typesUi.Filter, map[string]*fePb.GetRewardsAndOffersWidgetResponse_TabData, bool) {
	var (
		offerCards []*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card
	)

	// add your fi coins card
	offerCards = append(offerCards, c.mappingsManager.GetYourRewardsCard(int(fiCoinsBalance)))

	// convert to catalog offers for widget and interleave fi coin and exchanger offers according to config
	fiCoinOffers, exchangerOffers := c.mappingsManager.GetFiCoinOfferAndExchangerOfferCards(ctx, offers.FeCatalogOffers)
	var fiCoinOfferIter, exchangerOfferIter int
	orderedOfferTypes := []string{offerTypeNonCbr, offerTypeCbr, offerTypeNonCbr, offerTypeCbr, offerTypeNonCbr, offerTypeCbr, offerTypeNonCbr, offerTypeCbr, offerTypeNonCbr, offerTypeCbr}
	for i := 0; i < len(orderedOfferTypes); i++ {
		offerType := orderedOfferTypes[i]
		switch offerType {
		case offerTypeNonCbr:
			if fiCoinOfferIter < len(fiCoinOffers) {
				offerCards = append(offerCards, fiCoinOffers[fiCoinOfferIter])
				fiCoinOfferIter++
			}
		case offerTypeCbr:
			if exchangerOfferIter < len(exchangerOffers) {
				offerCards = append(offerCards, exchangerOffers[exchangerOfferIter])
				exchangerOfferIter++
			}
		}
	}

	// add view all offers card
	offerCards = append(offerCards, c.mappingsManager.GetViewAllOffersCard(viewAllCatalogOffersImageUrl, &deepLinkPb.Deeplink{
		Screen: deepLinkPb.Screen_OFFERS_LANDING_SCREEN,
	}))

	// construct tab data for catalog offers
	tabDataMap := map[string]*fePb.GetRewardsAndOffersWidgetResponse_TabData{
		catalogOffersTabId: {Cards: offerCards},
	}

	filter := &typesUi.Filter{
		Tabs: []*typesUi.Tab{
			{
				Id: catalogOffersTabId,
			},
		},
		FilterType:        typesUi.Filter_FILTER_TYPE_SWITCH,
		DefaultTabSection: catalogOffersTabId,
	}

	return filter, tabDataMap, false
}

func (c *CCDashboardOfferWidgetGenerator) GetBottomCta(_ commontypes.Platform, _ uint32) *ui.IconTextComponent {
	return &ui.IconTextComponent{
		LeftIcon: &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/rewards/union-monochrome-lead.png"},
		Texts: []*commontypes.Text{
			{
				FontColor:    colorMonochromeLead,
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Collected Offers"},
			},
		},
		RightIcon: &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/rewards/chevron-right-lead.png"},
		Deeplink:  &deepLinkPb.Deeplink{Screen: deepLinkPb.Screen_REDEEMED_OFFERS_SCREEN},
		ContainerProperties: &ui.IconTextComponent_ContainerProperties{
			BgColor:      "#FFFFFF",
			CornerRadius: 20,
		},
	}
}
