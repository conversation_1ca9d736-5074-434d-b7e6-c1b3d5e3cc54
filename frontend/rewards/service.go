package rewards

import (
	"context"
	"fmt"
	"math"
	"math/rand"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	fireflyaccpb "github.com/epifi/gamma/api/firefly/accounting"
	fireflyV2Pb "github.com/epifi/gamma/api/firefly/v2"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"golang.org/x/text/language"
	"golang.org/x/text/message"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/async/waitgroup"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/frontend/app/apputils"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	beAccrualPb "github.com/epifi/gamma/api/accrual"
	"github.com/epifi/gamma/api/actor"
	beActorPb "github.com/epifi/gamma/api/actor"
	bcPb "github.com/epifi/gamma/api/bankcust"
	beCardPb "github.com/epifi/gamma/api/card"
	"github.com/epifi/gamma/api/card/provisioning"
	beCasperPb "github.com/epifi/gamma/api/casper"
	beExchangerPb "github.com/epifi/gamma/api/casper/exchanger"
	evrPb "github.com/epifi/gamma/api/casper/external_vendor_redemption"
	beRedemptionPb "github.com/epifi/gamma/api/casper/redemption"
	depositPb "github.com/epifi/gamma/api/deposit"
	"github.com/epifi/gamma/api/firefly"
	"github.com/epifi/gamma/api/frontend/deeplink"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	errors2 "github.com/epifi/gamma/api/frontend/errors"
	"github.com/epifi/gamma/api/frontend/header"
	fePb "github.com/epifi/gamma/api/frontend/rewards"
	beKycPb "github.com/epifi/gamma/api/kyc"
	vkyc2 "github.com/epifi/gamma/api/kyc/vkyc"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	beRewardsPb "github.com/epifi/gamma/api/rewards"
	luckydrawPb "github.com/epifi/gamma/api/rewards/luckydraw"
	beRewardOffersPb "github.com/epifi/gamma/api/rewards/rewardoffers"
	beSalaryProgramPb "github.com/epifi/gamma/api/salaryprogram"
	savingsPb "github.com/epifi/gamma/api/savings"
	segmentPb "github.com/epifi/gamma/api/segment"
	beTieringPb "github.com/epifi/gamma/api/tiering"
	tieringExtPb "github.com/epifi/gamma/api/tiering/external"
	types "github.com/epifi/gamma/api/typesv2"
	pkgScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/pkg"
	screenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/rewards"
	rewardsTypes "github.com/epifi/gamma/api/typesv2/rewards"
	"github.com/epifi/gamma/api/typesv2/ui"
	beUserPb "github.com/epifi/gamma/api/user"
	beUserGroupPb "github.com/epifi/gamma/api/user/group"
	onboardingPb "github.com/epifi/gamma/api/user/onboarding"
	vendormappingPb "github.com/epifi/gamma/api/vendormapping"
	"github.com/epifi/gamma/frontend/pkg/common"
	"github.com/epifi/gamma/frontend/pkg/featureflags"
	pkgHome "github.com/epifi/gamma/frontend/pkg/home"
	feRewardsPkg "github.com/epifi/gamma/frontend/pkg/rewards"
	"github.com/epifi/gamma/frontend/rewards/earnedrewardshistory"
	pkgHelper "github.com/epifi/gamma/frontend/rewards/pkg"
	accrualPkg "github.com/epifi/gamma/pkg/accrual"
	"github.com/epifi/gamma/pkg/feature/release"
	pkgUser "github.com/epifi/gamma/pkg/user"
	questSdk "github.com/epifi/gamma/quest/sdk"

	widgetPb "github.com/epifi/gamma/api/frontend/search/widget"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/rewards/offerdisplay"
	"github.com/epifi/gamma/frontend/rewards/offerwidget"
	"github.com/epifi/gamma/frontend/rewards/tags"
	wireTypes "github.com/epifi/gamma/frontend/wire/types"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	feErrors "github.com/epifi/gamma/pkg/frontend/errors"
	"github.com/epifi/gamma/pkg/onboarding"
	"github.com/epifi/gamma/pkg/vkyc"
)

var _ fePb.RewardsServer = &RewardService{}

type RewardService struct {
	fePb.UnimplementedRewardsServer
	rewardsFrontendMeta            *config.RewardsFrontendMeta
	rewardsGeneratorClient         beRewardsPb.RewardsGeneratorClient
	rewardOffersClient             beRewardOffersPb.RewardOffersClient
	accrualClient                  beAccrualPb.AccrualClient
	tieringClient                  beTieringPb.TieringClient
	offerListingClient             beCasperPb.OfferListingServiceClient
	offerCatalogClient             beCasperPb.OfferCatalogServiceClient
	offerInventoryService          beCasperPb.OfferInventoryServiceClient
	salaryProgramClient            beSalaryProgramPb.SalaryProgramClient
	offerRedemptionClient          beRedemptionPb.OfferRedemptionServiceClient
	luckyDrawClient                luckydrawPb.LuckyDrawServiceClient
	depositClient                  depositPb.DepositClient
	usersClient                    beUserPb.UsersClient
	exchangerOfferClient           beExchangerPb.ExchangerOfferServiceClient
	actorServiceClient             beActorPb.ActorClient
	offerDisplayEngine             offerdisplay.IEngine
	dyconf                         *genconf.Config
	bcClient                       bcPb.BankCustomerServiceClient
	tagsManager                    tags.IManager
	offerWidgetGeneratorFactory    offerwidget.IOfferWidgetGeneratorFactory
	rewardsCacheStorage            wireTypes.RewardsCacheStorage
	fireflyClient                  firefly.FireflyClient
	fireflyAccountingClient        fireflyaccpb.AccountingClient
	fireflyClient2                 fireflyV2Pb.FireflyV2Client
	savingsClient                  savingsPb.SavingsClient
	vendorMappingClient            vendormappingPb.VendorMappingServiceClient
	cardClient                     provisioning.CardProvisioningClient
	userGroupClient                beUserGroupPb.GroupClient
	externalVendorRedemptionClient evrPb.ExternalVendorRedemptionServiceClient
	onboardingClient               onboardingPb.OnboardingClient
	segmentClient                  segmentPb.SegmentationServiceClient
	palClient                      palPb.PreApprovedLoanClient
	releaseEvaluator               release.IEvaluator
	questSdkClient                 *questSdk.Client
	userAttributeFetcher           pkgUser.UserAttributesFetcher
	networthClient                 networthPb.NetWorthClient
	ScreenDataBuilderFactory       earnedrewardshistory.ScreenDataBuilderFactory
}

func NewRewardsService(
	rewardsFrontendMeta *config.RewardsFrontendMeta,
	rewardsGeneratorClient beRewardsPb.RewardsGeneratorClient,
	rewardOffersClient beRewardOffersPb.RewardOffersClient,
	accrualClient beAccrualPb.AccrualClient,
	tieringClient beTieringPb.TieringClient,
	offerListingClient beCasperPb.OfferListingServiceClient,
	offerCatalogClient beCasperPb.OfferCatalogServiceClient,
	offerInventoryService beCasperPb.OfferInventoryServiceClient,
	salaryProgramClient beSalaryProgramPb.SalaryProgramClient,
	offerRedemptionClient beRedemptionPb.OfferRedemptionServiceClient,
	luckyDrawClient luckydrawPb.LuckyDrawServiceClient,
	usersClient beUserPb.UsersClient,
	depositClient depositPb.DepositClient,
	exchangerOfferClient beExchangerPb.ExchangerOfferServiceClient,
	actorServiceClient beActorPb.ActorClient,
	offerDisplayEngine offerdisplay.IEngine,
	dyconf *genconf.Config,
	bcClient bcPb.BankCustomerServiceClient,
	tagsManager tags.IManager,
	offerWidgetGeneratorFactory offerwidget.IOfferWidgetGeneratorFactory,
	cacheStorage wireTypes.RewardsCacheStorage,
	fireflyClient firefly.FireflyClient,
	savingsClient savingsPb.SavingsClient,
	vendorMappingClient vendormappingPb.VendorMappingServiceClient,
	cardClient provisioning.CardProvisioningClient,
	userGroupClient beUserGroupPb.GroupClient,
	externalVendorRedemptionClient evrPb.ExternalVendorRedemptionServiceClient,
	onboardingClient onboardingPb.OnboardingClient,
	segmentClient segmentPb.SegmentationServiceClient,
	palClient palPb.PreApprovedLoanClient,
	releaseEvaluator release.IEvaluator,
	questSdkClient *questSdk.Client,
	userAttributeFetcher pkgUser.UserAttributesFetcher,
	networthClient networthPb.NetWorthClient,
	fireflyAccountingClient fireflyaccpb.AccountingClient,
	fireflyClient2 fireflyV2Pb.FireflyV2Client,
	screenDataBuilderFactory earnedrewardshistory.ScreenDataBuilderFactory,
) *RewardService {
	rand.Seed(time.Now().Unix())
	return &RewardService{
		rewardsFrontendMeta:            rewardsFrontendMeta,
		rewardsGeneratorClient:         rewardsGeneratorClient,
		rewardOffersClient:             rewardOffersClient,
		accrualClient:                  accrualClient,
		tieringClient:                  tieringClient,
		offerListingClient:             offerListingClient,
		offerCatalogClient:             offerCatalogClient,
		offerInventoryService:          offerInventoryService,
		salaryProgramClient:            salaryProgramClient,
		offerRedemptionClient:          offerRedemptionClient,
		luckyDrawClient:                luckyDrawClient,
		usersClient:                    usersClient,
		depositClient:                  depositClient,
		exchangerOfferClient:           exchangerOfferClient,
		actorServiceClient:             actorServiceClient,
		offerDisplayEngine:             offerDisplayEngine,
		dyconf:                         dyconf,
		bcClient:                       bcClient,
		tagsManager:                    tagsManager,
		offerWidgetGeneratorFactory:    offerWidgetGeneratorFactory,
		rewardsCacheStorage:            cacheStorage,
		fireflyClient:                  fireflyClient,
		savingsClient:                  savingsClient,
		vendorMappingClient:            vendorMappingClient,
		cardClient:                     cardClient,
		userGroupClient:                userGroupClient,
		externalVendorRedemptionClient: externalVendorRedemptionClient,
		onboardingClient:               onboardingClient,
		segmentClient:                  segmentClient,
		palClient:                      palClient,
		releaseEvaluator:               releaseEvaluator,
		questSdkClient:                 questSdkClient,
		userAttributeFetcher:           userAttributeFetcher,
		networthClient:                 networthClient,
		fireflyAccountingClient:        fireflyAccountingClient,
		fireflyClient2:                 fireflyClient2,
		ScreenDataBuilderFactory:       screenDataBuilderFactory,
	}
}

var (
	exchangerOrderUserInputDefaultErrorView = feErrors.NewBottomSheetErrorView(
		"",
		"Something went wrong.", "",
		"Your reward is safe! But we need some additional information to process it. Re-directing you to my orders to confirm the same.",
		&errors2.CTA{
			Text: "OK",
			Type: errors2.CTA_CUSTOM,
			Action: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_REDEEMED_OFFERS_SCREEN,
			},
			DisplayTheme: errors2.CTA_PRIMARY,
		},
	)
)

type deeplinkName string

const (
	minKycToFullKycConversionDeeplink deeplinkName = "minKycToFullKycConversionDeeplink"
	openSavingsAccountDeeplink        deeplinkName = "openSavingsAccountDeeplink"
)

func (r *RewardService) GetRewardDigest(ctx context.Context, req *fePb.GetRewardDigestRequest) (*fePb.GetRewardDigestResponse, error) {
	getUnOpenedRewardsCountRequest := &beRewardsPb.GetUnOpenedRewardsCountRequest{
		ActorId: req.GetReq().GetAuth().GetActorId(),
	}
	getUnOpenedRewardsCountResponse, err := r.rewardsGeneratorClient.GetUnOpenedRewardsCount(ctx, getUnOpenedRewardsCountRequest)
	if err != nil || getUnOpenedRewardsCountResponse == nil || !getUnOpenedRewardsCountResponse.Status.IsSuccess() {
		logger.Error(ctx, "GetUnOpenedRewardsCount call failed", zap.Error(err), zap.Any("res", getUnOpenedRewardsCountResponse))
		return &fePb.GetRewardDigestResponse{
			Status: rpc.StatusInternalWithDebugMsg("GetUnOpenedRewardsCount call failed"),
		}, nil
	}

	getAccountDetailsRequest := &beAccrualPb.GetAccountDetailsRequest{
		ActorId:     req.GetReq().GetAuth().GetActorId(),
		AccountType: accrualPkg.FetchAccrualAccountType(beRewardsPb.RewardType_FI_COINS, timestamppb.Now()),
	}
	getAccountDetailsResponse, err := r.accrualClient.GetAccountDetails(ctx, getAccountDetailsRequest)
	if err != nil || getAccountDetailsResponse == nil || !getAccountDetailsResponse.Status.IsSuccess() {
		logger.Error(ctx, "GetAccountDetails call failed", zap.Error(err), zap.Any("res", getAccountDetailsResponse))
		return &fePb.GetRewardDigestResponse{
			Status: rpc.StatusInternalWithDebugMsg("GetAccountDetails call failed"),
		}, nil
	}

	return &fePb.GetRewardDigestResponse{
		Status:                rpc.StatusOk(),
		UnopendedRewardsCount: uint32(getUnOpenedRewardsCountResponse.UnopenedRewardCount),
		FiCoinsBalance:        uint32(getAccountDetailsResponse.AccountBalance),
	}, nil
}

// GetRewardOfferDetails rpc is useful to fetch rewardOffer details by offer-id.
func (r *RewardService) GetRewardOfferDetails(ctx context.Context, req *fePb.GetRewardOfferDetailsRequest) (*fePb.GetRewardOfferDetailsResponse, error) {
	currentTimeString := time.Now().Format(time.RFC3339)

	rewardOffersResponse, err := r.rewardOffersClient.GetRewardOffersForActor(ctx, &beRewardOffersPb.GetRewardOffersForActorRequest{
		Id:                req.GetRewardOfferId(),
		DisplaySince:      currentTimeString,
		DisplayTill:       currentTimeString,
		Status:            beRewardOffersPb.RewardOfferStatus_ACTIVE,
		IsVisible:         true,
		ActorId:           req.GetReq().GetAuth().GetActorId(),
		SupportedPlatform: req.GetReq().GetAuth().GetDevice().GetPlatform(),
	})
	if rpcErr := epifigrpc.RPCError(rewardOffersResponse, err); rpcErr != nil {
		logger.Error(ctx, "rewardOffersClient.GetRewardOffersForActor call failed", zap.String(logger.REWARD_OFFER_ID, req.GetRewardOfferId()), zap.Error(err))
		return &fePb.GetRewardOfferDetailsResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("rewardOffersClient.GetRewardOffersForActor call failed")}}, nil
	}
	if len(rewardOffersResponse.GetRewardOffers()) == 0 {
		logger.WarnWithCtx(ctx, "no reward offer found with given id", zap.String(logger.REWARD_OFFER_ID, req.GetRewardOfferId()))
		return &fePb.GetRewardOfferDetailsResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusRecordNotFound()}}, nil
	}
	beRewardOffer := rewardOffersResponse.GetRewardOffers()[0]

	var (
		actionLevelOfferInventory, actorLevelOfferInventory *beRewardOffersPb.RewardOfferInventory
		actorLevelOfferGroupInventory                       *beRewardOffersPb.RewardOfferGroupInventory
		actorAndOfferLevelRewardUnitsUtilization            *beRewardOffersPb.RewardOfferRewardUnitsActorUtilisation
		actorAndOfferGroupLevelRewardUnitsUtilization       *beRewardOffersPb.RewardOfferGroupRewardUnitsActorUtilisation
	)

	rewardOfferGroup := rewardOffersResponse.GetOfferGroupIdToOfferGroupMap()[beRewardOffer.GetGroupId()]
	if len(rewardOffersResponse.GetActionLevelRewardOfferInventory()) > 0 {
		actionLevelOfferInventory = rewardOffersResponse.GetActionLevelRewardOfferInventory()[0]
	}
	if len(rewardOffersResponse.GetActorLevelRewardOfferInventory()) > 0 {
		actorLevelOfferInventory = rewardOffersResponse.GetActorLevelRewardOfferInventory()[0]
	}
	if len(rewardOffersResponse.GetActorLevelOfferGroupInventory()) > 0 {
		actorLevelOfferGroupInventory = rewardOffersResponse.GetActorLevelOfferGroupInventory()[0]
	}
	if len(rewardOffersResponse.GetActorLevelRewardUnitsUtil()) > 0 {
		actorAndOfferLevelRewardUnitsUtilization = rewardOffersResponse.GetActorLevelRewardUnitsUtil()[0]
	}
	if len(rewardOffersResponse.GetActorLevelGroupRewardsUnitsUtil()) > 0 {
		actorAndOfferGroupLevelRewardUnitsUtilization = rewardOffersResponse.GetActorLevelGroupRewardsUnitsUtil()[0]
	}

	shouldRewardOfferBeDisplayed := r.shouldRewardOfferBeDisplayed(beRewardOffer, req.GetReq().GetAuth().GetDevice().GetPlatform(), req.GetReq().GetAppVersionCode(), nil)
	if !shouldRewardOfferBeDisplayed {
		logger.WarnWithCtx(ctx, "rewardOffer should not be displayed to the user", zap.String(logger.REWARD_OFFER_ID, req.GetRewardOfferId()))
		return &fePb.GetRewardOfferDetailsResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusRecordNotFound()}}, nil
	}

	// if total count is zero, then it implies action level inventory is infinite, so we need not check remaining count in such case.
	// if action level inventory is already exhausted, then do not show the reward offer to the user.
	if actionLevelOfferInventory.GetTotalCount() != 0 && actionLevelOfferInventory.GetRemainingCount() == 0 {
		logger.WarnWithCtx(ctx, "action level offer inventory exhausted for offer", zap.String(logger.REWARD_OFFER_ID, req.GetRewardOfferId()))
		return &fePb.GetRewardOfferDetailsResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusRecordNotFound()}}, nil
	}

	feRewardOffer := r.getFeRewardOffer(ctx, beRewardOffer, rewardOfferGroup, actorAndOfferLevelRewardUnitsUtilization, actorAndOfferGroupLevelRewardUnitsUtilization,
		actorLevelOfferInventory, actorLevelOfferGroupInventory, req.GetReq().GetAppVersionCode(), req.GetReq().GetAuth().GetDevice().GetPlatform())

	return &fePb.GetRewardOfferDetailsResponse{
		RespHeader:  &header.ResponseHeader{Status: rpc.StatusOk()},
		RewardOffer: feRewardOffer,
	}, nil
}

// order feRewardOffers list in particular fashion to be displayed on APP.
// Current ordering logic is -> Show reward offers with 'New' tag first and 'COMPLETED' tag last.
// Within similarly tagged reward offers order by reward offer display rank in ascending order.
func orderFeRewardOffersForDisplay(feRewardOffers []*fePb.RewardOffer, beRewardOffers []*beRewardOffersPb.RewardOffer) {
	// create offer id to reward offer display rank map
	offerIdToDisplayRankMap := make(map[string]int32, len(beRewardOffers))
	for _, beRewardOffer := range beRewardOffers {
		offerIdToDisplayRankMap[beRewardOffer.GetId()] = beRewardOffer.GetDisplayMeta().GetDisplayRank()
	}

	// offers with 'NEW' tag should be shown first and 'COMPLETED' tag should be shown last
	// Within similarly tagged reward offers order by reward offer display rank in ascending order.
	sort.Slice(feRewardOffers, func(i, j int) bool {
		rewardOfferITileTag, rewardOfferJTileTag := strings.TrimSpace(feRewardOffers[i].GetDisplayDetails().GetTileTagText()), strings.TrimSpace(feRewardOffers[j].GetDisplayDetails().GetTileTagText())
		rewardOfferIDisplayRank, rewardOfferJDisplayRank := offerIdToDisplayRankMap[feRewardOffers[i].GetId()], offerIdToDisplayRankMap[feRewardOffers[j].GetId()]
		tagValueToSortOrderMap := map[string]int{
			"NEW":       1,
			"":          2,
			"COMPLETED": 3,
		}
		if tagValueToSortOrderMap[rewardOfferITileTag] != tagValueToSortOrderMap[rewardOfferJTileTag] {
			return tagValueToSortOrderMap[rewardOfferITileTag] < tagValueToSortOrderMap[rewardOfferJTileTag]
		}
		// if tags are equal sort by reward offer display rank in ascending order.
		return rewardOfferIDisplayRank < rewardOfferJDisplayRank
	})
}

func (r *RewardService) GetRewardSummary(ctx context.Context, req *fePb.GetRewardSummaryRequest) (*fePb.GetRewardSummaryResponse, error) {
	accountDetailsRequest := &beAccrualPb.GetAccountDetailsRequest{
		ActorId:     req.GetReq().GetAuth().GetActorId(),
		AccountType: accrualPkg.FetchAccrualAccountType(beRewardsPb.RewardType_FI_COINS, timestamppb.Now()),
	}
	accountDetailsResponse, err := r.accrualClient.GetAccountDetails(ctx, accountDetailsRequest)
	if err != nil || !accountDetailsResponse.GetStatus().IsSuccess() {
		logger.Error(ctx, "GetAccountDetails call failed", zap.Any(logger.RPC_STATUS, accountDetailsResponse.GetStatus()), zap.Error(err))
		return &fePb.GetRewardSummaryResponse{
			Status: rpc.StatusInternalWithDebugMsg("GetAccountDetails call failed"),
		}, nil
	}

	rewardSummaryRequest := &beRewardsPb.GetRewardSummaryRequest{
		ActorId: req.GetReq().GetAuth().GetActorId(),
	}

	rewardSummaryResponse, err := r.rewardsGeneratorClient.GetRewardSummary(ctx, rewardSummaryRequest)
	if err != nil || !rewardSummaryResponse.GetStatus().IsSuccess() {
		logger.Error(ctx, "GetRewardSummary call failed", zap.Any(logger.RPC_STATUS, rewardSummaryResponse.GetStatus()), zap.Error(err))
		return &fePb.GetRewardSummaryResponse{
			Status: rpc.StatusInternalWithDebugMsg("GetRewardSummary call failed"),
		}, nil
	}

	exchangerOfferOrdersSummaryResp, err := r.getExchangerOfferOrdersRewardsSummary(ctx, req.GetReq().GetAuth().GetActorId())
	if err != nil {
		logger.Error(ctx, "error adding exchanger orders summary to reward summary", zap.Error(err))
		return &fePb.GetRewardSummaryResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	rewardUnitsSummaries, err := getRewardUnitsSummaries(rewardSummaryResponse, exchangerOfferOrdersSummaryResp, accountDetailsResponse.GetAvailableBalance())
	if err != nil {
		logger.Error(ctx, "error while fetching formatted reward units summaries", zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()), zap.Error(err))
		return &fePb.GetRewardSummaryResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	return &fePb.GetRewardSummaryResponse{
		Status:                         rpc.StatusOk(),
		FiCoinsBalance:                 uint32(accountDetailsResponse.GetAccountBalance()),
		WorthSaved:                     types.GetFromBeMoney(rewardSummaryResponse.GetTotalSidRewardEarned()),
		InProcessFiCoinsAmount:         uint32(rewardSummaryResponse.GetTotalInProcessingFiCoins()) + uint32(exchangerOfferOrdersSummaryResp.GetInProcessFiCoins()),
		InProcessCashAmount:            types.GetFromBeMoney(rewardSummaryResponse.GetTotalInProcessingSidRewardAmount()),
		TotalCashRewardAmountEarned:    types.GetFromBeMoney(money.AmountINR(exchangerOfferOrdersSummaryResp.GetCashEarned().GetUnits() + rewardSummaryResponse.GetTotalCashRewardEarned().GetUnits()).GetPb()),
		TotalInProcessCashRewardAmount: types.GetFromBeMoney(money.AmountINR(exchangerOfferOrdersSummaryResp.GetInProcessCashRewardAmount().GetUnits() + rewardSummaryResponse.GetTotalInProcessingCashRewardAmount().GetUnits()).GetPb()),
		TotalSdRewardAmountEarned:      types.GetFromBeMoney(rewardSummaryResponse.GetTotalSidRewardEarned()),
		TotalInProcessSdRewardAmount:   types.GetFromBeMoney(rewardSummaryResponse.GetTotalInProcessingSidRewardAmount()),
		RewardUnitsSummaries:           rewardUnitsSummaries,
	}, nil
}

func getRewardUnitsSummaries(rewardSummaryRes *beRewardsPb.GetRewardSummaryResponse, exchangerOrdersSummaryRes *beExchangerPb.GetExchangerOffersOrdersSummaryResponse, fiCoinsBalance int32) ([]*fePb.GetRewardSummaryResponse_RewardUnitsSummary, error) {
	var (
		lockedCashRewardUnits     = int32(rewardSummaryRes.GetTotalLockedCashRewardAmount().GetUnits())
		processingCashRewardUnits = int32(rewardSummaryRes.GetTotalInProcessingCashRewardAmount().GetUnits() +
			rewardSummaryRes.GetTotalInProcessingSidRewardAmount().GetUnits() +
			exchangerOrdersSummaryRes.GetInProcessCashRewardAmount().GetUnits())
		processedCashRewardUnits = int32(rewardSummaryRes.GetTotalCashRewardEarned().GetUnits() +
			exchangerOrdersSummaryRes.GetCashEarned().GetUnits())

		lockedFiCoinsRewardAmount     = rewardSummaryRes.GetTotalLockedFiCoinsRewardAmount()
		processingFiCoinsRewardAmount = rewardSummaryRes.GetTotalInProcessingFiCoins() +
			exchangerOrdersSummaryRes.GetInProcessFiCoins()
	)

	fiCoinsRewardsSummary, err := getRewardUnitsSummary(beRewardsPb.RewardType_FI_COINS, fiCoinsBalance, processingFiCoinsRewardAmount, lockedFiCoinsRewardAmount)
	if err != nil {
		return nil, fmt.Errorf("error while fetching rewards summary for Fi-coins, err: %w", err)
	}

	cashRewardsSummary, err := getRewardUnitsSummary(beRewardsPb.RewardType_CASH, processedCashRewardUnits, processingCashRewardUnits, lockedCashRewardUnits)
	if err != nil {
		return nil, fmt.Errorf("error while fetching rewards summary for cash rewards, err: %w", err)
	}

	rewardUnitsSummaries := []*fePb.GetRewardSummaryResponse_RewardUnitsSummary{
		fiCoinsRewardsSummary,
		cashRewardsSummary,
	}

	return rewardUnitsSummaries, nil
}

// nolint:funlen,dupl
func getRewardUnitsSummary(rewardType beRewardsPb.RewardType, processedRewardUnits, processingRewardUnits, lockedRewardUnits int32) (*fePb.GetRewardSummaryResponse_RewardUnitsSummary, error) {
	if !lo.Contains([]beRewardsPb.RewardType{beRewardsPb.RewardType_CASH, beRewardsPb.RewardType_FI_COINS}, rewardType) {
		return nil, fmt.Errorf("unsupported reward type encountered while fetching summary, rewardType: %s", rewardType.String())
	}

	var (
		p                        = message.NewPrinter(language.Hindi)
		lockedRewardUnitsStr     = p.Sprintf("%v", lockedRewardUnits)
		processingRewardUnitsStr = p.Sprintf("%v", processingRewardUnits)
		earnedRewardUnitsStr     = p.Sprintf("%v", processedRewardUnits)
		pendingRewardUnitsStr    = p.Sprintf("%v", lockedRewardUnits+processingRewardUnits)
	)

	rewardUnitsSummary := &fePb.GetRewardSummaryResponse_RewardUnitsSummary{
		RewardUnitName: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					FontColor:    colorContentOnDarkLowEmphasis,
					DisplayValue: &commontypes.Text_PlainString{PlainString: rewardTypeDisplayDetailsMapping[rewardType].summaryTitle},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_OVERLINE_XS_CAPS,
					},
				},
			},
		},
		ProcessedRewardUnitsValue: &ui.IconTextComponent{
			LeftVisualElement: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source: &commontypes.VisualElement_Image_Url{
							Url: rewardTypeDisplayDetailsMapping[rewardType].summarySectionProcessedRewardsIcon,
						},
						Properties: &commontypes.VisualElementProperties{
							Width:  32,
							Height: 32,
						},
					},
				},
			},
			Texts: []*commontypes.Text{
				{
					FontColor:    colorContentOnDarkHighEmphasis,
					DisplayValue: &commontypes.Text_PlainString{PlainString: earnedRewardUnitsStr},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_NUMBER_2XL,
					},
				},
			},
		},
		PendingRewardUnitsValue: &ui.IconTextComponent{
			LeftVisualElement: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source: &commontypes.VisualElement_Image_Url{
							Url: rewardsSummaryProcessingRewardsIcon,
						},
						Properties: &commontypes.VisualElementProperties{
							Width:  18,
							Height: 18,
						},
					},
				},
			},
			Texts: []*commontypes.Text{
				{
					FontColor:    colorContentOnDarkMediumEmphasis,
					DisplayValue: &commontypes.Text_PlainString{PlainString: pendingRewardUnitsStr},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_NUMBER_M,
					},
				},
			},
		},
	}

	if (processingRewardUnits + lockedRewardUnits) > 0 {
		pendingRewardsBreakdown := &fePb.GetRewardSummaryResponse_PendingRewardsBreakdown{
			Title: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: rewardTypeDisplayDetailsMapping[rewardType].pendingRewardsBottomSheetTitle},
				FontColor:    colorInk,
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_SUBTITLE_2,
				},
			},
		}
		// add chevron
		rewardUnitsSummary.PendingRewardUnitsValue.RightVisualElement = &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{
						Url: chevronDownGray,
					},
					Properties: &commontypes.VisualElementProperties{
						Width:  18,
						Height: 18,
					},
				},
			},
		}
		if lockedRewardUnits > 0 {
			// change icon shown on rewards summary for this reward type to "lock"
			rewardUnitsSummary.PendingRewardUnitsValue.LeftVisualElement = &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source: &commontypes.VisualElement_Image_Url{
							Url: rewardsSummaryLockedRewardsIcon,
						},
						Properties: &commontypes.VisualElementProperties{
							Width:  18,
							Height: 18,
						},
					},
				},
			}

			// add breakdown of locked rewards
			pendingRewardsBreakdown.Rows = append(pendingRewardsBreakdown.Rows, &fePb.GetRewardSummaryResponse_PendingRewardsBreakdown_Row{
				Icon: &commontypes.VisualElement{
					Asset: &commontypes.VisualElement_Image_{
						Image: &commontypes.VisualElement_Image{
							Source: &commontypes.VisualElement_Image_Url{
								Url: pendingRewardsBottomSheetLockedRewardsIcon,
							},
							Properties: &commontypes.VisualElementProperties{
								Width:  40,
								Height: 40,
							},
						},
					},
				},
				Status: &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{PlainString: "Locked"},
					FontColor:    colorContentOnLightHighEmphasis,
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
					},
				},
				Desc: &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{PlainString: "For more details, tap on your locked rewards"},
					FontColor:    colorContentOnDarkLowEmphasis,
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_BODY_XS,
					},
				},
				RewardUnits: &ui.IconTextComponent{
					LeftVisualElement: &commontypes.VisualElement{
						Asset: &commontypes.VisualElement_Image_{
							Image: &commontypes.VisualElement_Image{
								Source: &commontypes.VisualElement_Image_Url{
									Url: rewardTypeDisplayDetailsMapping[rewardType].pendingRewardsBottomSheetRewardsIcon,
								},
								Properties: &commontypes.VisualElementProperties{
									Width:  20,
									Height: 20,
								},
							},
						},
					},
					Texts: []*commontypes.Text{
						{
							FontColor:    colorContentOnLightHighEmphasis,
							DisplayValue: &commontypes.Text_PlainString{PlainString: lockedRewardUnitsStr},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
							},
						},
					},
				},
			})
		}

		if processingRewardUnits > 0 {
			pendingRewardsBreakdown.Rows = append(pendingRewardsBreakdown.Rows, &fePb.GetRewardSummaryResponse_PendingRewardsBreakdown_Row{
				Icon: &commontypes.VisualElement{
					Asset: &commontypes.VisualElement_Image_{
						Image: &commontypes.VisualElement_Image{
							Source: &commontypes.VisualElement_Image_Url{
								Url: pendingRewardsBottomSheetProcessingRewardsIcon,
							},
							Properties: &commontypes.VisualElementProperties{
								Width:  40,
								Height: 40,
							},
						},
					},
				},
				Status: &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{PlainString: "Processing"},
					FontColor:    colorContentOnLightHighEmphasis,
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
					},
				},
				Desc: &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{PlainString: "We'll notify you when it's done"},
					FontColor:    colorContentOnDarkLowEmphasis,
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_BODY_XS,
					},
				},
				RewardUnits: &ui.IconTextComponent{
					LeftVisualElement: &commontypes.VisualElement{
						Asset: &commontypes.VisualElement_Image_{
							Image: &commontypes.VisualElement_Image{
								Source: &commontypes.VisualElement_Image_Url{
									Url: rewardTypeDisplayDetailsMapping[rewardType].pendingRewardsBottomSheetRewardsIcon,
								},
								Properties: &commontypes.VisualElementProperties{
									Width:  20,
									Height: 20,
								},
							},
						},
					},
					Texts: []*commontypes.Text{
						{
							FontColor:    colorContentOnLightHighEmphasis,
							DisplayValue: &commontypes.Text_PlainString{PlainString: processingRewardUnitsStr},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
							},
						},
					},
				},
			})
		}

		rewardUnitsSummary.PendingRewardsBreakdown = pendingRewardsBreakdown
	}

	rewardUnitsSummary.ProcessedRewardUnitsValue.LeftImgTxtPadding = -5

	return rewardUnitsSummary, nil
}

func (r *RewardService) getExchangerOfferOrdersRewardsSummary(ctx context.Context, actorId string) (*beExchangerPb.GetExchangerOffersOrdersSummaryResponse, error) {
	exchangerOffersSummaryRequest := &beExchangerPb.GetExchangerOffersOrdersSummaryRequest{
		ActorId: actorId,
		Filters: &beExchangerPb.GetExchangerOffersOrdersSummaryRequest_Filters{
			// todo: take filters are arguments instead of hardcoding if the use-cases expand
			States: []beExchangerPb.ExchangerOfferOrderState{
				beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_FULFILLMENT_INITIATED,
				beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_FULFILLED,
				beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_USER_INPUT_NEEDED_FOR_FULFILLMENT,
				beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_OPTION_CHOSEN,
			},
			RewardTypes: []beExchangerPb.RewardType{
				beExchangerPb.RewardType_REWARD_TYPE_CASH,
				beExchangerPb.RewardType_REWARD_TYPE_FI_COINS,
			},
		},
	}

	exchangerOfferOrdersSummaryResponse, err := r.exchangerOfferClient.GetExchangerOffersOrdersSummary(ctx, exchangerOffersSummaryRequest)
	if rpcErr := epifigrpc.RPCError(exchangerOfferOrdersSummaryResponse, err); rpcErr != nil {
		logger.Error(ctx, "exchangerOfferClient.GetExchangerOffersOrdersSummary rpc call failed", zap.String(logger.ACTOR_ID, actorId), zap.Any(logger.RPC_STATUS, exchangerOfferOrdersSummaryResponse.GetStatus()), zap.Error(rpcErr))
		return nil, rpcErr
	}

	return exchangerOfferOrdersSummaryResponse, nil
}

func (r *RewardService) getUserAndAppAttributes(ctx context.Context, auth *header.AuthHeader, appVersion uint32) (*UserAndAppAttributes, error) {
	var (
		actorId              = auth.GetActorId()
		userAndAppAttributes = &UserAndAppAttributes{}
	)
	isTieringEnabled, currentTier := getTieringFeatureFlagAndCurrentTier(ctx, actorId, r.tieringClient)
	userAndAppAttributes.IsTieringEnabled = isTieringEnabled
	userAndAppAttributes.AccountTier = currentTier
	userAndAppAttributes.AppPlatform = auth.GetDevice().GetPlatform()
	userAndAppAttributes.AppVersionCode = appVersion

	if r.checkIfLockingRewardsForMinKycUsersSupported(auth.GetDevice().GetPlatform(), appVersion) {
		userKycLevel, kycErr := GetKycLevelOfUser(ctx, actorId, r.actorServiceClient, r.bcClient)
		if kycErr != nil {
			return nil, fmt.Errorf("error fetching KYC level of user, err: %w", kycErr)
		}
		userAndAppAttributes.IsLockingRewardsForMinKycUsersSupported = true
		userAndAppAttributes.KycLevel = userKycLevel

		// get and save user's saving account state
		savingAccountState, err := GetSavingAccountStateForActor(ctx, actorId, r.savingsClient)
		if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
			return nil, fmt.Errorf("error fetching Savings Account state for user")
		}
		userAndAppAttributes.SavingsAccountStatus = savingAccountState
	}

	isUserPartOfInternalGrp, err := r.isUserPartOfUserGroups(ctx, []commontypes.UserGroup{commontypes.UserGroup_INTERNAL}, actorId)
	if err != nil {
		logger.Error(ctx, "error checking whether user is part of user group", zap.String(logger.ACTOR_ID_V2, actorId))
		isUserPartOfInternalGrp = false
	}
	userAndAppAttributes.IsUserPartOfInternalUserGrp = isUserPartOfInternalGrp
	userAndAppAttributes.IsFeatureRewardsCatalogMergedPageEnabled = featureflags.IsFeatureRewardsCatalogMergedPageEnabled(ctx, &featureflags.IsFeatureRewardsCatalogMergedPageEnabledRequest{
		ActorId: actorId,
		ExternalDeps: &common.ExternalDependencies{
			Evaluator:            r.releaseEvaluator,
			OnboardingClient:     r.onboardingClient,
			QuestSdkClient:       r.questSdkClient,
			UserAttributeFetcher: r.userAttributeFetcher,
			NetWorthClient:       r.networthClient,
		},
	})

	return userAndAppAttributes, nil
}

// GetRewardsForActorV1 returns the rewards for the actor for displaying on "My Rewards" Screen.
// This RPC supports filters that client can use for different use-cases.
// NOTE: the PageCtx will be used for one of the two purposes depending on the value of req.Filter.RewardTileStatus.
// it would either be used for pagination of unopened rewards or as a multi-entity pagination object for rewards and exchanger orders.
// nolint: funlen
func (r *RewardService) GetRewardsForActorV1(ctx context.Context, req *fePb.GetRewardsForActorV1Request) (*fePb.GetRewardsForActorV1Response, error) {
	var (
		actorId = req.GetReq().GetAuth().GetActorId()
	)
	userAndAppAttributes, err := r.getUserAndAppAttributes(ctx, req.GetReq().GetAuth(), req.GetReq().GetAppVersionCode())
	if err != nil {
		logger.Error(ctx, "error while fetching user and app attributes", zap.String(logger.ACTOR_ID_V2, actorId), zap.Uint32(logger.APP_VERSION_CODE, req.GetReq().GetAppVersionCode()), zap.Error(err))
		return &fePb.GetRewardsForActorV1Response{
			Status: rpc.StatusInternalWithDebugMsg("error while fetching user and app attributes"),
		}, nil
	}

	// we'll ignore RewardTileStatus filter sent by IOS client for versions <
	// MinIosVersionSupportingMyRewardsV2Screen as a bug was introduced in the IOS
	// app that was sending REWARD_TILE_STATUS_OPENED as filter value when it should
	// have been sending UNSPECIFIED for showing older screen.
	if userAndAppAttributes.AppPlatform == commontypes.Platform_IOS && userAndAppAttributes.AppVersionCode < r.dyconf.RewardsFrontendMeta().MinIosVersionSupportingMyRewardsV2Screen() && req.GetFilters().GetRewardTileStatus() != fePb.GetRewardsForActorV1Request_REWARD_TILE_STATUS_UNSPECIFIED {
		req.GetFilters().RewardTileStatus = fePb.GetRewardsForActorV1Request_REWARD_TILE_STATUS_UNSPECIFIED
	}

	// if RewardTileStatus filter's value is REWARD_TILE_STATUS_UNOPENED, we only
	// need to fetch and return unopened rewards for a user. no exchanger orders will
	// be fetched as exchanger orders only appear on MyRewards screen after they move
	// are claimed by the user.
	if req.GetFilters().GetRewardTileStatus() == fePb.GetRewardsForActorV1Request_REWARD_TILE_STATUS_UNOPENED {
		beRewardsPageCtxReq := r.getBeRewardPageContext(req.GetPageContext(), r.rewardsFrontendMeta)
		unopenedRewardsFilter := &beRewardsPb.RewardsByActorIdRequest_FiltersV2{
			AndFilter: &beRewardsPb.RewardsByActorIdRequest_Filter{
				ClaimType: beRewardsPb.ClaimType_CLAIM_TYPE_MANUAL,
				Statuses:  []beRewardsPb.RewardStatus{beRewardsPb.RewardStatus_CREATED, beRewardsPb.RewardStatus_LOCKED},
			},
		}
		rewardId := req.GetFilters().GetRewardId()
		if rewardId != "" {
			unopenedRewardsFilter.AndFilter.RewardId = rewardId
		}
		unopenedRewards, beRewardsPageCtxRes, err := r.getFeRewardsForActor(ctx, actorId, unopenedRewardsFilter, beRewardsPageCtxReq, userAndAppAttributes)
		if err != nil {
			logger.Error(ctx, "error while fetching unopened rewards for actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
			return &fePb.GetRewardsForActorV1Response{
				Status: rpc.StatusInternalWithDebugMsg("error while fetching unopened rewards for actor"),
			}, nil
		}

		orderRewardsWrapperV1ListForDisplay(unopenedRewards)

		return &fePb.GetRewardsForActorV1Response{
			Status:                rpc.StatusOk(),
			Rewards:               unopenedRewards,
			PageContext:           r.getFePageContextFromBeRewardPageContext(beRewardsPageCtxRes),
			BulkClaimRewardsCtaV2: r.getBulkClaimRewardsCtaV2(ctx, actorId, userAndAppAttributes, unopenedRewards),
		}, nil
	}

	// we'll need to fetch both exchanger orders and rewards for user for other
	// RewardTileStatus filter values. in this case the page context object will be a
	// multi-entity pagination object
	bePageCtxReqs, err := splitFePageCtxReqToBePageCtxReqs(req.GetPageContext(), int(r.rewardsFrontendMeta.AppFetchRewardsPageSize), 2)
	if err != nil {
		logger.Error(ctx, "error splitting concatenated page tokens", zap.Any("fePageCtxReq", req.GetPageContext()), zap.Error(err))
		return &fePb.GetRewardsForActorV1Response{Status: rpc.StatusInternalWithDebugMsg("error splitting concatenated page tokens")}, nil
	}

	var wg = &sync.WaitGroup{}
	bePageCtxRespList := make([]*rpc.PageContextResponse, 2)
	errorChannel := make(chan error, 2)
	var allFeRewards []*fePb.RewardWrapperV1

	// first page token in bePageCtxReqs list is for rewards pagination
	rewardsPageCtxReq := bePageCtxReqs[0]
	if rewardsPageCtxReq != nil {
		wg.Add(1)
		goroutine.RunWithCtx(ctx, func(ctx context.Context) {
			defer wg.Done()
			var fetchRewardsFilter *beRewardsPb.RewardsByActorIdRequest_FiltersV2
			if req.GetFilters().GetRewardTileStatus() == fePb.GetRewardsForActorV1Request_REWARD_TILE_STATUS_OPENED {
				// to show opened reward tiles, we'll fetch all reward tiles that either:
				// 1. are having a status that shows opened reward tile always (i.e. reward has been chosen)
				// 2. have claim type automatic
				fetchRewardsFilter = &beRewardsPb.RewardsByActorIdRequest_FiltersV2{
					OrFilter: &beRewardsPb.RewardsByActorIdRequest_Filter{
						Statuses:  getOpenRewardTileRewardStatuses(),
						ClaimType: beRewardsPb.ClaimType_CLAIM_TYPE_AUTOMATIC,
					},
				}
			}

			feRewards, beRewardsPageCtxRes, err := r.getFeRewardsForActor(ctx, actorId, fetchRewardsFilter, rewardsPageCtxReq, userAndAppAttributes)
			if err != nil {
				logger.Error(ctx, "error while fetching rewards for actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
				errorChannel <- fmt.Errorf("error while fetching rewards for actor")
				return
			}
			allFeRewards = append(allFeRewards, feRewards...)
			bePageCtxRespList[0] = beRewardsPageCtxRes
		})
	} else {
		bePageCtxRespList[1] = &rpc.PageContextResponse{
			HasAfter: false,
		}
	}

	// second page token in the bePageCtxReqs list is for exchanger-orders pagination
	exchangerOrdersPageCtxReq := bePageCtxReqs[1]
	exchangerOrdersRewardWrapperV1Channel := make(chan *fePb.RewardWrapperV1, r.rewardsFrontendMeta.AppFetchRewardsPageSize)
	if exchangerOrdersPageCtxReq != nil {
		wg.Add(1)
		goroutine.RunWithCtx(ctx, func(ctx context.Context) {
			defer wg.Done()
			defer logger.RecoverPanicAndError(ctx)

			exchangerOrdersRes, err := r.exchangerOfferClient.GetExchangerOfferOrdersForActor(ctx, &beExchangerPb.GetExchangerOfferOrdersForActorRequest{
				ActorId:     actorId,
				PageContext: exchangerOrdersPageCtxReq,
				// todo(rohan): add "user-intervention-reqd" state here in the future if reqd
				Filters: &beExchangerPb.GetExchangerOfferOrdersForActorRequest_Filters{
					States: getOpenExchangerRewardTileExchangerOrderStates(),
					// We won't be loading Fi-Coins rewards of CBR since they aren't exactly rewards, but more like a
					// compensation for spending their Fi-coins in CBR. Though, the fi-coins are added in their balance.
					RewardTypes: getOpenExchangerRewardTileRewardTypes(),
				},
			})
			if err != nil || !exchangerOrdersRes.GetStatus().IsSuccess() {
				logger.Error(ctx, "error fetching be-exchanger-orders for actor", zap.Any(logger.RPC_STATUS, exchangerOrdersRes.GetStatus()), zap.Error(err))
				errorChannel <- fmt.Errorf("exchangerOfferClient.GetExchangerOfferOrdersForActor rpc call failed")
				return
			}
			// convert be exchanger-orders list to fe RewardWrapperV1 list
			for _, beOrder := range exchangerOrdersRes.GetExchangerOfferOrders() {
				feOrder, err := r.convertToFeExchangerOrder(ctx, beOrder, nil, tags.RenderLocationUnspecified)
				if err != nil {
					logger.Error(ctx, "error converting beOrder to feOrder", zap.String(logger.EXCHANGER_OFFER_ORDER_ID, beOrder.GetId()), zap.Error(err))
					continue
				}
				tileText, err := r.getTileTextV1ForExchangerOrder(feOrder.GetStatus())
				if err != nil {
					logger.Error(ctx, "unable to get tile text for reward", zap.String(logger.EXCHANGER_OFFER_ORDER_ID, beOrder.GetId()), zap.String("exchangerOrderState", beOrder.GetState().String()), zap.Error(err))
					continue
				}

				exchangerOrdersRewardWrapperV1Channel <- &fePb.RewardWrapperV1{
					Data:                   &fePb.RewardWrapperV1_ExchangerOrder{ExchangerOrder: feOrder},
					TileText:               tileText,
					RewardTileDisplayState: r.getRewardTileDisplayStateForExchangerOrder(feOrder.GetStatus()),
					RewardTileDeeplink:     r.getRewardTileRedirectionDeeplinkForExchangerOrder(ctx, beOrder, userAndAppAttributes.IsUserPartOfInternalUserGrp),
				}
			}
			bePageCtxRespList[1] = exchangerOrdersRes.GetPageContext()
		})
	} else {
		// since next page is not there setting HasAfter as false is the pageTokenResp for exchanger-orders
		bePageCtxRespList[1] = &rpc.PageContextResponse{
			HasAfter: false,
		}
	}

	var (
		rewardSummaryWidget *widgetPb.RewardSummaryWidget
		fiCoinsBalance      uint32
		rewardSummaryRes    *beRewardsPb.GetRewardSummaryResponse
		exchangerSummaryRes *beExchangerPb.GetExchangerOffersOrdersSummaryResponse
		topStickyBanner     *ui.IconTextComponent
	)
	// generate reward summary widget only for the first call and when merged catalog page is enabled
	if req.GetPageContext() == nil && userAndAppAttributes.IsFeatureRewardsCatalogMergedPageEnabled {
		wg.Add(2)
		goroutine.RunWithCtx(ctx, func(ctx context.Context) {
			defer wg.Done()
			defer logger.RecoverPanicAndError(ctx)

			var rewardSummaryErr error

			// Fetch reward summary from rewards generator
			rewardSummaryRes, rewardSummaryErr = r.rewardsGeneratorClient.GetRewardSummary(ctx, &beRewardsPb.GetRewardSummaryRequest{
				ActorId: actorId,
			})
			if rpcErr := epifigrpc.RPCError(rewardSummaryRes, rewardSummaryErr); rpcErr != nil {
				logger.Error(ctx, "GetRewardSummary call failed in GetLifetimeRewardInfo", zap.Any(logger.RPC_STATUS, rewardSummaryRes.GetStatus()), zap.Error(rpcErr))
				errorChannel <- fmt.Errorf("GetRewardSummary call failed")
			}
		})

		goroutine.RunWithCtx(ctx, func(ctx context.Context) {
			defer wg.Done()
			defer logger.RecoverPanicAndError(ctx)

			var fiCoinsBalanceErr, exchangerSummaryErr error

			// Fetch Fi Coin balance
			fiCoinsBalance, fiCoinsBalanceErr = r.getFiCoinsBalanceForActor(ctx, actorId)
			if fiCoinsBalanceErr != nil {
				logger.Error(ctx, "error fetching fi coins balance for GetLifetimeRewardInfo", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
				errorChannel <- fmt.Errorf("error fetching fi coins balance for actor")
			}

			// Fetch exchanger offer orders summary
			exchangerSummaryRes, exchangerSummaryErr = r.getExchangerOfferOrdersRewardsSummary(ctx, actorId)
			if exchangerSummaryErr != nil {
				logger.Error(ctx, "error fetching exchanger orders summary in GetLifetimeRewardInfo", zap.Error(exchangerSummaryErr))
				errorChannel <- fmt.Errorf("error fetching exchanger orders summary")
			}
		})
	}

	// wait until rewards and exchanger-orders rpc calls complete
	waitgroup.SafeWaitCtx(ctx, wg)

	// todo: see if we need to add checks for errorChannel
	// if we get an error in errorChannel, we'll return StatusInternal
	if len(errorChannel) > 0 {
		logger.Error(ctx, "some error(s) occurred while fetching rewards and exchanger orders", zap.String(logger.ACTOR_ID_V2, actorId))
		return &fePb.GetRewardsForActorV1Response{
			Status: rpc.StatusInternalWithDebugMsg("some error(s) occurred while fetching rewards and exchanger orders"),
		}, nil
	}

	// convert rewardWrapperV1Channel to
	rewardWrapperV1ChanLen := len(exchangerOrdersRewardWrapperV1Channel)
	for i := 0; i < rewardWrapperV1ChanLen; i++ {
		allFeRewards = append(allFeRewards, <-exchangerOrdersRewardWrapperV1Channel)
	}
	// order rewardWrapperV1 list in particular fashion to be displayed on APP
	orderRewardsWrapperV1ListForDisplay(allFeRewards)

	featureFlagToEnabledMap := r.fetchFeatureFlagToEnabledMap(ctx, actorId)

	// generate reward summary widget only for the first call and when merged catalog page is enabled
	if req.GetPageContext() == nil && userAndAppAttributes.IsFeatureRewardsCatalogMergedPageEnabled {
		var (
			rewardsSummaryWidgetConfig = r.dyconf.RewardsFrontendMeta().OffersCatalogPageConfig().SectionsConfig().MyEarningsSection().RedeemedRewardsPageConfig().RewardsSummaryWidget()
			fiCoinsConfig              = accrualPkg.ReturnApplicableValue(rewardsSummaryWidgetConfig.FiCoinsBalanceSummary(), rewardsSummaryWidgetConfig.FiPointsBalanceSummary(), nil, true).(*genconf.RewardSummaryWidgetRow)
			cashbackConfig             = rewardsSummaryWidgetConfig.LifetimeCashbackSummary()

			// Calculate lifetime cashback (total cash earned from both sources)
			lifetimeCashback = rewardSummaryRes.GetTotalCashRewardEarned().GetUnits() + exchangerSummaryRes.GetCashEarned().GetUnits()
		)

		rows := []*widgetPb.RewardSummaryWidget_RewardSummaryRow{
			feRewardsPkg.GetRewardSummaryWidgetRow(fiCoinsConfig, float64(fiCoinsBalance), nil),
			feRewardsPkg.GetRewardSummaryWidgetRow(cashbackConfig, float64(lifetimeCashback), nil),
		}

		rewardSummaryWidget = &widgetPb.RewardSummaryWidget{
			Heading:           commontypes.GetTextFromStringFontColourFontStyle("TOTAL REWARDS", colorBlack, commontypes.FontStyle_OVERLINE_2XS_CAPS),
			RewardSummaryRows: rows,
		}
	}

	if featureFlagToEnabledMap[types.Feature_FEATURE_FI_COINS_TO_FI_POINTS_POST_MIGRATION_PHASE] {
		topStickyBanner = ui.NewITC().
			WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Fi-Coins balance until now were converted to Fi-Points", colorGunmetal, commontypes.FontStyle_SUBTITLE_XS)).
			WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(infoCircleUrl, 16, 16)).
			WithContainerWidgetBackgroundColor(widget.GetLinearGradientBackgroundColour(90, []*widget.ColorStop{
				{
					Color:          "#FCF1E4",
					StopPercentage: 0,
				},
				{
					Color:          "#FDDCA8",
					StopPercentage: 100,
				}})).
			WithContainerPadding(8, 8, 8, 8)
	}

	// construct success response
	successResp := &fePb.GetRewardsForActorV1Response{
		Status:          rpc.StatusOk(),
		Rewards:         allFeRewards,
		SummaryWidget:   rewardSummaryWidget,
		PageContext:     mergeBePageCtxRespListToFePageCtxResp(bePageCtxRespList),
		TopStickyBanner: topStickyBanner,
	}

	return successResp, nil
}

func (r *RewardService) GetMyRewardsScreenDetails(ctx context.Context, req *fePb.GetMyRewardsScreenDetailsRequest) (*fePb.GetMyRewardsScreenDetailsResponse, error) {
	// check if fi coins to fi points migration is active
	if accrualPkg.IsFiCoinsToFiPointsMigrationInProgress() {
		return &fePb.GetMyRewardsScreenDetailsResponse{
			Status: rpc.StatusUnavailableWithDebugMsg(accrualPkg.ErrFiCoinsToFiPointsMigrationInProgress.Error()),
			RespHeader: &header.ResponseHeader{
				ErrorView: GetFiCoinsToFiPointsDowntimeErrorView(),
			},
		}, nil
	}

	var (
		actorId                                = req.GetReq().GetAuth().GetActorId()
		isFeatureHomeDesignEnhancementsEnabled = featureflags.IsFeatureHomeDesignEnhancementsEnabled(ctx, &featureflags.IsFeatureHomeDesignEnhancementsEnabledRequest{
			ActorId: actorId,
			ExternalDeps: &common.ExternalDependencies{
				Evaluator:            r.releaseEvaluator,
				OnboardingClient:     r.onboardingClient,
				QuestSdkClient:       r.questSdkClient,
				UserAttributeFetcher: r.userAttributeFetcher,
				NetWorthClient:       r.networthClient,
			},
		})
	)

	userAndAppAttributes, err := r.getUserAndAppAttributes(ctx, req.GetReq().GetAuth(), req.GetReq().GetAppVersionCode())
	if err != nil {
		logger.Error(ctx, "error while fetching user and app attributes", zap.String(logger.ACTOR_ID_V2, actorId), zap.Uint32(logger.APP_VERSION_CODE, req.GetReq().GetAppVersionCode()), zap.Error(err))
		return &fePb.GetMyRewardsScreenDetailsResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while fetching user and app attributes"),
		}, nil
	}

	var sections []*fePb.GetMyRewardsScreenDetailsResponse_Section

	// summary section. client makes separate call to GetRewardSummary RPC to fetch
	// rewards summary. we're sending empty section for controlling the ordering of
	// sections.
	sections = append(sections, &fePb.GetMyRewardsScreenDetailsResponse_Section{
		SectionDetails: &fePb.GetMyRewardsScreenDetailsResponse_Section_RewardsSummary{
			RewardsSummary: &fePb.GetMyRewardsScreenDetailsResponse_RewardsSummary{},
		},
	})

	// widgets section
	widgets := &fePb.GetMyRewardsScreenDetailsResponse_Widgets{
		Ctas: []*fePb.CtaV1{
			{
				RightIconUrl: "https://epifi-icons.pointz.in/rewards/my_rewards_screen_ways_to_earn_icon.png",
				CtaTitle: &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{PlainString: "Earn more Rewards"},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_HEADLINE_S,
					},
					FontColor: colorFiSnow,
				},
				BgColor: colorDarkOcean,
				Action: &fePb.CtaV1_DeeplinkAction{
					DeeplinkAction: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_REWARDS_WAYS_TO_EARN,
					},
				},
				Shadow: &ui.Shadow{
					Height: 4,
					Colour: &ui.BackgroundColour{
						Colour: &ui.BackgroundColour_BlockColour{
							BlockColour: "#7B5B30",
						},
					},
				},
				BgColorV2: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_RadialGradient{
						RadialGradient: &widget.RadialGradient{
							Center: &widget.CenterCoordinates{
								CenterX: 0,
								CenterY: 0,
							},
							OuterRadius: 20,
							Colours:     []string{"#D3B250", "#AC7C44"},
						},
					},
				},
				LeftIcon: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/my_rewards_screen_ways_to_earn_double_coins_icon.png", 48, 48),
			},
		},
	}

	if apputils.IsFeatureEnabledFromCtxDynamic(ctx, r.dyconf.RewardsFrontendMeta().MyRewardsScreenCtaCarouselFeatureConfig()) {
		widgets.Ctas = append(widgets.GetCtas(), &fePb.CtaV1{
			CtaTitle: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Shop on Fi-Store"},
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_HEADLINE_S,
				},
				FontColor: colorFiSnow,
			},
			Action: &fePb.CtaV1_DeeplinkAction{
				DeeplinkAction: &deeplink.Deeplink{
					Screen: deeplink.Screen_WEB_PAGE_WITH_CARD_DETAILS_SCREEN,
					ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&pkgScreenOptionsPb.WebPageWithCardDetailsScreenOptions{
						WebpageTitle:             "Fi Store",
						WebpageUrl:               "",
						DisableHardwareBackPress: true,
						DisableDropDownAnimation: true,
						ShowUpiDetails:           true,
						ShowCreditCardDetails:    true,
						RequestMetadata:          fmt.Sprintf("vendor:DPANDA,target_url:fimoney.staging.dpanda.io/"),
					}),
				},
			},
			Shadow: &ui.Shadow{
				Height: 4,
				Colour: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{
					BlockColour: "#3C5B86",
				}},
			},
			BgColorV2: &widget.BackgroundColour{
				Colour: &widget.BackgroundColour_RadialGradient{
					RadialGradient: &widget.RadialGradient{
						Center: &widget.CenterCoordinates{
							CenterX: 0,
							CenterY: 0,
						},
						OuterRadius: 20,
						Colours:     []string{"#7594C9", "#1C4B8B"},
					},
				},
			},
			LeftIcon: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/rewards/fi-store-48-48.png", 48, 48),
		})
	}

	errGrp, gctx := errgroup.WithContext(ctx)

	// unopened rewards section

	var unOpenedMoneyPlantSection *fePb.GetMyRewardsScreenDetailsResponse_Section
	errGrp.Go(func() error {
		unopenedRewards, sectionTitle, rpcErr := r.getUnopenedRewardsAndTitle(gctx, actorId, userAndAppAttributes)
		if rpcErr != nil {
			return fmt.Errorf("error while getting unopened rewards and section title: %w", rpcErr)
		}
		if len(unopenedRewards) > 0 {
			maxUnopenedMoneyPlantsToDisplay := min(len(unopenedRewards), int(r.rewardsFrontendMeta.MaxUnopenedRewardsToShowOnMyRewardsScreen))
			unOpenedMoneyPlantSection = &fePb.GetMyRewardsScreenDetailsResponse_Section{
				HeaderTitle: commontypes.GetTextFromStringFontColourFontStyle(sectionTitle, colorContentOnLightHighEmphasis, commontypes.FontStyle_SUBTITLE_M),
				HeaderCta: r.getViewAllCtaV1("VIEW ALL",
					&deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_UNREDEEMED_REWARDS_SCREEN},
					isFeatureHomeDesignEnhancementsEnabled,
				),
				SectionDetails: &fePb.GetMyRewardsScreenDetailsResponse_Section_UnopenedRewardTiles{
					UnopenedRewardTiles: &fePb.GetMyRewardsScreenDetailsResponse_UnopenedRewardTiles{
						Rewards:               unopenedRewards[:maxUnopenedMoneyPlantsToDisplay],
						BulkClaimRewardsCtaV2: r.getBulkClaimRewardsCtaV2(ctx, actorId, userAndAppAttributes, unopenedRewards),
					},
				},
			}
		}
		return nil
	})

	// opened rewards section
	var openedMoneyPlantSection *fePb.GetMyRewardsScreenDetailsResponse_Section
	areOpenedRewardsPresent := false
	errGrp.Go(func() error {
		openedRewards, err := r.getOpenedMoneyPlants(gctx, actorId, userAndAppAttributes)
		if err != nil {
			return fmt.Errorf("error in getting opened money plant")
		}
		if len(openedRewards) > 0 {
			areOpenedRewardsPresent = true
		}

		sectionTitle := &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: "All Rewards",
			},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
			},
			FontColor: colorContentOnLightHighEmphasis,
		}
		if !apputils.IsFeatureEnabledFromCtxDynamic(ctx, r.dyconf.RewardsFrontendMeta().MyRewardsScreenNewOpenedRewardsWidgetFeatureConfig()) {
			openedMoneyPlantSection = &fePb.GetMyRewardsScreenDetailsResponse_Section{
				HeaderTitle: sectionTitle,
				SectionDetails: &fePb.GetMyRewardsScreenDetailsResponse_Section_OpenRewardTiles{
					OpenRewardTiles: &fePb.GetMyRewardsScreenDetailsResponse_OpenRewardTiles{},
				},
			}
			return nil
		}

		if len(openedRewards) > 0 {
			maxOpenedMoneyPlantsToDisplay := len(openedRewards)
			if maxOpenedMoneyPlantsToDisplay > int(r.rewardsFrontendMeta.MaxOpenedRewardsToShowOnMyRewardsScreen) {
				maxOpenedMoneyPlantsToDisplay = int(r.rewardsFrontendMeta.MaxOpenedRewardsToShowOnMyRewardsScreen)
			}

			orderRewardsWrapperV1ListForDisplay(openedRewards)

			openedMoneyPlantSection = &fePb.GetMyRewardsScreenDetailsResponse_Section{
				HeaderTitle: sectionTitle,
				HeaderCta: r.getViewAllCtaV1("VIEW ALL",
					&deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_REDEEMED_REWARDS_SCREEN},
					isFeatureHomeDesignEnhancementsEnabled,
				),
				SectionDetails: &fePb.GetMyRewardsScreenDetailsResponse_Section_OpenRewardTiles{
					OpenRewardTiles: &fePb.GetMyRewardsScreenDetailsResponse_OpenRewardTiles{
						Rewards: openedRewards[:maxOpenedMoneyPlantsToDisplay],
					},
				},
			}
		}
		return nil
	})

	var navBarCta *fePb.CtaV1
	errGrp.Go(func() error {
		isNrUserRes, isNrUserErr := r.usersClient.IsNonResidentUser(ctx, &beUserPb.IsNonResidentUserRequest{
			Identifier: &beUserPb.IsNonResidentUserRequest_ActorId{
				ActorId: actorId,
			},
		})
		if rpcErr := epifigrpc.RPCError(isNrUserRes, isNrUserErr); rpcErr != nil {
			logger.Error(ctx, "error while checking if user is non-resident", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
			// we'll not show any CTA in case of error
			return nil
		}

		if !isNrUserRes.GetIsNonResidentUser().ToBool() {
			navBarCta = &fePb.CtaV1{
				LeftIconUrl: "https://epifi-icons.pointz.in/salaryprogram/question-mark-circle.png",
				Action: &fePb.CtaV1_DeeplinkAction{
					DeeplinkAction: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_HELP_MAIN,
					},
				},
			}
		}

		return nil
	})
	var isFiLiteUser bool
	errGrp.Go(func() error {
		featureDetailsResp, featureDetailsErr := r.onboardingClient.GetFeatureDetails(ctx, &onboardingPb.GetFeatureDetailsRequest{
			ActorId: actorId,
			Feature: onboardingPb.Feature_FEATURE_FI_LITE,
		})
		if rpcErr := epifigrpc.RPCError(featureDetailsResp, featureDetailsErr); rpcErr != nil {
			logger.Error(ctx, "error while checking user is Fi Lite or not", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
			return nil
		}
		isFiLiteUser = featureDetailsResp.GetIsFiLiteUser()

		return nil
	})

	if grpErr := errGrp.Wait(); grpErr != nil {
		return &fePb.GetMyRewardsScreenDetailsResponse{
			Status: rpc.StatusInternalWithDebugMsg(grpErr.Error()),
		}, nil
	}

	secondCTAText, secondCTAIcon, secondCTADeeplink := getSecondCTADetailsForMyRewardsScreen(isFiLiteUser)
	widgets.Ctas = append(widgets.GetCtas(), &fePb.CtaV1{
		RightIconUrl: secondCTAIcon,
		CtaTitle: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: secondCTAText},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_HEADLINE_S,
			},
			FontColor: colorFiSnow,
		},
		BgColor: colorDarkBerry,
		Action: &fePb.CtaV1_DeeplinkAction{
			DeeplinkAction: secondCTADeeplink,
		},
		Shadow: &ui.Shadow{
			Height: 4,
			Colour: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{
				BlockColour: "#1d1557",
			}},
		},
		BgColorV2: &widget.BackgroundColour{
			Colour: &widget.BackgroundColour_RadialGradient{
				RadialGradient: &widget.RadialGradient{
					Center: &widget.CenterCoordinates{
						CenterX: 0,
						CenterY: 0,
					},
					OuterRadius: 20,
					Colours:     []string{"#6F62A4", "#453C86"},
				},
			},
		},
		LeftIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(secondCTAIcon, 48, 48),
	})

	sections = append(sections, &fePb.GetMyRewardsScreenDetailsResponse_Section{
		SectionDetails: &fePb.GetMyRewardsScreenDetailsResponse_Section_Widgets{
			Widgets: widgets,
		},
	})

	// promotion widget section
	sections = append(sections, &fePb.GetMyRewardsScreenDetailsResponse_Section{
		SectionDetails: &fePb.GetMyRewardsScreenDetailsResponse_Section_PromotionWidget{
			PromotionWidget: &fePb.GetMyRewardsScreenDetailsResponse_PromotionBannerWidget{},
		},
	})

	if apputils.IsFeatureEnabledFromCtxDynamic(ctx, r.dyconf.RewardsFrontendMeta().MyRewardsScreenOfferWidgetFeatureConfig()) {
		sections = append(sections, &fePb.GetMyRewardsScreenDetailsResponse_Section{
			SectionDetails: &fePb.GetMyRewardsScreenDetailsResponse_Section_CatalogOffersCardWidget{
				CatalogOffersCardWidget: &fePb.GetMyRewardsScreenDetailsResponse_CatalogOffersCardWidget{},
			},
		})
	}

	if unOpenedMoneyPlantSection != nil {
		sections = append(sections, unOpenedMoneyPlantSection)
	}

	if openedMoneyPlantSection != nil {
		sections = append(sections, openedMoneyPlantSection)
	}

	switch {
	case len(unOpenedMoneyPlantSection.GetUnopenedRewardTiles().GetRewards()) == 0 && !areOpenedRewardsPresent:
		// if no opened and unopened money plants are there, show zero state widget
		zeroStateWidgetSection, sectionErr := r.getZeroStateWidget(ctx, isFiLiteUser)
		if sectionErr != nil {
			return &fePb.GetMyRewardsScreenDetailsResponse{
				Status: rpc.StatusInternalWithDebugMsg(sectionErr.Error()),
			}, nil
		}
		sections = append(sections, zeroStateWidgetSection)
	case len(unOpenedMoneyPlantSection.GetUnopenedRewardTiles().GetRewards()) > 0 && !areOpenedRewardsPresent:
		// if only unopened money plants are there, show zero opened money plants widget
		sections = append(sections, getZeroOpenedMoneyPlantsSection())
	}
	// add feedback engine info to show referrals happy flow bottom sheet, triggered by feedback engine
	feedbackEngineInfo := pkgHome.GetFeedbackAppFlowHeaderForReferralsHappyFlow()
	return &fePb.GetMyRewardsScreenDetailsResponse{
		RespHeader: &header.ResponseHeader{
			FeedbackEngineInfo: feedbackEngineInfo,
			Status:             rpc.StatusOk(),
		},
		Sections:  sections,
		NavBarCta: navBarCta,
	}, nil
}

func (r *RewardService) getUnopenedRewardsAndTitle(ctx context.Context, actorId string, userAndAppAttributes *UserAndAppAttributes) ([]*fePb.RewardWrapperV1, string, error) {
	var sectionTitle string
	unopenedRewardsPageCtxReq := &rpc.PageContextRequest{PageSize: r.rewardsFrontendMeta.MaxUnopenedRewardsToShowOnMyRewardsScreen}
	unopenedRewardsFilter := &beRewardsPb.RewardsByActorIdRequest_FiltersV2{
		AndFilter: &beRewardsPb.RewardsByActorIdRequest_Filter{
			ClaimType: beRewardsPb.ClaimType_CLAIM_TYPE_MANUAL,
			Statuses:  []beRewardsPb.RewardStatus{beRewardsPb.RewardStatus_CREATED, beRewardsPb.RewardStatus_LOCKED},
		},
	}
	unopenedRewards, pageCtxRes, err := r.getFeRewardsForActor(ctx, actorId, unopenedRewardsFilter, unopenedRewardsPageCtxReq, userAndAppAttributes)
	if err != nil {
		logger.Error(ctx, "error while getting unopened rewards for actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return nil, "", fmt.Errorf("error while getting unopened rewards for actor")
	}

	if len(unopenedRewards) > 0 {
		unopenedRewardsCountString := strconv.Itoa(len(unopenedRewards))
		if pageCtxRes.GetHasAfter() {
			unopenedRewardsCountString = fmt.Sprintf("%d+", r.rewardsFrontendMeta.MaxUnopenedRewardsToShowOnMyRewardsScreen)
		}
		sectionTitle = fmt.Sprintf("Unopened Money-Plants (%s)", unopenedRewardsCountString)
		orderRewardsWrapperV1ListForDisplay(unopenedRewards)
	}

	if userAndAppAttributes.IsFeatureRewardsCatalogMergedPageEnabled {
		lo.ForEach(unopenedRewards, func(unopenedReward *fePb.RewardWrapperV1, _ int) {
			unopenedReward.BgColor = widget.GetLinearGradientBackgroundColour(90, []*widget.ColorStop{
				{Color: "#254543", StopPercentage: 0},
				{Color: "#161E1B", StopPercentage: 100},
			})
		})
	}

	return unopenedRewards, sectionTitle, nil
}

func (r *RewardService) getZeroStateWidget(ctx context.Context, isFiLiteUser bool) (*fePb.GetMyRewardsScreenDetailsResponse_Section, error) {
	dl, errDL := onboarding.GetSABenefitsScreen(ctx)
	if errDL != nil {
		logger.Error(ctx, "failed to get SA Benefit Screen", zap.Error(errDL))
		return nil, errDL
	}

	desc := "You don't have any Money-Plants yet. \nOnce you collect them, they will appear here."
	var cta *ui.IconTextComponent
	if isFiLiteUser {
		desc = "No Money-Plants yet. Open a Savings \n Account to start earning them"
		cta = &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle("Open account", "#FFFFFF", commontypes.FontStyle_HEADLINE_M),
			},
			Deeplink:           dl,
			RightVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/rewards/white-right-arrow-icon.png", 16, 16),
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:       "#00B899",
				CornerRadius:  20,
				LeftPadding:   12,
				RightPadding:  12,
				TopPadding:    8,
				BottomPadding: 8,
			},
		}
	}

	return &fePb.GetMyRewardsScreenDetailsResponse_Section{
		HeaderTitle: commontypes.GetTextFromStringFontColourFontStyle("All Rewards", "#313234", commontypes.FontStyle_SUBTITLE_S),
		SectionDetails: &fePb.GetMyRewardsScreenDetailsResponse_Section_ZeroStateWidget{
			ZeroStateWidget: &fePb.GetMyRewardsScreenDetailsResponse_ZeroStateWidget{
				Image: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/rewards/chrysanthemum-plant-black.png"),
				Desc:  commontypes.GetTextFromStringFontColourFontStyle(desc, "#929599", commontypes.FontStyle_SUBTITLE_S),
				Cta:   cta,
			},
		},
	}, nil
}

func getZeroOpenedMoneyPlantsSection() *fePb.GetMyRewardsScreenDetailsResponse_Section {
	return &fePb.GetMyRewardsScreenDetailsResponse_Section{
		HeaderTitle: commontypes.GetTextFromStringFontColourFontStyle("All Rewards", "#313234", commontypes.FontStyle_SUBTITLE_S),
		SectionDetails: &fePb.GetMyRewardsScreenDetailsResponse_Section_ZeroStateWidget{
			ZeroStateWidget: &fePb.GetMyRewardsScreenDetailsResponse_ZeroStateWidget{
				Image: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/rewards/chrysanthemum-plant-black.png"),
				Desc:  commontypes.GetTextFromStringFontColourFontStyle("Your rewards will start to appear here after you've opened your money-plants", "#929599", commontypes.FontStyle_SUBTITLE_S),
			},
		},
	}
}

// nolint: funlen, gocritic
func (r *RewardService) getOpenedMoneyPlants(ctx context.Context, actorId string, userAndAppAttributes *UserAndAppAttributes) ([]*fePb.RewardWrapperV1, error) {
	var allFeRewards []*fePb.RewardWrapperV1

	fetchRewardsFilter := &beRewardsPb.RewardsByActorIdRequest_FiltersV2{
		OrFilter: &beRewardsPb.RewardsByActorIdRequest_Filter{
			Statuses:  getOpenRewardTileRewardStatuses(),
			ClaimType: beRewardsPb.ClaimType_CLAIM_TYPE_AUTOMATIC,
		},
	}

	errGrp, gctx := errgroup.WithContext(ctx)

	errGrp.Go(func() error {

		rewardsPageCtxReq := &rpc.PageContextRequest{PageSize: r.rewardsFrontendMeta.MaxOpenedRewardsToShowOnMyRewardsScreen}
		feRewards, _, err := r.getFeRewardsForActor(gctx, actorId, fetchRewardsFilter, rewardsPageCtxReq, userAndAppAttributes)
		if err != nil {
			logger.Error(gctx, "error while fetching rewards for actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
			return fmt.Errorf("error while fetching rewards for actor")
		}
		allFeRewards = append(allFeRewards, feRewards...)
		return nil
	})

	var allFeExchangerOrders []*fePb.RewardWrapperV1

	errGrp.Go(func() error {

		exchangerOrdersPageCtxReq := &rpc.PageContextRequest{PageSize: r.rewardsFrontendMeta.AppFetchRewardsPageSize}

		exchangerOrdersRes, err := r.exchangerOfferClient.GetExchangerOfferOrdersForActor(gctx, &beExchangerPb.GetExchangerOfferOrdersForActorRequest{
			ActorId:     actorId,
			PageContext: exchangerOrdersPageCtxReq,
			// todo(rohan): add "user-intervention-reqd" state here in the future if reqd
			Filters: &beExchangerPb.GetExchangerOfferOrdersForActorRequest_Filters{
				States: []beExchangerPb.ExchangerOfferOrderState{
					beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_OPTION_CHOSEN,
					beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_FULFILLMENT_INITIATED,
					beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_FULFILLED,
				},
				// We won't be loading Fi-Coins rewards of CBR since they aren't exactly rewards, but more like a
				// compensation for spending their Fi-coins in CBR. Though, the fi-coins are added in their balance.
				RewardTypes: []beExchangerPb.RewardType{
					beExchangerPb.RewardType_REWARD_TYPE_CASH,
					beExchangerPb.RewardType_REWARD_TYPE_CREDIT_CARD_BILL_ERASER,
				},
			},
		})
		if err != nil || !exchangerOrdersRes.GetStatus().IsSuccess() {
			logger.Error(gctx, "error fetching be-exchanger-orders for actor", zap.Any(logger.RPC_STATUS, exchangerOrdersRes.GetStatus()), zap.Error(err))
			return fmt.Errorf("exchangerOfferClient.GetExchangerOfferOrdersForActor rpc call failed")
		}
		// convert be exchanger-orders list to fe RewardWrapperV1 list
		for _, beOrder := range exchangerOrdersRes.GetExchangerOfferOrders() {
			feOrder, err := r.convertToFeExchangerOrder(gctx, beOrder, nil, tags.RenderLocationUnspecified)
			if err != nil {
				logger.Error(gctx, "error converting beOrder to feOrder", zap.String(logger.EXCHANGER_OFFER_ORDER_ID, beOrder.GetId()), zap.Error(err))
				continue
			}
			tileText, err := r.getTileTextV1ForExchangerOrder(feOrder.GetStatus())
			if err != nil {
				logger.Error(ctx, "unable to get tile text for reward", zap.String(logger.EXCHANGER_OFFER_ORDER_ID, beOrder.GetId()), zap.String("exchangerOrderState", beOrder.GetState().String()), zap.Error(err))
				continue
			}
			allFeExchangerOrders = append(allFeExchangerOrders, &fePb.RewardWrapperV1{Data: &fePb.RewardWrapperV1_ExchangerOrder{ExchangerOrder: feOrder}, TileText: tileText, RewardTileDisplayState: r.getRewardTileDisplayStateForExchangerOrder(feOrder.GetStatus())})
		}
		return nil
	})

	if grpErr := errGrp.Wait(); grpErr != nil {
		return nil, fmt.Errorf("%v", grpErr.Error())
	}

	allFeRewardsAndExchangerOrders := append(allFeRewards, allFeExchangerOrders...)

	// order rewardWrapperV1 list in particular fashion to be displayed on APP
	orderRewardsWrapperV1ListForDisplay(allFeRewardsAndExchangerOrders)

	return allFeRewardsAndExchangerOrders, nil
}

// nolint:funlen
func (r *RewardService) getFeRewardsForActor(ctx context.Context, actorId string, filters *beRewardsPb.RewardsByActorIdRequest_FiltersV2, pageCtxReq *rpc.PageContextRequest, userAndAppAttributes *UserAndAppAttributes) ([]*fePb.RewardWrapperV1, *rpc.PageContextResponse, error) {
	var (
		feRewards              []*fePb.RewardWrapperV1
		pageCtxRes             *rpc.PageContextResponse
		rewardWrapperV1Channel = make(chan *fePb.RewardWrapperV1, 2*r.rewardsFrontendMeta.AppFetchRewardsPageSize)
		deeplinksMap           = map[deeplinkName]*deeplink.Deeplink{}
	)
	rewardsByActorIdReq := &beRewardsPb.RewardsByActorIdRequest{
		ActorId:     actorId,
		PageContext: pageCtxReq,
		FiltersV2:   filters,
	}
	rewardsResp, err := r.rewardsGeneratorClient.GetRewardsByActorId(ctx, rewardsByActorIdReq)
	if rpcErr := epifigrpc.RPCError(rewardsResp, err); rpcErr != nil {
		logger.Error(ctx, "GetRewardsByActorId rpc call failed", zap.Any(logger.RPC_STATUS, rewardsResp.GetStatus()), zap.Error(err))
		return nil, nil, fmt.Errorf("GetRewardsByActorId rpc call failed")
	}

	if userAndAppAttributes.KycLevel != beKycPb.KYCLevel_FULL_KYC {
		deeplinksMap[minKycToFullKycConversionDeeplink], err = vkyc.BuildVKYCStatusDeeplink(&vkyc.StatusScreenOptions{
			EntryPoint: vkyc2.EntryPoint_ENTRY_POINT_REWARDS,
		})
		if err != nil {
			logger.Error(ctx, "error while fetching KYC completion deeplink for user", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		}
	}

	if userAndAppAttributes.SavingsAccountStatus != savingsPb.State_CREATED {
		deeplinksMap[openSavingsAccountDeeplink], err = onboarding.GetSABenefitsScreen(ctx)
		if err != nil {
			logger.Error(ctx, "error while fetching deeplink to open SA for user", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		}
	}

	// convert be rewards list to fe RewardWrapperV1 list in go routines as it's a heavy operation.
	var convertBeToFeRewardWg = &sync.WaitGroup{}
	for _, reward := range rewardsResp.GetRewards() {
		convertBeToFeRewardWg.Add(1)
		beReward := reward
		goroutine.RunWithCtx(ctx, func(ctx context.Context) {
			defer convertBeToFeRewardWg.Done()
			// if shouldRewardBeDisplayed returns false, we don't need to show the
			// reward in MyRewards screen.
			if !shouldRewardBeDisplayed(beReward) {
				return
			}

			feReward, err := r.getFeReward(ctx, actorId, beReward, userAndAppAttributes, deeplinksMap)
			if err != nil {
				logger.Error(ctx, "unable to convert be reward to fe reward", zap.String(logger.REWARD_ID, beReward.GetId()), zap.Error(err))
				return
			}
			tileText, err := r.getTileTextV1ForReward(feReward.GetStatus(), beReward, userAndAppAttributes)
			if err != nil {
				logger.Error(ctx, "unable to get tile text for reward", zap.String(logger.REWARD_ID, beReward.GetId()), zap.String(logger.REWARD_STATUS, feReward.GetStatus().String()), zap.Error(err))
				return
			}

			rewardWrapperV1Channel <- &fePb.RewardWrapperV1{
				Data:                   &fePb.RewardWrapperV1_Reward{Reward: feReward},
				TileText:               tileText,
				RewardTileDisplayState: r.getRewardTileDisplayStateForReward(feReward.GetStatus()),
				ShowClosedRewardTile:   shouldClosedRewardTileBeShown(feReward.GetStatus(), beReward.GetClaimType()),
				RewardTileDeeplink:     r.getRewardTileRedirectionDeeplink(ctx, beReward, userAndAppAttributes.IsUserPartOfInternalUserGrp),
				AllowRetryAction:       isRewardForceRetryable(reward),
				// RewardTileDisplayInfo: &fePb.RewardTileDisplayInfo{
				// 	Icon:     commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/rewards/green-lock.png", 44, 44),
				// 	Title:    commontypes.GetTextFromStringFontColourFontStyle("hello", colorGrayNight, commontypes.FontStyle_NUMBER_M),
				// 	Subtitle: commontypes.GetTextFromStringFontColourFontStyle("peter", "#A4A4A4", commontypes.FontStyle_BODY_S),
				// 	// BgColor:             widget.GetBlockBackgroundColour(colorDarkOcean),
				// 	// BottomVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/rewards/green-lock.png", 44, 44),
				// },
			}
		})
	}
	waitgroup.SafeWaitCtx(ctx, convertBeToFeRewardWg)

	pageCtxRes = rewardsResp.GetPageContext()

	// convert rewardWrapperV1Channel to a list
	rewardWrapperV1ChanLen := len(rewardWrapperV1Channel)
	for i := 0; i < rewardWrapperV1ChanLen; i++ {
		feRewards = append(feRewards, <-rewardWrapperV1Channel)
	}

	return feRewards, pageCtxRes, nil
}

func (r *RewardService) getRewardTileRedirectionDeeplink(ctx context.Context, beReward *beRewardsPb.Reward, isUserPartOfInternalGrp bool) *deeplink.Deeplink {
	if !apputils.IsFeatureEnabledFromCtxDynamic(ctx, r.dyconf.RewardsFrontendMeta().ClaimedRewardScreenDeeplinkFeatureConfig()) {
		return nil
	}

	// CLAIMED_REWARD_DETAILS_SCREEN supports only claimed and LOCKED rewards
	if !(beReward.GetChosenReward().GetOption() != nil || beReward.GetStatus() == beRewardsPb.RewardStatus_LOCKED) {
		return nil
	}

	if !isUserPartOfInternalGrp {
		return nil
	}

	return deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_CLAIMED_REWARD_DETAILS_SCREEN, &screenOptionsPb.ClaimedRewardDetailsScreenOptions{
		RewardId: beReward.GetId(),
	})
}

func (r *RewardService) getRewardTileRedirectionDeeplinkForExchangerOrder(ctx context.Context, beOrder *beExchangerPb.ExchangerOfferOrder, isUserPartOfInternalUserGrp bool) *deeplink.Deeplink {
	if !apputils.IsFeatureEnabledFromCtxDynamic(ctx, r.dyconf.RewardsFrontendMeta().ClaimedRewardScreenDeeplinkFeatureConfig()) {
		return nil
	}

	if !isUserPartOfInternalUserGrp {
		return nil
	}

	if !lo.Contains(getOpenExchangerRewardTileExchangerOrderStates(), beOrder.GetState()) {
		return nil
	}

	if !lo.Contains(getOpenExchangerRewardTileRewardTypes(), beOrder.GetRewardType()) {
		return nil
	}

	return deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_CLAIMED_REWARD_DETAILS_SCREEN, &screenOptionsPb.ClaimedRewardDetailsScreenOptions{
		RewardId: beOrder.GetId(),
	})
}

// shouldRewardBeDisplayed decides whether a reward should be displayed on the "My Rewards" page or not. Currently, it returns false if:
// 1. the reward is unclaimed and the offer type is not one of those reward offer types that can't be claimed from MyRewards screen (like quarterly lounge access reward) OR
// 2. the chosen reward type need not displayed on "My Rewards" page as they only need to displayed on some other specific screens on the APP like EGV_BASKET on redeemed offers screen, METAL_CARD on CC screens etc.
// 3. the reward is unclaimed, has only one option of type FI_COINS or any other type, and those FI_COINS are expired. Note: This reward will be expired by rewards_expiry script daily, but we are also handling it here to avoid showing the reward at all.
// 4. When reward offer's contain rewardExpiryTimeConfig and based on that reward is expired.
func shouldRewardBeDisplayed(reward *beRewardsPb.Reward) bool {
	// If reward is not yet claimed
	if reward.GetChosenReward() == nil {
		// if the reward option is expired, don't show it on MyRewards screen
		if len(reward.GetRewardOptions().GetOptions()) == 1 && isRewardOptionExpired(time.Now(), reward.GetRewardOptions().GetOptions()[0]) {
			return false
		}

		// if reward is expired, don't show it on MyRewards screen
		if reward.GetExpiresAt() != nil && !reward.GetExpiresAt().AsTime().IsZero() && reward.GetExpiresAt().AsTime().Before(time.Now()) {
			return false
		}

		// if reward's offer type is one of those reward offer types that can be claimed from MyRewards screen, then show it on MyRewards screen.
		unclaimedRewardOfferTypesToDisplayOnMyRewardsScreen := pkgHelper.GetUnclaimedRewardOfferTypesToDisplayOnMyRewardsScreen()
		if lo.Contains(unclaimedRewardOfferTypesToDisplayOnMyRewardsScreen, reward.GetOfferType()) {
			return true
		}

	} else { // if reward is claimed
		// we need to check if the chosen reward type is one of those reward types that need to be displayed on MyRewards screen.
		rewardTypesToDisplayOnMyRewardsScreen := pkgHelper.GetRewardTypesToDisplayOnMyRewardsScreen()
		if reward.GetChosenReward() != nil && lo.Contains(rewardTypesToDisplayOnMyRewardsScreen, reward.GetChosenReward().GetRewardType()) {
			return true
		}
	}
	return false
}

// isRewardForceRetryable returns true if reward is in force retryable reward statuses and is not updated in last 7 days.
func isRewardForceRetryable(reward *beRewardsPb.Reward) bool {
	// return false if reward is not in force retryable reward statuses
	forceRetryableRewardStatuses := []beRewardsPb.RewardStatus{beRewardsPb.RewardStatus_PROCESSING_PENDING, beRewardsPb.RewardStatus_PROCESSING_FAILED, beRewardsPb.RewardStatus_PROCESSING_MANUAL_INTERVENTION}
	if !lo.Contains(forceRetryableRewardStatuses, reward.GetStatus()) {
		return false
	}

	// return false if reward is updated in last 7 days
	if time.Since(reward.GetUpdatedAt().AsTime()) <= time.Hour*24*7 {
		return false
	}

	return true
}

// orderRewardsWrapperV1ListForDisplay order rewardWrapperV1 list in particular fashion to be displayed on APP.
// Current ordering logic -> entries are ordered in desc order of reward/exchanger-order creation timestamp.
func orderRewardsWrapperV1ListForDisplay(rewardWrapperV1List []*fePb.RewardWrapperV1) {
	sort.Slice(rewardWrapperV1List, func(i, j int) bool {
		rewardWrapperI, rewardWrapperJ := rewardWrapperV1List[i], rewardWrapperV1List[j]
		var rewardWrapperICreatedAt, rewardWrapperJCreatedAt *timestamppb.Timestamp

		if rewardWrapperI.GetReward() != nil {
			rewardWrapperICreatedAt = rewardWrapperI.GetReward().GetCreatedAt()
		} else if rewardWrapperI.GetExchangerOrder() != nil {
			rewardWrapperICreatedAt = rewardWrapperI.GetExchangerOrder().GetCreatedAt()
		}

		if rewardWrapperJ.GetReward() != nil {
			rewardWrapperJCreatedAt = rewardWrapperJ.GetReward().GetCreatedAt()
		} else if rewardWrapperJ.GetExchangerOrder() != nil {
			rewardWrapperJCreatedAt = rewardWrapperJ.GetExchangerOrder().GetCreatedAt()
		}

		return rewardWrapperICreatedAt.AsTime().After(rewardWrapperJCreatedAt.AsTime())
	})
}

func (r *RewardService) GetEarnedRewards(ctx context.Context, req *fePb.GetEarnedRewardsRequest) (*fePb.GetEarnedRewardsResponse, error) {
	userAndAppAttributes, err := r.getUserAndAppAttributes(ctx, req.GetReq().GetAuth(), req.GetReq().GetAppVersionCode())
	if err != nil {
		logger.Error(ctx, "error while fetching user and app attributes", zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()), zap.Uint32(logger.APP_VERSION_CODE, req.GetReq().GetAppVersionCode()), zap.Error(err))
		return &fePb.GetEarnedRewardsResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while fetching user and app attributes"),
		}, nil
	}
	rewardsByActorIdRequest := &beRewardsPb.RewardsByActorIdRequest{
		ActorId:     req.GetReq().GetAuth().GetActorId(),
		PageContext: r.getBeRewardPageContext(req.PageContext, r.rewardsFrontendMeta),
	}
	rewardsResponse, err := r.rewardsGeneratorClient.GetRewardsByActorId(ctx, rewardsByActorIdRequest)
	if err != nil || rewardsResponse == nil || !rewardsResponse.Status.IsSuccess() {
		logger.Error(ctx, "GetRewardsByActorId call failed", zap.Error(err), zap.Any("res", rewardsResponse))
		return &fePb.GetEarnedRewardsResponse{
			Status: rpc.StatusInternalWithDebugMsg("GetRewardsByActorId call failed"),
		}, nil
	}
	res := &fePb.GetEarnedRewardsResponse{
		Status:      rpc.StatusOk(),
		PageContext: r.getFePageContextFromBeRewardPageContext(rewardsResponse.PageContext),
	}
	for _, beReward := range rewardsResponse.Rewards {
		// if shouldRewardBeDisplayed returns false, we don't need to show the
		// reward in MyRewards screen.
		if !shouldRewardBeDisplayed(beReward) {
			continue
		}

		feReward, err := r.getFeReward(ctx, req.GetReq().GetAuth().GetActorId(), beReward, userAndAppAttributes, nil)
		if err != nil {
			logger.Error(ctx, "unable to convert be reward to fe reward", zap.String(logger.REWARD_ID, beReward.GetId()), zap.Error(err))
			continue
		}
		// if claimed reward is of no reward type, then it does not need to be displayed in my rewards screen.
		if feReward.GetClaimedReward().GetNoReward() != nil {
			continue
		}
		res.Rewards = append(res.Rewards, feReward)
	}
	return res, nil
}

func (r *RewardService) GetActiveOffers(ctx context.Context, req *fePb.GetActiveOffersRequest) (*fePb.GetActiveOffersResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()
	getOffersRequest := &beCasperPb.GetOffersRequest{
		ActorId:        req.GetReq().GetAuth().GetActorId(),
		PageContext:    r.getBeOfferPageContext(req.PageContext, r.rewardsFrontendMeta.AppFetchOffersPageSize),
		RedemptionMode: r.getBeOfferRedemptionMode(req.GetOfferRedemptionMode()),
	}
	// if redemption mode is not specified, defaulting in to FI_COINS redemption mode.
	// In FI coins catalog rpc call, android client is not sending the redemption mode field but wants to display only FI_COINS offers.
	// The other clients are passing the correct specific redemption mode. So in order to handle this, defaulting to FI_COINS redemption mode.
	// todo (utkarsh) : should we add app version level check for this ?
	if req.GetOfferRedemptionMode() == fePb.OfferRedemptionMode_UNSPECIFIED_REDEMPTION_MODE {
		getOffersRequest.RedemptionMode = beCasperPb.OfferRedemptionMode_FI_COINS
	}

	getOffersResponse, err := r.offerListingClient.GetOffers(ctx, getOffersRequest)
	if rpcErr := epifigrpc.RPCError(getOffersResponse, err); rpcErr != nil {
		if getOffersResponse.GetStatus().IsRecordNotFound() {
			logger.WarnWithCtx(ctx, "no active offers found", zap.String("redemptionMode", getOffersRequest.GetRedemptionMode().String()), zap.String(logger.ACTOR_ID_V2, actorId))
			return &fePb.GetActiveOffersResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "GetOffers call failed", zap.Error(rpcErr), zap.Any("res", getOffersResponse))
		return &fePb.GetActiveOffersResponse{
			Status: rpc.StatusInternalWithDebugMsg("GetOffers call failed"),
		}, nil
	}

	beOffersList := make([]*beCasperPb.Offer, 0, len(getOffersResponse.GetOffers()))
	offerIdToListingMap := getOffersResponse.GetOfferIdToListingMap()

	var offerIdListUnfiltered []string
	for _, beOffer := range beOffersList {
		offerIdListUnfiltered = append(offerIdListUnfiltered, beOffer.GetId())
	}

	getOfferInventoryByOfferIdsResponse, err := r.offerInventoryService.GetOfferInventoryByOfferIds(ctx,
		&beCasperPb.GetOfferInventoryByOfferIdsRequest{
			OfferIds: offerIdListUnfiltered,
		},
	)
	if err != nil || !getOfferInventoryByOfferIdsResponse.Status.IsSuccess() {
		logger.Error(ctx, "GetOfferInventoryByOfferIds call failed", zap.Error(err), zap.Any(logger.RPC_STATUS, getOfferInventoryByOfferIdsResponse.GetStatus()))
		return &fePb.GetActiveOffersResponse{
			Status: rpc.StatusInternalWithDebugMsg("GetOfferInventoryByOfferIds call failed"),
		}, nil
	}

	offerIdToOfferGlobalInventoryMap := getOfferInventoryByOfferIdsResponse.GetOfferIdToOfferInventoryMap()

	offerIdToUserLevelAttemptsRemainingMap := getOffersResponse.GetOfferIdToUserLevelAttemptsRemainingMap()

	// filter out offers which are unsupported
	for _, beOffer := range getOffersResponse.GetOffers() {

		// app version checks are only for FiCoinOffers
		if req.GetOfferRedemptionMode() == fePb.OfferRedemptionMode_FI_COINS {
			userLevelAttemptsRemaining := offerIdToUserLevelAttemptsRemainingMap[beOffer.GetId()]
			globalLevelOfferInventory := offerIdToOfferGlobalInventoryMap[beOffer.GetId()].GetAvailableCount()

			appPlatform := req.GetReq().GetAuth().GetDevice().GetPlatform()
			appVersion := req.GetReq().GetAuth().GetDevice().GetAppVersion()
			// version checks
			shouldDisplayFiCoinOfferOnAppVersion, err1 := r.shouldDisplayFiCoinOfferOnAppVersion(ctx, beOffer, offerIdToListingMap[beOffer.GetId()], userLevelAttemptsRemaining, globalLevelOfferInventory, appPlatform, appVersion)
			// bypassing error as the flow should not break for one corrupted offer
			if err1 != nil {
				logger.Error(ctx, "shouldDisplayFiCoinOfferOnAppVersion call failed", zap.Error(err1), zap.String("offerId: ", beOffer.GetId()))
				continue
			}
			if !shouldDisplayFiCoinOfferOnAppVersion {
				continue
			}
		}

		beOffersList = append(beOffersList, beOffer)
	}

	// get expression function map for display filtering
	displayExpressionFunctionMap := r.offerDisplayEngine.GetExpressionFunctionMap(ctx, req.GetReq().GetAuth().GetActorId(), beOffersList, nil)

	var offerIdList []string
	for _, beOffer := range beOffersList {
		offerIdList = append(offerIdList, beOffer.GetId())
	}

	getOfferInventoryByOfferIdsResponse, err = r.offerInventoryService.GetOfferInventoryByOfferIds(ctx,
		&beCasperPb.GetOfferInventoryByOfferIdsRequest{
			OfferIds: offerIdList,
		},
	)
	if err != nil || !getOfferInventoryByOfferIdsResponse.Status.IsSuccess() {
		logger.Error(ctx, "GetOfferInventoryByOfferIds call failed", zap.Error(err), zap.Any(logger.RPC_STATUS, getOfferInventoryByOfferIdsResponse.GetStatus()))
		return &fePb.GetActiveOffersResponse{
			Status: rpc.StatusInternalWithDebugMsg("GetOfferInventoryByOfferIds call failed"),
		}, nil
	}

	offerIdToOfferGlobalInventoryMap = getOfferInventoryByOfferIdsResponse.GetOfferIdToOfferInventoryMap()

	fiCoinBalance, err := r.getFiCoinsBalanceForActor(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "getFiCoinsBalanceForActor call failed", zap.Error(err))
		return &fePb.GetActiveOffersResponse{
			Status: rpc.StatusInternalWithDebugMsg("getFiCoinsBalanceForActor call failed"),
		}, nil
	}

	feOffersList := make([]*fePb.Offer, 0, len(beOffersList))
	for _, beOffer := range beOffersList {
		// passing `isFullSalaryProgramActiveForActor` as false by default since this RPC is being called by older client versions
		// which don't support salary-account-exclusive offers anyway.
		// passing `isMonthlyMaxCapHit` as false as it's irrelevant for this flow
		feOffer, err := r.getFeOffer(ctx, beOffer, offerIdToListingMap[beOffer.GetId()], displayExpressionFunctionMap, false, req.GetReq().GetAppVersionCode(), req.GetReq().GetAuth().GetDevice().GetPlatform(), tags.RenderLocationFiCoinOffersCatalogCard, offerIdToOfferGlobalInventoryMap, fiCoinBalance, offerIdToUserLevelAttemptsRemainingMap, false)
		if err != nil {
			logger.Error(ctx, "error converting beOffer to feOffer", zap.String(logger.OFFER_ID, beOffer.GetId()), zap.Error(err))
			return &fePb.GetActiveOffersResponse{
				Status: rpc.StatusInternalWithDebugMsg("error converting beOffer to feOffer"),
			}, nil
		}
		if feOffer == nil {
			logger.Debug(ctx, "user is not eligible for the offer", zap.String(logger.OFFER_ID, beOffer.GetId()), zap.Error(err))
			continue
		}
		feOffersList = append(feOffersList, feOffer)
	}

	// todo (utkarsh) : how to support this ordering with pagination ?
	// order feOffers list in particular order to be displayed on APP.
	orderFeOffersForDisplay(feOffersList, beOffersList)

	return &fePb.GetActiveOffersResponse{
		Status:      rpc.StatusOk(),
		PageContext: r.getFePageContextFromBeOfferPageContext(getOffersResponse.PageContext),
		Offers:      feOffersList,
	}, nil
}

// GetOfferDetailsById takes in offer_id and returns details of the offer only if offer is active
// will be called from deeplink DEBIT_CARD_OFFERS_HOME_SCREEN if DebitCardOffersHomeScreenOptions has offer_id field set
func (r *RewardService) GetOfferDetailsById(ctx context.Context, req *fePb.GetOfferDetailsByIdRequest) (*fePb.GetOfferDetailsByIdResponse, error) {
	var (
		appPlatform = req.GetReq().GetAuth().GetDevice().GetPlatform()
		appVersion  = req.GetReq().GetAuth().GetDevice().GetAppVersion()
	)

	// fetch catalog offer using offer id from be
	offerDetailsRes, err := r.offerCatalogClient.GetBulkOfferDetailsByIds(ctx, &beCasperPb.GetBulkOfferDetailsByIdsRequest{OfferIds: []string{req.GetOfferId()}})
	if te := epifigrpc.RPCError(offerDetailsRes, err); te != nil {
		logger.Error(ctx, "error while fetching catalog offer from be", zap.String(logger.OFFER_ID, req.GetOfferId()), zap.Error(te))
		return &fePb.GetOfferDetailsByIdResponse{Status: rpc.StatusInternalWithDebugMsg(te.Error())}, nil
	}
	if len(offerDetailsRes.GetOffers()) != 1 {
		logger.Error(ctx, "invalid response while fetching catalog offer from be", zap.String(logger.OFFER_ID, req.GetOfferId()), zap.Int(logger.LENGTH, len(offerDetailsRes.GetOffers())))
		return &fePb.GetOfferDetailsByIdResponse{
			Status: rpc.StatusRecordNotFound(),
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusRecordNotFound(),
				ErrorView: offerNotFoundErrorView,
			},
		}, nil
	}

	// fetch display active listings of the offer
	offerListingsRes, err := r.offerListingClient.GetDisplayActiveListingsForOffer(ctx, &beCasperPb.GetDisplayActiveListingsForOfferRequest{OfferId: req.GetOfferId()})
	if te := epifigrpc.RPCError(offerListingsRes, err); te != nil {
		if offerListingsRes.GetStatus().IsRecordNotFound() {
			logger.Error(ctx, "no display active listings found for the offer", zap.String(logger.OFFER_ID, req.GetOfferId()))
			return &fePb.GetOfferDetailsByIdResponse{
				Status: rpc.StatusRecordNotFound(),
				RespHeader: &header.ResponseHeader{
					Status:    rpc.StatusRecordNotFound(),
					ErrorView: offerNotFoundErrorView,
				},
			}, nil
		}
		logger.Error(ctx, "error while fetching display active listings of the offer", zap.String(logger.OFFER_ID, req.GetOfferId()), zap.Error(te))
		return &fePb.GetOfferDetailsByIdResponse{Status: rpc.StatusInternalWithDebugMsg(te.Error())}, nil
	}

	latestOfferListing := offerListingsRes.GetOfferListings()[len(offerListingsRes.GetOfferListings())-1]

	// check if user is salary program active
	actorId := req.GetReq().GetAuth().GetActorId()
	isUserFullSalaryProgramActive, err := r.IsUserFullSalaryProgramActive(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error while checking if user is salary program active", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return &fePb.GetOfferDetailsByIdResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	// get display expression function map
	displayExpressionFunctionMap := r.offerDisplayEngine.GetExpressionFunctionMap(ctx, actorId, offerDetailsRes.GetOffers()[:1], nil)

	getOfferInventoryByOfferIdsResponse, err := r.offerInventoryService.GetOfferInventoryByOfferIds(ctx,
		&beCasperPb.GetOfferInventoryByOfferIdsRequest{
			OfferIds: []string{req.GetOfferId()},
		},
	)
	if te := epifigrpc.RPCError(getOfferInventoryByOfferIdsResponse, err); te != nil {
		logger.Error(ctx, "GetOfferInventoryByOfferIds call failed", zap.Error(err), zap.Any(logger.RPC_STATUS, getOfferInventoryByOfferIdsResponse.GetStatus()))
		return &fePb.GetOfferDetailsByIdResponse{Status: rpc.StatusInternalWithDebugMsg(te.Error())}, nil
	}

	fiCoinBalance, err := r.getFiCoinsBalanceForActor(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "getFiCoinsBalanceForActor call failed", zap.Error(err))
		return &fePb.GetOfferDetailsByIdResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	// getting GetOfferInventory details for actor
	getOfferInventoryResponse, err := r.offerInventoryService.GetOfferInventory(
		ctx,
		&beCasperPb.GetOfferInventoryRequest{
			OfferId: req.GetOfferId(),
			ActorId: actorId,
		},
	)
	if te := epifigrpc.RPCError(getOfferInventoryResponse, err); te != nil {
		logger.Error(ctx, "getOfferInventoryResponse call failed", zap.Error(err), zap.Any(logger.RPC_STATUS, getOfferInventoryResponse.GetStatus()))
		return &fePb.GetOfferDetailsByIdResponse{Status: rpc.StatusInternalWithDebugMsg(te.Error())}, nil
	}

	// making map of offerId to available user level inventory
	offerIdToAvailableUserLevelInventoryMap := make(map[string]int32, 1)
	offerIdToAvailableUserLevelInventoryMap[req.GetOfferId()] = getOfferInventoryResponse.GetOfferInventory().GetAvailableCount()

	globalLevelOfferInventory := getOfferInventoryByOfferIdsResponse.GetOfferIdToOfferInventoryMap()[req.GetOfferId()].GetAvailableCount()

	// logic for getting user level attempts remaining, first get the number of times user has
	// purchased the offer from offerInventoryService, then get the max number of redemptions allowed per user,
	// user level attempts remaining = (max number of redemptions allowed per user) - (purchased count for user)
	// not using 'offerIdToAvailableUserLevelInventoryMap' as it sets the user level remaining attempts to availableCount,
	// when availableCount < userLevelAttemptsRemaining, so created a new logic for that as we want to handle the two cases differently
	offerInventoryId := getOfferInventoryResponse.GetOfferInventory().GetId()
	offerId := getOfferInventoryResponse.GetOfferInventory().GetOfferId()
	getPurchaseCountForActorByOfferIdsResponse, err := r.offerInventoryService.GetPurchaseCountForActorByOfferInventoryIds(ctx,
		&beCasperPb.GetPurchaseCountForActorByOfferInventoryIdsRequest{
			OfferInventoryIds: []string{offerInventoryId},
			ActorId:           actorId,
		})
	if rpcErr := epifigrpc.RPCError(getPurchaseCountForActorByOfferIdsResponse, err); rpcErr != nil {
		logger.Error(ctx, "failed GetPurchaseCountForActorByOfferInventoryIds call", zap.Any("offerInventoryIdList", []string{offerInventoryId}), zap.String(logger.ACTOR_ID_V2, actorId), zap.Any(logger.RPC_STATUS, getPurchaseCountForActorByOfferIdsResponse.GetStatus()))
		return &fePb.GetOfferDetailsByIdResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed GetPurchaseCountForActorByOfferInventoryIds call"),
		}, nil
	}
	offerInventoryIdToUserLevelPurchaseCountMap := getPurchaseCountForActorByOfferIdsResponse.GetOfferInventoryIdToUserLevelPurchaseCountMap()
	offerIdToUserLevelAttemptsRemainingMap := make(map[string]int32, 1)
	userLevelPurchaseCountForOfferInventoryId, ok := offerInventoryIdToUserLevelPurchaseCountMap[offerInventoryId]
	purchaseCount := int32(0)
	if ok {
		purchaseCount = userLevelPurchaseCountForOfferInventoryId
	}
	userLevelAttemptsRemaining := math.Max(float64(getOfferInventoryResponse.GetOfferInventory().GetMaxPerUserLimit()-purchaseCount), 0)
	offerIdToUserLevelAttemptsRemainingMap[offerId] = int32(userLevelAttemptsRemaining)

	offerRedemptionMode := offerDetailsRes.GetOffers()[0].GetRedemptionMode()

	switch offerRedemptionMode {
	case beCasperPb.OfferRedemptionMode_FI_COINS:
		shouldDisplayFiCoinOfferOnAppVersion, shouldDisplayCheckErr := r.shouldDisplayFiCoinOfferOnAppVersion(ctx, offerDetailsRes.GetOffers()[0], latestOfferListing, int32(userLevelAttemptsRemaining), globalLevelOfferInventory, appPlatform, appVersion)
		if shouldDisplayCheckErr != nil {
			logger.Error(ctx, "error checking if the Fi-Coins offer should be display on the app", zap.String(logger.OFFER_ID, offerDetailsRes.GetOffers()[0].GetId()), zap.Error(shouldDisplayCheckErr))
			return &fePb.GetOfferDetailsByIdResponse{
				Status: rpc.StatusInternalWithDebugMsg("error checking if the Fi-Coins offer should be display on the app"),
			}, nil
		}
		if !shouldDisplayFiCoinOfferOnAppVersion {
			return &fePb.GetOfferDetailsByIdResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		}
	case beCasperPb.OfferRedemptionMode_FI_CARD:
		// do not need any specific checks for FI_CARD redemption mode as of now.

	case beCasperPb.OfferRedemptionMode_INTERNAL:
		// client shouldn't try to fetch these offers as they are useful for internal usage only.
		logger.WarnWithCtx(ctx, "cannot fetch offer with internal redemption mode", zap.String(logger.OFFER_ID, req.GetOfferId()))
		return &fePb.GetOfferDetailsByIdResponse{
			Status: rpc.StatusPermissionDenied(),
		}, nil

	default:
		logger.Error(ctx, "unhandled offer type in get offer details by id rpc", zap.String(logger.OFFER_ID, req.GetOfferId()))
		return &fePb.GetOfferDetailsByIdResponse{
			Status: rpc.StatusInternalWithDebugMsg("unhandled offer type in get offer details by id rpc"),
		}, nil
	}

	// convert be offer to fe offer
	// ** Note ** - we're passing the value of
	// `isMonthlyMaxCapHit` as false here as we assume that the RPC will always be
	// called for DC/CC offers (that don't have any capping). If we plan to use it
	// for catalog offers then we will need to actually evaluate whether the cap has
	// been hit or not. To do so, a new RPC will be required to fetch and check if
	// monthly redemption cap has been hit for user for that offer.
	feOffer, err := r.getFeOffer(ctx, offerDetailsRes.GetOffers()[0], latestOfferListing, displayExpressionFunctionMap, isUserFullSalaryProgramActive, req.GetReq().GetAppVersionCode(), req.GetReq().GetAuth().GetDevice().GetPlatform(), tags.RenderLocationFiCoinOffersCatalogCard, getOfferInventoryByOfferIdsResponse.GetOfferIdToOfferInventoryMap(), fiCoinBalance, offerIdToUserLevelAttemptsRemainingMap, false)
	if err != nil {
		logger.Error(ctx, "error while converting be offer to fe offer", zap.String(logger.OFFER_ID, req.GetOfferId()), zap.Error(err))
		return &fePb.GetOfferDetailsByIdResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}
	if feOffer == nil {
		logger.Debug(ctx, "user is not eligible for the offer", zap.String(logger.OFFER_ID, offerDetailsRes.GetOffers()[0].GetId()), zap.Error(err))
		return &fePb.GetOfferDetailsByIdResponse{Status: rpc.StatusRecordNotFound()}, nil
	}

	return &fePb.GetOfferDetailsByIdResponse{
		Offer:  feOffer,
		Status: rpc.StatusOk(),
	}, nil
}

// order feOffers list in particular fashion to be displayed on APP.
// Current ordering logic -> offers should be ordered by display rank (in ascending order), then creation date (in descending order) for same display ranked offers.
func orderFeOffersForDisplay(feOffers []*fePb.Offer, beOffers []*beCasperPb.Offer) {
	// create offer id to be offer map
	offerIdToBeOfferMap := make(map[string]*beCasperPb.Offer, len(beOffers))
	for _, beOffer := range beOffers {
		offerIdToBeOfferMap[beOffer.GetId()] = beOffer
	}

	// offers should be ordered by display rank (in ascending order), then creation date (in descending order)
	sort.Slice(feOffers, func(i, j int) bool {
		beOfferI, beOfferJ := offerIdToBeOfferMap[feOffers[i].GetId()], offerIdToBeOfferMap[feOffers[j].GetId()]
		offerIDisplayRank, offerJDisplayRank := beOfferI.GetAdditionalDetails().GetOfferDisplayRank(), beOfferJ.GetAdditionalDetails().GetOfferDisplayRank()
		// order by display rank in ascending order
		if offerIDisplayRank != offerJDisplayRank {
			return offerIDisplayRank < offerJDisplayRank
		}
		// for equal display rank order by creation date in descending order
		return beOfferI.GetCreatedAt().AsTime().After(beOfferJ.GetCreatedAt().AsTime())
	})
}

func (r *RewardService) GetRedeemedOffersCatalogLayout(ctx context.Context, req *fePb.GetRedeemedOffersCatalogLayoutRequest) (*fePb.GetRedeemedOffersCatalogLayoutResponse, error) {
	var (
		filtersWidget   *fePb.FiltersWidget
		zeroStateWidget *fePb.ZeroStateWidget
	)

	if apputils.IsFeatureEnabledFromCtxDynamic(ctx, r.dyconf.RewardsFrontendMeta().CollectedOffersPageFiltersFeatureConfig()) {
		var getErr error
		filtersWidget, getErr = r.getFiltersWidgetForRedeemedOffersCatalogLayout(ctx)
		if getErr != nil {
			return &fePb.GetRedeemedOffersCatalogLayoutResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusInternalWithDebugMsg("failed to get filters widget"),
				},
			}, nil
		}
		// zero state support is added with filters widget
		zeroStateWidget = &fePb.ZeroStateWidget{
			VisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/rewards/magnify-glass-cross.png", 88, 88),
			Title:         commontypes.GetTextFromStringFontColourFontStyle("No collected offers", colorContentOnLightHighEmphasis, commontypes.FontStyle_HEADLINE_L),
			Subtitle: ui.NewITC().
				WithTexts(&commontypes.Text{
					Alignment: commontypes.Text_ALIGNMENT_CENTER,
					FontColor: "#6A6D70",
					DisplayValue: &commontypes.Text_Html{
						Html: accrualPkg.ReplaceCoinWithPointIfApplicable("Start using your Fi-Coins to collect<br>offers. Check out <span style='color: #00B899;'>all the offers.</span>"),
					},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_BODY_XS,
					},
				}).
				WithDeeplink(&deeplink.Deeplink{
					Screen: deeplinkPb.Screen_OFFERS_LANDING_SCREEN,
				}),
		}
	}

	return &fePb.GetRedeemedOffersCatalogLayoutResponse{
		Title:           commontypes.GetTextFromStringFontColourFontStyle("Collected Offers", colorContentOnLightHighEmphasis, commontypes.FontStyle_HEADLINE_M),
		FiltersWidget:   filtersWidget,
		ZeroStateWidget: zeroStateWidget,
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
	}, nil
}

// nolint:funlen
func (r *RewardService) getFiltersWidgetForRedeemedOffersCatalogLayout(ctx context.Context) (*fePb.FiltersWidget, error) {
	const maxAllowedPromotedFiltersForTopBar = 5

	filterTags := []beCasperPb.TagName{
		beCasperPb.TagName_REWARD_TYPE_PHYSICAL_MERCHANDISE,
		beCasperPb.TagName_FI_STORE,
		beCasperPb.TagName_REWARD_TYPE_EGV,
		beCasperPb.TagName_CREDIT_CARD_EXCLUSIVE,
		beCasperPb.TagName_SALARY_EXCLUSIVE_V2,
	}

	var widgets []*fePb.FilterWidget

	// generating list of all filters
	allTagDetails, orderedAllTagNames, err := r.tagsManager.GetTagsDetailsOrderedByPriority(ctx, filterTags, tags.RenderLocationCollectedOffersAllFilters)
	if err != nil {
		logger.Error(ctx, "failed to get tag details", zap.Any("tagsList", filterTags), zap.String("renderLocation", string(tags.RenderLocationCollectedOffersAllFilters)), zap.Error(err))
		return nil, fmt.Errorf("failed to get tag details")
	}

	var allFilterTags []*fePb.CatalogTagFilter
	for i, feTagDetails := range allTagDetails {
		activeFilterCta, getErr := r.tagsManager.GetActiveCatalogTagFilterDetailsV2(orderedAllTagNames[i], tags.RenderLocationCollectedOffersAllFilters)
		if getErr != nil {
			logger.Error(ctx, "failed to get active tag details", zap.Any("tagName", orderedAllTagNames[i]), zap.Error(getErr))
			continue
		}
		allFilterTags = append(allFilterTags, &fePb.CatalogTagFilter{
			TagName:           orderedAllTagNames[i].String(),
			InactiveFilterCta: feTagDetails,
			ActiveFilterCta:   activeFilterCta,
		})
	}

	if r.dyconf.RewardsFrontendMeta().IsAllFiltersCtaEnabledOnCollectedOffersPage() && (cfg.IsNonProdEnv(r.dyconf.Application().Environment) || len(allFilterTags) > maxAllowedPromotedFiltersForTopBar) {
		widgets = append(
			widgets,
			// add "All Filters" button to filters bar
			&fePb.FilterWidget{
				Widget: &fePb.FilterWidget_AllFiltersWidget{
					AllFiltersWidget: &fePb.AllFiltersWidget{
						Cta: &ui.IconTextComponent{
							Texts: []*commontypes.Text{
								commontypes.GetTextFromStringFontColourFontStyle("All filters", "#646464", commontypes.FontStyle_SUBTITLE_S),
							},
							LeftImgTxtPadding:   4,
							RightImgTxtPadding:  4,
							LeftVisualElement:   commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/rewards/catalog_filters_all_filters_icon.png", 16, 16),
							RightVisualElement:  commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/rewards/filter_widget_dropdown_icon.png", 16, 16),
							ContainerProperties: r.getFilterWidgetContainerProperties(colorFiSnow, "", 0),
						},
						TagFilters: allFilterTags,
					},
				},
			},
		)
	}

	// generating list of prioritised filters
	priorityTagsDetails, orderedPriorityTagNames, err := r.tagsManager.GetTagsDetailsOrderedByPriority(ctx, filterTags, tags.RenderLocationCollectedOffersFiltersList)
	if err != nil {
		logger.Error(ctx, "failed to get priority tag details", zap.Any("tagsList", filterTags), zap.String("renderLocation", string(tags.RenderLocationCollectedOffersFiltersList)), zap.Error(err))
		return nil, fmt.Errorf("failed to get priority tag details")
	}

	maxPromotedFiltersForTopBar := maxAllowedPromotedFiltersForTopBar
	if len(orderedPriorityTagNames) < maxPromotedFiltersForTopBar {
		maxPromotedFiltersForTopBar = len(orderedPriorityTagNames)
	}
	for i, priorityTagDetail := range priorityTagsDetails[:maxPromotedFiltersForTopBar] {
		activeFilterCta, getErr := r.tagsManager.GetActiveCatalogTagFilterDetailsV2(orderedPriorityTagNames[i], tags.RenderLocationCollectedOffersFiltersList)
		if getErr != nil {
			logger.Error(ctx, "failed to get active tag details", zap.Any("tagName", orderedPriorityTagNames[i]), zap.Error(getErr))
			continue
		}
		widgets = append(widgets, &fePb.FilterWidget{
			Widget: &fePb.FilterWidget_TagFilterWidget{
				TagFilterWidget: &fePb.CatalogTagFilter{
					TagName:           orderedPriorityTagNames[i].String(),
					InactiveFilterCta: priorityTagDetail,
					ActiveFilterCta:   activeFilterCta,
				},
			},
		})
	}

	widgets = append(widgets, &fePb.FilterWidget{
		Widget: &fePb.FilterWidget_OptionsFilterWidget{
			OptionsFilterWidget: &fePb.OptionsFilterWidget{
				Cta: &ui.IconTextComponent{
					Texts:               []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Status", "#6A6D70", commontypes.FontStyle_SUBTITLE_S)},
					RightVisualElement:  commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/rewards/filter_widget_dropdown_icon.png", 16, 16),
					ContainerProperties: r.getFilterWidgetContainerProperties(colorFiSnow, "", 0),
				},
				OptionsDialogTitle: commontypes.GetTextFromStringFontColourFontStyle("Filter by Status", colorContentOnLightHighEmphasis, commontypes.FontStyle_SUBTITLE_S),
				SortOptions:        r.getRedeemedOffersStatusFilterOptions(),
			},
		},
	})

	return &fePb.FiltersWidget{
		ClearFiltersCta: &ui.IconTextComponent{
			LeftVisualElement:   commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/rewards/clear_filters_icon.png", 16, 16),
			ContainerProperties: r.getFilterWidgetContainerProperties("#D6D9DD", "", 0),
		},
		Widgets: widgets,
	}, nil
}

func (r *RewardService) getRedeemedOffersStatusFilterOptions() []*fePb.OptionsFilterWidget_FilterOption {
	return []*fePb.OptionsFilterWidget_FilterOption{
		{
			OptionId: fePb.RedeemedOfferStatus_REDEEMED_OFFER_STATUS_SUCCESSFUL.String(),
			FilterOptionCta: &ui.IconTextComponent{
				Texts: []*commontypes.Text{
					{
						DisplayValue: &commontypes.Text_PlainString{PlainString: "Successful"},
						FontColor:    colorContentOnLightHighEmphasis,
						BgColor:      "#F7F9FA",
					},
				},
			},
			AppliedFilterOptionCta: &ui.IconTextComponent{
				Texts:               []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Status: Successful", "#6A6D70", commontypes.FontStyle_SUBTITLE_S)},
				ContainerProperties: r.getFilterWidgetContainerProperties(colorFiSnow, colorFiGreen, 2),
				RightVisualElement:  commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/rewards/filter_widget_dropdown_icon.png", 16, 16),
			},
		},
		{
			OptionId: fePb.RedeemedOfferStatus_REDEEMED_OFFER_STATUS_CANCELLED.String(),
			FilterOptionCta: &ui.IconTextComponent{
				Texts: []*commontypes.Text{
					{
						DisplayValue: &commontypes.Text_PlainString{PlainString: "Cancelled"},
						FontColor:    colorContentOnLightHighEmphasis,
						BgColor:      "#F7F9FA",
					},
				},
			},
			AppliedFilterOptionCta: &ui.IconTextComponent{
				Texts:               []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Status: Cancelled", "#6A6D70", commontypes.FontStyle_SUBTITLE_S)},
				ContainerProperties: r.getFilterWidgetContainerProperties(colorFiSnow, colorFiGreen, 2),
				RightVisualElement:  commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/rewards/filter_widget_dropdown_icon.png", 16, 16),
			},
		},
		{
			OptionId: fePb.RedeemedOfferStatus_REDEEMED_OFFER_STATUS_EXPIRED.String(),
			FilterOptionCta: &ui.IconTextComponent{
				Texts: []*commontypes.Text{
					{
						DisplayValue: &commontypes.Text_PlainString{PlainString: "Expired"},
						FontColor:    colorContentOnLightHighEmphasis,
						BgColor:      "#F7F9FA",
					},
				},
			},
			AppliedFilterOptionCta: &ui.IconTextComponent{
				Texts:               []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Status: Expired", "#6A6D70", commontypes.FontStyle_SUBTITLE_S)},
				ContainerProperties: r.getFilterWidgetContainerProperties(colorFiSnow, colorFiGreen, 2),
				RightVisualElement:  commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/rewards/filter_widget_dropdown_icon.png", 16, 16),
			},
		},
	}
}

func (r *RewardService) getFilterWidgetContainerProperties(bgColor, borderColor string, borderWidth int32) *ui.IconTextComponent_ContainerProperties {
	return &ui.IconTextComponent_ContainerProperties{
		BgColor:       bgColor,
		CornerRadius:  19,
		LeftPadding:   12,
		RightPadding:  12,
		TopPadding:    12,
		BottomPadding: 12,
		BorderColor:   borderColor,
		BorderWidth:   borderWidth,
	}
}

// GetRedeemedOffersV1 RPC will replace GetActiveRedeemedOffers. It sends a combined list of exchanger orders and active redeemed offers (and not expired ones).
// nolint: funlen
func (r *RewardService) GetRedeemedOffersV1(ctx context.Context, req *fePb.GetRedeemedOffersV1Request) (*fePb.GetRedeemedOffersV1Response, error) {
	var (
		feRedeemedOffers            []*fePb.RedeemedOffer
		feExchangerOrders           []*fePb.ExchangerOrder
		feExternalVendorRedemptions []*fePb.CollectedOffer
		beOrTags                    []beCasperPb.TagName
		feStatus                    fePb.RedeemedOfferStatus

		bePageCtxRespList = make([]*rpc.PageContextResponse, 3)

		appVersion            = req.GetReq().GetAppVersionCode()
		appPlatform           = req.GetReq().GetAuth().GetDevice().GetPlatform()
		redemptionStateFilter = append(r.getOfferRedemptionInProgressStates(), beRedemptionPb.OfferRedemptionState_VENDOR_REDEMPTION_PENDING_UPDATE, beRedemptionPb.OfferRedemptionState_OFFER_REDEMPTION_SUCCESSFUL)
		actorId               = req.GetReq().GetAuth().GetActorId()
	)

	logger.Debug(ctx, "got GetRedeemedOffersV1 rpc request", zap.Any("filters", req.GetFilters()), zap.Any("tags", req.GetFilters().GetOrTags()), zap.Int(logger.LENGTH, len(req.GetFilters().GetOrTags())))

	for _, tagStr := range req.GetFilters().GetOrTags() {
		tag := beCasperPb.TagName(beCasperPb.TagName_value[tagStr])
		if tag == beCasperPb.TagName_UNSPECIFIED_TAG_NAME {
			logger.Error(ctx, "invalid tag name received", zap.String("tag", tagStr))
			return &fePb.GetRedeemedOffersV1Response{Status: rpc.StatusInvalidArgument()}, nil
		}
		beOrTags = append(beOrTags, tag)
	}

	if req.GetFilters().GetStatus() != "" {
		feStatus = fePb.RedeemedOfferStatus(fePb.RedeemedOfferStatus_value[req.GetFilters().GetStatus()])
		if feStatus == fePb.RedeemedOfferStatus_REDEEMED_OFFER_STATUS_UNSPECIFIED {
			logger.Error(ctx, "invalid status received", zap.String("status", req.GetFilters().GetStatus()))
			return &fePb.GetRedeemedOffersV1Response{Status: rpc.StatusInvalidArgument()}, nil
		}
	}

	fePageCtxReq := r.getFePageCtxReqFromRpcPageCtxReq(req.GetPageContext())

	pageSize := int(r.rewardsFrontendMeta.AppFetchRewardsPageSize)
	// ignoring page size in case of IOS as there's a bug because of which they send empty page context every time, leading to infinite loop.
	// we'll fetch 50 redeemed offers for actor so that for most actors they may not call the BE RPC again as the pageCtxRes will be empty.
	// todo(growth-infra): remove this so that regular pagination may start working for IOS as well when the fixed IOS app has been adopted completely
	if appPlatform == commontypes.Platform_IOS && appVersion < r.dyconf.RewardsFrontendMeta().MinIosVersionWithRedeemedOffersScreenPaginationFix() {
		pageSize = 50
	}
	bePageCtxReqs, err := splitFePageCtxReqToBePageCtxReqs(fePageCtxReq, pageSize, 3)
	if err != nil {
		logger.Error(ctx, "error splitting concatenated page tokens", zap.Any("pageCtxReq", req.GetPageContext()), zap.Error(err))
		return &fePb.GetRedeemedOffersV1Response{Status: rpc.StatusInternalWithDebugMsg("error splitting concatenated page tokens")}, nil
	}
	errGrp, gctx := errgroup.WithContext(ctx)

	// first page token in bePageCtxReqs list is for redeemed-offers pagination
	redeemedOffersPageCtxReq := bePageCtxReqs[0]
	if redeemedOffersPageCtxReq != nil && r.shouldFetchRedeemedOffersForCollectedOffersPage(beOrTags, feStatus) {
		errGrp.Go(func() error {
			var expiryStatus beRedemptionPb.GetRedeemedOffersForActorRequest_Filters_ExpiryStatus
			switch feStatus {
			case fePb.RedeemedOfferStatus_REDEEMED_OFFER_STATUS_SUCCESSFUL:
				expiryStatus = beRedemptionPb.GetRedeemedOffersForActorRequest_Filters_NOT_EXPIRED
			case fePb.RedeemedOfferStatus_REDEEMED_OFFER_STATUS_EXPIRED:
				expiryStatus = beRedemptionPb.GetRedeemedOffersForActorRequest_Filters_EXPIRED
			default:
				// do nothing
			}

			orTags := beOrTags
			orTags = lo.Filter(orTags, func(item beCasperPb.TagName, index int) bool {
				// removing fi-store tag from orTags as it's not used for filtering redeemed offers
				return item != beCasperPb.TagName_FI_STORE
			})
			orders, pageCtxResp, err2 := r.getRedeemedOffersForActor(gctx, actorId, redeemedOffersPageCtxReq, redemptionStateFilter, expiryStatus, appVersion, appPlatform, orTags)
			if err2 != nil {
				return fmt.Errorf("error getting redeemed offers for actor: %w", err2)
			}

			feRedeemedOffers = append(feRedeemedOffers, orders...)
			bePageCtxRespList[0] = pageCtxResp

			return nil
		})
	} else {
		// since next page is not there setting HasAfter as false is the pageTokenResp for rewards
		bePageCtxRespList[0] = &rpc.PageContextResponse{
			HasAfter: false,
		}
	}

	// second page token in the bePageCtxReqs list is for exchanger-orders pagination
	exchangerOrdersPageCtxReq := bePageCtxReqs[1]
	if exchangerOrdersPageCtxReq != nil && r.shouldFetchExchangerOfferOrdersForCollectedOffersPage(beOrTags, feStatus) {
		errGrp.Go(func() error {
			var expiryStatus beExchangerPb.RewardExpiryStatus
			if feStatus == fePb.RedeemedOfferStatus_REDEEMED_OFFER_STATUS_EXPIRED {
				expiryStatus = beExchangerPb.RewardExpiryStatus_REWARD_EXPIRY_STATUS_EXPIRED
			}
			switch feStatus {
			case fePb.RedeemedOfferStatus_REDEEMED_OFFER_STATUS_SUCCESSFUL:
				expiryStatus = beExchangerPb.RewardExpiryStatus_REWARD_EXPIRY_STATUS_NOT_EXPIRED
			case fePb.RedeemedOfferStatus_REDEEMED_OFFER_STATUS_EXPIRED:
				expiryStatus = beExchangerPb.RewardExpiryStatus_REWARD_EXPIRY_STATUS_EXPIRED
			default:
				// do nothing
			}

			rewardTypeFilters, orOfferTagFilters := r.getRewardTypesAndOfferTagFiltersFromTags(beOrTags)

			orderStatesFilter := []beExchangerPb.ExchangerOfferOrderState{
				beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_OPTION_CHOSEN,
				beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_FULFILLMENT_INITIATED,
				beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_USER_INPUT_NEEDED_FOR_FULFILLMENT,
				beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_FULFILLED,
			}

			orFilters := &beExchangerPb.GetExchangerOfferOrdersForActorRequest_OrFilters{
				RewardTypes: rewardTypeFilters,
				OrOfferTags: orOfferTagFilters,
			}
			orders, bePageCtxResp, err2 := r.getExchangerOrdersForActor(gctx, actorId, exchangerOrdersPageCtxReq, nil, orderStatesFilter, true, tags.RenderLocationCollectedOffersScreen, expiryStatus, orFilters)
			if err2 != nil {
				return fmt.Errorf("error fetching exchanger orders for actor: %w", err2)
			}

			feExchangerOrders = append(feExchangerOrders, orders...)
			bePageCtxRespList[1] = bePageCtxResp

			return nil
		})
	} else {
		// since next page is not there setting HasAfter as false is the pageTokenResp for exchanger-orders
		bePageCtxRespList[1] = &rpc.PageContextResponse{
			HasAfter: false,
		}
	}

	// third page token in the bePageCtxReqs list is for external vendor redemptions (fi-store) pagination
	externalVendorRedemptionsPageCtxReq := bePageCtxReqs[2]
	if externalVendorRedemptionsPageCtxReq != nil && r.shouldFetchExternalVendorRedemptionsForCollectedOffersPage(beOrTags, feStatus) {
		errGrp.Go(func() error {

			orderStatus := r.getExternalVendorRedemptionOrderStatusFromFeStatus(feStatus)
			redemptions, bePageCtxResp, err2 := r.getExternalVendorRedemptionsForActor(gctx, actorId, orderStatus, externalVendorRedemptionsPageCtxReq)
			if err2 != nil {
				return fmt.Errorf("error fetching external vendor redemptions for actor: %w", err2)
			}

			feExternalVendorRedemptions = append(feExternalVendorRedemptions, redemptions...)
			bePageCtxRespList[2] = bePageCtxResp

			return nil
		})
	} else {
		// since next page is not there setting HasAfter as false is the pageTokenResp for external vendor redemptions (fi-store) pagination
		bePageCtxRespList[2] = &rpc.PageContextResponse{
			HasAfter: false,
		}
	}

	if grpErr := errGrp.Wait(); grpErr != nil {
		logger.Error(ctx, "error fetching collected offers", zap.Error(err))
		return &fePb.GetRedeemedOffersV1Response{Status: rpc.StatusInternalWithDebugMsg("error fetching collected offers")}, nil
	}

	// generate the wrapper offer using fe offers
	orders := r.mergeAllCollectedOffers(feRedeemedOffers, feExchangerOrders, feExternalVendorRedemptions)

	return &fePb.GetRedeemedOffersV1Response{
		Status:      rpc.StatusOk(),
		Orders:      orders,
		PageContext: r.getRpcPageContextRespFromFePageContextResp(mergeBePageCtxRespListToFePageCtxResp(bePageCtxRespList)),
	}, nil
}

func (r *RewardService) getRewardTypesAndOfferTagFiltersFromTags(orTagFilters []beCasperPb.TagName) ([]beExchangerPb.RewardType, []beCasperPb.TagName) {
	if len(orTagFilters) > 0 {
		var rewardTypes []beExchangerPb.RewardType
		var finalOrTagFilters []beCasperPb.TagName
		for _, tag := range orTagFilters {
			switch tag {
			case beCasperPb.TagName_REWARD_TYPE_PHYSICAL_MERCHANDISE:
				rewardTypes = append(rewardTypes, beExchangerPb.RewardType_REWARD_TYPE_PHYSICAL_MERCHANDISE)
			case beCasperPb.TagName_REWARD_TYPE_EGV:
				rewardTypes = append(rewardTypes, beExchangerPb.RewardType_REWARD_TYPE_EGV)
			case beCasperPb.TagName_FI_STORE:
				// ignore this tag as it is not used for filtering exchanger orders
			default:
				finalOrTagFilters = append(finalOrTagFilters, tag)
			}
		}

		return rewardTypes, finalOrTagFilters
	}

	return []beExchangerPb.RewardType{
		beExchangerPb.RewardType_REWARD_TYPE_EGV,
		beExchangerPb.RewardType_REWARD_TYPE_PHYSICAL_MERCHANDISE,
	}, nil
}

func (r *RewardService) shouldFetchRedeemedOffersForCollectedOffersPage(orTags []beCasperPb.TagName, status fePb.RedeemedOfferStatus) bool {
	if status == fePb.RedeemedOfferStatus_REDEEMED_OFFER_STATUS_CANCELLED {
		return false
	}

	if len(orTags) == 1 && orTags[0] == beCasperPb.TagName_FI_STORE {
		return false
	}

	return true
}

func (r *RewardService) shouldFetchExchangerOfferOrdersForCollectedOffersPage(orTags []beCasperPb.TagName, status fePb.RedeemedOfferStatus) bool {
	if status == fePb.RedeemedOfferStatus_REDEEMED_OFFER_STATUS_CANCELLED {
		return false
	}

	if len(orTags) == 1 && orTags[0] == beCasperPb.TagName_FI_STORE {
		return false
	}

	return true
}

func (r *RewardService) shouldFetchExternalVendorRedemptionsForCollectedOffersPage(orTags []beCasperPb.TagName, status fePb.RedeemedOfferStatus) bool {
	if status == fePb.RedeemedOfferStatus_REDEEMED_OFFER_STATUS_EXPIRED {
		return false
	}

	if len(orTags) > 0 && !lo.Contains(orTags, beCasperPb.TagName_FI_STORE) {
		return false
	}

	return true
}

// merges redeemed offers, exchanger orders & external vendor redemptions and sorts the list by CreatedAt timestamp
func (r *RewardService) mergeAllCollectedOffers(redeemedOffers []*fePb.RedeemedOffer, exchangerOrders []*fePb.ExchangerOrder, collectedOffers []*fePb.CollectedOffer) []*fePb.OrderV1 {
	allOrders := make([]*fePb.OrderV1, 0, len(redeemedOffers)+len(exchangerOrders)+len(collectedOffers))
	for _, redeemedOffer := range redeemedOffers {
		order := &fePb.OrderV1{OrderData: &fePb.OrderV1_RedeemedOffer{RedeemedOffer: redeemedOffer}}
		allOrders = append(allOrders, order)
	}
	for _, exchangerOrder := range exchangerOrders {
		order := &fePb.OrderV1{OrderData: &fePb.OrderV1_ExchangerOrder{ExchangerOrder: exchangerOrder}}
		allOrders = append(allOrders, order)
	}
	for _, collectedOffer := range collectedOffers {
		order := &fePb.OrderV1{OrderData: &fePb.OrderV1_CollectedOffer{CollectedOffer: collectedOffer}}
		allOrders = append(allOrders, order)
	}
	sort.Slice(allOrders, func(i, j int) bool {
		timeI := r.extractUnixTimeFromOrder(allOrders[i])
		timeJ := r.extractUnixTimeFromOrder(allOrders[j])
		return timeI.AsTime().After(timeJ.AsTime())
	})
	return allOrders
}

func (r *RewardService) extractUnixTimeFromOrder(order *fePb.OrderV1) *timestamppb.Timestamp {
	switch order.GetOrderData().(type) {
	case *fePb.OrderV1_ExchangerOrder:
		return order.GetExchangerOrder().GetCreatedAt()
	case *fePb.OrderV1_RedeemedOffer:
		return order.GetRedeemedOffer().GetCreatedAt()
	case *fePb.OrderV1_CollectedOffer:
		return order.GetCollectedOffer().GetCardRow().GetCreatedAt()
	default:
		// should never reach here as only the above two types are possible
		logger.Warn("unsupported order type encountered during merging of redeemed offers and exchanger orders and collected offers list")
		return nil
	}
}

// returns all offers that have been successfully redeemed or whose redemption is still in_progress.
// It does not returns offers that were successfully redeemed but got expired.
func (r *RewardService) GetActiveRedeemedOffers(ctx context.Context, req *fePb.GetActiveRedeemedOffersRequest) (*fePb.GetActiveRedeemedOffersResponse, error) {
	redemptionStateFilter := append(r.getOfferRedemptionInProgressStates(), beRedemptionPb.OfferRedemptionState_VENDOR_REDEMPTION_PENDING_UPDATE, beRedemptionPb.OfferRedemptionState_OFFER_REDEMPTION_SUCCESSFUL)
	actorId := req.GetReq().GetAuth().GetActorId()
	rpcPageContextRequest := r.getBeRpcPageContext(req.PageContext, r.rewardsFrontendMeta.AppFetchRedeemedOffersPageSize)
	feRedeemedOffers, bePageContextResponse, err := r.getRedeemedOffersForActor(ctx, actorId, rpcPageContextRequest, redemptionStateFilter, beRedemptionPb.GetRedeemedOffersForActorRequest_Filters_NOT_EXPIRED, req.GetReq().GetAppVersionCode(), req.GetReq().GetAuth().GetDevice().GetPlatform(), nil)
	if err != nil {
		logger.Error(ctx, "error occurred while trying to fetch redeemed offers for actor", zap.String(logger.ACTOR_ID, actorId), zap.Error(err))
		return &fePb.GetActiveRedeemedOffersResponse{Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error occurred while trying to fetch redeemed offers for actor. err - %v", err.Error()))}, nil
	}

	return &fePb.GetActiveRedeemedOffersResponse{
		Status:         rpc.StatusOk(),
		RedeemedOffers: feRedeemedOffers,
		PageContext:    r.getFePageContextFromBeRpcPageContext(bePageContextResponse),
	}, nil
}

func (r *RewardService) getRedeemedOffersForActor(ctx context.Context, actorId string, pageContextRequest *rpc.PageContextRequest, redemptionStateFilter []beRedemptionPb.OfferRedemptionState, expiryStatus beRedemptionPb.GetRedeemedOffersForActorRequest_Filters_ExpiryStatus, appVersion uint32,
	appPlatform commontypes.Platform, orTags []beCasperPb.TagName) ([]*fePb.RedeemedOffer, *rpc.PageContextResponse, error) {
	activeOffersRequest := &beRedemptionPb.GetRedeemedOffersForActorRequest{
		ActorId: actorId,
		Filters: &beRedemptionPb.GetRedeemedOffersForActorRequest_Filters{
			RedemptionStates: redemptionStateFilter,
			ExpiryStatus:     expiryStatus,
			OrOfferTags:      orTags,
		},
		PageContext: pageContextRequest,
	}
	activeRedeemedOffersRes, err := r.offerRedemptionClient.GetRedeemedOffersForActor(ctx, activeOffersRequest)
	if err != nil || !activeRedeemedOffersRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "get active redeemed offers call failed", zap.Any("response", activeRedeemedOffersRes), zap.Error(err))
		return nil, nil, fmt.Errorf("get active redeemed offers call failed. err - %w", err)
	}
	activeRedeemedOffersList := activeRedeemedOffersRes.GetRedeemedOffers()

	// decrypt redeemed offers fetched from backend
	if len(activeRedeemedOffersList) > 0 {
		decryptedRedeemedOffersRes, err := r.offerRedemptionClient.DecryptRedeemedOffersDetails(ctx, &beRedemptionPb.DecryptRedeemedOffersDetailsRequest{RedeemedOffers: activeRedeemedOffersList})
		if err != nil || !decryptedRedeemedOffersRes.GetStatus().IsSuccess() {
			logger.Error(ctx, "gofferRedemptionClient.DecryptRedeemedOffersDetails call failed", zap.Any("response", decryptedRedeemedOffersRes.GetStatus()), zap.Error(err))
			return nil, nil, fmt.Errorf("error decryption redeemed offer details. err - %w", err)
		}
		activeRedeemedOffersList = decryptedRedeemedOffersRes.GetRedeemedOffers()
	}

	// get offer ids from active redeemed offers
	var offerIds []string
	for _, redeemedOffer := range activeRedeemedOffersList {
		offerIds = append(offerIds, redeemedOffer.GetOfferId())
	}

	// create offerId to offer map
	// we don't need updated vendor offer details as offer is already redeemed
	offerIdToOfferMap, err := r.getOfferIdToOfferMap(ctx, offerIds, false)
	if err != nil {
		logger.Error(ctx, "error getting offer id to offers map", zap.Error(err))
		return nil, nil, fmt.Errorf("error getting offer id to offers map. err - %w", err)
	}

	// convert backend redeemed offers to fe redeemed offers using be redeemed offer and offer details
	var feRedeemedOffers []*fePb.RedeemedOffer
	for _, beRedeemedOffer := range activeRedeemedOffersList {
		beOffer := offerIdToOfferMap[beRedeemedOffer.GetOfferId()]
		if !r.shouldDisplayRedeemedOfferOnAppVersion(ctx, appPlatform, appVersion, beRedeemedOffer, beOffer) {
			continue
		}

		// todo(rohan): do we need to pass salary program status from here?
		feRedeemedOffer, err := r.getFeRedeemedOffer(ctx, beRedeemedOffer, beOffer, appVersion, appPlatform)
		if err != nil {
			logger.Error(ctx, "error converting beRedeemedOffer to feRedeemedOffer", zap.String(logger.REDEEMED_OFFER_ID, beRedeemedOffer.GetId()), zap.Error(err))
			return nil, nil, fmt.Errorf("error converting beRedeemedOffer to feRedeemedOffer. err - %w", err)
		}
		feRedeemedOffers = append(feRedeemedOffers, feRedeemedOffer)
	}
	return feRedeemedOffers, activeRedeemedOffersRes.GetPageContext(), nil
}

// fetches redeeemed offers for an actor that have expired.
func (r *RewardService) GetExpiredRedeemedOffers(ctx context.Context, req *fePb.GetExpiredRedeemedOffersRequest) (*fePb.GetExpiredRedeemedOffersResponse, error) {
	var (
		appPlatform = req.GetReq().GetAuth().GetDevice().GetPlatform()
		appVersion  = req.GetReq().GetAppVersionCode()
	)

	// get expired redeemed offers for actor
	expiredOffersRequest := &beRedemptionPb.GetRedeemedOffersForActorRequest{
		ActorId: req.GetReq().GetAuth().GetActorId(),
		Filters: &beRedemptionPb.GetRedeemedOffersForActorRequest_Filters{
			RedemptionState: beRedemptionPb.OfferRedemptionState_OFFER_REDEMPTION_SUCCESSFUL,
			ExpiryStatus:    beRedemptionPb.GetRedeemedOffersForActorRequest_Filters_EXPIRED,
		},
		PageContext: r.getBeRpcPageContext(req.PageContext, r.rewardsFrontendMeta.AppFetchRedeemedOffersPageSize),
	}
	expiredRedeemedOffersRes, err := r.offerRedemptionClient.GetRedeemedOffersForActor(ctx, expiredOffersRequest)
	if err != nil || !expiredRedeemedOffersRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "get expired redeemed offers call failed", zap.Any("response", expiredRedeemedOffersRes), zap.Error(err))
		return &fePb.GetExpiredRedeemedOffersResponse{Status: rpc.StatusInternalWithDebugMsg("get expired redeemed offers call failed")}, nil
	}
	expiredRedeemedOffersList := expiredRedeemedOffersRes.GetRedeemedOffers()

	// decrypt redeemed offers fetched from backend
	if len(expiredRedeemedOffersList) > 0 {
		decryptedRedeemedOffersRes, err := r.offerRedemptionClient.DecryptRedeemedOffersDetails(ctx, &beRedemptionPb.DecryptRedeemedOffersDetailsRequest{RedeemedOffers: expiredRedeemedOffersList})
		if err != nil || !decryptedRedeemedOffersRes.GetStatus().IsSuccess() {
			logger.Error(ctx, "offerRedemptionClient.DecryptRedeemedOffersDetails call failed", zap.Any("response", decryptedRedeemedOffersRes.GetStatus()), zap.Error(err))
			return &fePb.GetExpiredRedeemedOffersResponse{Status: rpc.StatusInternalWithDebugMsg("error decryption redeemed offer details ")}, nil
		}
		expiredRedeemedOffersList = decryptedRedeemedOffersRes.GetRedeemedOffers()
	}

	// get offer ids from expired redeemed offers
	var offerIds []string
	for _, redeemedOffer := range expiredRedeemedOffersList {
		offerIds = append(offerIds, redeemedOffer.GetOfferId())
	}

	// create offerId to offer map
	// we don't need updated vendor offer details as offer is already redeemed
	offerIdToOfferMap, err := r.getOfferIdToOfferMap(ctx, offerIds, false)
	if err != nil {
		logger.Error(ctx, "error getting offer id to offers map", zap.Error(err))
		return &fePb.GetExpiredRedeemedOffersResponse{Status: rpc.StatusInternalWithDebugMsg("error getting offer id to offers map " + err.Error())}, nil
	}

	// convert backend redeemed offers to fe redeemed offers using be redeemed offer and offer details
	var feRedeemedOffers []*fePb.RedeemedOffer
	for _, beRedeemedOffer := range expiredRedeemedOffersList {
		beOffer := offerIdToOfferMap[beRedeemedOffer.GetOfferId()]
		if !r.shouldDisplayRedeemedOfferOnAppVersion(ctx, appPlatform, appVersion, beRedeemedOffer, beOffer) {
			continue
		}

		// todo(rohan): do we need to pass salary program status from here?
		feRedeemedOffer, err := r.getFeRedeemedOffer(ctx, beRedeemedOffer, beOffer, req.GetReq().GetAppVersionCode(), req.GetReq().GetAuth().GetDevice().GetPlatform())
		if err != nil {
			logger.Error(ctx, "error converting beRedeemedOffer to feRedeemedOffer", zap.String(logger.REDEEMED_OFFER_ID, beRedeemedOffer.GetId()), zap.Error(err))
			return &fePb.GetExpiredRedeemedOffersResponse{Status: rpc.StatusInternalWithDebugMsg("error converting beRedeemedOffer to feRedeemedOffer " + err.Error())}, nil
		}
		feRedeemedOffers = append(feRedeemedOffers, feRedeemedOffer)
	}

	return &fePb.GetExpiredRedeemedOffersResponse{
		Status:         rpc.StatusOk(),
		RedeemedOffers: feRedeemedOffers,
		PageContext:    r.getFePageContextFromBeRpcPageContext(expiredRedeemedOffersRes.GetPageContext()),
	}, nil
}

func (r *RewardService) getOfferIdToOfferMap(ctx context.Context, offerIds []string, withUpdatedVendorOfferDetails bool) (map[string]*beCasperPb.Offer, error) {
	offerIdToOfferMap := make(map[string]*beCasperPb.Offer)

	if len(offerIds) == 0 {
		return offerIdToOfferMap, nil
	}
	// fetch offer details using offer ids
	beOfferDetailsRes, err := r.offerCatalogClient.GetBulkOfferDetailsByIds(ctx, &beCasperPb.GetBulkOfferDetailsByIdsRequest{OfferIds: offerIds, WithUpdatedVendorOfferDetails: withUpdatedVendorOfferDetails})
	if err != nil || !beOfferDetailsRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "GetBulkOfferDetailsByIds call failed", zap.Any("response", beOfferDetailsRes), zap.Error(err))
		return nil, errors.New("GetBulkOfferDetailsByIds call failed")
	}
	// create offer.Id to offer map
	for _, catalogOffer := range beOfferDetailsRes.GetOffers() {
		offerIdToOfferMap[catalogOffer.Id] = catalogOffer
	}
	return offerIdToOfferMap, nil
}

// getRewardAndOptionById fetches Reward and RewardOption by reward and option id.
// returns error if no reward option found for given optionId.
func (r *RewardService) getRewardAndOptionById(ctx context.Context, rewardId string, optionId string) (*beRewardsPb.Reward, *beRewardsPb.RewardOption, error) {
	rewardByIdRes, err := r.rewardsGeneratorClient.GetRewardsByRewardId(ctx, &beRewardsPb.RewardsByRewardIdRequest{RewardId: rewardId})
	if rpcErr := epifigrpc.RPCError(rewardByIdRes, err); rpcErr != nil {
		return nil, nil, fmt.Errorf("error fetching reward by id, err: %w", err)
	}

	for _, rewardOption := range rewardByIdRes.GetReward().GetRewardOptions().GetOptions() {
		if rewardOption.GetId() == optionId {
			return rewardByIdRes.GetReward(), rewardOption, nil
		}
	}
	return nil, nil, fmt.Errorf("no reward option found for given reward and option id")
}

// GetExchangerOrdersForActor fetches the exchanger orders for an actor.
// Note: Only the in-progress and fulfilled orders are fetched. Modify the logic if "user-intervention-reqd"
// status orders are also reqd in the future.
// Note: Newer clients should use GetRewardsForActorV1 in case they are fetching rewards for MyRewards screen.
// Since this RPC is specifically used for loading exchanger-orders for MyRewards page, the filters are accordingly hardcoded.
func (r *RewardService) GetExchangerOrdersForActor(ctx context.Context, req *fePb.GetExchangerOrdersForActorRequest) (*fePb.GetExchangerOrdersForActorResponse, error) {
	var (
		actorId     = req.GetReq().GetAuth().GetActorId()
		pageContext = req.GetPageContext()
	)
	if pageContext == nil {
		pageContext = &rpc.PageContextRequest{}
	}
	// setting the page-size explicitly irrespective of what client sends for consistency and safety-net
	pageContext.PageSize = r.rewardsFrontendMeta.AppFetchRewardsPageSize

	// note: add "user-intervention-reqd" state here in the future if reqd
	orderStatesFilter := []beExchangerPb.ExchangerOfferOrderState{
		beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_OPTION_CHOSEN,
		beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_FULFILLMENT_INITIATED,
		beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_FULFILLED,
	}

	// We won't be loading Fi-Coins rewards of CBR since they aren't exactly rewards, but more like a
	// compensation for spending their Fi-coins in CBR. Though, the fi-coins are added in their balance.
	rewardTypeFilter := []beExchangerPb.RewardType{
		beExchangerPb.RewardType_REWARD_TYPE_CASH,
	}

	feOrders, bePageContextResponse, err := r.getExchangerOrdersForActor(ctx, actorId, pageContext, rewardTypeFilter, orderStatesFilter, false, tags.RenderLocationUnspecified, beExchangerPb.RewardExpiryStatus_REWARD_EXPIRY_STATUS_UNSPECIFIED, nil)

	if err != nil {
		return &fePb.GetExchangerOrdersForActorResponse{Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("failed to fetch exchangerOrders for actor, err - %v", err.Error()))}, nil
	}

	return &fePb.GetExchangerOrdersForActorResponse{
		Status:      rpc.StatusOk(),
		Orders:      feOrders,
		PageContext: bePageContextResponse,
	}, nil
}

func (r *RewardService) getExchangerOrdersForActor(ctx context.Context, actorId string, pageContext *rpc.PageContextRequest, rewardTypesFilter []beExchangerPb.RewardType, orderStatesFilter []beExchangerPb.ExchangerOfferOrderState, fetchRedemptionCurrencyAndAmount bool,
	renderLocation tags.RenderLocation, rewardExpiryStatus beExchangerPb.RewardExpiryStatus, orFilters *beExchangerPb.GetExchangerOfferOrdersForActorRequest_OrFilters) ([]*fePb.ExchangerOrder, *rpc.PageContextResponse, error) {
	beExchangerOrdersRes, err := r.exchangerOfferClient.GetExchangerOfferOrdersForActor(ctx, &beExchangerPb.GetExchangerOfferOrdersForActorRequest{
		ActorId:     actorId,
		PageContext: pageContext,
		Filters: &beExchangerPb.GetExchangerOfferOrdersForActorRequest_Filters{
			States:             orderStatesFilter,
			RewardTypes:        rewardTypesFilter,
			RewardExpiryStatus: rewardExpiryStatus,
			WithinOrFilters:    orFilters,
		},
	})
	if err != nil || !beExchangerOrdersRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "error fetching be-exchanger-orders for actor", zap.Error(err), zap.Any(logger.RPC_STATUS, beExchangerOrdersRes.GetStatus()))
		return nil, nil, fmt.Errorf("error fetching be-exchanger-orders for actor. err - %w", err)
	}

	exchangerOfferOrders := beExchangerOrdersRes.GetExchangerOfferOrders()

	// decrypt exchanger offer orders if the orders fetched include EGV reward-type orders
	if r.isExchangerOfferRewardTypeInList(beExchangerPb.RewardType_REWARD_TYPE_EGV, rewardTypesFilter) {
		exchangerOfferOrders, err = r.decryptExchangerOfferOrders(ctx, exchangerOfferOrders)
		if err != nil {
			logger.Error(ctx, "error while decrypting exchanger offer orders", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
			return nil, nil, fmt.Errorf("error while decrypting exchanger offer orders, err: %w", err)
		}
	}

	exchangerOfferIdToOfferMap := map[string]*beExchangerPb.ExchangerOffer{}

	if fetchRedemptionCurrencyAndAmount {
		uniqueExchangerOfferIds := r.getUniqueExchangerOfferIdsFromExchangerOrders(exchangerOfferOrders)

		exchangerOfferIdToOfferMap, err = r.getExchangerOfferIdToOfferMap(ctx, uniqueExchangerOfferIds)
		if err != nil {
			logger.Error(ctx, "error in creating exchangerOfferId to exchangerOffer map", zap.Error(err))
			return nil, nil, fmt.Errorf("error in creating exchangerOfferId to exchangerOffer map - %w", err)
		}
	}

	feOrders := make([]*fePb.ExchangerOrder, 0, len(exchangerOfferOrders))
	for _, beOrder := range exchangerOfferOrders {
		var beExchangerOffer *beExchangerPb.ExchangerOffer
		if fetchRedemptionCurrencyAndAmount {
			if exchangerOffer, ok := exchangerOfferIdToOfferMap[beOrder.GetExchangerOfferId()]; ok {
				beExchangerOffer = exchangerOffer
			} else {
				logger.Error(ctx, "exchangerOffer does not exist for given exchangerOfferId", zap.String("exchangerOfferId", beOrder.GetExchangerOfferId()), zap.String(logger.EXCHANGER_OFFER_ORDER_ID, beOrder.GetId()))
				return nil, nil, fmt.Errorf("exchangerOffer does not exist for given exchangerOfferId")
			}
		}
		feOrder, err := r.convertToFeExchangerOrder(ctx, beOrder, beExchangerOffer, renderLocation)
		if err != nil {
			logger.Error(ctx, "error converting beOrder to feOrder", zap.String(logger.EXCHANGER_OFFER_ORDER_ID, beOrder.GetId()), zap.Error(err))
			continue
		}

		feOrders = append(feOrders, feOrder)
	}

	return feOrders, beExchangerOrdersRes.GetPageContext(), nil
}

// isExchangerOfferRewardTypeInList checks whether a given rewardType is a included in a list of rewardTypes or not
func (r *RewardService) isExchangerOfferRewardTypeInList(rewardTypeToCheck beExchangerPb.RewardType, rewardTypes []beExchangerPb.RewardType) bool {
	for _, rewardType := range rewardTypes {
		if rewardType == rewardTypeToCheck {
			return true
		}
	}
	return false
}

// decryptExchangerOfferOrders is used for decrypting exchanger offer orders.
// current use-case only requires us to decrypt EGV reward type orders. In future this method can be extended as per the use-case
// note: this method will preserve the order of exchangerOrders
func (r *RewardService) decryptExchangerOfferOrders(ctx context.Context, exchangerOfferOrders []*beExchangerPb.ExchangerOfferOrder) ([]*beExchangerPb.ExchangerOfferOrder, error) {
	processedExchangerOfferOrders := make([]*beExchangerPb.ExchangerOfferOrder, len(exchangerOfferOrders))
	exchangerOfferOrdersToDecrypt := make([]*beExchangerPb.ExchangerOfferOrder, 0)
	exchangerOfferOrderIdToIndexMap := map[string]int{}

	// only decrypting EGV reward type exchanger offer orders
	for idx, exchangerOfferOrder := range exchangerOfferOrders {
		if exchangerOfferOrder.GetRewardType() == beExchangerPb.RewardType_REWARD_TYPE_EGV {
			exchangerOfferOrdersToDecrypt = append(exchangerOfferOrdersToDecrypt, exchangerOfferOrder)
			exchangerOfferOrderIdToIndexMap[exchangerOfferOrder.GetId()] = idx
		} else {
			processedExchangerOfferOrders[idx] = exchangerOfferOrder
		}
	}

	if len(exchangerOfferOrdersToDecrypt) > 0 {
		decryptExchangerOfferOrdersRes, err := r.exchangerOfferClient.DecryptExchangerOfferOrdersDetails(ctx, &beExchangerPb.DecryptExchangerOfferOrdersDetailsRequest{
			ExchangerOfferOrders: exchangerOfferOrdersToDecrypt,
		})
		if err != nil || !decryptExchangerOfferOrdersRes.GetStatus().IsSuccess() {
			return nil, fmt.Errorf("failed to decrypt exchanger offer orders, err: %w, DecryptExchangerOfferOrdersDetails RPC response status: %s", err, decryptExchangerOfferOrdersRes.GetStatus().String())
		}

		for _, decryptedExchangerOrder := range decryptExchangerOfferOrdersRes.GetExchangerOfferOrders() {
			processedExchangerOfferOrders[exchangerOfferOrderIdToIndexMap[decryptedExchangerOrder.GetId()]] = decryptedExchangerOrder
		}
	}

	return processedExchangerOfferOrders, nil
}

func (r *RewardService) getUniqueExchangerOfferIdsFromExchangerOrders(exchangerOrders []*beExchangerPb.ExchangerOfferOrder) []string {
	exchangerOrderUniqueMap := map[string]struct{}{}
	// using a map to extract exchangerOfferId from exchangerOrders as exchangerOffers can have a 1:n relationship with exchangerOrders
	for _, exchangerOrder := range exchangerOrders {
		exchangerOrderUniqueMap[exchangerOrder.GetExchangerOfferId()] = struct{}{}
	}

	exchangerOfferIds := make([]string, 0, len(exchangerOrderUniqueMap))

	for key := range exchangerOrderUniqueMap {
		exchangerOfferIds = append(exchangerOfferIds, key)
	}

	return exchangerOfferIds
}

func (r *RewardService) getExchangerOfferIdToOfferMap(ctx context.Context, exchangerOfferIds []string) (map[string]*beExchangerPb.ExchangerOffer, error) {
	exchangerOrderIdToOfferMap := map[string]*beExchangerPb.ExchangerOffer{}

	getExchangerOffersByIdsResponse, err := r.exchangerOfferClient.GetExchangerOffersByIds(ctx, &beExchangerPb.GetExchangerOffersByIdsRequest{Ids: exchangerOfferIds})
	if err != nil || !getExchangerOffersByIdsResponse.GetStatus().IsSuccess() {
		logger.Error(ctx, "error while fetching exchanger offer by Ids", zap.Error(err), zap.String(logger.RPC_STATUS, getExchangerOffersByIdsResponse.GetStatus().String()))

		return nil, fmt.Errorf("error while fetching exchanger offers by Ids - %w", err)
	}

	for _, exchangerOffer := range getExchangerOffersByIdsResponse.GetExchangerOffers() {
		exchangerOrderIdToOfferMap[exchangerOffer.GetId()] = exchangerOffer
	}

	return exchangerOrderIdToOfferMap, nil
}

// getFiCoinsBalanceForActor returns the fi-coins balance for the actor
func (r *RewardService) getFiCoinsBalanceForActor(ctx context.Context, actorId string) (uint32, error) {
	accountDetailsRes, err := r.accrualClient.GetAccountDetails(ctx, &beAccrualPb.GetAccountDetailsRequest{
		ActorId:     actorId,
		AccountType: accrualPkg.FetchAccrualAccountType(beRewardsPb.RewardType_FI_COINS, timestamppb.Now()),
	})
	if rpcErr := epifigrpc.RPCError(accountDetailsRes, err); rpcErr != nil {
		logger.Error(ctx, "error fetching fi-coins balance for the actor", zap.String(logger.ACTOR_ID, actorId),
			zap.Error(err), zap.Any(logger.RPC_STATUS, accountDetailsRes.GetStatus()),
		)
		return 0, errors.New("GetAccountDetails call failed to fetch fi-coins balance")
	}

	return uint32(accountDetailsRes.GetAvailableBalance()), nil
}

//nolint:funlen
func (r *RewardService) GetExchangerOrderInputScreen(ctx context.Context, req *fePb.GetExchangerOrderInputScreenRequest) (*fePb.GetExchangerOrderInputScreenResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()
	exchangerOrderId := req.GetExchangerOrderId()

	getActorByIdResponse, err := r.actorServiceClient.GetActorById(ctx, &actor.GetActorByIdRequest{Id: actorId})
	if err != nil || !getActorByIdResponse.GetStatus().IsSuccess() {
		logger.Error(ctx, "failed to fetch actor by actorId", zap.String(logger.ACTOR_ID, actorId), zap.Error(err), zap.String(logger.RPC_STATUS, getActorByIdResponse.GetStatus().String()))
		return &fePb.GetExchangerOrderInputScreenResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusInternalWithDebugMsg("failed to fetch actor by actorId"),
				ErrorView: nil,
			},
		}, nil
	}
	actor := getActorByIdResponse.GetActor()

	getExchangerOrderByIdRequest := &beExchangerPb.GetExchangerOrderByIdRequest{
		ExchangerOrderId: exchangerOrderId,
	}

	// fetch the exchanger order for the given exchangerOrderId for this actor
	getExchangerOrderResp, err := r.exchangerOfferClient.GetExchangerOrderById(ctx, getExchangerOrderByIdRequest)
	if err != nil || !getExchangerOrderResp.GetStatus().IsSuccess() {
		logger.Error(ctx, "error fetching exchanger order for the given exchangerOrderId for actor", zap.String(logger.EXCHANGER_OFFER_ORDER_ID, req.GetExchangerOrderId()), zap.String(logger.ACTOR_ID, actorId), zap.Error(err), zap.String(logger.RPC_STATUS, getExchangerOrderResp.GetStatus().String()))
		return &fePb.GetExchangerOrderInputScreenResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusInternalWithDebugMsg("error fetching exchanger order for the given exchangerOrderId for actor"),
				ErrorView: nil,
			},
		}, nil
	}

	beExchangerOrder := getExchangerOrderResp.GetExchangerOrder()

	if beExchangerOrder.GetState() == beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_USER_INPUT_NEEDED_FOR_FULFILLMENT {
		chosenOption := beExchangerOrder.GetChosenOption()

		inputScreenDeeplink, errorView := r.getExchangerOrderInputScreenDeeplink(ctx, actor, chosenOption.GetRewardType(),
			req.GetReq().GetAuth().GetDevice().GetPlatform(), req.GetReq().GetAppVersionCode(),
		)
		if errorView != nil {
			logger.Error(ctx, "error getting input screen deeplink", zap.Any(logger.ACTOR_ID, actorId), zap.String(logger.EXCHANGER_OFFER_ORDER_ID, exchangerOrderId))
			return &fePb.GetExchangerOrderInputScreenResponse{
				RespHeader: &header.ResponseHeader{
					Status:    rpc.StatusInternalWithDebugMsg("error getting input screen deeplink"),
					ErrorView: errorView,
				},
			}, nil
		}
		return &fePb.GetExchangerOrderInputScreenResponse{
			InputScreen: inputScreenDeeplink,
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusOk(),
				ErrorView: nil,
			},
		}, nil
	}

	logger.Error(ctx, "exchanger order not in USER_INPUT_NEEDED_FOR_FULFILLMENT state", zap.String(logger.EXCHANGER_OFFER_ORDER_ID, exchangerOrderId), zap.String("exchangerOrderRewardType", beExchangerOrder.GetRewardType().String()), zap.String("exchangerOrderState", beExchangerOrder.GetState().String()))
	return &fePb.GetExchangerOrderInputScreenResponse{
		RespHeader: &header.ResponseHeader{
			Status:    rpc.StatusInternalWithDebugMsg("exchanger order not in USER_INPUT_NEEDED_FOR_FULFILLMENT state"),
			ErrorView: nil,
		},
	}, nil
}

func (r *RewardService) getExchangerOrderInputScreenDeeplink(ctx context.Context, actor *types.Actor, rewardType beExchangerPb.RewardType, platform commontypes.Platform, appVersion uint32) (*deeplink.Deeplink, *errors2.ErrorView) {
	if rewardType == beExchangerPb.RewardType_REWARD_TYPE_PHYSICAL_MERCHANDISE {
		// fetch existing shipping and permanent address from user profile
		userAddressRes, err := r.usersClient.GetAllAddresses(ctx, &beUserPb.GetAllAddressesRequest{UserId: actor.GetEntityId()})
		if err != nil || !userAddressRes.GetStatus().IsSuccess() {
			logger.Error(ctx, "GetAllAddresses rpc call failed", zap.String(logger.USER_ID, actor.GetEntityId()), zap.Any(logger.RPC_STATUS, userAddressRes.GetStatus()), zap.Error(err))

			// if client doesn't support graceful handling of the empty addresses, then return error
			if !r.checkIfClientSupportsEmptyAddressesGracefully(platform, appVersion) {
				return nil, exchangerOrderUserInputDefaultErrorView
			}
		}

		// convert google postal address to types.postalAddress
		var addresses []*types.PostalAddress
		for addressType, address := range userAddressRes.GetAddresses() {
			addressList := address.GetAddresses()
			if (addressType == types.AddressType_SHIPPING.String() || addressType == types.AddressType_PERMANENT.String()) && len(addressList) > 0 {
				addresses = append(addresses, convertToClientPostalAddressType(addressList[0]))
			}
		}

		// return deeplink with user's existing shipping address details
		return &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_REWARD_SHIPPING_ADDRESS_INPUT_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_RewardShippingAddressInputScreenOptions{
				RewardShippingAddressInputScreenOptions: &deeplinkPb.RewardShippingAddressInputScreenOptions{
					// existing shipping addresses present in user profile
					Addresses: addresses,
				},
			},
		}, nil
	}
	return nil, exchangerOrderUserInputDefaultErrorView
}

//nolint:funlen
func (r *RewardService) SubmitExchangerOrderUserInput(ctx context.Context, req *fePb.SubmitExchangerOrderUserInputRequest) (*fePb.SubmitExchangerOrderUserInputResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()
	exchangerOrderId := req.GetExchangerOrderId()

	getExchangerOrderByIdRequest := &beExchangerPb.GetExchangerOrderByIdRequest{
		ExchangerOrderId: exchangerOrderId,
	}
	// fetch the exchanger order for the given exchangerOrderId for this actor
	getExchangerOrderResp, err := r.exchangerOfferClient.GetExchangerOrderById(ctx, getExchangerOrderByIdRequest)
	if err != nil || !getExchangerOrderResp.GetStatus().IsSuccess() {
		logger.Error(ctx, "error fetching exchanger order for the given exchangerOrderId for actor", zap.String(logger.EXCHANGER_OFFER_ORDER_ID, exchangerOrderId), zap.String(logger.ACTOR_ID, actorId), zap.Error(err), zap.String(logger.RPC_STATUS, getExchangerOrderResp.GetStatus().String()))
		return &fePb.SubmitExchangerOrderUserInputResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusInternalWithDebugMsg("error fetching exchanger order for the given exchangerOrderId for actor"),
				ErrorView: exchangerOrderUserInputDefaultErrorView,
			},
		}, nil
	}

	beExchangerOrder := getExchangerOrderResp.GetExchangerOrder()

	if beExchangerOrder.GetState() != beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_USER_INPUT_NEEDED_FOR_FULFILLMENT {
		logger.Error(ctx, "exchanger order isn't in USER_INPUT_NEEDED_FOR_FULFILLMENT state", zap.String(logger.EXCHANGER_OFFER_ORDER_ID, exchangerOrderId), zap.String("exchangerOrderState", beExchangerOrder.GetState().String()))
		return &fePb.SubmitExchangerOrderUserInputResponse{RespHeader: &header.ResponseHeader{
			Status: rpc.StatusInternalWithDebugMsg("exchanger order isn't in USER_INPUT_NEEDED_FOR_FULFILLMENT state"),
		}}, nil
	}

	if beExchangerOrder.GetRewardType() == beExchangerPb.RewardType_REWARD_TYPE_PHYSICAL_MERCHANDISE {
		feShippingAddress := req.GetUserInput().GetShippingAddress()
		beShippingAddress := convertToGooglePostalAddressType(feShippingAddress)

		submitUserInputForChosenOptionRequest := &beExchangerPb.SubmitUserInputForChosenOptionRequest{
			ActorId:          actorId,
			ExchangerOrderId: exchangerOrderId,
			ShippingAddress:  beShippingAddress,
		}

		submitUserInputForChosenOptionResponse, err := r.exchangerOfferClient.SubmitUserInputForChosenOption(ctx, submitUserInputForChosenOptionRequest)
		if err != nil || !submitUserInputForChosenOptionResponse.GetStatus().IsSuccess() {
			logger.Error(ctx, "error occurred while submitting user input for chosen option", zap.Error(err), zap.String(logger.ACTOR_ID, actorId), zap.String(logger.EXCHANGER_OFFER_ORDER_ID, exchangerOrderId), zap.Any(logger.RPC_STATUS, submitUserInputForChosenOptionResponse.GetStatus()))
			return &fePb.SubmitExchangerOrderUserInputResponse{
				RespHeader: &header.ResponseHeader{
					Status:    rpc.StatusInternalWithDebugMsg("error occurred while submitting user input for chosen option"),
					ErrorView: exchangerOrderUserInputDefaultErrorView,
				},
			}, nil
		}
		// return error view if order is still in USER_INPUT_NEEDED state
		if r.checkIfBottomSheetErrorIsSupportedOnAppVersion(req.GetReq()) && submitUserInputForChosenOptionResponse.GetExchangerOfferOrder().GetState() == beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_USER_INPUT_NEEDED_FOR_FULFILLMENT {
			logger.Error(ctx, "exchanger offer order stuck in user input needed state", zap.Error(err), zap.String(logger.ACTOR_ID, actorId), zap.String(logger.EXCHANGER_OFFER_ORDER_ID, exchangerOrderId))
			return &fePb.SubmitExchangerOrderUserInputResponse{
				RespHeader: &header.ResponseHeader{
					Status:    rpc.StatusInternalWithDebugMsg("exchanger offer order stuck in user input needed state"),
					ErrorView: stuckSubmitUserInputForChosenOption,
				},
			}, nil
		}
		feExchangerOrder, err := r.convertToFeExchangerOrder(ctx, submitUserInputForChosenOptionResponse.GetExchangerOfferOrder(), nil, tags.RenderLocationUnspecified)
		if err != nil {
			logger.Error(ctx, "error occurred while converting BE exchanger-order to FE exchanger-order", zap.Error(err))
			return &fePb.SubmitExchangerOrderUserInputResponse{
				RespHeader: &header.ResponseHeader{
					Status:    rpc.StatusInternalWithDebugMsg("error occurred while converting BE exchanger-order to FE exchanger-order"),
					ErrorView: exchangerOrderUserInputDefaultErrorView,
				},
			}, nil
		}
		return &fePb.SubmitExchangerOrderUserInputResponse{
			ExchangerOrder: feExchangerOrder,
			Cta: &fePb.SubmitExchangerOrderUserInputResponse_CTA{
				Text: "View in 'Collected Offers'",
				NextScreen: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_REDEEMED_OFFERS_SCREEN,
				},
			},
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusOk(),
			},
		}, nil
	}
	logger.Error(ctx, "error in submitting user's input, unsupported reward type", zap.Any(logger.ACTOR_ID, actorId), zap.String(logger.REWARD_TYPE, beExchangerOrder.GetChosenOption().GetRewardType().String()), zap.String(logger.EXCHANGER_OFFER_ORDER_ID, exchangerOrderId))
	return &fePb.SubmitExchangerOrderUserInputResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusInternalWithDebugMsg("error in submitting user's input, unsupported reward type"),
		},
	}, nil
}

func (r *RewardService) GetCardOfferDetails(ctx context.Context, req *fePb.GetCardOfferDetailsRequest) (*fePb.GetCardOfferDetailsResponse, error) {
	// todo(yuvraj): add support for display expression based filtering if required for card offers.
	offerDetailsRes, err := r.offerCatalogClient.GetBulkOfferDetailsByIds(ctx, &beCasperPb.GetBulkOfferDetailsByIdsRequest{OfferIds: []string{req.GetOfferId()}})
	if grpcErr := epifigrpc.RPCError(offerDetailsRes, err); grpcErr != nil {
		logger.Error(ctx, "error while fetching offer from be", zap.String(logger.OFFER_ID, req.GetOfferId()), zap.Error(grpcErr))
		return &fePb.GetCardOfferDetailsResponse{RespHeader: &header.ResponseHeader{
			Status: rpc.StatusInternalWithDebugMsg("error while fetching offer from be"),
		}}, nil
	}
	if len(offerDetailsRes.GetOffers()) != 1 {
		logger.Error(ctx, "invalid response while fetching offer from be", zap.String(logger.OFFER_ID, req.GetOfferId()), zap.Int(logger.LENGTH, len(offerDetailsRes.GetOffers())))
		return &fePb.GetCardOfferDetailsResponse{RespHeader: &header.ResponseHeader{
			Status: rpc.StatusInternalWithDebugMsg("invalid response while fetching offer from be"),
		}}, nil
	}

	// fetch display active listings of the offer
	offerListingsRes, err := r.offerListingClient.GetDisplayActiveListingsForOffer(ctx, &beCasperPb.GetDisplayActiveListingsForOfferRequest{OfferId: req.GetOfferId()})
	if grpcErr := epifigrpc.RPCError(offerListingsRes, err); grpcErr != nil {
		if offerListingsRes.GetStatus().IsRecordNotFound() {
			logger.Error(ctx, "no display active listings found for the offer", zap.String(logger.OFFER_ID, req.GetOfferId()))
			return &fePb.GetCardOfferDetailsResponse{RespHeader: &header.ResponseHeader{
				Status: rpc.StatusRecordNotFound(),
			}}, nil
		}
		logger.Error(ctx, "error while fetching display active listings of the offer", zap.String(logger.OFFER_ID, req.GetOfferId()), zap.Error(grpcErr))
		return &fePb.GetCardOfferDetailsResponse{RespHeader: &header.ResponseHeader{
			Status: rpc.StatusInternalWithDebugMsg("error while fetching display active listings of the offer"),
		}}, nil
	}

	latestOfferListing := offerListingsRes.GetOfferListings()[len(offerListingsRes.GetOfferListings())-1]
	feCardOffer, err := r.getFeCardOfferFromBeOffer(offerDetailsRes.GetOffers()[0], latestOfferListing, false)
	if err != nil {
		logger.Error(ctx, "error while getting FE card offer from BE offer", zap.String(logger.OFFER_ID, req.GetOfferId()), zap.Error(err))
		return &fePb.GetCardOfferDetailsResponse{RespHeader: &header.ResponseHeader{
			Status: rpc.StatusInternalWithDebugMsg("error while getting FE card offer from BE offer"),
		}}, nil
	}
	return &fePb.GetCardOfferDetailsResponse{
		CardOffer: feCardOffer,
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
	}, nil
}

// getTieringFeatureFlagAndCurrentTier checks and returns whether tiering is enabled or not. If yes, it also returns the current tier.
// Note: Any error while checking for tier is ignored silently and assumed that tiering is disabled
func getTieringFeatureFlagAndCurrentTier(ctx context.Context, actorId string, tieringClient beTieringPb.TieringClient) (isTieringEnabled bool, currentTier tieringExtPb.Tier) {
	currentTierRes, tierErr := tieringClient.GetTieringPitchV2(ctx, &beTieringPb.GetTieringPitchV2Request{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(currentTierRes, tierErr); rpcErr != nil {
		logger.Error(ctx, "error fetching current tier of the actor. Ignoring silently", zap.Error(tierErr), zap.Any(logger.RPC_STATUS, currentTierRes.GetStatus()))
		return false, tieringExtPb.Tier_TIER_UNSPECIFIED
	}
	logger.Debug(ctx, "tiering feature flag and current tier info", zap.Any("currentTierRes", currentTierRes))

	return true, currentTierRes.GetCurrentTier()
}

// shouldDisplayRedeemedOfferOnAppVersion returns whether the redeemedOffer can be displayed on the given app version.
func (r *RewardService) shouldDisplayRedeemedOfferOnAppVersion(_ context.Context, appPlatform commontypes.Platform, appVersion uint32, beRedeemedOffer *beRedemptionPb.RedeemedOffer, beOffer *beCasperPb.Offer) bool {
	// not displaying redemptions for a mis-configured offer
	// todo (utkarsh) : soft delete these redemptions from the db.
	if beRedeemedOffer.GetOfferId() == "d345cbd8-ab86-4145-ad02-9699704e2147" ||
		// hide the redemptions of this offer for specific users.
		(beRedeemedOffer.GetOfferId() == "b43d430d-266a-4155-952b-ec1babbcbbeb" && lo.Contains([]string{"AC210522CWnSPcY0TQeYTjsSDPhB2w==", "AC210613VrHFiwh0T9OG5iszNDZ1iA==", "AC210622KqPeekvVTOuMmZaVRMZBew==", "AC2107194HAjJ5VWSO+7Knodz/Midw==", "AC2107317IC7jUBhQ62LHmeapf16qQ==",
			"AC210807UlEMxxisSPi/hEqDP8KIjw==", "AC2108081u1nzgDXQyCRgO7i+vTB/A==", "AC210908/dWInNKMSfeISNnWki9KWA==", "AC2109086YU9IC/8Rl22egaVTgRjiA==", "AC210914DbRJCkyVTdewiAPkbwN3Yw==", "AC2110138ZnwcR/cQEi/o+j4Zu/slg==", "AC220306f6kUd74SS2uEWKwFHiIKkg==", "AC220402ZHsm67ceQvOJXqRTdh2zsw==",
			"AC220518jANGbEl7TYmtw3/o98eMSg==", "AC220731ha2O+6sISjK7Tjz0nYNWtA=="}, beRedeemedOffer.GetActorId())) {
		return false
	}

	if beOffer.GetOfferType() == beCasperPb.OfferType_THRIWE_BENEFITS_PACKAGE {
		if appPlatform == commontypes.Platform_ANDROID && appVersion < r.dyconf.RewardsFrontendMeta().MinAndroidAppVersionSupportingThriweBenefitsPackageOffers() ||
			appPlatform == commontypes.Platform_IOS && appVersion < r.dyconf.RewardsFrontendMeta().MinIosAppVersionSupportingThriweBenefitsPackageOffers() {
			return false
		}
	}

	if beOffer.GetOfferType() == beCasperPb.OfferType_VISTARA_AIR_MILES {
		if appPlatform == commontypes.Platform_ANDROID && appVersion < r.dyconf.RewardsFrontendMeta().MinAndroidAppVersionSupportingVistaraAirMilesOffer() ||
			appPlatform == commontypes.Platform_IOS && appVersion < r.dyconf.RewardsFrontendMeta().MinIosAppVersionSupportingVistaraAirMilesOffer() {
			return false
		}
	}

	return true
}

// getBulkClaimRewardsCtaV2 returns the CTA for bulk claim rewards.
// Currently, it is only enabled for full KYC users.
// If bulk claim rewards are in processing state, it returns a CTA to show the processing state.
// If bulk claim rewards are not in processing state, it returns a CTA if there are unopened rewards.
// TODO: also need to handle fi lite case where kyc level is unspecified, currently this is enabled to full kyc users only.
// Monorail - https://monorail.pointz.in/p/fi-app/issues/detail?id=68121
func (r *RewardService) getBulkClaimRewardsCtaV2(ctx context.Context, actorId string, userAndAppAttributes *UserAndAppAttributes, unopenedRewards []*fePb.RewardWrapperV1) *fePb.IconTextCta {
	if userAndAppAttributes.KycLevel != beKycPb.KYCLevel_FULL_KYC {
		return nil
	}

	resp, err := r.rewardsGeneratorClient.GetBulkClaimRewardsProcessingState(ctx, &beRewardsPb.GetBulkClaimRewardsProcessingStateRequest{ActorId: actorId})
	if err != nil {
		logger.Error(ctx, "error while fetching GetBulkClaimRewardsProcessingState", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return nil
	}
	// only if bulk rewards are in processing state, show processing CTA, otherwise show Open all Money Plants CTA.
	if resp.GetIsProcessingState() {
		return &fePb.IconTextCta{
			LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/rewards/my_rewards_claim_all_time_clock.png", 28, 28),
			CtaText:           commontypes.GetPlainStringText("Opening your rewards...").WithFontStyle(commontypes.FontStyle_HEADLINE_S).WithFontColor("#A9A2C9"),
			CtaSubText:        commontypes.GetPlainStringText("This may take up 24 hours").WithFontStyle(commontypes.FontStyle_OVERLINE_2XS_CAPS).WithFontColor("#F6E1C1"),
			BgColor:           widget.GetBlockBackgroundColour("#282828"),
		}
	} else {
		// if there are no unopened rewards, no need to show the CTA.
		if len(unopenedRewards) == 0 {
			return nil
		}
		return &fePb.IconTextCta{
			LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/rewards/my_rewards_claim_all_lightning.png", 28, 28),
			CtaText:           commontypes.GetPlainStringText("Open all Money-Plants").WithFontStyle(commontypes.FontStyle_HEADLINE_S).WithFontColor("#A9A2C9"),
			BgColor:           widget.GetBlockBackgroundColour("#282828"),
			Action: &fePb.IconTextCta_BottomSheetAction{
				BottomSheetAction: &fePb.BottomSheetInfo{
					Title: commontypes.GetPlainStringText("Open all Money-Plants\nin 1-tap").WithFontColor("#F6F9FD").WithFontStyle(commontypes.FontStyle_HEADLINE_M),
					Desc:  commontypes.GetPlainStringText("When we open a Money-Plant for you, we pick the default choice as your reward. \n\nNote: Some Money-Plants provide reward choices — those you must open yourself.").WithFontColor("#B2B5B9"),
					Image: commontypes.GetImageFromUrl("https://epifi-icons.pointz.in/rewards/my_rewards_claim_all_lightning.png"),
					CtaV1: &fePb.CtaV1{
						CtaTitle: commontypes.GetPlainStringText("Continue").WithFontColor("#FFFFFF").WithFontStyle(commontypes.FontStyle_BUTTON_M),
						Action: &fePb.CtaV1_CustomAction{
							CustomAction: &fePb.CustomAction{
								ActionType: fePb.CustomAction_MAKE_API_CALL,
								ActionApi:  fePb.CustomActionApi_BULK_CLAIM_REWARDS_RPC,
							},
						},
						BgColor: "#00B899",
					},
				},
			},
		}
	}
}

func (r *RewardService) GetWaysToEarnRewardsScreenVersionToRender(ctx context.Context, req *fePb.GetWaysToEarnRewardsScreenVersionToRenderRequest) (*fePb.GetWaysToEarnRewardsScreenVersionToRenderResponse, error) {

	if apputils.IsFeatureEnabledFromCtxDynamic(ctx, r.dyconf.RewardsFrontendMeta().WaysToEarnRewardsV2ScreenFeatureConfig()) {
		return &fePb.GetWaysToEarnRewardsScreenVersionToRenderResponse{
			RespHeader:                     &header.ResponseHeader{Status: rpc.StatusOk()},
			WaysToEarnRewardsScreenVersion: fePb.GetWaysToEarnRewardsScreenVersionToRenderResponse_WAYS_TO_EARN_REWARDS_SCREEN_VERSION_2,
		}, nil
	}

	return &fePb.GetWaysToEarnRewardsScreenVersionToRenderResponse{
		RespHeader:                     &header.ResponseHeader{Status: rpc.StatusOk()},
		WaysToEarnRewardsScreenVersion: fePb.GetWaysToEarnRewardsScreenVersionToRenderResponse_WAYS_TO_EARN_REWARDS_SCREEN_VERSION_1,
	}, nil
}

func (r *RewardService) getExternalVendorRedemptionsForActor(ctx context.Context, actorId string, orderStatus []evrPb.OrderStatus, pageContextReq *rpc.PageContextRequest) ([]*fePb.CollectedOffer, *rpc.PageContextResponse, error) {
	// flag to enable fi store collected offers cards
	if !r.dyconf.FiStoreCollectedOffersConfig().IsFiStoreCollectedOffersEnabled() {
		return []*fePb.CollectedOffer{}, &rpc.PageContextResponse{HasAfter: false}, nil
	}
	// app version check to support the fi store collected offers cards
	appPlatform, appVersion := epificontext.AppPlatformAndVersion(ctx)
	if !((appPlatform == commontypes.Platform_ANDROID && appVersion >= r.dyconf.FiStoreCollectedOffersConfig().MinAndroidVersionForFiStoreCollectedOffers()) ||
		(appPlatform == commontypes.Platform_IOS && appVersion >= r.dyconf.FiStoreCollectedOffersConfig().MinIosVersionForFiStoreCollectedOffers())) {
		return []*fePb.CollectedOffer{}, &rpc.PageContextResponse{HasAfter: false}, nil
	}
	resp, err := r.externalVendorRedemptionClient.GetFiStoreRedemptions(ctx, &evrPb.GetFiStoreRedemptionsRequest{
		ActorId: actorId,
		Filters: &evrPb.GetFiStoreRedemptionsRequest_Filters{
			Categories: []evrPb.Category{
				evrPb.Category_CATEGORY_ECOM,
				evrPb.Category_CATEGORY_GIFT_CARDS,
				evrPb.Category_CATEGORY_MILES_EXCHANGE,
			},
			OrderStatuses: orderStatus,
		},
		PageCtxRequest: pageContextReq,
	})
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		return nil, nil, rpcErr
	}

	logger.Debug(ctx, "fi store redemptions fetched for the actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Int(logger.LENGTH, len(resp.GetRedemptions())))

	// if no redemptions exists return empty collected offers list
	if len(resp.GetRedemptions()) == 0 {
		return []*fePb.CollectedOffer{}, &rpc.PageContextResponse{HasAfter: false}, nil
	}

	// fetch be mapping value for given actor id
	vmResp, err := r.vendorMappingClient.GetBEMappingById(ctx, &vendormappingPb.GetBEMappingByIdRequest{Id: actorId})
	if rpcErr := epifigrpc.RPCError(vmResp, err); rpcErr != nil {
		return nil, nil, rpcErr
	}

	debitCardId, err := r.getDebitCardIdForActor(ctx, actorId)
	if err != nil {
		return nil, nil, err
	}

	refIdToRedemptionsMap := filterRedemptionsBasedOnRefId(resp.GetRedemptions())
	var collectedOffers []*fePb.CollectedOffer
	for refId, redemptions := range refIdToRedemptionsMap {
		if len(redemptions) == 0 {
			continue
		}
		collectedOffer, collectedOfferErr := r.getFeCollectedOfferForFiStoreRedemption(ctx, r.dyconf, actorId, vmResp.GetDpandaId(), debitCardId, redemptions)
		if collectedOfferErr != nil {
			logger.Error(ctx, "error while fetching fe collected offer card for fi store", zap.String(logger.ACTOR_ID_V2, actorId), zap.String("refId", refId), zap.Error(collectedOfferErr))
			continue
		}
		collectedOffers = append(collectedOffers, collectedOffer)
	}
	return collectedOffers, resp.GetPageCtxResponse(), nil
}

// filterRedemptionsBasedOnRefId returns map of redemptions based on unique vendor ref id.
func filterRedemptionsBasedOnRefId(redemptions []*evrPb.FiStoreRedemption) map[string][]*evrPb.FiStoreRedemption {
	refIdToRedemptionsMap := make(map[string][]*evrPb.FiStoreRedemption)
	for _, re := range redemptions {
		refIdToRedemptionsMap[re.GetVendorRefId()] = append(refIdToRedemptionsMap[re.GetVendorRefId()], re)
	}
	return refIdToRedemptionsMap
}

func (r *RewardService) getFeCollectedOfferForFiStoreRedemption(ctx context.Context, dynConf *genconf.Config, actorId, dPandaId, debitCardId string, redemptions []*evrPb.FiStoreRedemption) (*fePb.CollectedOffer, error) {
	offerDetails, err := r.getFiStoreCardOfferDetail(ctx, dynConf, dPandaId, debitCardId, redemptions[0])
	if err != nil {
		logger.Error(ctx, "error while fetching offer details sections", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return nil, err
	}
	return &fePb.CollectedOffer{
		Id:           redemptions[0].GetId(),
		CardRow:      r.getFiStoreCardRow(dynConf, redemptions),
		OfferDetails: offerDetails,
	}, nil
}

// nolint: funlen, gocritic
func (r *RewardService) getFiStoreCardOfferDetail(_ context.Context, dynConf *genconf.Config, dPandaId, debitCardId string, redemption *evrPb.FiStoreRedemption) (*fePb.CollectedOffer_OfferDetails, error) {
	var (
		offerDetailsSections []*fePb.OfferDetailsSection
		requestMetadata      string
		externalUrl          string
		text                 *commontypes.Text
	)
	switch redemption.GetCategory() {
	case evrPb.Category_CATEGORY_ECOM:
		switch redemption.GetVendor() {
		case evrPb.Vendor_DPANDA:
			externalUrl = fmt.Sprintf(r.dyconf.FiStoreCollectedOffersConfig().EComCategoryCard().WebpageUrl(), dPandaId)
			text = r.getText("#646464", "#FFFFFF", dynConf.FiStoreCollectedOffersConfig().EComCategoryCard().Description(), commontypes.FontStyle_BODY_S)
		case evrPb.Vendor_RAZORPAY:
			text = r.getText("#646464", "#FFFFFF", accrualPkg.ReplaceCoinWithPointIfApplicable(dynConf.FiStoreCollectedOffersConfig().EComDiscountsCard().Description()), commontypes.FontStyle_BODY_S)
		default:
			return nil, fmt.Errorf("unsupported vendor")
		}
	case evrPb.Category_CATEGORY_GIFT_CARDS:
		requestMetadata = fmt.Sprintf("vendor:POSHVINE,target_url:%s", dynConf.FiStoreCollectedOffersConfig().GiftCardsCard().WebpageUrl())
		text = r.getText("#646464", "#FFFFFF", dynConf.FiStoreCollectedOffersConfig().GiftCardsCard().Description(), commontypes.FontStyle_BODY_S)
	case evrPb.Category_CATEGORY_FLIGHTS:
		requestMetadata = fmt.Sprintf("vendor:POSHVINE,target_url:%s", dynConf.FiStoreCollectedOffersConfig().FlightsCard().WebpageUrl())
		text = r.getText("#646464", "#FFFFFF", dynConf.FiStoreCollectedOffersConfig().FlightsCard().Description(), commontypes.FontStyle_BODY_S)
	case evrPb.Category_CATEGORY_HOTELS:
		requestMetadata = fmt.Sprintf("vendor:POSHVINE,target_url:%s", dynConf.FiStoreCollectedOffersConfig().HotelsCard().WebpageUrl())
		text = r.getText("#646464", "#FFFFFF", dynConf.FiStoreCollectedOffersConfig().HotelsCard().Description(), commontypes.FontStyle_BODY_S)
	case evrPb.Category_CATEGORY_MILES_EXCHANGE:
		requestMetadata = fmt.Sprintf("vendor:POSHVINE,target_url:%s", dynConf.FiStoreCollectedOffersConfig().MilesExchangeCard().WebpageUrl())
		text = r.getText("#646464", "#FFFFFF", dynConf.FiStoreCollectedOffersConfig().MilesExchangeCard().Description(), commontypes.FontStyle_BODY_S)
	default:
		return nil, fmt.Errorf("unsupported category")
	}

	var cta *fePb.CtaV1

	// for razorpay we don't have any deeplink
	if redemption.GetVendor() != evrPb.Vendor_RAZORPAY {
		deeplinkAction := r.getWebPageWithScreenDetailsScreen(debitCardId != "", debitCardId, externalUrl, requestMetadata)
		cta = &fePb.CtaV1{
			CtaTitle:     r.getText("#00B899", "#F7F9FC", "View Details", commontypes.FontStyle_BUTTON_M),
			RightIconUrl: "https://epifi-icons.pointz.in/rewards/share-code-as-gift-icon.png",
			Action: &fePb.CtaV1_DeeplinkAction{
				DeeplinkAction: deeplinkAction,
			},
			BgColor: "#F7F9FC",
		}
	}

	// offer description section
	offerDetailsSections = append(offerDetailsSections, &fePb.OfferDetailsSection{
		Infos: []*fePb.OfferDetailsSection_Info{
			{
				Data: &fePb.OfferDetailsSection_Info_ImageAndTextInfo{
					ImageAndTextInfo: &fePb.ImageAndTextInfo{
						Text: text,
					},
				},
			},
		},
		Cta:     cta,
		BgColor: "#FFFFFF",
	})

	// order id section
	offerDetailsSections = append(offerDetailsSections, &fePb.OfferDetailsSection{
		Header: &fePb.OfferDetailsSection_Header{
			Title: r.getText("#333333", "#F7F9FC", "Order Id", commontypes.FontStyle_SUBTITLE_S),
		},
		Infos: []*fePb.OfferDetailsSection_Info{
			{
				Data: &fePb.OfferDetailsSection_Info_ImageAndTextInfo{
					ImageAndTextInfo: &fePb.ImageAndTextInfo{
						Text: r.getText("#646464", "#FFFFFF", redemption.GetVendorRefId(), commontypes.FontStyle_BODY_S),
					},
				},
			},
		},
		BgColor: "#FFFFFF",
	})

	return &fePb.CollectedOffer_OfferDetails{
		RedeemedOfferDetailsSections: offerDetailsSections,
	}, nil
}

// nolint: funlen
func (r *RewardService) getFiStoreCardRow(dynConf *genconf.Config, redemptions []*evrPb.FiStoreRedemption) *fePb.CollectedOffer_CardRow {
	var (
		card                 *genconf.FiStoreCollectedOffersCardConfig
		icon                 string
		displayTagTitle      string
		displayTagBgColor    string
		cardTitle            string
		showGreyOutOfferCard bool
		redemption           = redemptions[0]
	)
	switch redemption.GetCategory() {
	case evrPb.Category_CATEGORY_ECOM:
		switch redemption.GetVendor() {
		case evrPb.Vendor_DPANDA:
			card = dynConf.FiStoreCollectedOffersConfig().EComCategoryCard()
			// If user brought 2 or more products at a time, show on collected offers only one card with stating "and 2 more" for given order id.
			if len(redemptions) >= 2 {
				cardTitle = redemption.GetRedemptionMetaData().GetProductName() + fmt.Sprintf("and %v more", len(redemptions)-1)
			} else {
				cardTitle = redemption.GetRedemptionMetaData().GetProductName()
			}
		case evrPb.Vendor_RAZORPAY:
			card = dynConf.FiStoreCollectedOffersConfig().EComDiscountsCard()
			cardTitle = redemption.GetRedemptionMetaData().GetBrandName()
		default:
			return nil
		}
	case evrPb.Category_CATEGORY_GIFT_CARDS:
		card = dynConf.FiStoreCollectedOffersConfig().GiftCardsCard()
		cardTitle = redemption.GetRedemptionMetaData().GetBrandName()
	case evrPb.Category_CATEGORY_FLIGHTS:
		card = dynConf.FiStoreCollectedOffersConfig().FlightsCard()
		cardTitle = redemption.GetRedemptionMetaData().GetBrandName()
	case evrPb.Category_CATEGORY_HOTELS:
		card = dynConf.FiStoreCollectedOffersConfig().HotelsCard()
		cardTitle = redemption.GetRedemptionMetaData().GetBrandName()
	case evrPb.Category_CATEGORY_MILES_EXCHANGE:
		card = dynConf.FiStoreCollectedOffersConfig().MilesExchangeCard()
		cardTitle = redemption.GetRedemptionMetaData().GetBrandName()
		if cardTitle == "" {
			cardTitle = redemption.GetRedemptionMetaData().GetProductName()
		}
	default:
		return nil
	}

	// show greyed out offer card only if order status is cancelled or failed
	if redemption.GetOrderStatus() == evrPb.OrderStatus_ORDER_STATUS_CANCELLED || redemption.GetOrderStatus() == evrPb.OrderStatus_ORDER_STATUS_FAILED {
		icon = r.dyconf.FiStoreCollectedOffersConfig().CancelIconUrl()
		showGreyOutOfferCard = true
		displayTagTitle = "Cancelled"
		displayTagBgColor = "#AA301F"
	} else {
		icon = r.dyconf.FiStoreCollectedOffersConfig().FiStoreIconUrl()
		showGreyOutOfferCard = false
		displayTagTitle = "Fi Store"
		displayTagBgColor = "#33333399"
	}

	return &fePb.CollectedOffer_CardRow{
		Header: &commontypes.Text{
			FontColor: "#FFFFFF",
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: cardTitle,
			},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_HEADLINE_2,
			},
		},
		SubTitle:               nil, // TODO: add relevant subtitle.
		BannerBgImage:          commontypes.GetVisualElementImageFromUrl(card.BackgroundImageUrl()),
		BannerBgColor:          widget.GetBlockBackgroundColour(card.BackgroundColor()),
		BannerLogo:             commontypes.GetVisualElementFromUrlHeightAndWidth(card.LogoUrl(), 32, 32),
		ShowGreyedOutOfferCard: showGreyOutOfferCard,
		DisplayTags: []*ui.IconTextComponent{
			{
				ContainerProperties: &ui.IconTextComponent_ContainerProperties{
					BgColor: displayTagBgColor,
				},
				LeftVisualElement: &commontypes.VisualElement{
					Asset: &commontypes.VisualElement_Image_{
						Image: &commontypes.VisualElement_Image{
							Source: &commontypes.VisualElement_Image_Url{
								Url: icon,
							},
							Properties: &commontypes.VisualElementProperties{
								Width:  20,
								Height: 20,
							},
						},
					},
				},
				Texts: []*commontypes.Text{
					{
						FontColor: "#FFFFFF",
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: displayTagTitle,
						},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS,
						},
					},
				},
				LeftImgTxtPadding: 8,
			},
		},
		SpentUnits: r.getFiStoreSpentUnits(dynConf, redemption),
		CreatedAt:  redemption.GetOrderTimestamp(),
		CollectedAt: &commontypes.Text{
			FontColor: "#333333",
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: "Collected " + redemption.GetOrderTimestamp().AsTime().Format("Monday, 2 January"),
			},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_BODY_4,
			},
		},
	}
}

func (r *RewardService) getFiStoreSpentUnits(dynConf *genconf.Config, redemption *evrPb.FiStoreRedemption) []*fePb.CollectedOffer_HeaderIconTextComponent {
	offerConfig := dynConf.FiStoreCollectedOffersConfig()
	// assigning fi-point icon for offer catalogs if called after August 1, 2025 (prod)
	headerText := accrualPkg.ReturnApplicableValue(fiCoinsRedemptionValueText, accrualPkg.ReplaceCoinWithPointIfApplicable(fiCoinsRedemptionValueText), redemption.GetCreatedAt(), true).(string)
	imageUrl := accrualPkg.ReturnApplicableValue(offerConfig.FiCoinIconUrl(), offerConfig.FiPointIconUrl(), redemption.GetCreatedAt(), true).(string)

	return []*fePb.CollectedOffer_HeaderIconTextComponent{
		{
			Header: &commontypes.Text{
				FontColor: "#FFFFFF",
				DisplayValue: &commontypes.Text_PlainString{
					PlainString: headerText,
				},
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_OVERLINE_2,
				},
			},
			IconText: &ui.IconTextComponent{
				LeftImgTxtPadding: 2,
				LeftVisualElement: &commontypes.VisualElement{
					Asset: &commontypes.VisualElement_Image_{
						Image: &commontypes.VisualElement_Image{
							Source: &commontypes.VisualElement_Image_Url{
								Url: imageUrl,
							},
							Properties: &commontypes.VisualElementProperties{
								Width:  18,
								Height: 18,
							},
						},
					},
				},
				Texts: []*commontypes.Text{
					commontypes.GetPlainStringText(money.ToDisplayStringInIndianFormatFromFloatValue(float64(redemption.GetSpentFiCoinUnits()), 0)),
				},
			},
		},
		{
			Header: &commontypes.Text{
				FontColor: "#FFFFFF",
				DisplayValue: &commontypes.Text_PlainString{
					PlainString: "CASH SPENT",
				},
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_OVERLINE_2,
				},
			},
			IconText: &ui.IconTextComponent{
				Texts: []*commontypes.Text{
					commontypes.GetPlainStringText(money.ToDisplayStringInIndianFormat(redemption.GetSpentCashUnits(), 0, true)),
				},
			},
		},
	}
}

func (r *RewardService) getDebitCardIdForActor(ctx context.Context, actorId string) (string, error) {
	// fetch debit card id to pass in deeplink to show debit card details in webpage.
	cards, cardFetchErr := r.cardClient.FetchCards(ctx, &provisioning.FetchCardsRequest{
		Actor:            &types.Actor{Id: actorId},
		IssuingBanks:     []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
		CardStates:       []beCardPb.CardState{beCardPb.CardState_CREATED, beCardPb.CardState_ACTIVATED},
		CardTypes:        []beCardPb.CardType{beCardPb.CardType_DEBIT},
		CardNetworkTypes: []beCardPb.CardNetworkType{beCardPb.CardNetworkType_VISA},
		CardForms:        []beCardPb.CardForm{beCardPb.CardForm_PHYSICAL, beCardPb.CardForm_DIGITAL},
		SortedBy:         beCardPb.CardFieldMask_CARD_UPDATED_AT,
	})
	switch rpcErr := epifigrpc.RPCError(cards, cardFetchErr); {
	case rpc.StatusFromError(rpcErr).IsRecordNotFound():
		return "", nil
	case rpcErr != nil:
		logger.Error(ctx, "error while fetching debit card details", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
		return "", rpcErr
	case len(cards.GetCards()) == 0:
		return "", nil
	default:
		return cards.GetCards()[0].GetId(), nil
	}
}

func (r *RewardService) getGiftCardStoreCtaV1(ctx context.Context, actorId string) *fePb.CtaV1 {
	if !r.isUserEligibleForGiftCardStore(ctx, actorId) {
		return nil
	}
	debitCardId, err := r.getDebitCardIdForActor(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error while fetching debit card id", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return nil
	}

	requestMetadata := fmt.Sprintf("vendor:POSHVINE,target_url:%s", "fimoney.poshvine.com/gvms") // prod Url
	deeplinkAction := r.getWebPageWithScreenDetailsScreen(debitCardId != "", debitCardId, "", requestMetadata)

	return &fePb.CtaV1{
		CtaTitle: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: "Get exciting vouchers"},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_HEADLINE_S,
			},
			FontColor: colorFiSnow,
		},
		Action: &fePb.CtaV1_DeeplinkAction{
			DeeplinkAction: deeplinkAction,
		},
		Shadow: &ui.Shadow{
			Height: 4,
			Colour: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{
				BlockColour: "#254e5c",
			}},
		},
		BgColorV2: &widget.BackgroundColour{
			Colour: &widget.BackgroundColour_RadialGradient{
				RadialGradient: &widget.RadialGradient{
					Center: &widget.CenterCoordinates{
						CenterX: 0,
						CenterY: 0,
					},
					OuterRadius: 20,
					Colours:     []string{"#608C9B"},
				},
			},
		},
		LeftIcon: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.s3.ap-south-1.amazonaws.com/rewards/vouchers.png", 48, 48),
	}
}

func (r *RewardService) isUserEligibleForGiftCardStore(ctx context.Context, actorId string) bool {
	// Hard coding segment expression as this CTA is for temporary only.
	segmentExp := "IsMember('e48701fc-28a6-4623-bf28-4929e6020bb2') || IsMember('8a5c3a22-b467-41db-a7aa-1c50c143babc')"
	segmentResp, err := r.segmentClient.IsMemberOfExpressions(ctx, &segmentPb.IsMemberOfExpressionsRequest{
		ActorId:              actorId,
		SegmentIdExpressions: []string{segmentExp},
		LatestBy:             timestamppb.Now(),
	})
	if rpcErr := epifigrpc.RPCError(segmentResp, err); rpcErr != nil {
		logger.Error(ctx, "IsMemberOfExpressions call failed", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
		return false
	}

	return isActorMemberOfSegmentExpr(ctx, segmentResp.GetSegmentExpressionMembershipMap(), segmentExp)
}

func isActorMemberOfSegmentExpr(ctx context.Context, membershipMap map[string]*segmentPb.SegmentExpressionMembership, segmentExpr string) bool {
	segmentExprMembership, ok := membershipMap[segmentExpr]
	if !ok {
		logger.Error(ctx, "segment expression not found in response membership map", zap.String(logger.SEGMENT_ID, segmentExpr))
		return false
	}
	if segmentExprMembership.GetSegmentExpressionStatus() != segmentPb.SegmentExpressionStatus_OK {
		logger.Error(ctx, "error while checking if actor still belongs to this segment expression", zap.String(logger.SEGMENT_ID, segmentExpr))
		return false
	}
	return segmentExprMembership.GetIsActorMember()
}

func (r *RewardService) ForceRetryRewardProcessing(ctx context.Context, req *fePb.ForceRetryRewardProcessingRequest) (*fePb.ForceRetryRewardProcessingResponse, error) {
	// check if fi coins to fi points migration is active
	if accrualPkg.IsFiCoinsToFiPointsMigrationInProgress() {
		return &fePb.ForceRetryRewardProcessingResponse{
			RespHeader: &header.ResponseHeader{
				ErrorView: GetFiCoinsToFiPointsDowntimeErrorView(),
			},
		}, nil
	}

	if req.GetExchangerOrderId() == "" && req.GetRewardId() == "" {
		logger.Error(ctx, "exchanger order id or reward id is required")
		return &fePb.ForceRetryRewardProcessingResponse{RespHeader: &header.ResponseHeader{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("exchanger order id or reward id is required"),
		}}, nil
	}

	if req.GetExchangerOrderId() != "" {
		logger.Error(ctx, "force retry reward processing for exchanger order id is not supported yet", zap.String("exchangerOrderId", req.GetExchangerOrderId()))
		return &fePb.ForceRetryRewardProcessingResponse{RespHeader: &header.ResponseHeader{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("force retry reward processing for exchanger order id is not supported yet"),
		}}, nil
	}

	forceRetryRewardProcessingRes, err := r.rewardsGeneratorClient.ForceRetryRewardProcessing(ctx, &beRewardsPb.ForceRetryRewardProcessingRequest{
		RewardId: req.GetRewardId(),
	})
	if rpcErr := epifigrpc.RPCError(forceRetryRewardProcessingRes, err); rpcErr != nil {
		logger.Error(ctx, "error while forcing retry reward processing", zap.String(logger.REWARD_ID, req.GetRewardId()), zap.Error(rpcErr))
		return &fePb.ForceRetryRewardProcessingResponse{RespHeader: &header.ResponseHeader{
			Status: rpc.StatusInternalWithDebugMsg(rpcErr.Error()),
		}}, nil
	}

	return &fePb.ForceRetryRewardProcessingResponse{RespHeader: &header.ResponseHeader{
		Status: rpc.StatusOk(),
	}}, nil
}

func getSecondCTADetailsForMyRewardsScreen(isFiLiteUser bool) (string, string, *deeplink.Deeplink) {
	if isFiLiteUser {
		return accrualPkg.ReplaceCoinWithPointIfApplicable("Spend Fi-Coins"), "https://epifi-icons.pointz.in/my_rewards_screen_spend_ficoins_icon.png", &deeplink.Deeplink{
			Screen: deeplinkPb.Screen_OFFERS_LANDING_SCREEN,
		}
	}
	return "Refer &\nEarn", "https://epifi-icons.pointz.in/rewards/refer-and-earn.png", &deeplink.Deeplink{
		Screen: deeplinkPb.Screen_REFERRALS_ELIGIBILITY_LANDING_SCREEN,
	}
}

func (r *RewardService) getViewAllCtaV1(ctaText string, deeplink *deeplinkPb.Deeplink, isFeatureHomeDesignEnhancementsEnabled bool) *fePb.CtaV1 {
	action := &fePb.CtaV1_DeeplinkAction{DeeplinkAction: deeplink}
	if isFeatureHomeDesignEnhancementsEnabled {
		return &fePb.CtaV1{
			CtaTitle:     commontypes.GetTextFromStringFontColourFontStyle(ctaText, colorFiGreen, commontypes.FontStyle_OVERLINE_2XS_CAPS),
			Action:       action,
			RightIconUrl: rightChevronGreenUrl,
		}
	}

	return &fePb.CtaV1{
		CtaTitle:     commontypes.GetTextFromStringFontColourFontStyle(ctaText, colorMonochromeLead, commontypes.FontStyle_OVERLINE_XS_CAPS),
		Action:       action,
		RightIconUrl: rightChevronGreyUrl,
	}
}

// GetEarnedRewardsHistory implements the RPC to fetch earned rewards history based on domain ID
func (r *RewardService) GetEarnedRewardsHistory(ctx context.Context, req *fePb.GetEarnedRewardsHistoryRequest) (*fePb.GetEarnedRewardsHistoryResponse, error) {
	// Step 1: Get the domainId enum from the string in request
	domainIdValue, ok := rewardsTypes.EarnedRewardsHistoryDomainId_value[req.GetEarnedRewardsHistoryDomainId()]
	if !ok {
		logger.Error(ctx, "invalid domainId to fetch EarnedRewardsHistory", zap.String(logger.IDENTIFIER_VALUE, req.GetEarnedRewardsHistoryDomainId()))
		return &fePb.GetEarnedRewardsHistoryResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInvalidArgument()}}, nil
	}
	domainIdEnum := rewardsTypes.EarnedRewardsHistoryDomainId(domainIdValue)

	// Step 2: Get the screen data builder factory
	screenBuilder, err := r.ScreenDataBuilderFactory.GetScreenDataBuilder(domainIdEnum)
	if err != nil {
		logger.Error(ctx, "failed to get screen builder", zap.String("domainId", req.GetEarnedRewardsHistoryDomainId()), zap.Error(err))
		return &fePb.GetEarnedRewardsHistoryResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("failed to get screen builder")},
		}, nil
	}

	// Step 3: Use the builder interface method to build the screen
	actorId := req.GetReq().GetAuth().GetActorId()
	screenData, err := screenBuilder.BuildScreenData(ctx, actorId, req.GetPageContext())
	if err != nil {
		logger.Error(ctx, "failed to build earned rewards history screen",
			zap.String(logger.ACTOR_ID_V2, actorId),
			zap.String("domainId", req.GetEarnedRewardsHistoryDomainId()),
			zap.Error(err))
		return &fePb.GetEarnedRewardsHistoryResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternal()}}, nil
	}

	return &fePb.GetEarnedRewardsHistoryResponse{
		RespHeader:  &header.ResponseHeader{Status: rpc.StatusOk()},
		PageContext: screenData.GetPageContextResponse(),
		TopBanner:   screenData.GetTopBanner(),
		HeaderView:  screenData.GetHeaderView(),
		CurrentMonthRewardsProcessingTimelineInfo: screenData.GetCurrentMonthRewardView(),
		MonthlyEarnedRewardsViews:                 screenData.GetMonthlyEarnedRewardsViews(),
	}, nil
}

func (r *RewardService) getRedemptionValueComponent(headerText string, iconUrl string, value float32) *ui.VerticalKeyValuePair {
	return &ui.VerticalKeyValuePair{
		Title: ui.NewITC().
			WithTexts(commontypes.GetTextFromStringFontColourFontStyleFontAlignment(headerText, colorFiSnow, commontypes.FontStyle_SUBTITLE_2XS, commontypes.Text_ALIGNMENT_LEFT)),
		Value: ui.NewITC().
			WithTexts(commontypes.GetTextFromStringFontColourFontStyleFontAlignment(money.ToDisplayStringInIndianFormatFromFloatValue(float64(value), 0), colorFiSnow, commontypes.FontStyle_NUMBERS_7, commontypes.Text_ALIGNMENT_CENTER)).
			WithLeftVisualElementUrlHeightAndWidth(iconUrl, 18, 18),
		VerticalPaddingBtwTitleValue: 4,
		HAlignment:                   ui.VerticalKeyValuePairHAlignment_VERTICAL_KEY_VALUE_PAIR_H_ALIGNMENT_LEFT,
	}
}
