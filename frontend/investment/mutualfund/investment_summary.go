package mutualfund

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/async/goroutine"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/money"

	feHdr "github.com/epifi/gamma/api/frontend/header"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	invFeEvents "github.com/epifi/gamma/frontend/events/investment/mutualfund"
	"github.com/epifi/gamma/rewards/helper"

	types "github.com/epifi/gamma/api/typesv2"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	fePb "github.com/epifi/gamma/api/frontend/investment/mutualfund"
	beSvc "github.com/epifi/gamma/api/investment/mutualfund/catalog"
)

func (s *Service) InvestmentSummary(ctx context.Context, feReq *fePb.InvestmentSummaryRequest) (*fePb.InvestmentSummaryResponse, error) {
	actorId := feReq.GetReq().GetAuth().GetActorId()
	feResp := &fePb.InvestmentSummaryResponse{
		RespHeader: &feHdr.ResponseHeader{
			Status: rpc.StatusOk(),
		},
	}

	// checking if mitc consent screen is required, if required will show popup deeplink to record consent
	stepRes, stepErr := s.wealthOnboardingClient.GetNextOnboardingStep(ctx, &woPb.GetNextOnboardingStatusRequest{
		ActorId:        actorId,
		WealthFlow:     woPb.WealthFlow_WEALTH_FLOW_INVESTMENT,
		OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
		EntryPoint:     woPb.OnboardingStepEntrypoint_ONBOARDING_STEP_ENTRYPOINT_INVEST_SUMMARY,
	})
	if rpcErr := epifigrpc.RPCError(stepRes, stepErr); rpcErr != nil {
		logger.Error(ctx, "failed to additional step details for step", zap.String(logger.WEALTH_ONB_STEP_NAME, woPb.OnboardingStep_ONBOARDING_STEP_USER_MITC_CONSENT.String()), zap.Error(rpcErr))
		feResp.RespHeader.Status = rpc.StatusInternalWithDebugMsg(rpcErr.Error())
		return feResp, nil
	}
	if stepRes.GetNextStep() != nil {
		return &fePb.InvestmentSummaryResponse{
			RespHeader: &feHdr.ResponseHeader{
				Status: rpc.StatusOk(),
			},
			PopupDeeplink: stepRes.GetNextStep(),
		}, nil
	}

	beResp, beErr := s.catalogClient.GetInvestmentSummaryInfo(ctx, &beSvc.GetInvestmentSummaryInfoRequest{
		ActorId: actorId,
	})
	if err2 := epifigrpc.RPCError(beResp, beErr); err2 != nil {
		logger.Error(ctx, "Error while getting investment summary for actor", zap.String(logger.ACTOR_ID, feReq.GetReq().GetAuth().GetActorId()), zap.Error(err2))
		feResp.RespHeader.Status = rpc.StatusInternalWithDebugMsg(err2.Error())
		return feResp, nil
	}
	popErr := populateFeResponse(feResp, beResp)
	if popErr != nil {
		logger.Error(ctx, "Error while populating investment summary response",
			zap.String(logger.ACTOR_ID, feReq.GetReq().GetAuth().GetActorId()), zap.Error(popErr))
		feResp.RespHeader.Status = rpc.StatusInternalWithDebugMsg(popErr.Error())
		return feResp, nil
	}

	s.portfolioPageRudderEvent(ctx, feReq, feResp)

	resp, popErr := s.populateInvestmentLandingComponents(ctx, feReq, feResp, beResp)
	if popErr != nil {
		logger.Error(ctx, "Error while populating investment landing components",
			zap.String(logger.ACTOR_ID, feReq.GetReq().GetAuth().GetActorId()), zap.Error(popErr))
		feResp.RespHeader.Status = rpc.StatusInternalWithDebugMsg(popErr.Error())
		return feResp, nil
	}

	return resp, nil
}

func (s *Service) portfolioPageRudderEvent(ctx context.Context, req *fePb.InvestmentSummaryRequest, resp *fePb.InvestmentSummaryResponse) {

	portfolioAmount := helper.ConvertMoneyToValue(resp.CurrentValue.GetBeMoney())
	investmentSummaryState := resp.GetSummaryState()
	isAlreadyInvested := hasUserAlreadyInvested(investmentSummaryState)

	goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
		s.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), invFeEvents.NewPortfolioPageTriggered(
			req.GetReq().GetAuth().GetActorId(),
			req.GetReq().GetSessionId(),
			req.GetReq().GetAuth().GetDevice().GetAppVersion(),
			req.GetReq().GetAuth().GetDevice().GetPlatform(),
			time.Now(),
			portfolioAmount,
			isAlreadyInvested))
	})
}

func populateFeResponse(feResp *fePb.InvestmentSummaryResponse, beResp *beSvc.GetInvestmentSummaryInfoResponse) error {
	feResp.InvestedValue = types.GetFromBeMoney(beResp.GetInvestmentSummary().GetTotalInvestedValue())
	feResp.CurrentValue = types.GetFromBeMoney(beResp.GetInvestmentSummary().GetCurrentValue())
	feResp.GrowthPercent = beResp.GetInvestmentSummary().GetGrowthPercentage()
	// pendOrdersBool will be true if there are either non-terminal buy or sell orders.
	pendOrdersBool := beResp.GetInvestmentSummary().GetPendingOrdersPresent()

	inProgressBuyAmount := beResp.InvestmentSummary.InProgressBuyOrderAmount
	if inProgressBuyAmount != nil && !money.IsZero(inProgressBuyAmount) {
		feResp.SummaryState = fePb.InvestmentSummaryState_INVESTMENT_SUMMARY_STATE_PROCESSING
		feResp.StatusText = fmt.Sprintf(InvSummaryStateTextProcessing, money.ToDisplayStringWithPrecision(inProgressBuyAmount, 0))
	} else {
		feResp.SummaryState = fePb.InvestmentSummaryState_INVESTMENT_SUMMARY_STATE_NO_OP
	}

	// Decide on info text on the basis of pending orders and invested value
	switch {
	case pendOrdersBool:
		feResp.InfoText = InvSummaryInfoPendingInvestments
	case money.IsZero(beResp.GetInvestmentSummary().GetTotalInvestedValue()):
		feResp.InfoText = InvSummaryInfoNoInvestments
	default:
		// Pick later of Nav and Folio update time as time to display
		navTime := beResp.GetInvestmentSummary().GetNavUpdatedAt().AsTime()
		folTime := beResp.GetInvestmentSummary().GetLatestFolioUpdatedAt().AsTime()
		timeZoneLocation, err := time.LoadLocation(ISTTimeZoneLocation)
		if err != nil {
			return fmt.Errorf("error while trying to load IST time zone: %w", err)
		}
		if navTime.After(folTime) {
			feResp.InfoText = fmt.Sprintf(InvSummaryInfoDefault, navTime.In(timeZoneLocation).Format("02 January 3:04 PM"))
		} else {
			feResp.InfoText = fmt.Sprintf(InvSummaryInfoDefault, folTime.In(timeZoneLocation).Format("02 January 3:04 PM"))
		}
	}
	return nil
}

// if investment summary state is InvestmentSummaryState_INVESTMENT_SUMMARY_STATE_NO_OP,
// InvestmentSummaryState_INVESTMENT_SUMMARY_STATE_PROCESSING or InvestmentSummaryState_INVESTMENT_SUMMARY_STATE_NEW
// means, user has already invested
func hasUserAlreadyInvested(investmentSummaryState fePb.InvestmentSummaryState) bool {
	switch investmentSummaryState {
	case fePb.InvestmentSummaryState_INVESTMENT_SUMMARY_STATE_NO_OP:
		return true
	case fePb.InvestmentSummaryState_INVESTMENT_SUMMARY_STATE_PROCESSING:
		return true
	case fePb.InvestmentSummaryState_INVESTMENT_SUMMARY_STATE_NEW:
		return true
	default:
		return false
	}
}
