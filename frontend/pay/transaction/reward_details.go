package transaction

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/frontend/app/apputils"

	"github.com/epifi/gamma/frontend/pkg/common"
	"github.com/epifi/gamma/frontend/pkg/featureflags"
	"github.com/epifi/gamma/pkg/accrual"
	"github.com/epifi/gamma/pkg/rewards"

	"github.com/epifi/be-common/pkg/money"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/gamma/pkg/feature/release"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	deeplink2 "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/pay/transaction"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	tieringPb "github.com/epifi/gamma/api/tiering"
	"github.com/epifi/gamma/api/tiering/external"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/ui"

	"github.com/shopspring/decimal"
)

func (s *Service) getRewardDetailsSection(ctx context.Context, actorId string, refId string, orderExecutionTime *timestampPb.Timestamp) (*transaction.RewardDetails, error) {
	// flag to enable rewards details section on order receipt page
	releaseConstraint := release.NewCommonConstraintData(typesPb.Feature_FEATURE_REWARD_DETAILS_IN_ORDER_RECEIPT).WithActorId(actorId)
	isEligible, releaseErr := s.releaseEvaluator.Evaluate(ctx, releaseConstraint)
	if releaseErr != nil {
		logger.Error(ctx, "error in release evaluator for feature FEATURE_REWARD_DETAILS_IN_ORDER_RECEIPT", zap.Error(releaseErr))
		return nil, releaseErr
	}
	if isEligible {
		// fetch all rewards and projections generated for this order
		rewardsRes, err := s.rewardsGeneratorClient.GetAllRewardsAndProjections(ctx, &rewardsPb.GetAllRewardsAndProjectionRequest{
			ActorId:    actorId,
			ActionType: rewardsPb.CollectedDataType_ORDER,
			RefIds:     []string{refId},
		})
		if rpcErr := epifigrpc.RPCError(rewardsRes, err); rpcErr != nil {
			logger.Error(ctx, "error while fetching GetAllRewardsAndProjections for order receipt page", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
			return nil, rpcErr
		}

		rewardEntities, ok := rewardsRes.GetRefIdToRewardEntitiesMap()[refId]
		if !ok {
			// if no reward entities present for give ref id, no need to show the rewards details section.
			return nil, nil
		}

		if len(rewardEntities.GetRewardEntities()) == 0 {
			// if no reward entities present, no need to show the rewards details section.
			return nil, nil
		}

		res, err := s.tieringClient.GetTierAtTime(ctx, &tieringPb.GetTierAtTimeRequest{
			ActorId:       actorId,
			TierTimestamp: orderExecutionTime,
		})
		if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
			logger.Error(ctx, "error while fetching GetTierAtTime for order receipt page", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
			return nil, rpcErr
		}

		var earnedRewardUnits, actualizedProjectedRewardUnits, projectedRewardUnits []*rewardsPb.RewardOptionMinimal
		if time.Now().After(accrual.GetFiCoinsToFiPointsMigrationTime()) {
			earnedRewardUnits, actualizedProjectedRewardUnits, projectedRewardUnits = s.getRewardAggregatesV2(rewardEntities.GetRewardEntities())
		} else {
			earnedRewardUnits, projectedRewardUnits = s.getRewardAggregates(rewardEntities.GetRewardEntities())
			actualizedProjectedRewardUnits = []*rewardsPb.RewardOptionMinimal{}
		}

		// flag to enable fi-coins to fi-points post migration
		isFeatureFiCoinsToFiPointsPostMigrationPhaseEnabled := featureflags.IsFeatureFiCoinsToFiPointsPostMigrationPhaseEnabled(ctx, &featureflags.IsFeatureFiCoinsToFiPointsPostMigrationPhaseEnabledRequest{
			ActorId: actorId,
			ExternalDeps: &common.ExternalDependencies{
				Evaluator: s.releaseEvaluator,
			},
		})

		// summary of the rewards of the transaction
		summary := s.getRewardSummarySection(ctx, earnedRewardUnits, projectedRewardUnits, actualizedProjectedRewardUnits, orderExecutionTime, res.GetTierInfo().GetTier(), actorId, isFeatureFiCoinsToFiPointsPostMigrationPhaseEnabled)
		// bottom sheet having info on the rewards for the transaction when reward summary section is clicked
		bottomSheet := s.getRewardDetailsBottomSheet(ctx, earnedRewardUnits, projectedRewardUnits, actualizedProjectedRewardUnits, orderExecutionTime, actorId, isFeatureFiCoinsToFiPointsPostMigrationPhaseEnabled)

		// if no reward values exists then don't show the reward details section in order receipt page.
		if len(summary.GetRewardValues()) == 0 {
			return nil, nil
		}

		return &transaction.RewardDetails{
			Summary:     summary,
			BottomSheet: bottomSheet,
		}, nil
	}
	return nil, nil
}

func (s *Service) getRewardAggregatesV2(rewardEntities []*rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntity) ([]*rewardsPb.RewardOptionMinimal, []*rewardsPb.RewardOptionMinimal, []*rewardsPb.RewardOptionMinimal) {
	var (
		aggregatesOfEarnedFiCoinsUnits               float32
		aggregatesOfEarnedCashbackUnits              float32
		aggregatesOfProjectedFiCoinsUnits            float32
		aggregatesOfProjectedCashbackUnits           float32
		aggregatesOfActualizedProjectedFiCoinsUnits  float32
		aggregatesOfActualizedProjectedCashbackUnits float32
	)
	for _, rewardEntity := range rewardEntities {
		switch rewardEntity.GetRewardEntityType() {
		case rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_GENERATED_REWARD:
			for _, rewardOption := range rewardEntity.GetRewardOptions() {
				switch rewardOption.GetRewardType() {
				case rewardsPb.RewardType_FI_COINS:
					aggregatesOfEarnedFiCoinsUnits += rewardOption.GetUnits()
				case rewardsPb.RewardType_CASH:
					aggregatesOfEarnedCashbackUnits += rewardOption.GetUnits()
				}
			}
		case rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_ACTUALISED_PROJECTED_REWARD:
			for _, rewardOption := range rewardEntity.GetRewardOptions() {
				switch rewardOption.GetRewardType() {
				case rewardsPb.RewardType_FI_COINS:
					aggregatesOfActualizedProjectedFiCoinsUnits += rewardOption.GetUnits()
				case rewardsPb.RewardType_CASH:
					aggregatesOfActualizedProjectedCashbackUnits += rewardOption.GetUnits()
				}
			}
		case rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_PROJECTED_REWARD:
			for _, rewardOption := range rewardEntity.GetRewardOptions() {
				switch rewardOption.GetRewardType() {
				case rewardsPb.RewardType_FI_COINS:
					aggregatesOfProjectedFiCoinsUnits += rewardOption.GetUnits()
				case rewardsPb.RewardType_CASH:
					aggregatesOfProjectedCashbackUnits += rewardOption.GetUnits()
				}
			}
		}
	}

	earnedRewardUnits := []*rewardsPb.RewardOptionMinimal{
		{
			RewardType: rewardsPb.RewardType_FI_COINS,
			Units:      aggregatesOfEarnedFiCoinsUnits,
		},
		{
			RewardType: rewardsPb.RewardType_CASH,
			Units:      aggregatesOfEarnedCashbackUnits,
		},
	}

	actualizedProjectedRewardUnits := []*rewardsPb.RewardOptionMinimal{
		{
			RewardType: rewardsPb.RewardType_FI_COINS,
			Units:      aggregatesOfActualizedProjectedFiCoinsUnits,
		},
		{
			RewardType: rewardsPb.RewardType_CASH,
			Units:      aggregatesOfActualizedProjectedCashbackUnits,
		},
	}

	// if both earned and projected reward units are zero, then return empty arrays.
	// this handling is under the assumption that only one projection is generated against an order,
	// and we pick cashback projection if it exists, else we pick fi-coins projection.
	projectedRewardUnits := make([]*rewardsPb.RewardOptionMinimal, 0)
	if aggregatesOfProjectedCashbackUnits != 0 {
		projectedRewardUnits = append(projectedRewardUnits, &rewardsPb.RewardOptionMinimal{
			RewardType: rewardsPb.RewardType_CASH,
			Units:      aggregatesOfProjectedCashbackUnits,
		})
	} else if aggregatesOfProjectedFiCoinsUnits != 0 {
		projectedRewardUnits = append(projectedRewardUnits, &rewardsPb.RewardOptionMinimal{
			RewardType: rewardsPb.RewardType_FI_COINS,
			Units:      aggregatesOfProjectedFiCoinsUnits,
		})
	}

	// sort in descending order of units
	sort.Slice(earnedRewardUnits, func(i, j int) bool {
		return earnedRewardUnits[i].GetUnits() > earnedRewardUnits[j].GetUnits()
	})
	sort.Slice(actualizedProjectedRewardUnits, func(i, j int) bool {
		return actualizedProjectedRewardUnits[i].GetUnits() > actualizedProjectedRewardUnits[j].GetUnits()
	})
	sort.Slice(projectedRewardUnits, func(i, j int) bool {
		return projectedRewardUnits[i].GetUnits() > projectedRewardUnits[j].GetUnits()
	})

	return earnedRewardUnits, actualizedProjectedRewardUnits, projectedRewardUnits
}

func (s *Service) getRewardAggregates(rewardEntities []*rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntity) ([]*rewardsPb.RewardOptionMinimal, []*rewardsPb.RewardOptionMinimal) {
	var (
		aggregatesOfEarnedFiCoinsUnits     float32
		aggregatesOfEarnedCashbackUnits    float32
		aggregatesOfProjectedFiCoinsUnits  float32
		aggregatesOfProjectedCashbackUnits float32
	)
	for _, rewardEntity := range rewardEntities {
		switch rewardEntity.GetRewardEntityType() {
		case rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_GENERATED_REWARD, rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_ACTUALISED_PROJECTED_REWARD:
			for _, rewardOption := range rewardEntity.GetRewardOptions() {
				switch rewardOption.GetRewardType() {
				case rewardsPb.RewardType_FI_COINS:
					aggregatesOfEarnedFiCoinsUnits += rewardOption.GetUnits()
				case rewardsPb.RewardType_CASH:
					aggregatesOfEarnedCashbackUnits += rewardOption.GetUnits()
				}
			}
		case rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_PROJECTED_REWARD:
			for _, rewardOption := range rewardEntity.GetRewardOptions() {
				switch rewardOption.GetRewardType() {
				case rewardsPb.RewardType_FI_COINS:
					aggregatesOfProjectedFiCoinsUnits += rewardOption.GetUnits()
				case rewardsPb.RewardType_CASH:
					aggregatesOfProjectedCashbackUnits += rewardOption.GetUnits()
				}
			}
		}
	}
	earnedRewardUnits := []*rewardsPb.RewardOptionMinimal{
		{
			RewardType: rewardsPb.RewardType_FI_COINS,
			Units:      aggregatesOfEarnedFiCoinsUnits,
		},
		{
			RewardType: rewardsPb.RewardType_CASH,
			Units:      aggregatesOfEarnedCashbackUnits,
		},
	}
	// if both earned and projected reward units are zero, then return empty arrays.
	// this handling is under the assumption that only one projection is generated against an order,
	// and we pick cashback projection if it exists, else we pick fi-coins projection.
	projectedRewardUnits := make([]*rewardsPb.RewardOptionMinimal, 0)
	if aggregatesOfProjectedCashbackUnits != 0 {
		projectedRewardUnits = append(projectedRewardUnits, &rewardsPb.RewardOptionMinimal{
			RewardType: rewardsPb.RewardType_CASH,
			Units:      aggregatesOfProjectedCashbackUnits,
		})
	} else if aggregatesOfProjectedFiCoinsUnits != 0 {
		projectedRewardUnits = append(projectedRewardUnits, &rewardsPb.RewardOptionMinimal{
			RewardType: rewardsPb.RewardType_FI_COINS,
			Units:      aggregatesOfProjectedFiCoinsUnits,
		})
	}

	// sort in descending order of units
	sort.Slice(earnedRewardUnits, func(i, j int) bool {
		return earnedRewardUnits[i].GetUnits() > earnedRewardUnits[j].GetUnits()
	})
	sort.Slice(projectedRewardUnits, func(i, j int) bool {
		return projectedRewardUnits[i].GetUnits() > projectedRewardUnits[j].GetUnits()
	})

	return earnedRewardUnits, projectedRewardUnits
}

func (s *Service) getRewardSummarySection(ctx context.Context, earnedRewardUnits, projectedRewardUnits, actualizedProjectedRewardUnits []*rewardsPb.RewardOptionMinimal, orderExecutionTime *timestampPb.Timestamp, evaluatedTier external.Tier, actorId string, isFeatureFiCoinsToFiPointsPostMigrationPhaseEnabled bool) *transaction.RewardSummarySection {
	rewardsValues := make([]*transaction.RewardSummarySection_EarnedValueChip, 0)

	// Handle non-projected rewards with transition logic for July
	if time.Now().After(accrual.GetFiCoinsToFiPointsMigrationTime()) {
		s.addNonProjectedRewardsWithTransitionLogic(&rewardsValues, nil, earnedRewardUnits, actualizedProjectedRewardUnits, orderExecutionTime, true)
	} else {
		actualEarnedValueChips := s.getRewardEarnedValueChips(earnedRewardUnits, false, orderExecutionTime)
		rewardsValues = append(rewardsValues, actualEarnedValueChips...)
	}

	// Handle projected rewards (washed out)
	projectedEarnedValueChips := s.getRewardEarnedValueChips(projectedRewardUnits, true, orderExecutionTime)
	rewardsValues = append(rewardsValues, projectedEarnedValueChips...)

	rewardSummaryConf := s.conf.RewardDetailsForOrderReceiptConfig().RewardSummarySection()
	subTitle := fmt.Sprintf(rewardSummaryConf.SubTitle(), strings.Title(strings.ToLower(getUserTier(evaluatedTier))))
	minlen := min(len(rewardsValues), 3)

	// footer component in the rewards summary section which announces the conversion of fi-coins to fi-points (post-conversion)
	var footerInfoSection *ui.IconTextComponent
	if apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.conf.RewardFooterInfoSectionConfig()) && isFeatureFiCoinsToFiPointsPostMigrationPhaseEnabled {
		footerInfoSection = &ui.IconTextComponent{
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BackgroundColour: widget.GetLinearGradientBackgroundColour(90, []*widget.ColorStop{
					{
						Color:          "#FCEEDF",
						StopPercentage: 0,
					},
					{
						Color:          "#FDDDAA",
						StopPercentage: 100,
					},
				}),
				TopPadding:    rewardSummaryConf.TopPadding(),
				BottomPadding: rewardSummaryConf.BottomPadding(),
			},
			LeftVisualElement:  commontypes.GetVisualElementFromUrlHeightAndWidth(rewardSummaryConf.LeftElementForFooterInfo(), 16, 16),
			RightVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(rewardSummaryConf.RightElementForFooterInfo(), 16, 16),
			LeftImgTxtPadding:  rewardSummaryConf.LeftImgTxtPadding(),
			Texts: []*commontypes.Text{
				{
					FontColor: rewardSummaryConf.FontColorForFooterInfo(),
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: rewardSummaryConf.TextForFooterInfo(),
					},
					FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
				},
			},
			Deeplink: rewards.GetFiCoinsFiPointsKnowMoreButtonDeeplink(ctx, actorId, s.ffAccountingClient, s.ffClientv2, s.tieringClient, false),
		}
	}

	return &transaction.RewardSummarySection{
		Title: &commontypes.Text{
			FontColor: "#333333",
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: rewardSummaryConf.Title(),
			},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_BODY_S,
			},
		},
		SubTitle: &commontypes.Text{
			FontColor: "#A4A4A4",
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: subTitle,
			},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_BODY_XS,
			},
		},
		RewardValues:            rewardsValues[:minlen], // show only top 3 or fewer values.
		FooterInfoTextComponent: footerInfoSection,
	}
}

func getUserTier(evaluatedTier external.Tier) string {
	switch evaluatedTier {
	case external.Tier_TIER_FI_REGULAR:
		return "REGULAR"
	case external.Tier_TIER_FI_BASIC:
		return "STANDARD"
	case external.Tier_TIER_FI_SALARY_LITE:
		return "SALARY LITE"
	case external.Tier_TIER_FI_AA_SALARY, external.Tier_TIER_FI_AA_SALARY_BAND_1, external.Tier_TIER_FI_AA_SALARY_BAND_2, external.Tier_TIER_FI_AA_SALARY_BAND_3:
		return "PRIME"
	default:
		return strings.TrimPrefix(evaluatedTier.String(), "TIER_FI_")
	}
}

func (s *Service) getRewardEarnedValueChips(rewardUnits []*rewardsPb.RewardOptionMinimal, showWashedOutChip bool, orderExecutionTime *timestampPb.Timestamp) []*transaction.RewardSummarySection_EarnedValueChip {
	var (
		res      []*transaction.RewardSummarySection_EarnedValueChip
		deeplink *deeplink2.Deeplink
	)

	// for grey out chip, don't send any deeplink
	if !showWashedOutChip {
		deeplink = &deeplink2.Deeplink{
			Screen: deeplink2.Screen_MY_REWARDS_SCREEN,
		}
	}
	for _, reward := range rewardUnits {
		// don't show chip if units are equal to zero.
		if reward.GetUnits() == 0 {
			continue
		}
		var iconUrl string
		switch reward.GetRewardType() {
		case rewardsPb.RewardType_FI_COINS:
			if showWashedOutChip {
				// if feature flag is enabled  and order time is after July 1 2025, projected rewards will be in fi-points
				if time.Now().After(accrual.GetFiCoinsToFiPointsMigrationTime()) && orderExecutionTime.AsTime().After(accrual.GetFiCoinsToFiPointsMigrationTime().AddDate(0, -1, 0)) {
					iconUrl = s.conf.RewardDetailsForOrderReceiptConfig().FiPointsSymbolIconUrl()
				} else {
					iconUrl = s.conf.RewardDetailsForOrderReceiptConfig().ProjectedFiCoinSymbolIconUrl()
				}
			} else {
				iconUrl = s.conf.RewardDetailsForOrderReceiptConfig().FiCoinSymbolIconUrl()
			}
		case rewardsPb.RewardType_CASH:
			if showWashedOutChip {
				iconUrl = s.conf.RewardDetailsForOrderReceiptConfig().ProjectedRupeeSymbolIconUrl()
			} else {
				iconUrl = s.conf.RewardDetailsForOrderReceiptConfig().RupeeSymbolIconUrl()
			}
		default:
			continue
		}
		units := money.ParseFloat(float64(reward.GetUnits()), money.RupeeCurrencyCode)
		unitsInString := money.ToDisplayStringWithSuffixAndPrecisionV2(units, false, true, 1, false, money.IndianNumberSystem)

		res = append(res, &transaction.RewardSummarySection_EarnedValueChip{
			Value: &ui.IconTextComponent{
				ContainerProperties: &ui.IconTextComponent_ContainerProperties{
					BgColor:       "#EFF2F6",
					CornerRadius:  14,
					LeftPadding:   8,
					RightPadding:  8,
					TopPadding:    8,
					BottomPadding: 8,
				},
				LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(iconUrl, 18, 18),
				Texts: []*commontypes.Text{
					{
						FontColor: "#313234",
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: unitsInString,
						},
						FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
					},
				},
				Deeplink: deeplink,
			},
			ShowWashedOutChip: showWashedOutChip,
		})
	}

	return res
}

// addNonProjectedRewardsWithTransitionLogic handles the July transition period where rewards can be mixed types
func (s *Service) addNonProjectedRewardsWithTransitionLogic(rewardsValues *[]*transaction.RewardSummarySection_EarnedValueChip, rewardDetailsCards *[]*transaction.RewardDetailsBottomSheet_RewardDetailsRow, earnedRewardUnits, actualizedProjectedRewardUnits []*rewardsPb.RewardOptionMinimal, orderExecutionTime *timestampPb.Timestamp, isFromRewardSummarySection bool) {
	orderTime := orderExecutionTime.AsTime()

	// Determine what period we're in
	isBeforeJuly := orderTime.Before(accrual.GetFiCoinsToFiPointsMigrationTime().AddDate(0, -1, 0))
	isAfterJuly := orderTime.After(accrual.GetFiCoinsToFiPointsMigrationTime())

	var totalFiCoinsUnits, totalFiPointsUnits, totalCashUnits float32

	switch {
	case isBeforeJuly:
		// Before July 1st: Everything is fi-coins
		for _, reward := range earnedRewardUnits {
			if reward.GetRewardType() == rewardsPb.RewardType_FI_COINS {
				totalFiCoinsUnits += reward.GetUnits()
			} else if reward.GetRewardType() == rewardsPb.RewardType_CASH {
				totalCashUnits += reward.GetUnits()
			}
		}
		for _, reward := range actualizedProjectedRewardUnits {
			if reward.GetRewardType() == rewardsPb.RewardType_FI_COINS {
				totalFiCoinsUnits += reward.GetUnits()
			} else if reward.GetRewardType() == rewardsPb.RewardType_CASH {
				totalCashUnits += reward.GetUnits()
			}
		}
	case isAfterJuly:
		// After July 31st: Everything is fi-points
		for _, reward := range earnedRewardUnits {
			if reward.GetRewardType() == rewardsPb.RewardType_FI_COINS {
				totalFiPointsUnits += reward.GetUnits()
			} else if reward.GetRewardType() == rewardsPb.RewardType_CASH {
				totalCashUnits += reward.GetUnits()
			}
		}
		for _, reward := range actualizedProjectedRewardUnits {
			if reward.GetRewardType() == rewardsPb.RewardType_FI_COINS {
				totalFiPointsUnits += reward.GetUnits()
			} else if reward.GetRewardType() == rewardsPb.RewardType_CASH {
				totalCashUnits += reward.GetUnits()
			}
		}
	default:
		// During July
		// Generated (earned) rewards: fi-coins
		for _, reward := range earnedRewardUnits {
			if reward.GetRewardType() == rewardsPb.RewardType_FI_COINS {
				totalFiCoinsUnits += reward.GetUnits()
			} else if reward.GetRewardType() == rewardsPb.RewardType_CASH {
				totalCashUnits += reward.GetUnits()
			}
		}

		// Actualized rewards: fi-points (after August, everything will be actualized to fi-points)
		for _, reward := range actualizedProjectedRewardUnits {
			if reward.GetRewardType() == rewardsPb.RewardType_FI_COINS {
				totalFiPointsUnits += reward.GetUnits()
			} else if reward.GetRewardType() == rewardsPb.RewardType_CASH {
				totalCashUnits += reward.GetUnits()
			}
		}
	}

	if isFromRewardSummarySection {
		if totalFiCoinsUnits != 0 {
			fiCoinsChip := s.createRewardChip(totalFiCoinsUnits, false, false)
			*rewardsValues = append(*rewardsValues, fiCoinsChip)
		}

		if totalFiPointsUnits != 0 {
			fiPointsChip := s.createRewardChip(totalFiPointsUnits, true, false)
			*rewardsValues = append(*rewardsValues, fiPointsChip)
		}

		if totalCashUnits != 0 {
			cashChip := s.createRewardChip(totalCashUnits, false, true)
			*rewardsValues = append(*rewardsValues, cashChip)
		}
	} else {
		if totalFiCoinsUnits != 0 {
			*rewardDetailsCards = append(*rewardDetailsCards, s.getRewardDetailsRow(s.conf.RewardDetailsForOrderReceiptConfig().FiCoinsEarnedIconUrl(), "Fi-Coins", s.conf.RewardDetailsForOrderReceiptConfig().FiCoinSymbolIconUrl(), totalFiCoinsUnits, false, "", ""))
		}

		if totalFiPointsUnits != 0 {
			*rewardDetailsCards = append(*rewardDetailsCards, s.getRewardDetailsRow(s.conf.RewardDetailsForOrderReceiptConfig().ProjectedFiPointsSymbolIconUrl(), "Fi-Points", s.conf.RewardDetailsForOrderReceiptConfig().FiPointsSymbolIconUrl(), totalFiPointsUnits, false, "", ""))
		}

		if totalCashUnits != 0 {
			*rewardDetailsCards = append(*rewardDetailsCards, s.getRewardDetailsRow(s.conf.RewardDetailsForOrderReceiptConfig().CashbackEarnedIconUrl(), "Cashback", s.conf.RewardDetailsForOrderReceiptConfig().RupeeSymbolIconUrl(), totalCashUnits, false, "", ""))
		}
	}
}

// createRewardChip creates a reward chip for earned rewards
func (s *Service) createRewardChip(units float32, isFiPoints bool, isCashReward bool) *transaction.RewardSummarySection_EarnedValueChip {
	var iconUrl string
	switch {
	case isFiPoints:
		iconUrl = s.conf.RewardDetailsForOrderReceiptConfig().FiPointsSymbolIconUrl()
	case isCashReward:
		iconUrl = s.conf.RewardDetailsForOrderReceiptConfig().RupeeSymbolIconUrl()
	default:
		iconUrl = s.conf.RewardDetailsForOrderReceiptConfig().FiCoinSymbolIconUrl()
	}

	deeplink := &deeplink2.Deeplink{
		Screen: deeplink2.Screen_MY_REWARDS_SCREEN,
	}

	unitsAsMoney := money.ParseFloat(float64(units), money.RupeeCurrencyCode)
	unitsInString := money.ToDisplayStringWithSuffixAndPrecisionV2(unitsAsMoney, false, true, 1, false, money.IndianNumberSystem)

	return &transaction.RewardSummarySection_EarnedValueChip{
		Value: &ui.IconTextComponent{
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:       "#EFF2F6",
				CornerRadius:  14,
				LeftPadding:   8,
				RightPadding:  8,
				TopPadding:    8,
				BottomPadding: 8,
			},
			LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(iconUrl, 18, 18),
			Texts: []*commontypes.Text{
				{
					FontColor: "#313234",
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: unitsInString,
					},
					FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
				},
			},
			Deeplink: deeplink,
		},
		ShowWashedOutChip: false,
	}
}

// nolint: dupl, funlen
func (s *Service) getRewardDetailsBottomSheet(ctx context.Context, earnedRewardUnits, projectedRewardUnits, actualizedProjectedRewardUnits []*rewardsPb.RewardOptionMinimal, orderExecutionTime *timestampPb.Timestamp, actorId string, isFeatureFiCoinsToFiPointsPostMigrationPhaseEnabled bool) *transaction.RewardDetailsBottomSheet {
	var (
		rewardDetailsCards  []*transaction.RewardDetailsBottomSheet_RewardDetailsRow
		txnType             string
		bottomSheetInfoText = s.conf.RewardDetailsForOrderReceiptConfig().RewardDetailsBottomSheet().ActualizedRewardTncBottomSheetInfoText()
	)
	// Handle non-projected rewards with July transition logic for bottom sheet
	if time.Now().After(accrual.GetFiCoinsToFiPointsMigrationTime()) {
		s.addNonProjectedRewardsWithTransitionLogic(nil, &rewardDetailsCards, earnedRewardUnits, actualizedProjectedRewardUnits, orderExecutionTime, false)
	} else {
		// Fallback to original logic - combine all non-projected rewards
		for _, val := range earnedRewardUnits {
			if val.GetUnits() == 0 {
				continue
			}
			switch val.GetRewardType() {
			case rewardsPb.RewardType_FI_COINS:
				rewardDetailsCards = append(rewardDetailsCards, s.getRewardDetailsRow(s.conf.RewardDetailsForOrderReceiptConfig().FiCoinsEarnedIconUrl(), "Fi-Coins", s.conf.RewardDetailsForOrderReceiptConfig().FiCoinSymbolIconUrl(), val.GetUnits(), false, "", ""))
			case rewardsPb.RewardType_CASH:
				rewardDetailsCards = append(rewardDetailsCards, s.getRewardDetailsRow(s.conf.RewardDetailsForOrderReceiptConfig().CashbackEarnedIconUrl(), "Cashback", s.conf.RewardDetailsForOrderReceiptConfig().RupeeSymbolIconUrl(), val.GetUnits(), false, "", ""))
			default:
				continue
			}
		}
	}

	// show projected month as next consecutive month of order execution time.
	projectedMonth := orderExecutionTime.AsTime().In(datetime.IST).AddDate(0, 1, 0).Format("Jan")
	for _, val := range projectedRewardUnits {
		switch {
		case val.GetUnits() == 0:
			continue
		case val.GetUnits() > 0:
			txnType = "credit"
		case val.GetUnits() < 0:
			txnType = "debit"
		}

		switch val.GetRewardType() {
		case rewardsPb.RewardType_FI_COINS:
			if time.Now().After(accrual.GetFiCoinsToFiPointsMigrationTime()) && orderExecutionTime.AsTime().After(accrual.GetFiCoinsToFiPointsMigrationTime().AddDate(0, -1, 0)) {
				rewardDetailsCards = append(rewardDetailsCards, s.getRewardDetailsRow(s.conf.RewardDetailsForOrderReceiptConfig().ProjectedFiPointsSymbolIconUrl(), "Projected Fi-Points", s.conf.RewardDetailsForOrderReceiptConfig().FiPointsSymbolIconUrl(), val.GetUnits(), true, fmt.Sprintf(s.conf.RewardDetailsForOrderReceiptConfig().ProjectedRewardDescription(), txnType, projectedMonth), s.conf.RewardDetailsForOrderReceiptConfig().TimerIconUrl()))
			} else {
				rewardDetailsCards = append(rewardDetailsCards, s.getRewardDetailsRow(s.conf.RewardDetailsForOrderReceiptConfig().FiCoinsEarnedIconUrl(), "Projected Fi-Coins", s.conf.RewardDetailsForOrderReceiptConfig().ProjectedFiCoinSymbolIconUrl(), val.GetUnits(), true, fmt.Sprintf(s.conf.RewardDetailsForOrderReceiptConfig().ProjectedRewardDescription(), txnType, projectedMonth), s.conf.RewardDetailsForOrderReceiptConfig().TimerIconUrl()))
			}
		case rewardsPb.RewardType_CASH:
			rewardDetailsCards = append(rewardDetailsCards, s.getRewardDetailsRow(s.conf.RewardDetailsForOrderReceiptConfig().CashbackEarnedIconUrl(), "Projected Cashback", s.conf.RewardDetailsForOrderReceiptConfig().ProjectedRupeeSymbolIconUrl(), val.GetUnits(), true, fmt.Sprintf(s.conf.RewardDetailsForOrderReceiptConfig().ProjectedRewardDescription(), txnType, projectedMonth), s.conf.RewardDetailsForOrderReceiptConfig().TimerIconUrl()))
		default:
			continue
		}

		// if any of the projected reward exists, then show the projected TnC bottom sheet info text.
		bottomSheetInfoText = s.conf.RewardDetailsForOrderReceiptConfig().RewardDetailsBottomSheet().ProjectedRewardTncBottomSheetInfoText()
	}

	// the cta that has the deeplink to announce fi-coins to fi-points (post conversion) in the reward summary bottom sheet
	var bottomSheetCta *deeplink2.Cta
	if isFeatureFiCoinsToFiPointsPostMigrationPhaseEnabled {
		bottomSheetCta = &deeplink2.Cta{
			Text:         "Know more about Fi-Points",
			Type:         deeplink2.Cta_CUSTOM,
			DisplayTheme: deeplink2.Cta_PRIMARY,
			Deeplink:     rewards.GetFiCoinsFiPointsKnowMoreButtonDeeplink(ctx, actorId, s.ffAccountingClient, s.ffClientv2, s.tieringClient, false),
		}
	}

	return &transaction.RewardDetailsBottomSheet{
		Title: &commontypes.Text{
			FontColor: "#282828",
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: s.conf.RewardDetailsForOrderReceiptConfig().RewardDetailsBottomSheet().Title(),
			},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_SUBTITLE_2,
			},
		},
		RewardDetailsCards: rewardDetailsCards,
		BgColor: &widget.BackgroundColour{
			Colour: &widget.BackgroundColour_RadialGradient{
				RadialGradient: &widget.RadialGradient{
					Colours: []string{"#E6E9ED"},
				},
			},
		},
		BottomSheetInfoText: commontypes.GetTextFromStringWithCustomFontStyle(bottomSheetInfoText, "#A4A4A4", &commontypes.FontStyleInfo{
			FontFamily: "Inter",
			FontStyle:  "normal",
			FontSize:   "9",
		}),
		Cta: bottomSheetCta,
	}
}

// nolint: funlen
func (s *Service) getRewardDetailsRow(iconUrl, title, earnedValueIconUrl string, unitsValue float32, showWashedOutCard bool, washedOutDescription, washedOutIconUrl string) *transaction.RewardDetailsBottomSheet_RewardDetailsRow {
	var (
		desc     *ui.IconTextComponent
		deeplink *deeplink2.Deeplink
	)
	if washedOutDescription != "" {
		desc = &ui.IconTextComponent{
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:       "#FFFFFF",
				CornerRadius:  11,
				LeftPadding:   5,
				RightPadding:  5,
				TopPadding:    5,
				BottomPadding: 5,
			},
			LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(washedOutIconUrl, 18, 18),
			Texts: []*commontypes.Text{
				{
					FontColor: "#313234",
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: washedOutDescription,
					},
					FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
				},
			},
		}
	} else {
		// if the card is not greyed out, then only send deeplink.
		deeplink = &deeplink2.Deeplink{
			Screen: deeplink2.Screen_MY_REWARDS_SCREEN,
		}
	}

	return &transaction.RewardDetailsBottomSheet_RewardDetailsRow{
		Icon: commontypes.GetVisualElementFromUrlHeightAndWidth(iconUrl, 32, 32),
		Title: &commontypes.Text{
			FontColor: "#313234",
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: title,
			},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
			},
		},
		EarnedValue: &ui.IconTextComponent{
			LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(earnedValueIconUrl, 18, 18),
			Texts: []*commontypes.Text{
				{
					FontColor: "#313234",
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: decimal.NewFromFloat32(unitsValue).Round(0).String(),
					},
					FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
				},
			},
			Deeplink: deeplink,
		},
		Deeplink: deeplink,
		BgColor: &widget.BackgroundColour{
			Colour: &widget.BackgroundColour_RadialGradient{
				RadialGradient: &widget.RadialGradient{
					Colours: []string{"#F6F9FD"},
				},
			},
		},
		ShowWashedOutCard: showWashedOutCard,
		Desc:              desc,
	}
}
