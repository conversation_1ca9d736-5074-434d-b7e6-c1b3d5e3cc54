package data_collector

import (
	"context"
	"errors"
	"fmt"
	"sort"

	"github.com/google/wire"
	"go.uber.org/zap"
	"golang.org/x/exp/slices"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/accounts/balance"
	"github.com/epifi/gamma/api/accounts/balance/enums"
	beActorPb "github.com/epifi/gamma/api/actor"
	typesPb "github.com/epifi/gamma/api/typesv2"
	beUserPb "github.com/epifi/gamma/api/user"
	beUserGrpPb "github.com/epifi/gamma/api/user/group"
	tieringData "github.com/epifi/gamma/frontend/tiering/data_collector"
	"github.com/epifi/gamma/frontend/tiering/helper"
	release2 "github.com/epifi/gamma/pkg/feature/release"

	"github.com/epifi/gamma/api/accounts"
	authPb "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/frontend/deeplink/timeline"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/typesv2/account"
	upiOnbPb "github.com/epifi/gamma/api/upi/onboarding"
	upiOnbEnumsPb "github.com/epifi/gamma/api/upi/onboarding/enums"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/config/genconf"

	payPkg "github.com/epifi/gamma/pkg/pay"
)

var (
	Wireset = wire.NewSet(NewPaymentOptionsDataCollector, wire.Bind(new(DataCollector), new(*DataCollectorService)))
)

type DataCollector interface {
	// CollectData collects data needed for payment options
	CollectData(ctx context.Context, actorId string, uiEntryPoint timeline.TransactionUIEntryPoint) (*CollectedData, error)
}

// DataCollectorService implements DataCollector
type DataCollectorService struct {
	conf                    *config.Config
	gconf                   *genconf.Config
	savingsClient           savingsPb.SavingsClient
	balanceClient           balance.BalanceClient
	accountPIRelationClient accountPiPb.AccountPIRelationClient
	upiOnbClient            upiOnbPb.UpiOnboardingClient
	authClient              authPb.AuthClient
	tieringDataCollector    tieringData.DataCollector
	abEvaluator             *release2.ABEvaluator[string]
}

var _ DataCollector = &DataCollectorService{}

func NewPaymentOptionsDataCollector(
	conf *config.Config,
	gconf *genconf.Config,
	savingsClient savingsPb.SavingsClient,
	balanceClient balance.BalanceClient,
	accountPIRelationClient accountPiPb.AccountPIRelationClient,
	upiOnbClient upiOnbPb.UpiOnboardingClient,
	authClient authPb.AuthClient,
	tieringDataCollector tieringData.DataCollector,
	beActorClient beActorPb.ActorClient,
	userClient beUserPb.UsersClient,
	userGrpClient beUserGrpPb.GroupClient,
) *DataCollectorService {
	return &DataCollectorService{
		conf:                    conf,
		gconf:                   gconf,
		savingsClient:           savingsClient,
		balanceClient:           balanceClient,
		accountPIRelationClient: accountPIRelationClient,
		upiOnbClient:            upiOnbClient,
		authClient:              authClient,
		tieringDataCollector:    tieringDataCollector,
		abEvaluator:             helper.GetABEvaluatorOfFeature[string](beActorClient, userClient, userGrpClient, gconf.ABFeatureReleaseConfig(), func(str string) string { return str }),
	}
}

func (s *DataCollectorService) CollectData(ctx context.Context, actorId string, uiEntryPoint timeline.TransactionUIEntryPoint) (*CollectedData, error) {
	dataCollectorConfig, err := s.gconf.PaymentOptionsConfig().GetDataCollectorConfig(uiEntryPoint)
	if err != nil {
		return nil, fmt.Errorf("error in fetching data collector config for entrypoint %s: %w", uiEntryPoint.String(), err)
	}
	// collect l1 data(data that can be fetched with just actorId)
	l1Data, collectL1DataErr := s.gatherL1Data(ctx, actorId, uiEntryPoint)
	if collectL1DataErr != nil {
		return nil, fmt.Errorf("error collecting l1 data: %w", collectL1DataErr)
	}
	// collect l2 data(l1 data is needed to fetch this data)
	l2Data, collectL2DataErr := s.gatherL2Data(ctx, l1Data, dataCollectorConfig)
	if collectL2DataErr != nil {
		// gracefully ignoring error since l1 data may still be just sufficient
		logger.Error(ctx, "error collecting l2 data", zap.Error(collectL2DataErr), zap.String(logger.ACTOR_ID_V2, actorId))
	}
	return &CollectedData{
		savingsAccDetails:                     l1Data.SavingsAccDetails(),
		vpaId:                                 l2Data.VpaId(),
		tpapAccountsData:                      l2Data.TpapAccountsData(),
		tieringEssentials:                     l1Data.TieringFeEssentials(),
		isTieringPitchInPaymentOptionsEnabled: l1Data.IsTieringPitchInPaymentOptionsEnabled(),
		savingsAccountBalance:                 l2Data.SavingsAccountBalance(),
	}, nil
}

// gatherL1Data gathers required data that can be fetched from just actorId
func (s *DataCollectorService) gatherL1Data(ctx context.Context, actorId string, uiEntryPoint timeline.TransactionUIEntryPoint) (*l1CollectedData, error) {
	l1Data := &l1CollectedData{}
	gatherL1DataErrGrp, _ := errgroup.WithContext(ctx)
	// fetch list of savings account details
	gatherL1DataErrGrp.Go(func() error {
		getAccountResp, getAccountErr := s.savingsClient.GetAccountsList(ctx, &savingsPb.GetAccountsListRequest{
			Identifier: &savingsPb.GetAccountsListRequest_BulkActorIdentifier{
				BulkActorIdentifier: &savingsPb.BulkActorIdentifier{
					ActorIds:                []string{actorId},
					AccountProductOfferings: []account.AccountProductOffering{account.AccountProductOffering_APO_REGULAR, account.AccountProductOffering_APO_NRE, account.AccountProductOffering_APO_NRO},
					PartnerBanks:            []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
				},
			},
		})
		if getAccountErr != nil {
			return fmt.Errorf("error in savingsClient GetAccount rpc call: %w", getAccountErr)
		}
		// sorting the accounts list based on APO priority, lower the value in APOPriorityMap , more the priority
		slices.SortFunc(getAccountResp.GetAccounts(), func(first, second *savingsPb.Account) int {
			firstPriority := payPkg.ApoPriorityMap[first.GetSkuInfo().GetAccountProductOffering()]
			secondPriority := payPkg.ApoPriorityMap[second.GetSkuInfo().GetAccountProductOffering()]
			if firstPriority < secondPriority {
				return -1
			}
			if firstPriority > secondPriority {
				return 1
			}
			return 0
		})
		l1Data.savingsAccDetails = getAccountResp.GetAccounts()
		return nil
	})
	// fetch active upi onboarding(tpap) accounts
	gatherL1DataErrGrp.Go(func() error {
		getAccountsResp, getAccountsErr := s.upiOnbClient.GetAccounts(ctx, &upiOnbPb.GetAccountsRequest{
			ActorId: actorId,
			AccountStatus: []upiOnbEnumsPb.UpiAccountStatus{
				upiOnbEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
				upiOnbEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
			},
		})
		if rpcErr := epifigrpc.RPCError(getAccountsResp, getAccountsErr); rpcErr != nil {
			if getAccountsResp.GetStatus().IsRecordNotFound() {
				return nil
			}
			return fmt.Errorf("error in upiOnbClient GetAccounts rpc call: %w", rpcErr)
		}
		l1Data.upiAccounts = getAccountsResp.GetAccounts()
		return nil
	})
	gatherL1DataErrGrp.Go(func() error {
		_, ok := helper.TransactionUIEntryPointToExternalTierMap[uiEntryPoint]
		if !ok {
			// skip collecting tiering essentials if the ui entry point is not configured in helper.UIEntryPointToExternalTierMap
			// as we need tiering essential data for Tiering related entry points
			return nil
		}

		tieringEssentials, getTieringEssentialsErr := s.tieringDataCollector.GetTieringEssentials(ctx, actorId)
		if getTieringEssentialsErr != nil {
			return fmt.Errorf("error getting tiering essentials for actor %s: %w", actorId, getTieringEssentialsErr)
		}

		l1Data.tieringFeEssentials = tieringEssentials
		return nil
	})

	gatherL1DataErrGrp.Go(func() error {
		releaseConstraint := release2.NewCommonConstraintData(typesPb.Feature_FEATURE_TIERING_PITCH_IN_PAYMENT_OPTIONS).WithActorId(actorId)
		enabled, _, evalErr := s.abEvaluator.Evaluate(ctx, releaseConstraint)
		if evalErr != nil {
			logger.Error(ctx, "error evaluating AB feature release for tiering pitch in payment options", zap.Error(evalErr), zap.String(logger.ACTOR_ID_V2, actorId))
			return nil
		}

		// Feature is enabled if the evaluator returned true
		l1Data.isTieringPitchInPaymentOptionsEnabled = enabled
		return nil
	})

	gatherL1DataErr := gatherL1DataErrGrp.Wait()
	return l1Data, gatherL1DataErr
}

// gatherL2Data gathers required data and also depends on l1 data
func (s *DataCollectorService) gatherL2Data(ctx context.Context, l1Data *l1CollectedData,
	dataCollectorConfig *genconf.PaymentOptionsDataCollectorConfig) (*l2CollectedData, error) {
	l2Data := &l2CollectedData{}
	gatherL2DataErrGrp, _ := errgroup.WithContext(ctx)
	// fetch vpa for actor
	// needs savings account details that got fetched in l1 iteration
	// Since the list is already sorted on the basis of APO priority, picking up VPA of 0th index which has the highest APO Priority.
	gatherL2DataErrGrp.Go(func() error {
		if len(l1Data.SavingsAccDetails()) == 0 {
			return fmt.Errorf("no savings account details available for actor")
		}
		vpaId, getVpaErr := s.getVpaForActor(ctx, l1Data.SavingsAccDetails()[0])
		if getVpaErr != nil {
			// If no VPA is found for the accountId, we will log the error and set the vpaId as "Not Available" instead of "".
			if errors.Is(getVpaErr, epifierrors.ErrRecordNotFound) {
				logger.Error(ctx, "no vpa found for accountId", zap.String(logger.ACCOUNT_ID, l1Data.SavingsAccDetails()[0].GetId()))
				l2Data.vpaId = "Not Available"
				return nil
			}
			return fmt.Errorf("error getting vpa id for actor: %w", getVpaErr)
		}
		l2Data.vpaId = vpaId
		return nil
	})
	// fetch savings account balance for the primary account
	gatherL2DataErrGrp.Go(func() error {
		if len(l1Data.SavingsAccDetails()) == 0 {
			return fmt.Errorf("no savings account details available for actor to fetch balance")
		}
		getBalanceResp, getBalanceErr := s.balanceClient.GetAccountBalance(ctx, &balance.GetAccountBalanceRequest{
			Identifier:    &balance.GetAccountBalanceRequest_Id{Id: l1Data.SavingsAccDetails()[0].GetId()},
			DataFreshness: enums.DataFreshness_HISTORICAL,
		})
		if rpcErr := epifigrpc.RPCError(getBalanceResp, getBalanceErr); rpcErr != nil {
			logger.Error(ctx, "error getting savings account balance", zap.Error(rpcErr), zap.String(logger.ACCOUNT_ID, l1Data.SavingsAccDetails()[0].GetId()))
			return nil // gracefully ignoring error as balance is not critical for payment options
		}
		l2Data.savingsAccountBalance = getBalanceResp.GetAvailableBalance()
		return nil
	})
	var accountsData []*TpapAccountData
	for _, upiAccount := range l1Data.UpiAccounts() {
		// skip adding internal account to accountsData list, if listing of internal accounts is not enabled
		// We are excluding NRE account from this check as we can add funds from NRE account to NRO account.
		if upiAccount.IsInternal() && !dataCollectorConfig.AllowInternalAccountListing() && upiAccount.GetApo() != account.AccountProductOffering_APO_NRE {
			continue
		}
		// skip adding credit account to accountsData list, if listing of credit accounts is not enabled
		if upiAccount.GetAccountType() == accounts.Type_CREDIT && !dataCollectorConfig.AllowRupayAccountListing() {
			continue
		}
		// skip adding TPAP account to accountsData list, if listing of TPAP accounts is disabled
		if !upiAccount.IsInternal() && dataCollectorConfig.DisallowExternalTpapAccountListing() {
			continue
		}
		accountsData = append(accountsData, &TpapAccountData{
			upiAccount:  upiAccount,
			bankLogoUrl: s.conf.BankNameToLogoUrlMap[upiAccount.GetBankName()],
		})
	}
	if gatherL2DataErr := gatherL2DataErrGrp.Wait(); gatherL2DataErr != nil {
		return nil, gatherL2DataErr
	}
	sort.Slice(accountsData, func(i, j int) bool {
		return accountsData[i].UpiAccount().GetBankName() < accountsData[j].UpiAccount().GetBankName()
	})
	l2Data.tpapAccountsData = accountsData
	return l2Data, nil
}

// getVpaForActor fetches active vpa id for users savings account
// TODO(sainath): move the order helper function to pkg and use that instead to avoid code duplication
func (s *DataCollectorService) getVpaForActor(ctx context.Context, savingsAcc *savingsPb.Account) (string, error) {
	// TODO (Ashutosh) : Along with CREATED and VERIFIED vpa states, figure out for SUSPENDED as well - atleast have `Vpa disabled, enable to use` message to show.
	accountPiReq := &accountPiPb.GetPiByAccountIdRequest{
		AccountId:   savingsAcc.GetId(),
		AccountType: accounts.Type_SAVINGS,
		PiTypes:     []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_UPI},
		PiStates:    []piPb.PaymentInstrumentState{piPb.PaymentInstrumentState_CREATED, piPb.PaymentInstrumentState_VERIFIED},
	}
	piByAccountId, err := s.accountPIRelationClient.GetPiByAccountId(ctx, accountPiReq)
	if err != nil || !piByAccountId.Status.IsSuccess() {
		return "", fmt.Errorf("error getting PIs for account : %w", err)
	}
	// We will be checking if the PI is mandate VPA or not, if it is mandate VPA i.e can't be used for normal UPI transfer, so we will skip that PI and move to next PI.
	for _, pi := range piByAccountId.GetPaymentInstruments() {
		if !pi.IsMandateVPA() {
			return pi.GetUpi().GetVpa(), nil
		}
	}
	return "", epifierrors.ErrRecordNotFound
}
