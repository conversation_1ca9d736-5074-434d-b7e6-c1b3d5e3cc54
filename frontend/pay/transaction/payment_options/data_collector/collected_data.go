package data_collector

import (
	gmoney "google.golang.org/genproto/googleapis/type/money"

	savingsPb "github.com/epifi/gamma/api/savings"
	upiOnbPb "github.com/epifi/gamma/api/upi/onboarding"
	"github.com/epifi/gamma/frontend/tiering/helper"
)

type TpapAccountData struct {
	upiAccount  *upiOnbPb.UpiAccount
	bankLogoUrl string
}

func (t *TpapAccountData) UpiAccount() *upiOnbPb.UpiAccount {
	if t != nil {
		return t.upiAccount
	}
	return nil
}

func (t *TpapAccountData) BankLogoUrl() string {
	if t != nil {
		return t.bankLogoUrl
	}
	return ""
}

// CollectedData to store gathered data at data collector layer
type CollectedData struct {
	savingsAccDetails                     []*savingsPb.Account
	vpaId                                 string
	tpapAccountsData                      []*TpapAccountData
	tieringEssentials                     *helper.TieringFeEssentials
	isTieringPitchInPaymentOptionsEnabled bool
	savingsAccountBalance                 *gmoney.Money
}

func (d *CollectedData) SavingsAccDetails() []*savingsPb.Account {
	if d != nil {
		return d.savingsAccDetails
	}
	return nil
}

func (d *CollectedData) VpaId() string {
	if d != nil {
		return d.vpaId
	}
	return ""
}

func (d *CollectedData) TpapAccountsData() []*TpapAccountData {
	if d != nil {
		return d.tpapAccountsData
	}
	return nil
}

func (d *CollectedData) TieringEssentials() *helper.TieringFeEssentials {
	if d != nil {
		return d.tieringEssentials
	}
	return nil
}

func (d *CollectedData) IsTieringPitchInPaymentOptionsEnabled() bool {
	if d != nil {
		return d.isTieringPitchInPaymentOptionsEnabled
	}
	return false
}

func (d *CollectedData) SavingsAccountBalance() *gmoney.Money {
	if d != nil {
		return d.savingsAccountBalance
	}
	return nil
}

// l1CollectedData is to gather data at l1 level(first iteration)
// data fetched with just actorId
type l1CollectedData struct {
	savingsAccDetails                     []*savingsPb.Account
	upiAccounts                           []*upiOnbPb.UpiAccount
	tieringFeEssentials                   *helper.TieringFeEssentials
	isTieringPitchInPaymentOptionsEnabled bool
}

func (d *l1CollectedData) SavingsAccDetails() []*savingsPb.Account {
	if d != nil {
		return d.savingsAccDetails
	}
	return nil
}

func (d *l1CollectedData) UpiAccounts() []*upiOnbPb.UpiAccount {
	if d != nil {
		return d.upiAccounts
	}
	return nil
}

func (d *l1CollectedData) TieringFeEssentials() *helper.TieringFeEssentials {
	if d != nil {
		return d.tieringFeEssentials
	}
	return nil
}

func (d *l1CollectedData) IsTieringPitchInPaymentOptionsEnabled() bool {
	if d != nil {
		return d.isTieringPitchInPaymentOptionsEnabled
	}
	return false
}

// l2CollectedData is to gather data at l2 level(second iteration)
// data fetched with l1 data
type l2CollectedData struct {
	vpaId                 string
	tpapAccountsData      []*TpapAccountData
	savingsAccountBalance *gmoney.Money
}

func (d *l2CollectedData) VpaId() string {
	if d != nil {
		return d.vpaId
	}
	return ""
}

func (d *l2CollectedData) TpapAccountsData() []*TpapAccountData {
	if d != nil {
		return d.tpapAccountsData
	}
	return nil
}

func (d *l2CollectedData) SavingsAccountBalance() *gmoney.Money {
	if d != nil {
		return d.savingsAccountBalance
	}
	return nil
}
