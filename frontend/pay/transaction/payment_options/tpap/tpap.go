package tpap

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/mask"

	accountsPb "github.com/epifi/gamma/api/accounts"

	"context"
	"fmt"

	widgetPb "github.com/epifi/be-common/api/typesv2/common/ui/widget"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/deeplink/timeline"
	txnPb "github.com/epifi/gamma/api/frontend/pay/transaction"
	feUpiOnbEnumsPb "github.com/epifi/gamma/api/frontend/upi/onboarding/enums"
	typesPb "github.com/epifi/gamma/api/typesv2"
	upiScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/upi"
	uiPb "github.com/epifi/gamma/api/typesv2/ui"
	upiOnbEnumsPb "github.com/epifi/gamma/api/upi/onboarding/enums"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/pay/transaction/payment_options/common"
	dataCollector "github.com/epifi/gamma/frontend/pay/transaction/payment_options/data_collector"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
	payPkg "github.com/epifi/gamma/pkg/pay"
)

const (
	noteText                             = "You can only transfer a total of ₹5000 in the first 24 hours from a newly linked account"
	noteTextColor                        = "#6A6D70"
	noteLeftImgTextPadding         int32 = 12
	noteCornerRadius               int32 = 20
	noteLeftPadding                int32 = 20
	noteRightPadding               int32 = 20
	noteTopPadding                 int32 = 12
	noteBottomPadding              int32 = 12
	noteBorderColor                      = "#E7E7E7"
	noteBorderWidth                int32 = 1
	noteLeftVisualElementImgUrl          = "https://epifi-icons.pointz.in/tiering/add_funds/info_icon.png"
	noteLeftVisualElementImgHeight int32 = 24
	noteLeftVisualElementImgWidth  int32 = 24

	addAccountsCtaText                     = "Add more accounts"
	addAccountsCtaTextFontColor            = "#00B899"
	addMoreCtaContainerBgColor             = "#F6F9FD"
	addMoreCtaContainerCornerRadius  int32 = 19
	addMoreCtaContainerTopPadding    int32 = 12
	addMoreCtaContainerBottomPadding int32 = 12
	addMoreCtaContainerLeftPadding   int32 = 24
	addMoreCtaContainerRightPadding  int32 = 24

	upiAccountTextFontColor               = "#6A6D70"
	upiAccountTextDisabledFontColor       = "#B2B5B9"
	upiAccountLeftImgPadding        int32 = 8
	upiAccountLeftImgHeight         int32 = 32
	upiAccountLeftImgWidth          int32 = 32

	checkboxText      = "By proceeding I consent to Epifi Tech reactivating the displayed accounts with my UPI ID"
	checkboxFontColor = "#313234"
)

var (
	unavailableTag = func() *uiPb.IconTextComponent {
		return &uiPb.IconTextComponent{
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle("UNAVAILABLE", "#6A6D70", commontypes.FontStyle_OVERLINE_2XS_CAPS),
			},
			ContainerProperties: &uiPb.IconTextComponent_ContainerProperties{
				BgColor:       "#EFF2F6",
				CornerRadius:  9,
				LeftPadding:   8,
				RightPadding:  8,
				TopPadding:    2,
				BottomPadding: 1,
			},
		}
	}
)

// nolint: funlen
func GetTpapOption(_ context.Context, gconf *genconf.Config, amount *typesPb.Money, collectedData *dataCollector.CollectedData, args *Args,
	uiEntryPoint timeline.TransactionUIEntryPoint) (*txnPb.GetPaymentOptionsResponse_PaymentOption, error) {
	if !shouldSkipCollectedDataValidationForTpapOptions(uiEntryPoint) {
		if validateErr := validateCollectedDataForTpapOption(collectedData); validateErr != nil {
			return nil, fmt.Errorf("error validating collected data for tpap option: %w", validateErr)
		}
	}
	var tag, note *uiPb.IconTextComponent
	if args.ToShowTag() {
		tag = unavailableTag()
	}
	// TODO(sainath): Make it extensible to support other notes as well
	if args.ToShowNote() {
		note = &uiPb.IconTextComponent{
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle(noteText, noteTextColor, commontypes.FontStyle_BODY_XS),
			},
			LeftImgTxtPadding: noteLeftImgTextPadding,
			ContainerProperties: &uiPb.IconTextComponent_ContainerProperties{
				CornerRadius:  noteCornerRadius,
				LeftPadding:   noteLeftPadding,
				RightPadding:  noteRightPadding,
				TopPadding:    noteTopPadding,
				BottomPadding: noteBottomPadding,
				BorderColor:   noteBorderColor,
				BorderWidth:   noteBorderWidth,
			},
			LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(noteLeftVisualElementImgUrl, noteLeftVisualElementImgHeight, noteLeftVisualElementImgWidth),
		}
	}
	var addMoreAccountsCta *uiPb.IconTextComponent
	if args.ToShowAddMoreAccounts() {
		tpapConnectNewAccountsDeeplink, getTpapDlErr := deeplinkV3.GetDeeplinkV3(deeplinkPb.Screen_LIST_ACCOUNT_PROVIDER_SCREEN, &upiScreenOptions.ListAccountProviderScreenOptions{
			UpiAccountType: feUpiOnbEnumsPb.UpiAccountType_UPI_ACCOUNT_TYPE_BANK_ACCOUNT,
		})
		if getTpapDlErr != nil {
			return nil, fmt.Errorf("error getting tpap deeplink: %w", getTpapDlErr)
		}
		addMoreAccountsCta = &uiPb.IconTextComponent{
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle(addAccountsCtaText, addAccountsCtaTextFontColor, commontypes.FontStyle_BUTTON_S),
			},
			Deeplink: tpapConnectNewAccountsDeeplink,
			ContainerProperties: &uiPb.IconTextComponent_ContainerProperties{
				BgColor:       addMoreCtaContainerBgColor,
				CornerRadius:  addMoreCtaContainerCornerRadius,
				LeftPadding:   addMoreCtaContainerLeftPadding,
				RightPadding:  addMoreCtaContainerRightPadding,
				TopPadding:    addMoreCtaContainerTopPadding,
				BottomPadding: addMoreCtaContainerBottomPadding,
			},
		}
	}
	isAmountInAllowedThresholdForCoolOff := amount.GetUnits() <= gconf.PaymentOptionsConfig().TpapOptionsConfig().MaxAmountAllowedWhileInCoolOff()
	isAmountInAllowedThresholdForNonCoolOff := amount.GetUnits() <= gconf.PaymentOptionsConfig().TpapOptionsConfig().MaxAmountAllowed()
	upiAccounts, getUpiAccsErr := getUpiAccounts(collectedData, isAmountInAllowedThresholdForCoolOff, isAmountInAllowedThresholdForNonCoolOff, uiEntryPoint)
	if getUpiAccsErr != nil {
		return nil, fmt.Errorf("error getting upi accounts data: %w", getUpiAccsErr)
	}
	return &txnPb.GetPaymentOptionsResponse_PaymentOption{
		OptionTitle:         common.GetPaymentOptionTitle(uiEntryPoint, txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_TPAP, gconf.PaymentOptionsConfig()),
		Tag:                 tag,
		ExpandCollapseState: args.ExpandCollapsibleState(),
		IsCollapsible:       true,
		DownArrow:           common.GetDownArrow(),
		PaymentOptionState:  args.PaymentOptionState(),
		PaymentOptionType:   txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_TPAP,
		BackgroundColour:    widgetPb.GetBlockBackgroundColour(common.PaymentOptionBgColor),
		PaymentOptionCta:    common.GetPaymentOptionCtaV2(amount, uiEntryPoint, txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_TPAP),
		Option: &txnPb.GetPaymentOptionsResponse_PaymentOption_UpiAccountsOption{
			UpiAccountsOption: &txnPb.TpapPaymentOption{
				UpiAccounts:        upiAccounts,
				Note:               note,
				AddMoreAccountsCta: addMoreAccountsCta,
			},
		},
		PaymentOptionCheckbox: &widgetPb.CheckboxItem{
			DisplayText: commontypes.GetTextFromStringFontColourFontStyle(checkboxText, checkboxFontColor, commontypes.FontStyle_BODY_4),
			IsChecked:   true,
		},
	}, nil
}

// validateCollectedDataForTpapOption validates the collected data for any mandatory information that is needed for tpap payment option
func validateCollectedDataForTpapOption(collectedData *dataCollector.CollectedData) error {
	if collectedData.TpapAccountsData() == nil {
		return fmt.Errorf("TpapAccountsData cannot be nil")
	}
	return nil
}

// shouldSkipCollectedDataValidationForTpapOptions check if validation for required data for tpap payment option should be skipped, as per BU's requirements
func shouldSkipCollectedDataValidationForTpapOptions(uiEntryPoint timeline.TransactionUIEntryPoint) bool {
	if uiEntryPoint == timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_PHYSICAL_DEBIT_CARD_CHARGES {
		return true
	}
	if uiEntryPoint == timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_ADD_FUNDS_USS {
		return true
	}
	return false
}

func getUpiAccounts(collectedData *dataCollector.CollectedData, _, isAmountInAllowedThresholdForNonCoolOff bool, uiEntryPoint timeline.TransactionUIEntryPoint) ([]*txnPb.TpapPaymentOption_UpiAccount, error) {
	var upiAccounts []*txnPb.TpapPaymentOption_UpiAccount
	for _, accountData := range collectedData.TpapAccountsData() {
		// We do not want to show Rupay CC option in TPAP payment options for adding funds.
		if uiEntryPoint == timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_TRANSFER_IN && accountData.UpiAccount().GetAccountType() == accountsPb.Type_CREDIT {
			continue
		}
		textFontColor := upiAccountTextFontColor
		var tag *uiPb.IconTextComponent
		availabilityState := txnPb.AvailabilityState_AVAILABILITY_STATE_AVAILABLE
		var opacity int32 = 100
		if !isAmountInAllowedThresholdForNonCoolOff {
			textFontColor = upiAccountTextDisabledFontColor
			tag = unavailableTag()
			availabilityState = txnPb.AvailabilityState_AVAILABILITY_STATE_UNAVAILABLE
			opacity = 50
		}
		var derivedAccountId string
		var getDerivedAccIdErr error
		if accountData.UpiAccount().IsInternal() {
			derivedAccountId, getDerivedAccIdErr = payPkg.GetEncodedDerivedAccountId(accountData.UpiAccount().GetAccountRefId(), accountData.UpiAccount().GetId(), "")
		} else {
			derivedAccountId, getDerivedAccIdErr = payPkg.GetEncodedDerivedAccountId("", accountData.UpiAccount().GetId(), "")
		}
		if accountData.UpiAccount().GetStatus() == upiOnbEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE {
			availabilityState = txnPb.AvailabilityState_AVAILABILITY_STATE_INACTIVE
		}
		if getDerivedAccIdErr != nil {
			return nil, fmt.Errorf("error getting encode derived accourt id: %w", getDerivedAccIdErr)
		}
		upiAccounts = append(upiAccounts,
			&txnPb.TpapPaymentOption_UpiAccount{
				UpiAccountDisplayData: &uiPb.IconTextComponent{
					Texts: []*commontypes.Text{
						commontypes.GetTextFromStringFontColourFontStyle(accountData.UpiAccount().GetBankName()+" "+accountData.UpiAccount().GetMaskedAccountNumber(),
							textFontColor, commontypes.FontStyle_SUBTITLE_S),
					},
					LeftImgTxtPadding: upiAccountLeftImgPadding,
					LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(accountData.BankLogoUrl(), upiAccountLeftImgHeight, upiAccountLeftImgWidth),
				},
				Tag:                   tag,
				AvailabilityState:     availabilityState,
				Opacity:               opacity,
				IsRadioButtonSelected: false,
				UpiAccountDetailsForPayment: &txnPb.TpapPaymentOption_UpiAccountDetailsForPayment{
					MaskedAccountNumber: mask.GetMaskedAccountNumber(accountData.UpiAccount().GetMaskedAccountNumber(), ""),
					IfscCode:            accountData.UpiAccount().GetIfscCode(),
					DerivedAccountId:    derivedAccountId,
					AccountType:         accountData.UpiAccount().GetAccountType(),
				},
			})
	}
	return upiAccounts, nil
}
