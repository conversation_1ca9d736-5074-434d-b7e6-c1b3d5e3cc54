package home

import (
	"context"
	json2 "encoding/json"
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/syncmap"

	"github.com/epifi/gamma/pkg/accrual"

	crossAttachPb "github.com/epifi/gamma/api/acquisition/crossattach"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/secrets"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/frontend/insights/secrets/secretNames"
	"github.com/epifi/gamma/frontend/pkg/featureflags"

	"github.com/epifi/be-common/pkg/frontend/app/apputils"

	"github.com/mohae/deepcopy"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	anyPb "google.golang.org/protobuf/types/known/anypb"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	"k8s.io/apimachinery/pkg/util/waitgroup"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/accounts"
	beCardPb "github.com/epifi/gamma/api/card"
	"github.com/epifi/gamma/api/card/provisioning"
	casperPb "github.com/epifi/gamma/api/casper"
	caPb "github.com/epifi/gamma/api/connected_account"
	caExternalPb "github.com/epifi/gamma/api/connected_account/external"
	"github.com/epifi/gamma/api/firefly"
	"github.com/epifi/gamma/api/firefly/accounting"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	pb "github.com/epifi/gamma/api/frontend/home"
	palEnumFePb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	feRewardsPb "github.com/epifi/gamma/api/frontend/rewards"
	feUpiOnbEnumsPb "github.com/epifi/gamma/api/frontend/upi/onboarding/enums"
	"github.com/epifi/gamma/api/insights/story"
	"github.com/epifi/gamma/api/insights/story/model"
	salaryPb "github.com/epifi/gamma/api/salaryprogram"
	savingsPb "github.com/epifi/gamma/api/savings"
	segmentPb "github.com/epifi/gamma/api/segment"
	"github.com/epifi/gamma/api/tiering"
	"github.com/epifi/gamma/api/tiering/external"
	types "github.com/epifi/gamma/api/typesv2"
	cxScreenTypes "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/inapphelp"
	typesPayPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/pay/pay_search_screen_v2"
	pkgScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/pkg"
	tiering2 "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/tiering"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/upi"
	homeTypesPb "github.com/epifi/gamma/api/typesv2/home"
	"github.com/epifi/gamma/api/typesv2/ui"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
	upiOnbEnumsPb "github.com/epifi/gamma/api/upi/onboarding/enums"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/onboarding"
	fireflyHelper "github.com/epifi/gamma/firefly/helper"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/pkg/common" // Added common import
	homePkg "github.com/epifi/gamma/frontend/pkg/home"
	"github.com/epifi/gamma/frontend/tiering/helper"
	cardPkg "github.com/epifi/gamma/pkg/card"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/feature/release"
	pkgFirefly "github.com/epifi/gamma/pkg/firefly"
	pkgOnb "github.com/epifi/gamma/pkg/onboarding"
)

var (
	// story groups eligible to be displayed through the story icon in explore section
	// note if multiple groups are eligible, then the group which occurs earliest in this list will be chosen (prioritized)
	fiMinutesStoryGroups = []model.StoryGroupName{
		model.StoryGroupName_STORY_GROUP_MONTHLY_RECAP,
	}

	intentCategoryToD2hExploreSectionMap = map[onboarding.OnboardingSoftIntentCategory][]homeTypesPb.ExploreSectionType{
		onboarding.OnboardingSoftIntentCategory_ONBOARDING_SOFT_INTENT_CATEGORY_EVERYDAY_NEEDS: {
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_YOUR_FINANCES,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_AUTOMATE_FINANCES,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_PAYMENTS,
		},
		onboarding.OnboardingSoftIntentCategory_ONBOARDING_SOFT_INTENT_CATEGORY_INVEST_MONEY: {
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_INVEST_MONEY,
		},
		onboarding.OnboardingSoftIntentCategory_ONBOARDING_SOFT_INTENT_CATEGORY_INSTANT_LOANS: {
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_LOANS,
		},
		onboarding.OnboardingSoftIntentCategory_ONBOARDING_SOFT_INTENT_CATEGORY_GLOBAL_SPENDING: {
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_CARDS,
		},
		onboarding.OnboardingSoftIntentCategory_ONBOARDING_SOFT_INTENT_CATEGORY_UNSPECIFIED: {
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_MUST_TRY,
			// below explore sections are not applicable for d2h
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_REWARDS_AND_REFERRALS,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_DOCUMENT_REQUESTS,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_ACCOUNT_PLANS,
		},
	}

	// softIntentSpecificOrderedShortcutsForMustTrySectionForNewSAUsers is the list of shortcuts which should be shown to new sa users who have selected soft intents
	softIntentSpecificOrderedShortcutsForMustTrySectionForNewSAUsers = []string{
		homeTypesPb.IconType_SHORTCUT_CONNECTED_ACCOUNT.String(),
		homeTypesPb.IconType_SHORTCUT_CREDIT_SCORE_ANALYSER.String(),
		homeTypesPb.IconType_SHORTCUT_AA_SALARY_PLAN.String(),
		homeTypesPb.IconType_SHORTCUT_WAYS_TO_EARN.String(),
		homeTypesPb.IconType_SHORTCUT_AMB.String(),
	}

	// softIntentCategoryToExploreSectionPriorityMapNewSAUsers is a map of soft intent category to ordered explore section types for new SA users
	// currently new SA user covers UserTypeFiSAWithD0To7 and UserTypeFiSAWithD8To14
	// this can be moved to config or we can have a shortcut evaluater if such use cases increases
	softIntentCategoryToExploreSectionPriorityMapNewSAUsers = map[onboarding.OnboardingSoftIntentCategory]map[homeTypesPb.ExploreSectionType]uint32{
		onboarding.OnboardingSoftIntentCategory_ONBOARDING_SOFT_INTENT_CATEGORY_EVERYDAY_NEEDS: {
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_MUST_TRY:              1,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_PAYMENTS:              2,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_ACCOUNT_PLANS:         3,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_INSIGHTS:              4,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_CARDS:                 5,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_REWARDS_AND_REFERRALS: 6,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_TRAVEL:                7,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_YOUR_FINANCES:         8,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_INVEST_MONEY:          9,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_AUTOMATE_FINANCES:     10,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_LOANS:                 11,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_DOCUMENT_REQUESTS:     12,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_UNSPECIFIED:           1000,
		},
		onboarding.OnboardingSoftIntentCategory_ONBOARDING_SOFT_INTENT_CATEGORY_GLOBAL_SPENDING: {
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_MUST_TRY:              1,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_CARDS:                 2,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_TRAVEL:                3,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_ACCOUNT_PLANS:         4,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_PAYMENTS:              5,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_INSIGHTS:              6,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_REWARDS_AND_REFERRALS: 7,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_YOUR_FINANCES:         8,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_INVEST_MONEY:          9,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_AUTOMATE_FINANCES:     10,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_LOANS:                 11,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_DOCUMENT_REQUESTS:     12,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_UNSPECIFIED:           1000,
		},
		onboarding.OnboardingSoftIntentCategory_ONBOARDING_SOFT_INTENT_CATEGORY_INVEST_MONEY: {
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_MUST_TRY:              1,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_INVEST_MONEY:          2,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_AUTOMATE_FINANCES:     3,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_YOUR_FINANCES:         4,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_INSIGHTS:              5,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_ACCOUNT_PLANS:         6,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_PAYMENTS:              7,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_CARDS:                 8,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_REWARDS_AND_REFERRALS: 9,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_TRAVEL:                10,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_LOANS:                 11,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_DOCUMENT_REQUESTS:     12,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_UNSPECIFIED:           1000,
		},
		onboarding.OnboardingSoftIntentCategory_ONBOARDING_SOFT_INTENT_CATEGORY_INSTANT_LOANS: {
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_MUST_TRY:              1,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_LOANS:                 2,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_PAYMENTS:              3,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_ACCOUNT_PLANS:         4,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_YOUR_FINANCES:         5,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_CARDS:                 6,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_REWARDS_AND_REFERRALS: 7,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_TRAVEL:                8,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_INSIGHTS:              9,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_INVEST_MONEY:          10,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_AUTOMATE_FINANCES:     11,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_DOCUMENT_REQUESTS:     12,
			homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_UNSPECIFIED:           1000,
		},
	}
)

func (s *Service) GetHomeExplore(ctx context.Context, req *pb.GetHomeExploreRequest) (*pb.GetHomeExploreResponse, error) {
	var (
		exploreWidgetSections []*pb.HomeExploreWidget
		feedbackEngineInfo    *header.FeedbackEngineInfo
		actorId               = req.GetReq().GetAuth().GetActorId()
		appPlatform           = req.GetReq().GetAuth().GetDevice().GetPlatform()
		appVersionCode        = req.GetReq().GetAppVersionCode()
		err                   error
	)

	isFeatureHomeDesignEnhancementsEnabled := featureflags.IsFeatureHomeDesignEnhancementsEnabled(ctx, &featureflags.IsFeatureHomeDesignEnhancementsEnabledRequest{
		ActorId: actorId,
		ExternalDeps: &common.ExternalDependencies{
			Evaluator:            s.evaluator,
			OnboardingClient:     s.onboardingClient,
			QuestSdkClient:       s.questSdkClient,
			UserAttributeFetcher: s.userAttributeFetcher,
			NetWorthClient:       s.networthClient,
		},
	})
	userType, exploreSections := s.getHomeExploreSections(ctx, actorId, appPlatform, appVersionCode, isFeatureHomeDesignEnhancementsEnabled)
	for idx, section := range exploreSections {
		exploreWidgetSection := &pb.HomeExploreWidget{
			Section: &pb.HomeExploreWidget_HomeExploreSection{HomeExploreSection: section},
		}

		if isFeatureHomeDesignEnhancementsEnabled {
			exploreWidgetSection.TopSpacer = components.NewSpacer().WithBgColor(widget.GetBlockBackgroundColour(Snow)).WithSpacingValue(components.Spacing_SPACING_XL)
			exploreWidgetSection.BottomSpacer = components.NewSpacer().WithBgColor(widget.GetBlockBackgroundColour(Snow)).WithSpacingValue(components.Spacing_SPACING_XL)

			if idx != 0 {
				exploreWidgetSection.TopSpacer = exploreWidgetSection.GetTopSpacer().WithSeperator(components.NewSeparator().WithBackgroundColor(widget.GetBlockBackgroundColour(colors.ColorOnDarkHighEmphasis)).WithThickness(4))
			}
		}

		exploreWidgetSections = append(exploreWidgetSections, exploreWidgetSection)
	}
	feedbackEngineInfo, exploreWidgetSections, err = s.getFeedbackSection(ctx, s.genconf.HomeExploreConfig().ExploreFeedbackSectionConfig(), exploreWidgetSections)
	if err != nil {
		logger.Error(ctx, "error while fetching feedback section", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
	}

	// Only for WealthAnalyser user type, we need show full visual card element sections
	if homePkg.IsWealthAnalyserUser(userType) {
		exploreWidgetSections = s.getFullVisualElementCardSection(ctx, actorId, exploreWidgetSections)
	}

	return &pb.GetHomeExploreResponse{
		RespHeader: &header.ResponseHeader{
			FeedbackEngineInfo: feedbackEngineInfo,
			Status:             rpc.StatusOk(),
		},
		Sections:              s.getEligibleHomeExploreSections(ctx, userType, exploreSections),
		ExploreWidgetSections: exploreWidgetSections,
		PageTitle:             s.getExplorePageTitle(s.genconf.HomeExploreConfig().PageTitle()),
		StickyIcon:            s.getStickyIcon(userType, s.genconf.HomeExploreConfig().ShortcutStickyIcon()),
		ToastText:             s.getShortcutUpdatedToastMessage(s.genconf.HomeExploreConfig().ToastText()),
		ExploreSearchWidget:   s.getExploreSearchWidget(ctx, userType),
	}, nil
}

// getEligibleHomeExploreSections determines the explore sections to return based on user type and feature flag eligibility.
// If the feature flag is enabled for the given user type and version, it returns nil (indicating the sections are not needed from BE).
// Otherwise, it returns the default explore sections for old app versions.
func (s *Service) getEligibleHomeExploreSections(ctx context.Context, userType homePkg.UserType, sections []*pb.HomeExploreSection) []*pb.HomeExploreSection {
	conf := s.genconf.HomeExploreConfig().FullVisualCardSectionConfig()
	if homePkg.IsWealthAnalyserUser(userType) && apputils.IsFeatureEnabledFromCtxDynamic(ctx, conf.FeatureFlag()) {
		return nil
	}
	return sections
}

// getFullVisualElementCardSection returns the explore widgets for the full visual element card section
// if Feature is not enabled, return the existing widget sections.
// if canCrossSell is false or RPC throws any error, return the existing explore widgets in same priority.
// if canCrossSell is true, return the visual cards in the order of the prioritized products to pitch.
func (s *Service) getFullVisualElementCardSection(ctx context.Context, actorId string, exploreWidgetSections []*pb.HomeExploreWidget) []*pb.HomeExploreWidget {
	conf := s.genconf.HomeExploreConfig().FullVisualCardSectionConfig()
	if !apputils.IsFeatureEnabledFromCtxDynamic(ctx, conf.FeatureFlag()) {
		return exploreWidgetSections
	}

	visualCards := deepcopy.Copy(conf.VisualCards()).([]*config.ExploreVisualCardConfig)

	resp, err := s.crossAttachClient.GetCrossSellInfo(ctx, &crossAttachPb.GetCrossSellInfoRequest{ActorId: actorId})
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		logger.Error(ctx, "error while fetching cross sell info", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
		// if GetCrossSellInfo returned any error then, return the existing explore widgets in same priority
		return generateExploreVisualCards(visualCards)
	}

	// if canCrossSell is false, return the existing explore widgets in same priority
	if !resp.GetCanCrossSell().ToBool() {
		return generateExploreVisualCards(visualCards)
	}

	// Create a priority map for quick lookup
	priorityMap := make(map[string]int)
	for idx, product := range resp.GetPrioritizedProductsToPitch() {
		priorityMap[product.String()] = idx
	}

	// sort the visual cards based on the order of the products in the response
	sort.Slice(visualCards, func(i, j int) bool {
		iPriority, iExists := priorityMap[visualCards[i].Id]
		jPriority, jExists := priorityMap[visualCards[j].Id]
		if iExists && jExists {
			return iPriority < jPriority
		}
		return iExists
	})

	return generateExploreVisualCards(visualCards)
}

// generateExploreVisualCards returns the explore widgets for the full visual element card section
func generateExploreVisualCards(visualCards []*config.ExploreVisualCardConfig) []*pb.HomeExploreWidget {
	homeExploreWidget := make([]*pb.HomeExploreWidget, 0)
	for idx, visualCard := range visualCards {
		// by default use secondary visual element card
		fullVisualCard := visualCard.SecondaryVisualElementCard
		// if it is the first card, use primary visual element card
		if idx == 0 {
			fullVisualCard = visualCard.PrimaryVisualElementCard
		}
		visualElement := commontypes.GetVisualElementFromUrlHeightAndWidth(fullVisualCard.FullCardImage.Url, fullVisualCard.FullCardImage.Properties.Height, fullVisualCard.FullCardImage.Properties.Width)
		if fullVisualCard.FullCardLottie != nil {
			visualElement = commontypes.GetVisualElementLottieFromUrlHeightAndWidth(fullVisualCard.FullCardLottie.LottieUrl, fullVisualCard.FullCardLottie.Properties.Height, fullVisualCard.FullCardLottie.Properties.Width)
		}

		// Unmarshal the full deeplink from config
		dl := &deeplink.Deeplink{}
		if err := protojson.Unmarshal([]byte(visualCard.Deeplink), dl); err != nil {
			logger.ErrorNoCtx("failed to unmarshal visual card deeplink", zap.String("Deeplink", visualCard.Deeplink), zap.Error(err))
			continue // Skip this card if deeplink is invalid
		}

		homeExploreWidget = append(homeExploreWidget, &pb.HomeExploreWidget{
			Section: &pb.HomeExploreWidget_FullVisualElementCardSection{
				FullVisualElementCardSection: &pb.FullVisualElementCardSection{
					Title:         commontypes.GetTextFromStringFontColourFontStyle(visualCard.Title.Content, visualCard.Title.FontColor, commontypes.FontStyle(commontypes.FontStyle_value[visualCard.Title.FontStyle])),
					VisualElement: visualElement,
					Deeplink:      dl,
					BgColour:      &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: visualCard.BgColor}},
				},
			}})
	}

	return homeExploreWidget
}

// getSoftIntentBasedExploreSectionsForNewSAUsers returns the explore sections based on the soft intents for new SA users
// currently new SA user covers UserTypeFiSAWithD0To7 and UserTypeFiSAWithD8To14
func (s *Service) getSoftIntentBasedExploreSectionsForNewSAUsers(userSelectedSoftIntents []onboarding.OnboardingSoftIntent, defaultExploreSections []*config.ExploreSection) []*config.ExploreSection {
	if len(userSelectedSoftIntents) > 0 {
		softIntentCategory := s.fetchDerivedSoftIntentCategoryBasedOnPriority(userSelectedSoftIntents, P0)
		if exploreSectionToRankingMap, ok := softIntentCategoryToExploreSectionPriorityMapNewSAUsers[softIntentCategory]; ok {
			sort.Slice(defaultExploreSections, func(i, j int) bool {
				iExploreSection := homeTypesPb.ExploreSectionType(homeTypesPb.ExploreSectionType_value[defaultExploreSections[i].SectionType])
				jExploreSection := homeTypesPb.ExploreSectionType(homeTypesPb.ExploreSectionType_value[defaultExploreSections[j].SectionType])
				return exploreSectionToRankingMap[iExploreSection] < exploreSectionToRankingMap[jExploreSection]
			})
		}
	}

	for _, section := range defaultExploreSections {
		if section.SectionType == homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_MUST_TRY.String() {
			section.IconTypes = softIntentSpecificOrderedShortcutsForMustTrySectionForNewSAUsers
		}
	}

	return defaultExploreSections
}

func (s *Service) getSoftIntentBasedExploreSections(ctx context.Context, actorId string, selectedSoftIntents []onboarding.OnboardingSoftIntent) ([]*config.ExploreSection, error) {
	featureLifecycleResp, featureLifecycleErr := s.onboardingClient.GetFeatureLifecycle(ctx, &onboarding.GetFeatureLifecycleRequest{
		ActorId: actorId,
		Features: []onboarding.Feature{
			onboarding.Feature_FEATURE_SA,
			onboarding.Feature_FEATURE_PL,
		},
		WantCachedData: true,
	})
	if rpcErr := epifigrpc.RPCError(featureLifecycleResp, featureLifecycleErr); rpcErr != nil {
		return nil, fmt.Errorf("GetFeatureLifecycle rpc failed, %w", rpcErr)
	}

	var allSoftIntents []onboarding.OnboardingSoftIntent
	for intent := range onboarding.SoftIntentToCategoryMap {
		allSoftIntents = append(allSoftIntents, intent)
	}

	// intents are reordered based on Selected-Intents > Feature-Activation > Feature-Eligibility
	// todo (himanshu) sending selected soft intents in feature lifecycle api and call that instead of get details in the caller
	prioritizer := &homePkg.SoftIntentPrioritizer{SelectedSoftIntents: selectedSoftIntents, FeatureLifecycleMap: featureLifecycleResp.GetFeatureLifecycleMap()}
	orderedSoftIntents := prioritizer.OrderSoftIntents(allSoftIntents)
	return s.getSoftIntentOrderedExploreSections(ctx, s.genconf.LiteHomeRevampParams().ExploreSections(), orderedSoftIntents), nil
}

// getSoftIntentOrderedExploreSections orders explore sections based on the given soft intent order
func (s *Service) getSoftIntentOrderedExploreSections(ctx context.Context, cfgExploreSections []*config.ExploreSection, orderedIntents []onboarding.OnboardingSoftIntent) []*config.ExploreSection {
	var exploreSections []*config.ExploreSection

	// adding must try section at the top
	mustTrySection, ok := lo.Find(cfgExploreSections, func(item *config.ExploreSection) bool {
		return item.SectionType == homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_MUST_TRY.String()
	})
	if !ok {
		logger.Error(ctx, "must try section not found in d2h home config")
	} else {
		var defaultMustTryIconValues []int32
		for _, iconType := range mustTrySection.IconTypes {
			defaultMustTryIconValues = append(defaultMustTryIconValues, homeTypesPb.IconType_value[iconType])
		}
		mustTryIconsValues := s.getShortcutsOneFromEachCategory(len(mustTrySection.IconTypes), orderedIntents, defaultMustTryIconValues)
		var mustTryIconNames []string
		for _, mustTryIconValue := range mustTryIconsValues {
			mustTryIconNames = append(mustTryIconNames, homeTypesPb.IconType_name[mustTryIconValue])
		}

		exploreSections = append(exploreSections, &config.ExploreSection{
			SectionType: homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_MUST_TRY.String(),
			Title:       mustTrySection.Title,
			BgColour:    mustTrySection.BgColour,
			IconTypes:   mustTryIconNames,
		})
	}

	orderedCategories := getUniqueCategoriesForIntent(orderedIntents)
	for _, category := range orderedCategories {
		exploreSectionsType := intentCategoryToD2hExploreSectionMap[category]
		for _, exploreSectionType := range exploreSectionsType {
			exploreSection, ok := lo.Find(cfgExploreSections, func(item *config.ExploreSection) bool {
				return item.SectionType == exploreSectionType.String()
			})
			if !ok {
				continue
			}
			exploreSections = append(exploreSections, exploreSection)
		}
	}

	return exploreSections
}

// getUniqueCategoriesForIntent gets unique categories of given soft intents while maintaining the order of category
func getUniqueCategoriesForIntent(intents []onboarding.OnboardingSoftIntent) []onboarding.OnboardingSoftIntentCategory {
	var categories []onboarding.OnboardingSoftIntentCategory
	for _, intent := range intents {
		categories = append(categories, onboarding.SoftIntentToCategoryMap[intent])
	}
	return lo.Uniq(categories)
}

/*
This method is a central(utility) method to get the home explore page options. This can be used by multiple
api's/methods to fetch the same results - for now, to build the home shortcut icons api's/methods to fetch the
same results - for now, to build the home shortcut icon options, and explore page
*/
func (s *Service) getHomeExploreSections(ctx context.Context, actorId string, appPlatform commontypes.Platform, appVersionCode uint32, isFeatureHomeDesignEnhancementsEnabled bool) (homePkg.UserType, []*pb.HomeExploreSection) {
	// get explore sections from config
	userType, confExploreSections := s.getHomeExploreSectionsConfig(ctx, actorId)

	// get explore sections from experiment
	exploreSectionsFromExperimentString := s.genconf.HomeRevampParams().HomeLayoutParams().ExploreSectionsJson(ctx)
	if exploreSectionsFromExperimentString != "" {
		exploreSectionsFromExperiment, err := s.GetExploreSectionsFromExperiment(ctx, exploreSectionsFromExperimentString)
		if err == nil {
			logger.Info(ctx, fmt.Sprintf("Explore section from experiment loaded for actor: %v", actorId))
			confExploreSections = *exploreSectionsFromExperiment
		}
	}

	var (
		exploreSections            = make([]*pb.HomeExploreSection, len(confExploreSections))
		sectionWg                  waitgroup.SafeWaitGroup
		ctxWithTimeout, cancelFunc = context.WithTimeout(ctx, 3*time.Second)
	)

	defer cancelFunc()
	for k, v := range confExploreSections {
		sectionWg.Add(1)
		idx := k
		sectionConfig := v
		goroutine.RunWithCtx(ctxWithTimeout, func(ctx context.Context) {
			defer sectionWg.Done()
			exploreSections[idx] = s.getHomeExploreSection(ctx, actorId, sectionConfig, appPlatform, appVersionCode, isFeatureHomeDesignEnhancementsEnabled)
		})
	}
	sectionWg.Wait()

	// filter out empty sections
	exploreSections = lo.Filter(exploreSections, func(val *pb.HomeExploreSection, _ int) bool { return len(val.Icon) != 0 })

	return userType, exploreSections
}

func (s *Service) getHomeExploreSection(ctx context.Context, actorId string, sectionConfig *config.ExploreSection, appPlatform commontypes.Platform, appVersionCode uint32, isFeatureHomeDesignEnhancementsEnabled bool) *pb.HomeExploreSection {
	var iconWg waitgroup.SafeWaitGroup
	exploreIcons := make([]*pb.Icon, len(sectionConfig.IconTypes))

	for k, v := range sectionConfig.IconTypes {
		iconWg.Add(1)
		idx := k
		iconType := v
		goroutine.RunWithCtx(ctx, func(ctx context.Context) {
			defer iconWg.Done()
			exploreIcons[idx] = s.getHomeExploreIcon(ctx, actorId, iconType, appPlatform, appVersionCode, isFeatureHomeDesignEnhancementsEnabled)
		})
	}
	iconWg.Wait()

	// filter out nil icons
	exploreIcons = lo.Filter(exploreIcons, func(val *pb.Icon, _ int) bool { return val != nil })

	sectionBgColor := sectionConfig.BgColour
	sectionTitleColour := MonochromeSteel
	if isFeatureHomeDesignEnhancementsEnabled {
		sectionBgColor = "#FFFFFF"
		sectionTitleColour = MonochromeNight
	}

	return &pb.HomeExploreSection{
		Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: sectionConfig.Title},
			FontColor: sectionTitleColour, FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_M}},
		BgColour: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: sectionBgColor}},
		Icon:     exploreIcons,
	}
}

func (s *Service) getHomeExploreIcon(ctx context.Context, actorId string, iconType string, appPlatform commontypes.Platform, appVersionCode uint32, isFeatureHomeDesignEnhancementsEnabled bool) *pb.Icon {
	defaultDl := &deeplink.Deeplink{}
	iconDetails, found := s.conf.HomeRevampParams.ShortcutIconTypeToDetailsMap[iconType]
	if !found {
		logger.Error(ctx, "icon type not found in icon details map", zap.String("IconType", iconType))
		return nil
	}
	err := protojson.Unmarshal([]byte(iconDetails.Deeplink), defaultDl)
	if err != nil {
		logger.Error(ctx, "failed to unmarshal dl", zap.String("Deeplink", iconDetails.Deeplink))
		return nil
	}

	dl := s.getAppropriateDeeplink(ctx, actorId, homeTypesPb.IconType(homeTypesPb.IconType_value[iconType]), defaultDl, appPlatform, appVersionCode)
	if dl == nil {
		return nil
	}

	var (
		imageUrl  = iconDetails.ImageUrl
		fontColor = MonochromeNight
		bgColorV2 = widget.GetLinearGradientBackgroundColour(0, []*widget.ColorStop{
			{
				Color:          "#E3F7F2",
				StopPercentage: 0,
			},
			{
				Color:          "#F4FCFA",
				StopPercentage: 100,
			},
		})
		borderColor *widget.BackgroundColour
	)
	if isFeatureHomeDesignEnhancementsEnabled {
		imageUrl = iconDetails.ImageUrlV2
		fontColor = colors.ColorOnDarkLowEmphasis
		var colorStops []*widget.ColorStop
		for _, colorStop := range iconDetails.BgColor.BackgroundColour.LinearGradient.LinearColorStops {
			colorStops = append(colorStops, &widget.ColorStop{
				Color:          colorStop.Color,
				StopPercentage: colorStop.StopPercentage,
			})
		}
		if len(colorStops) > 0 {
			bgColorV2 = widget.GetLinearGradientBackgroundColour(0, colorStops)
		}
	}

	// doing this to avoid panic in android
	if iconDetails.BorderColor != "" {
		borderColor = widget.GetBlockBackgroundColour(iconDetails.BorderColor)
	}

	// assigning fi-point icon for offer catalogs if called after August 1, 2025 (prod)
	imageUrl = accrual.ReturnApplicableValue(imageUrl, "https://epifi-icons.pointz.in/home/<USER>", nil, iconType == homeTypesPb.IconType_SHORTCUT_OFFERS_CATALOG.String()).(string)

	return &pb.Icon{
		Title: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: accrual.ReplaceCoinWithPointIfApplicable(iconDetails.Title)},
			FontColor:    fontColor,
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
		},
		IconImage:     &commontypes.Image{ImageUrl: imageUrl},
		VisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(imageUrl, 46, 46),
		ActionType:    pb.Icon_ACTION_TYPE_DEEPLINK,
		Action: &pb.Icon_Deeplink{
			Deeplink: dl,
		},
		IconType:    homeTypesPb.IconType(homeTypesPb.IconType_value[iconType]),
		Id:          iconType,
		FeatureTag:  getFeatureTagForShortcut(iconDetails),
		BorderColor: borderColor,
		BgColourV2:  bgColorV2,
	}
}

func isSAOnboardingFail(onb *onboarding.OnboardingDetails) bool {
	return onb.GetFeatureDetails().GetFeatureInfo()[onboarding.Feature_FEATURE_SA.String()].GetFeatureStatus() == onboarding.FeatureStatus_FEATURE_STATUS_ONBOARDING_FAILURE

}

func isCCOnboardingStarted(onb *onboarding.OnboardingDetails) bool {
	featStatus := onb.GetFeatureDetails().GetFeatureInfo()[onboarding.Feature_FEATURE_CC.String()].GetFeatureStatus()
	return featStatus == onboarding.FeatureStatus_FEATURE_STATUS_ACTIVE || featStatus == onboarding.FeatureStatus_FEATURE_STATUS_ONBOARDING_IN_PROGRESS
}

// getAppropriateDeeplink: takes default deeplink and returns user state specific deeplink, if applicable.
// nolint:funlen
func (s *Service) getAppropriateDeeplink(ctx context.Context, actorId string, iconType homeTypesPb.IconType, defaultDl *deeplink.Deeplink, appPlatform commontypes.Platform, appVersionCode uint32) *deeplink.Deeplink {
	// TODO: refactor
	switch iconType {
	case homeTypesPb.IconType_SHORTCUT_INFINITE_PLAN, homeTypesPb.IconType_SHORTCUT_PLUS_PLAN:
		// checking if user is b2b verified, if yes then not showing infinite and plus icon.
		isUserB2BVerified, isUserB2BVerifiedErr := s.usersClient.GetB2BSalaryProgramVerificationStatus(ctx, &user.GetB2BSalaryProgramVerificationStatusRequest{
			Identifier: &user.GetB2BSalaryProgramVerificationStatusRequest_ActorId{ActorId: actorId},
		})
		if rpcErr := epifigrpc.RPCError(isUserB2BVerified, isUserB2BVerifiedErr); rpcErr != nil {
			logger.Error(ctx, "failed to get b2b verification status", zap.Error(rpcErr))
			return nil
		}
		if isUserB2BVerified.GetIsVerified() {
			return nil
		}
		return s.getDeeplinkForTieringExploreIcons(ctx, actorId, defaultDl, iconType)
	case homeTypesPb.IconType_SHORTCUT_DEBIT_CARD_CONTROLS:
		return s.getDebitCardUsageDeeplink(ctx, actorId)
	case homeTypesPb.IconType_SHORTCUT_CREDIT_CARD_CONTROLS:
		return s.getCreditCardControlsDeeplink(ctx, actorId)
	case homeTypesPb.IconType_SHORTCUT_DEBIT_CARD:
		onbResp, err := s.onboardingClient.GetDetails(ctx, &onboarding.GetDetailsRequest{
			ActorId:    actorId,
			CachedData: true,
		})
		if grpcErr := epifigrpc.RPCError(onbResp, err); grpcErr != nil {
			return nil
		}
		if onbResp.GetDetails().GetFiLiteDetails().GetIsEnabled() == commontypes.BooleanEnum_TRUE {
			if isSAOnboardingFail(onbResp.GetDetails()) {
				return pkgOnb.GetOnboardingFailureScreenForCC()
			}
			dl, dlErr := pkgOnb.GetDCBenefitsScreen()
			if dlErr != nil {
				return nil
			}
			return dl
		}
	case homeTypesPb.IconType_SHORTCUT_ATM_LOCATOR:
		return cardPkg.GetAtmLocatorScreenDeeplink()
	case homeTypesPb.IconType_SHORTCUT_CREDIT_CARD:
		onbResp, err := s.onboardingClient.GetDetails(ctx, &onboarding.GetDetailsRequest{
			ActorId:    actorId,
			CachedData: true,
		})
		if grpcErr := epifigrpc.RPCError(onbResp, err); grpcErr != nil {
			return nil
		}
		if onbResp.GetDetails().GetFiLiteDetails().GetIsEnabled() == commontypes.BooleanEnum_TRUE {
			if isSAOnboardingFail(onbResp.GetDetails()) {
				return pkgOnb.GetOnboardingFailureScreenForCC()
			}
			if isCCOnboardingStarted(onbResp.GetDetails()) {
				return &deeplink.Deeplink{
					Screen: deeplink.Screen_CREDIT_CARD_DASHBOARD_SCREEN,
				}
			}
			dl, dlErr := pkgOnb.GetCCBenefitsScreen()
			if dlErr != nil {
				return nil
			}
			return dl
		}
	// if user is salary active, then return account benefits screen
	case homeTypesPb.IconType_SHORTCUT_SALARY_PROGRAM, homeTypesPb.IconType_SHORTCUT_SALARY_BASIC_PLAN:

		if s.genconf.SalaryProgram().DisableB2CSalaryEntryPointsFlag() {

			tieringPitchV2Resp, tieringPitchV2Err := s.beTieringClient.GetTieringPitchV2(ctx, &tiering.GetTieringPitchV2Request{
				ActorId: actorId,
			})
			if rpcErr := epifigrpc.RPCError(tieringPitchV2Resp, tieringPitchV2Err); rpcErr != nil {
				logger.Error(ctx, "failed to get tiering pitch v2", zap.Error(rpcErr))
				return nil
			}
			currTier := tieringPitchV2Resp.GetCurrentTier()
			// checking if user is b2b verified, if not then not showing salary program explore icon.
			isUserB2BVerified, isUserB2BVerifiedErr := s.usersClient.GetB2BSalaryProgramVerificationStatus(ctx, &user.GetB2BSalaryProgramVerificationStatusRequest{
				Identifier: &user.GetB2BSalaryProgramVerificationStatusRequest_ActorId{ActorId: actorId},
			})
			if rpcErr := epifigrpc.RPCError(isUserB2BVerified, isUserB2BVerifiedErr); rpcErr != nil {
				logger.Error(ctx, "failed to get b2b verification status", zap.Error(rpcErr))
				return nil
			}
			if !isUserB2BVerified.GetIsVerified() && currTier != external.Tier_TIER_FI_SALARY {
				return nil
			}
		}

		salRegRes, err := s.salaryProgramClient.GetCurrentRegStatusAndNextRegStage(ctx, &salaryPb.CurrentRegStatusAndNextRegStageRequest{ActorId: actorId,
			FlowType: salaryPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE})
		if te := epifigrpc.RPCError(salRegRes, err); te != nil {
			logger.Error(ctx, "failed to get sal registration status GetCurrentRegStatusAndNextRegStage", zap.Error(te))
			return nil
		}
		isPlansV2Enabled := s.getIsTieringV2EnabledForActor(ctx, actorId)
		var tierToLoad external.Tier
		if iconType == homeTypesPb.IconType_SHORTCUT_SALARY_PROGRAM {
			tierToLoad = external.Tier_TIER_FI_SALARY
		} else {
			tierToLoad = external.Tier_TIER_FI_SALARY_BASIC
		}
		return tiering2.AllPlansDeeplink(tierToLoad, isPlansV2Enabled)
	case homeTypesPb.IconType_SHORTCUT_FI_MINUTES:
		activeStoryGroupRes, err := s.storyClient.GetActiveStoryGroup(ctx, &story.GetActiveStoryGroupRequest{
			StoryGroupName: fiMinutesStoryGroups,
		})
		if rpcErr := epifigrpc.RPCError(activeStoryGroupRes, err); rpcErr != nil {
			logger.Error(ctx, "failed to get active story group, not showing icon in explore section", zap.Error(rpcErr))
			return nil
		}
		if len(activeStoryGroupRes.GetStoryGroups()) == 0 {
			logger.Info(ctx, "no active story group, not showing icon in explore section")
			return nil
		}
		storyGroup := getPrioritizedStoryGroup(activeStoryGroupRes.GetStoryGroups())
		if storyGroup == model.StoryGroupName_STORY_GROUP_NAME_UNSPECIFIED {
			logger.Info(ctx, "no story group left after prioritized story group filtering")
			return nil
		}
		return &deeplink.Deeplink{
			Screen: deeplink.Screen_INSIGHTS_STORIES_SCREEN,
			ScreenOptions: &deeplink.Deeplink_InsightsStoriesScreenOptions{
				InsightsStoriesScreenOptions: &deeplink.InsightsStoriesScreenOptions{
					StoryGroup: storyGroup.String(),
				},
			},
		}
	case homeTypesPb.IconType_SHORTCUT_SET_REMINDER:
		if appPlatform == commontypes.Platform_ANDROID && int(appVersionCode) >= 251 {
			return defaultDl
		}
		if appPlatform == commontypes.Platform_IOS && int(appVersionCode) >= 347 {
			return defaultDl
		}
		eligible := s.isAllowedForGroup(ctx, []commontypes.UserGroup{
			commontypes.UserGroup_INTERNAL,
		}, actorId)
		if eligible {
			return defaultDl
		}
		return nil
	case homeTypesPb.IconType_SHORTCUT_EARLY_SALARY:
		isEligible, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_LOANS_EARLY_SALARY).WithActorId(actorId))
		if err != nil {
			logger.Error(ctx, "failed to evaluate release config for Loans Early Salary", zap.Error(err))
			return nil
		}
		if !isEligible {
			return nil
		}
		isPostLoanV2, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_FEATURE_POST_LOAN_DISBURSAL_SCREENS).WithActorId(actorId))
		if err != nil {
			logger.Error(ctx, "failed in post loan disbursal release evaluator", zap.Error(err))
			return nil
		}
		appVersionConstraintData := release.NewAppVersionConstraintData(s.genconf.Lending().PreApprovedLoan().PostDisbursalV2FlowAppVersionConstraintConfig().AppVersionConstraintConfig())
		isAppVersionGreater, appVerErr := release.NewAppVersionConstraint().Evaluate(ctx, appVersionConstraintData, nil)
		if appVerErr != nil {
			logger.Error(ctx, "failed in post loan disbursal app version evaluator", zap.Error(err))
			return nil
		}
		platform, _ := epificontext.AppPlatformAndVersion(ctx)
		if platform == commontypes.Platform_ANDROID && isPostLoanV2 && isAppVersionGreater && (s.genconf.Lending().PreApprovedLoan().PostDisbursalV2FlowAppVersionConstraintConfig().SkipVendorLoanProgramCheck() ||
			s.genconf.Lending().PreApprovedLoan().PostDisbursalV2FlowAppVersionConstraintConfig().VendorLoanProgramMap().Get(strings.Join([]string{
				"LIQUILOANS",
				"LOAN_PROGRAM_EARLY_SALARY",
			}, ":"))) {
			return &deeplink.Deeplink{
				Screen: deeplink.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
				ScreenOptions: &deeplink.Deeplink_PreApprovedLoanLandingScreenOptions{
					PreApprovedLoanLandingScreenOptions: &deeplink.PreApprovedLoanLandingScreenOptions{
						LoanHeader: &palEnumFePb.LoanHeader{
							LoanProgram: palEnumFePb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY,
							EventData:   &palEnumFePb.EventData{EntryPoint: palEnumFePb.EntryPoint_ENTRY_POINT_EXPLORE_TAB},
						},
					},
				},
			}
		}
		return &deeplink.Deeplink{
			Screen: deeplink.Screen_EARLY_SALARY_LANDING_SCREEN,
			ScreenOptions: &deeplink.Deeplink_PreApprovedLoanLandingScreenOptions{
				PreApprovedLoanLandingScreenOptions: &deeplink.PreApprovedLoanLandingScreenOptions{
					LoanHeader: &palEnumFePb.LoanHeader{
						LoanProgram: palEnumFePb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY,
						EventData:   &palEnumFePb.EventData{EntryPoint: palEnumFePb.EntryPoint_ENTRY_POINT_EXPLORE_TAB},
					},
				},
			},
		}
	case homeTypesPb.IconType_SHORTCUT_CARD_OFFERS:
		if appPlatform == commontypes.Platform_ANDROID && appVersionCode >= s.genconf.RewardsFrontendMeta().MinAndroidAppVersionSupportingCardOfferCatalogV2() ||
			appPlatform == commontypes.Platform_IOS && appVersionCode >= s.genconf.RewardsFrontendMeta().MinIosAppVersionSupportingCardOfferCatalogV2() {
			return &deeplink.Deeplink{
				Screen: deeplink.Screen_CARD_OFFERS_CATALOG_SCREEN,
				ScreenOptions: &deeplink.Deeplink_CardOffersCatalogScreenOptions{
					CardOffersCatalogScreenOptions: &deeplink.CardOffersCatalogScreenOptions{
						CardType:   deeplink.CardOffersCatalogScreenOptions_DEBIT_CARD,
						CardTypeId: feRewardsPb.CardTypeId_DEBIT_CARD_ID.String(),
					},
				},
			}
		}
		return defaultDl
	case homeTypesPb.IconType_SHORTCUT_ORDER_PHYSICAL_DEBIT_CARD:
		if !s.shouldDisplayPhysicalCardOrderShortcut(ctx, actorId) {
			return nil
		}
		return defaultDl
	case homeTypesPb.IconType_SHORTCUT_DEBIT_CARD_OFFERS:
		if !s.shouldDisplayDebitCardOffersShortcut(ctx, actorId) {
			return nil
		}

		if appPlatform == commontypes.Platform_ANDROID && appVersionCode >= s.genconf.RewardsFrontendMeta().MinAndroidAppVersionSupportingCardOfferCatalogV2() ||
			appPlatform == commontypes.Platform_IOS && appVersionCode >= s.genconf.RewardsFrontendMeta().MinIosAppVersionSupportingCardOfferCatalogV2() {
			return &deeplink.Deeplink{
				Screen: deeplink.Screen_CARD_OFFERS_CATALOG_SCREEN,
				ScreenOptions: &deeplink.Deeplink_CardOffersCatalogScreenOptions{
					CardOffersCatalogScreenOptions: &deeplink.CardOffersCatalogScreenOptions{
						CardType:   deeplink.CardOffersCatalogScreenOptions_DEBIT_CARD,
						CardTypeId: feRewardsPb.CardTypeId_DEBIT_CARD_ID.String(),
					},
				},
			}
		}
		return defaultDl
	case homeTypesPb.IconType_SHORTCUT_CREDIT_CARD_OFFERS:
		if appPlatform == commontypes.Platform_ANDROID && appVersionCode >= s.genconf.RewardsFrontendMeta().MinAndroidAppVersionSupportingCardOfferCatalogV2() ||
			appPlatform == commontypes.Platform_IOS && appVersionCode >= s.genconf.RewardsFrontendMeta().MinIosAppVersionSupportingCardOfferCatalogV2() {
			return s.getCreditCardOffersDeeplink(ctx, defaultDl, actorId)
		}
		return defaultDl
	case homeTypesPb.IconType_SHORTCUT_CONNECTED_ACCOUNT:
		connectAccountDeeplink := &deeplink.Deeplink{Screen: deeplink.Screen_CONNECTED_ACCOUNTS_CONNECT_ACCOUNT_FLOW}
		savingsHomeSummaryDeeplink := &deeplink.Deeplink{Screen: deeplink.Screen_SAVINGS_ACCOUNTS_HOME_SUMMARY_SCREEN}

		getAccountReq := &caPb.GetAccountsRequest{
			ActorId:           actorId,
			AccountFilterList: []caExternalPb.AccountFilter{caExternalPb.AccountFilter_ACCOUNT_FILTER_ACTIVE},
		}
		getAccountsResponse, getAccountsErr := s.beConnectedAccountClient.GetAccounts(ctx, getAccountReq)

		if getAccountsErr != nil {
			return connectAccountDeeplink
		}
		if len(getAccountsResponse.GetAccountDetailsList()) == 0 {
			return connectAccountDeeplink
		}
		return savingsHomeSummaryDeeplink
	case homeTypesPb.IconType_SHORTCUT_SECURED_LOANS:
		if !s.isActorEligibleForSecuredLoans(ctx, actorId) {
			return nil
		}
		return &deeplink.Deeplink{
			Screen: deeplink.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
			ScreenOptions: &deeplink.Deeplink_PreApprovedLoanLandingScreenOptions{
				PreApprovedLoanLandingScreenOptions: &deeplink.PreApprovedLoanLandingScreenOptions{
					LoanHeader: &palEnumFePb.LoanHeader{
						LoanProgram: palEnumFePb.LoanProgram_LOAN_PROGRAM_LAMF,
						Vendor:      palEnumFePb.Vendor_FIFTYFIN,
						LoanType:    palEnumFePb.LoanType_LOAN_TYPE_SECURED_LOAN,
						EventData:   &palEnumFePb.EventData{EntryPoint: palEnumFePb.EntryPoint_ENTRY_POINT_EXPLORE_TAB},
					},
				},
			},
		}
	case homeTypesPb.IconType_SHORTCUT_PRE_APPROVED_LOAN:
		return &deeplink.Deeplink{
			Screen: deeplink.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
			ScreenOptions: &deeplink.Deeplink_PreApprovedLoanLandingScreenOptions{
				PreApprovedLoanLandingScreenOptions: &deeplink.PreApprovedLoanLandingScreenOptions{
					LoanHeader: &palEnumFePb.LoanHeader{
						EventData: &palEnumFePb.EventData{EntryPoint: palEnumFePb.EntryPoint_ENTRY_POINT_EXPLORE_TAB},
					},
				},
			},
		}
	case homeTypesPb.IconType_SHORTCUT_PRE_PAY_LOAN:
		segRes, err := s.segmentClient.IsMemberOfExpressions(ctx, &segmentPb.IsMemberOfExpressionsRequest{
			ActorId:              actorId,
			SegmentIdExpressions: []string{s.genconf.Lending().PreApprovedLoan().PrePayShortcutSegmentExpression()},
		})
		if rpcErr := epifigrpc.RPCError(segRes, err); rpcErr != nil {
			logger.Error(ctx, "error while checking if user belongs to segment", zap.Error(rpcErr))
			return nil
		}
		if segRes.GetSegmentExpressionMembershipMap()[s.genconf.Lending().PreApprovedLoan().PrePayShortcutSegmentExpression()].GetIsActorMember() {
			return defaultDl
		}
		return nil
	case homeTypesPb.IconType_SHORTCUT_PAY_TO_UPI:
		isPayAskFiSearchScreenAllowed, _ := s.evaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_PAY_ASK_FI_SEARCH_SCREEN).WithActorId(actorId))
		if isPayAskFiSearchScreenAllowed && (appPlatform == commontypes.Platform_ANDROID && appVersionCode >= 10000 || appPlatform == commontypes.Platform_IOS && appVersionCode >= 10000) {
			return &deeplink.Deeplink{
				Screen: deeplink.Screen_PAY_ASK_FI_SEARCH_SCREEN,
			}
		}
		return defaultDl
	case homeTypesPb.IconType_SHORTCUT_FI_STORE:
		return nil
	case homeTypesPb.IconType_SHORTCUT_FI_STORE_GIFT_CARDS:
		eligible := s.isAllowedForGroup(ctx, []commontypes.UserGroup{
			commontypes.UserGroup_FI_STORE_INTERNAL,
		}, actorId)
		if eligible {
			return s.getPoshVineDeeplink(ctx, actorId, defaultDl, appPlatform, appVersionCode)
		}
		return nil
	case homeTypesPb.IconType_SHORTCUT_FI_STORE_MILES_EXCHANGE:
		eligible := s.isAllowedForGroup(ctx, []commontypes.UserGroup{
			commontypes.UserGroup_FI_STORE_INTERNAL,
		}, actorId)
		if eligible {
			return s.getPoshVineDeeplink(ctx, actorId, defaultDl, appPlatform, appVersionCode)
		}
		return nil
	case homeTypesPb.IconType_SHORTCUT_AA_SALARY_PLAN:
		// checking if user is b2b verified, if yes then not showing Prime explore icon.
		isUserB2BVerified, isUserB2BVerifiedErr := s.usersClient.GetB2BSalaryProgramVerificationStatus(ctx, &user.GetB2BSalaryProgramVerificationStatusRequest{
			Identifier: &user.GetB2BSalaryProgramVerificationStatusRequest_ActorId{ActorId: actorId},
		})
		if rpcErr := epifigrpc.RPCError(isUserB2BVerified, isUserB2BVerifiedErr); rpcErr != nil {
			logger.Error(ctx, "failed to get b2b verification status", zap.Error(rpcErr))
			return nil
		}
		if isUserB2BVerified.GetIsVerified() {
			return nil
		}
		tieringPitchResp, getTieringPitchErr := s.beTieringClient.GetTieringPitchV2(ctx, &tiering.GetTieringPitchV2Request{
			ActorId: actorId,
		})
		if rpcErr := epifigrpc.RPCError(tieringPitchResp, getTieringPitchErr); rpcErr != nil {
			logger.Error(ctx, "failed to get tiering pitch", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
			return nil
		}

		currTier := tieringPitchResp.GetCurrentTier()
		if currTier.IsSalaryOrSalaryLiteTier() {
			return nil
		}

		if s.getIsTieringV2EnabledForActor(ctx, actorId) {
			return tiering2.AllPlansDeeplink(external.Tier_TIER_FI_AA_SALARY_BAND_3, true)
		}
		isEligible := helper.ShouldPitchAaSalaryTier(ctx, s.genconf, tieringPitchResp.GetCurrentTier())
		if isEligible {
			return defaultDl
		}
		return nil
	case homeTypesPb.IconType_SHORTCUT_BANK_STATEMENT:
		acctResp, err := s.savClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
			Identifier: &savingsPb.GetAccountRequest_ActorId{
				ActorId: actorId,
			},
		})
		if err != nil {
			logger.Error(ctx, "failed to get account by user id", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
			return nil
		}
		return &deeplink.Deeplink{
			Screen: deeplink.Screen_STATEMENT_REQUEST_SCREEN,
			ScreenOptions: &deeplink.Deeplink_StatementRequestOptions{
				StatementRequestOptions: &deeplink.StatementRequestOptions{
					AccountId:   acctResp.GetAccount().GetId(),
					AccountType: accounts.Type_SAVINGS,
				},
			},
		}
	case homeTypesPb.IconType_SHORTCUT_SAVINGS_ACCOUNT:
		saBenefitsDeeplink, dlErr := pkgOnb.GetSABenefitsScreen(ctx)
		if dlErr != nil {
			logger.Error(ctx, "failed to get sa benefits deeplink", zap.Error(dlErr), zap.String(logger.ACTOR_ID_V2, actorId))
		}
		return saBenefitsDeeplink
	case homeTypesPb.IconType_SHORTCUT_PAY_TO_PHONE:
		isUpiMapperEnabled, err := s.evaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_UPI_MAPPER).WithActorId(actorId))
		if err != nil {
			// fall back to Screen_PAY_VIA_MOBILE_NUMBER in case of error
			logger.Error(ctx, "error in evaluating upi mapper feature", zap.Error(err))
			isUpiMapperEnabled = false
		}
		if isUpiMapperEnabled {
			return &deeplink.Deeplink{
				Screen: deeplink.Screen_PAY_SEARCH_SCREEN_V2,
				ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&typesPayPb.PaySearchV2ScreenOptions{
					PaySearchScreenType: typesPayPb.PaySearchScreenType_PAY_SEARCH_SCREEN_TYPE_NUMBER_SEARCH,
				}),
			}
		}
		return &deeplink.Deeplink{
			Screen: deeplink.Screen_PAY_VIA_MOBILE_NUMBER,
		}
	case homeTypesPb.IconType_SHORTCUT_SELF_TRANSFER:
		return s.getDeeplinkForSelfTransfer(ctx, actorId)
	case homeTypesPb.IconType_SHORTCUT_SCAN_AND_PAY:
		return s.getQrDeeplink(ctx, actorId)
	case homeTypesPb.IconType_SHORTCUT_NETWORTH:
		isEnabled, err := s.IsFeatureEnabled(ctx, types.Feature_FEATURE_HOME_NAV_BAR_WEALTH_BUILDER_SECTION, actorId)
		if err != nil {
			logger.Error(ctx, "error while pulling wealth builder widget for explore tab", zap.Error(err))
			return defaultDl
		}
		if isEnabled {
			return &deeplink.Deeplink{
				Screen: deeplink.Screen_WEALTH_BUILDER_LANDING_SCREEN,
			}
		}
		return defaultDl
	case homeTypesPb.IconType_SHORTCUT_AMB:
		isAmbEnabled := s.isAmbEnabledForActor(ctx, actorId)
		if isAmbEnabled {
			return &deeplink.Deeplink{
				Screen: deeplink.Screen_AMB_DETAILS_SCREEN,
			}
		}
		return nil
	case homeTypesPb.IconType_SHORTCUT_UPGRADE_PLANS:
		// handling error gracefully by sending nil for the deeplink
		if !s.isUpgradePlansEnabledForActor(ctx, actorId) {
			logger.InfoForActor(ctx, s.LogWhitelistedActors(), "User is not eligible to see upgrade plans icon", zap.String(logger.ACTOR_ID_V2, actorId))
			return nil
		}
		upgradePlansDeeplink := s.getUpgradePlansDeeplinkForBaseTierActor(ctx, actorId)
		if upgradePlansDeeplink != nil {
			logger.InfoForActor(ctx, s.LogWhitelistedActors(), "User is eligible to see upgrade plans icon", zap.String(logger.ACTOR_ID_V2, actorId))
			return upgradePlansDeeplink
		}
		logger.InfoForActor(ctx, s.LogWhitelistedActors(), "Even though user is eligible to see upgrade plans icon, not seeing it because of error in getting deeplink", zap.String(logger.ACTOR_ID_V2, actorId))
		return nil
	case homeTypesPb.IconType_SHORTCUT_CREDIT_SCORE_ANALYSER:
		isEnabled, err := s.IsFeatureEnabled(ctx, types.Feature_FEATURE_CREDIT_REPORT_MONEY_SECRET, actorId)
		if err != nil {
			logger.Error(ctx, "error while checking if credit report money secret feature is enabled", zap.Error(err))
			return defaultDl
		}
		if isEnabled {
			return secrets.GetSecretAnalyserScreenDeeplink(secretNames.CreditAnalyzerCreditScoreDetails)
		}
		return defaultDl
	}
	return defaultDl
}

func (s *Service) LogWhitelistedActors() *syncmap.Map[string, bool] {
	return s.genconf.ActorsWhitelistedForLogs()
}

func (s *Service) getIsTieringV2EnabledForActor(ctx context.Context, actorId string) bool {
	tieringConfigParamsResp, tieringConfigParamsConfigParamsErr := s.beTieringClient.GetConfigParams(ctx, &tiering.GetConfigParamsRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(tieringConfigParamsResp, tieringConfigParamsConfigParamsErr); rpcErr != nil {
		logger.Info(ctx, "failed to get config params", zap.Error(rpcErr))
		return false
	}
	return tieringConfigParamsResp.GetIsMultipleWaysToEnterTieringEnabledForActor()
}

func (s *Service) getDeeplinkForTieringExploreIcons(ctx context.Context, actorId string, defaultDl *deeplink.Deeplink, iconType homeTypesPb.IconType) *deeplink.Deeplink {
	if s.getIsTieringV2EnabledForActor(ctx, actorId) {

		tieringPitchV2Resp, tieringPitchV2Err := s.beTieringClient.GetTieringPitchV2(ctx, &tiering.GetTieringPitchV2Request{
			ActorId: actorId,
		})
		if rpcErr := epifigrpc.RPCError(tieringPitchV2Resp, tieringPitchV2Err); rpcErr != nil {
			logger.Error(ctx, "failed to get tiering pitch v2", zap.Error(rpcErr))
			return nil
		}
		currTier := tieringPitchV2Resp.GetCurrentTier()

		if currTier.IsSalaryOrSalaryLiteTier() {
			return nil
		}

		var tierToFocus external.Tier
		switch iconType {
		case homeTypesPb.IconType_SHORTCUT_INFINITE_PLAN:
			tierToFocus = external.Tier_TIER_FI_INFINITE
		case homeTypesPb.IconType_SHORTCUT_PLUS_PLAN:
			tierToFocus = external.Tier_TIER_FI_PLUS
		}

		return tiering2.AllPlansDeeplink(tierToFocus, true)

	}
	return defaultDl
}

// getDeeplinkForSelfTransfer returns appropriate deeplink for self-transfer feature based on actor's UPI accounts.
// If actor has more than one eligible account (savings/current), shows self-transfer screen, otherwise shows list account provider screen.
func (s *Service) getDeeplinkForSelfTransfer(ctx context.Context, actorId string) *deeplink.Deeplink {
	isSelfTransferEnabled, err := s.evaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_SELF_TRANSFER).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "error in evaluating self transfer feature", zap.Error(err))
		isSelfTransferEnabled = false
	}
	if !isSelfTransferEnabled {
		return nil
	}

	getAccountsRes, err := s.upiOnboardingClient.GetAccounts(ctx, &upiOnboardingPb.GetAccountsRequest{
		ActorId: actorId,
		AccountStatus: []upiOnbEnumsPb.UpiAccountStatus{
			upiOnbEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
		},
	})
	if err = epifigrpc.RPCError(getAccountsRes, err); err != nil {
		// not showing icon in case of error
		logger.Error(ctx, "failed to fetch the upi accounts for the actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return nil
	}
	countAccountsForSelfTransfer := 0
	allowedAccountTypesForSelfTransfer := []accounts.Type{accounts.Type_SAVINGS, accounts.Type_CURRENT}
	for _, upiAccount := range getAccountsRes.GetAccounts() {
		if lo.Contains(allowedAccountTypesForSelfTransfer, upiAccount.GetAccountType()) {
			countAccountsForSelfTransfer++
		}
	}
	// if only one account (or less) is present then show the list account provider screen
	if countAccountsForSelfTransfer <= 1 {
		return deeplinkv3.GetDeeplinkV3WithoutError(deeplink.Screen_LIST_ACCOUNT_PROVIDER_SCREEN, &upi.ListAccountProviderScreenOptions{
			UpiAccountType: feUpiOnbEnumsPb.UpiAccountType_UPI_ACCOUNT_TYPE_BANK_ACCOUNT,
		})
	}
	// if more than one account is present then show the self transfer screen
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_SELF_TRANSFER_SCREEN,
	}
}

func (s *Service) getPoshVineDeeplink(ctx context.Context, actorId string, defaultDl *deeplink.Deeplink, appPlatform commontypes.Platform, appVersionCode uint32) *deeplink.Deeplink {
	if (appPlatform == commontypes.Platform_ANDROID && appVersionCode >= s.genconf.FiStoreConfig().MinAndroidVersionForWebPageWithCardDetailsScreen()) ||
		(appPlatform == commontypes.Platform_IOS && appVersionCode >= s.genconf.FiStoreConfig().MinIosVersionForWebPageWithCardDetailsScreen()) {
		giftCardUrl := strings.Replace(defaultDl.GetWebPageScreenOptions().GetWebpageUrl(), "https://", "", 1)
		webPageDeeplink, err := s.getWebPageWithScreenDetailsScreen(ctx, actorId, "", fmt.Sprintf("vendor:POSHVINE,target_url:%s", giftCardUrl))
		if err != nil {
			logger.Error(ctx, "error while fetching card details deeplink for fi store gift cards", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
			return nil
		}
		return webPageDeeplink
	}
	return nil
}

func (s *Service) getFiStoreDeeplink(ctx context.Context, actorId string, defaultDl *deeplink.Deeplink, appPlatform commontypes.Platform, appVersionCode uint32) *deeplink.Deeplink {
	// fetch dPanda external webpage url
	res, err := s.offerCatalogServiceClient.GetExternalVendorDynamicWebpageUrl(ctx, &casperPb.GetExternalVendorDynamicWebpageUrlRequest{
		ActorId:    actorId,
		VendorName: casperPb.OfferVendor_DPANDA,
		TargetUrl:  defaultDl.GetWebPageScreenOptions().GetWebpageUrl(),
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		logger.Error(ctx, "error while fetching external vendor webpage url for dpanda", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
		return nil
	}
	externalUrl := res.GetWebpageUrl()

	if s.genconf.RewardsFrontendMeta().IsWebPageWithCardDetailsScreenEnabled() {
		if (appPlatform == commontypes.Platform_ANDROID && appVersionCode >= s.genconf.RewardsFrontendMeta().MinAndroidVersionForWebPageWithCardDetailsScreen()) ||
			(appPlatform == commontypes.Platform_IOS && appVersionCode >= s.genconf.RewardsFrontendMeta().MinIosVersionForWebPageWithCardDetailsScreen()) {
			webPageDeeplink, err := s.getWebPageWithScreenDetailsScreen(ctx, actorId, externalUrl, "")
			if err != nil {
				logger.Error(ctx, "error while fetching card details deeplink for fi store gift cards", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
				return nil
			}
			return webPageDeeplink
		}
	}
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_WEB_PAGE,
		ScreenOptions: &deeplink.Deeplink_WebPageScreenOptions{
			WebPageScreenOptions: &deeplink.WebpageScreenOptions{
				WebpageTitle:             defaultDl.GetWebPageScreenOptions().GetWebpageTitle(),
				WebpageUrl:               externalUrl,
				DisableHardwareBackPress: defaultDl.GetWebPageScreenOptions().GetDisableHardwareBackPress(),
			},
		},
	}
}

func (s *Service) getWebPageWithScreenDetailsScreen(ctx context.Context, actorId string, externalUrl string, requestMetadata string) (*deeplink.Deeplink, error) {
	var (
		debitCardId          string
		showDebitCardDetails bool
	)
	// fetch debit card id to pass in deeplink to show debit card details in webpage.
	cards, cardFetchErr := s.cardProvisioningClient.FetchCards(ctx, &provisioning.FetchCardsRequest{
		Actor:            &types.Actor{Id: actorId},
		IssuingBanks:     []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
		CardStates:       []beCardPb.CardState{beCardPb.CardState_CREATED, beCardPb.CardState_ACTIVATED},
		CardTypes:        []beCardPb.CardType{beCardPb.CardType_DEBIT},
		CardNetworkTypes: []beCardPb.CardNetworkType{beCardPb.CardNetworkType_VISA},
		CardForms:        []beCardPb.CardForm{beCardPb.CardForm_PHYSICAL, beCardPb.CardForm_DIGITAL},
		SortedBy:         beCardPb.CardFieldMask_CARD_UPDATED_AT,
	})
	switch rpcErr := epifigrpc.RPCError(cards, cardFetchErr); {
	case rpc.StatusFromError(rpcErr).IsRecordNotFound():
		showDebitCardDetails = false
	case rpcErr != nil:
		logger.Error(ctx, "error while fetching debit card details", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
		return nil, rpcErr
	case len(cards.GetCards()) == 0:
		showDebitCardDetails = false
	default:
		showDebitCardDetails = true
		debitCardId = cards.GetCards()[0].GetId()
	}
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_WEB_PAGE_WITH_CARD_DETAILS_SCREEN,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&pkgScreenOptionsPb.WebPageWithCardDetailsScreenOptions{
			WebpageTitle:             "Fi Store",
			WebpageUrl:               externalUrl,
			DisableHardwareBackPress: true,
			DisableDropDownAnimation: true,
			ShowUpiDetails:           true,
			ShowCreditCardDetails:    true,
			ShowDebitCardDetails:     showDebitCardDetails,
			DebitCardId:              debitCardId,
			RequestMetadata:          requestMetadata,
		}),
	}, nil
}

// only if actor present in any of the given segments, then we return true
func isActorMemberOfAnySegment(membership map[string]*segmentPb.SegmentMembership, segmentIds []string) bool {
	for _, segmentId := range segmentIds {
		if membership[segmentId].GetSegmentStatus() == segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND && membership[segmentId].GetIsActorMember() {
			return true
		}
	}
	return false
}

// getPrioritizedStoryGroup return the group which occurs earliest in the fiMinutesStoryGroups list
func getPrioritizedStoryGroup(storyGroupsDetails []*story.StoryGroupDetail) model.StoryGroupName {
	for _, group := range fiMinutesStoryGroups {
		for _, groupDetails := range storyGroupsDetails {
			if groupDetails.GetStoryGroupName() == group {
				return group
			}
		}
	}
	return model.StoryGroupName_STORY_GROUP_NAME_UNSPECIFIED
}

// this func can be used to update explore tab icon details based on segment Id or any other use-case
func (s *Service) getAppropriateIconDetails(ctx context.Context, iconType string, actorId string, iconDetails *config.ShortcutIconDetails) *config.ShortcutIconDetails {
	updatedIconDetails := &config.ShortcutIconDetails{
		Title:    iconDetails.Title,
		ImageUrl: iconDetails.ImageUrl,
		Deeplink: iconDetails.Deeplink,
	}
	switch iconType {
	case homeTypesPb.IconType_SHORTCUT_PRE_APPROVED_LOAN.String():
		if s.genconf.Lending().PreApprovedLoan().InstantCashSegmentId() != "" {
			segRes, err := s.segmentClient.IsMember(ctx, &segmentPb.IsMemberRequest{
				ActorId:    actorId,
				SegmentIds: []string{s.genconf.Lending().PreApprovedLoan().InstantCashSegmentId()},
				LatestBy:   timestampPb.Now(),
			})
			if rpcErr := epifigrpc.RPCError(segRes, err); rpcErr != nil {
				// log error and move forward as default values will be shown
				logger.Error(ctx, "error while checking if user belongs to pre approved loan segment", zap.Error(rpcErr))
			}
			if segRes != nil &&
				segRes.GetSegmentMembershipMap()[s.genconf.Lending().PreApprovedLoan().InstantCashSegmentId()].GetSegmentStatus() == segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND &&
				segRes.GetSegmentMembershipMap()[s.genconf.Lending().PreApprovedLoan().InstantCashSegmentId()].GetIsActorMember() {
				updatedIconDetails.Title = "Instant\nCash"
				updatedIconDetails.ImageUrl = "https://epifi-icons.pointz.in/preapprovedloan/instant_loan_explore_tab.png"
			}
		}
	default:
		// do nothing
	}
	return updatedIconDetails
}

func (s *Service) GetExploreSectionsFromExperiment(ctx context.Context, exploreSectionsString string) (*[]*config.ExploreSection, error) {
	exploreSections := &[]*config.ExploreSection{}
	err := json2.Unmarshal([]byte(exploreSectionsString), exploreSections)
	if err != nil {
		logger.Error(ctx, "failed to unmarshal val")
		return nil, err
	}
	return exploreSections, nil
}

// This method first checks if segment release flag is enabled. If it is enabled then it checks if actor is present in the release segment. If not then feature is not released to the actor.
// If segment release flag is disabled then actor eligibility is checked using release evaluator.
func (s *Service) isActorEligibleForSecuredLoans(ctx context.Context, actorId string) bool {
	if s.genconf.Lending().SecuredLoanParams().ReleasedSegmentDetails().IsEnabled() {
		segRes, err := s.segmentClient.IsMemberOfExpressions(ctx, &segmentPb.IsMemberOfExpressionsRequest{
			ActorId:              actorId,
			SegmentIdExpressions: []string{s.genconf.Lending().SecuredLoanParams().ReleasedSegmentDetails().Expression()},
		})
		if rpcErr := epifigrpc.RPCError(segRes, err); rpcErr != nil {
			logger.Error(ctx, "error in segmentClient.IsMemberOfExpressions rpc", zap.Error(rpcErr))
			return false
		}
		if segRes == nil ||
			segRes.GetSegmentExpressionMembershipMap()[s.genconf.Lending().SecuredLoanParams().ReleasedSegmentDetails().Expression()].GetSegmentExpressionStatus() != segmentPb.SegmentExpressionStatus_OK ||
			!segRes.GetSegmentExpressionMembershipMap()[s.genconf.Lending().SecuredLoanParams().ReleasedSegmentDetails().Expression()].GetIsActorMember() {
			return false
		}
	}
	isEligible, err := s.IsFeatureEnabled(ctx, types.Feature_FEATURE_SECURED_LOANS, actorId)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("error checking feature eligibility: %s", types.Feature_FEATURE_SECURED_LOANS), zap.Error(err))
		return false
	}
	return isEligible
}

func (s *Service) getDebitCardUsageDeeplink(ctx context.Context, actorId string) *deeplink.Deeplink {
	//  added a card form check to exclude such users
	cards, cardFetchErr := s.cardProvisioningClient.FetchCards(ctx, &provisioning.FetchCardsRequest{
		Actor:            &types.Actor{Id: actorId},
		IssuingBanks:     []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
		CardStates:       []beCardPb.CardState{beCardPb.CardState_CREATED, beCardPb.CardState_ACTIVATED},
		CardTypes:        []beCardPb.CardType{beCardPb.CardType_DEBIT},
		CardNetworkTypes: []beCardPb.CardNetworkType{beCardPb.CardNetworkType_VISA},
		CardForms:        []beCardPb.CardForm{beCardPb.CardForm_PHYSICAL, beCardPb.CardForm_DIGITAL},
		SortedBy:         beCardPb.CardFieldMask_CARD_UPDATED_AT,
	})
	if te := epifigrpc.RPCError(cards, cardFetchErr); te != nil {
		logger.Error(ctx, "error fetching cards for actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(te))
		return nil
	}
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CARD_USAGE_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CardUsageScreenOptions{
			CardUsageScreenOptions: &deeplink.CardUsageScreenOptions{
				CardId: cards.GetCards()[0].GetId(),
			},
		},
	}
}

func (s *Service) getCreditCardControlsDeeplink(ctx context.Context, actorId string) *deeplink.Deeplink {
	// If user does not have credit card then will return nil
	isCreditCardUser, isCreditCardUserErr := s.beFireflyClient.IsCreditCardUser(ctx, &firefly.IsCreditCardUserRequest{ActorId: actorId})
	if ve := epifigrpc.RPCError(isCreditCardUser, isCreditCardUserErr); ve != nil {
		logger.Error(ctx, "error in determining whether the user has a credit card or not", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(ve))
		return nil
	}
	// user does not have credit card
	if !isCreditCardUser.GetIsCreditCardUser() {
		return nil
	}
	creditCardDetail, creditCardErr := s.beFireflyClient.GetCreditCard(ctx, &firefly.GetCreditCardRequest{GetBy: &firefly.GetCreditCardRequest_ActorId{ActorId: actorId}})
	if ve := epifigrpc.RPCError(creditCardDetail, creditCardErr); ve != nil {
		logger.Error(ctx, "error in fetching credit card details", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(ve))
		return nil
	}
	return fireflyHelper.GetCreditCardControlsScreenDeeplink(creditCardDetail.GetCreditCard().GetId())
}

func (s *Service) getCreditCardOffersDeeplink(ctx context.Context, defaultDl *deeplink.Deeplink, actorId string) *deeplink.Deeplink {
	isCreditCardUser, isCreditCardUserErr := s.beFireflyClient.IsCreditCardUser(ctx, &firefly.IsCreditCardUserRequest{ActorId: actorId})
	if ve := epifigrpc.RPCError(isCreditCardUser, isCreditCardUserErr); ve != nil {
		logger.Error(ctx, "error in determining whether the user has a credit card or not", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(ve))
		return defaultDl
	}
	// user does not have credit card
	if !isCreditCardUser.GetIsCreditCardUser() {
		return defaultDl
	}

	getAccResp, getAccErr := s.ffAccClient.GetAccounts(ctx, &accounting.GetAccountsRequest{GetBy: &accounting.GetAccountsRequest_ActorId{ActorId: actorId}})
	if ve := epifigrpc.RPCError(getAccResp, getAccErr); ve != nil {
		logger.Error(ctx, "failed to fetch account", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(ve))
		return defaultDl
	}

	// The default credit card type is amplifi
	var creditCardType string
	cardProgram := getAccResp.GetAccounts()[0].GetCardProgram()
	switch {
	case pkgFirefly.IsCreditCardProgramSecured(cardProgram):
		creditCardType = feRewardsPb.CardTypeId_SIMPLIFI_CREDIT_CARD_ID.String()
	case pkgFirefly.IsCreditCardProgramMassUnsecured(cardProgram):
		creditCardType = feRewardsPb.CardTypeId_MAGNIFI_CREDIT_CARD_ID.String()
	default:
		creditCardType = feRewardsPb.CardTypeId_AMPLIFI_CREDIT_CARD_ID.String()
	}

	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CARD_OFFERS_CATALOG_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CardOffersCatalogScreenOptions{
			CardOffersCatalogScreenOptions: &deeplink.CardOffersCatalogScreenOptions{
				CardType:   deeplink.CardOffersCatalogScreenOptions_CREDIT_CARD,
				CardTypeId: creditCardType,
			},
		},
	}
}

func (s *Service) getExplorePageTitle(conf *genconf.Text) *commontypes.Text {
	return commontypes.GetTextFromStringFontColourFontStyle(conf.Content(), conf.FontColor(), commontypes.FontStyle_HEADLINE_M)
}

// getSearchUiContainer returns search ui container for explore tab.
func (s *Service) getExploreSearchUiContainer(ctx context.Context) *pb.SearchUiContainer {
	if !s.genconf.HomeExploreConfig().EnableAskFiSection(ctx) {
		return nil
	}
	return &pb.SearchUiContainer{
		SearchTextField: &ui.IconTextComponent{
			LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/home/<USER>/search-icon.png", 24, 24),
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:       "#313234",
				CornerRadius:  20,
				Height:        52,
				TopPadding:    16,
				BottomPadding: 16,
				RightPadding:  137,
				LeftPadding:   16,
			},
			Texts: []*commontypes.Text{
				{
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: "What are you looking for?",
					},
					FontColor: "#929599",
				},
			},
		},
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_ASK_FI_LANDING_PAGE,
			ScreenOptions: &deeplink.Deeplink_AskFiLandingPageOptions{
				AskFiLandingPageOptions: &deeplink.AskFiLandingPageOptions{},
			},
		},
	}
}

// getExploreSearchWidget returns AskFi search widget for explore tab.
func (s *Service) getExploreSearchWidget(ctx context.Context, userType homePkg.UserType) *pb.ExploreSearchWidget {
	if !s.genconf.HomeExploreConfig().EnableAskFiSection(ctx) || homePkg.IsWealthAnalyserUser(userType) {
		return nil
	}
	return &pb.ExploreSearchWidget{
		BgColor: ui.GetBlockColor("#313234"),
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_ASK_FI_LANDING_PAGE,
			ScreenOptions: &deeplink.Deeplink_AskFiLandingPageOptions{
				AskFiLandingPageOptions: &deeplink.AskFiLandingPageOptions{},
			},
		},
		LeftElement: &ui.IconTextComponent{
			LeftVisualElement: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source: &commontypes.VisualElement_Image_Url{
							Url: "https://epifi-icons.pointz.in/quick-link-icons/search_large.png",
						},
						Properties: &commontypes.VisualElementProperties{
							Width:  20,
							Height: 20,
						},
						ImageType:     commontypes.ImageType_PNG,
						RenderingType: nil,
					},
				},
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				LeftPadding:   2,
				TopPadding:    2,
				RightPadding:  2,
				BottomPadding: 2,
			},
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_ASK_FI_LANDING_PAGE,
				ScreenOptions: &deeplink.Deeplink_AskFiLandingPageOptions{
					AskFiLandingPageOptions: &deeplink.AskFiLandingPageOptions{},
				},
			},
		},
		TextContent: &pb.ExploreSearchBarTextContent{
			Queries:   []string{"what are you looking for?", "shopping spends", "my transactions", "upi id", "swiggy spends", "my investments", "what is form 16"},
			FontColor: "#B9B9B9",
		},
	}
}

// nolint: funlen
func (s *Service) getFeedbackSection(ctx context.Context, conf *genconf.ExploreFeedbackSectionConfig, exploreWidgetSections []*pb.HomeExploreWidget) (*header.FeedbackEngineInfo, []*pb.HomeExploreWidget, error) {
	if !s.genconf.HomeExploreConfig().EnableFeedbackSection(ctx) {
		return nil, exploreWidgetSections, nil
	}

	feedbackEngineInfo := &header.FeedbackEngineInfo{
		FlowIdDetails: &header.FlowIdentifierDetails{
			FlowIdentifierType: types.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_DROP_OFF,
			FlowIdentifier:     types.FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_HOME_EXPLORE.String(),
		},
	}

	screenOptionsFeedbackEngineMarshalled, err := anyPb.New(
		&cxScreenTypes.FeedbackEngineScreenOptions{
			FlowIdentifierDetails: &cxScreenTypes.FlowIdentifierDetails{
				FlowIdentifierType: types.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_CTA,
				FlowIdentifier:     types.FeedbackCTAFlowIdentifier_FEEDBACK_CTA_FLOW_IDENTIFIER_HOME_EXPLORE.String(),
			},
		})
	if err != nil {
		return nil, nil, err
	}

	feedbackSection := &pb.ExploreFeedbackSection{
		Highlight:        commontypes.GetVisualElementFromUrlHeightAndWidth(conf.Highlight().Url(), conf.Highlight().Properties().Height(), conf.Highlight().Properties().Width()),
		FeedbackTitle:    commontypes.GetTextFromStringFontColourFontStyle(conf.Title().Content(), conf.Title().FontColor(), commontypes.FontStyle_HEADLINE_M),
		FeedbackSubtitle: commontypes.GetTextFromStringFontColourFontStyle(conf.Subtitle().Content(), conf.Subtitle().FontColor(), commontypes.FontStyle_SUBTITLE_S),
		FeedbackButton: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: conf.Button().Texts()[0].Content,
					},
					FontColor: conf.Button().Texts()[0].FontColor,
				},
			},
			Deeplink: &deeplink.Deeplink{
				Screen:          deeplink.Screen_FEEDBACK_ENGINE_SCREEN,
				ScreenOptionsV2: screenOptionsFeedbackEngineMarshalled,
			},
			LeftImgTxtPadding: conf.Button().LeftImgTxtPadding(),
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:       conf.Button().ContainerProperties().BgColor(),
				CornerRadius:  conf.Button().ContainerProperties().CornerRadius(),
				Height:        conf.Button().ContainerProperties().Height(),
				Width:         conf.Button().ContainerProperties().Width(),
				LeftPadding:   conf.Button().ContainerProperties().Padding().Left(ctx),
				RightPadding:  conf.Button().ContainerProperties().Padding().Right(ctx),
				TopPadding:    conf.Button().ContainerProperties().Padding().Top(ctx),
				BottomPadding: conf.Button().ContainerProperties().Padding().Bottom(ctx),
			},
		},
		BgColour: &ui.BackgroundColour{
			Colour: &ui.BackgroundColour_BlockColour{
				BlockColour: conf.BgColor(),
			},
		},
	}

	exploreWidgetSections = append(exploreWidgetSections, &pb.HomeExploreWidget{
		Section: &pb.HomeExploreWidget_FeedbackSection{FeedbackSection: feedbackSection},
	})

	return feedbackEngineInfo, exploreWidgetSections, nil
}

func (s *Service) getStickyIcon(userType homePkg.UserType, conf *genconf.ShortcutStickyIcon) *pb.Icon {
	if homePkg.IsWealthAnalyserUser(userType) {
		return nil
	}

	return &pb.Icon{
		Title:      commontypes.GetTextFromStringFontColourFontStyle(conf.Title().Content(), conf.Title().FontColor(), commontypes.FontStyle_BUTTON_S),
		ActionType: pb.Icon_ACTION_TYPE_DEEPLINK,
		Action: &pb.Icon_Deeplink{
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_HOME_SHORTCUT_OPTIONS_SCREEN,
			},
		},
		BgColour:      &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: conf.BgColor()}},
		BgColourV2:    widget.GetLinearGradientBackgroundColour(45, []*widget.ColorStop{{Color: "#006D5B", StopPercentage: 0}, {Color: "#00B899", StopPercentage: 100}}),
		VisualElement: commontypes.GetVisualElementImageFromUrl(conf.VisualElement().Url()),
	}
}

func (s *Service) getShortcutUpdatedToastMessage(conf *genconf.Text) *commontypes.Text {
	return &commontypes.Text{
		FontColor:    conf.FontColor(),
		BgColor:      conf.BgColor(),
		DisplayValue: &commontypes.Text_PlainString{PlainString: conf.Content()},
		FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
	}
}

func (s *Service) getHomeExploreSectionsConfig(ctx context.Context, actorId string) (homePkg.UserType, []*config.ExploreSection) {
	// start default config with home revamp explore sections
	homeExploreSectionsConfig := deepcopy.Copy(s.conf.HomeRevampParams.ExploreSections).([]*config.ExploreSection)
	// get suitable user type
	userType, getDetailsRes, _, err := homePkg.GetSuitableUserType(ctx, &homePkg.GetSuitableUserTypeRequest{
		ActorId: actorId,
		ExternalDeps: &common.ExternalDependencies{
			Evaluator:            s.evaluator,
			OnboardingClient:     s.onboardingClient,
			QuestSdkClient:       s.questSdkClient,
			UserAttributeFetcher: s.userAttributeFetcher,
			NetWorthClient:       s.networthClient,
		},
	})
	if err != nil {
		logger.Error(ctx, "failed to get suitable user type in getHomeExploreSectionsConfig", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		// Return default config, mimicking old errgroup failure path
		return homePkg.UserTypeUnspecified, homeExploreSectionsConfig
	}
	userSelectedSoftIntents := getDetailsRes.GetDetails().GetStageMetadata().GetSoftIntentSelectionMetadata().GetSelection()

	// check if user has credit card
	isCreditCardUserResp, err := s.beFireflyClient.IsCreditCardUser(ctx, &firefly.IsCreditCardUserRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(isCreditCardUserResp, err); rpcErr != nil {
		logger.Error(ctx, "failed to determine if user has credit card", zap.Error(err))
		return homePkg.UserTypeUnspecified, homeExploreSectionsConfig
	}

	// Remove credit card section if user doesn't have a credit card
	if !isCreditCardUserResp.GetIsCreditCardUser() {
		homeExploreSectionsConfig = lo.Filter(homeExploreSectionsConfig, func(section *config.ExploreSection, _ int) bool {
			return section.SectionType != homeTypesPb.ExploreSectionType_EXPLORE_SECTION_TYPE_CREDIT_CARDS.String()
		})
	}

	// set default config to use fi lite explore sections if the user is fi lite user
	if getDetailsRes.GetDetails().GetFiLiteDetails().GetIsEnabled() == commontypes.BooleanEnum_TRUE {
		homeExploreSectionsConfig = s.conf.LiteHomeRevampParams.ExploreSections
	}

	switch userType {
	case homePkg.UserTypeFiLite:
		selectedSoftIntentsBasedExploreSections, err := s.getSoftIntentBasedExploreSections(ctx, actorId, userSelectedSoftIntents)
		if err != nil {
			logger.Error(ctx, "failed to get explore sections based on user selected soft intents", zap.Error(err))
			return userType, homeExploreSectionsConfig
		}
		return userType, selectedSoftIntentsBasedExploreSections
	case homePkg.UserTypeFiNR:
		return userType, s.conf.HomeRevampParams.NRExploreSections
	case homePkg.UserTypeWealthAnalyser, homePkg.UserTypeNoAssetConnectedWealthAnalyser:
		return userType, s.conf.HomeRevampParams.WealthAnalyserExploreSections
	case homePkg.UserTypeFiSAWithD0To7, homePkg.UserTypeFiSAWithD8To14:
		return userType, s.getSoftIntentBasedExploreSectionsForNewSAUsers(userSelectedSoftIntents, homeExploreSectionsConfig)
	}
	return userType, homeExploreSectionsConfig
}

// shouldDisplayPhysicalCardOrderShortcut determines if SHORTCUT_ORDER_PHYSICAL_DEBIT_CARD should be shown to the user.
func (s *Service) shouldDisplayPhysicalCardOrderShortcut(ctx context.Context, actorId string) bool {
	physicalCardDispatchExists, err := s.physicalCardDispatchExists(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error determining visibility of SHORTCUT_ORDER_PHYSICAL_DEBIT_CARD for actor", zap.Error(err))
		// shortcut will be disabled by default
		return false
	}
	return !physicalCardDispatchExists
}

// shouldDisplayDebitCardOffersShortcut determines if SHORTCUT_CARD_OFFERS should be shown to the user.
func (s *Service) shouldDisplayDebitCardOffersShortcut(ctx context.Context, actorId string) bool {
	physicalCardDispatchExists, err := s.physicalCardDispatchExists(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error determining visibility of SHORTCUT_CARD_OFFERS for actor", zap.Error(err))
		// shortcut will be displayed by default
		return true
	}
	return physicalCardDispatchExists
}

// physicalCardDispatchExists checks if user already have a physical debit card or their exists a physical card dispatch request in non-terminal state.
// steps to determine if the shortcut should be displayed:
//  1. Check if current debit card is not physical card
//  2. Check if physical card dispatch request does not exist or has already FAILED
func (s *Service) physicalCardDispatchExists(ctx context.Context, actorId string) (bool, error) {
	fetchCardResp, err := s.cardProvisioningClient.FetchCards(ctx, &provisioning.FetchCardsRequest{
		Actor:      &types.Actor{Id: actorId},
		CardStates: []beCardPb.CardState{beCardPb.CardState_ACTIVATED, beCardPb.CardState_CREATED},
		Limit:      1,
	})
	switch {
	case epifigrpc.RPCError(fetchCardResp, err) != nil && !fetchCardResp.GetStatus().IsRecordNotFound():
		return false, fmt.Errorf("failed to fetch current debit card for actor, err: %w", epifigrpc.RPCError(fetchCardResp, err))
	case fetchCardResp.GetStatus().IsRecordNotFound(), fetchCardResp.GetCards()[0].GetForm() == beCardPb.CardForm_PHYSICAL:
		return true, nil
	}

	physicalDispatchStatusResp, err := s.cardProvisioningClient.GetPhysicalCardDispatchStatus(ctx, &provisioning.GetPhysicalCardDispatchStatusRequest{
		CardId: fetchCardResp.GetCards()[0].GetId(),
	})
	switch {
	case err != nil, physicalDispatchStatusResp.GetStatus().IsInternal():
		return false, fmt.Errorf("failed to fetch physcial dispatch request for debit card, err: %w, card Id: %s", epifigrpc.RPCError(physicalDispatchStatusResp, err), fetchCardResp.GetCards()[0].GetId())
	case physicalDispatchStatusResp.GetStatus().IsRecordNotFound(), physicalDispatchStatusResp.GetStatus().GetCode() == uint32(provisioning.GetPhysicalCardDispatchStatusResponse_FAILED),
		physicalDispatchStatusResp.GetStatus().GetCode() == uint32(provisioning.GetPhysicalCardDispatchStatusResponse_NOT_FOUND):
		return false, nil
	default:
		return true, nil
	}
}
