package home

import (
	"context"
	"sort"
	"sync"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/async/waitgroup"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/frontend/header"
	pb "github.com/epifi/gamma/api/frontend/home"
)

var navigationBarIconTypePriorityMap = map[string]int{
	"CARD":     0,
	"BORROW":   1,
	"INVEST":   2,
	"PAY":      3,
	"DISCOVER": 4,
}

// GetNavigationBarHighlights returns navigation bar highlights to draw user's attention to the bar for a new feature launch, etc
func (s *Service) GetNavigationBarHighlights(ctx context.Context, req *pb.GetNavigationBarHighlightsRequest) (*pb.GetNavigationBarHighlightsResponse, error) {
	navigationBarHighlightServices := s.getNavigationBarHighlightServices()

	// get navigation bar highlights from all registered services
	var wg sync.WaitGroup
	navigationBarHighlights := make(chan *pb.GetNavigationBarHighlightsResponse_NavigationBarHighlight, len(navigationBarHighlightServices))
	for _, navigationBarHighlightService := range navigationBarHighlightServices {
		_navigationBarHighlightService := navigationBarHighlightService
		wg.Add(1)
		goroutine.Run(ctx, time.Second, func(fctx context.Context) {
			defer wg.Done()
			navigationBarHighlightRes, err := _navigationBarHighlightService.GetNavigationBarHighlight(fctx, &pb.GetNavigationBarHighlightRequest{
				ActorId:         req.GetReq().GetAuth().GetActorId(),
				RequestMetadata: req.GetRequestMetadata(),
			})
			if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
				logger.Error(fctx, "error while getting navigation bar highlight", zap.Error(err))
				return
			}
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				logger.Debug(fctx, "no navigation highlight returned")
				return
			}
			if _, found := navigationBarHighlightRes.GetNavigationBarHighlight().GetMetaData()["Reason"]; !found {
				logger.Error(ctx, "error while pushing navigation highlight since reason is empty", zap.String("iconType", navigationBarHighlightRes.GetNavigationBarHighlight().GetIconType().String()))
				return
			}
			navigationBarHighlights <- navigationBarHighlightRes.GetNavigationBarHighlight()
		})
	}
	waitgroup.SafeWaitWithDefaultTimeout(&wg)
	close(navigationBarHighlights)

	// return if there are no navigation bar highlights
	if len(navigationBarHighlights) == 0 {
		return &pb.GetNavigationBarHighlightsResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusRecordNotFound()},
		}, nil
	}

	// get prioritised list of navigation bar highlights
	navigationBarHighlightsList := s.getPrioritisedNavigationBarHighlights(lo.ChannelToSlice(navigationBarHighlights))

	return &pb.GetNavigationBarHighlightsResponse{
		RespHeader:              &header.ResponseHeader{Status: rpc.StatusOk()},
		NavigationBarHighlights: navigationBarHighlightsList,
	}, nil
}

// getPrioritisedNavigationBarHighlights sorts navigation bar highlights according to navigation bar icon type
func (s *Service) getPrioritisedNavigationBarHighlights(navigationBarHighlights []*pb.GetNavigationBarHighlightsResponse_NavigationBarHighlight) []*pb.GetNavigationBarHighlightsResponse_NavigationBarHighlight {
	sort.Slice(navigationBarHighlights, func(i, j int) bool {
		return navigationBarIconTypePriorityMap[navigationBarHighlights[i].GetIconType().String()] < navigationBarIconTypePriorityMap[navigationBarHighlights[j].GetIconType().String()]
	})
	return navigationBarHighlights
}

// getNavigationBarHighlightServices returns list of services registered to return navigation bar highlight
func (s *Service) getNavigationBarHighlightServices() []pb.INavigationBarHighlightService {
	return []pb.INavigationBarHighlightService{
		s.plNavigationBarHighlightSvc,
		s.ussNavigationBarHighlightSvc,
		s.rewardsNavigationBarHighlightSvc,
		s.networthNavBarHighlightSvc,
	}
}
