package home

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"

	mockNetworthPb "github.com/epifi/gamma/api/insights/networth/mocks"

	types "github.com/epifi/gamma/api/typesv2"
	homePkg "github.com/epifi/gamma/frontend/pkg/home"
	"github.com/epifi/gamma/pkg/feature/release"

	feUpiOnbEnumsPb "github.com/epifi/gamma/api/frontend/upi/onboarding/enums"
	typesPayPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/pay/pay_search_screen_v2"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/upi"
	upionboardingpb "github.com/epifi/gamma/api/upi/onboarding"

	"github.com/epifi/gamma/api/tiering"
	tieringMocks "github.com/epifi/gamma/api/tiering/mocks"

	"context"
	"reflect"
	"testing"

	productPb "github.com/epifi/gamma/api/product"

	productMock "github.com/epifi/gamma/api/product/mocks"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/rpc"
	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/epificontext"
	questSdkGenConf "github.com/epifi/be-common/quest/sdk/config/genconf"

	actorMocks "github.com/epifi/gamma/api/actor/mocks"
	"github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/connected_account/external"
	connectedAccountMocks "github.com/epifi/gamma/api/connected_account/mocks"
	analyserPb "github.com/epifi/gamma/api/frontend/analyser"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	feHeader "github.com/epifi/gamma/api/frontend/header"
	pb "github.com/epifi/gamma/api/frontend/home"
	palEnumFePb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	segmentPb "github.com/epifi/gamma/api/segment"
	segmentMock "github.com/epifi/gamma/api/segment/mocks"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/networth"
	"github.com/epifi/gamma/api/typesv2/home"
	"github.com/epifi/gamma/api/typesv2/ui"
	mock4 "github.com/epifi/gamma/api/upi/onboarding/mocks"
	userPb "github.com/epifi/gamma/api/user"
	groupMock "github.com/epifi/gamma/api/user/group/mocks"
	userMocks "github.com/epifi/gamma/api/user/mocks"
	"github.com/epifi/gamma/api/user/onboarding"
	mocks3 "github.com/epifi/gamma/api/user/onboarding/mocks"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	mockRelease "github.com/epifi/gamma/pkg/feature/release/mocks"
	pkgUser "github.com/epifi/gamma/pkg/user"
	pkgUserMocks "github.com/epifi/gamma/pkg/user/mocks"
	questSdk "github.com/epifi/gamma/quest/sdk"
)

var (
	actorIdSample1 = "act101"
	actorIdSample2 = "act102"
)

type args struct {
	ctx context.Context
	req *pb.GetHomeShortcutsRequest
}

func TestService_GetHomeShortcuts(t *testing.T) {
	t.Parallel()
	loanSegmentId := "c41acc21-f2e0-4ca7-a7fc-edcf956cfdda"
	ccSegmentId := "58fb617a-bf81-4163-90a9-06d50b1b917f"
	usStocksSegmentId := "1a8cc5c9-1010-4a10-8649-5b1ac1fb9fd0"
	prepayLoanSegmentId := "65e992ff-e163-497f-9a30-44b647aaa65e"
	type mockStruct struct {
		mockUser                    *userMocks.MockUsersClient
		mockReleaseEvaluator        *mockRelease.MockIEvaluator
		mockConnectedAccountsClient *connectedAccountMocks.MockConnectedAccountClient
		mockOnbClient               *mocks3.MockOnboardingClient
		mockUpiOnboardingClient     *mock4.MockUpiOnboardingClient
		mockSegmentClient           *segmentMock.MockSegmentationServiceClient
		mockUserAttributesFetcher   *pkgUserMocks.MockUserAttributesFetcher
		mockProductClient           *productMock.MockProductClient
		mockTieringClient           *tieringMocks.MockTieringClient
		mockNetworthClient          *mockNetworthPb.MockNetWorthClient
	}

	tests := []struct {
		name       string
		setupMocks func(ctx context.Context, mocks mockStruct)
		args       args
		want       *pb.GetHomeShortcutsResponse
		wantErr    bool
	}{
		{
			name: "Should successfully get home shortcuts",
			args: args{
				req: &pb.GetHomeShortcutsRequest{
					Req: &header.RequestHeader{
						Auth: &feHeader.AuthHeader{
							ActorId: actorIdSample1,
							Device: &commontypes.Device{
								Platform: commontypes.Platform_ANDROID,
							},
						},
					},
				},
			},
			setupMocks: func(ctx context.Context, mocks mockStruct) {
				mocks.mockUser.EXPECT().GetUserPreferences(ctx, getMockUserPreferencesRequest_HomeShortcutsPreferenceType()).
					Return(getMockUserPreferencesResponseSample1(), nil)
				mocks.mockTieringClient.EXPECT().GetConfigParams(ctx, getMockGetConfigParamsRequest()).Return(getMockGetConfigParamsResponseSample1(), nil).AnyTimes()
				mocks.mockOnbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    actorIdSample1,
					CachedData: true}).
					Return(&onboarding.GetDetailsResponse{
						Details: &onboarding.OnboardingDetails{
							StageMetadata: &onboarding.StageMetadata{
								IntentSelectionMetadata: &onboarding.IntentSelectionMetadata{
									Selection: onboarding.OnboardingIntent_ONBOARDING_INTENT_FI_LITE,
								},
							},
						},
						Status: rpc.StatusOk(),
					}, nil).Times(1)
				mocks.mockUserAttributesFetcher.EXPECT().IsNonResidentUser(gomock.Any(), &pkgUser.IsNonResidentUserRequest{
					ActorId: actorIdSample1,
				}).Return(&pkgUser.IsNonResidentUserResponse{
					IsNonResidentUser: false,
				}, nil)
			},
			want: &pb.GetHomeShortcutsResponse{
				RespHeader:           &header.ResponseHeader{Status: rpcPb.StatusOk()},
				ShortcutIcons:        getMockHomeIconsFromResponseSample1(),
				MaximumNoOfShortcuts: 4,
				Title: &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: "Shortcuts",
					},
					FontColor: "#333333",
					FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_M},
				},
			},
		},
		{
			name: "should return default shortcuts for fi lite users",
			args: args{
				req: &pb.GetHomeShortcutsRequest{
					Req: &header.RequestHeader{
						Auth: &feHeader.AuthHeader{
							ActorId: actorIdSample1,
							Device: &commontypes.Device{
								Platform: commontypes.Platform_ANDROID,
							},
						},
					},
				},
			},
			setupMocks: func(ctx context.Context, mocks mockStruct) {
				mocks.mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_HOME_NAV_BAR_WEALTH_BUILDER_SECTION).WithActorId(actorIdSample1)).Return(false, nil).AnyTimes()
				mocks.mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_CREDIT_REPORT_MONEY_SECRET).WithActorId(actorIdSample1)).Return(false, nil).AnyTimes()
				mocks.mockUser.EXPECT().GetUserPreferences(ctx, getMockUserPreferencesRequest_HomeShortcutsPreferenceType()).
					Return(getMockUserPreferencesResponse_NoShortcutsPreference(), nil)
				mocks.mockOnbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    actorIdSample1,
					CachedData: true}).
					Return(&onboarding.GetDetailsResponse{
						Details: &onboarding.OnboardingDetails{
							StageMetadata: &onboarding.StageMetadata{
								IntentSelectionMetadata: &onboarding.IntentSelectionMetadata{
									Selection: onboarding.OnboardingIntent_ONBOARDING_INTENT_FI_LITE,
								},
							},
						},
						Status: rpc.StatusOk(),
					}, nil).Times(1)
				mocks.mockConnectedAccountsClient.EXPECT().GetAccounts(gomock.Any(), &connected_account.GetAccountsRequest{
					ActorId: actorIdSample1,
					AccountFilterList: []external.AccountFilter{
						external.AccountFilter_ACCOUNT_FILTER_ACTIVE,
					},
				}).Return(&connected_account.GetAccountsResponse{
					Status: rpc.StatusOk(),
					AccountDetailsList: []*external.AccountDetails{
						{
							AccountId: "acc-1",
						},
						{
							AccountId: "acc-2",
						},
					},
				}, nil)
				mocks.mockUserAttributesFetcher.EXPECT().IsNonResidentUser(gomock.Any(), &pkgUser.IsNonResidentUserRequest{
					ActorId: actorIdSample1,
				}).Return(&pkgUser.IsNonResidentUserResponse{
					IsNonResidentUser: false,
				}, nil)
				mocks.mockProductClient.EXPECT().GetProductsStatus(gomock.Any(), &productPb.GetProductsStatusRequest{
					ActorId: actorIdSample1,
					ProductTypes: []productPb.ProductType{
						productPb.ProductType_PRODUCT_TYPE_TPAP,
					},
				}).Return(&productPb.GetProductsStatusResponse{Status: rpc.StatusOk()}, nil).AnyTimes()
				mocks.mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_WB_MAGIC_IMPORT).WithActorId(actorIdSample1)).Return(true, nil).AnyTimes()
				mocks.mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_FI_MCP_TOTP_CODE).WithActorId(actorIdSample1)).Return(true, nil).AnyTimes()
				mocks.mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_NET_WORTH_AI_ICONS).WithActorId(actorIdSample1)).Return(false, nil).AnyTimes()
				mocks.mockTieringClient.EXPECT().GetConfigParams(ctx, getMockGetConfigParamsRequest()).Return(getMockGetConfigParamsResponseSample1(), nil).AnyTimes()
				mocks.mockSegmentClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentPb.IsMemberResponse{Status: rpc.StatusOk(), SegmentMembershipMap: map[string]*segmentPb.SegmentMembership{}}, nil).AnyTimes()
			},
			want: &pb.GetHomeShortcutsResponse{
				ShortcutIcons:        getMockDefaultFiLiteHomeIconsFromResponseSample(),
				MaximumNoOfShortcuts: 4,
				Title: &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: "Shortcuts",
					},
					FontColor: "#333333",
					FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_M},
				},
				RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
			},
		},
		{
			name: "Zero state, when home shortcut preference values list was empty",
			args: args{
				req: &pb.GetHomeShortcutsRequest{
					Req: &header.RequestHeader{
						Auth: &feHeader.AuthHeader{
							ActorId: actorIdSample1,
							Device: &commontypes.Device{
								Platform: commontypes.Platform_ANDROID,
							},
						},
					},
				},
			},
			setupMocks: func(ctx context.Context, mocks mockStruct) {
				mocks.mockUser.EXPECT().GetUserPreferences(ctx, getMockUserPreferencesRequest_HomeShortcutsPreferenceType()).
					Return(getMockUserPreferencesResponse_EmptyShortcutsPreference(), nil)
				mocks.mockOnbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    actorIdSample1,
					CachedData: true}).
					Return(&onboarding.GetDetailsResponse{
						Details: &onboarding.OnboardingDetails{
							StageMetadata: &onboarding.StageMetadata{
								IntentSelectionMetadata: &onboarding.IntentSelectionMetadata{
									Selection: onboarding.OnboardingIntent_ONBOARDING_INTENT_FI_LITE,
								},
							},
						},
						Status: rpc.StatusOk(),
					}, nil).Times(1)
				mocks.mockUserAttributesFetcher.EXPECT().IsNonResidentUser(gomock.Any(), &pkgUser.IsNonResidentUserRequest{
					ActorId: actorIdSample1,
				}).Return(&pkgUser.IsNonResidentUserResponse{
					IsNonResidentUser: false,
				}, nil)
			},
			want: &pb.GetHomeShortcutsResponse{
				RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
				ZeroState:  getZeroStateIconForHomeShortcuts(),
			},
		},
		{
			name: "Default home shortcuts, when the home shortcuts preference isn't set",
			args: args{
				req: &pb.GetHomeShortcutsRequest{
					Req: &header.RequestHeader{
						Auth: &feHeader.AuthHeader{
							ActorId: actorIdSample1,
							Device: &commontypes.Device{
								Platform: commontypes.Platform_ANDROID,
							},
						},
					},
				},
			},
			setupMocks: func(ctx context.Context, mocks mockStruct) {
				mocks.mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_CREDIT_REPORT_MONEY_SECRET).WithActorId(actorIdSample1)).Return(false, nil).AnyTimes()
				mocks.mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_WB_MAGIC_IMPORT).WithActorId(actorIdSample1)).Return(false, nil).AnyTimes()
				mocks.mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_FI_MCP_TOTP_CODE).WithActorId(actorIdSample1)).Return(false, nil).AnyTimes()
				mocks.mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_NET_WORTH_AI_ICONS).WithActorId(actorIdSample1)).Return(false, nil).AnyTimes()
				mocks.mockTieringClient.EXPECT().GetConfigParams(ctx, getMockGetConfigParamsRequest()).Return(getMockGetConfigParamsResponseSample1(), nil).AnyTimes()
				mocks.mockUser.EXPECT().GetUserPreferences(ctx, getMockUserPreferencesRequest_HomeShortcutsPreferenceType()).
					Return(getMockUserPreferencesResponse_NoShortcutsPreference(), nil)
				mocks.mockOnbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    actorIdSample1,
					CachedData: true}).
					Return(&onboarding.GetDetailsResponse{
						Status: rpc.StatusOk(),
						Details: &onboarding.OnboardingDetails{
							FeatureDetails: &onboarding.FeatureDetails{
								FeatureInfo: map[string]*onboarding.FeatureInfo{
									onboarding.Feature_FEATURE_SA.String(): {
										FeatureStatus: onboarding.FeatureStatus_FEATURE_STATUS_ACTIVE,
									},
								},
							},
						}}, nil).Times(2)
				mocks.mockUpiOnboardingClient.EXPECT().GetAccounts(gomock.Any(), gomock.Any()).Return(&upionboardingpb.GetAccountsResponse{
					Status: rpc.StatusOk(),
				}, nil).Times(1)
				mocks.mockSegmentClient.EXPECT().IsMember(ctx, &segmentPb.IsMemberRequest{
					ActorId:    actorIdSample1,
					SegmentIds: []string{loanSegmentId, ccSegmentId, usStocksSegmentId, prepayLoanSegmentId},
				}).Return(&segmentPb.IsMemberResponse{
					Status: rpcPb.StatusOk(),
					SegmentMembershipMap: map[string]*segmentPb.SegmentMembership{
						loanSegmentId: {
							SegmentStatus: segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND,
							IsActorMember: false,
						},
						ccSegmentId: {
							SegmentStatus: segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND,
							IsActorMember: true,
						},
						usStocksSegmentId: {
							SegmentStatus: segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND,
							IsActorMember: false,
						},
						prepayLoanSegmentId: {
							SegmentStatus: segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND,
							IsActorMember: false,
						},
					},
				}, nil)
				mocks.mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil).AnyTimes()
				mocks.mockUserAttributesFetcher.EXPECT().IsNonResidentUser(gomock.Any(), &pkgUser.IsNonResidentUserRequest{
					ActorId: actorIdSample1,
				}).Return(&pkgUser.IsNonResidentUserResponse{
					IsNonResidentUser: false,
				}, nil)
			},
			want: &pb.GetHomeShortcutsResponse{
				RespHeader:           &header.ResponseHeader{Status: rpc.StatusOk()},
				ShortcutIcons:        getMockDefaultHomeIconsFromResponseSample(),
				MaximumNoOfShortcuts: 4,
				Title: &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: "Shortcuts",
					},
					FontColor: "#333333",
					FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_M},
				},
			},
		},
		{
			name: "Default home shortcuts, when the home shortcuts preference isn't set",
			args: args{
				req: &pb.GetHomeShortcutsRequest{
					Req: &header.RequestHeader{
						Auth: &feHeader.AuthHeader{
							ActorId: actorIdSample1,
							Device: &commontypes.Device{
								Platform: commontypes.Platform_ANDROID,
							},
						},
					},
				},
			},
			setupMocks: func(ctx context.Context, mocks mockStruct) {
				mocks.mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_CREDIT_REPORT_MONEY_SECRET).WithActorId(actorIdSample1)).Return(false, nil).AnyTimes()
				mocks.mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_WB_MAGIC_IMPORT).WithActorId(actorIdSample1)).Return(false, nil).AnyTimes()
				mocks.mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_FI_MCP_TOTP_CODE).WithActorId(actorIdSample1)).Return(false, nil).AnyTimes()
				mocks.mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_NET_WORTH_AI_ICONS).WithActorId(actorIdSample1)).Return(false, nil).AnyTimes()
				mocks.mockTieringClient.EXPECT().GetConfigParams(ctx, getMockGetConfigParamsRequest()).Return(getMockGetConfigParamsResponseSample1(), nil).AnyTimes()
				mocks.mockUser.EXPECT().GetUserPreferences(ctx, getMockUserPreferencesRequest_HomeShortcutsPreferenceType()).
					Return(getMockUserPreferencesResponse_NoShortcutsPreference(), nil)
				mocks.mockOnbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    actorIdSample1,
					CachedData: true}).
					Return(&onboarding.GetDetailsResponse{
						Status: rpc.StatusOk(),
					}, nil).Times(2)
				mocks.mockUpiOnboardingClient.EXPECT().GetAccounts(gomock.Any(), gomock.Any()).Return(&upionboardingpb.GetAccountsResponse{
					Status: rpc.StatusOk(),
				}, nil).Times(1)
				mocks.mockSegmentClient.EXPECT().IsMember(ctx, &segmentPb.IsMemberRequest{
					ActorId:    actorIdSample1,
					SegmentIds: []string{loanSegmentId, ccSegmentId, usStocksSegmentId, prepayLoanSegmentId},
				}).Return(&segmentPb.IsMemberResponse{
					Status: rpcPb.StatusOk(),
					SegmentMembershipMap: map[string]*segmentPb.SegmentMembership{
						loanSegmentId: {
							SegmentStatus: segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND,
							IsActorMember: false,
						},
						ccSegmentId: {
							SegmentStatus: segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND,
							IsActorMember: true,
						},
						usStocksSegmentId: {
							SegmentStatus: segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND,
							IsActorMember: false,
						},
						prepayLoanSegmentId: {
							SegmentStatus: segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND,
							IsActorMember: false,
						},
					},
				}, nil)
				mocks.mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil).AnyTimes()
				mocks.mockUserAttributesFetcher.EXPECT().IsNonResidentUser(gomock.Any(), &pkgUser.IsNonResidentUserRequest{
					ActorId: actorIdSample1,
				}).Return(&pkgUser.IsNonResidentUserResponse{
					IsNonResidentUser: false,
				}, nil)
			},
			want: &pb.GetHomeShortcutsResponse{
				RespHeader:           &header.ResponseHeader{Status: rpc.StatusOk()},
				ShortcutIcons:        getMockDefaultHomeIconsFromResponseSample(),
				MaximumNoOfShortcuts: 4,
				Title: &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: "Shortcuts",
					},
					FontColor: "#333333",
					FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_M},
				},
			},
		},
		{
			name: "Default home shortcuts, when no record is found in preference",
			args: args{
				req: &pb.GetHomeShortcutsRequest{
					Req: &header.RequestHeader{
						Auth: &feHeader.AuthHeader{
							ActorId: actorIdSample1,
							Device: &commontypes.Device{
								Platform: commontypes.Platform_ANDROID,
							},
						},
					},
				},
			},
			setupMocks: func(ctx context.Context, mocks mockStruct) {
				mocks.mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_CREDIT_REPORT_MONEY_SECRET).WithActorId(actorIdSample1)).Return(false, nil).AnyTimes()
				mocks.mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_WB_MAGIC_IMPORT).WithActorId(actorIdSample1)).Return(false, nil).AnyTimes()
				mocks.mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_FI_MCP_TOTP_CODE).WithActorId(actorIdSample1)).Return(false, nil).AnyTimes()
				mocks.mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_NET_WORTH_AI_ICONS).WithActorId(actorIdSample1)).Return(false, nil).AnyTimes()
				mocks.mockTieringClient.EXPECT().GetConfigParams(ctx, getMockGetConfigParamsRequest()).Return(getMockGetConfigParamsResponseSample1(), nil).AnyTimes()
				mocks.mockUser.EXPECT().GetUserPreferences(ctx, getMockUserPreferencesRequest_HomeShortcutsPreferenceType()).
					Return(getMockUserPreferencesResponse_RecordNotFound(), nil)
				mocks.mockOnbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    actorIdSample1,
					CachedData: true}).
					Return(&onboarding.GetDetailsResponse{
						Details: &onboarding.OnboardingDetails{
							FeatureDetails: &onboarding.FeatureDetails{
								FeatureInfo: map[string]*onboarding.FeatureInfo{
									onboarding.Feature_FEATURE_SA.String(): {
										FeatureStatus: onboarding.FeatureStatus_FEATURE_STATUS_ACTIVE,
									},
								},
							},
						},
						Status: rpc.StatusOk(),
					}, nil).Times(2)
				mocks.mockUpiOnboardingClient.EXPECT().GetAccounts(gomock.Any(), gomock.Any()).Return(&upionboardingpb.GetAccountsResponse{
					Status: rpc.StatusOk(),
				}, nil).Times(1)
				mocks.mockSegmentClient.EXPECT().IsMember(ctx, &segmentPb.IsMemberRequest{
					ActorId:    actorIdSample1,
					SegmentIds: []string{loanSegmentId, ccSegmentId, usStocksSegmentId, prepayLoanSegmentId},
				}).Return(&segmentPb.IsMemberResponse{
					Status: rpcPb.StatusOk(),
					SegmentMembershipMap: map[string]*segmentPb.SegmentMembership{
						loanSegmentId: {
							SegmentStatus: segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND,
							IsActorMember: false,
						},
						ccSegmentId: {
							SegmentStatus: segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND,
							IsActorMember: true,
						},
						usStocksSegmentId: {
							SegmentStatus: segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND,
							IsActorMember: false,
						},
						prepayLoanSegmentId: {
							SegmentStatus: segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND,
							IsActorMember: false,
						},
					},
				}, nil)
				mocks.mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil).AnyTimes()
				mocks.mockUserAttributesFetcher.EXPECT().IsNonResidentUser(gomock.Any(), &pkgUser.IsNonResidentUserRequest{
					ActorId: actorIdSample1,
				}).Return(&pkgUser.IsNonResidentUserResponse{
					IsNonResidentUser: false,
				}, nil)
			},
			want: &pb.GetHomeShortcutsResponse{
				RespHeader:           &header.ResponseHeader{Status: rpc.StatusOk()},
				ShortcutIcons:        getMockDefaultHomeIconsFromResponseSample(),
				MaximumNoOfShortcuts: 4,
				Title: &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: "Shortcuts",
					},
					FontColor: "#333333",
					FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_M},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			mockActorClient := actorMocks.NewMockActorClient(ctrl)
			mockUsersClient := userMocks.NewMockUsersClient(ctrl)
			mockUserGroupClient := groupMock.NewMockGroupClient(ctrl)
			mockReleaseEvaluator := mockRelease.NewMockIEvaluator(ctrl)
			mockConnectedAccountsClient := connectedAccountMocks.NewMockConnectedAccountClient(ctrl)
			mockOnb := mocks3.NewMockOnboardingClient(ctrl)
			mockSegmentClient := segmentMock.NewMockSegmentationServiceClient(ctrl)
			mockQuestSdkConf, _ := questSdkGenConf.NewConfig()
			mockQuestSdkClient := questSdk.NewClient(mockQuestSdkConf, nil, nil, nil, nil)
			mockUserAttributesFetcher := pkgUserMocks.NewMockUserAttributesFetcher(ctrl)
			mockProductClient := productMock.NewMockProductClient(ctrl)
			mockTieringClient := tieringMocks.NewMockTieringClient(ctrl)
			mockUpiOnboardingClient := mock4.NewMockUpiOnboardingClient(ctrl)
			mockNetworthClient := mockNetworthPb.NewMockNetWorthClient(ctrl)
			mock := mockStruct{
				mockUser:                    mockUsersClient,
				mockReleaseEvaluator:        mockReleaseEvaluator,
				mockConnectedAccountsClient: mockConnectedAccountsClient,
				mockOnbClient:               mockOnb,
				mockSegmentClient:           mockSegmentClient,
				mockUserAttributesFetcher:   mockUserAttributesFetcher,
				mockProductClient:           mockProductClient,
				mockTieringClient:           mockTieringClient,
				mockUpiOnboardingClient:     mockUpiOnboardingClient,
				mockNetworthClient:          mockNetworthClient,
			}
			ctx := context.Background()
			ctx = epificontext.CtxWithAppPlatform(ctx, commontypes.Platform_ANDROID)
			ctx = epificontext.CtxWithAppVersionCode(ctx, "277")
			tt.setupMocks(ctx, mock)
			s := &Service{
				actorClient:              mockActorClient,
				usersClient:              mockUsersClient,
				userGroupClient:          mockUserGroupClient,
				evaluator:                mockReleaseEvaluator,
				conf:                     conf,
				genconf:                  gconf,
				beConnectedAccountClient: mockConnectedAccountsClient,
				onboardingClient:         mockOnb,
				segmentClient:            mockSegmentClient,
				questSdkClient:           mockQuestSdkClient,
				userAttributeFetcher:     mockUserAttributesFetcher,
				productClient:            mockProductClient,
				beTieringClient:          mockTieringClient,
				upiOnboardingClient:      mockUpiOnboardingClient,
				networthClient:           mockNetworthClient,
			}
			got, err := s.GetHomeShortcuts(ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetHomeShortcuts() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("GetHomeShortcuts() got = %v,\n want %v\n diff %s", got, tt.want, diff)
			}
		})
	}
}

func TestService_GetHomeShortcuts_AppVersionMismatch_PlatformAndroid(t *testing.T) {
	tests := []struct {
		name    string
		args    args
		want    *pb.GetHomeShortcutsResponse
		wantErr bool
	}{
		{
			name: "Should test for invalid android app version",
			args: args{
				req: &pb.GetHomeShortcutsRequest{
					Req: &header.RequestHeader{
						Auth: &feHeader.AuthHeader{
							ActorId: actorIdSample1,
							Device: &commontypes.Device{
								Platform: commontypes.Platform_ANDROID,
							},
						},
					},
				},
			},
			want: &pb.GetHomeShortcutsResponse{
				RespHeader: &header.ResponseHeader{Status: rpc.StatusRecordNotFoundWithDebugMsg("App version not valid for home shortcuts")},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			ctx := context.Background()
			ctx = epificontext.CtxWithAppPlatform(ctx, commontypes.Platform_ANDROID)
			ctx = epificontext.CtxWithAppVersionCode(ctx, "276")
			s := &Service{
				genconf: gconf,
			}
			got, err := s.GetHomeShortcuts(ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetHomeShortcuts() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetHomeShortcuts() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetHomeShortcuts_AppVersionMismatch_PlatformIOS(t *testing.T) {
	tests := []struct {
		name    string
		args    args
		want    *pb.GetHomeShortcutsResponse
		wantErr bool
	}{
		{
			name: "Should test for invalid android app version",
			args: args{
				req: &pb.GetHomeShortcutsRequest{
					Req: &header.RequestHeader{
						Auth: &feHeader.AuthHeader{
							ActorId: actorIdSample1,
							Device: &commontypes.Device{
								Platform: commontypes.Platform_ANDROID,
							},
						},
					},
				},
			},
			want: &pb.GetHomeShortcutsResponse{
				RespHeader: &header.ResponseHeader{Status: rpc.StatusRecordNotFoundWithDebugMsg("App version not valid for home shortcuts")},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			ctx := context.Background()
			ctx = epificontext.CtxWithAppPlatform(ctx, commontypes.Platform_IOS)
			ctx = epificontext.CtxWithAppVersionCode(ctx, "378")
			s := &Service{
				genconf: gconf,
			}
			got, err := s.GetHomeShortcuts(ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetHomeShortcuts() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetHomeShortcuts() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func getMockHomeIconsFromResponseSample1() []*pb.Icon {
	return []*pb.Icon{
		{
			IconImage: &commontypes.Image{
				ImageUrl:  "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/InstantLoan.png",
				ImageType: commontypes.ImageType_PNG,
				Width:     36,
				Height:    36,
			},
			VisualElement: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source:     &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/InstantLoan.png"},
						ImageType:  commontypes.ImageType_PNG,
						Properties: &commontypes.VisualElementProperties{Height: 36, Width: 36},
					},
				},
			},
			Title: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Instant\nLoan"},
				FontColor:    colors.ColorOnDarkLowEmphasis,
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			},
			Id: "SHORTCUT_PRE_APPROVED_LOAN",
			Action: &pb.Icon_Deeplink{
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
					ScreenOptions: &deeplink.Deeplink_PreApprovedLoanLandingScreenOptions{
						PreApprovedLoanLandingScreenOptions: &deeplink.PreApprovedLoanLandingScreenOptions{
							LoanHeader: &palEnumFePb.LoanHeader{
								EventData: &palEnumFePb.EventData{EntryPoint: palEnumFePb.EntryPoint_ENTRY_POINT_EXPLORE_TAB},
							},
						},
					},
				},
			},
			BgColour: &ui.BackgroundColour{
				Colour: &ui.BackgroundColour_BlockColour{
					BlockColour: "#FFFFFF",
				},
			},
			Shadow: &ui.Shadow{
				Height: 4,
				Colour: &ui.BackgroundColour{
					Colour: &ui.BackgroundColour_BlockColour{
						BlockColour: "#CED2D6",
					},
				},
			},
			BorderColor: pb.GetHomeExploreIconBorderColor(),
			BgColourV2: widget.GetLinearGradientBackgroundColour(0, []*widget.ColorStop{
				{
					Color:          "#E3F7F2",
					StopPercentage: 0,
				},
				{
					Color:          "#F4FCFA",
					StopPercentage: 100,
				},
			}),
		},
		{
			IconImage: &commontypes.Image{
				ImageUrl:  "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/MutualFunds.png",
				ImageType: commontypes.ImageType_PNG,
				Width:     36,
				Height:    36,
			},
			VisualElement: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source:     &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/MutualFunds.png"},
						ImageType:  commontypes.ImageType_PNG,
						Properties: &commontypes.VisualElementProperties{Height: 36, Width: 36},
					},
				},
			},
			Title: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Mutual\nFunds"},
				FontColor:    colors.ColorOnDarkLowEmphasis,
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			},
			Id: "SHORTCUT_MF",
			Action: &pb.Icon_Deeplink{
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_MUTUAL_FUND_COLLECTIONS_LANDING_SCREEN,
				},
			},
			BgColour: &ui.BackgroundColour{
				Colour: &ui.BackgroundColour_BlockColour{
					BlockColour: "#FFFFFF",
				},
			},
			Shadow: &ui.Shadow{
				Height: 4,
				Colour: &ui.BackgroundColour{
					Colour: &ui.BackgroundColour_BlockColour{
						BlockColour: "#CED2D6",
					},
				},
			},
			BorderColor: pb.GetHomeExploreIconBorderColor(),
			BgColourV2: widget.GetLinearGradientBackgroundColour(0, []*widget.ColorStop{
				{
					Color:          "#E3F7F2",
					StopPercentage: 0,
				},
				{
					Color:          "#F4FCFA",
					StopPercentage: 100,
				},
			}),
		},
		getEditIconResponse(),
	}
}

func getMockDefaultHomeIconsFromResponseSample() []*pb.Icon {
	return []*pb.Icon{
		getExploreFiIcon(),
		{
			IconImage: &commontypes.Image{
				ImageUrl:  "https://epifi-icons.pointz.in/home-v2/ExploreIcons/payViaNumber.png",
				ImageType: commontypes.ImageType_PNG,
				Width:     36,
				Height:    36,
			},
			VisualElement: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source:     &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/home-v2/ExploreIcons/payViaNumber.png"},
						ImageType:  commontypes.ImageType_PNG,
						Properties: &commontypes.VisualElementProperties{Height: 36, Width: 36},
					},
				},
			},
			Title: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Pay Phone\nNumber"},
				FontColor:    colors.ColorOnDarkLowEmphasis,
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			},
			Id: "SHORTCUT_PAY_TO_PHONE",
			Action: &pb.Icon_Deeplink{
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_PAY_SEARCH_SCREEN_V2,
					ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&typesPayPb.PaySearchV2ScreenOptions{
						PaySearchScreenType: typesPayPb.PaySearchScreenType_PAY_SEARCH_SCREEN_TYPE_NUMBER_SEARCH,
					}),
				},
			},
			BgColour: &ui.BackgroundColour{
				Colour: &ui.BackgroundColour_BlockColour{
					BlockColour: "#FFFFFF",
				},
			},
			Shadow: &ui.Shadow{
				Height: 4,
				Colour: &ui.BackgroundColour{
					Colour: &ui.BackgroundColour_BlockColour{
						BlockColour: "#CED2D6",
					},
				},
			},
			BorderColor: pb.GetHomeExploreIconBorderColor(),
			BgColourV2: widget.GetLinearGradientBackgroundColour(0, []*widget.ColorStop{
				{
					Color:          "#E3F7F2",
					StopPercentage: 0,
				},
				{
					Color:          "#F4FCFA",
					StopPercentage: 100,
				},
			}),
		},
		{
			IconImage: &commontypes.Image{
				ImageUrl:  "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/CreditCard.png",
				ImageType: commontypes.ImageType_PNG,
				Width:     36,
				Height:    36,
			},
			VisualElement: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source:     &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/CreditCard.png"},
						ImageType:  commontypes.ImageType_PNG,
						Properties: &commontypes.VisualElementProperties{Height: 36, Width: 36},
					},
				},
			},
			Title: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Credit\nCard"},
				FontColor:    colors.ColorOnDarkLowEmphasis,
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			},
			Id: "SHORTCUT_CREDIT_CARD",
			Action: &pb.Icon_Deeplink{
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_CREDIT_CARD_DASHBOARD_SCREEN,
				},
			},
			BgColour: &ui.BackgroundColour{
				Colour: &ui.BackgroundColour_BlockColour{
					BlockColour: "#FFFFFF",
				},
			},
			Shadow: &ui.Shadow{
				Height: 4,
				Colour: &ui.BackgroundColour{
					Colour: &ui.BackgroundColour_BlockColour{
						BlockColour: "#CED2D6",
					},
				},
			},
			BorderColor: pb.GetHomeExploreIconBorderColor(),
			BgColourV2: widget.GetLinearGradientBackgroundColour(0, []*widget.ColorStop{
				{
					Color:          "#E3F7F2",
					StopPercentage: 0,
				},
				{
					Color:          "#F4FCFA",
					StopPercentage: 100,
				},
			}),
		},
		{
			IconImage: &commontypes.Image{
				ImageUrl:  "https://epifi-icons.pointz.in/home-v2/ExploreIcons/SelfTransfer.png",
				ImageType: commontypes.ImageType_PNG,
				Width:     36,
				Height:    36,
			},
			VisualElement: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source:     &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/home-v2/ExploreIcons/SelfTransfer.png"},
						ImageType:  commontypes.ImageType_PNG,
						Properties: &commontypes.VisualElementProperties{Height: 36, Width: 36},
					},
				},
			},
			Title: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Self\nTransfer"},
				FontColor:    colors.ColorOnDarkLowEmphasis,
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			},
			Id: "SHORTCUT_SELF_TRANSFER",
			Action: &pb.Icon_Deeplink{
				Deeplink: deeplinkv3.GetDeeplinkV3WithoutError(deeplink.Screen_LIST_ACCOUNT_PROVIDER_SCREEN, &upi.ListAccountProviderScreenOptions{
					UpiAccountType: feUpiOnbEnumsPb.UpiAccountType_UPI_ACCOUNT_TYPE_BANK_ACCOUNT,
				}),
			},
			BgColour: &ui.BackgroundColour{
				Colour: &ui.BackgroundColour_BlockColour{
					BlockColour: "#FFFFFF",
				},
			},
			Shadow: &ui.Shadow{
				Height: 4,
				Colour: &ui.BackgroundColour{
					Colour: &ui.BackgroundColour_BlockColour{
						BlockColour: "#CED2D6",
					},
				},
			},
			BorderColor: pb.GetHomeExploreIconBorderColor(),
			BgColourV2: widget.GetLinearGradientBackgroundColour(0, []*widget.ColorStop{
				{
					Color:          "#E3F7F2",
					StopPercentage: 0,
				},
				{
					Color:          "#F4FCFA",
					StopPercentage: 100,
				},
			}),
		},
		{
			IconImage: &commontypes.Image{
				ImageUrl:  "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/CreditScore.png",
				ImageType: commontypes.ImageType_PNG,
				Width:     36,
				Height:    36,
			},
			VisualElement: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source:     &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/CreditScore.png"},
						ImageType:  commontypes.ImageType_PNG,
						Properties: &commontypes.VisualElementProperties{Height: 36, Width: 36},
					},
				},
			},
			Title: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Credit\nScore"},
				FontColor:    colors.ColorOnDarkLowEmphasis,
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			},
			Id: "SHORTCUT_CREDIT_SCORE_ANALYSER",
			Action: &pb.Icon_Deeplink{
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_ANALYSER_SCREEN,
					ScreenOptions: &deeplink.Deeplink_AnalyserScreenOptions{
						AnalyserScreenOptions: &deeplink.AnalyserScreenOptions{
							AnalyserName: analyserPb.AnalyserName_ANALYSER_NAME_CREDIT_SCORE.String(),
						},
					},
				},
			},
			BgColour: &ui.BackgroundColour{
				Colour: &ui.BackgroundColour_BlockColour{
					BlockColour: "#FFFFFF",
				},
			},
			Shadow: &ui.Shadow{
				Height: 4,
				Colour: &ui.BackgroundColour{
					Colour: &ui.BackgroundColour_BlockColour{
						BlockColour: "#CED2D6",
					},
				},
			},
			BorderColor: pb.GetHomeExploreIconBorderColor(),
			BgColourV2: widget.GetLinearGradientBackgroundColour(0, []*widget.ColorStop{
				{
					Color:          "#E3F7F2",
					StopPercentage: 0,
				},
				{
					Color:          "#F4FCFA",
					StopPercentage: 100,
				},
			}),
		},
		getPlusIconResponse(),
	}
}

func getMockDefaultFiLiteHomeIconsFromResponseSample() []*pb.Icon {
	return []*pb.Icon{
		{
			IconImage: &commontypes.Image{
				ImageUrl:  "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/ConnectYourBanks.png",
				ImageType: commontypes.ImageType_PNG,
				Width:     36,
				Height:    36,
			},
			VisualElement: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source:     &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/ConnectYourBanks.png"},
						ImageType:  commontypes.ImageType_PNG,
						Properties: &commontypes.VisualElementProperties{Height: 36, Width: 36},
					},
				},
			},
			Title: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "All Bank\nBalances"},
				FontColor:    colors.ColorOnDarkLowEmphasis,
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			},
			Id: "SHORTCUT_CONNECTED_ACCOUNT",
			Action: &pb.Icon_Deeplink{
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_SAVINGS_ACCOUNTS_HOME_SUMMARY_SCREEN,
				},
			},
			BgColour: &ui.BackgroundColour{
				Colour: &ui.BackgroundColour_BlockColour{
					BlockColour: "#FFFFFF",
				},
			},
			Shadow: &ui.Shadow{
				Height: 4,
				Colour: &ui.BackgroundColour{
					Colour: &ui.BackgroundColour_BlockColour{
						BlockColour: "#CED2D6",
					},
				},
			},
			BorderColor: pb.GetHomeExploreIconBorderColor(),
			BgColourV2: widget.GetLinearGradientBackgroundColour(0, []*widget.ColorStop{
				{
					Color:          "#E3F7F2",
					StopPercentage: 0,
				},
				{
					Color:          "#F4FCFA",
					StopPercentage: 100,
				},
			}),
		},
		{
			IconImage: &commontypes.Image{
				ImageUrl:  "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/CreditScore.png",
				ImageType: commontypes.ImageType_PNG,
				Width:     36,
				Height:    36,
			},
			VisualElement: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source:     &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/CreditScore.png"},
						ImageType:  commontypes.ImageType_PNG,
						Properties: &commontypes.VisualElementProperties{Height: 36, Width: 36},
					},
				},
			},
			Title: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Credit\nScore"},
				FontColor:    colors.ColorOnDarkLowEmphasis,
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			},
			Id: "SHORTCUT_CREDIT_SCORE_ANALYSER",
			Action: &pb.Icon_Deeplink{
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_ANALYSER_SCREEN,
					ScreenOptions: &deeplink.Deeplink_AnalyserScreenOptions{
						AnalyserScreenOptions: &deeplink.AnalyserScreenOptions{
							AnalyserName: analyserPb.AnalyserName_ANALYSER_NAME_CREDIT_SCORE.String(),
						},
					},
				},
			},
			BgColour: &ui.BackgroundColour{
				Colour: &ui.BackgroundColour_BlockColour{
					BlockColour: "#FFFFFF",
				},
			},
			Shadow: &ui.Shadow{
				Height: 4,
				Colour: &ui.BackgroundColour{
					Colour: &ui.BackgroundColour_BlockColour{
						BlockColour: "#CED2D6",
					},
				},
			},
			BorderColor: pb.GetHomeExploreIconBorderColor(),
			BgColourV2: widget.GetLinearGradientBackgroundColour(0, []*widget.ColorStop{
				{
					Color:          "#E3F7F2",
					StopPercentage: 0,
				},
				{
					Color:          "#F4FCFA",
					StopPercentage: 100,
				},
			}),
		},
		{
			IconImage: &commontypes.Image{
				ImageUrl:  "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/NetWorth.png",
				ImageType: commontypes.ImageType_PNG,
				Width:     36,
				Height:    36,
			},
			VisualElement: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source:     &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/NetWorth.png"},
						ImageType:  commontypes.ImageType_PNG,
						Properties: &commontypes.VisualElementProperties{Height: 36, Width: 36},
					},
				},
			},
			Title: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Net\nWorth"},
				FontColor:    colors.ColorOnDarkLowEmphasis,
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			},
			Id: "SHORTCUT_NETWORTH",
			Action: &pb.Icon_Deeplink{
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_NET_WORTH_HUB_SCREEN,
					ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&networth.NetWorthHubScreenOptions{
						LoadingNessage: &commontypes.Text{
							FontColor: colors.ColorOnDarkHighEmphasis,
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: "Taking you to Net Worth",
							},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_HEADLINE_L,
							},
						},
					}),
				},
			},
			BgColour: &ui.BackgroundColour{
				Colour: &ui.BackgroundColour_BlockColour{
					BlockColour: "#FFFFFF",
				},
			},
			Shadow: &ui.Shadow{
				Height: 4,
				Colour: &ui.BackgroundColour{
					Colour: &ui.BackgroundColour_BlockColour{
						BlockColour: "#CED2D6",
					},
				},
			},
			BorderColor: pb.GetHomeExploreIconBorderColor(),
			BgColourV2: widget.GetLinearGradientBackgroundColour(0, []*widget.ColorStop{
				{
					Color:          "#E3F7F2",
					StopPercentage: 0,
				},
				{
					Color:          "#F4FCFA",
					StopPercentage: 100,
				},
			}),
		},
		getPlusIconResponse(),
	}
}

func getEditIconResponse() *pb.Icon {
	return &pb.Icon{
		IconImage: &commontypes.Image{
			ImageUrl:  "https://epifi-icons.pointz.in/home/<USER>/shortcut-edit-icon-re-re-re.png",
			ImageType: commontypes.ImageType_PNG,
			Width:     36,
			Height:    36,
		},
		VisualElement: &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source:     &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/home/<USER>/shortcut-edit-icon-re-re-re.png"},
					ImageType:  commontypes.ImageType_PNG,
					Properties: &commontypes.VisualElementProperties{Height: 36, Width: 36},
				},
			},
		},
		Title: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: "Edit\nShortcuts"},
			FontColor:    colors.ColorOnDarkLowEmphasis,
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
		},
		Action: &pb.Icon_Deeplink{
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_HOME_SHORTCUT_OPTIONS_SCREEN,
			},
		},
		BgColour: &ui.BackgroundColour{
			Colour: &ui.BackgroundColour_BlockColour{
				BlockColour: "#FFFFFF",
			},
		},
	}
}

func getPlusIconResponse() *pb.Icon {
	return &pb.Icon{
		IconImage: &commontypes.Image{
			ImageUrl:  "https://epifi-icons.pointz.in/home/<USER>/shortcuts-plus-icon-re.png",
			ImageType: commontypes.ImageType_PNG,
			Width:     36,
			Height:    36,
		},
		VisualElement: &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source:     &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/home/<USER>/shortcuts-plus-icon-re.png"},
					ImageType:  commontypes.ImageType_PNG,
					Properties: &commontypes.VisualElementProperties{Height: 36, Width: 36},
				},
			},
		},
		Title: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: "Add\nShortcuts"},
			FontColor:    colors.ColorOnDarkLowEmphasis,
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
		},
		Action: &pb.Icon_Deeplink{
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_HOME_SHORTCUT_OPTIONS_SCREEN,
			},
		},
		BgColour: &ui.BackgroundColour{
			Colour: &ui.BackgroundColour_BlockColour{
				BlockColour: "#FFFFFF",
			},
		},
	}
}

func getMockUserPreferencesRequest_HomeShortcutsPreferenceType() *userPb.GetUserPreferencesRequest {
	return &userPb.GetUserPreferencesRequest{
		ActorId:         actorIdSample1,
		PreferenceTypes: []userPb.PreferenceType{userPb.PreferenceType_PREFERENCE_TYPE_HOME_SHORTCUTS},
	}
}

func getMockGetConfigParamsRequest() *tiering.GetConfigParamsRequest {
	return &tiering.GetConfigParamsRequest{ActorId: actorIdSample1}
}

func getMockGetConfigParamsResponseSample1() *tiering.GetConfigParamsResponse {
	return &tiering.GetConfigParamsResponse{
		Status: rpcPb.StatusOk(),
		IsMultipleWaysToEnterTieringEnabledForActor: true,
	}
}
func getMockUserPreferencesResponseSample1() *userPb.GetUserPreferencesResponse {
	return &userPb.GetUserPreferencesResponse{Status: rpcPb.StatusOk(), UserPreferences: []*userPb.UserPreference{{
		PreferenceType: userPb.PreferenceType_PREFERENCE_TYPE_HOME_SHORTCUTS,
		PreferenceValue: &userPb.PreferenceValue{PrefVal: &userPb.PreferenceValue_HomeShortcutsPreference{
			HomeShortcutsPreference: &userPb.ShortcutsPreference{
				IconTypes: []home.IconType{
					home.IconType_SHORTCUT_PRE_APPROVED_LOAN,
					home.IconType_SHORTCUT_MF,
				}},
		}},
	}}}
}

func getMockUserPreferencesResponse_RecordNotFound() *userPb.GetUserPreferencesResponse {
	return &userPb.GetUserPreferencesResponse{Status: rpcPb.StatusRecordNotFound()}
}

func getMockUserPreferencesResponse_EmptyShortcutsPreference() *userPb.GetUserPreferencesResponse {
	return &userPb.GetUserPreferencesResponse{Status: rpcPb.StatusOk(), UserPreferences: []*userPb.UserPreference{{
		PreferenceType: userPb.PreferenceType_PREFERENCE_TYPE_HOME_SHORTCUTS,
		PreferenceValue: &userPb.PreferenceValue{PrefVal: &userPb.PreferenceValue_HomeShortcutsPreference{
			HomeShortcutsPreference: &userPb.ShortcutsPreference{
				IconTypes: []home.IconType{}},
		}},
	}}}
}

func getMockUserPreferencesResponse_NoShortcutsPreference() *userPb.GetUserPreferencesResponse {
	return &userPb.GetUserPreferencesResponse{Status: rpcPb.StatusOk()}
}

func TestService_getRandomIndexForDefaultShortcutForUser(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx     context.Context
		actorId string
		length  int
	}
	tests := []struct {
		name     string
		args     args
		maxIndex int32
	}{
		{
			name: "should return generated index in the range from 0 to 5 both inclusive",
			args: args{
				ctx:     context.Background(),
				actorId: actorIdSample1,
				length:  6,
			},
			maxIndex: 5,
		},
		{
			name: "Should return generated index in the range from 0 to 10 both inclusive",
			args: args{
				ctx:     context.Background(),
				actorId: actorIdSample2,
				length:  11,
			},
			maxIndex: 10,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{}
			result := int32(0)
			if result = s.getRandomIndexForDefaultShortcutForUser(tt.args.ctx, tt.args.actorId, tt.args.length); result > tt.maxIndex {
				t.Errorf("getRandomIndexForDefaultShortcutForUser() = %v, want %v", result, tt.maxIndex)
			}
			// using loop to check consistency of hash algo
			for i := 0; i < 3; i++ {
				if got := s.getRandomIndexForDefaultShortcutForUser(tt.args.ctx, tt.args.actorId, tt.args.length); got != result {
					t.Errorf("getRandomIndexForDefaultShortcutForUser() = %v, want %v", got, result)
				}
			}
		})
	}
}

func TestService_getRemainingDefaultShortcuts(t *testing.T) {
	t.Parallel()
	ctrl := gomock.NewController(t)
	mockReleaseEvaluator := mockRelease.NewMockIEvaluator(ctrl)
	type args struct {
		ctx                    context.Context
		actorId                string
		remainingShortcutSlots int
	}
	tests := []struct {
		name       string
		fields     fields
		args       args
		setupMocks func()
		want       []int32
	}{
		{
			name: "should return array of remaining shortcut with only self transfer enum value",
			args: args{
				ctx:                    context.Background(),
				actorId:                actorIdSample1,
				remainingShortcutSlots: 1,
			},
			setupMocks: func() {
				mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_WB_MAGIC_IMPORT).WithActorId(actorIdSample1)).Return(true, nil).AnyTimes()
				mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_FI_MCP_TOTP_CODE).WithActorId(actorIdSample1)).Return(true, nil).AnyTimes()
				mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_NET_WORTH_AI_ICONS).WithActorId(actorIdSample1)).Return(false, nil).AnyTimes()
			},
			want: []int32{76},
		},
		{
			name: "should return array of remaining shortcut with self transfer and net worth enum values",
			args: args{
				ctx:                    context.Background(),
				actorId:                actorIdSample1,
				remainingShortcutSlots: 2,
			},
			setupMocks: func() {
				mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_WB_MAGIC_IMPORT).WithActorId(actorIdSample1)).Return(true, nil).AnyTimes()
				mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_FI_MCP_TOTP_CODE).WithActorId(actorIdSample1)).Return(true, nil).AnyTimes()
				mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_NET_WORTH_AI_ICONS).WithActorId(actorIdSample1)).Return(false, nil).AnyTimes()
			},
			want: []int32{76, 36},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				evaluator: mockReleaseEvaluator,
			}
			tt.setupMocks()
			if got := s.getRemainingDefaultShortcuts(tt.args.ctx, tt.args.actorId, tt.args.remainingShortcutSlots); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getRemainingDefaultShortcuts() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_getDefaultShortcutsForSAUsers(t *testing.T) {
	t.Parallel()
	loanSegmentId := "c41acc21-f2e0-4ca7-a7fc-edcf956cfdda"
	ccSegmentId := "58fb617a-bf81-4163-90a9-06d50b1b917f"
	usStocksSegmentId := "1a8cc5c9-1010-4a10-8649-5b1ac1fb9fd0"
	prepayLoanSegmentId := "65e992ff-e163-497f-9a30-44b647aaa65e"
	ctrl := gomock.NewController(t)
	mockSegmentClient := segmentMock.NewMockSegmentationServiceClient(ctrl)
	mockReleaseEvaluator := mockRelease.NewMockIEvaluator(ctrl)
	ctx := context.Background()
	type args struct {
		ctx     context.Context
		actorId string
	}
	tests := []struct {
		name       string
		args       args
		want       []int32
		setupMocks func()
	}{
		{
			name: "Should return shortcuts for the case when actor belongs to CC segment",
			args: args{
				ctx:     ctx,
				actorId: actorIdSample1,
			},
			setupMocks: func() {
				mockSegmentClient.EXPECT().IsMember(ctx, &segmentPb.IsMemberRequest{
					ActorId:    actorIdSample1,
					SegmentIds: []string{loanSegmentId, ccSegmentId, usStocksSegmentId, prepayLoanSegmentId},
				}).Return(&segmentPb.IsMemberResponse{
					Status: rpcPb.StatusOk(),
					SegmentMembershipMap: map[string]*segmentPb.SegmentMembership{
						loanSegmentId: {
							SegmentStatus: segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND,
							IsActorMember: false,
						},
						ccSegmentId: {
							SegmentStatus: segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND,
							IsActorMember: true,
						},
						usStocksSegmentId: {
							SegmentStatus: segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND,
							IsActorMember: false,
						},
						prepayLoanSegmentId: {
							SegmentStatus: segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND,
							IsActorMember: false,
						},
					},
				}, nil)
				mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_WB_MAGIC_IMPORT).WithActorId(actorIdSample1)).Return(true, nil).AnyTimes()
				mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_FI_MCP_TOTP_CODE).WithActorId(actorIdSample1)).Return(true, nil).AnyTimes()
				mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_NET_WORTH_AI_ICONS).WithActorId(actorIdSample1)).Return(false, nil).AnyTimes()
			},
			want: []int32{26, 29, 76, 36},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				genconf:       gconf,
				segmentClient: mockSegmentClient,
				evaluator:     mockReleaseEvaluator,
			}

			fiSAShortcutsParamsMap, ok := s.genconf.HomeRevampParams().HomeShortcutParams().UserTypeToShortcutParamsMap().Load(homePkg.UserTypeFiSA.String())
			if !ok {
				t.Errorf("shortcut params not found for user type %s", homePkg.UserTypeFiSA.String())
			}

			tt.setupMocks()
			if got := s.getDefaultShortcutsForSAUsers(tt.args.ctx, tt.args.actorId, fiSAShortcutsParamsMap.DefaultShortcuts()); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getDefaultShortcutsForSAUsers() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_getSoftIntentBasedDefaultShortcutIcons(t *testing.T) {
	t.Parallel()
	type args struct {
		userSelectedSoftIntents []onboarding.OnboardingSoftIntent
	}
	tests := []struct {
		name string
		args args
		want []int32
	}{
		{
			name: "selected only 1 intent",
			args: args{
				userSelectedSoftIntents: []onboarding.OnboardingSoftIntent{
					onboarding.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_ZERO_BALANCE_SAVINGS_ACCOUNT,
				},
			},
			want: []int32{13, 46, 36, 92},
		},
		{
			name: "selected 4 intents from same category",
			args: args{
				userSelectedSoftIntents: []onboarding.OnboardingSoftIntent{
					onboarding.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_ZERO_BALANCE_SAVINGS_ACCOUNT,
					onboarding.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_EXPENSE_TRACKER,
					onboarding.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_CASHBACK_ON_UPI,
					onboarding.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_INVESTMENT_TRACKER,
				},
			},
			want: []int32{13, 51, 35, 46},
		},
		{
			name: "selected > 4 intents from 2 categories",
			args: args{
				userSelectedSoftIntents: []onboarding.OnboardingSoftIntent{
					onboarding.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_ZERO_BALANCE_SAVINGS_ACCOUNT,
					onboarding.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_EXPENSE_TRACKER,
					onboarding.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_CASHBACK_ON_UPI,
					onboarding.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_INVESTMENT_TRACKER,
					onboarding.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_MUTUAL_FUNDS,
					onboarding.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_FIXED_DEPOSITS,
				},
			},
			want: []int32{13, 51, 35, 20},
		},
		{
			name: "selected 1 intent form every category",
			args: args{
				userSelectedSoftIntents: []onboarding.OnboardingSoftIntent{
					onboarding.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_ZERO_BALANCE_SAVINGS_ACCOUNT,
					onboarding.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_MUTUAL_FUNDS,
					onboarding.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_INTERNATIONAL_DEBIT_CARD,
					onboarding.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_LOANS_UP_TO_5L,
				},
			},
			want: []int32{13, 20, 28, 19},
		},
		{
			name: "selected 2 intent form every category",
			args: args{
				userSelectedSoftIntents: []onboarding.OnboardingSoftIntent{
					onboarding.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_EXPENSE_TRACKER,
					onboarding.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_ZERO_BALANCE_SAVINGS_ACCOUNT,
					onboarding.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_FIXED_DEPOSITS,
					onboarding.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_MUTUAL_FUNDS,
					onboarding.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_ZERO_FOREX_FEES,
					onboarding.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_INTERNATIONAL_DEBIT_CARD,
					onboarding.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_INSTANT_SALARY,
					onboarding.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_LOANS_UP_TO_5L,
				},
			},
			want: []int32{35, 21, 28, 19},
		},
		{
			name: "intents having same shortcuts - pick remaining form default",
			args: args{
				userSelectedSoftIntents: []onboarding.OnboardingSoftIntent{
					onboarding.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_ZERO_FOREX_FEES,
					onboarding.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_INTERNATIONAL_DEBIT_CARD,
					onboarding.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_INSTANT_SALARY,
					onboarding.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_LOANS_UP_TO_5L,
				},
			},
			want: []int32{28, 19, 46, 36},
		},
		{
			name: "selected intent is in default",
			args: args{
				userSelectedSoftIntents: []onboarding.OnboardingSoftIntent{
					onboarding.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_INVESTMENT_TRACKER,
				},
			},
			want: []int32{51, 46, 36, 92},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				genconf: gconf,
			}

			fiLiteShortcutParamsMap, ok := s.genconf.HomeRevampParams().HomeShortcutParams().UserTypeToShortcutParamsMap().Load(homePkg.UserTypeFiLite.String())
			if !ok {
				t.Errorf("shortcut params not found for user type %s", homePkg.UserTypeFiLite.String())
			}

			if got := s.getSoftIntentBasedDefaultShortcutIcons(tt.args.userSelectedSoftIntents, fiLiteShortcutParamsMap.DefaultShortcuts()); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getDirectToHomeDefaultShortcutIcons() = %v, want %v", got, tt.want)
			}
		})
	}
}
