package home

import (
	"context"
	"errors"
	"fmt"
	"math"
	"sort"
	"strings"
	"text/template"
	"time"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/bankcust"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/pkg/accrual"

	"github.com/epifi/be-common/pkg/money"
	feHomePb "github.com/epifi/gamma/api/frontend/home"
	"github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/kyc/vkyc"
	segmentPb "github.com/epifi/gamma/api/segment"
	tieringPb "github.com/epifi/gamma/api/tiering"
	"github.com/epifi/gamma/api/tiering/enums"
	"github.com/epifi/gamma/api/tiering/external"
	"github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/tiering"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/tiering/data_collector"
	"github.com/epifi/gamma/frontend/tiering/display_names"
	earnedBenefits "github.com/epifi/gamma/frontend/tiering/earned_benefits"
	feTieringHelper "github.com/epifi/gamma/frontend/tiering/helper"
	tieringAaSalHelper "github.com/epifi/gamma/frontend/tiering/helper/aasalary"
	"github.com/epifi/gamma/pkg/feature"
	"github.com/epifi/gamma/pkg/feature/release"
	pkgUser "github.com/epifi/gamma/pkg/user"
	"github.com/epifi/gamma/tiering/tiermappings"
)

// https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=11487-12609&t=By7pyxhjw35Lvwzn-4
// nolint: funlen, ineffassign
func (s *Service) getNotchComponents(ctx context.Context, actorId string) (badge *commontypes.Image, promptIcon *feHomePb.Icon, promptTitle *commontypes.Text, err error) {
	promptTitle = &commontypes.Text{
		FontColor: Snow,
		FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
	}
	promptIcon = &feHomePb.Icon{
		IconImage:     promptChevronV2Image,
		VisualElement: commontypes.GetVisualElementImageFromUrl(promptChevronV2Image.GetImageUrl()),
		ActionType:    feHomePb.Icon_ACTION_TYPE_DEEPLINK,
		BgColour:      &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: Charcoal}},
	}
	badge = &commontypes.Image{}

	data, notchDataErr := s.gatherDataForNotchComponents(ctx, actorId)
	if notchDataErr != nil {
		return badge, promptIcon, promptTitle, fmt.Errorf("error in gathering data for notch components: %w", notchDataErr)
	}

	switch data.kycLevel {
	case kyc.KYCLevel_MIN_KYC:
		badge = minKycBadge
		promptTitle.FontColor = UserIsMinKycColour
		promptTitle.DisplayValue = &commontypes.Text_PlainString{PlainString: data.getProfileExtensionResp.GetTitle()}
		promptIcon.Action = &feHomePb.Icon_Deeplink{Deeplink: data.getProfileExtensionResp.GetDeeplink()}
		promptIcon.BgColour = &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: MonochromeNight}}
		promptIcon.IconImage = nil
		if data.getProfileExtensionResp.GetDeeplink() != nil {
			promptIcon.IconImage = &commontypes.Image{ImageUrl: promptChevronRavenSteel}
			promptIcon.VisualElement = commontypes.GetVisualElementImageFromUrl(promptChevronRavenSteel)
		}
	case kyc.KYCLevel_FULL_KYC:
		badge = fullKycBadge
		promptTitle.DisplayValue = &commontypes.Text_PlainString{PlainString: FullKycNonSalaryRegPrompt1}
	}

	// return early if user is NR
	if data.isNrUser {
		promptTitle.DisplayValue = &commontypes.Text_PlainString{PlainString: FullKycNonSalaryRegPrompt1}
		return badge, promptIcon, nil, nil
	}

	currTier := data.tieringFeEssentials.GetCurrentTier()
	prevTier := data.tieringFeEssentials.GetPreviousTier()
	isUserDowngraded := data.tieringFeEssentials.GetIsUserInDowngradedWindow()
	isUserInGrace := data.tieringFeEssentials.GetIsUserInGrace()
	allPlansDl := tiering.AllPlansDeeplink(currTier, data.isTieringMultipleWaysEnabledForActor)

	// use tiering based prompt title, badge and deeplink
	dl := allPlansDl
	if data.isTieringMultipleWaysEnabledForActor && earnedBenefits.ShouldShowTieringEarnedBenefitsScreen(currTier, prevTier, isUserDowngraded) {
		dl, _ = earnedBenefits.GetEarnedBenefitsDeeplink(earnedBenefits.GetTierToLoad(currTier, prevTier, isUserDowngraded))
	}
	promptIcon.Action = &feHomePb.Icon_Deeplink{Deeplink: dl}
	badge = getTieringPromptBadge(isUserDowngraded, isUserInGrace, prevTier, currTier)
	promptTitle.DisplayValue = &commontypes.Text_PlainString{PlainString: getTieringPromptTitle(currTier, prevTier)}
	promptIcon.BgColour = getBgColor(currTier)

	// override prompt title for users eligible for notch campaign
	campaignNotchCopy, campaignNotchDl, getCampaignErr := s.getCampaignCopyAndDeeplinkForActor(data.activeCampaignSegmentIds, data.notchCampaignSegmentMap)
	if getCampaignErr != nil {
		return badge, promptIcon, promptTitle, fmt.Errorf("failed to get campaign copy and deeplink for actor, %w", getCampaignErr)
	}
	if campaignNotchCopy != "" && campaignNotchDl != nil {
		logger.Info(ctx, "profile notch campaign", zap.String("notch copy", campaignNotchCopy), zap.Any(logger.ACTOR_ID_V2, actorId))
		promptTitle.DisplayValue = &commontypes.Text_PlainString{PlainString: campaignNotchCopy}
		promptIcon.Action = &feHomePb.Icon_Deeplink{Deeplink: campaignNotchDl}
	}

	// --- Notch config enrichment logic using segment membership ---
	if feature.IsFeatureEnabledForUserV2(ctx, actorId, s.genconf.TieringNotchConfig().EnableNotchConfigEnrichmentFeatureFlag(), s.userGroupClient, s.usersClient, s.actorClient) && s.genconf.TieringNotchConfig().SegmentConfMap().Len() > 0 {
		// Collect segments and sort by Priority
		segmentEntries := make([]struct {
			Name string
			Conf *genconf.SegmentConf
		}, 0, s.genconf.TieringNotchConfig().SegmentConfMap().Len())
		segmentConfMap := s.genconf.TieringNotchConfig().SegmentConfMap()
		segmentConfMap.Iterate(func(segmentName string, conf *genconf.SegmentConf) (stop bool) {
			segmentEntries = append(segmentEntries, struct {
				Name string
				Conf *genconf.SegmentConf
			}{segmentName, conf})
			return false
		})

		// Sort by Priority
		sort.Slice(segmentEntries, func(i, j int) bool {
			return segmentEntries[i].Conf.Priority(ctx) < segmentEntries[j].Conf.Priority(ctx)
		})

		// Prepare non-realtime segment IDs for batch DB fetch
		var realTimeDisabledSegmentIds []string
		for _, entry := range segmentEntries {
			if !entry.Conf.UseRealtime(ctx) {
				realTimeDisabledSegmentIds = append(realTimeDisabledSegmentIds, entry.Conf.SegmentId(ctx))
			}
		}
		actorSegmentMembershipMap := map[string]*segmentPb.SegmentMembership{}
		if len(realTimeDisabledSegmentIds) > 0 {
			res, err := s.segmentClient.IsMember(ctx, &segmentPb.IsMemberRequest{
				ActorId:    actorId,
				SegmentIds: realTimeDisabledSegmentIds,
			})
			if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
				// Continuing without returning when rpc fails.
				// While identifying nonrealtime segment, if no actorSegmentMembership found , return non enriched notch title
				logger.Error(ctx, "rpc error", zap.Error(rpcErr))
			}
			actorSegmentMembershipMap = res.GetSegmentMembershipMap()
		}

		// Calculate all possible variables for template enrichment (as before)
		currentTier, _ := display_names.GetTitleCaseDisplayString(currTier)
		cashBackPercent := tieringAaSalHelper.TierToCashbackPercent[currTier]
		month := time.Now().AddDate(0, -1, 0).Month().String()[:3]
		tierMinBalance := ""
		if minBal := data.tieringFeEssentials.GetCurrTierMinBal(); minBal != nil {
			tierMinBalance = money.ToDisplayStringWithPrecision(minBal, 0)
		}
		daysLeft := ""
		if isUserInGrace {
			days := int(math.Ceil(time.Until(data.tieringFeEssentials.GetGracePeriodExpiry()).Hours() / 24))
			daysLeft = fmt.Sprintf("%d", days)
		}
		projectedFiCoins := ""

		currMonth := data_collector.GetMonthYear(time.Now())
		fiCoins, err := s.tieringDataCollector.GetCustomRewardProjectionsAggregate(ctx, actorId, rewardsPb.RewardType_FI_COINS, currTier, currMonth)
		if err != nil {
			// Continuing without returning when rpc fails.
			// While identifying realtime segment, if a segment requires projectedFiCoins then return non enriched notch title.

			// epifierrors.ErrInvalidArgument is returned when we request with an ineligible tier for monthly tiering rewards
			// since a lot of such users land on home, we are gracefully ignoring this case.
			if !errors.Is(err, epifierrors.ErrInvalidArgument) {
				logger.Error(ctx, "failed to get projected fi-coins:", zap.Error(err))
			}
		} else {
			data.fiCoins = fiCoins
			// Format projectedFiCoins as '6.12K' or '7.19L'
			projectedFiCoins = formatFiCoins(fiCoins)
		}

		previousTier, _ := display_names.GetTitleCaseDisplayString(prevTier)
		previousTierCashBackPercent := tieringAaSalHelper.TierToCashbackPercent[prevTier]

		vars := &NotchTemplateVars{
			CurrentTier:                 currentTier,
			CashBackPercent:             cashBackPercent,
			ProjectedFiCoins:            projectedFiCoins,
			Month:                       month,
			TierMinBalance:              tierMinBalance,
			DownGradeGracePeriodLeft:    daysLeft,
			PreviousTier:                previousTier,
			PreviousTierCashBackPercent: previousTierCashBackPercent,
		}

		// Evaluate segments by priority
		for _, entry := range segmentEntries {
			conf := entry.Conf
			segmentId := entry.Conf.SegmentId(ctx)
			segmentName := entry.Name
			matched := false
			if conf.UseRealtime(ctx) {
				matched, err = s.calculateRealtimeSegment(ctx, actorId, segmentName, data, s.genconf.TieringNotchConfig(), vars)
				if err != nil {
					logger.Error(ctx, "realtime segment calculator error, returning non-enriched notch title", zap.Error(err), zap.String("SegmentName", segmentName))
					return badge, promptIcon, promptTitle, nil
				}
			} else {
				matched, err = s.calculateNonRealtimeSegment(ctx, segmentId, segmentName, actorSegmentMembershipMap)
				if err != nil {
					logger.Error(ctx, err.Error(), zap.String("SegmentName", segmentName))
					return badge, promptIcon, promptTitle, nil
				}
			}
			if matched {
				enrichedNotchTitle, err := enrichNotchTemplate(conf.SegmentTemplate(ctx), vars)
				if err != nil {
					logger.Error(ctx, "enriched notch template error", zap.Error(err))
					// error enriching the template of the segment user belongs to , falling back to old version of notch title
					return badge, promptIcon, promptTitle, nil
				}
				promptTitle.DisplayValue = &commontypes.Text_PlainString{PlainString: accrual.ReplaceCoinWithPointIfApplicable(enrichedNotchTitle, nil)}
				break
			}
		}
	}

	return badge, promptIcon, promptTitle, nil
}

func getTieringPromptTitle(currTier, prevTier external.Tier) string {
	currTierInternal, _ := tiermappings.GetInternalTierFromExternalTier(currTier)
	prevTierInternal, _ := tiermappings.GetInternalTierFromExternalTier(prevTier)
	tierDisplayName, _ := display_names.GetTitleCaseDisplayString(currTier)

	if currTier.IsBaseTier() && prevTier.IsBaseTier() {
		return fmt.Sprintf(youAreOnTierText, tierDisplayName)
	}

	movementType, _ := enums.GetMovementTypeFromStartAndEndTiers(prevTierInternal, currTierInternal)
	switch movementType {
	case enums.TierMovementType_TIER_MOVEMENT_TYPE_UPGRADE:
		return fmt.Sprintf(upgradedToTierText, tierDisplayName)
	case enums.TierMovementType_TIER_MOVEMENT_TYPE_DOWNGRADE:
		return fmt.Sprintf(youAreOnTierText, tierDisplayName)
	}

	return fmt.Sprintf(youAreOnTierText, tierDisplayName)
}

type notchData struct {
	kycLevel                             kyc.KYCLevel
	getProfileExtensionResp              *vkyc.GetProfileExtensionResponse // fetched only for Min kyc users
	isNrUser                             bool
	isTieringMultipleWaysEnabledForActor bool
	isTieringEarnedBenefitsEnabled       bool
	tieringFeEssentials                  *feTieringHelper.TieringFeEssentials
	activeCampaignSegmentIds             []string
	notchCampaignSegmentMap              map[string]*segmentPb.SegmentMembership
	fiCoins                              float32
}

func (s *Service) gatherDataForNotchComponents(ctx context.Context, actorId string) (*notchData, error) {
	data := &notchData{}
	errGrp, gCtx := errgroup.WithContext(ctx)

	// fetch kyc data in error group
	errGrp.Go(func() error {
		bankCustInfo, err := s.bcClient.GetBankCustomer(gCtx, &bankcust.GetBankCustomerRequest{
			Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
			Identifier: &bankcust.GetBankCustomerRequest_ActorId{ActorId: actorId},
		})
		if rpcErr := epifigrpc.RPCError(bankCustInfo, err); rpcErr != nil {
			return fmt.Errorf("failed to get bank customer info: %w", rpcErr)
		}

		data.kycLevel = bankCustInfo.GetBankCustomer().GetDedupeInfo().GetKycLevel()

		if data.kycLevel == kyc.KYCLevel_FULL_KYC {
			return nil
		}

		profileExtRes, profileExtErr := s.vkycFeClient.GetProfileExtension(ctx, &vkyc.GetProfileExtensionRequest{
			ActorId: actorId,
		})
		if rpcErr := epifigrpc.RPCError(profileExtRes, profileExtErr); rpcErr != nil {
			if rpc.StatusFromError(rpcErr).IsRecordNotFound() {
				logger.Error(ctx, "error in getting profile extension from GetProfileExtension rpc", zap.Error(rpcErr))
			}
			return fmt.Errorf("error in getting profile extension from GetProfileExtension rpc, %w", rpcErr)
		}

		return nil
	})

	errGrp.Go(func() error {
		isNrAccountRes, isNrAccountResErr := s.userAttributeFetcher.IsNonResidentUser(ctx, &pkgUser.IsNonResidentUserRequest{
			ActorId: actorId,
		})
		if isNrAccountResErr != nil {
			return fmt.Errorf("error in fetching is non resident user: %w", isNrAccountResErr)
		}

		data.isNrUser = isNrAccountRes.GetIsNonResidentUser()
		return nil
	})

	errGrp.Go(func() error {
		tieringEssentials, getErr := s.tieringDataCollector.GetTieringEssentials(gCtx, actorId)
		if getErr != nil {
			return fmt.Errorf("error in getting tiering essentials: %w", getErr)
		}

		data.tieringFeEssentials = tieringEssentials
		data.isTieringMultipleWaysEnabledForActor = tieringEssentials.GetGetConfigParamsResp().GetIsMultipleWaysToEnterTieringEnabledForActor()
		return nil
	})

	errGrp.Go(func() error {
		releaseConstraint := release.NewCommonConstraintData(typesv2.Feature_TIERING_EARNED_BENEFITS_FROM_PROFILE_NOTCH).WithActorId(actorId)
		isTieringEarnedBenefitsEnabled, evalErr := s.evaluator.Evaluate(ctx, releaseConstraint)
		if evalErr != nil {
			return fmt.Errorf("failed to evaluate TIERING_EARNED_BENEFITS_FROM_PROFILE_NOTCH feature, %w", evalErr)
		}

		data.isTieringEarnedBenefitsEnabled = isTieringEarnedBenefitsEnabled
		return nil
	})

	var campaignSegmentResp *segmentPb.IsMemberResponse
	errGrp.Go(func() error {
		activeNotchCampaigns := s.getActiveCampaigns()
		activeCampaignSegmentIds := getSegmentIdsFromCampaignDetails(activeNotchCampaigns)
		if len(activeCampaignSegmentIds) == 0 {
			return nil
		}

		var segmentMemberErr error
		campaignSegmentResp, segmentMemberErr = s.segmentClient.IsMember(ctx, &segmentPb.IsMemberRequest{
			ActorId:    actorId,
			SegmentIds: activeCampaignSegmentIds,
			LatestBy:   timestamppb.Now(),
		})
		if rpcErr := epifigrpc.RPCError(campaignSegmentResp, segmentMemberErr); rpcErr != nil {
			return fmt.Errorf("segmentClient.IsMember Rpc failed, %w", rpcErr)
		}

		data.activeCampaignSegmentIds = activeCampaignSegmentIds
		data.notchCampaignSegmentMap = campaignSegmentResp.GetSegmentMembershipMap()
		return nil
	})

	err := errGrp.Wait()
	if err != nil {
		return nil, fmt.Errorf("error in gathering data for notch components: %w", err)
	}

	return data, nil
}

func getBgColor(currTier external.Tier) *ui.BackgroundColour {
	var (
		radialGradientColor1, radialGradientColor2   string
		radialGradientCenterX, radialGradientCenterY int32
	)

	switch {
	case currTier == external.Tier_TIER_FI_PLUS:
		radialGradientColor1, radialGradientColor2, radialGradientCenterX, radialGradientCenterY = RadialGradientColorPlus1, RadialGradientColorPlus2, RadialGradientPlusCenterX, RadialGradientPlusCenterY
	case currTier == external.Tier_TIER_FI_INFINITE:
		radialGradientColor1, radialGradientColor2, radialGradientCenterX, radialGradientCenterY = RadialGradientColorInfinite1, RadialGradientColorInfinite2, RadialGradientInfiniteCenterX, RadialGradientInfiniteCenterY
	case currTier.IsAaSalaryTier():
		radialGradientColor1, radialGradientColor2, radialGradientCenterX, radialGradientCenterY = RadialGradientColorPrime1, RadialGradientColorPrime2, RadialGradientPrimeCenterX, RadialGradientPrimeCenterY
	case currTier == external.Tier_TIER_FI_REGULAR:
		radialGradientColor1, radialGradientColor2, radialGradientCenterX, radialGradientCenterY = RadialGradientColorRegular1, RadialGradientColorRegular2, RadialGradientRegularCenterX, RadialGradientRegularCenterY
	case currTier == external.Tier_TIER_FI_BASIC:
		radialGradientColor1, radialGradientColor2, radialGradientCenterX, radialGradientCenterY = RadialGradientColorStandard1, RadialGradientColorStandard2, RadialGradientStandardCenterX, RadialGradientStandardCenterY
	case currTier == external.Tier_TIER_FI_SALARY, currTier == external.Tier_TIER_FI_SALARY_BASIC:
		radialGradientColor1, radialGradientColor2, radialGradientCenterX, radialGradientCenterY = RadialGradientColorSalary1, RadialGradientColorSalary2, RadialGradientSalaryCenterX, RadialGradientSalaryCenterY
	}

	return &ui.BackgroundColour{
		Colour: &ui.BackgroundColour_RadialGradient{
			RadialGradient: &ui.RadialGradient{
				Center: &ui.CenterCoordinates{
					CenterX: radialGradientCenterX,
					CenterY: radialGradientCenterY,
				},
				OuterRadius: RadialGradientOuterRadius,
				Colours:     []string{radialGradientColor1, radialGradientColor2},
			},
		},
	}
}

// NotchTemplateVars holds all possible variables for template enrichment
// Example: CurrentTier, CashBackPercent, Month, AMBThreshold, DaysLeft, ProjectedFiCoins, PreviousTier, PreviousTierCashBackPercent, etc.
type NotchTemplateVars struct {
	CurrentTier                 string // e.g. "Prime"
	CashBackPercent             string // e.g. "2%"
	ProjectedFiCoins            string // e.g. "3000"
	Month                       string // e.g. "April"
	TierMinBalance              string // e.g. "₹25,000"
	DownGradeGracePeriodLeft    string // e.g. "5"
	PreviousTier                string // e.g. "Plus"
	PreviousTierCashBackPercent string // e.g. "1%"
}

// enrichNotchTemplate fills the template string with the provided variables
func enrichNotchTemplate(tmpl string, vars *NotchTemplateVars) (string, error) {
	t, err := template.New("notch").Parse(tmpl)
	if err != nil {
		return "", err
	}
	var sb strings.Builder
	err = t.Execute(&sb, vars)
	if err != nil {
		return "", err
	}
	return sb.String(), nil
}

// RealtimeSegmentCalculator defines the interface for realtime segment calculation
// Implementations should be registered in getRealtimeSegmentCalculators
// Example: "UPGRADE_JOURNEY": &UpgradeJourneyRealtimeCalculator{}
type RealtimeSegmentCalculator interface {
	IsMember(ctx context.Context, actorId string, data *notchData, tieringNotchConfig *genconf.TieringNotchConfig, vars *NotchTemplateVars) (bool, error)
}

// UpgradeJourneyRealtimeCalculator implements the realtime segment logic for upgrade journey
// Returns true if the user just upgraded to the current tier and is within the segment expiry window
// (now < lastUpgradeTime + SegmentExpiry)
type UpgradeJourneyRealtimeCalculator struct{}

func (c *UpgradeJourneyRealtimeCalculator) IsMember(
	ctx context.Context,
	actorId string,
	data *notchData,
	tieringNotchConfig *genconf.TieringNotchConfig,
	vars *NotchTemplateVars,
) (bool, error) {
	if vars.CurrentTier == "" {
		return false, fmt.Errorf("invalid arguments : CurrentTier not found")
	}

	currTier := data.tieringFeEssentials.GetCurrentTier()
	lastUpgrade := data.tieringFeEssentials.GetLastUpgradeDetails()
	if lastUpgrade == nil {
		logger.Debug(ctx, "UpgradeJourney: lastUpgrade is nil, not segmented")
		return false, nil
	}

	if lastUpgrade.ToTier != currTier {
		logger.Debug(ctx, "UpgradeJourney: lastUpgrade.ToTier != currTier, not segmented", zap.String("toTier", lastUpgrade.ToTier.String()), zap.String("currTier", currTier.String()))
		return false, nil
	}

	expiryDays := tieringNotchConfig.UpgradeJourneyConf().SegmentExpiry(ctx)
	expiryDuration := time.Duration(expiryDays) * 24 * time.Hour
	timeSinceUpgrade := time.Since(lastUpgrade.MovementTimestamp.AsTime())

	if timeSinceUpgrade < expiryDuration {
		return true, nil
	}
	logger.Debug(ctx, "UpgradeJourney: not segmented, time since upgrade exceeds expiry", zap.Duration("expiryDuration", expiryDuration), zap.Duration("timeSinceUpgrade", timeSinceUpgrade))
	return false, nil
}

// EarningPotentialLowRealtimeCalculator implements the realtime segment logic for low earning potential
// Returns true if the user's projected rewards are less than the configured low potential limit
type EarningPotentialLowRealtimeCalculator struct {
}

func (c *EarningPotentialLowRealtimeCalculator) IsMember(
	ctx context.Context,
	actorId string,
	data *notchData,
	tieringNotchConfig *genconf.TieringNotchConfig,
	vars *NotchTemplateVars,
) (bool, error) {
	if !data.tieringFeEssentials.GetCurrentTier().IsCashbackEligibleTier() {
		logger.Debug(ctx, "EarningPotentialLow: not cashback eligible tier")
		return false, nil
	}
	if vars.CashBackPercent == "" {
		return false, fmt.Errorf("invalid arguments : CashBackPercent not found")
	}
	projected := int32(data.fiCoins)
	limit := tieringNotchConfig.EarningPotentialConf().LowPotentialLimit(ctx)
	if projected < limit {
		return true, nil
	}
	logger.Debug(ctx, "EarningPotentialLow: not segmented", zap.Int32("projectedRewardUnits", projected), zap.Int32("lowPotentialLimit", limit))
	return false, nil
}

// EarningPotentialHighRealtimeCalculator implements the realtime segment logic for high earning potential
// Returns true if the user's projected rewards are greater than the configured low potential limit
type EarningPotentialHighRealtimeCalculator struct {
}

func (c *EarningPotentialHighRealtimeCalculator) IsMember(
	ctx context.Context,
	actorId string,
	data *notchData,
	tieringNotchConfig *genconf.TieringNotchConfig,
	vars *NotchTemplateVars,
) (bool, error) {
	if !data.tieringFeEssentials.GetCurrentTier().IsCashbackEligibleTier() {
		logger.Debug(ctx, "EarningPotentialHigh: not cashback eligible tier")
		return false, nil
	}
	if vars.CashBackPercent == "" {
		return false, fmt.Errorf("invalid arguments : CashBackPercent not found")
	}
	projected := int32(data.fiCoins)
	limit := tieringNotchConfig.EarningPotentialConf().LowPotentialLimit(ctx)
	if projected >= limit {
		return true, nil
	}
	logger.Debug(ctx, "EarningPotentialHigh: not segmented", zap.Int32("projectedRewardUnits", projected), zap.Int32("lowPotentialLimit", limit))
	return false, nil
}

// Helper for DowngradeGrace calculators
func isUserInGraceWindow(ctx context.Context, data *notchData, vars *NotchTemplateVars, minDays, maxDays int) (bool, error) {
	if !data.tieringFeEssentials.GetCurrentTier().IsCashbackEligibleTier() {
		logger.Debug(ctx, "GraceWindow: not cashback eligible tier")
		return false, nil
	}
	if vars.CashBackPercent == "" {
		return false, fmt.Errorf("invalid arguments : CashBackPercent not found")
	}
	if !data.tieringFeEssentials.GetIsUserInGrace() {
		logger.Debug(ctx, "GraceWindow: user not in grace period")
		return false, nil
	}
	daysLeft := int(math.Ceil(time.Until(data.tieringFeEssentials.GetGracePeriodExpiry()).Hours() / 24))
	if daysLeft <= maxDays && daysLeft >= minDays {
		return true, nil
	}
	logger.Debug(ctx, "GraceWindow: not segmented", zap.Int("daysLeft", daysLeft), zap.Int("minDays", minDays), zap.Int("maxDays", maxDays))
	return false, nil
}

// DowngradeGrace106RealtimeCalculator implements the realtime segment logic for downgrade grace period (10–6 days)
// Returns true if the user is in grace period and has 10 to 6 days left
type DowngradeGrace106RealtimeCalculator struct{}

func (c *DowngradeGrace106RealtimeCalculator) IsMember(
	ctx context.Context,
	actorId string,
	data *notchData,
	tieringNotchConfig *genconf.TieringNotchConfig,
	vars *NotchTemplateVars,
) (bool, error) {
	return isUserInGraceWindow(ctx, data, vars, 6, 10)
}

// DowngradeGrace52RealtimeCalculator implements the realtime segment logic for downgrade grace period (5–3 days)
// Returns true if the user is in grace period and has 5 to 3 days left
type DowngradeGrace52RealtimeCalculator struct{}

func (c *DowngradeGrace52RealtimeCalculator) IsMember(
	ctx context.Context,
	actorId string,
	data *notchData,
	tieringNotchConfig *genconf.TieringNotchConfig,
	vars *NotchTemplateVars,
) (bool, error) {
	return isUserInGraceWindow(ctx, data, vars, 2, 5)
}

// DowngradeGrace1DayRealtimeCalculator implements the realtime segment logic for downgrade (1 day)
// Returns true if the user is in grace period and has exactly 1 day left
type DowngradeGrace1DayRealtimeCalculator struct{}

func (c *DowngradeGrace1DayRealtimeCalculator) IsMember(
	ctx context.Context,
	actorId string,
	data *notchData,
	tieringNotchConfig *genconf.TieringNotchConfig,
	vars *NotchTemplateVars,
) (bool, error) {
	if !data.tieringFeEssentials.GetCurrentTier().IsCashbackEligibleTier() {
		logger.Debug(ctx, "DowngradeGrace1Day: not cashback eligible tier")
		return false, nil
	}
	if vars.CashBackPercent == "" {
		return false, fmt.Errorf("invalid arguments : CashBackPercent not found")
	}
	if !data.tieringFeEssentials.GetIsUserInGrace() {
		logger.Debug(ctx, "DowngradeGrace1Day: user not in grace period")
		return false, nil
	}
	daysLeft := int(math.Ceil(time.Until(data.tieringFeEssentials.GetGracePeriodExpiry()).Hours() / 24))
	if daysLeft == 1 {
		return true, nil
	}
	logger.Debug(ctx, "DowngradeGrace1Day: not segmented", zap.Int("daysLeft", daysLeft))
	return false, nil
}

// DowngradeAMBLowRealtimeCalculator implements the realtime segment logic for users with low AMB
// Returns true if the user's current AMB is less than the target AMB
type DowngradeAMBLowRealtimeCalculator struct {
	tieringClient tieringPb.TieringClient
}

func (c *DowngradeAMBLowRealtimeCalculator) IsMember(
	ctx context.Context,
	actorId string,
	data *notchData,
	tieringNotchConfig *genconf.TieringNotchConfig,
	vars *NotchTemplateVars,
) (bool, error) {
	if vars.TierMinBalance == "" {
		// not returning error as tiers like salary and salary basic have no tier min balance .
		// we want to return false and segment further tiers rather than returning error and falling back to un-enriched notch title
		logger.Debug(ctx, "DowngradeAMBLow: not segmented, no tier min balance required")
		return false, nil
	}
	resp, err := c.tieringClient.GetAMBInfo(ctx, &tieringPb.GetAMBInfoRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		logger.Error(ctx, "Failed to fetch AMB info", zap.Error(rpcErr))
		return false, rpcErr
	}
	currAMB := resp.GetCurrentAmb().GetUnits()
	targetAMB := resp.GetTargetAmb().GetUnits()
	if currAMB < targetAMB {
		return true, nil
	}
	logger.Debug(ctx, "DowngradeAMBLow: not segmented", zap.Int64("currAMB", currAMB), zap.Int64("targetAMB", targetAMB))
	return false, nil
}

// AbuserFlaggedRealtimeCalculator implements the realtime segment logic for abuser flagged users
// SegmentDefinition: "Returns true if the user is flagged as an abuser based on AMB thresholds for their tier"
type AbuserFlaggedRealtimeCalculator struct {
	tieringClient tieringPb.TieringClient
}

func (c *AbuserFlaggedRealtimeCalculator) IsMember(
	ctx context.Context,
	actorId string,
	data *notchData,
	tieringNotchConfig *genconf.TieringNotchConfig,
	vars *NotchTemplateVars,
) (bool, error) {
	currTier := data.tieringFeEssentials.GetCurrentTier()
	if currTier != external.Tier_TIER_FI_PLUS &&
		currTier != external.Tier_TIER_FI_INFINITE &&
		currTier != external.Tier_TIER_FI_AA_SALARY_BAND_3 {
		logger.Debug(ctx, "AbuserFlagged: not a relevant tier", zap.String("currTier", currTier.String()))
		return false, nil
	}
	resp, err := c.tieringClient.GetAMBInfo(ctx, &tieringPb.GetAMBInfoRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		logger.Error(ctx, "Failed to fetch AMB info", zap.Error(rpcErr))
		return false, rpcErr
	}
	currAMB := resp.GetCurrentAmb().GetUnits()
	minRequiredAmb := tieringNotchConfig.AbuserConfig().Get(currTier.String())
	if currAMB < int64(minRequiredAmb) {
		return true, nil
	}
	logger.Debug(ctx, "AbuserFlagged: not segmented", zap.Int64("currAMB", currAMB), zap.Int64("minRequiredAmb", int64(minRequiredAmb)))
	return false, nil
}

// Helper for PostDowngrade calculators
func isUserInPostDowngradeWindow(ctx context.Context, data *notchData, minDays, maxDays int) (bool, error) {
	lastDowngrade := data.tieringFeEssentials.GetLastDowngradeDetails()
	if lastDowngrade == nil {
		logger.Debug(ctx, "PostDowngrade: lastDowngrade is nil")
		return false, nil
	}
	currTier := data.tieringFeEssentials.GetCurrentTier()
	if lastDowngrade.GetToTier() != currTier {
		logger.Debug(ctx, "PostDowngrade: lastDowngrade.ToTier != currTier", zap.String("toTier", lastDowngrade.GetToTier().String()), zap.String("currTier", currTier.String()))
		return false, nil
	}
	daysSince := int(math.Ceil(time.Since(lastDowngrade.GetMovementTimestamp().AsTime()).Hours() / 24))
	if daysSince >= minDays && daysSince <= maxDays {
		return true, nil
	}
	logger.Debug(ctx, "PostDowngrade: not segmented", zap.Int("daysSince", daysSince), zap.Int("minDays", minDays), zap.Int("maxDays", maxDays))
	return false, nil
}

// PostDowngrade15RealtimeCalculator implements the realtime segment logic for 1-5 days after downgrade
// Returns true if the user was downgraded to the current tier and days since downgrade is in [1,5]
type PostDowngrade15RealtimeCalculator struct{}

func (c *PostDowngrade15RealtimeCalculator) IsMember(
	ctx context.Context,
	actorId string,
	data *notchData,
	tieringNotchConfig *genconf.TieringNotchConfig,
	vars *NotchTemplateVars,
) (bool, error) {
	return isUserInPostDowngradeWindow(ctx, data, 1, 5)
}

// PostDowngrade615RealtimeCalculator implements the realtime segment logic for 6-15 days after downgrade
// Returns true if the user was downgraded to the current tier and days since downgrade is in [6,15]
type PostDowngrade615RealtimeCalculator struct{}

func (c *PostDowngrade615RealtimeCalculator) IsMember(
	ctx context.Context,
	actorId string,
	data *notchData,
	tieringNotchConfig *genconf.TieringNotchConfig,
	vars *NotchTemplateVars,
) (bool, error) {
	return isUserInPostDowngradeWindow(ctx, data, 6, 15)
}

// MoneyPlantNotRedeemedRealtimeCalculator implements the realtime segment logic for unredeemed money plant rewards in the last month
// Returns true if the user has any unclaimed rewards in the last 1 month, and it has been 3 days since the reward have been credited but unclaimed
type MoneyPlantNotRedeemedRealtimeCalculator struct {
	tieringDataCollector data_collector.DataCollector
}

func (c *MoneyPlantNotRedeemedRealtimeCalculator) IsMember(
	ctx context.Context,
	actorId string,
	data *notchData,
	tieringNotchConfig *genconf.TieringNotchConfig,
	vars *NotchTemplateVars,
) (bool, error) {
	// Only consider rewards that are not expired and have been unredeemed for at least 3 days
	currTier := data.tieringFeEssentials.GetCurrentTier()
	resp, err := c.tieringDataCollector.GetRewardsByActorId(ctx, actorId, currTier)
	if err != nil {
		logger.Error(ctx, "Failed to fetch unredeemed money plant rewards via DataCollector", zap.Error(err))
		return false, err
	}
	now := time.Now()
	for _, reward := range resp.GetRewards() {
		if reward.GetStatus() == rewardsPb.RewardStatus_CREATED {
			daysSinceMoneyPlantCreated := uint32(now.Sub(reward.GetCreatedAt().AsTime()).Hours() / 24)
			if daysSinceMoneyPlantCreated >= 3 {
				return true, nil
			}
		}
	}
	logger.Debug(ctx, "MoneyPlantNotRedeemed: not segmented (via DataCollector)", zap.Int("rewardCount", len(resp.GetRewards())))
	return false, nil
}

// Service method to get the realtime segment calculators
func (s *Service) getRealtimeSegmentCalculators() map[string]RealtimeSegmentCalculator {
	return map[string]RealtimeSegmentCalculator{
		"UPGRADE_JOURNEY":          &UpgradeJourneyRealtimeCalculator{},
		"EARNING_POTENTIAL_LOW":    &EarningPotentialLowRealtimeCalculator{},
		"EARNING_POTENTIAL_HIGH":   &EarningPotentialHighRealtimeCalculator{},
		"DOWNGRADE_GRACE_10_6":     &DowngradeGrace106RealtimeCalculator{},
		"DOWNGRADE_GRACE_5_2":      &DowngradeGrace52RealtimeCalculator{},
		"DOWNGRADE_1_DAY":          &DowngradeGrace1DayRealtimeCalculator{},
		"DOWNGRADE_AMB_LOW":        &DowngradeAMBLowRealtimeCalculator{tieringClient: s.beTieringClient},
		"ABUSER_FLAGGED":           &AbuserFlaggedRealtimeCalculator{tieringClient: s.beTieringClient},
		"POST_DOWNGRADE_1_5":       &PostDowngrade15RealtimeCalculator{},
		"POST_DOWNGRADE_6_15":      &PostDowngrade615RealtimeCalculator{},
		"MONEY_PLANT_NOT_REDEEMED": &MoneyPlantNotRedeemedRealtimeCalculator{tieringDataCollector: s.tieringDataCollector},
	}
}

func (s *Service) calculateRealtimeSegment(
	ctx context.Context,
	actorId string,
	segmentName string,
	data *notchData,
	tieringNotchConfig *genconf.TieringNotchConfig,
	vars *NotchTemplateVars,
) (matched bool, err error) {
	calc, ok := s.getRealtimeSegmentCalculators()[segmentName]
	if !ok {
		return false, fmt.Errorf("realtime segment calculator not found for segment: %s", segmentName)
	}
	isMember, err := calc.IsMember(ctx, actorId, data, tieringNotchConfig, vars)
	if err != nil {
		return false, err
	}
	if isMember {
		return true, nil
	}
	return false, nil
}

func (s *Service) calculateNonRealtimeSegment(
	ctx context.Context,
	segmentId string,
	segmentName string,
	actorSegmentMembershipMap map[string]*segmentPb.SegmentMembership,
) (matched bool, err error) {
	if membership, ok := actorSegmentMembershipMap[segmentId]; ok {
		if membership.GetIsActorMember() {
			logger.Debug(ctx, "user segmented successfully", zap.String("SegmentName", segmentName))
			return true, nil
		}
		return false, nil
	} else {
		return false, fmt.Errorf("actor segment membership not found")
	}
}

func formatFiCoins(balance float32) string {
	var (
		lakh     = float32(100000)
		thousand = float32(1000)
	)

	switch {
	case balance >= lakh:
		return fmt.Sprintf("%.2fL", balance/lakh)
	case balance >= thousand:
		return fmt.Sprintf("%.2fK", balance/thousand)
	}
	return fmt.Sprintf("%v", int(balance))
}
