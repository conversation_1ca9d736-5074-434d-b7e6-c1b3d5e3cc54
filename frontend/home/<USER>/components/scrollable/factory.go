package scrollable

import (
	"fmt"

	"github.com/google/wire"

	"github.com/epifi/gamma/frontend/home/<USER>"
	"github.com/epifi/gamma/frontend/home/<USER>/metrics"
)

var ComponentGeneratorFactoryWireSet = wire.NewSet(NewComponentGeneratorFactory, wire.Bind(new(IComponentGeneratorFactory), new(*ComponentGeneratorFactory)))
var AllComponentGeneratorWireSet = wire.NewSet(
	NewCriticalNotificationComponent,
	NewDashboardComponent,
	NewAnalyserComponent,
	NewCardOffersComponent,
	NewCatalogOffersComponent,
	NewHelpComponent,
	NewInvestmentComponent,
	NewNudgesComponent,
	NewPrimaryFeatureComponent,
	NewSecondaryFeatureComponent,
	NewPromotionalBannerComponent,
	NewPromotionalBanner2Component,
	NewRecentAndUpcomingComponent,
	NewReferralComponent,
	NewSearchComponent,
	NewShortcutsComponent,
	NewTabbedCardComponent,
	NewTrustMarkerComponent,
	NewMoneySecretsComponent,
	NewUpiWidgetComponent,
	NewJourneysComponent,
	NewWealthAnalyserComponent,
	NewDcInternationWidgetComponent,
	NewActivationWidgetComponent,
	NewWealthBuilderLandingComponent,
)

type IComponentGeneratorFactory interface {
	GetComponentGenerator(componentId string) (ComponentGenerator, error)
}

type ComponentGeneratorFactory struct {
	criticalNotificationComponent *CriticalNotificationComponent
	dashboardComponent            *DashboardComponent
	analyserComponent             *AnalyserComponent
	cardOffersComponent           *CardOffersComponent
	catalogOffersComponent        *CatalogOffersComponent
	helpComponent                 *HelpComponent
	investmentComponent           *InvestmentComponent
	nudgesComponent               *NudgesComponent
	primaryFeatureComponent       *PrimaryFeatureComponent
	secondaryFeatureComponent     *SecondaryFeatureComponent
	promotionalBannerComponent    *PromotionalBannerComponent
	promotionalBanner2Component   *PromotionalBanner2Component
	recentAndUpcomingComponent    *RecentAndUpcomingComponent
	referralComponent             *ReferralComponent
	searchComponent               *SearchComponent
	shortcutsComponent            *ShortcutsComponent
	tabbedCardComponent           *TabbedCardComponent
	trustMarkerComponent          *TrustMarkerComponent
	moneySecretsComponent         *MoneySecretsComponent
	upiWidgetComponent            *UpiWidgetComponent
	journeysComponent             *JourneysComponent
	wealthAnalyserComponent       *WealthAnalyserComponent
	dcInternationWidgetComponent  *DcInternationWidgetComponent
	activationWidgetComponent     *ActivationWidgetComponent
	wealthBuilderLandingComponent *WealthBuilderLandingComponent
}

func NewComponentGeneratorFactory(
	criticalNotificationComponent *CriticalNotificationComponent,
	dashboardComponent *DashboardComponent,
	analyserComponent *AnalyserComponent,
	cardOffersComponent *CardOffersComponent,
	catalogOffersComponent *CatalogOffersComponent,
	helpComponent *HelpComponent,
	investmentComponent *InvestmentComponent,
	nudgesComponent *NudgesComponent,
	primaryFeatureComponent *PrimaryFeatureComponent,
	secondaryFeatureComponent *SecondaryFeatureComponent,
	promotionalBannerComponent *PromotionalBannerComponent,
	promotionalBanner2Component *PromotionalBanner2Component,
	recentAndUpcomingComponent *RecentAndUpcomingComponent,
	referralComponent *ReferralComponent,
	searchComponent *SearchComponent,
	shortcutsComponent *ShortcutsComponent,
	tabbedCardComponent *TabbedCardComponent,
	trustMarkerComponent *TrustMarkerComponent,
	moneySecretsComponent *MoneySecretsComponent,
	upiWidgetComponent *UpiWidgetComponent,
	journeysComponent *JourneysComponent,
	wealthAnalyserComponent *WealthAnalyserComponent,
	dcInternationWidgetComponent *DcInternationWidgetComponent,
	activationWidgetComponent *ActivationWidgetComponent,
	wealthBuilderLandingComponent *WealthBuilderLandingComponent,
) *ComponentGeneratorFactory {
	return &ComponentGeneratorFactory{
		criticalNotificationComponent: criticalNotificationComponent,
		dashboardComponent:            dashboardComponent,
		analyserComponent:             analyserComponent,
		cardOffersComponent:           cardOffersComponent,
		catalogOffersComponent:        catalogOffersComponent,
		helpComponent:                 helpComponent,
		investmentComponent:           investmentComponent,
		nudgesComponent:               nudgesComponent,
		primaryFeatureComponent:       primaryFeatureComponent,
		secondaryFeatureComponent:     secondaryFeatureComponent,
		promotionalBannerComponent:    promotionalBannerComponent,
		promotionalBanner2Component:   promotionalBanner2Component,
		recentAndUpcomingComponent:    recentAndUpcomingComponent,
		referralComponent:             referralComponent,
		searchComponent:               searchComponent,
		shortcutsComponent:            shortcutsComponent,
		tabbedCardComponent:           tabbedCardComponent,
		trustMarkerComponent:          trustMarkerComponent,
		moneySecretsComponent:         moneySecretsComponent,
		upiWidgetComponent:            upiWidgetComponent,
		journeysComponent:             journeysComponent,
		wealthAnalyserComponent:       wealthAnalyserComponent,
		dcInternationWidgetComponent:  dcInternationWidgetComponent,
		activationWidgetComponent:     activationWidgetComponent,
		wealthBuilderLandingComponent: wealthBuilderLandingComponent,
	}
}

// nolint:funlen
func (c *ComponentGeneratorFactory) GetComponentGenerator(componentId string) (ComponentGenerator, error) {
	switch componentId {
	case constants.CriticalNotificationScrollableComponentId.String():
		return c.criticalNotificationComponent, nil
	case constants.DashboardSectionComponentId.String():
		return c.dashboardComponent, nil
	case constants.ShortcutsScrollableComponentId.String(), constants.Shortcuts2ScrollableComponentId.String():
		return c.shortcutsComponent, nil
	case constants.AnalyserCardsScrollableComponentId.String():
		return c.analyserComponent, nil
	case constants.CardOffersScrollableComponentId.String():
		return c.cardOffersComponent, nil
	case constants.CatalogOffersScrollableComponentId.String():
		return c.catalogOffersComponent, nil
	case constants.HelpScrollableComponentId.String():
		return c.helpComponent, nil
	case constants.InvestmentScrollableComponentId.String():
		return c.investmentComponent, nil
	case constants.SuggestedForYouScrollableComponentId.String():
		return c.nudgesComponent, nil
	case constants.PrimaryFeatureScrollableComponentId.String():
		return c.primaryFeatureComponent, nil
	case constants.SecondaryFeatureScrollableComponentId.String():
		return c.secondaryFeatureComponent, nil
	case constants.PromotionalBannerScrollableComponentId.String():
		return c.promotionalBannerComponent, nil
	case constants.PromotionalBanner2ScrollableComponentId.String():
		return c.promotionalBanner2Component, nil
	case constants.RecentAndUpcomingScrollableComponentId.String():
		return c.recentAndUpcomingComponent, nil
	case constants.ReferralScrollableComponentId.String():
		return c.referralComponent, nil
	case constants.SearchBarScrollableComponentId.String():
		return c.searchComponent, nil
	case constants.TabbedCardScrollableComponentId.String():
		return c.tabbedCardComponent, nil
	case constants.TrustMarkerScrollableComponentId.String():
		return c.trustMarkerComponent, nil
	case constants.MoneySecretsScrollableComponentId.String():
		return c.moneySecretsComponent, nil
	case constants.UpiConnectScrollableComponentId.String():
		return c.upiWidgetComponent, nil
	case constants.JourneysScrollableComponentId.String():
		return c.journeysComponent, nil
	case constants.WealthAnalyserScrollableComponentId.String():
		return c.wealthAnalyserComponent, nil
	case constants.DcInternationalWidgetScrollableComponentId.String():
		return c.dcInternationWidgetComponent, nil
	case constants.ActivationWidgetScrollableComponentId.String():
		return c.activationWidgetComponent, nil
	case constants.WealthBuilderNetworthDashboardScrollableComponentId.String():
		return c.wealthBuilderLandingComponent, nil
	default:
		metrics.Recorder.RecordUnhandledComponentIdError(componentId, "")
		return nil, fmt.Errorf("invalid component id for scrollable component generator: %s", componentId)
	}
}
