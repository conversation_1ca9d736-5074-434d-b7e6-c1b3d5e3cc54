Application:
  Environment: "qa"
  Name: "frontend"
  EnableDeviceIntegrityCheck: true
  EnableLocationInterceptor: true
  MaxGRPCTimeout: "1m"

Server:
  Ports:
    GrpcPort: 8082
    GrpcSecurePort: 9509
    HttpPort: 9999
    HttpPProfPort: 9990

SecureLogging:
  EnablePartnerLog: false
  EnableSecureLog: true
  PartnerLogPath: "/var/log/frontend/partner/partner.log"
  SecureLogPath: "/var/log/frontend/secure.log"
  MaxSizeInMBs: 5
  MaxBackups: 20

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.qa-common-cache-redis.rxzivf.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 0
    ClientName: home

LegalDocuments:
  FiTncUrl: "https://web.qa.pointz.in/T&C"
  FederalBankTncUrl: "https://www.federalbank.co.in/epifi-tandc#CASA"
  FiPrivacyPolicyUrl: "https://web.qa.pointz.in/privacy"
  FiWealthTncUrl: "https://web.qa.pointz.in/wealth/TnC"
  OpenSourceLicenses:
    Firebase: ""
    Cronet: ""
    ChromeWebView: ""

#json file path
PayErrorViewJson: "./mappingJson/errorViewMapping.json"
CardErrorViewJson: "./mappingJson/cardErrorViewMapping.json"
DisplayCategoryMappingJson: "./mappingJson/displayCategoryMapping.json"


Flags:
  SkipAddMoney: false
  TrimDebugMessageFromStatus: false
  EnableCardTracking: true
  EnableCardBlock: true
  EnableCardQRCodeScan: true
  EnableCardOnlineTxnEnabledTile: true
  EnableCardOffers: true
  EnableVKYCOnlyForInternalUsers: true
  EnableVkycScheduleFlow: true
  # redmit 8A, Samsumng S20+, Samsung note10+
  SkipUserRevokeStateCheck: false
  EnableCardTrackingAndActivationChanges:
    IsEnableOnAndroid: true
    MinAndroidVersion: 117
    IsEnableOnIos: true
    MinIosVersion: 196
  EnableLivenessManualReviewInAfu: true
  EnableCheckForAccessRevoke: true
  EnableAutoInvestComponentOnInvestLanding:
    MinAndroidVersion: 231
    MinIOSVersion: 333
    FallbackToEnableFeature: false
    DisableFeature: false
  EnableUSStocksInstrumentCardFlag: true
  EnableMFInstrumentCardFlag: true
  EnableSDInstrumentCardFlag: true
  EnableFDInstrumentCardFlag: true
  EnableJumpInstrumentCardFlag: true
  EnableInvestmentLandingQuickLinksComponent:
    MinAndroidVersion: 237
    MinIOSVersion: 1272
    FallbackToEnableFeature: false
    DisableFeature: false
  EnablePhysicalCardChargesFlow:
    IsEnableOnAndroid: true
    MinAndroidVersion: 350
    IsEnableOnIos: true
    MinIosVersion: 2283
  EnablePhysicalCardChargesFlowWithTiering:
    IsEnableOnAndroid: true
    MinAndroidVersion: 350
    IsEnableOnIos: true
    MinIosVersion: 2283
  EnablePartnersComponentInvestLanding:
    IsEnableOnAndroid: false
    MinAndroidVersion: 1000
    IsEnableOnIos: false
    MinIosVersion: 1000
  UseAppsFlyerClientKeyV2: true
  EnableCCBillDashboardV2FeatureFlag:
    IsEnableOnAndroid: true
    MinAndroidVersion: 281
    IsEnableOnIos: true
    MinIosVersion: 1674
  EnableSecuredCardsRewardsDashboard:
    IsEnableOnAndroid: true
    MinAndroidVersion: 281
    IsEnableOnIos: true
    MinIosVersion: 381
  EnableLoungeAccessV2FeatureFlag:
    IsEnableOnAndroid: true
    MinAndroidVersion: 100
    IsEnableOnIos: true
    MinIosVersion: 1998
  EnableGenericRewardsDashboard:
    IsEnableOnAndroid: false
    MinAndroidVersion: 10000
    IsEnableOnIos: false
    MinIosVersion: 10000
  ReOOBECooldownBottomSheetScreenOptionsV2:
    MinAndroidVersion: 417
    MinIOSVersion: 2630
    FallbackToEnableFeature: false
    DisableFeature: false
  EnableCardDesignEnhancement:
    MinAndroidVersion: 444
    MinIOSVersion: 1000000
    FallbackToEnableFeature: false
    DisableFeature: false

RudderStack:
  Host: "https://rudder.pointz.in"
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

RewardsFrontendMeta:
  AndroidAppVersionsNotSupportingMultiChoiceRewards: [ 27 ]
  MinAndroidAppVersionWithAllRewardTileStatesClickable: 63
  MinAndroidAppVersionSupportingGiftHamperReward: 0
  MinIosAppVersionSupportingGiftHamperReward: 0
  MinAndroidAppVersionSupportingSalaryExclusiveOffer: 166
  MinIosAppVersionSupportingSalaryExclusiveOffer: 610
  MinAndroidAppVersionSupportingCBRV2: 143
  MinAndroidVersionSupportingLockingOfRewardsForMinKycUsers: 181
  MinIosVersionSupportingLockingOfRewardsForMinKycUsers: 724
  MinAndroidVersionForEmptyAddressesGracefulHandling: 9999
  MinIosVersionForEmptyAddressesGracefulHandling: 9999
  MinAndroidVersionSupportingBoosterFields: 209
  MinIosVersionSupportingBoosterFields: 982
  MinAndroidAppVersionSupportingUnredeemableOfferLabel: 194
  MinIosAppVersionSupportingUnredeemableOfferLabel: 1331
  MinAndroidAppVersionSupportingComingSoonOffer: 225
  MinIosAppVersionSupportingComingSoonOffer: 1331
  MinAndroidAppVersionSupportingInventoryExhaustedOffer: 225
  MinIosAppVersionSupportingInventoryExhaustedOffer: 1331
  MinAndroidAppVersionSupportingCardOfferCatalogV2: 228
  MinIosAppVersionSupportingCardOfferCatalogV2: 1280
  MinAndroidAppVersionSupportingCardOfferDetailsV2: 228
  MinIosAppVersionSupportingCardOfferDetailsV2: 1280
  MinAndroidAppVersionSupportingCreditCardOffersOnHome: 253
  MinIosAppVersionSupportingCreditCardOffersOnHome: 1493
  MinAndroidAppVersionSupportingThriweBenefitsPackageOffers: 242
  MinIosAppVersionSupportingThriweBenefitsPackageOffers: 1366
  MinAndroidAppVersionSupportingVistaraAirMilesOffer: 246
  MinIosAppVersionSupportingVistaraAirMilesOffer: 1382
  MinAndroidAppVersionSupportingYourRewardsCardV2: 249
  MinIosAppVersionSupportingYourRewardsCardV2: 1424
  MinAndroidAppVersionSupportingDefaultOfferType: 263
  MinIosAppVersionSupportingDefaultOfferType: 1545
  MinIosVersionSupportingMyRewardsV2Screen: 1623
  MinIosVersionWithRedeemedOffersScreenPaginationFix: 1719
  IsWebPageWithCardDetailsScreenEnabled: true
  MinAndroidVersionForWebPageWithCardDetailsScreen: 5620
  MinIosVersionForWebPageWithCardDetailsScreen: 2118
  WaysToEarnRewardsScreenVersionToRender: 1
  RewardClaimFlowAllowedNextScreenCTAs:
    TIER: true
  TagsConfig:
    TagNameToConfigMap:
      qa_automation_test_offer:
        RenderLocationToDisplayDetailsMap:
          fi_coin_offers_catalog_filters_list:
            ImageUrl: "https://epifi-icons.pointz.in/rewards/filters_qa_automation_test_filter.png"
            DisplayText: "QA Automation Test"
            TextColor: "#646464"
            BackgroundColor: "#FFFFFF"
            Priority: 1
          fi_coin_offers_catalog_all_filters:
            ImageUrl: "https://epifi-icons.pointz.in/rewards/filters_qa_automation_test_filter.png"
            DisplayText: "QA Automation Test"
            TextColor: "#646464"
            BackgroundColor: "#FFFFFF"
            Priority: 1
          offers_catalog_page_filters_list:
            ImageUrl: "https://epifi-icons.pointz.in/rewards/filters_qa_automation_test_filter.png"
            DisplayText: "QA Automation"
            TextColor: "#6A6D70"
            BackgroundColor: "#28292B"
            Priority: 2
      exclusive_offer:
        RenderLocationToDisplayDetailsMap:
          home_screen:
            ImageUrl: "https://epifi-icons.pointz.in/rewards/exclusive-star.png"
            DisplayText: "EXCLUSIVE"
            TextColor: "#FFFFFF"
            BackgroundColor: "#33FFFFFF"
            Priority: 1
      credit_card_exclusive:
        RenderLocationToDisplayDetailsMap:
          fi_coin_offers_catalog_card:
            ImageUrl: "https://epifi-icons.pointz.in/casper/card_offers_2023/debit_card/Credit_card/CC_mini_icon.png"
            DisplayText: "Credit card\nexclusive"
            TextColor: "#FFFFFF"
            BackgroundColor: "#********"
            Priority: 1
          collected_offers_screen:
            ImageUrl: "https://epifi-icons.pointz.in/casper/card_offers_2023/debit_card/Credit_card/CC_mini_icon.png"
            DisplayText: "Credit card\nexclusive"
            TextColor: "#FFFFFF"
            BackgroundColor: "#********"
            Priority: 1
          fi_coin_offers_catalog_filters_list:
            ImageUrl: "https://epifi-icons.pointz.in/rewards/filters_cc_exclusive_icon.png"
            DisplayText: "Credit Card Exclusive"
            TextColor: "#B9B9B9"
            BackgroundColor: "#383838"
            Priority: 4
          fi_coin_offers_catalog_all_filters:
            ImageUrl: "https://epifi-icons.pointz.in/rewards/filters_cc_exclusive_icon.png"
            DisplayText: "Credit Card Exclusive"
            TextColor: "#646464"
            BackgroundColor: "#F7F9FA"
            Priority: 4
      salary_exclusive_v2:
        RenderLocationToDisplayDetailsMap:
          fi_coin_offers_catalog_card:
            ImageUrl: "https://epifi-icons.pointz.in/casper/salary-program-tag-star.png"
            DisplayText: "Salary account\nexclusive"
            TextColor: "#FFFFFF"
            BackgroundColor: "#********"
            Priority: 2
          collected_offers_screen:
            ImageUrl: "https://epifi-icons.pointz.in/casper/salary-program-tag-star.png"
            DisplayText: "Salary account\nexclusive"
            TextColor: "#FFFFFF"
            BackgroundColor: "#********"
            Priority: 2
          fi_coin_offers_catalog_filters_list:
            ImageUrl: "https://epifi-icons.pointz.in/rewards/filters_salary_exclusive_icon.png"
            DisplayText: "Salary Exclusive"
            TextColor: "#B9B9B9"
            BackgroundColor: "#383838"
            Priority: 3
          fi_coin_offers_catalog_all_filters:
            ImageUrl: "https://epifi-icons.pointz.in/rewards/filters_salary_exclusive_icon.png"
            DisplayText: "Salary Exclusive"
            TextColor: "#646464"
            BackgroundColor: "#F7F9FA"
            Priority: 3
      redemption_mechanism_catalog_fi_coins:
        RenderLocationToDisplayDetailsMap:
          home_screen:
            DisplayText: "USE FI-COINS"
            TextColor: "#FFFFFF"
            BackgroundColor: "#33FFFFFF"
            Priority: 2
      redemption_mechanism_catalog_fi_card:
        RenderLocationToDisplayDetailsMap:
          home_screen:
            DisplayText: "DEBIT CARD"
            TextColor: "#FFFFFF"
            BackgroundColor: "#33FFFFFF"
            Priority: 2
      redemption_mechanism_catalog_fi_credit_card:
        RenderLocationToDisplayDetailsMap:
          home_screen:
            DisplayText: "CREDIT CARD"
            TextColor: "#FFFFFF"
            BackgroundColor: "#33FFFFFF"
            Priority: 2
      redemption_mechanism_cbr:
        RenderLocationToDisplayDetailsMap:
          home_screen:
            DisplayText: "PLAY & WIN"
            TextColor: "#FFFFFF"
            BackgroundColor: "#33FFFFFF"
            Priority: 2
          fi_coin_offers_catalog_filters_list:
            ImageUrl: "https://epifi-icons.pointz.in/rewards/filters_cbr_icon.png"
            DisplayText: "Play & Win"
            TextColor: "#B9B9B9"
            BackgroundColor: "#383838"
            Priority: 4
          fi_coin_offers_catalog_all_filters:
            ImageUrl: "https://epifi-icons.pointz.in/rewards/filters_cbr_icon.png"
            DisplayText: "Play & Win"
            TextColor: "#646464"
            BackgroundColor: "#F7F9FA"
            Priority: 5
      reward_type_cash:
        RenderLocationToDisplayDetailsMap:
          fi_coin_offers_catalog_filters_list:
            ImageUrl: "https://epifi-icons.pointz.in/rewards/filters_cash_icon.png"
            DisplayText: "Win cash"
            TextColor: "#B9B9B9"
            BackgroundColor: "#383838"
            Priority: 10
          fi_coin_offers_catalog_all_filters:
            ImageUrl: "https://epifi-icons.pointz.in/rewards/filters_cash_icon.png"
            DisplayText: "Win cash"
            TextColor: "#646464"
            BackgroundColor: "#F7F9FA"
            Priority: 10
      discounted:
        RenderLocationToDisplayDetailsMap:
          fi_coin_offers_catalog_filters_list:
            ImageUrl: "https://epifi-icons.pointz.in/rewards/filters_discounted_icon.png"
            DisplayText: "Discounted"
            TextColor: "#B9B9B9"
            BackgroundColor: "#383838"
            Priority: 7
          fi_coin_offers_catalog_all_filters:
            ImageUrl: "https://epifi-icons.pointz.in/rewards/filters_discounted_icon.png"
            DisplayText: "Discounted"
            TextColor: "#646464"
            BackgroundColor: "#F7F9FA"
            Priority: 7
      reward_type_egv:
        RenderLocationToDisplayDetailsMap:
          fi_coin_offers_catalog_filters_list:
            ImageUrl: "https://epifi-icons.pointz.in/rewards/filters_egv_icon.png"
            DisplayText: "Vouchers"
            TextColor: "#B9B9B9"
            BackgroundColor: "#383838"
            Priority: 6
          fi_coin_offers_catalog_all_filters:
            ImageUrl: "https://epifi-icons.pointz.in/rewards/filters_egv_icon.png"
            DisplayText: "Vouchers"
            TextColor: "#646464"
            BackgroundColor: "#F7F9FA"
            Priority: 6
      reward_type_physical_merchandise:
        RenderLocationToDisplayDetailsMap:
          fi_coin_offers_catalog_filters_list:
            ImageUrl: "https://epifi-icons.pointz.in/rewards/filters_merchandise_icon.png"
            DisplayText: "Merchandise"
            TextColor: "#B9B9B9"
            BackgroundColor: "#383838"
            Priority: 8
          fi_coin_offers_catalog_all_filters:
            ImageUrl: "https://epifi-icons.pointz.in/rewards/filters_merchandise_icon.png"
            DisplayText: "Merchandise"
            TextColor: "#646464"
            BackgroundColor: "#F7F9FA"
            Priority: 8
      coming_soon:
        RenderLocationToDisplayDetailsMap:
          fi_coin_offers_catalog_filters_list:
            ImageUrl: "https://epifi-icons.pointz.in/rewards/filters_widget_coming_soon_icon.png"
            DisplayText: "Coming soon"
            TextColor: "#B9B9B9"
            BackgroundColor: "#383838"
            Priority: 9
          fi_coin_offers_catalog_all_filters:
            ImageUrl: "https://epifi-icons.pointz.in/rewards/filters_widget_coming_soon_icon.png"
            DisplayText: "Coming soon"
            TextColor: "#646464"
            BackgroundColor: "#F7F9FA"
            Priority: 9
      campaign_special:
        RenderLocationToDisplayDetailsMap:
          fi_coin_offers_catalog_filters_list:
            ImageUrl: "https://epifi-icons.pointz.in/rewards/filters_campaign_christmas_special.png"
            DisplayText: "X-mas Special"
            TextColor: "#B9B9B9"
            BackgroundColor: "#383838"
            Priority: 2
          fi_coin_offers_catalog_all_filters:
            ImageUrl: "https://epifi-icons.pointz.in/rewards/filters_campaign_christmas_special.png"
            DisplayText: "X-mas Special"
            TextColor: "#646464"
            BackgroundColor: "#F7F9FA"
            Priority: 2
      offer_type_power_up:
        RenderLocationToDisplayDetailsMap:
          fi_coin_offers_catalog_filters_list:
            ImageUrl: "https://epifi-icons.pointz.in/rewards/filters_power_up_icon.png"
            DisplayText: "Power-up"
            TextColor: "#B9B9B9"
            BackgroundColor: "#383838"
            Priority: 2
          fi_coin_offers_catalog_all_filters:
            ImageUrl: "https://epifi-icons.pointz.in/rewards/filters_power_up_icon.png"
            DisplayText: "Power-up"
            TextColor: "#646464"
            BackgroundColor: "#F7F9FA"
            Priority: 2
  DebitCardRewardsConfig:
    InternationalSpendsRewardOfferId: "801407b1-27be-4b33-a250-162de890512a"
  OffersCatalogPageConfig:
    ForceUpgradeFeatureConfig:
      MinAndroidVersion: 466
      MinIOSVersion: 2930

Aws:
  Region: "ap-south-1"

Secrets:
  Ids:
    RudderWriteKey: "qa/rudder/internal-writekey"
    GoogleCloudProfilingServiceAccountKey: "qa/gcloud/profiling-service-account-key"
    RudderClientApiKey: "qa/rudder/android_write_key"
    AppsFlyerClientKey: "qa/appsflyer/client_key"
    OneMoneyClientSecret: "qa/onemoney/client_secret"
    DeviceIdsEnabledForSafetyNetV2: "qa/frontend-auth/v2_safetynet_enabled_device_ids"
    MoengageAppSdkKey: "qa/moengage/app-sdk-key"
    ActiveDeviceIntegrityTokenSigningKey: "qa/frontend-auth/device-integrity-token-signing-key"
    MiAmpPushSecretJson: "qa/mi_amp_push/mi_secret_json"
    AppsFlyerClientKeyV2: "qa/appsflyer/client_key_2"
    RudderIosClientApiKey: "qa/rudder/ios_write_key"
    RazorpaySecrets: "qa/frontend/razorpay-federal-secured-cards-api-key"



DeviceIntegrity:
  EnableWhitelistedTokens: true
  SkipAsyncDeviceIntegrityChecks: false
  WhitelistedTokensList: [ "DUMMY_TOKEN" ]
  DefaultHighRiskDeviceConsentDuration: "24h"
  MaxHighRiskDeviceConsentDuration: "1080h" # 45 days
  AsyncDeviceIntegrityCheck:
    DisableFeature: false
    MinAndroidVersion: 174
    MinIOSVersion: 481

AFU:
  AllowSimUpdateAFU: true
  EnableAccountInactiveCheck: false

VKYC:
  PopupTileDuration: 12 # in hours
  PopupTileNonDismissableAfter: 60 # in minutes
  AccountFreezePopupNonDismissibleWithinDays: 30 # in days
  AccountFreezeThreshold: "4320h" # 180 days
  SavingsBalanceLimitPercent: 20
  CreditBalanceLimitPercent: 50
  PerformEkycAfter: "72h"
  SlotDays: 5
  MorningStart: 8
  SplitMorning: 12
  SplitAfternoon: 15
  SplitLateAfternoon: 18
  EveningEnd: 22
  ScheduleToLiveCutOffMinutes: 2
  DisableVKYCOutOfBizHoursForForceLSO: false
  NewPopupTileAccountCreationTimeLimit: 180 # in days
  NewPopupTileDuration: 84 # in hours
  HomeBannerColorHex: "#383838"
  ABFeatureReleaseConfig:
    FeatureConstraints:
      - VKYC_NEW_REVIEW_SCREEN:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 332
              MinIOSVersion: 0
          Buckets:
            - ONE:
                Start: 0
                End: 99

InsightsParams:
  GetInsightConfig:
    MarkNoticedAfter: 2
  EnableDailyChangeComponentViaSegment: false
  EpfConfig:
    AndroidAppVersionSkipEpfDeeplink: 463

UserProfile:
  ProfileHeaderV2MinVersion:
    MinVersionAndroid: 321
    MinVersionIos: 2077
  IsEmailEditableConfig:
    DisableFeature: false
    MinAndroidVersion: 1
    MinIOSVersion: 1

Card:
  MinAndroidVersionForFMAuth: 56
  CardDynamicTileDuration:
    ViewCardDeliveryTracking: "5s"
    QRCodeAsPrimaryTile: "15s"
    ViewCardOnlineTxnEnabledTile: "5s"
    ViewQRCodeScanTime: "10s"
    ViewSecurePinActivationTime: "20s"
  EnableSecurePinActivationFlow:
    IsEnableOnAndroid: true
    MinAndroidVersion: 250
    IsEnableOnIos: true
    MinIosVersion: 250
  MinAndroidVersionForCardOffers: 79
  MinAndroidVersionForCardTracking: 83
  MinAndroidVersionForSecurePinValidation: 107
  MinIOSVersionForSecurePinValidation: 176
  EnableSecurePinValidationAuth: true
  OffersDynamicTileExpiryTime: "31-08-2022T23:59:00"
  EnableCardOffersInformativeDynamicTile: false
  PrintingToDispatchDynamicTileDuration: "10m"
  PhysicalCardChargingFlowStartTimestamp: 1675949085
  EnableDcCardRenewalChargesFlow:
    IsEnableOnAndroid: true
    MinAndroidVersion: 32
    IsEnableOnIos: true
    MinIosVersion: 465
  ShowPhysicalCardOrderEntryPointInCardSettings:
    IsEnableOnAndroid: true
    MinAndroidVersion: 363
    IsEnableOnIos: true
    MinIosVersion: 2315
  EnablePinSetFlowRedirectionPlatformVersionCheck:
    IsEnableOnAndroid: false
    MinAndroidVersion: 3000
    IsEnableOnIos: false
    MinIosVersion: 3000
  DashboardV2Config:
    IsQuestCheckEnabledForDashboardV2: true
    IsDashboardV2EnabledByQuest: true
    SectionsConfig:
      CardSectionConfig:
        ShowTapnPaySettingOnHome:
          IsEnableOnAndroid: true
          MinAndroidVersion: 395
          IsEnableOnIos: true
          MinIosVersion: 2495
      ShortcutsSectionConfig:
        PlanTravelBudgetUrl: "https://web.qa.pointz.in/travel-budget"
        EnableAtmLocatorShortcut:
          IsEnableOnAndroid: false
          MinAndroidVersion: 100
          IsEnableOnIos: true
          MinIosVersion: 2641
        AtmLocatorUserGrpCheck:
          EnableUserGroupCheck: true
          AllowedUserGrp:
            - 1 # INTERNAL

Comms:
  NotificationsPageSize: 10

Screening:
  EmpVerificationCheckStatusPollIntervalInSecs: 5
  CheckCreditReportAvailabilityStatusPollIntervalInSecs: 5
  CheckCreditReportVerificationStatusPollIntervalInSecs: 5

Referrals:
  LandingPageVersion: 2 # referrals-v1
  IsReferralsViaFiniteCodeAllowed: true
  ReferralsHistoryPageSize: 1
  AppDownloadUrlWithFiniteCode: "https://test-onelink.onelink.me/5jZl/u4zguvmu?referralCode=%s"
  MinAndroidVersionSupportingNewHomeReferralsWidget: 195
  MinIosVersionSupportingNewHomeReferralsWidget: 818
  MinIosVersionSupportingCtasFixForHomeV1EntryPoint: 1169
  LandingScreenPromotionalBanner:
    ShowBanner: true
    Title: "Earn assured ₹230 per referral & stand a chance to win an iPhone 14"
    ImageUrl: "https://epifi-icons.pointz.in/referrals/iphone.png"
    BgColor: "#555555"
    Cta:
      Text: "See Details"
      TextColor: "#FFFFFF"
      BgColor: "#00B899"
      ImageUrl: "https://epifi-icons.pointz.in/referrals/chevron-right.png"
      IsVisible: true
      IsEnabled: true
    ExpandedStateDesc: "Earn assured ₹230 per referral & stand a chance to win an iPhone 14"
    ExpandedStateInfos1: "You need to maintain an average balance of ₹2000 or more across your Savings Account and Deposits for at least 10 days to unlock referrals\nOnce you unlock referrals, share your unique finite code with your friends\nWhen a friend opens an account on Fi and adds at least ₹3000 in their savings account you get a reward\nYou will win assured ₹200 and your friend earns atleast ₹200 in cash rewards\nYou will get another money plant that can be unlocked on October 16. On growing this money plant, you can win an iPhone or assured cash reward of ₹30-₹1000\nThe second money plant is only available for successfully referrals made between September 24, 10 am and October 14"
    ExpandedStateInfos2: "To qualify you have to maintain an average balance of ₹2000 or more across your Savings Account and Deposits for at least 10 days\nYour friend has to use your unique Finite code when opening their account\nYou will get a reward only when your friend adds at least ₹3000 to their savings account within 7 days of opening an account\nA maximum of 40 referral rewards can be earned by you in a financial year\nThis offer is valid from 24 Sep'22 to 14 Oct'22\nReward amount is determined based on the reward offer active when your referee creates a Fi account and not according to the offer active when you shared your Fi.nite code\nReward programs can be terminated without prior notice at the Company's discretion\nPlease also note that users of our Platform residing in the State of Tamil Nadu are not eligible to participate in specific rewards/offers as per the applicable law in Tamil Nadu, thus users residing in Tamil Nadu are requested not to participate in offers relating to cash backs etc"
  InfoDuringOnboarding:
    OfferCodes:
      - CODE_1:
          IsEnabled: true
          Icon: "https://epifi-icons.pointz.in/referrals/referrals_v1/onboarding/referral_entry/FI200-offer-icon.png"
          DisplayCode: "FI200"
          UnderlyingFiniteCode: "6VLHF3ZZWH"
          Offer: "Get up to ₹200 cashback"
          Desc: "When you add money to your account"
          CtaText: "Apply now"
          CodeAppliedPopupDetails:
            Image: "https://epifi-icons.pointz.in/referrals/referrals_v1/onboarding/referral_entry/code-applied-1.png"
            Title: "Yay! “FI200” applied"
            Subtitle: "You get up to ₹200 cashback"
            Desc: "Sign up & add funds to your Savings Account to claim this offer"
            CanDismiss: true
          Identifier: "OnboardingOfferCode1"
      - CODE_2:
          IsEnabled: true
          Icon: "https://epifi-icons.pointz.in/referrals/referrals_v1/onboarding/referral_entry/FiCoins-offer-icon.png"
          DisplayCode: "FI2500"
          UnderlyingFiniteCode: "6VLHF3ZZWH" # todo: change to some other code
          Offer: "Get flat 2,500 Fi-Coins"
          Desc: "When you complete 3 UPI payments using Fi"
          CtaText: "Apply now"
          CodeAppliedPopupDetails:
            Image: "https://epifi-icons.pointz.in/referrals/referrals_v1/onboarding/referral_entry/code-applied-1.png"
            Title: "Yay! “FI2500” applied"
            Subtitle: "You get flat 2,500 Fi-Coins"
            Desc: "Sign up & complete 3 UPI payments using Fi to claim this offer"
            CanDismiss: true
          Identifier: "OnboardingOfferCode2"
    ReferralCodeAppliedPopupDetails:
      Image: "https://epifi-icons.pointz.in/referrals/referrals_v1/onboarding/referral_entry/code-applied-1.png"
      Title: "Yay! Code applied"
      Subtitle: "You’re off to a great start 🎉"
      Desc: "Continue signing up to Fi"
      CanDismiss: false
    Fi200ReferralCodeAppliedPopupDetails:
      Image: "https://epifi-icons.pointz.in/referrals/referrals_v1/onboarding/referral_entry/code-applied-1.png"
      Title: "Yay! Code applied"
      Subtitle: "You'll get up to ₹200 cashback"
      Desc: "Sign up & add funds to your Savings Account to claim this offer"
      CanDismiss: false
    ReferralCodeClaimFailedPopup:
      MinAndroidVersion: 1
      MinIOSVersion: 1
    MaxReferralCodeClaimAttempts: 3
  FeatureRestrictionParams:
    AgeRestriction:
      Enable: false
      AgeLowerBound: 30

ReferralsV1:
  MinAndroidVersionSupportingInviteContactsSection: 228
  MinIosVersionSupportingInviteContactsSection: 1256
  MinAndroidVersionForImplicitCopyCodeButton: 236
  MinIosVersionForImplicitCopyCodeButton: 1258
  ReferralLinkGenerationInfoConf:
    LinkValidForSinceGeneration: 20m
    OnelinkTemplateId: "5jZl"
    AfChannel: "referrals-channel"
    Campaign: "referrals-campaign"
    OnelinkGenerationParams:
      - deep_link_sub5: "FINITE_CODE_PLACEHOLDER"
      - pid: "referrals-pid"
  AppUpdateHardNudgeConf:
    ShowNudgeTillAppVersionAndroid: 8999
    ShowNudgeTillAppVersionIos: 8999
    UserBucketStart: 0
    UserBucketEnd: 99
  AppUpdateSoftNudgeConf:
    ShowNudgeTillAppVersionAndroid: 8999
    ShowNudgeTillAppVersionIos: 8999
    UserBucketStart: 0
    UserBucketEnd: 99
  StackedRewards:
    StartDateTime: "2023-08-10T15:04:05+05:30"
    EndDateTime: "2033-08-10T15:04:05+05:30"
    ShowReferralHistoryEarningSummary: true
    RefereeSignupActionExpiryDuration: 168h
    RefereeAddMoneyActionExpiryDuration: 168h

SalaryProgram:
  SalaryLiteConfig:
    IsSalaryLiteMandateSetupDropOffFeedbackFlowEnabled: true
    IsSalaryLiteEnachDropOffFeedbackFlowEnabled: true
  LandingPageCommsInfoSectionFeatureConfig:
    DisableFeature: false
    MinAndroidVersion: 100
    MinIOSVersion: 100
  LandingPageQuickLinkTilesV1FeatureConfig:
    DisableFeature: false
    MinAndroidVersion: 100
    MinIOSVersion: 100
  ShareDetailsOnMailV1FeatureConfig:
    DisableFeature: false
  HomeCard:
    ShowCard: true
    HeaderTitle: "Fi Salary Benefits"
    BenefitsInactiveTitle: "Get flat 10% of your salary as Fi-Coins"
    RegCompleteBenefitsInactiveTitle: "Get flat 10% of your salary as Fi-Coins"
    BenefitsActiveTitle: "Get flat 10% of your salary as Fi-Coins"
    BenefitsInactiveCta:
      BgColor: "#7FBECE"
      IsVisible: true
      Text: "Get Salary Account"
      TextColor: "#FFFFFF"
    RegCompleteBenefitsInactiveCta:
      BgColor: "#7FBECE"
      IsVisible: true
      Text: "Upgrade Now"
      TextColor: "#FFFFFF"
    BenefitsActiveCta:
      BgColor: "#7FBECE"
      IsVisible: true
      Text: "View All Benefits"
      TextColor: "#FFFFFF"
    BenefitsInactiveTag:
      BgColor: "#DEEEF2"
      Tag: ""
      TextColor: "#4E9199"
    RegCompleteBenefitsInactiveTag:
      BgColor: "#DEEEF2"
      Tag: ""
      TextColor: "#4E9199"
    BenefitsActiveTag:
      BgColor: "#DEEEF2"
      Tag: ""
      TextColor: "#4E9199"
    BenefitsInactiveImageUrl: "https://epifi-icons.pointz.in/coins-home-card.png"
    RegCompleteBenefitsInactiveImageUrl: "https://epifi-icons.pointz.in/coins-home-card.png"
    BenefitsActiveImageUrl: "https://epifi-icons.pointz.in/coins-home-card.png"
    BenefitsInactiveBgColor: "#C0DAE0"
    RegCompleteBenefitsInactiveBgColor: "#C0DAE0"
    BenefitsActiveBgColor: "#C0DAE0"
  HomeCardsTimeBound:
    Custom1:
      ActiveFrom: "2022-12-01T00:00:00+05:30"
      ActiveTill: "2022-12-03T23:59:59+05:30"
      ShowCard: true
      BenefitsActiveBgColor: "#C0DAE0"
      BenefitsActiveCta:
        BgColor: "#7FBECE"
        IsVisible: true
        Text: "Invite Now"
        TextColor: "#FFFFFF"
      BenefitsActiveImageUrl: "https://epifi-icons.pointz.in/salaryprogram/bag.png"
      BenefitsActiveTag:
        BgColor: "#D9F2CC"
        Tag: ""
        TextColor: "#5D7D4C"
      BenefitsActiveTitle: "Monthly bonus: Lifestyle Voucher worth ₹500 at 500 Fi-Coins"
      BenefitsInactiveBgColor: "#C0DAE0"
      BenefitsInactiveCta:
        BgColor: "#7FBECE"
        IsVisible: true
        Text: "Upgrade Now"
        TextColor: "#FFFFFF"
      BenefitsInactiveImageUrl: "https://epifi-icons.pointz.in/salaryprogram/amazon_voucher.png"
      BenefitsInactiveTag:
        BgColor: "#D9F2CC"
        Tag: ""
        TextColor: "#5D7D4C"
      BenefitsInactiveTitle: "Get a ₹1000 Amazon Voucher when you activate salary benefits"
      HeaderTitle: "Fi Salary Benefits"
      RegCompleteBenefitsInactiveBgColor: "#C0DAE0"
      RegCompleteBenefitsInactiveCta:
        BgColor: "#7FBECE"
        IsVisible: true
        Text: "Upgrade Now"
        TextColor: "#FFFFFF"
      RegCompleteBenefitsInactiveImageUrl: "https://epifi-icons.pointz.in/salaryprogram/amazon_voucher.png"
      RegCompleteBenefitsInactiveTag:
        BgColor: "#D9F2CC"
        Tag: ""
        TextColor: "#5D7D4C"
      RegCompleteBenefitsInactiveTitle: "Get a ₹1000 Amazon Voucher when you activate salary benefits"
    Custom2:
      ActiveFrom: "2022-12-04T00:00:00+05:30"
      ActiveTill: "2022-12-07T23:59:59+05:30"
      ShowCard: true
      BenefitsActiveBgColor: "#C0DAE0"
      BenefitsActiveCta:
        BgColor: "#7FBECE"
        IsVisible: true
        Text: "View All Benefits"
        TextColor: "#FFFFFF"
      BenefitsActiveImageUrl: "https://epifi-icons.pointz.in/coins-home-card.png"
      BenefitsActiveTag:
        BgColor: "#DEEEF2"
        Tag: ""
        TextColor: "#4E9199"
      BenefitsActiveTitle: "Get flat 10% of your salary as Fi-Coins"
      BenefitsInactiveBgColor: "#C0DAE0"
      BenefitsInactiveCta:
        BgColor: "#7FBECE"
        IsVisible: true
        Text: "Get Salary Account"
        TextColor: "#FFFFFF"
      BenefitsInactiveImageUrl: "https://epifi-icons.pointz.in/coins-home-card.png"
      BenefitsInactiveTag:
        BgColor: "#DEEEF2"
        Tag: ""
        TextColor: "#4E9199"
      BenefitsInactiveTitle: "Get flat 10% of your salary as Fi-Coins"
      HeaderTitle: "Fi Salary Benefits"
      RegCompleteBenefitsInactiveBgColor: "#C0DAE0"
      RegCompleteBenefitsInactiveCta:
        BgColor: "#7FBECE"
        IsVisible: true
        Text: "Upgrade Now"
        TextColor: "#FFFFFF"
      RegCompleteBenefitsInactiveImageUrl: "https://epifi-icons.pointz.in/coins-home-card.png"
      RegCompleteBenefitsInactiveTag:
        BgColor: "#DEEEF2"
        Tag: ""
        TextColor: "#4E9199"
      RegCompleteBenefitsInactiveTitle: "Get flat 10% of your salary as Fi-Coins"
  HomeCardsB2BTimeBound:
    Custom1:
      ActiveFrom: "2022-12-01T00:00:00+05:30"
      ActiveTill: "2022-12-03T23:59:59+05:30"
      ShowCard: true
      BenefitsActiveBgColor: "#C0DAE0"
      BenefitsActiveCta:
        BgColor: "#7FBECE"
        IsVisible: true
        Text: "View All Benefits"
        TextColor: "#FFFFFF"
      BenefitsActiveImageUrl: "https://epifi-icons.pointz.in/salaryprogram/bag.png"
      BenefitsActiveTag:
        BgColor: "#DEEEF2"
        Tag: ""
        TextColor: "#4E9199"
      BenefitsActiveTitle: "Monthly bonus: Lifestyle Voucher worth ₹500 at 500 Fi-Coins"
      BenefitsInactiveBgColor: "#C0DAE0"
      BenefitsInactiveCta:
        BgColor: "#7FBECE"
        IsVisible: true
        Text: "Get Salary Account"
        TextColor: "#FFFFFF"
      BenefitsInactiveImageUrl: "https://epifi-icons.pointz.in/coins-home-card.png"
      BenefitsInactiveTag:
        BgColor: "#DEEEF2"
        Tag: ""
        TextColor: "#4E9199"
      BenefitsInactiveTitle: "Get flat 10% of your salary as Fi-Coins"
      HeaderTitle: "Fi Salary Benefits"
      RegCompleteBenefitsInactiveBgColor: "#C0DAE0"
      RegCompleteBenefitsInactiveCta:
        BgColor: "#7FBECE"
        IsVisible: true
        Text: "Upgrade Now"
        TextColor: "#FFFFFF"
      RegCompleteBenefitsInactiveImageUrl: "https://epifi-icons.pointz.in/coins-home-card.png"
      RegCompleteBenefitsInactiveTag:
        BgColor: "#DEEEF2"
        Tag: ""
        TextColor: "#4E9199"
      RegCompleteBenefitsInactiveTitle: "Get flat 10% of your salary as Fi-Coins"
    Custom2:
      ActiveFrom: "2022-12-04T00:00:00+05:30"
      ActiveTill: "2022-12-07T23:59:59+05:30"
      ShowCard: true
      BenefitsActiveBgColor: "#C0DAE0"
      BenefitsActiveCta:
        BgColor: "#7FBECE"
        IsVisible: true
        Text: "View All Benefits"
        TextColor: "#FFFFFF"
      BenefitsActiveImageUrl: "https://epifi-icons.pointz.in/coins-home-card.png"
      BenefitsActiveTag:
        BgColor: "#DEEEF2"
        Tag: ""
        TextColor: "#4E9199"
      BenefitsActiveTitle: "Get flat 10% of your salary as Fi-Coins"
      BenefitsInactiveBgColor: "#C0DAE0"
      BenefitsInactiveCta:
        BgColor: "#7FBECE"
        IsVisible: true
        Text: "Get Salary Account"
        TextColor: "#FFFFFF"
      BenefitsInactiveImageUrl: "https://epifi-icons.pointz.in/coins-home-card.png"
      BenefitsInactiveTag:
        BgColor: "#DEEEF2"
        Tag: ""
        TextColor: "#4E9199"
      BenefitsInactiveTitle: "Get flat 10% of your salary as Fi-Coins"
      HeaderTitle: "Fi Salary Benefits"
      RegCompleteBenefitsInactiveBgColor: "#C0DAE0"
      RegCompleteBenefitsInactiveCta:
        BgColor: "#7FBECE"
        IsVisible: true
        Text: "Upgrade Now"
        TextColor: "#FFFFFF"
      RegCompleteBenefitsInactiveImageUrl: "https://epifi-icons.pointz.in/coins-home-card.png"
      RegCompleteBenefitsInactiveTag:
        BgColor: "#DEEEF2"
        Tag: ""
        TextColor: "#4E9199"
      RegCompleteBenefitsInactiveTitle: "Get flat 10% of your salary as Fi-Coins"
  EntryPointSectionInfo:
    BenefitsActivePromoBannerTileInfo:
      IsVisible: true
      Title: "Get benefits upto ₹30,000 with Fi Salary Program"
      TitleColor: "#383838"
      BgColor: "#BBC8E9"
      ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/Homebanner/Cash_cash.png"
      Cta:
        Deeplink:
          Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
      Tag:
        BgColor: "#CDC6E8"
        Tag: ""
        TextColor: "#6F62A4"
    RegCompletedBenefitsInActivePromoBannerTileInfo:
      IsVisible: true
      Title: "Get benefits upto ₹30,000 with Fi Salary Program"
      TitleColor: "#383838"
      BgColor: "#BBC8E9"
      ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/Homebanner/Cash_cash.png"
      Cta:
        Deeplink:
          Screen: "SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"
      Tag:
        BgColor: ""
        Tag: ""
        TextColor: ""
    RegNotCompletedPromoBannerTileInfo:
      IsVisible: true
      Title: "Get benefits upto ₹30,000 with Fi Salary Program"
      TitleColor: "#383838"
      BgColor: "#BBC8E9"
      ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/Homebanner/Cash_cash.png"
      Cta:
        Deeplink:
          Screen: "SALARY_PROGRAM_INTRO_SCREEN"
      Tag:
        BgColor: ""
        Tag: ""
        TextColor: ""
  SalaryWinningSectionPrioritizedMonthlyIntervals:
    StartDate: 6
    EndDate: 23


  EntryPointSectionInfoTimeBound:
    Custom1:
      ActiveFrom: '2023-11-27T00:00:00+05:30'
      ActiveTill: '2023-11-30T06:59:59+05:30'
      IsVisible: true
      BenefitsActiveSummaryTileInfo:
        IsVisible: true
        HeaderTitle: "Get ₹30k in"
        HeaderTitleColor: "#929599"
        Title: |-
          Salary
          Benefits
        TitleColor: '#2A496F'
        BgColor: '#FFFFFF'
        ImageUrl: 'https://epifi-icons.pointz.in/salaryprogram/star.png'
        Cta:
          Text: View All
          TextColor: '#4F71AB'
          BgColor: "#FFFFFF"
          ImageUrl: >-
            https://epifi-icons.pointz.in/salaryprogram/right-chevron-indigo.png
          Deeplink:
            Screen: SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN
      RegCompletedBenefitsInActiveSummaryTileInfo:
        IsVisible: true
        HeaderTitle: "Get ₹30k in"
        HeaderTitleColor: "#929599"
        Title: |-
          Salary
          Benefits
        TitleColor: '#2A496F'
        BgColor: '#FFFFFF'
        ImageUrl: 'https://epifi-icons.pointz.in/salaryprogram/star.png'
        Cta:
          Text: View All
          TextColor: '#4F71AB'
          BgColor: "#FFFFFF"
          ImageUrl: >-
            https://epifi-icons.pointz.in/salaryprogram/right-chevron-indigo.png
          Deeplink:
            Screen: SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN
      RegNotCompletedSummaryTileInfo:
        IsVisible: true
        HeaderTitle: "Get ₹30k in"
        HeaderTitleColor: "#929599"
        Title: |-
          Salary
          Benefits
        TitleColor: '#2A496F'
        BgColor: '#FFFFFF'
        ImageUrl: 'https://epifi-icons.pointz.in/salaryprogram/star.png'
        Cta:
          Text: Upgrade Now
          TextColor: '#4F71AB'
          BgColor: "#FFFFFF"
          ImageUrl: >-
            https://epifi-icons.pointz.in/salaryprogram/right-chevron-indigo.png
          Deeplink:
            Screen: SALARY_PROGRAM_INTRO_SCREEN
      RegCompletedBenefitsInActiveStatusTileInfo:
        IsVisible: false
        Title: Your salary has not arrived yet
        TitleColor: '#AC7C44'
        BgColor: '#F4E7BF'
        ImageUrl: >-
          https://epifi-icons.pointz.in/salaryprogram/warning-triangle.png
        Cta:
          Deeplink:
            Screen: SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN
      RegNotCompletedStatusTileInfo:
        IsVisible: false
        Title: Your salary has not arrived yet
        TitleColor: '#AC7C44'
        BgColor: '#F4E7BF'
        ImageUrl: >-
          https://epifi-icons.pointz.in/salaryprogram/warning-triangle.png
        Cta:
          Deeplink:
            Screen: SALARY_PROGRAM_INTRO_SCREEN
      BenefitsActivePromoBannerTileInfo:
        IsVisible: true
        Title: Claim your FREE health cover of up to ₹20L!
        TitleColor: '#383838'
        BgColor: '#C0B7E1'
        ImageUrl: >-
          https://epifi-icons.pointz.in/salaryprogram/health-insurance-benefit.png
        Tag:
          Tag: ''
          TextColor: '#6F62A4'
          BgColor: '#CDC6E8'
        Cta:
          Deeplink:
            Screen: SALARY_PROGRAM_BENEFIT_INFO_SCREEN
            SalaryBenefitInfoScreenOptions:
              BenefitId: '75c7221a-c14d-4204-bcea-d3d747afc7b6'

      RegCompletedBenefitsInActivePromoBannerTileInfo:
        IsVisible: true
        Title: Megha paid ₹0 on her medical bill
        TitleColor: '#383838'
        BgColor: '#EFC0C0'
        ImageUrl: 'https://epifi-icons.pointz.in/cards/insurance.png'
        Tag:
          Tag: ''
          TextColor: '#A73F4B'
          BgColor: '#FAD0D0'
        Cta:
          Deeplink:
            Screen: STORY_SCREEN
            StoryScreenOptions:
              StoryTitle: Use Fi as your main account?
              StoryUrl: 'https://stories.fi.money/stories/addmoney-1'
      RegNotCompletedPromoBannerTileInfo:
        IsVisible: true
        Title: Megha paid ₹0 on her medical bill
        TitleColor: '#383838'
        BgColor: '#C0B7E1'
        ImageUrl: 'https://epifi-icons.pointz.in/cards/insurance.png'
        Tag:
          Tag: ''
          TextColor: '#6F62A4'
          BgColor: '#CDC6E8'
        Cta:
          Deeplink:
            Screen: STORY_SCREEN
            StoryScreenOptions:
              StoryTitle: Use Fi as your main account?
              StoryUrl: 'https://stories.fi.money/stories/addmoney-1'

  EntryPointSectionInfoB2BTimeBound:
    Custom1:
      ActiveFrom: '2023-11-27T00:00:00+05:30'
      ActiveTill: '2023-11-30T06:59:59+05:30'
      IsVisible: true
      BenefitsActiveSummaryTileInfo:
        IsVisible: true
        HeaderTitle: "Get ₹30k in"
        HeaderTitleColor: "#929599"
        Title: |-
          Salary
          Benefits
        TitleColor: '#2A496F'
        BgColor: '#FFFFFF'
        ImageUrl: 'https://epifi-icons.pointz.in/salaryprogram/star.png'
        Cta:
          Text: View All
          TextColor: '#4F71AB'
          BgColor: "#FFFFFF"
          ImageUrl: >-
            https://epifi-icons.pointz.in/salaryprogram/right-chevron-indigo.png
          Deeplink:
            Screen: SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN
      RegCompletedBenefitsInActiveSummaryTileInfo:
        IsVisible: true
        HeaderTitle: "Get ₹30k in"
        HeaderTitleColor: "#929599"
        Title: |-
          Salary
          Benefits
        TitleColor: '#2A496F'
        BgColor: '#FFFFFF'
        ImageUrl: 'https://epifi-icons.pointz.in/salaryprogram/star.png'
        Cta:
          Text: View All
          TextColor: '#4F71AB'
          BgColor: "#FFFFFF"
          ImageUrl: >-
            https://epifi-icons.pointz.in/salaryprogram/right-chevron-indigo.png
          Deeplink:
            Screen: SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN
      RegNotCompletedSummaryTileInfo:
        IsVisible: true
        HeaderTitle: "Get ₹30k in"
        HeaderTitleColor: "#929599"
        Title: |-
          Salary
          Benefits
        TitleColor: '#2A496F'
        BgColor: '#FFFFFF'
        ImageUrl: 'https://epifi-icons.pointz.in/salaryprogram/star.png'
        Cta:
          Text: Upgrade Now
          TextColor: '#4F71AB'
          BgColor: "#FFFFFF"
          ImageUrl: >-
            https://epifi-icons.pointz.in/salaryprogram/right-chevron-indigo.png
          Deeplink:
            Screen: SALARY_PROGRAM_INTRO_SCREEN
      RegCompletedBenefitsInActiveStatusTileInfo:
        IsVisible: false
        Title: Your salary has not arrived yet
        TitleColor: '#AC7C44'
        BgColor: '#F4E7BF'
        ImageUrl: >-
          https://epifi-icons.pointz.in/salaryprogram/warning-triangle.png
        Cta:
          Deeplink:
            Screen: SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN
      RegNotCompletedStatusTileInfo:
        IsVisible: false
        Title: Your salary has not arrived yet
        TitleColor: '#AC7C44'
        BgColor: '#F4E7BF'
        ImageUrl: >-
          https://epifi-icons.pointz.in/salaryprogram/warning-triangle.png
        Cta:
          Deeplink:
            Screen: SALARY_PROGRAM_INTRO_SCREEN
      BenefitsActivePromoBannerTileInfo:
        IsVisible: true
        Title: Claim your FREE health cover of up to ₹2L!
        TitleColor: '#383838'
        BgColor: '#C0B7E1'
        ImageUrl: >-
          https://epifi-icons.pointz.in/salaryprogram/health-insurance-benefit.png
        Tag:
          Tag: ''
          TextColor: '#6F62A4'
          BgColor: '#CDC6E8'
        Cta:
          Deeplink:
            Screen: SALARY_PROGRAM_BENEFIT_INFO_SCREEN
            SalaryBenefitInfoScreenOptions:
              BenefitId: '********-2ad6-455f-952d-0d5477387e2c'

      RegCompletedBenefitsInActivePromoBannerTileInfo:
        IsVisible: true
        Title: Megha paid ₹0 on her medical bill
        TitleColor: '#383838'
        BgColor: '#EFC0C0'
        ImageUrl: 'https://epifi-icons.pointz.in/cards/insurance.png'
        Tag:
          Tag: ''
          TextColor: '#A73F4B'
          BgColor: '#FAD0D0'
        Cta:
          Deeplink:
            Screen: STORY_SCREEN
            StoryScreenOptions:
              StoryTitle: Use Fi as your main account?
              StoryUrl: 'https://stories.fi.money/stories/addmoney-1'
      RegNotCompletedPromoBannerTileInfo:
        IsVisible: true
        Title: Megha paid ₹0 on her medical bill
        TitleColor: '#383838'
        BgColor: '#C0B7E1'
        ImageUrl: 'https://epifi-icons.pointz.in/cards/insurance.png'
        Tag:
          Tag: ''
          TextColor: '#6F62A4'
          BgColor: '#CDC6E8'
        Cta:
          Deeplink:
            Screen: STORY_SCREEN
            StoryScreenOptions:
              StoryTitle: Use Fi as your main account?
              StoryUrl: 'https://stories.fi.money/stories/addmoney-1'

  SalaryProgramSpecialRewardCampaignEndDate: "2023-01-10T23:59:59+05:30"
  SalaryBenefitsLandingPageQuickLinksSection:
    IsVisible: true
    MinAndroidVersionSupportingQuickLinksSection: 100
    MinIOSVersionSupportingQuickLinksSection: 729
    QuickLinksTiles:
      Link1:
        Title: "Get a chequebook"
        TitleColor: "#333333"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/Quick_links/chequebook.png"
        BgColor: "#FAD0D0"
        CTA:
          IsVisible: true
          ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-pink.png"
          BgColor: "#EFC0C0"
          Deeplink:
            Screen: "STORY_SCREEN"
            StoryScreenOptions:
              StoryTitle: "Get a chequebook"
              StoryUrl: "https://stories.fi.money/stories/salary-chequebook-request"
              StoryId: "e24825d2-740b-4fce-9aba-6a1ecc47d0ef"
        TileRank: 1
      Link2:
        Title: "Download your bank statement"
        TitleColor: "#333333"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/Quick_links/statement.png"
        BgColor: "#DEEEF2"
        CTA:
          IsVisible: true
          ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-dark-blue.png"
          BgColor: "#C0DAE0"
          Deeplink:
            Screen: "STATEMENT_REQUEST_SCREEN"
        TileRank: 2
      Link3:
        Title: "Download your bank statement"
        TitleColor: "#333333"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/coins-home-card.png"
        BgColor: "#DEEEF2"
        CTA:
          IsVisible: true
          ImageUrl: "https://epifi-icons.pointz.in/referrals/chevron-right.png"
          BgColor: "#DEEEF2"
          Deeplink:
            Screen: "STATEMENT_REQUEST_SCREEN"
        TileRank: 3
      Link4:
        VisibleFromAndroidVersion: 100
        VisibleFromIosVersion: 100
        Title: "Download Cancelled cheque"
        TitleColor: "#333333"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/coins-home-card.png"
        BgColor: "#FAD0D0"
        CTA:
          IsVisible: true
          ImageUrl: "https://epifi-icons.pointz.in/referrals/chevron-right.png"
          BgColor: "#EFC0C0"
          Deeplink:
            Screen: "DOWNLOAD_DIGITAL_CANCELLED_CHEQUE"
            DownloadDigitalCancelledChequeScreenOptions:
              CtaText:
                FontColor: "#FFFFFF"
                PlainString: "Preview & Download"
                BgColor: '#00B899'
                StandardFontStyle: "BUTTON_M"
              Title:
                FontColor: "#333333"
                PlainString: "Download a digital cancelled cheque on your device"
                StandardFontStyle: "SUBTITLE_1"
              Description:
                FontColor: "#646464"
                PlainString: "Your cancelled cheque is now on your device and ready for you to share"
                StandardFontStyle: "BODY_S"
        TileRank: 4
  HelpSectionInfo:
    IsVisible: true
    MinAndroidVersionSupportingHelpSection: 185
    MinIOSVersionSupportingHelpSection: 749
  SalaryProgramFAQsCategoryId: "***********"
  SalaryReferralEmployersBlockedMap: [ ]
  B2BEmployersMap:
    9271ea6b-2118-4585-bce1-e4c65ba14a2d: "Epifi Tech"
    5a7ac603-69e9-4876-88db-0e288fbe8c49: "Epifi Tech"
  LandingPageTopSectionCommsInfoMap:
    referralb2cpromotions:
      Title: "Upto 50% of your salary in Fi-Coins"
      TitleColor: "#FFFFFF"
      Desc: "Multiply salary account benefits, when colleagues from your company join"
      DescColor: "#CED2D6"
      ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/fi-coins-salary-top-scn.png"
      IsEnabled: false
      MinAndroidAppVersionSupported: 1000
      MinIosAppVersionSupported: 763
      OnlyForB2BEmployers: false
      OnlyForB2CEmployers: true
      OnlyForSalaryProgramActiveUsers: true
      PrimaryCta:
        BgColor: "#383838"
        Deeplink:
          Screen: "SALARY_PROGRAM_REFERRALS_LANDING_SCREEN"
        IsVisible: true
        Text: "Find out how"
        TextColor: "#00B899"
    referralb2cpromotionsupdateapp:
      Title: "Upto 50% of your salary in Fi-Coins"
      TitleColor: "#FFFFFF"
      Desc: "Multiply salary account benefits, when colleagues from your company join"
      DescColor: "#CED2D6"
      ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/fi-coins-salary-top-scn.png"
      IsEnabled: false
      MaxAndroidAppVersionAllowed: 0
      MaxIosAppVersionAllowed: 762
      OnlyForB2BEmployers: false
      OnlyForB2CEmployers: true
      OnlyForSalaryProgramActiveUsers: true
      PrimaryCta:
        BgColor: "#383838"
        Deeplink:
          Screen: "UPDATE_APP_SCREEN"
        IsVisible: true
        Text: "Update app to learn more"
        TextColor: "#00B899"
  HealthInsuranceRewardOfferIdToPolicyConfigMap:
    75c7221a-c14d-4204-bcea-d3d747afc7b6:
      HealthInsurancePolicyFAQsDocS3Path: "healthinsurance/docs/healthInsuranceFAQs.pdf"
      HealthInsurancePolicyClaimProcessDocS3Path: "healthinsurance/docs/claimsProcessDoc.pdf"
      HealthInsuranceCashlessHospitalListDocS3Path: "healthinsurance/docs/cashlessHospitalsDoc.pdf"
      HealthInsuranceTncsDocS3Path: "healthinsurance/docs/healthInsuranceTncs.pdf"
      HealthInsurancePolicyType: "SUPER_TOP_UP_INSURANCE"
    ********-2ad6-455f-952d-0d5477387e2c:
      HealthInsurancePolicyFAQsDocS3Path: "healthinsurance/docs/healthInsuranceFAQs.pdf"
      HealthInsurancePolicyClaimProcessDocS3Path: "healthinsurance/docs/claimsProcessDoc.pdf"
      HealthInsuranceCashlessHospitalListDocS3Path: "healthinsurance/docs/cashlessHospitalsDoc.pdf"
      HealthInsuranceTncsDocS3Path: "healthinsurance/docs/healthInsuranceTncs.pdf"
      HealthInsurancePolicyType: "BASE_HEALTH_INSURANCE"
    opd-1:
      HealthInsurancePolicyType: "ONSURITY_OPD_WELLNESS_2A2C"
    opd-2:
      HealthInsurancePolicyType: "ONSURITY_OPD_WELLNESS_2A"
    opd-3:
      HealthInsurancePolicyType: "ONSURITY_OPD_WELLNESS_1A"
  EarlySalaryBenefitConfig:
    EarlySalaryBenefitRewardOfferId: "05613a9f-0015-4a44-9c35-b080f50f9318"
  SalaryAccountBenefitsSectionTopBannerInfo:
    Title: "Calculate the total annual value of your own salary benefits here"
    TitleFontColor: "#FFFFFF"
    BgColor: "#383838"
    LeftIconUrl: ""
    RightIconUrl: "https://epifi-icons.pointz.in/salaryprogram/right-arrow.png"
    IsVisible: true
  SalaryAccountVerificationStepperInfo:
    IsEnabled: true
    MinAndroidAppVersionSupported: 288
    MinIosAppVersionSupported: 1731
  MinAndroidAppVersionToSupportBenefitsCalculatorPage: 225
  MinIosAppVersionToSupportBenefitsCalculatorPage: 1202
  MinReqDurationSinceLastActivationForBenefitsExpiryComms: 5m # 5 minutes for testing
  MinAndroidAppVersionHandlingRpcResponseErrorView: 185
  MinAndroidAppVersionSupportingLandingScreenRedirection: 100
  MinIosAppVersionSupportingLandingScreenRedirection: 100
  MinAndroidAppVersionSupportingBenefitsSectionV1: 253
  MinIosAppVersionSupportingBenefitsSectionV1: 1483
  AaSalaryConfig:
    EnableAaSalaryAmountSetupVersionV2: false
    AaSalaryAmountSetupVersionV2:
      MinAndroidVersion: 372
      MinIOSVersion: 2360
    AaSalarySourceScreenHideConnectTitle:
      MinAndroidVersion: 372
      MinIOSVersion: 2360
    AaSalaryAmountSetupBackConfirmationPopup:
      Enable: true
      AppVersion:
        MinAndroidVersion: 1
        MinIOSVersion: 2604
  HealthInsuranceOnsurityPolicyFlowsConfig:
    DisableFeature: false
    MinAndroidVersion: 370
    MinIOSVersion: 2350
  DisableB2CSalaryEntryPointsFlag: true

Signup:
  ThemeBasedInfoAckScreen:
    MinAndroidVersion: 10
    MinIOSVersion: 10
  EnableKnowMoreAccountClosureFlowIOS:
    MinIOSVersion: 2000
    MinAndroidVersion: 1
    FallbackToEnableFeature: true
  BlockOnboardingDueToUnlinkedPANAndAadhaar: false

AttributionLinkParseConfigs:
  - "CONF-1":
      AcquisitionSource: "STUDENT_PROGRAM_VIT"
      IsEnabled: true
      SourceIdentification:
        CampaignName: "Students_VITVellore"
  - "CONF-2":
      AcquisitionSource: "SOURCE-2"
      IsEnabled: false
      SourceIdentification:
        CampaignName: "campaign-2"
  - "CONF-3":
      AcquisitionSource: "STUDENT_PROGRAM_VIT"
      IsEnabled: true
      SourceIdentification:
        DeepLinkSub1: "Students_VITVellore"
  - "CONF-4":
      AcquisitionSource: "STUDENT_PROGRAM_VIT"
      IsEnabled: true
      SourceIdentification:
        DeepLinkSub2: "Students_VITVellore"

IPInterceptorParams:
  EnableIPInterceptor: true

ConnectedAccount:
  DisableWealthOnboardingMinAndroidVersion: 147 #min version on android on which wonb is disabled to use connected account
  DisableWealthOnboardingMinIosVersion: 415 #min version on ios on which wonb is disabled to use connected account
  FinvuAccountDiscoveryTimeoutSeconds: 12
  OnemoneyAccountDiscoveryTimeoutSeconds: 12
  FinvuDisconnectUrl: "https://sherlock.qa.pointz.in/developer/actions" # need to run manual dev action to revoke
  HomeBannerCutoffDays: 10000
  IsConnectedAccountEnabled: true
  MinVersionCheckForNewBanks: 158
  HomeEntryPoint:
    Enabled: true
    Text: "Search across all your other account spends"
    LogoUrl: "https://epifi-icons.pointz.in/connectedaccounts/banks/home_entry_1.png"
  SearchEntryPoint:
    Enabled: true
    Text: "Connect Account"
    LogoUrl: "https://epifi-icons.pointz.in/home-v2/LinkIconV2.png"
  AnalyserEntryPoint:
    Enabled: true
    Text: "Connect\naccounts"
    LogoUrl: "https://epifi-icons.pointz.in/home-v2/LinkIconV2.png"
  ProfileEntryPoint:
    Enabled: true
    Text: "Track all your other accounts accurately & safely in one place"
    LogoUrl: "https://epifi-icons.pointz.in/connectedaccounts/banks/home_entry_1.png"
  AccountManagerEntryPoint:
    Enabled: true
    Text: "Connect more accounts"
    LogoUrl: "https://epifi-icons.pointz.in/connectedaccounts/banks/ca_entry_plus_circle_1.png"
  AllTransactionsEntryPoint:
    Enabled: true
    Text: "Connect Accounts"
    LogoUrl: "https://epifi-icons.pointz.in/home-v2/LinkIconV2.png"
  SearchBannerEntryPoint:
    Enabled: true
    Text: "Track all your other accounts accurately & safely in one place"
    LogoUrl: "https://epifi-icons.pointz.in/connectedaccounts/banks/home_entry_1.png"
  V2FlowParams:
    UseV2Flow: true
    AccountDiscoveryTitleText: "Select the accounts you want to link & track on Fi"
    AccountDiscoverySubtitleText: "We found these accounts linked to %s"
    AccountDiscoverySubtitleSearchingText: "One moment! We're searching for your accounts linked to %s."
    AccountDiscoveryCtaText: "Continue"
    MinVersionAndroid: 1
    MinVersionIos: 1
    AccountDiscoveryLoadingText: "Searching for your accounts"
    RegisterOtherAccountsText: "Can't see your accounts?"
  MinimumDurationRequiredToPermitDisconnect: "72h"
  FinvuAsyncDiscovery:
    AndroidMinVersion: 40000
    IosMinVersion: 40000
  EnableConsentRenewalSegmentNonProdTest: true
  EnableAccountManagerConsentRenewalEntryPoint: true

ConnectedAccountUserGroupParams:
  IsConnectedAccountRestricted: false
  AllowedUserGrps:
    - 1 # INTERNAL
    - 2 # FNF
    - 7 # CONNECTED_ACCOUNT

Tiering:
  NewTiersConstraints:
    AaSalary:
      IsEnableOnAndroid: true
      MinAndroidVersion: 350
      IsEnableOnIos: true
      MinIosVersion: 2271
  TieringFeature:
    MinVersionAndroid: 1
    MinVersionIos: 1
  HeroBenefits:
    MinVersionAndroid: 1
    MinVersionIos: 1
  TierIntroduction:
    ReleaseConstraints:
      MinVersionAndroid: 100000
      MinVersionIos: 100000
    LaunchAnimationInactivitySeconds: 0
  MaxNumberOfManualUpgradeRetries: 3
  OnbAddFundsSuccessVersionConstraints:
    IsEnableOnAndroid: true
    MinAndroidVersion: 1
    IsEnableOnIos: true
    MinIosVersion: 1
  IsTierAllPlansDropOffFeedbackFlowEnabled: false
  FailEarnedBenefitComponentsForActors:
    - "AC2210181j4r+aOFTdmDmdTFWm8aEQ=="
  EnableHeroV3Component: true
  HeroBenefitsV3Constraints:
    MinVersionAndroid: 403
    MinVersionIos: 2581
  TierAllPlansV2ConfigParams:
    ShouldShowEntryBanner: true
  TierDropOffBottomSheet:
    ShouldShowUSStocks: true
    ShouldShowFixedDeposit: false
    ShouldShowAddFunds: true

Fittt:
  MinAndroidVersionToSupportAggregationTileV2: 144
  MinIOSVersionToSupportAggregationTileV2: 1000
  MinAndroidVersionToSupportFeaturedRulesBanner: 1000
  MinIOSVersionToSupportFeaturedRulesBanner: 565
  AutoPayCollectionBannerAllowedUserGroups:
    - 1 # INTERNAL = user belongs to epiFi

ABFeatureReleaseConfig:
  FeatureConstraints:
    - FEATURE_US_FUNDS_IN_INVEST_LANDING_PAGE:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 100
            MinIOSVersion: 100
          StickyPercentageConstraintConfig:
            RolloutPercentage: 100
        Buckets:
          - ONE:
              Start: 0
              End: 100
    - SAVINGS_ACCOUNT_CLOSURE_PROFILE_SETTINGS_ENTRY_POINT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 311
          MinIOSVersion: 9999
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - ASK_FI_HOME_SEARCH_BAR:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 0
            MinIOSVersion: 0
        Buckets:
          - CONTROL:
              Start: 70
              End: 74
          - TYPE_1:
              Start: 75
              End: 79
          - TYPE_2:
              Start: 80
              End: 84
          - TYPE_3:
              Start: 85
              End: 89
    - HOME_SEARCH_BAR_CHIPS:
        Buckets:
          - TYPE_1:
              Start: 43
              End: 69
    - INVESTMENT_LANDING_COMPONENTS_ORDERING:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 100
            MinIOSVersion: 100
        Buckets:
          - CONTROL_1:
              Start: 10
              End: 13
    - INVESTMENT_LANDING_MUTUAL_FUNDS_DEEPLINK:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 100
            MinIOSVersion: 100
        Buckets:
          - INVESTMENT_LANDING_MUTUAL_FUNDS_INSTRUMENT_DEEPLINK_CONTROL_1:
              Start: 48
              End: 59
    - JUMP_DASHBOARD_FOR_NON_INVESTED_USERS:
        Buckets:
          - ZERO_STATE_DASHBOARD_VARIANT_ENABLED:
              Start: 16
              End: 26
    - FIXED_DEPOSIT_INTEREST_RATES:
        Buckets:
          - FIXED_DEPOSIT_INTEREST_RATES_EXPERIMENT_MIN_1_YEAR:
              Start: -1
              End: -1
    - FIXED_DEPOSIT_CUSTOM_TEMPLATE_DEFAULT_TERM:
        Buckets:
          - FIXED_DEPOSIT_CUSTOM_TEMPLATE_DEFAULT_TERM_EXPERIMENT_15_MONTHS:
              Start: -1
              End: -1
    - REFERRAL_SCREEN_DURING_ONBOARDING_V1:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 249
            MinIOSVersion: 1413
          UserGroupConstraintConfig:
            AllowedGroups:
              - 1 # INTERNAL
        Buckets:
          - ONE:
              Start: 0
              End: 99
    - UPDATED_BENEFITS_FOR_AFFLUENT_USER_ONB_ADD_FUNDS:
        ConstraintConfig:
          AppVersionConstraintConfig: # this should be the same as the app version for AFFLUENT_USER_BONUS_TRANSITION_SCREEN in user-qa.yml
            MinAndroidVersion: 99999
            MinIOSVersion: 99999
          UserGroupConstraintConfig:
            AllowedGroups:
              - 1 # INTERNAL
        Buckets: # this should be the same as the buckets for AFFLUENT_USER_BONUS_TRANSITION_SCREEN in user-qa.yml
          - ONE:
              Start: 0
              End: 0
    - APP_UPDATE_HARD_NUDGE_REFERRALS:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 1
            MinIOSVersion: 1
          UserGroupConstraintConfig:
            AllowedGroups:
              - 2 # FNF
        Buckets:
          - ONE:
              Start: 0
              End: 99
    - APP_UPDATE_SOFT_NUDGE_REFERRALS:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 1
            MinIOSVersion: 1
          UserGroupConstraintConfig:
            AllowedGroups:
              - 1 # INTERNAL
        Buckets:
          - ONE:
              Start: 0
              End: 99
    - REFERRALS_V1_LANDING_PAGE_STACKED_REWARDS_COMPONENT:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 1
            MinIOSVersion: 1
          UserGroupConstraintConfig:
            AllowedGroups:
              - 1 # INTERNAL
        Buckets:
          - ONE:
              Start: 0
              End: 99
    - PHONE_NUMBER_AS_REFERRAL_CODE:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 1
            MinIOSVersion: 1
          UserGroupConstraintConfig:
            AllowedGroups:
              - 1 # INTERNAL
              - 2 # FNF
        Buckets:
          - ONE:
              Start: 0
              End: 99

FeatureReleaseConfig:
  FeatureConstraints:
    - FEATURE_DISPUTE_COMPLAINT_SUMMARY_INPUT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 466
          MinIOSVersion: 642
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_WB_MAGIC_IMPORT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 469
          MinIOSVersion: 2953
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_DC_ORDER_SCREEN_WA_ADDRESS_UPDATE_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 407
          MinIOSVersion: 2595
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_CA_BANK_SELECTION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 375
          MinIOSVersion: 2491
    - FEATURE_NET_WORTH_INDIAN_STOCKS_DISTRIBUTION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 435
          MinIOSVersion: 2773
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_CC_DASHBOARD_BENEFITS_WIDGET_UNSECURED_CARD:
        AppVersionConstraintConfig:
          MinAndroidVersion: 20000
          MinIOSVersion: 2351
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - NSDL_PAN_API_V2_FOR_CA:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - TIERING_EARNED_BENEFITS_FROM_PROFILE_NOTCH:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100
          MinIOSVersion: 100
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - PRIME_SMS_PARSER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100000
          MinIOSVersion: 100000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - SALARY_LITE_PROGRAM:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - INVESTMENT_MF_UI:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
            - 2 # FNF
            - 9 # FIT_INVESTMENT
    - MF_ADVANCE_FILTER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 153
          MinIOSVersion: 470
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - MF_TEXT_SEARCH:
        AppVersionConstraintConfig:
          MinAndroidVersion: 163
          MinIOSVersion: 582
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - MF_SIP:
        AppVersionConstraintConfig:
          MinAndroidVersion: 163
          MinIOSVersion: 582
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - MF_NEW_OTI_PAYMENT_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100
          MinIOSVersion: 100
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - MERCHANT_ANALYSER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - TIME_ANALYSER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 196
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - JUMP_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 196
          MinIOSVersion: 801
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 12 # P2P_INVESTMENT_INTERNAL
    - CATEGORY_ANALYSER_ADD_FUNDS_BANNER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 194
          MinIOSVersion: 1000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - HOME_PAGE_LAYOUT_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 195
          MinIOSVersion: 300
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 16 # HOME_V2_INTERNAL
    - REFERRALS_V1_LANDING_PAGE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 213
          MinIOSVersion: 1077
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - REFERRAL_LINK_GENERATION_AT_CLIENT_SIDE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 233
          MinIOSVersion: 1319
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - CREDIT_SCORE_ANALYSER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - ANALYSER_FEEDBACK:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FI_MINUTE_HUB_BANNER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - US_STOCK_LANDING_PAGE_UI:
        # US_STOCK_LANDING_PAGE_UI feature controls visibility of landing page to the user to invest in us stocks
        # Pre-launch page is shown instead of landing for users not allowed for this feature
        AppVersionConstraintConfig:
          MinAndroidVersion: 201
          MinIOSVersion: 286
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - SALARY_PROGRAM_HEALTH_INSURANCE_BENEFIT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 227
          MinIOSVersion: 328
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - JUMP_RENEWAL_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 220
          MinIOSVersion: 314
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - ML_KIT_QR:
        AppVersionConstraintConfig:
          MinAndroidVersion: 228
          MinIOSVersion: 1000000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
            - 2 # FNF
    - ENABLE_GET_VKYC_NEXT_ACTION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 237
          MinIOSVersion: 1317
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - ENABLE_2FA_MF_ONE_TIME_BUY:
        AppVersionConstraintConfig:
          MinAndroidVersion: 0
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - ENABLE_2FA_MF_REGISTER_SIP:
        AppVersionConstraintConfig:
          MinAndroidVersion: 0
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - CATEGORY_ANALYSER_ACCOUNT_FILTER_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 230
          MinIOSVersion: 1000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - JUMP_DASHBOARD_FOR_NON_INVESTED_USERS:
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - TIME_ANALYSER_UPCOMING_TRANSACTIONS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 247
          MinIOSVersion: 343
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - ANALYSER_ZERO_STATES:
        AppVersionConstraintConfig:
          MinAndroidVersion: 252
          MinIOSVersion: 348
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - AA_CONSENT_RENEWAL:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups: [ ]
    - JUMP_MATURITY_CONSENT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 254
          MinIOSVersion: 1480
    - JUMP_ALL_ACTIVITY_DEEPLINK:
        AppVersionConstraintConfig:
          MinAndroidVersion: 10000
          MinIOSVersion: 1480
    - CA_CONNECT_FI_TO_FI:
        AppVersionConstraintConfig:
          MinAndroidVersion: 313
          MinIOSVersion: 2012
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
            - 7 # CONNECTED_ACCOUNT
    - AA_FINVU_TOKEN_AUTHENTICATION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - HEALTH_ENGINE_FOR_PAYMENTS:
        AppVersionConstraintConfig:
          MinAndroidVersion: ********
          MinIOSVersion: **********
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_JUMP_INVEST_PAGE_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 270
          MinIOSVersion: 1612
    - MF_HOLDINGS_IMPORT_PHONE_NUMBER_SCREEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 0
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_JUMP_NEW_LANDING_PAGE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 276
          MinIOSVersion: 1631
    - AA_CONSENT_RENEWAL_ALL_TXNS_PAGE_WIDGET:
        AppVersionConstraintConfig:
          MinAndroidVersion: 274
          MinIOSVersion: 1630
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - POST_PAYMENT_SCREEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 401
          MinIOSVersion: 2738
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_BIOMETRIC_REVALIDATION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 286
          MinIOSVersion: 1894
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - REFERRALS_V1_REFERRAL_HISTORY_EARNING_SUMMARY_INFO:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - LOANS_FI_LITE:
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - INVESTMENT_RETENTION_UI:
        AppVersionConstraintConfig:
          MinAndroidVersion: 282
          MinIOSVersion: 1689
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - PAY_VIA_PHONE_NUMBER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100000000
          MinIOSVersion: 1000000000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - MF_INVESTMENT_CALCULATOR_UI:
        AppVersionConstraintConfig:
          MinAndroidVersion: 289
          MinIOSVersion: 1838
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - MF_NAV_GRAPH_UI:
        AppVersionConstraintConfig:
          MinAndroidVersion: 289
          MinIOSVersion: 1838
    - CONNECTED_ACCOUNT_BENEFITS_SCREEN_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 300
          MinIOSVersion: 1995
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - NETWORTH_IND_SECURITIES_WIDGET:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
            - 7 # CONNECTED_ACCOUNT
    - AA_EQUITY_ACC_HOME_SUMMARY:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100000
          MinIOSVersion: 100000
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
            - 7 # CONNECTED_ACCOUNT
    - MF_HOLDINGS_IMPORT_V2_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 318
          MinIOSVersion: 1995
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_SECURED_LOANS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - ACTIVATE_BENEFICIARY_VIA_LIVENESS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 0
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - ONB_ADD_FUNDS_TIERING_SUCCESS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 312
          MinIOSVersion: 2024
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - SPEND_ANALYSER_INVEST_MORE_INSIGHT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 298
          MinIOSVersion: 1000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - SPEND_ANALYSER_INVEST_SURPLUS_BALANCE_INSIGHT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 298
          MinIOSVersion: 1000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - SPEND_ANALYSER_EARLY_SALARY_INSIGHT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 10000
          MinIOSVersion: 10000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - SPEND_ANALYSER_SET_REMINDER_INSIGHT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 298
          MinIOSVersion: 1000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - PAY_ASK_FI_SEARCH_SCREEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 10000
          MinIOSVersion: 10000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_CREDIT_CARD_TPAP_PAYMENTS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 406
          MinIOSVersion: 2583
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - AA_PERMITTED_FIP_CONFIG_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 7 # connected_account
    - ADD_FUNDS_V3:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - ADD_FUNDS_V4:
        AppVersionConstraintConfig:
          MinAndroidVersion: 319
          MinIOSVersion: 2037
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_CREDIT_CARD_RECOMMENDATION_FRAMEWORK:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100
          MinIOSVersion: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - DEPOSIT_AUTO_RENEW_CTA:
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_ENABLE_SIMPLIFI_CREDIT_CARD_ATM:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100
          MinIOSVersion: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - ALFRED_SAVINGS_ACC_SIGN_UPDATE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100000
          MinIOSVersion: 100000
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - INDIAN_SECURITIES_ETF:
        AppVersionConstraintConfig:
          MinAndroidVersion: 321
          MinIOSVersion: 459
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - INDIAN_SECURITIES_REIT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 321
          MinIOSVersion: 459
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - INDIAN_SECURITIES_INVIT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 321
          MinIOSVersion: 459
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - MANUAL_UAN_EPF_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 328
          MinIOSVersion: 473
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_DC_INTERNATIONAL_ATM_WITHDRAWAL_LAYOUT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 377
          MinIOSVersion: 2399
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_DC_TRAVEL_MODE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 387
          MinIOSVersion: 2450
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_DC_TOGGLE_TRAVEL_MODE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1000
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_DC_TRAVEL_MODE_LOTTIE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 412
          MinIOSVersion: 2608
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_REWARD_DETAILS_IN_ORDER_RECEIPT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 300
          MinIOSVersion: 300
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - NETWORTH_REFRESH_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 346
          MinIOSVersion: 501

    - FEATURE_MS_CLARITY_SDK_ENABLED:
        AppVersionConstraintConfig:
          MinAndroidVersion: 368
          MinIOSVersion: 9999
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups: [ ]
    - LOANS_IDFC_VKYC_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 328
          MinIOSVersion: 4
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL

    - NSDL_PAN_FLOW_V2_MF_ANALYSER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 343
          MinIOSVersion: 484
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_REWARD_DETAILS_IN_ALL_TRANSACTIONS_PAGE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 300
          MinIOSVersion: 300
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - MANUAL_ASSET_FORM_EMPLOYEE_STOCK_OPTION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 351
          MinIOSVersion: 506
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_ALFRED_USS_DOCUMENT_REQUEST:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100
          MinIOSVersion: 100
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - ANALYSER_HUB_FI_TO_FI_INTEGRATION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 364
          MinIOSVersion: 2331
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_OFF_APP_ENACH_CANCELLATION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 363
          MinIOSVersion: 2296
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - ASSET_LANDING_PAGE_FOR_MANUAL_ASSET:
        AppVersionConstraintConfig:
          MinAndroidVersion: 363
          MinIOSVersion: 2296
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_ADD_FUNDS_USS_DOUBLE_PIN_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 363
          MinIOSVersion: 2296
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_NET_WORTH_NPS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 390
          MinIOSVersion: 540
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_MONEY_SECRET_HOME_SCREEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 368
          MinIOSVersion: 2331
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
    - FEATURE_IN_APP_ISSUE_REPORTING_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 371
          MinIOSVersion: 2360
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
          RolloutPercentage: 100
    - FEATURE_HOME_SINGLE_RPC:
        AppVersionConstraintConfig:
          MinAndroidVersion: 393
          MinIOSVersion: 2492
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_HOME_DESIGN_ENHANCEMENTS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 0
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_SMS_PARSER_PARTNER_SDK:
        AppVersionConstraintConfig:
          MinAndroidVersion: 400
          MinIOSVersion: 9999
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 40 # SMS_PARSER_INTERNAL
    - FEATURE_CX_HELP_RECENT_ACTIVITY:
        AppVersionConstraintConfig:
          MinAndroidVersion: 377
          MinIOSVersion: 2387
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_PAY_RECEIPT_NEW_ERROR_VIEW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 377
          MinIOSVersion: 2391
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_PMS_PROVIDER_AND_AIF_SEARCH:
        AppVersionConstraintConfig:
          MinAndroidVersion: 378
          MinIOSVersion: 2399
    - FEATURE_US_STOCKS_SIP:
        AppVersionConstraintConfig:
          MinAndroidVersion: 386
          MinIOSVersion: 2478
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_ENABLE_PAYMENT_OPTIONS_V1:
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_IN_APP_ISSUE_REPORTING_NON_FCR_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 371
          MinIOSVersion: 2360
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_ISSUE_REPORTING_ASK_FI_INTEGRATION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentageIOS: 100
          RolloutPercentageAndroid: 100
    - FEATURE_NETWORTH_DASHBOARD_SECRET_SUMMARIES:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentageIOS: 100
          RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_HOME_BOTTOM_NAV_BAR_NETWORTH_SECTION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 389
          MinIOSVersion: 2458
        StickyPercentageConstraintConfig:
          RolloutPercentageIOS: 100
          RolloutPercentageAndroid: 100
    - PHYSICAL_DEBIT_CARD_CHARGES_PAYMENT_OPTIONS_SCREEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 388
          MinIOSVersion: 2462
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_PAY_SEARCH_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 420
          MinIOSVersion: 2738
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_EPF_GENERIC_ERROR_SCREEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 396
          MinIOSVersion: 547
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
    - FEATURE_US_STOCKS_LIMIT_ORDER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 403
          MinIOSVersion: 2570
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_MONEY_SECRET_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 401
          MinIOSVersion: 2537
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_SHOW_WEALTH_ANALYSER_WIDGET_HOME:
        AppVersionConstraintConfig:
          MinAndroidVersion: 401
          MinIOSVersion: 555
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_CONNECTED_ACCOUNTS_SKIP_PAN_DOB:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_WEALTH_BUILDER_PAN_COLLECTION_FORM:
        AppVersionConstraintConfig:
          MinAndroidVersion: 405
          MinIOSVersion: 2570
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_SHARE_POST_PAYMENT_SCREEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 404
          MinIOSVersion: 2570
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 21 # TPAP_INTERNAL
    - FEATURE_CONNECTED_ACCOUNT_UNIFIED_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 405
          MinIOSVersion: 2597
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_MONEY_SECRET_PEER_COMPARISON:
        AppVersionConstraintConfig:
          MinAndroidVersion: 401
          MinIOSVersion: 557
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - UPI_MAPPER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 230
          MinIOSVersion: 1030
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 28 # NEW_VPA_HANDLE
    - SELF_TRANSFER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 407
          MinIOSVersion: 2738
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_SALARY_REPORT_MONEY_SECRET:
        AppVersionConstraintConfig:
          MinAndroidVersion: 410
          MinIOSVersion: 2609
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_DC_MANDATES:
        AppVersionConstraintConfig:
          MinAndroidVersion: 419
          MinIOSVersion: 2662
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_MONEY_SECRET_FOOTER_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 416
          MinIOSVersion: 2625
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_HOME_NAV_BAR_WEALTH_BUILDER_SECTION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 423
          MinIOSVersion: 2672
        StickyPercentageConstraintConfig:
          RolloutPercentageIOS: 100
          RolloutPercentageAndroid: 100
    - FEATURE_ASSET_IMPORT_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 425
          MinIOSVersion: 589
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_ASSET_DASHBOARD_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 426
          MinIOSVersion: 589
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_MF_HOLDINGS_IMPORT_PAN_CONSENT_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 429
          MinIOSVersion: 2729
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_MF_HOLDINGS_IMPORT_DARK_THEME:
        AppVersionConstraintConfig:
          MinAndroidVersion: 427
          MinIOSVersion: 2714
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_WEALTH_BUILDER_NETWORTH_PAGE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 434
          MinIOSVersion: 2771
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_QR_SCAN_ENHANCEMENTS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 300
          MinIOSVersion: 300
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups: [ ]
    - FEATURE_LOANS_NEW_DATA_COLLECTION_SCREENS:
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        AppVersionConstraintConfig:
          MinAndroidVersion: 435
          MinIOSVersion: 2783
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_LOANS_PREQUAL_OFFER_FLOW:
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        AppVersionConstraintConfig:
          MinAndroidVersion: 100
          MinIOSVersion: 100
    - FEATURE_UPI_QUICK_LINK_BANNER_PAY_LANDING_SCREEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 230
          MinIOSVersion: 1000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_MONEY_PLANT_EARNED_BENEFITS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 435
          MinIOSVersion: 2780
    - FEATURE_PORTFOLIO_TRACKER_LANDING_SCREEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 435
          MinIOSVersion: 2773
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_SHOULD_SHOW_PIN_SCREEN_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 415
          MinIOSVersion: 2619
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_AMB_ENTRYPOINT_BANNER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_CX_NEW_LANDING_PAGE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 0
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_WB_DASHBOARD_LIABILITIES:
        AppVersionConstraintConfig:
          MinAndroidVersion: 456
          MinIOSVersion: 2876
    - FEATURE_REWARDS_CATALOG_MERGED_PAGE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 467
          MinIOSVersion: 2940
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_WEEKLY_PORTFOLIO_TRACKER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 435
          MinIOSVersion: 2773
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_PAY_LANDING_BANNER_RUPAY_CC_BE_DRIVEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 456
          MinIOSVersion: 619
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_FI_MCP_TOTP_CODE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100
          MinIOSVersion: 100
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_CREDIT_REPORT_MONEY_SECRET:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_TIERING_UPGRADE_PLAN_IN_EXPLORE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 465
          MinIOSVersion: 636
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_TIERING_PITCH_IN_PROFILE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 465
          MinIOSVersion: 636
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_NUGGET_CHATBOT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 468
          MinIOSVersion: 646
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_FI_COINS_TO_FI_POINTS_PRE_MIGRATION_PHASE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 467
          MinIOSVersion: 2930
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_FI_COINS_TO_FI_POINTS_POST_MIGRATION_PHASE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 467
          MinIOSVersion: 2930
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_FI_COINS_TO_FI_POINTS_DURING_MIGRATION_PHASE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1000000
          MinIOSVersion: 1000000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_MOENGAGE_INAPP_ENABLED:
        AppVersionConstraintConfig:
          MinAndroidVersion: 0
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups: [ ]
    - FEATURE_NET_WORTH_GOLD_DISTRIBUTION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 435
          MinIOSVersion: 2773
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL

USStocks:
  WithdrawFundsBlockedDuration: "1m"
  IsDoublePinAddFundFlowEnabled: true
  EnableWalletForAllUsers: true
  A2FormURL: "https://fi.money/assets/pages/a2-form/v2"
  ETF:
    IsEnabled: true
  MinAndroidAppVersionToSupportInlineErrDuringBuyFlow: 241
  MinIOSAppVersionToSupportInlineErrDuringBuyFlow: 337
  VersionSupport:
    MinAndroidAppVersionToSupportOnboardingPreRequisites: 100
    MinIOSAppVersionToSupportOnboardingPreRequisites: 100
    MinAndroidAppVersionToSupportVkycCheck: 318
    MinIOSAppVersionToSupportVkycCheck: 2036
    MinIOSAppVersionToSupportPanAadhaarLinkCheck: 2036
    MinAndroidAppVersionToSupportPanAadhaarLinkCheck: 318
    MinIOSAppVersionToSupportAnnouncementsInSymbolDetails: 2036
    MinAndroidAppVersionToSupportAnnouncementsInSymbolDetails: 318
    MinAndroidAppVersionToSupportFiLite: 100
    MinIOSAppVersionToSupportFiLite: 100
    MinAndroidAppVersionToSupportDropdownForCurrentlyInvestedInstruments: 325
    MinIOSAppVersionToSupportDropdownForCurrentlyInvestedInstruments: 2064
    MinAndroidAppVersionToSupportHiddenActivityTab: 10_000
  BuyTimeoutInMilliseconds: 60000
  PriceGraphURL: "https://web.qa.pointz.in/fin-charts/line-chart"
  RatioGraphURL: "https://web.qa.pointz.in/fin-charts/multi-chart"
  PriceGraphUpdatedAt: 1675794600 # Wednesday, 8 February 2023 12:00:00 AM GMT+05:30
  RatioGraphUpdatedAt: 1675794600 # Wednesday, 8 February 2023 12:00:00 AM GMT+05:30
  IsBuyDisabled: false
  IsSellDisabled: false

Tracing:
  Enable: true

Investment:
  MfNavChart:
    WebUrl: "https://web.qa.pointz.in/fin-charts/line-chart"
    LastUpdatedAt: 1675794600 # Wednesday, 8 February 2023 12:00:00 AM GMT+05:30
  MinAndroidVersionToSupportMINKYCCheckForPurchase: 137
  MinIOSVersionToSupportMINKYCCheckForPurchase: 169
  MinAndroidVersionToUploadPan: 137
  MinIosVersionToUploadPan: 169
  ISKYCCheckOnPurchaseEnabledForIOS: true
  ISKYCCheckOnPurchaseEnabledForAndroid: true
  FundActivityManualInterventionSupport:
    IsEnableOnAndroid: 1
    MinAndroidVersion: 166
    IsEnableOnIos: 1
    MinIosVersion: 582
  MinAndroidVersionForNextOnboardingStep: 228
  MinIOSVersionForNextOnboardingStep: 1268
  MFLandingPageCollectionIDForCuratedFunds: "MFCOLL11TM0etSbBQh6UBaCkZ50YBg=="
  MFLandingPageCollectionNameForCuratedFunds: "Create wealth"
  EnableAggregatorNotificationAlertFlag: true
  MinAndroidVersionToSupportGraph: 10000
  MinIOSVersionToSupportGraph: 285
  EnableOTIReminderPNFlag: true
  PromotionalContentUsecase: "JUMP_2"
  InvestmentLandingRecommendations:
    SegmentExpressionToRecommendationDetailsMap:
      - "TestSegmentID":
          RecommendationID: "HUNDRED_ONLY_COLLECTION"
          InstrumentType: 1 # 1 represents mutual fund
    FallBackRecommendationDetails:
      RecommendationID: "HYBRID_1"
      InstrumentType: 0 # 0 represents hybrid
    MFRecommendationIDToDetailsMap:
      - "HUNDRED_ONLY_COLLECTION":
          CollectionID: "MFCOLL11Wda9BHSQTM2gbsBkJ7SrPA=="
          HeaderDisplayString: "₹100 Only"
          SubtitleString: "Based on what people invest"
          HeaderImageUrl: "https://epifi-icons.pointz.in/investments/Cash100.png"
      - "POPULAR_IN_MF":
          FilterIDs:
            - "INDEX_FUND_TYPE"
            - "UPTO_POINT5_PER_EXPENSE"
            - "1001_TO_5000CR_FUND_SIZE"
            - "5001_TO_10KCR_FUND_SIZE"
            - "MORE_THAN_10KCR_FUND_SIZE"
          HeaderDisplayString: "Popular in Mutual funds"
          HeaderImageUrl: "https://epifi-icons.pointz.in/investments/Cash100.png"
          SubtitleString: "Based on what people invest"
    FallBackMFRecommendationDetails:
      CollectionID: "MFCOLL11Wda9BHSQTM2gbsBkJ7SrPA=="
      HeaderDisplayString: "₹100 Only"
      SubtitleString: "Based on what people invest"
      HeaderImageUrl: "https://epifi-icons.pointz.in/investments/Cash100.png"
    USStocksRecommendationIDToDetailsMap:
      - "BANKING_COLLECTION":
          CollectionID: "BANKING"
          HeaderDisplayString: "Banking"
          SubtitleString: "Based on what people invest"
          HeaderImageUrl: "https://epifi-icons.pointz.in/investments/Cash100.png"
    FallBackUSStocksRecommendationDetails:
      CollectionID: "BIG_TECH"
      HeaderDisplayString: "Big Tech"
      SubtitleString: "Based on what people invest"
      HeaderImageUrl: "https://epifi-icons.pointz.in/investments/Cash100.png"
    HybridRecommendationIDToDetailsMap:
      - "HYBRID_1":
          #          TitleString: "Popular on Fi"
          #          SubtitleString: "Based on what people invest"
          #          TitleImageUrl: "https://epifi-icons.pointz.in/investments/landing/recommendation_star.png"
          TitleString: "Mutual Funds collections"
          SubtitleString: "Save time with expert-picked funds"
          CTAForInstrumentType: 1
          TitleImageUrl: "https://epifi-icons.pointz.in/investments/investment_landing_collection.png"
          CollectionRecommendationCards:
            "MFCOLL11TM0etSbBQh6UBaCkZ50YBg==":
              InstrumentType: 1
              Rank: 1
              Title: "SIP with ₹500"
              Subtitle: "Invest regularly with these top funds"
              ImgUrl: "https://epifi-icons.pointz.in/investments/landing/mf_sip_500_collection_logo.png"
            "MFCOLL11q30wxq/uS0SO98y2Fqzr2w==":
              InstrumentType: 1
              Rank: 2
              Title: "Tax saver"
              Subtitle: "Funds to save tax under section 80C"
              ImgUrl: "https://epifi-icons.pointz.in/investments/Pad.png"
            "MFCOLL11yBS2aEbfSoOR6wzNnn3LFA==":
              InstrumentType: 1
              Rank: 3
              Title: "Better than FD"
              Subtitle: "Returns up to 7.8% at lower risk"
              ImgUrl: "https://epifi-icons.pointz.in/investments/Boxes-1.png"
            "MFCOLL221031aU9DeuaaQguyycGUtdBztw==":
              InstrumentType: 1
              Rank: 4
              Title: "Index funds"
              Subtitle: "Invest in top Indian companies"
              ImgUrl: "https://epifi-icons.pointz.in/investments/invest-landing-graph.png"
    #          MFRecommendationCards:
    #            - "1":
    #                Id: "MF220622XboJDGq7RHG9oR0ButrRyQ=="
    #                Tag:
    #                  FontColor: "#879EDB"
    #                  BgColor: "#D1DAF1"
    #                  Content: "MUTUAL FUNDS"
    #                Rank: 1
    #            - "2":
    #                Id: "MF220411EeRofPZwTIusYZUOko4eig=="
    #                Tag:
    #                  FontColor: "#879EDB"
    #                  BgColor: "#D1DAF1"
    #                  Content: "MUTUAL FUNDS"
    #                Rank: 5
    #          FixedDepositRecommendationCards:
    #            - "1":
    #                Id: "FD725"
    #                Tag:
    #                  FontColor: "#9287BD"
    #                  BgColor: "#CDC6E8"
    #                  Content: "FIXED DEPOSIT"
    #                Rank: 4
    #                Title: "Bumper jar - Lock safe returns"
    #                Info: "15 months | 7.25% p.a. "
    #          SmartDepositRecommendationCards:
    #            - "1":
    #                Id: "SD4"
    #                Tag:
    #                  FontColor: "#87BA6B"
    #                  BgColor: "#D9F2CC"
    #                  Content: "SMART DEPOSIT"
    #                Rank: 6
    #                Title: "Monthly autosave in a Smart deposit"
    #                Info: "₹100 only | 6.8% p.a."
    #          P2PRecommendationCards:
    #            - "SCHEME_NAME_LL_BOOSTER":
    #                Tag:
    #                  FontColor: "#D3B250"
    #                  BgColor: "#F4E7BF"
    #                  Content: "JUMP"
    #                Rank: 2
    #                Title: "Booster plan - Invest up to ₹10L"
    #                Info: "Limited period"
    #            - "SCHEME_NAME_LL_SHORT_TERM":
    #                Tag:
    #                  FontColor: "#879EDB"
    #                  BgColor: "#D1DAF1"
    #                  Content: "JUMP"
    #                Rank: 6
    #                Title: "Short term plan Start with ₹10,000"
    #                Info: "Join 10k+ investors"
    FallBackHybridRecommendationDetails:
      #      TitleString: "Popular in Fi"
      #      SubtitleString: "Based on what people invest"
      #      TitleImageUrl: "https://epifi-icons.pointz.in/investments/landing/recommendation_star.png"
      TitleString: "Mutual Funds collections"
      SubtitleString: "Save time with expert-picked funds"
      CTAForInstrumentType: 1
      TitleImageUrl: "https://epifi-icons.pointz.in/investments/investment_landing_collection.png"
      CollectionRecommendationCards:
        "MFCOLL11TM0etSbBQh6UBaCkZ50YBg==":
          InstrumentType: 1
          Rank: 1
          Title: "SIP with ₹500"
          Subtitle: "Invest regularly with these top funds"
          ImgUrl: "https://epifi-icons.pointz.in/investments/landing/mf_sip_500_collection_logo.png"
        "MFCOLL11q30wxq/uS0SO98y2Fqzr2w==":
          InstrumentType: 1
          Rank: 2
          Title: "Tax saver"
          Subtitle: "Funds to save tax under section 80C"
          ImgUrl: "https://epifi-icons.pointz.in/investments/Pad.png"
        "MFCOLL11yBS2aEbfSoOR6wzNnn3LFA==":
          InstrumentType: 1
          Rank: 3
          Title: "Better than FD"
          Subtitle: "Returns up to 7.8% at lower risk"
          ImgUrl: "https://epifi-icons.pointz.in/investments/Boxes-1.png"
        "MFCOLL221031aU9DeuaaQguyycGUtdBztw==":
          InstrumentType: 1
          Rank: 4
          Title: "Index funds"
          Subtitle: "Invest in top Indian companies"
          ImgUrl: "https://epifi-icons.pointz.in/investments/invest-landing-graph.png"
    #      TitleString: "Popular in Fi"
    #      SubtitleString: "Based on what people invest"
    #      TitleImageUrl: "https://epifi-icons.pointz.in/investments/landing/recommendation_star.png"
    #      P2PRecommendationCards:
    #        - "SCHEME_NAME_LL_FLEXI":
    #            Tag:
    #              FontColor: "#9287BD"
    #              BgColor: "#CDC6E8"
    #              Content: "JUMP"
    #            Rank: 1
    #        - "SCHEME_NAME_LL_SHORT_TERM":
    #            Tag:
    #              FontColor: "#879EDB"
    #              BgColor: "#D1DAF1"
    #              Content: "JUMP"
    #            Rank: 2
    #        - "SCHEME_NAME_LL_LONG_TERM":
    #            Tag:
    #              FontColor: "#7FBECE"
    #              BgColor: "#DEEEF2"
    #              Content: "JUMP"
    #            Rank: 3
    MinIosVersionForV2Recommendations: 341
    MinAndroidVersionForV2Recommendations: 249
    AndroidVersionForSubtitleSwapFix: 254
    EnableMultipleRecommendation: false
  EnableInvestmentDigestTileDedupe: true
  EnableHardLockinCalculator: true

Deposit:
  Preclosure:
    ConfirmationNudge:
      FaqCategoryId: "82000180883" # currently its save category, also category id is different for prod and non prod
  MaturityAmountVisibility: # deposit's maturity amount feature flags belong here
    Global: true # if false, maturity amount will be hidden everywhere irrespective of the screen
    GlobalAllowedUserGroups: [ ] # allowed user groups
    SDCreation: false  # if false, maturity amount will be hidden in SD creation flow
    FDCreation: false # if false, maturity amount will be hidden in FD creation flow
    SDDetails: false # if false, maturity amount will be hidden in SD details screen
    FDDetails: true # if false, maturity amount will be hidden in FD details screen
    SDAddFunds: false # if false, maturity amount will be hidden in SD add funds flow
  Goals:
    GoalDetailsInDepositList:
      Enable: true
    GoalDetailsInDepositDetails:
      Enable: true
  AutoSave:
    PostCreationFlow:
      Enable: false
      GlobalAllowedUserGroups: [ ] # allowed user groups
    DetailsFlow:
      Enable: true
      GlobalAllowedUserGroups: [ ] # allowed user groups
      EnableAutoSaveSuggestions: true
      EnableAutoSaveRuleList: true
    PreCreationFlow:
      Enable: true
      AllowedUserGroups: [ ] # allowed user groups
  TaxSaving:
    Enable: true
    MinAndroidVersion: 10
    MinIosVersion: 10
  Statement:
    Enable: true
    AllowedUserGroups: [ ] # allowed user groups
  Summary:
    MinIosVersionForAddMoneyBottomSheet: 1485
    MinAndroidVersionForAddMoneyBottomSheet: 10000

AnalyserParams:
  ShowAnalyser: true
  AnalyserConfigJson: "./mappingJson/analyserConfig.json"
  AddFundsBannerConf:
    EndOfMonthDays: 10
    StartOfMonthDays: 7
  AnalyserFeedbackParams:
    ExitAnalyserFeedbackCoolOff: "24h"
    OnScreenLensFeedbackCoolOffDuration: "24h"
    AcrossAnalyserFeedbackCoolOff: "24h"
    AnalyserFeedbackRateLimit:
      TimePeriod: "24h"
      MaxFeedbackAskLimit: 2
  AnalyserReleaseConfig:
    - ANALYSER_NAME_SPEND_TOP_CATEGORIES:
        ReleaseConfig:
          AndroidReleaseConfig:
            MinAppVersion: 100
            IsFeatureRestricted: false
          IOSReleaseConfig:
            MinAppVersion: 100
            IsFeatureRestricted: false
        LensReleaseConfigs:
          - LENS_NAME_TOP_BY_AMOUNT:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 100
                  IsFeatureRestricted: false
                IOSReleaseConfig:
                  MinAppVersion: 100
                  IsFeatureRestricted: false
          - LENS_NAME_TOP_SPENDS_BY_AMOUNT:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 247
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
                IOSReleaseConfig:
                  MinAppVersion: 328
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
          - LENS_NAME_TOP_INVESTMENTS_BY_AMOUNT:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 247
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
                IOSReleaseConfig:
                  MinAppVersion: 328
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
    - ANALYSER_NAME_SPEND_TOP_MERCHANTS:
        ReleaseConfig:
          AndroidReleaseConfig:
            MinAppVersion: 189
            IsFeatureRestricted: false
          IOSReleaseConfig:
            MinAppVersion: 275
            IsFeatureRestricted: false
        LensReleaseConfigs:
          - LENS_NAME_TOP_BY_AMOUNT:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 189
                  IsFeatureRestricted: false
                IOSReleaseConfig:
                  MinAppVersion: 275
                  IsFeatureRestricted: false
          - LENS_NAME_TOP_PEOPLE_BY_AMOUNT:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 247
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
                IOSReleaseConfig:
                  MinAppVersion: 328
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
          - LENS_NAME_TOP_MERCHANTS_BY_AMOUNT:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 247
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
                IOSReleaseConfig:
                  MinAppVersion: 328
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
    - ANALYSER_NAME_SPEND_TIME:
        ReleaseConfig:
          AndroidReleaseConfig:
            MinAppVersion: 198
            IsFeatureRestricted: false
            AllowedUserGroups:
              - "INTERNAL"
          IOSReleaseConfig:
            MinAppVersion: 1
            IsFeatureRestricted: false
            AllowedUserGroups:
              - "INTERNAL"
        LensReleaseConfigs:
          - LENS_NAME_INCREMENTAL_DISTRIBUTION:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 198
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
                IOSReleaseConfig:
                  MinAppVersion: 1
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
    - ANALYSER_NAME_CREDIT_SCORE:
        ReleaseConfig:
          AndroidReleaseConfig:
            MinAppVersion: 1
            IsFeatureRestricted: false
            AllowedUserGroups:
              - "INTERNAL"
          IOSReleaseConfig:
            MinAppVersion: 1
            IsFeatureRestricted: false
            AllowedUserGroups:
              - "INTERNAL"
        LensReleaseConfigs:
          - LENS_NAME_CREDIT_SCORE_SUMMARY:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 1
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
                IOSReleaseConfig:
                  MinAppVersion: 1
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
    - ANALYSER_NAME_INVESTMENTS_MF_GROWTH:
        ReleaseConfig:
          AndroidReleaseConfig:
            MinAppVersion: 252
            IsFeatureRestricted: false
            AllowedUserGroups:
              - "INTERNAL"
          IOSReleaseConfig:
            MinAppVersion: 1479
            IsFeatureRestricted: false
            AllowedUserGroups:
              - "INTERNAL"
        LensReleaseConfigs:
          - LENS_NAME_PORTFOLIO_GROWTH:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 252
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
                IOSReleaseConfig:
                  MinAppVersion: 1479
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
    - ANALYSER_NAME_INVESTMENTS_MF_TOP_FUNDS:
        ReleaseConfig:
          AndroidReleaseConfig:
            MinAppVersion: 252
            IsFeatureRestricted: false
            AllowedUserGroups:
              - "INTERNAL"
          IOSReleaseConfig:
            MinAppVersion: 1479
            IsFeatureRestricted: false
            AllowedUserGroups:
              - "INTERNAL"
        LensReleaseConfigs:
          - LENS_NAME_TOP_MUTUAL_FUNDS:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 252
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
                IOSReleaseConfig:
                  MinAppVersion: 1479
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
    - ANALYSER_NAME_INVESTMENTS_MF_ASSET_CLASS:
        ReleaseConfig:
          AndroidReleaseConfig:
            MinAppVersion: 252
            IsFeatureRestricted: false
            AllowedUserGroups:
              - "INTERNAL"
          IOSReleaseConfig:
            MinAppVersion: 1479
            IsFeatureRestricted: false
            AllowedUserGroups:
              - "INTERNAL"
        LensReleaseConfigs:
          - LENS_NAME_MUTUAL_FUNDS_ASSET_CLASS:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 252
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
                IOSReleaseConfig:
                  MinAppVersion: 1479
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
    - ANALYSER_NAME_INVESTMENTS_MF_EQUITY:
        ReleaseConfig:
          AndroidReleaseConfig:
            MinAppVersion: 215
            IsFeatureRestricted: false
            AllowedUserGroups:
              - "INTERNAL"
          IOSReleaseConfig:
            MinAppVersion: 306
            IsFeatureRestricted: false
            AllowedUserGroups:
              - "INTERNAL"
        LensReleaseConfigs:
          - LENS_NAME_MUTUAL_FUNDS_EQUITY_MARKET_CAP:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 215
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
                IOSReleaseConfig:
                  MinAppVersion: 306
                  IsFeatureRestricted: false
                  AllowedUserGroups:
                    - "INTERNAL"
  AnalyserLandingPageReleaseConfig:
    - ANALYSER_LANDING_PAGE_NAME_MUTUAL_FUND:
        AndroidReleaseConfig:
          MinAppVersion: 252
          IsFeatureRestricted: false
          AllowedUserGroups:
            - "INTERNAL"
        IOSReleaseConfig:
          MinAppVersion: 1479
          IsFeatureRestricted: false
          AllowedUserGroups:
            - "INTERNAL"
  AnalyserHubConfig:
    Experiments:
      - EXPERIMENT_NAME_CTA_BOTTOM_LEFT_WITHOUT_TEXT
  CreditReportParams:
    DownloadProcessExpiry: 30m
  CreditScoreAnalyserConfig:
    AutoRefreshCoolOffDurationInDays: 30
    AutoRefreshPollerTimeoutDuration: 5s
    ExperianV2InsightsConfig:
      IsEnabled: true
      ActiveFrom: "2024-04-01T00:00:00+05:30"
      ActiveTill: "2024-06-30T23:59:59+05:30"
  MfAnalyserRefreshBannerReleaseConfig:
    IsFeatureRestricted: true
    AllowedUserGroups:
      - "INTERNAL"

TxnCatUserGroupParams:
  IstxnCatRestricted: true
  AllowedGroups:
    - 1 # INTERNAL

P2PInvestment:
  Activity:
    AppVersionConstraintConfig:
      MinAndroidVersion: 160
      MinIOSVersion: 552
  RecentActivity:
    AppVersionConstraintConfig:
      MinAndroidVersion: 162
      MinIOSVersion: 836
  WithdrawalDowntime:
    IsEnable: true
    StartTime: "21:30"
    EndTime: "02:30"
  CloseJump:
    IsEnable: true
    StartDate: "2024-08-16"
    EndDate: "2025-11-10"
    Icon: "https://epifi-icons.pointz.in/p2pinvestment/blocker.png"
    Title: "Jump is unavailable for further investments"
    SubTitle: "We would be back soon, until then check our other wealth building products such as US Stocks and Mutual Funds"
  DeeplinkV2CompatibilityAndroidVersion: 254
  MinIosVersionForDynamicSlider: 1631
  MinIosVersionForPromotionLoadingScreen: 1630
  MinAndroidVersionForPromotionLoadingScreen: 272
  MinIosVersionForConsentCardV2: 1674
  MinAndroidVersionForConsentCardV2: 281
  DisableFlexiSchemeBanners: true

Goals:
  GoalDiscovery:
    Enable: true
    AllowedGroups: [ ] # allowed user groups
  GoalDiscoveryInExistingInvestmentInstrument:
    Enable: true
    AllowedGroups: [ ] # allowed user groups

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

QrDeeplinkParams:
  MinAndroidVersionForAmountScreenDeeplink: 431
  MinIosVersionForAmountScreenDeeplink: 261

SharedConfig:
  EnableV2SafetyNetFlow: true

AddFundsScreenParams:
  OnboardAddFunds:
    - DefaultAmount:
        CurrencyCode: "INR"
        Units: 25000
        Nanos: 0
      MinAmount:
        CurrencyCode: "INR"
        Units: 1
        Nanos: 0
      MaxAmount:
        CurrencyCode: "INR"
        Units: 100000
        Nanos: 0
      SuggestedAmounts:
        - Amount:
            CurrencyCode: "INR"
            Units: 5000
            Nanos: 0
        - Amount:
            CurrencyCode: "INR"
            Units: 10000
            Nanos: 0
          Tags:
            - Title: "Popular"
              IconUrl: "https://epifi-icons.pointz.in/transactions/addfunds/SuggestionPopularIcon.png"
        - Amount:
            CurrencyCode: "INR"
            Units: 25000
            Nanos: 0
        - Amount:
            CurrencyCode: "INR"
            Units: 50000
            Nanos: 0
      Remarks: "Add Funds to Federal Bank Savings Account"
      RewardsBanner:
        Description: "Win exciting rewards on adding funds in Fi account.T&C apply"
        IconUrl: ""
      IsBlocking: true
      IsEnabled: true

  TransferIn:
    - DefaultAmount:
        CurrencyCode: "INR"
        Units: 20000
        Nanos: 0
      MinAmount:
        CurrencyCode: "INR"
        Units: 1
        Nanos: 0
      MaxAmount:
        CurrencyCode: "INR"
        Units: 100000
        Nanos: 0
      SuggestedAmounts:
        - Amount:
            CurrencyCode: "INR"
            Units: 5000
            Nanos: 0
        - Amount:
            CurrencyCode: "INR"
            Units: 10000
            Nanos: 0
          Tags:
            - Title: "Popular"
              IconUrl: "https://epifi-icons.pointz.in/transactions/addfunds/SuggestionPopularIcon.png"
        - Amount:
            CurrencyCode: "INR"
            Units: 25000
            Nanos: 0
        - Amount:
            CurrencyCode: "INR"
            Units: 50000
            Nanos: 0
      Remarks: "Add Funds to Federal Bank Savings Account"
      RewardsBanner:
        Description: "Win exciting rewards on adding funds in Fi account. Check Way to earn rewards page for details."
        IconUrl: ""
      IsBlocking: false
      IsEnabled: true

  Default:
    - DefaultAmount:
        CurrencyCode: "INR"
        Units: 20000
        Nanos: 0
      MinAmount:
        CurrencyCode: "INR"
        Units: 1
        Nanos: 0
      MaxAmount:
        CurrencyCode: "INR"
        Units: 100000
        Nanos: 0
      SuggestedAmounts:
        - Amount:
            CurrencyCode: "INR"
            Units: 5000
            Nanos: 0
        - Amount:
            CurrencyCode: "INR"
            Units: 10000
            Nanos: 0
          Tags:
            - Title: "Popular"
              IconUrl: "https://epifi-icons.pointz.in/transactions/addfunds/SuggestionPopularIcon.png"
        - Amount:
            CurrencyCode: "INR"
            Units: 15000
            Nanos: 0
        - Amount:
            CurrencyCode: "INR"
            Units: 20000
            Nanos: 0
      Remarks: "Add Funds to Federal Bank Savings Account"
      RewardsBanner:
        Description: ""
        IconUrl: ""
      IsBlocking: false
      IsEnabled: true

# MerchantIdCapabilityMap contains merchant id for which we need to
# enable special capabilities on timeline like pay and request
MerchantIdTimelineCapabilityMap:
  "e162c098-17f3-448b-8057-f4b7eb219c5f":
    "TIMELINE_OPTION_PAY": true
    "TIMELINE_OPTION_REQUEST": true

QuestSdk:
  Disable: false

AutoInvestStoryConfig:
  StoryId: "ab2dc47f-9d61-4b53-a549-b44901853b24"

AddFundsParams:
  IsTierMovementDropOffFeedbackFlowEnabled: false
  AddFundsV3Params:
    ImageWithTextConstraints:
      IsEnableOnAndroid: true
      MinAndroidVersion: 314
      IsEnableOnIos: true
      MinIosVersion: 2024
    IntentThreshold: 6000
  IntentNavigateToPayStatusAndroidVersion: 430
  CollectNavigateToPayStatusAndroidVersion: 10000

AddFundsV2Params:
  WhitelistedActorIds: [ ]
  OnboardingAddFundsV2ScreenDetails:
    ShowManualAccountBalanceRefreshCta: true
    MinAmount: 100
    SalaryB2BSignupUrl: "https://web.qa.pointz.in/signup"

NonTpapPspHandles: [ "fede" ]

HomeRevampParams:
  NreSavingsDashboardReleaseConfig:
    IsEnableOnAndroid: true
    MinAndroidVersion: 377
    IsEnableOnIos: true
    MinIosVersion: 2399
  NroSavingsDashboardReleaseConfig:
    IsEnableOnAndroid: true
    MinAndroidVersion: 10000
    IsEnableOnIos: true
    MinIosVersion: 10000
  MutualfundDashboardCardReleaseConfig:
    IsEnableOnAndroid: true
    MinAndroidVersion: 418
    IsEnableOnIos: true
    MinIosVersion: 2635
  CreditScoreDashboardCardReleaseConfig:
    IsEnableOnAndroid: true
    MinAndroidVersion: 418
    IsEnableOnIos: true
    MinIosVersion: 2635
  EpfDashboardCardReleaseConfig:
    IsEnableOnAndroid: true
    MinAndroidVersion: 418
    IsEnableOnIos: true
    MinIosVersion: 2635
  DcInternationalWidgetReleaseConfig:
    IsEnableOnAndroid: true
    MinAndroidVersion: 418
    IsEnableOnIos: true
    MinIosVersion: 2776
  ShortcutIconTypeToDetailsMap:
    SHORTCUT_FI_STORE:
      Title: "Fi\nStore"
      ImageUrl: "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/fi-store-explore.png"
      Deeplink: '{"screen":"WEB_PAGE", "webPageScreenOptions":{"webpageTitle":"Fi Store", "webpageUrl":"https://fimoney.staging.dpanda.io/", "disableHardwareBackPress": true}}'
    SHORTCUT_FI_STORE_GIFT_CARDS:
      Title: "50%\nStore"
      ImageUrl: "https://epifi-icons.pointz.in/home-v2/gift-card-store-shortcut-icon.png"
      Deeplink: '{"screen":"WEB_PAGE", "webPageScreenOptions":{"webpageTitle":"Fi Store", "webpageUrl":"https://sandbox-fimoney.poshvine.com/gvms", "disableHardwareBackPress": true}}'
    SHORTCUT_FI_STORE_MILES_EXCHANGE:
      Title: "Travel\nMiles"
      ImageUrl: "https://epifi-icons.s3.ap-south-1.amazonaws.com/home-v2/ExploreIconsV2/miles-exchange-icon.png"
      Deeplink: '{"screen":"WEB_PAGE", "webPageScreenOptions":{"webpageTitle":"Fi Store", "webpageUrl":"https://sandbox-fimoney.poshvine.com/points-xchange/home", "disableHardwareBackPress": true}}'
  BgColorForBottomNavBarReleaseConfig:
    IsEnableOnAndroid: true
    MinAndroidVersion: 0
    IsEnableOnIos: true
    MinIosVersion: 2216
  DashboardVersionV2ReleaseConfig:
    IsEnableOnAndroid: true
    MinAndroidVersion: 292
    IsEnableOnIos: true
    MinIosVersion: 1856
  NewSearchUIReleaseConfig:
    IsEnableOnAndroid: true
    MinAndroidVersion: 292
    IsEnableOnIos: true
    MinIosVersion: 1856
  AllHomeIcons:
    "qr_code-69c444b6-cd1e-4b2d-8be3-7d9b2e2a25bb":
      IconWithVersionConstraints:
        - VersionConstraints:
            IsEnableOnAndroid: true
            MinAndroidVersion: 237
            IsEnableOnIos: true
            MinIosVersion: 1324
          IconType: "QR_CODE"
          ImageUrl: "https://epifi-icons.pointz.in/home-v2/QRCodeWhite.png"
          OnclickImageUrl: ""
          Title: "Scan & Pay"
          FontColour: "#FFFFFF"
          FontStyle: "BUTTON_S"
        # The default icon parameters do not require a version constraint check
        - IconType: "QR_CODE"
          ImageUrl: "https://epifi-icons.pointz.in/home-v2/QRCode.png"
          OnclickImageUrl: ""
          Title: ""
  HomeLayoutConfigurationV2Params:
    SectionScreenElementsConfig:
      BottomNavBarSection:
        ScreenElementsMapping:
          usstocks-1:
            IconWithVersionConstraints:
              VersionConstraints:
                IsEnableOnAndroid: true
                MinAndroidVersion: 424
                IsEnableOnIos: true
                MinIosVersion: 2672
  ToShowTieringInfoInSavingsDashboard: true
  HomeNudgeParams:
    TieringParams:
      ToUseV2States: false
    MinAndroidAppVersionToSupportGTMNudge: 249
    MinIOSAppVersionToSupportGTMNudge: 1424
    MinAndroidAppVersionToSupportV2StandardNudge: 254
    MinIOSAppVersionToSupportV2StandardNudge: 1504
  HomeLayoutV2Params:
    HomeElementAttributes:
      # This document contains the logic for the different layouts listed below. - https://docs.google.com/document/d/1DPhkfXSf-8COI4eNfUuXLMPsy_FdfcV5Qbv0TwQ0CIg/edit
      ElementIdToSegmentExpressionScores:
        "card-1":
          - Expression: "IsMember('AWS_test-cc-active')" # cc active
            Score: 0
          - Expression: "IsMember('AWS_test-loan-eligible')" # loans # 3 and 4 are for SG loans
            Score: 3
          - Expression: "IsMember('AWS_test-cc-active') && IsMember('AWS_test-loan-eligible')" # cc & loans  # 4,5 are SG loan segments
            Score: 0
          - Expression: "IsMember('AWS_test-cc-active-sa-frozen')" # cc active but sa account frozen
            Score: -1
        "wealth-builder-1":
          - Expression: "IsMember('AWS_test-cc-active')" # cc active
            Score: 3
          - Expression: "IsMember('AWS_test-loan-eligible')" # loans # 3 and 4 are for SG loans
            Score: 2
          - Expression: "IsMember('AWS_test-cc-active') && IsMember('AWS_test-loan-eligible')" # cc & loans  # 4,5 are SG loan segments
            Score: 1
          - Expression: "IsMember('AWS_test-cc-active-sa-frozen')" # cc active but sa account frozen
            Score: -1
        "discover-1":
          - Expression: "IsMember('AWS_test-cc-active')" # cc active
            Score: 1
          - Expression: "IsMember('AWS_test-loan-eligible')" # loans # 3 and 4 are for SG loans
            Score: 0
          - Expression: "IsMember('AWS_test-cc-active') && IsMember('AWS_test-loan-eligible')" # cc & loans  # 4,5 are SG loan segments
            Score: 0
          - Expression: "IsMember('AWS_test-cc-active-sa-frozen')" # cc active but sa account frozen
            Score: -1
        "card-2":
          - Expression: "IsMember('AWS_test-cc-active')" # cc active
            Score: 4
          - Expression: "IsMember('AWS_test-loan-eligible')" # loans # 3 and 4 are for SG loans
            Score: 0
          - Expression: "IsMember('AWS_test-cc-active') && IsMember('AWS_test-loan-eligible')" # cc & loans  # 4,5 are SG loan segments
            Score: 4
          - Expression: "IsMember('AWS_test-cc-active-sa-frozen')" # cc active but sa account frozen
            Score: -1
        "loans-2":
          - Expression: "IsMember('AWS_test-cc-active')" # cc active
            Score: 0
          - Expression: "IsMember('AWS_test-loan-eligible')" # loans # 3 and 4 are for SG loans
            Score: 1
          - Expression: "IsMember('AWS_test-cc-active') && IsMember('AWS_test-loan-eligible')" # cc & loans  # 4,5 are SG loan segments
            Score: 3
          - Expression: "IsMember('AWS_test-cc-active-sa-frozen')" # cc active but sa account frozen
            Score: -1
          - Expression: "!IsMember('AWS_test-loan-eligible')" # cc active but sa account frozen
            Score: -1
        "usstocks-1":
          - Expression: "IsMember('AWS_test-usstocks')" # for users not invested in any invest product or just invested in usstocks
            Score: 1
    SlotIdToScreenElementIdsMap:
      TopNavBarSlotSection:
        LeftSlots:
          slot_1: [ "profile-1" ]
        RightSlots:
          slot_1: [ "card-1", "wealth-builder-1", "discover-1" ]
          slot_2: [ "rewards-4" ]
          slot_3: [ "notification-1" ]
      DashboardSlotSection:
        slot_1: [ "intro-1" ]
        slot_2: [ "primarysavings-1" ]
        slot_3: [ "networth-1" ]
        slot_4: [ "invest-1" ]
        slot_5: [ "creditcards-1" ]
        slot_6: [ "loans-1" ]
      VerticalSlotSection:
        slot_1: [ "shortcuts-2" ]
        slot_2: [ "dc-international-widget" ]
        slot_3: [ "promotionalbanner-1" ]
        slot_4: [ "" ]
        slot_5: [ "suggestedforyou-1", "recentupcomingactivities-1", "primary-feature-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_6: [ "suggestedforyou-1", "recentupcomingactivities-1", "primary-feature-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_7: [ "suggestedforyou-1", "recentupcomingactivities-1", "primary-feature-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_8: [ "suggestedforyou-1", "recentupcomingactivities-1", "primary-feature-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_9: [ "suggestedforyou-1", "recentupcomingactivities-1", "primary-feature-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_10: [ "suggestedforyou-1", "recentupcomingactivities-1", "primary-feature-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_11: [ "suggestedforyou-1", "recentupcomingactivities-1", "primary-feature-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_12: [ "suggestedforyou-1", "recentupcomingactivities-1", "primary-feature-1", "rewards-2", "rewards-3", "secondary-feature-1", "tabbed-card-1" ]
        slot_13: [ "" ]
        slot_14: [ "" ]
        slot_15: [ "help-1" ]
        slot_16: [ "refer-2" ]
        slot_17: [ "trust-marker" ]
      BottomNavBarSlotSection:
        slot_1: [ "home-1" ]
        slot_2: [ "pay-1" ]
        slot_3: [ "invest-3", "usstocks-1" ]
        slot_4: [ "wealth-builder-1","card-2" ]
        slot_5: [ "wealth-builder-1", "discover-1", "loans-2" ]
      StickyIconSlotSection:
        slot_1: [ "qr_code-1" ]
        slot_2: [ "qr_code-2" ]
        slot_3: [ "" ]
  HomeLayoutV2D0To7Params:
    HomeElementAttributes:
      ElementIdToSegmentExpressionScores:
        "usstocks-1":
          - Expression: "IsMember('AWS_test-usstocks')" # for users not invested in any stocks and users invested only in US Stocks, usstocks-1 will be shown
            Score: 1
    SlotIdToScreenElementIdsMap:
      TopNavBarSlotSection:
        LeftSlots:
          slot_1: "profile-1"
        RightSlots:
          slot_1: "card-1"
          slot_2: "rewards-4"
          slot_3: "notification-1"
      DashboardSlotSection:
        slot_1: "intro-1"
        slot_2: "primarysavings-1"
        slot_3: "networth-1"
        slot_4: "invest-1"
        slot_5: "creditcards-1"
        slot_6: "loans-1"
      VerticalSlotSection:
        slot_1: "shortcuts-2"
        slot_2: "activation-widget"
        slot_3: "journeys"
        slot_4: "dc-international-widget"
        slot_5: "promotionalbanner-1"
        slot_6: "suggestedforyou-1"
        slot_7: "recentupcomingactivities-1"
        slot_8: "primary-feature-1"
        slot_9: ""
        slot_10: ""
        slot_11: "tabbed-card-1"
        slot_12: ""
        slot_13: "rewards-2"
        slot_14: "rewards-3"
        slot_15: "help-1"
        slot_16: "refer-2"
        slot_17: "trust-marker"
      BottomNavBarSlotSection:
        slot_1: "home-1"
        slot_2: "pay-1"
        slot_3: "invest-3"
        slot_4: "wealth-builder-1"
        slot_5: "discover-1"
      StickyIconSlotSection:
        slot_1: "qr_code-1"
        slot_2: ""
        slot_3: ""
  HomeLayoutV2D8To14Params:
    HomeElementAttributes:
      ElementIdToSegmentExpressionScores:
        "usstocks-1":
          - Expression: "IsMember('AWS_test-usstocks')" # for users not invested in any stocks and users invested only in US Stocks, usstocks-1 will be shown
            Score: 1
  HomeLayoutV2D15To28Params:
    HomeElementAttributes:
      ElementIdToSegmentExpressionScores:
        "usstocks-1":
          - Expression: "IsMember('AWS_test-usstocks')" # for users not invested in any stocks and users invested only in US Stocks, usstocks-1 will be shown
            Score: 1
  HomeLayoutV2WealthAnalyserParams:
    HomeElementAttributes:
      ElementIdToCrossAttachExpressionScores:
        "savings-account-benefits":
          - Expression: "IsCrossAttachIntent('PRODUCT_TYPE_SAVINGS_ACCOUNT')"
            Score: 5
        "loans-2":
          - Expression: "IsCrossAttachIntent('PRODUCT_TYPE_PERSONAL_LOANS')"
            Score: 5
  LayoutBySegFeatureRelease:
    IsEnableOnAndroid: true
    MinAndroidVersion: 250
    IsEnableOnIos: true
    MinIosVersion: 354
  HomeLayoutUIRevamp:
    IsEnableOnAndroid: true
    MinAndroidVersion: 264
    IsEnableOnIos: true
    MinIosVersion: 1561
  MaxDurationSinceOnbForNewAppWalkthrough: 10m

LiteHomeRevampParams:
  HomeLayoutUIRevamp:
    IsEnableOnAndroid: true
    MinAndroidVersion: 264
    IsEnableOnIos: true
    MinIosVersion: 1561
  DashboardVersionV2ReleaseConfig:
    IsEnableOnAndroid: true
    MinAndroidVersion: 292
    IsEnableOnIos: true
    MinIosVersion: 1856
  NewSearchUIReleaseConfig:
    IsEnableOnAndroid: true
    MinAndroidVersion: 292
    IsEnableOnIos: true
    MinIosVersion: 1856

Dispute:
  IsGetNextQuestionsForAppRequestChangeEnabled: true
  RaiseDisputeErrorViewConfig:
    IsBottomSheetErrorViewPropagationEnabled: true

EmailDomainCheck:
  EnableDomainCheck: false
  InvalidDomains: [ "privaterelay" ]
  InvalidDomainPopupConfig:
    MinIOSVersion: 400
    MinAndroidVersion: 10000
    FallbackToEnableFeature: true
    DisableFeature: false

Lending:
  PreApprovedLoan:
    ShowUnCollapsedOfferDetailsConfig:
      MinAndroidVersion: ********
      MinIOSVersion: ********
    IsAlternateAccountFlowEnabled: false
    LoanDetailsSelectionV2Flow:
      EnableLoanPrograms:
        - IDFC
        - FEDERAL_BANK
        - LOAN_PROGRAM_PRE_APPROVED_LOAN
        - LOAN_PROGRAM_FLDG
        - LOAN_PROGRAM_STPL
        - LOAN_PROGRAM_ACQ_TO_LEND
      DefaultAmountPercentage:
        - "LIQUILOANS": 0.75
        - "IDFC": 1
        - "FEDERAL": 0.75
    InstantCashSegmentId: "AWS_pal-test-qa-segment"
    DesiredLoanAmountAppVersionConstraintConfig:
      MinAndroidVersion: 431
      MinIOSVersion: 2736
    OfferDetailsV3Config:
      IsEnabled: true
      AppVersionConstraintConfig:
        MinAndroidVersion: 359
        MinIOSVersion: 506
      VendorLoanProgramMap:
        - "LIQUILOANS:LOAN_PROGRAM_FLDG": true
        - "LIQUILOANS:LOAN_PROGRAM_PRE_APPROVED_LOAN": true
        - "LIQUILOANS:LOAN_PROGRAM_STPL": true
        - "STOCK_GUARDIAN_LSP:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION": true
        - "FEDERAL_BANK:LOAN_PROGRAM_PRE_APPROVED_LOAN": true
        - "ABFL:LOAN_PROGRAM_PRE_APPROVED_LOAN": false
        - "ABFL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION": false
        - "FEDERAL_BANK:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION": true
        - "LENDEN:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION": true
        - "FEDERAL_BANK:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB": true
      SkipAmountSelectionScreen: true
      ShowInterestRate: true
      ShowZeroPreClosureTag: true
    OfferDetailsV4Config:
      IsEnabled: true
      AppVersionConstraintConfig:
        MinAndroidVersion: 426
        MinIOSVersion: 590
      VendorLoanProgramMap:
        - "STOCK_GUARDIAN_LSP:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
            IsEnabled: true
            AppVersionConstraintConfig:
              MinAndroidVersion: 446
              MinIOSVersion: 2839
        - "STOCK_GUARDIAN_LSP:LOAN_PROGRAM_EARLY_SALARY_V2":
            IsEnabled: true
            AppVersionConstraintConfig:
              MinAndroidVersion: 461
              MinIOSVersion: 2909
        - "FEDERAL_BANK:LOAN_PROGRAM_PRE_APPROVED_LOAN":
            IsEnabled: true
            AppVersionConstraintConfig:
              MinAndroidVersion: 446
              MinIOSVersion: 2839
        - "ABFL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
            IsEnabled: true
            AppVersionConstraintConfig:
              MinAndroidVersion: 446
              MinIOSVersion: 2839
        - "FEDERAL_BANK:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
            IsEnabled: true
            AppVersionConstraintConfig:
              MinAndroidVersion: 446
              MinIOSVersion: 2839
        - "LENDEN:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
            IsEnabled: true
        - "FEDERAL_BANK:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB":
            IsEnabled: true
            AppVersionConstraintConfig:
              MinAndroidVersion: 446
              MinIOSVersion: 2839
        - "MONEYVIEW:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
            IsEnabled: true
            AppVersionConstraintConfig:
              MinAndroidVersion: 446
              MinIOSVersion: 2839
        - "MONEYVIEW:LOAN_PROGRAM_PRE_APPROVED_LOAN":
            IsEnabled: true
            AppVersionConstraintConfig:
              MinAndroidVersion: 446
              MinIOSVersion: 2839
      SkipAmountSelectionScreen: true
      ShowInterestRate: true
      ShowZeroPreClosureTag: true
    AutoCancelCurrentLrConfig:
      MinAndroidVersion: 458
      MinIOSVersion: 2888
    ShowEligibilityInMultiOfferConfig:
      MinAndroidVersion: 458
      MinIOSVersion: 2888
    BadgeOnOfferIntroScreenConfig:
      MinAndroidVersion: 464
      MinIOSVersion: 2911
    RestrictShowingMultipleOffers: false
  SecuredLoanParams:
    ReleasedSegmentDetails:
      IsEnabled: false
      Expression: ""
    SecuredLoanExperiments:
      SkipInfoPage: false
      SkipLandingPage: false
      ChangeButtonTextLandingPage: false
      IsDeclarationCheckboxJourneyPage: false
    HomeNavBarSegmentExpression: "IsMember('AWS_lamfTestSegment')"
    DowntimeConfig:
      Start: "14-03-2024T19:00:00"
      End: "16-03-2024T23:59:59"
    EnablePreCloseFlow: true
  UseLandingDataV3:
    DisableFeature: false
    MinIOSVersion: 2952
    MinAndroidVersion: 468
    FallbackToEnableFeature: true
  EligibilityNuggetBotFeature:
    DisableFeature: false
    MinIOSVersion: 2954
    MinAndroidVersion: 470
    FallbackToEnableFeature: true
  ConcurrentLoansConfig:
    Enabled: true
    LoanTypeToMinFreezeDuration:
      LOAN_TYPE_PERSONAL: 1m
      LOAN_TYPE_EARLY_SALARY: 1m
      LOAN_TYPE_SECURED_LOAN: 0

Alfred:
  ServiceRequestHistoryPageSize: 2
  EnableVRH:
    MinAndroidVersion: 252
    MinIOSVersion: 1445
    FallbackToEnableFeature: false
    DisableFeature: false
  EnableCopyTrackingUrl:
    MinAndroidVersion: 294
    MinIOSVersion: 2000
    FallbackToEnableFeature: true
    DisableFeature: true
  EnableRequestChoiceBottomSheet:
    MinAndroidVersion: 292
    MinIOSVersion: 2000
    FallbackToEnableFeature: true
    DisableFeature: false
  EnableSavingsAccSignUpdate:
    MinAndroidVersion: 100000
    MinIOSVersion: 100000
    FallbackToEnableFeature: true
    DisableFeature: true

CreditCard:
  AppVersionSupport:
    MinIosVersionForCreditCard: 330
    MinAndroidVersionForCreditCard: 228
    MinIosVersionForCreditCardIntroV2: 349
    MinAndroidVersionForCreditCardIntroV2: 250
  OnboardingRetryAttemptCutoff: 10
  FiLiteOnboardingHomeScreenRedirectAttemptCutoff: 7
  FiLiteOnboardingBottomTextDisplayAttemptCutoff: 5
  EnableCCAllTxnPagination: false
  PaymentSuccessBannerTimeInMinutes: 5
  ShowCreditCardTabByDefaultFromCardTab: true
  WorkflowConstraints:
    - "CARD_REQUEST_WORKFLOW_CARD_ACTIVATION":
        AppVersionConstraintConfig:
          MinIOSVersion: 1529
    # to be used for secured card onboarding.
    - "CARD_REQUEST_WORKFLOW_CARD_ONBOARDING":
        AppVersionConstraintConfig:
          MinAndroidVersion: 276
          MinIOSVersion: 1709
  AllEligibleCcScreenConfig:
    CardComponentTemplateVersion: 1
  IsCcChoicesComponentListViewEnabled: false
  CcNetworkSelectionScreenVersionCheck:
    IsEnableOnAndroid: false
    MinAndroidVersion: 1000
    IsEnableOnIos: false
    MinIosVersion: 1000
  FiLiteBottomCtaConfigs:
    EnableFiLiteBottomCtaVersionCheckFlag:
      IsEnableOnAndroid: true
      MinAndroidVersion: 312
      IsEnableOnIos: true
      MinIosVersion: 2123
    IsCcChoicesComponentListViewEnabled: true
  EnableDashboardSegmentationCarousels: false
  SegmentIdToCarouselObjectMap:
    AWS_test-segment: # for actor ID ACGfLvx+oiTPuofDDFnAv0cw240408== (only in QA)
      InfoIcon: "https://epifi-icons.pointz.in/credit_card_images/welcome_offers_claim_img.png"
      InfoTitle: "This is a sample banner for testing"
      InfoDescription: "This is a sample banner for testing"
      DeeplinkScreenName: 489
  BillEraserOfferIDForOffersCatalogue: "f086f9f4-44a9-4be1-b92d-e93e9524967c"

PaymentOptionsConfig:
  IntentOptionsConfig:
    UpiAppsAndroidPackages:
      - PackageId: "com.phonepe.app"
        AppLogo: "https://epifi-icons.pointz.in/tiering/add_funds/phone_pe.png"
        AppName: "Phone Pe"
      - PackageId: "com.google.android.apps.nbu.paisa.user"
        AppLogo: "https://epifi-icons.pointz.in/tiering/add_funds/google_pay.png"
        AppName: "Google Pay"
      - PackageId: "net.one97.paytm"
        AppLogo: "https://epifi-icons.pointz.in/tiering/add_funds/pay_tm.png"
        AppName: "Paytm"
      - PackageId: "com.epifi.paisa.qa"
        AppLogo: "https://epifi-icons.pointz.in/M.fund+icon.png"
        AppName: "Fi QA"
      - PackageId: "in.org.npci.upiapp"
        AppLogo: "https://epifi-icons.pointz.in/M.fund+icon.png"
        AppName: "NPCI upi app"
      - PackageId: "com.idfcfirstbank.optimus"
        AppLogo: "https://epifi-icons.pointz.in/M.fund+icon.png"
        AppName: "IDFC"
      - PackageId: "com.axis.mobile"
        AppLogo: "https://epifi-icons.pointz.in/M.fund+icon.png"
        AppName: "AXIS"
      - PackageId: "com.flipkart.android"
        AppLogo: "https://epifi-icons.pointz.in/M.fund+icon.png"
        AppName: "Flipkart"
      - PackageId: "org.altruist.BajajExperia"
        AppLogo: "https://epifi-icons.pointz.in/M.fund+icon.png"
        AppName: "Bajaj"
    AllowedUserGroups:
      - 21 # TPAP_INTERNAL

FeedbackEngineConfig:
  ResponseHeaderPopulationConfig:
    IsPopulationInGetAnalyserEnabled: false
    IsPopulationInGetP2POrderStatusEnabled: true
    IsPopulationInCollectDataFromCustomerEnabled: true
    IsPopulationInGetNextOnboardingStepEnabled: true
    IsPopulationInEKYCForOnboardingForUserEnabled: true

NetworthConfig:
  ConfigPath: "./mappingJson/networthConfig.json"
  AppVersionsForWBDashboardV2:
    MinVersionAndroid: 464
    MinVersionIos: 2910
  AppVersionsForConnectMoreAssetsScreenV2:
    MinVersionAndroid: 470
    MinVersionIos: 2957

ShowAutoRenewCta: true

TpapUnifiedFlowReleaseVersions:
  MinAndroidVersion: 320
  MinIOSVersion: 2045

VpaMigrationScreenParams:
  OldVpaHandle: "fede"

OnAppEnachRedirectionDebug:
  ReturnRedirectionUrl: false
  AllowedActors:
    ALL: false # for allowing debugging for all actor-ids at once
    ACIlBiYsziR/aKxx4PvbyVIg240411==: true # Jithin

EnableGetPaymentOptionsV1: true

MsClarityConfig:
  IsEnabled: true
  AllowedScreenNames: [ ]
  AllowedActivityNames:
    - "com.epifi.paisa.home.HomeActivity"
    - "com.epifi.paisa.ui.MainActivity"
    - "com.epifi.paisa.accounttiering.TieringActivity" # Following Activity names are temporarily enabled until Android single activity flag is turned on for all of these
    - "com.epifi.paisa.analyser.AnalyserActivity"
    - "com.epifi.paisa.analyser.mutualfundimport.ImportMutualFundActivity"
    - "com.epifi.paisa.commonflows.chat.CustomerSupportCallUsActivity"
    - "com.epifi.paisa.commonflows.chat.CustomerSupportChatActivity"
    - "com.epifi.paisa.commonflows.feedback.feedbackvote.FeedbackVoteActivity"
    - "com.epifi.paisa.commonflows.feedbackengine.FeedbackEngineActivity"
    - "com.epifi.paisa.commonflows.generate_deposit_statement.GenerateDepositStatementActivity"
    - "com.epifi.paisa.commonflows.goals.GoalsActivity"
    - "com.epifi.paisa.commonflows.information_popup.InformationPopupActivity"
    - "com.epifi.paisa.commonflows.investment_retention.InvestmentRetentionActivity"
    - "com.epifi.paisa.help.HelpActivity"
    - "com.epifi.paisa.investments.InvestmentsActivity"
    - "com.epifi.paisa.nominee.NomineeActivity"
    - "com.epifi.paisa.onboarding.login.LoginActivity"
    - "com.epifi.paisa.onboarding.login.safetynet.SafetynetConsentActivity"
    - "com.epifi.paisa.pay.PayActivity"
    - "com.epifi.paisa.profile.ProfileActivity"
    - "com.epifi.paisa.ui.authorize.notification.CxUserAuthenticationActivity"
    - "com.epifi.paisa.ui.deeplink.DeeplinkActivity"
    - "com.epifi.paisa.ui.fullscreen.FullScreenNotificationActivity"
    - "com.epifi.paisa.ui.splash.SplashActivity"
    - "com.epifi.paisa.usstocks.UsStocksActivity"
    - "com.epifi.paisa.videokyc.OverlayActivity"
    - "com.epifi.paisa.videokyc.VideoKycActivity"
    - "com.epifi.paisa.wealth.WealthActivity"
    - "com.epifi.paisa.wealth.WealthOnboardingActivity"

UIEntryPointConfigForResolveQr:
  MinAndroidVersion: 344
  MinIOSVersion: 2244
  DisableFeature: false

HomeExploreConfig:
  EnableAskFiSection: true
  EnableFeedbackSection: true

SavingsAccountClosure:
  FullGroupCriteiaItemMinAppVersions:
    MinVersionAndroid: 10000
    MinVersionIos: 10000

Cx:
  CxHomeWidgetConfig:
    MinIosVersionForBrowseFaqDeprecation: 2617
  IsLLMSearchDropOffSurveyEnabled: true
  IsForceEnabledForNugget: true
  IsNuggetTransactionBotEnabled: false
  CxLandingPageV2Config:
    MaxNumberOfOpenIndividualTickets: 2
    MaxNumberOfTicketsToFetch: 50
    HelpSectionConfig:
      FAQDetails:
        - FAQId: ***********
          FAQType: "ARTICLE"
          Priority: 1
        - FAQId: ***********
          FAQType: "ARTICLE"
          Priority: 2
        - FAQId: ***********
          FAQType: "ARTICLE"
          Priority: 3
        - FAQId: ***********
          FAQType: "CATEGORY"
          Priority: 1
        - FAQId: ***********
          FAQType: "CATEGORY"
          Priority: 2
        - FAQId: ***********
          FAQType: "CATEGORY"
          Priority: 3

MoneySecrets:
  MfStocksBreakdown:
    MaxStocks: 50

MoneySecretsConfig:
  ExplicitLockingFeatureConfig:
    UnlockNudgeId: "84dfc46c-5ea1-4334-a47b-ec5b6ead6c96"
  CreditScoreSecretsConfig:
    CoolOffDurationForRnfInMinutes: "2m"

PayLandingScreenParams:
  IOSMinVersionForDesignFixit: 2811
  AndroidMinVersionForDesignFixit: 442

PostConversionMigrationDate:
  Day: 18
  Month: 7
  Year: 2025
