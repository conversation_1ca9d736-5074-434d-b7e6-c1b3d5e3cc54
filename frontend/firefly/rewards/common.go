package rewards

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"strconv"
	"time"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/casper"
	ffBeAccountsPb "github.com/epifi/gamma/api/firefly/accounting"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/firefly/helper"
	"github.com/epifi/gamma/pkg/accrual"
	ffPkg "github.com/epifi/gamma/pkg/firefly"
)

// getTransactionsForWeekendsInTimeRange retrieves credit card transactions that occurred on weekends within a specified time range.
// It organizes the transactions into a response where each entry corresponds to a specific weekend.
//
// Arguments:
// - ctx: The context for managing request-scoped values, deadlines, and cancellation signals.
// - actorId: The ID of the actor (user) for whom the transactions are to be fetched.
// - fromTime: The start of the time range for fetching transactions.
// - toTime: The end of the time range for fetching transactions.
// - fireflyAccountingClient: The client interface to communicate with the Firefly accounting service.
//
// Returns:
// - [][]*ffBeAccountsPb.CardTransaction: A slice of slices, where each inner slice contains transactions for a specific weekend.
// - error: An error if something goes wrong during the process.
func getTransactionsForWeekendsInTimeRange(ctx context.Context, actorId string, fromTime time.Time, toTime time.Time, fireflyAccountingClient ffBeAccountsPb.AccountingClient) ([][]*ffBeAccountsPb.CardTransaction, error) {
	// Get the weekends in the specified time range using a helper function
	weekends := ffPkg.GetWeekendsInRange(fromTime, toTime)
	// Initialize a slice to hold transactions for each weekend
	transactionsForWeekends := make([][]*ffBeAccountsPb.CardTransaction, len(weekends))

	// Loop through each weekend to fetch transactions
	for i, weekend := range weekends {
		// Create the request for fetching transactions for the current weekend
		req := &ffBeAccountsPb.GetTransactionsForATimeIntervalRequest{
			ActorId:  actorId,
			FromTime: timestamppb.New(weekend[0]),
			ToTime:   timestamppb.New(weekend[1]),
		}

		// Fetch transactions for the current weekend
		transactionsResp, err := fireflyAccountingClient.GetTransactionsForATimeInterval(ctx, req)
		switch {
		case err != nil:
			// Log the error and return it if it is not a "record not found" error
			logger.Error(ctx, "error in fetching transactions for the weekend", zap.Error(err))
			return nil, err
		case transactionsResp.GetStatus().IsRecordNotFound():
			// If no transactions are found for a weekend, continue to the next weekend
			continue
		default:
			// do nothing
		}
		// Assign the fetched transactions to the corresponding index in the slice
		transactionsForWeekends[i] = transactionsResp.GetTransactions()
	}

	// Return the grouped transactions for each weekend
	return transactionsForWeekends, nil
}

func getCommonRewardsAndOffersData(genConf *genconf.Config) []*helper.RewardsAndOffersData {
	return []*helper.RewardsAndOffersData{
		{
			ImageUrl: "https://epifi-icons.pointz.in/credit_card_images/rewards_dashboard_simplifi/gift_card.png",
			Title:    "Gift cards",
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_OFFERS_LANDING_SCREEN,
				ScreenOptions: &deeplink.Deeplink_OfferCatalogScreenOptions{
					OfferCatalogScreenOptions: &deeplink.OfferCatalogScreenOptions{
						TagFilters: []string{casper.CategoryTag_CATEGORY_TAG_VOUCHERS.String()},
					},
				},
			},
		},
		{
			ImageUrl: "https://epifi-icons.pointz.in/credit_card_images/rewards_dashboard_simplifi/merchandise.png",
			Title:    "Merchandise",
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_OFFERS_LANDING_SCREEN,
				ScreenOptions: &deeplink.Deeplink_OfferCatalogScreenOptions{
					OfferCatalogScreenOptions: &deeplink.OfferCatalogScreenOptions{
						TagFilters: []string{casper.CategoryTag_CATEGORY_TAG_SHOPPING.String()},
					},
				},
			},
		},
		{
			ImageUrl: "https://epifi-icons.pointz.in/credit_card_images/rewards_dashboard_simplifi/bill_eraser.png",
			Title:    "Bill eraser",
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_OFFERS_LANDING_SCREEN,
				ScreenOptions: &deeplink.Deeplink_OfferCatalogScreenOptions{
					OfferCatalogScreenOptions: &deeplink.OfferCatalogScreenOptions{
						DisplayFirstOfferIds: []string{genConf.CreditCard().BillEraserOfferIDForOffersCatalogue()},
					},
				},
			},
		},
	}
}

func getCommonRewardCoinSummaryData(rewardsSpendsWeekdayUnits int, rewardsSpendsWeekendUnits int, rewardCoinsAgainstSpend int32) *helper.RewardCoinSummaryData {
	var (
		infoIconDeeplink *deeplink.Deeplink
	)

	// Convert rewardCoinsAgainstSpend to migration-aware values
	rewardPointsInt := accrual.ConvertFiCoinsToFiPointsIfApplicable(int32(rewardCoinsAgainstSpend), false, nil)

	// Get migration-aware reward rate for display
	rewardRateDisplay := accrual.ReturnApplicableValue(rewardCoinsAgainstSpend, int(rewardPointsInt), nil, true).(int)
	if rewardsSpendsWeekendUnits+rewardsSpendsWeekdayUnits > 0 {
		infoIconDeeplink = &deeplink.Deeplink{ // nolint: dupl
			Screen: deeplink.Screen_INFORMATION_POPUP,
			ScreenOptions: &deeplink.Deeplink_InformationPopupOptions{
				InformationPopupOptions: &deeplink.InformationPopupOptions{
					InfoList: []*deeplink.InformationPopupOptions_Info{
						{
							TextTitle: commontypes.GetTextFromHtmlStringFontColourFontStyle("Rewards on other brands", colors.ColorNight, commontypes.FontStyle_SUBTITLE_L),
							BodyTexts: []*commontypes.Text{
								commontypes.GetTextFromHtmlStringFontColourFontStyle(fmt.Sprintf(accrual.ReplaceCoinWithPointIfApplicable("Get %d Fi-Coins for every ₹100 spent.", nil), rewardRateDisplay), colors.ColorSlate, commontypes.FontStyle_BODY_S),
								commontypes.GetTextFromHtmlStringFontColourFontStyle(accrual.ReplaceCoinWithPointIfApplicable("Fi-Coins get credited 10 days after the card bill generation date.", nil), colors.ColorSlate, commontypes.FontStyle_BODY_S),
								commontypes.GetTextFromHtmlStringFontColourFontStyle("Wallet reloads, cash advances, fees & charges or repayments and any other credit transactions are not eligible for any form of rewards.", colors.ColorSlate, commontypes.FontStyle_BODY_S),
							},
						},
					},
					Icon:    commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/credit_card_images/rewards_dashboard_simplifi/info_icon_green.png", 48, 48),
					BgColor: colors.ColorSnow,
				},
			},
		}
	}
	// Get migration-aware total earned
	totalEarned := accrual.ConvertFiCoinsToFiPointsIfApplicable(int32(rewardsSpendsWeekendUnits+rewardsSpendsWeekdayUnits), false, nil) // nolint: gosec

	return &helper.RewardCoinSummaryData{
		Title:        accrual.ReplaceCoinWithPointIfApplicable("TOTAL FI-COINS", nil),
		CoinsEarned:  strconv.Itoa(int(totalEarned)),
		InfoDeeplink: infoIconDeeplink,
	}
}
