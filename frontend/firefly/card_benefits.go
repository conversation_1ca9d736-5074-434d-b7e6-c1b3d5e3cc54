// nolint:funlen
package firefly

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	ffAccountsPb "github.com/epifi/gamma/api/firefly/accounting"
	"github.com/epifi/gamma/api/frontend/deeplink"
	ffPb "github.com/epifi/gamma/api/frontend/firefly"
	"github.com/epifi/gamma/api/frontend/header"
	ffScreenTypes "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/firefly"
	ffHelper "github.com/epifi/gamma/firefly/helper"
	accrualPkg "github.com/epifi/gamma/pkg/accrual"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

func (s *Service) GetCreditCardDetailsAndBenefits(ctx context.Context, req *ffPb.GetCreditCardDetailsAndBenefitsRequest) (*ffPb.GetCreditCardDetailsAndBenefitsResponse, error) {
	res := &ffPb.GetCreditCardDetailsAndBenefitsResponse{RespHeader: &header.ResponseHeader{}}

	limitAmountString := ""
	limitResp, err := s.fireflyAccountingClient.GetCreditAccountLimitUtilisation(ctx, &ffAccountsPb.GetCreditAccountLimitUtilisationRequest{GetBy: &ffAccountsPb.GetCreditAccountLimitUtilisationRequest_ActorId{
		ActorId: req.GetReq().GetAuth().GetActorId(),
	}})
	if te := epifigrpc.RPCError(limitResp, err); te != nil {
		logger.Error(ctx, "error while fetching limit utilization info from BE", zap.Error(te))
		// do not throw error; use fallback limit string
		limitAmountString = "Up to ₹5,00,000"
	} else {
		limitAmountString = money.ToDisplayStringInIndianFormat(limitResp.GetLimitActual(), 0, true)
	}

	screenOptions := &ffScreenTypes.CreditCardDetailsAndBenefitsScreenOptions{
		ScreenTitle:         "Credit card details",
		CardImageUrl:        "https://epifi-icons.pointz.in/credit_card_images/credit_card_with_coins.png",
		CreditLimitStr:      fmt.Sprintf("Your credit limit: <b>%s</b>", limitAmountString),
		Brands:              ffHelper.GetTopBrandsInfoItemWithCTAV2(ctx),
		ValuebackCheatsheet: ffHelper.GetValueBackCheatSheetInfoItemWithCTA(),
		WelcomeVouchers:     ffHelper.GetWelcomeVouchersInfoItemWithCTA(),
		StaticImages: []*deeplink.InfoItem{
			{
				Icon:  "https://epifi-icons.pointz.in/credit_card_images/redemption_options.png",
				Title: accrualPkg.ReplaceCoinWithPointIfApplicable("Don't just collect Fi-Coins; Redeem them", nil),
			},
			{
				Icon:  "https://epifi-icons.pointz.in/credit_card_images/milestone_rewards_benefits.png",
				Title: "Milestone rewards",
			},
			{
				Icon:  "https://epifi-icons.pointz.in/credit_card_images/card_additonal_offers_vertical.png",
				Title: "That’s not all!",
			},
		},
		BottomInfoItems: []*deeplink.InfoItemWithCtaV2{
			{
				Cta: &deeplink.Cta{
					Text: "All fees & charges",
					Deeplink: &deeplink.Deeplink{
						Screen: deeplink.Screen_WEB_PAGE,
						ScreenOptions: &deeplink.Deeplink_WebPageScreenOptions{
							WebPageScreenOptions: &deeplink.WebpageScreenOptions{
								WebpageUrl: "https://fi.money/credit-card/key-fact-statement",
							},
						},
					},
					Weblink: "https://fi.money/credit-card/key-fact-statement",
				},
			},
			{
				Cta: &deeplink.Cta{
					Text: accrualPkg.ReplaceCoinWithPointIfApplicable("Know more about Fi-Coins", nil),
					Deeplink: &deeplink.Deeplink{
						Screen: deeplink.Screen_STORY_SCREEN,
						ScreenOptions: &deeplink.Deeplink_StoryScreenOptions{
							StoryScreenOptions: &deeplink.StoryScreenOptions{
								StoryTitle: accrualPkg.ReplaceCoinWithPointIfApplicable("Know more about Fi-Coins", nil),
								StoryUrl:   "https://stories.fi.money/stories/fi-coins-intro",
								StoryId:    "e24825d2-740b-4fce-9aba-6a1ecc47d0ef",
							},
						},
					},
				},
			},
			{
				Cta: &deeplink.Cta{
					Text: "Frequently asked questions",
					Deeplink: &deeplink.Deeplink{
						Screen: deeplink.Screen_CARD_KNOW_MORE_SCREEN,
					},
				},
			},
		},
		Share: &deeplink.Cta{
			Text: "Share",
		},
		ShareCreditLimitText: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: "Your credit limit:\n<b>upto 10 Lakh</b>"},
		},
	}

	screenDeeplink, err := deeplinkv3.GetDeeplinkV3(deeplink.Screen_CREDIT_CARD_DETAILS_AND_BENEFITS_SCREEN, screenOptions)
	if err != nil {
		logger.Error(ctx, "error while getting v2 screen deeplink", zap.Error(err))
		res.RespHeader.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		res.RespHeader.ErrorView = defaultBottomSheetErrorView
		return res, nil
	}

	res.CreditCardDetailsAndBenefitsScreen = screenDeeplink
	res.RespHeader = &header.ResponseHeader{
		Status: rpc.StatusOk(),
	}
	return res, nil
}
