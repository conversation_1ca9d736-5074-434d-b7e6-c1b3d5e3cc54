// nolint: funlen,dupl,ineffassign
package firefly

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	types "github.com/epifi/gamma/api/typesv2"
	accrualPkg "github.com/epifi/gamma/pkg/accrual"
	cardPkg "github.com/epifi/gamma/pkg/card"

	"context"
	"fmt"
	"strconv"

	pkgErr "github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	anyPb "google.golang.org/protobuf/types/known/anypb"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	pkggenconf "github.com/epifi/be-common/pkg/frontend/app/genconf"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	ffBePb "github.com/epifi/gamma/api/firefly"
	ffAccPb "github.com/epifi/gamma/api/firefly/accounting"
	ffAccEnumsPb "github.com/epifi/gamma/api/firefly/accounting/enums"
	ffBeBillingEnums "github.com/epifi/gamma/api/firefly/billing/enums"
	ffEnumsBePb "github.com/epifi/gamma/api/firefly/enums"
	ffPinotPb "github.com/epifi/gamma/api/firefly/pinot"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/errors"
	ffPb "github.com/epifi/gamma/api/frontend/firefly"
	ffEnumsPb "github.com/epifi/gamma/api/frontend/firefly/enums"
	"github.com/epifi/gamma/api/frontend/header"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/typesv2/ui/card"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/behaviors"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/sections"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/firefly/helper"
	helper2 "github.com/epifi/gamma/frontend/firefly/helper"
	"github.com/epifi/gamma/frontend/firefly/internal"
	"github.com/epifi/gamma/frontend/firefly/rewards"
	fireflyPkg "github.com/epifi/gamma/pkg/firefly"
)

var (
	feRewardOfferTypeToBeRewardOfferType = map[ffEnumsPb.CCRewardOfferType]ffEnumsBePb.CCRewardOfferType{
		ffEnumsPb.CCRewardOfferType_CC_REWARD_OFFER_TYPE_UNSPECIFIED:                 ffEnumsBePb.CCRewardOfferType_CC_REWARD_OFFER_TYPE_UNSPECIFIED,
		ffEnumsPb.CCRewardOfferType_CC_REWARD_OFFER_TYPE_WELCOME:                     ffEnumsBePb.CCRewardOfferType_CC_REWARD_OFFER_TYPE_WELCOME,
		ffEnumsPb.CCRewardOfferType_CC_REWARD_OFFER_TYPE_TOP_MERCHANT_SPENDS_OFFER:   ffEnumsBePb.CCRewardOfferType_CC_REWARD_OFFER_TYPE_TOP_MERCHANT_SPENDS_OFFER,
		ffEnumsPb.CCRewardOfferType_CC_REWARD_OFFER_TYPE_CREDIT_CARD_SPENDS_1X_OFFER: ffEnumsBePb.CCRewardOfferType_CC_REWARD_OFFER_TYPE_CREDIT_CARD_SPENDS_1X_OFFER,
	}
	feRewardTypeToBeRewardType = map[ffEnumsPb.CCRewardType]ffEnumsBePb.CCRewardType{
		ffEnumsPb.CCRewardType_CC_REWARD_TYPE_UNSPECIFIED:   ffEnumsBePb.CCRewardType_CC_REWARD_TYPE_UNSPECIFIED,
		ffEnumsPb.CCRewardType_CC_REWARD_TYPE_FI_COINS:      ffEnumsBePb.CCRewardType_CC_REWARD_TYPE_FI_COINS,
		ffEnumsPb.CCRewardType_CC_REWARD_TYPE_GIFT_VOUCHERS: ffEnumsBePb.CCRewardType_CC_REWARD_TYPE_GIFT_VOUCHERS,
		ffEnumsPb.CCRewardType_CC_REWARD_TYPE_METAL_CARD:    ffEnumsBePb.CCRewardType_CC_REWARD_TYPE_METAL_CARD,
	}
)

const (
	snowColor     = "#FFFFFF"
	forestColor   = "#00B899"
	inkColor      = "#262728"
	onyxColor     = "#CED2D6"
	charcoalColor = "#383838"
	oceanColor    = "#2D5E6E"
	ocean100      = "#BCDCE7"
	darkOcean     = "#7FBECE"
	pastelOcean   = "#DEEEF2"
)

func (s *Service) GetRewardDisbursementStatus(ctx context.Context, req *ffPb.GetRewardDisbursementStatusRequest) (*ffPb.GetRewardDisbursementStatusResponse, error) {
	res := &ffPb.GetRewardDisbursementStatusResponse{RespHeader: &header.ResponseHeader{}}

	beRes, err := s.fireFlyClient.GetRewardDisbursementStatus(ctx, &ffBePb.GetRewardDisbursementStatusRequest{
		ActorId:         req.GetReq().GetAuth().GetActorId(),
		ClientRequestId: req.GetClientRequestId(),
		CardRequestId:   req.GetCardRequestId(),
		RewardOfferType: feRewardOfferTypeToBeRewardOfferType[req.GetRewardOfferType()],
	})
	switch {
	case !beRes.GetStatus().IsSuccess():
		logger.Error(ctx, fmt.Sprintf("error in GetRewardDisbursementStatus BE RPC : %v", beRes.GetStatus()))
		res.RespHeader.Status = beRes.GetStatus()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error in GetRewardDisbursementStatus BE rpc", zap.Error(err))
		res.RespHeader.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return res, nil
	default:
		res.RespHeader.Status = rpc.StatusOk()
		res.NextAction = beRes.GetNextAction()
		return res, nil
	}
}

// GetRewardDetails to get reward details for cc dashboard
func (s *Service) GetRewardDetails(ctx context.Context, req *ffPb.GetRewardDetailsRequest) (*ffPb.GetRewardDetailsResponse, error) {
	var (
		res                        = &ffPb.GetRewardDetailsResponse{}
		offersAndPromotionsSection *card.OffersAndPromotionsSection
		err                        error
		// cardDesignEnhancementEnabled Flag determines whether to use design fixit ui or fallback to old UI
		cardDesignEnhancementEnabled bool
		actorId                      = req.GetReq().GetAuth().GetActorId()
	)

	cardDesignEnhancementEnabled = cardPkg.DesignEnhancementEnabled(ctx, actorId, s.releaseEvaluator, s.onbClient, s.questSdkClient, s.userAttributeFetcher, s.networthClient, s.dynamicConf)

	responseWithStatus := func(status *rpc.Status, errorView *errors.ErrorView, offersAndPromotionsSection *card.OffersAndPromotionsSection) (*ffPb.GetRewardDetailsResponse, error) {
		res = &ffPb.GetRewardDetailsResponse{
			RespHeader: &header.ResponseHeader{
				Status:    status,
				ErrorView: errorView,
			},
			OffersAndPromotionsSection: offersAndPromotionsSection,
		}
		return res, nil
	}

	cardInfo, cardErr := s.fireFlyClient.GetCreditCard(ctx, &ffBePb.GetCreditCardRequest{
		GetBy: &ffBePb.GetCreditCardRequest_ActorId{ActorId: actorId},
	})
	if te := epifigrpc.RPCError(cardInfo, cardErr); te != nil {
		logger.Error(ctx, pkgErr.Wrap(te, "error in fetching card info").Error())
		return responseWithStatus(rpc.StatusInternal(), genericErrorView, nil)
	}

	accountInfo, accErr := s.fireflyAccountingClient.GetAccount(ctx, &ffAccPb.GetAccountRequest{
		GetBy: &ffAccPb.GetAccountRequest_AccountId{AccountId: cardInfo.GetCreditCard().GetAccountId()},
	})
	if te := epifigrpc.RPCError(accountInfo, accErr); te != nil {
		logger.Error(ctx, pkgErr.Wrap(te, "error in fetching card account info").Error())
		return responseWithStatus(rpc.StatusInternal(), genericErrorView, nil)
	}

	if s.checkFeatureReleaseConstraints(ctx, types.Feature_FEATURE_CC_OFFERS_WIDGET, actorId) {
		offersAndPromotionsSection, err = s.buildOffersAndPromotionsSection(ctx, accountInfo.GetAccount(), cardDesignEnhancementEnabled)
		if err != nil {
			logger.Error(ctx, pkgErr.Wrap(err, "error in building offers widget").Error())
			// Note: We intentionally return internal status codes instead of error-view responses on failure.
			//
			// Rationale:
			// This RPC is called during credit card dashboard load. If we return error-view responses
			// for offer widget building failures, the entire dashboard would display an error view bottom-sheet, which
			// creates a poor user experience for non-critical functionality.
			//
			// By returning internal status codes, we allow the client to gracefully handle failures for
			// non-critical dashboard components while still loading essential dashboard features.
			return responseWithStatus(rpc.StatusInternal(), nil, nil)
		}
	}

	if !pkggenconf.IsFeatureEnabledOnPlatform(ctx, s.dynamicConf.Flags().EnableGenericRewardsDashboard()) {
		return responseWithStatus(rpc.StatusOk(), nil, offersAndPromotionsSection)
	}

	startDate, endDate, startTimeStamp, endTimeStamp := fireflyPkg.GetBillingWindowFromTime(cardInfo.GetCreditCard().GetBasicInfo().GetBillGenDate(), datetime.TimestampToTime(timestampPb.Now()))

	provider, err := s.rewardsProvider.GetRewardsProvider(accountInfo.GetAccount().GetCardProgram())
	if err != nil {
		logger.Info(ctx, "error fetching rewards provider", zap.String(logger.CARD_PROGRAM, fireflyPkg.GetCardProgramStringFromCardProgram(accountInfo.GetAccount().GetCardProgram())),
			zap.Error(err))
		return responseWithStatus(rpc.StatusOk(), nil, offersAndPromotionsSection)
	}

	rewardsResponse, err := provider.GetDashboardRewardView(ctx, &rewards.GetDashboardRewardViewRequest{
		ActorId:                      actorId,
		CardProgram:                  accountInfo.GetAccount().GetCardProgram(),
		CreditCard:                   cardInfo.GetCreditCard(),
		StartTimestamp:               startTimeStamp,
		EndTimestamp:                 endTimeStamp,
		StartDate:                    startDate,
		EndDate:                      endDate,
		CardDesignEnhancementEnabled: cardDesignEnhancementEnabled,
	})
	if err != nil {
		logger.Info(ctx, "error fetching dashboard rewards view from provider", zap.String(logger.CARD_PROGRAM, fireflyPkg.GetCardProgramStringFromCardProgram(accountInfo.GetAccount().GetCardProgram())),
			zap.Error(err))
		return responseWithStatus(rpc.StatusOk(), nil, offersAndPromotionsSection)
	}

	// this can be due to rewards being switched off for a card cvp
	if rewardsResponse == nil {
		return responseWithStatus(rpc.StatusOk(), nil, offersAndPromotionsSection)
	}

	rewardsResponse.GetRewardDetailsResponse.OffersAndPromotionsSection = offersAndPromotionsSection
	return rewardsResponse.GetRewardDetailsResponse, nil
}

// getAggregateTxns fetches the txn aggregates for a cc user in a given time range
func (s *Service) getAggregateTxns(ctx context.Context, startTimeStamp, endTimeStamp *timestampPb.Timestamp, actorId, accountId string, excludedOntologyIds []string) (*ffPinotPb.GetTransactionAggregatesResponse, error) {
	return s.pinotClient.GetTransactionAggregates(ctx, &ffPinotPb.GetTransactionAggregatesRequest{
		ActorId:              actorId,
		CreditAccountId:      accountId,
		FromExecutedTime:     startTimeStamp,
		ToExecutedTime:       endTimeStamp,
		TransactionStatus:    []ffAccEnumsPb.TransactionStatus{ffAccEnumsPb.TransactionStatus_TRANSACTION_STATUS_SUCCESS},
		TransactionType:      ffAccEnumsPb.TransactionType_TRANSACTION_TYPE_DEBIT,
		ExcludeDsOntologyIds: excludedOntologyIds,
	})
}

// getRefundTxnAggregates fetches the refund txn aggregates for a cc user in a given time range
func (s *Service) getRefundTxnAggregates(ctx context.Context, startTimeStamp, endTimeStamp *timestampPb.Timestamp, actorId, accountId string, excludedOntologyIds []string) (*ffPinotPb.GetRefundTransactionAggregatesResponse, error) {
	return s.pinotClient.GetRefundTransactionAggregates(ctx, &ffPinotPb.GetRefundTransactionAggregatesRequest{
		ActorId:                    actorId,
		FromParentExecutedTime:     startTimeStamp,
		ToParentExecutedTime:       endTimeStamp,
		CreditAccountId:            accountId,
		ExcludeParentDsOntologyIds: excludedOntologyIds,
	})
}

func getBillingCycleInfo(startDate *date.Date, endDate *date.Date, cardProgram *types.CardProgram) *ffPb.BillingCycleInfo {
	billingCycleText := fmt.Sprintf(internal.BillingCycleLayout, startDate.GetDay(), getDaySuffix(int64(startDate.GetDay())), datetime.DateToString(startDate, "Jan", datetime.IST),
		endDate.GetDay(), getDaySuffix(int64(endDate.GetDay())), datetime.DateToString(endDate, "Jan", datetime.IST))
	rewardsConstructCta := getBillingCycleCtaByCardProgram(cardProgram)
	rewardsConstructText := "HOW IT WORKS"
	return &ffPb.BillingCycleInfo{
		Title: "Monthly Rewards",
		CurrentBillingCycleDetails: &deeplink.InfoItem{
			Icon:  internal.BillingCycleCalendarUrl,
			Title: billingCycleText,
		},
		BillingCycleCta: &deeplink.InfoItemWithCta{
			Info: &deeplink.InfoItem{
				Icon:  internal.RightArrowIconUrl,
				Title: rewardsConstructText,
			},
			Cta: rewardsConstructCta,
		},
		TitleText: &commontypes.Text{
			FontColor:    "#282828",
			DisplayValue: &commontypes.Text_PlainString{PlainString: "Monthly Rewards"},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
		},
		RewardsConstructCta: rewardsConstructCta,
		CurrentBillingCycleInfo: &commontypes.TextWithIcon{
			Text: &commontypes.Text{
				FontColor:    "#646464",
				DisplayValue: &commontypes.Text_PlainString{PlainString: billingCycleText},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_XS_CAPS},
			},
			VisualElement: commontypes.GetVisualElementImageFromUrl(internal.BillingCycleCalendarUrl).
				WithProperties(&commontypes.VisualElementProperties{
					Width:  20,
					Height: 20,
				}).WithImageType(commontypes.ImageType_PNG),
		},
		RewardsConstructInfo: &commontypes.TextWithIcon{
			Text: &commontypes.Text{
				FontColor:    "#646464",
				DisplayValue: &commontypes.Text_PlainString{PlainString: rewardsConstructText},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_XS_CAPS},
			},
			VisualElement: commontypes.GetVisualElementImageFromUrl(internal.RightArrowIconUrl).
				WithProperties(&commontypes.VisualElementProperties{
					Width:  16,
					Height: 16,
				}).WithImageType(commontypes.ImageType_PNG),
		},
	}
}

func getBillingCycleCtaByCardProgram(cardProgram *types.CardProgram) *deeplink.Cta {
	cardProgram = fireflyPkg.GetCardProgramWithFallback(cardProgram)
	switch {
	case cardProgram.GetCardProgramType() == types.CardProgramType_CARD_PROGRAM_TYPE_SECURED:
		return helper.GetSecuredCardsBillingCycleCta()
	default:
	}
	return &deeplink.Cta{
		Text: "HOW IT WORKS",
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_STORY_SCREEN,
			ScreenOptions: &deeplink.Deeplink_StoryScreenOptions{
				StoryScreenOptions: &deeplink.StoryScreenOptions{
					StoryTitle: "How it works",
					StoryUrl:   "https://stories.fi.money/stories/credit-card-rewards",
				},
			},
		},
	}
}

// nolint:funlen
func getExtraRewardsInfo(rewardSummary *rewardsPb.GetCreditCard1XRewardsSummaryResponse, currentSpends *moneyPb.Money, endDate *date.Date) *ffPb.ExtraRewardsInfo {
	total2xProjectedRewards, total5xProjectedRewards := fireflyPkg.GetTotalProjectedRewardCoins(rewardSummary.GetMerchantRewardAggregates(), currentSpends)
	total1xRewards := rewardSummary.GetProcessed_1XFiCoins() + rewardSummary.GetProcessing_1XFiCoins()
	expectedRewardsArrivalDate := datetime.TimeToDateInLoc(datetime.DateToTimeV2(endDate, datetime.IST).Add(fireflyPkg.MinReqDelayForRewardEvalRelativeToBillWindowEndDateInHours), datetime.IST)
	// For 2x rewards:
	dashboardRewardsInfo := make([]*ffPb.DashboardRewardsInfo, 0)
	dashboardRewardsInfo = append(dashboardRewardsInfo, &ffPb.DashboardRewardsInfo{
		Title:             "2x Rewards",
		ViewRewardInfoCta: getViewRewardsDetailInfoItemWithCta(expectedRewardsArrivalDate, int(total1xRewards), total2xProjectedRewards, total5xProjectedRewards, ffBeBillingEnums.MerchantRewardType_MERCHANT_REWARD_TYPE_TWO_X),
		TotalRewardCoins: &commontypes.TextWithIcon{
			Text: &commontypes.Text{
				FontColor:    "#8D8D8D",
				DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("+%d", total2xProjectedRewards)},
			},
			IconUrl: internal.FiProcessingRewardsIconUrl,
		},
		BgColor:          "#F7F9FA",
		TopMerchantsInfo: nil,
		BgShadowColor:    "#DDDFE1",
	})

	upgradeRewardInfo := &ffPb.UpgradeRewardTierInfo{}
	// 5x rewards only if we have crossed the spend limits for next reward tier.
	if moneyPkg.Compare(currentSpends, fireflyPkg.SpendLimitToUnlock5xRewards) >= 0 {
		dashboardRewardsInfo = append(dashboardRewardsInfo, &ffPb.DashboardRewardsInfo{
			Title:             "5x Rewards",
			ViewRewardInfoCta: getViewRewardsDetailInfoItemWithCta(expectedRewardsArrivalDate, int(total1xRewards), total2xProjectedRewards, total5xProjectedRewards, ffBeBillingEnums.MerchantRewardType_MERCHANT_REWARD_TYPE_FIVE_X),
			TotalRewardCoins: &commontypes.TextWithIcon{
				Text: &commontypes.Text{
					FontColor:    "#478295",
					DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("+%d", total5xProjectedRewards)},
					BgColor:      "#DEEEF2",
				},
				IconUrl: internal.FiProcessingRewardsIconUrl,
			},
			BgColor:          "#DEEEF2",
			TopMerchantsInfo: getTopMerchantsInfoFromRewardsSummary(rewardSummary),
			BottomText:       fmt.Sprintf("5x rewards will be credited on your top %d brands with the highest spends", fireflyPkg.CountOfMerchantsToBeRewarded5x),
			BgShadowColor:    "#C0DAE0",
		})
	} else {
		upgradeRewardInfo = &ffPb.UpgradeRewardTierInfo{
			RewardUpgradeInfo: &commontypes.TextWithIcon{
				Text: &commontypes.Text{
					FontColor:    "#478295",
					BgColor:      "#DEEEF2",
					DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("Upgrade to 5x rewards when you spend %s on your credit card", moneyPkg.ToDisplayString(fireflyPkg.SpendLimitToUnlock5xRewards))},
				},
				IconUrl: internal.UpgradeRewardToFiveXIconUrl,
			},
			ProgressBarColor: "#7FBECE",
			ProgressBarText: &commontypes.Text{
				FontColor:    "#478295",
				BgColor:      "#ECEEF0",
				DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("%s/%s", moneyPkg.ToDisplayString(currentSpends), moneyPkg.ToDisplayString(fireflyPkg.SpendLimitToUnlock5xRewards))},
			},
			ProgressPercentage: float32(currentSpends.GetUnits()) / float32(fireflyPkg.SpendLimitToUnlock5xRewards.GetUnits()) * 100.0,
			BottomText: &commontypes.Text{
				FontColor:    "#478295",
				BgColor:      "#F7F9FA",
				DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("5x rewards will be credited on your top %d brands with the highest spends", fireflyPkg.CountOfMerchantsToBeRewarded5x)},
			},
		}
	}

	return &ffPb.ExtraRewardsInfo{
		Title: fmt.Sprintf("EXTRA REWARDS <font color='#8D8D8D'>(ON %d BRANDS)</font>", fireflyPkg.EligibleMerchantCount),
		RewardsArrivalInfo: &commontypes.TextWithIcon{
			Text: &commontypes.Text{
				FontColor:    "#5D7D4C",
				BgColor:      "#D9F2CC",
				DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("Arriving %s", datetime.DateToString(expectedRewardsArrivalDate, "Jan 02", datetime.IST))},
			},
			IconUrl: internal.LoopIconUrl,
		},
		UpgradeRewardTierInfo: upgradeRewardInfo,
		DashboardRewardsInfos: dashboardRewardsInfo,
		EligibleMerchantCta:   helper.GetViewEligibleMerchantCta(),
	}
}

func getTopMerchantsInfoFromRewardsSummary(rewardSummary *rewardsPb.GetCreditCard1XRewardsSummaryResponse) *ffPb.TopMerchantsInfo {
	merchantAggregates := rewardSummary.GetMerchantRewardAggregates()
	return &ffPb.TopMerchantsInfo{
		Title: &commontypes.Text{
			FontColor:    "#478295",
			BgColor:      "#DEEEF2",
			DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("Your top %d brands", fireflyPkg.CountOfMerchantsToBeRewarded5x)},
		},
		MerchantInfos: getMerchantInfoAndRanking(merchantAggregates),
	}
}

func getMerchantInfoAndRanking(merchantAggregates []*rewardsPb.GetCreditCard1XRewardsSummaryResponse_MerchantRewardAggregate) []*ffPb.MerchantInfo {
	merchantInfos := make([]*ffPb.MerchantInfo, 0)
	for idx, merchantAggregate := range merchantAggregates {
		if idx == fireflyPkg.CountOfMerchantsToBeRewarded5x {
			break
		}
		switch {
		case merchantAggregate.GetNetFiCoins() != 0:
			merchantInfos = append(merchantInfos, &ffPb.MerchantInfo{
				MerchantIcon: fireflyPkg.GetMerchantLogoByMerchantName(merchantAggregate.GetMerchantName()),
				MerchantName: fireflyPkg.GetMerchantDisplayNameByMerchantName(merchantAggregate.GetMerchantName()),
				Rank:         int32(idx + 1),
			})
		default:
			// Returning a placeholder icon in case there are no reward coins awarded for the merchant
			merchantInfos = append(merchantInfos, &ffPb.MerchantInfo{
				MerchantIcon: internal.ShoppingBagIconDashboardUrl,
				Rank:         int32(idx + 1),
			})
		}
	}
	return merchantInfos
}

func getCurrentRewardsInfo(rewardSummary *rewardsPb.GetCreditCard1XRewardsSummaryResponse) *ffPb.CurrentRewardsInfo {
	return &ffPb.CurrentRewardsInfo{
		Title: "1X REWARDS <font color='#8D8D8D'>(ON ALL SPENDS)</font>",
		ProcessedRewards: &deeplink.InfoItem{
			Icon:  internal.FiRewardsIconUrl,
			Title: strconv.Itoa(int(rewardSummary.GetProcessed_1XFiCoins())),
		},
		ProcessingRewards: &deeplink.InfoItem{
			Icon:  internal.FiProcessingRewardsIconUrl,
			Title: strconv.Itoa(int(rewardSummary.GetProcessing_1XFiCoins())),
		},
		BottomText: "1x rewards give you 1 Fi-Coin for every ₹5 you spend",
	}
}

// nolint:funlen
// GetEligibleMerchantsAndRewards : rpc to get eligible merchants and rewards info in a ranking based fashion
func (s *Service) GetEligibleMerchantsAndRewards(ctx context.Context, req *ffPb.GetEligibleMerchantsAndRewardsRequest) (*ffPb.GetEligibleMerchantsAndRewardsResponse, error) {
	var (
		res = &ffPb.GetEligibleMerchantsAndRewardsResponse{}
	)

	responseWithStatus := func(status *rpc.Status, errorView *errors.ErrorView) (*ffPb.GetEligibleMerchantsAndRewardsResponse, error) {
		res.RespHeader = &header.ResponseHeader{
			Status:    status,
			ErrorView: errorView,
		}
		return res, nil
	}

	cardInfo, cardErr := s.fireFlyClient.GetCreditCard(ctx, &ffBePb.GetCreditCardRequest{
		GetBy: &ffBePb.GetCreditCardRequest_ActorId{ActorId: req.GetReq().GetAuth().GetActorId()},
	})
	if te := epifigrpc.RPCError(cardInfo, cardErr); te != nil {
		logger.Error(ctx, pkgErr.Wrap(te, "error in fetching card info").Error())
		return responseWithStatus(rpc.StatusInternal(), genericErrorView)
	}

	_, _, startTimeStamp, endTimeStamp := fireflyPkg.GetBillingWindowFromTime(cardInfo.GetCreditCard().GetBasicInfo().GetBillGenDate(), datetime.TimestampToTime(timestampPb.Now()))

	rewardSummary, rewardsErr := s.rewardsClient.GetCreditCard1XRewardsSummary(ctx, &rewardsPb.GetCreditCard1XRewardsSummaryRequest{
		CreditCardAccountId: cardInfo.GetCreditCard().GetAccountId(),
		TimeWindow: &rewardsPb.TimeWindow{
			FromTime: startTimeStamp,
			TillTime: endTimeStamp,
		},
	})
	if te := epifigrpc.RPCError(rewardSummary, rewardsErr); te != nil {
		logger.Error(ctx, pkgErr.Wrap(te, "error in fetching rewards info").Error())
		return responseWithStatus(rpc.StatusInternal(), genericErrorView)
	}

	topMerchantInfos, otherEligibleMerchantsInfo := getTopAndEligibleMerchantInfoAndRanking(rewardSummary.GetMerchantRewardAggregates())

	res.TopEligibleMerchantInfo = &ffPb.TopEligibleMerchantInfo{
		Title:         fmt.Sprintf("Your top %d brands", fireflyPkg.CountOfMerchantsToBeRewarded5x),
		MerchantInfos: topMerchantInfos,
		BottomText:    fmt.Sprintf("Top %d brands are finalised at the end of the billing cycle", fireflyPkg.CountOfMerchantsToBeRewarded5x),
	}
	res.OtherEligibleMerchantInfo = &ffPb.OtherEligibleMerchantInfo{
		Title:         "Other eligible brands",
		MerchantInfos: otherEligibleMerchantsInfo,
		ShowFullList: &deeplink.Cta{
			Type: deeplink.Cta_CUSTOM,
			Text: "Show full list",
		},
		HideFullList: &deeplink.Cta{
			Type: deeplink.Cta_CUSTOM,
			Text: "Hide full list",
		},
		PageSize: int32(fireflyPkg.OtherEligibleMerchantPageSize),
	}

	return responseWithStatus(rpc.StatusOk(), nil)
}

// getTopAndEligibleMerchantInfoAndRanking Is used to get two lists:
// top merchants: Top k brands on which 5x is rewarded
// Other eligible merchants: Other eligible merchants which are in contention to be top merchants
// nolint
func getTopAndEligibleMerchantInfoAndRanking(merchantAggregates []*rewardsPb.GetCreditCard1XRewardsSummaryResponse_MerchantRewardAggregate) ([]*ffPb.MerchantInfo, []*ffPb.MerchantInfo) {
	topMerchantInfos := make([]*ffPb.MerchantInfo, 0)
	otherEligibleMerchantInfos := make([]*ffPb.MerchantInfo, 0)
	otherEligibleMerchantBeginningIdx := -1
	for idx, merchantAggregate := range merchantAggregates {
		switch {
		// in case the merchant has a rank below the threshold of merchants to be rewarded 5x
		// and has some net reward coins, we add it to top merchant list
		case idx < fireflyPkg.CountOfMerchantsToBeRewarded5x && merchantAggregate.GetNetFiCoins() != 0:
			topMerchantInfos = append(topMerchantInfos, &ffPb.MerchantInfo{
				MerchantIcon: fireflyPkg.GetMerchantLogoByMerchantName(merchantAggregate.GetMerchantName()),
				MerchantName: fireflyPkg.GetMerchantDisplayNameByMerchantName(merchantAggregate.GetMerchantName()),
				Rank:         int32(idx + 1),
				RewardCoinDetails: &deeplink.InfoItem{
					Icon:  internal.FiCoinSuccessIcon,
					Desc:  strconv.Itoa(int(merchantAggregate.GetNetFiCoins())),
					Title: "EARNED",
				},
			})
		// since this list is sorted, once we encounter a merchant with zero fi coins,
		// all the subsequent merchants will also have zero fi coins. In such a case ,
		// we will fill a dummy icon on leader board.
		case idx < fireflyPkg.CountOfMerchantsToBeRewarded5x && merchantAggregate.GetNetFiCoins() == 0:
			if otherEligibleMerchantBeginningIdx == -1 {
				otherEligibleMerchantBeginningIdx = idx
			}
			topMerchantInfos = append(topMerchantInfos, &ffPb.MerchantInfo{
				MerchantIcon:  internal.ShoppingBagIconUrl,
				Rank:          int32(idx + 1),
				NoFiCoinsText: accrualPkg.ReplaceCoinWithPointIfApplicable("No Fi-Coins\nyet", nil),
			})
		default:
			if otherEligibleMerchantBeginningIdx == -1 {
				otherEligibleMerchantBeginningIdx = idx
			}
			break
		}
	}

	for idx, merchantAggregate := range merchantAggregates {
		// if the current rank of merchant is less than the merchant which appears on leaderboard,
		// we don't add it again in the list.
		if idx < otherEligibleMerchantBeginningIdx {
			continue
		}
		switch {
		case merchantAggregate.GetNetFiCoins() != 0:
			otherEligibleMerchantInfos = append(otherEligibleMerchantInfos, &ffPb.MerchantInfo{
				MerchantIcon: fireflyPkg.GetMerchantLogoByMerchantName(merchantAggregate.GetMerchantName()),
				MerchantName: fireflyPkg.GetMerchantDisplayNameByMerchantName(merchantAggregate.GetMerchantName()),
				Rank:         int32(idx + 1),
				RewardCoinDetails: &deeplink.InfoItem{
					Icon:  internal.FiCoinSuccessIcon,
					Desc:  strconv.Itoa(int(merchantAggregate.GetNetFiCoins())),
					Title: "EARNED",
				},
			})
		default:
			otherEligibleMerchantInfos = append(otherEligibleMerchantInfos, &ffPb.MerchantInfo{
				MerchantIcon:  fireflyPkg.GetMerchantLogoByMerchantName(merchantAggregate.GetMerchantName()),
				MerchantName:  fireflyPkg.GetMerchantDisplayNameByMerchantName(merchantAggregate.GetMerchantName()),
				Rank:          int32(idx + 1),
				NoFiCoinsText: accrualPkg.ReplaceCoinWithPointIfApplicable("<font color = '#8D8D8D'>NO FI-COINS YET</font>", nil),
			})
		}

	}
	return topMerchantInfos, otherEligibleMerchantInfos
}

// nolint:funlen
func getViewRewardsDetailInfoItemWithCta(expectedRewardsArrivalDate *date.Date, total1xRewards int, total2xProjectedRewards int, total5xProjectedRewards int, rewardType ffBeBillingEnums.MerchantRewardType) *deeplink.InfoItemWithCta {
	viewRewardsCta := &deeplink.InfoItemWithCta{
		Info: &deeplink.InfoItem{
			Icon:  internal.RightArrowIconUrl,
			Title: "VIEW DETAILS",
		},
		Cta: &deeplink.Cta{
			Type: deeplink.Cta_CUSTOM,
			Text: "VIEW DETAILS",
		},
	}

	// CTA IS DISABLED IF NO COINS ARE EARNED
	switch {
	case total2xProjectedRewards <= 0 && rewardType == ffBeBillingEnums.MerchantRewardType_MERCHANT_REWARD_TYPE_TWO_X:
		viewRewardsCta.Cta.Status = deeplink.Cta_CTA_STATUS_DISABLED
		return viewRewardsCta
	case total5xProjectedRewards <= 0 && rewardType == ffBeBillingEnums.MerchantRewardType_MERCHANT_REWARD_TYPE_FIVE_X:
		viewRewardsCta.Cta.Status = deeplink.Cta_CTA_STATUS_DISABLED
		return viewRewardsCta
	default:
	}

	switch rewardType {
	case ffBeBillingEnums.MerchantRewardType_MERCHANT_REWARD_TYPE_TWO_X:
		viewRewardsCta.Cta.Deeplink = &deeplink.Deeplink{
			Screen: deeplink.Screen_CREDIT_CARD_EXTRA_REWARDS_BOTTOM_VIEW_SCREEN,
			ScreenOptions: &deeplink.Deeplink_CardExtraRewardsBottomViewScreenOptions{
				CardExtraRewardsBottomViewScreenOptions: &deeplink.CreditCardExtraRewardsBottomViewScreenOptions{
					Title: fmt.Sprintf("2x Rewards(On %d brands)", fireflyPkg.EligibleMerchantCount),
					RewardsText: &commontypes.Text{
						FontColor:    "#4F71AB",
						BgColor:      "#FFFFFF",
						DisplayValue: &commontypes.Text_PlainString{PlainString: "2x Rewards"},
					},
					ProjectedRewardsCoins: &commontypes.TextWithIcon{
						Text: &commontypes.Text{
							FontColor:    "#8D8D8D",
							BgColor:      "#FFFFFF",
							DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("+%d", total2xProjectedRewards)},
						},
						IconUrl: internal.FiProcessingRewardsIconUrl,
					},
					RewardChartLegends: []*deeplink.RewardChartLegend{{
						RewardText:        "1x Earned",
						RewardLegendColor: "#879EDB",
						TotalCoins: &commontypes.TextWithIcon{
							Text: &commontypes.Text{
								FontColor:    "#646464",
								BgColor:      "#FFFFFF",
								DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("%d", total1xRewards)},
							},
							IconUrl: internal.FiRewardsIconUrl},
						RewardPercentage: float32(total1xRewards) / float32(total1xRewards+total2xProjectedRewards) * 100.0,
					},
						{
							RewardText:        "+1x Extra",
							RewardLegendColor: "#BBC8E9",
							TotalCoins: &commontypes.TextWithIcon{
								Text: &commontypes.Text{
									FontColor:    "#8D8D8D",
									BgColor:      "#FFFFFF",
									DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("+%d", total2xProjectedRewards)},
								},
								IconUrl: internal.FiProcessingRewardsIconUrl},
							RewardPercentage: float32(total2xProjectedRewards) / float32(total1xRewards+total2xProjectedRewards) * 100.0,
						}},
					BottomText: &commontypes.Text{
						FontColor:    "#4F71AB",
						BgColor:      "#FFFFFF",
						DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("1x extra rewards arriving %s", datetime.DateToString(expectedRewardsArrivalDate, "Jan 02", datetime.IST))},
					},
				}},
		}
	case ffBeBillingEnums.MerchantRewardType_MERCHANT_REWARD_TYPE_FIVE_X:
		viewRewardsCta.Cta.Deeplink = &deeplink.Deeplink{
			Screen: deeplink.Screen_CREDIT_CARD_EXTRA_REWARDS_BOTTOM_VIEW_SCREEN,
			ScreenOptions: &deeplink.Deeplink_CardExtraRewardsBottomViewScreenOptions{
				CardExtraRewardsBottomViewScreenOptions: &deeplink.CreditCardExtraRewardsBottomViewScreenOptions{
					Title: fmt.Sprintf("5x Rewards (On top %d brands)", fireflyPkg.CountOfMerchantsToBeRewarded5x),
					RewardsText: &commontypes.Text{
						FontColor:    "#478295",
						BgColor:      "#FFFFFF",
						DisplayValue: &commontypes.Text_PlainString{PlainString: "5x Rewards"},
					},
					ProjectedRewardsCoins: &commontypes.TextWithIcon{
						Text: &commontypes.Text{
							FontColor:    "#8D8D8D",
							BgColor:      "#FFFFFF",
							DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("+%d", total5xProjectedRewards)},
						},
						IconUrl: internal.FiProcessingRewardsIconUrl,
					},
					RewardChartLegends: []*deeplink.RewardChartLegend{
						{
							RewardText:        "1x Earned",
							RewardLegendColor: "#478295",
							TotalCoins: &commontypes.TextWithIcon{
								Text: &commontypes.Text{
									FontColor:    "#646464",
									BgColor:      "#FFFFFF",
									DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("%d", total1xRewards)},
								},
								IconUrl: internal.FiRewardsIconUrl},
							RewardPercentage: float32(total1xRewards) / float32(total1xRewards+total5xProjectedRewards+total2xProjectedRewards) * 100.0,
						},
						{
							RewardText:        "+1x Extra",
							RewardLegendColor: "#7FBECE",
							TotalCoins: &commontypes.TextWithIcon{
								Text: &commontypes.Text{
									FontColor:    "#8D8D8D",
									BgColor:      "#FFFFFF",
									DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("+%d", total2xProjectedRewards)},
								},
								IconUrl: internal.FiProcessingRewardsIconUrl},
							RewardPercentage: float32(total2xProjectedRewards) / float32(total1xRewards+total5xProjectedRewards+total2xProjectedRewards) * 100.0,
						},
						{
							RewardText:        "+3x Extra",
							RewardLegendColor: "#C0DAE0",
							TotalCoins: &commontypes.TextWithIcon{
								Text: &commontypes.Text{
									FontColor:    "#8D8D8D",
									BgColor:      "#FFFFFF",
									DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("+%d", total5xProjectedRewards)},
								},
								IconUrl: internal.FiProcessingRewardsIconUrl},
							RewardPercentage: float32(total5xProjectedRewards) / float32(total1xRewards+total5xProjectedRewards+total2xProjectedRewards) * 100.0,
						},
					},
					BottomText: &commontypes.Text{
						FontColor:    "#478295",
						BgColor:      "#FFFFFF",
						DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("Extra rewards arriving %s", datetime.DateToString(expectedRewardsArrivalDate, "Jan 02", datetime.IST))},
					},
				}},
		}
	default:
		viewRewardsCta.Cta.Status = deeplink.Cta_CTA_STATUS_DISABLED
	}
	return viewRewardsCta
}

func (s *Service) RecordRewardSelectionInfo(ctx context.Context, req *ffPb.RecordRewardSelectionInfoRequest) (*ffPb.RecordRewardSelectionInfoResponse, error) {
	res := &ffPb.RecordRewardSelectionInfoResponse{RespHeader: &header.ResponseHeader{}}
	beRes, err := s.fireFlyClient.RecordRewardSelectionInfo(ctx, &ffBePb.RecordRewardSelectionInfoRequest{
		RewardId:       req.GetRewardId(),
		RewardOptionId: req.GetRewardOptionId(),
		CardRequestId:  req.GetCardRequestId(),
		RewardType:     feRewardTypeToBeRewardType[req.GetRewardType()],
	})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		logger.Error(ctx, "error in BE RPC for RecordRewardSelectionInfo", zap.Error(te))
		res.RespHeader.Status = rpc.StatusInternalWithDebugMsg(te.Error())
		return res, nil
	}
	res.NextAction = beRes.GetNextAction()
	res.RespHeader.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) GetLoungePassesForUser(ctx context.Context, req *ffPb.GetLoungePassesForUserRequest) (*ffPb.GetLoungePassesForUserResponse, error) {
	var (
		res              = &ffPb.GetLoungePassesForUserResponse{RespHeader: &header.ResponseHeader{}}
		userRes          = &userPb.GetUserResponse{}
		loungePasseRes   = &ffBePb.GetLoungePassesForUserResponse{}
		beRewardRes      = &rewardsPb.RewardsResponse{}
		userErr          error
		loungePassErr    error
		loungeRewardsErr error
	)

	actorId := req.GetReq().GetAuth().GetActorId()

	grp, grpCtx := errgroup.WithContext(ctx)

	grp.Go(func() error {
		userRes, userErr = s.userClient.GetUser(grpCtx, &userPb.GetUserRequest{
			Identifier: &userPb.GetUserRequest_ActorId{
				ActorId: actorId,
			},
		})
		if te := epifigrpc.RPCError(userRes, userErr); te != nil {
			return pkgErr.Wrap(te, "error in fetching user response for the given user")
		}
		return nil
	})

	grp.Go(func() error {
		loungePasseRes, loungePassErr = s.fireFlyClient.GetLoungePassesForUser(grpCtx, &ffBePb.GetLoungePassesForUserRequest{ActorId: actorId})
		if te := epifigrpc.RPCError(loungePasseRes, loungePassErr); te != nil {
			return pkgErr.Wrap(te, "error in fetching lounge passes for the user")
		}
		return nil
	})
	grp.Go(func() error {
		beRewardRes, loungeRewardsErr = s.rewardsClient.GetRewardsByActorId(grpCtx, &rewardsPb.RewardsByActorIdRequest{
			ActorId: actorId,
			Filter: &rewardsPb.RewardsByActorIdRequest_Filter{
				RewardOfferType: rewardsPb.RewardOfferType_CREDIT_CARD_QUARTERLY_AIRPORT_LOUNGE_OFFER,
			},
			PageContext: &rpc.PageContextRequest{
				PageSize: 10,
			},
		})
		if te := epifigrpc.RPCError(beRewardRes, loungeRewardsErr); te != nil {
			return pkgErr.Wrap(te, "error in fetching lounge access reward info")
		}
		return nil
	})

	if grpErr := grp.Wait(); grpErr != nil {
		logger.Error(ctx, "error in data aggregation for fetching lounge passes", zap.Error(grpErr), zap.String(logger.ACTOR_ID_V2, actorId))
		res.RespHeader.Status = rpc.StatusInternal()
		return res, nil
	}

	unclaimedLoungeExists := false
	for _, reward := range beRewardRes.GetRewards() {
		if reward.GetStatus() == rewardsPb.RewardStatus_CREATED {
			if !helper2.IsTimestampInCurrentQuarter(reward.GetCreatedAt()) {
				continue
			}
			unclaimedLoungeExists = true
			break
		}
	}

	if unclaimedLoungeExists {
		res.ClaimAction = &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					FontColor:    snowColor,
					DisplayValue: &commontypes.Text_PlainString{PlainString: "Claim new lounge pass"},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_S},
				},
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:       forestColor,
				CornerRadius:  19,
				Height:        44,
				Width:         198,
				LeftPadding:   24,
				RightPadding:  24,
				TopPadding:    12,
				BottomPadding: 12,
			},
		}
	}

	res.Heading = &commontypes.Text{
		FontColor:    snowColor,
		DisplayValue: &commontypes.Text_PlainString{PlainString: "Collected passes"},
		FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_M},
	}

	res.Sections = &sections.Section{
		Content: &sections.Section_VerticalListSection{VerticalListSection: helper2.GetLoungePassComponentsForBeLoungePasses(userRes.GetUser().GetProfile().GetKycName(), loungePasseRes.GetLoungePasses())},
	}
	res.RespHeader.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) GetLoungeAccessIntroScreen(ctx context.Context, req *ffPb.GetLoungeAccessIntroScreenRequest) (*ffPb.GetLoungeAccessIntroScreenResponse, error) {
	var (
		res = &ffPb.GetLoungeAccessIntroScreenResponse{RespHeader: &header.ResponseHeader{}}
	)
	actorId := req.GetReq().GetAuth().GetActorId()

	cardRes, err := s.fireFlyClient.GetCreditCard(ctx, &ffBePb.GetCreditCardRequest{
		GetBy: &ffBePb.GetCreditCardRequest_ActorId{ActorId: actorId},
	})

	if te := epifigrpc.RPCError(cardRes, err); te != nil {
		logger.Error(ctx, "error in fetchng credit card info for the user", zap.Error(te), zap.String(logger.ACTOR_ID_V2, actorId))
		res.RespHeader.Status = rpc.StatusInternal()
		return res, nil
	}

	claimCta := &deeplink.Cta{}

	beRewardRes, err := s.rewardsClient.GetRewardsByActorId(ctx, &rewardsPb.RewardsByActorIdRequest{
		ActorId: actorId,
		Filter: &rewardsPb.RewardsByActorIdRequest_Filter{
			RewardOfferType: rewardsPb.RewardOfferType_CREDIT_CARD_QUARTERLY_AIRPORT_LOUNGE_OFFER,
		},
		PageContext: &rpc.PageContextRequest{
			PageSize: 10,
		},
	})
	switch {
	case err != nil:
		logger.Error(ctx, "error in fetching lounge access reward info", zap.Error(err),
			zap.String(logger.ACTOR_ID_V2, actorId))
		res.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusInternal(),
		}
		return res, nil
	case !beRewardRes.GetStatus().IsSuccess():
		logger.Error(ctx, "error in be rewards res", zap.String(logger.STATUS, beRewardRes.GetStatus().String()))
		res.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusInternal(),
		}
		return res, nil
	default:
	}

	netQuarterlySpends, err := s.getNetQuarterlySpendsInfo(ctx, cardRes.GetCreditCard().GetActorId(), cardRes.GetCreditCard().GetAccountId())
	if err != nil {
		logger.Error(ctx, "error in fetching net quarterly spends for the user", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		res.RespHeader.Status = rpc.StatusInternal()
		return res, nil
	}

	if len(beRewardRes.GetRewards()) == 0 {
		claimCta.Text = "Claim lounge pass"
		claimCta.Status = deeplink.Cta_CTA_STATUS_DISABLED
	} else {
		switch beRewardRes.GetRewards()[0].GetStatus() {
		case rewardsPb.RewardStatus_CREATED:
			claimCta.Text = "Claim lounge pass"
			claimCta.Status = deeplink.Cta_CTA_STATUS_ENABLED
			if !helper2.IsTimestampInCurrentQuarter(beRewardRes.GetRewards()[0].GetCreatedAt()) {
				claimCta.Status = deeplink.Cta_CTA_STATUS_DISABLED
			}
		case rewardsPb.RewardStatus_PROCESSED:
			claimCta = nil
		default:
			claimCta.Text = "Claiming lounge pass..."
			claimCta.Status = deeplink.Cta_CTA_STATUS_DISABLED
		}
	}

	res.Heading = &commontypes.Text{
		FontColor:    snowColor,
		DisplayValue: &commontypes.Text_PlainString{PlainString: "Lounge access"},
		FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_M},
	}

	res.LoungeAccessTopContainer = &ffPb.GetLoungeAccessIntroScreenResponse_LoungeAccessTopContainer{
		TopSectionView: &sections.Section{
			Content: &sections.Section_VerticalListSection{
				VerticalListSection: &sections.VerticalListSection{
					IsScrollable: false,
					Components: []*components.Component{
						{
							Content: getLoungeAccessHeaderComponent(netQuarterlySpends, moneyPkg.ParseInt(s.dynamicConf.CreditCard().SpendsThresholdForCcLoungeAccess(), "INR")),
						},
						{
							Content: helper2.GetAnyWithoutError(
								&sections.HorizontalListSection{
									VisualProperties: []*properties.VisualProperty{
										{
											Properties: &properties.VisualProperty_ContainerProperty{
												ContainerProperty: &properties.ContainerProperty{
													Size: &properties.Size{
														Width: &properties.Size_Dimension{
															Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
															ExactValue: 148,
														},
														Height: &properties.Size_Dimension{
															Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
															ExactValue: 148,
														},
													},
												},
											},
										},
									},
									Components: []*components.Component{
										{
											Content: helper2.GetAnyWithoutError(
												&commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{Image: &commontypes.VisualElement_Image{
													Source: &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/credit_card_images/lounge_access.png"},
													Properties: &commontypes.VisualElementProperties{
														Width:  188,
														Height: 188,
													},
													ImageType: commontypes.ImageType_PNG,
												}}}),
										}}}),
						},
						{
							Content: helper2.GetAnyWithoutError(&commontypes.Text{
								FontColor:    snowColor,
								DisplayValue: &commontypes.Text_PlainString{PlainString: "Enjoy access to the airport lounge"},
								FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_1},
							}),
						},
						{
							Content: getNumberedPointsComponent(1, "Get lounge access once every quarter."),
						},
						{
							Content: getNumberedPointsComponent(2, "Claim a lounge pass & you will get a QR code immediately. The QR code will remain valid for the entire quarter."),
						},
						{
							Content: getNumberedPointsComponent(3, "Show the QR code at any international or national airport lounge to get access."),
						},
					},
					VisualProperties: []*properties.VisualProperty{
						{
							Properties: &properties.VisualProperty_ContainerProperty{
								ContainerProperty: &properties.ContainerProperty{
									Size: &properties.Size{
										Width: &properties.Size_Dimension{
											Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
										},
										Height: &properties.Size_Dimension{
											Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
										},
									},
									BgColor: &widget.BackgroundColour{Colour: &widget.BackgroundColour_BlockColour{BlockColour: inkColor}},
									Corner: &properties.CornerProperty{
										TopLeftCornerRadius:  19,
										TopRightCornerRadius: 19,
										BottomLeftCorner:     19,
										BottomRightCorner:    19,
									},
								},
							},
						},
					},
				}},
		},
		PrimaryAction: claimCta,
		SecondaryAction: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					FontColor:    forestColor,
					DisplayValue: &commontypes.Text_PlainString{PlainString: "View available lounges"},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_S},
				},
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				LeftPadding:   24,
				RightPadding:  24,
				TopPadding:    12,
				BottomPadding: 12,
			},
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_WEB_PAGE,
				ScreenOptions: &deeplink.Deeplink_WebPageScreenOptions{
					WebPageScreenOptions: &deeplink.WebpageScreenOptions{
						WebpageUrl: "https://info.dreamfolks.in/partner-lounges?filter=UTJoLy9QZk1TMUtkdU5KbGxRdDM2dz09",
					},
				},
			},
		},
	}
	res.BottomSections = s.getLoungeAccessIntroScreenBottomSection(ctx, req.GetReq().GetAuth().GetActorId())
	res.RespHeader.Status = rpc.StatusOk()
	return res, nil
}

// getQuarterlySpendsInfo fetches the net quarterly spends for the user.
func (s *Service) getNetQuarterlySpendsInfo(ctx context.Context, actorId, accountId string) (*moneyPb.Money, error) {
	quarterStartTs, quarterEndTs := helper2.GetQuarterStartAndEndTimestampForAGivenTimestamp(timestampPb.Now())
	totalTxnAmt := &moneyPb.Money{}
	totalRefundAmt := &moneyPb.Money{}
	grp, grpCtx := errgroup.WithContext(ctx)
	grp.Go(func() error {
		aggregateSpendsRes, aggregateSpendsErr := s.getAggregateTxns(grpCtx, quarterStartTs, quarterEndTs, actorId, accountId, fireflyPkg.GetOntologyIdsToExcludeInTxnAggregatesForLoungeAccess())
		if aggregateSpendsErr != nil {
			return pkgErr.Wrap(aggregateSpendsErr, "error in fetching aggregate txns for the user")
		}
		totalTxnAmt = aggregateSpendsRes.GetTransactionAggregates().GetAmount()
		return nil
	})

	grp.Go(func() error {
		aggregateRefundSpendsRes, aggregateRefundSpendsErr := s.getRefundTxnAggregates(grpCtx, quarterStartTs, quarterEndTs, actorId, accountId, fireflyPkg.GetOntologyIdsToExcludeInTxnAggregatesForLoungeAccess())
		if aggregateRefundSpendsErr != nil {
			return pkgErr.Wrap(aggregateRefundSpendsErr, "error in fetching refund aggregate txns for the user")
		}
		totalRefundAmt = aggregateRefundSpendsRes.GetTransactionAggregates().GetAmount()
		return nil
	})

	if grpErr := grp.Wait(); grpErr != nil {
		return nil, pkgErr.Wrap(grpErr, "error in fetching aggregate txn info")
	}
	return moneyPkg.Subtract(totalTxnAmt, totalRefundAmt)
}

func (s *Service) getNetYearlySelectSpendsInfo(ctx context.Context, creditAccount *ffAccPb.CreditAccount, ontologyIds []string) (*moneyPb.Money, error) {
	startTime, endTime := helper2.GetYearStartAndEndTimestampForCreditAccount(creditAccount)
	spends, err := fireflyPkg.GetEffectiveUserSpendsByOntologyIds(ctx, creditAccount.GetActorId(), startTime, endTime, ontologyIds, s.pinotClient)
	if err != nil {
		return nil, pkgErr.Wrap(err, "error in GetEffectiveUserSpendsByOntologyIds")
	}
	return spends.GetPb(), nil
}

func (s *Service) getLoungeAccessIntroScreenBottomSection(ctx context.Context, actorId string) *sections.Section {
	verticalComponents := make([]*components.Component, 0)
	loungePassRes, err := s.fireFlyClient.GetLoungePassesForUser(ctx, &ffBePb.GetLoungePassesForUserRequest{ActorId: actorId})
	switch {
	case epifigrpc.RPCError(loungePassRes, err) != nil,
		len(loungePassRes.GetLoungePasses()) == 0:
	default:
		verticalComponents = append(verticalComponents,
			&components.Component{
				Content: helper2.GetAnyWithoutError(&components.Spacer{SpacingValue: components.Spacing_SPACING_M}),
			},
			&components.Component{
				Content: getCurvedRectangularFrameComponent("View collected passes", &deeplink.Deeplink{Screen: deeplink.Screen_CC_COLLECTED_LOUNGE_PASSES_SCREEN}),
			},
			&components.Component{
				Content: helper2.GetAnyWithoutError(&components.Spacer{SpacingValue: components.Spacing_SPACING_M}),
			})
	}
	verticalComponents = append(verticalComponents, &components.Component{
		Content: getCurvedRectangularFrameComponent("Frequently asked questions", &deeplink.Deeplink{
			Screen: deeplink.Screen_HELP_MAIN,
		}),
	})
	return &sections.Section{
		Content: &sections.Section_VerticalListSection{
			VerticalListSection: &sections.VerticalListSection{
				Components: verticalComponents,
			},
		},
	}
}

func getCurvedRectangularFrameComponent(text string, onClickAction *deeplink.Deeplink) *anyPb.Any {
	return helper2.GetAnyWithoutError(&sections.HorizontalListSection{
		HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_SPACE_BETWEEN,
		Components: []*components.Component{
			{
				Content: helper2.GetAnyWithoutError(&commontypes.Text{
					FontColor:    snowColor,
					DisplayValue: &commontypes.Text_PlainString{PlainString: text},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
					},
				}),
			},
			{
				Content: helper2.GetAnyWithoutError(&sections.HorizontalListSection{
					VisualProperties: []*properties.VisualProperty{
						{
							Properties: &properties.VisualProperty_ContainerProperty{
								ContainerProperty: &properties.ContainerProperty{
									Size: &properties.Size{
										Width: &properties.Size_Dimension{
											Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
											ExactValue: 24,
										},
										Height: &properties.Size_Dimension{
											Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
											ExactValue: 24,
										},
									},
								}}},
					},
					Components: []*components.Component{
						{
							Content: helper2.GetAnyWithoutError(&commontypes.VisualElement{
								Asset: &commontypes.VisualElement_Image_{
									Image: &commontypes.VisualElement_Image{
										Source: &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/credit_card_images/right_arrow.png"},
										Properties: &commontypes.VisualElementProperties{
											Width:  24,
											Height: 24,
										},
										ImageType: commontypes.ImageType_PNG,
									},
								},
							}),
						},
					}}),
			},
		},
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: &properties.ContainerProperty{
						Size: &properties.Size{
							Width: &properties.Size_Dimension{
								Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
							},
							Height: &properties.Size_Dimension{
								Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
								ExactValue: 72,
							},
						},
						BgColor: &widget.BackgroundColour{Colour: &widget.BackgroundColour_BlockColour{BlockColour: inkColor}},
						Corner: &properties.CornerProperty{
							TopLeftCornerRadius:  19,
							TopRightCornerRadius: 19,
							BottomLeftCorner:     19,
							BottomRightCorner:    19,
						},
						Padding: &properties.PaddingProperty{
							Left:   20,
							Top:    24,
							Right:  16,
							Bottom: 24,
						},
						Margin: &properties.PaddingProperty{
							Left:   16,
							Top:    8,
							Right:  16,
							Bottom: 8,
						},
					},
				},
			},
		},
		InteractionBehaviors: []*behaviors.InteractionBehavior{
			{
				Behavior: &behaviors.InteractionBehavior_OnClickBehavior{OnClickBehavior: &behaviors.OnClickBehavior{Action: helper2.GetAnyWithoutError(onClickAction)}},
			},
		},
	})
}

func getNumberedPointsComponent(pointNumber int, pointText string) *anyPb.Any {
	return helper2.GetAnyWithoutError(&sections.HorizontalListSection{
		HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_START,
		Components: []*components.Component{
			{
				Content: getBulletPointComponent(pointNumber),
			},
			{
				Content: helper2.GetAnyWithoutError(&components.Spacer{SpacingValue: components.Spacing_SPACING_XS}),
			},
			{
				Content: helper2.GetAnyWithoutError(&commontypes.Text{
					Alignment:    commontypes.Text_ALIGNMENT_LEFT,
					FontColor:    onyxColor,
					DisplayValue: &commontypes.Text_PlainString{PlainString: pointText},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_S},
				}),
			},
			{
				Content: helper2.GetAnyWithoutError(&components.Spacer{SpacingValue: components.Spacing_SPACING_UNSPECIFIED}),
			},
		},
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: &properties.ContainerProperty{
						Size: &properties.Size{
							Width: &properties.Size_Dimension{
								Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
							},
							Height: &properties.Size_Dimension{
								Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
							},
						},
						Padding: &properties.PaddingProperty{
							Left:   16,
							Top:    8,
							Right:  16,
							Bottom: 8,
						},
					},
				},
			},
		},
	})
}

func getBulletPointComponent(pointNumber int) *anyPb.Any {
	return helper2.GetAnyWithoutError(&sections.HorizontalListSection{
		HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_CENTER,
		VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_CENTER_VERTICALLY,
		Components: []*components.Component{
			{
				Content: helper2.GetAnyWithoutError(&commontypes.Text{
					FontColor: onyxColor,
					BgColor:   charcoalColor,
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: fmt.Sprintf("%d", pointNumber),
					},
					FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_3},
				}),
			},
		},
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: &properties.ContainerProperty{
						Size: &properties.Size{
							Width: &properties.Size_Dimension{
								Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
								ExactValue: 32,
							},
							Height: &properties.Size_Dimension{
								Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
								ExactValue: 32,
							},
						},
						BgColor: &widget.BackgroundColour{Colour: &widget.BackgroundColour_BlockColour{BlockColour: charcoalColor}},
						Corner: &properties.CornerProperty{
							TopLeftCornerRadius:  16,
							TopRightCornerRadius: 16,
							BottomLeftCorner:     16,
							BottomRightCorner:    16,
						},
						Padding: &properties.PaddingProperty{
							Top:    8,
							Bottom: 8,
						},
					},
				},
			},
		},
	})
}

func getLoungeAccessHeaderComponent(spentAmount, thresholdAmount *moneyPb.Money) *anyPb.Any {
	if moneyPkg.Compare(thresholdAmount, spentAmount) <= 0 {
		return getLoungeEligibleHeaderComponent()
	}
	return getNonEligibleSpendsLoungeHeaderComponent(spentAmount, thresholdAmount)
}

func getNonEligibleSpendsLoungeHeaderComponent(spentAmount, thresholdAmount *moneyPb.Money) *anyPb.Any {
	resCmp := make([]*components.Component, 0)
	textWithIconCmp := helper2.GetAnyWithoutError(&sections.HorizontalListSection{
		Components: []*components.Component{
			{
				Content: helper2.GetAnyWithoutError(&commontypes.VisualElement{
					Asset: &commontypes.VisualElement_Image_{
						Image: &commontypes.VisualElement_Image{
							Source: &commontypes.VisualElement_Image_Url{
								Url: "https://epifi-icons.pointz.in/credit_card_images/blue_star.png",
							},
							Properties: &commontypes.VisualElementProperties{
								Width:  48,
								Height: 48,
							},
							ImageType: commontypes.ImageType_PNG,
						},
					},
				}),
			},
			{
				Content: helper2.GetAnyWithoutError(&commontypes.Text{
					Alignment:    commontypes.Text_ALIGNMENT_LEFT,
					FontColor:    oceanColor,
					DisplayValue: &commontypes.Text_PlainString{PlainString: "Unlock lounge access for this or next quarter when you spend a minimum of ₹10,000"},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
				}),
			},
		},
	})

	textWithIconCmp = helper2.GetAnyWithoutError(
		&sections.HorizontalListSection{
			Components: []*components.Component{
				{
					Content: helper2.GetAnyWithoutError(&sections.VerticalListSection{
						VisualProperties: []*properties.VisualProperty{
							{
								Properties: &properties.VisualProperty_ContainerProperty{
									ContainerProperty: &properties.ContainerProperty{
										Size: &properties.Size{
											Width: &properties.Size_Dimension{
												Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
												ExactValue: 40,
											},
											Height: &properties.Size_Dimension{
												Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
												ExactValue: 40,
											},
										},
									},
								},
							},
						},
						Components: []*components.Component{
							{
								Content: helper2.GetAnyWithoutError(&commontypes.VisualElement{
									Asset: &commontypes.VisualElement_Image_{
										Image: &commontypes.VisualElement_Image{
											Source: &commontypes.VisualElement_Image_Url{
												Url: "https://epifi-icons.pointz.in/credit_card_images/blue_star.png",
											},
											Properties: &commontypes.VisualElementProperties{
												Width:  24,
												Height: 24,
											},
											ImageType: commontypes.ImageType_PNG,
										},
									},
								}),
							},
						},
					}),
				},
				{
					Content: helper2.GetAnyWithoutError(&sections.VerticalListSection{
						Components: []*components.Component{
							{
								Content: helper2.GetAnyWithoutError(&commontypes.Text{
									FontColor:    oceanColor,
									DisplayValue: &commontypes.Text_PlainString{PlainString: "Unlock lounge access for this or next quarter when you spend a minimum of ₹10,000"},
									FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
								}),
							},
						}}),
				},
			}})
	resCmp = append(resCmp, &components.Component{
		Content: textWithIconCmp,
	})
	if !moneyPkg.IsZero(spentAmount) {
		spentAmountDecimal := moneyPkg.ToDecimal(spentAmount)
		thresholdDecimal := moneyPkg.ToDecimal(thresholdAmount)
		ratio, _ := spentAmountDecimal.Div(thresholdDecimal).Float64()
		ratio *= 324
		ratioInt := int32(ratio)
		progressBar := &sections.HorizontalListSection{

			HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_START,
			Components: []*components.Component{
				{
					Content: helper2.GetAnyWithoutError(&sections.HorizontalListSection{
						Components: []*components.Component{},
						VisualProperties: []*properties.VisualProperty{
							{
								Properties: &properties.VisualProperty_ContainerProperty{
									ContainerProperty: &properties.ContainerProperty{
										Size: &properties.Size{
											Width: &properties.Size_Dimension{
												Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
												ExactValue: ratioInt,
											},
											Height: &properties.Size_Dimension{
												Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
												ExactValue: 8,
											},
										},
										BgColor: &widget.BackgroundColour{
											Colour: &widget.BackgroundColour_BlockColour{
												BlockColour: darkOcean,
											},
										},
										Corner: &properties.CornerProperty{
											TopLeftCornerRadius:  4,
											TopRightCornerRadius: 4,
											BottomLeftCorner:     4,
											BottomRightCorner:    4,
										},
									}},
							},
						},
					}),
				},
			},
			VisualProperties: []*properties.VisualProperty{
				{
					Properties: &properties.VisualProperty_ContainerProperty{
						ContainerProperty: &properties.ContainerProperty{
							Size: &properties.Size{
								Width: &properties.Size_Dimension{
									Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
								},
								Height: &properties.Size_Dimension{
									Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
									ExactValue: 8,
								},
							},
							BgColor: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{
									BlockColour: ocean100,
								},
							},
							Corner: &properties.CornerProperty{
								TopLeftCornerRadius:  20,
								TopRightCornerRadius: 20,
								BottomLeftCorner:     20,
								BottomRightCorner:    20,
							},
						}},
				},
			},
		}
		resCmp = append(resCmp, &components.Component{
			Content: helper2.GetAnyWithoutError(progressBar),
		}, &components.Component{
			Content: helper2.GetAnyWithoutError(&sections.HorizontalListSection{
				VisualProperties: []*properties.VisualProperty{
					{
						Properties: &properties.VisualProperty_ContainerProperty{
							ContainerProperty: &properties.ContainerProperty{
								Size: &properties.Size{
									Width: &properties.Size_Dimension{
										Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
									},
									Height: &properties.Size_Dimension{
										Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
									},
								},
							},
						},
					},
				},
				HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_START,
				Components: []*components.Component{
					{
						Content: helper2.GetAnyWithoutError(&commontypes.Text{
							FontColor:    oceanColor,
							DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("%s/%s", moneyPkg.ToDisplayStringWithPrecision(spentAmount, 0), moneyPkg.ToDisplayStringWithPrecision(thresholdAmount, 0))},
						}),
					},
				},
			}),
		})
	}
	parentSection := &sections.VerticalListSection{
		IsScrollable: false,
		Components:   resCmp,
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: &properties.ContainerProperty{
						Size: &properties.Size{
							Width: &properties.Size_Dimension{
								Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
							},
							Height: &properties.Size_Dimension{
								Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
							},
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{
								BlockColour: pastelOcean,
							},
						},
						Corner: &properties.CornerProperty{
							TopLeftCornerRadius:  10,
							TopRightCornerRadius: 10,
							BottomLeftCorner:     10,
							BottomRightCorner:    10,
						},
						Padding: &properties.PaddingProperty{
							Left:   20,
							Top:    12,
							Right:  20,
							Bottom: 12,
						},
						Margin: &properties.PaddingProperty{
							Left:  8,
							Top:   8,
							Right: 8,
						},
					},
				},
			},
		},
	}
	return helper2.GetAnyWithoutError(parentSection)
}

func getLoungeEligibleHeaderComponent() *anyPb.Any {
	childCmp := helper2.GetAnyWithoutError(&sections.HorizontalListSection{
		Components: []*components.Component{
			{
				Content: helper2.GetAnyWithoutError(&commontypes.VisualElement{
					Asset: &commontypes.VisualElement_Image_{
						Image: &commontypes.VisualElement_Image{
							Source: &commontypes.VisualElement_Image_Url{
								Url: "https://epifi-icons.pointz.in/credit_card_images/blue_star.png",
							},
							Properties: &commontypes.VisualElementProperties{
								Width:  24,
								Height: 24,
							},
							ImageType: commontypes.ImageType_PNG,
						},
					},
				}),
			},
			{
				Content: helper2.GetAnyWithoutError(&commontypes.Text{
					Alignment:    commontypes.Text_ALIGNMENT_LEFT,
					FontColor:    oceanColor,
					DisplayValue: &commontypes.Text_PlainString{PlainString: "Lounge access unlocked!"},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
				}),
			},
			{
				Content: helper2.GetAnyWithoutError(&components.Spacer{}),
			},
		},
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: &properties.ContainerProperty{
						Size: &properties.Size{
							Width: &properties.Size_Dimension{
								Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
							},
							Height: &properties.Size_Dimension{
								Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
							},
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{
								BlockColour: pastelOcean,
							},
						},
						Corner: &properties.CornerProperty{
							TopLeftCornerRadius:  10,
							TopRightCornerRadius: 10,
							BottomLeftCorner:     10,
							BottomRightCorner:    10,
						},
						Padding: &properties.PaddingProperty{
							Left:   20,
							Top:    12,
							Right:  20,
							Bottom: 12,
						},
						Margin: &properties.PaddingProperty{
							Left:   8,
							Top:    8,
							Right:  8,
							Bottom: 8,
						},
					},
				},
			},
		},
	})
	return childCmp
}
