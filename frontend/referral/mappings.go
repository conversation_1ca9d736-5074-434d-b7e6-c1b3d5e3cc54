package referral

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/frontend/app/apputils"
	"github.com/epifi/gamma/pkg/accrual"

	"context"
	"fmt"
	"strings"
	"time"

	"github.com/samber/lo"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/pkg/money"

	beInAppReferralEnumPb "github.com/epifi/gamma/api/inappreferral/enums"
	"github.com/epifi/gamma/api/typesv2/ui"
	userContactPb "github.com/epifi/gamma/api/user/contact"

	beInAppReferralPb "github.com/epifi/gamma/api/inappreferral"

	deepLinkPb "github.com/epifi/gamma/api/frontend/deeplink"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	beRewardsPb "github.com/epifi/gamma/api/rewards"

	feReferralPb "github.com/epifi/gamma/api/frontend/referral"
	beSeasonsPb "github.com/epifi/gamma/api/inappreferral/season"
)

var (
	beShareActionTypeToFeMapping = map[beInAppReferralEnumPb.ReferralsV1CodeShareActionType]feReferralPb.ShareActionType{
		beInAppReferralEnumPb.ReferralsV1CodeShareActionType_WHATSAPP:          feReferralPb.ShareActionType_WHATSAPP,
		beInAppReferralEnumPb.ReferralsV1CodeShareActionType_MESSAGE:           feReferralPb.ShareActionType_MESSAGE,
		beInAppReferralEnumPb.ReferralsV1CodeShareActionType_COPY_TO_CLIPBOARD: feReferralPb.ShareActionType_COPY_TO_CLIPBOARD,
		beInAppReferralEnumPb.ReferralsV1CodeShareActionType_INVITE_FRIENDS:    feReferralPb.ShareActionType_INVITE_FRIENDS,
		beInAppReferralEnumPb.ReferralsV1CodeShareActionType_INSTAGRAM:         feReferralPb.ShareActionType_INSTAGRAM,
		beInAppReferralEnumPb.ReferralsV1CodeShareActionType_TWITTER:           feReferralPb.ShareActionType_TWITTER,
	}
	beShareCodeMediaTypeToFeMapping = map[beInAppReferralEnumPb.ReferralsV1CodeShareMediaType]feReferralPb.ShareCodeV1Info_ShareActionInfo_ShareContent_MediaType{
		beInAppReferralEnumPb.ReferralsV1CodeShareMediaType_DEFAULT:                                        feReferralPb.ShareCodeV1Info_ShareActionInfo_ShareContent_DEFAULT_IMAGE,
		beInAppReferralEnumPb.ReferralsV1CodeShareMediaType_CUSTOM:                                         feReferralPb.ShareCodeV1Info_ShareActionInfo_ShareContent_CUSTOM_IMAGE,
		beInAppReferralEnumPb.ReferralsV1CodeShareMediaType_REFERRALS_V1_CODE_SHARE_MEDIA_TYPE_UNSPECIFIED: feReferralPb.ShareCodeV1Info_ShareActionInfo_ShareContent_MEDIA_TYPE_UNSPECIFIED,
	}
	beShareCodeMediaTypeToFeMappingV2 = map[beInAppReferralEnumPb.ReferralsV1CodeShareMediaType]feReferralPb.MediaType{
		beInAppReferralEnumPb.ReferralsV1CodeShareMediaType_DEFAULT:                                        feReferralPb.MediaType_DEFAULT_IMAGE,
		beInAppReferralEnumPb.ReferralsV1CodeShareMediaType_CUSTOM:                                         feReferralPb.MediaType_CUSTOM_IMAGE,
		beInAppReferralEnumPb.ReferralsV1CodeShareMediaType_REFERRALS_V1_CODE_SHARE_MEDIA_TYPE_UNSPECIFIED: feReferralPb.MediaType_MEDIA_TYPE_UNSPECIFIED,
	}
	feReferralLinkVendorToBeMapping = map[feReferralPb.ReferralLinkVendor]beInAppReferralEnumPb.ReferralLinkVendor{
		feReferralPb.ReferralLinkVendor_APPSFLYER_ONELINK:                beInAppReferralEnumPb.ReferralLinkVendor_APPSFLYER_ONELINK,
		feReferralPb.ReferralLinkVendor_REFERRAL_LINK_VENDOR_UNSPECIFIED: beInAppReferralEnumPb.ReferralLinkVendor_REFERRAL_LINK_VENDOR_UNSPECIFIED,
	}
	beIconAnimationTypeToFeIconAnimationTypeMapping = map[beInAppReferralEnumPb.ReferralsV1IconAnimationType]feReferralPb.IconAnimationType{
		beInAppReferralEnumPb.ReferralsV1IconAnimationType_TYPE_ONE:                                     feReferralPb.IconAnimationType_TYPE_ONE,
		beInAppReferralEnumPb.ReferralsV1IconAnimationType_REFERRALS_V1_ICON_ANIMATION_TYPE_UNSPECIFIED: feReferralPb.IconAnimationType_ICON_ANIMATION_TYPE_UNSPECIFIED,
	}
	refereeRemindStageToRemindScriptKeyMap = map[RefereeRemindStage]string{
		NeedsToSignUp:        "NEEDS_TO_SIGN_UP",
		NeedsToAddFunds:      "NEEDS_TO_ADD_FUNDS",
		NeedsToReceiveSalary: "NEEDS_TO_RECEIVE_SALARY",
	}
)

// note - please be careful when creating new placeholders so that no placeholder contains any other placeholder. e.g., if there's a placeholder "FINITE_CODE",
// no placeholder should contain this string, as (if the order of evaluation isn't correct), we will end up replacing "FINITE_CODE" in the placeholder
// where it's not intended.
const (
	finiteCodePlaceHolder                   = "FINITE_CODE_PLACEHOLDER"
	appDownloadUrlPlaceholder               = "APP_DOWNLOAD_URL_PLACEHOLDER"
	appDownloadUrlWithFiniteCodePlaceholder = "APP_DOWNLOAD_URL_WITH_FINITE_CODE"
)

func (s *Service) convertToFeSeasonFeatureDetails(beFeatureDetails []*beSeasonsPb.Season_DisplayDetails_FeatureDetails) []*feReferralPb.Season_DisplayDetails_AdditionalDetails_FeatureDetails {
	featureDetailsArray := make([]*feReferralPb.Season_DisplayDetails_AdditionalDetails_FeatureDetails, 0, len(beFeatureDetails))
	for _, beFeatureDetail := range beFeatureDetails {
		featureDetailsArray = append(featureDetailsArray, &feReferralPb.Season_DisplayDetails_AdditionalDetails_FeatureDetails{
			ImageUrl: beFeatureDetail.GetImageUrl(),
			Desc:     beFeatureDetail.GetDesc(),
		})
	}
	return featureDetailsArray
}

// Currently only SD and Cash type referral rewards are supported hence this function only extracts those and returns
// error if any other rewardType is encountered. In future additional rewardTypes handling can be added if needed.
func (s *Service) getRewardUnitsFromRewardOptionForSeason(rewardOption *beRewardsPb.RewardOption) (int64, error) {
	switch rewardOption.GetRewardType() {
	case beRewardsPb.RewardType_CASH:
		return rewardOption.GetCash().GetAmount().GetUnits(), nil
	case beRewardsPb.RewardType_SMART_DEPOSIT:
		return rewardOption.GetSmartDeposit().GetAmount().GetUnits(), nil
	default:
		return 0, fmt.Errorf("unsupported reward type encountered while extracting reward units from generated reward option")
	}
}

// getReferralsLandingPromotionalBannerInfo generates the promotional banner response
// nolint:funlen
func (s *Service) getReferralsLandingPromotionalBannerInfo() *feReferralPb.GetInviteFriendScreenDetailsResponse_Banner {
	banner := &feReferralPb.GetInviteFriendScreenDetailsResponse_Banner{
		Title: &commontypes.Text{
			Text:      s.dyconf.Referrals().LandingScreenPromotionalBanner().Title(),
			FontColor: s.dyconf.Referrals().LandingScreenPromotionalBanner().TitleColor(),
		},
		Image: &commontypes.Image{
			ImageType: commontypes.ImageType_PNG,
			ImageUrl:  s.dyconf.Referrals().LandingScreenPromotionalBanner().ImageUrl(),
		},
		BgColor: s.dyconf.Referrals().LandingScreenPromotionalBanner().BgColor(),
	}

	// add CTA and ExpandedState to banner if CTA exists
	if s.dyconf.Referrals().LandingScreenPromotionalBanner().Cta().IsEnabled() {
		banner.Cta = &feReferralPb.CTA{
			Text: &commontypes.Text{
				Text:      s.dyconf.Referrals().LandingScreenPromotionalBanner().Cta().Text(),
				FontColor: s.dyconf.Referrals().LandingScreenPromotionalBanner().Cta().TextColor(),
				BgColor:   s.dyconf.Referrals().LandingScreenPromotionalBanner().Cta().BgColor(),
			},
			IsVisible: s.dyconf.Referrals().LandingScreenPromotionalBanner().Cta().IsVisible(),
		}
		if s.dyconf.Referrals().LandingScreenPromotionalBanner().Cta().ImageUrl() != "" {
			banner.Cta.Image = &commontypes.Image{
				ImageType: commontypes.ImageType_PNG,
				ImageUrl:  s.dyconf.Referrals().LandingScreenPromotionalBanner().Cta().ImageUrl(),
			}
		}

		banner.ExpandedInfo = &feReferralPb.GetInviteFriendScreenDetailsResponse_Banner_ExpandedInfo{
			Title: &commontypes.Text{
				Text:      s.dyconf.Referrals().LandingScreenPromotionalBanner().ExpandedStateTitle(),
				FontColor: s.dyconf.Referrals().LandingScreenPromotionalBanner().ExpandedStateTitleColor(),
			},
			Desc: &commontypes.Text{
				Text:      s.dyconf.Referrals().LandingScreenPromotionalBanner().ExpandedStateDesc(),
				FontColor: s.dyconf.Referrals().LandingScreenPromotionalBanner().ExpandedStateDescColor(),
			},
			Icon: &commontypes.Image{
				ImageUrl: s.dyconf.Referrals().LandingScreenPromotionalBanner().ExpandedStateIconUrl(),
			},
			DynamicInfos: []*feReferralPb.GetInviteFriendScreenDetailsResponse_Banner_ExpandedInfo_DynamicInfo{
				{
					Heading: s.dyconf.Referrals().LandingScreenPromotionalBanner().ExpandedStateHeading1(),
					Info:    strings.Split(s.dyconf.Referrals().LandingScreenPromotionalBanner().ExpandedStateInfos1(), "\n"),
				},
				{
					Heading: s.dyconf.Referrals().LandingScreenPromotionalBanner().ExpandedStateHeading2(),
					Info:    strings.Split(accrual.ReplaceCoinWithPointIfApplicable(s.dyconf.Referrals().LandingScreenPromotionalBanner().ExpandedStateInfos2(), nil), "\n"),
				},
			},
		}
	}

	// add remaining attributes to ExpandedState
	if banner.ExpandedInfo != nil {
		// add CTA to expanded state of banner if it exists
		if s.dyconf.Referrals().LandingScreenPromotionalBanner().ExpandedStateCta().IsEnabled() {
			banner.ExpandedInfo.Cta = &feReferralPb.CTA{
				Text: &commontypes.Text{
					Text:      s.dyconf.Referrals().LandingScreenPromotionalBanner().ExpandedStateCta().Text(),
					FontColor: s.dyconf.Referrals().LandingScreenPromotionalBanner().ExpandedStateCta().TextColor(),
					BgColor:   s.dyconf.Referrals().LandingScreenPromotionalBanner().ExpandedStateCta().BgColor(),
				},
				DeeplinkAction: s.dyconf.Referrals().LandingScreenPromotionalBanner().ExpandedStateCta().Deeplink(),
				IsVisible:      s.dyconf.Referrals().LandingScreenPromotionalBanner().ExpandedStateCta().IsVisible(),
			}
			if s.dyconf.Referrals().LandingScreenPromotionalBanner().ExpandedStateCta().ImageUrl() != "" {
				banner.ExpandedInfo.Cta.Image = &commontypes.Image{
					ImageType: commontypes.ImageType_PNG,
					ImageUrl:  s.dyconf.Referrals().LandingScreenPromotionalBanner().ExpandedStateCta().ImageUrl(),
				}
			}
		}
	}

	return banner
}

func (s *Service) getFeSeasonRewardFromBeSeasonReferralReward(beSeasonReferralReward *beRewardsPb.Reward) (*feReferralPb.SeasonReward, error) {
	// we assume that there will be only a single reward option, hence passing 0th element to extract reward units from.
	// if, in the future, we decide to support multi-option rewards, we'll need to make changes here to support them.
	rewardUnits, err := s.getRewardUnitsFromRewardOptionForSeason(beSeasonReferralReward.GetRewardOptions().GetOptions()[0])
	if err != nil {
		return nil, fmt.Errorf("error in extracting reward units from generated reward, rewardId: %s, err: %w", beSeasonReferralReward.GetId(), err)
	}

	switch beSeasonReferralReward.GetStatus() {
	case beRewardsPb.RewardStatus_CREATED:
		return &feReferralPb.SeasonReward{
			Status: feReferralPb.SeasonRewardStatus_UNCLAIMED,
			DisplayDetails: &feReferralPb.SeasonReward_DisplayDetails{
				RewardValue: fmt.Sprintf("₹%d", rewardUnits),
				Cta: &feReferralPb.SeasonReward_DisplayDetails_CTA{
					Text: "CLAIM",
					Deeplink: &deepLinkPb.Deeplink{
						Screen: deepLinkPb.Screen_MY_REWARDS_SCREEN,
					},
				},
			},
		}, nil
	case beRewardsPb.RewardStatus_PROCESSED:
		return &feReferralPb.SeasonReward{
			Status: feReferralPb.SeasonRewardStatus_CLAIMED,
			DisplayDetails: &feReferralPb.SeasonReward_DisplayDetails{
				RewardValue: fmt.Sprintf("₹%d", rewardUnits),
				Cta:         nil,
			},
		}, nil

	case beRewardsPb.RewardStatus_PROCESSING_PENDING, beRewardsPb.RewardStatus_PROCESSING_IN_PROGRESS, beRewardsPb.RewardStatus_PROCESSING_FAILED:
		return &feReferralPb.SeasonReward{
			Status: feReferralPb.SeasonRewardStatus_PROCESSING,
			DisplayDetails: &feReferralPb.SeasonReward_DisplayDetails{
				RewardValue: fmt.Sprintf("₹%d", rewardUnits),
				Cta:         nil,
			},
		}, nil
	default:
	}
	// should never reach this case ideally
	logger.Warn("un-handled state encountered for reward while converting to FE reward for seasons", zap.String("beRewardState", beSeasonReferralReward.GetStatus().String()))
	return &feReferralPb.SeasonReward{
		Status: feReferralPb.SeasonRewardStatus_SEASON_REWARD_STATUS_UNSPECIFIED,
		DisplayDetails: &feReferralPb.SeasonReward_DisplayDetails{
			RewardValue: fmt.Sprintf("₹%d", rewardUnits),
			Cta:         nil,
		},
	}, nil
}

// nolint:funlen
func (s *Service) getFeLevelsInfoFromBeSeasonLevels(beSeasonLevels *beSeasonsPb.SeasonLevels, rewardsEarnedDuringSeason []*beRewardsPb.Reward) (*feReferralPb.SeasonLevelsInfo, error) {
	feSeasonLevels := make([]*feReferralPb.SeasonLevel, 0, len(beSeasonLevels.GetLevels()))

	// rewardsListStartIdx is used to track the start pointer in the referralRewards array
	// from which we check if rewards for a particular level have been given out
	rewardsListStartIdx := uint32(0)

	totalRewardsUptoPreviousLevel := 0
	currentOngoingLevel := 0

	for levelIdx, beSeasonLevel := range beSeasonLevels.GetLevels() {
		feSeasonRewards := make([]*feReferralPb.SeasonReward, 0, beSeasonLevel.GetTotalRewards())

		// if seasonReferralRewards has seasonRewardIdx + rewardsListStartIdx index present we use that reward as the level's reward entry.
		// The seasonReferralRewards array is sorted by createdAt time and contains rewards given to user since the season became active,
		// hence it is safe to assume that each reward in this array corresponds to a reward in a level in the season.
		// Given this, we can populate the levels of a season with the earned rewards and populate the remaining rewards for a level as 'YET_TO_EARN'
		for seasonRewardIdx := uint32(0); seasonRewardIdx < beSeasonLevel.GetTotalRewards(); seasonRewardIdx++ {
			if seasonRewardIdx+rewardsListStartIdx < uint32(len(rewardsEarnedDuringSeason)) {
				seasonReferralReward := rewardsEarnedDuringSeason[seasonRewardIdx+rewardsListStartIdx]

				feSeasonReward, err := s.getFeSeasonRewardFromBeSeasonReferralReward(seasonReferralReward)
				if err != nil {
					return nil, fmt.Errorf("error while fetching fe season reward from be referral reward, err - %w", err)
				}

				feSeasonRewards = append(feSeasonRewards, feSeasonReward)
			} else {
				feSeasonRewards = append(feSeasonRewards, &feReferralPb.SeasonReward{
					Status: feReferralPb.SeasonRewardStatus_YET_TO_EARN,
					DisplayDetails: &feReferralPb.SeasonReward_DisplayDetails{
						RewardValue: fmt.Sprintf("₹%d", beSeasonLevel.GetRewardValue()),
						Cta:         nil,
					},
				})
			}
		}
		rewardsListStartIdx += beSeasonLevel.GetTotalRewards()

		// setting level's status as locked/unlocked depending on whether the number of total rewards upto previous level
		// is <= the number of referral rewards given out to the user so far
		var seasonLevelStatus feReferralPb.SeasonLevelStatus
		if len(rewardsEarnedDuringSeason) >= totalRewardsUptoPreviousLevel {
			seasonLevelStatus = feReferralPb.SeasonLevelStatus_UNLOCKED
		} else {
			seasonLevelStatus = feReferralPb.SeasonLevelStatus_LOCKED
		}

		// setting current ongoing level based on how many total referral rewards have been given out to user till now.
		// current ongoing level will be the one for which the count of referral rewards given out lie between
		// totalRewardsUptoPreviousLevel (inclusive) and totalRewardsUptoCurrentLevel (exclusive)
		if len(rewardsEarnedDuringSeason) >= totalRewardsUptoPreviousLevel &&
			len(rewardsEarnedDuringSeason) < totalRewardsUptoPreviousLevel+int(beSeasonLevel.GetTotalRewards()) {
			currentOngoingLevel = levelIdx + 1
		}

		totalRewardsUptoPreviousLevel += int(beSeasonLevel.GetTotalRewards())

		feSeasonLevels = append(feSeasonLevels, &feReferralPb.SeasonLevel{
			Status: seasonLevelStatus,
			DisplayDetails: &feReferralPb.SeasonLevel_DisplayDetails{
				Title: beSeasonLevel.GetDisplayDetails().GetTitle(),
				Desc:  beSeasonLevel.GetDisplayDetails().GetDesc(),
			},
			Rewards: feSeasonRewards,
		})
	}
	return &feReferralPb.SeasonLevelsInfo{
		SeasonLevels: feSeasonLevels,
		OngoingLevel: uint32(currentOngoingLevel),
	}, nil
}

func (s *Service) getFeSeasonFromBeSeason(beSeason *beSeasonsPb.Season, rewardsEarnedDuringSeason []*beRewardsPb.Reward) (*feReferralPb.Season, error) {
	seasonTimePeriodString := fmt.Sprintf("%d %s - %d %s",
		beSeason.GetActiveSince().AsTime().Day(),
		beSeason.GetActiveSince().AsTime().Month().String(),
		beSeason.GetActiveTill().AsTime().Day(),
		beSeason.GetActiveTill().AsTime().Month().String(),
	)

	feLevelsInfo, err := s.getFeLevelsInfoFromBeSeasonLevels(beSeason.GetSeasonLevels(), rewardsEarnedDuringSeason)
	if err != nil {
		return nil, fmt.Errorf("error while generating fe levels info for season from be season levels, err - %w", err)
	}

	return &feReferralPb.Season{
		Id: beSeason.GetId(),
		DisplayDetails: &feReferralPb.Season_DisplayDetails{
			NthSeason: fmt.Sprintf("Season %v", beSeason.GetDisplayDetails().GetSeasonNumber()),
			Title:     beSeason.GetDisplayDetails().GetTitle(),
			Period:    seasonTimePeriodString,
			ImageUrl:  beSeason.GetDisplayDetails().GetImageUrl(),
			BgColor:   beSeason.GetDisplayDetails().GetBgColor(),
			AdditionalDetails: &feReferralPb.Season_DisplayDetails_AdditionalDetails{
				Desc:           beSeason.GetDisplayDetails().GetDesc(),
				FeatureDetails: s.convertToFeSeasonFeatureDetails(beSeason.GetDisplayDetails().GetFeatureDetails()),
				Cta:            &feReferralPb.Season_DisplayDetails_AdditionalDetails_CTA{Text: "Ok, got it"},
			},
		},
		LevelsInfo: feLevelsInfo,
	}, nil
}

func (s *Service) getFeReferralsHomeScreenWidgetV1(beReferralsWidgetV1 *beInAppReferralPb.HomeScreenReferralsWidgetDisplayDetailsV1, isReferralsUnlockedForActor bool, actorFiniteCode string, appPlatform commontypes.Platform, appVersion uint32) (*feReferralPb.HomeScreenReferralsWidgetDisplayDetails_ReferralsWidgetV1, error) {
	if beReferralsWidgetV1 == nil {
		return nil, nil
	}

	var feReferralsWidgetV1 *feReferralPb.HomeScreenReferralsWidgetDisplayDetails_ReferralsWidgetV1
	if isReferralsUnlockedForActor {
		ctas, err := s.getFeCtasFromBeCtas(beReferralsWidgetV1.GetReferralsUnlocked().GetCtas(), actorFiniteCode)
		if err != nil {
			return nil, fmt.Errorf("error while converting BE CTA to FE CTA, err: %w", err)
		}
		/*
			Backwards compatibility fix for iOS wherein sending 1 CTA from BE still shows 2 CTAs on the entry point
			with the first one being glitch-y.
			Thus, manually prefixing with share-code CTA for appVersions below the fix-version.
		*/
		if appPlatform == commontypes.Platform_IOS && appVersion < s.dyconf.Referrals().MinIosVersionSupportingCtasFixForHomeV1EntryPoint() && len(ctas) == 1 {
			ctaConf := s.dyconf.Referrals().PrefixCtaForIosHomeV1ReferralsUnlockedEntryPoint()
			prefixCta := &feReferralPb.CtaV1{
				IconTextComponent: &ui.IconTextComponent{
					RightIcon: &commontypes.Image{ImageUrl: ctaConf.IconUrl()},
					Texts: []*commontypes.Text{
						{
							DisplayValue: &commontypes.Text_PlainString{PlainString: ctaConf.Text()},
							FontColor:    ctaConf.FontColor(),
							FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_S},
						},
					},
				},
				IsVisible: true,
				BgColor:   ctaConf.BgColor(),
				Shadow: &ui.Shadow{
					Height: ctaConf.ShadowHeight(),
					Colour: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: ctaConf.ShadowColor()}},
				},
				Action: &feReferralPb.CtaV1_ShareFiniteCodeAction{ShareFiniteCodeAction: &feReferralPb.ShareFiniteCodeAction{
					ShareMessage: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: s.replaceShareCodePlaceHolders(ctaConf.ShareFiniteCodeMessage(), actorFiniteCode, "", false),
						},
					},
				}},
			}
			ctas = append([]*feReferralPb.CtaV1{prefixCta}, ctas...)
		}

		feReferralsWidgetV1 = &feReferralPb.HomeScreenReferralsWidgetDisplayDetails_ReferralsWidgetV1{
			Title: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: beReferralsWidgetV1.GetReferralsUnlocked().GetTitle()},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_DISPLAY_XL},
				FontColor:    beReferralsWidgetV1.GetReferralsUnlocked().GetTitleFontColor(),
			},
			MainImage: &commontypes.Image{ImageUrl: beReferralsWidgetV1.GetReferralsUnlocked().GetMainImageUrl()},
			Description: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: beReferralsWidgetV1.GetReferralsUnlocked().GetDescription()},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_M},
				FontColor:    beReferralsWidgetV1.GetReferralsUnlocked().GetDescriptionFontColor(),
			},
			Ctas: ctas,
		}
	} else {
		ctas, err := s.getFeCtasFromBeCtas(beReferralsWidgetV1.GetReferralsLocked().GetCtas(), actorFiniteCode)
		if err != nil {
			return nil, fmt.Errorf("error while converting BE CTA to FE CTA, err: %w", err)
		}
		feReferralsWidgetV1 = &feReferralPb.HomeScreenReferralsWidgetDisplayDetails_ReferralsWidgetV1{
			Title: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: beReferralsWidgetV1.GetReferralsLocked().GetTitle()},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_DISPLAY_XL},
				FontColor:    beReferralsWidgetV1.GetReferralsLocked().GetTitleFontColor(),
			},
			MainImage: &commontypes.Image{ImageUrl: beReferralsWidgetV1.GetReferralsLocked().GetMainImageUrl()},
			Description: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: beReferralsWidgetV1.GetReferralsLocked().GetDescription()},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_M},
				FontColor:    beReferralsWidgetV1.GetReferralsLocked().GetDescriptionFontColor(),
			},
			Ctas: ctas,
		}
	}

	// add social proofing if available
	if beReferralsWidgetV1.GetSocialProofingDetails() != nil {
		feReferralsWidgetV1.SocialProofing = &ui.IconTextComponent{
			LeftIcon: &commontypes.Image{
				ImageUrl:  beReferralsWidgetV1.GetSocialProofingDetails().GetImageUrl(),
				ImageType: commontypes.ImageType_PNG,
			},
			Texts: []*commontypes.Text{
				{
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: beReferralsWidgetV1.GetSocialProofingDetails().GetText(),
					},
					FontColor: beReferralsWidgetV1.GetSocialProofingDetails().GetTextColor(),
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS,
					},
				},
			},
			LeftImgTxtPadding: 2,
		}
	}

	return feReferralsWidgetV1, nil
}

func (s *Service) getFeReferralsHomeScreenWidgetV2(ctx context.Context, beReferralsWidgetV2 *beInAppReferralPb.HomeScreenReferralsWidgetDisplayDetailsV2, isReferralsUnlockedForActor bool) *feReferralPb.HomeScreenReferralsWidgetDisplayDetails_ReferralsWidgetV2 {
	if beReferralsWidgetV2 == nil {
		return nil
	}
	if !apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.dyconf.ReferralsV1().HomeWidgetV2FeatureConfig()) {
		return nil
	}

	var widgetBEConfig *beInAppReferralPb.HomeScreenReferralsWidgetDisplayDetailsV2_HomeScreenReferralsWidget
	if isReferralsUnlockedForActor {
		widgetBEConfig = beReferralsWidgetV2.GetReferralsUnlocked()
	} else {
		widgetBEConfig = beReferralsWidgetV2.GetReferralsLocked()
	}

	var visualElement *commontypes.VisualElement
	switch widgetBEConfig.GetVisualElementType() {
	case beInAppReferralEnumPb.VisualElementType_IMAGE:
		visualElement = commontypes.GetVisualElementFromUrlHeightAndWidth(widgetBEConfig.GetVisualElementUrl(), 190, 412)
	case beInAppReferralEnumPb.VisualElementType_LOTTIE:
		visualElement = commontypes.GetVisualElementLottieFromUrl(widgetBEConfig.GetVisualElementUrl()).WithProperties(&commontypes.VisualElementProperties{
			Width:  412,
			Height: 190,
		})
	}

	return &feReferralPb.HomeScreenReferralsWidgetDisplayDetails_ReferralsWidgetV2{
		WidgetVisualElement: visualElement,
		Deeplink:            widgetBEConfig.GetDeeplink(),
	}
}

func (s *Service) getFeCtasFromBeCtas(beCtas []*beInAppReferralPb.CTA, actorFiniteCode string) ([]*feReferralPb.CtaV1, error) {
	var feCtas []*feReferralPb.CtaV1
	for _, beCta := range beCtas {
		feCta := &feReferralPb.CtaV1{
			IconTextComponent: &ui.IconTextComponent{
				RightIcon: &commontypes.Image{ImageUrl: beCta.GetIconUrl()},
				Texts: []*commontypes.Text{
					{
						DisplayValue: &commontypes.Text_PlainString{PlainString: beCta.GetText()},
						FontColor:    beCta.GetFontColor(),
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_S},
					},
				},
			},
			IsVisible: true,
			BgColor:   beCta.GetBgColor(),
			Shadow: &ui.Shadow{
				Height: beCta.GetShadowDetails().GetHeight(),
				Colour: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: beCta.GetShadowDetails().GetColor()}},
			},
		}

		switch beCta.GetAction().(type) {
		case *beInAppReferralPb.CTA_DeeplinkAction:
			feCta.Action = &feReferralPb.CtaV1_DeeplinkAction{DeeplinkAction: &deepLinkPb.Deeplink{Screen: beCta.GetDeeplinkAction().GetScreen()}}
		case *beInAppReferralPb.CTA_ShareFiniteCodeAction:
			feCta.Action = &feReferralPb.CtaV1_ShareFiniteCodeAction{ShareFiniteCodeAction: &feReferralPb.ShareFiniteCodeAction{
				ShareMessage: &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: s.replaceShareCodePlaceHolders(beCta.GetShareFiniteCodeAction().GetShareFiniteCodeMessage(), actorFiniteCode, "", false),
					},
				},
			},
			}
		default:
			return nil, fmt.Errorf("unsupported action encountered while converting BE referrals CTAs to FE referrals CTAs")
		}

		feCtas = append(feCtas, feCta)
	}

	return feCtas, nil
}

func (s *Service) replaceShareCodePlaceHolders(
	message string, finiteCode string,
	generatedReferralLink string, isReferralLinkGenerationEnabledAtClient bool,
) string {
	var appDownloadUrlWithFiniteCode string

	switch {
	// using the already generated referral link
	case generatedReferralLink != "":
		appDownloadUrlWithFiniteCode = generatedReferralLink
	// keeping the placeholder as is for the client to replace it
	case isReferralLinkGenerationEnabledAtClient:
		appDownloadUrlWithFiniteCode = appDownloadUrlWithFiniteCodePlaceholder
	// using the fallback referral URL if generation at client is not possible and there is no available link
	default:
		appDownloadUrlWithFiniteCode = fmt.Sprintf(s.dyconf.Referrals().AppDownloadUrlWithFiniteCode(), finiteCode)
	}

	updatedMessage := message
	updatedMessage = strings.ReplaceAll(updatedMessage, appDownloadUrlWithFiniteCodePlaceholder, appDownloadUrlWithFiniteCode)
	updatedMessage = strings.ReplaceAll(updatedMessage, appDownloadUrlPlaceholder, s.dyconf.Referrals().AppDownloadUrl())
	updatedMessage = strings.ReplaceAll(updatedMessage, finiteCodePlaceHolder, finiteCode)

	return updatedMessage
}

func (s *Service) getFeTncFromBeTnc(beTnc *beInAppReferralPb.TnC) *feReferralPb.TnC {
	var feTncSections []*feReferralPb.TnC_TncSection

	for _, tncSection := range beTnc.GetTncSections() {
		var bulletPoints []*commontypes.Text
		for _, bulletPoint := range tncSection.GetBulletPoints() {
			bulletPoints = append(bulletPoints, &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: bulletPoint}})
		}

		feTncSections = append(feTncSections, &feReferralPb.TnC_TncSection{
			SectionTitle: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: tncSection.GetSectionTitle()}},
			BulletPoints: bulletPoints,
		})
	}

	return &feReferralPb.TnC{
		Title:       &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: beTnc.GetTitle()}},
		SubTitle:    &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: beTnc.GetSubTitle()}},
		Icon:        &commontypes.Image{ImageUrl: beTnc.GetIconUrl()},
		BgColor:     beTnc.GetBgColor(),
		TncSections: feTncSections,
	}
}

func (s *Service) getFeHowToWinWidgetFromBeHowToWinDetails(steps []*beInAppReferralPb.HowToWinWidgetDisplayDetails_HowToWinStep, fontColor string) []*ui.IconTextComponent {
	var feSteps []*ui.IconTextComponent
	for _, step := range steps {
		feSteps = append(feSteps, &ui.IconTextComponent{
			LeftIcon: &commontypes.Image{ImageUrl: step.GetLeftIconUrl()},
			Texts: []*commontypes.Text{
				{
					DisplayValue: &commontypes.Text_PlainString{PlainString: step.GetText()},
					FontColor:    fontColor,
				},
			},
		})
	}

	return feSteps
}

// getReferralsV1ShareActionInfo returns the FE share action info from BE share-script. It populates the following:
// 1. ShareActionType
// 2. Content to Share
// 3. Button UI properties
// Deprecated: in favour of getShareActionInfoV1
// nolint:dupl
func (s *Service) getReferralsV1ShareActionInfo(
	ctx context.Context,
	beShareScriptInfo *beInAppReferralPb.ReferralsV1ShareCodeComponentDetails_ShareScriptInfo,
	finiteCode string, referralLink string, isReferralLinkGenerationEnabledAtClient bool,
) (*feReferralPb.ShareCodeV1Info_ShareActionInfo, error) {
	if beShareScriptInfo == nil {
		return nil, nil
	}

	shareActionInfo := &feReferralPb.ShareCodeV1Info_ShareActionInfo{}
	if feShareActionType, ok := beShareActionTypeToFeMapping[beShareScriptInfo.GetShareActionType()]; ok {
		shareActionInfo.ShareActionType = feShareActionType
	} else {
		return nil, fmt.Errorf("unsupported share action type received: %T", beShareScriptInfo.GetShareActionType())
	}

	shareActionInfo.ShareContent = s.getReferralsV1ShareActionContent(beShareScriptInfo, finiteCode, referralLink, isReferralLinkGenerationEnabledAtClient)
	shareActionInfo.Button = s.getReferralsV1ShareCodeButton(beShareScriptInfo)

	if feIconAnimationType, ok := beIconAnimationTypeToFeIconAnimationTypeMapping[beShareScriptInfo.GetIconAnimationType()]; ok {
		shareActionInfo.IconAnimationType = feIconAnimationType.String()
	} else {
		logger.Error(ctx, "failed to get fe icon animation type mapping", zap.Error(fmt.Errorf("unsupported icon animation type received: %T", beShareScriptInfo.GetIconAnimationType())))
	}

	return shareActionInfo, nil
}

// getReferralsV1ShareActionContent returns the FE share code content from BE share script
// Deprecated: in favour of getShareActionContentV1
// nolint:dupl
func (s *Service) getReferralsV1ShareActionContent(
	beShareScriptInfo *beInAppReferralPb.ReferralsV1ShareCodeComponentDetails_ShareScriptInfo,
	finiteCode string, referralLink string, isReferralLinkGenerationEnabledAtClient bool,
) *feReferralPb.ShareCodeV1Info_ShareActionInfo_ShareContent {
	shareContent := &feReferralPb.ShareCodeV1Info_ShareActionInfo_ShareContent{
		Messages: lo.Map(beShareScriptInfo.GetMessages(), func(msg string, _ int) string {
			return s.replaceShareCodePlaceHolders(msg, finiteCode, referralLink, isReferralLinkGenerationEnabledAtClient)
		}),
		MediaType: beShareCodeMediaTypeToFeMapping[beShareScriptInfo.GetShareMediaType()],
	}

	switch shareContent.GetMediaType() {
	case feReferralPb.ShareCodeV1Info_ShareActionInfo_ShareContent_CUSTOM_IMAGE:
		shareContent.Media = &feReferralPb.ShareCodeV1Info_ShareActionInfo_ShareContent_CustomImage{
			CustomImage: &commontypes.Image{ImageType: commontypes.ImageType_PNG, ImageUrl: beShareScriptInfo.GetCustomImageUrl()},
		}
	default:
		// do nothing
	}

	return shareContent
}

// getReferralsV1ShareCodeButton returns the button component for share-code action. Currently, two types are returned:
// 1. Button with Icon ONLY
// 2. Button with Icon and Name
// Deprecated: in favour of getShareCodeButtonV1
// nolint:dupl
func (s *Service) getReferralsV1ShareCodeButton(beShareScriptInfo *beInAppReferralPb.ReferralsV1ShareCodeComponentDetails_ShareScriptInfo) *ui.IconTextComponent {
	if beShareScriptInfo.GetName() == "" {
		/*
			Note: For button without any name, we are using icon with bg color as a workaround
			since client was not able to render the icon with container properties.
		*/
		return &ui.IconTextComponent{
			LeftIcon: &commontypes.Image{
				ImageType: commontypes.ImageType_PNG, ImageUrl: beShareScriptInfo.GetIconUrl(),
				Width: 48, Height: 48,
			},
			// ContainerProperties: &ui.IconTextComponent_ContainerProperties{
			//	BgColor:      "#00B899",
			//	CornerRadius: 19,
			//	LeftPadding:  8, RightPadding: 8, TopPadding: 8, BottomPadding: 8,
			//	Height: 48, Width: 48,
			// },
		}
	}

	return &ui.IconTextComponent{
		LeftIcon: &commontypes.Image{
			ImageType: commontypes.ImageType_PNG, ImageUrl: beShareScriptInfo.GetIconUrl(),
			Width: 32, Height: 32,
		},
		LeftImgTxtPadding: 10,
		Texts: []*commontypes.Text{
			{
				DisplayValue: &commontypes.Text_PlainString{PlainString: beShareScriptInfo.GetName()},
				FontColor:    "#FFFFFF",
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_M},
			},
		},
		ContainerProperties: &ui.IconTextComponent_ContainerProperties{
			BgColor:      "#00B899",
			CornerRadius: 19,
			LeftPadding:  19, RightPadding: 19, TopPadding: 8, BottomPadding: 8,
			Height: 48,
		},
	}
}

// getShareActionInfoV1 returns the FE share action info from BE share-script. It populates the following:
// 1. ShareActionType
// 2. Content to Share
// 3. Button UI properties
// nolint:dupl
func (s *Service) getShareActionInfoV1(
	ctx context.Context,
	beShareScriptInfo *beInAppReferralPb.ShareScriptInfo, finiteCode string,
	referralLink string, isReferralLinkGenerationEnabledAtClient bool,
) (*feReferralPb.ShareActionInfo, error) {
	if beShareScriptInfo == nil {
		return nil, nil
	}

	shareActionInfo := &feReferralPb.ShareActionInfo{}
	if feShareActionType, ok := beShareActionTypeToFeMapping[beShareScriptInfo.GetShareActionType()]; ok {
		shareActionInfo.ShareActionType = feShareActionType
	} else {
		return nil, fmt.Errorf("unsupported share action type received: %T", beShareScriptInfo.GetShareActionType())
	}

	shareActionInfo.ShareContent = s.getShareActionContentV1(beShareScriptInfo, finiteCode, referralLink, isReferralLinkGenerationEnabledAtClient)
	shareActionInfo.Button = s.getShareCodeButtonV1(beShareScriptInfo)

	if feIconAnimationType, ok := beIconAnimationTypeToFeIconAnimationTypeMapping[beShareScriptInfo.GetIconAnimationType()]; ok {
		shareActionInfo.IconAnimationType = feIconAnimationType.String()
	} else {
		logger.Error(ctx, "failed to get fe icon animation type mapping", zap.Error(fmt.Errorf("unsupported icon animation type received: %T", beShareScriptInfo.GetIconAnimationType())))
	}

	return shareActionInfo, nil
}

// getShareActionContentV1 returns the FE share code content from BE share script
// nolint:dupl
func (s *Service) getShareActionContentV1(
	beShareScriptInfo *beInAppReferralPb.ShareScriptInfo, finiteCode string,
	referralLink string, isReferralLinkGenerationEnabledAtClient bool,
) *feReferralPb.ShareActionInfo_ShareContent {
	shareContent := &feReferralPb.ShareActionInfo_ShareContent{
		Messages: lo.Map(beShareScriptInfo.GetMessages(), func(msg string, _ int) string {
			return s.replaceShareCodePlaceHolders(msg, finiteCode, referralLink, isReferralLinkGenerationEnabledAtClient)
		}),
		MediaType: beShareCodeMediaTypeToFeMappingV2[beShareScriptInfo.GetShareMediaType()],
	}

	switch shareContent.GetMediaType() {
	case feReferralPb.MediaType_CUSTOM_IMAGE:
		shareContent.Media = &feReferralPb.ShareActionInfo_ShareContent_CustomImage{
			CustomImage: &commontypes.Image{ImageType: commontypes.ImageType_PNG, ImageUrl: beShareScriptInfo.GetCustomImageUrl()},
		}
	default:
		// do nothing
	}

	return shareContent
}

// getShareCodeButtonV1 returns the button component for share-code action. Currently, two types are returned:
// 1. Button with Icon ONLY
// 2. Button with Icon and Name
// nolint:dupl
func (s *Service) getShareCodeButtonV1(beShareScriptInfo *beInAppReferralPb.ShareScriptInfo) *ui.IconTextComponent {
	if beShareScriptInfo.GetName() == "" {
		/*
			Note: For button without any name, we are using icon with bg color as a workaround
			since client was not able to render the icon with container properties.
		*/
		return &ui.IconTextComponent{
			LeftIcon: &commontypes.Image{
				ImageType: commontypes.ImageType_PNG, ImageUrl: beShareScriptInfo.GetIconUrl(),
				Width: 48, Height: 48,
			},
			// ContainerProperties: &ui.IconTextComponent_ContainerProperties{
			//	BgColor:      "#00B899",
			//	CornerRadius: 19,
			//	LeftPadding:  8, RightPadding: 8, TopPadding: 8, BottomPadding: 8,
			//	Height: 48, Width: 48,
			// },
		}
	}

	return &ui.IconTextComponent{
		LeftIcon: &commontypes.Image{
			ImageType: commontypes.ImageType_PNG, ImageUrl: beShareScriptInfo.GetIconUrl(),
			Width: 32, Height: 32,
		},
		LeftImgTxtPadding: 10,
		Texts: []*commontypes.Text{
			{
				DisplayValue: &commontypes.Text_PlainString{PlainString: beShareScriptInfo.GetName()},
				FontColor:    "#FFFFFF",
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_M},
			},
		},
		ContainerProperties: &ui.IconTextComponent_ContainerProperties{
			BgColor:      "#00B899",
			CornerRadius: 19,
			LeftPadding:  19, RightPadding: 19, TopPadding: 8, BottomPadding: 8,
			Height: 48,
		},
	}
}

// getRefereeRemindScriptShareAction returns the remind-script to be shared by the referrer to the referee for completing
// the remaining stage(s).
func (s *Service) getRefereeRemindScriptShareAction(refereeRemindStage RefereeRemindStage) *feReferralPb.ShareActionInfo {
	// if empty remind stage, return
	if refereeRemindStage == "" {
		return nil
	}

	remindScriptKey, ok := refereeRemindStageToRemindScriptKeyMap[refereeRemindStage]
	// if reminder-script-key doesn't exist for the particular remind-stage, return
	if !ok {
		return nil
	}

	script := s.dyconf.ReferralsV1().RefereeStageRemindScripts().Get(remindScriptKey)
	// if no script is found for the given remind key, return
	if script == "" {
		return nil
	}

	return &feReferralPb.ShareActionInfo{
		ShareActionType: feReferralPb.ShareActionType_WHATSAPP_NO_CONTACT,
		Button: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: "Remind",
					},
					FontColor: "#00B899",
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_BUTTON_S,
					},
				},
			},
		},
		ShareContent: &feReferralPb.ShareActionInfo_ShareContent{
			Messages: []string{script},
		},
	}
}

// getRewardV1FromBeReward returns the FE RewardV1 from BE Reward
func (s *Service) getRewardV1FromBeReward(ctx context.Context, beReward *beRewardsPb.Reward) *feReferralPb.RewardV1 {
	if beReward == nil {
		return nil
	}

	switch beReward.GetStatus() {
	case beRewardsPb.RewardStatus_PROCESSED:
		return &feReferralPb.RewardV1{
			Icon: s.getRewardV1IconImage(beReward.GetChosenReward().GetRewardType()),
			Amount: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{
					PlainString: s.getRewardV1AmountFromChosenOption(beReward.GetChosenReward()),
				},
				FontColor: "#313234",
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
				},
			},
			RewardStatus: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{
					PlainString: "Earned",
				},
				FontColor: "#878A8D",
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_NUMBER_XS,
				},
			},
		}
	case beRewardsPb.RewardStatus_CREATED:
		return nil
	case beRewardsPb.RewardStatus_PROCESSING_PENDING:
		return &feReferralPb.RewardV1{
			Icon: s.getRewardV1IconImage(beReward.GetChosenReward().GetRewardType()),
			Amount: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{
					PlainString: s.getRewardV1AmountFromChosenOption(beReward.GetChosenReward()),
				},
				FontColor: "#B2B5B9",
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
				},
			},
			RewardStatus: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{
					PlainString: "Processing",
				},
				FontColor: "#878A8D",
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_NUMBER_XS,
				},
			},
			ComponentUiState: feReferralPb.ComponentUiState_FADED,
		}
	default:
		return nil
	}
}

// getRewardV1IconImage returns the RewardV1 icon image based on the RewardType
func (s *Service) getRewardV1IconImage(rewardType beRewardsPb.RewardType) *commontypes.Image {
	iconUrl, ok := s.dyconf.ReferralsV1().RewardTypeToRewardV1IconMap().Load(rewardType.String())
	if !ok || iconUrl == "" {
		return nil
	}

	return &commontypes.Image{ImageType: commontypes.ImageType_PNG, ImageUrl: iconUrl}
}

// getRewardV1AmountFromChosenOption returns the amount in formatted manner for RewardV1 from BE reward chosen-option
func (s *Service) getRewardV1AmountFromChosenOption(chosenOption *beRewardsPb.RewardOption) string {
	switch chosenOption.GetRewardType() {
	case beRewardsPb.RewardType_FI_COINS:
		return money.ToDisplayStringInIndianFormat(&moneyPb.Money{Units: int64(chosenOption.GetFiCoins().GetUnits())}, 0, false)
	case beRewardsPb.RewardType_CASH:
		return money.ToDisplayStringInIndianFormat(&moneyPb.Money{CurrencyCode: chosenOption.GetCash().GetAmount().GetCurrencyCode(), Units: chosenOption.GetCash().GetAmount().GetUnits()}, 0, true)
	case beRewardsPb.RewardType_SMART_DEPOSIT:
		return money.ToDisplayStringInIndianFormat(&moneyPb.Money{Units: chosenOption.GetSmartDeposit().GetAmount().GetUnits()}, 0, true)
	default:
		// todo: return error?
		return ""
	}
}

func (s *Service) getReferralsV1FeContacts(beContacts []*userContactPb.SyncContactDetailsResponse_ContactDetails) []*feReferralPb.InviteContactsInfoResponse_ContactsInfo_ContactDetails {
	var feContacts []*feReferralPb.InviteContactsInfoResponse_ContactsInfo_ContactDetails

	for _, beContact := range beContacts {
		feContacts = append(feContacts, &feReferralPb.InviteContactsInfoResponse_ContactsInfo_ContactDetails{
			PhoneNumberHash: beContact.GetPhoneNumberHash(),
			IconUrl:         beContact.GetIconUrl(),
		})
	}

	return feContacts
}

// getReferralLinkFromBe extracts the unique referral link from BE proto. It also checks whether the link has expired or not.
func (s *Service) getReferralLinkFromBe(ctx context.Context, beReferralInviteLinkInfo *beInAppReferralPb.ReferralInviteLinkInfo) (referralLink string) {
	if beReferralInviteLinkInfo == nil {
		return ""
	}

	currentTime := time.Now()
	linkGeneratedAt := beReferralInviteLinkInfo.GetGeneratedAt()
	linkValidTill := linkGeneratedAt.AsTime().Add(s.dyconf.ReferralsV1().ReferralLinkGenerationInfoConf().LinkValidForSinceGeneration())

	// todo: add a buffer so that link isn't immediately invalid after returning from here
	if linkValidTill.Before(currentTime) {
		logger.Debug(ctx, "referral link from BE isn't valid anymore", zap.String("referralLink", beReferralInviteLinkInfo.GetLink()),
			zap.Time("linkGeneratedAt", beReferralInviteLinkInfo.GetGeneratedAt().AsTime()),
		)
		return ""
	}

	return beReferralInviteLinkInfo.GetLink()
}

// replaceReferralLinkUrlGenerationParamPlaceholders replaces the placeholders in param values which will be used for
// generation of unique referral link at client side.
func (s *Service) replaceReferralLinkUrlGenerationParamPlaceholders(paramValue string, finiteCode string) (replacedParamValue string) {
	replacedParamValue = paramValue
	replacedParamValue = strings.ReplaceAll(replacedParamValue, finiteCodePlaceHolder, finiteCode)

	return replacedParamValue
}

func getTypesTextFromBETextWithFallback(beText *beInAppReferralPb.Text, fontStyle commontypes.FontStyle, fallbackText, fallbackFontColor string) *commontypes.Text {
	var (
		text      = fallbackText
		fontColor = fallbackFontColor
	)

	if beText.GetText() != "" {
		text = beText.GetText()
	}
	if beText.GetFontColor() != "" {
		fontColor = beText.GetFontColor()
	}

	return commontypes.GetTextFromStringFontColourFontStyle(text, fontColor, fontStyle)
}

func getStringWithFallback(str, fallback string) string {
	if str != "" {
		return str
	}

	return fallback
}
