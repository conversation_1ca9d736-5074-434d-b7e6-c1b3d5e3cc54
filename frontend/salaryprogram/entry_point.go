package salaryprogram

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/gamma/pkg/accrual"

	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/syncmap"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	fePb "github.com/epifi/gamma/api/frontend/salaryprogram"
	beSalaryPb "github.com/epifi/gamma/api/salaryprogram"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/config/genconf"
	pkgDeeplink "github.com/epifi/gamma/pkg/deeplink"
)

type userSalaryStates struct {
	isUserFullSalaryProgramActive bool
	isUserSalaryLiteProgramActive bool
	hasUserCompletedSalaryLiteReg bool
	userSalaryActiveFrom          *timestampPb.Timestamp
}

// GetSalaryProgramEntryPointCard fetches the entry point card data to be shown on the app homescreen and profile section.
// nolint:funlen,dupl
func (s *Service) GetSalaryProgramEntryPointCard(ctx context.Context, req *fePb.GetSalaryProgramEntryPointCardRequest) (*fePb.GetSalaryProgramEntryPointCardResponse, error) {
	var (
		actorId    = req.GetReq().GetAuth().GetActorId()
		employerId string
	)

	// fetch current employer of the actor to decide the home banner config to load
	employerInfo, err := s.getCurrentEmployerOfActor(ctx, actorId)
	if err != nil {
		logger.Debug(ctx, "error fetching current employer of the actor. silently ignoring it for graceful handling", zap.Error(err))
		// note: silently ignoring the error irrespective of the error type for graceful handling
	} else {
		if employerInfo.GetIsVerifiedEmployer() {
			employerId = employerInfo.GetId()
		}
	}

	// fetch home card config
	homeCardConf := s.getHomeCardConfig(ctx, employerId)

	// if home screen salary program card is not to be shown
	if !homeCardConf.ShowCard() {
		return &fePb.GetSalaryProgramEntryPointCardResponse{
			ShowCard:   false,
			RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
		}, nil
	}

	// populate default display details of the home screen card
	cardDisplayInfo := &fePb.CardDisplayInfo{
		Title:    &fePb.Text{Text: accrual.ReplaceCoinWithPointIfApplicable(homeCardConf.BenefitsInactiveTitle(), nil), FontColor: "#333333"},
		ImageUrl: homeCardConf.BenefitsInactiveImageUrl(),
		BgColor:  homeCardConf.BenefitsInactiveBgColor(),
	}
	/*
		The assumption regarding tags here is that we will always have only ONE tag for the card live at any given time.
		Also, the priority is to quickly update the tag dynamically over having multi-tag support.
	*/
	// if INACTIVE tag is available, set it for the home card
	if homeCardConf.BenefitsInactiveTag().Tag() != "" {
		cardDisplayInfo.Tags = []*fePb.Text{
			{
				Text:      homeCardConf.BenefitsInactiveTag().Tag(),
				FontColor: homeCardConf.BenefitsInactiveTag().TextColor(),
				BgColor:   homeCardConf.BenefitsInactiveTag().BgColor(),
			},
		}
	}

	// populate CTA details
	cardDisplayInfo.Cta = &fePb.CTA{
		Text: &fePb.Text{
			Text:      homeCardConf.BenefitsInactiveCta().Text(),
			FontColor: homeCardConf.BenefitsInactiveCta().TextColor(),
			BgColor:   homeCardConf.BenefitsInactiveCta().BgColor(),
		},
		Action:    &fePb.CTA_DeeplinkAction{DeeplinkAction: &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_SALARY_PROGRAM_INTRO_SCREEN}}, // by default take the user to salary intro page
		IsVisible: homeCardConf.BenefitsInactiveCta().IsVisible(),
	}

	// get salary program registration status for the actor
	registrationStatusRes, err := s.salaryProgramClient.GetCurrentRegStatusAndNextRegStage(ctx, &beSalaryPb.CurrentRegStatusAndNextRegStageRequest{
		ActorId:  actorId,
		FlowType: beSalaryPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE,
	})
	if err != nil || !registrationStatusRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "error while fetching salary registration status for actor", zap.Error(err),
			zap.Any(logger.RPC_STATUS, registrationStatusRes.GetStatus()),
		)
		return &fePb.GetSalaryProgramEntryPointCardResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error fetching salary reg status for actor")},
		}, nil
	}

	// if salary program registration is complete for the user, we check for the salary benefits activation status
	if registrationStatusRes.GetRegistrationStatus() == beSalaryPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED {
		cardDisplayInfo.Cta.Action = &fePb.CTA_DeeplinkAction{DeeplinkAction: &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN}} // take the user to salary benefits page

		salaryPgActivationRes, activationErr := s.salaryProgramClient.GetLatestActivationDetailsActiveAtTime(ctx, &beSalaryPb.LatestActivationDetailsActiveAtTimeRequest{
			RegistrationId: registrationStatusRes.GetRegistrationId(),
			ActiveAtTime:   timestampPb.Now(),
			// activation_kind is unspecified as latest activation can be used to determine whether an active salary program exists for the user.
			ActivationKind: beSalaryPb.SalaryProgramActivationKind_SALARY_PROGRAM_ACTIVATION_KIND_UNSPECIFIED,
		})
		// activation record not found is not an error scenario. It means that actor is yet to verify the salary txn to activate the benefits
		if activationErr != nil || (!salaryPgActivationRes.GetStatus().IsSuccess() && !salaryPgActivationRes.GetStatus().IsRecordNotFound()) {
			logger.Error(ctx, "error while fetching salary benefits activation details for actor", zap.Error(activationErr),
				zap.Any(logger.RPC_STATUS, salaryPgActivationRes.GetStatus()), zap.String(logger.REGISTRATION_ID, registrationStatusRes.GetRegistrationId()),
			)
			return &fePb.GetSalaryProgramEntryPointCardResponse{
				RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error fetching salary benefits activation details")},
			}, nil
		}

		// if salary benefits are active for the user, then change the CTA and display details
		if !salaryPgActivationRes.GetStatus().IsRecordNotFound() {
			cardDisplayInfo.ImageUrl = homeCardConf.BenefitsActiveImageUrl()
			cardDisplayInfo.Title = &fePb.Text{Text: accrual.ReplaceCoinWithPointIfApplicable(homeCardConf.BenefitsActiveTitle(), nil), FontColor: "#333333"}
			cardDisplayInfo.Cta.Text = &fePb.Text{
				Text:      homeCardConf.BenefitsActiveCta().Text(),
				FontColor: homeCardConf.BenefitsActiveCta().TextColor(),
				BgColor:   homeCardConf.BenefitsActiveCta().BgColor(),
			}
			cardDisplayInfo.Cta.IsVisible = homeCardConf.BenefitsActiveCta().IsVisible()
			cardDisplayInfo.BgColor = homeCardConf.BenefitsActiveBgColor()
			// if ACTIVE tag is available, set it for the home card. But first, clear any existing tag set
			cardDisplayInfo.Tags = []*fePb.Text{}
			if homeCardConf.BenefitsActiveTag().Tag() != "" {
				cardDisplayInfo.Tags = []*fePb.Text{
					{
						Text:      homeCardConf.BenefitsActiveTag().Tag(),
						FontColor: homeCardConf.BenefitsActiveTag().TextColor(),
						BgColor:   homeCardConf.BenefitsActiveTag().BgColor(),
					},
				}
			}
		} else {
			// if registration is complete but salary benefits are not active for the user
			cardDisplayInfo.ImageUrl = homeCardConf.RegCompleteBenefitsInactiveImageUrl()
			cardDisplayInfo.Title = &fePb.Text{Text: accrual.ReplaceCoinWithPointIfApplicable(homeCardConf.RegCompleteBenefitsInactiveTitle(), nil), FontColor: "#333333"}
			cardDisplayInfo.Cta.Text = &fePb.Text{
				Text:      homeCardConf.RegCompleteBenefitsInactiveCta().Text(),
				FontColor: homeCardConf.RegCompleteBenefitsInactiveCta().TextColor(),
				BgColor:   homeCardConf.RegCompleteBenefitsInactiveCta().BgColor(),
			}
			cardDisplayInfo.Cta.IsVisible = homeCardConf.RegCompleteBenefitsInactiveCta().IsVisible()
			cardDisplayInfo.BgColor = homeCardConf.RegCompleteBenefitsInactiveBgColor()
			// if INACTIVE-REG-COMPLETE tag is available, set it for the home card. But first, clear any existing tag set
			cardDisplayInfo.Tags = []*fePb.Text{}
			if homeCardConf.RegCompleteBenefitsInactiveTag().Tag() != "" {
				cardDisplayInfo.Tags = []*fePb.Text{
					{
						Text:      homeCardConf.RegCompleteBenefitsInactiveTag().Tag(),
						FontColor: homeCardConf.RegCompleteBenefitsInactiveTag().TextColor(),
						BgColor:   homeCardConf.RegCompleteBenefitsInactiveTag().BgColor(),
					},
				}
			}
		}
	}

	return &fePb.GetSalaryProgramEntryPointCardResponse{
		ShowCard:    true,
		HeaderTitle: &fePb.Text{Text: homeCardConf.HeaderTitle(), FontColor: "#282828"},
		DisplayInfo: cardDisplayInfo,
		RespHeader:  &header.ResponseHeader{Status: rpc.StatusOk()},
	}, nil
}

// getHomeCardConfig returns the home-card config which is applicable currently.
// It goes via the time-bound (B2C and B2B) configs and returns the currently active one.
// If none are found within the time-bound, the default config (B2C) is returned.
// nolint:funlen
func (s *Service) getHomeCardConfig(ctx context.Context, employerId string) *genconf.SalaryProgramHomeCard {
	var (
		// default home card config
		homeCardConfig = s.dyconf.SalaryProgram().HomeCard()
		currentTime    = time.Now()
		// flag to check if the user's employer was onboarded to salary program via B2B route
		isEmployerB2BOnboarded = false
		// flag to check if time bound B2B config is available or not
		isB2BTimeBoundConfigAvailable = false
	)

	// check if user's current employer is onboarded via B2B route or not for the salary program
	s.dyconf.SalaryProgram().B2BEmployersMap().Range(func(empId string, _ string) bool {
		if empId == employerId {
			isEmployerB2BOnboarded = true
			return false
		}

		return true
	})

	// if employer is onboarded via B2B route for the salary program, then check for the B2B time-bound configs
	if isEmployerB2BOnboarded {
		// iterating over time-bound B2B home-card configs to extract the one that is live currently
		s.dyconf.SalaryProgram().HomeCardsB2BTimeBound().Range(func(_ string, value *genconf.SalaryProgramHomeCard) bool {
			activeFromString := value.ActiveFrom()
			activeTillString := value.ActiveTill()

			activeFrom, err := time.Parse(time.RFC3339, activeFromString)
			if err != nil {
				logger.Error(ctx, "error parsing activeFromString from time-bound B2B home-card config", zap.Error(err),
					zap.String("activeFromString", activeFromString),
				)

				// returning true to continue iterating over the range
				return true
			}
			activeTill, err := time.Parse(time.RFC3339, activeTillString)
			if err != nil {
				logger.Error(ctx, "error parsing activeTillString from time-bound B2B home-card config", zap.Error(err),
					zap.String("activeTillString", activeTillString),
				)

				// returning true to continue iterating over the range
				return true
			}

			// activeFrom <= currentTime < activeTill
			if (activeFrom.Before(currentTime) || activeFrom.Equal(currentTime)) && currentTime.Before(activeTill) {
				homeCardConfig = value
				isB2BTimeBoundConfigAvailable = true
				return false
			}

			return true
		})
	}

	// returning the homeCardConfig found from the B2B map
	if isB2BTimeBoundConfigAvailable {
		return homeCardConfig
	}

	// iterating over time-bound B2C home-card configs to extract the one that is live currently
	s.dyconf.SalaryProgram().HomeCardsTimeBound().Range(func(_ string, value *genconf.SalaryProgramHomeCard) bool {
		activeFromString := value.ActiveFrom()
		activeTillString := value.ActiveTill()

		activeFrom, err := time.Parse(time.RFC3339, activeFromString)
		if err != nil {
			logger.Error(ctx, "error parsing activeFromString from time-bound home-card config", zap.Error(err),
				zap.String("activeFromString", activeFromString),
			)

			// returning true to continue iterating over the range
			return true
		}
		activeTill, err := time.Parse(time.RFC3339, activeTillString)
		if err != nil {
			logger.Error(ctx, "error parsing activeTillString from time-bound home-card config", zap.Error(err),
				zap.String("activeTillString", activeTillString),
			)

			// returning true to continue iterating over the range
			return true
		}

		// activeFrom <= currentTime < activeTill
		if (activeFrom.Before(currentTime) || activeFrom.Equal(currentTime)) && currentTime.Before(activeTill) {
			homeCardConfig = value
			return false
		}

		return true
	})

	return homeCardConfig
}

// GetSalaryProgramEntryPointSection returns the details needed to render the salary program entrypoint section on the homepage/profile page.
// nolint: funlen
func (s *Service) GetSalaryProgramEntryPointSection(ctx context.Context, req *fePb.SalaryProgramEntryPointSectionRequest) (*fePb.SalaryProgramEntryPointSectionResponse, error) {
	var (
		actorId      = req.GetReq().GetAuth().GetActorId()
		employerId   string
		salaryStates *userSalaryStates
	)

	// fetch current employer of the actor to decide the entry point section config to load
	employerInfo, err := s.getCurrentEmployerOfActor(ctx, actorId)
	if err != nil {
		logger.Debug(ctx, "error fetching current employer of the actor. silently ignoring it for graceful handling", zap.Error(err))
		// note: silently ignoring the error irrespective of the error type for graceful handling
	} else {
		if employerInfo.GetIsVerifiedEmployer() {
			employerId = employerInfo.GetId()
		}
	}

	// fetch the entry point section config
	entryPointSectionInfo := s.getEntryPointSectionConfig(ctx, employerId)

	// check if entrypoint section should be displayed or not.
	if !entryPointSectionInfo.IsVisible() {
		return &fePb.SalaryProgramEntryPointSectionResponse{
			RespHeader:  &header.ResponseHeader{Status: rpc.StatusOk()},
			ShowSection: false,
		}, nil
	}

	// get salary program registration status for the actor
	registrationStatusRes, err := s.salaryProgramClient.GetCurrentRegStatusAndNextRegStage(ctx, &beSalaryPb.CurrentRegStatusAndNextRegStageRequest{
		ActorId:  actorId,
		FlowType: beSalaryPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE,
	})
	if err != nil || !registrationStatusRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "error while fetching salary registration status for actor", zap.Any(logger.RPC_STATUS, registrationStatusRes.GetStatus()), zap.Error(err))
		return &fePb.SalaryProgramEntryPointSectionResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error fetching salary reg status for actor")},
		}, nil
	}
	registrationStatus := registrationStatusRes.GetRegistrationStatus()

	// get activation status if user has completed registration.
	if registrationStatus == beSalaryPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED {
		salaryStates, err = s.getUserSalaryStates(ctx, actorId, registrationStatusRes.GetRegistrationId())
		if err != nil {
			logger.Error(ctx, "error while fetch user salary states", zap.Error(err), zap.String(logger.REGISTRATION_ID, registrationStatusRes.GetRegistrationId()))
			return &fePb.SalaryProgramEntryPointSectionResponse{
				RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error while fetch user salary states")},
			}, nil
		}
	}

	var (
		summaryTileCfg, statusTileCfg, statusTileCampaignCfg, promoBannerTileCfg *genconf.SalaryProgramEntryPointSectionTileInfo
		feEntryPointSectionTileInfos                                             []*fePb.SalaryProgramEntryPointSectionResponse_TileInfo
	)

	// get entrypoint section tile configs based on registration and activation status
	switch {
	case registrationStatus != beSalaryPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED:
		summaryTileCfg = entryPointSectionInfo.RegNotCompletedSummaryTileInfo()
		statusTileCfg = entryPointSectionInfo.RegNotCompletedStatusTileInfo()
		promoBannerTileCfg = entryPointSectionInfo.RegNotCompletedPromoBannerTileInfo()

	case salaryStates.isUserFullSalaryProgramActive:
		summaryTileCfg = entryPointSectionInfo.BenefitsActiveSummaryTileInfo()
		statusTileCfg = entryPointSectionInfo.BenefitsActiveStatusTileInfo()
		promoBannerTileCfg = entryPointSectionInfo.BenefitsActivePromoBannerTileInfo()

	case s.isUserInSalaryLiteGracePeriod(salaryStates.isUserSalaryLiteProgramActive, salaryStates.userSalaryActiveFrom):
		summaryTileCfg = entryPointSectionInfo.SalaryLiteBenefitsActiveInGracePeriodSummaryTileInfo()
		promoBannerTileCfg = entryPointSectionInfo.SalaryLiteBenefitsActiveInGracePeriodPromoBannerTileInfo()

	case salaryStates.isUserSalaryLiteProgramActive:
		summaryTileCfg = entryPointSectionInfo.SalaryLiteBenefitsActiveSummaryTileInfo()
		promoBannerTileCfg = entryPointSectionInfo.SalaryLiteBenefitsActivePromoBannerTileInfo()

	case salaryStates.hasUserCompletedSalaryLiteReg:
		summaryTileCfg = entryPointSectionInfo.SalaryLiteRegCompletedSummaryTileInfo()
		promoBannerTileCfg = entryPointSectionInfo.SalaryLiteRegCompletedPromoBannerTileInfo()

	// if user has completed reg but is not active.
	default:
		summaryTileCfg = entryPointSectionInfo.RegCompletedBenefitsInActiveSummaryTileInfo()
		statusTileCfg = entryPointSectionInfo.RegCompletedBenefitsInActiveStatusTileInfo()
		statusTileCampaignCfg = entryPointSectionInfo.RegCompletedBenefitsInActiveStatusTileCampaignInfo()
		promoBannerTileCfg = entryPointSectionInfo.RegCompletedBenefitsInActivePromoBannerTileInfo()
	}
	isNewHomeUiRevamp := cfg.IsFeatureEnabledOnPlatform(ctx, s.dyconf.HomeRevampParams().HomeLayoutUIRevamp())
	// get summary tile
	feSummaryTileInfo, err := createSummaryTileInfoUsingTileCfg(ctx, req.GetEntryPoint(), summaryTileCfg, isNewHomeUiRevamp)
	if err != nil {
		logger.Error(ctx, "error creating summary tile from tile config", zap.Error(err))
		return &fePb.SalaryProgramEntryPointSectionResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error creating summary tile from tile config")},
		}, nil
	}
	if feSummaryTileInfo != nil {
		feEntryPointSectionTileInfos = append(feEntryPointSectionTileInfos, feSummaryTileInfo)
	}

	// get status tile
	feStatusTileInfo, err := s.createStatusTileInfoUsingTileCfg(ctx, req.GetEntryPoint(), statusTileCfg, statusTileCampaignCfg)
	if err != nil {
		logger.Error(ctx, "error creating status tile from tile config", zap.Error(err))
		return &fePb.SalaryProgramEntryPointSectionResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error creating status tile from tile config")},
		}, nil
	}
	if feStatusTileInfo != nil {
		feEntryPointSectionTileInfos = append(feEntryPointSectionTileInfos, feStatusTileInfo)
	}

	// get promo-banner tile
	fePromoBannerTileInfo, err := createPromoBannerTileInfoUsingTileCfg(ctx, req.GetEntryPoint(), promoBannerTileCfg)
	if err != nil {
		logger.Error(ctx, "error creating promo-banner tile from tile config", zap.Error(err))
		return &fePb.SalaryProgramEntryPointSectionResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error creating promo-banner tile from tile config")},
		}, nil
	}
	if fePromoBannerTileInfo != nil {
		feEntryPointSectionTileInfos = append(feEntryPointSectionTileInfos, fePromoBannerTileInfo)
	}

	return &fePb.SalaryProgramEntryPointSectionResponse{
		RespHeader:  &header.ResponseHeader{Status: rpc.StatusOk()},
		ShowSection: true,
		TileInfos:   feEntryPointSectionTileInfos,
	}, nil
}

func (s *Service) isUserInSalaryLiteGracePeriod(isUserSalaryLiteProgramActive bool, userSalaryActiveFrom *timestampPb.Timestamp) bool {
	return isUserSalaryLiteProgramActive && time.Since(userSalaryActiveFrom.AsTime()) > s.conf.SalaryProgram.SalaryLiteConfig.MinReqDurationSinceLastActivationForShowingGracePeriod
}

func (s *Service) getUserSalaryStates(ctx context.Context, actorId, salaryRegId string) (*userSalaryStates, error) {
	salaryStates := &userSalaryStates{}
	errGroup, gctx := errgroup.WithContext(ctx)
	errGroup.Go(func() error {
		salaryPgActivationRes, activationErr := s.salaryProgramClient.GetLatestActivationDetailsActiveAtTime(gctx, &beSalaryPb.LatestActivationDetailsActiveAtTimeRequest{
			RegistrationId: salaryRegId,
			ActiveAtTime:   timestampPb.Now(),
		})
		// activation record not found is not an error scenario. It means that actor is yet to verify the salary txn to activate the benefits
		if activationErr != nil || (!salaryPgActivationRes.GetStatus().IsSuccess() && !salaryPgActivationRes.GetStatus().IsRecordNotFound()) {
			logger.Error(gctx, "error while fetching salary benefits activation details for actor", zap.Any(logger.RPC_STATUS, salaryPgActivationRes.GetStatus()), zap.String(logger.REGISTRATION_ID, salaryRegId), zap.Error(activationErr))
			return fmt.Errorf("error fetching salary benefits activation details")
		}
		if !salaryPgActivationRes.GetStatus().IsRecordNotFound() {
			salaryStates.userSalaryActiveFrom = salaryPgActivationRes.GetActiveFrom()
			if salaryPgActivationRes.GetActivationType() == beSalaryPb.SalaryActivationType_FULL_SALARY_ACTIVATION {
				salaryStates.isUserFullSalaryProgramActive = true
			} else {
				salaryStates.isUserSalaryLiteProgramActive = true
			}
		}
		return nil
	})
	errGroup.Go(func() error {
		getMandateReqsRes, getMandateReqsErr := s.salaryProgramClient.GetSalaryLiteMandateRequests(gctx, &beSalaryPb.GetSalaryLiteMandateRequestsRequest{
			Filters: &beSalaryPb.GetSalaryLiteMandateRequestsRequest_Filters{
				ActorId: actorId,
			},
			PageContextRequest: &rpc.PageContextRequest{
				PageSize: 1,
			},
		})
		if getMandateReqsErr != nil || (!getMandateReqsRes.GetStatus().IsSuccess() && !getMandateReqsRes.GetStatus().IsRecordNotFound()) {
			logger.Error(gctx, "salaryProgramClient.GetSalaryLiteMandateRequests call failed", zap.Any(logger.RPC_STATUS, getMandateReqsRes.GetStatus()), zap.String(logger.REGISTRATION_ID, salaryRegId), zap.Error(getMandateReqsErr))
			return fmt.Errorf("salaryProgramClient.GetSalaryLiteMandateRequests call failed")
		}
		if !getMandateReqsRes.GetStatus().IsRecordNotFound() && getMandateReqsRes.GetSalaryLiteMandateRequests()[0].GetRequestStatus() == beSalaryPb.SalaryLiteMandateRequestStatus_SUCCESS {
			salaryStates.hasUserCompletedSalaryLiteReg = true
		}
		return nil
	})
	if errGrpErr := errGroup.Wait(); errGrpErr != nil {
		return nil, errGrpErr
	}
	return salaryStates, nil
}

// nolint: dupl
func createSummaryTileInfoUsingTileCfg(ctx context.Context, entryPoint fePb.SalaryProgramEntryPointSectionRequest_EntryPoint, tileCfg *genconf.SalaryProgramEntryPointSectionTileInfo, isNewUiEnabled bool) (*fePb.SalaryProgramEntryPointSectionResponse_TileInfo, error) {
	if !tileCfg.IsVisible() {
		return nil, nil
	}
	summaryTileInfo := &fePb.SalaryProgramEntryPointSectionResponse_SummaryTileInfo{
		Title:    &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: tileCfg.Title()}, FontColor: tileCfg.TitleColor()},
		BgColor:  tileCfg.BgColor(),
		ImageUrl: tileCfg.ImageUrl(),
	}

	appPlatform, appVersion := epificontext.AppPlatformAndVersion(ctx)
	ctaConfig := getAppVersionedSalaryProgramCta(ctx, tileCfg.Cta(), tileCfg.AppVersionedCtas(), appPlatform, appVersion)
	// add cta if configured
	if ctaConfig.Deeplink() != nil {
		deeplink, err := pkgDeeplink.NewDeeplinkFromV2Config(ctaConfig.Deeplink())
		if err != nil {
			return nil, fmt.Errorf("error parsing deeplink from config: %w", err)
		}
		summaryTileInfo.Cta = &fePb.CTA{
			IsVisible: true,
			Text:      &fePb.Text{Text: ctaConfig.Text(), FontColor: ctaConfig.TextColor()},
			ImageUrl:  ctaConfig.ImageUrl(),
			Action:    &fePb.CTA_DeeplinkAction{DeeplinkAction: deeplink},
			BgColor:   ctaConfig.BgColor(),
		}
	}

	// color theme for profile section is a bit different than other sections, sp override some default display attributes.
	if entryPoint == fePb.SalaryProgramEntryPointSectionRequest_ENTRY_POINT_PROFILE_SCREEN {
		summaryTileInfo.Title.FontColor = "#FFFFFF" // Snow
		summaryTileInfo.BgColor = "#333333"         // Night
		if summaryTileInfo.GetCta().GetText() != nil {
			summaryTileInfo.Cta.Text.FontColor = "#00B899" // Forest
		}
		if summaryTileInfo.GetCta().GetImageUrl() != "" {
			summaryTileInfo.Cta.ImageUrl = "https://epifi-icons.pointz.in/salaryprogram/right-chevron-green.png"
		}
	}

	if entryPoint == fePb.SalaryProgramEntryPointSectionRequest_ENTRY_POINT_HOME_SCREEN_V2 {
		if summaryTileInfo.GetTitle().GetFontColor() == "" {
			summaryTileInfo.Title.FontColor = "#FFFFFF"
		}
		if summaryTileInfo.GetCta().GetText() != nil && summaryTileInfo.GetCta().GetText().GetFontColor() == "" {
			summaryTileInfo.Cta.Text.FontColor = "#FFFFFF"
		}
		if summaryTileInfo.GetCta().GetImageUrl() == "" {
			summaryTileInfo.Cta.ImageUrl = "https://epifi-icons.pointz.in/salaryprogram/right-chevron-white.png"
		}
		// set bgcolor for salary CTA to empty for backward compatibility
		// CTA bg color is not supported on older clients
		if !isNewUiEnabled {
			summaryTileInfo.Cta.BgColor = ""
		}
		summaryTileInfo.HeaderTitle = &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: tileCfg.HeaderTitle()},
			FontColor:    tileCfg.HeaderTitleColor(),
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS,
			},
		}
	}

	return &fePb.SalaryProgramEntryPointSectionResponse_TileInfo{
		Data: &fePb.SalaryProgramEntryPointSectionResponse_TileInfo_SummaryTileInfo{
			SummaryTileInfo: summaryTileInfo,
		},
	}, nil
}

// nolint: dupl
func (s *Service) createStatusTileInfoUsingTileCfg(ctx context.Context, entryPoint fePb.SalaryProgramEntryPointSectionRequest_EntryPoint, defaultTileCfg *genconf.SalaryProgramEntryPointSectionTileInfo,
	campaignTileCfg *genconf.SalaryProgramEntryPointSectionTileInfo) (*fePb.SalaryProgramEntryPointSectionResponse_TileInfo, error) {
	if defaultTileCfg == nil || (!defaultTileCfg.IsVisible() && (campaignTileCfg == nil || !campaignTileCfg.IsVisible())) {
		return nil, nil
	}

	daysLeftForCampaignCompletion := 0
	if s.dyconf.SalaryProgram().SalaryProgramSpecialRewardCampaignEndDate() != "" {
		campaignEndDate, err := time.ParseInLocation(time.RFC3339, s.dyconf.SalaryProgram().SalaryProgramSpecialRewardCampaignEndDate(), datetime.IST)
		// intentionally muting the error to gracefully handling config error
		if err != nil {
			logger.Error(ctx, "error parsing rewards campaign date", zap.Error(err))
			return nil, nil
		}
		daysLeftForCampaignCompletion = int(datetime.EndOfDay(campaignEndDate).Sub(datetime.EndOfDay(time.Now().In(datetime.IST))) / (24 * time.Hour))
		if daysLeftForCampaignCompletion < 0 {
			daysLeftForCampaignCompletion = 0
		}
	}

	appPlatform, appVersion := epificontext.AppPlatformAndVersion(ctx)

	// if campaign tile is configured, it's config instead of the default one.
	if campaignTileCfg != nil && campaignTileCfg.IsVisible() {
		tileTitle := strings.ReplaceAll(campaignTileCfg.Title(), "{daysLeftForCampaignCompletion}", strconv.Itoa(daysLeftForCampaignCompletion))
		statusTileInfo := &fePb.SalaryProgramEntryPointSectionResponse_StatusTileInfo{
			Title:    &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: tileTitle}, FontColor: campaignTileCfg.TitleColor()},
			BgColor:  campaignTileCfg.BgColor(),
			ImageUrl: campaignTileCfg.ImageUrl(),
		}

		// todo (himanshu) move this to config once home v2 is rolled out to 100% users
		if entryPoint == fePb.SalaryProgramEntryPointSectionRequest_ENTRY_POINT_HOME_SCREEN_V2 {
			statusTileInfo.Title.FontColor = "#FFFFFF"
			statusTileInfo.BgColor = "#4D333333"
			statusTileInfo.ImageUrl = "https://epifi-icons.pointz.in/salaryprogram/white-timer.png"
		}

		ctaConfig := getAppVersionedSalaryProgramCta(ctx, campaignTileCfg.Cta(), campaignTileCfg.AppVersionedCtas(), appPlatform, appVersion)

		// add cta if configured
		if ctaConfig.Deeplink() != nil {
			deeplink, err := pkgDeeplink.NewDeeplinkFromV2Config(ctaConfig.Deeplink())
			if err != nil {
				return nil, fmt.Errorf("error parsing deeplink from config: %w", err)
			}
			statusTileInfo.Cta = &fePb.CTA{
				IsVisible: true,
				Text:      &fePb.Text{Text: ctaConfig.Text(), FontColor: ctaConfig.TextColor()},
				Action:    &fePb.CTA_DeeplinkAction{DeeplinkAction: deeplink},
			}
		}
		return &fePb.SalaryProgramEntryPointSectionResponse_TileInfo{
			Data: &fePb.SalaryProgramEntryPointSectionResponse_TileInfo_StatusTileInfo{
				StatusTileInfo: statusTileInfo,
			},
		}, nil
	}

	tileTitle := strings.ReplaceAll(defaultTileCfg.Title(), "{daysLeftForCampaignCompletion}", strconv.Itoa(daysLeftForCampaignCompletion))
	statusTileInfo := &fePb.SalaryProgramEntryPointSectionResponse_StatusTileInfo{
		Title:    &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: tileTitle}, FontColor: defaultTileCfg.TitleColor()},
		BgColor:  defaultTileCfg.BgColor(),
		ImageUrl: defaultTileCfg.ImageUrl(),
	}

	// todo (himanshu) move this to config once home v2 is rolled out to 100% users
	if entryPoint == fePb.SalaryProgramEntryPointSectionRequest_ENTRY_POINT_HOME_SCREEN_V2 {
		statusTileInfo.Title.FontColor = "#FFFFFF"
		statusTileInfo.BgColor = "#4D333333"
		statusTileInfo.ImageUrl = "https://epifi-icons.pointz.in/salaryprogram/white-timer.png"
	}

	ctaConfig := getAppVersionedSalaryProgramCta(ctx, defaultTileCfg.Cta(), defaultTileCfg.AppVersionedCtas(), appPlatform, appVersion)

	// add cta if configured
	if ctaConfig.Deeplink() != nil {
		deeplink, err := pkgDeeplink.NewDeeplinkFromV2Config(ctaConfig.Deeplink())
		if err != nil {
			return nil, fmt.Errorf("error parsing deeplink from config: %w", err)
		}
		statusTileInfo.Cta = &fePb.CTA{
			IsVisible: true,
			Text:      &fePb.Text{Text: ctaConfig.Text(), FontColor: ctaConfig.TextColor()},
			Action:    &fePb.CTA_DeeplinkAction{DeeplinkAction: deeplink},
		}
	}
	return &fePb.SalaryProgramEntryPointSectionResponse_TileInfo{
		Data: &fePb.SalaryProgramEntryPointSectionResponse_TileInfo_StatusTileInfo{
			StatusTileInfo: statusTileInfo,
		},
	}, nil
}

func createPromoBannerTileInfoUsingTileCfg(ctx context.Context, entryPoint fePb.SalaryProgramEntryPointSectionRequest_EntryPoint, tileCfg *genconf.SalaryProgramEntryPointSectionTileInfo) (*fePb.SalaryProgramEntryPointSectionResponse_TileInfo, error) {
	if !tileCfg.IsVisible() {
		return nil, nil
	}
	promoBannerTileInfo := &fePb.CardDisplayInfo{
		Title:    &fePb.Text{Text: accrual.ReplaceCoinWithPointIfApplicable(tileCfg.Title(), nil), FontColor: tileCfg.TitleColor()},
		BgColor:  tileCfg.BgColor(),
		ImageUrl: tileCfg.ImageUrl(),
	}

	// for now only single tag can be configured for promo-banner
	if tileCfg.Tag().Tag() != "" {
		promoBannerTileInfo.Tags = append(promoBannerTileInfo.Tags, &fePb.Text{
			Text:      tileCfg.Tag().Tag(),
			FontColor: tileCfg.Tag().TextColor(),
			BgColor:   tileCfg.Tag().BgColor(),
		})
	}

	appPlatform, appVersion := epificontext.AppPlatformAndVersion(ctx)
	ctaConfig := getAppVersionedSalaryProgramCta(ctx, tileCfg.Cta(), tileCfg.AppVersionedCtas(), appPlatform, appVersion)

	// add cta if configured
	if ctaConfig.Deeplink() != nil {
		deeplink, err := pkgDeeplink.NewDeeplinkFromV2Config(ctaConfig.Deeplink())
		if err != nil {
			return nil, fmt.Errorf("error parsing deeplink from config: %w", err)
		}
		promoBannerTileInfo.Cta = &fePb.CTA{
			IsVisible: true,
			Text:      &fePb.Text{Text: ctaConfig.Text(), FontColor: ctaConfig.TextColor()},
			Action:    &fePb.CTA_DeeplinkAction{DeeplinkAction: deeplink},
		}
	}

	if entryPoint == fePb.SalaryProgramEntryPointSectionRequest_ENTRY_POINT_HOME_SCREEN_V2 {
		promoBannerTileInfo.BgColor = "#FFFFFF" // Snow
		promoBannerTileInfo.Shadow = &ui.Shadow{
			Height: 4,
			Colour: &ui.BackgroundColour{
				Colour: &ui.BackgroundColour_BlockColour{
					BlockColour: "#1A000000",
				},
			},
		}
	}

	return &fePb.SalaryProgramEntryPointSectionResponse_TileInfo{
		Data: &fePb.SalaryProgramEntryPointSectionResponse_TileInfo_PromoBannerTileInfo{
			PromoBannerTileInfo: promoBannerTileInfo,
		},
	}, nil
}

// getAppVersionedSalaryProgramCta returns appropriate cta config from appVersionedCtaConfigs for the given app version and fallbacks
// to the default supplied cta config if not config exists for given app version
// **Note** : using a map as a proxy for list DS for appVersionedCta param.
func getAppVersionedSalaryProgramCta(ctx context.Context, defaultCtaConfig *genconf.SalaryProgramCta, appVersionedCtaConfigsMap *syncmap.Map[string, *genconf.AppVersionedSalaryProgramCta], appPlatform commontypes.Platform, appVersion int) *genconf.SalaryProgramCta {
	ctaConfig := defaultCtaConfig

	if appVersionedCtaConfigsMap != nil {
		appVersionedCtaConfigsMap.Range(func(key string, appVersionedCta *genconf.AppVersionedSalaryProgramCta) (continueRange bool) {
			if appPlatform == commontypes.Platform_ANDROID && (appVersion < appVersionedCta.MinAndroidAppVersionSupportingCta() || appVersion > appVersionedCta.MaxAndroidAppVersionSupportingCta()) ||
				appPlatform == commontypes.Platform_IOS && (appVersion < appVersionedCta.MinIosAppVersionSupportingCta() || appVersion > appVersionedCta.MaxIosAppVersionSupportingCta()) {
				return true
			}
			ctaConfig = appVersionedCta.Cta()
			return false
		})
	}
	return ctaConfig
}

// getEntryPointSectionConfig returns the entry point section config which is applicable currently.
// It goes via the time-bound (B2C and B2B) configs and returns the currently active one.
// If none are found within the time-bound, the default config (B2C) is returned.
// nolint:funlen
func (s *Service) getEntryPointSectionConfig(ctx context.Context, employerId string) *genconf.SalaryProgramEntryPointSectionInfo {
	var (
		// default entry point section config
		entryPointSectionConfig = s.dyconf.SalaryProgram().EntryPointSectionInfo()
		currentTime             = time.Now()
		// flag to check if the user's employer was onboarded to salary program via B2B route
		isEmployerB2BOnboarded = false
		// flag to check if time bound B2B config is available or not
		isB2BTimeBoundConfigAvailable = false
	)

	// check if user's current employer is onboarded via B2B route or not for the salary program
	s.dyconf.SalaryProgram().B2BEmployersMap().Range(func(empId string, _ string) bool {
		if empId == employerId {
			isEmployerB2BOnboarded = true
			return false
		}

		return true
	})

	// if employer is onboarded via B2B route for the salary program, then check for the B2B time-bound configs
	if isEmployerB2BOnboarded {
		// iterating over time-bound B2B entry point section configs to extract the one that is live currently
		s.dyconf.SalaryProgram().EntryPointSectionInfoB2BTimeBound().Range(func(_ string, value *genconf.SalaryProgramEntryPointSectionInfo) bool {
			activeFromString := value.ActiveFrom()
			activeTillString := value.ActiveTill()

			activeFrom, err := time.Parse(time.RFC3339, activeFromString)
			if err != nil {
				logger.Error(ctx, "error parsing activeFromString from time-bound B2B entry point section config", zap.Error(err),
					zap.String("activeFromString", activeFromString),
				)

				// returning true to continue iterating over the range
				return true
			}
			activeTill, err := time.Parse(time.RFC3339, activeTillString)
			if err != nil {
				logger.Error(ctx, "error parsing activeTillString from time-bound B2B entry point section config", zap.Error(err),
					zap.String("activeTillString", activeTillString),
				)

				// returning true to continue iterating over the range
				return true
			}

			// activeFrom <= currentTime < activeTill
			if (activeFrom.Before(currentTime) || activeFrom.Equal(currentTime)) && currentTime.Before(activeTill) {
				entryPointSectionConfig = value
				isB2BTimeBoundConfigAvailable = true
				return false
			}

			return true
		})
	}

	// returning the entryPointSectionConfig found from the B2B map
	if isB2BTimeBoundConfigAvailable {
		return entryPointSectionConfig
	}

	// iterating over time-bound B2C entry point section configs to extract the one that is live currently
	s.dyconf.SalaryProgram().EntryPointSectionInfoTimeBound().Range(func(_ string, value *genconf.SalaryProgramEntryPointSectionInfo) bool {
		activeFromString := value.ActiveFrom()
		activeTillString := value.ActiveTill()

		activeFrom, err := time.Parse(time.RFC3339, activeFromString)
		if err != nil {
			logger.Error(ctx, "error parsing activeFromString from time-bound entry point section config", zap.Error(err),
				zap.String("activeFromString", activeFromString),
			)

			// returning true to continue iterating over the range
			return true
		}
		activeTill, err := time.Parse(time.RFC3339, activeTillString)
		if err != nil {
			logger.Error(ctx, "error parsing activeTillString from time-bound entry point section config", zap.Error(err),
				zap.String("activeTillString", activeTillString),
			)

			// returning true to continue iterating over the range
			return true
		}

		// activeFrom <= currentTime < activeTill
		if (activeFrom.Before(currentTime) || activeFrom.Equal(currentTime)) && currentTime.Before(activeTill) {
			entryPointSectionConfig = value
			return false
		}

		return true
	})

	return entryPointSectionConfig
}
