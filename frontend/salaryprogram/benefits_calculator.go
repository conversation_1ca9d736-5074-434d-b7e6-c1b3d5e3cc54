package salaryprogram

import (
	"context"
	"fmt"
	"math"
	"sort"
	"strings"
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/gamma/pkg/accrual"
	"github.com/epifi/gamma/pkg/rewards"

	"github.com/Knetic/govaluate"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	fePb "github.com/epifi/gamma/api/frontend/salaryprogram"
	beRewardsPb "github.com/epifi/gamma/api/rewards"
	beRewardOffersPb "github.com/epifi/gamma/api/rewards/rewardoffers"
	deeplinkScreenOptionPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/salaryprogram"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

func (s *Service) GetSalaryProgramBenefitsCalculatorLandingPage(ctx context.Context, req *fePb.GetSalaryProgramBenefitsCalculatorLandingPageRequest) (*fePb.GetSalaryProgramBenefitsCalculatorLandingPageResponse, error) {
	// COMMENTING: CURRENTLY WE ARE NOT CALCULATING ANY BENEFITS FROM USER'S MONTHLY SALARY
	//// get minimum amount required for salary activation for the user
	//minSalaryRes, err := s.salaryProgramClient.GetMinRequiredAmountForSalaryTxnDetection(ctx, &beSalaryPb.MinRequiredAmountForSalaryTxnDetectionRequest{
	//	ActorId: req.GetReq().GetAuth().GetActorId(),
	//})
	//if rpcErr := epifigrpc.RPCError(minSalaryRes, err); rpcErr != nil {
	//	logger.Error(ctx, "error while fetching min required salary amount", zap.Error(rpcErr))
	//	return &fePb.GetSalaryProgramBenefitsCalculatorLandingPageResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(rpcErr.Error())}}, nil
	//}

	return &fePb.GetSalaryProgramBenefitsCalculatorLandingPageResponse{
		HeaderTitle: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: "Find out the total annual value of your Fi salary benefits"},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_L},
			FontColor:    "#FFFFFF",
		},
		HeaderIconUrl: "https://epifi-icons.pointz.in/salaryprogram/money-bag.png",
		HeaderBgColor: "#282828",
		CalculatorInput: &fePb.GetSalaryProgramBenefitsCalculatorLandingPageResponse_CalculatorInput{
			// COMMENTING: CURRENTLY WE ARE NOT CALCULATING ANY BENEFITS FROM USER'S MONTHLY SALARY
			//MonthlySalary: &fePb.GetSalaryProgramBenefitsCalculatorLandingPageResponse_CalculatorInput_InputBox{
			//	Label: &commontypes.Text{
			//		DisplayValue: &commontypes.Text_PlainString{PlainString: "Enter your in-hand monthly salary"},
			//		FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			//		FontColor:    "#A4A4A4",
			//	},
			//	Placeholder: &types.Money{
			//		CurrencyCode: "INR",
			//		Units:        0,
			//	},
			//	MinAllowedValue: &types.Money{
			//		CurrencyCode: minSalaryRes.GetAmount().GetCurrencyCode(),
			//		Units:        minSalaryRes.GetAmount().GetUnits(),
			//	},
			//	MinAllowedValueValidationFailureMessage: &commontypes.Text{
			//		DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("Please enter a salary amount above ₹%s", money.ToDisplayStringFromIntValue(int(minSalaryRes.GetAmount().GetUnits())))},
			//		FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
			//		FontColor:    "#A73F4B",
			//	},
			//},
			MonthlySpends: &fePb.GetSalaryProgramBenefitsCalculatorLandingPageResponse_CalculatorInput_InputBox{
				Label: &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{PlainString: "Enter how much you spend in a month"},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
					FontColor:    "#A4A4A4",
				},
				Placeholder: &types.Money{
					CurrencyCode: "INR",
					Units:        0,
				},
			},
			SubmitButton: &fePb.GetSalaryProgramBenefitsCalculatorLandingPageResponse_CalculatorInput_SubmitButton{
				InactiveCta: &ui.IconTextComponent{
					Texts: []*commontypes.Text{
						{
							FontColor:    "#646464",
							FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_M},
							DisplayValue: &commontypes.Text_PlainString{PlainString: "Calculate benefits"},
						},
					},
					ContainerProperties: &ui.IconTextComponent_ContainerProperties{BgColor: "#383838"},
				},
				ActiveCta: &ui.IconTextComponent{
					Texts: []*commontypes.Text{
						{
							FontColor:    "#FFFFFF",
							FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_M},
							DisplayValue: &commontypes.Text_PlainString{PlainString: "Calculate benefits"},
						},
					},
					ContainerProperties: &ui.IconTextComponent_ContainerProperties{BgColor: "#00B899"},
				},
			},
			BgColor: "#333333",
		},
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
	}, nil
}

func (s *Service) GetSalaryProgramBenefitsCalculatorResults(ctx context.Context, req *fePb.GetSalaryProgramBenefitsCalculatorResultsRequest) (*fePb.GetSalaryProgramBenefitsCalculatorResultsResponse, error) {
	var (
		appPlatform = req.GetReq().GetAuth().GetDevice().GetPlatform()
		appVersion  = req.GetReq().GetAppVersionCode()
	)
	// get earnings section for calculator results
	earningsSection, err := s.getCalculatorEarningsSection(ctx, req)
	if err != nil {
		logger.Error(ctx, "error getting calculator earning section results", zap.Error(err))
		return &fePb.GetSalaryProgramBenefitsCalculatorResultsResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(err.Error())}}, nil
	}

	// get important info section for calculator results
	importantInfoSection := s.getCalculatorImportantInfoSection(ctx, appPlatform, appVersion)

	return &fePb.GetSalaryProgramBenefitsCalculatorResultsResponse{
		Title: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: "Here’s how much you could earn"},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
			FontColor:    "#333333",
		},
		EarningsSection:      earningsSection,
		ImportantInfoSection: importantInfoSection,
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
	}, nil
}

//nolint:funlen
func (s *Service) getCalculatorEarningsSection(ctx context.Context, req *fePb.GetSalaryProgramBenefitsCalculatorResultsRequest) (*fePb.GetSalaryProgramBenefitsCalculatorResultsResponse_EarningsSection, error) {
	// todo (himanshu) see if we can use benefits manager service to get screen specific offers
	rewardOffersRes, err := s.rewardOffersClient.GetRewardOffersForScreen(ctx, &beRewardOffersPb.GetRewardOffersForScreenRequest{
		ActorId: req.GetReq().GetAuth().GetActorId(),
		Filter: &beRewardOffersPb.GetRewardOffersForScreenRequest_Filter{
			OfferTypes: []beRewardsPb.RewardOfferType{beRewardsPb.RewardOfferType_SALARY_PROGRAM_OFFER},
		},
	})
	if rpcErr := epifigrpc.RPCError(rewardOffersRes, err); rpcErr != nil {
		logger.Error(ctx, "error fetching reward offers for salary program", zap.Error(rpcErr),
			zap.Any(logger.RPC_STATUS, rewardOffersRes.GetStatus()),
		)
		return nil, fmt.Errorf("error fetching reward offers for salary prorgram")
	}
	rewardOffers := rewardOffersRes.GetRewardOffers()

	// sort the reward offers according to the display rank
	sort.Slice(rewardOffers, func(i, j int) bool {
		rewardOfferIDisplayRank, rewardOfferJDisplayRank := rewardOffers[i].GetDisplayMeta().GetDisplayRank(), rewardOffers[j].GetDisplayMeta().GetDisplayRank()
		// 0 implies rank was not specified, using default display_rank in such a case
		if rewardOffers[i].GetDisplayMeta().GetDisplayRankOnSalaryIntroPage() != 0 {
			rewardOfferIDisplayRank = rewardOffers[i].GetDisplayMeta().GetDisplayRankOnSalaryIntroPage()
		}
		if rewardOffers[j].GetDisplayMeta().GetDisplayRankOnSalaryIntroPage() != 0 {
			rewardOfferJDisplayRank = rewardOffers[j].GetDisplayMeta().GetDisplayRankOnSalaryIntroPage()
		}
		return rewardOfferIDisplayRank < rewardOfferJDisplayRank
	})

	individualEarnings := make([]*fePb.GetSalaryProgramBenefitsCalculatorResultsResponse_EarningsSection_IndividualEarnings, 0, len(rewardOffers))
	appPlatform, appVersion := req.GetReq().GetAuth().GetDevice().GetPlatform(), req.GetReq().GetAuth().GetDevice().GetAppVersion()
	totalCashEquivalent := 0
	for _, rewardOffer := range rewardOffers {
		// skipping the listing of reward offer if it's not allowed for user's app platform and app version
		minAllowedAppVersion, isMinVerConfigured := rewardOffer.GetDisplayMeta().GetPlatformToMinSupportedAppVersionMap()[appPlatform.String()]
		maxAllowedAppVersion, isMaxVerConfigured := rewardOffer.GetDisplayMeta().GetPlatformToMaxSupportedAppVersionMap()[appPlatform.String()]
		if isMinVerConfigured && appVersion < minAllowedAppVersion || isMaxVerConfigured && appVersion > maxAllowedAppVersion {
			continue
		}

		// skipping the listing of reward offer on salary intro page if the visibility is off
		if !rewardOffer.GetDisplayMeta().GetIsVisibleOnSalaryIntroPage() {
			continue
		}

		cashEquivalent, err := s.calculateRewardOfferCashEquivalent(rewardOffer, req.GetMonthlySalary().GetUnits(), req.GetMonthlySpends().GetUnits())
		if err != nil {
			logger.Error(ctx, "error calculating cash equivalent for reward offer", zap.String(logger.REWARD_OFFER_ID, rewardOffer.GetId()), zap.Error(err))
			continue
		}

		individualEarnings = append(individualEarnings, &fePb.GetSalaryProgramBenefitsCalculatorResultsResponse_EarningsSection_IndividualEarnings{
			Amount: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("₹%s", money.ToDisplayStringFromIntValue(int(cashEquivalent)))},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_NUMBER_L},
				FontColor:    "#333333",
			},
			Desc: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: rewardOffer.GetDisplayMeta().GetTitle()},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
				FontColor:    "#646464",
			},
			ImageUrl: rewardOffer.GetDisplayMeta().GetIcon(),
		})
		totalCashEquivalent += int(cashEquivalent)
	}

	return &fePb.GetSalaryProgramBenefitsCalculatorResultsResponse_EarningsSection{
		TotalEarnings: &fePb.GetSalaryProgramBenefitsCalculatorResultsResponse_EarningsSection_TotalEarnings{
			Title: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "TOTAL ANNUAL EARNINGS"},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_XS_CAPS},
				FontColor:    "#478295",
			},
			Amount: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("₹%s", money.ToDisplayStringFromIntValue(totalCashEquivalent))},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_NUMBER_2XL},
				FontColor:    "#333333",
			},
			ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/3d-cash-prize.png",
			BgColor:  "#C0DAE0",
		},
		IndividualEarnings: individualEarnings,
	}, nil
}

func (s *Service) getCalculatorImportantInfoSection(ctx context.Context, appPlatform commontypes.Platform, appVersion uint32) *fePb.GetSalaryProgramBenefitsCalculatorResultsResponse_ImportantInfoSection {
	bulletPoints := make([]*fePb.GetSalaryProgramBenefitsCalculatorResultsResponse_ImportantInfoSection_BulletPoint, len(s.conf.SalaryProgram.BenefitsCalculatorPage.ImportantInfoBulletPoints))
	for i, bulletPoint := range s.conf.SalaryProgram.BenefitsCalculatorPage.ImportantInfoBulletPoints {
		// Check if the HTML contains format specifiers
		formattedHtml := bulletPoint.Html
		if strings.Contains(bulletPoint.Html, "%") {
			formattedHtml = fmt.Sprintf(bulletPoint.Html, rewards.GetFiCoinsToCashConversionRatio(false, time.Time{}))
		}
		bulletPoints[i] = &fePb.GetSalaryProgramBenefitsCalculatorResultsResponse_ImportantInfoSection_BulletPoint{
			Text: &commontypes.Text{
				DisplayValue: &commontypes.Text_Html{Html: accrual.ReplaceCoinWithPointIfApplicable(formattedHtml)},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
			},
		}
		if bulletPoint.Deeplink != "" {
			dl := &deeplink.Deeplink{}
			err := protojson.Unmarshal([]byte(bulletPoint.Deeplink), dl)
			if err != nil {
				logger.Error(ctx, "failed to unmarshal dl", zap.String("deeplink", bulletPoint.Deeplink))
			}

			if dl.GetScreen() == deeplink.Screen_SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION {
				if appPlatform == commontypes.Platform_IOS && appVersion < s.dyconf.SalaryProgram().MinIosAppVersionSupportingLandingScreenRedirection() ||
					appPlatform == commontypes.Platform_ANDROID && appVersion < s.dyconf.SalaryProgram().MinAndroidAppVersionSupportingLandingScreenRedirection() {
					dl = &deeplink.Deeplink{
						Screen: deeplink.Screen_SALARY_PROGRAM_INTRO_SCREEN,
					}
				} else {
					screenOptions, screenOptionsErr := deeplinkv3.GetScreenOptionV2(&deeplinkScreenOptionPb.SalaryProgramLandingScreenRedirectionScreenOptions{})
					if screenOptionsErr != nil {
						logger.Error(ctx, "error while getting deeplink screen options v2", zap.Error(screenOptionsErr))
					}
					dl = &deeplink.Deeplink{
						Screen:          deeplink.Screen_SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION,
						ScreenOptionsV2: screenOptions,
					}
				}
			}
			bulletPoints[i].Deeplink = dl
		}
	}
	return &fePb.GetSalaryProgramBenefitsCalculatorResultsResponse_ImportantInfoSection{
		Title: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: "Important information"},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			FontColor:    "#333333",
		},
		BulletPoints: bulletPoints,
	}
}

func (s *Service) calculateRewardOfferCashEquivalent(rewardOffer *beRewardOffersPb.RewardOffer, monthlySalary int64, monthlySpends int64) (float64, error) {
	expression, err := govaluate.NewEvaluableExpressionWithFunctions(rewardOffer.GetDisplayMeta().GetCashEquivalentCalculationExpression(), s.getExpressionFunctionMap())
	if err != nil {
		return 0, err
	}
	res, err := expression.Evaluate(s.getExpressionParameterMap(monthlySalary, monthlySpends))
	if err != nil {
		return 0, err
	}
	resFloat, ok := res.(float64)
	if !ok {
		return 0, fmt.Errorf("error typecasting interface to float64")
	}
	return resFloat, nil
}

func (s *Service) getExpressionFunctionMap() map[string]govaluate.ExpressionFunction {
	return map[string]govaluate.ExpressionFunction{
		"Min": func(args ...interface{}) (interface{}, error) {
			if len(args) != 2 {
				return false, fmt.Errorf("invalid param count, Min expects exactly 2 arguments")
			}

			var floatVals []float64
			for _, val := range args {
				floatVal, ok := val.(float64)
				if !ok {
					return nil, fmt.Errorf("error typecasting interface to float64")
				}
				floatVals = append(floatVals, floatVal)
			}

			return math.Min(floatVals[0], floatVals[1]), nil
		},
	}
}

func (s *Service) getExpressionParameterMap(monthlySalary, monthlySpends int64) map[string]interface{} {
	return map[string]interface{}{
		"MONTHLY_SALARY": monthlySalary,
		"MONTHLY_SPENDS": monthlySpends,
	}
}
