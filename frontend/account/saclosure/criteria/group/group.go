package group

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"

	"github.com/google/wire"
	"github.com/pkg/errors"

	"github.com/epifi/gamma/api/frontend/account/enums"
	saClosurePb "github.com/epifi/gamma/api/frontend/account/sa_closure"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/account/saclosure/criteria/helper"
	"github.com/epifi/gamma/frontend/account/saclosure/criteria/item"
	accrualPkg "github.com/epifi/gamma/pkg/accrual"
)

type IBuilder interface {
	BuildCriteriaGroups(ctx context.Context, actorId string) ([]*saClosurePb.CriteriaGroup, error)
}

type Builder struct {
	itemParallelExecutor item.IParallelExecutor
}

func NewBuilder(itemParallelExecutor item.IParallelExecutor) *Builder {
	return &Builder{itemParallelExecutor: itemParallelExecutor}
}

var CriteriaGroupBuilderWireSet = wire.NewSet(NewBuilder, wire.Bind(new(IBuilder), new(*Builder)))

func (b *Builder) BuildCriteriaGroups(ctx context.Context, actorId string) ([]*saClosurePb.CriteriaGroup, error) {
	criteriaItemMap, err := b.itemParallelExecutor.Execute(ctx, actorId, GetAllowedCriteriaItems())
	if err != nil {
		return nil, errors.Wrap(err, "failed to collect criteria items using parallel executor")
	}

	var criteriaGroups []*saClosurePb.CriteriaGroup
	for idx, group := range allowedCriteriaGroups {
		criteriaGroup, buildErr := b.buildGroup(idx+1, group, criteriaItemMap)
		if buildErr != nil {
			return nil, errors.Wrap(buildErr, "failed to build criteria group")
		}

		criteriaGroups = append(criteriaGroups, criteriaGroup)
	}

	return criteriaGroups, nil
}

func (b *Builder) buildGroup(groupNumber int, groupEnum enums.SaClosureCriteriaGroup, criteriaItemsMap map[enums.SaClosureCriteriaItem]saClosurePb.ICriteriaItem) (*saClosurePb.CriteriaGroup, error) {
	criteriaGroup, err := b.buildGroupLayout(groupEnum)
	if err != nil {
		return nil, errors.Wrap(err, "failed to build criteria group layout")
	}
	criteriaGroup.GroupNumber = helper.GetCriteriaGroupNumberComponentImage(groupNumber)

	criteriaItemsEnumList := groupItemMap[groupEnum]
	for _, criteriaItemsEnum := range criteriaItemsEnumList {
		iCriteriaItem, ok := criteriaItemsMap[criteriaItemsEnum]
		if !ok {
			continue
		}

		switch criteriaItem := iCriteriaItem.(type) {
		case *saClosurePb.CriteriaItem:
			criteriaGroup.CriteriaItems = append(criteriaGroup.CriteriaItems, criteriaItem)
			if criteriaGroup.GetCriteriaItemList() == nil {
				criteriaGroup.CriteriaItemsV2 = &saClosurePb.CriteriaGroup_CriteriaItemList{CriteriaItemList: &saClosurePb.CriteriaItemList{}}
			}

			criteriaGroup.GetCriteriaItemList().CriteriaItems = append(criteriaGroup.GetCriteriaItemList().CriteriaItems, criteriaItem)
		case *saClosurePb.FullGroupCriteriaItem:
			criteriaGroup.CriteriaItemsV2 = &saClosurePb.CriteriaGroup_FullGroupCriteriaItem{FullGroupCriteriaItem: criteriaItem}
		default:
			return nil, fmt.Errorf("unknown criteria item type %T", iCriteriaItem)
		}
	}

	if groupEnum == enums.SaClosureCriteriaGroup_SA_CLOSURE_CRITERIA_GROUP_SA_BALANCE {
		valItem, ok := criteriaItemsMap[enums.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_SAVINGS_ACCOUNT_BALANCE]
		if !ok {
			return criteriaGroup, nil
		}

		currValue := valItem.GetMeta().GetMoney().GetUnits()
		currFraction := valItem.GetMeta().GetMoney().GetNanos()
		if currValue == 0 && currFraction > 0 {
			criteriaGroup.InfoText = helper.GetCriteriaGroupInfoText(helper.SaBalanceInfoText)
		}
	}

	return criteriaGroup, nil
}

func (b *Builder) buildGroupLayout(groupEnum enums.SaClosureCriteriaGroup) (*saClosurePb.CriteriaGroup, error) {
	criteriaGroup := &saClosurePb.CriteriaGroup{}

	var heading, zeroStateIconUrl, zeroStateText string
	var infoText *ui.IconTextComponent
	switch groupEnum {
	case enums.SaClosureCriteriaGroup_SA_CLOSURE_CRITERIA_GROUP_INVESTMENTS:
		heading = helper.CriteriaGroupInvestmentsTitle
		zeroStateText = helper.CriteriaGroupInvestmentsZeroStateText
		zeroStateIconUrl = helper.InvestmentsZeroStateUrl
	case enums.SaClosureCriteriaGroup_SA_CLOSURE_CRITERIA_GROUP_LOANS:
		heading = helper.CriteriaGroupLoans
		zeroStateText = helper.CriteriaGroupLoansZeroStateText
		zeroStateIconUrl = helper.LoansZeroStateUrl
	case enums.SaClosureCriteriaGroup_SA_CLOSURE_CRITERIA_GROUP_CREDIT_CARDS:
		heading = helper.CriteriaGroupCreditCards
		zeroStateText = helper.CriteriaGroupCreditCardsZeroStateText
		zeroStateIconUrl = helper.CcZeroStateUrl
		infoText = helper.GetCriteriaGroupInfoText(helper.CcInfoText)
	case enums.SaClosureCriteriaGroup_SA_CLOSURE_CRITERIA_GROUP_AUTOMATED_PAYMENTS:
		heading = helper.CriteriaGroupAutoPayments
		zeroStateText = helper.CriteriaGroupAutoPaymentsZeroStateText
		zeroStateIconUrl = helper.AutoPayZeroStateUrl
	case enums.SaClosureCriteriaGroup_SA_CLOSURE_CRITERIA_GROUP_FI_COINS:
		heading = accrualPkg.ReplaceCoinWithPointIfApplicable(helper.CriteriaGroupFiCoins, nil)
		zeroStateText = accrualPkg.ReplaceCoinWithPointIfApplicable(helper.CriteriaGroupFiCoinsZeroStateText, nil)
		zeroStateIconUrl = helper.FiCoinsZeroStateUrl
		infoText = helper.GetCriteriaGroupInfoText(accrualPkg.ReplaceCoinWithPointIfApplicable(helper.FiCoinInfoText, nil))
	case enums.SaClosureCriteriaGroup_SA_CLOSURE_CRITERIA_GROUP_SA_BALANCE:
		heading = helper.CriteriaGroupSaBalance
		zeroStateText = helper.CriteriaGroupSaBalanceZeroStateText
		zeroStateIconUrl = helper.SaBalanceZeroStateUrl
	case enums.SaClosureCriteriaGroup_SA_CLOSURE_CRITERIA_GROUP_UPI_LITE:
		heading = helper.CriteriaGroupUpiLite
		zeroStateText = helper.CriteriaGroupUpiLiteZeroStateText
		zeroStateIconUrl = helper.UpiLiteZeroStateUrl
	default:
		return nil, fmt.Errorf("cannot build criteria group for %s", groupEnum.String())
	}

	criteriaGroup.GroupHeading = commontypes.GetTextFromStringFontColourFontStyle(heading, helper.CriteriaGroupTitleColor, commontypes.FontStyle_HEADLINE_S)
	criteriaGroup.ZeroState = helper.NewZeroStateGroup(zeroStateIconUrl, zeroStateText)
	criteriaGroup.InfoText = infoText
	criteriaGroup.BgColor = helper.CriteriaGroupBgColor
	criteriaGroup.CollapseIcon = commontypes.GetVisualElementFromUrlHeightAndWidth(helper.CriteriaGroupCollapseIcon, helper.CriteriaGroupCollapseIconSize, helper.CriteriaGroupCollapseIconSize)
	criteriaGroup.IsCollapsed = true
	criteriaGroup.GroupIdentifier = groupEnum.String()

	return criteriaGroup, nil
}
