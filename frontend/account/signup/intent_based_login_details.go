package signup

import (
	"github.com/epifi/be-common/api/typesv2/common"

	consentPb "github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/frontend/account"
	"github.com/epifi/gamma/api/frontend/account/signup"
	"github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/form"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/pkg/countrystdinfo"
)

// I agree to T&Cs of <a style='color: #00B899; text-decoration: none;' href="https://fi.money/T&C">Fi</a>, <a style='color: #00B899; text-decoration: none;' href="https://fi.money/wealth/TnC">Epifi Wealth</a> & <a style='color: #00B899; text-decoration: none;' href="https://fi.money/assets/pages/experian-tnc">Experian</a>, and the <a style='color: #00B899; text-decoration: none;' href="https://fi.money/privacy">Privacy Policy</a>.
const (
	EpifiWealthLogoURL      = "https://epifi-icons.pointz.in/signup/epifi-wealth-logo.png"
	ExperianLogoURL         = "https://epifi-icons.pointz.in/signup/experian-logo"
	loginConsent            = "I agree to Fi's <a style='color: #00B899; text-decoration: none;' href=\"https://fi.money/privacy\">Privacy Policy</a>, and the T&Cs of <a style='color: #00B899; text-decoration: none;' href=\"https://fi.money/T&C\">Fi</a>, <a style='color: #00B899; text-decoration: none;' href=\"https://fi.money/wealth/TnC\">Epifi Wealth</a>, and <a style='color: #00B899; text-decoration: none;' href=\"https://fi.money/assets/pages/experian-tnc\">Experian</a>. I also consent to:"
	loginConsentDescription = "<ul><li>Fi to process and store my Experian credit report for 6 months to provide insights and services</li><li>Fi and its partners (including <a style='color: #00B899; text-decoration: none;' href='https://fi.money/features/instant-loans'>lenders</a>, <a style='color: #00B899; text-decoration: none;' href='https://fi.money/features/accounts'>banks</a>, <a style='color: #00B899; text-decoration: none;' href='https://fi.money/features/mutual-funds'>MF partners</a> etc) receiving, processing and storing all my data to provide services</li><li>Epifi Wealth to process and store my data (including SMS), and to share it with group companies and third parties to provide services</li><li>Fi accessing my location for security purposes and agree to receive updates and information on Whatsapp, SMS or email</li></ul>"
	nrLoginConsent          = "I agree to T&Cs of <a style='color: #00B899; text-decoration: none;' href=\"https://fi.money/T&C\">Fi</a> and I agree to receive updates on Whatsapp."
)

var platformToConsentTextColor = map[common.Platform]string{
	common.Platform_ANDROID: "#333333",
	common.Platform_IOS:     "#929599",
}

var platformToConsentDescTextColor = map[common.Platform]string{
	common.Platform_ANDROID: "#6A6D70",
	common.Platform_IOS:     "#6A6D70",
}

var consentInfo = func(platform common.Platform, version int) *signup.GetGenerateOtpInfoResponse_ConsentWithDescription {
	consentTextColor := platformToConsentTextColor[platform]
	consentDescTextColor := platformToConsentDescTextColor[platform]
	phoneVerificationScreenDarkThemeEnabledOnAndroid := isPhoneVerificationScreenDarkThemeEnabledOnAndroid(version)
	if phoneVerificationScreenDarkThemeEnabledOnAndroid && platform == common.Platform_ANDROID {
		consentTextColor = "#929599"
		consentDescTextColor = "#6A6D70"
	}
	return &signup.GetGenerateOtpInfoResponse_ConsentWithDescription{
		Consent: &form.Consent{
			Text:        common.GetTextFromHtmlStringFontColourFontStyle(loginConsent, consentTextColor, common.FontStyle_BODY_XS),
			IsChecked:   false,
			IsMandatory: true,
			ConsentIdWithOwner: []*form.ConsentIdWithOwner{
				{
					ConsentId:    consentPb.ConsentType_CREDIT_REPORT_TNC.String(),
					ConsentOwner: common.Owner_OWNER_EPIFI_TECH.String(),
				},
				{
					ConsentId:    consentPb.ConsentType_CONSENT_SMS_DATA_PROCESSING_CONSENT.String(),
					ConsentOwner: common.Owner_OWNER_EPIFI_WEALTH.String(),
				},
				{
					ConsentId:    consentPb.ConsentType_CONSENT_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP_V2.String(),
					ConsentOwner: common.Owner_OWNER_EPIFI_WEALTH.String(),
				},
				{
					ConsentId:    consentPb.ConsentType_FI_TNC.String(),
					ConsentOwner: common.Owner_OWNER_EPIFI_TECH.String(),
				},
				{
					ConsentId:    consentPb.ConsentType_FI_PRIVACY_POLICY.String(),
					ConsentOwner: common.Owner_OWNER_EPIFI_TECH.String(),
				},
			},
		},
		Description: common.GetTextFromHtmlStringFontColourFontStyle(loginConsentDescription, consentDescTextColor, common.FontStyle_BODY_XS),
		PartnerLogosInfo: &signup.GetGenerateOtpInfoResponse_PartnerLogosInfo{
			PartnerLogos: []*common.VisualElement{
				common.GetVisualElementFromUrlHeightAndWidth(EpifiWealthLogoURL, 13, 65),
				common.GetVisualElementFromUrlHeightAndWidth(ExperianLogoURL, 20, 62),
			},
		},
	}
}

var NRConsentInfo = func(platform common.Platform, version int) *signup.GetGenerateOtpInfoResponse_ConsentWithDescription {
	consentTextColor := platformToConsentTextColor[platform]
	phoneVerificationScreenDarkThemeEnabledOnAndroid := isPhoneVerificationScreenDarkThemeEnabledOnAndroid(version)
	if phoneVerificationScreenDarkThemeEnabledOnAndroid && platform == common.Platform_ANDROID {
		consentTextColor = "#929599"
	}
	return &signup.GetGenerateOtpInfoResponse_ConsentWithDescription{
		Consent: &form.Consent{
			Text:        common.GetTextFromHtmlStringFontColourFontStyle(nrLoginConsent, consentTextColor, common.FontStyle_BODY_XS),
			IsChecked:   false,
			IsMandatory: true,
			ConsentIdWithOwner: []*form.ConsentIdWithOwner{
				{
					ConsentId:    consentPb.ConsentType_FI_TNC.String(),
					ConsentOwner: common.Owner_OWNER_EPIFI_TECH.String(),
				},
			},
		},
	}
}

type IntentBasedLoginDetails struct {
	// ShouldTakeBiometricConsent indicates whether the user will be required to provide biometric consent
	// and device authentication using biometrics or a PIN during email verification.
	ShouldTakeBiometricConsent bool
	// PhoneVerificationScreenTitle is the title for the phone verification screen.
	PhoneVerificationScreenTitle string
	// CountryInfo contains the list of countries for which the user can sign up.
	CountryInfo []*account.CountryInfo
	// Consent contains the consents required for the user to sign up.
	Consent *signup.GetGenerateOtpInfoResponse_ConsentWithDescription
}

// nolint: dupl
func (s *Service) getIntentBasedLoginDetails(platform common.Platform, version int, acqInfo *user.AcquisitionInfo) *IntentBasedLoginDetails {
	switch acqInfo.GetAcquisitionIntent() {
	case user.AcquisitionIntent_ACQUISITION_INTENT_WEALTH_ANALYSER:
		indiaCountryInfo := countrystdinfo.GetCountryStandardInfo(typesv2.CountryCode_COUNTRY_CODE_IND)
		return &IntentBasedLoginDetails{
			ShouldTakeBiometricConsent:   false,
			PhoneVerificationScreenTitle: "Maximise your wealth",
			CountryInfo: []*account.CountryInfo{
				{
					CountryCode: typesv2.CountryCode_COUNTRY_CODE_IND.String(),
					CountryDisplayInfo: &account.CountryDisplayInfo{
						CountryName: indiaCountryInfo.GetCLDRName(),
						FlagEmoji:   indiaCountryInfo.GetFlagEmoji(),
					},
					CountryPhNumInfo: &account.CountryPhNumInfo{
						IsdCode:   int32(indiaCountryInfo.GetPhoneInfo().GetISDCode()),
						MinDigits: int32(indiaCountryInfo.GetPhoneInfo().GetMinDigits()),
						MaxDigits: int32(indiaCountryInfo.GetPhoneInfo().GetMaxDigits()),
					},
				},
			},
			Consent: consentInfo(platform, version),
		}
	case user.AcquisitionIntent_ACQUISITION_INTENT_PERSONAL_LOANS:
		return &IntentBasedLoginDetails{
			ShouldTakeBiometricConsent:   true,
			PhoneVerificationScreenTitle: "Get loan in minutes",
			CountryInfo:                  s.getCountriesInfo(),
			Consent:                      consentInfo(platform, version),
		}
	default:
		return &IntentBasedLoginDetails{
			ShouldTakeBiometricConsent:   true,
			PhoneVerificationScreenTitle: "Log in/Sign up",
			CountryInfo:                  s.getCountriesInfo(),
			Consent:                      consentInfo(platform, version),
		}
	}
}
