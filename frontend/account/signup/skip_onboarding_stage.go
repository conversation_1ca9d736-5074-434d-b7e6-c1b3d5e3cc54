package signup

import (
	"context"
	"fmt"

	"github.com/samber/lo"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/frontend/account/signup"
	"github.com/epifi/gamma/api/frontend/header"
	onboardingPb "github.com/epifi/gamma/api/user/onboarding"
)

var (
	feStageToBeStageMap = map[signup.SkipOnboardingStageRequest_Stage]onboardingPb.OnboardingStage{
		signup.SkipOnboardingStageRequest_ADD_MONEY:                   onboardingPb.OnboardingStage_ADD_MONEY,
		signup.SkipOnboardingStageRequest_VKYC:                        onboardingPb.OnboardingStage_VKYC,
		signup.SkipOnboardingStageRequest_REFERRAL_FINITE_CODE:        onboardingPb.OnboardingStage_REFERRAL_FINITE_CODE,
		signup.SkipOnboardingStageRequest_WORK_EMAIL_VERIFICATION:     onboardingPb.OnboardingStage_WORK_EMAIL_VERIFICATION,
		signup.SkipOnboardingStageRequest_LINKEDIN_VERIFICATION:       onboardingPb.OnboardingStage_LINKEDIN_VERIFICATION,
		signup.SkipOnboardingStageRequest_OPTIONAL_VKYC:               onboardingPb.OnboardingStage_OPTIONAL_VKYC,
		signup.SkipOnboardingStageRequest_EMPLOYMENT_VERIFICATION:     onboardingPb.OnboardingStage_EMPLOYMENT_VERIFICATION,
		signup.SkipOnboardingStageRequest_GMAIL_VERIFICATION:          onboardingPb.OnboardingStage_GMAIL_VERIFICATION,
		signup.SkipOnboardingStageRequest_EPFO_COMPANY_SEARCH:         onboardingPb.OnboardingStage_EPFO_COMPANY_SEARCH,
		signup.SkipOnboardingStageRequest_CONNECTED_ACCOUNTS:          onboardingPb.OnboardingStage_CONNECTED_ACCOUNTS,
		signup.SkipOnboardingStageRequest_BKYC:                        onboardingPb.OnboardingStage_BKYC,
		signup.SkipOnboardingStageRequest_ITR_INTIMATION_VERIFICATION: onboardingPb.OnboardingStage_ITR_INTIMATION_VERIFICATION,
		signup.SkipOnboardingStageRequest_KYC_NAME_DOB_VALIDATION:     onboardingPb.OnboardingStage_KYC_NAME_DOB_VALIDATION,
		signup.SkipOnboardingStageRequest_SA_DECLARATION:              onboardingPb.OnboardingStage_SA_DECLARATION,
	}
	skipAllowedStage = []onboardingPb.OnboardingStage{
		onboardingPb.OnboardingStage_ADD_MONEY,
		onboardingPb.OnboardingStage_VKYC,
		onboardingPb.OnboardingStage_REFERRAL_FINITE_CODE,
		onboardingPb.OnboardingStage_WORK_EMAIL_VERIFICATION,
		onboardingPb.OnboardingStage_LINKEDIN_VERIFICATION,
		onboardingPb.OnboardingStage_OPTIONAL_VKYC,
		onboardingPb.OnboardingStage_EMPLOYMENT_VERIFICATION,
		onboardingPb.OnboardingStage_GMAIL_VERIFICATION,
		onboardingPb.OnboardingStage_EPFO_COMPANY_SEARCH,
		onboardingPb.OnboardingStage_CONNECTED_ACCOUNTS,
		onboardingPb.OnboardingStage_BKYC,
		onboardingPb.OnboardingStage_ITR_INTIMATION_VERIFICATION,
		onboardingPb.OnboardingStage_ORDER_PHYSICAL_CARD,
		onboardingPb.OnboardingStage_SMS_PARSER_CONSENT,
		onboardingPb.OnboardingStage_WEALTH_BUILDER_CONNECTED_ACCOUNTS_FLOW,
	}
)

// SkipOnboardingStage rpc to skip a particular onboarding stage
// also returns next acton to be taken after skipping the specified stage
func (s *Service) SkipOnboardingStage(ctx context.Context, req *signup.SkipOnboardingStageRequest) (*signup.SkipOnboardingStageResponse, error) {
	if req.GetStage() == signup.SkipOnboardingStageRequest_STAGE_UNSPECIFIED && req.GetStageStr() == "" {
		logger.Info(ctx, "Both stage and str are unspecified")
		return &signup.SkipOnboardingStageResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInvalidArgument(),
			},
		}, nil
	}

	beStage, ok := feStageToBeStageMap[req.GetStage()]
	// adding req.GetStageStr ==  "" since we are giving preference to stageStr
	if !ok && req.GetStage() != signup.SkipOnboardingStageRequest_STAGE_UNSPECIFIED && req.GetStageStr() == "" {
		logger.Info(ctx, "skippping this stage not supported", zap.String(logger.ONBOARDING_STAGE, req.GetStage().String()))
		return &signup.SkipOnboardingStageResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInvalidArgument(),
			},
		}, nil
	}

	// giving preference to stage in string
	if req.GetStageStr() != "" {
		beStage = onboardingPb.OnboardingStage(onboardingPb.OnboardingStage_value[req.GetStageStr()])
		_, ok := lo.Find(skipAllowedStage, func(s onboardingPb.OnboardingStage) bool {
			return beStage == s
		})
		if !ok {
			logger.Info(ctx, "skippping this stage not supported", zap.String(logger.ONBOARDING_STAGE, req.GetStageStr()))
			return &signup.SkipOnboardingStageResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusInvalidArgument(),
				},
			}, nil
		}
	}

	actorId := req.GetReq().GetAuth().GetActorId()

	updateStageRes, err := s.OnboardingClient.UpdateStage(ctx, &onboardingPb.UpdateStageRequest{
		ActorId:  actorId,
		Stage:    beStage,
		NewState: onboardingPb.OnboardingState_SKIPPED,
	})
	if err = epifigrpc.RPCError(updateStageRes, err); err != nil {
		logger.Error(ctx, "error updating stage", zap.Error(err))
		return &signup.SkipOnboardingStageResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternal(),
			},
		}, nil
	}

	logger.Info(ctx, fmt.Sprintf("Successfully skipped stage: %v", beStage.String()))

	nextActionRes, err := s.OnboardingClient.GetNextAction(ctx, &onboardingPb.GetNextActionRequest{
		ActorId: actorId,
	})
	if err = epifigrpc.RPCError(nextActionRes, err); err != nil {
		logger.Error(ctx, "error fetching next action", zap.Error(err))
		return &signup.SkipOnboardingStageResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternal(),
			},
		}, nil
	}

	s.logSkippedStage(ctx, actorId, beStage)

	return &signup.SkipOnboardingStageResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		NextAction: nextActionRes.GetNextAction(),
	}, nil
}
