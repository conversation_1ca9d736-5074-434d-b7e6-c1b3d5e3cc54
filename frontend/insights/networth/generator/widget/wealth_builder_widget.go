package widget

import (
	"fmt"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"

	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/encoding/protojson"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/frontend/deeplink"
	networthFePb "github.com/epifi/gamma/api/frontend/insights/networth"
	feNetworthUi "github.com/epifi/gamma/api/frontend/insights/networth/ui"
	types "github.com/epifi/gamma/api/typesv2"
	typesUiPb "github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/insights/networth/common"
)

type WidgetUIDetails struct {
	widgetState        feNetworthUi.WidgetStateV2
	currencySymbol     *commontypes.Text
	displayValue       *commontypes.Text
	backgroundColor    *widget.BackgroundColour
	borderProperty     *typesUiPb.BorderProperty
	sduiBorderProperty *properties.BorderProperty
}

func NewBasicWealthBuilderWidget(widgetDataDetails *WidgetDataDetails, deeplink *deeplink.Deeplink, topRightNudge *commontypes.VisualElement, bottomTag *typesUiPb.IconTextComponent) (*feNetworthUi.WidgetV2, error) {
	widgetUiDetails := getWidgetUiDetails(widgetDataDetails)
	cacheControl := getCacheControlV2(widgetDataDetails.NetWorthCategoryStatus)
	widgetAnalyticsPayload := getWidgetAnalyticsPayload(widgetDataDetails.NetWorthCategoryType, widgetDataDetails.NetWorthCategoryStatus)
	analyticsPayloadStr, marshalingErr := protojson.Marshal(widgetAnalyticsPayload)
	if marshalingErr != nil {
		return nil, fmt.Errorf("failed to marshal analytics payload for widget : %w", marshalingErr)
	}

	return NewWealthBuilderWidget(widgetDataDetails.WidgetConfig.GetWealthLandingWidgetId()).
		WithWeight(widgetDataDetails.WidgetWidth).
		WithTitle(commontypes.GetTextFromStringFontColourFontStyle(widgetDataDetails.WidgetConfig.GetTitle(), colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_HEADLINE_S)).
		WithIcon(getWbWidgetIcon(widgetDataDetails)).
		WithCurrencySymbol(widgetUiDetails.currencySymbol).
		WithDisplayValue(widgetUiDetails.displayValue).
		WithValue(widgetDataDetails.WidgetData.Value).
		WithBottomTag(bottomTag).
		WithNudge(topRightNudge).
		WithBackgroundColor(widgetUiDetails.backgroundColor).
		WithBorderProperty(widgetUiDetails.borderProperty).
		WithDeeplink(deeplink).
		WithWidgetState(widgetUiDetails.widgetState).
		WithCacheControl(cacheControl).
		WithWidgetAnalyticsPayload(string(analyticsPayloadStr)).
		WithSduiBorderProperty(widgetUiDetails.sduiBorderProperty).
		Build(), nil
}

func getWbWidgetIcon(widgetDataDetails *WidgetDataDetails) *commontypes.VisualElement {
	imageWidth := int32(48)
	if widgetDataDetails.WidgetWidth < common.PrimaryWidgetWidth {
		imageWidth = 36
		if widgetDataDetails.NetWorthCategoryStatus != networthFePb.NetworthCategoryStatus_NETWORTH_CATEGORY_STATUS_INITIALIZED {
			return commontypes.GetVisualElementFromUrlHeightAndWidth(common.GreyPlusCircleIcon, imageWidth, imageWidth)
		}
	}
	return commontypes.GetVisualElementFromUrlHeightAndWidth(widgetDataDetails.WidgetConfig.GetWealthBuilderLandingIconUrl(), imageWidth, imageWidth)
}

func getCacheControlV2(categoryStatus networthFePb.NetworthCategoryStatus) feNetworthUi.CacheControlV2 {
	if categoryStatus == networthFePb.NetworthCategoryStatus_NETWORTH_CATEGORY_STATUS_VALUE_FETCH_FAILED {
		return feNetworthUi.CacheControlV2_CACHE_CONTROL_V2_USE_CACHE
	}
	return feNetworthUi.CacheControlV2_CACHE_CONTROL_V2_UNSPECIFIED
}

func getWidgetAnalyticsPayload(categoryName networthFePb.NetworthCategory, categoryStatus networthFePb.NetworthCategoryStatus) *networthFePb.WidgetAnalyticsPayload {
	var status networthFePb.WidgetStatus
	switch categoryStatus {
	case networthFePb.NetworthCategoryStatus_NETWORTH_CATEGORY_STATUS_INITIALIZED:
		status = networthFePb.WidgetStatus_WIDGET_STATUS_COMPLETED
	default:
		status = networthFePb.WidgetStatus_WIDGET_STATUS_NOT_COMPLETED
	}
	return &networthFePb.WidgetAnalyticsPayload{
		WidgetName: categoryName,
		Status:     status,
	}
}

func getWidgetUiDetails(widgetDataDetails *WidgetDataDetails) WidgetUIDetails {
	if widgetDataDetails.NetWorthCategoryStatus == networthFePb.NetworthCategoryStatus_NETWORTH_CATEGORY_STATUS_INITIALIZED {
		return WidgetUIDetails{
			widgetState:    feNetworthUi.WidgetStateV2_WIDGET_STATE_V2_INITIALIZED,
			currencySymbol: commontypes.GetTextFromStringFontColourFontStyle("₹", colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_CURRENCY_M),
			displayValue:   commontypes.GetTextFromStringFontColourFontStyle(money.ToDisplayStringWithSuffixAndPrecisionV2(widgetDataDetails.WidgetData.Value, false, true, 2, true, money.IndianNumberSystem), colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_NUMBER_M),
			backgroundColor: &widget.BackgroundColour{
				Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#28292B"},
			},
			borderProperty: &typesUiPb.BorderProperty{
				BorderThickness: 1,
				BorderColor:     common.WealthBuilderComponentsBorderColor,
				BorderType:      typesUiPb.BorderStyle_SOLID_LINE,
			},
			sduiBorderProperty: &properties.BorderProperty{
				BorderThickness: 1,
				BorderColor:     common.WealthBuilderComponentsBorderColor,
				CornerRadius:    16,
				IsDash:          false,
			},
		}
	}
	return WidgetUIDetails{
		widgetState:  feNetworthUi.WidgetStateV2_WIDGET_STATE_V2_UNINITIALIZED,
		displayValue: commontypes.GetTextFromStringFontColourFontStyle("₹ ₹ ₹", colors.ColorOnDarkDisabled700, commontypes.FontStyle_CURRENCY_M),
		borderProperty: &typesUiPb.BorderProperty{
			BorderThickness: 1,
			BorderColor:     colors.ColorOnDarkDisabled700,
			BorderType:      typesUiPb.BorderStyle_DASHED_LINE,
		},
		sduiBorderProperty: &properties.BorderProperty{
			BorderThickness: 1,
			BorderColor:     common.WealthBuilderComponentsBorderColor,
			CornerRadius:    16,
			IsDash:          true,
			DashLength:      4,
			DashGapLength:   4,
		},
	}
}

type WealthBuilderWidget struct {
	widget *feNetworthUi.WidgetV2
}

func NewWealthBuilderWidget(id string) *WealthBuilderWidget {
	return &WealthBuilderWidget{
		widget: &feNetworthUi.WidgetV2{
			Id:   id,
			Type: feNetworthUi.WidgetTypeV2_WIDGET_TYPE_V2_WEALTH_BUILDER,
		},
	}
}

func (w *WealthBuilderWidget) WithWeight(weight float32) *WealthBuilderWidget {
	if w.widget.GetWealthLandingWidgetParams() == nil {
		w.widget.Params = &feNetworthUi.WidgetV2_WealthLandingWidgetParams{
			WealthLandingWidgetParams: &feNetworthUi.WealthBuilderLandingWidgetParams{},
		}
	}
	w.widget.GetWealthLandingWidgetParams().Weight = weight
	return w
}

func (w *WealthBuilderWidget) WithTitle(title *commontypes.Text) *WealthBuilderWidget {
	if title == nil {
		return w
	}
	if w.widget.GetWealthLandingWidgetParams() == nil {
		w.widget.Params = &feNetworthUi.WidgetV2_WealthLandingWidgetParams{
			WealthLandingWidgetParams: &feNetworthUi.WealthBuilderLandingWidgetParams{},
		}
	}
	w.widget.GetWealthLandingWidgetParams().WidgetTitle = title
	return w
}

func (w *WealthBuilderWidget) WithIcon(icon *commontypes.VisualElement) *WealthBuilderWidget {
	if icon == nil {
		return w
	}
	if w.widget.GetWealthLandingWidgetParams() == nil {
		w.widget.Params = &feNetworthUi.WidgetV2_WealthLandingWidgetParams{
			WealthLandingWidgetParams: &feNetworthUi.WealthBuilderLandingWidgetParams{},
		}
	}
	w.widget.GetWealthLandingWidgetParams().BottomRightIcon = icon
	return w
}

func (w *WealthBuilderWidget) WithDisplayValue(displayValue *commontypes.Text) *WealthBuilderWidget {
	if displayValue == nil {
		return w
	}
	if w.widget.GetWealthLandingWidgetParams() == nil {
		w.widget.Params = &feNetworthUi.WidgetV2_WealthLandingWidgetParams{
			WealthLandingWidgetParams: &feNetworthUi.WealthBuilderLandingWidgetParams{
				WealthDisplayDetails: &feNetworthUi.WealthDisplayDetails{},
			},
		}
	}
	if w.widget.GetWealthLandingWidgetParams().GetWealthDisplayDetails() == nil {
		w.widget.GetWealthLandingWidgetParams().WealthDisplayDetails = &feNetworthUi.WealthDisplayDetails{}
	}
	w.widget.GetWealthLandingWidgetParams().GetWealthDisplayDetails().TotalDisplayValue = displayValue
	return w
}

func (w *WealthBuilderWidget) WithCurrencySymbol(currencySymbol *commontypes.Text) *WealthBuilderWidget {
	if currencySymbol == nil {
		return w
	}
	if w.widget.GetWealthLandingWidgetParams() == nil {
		w.widget.Params = &feNetworthUi.WidgetV2_WealthLandingWidgetParams{
			WealthLandingWidgetParams: &feNetworthUi.WealthBuilderLandingWidgetParams{
				WealthDisplayDetails: &feNetworthUi.WealthDisplayDetails{},
			},
		}
	}
	if w.widget.GetWealthLandingWidgetParams().GetWealthDisplayDetails() == nil {
		w.widget.GetWealthLandingWidgetParams().WealthDisplayDetails = &feNetworthUi.WealthDisplayDetails{}
	}
	w.widget.GetWealthLandingWidgetParams().GetWealthDisplayDetails().CurrencySymbol = currencySymbol
	return w
}

func (w *WealthBuilderWidget) WithValue(value *moneyPb.Money) *WealthBuilderWidget {
	if value == nil {
		return w
	}
	if w.widget.GetWealthLandingWidgetParams() == nil {
		w.widget.Params = &feNetworthUi.WidgetV2_WealthLandingWidgetParams{
			WealthLandingWidgetParams: &feNetworthUi.WealthBuilderLandingWidgetParams{
				WealthDisplayDetails: &feNetworthUi.WealthDisplayDetails{},
			},
		}
	}
	if w.widget.GetWealthLandingWidgetParams().GetWealthDisplayDetails() == nil {
		w.widget.GetWealthLandingWidgetParams().WealthDisplayDetails = &feNetworthUi.WealthDisplayDetails{}
	}
	w.widget.TotalValue = types.GetFromBeMoney(value)
	w.widget.GetWealthLandingWidgetParams().GetWealthDisplayDetails().TotalValue = types.GetFromBeMoney(value)
	return w
}

func (w *WealthBuilderWidget) WithRightTag(rightTag *typesUiPb.IconTextComponent) *WealthBuilderWidget {
	if rightTag == nil {
		return w
	}
	if w.widget.GetWealthLandingWidgetParams() == nil {
		w.widget.Params = &feNetworthUi.WidgetV2_WealthLandingWidgetParams{
			WealthLandingWidgetParams: &feNetworthUi.WealthBuilderLandingWidgetParams{},
		}
	}
	w.widget.GetWealthLandingWidgetParams().RightTag = rightTag
	return w
}

func (w *WealthBuilderWidget) WithBottomTag(bottomTag *typesUiPb.IconTextComponent) *WealthBuilderWidget {
	if bottomTag == nil {
		return w
	}
	if w.widget.GetWealthLandingWidgetParams() == nil {
		w.widget.Params = &feNetworthUi.WidgetV2_WealthLandingWidgetParams{
			WealthLandingWidgetParams: &feNetworthUi.WealthBuilderLandingWidgetParams{},
		}
	}
	w.widget.GetWealthLandingWidgetParams().BottomTag = bottomTag
	return w
}

func (w *WealthBuilderWidget) WithDeeplink(deeplink *deeplink.Deeplink) *WealthBuilderWidget {
	if deeplink == nil {
		return w
	}
	if w.widget.GetWealthLandingWidgetParams() == nil {
		w.widget.Params = &feNetworthUi.WidgetV2_WealthLandingWidgetParams{
			WealthLandingWidgetParams: &feNetworthUi.WealthBuilderLandingWidgetParams{},
		}
	}
	w.widget.GetWealthLandingWidgetParams().Deeplink = deeplink
	return w
}

func (w *WealthBuilderWidget) WithBackgroundColor(backgroundColor *widget.BackgroundColour) *WealthBuilderWidget {
	if backgroundColor == nil {
		return w
	}
	if w.widget.GetWealthLandingWidgetParams() == nil {
		w.widget.Params = &feNetworthUi.WidgetV2_WealthLandingWidgetParams{
			WealthLandingWidgetParams: &feNetworthUi.WealthBuilderLandingWidgetParams{},
		}
	}
	w.widget.GetWealthLandingWidgetParams().BgColor = backgroundColor
	return w
}

func (w *WealthBuilderWidget) WithNudge(nudge *commontypes.VisualElement) *WealthBuilderWidget {
	if nudge == nil {
		return w
	}
	if w.widget.GetWealthLandingWidgetParams() == nil {
		w.widget.Params = &feNetworthUi.WidgetV2_WealthLandingWidgetParams{
			WealthLandingWidgetParams: &feNetworthUi.WealthBuilderLandingWidgetParams{},
		}
	}
	w.widget.GetWealthLandingWidgetParams().TopRightNudge = nudge
	return w
}

func (w *WealthBuilderWidget) WithBorderProperty(borderProperty *typesUiPb.BorderProperty) *WealthBuilderWidget {
	if borderProperty == nil {
		return w
	}
	if w.widget.GetWealthLandingWidgetParams() == nil {
		w.widget.Params = &feNetworthUi.WidgetV2_WealthLandingWidgetParams{
			WealthLandingWidgetParams: &feNetworthUi.WealthBuilderLandingWidgetParams{},
		}
	}
	w.widget.GetWealthLandingWidgetParams().BorderProperty = borderProperty
	return w
}

func (w *WealthBuilderWidget) WithCacheControl(cacheControl feNetworthUi.CacheControlV2) *WealthBuilderWidget {
	w.widget.CacheControl = cacheControl
	return w
}

func (w *WealthBuilderWidget) WithWidgetState(widgetState feNetworthUi.WidgetStateV2) *WealthBuilderWidget {
	w.widget.State = widgetState
	return w
}

func (w *WealthBuilderWidget) WithWidgetAnalyticsPayload(widgetAnalyticsPayload string) *WealthBuilderWidget {
	w.widget.WidgetAnalyticsPayload = widgetAnalyticsPayload
	return w
}

func (w *WealthBuilderWidget) WithSduiBorderProperty(sduiBorderProperty *properties.BorderProperty) *WealthBuilderWidget {
	if sduiBorderProperty == nil {
		return w
	}
	if w.widget.GetWealthLandingWidgetParams() == nil {
		w.widget.Params = &feNetworthUi.WidgetV2_WealthLandingWidgetParams{
			WealthLandingWidgetParams: &feNetworthUi.WealthBuilderLandingWidgetParams{},
		}
	}
	w.widget.GetWealthLandingWidgetParams().SduiBorderProperty = sduiBorderProperty
	return w
}

func (w *WealthBuilderWidget) Build() *feNetworthUi.WidgetV2 {
	return w.widget
}
