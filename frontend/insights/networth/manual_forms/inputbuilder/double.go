// nolint:dupl
package inputbuilder

import (
	wrappersPb "google.golang.org/protobuf/types/known/wrapperspb"

	networthPb "github.com/epifi/gamma/api/frontend/insights/networth"
	networthEnumsPb "github.com/epifi/gamma/api/frontend/insights/networth/enums"
)

type DoubleBuilder struct {
	*networthPb.NetWorthManualFormInputComponent
}

func NewDoubleBuilder(title, placeholderText, fieldName string) *DoubleBuilder {
	component := networthPb.NewNetWorthManualFormInputComponent(fieldName, networthEnumsPb.NetworthManualFormInputStyle_NETWORTH_MANUAL_FORM_INPUT_STYLE_TEXT_FIELD)
	component.WithMandatoryDisplayTitle(title)
	component.WithMandatoryPlaceholderText(placeholderText)
	component.InputData = WithInputDataDouble(fieldName)
	return &DoubleBuilder{NetWorthManualFormInputComponent: component}
}

func NewOptionalDoubleBuilder(title, placeholderText, fieldName string) *DoubleBuilder {
	component := networthPb.NewNetWorthManualFormInputComponent(fieldName, networthEnumsPb.NetworthManualFormInputStyle_NETWORTH_MANUAL_FORM_INPUT_STYLE_TEXT_FIELD)
	component.MakeOptional()
	component.WithDefaultTitle(title)
	component.WithDefaultPlaceholderText(placeholderText)
	component.InputData = WithInputDataDouble(fieldName)
	return &DoubleBuilder{NetWorthManualFormInputComponent: component}
}

func (d *DoubleBuilder) WithValue(value float64) {
	d.NetWorthManualFormInputComponent.GetInputData().GetSingleOption().GetInputValue().GetDoubleData().Data = &wrappersPb.DoubleValue{Value: value}
	d.NetWorthManualFormInputComponent.GetInputData().GetSingleOption().GetInputOptionData().GetInputValue().GetDoubleData().Data = &wrappersPb.DoubleValue{Value: value}
}

func (d *DoubleBuilder) WithValidation(validation *networthPb.DoubleType_DoubleValidation) {
	d.NetWorthManualFormInputComponent.GetInputData().GetSingleOption().GetInputValue().GetDoubleData().Validation = validation
	d.NetWorthManualFormInputComponent.GetInputData().GetSingleOption().GetInputOptionData().GetInputValue().GetDoubleData().Validation = validation
}

func (d *DoubleBuilder) Build() *networthPb.NetWorthManualFormInputComponent {
	return d.NetWorthManualFormInputComponent
}

func WithInputDataDouble(fieldName string) *networthPb.NetWorthManualInputData {
	return &networthPb.NetWorthManualInputData{
		FieldName: fieldName,
		DataType:  networthEnumsPb.NetworthManualFormInputDataType_NETWORTH_MANUAL_FORM_INPUT_DATA_TYPE_DOUBLE,
		Input: &networthPb.NetWorthManualInputData_SingleOption{
			SingleOption: &networthPb.SingleInputOption{
				InputValue: &networthPb.InputOptionValue{
					Value: &networthPb.InputOptionValue_DoubleData{
						DoubleData: &networthPb.DoubleType{
							Validation: &networthPb.DoubleType_DoubleValidation{
								MinVal:               0,
								MaxVal:               1000000000,
								ValidationFailureMsg: "Value should be in between 0 and 100Cr",
							},
						},
					},
				},
				InputOptionData: &networthPb.InputOptionData{
					InputValue: &networthPb.InputOptionValue{
						Value: &networthPb.InputOptionValue_DoubleData{
							DoubleData: &networthPb.DoubleType{
								Validation: &networthPb.DoubleType_DoubleValidation{
									MinVal:               0,
									MaxVal:               1000000000,
									ValidationFailureMsg: "Value should be in between 0 and 100Cr",
								},
							},
						},
					},
				},
			},
		},
	}
}
