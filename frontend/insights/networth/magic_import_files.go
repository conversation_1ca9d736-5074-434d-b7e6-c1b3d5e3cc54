package networth

import (
	"context"
	"fmt"
	"time"

	anyPb "google.golang.org/protobuf/types/known/anypb"

	"github.com/epifi/be-common/pkg/frontend/app/apputils"

	feWealthAnalyserPb "github.com/epifi/gamma/api/insights/secrets/frontend"
	cxScreenTypes "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/inapphelp"
	deeplinkSecrets "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/secrets"
	"github.com/epifi/gamma/pkg/networth"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/epifi/be-common/pkg/colors"

	"github.com/epifi/be-common/api/typesv2/common/ui/widget"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	moneyPb "github.com/epifi/be-common/pkg/money"

	"github.com/epifi/be-common/pkg/epificontext"

	"github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	connectedAccountEnumsPb "github.com/epifi/gamma/api/connected_account/enums"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	headerPb "github.com/epifi/gamma/api/frontend/header"
	feNetworthPb "github.com/epifi/gamma/api/frontend/insights/networth"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	networthBeFePb "github.com/epifi/gamma/api/insights/networth/frontend"
	magicimportPb "github.com/epifi/gamma/api/insights/networth/magicimport"
	modelPb "github.com/epifi/gamma/api/insights/networth/model"
	typesV2 "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/magicimport"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/insights/networth/common"
	networthDeeplink "github.com/epifi/gamma/frontend/insights/networth/deeplink"
	"github.com/epifi/gamma/frontend/insights/networth/manual_forms/formbuilder"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/frontend/header"
)

var (
	assetsExcludedForMagicImport = []typesV2.InvestmentInstrumentType{
		typesV2.InvestmentInstrumentType_MUTUAL_FUNDS,
		typesV2.InvestmentInstrumentType_INDIAN_STOCKS,
		typesV2.InvestmentInstrumentType_NPS,
	}

	assetsForConnectedAccountsImport = []typesV2.InvestmentInstrumentType{
		typesV2.InvestmentInstrumentType_INDIAN_STOCKS,
		typesV2.InvestmentInstrumentType_NPS,
	}
)

func (s *Service) MagicImportFiles(ctx context.Context, req *feNetworthPb.MagicImportFilesRequest) (*feNetworthPb.MagicImportFilesResponse, error) {
	var (
		errRes = func(err error) *feNetworthPb.MagicImportFilesResponse {
			return &feNetworthPb.MagicImportFilesResponse{
				RespHeader: &headerPb.ResponseHeader{
					Status: rpc.StatusInternalWithDebugMsg(err.Error()),
				},
				ErrorStateScreen: s.getErrorStateScreen(rpc.StatusInternal().GetCode()),
			}
		}
	)
	// create context independent of client context, this will avoid context cancelling of rpc in case user closes the app
	var cancelCtx func()
	ctx, cancelCtx = context.WithDeadline(epificontext.CloneCtx(ctx), time.Now().Add(90*time.Second))
	defer cancelCtx()
	res, err := s.networthClient.MagicImportFiles(ctx, &networthPb.MagicImportFilesRequest{
		ActorId: req.GetReq().GetAuth().GetActorId(),
		Files:   req.GetFiles(),
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		if res.GetStatus().GetCode() == uint32(networthPb.MagicImportFilesResponse_MALFORMED_LLM_RESPONSE) {
			return &feNetworthPb.MagicImportFilesResponse{
				RespHeader: &headerPb.ResponseHeader{
					Status: rpc.NewStatus(uint32(feNetworthPb.MagicImportFilesResponse_MALFORMED_LLM_RESPONSE), "", "Malformed LLM response"),
				},
				ErrorStateScreen: s.getErrorStateScreen(uint32(feNetworthPb.MagicImportFilesResponse_MALFORMED_LLM_RESPONSE)),
			}, nil
		}
		logger.Error(ctx, "error in MagicImportFiles API", zap.Error(rpcErr))
		return errRes(rpcErr), nil
	}
	var (
		importedAssetsListComponents             = make([]*feNetworthPb.ImportedAssetsListComponent, 0)
		investmentInstrumentTypeToAssetItemsList = make(map[typesV2.InvestmentInstrumentType][]*feNetworthPb.ImportedAssetsListItem)
		unconventionalAssetTypeToAssetItemsList  = make(map[string][]*feNetworthPb.ImportedAssetsListItem)
		totalConventionalAssetsCount             int32
	)

	totalValue := moneyPb.ZeroINR().GetPb()
	unconventionalAssetDetailsList := lo.Filter(res.GetMagicImportDetails().GetAssetDetailsList(), func(assetDetails *magicimportPb.MagicImportAssetDetails, _ int) bool {
		return assetDetails.GetAssetType() == magicimportPb.MagicImportAssetType_MAGIC_IMPORT_ASSET_TYPE_UNCONVENTIONAL
	})
	conventionalAssetDetailsList := lo.Filter(res.GetMagicImportDetails().GetAssetDetailsList(), func(assetDetails *magicimportPb.MagicImportAssetDetails, _ int) bool {
		return assetDetails.GetAssetType() == magicimportPb.MagicImportAssetType_MAGIC_IMPORT_ASSET_TYPE_CONVENTIONAL
	})

	if len(conventionalAssetDetailsList) == 0 && len(unconventionalAssetDetailsList) == 0 {
		logger.Info(ctx, "no assets found")
		return &feNetworthPb.MagicImportFilesResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpc.NewStatus(uint32(feNetworthPb.MagicImportFilesResponse_NO_ASSETS_FOUND), "", "No assets found"),
			},
			ErrorStateScreen: s.getErrorStateScreen(uint32(feNetworthPb.MagicImportFilesResponse_NO_ASSETS_FOUND)),
		}, nil
	}

	for _, assetDetails := range conventionalAssetDetailsList {
		investmentDeclaration := assetDetails.GetConventionalAssetDetails().GetInvestmentDeclaration()
		assetExcluded := isAssetExcludedForMagicImport(investmentDeclaration.GetInstrumentType())
		logger.Debug(ctx, "Processing asset excluded", zap.Any("assetExcluded", assetExcluded))
		if assetDetails.GetConventionalAssetDetails().GetEstimatedValue() != nil && !assetExcluded {
			totalValue, err = moneyPb.Sum(totalValue, assetDetails.GetConventionalAssetDetails().GetEstimatedValue())
			if err != nil {
				logger.Error(ctx, "error in summation of asset estimated values", zap.Error(err))
				return errRes(err), nil
			}
		}
		manualForm, formIdentifier, err := s.getNetworthManualFormForMagicImport(ctx, investmentDeclaration)
		if err != nil {
			logger.Error(ctx, "error in getNetworthManualFormForMagicImport", zap.Error(err))
			return errRes(err), nil
		}
		validateErr := s.validateFormData(ctx, manualForm, investmentDeclaration.GetActorId(), formIdentifier)
		if validateErr != nil && !errors.Is(validateErr, epifierrors.ErrInvalidArgument) {
			logger.Error(ctx, "error in validateFormData", zap.Error(validateErr))
			return errRes(validateErr), nil
		}
		var (
			importErrorITC          *ui.IconTextComponent
			isAssetListItemSelected = true
			isEditable              = true
			isSelectable            = true
		)
		hasMissingInfo := errors.Is(validateErr, epifierrors.ErrInvalidArgument)
		if hasMissingInfo {
			importErrorITC = ui.NewITC().WithTexts(commontypes.GetPlainStringText("We couldn’t get all the details. Please add them manually"))
			// isAssetListItemSelected is enabled only in case of no missing info
			// Client uses isAssetListItemSelected to compute the networth of an asset, this is to ignore assets when details are incomplete
			isAssetListItemSelected = false
		}
		if assetExcluded {
			// Have an empty string as we need not show any error message in case of excluded assets
			importErrorITC = ui.NewITC().WithTexts(commontypes.GetPlainStringText(""))
			isEditable = false
			isSelectable = false
			isAssetListItemSelected = false
		} else {
			totalConventionalAssetsCount++
		}

		assetValueStr := moneyPb.ToDisplayStringInIndianFormat(assetDetails.GetConventionalAssetDetails().GetEstimatedValue(), 0, true)
		investmentInstrumentTypeToAssetItemsList[investmentDeclaration.GetInstrumentType()] = append(investmentInstrumentTypeToAssetItemsList[investmentDeclaration.GetInstrumentType()], &feNetworthPb.ImportedAssetsListItem{
			AssetName:  commontypes.GetTextFromStringFontColourFontStyleFontAlignment(assetDetails.GetConventionalAssetDetails().GetInvestmentName(), colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_SUBTITLE_M, commontypes.Text_ALIGNMENT_LEFT),
			AssetValue: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyleFontAlignment(assetValueStr, colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_SUBTITLE_M, commontypes.Text_ALIGNMENT_LEFT)),
			EditDetails: &feNetworthPb.ImportedAssetsListItemEditDetails{
				Title:          commontypes.GetPlainStringText("Modify asset details"),
				ManualForm:     manualForm,
				FormIdentifier: formIdentifier,
			},
			IsEditable:     isEditable,
			IsSelected:     isAssetListItemSelected,
			IsSelectable:   isSelectable,
			HasMissingInfo: hasMissingInfo,
			ImportError:    importErrorITC,
			FileName:       assetDetails.GetConventionalAssetDetails().GetFileName(),
		})
	}

	logger.Debug(ctx, "Total value of all conventional assets", zap.Any("totalValue", totalValue))
	for investmentInstrumentType, assetItems := range investmentInstrumentTypeToAssetItemsList {
		assetName := networth.ManualInstrumentTypeToAssetDisplayName[investmentInstrumentType]
		title := fmt.Sprintf("%d %v", len(assetItems), assetName)
		importedAssetsListComponents = append(importedAssetsListComponents, s.getImportedAssetsListComponent(ctx, investmentInstrumentType, title, assetItems))
	}

	for _, assetDetails := range unconventionalAssetDetailsList {
		assetType := assetDetails.GetUnconventionalAssetDetails().GetAssetType()
		unconventionalAssetTypeToAssetItemsList[assetType] = append(unconventionalAssetTypeToAssetItemsList[assetType], &feNetworthPb.ImportedAssetsListItem{
			AssetName: commontypes.GetTextFromStringFontColourFontStyleFontAlignment(assetDetails.GetUnconventionalAssetDetails().GetAssetName(), colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_SUBTITLE_M, commontypes.Text_ALIGNMENT_LEFT),
			/*
				Added \n to ensure that the text does not overflow and is properly wrapped in the UI.
				UI before \n: "disabled icon" + text
				UI after \n : "disabled icon" + "\n" + text
			*/
			AssetValue:   ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyleFontAlignment("\n"+assetDetails.GetUnconventionalAssetDetails().GetAiCommentary(), colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_SUBTITLE_M, commontypes.Text_ALIGNMENT_LEFT)),
			IsEditable:   false,
			IsSelected:   false,
			IsSelectable: false,
		})
	}
	for assetType, assetItems := range unconventionalAssetTypeToAssetItemsList {
		title := fmt.Sprintf("%d %v", len(assetItems), assetType)
		importedAssetsListComponents = append(importedAssetsListComponents, s.getImportedAssetsListComponent(ctx, typesV2.InvestmentInstrumentType_INVESTMENT_INSTRUMENT_TYPE_UNSPECIFIED, title, assetItems))
	}

	assetsInfoTitle := ""
	addToNetworthButton := &deeplinkPb.Cta{
		Type:              deeplinkPb.Cta_CUSTOM,
		Text:              "Add to Networth",
		DisplayTheme:      deeplinkPb.Cta_PRIMARY,
		CtaLeadingImage:   commontypes.GetVisualElementFromUrlHeightAndWidth(common.MagicImportAddToNetworthButton, 24, 24),
		LeftImgTxtPadding: 4,
	}
	shareButtonItc := ui.NewITC().WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(common.MagicImportShareButton, 24, 24)).
		WithContainerProperties(&ui.IconTextComponent_ContainerProperties{
			CornerRadius: 40,
			BackgroundColour: &widget.BackgroundColour{
				Colour: &widget.BackgroundColour_BlockColour{
					BlockColour: "#28292B",
				},
			},
			BgBorderColour: &widget.BackgroundColour{
				Colour: &widget.BackgroundColour_RadialGradient{
					RadialGradient: &widget.RadialGradient{
						OuterRadius: 40,
						Colours:     []string{"#4DDCF3EE", "#0DDCF3EE"},
					},
				},
			},
		})

	totalValueStr := moneyPb.ToDisplayStringInIndianFormat(totalValue, 0, true)
	assetsInfo := &ui.VerticalKeyValuePair{
		Title: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(assetsInfoTitle, "#929599", commontypes.FontStyle_SUBTITLE_M)),
		Value: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(totalValueStr, "#AFD2A2", commontypes.FontStyle_DISPLAY_4XL)),
	}
	switch totalConventionalAssetsCount {
	case 0:
		assetsInfoTitle = "No assets found"
		if apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.feConfig.NetworthConfig().MagicImportUnconventionalAssetsV2()) {
			addToNetworthButton = nil
			shareButtonItc = ui.NewITC().WithTexts(commontypes.GetTextFromHtmlStringFontColourFontStyle("Share", "#FFFFFF", commontypes.FontStyle_BUTTON_M)).
				WithContainerBackgroundColor("#00B899").
				WithContainerPadding(12, 16, 12, 16).
				WithContainerCornerRadius(40)
			assetsInfo = &ui.VerticalKeyValuePair{
				Title: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(res.GetMagicImportDetails().GetOneWordAiCommentary(), "#FFFFFF", commontypes.FontStyle_DISPLAY_4XL)),
			}
		}
	case 1:
		assetsInfoTitle = "We found 1 asset worth"
	default:
		assetsInfoTitle = fmt.Sprintf("We found %v assets worth", totalConventionalAssetsCount)
	}

	shareText := "Just tracked some of my most valuable assets with Magic Lens on Fi.\nFrom ESOPs and gold to gadgets, vehicles, crypto and more – you can track it all.\nSnap, analyse, and add – it was that simple.\n👉Try it here: https://fi.onelink.me/GvZH/8rotze4l"
	if len(unconventionalAssetDetailsList) > 0 {
		shareText = "Been having fun adding the invaluable things in my life to my Net Worth with Magic Lens on Fi.\nSnap, analyse, and add - it was that simple.\n👉Try it here: https://fi.onelink.me/GvZH/8rotze4l"
	}
	feedbackEngineScreenOptions, err := anyPb.New(
		&cxScreenTypes.FeedbackEngineScreenOptions{
			FlowIdentifierDetails: &cxScreenTypes.FlowIdentifierDetails{
				FlowIdentifierType: typesV2.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_CTA,
				FlowIdentifier:     typesV2.FeedbackCTAFlowIdentifier_FEEDBACK_CTA_FLOW_IDENTIFIER_MAGIC_LENS.String(),
			},
		})

	if err != nil {
		logger.Error(ctx, "error in marshalling feedback engine screen options", zap.Error(err))
	}
	feedbackEngineDl := &deeplinkPb.Deeplink{
		Screen:          deeplinkPb.Screen_FEEDBACK_ENGINE_SCREEN,
		ScreenOptionsV2: feedbackEngineScreenOptions,
	}
	importedAssetsListScreen := &feNetworthPb.MagicImportedAssetsListScreen{
		HeaderBar: &ui.HeaderBar{
			CenterItc: ui.NewITC().WithTexts(commontypes.GetPlainStringText(common.MagicImportAssetsScreenTitle)),
			LeftItc:   ui.NewITC().WithLeftVisualElement(commontypes.GetVisualElementImageFromUrl(common.MagicImportBackIcon)),
		},
		AssetsSummary: &feNetworthPb.AssetsSummary{
			Title:      commontypes.GetTextFromHtmlStringFontColourFontStyle("Your analysis is ready!", colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_HEADLINE_L),
			AssetsInfo: assetsInfo,
			FeedbackComponent: &feNetworthPb.FeedbackComponent{
				ThumbsUpNormalView: ui.NewITC().
					WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(common.MagicImportThumbsUpUnselected, 44, 44)).
					WithDeeplink(feedbackEngineDl),
				ThumbsDownNormalView: ui.NewITC().
					WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(common.MagicImportThumbsDownUnselected, 44, 44)).
					WithDeeplink(feedbackEngineDl),
				ThumbsUp: &feNetworthPb.FeedbackView{
					NormalView: commontypes.GetVisualElementFromUrlHeightAndWidth(common.MagicImportThumbsUpUnselected, 44, 44),
				},
				ThumbsDown: &feNetworthPb.FeedbackView{
					NormalView: commontypes.GetVisualElementFromUrlHeightAndWidth(common.MagicImportThumbsDownUnselected, 44, 44),
				},
			},
			AiCommentary: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyleFontAlignment(res.GetMagicImportDetails().GetAiCommentary(), "#AFD2A2", commontypes.FontStyle_BODY_S, commontypes.Text_ALIGNMENT_CENTER)).
				WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(common.MagicImportSparkleIcon, 32, 32)).
				WithRightVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(common.MagicImportSparkleIcon, 32, 32)),
		},
		ImportedAssetsListComponents: importedAssetsListComponents,
		FooterComponent: &feNetworthPb.FooterComponent{
			Disclaimer:          commontypes.GetTextFromHtmlStringFontColourFontStyle(`Valuations mentioned are only estimates and may not be <br> accurate, exercise discretion. T&C apply. <a style='color: #00B899; text-decoration: none;' href="https://fi.money/blog/tnc#magic-lens"> Know more</a>`, "#929599", commontypes.FontStyle_BODY_XS),
			ShareButton:         shareButtonItc,
			AddToNetworthButton: addToNetworthButton,
			ShareImage:          shareImage,
			ShareText:           shareText,
		},
	}
	return &feNetworthPb.MagicImportFilesResponse{
		RespHeader:               header.SuccessRespHeader(),
		ImportedAssetsListScreen: importedAssetsListScreen,
	}, nil
}

func (s *Service) getImportedAssetsListComponent(ctx context.Context, instrumentType typesV2.InvestmentInstrumentType, title string, assetItems []*feNetworthPb.ImportedAssetsListItem) *feNetworthPb.ImportedAssetsListComponent {
	assetsComponentHeader := s.getAssetsComponentHeader(ctx, instrumentType)
	return &feNetworthPb.ImportedAssetsListComponent{
		AssetsComponentHeader:   assetsComponentHeader,
		IsComponentDisabled:     isAssetExcludedForMagicImport(instrumentType),
		Title:                   commontypes.GetPlainStringText(title),
		ImportedAssetsListItems: assetItems,
		BorderColor: &widget.BackgroundColour{
			Colour: &widget.BackgroundColour_RadialGradient{
				RadialGradient: &widget.RadialGradient{
					OuterRadius: 16,
					Colours:     []string{"#4DDCF3EE", "#0DDCF3EE"},
				},
			},
		},
		BackgroundColour: &widget.BackgroundColour{
			Colour: &widget.BackgroundColour_BlockColour{
				BlockColour: "#28292B",
			},
		},
		DividerColor: &widget.BackgroundColour{
			Colour: &widget.BackgroundColour_BlockColour{
				BlockColour: "#313234",
			},
		},
	}
}

func (s *Service) getAssetsComponentHeader(ctx context.Context, instrumentType typesV2.InvestmentInstrumentType) *feNetworthPb.AssetsComponentHeader {
	if !isAssetExcludedForMagicImport(instrumentType) {
		return nil
	}
	var connectAssetsCtaDl *deeplinkPb.Deeplink
	switch {
	case lo.Contains(assetsForConnectedAccountsImport, instrumentType):
		logger.Debug(ctx, "Creating header for connected accounts import", zap.Any("instrumentType", instrumentType))
		connectAssetsCtaDl = connectedAccountsFlowDl(ctx, instrumentType)
	case instrumentType == typesV2.InvestmentInstrumentType_MUTUAL_FUNDS:
		connectAssetsCtaDl = deeplinkv3.GetDeeplinkV3WithoutError(
			deeplinkPb.Screen_WEALTH_ANALYSER_REPORT_SCREEN,
			&deeplinkSecrets.WealthAnalyserReportScreenOptions{ReportType: feWealthAnalyserPb.WealthAnalyserReportType_WEALTH_ANALYSER_REPORT_TYPE_MUTUAL_FUND.String()})
	default:
		logger.Error(ctx, "no handling for instrument type", zap.Any("instrumentType", instrumentType))
		connectAssetsCtaDl = connectedAccountsFlowDl(ctx, typesV2.InvestmentInstrumentType_INVESTMENT_INSTRUMENT_TYPE_UNSPECIFIED)
	}
	titleText := getMagicImportAssetsComponentHeaderTitle(instrumentType)
	return &feNetworthPb.AssetsComponentHeader{
		Title: commontypes.GetPlainStringText(titleText).WithFontColor(colors.ColorSupportingMoss100).WithFontStyle(commontypes.FontStyle_SUBTITLE_M).WithAlignment(commontypes.Text_ALIGNMENT_LEFT),
		Icon:  commontypes.GetVisualElementFromUrlHeightAndWidth(common.MagicImportAssetComponentHeaderIcon, 40, 40),
		ConnectAssetsCta: ui.NewITC().WithTexts(commontypes.GetPlainStringText("Connect via OTP").WithFontColor(colors.ColorForest).WithFontStyle(commontypes.FontStyle_BUTTON_S)).
			WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(common.MagicImportLinkAssetIcon, 20, 20)).
			WithDeeplink(connectAssetsCtaDl).WithContainerBackgroundColor(colors.ColorOnDarkHighEmphasis).WithContainerCornerRadius(40).WithContainerPaddingSymmetrical(16, 6),
		BackgroundColour: &widget.BackgroundColour{
			Colour: &widget.BackgroundColour_LinearGradient{
				LinearGradient: &widget.LinearGradient{
					Degree: 180,
					LinearColorStops: []*widget.ColorStop{
						{
							Color:          "#40648E4D",
							StopPercentage: 0,
						},
						{
							Color:          "#4037522A",
							StopPercentage: 100,
						},
					},
				},
			},
		},
	}
}

func connectedAccountsFlowDl(ctx context.Context, instrumentType typesV2.InvestmentInstrumentType) *deeplinkPb.Deeplink {
	instrumentTypeToCaFlow := map[typesV2.InvestmentInstrumentType]connectedAccountEnumsPb.CAFlowName{
		typesV2.InvestmentInstrumentType_NPS:           connectedAccountEnumsPb.CAFlowName_CA_FLOW_NAME_NPS,
		typesV2.InvestmentInstrumentType_INDIAN_STOCKS: connectedAccountEnumsPb.CAFlowName_CA_FLOW_NAME_NET_WORTH_IND_STOCKS,
	}
	caFlow, ok := instrumentTypeToCaFlow[instrumentType]
	if !ok {
		return networthDeeplink.ConnectedAccountDashboardDeeplink()
	}
	connectAccDeeplink, err := networthDeeplink.ConnectedFiTypeSpecificAccountsDeeplink(caFlow)
	if err != nil {
		// gracefully handle the error and return the default ca flow deeplink
		logger.Error(ctx, "error in getting connected accounts deeplink", zap.Error(err))
		return networthDeeplink.ConnectedAccountDashboardDeeplink()
	}
	return connectAccDeeplink
}

func getMagicImportAssetsComponentHeaderTitle(instrumentType typesV2.InvestmentInstrumentType) string {
	switch instrumentType {
	case typesV2.InvestmentInstrumentType_NPS:
		return "Connect your NPS accounts securely to unlock portfolio insights in 2 mins"
	case typesV2.InvestmentInstrumentType_INDIAN_STOCKS:
		return "Connect your Indian Stocks securely to unlock portfolio insights in 2 mins"
	default:
		return "Connect your Funds securely to unlock portfolio insights in 2 mins"
	}
}

func isAssetExcludedForMagicImport(instrumentType typesV2.InvestmentInstrumentType) bool {
	return lo.Contains(assetsExcludedForMagicImport, instrumentType)
}

func (s *Service) validateFormData(ctx context.Context, manualForm *feNetworthPb.NetWorthManualForm, actorId string, formIdentifier *typesV2.ManualAssetFormIdentifier) error {
	emptyForm, err := s.formProcessor.BuildEmptyForm(ctx, &formbuilder.BuildFormRequest{
		ActorId:        actorId,
		FormIdentifier: formIdentifier,
	})
	if err != nil {
		return fmt.Errorf("failure in build empty form : %w", err)
	}

	_, err = s.validateAndSetInputDataInForm(ctx, manualForm.GetAllInputData(), emptyForm)
	if errors.Is(err, epifierrors.ErrInvalidArgument) {
		logger.Info(ctx, "missing data in manual form", zap.Error(err))
	}
	if err != nil {
		return fmt.Errorf("failure in validate and set input data : %w", err)
	}
	return err
}

func (s *Service) getNetworthManualFormForMagicImport(ctx context.Context, investmentDeclaration *modelPb.InvestmentDeclaration) (*feNetworthPb.NetWorthManualForm, *typesV2.ManualAssetFormIdentifier, error) {
	manualFormIdentifier := &networthBeFePb.ManualFormIdentifier{
		Identifier: &networthBeFePb.ManualFormIdentifier_MagicImportFormIdentifier{
			MagicImportFormIdentifier: &networthBeFePb.MagicImportFormIdentifier{
				InstrumentType: investmentDeclaration.GetInstrumentType(),
			},
		},
	}

	buf, err := protojson.Marshal(manualFormIdentifier)
	if err != nil {
		logger.Error(ctx, "error in marshaling manual form identifier", zap.Error(err))
		return nil, nil, err
	}
	manualAssetFormIdentifier := &typesV2.ManualAssetFormIdentifier{
		Identifier: &typesV2.ManualAssetFormIdentifier_GenericIdentifier{
			GenericIdentifier: string(buf),
		},
	}
	form, buildErr := s.formProcessor.BuildForm(ctx, &formbuilder.BuildFormRequest{
		ActorId:               investmentDeclaration.GetActorId(),
		InvestmentDeclaration: investmentDeclaration,
		FormIdentifier:        manualAssetFormIdentifier,
	})
	if buildErr != nil {
		logger.Error(ctx, "error in BuildForm API", zap.Error(buildErr))
		return nil, nil, errors.Wrapf(buildErr, "failed to build form for magic import identifier")
	}
	logger.Debug(ctx, "Build form", zap.Any("form", form))
	// todo(Rohit): migrate this logic to BuildForm
	form.SubmitCta = ui.NewITC().
		WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Confirm", colors.ColorSnow, commontypes.FontStyle_BUTTON_M)).
		WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(common.MagicImportStrikeIcon, 24, 24)).
		WithContainerPadding(12, 12, 12, 12).
		WithContainerBackgroundColor(colors.ColorLightPrimaryAction).
		WithContainerCornerRadius(40)
	for i := range form.ComponentsSections {
		form.ComponentsSections[i].Header = nil
	}
	return form, manualAssetFormIdentifier, nil
}

func (s *Service) getErrorStateScreen(statusCode uint32) *magicimport.ErrorStateScreen {
	var (
		text    string
		message string
		ctaType deeplinkPb.Cta_Type
	)
	switch statusCode {
	case uint32(feNetworthPb.MagicImportFilesResponse_MALFORMED_LLM_RESPONSE):
		text = "Uh oh! some issue while analysing at our end"
		message = "Please try again"
		ctaType = deeplinkPb.Cta_RETRY
	case uint32(feNetworthPb.MagicImportFilesResponse_NO_ASSETS_FOUND):
		text = "Asset details not found"
		message = "Try scanning or uploading again"
		ctaType = deeplinkPb.Cta_DONE
	default:
		text = "Something went wrong"
		message = "Please try again"
		ctaType = deeplinkPb.Cta_RETRY
	}

	return &magicimport.ErrorStateScreen{
		Title:   commontypes.GetTextFromStringFontColourFontStyle(text, colors.ColorSupportingCherry100, commontypes.FontStyle_HEADLINE_L),
		Message: commontypes.GetTextFromStringFontColourFontStyle(message, colors.ColorOnlightLowEmphasis, commontypes.FontStyle_SUBTITLE_M),
		Icon:    commontypes.GetVisualElementFromUrlHeightAndWidth(common.MagicImportErrorIcon, 49, 49),
		ActionCtas: []*deeplinkPb.Cta{
			{
				Text:         "Try again",
				Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
				DisplayTheme: deeplinkPb.Cta_PRIMARY,
				Type:         ctaType,
				CtaLeadingImage: commontypes.GetVisualElementImageFromUrl(common.MagicImportTryAgain).
					WithProperties(&commontypes.VisualElementProperties{Width: 24, Height: 24}),
				LeftImgTxtPadding: 4,
			},
		},
	}
}
