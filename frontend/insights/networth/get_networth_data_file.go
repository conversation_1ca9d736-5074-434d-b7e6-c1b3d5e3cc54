package networth

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epificontext"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/frontend/header"
	feNetworthPb "github.com/epifi/gamma/api/frontend/insights/networth"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	networthEnums "github.com/epifi/gamma/api/insights/networth/enums"
	"github.com/epifi/gamma/frontend/insights/networth/events"
)

// GetNetworthDataFile generates a comprehensive financial data file containing user's networth summary,
// mutual fund holdings, account aggregator data, credit reports, EPF details, and transaction history (includes both bank and mf transactions).
// Returns base64-encoded JSON data as a downloadable text file, with concurrent data fetching for performance.
func (s *Service) GetNetworthDataFile(ctx context.Context, req *feNetworthPb.GetNetworthDataFileRequest) (*feNetworthPb.GetNetworthDataFileResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()
	reqPayload, err := s.getNetworthDataFilePurposeFromPayload(req.GetReqPayload())
	if err != nil {
		logger.Error(ctx, "error in getting networth data file purpose", zap.Error(err))
		return &feNetworthPb.GetNetworthDataFileResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInvalidArgument(),
			},
		}, nil
	}

	var networthDataFileType networthEnums.NetworthDataFileType
	switch reqPayload.GetNetworthDataFileType() {
	case feNetworthPb.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_NETWORTH_DATA:
		networthDataFileType = networthEnums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_NETWORTH_DATA
	case feNetworthPb.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_BANK_DETAILS_DATA:
		networthDataFileType = networthEnums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_BANK_DETAILS_DATA
	default:
		// to handle backward compatible changes, check this once before merging
		networthDataFileType = networthEnums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_NETWORTH_DATA
	}

	networthDataRes, networthDataErr := s.networthClient.GetNetworthDataFile(ctx, &networthPb.GetNetworthDataFileRequest{
		ActorId:              actorId,
		NetworthDataFileType: networthDataFileType,
	})
	if rpcErr := epifigrpc.RPCError(networthDataRes, networthDataErr); rpcErr != nil {
		logger.Error(ctx, "error in getting networth data file", zap.Error(rpcErr))
		return &feNetworthPb.GetNetworthDataFileResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternal(),
			},
		}, nil
	}

	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		s.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), events.NewNetworthDataFileGenerationEvent(actorId))
	})

	return &feNetworthPb.GetNetworthDataFileResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		NetworthFile: networthDataRes.GetNetworthFile(),
	}, nil
}

func (s *Service) getNetworthDataFilePurposeFromPayload(requestPayload string) (*feNetworthPb.NetworthDataFileRequestPayload, error) {
	fileRequestPayload := &feNetworthPb.NetworthDataFileRequestPayload{}
	if requestPayload == "" {
		return fileRequestPayload, nil
	}
	unmarshalOptions := protojson.UnmarshalOptions{DiscardUnknown: true}
	if unmarshalErr := unmarshalOptions.Unmarshal([]byte(requestPayload), fileRequestPayload); unmarshalErr != nil {
		return nil, errors.Wrap(unmarshalErr, "error in unmarshaling payload")
	}
	return fileRequestPayload, nil
}
