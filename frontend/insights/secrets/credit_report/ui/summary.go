package ui

import (
	"context"
	"fmt"
	"strconv"
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	pkgColors "github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/epifigrpc"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/analyser/creditscore/constant"
	pb "github.com/epifi/gamma/api/frontend/analyser"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	secretsFePb "github.com/epifi/gamma/api/frontend/insights/secrets"
	ui1 "github.com/epifi/gamma/api/frontend/investment/ui"
	modelPb "github.com/epifi/gamma/api/insights/networth/model"
	secretsConfigPb "github.com/epifi/gamma/api/insights/secrets/config"
	secretsModelPb "github.com/epifi/gamma/api/insights/secrets/model"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/analyser/details_modal"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/secrets"
	"github.com/epifi/gamma/api/typesv2/ui"
	secretsUiPb "github.com/epifi/gamma/api/typesv2/ui/insights/secrets"
	"github.com/epifi/gamma/frontend/analyser/debt/credit_score/lens"
	networthDeeplink "github.com/epifi/gamma/frontend/insights/networth/deeplink"
	"github.com/epifi/gamma/frontend/insights/secrets/colors"
	secretStore "github.com/epifi/gamma/frontend/insights/secrets/credit_report/store"
	creditScoreSecret "github.com/epifi/gamma/frontend/insights/secrets/credit_report_secret/store"
	"github.com/epifi/gamma/frontend/insights/secrets/credit_report_secret/valuegenerator"
	"github.com/epifi/gamma/frontend/insights/secrets/dataproviders"
	secretErrors "github.com/epifi/gamma/frontend/insights/secrets/errors"
	"github.com/epifi/gamma/frontend/insights/secrets/secret_builder"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
)

type CreditReportUiBuilder struct {
	refreshSecret    networthDeeplink.ISecretRefreshDeeplinkProvider
	palClient        palPb.PreApprovedLoanClient
	userDataProvider dataproviders.UserDataProvider
	dataProvider     valuegenerator.CreditReportSecretDataProvider
}

func NewCreditReportUiBuilder(
	refreshSecret networthDeeplink.ISecretRefreshDeeplinkProvider,
	palClient palPb.PreApprovedLoanClient,
	userDataProvider dataproviders.UserDataProvider,
	dataProvider valuegenerator.CreditReportSecretDataProvider,
) *CreditReportUiBuilder {
	return &CreditReportUiBuilder{
		refreshSecret:    refreshSecret,
		palClient:        palClient,
		userDataProvider: userDataProvider,
		dataProvider:     dataProvider,
	}
}

const (
	CreditScoreLineItems            = "CreditScoreLineItems"
	CreditScoreSpiderChart          = "CreditScoreSpiderChart"
	greenSpiderGraph                = "https://epifi-icons.pointz.in/insights/secrets/green_spider_graph.png"
	orangeSpiderGraph               = "https://epifi-icons.pointz.in/insights/secrets/orange_spider_graph.png"
	CreditReportSecretRefreshBanner = "CreditReportSecretRefreshBanner"
	PayPendingLoanDuesBanner        = "PayPendingLoanDuesBanner"
	CreditReportScoreChangeBanner   = "CreditReportScoreChangeBanner"
	CreditReportActionV2Banner      = "CreditReportActionV2Banner"
	chevronMoss                     = "https://epifi-icons.pointz.in/insights/secrets/chevron_moss.png"
	chevronSnow                     = "https://epifi-icons.pointz.in/insights/secrets/chevron_snow.png"
	chevronAmber                    = "https://epifi-icons.pointz.in/insights/secrets/chevron_amber.png"
	partyEmoji                      = "https://epifi-icons.pointz.in/insights/secrets/party_emoji.png"
	thumbsUpEmoji                   = "https://epifi-icons.s3.ap-south-1.amazonaws.com/insights/secrets/thumbs_up_emoji.png"
	arrowDownEmoji                  = "https://epifi-icons.s3.ap-south-1.amazonaws.com/insights/secrets/arrow_down_emoji.png"
	warningErrorBrown               = "https://epifi-icons.pointz.in/insights/secrets/red_warning"
	chevronBrown                    = "https://epifi-icons.pointz.in/insights/secrets/red_stroke"
	CreditScoreNumberCard           = "CreditScoreNumberCard"
	CreditScoreSecretCollectionCard = "CreditScoreSecretCollectionCard"
)

func (c *CreditReportUiBuilder) BuildZeroStateSummary(_ context.Context, secretConfig *secretsModelPb.Secret) (*secretsFePb.SecretSummary, error) {
	card := secretsUiPb.NewSecretSummaryCardBuilder(secretConfig.GetSecretSummaryUiConfig().GetSecretSummaryCardConfiguration()).
		SetTitle("Your credit score").
		SetHiddenValue("899").
		SetBottomTag("TAP TO REVEAL").
		SetDeeplink(secrets.GetSecretAnalyserScreenDeeplink(secretConfig.GetName())).
		SetVisualisation(greenSpiderGraph, 0, 0).
		Build()
	return &secretsFePb.SecretSummary{
		SummaryUi: &secretsFePb.SecretSummary_SecretSummaryCard{
			SecretSummaryCard: card,
		},
	}, nil
}

func (c *CreditReportUiBuilder) BuildSummary(ctx context.Context, secret *secretsModelPb.Secret, store *secretStore.CreditScoreSecretStore) (*secretsFePb.SecretSummary, error) {
	creditReports := store.GetCreditReports()
	if len(creditReports) == 0 {
		return c.BuildZeroStateSummary(ctx, secret)
	}
	scoreStrength, err := store.GetLatestCreditScoreStrength(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get credit score strength: %w", err)
	}
	imgUrl := c.GetScoreSummaryImageUrl(scoreStrength)

	latestReport := creditReports[0]
	creditScore := latestReport.GetCreditReportData().GetScore().GetBureauScore()
	cardBuilder := secretsUiPb.NewSecretSummaryCardBuilder(secret.GetSecretSummaryUiConfig().GetSecretSummaryCardConfiguration()).
		SetTitle("Your credit score").
		SetVisibleValue(creditScore).
		SetDeeplink(secrets.GetSecretAnalyserScreenDeeplink(secret.GetName())).
		SetVisualisation(imgUrl, 0, 0)

	return &secretsFePb.SecretSummary{
		SummaryUi: &secretsFePb.SecretSummary_SecretSummaryCard{
			SecretSummaryCard: cardBuilder.Build(),
		},
	}, nil
}

func (c *CreditReportUiBuilder) BuildSecretAnalyser(ctx context.Context,
	req *secret_builder.BuildSecretAnalyserRequest,
	creditScoreLensProcessor *lens.CreditScoreSummaryLensProcessor,
	store *secretStore.CreditScoreSecretStore,
) (*secretsFePb.SecretAnalyser, error) {
	err := creditScoreLensProcessor.GenerateLensData(ctx, req.ActorId, nil)
	if err != nil {
		logger.Error(ctx, "lens could not be generated", zap.String(logger.ACTOR_ID_V2, req.ActorId), zap.Error(err))
		return nil, fmt.Errorf("error generating lens data for credit score analyser: %w", err)
	}
	compGens, err := creditScoreLensProcessor.GetVisualComponentGenerators(ctx, req.ActorId)
	if err != nil {
		logger.Error(ctx, "error getting credit score visual component generators", zap.String(logger.ACTOR_ID_V2, req.ActorId), zap.Error(err))
		return nil, fmt.Errorf("error generating credit score visual component generators: %w", err)
	}
	uiComponents := make([]*secretsFePb.SecretAnalyserComponent, 0)
	latestCreditScore, cErr := store.GetLatestCreditScore()
	if cErr != nil {
		logger.Error(ctx, "error getting credit score", zap.String(logger.ACTOR_ID_V2, req.ActorId), zap.Error(cErr))
		return nil, fmt.Errorf("error getting credit score: %w", cErr)
	}
	scoreStrength, sErr := store.GetLatestCreditScoreStrength(ctx)
	if sErr != nil {
		logger.Error(ctx, "error getting credit score strength", zap.String(logger.ACTOR_ID_V2, req.ActorId), zap.Error(sErr))
		return nil, fmt.Errorf("error getting credit score: %w", sErr)
	}
	transformedComponents := make(map[string]*secretsFePb.SecretAnalyserComponent)
	for _, compGen := range compGens {
		comp, err2 := compGen.Generate(ctx)
		if err2 != nil {
			logger.Error(ctx, "getting credit score visual component generator failed", zap.String(logger.ACTOR_ID_V2, req.ActorId), zap.Error(err2))
			return nil, fmt.Errorf("error generating credit score visual component generators: %w", err2)
		}
		if comp != nil {
			uiComp, err := c.convertAnalyserVisualComponentToSecretAnalyserComponent(ctx, req.SecretConfig, comp, latestCreditScore, scoreStrength)
			if err != nil {
				return nil, fmt.Errorf("error converting analyser visual component to secret analyser component: %w", err)
			}
			if uiComp != nil {
				transformedComponents[uiComp.GetComponentName()] = uiComp
			}
		}
	}
	for _, cfg := range req.SecretConfig.GetSecretAnalyserUiConfigs() {
		switch cfg.GetComponentName() {
		case CreditScoreSpiderChart, CreditScoreLineItems:
			component, ok := transformedComponents[cfg.GetComponentName()]
			if !ok {
				return nil, fmt.Errorf("component %s missing in transformed components mpa", cfg.GetComponentName())
			}
			uiComponents = append(uiComponents, component)
		case CreditReportSecretRefreshBanner:
			refreshBanner, err := c.refreshSecret.GetActionableRefreshBanner(ctx, &networthDeeplink.RefreshSecretParams{
				ActorId:              req.ActorId,
				RedirectDeepLink:     secrets.GetSecretAnalyserScreenDeeplink(req.SecretConfig.GetName()),
				NetworthRefreshAsset: modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE,
			}, cfg.GetActionableBannerConfig())
			if err != nil {
				if errors.Is(err, secretErrors.UiComponentBuildingSkippedErr) {
					continue
				}
				return nil, errors.Wrap(err, "error while building refresh banner")
			}
			uiComponents = append(uiComponents, secretsFePb.NewSecretAnalyserComponent(refreshBanner, cfg))
		case PayPendingLoanDuesBanner:
			loanUserStatusResp, loanUserStatusRespErr := c.palClient.GetLoansUserStatus(ctx, &palPb.GetLoansUserStatusRequest{
				ActorId: req.ActorId,
			})
			if rpcErr := epifigrpc.RPCError(loanUserStatusResp, loanUserStatusRespErr); rpcErr != nil {
				return nil, errors.Wrap(rpcErr, "error getting loan user status")
			}

			if loanUserStatusResp.GetIsLoanOverdue() {
				loanDuesBanner := secretsFePb.NewActionableBannerBuilder(&secretsConfigPb.ActionableBannerConfig{
					LeftImageConfig:   &secretsConfigPb.VisualisationConfiguration{ImageUrl: warningErrorBrown, Height: 32, Width: 32},
					DescriptionConfig: &secretsConfigPb.TextConfiguration{Text: "PAY pending loan dues to avoid adverse impact on your score", Color: colors.ColorBrown, FontStyle: commontypes.FontStyle_SUBTITLE_S},
					RightComponentConfig: &secretsConfigPb.ITCConfiguration{LeftVisualisationConfig: &secretsConfigPb.VisualisationConfiguration{
						ImageUrl: chevronBrown, Height: 28, Width: 28,
					}},
					Deeplink: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
					},
					BgColor: pkgColors.ColorSnow,
				}).Build()

				uiComponents = append(uiComponents, secretsFePb.NewSecretAnalyserComponent(loanDuesBanner, cfg))
			}
		case CreditReportScoreChangeBanner:
			component, ok := transformedComponents[CreditScoreSpiderChart]
			if !ok {
				component, ok = transformedComponents[CreditScoreNumberCard]
				if !ok {
					return nil, fmt.Errorf("neither %s nor %s found in transformed components", CreditScoreSpiderChart, CreditScoreNumberCard)
				}
			}
			scoreChangeModal := component.GetAnalyserCard().GetSpiderGraphCard().GetSpiderWebParams().GetCenter().GetDetails()
			var scoreChangeModalDl *deeplinkPb.Deeplink
			if scoreChangeModal != nil {
				scoreChangeModalDl = deeplinkV3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_ANALYSER_DETAILS_MODAL_BOTTOM_SHEET, &details_modal.DetailsModalScreenOptions{
					DetailsModal: scoreChangeModal,
				})
			}
			scoreChangeBanner := c.BuildScoreChangeBanner(ctx, cfg.GetActionableBannerConfig(), store, scoreChangeModalDl)
			if scoreChangeBanner != nil {
				uiComponents = append(uiComponents, secretsFePb.NewSecretAnalyserComponent(scoreChangeBanner, cfg))
			}
		case CreditReportActionV2Banner:
			actionV2Banner, err := c.getActionV2Banner(ctx, req, store, cfg.GetActionableBannerV2Config())
			if err != nil {
				if errors.Is(err, secretErrors.UiComponentBuildingSkippedErr) {
					continue
				}
				return nil, errors.Wrap(err, "error while building CreditReportActionV2Banner")
			}
			uiComponents = append(uiComponents, secretsFePb.NewSecretAnalyserComponent(actionV2Banner, cfg))
		case CreditScoreNumberCard:
			numberCard := &secretsFePb.SecretsAnalyserCard{
				Visualisation: &secretsFePb.SecretsAnalyserCard_NumberCard{NumberCard: secretsUiPb.NewNumberCardBuilder(cfg.GetVisualisationCardConfig().GetNumberCardConfig()).
					SetValueWithoutBgColor(latestCreditScore).Build(),
				},
			}
			uiComponents = append(uiComponents, secretsFePb.NewSecretAnalyserComponent(numberCard, cfg))
		case CreditScoreSecretCollectionCard:
			secretCollectionCard, secretErr := c.getSecretCollectionCard(ctx, store, req, cfg.GetSecretSummaryCardUiConfig())
			if secretErr != nil {
				return nil, errors.Wrap(secretErr, "error while building CreditScoreSecretCollectionCard")
			}
			uiComponents = append(uiComponents, secretsFePb.NewSecretAnalyserComponent(secretCollectionCard, cfg))
		}
	}
	return &secretsFePb.SecretAnalyser{
		ComponentList: uiComponents,
		BgColor:       c.GetAnalyserCardBgColor(scoreStrength), // bg color is usually populated via config but for credit score it is dynamic on the basis of score strength
	}, nil
}

func (c *CreditReportUiBuilder) BuildScoreChangeBanner(ctx context.Context, config *secretsConfigPb.ActionableBannerConfig, store *secretStore.CreditScoreSecretStore, dl *deeplinkPb.Deeplink) *secretsFePb.ActionableBanner {
	reports := store.GetCreditReports()
	if len(reports) <= 1 {
		return nil
	}
	latestReport := reports[0]
	prevReport := reports[1]
	currScore, err := strconv.Atoi(latestReport.GetCreditReportData().GetScore().GetBureauScore())
	if err != nil {
		logger.Error(ctx, "error in converting credit score", zap.Error(err))
		return nil
	}
	prevScore, err := strconv.Atoi(prevReport.GetCreditReportData().GetScore().GetBureauScore())
	if err != nil {
		logger.Error(ctx, "error in converting credit score", zap.Error(err))
		return nil
	}
	diff := currScore - prevScore
	var text, color, chevronUrl, imageUrl string
	switch {
	case diff > 0:
		text, color, chevronUrl, imageUrl = fmt.Sprintf("Your score is up by %d points this month!", diff), colors.ColorMoss700, chevronMoss, partyEmoji
	case diff < 0:
		text, color, chevronUrl, imageUrl = fmt.Sprintf("Your score is down by %d points this month", -diff), colors.ColorSupportingAmber900, chevronAmber, arrowDownEmoji
	default:
		text, color, chevronUrl, imageUrl = "Your credit score looks unchanged!", pkgColors.ColorOnDarkLowEmphasis, chevronSnow, thumbsUpEmoji
	}
	var rightComponent *secretsConfigPb.ITCConfiguration
	if dl != nil {
		rightComponent = &secretsConfigPb.ITCConfiguration{LeftVisualisationConfig: &secretsConfigPb.VisualisationConfiguration{
			ImageUrl: chevronUrl, Height: 28, Width: 28,
		}}
	}

	return secretsFePb.NewActionableBannerBuilder(&secretsConfigPb.ActionableBannerConfig{
		LeftImageConfig:      &secretsConfigPb.VisualisationConfiguration{ImageUrl: imageUrl, Height: 32, Width: 32},
		DescriptionConfig:    &secretsConfigPb.TextConfiguration{Text: text, Color: color, FontStyle: commontypes.FontStyle_SUBTITLE_S},
		RightComponentConfig: rightComponent,
		Deeplink:             dl,
		BgColor:              pkgColors.ColorSnow,
		BorderProperty:       config.GetBorderProperty(),
	}).Build()
}

// nolint: funlen
func (c *CreditReportUiBuilder) convertAnalyserVisualComponentToSecretAnalyserComponent(ctx context.Context, secret *secretsModelPb.Secret,
	component *pb.VisualComponent, latestCreditScore string, scoreStrength string) (*secretsFePb.SecretAnalyserComponent, error) {
	switch component.GetComponent().(type) {
	case *pb.VisualComponent_Visualisation:
		secretCfg, ok := lo.Find(secret.GetSecretAnalyserUiConfigs(), func(cfg *secretsConfigPb.SecretAnalyserUIConfig) bool {
			return cfg.GetComponentName() == CreditScoreSpiderChart || cfg.GetComponentName() == CreditScoreNumberCard
		})
		if !ok {
			logger.Info(ctx, "failed to find config for credit score spider chart")
			return nil, nil
		}
		if component.GetVisualisation().GetType() == pb.VisualisationType_VISUALISATION_SPIDER_WEB {
			spiderWeb := component.GetVisualisation().GetSpiderWeb()
			spiderWeb.WebBorderColor = c.GetSpiderWebBorderColor(scoreStrength)
			spiderWeb.GetCenter().BackgroundColor = c.GetAnalyserCardBgColor(scoreStrength)
			spiderWeb.GetCenter().GetText().Color = c.GetSpiderWebCentreTextColor(scoreStrength)
			for i, _ := range spiderWeb.GetSections() {
				spiderWeb.GetSections()[i].BackgroundColor = c.GetSpiderWebSectionColor(scoreStrength)
				spiderWeb.GetSections()[i].GetName().Color = c.GetSpiderWebSectionTextColor(scoreStrength)
			}
			vis := &secretsFePb.SecretsAnalyserCard{
				Visualisation: &secretsFePb.SecretsAnalyserCard_SpiderGraphCard{
					SpiderGraphCard: &secretsFePb.SecretsSpiderGraph{
						TitleValuePair: &ui.VerticalKeyValuePair{
							Title: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Your credit score",
								c.GetTitleColor(scoreStrength), commontypes.FontStyle_SUBTITLE_L)),
							Value: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(latestCreditScore,
								colors.ColorDarkBase, commontypes.FontStyle_SUBTITLE_2XL)),
							VerticalPaddingBtwTitleValue: 8,
							HAlignment:                   ui.VerticalKeyValuePairHAlignment_VERTICAL_KEY_VALUE_PAIR_H_ALIGNMENT_CENTER,
						},
						SpiderWebParams: spiderWeb,
						Footer: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("powered by", colors.ColorSlate,
							commontypes.FontStyle_OVERLINE_2XS_CAPS)).WithRightVisualElementUrlHeightAndWidth("https://epifi-icons.pointz.in/credit_score/experian_logo_dark.png", 16, 48),
						BgColor: c.GetAnalyserCardBgColor(scoreStrength),
					},
				},
			}
			secretAnalyserComponent := secretsFePb.NewSecretAnalyserComponent(vis, secretCfg)
			return secretAnalyserComponent, nil
		}
	case *pb.VisualComponent_LineItems:
		secretCfg, ok := lo.Find(secret.GetSecretAnalyserUiConfigs(), func(cfg *secretsConfigPb.SecretAnalyserUIConfig) bool {
			return cfg.GetComponentName() == CreditScoreLineItems
		})
		if !ok {
			return nil, fmt.Errorf("failed to find config for %s", CreditScoreLineItems)
		}
		if component.GetLineItems().GetItems() != nil {
			lineItemsBuilder := secretsFePb.NewLineItemCardWithAnalyserFilter(secretCfg.GetLineItemsConfig())
			for _, li := range component.GetLineItems().GetItems() {
				secretLi := c.convertAnalyserLineItemToSecretLineItem(li, secretCfg.GetLineItemsConfig())
				lineItemsBuilder.AddLineItem(secretLi)
			}
			return secretsFePb.NewSecretAnalyserComponent(lineItemsBuilder.Build(), secretCfg), nil
		}
	}
	return nil, nil
}

func (c *CreditReportUiBuilder) convertAnalyserLineItemToSecretLineItem(lineItem *pb.LineItem, liConfig *secretsConfigPb.LineItemsConfig) *ui1.LineItem {
	leftTag := ""
	if len(lineItem.GetSecondaryInfo()) > 0 {
		leftTag = lineItem.GetSecondaryInfo()[0]
	}
	secretLiBuilder := secretsFePb.NewLineItemBuilder(liConfig.GetLineItemConfig()).
		SetLeftHeading(lineItem.GetPrimaryTitle()).
		AddLeftTag(leftTag).
		SetRightHeading(lineItem.GetPrimaryMetric())

	if lineItem.GetDetails() != nil {
		secretLiBuilder.SetDeeplink(deeplinkV3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_ANALYSER_DETAILS_MODAL_BOTTOM_SHEET,
			&details_modal.DetailsModalScreenOptions{DetailsModal: lineItem.GetDetails()}))
	}
	secretLi := secretLiBuilder.Build()
	secretLi.RightTags = []*ui1.Tags{
		{
			Tags: []*ui.IconTextComponent{
				ui.NewITC().WithTexts(commontypes.GetHtmlText(lineItem.GetSecondaryMetric())),
			},
		},
	}
	return secretLi
}

func (c *CreditReportUiBuilder) GetTitleColor(scoreStrength string) string {
	switch scoreStrength {
	case constant.StrengthLevelExcellent, constant.StrengthLevelGood:
		return colors.ColorMoss700
	case constant.StrengthLevelBad:
		return colors.ColorSupportingAmber900
	default:
		return colors.ColorSupportingAmber900
	}
}

func (c *CreditReportUiBuilder) GetAnalyserCardBgColor(scoreStrength string) string {
	switch scoreStrength {
	case constant.StrengthLevelExcellent, constant.StrengthLevelGood:
		return colors.ColorSupportingMoss50
	case constant.StrengthLevelBad:
		return colors.ColorSupportingAmber50
	default:
		return colors.ColorSupportingAmber50
	}
}

func (c *CreditReportUiBuilder) GetSpiderWebBorderColor(scoreStrength string) string {
	switch scoreStrength {
	case constant.StrengthLevelExcellent, constant.StrengthLevelGood:
		return colors.ColorSupportingMoss200
	case constant.StrengthLevelBad:
		return colors.ColorSupportingAmber200
	default:
		return colors.ColorSupportingAmber200
	}
}

func (c *CreditReportUiBuilder) GetSpiderWebSectionColor(scoreStrength string) string {
	switch scoreStrength {
	case constant.StrengthLevelExcellent, constant.StrengthLevelGood:
		return colors.ColorSupportingMoss400Opacity60
	case constant.StrengthLevelBad:
		return colors.ColorSupportingAmber400Opacity60
	default:
		return colors.ColorSupportingAmber400Opacity60
	}
}

func (c *CreditReportUiBuilder) GetSpiderWebSectionTextColor(scoreStrength string) string {
	switch scoreStrength {
	case constant.StrengthLevelExcellent, constant.StrengthLevelGood:
		return colors.ColorSupportingMoss700
	case constant.StrengthLevelBad:
		return colors.ColorSupportingAmber700
	default:
		return colors.ColorSupportingAmber700
	}
}

func (c *CreditReportUiBuilder) GetSpiderWebCentreTextColor(scoreStrength string) string {
	switch scoreStrength {
	case constant.StrengthLevelExcellent, constant.StrengthLevelGood:
		return colors.ColorSupportingMoss900
	case constant.StrengthLevelBad:
		return colors.ColorSupportingAmber900
	default:
		return colors.ColorSupportingAmber900
	}
}

func (c *CreditReportUiBuilder) GetScoreSummaryImageUrl(scoreStrength string) string {
	switch scoreStrength {
	case constant.StrengthLevelExcellent, constant.StrengthLevelGood:
		return greenSpiderGraph
	case constant.StrengthLevelBad:
		return orangeSpiderGraph
	default:
		return orangeSpiderGraph
	}
}

func (c *CreditReportUiBuilder) getActionV2Banner(ctx context.Context, req *secret_builder.BuildSecretAnalyserRequest, store *secretStore.CreditScoreSecretStore, config *secretsConfigPb.ActionableBannerV2Config) (*secretsFePb.ActionableBannerV2, error) {
	if req.Provenance != secrets.Provenance_PROVENANCE_WEB_CREDIT_REPORT_ANALYSER {
		return nil, secretErrors.UiComponentBuildingSkippedErr
	}
	// Perform null check on store and credit report data
	if store == nil || config == nil {
		return nil, secretErrors.UiComponentBuildingSkippedErr
	}

	creditScoreString, err := store.GetLatestCreditScore()
	if err != nil {
		return nil, secretErrors.UiComponentBuildingSkippedErr
	}

	creditScore, err := strconv.Atoi(creditScoreString)
	if err != nil {
		return nil, secretErrors.UiComponentBuildingSkippedErr
	}

	// Show banner only if credit score is equal and above 720
	if creditScore < 720 {
		return nil, secretErrors.UiComponentBuildingSkippedErr
	}

	dob, err := c.userDataProvider.GetUsersDateOfBirth(ctx, req.ActorId)
	if err != nil {
		return nil, secretErrors.UiComponentBuildingSkippedErr
	}
	now := time.Now()

	// Standard age calculation logic
	age := now.Year() - dob.Year()

	// Adjust age if birthday hasn't occurred yet this year
	if now.Before(time.Date(now.Year(), dob.Month(), dob.Day(), 0, 0, 0, 0, time.UTC)) {
		age--
	}

	if age < 23 {
		return nil, secretErrors.UiComponentBuildingSkippedErr
	}

	builder := secretsFePb.NewActionableBannerV2Builder(config)
	banner := builder.Build()
	return banner, nil
}

func (c *CreditReportUiBuilder) getSecretCollectionCard(ctx context.Context, store *secretStore.CreditScoreSecretStore, req *secret_builder.BuildSecretAnalyserRequest, config *secretsConfigPb.SecretSummaryCardUiConfig) (*secretsFePb.SecretCollectionComponent, error) {
	if len(config.GetSecretSummaryCardConfig()) == 0 {
		return nil, fmt.Errorf("secret summary card config is empty")
	}
	if len(config.GetSecretSummaryCardConfig()) != len(req.SecretConfig.GetSecretBuilderDataConfig().GetCreditScoreSecretDataConfig().GetCreditReportSecrets()) {
		return nil, fmt.Errorf("len of secret summary card config is different from credit score secret data config")
	}
	var secretSummaries []*secretsFePb.SecretSummary
	latestCreditReport, err := store.GetLatestCreditReport()
	if err != nil {
		return nil, errors.Wrap(err, "failed to get latest credit report")
	}
	creditReportScoreSecret := &creditScoreSecret.CreditReportSecretStore{
		LatestCreditReport: latestCreditReport,
	}
	for i, creditReportSecret := range req.SecretConfig.GetSecretBuilderDataConfig().GetCreditScoreSecretDataConfig().GetCreditReportSecrets() {
		summaryDataProvider, err := c.dataProvider.GetSummaryData(ctx, &valuegenerator.DataProviderRequest{
			ActorId: req.ActorId,
			Store:   creditReportScoreSecret,
			SecretConfig: &secretsModelPb.Secret{
				Name: creditReportSecret,
			},
		})
		if err != nil && !errors.Is(err, secretErrors.NoDataToBuildSecretPostFilters) {
			return nil, errors.Wrap(err, "error while getting summary data")
		}
		if errors.Is(err, secretErrors.NoDataToBuildSecretPostFilters) {
			continue
		}
		secretSummary := &secretsFePb.SecretSummary{
			SummaryUi: &secretsFePb.SecretSummary_SecretSummaryCard{
				SecretSummaryCard: secretsUiPb.NewSecretSummaryCardBuilder(config.GetSecretSummaryCardConfig()[i]).
					SetVisibleValue(summaryDataProvider.GetSummaryValue()).
					SetDeeplink(secrets.GetSecretAnalyserScreenDeeplink(creditReportSecret)).
					SetBottomTagWithChevron("Check all").
					Build(),
			},
		}
		secretSummaries = append(secretSummaries, secretSummary)
	}

	return &secretsFePb.SecretCollectionComponent{
		Title:           ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(config.GetTitleConfig().GetText(), config.GetTitleConfig().GetBgColor(), config.GetTitleConfig().GetFontStyle())),
		SecretSummaries: secretSummaries,
	}, nil
}
