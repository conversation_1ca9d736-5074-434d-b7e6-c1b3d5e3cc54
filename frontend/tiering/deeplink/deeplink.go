package deeplink

import (
	"context"
	"fmt"
	"strings"

	"github.com/google/wire"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	gmoney "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/encoding/protojson"

	accrualPkg "github.com/epifi/gamma/pkg/accrual"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	widgetPb "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	cfggenconf "github.com/epifi/be-common/pkg/frontend/app/genconf"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	tieringScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/tiering"
	tiering2 "github.com/epifi/gamma/api/typesv2/tiering"
	"github.com/epifi/gamma/frontend/tiering/earned_benefits"

	"github.com/epifi/gamma/api/card/provisioning"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	salaryPb "github.com/epifi/gamma/api/salaryprogram"
	segmentPb "github.com/epifi/gamma/api/segment"
	beTieringPb "github.com/epifi/gamma/api/tiering"
	beTieringExtPb "github.com/epifi/gamma/api/tiering/external"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/pay/add_funds/tiering"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/frontend/config/genconf"
	tieringEvents "github.com/epifi/gamma/frontend/events"
	jumpBenefits "github.com/epifi/gamma/frontend/p2pinvestment/benefits"
	salaryBenefits "github.com/epifi/gamma/frontend/salaryprogram/benefits"
	"github.com/epifi/gamma/frontend/tiering/benefits"
	"github.com/epifi/gamma/frontend/tiering/data_collector"
	"github.com/epifi/gamma/frontend/tiering/deeplink/plan_options"
	tieringBottomInfo "github.com/epifi/gamma/frontend/tiering/deeplink/plan_options/bottom_info"
	tieringCard "github.com/epifi/gamma/frontend/tiering/deeplink/plan_options/card"
	tieringCta "github.com/epifi/gamma/frontend/tiering/deeplink/plan_options/cta"
	feTieringErrors "github.com/epifi/gamma/frontend/tiering/errors"
	"github.com/epifi/gamma/frontend/tiering/helper"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

var DeeplinkWireSet = wire.NewSet(
	wire.NewSet(NewDeeplinkManagerService, wire.Bind(new(Manager), new(*Service))),
	wire.NewSet(benefits.NewBenefitsManagerService, wire.Bind(new(benefits.Manager), new(*benefits.Service))),
	wire.NewSet(plan_options.NewPlanOptionsManagerService, wire.Bind(new(plan_options.Manager), new(*plan_options.Service))),
	wire.NewSet(tieringBottomInfo.NewBottomInfoManagerService, wire.Bind(new(tieringBottomInfo.Manager), new(*tieringBottomInfo.Service))),
	wire.NewSet(tieringCta.NewCtaManagerService, wire.Bind(new(tieringCta.Manager), new(*tieringCta.Service))),
	wire.NewSet(tieringCard.NewCardManagerService, wire.Bind(new(tieringCard.Manager), new(*tieringCard.Service))),
	salaryBenefits.SalaryProgramBenefitsWireSet,
)

// Manager manages all tiering related deeplinks
type Manager interface {
	// GetTierLaunchInfoDeeplink returns deeplink for tier launch info screen i.e, Tier Introduction
	GetTierLaunchInfoDeeplink(ctx context.Context, feTier beTieringExtPb.Tier, actorId string, appPlatform commontypes.Platform, appVersion uint32) (*deeplinkPb.Deeplink, error)
	// GetAllPlansDeeplink returns deeplink for tier all plans screen
	GetAllPlansDeeplink(ctx context.Context, actorId string, appPlatform commontypes.Platform, appVersion uint32, uiContext string) (*deeplinkPb.Deeplink, error)
	// GetUpgradeSuccessDeeplink returns deeplink for upgrade success screen
	// eg: Upgrade success screen after add funds
	GetUpgradeSuccessDeeplink(ctx context.Context, feTier beTieringExtPb.Tier, actorId string, appPlatform commontypes.Platform, appVersion uint32) (*deeplinkPb.Deeplink, error)
	// https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10277-34009&t=IFhGVdV82XTiVahN-4
	GetUpgradeSuccessDeeplinkV2(feTier beTieringExtPb.Tier) (*deeplinkPb.Deeplink, error)
	// GetOnbAddFundsUpgradeSuccessDeeplink returns deeplink for onboarding add funds success screen
	// eg: Upgrade success screen after onboarding add funds
	GetOnbAddFundsUpgradeSuccessDeeplink(ctx context.Context, feTier beTieringExtPb.Tier, actorId string, nextAction *deeplinkPb.Deeplink) (*deeplinkPb.Deeplink, error)
}

// Service implements Manager
type Service struct {
	gconf              *genconf.Config
	dataCollector      data_collector.DataCollector
	planOptionsManager plan_options.Manager
	benefitsManager    benefits.Manager
	beTieringClient    beTieringPb.TieringClient
	salaryBenefitsSvc  salaryBenefits.IService
	// TODO(sainath): explore strategy pattern and inject a interface here instead of struct
	jumpBenefits       *jumpBenefits.TieringBenefitsService
	cardClient         provisioning.CardProvisioningClient
	segmentationClient segmentPb.SegmentationServiceClient
	salaryClient       salaryPb.SalaryProgramClient
	eventBroker        events.Broker
	userClient         userPb.UsersClient
}

// NewDeeplinkManagerService to be used in dependency injection
func NewDeeplinkManagerService(
	gconf *genconf.Config,
	dataCollector data_collector.DataCollector,
	planOptionsManager plan_options.Manager,
	benefitsManager benefits.Manager,
	beTieringClient beTieringPb.TieringClient,
	salaryBenefitsSvc salaryBenefits.IService,
	jumpBenefits *jumpBenefits.TieringBenefitsService,
	cardClient provisioning.CardProvisioningClient,
	segmentationClient segmentPb.SegmentationServiceClient,
	salaryClient salaryPb.SalaryProgramClient,
	eventBroker events.Broker,
	userClient userPb.UsersClient,
) *Service {
	return &Service{
		gconf:              gconf,
		dataCollector:      dataCollector,
		planOptionsManager: planOptionsManager,
		benefitsManager:    benefitsManager,
		beTieringClient:    beTieringClient,
		salaryBenefitsSvc:  salaryBenefitsSvc,
		jumpBenefits:       jumpBenefits,
		cardClient:         cardClient,
		segmentationClient: segmentationClient,
		salaryClient:       salaryClient,
		eventBroker:        eventBroker,
		userClient:         userClient,
	}
}

// GetAllPlansDeeplink returns deeplink for tier all plans screen
func (s *Service) GetAllPlansDeeplink(ctx context.Context, actorId string, appPlatform commontypes.Platform, appVersion uint32, uiContext string) (*deeplinkPb.Deeplink, error) {
	planOptionsData, eligibilityMap, gatherDataErr := s.gatherDataForAllPlans(ctx, actorId, appPlatform, appVersion)
	if gatherDataErr != nil {
		return nil, errors.Wrap(gatherDataErr, "error in gathering data")
	}

	tierPlansToShow := []beTieringExtPb.Tier{
		beTieringExtPb.Tier_TIER_FI_PLUS,
		beTieringExtPb.Tier_TIER_FI_INFINITE,
	}

	if planOptionsData.GetBaseTier() == beTieringExtPb.Tier_TIER_FI_REGULAR {
		tierPlansToShow = append([]beTieringExtPb.Tier{beTieringExtPb.Tier_TIER_FI_REGULAR}, tierPlansToShow...)
	} else if planOptionsData.GetBaseTier() == beTieringExtPb.Tier_TIER_FI_BASIC {
		tierPlansToShow = append([]beTieringExtPb.Tier{beTieringExtPb.Tier_TIER_FI_BASIC}, tierPlansToShow...)
	}

	if helper.ShouldPitchAaSalaryTier(ctx, s.gconf, planOptionsData.GetCurrentTier()) {
		tierPlansToShow = append(tierPlansToShow, beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_1)
	}

	if s.gconf.SalaryProgram().DisableB2CSalaryEntryPointsFlag() {
		// showing salary plan only to B2B users
		isEmploymentChannelB2bVerifiedResp, isEmploymentChannelB2bVerifiedErr := s.userClient.GetB2BSalaryProgramVerificationStatus(ctx, &userPb.GetB2BSalaryProgramVerificationStatusRequest{
			Identifier: &userPb.GetB2BSalaryProgramVerificationStatusRequest_ActorId{
				ActorId: actorId,
			},
		})
		if rpcErr := epifigrpc.RPCError(isEmploymentChannelB2bVerifiedResp, isEmploymentChannelB2bVerifiedErr); rpcErr != nil {
			logger.Error(ctx, "error getting B2B salary program verification status", zap.String("actor_id", actorId), zap.Error(rpcErr))
		}

		// TODO: remove this check once we have shifted the current SalaryB2C users to Prime
		// Showing Salary Tier only in case user is currently in SalaryTier or their employment channel is B2B
		if (planOptionsData.GetCurrentTier() == beTieringExtPb.Tier_TIER_FI_SALARY) || (isEmploymentChannelB2bVerifiedResp.GetIsVerified()) {
			tierPlansToShow = append(tierPlansToShow, beTieringExtPb.Tier_TIER_FI_SALARY)
		}
	} else {

		if planOptionsData.GetCurrentTier() == beTieringExtPb.Tier_TIER_FI_SALARY_LITE {
			tierPlansToShow = append(tierPlansToShow, beTieringExtPb.Tier_TIER_FI_SALARY_LITE)
		} else {
			tierPlansToShow = append(tierPlansToShow, beTieringExtPb.Tier_TIER_FI_SALARY)
		}
	}

	var planOptions []*deeplinkPb.TierPlanOptions
	for _, tier := range tierPlansToShow {
		planOptionsData.FeTier = tier
		planOptionsData.IsEligibleForTierUpgrade = eligibilityMap[tier]
		// if tier is not present in map, then mark as ineligible
		// by default map will return false if key is not present
		planOption, planOptionErr := s.planOptionsManager.GetPlanOption(ctx, planOptionsData, appPlatform, appVersion)
		if planOptionErr != nil {
			return nil, fmt.Errorf("error in getting %s plan option, %w", tier.String(), planOptionErr)
		}
		planOptions = append(planOptions, planOption)
	}

	analyticsMap := map[string]string{}
	analyticsMap["current_tier"] = planOptionsData.GetCurrentTier().String()

	idx := s.getTierIndexToFocus(planOptionsData.GetCurrentTier(), uiContext, tierPlansToShow)
	if int(idx) >= len(planOptions) {
		idx = 0
	}
	tier := planOptions[idx].GetIdentifier()

	goroutine.Run(ctx, goroutine.DefaultTimeout, func(ctx context.Context) {
		s.eventBroker.AddToBatch(
			epificontext.WithEventAttributes(ctx),
			tieringEvents.NewLoadedTierOverviewScreenBE(
				actorId, tier))
	})

	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_TIER_ALL_PLANS,
		ScreenOptions: &deeplinkPb.Deeplink_TierAllPlansOptions{
			TierAllPlansOptions: &deeplinkPb.TierAllPlansOptions{
				TierPlanOptionsList:    planOptions,
				TierIndexToFocus:       idx,
				AnalyticsKeyValuePairs: analyticsMap,
			},
		},
	}, nil
}

// GetTierLaunchInfoDeeplink returns deeplink for tier launch info screen i.e, Tier Introduction
func (s *Service) GetTierLaunchInfoDeeplink(ctx context.Context, feTier beTieringExtPb.Tier, actorId string, appPlatform commontypes.Platform, appVersion uint32) (*deeplinkPb.Deeplink, error) {
	planOptionsData, gatherDataErr := s.gatherDataForLaunchInfo(ctx, actorId, appPlatform, appVersion)
	if gatherDataErr != nil {
		return nil, errors.Wrap(gatherDataErr, "error gathering data for tier launch info deeplink")
	}
	planOptionsData.FeTier = feTier
	benefitsResp, fiBenefitsErr := s.benefitsManager.GetBenefitsForTierLaunchInfo(ctx, planOptionsData)
	if fiBenefitsErr != nil {
		return nil, fmt.Errorf("error getting benefits for tier %s, %w", feTier.String(), fiBenefitsErr)
	}
	cashEquivalent, _ := planOptionsData.GetUnlockedCashEquivalentForTier(feTier)
	launchAnimationText1 := FiPlusLaunchAnimationText1
	if feTier.IsAaSalaryTier() {
		launchAnimationText1 = FiPrimeLaunchAnimationText1
	}
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_TIER_INTRODUCTION,
		ScreenOptions: &deeplinkPb.Deeplink_TierIntroductionOptions{
			TierIntroductionOptions: &deeplinkPb.TierIntroductionOptions{
				LaunchAnimationJson:     GetLaunchAnimationLottieJsonString(feTier),
				HeroBenefit:             s.getHeroBenefit(feTier, cashEquivalent),
				BenefitItemList:         benefitsResp.GetDefaultBenefits(),
				CtaList:                 s.getCtasList(feTier),
				BenefitsBackgroundColor: widgetPb.GetBlockBackgroundColour(BenefitsBgColor),
				PlanIcon: &commontypes.Image{
					ImageType: commontypes.ImageType_PNG,
					ImageUrl:  GetLaunchAnimationBadgeUrl(feTier),
					Width:     int32(FiPlusLaunchAnimationBadgeWidth),
					Height:    int32(FiPlusLaunchAnimationBadgeHeight),
				},
				TextLine_1: commontypes.GetTextFromStringFontColourFontStyle(
					launchAnimationText1,
					FiPlusLaunchAnimationText1FontColor,
					commontypes.FontStyle_SUBTITLE_S,
				),
				TextLine_2: &commontypes.Text{
					FontColor: FiPlusLaunchAnimationText2FontColor,
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: GetLaunchAnimationText2(feTier),
					},
					FontStyle: &commontypes.Text_CustomFontStyle{
						CustomFontStyle: &commontypes.FontStyleInfo{
							FontFamily: "Gilroy",
							FontStyle:  "normal",
							FontSize:   "36px",
						},
					},
				},
				TextLine_3: commontypes.GetTextFromStringFontColourFontStyle(
					GetLaunchAnimationText3(feTier),
					FiPlusLaunchAnimationText3FontColor,
					commontypes.FontStyle_SUBTITLE_S,
				),
				Identifier: feTier.String(),
			},
		},
	}, nil
}

func (s *Service) getUpgradeSuccessDeeplinkV1(ctx context.Context, feTier beTieringExtPb.Tier, actorId string, appPlatform commontypes.Platform, appVersion uint32) (*deeplinkPb.Deeplink, error) {
	if appPlatform == commontypes.Platform_IOS && feTier.IsAaSalaryTier() {
		return nil, nil
	}

	planOptionsData, gatherDataErr := s.gatherDataForUpgradeSuccess(ctx, actorId, appPlatform, appVersion)
	if gatherDataErr != nil {
		return nil, errors.Wrap(gatherDataErr, "error gathering data for upgrade success")
	}
	planOptionsData.FeTier = feTier
	benefitsResp, fiBenefitsErr := s.benefitsManager.GetBenefitsForTierLaunchInfo(ctx, planOptionsData)
	if fiBenefitsErr != nil {
		return nil, fmt.Errorf("error getting benefits for tier %s, %w", feTier.String(), fiBenefitsErr)
	}
	minBalance, minBalanceErr := s.getMinBalanceForFeTier(ctx, actorId, feTier)
	if minBalanceErr != nil {
		if errors.Is(minBalanceErr, feTieringErrors.ErrTierHasNoMinBalanceCriteria) {
			minBalance = &gmoney.Money{CurrencyCode: "INR", Units: 0}
		} else {
			return nil, minBalanceErr
		}
	}
	if feTier.IsBaseTier() {
		return nil, feTieringErrors.ErrNoUpgradeSuccessDeeplinkForLowestTier
	}
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_TIER_UPGRADE_SUCCESS_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_TierUpgradeSuccessScreenOptions{
			TierUpgradeSuccessScreenOptions: &deeplinkPb.TierUpgradeSuccessScreenOptions{
				SuccessAnimationJson: GetUpgradeSuccessLottieJsonString(feTier),
				BenefitItemList:      benefitsResp.GetDefaultBenefits(),
				CtaList: []*deeplinkPb.Cta{
					{
						Type:         deeplinkPb.Cta_DONE,
						Text:         UpgradeSuccessScreenBgColorCtaText,
						DisplayTheme: deeplinkPb.Cta_PRIMARY,
					},
				},
				BenefitsBackgroundColor: widgetPb.GetBlockBackgroundColour(UpgradeSuccessScreenBgColor),
				PlanIcon: &commontypes.Image{
					ImageType: commontypes.ImageType_PNG,
					ImageUrl:  GetLaunchAnimationBadgeUrl(feTier),
					Width:     int32(FiPlusLaunchAnimationBadgeWidth),
					Height:    int32(FiPlusLaunchAnimationBadgeHeight),
				},
				TextLine_1: commontypes.GetTextFromStringFontColourFontStyle(
					FiPlusUpgradeSuccessText1,
					FiPlusUpgradeSuccessText1FontColor,
					commontypes.FontStyle_HEADLINE_XL,
				),
				TextLine_2: commontypes.GetTextFromStringFontColourFontStyle(
					GetUpgradeSuccessText2(feTier),
					FiPlusUpgradeSuccessText2FontColor,
					commontypes.FontStyle_HEADLINE_L,
				),
				TextLine_3: commontypes.GetTextFromStringFontColourFontStyle(
					GetUpgradeSuccessText3(feTier, moneyPkg.ToDisplayStringInIndianFormat(minBalance, 0, true)),
					FiPlusUpgradeSuccessText3FontColor,
					commontypes.FontStyle_SUBTITLE_S,
				),
				Identifier: feTier.String(),
			},
		},
	}, nil
}

// GetUpgradeSuccessDeeplinkV2 - https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10277-34009&t=IFhGVdV82XTiVahN-4
func (s *Service) GetUpgradeSuccessDeeplinkV2(feTier beTieringExtPb.Tier) (*deeplinkPb.Deeplink, error) {
	feTierStr := feTier.String()
	metaDataBytes, err := protojson.Marshal(&tiering2.SuccessV2MetaData{Tier: feTierStr})
	if err != nil {
		return nil, fmt.Errorf("error marshalling metadata, %w", err)
	}

	dl := tieringScreenOptionsPb.AllPlansDeeplink(feTier, true)
	if earned_benefits.IsTierAllowedForEarnedBenefitsScreen(feTier) {
		dl, err = earned_benefits.GetEarnedBenefitsDeeplink(feTier)
		if err != nil {
			return nil, fmt.Errorf("failed to get earned benefits deeplink, %w", err)
		}
	}

	tierV2Color := helper.GetTierV2Color(feTierStr, s.gconf.Tiering().TierV2ThemeColorMap(), s.gconf.Tiering().TierV2ColorMap())
	plansConfig, _ := s.gconf.Tiering().TierSuccessScreenV2UiParams().TierPlans().Load(feTierStr)
	subTitle := &commontypes.Text{
		// the existing config values for TierSuccessScreenV2UiParams subtitle are only plain string
		// avoiding NewTextPb() to modify subtitle that has 'fi-coins' as we are moving from fi-coins to fi-points from August 1, 2025
		DisplayValue: &commontypes.Text_PlainString{
			PlainString: accrualPkg.ReplaceCoinWithPointIfApplicable(plansConfig.SubTitle().PlainString(), nil),
		},
		FontColor: plansConfig.SubTitle().FontColor(),
		FontStyle: &commontypes.Text_StandardFontStyle{
			StandardFontStyle: commontypes.FontStyle(commontypes.FontStyle_value[strings.ToUpper(plansConfig.SubTitle().StandardFontStyle())]),
		},
	}
	pillarConfig, _ := s.gconf.Tiering().TierSuccessScreenV2UiParams().PillarIcons().Load(plansConfig.PillarTheme())
	upgradeSuccessScreen := &tieringScreenOptionsPb.UpgradeSuccessV2ScreenOptions{
		MetaData:                     string(metaDataBytes),
		PageBackgroundColor:          tierV2Color.ContentBackgroundColor(),
		HeaderBackgroundColor:        tierV2Color.HeaderBgColor(),
		HeaderOverlayBackgroundColor: tierV2Color.HeaderOverlayBackgroundColor(),
		PillarOverlayGradient:        tierV2Color.HeaderOverlayBackgroundColor(),
		Title:                        cfggenconf.NewTextPb(plansConfig.Title(), ""),
		Subtitle:                     subTitle,
		PlanImage:                    cfggenconf.NewVisualElementImagePb(plansConfig.PlanImage()),
		PillarImage:                  cfggenconf.NewVisualElementImagePb(pillarConfig),
		PillarPlanImageGap:           plansConfig.PillarPlanImageGap(),
		Cta: &deeplinkPb.Cta{
			Type:         deeplinkPb.Cta_CUSTOM,
			Deeplink:     dl,
			Text:         plansConfig.CtaText(),
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
		},
	}

	return &deeplinkPb.Deeplink{
		Screen:          deeplinkPb.Screen_TIER_UPGRADE_SUCCESS_SCREEN_V2,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(upgradeSuccessScreen),
	}, nil
}

// GetUpgradeSuccessDeeplink returns deeplink for upgrade success screen
// eg: Upgrade success screen after add funds
func (s *Service) GetUpgradeSuccessDeeplink(ctx context.Context, feTier beTieringExtPb.Tier, actorId string, appPlatform commontypes.Platform,
	appVersion uint32) (*deeplinkPb.Deeplink, error) {
	configParamsResp, configParamsErr := s.beTieringClient.GetConfigParams(ctx, &beTieringPb.GetConfigParamsRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(configParamsResp, configParamsErr); rpcErr != nil {
		return nil, fmt.Errorf("error getting config params, %w", rpcErr)
	}

	if configParamsResp.GetIsMultipleWaysToEnterTieringEnabledForActor() {
		return s.GetUpgradeSuccessDeeplinkV2(feTier)
	}

	return s.getUpgradeSuccessDeeplinkV1(ctx, feTier, actorId, appPlatform, appVersion)
}

func (s *Service) GetOnbAddFundsUpgradeSuccessDeeplink(ctx context.Context, feTier beTieringExtPb.Tier, actorId string, nextAction *deeplinkPb.Deeplink) (*deeplinkPb.Deeplink, error) {
	if !cfggenconf.IsFeatureEnabledOnPlatform(ctx, s.gconf.Tiering().OnbAddFundsSuccessVersionConstraints()) {
		return nil, errors.New("min version checks failed for onb add funds upgrade success screen")
	}
	screenOptionsConf, getScreenOptionsConfErr := s.gconf.GetOnbAddFundsSuccessScreenOptionsFromMap(feTier.String())
	if getScreenOptionsConfErr != nil {
		return nil, errors.Wrap(getScreenOptionsConfErr, "error getting screen options from config")
	}
	screenOptionsV2, getScreenOptsV2Err := deeplinkv3.GetScreenOptionV2(&tiering.OnboardingAddFundsTieringSuccessScreenOptions{
		BgColor:            screenOptionsConf.BgColor().NewBgColorPb(),
		SuccessMessage:     cfggenconf.NewTextPb(screenOptionsConf.SuccessMessage(), ""),
		TierLogo:           cfggenconf.NewVisualElementImagePb(screenOptionsConf.TierLogo()),
		SocialProofElement: screenOptionsConf.SocialProofElement().NewVisualElementTitleSubtitleElementPb(),
		BenefitsContainer: &tiering.OnboardingAddFundsTieringSuccessScreenOptions_BenefitsContainer{
			Title:    cfggenconf.NewTextPb(screenOptionsConf.BenefitsContainer().Title(), ""),
			Benefits: genconf.NewITCPbFromConfigList(screenOptionsConf.BenefitsContainer().BenefitsList()),
			BgColor:  screenOptionsConf.BenefitsContainer().BgColor().NewBgColorPb(),
		},
		Cta: &deeplinkPb.Cta{
			Type:         deeplinkPb.Cta_CUSTOM,
			Text:         screenOptionsConf.CtaText(),
			Deeplink:     nextAction,
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
		},
	})
	if getScreenOptsV2Err != nil {
		return nil, errors.Wrap(getScreenOptsV2Err, "error getting screen options v2")
	}
	return &deeplinkPb.Deeplink{
		Screen:          deeplinkPb.Screen_ONBOARDING_ADD_FUNDS_TIERING_SUCCESS_SCREEN,
		ScreenOptionsV2: screenOptionsV2,
	}, nil
}
