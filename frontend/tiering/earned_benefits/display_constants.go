package earned_benefits

import (
	"fmt"

	tieringExternalPb "github.com/epifi/gamma/api/tiering/external"
	rewardsPkg "github.com/epifi/gamma/pkg/rewards"
)

const (
	white                   = "#FFFFFF"
	black                   = "#000000"
	blackCat                = "#313234"
	brownBramble            = "#482017"
	nearCyprusBlue          = "#0D3641"
	blueMana                = "#C7ECF9"
	tuftBush                = "#F3CEBA"
	aliceBlue               = "#F6F9FD"
	platinumGrey            = "#6A6D70"
	fiGreen                 = "#00B899"
	chalk100                = "#EFF2F6"
	aircraftExteriorGrey    = "#929599"
	palmLeafGreen           = "#648E4D"
	primo                   = "#86BA6F"
	tropicalRainForestGreen = "#007A56"
	amber50                 = "#FBF3E6"
	aquaPuraGreen           = "#DCF3EE"
	solitudePurple          = "#EAE8F1"
	royalPurple             = "#453C86"
	silverGrey              = "#D6D9DD"
	hunterGreen             = "#37522A"
	paleGreen               = "#D5E6CE"
	creamyYellow            = "#FFE8AD"
	saddleBrown             = "#AB752E"
	skyBlue                 = "#A1CFDE"
	steelBlue               = "#4C869B"
	tanBrown                = "#DBB295"
	airForceBlue            = "#577696"
	hawkesBlue              = "#DBE7F3"
	chestNutBrown           = "#84432E"
	charCoal                = "#28292B"
	charCoal90Alpha         = "#E528292B"
	jetBlack                = "#18191B"
	crimsonPink             = "#D65779"
	powderBlue              = "#8EC5D9"
	nepalBlue               = "#8BA6C3"
	coffeeBrown             = "#A06F57"
	neutralFog200           = "#E6E9ED"
	lightSlateBlue          = "#9DC2D0"
	cadetBlue               = "#96ACC1"
	softPastelBlue          = "#E4F1F5"
	deepGraphite            = "#262728"
	liteCocoaBrown          = "#C29277"
	cherry50                = "#F8E5EB"
	berry50                 = "#EAE8F1"
	berry900                = "#2C2B6E"
	berryGradientLeft       = "#0054BE"
	berryGradientRight      = "#A6D9E9"
	blue                    = "#04254C"
	pattensBlue             = "#D7F0FF"
	lightGreen              = "#EDF5EB"
	lightYellow             = "#FFF8CE"
	darkYellow              = "#98712F"
	paleYellow              = "#FFFCEB"
	golden                  = "#D8B672"
	lightPeriwinkle         = "#C1CCE1"
	coolBlack               = "#002D6A"
	brightGray              = "#E7EBF2"
	blueHaze                = "#CBC6DE"
	lightOrange             = "#D5AB52"
	midOrange               = "#B48535"
	feldspar                = "D8B672"
	lightGrayishOrange      = "#FCF2D8"
	softOrange              = "#DFAE4C"
	moderateOrange          = "#C0723D"
	rootBeer                = "#201207"
	silverLake              = "#DEDDDD"
	approxWhisper           = "#EFEDED"
	nero                    = "#282828"
	moderatePink            = "#A93D5B"
	OldMauve                = "#6D3149"
)

const (
	monthYearFormat = "January 2006"
)

const (
	navigationBarTitle = "%s benefits"
)

const (
	plusIconWithTextEarnedHistory     = "https://epifi-icons.pointz.in/PlusIconWithTextEarnedBenefitHistory"
	infiniteIconWithTextEarnedHistory = "https://epifi-icons.pointz.in/InfiniteIconWithTextEarnedBenefitHistory"
	plusIconWithText                  = "https://epifi-icons.pointz.in/tiering/earned-benefits/plus-icon-with-text.png"
	infiniteIconWithText              = "https://epifi-icons.pointz.in/tiering/earned-benefits/infinite-icon-with-text.png"
	salaryIconWithTextWhite           = "https://epifi-icons.pointz.in/tiering/earned-benefits/salary-icon-with-text-white.png"
	salaryIconWithTextDark            = "https://epifi-icons.pointz.in/tiering/earned-benefits/salary-icon-with-text.png"
	aaSalaryIconWithTextDark          = "https://epifi-icons.pointz.in/aasalary/earned_benefits/prime_badge_with_text.png"
	salaryBasicIconWithText           = "https://epifi-icons.pointz.in/tiering/earned-benefits/salary-basic-icon-with-text.png"
	salaryBasicIconWithTextDark       = "https://epifi-icons.s3.ap-south-1.amazonaws.com/tiering/earned-benefits/salary-basic-icon-with-text-dark-1.png"
	aaSalaryIconWithText              = "https://epifi-icons.pointz.in/tiering/prime_icon_with_text.png"
	plusIconBig                       = "https://epifi-icons.pointz.in/tiering/earned-benefits/plus-icon-big.png"
	plusIconSmall                     = "https://epifi-icons.pointz.in/tiering/earned-benefits/plus-icon-small.png"
	infiniteIconBig                   = "https://epifi-icons.pointz.in/tiering/earned-benefits/infinite-icon-big.png"
	infiniteIconSmall                 = "https://epifi-icons.pointz.in/tiering/earned-benefits/infinite-icon-small.png"
	salaryIconSmall                   = "https://epifi-icons.pointz.in/tiering/earned-benefits/salary-icon-small.png"
	salaryBasicIconSmall              = "https://epifi-icons.pointz.in/tiering/earned-benefits/salary-basic-icon-small.png"
	aaSalaryIconSmall                 = "https://epifi-icons.pointz.in/tiering/earned-benefits/prime-icon-small.png"
	plusChevron                       = "https://epifi-icons.pointz.in/tiering/earned-benefits/plus-chevron.png"
	infiniteChevron                   = "https://epifi-icons.pointz.in/tiering/earned-benefits/infinite-chevron.png"
	aaSalaryChevron                   = "https://epifi-icons.pointz.in/tiering/earned-benefits/aa-salary-chevron.png"
	whiteChevron                      = "https://epifi-icons.pointz.in/tiering/earned-benefits/white-chevron.png"
	platinumGreyChevron               = "https://epifi-icons.pointz.in/tiering/earned-benefits/platinum-grey-chevron.png"
	plusInfoIcon                      = "https://epifi-icons.pointz.in/tiering/earned-benefits/plus-info-icon.png"
	infiniteInfoIcon                  = "https://epifi-icons.pointz.in/tiering/earned-benefits/infinite-info-icon.png"
	infoIcon                          = "https://epifi-icons.pointz.in/tiering/earned-benefits/info-icon.png"
	popupInfoIcon                     = "https://epifi-icons.pointz.in/tiering/earned-benefits/popup-info-icon-green.png"
	cashIcon                          = "https://epifi-icons.pointz.in/tiering/earned-benefits/cash-icon.png"
	fiCoinsIcon                       = "https://epifi-icons.pointz.in/tiering/earned-benefits/fi-coins-icon.png"
	fiCoinsIconGreen                  = "https://epifi-icons.pointz.in/tiering/earned-benefits/fi-coin-icon-fi-green.png"
	fiPointsIcon                      = "https://epifi-icons.pointz.in/rewards/fi_point_icon.png"
	fiCoinsCurrencyIcon               = "https://epifi-icons.pointz.in/tiering/earned-benefits/fi-coins-currency-icon.png"
	checkCircle                       = "https://epifi-icons.pointz.in/tiering/earned-benefits/check-circle.png"
	checkCircleV2                     = "https://epifi-icons.pointz.in/tiering/earned-benefits/check-circle-updated.png"
	purpleBag                         = "https://epifi-icons.pointz.in/tiering/earned-benefits/purple-bag.png"
	lightningEmoji                    = "https://epifi-icons.pointz.in/tiering/earned-benefits/lightning-emoji.png"
	twoDebitCards                     = "https://epifi-icons.pointz.in/tiering/earned-benefits/two-debit-cards.png"
	firstAidBox                       = "https://epifi-icons.pointz.in/tiering/earned-benefits/first-aid-box.png"
	plant3D                           = "https://epifi-icons.pointz.in//savingsAccountClosure/benefits/3d_plant_48.png"
	shoppingBag3D                     = "https://epifi-icons.pointz.in//savingsAccountClosure/benefits/3d_shopping_bag_48.png"
	blackDebitCard3D                  = "https://epifi-icons.pointz.in//savingsAccountClosure/benefits/3d_card_48.png"
	thunderCloudSmall                 = "https://epifi-icons.pointz.in/tiering/earned-benefits/thunder-cloud-small.png"
	ThunderCloudBig                   = "https://epifi-icons.pointz.in/savingsAccountClosure/errors/something-went-wrong-cloud.png"
	rightChevronPurple                = "https://epifi-icons.pointz.in/tiering/earned-benefits/right-chevron-purple.png"
	fiDebitCard                       = "https://epifi-icons.pointz.in/tiering/earned-benefits/fi-debit-card.png"
	fiGreenTick                       = "https://epifi-icons.pointz.in/tiering/earned-benefits/fi-green-tick.png"
	screwDriverSpanner                = "https://epifi-icons.pointz.in/tiering/earned-benefits/screw-driver-spanner.png"
	globe                             = "https://epifi-icons.pointz.in/tiering/earned-benefits/globe.png"
	purpleChequeBook                  = "https://epifi-icons.pointz.in/tiering/earned-benefits/purple-cheque-book.png"
	faqIconGreen                      = "https://epifi-icons.pointz.in/tiering/earned-benefits/green-faq-qn-mark.png"
	tncIcon                           = "https://epifi-icons.pointz.in/tiering/earned-benefits/tnc-icon.png"
	pinkWarningTriangle               = "https://epifi-icons.pointz.in/tiering/earned-benefits/pink-warning-triangle.png"
	pinkLock                          = "https://epifi-icons.pointz.in/tiering/earned-benefits/pink-lock.png"
	vsIcon                            = "https://epifi-icons.pointz.in/tiering/earned-benefits/vs-icon.png"
	popperEmojiBig                    = "https://epifi-icons.pointz.in/tiering/earned-benefits/popper.png"
	dismissIconInfinite               = "https://epifi-icons.pointz.in/tiering/earned-benefits/infinite-dismiss-icon.png"
	dismissIconInfinite1              = "https://epifi-icons.pointz.in/tiering/earned-benefits/infinite-dismiss-icon-1.png"
	dismissIconPlus                   = "https://epifi-icons.pointz.in/tiering/earned-benefits/plus-dismiss-icon.png"
	dismissIconAaSalary               = "https://epifi-icons.pointz.in/tiering/earned-benefits/aa-salary-dismiss-icon.png"
	dangerTrianglePink                = "https://epifi-icons.pointz.in/aa-salary/danger-triangle-pink.png"
	tableCalender                     = "https://epifi-icons.pointz.in/aa-salary/table-calander.png"
	rupeeIconGreenSmall               = "https://epifi-icons.pointz.in/aa-salary/rupee-icon-green-small.png"
	twoPercentCashback3dSilver        = "https://epifi-icons.pointz.in/tiering/earned-benefits/2-percent-cashback-3d-silver.png"
	threePercentCashback3dSilver      = "https://epifi-icons.pointz.in/tiering/earned-benefits/3-percent-cashback-3d-silver.png"
	editIconUrl                       = "https://epifi-icons.pointz.in/aa-salary/edit-icon.png"
	infiniteCheckCircleIcon           = "https://epifi-icons.s3.ap-south-1.amazonaws.com/tiering/earned-benefits/check_circle.png"
	plusCheckCircleIcon               = "https://epifi-icons.s3.ap-south-1.amazonaws.com/tiering/earned-benefits/check_circle_plus.png"
	goldenSandClockIcon               = "https://epifi-icons.pointz.in/tiering/earned-benefits/golden-sand-clock.png"
	moneyPlantBlackAndWhiteIcon       = "https://epifi-icons.pointz.in/tiering/earned-benefits/money-plant-black-and-white"
	moneyPlantColor                   = "https://epifi-icons.pointz.in/tiering/earned-benefits/money-plant-color.png"
	preConversionStaticImg            = "https://epifi-icons.pointz.in/FiCoinsToFiPoints/StaticImagePreConversion.png"
	postConversionStaticImg           = "https://epifi-icons.pointz.in/FiCoinsToFiPoints/FiPointsPostConversionStaticImg"
)

const (
	monthlyRewardTitleText                     = "%s rewards"
	monthlyRewardsPopupCashbackTitle           = "How rewards work"
	monthlyRewardsPopupFiCoinsTitle            = "How Fi-Coin rewards work"
	monthRewardText                            = "%s reward"
	monthlyRewardPopUp2PercentCashbackText     = "You can earn up to %s back in a month when you spend via UPI (Fi app only) or Debit card. Earn Fi-coins worth ₹30/txn, ₹100/day & %s/month. <br><br>Fi-coins earned this month will be credited to your account on the 5th of the next month. Please note, the total value shown could change depending on refunds."
	monthlyRewardPopUpFiCoinsRewardsText       = "You can earn %s Fi-Coins every month on your spends. You will receive the Fi-Coins immediately. But the Fi-Coins earned will be reversed if any payment is unsuccessful and refunded.<br><br><b>%d Fi-Coins = ₹%d</b>"
	plusRewardsMultiplier                      = "2X"
	infiniteRewardsMultiplier                  = "4X"
	aaSalaryRewardsMultiplier                  = "2X"
	cashRewardProgressBarTileTitle             = "PROJECTED REWARDS"
	cashRewardProgressBarTitleInfiniteAaSalary = "%s BACK PROJECTION"
	fiCoinsProgressBarTileTitle                = "%s FI-COINS"
	backWorthProgressBarTileTitle              = "%s BACK WORTH"
	backOnPaymentsTitle                        = "%s back on all spends"
	cashRewardTileSubtitle                     = "Added to account on 5th of every month"
	dailyLimitReached                          = "Reached Daily Limit of ₹%d"
	monthlyLimitReached                        = "Monthly limit reached"
	monthlyLimitAchieved                       = "Monthly limit achieved"
	earnMoreInfoText                           = "Pay with Fi-Federal Debit Card or UPI to earn more"
	redeemInfoText                             = "Redeem Fi-Coins for cash, vouchers, and more"
	monthlyPopupTitle                          = "Monthly Rewards"
	expiredText                                = "Expired"
	totalBenefitsCalcuatedHeader               = "How are your total benefits calculated?"
	totalBenefitsCalcuatedSubtext              = "Your total benefits include the %s Fi-Coins you earn on Debit Card & UPI spends, plus the charges/fees you save on Forex, ordering Debit Card, etc."
	totalWorthCalcuatedSubtextForSalaryBasic   = "Your total benefits include the %s back you earn on Debit Card & UPI (via Fi) spends. Plus, your Debit Card fee and Annual Maintenance fees will be first charged and then reversed back to your account.<br><br><b>%s</b>"
	totalWorthCalcuatedSubtextForSalary        = "Your total benefits include the %s back you earn on Debit Card & UPI (via Fi) spends. Plus, you can save fees on Forex, a new Debit Card & AMC fees.<br><br><b>%s</b>"
	totalWorthCalcuatedSubtext                 = "Your total benefits include %s back that you earn on Debit Card & UPI spends, plus the charges/fees you save on Forex, ordering Debit Card, etc.<br><br><b>%s</b>"
	moneyPlantPost3DaysClaimRewardsText        = "Your rewards are waiting to\nbe claimed"
	moneyPlantClaimRewardsText                 = "Harvest time! Claim your\n%s rewards now"

	infoPopupTitle               = "How %s back works"
	infoPopupTitleSalary         = "Monthly rewards"
	infoPopupBodyTextSalary      = "Salary users will earn 2%% back as Fi-Coins on all UPI (Fi app only) and Debit Card spends. Payments to merchants with minimum order value of ₹100 qualify for this reward. <br><br><b>Limits</b><br>Up to %d Fi-coins/month (worth ₹%d)<br>Up to %d Fi-coins/day (worth ₹%d)<br>Up to %d Fi-coins/txn (worth ₹%d)<br><br>Your Fi-coins earned this month will be ready on the 5th of the next month. Please note that the total value shown is solely a projection and could change depending on refunds/cancellations/limits <br><b>%s</b>"
	infoPopupBodyTextSalaryBasic = "Salary Basic users will earn 1%% back as Fi-Coins on all UPI (Fi app only) and Debit Card spends. Payments to merchants with minimum order value of ₹50 qualify for this reward. <br><br><b>Limits</b><br>Up to %d Fi-coins/month (worth ₹%d)<br>Up to %d Fi-coins/day (worth ₹%d)<br>Up to %d Fi-coins/txn (worth ₹%d)<br><br>Your Fi-coins earned this month will be ready on the 5th of the next month. Please note that the total value shown is solely a projection and could change depending on refunds/cancellations/limits <br><b>%s</b>"
	infoPopupBodyText1           = "You can earn up to %s back in Fi-coins on each transaction when you spend via UPI (Fi app only) or Debit card.\nPayments to merchants with minimum order value of %s qualify for this reward."
	fiCoinsLimitsForPlus         = "Up to %d Fi-coins/month (worth ₹%d)<br>Up to %d Fi-coins/day (worth ₹%d)<br>Up to %d Fi-coins/txn (worth ₹%d)"
	fiCoinsLimitsForInfinite     = "Up to %d Fi-coins/month (worth ₹%d)<br>Up to %d Fi-coins/day (worth ₹%d)<br>Up to %d Fi-coins/txn (worth ₹%d)<br>You can earn up to %d Fi-Coins (worth ₹%d) per calendar month, across all plans, regardless of any plan changes."
	fiCoinsLimitsTextForPrime    = "Up to %d Fi-coins/month (worth ₹%d)<br>Up to %d Fi-coins/day (worth ₹%d)<br>Up to %d Fi-coins/txn (worth ₹%d)<br>You can earn up to %d Fi-Coins (worth ₹%d) per calendar month, across all plans, regardless of any plan changes"
	rewardAmountText             = "Please note that the total value shown is solely a projection and could change depending on refunds/cancellations/limits."
	rewardGiveawayText           = "Your Fi-coins earned this month will be ready by the 5th of the next month."
	infoPopupDisclaimer          = "Your Fi-coins earned this month will be ready to be redeemed on the 5th of the next month. Please note that the total value shown is solely a projection and could change depending on refunds/cancellations/limits."
	plusValueBack                = "1%"
	salaryBasicValueBack         = "1%"
	infiniteValueBack            = "2%"
	primeValueBack               = "3%"
	salaryValueBack              = "2%"
	plusMinOrderValue            = "50"
	infiniteMinOrderValue        = "100"
	primeMinOrderValue           = "100"
	plusLightningText            = "On a spend of ₹100, earn ₹1 worth of Fi-coins"
	infiniteLightningText        = "On a spend of ₹100, earn ₹2 worth of Fi-coins"
	primeLightningText           = "On a spend of ₹100, earn ₹3 worth of Fi-coins"

	debitCardSalaryBasicAmcValue              = "₹0"
	debitCardSalaryAmcValue                   = "₹0"
	debitCardAmcSalaryBasicStrikethroughValue = "₹199"
	debitCardAmcSalaryStrikethroughValue      = "₹199"
)

var (
	monthlyPopupSubTitle = fmt.Sprintf("<b>100 Fi-Coins = ₹%d</b>", int(rewardsPkg.FiCoinsToCashConversionRatioForNonCCUser*100))
)

const (
	dcComponentTitle              = "Debit card benefits"
	healthInsuranceTitle          = "Health insurance"
	healthInsuranceBenefits       = "View your health insurance benefits"
	monthlyRewards                = "Monthly rewards"
	inactiveStateTitle            = "Save more with Fi-Federal Debit Card"
	healthInsuranceInactiveTitle  = "Give your health insurance a boost"
	zeroForexTitle                = "0 Forex charges"
	zeroForexSubTitle             = "Save 3.5% on international spends"
	tapPayAndEarnTitle            = "Tap,Pay & Earn"
	tapPayAndEarnSubTitle         = "Tap your card or phone to pay"
	freeAtmWithdrawalTitle        = "Free ATM withdrawals*"
	freeAtmWithdrawalSubTitle     = "Withdraw from anywhere in India"
	dcCashbackTitle               = "%s back "
	dcCashbackSubTitle            = "Get %s back on all your domestic spends"
	orderNow                      = "Order now"
	Retry                         = "Retry"
	activateNow                   = "Activate"
	loadFailedTitle               = "Unable to load benefits"
	exploreDcOffers               = "Explore Debit Card offers"
	plusCardChargesTitle          = "Saved ₹%v in order fees"
	debitCardChargeDiscountedBody = "%s users get the Fi-Federal Debit card worth %s for %s"
	infiniteCardChargesTitle      = "Debit Card order charges"
	salaryCardChargesTitle        = "No Debit Card charge"
	salaryBasicCardChargesTitle   = "Debit Card charges on us"
	debitCardChargesFeeBody       = "As you are on the %s Plan, you get the Fi-Federal Debit Card worth %s for %s"
	freeCardActiveTitle           = "Debit Card order fee"
	forexRefundActiveTitle        = "Forex refund"
	dcAmcTitle                    = "Debit Card AMC"
	plusForexRefundPopupTitle     = "Forex refund"
	plusForexRefundPopupBody      = "You enjoy 0 Forex fee on spends up to ₹30,000 per month. After that, a 3.5% Forex fee will be charged on all international Debit Card spends."
	infiniteForexPopupTitle       = "Uncapped Zero Forex charges"
	salaryForexPopupTitle         = "Forex refund"
	salaryBasicForexPopupTitle    = "Forex refund"
	salaryAMCPopupTitle           = "No Debit Card Annual Maintenance Charge (AMC)"
	salaryBasicAMCPopupTitle      = "Debit Card Annual Maintenance Charge (AMC) covered by us"
	infiniteForexPopupBody        = "Enjoy Zero Forex charges on unlimited international payments. At first, a 3.5% forex markup will be charged on all international spends with your Debit Card. However, it will be reversed for eligible transactions within 30 days."
	salaryForexPopupBody          = "You enjoy 0 Forex fee on unlimited spends up. The forex fee will be charged first <b>and then refunded later.</b>"
	salaryBasicForexPopupBody     = "You enjoy 0 Forex fee on eligible spends up to ₹30,000 per month. The forex fee of 3.5% will be charged <b>and then refunded later.</b>"
	salaryAMCPopupBody            = "For Salary users, AMC will be free."
	salaryBasicAMCPopupBody       = "Annual Card Maintenance Fee is ₹0 for DC spends above <b>₹25,000.</b> On spends below that, a fee of <b>₹199+GST</b> is charged on the date of your card anniversary and then reversed to your account."
	monthlyTransferAmount         = "Monthly transfer amount"
	transferNow                   = "Transfer now"
	investInUSStocks              = "Invest in US Stocks"
	addFundsSATitle               = "Add funds in your savings account"
	maintainFixedDeposit          = "Maintain Fixed Deposit"

	salaryDebitCardChargesFeeBody   = "Salary users will get a brand new VISA Platinum Fi-Federal Debit Card for free."
	salaryBasicDebitCardChargesBody = "Card issuance fee of <b>₹399+GST</b> is charged at the time of ordering and is then <b>reversed</b> to your account."
)

const (
	otherBenefitsComponentTitle = "Other benefits"
	chequeBookBenefitItemTitle  = "Chequebook order charges"
	chequeBookInactiveTitle     = "Your first 10-leaf chequebook worth  ₹100 is free"
	orderChequeBook             = "Order Chequebook"
)

const (
	higherRewardsComponentTitle = "Unlock higher rewards"
	higherRewardsHeaderText     = "Same spends, higher rewards"
	upgradeNow                  = "Upgrade now"
	recommended                 = "RECOMMENDED"
	fiCoins                     = " Fi-Coins"
	back                        = " back"
	haveEarned                  = "You earned\n%s"
	couldHaveEarned             = "You could've earned\n%s"
)

const (
	morePlansComponentTitle  = "Quick links"
	currEmployer             = "Current employer"
	faqs                     = "FAQs"
	tncs                     = "TnCs"
	cancelledChequeBookText  = "Cancelled cheque"
	chequeBookText           = "Chequebook"
	downloadAccStatementText = "Download account statement"
	tierBenefitsText         = "%s plan benefits"
)

const (
	planExpiredText       = "%s Plan expired! Add %s to get access again."
	planExpiresSoonText   = "%s Plan expires soon! Add %s to continue enjoying benefits."
	balBelowThresholdText = "Account balance is below %s. Add money to keep enjoying %s benefits."
	daysLeftText          = "DAYS LEFT"
	dayLeftText           = "DAY LEFT"
	accessToPlanWithdrawn = "Access to %s withdrawn. Restart monthly transfers now!"
	doNotMissOutXDaysLeft = "Don't miss out on %s back on your %s spends. Add money."
	addMoneyText          = "Add money to restart earning %s back on your spends"
	rewardReadyText       = "Your reward for %s will be ready by 5th %s"
)

const (
	ErrorScreenTitle    = "Uh-oh...something went wrong!"
	ErrorScreenSubTitle = "We were unable to load this page. Please try again."
)

const (
	TotalBenefitsEarnedTitle = "TOTAL BENEFITS EARNED"
	EarnedBenefitsWorthTitle = "TOTAL BENEFITS EARNED WORTH"
	benefitsEarned           = "BENEFITS EARNED"
	question                 = "How is this calculated?"
)

const (
	rewardRockstarPopupTitle = "You were a reward rockstar last month!"
	cashbackEarned           = "%s back earned"
	fiCoinsEarned            = "%d Fi-Coins earned"
	ctaText                  = "Ok, got it"
)

const (
	yetToReceiveMonthlyTransfer       = "Monthly transfer: Yet to be received!"
	moneyTransferDueInXdays           = "Monthly transfer: Due in %d days. Transfer to continue receiving rewards."
	moneyTransferDueToday             = "Monthly transfer: Due today. Transfer to continue receiving rewards."
	addMoneyToEarnCashbackText        = "Add money on the 5th of each month: Keep earning rewards on your spends"
	completedCaps                     = "COMPLETED"
	popularCaps                       = "POPULAR"
	pendingCaps                       = "PENDING"
	upcomingCaps                      = "UPCOMING"
	currentCaps                       = "CURRENT"
	transferSalaryPopupHeader         = "Monthly transfer amount"
	transferSalaryPopupBody           = "The money you transfer monthly to your account linked to Fi — gets you %s back on all your spends with the Fi-Federal Debit Card & UPI.\n\nYour reward value is calculated based on your salary range."
	nextTransferFromToDateMessage     = "Next transfer date is from %s %s to %s %s"
	AddFundsToEarnBackMessage         = "Add by %s of every month to earn %s back"
	msgAfterTransferWindowStarted     = "Add money before %02d/%02d, to keep earning %s back on your %s spends"
	xDaysLeftMsg                      = "%d days left. Add money to keep earning %s back on your %s spends"
	lastDateToAddMoneyMsg             = "Last date to add money is %02d/%02d"
	lastDateTodayMsg                  = "Last day to add money"
	keepYourFDReady                   = "Keep your FD active to continue earning rewards"
	renewFDText                       = "Renew your FD to access %s back"
	investEveryMonthToEarnCashback    = "Invest on the 5th of every month to earn rewards"
	topUpUSStocksWalletText           = "Top-up your US Stocks wallet to access %s back"
	addEveryMonthToEarnCashback       = "Add by the 5th of every month to earn rewards"
	speedUpEarningText                = "Speed up your earnings"
	rewardsGotBetter                  = "Your rewards just got better"
	rewardsGettingCloseText           = "Your rewards are getting closer"
	viewMoreText                      = "View more"
	graceEarnedBenefitsText           = "Low balance? Invest in US Stocks to keep enjoying the perks of %s"
	autoUpgradedText                  = "You’ve been auto-upgraded to %s! Great job investing in US Stocks"
	pitchHigherTierText               = "Add %s and upgrade to %s to earn %s back"
	pitchPrimeThroughSABal            = "Add %s to keep enjoying %s rewards"
	investToEnjoyPrimeText            = "Invest %s in US Stocks to keep enjoying %s rewards"
	maintainToEnjoyPrimeText          = "Maintain %s to keep enjoying %s rewards"
	tierDowngradedText                = "You lost access to %s and your earned rewards. Add %s to reclaim benefits"
	tierGraceText1                    = "Low balance. Add %s to stay on %s"
	addFundsToAvoidLosingText         = "Add funds to avoid losing your projected rewards"
	dontLoseBackOnYourSpendsText      = "Don’t lose %s back on your spends! Add %s now"
	daysLeftAddMoney                  = "Only %d days left! Add %s now to keep %s rewards"
	finalCallBackExpiresTodayText     = "Final call! %s back on all spends expires today"
	makeEverySpendCountText           = "You're upgraded to %s rewards – make every spend count!"
	salaryCreditedButNotReflectingYet = "Salary credited but not reflecting yet?"
	addBalanceText                    = "Add balance"
)

const (
	grayPencilIcon                              = "https://epifi-icons.pointz.in/tiering/earned-benefits/gray-pencil.png"
	addMoneyIcon                                = "https://epifi-icons.pointz.in/multiple-ways-tiering/Add-Money.png"
	bankTransferIcon                            = "https://epifi-icons.s3.ap-south-1.amazonaws.com/multiple-ways-tiering/bank-image-3x.png"
	checkCircleIcon                             = "https://epifi-icons.pointz.in/multiple-ways-tiering/check-circle.png"
	warningErrorIcon                            = "https://epifi-icons.pointz.in/multiple-ways-tiering/warning-error.png"
	bellIcon                                    = "https://epifi-icons.pointz.in/multiple-ways-tiering/bell.png"
	lightningIcon                               = "https://epifi-icons.pointz.in/multiple-ways-tiering/lighting.png"
	usDollarIcon                                = "https://epifi-icons.pointz.in/multiple-ways-tiering/us-dollar-sign.png"
	lockerSign                                  = "https://epifi-icons.pointz.in/multiple-ways-tiering/locker.png"
	arrowIcon                                   = "https://epifi-icons.pointz.in/multiple-ways-tiering/arrow-3x.png"
	checkCircleDepositIcon                      = "https://epifi-icons.pointz.in/multiple-ways-tiering/check-circle-3x.png"
	checkCircleUSStocksIcon                     = "https://epifi-icons.pointz.in/multiple-ways-tiering/check-circle-us-stocks.png"
	infoIconBlack                               = "https://epifi-icons.pointz.in/multiple-ways-tiering/info-icon-black.png"
	moneyPlantIcon                              = "https://epifi-icons.pointz.in/tiering/earned-benefits/money-plant.png"
	moneyPlantBlurIcon                          = "https://epifi-icons.pointz.in/tiering/earned-benefits/money-plant-blur.png"
	starIcon                                    = "https://epifi-icons.pointz.in/tiering/earned-benefits/star-icon-header.png"
	carouselPlaceholderImage                    = "https://epifi-icons.pointz.in/tiering/earned-benefits/carousel-placeholder.png"
	carouselPlaceholderImageV2                  = "https://epifi-icons.pointz.in/tiering/earned-benefits/carousel-placeholder-v2.png"
	upiCoinOptJson                              = "https://epifi-icons.pointz.in/tiering/upi_coin.json"
	fiPointsPreConversion                       = "https://epifi-icons.pointz.in/FiCoinsToFiPoints/FiPointsPreConversion.json"
	fiPointsPostConversion                      = "https://epifi-icons.pointz.in/FiCoinsToFiPoints/FiPointsPostConversion.json"
	starIcons                                   = "https://epifi-icons.pointz.in/tiering/earned-benefits/staricons.png"
	progressBarSlash                            = "https://epifi-icons.pointz.in/tiering/earned-benefits/slash.png"
	fiCoinsCurrencyIconV2                       = "https://epifi-icons.pointz.in/tiering/earned-benefits/golden-fi-coins.png"
	informationIcon                             = "https://epifi-icons.pointz.in/tiering/earned-benefits/banner-v2-information.png"
	dollarIcon                                  = "https://epifi-icons.pointz.in/tiering/earned-benefits/banner-v2-dollar-icon.png"
	plusEarnedBenefitsPagePitchStarIcon         = "https://epifi-icons.pointz.in/tiering/earned-benefits/banner-v2-star-plus-icon.png"
	plusEarnedBenefitsPagePitchInfiniteStarIcon = "https://epifi-icons.pointz.in/earned-benefits/banner-v2-star-infinite-icon.png"
	earnedBenefitsPagePitchPrimeStarIcon        = "https://epifi-icons.pointz.in/earned-benefits/banner-v2-star-prime-icon.png"
	alertIcon                                   = "https://epifi-icons.pointz.in/tiering/earned-benefits/alerts-v2.png"
	moneyIcon                                   = "https://epifi-icons.pointz.in/tiering/earned-benefits/money-icon-v2.png"
)

const (
	OnsurityInputPageTitle                  = "Let’s activate your health insurance"
	OnsurityInputPageSubTitle               = "Please enter your details below"
	OnsurityInputPagePlaceHolderFullName    = "Full Name"
	OnsurityInputPagePlaceHolderDOB         = "Date of Birth"
	OnsurityInputPagePlaceHolderPhoneNumber = "Mobile Number"
	OnsurityInputPagePlaceHolderGender      = "Gender"
	OnsurityInputPageCtaText                = "Submit details"
	OnsurityInputPageGenderBSTitle          = "Select your gender"
	OnsurityAppDeeplink                     = "https://onsurity-deeplinks.app.link/Home"
	OnsurityInputPageDobDateLayout          = "02/01/2006"
)

const (

	// Salary Tier Constants
	salaryDcBenefitCardTitle     = "Free Physical Debit Card"
	salaryDcBenefitCardSubtitle  = "Yes, it’s VISA Platinum"
	salaryDcBenefitForexTitle    = "0 forex charges"
	salaryDcBenefitForexSubtitle = "On eligible spends – travel guilt-free!"
	salaryDcBenefitAmcTitle      = "No Physical Debit Card AMC"
	salaryDcBenefitAmcSubtitle   = "No surprises, totally free"

	// Salary Basic Tier Constants
	salaryBasicDcBenefitCardTitle     = "Card fee of ₹399+ GST covered by us"
	salaryBasicDcBenefitCardSubtitle  = "The fee will be reversed to you"
	salaryBasicDcBenefitForexTitle    = "0 forex charges"
	salaryBasicDcBenefitForexSubtitle = "On eligible spends up to ₹30K/month"
	salaryBasicDcBenefitAmcTitle      = "Debit Card AMC covered by us"
	salaryBasicDcBenefitAmcSubtitle   = "Annual maintenance charges reversed"
)

// Debit Card Benefit Item struct to hold title, subtitle and icon
type DebitCardBenefitItem struct {
	Title    string
	Subtitle string
	Icon     string
}

// Map to store benefit items for each tier
var SalaryTierDebitCardBenefits = map[tieringExternalPb.Tier][]DebitCardBenefitItem{
	tieringExternalPb.Tier_TIER_FI_SALARY: {
		{
			Title:    salaryDcBenefitCardTitle,
			Subtitle: salaryDcBenefitCardSubtitle,
			Icon:     plant3D,
		},
		{
			Title:    salaryDcBenefitForexTitle,
			Subtitle: salaryDcBenefitForexSubtitle,
			Icon:     blackDebitCard3D,
		},
		{
			Title:    salaryDcBenefitAmcTitle,
			Subtitle: salaryDcBenefitAmcSubtitle,
			Icon:     shoppingBag3D,
		},
	},
	tieringExternalPb.Tier_TIER_FI_SALARY_BASIC: {
		{
			Title:    salaryBasicDcBenefitCardTitle,
			Subtitle: salaryBasicDcBenefitCardSubtitle,
			Icon:     plant3D,
		},
		{
			Title:    salaryBasicDcBenefitForexTitle,
			Subtitle: salaryBasicDcBenefitForexSubtitle,
			Icon:     blackDebitCard3D,
		},
		{
			Title:    salaryBasicDcBenefitAmcTitle,
			Subtitle: salaryBasicDcBenefitAmcSubtitle,
			Icon:     shoppingBag3D,
		},
	},
}
