package auth

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/proto"
	"github.com/stretchr/testify/assert"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"

	authPb "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/auth/biometrics"
	biometricsMock "github.com/epifi/gamma/api/auth/biometrics/mocks"
	livenessPb "github.com/epifi/gamma/api/auth/liveness"
	livenessMock "github.com/epifi/gamma/api/auth/liveness/mocks"
	authMock "github.com/epifi/gamma/api/auth/mocks"
	authOrchMock "github.com/epifi/gamma/api/auth/orchestrator/mocks"
	"github.com/epifi/gamma/api/frontend/auth"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	userPb "github.com/epifi/gamma/api/user"
	userMock "github.com/epifi/gamma/api/user/mocks"
)

func TestService_GetDeviceIntegrityNonce(t *testing.T) {
	type args struct {
		req *auth.GetDeviceIntegrityNonceRequest
	}
	tests := []struct {
		name       string
		args       args
		want       *auth.GetDeviceIntegrityNonceResponse
		wantErr    string
		setupMocks func(*authMock.MockAuthClient)
	}{
		{
			name: "get device integrity nonce successful",
			args: args{
				req: &auth.GetDeviceIntegrityNonceRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							Device: &commontypes.Device{
								DeviceId: "device_123",
							},
						},
					},
				},
			},
			want: &auth.GetDeviceIntegrityNonceResponse{
				Nonce:  "nonce_123",
				Status: rpc.StatusOk(),
			},
			setupMocks: func(mockAuthClient *authMock.MockAuthClient) {
				mockAuthClient.EXPECT().GetDeviceIntegrityNonce(gomock.Any(), &authPb.GetDeviceIntegrityNonceRequest{
					Device: &commontypes.Device{
						DeviceId: "device_123",
					},
				}).Return(&authPb.GetDeviceIntegrityNonceResponse{
					Nonce:  "nonce_123",
					Status: rpc.StatusOk(),
				}, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			mockAuthClient := authMock.NewMockAuthClient(ctr)
			s := &Service{
				client: mockAuthClient,
			}
			tt.setupMocks(mockAuthClient)

			got, err := s.GetDeviceIntegrityNonce(context.Background(), tt.args.req)
			if err != nil {
				if !assert.Equal(t, tt.wantErr, err.Error()) {
					t.Errorf("GetDeviceIntegrityNonce() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
			}

			if !proto.Equal(got, tt.want) {
				t.Errorf("GetDeviceIntegrityNonce() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GenerateOtp(t *testing.T) {
	type args struct {
		req *auth.GenerateOtpRequest
	}
	tests := []struct {
		name       string
		args       args
		want       *auth.GenerateOtpResponse
		wantErr    string
		setupMocks func(*authMock.MockAuthClient)
	}{
		{
			name: "otp generated successfully",
			args: args{
				req: &auth.GenerateOtpRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor_1",
						},
					},
					CardAuthAttemptId: "attempt_id1",
					RequestType:       auth.RequestType_CARD_PIN,
				},
			},
			setupMocks: func(mockAuthClient *authMock.MockAuthClient) {
				mockAuthClient.EXPECT().GenerateVendorOtp(gomock.Any(), &authPb.GenerateVendorOtpRequest{
					ActorId:           "actor_1",
					RequestType:       authPb.RequestType_CARD_PIN,
					CardAuthAttemptId: "attempt_id1",
				}).Return(&authPb.GenerateVendorOtpResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			want: &auth.GenerateOtpResponse{
				Status: rpc.StatusOk(),
			},
		},
		{
			name: "otp generation failed",
			args: args{
				req: &auth.GenerateOtpRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor_1",
						},
					},
					CardAuthAttemptId: "attempt_id2",
					RequestType:       auth.RequestType_CARD_PIN,
				},
			},
			setupMocks: func(mockAuthClient *authMock.MockAuthClient) {
				mockAuthClient.EXPECT().GenerateVendorOtp(gomock.Any(), gomock.Any()).Return(&authPb.GenerateVendorOtpResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want: &auth.GenerateOtpResponse{
				Status: rpc.StatusInternalWithDebugMsg("unable to generate vendor otp"),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			mockAuthClient := authMock.NewMockAuthClient(ctr)

			s := &Service{
				client: mockAuthClient,
			}
			tt.setupMocks(mockAuthClient)
			got, err := s.GenerateOtp(context.Background(), tt.args.req)
			if err != nil {
				if !assert.Equal(t, tt.wantErr, err.Error()) {
					t.Errorf("GenerateOtp() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GenerateOtp() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_VerifyDeviceIntegrity(t *testing.T) {
	type args struct {
		req *auth.VerifyDeviceIntegrityRequest
	}

	var mockAuthClient *authMock.MockAuthClient

	tests := []struct {
		name       string
		args       args
		want       *auth.VerifyDeviceIntegrityResponse
		wantErr    string
		setupMocks func(*authMock.MockAuthClient)
	}{
		{
			name: "device integrity verified successfully",
			args: args{
				req: &auth.VerifyDeviceIntegrityRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							Device: &commontypes.Device{
								DeviceId: "device_1",
							},
						},
					},
					AttestationToken: "token_1",
					Nonce:            "nonce",
				},
			},
			want: &auth.VerifyDeviceIntegrityResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				DeviceIntegrityToken: "integrity_1",
			},
			setupMocks: func(mockAuthClient *authMock.MockAuthClient) {
				mockAuthClient.EXPECT().VerifyDeviceIntegrity(gomock.Any(), gomock.Any()).Return(&authPb.VerifyDeviceIntegrityResponse{
					Status:            rpc.StatusOk(),
					DeviceIntegrityId: "integrity_1",
				}, nil)
			},
		},

		{
			name: "device integrity verification failed",
			args: args{
				req: &auth.VerifyDeviceIntegrityRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							Device: &commontypes.Device{
								DeviceId: "device_2",
							},
						},
					},
					AttestationToken: "token_1",
				},
			},
			want: &auth.VerifyDeviceIntegrityResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusInternalWithDebugMsg("error in verifying device integrity"),
				},
			},
			setupMocks: func(mockAuthClient *authMock.MockAuthClient) {
				mockAuthClient.EXPECT().VerifyDeviceIntegrity(gomock.Any(), gomock.Any()).Return(&authPb.VerifyDeviceIntegrityResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			mockAuthClient = authMock.NewMockAuthClient(ctr)
			s := &Service{
				client: mockAuthClient,
				conf:   gconf,
			}
			tt.setupMocks(mockAuthClient)
			got, err := s.VerifyDeviceIntegrity(context.Background(), tt.args.req)
			if err != nil {
				if !assert.Equal(t, tt.wantErr, err.Error()) {
					t.Errorf("VerifyDeviceIntegrity() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("VerifyDeviceIntegrity() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_SyncBiometricIdentifier(t *testing.T) {
	var (
		reqH = &header.RequestHeader{
			Auth: &header.AuthHeader{
				Device: &commontypes.Device{
					DeviceId: "device_1",
				},
				ActorId: "actorId",
			},
		}
		biometricId       = "biometricId"
		biometricId2      = "biometricId2"
		biometricColumnId = "biometricColumnId"
	)
	type args struct {
		req *auth.SyncBiometricIdentifierRequest
	}
	type mockedDependencies struct {
		authClient       *authMock.MockAuthClient
		authOrchClient   *authOrchMock.MockOrchestratorClient
		biometricsClient *biometricsMock.MockBiometricsServiceClient
		userClient       *userMock.MockUsersClient
		livClient        *livenessMock.MockLivenessClient
	}
	tests := []struct {
		name       string
		args       args
		want       *auth.SyncBiometricIdentifierResponse
		wantErr    string
		setupMocks func(args args, md *mockedDependencies)
	}{
		{
			name: "first time biometric",
			args: args{
				req: &auth.SyncBiometricIdentifierRequest{
					Req:         reqH,
					BiometricId: biometricId,
				},
			},
			want: &auth.SyncBiometricIdentifierResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
			},
			setupMocks: func(args args, md *mockedDependencies) {
				md.biometricsClient.EXPECT().GetBiometricsDetails(gomock.Any(), &biometrics.GetBiometricsDetailsRequest{
					ActorId: args.req.GetReq().GetAuth().GetActorId(),
				}).Return(&biometrics.GetBiometricsDetailsResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				md.biometricsClient.EXPECT().SyncBiometricIdentifier(gomock.Any(), &biometrics.SyncBiometricIdentifierRequest{
					ActorId: args.req.GetReq().GetAuth().GetActorId(),
					BiometricInfo: &biometrics.BiometricInfo{
						BiometricId: args.req.GetBiometricId(),
						AppPlatform: args.req.GetReq().GetAuth().GetDevice().GetPlatform(),
						DeviceId:    args.req.GetReq().GetAuth().GetDevice().GetDeviceId(),
						AppVersion:  int64(args.req.GetReq().GetAuth().GetDevice().GetAppVersion()),
					},
				}).Return(&biometrics.SyncBiometricIdentifierResponse{
					Status: rpc.StatusOk(),
				}, nil)
				md.biometricsClient.EXPECT().UpdateBiometricsDetails(gomock.Any(), &biometrics.UpdateBiometricsDetailsRequest{
					ActorId:                args.req.GetReq().GetAuth().GetActorId(),
					UpdatedBiometricStatus: biometrics.BiometricStatus_BIOMETRIC_STATUS_VERIFIED,
				}).Return(&biometrics.UpdateBiometricsDetailsResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
		},
		{
			name: "initiate liveness",
			args: args{
				req: &auth.SyncBiometricIdentifierRequest{
					Req:         reqH,
					BiometricId: biometricId,
				},
			},
			want: &auth.SyncBiometricIdentifierResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				NextAction: &dlPb.Deeplink{
					Screen: dlPb.Screen_CHECK_LIVENESS,
				},
			},
			setupMocks: func(args args, md *mockedDependencies) {
				md.biometricsClient.EXPECT().GetBiometricsDetails(gomock.Any(), &biometrics.GetBiometricsDetailsRequest{
					ActorId: args.req.GetReq().GetAuth().GetActorId(),
				}).Return(&biometrics.GetBiometricsDetailsResponse{
					BiometricStatus: biometrics.BiometricStatus_BIOMETRIC_STATUS_VERIFIED,
					BiometricInfo: &biometrics.BiometricInfo{
						BiometricId: biometricId2,
						AppPlatform: args.req.GetReq().GetAuth().GetDevice().GetPlatform(),
						DeviceId:    args.req.GetReq().GetAuth().GetDevice().GetDeviceId(),
						AppVersion:  int64(args.req.GetReq().GetAuth().GetDevice().GetAppVersion()),
					},
					BiometricTableId: biometricColumnId,
					Status:           rpc.StatusOk(),
				}, nil).Times(2)
				md.userClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_ActorId{
						ActorId: args.req.GetReq().GetAuth().GetActorId(),
					},
				}).Return(&userPb.GetUserResponse{
					Status: rpc.StatusOk(),
					User: &userPb.User{
						Profile: &userPb.Profile{
							Photo: &commontypes.Image{},
						},
					},
				}, nil)
				md.biometricsClient.EXPECT().SyncBiometricIdentifier(gomock.Any(), &biometrics.SyncBiometricIdentifierRequest{
					ActorId: args.req.GetReq().GetAuth().GetActorId(),
					BiometricInfo: &biometrics.BiometricInfo{
						BiometricId: args.req.GetBiometricId(),
						AppPlatform: args.req.GetReq().GetAuth().GetDevice().GetPlatform(),
						DeviceId:    args.req.GetReq().GetAuth().GetDevice().GetDeviceId(),
						AppVersion:  int64(args.req.GetReq().GetAuth().GetDevice().GetAppVersion()),
					},
				}).Return(&biometrics.SyncBiometricIdentifierResponse{
					Status: rpc.StatusOk(),
				}, nil)
				gomock.InOrder(
					md.livClient.EXPECT().GetLivenessSummary(gomock.Any(), &livenessPb.GetLivenessSummaryRequest{
						ActorId:      args.req.GetReq().GetAuth().GetActorId(),
						RequestId:    biometricColumnId,
						LivenessFlow: livenessPb.LivenessFlow_DEVICE_BIOMETRIC,
					}).Return(&livenessPb.GetLivenessSummaryResponse{
						Status: rpc.StatusOk(),
					}, nil),
					md.livClient.EXPECT().GetLivenessSummaryStatus(gomock.Any(), &livenessPb.GetLivenessSummaryStatusRequest{
						ActorId:      args.req.GetReq().GetAuth().GetActorId(),
						RequestId:    biometricColumnId,
						LivenessFlow: livenessPb.LivenessFlow_DEVICE_BIOMETRIC,
					}).Return(&livenessPb.GetLivenessSummaryStatusResponse{
						Status: rpc.StatusRecordNotFound(),
					}, nil).Times(1),
					md.livClient.EXPECT().CreateLivenessSummary(gomock.Any(), gomock.Any()).Return(&livenessPb.CreateLivenessSummaryResponse{
						Status: rpc.StatusOk(),
					}, nil),
					md.livClient.EXPECT().GetLivenessSummaryStatus(gomock.Any(), &livenessPb.GetLivenessSummaryStatusRequest{
						ActorId:      args.req.GetReq().GetAuth().GetActorId(),
						RequestId:    biometricColumnId,
						LivenessFlow: livenessPb.LivenessFlow_DEVICE_BIOMETRIC,
					}).Return(&livenessPb.GetLivenessSummaryStatusResponse{
						Status: rpc.StatusOk(),
						NextAction: &dlPb.Deeplink{
							Screen: dlPb.Screen_CHECK_LIVENESS,
						},
					}, nil),
				)
			},
		},
		{
			name: "resume liveness",
			args: args{
				req: &auth.SyncBiometricIdentifierRequest{
					Req:         reqH,
					BiometricId: biometricId,
				},
			},
			want: &auth.SyncBiometricIdentifierResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				NextAction: &dlPb.Deeplink{
					Screen: dlPb.Screen_CHECK_LIVENESS,
				},
			},
			setupMocks: func(args args, md *mockedDependencies) {
				md.biometricsClient.EXPECT().GetBiometricsDetails(gomock.Any(), &biometrics.GetBiometricsDetailsRequest{
					ActorId: args.req.GetReq().GetAuth().GetActorId(),
				}).Return(&biometrics.GetBiometricsDetailsResponse{
					BiometricStatus: biometrics.BiometricStatus_BIOMETRIC_STATUS_VERIFIED,
					BiometricInfo: &biometrics.BiometricInfo{
						BiometricId: biometricId2,
						AppPlatform: args.req.GetReq().GetAuth().GetDevice().GetPlatform(),
						DeviceId:    args.req.GetReq().GetAuth().GetDevice().GetDeviceId(),
						AppVersion:  int64(args.req.GetReq().GetAuth().GetDevice().GetAppVersion()),
					},
					BiometricTableId: biometricColumnId,
					Status:           rpc.StatusOk(),
				}, nil).Times(2)
				md.userClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_ActorId{
						ActorId: args.req.GetReq().GetAuth().GetActorId(),
					},
				}).Return(&userPb.GetUserResponse{
					Status: rpc.StatusOk(),
					User: &userPb.User{
						Profile: &userPb.Profile{
							Photo: &commontypes.Image{},
						},
					},
				}, nil)
				md.biometricsClient.EXPECT().SyncBiometricIdentifier(gomock.Any(), &biometrics.SyncBiometricIdentifierRequest{
					ActorId: args.req.GetReq().GetAuth().GetActorId(),
					BiometricInfo: &biometrics.BiometricInfo{
						BiometricId: args.req.GetBiometricId(),
						AppPlatform: args.req.GetReq().GetAuth().GetDevice().GetPlatform(),
						DeviceId:    args.req.GetReq().GetAuth().GetDevice().GetDeviceId(),
						AppVersion:  int64(args.req.GetReq().GetAuth().GetDevice().GetAppVersion()),
					},
				}).Return(&biometrics.SyncBiometricIdentifierResponse{
					Status: rpc.StatusOk(),
				}, nil)
				md.livClient.EXPECT().GetLivenessSummary(gomock.Any(), &livenessPb.GetLivenessSummaryRequest{
					ActorId:      args.req.GetReq().GetAuth().GetActorId(),
					RequestId:    biometricColumnId,
					LivenessFlow: livenessPb.LivenessFlow_DEVICE_BIOMETRIC,
				}).Return(&livenessPb.GetLivenessSummaryResponse{
					Status: rpc.StatusOk(),
				}, nil)
				md.livClient.EXPECT().GetLivenessSummaryStatus(gomock.Any(), &livenessPb.GetLivenessSummaryStatusRequest{
					ActorId:      args.req.GetReq().GetAuth().GetActorId(),
					RequestId:    biometricColumnId,
					LivenessFlow: livenessPb.LivenessFlow_DEVICE_BIOMETRIC,
				}).Return(&livenessPb.GetLivenessSummaryStatusResponse{
					Status: rpc.StatusOk(),
					NextAction: &dlPb.Deeplink{
						Screen: dlPb.Screen_CHECK_LIVENESS,
					},
				}, nil).Times(1)
			},
		},
		{
			name: "liveness completed",
			args: args{
				req: &auth.SyncBiometricIdentifierRequest{
					Req:         reqH,
					BiometricId: biometricId,
				},
			},
			want: &auth.SyncBiometricIdentifierResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				// Not returning a deeplink to avoid loading same screen twice
			},
			setupMocks: func(args args, md *mockedDependencies) {
				md.biometricsClient.EXPECT().GetBiometricsDetails(gomock.Any(), &biometrics.GetBiometricsDetailsRequest{
					ActorId: args.req.GetReq().GetAuth().GetActorId(),
				}).Return(&biometrics.GetBiometricsDetailsResponse{
					BiometricStatus: biometrics.BiometricStatus_BIOMETRIC_STATUS_REVOKED,
					BiometricInfo: &biometrics.BiometricInfo{
						BiometricId: biometricId2,
						AppPlatform: args.req.GetReq().GetAuth().GetDevice().GetPlatform(),
						DeviceId:    args.req.GetReq().GetAuth().GetDevice().GetDeviceId(),
						AppVersion:  int64(args.req.GetReq().GetAuth().GetDevice().GetAppVersion()),
					},
					BiometricTableId: biometricColumnId,
					UpdatedAt:        &timestamppb.Timestamp{Seconds: 1609678800},
					Status:           rpc.StatusOk(),
				}, nil)
				md.userClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_ActorId{
						ActorId: args.req.GetReq().GetAuth().GetActorId(),
					},
				}).Return(&userPb.GetUserResponse{
					Status: rpc.StatusOk(),
					User: &userPb.User{
						Profile: &userPb.Profile{
							Photo: &commontypes.Image{},
						},
					},
				}, nil)
				md.biometricsClient.EXPECT().SyncBiometricIdentifier(gomock.Any(), &biometrics.SyncBiometricIdentifierRequest{
					ActorId: args.req.GetReq().GetAuth().GetActorId(),
					BiometricInfo: &biometrics.BiometricInfo{
						BiometricId: args.req.GetBiometricId(),
						AppPlatform: args.req.GetReq().GetAuth().GetDevice().GetPlatform(),
						DeviceId:    args.req.GetReq().GetAuth().GetDevice().GetDeviceId(),
						AppVersion:  int64(args.req.GetReq().GetAuth().GetDevice().GetAppVersion()),
					},
				}).Return(&biometrics.SyncBiometricIdentifierResponse{
					Status: rpc.StatusOk(),
				}, nil)
				md.biometricsClient.EXPECT().UpdateBiometricsDetails(gomock.Any(), &biometrics.UpdateBiometricsDetailsRequest{
					ActorId:                args.req.GetReq().GetAuth().GetActorId(),
					UpdatedBiometricStatus: biometrics.BiometricStatus_BIOMETRIC_STATUS_VERIFIED,
				}).Return(&biometrics.UpdateBiometricsDetailsResponse{
					Status: rpc.StatusOk(),
				}, nil)
				md.livClient.EXPECT().GetLivenessSummary(gomock.Any(), &livenessPb.GetLivenessSummaryRequest{
					ActorId:      args.req.GetReq().GetAuth().GetActorId(),
					RequestId:    biometricColumnId,
					LivenessFlow: livenessPb.LivenessFlow_DEVICE_BIOMETRIC,
				}).Return(&livenessPb.GetLivenessSummaryResponse{
					Status: rpc.StatusOk(),
					Summary: &livenessPb.LivenessSummary{
						Status:    livenessPb.SummaryStatus_SUMMARY_PASSED,
						CreatedAt: &timestamppb.Timestamp{Seconds: 1609678900},
					}}, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			mockAuthClient := authMock.NewMockAuthClient(ctr)
			authOrchClient := authOrchMock.NewMockOrchestratorClient(ctr)
			biometricsClient := biometricsMock.NewMockBiometricsServiceClient(ctr)
			userClient := userMock.NewMockUsersClient(ctr)
			livClient := livenessMock.NewMockLivenessClient(ctr)
			s := &Service{
				client:          mockAuthClient,
				authOrchClient:  authOrchClient,
				biometricClient: biometricsClient,
				conf:            gconf,
				userClient:      userClient,
				livClient:       livClient,
			}
			tt.setupMocks(tt.args, &mockedDependencies{
				authClient:       mockAuthClient,
				authOrchClient:   authOrchClient,
				biometricsClient: biometricsClient,
				userClient:       userClient,
				livClient:        livClient,
			})
			got, err := s.SyncBiometricIdentifier(context.Background(), tt.args.req)
			if err != nil {
				if !assert.Equal(t, tt.wantErr, err.Error()) {
					t.Errorf("SyncBiometricIdentifier() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("SyncBiometricIdentifier() got = %v, want %v", got, tt.want)
			}
		})
	}
}
