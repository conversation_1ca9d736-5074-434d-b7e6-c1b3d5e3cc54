// nolint: funlen,gocritic
package preapprovedloan

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/rpc/code"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/encoding/protojson"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	vgPb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/cfg"
	pkgColors "github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/frontend/app/apputils"
	"github.com/epifi/be-common/pkg/logger"
	moneyPb "github.com/epifi/be-common/pkg/money"

	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	actorPb "github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	livenessPb "github.com/epifi/gamma/api/auth/liveness"
	consentPb "github.com/epifi/gamma/api/consent"
	employmentPb "github.com/epifi/gamma/api/employment"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	errorsPb "github.com/epifi/gamma/api/frontend/errors"
	"github.com/epifi/gamma/api/frontend/header"
	homePb "github.com/epifi/gamma/api/frontend/home"
	feTxnPb "github.com/epifi/gamma/api/frontend/pay/transaction"
	palFePb "github.com/epifi/gamma/api/frontend/preapprovedloan"
	palFeEnumsPb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	vkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	payPb "github.com/epifi/gamma/api/pay"
	preapprovedloanPbBe "github.com/epifi/gamma/api/preapprovedloan"
	palEnumsPb "github.com/epifi/gamma/api/preapprovedloan/enums"
	securedLoansPb "github.com/epifi/gamma/api/preapprovedloan/secured_loans"
	rpEnachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	savingsPb "github.com/epifi/gamma/api/savings"
	searchPb "github.com/epifi/gamma/api/search"
	"github.com/epifi/gamma/api/search/enums"
	segmentPb "github.com/epifi/gamma/api/segment"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	"github.com/epifi/gamma/api/typesv2/ui"
	userPb "github.com/epifi/gamma/api/user"
	groupPb "github.com/epifi/gamma/api/user/group"
	userLocationPb "github.com/epifi/gamma/api/user/location"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/frontend/config"
	genConf "github.com/epifi/gamma/frontend/config/genconf"
	providers "github.com/epifi/gamma/frontend/preapprovedloan/datacollectors"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/aggregated_deeplinks"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/baseprovider"
	palEvents "github.com/epifi/gamma/frontend/preapprovedloan/events"
	"github.com/epifi/gamma/frontend/preapprovedloan/helper"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/feature/release"
	fepkg "github.com/epifi/gamma/pkg/frontend/msg"
	"github.com/epifi/gamma/pkg/onboarding"
	"github.com/epifi/gamma/pkg/pay/payerrorcode"
	palHelper "github.com/epifi/gamma/preapprovedloan/helper"
)

var _ palFePb.PreApprovedLoanServer = &Service{}

var mandatoryFieldsForSalaried = []palFePb.DetailsFormField_DetailFormFieldInfoType{
	palFePb.DetailsFormField_OCCUPATION_NAME,
	palFePb.DetailsFormField_WORK_EMAIL_ADDRESS,
	palFePb.DetailsFormField_MONTHLY_INCOME,
}

var mandatoryFormFieldsForBusiness = []palFePb.DetailsFormField_DetailFormFieldInfoType{
	palFePb.DetailsFormField_OCCUPATION_NAME,
	palFePb.DetailsFormField_MONTHLY_INCOME,
}

var mandatoryFormFieldsForSelfEmployed = []palFePb.DetailsFormField_DetailFormFieldInfoType{
	palFePb.DetailsFormField_OCCUPATION_NAME,
	palFePb.DetailsFormField_MONTHLY_INCOME,
}

var mandatoryFormFieldsForOthers = []palFePb.DetailsFormField_DetailFormFieldInfoType{
	palFePb.DetailsFormField_MONTHLY_INCOME,
}

const perMonthFontColor string = "#8D8D8D"

type Service struct {
	// UnimplementedPreApprovedLoanServer is embedded to have forward compatible implementations.
	palFePb.UnimplementedPreApprovedLoanServer
	preApprovedLoanClient      preapprovedloanPbBe.PreApprovedLoanClient
	consentClient              consentPb.ConsentClient
	vkycClient                 vkycPb.VKYCClient
	savingsClient              savingsPb.SavingsClient
	actorClient                actorPb.ActorClient
	userGroupClient            groupPb.GroupClient
	eventBroker                events.Broker
	conf                       *config.Config
	deeplinkFactory            deeplinks.IDeeplinkProviderFactory
	usersClient                userPb.UsersClient
	employmentClient           employmentPb.EmploymentClient
	genconf                    *genConf.Config
	onbClient                  onbPb.OnboardingClient
	segmentClient              segmentPb.SegmentationServiceClient
	releaseEvaluator           *release.Evaluator
	accountBalanceClient       accountBalancePb.BalanceClient
	authClient                 authPb.AuthClient
	securedLoanClient          securedLoansPb.SecuredLoansClient
	rpcHelper                  helper.IRpcHelper
	aggregatedDeeplinkProvider *aggregated_deeplinks.AggregatedDeeplinkProvider
	searchClient               searchPb.ActionBarClient
	userLocationClient         userLocationPb.LocationClient
	payClient                  payPb.PayClient
	recordConsent              providers.RecordConsent
}

func NewService(
	preApprovedLoanClient preapprovedloanPbBe.PreApprovedLoanClient,
	consentClient consentPb.ConsentClient,
	vkycClient vkycPb.VKYCClient,
	savingsClient savingsPb.SavingsClient,
	actorClient actorPb.ActorClient,
	eventBroker events.Broker,
	conf *config.Config,
	deeplinkFactory deeplinks.IDeeplinkProviderFactory,
	usersClient userPb.UsersClient,
	employmentClient employmentPb.EmploymentClient,
	genconf *genConf.Config,
	onbClient onbPb.OnboardingClient,
	segmentClient segmentPb.SegmentationServiceClient,
	releaseEvaluator *release.Evaluator,
	accountBalanceClient accountBalancePb.BalanceClient,
	userGroupClient groupPb.GroupClient,
	authClient authPb.AuthClient,
	securedLoanClient securedLoansPb.SecuredLoansClient,
	rpcHelper helper.IRpcHelper,
	aggregatedDeeplinkProvider *aggregated_deeplinks.AggregatedDeeplinkProvider,
	searchClient searchPb.ActionBarClient,
	userLocationClient userLocationPb.LocationClient,
	payClient payPb.PayClient,
	recordConsent providers.RecordConsent,
) *Service {
	return &Service{
		preApprovedLoanClient:      preApprovedLoanClient,
		consentClient:              consentClient,
		eventBroker:                eventBroker,
		conf:                       conf,
		vkycClient:                 vkycClient,
		savingsClient:              savingsClient,
		actorClient:                actorClient,
		deeplinkFactory:            deeplinkFactory,
		usersClient:                usersClient,
		employmentClient:           employmentClient,
		genconf:                    genconf,
		onbClient:                  onbClient,
		segmentClient:              segmentClient,
		releaseEvaluator:           releaseEvaluator,
		accountBalanceClient:       accountBalanceClient,
		userGroupClient:            userGroupClient,
		authClient:                 authClient,
		securedLoanClient:          securedLoanClient,
		rpcHelper:                  rpcHelper,
		aggregatedDeeplinkProvider: aggregatedDeeplinkProvider,
		searchClient:               searchClient,
		userLocationClient:         userLocationClient,
		payClient:                  payClient,
		recordConsent:              recordConsent,
	}
}

func (s *Service) getDeeplinkProvider(ctx context.Context, vendor palFeEnumsPb.Vendor, loanProgram palFeEnumsPb.LoanProgram) provider.IDeeplinkProvider {
	provider, _ := s.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplinks.GetDeeplinkProviderRequest{
		Vendor:      vendor,
		LoanProgram: loanProgram,
	})
	return provider
}

func isLamfDownTimeApplicableForUser(actorId string) bool {
	// excluding pulkit's actor id from downtime so that we can test vendor's new environment once migration is completed.
	return actorId != "AC2109222f3TkKL6Riu1rFPslDCQcQ=="
}

func isSecuredLoanHeader(header *palFeEnumsPb.LoanHeader) bool {
	return header.GetLoanType() == palFeEnumsPb.LoanType_LOAN_TYPE_SECURED_LOAN || palHelper.GetLoanTypeFromLoanProgram(helper.GetBeLoanProgramFromFe(header.GetLoanProgram())) == preapprovedloanPbBe.LoanType_LOAN_TYPE_SECURED_LOAN
}

// nolint: funlen
func (s *Service) GetLandingInfo(ctx context.Context, req *palFePb.GetLandingInfoRequest) (*palFePb.GetLandingInfoResponse, error) {
	helper.LogIfLoanHeaderNotPresent(ctx, req.GetLoanHeader(), "GetLandingInfo")
	s.setLoanHeaderIfEmpty(ctx, req)

	res := &palFePb.GetLandingInfoResponse{
		RespHeader: &header.ResponseHeader{},
	}

	appVersionConstraintData := release.NewAppVersionConstraintData(s.genconf.Lending().ForceUpgradeFeatureConfig())
	isAppVersionGreater, appVerErr := release.NewAppVersionConstraint().Evaluate(ctx, appVersionConstraintData, nil)
	if appVerErr != nil {
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}

	deeplinkProvider := s.getDeeplinkProvider(ctx, req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())
	platform, _ := epificontext.AppPlatformAndVersion(ctx)
	if !isAppVersionGreater {
		dl := deeplinkProvider.GetForceUpgradeLandingResponse(platform, "", "Please update your app to latest version to access loans", req.GetLoanHeader())
		res.Deeplink = dl
		res.RespHeader.Status = rpcPb.StatusOk()
		return res, nil
	}

	if apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.genconf.Lending().UseLandingDataV3()) && req.GetLoanType() != palFeEnumsPb.LoanType_LOAN_TYPE_SECURED_LOAN && !isSecuredLoanHeader(req.GetLoanHeader()) {
		return s.getLandingInfoV3(ctx, req)
	}

	startTime := time.Now()

	var landingInfoFilter []preapprovedloanPbBe.LandingInfoFilter
	for _, filter := range req.GetLandingInfoFilters() {
		mp, ok := preapprovedloanPbBe.LandingInfoFilter_value[filter]
		if !ok {
			logger.Error(ctx, "incorrect landinginfo filter provided", zap.String("filter", filter))
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
		landingInfoFilter = append(landingInfoFilter, preapprovedloanPbBe.LandingInfoFilter(mp))
	}

	s.PushApiTriggeredEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameLandingInfo, "", "", palEvents.LoanManagementFlow,
		getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())

	var beResStatusCode uint32
	var loanOffer *preapprovedloanPbBe.LoanOffer
	var loanOffers []*preapprovedloanPbBe.LoanOffer
	var loanOptions []*preapprovedloanPbBe.LoanOption
	var activeLoanRequest *preapprovedloanPbBe.LoanRequest
	var rejectedLoanOffers []*preapprovedloanPbBe.LoanHeader
	if req.GetLoanType() == palFeEnumsPb.LoanType_LOAN_TYPE_SECURED_LOAN || req.GetLoanHeader().GetLoanType() == palFeEnumsPb.LoanType_LOAN_TYPE_SECURED_LOAN {
		isDown := s.isDownTime(ctx, s.genconf.Lending().SecuredLoanParams().DowntimeConfig())
		if isDown && isLamfDownTimeApplicableForUser(req.GetReq().GetAuth().GetActorId()) {
			logger.Info(ctx, "lamf downtime is active right now")
			res.RespHeader.Status = rpcPb.StatusOk()
			res.Deeplink = baseprovider.GetLamfDowntimeScreenDeepLink()
			return res, nil
		}
		isEnabled, err := s.isActorEligibleForSecuredLoans(ctx, req.GetReq().GetAuth().GetActorId())
		if err != nil || !isEnabled {
			logger.Error(ctx, "feature eligibility check failed for secured loans", zap.Error(err))
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
	}

	// This is empty when loan program is not early salary.
	var earlySalaryDetails = &preapprovedloanPbBe.GetLandingInfoResponse_EarlySalaryDetails{}
	if helper.GetBeLoanProgramFromFe(req.GetLoanHeader().GetLoanProgram()) == preapprovedloanPbBe.LoanProgram_LOAN_PROGRAM_EARLY_SALARY {
		beRes, beErr := s.preApprovedLoanClient.GetLandingInfo(ctx, &preapprovedloanPbBe.GetLandingInfoRequest{
			ActorId:    req.GetReq().GetAuth().GetActorId(),
			LoanHeader: helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
		})
		if beErr != nil {
			logger.Error(ctx, "preApprovedLoanClient.GetLandingInfo BE API failed", zap.Error(beErr))
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
		beResStatusCode = beRes.GetStatus().GetCode()
		activeLoanAccount := beRes.GetActiveLoanAccount()
		loanOffer = beRes.GetLoanOffer()
		activeLoanRequest = beRes.GetActiveLoanRequest()
		earlySalaryDetails = beRes.GetEarlySalaryDetails()

		// set the loan program and vendor from the BE rpc response, client will make subsequent rpc calls with this loan header
		if req.GetLoanHeader().GetVendor() == palFeEnumsPb.Vendor_VENDOR_UNSPECIFIED {
			req.GetLoanHeader().Vendor = helper.GetPalFeVendorFromBe(getVendorFromBeTables(activeLoanRequest, loanOffer, activeLoanAccount, nil, nil))
		}
		if helper.GetFeLoanProgramFromBe(getLpFromBeTables(activeLoanRequest, loanOffer, nil, nil)) !=
			palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
			req.GetLoanHeader().LoanProgram = helper.GetFeLoanProgramFromBe(getLpFromBeTables(activeLoanRequest, loanOffer, nil, nil))
		}
	} else {
		beRes, beErr := s.preApprovedLoanClient.GetLandingInfoV2(ctx, &preapprovedloanPbBe.GetLandingInfoV2Request{
			ActorId:            req.GetReq().GetAuth().GetActorId(),
			LoanHeader:         helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
			LoanType:           helper.GetBeLoanTypeFromFe(req.GetLoanType()),
			LandingInfoFilters: landingInfoFilter,
		})
		if beErr != nil {
			logger.Error(ctx, "preApprovedLoanClient.GetLandingInfoV2 BE API failed", zap.Error(beErr))
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}

		req.LoanHeader = helper.GetFeLoanHeaderByBeLoanHeader(beRes.GetLoanHeader())
		// for acq to lend cases, in this request from client will contain loan header as follows
		// loan program = ACQ_TO_LEND, vendor = UNSPECIFIED
		callAltLanding, dl, err := s.callAlternateLandingRpc(ctx, req.GetLoanHeader(), beRes, req.GetReq().GetAuth().GetActorId(), startTime, loanOffer.GetId(), activeLoanRequest.GetId(), req.GetLoanHeader().GetEventData())
		if callAltLanding {
			if err != nil {
				logger.Error(ctx, "error in getting deeplink from AlternateLandingRpc", zap.Error(err))
				res.GetRespHeader().Status = rpcPb.StatusInternal()
				res.GetRespHeader().ErrorView = defaultErrorView
				return res, nil
			}

			platform, version := epificontext.AppPlatformAndVersion(ctx)
			ownership := getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())
			// TODO(@Shivansh) check if actor id is already present in the context from the interceptor
			ctx = epificontext.CtxWithActorId(ctx, req.GetReq().GetAuth().GetActorId())
			deeplinkProvider := s.getDeeplinkProvider(ctx, req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())

			res.GetRespHeader().Status = rpcPb.StatusOk()
			res.Deeplink = dl
			if s.isUpdateNeeded(ctx, platform, version, helper.GetBeVendorFromFe(req.GetLoanHeader().GetVendor()), helper.GetBeLoanProgramFromFe(req.GetLoanHeader().GetLoanProgram()), req.GetReq().GetAuth().GetActorId()) {
				s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameLandingInfo, time.Since(startTime), palEvents.StatusSuccess, palEvents.SubStatusUpdateForLoanEligibility, loanOffer.GetId(), activeLoanRequest.GetId(), palEvents.LoanEligibilityFlow, ownership, req.GetLoanHeader().GetEventData())
				res.Deeplink = deeplinkProvider.GetForceUpgradeLandingResponse(platform, loanOffer.GetId(), "", nil)
			}
			return res, nil
		}

		beResStatusCode = beRes.GetStatus().GetCode()
		loanOptions = beRes.GetLoanOptions()
		loanOffer = beRes.GetLoanOffer()
		loanOffers = beRes.GetLoanOffers()
		activeLoanRequest = beRes.GetActiveLoanRequest()
		rejectedLoanOffers = beRes.GetRejectedLoanOffers()
		// TODO(Sundeep): Currently this is a hacky way to handle separate landing status code & RPC status code.
		// Once we remove GetLandingInfo & completely move to GetLandingInfoV2, we can do the appropriate handling
		// based on RPC status code & landing status code in the following statements.
		if beResStatusCode == 0 {
			beResStatusCode = beRes.GetLandingStatus().GetCode()
		}
	}
	platform, version := epificontext.AppPlatformAndVersion(ctx)
	ownership := getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())
	ctx = epificontext.CtxWithActorId(ctx, req.GetReq().GetAuth().GetActorId())
	deeplinkProvider = s.getDeeplinkProvider(ctx, req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())
	var err error

	switch beResStatusCode {
	case uint32(preapprovedloanPbBe.GetLandingInfoResponse_LOAN_DETAILS),
		uint32(preapprovedloanPbBe.GetLandingInfoResponse_LOAN_APPLICATIONS):
		res.RespHeader.Status = rpcPb.StatusOk()
		var dl *deeplinkPb.Deeplink
		var dlErr error
		isDlSupported, dlSupportedErr := isNextActionDeeplinkSupported(ctx, activeLoanRequest.GetNextAction(), s.genconf.Lending())
		if dlSupportedErr != nil {
			logger.Error(ctx, "error in checking if deeplink is supported", zap.Error(dlSupportedErr), zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()),
				zap.String(logger.LOAN_REQUEST_ID, activeLoanRequest.GetId()))
			res.GetRespHeader().Status = rpcPb.StatusInternal()
			res.GetRespHeader().ErrorView = defaultErrorView
			return res, nil
		}
		if !isDlSupported {
			dl = deeplinkProvider.GetForceUpgradeLandingResponse(platform, "", "Please update your app to the latest version to continue your application", req.GetLoanHeader())
			res.Deeplink = dl
			return res, nil
		}
		if s.isPostDisbursalFlowEnabled(ctx, req.GetReq().GetAuth().GetActorId(), req.GetLoanHeader()) {
			s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameLandingInfo, time.Since(startTime), palEvents.StatusSuccess, palEvents.SubStatusLoanManagement, loanOffer.GetId(), activeLoanRequest.GetId(), palEvents.LoanManagementFlowV2, ownership, req.GetLoanHeader().GetEventData())
			dl = deeplinkV3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LOANS_DASHBOARD_SCREEN, &preapprovedloans.LoansDashboardScreenOptions{
				LoanHeader: deeplinkProvider.GetLoanHeader(),
			})
		} else {
			s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameLandingInfo, time.Since(startTime), palEvents.StatusSuccess, palEvents.SubStatusLoanManagement, loanOffer.GetId(), activeLoanRequest.GetId(), palEvents.LoanManagementFlow, ownership, req.GetLoanHeader().GetEventData())
			dl, dlErr = deeplinkProvider.GetLoanDashboardScreenDeepLink(ctx, deeplinkProvider.GetLoanHeader(), req.GetReq().GetAuth().GetActorId())
			if dlErr != nil {
				logger.Error(ctx, "failed to get loans dashboard deeplink", zap.Error(dlErr), zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()),
					zap.String(logger.LOAN_REQUEST_ID, activeLoanRequest.GetId()))
				res.GetRespHeader().Status = rpcPb.StatusInternal()
				res.GetRespHeader().ErrorView = defaultErrorView
				return res, nil
			}
		}
		res.Deeplink = dl
	case uint32(preapprovedloanPbBe.GetLandingInfoResponse_LOAN_OFFER):
		// check if loan application flow is enabled for the user for the given lender, program and version
		// this is useful in controlled rollout of the flow even after ingesting the offers
		if !s.isApplicationFlowEnabled(ctx, req.GetReq().GetAuth().GetActorId(), req.GetLoanHeader(), loanOffer.GetProcessingInfo().GetLoanProgramVersion()) {
			res.RespHeader.Status = rpcPb.StatusOk()
			res.Deeplink = s.getNoOfferDeeplink(ctx, deeplinkProvider, loanOffer, earlySalaryDetails, req.GetReq().GetAuth().GetActorId())
			return res, nil
		}
		if s.isUpdateNeeded(ctx, platform, version, loanOffer.GetVendor(), helper.GetBeLoanProgramFromFe(req.GetLoanHeader().GetLoanProgram()), req.GetReq().GetAuth().GetActorId()) {
			s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameLandingInfo, time.Since(startTime), palEvents.StatusSuccess, palEvents.SubStatusUpdateForLoanOffer, loanOffer.GetId(), activeLoanRequest.GetId(), palEvents.LoanManagementFlow, ownership, req.GetLoanHeader().GetEventData())
			res.RespHeader.Status = rpcPb.StatusOk()
			res.Deeplink = deeplinkProvider.GetForceUpgradeLandingResponse(platform, loanOffer.GetId(), "", nil)
			return res, nil
		}
		if loanOffer.GetVendor() == preapprovedloanPbBe.Vendor_FEDERAL && s.isDownTime(ctx, s.conf.Lending.PreApprovedLoan.Downtime) {
			res.RespHeader.Status = rpcPb.StatusOk()
			res.Deeplink = baseprovider.GetDowntimeScreenDeepLink(ctx)
			return res, nil
		}

		s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameLandingInfo, time.Since(startTime), palEvents.StatusSuccess, palEvents.SubStatusLoanOffer, loanOffer.GetId(), activeLoanRequest.GetId(), palEvents.LoanManagementFlow, ownership, req.GetLoanHeader().GetEventData())
		res.RespHeader.Status = rpcPb.StatusOk()
		var getDowntimeStatusRes *preapprovedloanPbBe.GetDowntimeStatusResponse
		getDowntimeStatusRes, err = s.preApprovedLoanClient.GetDowntimeStatus(
			ctx,
			&preapprovedloanPbBe.GetDowntimeStatusRequest{
				LoanHeader: helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
			},
		)
		if err = epifigrpc.RPCError(getDowntimeStatusRes, err); err != nil {
			logger.Error(ctx, "error while getting downtime status", zap.Error(err))
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}

		isMultiOfferEnabled, err := s.IsOfferDetailsV4FlowEnabled(ctx, deeplinkProvider.GetLoanHeader())
		if err != nil {
			logger.Error(ctx, "error while computing if multi offer enabled", zap.Error(err))
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}

		var errLoanOffer error
		if isMultiOfferEnabled {
			res.Deeplink, errLoanOffer = s.aggregatedDeeplinkProvider.GetLoanOfferIntroScreen(ctx, &provider.LandingInfoRequest{
				LoanOffer:              loanOffer,
				EarlySalaryDetails:     earlySalaryDetails,
				DowntimeStatusResponse: getDowntimeStatusRes,
				LoanOffers:             loanOffers,
				LoanOptions:            loanOptions,
				ActorId:                req.GetReq().GetAuth().GetActorId(),
				RejectedLoanOffers:     rejectedLoanOffers,
			})
			if errLoanOffer != nil {
				logger.Error(ctx, "error while generating loans offer intro screen deeplink", zap.Error(errLoanOffer))
			}
			res.GetRespHeader().FeedbackEngineInfo = &header.FeedbackEngineInfo{
				FlowIdDetails: &header.FlowIdentifierDetails{
					FlowIdentifierType: types.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_DROP_OFF,
					FlowIdentifier:     types.FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_OFFER_INTRO_SCREEN.String(),
				},
			}
		} else {
			res.Deeplink, errLoanOffer = deeplinkProvider.GetLoanLandingScreenDeepLink(ctx, deeplinkProvider.GetLoanHeader(), &provider.LandingInfoRequest{
				LoanOffer:              loanOffer,
				EarlySalaryDetails:     earlySalaryDetails,
				DowntimeStatusResponse: getDowntimeStatusRes,
				LoanOffers:             loanOffers,
				ActorId:                req.GetReq().GetAuth().GetActorId(),
			})
			if errLoanOffer != nil {
				logger.Error(ctx, "error while generating loans landing page deeplink", zap.Error(errLoanOffer))
			}
			res.GetRespHeader().FeedbackEngineInfo = &header.FeedbackEngineInfo{
				FlowIdDetails: &header.FlowIdentifierDetails{
					FlowIdentifierType: types.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_DROP_OFF,
					FlowIdentifier:     types.FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_LANDING_INFO_V2_LENDER_OFFER_SCREEN.String(),
				},
			}
		}
		if errLoanOffer != nil {
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
		if s.skipLandingScreen(ctx, req.GetLoanHeader()) {
			res.Deeplink = &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_OFFER_DETAILS_SCREEN,
				ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanOfferDetailsScreenOptions{
					PreApprovedLoanOfferDetailsScreenOptions: &deeplinkPb.PreApprovedLoanOfferDetailsScreenOptions{
						OfferId:    loanOffer.GetId(),
						LoanHeader: req.GetLoanHeader(),
					},
				},
			}
		}
	case uint32(preapprovedloanPbBe.GetLandingInfoV2Response_LANDING_STATUS_CHECK_LOAN_ELIGIBILITY):
		if s.isUpdateNeeded(ctx, platform, version, helper.GetBeVendorFromFe(req.GetLoanHeader().GetVendor()), helper.GetBeLoanProgramFromFe(req.GetLoanHeader().GetLoanProgram()), req.GetReq().GetAuth().GetActorId()) {
			s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameLandingInfo, time.Since(startTime), palEvents.StatusSuccess, palEvents.SubStatusUpdateForLoanEligibility, loanOffer.GetId(), activeLoanRequest.GetId(), palEvents.LoanEligibilityFlow, ownership, req.GetLoanHeader().GetEventData())
			res.RespHeader.Status = rpcPb.StatusOk()
			res.Deeplink = deeplinkProvider.GetForceUpgradeLandingResponse(platform, loanOffer.GetId(), "", nil)
			return res, nil
		}
		s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameLandingInfo, time.Since(startTime), palEvents.StatusSuccess, palEvents.SubStatusLoanEligibility, loanOffer.GetId(), activeLoanRequest.GetId(), palEvents.LoanEligibilityFlow, ownership, req.GetLoanHeader().GetEventData())
		res.RespHeader.Status = rpcPb.StatusOk()
		var dl *deeplinkPb.Deeplink

		isMultiOfferEnabled, mErr := s.IsOfferDetailsV4FlowEnabled(ctx, deeplinkProvider.GetLoanHeader())
		if mErr != nil {
			logger.Error(ctx, "error while computing if multi offer enabled", zap.Error(mErr))
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}

		appVersionConstraintData := release.NewAppVersionConstraintData(s.genconf.Lending().PreApprovedLoan().ShowEligibilityInMultiOfferConfig())
		isAppVersionCompatible, appVerErr := release.NewAppVersionConstraint().Evaluate(ctx, appVersionConstraintData, nil)
		if appVerErr != nil {
			logger.Error(ctx, "Error evaluating app version constraint", zap.Error(appVerErr))
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
		userFeatProp, a2lErr := palHelper.GetUserFeatureProperty(ctx, req.GetReq().GetAuth().GetActorId(), s.onbClient, s.savingsClient)
		if a2lErr != nil {
			logger.Error(ctx, "error in checking if user is a2l", zap.Error(a2lErr))
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
		if isMultiOfferEnabled && isAppVersionCompatible && userFeatProp.IsHomeAccessible {
			dl, err = s.aggregatedDeeplinkProvider.GetLoanOfferIntroScreen(ctx, &provider.LandingInfoRequest{
				LoanOffer:          loanOffer,
				EarlySalaryDetails: earlySalaryDetails,
				LoanOffers:         loanOffers,
				LoanOptions:        loanOptions,
				ActorId:            req.GetReq().GetAuth().GetActorId(),
				RejectedLoanOffers: rejectedLoanOffers,
			})
			if err != nil {
				logger.Error(ctx, "error while generating loans offer intro screen deeplink", zap.Error(err))
				res.RespHeader.Status = rpcPb.StatusInternal()
				res.RespHeader.ErrorView = defaultErrorView
				return res, nil
			}
			res.GetRespHeader().FeedbackEngineInfo = &header.FeedbackEngineInfo{
				FlowIdDetails: &header.FlowIdentifierDetails{
					FlowIdentifierType: types.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_DROP_OFF,
					FlowIdentifier:     types.FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_OFFER_INTRO_SCREEN.String(),
				},
			}
		} else {
			dl, err = deeplinkProvider.CheckLoanEligibilityScreenDeeplink(ctx, req.GetReq().GetAuth().GetActorId(), req.GetLoanHeader())
			if err != nil {
				logger.Error(ctx, "error while generating loan eligibility screen deeplink", zap.Error(err))
				res.RespHeader.Status = rpcPb.StatusInternal()
				res.RespHeader.ErrorView = defaultErrorView
				return res, nil
			}
		}
		res.Deeplink = dl
	case rpcPb.StatusPermissionDenied().GetCode(),
		uint32(preapprovedloanPbBe.GetLandingInfoResponse_NON_SALARY_USER):
		s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameLandingInfo, time.Since(startTime), palEvents.StatusSuccess, palEvents.SubStatusPermissionDenied, loanOffer.GetId(), activeLoanRequest.GetId(), palEvents.LoanManagementFlow, ownership, req.GetLoanHeader().GetEventData())
		res.RespHeader.Status = rpcPb.StatusOk()
		res.Deeplink = s.getNoOfferDeeplink(ctx, deeplinkProvider, loanOffer, earlySalaryDetails, req.GetReq().GetAuth().GetActorId())
	default:
		logger.Error(ctx, fmt.Sprintf("unhandled be status: %v", beResStatusCode))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
	}
	return res, nil
}

func (s *Service) getNoOfferDeeplink(ctx context.Context, deeplinkProvider provider.IDeeplinkProvider, loanOffer *preapprovedloanPbBe.LoanOffer, esDetails *preapprovedloanPbBe.GetLandingInfoResponse_EarlySalaryDetails, actorId string) *deeplinkPb.Deeplink {
	if s.showNoOfferLandingScreenV2(ctx) {
		faqDl := deeplinkProvider.GetKnowMoreScreenDeepLink(ctx, deeplinkProvider.GetLoanHeader(), palFeEnumsPb.FaqTopic_FAQ_TOPIC_UNSPECIFIED)
		return deeplinkProvider.GetNoOfferLandingScreenDeepLinkV2(ctx, deeplinkProvider.GetLoanHeader(), &provider.NoOfferLandingInfoRequest{
			LoanOffer:          loanOffer,
			EarlySalaryDetails: esDetails,
			ActorId:            actorId,
		}, faqDl)

	}
	return deeplinkProvider.GetNoOfferLandingScreenDeepLink(ctx, deeplinkProvider.GetLoanHeader(), &provider.NoOfferLandingInfoRequest{
		LoanOffer:          loanOffer,
		EarlySalaryDetails: esDetails,
		ActorId:            actorId,
	})
}

// nolint: funlen
func (s *Service) GetOfferDetails(ctx context.Context, req *palFePb.GetOfferDetailsRequest) (*palFePb.GetOfferDetailsResponse, error) {
	helper.LogIfLoanHeaderNotPresent(ctx, req.GetLoanHeader(), "GetOfferDetails")
	res := &palFePb.GetOfferDetailsResponse{
		RespHeader: &header.ResponseHeader{},
	}
	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}
	if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		req.GetLoanHeader().LoanProgram = palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram)
	}
	startTime := time.Now()
	s.PushApiTriggeredEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGetLoanOffer,
		req.GetOfferId(), "", palEvents.LoanApplicationFlow, getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())

	beRes, err := s.preApprovedLoanClient.GetOfferDetails(ctx, &preapprovedloanPbBe.GetOfferDetailsRequest{
		ActorId:        req.GetReq().GetAuth().GetActorId(),
		OfferId:        req.GetOfferId(),
		LoanAmount:     req.GetUserInput().GetLoanAmount().GetBeMoney(),
		TenureInMonths: req.GetUserInput().GetTenureInMonths(),
		LoanHeader:     helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
		PledgeDetails:  getBeMfPledgeDetails(req.GetUserInput().GetMutualFunds()),
	})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		if beRes.GetStatus().IsPermissionDenied() || beRes.GetStatus().IsRecordNotFound() {
			deeplinkProvider := s.getDeeplinkProvider(ctx, req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())
			logger.Error(ctx, "preApprovedLoanClient.GetOfferDetails BE API permission denied", zap.Error(err))
			res.RespHeader.Status = rpcPb.StatusOk()
			res.Deeplink = deeplinkProvider.GetNoOfferLandingScreenDeepLink(ctx, deeplinkProvider.GetLoanHeader(), &provider.NoOfferLandingInfoRequest{
				ActorId: req.GetReq().GetAuth().GetActorId(),
			})
			return res, nil
		}
		logger.Error(ctx, "preApprovedLoanClient.GetOfferDetails BE API failed", zap.Error(err))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}
	ownership := getEventsOwnership(helper.GetPalFeVendorFromBe(beRes.GetVendor()), req.GetLoanHeader().GetLoanProgram())
	s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGetLoanOffer, time.Since(startTime),
		palEvents.StatusSuccess, "", req.GetOfferId(), "", palEvents.LoanApplicationFlow, ownership, req.GetLoanHeader().GetEventData())
	res.RespHeader.Status = rpcPb.StatusOk()
	if req.GetLoanHeader().GetVendor() == palFeEnumsPb.Vendor_VENDOR_UNSPECIFIED {
		req.GetLoanHeader().Vendor = helper.GetPalFeVendorFromBe(beRes.GetVendor())
	}
	deeplinkProvider := s.getDeeplinkProvider(ctx, helper.GetPalFeVendorFromBe(beRes.GetVendor()), req.GetLoanHeader().GetLoanProgram())
	var dl *deeplinkPb.Deeplink
	var getDlErr error
	if req.GetFlow() == palFeEnumsPb.LoanParamsSelectionFlow_LOAN_PARAMS_SELECTION_FLOW_ACTIVE_APPLICATION_REVISED_OFFER {
		dl, getDlErr = deeplinkProvider.GetRevisedLoanOfferDetailsScreenDeepLink(ctx, deeplinkProvider.GetLoanHeader(), beRes, req)
		if getDlErr != nil {
			logger.Error(ctx, "error while generating revised offer details screen deeplink", zap.Error(getDlErr))
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
	} else {
		dl, getDlErr = deeplinkProvider.GetLoanOfferDetailsScreenDeepLink(ctx, req.GetReq().GetAuth().GetActorId(), deeplinkProvider.GetLoanHeader(), beRes, req)
		if getDlErr != nil {
			logger.Error(ctx, "error while generating revised offer details screen deeplink", zap.Error(getDlErr), zap.Any(logger.LOAN_HEADER, req.GetLoanHeader()))
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
	}
	res.Deeplink = dl

	s.triggerOfferViewedCallbackIfRequired(ctx, req.GetReq().GetAuth().GetActorId(), beRes.GetOfferId(), req.GetLoanHeader())

	s.populateLODropOffFeedbackIdentifierInRespHeaderIfEnabled(res.GetRespHeader())

	return res, nil
}

func (s *Service) triggerOfferViewedCallbackIfRequired(ctx context.Context, actorId, offerId string, lh *palFeEnumsPb.LoanHeader) {
	if !isOfferViewedCallbackRequired(lh) {
		return
	}
	callbackRes, err := s.preApprovedLoanClient.ClientCallback(ctx, &preapprovedloanPbBe.ClientCallbackRequest{
		ActorId:    actorId,
		Type:       preapprovedloanPbBe.ClientCallbackRequest_OFFER_VIEWED,
		LoanHeader: helper.GetBeLoanHeaderByFeLoanHeader(lh),
		Identifier: &preapprovedloanPbBe.ClientCallbackRequest_LoanOfferId{
			LoanOfferId: offerId,
		},
	})
	if err = epifigrpc.RPCError(callbackRes, err); err != nil {
		logger.Error(ctx, "error in ClientCallback BE rpc", zap.Error(err))
	}
}

func isOfferViewedCallbackRequired(lh *palFeEnumsPb.LoanHeader) bool {
	// the offer viewed callback information is used to check if the user dropped off and present a new offer with reduced pricing as an experiment
	// this experiment is being done only for realtime subvention program as of now
	if lh.GetVendor() == palFeEnumsPb.Vendor_LIQUILOANS && lh.GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION {
		return true
	}
	return false
}

// nolint: funlen
func (s *Service) GetApplicationDetails(ctx context.Context, req *palFePb.GetApplicationDetailsRequest) (*palFePb.GetApplicationDetailsResponse, error) {
	helper.LogIfLoanHeaderNotPresent(ctx, req.GetLoanHeader(), "GetApplicationDetails")
	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}
	if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		req.GetLoanHeader().LoanProgram = palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram)
	}
	startTime := time.Now()
	s.PushApiTriggeredEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameApplicationDetails,
		req.GetOfferId(), "", palEvents.LoanApplicationFlow, getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())
	res := &palFePb.GetApplicationDetailsResponse{
		RespHeader: &header.ResponseHeader{},
	}

	beRes, err := s.preApprovedLoanClient.GetOfferDetails(ctx, &preapprovedloanPbBe.GetOfferDetailsRequest{
		ActorId:            req.GetReq().GetAuth().GetActorId(),
		OfferId:            req.GetOfferId(),
		LoanAmount:         req.GetLoanAmount().GetBeMoney(),
		TenureInMonths:     req.GetTenureInMonths(),
		PledgeDetails:      getBeMfPledgeDetails(req.GetMutualFunds()),
		LoanHeader:         helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
		CalculatorAccuracy: palEnumsPb.CalculatorAccuracy_CALCULATOR_ACCURACY_ACCURATE,
	})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		logger.Error(ctx, "preApprovedLoanClient.GetOfferDetails BE API failed", zap.Error(err))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = bottomSheetErrorView
		return res, nil
	}
	deeplinkProvider := s.getDeeplinkProvider(ctx, helper.GetPalFeVendorFromBe(beRes.GetVendor()), req.GetLoanHeader().GetLoanProgram())
	res.RespHeader.Status = rpcPb.StatusOk()
	var dl *deeplinkPb.Deeplink
	var getDlErr error
	if req.GetFlow() == palFeEnumsPb.LoanParamsSelectionFlow_LOAN_PARAMS_SELECTION_FLOW_ACTIVE_APPLICATION_REVISED_OFFER {
		dl, getDlErr = deeplinkProvider.GetRevisedLoanOfferApplicationDetailsScreenDeepLink(ctx, deeplinkProvider.GetLoanHeader(), beRes, req)
		if getDlErr != nil {
			logger.Error(ctx, "error in GetRevisedLoanOfferApplicationDetailsScreenDeepLink", zap.Error(err))
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = bottomSheetErrorView
			return res, nil
		}
	} else {
		dl, getDlErr = deeplinkProvider.GetLoanApplicationDetailsScreenDeepLink(ctx, req.GetReq().GetAuth().GetActorId(), deeplinkProvider.GetLoanHeader(), beRes)
		if getDlErr != nil {
			logger.Error(ctx, "error in GetLoanApplicationDetailsScreenDeepLink", zap.Error(getDlErr), zap.Any(logger.LOAN_HEADER, req.GetLoanHeader()))
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = bottomSheetErrorView
			return res, nil
		}
	}
	res.Deeplink = dl
	s.triggerOfferViewedCallbackIfRequired(ctx, req.GetReq().GetAuth().GetActorId(), beRes.GetOfferId(), req.GetLoanHeader())

	ownership := getEventsOwnership(helper.GetPalFeVendorFromBe(beRes.GetVendor()), req.GetLoanHeader().GetLoanProgram())
	s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameApplicationDetails, time.Since(startTime),
		palEvents.StatusSuccess, "", req.GetOfferId(), "", palEvents.LoanApplicationFlow, ownership, req.GetLoanHeader().GetEventData())

	s.populateLODropOffFeedbackIdentifierInRespHeaderIfEnabled(res.GetRespHeader())

	return res, nil
}

func (s *Service) GetApplicationStatus(ctx context.Context, req *palFePb.GetApplicationStatusRequest) (*palFePb.GetApplicationStatusResponse, error) {
	helper.LogIfLoanHeaderNotPresent(ctx, req.GetLoanHeader(), "GetApplicationStatus")
	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}
	if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		req.GetLoanHeader().LoanProgram = palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram)
	}

	// Todo(Anupam): Adding hack for payment requests orch, that it will take the user out of the webview whenever call comes.
	// Need to add a support for sync proxy here to get the actual poll here
	if req.GetLoanHeader().GetVendor() == palFeEnumsPb.Vendor_LENDEN && strings.HasPrefix(req.GetLoanRequestId(), "PALPR") {
		res := &palFePb.GetApplicationStatusResponse{
			LoanRequestId: req.GetLoanRequestId(),
			Deeplink: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
				ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanLandingScreenOptions{
					PreApprovedLoanLandingScreenOptions: &deeplinkPb.PreApprovedLoanLandingScreenOptions{
						LoanHeader: req.GetLoanHeader(),
					},
				},
			},
			RespHeader: &header.ResponseHeader{
				Status: rpcPb.StatusOk(),
			},
		}
		return res, nil
	}

	res := &palFePb.GetApplicationStatusResponse{
		RespHeader: &header.ResponseHeader{},
	}
	if req.GetGetNextActionInSync() {
		beRes, err := s.preApprovedLoanClient.GetApplicationStatusSync(ctx, &preapprovedloanPbBe.GetApplicationStatusSyncRequest{
			LoanRequestId: req.GetLoanRequestId(),
			ActorId:       req.GetReq().GetAuth().GetActorId(),
			LoanHeader:    helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
		})
		if te := epifigrpc.RPCError(beRes, err); te != nil {
			logger.Error(ctx, "preApprovedLoanClient.GetApplicationStatusSync BE API failed",
				zap.Error(te),
			)
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
		res.RespHeader.Status = rpcPb.StatusOk()
		deeplinkProvider := s.getDeeplinkProvider(ctx, req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())
		isDlSupported, err := isNextActionDeeplinkSupported(ctx, beRes.GetNextAction(), s.genconf.Lending())
		if err != nil {
			return nil, errors.Wrap(err, "error in checking if deeplink is supported")
		}
		if !isDlSupported {
			platform, _ := epificontext.AppPlatformAndVersion(ctx)
			res.Deeplink = deeplinkProvider.GetForceUpgradeLandingResponse(platform, "", "Please update your app to the latest version to continue your application", req.GetLoanHeader())
		} else {
			res.Deeplink = beRes.GetNextAction()
		}
		// if nextAction is not received from BE, we are setting it to polling screen on best effort basis.
		if res.GetDeeplink() == nil {
			dl, dlErr := deeplinkProvider.GetLoansApplicationStatusPollDeeplink(ctx, deeplinkProvider.GetLoanHeader(), req.GetReq().GetAuth().GetActorId(), req.GetLoanRequestId(), &provider.ApplicationStatusPollDeeplinkParams{
				GetNextActionInSync: false,
			})
			if dlErr != nil {
				res.RespHeader.Status = rpcPb.StatusInternal()
				res.RespHeader.ErrorView = defaultErrorView
				return res, nil
			}
			res.Deeplink = dl
		}
		logger.Info(ctx, "screen name in GetApplicationStatusResponse", zap.String(logger.ID, res.GetDeeplink().GetScreen().String()))
		return res, nil
	}
	beRes, err := s.preApprovedLoanClient.GetApplicationStatus(ctx, &preapprovedloanPbBe.GetApplicationStatusRequest{
		LoanRequestId: req.GetLoanRequestId(), // Check if it's the same
		ActorId:       req.GetReq().GetAuth().GetActorId(),
		LoanHeader:    helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
	})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		logger.Error(ctx, "preApprovedLoanClient.GetApplicationStatus BE API failed",
			zap.Error(te),
		)
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}
	res.RespHeader.Status = rpcPb.StatusOk()

	if deepLinkRes, err := s.getApplicationStatusScreenV2(ctx, beRes.GetLoanRequest(), beRes.GetCurrentExecution(), req); err != nil {
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	} else {
		res.Deeplink = deepLinkRes
		res.LoanRequestId = req.GetLoanRequestId()
		minAndroidVersion := s.genconf.Lending().PreApprovedLoan().PollScreenV2AppVersionConstraintConfig().MinAndroidVersion()
		minIosVersion := s.genconf.Lending().PreApprovedLoan().PollScreenV2AppVersionConstraintConfig().MinIOSVersion()
		if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_LAMF &&
			deepLinkRes.GetScreen() == deeplinkPb.Screen_LOANS_STATUS_POLL_SCREEN &&
			helper.ShowOldVersionForPollingScreen(ctx, minAndroidVersion, minIosVersion) {
			dl, dlErr := s.convertIntoOldPollingScreen(ctx, deepLinkRes)
			if dlErr != nil {
				res.RespHeader.Status = rpcPb.StatusInternal()
				res.RespHeader.ErrorView = defaultErrorView
				return res, nil
			}
			res.Deeplink = dl
		}
		logger.Info(ctx, "screen name in GetApplicationStatusResponse", zap.String(logger.ID, res.GetDeeplink().GetScreen().String()))
		s.sendScreenLoadedEvent(ctx, beRes.GetLoanRequest(), beRes.GetCurrentExecution())
		res.GetRespHeader().FeedbackEngineInfo = getFeedbackInfoForScreen(res.GetDeeplink().GetScreen())
		return res, nil
	}
}

func getFeedbackInfoForScreen(screen deeplinkPb.Screen) *header.FeedbackEngineInfo {
	switch screen {
	case deeplinkPb.Screen_LOANS_WEBVIEW_WITH_STATUS_POLL_SCREEN:
		return &header.FeedbackEngineInfo{
			FlowIdDetails: &header.FlowIdentifierDetails{
				FlowIdentifierType: types.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_DROP_OFF,
				FlowIdentifier:     types.FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_WEBVIEW_WITH_STATUS_POLL_SCREEN.String(),
			},
		}
	default:
		return nil
	}
}

func (s *Service) sendScreenLoadedEvent(ctx context.Context, lr *preapprovedloanPbBe.LoanRequest, lse *preapprovedloanPbBe.LoanStepExecution) {
	if lr.GetNextAction().GetScreen() == deeplinkPb.Screen_LAMF_LINK_MF_SCREEN {
		goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
			var (
				successfulPfFetchCount        = 0
				foliosUnlinkedToPrimaryEmail  = len(lse.GetDetails().GetLoanDetailsVerificationData().GetLamf().GetEmailLinkDetails().GetFolios())
				foliosUnlinkedToPrimaryMobile = len(lse.GetDetails().GetLoanDetailsVerificationData().GetLamf().GetMobileLinkDetails().GetFolios())
				screenName                    string
			)
			for _, pfFetchData := range lse.GetDetails().GetLoanDetailsVerificationData().GetLamf().GetPfFetch_Data() {
				if pfFetchData.GetIsFetchSuccess() {
					successfulPfFetchCount++
				}
			}
			if successfulPfFetchCount > 0 {
				screenName = palEvents.LinkMutualFundsIntermediateScreen
			} else if foliosUnlinkedToPrimaryMobile == 0 && foliosUnlinkedToPrimaryEmail == 0 {
				screenName = palEvents.LinkMutualFundsSuccessfulScreen
			} else {
				screenName = palEvents.LinkMutualFundsScreen
			}
			s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), palEvents.NewScreenLoadedEvent(lr, lse, screenName))
		})
	}
}

func getFirstElementFromTextArray(textArray []*commontypes.Text) *commontypes.Text {
	if textArray != nil {
		return textArray[0]
	}
	return nil
}

func (s *Service) convertIntoOldPollingScreen(_ context.Context, dl *deeplinkPb.Deeplink) (*deeplinkPb.Deeplink, error) {
	screenParams := &preapprovedloans.LoansStatusPollScreenOptions{}
	err := dl.GetScreenOptionsV2().UnmarshalTo(screenParams)
	if err != nil {
		return nil, fmt.Errorf("error in unmarshalling screen options for polling screen : %w", err)
	}

	var title, subtitle *commontypes.Text
	var centreIcon *commontypes.VisualElement
	if screenParams.GetPollingView().GetTimerView() != nil {
		title = getFirstElementFromTextArray(screenParams.GetPollingView().GetTimerView().GetPollingText().GetTitle().GetTexts())
		subtitle = getFirstElementFromTextArray(screenParams.GetPollingView().GetTimerView().GetPollingText().GetValue().GetTexts())
		centreIcon = commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/lamf/loader_doc.png", 120, 120)
	} else {
		title = screenParams.GetPollingView().GetPollingTextWithVisualView().GetTextWithVisual().GetTitleText()
		subtitle = screenParams.GetPollingView().GetPollingTextWithVisualView().GetTextWithVisual().GetSubtitleText()
		centreIcon = screenParams.GetPollingView().GetPollingTextWithVisualView().GetTextWithVisual().GetVisualElement()
	}
	bgColor := &widget.BackgroundColour{Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#FFFFFF"}}
	if screenParams.GetBgColour() != nil {
		bgColor = screenParams.GetBgColour()
	}
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_APPLICATION_STATUS_POLL_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanApplicationStatusPollScreenOptions{
			PreApprovedLoanApplicationStatusPollScreenOptions: &deeplinkPb.PreApprovedLoanApplicationStatusPollScreenOptions{
				RetryAttemptNumber: screenParams.GetRetryAttemptNumber(),
				RetryDelay:         screenParams.GetRetryDelay(),
				RetryDuration:      screenParams.GetRetryDuration(),
				LoanRequestId:      screenParams.GetLoanRequestId(),
				PollingText: &deeplinkPb.InfoItemV2{
					Title:    title,
					SubTitle: subtitle,
				},
				LoanHeader:       screenParams.GetLoanHeader(),
				CentreIcon:       centreIcon,
				BackgroundColour: bgColor,
			},
		},
	}, nil
}

// getDowntimeDeeplink returns downtime deeplink if downtime is active for the given vendor & program
// it returns nil if downtime is not active/applicable
func (s *Service) getDowntimeDeeplink(ctx context.Context, loanHeader *palFeEnumsPb.LoanHeader) (*deeplinkPb.Deeplink, error) {
	getDowntimeStatusRes, err := s.preApprovedLoanClient.GetDowntimeStatus(
		ctx,
		&preapprovedloanPbBe.GetDowntimeStatusRequest{
			LoanHeader: helper.GetBeLoanHeaderByFeLoanHeader(loanHeader),
		},
	)
	if err != nil {
		return nil, fmt.Errorf("error while getting downtime status: %w", err)
	}
	if !getDowntimeStatusRes.GetIsDowntime() {
		return nil, nil
	}
	deeplinkProvider := s.getDeeplinkProvider(
		ctx,
		loanHeader.GetVendor(),
		loanHeader.GetLoanProgram(),
	)
	downtimeDeeplink, err := deeplinkProvider.GetPlDowntimeScreenDeepLink(
		ctx,
		loanHeader,
		getDowntimeStatusRes.GetNextAvailableTime().AsTime(),
	)
	if err != nil {
		return nil, fmt.Errorf("error while getting downtime deeplink: %w", err)
	}
	return downtimeDeeplink, nil
}

// nolint: dupl
func (s *Service) ApplyForLoan(ctx context.Context, req *palFePb.ApplyForLoanRequest) (*palFePb.ApplyForLoanResponse, error) {
	res := &palFePb.ApplyForLoanResponse{
		RespHeader: &header.ResponseHeader{},
	}
	helper.LogIfLoanHeaderNotPresent(ctx, req.GetLoanHeader(), "ApplyForLoan")
	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}
	if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		req.GetLoanHeader().LoanProgram = palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram)
	}

	downtimeDeeplink, err := s.getDowntimeDeeplink(ctx, req.GetLoanHeader())
	if err != nil {
		logger.Error(ctx, "error while getting downtime deeplink", zap.Error(err))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}
	// If downtime is active, return downtime deeplink
	if downtimeDeeplink != nil {
		res.Deeplink = downtimeDeeplink
		res.RespHeader.Status = rpcPb.StatusOk()
		return res, nil
	}

	logger.Info(ctx, fmt.Sprintf("loan application started for vendor: %v, program: %v", req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()))
	startTime := time.Now()
	s.PushApiTriggeredEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameApplyForLoan,
		req.GetOfferId(), "", palEvents.LoanApplicationFlow, getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())

	var consents []consentPb.ConsentType
	if len(req.GetConsentIds()) > 0 {
		for _, consent := range req.GetConsentIds() {
			consents = append(consents, consentPb.ConsentType(consentPb.ConsentType_value[consent]))
		}
	} else {
		consents, err = s.getConsentsForApplyForLoan(req)
		if err != nil {
			logger.Error(ctx, "error while getting consents", zap.Error(err), zap.String(logger.VENDOR, req.GetLoanHeader().GetVendor().String()),
				zap.String(logger.LOAN_PROGRAM, req.GetLoanHeader().GetLoanProgram().String()))
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
	}
	if len(consents) > 0 {
		if req.GetReq().GetAuth().GetDevice().GetLocationToken() == "" {
			logger.Error(ctx, "received empty location token while recording consents", zap.String(logger.VENDOR, req.GetLoanHeader().GetVendor().String()),
				zap.String(logger.LOAN_PROGRAM, req.GetLoanHeader().GetLoanProgram().String()))
			res.RespHeader.Status = rpcPb.StatusFailedPrecondition()
			res.RespHeader.ErrorView = locationRequiredErrorView
			return res, nil
		}
		err = s.RecordConsents(ctx, req.GetReq().GetAuth().GetActorId(), req.GetReq().GetAuth().GetDevice(), consents, "")
		if err != nil {
			logger.Error(ctx, "error while calling RecordConsents", zap.Error(err))
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
	}

	beRes, err := s.preApprovedLoanClient.ApplyForLoan(ctx, &preapprovedloanPbBe.ApplyForLoanRequest{
		ActorId:                  req.GetReq().GetAuth().GetActorId(),
		OfferId:                  req.GetOfferId(),
		LoanAmount:               req.GetLoanAmount().GetBeMoney(),
		TenureInMonths:           req.GetTenureInMonths(),
		PledgeDetails:            getBeMfPledgeDetails(req.GetMutualFunds()),
		LoanHeader:               helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
		LocationToken:            req.GetReq().GetAuth().GetDevice().GetLocationToken(),
		CancelCurrentLoanRequest: req.GetCancelCurrentLoanRequest(),
	})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		if beRes.GetStatus().IsAlreadyExists() || beRes.GetStatus().IsPermissionDenied() {
			res.RespHeader.Status = rpcPb.StatusOk()
			appVersionConstraintData := release.NewAppVersionConstraintData(s.genconf.Lending().PreApprovedLoan().AutoCancelCurrentLrConfig())
			isAppVersionCompatible, appVerErr := release.NewAppVersionConstraint().Evaluate(ctx, appVersionConstraintData, nil)
			if appVerErr != nil {
				logger.Error(ctx, "Error evaluating app version constraint", zap.Error(appVerErr))
				res.RespHeader.Status = rpcPb.StatusInternal()
				res.RespHeader.ErrorView = defaultErrorView
				return res, nil
			}

			res.Deeplink, err = baseprovider.GetApplyForLoanAlreadyExistScreen(&baseprovider.GetApplyForLoanAlreadyExistScreenRequest{
				ActiveLr:                 beRes.GetActiveLoanRequest(),
				ActiveLse:                beRes.GetActiveLse(),
				ActiveAccountId:          beRes.GetActiveLoanAccountId(),
				IsCancelSupported:        isAppVersionCompatible,
				ApplyForLoanReq:          req,
				IsCancellationRestricted: beRes.GetStatus().IsPermissionDenied(),
			})
			if err != nil {
				logger.Error(ctx, "error while calling GetApplyForLoanAlreadyExistScreen", zap.Error(err))
				res.RespHeader.Status = rpcPb.StatusInternal()
				res.RespHeader.ErrorView = bottomSheetErrorView
				return res, nil
			}
			ownership := getEventsOwnership(helper.GetPalFeVendorFromBe(beRes.GetRespHeader().GetVendor()), helper.GetFeLoanProgramFromBe(beRes.GetRespHeader().GetLoanProgram()))
			subStatus := getEventsOwnership(helper.GetPalFeVendorFromBe(beRes.GetActiveLoanRequest().GetVendor()), helper.GetFeLoanProgramFromBe(beRes.GetActiveLoanRequest().GetLoanProgram()))
			s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameApplyForLoan, time.Since(startTime), palEvents.StatusAlreadyExists, subStatus, req.GetOfferId(), "", palEvents.LoanApplicationFlow, ownership, req.GetLoanHeader().GetEventData())
			return res, nil
		}
		logger.Error(ctx, "preApprovedLoanClient.ApplyForLoan BE API failed", zap.String(logger.OFFER_ID, req.GetOfferId()), zap.Error(te))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = bottomSheetErrorView
		return res, nil
	}

	if err != nil {
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = bottomSheetErrorView
		return res, nil
	} else {
		ownership := getEventsOwnership(helper.GetPalFeVendorFromBe(beRes.GetRespHeader().GetVendor()), helper.GetFeLoanProgramFromBe(beRes.GetRespHeader().GetLoanProgram()))
		status := palEvents.StatusSuccess
		subStatus := ""
		if req.GetCancelCurrentLoanRequest() {
			status = palEvents.StatusCancelledAndStartedNew
			subStatus = getEventsOwnership(helper.GetPalFeVendorFromBe(beRes.GetActiveLoanRequest().GetVendor()), helper.GetFeLoanProgramFromBe(beRes.GetActiveLoanRequest().GetLoanProgram()))
		}
		s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameApplyForLoan, time.Since(startTime), status, subStatus, req.GetOfferId(), "", palEvents.LoanApplicationFlow, ownership, req.GetLoanHeader().GetEventData())

		deeplinkProvider := s.getDeeplinkProvider(ctx, req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())
		dl, dlErr := getStatusPollScreen(ctx, deeplinkProvider, beRes.GetLoanRequestId())
		if dlErr != nil {
			logger.Error(ctx, "error while generating application status poll deeplink", zap.Error(dlErr))
			return nil, fmt.Errorf("error while generating application status poll deeplink: %w", dlErr)
		}
		res.Deeplink = dl
		s.populateLODropOffFeedbackIdentifierInRespHeaderIfEnabled(res.GetRespHeader())
		res.RespHeader.Status = rpcPb.StatusOk()
		return res, nil
	}
}

func (s *Service) ConfirmApplication(ctx context.Context, req *palFePb.ConfirmApplicationRequest) (*palFePb.ConfirmApplicationResponse, error) {
	helper.LogIfLoanHeaderNotPresent(ctx, req.GetLoanHeader(), "ConfirmApplication")
	startTime := time.Now()
	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}
	if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		req.GetLoanHeader().LoanProgram = palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram)
	}
	s.PushApiTriggeredEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameConfirmApplication, "", req.GetLoanRequestId(), palEvents.LoanManagementFlow,
		getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())

	res := &palFePb.ConfirmApplicationResponse{
		RespHeader: &header.ResponseHeader{},
	}
	err := s.validateConfirmApplicationRequest(req)
	if err != nil {
		logger.Error(ctx, "confirm application request validation failed", zap.Error(err))
		res.RespHeader.Status = rpcPb.StatusInvalidArgument()
		res.RespHeader.ErrorView = bottomSheetErrorView
		return res, nil
	}
	beRes, err := s.preApprovedLoanClient.ConfirmApplication(ctx, &preapprovedloanPbBe.ConfirmApplicationRequest{
		ActorId:             req.GetReq().GetAuth().GetActorId(),
		LoanRequestId:       req.GetLoanRequestId(),
		Otp:                 req.GetOtp(),
		OtpFlow:             getBeOtpFlow(req.GetOtpFlow()),
		LoanHeader:          helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
		Token:               req.GetToken(),
		Device:              req.GetReq().GetAuth().GetDevice(),
		LoanStepExecutionId: req.GetLoanStepExecutionId(),
		OtpFlowIdentifier:   req.GetOtpFlowIdentifier(),
	})

	if te := epifigrpc.RPCError(beRes, err); te != nil {
		if beRes.GetStatus().IsPermissionDenied() {
			res.RespHeader.Status = rpcPb.StatusOk()
			deeplinkProvider := s.getDeeplinkProvider(ctx, helper.GetPalFeVendorFromBe(beRes.GetLoanRequest().GetVendor()), helper.GetFeLoanProgramFromBe(beRes.GetLoanRequest().GetLoanProgram()))
			res.Deeplink = deeplinkProvider.GetLoanLandingInfo(ctx, deeplinkProvider.GetLoanHeader(), &provider.GetLoanLandingInfoRequest{ActorId: req.GetReq().GetAuth().GetActorId()})
			if beRes.GetLoanRequest().GetLoanProgram() == helper.GetBeLoanProgramFromFe(palFeEnumsPb.LoanProgram_LOAN_PROGRAM_LAMF) {
				deeplinkProvider := s.getDeeplinkProvider(ctx, helper.GetPalFeVendorFromBe(beRes.GetLoanRequest().GetVendor()), helper.GetFeLoanProgramFromBe(beRes.GetLoanRequest().GetLoanProgram()))
				dl, dlErr := deeplinkProvider.GetApplicationStatusPollScreenDeepLink(ctx, deeplinkProvider.GetLoanHeader(), beRes.GetLoanRequest().GetId())
				if dlErr != nil {
					logger.Error(ctx, "error while generating application status poll deeplink %v", zap.Error(dlErr))
					res.RespHeader.Status = rpcPb.StatusInternal()
					res.RespHeader.ErrorView = bottomSheetErrorView
					return res, nil
				}
				res.Deeplink = dl
			}
			return res, nil
		}
		if beRes.GetStatus().IsTransientFailure() || beRes.GetStatus().GetCode() == uint32(preapprovedloanPbBe.ConfirmApplicationResponse_OTP_EXPIRED) || beRes.GetStatus().GetCode() == uint32(preapprovedloanPbBe.ConfirmApplicationResponse_INCORRECT_OTP) || beRes.GetStatus().GetCode() == uint32(preapprovedloanPbBe.ConfirmApplicationResponse_TEMP_BLOCKED) {
			deeplinkProvider := s.getDeeplinkProvider(ctx, helper.GetPalFeVendorFromBe(beRes.GetLoanRequest().GetVendor()), helper.GetFeLoanProgramFromBe(beRes.GetLoanRequest().GetLoanProgram()))
			res.RespHeader.Status = rpcPb.StatusOk()
			// Backward compatibility check for clients not having CKYC OTP flow enum
			isSGCKYCOTPFlowAsPerLrLse := beRes.GetLoanRequest().GetVendor() == preapprovedloanPbBe.Vendor_STOCK_GUARDIAN_LSP &&
				beRes.GetLoanStep().GetStepName() == preapprovedloanPbBe.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CKYC
			switch {
			case req.GetOtpFlowIdentifier() == palFeEnumsPb.OtpFlow_OTP_FLOW_ADD_ALTERNATE_PHONE_NUMBER:
				var dl *deeplinkPb.Deeplink
				var dlErr error
				if beRes.GetLoanStep().GetSubStatus() == preapprovedloanPbBe.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CONTACTABILITY_COOL_OFF_PERIOD {
					dl, dlErr = deeplinkProvider.GetAlternateContactCoolOffScreen(ctx, deeplinkProvider.GetLoanHeader())
				} else {
					dl, dlErr = deeplinkProvider.GetAlternateContactVerificationViaOtpScreen(ctx, deeplinkProvider.GetLoanHeader(), beRes.GetLoanStep(), true)
				}
				if dlErr != nil {
					logger.Error(ctx, "error in getting GetAlternateContactVerificationViaOtpScreen")
				}
				res.Deeplink = dl
			case req.GetOtpFlowIdentifier() == palFeEnumsPb.OtpFlow_OTP_FLOW_CKYC_OTP_VERIFCATION || isSGCKYCOTPFlowAsPerLrLse:
				nextAction, err := s.handleCkycOtpVerificationRes(ctx, beRes)
				if err != nil {
					logger.Error(ctx, "error handling CKYC OTP verification response", zap.Error(err))
					res.RespHeader.Status = rpcPb.StatusInternal()
					res.RespHeader.ErrorView = bottomSheetErrorView
					return res, nil
				}
				res.Deeplink = nextAction
			default:
				res.Deeplink = deeplinkProvider.GetLoanApplicationConfirmationViaOtpIncorrectScreenDeepLink(deeplinkProvider.GetLoanHeader(), beRes.GetLoanRequest(), beRes.GetLoanStep(), req.GetToken())
				switch beRes.GetStatus().GetCode() {
				case uint32(preapprovedloanPbBe.ConfirmApplicationResponse_OTP_EXPIRED):
					res.Deeplink.GetPreApprovedLoanApplicationConfirmationViaOtpScreenOptions().IncorrectOtpMessage = "Resend OTP as it has expired"
				case uint32(preapprovedloanPbBe.ConfirmApplicationResponse_INCORRECT_OTP):
					res.Deeplink.GetPreApprovedLoanApplicationConfirmationViaOtpScreenOptions().IncorrectOtpMessage = "Incorrect OTP"
				}
			}
			return res, nil
		}
		if beRes.GetStatus().IsFailedPrecondition() {
			res.RespHeader.Status = rpcPb.StatusFailedPrecondition()
			res.RespHeader.ErrorView = mandateNotActiveBottomSheetErrorView
			return res, nil
		}
		logger.Error(ctx, "preApprovedLoanClient.ConfirmApplication BE API failed",
			zap.Error(err),
		)
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = bottomSheetErrorView
		return res, nil
	}

	if req.GetOtpFlowIdentifier() == palFeEnumsPb.OtpFlow_OTP_FLOW_ADD_ALTERNATE_PHONE_NUMBER {
		res.Deeplink = beRes.GetLoanRequest().GetNextAction()
		res.RespHeader.Status = rpcPb.StatusOk()
		return res, nil
	}

	ownership := getEventsOwnership(helper.GetPalFeVendorFromBe(beRes.GetLoanRequest().GetVendor()), helper.GetFeLoanProgramFromBe(beRes.GetLoanRequest().GetLoanProgram()))
	s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameConfirmApplication, time.Since(startTime), palEvents.StatusSuccess, "", beRes.GetLoanRequest().GetOfferId(), beRes.GetLoanRequest().GetId(), palEvents.LoanApplicationFlow, ownership, req.GetLoanHeader().GetEventData())
	deeplinkProvider := s.getDeeplinkProvider(ctx, helper.GetPalFeVendorFromBe(beRes.GetLoanRequest().GetVendor()), helper.GetFeLoanProgramFromBe(beRes.GetLoanRequest().GetLoanProgram()))

	// Backward compatibility check for clients not having CKYC OTP flow enum
	isSGCKYCOTPFlowAsPerLrLse := beRes.GetLoanRequest().GetVendor() == preapprovedloanPbBe.Vendor_STOCK_GUARDIAN_LSP &&
		beRes.GetLoanStep().GetStepName() == preapprovedloanPbBe.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CKYC
	if req.GetOtpFlowIdentifier() == palFeEnumsPb.OtpFlow_OTP_FLOW_CKYC_OTP_VERIFCATION || isSGCKYCOTPFlowAsPerLrLse {
		nextAction, err := s.handleCkycOtpVerificationRes(ctx, beRes)
		if err != nil {
			logger.Error(ctx, "error handling CKYC OTP verification response", zap.Error(err))
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = bottomSheetErrorView
			return res, nil
		}
		res.Deeplink = nextAction
		res.RespHeader.Status = rpcPb.StatusOk()
		return res, nil
	}
	if beRes.GetMoreOtpLeft() {
		res.Deeplink = deeplinkProvider.GetLoanApplicationConfirmationViaOtpScreen(deeplinkProvider.GetLoanHeader(), beRes.GetLoanRequest().GetId(), beRes.GetLoanStep().GetId())
	} else {
		if req.GetLoanHeader().GetVendor() == palFeEnumsPb.Vendor_FIFTYFIN {
			res.Deeplink = beRes.GetLoanRequest().GetNextAction()
		} else {
			dl, dlErr := deeplinkProvider.GetApplicationStatusPollScreenDeepLink(ctx, deeplinkProvider.GetLoanHeader(), beRes.GetLoanRequest().GetId())
			if dlErr != nil {
				logger.Error(ctx, "error while generating application status poll deeplink", zap.Error(dlErr))
				return nil, fmt.Errorf("error while generating application status poll deeplink: %w", dlErr)
			}
			res.Deeplink = dl
		}
	}

	s.populateLODropOffFeedbackIdentifierInRespHeaderIfEnabled(res.GetRespHeader())

	res.RespHeader.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) handleCkycOtpVerificationRes(ctx context.Context, beRes *preapprovedloanPbBe.ConfirmApplicationResponse) (*deeplinkPb.Deeplink, error) {
	deeplinkProvider := s.getDeeplinkProvider(ctx, helper.GetPalFeVendorFromBe(beRes.GetLoanRequest().GetVendor()), helper.GetFeLoanProgramFromBe(beRes.GetLoanRequest().GetLoanProgram()))
	var nextAction *deeplinkPb.Deeplink
	var err error
	switch beRes.GetStatus().GetCode() {
	case uint32(code.Code_OK):
		// todo: check if correct
		nextAction, err = deeplinkProvider.GetApplicationStatusPollScreenDeepLink(ctx, deeplinkProvider.GetLoanHeader(), beRes.GetLoanStep().GetRefId())
	case uint32(preapprovedloanPbBe.ConfirmApplicationResponse_TEMP_BLOCKED):
		nextAction, err = deeplinkProvider.GetApplicationStatusPollScreenDeepLink(ctx, deeplinkProvider.GetLoanHeader(), beRes.GetLoanStep().GetRefId())
	case uint32(preapprovedloanPbBe.ConfirmApplicationResponse_OTP_EXPIRED):
		nextAction, err = deeplinkProvider.GetCkycVerificationViaOtpScreen(ctx, deeplinkProvider.GetLoanHeader(), false, true, false, beRes.LoanStep, beRes.GetDisplayMsg())
	case uint32(preapprovedloanPbBe.ConfirmApplicationResponse_INCORRECT_OTP):
		nextAction, err = deeplinkProvider.GetCkycVerificationViaOtpScreen(ctx, deeplinkProvider.GetLoanHeader(), true, false, false, beRes.LoanStep, beRes.GetDisplayMsg())
	}
	if err != nil {
		return nil, errors.Wrap(err, "error generating next action for CKYC OTP verification flow")
	}
	return nextAction, nil
}

func (s *Service) validateConfirmApplicationRequest(req *palFePb.ConfirmApplicationRequest) error {
	if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_LAMF {
		if req.GetToken() == "" {
			return fmt.Errorf("received empty token")
		}
		if req.GetLoanStepExecutionId() == "" {
			return fmt.Errorf("received empty loan step execution id")
		}
	}
	return nil
}

func (s *Service) GenerateConfirmationCode(ctx context.Context, req *palFePb.GenerateConfirmationCodeRequest) (*palFePb.GenerateConfirmationCodeResponse, error) {
	helper.LogIfLoanHeaderNotPresent(ctx, req.GetLoanHeader(), "GenerateConfirmationCode")
	startTime := time.Now()
	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}
	if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		req.GetLoanHeader().LoanProgram = palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram)
	}
	s.PushApiTriggeredEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGenerateConfirmationCode, "", req.GetLoanRequestId(), palEvents.LoanManagementFlow,
		getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())

	res := &palFePb.GenerateConfirmationCodeResponse{
		RespHeader: &header.ResponseHeader{},
	}
	beRes, err := s.preApprovedLoanClient.GenerateConfirmationCode(ctx, &preapprovedloanPbBe.GenerateConfirmationCodeRequest{
		ActorId:             req.GetReq().GetAuth().GetActorId(),
		LoanRequestId:       req.GetLoanRequestId(),
		LoanHeader:          helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
		Device:              req.GetReq().GetAuth().GetDevice(),
		LoanStepExecutionId: req.GetLoanStepExecutionId(),
		OtpFlow:             req.GetOtpFlow(),
	})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		logger.Error(ctx, "preApprovedLoanClient.GenerateConfirmationCode BE API failed", zap.Error(err))
		deeplinkProvider := s.getDeeplinkProvider(ctx, helper.GetPalFeVendorFromBe(beRes.GetLoanRequest().GetVendor()), helper.GetFeLoanProgramFromBe(beRes.GetLoanRequest().GetLoanProgram()))
		if beRes.GetStatus().IsRecordNotFound() {
			res.RespHeader.Status = rpcPb.StatusOk()
			dl, dlErr := deeplinkProvider.GetApplicationStatusPollScreenDeepLink(ctx, deeplinkProvider.GetLoanHeader(), beRes.GetLoanRequest().GetId())
			if dlErr != nil {
				logger.Error(ctx, "error while generating application status poll deeplink", zap.Error(dlErr))
				return nil, fmt.Errorf("error while generating application status poll deeplink: %w", dlErr)
			}
			res.Deeplink = dl
			return res, nil
		}

		// Backward compatibility check for clients not having CKYC OTP flow enum
		isSGCKYCOTPFlowAsPerLrLse := beRes.GetLoanRequest().GetVendor() == preapprovedloanPbBe.Vendor_STOCK_GUARDIAN_LSP &&
			beRes.GetLoanStepExecution().GetStepName() == preapprovedloanPbBe.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CKYC
		if req.GetOtpFlow() == palFeEnumsPb.OtpFlow_OTP_FLOW_CKYC_OTP_VERIFCATION || isSGCKYCOTPFlowAsPerLrLse {
			nextAction, err := s.handleCkycOtpGenerationRes(ctx, beRes)
			if err != nil {
				logger.Error(ctx, "error handling CKYC OTP generation response", zap.Error(err))
				res.RespHeader.Status = rpcPb.StatusInternal()
				res.RespHeader.ErrorView = bottomSheetErrorView
				return res, nil
			}
			res.Deeplink = nextAction
			res.RespHeader.Status = rpcPb.StatusOk()
			return res, nil
		}
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}

	deeplinkProvider := s.getDeeplinkProvider(ctx, helper.GetPalFeVendorFromBe(beRes.GetLoanRequest().GetVendor()), helper.GetFeLoanProgramFromBe(beRes.GetLoanRequest().GetLoanProgram()))
	ownership := getEventsOwnership(helper.GetPalFeVendorFromBe(beRes.GetLoanRequest().GetVendor()), helper.GetFeLoanProgramFromBe(beRes.GetLoanRequest().GetLoanProgram()))
	s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGenerateConfirmationCode, time.Since(startTime), palEvents.StatusSuccess, "", beRes.GetLoanRequest().GetOfferId(), beRes.GetLoanRequest().GetId(), palEvents.LoanApplicationFlow, ownership, req.GetLoanHeader().GetEventData())
	// Backward compatibility check for clients not having CKYC OTP flow enum
	isSGCKYCOTPFlowAsPerLrLse := beRes.GetLoanRequest().GetVendor() == preapprovedloanPbBe.Vendor_STOCK_GUARDIAN_LSP &&
		beRes.GetLoanStepExecution().GetStepName() == preapprovedloanPbBe.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CKYC
	if req.GetOtpFlow() == palFeEnumsPb.OtpFlow_OTP_FLOW_ADD_ALTERNATE_PHONE_NUMBER {
		dl, err := deeplinkProvider.GetAlternateContactVerificationViaOtpScreen(ctx, deeplinkProvider.GetLoanHeader(), beRes.GetLoanStepExecution(), false)
		if err != nil {
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
		res.Deeplink = dl
	} else if req.GetOtpFlow() == palFeEnumsPb.OtpFlow_OTP_FLOW_CKYC_OTP_VERIFCATION || isSGCKYCOTPFlowAsPerLrLse {
		nextAction, err := s.handleCkycOtpGenerationRes(ctx, beRes)
		if err != nil {
			logger.Error(ctx, "error handling CKYC OTP generation response", zap.Error(err))
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = bottomSheetErrorView
			return res, nil
		}
		res.Deeplink = nextAction
	} else {
		res.Deeplink = deeplinkProvider.GetLoanApplicationConfirmationViaOtpScreenDeepLink(deeplinkProvider.GetLoanHeader(), beRes.GetLoanRequest(), beRes.GetLoanStepExecution(), true, beRes.GetToken())
	}
	res.RespHeader.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) handleCkycOtpGenerationRes(ctx context.Context, beRes *preapprovedloanPbBe.GenerateConfirmationCodeResponse) (*deeplinkPb.Deeplink, error) {
	deeplinkProvider := s.getDeeplinkProvider(ctx, helper.GetPalFeVendorFromBe(beRes.GetLoanRequest().GetVendor()), helper.GetFeLoanProgramFromBe(beRes.GetLoanRequest().GetLoanProgram()))
	var nextAction *deeplinkPb.Deeplink
	var err error
	switch beRes.GetStatus().GetCode() {
	case uint32(code.Code_OK):
		nextAction, err = deeplinkProvider.GetCkycVerificationViaOtpScreen(ctx, deeplinkProvider.GetLoanHeader(), false, false, true, beRes.LoanStepExecution, beRes.GetDisplayMsg())
	case uint32(preapprovedloanPbBe.GenerateConfirmationCodeResponse_STATUS_TEMP_BLOCKED),
		uint32(preapprovedloanPbBe.GenerateConfirmationCodeResponse_STATUS_FAILED):
		nextAction, err = deeplinkProvider.GetApplicationStatusPollScreenDeepLink(ctx, deeplinkProvider.GetLoanHeader(), beRes.GetLoanStepExecution().GetRefId())
	}
	if err != nil {
		return nil, errors.Wrap(err, "error generating next action for CKYC OTP generation flow")
	}
	return nextAction, nil
}

func (s *Service) GetDashboard(ctx context.Context, req *palFePb.GetDashboardRequest) (*palFePb.GetDashboardResponse, error) {
	helper.LogIfLoanHeaderNotPresent(ctx, req.GetLoanHeader(), "GetDashboard")
	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}
	if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		req.GetLoanHeader().LoanProgram = palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram)
	}

	res := &palFePb.GetDashboardResponse{
		RespHeader: &header.ResponseHeader{},
	}

	appVersionConstraintData := release.NewAppVersionConstraintData(s.genconf.Lending().ForceUpgradeFeatureConfig())
	isAppVersionGreater, appVerErr := release.NewAppVersionConstraint().Evaluate(ctx, appVersionConstraintData, nil)
	if appVerErr != nil {
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}

	deeplinkProvider := s.getDeeplinkProvider(ctx, req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())
	platform, _ := epificontext.AppPlatformAndVersion(ctx)
	if !isAppVersionGreater {
		dl := deeplinkProvider.GetForceUpgradeLandingResponse(platform, "", "Please update your app to latest version to continue your application", req.GetLoanHeader())
		res.Deeplink = dl
		res.RespHeader.Status = rpcPb.StatusOk()
		return res, nil
	}

	if apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.genconf.Lending().UseLandingDataV3()) && !isSecuredLoanHeader(req.GetLoanHeader()) {
		return s.getDashboardV3(ctx, req)
	}

	startTime := time.Now()
	s.PushApiTriggeredEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGetDashboard, "", "", palEvents.LoanManagementFlow,
		getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())

	var beRes *preapprovedloanPbBe.GetDashboardResponse
	var err error
	// todo need to remove this when repeat loans changes are merged
	// calling GetDashboard() API for Es to allow users see latest dashboard for ongoing
	// application instead of previous closed loan if any.
	if helper.GetBeLoanProgramFromFe(req.GetLoanHeader().GetLoanProgram()) == preapprovedloanPbBe.LoanProgram_LOAN_PROGRAM_EARLY_SALARY {
		beRes, err = s.preApprovedLoanClient.GetDashboard(ctx, &preapprovedloanPbBe.GetDashboardRequest{
			ActorId:    req.GetReq().GetAuth().GetActorId(),
			LoanHeader: helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
		})
		if te := epifigrpc.RPCError(beRes, err); te != nil {
			logger.Error(ctx, "preApprovedLoanClient.GetDashboard BE API failed", zap.Error(te))
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
	} else {
		beRes, err = s.preApprovedLoanClient.GetDashboardV2(ctx, &preapprovedloanPbBe.GetDashboardRequest{
			ActorId:    req.GetReq().GetAuth().GetActorId(),
			LoanHeader: helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
		})
		if te := epifigrpc.RPCError(beRes, err); te != nil {
			logger.Error(ctx, "preApprovedLoanClient.GetDashboardV2 BE API failed", zap.Error(te))
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
	}

	var la *preapprovedloanPbBe.LoanAccount
	if beRes.GetLoanInfoList() != nil {
		la = beRes.GetLoanInfoList()[0].GetLoanAccount()
	}
	vendor := helper.GetPalFeVendorFromBe(getVendorFromBeTables(beRes.GetRecentLoanRequest(), beRes.GetActiveLoanOffer(), la, nil, nil))
	lp := helper.GetFeLoanProgramFromBe(getLpFromBeTables(beRes.GetRecentLoanRequest(), beRes.GetActiveLoanOffer(), la, nil))

	ownership := getEventsOwnership(vendor, lp)
	deeplinkProvider = s.getDeeplinkProvider(ctx, vendor, lp)
	var dl *deeplinkPb.Deeplink

	if s.isPostDisbursalFlowEnabled(ctx, req.GetReq().GetAuth().GetActorId(), &palFeEnumsPb.LoanHeader{
		LoanProgram: lp,
		Vendor:      vendor,
	}) {
		dlRes, err := s.aggregatedDeeplinkProvider.GetLoanDashboardDeepLinkV3(ctx, beRes)
		if err != nil {
			s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGetDashboard, time.Since(startTime),
				palEvents.StatusFailed, palEvents.SubStatusLoanDashboard, beRes.GetActiveLoanOffer().GetId(), beRes.GetRecentLoanRequest().GetId(),
				palEvents.LoanManagementFlow, ownership, req.GetLoanHeader().GetEventData())
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
		dl = dlRes.Deeplink
		s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGetDashboard, time.Since(startTime), palEvents.StatusSuccess, dlRes.EventSubStatus,
			beRes.GetActiveLoanOffer().GetId(), beRes.GetRecentLoanRequest().GetId(), palEvents.LoanManagementFlowV2, ownership, deeplinkProvider.GetLoanHeader().GetEventData())
	} else {
		dl, err = deeplinkProvider.GetLoanDashboardScreenDeepLinkWithScreenOptionsV2(ctx,
			deeplinkProvider.GetLoanHeader(), beRes, false, req.GetEntryPoint())
		if err != nil {
			s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGetDashboard, time.Since(startTime),
				palEvents.StatusFailed, palEvents.SubStatusLoanDashboard, beRes.GetActiveLoanOffer().GetId(), beRes.GetRecentLoanRequest().GetId(),
				palEvents.LoanManagementFlow, ownership, req.GetLoanHeader().GetEventData())
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
		s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGetDashboard, time.Since(startTime), palEvents.StatusSuccess, palEvents.SubStatusLoanDashboard,
			beRes.GetActiveLoanOffer().GetId(), beRes.GetRecentLoanRequest().GetId(), palEvents.LoanManagementFlow, ownership, deeplinkProvider.GetLoanHeader().GetEventData())
	}

	res.Deeplink = dl
	res.RespHeader.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) GetDeeplink(ctx context.Context, req *palFePb.GetDeeplinkRequest) (*palFePb.GetDeeplinkResponse, error) {
	helper.LogIfLoanHeaderNotPresent(ctx, req.GetLoanHeader(), "GetDeeplink")
	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}
	if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		req.GetLoanHeader().LoanProgram = palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram)
	}
	startTime := time.Now()
	s.PushApiTriggeredEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGetDeepLink, "", req.GetLoanRequestId(), palEvents.LoanManagementFlow,
		getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())
	res := &palFePb.GetDeeplinkResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpcPb.StatusOk(),
		},
	}

	deeplinkProvider := s.getDeeplinkProvider(ctx, req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())
	switch req.GetScreen() {
	case deeplinkPb.Screen_PRE_APPROVED_LOAN_KNOW_MORE_SCREEN,
		deeplinkPb.Screen_EARLY_SALARY_FAQ_SCREEN:
		s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGetDeepLink, time.Since(startTime), palEvents.StatusSuccess, palEvents.SubStatusKnowMore,
			"", req.GetLoanRequestId(), palEvents.LoanManagementFlow, getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())
		res.Deeplink = deeplinkProvider.GetKnowMoreScreenDeepLink(ctx, deeplinkProvider.GetLoanHeader(), req.GetKnowMoreScreenParams().GetFaqTopic())
		return res, nil
	case deeplinkPb.Screen_PRE_APPROVED_LOAN_HOW_TO_APPLY_SCREEN:
		s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGetDeepLink, time.Since(startTime), palEvents.StatusSuccess, palEvents.SubStatusHowToApply,
			"", req.GetLoanRequestId(), palEvents.LoanManagementFlow, getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())
		res.Deeplink = baseprovider.GetHowToApplyScreenDeepLink()
		return res, nil
	case deeplinkPb.Screen_PRE_APPROVED_LOAN_DASHBOARD_SCREEN:
		feRes, err := s.GetDashboard(ctx, &palFePb.GetDashboardRequest{
			Req: req.GetReq(),
		})
		if te := fepkg.FeRPCError(feRes, err); te != nil {
			logger.Error(ctx, "GetDashboard FE API failed", zap.Error(te))
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}

		res.RespHeader = feRes.GetRespHeader()
		res.Deeplink = feRes.GetDeeplink()
		return res, nil
	case deeplinkPb.Screen_PL_APPLICATION_REVIEW_DETAILS_SCREEN:
		beRes, err := s.preApprovedLoanClient.GetLoanReviewDetails(ctx, &preapprovedloanPbBe.GetLoanReviewDetailsRequest{
			LoanRequestId: req.GetLoanRequestId(),
			ActorId:       req.GetReq().GetAuth().GetActorId(),
			LoanHeader:    helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
		})
		if te := epifigrpc.RPCError(beRes, err); te != nil {
			logger.Error(ctx, "error in GetLoanReviewDetails BE api", zap.Error(te))
			res.GetRespHeader().Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
		deeplinkReviewDetailsScreen, dpErr := deeplinkProvider.GetLoanReviewDetailsScreenDeeplink(ctx, req.GetLoanHeader(), req.GetLoanRequestId(), beRes, req.GetReq().GetAuth().GetActorId())
		if dpErr != nil {
			logger.Error(ctx, "error in getting review details screen deeplink", zap.Error(dpErr))
			res.GetRespHeader().Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
		res.Deeplink = deeplinkReviewDetailsScreen
		return res, nil
	case deeplinkPb.Screen_PRE_APPROVED_LOAN_PRE_PAY_SCREEN:
		loanIdResp, loanIdErr := s.preApprovedLoanClient.GetLoanIdByHeaderAndActorId(ctx, &preapprovedloanPbBe.GetLoanIdByHeaderAndActorIdRequest{
			LoanHeader: &preapprovedloanPbBe.LoanHeader{
				LoanProgram: preapprovedloanPbBe.LoanProgram(req.GetLoanHeader().GetLoanProgram()),
				Vendor:      preapprovedloanPbBe.Vendor(req.GetLoanHeader().GetVendor()),
			},
			ActorId: req.GetReq().GetAuth().GetActorId(),
		})
		if te := epifigrpc.RPCError(loanIdResp, loanIdErr); te != nil {
			logger.Error(ctx, "error in GetLoanIdByHeaderAndActorId BE api", zap.Error(te))
			res.GetRespHeader().Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
		prePayReq := &palFePb.GetPrePayDetailsRequest{
			Req:                      req.GetReq(),
			LoanId:                   loanIdResp.GetLoanId(),
			Amount:                   nil,
			IsAmountSelectionSkipped: false,
			LoanHeader:               req.GetLoanHeader(),
			IsPreClose:               false,
		}
		prePayDetails, prePayErr := s.GetPrePayDetails(ctx, prePayReq)
		if prePayErr != nil {
			logger.Error(ctx, "Error in GetPrePayDetails", zap.Error(prePayErr))
			res.GetRespHeader().Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
		res.Deeplink = prePayDetails.GetDeeplink()
		return res, nil
	case deeplinkPb.Screen_LOANS_INCOME_VERIFICATION_INTRO_SCREEN:
		// check if consent is already given
		consentResp, fcErr := s.consentClient.FetchConsent(ctx, &consentPb.FetchConsentRequest{
			ActorId:     req.GetReq().GetAuth().GetActorId(),
			ConsentType: consentPb.ConsentType_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP,
			Owner:       commontypes.Owner_OWNER_EPIFI_TECH,
		})
		if te := epifigrpc.RPCError(consentResp, fcErr); te != nil && !consentResp.GetStatus().IsRecordNotFound() {
			logger.Error(ctx, "Error in FetchConsent", zap.Error(te))
			res.GetRespHeader().Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
		appVersionConstraintData := release.NewAppVersionConstraintData(s.genconf.Lending().PreApprovedLoan().ITRFlow().AppVersionConstraintConfig())
		isAppVersionGreater, appVerErr := release.NewAppVersionConstraint().Evaluate(ctx, appVersionConstraintData, nil)
		if appVerErr != nil {
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}

		resp, err := s.preApprovedLoanClient.GetIncomeVerificationInfo(ctx, &preapprovedloanPbBe.GetIncomeVerificationInfoRequest{
			ActorId:       req.GetReq().GetAuth().GetActorId(),
			LoanHeader:    helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
			LoanRequestId: req.GetLoanRequestId(),
		})
		if te := epifigrpc.RPCError(resp, err); te != nil {
			logger.Error(ctx, "error in getIncomeVerificationInfo rpc", zap.Error(te))
			res.GetRespHeader().Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}

		getUserConsent := consentResp.GetStatus().IsRecordNotFound()
		res.Deeplink = deeplinkProvider.GetIncomeVerificationLandingScreen(req.GetLoanHeader(), req.GetLoanRequestId(), getUserConsent, s.genconf.Lending().PreApprovedLoan().ITRFlow().Enabled() && isAppVersionGreater, resp.GetItrInfo().GetIsMaxAttemptsReached())
		return res, nil
	case deeplinkPb.Screen_PRE_APPROVED_LOAN_E_SIGN_VIEW_DOCUMENT_SCREEN:
		generateEsignAgreementResp, err := s.preApprovedLoanClient.GenerateEsignAgreement(ctx, &preapprovedloanPbBe.GenerateEsignAgreementRequest{
			LoanHeader:    helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
			LoanRequestId: req.GetLoanRequestId(),
			LoanDocType:   helper.GetBEEsignDocTypeFromFEESignDocType(req.GetEsignScreenParams().GetLoanDocType()),
		})
		if te := epifigrpc.RPCError(generateEsignAgreementResp, err); te != nil {
			logger.Error(ctx, "error in GenerateEsignAgreementUrl rpc", zap.Error(te), zap.String(logger.LOAN_REQUEST_ID, req.GetLoanRequestId()))
			res.GetRespHeader().Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
		res.Deeplink = deeplinkProvider.GetEsignViewDocumentScreen(req.GetLoanHeader(), req.GetLoanRequestId(), generateEsignAgreementResp.GetSignedUrl(), req.GetEsignScreenParams().GetLoanDocType())
		if res.GetDeeplink() == nil {
			logger.Error(ctx, "failed to fetch e-sign view document screen", zap.String(logger.LOAN_REQUEST_ID, req.GetLoanRequestId()))
			res.GetRespHeader().Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
		}
		return res, nil
	case deeplinkPb.Screen_LOAN_ADD_NEW_ADDRESS_DETAILS_SCREEN:
		if req.GetReq().GetAuth().GetDevice().GetLocationToken() == "" {
			logger.Error(ctx, "location token is not present in rpc request", zap.String(logger.LOAN_REQUEST_ID, req.GetLoanRequestId()))
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
		currentLocationRes, rpcErr := s.userLocationClient.FetchAndStoreAddressForIdentifier(ctx, &userLocationPb.FetchAndStoreAddressForIdentifierRequest{
			IdentifierType:  types.IdentifierType_IDENTIFIER_TYPE_LOCATION,
			IdentifierValue: req.GetReq().GetAuth().GetDevice().GetLocationToken(),
		})
		if err := epifigrpc.RPCError(currentLocationRes, rpcErr); err != nil {
			logger.Error(ctx, "error getting address from location token", zap.Error(err))
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}

		res.Deeplink = deeplinkProvider.GetAddressDetailsScreen(req.GetLoanHeader(), req.GetLoanRequestId(), currentLocationRes.GetAddress())
		if res.GetDeeplink() == nil {
			logger.Error(ctx, "failed to fetch address details screen", zap.String(logger.LOAN_REQUEST_ID, req.GetLoanRequestId()))
			res.GetRespHeader().Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
		}
		return res, nil
	case deeplinkPb.Screen_LOANS_MULTIPLE_OFFER_SELECTION_SCREEN:
		var lopts []*preapprovedloanPbBe.LoanOption
		var activeLr *preapprovedloanPbBe.LoanRequest
		if apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.genconf.Lending().UseLandingDataV3()) {
			beRes, beErr := s.preApprovedLoanClient.GetLandingInfoV3(ctx, &preapprovedloanPbBe.GetLandingInfoV3Request{
				ActorId:    req.GetReq().GetAuth().GetActorId(),
				LoanHeader: helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
			})
			if beErr != nil {
				logger.Error(ctx, "preApprovedLoanClient.GetLandingInfoV3 BE API failed", zap.Error(beErr))
				res.RespHeader.Status = rpcPb.StatusInternal()
				res.RespHeader.ErrorView = defaultErrorView
				return res, nil
			}
			lopts = beRes.GetLoanOptions()
			for _, lr := range beRes.GetLoanRequestInfos() {
				if lr.GetLr().GetCompletedAt() == nil {
					activeLr = lr.GetLr()
					break
				}
			}
		} else {
			beRes, beErr := s.preApprovedLoanClient.GetLandingInfoV2(ctx, &preapprovedloanPbBe.GetLandingInfoV2Request{
				ActorId: req.GetReq().GetAuth().GetActorId(),
			})
			if beErr = epifigrpc.RPCError(beRes, beErr); beErr != nil {
				logger.Error(ctx, "preApprovedLoanClient.GetLandingInfoV2 BE API failed", zap.Error(beErr))
				res.RespHeader.Status = rpcPb.StatusInternal()
				res.RespHeader.ErrorView = defaultErrorView
				return res, nil
			}
			lopts = beRes.GetLoanOptions()
			activeLr = beRes.GetActiveLoanRequest()
		}
		dl, _, err := s.aggregatedDeeplinkProvider.GetMultiOfferScreenWithSupportedOffers(ctx, lopts, activeLr, req.GetReq().GetAuth().GetActorId())
		if err != nil || dl == nil {
			if err != nil {
				logger.Error(ctx, "error in GetMultiOfferScreenWithSupportedOffers", zap.Error(err))
			} else {
				logger.Error(ctx, "got nil deeplink from GetMultiOfferScreenWithSupportedOffers")
			}
			res.RespHeader.Status = rpcPb.StatusFailedPrecondition()
			res.RespHeader.ErrorView = noOtherOfferErrorView
			return res, nil
		}
		res.Deeplink = dl
		return res, nil
	default:
		logger.Error(ctx, "unsupported screen", zap.String("Screen", req.GetScreen().String()))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}
}

func (s *Service) GetLivenessStatus(ctx context.Context, req *palFePb.GetLivenessStatusRequest) (*palFePb.GetLivenessStatusResponse, error) {
	helper.LogIfLoanHeaderNotPresent(ctx, req.GetLoanHeader(), "GetLivenessStatus")
	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}
	if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		req.GetLoanHeader().LoanProgram = palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram)
	}
	startTime := time.Now()
	s.PushApiTriggeredEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGetLivenessStatus, "", req.GetLoanRequestId(),
		palEvents.LoanApplicationFlow, getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())
	res := &palFePb.GetLivenessStatusResponse{
		RespHeader: &header.ResponseHeader{},
	}
	beRes, err := s.preApprovedLoanClient.GetLivenessStatus(ctx, &preapprovedloanPbBe.GetLivenessStatusRequest{
		LoanRequestId: req.GetLoanRequestId(),
		ActorId:       req.GetReq().GetAuth().GetActorId(),
		LoanHeader:    helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
	})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		logger.Error(ctx, "preApprovedLoanClient.GetLivenessStatus BE API failed",
			zap.Error(err),
		)
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}
	deeplinkProvider := s.getDeeplinkProvider(ctx, helper.GetPalFeVendorFromBe(beRes.GetLoanRequest().GetVendor()), helper.GetFeLoanProgramFromBe(beRes.GetLoanRequest().GetLoanProgram()))
	// when in case liveness service returns' liveness passed, but loan request substatus is still in liveness pending in progress,
	// user was going to liveness is pending screen right after speaking the OTP, which was a bad user experience
	if beRes.GetLivenessAttempt().GetStatus() == livenessPb.LivenessStatus_LIVENESS_PASSED &&
		beRes.GetLoanRequest().GetSubStatus() != preapprovedloanPbBe.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_LIVENESS &&
		beRes.GetLoanRequest().GetSubStatus() != preapprovedloanPbBe.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_LIVENESS_IN_PROGRESS {
		dl := baseprovider.CheckLivenessScreenDeeplink(beRes.GetLivenessAttempt())
		nextAction, dlErr := getStatusPollScreen(ctx, deeplinkProvider, beRes.GetLoanRequest().GetId())
		if dlErr != nil {
			logger.Error(ctx, "error while generating application status poll deeplink", zap.Error(dlErr))
			return nil, fmt.Errorf("error while generating application status poll deeplink: %w", dlErr)
		}
		res.Deeplink = nextAction
		s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGetLivenessStatus, time.Since(startTime), palEvents.StatusSuccess, palEvents.SubStatusLivenessPassed, beRes.GetLoanRequest().GetOfferId(), beRes.GetLoanRequest().GetId(), palEvents.LoanApplicationFlow, palEvents.VendorFederal, req.GetLoanHeader().GetEventData())
		res.Deeplink = dl
		res.RespHeader.Status = rpcPb.StatusOk()
		return res, nil
	}
	s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGetLivenessStatus, time.Since(startTime), palEvents.StatusSuccess, palEvents.SubStatusLivenessChecked,
		beRes.GetLoanRequest().GetOfferId(), beRes.GetLoanRequest().GetId(), palEvents.LoanApplicationFlow, getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())
	res.Deeplink = baseprovider.CheckLivenessScreenDeeplink(beRes.GetLivenessAttempt())
	res.GetDeeplink().GetCheckLivenessScreenOptions().NextAction = &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_CHECK_LIVENESS}
	s.populateLODropOffFeedbackIdentifierInRespHeaderIfEnabled(res.GetRespHeader())
	res.RespHeader.Status = rpcPb.StatusOk()
	return res, nil
}

// RecordConsent deprecated, please use RecordConsents
func (s *Service) RecordConsent(ctx context.Context, actorId string, device *commontypes.Device, consentType consentPb.ConsentType) error {
	res, err := s.consentClient.RecordConsent(ctx, &consentPb.RecordConsentRequest{
		ConsentType: consentType,
		Version:     0,
		Actor: &types.Actor{
			Id: actorId,
		},
		Device:  device,
		ActorId: actorId,
		Owner:   commontypes.Owner_OWNER_EPIFI_TECH,
	})
	if te := epifigrpc.RPCError(res, err); te != nil {
		return errors.Wrap(te, "error while calling RecordConsent")
	}
	return nil
}

func (s *Service) RecordConsents(ctx context.Context, actorId string, device *commontypes.Device, consentTypes []consentPb.ConsentType, identifier string) error {
	var cts []*consentPb.ConsentRequestInfo
	for _, ct := range consentTypes {
		cts = append(cts, &consentPb.ConsentRequestInfo{
			ConsentType: ct,
			Version:     0,
			ClientReqId: uuid.New().String(),
		})
	}

	res, err := s.consentClient.RecordConsents(ctx, &consentPb.RecordConsentsRequest{
		Device:   device,
		ActorId:  actorId,
		Consents: cts,
		Owner:    commontypes.Owner_OWNER_EPIFI_TECH,
	})
	if te := epifigrpc.RPCError(res, err); te != nil {
		return errors.Wrap(te, "error while calling RecordConsents")
	}
	return nil
}

func (s *Service) PushApiTriggeredEvent(ctx context.Context, actorId string, apiName string, offerId string, loanRequestId string, flowName string, ownership string, eventData *palFeEnumsPb.EventData) {
	// ownership = getEventOwnership(ctx, ownership)
	goroutine.Run(ctx, 10*time.Second, func(ctx context.Context) {
		s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), palEvents.NewApiTriggeredEvent(actorId, apiName, offerId, loanRequestId, flowName, ownership, eventData.GetEntryPoint().String(), eventData.GetEntryPointV2(), eventData.GetComponentIdentifier()))
	})
}

func (s *Service) PushApiResponseEvent(ctx context.Context, actorId string, apiName string, timeTaken time.Duration, status string, substatus string, offerId string, loanRequestId string, flowName string, ownership string, eventData *palFeEnumsPb.EventData) {
	// ownership = getEventOwnership(ctx, ownership)
	goroutine.Run(ctx, 10*time.Second, func(ctx context.Context) {
		s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), palEvents.NewApiResponseEvent(actorId, apiName, timeTaken, status, substatus, offerId, loanRequestId, flowName, ownership, eventData.GetEntryPoint().String(), eventData.GetEntryPointV2(), eventData.GetComponentIdentifier()))
		s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), palEvents.NewSafeApiResponseEvent(actorId, apiName, timeTaken, status, substatus, flowName, ownership))
	})
}

func (s *Service) PushLoanApplicationFlowEvent(ctx context.Context, actorId string, ownership string, activity string, timeTaken time.Duration, loanRequestId string, status string, subStatus string, eventParams interface{}) {
	goroutine.Run(ctx, 10*time.Second, func(ctx context.Context) {
		s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx),
			palEvents.NewLoanApplicationFlowEvent(actorId, ownership, activity, timeTaken, loanRequestId, status, subStatus, eventParams))
	})
}

func (s *Service) CancelApplication(ctx context.Context, req *palFePb.CancelApplicationRequest) (*palFePb.CancelApplicationResponse, error) {
	helper.LogIfLoanHeaderNotPresent(ctx, req.GetLoanHeader(), "CancelApplication")
	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}
	if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		req.GetLoanHeader().LoanProgram = palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram)
	}
	startTime := time.Now()
	s.PushApiTriggeredEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameCancelApplication, "", req.GetLoanRequestId(), palEvents.LoanApplicationFlow, palEvents.VendorFederal, req.GetLoanHeader().GetEventData())

	res := &palFePb.CancelApplicationResponse{
		RespHeader: &header.ResponseHeader{},
	}
	beRes, err := s.preApprovedLoanClient.CancelApplication(ctx, &preapprovedloanPbBe.CancelApplicationRequest{
		LoanRequestId: req.GetLoanRequestId(),
		ActorId:       req.GetReq().GetAuth().GetActorId(),
		LoanHeader:    helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
	})
	if beRes.GetStatus().IsPermissionDenied() {
		s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameCancelApplication, time.Since(startTime), palEvents.StatusPermissionDenied, "",
			beRes.GetOfferId(), req.GetLoanRequestId(), palEvents.LoanApplicationFlow, getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())
		res.RespHeader.Status = rpcPb.StatusPermissionDenied()
		platform, _ := epificontext.AppPlatformAndVersion(ctx)
		if platform == commontypes.Platform_ANDROID {
			res.RespHeader.ErrorView = getApplicationNonCancellableFullSheetErrorView()
		} else {
			res.RespHeader.ErrorView = getApplicationNonCancellableBottomSheetErrorView()
		}
		return res, nil
	}
	if beRes.GetStatus().GetCode() == rpcPb.StatusFailedPrecondition().GetCode() {
		s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameCancelApplication, time.Since(startTime), palEvents.StatusFailed, "",
			beRes.GetOfferId(), req.GetLoanRequestId(), palEvents.LoanApplicationFlow, getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		logger.Error(ctx, "preApprovedLoanClient.CancelApplication BE API failed", zap.Error(err))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}
	s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameCancelApplication, time.Since(startTime), palEvents.StatusSuccess, "",
		beRes.GetOfferId(), req.GetLoanRequestId(), palEvents.LoanApplicationFlow, getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())
	deeplinkProvider := s.getDeeplinkProvider(ctx, req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())
	res.Deeplink = deeplinkProvider.GetCancelApplicationScreenDeeplink(ctx, deeplinkProvider.GetLoanHeader(), beRes.GetOfferId(), beRes.GetLoanOffer())
	res.RespHeader.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) InitiateESign(ctx context.Context, req *palFePb.InitiateESignRequest) (*palFePb.InitiateESignResponse, error) {
	helper.LogIfLoanHeaderNotPresent(ctx, req.GetLoanHeader(), "InitiateESign")
	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}
	if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		req.GetLoanHeader().LoanProgram = palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram)
	}
	startTime := time.Now()
	s.PushApiTriggeredEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameInitiateESign, "", req.GetLoanRequestId(), palEvents.LoanApplicationFlow, getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())
	res := &palFePb.InitiateESignResponse{
		RespHeader: &header.ResponseHeader{},
	}

	beRes, err := s.preApprovedLoanClient.InitiateESign(ctx, &preapprovedloanPbBe.InitiateESignRequest{
		LoanRequestId: req.GetLoanRequestId(),
		ActorId:       req.GetReq().GetAuth().GetActorId(),
		LoanHeader:    helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
	})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		logger.Error(ctx, "preApprovedLoanClient.InitiateESign BE API failed", zap.Error(err))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}
	deeplinkProvider := s.getDeeplinkProvider(ctx, req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())
	s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameInitiateESign, time.Since(startTime), palEvents.StatusSuccess, "",
		"", req.GetLoanRequestId(), palEvents.LoanApplicationFlow, getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())
	res.SignUrl = beRes.GetSignUrl()
	res.ExitUrl = beRes.GetExitUrl()
	dl, dlErr := getStatusPollScreen(ctx, deeplinkProvider, req.GetLoanRequestId())
	if dlErr != nil {
		logger.Error(ctx, "error while generating application status poll deeplink", zap.Error(dlErr))
		return nil, fmt.Errorf("error while generating application status poll deeplink: %w", dlErr)
	}
	res.NextStep = dl
	res.RespHeader.Status = rpcPb.StatusOk()

	s.populateLODropOffFeedbackIdentifierInRespHeaderIfEnabled(res.GetRespHeader())

	return res, nil
}

func (s *Service) GetLoanDetails(ctx context.Context, req *palFePb.GetLoanDetailsRequest) (*palFePb.GetLoanDetailsResponse, error) {
	helper.LogIfLoanHeaderNotPresent(ctx, req.GetLoanHeader(), "GetLoanDetails")
	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}
	if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		req.GetLoanHeader().LoanProgram = palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram)
	}
	startTime := time.Now()
	s.PushApiTriggeredEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGetLoanDetails, "", req.GetLoanId(),
		palEvents.LoanManagementFlow, getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())
	res := &palFePb.GetLoanDetailsResponse{
		RespHeader: &header.ResponseHeader{},
	}
	getDowntimeStatusRes, err := s.preApprovedLoanClient.GetDowntimeStatus(ctx, &preapprovedloanPbBe.GetDowntimeStatusRequest{
		LoanHeader: helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
	})
	if err = epifigrpc.RPCError(getDowntimeStatusRes, err); err != nil {
		logger.Error(ctx, "preApprovedLoanClient.GetDowntimeStatus BE API failed", zap.Error(err))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}

	// TODO(@prasoon): to move to v2
	if req.GetLoanHeader().GetVendor() == palFeEnumsPb.Vendor_MONEYVIEW {
		dlProvider := s.getDeeplinkProvider(ctx, helper.GetPalFeVendorFromBe(helper.GetBeVendorFromFe(req.GetLoanHeader().GetVendor())), req.GetLoanHeader().GetLoanProgram())
		dl, dlErr := dlProvider.GetLoanDetailsScreenDeeplink(ctx, dlProvider.GetLoanHeader(), &preapprovedloanPbBe.GetLoanDetailsResponse{
			LoanAccount: &preapprovedloanPbBe.LoanAccount{
				Id: req.GetLoanId(),
			},
		}, getDowntimeStatusRes, nil)
		if dlErr != nil {
			logger.Error(ctx, "failed to get loan details screen deeplink", zap.Error(dlErr))
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
		res.Deeplink = dl
		res.RespHeader.Status = rpcPb.StatusOk()
		return res, nil
	}

	dlProvider := s.getDeeplinkProvider(ctx, helper.GetPalFeVendorFromBe(helper.GetBeVendorFromFe(req.GetLoanHeader().GetVendor())), req.GetLoanHeader().GetLoanProgram())

	beRes, beResErr := s.preApprovedLoanClient.GetLoanDetails(ctx, &preapprovedloanPbBe.GetLoanDetailsRequest{
		ActorId:    req.GetReq().GetAuth().GetActorId(),
		LoanId:     req.GetLoanId(),
		LoanHeader: helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
	})
	if te := epifigrpc.RPCError(beRes, beResErr); te != nil {
		logger.Error(ctx, "preApprovedLoanClient.GetLoanDetails BE API failed", zap.Error(te))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}

	var dl *deeplinkPb.Deeplink
	var dlErr error

	if s.isPostDisbursalFlowEnabled(ctx, req.GetReq().GetAuth().GetActorId(), req.GetLoanHeader()) {
		displayAccountNumber := getDisplayLoanAccountNumber(beRes.GetLoanAccount())
		dl, dlErr = dlProvider.GetLoanDetailsScreenDeeplinkV2(ctx, dlProvider.GetLoanHeader(), beRes,
			getDowntimeStatusRes, s.genconf.Lending().PreApprovedLoan().PrePayConfig(), &provider.GetLoanDetailsScreenDeeplinkV2Request{
				DisplayAccountNumber: displayAccountNumber})
		if dlErr != nil {
			logger.Error(ctx, "failed to get loan details screen deeplink v2", zap.Error(dlErr))
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
		s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGetLoanDetails, time.Since(startTime), palEvents.StatusSuccess, "",
			beRes.GetLoanRequest().GetOfferId(), beRes.GetLoanRequest().GetId(), palEvents.LoanManagementFlowV2, getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())
	} else {
		// adding this for backward compatibility
		dl, dlErr = dlProvider.GetLoanDetailsScreenDeeplink(ctx, dlProvider.GetLoanHeader(), beRes,
			getDowntimeStatusRes, s.genconf.Lending().PreApprovedLoan().PrePayConfig())
		if s.isLAMFNewPrepayEnabled(ctx, req.GetLoanHeader()) {
			dl, dlErr = dlProvider.GetLoanDetailsScreenDeeplinkWithAccountSelection(ctx, &provider.GetLoanDetailsScreenDeeplinkWithAccountSelectionRequest{
				Lh:                         req.GetLoanHeader(),
				BeRes:                      beRes,
				PrePayConfigMap:            s.genconf.Lending().PreApprovedLoan().PrePayConfig(),
				ShowAccountSelectionScreen: true,
			})
		} else if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_LAMF {
			platform, _ := epificontext.AppPlatformAndVersion(ctx)
			dl, dlErr = dlProvider.GetLoanDetailsScreenDeeplinkWithAccountSelection(ctx, &provider.GetLoanDetailsScreenDeeplinkWithAccountSelectionRequest{
				Lh:               req.GetLoanHeader(),
				BeRes:            beRes,
				PrePayConfigMap:  s.genconf.Lending().PreApprovedLoan().PrePayConfig(),
				ShowUpdateScreen: true,
				Platform:         platform,
			})
		}
		if dlErr != nil {
			logger.Error(ctx, "failed to get loan details screen deeplink", zap.Error(dlErr))
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
		baseprovider.UpdateLoanDetailsDeeplinkForAltAccFlow(ctx, req.GetLoanHeader(), dl, beRes)
		if !(s.conf.Lending.PreApprovedLoan.PrePay.Enabled) {
			res.GetDeeplink().GetPreApprovedLoanDetailsScreenOptions().GetPrePayTile().GetCta().Text += " (Coming Soon)"
			res.GetDeeplink().GetPreApprovedLoanDetailsScreenOptions().GetPrePayTile().GetCta().Status = deeplinkPb.Cta_CTA_STATUS_DISABLED
		}
		s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGetLoanDetails, time.Since(startTime), palEvents.StatusSuccess, "",
			beRes.GetLoanRequest().GetOfferId(), beRes.GetLoanRequest().GetId(), palEvents.LoanManagementFlow, getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())
	}

	res.Deeplink = dl
	res.RespHeader.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) isLAMFNewPrepayEnabled(ctx context.Context, lh *palFeEnumsPb.LoanHeader) bool {
	if lh.GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_LAMF && lh.GetVendor() == palFeEnumsPb.Vendor_FIFTYFIN &&
		apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.genconf.Flags().EnableLAMFPrePayV2()) {
		return true
	}
	return false
}

// nolint: funlen
func (s *Service) GetPrePayDetails(ctx context.Context, req *palFePb.GetPrePayDetailsRequest) (*palFePb.GetPrePayDetailsResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()

	helper.LogIfLoanHeaderNotPresent(ctx, req.GetLoanHeader(), "GetPrePayDetails")
	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}
	if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		req.GetLoanHeader().LoanProgram = palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram)
	}
	startTime := time.Now()
	// TODO (naresh) : loanRequestId correct ?
	s.PushApiTriggeredEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGetPrePayDetails, "", req.GetLoanId(), palEvents.LoanRepaymentFlow, getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())
	res := &palFePb.GetPrePayDetailsResponse{
		RespHeader: &header.ResponseHeader{},
	}

	// for lamf however, the screen is shown if pre-close flow is live.
	// for other loan programs, show prepay v2 screen if new post-disbursal flow is live.
	if (s.isPostDisbursalFlowEnabled(ctx, req.GetReq().GetAuth().GetActorId(), req.GetLoanHeader()) && baseprovider.IsPrePayV2Enabled(ctx, s.genconf.Lending().PreApprovedLoan().PrePay().V2Config())) ||
		(req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_LAMF && s.genconf.Lending().SecuredLoanParams().EnablePreCloseFlow()) {
		return s.getPrePayDetailsV2(ctx, req)
	}

	beRes, err := s.preApprovedLoanClient.GetLoanDetails(ctx, &preapprovedloanPbBe.GetLoanDetailsRequest{
		ActorId:    actorId,
		LoanId:     req.GetLoanId(),
		LoanHeader: helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
	})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		logger.Error(ctx, "preApprovedLoanClient.GetLoanDetails for PrePay Details BE API failed", zap.Error(err))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}

	// not allowing prepayment if the pre-closure amount is zero/unknown as max allowed prepay amount calculation is dependent on the foreclosure amount,
	// negative foreclosure amount denotes that loan was overpaid, so not allowing prepayments in that case as well.
	if !moneyPb.IsPositive(beRes.GetPreClosureDetails().GetLoanPreCloseAmount()) {
		logger.Error(ctx, "not allowing prepayment as the pre-closure amount is zero/unknown", zap.String(logger.LOAN_ACCOUNT_ID, req.GetLoanId()), zap.String(logger.ACTOR_ID_V2, actorId), zap.Any("foreclosureAmt", beRes.GetPreClosureDetails().GetLoanPreCloseAmount()))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = prePayNotAllowedBottomSheetErrorView
	}

	outStandingAmount, err := moneyPb.ToPaise(beRes.GetLoanAccount().GetLoanAmountInfo().GetOutstandingAmount())
	if err != nil {
		logger.Error(ctx, "error getting outStandingAmount", zap.Error(err))
	}
	emiAmount, err := moneyPb.ToPaise(beRes.GetLoanRequest().GetDetails().GetLoanInfo().GetEmiAmount())
	if err != nil {
		logger.Error(ctx, "error getting emi Amount", zap.Error(err))
	}
	if req.GetAmount() == nil || moneyPb.IsZero(req.GetAmount().GetBeMoney()) {
		if outStandingAmount <= emiAmount {
			req.Amount = &types.Money{
				CurrencyCode: "INR",
				Units:        (outStandingAmount + (baseprovider.GetMinPrePayAmountInUnits() * 100)) / 200,
			}
		} else {
			req.Amount = types.GetFromBeMoney(helper.CeilMoney(moneyPb.FromPaisa(emiAmount)))
		}
	}

	getLoanDefaultDetailsRes, err := s.preApprovedLoanClient.GetLoanDefaultDetails(ctx,
		&preapprovedloanPbBe.GetLoanDefaultDetailsRequest{
			LoanHeader:    helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
			LoanAccountId: req.GetLoanId(),
		})
	if te := epifigrpc.RPCError(getLoanDefaultDetailsRes, err); te != nil {
		logger.Error(ctx, "preApprovedLoanClient.GetLoanDefaultDetails for PrePay Details BE API failed", zap.Error(err))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}
	loanDefaultDetails := getLoanDefaultDetailsRes.GetDefaultDetails()

	s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGetPrePayDetails, time.Since(startTime),
		palEvents.StatusSuccess, palEvents.SubStatusPrePay, beRes.GetLoanOffer().GetId(), beRes.GetLoanRequest().GetId(), palEvents.LoanRepaymentFlow,
		getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())

	shouldFetchUserAccounts := helper.ShouldFetchUserAccountsForTpapPrePaymentFlow(ctx, req.GetLoanHeader(), s.genconf.Lending().PreApprovedLoan().PrePayConfig())

	// taking ceil amount for pre-closure to solve for slider when max amount is in decimals
	if beRes.GetPreClosureDetails() != nil {
		beRes.GetPreClosureDetails().LoanPreCloseAmount = moneyPb.NewMoney(beRes.GetPreClosureDetails().GetLoanPreCloseAmount()).Ceil()
	}
	deeplinkProvider := s.getDeeplinkProvider(ctx, helper.GetPalFeVendorFromBe(beRes.GetLoanAccount().GetVendor()), req.GetLoanHeader().GetLoanProgram())
	prePayDeeplink, err := deeplinkProvider.GetPrePayDetailsScreenDeeplink(ctx, req.GetLoanHeader(), beRes, loanDefaultDetails, req.GetAmount(), req.GetIsAmountSelectionSkipped(), req.GetIsPreClose(), shouldFetchUserAccounts)
	if err != nil {
		logger.Error(ctx, "error getting deeplink for prepay details", zap.Error(err))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}

	res.Deeplink = prePayDeeplink
	res.RespHeader.Status = rpcPb.StatusOk()
	return res, nil
}
func (s *Service) GetProgressiveDisclosureFormFields(ctx context.Context, req *palFePb.GetProgressiveDisclosureFormFieldsRequest) (*palFePb.GetProgressiveDisclosureFormFieldsResponse, error) {
	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}
	if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		req.GetLoanHeader().LoanProgram = palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram)
	}

	resp := &palFePb.GetProgressiveDisclosureFormFieldsResponse{
		RespHeader: &header.ResponseHeader{},
	}
	deeplinkProvider := s.getDeeplinkProvider(ctx, req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())
	fieldType := req.GetFieldId()
	minAmount := &types.Money{CurrencyCode: "INR", Units: 0}
	maxAmountIncomeAndRent := &types.Money{CurrencyCode: "INR", Units: 10000000}
	maxAmountAnnualRevenue := &types.Money{CurrencyCode: "INR", Units: 100000000}
	switch fieldType {
	case palEnumsPb.FieldId_ADDRESS_TYPE.String():
		switch req.GetSelectedOptionId() {
		case types.ResidenceType_RESIDENCE_TYPE_RENTED.String(), types.ResidenceType_RESIDENCE_TYPE_PAYING_GUEST.String():
			amountField := getAmountCompositeField(getAmountCompositeFieldRequest{deeplinkProvider, palEnumsPb.FieldId_RENT.String(), "Monthly Rent", "HOW MUCH RENT DO YOU PAY MONTHLY", "RENT", "Please enter your monthly rent", minAmount, maxAmountIncomeAndRent, req.GetLoanHeader(), "/m"})
			resp.AdditionalFormFields = append(resp.AdditionalFormFields, amountField)
		default:
			// do nothing
		}
	case palEnumsPb.FieldId_EMPLOYMENT_TYPE.String():
		switch req.GetSelectedOptionId() {
		case types.EmploymentType_EMPLOYMENT_TYPE_SALARIED.String():
			minAmount = &types.Money{CurrencyCode: "INR", Units: 1}
			employerNameField := getEmployerNameFormField("EMPLOYER'S NAME", "Employer's name")
			// workEmailField := getStringCompositeField("ENTER YOUR WORK EMAIL", "Work email", palEnumsPb.FieldId_WORK_EMAIL.String(), false)
			income := getAmountCompositeField(getAmountCompositeFieldRequest{deeplinkProvider, palEnumsPb.FieldId_MONTHLY_INCOME.String(), "Monthly Income", "ENTER YOUR MONTHLY INCOME", "INCOME", "Please enter your monthly income", minAmount, maxAmountIncomeAndRent, req.GetLoanHeader(), "/m"})
			resp.AdditionalFormFields = append(resp.AdditionalFormFields, employerNameField, income)
		case types.EmploymentType_EMPLOYMENT_TYPE_SELF_EMPLOYED.String():
			minAmount = &types.Money{CurrencyCode: "INR", Units: 1}
			annualRevenue := getAmountCompositeField(getAmountCompositeFieldRequest{deeplinkProvider, palEnumsPb.FieldId_ANNUAL_REVENUE.String(), "Yearly Revenue", "ENTER REVENUE EARNED IN A YEAR", "YEARLY REVENUE", "Please enter revenue earned in a year", minAmount, maxAmountAnnualRevenue, req.GetLoanHeader(), ""})
			// This data is not being used in the business logic, removing this in the hope of improving the funnel conversion
			// GSTIN := getStringCompositeField("GSTIN", "Enter your GSTIN (optional)", palEnumsPb.FieldId_GSTIN.String(), true)
			organizationNameField := getEmployerNameFormField("ORGANIZATION NAME", "Organization Name")
			resp.AdditionalFormFields = append(resp.AdditionalFormFields, organizationNameField, annualRevenue)
		default:
			if req.GetSelectedOptionId() == types.EmploymentType_EMPLOYMENT_TYPE_OTHERS.String() {
				minAmount = &types.Money{CurrencyCode: "INR", Units: 1}
			}
			income := getAmountCompositeField(getAmountCompositeFieldRequest{deeplinkProvider, palEnumsPb.FieldId_MONTHLY_INCOME.String(), "Monthly Income", "ENTER YOUR MONTHLY INCOME", "INCOME", "Please enter your monthly income", minAmount, maxAmountIncomeAndRent, req.GetLoanHeader(), "/m"})
			resp.AdditionalFormFields = append(resp.AdditionalFormFields, income)
		}
	default:
		return nil, fmt.Errorf("invalid field Id %s", fieldType)
	}
	resp.RespHeader.Status = rpcPb.StatusOk()
	return resp, nil
}

type getAmountCompositeFieldRequest struct {
	deeplinkProvider            provider.IDeeplinkProvider
	fieldType                   string
	hintText                    string
	labelText                   string
	titleIncomeSelectionScreen  string
	bottomIncomeSelectionScreen string
	minAmount                   *types.Money
	maxAmount                   *types.Money
	loanHeader                  *palFeEnumsPb.LoanHeader
	displayPerMonthOrYear       string
}

func getEmployerNameFormField(label string, hint string) *preapprovedloans.CompositeFormField {
	return &preapprovedloans.CompositeFormField{
		FieldType: palEnumsPb.FieldId_EMPLOYER_NAME.String(),
		Field: &preapprovedloans.CompositeFormField_SelectableFormField_{
			SelectableFormField: &preapprovedloans.CompositeFormField_SelectableFormField{},
		},
		BorderColor:      "#D6D9DD",
		Label:            commontypes.GetTextFromStringFontColourFontStyle(label, "#A4A4A4", commontypes.FontStyle_OVERLINE_XS_CAPS),
		Hint:             commontypes.GetTextFromStringFontColourFontStyle(hint, "#929599", commontypes.FontStyle_SUBTITLE_M),
		EnteredTextStyle: commontypes.GetTextFromStringFontColourFontStyle("", "#313234", commontypes.FontStyle_SUBTITLE_M),
	}
}

func getStringCompositeField(labelName string, hintText string, fieldType string, isOptional bool) *preapprovedloans.CompositeFormField {
	req := &preapprovedloans.CompositeFormField{
		Field: &preapprovedloans.CompositeFormField_StringInputFormField_{
			StringInputFormField: &preapprovedloans.CompositeFormField_StringInputFormField{
				DefaultValue: "",
			},
		},
		FieldType:               fieldType,
		BorderColor:             "#D6D9DD",
		IsOptional:              isOptional,
		Label:                   commontypes.GetTextFromStringFontColourFontStyle(labelName, "#A4A4A4", commontypes.FontStyle_OVERLINE_XS_CAPS),
		AdditionalFetchRequired: false,
		EnteredTextStyle:        commontypes.GetTextFromStringFontColourFontStyle("", "#313234", commontypes.FontStyle_SUBTITLE_M),
		Hint:                    commontypes.GetTextFromStringFontColourFontStyle(hintText, "#929599", commontypes.FontStyle_SUBTITLE_M),
	}

	return req
}

func getAmountCompositeField(req getAmountCompositeFieldRequest) *preapprovedloans.CompositeFormField {
	borderColor := "#D6D9DD"
	res := &preapprovedloans.CompositeFormField{
		Field: &preapprovedloans.CompositeFormField_AmountSelectionFormField_{
			AmountSelectionFormField: &preapprovedloans.CompositeFormField_AmountSelectionFormField{
				DefaultAmount:    nil,
				MinAmount:        req.minAmount,
				MaxAmount:        req.maxAmount,
				EditIcon:         nil,
				IsAmountEditable: true,
				Deeplink: req.deeplinkProvider.GetIncomeSelectionDeeplink(createIncomeSelectionDeeplinkRequest(req.loanHeader,
					&types.Money{CurrencyCode: "INR"}, req.titleIncomeSelectionScreen, req.bottomIncomeSelectionScreen, req.minAmount, req.maxAmount, req.displayPerMonthOrYear)),
			},
		},
		FieldType:               req.fieldType,
		BorderColor:             borderColor,
		IsOptional:              false,
		Label:                   commontypes.GetTextFromStringFontColourFontStyle(req.labelText, "#A4A4A4", commontypes.FontStyle_OVERLINE_XS_CAPS),
		AdditionalFetchRequired: false,
		EnteredTextStyle:        commontypes.GetTextFromStringFontColourFontStyle("", "#313234", commontypes.FontStyle_SUBTITLE_M),
		Hint:                    commontypes.GetTextFromStringFontColourFontStyle(req.hintText, "#929599", commontypes.FontStyle_SUBTITLE_M),
	}
	return res
}

func (s *Service) PrePayLoan(ctx context.Context, req *palFePb.PrePayLoanRequest) (*palFePb.PrePayLoanResponse, error) {
	helper.LogIfLoanHeaderNotPresent(ctx, req.GetLoanHeader(), "PrePayLoan")
	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}
	if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		req.GetLoanHeader().LoanProgram = palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram)
	}
	actorId := req.GetReq().GetAuth().GetActorId()
	startTime := time.Now()
	// TODO (naresh) : loanRequestId correct ?
	s.PushApiTriggeredEvent(ctx, actorId, palEvents.ApiNamePrePayLoan, "", req.GetLoanId(), palEvents.LoanRepaymentFlow, getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())

	res := &palFePb.PrePayLoanResponse{
		RespHeader: &header.ResponseHeader{},
	}

	if moneyPb.IsZero(req.GetAmount().GetBeMoney()) {
		logger.Error(ctx, "preApprovedLoanClient.PrePayLoan for cannot prepay for zero amount")
		res.RespHeader.Status = rpcPb.StatusInvalidArgument()
		res.RespHeader.ErrorView = prepayAmountIsZeroBottomSheetErrorView
		return res, nil
	}

	var payerIdentifier *preapprovedloanPbBe.PrePayLoanRequest_UserIdentifier
	// convert derived account id to user identifier
	if req.GetDerivedAccountId() != "" {
		identifier, payerErr := getBeUserIdentifierForDerivedAccId(req.GetDerivedAccountId(), actorId, req.GetAccountType())
		if payerErr != nil {
			logger.Error(ctx, "error deriving BE user identifier from derived account id", zap.Error(payerErr))
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
		payerIdentifier = identifier
	}

	isPrePayViaPgEnabled := s.isPrePayViaPgEnabled(ctx, actorId, req.GetLoanHeader())
	// todo (team) : remove this log after pg testing
	logger.Info(ctx, "isPrePayViaPgEnabled", zap.Bool("isPrePayViaPgEnabled", isPrePayViaPgEnabled))
	beRes, err := s.preApprovedLoanClient.PrePayLoan(ctx, &preapprovedloanPbBe.PrePayLoanRequest{
		ActorId:             actorId,
		LoanId:              req.GetLoanId(),
		Amount:              req.GetAmount().GetBeMoney(),
		LoanHeader:          helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
		IsPreClose:          req.GetIsPreClose(),
		PayerUserIdentifier: payerIdentifier,
		UsePgForPrepay:      isPrePayViaPgEnabled,
		AccountType:         helper.FeToBeLoanAccountType[req.GetLoanAccountTypeIdentifier()],
	})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		logger.Error(ctx, "preApprovedLoanClient.PrePayLoan for PrePay Loan BE API failed", zap.Error(te))
		switch beRes.GetStatus().GetCode() {
		case uint32(preapprovedloanPbBe.PrePayLoanResponse_KNOWN_ERROR_FROM_PAY):
			res.RespHeader.Status = rpcPb.StatusFailedPrecondition()
			res.RespHeader.ErrorView = payerrorcode.GetBottomSheetCardErrorView(beRes.GetPayErrorCodeForPayer())
		case uint32(preapprovedloanPbBe.PrePayLoanResponse_FAILED_PRECONDITION_EXISTING_PAYMENT_IN_PROGRESS):
			res.RespHeader.Status = rpcPb.StatusFailedPrecondition()
			res.RespHeader.ErrorView = prePayNotAllowedExistingPaymentInProgressErrorView
		case uint32(preapprovedloanPbBe.PrePayLoanResponse_FAILED_PRECONDITION_PRE_CLOSURE_BLACKOUT_PERIOD):
			res.RespHeader.Status = rpcPb.StatusFailedPrecondition()
			res.RespHeader.ErrorView = s.getDeeplinkProvider(ctx, req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()).GetPreClosureBlackoutPeriodBottomSheetErrorView()
		case rpcPb.StatusFailedPrecondition().GetCode(), uint32(preapprovedloanPbBe.PrePayLoanResponse_FAILED_PRECONDITION_PREPAY_BLACKOUT_PERIOD),
			uint32(preapprovedloanPbBe.PrePayLoanResponse_EXPECTED_DELAY_IN_LMS_UPDATE_AT_VENDOR):
			res.RespHeader.Status = rpcPb.StatusFailedPrecondition()
			res.RespHeader.ErrorView = prePayNotAllowedBottomSheetErrorView
		default:
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
		}
		return res, nil
	}

	if beRes.GetPaymentUrl() != "" {
		res.Deeplink, err = baseprovider.GetPaymentWebviewPollDeeplink(ctx, req.GetLoanHeader(), beRes.GetPaymentUrl(), beRes.GetLoanPaymentRequestId())
		if err != nil {
			logger.Error(ctx, "error getting pg fund transfer deeplink", zap.Error(err))
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
	} else if isPrePayViaPgEnabled {
		res.Deeplink, err = baseprovider.GetPGFundTransferDeeplink(ctx, req.GetLoanHeader(), req.GetAmount(), beRes, s.payClient)
		if err != nil {
			logger.Error(ctx, "error getting pg fund transfer deeplink", zap.Error(err))
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
		res.ReferenceId = beRes.GetReferenceId()
	} else {
		// TODO(yatin / bhumij): Use the pkg method that pay team will expose to convert Be to Fe txn attribute
		var feTransactionAttribute *feTxnPb.TransactionAttribute

		if beRes.GetTransactionAttribute().GetPaymentProtocol() == paymentPb.PaymentProtocol_UPI {
			feTransactionAttribute, err = convertBeToFeTransactionAttributeForTpapFlow(beRes.GetTransactionAttribute())
			if err != nil {
				logger.Error(ctx, "Unable to convert BE Transaction Attributes to FE Transaction Attributes for Tpap flow", zap.Error(err))
				res.RespHeader.Status = rpcPb.StatusInternal()
				res.RespHeader.ErrorView = defaultErrorView
				return res, nil
			}
		} else {
			feTransactionAttribute, err = convertBeToFeTransactionAttribute(beRes.GetTransactionAttribute())
			if err != nil {
				logger.Error(ctx, "Unable to convert BE Transaction Attributes to FE Transaction Attributes", zap.Error(err))
				res.RespHeader.Status = rpcPb.StatusInternal()
				res.RespHeader.ErrorView = defaultErrorView
				return res, nil
			}
		}

		res.Deeplink = baseprovider.GetLoanActivityStatusPollScreenDeepLink(req.GetLoanHeader(), beRes.GetReferenceId(), 0)
		res.PinRequired = getPinRequired(beRes.GetTransactionAttribute().GetCredRequiredType())
		res.TransactionAttribute = feTransactionAttribute
		res.ReferenceId = beRes.GetReferenceId()
		res.OrderId = beRes.GetOrderId()
	}
	s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNamePrePayLoan, time.Since(startTime), palEvents.StatusSuccess, "",
		"", req.GetLoanId(), palEvents.LoanRepaymentFlow, getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())
	res.RespHeader.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) GetLoanActivityStatus(ctx context.Context, req *palFePb.GetLoanActivityStatusRequest) (*palFePb.GetLoanActivityStatusResponse, error) {
	helper.LogIfLoanHeaderNotPresent(ctx, req.GetLoanHeader(), "GetLoanActivityStatus")
	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}
	if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		req.GetLoanHeader().LoanProgram = palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram)
	}
	startTime := time.Now()
	s.PushApiTriggeredEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGetLoanActivityStatus, "", "", palEvents.LoanRepaymentFlow, getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())

	res := &palFePb.GetLoanActivityStatusResponse{
		RespHeader: &header.ResponseHeader{},
	}

	isLoanDetailsV2Enabled := s.isPostDisbursalFlowEnabled(ctx, req.GetReq().GetAuth().GetActorId(), req.GetLoanHeader())
	// Calling BE RPC
	beRes, err := s.preApprovedLoanClient.GetLoanActivityStatus(ctx, &preapprovedloanPbBe.GetLoanActivityStatusRequest{
		RefId:      req.GetRefId(),
		ActorId:    req.GetReq().GetAuth().GetActorId(),
		LoanHeader: helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
	})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		logger.Error(ctx, "preApprovedLoanClient.GetLoanActivityStatus BE API failed", zap.Error(err))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}

	deeplinkProvider := s.getDeeplinkProvider(ctx, req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())

	switch beRes.GetActivityStatus() {
	case preapprovedloanPbBe.GetLoanActivityStatusResponse_COMPLETED:
		if req.GetActivityType() == palFePb.GetLoanActivityStatusRequest_PRE_CLOSE {
			res.Deeplink, err = deeplinkProvider.GetLoanPreCloseActivityStatusSuccessScreenDeepLink(req.GetLoanHeader(), beRes.GetLoanAccountId(), isLoanDetailsV2Enabled)
			if err != nil {
				logger.Error(ctx, "failed to get loan pre close activity status success screen deeplink", zap.Error(err))
				res.RespHeader.Status = rpcPb.StatusInternal()
				res.RespHeader.ErrorView = defaultErrorView
				return res, nil
			}
		} else {
			res.Deeplink, err = deeplinkProvider.GetLoanPrePayActivityStatusSuccessScreenDeepLink(req.GetLoanHeader(), beRes.GetLoanAccountId(), isLoanDetailsV2Enabled)
			if err != nil {
				logger.Error(ctx, "failed to get loan pre pay activity status success screen deeplink", zap.Error(err))
				res.RespHeader.Status = rpcPb.StatusInternal()
				res.RespHeader.ErrorView = defaultErrorView
				return res, nil
			}
		}
	case preapprovedloanPbBe.GetLoanActivityStatusResponse_FAILED:
		res.Deeplink = baseprovider.GetLoanActivityStatusFailureScreenDeepLink(deeplinkProvider.GetLoanHeader(), beRes.GetLoanAccountId(), isLoanDetailsV2Enabled)
	default:
		if req.GetAttemptNumber() > baseprovider.LoanActivityStatusPollMaxRetryNumber {
			res.Deeplink = deeplinkProvider.GetLoanActivityStatusPendingScreenDeepLink(deeplinkProvider.GetLoanHeader(), beRes.GetLoanAccountId(), isLoanDetailsV2Enabled)
		} else {
			res.Deeplink = baseprovider.GetLoanActivityStatusPollScreenDeepLink(deeplinkProvider.GetLoanHeader(), req.GetRefId(), req.GetAttemptNumber()+1)
		}
	}
	eventFlow := palEvents.LoanRepaymentFlow
	if isLoanDetailsV2Enabled {
		eventFlow = palEvents.LoanRepaymentFlowV2
	}
	s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGetLoanActivityStatus, time.Since(startTime), palEvents.StatusSuccess, "", "", "", eventFlow, getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())

	res.RespHeader.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) GetAllTransactions(ctx context.Context, req *palFePb.GetAllTransactionsRequest) (*palFePb.GetAllTransactionsResponse, error) {
	helper.LogIfLoanHeaderNotPresent(ctx, req.GetLoanHeader(), "GetAllTransactions")
	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}
	if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		req.GetLoanHeader().LoanProgram = palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram)
	}
	startTime := time.Now()
	s.PushApiTriggeredEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGetAllTransactions, "", "", palEvents.LoanManagementFlow, getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())

	res := &palFePb.GetAllTransactionsResponse{
		RespHeader: &header.ResponseHeader{},
	}

	allTxnsRes, err := s.preApprovedLoanClient.GetAllTransactions(ctx, &preapprovedloanPbBe.GetAllTransactionsRequest{
		LoanAccountId: req.GetLoanAccountId(),
		ActorId:       req.GetReq().GetAuth().GetActorId(),
		LoanHeader:    helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
	})
	if te := epifigrpc.RPCError(allTxnsRes, err); te != nil {
		logger.Error(ctx, "preApprovedLoanClient.GetAllTransactions for GetAllTransactions BE API failed", zap.Error(err))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}

	loanDetailsRes, err := s.preApprovedLoanClient.GetLoanDetails(ctx, &preapprovedloanPbBe.GetLoanDetailsRequest{
		ActorId:    req.GetReq().GetAuth().GetActorId(),
		LoanId:     req.GetLoanAccountId(),
		LoanHeader: helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
	})
	if rpcErr := epifigrpc.RPCError(loanDetailsRes, err); rpcErr != nil {
		logger.Error(ctx, "failed to get loan details", zap.Error(rpcErr))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}

	deeplinkProvider := s.getDeeplinkProvider(ctx, req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())

	if s.isPostDisbursalFlowEnabled(ctx, req.GetReq().GetAuth().GetActorId(), req.GetLoanHeader()) {
		s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGetAllTransactions, time.Since(startTime), palEvents.StatusSuccess, "", "", "", palEvents.LoanManagementFlowV2, getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())
		res.Deeplink, err = deeplinkProvider.GetAllTransactionDeeplinkV2(ctx, req.GetLoanHeader(), allTxnsRes, loanDetailsRes)
		if err != nil {
			logger.Error(ctx, "failed to get all transactions deeplink V2", zap.Error(err),
				zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()), zap.String(logger.LOAN_ACCOUNT_ID, req.GetLoanAccountId()))
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
	} else {
		s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGetAllTransactions, time.Since(startTime), palEvents.StatusSuccess, "", "", "", palEvents.LoanManagementFlow, getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())
		res.Deeplink, err = deeplinkProvider.GetAllTransactionDeeplink(ctx, req.GetLoanHeader(), req.GetLoanAccountId(), allTxnsRes, loanDetailsRes)
		if err != nil {
			logger.Error(ctx, "failed to get all transactions deeplink", zap.Error(err),
				zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()), zap.String(logger.LOAN_ACCOUNT_ID, req.GetLoanAccountId()))
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
	}
	res.RespHeader.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) GetTransactionReceipt(ctx context.Context, req *palFePb.GetTransactionReceiptRequest) (*palFePb.GetTransactionReceiptResponse, error) {
	helper.LogIfLoanHeaderNotPresent(ctx, req.GetLoanHeader(), "GetTransactionReceipt")
	startTime := time.Now()
	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}
	if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		req.GetLoanHeader().LoanProgram = palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram)
	}

	s.PushApiTriggeredEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGetTransactionReceipt, "", "", palEvents.LoanManagementFlow,
		getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())

	res := &palFePb.GetTransactionReceiptResponse{
		RespHeader: &header.ResponseHeader{},
	}

	beRes, err := s.preApprovedLoanClient.GetTransactionReceipt(ctx, &preapprovedloanPbBe.GetTransactionReceiptRequest{
		LoanActivityId: req.GetLoanActivityId(),
		ActorId:        req.GetReq().GetAuth().GetActorId(),
		LoanHeader:     helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
	})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		logger.Error(ctx, "preApprovedLoanClient.GetTransactionReceipt for GetTransactionReceipt BE API failed", zap.Error(err))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}

	if s.isPostDisbursalFlowEnabled(ctx, req.GetReq().GetAuth().GetActorId(), req.GetLoanHeader()) {
		s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGetTransactionReceipt, time.Since(startTime), palEvents.StatusSuccess, "", "", "", palEvents.LoanManagementFlowV2,
			getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())
		res.Deeplink = baseprovider.GetTransactionReceiptDeeplinkV2(req.GetLoanHeader(), beRes)
	} else {
		s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGetTransactionReceipt, time.Since(startTime), palEvents.StatusSuccess, "", "", "", palEvents.LoanManagementFlow,
			getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())
		res.Deeplink = baseprovider.GetTransactionReceiptScreenDeeplink(req.GetLoanHeader(), beRes)
	}
	res.RespHeader.Status = rpcPb.StatusOk()
	return res, nil
}

// nolint: funlen
func (s *Service) GetLoanSummaryForHome(ctx context.Context, req *palFePb.GetLoanSummaryForHomeRequest) (*palFePb.GetLoanSummaryForHomeResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()

	res := &palFePb.GetLoanSummaryForHomeResponse{
		RespHeader: &header.ResponseHeader{},
	}

	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}
	if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		req.GetLoanHeader().LoanProgram = palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram)
	}

	beRes, err := s.preApprovedLoanClient.GetLoanSummaryForHome(ctx, &preapprovedloanPbBe.GetLoanSummaryForHomeRequest{
		ActorId:    req.GetReq().GetAuth().GetActorId(),
		LoanHeader: helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
	})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		logger.Error(ctx, "preApprovedLoanClient.GetLoanSummaryForHome for GetGetLoanSummaryForHome BE API failed", zap.Error(err))
		res.GetRespHeader().Status = rpcPb.StatusInternal()
		return res, nil
	}

	// return pl benefits zero state home dashboard card only when there is no loan offer and user is aa fi-lite user
	if beRes.GetHomeCardType() == preapprovedloanPbBe.GetLoanSummaryForHomeResponse_HOME_CARD_TYPE_NO_OFFER {
		if isLiteUser, _, liteUserErr := s.isFiLiteUser(ctx, actorId); liteUserErr == nil && isLiteUser {
			return getPALFiLiteZeroState()
		}
	}

	dashboardVersionV2ReleaseConfig := s.genconf.HomeRevampParams().DashboardVersionV2ReleaseConfig()
	isNewDashVersion := req.GetDashboardVersion() == homePb.DashboardVersion_DASHBOARD_VERSION_V2
	dashInfo, err := baseprovider.GetHomeDashboardInfo(ctx, beRes, false, isNewDashVersion && isDevicePlatformVersionValidForDashboardV2(ctx, dashboardVersionV2ReleaseConfig))
	if err != nil {
		logger.Error(ctx, "error in GetHomeDashboardInfo", zap.Error(err))
		res.GetRespHeader().Status = rpcPb.StatusInternal()
		return res, nil
	}

	res.GetRespHeader().Status = rpcPb.StatusOk()
	res.DashboardInfo = dashInfo
	return res, nil
}

func (s *Service) isFiLiteUser(ctx context.Context, actorId string) (bool, onbPb.FeatureStatus, error) {
	getFeatResp, errGetFeat := s.onbClient.GetFeatureDetails(ctx, &onbPb.GetFeatureDetailsRequest{
		ActorId: actorId,
		Feature: onbPb.Feature_FEATURE_SA,
	})
	if grpcErr := epifigrpc.RPCError(getFeatResp, errGetFeat); grpcErr != nil {
		logger.Error(ctx, "failed to get feature details from onboarding", zap.Error(grpcErr))
		return false, getFeatResp.GetFeatureInfo().GetFeatureStatus(), grpcErr
	}
	return getFeatResp.GetIsFiLiteUser(), getFeatResp.GetFeatureInfo().GetFeatureStatus(), nil
}

func getPALFiLiteZeroState() (*palFePb.GetLoanSummaryForHomeResponse, error) {
	footerTicker := &homePb.HomeDashboard_FooterTicker{
		TickerItems: []*homePb.HomeDashboard_FooterTicker_TickerItem{
			{
				TickerContent: ui.NewITC().
					WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Check eligibility", "#00B899", commontypes.FontStyle_SUBTITLE_S)).
					WithRightImageUrl("https://epifi-icons.pointz.in/onboarding/lite/chevron_right_green.png"),
			},
		},
	}
	return &palFePb.GetLoanSummaryForHomeResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpcPb.StatusOk(),
		},
		DashboardInfo: &homePb.HomeDashboard{
			Title: commontypes.GetTextFromStringFontColourFontStyle("Instant Loan", "#878A8D", commontypes.FontStyle_SUBTITLE_S),
			Body: &homePb.HomeDashboard_Body{
				DashboardState: homePb.HomeDashboard_Body_STATE_ZERO,
				MoneyValueV2: []*ui.IconTextComponent{
					{
						Texts: []*commontypes.Text{
							commontypes.GetTextFromStringFontColourFontStyle("Check your eligibility for a\nloan worth up to ₹5,00,000", "#FFFFFF", commontypes.FontStyle_HEADLINE_M),
						},
						Deeplink: &deeplinkPb.Deeplink{
							Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
						},
					},
				},
			},
			Footer: []*ui.IconTextComponent{
				{
					RightIcon: commontypes.GetImageFromUrl("https://epifi-icons.pointz.in/onboarding/lite/chevron_right_green.png"),
					Texts: []*commontypes.Text{
						commontypes.GetTextFromStringFontColourFontStyle("Check eligibility", "#00B899", commontypes.FontStyle_SUBTITLE_S),
					},
					RightVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/onboarding/lite/chevron_right_green.png"),
				},
			},
			FooterTicker:    footerTicker,
			DashboardFooter: &homePb.HomeDashboard_DashboardFooterTicker{DashboardFooterTicker: footerTicker},
			Deeplink: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
			},
			Shadow:              ui.GetDashboardShadow(),
			ZeroStateImage:      commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/onboarding/lite/money_bag.png"),
			DashboardBackground: homePb.GetHomeDashboardSectionBackground(),
			BorderColor:         homePb.GetHomeDashboardBorderColor(),
		},
	}, nil
}

func (s *Service) AddAddressDetails(ctx context.Context, req *palFePb.AddAddressDetailsRequest) (*palFePb.AddAddressDetailsResponse, error) {
	helper.LogIfLoanHeaderNotPresent(ctx, req.GetLoanHeader(), "AddAddressDetails")
	startTime := time.Now()
	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}
	if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		req.GetLoanHeader().LoanProgram = palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram)
	}
	// if loan program is set early salary, it will handle for liquiloans, else, it will take from hardcoded liquiloans for pl
	s.PushApiTriggeredEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameAddAddressDetails, "", req.GetLoanRequestId(), palEvents.LoanApplicationFlow, getEventsOwnership(palFeEnumsPb.Vendor_LIQUILOANS, req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())

	err := validateAddress(req.GetAddress())
	if err != nil {
		logger.Error(ctx, "error validating address", zap.Error(err))
		invalidAddressErrorView := &errorsPb.ErrorView{
			Type: errorsPb.ErrorViewType_BOTTOM_SHEET,
			Options: &errorsPb.ErrorView_BottomSheetErrorView{
				BottomSheetErrorView: &errorsPb.BottomSheetErrorView{
					Title:    "Please check the address entered",
					Subtitle: "We faced an error validating the address. Please check again!",
					Ctas:     []*errorsPb.CTA{{Type: errorsPb.CTA_RETRY, Text: "Retry"}},
				},
			},
		}
		return &palFePb.AddAddressDetailsResponse{
			RespHeader: &header.ResponseHeader{Status: rpcPb.StatusInvalidArgument(), ErrorView: invalidAddressErrorView},
		}, nil
	}

	res := &palFePb.AddAddressDetailsResponse{
		RespHeader: &header.ResponseHeader{},
	}

	var te error
	var addAddressErr error
	var addRes *preapprovedloanPbBe.AddAddressDetailsResponse
	if req.GetAddAddressDetailsInSync() {
		addRes, addAddressErr = s.preApprovedLoanClient.AddAddressDetailsSync(ctx, &preapprovedloanPbBe.AddAddressDetailsRequest{
			LoanRequestId: req.GetLoanRequestId(),
			Address:       req.GetAddress(),
			ActorId:       req.GetReq().GetAuth().GetActorId(),
			LoanHeader:    helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
			LocationToken: req.GetReq().GetAuth().GetDevice().GetLocationToken(),
		})
	} else {
		addRes, addAddressErr = s.preApprovedLoanClient.AddAddressDetails(ctx, &preapprovedloanPbBe.AddAddressDetailsRequest{
			LoanRequestId: req.GetLoanRequestId(),
			Address:       req.GetAddress(),
			ActorId:       req.GetReq().GetAuth().GetActorId(),
			LoanHeader:    helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
			LocationToken: req.GetReq().GetAuth().GetDevice().GetLocationToken(),
		})
	}
	te = epifigrpc.RPCError(addRes, addAddressErr)
	if te != nil {
		if addRes.GetStatus().IsOutOfRange() {
			res.GetRespHeader().Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = addressNotServiceableBottomSheetErrorView
			return res, nil
		}
		logger.Error(ctx, "error in persisting address details", zap.Error(te))
		res.GetRespHeader().Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = bottomSheetErrorView
		return res, nil
	}
	// if loan program is set early salary, it will handle for liquiloans, else, it will take from hardcoded liquiloans for pl
	s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameAddAddressDetails, time.Since(startTime), palEvents.StatusSuccess, "", "", req.GetLoanRequestId(), palEvents.LoanApplicationFlow, getEventsOwnership(palFeEnumsPb.Vendor_LIQUILOANS, req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())
	res.GetRespHeader().Status = rpcPb.StatusOk()
	deeplinkProvider := s.getDeeplinkProvider(ctx, req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())
	dl, dlErr := deeplinkProvider.GetApplicationStatusPollScreenDeepLink(ctx, deeplinkProvider.GetLoanHeader(), req.GetLoanRequestId())
	if dlErr != nil {
		logger.Error(ctx, "error while generating application status poll deeplink", zap.Error(dlErr))
		return nil, fmt.Errorf("error while generating application status poll deeplink: %w", dlErr)
	}
	res.Deeplink = dl

	s.populateLODropOffFeedbackIdentifierInRespHeaderIfEnabled(res.GetRespHeader())
	return res, nil
}

func validateAddress(address *types.PostalAddress) error {
	if strings.TrimSpace(address.GetPostalCode()) == "" {
		return errors.New("no postal code found")
	}
	if strings.TrimSpace(address.GetAdministrativeArea()) == "" {
		return errors.Errorf("no administrative area (state) found")
	}
	if strings.TrimSpace(address.GetLocality()) == "" {
		return errors.Errorf("no locality found")
	}
	if len(address.GetAddressLines()) == 0 {
		return errors.Errorf("no address lines found")
	}
	return nil
}

// nolint: funlen,dupl
// TODO(Vikas): Refactor Employment Form Details rpc
func (s *Service) GetEmploymentFormDetails(ctx context.Context, req *palFePb.GetEmploymentFormDetailsRequest) (*palFePb.GetEmploymentFormDetailsResponse, error) {
	helper.LogIfLoanHeaderNotPresent(ctx, req.GetLoanHeader(), "GetEmploymentFormDetails")
	startTime := time.Now()
	// Stage 0 : Set Default Values for FormFields and other required defaults
	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}
	if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		req.GetLoanHeader().LoanProgram = palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram)
	}

	// if loan program is set early salary, it will handle for liquiloans, else, it will take from hardcoded liquiloans for pl
	s.PushApiTriggeredEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGetEmploymentFormDetails, "", "", palEvents.LoanApplicationFlow, getEventsOwnership(palFeEnumsPb.Vendor_LIQUILOANS, req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())

	res := &palFePb.GetEmploymentFormDetailsResponse{
		RespHeader: &header.ResponseHeader{},
	}
	deeplinkProvider := s.getDeeplinkProvider(ctx, req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())
	var (
		defaultWorkEmailId, defaultBusinessName, defaultCompanyName string
		// Here sending 0 as default means we don't have info and client handles this case automatically by default
		initialMoney = &types.Money{CurrencyCode: "INR"}
	)

	employmentType := req.GetEmploymentType()

	// Stage 2 : Get FormFields
	var formFieldList []*palFePb.DetailsFormField
	// On basis of employmentType create form fields
	switch employmentType {
	case types.EmploymentType_EMPLOYMENT_TYPE_SALARIED:
		formFieldList = getFormFieldsForSalaried(deeplinkProvider, initialMoney, defaultCompanyName, defaultWorkEmailId)
	case types.EmploymentType_EMPLOYMENT_TYPE_BUSINESS_OWNER,
		types.EmploymentType_EMPLOYMENT_TYPE_ENTREPRENEUR:
		formFieldList = getFormFieldsForBusiness(deeplinkProvider, initialMoney, defaultBusinessName)
	case types.EmploymentType_EMPLOYMENT_TYPE_FREELANCER,
		types.EmploymentType_EMPLOYMENT_TYPE_WORKING_PROFESSIONAL,
		types.EmploymentType_EMPLOYMENT_TYPE_STUDENT,
		types.EmploymentType_EMPLOYMENT_TYPE_HOMEMAKER,
		types.EmploymentType_EMPLOYMENT_TYPE_RETIRED:
		formFieldList = getFormFieldsForSelfEmplyed(deeplinkProvider, initialMoney, employmentType)
	case types.EmploymentType_EMPLOYMENT_TYPE_OTHERS:
		formFieldList = getFormFieldsForOthers(deeplinkProvider, initialMoney, employmentType)
	case types.EmploymentType_EMPLOYMENT_TYPE_UNSPECIFIED:
		formFieldList = []*palFePb.DetailsFormField{
			{
				InFocusText:     "EMPLOYMENT TYPE",
				PlaceholderText: "Select employment type",
				IsEditable:      false,
				NavigationCta: &deeplinkPb.Cta{
					Type:     deeplinkPb.Cta_CUSTOM,
					Deeplink: deeplinkProvider.GetOccupationSelectionDeeplink(deeplinkProvider.GetLoanHeader(), nil),
				},
				IconUrl:                 "https://epifi-icons.pointz.in/preapprovedloan/employment_down_arrow.png",
				ValueFontStyle:          commontypes.FontStyle_SUBTITLE_1,
				DetailFormFieldInfoType: palFePb.DetailsFormField_OCCUPATION_TYPE,
				IsMandatory:             true,
				PlaceholderTextV2:       commontypes.GetTextFromStringFontColourFontStyle("Select employment type", "#333333", commontypes.FontStyle_SUBTITLE_1),
				UpdatedFieldStyle:       commontypes.GetTextFromStringFontColourFontStyle("", "#333333", commontypes.FontStyle_SUBTITLE_1),
				InFocusTextV2:           commontypes.GetTextFromStringFontColourFontStyle("EMPLOYMENT TYPE", "#B9B9B9", commontypes.FontStyle_OVERLINE_1),
			},
		}
	default:
		// This is needed for case when employmentInfo is available but is set as something other than accepted ones.
		if req.GetLoanHeader().GetVendor() != palFeEnumsPb.Vendor_EPIFI_TECH {
			employmentType = types.EmploymentType_EMPLOYMENT_TYPE_SELF_EMPLOYED
		}
		formFieldList = getFormFieldsForSelfEmplyed(deeplinkProvider, initialMoney, employmentType)
	}

	appVersionConstraintData := release.NewAppVersionConstraintData(s.genconf.Lending().PreApprovedLoan().DesiredLoanAmountAppVersionConstraintConfig())
	isAppVersionCompatible, appVerErr := release.NewAppVersionConstraint().Evaluate(ctx, appVersionConstraintData, nil)
	if appVerErr != nil {
		logger.Error(ctx, "Error evaluating app version constraint", zap.Error(appVerErr))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}

	if isAppVersionCompatible {
		formFieldList = append(formFieldList, &palFePb.DetailsFormField{
			InFocusText:     "DESIRED LOAN AMOUNT",
			PlaceholderText: "Desired loan amount",
			IsEditable:      false,
			NavigationCta: &deeplinkPb.Cta{
				Type: deeplinkPb.Cta_CUSTOM,
				Deeplink: deeplinkProvider.GetIncomeSelectionDeeplink(&provider.GetIncomeSelectionDeeplinkRequest{
					Lh:                   deeplinkProvider.GetLoanHeader(),
					DefaultInitialAmount: initialMoney,
					Title:                "Enter desired loan amount",
					BottomMessage:        "This is required to run final checks on your application",
					IncomeFrequencyLabel: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{PlainString: ""},
					},
				}),
			},
			IconUrl: "https://epifi-icons.pointz.in/preapprovedloan/employment_edit_pencil.png",
			DefaultValues: &palFePb.DetailsFormField_MoneyDefaultValue{
				MoneyDefaultValue: initialMoney,
			},
			ValueFontStyle:          commontypes.FontStyle_BODY_2,
			DetailFormFieldInfoType: palFePb.DetailsFormField_DESIRED_LOAN_AMOUNT,
			IsMandatory:             true,
			PlaceholderTextV2:       commontypes.GetTextFromStringFontColourFontStyle("Desired loan amount", "#878A8D", commontypes.FontStyle_SUBTITLE_1),
			UpdatedFieldStyle:       commontypes.GetTextFromStringFontColourFontStyle("", "#333333", commontypes.FontStyle_SUBTITLE_1),
			InFocusTextV2:           commontypes.GetTextFromStringFontColourFontStyle("DESIRED LOAN AMOUNT", "#B9B9B9", commontypes.FontStyle_OVERLINE_1),
		})
	}
	// if loan program is set early salary, it will handle for liquiloans, else, it will take from hardcoded liquiloans for pl
	s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGetEmploymentFormDetails, time.Since(startTime), palEvents.StatusSuccess, "", "", "", palEvents.LoanApplicationFlow, getEventsOwnership(palFeEnumsPb.Vendor_LIQUILOANS, req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())
	// Stage 3 : Set form fields
	res.FormFieldList = formFieldList
	res.DefaultEmploymentType = employmentType
	res.GetRespHeader().Status = rpcPb.StatusOk()
	return res, nil
}

func getMissingMandatoryFields(details []*palFePb.AddEmploymentDetailsRequest_EmploymentDetails, mandatoryFields []palFePb.DetailsFormField_DetailFormFieldInfoType) []palFePb.DetailsFormField_DetailFormFieldInfoType {
	detailsTypeMap := make(map[palFePb.DetailsFormField_DetailFormFieldInfoType]bool)
	for _, ld := range details {
		detailsTypeMap[ld.GetDetailsType()] = true
	}

	var missingFields []palFePb.DetailsFormField_DetailFormFieldInfoType
	for _, field := range mandatoryFields {
		if !detailsTypeMap[field] {
			missingFields = append(missingFields, field)
		}
	}
	return missingFields
}

func validateEmploymentDetails(ctx context.Context, req *palFePb.AddEmploymentDetailsRequest) *errorsPb.ErrorView {
	if len(req.GetEmploymentDetailsList()) == 0 {
		return &errorsPb.ErrorView{
			Type: errorsPb.ErrorViewType_BOTTOM_SHEET,
			Options: &errorsPb.ErrorView_BottomSheetErrorView{
				BottomSheetErrorView: &errorsPb.BottomSheetErrorView{
					Title: "Please fill the required fields",
				},
			},
		}
	}

	var absentFields []palFePb.DetailsFormField_DetailFormFieldInfoType
	switch req.GetEmploymentDetailsList()[0].GetEmploymentValue() {
	case types.EmploymentType_EMPLOYMENT_TYPE_SALARIED:
		absentFields = getMissingMandatoryFields(req.GetEmploymentDetailsList(), mandatoryFieldsForSalaried)
	case types.EmploymentType_EMPLOYMENT_TYPE_BUSINESS_OWNER,
		types.EmploymentType_EMPLOYMENT_TYPE_ENTREPRENEUR:
		absentFields = getMissingMandatoryFields(req.GetEmploymentDetailsList(), mandatoryFormFieldsForBusiness)
	case types.EmploymentType_EMPLOYMENT_TYPE_FREELANCER,
		types.EmploymentType_EMPLOYMENT_TYPE_WORKING_PROFESSIONAL,
		types.EmploymentType_EMPLOYMENT_TYPE_STUDENT,
		types.EmploymentType_EMPLOYMENT_TYPE_HOMEMAKER,
		types.EmploymentType_EMPLOYMENT_TYPE_RETIRED:
		absentFields = getMissingMandatoryFields(req.GetEmploymentDetailsList(), mandatoryFormFieldsForSelfEmployed)
	case types.EmploymentType_EMPLOYMENT_TYPE_OTHERS:
		absentFields = getMissingMandatoryFields(req.GetEmploymentDetailsList(), mandatoryFormFieldsForOthers)
	default:
		// do nothing
	}

	var missingFields []string
	for _, field := range absentFields {
		missingFields = append(missingFields, field.String())
	}

	if len(absentFields) > 0 {
		logger.Info(ctx, "missing mandatory fields", zap.Strings("absentFields", missingFields))
		return &errorsPb.ErrorView{
			Type: errorsPb.ErrorViewType_BOTTOM_SHEET,
			Options: &errorsPb.ErrorView_BottomSheetErrorView{
				BottomSheetErrorView: &errorsPb.BottomSheetErrorView{
					Title: "Please fill the required fields",
				},
			},
		}
	}

	for _, ld := range req.GetEmploymentDetailsList() {
		switch ld.GetDetailsType() {
		case palFePb.DetailsFormField_OCCUPATION_NAME:
			matched, err := regexp.MatchString(`^[a-zA-Z0-9\s!&.,@#(){}\[\]/|\\'":;_\-=+~]+$`, ld.GetStringValue())
			if !matched || err != nil {
				logger.Error(ctx, "unsupported characters in the organization name", zap.Error(err), zap.String("employerName", ld.GetStringValue()))
				return &errorsPb.ErrorView{
					Type: errorsPb.ErrorViewType_BOTTOM_SHEET,
					Options: &errorsPb.ErrorView_BottomSheetErrorView{
						BottomSheetErrorView: &errorsPb.BottomSheetErrorView{
							Title:    "Company name is invalid",
							Subtitle: "Only English characters are allowed",
							Ctas: []*errorsPb.CTA{
								{
									Type: errorsPb.CTA_DONE,
									Text: "Ok",
								},
							},
						},
					},
				}
			}
		case palFePb.DetailsFormField_MONTHLY_INCOME:
			if ld.GetMoneyValue().GetUnits() <= int64(getMinimumMonthlyIncomeByVendor(req.GetLoanHeader().GetVendor())) {
				logger.Info(ctx, "monthly income is invalid")
				return &errorsPb.ErrorView{
					Type: errorsPb.ErrorViewType_BOTTOM_SHEET,
					Options: &errorsPb.ErrorView_BottomSheetErrorView{
						BottomSheetErrorView: &errorsPb.BottomSheetErrorView{
							Title:    "Monthly Income is invalid",
							Subtitle: fmt.Sprintf("Income should be more than %d", getMinimumMonthlyIncomeByVendor(req.GetLoanHeader().GetVendor())),
						},
					},
				}
			}
		case palFePb.DetailsFormField_DESIRED_LOAN_AMOUNT:
			if ld.GetMoneyValue().GetUnits() < int64(25000) || ld.GetMoneyValue().GetUnits() > int64(1000000) {
				logger.Info(ctx, "invalid loan amount")
				return &errorsPb.ErrorView{
					Type: errorsPb.ErrorViewType_BOTTOM_SHEET,
					Options: &errorsPb.ErrorView_BottomSheetErrorView{
						BottomSheetErrorView: &errorsPb.BottomSheetErrorView{
							Title:    "Loan amount is invalid",
							Subtitle: "Loan amount should be between 25,000 and 10,00,000",
						},
					},
				}
			}
		case palFePb.DetailsFormField_WORK_EMAIL_ADDRESS:
			matched, err := regexp.MatchString(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`, ld.GetStringValue())
			if !matched || err != nil {
				logger.Error(ctx, "invalid work email address", zap.Error(err))
				return &errorsPb.ErrorView{
					Type: errorsPb.ErrorViewType_BOTTOM_SHEET,
					Options: &errorsPb.ErrorView_BottomSheetErrorView{
						BottomSheetErrorView: &errorsPb.BottomSheetErrorView{
							Title:    "Work email address is invalid",
							Subtitle: "Please enter a valid email address",
						},
					},
				}
			}
		default:
			// do nothing
		}
	}
	return nil
}

func (s *Service) AddEmploymentDetails(ctx context.Context, req *palFePb.AddEmploymentDetailsRequest) (*palFePb.AddEmploymentDetailsResponse, error) {
	helper.LogIfLoanHeaderNotPresent(ctx, req.GetLoanHeader(), "AddEmploymentDetails")
	startTime := time.Now()
	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}
	if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		req.GetLoanHeader().LoanProgram = palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram)
	}
	// if loan program is set early salary, it will handle for liquiloans, else, it will take from hardcoded liquiloans for pl
	s.PushApiTriggeredEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameAddEmploymentDetails, "", req.GetLoanRequestId(), palEvents.LoanApplicationFlow, getEventsOwnership(palFeEnumsPb.Vendor_LIQUILOANS, req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())

	errView := validateEmploymentDetails(ctx, req)
	if errView != nil {
		return &palFePb.AddEmploymentDetailsResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpcPb.StatusInvalidArgument(),
				ErrorView: errView,
			},
		}, nil
	}
	res := &palFePb.AddEmploymentDetailsResponse{
		RespHeader: &header.ResponseHeader{},
	}

	// For ABFl, recording consent for adding employment details
	if req.GetLoanHeader().GetVendor() == palFeEnumsPb.Vendor_ABFL {
		err := s.RecordConsent(ctx, req.GetReq().GetAuth().GetActorId(), req.GetReq().GetAuth().GetDevice(), consentPb.ConsentType_LOANS_ABFL_EMPLOYMENT_DATA)
		if err != nil {
			logger.Error(ctx, "error while calling RecordConsent", zap.Error(err))
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
	}

	occupationType, organizationName, averageMonthlyIncome, workEmail, officeAddress, desiredLoanAmount := getDetailsFromEmploymentList(req.GetEmploymentDetailsList())
	addDetailsReq := &preapprovedloanPbBe.AddEmploymentDetailsRequest{
		LoanRequestId:     req.GetLoanRequestId(),
		OccupationType:    occupationType,
		OrganizationName:  organizationName,
		MonthlyIncome:     averageMonthlyIncome.GetBeMoney(),
		ActorId:           req.GetReq().GetAuth().GetActorId(),
		WorkEmail:         workEmail,
		LoanHeader:        helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
		OfficeAddress:     officeAddress,
		DesiredLoanAmount: desiredLoanAmount.GetBeMoney(),
	}
	var te error
	// if adding the employment details in sync flag is true, we will call that rpc for BE service
	// we set this flag in screen options to client, client returns this value in the subsequent rpc call.
	if req.GetAddEmploymentDetailsInSync() {
		addRes, err := s.preApprovedLoanClient.AddEmploymentDetailsSync(ctx, addDetailsReq)
		te = epifigrpc.RPCError(addRes, err)
	} else {
		addRes, err := s.preApprovedLoanClient.AddEmploymentDetails(ctx, addDetailsReq)
		te = epifigrpc.RPCError(addRes, err)
	}
	if te != nil {
		logger.Error(ctx, "error in persisting employment details", zap.Error(te))
		res.GetRespHeader().Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}
	// if loan program is set early salary, it will handle for liquiloans, else, it will take from hardcoded liquiloans for pl
	s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameAddEmploymentDetails, time.Since(startTime), palEvents.StatusSuccess, "", "", req.GetLoanRequestId(), palEvents.LoanApplicationFlow, getEventsOwnership(palFeEnumsPb.Vendor_LIQUILOANS, req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())
	res.GetRespHeader().Status = rpcPb.StatusOk()
	deeplinkProvider := s.getDeeplinkProvider(ctx, req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())
	dl, dlErr := deeplinkProvider.GetApplicationStatusPollScreenDeepLink(ctx, deeplinkProvider.GetLoanHeader(), req.GetLoanRequestId())
	if dlErr != nil {
		logger.Error(ctx, "error while generating application status poll deeplink", zap.Error(dlErr))
		return nil, fmt.Errorf("error while generating application status poll deeplink: %w", dlErr)
	}
	res.Deeplink = dl
	s.populateLODropOffFeedbackIdentifierInRespHeaderIfEnabled(res.GetRespHeader())
	return res, nil
}

func getDetailsFromEmploymentList(listOfDetails []*palFePb.AddEmploymentDetailsRequest_EmploymentDetails) (types.EmploymentType, string, *types.Money, string, *types.PostalAddress, *types.Money) {
	var (
		occupationType       types.EmploymentType
		organizationName     string
		averageMonthlyIncome *types.Money
		workEmail            string
		officeAddress        *types.PostalAddress
		desiredLoanAmount    *types.Money
	)

	for _, ld := range listOfDetails {
		switch ld.GetDetailsType() {
		case palFePb.DetailsFormField_OCCUPATION_TYPE:
			occupationType = ld.GetEmploymentValue()
		case palFePb.DetailsFormField_MONTHLY_INCOME:
			averageMonthlyIncome = ld.GetMoneyValue()
		case palFePb.DetailsFormField_OCCUPATION_NAME:
			organizationName = ld.GetStringValue()
		case palFePb.DetailsFormField_WORK_EMAIL_ADDRESS:
			workEmail = ld.GetStringValue()
		case palFePb.DetailsFormField_OFFICE_ADDRESS:
			officeAddress = ld.GetAddressValue()
		case palFePb.DetailsFormField_DESIRED_LOAN_AMOUNT:
			desiredLoanAmount = ld.GetMoneyValue()
		default:
			// Do nothing
		}
	}

	return occupationType, organizationName, averageMonthlyIncome, workEmail, officeAddress, desiredLoanAmount
}

func createMonthlyIncomeSelectionDeeplinkRequest(lh *palFeEnumsPb.LoanHeader, defaultInitialMoney *types.Money) *provider.GetIncomeSelectionDeeplinkRequest {
	return &provider.GetIncomeSelectionDeeplinkRequest{
		Lh:                   lh,
		DefaultInitialAmount: defaultInitialMoney,
		Title:                "Enter in-hand monthly income",
		BottomMessage:        "This is required to run final checks on your application",
		IncomeFrequencyLabel: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: "/m"},
			FontColor:    perMonthFontColor,
		},
	}
}
func createIncomeSelectionDeeplinkRequest(lh *palFeEnumsPb.LoanHeader, defaultInitialMoney *types.Money, title string, bottomMessage string, minAmt, maxAmt *types.Money, displayPerMonthOrYear string) *provider.GetIncomeSelectionDeeplinkRequest {
	return &provider.GetIncomeSelectionDeeplinkRequest{
		Lh:                   lh,
		DefaultInitialAmount: defaultInitialMoney,
		Title:                title,
		BottomMessage:        bottomMessage,
		IncomeFrequencyLabel: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: displayPerMonthOrYear},
			FontColor:    perMonthFontColor,
		},
		MinValue: minAmt,
		MaxValue: maxAmt,
	}
}

// nolint: dupl
func getFormFieldsForOthers(deeplinkProvider provider.IDeeplinkProvider, defaultInitialMoney *types.Money, employmentType types.EmploymentType) []*palFePb.DetailsFormField {
	occupationType := helper.EmploymentMap[employmentType]
	lh := deeplinkProvider.GetLoanHeader()
	formFieldDetails := []*palFePb.DetailsFormField{
		{
			InFocusText:     "OCCUPATION",
			PlaceholderText: "OCCUPATION",
			IsEditable:      false,
			NavigationCta: &deeplinkPb.Cta{
				Type:     deeplinkPb.Cta_CUSTOM,
				Deeplink: deeplinkProvider.GetOccupationSelectionDeeplink(deeplinkProvider.GetLoanHeader(), occupationType),
			},
			IconUrl: "https://epifi-icons.pointz.in/preapprovedloan/employment_down_arrow.png",
			DefaultValues: &palFePb.DetailsFormField_StringDefaultValue{
				StringDefaultValue: occupationType.GetEmploymentText(),
			},
			ValueFontStyle:          commontypes.FontStyle_SUBTITLE_1,
			DetailFormFieldInfoType: palFePb.DetailsFormField_OCCUPATION_TYPE,
			IsMandatory:             true,
			PlaceholderTextV2:       commontypes.GetTextFromStringFontColourFontStyle("OCCUPATION", "#333333", commontypes.FontStyle_SUBTITLE_1),
			UpdatedFieldStyle:       commontypes.GetTextFromStringFontColourFontStyle("", "#333333", commontypes.FontStyle_SUBTITLE_1),
			InFocusTextV2:           commontypes.GetTextFromStringFontColourFontStyle("OCCUPATION", "#B9B9B9", commontypes.FontStyle_OVERLINE_1),
		},
		{
			InFocusText:     "AVERAGE MONTHLY INCOME",
			PlaceholderText: "Enter your average monthly income",
			IsEditable:      true,
			NavigationCta: &deeplinkPb.Cta{
				Type:     deeplinkPb.Cta_CUSTOM,
				Deeplink: deeplinkProvider.GetIncomeSelectionDeeplink(createMonthlyIncomeSelectionDeeplinkRequest(lh, defaultInitialMoney)),
			},
			IconUrl: "https://epifi-icons.pointz.in/preapprovedloan/employment_edit_pencil.png",
			DefaultValues: &palFePb.DetailsFormField_MoneyDefaultValue{
				MoneyDefaultValue: defaultInitialMoney,
			},
			ValueFontStyle:          commontypes.FontStyle_BODY_2,
			DetailFormFieldInfoType: palFePb.DetailsFormField_MONTHLY_INCOME,
			IsMandatory:             true,
			PlaceholderTextV2:       commontypes.GetTextFromStringFontColourFontStyle("Enter your average monthly income", "#878A8D", commontypes.FontStyle_SUBTITLE_1),
			UpdatedFieldStyle:       commontypes.GetTextFromStringFontColourFontStyle("", "#333333", commontypes.FontStyle_SUBTITLE_1),
			InFocusTextV2:           commontypes.GetTextFromStringFontColourFontStyle("AVERAGE MONTHLY INCOME", "#B9B9B9", commontypes.FontStyle_OVERLINE_1),
		},
	}

	switch lh.GetVendor() {
	case palFeEnumsPb.Vendor_ABFL:
		formFieldDetails = append(formFieldDetails, &palFePb.DetailsFormField{
			InFocusText:     companyAddressTitleString,
			PlaceholderText: officeaddressSubTitleString,
			IsEditable:      false,
			IconUrl:         "https://epifi-icons.pointz.in/preapprovedloan/employment_edit_pencil.png",
			DefaultValues: &palFePb.DetailsFormField_AddressValue{
				AddressValue: &types.PostalAddress{},
			},
			ValueFontStyle:          commontypes.FontStyle_BODY_2,
			DetailFormFieldInfoType: palFePb.DetailsFormField_OFFICE_ADDRESS,
			IsMandatory:             false,
			PlaceholderTextV2:       commontypes.GetTextFromStringFontColourFontStyle(officeaddressSubTitleString, "#878A8D", commontypes.FontStyle_SUBTITLE_1),
			UpdatedFieldStyle:       commontypes.GetTextFromStringFontColourFontStyle("", "#333333", commontypes.FontStyle_SUBTITLE_1),
			InFocusTextV2:           commontypes.GetTextFromStringFontColourFontStyle(companyAddressTitleString, "#B9B9B9", commontypes.FontStyle_OVERLINE_1),
		})
	default:
		// do nothing for now
	}

	return formFieldDetails
}

// nolint: dupl
func getFormFieldsForSelfEmplyed(deeplinkProvider provider.IDeeplinkProvider, defaultInitialMoney *types.Money, employmentType types.EmploymentType) []*palFePb.DetailsFormField {
	occupationType := helper.EmploymentMap[employmentType]
	lh := deeplinkProvider.GetLoanHeader()
	formFieldDetails := []*palFePb.DetailsFormField{
		{
			InFocusText:     "OCCUPATION",
			PlaceholderText: "OCCUPATION",
			IsEditable:      false,
			NavigationCta: &deeplinkPb.Cta{
				Type:     deeplinkPb.Cta_CUSTOM,
				Deeplink: deeplinkProvider.GetOccupationSelectionDeeplink(deeplinkProvider.GetLoanHeader(), occupationType),
			},
			IconUrl: "https://epifi-icons.pointz.in/preapprovedloan/employment_down_arrow.png",
			DefaultValues: &palFePb.DetailsFormField_StringDefaultValue{
				StringDefaultValue: occupationType.GetEmploymentText(),
			},
			ValueFontStyle:          commontypes.FontStyle_SUBTITLE_1,
			DetailFormFieldInfoType: palFePb.DetailsFormField_OCCUPATION_TYPE,
			IsMandatory:             true,
			PlaceholderTextV2:       commontypes.GetTextFromStringFontColourFontStyle("OCCUPATION", "#333333", commontypes.FontStyle_SUBTITLE_1),
			UpdatedFieldStyle:       commontypes.GetTextFromStringFontColourFontStyle("", "#333333", commontypes.FontStyle_SUBTITLE_1),
			InFocusTextV2:           commontypes.GetTextFromStringFontColourFontStyle("OCCUPATION", "#B9B9B9", commontypes.FontStyle_OVERLINE_1),
		},
		{
			InFocusText:     "YOUR BUSINESS NAME",
			PlaceholderText: "Enter your business name",
			IsEditable:      true,
			DefaultValues: &palFePb.DetailsFormField_StringDefaultValue{
				StringDefaultValue: "",
			},
			ValueFontStyle:          commontypes.FontStyle_BODY_2,
			DetailFormFieldInfoType: palFePb.DetailsFormField_OCCUPATION_NAME,
			IsMandatory:             true,
			PlaceholderTextV2:       commontypes.GetTextFromStringFontColourFontStyle("Enter your business name", "#878A8D", commontypes.FontStyle_SUBTITLE_1),
			UpdatedFieldStyle:       commontypes.GetTextFromStringFontColourFontStyle("", "#333333", commontypes.FontStyle_SUBTITLE_1),
			InFocusTextV2:           commontypes.GetTextFromStringFontColourFontStyle("YOUR BUSINESS NAME", "#B9B9B9", commontypes.FontStyle_OVERLINE_1),
		},
		{
			InFocusText:     "AVERAGE MONTHLY INCOME",
			PlaceholderText: "Enter your average monthly income",
			IsEditable:      true,
			NavigationCta: &deeplinkPb.Cta{
				Type:     deeplinkPb.Cta_CUSTOM,
				Deeplink: deeplinkProvider.GetIncomeSelectionDeeplink(createMonthlyIncomeSelectionDeeplinkRequest(lh, defaultInitialMoney)),
			},
			IconUrl: "https://epifi-icons.pointz.in/preapprovedloan/employment_edit_pencil.png",
			DefaultValues: &palFePb.DetailsFormField_MoneyDefaultValue{
				MoneyDefaultValue: defaultInitialMoney,
			},
			ValueFontStyle:          commontypes.FontStyle_BODY_2,
			DetailFormFieldInfoType: palFePb.DetailsFormField_MONTHLY_INCOME,
			IsMandatory:             true,
			PlaceholderTextV2:       commontypes.GetTextFromStringFontColourFontStyle("Enter your average monthly income", "#878A8D", commontypes.FontStyle_SUBTITLE_1),
			UpdatedFieldStyle:       commontypes.GetTextFromStringFontColourFontStyle("", "#333333", commontypes.FontStyle_SUBTITLE_1),
			InFocusTextV2:           commontypes.GetTextFromStringFontColourFontStyle("AVERAGE MONTHLY INCOME", "#B9B9B9", commontypes.FontStyle_OVERLINE_1),
		},
	}

	switch lh.GetVendor() {
	case palFeEnumsPb.Vendor_ABFL:
		formFieldDetails = append(formFieldDetails, &palFePb.DetailsFormField{
			InFocusText:     companyAddressTitleString,
			PlaceholderText: officeaddressSubTitleString,
			IsEditable:      false,
			IconUrl:         "https://epifi-icons.pointz.in/preapprovedloan/employment_edit_pencil.png",
			DefaultValues: &palFePb.DetailsFormField_AddressValue{
				AddressValue: &types.PostalAddress{},
			},
			ValueFontStyle:          commontypes.FontStyle_BODY_2,
			DetailFormFieldInfoType: palFePb.DetailsFormField_OFFICE_ADDRESS,
			IsMandatory:             false,
			PlaceholderTextV2:       commontypes.GetTextFromStringFontColourFontStyle(officeaddressSubTitleString, "#878A8D", commontypes.FontStyle_SUBTITLE_1),
			UpdatedFieldStyle:       commontypes.GetTextFromStringFontColourFontStyle("", "#333333", commontypes.FontStyle_SUBTITLE_1),
			InFocusTextV2:           commontypes.GetTextFromStringFontColourFontStyle(companyAddressTitleString, "#B9B9B9", commontypes.FontStyle_OVERLINE_1),
		})
	default:
		// do nothing for now
	}

	return formFieldDetails
}

// nolint: dupl
func getFormFieldsForBusiness(deeplinkProvider provider.IDeeplinkProvider, defaultInitialMoney *types.Money, defaultBusinessName string) []*palFePb.DetailsFormField {
	occupationType := &deeplinkPb.PreApprovedOccupationSelectionScreenOptions_EmploymentData{
		EmploymentType: types.EmploymentType_EMPLOYMENT_TYPE_BUSINESS_OWNER,
		EmploymentText: "Business Owner / Entrepreneur",
	}
	lh := deeplinkProvider.GetLoanHeader()
	formFieldDetails := []*palFePb.DetailsFormField{
		{
			InFocusText:     "OCCUPATION",
			PlaceholderText: "OCCUPATION",
			IsEditable:      false,
			NavigationCta: &deeplinkPb.Cta{
				Type:     deeplinkPb.Cta_CUSTOM,
				Deeplink: deeplinkProvider.GetOccupationSelectionDeeplink(deeplinkProvider.GetLoanHeader(), occupationType),
			},
			IconUrl: "https://epifi-icons.pointz.in/preapprovedloan/employment_down_arrow.png",
			DefaultValues: &palFePb.DetailsFormField_StringDefaultValue{
				StringDefaultValue: occupationType.GetEmploymentText(),
			},
			ValueFontStyle:          commontypes.FontStyle_SUBTITLE_1,
			DetailFormFieldInfoType: palFePb.DetailsFormField_OCCUPATION_TYPE,
			IsMandatory:             true,
			PlaceholderTextV2:       commontypes.GetTextFromStringFontColourFontStyle("OCCUPATION", "#333333", commontypes.FontStyle_SUBTITLE_1),
			UpdatedFieldStyle:       commontypes.GetTextFromStringFontColourFontStyle("", "#333333", commontypes.FontStyle_SUBTITLE_1),
			InFocusTextV2:           commontypes.GetTextFromStringFontColourFontStyle("OCCUPATION", "#B9B9B9", commontypes.FontStyle_OVERLINE_1),
		},
		{
			InFocusText:     "YOUR BUSINESS NAME",
			PlaceholderText: "Enter your business name",
			IsEditable:      true,
			DefaultValues: &palFePb.DetailsFormField_StringDefaultValue{
				StringDefaultValue: defaultBusinessName,
			},
			ValueFontStyle:          commontypes.FontStyle_BODY_2,
			DetailFormFieldInfoType: palFePb.DetailsFormField_OCCUPATION_NAME,
			IsMandatory:             true,
			PlaceholderTextV2:       commontypes.GetTextFromStringFontColourFontStyle("Enter your business name", "#878A8D", commontypes.FontStyle_SUBTITLE_1),
			UpdatedFieldStyle:       commontypes.GetTextFromStringFontColourFontStyle("", "#333333", commontypes.FontStyle_SUBTITLE_1),
			InFocusTextV2:           commontypes.GetTextFromStringFontColourFontStyle("YOUR BUSINESS NAME", "#B9B9B9", commontypes.FontStyle_OVERLINE_1),
		},
		{
			InFocusText:     "AVERAGE MONTHLY REVENUE",
			PlaceholderText: "Enter your average monthly revenue",
			IsEditable:      false,
			NavigationCta: &deeplinkPb.Cta{
				Type:     deeplinkPb.Cta_CUSTOM,
				Deeplink: deeplinkProvider.GetIncomeSelectionDeeplink(createMonthlyIncomeSelectionDeeplinkRequest(lh, defaultInitialMoney)),
			},
			IconUrl: "https://epifi-icons.pointz.in/preapprovedloan/employment_edit_pencil.png",
			DefaultValues: &palFePb.DetailsFormField_MoneyDefaultValue{
				MoneyDefaultValue: defaultInitialMoney,
			},
			ValueFontStyle:          commontypes.FontStyle_BODY_2,
			DetailFormFieldInfoType: palFePb.DetailsFormField_MONTHLY_INCOME,
			IsMandatory:             true,
			PlaceholderTextV2:       commontypes.GetTextFromStringFontColourFontStyle("Enter your average monthly revenue", "#878A8D", commontypes.FontStyle_SUBTITLE_1),
			UpdatedFieldStyle:       commontypes.GetTextFromStringFontColourFontStyle("", "#333333", commontypes.FontStyle_SUBTITLE_1),
			InFocusTextV2:           commontypes.GetTextFromStringFontColourFontStyle("AVERAGE MONTHLY REVENUE", "#B9B9B9", commontypes.FontStyle_OVERLINE_1),
		},
	}
	switch lh.GetVendor() {
	case palFeEnumsPb.Vendor_ABFL:
		formFieldDetails = append(formFieldDetails, &palFePb.DetailsFormField{
			InFocusText:     companyAddressTitleString,
			PlaceholderText: officeaddressSubTitleString,
			IsEditable:      false,
			IconUrl:         "https://epifi-icons.pointz.in/preapprovedloan/employment_edit_pencil.png",
			DefaultValues: &palFePb.DetailsFormField_AddressValue{
				AddressValue: &types.PostalAddress{},
			},
			ValueFontStyle:          commontypes.FontStyle_BODY_2,
			DetailFormFieldInfoType: palFePb.DetailsFormField_OFFICE_ADDRESS,
			IsMandatory:             false,
			PlaceholderTextV2:       commontypes.GetTextFromStringFontColourFontStyle(officeaddressSubTitleString, "#878A8D", commontypes.FontStyle_SUBTITLE_1),
			UpdatedFieldStyle:       commontypes.GetTextFromStringFontColourFontStyle("", "#333333", commontypes.FontStyle_SUBTITLE_1),
			InFocusTextV2:           commontypes.GetTextFromStringFontColourFontStyle(companyAddressTitleString, "#B9B9B9", commontypes.FontStyle_OVERLINE_1),
		})
	default:
		// do nothing for now
	}
	return formFieldDetails
}

// nolint: dupl
func getFormFieldsForSalariedForEpifiTech(deeplinkProvider provider.IDeeplinkProvider, defaultInitialMoney *types.Money, companyName string, workEmailId string) []*palFePb.DetailsFormField {
	occupationType := &deeplinkPb.PreApprovedOccupationSelectionScreenOptions_EmploymentData{
		EmploymentType: types.EmploymentType_EMPLOYMENT_TYPE_SALARIED,
		EmploymentText: "Salaried Professional",
	}
	formFieldDetails := []*palFePb.DetailsFormField{
		{
			InFocusText:     "EMPLOYMENT TYPE",
			PlaceholderText: "Select employment type",
			IsEditable:      false,
			NavigationCta: &deeplinkPb.Cta{
				Type:     deeplinkPb.Cta_CUSTOM,
				Deeplink: deeplinkProvider.GetOccupationSelectionDeeplink(deeplinkProvider.GetLoanHeader(), occupationType),
			},
			IconUrl: "https://epifi-icons.pointz.in/preapprovedloan/employment_down_arrow.png",
			DefaultValues: &palFePb.DetailsFormField_StringDefaultValue{
				StringDefaultValue: occupationType.GetEmploymentText(),
			},
			ValueFontStyle:          commontypes.FontStyle_SUBTITLE_1,
			DetailFormFieldInfoType: palFePb.DetailsFormField_OCCUPATION_TYPE,
			IsMandatory:             true,
			PlaceholderTextV2:       commontypes.GetTextFromStringFontColourFontStyle("Select employment type", "#333333", commontypes.FontStyle_SUBTITLE_1),
			UpdatedFieldStyle:       commontypes.GetTextFromStringFontColourFontStyle("", "#333333", commontypes.FontStyle_SUBTITLE_1),
			InFocusTextV2:           commontypes.GetTextFromStringFontColourFontStyle("EMPLOYMENT TYPE", "#B9B9B9", commontypes.FontStyle_OVERLINE_1),
		},
		{
			InFocusText:     "EMPLOYER NAME",
			PlaceholderText: "Add employer name",
			IsEditable:      true,
			DefaultValues: &palFePb.DetailsFormField_StringDefaultValue{
				StringDefaultValue: companyName,
			},
			ValueFontStyle:          commontypes.FontStyle_BODY_2,
			DetailFormFieldInfoType: palFePb.DetailsFormField_OCCUPATION_NAME,
			IsMandatory:             true,
			PlaceholderTextV2:       commontypes.GetTextFromStringFontColourFontStyle("Add employer name", "#878A8D", commontypes.FontStyle_SUBTITLE_1),
			UpdatedFieldStyle:       commontypes.GetTextFromStringFontColourFontStyle("", "#333333", commontypes.FontStyle_SUBTITLE_1),
			InFocusTextV2:           commontypes.GetTextFromStringFontColourFontStyle("EMPLOYER NAME", "#B9B9B9", commontypes.FontStyle_OVERLINE_1),
		},
		{
			InFocusText:     "WORK EMAIL",
			PlaceholderText: "Enter your work email",
			IsEditable:      true,
			DefaultValues: &palFePb.DetailsFormField_StringDefaultValue{
				StringDefaultValue: workEmailId,
			},
			ValueFontStyle:          commontypes.FontStyle_BODY_2,
			DetailFormFieldInfoType: palFePb.DetailsFormField_WORK_EMAIL_ADDRESS,
			IsMandatory:             true,
			PlaceholderTextV2:       commontypes.GetTextFromStringFontColourFontStyle("Enter your work email", "#878A8D", commontypes.FontStyle_SUBTITLE_1),
			UpdatedFieldStyle:       commontypes.GetTextFromStringFontColourFontStyle("", "#333333", commontypes.FontStyle_SUBTITLE_1),
			InFocusTextV2:           commontypes.GetTextFromStringFontColourFontStyle("WORK EMAIL", "#B9B9B9", commontypes.FontStyle_OVERLINE_1),
		},
		{
			InFocusText:     "MONTHLY INCOME",
			PlaceholderText: "Enter monthly income",
			IsEditable:      false,
			NavigationCta: &deeplinkPb.Cta{
				Type:     deeplinkPb.Cta_CUSTOM,
				Deeplink: deeplinkProvider.GetIncomeSelectionDeeplink(createMonthlyIncomeSelectionDeeplinkRequest(deeplinkProvider.GetLoanHeader(), defaultInitialMoney)),
			},
			IconUrl: "https://epifi-icons.pointz.in/preapprovedloan/employment_edit_pencil.png",
			DefaultValues: &palFePb.DetailsFormField_MoneyDefaultValue{
				MoneyDefaultValue: defaultInitialMoney,
			},
			ValueFontStyle:          commontypes.FontStyle_BODY_2,
			DetailFormFieldInfoType: palFePb.DetailsFormField_MONTHLY_INCOME,
			IsMandatory:             true,
			PlaceholderTextV2:       commontypes.GetTextFromStringFontColourFontStyle("Enter monthly income", "#878A8D", commontypes.FontStyle_SUBTITLE_1),
			UpdatedFieldStyle:       commontypes.GetTextFromStringFontColourFontStyle("", "#333333", commontypes.FontStyle_SUBTITLE_1),
			InFocusTextV2:           commontypes.GetTextFromStringFontColourFontStyle("MONTHLY INCOME", "#B9B9B9", commontypes.FontStyle_OVERLINE_1),
		},
	}
	return formFieldDetails
}

// nolint: dupl
func getFormFieldsForSalaried(deeplinkProvider provider.IDeeplinkProvider, defaultInitialMoney *types.Money, companyName string, workEmailId string) []*palFePb.DetailsFormField {
	occupationType := &deeplinkPb.PreApprovedOccupationSelectionScreenOptions_EmploymentData{
		EmploymentType: types.EmploymentType_EMPLOYMENT_TYPE_SALARIED,
		EmploymentText: "Salaried Professional",
	}
	lh := deeplinkProvider.GetLoanHeader()
	formFieldDetails := []*palFePb.DetailsFormField{
		{
			InFocusText:     "EMPLOYMENT TYPE",
			PlaceholderText: "Select employment type",
			IsEditable:      false,
			NavigationCta: &deeplinkPb.Cta{
				Type:     deeplinkPb.Cta_CUSTOM,
				Deeplink: deeplinkProvider.GetOccupationSelectionDeeplink(deeplinkProvider.GetLoanHeader(), occupationType),
			},
			IconUrl: "https://epifi-icons.pointz.in/preapprovedloan/employment_down_arrow.png",
			DefaultValues: &palFePb.DetailsFormField_StringDefaultValue{
				StringDefaultValue: occupationType.GetEmploymentText(),
			},
			ValueFontStyle:          commontypes.FontStyle_SUBTITLE_1,
			DetailFormFieldInfoType: palFePb.DetailsFormField_OCCUPATION_TYPE,
			IsMandatory:             true,
			PlaceholderTextV2:       commontypes.GetTextFromStringFontColourFontStyle("Select employment type", "#333333", commontypes.FontStyle_SUBTITLE_1),
			UpdatedFieldStyle:       commontypes.GetTextFromStringFontColourFontStyle("", "#333333", commontypes.FontStyle_SUBTITLE_1),
			InFocusTextV2:           commontypes.GetTextFromStringFontColourFontStyle("EMPLOYMENT TYPE", "#B9B9B9", commontypes.FontStyle_OVERLINE_1),
		},
		{
			InFocusText:     "EMPLOYER NAME",
			PlaceholderText: "Add employer name",
			IsEditable:      true,
			DefaultValues: &palFePb.DetailsFormField_StringDefaultValue{
				StringDefaultValue: companyName,
			},
			ValueFontStyle:          commontypes.FontStyle_BODY_2,
			DetailFormFieldInfoType: palFePb.DetailsFormField_OCCUPATION_NAME,
			IsMandatory:             true,
			PlaceholderTextV2:       commontypes.GetTextFromStringFontColourFontStyle("Add employer name", "#878A8D", commontypes.FontStyle_SUBTITLE_1),
			UpdatedFieldStyle:       commontypes.GetTextFromStringFontColourFontStyle("", "#333333", commontypes.FontStyle_SUBTITLE_1),
			InFocusTextV2:           commontypes.GetTextFromStringFontColourFontStyle("EMPLOYER NAME", "#B9B9B9", commontypes.FontStyle_OVERLINE_1),
		},
		{
			InFocusText:     "WORK EMAIL",
			PlaceholderText: "Enter work email",
			IsEditable:      true,
			DefaultValues: &palFePb.DetailsFormField_StringDefaultValue{
				StringDefaultValue: workEmailId,
			},
			ValueFontStyle:          commontypes.FontStyle_BODY_2,
			DetailFormFieldInfoType: palFePb.DetailsFormField_WORK_EMAIL_ADDRESS,
			IsMandatory:             true,
			PlaceholderTextV2:       commontypes.GetTextFromStringFontColourFontStyle("Enter work email", "#878A8D", commontypes.FontStyle_SUBTITLE_1),
			UpdatedFieldStyle:       commontypes.GetTextFromStringFontColourFontStyle("", "#333333", commontypes.FontStyle_SUBTITLE_1),
			InFocusTextV2:           commontypes.GetTextFromStringFontColourFontStyle("WORK EMAIL", "#B9B9B9", commontypes.FontStyle_OVERLINE_1),
		},
		{
			InFocusText:     "MONTHLY INCOME",
			PlaceholderText: "Enter monthly income",
			IsEditable:      false,
			NavigationCta: &deeplinkPb.Cta{
				Type:     deeplinkPb.Cta_CUSTOM,
				Deeplink: deeplinkProvider.GetIncomeSelectionDeeplink(createMonthlyIncomeSelectionDeeplinkRequest(lh, defaultInitialMoney)),
			},
			IconUrl: "https://epifi-icons.pointz.in/preapprovedloan/employment_edit_pencil.png",
			DefaultValues: &palFePb.DetailsFormField_MoneyDefaultValue{
				MoneyDefaultValue: defaultInitialMoney,
			},
			ValueFontStyle:          commontypes.FontStyle_BODY_2,
			DetailFormFieldInfoType: palFePb.DetailsFormField_MONTHLY_INCOME,
			IsMandatory:             true,
			PlaceholderTextV2:       commontypes.GetTextFromStringFontColourFontStyle("Enter monthly income", "#878A8D", commontypes.FontStyle_SUBTITLE_1),
			UpdatedFieldStyle:       commontypes.GetTextFromStringFontColourFontStyle("", "#333333", commontypes.FontStyle_SUBTITLE_1),
			InFocusTextV2:           commontypes.GetTextFromStringFontColourFontStyle("MONTHLY INCOME", "#B9B9B9", commontypes.FontStyle_OVERLINE_1),
		},
	}
	employerAddressFormField := &palFePb.DetailsFormField{
		InFocusText:     companyAddressTitleString,
		PlaceholderText: "Enter work address",
		IsEditable:      false,
		IconUrl:         "https://epifi-icons.pointz.in/preapprovedloan/employment_edit_pencil.png",
		DefaultValues: &palFePb.DetailsFormField_AddressValue{
			AddressValue: &types.PostalAddress{},
		},
		ValueFontStyle:          commontypes.FontStyle_BODY_2,
		DetailFormFieldInfoType: palFePb.DetailsFormField_OFFICE_ADDRESS,
		IsMandatory:             true,
		PlaceholderTextV2:       commontypes.GetTextFromStringFontColourFontStyle("Enter work address", "#878A8D", commontypes.FontStyle_SUBTITLE_1),
		UpdatedFieldStyle:       commontypes.GetTextFromStringFontColourFontStyle("", "#333333", commontypes.FontStyle_SUBTITLE_1),
		InFocusTextV2:           commontypes.GetTextFromStringFontColourFontStyle(companyAddressTitleString, "#B9B9B9", commontypes.FontStyle_OVERLINE_1),
	}
	switch lh.GetVendor() {
	case palFeEnumsPb.Vendor_EPIFI_TECH:
		formFieldDetails = getFormFieldsForSalariedForEpifiTech(deeplinkProvider, defaultInitialMoney, companyName, workEmailId)
	case palFeEnumsPb.Vendor_LIQUILOANS:
		switch lh.GetLoanProgram() {
		case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL:
			fillLLEmploymentFormDetails(formFieldDetails, llEmploymentFormDetailsMap[palFeEnumsPb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL])
			formFieldDetails[1].IconUrl = "https://epifi-icons.pointz.in/preapprovedloan/address_fl_add_plus.png"
		default:
			// do nothing for now
		}
	case palFeEnumsPb.Vendor_ABFL:
		formFieldDetails = append(formFieldDetails, employerAddressFormField)
	default:
		// do nothing for now
	}
	return formFieldDetails
}

func fillLLEmploymentFormDetails(formFieldDetails []*palFePb.DetailsFormField, textMap [][]string) {
	for i, val := range formFieldDetails {
		val.InFocusText = textMap[i][0]
		val.PlaceholderText = textMap[i][1]
	}
}

func (s *Service) ConfirmRevisedLoanDetails(ctx context.Context, req *palFePb.ConfirmRevisedLoanDetailsRequest) (*palFePb.ConfirmRevisedLoanDetailsResponse, error) {
	helper.LogIfLoanHeaderNotPresent(ctx, req.GetLoanHeader(), "ConfirmRevisedLoanDetails")
	startTime := time.Now()
	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}
	if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		req.GetLoanHeader().LoanProgram = palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram)
	}
	// if loan program is set early salary, it will handle for liquiloans, else, it will take from hardcoded liquiloans for pl
	s.PushApiTriggeredEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameConfirmRevisedLoanDetails, "", req.GetLoanRequestId(), palEvents.LoanApplicationFlow, getEventsOwnership(palFeEnumsPb.Vendor_LIQUILOANS, req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())
	res := &palFePb.ConfirmRevisedLoanDetailsResponse{
		RespHeader: &header.ResponseHeader{},
	}
	err := s.RecordConsent(ctx, req.GetReq().GetAuth().GetActorId(), req.GetReq().GetAuth().GetDevice(), consentPb.ConsentType_FI_PRE_APPROVED_LOAN)
	if err != nil {
		logger.Error(ctx, "error while calling RecordConsent", zap.Error(err))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}
	beRes, err := s.preApprovedLoanClient.ConfirmRevisedLoanDetails(ctx, &preapprovedloanPbBe.ConfirmRevisedLoanDetailsRequest{
		LoanRequestId: req.GetLoanRequestId(),
		ActorId:       req.GetReq().GetAuth().GetActorId(),
		LoanHeader:    helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
	})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		logger.Error(ctx, "preApprovedLoanClient.ConfirmRevisedLoanDetails BE API failed",
			zap.Error(err),
		)
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}
	// if loan program is set early salary, it will handle for liquiloans, else, it will take from hardcoded liquiloans for pl
	s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameConfirmRevisedLoanDetails, time.Since(startTime), palEvents.StatusSuccess, "", "", req.GetLoanRequestId(), palEvents.LoanApplicationFlow, getEventsOwnership(palFeEnumsPb.Vendor_LIQUILOANS, req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())
	deeplinkProvider := s.getDeeplinkProvider(ctx, helper.GetPalFeVendorFromBe(beRes.GetLoanRequest().GetVendor()), helper.GetFeLoanProgramFromBe(beRes.GetLoanRequest().GetLoanProgram()))
	dl, dlErr := getStatusPollScreen(ctx, deeplinkProvider, beRes.GetLoanRequest().GetId())
	if dlErr != nil {
		logger.Error(ctx, "error while generating application status poll deeplink", zap.Error(dlErr))
		return nil, fmt.Errorf("error while generating application status poll deeplink: %w", dlErr)
	}
	res.Deeplink = dl
	res.RespHeader.Status = rpcPb.StatusOk()
	s.populateLODropOffFeedbackIdentifierInRespHeaderIfEnabled(res.GetRespHeader())
	return res, nil
}

// nolint: funlen
func (s *Service) ClientCallback(ctx context.Context, req *palFePb.ClientCallbackRequest) (*palFePb.ClientCallbackResponse, error) {
	startTime := time.Now()
	res := &palFePb.ClientCallbackResponse{
		RespHeader: &header.ResponseHeader{},
	}

	if req.GetClientCallbackReq() != "" {
		unmarshalReq := &palFePb.ClientCallbackRequest{}
		unmarshalReqErr := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal([]byte(req.GetClientCallbackReq()), unmarshalReq)
		if unmarshalReqErr != nil {
			logger.Error(ctx, "preApprovedLoanClient.ClientCallback unable to unmarshal request", zap.Error(unmarshalReqErr))
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
		req = fillClientCallbackReqWithUnmarshalledReq(req, unmarshalReq)
	}

	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}
	if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		req.GetLoanHeader().LoanProgram = palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram)
	}
	// if loan program is set early salary, it will handle for liquiloans, else, it will take from hardcoded liquiloans for pl
	s.PushApiTriggeredEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameClientCallback, "", req.GetLoanReqId(), palEvents.LoanApplicationFlow, getEventsOwnership(palFeEnumsPb.Vendor_LIQUILOANS, req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())

	// for backward compatible
	lrId := req.GetLoanReqId()
	if lrId == "" {
		lrId = req.GetLoanRequestId()
	}

	beReq := &preapprovedloanPbBe.ClientCallbackRequest{}
	identifier := beReq.GetIdentifier()
	switch req.GetIdentifier().(type) {
	case *palFePb.ClientCallbackRequest_StepOrchId:
		identifier = &preapprovedloanPbBe.ClientCallbackRequest_StepOrchId{StepOrchId: req.GetStepOrchId()}
	case *palFePb.ClientCallbackRequest_LoanReqId:
		identifier = &preapprovedloanPbBe.ClientCallbackRequest_LoanReqId{LoanReqId: lrId}
	}
	cbType, ok := palClientCallbackTypeFeToBeMap[req.GetType()]
	if !ok {
		logger.Error(ctx, fmt.Sprintf("preApprovedLoanClient.ClientCallback type map fetch failed, type: %s", req.GetType().String()))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}
	cbResult, ok := palClientCallbackResultFeToBeMap[req.GetResult()]
	if !ok {
		logger.Error(ctx, fmt.Sprintf("preApprovedLoanClient.ClientCallback result map fetch failed, type: %s", req.GetResult().String()))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}

	beRes, err := s.preApprovedLoanClient.ClientCallback(ctx, &preapprovedloanPbBe.ClientCallbackRequest{
		LoanRequestId:   lrId,
		ActorId:         req.GetReq().GetAuth().GetActorId(),
		Type:            cbType,
		Result:          cbResult,
		VendorCode:      req.GetVendorCode(),
		LoanHeader:      helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
		Identifier:      identifier,
		CallbackPayload: helper.GetBECallBackPayloadFromFECallBackPayload(req.GetCallbackPayload()),
		DevicePlatform:  req.GetReq().GetAuth().GetDevice().GetPlatform(),
	})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		logger.Error(ctx, "preApprovedLoanClient.ClientCallback BE API failed", zap.Error(err))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}

	deeplinkProvider := s.getDeeplinkProvider(ctx, req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())
	switch req.GetResult() {
	case palFePb.ClientCallbackRequest_SUCCESS, palFePb.ClientCallbackRequest_INITIATED:
		dl, dlErr := getStatusPollScreen(ctx, deeplinkProvider, lrId)
		if dlErr != nil {
			logger.Error(ctx, "error while generating application status poll deeplink", zap.Error(dlErr))
			return nil, fmt.Errorf("error while generating application status poll deeplink: %w", dlErr)
		}
		res.Deeplink = dl
	default:
		res.Deeplink = deeplinkProvider.GetLoanLandingInfo(ctx, deeplinkProvider.GetLoanHeader(), &provider.GetLoanLandingInfoRequest{ActorId: req.GetReq().GetAuth().GetActorId()})
	}
	// do not send deeplink if client callback response is for digio sdk gateway event
	// can come at any point in time when SDK is open and if we send deeplink to client when SDK is open than client app would crash
	// SUCCESS and FAILURE event only occurs when client tries to exit the SDK view, in that case we should send deeplink
	// this check acts as an additional guardrail beyond client side fix
	if req.GetCallbackPayload().GetMandatePayload().GetMandateSdkEvent().GetEventType() == palFePb.MandateSdkEvent_EVENT_TYPE_GATEWAY {
		res.Deeplink = nil
	}
	// if loan program is set early salary, it will handle for liquiloans, else, it will take from hardcoded liquiloans for pl
	s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameClientCallback, time.Since(startTime), palEvents.StatusSuccess, "", "", req.GetLoanReqId(), palEvents.LoanApplicationFlow, getEventsOwnership(palFeEnumsPb.Vendor_LIQUILOANS, req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())
	res.RespHeader.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) GetLoanAddresses(ctx context.Context, req *palFePb.GetLoanAddressesRequest) (*palFePb.GetLoanAddressesResponse, error) {
	startTime := time.Now()
	res := &palFePb.GetLoanAddressesResponse{
		RespHeader: &header.ResponseHeader{},
	}
	actorId := req.GetReq().GetAuth().GetActorId()
	actorResp, errResp := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: actorId})
	if err := epifigrpc.RPCError(actorResp, errResp); err != nil {
		logger.Error(ctx, "error in fetching actor by id", zap.Error(err))
		res.GetRespHeader().Status = rpcPb.StatusInternal()
		return res, nil
	}
	// if skipPreFillAddress flag is set to false in request, we do not need to fetch the user address list from BE.
	if !req.GetSkipAddressAutoFill() {
		entityId := actorResp.GetActor().GetEntityId()
		beReq := &userPb.GetAllAddressesRequest{
			UserId: entityId,
			Format: types.AddressFormat_USER_READABLE,
		}

		beRes, err := s.usersClient.GetAllAddresses(ctx, beReq)
		if rpcErr := epifigrpc.RPCError(beRes, err); rpcErr != nil {
			logger.Error(ctx, "error in fetching all addresses for user", zap.Error(rpcErr))
			res.GetRespHeader().Status = rpcPb.StatusInternal()
			return res, nil
		}

		if res.AddressesWithType, err = sanitizeLoanAddresses(ctx, beRes.GetAddresses()); err != nil {
			logger.Error(ctx, "error while sanitizing address with type", zap.Error(err))
			res.GetRespHeader().Status = rpcPb.StatusInternal()
			return res, nil
		}
	}
	res.AddressesWithTypeV2 = res.GetAddressesWithType()
	res.HintAddress = provider.GetText("Add current address", "#878A8D", commontypes.FontStyle_SUBTITLE_1)
	res.HintIcon = provider.GetVisualElementPng("https://epifi-icons.pointz.in/preapprovedloan/address_fl_add_plus.png", 24, 24)
	if len(res.GetAddressesWithTypeV2()) > 0 {
		res.HintIcon = provider.GetVisualElementPng("https://epifi-icons.pointz.in/preapprovedloan/address_fl_dropdown.png", 24, 24)
	}
	s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGetLoanAddresses, time.Since(startTime), palEvents.StatusSuccess, "", "", "", palEvents.LoanApplicationFlow, palEvents.OwnershipLiquiloansPL, nil)
	res.GetRespHeader().Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) GetCustomOfferDetails(ctx context.Context, req *palFePb.GetCustomOfferDetailsRequest) (*palFePb.GetCustomOfferDetailsResponse, error) {
	helper.LogIfLoanHeaderNotPresent(ctx, req.GetLoanHeader(), "GetCustomOfferDetails")
	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}
	if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		req.GetLoanHeader().LoanProgram = palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram)
	}
	startTime := time.Now()
	s.PushApiTriggeredEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGetCustomLoanOffer,
		req.GetOfferId(), "", palEvents.LoanApplicationFlow, getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())
	res := &palFePb.GetCustomOfferDetailsResponse{
		RespHeader: &header.ResponseHeader{},
	}
	deeplinkProvider := s.getDeeplinkProvider(ctx, req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())
	beRes, err := s.preApprovedLoanClient.GetOfferDetails(ctx, &preapprovedloanPbBe.GetOfferDetailsRequest{
		ActorId:        req.GetReq().GetAuth().GetActorId(),
		OfferId:        req.GetOfferId(),
		LoanAmount:     req.GetUserInput().GetLoanAmount().GetBeMoney(),
		TenureInMonths: req.GetUserInput().GetTenureInMonths(),
		LoanHeader:     helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
	})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		if beRes.GetStatus().GetCode() == rpcPb.StatusPermissionDenied().GetCode() {
			logger.Error(ctx, "preApprovedLoanClient.GetOfferDetails BE API permission denied", zap.Error(err))
			res.RespHeader.Status = rpcPb.StatusOk()
			res.Deeplink = deeplinkProvider.GetNoOfferLandingScreenDeepLink(ctx, deeplinkProvider.GetLoanHeader(), nil)
			return res, nil
		}
		logger.Error(ctx, "preApprovedLoanClient.GetOfferDetails BE API failed", zap.Error(err))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}

	s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGetCustomLoanOffer, time.Since(startTime),
		palEvents.StatusSuccess, "", req.GetOfferId(), "", palEvents.LoanApplicationFlow, getEventsOwnership(helper.GetPalFeVendorFromBe(beRes.GetVendor()), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())
	res.RespHeader.Status = rpcPb.StatusOk()
	res.Deeplink = deeplinkProvider.GetCustomOfferDetailsScreenDeepLink(deeplinkProvider.GetLoanHeader(), beRes)
	s.populateLODropOffFeedbackIdentifierInRespHeaderIfEnabled(res.GetRespHeader())

	s.triggerOfferViewedCallbackIfRequired(ctx, req.GetReq().GetAuth().GetActorId(), beRes.GetOfferId(), req.GetLoanHeader())

	return res, nil
}

// nolint: funlen
func (s *Service) VerifyDetails(ctx context.Context, req *palFePb.VerifyDetailsRequest) (*palFePb.VerifyDetailsResponse, error) {
	helper.LogIfLoanHeaderNotPresent(ctx, req.GetLoanHeader(), "VerifyDetails")
	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}
	if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		req.GetLoanHeader().LoanProgram = palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram)
	}
	startTime := time.Now()
	s.PushApiTriggeredEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameVerifyDetailsDetails,
		"", req.GetLoanReqId(), palEvents.LoanApplicationFlow, getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())
	res := &palFePb.VerifyDetailsResponse{
		RespHeader: &header.ResponseHeader{},
	}
	verifyDetailRes, err := s.preApprovedLoanClient.VerifyDetails(ctx, &preapprovedloanPbBe.VerifyDetailsRequest{
		Value:          req.GetValue(),
		ActorId:        req.GetReq().GetAuth().GetActorId(),
		LoanHeader:     helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
		DetailsType:    req.GetDetailsType(),
		ReqId:          req.GetLoanReqId(),
		LseId:          req.GetLoanStepExecutionId(),
		EmploymentType: req.GetEmploymentType(),
		UserInput:      helper.GetBeUserInputForVerifyDetails(req.GetUserInput()),
	})
	if verifyDetailRes.GetStatus().IsInvalidArgument() {
		res.GetRespHeader().Status = rpcPb.StatusInternal()
		val, ok := detailsTypeToErrorViewMsg[req.GetDetailsType()]
		if !ok {
			res.GetRespHeader().ErrorView = defaultErrorView
			return res, nil
		}
		// TODO(@Shivansh) update this with correct error msg
		res.GetRespHeader().ErrorView = &errorsPb.ErrorView{
			Type: errorsPb.ErrorViewType_BOTTOM_SHEET,
			Options: &errorsPb.ErrorView_BottomSheetErrorView{
				BottomSheetErrorView: &errorsPb.BottomSheetErrorView{
					Title:    val[0],
					Subtitle: val[1],
					Ctas: []*errorsPb.CTA{
						{Type: errorsPb.CTA_DONE, Text: "Retry"},
					},
				},
			},
		}
		return res, nil
	}
	// used only in case of unverified Aadhaar. Use details type condition to handle specific types only for inline errors
	if verifyDetailRes.GetStatus().IsPermissionDenied() {
		res.GetRespHeader().Status = rpcPb.StatusPermissionDenied()
		val, ok := detailsTypeToErrorViewMsg[req.GetDetailsType()]
		if !ok {
			res.GetRespHeader().ErrorView = defaultErrorView
			return res, nil
		}
		res.GetRespHeader().ErrorView = &errorsPb.ErrorView{
			Type: errorsPb.ErrorViewType_INLINE,
			Options: &errorsPb.ErrorView_InlineErrorView{
				InlineErrorView: &errorsPb.InlineErrorView{
					ErrorCode: val[0],
					Title:     val[1],
				},
			},
		}
		return res, nil
	}
	if te := epifigrpc.RPCError(verifyDetailRes, err); te != nil {
		res.GetRespHeader().Status = rpcPb.StatusInternal()
		return res, nil
	}

	// record the consent if needed
	val, ok := detailsTypeToConsent[req.GetDetailsType()]
	if ok {
		err = s.RecordConsent(ctx, req.GetReq().GetAuth().GetActorId(), req.GetReq().GetAuth().GetDevice(), val)
		if err != nil {
			logger.Error(ctx, "error while calling RecordConsent", zap.Error(err))
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
	}

	pollingMsg, iconUrl := "Please wait", ""
	pollingInfo, ok := screenTypeToCustomPollingInfo[req.GetDetailsType()]
	if ok {
		pollingMsg = pollingInfo[0]
		iconUrl = pollingInfo[1]
	}
	res.GetRespHeader().Status = rpcPb.StatusOk()
	s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameVerifyDetailsDetails, time.Since(startTime),
		palEvents.StatusSuccess, palEvents.GetSubstatusByVerifyDetailsType(req.GetDetailsType()), "", req.GetLoanReqId(), palEvents.LoanApplicationFlow, getEventsOwnership(helper.GetPalFeVendorFromBe(helper.GetBeVendorFromFe(req.GetLoanHeader().GetVendor())), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())
	deeplinkProvider := s.getDeeplinkProvider(ctx, req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())
	dl, dlErr := deeplinkProvider.GetApplicationStatusPollScreenWithCustomMsgDeepLink(ctx, deeplinkProvider.GetLoanHeader(), req.GetLoanReqId(), pollingMsg, "#333333", iconUrl)
	if dlErr != nil {
		logger.Error(ctx, "error while generating application status poll deeplink %v", zap.Error(dlErr))
		return nil, fmt.Errorf("error while generating application status deeplink %w", dlErr)
	}
	res.Deeplink = dl
	s.populateLODropOffFeedbackIdentifierInRespHeaderIfEnabled(res.GetRespHeader())
	return res, nil
}

func (s *Service) VerifyCkycDetails(ctx context.Context, req *palFePb.VerifyCkycDetailsRequest) (*palFePb.VerifyCkycDetailsResponse, error) {
	helper.LogIfLoanHeaderNotPresent(ctx, req.GetLoanHeader(), "VerifyCkycDetails")
	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}
	if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		req.GetLoanHeader().LoanProgram = palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram)
	}
	res := &palFePb.VerifyCkycDetailsResponse{
		RespHeader: &header.ResponseHeader{},
	}

	err := s.RecordConsent(ctx, req.GetReq().GetAuth().GetActorId(), req.GetReq().GetAuth().GetDevice(), consentPb.ConsentType_cKYC)
	if err != nil {
		logger.Error(ctx, "error while calling RecordConsent", zap.Error(err))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}

	verifyCkycDetailsRes, err := s.preApprovedLoanClient.VerifyCkycDetails(ctx, &preapprovedloanPbBe.VerifyCkycDetailsRequest{
		ActorId:    req.GetReq().GetAuth().GetActorId(),
		LoanHeader: helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
		LoanReqId:  req.GetLoanReqId(),
	})

	if te := epifigrpc.RPCError(verifyCkycDetailsRes, err); te != nil {
		res.GetRespHeader().Status = rpcPb.StatusInternal()
		return res, nil
	}

	res.GetRespHeader().Status = verifyCkycDetailsRes.GetStatus()
	deeplinkProvider := s.getDeeplinkProvider(ctx, req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())
	dl, dlErr := deeplinkProvider.GetApplicationStatusPollScreenDeepLink(ctx, deeplinkProvider.GetLoanHeader(), req.GetLoanReqId())
	if dlErr != nil {
		logger.Error(ctx, "error while generating application status poll deeplink", zap.Error(dlErr))
		return nil, fmt.Errorf("error while generating application status poll deeplink: %w", dlErr)
	}
	res.Deeplink = dl
	s.populateLODropOffFeedbackIdentifierInRespHeaderIfEnabled(res.GetRespHeader())

	return res, nil
}

// nolint:dupl
func (s *Service) GetMandateViewData(ctx context.Context, req *palFePb.GetMandateViewDataRequest) (*palFePb.GetMandateViewDataResponse, error) {
	helper.LogIfLoanHeaderNotPresent(ctx, req.GetLoanHeader(), "GetMandateViewData")
	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}
	if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		req.GetLoanHeader().LoanProgram = palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram)
	}

	res := &palFePb.GetMandateViewDataResponse{
		RespHeader: &header.ResponseHeader{Status: rpcPb.StatusOk()},
	}

	// record the mandate consent
	err := s.RecordConsent(ctx, req.GetReq().GetAuth().GetActorId(), req.GetReq().GetAuth().GetDevice(), consentPb.ConsentType_PL_IDFC_MANDATE)
	if err != nil {
		logger.Error(ctx, "error while calling RecordConsent for idfc mandate", zap.Error(err))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}

	webRes, err := s.preApprovedLoanClient.GetMandateViewData(ctx, &preapprovedloanPbBe.GetMandateViewDataRequest{
		ActorId:       req.GetReq().GetAuth().GetActorId(),
		LoanHeader:    helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
		LoanRequestId: req.GetLoanRequestId(),
	})
	if te := epifigrpc.RPCError(webRes, err); te != nil {
		if webRes.GetStatus().IsAlreadyExists() {
			logger.Error(ctx, "one mandate request already in progress", zap.Error(te))
			res.GetRespHeader().Status = rpcPb.StatusInternal()
			res.GetRespHeader().ErrorView = &errorsPb.ErrorView{
				Type: errorsPb.ErrorViewType_BOTTOM_SHEET,
				Options: &errorsPb.ErrorView_BottomSheetErrorView{BottomSheetErrorView: &errorsPb.BottomSheetErrorView{
					Title: "One mandate request already in progress. Please try after sometime.",
					TitleText: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{PlainString: "One mandate request already in progress. Please try after sometime."},
					},
				}},
			}
			return res, nil
		}
		logger.Error(ctx, "error while getting response from BE", zap.Error(te))
		res.GetRespHeader().Status = rpcPb.StatusInternal()
		return res, nil
	}

	s.populateLODropOffFeedbackIdentifierInRespHeaderIfEnabled(res.GetRespHeader())

	res.Base64EncodedHtml = webRes.GetBase64EncodedHtml()
	return res, nil
}

func (s *Service) AddPanAndDobData(ctx context.Context, req *palFePb.AddPanAndDobDataRequest) (*palFePb.AddPanAndDobDataResponse, error) {
	res := &palFePb.AddPanAndDobDataResponse{
		RespHeader: &header.ResponseHeader{},
	}

	// record the consent of the user for PAN and DOB submission
	err := s.RecordConsent(ctx, req.GetReq().GetAuth().GetActorId(), req.GetReq().GetAuth().GetDevice(), consentPb.ConsentType_PL_LL_PAN_DOB)
	if err != nil {
		logger.Error(ctx, "error while calling RecordConsent", zap.Error(err))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}

	beRes, err := s.preApprovedLoanClient.AddPanAndDobData(ctx, &preapprovedloanPbBe.AddPanAndDobDataRequest{
		LoanHeader: helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
		ActorId:    req.GetReq().GetAuth().GetActorId(),
		Pan:        req.GetPan(),
		Dob:        datetime.DateFromString(req.GetDob()),
		ReqId:      req.GetLoanRequestId(),
	})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		logger.Error(ctx, "error in AddPanAndDob BE API", zap.Error(te))
		res.GetRespHeader().Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}

	res.GetRespHeader().Status = rpcPb.StatusOk()
	deeplinkProvider := s.getDeeplinkProvider(ctx, req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())
	// TDOO(@Shivansh) edit the polling screen with correct msg and icon
	dl, dlErr := deeplinkProvider.GetApplicationStatusPollScreenWithCustomMsgDeepLink(ctx, deeplinkProvider.GetLoanHeader(), req.GetLoanRequestId(), "Please wait", "#333333", "")
	if dlErr != nil {
		logger.Error(ctx, "error while generating application status poll deeplink %v", zap.Error(dlErr))
		return nil, fmt.Errorf("error while generating application status deeplink %w", dlErr)
	}
	res.Deeplink = dl
	s.populateLODropOffFeedbackIdentifierInRespHeaderIfEnabled(res.GetRespHeader())
	return res, nil
}

// nolint: funlen
func (s *Service) AddNameAndGender(ctx context.Context, req *palFePb.AddNameAndGenderRequest) (*palFePb.AddNameAndGenderResponse, error) {
	startTime := time.Now()

	// add a validation for name
	req.Name = strings.TrimSpace(req.GetName())
	errView, err := validateUserName(ctx, req.GetName())
	if err != nil {
		logger.Error(ctx, "error in validating user name", zap.Error(err))
		return &palFePb.AddNameAndGenderResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpcPb.StatusInternal(),
				ErrorView: defaultErrorView,
			},
		}, nil
	}
	if errView != nil {
		return &palFePb.AddNameAndGenderResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpcPb.StatusInvalidArgument(),
				ErrorView: errView,
			},
		}, nil
	}

	res := &palFePb.AddNameAndGenderResponse{
		RespHeader: &header.ResponseHeader{},
	}
	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}
	if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		req.GetLoanHeader().LoanProgram = palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram)
	}
	// if loan program is set early salary, it will handle for liquiloans, else, it will take from hardcoded liquiloans for pl
	s.PushApiTriggeredEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameAddNameAndGender, "", req.GetLoanRequestId(), palEvents.LoanEligibilityFlow, getEventsOwnership(palFeEnumsPb.Vendor_LIQUILOANS, req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())

	beRes, err := s.preApprovedLoanClient.AddNameAndGender(ctx, &preapprovedloanPbBe.AddNameAndGenderRequest{
		LoanHeader: helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
		ActorId:    req.GetReq().GetAuth().GetActorId(),
		LoanReqId:  req.GetLoanRequestId(),
		Name:       req.GetName(),
		Gender:     req.GetGender(),
	})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		logger.Error(ctx, "preApprovedLoanClient.AddNameAndGender BE API failed", zap.Error(te))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}

	deeplinkProvider := s.getDeeplinkProvider(ctx, req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())
	dl, dlErr := deeplinkProvider.GetApplicationStatusPollScreenDeepLink(ctx, deeplinkProvider.GetLoanHeader(), req.GetLoanRequestId())
	if dlErr != nil {
		logger.Error(ctx, "error while generating application status poll deeplink", zap.Error(dlErr))
		return nil, fmt.Errorf("error while generating application status poll deeplink: %w", dlErr)
	}
	res.Deeplink = dl
	s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameAddNameAndGender, time.Since(startTime), palEvents.StatusSuccess, "", "", req.GetLoanRequestId(), palEvents.LoanEligibilityFlow, getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())
	s.populateLODropOffFeedbackIdentifierInRespHeaderIfEnabled(res.GetRespHeader())
	res.RespHeader.Status = rpcPb.StatusOk()
	return res, nil
}

// nolint:dupl
func (s *Service) CheckLoanEligibility(ctx context.Context, req *palFePb.CheckLoanEligibilityRequest) (*palFePb.CheckLoanEligibilityResponse, error) {
	startTime := time.Now()
	res := &palFePb.CheckLoanEligibilityResponse{
		RespHeader: &header.ResponseHeader{},
	}
	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}
	if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		req.GetLoanHeader().LoanProgram = palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram)
	}
	// if loan program is set early salary, it will handle for liquiloans, else, it will take from hardcoded liquiloans for pl
	s.PushApiTriggeredEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameCheckLoanEligibility, "", "", palEvents.LoanEligibilityFlow, getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())
	s.sendATLJourneyStartedEventIfApplicable(ctx, req.GetLoanHeader(), req.GetReq().GetAuth().GetActorId())

	beRes, err := s.preApprovedLoanClient.CheckLoanEligibility(ctx, &preapprovedloanPbBe.CheckLoanEligibilityRequest{
		LoanHeader:               helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
		ActorId:                  req.GetReq().GetAuth().GetActorId(),
		CancelCurrentLoanRequest: req.GetCancelCurrentLoanRequest(),
	})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		if beRes.GetStatus().IsAlreadyExists() || beRes.GetStatus().IsFailedPrecondition() {
			res.RespHeader.Status = rpcPb.StatusOk()
			appVersionConstraintData := release.NewAppVersionConstraintData(s.genconf.Lending().PreApprovedLoan().AutoCancelCurrentLrConfig())
			isAppVersionCompatible, appVerErr := release.NewAppVersionConstraint().Evaluate(ctx, appVersionConstraintData, nil)
			if appVerErr != nil {
				logger.Error(ctx, "Error evaluating app version constraint", zap.Error(appVerErr))
				res.RespHeader.Status = rpcPb.StatusInternal()
				res.RespHeader.ErrorView = defaultErrorView
				return res, nil
			}

			res.Deeplink, err = baseprovider.GetApplyForLoanAlreadyExistScreen(&baseprovider.GetApplyForLoanAlreadyExistScreenRequest{
				ActiveLr:                 beRes.GetActiveLoanRequest(),
				ActiveLse:                beRes.GetActiveLse(),
				ActiveAccountId:          beRes.GetActiveLoanAccountId(),
				IsCancelSupported:        isAppVersionCompatible,
				CheckLoanEligibilityReq:  req,
				IsCancellationRestricted: beRes.GetStatus().IsPermissionDenied(),
			})
			if err != nil {
				logger.Error(ctx, "error while calling GetApplyForLoanAlreadyExistScreen", zap.Error(err))
				res.RespHeader.Status = rpcPb.StatusInternal()
				res.RespHeader.ErrorView = bottomSheetErrorView
				return res, nil
			}
			subStatus := getEventsOwnership(helper.GetPalFeVendorFromBe(beRes.GetActiveLoanRequest().GetVendor()), helper.GetFeLoanProgramFromBe(beRes.GetActiveLoanRequest().GetLoanProgram()))
			s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameCheckLoanEligibility, time.Since(startTime), palEvents.StatusAlreadyExists, subStatus, "", "", palEvents.LoanEligibilityFlow, getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())
			return res, nil
		}
		logger.Error(ctx, "preApprovedLoanClient.CheckLoanEligibility BE API failed", zap.Error(err))
		if beRes.GetStatus().IsPermissionDenied() {
			res.Deeplink = s.getNoOfferDeeplink(ctx, s.getDeeplinkProvider(ctx, palFeEnumsPb.Vendor_VENDOR_UNSPECIFIED, palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED), nil, nil, req.GetReq().GetAuth().GetActorId())
			res.RespHeader.Status = rpcPb.StatusOk()
			return res, nil
		}
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}

	deeplinkProvider := s.getDeeplinkProvider(ctx, helper.GetPalFeVendorFromBe(beRes.GetRespHeader().GetVendor()), helper.GetFeLoanProgramFromBe(beRes.GetRespHeader().GetLoanProgram()))
	dl, dlErr := deeplinkProvider.GetApplicationStatusPollScreenDeepLink(ctx, deeplinkProvider.GetLoanHeader(), beRes.GetLoanRequestId())
	if dlErr != nil {
		logger.Error(ctx, "error while generating application status poll deeplink", zap.Error(dlErr))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}
	res.Deeplink = dl
	status := palEvents.StatusSuccess
	subStatus := ""
	if req.GetCancelCurrentLoanRequest() {
		status = palEvents.StatusCancelledAndStartedNew
		subStatus = getEventsOwnership(helper.GetPalFeVendorFromBe(beRes.GetActiveLoanRequest().GetVendor()), helper.GetFeLoanProgramFromBe(beRes.GetActiveLoanRequest().GetLoanProgram()))
	}
	s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameCheckLoanEligibility, time.Since(startTime), status, subStatus, "", beRes.GetLoanRequestId(), palEvents.LoanEligibilityFlow, getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())
	res.RespHeader.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) FetchCreditReport(ctx context.Context, req *palFePb.FetchCreditReportRequest) (*palFePb.FetchCreditReportResponse, error) {
	res := &palFePb.FetchCreditReportResponse{
		RespHeader: &header.ResponseHeader{},
	}

	beRes, err := s.preApprovedLoanClient.FetchCreditReport(ctx, &preapprovedloanPbBe.FetchCreditReportRequest{
		LoanHeader:          helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
		ActorId:             req.GetReq().GetAuth().GetActorId(),
		LoanReqId:           req.GetLoanRequestId(),
		LoanStepExecutionId: req.GetLoanStepExecutionId(),
		CreditReportVendor:  req.GetCreditReportVendor(),
	})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		logger.Error(ctx, "error in FetchCreditReport BE API", zap.Error(te))
		res.GetRespHeader().Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}

	res.GetRespHeader().Status = rpcPb.StatusOk()
	deeplinkProvider := s.getDeeplinkProvider(ctx, req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())
	dl, dlErr := deeplinkProvider.GetApplicationStatusPollScreenDeepLink(ctx, req.GetLoanHeader(), req.GetLoanRequestId())
	if dlErr != nil {
		logger.Error(ctx, "error while generating application status poll deeplink", zap.Error(dlErr))
		return nil, fmt.Errorf("error while generating application status poll deeplink: %w", dlErr)
	}
	res.Deeplink = dl
	s.populateLODropOffFeedbackIdentifierInRespHeaderIfEnabled(res.GetRespHeader())
	return res, nil
}

func (s *Service) AddBankingDetails(ctx context.Context, req *palFePb.AddBankingDetailsRequest) (*palFePb.AddBankingDetailsResponse, error) {
	helper.LogIfLoanHeaderNotPresent(ctx, req.GetLoanHeader(), "AddBankingDetails")
	startTime := time.Now()
	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}
	if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		req.GetLoanHeader().LoanProgram = palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram)
	}
	// if loan program is set early salary, it will handle for liquiloans, else, it will take from hardcoded liquiloans for pl
	s.PushApiTriggeredEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameAddBankingDetails, "", req.GetLoanRequestId(), palEvents.LoanApplicationFlow, getEventsOwnership(palFeEnumsPb.Vendor_LIQUILOANS, req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())

	res := &palFePb.AddBankingDetailsResponse{
		RespHeader: &header.ResponseHeader{},
	}
	bankingDetails := &preapprovedloanPbBe.AddBankingDetailsRequest_BankingDetails{
		AccountNumber:     strings.TrimSpace(req.GetBankingDetails().GetAccountNumber()),
		AccountHolderName: strings.TrimSpace(req.GetBankingDetails().GetAccountHolderName()),
		IfscCode:          strings.TrimSpace(req.GetBankingDetails().GetIfscCode()),
		BankName:          strings.TrimSpace(req.GetBankingDetails().GetBankName()),
	}
	switch req.GetBankingDetails().GetAccountNum().(type) {
	case *palFePb.AddBankingDetailsRequest_BankingDetails_CompleteAccountNumber:
		bankingDetails.AccountNum = &preapprovedloanPbBe.AddBankingDetailsRequest_BankingDetails_CompleteAccountNumber{
			CompleteAccountNumber: strings.TrimSpace(req.GetBankingDetails().GetCompleteAccountNumber()),
		}
	case *palFePb.AddBankingDetailsRequest_BankingDetails_AccountNumberPrefix:
		bankingDetails.AccountNum = &preapprovedloanPbBe.AddBankingDetailsRequest_BankingDetails_AccountNumberPrefix{
			AccountNumberPrefix: strings.TrimSpace(req.GetBankingDetails().GetAccountNumberPrefix()),
		}
	default:
		// TODO(Brijesh): Replace below handling with error, once support for old app versions are deprecated
		logger.Info(ctx, "no account number type found, defaulting to complete account number")
		bankingDetails.AccountNum = &preapprovedloanPbBe.AddBankingDetailsRequest_BankingDetails_CompleteAccountNumber{
			CompleteAccountNumber: strings.TrimSpace(req.GetBankingDetails().GetAccountNumber()),
		}
	}
	addRes, err := s.preApprovedLoanClient.AddBankingDetails(ctx, &preapprovedloanPbBe.AddBankingDetailsRequest{
		LoanRequestId:  req.GetLoanRequestId(),
		ActorId:        req.GetReq().GetAuth().GetActorId(),
		LoanHeader:     helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
		BankingDetails: bankingDetails,
		LseId:          req.GetLseId(),
	})
	if te := epifigrpc.RPCError(addRes, err); te != nil {
		logger.Error(ctx, "error in persisting banking details", zap.Error(te))
		if addRes.GetStatus().GetCode() == uint32(preapprovedloanPbBe.AddBankingDetailsResponse_BANK_NOT_SUPPORTED) {
			res.GetRespHeader().Status = rpcPb.StatusInvalidArgument()
			res.RespHeader.ErrorView = getBankNotSupportedBottomSheetErrView()
			return res, nil
		}
		res.GetRespHeader().Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = bottomSheetErrorView
		return res, nil
	}
	// if loan program is set early salary, it will handle for liquiloans, else, it will take from hardcoded liquiloans for pl
	s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameAddBankingDetails, time.Since(startTime), palEvents.StatusSuccess, "", "", req.GetLoanRequestId(), palEvents.LoanApplicationFlow, getEventsOwnership(palFeEnumsPb.Vendor_LIQUILOANS, req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())
	res.GetRespHeader().Status = rpcPb.StatusOk()
	// if deeplink is present in be resp then use that deeplink, else construct deeplink at FE
	if addRes.GetDeeplink() != nil {
		res.Deeplink = addRes.GetDeeplink()
	} else {
		deeplinkProvider := s.getDeeplinkProvider(ctx, req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())
		dl, dlErr := deeplinkProvider.GetApplicationStatusPollScreenDeepLink(ctx, req.GetLoanHeader(), req.GetLoanRequestId())
		if dlErr != nil {
			logger.Error(ctx, "error while generating application status poll deeplink", zap.Error(dlErr))
			return nil, fmt.Errorf("error while generating application status poll deeplink: %w", dlErr)
		}
		res.Deeplink = dl
	}

	s.populateLODropOffFeedbackIdentifierInRespHeaderIfEnabled(res.GetRespHeader())
	return res, nil
}

func (s *Service) SubmitReviewLoanDetails(ctx context.Context, req *palFePb.SubmitReviewLoanDetailsRequest) (*palFePb.SubmitReviewLoanDetailsResponse, error) {
	helper.LogIfLoanHeaderNotPresent(ctx, req.GetLoanHeader(), "SubmitReviewLoanDetails")
	startTime := time.Now()
	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}
	if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		req.GetLoanHeader().LoanProgram = palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram)
	}
	s.PushApiTriggeredEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameSubmitReviewLoanDetails, "", req.GetLoanRequestId(), palEvents.LoanEligibilityFlow, getEventsOwnership(palFeEnumsPb.Vendor_LIQUILOANS, req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())

	res := &palFePb.SubmitReviewLoanDetailsResponse{
		RespHeader: &header.ResponseHeader{},
	}

	consentErr := s.RecordConsents(ctx, req.GetReq().GetAuth().GetActorId(), req.GetReq().GetAuth().GetDevice(), []consentPb.ConsentType{
		consentPb.ConsentType_FI_PRE_APPROVED_LOAN,
		consentPb.ConsentType_PL_LL_CREDIT_REPORT_DATA_PULL,
	}, req.GetLoanRequestId())
	if consentErr != nil {
		logger.Error(ctx, "error while calling RecordConsents", zap.Error(consentErr))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = bottomSheetErrorView
		return res, nil
	}

	beRes, err := s.preApprovedLoanClient.SubmitReviewLoanDetails(ctx, &preapprovedloanPbBe.SubmitReviewLoanDetailsRequest{
		LoanRequestId: req.GetLoanRequestId(),
		ActorId:       req.GetReq().GetAuth().GetActorId(),
		LoanHeader:    helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
	})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		logger.Error(ctx, "error in SubmitReviewLoanDetails BE api", zap.Error(te))
		res.GetRespHeader().Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = bottomSheetErrorView
		return res, nil
	}

	s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameSubmitReviewLoanDetails, time.Since(startTime), palEvents.StatusSuccess, "", "", req.GetLoanRequestId(), palEvents.LoanEligibilityFlow, getEventsOwnership(palFeEnumsPb.Vendor_LIQUILOANS, req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())
	res.GetRespHeader().Status = rpcPb.StatusOk()
	deeplinkProvider := s.getDeeplinkProvider(ctx, req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())
	dl, dlErr := deeplinkProvider.GetApplicationStatusPollScreenDeepLink(ctx, req.GetLoanHeader(), req.GetLoanRequestId())
	if dlErr != nil {
		logger.Error(ctx, "error while generating application status poll deeplink", zap.Error(dlErr))
		return nil, fmt.Errorf("error while generating application status poll deeplink: %w", dlErr)
	}
	res.Deeplink = dl
	return res, nil
}

func (s *Service) isSegmentMember(ctx context.Context, actorId string) bool {
	if s.genconf.Lending().PreApprovedLoan().InstantCashSegmentId() != "" {
		segRes, err := s.segmentClient.IsMember(ctx, &segmentPb.IsMemberRequest{
			ActorId:    actorId,
			SegmentIds: []string{s.genconf.Lending().PreApprovedLoan().InstantCashSegmentId()},
			LatestBy:   timestampPb.Now(),
		})
		if rpcErr := epifigrpc.RPCError(segRes, err); rpcErr != nil {
			// log error and move forward as default values will be shown
			logger.Error(ctx, "error while checking if user belongs to pre approved loan segment", zap.Error(rpcErr))
		}
		if segRes != nil &&
			segRes.GetSegmentMembershipMap()[s.genconf.Lending().PreApprovedLoan().InstantCashSegmentId()].GetSegmentStatus() == segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND {
			return segRes.GetSegmentMembershipMap()[s.genconf.Lending().PreApprovedLoan().InstantCashSegmentId()].GetIsActorMember()
		}
	}
	return false
}

func (s *Service) GetAcqToLendLandingScreen(ctx context.Context, req *palFePb.GetAcqToLendLandingScreenRequest) (*palFePb.GetAcqToLendLandingScreenResponse, error) {
	helper.LogIfLoanHeaderNotPresent(ctx, req.GetLoanHeader(), "GetAcqToLendLandingScreen")
	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}

	res := &palFePb.GetAcqToLendLandingScreenResponse{
		RespHeader: &header.ResponseHeader{},
	}

	deeplinkProvider := s.getDeeplinkProvider(ctx, req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())
	// we are reusing acqToLendLandingScreen for common eligibility landing screen for A2L users
	if palHelper.GetLoanTypeFromLoanProgram(helper.GetBeLoanProgramFromFe(req.GetLoanHeader().GetLoanProgram())) == preapprovedloanPbBe.LoanType_LOAN_TYPE_PERSONAL && req.GetLoanHeader().GetLoanProgram() != palFeEnumsPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND {
		// this API will only be called for A2L users, that is why we are passing isA2lUser as true
		dl, err := deeplinkProvider.CheckLoanEligibilityScreenDeeplink(ctx, req.GetReq().GetAuth().GetActorId(), req.GetLoanHeader())
		if err != nil {
			logger.Error(ctx, "error in CheckLoanEligibilityScreenDeeplink", zap.Error(err))
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
		res.GetRespHeader().Status = rpcPb.StatusOk()
		res.Deeplink = dl
		return res, nil
	}

	logger.Debug(ctx, "preapprovedloan.GetAcqToLendLandingScreen invoked")
	beRes, err := s.preApprovedLoanClient.GetDashboardV2(ctx, &preapprovedloanPbBe.GetDashboardRequest{
		ActorId:    req.GetReq().GetAuth().GetActorId(),
		LoanHeader: helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
	})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		logger.Error(ctx, "preApprovedLoanClient.GetDashboardV2 BE API failed", zap.Error(te))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}
	res.GetRespHeader().Status = rpcPb.StatusOk()
	res.Deeplink, err = deeplinkProvider.GetNextDeeplink(ctx, deeplinkProvider.GetLoanHeader(), beRes, false, req.GetReq().GetAuth().GetActorId(), s.isPostDisbursalFlowEnabled(ctx, req.GetReq().GetAuth().GetActorId(), deeplinkProvider.GetLoanHeader()))
	if err != nil {
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}
	return res, nil
}

func (s *Service) callAlternateLandingRpc(ctx context.Context, lh *palFeEnumsPb.LoanHeader, beRes *preapprovedloanPbBe.GetLandingInfoV2Response, actorId string,
	startTime time.Time, loanOfferId, loanRequestId string, eventData *palFeEnumsPb.EventData) (bool, *deeplinkPb.Deeplink, error) {
	// deeplink provider is only implemented for acq to lend, liquiloans only for now
	deeplinkProvider := s.getDeeplinkProvider(ctx, lh.GetVendor(), lh.GetLoanProgram())
	ownership := getEventsOwnership(deeplinkProvider.GetLoanHeader().GetVendor(), deeplinkProvider.GetLoanHeader().GetLoanProgram())
	switch lh.GetLoanProgram() {
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND:
		switch {
		case beRes.GetActiveLoanAccount() != nil:
			var dl *deeplinkPb.Deeplink
			var dlErr error
			if s.isPostDisbursalFlowEnabled(ctx, actorId, lh) {
				dl = deeplinkV3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LOANS_DASHBOARD_SCREEN, &preapprovedloans.LoansDashboardScreenOptions{
					LoanHeader: deeplinkProvider.GetLoanHeader(),
				})
			} else {
				dl, dlErr = deeplinkProvider.GetLoanDashboardScreenDeepLink(ctx, deeplinkProvider.GetLoanHeader(), actorId)
				if dlErr != nil {
					logger.Error(ctx, "failed to get loans dashboard deeplink from callAlternateLandingRpc", zap.Error(dlErr), zap.String(logger.ACTOR_ID_V2, actorId),
						zap.String(logger.LOAN_REQUEST_ID, beRes.GetActiveLoanAccount().GetId()))

					return true, nil, dlErr
				}
			}
			s.PushApiResponseEvent(ctx, actorId, palEvents.ApiNameLandingInfo, time.Since(startTime), palEvents.StatusSuccess, palEvents.SubStatusLoanDashboard, loanOfferId, loanRequestId, palEvents.LoanManagementFlow, ownership, eventData)
			return true, dl, nil
		case beRes.GetStatus().GetCode() == rpcPb.StatusPermissionDenied().GetCode():
			s.PushApiResponseEvent(ctx, actorId, palEvents.ApiNameLandingInfo, time.Since(startTime), palEvents.StatusSuccess, palEvents.SubStatusPermissionDenied, loanOfferId, loanRequestId, palEvents.LoanManagementFlow, ownership, eventData)
			return true, deeplinkProvider.GetNoOfferLandingScreenDeepLink(ctx, deeplinkProvider.GetLoanHeader(), &provider.NoOfferLandingInfoRequest{ActorId: actorId}), nil
		default:
			dl, err := deeplinkProvider.CheckLoanEligibilityScreenDeeplink(ctx, actorId, deeplinkProvider.GetLoanHeader())
			if err != nil {
				return true, dl, err
			}
			s.PushApiResponseEvent(ctx, actorId, palEvents.ApiNameLandingInfo, time.Since(startTime), palEvents.StatusSuccess, palEvents.SubStatusLoanEligibility, loanOfferId, loanRequestId, palEvents.LoanEligibilityFlow, ownership, eventData)
			return true, dl, nil
			// this will return Screen_PL_ACQ_TO_LEND_LANDING_SCREEN and client will call GetAcqToLendLandingScreen() to get screen options
		}
	default:
		return false, nil, nil
	}
}

func isDevicePlatformVersionValidForDashboardV2(ctx context.Context, releaseConfig *cfg.PlatformVersionCheck) bool {
	platform, version := epificontext.AppPlatformAndVersion(ctx)
	if version == 0 || platform == commontypes.Platform_PLATFORM_UNSPECIFIED {
		return false
	}
	if (releaseConfig.IsEnableOnAndroid && platform == commontypes.Platform_ANDROID && version >= releaseConfig.MinAndroidVersion) ||
		(releaseConfig.IsEnableOnIos && platform == commontypes.Platform_IOS && version >= releaseConfig.MinIosVersion) {
		return true
	}
	return false
}

func (s *Service) InitiateSiSetup(ctx context.Context, req *palFePb.InitiateSiSetupRequest) (*palFePb.InitiateSiSetupResponse, error) {
	helper.LogIfLoanHeaderNotPresent(ctx, req.GetLoanHeader(), "InitiateSiSetup")
	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}
	resp, err := s.preApprovedLoanClient.InitiateSiSetup(ctx, &preapprovedloanPbBe.InitiateSiSetupRequest{
		LoanHeader:    helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
		ActorId:       req.GetReq().GetAuth().GetActorId(),
		LoanAccountId: req.GetLoanAccountId(),
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		logger.Error(ctx, "error while initiating si setup", zap.Error(te))
		return &palFePb.InitiateSiSetupResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpcPb.StatusInternal(),
				ErrorView: defaultErrorView,
			},
		}, nil
	}
	deeplinkProvider := s.getDeeplinkProvider(ctx, req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())
	dl, dlErr := deeplinkProvider.GetApplicationStatusPollScreenDeepLink(ctx, deeplinkProvider.GetLoanHeader(), resp.GetLoanRequestId())
	if dlErr != nil {
		logger.Error(ctx, "error while generating application status poll deeplink", zap.Error(dlErr))
		return nil, fmt.Errorf("error while generating application status poll deeplink: %w", dlErr)
	}
	res := &palFePb.InitiateSiSetupResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpcPb.StatusOk(),
		},
		Deeplink: dl,
	}
	s.populateLODropOffFeedbackIdentifierInRespHeaderIfEnabled(res.GetRespHeader())

	return res, nil
}

// This method first checks if segment release flag is enabled. If it is enabled then it checks if actor is present in the release segment. If not then feature is not released to the actor.
// If segment release flag is disabled then actor eligibility is checked using release evaluator.
func (s *Service) isActorEligibleForSecuredLoans(ctx context.Context, actorId string) (bool, error) {
	if s.genconf.Lending().SecuredLoanParams().ReleasedSegmentDetails().IsEnabled() {
		segRes, err := s.segmentClient.IsMemberOfExpressions(ctx, &segmentPb.IsMemberOfExpressionsRequest{
			ActorId:              actorId,
			SegmentIdExpressions: []string{s.genconf.Lending().SecuredLoanParams().ReleasedSegmentDetails().Expression()},
		})
		if rpcErr := epifigrpc.RPCError(segRes, err); rpcErr != nil {
			return false, fmt.Errorf("error in segmentClient.IsMemberOfExpressions rpc : %w", rpcErr)
		}
		if segRes == nil ||
			segRes.GetSegmentExpressionMembershipMap()[s.genconf.Lending().SecuredLoanParams().ReleasedSegmentDetails().Expression()].GetSegmentExpressionStatus() != segmentPb.SegmentExpressionStatus_OK ||
			!segRes.GetSegmentExpressionMembershipMap()[s.genconf.Lending().SecuredLoanParams().ReleasedSegmentDetails().Expression()].GetIsActorMember() {
			return false, nil
		}
	}
	isEligible, err := s.releaseEvaluator.Evaluate(ctx,
		release.NewCommonConstraintData(types.Feature_FEATURE_SECURED_LOANS).WithActorId(actorId))
	if err != nil {
		return false, fmt.Errorf("error in release evaluator for feature: %s, error: %w", types.Feature_FEATURE_SECURED_LOANS, err)
	}
	return isEligible, nil
}

// nolint:dupl
func (s *Service) GetMandateViewDataV2(ctx context.Context, req *palFePb.GetMandateViewDataV2Request) (*palFePb.GetMandateViewDataV2Response, error) {
	helper.LogIfLoanHeaderNotPresent(ctx, req.GetLoanHeader(), "GetMandateViewDataV2")
	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}
	if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		req.GetLoanHeader().LoanProgram = palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram)
	}
	res := &palFePb.GetMandateViewDataV2Response{
		RespHeader: &header.ResponseHeader{Status: rpcPb.StatusOk()},
	}

	authType := rpEnachEnumsPb.EnachRegistrationAuthMode_ENACH_REGISTRATION_AUTH_MODE_UNSPECIFIED
	if req.GetAuthenticationMethod() != "" {
		val, ok := rpEnachEnumsPb.EnachRegistrationAuthMode_value[req.GetAuthenticationMethod()]
		if !ok {
			logger.Error(ctx, "unidentified authentication method string", zap.String("authentication_method", req.GetAuthenticationMethod()))
			res.GetRespHeader().Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
		authType = rpEnachEnumsPb.EnachRegistrationAuthMode(val)
	}

	resp, err := s.preApprovedLoanClient.GetMandateViewDataV2(ctx, &preapprovedloanPbBe.GetMandateViewDataV2Request{
		ActorId:       req.GetReq().GetAuth().GetActorId(),
		LoanHeader:    helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
		LoanRequestId: req.GetLoanRequestId(),
		LseId:         req.GetLseId(),
		MandateDetails: &preapprovedloanPbBe.GetMandateViewDataV2Request_MandateDetails{
			BankAccountDetails: req.GetBankAccountDetails(),
			AuthType:           authType,
		},
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		if resp.GetStatus().IsAlreadyExists() {
			errMsg := resp.GetUserFacingErrMsg()
			if errMsg == "" {
				errMsg = "One mandate request already in progress. Please try after sometime."
			}
			logger.Error(ctx, "one mandate request already in progress", zap.Error(te))
			res.GetRespHeader().Status = rpcPb.StatusInternal()
			res.GetRespHeader().ErrorView = &errorsPb.ErrorView{
				Type: errorsPb.ErrorViewType_BOTTOM_SHEET,
				Options: &errorsPb.ErrorView_BottomSheetErrorView{
					BottomSheetErrorView: &errorsPb.BottomSheetErrorView{
						// populate message in title since iOS has not implemented TitleText yet
						Title: errMsg,
						TitleText: &commontypes.Text{
							DisplayValue: &commontypes.Text_PlainString{PlainString: errMsg},
							FontColor:    pkgColors.ColorSnow,
						},
						Ctas: []*errorsPb.CTA{
							{
								Type: errorsPb.CTA_DONE,
								Text: "Ok",
							},
						},
					},
				},
			}
			return res, nil
		}
		if resp.GetStatus().IsResourceExhausted() {
			res.GetRespHeader().Status = rpcPb.StatusInternal()
			res.GetRespHeader().ErrorView = &errorsPb.ErrorView{
				Type: errorsPb.ErrorViewType_BOTTOM_SHEET,
				Options: &errorsPb.ErrorView_BottomSheetErrorView{
					BottomSheetErrorView: &errorsPb.BottomSheetErrorView{
						Title:    "Maximum attempts breached",
						Subtitle: "Maximum tries exhausted for setting up mandate",
						Ctas: []*errorsPb.CTA{
							{
								Type: errorsPb.CTA_DONE,
								Text: "Ok",
							},
						},
					},
				},
			}
			return res, nil
		}
		if resp.GetStatus().IsInvalidArgument() {
			res.GetRespHeader().Status = rpcPb.StatusInternal()
			res.GetRespHeader().ErrorView = &errorsPb.ErrorView{
				Type: errorsPb.ErrorViewType_BOTTOM_SHEET,
				Options: &errorsPb.ErrorView_BottomSheetErrorView{
					BottomSheetErrorView: &errorsPb.BottomSheetErrorView{
						// TODO(Brijesh): Finalise copies with product and design team
						Title:        "Error setting up AutoPay",
						TitleText:    &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Error setting up AutoPay"}},
						Subtitle:     resp.GetUserFacingErrMsg(),
						SubtitleText: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: resp.GetUserFacingErrMsg()}},
						Ctas:         []*errorsPb.CTA{{Type: errorsPb.CTA_DONE, Text: "Ok"}},
					},
				},
			}
			return res, nil
		}
		logger.Error(ctx, "error while getting response from BE", zap.Error(te))
		res.GetRespHeader().Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}
	s.populateLODropOffFeedbackIdentifierInRespHeaderIfEnabled(res.GetRespHeader())
	return &palFePb.GetMandateViewDataV2Response{
		RespHeader: &header.ResponseHeader{
			Status: rpcPb.StatusOk(),
		},
		Deeplink: resp.GetDeeplink(),
	}, nil
}

// nolint:dupl
func (s *Service) GetPWARedirectionDeeplink(ctx context.Context, req *palFePb.GetPWARedirectionDeeplinkRequest) (*palFePb.GetPWARedirectionDeeplinkResponse, error) {
	helper.LogIfLoanHeaderNotPresent(ctx, req.GetLoanHeader(), "GetPWARedirectionDeeplink")
	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}
	if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		req.GetLoanHeader().LoanProgram = palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram)
	}
	res := &palFePb.GetPWARedirectionDeeplinkResponse{
		RespHeader: &header.ResponseHeader{Status: rpcPb.StatusOk()},
	}

	getPWARedirectionDetailsReq := &preapprovedloanPbBe.GetPWARedirectionDetailsRequest{
		LoanHeader: helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
	}
	switch req.GetLoanIdentifier().(type) {
	case *palFePb.GetPWARedirectionDeeplinkRequest_LoanRequestId:
		getPWARedirectionDetailsReq.LoanIdentifier = &preapprovedloanPbBe.GetPWARedirectionDetailsRequest_LoanRequestId{LoanRequestId: req.GetLoanRequestId()}
	case *palFePb.GetPWARedirectionDeeplinkRequest_LoanAccountId:
		getPWARedirectionDetailsReq.LoanIdentifier = &preapprovedloanPbBe.GetPWARedirectionDetailsRequest_LoanAccountId{LoanAccountId: req.GetLoanAccountId()}
	default:
		logger.Error(ctx, "unhandled fe request identifier for fetching pwa redirection details", zap.Any(logger.REQUEST, req.GetLoanIdentifier()), zap.Any(logger.LOAN_HEADER, req.GetLoanHeader()))
		res.GetRespHeader().Status = rpcPb.StatusInternalWithDebugMsg("unhandled fe request identifier received for pwa redirection details")
		return res, nil
	}

	resp, err := s.preApprovedLoanClient.GetPWARedirectionDetails(ctx, getPWARedirectionDetailsReq)
	if err = epifigrpc.RPCError(resp, err); err != nil {
		if resp.GetStatus().IsFailedPrecondition() {
			return &palFePb.GetPWARedirectionDeeplinkResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpcPb.StatusFailedPreconditionWithDebugMsg(err.Error()),
					// TODO(Brijesh): Add a error view that is different from default error view but works across all lenders.
					ErrorView: defaultErrorView,
				},
			}, nil
		}
		logger.Error(ctx, "error while getting pwa url", zap.Error(err), zap.String(logger.LOAN_REQUEST_ID, req.GetLoanRequestId()))
		return &palFePb.GetPWARedirectionDeeplinkResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpcPb.StatusInternalWithDebugMsg("preApprovedLoanClient.GetPWARedirectionDetails rpc call failed"),
				ErrorView: defaultErrorView,
			},
		}, nil
	}
	deeplinkProvider := s.getDeeplinkProvider(ctx, req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())
	dl := deeplinkProvider.GetPWARedirectionDeeplink(ctx, resp.GetPwaUrl(), req.GetLoanHeader())
	res.Deeplink = dl
	res.GetRespHeader().FeedbackEngineInfo = &header.FeedbackEngineInfo{
		FlowIdDetails: &header.FlowIdentifierDetails{
			FlowIdentifierType: types.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_DROP_OFF,
			FlowIdentifier:     types.FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_PWA_REDIRECTION_SCREEN.String(),
		},
	}
	return res, nil
}

// setLoanHeaderIfEmpty method updates loan header if no details are sent by the caller.
func (s *Service) setLoanHeaderIfEmpty(ctx context.Context, req *palFePb.GetLandingInfoRequest) {
	// return if loan type or loan program is present in request
	if req.GetLoanHeader().GetLoanProgram() != palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED ||
		req.GetLoanType() != palFeEnumsPb.LoanType_LOAN_TYPE_UNSPECIFIED {
		return
	}
	if s.redirectUserToLamf(ctx, req) {
		setLamfLoanHeader(req)
		return
	}
	req.LoanType = palFeEnumsPb.LoanType_LOAN_TYPE_PERSONAL
	return
}

func setLamfLoanHeader(req *palFePb.GetLandingInfoRequest) {
	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{}
	}
	req.GetLoanHeader().LoanType = palFeEnumsPb.LoanType_LOAN_TYPE_SECURED_LOAN
	req.GetLoanHeader().Vendor = palFeEnumsPb.Vendor_FIFTYFIN
	req.GetLoanHeader().LoanProgram = palFeEnumsPb.LoanProgram_LOAN_PROGRAM_LAMF
}

// redirectUserToLamf method evaluates if user should be redirected to lamf landing page or not.
// currently, a segment of users who click loans section in home nav-bar are re-directed to LAMF.
func (s *Service) redirectUserToLamf(ctx context.Context, req *palFePb.GetLandingInfoRequest) bool {
	entryPoint := req.GetLoanHeader().GetEventData().GetEntryPoint()
	if entryPoint != palFeEnumsPb.EntryPoint_ENTRY_POINT_HOME_NAV_BAR {
		return false
	}
	actorId := req.GetReq().GetAuth().GetActorId()
	if s.genconf.Lending().SecuredLoanParams().HomeNavBarSegmentExpression() != "" {
		segRes, err := s.segmentClient.IsMemberOfExpressions(ctx, &segmentPb.IsMemberOfExpressionsRequest{
			ActorId:              actorId,
			SegmentIdExpressions: []string{s.genconf.Lending().SecuredLoanParams().HomeNavBarSegmentExpression()},
		})
		if rpcErr := epifigrpc.RPCError(segRes, err); rpcErr != nil {
			logger.Error(ctx, "error while evaluating lamf home nav bar segment expression", zap.Error(rpcErr))
			return false
		}
		if segRes == nil ||
			segRes.GetSegmentExpressionMembershipMap()[s.genconf.Lending().SecuredLoanParams().HomeNavBarSegmentExpression()].GetSegmentExpressionStatus() != segmentPb.SegmentExpressionStatus_OK ||
			!segRes.GetSegmentExpressionMembershipMap()[s.genconf.Lending().SecuredLoanParams().HomeNavBarSegmentExpression()].GetIsActorMember() {
			return false
		}
	} else {
		return false
	}
	isEligible, err := s.releaseEvaluator.Evaluate(ctx,
		release.NewCommonConstraintData(types.Feature_FEATURE_SECURED_LOANS).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "error while evaluating secured loans release constraint for home nav bar redirection", zap.Error(err))
		return false
	}
	return isEligible
}

func (s *Service) getPrePayDetailsV2(ctx context.Context, req *palFePb.GetPrePayDetailsRequest) (*palFePb.GetPrePayDetailsResponse, error) {
	startTime := time.Now()
	res := &palFePb.GetPrePayDetailsResponse{
		RespHeader: &header.ResponseHeader{},
	}
	beRes, err := s.preApprovedLoanClient.GetPrePayDetails(ctx,
		&preapprovedloanPbBe.GetPrePayDetailsRequest{
			LoanHeader: helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
			LoanId:     req.GetLoanId(),
		})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		logger.Error(ctx, "preApprovedLoanClient.GetPrePayDetails for PrePay Details BE API failed", zap.Error(te))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}
	outStandingAmount, err := moneyPb.ToPaise(beRes.GetLoanAccount().GetLoanAmountInfo().GetOutstandingAmount())
	if err != nil {
		logger.Error(ctx, "error getting outStandingAmount", zap.Error(err))
	}

	// if upcoming emi from beRes is nil and amount in the request is also nil, we can put zero amount so that next steps won't fail
	if req.GetAmount() == nil && beRes.GetUpcomingEmi() == nil {
		req.Amount = types.GetFromBeMoney(moneyPb.ZeroINR().GetPb())
	}
	if req.GetAmount() == nil && req.GetLoanHeader().GetLoanProgram() != palFeEnumsPb.LoanProgram_LOAN_PROGRAM_LAMF {
		emiAmount, err := moneyPb.ToPaise(beRes.GetUpcomingEmi())
		if err != nil {
			logger.Error(ctx, "error getting emi Amount", zap.Error(err))
		}
		if outStandingAmount <= emiAmount {
			req.Amount = &types.Money{
				CurrencyCode: "INR",
				Units:        (outStandingAmount + (baseprovider.GetMinPrePayAmountInUnits() * 100)) / 200,
			}
		} else {
			req.Amount = types.GetFromBeMoney(helper.CeilMoney(moneyPb.FromPaisa(emiAmount)))
		}
	}
	getLoanDetails, getLoanDetailsErr := s.preApprovedLoanClient.GetLoanDetails(ctx, &preapprovedloanPbBe.GetLoanDetailsRequest{
		LoanHeader: helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
		LoanId:     req.GetLoanId(),
		ActorId:    req.GetReq().GetAuth().GetActorId(),
	})
	if te := epifigrpc.RPCError(getLoanDetails, getLoanDetailsErr); te != nil {
		logger.Error(ctx, "preApprovedLoanClient.GetLoanDetails BE API failed", zap.Error(te))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}
	deeplinkProvider := s.getDeeplinkProvider(ctx, helper.GetPalFeVendorFromBe(beRes.GetLoanAccount().GetVendor()), req.GetLoanHeader().GetLoanProgram())
	deeplink, dlErr := deeplinkProvider.GetPrePayDetailsScreenV2(ctx, req.GetLoanHeader(), beRes, req.GetAmount())
	if s.isLAMFNewPrepayEnabled(ctx, req.GetLoanHeader()) {
		if req.GetIsLoanAccountSelectionScreen() {
			deeplink, dlErr = deeplinkProvider.GetPrePayAccountSelectionScreen(req.GetLoanHeader(), &provider.GetGetPrePayAccountSelectionScreenRequest{
				LoanId:             req.GetLoanId(),
				BeRes:              beRes,
				GetLoanDetailsResp: getLoanDetails,
			})
		} else {
			deeplink, dlErr = deeplinkProvider.GetPrePayDetailsScreenV3(ctx, &provider.GetPrepayDetailsScreenV3Request{
				Lh:                     req.GetLoanHeader(),
				BeRes:                  beRes,
				LoanPaymentAccountType: palFeEnumsPb.LoanPaymentAccountType(palFeEnumsPb.LoanPaymentAccountType_value[req.GetLoanAccountTypeIdentifier()]),
				PrePayAmount:           req.GetAmount(),
				GetLoanDetailsResp:     getLoanDetails,
			})
		}
	}
	if dlErr != nil {
		logger.Error(ctx, "error getting deeplink for prepay v2 details", zap.Error(dlErr))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}
	s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGetPrePayDetails, time.Since(startTime),
		palEvents.StatusSuccess, palEvents.SubStatusPrePay, "", "", palEvents.LoanRepaymentFlowV2,
		getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())

	res.Deeplink = deeplink
	res.RespHeader.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) GetLoanRepaymentMethods(ctx context.Context, req *palFePb.GetLoanRepaymentMethodsRequest) (*palFePb.GetLoanRepaymentMethodsResponse, error) {
	startTime := time.Now()
	helper.LogIfLoanHeaderNotPresent(ctx, req.GetLoanHeader(), "GetLoanRepaymentMethods")
	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}
	if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		req.GetLoanHeader().LoanProgram = palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram)
	}
	res := &palFePb.GetLoanRepaymentMethodsResponse{
		RespHeader: &header.ResponseHeader{},
	}

	beRes, err := s.preApprovedLoanClient.GetPrePayDetails(ctx,
		&preapprovedloanPbBe.GetPrePayDetailsRequest{
			LoanHeader: helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
			LoanId:     req.GetLoanAccountId(),
		})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		logger.Error(ctx, "preApprovedLoanClient.GetPrePayDetails for PrePay Details BE API failed", zap.Error(te))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}

	// not allowing prepayment if the pre-closure amount is zero/unknown as max allowed prepay amount calculation is dependent on the foreclosure amount,
	// negative foreclosure amount denotes that loan was overpaid, so not allowing prepayments in that case as well.
	if !moneyPb.IsPositive(beRes.GetForeclosureDetails().GetTotalOutstandingAmount()) {
		logger.Error(ctx, "not allowing prepayment as the pre-closure amount is zero/unknown", zap.String(logger.LOAN_ACCOUNT_ID, req.GetLoanAccountId()), zap.Any("foreclosureAmt", beRes.GetForeclosureDetails().GetTotalOutstandingAmount()))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = prePayNotAllowedBottomSheetErrorView
	}

	deeplinkProvider := s.getDeeplinkProvider(ctx, helper.GetPalFeVendorFromBe(beRes.GetLoanAccount().GetVendor()), req.GetLoanHeader().GetLoanProgram())
	prePayDeeplink, dlErr := deeplinkProvider.GetLoanRepaymentMethodsScreen(ctx, req.GetLoanHeader(), beRes, req.GetAmount(),
		req.GetRepaymentDetailsType(), helper.ShouldFetchUserAccountsForTpapPrePaymentFlow(ctx, req.GetLoanHeader(), s.genconf.Lending().PreApprovedLoan().PrePayConfig()), s.isPrePayViaPgEnabled(ctx, req.GetReq().GetAuth().GetActorId(), deeplinkProvider.GetLoanHeader()))
	if s.isLAMFNewPrepayEnabled(ctx, req.GetLoanHeader()) {
		prePayDeeplink, dlErr = deeplinkProvider.GetLoanRepaymentMethodsScreenV2(ctx, &provider.GetLoanRepaymentMethodsScreenV2Request{
			LoanPaymentAccountType:  palFeEnumsPb.LoanPaymentAccountType(palFeEnumsPb.LoanPaymentAccountType_value[req.GetLoanAccountTypeIdentifier()]),
			Lh:                      req.GetLoanHeader(),
			BeRes:                   beRes,
			Amount:                  req.GetAmount(),
			RepayDetailsType:        req.GetRepaymentDetailsType(),
			ShouldFetchUserAccounts: helper.ShouldFetchUserAccountsForTpapPrePaymentFlow(ctx, req.GetLoanHeader(), s.genconf.Lending().PreApprovedLoan().PrePayConfig()),
			IsPrePayViaPGEnabled:    s.isPrePayViaPgEnabled(ctx, req.GetReq().GetAuth().GetActorId(), deeplinkProvider.GetLoanHeader()),
		})
	}
	if dlErr != nil {
		logger.Error(ctx, "error getting deeplink for prepay repayment details", zap.Error(dlErr))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}
	s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGetLoanRepaymentMethods, time.Since(startTime),
		palEvents.StatusSuccess, palEvents.SubStatusRepaymentMethods, "", "", palEvents.LoanRepaymentFlowV2,
		getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())

	res.Deeplink = prePayDeeplink
	res.RespHeader.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) GetIfscCodeSuggestions(ctx context.Context, req *palFePb.GetIfscCodeSuggestionsRequest) (*palFePb.GetIfscCodeSuggestionsResponse, error) {
	// we are setting context to IFSC fieldType here as we are allowing search based on IFSC code only.
	queryContext := searchPb.Context{
		Field: enums.BankField_IFSC,
	}

	autoCompReq := searchPb.AutocompleteRequest{
		UserId:  req.GetReq().GetAuth().GetActorId(),
		Query:   req.GetQuery(),
		Context: &queryContext,
	}

	searchResp, err := s.searchClient.PayAutocomplete(ctx, &autoCompReq)
	if searchResp != nil && err == nil && searchResp.GetStatus().IsSuccess() {
		logger.Debug(ctx, "suggestions request successful", zap.String("query", req.GetQuery()), zap.Int("#suggestions", len(searchResp.GetResult().GetSuggestions())))
		return &palFePb.GetIfscCodeSuggestionsResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpcPb.StatusOk(),
			},
			Suggestions: helper.FilterIfscSuggestions(req.GetSearchIfscType(), searchResp),
		}, nil
	}

	if len(searchResp.GetResult().GetSuggestions()) == 0 && err == nil {
		return &palFePb.GetIfscCodeSuggestionsResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpcPb.StatusOk(),
			},
		}, nil
	}

	logger.Debug(ctx, "GetIfscCodeSuggestions rpc failed, payAutoComplete returned with error", zap.String("query", req.GetQuery()), zap.Error(err))
	return &palFePb.GetIfscCodeSuggestionsResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpcPb.StatusInternal(),
		},
	}, nil
}

func (s *Service) isPostDisbursalFlowEnabled(ctx context.Context, actorId string, lh *palFeEnumsPb.LoanHeader) bool {
	isAllowed, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_FEATURE_POST_LOAN_DISBURSAL_SCREENS).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "failed in post loan disbursal release evaluator", zap.Error(err))
		return false
	}
	appVersionConstraintData := release.NewAppVersionConstraintData(s.genconf.Lending().PreApprovedLoan().PostDisbursalV2FlowAppVersionConstraintConfig().AppVersionConstraintConfig())
	isAppVersionGreater, appVerErr := release.NewAppVersionConstraint().Evaluate(ctx, appVersionConstraintData, nil)
	if appVerErr != nil {
		logger.Error(ctx, "failed in post loan disbursal app version  evaluator", zap.Error(err))
		return false
	}
	return isAllowed && isAppVersionGreater && (s.genconf.Lending().PreApprovedLoan().PostDisbursalV2FlowAppVersionConstraintConfig().SkipVendorLoanProgramCheck() || s.genconf.Lending().PreApprovedLoan().PostDisbursalV2FlowAppVersionConstraintConfig().VendorLoanProgramMap().Get(strings.Join([]string{
		lh.GetVendor().String(),
		lh.GetLoanProgram().String(),
	}, ":")))
}

// nolint:gocritic
func (s *Service) isApplicationFlowEnabled(ctx context.Context, actorId string, lh *palFeEnumsPb.LoanHeader, programVersion palEnumsPb.LoanProgramVersion) bool {
	var feature types.Feature
	switch lh.GetVendor() {
	case palFeEnumsPb.Vendor_LIQUILOANS:
		switch lh.GetLoanProgram() {
		case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY:
			if programVersion == palEnumsPb.LoanProgramVersion_LOAN_PROGRAM_VERSION_V2 {
				feature = types.Feature_LOANS_LIQUILOANS_EARLY_SALARY_V2
			}
		}
	}
	if feature == types.Feature_FEATURE_UNSPECIFIED {
		return true
	}
	isAllowed, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(feature).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "error in evaluating feature", zap.Error(err), zap.String(logger.FEATURE, feature.String()))
		return false
	}
	return isAllowed
}

func (s *Service) SaveContactDetails(ctx context.Context, req *palFePb.SaveContactDetailsRequest) (*palFePb.SaveContactDetailsResponse, error) {
	res := &palFePb.SaveContactDetailsResponse{
		RespHeader: &header.ResponseHeader{Status: rpcPb.StatusOk()},
	}
	startTime := time.Now()
	s.PushApiTriggeredEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameSaveContactDetails, "", "", palEvents.LoanApplicationFlow,
		getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())

	beRes, err := s.preApprovedLoanClient.SaveContactDetails(ctx, &preapprovedloanPbBe.SaveContactDetailsRequest{
		LseId:       req.GetLseId(),
		PhoneNumber: req.GetPhoneNumber(),
		LoanHeader:  helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
	})

	if te := epifigrpc.RPCError(beRes, err); te != nil {
		logger.Error(ctx, "preApprovedLoanClient.SaveContactDetails BE API failed", zap.Error(te))
		if beRes.GetStatus().IsPermissionDenied() {
			deeplinkProvider := s.getDeeplinkProvider(ctx, req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())
			res.Deeplink = deeplinkProvider.GetLoanLandingInfo(ctx, deeplinkProvider.GetLoanHeader(), &provider.GetLoanLandingInfoRequest{ActorId: req.GetReq().GetAuth().GetActorId()})
			res.RespHeader.Status = rpcPb.StatusOk()
			return res, nil
		}
		if beRes.GetStatus().IsFailedPrecondition() {
			res.RespHeader.Status = rpcPb.StatusFailedPrecondition()
			res.RespHeader.ErrorView = invalidAlternateNumberBottomSheetErrorView
			return res, nil
		}
		res.RespHeader.Status = rpcPb.StatusInternal()
		return res, nil
	}

	ownership := getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())
	s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameSaveContactDetails, time.Since(startTime), palEvents.StatusSuccess, palEvents.SubStatusUpdateForLoanEligibility, "", "", palEvents.LoanApplicationFlow, ownership, req.GetLoanHeader().GetEventData())
	res.Deeplink = beRes.GetDeeplink()
	return res, nil
}

func (s *Service) GetVkycDeeplink(ctx context.Context, req *palFePb.GetVkycDeeplinkRequest) (*palFePb.GetVkycDeeplinkResponse, error) {
	helper.LogIfLoanHeaderNotPresent(ctx, req.GetLoanHeader(), "GetVkycDeeplink")
	if req.GetLoanHeader() == nil {
		req.LoanHeader = &palFeEnumsPb.LoanHeader{
			LoanProgram: palFeEnumsPb.LoanProgram(s.conf.Lending.LoanProgram),
		}
	}
	vkycResp, vkycErr := s.preApprovedLoanClient.GetVkycDeeplink(ctx, &preapprovedloanPbBe.GetVkycDeeplinkRequest{
		LoanHeader:    helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
		LoanRequestId: req.GetLoanRequestId(),
	})
	if te := epifigrpc.RPCError(vkycResp, vkycErr); te != nil {
		logger.Error(ctx, "error in getting response from vkyc deeplink", zap.Error(te))
		return &palFePb.GetVkycDeeplinkResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpcPb.StatusInternalWithDebugMsg("preApprovedLoanClient.GetVkycDeeplink rpc call failed"),
				ErrorView: bottomSheetErrorView,
			},
			NextAction: nil,
		}, nil
	}
	return &palFePb.GetVkycDeeplinkResponse{
		RespHeader: &header.ResponseHeader{Status: rpcPb.StatusOk()},
		NextAction: vkycResp.GetDeeplink(),
	}, nil
}

func (s *Service) ModifyLoanTerms(ctx context.Context, req *palFePb.ModifyLoanTermsRequest) (*palFePb.ModifyLoanTermsResponse, error) {
	res := &palFePb.ModifyLoanTermsResponse{
		RespHeader: &header.ResponseHeader{},
	}
	startTime := time.Now()

	if req.GetLoanHeader() == nil || req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		logger.Error(ctx, "loan header is nil", zap.Any("loan-header", req.GetLoanHeader()))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}

	var consents []consentPb.ConsentType
	for _, consent := range req.GetConsentIds() {
		if consent == "" {
			continue
		}
		consentVal, ok := consentPb.ConsentType_value[consent]
		if !ok {
			logger.Error(ctx, "consent type is invalid", zap.Any("consentType", consent), zap.Any("allConsentTypes", req.GetConsentIds()))
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
		consentType := consentPb.ConsentType(consentVal)
		consents = append(consents, consentType)
	}

	if len(consents) != 0 {
		recordConsentsErr := s.RecordConsents(ctx, req.GetReq().GetAuth().GetActorId(), req.GetReq().GetAuth().GetDevice(), consents, "")
		if recordConsentsErr != nil {
			logger.Error(ctx, "error while calling RecordConsents", zap.Error(recordConsentsErr))
			res.RespHeader.Status = rpcPb.StatusInternal()
			res.RespHeader.ErrorView = defaultErrorView
			return res, nil
		}
	}

	beRes, err := s.preApprovedLoanClient.ModifyLoanTerms(ctx, &preapprovedloanPbBe.ModifyLoanTermsRequest{
		LoanHeader:     helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
		ActorId:        req.GetReq().GetAuth().GetActorId(),
		LoanAmount:     req.GetLoanAmount().GetBeMoney(),
		TenureInMonths: req.GetTenureInMonths(),
		RevisedOfferId: req.GetOfferId(),
		LoanRequestId:  req.GetLoanRequestId(),
	})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		logger.Error(ctx, "preApprovedLoanClient.ModifyLoanTerms BE API failed", zap.String(logger.OFFER_ID, req.GetOfferId()), zap.Error(te))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = bottomSheetErrorView
		return res, nil
	}

	deeplinkProvider := s.getDeeplinkProvider(ctx, req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())
	dl, dlErr := getStatusPollScreen(ctx, deeplinkProvider, req.GetLoanRequestId())
	if dlErr != nil {
		logger.Error(ctx, "error while generating application status poll deeplink", zap.Error(dlErr))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}

	ownership := getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())
	s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameModifyLoanTerms, time.Since(startTime), palEvents.StatusSuccess, "", req.GetOfferId(), "", palEvents.LoanApplicationFlow, ownership, req.GetLoanHeader().GetEventData())
	res.Deeplink = dl
	s.populateLODropOffFeedbackIdentifierInRespHeaderIfEnabled(res.GetRespHeader())
	res.RespHeader.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) isPrePayViaPgEnabled(ctx context.Context, actorId string, lh *palFeEnumsPb.LoanHeader) bool {
	isPgPrepayFeatureEnabled, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_FEATURE_LOAN_PRE_PAY_VIA_PG).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "failed in pre-pay via pg release evaluator", zap.Error(err))
		return false
	}
	return isPgPrepayFeatureEnabled && s.genconf.Lending().PreApprovedLoan().PrePayConfig().PrePayViaPGConfig().LenderLoanProgramToIsPgEnabledMap().Get(strings.Join([]string{
		lh.GetVendor().String(),
		lh.GetLoanProgram().String(),
	}, ":"))
}

func (s *Service) getConsentsForApplyForLoan(req *palFePb.ApplyForLoanRequest) ([]consentPb.ConsentType, error) {
	var consents []consentPb.ConsentType
	consents = append(consents, consentPb.ConsentType_FI_PRE_APPROVED_LOAN)

	switch req.GetLoanHeader().GetVendor() {
	case palFeEnumsPb.Vendor_IDFC:
		// if user is eligible for alternate account flow and vendor is IDFC then also record IDFC mandate consent in this flow
		// Ref: https://epifi.slack.com/archives/C03LMRC4RH9/p1701845865063069
		consents = append(consents, consentPb.ConsentType_PL_IDFC_MANDATE)
	case palFeEnumsPb.Vendor_STOCK_GUARDIAN_LSP:
		consents = append(consents, consentPb.ConsentType_CONSENT_LOANS_SG_CKYC_RECORD_FETCH, consentPb.ConsentType_CONSENT_LOANS_SG_CREDIT_BUREAU_INFO, consentPb.ConsentType_CONSENT_LOANS_SG_DATA_USE_BY_ASSOCIATED_PARTNERS)
	case palFeEnumsPb.Vendor_FEDERAL_BANK:
		if req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB && req.GetTenureInMonths() == 0 {
			// missing consents when calling apply for loan against pre-qual offer (tenure will be not selected hence 0)
			return nil, errors.New("missing consent for federal ntb")
		}
	}

	return consents, nil
}

func (s *Service) CollectData(ctx context.Context, req *palFePb.CollectDataRequest) (*palFePb.CollectDataResponse, error) {
	var beErr error
	var beResp *preapprovedloanPbBe.CollectDataResponse
	lrId := req.GetLoanRequestId()
	actorId := req.GetReq().GetAuth().GetActorId()
	switch req.GetData().(type) {
	case *palFePb.CollectDataRequest_LoanPreferences_:
		beResp, beErr = s.preApprovedLoanClient.CollectData(ctx, &preapprovedloanPbBe.CollectDataRequest{
			LoanHeader:    helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
			Flow:          helper.FeToBeLseFlow[req.GetFlow()],
			LoanRequestId: lrId,
			ActorId:       actorId,
			Data: &preapprovedloanPbBe.CollectDataRequest_LoanPreferences_{
				LoanPreferences: &preapprovedloanPbBe.CollectDataRequest_LoanPreferences{
					Interest:   req.GetLoanPreferences().GetInterest(),
					LoanAmount: req.GetLoanPreferences().GetLoanAmount(),
				},
			},
		})
	case *palFePb.CollectDataRequest_ModifiedInterestRate_:
		if req.GetLoanHeader().GetVendor() == palFeEnumsPb.Vendor_LENDEN {
			consentRes, err := s.consentClient.RecordConsents(ctx, &consentPb.RecordConsentsRequest{
				Consents: []*consentPb.ConsentRequestInfo{
					{
						ConsentType: consentPb.ConsentType_CONSENT_TYPE_LDC_MODIFY_ROI,
						// sending uuid as clientReqId as the rpc is not handling any already exists states
						ClientReqId: uuid.NewString(),
					},
				},
				ActorId: actorId,
				// todo(Anupam): make lenden ownership by adding the DB access in the consent service
				Owner: commontypes.Owner_OWNER_EPIFI_TECH,
			})
			if err = epifigrpc.RPCError(consentRes, err); err != nil {
				logger.Error(ctx, "error while recording consents", zap.Error(err))
				return &palFePb.CollectDataResponse{
					RespHeader: &header.ResponseHeader{
						Status:    rpcPb.StatusInternal(),
						ErrorView: defaultErrorView,
					},
				}, nil
			}
		}
		beResp, beErr = s.preApprovedLoanClient.CollectData(ctx, &preapprovedloanPbBe.CollectDataRequest{
			LoanHeader:    helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
			Flow:          helper.FeToBeLseFlow[req.GetFlow()],
			ActorId:       actorId,
			LoanRequestId: lrId,
			Data: &preapprovedloanPbBe.CollectDataRequest_ModifiedInterestRate_{
				ModifiedInterestRate: &preapprovedloanPbBe.CollectDataRequest_ModifiedInterestRate{
					InterestRate: req.GetModifiedInterestRate().GetInterestRate(),
				},
			},
		})
	case *palFePb.CollectDataRequest_Consent_:
		var requestId string
		var consentIds []string
		var err error
		if req.GetLoanHeader().GetDataOwner() == palFeEnumsPb.Vendor_LENDEN {
			consentIds, requestId, err = s.recordConsent.RecordConsents(ctx, actorId, req.GetReq().GetAuth().GetDevice(), req.GetConsent().GetConsentIds())
			if err != nil {
				logger.Error(ctx, "error while recording consents", zap.Error(err))
				return &palFePb.CollectDataResponse{
					RespHeader: &header.ResponseHeader{
						Status:    rpcPb.StatusInternalWithDebugMsg(err.Error()),
						ErrorView: defaultErrorView,
					},
				}, nil
			}
		} else {
			requestId = uuid.NewString()
			var consents []*consentPb.ConsentRequestInfo
			for _, consentId := range req.GetConsent().GetConsentIds() {
				consents = append(consents, &consentPb.ConsentRequestInfo{
					ConsentType: consentPb.ConsentType(consentPb.ConsentType_value[consentId]),
					ClientReqId: requestId + "_" + consentId,
					Provenance:  consentPb.ConsentProvenance_CONSENT_PROVENANCE_IN_APP,
				})
			}
			consentRes, err := s.consentClient.RecordConsents(ctx, &consentPb.RecordConsentsRequest{
				Consents: consents,
				ActorId:  actorId,
				Owner:    commontypes.Owner_OWNER_EPIFI_TECH, // check
			})
			if err = epifigrpc.RPCError(consentRes, err); err != nil {
				logger.Error(ctx, "error while recording consents", zap.Error(err))
				return &palFePb.CollectDataResponse{
					RespHeader: &header.ResponseHeader{
						Status:    rpcPb.StatusInternal(),
						ErrorView: defaultErrorView,
					},
				}, nil
			}
			for _, consent := range consentRes.GetConsentResponses() {
				consentIds = append(consentIds, consent.GetConsentId())
			}
		}
		beResp, beErr = s.preApprovedLoanClient.CollectData(ctx, &preapprovedloanPbBe.CollectDataRequest{
			LoanHeader:    helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
			Flow:          helper.FeToBeLseFlow[req.GetFlow()],
			LoanRequestId: lrId,
			ActorId:       actorId,
			Data: &preapprovedloanPbBe.CollectDataRequest_Consent_{
				Consent: &preapprovedloanPbBe.CollectDataRequest_Consent{
					RequestId:  requestId,
					ConsentIds: consentIds,
				},
			},
		})
	}
	if te := epifigrpc.RPCError(beResp, beErr); te != nil {
		logger.Error(ctx, "error calling CollectData backend", zap.Error(te), zap.String(logger.LOAN_REQUEST_ID, lrId))
		return &palFePb.CollectDataResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpcPb.StatusInternalWithDebugMsg(te.Error()),
				ErrorView: defaultErrorView,
			},
		}, nil
	}
	return &palFePb.CollectDataResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpcPb.StatusOk(),
		},
		NextAction: beResp.GetNextAction(),
	}, nil
}

func getDisplayLoanAccountNumber(loanAccount *preapprovedloanPbBe.LoanAccount) string {
	switch loanAccount.GetVendor() {
	case preapprovedloanPbBe.Vendor_STOCK_GUARDIAN_LSP:
		return loanAccount.GetDetails().GetLenderAccountDetails().GetAccountNumber()
	default:
		return loanAccount.GetAccountNumber()
	}
}

func (s *Service) IsOfferDetailsV4FlowEnabled(ctx context.Context, lh *palFeEnumsPb.LoanHeader) (bool, error) {
	conf := s.genconf.Lending().PreApprovedLoan().OfferDetailsV4Config()
	if !conf.IsEnabled(ctx) {
		return false, nil
	}
	appVersionConstraintData := release.NewAppVersionConstraintData(s.genconf.Lending().PreApprovedLoan().OfferDetailsV4Config().AppVersionConstraintConfig())
	isAppVersionGreater, appVerErr := release.NewAppVersionConstraint().Evaluate(ctx, appVersionConstraintData, nil)
	if appVerErr != nil {
		return false, errors.Wrap(appVerErr, "failed to evaluate app version constraint for overall multi offer screen")
	}
	if !isAppVersionGreater {
		return false, nil
	}

	if lhConf := conf.VendorLoanProgramMap().Get(strings.Join([]string{
		lh.GetVendor().String(),
		lh.GetLoanProgram().String(),
	}, ":")); lhConf != nil && lhConf.IsEnabled(ctx) {
		if lhConf.AppVersionConstraintConfig() != nil {
			appVersionConstraintData = release.NewAppVersionConstraintData(lhConf.AppVersionConstraintConfig())
			isAppVersionGreater, appVerErr = release.NewAppVersionConstraint().Evaluate(ctx, appVersionConstraintData, nil)
			if appVerErr != nil {
				return false, errors.Wrap(appVerErr, fmt.Sprintln("failed to evaluate app version constraint for multi offer screen for vendor: ", lh.GetVendor().String(), ", program: ", lh.GetLoanProgram().String()))
			}
			if !isAppVersionGreater {
				return false, nil
			}
		}

		return true, nil
	}
	return false, nil
}

func validateUserName(ctx context.Context, sanitizedName string) (*errorsPb.ErrorView, error) {
	// error if name contains spaces before and after name
	if sanitizedName != strings.TrimSpace(sanitizedName) {
		return nil, errors.New("name contains spaces before and after name")
	}

	// Adding this check to avoid cibil pull if either forename or surname is missing
	if len(sanitizedName) == 0 || !strings.Contains(sanitizedName, " ") {
		return &errorsPb.ErrorView{
			Type: errorsPb.ErrorViewType_BOTTOM_SHEET,
			Options: &errorsPb.ErrorView_BottomSheetErrorView{
				BottomSheetErrorView: &errorsPb.BottomSheetErrorView{
					TitleText:    baseprovider.GetText("Provided name seems incomplete", "#FFFFFF", commontypes.FontStyle_HEADLINE_2),
					SubtitleText: baseprovider.GetText("Please provide your first and last name to help increase chances of getting a loan", "#B9B9B9", commontypes.FontStyle_BODY_3),
				},
			},
		}, nil
	}

	// Check if name contains only alphabets and spaces
	matched, err := regexp.MatchString(`^[a-zA-Z\s]+$`, sanitizedName)
	if err != nil {
		return nil, errors.Wrapf(err, "error matching name with regex: %s", sanitizedName)
	}
	if !matched {
		logger.Error(ctx, "name validation failed", zap.Error(err))
		return &errorsPb.ErrorView{
			Type: errorsPb.ErrorViewType_TOAST,
			Options: &errorsPb.ErrorView_ToastErrorView{
				ToastErrorView: &errorsPb.ToastErrorView{
					Title: "Please enter your name with only alphabets and spaces",
				},
			},
		}, nil
	}

	if len(sanitizedName) > 50 {
		logger.Error(ctx, "name too long", zap.Error(err), zap.Int(logger.LENGTH, len(sanitizedName)))
		logger.SecureInfo(ctx, vgPb.Vendor_VENDOR_UNSPECIFIED, "name too long", zap.String("name", sanitizedName))
		return &errorsPb.ErrorView{
			Type: errorsPb.ErrorViewType_TOAST,
			Options: &errorsPb.ErrorView_ToastErrorView{
				ToastErrorView: &errorsPb.ToastErrorView{
					Title: fmt.Sprintf("Please enter a shorter name (max 50 characters, current: %d)", len(sanitizedName)),
				},
			},
		}, nil
	}

	// Add a secure info log if the name contains duplicate words
	words := strings.Fields(sanitizedName)
	wordCount := make(map[string]int)
	var duplicates []string
	for _, word := range words {
		wordLower := strings.ToLower(word)
		wordCount[wordLower]++
		if wordCount[wordLower] == 2 {
			duplicates = append(duplicates, word)
		}
	}
	if len(duplicates) > 0 {
		logger.WarnWithCtx(ctx, "duplicate words in name detected", zap.Int(logger.DISPLAY_NAME, len(duplicates)))
		logger.SecureWarn(ctx, vgPb.Vendor_VENDOR_UNSPECIFIED, "duplicate words in name detected", zap.String("name", sanitizedName), zap.Strings("duplicates", duplicates))
	}

	return nil, nil
}

func (s *Service) CollectFormData(ctx context.Context, req *palFePb.CollectFormDataRequest) (*palFePb.CollectFormDataResponse, error) {
	if req.GetLoanHeader() == nil || req.GetLoanHeader().GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		logger.Error(ctx, "loan header is nil", zap.Any("loan-header", req.GetLoanHeader()))
		return &palFePb.CollectFormDataResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpcPb.StatusInternal(),
				ErrorView: defaultErrorView,
			},
		}, nil

	}

	formDataList, err := getFormDataList(ctx, req.GetFormFieldValues())
	if err != nil {
		// TODO: remove this payload once issue is identified
		req.Req = nil
		payload, _ := protojson.Marshal(req)
		logger.Error(ctx, "error in getting form data list", zap.Error(err), zap.Any("payload", payload))
		return &palFePb.CollectFormDataResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpcPb.StatusInternal(),
				ErrorView: defaultErrorView,
			},
		}, nil
	}
	// add a validation for name
	for _, formField := range formDataList {
		if formField.GetFieldId() == palEnumsPb.FieldId_NAME {
			formField.FieldValue = &preapprovedloanPbBe.CollectFormDataRequest_FormFieldValue_StringValue{
				StringValue: strings.TrimSpace(formField.GetStringValue()),
			}
			errView, validationErr := validateUserName(ctx, formField.GetStringValue())
			if validationErr != nil {
				logger.Error(ctx, "error in validating user name", zap.Error(validationErr))
				return &palFePb.CollectFormDataResponse{
					RespHeader: &header.ResponseHeader{
						Status:    rpcPb.StatusInternal(),
						ErrorView: defaultErrorView,
					},
				}, nil
			}
			if errView != nil {
				logger.Error(ctx, "name validation failed in CollectFormData RPC")
				return &palFePb.CollectFormDataResponse{
					RespHeader: &header.ResponseHeader{
						Status:    rpcPb.StatusInvalidArgument(),
						ErrorView: errView,
					},
				}, nil
			}
		}
		if formField.GetFieldId() == palEnumsPb.FieldId_ANNUAL_REVENUE {
			// users might be entering monthly revenue instead of annual and get rejected in BRE
			// to avoid this, return error view saying revenue value is low and nudge user to check again
			if formField.GetMoneyValue().GetUnits()/12 < 18000 {
				logger.Error(ctx, "revenue validation failed in CollectFormData RPC")
				return &palFePb.CollectFormDataResponse{
					RespHeader: &header.ResponseHeader{
						Status: rpcPb.StatusInvalidArgument(),
						ErrorView: &errorsPb.ErrorView{
							Type: errorsPb.ErrorViewType_BOTTOM_SHEET,
							Options: &errorsPb.ErrorView_BottomSheetErrorView{
								BottomSheetErrorView: &errorsPb.BottomSheetErrorView{
									TitleText:    baseprovider.GetText("Yearly revenue amount seems incorrect", "#FFFFFF", commontypes.FontStyle_HEADLINE_2),
									SubtitleText: baseprovider.GetText("Please ensure it's the amount earned in a year", "#B9B9B9", commontypes.FontStyle_BODY_3),
								},
							},
						},
					},
				}, nil
			}
		}
		if formField.GetFieldId() == palEnumsPb.FieldId_PAN {
			formField.FieldValue = &preapprovedloanPbBe.CollectFormDataRequest_FormFieldValue_StringValue{
				StringValue: strings.ToUpper(formField.GetStringValue()),
			}
			if !onboarding.IsValidIndividualPAN(formField.GetStringValue()) {
				return &palFePb.CollectFormDataResponse{
					RespHeader: &header.ResponseHeader{
						Status: rpcPb.StatusInvalidArgument(),
						ErrorView: &errorsPb.ErrorView{
							Type: errorsPb.ErrorViewType_BOTTOM_SHEET,
							Options: &errorsPb.ErrorView_BottomSheetErrorView{
								BottomSheetErrorView: &errorsPb.BottomSheetErrorView{
									TitleText:    baseprovider.GetText("Invalid PAN number", "#FFFFFF", commontypes.FontStyle_HEADLINE_2),
									SubtitleText: baseprovider.GetText("Please enter correct PAN number", "#B9B9B9", commontypes.FontStyle_BODY_3),
								},
							},
						},
					},
				}, nil
			}
		}
	}

	requestPayload := &preapprovedloanPbBe.CollectFormDataRequest{
		LoanHeader:    helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
		LoanRequestId: req.GetLoanRequestId(),
		FormDataList:  formDataList,
	}
	response, err := s.preApprovedLoanClient.CollectFormData(ctx, requestPayload)
	if te := epifigrpc.RPCError(response, err); te != nil {
		if response.GetStatus().IsInvalidArgument() {
			return collectFormDataIncompleteDataResponse()
		}
		logger.Error(ctx, "error calling CollectFormData backend", zap.Error(te), zap.String(logger.LOAN_REQUEST_ID, req.GetLoanRequestId()))
		return &palFePb.CollectFormDataResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpcPb.StatusInternalWithDebugMsg(te.Error()),
				ErrorView: defaultErrorView,
			},
		}, nil
	}
	return &palFePb.CollectFormDataResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpcPb.StatusOk(),
		},
		NextAction: response.GetNextAction(),
	}, nil

}

func collectFormDataIncompleteDataResponse() (*palFePb.CollectFormDataResponse, error) {
	return &palFePb.CollectFormDataResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpcPb.StatusInvalidArgument(),
			ErrorView: &errorsPb.ErrorView{
				Type: errorsPb.ErrorViewType_BOTTOM_SHEET,
				Options: &errorsPb.ErrorView_BottomSheetErrorView{
					BottomSheetErrorView: &errorsPb.BottomSheetErrorView{
						TitleText:    baseprovider.GetText("Please provide all required fields", "#FFFFFF", commontypes.FontStyle_HEADLINE_2),
						SubtitleText: baseprovider.GetText("This will help increase your chances of getting a loan", "#B9B9B9", commontypes.FontStyle_BODY_3),
					},
				},
			},
		},
	}, nil
}

func getFormDataList(ctx context.Context, values []*palFePb.CollectFormDataRequest_FormFieldValue) ([]*preapprovedloanPbBe.CollectFormDataRequest_FormFieldValue, error) {
	var formDataList []*preapprovedloanPbBe.CollectFormDataRequest_FormFieldValue
	for i, value := range values {
		// ignoring this case due to ongoing android issue where app is sending empty form field list value
		// TODO: remove continue and return errors once issue is fixed at client and app version updated for all
		if value == nil || (value.GetFieldId() == "" && value.GetFieldValue() == nil) {
			logger.Error(ctx, fmt.Sprintf("[CollectFormData] form field is nil, index: %d", i))
			continue
		}
		if value.GetFieldId() == "" {
			logger.Error(ctx, fmt.Sprintf("[CollectFormData] form field id is nil, index: %d, value: %v", i, value.GetFieldValue()))
			return nil, fmt.Errorf("[CollectFormData] form field id is nil, index: %d, value: %v", i, value.GetFieldValue())
		}
		if value.GetFieldId() == "FIELD_ID_UNSPECIFIED" {
			logger.Error(ctx, fmt.Sprintf("[CollectFormData] form field id is unspecified, index: %d, value: %v", i, value.GetFieldValue()))
			return nil, fmt.Errorf("[CollectFormData] form field id is unspecified, index: %d, value: %v", i, value.GetFieldValue())
		}
		if value.GetFieldValue() == nil {
			logger.Error(ctx, fmt.Sprintf("[CollectFormData] form field value is nil, index: %d, id: %v", i, value.GetFieldId()))
			return nil, fmt.Errorf("[CollectFormData] form field value is nil, index: %d, id: %v", i, value.GetFieldId())
		}
		cc := getFieldValueForCollectFormDataRequest(value)
		formDataList = append(formDataList, cc)
	}
	return formDataList, nil
}

func getFieldValueForCollectFormDataRequest(value *palFePb.CollectFormDataRequest_FormFieldValue) *preapprovedloanPbBe.CollectFormDataRequest_FormFieldValue {
	switch value.GetFieldValue().(type) {
	case *palFePb.CollectFormDataRequest_FormFieldValue_StringValue:
		return &preapprovedloanPbBe.CollectFormDataRequest_FormFieldValue{
			FieldId: palEnumsPb.FieldId(palEnumsPb.FieldId_value[value.GetFieldId()]),
			FieldValue: &preapprovedloanPbBe.CollectFormDataRequest_FormFieldValue_StringValue{
				StringValue: value.GetStringValue(),
			},
		}
	case *palFePb.CollectFormDataRequest_FormFieldValue_IntValue:
		return &preapprovedloanPbBe.CollectFormDataRequest_FormFieldValue{
			FieldId: palEnumsPb.FieldId(palEnumsPb.FieldId_value[value.GetFieldId()]),
			FieldValue: &preapprovedloanPbBe.CollectFormDataRequest_FormFieldValue_IntValue{
				IntValue: value.GetIntValue(),
			},
		}
	case *palFePb.CollectFormDataRequest_FormFieldValue_DateValue:
		return &preapprovedloanPbBe.CollectFormDataRequest_FormFieldValue{
			FieldId: palEnumsPb.FieldId(palEnumsPb.FieldId_value[value.GetFieldId()]),
			FieldValue: &preapprovedloanPbBe.CollectFormDataRequest_FormFieldValue_DateValue{
				DateValue: &date.Date{
					Year:  value.GetDateValue().GetYear(),
					Month: value.GetDateValue().GetMonth(),
					Day:   value.GetDateValue().GetDay(),
				},
			},
		}
	case *palFePb.CollectFormDataRequest_FormFieldValue_MoneyValue:
		return &preapprovedloanPbBe.CollectFormDataRequest_FormFieldValue{
			FieldId: palEnumsPb.FieldId(palEnumsPb.FieldId_value[value.GetFieldId()]),
			FieldValue: &preapprovedloanPbBe.CollectFormDataRequest_FormFieldValue_MoneyValue{
				MoneyValue: &money.Money{
					CurrencyCode: value.GetMoneyValue().GetCurrencyCode(),
					Units:        value.GetMoneyValue().GetUnits(),
					Nanos:        value.GetMoneyValue().GetDecimals(),
				},
			},
		}
	case *palFePb.CollectFormDataRequest_FormFieldValue_SelectedOptionId:
		return &preapprovedloanPbBe.CollectFormDataRequest_FormFieldValue{
			FieldId: palEnumsPb.FieldId(palEnumsPb.FieldId_value[value.GetFieldId()]),
			FieldValue: &preapprovedloanPbBe.CollectFormDataRequest_FormFieldValue_SelectedOptionId{
				SelectedOptionId: value.GetSelectedOptionId(),
			},
		}
	default:
		return nil
	}
}
