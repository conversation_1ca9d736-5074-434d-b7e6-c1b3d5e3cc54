package helper

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/logger"

	fePb "github.com/epifi/gamma/api/frontend"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palFePb "github.com/epifi/gamma/api/frontend/preapprovedloan"
	palFeEnumsPb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	palBePb "github.com/epifi/gamma/api/preapprovedloan"
	types "github.com/epifi/gamma/api/typesv2"
	genConf "github.com/epifi/gamma/frontend/config/genconf"
)

var EmploymentMap = map[types.EmploymentType]*deeplinkPb.PreApprovedOccupationSelectionScreenOptions_EmploymentData{
	types.EmploymentType_EMPLOYMENT_TYPE_FREELANCER: {
		EmploymentType: types.EmploymentType_EMPLOYMENT_TYPE_FREELANCER,
		EmploymentText: "Freelancer",
	},
	types.EmploymentType_EMPLOYMENT_TYPE_WORKING_PROFESSIONAL: {
		EmploymentType: types.EmploymentType_EMPLOYMENT_TYPE_WORKING_PROFESSIONAL,
		EmploymentText: "Professional",
	},
	types.EmploymentType_EMPLOYMENT_TYPE_STUDENT: {
		EmploymentType: types.EmploymentType_EMPLOYMENT_TYPE_STUDENT,
		EmploymentText: "Student",
	},
	types.EmploymentType_EMPLOYMENT_TYPE_HOMEMAKER: {
		EmploymentType: types.EmploymentType_EMPLOYMENT_TYPE_HOMEMAKER,
		EmploymentText: "Homemaker",
	},
	types.EmploymentType_EMPLOYMENT_TYPE_RETIRED: {
		EmploymentType: types.EmploymentType_EMPLOYMENT_TYPE_RETIRED,
		EmploymentText: "Retired",
	},
	types.EmploymentType_EMPLOYMENT_TYPE_OTHERS: {
		EmploymentType: types.EmploymentType_EMPLOYMENT_TYPE_OTHERS,
		EmploymentText: "Others",
	},
	types.EmploymentType_EMPLOYMENT_TYPE_SELF_EMPLOYED: {
		EmploymentType: types.EmploymentType_EMPLOYMENT_TYPE_SELF_EMPLOYED,
		EmploymentText: "Self Employed",
	},
}

// convertEnumTypeByName converts one enum type to another enum type, by matching their name strings. It assumes that,
// the 0 value is defined as the unspecified value.
func convertEnumTypeByName(valToNameMap map[int32]string, nameToValMap map[string]int32, val int32) int32 {
	// Assume that 0 value is of unspecified type
	unspecifiedStr := valToNameMap[0]

	name, ok := valToNameMap[val]
	if !ok {
		return nameToValMap[unspecifiedStr]
	}
	otherVal, ok := nameToValMap[name]
	if !ok {
		return nameToValMap[unspecifiedStr]
	}
	return otherVal
}

func GetBeLoanProgramFromFe(feLp palFeEnumsPb.LoanProgram) palBePb.LoanProgram {
	switch feLp {
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN:
		return palBePb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY:
		return palBePb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_FLDG:
		return palBePb.LoanProgram_LOAN_PROGRAM_FLDG
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_STPL:
		return palBePb.LoanProgram_LOAN_PROGRAM_STPL
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL:
		return palBePb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND:
		return palBePb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_LAMF:
		return palBePb.LoanProgram_LOAN_PROGRAM_LAMF
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_ETB:
		return palBePb.LoanProgram_LOAN_PROGRAM_REAL_TIME_ETB
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION:
		return palBePb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2:
		return palBePb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION:
		return palBePb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REALTIME_STPL:
		return palBePb.LoanProgram_LOAN_PROGRAM_REALTIME_STPL
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY:
		return palBePb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_STPL:
		return palBePb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_STPL
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_SUBVENTION:
		return palBePb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_SUBVENTION
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB:
		return palBePb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB
	default:
		return palBePb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED
	}
}

func GetFeLoanProgramFromBe(beLp palBePb.LoanProgram) palFeEnumsPb.LoanProgram {
	switch beLp {
	case palBePb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN
	case palBePb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY
	case palBePb.LoanProgram_LOAN_PROGRAM_FLDG:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_FLDG
	case palBePb.LoanProgram_LOAN_PROGRAM_STPL:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_STPL
	case palBePb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL
	case palBePb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND
	case palBePb.LoanProgram_LOAN_PROGRAM_LAMF:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_LAMF
	case palBePb.LoanProgram_LOAN_PROGRAM_REAL_TIME_ETB:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_ETB
	case palBePb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION
	case palBePb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2
	case palBePb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION
	case palBePb.LoanProgram_LOAN_PROGRAM_REALTIME_STPL:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REALTIME_STPL
	case palBePb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY
	case palBePb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_STPL:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_STPL
	case palBePb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_SUBVENTION:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_SUBVENTION
	case palBePb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB
	default:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED
	}
}

func GetBeVendorFromFe(feVendor palFeEnumsPb.Vendor) palBePb.Vendor {
	switch feVendor {
	case palFeEnumsPb.Vendor_FEDERAL_BANK:
		return palBePb.Vendor_FEDERAL
	case palFeEnumsPb.Vendor_LIQUILOANS:
		return palBePb.Vendor_LIQUILOANS
	case palFeEnumsPb.Vendor_IDFC:
		return palBePb.Vendor_IDFC
	case palFeEnumsPb.Vendor_FIFTYFIN:
		return palBePb.Vendor_FIFTYFIN
	case palFeEnumsPb.Vendor_ABFL:
		return palBePb.Vendor_ABFL
	case palFeEnumsPb.Vendor_STOCK_GUARDIAN_LSP:
		return palBePb.Vendor_STOCK_GUARDIAN_LSP
	case palFeEnumsPb.Vendor_MONEYVIEW:
		return palBePb.Vendor_MONEYVIEW
	case palFeEnumsPb.Vendor_EPIFI_TECH:
		return palBePb.Vendor_EPIFI_TECH
	case palFeEnumsPb.Vendor_LENDEN:
		return palBePb.Vendor_LENDEN
	default:
		return palBePb.Vendor_VENDOR_UNSPECIFIED
	}
}

func GetFeVendorFromBe(beVendor palBePb.Vendor) fePb.Vendor {
	switch beVendor {
	case palBePb.Vendor_FEDERAL:
		return fePb.Vendor_FEDERAL_BANK
	case palBePb.Vendor_LIQUILOANS:
		return fePb.Vendor_LIQUILOANS
	case palBePb.Vendor_IDFC:
		return fePb.Vendor_IDFC
	case palBePb.Vendor_FIFTYFIN:
		return fePb.Vendor_FIFTYFIN
	case palBePb.Vendor_MONEYVIEW:
		return fePb.Vendor_MONEYVIEW
	default:
		return fePb.Vendor_VENDOR_UNSPECIFIED
	}
}

func GetPalFeVendorFromBe(beVendor palBePb.Vendor) palFeEnumsPb.Vendor {
	switch beVendor {
	case palBePb.Vendor_FEDERAL:
		return palFeEnumsPb.Vendor_FEDERAL_BANK
	case palBePb.Vendor_LIQUILOANS:
		return palFeEnumsPb.Vendor_LIQUILOANS
	case palBePb.Vendor_IDFC:
		return palFeEnumsPb.Vendor_IDFC
	case palBePb.Vendor_FIFTYFIN:
		return palFeEnumsPb.Vendor_FIFTYFIN
	case palBePb.Vendor_ABFL:
		return palFeEnumsPb.Vendor_ABFL
	case palBePb.Vendor_MONEYVIEW:
		return palFeEnumsPb.Vendor_MONEYVIEW
	case palBePb.Vendor_EPIFI_TECH:
		return palFeEnumsPb.Vendor_EPIFI_TECH
	case palBePb.Vendor_STOCK_GUARDIAN_LSP:
		return palFeEnumsPb.Vendor_STOCK_GUARDIAN_LSP
	case palBePb.Vendor_LENDEN:
		return palFeEnumsPb.Vendor_LENDEN
	default:
		return palFeEnumsPb.Vendor_VENDOR_UNSPECIFIED
	}
}

func GetBeLoanTypeFromFe(feLoanType palFeEnumsPb.LoanType) palBePb.LoanType {
	return palBePb.LoanType(convertEnumTypeByName(palFeEnumsPb.LoanType_name, palBePb.LoanType_value, int32(feLoanType)))
}

func GetFeLoanTypeFromBe(beLoanType palBePb.LoanType) palFeEnumsPb.LoanType {
	return palFeEnumsPb.LoanType(convertEnumTypeByName(palBePb.LoanType_name, palFeEnumsPb.LoanType_value, int32(beLoanType)))
}

func GetBeLoanHeaderByFeLoanHeader(lh *palFeEnumsPb.LoanHeader) *palBePb.LoanHeader {
	return &palBePb.LoanHeader{
		LoanProgram: GetBeLoanProgramFromFe(lh.GetLoanProgram()),
		Vendor:      GetBeVendorFromFe(lh.GetVendor()),
		LoanType:    GetBeLoanTypeFromFe(lh.GetLoanType()),
		DataOwner:   GetBeVendorFromFe(lh.GetDataOwner()),
	}
}

func GetFeLoanHeaderByBeLoanHeader(lh *palBePb.LoanHeader) *palFeEnumsPb.LoanHeader {
	return &palFeEnumsPb.LoanHeader{
		LoanProgram: GetFeLoanProgramFromBe(lh.GetLoanProgram()),
		Vendor:      GetPalFeVendorFromBe(lh.GetVendor()),
		LoanType:    GetFeLoanTypeFromBe(lh.GetLoanType()),
	}
}

func LogIfLoanHeaderNotPresent(ctx context.Context, lh *palFeEnumsPb.LoanHeader, callerName string) {
	if lh == nil ||
		lh.GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED ||
		lh.GetVendor() == palFeEnumsPb.Vendor_VENDOR_UNSPECIFIED {
		logger.Info(
			ctx,
			fmt.Sprintf("improper loan header received...loanProgram:%v, vendor:%v", lh.GetLoanProgram().String(), lh.GetVendor().String()),
			zap.String("grpc.method", callerName),
			zap.Int(logger.APP_VERSION_CODE, epificontext.AppVersionFromContext(ctx)))
	}
}

// GetBeUserInputForVerifyDetails is used to map FE userDetails entered by the user to BE userDetails
func GetBeUserInputForVerifyDetails(input *palFePb.UserInput) *palBePb.UserInput {
	if input == nil || input.GetUserInput() == nil {
		return &palBePb.UserInput{}
	}
	switch input.GetUserInput().(type) {
	case *palFePb.UserInput_PhoneAndEmailDetails:
		return &palBePb.UserInput{
			UserInput: &palBePb.UserInput_PhoneAndEmailDetails{
				PhoneAndEmailDetails: &palBePb.PhoneAndEmailDetails{
					PhoneNumber: input.GetPhoneAndEmailDetails().GetPhoneNumber(),
					Email:       input.GetPhoneAndEmailDetails().GetEmail(),
				},
			},
		}
	case *palFePb.UserInput_AdditionalKycDetails:
		return &palBePb.UserInput{
			UserInput: &palBePb.UserInput_AdditionalKycDetails{
				AdditionalKycDetails: &palBePb.AdditionalKycDetails{
					EmploymentType: input.GetAdditionalKycDetails().GetEmploymentType(),
					MaritalStatus:  input.GetAdditionalKycDetails().GetMaritalStatus(),
					ResidenceType:  input.GetAdditionalKycDetails().GetResidenceType(),
					FatherName:     input.GetAdditionalKycDetails().GetFatherName(),
					MotherName:     input.GetAdditionalKycDetails().GetMotherName(),
				},
			},
		}
	case *palFePb.UserInput_SelfieDetails:
		return &palBePb.UserInput{
			UserInput: &palBePb.UserInput_SelfieDetails{
				SelfieDetails: &palBePb.SelfieDetails{
					SelfieImage: input.GetSelfieDetails().GetSelfieImage(),
				},
			},
		}
	case *palFePb.UserInput_FormDetails:
		return &palBePb.UserInput{
			UserInput: &palBePb.UserInput_FormDetails{
				FormDetails: &palBePb.FormDetails{
					FormFields: input.GetFormDetails().GetFormFields(),
				},
			},
		}
	default:
		return &palBePb.UserInput{}
	}
}

func ShouldFetchUserAccountsForTpapPrePaymentFlow(ctx context.Context, lh *palFeEnumsPb.LoanHeader, prePayConfMap *genConf.PrePayConfigMap) bool {
	switch lh.GetVendor() {
	case palFeEnumsPb.Vendor_FEDERAL_BANK:
		return prePayConfMap.FederalPl().Tpap().Enabled()
	case palFeEnumsPb.Vendor_LIQUILOANS:
		switch lh.GetLoanProgram() {
		case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_FLDG, palFeEnumsPb.LoanProgram_LOAN_PROGRAM_STPL:
			return prePayConfMap.LLFldg().Tpap().Enabled()
			// making it always live for A2L as that's the only option for users to pre-pay in A2L/Fi-Lite
		case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND,
			palFeEnumsPb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL, palFeEnumsPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_SUBVENTION,
			palFeEnumsPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_STPL:
			return true
		case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY:
			return prePayConfMap.LLEs().Tpap().Enabled()
		default:
			return false
		}
	case palFeEnumsPb.Vendor_IDFC:
		return prePayConfMap.IdfcPl().Tpap().Enabled()
	case palFeEnumsPb.Vendor_STOCK_GUARDIAN_LSP:
		return true
	case palFeEnumsPb.Vendor_LENDEN:
		return true
	default:
		return false
	}
}
func ShowOldVersionForPollingScreen(ctx context.Context, minAndroidVersion, minIosVersion int) bool {
	platform, version := epificontext.AppPlatformAndVersion(ctx)
	if (platform == commontypes.Platform_ANDROID && version < minAndroidVersion) ||
		(platform == commontypes.Platform_IOS && version < minIosVersion) {
		return true
	}
	return false
}

func GetBECallBackPayloadFromFECallBackPayload(fePayload *palFePb.CallbackPayload) *palBePb.CallbackPayload {
	switch payload := fePayload.GetPayload().(type) {
	case *palFePb.CallbackPayload_MandatePayload:
		return &palBePb.CallbackPayload{
			Paylaod: &palBePb.CallbackPayload_MandatePayload{
				MandatePayload: &palBePb.MandatePayload{
					MandateViewType:  payload.MandatePayload.GetMandateViewType(),
					MandateSdkVendor: payload.MandatePayload.GetMandateSdkVendor(),
					MandateSdkEvent: &palBePb.MandateSdkEvent{
						EventPayload: payload.MandatePayload.GetMandateSdkEvent().GetEventPayload(),
						EventType:    palBePb.MandateSdkEvent_EventType(palFePb.MandateSdkEvent_EventType_value[payload.MandatePayload.GetMandateSdkEvent().GetEventType().String()]),
					},
				},
			},
		}
	default:
		return nil
	}
}

func GetBEEsignDocTypeFromFEESignDocType(feEsignType palFeEnumsPb.LoanDocType) palBePb.LoanDocType {
	switch feEsignType {
	case palFeEnumsPb.LoanDocType_LOAN_DOC_TYPE_KFS:
		return palBePb.LoanDocType_LOAN_DOC_TYPE_KFS
	case palFeEnumsPb.LoanDocType_LOAN_DOC_TYPE_LOAN_AGREEMENT:
		return palBePb.LoanDocType_LOAN_DOC_TYPE_LOAN_AGREEMENT
	default:
		return palBePb.LoanDocType_LOAN_DOC_TYPE_UNSPECIFIED
	}
}

func GetPalOwnership(vendor palFeEnumsPb.Vendor) commontypes.Ownership {
	switch vendor {
	case palFeEnumsPb.Vendor_FEDERAL_BANK:
		return commontypes.Ownership_FEDERAL_BANK
	case palFeEnumsPb.Vendor_LIQUILOANS:
		return commontypes.Ownership_LIQUILOANS_PL
	case palFeEnumsPb.Vendor_IDFC:
		return commontypes.Ownership_IDFC_PL
	case palFeEnumsPb.Vendor_FIFTYFIN:
		return commontypes.Ownership_FIFTYFIN_LAMF
	case palFeEnumsPb.Vendor_MONEYVIEW:
		return commontypes.Ownership_MONEYVIEW_PL
	case palFeEnumsPb.Vendor_ABFL:
		return commontypes.Ownership_LOANS_ABFL
	case palFeEnumsPb.Vendor_EPIFI_TECH:
		return commontypes.Ownership_EPIFI_TECH_V2
	case palFeEnumsPb.Vendor_LENDEN:
		return commontypes.Ownership_LOANS_LENDEN
	default:
		return commontypes.Ownership_EPIFI_TECH
	}
}

var FeToBeLseFlow = map[palFeEnumsPb.LoanStepExecutionFlow]palBePb.LoanStepExecutionFlow{
	palFeEnumsPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_ELIGIBILITY: palBePb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_ELIGIBILITY,
}

var FeToBeLoanAccountType = map[string]palBePb.LoanPaymentAccountType{
	palFeEnumsPb.LoanPaymentAccountType_LOAN_PAYMENT_ACCOUNT_TYPE_UNSPECIFIED.String(): palBePb.LoanPaymentAccountType_LOAN_PAYMENT_ACCOUNT_TYPE_UNSPECIFIED,
	palFeEnumsPb.LoanPaymentAccountType_LOAN_PAYMENT_ACCOUNT_TYPE_INTEREST.String():    palBePb.LoanPaymentAccountType_LOAN_PAYMENT_ACCOUNT_TYPE_INTEREST,
	palFeEnumsPb.LoanPaymentAccountType_LOAN_PAYMENT_ACCOUNT_TYPE_PRINCIPAL.String():   palBePb.LoanPaymentAccountType_LOAN_PAYMENT_ACCOUNT_TYPE_PRINCIPAL,
	palFeEnumsPb.LoanPaymentAccountType_LOAN_PAYMENT_ACCOUNT_TYPE_COMMON.String():      palBePb.LoanPaymentAccountType_LOAN_PAYMENT_ACCOUNT_TYPE_COMMON,
}
