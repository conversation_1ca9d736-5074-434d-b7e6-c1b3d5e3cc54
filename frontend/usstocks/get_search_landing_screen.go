package usstocks

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"

	"go.uber.org/zap"
	fieldmaskPb "google.golang.org/protobuf/types/known/fieldmaskpb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	headerFe "github.com/epifi/gamma/api/frontend/header"
	usstocksFePb "github.com/epifi/gamma/api/frontend/usstocks"
	usstocksCatalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	usstocksUi "github.com/epifi/gamma/frontend/usstocks/ui"
	"github.com/epifi/gamma/frontend/usstocks/utils"
)

func (s *Service) GetSearchLandingScreen(
	ctx context.Context,
	req *usstocksFePb.GetSearchLandingScreenRequest,
) (*usstocksFePb.GetSearchLandingScreenResponse, error) {
	res := &usstocksFePb.GetSearchLandingScreenResponse{
		RespHeader: &headerFe.ResponseHeader{},
	}
	actorId := req.GetReq().GetAuth().GetActorId()
	platform := req.GetReq().GetAuth().GetDevice().GetPlatform()
	version := req.GetReq().GetAppVersionCode()

	getCollectionWithStocksRes, err := s.catalogManagerClient.GetCollectionWithStocks(ctx,
		&usstocksCatalogPb.GetCollectionWithStocksRequest{
			CollectionId:   "SEARCH_LANDING",
			StockFieldMask: &fieldmaskPb.FieldMask{Paths: []string{"symbol", "company_info", "daily_performance"}},
			ActorId:        actorId,
			Version:        version,
			Platform:       platform,
		})
	if err = epifigrpc.RPCError(getCollectionWithStocksRes, err); err != nil {
		if getCollectionWithStocksRes.GetStatus().IsResourceExhausted() {
			logger.Error(ctx, "resource exhausted while calling GetCollectionWithStocks RPC", zap.Error(err))
			res.RespHeader.Status = rpc.StatusResourceExhausted()
			return res, nil
		}
		logger.Error(ctx, "error while calling GetCollectionWithStocks RPC", zap.Error(err))
		res.RespHeader.Status = rpc.StatusInternal()
		return res, nil
	}

	collectionStocks := getCollectionWithStocksRes.GetCollectionWithStocks().GetCollectionStocks()
	collectionTitle := getCollectionWithStocksRes.GetCollectionWithStocks().GetCollection().GetDisplayDetails().GetTitle()
	stocks := utils.GetStocksFromCollections(collectionStocks)
	stockChips := utils.GetStockChipsFromStocks(stocks)

	res.Screen = &usstocksFePb.SearchLandingScreen{
		HeaderTitle:       usstocksUi.GetText("Find your stock", usstocksUi.Snow, commontypes.FontStyle_HEADLINE_3),
		SearchPlaceholder: usstocksUi.GetText("Enter a stock name or keyword", usstocksUi.Lead, commontypes.FontStyle_SUBTITLE_3),
		Sections: []*usstocksFePb.SearchLandingSection{
			{
				Container: &usstocksFePb.SearchLandingSection_Header{
					Header: &usstocksFePb.HeaderSection{
						Title: usstocksUi.GetText(collectionTitle, "#8D8D8D", commontypes.FontStyle_SUBTITLE_3),
					},
				},
			},
			{
				Container: &usstocksFePb.SearchLandingSection_ChipList{
					ChipList: &usstocksFePb.StockChipsList{
						Chips: stockChips,
					},
				},
			},
		},
	}
	res.RespHeader.Status = rpc.StatusOk()
	return res, nil
}
