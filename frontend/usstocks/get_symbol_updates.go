package usstocks

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifierrors"

	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	googleMoney "google.golang.org/genproto/googleapis/type/money"
	fieldmaskPb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	moneyPb "github.com/epifi/be-common/pkg/money"
	"github.com/epifi/gamma/api/frontend/header"
	"github.com/epifi/gamma/api/frontend/usstocks"
	typesPb "github.com/epifi/gamma/api/typesv2"
	chartPb "github.com/epifi/gamma/api/typesv2/chart"
	"github.com/epifi/gamma/api/usstocks/catalog"
	usstocksUi "github.com/epifi/gamma/frontend/usstocks/ui"
	"github.com/epifi/gamma/frontend/usstocks/utils"
)

type ChartDatapoint struct {
	Timestamp int64   `json:"time"`
	Value     float64 `json:"value"`
}

// Note: This RPC is exposed to web and doesn't require authentication necessarily

func (s *Service) GetSymbolUpdates(req *usstocks.GetSymbolUpdatesRequest, server usstocks.USStocks_GetSymbolUpdatesServer) error {
	ctx := server.Context()

	// Authenticate requests originating from Android & iOS. For requests originating from website, authentication is not needed
	// This will help debug issues that are device, platform or app version specific
	if req.GetReq().GetAuth().GetDevice().GetPlatform() == commontypes.Platform_ANDROID ||
		req.GetReq().GetAuth().GetDevice().GetPlatform() == commontypes.Platform_IOS {
		authActorId, authErr := authenticateHeaders(ctx, s.authClient, req.GetReq().GetAuth())
		if authErr != nil {
			logger.Error(ctx, "error authenticating req headers", zap.Error(authErr))
			return authErr
		}
		req.GetReq().GetAuth().ActorId = authActorId
		ctx = epificontext.CtxWithActorId(ctx, authActorId)
	}

	stocksRes, err := s.catalogManagerClient.GetStocks(ctx, &catalog.GetStocksRequest{
		Ids:       req.GetSymbolIds(),
		FieldMask: &fieldmaskPb.FieldMask{Paths: []string{"daily_performance", "exchange"}},
	})
	if te := epifigrpc.RPCError(stocksRes, err); te != nil {
		if stocksRes.GetStatus().IsResourceExhausted() {
			return errors.Wrap(epifierrors.ErrResourceExhausted, "resource exhausted while getting stock daily performance")
		}
		return fmt.Errorf("error getting stock daily performance: %w", te)
	}

	stockIdToMarketOpenPriceMap := map[string]*googleMoney.Money{}
	for stockId, stock := range stocksRes.GetStocks() {
		stockIdToMarketOpenPriceMap[stockId] = stock.GetDailyPerformance().GetOpen()
	}

	// BE stream connection may break due to multiple reason like
	// 1. vendor connection failure
	// 2. BE/VG server deployment
	// 3. Transient issue at BE/VG
	// retry should be done with sendPriceUpdates time interval to handle such cases
	for noOfAttempts := uint(0); !s.priceUpdatesRetryStrategy.IsRetryExhausted(noOfAttempts); noOfAttempts++ {
		priceUpdatesStream, err := s.catalogManagerStreamClient.GetPriceUpdates(ctx, &catalog.GetPriceUpdatesRequest{StockIds: req.GetSymbolIds()})
		if err != nil {
			logger.Error(ctx, "error in getting price updates", zap.Error(err))
			// sleep for configured interval before next reattempt
			time.Sleep(time.Duration(s.priceUpdatesRetryStrategy.GetNextRetryInterval(noOfAttempts)))
			// reattempt for getting stream connection
			continue
		}

		shouldRetry := s.sendPriceUpdates(ctx, priceUpdatesStream, server, stockIdToMarketOpenPriceMap)
		if !shouldRetry {
			logger.Info(ctx, "non retryable error encountered, closing stream connection")
			return nil
		}
		// sleep for configured interval before next reattempt
		time.Sleep(time.Duration(s.priceUpdatesRetryStrategy.GetNextRetryInterval(noOfAttempts)))
	}
	return nil
}

// waits on BE stream connection for price updates and sends the update to client stream
// based on type of error at BE connection, if BE connection should be reattempted is determined
func (s *Service) sendPriceUpdates(ctx context.Context, priceUpdatesStream catalog.CatalogManager_GetPriceUpdatesClient,
	server usstocks.USStocks_GetSymbolUpdatesServer, stockIdToMarketOpenPriceMap map[string]*googleMoney.Money) bool {
	for {
		// read price updates from BE
		priceUpdate, err := priceUpdatesStream.Recv()
		if err = epifigrpc.RPCError(priceUpdate, err); err != nil {
			logger.Error(ctx, "error in getting price updates", zap.Error(err))
			// if there issue while the connection was sending data, retry connection
			// can happen due to BE/VG service deployment
			return true
		}
		var updatesMap map[string]*usstocks.SymbolUpdate
		if priceUpdate.GetStockPriceUpdateDatapoint().GetTrade() != nil {
			feStockPriceUpdate := getFeStockPriceUpdate(priceUpdate.GetStockPriceUpdateDatapoint().GetTrade().GetPrice(),
				priceUpdate.GetStockPriceUpdateDatapoint().GetTrade().GetUpdatedAt(), stockIdToMarketOpenPriceMap[priceUpdate.GetStockPriceUpdateDatapoint().GetStockId()])
			updatesMap = map[string]*usstocks.SymbolUpdate{priceUpdate.GetStockPriceUpdateDatapoint().GetStockId(): feStockPriceUpdate}
		}
		feStockPriceUpdateDatapoint, err := getStockPriceUpdateDatapoint(priceUpdate.GetStockPriceUpdateDatapoint(), stockIdToMarketOpenPriceMap[priceUpdate.GetStockPriceUpdateDatapoint().GetStockId()])
		if err != nil {
			logger.Error(ctx, "error getting FE stock price update datapoint", zap.Error(err))
			return true
		}

		// relay the received price update to client
		err = server.SendMsg(&usstocks.GetSymbolUpdatesResponse{
			RespHeader:           &header.ResponseHeader{Status: rpc.StatusOk()},
			Updates:              updatesMap,
			StockPriceUpdatesMap: map[string]*usstocks.StockPriceUpdateDataPoint{priceUpdate.GetStockPriceUpdateDatapoint().GetStockId(): feStockPriceUpdateDatapoint},
		})
		if err != nil {
			logger.Error(ctx, "error in sending price updates to client", zap.Error(err))
			// if there is error in writing to response stream, BE connection retry is not required
			// client is expected to recall the GetSymbolUpdates RPC
			return false
		}

		// complete execution if context canceled or context deadline exceeded
		if ctx.Err() != nil {
			logger.Error(ctx, "context error", zap.Error(ctx.Err()))
			return false
		}
	}
}

func getFeStockPriceUpdate(tradePriceVal float64, updatedAt *timestampPb.Timestamp, marketOpenPrice *googleMoney.Money) *usstocks.SymbolUpdate {
	latestPrice := moneyPb.ParseFloat(tradePriceVal, moneyPb.USDCurrencyCode)
	marketOpenPriceVal, _ := moneyPb.ToDecimal(marketOpenPrice).Float64()
	percentageChange := (tradePriceVal/marketOpenPriceVal - 1) * 100
	return &usstocks.SymbolUpdate{
		StockPrice:            typesPb.GetFromBeMoney(latestPrice),
		GrowthText:            getDailyPercentageChangeText(percentageChange),
		UpdatedAt:             updatedAt,
		PercentageReturns:     utils.GetStockPercentageReturns(percentageChange),
		StockPriceText:        getTextForString(moneyPb.ToDisplayString(latestPrice), "#F5F5F5", commontypes.FontStyle_SUBTITLE_2, false),
		PercentagePriceChange: percentageChange,
	}
}

func getDailyPercentageChangeText(percentageChange float64) *commontypes.Text {
	textColor := usstocksUi.DarkMint
	growthStr := fmt.Sprintf("+%.2f%%", percentageChange)
	if percentageChange < 0 {
		textColor = usstocksUi.DarkPeach
		growthStr = fmt.Sprintf("%.2f%%", percentageChange)
	}
	return &commontypes.Text{
		FontColor:    textColor,
		DisplayValue: &commontypes.Text_PlainString{PlainString: growthStr},
		FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
	}
}

func getStockPriceUpdateDatapoint(priceUpdate *catalog.StockPriceUpdateDatapoint, marketOpenPrice *googleMoney.Money) (*usstocks.StockPriceUpdateDataPoint, error) {
	switch priceUpdate.GetDatapoint().(type) {
	case *catalog.StockPriceUpdateDatapoint_Trade:
		return &usstocks.StockPriceUpdateDataPoint{Datapoint: &usstocks.StockPriceUpdateDataPoint_PriceUpdate{
			PriceUpdate: getFeStockPriceUpdate(priceUpdate.GetTrade().GetPrice(), priceUpdate.GetTrade().GetUpdatedAt(), marketOpenPrice)},
		}, nil
	case *catalog.StockPriceUpdateDatapoint_MinuteBar:
		jsChartUpdateCmd, err := getJSUpdateChartDataCmd(priceUpdate.GetMinuteBar())
		if err != nil {
			return nil, errors.Wrap(err, "error getting js update chart cmd")
		}
		return &usstocks.StockPriceUpdateDataPoint{Datapoint: &usstocks.StockPriceUpdateDataPoint_MinuteBar{
			MinuteBar: &usstocks.StockBar{
				Timestamp:                priceUpdate.GetMinuteBar().GetTs(),
				ClosePrice:               priceUpdate.GetMinuteBar().GetClosePrice(),
				UpdateChartDataJsCommand: jsChartUpdateCmd,
			},
		}}, nil
	default:
		return nil, fmt.Errorf("unhandled stock price update datapoint: %v", priceUpdate.GetDatapoint())
	}
}

func getJSUpdateChartDataCmd(barUpdate *catalog.Bar) (string, error) {
	chartDatapoint := &chartPb.DataPoint{
		Timestamp: barUpdate.Ts,
		Value:     barUpdate.ClosePrice,
		Label:     USStockPriceChartLabel,
	}
	command, err := chartDatapoint.GetJSUpdateDataCommand()
	if err != nil {
		return "", errors.Wrap(err, "error while creating update command")
	}
	return command, nil
}
