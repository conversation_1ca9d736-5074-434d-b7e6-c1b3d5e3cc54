package usstocks

import (
	"context"
	"fmt"
	"math"
	"sort"
	"time"

	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/fieldmaskpb"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"github.com/epifi/gamma/pkg/fittt"

	"github.com/epifi/be-common/api/rpc"
	widgetUi "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	usstocksFePb "github.com/epifi/gamma/api/frontend/usstocks"
	usstocksEnumFePb "github.com/epifi/gamma/api/frontend/usstocks/client_states"
	types "github.com/epifi/gamma/api/typesv2"
	typesPb "github.com/epifi/gamma/api/typesv2"
	chartPb "github.com/epifi/gamma/api/typesv2/chart"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/usstocks/screen_options_v2"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/usstocks"
	usstocksAccountPb "github.com/epifi/gamma/api/usstocks/account"
	usstocksCatalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	"github.com/epifi/gamma/api/usstocks/order"
	usstocksPortfolioPb "github.com/epifi/gamma/api/usstocks/portfolio"
	usstocksUi "github.com/epifi/gamma/frontend/usstocks/ui"
	"github.com/epifi/gamma/frontend/usstocks/utils"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/feature/release"
	pkgOnboarding "github.com/epifi/gamma/pkg/onboarding"
	"github.com/epifi/gamma/pkg/usstocks/deeplinks"
)

var (
	USStockPriceChartLabel = "$"
	// nolint: dupl
	temporaryClosedForMaintenanceDeeplink = &deeplink.Deeplink{
		Screen: deeplink.Screen_USSTOCKS_BUY_SELL_CLOSED_SCREEN,
		ScreenOptions: &deeplink.Deeplink_UsstocksBuySellClosedScreenOptions{
			UsstocksBuySellClosedScreenOptions: &deeplink.USStocksBuySellClosedScreenOptions{
				ImageTitleSubtitle: &widgetUi.ImageTitleSubtitleElement{
					IconImage: &commontypes.Image{
						ImageType: commontypes.ImageType_PNG,
						ImageUrl:  "https://epifi-icons.pointz.in/usstocks_images/closed-icon.png",
						Width:     80,
						Height:    80,
					},
					TitleText: usstocksUi.GetText("Temporarily closed for maintenance!", usstocksUi.Night,
						commontypes.FontStyle_SUBTITLE_1),
					SubtitleText: usstocksUi.GetText("We would appreciate your patience as we improve your US Stocks experience. Rest assured; we will reopen shortly.", usstocksUi.Night,
						commontypes.FontStyle_BODY_3_PARA),
				},
				Cta: &deeplink.Cta{
					Type:         deeplink.Cta_DONE,
					Text:         "Ok, got it",
					DisplayTheme: deeplink.Cta_PRIMARY,
				},
			},
		},
	}
	// nolint: dupl
	routineMaintenanceDeeplink = &deeplink.Deeplink{
		Screen: deeplink.Screen_USSTOCKS_BUY_SELL_CLOSED_SCREEN,
		ScreenOptions: &deeplink.Deeplink_UsstocksBuySellClosedScreenOptions{
			UsstocksBuySellClosedScreenOptions: &deeplink.USStocksBuySellClosedScreenOptions{
				ImageTitleSubtitle: &widgetUi.ImageTitleSubtitleElement{
					IconImage: &commontypes.Image{
						ImageType: commontypes.ImageType_PNG,
						ImageUrl:  "https://epifi-icons.pointz.in/usstocks_images/closed-icon.png",
						Width:     80,
						Height:    80,
					},
					TitleText: usstocksUi.GetText("Routine maintenance in progress", usstocksUi.Night,
						commontypes.FontStyle_SUBTITLE_1),
					SubtitleText: usstocksUi.GetText("We’re doing a quick tune-up to improve your US Stocks withdrawal experience. Some actions might be briefly unavailable, but everything will be back up and running shortly. Thank you for your patience.", usstocksUi.Night,
						commontypes.FontStyle_BODY_3_PARA),
				},
				Cta: &deeplink.Cta{
					Type:         deeplink.Cta_DONE,
					Text:         "Ok, got it",
					DisplayTheme: deeplink.Cta_PRIMARY,
				},
			},
		},
	}

	creditFrozenDeeplink = &deeplink.Deeplink{
		Screen: deeplink.Screen_USSTOCKS_BUY_SELL_CLOSED_SCREEN,
		ScreenOptions: &deeplink.Deeplink_UsstocksBuySellClosedScreenOptions{
			UsstocksBuySellClosedScreenOptions: &deeplink.USStocksBuySellClosedScreenOptions{
				ImageTitleSubtitle: &widgetUi.ImageTitleSubtitleElement{
					IconImage: &commontypes.Image{
						ImageType: commontypes.ImageType_PNG,
						ImageUrl:  "https://epifi-icons.pointz.in/usstocks_images/credit-frozen.png",
						Width:     80,
						Height:    80,
					},
					TitleText: usstocksUi.GetText("Your Federal Bank account's frozen", usstocksUi.Night,
						commontypes.FontStyle_SUBTITLE_1),
					SubtitleText: usstocksUi.GetText("Don't panic — your account was flagged during a periodic review by the bank. Due to this, you can not receive the proceeds of your stocks' sale. Contact Fi Money customer care & we'll help you sort things out.", usstocksUi.Night,
						commontypes.FontStyle_BODY_3_PARA),
				},
				Cta: &deeplink.Cta{
					Type:         deeplink.Cta_DONE,
					Text:         "Okay",
					DisplayTheme: deeplink.Cta_PRIMARY,
				},
				SecondaryCta: &deeplink.Cta{
					Type:         deeplink.Cta_DONE,
					Text:         "Get Help",
					DisplayTheme: deeplink.Cta_PRIMARY,
				},
			},
		},
	}

	// nolint: dupl
	sellLockDeeplink = &deeplink.Deeplink{
		Screen: deeplink.Screen_USSTOCKS_BUY_SELL_CLOSED_SCREEN,
		ScreenOptions: &deeplink.Deeplink_UsstocksBuySellClosedScreenOptions{
			UsstocksBuySellClosedScreenOptions: &deeplink.USStocksBuySellClosedScreenOptions{
				ImageTitleSubtitle: &widgetUi.ImageTitleSubtitleElement{
					IconImage: &commontypes.Image{
						ImageType: commontypes.ImageType_PNG,
						ImageUrl:  "https://epifi-icons.pointz.in/usstocks_images/sell_lock_icon.png",
						Width:     80,
						Height:    80,
					},
					TitleText: usstocksUi.GetText("Your units are under sell-lock", usstocksUi.Night,
						commontypes.FontStyle_SUBTITLE_1),
					SubtitleText: usstocksUi.GetText("Sell lock means that you won’t be able to immediately sell the stocks that you have bought. \n\nYou’ll be able to sell the stocks you bought once we finish international remittance process (T+2).", usstocksUi.Night,
						commontypes.FontStyle_BODY_3_PARA),
				},
				Cta: &deeplink.Cta{
					Type:         deeplink.Cta_DONE,
					Text:         "Ok, got it",
					DisplayTheme: deeplink.Cta_PRIMARY,
				},
			},
		},
	}

	// nolint: dupl
	sellOrderInProgressDeeplink = &deeplink.Deeplink{
		Screen: deeplink.Screen_USSTOCKS_BUY_SELL_CLOSED_SCREEN,
		ScreenOptions: &deeplink.Deeplink_UsstocksBuySellClosedScreenOptions{
			UsstocksBuySellClosedScreenOptions: &deeplink.USStocksBuySellClosedScreenOptions{
				ImageTitleSubtitle: &widgetUi.ImageTitleSubtitleElement{
					IconImage: &commontypes.Image{
						ImageType: commontypes.ImageType_PNG,
						ImageUrl:  "https://epifi-icons.pointz.in/usstocks_images/Group+174963026.png",
						Width:     80,
						Height:    80,
					},
					TitleText: usstocksUi.GetText("You already have a sell order in process", usstocksUi.Night,
						commontypes.FontStyle_SUBTITLE_1),
					SubtitleText: usstocksUi.GetText("To prevent accidental duplicate orders, only one sell order can be active at a time. You can place a new order once your current one is confirmed, or cancel the active order to try again.", usstocksUi.Night,
						commontypes.FontStyle_BODY_S),
				},
				Cta: &deeplink.Cta{
					Type:         deeplink.Cta_DONE,
					Text:         "Ok, got it",
					DisplayTheme: deeplink.Cta_PRIMARY,
				},
			},
		},
	}

	stockNotAvailableDeeplink = &deeplink.Deeplink{
		Screen: deeplink.Screen_USSTOCKS_BUY_SELL_CLOSED_SCREEN,
		ScreenOptions: &deeplink.Deeplink_UsstocksBuySellClosedScreenOptions{
			UsstocksBuySellClosedScreenOptions: &deeplink.USStocksBuySellClosedScreenOptions{
				ImageTitleSubtitle: &widgetUi.ImageTitleSubtitleElement{
					IconImage: &commontypes.Image{
						ImageType: commontypes.ImageType_PNG,
						ImageUrl:  "https://epifi-icons.pointz.in/usstocks_images/closed-icon.png",
						Width:     80,
						Height:    80,
					},
					TitleText: usstocksUi.GetText("Stock is not available for purchase", usstocksUi.Night,
						commontypes.FontStyle_SUBTITLE_M),
					SubtitleText: usstocksUi.GetText("To protect your returns, buy/sell option for this stock has not been enabled at the moment.", usstocksUi.Night,
						commontypes.FontStyle_BODY_3_PARA),
				},
				Cta: &deeplink.Cta{
					Type:         deeplink.Cta_DONE,
					Text:         "Ok, got it",
					DisplayTheme: deeplink.Cta_PRIMARY,
				},
			},
		},
	}
)

// nolint:funlen
func (s *Service) GetSymbolDetails(ctx context.Context, req *usstocksFePb.GetSymbolDetailsRequest) (*usstocksFePb.GetSymbolDetailsResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()
	stockId := req.GetStockId()
	platform := req.GetReq().GetAuth().GetDevice().GetPlatform()
	version := req.GetReq().GetAuth().GetDevice().GetAppVersion()

	g, gCtx := errgroup.WithContext(ctx)
	var (
		stock         *usstocksCatalogPb.Stock
		tags          []*commontypes.Text
		numActivities int
		sipsPresent   bool
	)
	g.Go(func() error {
		var err error
		stock, err = utils.GetStockById(gCtx, s.catalogManagerClient, stockId)
		if err != nil {
			return errors.Wrap(err, fmt.Sprintf("error getting stock by id %s", stockId))
		}
		marketCategoryVendorIds := utils.GetMarketCategoryVendorIdsFromStocks([]*usstocksCatalogPb.Stock{stock})

		// fetch market categories for all the stocks to populate stock list item tags
		getMarketCategoriesRes, err := s.catalogManagerClient.GetMarketCategories(gCtx,
			&usstocksCatalogPb.GetMarketCategoriesRequest{
				VendorIds: marketCategoryVendorIds,
			})
		if err = epifigrpc.RPCError(getMarketCategoriesRes, err); err != nil {
			return errors.Wrap(err, "error while calling GetMarketCategories RPC")
		}
		marketCategoriesMap := getMarketCategoriesRes.GetMarketCategories()
		tags = utils.GetStockDetailsTags(stock, marketCategoriesMap)

		// get the number of activities
		activities, _, err := s.getSymbolActivities(ctx, actorId, []string{stock.GetSymbol()}, nil)
		if err != nil {
			return errors.Wrap(err, "error getting symbol activities")
		}
		numActivities = len(activities)
		// check if sips are present
		sipVerticalTiles, err := s.getSipComponentsOfSymbol(ctx, stock, actorId)
		if err != nil {
			logger.Error(ctx, "error while fetching sip tiles", zap.Error(err),
				zap.String(logger.STOCK_ID, stockId))
		}
		sipsPresent = sipVerticalTiles != nil && len(sipVerticalTiles.GetSipTile()) > 0
		return nil
	})

	var inProgressOrderCount int64
	var investmentDisplayEntries []*usstocksFePb.DisplayEntry
	var investmentSummary *usstocksFePb.StockInvestmentSummary
	var ctas []*deeplink.Cta

	g.Go(func() error {
		getStatsResp, err := s.orderManagerClient.GetStats(gCtx, &order.GetStatsRequest{
			ActorId:   actorId,
			StockIds:  []string{req.GetStockId()},
			FieldMask: []order.StatsFieldMask{order.StatsFieldMask_STATS_FIELD_MASK_IN_PROGRESS_ORDERS_COUNT},
		})
		if err2 := epifigrpc.RPCError(getStatsResp, err); err2 != nil {
			return errors.Wrap(err2, "error getting count for in progress orders")
		}
		inProgressOrderCount = getStatsResp.GetCountStats()[order.StatsFieldMask_STATS_FIELD_MASK_IN_PROGRESS_ORDERS_COUNT.String()]
		return nil
	})

	var (
		isOnboardedUser bool
		position        *usstocksPortfolioPb.Position
	)
	g.Go(func() error {
		var (
			isOnboardingInProgress, hasPendingSellOrders, isInvestedUser, hasSellUnlockedShares bool
			err                                                                                 error
		)
		isOnboardedUser, isOnboardingInProgress, err = s.isUserOnboarded(gCtx, actorId)
		if err != nil {
			return errors.Wrap(err, "error checking if user onboarded")
		}
		isInvestedUser, hasSellUnlockedShares, investmentDisplayEntries, investmentSummary, position, err = s.getInvestmentSummariesIfInvested(gCtx, actorId, stockId)
		if err != nil {
			return errors.Wrap(err, fmt.Sprintf("error getting investment summaries if user invested, stockId %s", stockId))
		}

		getPendingSellOrderResp, statsErr := s.orderManagerClient.GetStats(gCtx, &order.GetStatsRequest{
			ActorId:   actorId,
			StockIds:  []string{req.GetStockId()},
			FieldMask: []order.StatsFieldMask{order.StatsFieldMask_STATS_FIELD_MASK_UNFULFILLED_ORDERS_COUNT},
			Sides:     []usstocks.OrderSide{usstocks.OrderSide_SELL},
		})
		if err2 := epifigrpc.RPCError(getPendingSellOrderResp, statsErr); err2 != nil {
			return errors.Wrap(err2, "error getting count for in progress orders for sell")
		}

		// get stock detail with field mask as broker stock detail to get if symbol is fractional or not
		getStockResp, err := s.catalogManagerClient.GetStocks(gCtx, &usstocksCatalogPb.GetStocksRequest{
			Identifiers: &usstocksCatalogPb.GetStocksRequest_StockIds{
				StockIds: &usstocksCatalogPb.RepeatedStrings{
					Ids: []string{stockId},
				},
			},
			FieldMask: &fieldmaskpb.FieldMask{
				Paths: []string{"broker_stock_details"},
			},
		})
		if err = epifigrpc.RPCError(getStockResp, err); err != nil {
			return errors.Wrap(err, "error while calling GetStocks RPC")
		}
		stockDetails, ok := getStockResp.GetStocks()[stockId]
		if !ok {
			return fmt.Errorf("stock id not found in stocks: %s", stockId)
		}
		hasPendingSellOrders = getPendingSellOrderResp.GetCountStats()[order.StatsFieldMask_STATS_FIELD_MASK_UNFULFILLED_ORDERS_COUNT.String()] > 0
		ctas, err = s.getCTAs(gCtx, actorId, stockId, isOnboardedUser, isInvestedUser, hasSellUnlockedShares, isOnboardingInProgress,
			hasPendingSellOrders, stock.GetInternalStatus(), stockDetails.GetBrokerStockDetails().GetIsFractionable())
		if err != nil {
			return errors.Wrap(err, "error while getting ctas")
		}
		return nil
	})

	var beStockPricesForPeriods []*usstocksCatalogPb.StockPricesForPeriod
	g.Go(func() error {
		stockPricesRes, hspErr := s.catalogManagerClient.GetHistoricalStockPrices(gCtx, &usstocksCatalogPb.GetHistoricalStockPricesRequest{
			StockId: stockId,
		})
		if te := epifigrpc.RPCError(stockPricesRes, hspErr); te != nil {
			if stockPricesRes.GetStatus().IsResourceExhausted() {
				return errors.Wrap(epifierrors.ErrResourceExhausted, "resource exhausted while fetching historical stock prices")
			}
			return fmt.Errorf("error getting historical stock prices: %w", te)
		}
		beStockPricesForPeriods = stockPricesRes.GetStockPricesForPeriods()
		return nil
	})

	var marketStatus usstocks.MarketStatus
	g.Go(func() error {
		marketStatusRes, marketStatusErr := s.orderManagerClient.GetMarketStatus(gCtx, &order.GetMarketStatusRequest{})
		if te := epifigrpc.RPCError(marketStatusRes, marketStatusErr); te != nil {
			return fmt.Errorf("error getting market staus: %w", te)
		}
		marketStatus = marketStatusRes.GetMarketStatus()
		return nil
	})

	var stockWatchlistDetails *usstocksFePb.StockWatchlistDetails
	g.Go(func() error {
		var watchlistErr error
		stockWatchlistDetails, watchlistErr = s.getStockWatchlistDetails(gCtx, actorId, stockId)
		if watchlistErr != nil {
			return errors.Wrap(watchlistErr, "error getting user watchlists")
		}
		return nil
	})

	err := g.Wait()
	if err != nil {
		if errors.Is(err, epifierrors.ErrResourceExhausted) {
			logger.Error(ctx, "resource exhausted in getting symbol details", zap.Error(err))
			return &usstocksFePb.GetSymbolDetailsResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusResourceExhausted(),
				},
			}, nil
		}
		logger.Error(ctx, "error in getting symbol details", zap.Error(err))
		return &usstocksFePb.GetSymbolDetailsResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg(err.Error()),
			},
		}, nil
	}

	isInvested := investmentSummary != nil
	defaultGraphGranularity := ""
	stockPricesForTimePeriods, err := getStockPricesForTimePeriods(beStockPricesForPeriods, req.GetReq().GetAuth().GetDevice().GetPlatform(), marketStatus)
	if err != nil {
		logger.Error(ctx, "error getting stock prices for time periods", zap.Error(err))
		return &usstocksFePb.GetSymbolDetailsResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternal()}}, nil
	}
	if len(stockPricesForTimePeriods) > 0 {
		defaultGraphGranularity = stockPricesForTimePeriods[0].GetPeriodTextForTabs().GetPlainString()
	}
	s.publishDetailsPageLoadedEvent(ctx, actorId, platform, version, stockId, stockWatchlistDetails.GetIsWatchlisted(), isInvested, isOnboardedUser, stock.GetDailyPerformance().GetClose(), defaultGraphGranularity, position.GetMarketValue())
	s.publishStockVisitEvent(ctx, stock, actorId, usstocksCatalogPb.VisitedAction_VISITED_ACTION_VIEW)
	stockType, err := getStockType(stock.GetStockType())
	if err != nil {
		logger.Error(ctx, "error in getting stockType", zap.Error(err))
		return &usstocksFePb.GetSymbolDetailsResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg(err.Error()),
			},
		}, nil
	}

	var highlightContainer *usstocksFePb.StockHighlightContainer
	if stock.GetInternalStatus() == usstocksCatalogPb.InternalStatus_INTERNAL_STATUS_NON_TRADABLE {
		ctas = nil
		highlightContainer = &usstocksFePb.StockHighlightContainer{
			LeftIconUrl:     "https://epifi-icons.pointz.in/usstocks_images/alert.png",
			RightIconUrl:    "https://epifi-icons.pointz.in/usstocks_images/right_arrow.png",
			Description:     usstocksUi.GetText("The stock is not available for purchase at the moment.", "#606265", commontypes.FontStyle_BODY_XS),
			BackgroundColor: "#FFFFFF",
			Deeplink:        stockNotAvailableDeeplink,
		}
	}

	tabs, err := s.getSymbolDetailsTabs(platform, int(version), numActivities, int(inProgressOrderCount), sipsPresent)
	if err != nil {
		logger.Error(ctx, "error getting symbol details tabs", zap.Error(err))
		return &usstocksFePb.GetSymbolDetailsResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternal()}}, nil
	}

	return &usstocksFePb.GetSymbolDetailsResponse{
		RespHeader:               &header.ResponseHeader{Status: rpc.StatusOk()},
		Tabs:                     tabs,
		SymbolDetails:            getFESymbolDetails(stock, commontypes.FontStyle_SUBTITLE_2, usstocksUi.Snow, commontypes.FontStyle_SUBTITLE_1),
		InvestmentDisplayEntries: investmentDisplayEntries,
		Ctas:                     ctas,
		StockLogo:                &commontypes.Image{ImageUrl: stock.GetStockBasicDetails().GetLogoUrl(), Width: 48, Height: 48},
		StockName:                usstocksUi.GetText(stock.GetStockBasicDetails().GetName().GetShortName(), usstocksUi.Snow, commontypes.FontStyle_SUBTITLE_1),
		// TODO(Brijesh): Figure out logic
		StockPriceIndicatorImg:    nil,
		CurrentPrice:              types.GetFromBeMoney(stock.GetDailyPerformance().GetClose()),
		Tags:                      tags,
		StockPricesForTimePeriods: stockPricesForTimePeriods,
		StockInvestmentSummary:    investmentSummary,
		StockWatchlistDetails:     stockWatchlistDetails,
		StockType:                 stockType,
		StockHighlightContainer:   highlightContainer,
	}, nil
}

// nolint: errcheck
func (s *Service) publishStockVisitEvent(parentCtx context.Context, stock *usstocksCatalogPb.Stock, actorId string, visitedAction usstocksCatalogPb.VisitedAction) {
	// web sends actorid as empty so we are not storing stock visit for this use cases
	if len(actorId) == 0 {
		return
	}
	// using WithoutCancel() returns a copy of parent that is not canceled when parent is canceled.
	ctx := context.WithoutCancel(parentCtx)
	goroutine.RunWithCtx(ctx, func(ctx context.Context) {
		closingPrice, _ := money.ToDecimal(stock.GetDailyPerformance().GetClose()).Float64()
		// making this best effort since this is kind of event
		_, _ = s.catalogManagerClient.CollectStockVisit(ctx, &usstocksCatalogPb.CollectStockVisitRequest{StockVisit: &usstocksCatalogPb.StockVisit{
			ActorId:       actorId,
			StockId:       stock.GetId(),
			VisitedAction: visitedAction,
			Stock:         &usstocksCatalogPb.VisitedStock{Price: closingPrice},
			LastViewedAt:  datetime.TimeToDateInLoc(time.Now(), datetime.IST),
		}})
		return
	})
}

func getStockType(stockType usstocksCatalogPb.StockType) (usstocksFePb.StockType, error) {
	switch stockType {
	case usstocksCatalogPb.StockType_STOCK_TYPE_INDIVIDUAL_COMPANY:
		return usstocksFePb.StockType_STOCK_TYPE_INDIVIDUAL_COMPANY, nil
	case usstocksCatalogPb.StockType_STOCK_TYPE_ETF:
		return usstocksFePb.StockType_STOCK_TYPE_ETF, nil
	default:
		return 0, fmt.Errorf("invalid stockType: %v", stockType)
	}
}

func (s *Service) getMarketStatus(ctx context.Context) (*order.ExchangeStatusResponse, error) {
	exchangeStatusRes, err := s.orderManagerClient.ExchangeStatus(ctx, &order.ExchangeStatusRequest{})
	if err = epifigrpc.RPCError(exchangeStatusRes, err); err != nil {
		return nil, fmt.Errorf("error calling ExchangeStatus RPC, %w", err)
	}
	return exchangeStatusRes, nil
}

func (s *Service) isUserOnboarded(ctx context.Context, actorId string) (isOnboardedUser, isOnboardingInProgress bool, err error) {
	res, err := s.accountManagerClient.GetAccount(ctx, &usstocksAccountPb.GetAccountRequest{
		Vendor:    commonvgpb.Vendor_ALPACA,
		ActorId:   actorId,
		FieldMask: &fieldmaskpb.FieldMask{Paths: []string{(&usstocksAccountPb.Account{}).GetAccountAccountStatusPath()}},
	})
	if te := epifigrpc.RPCError(res, err); te != nil {
		if rpc.StatusFromError(te).IsRecordNotFound() {
			return false, false, nil
		}
		return false, false, fmt.Errorf("error getting user account: %w", te)
	}
	if res.GetAccount().GetAccountStatus() != usstocksAccountPb.AccountStatus_ACTIVE {
		return false, true, nil
	}
	return true, false, nil
}

// if user is not invested, no summaries are returned
func (s *Service) getInvestmentSummariesIfInvested(ctx context.Context, actorId, stockId string) (bool, bool, []*usstocksFePb.DisplayEntry, *usstocksFePb.StockInvestmentSummary, *usstocksPortfolioPb.Position, error) {
	res, err := s.portfolioManagerClient.GetPositionBySymbol(ctx, &usstocksPortfolioPb.GetPositionBySymbolRequest{
		StockId: stockId,
		ActorId: actorId,
	})
	if te := epifigrpc.RPCError(res, err); te != nil {
		if res.GetStatus().GetCode() != uint32(usstocksPortfolioPb.GetPositionBySymbolResponse_NOT_FOUND) {
			return false, false, nil, nil, nil, fmt.Errorf("error getting position by symbol: %w", te)
		}
	}
	if res.GetPosition() == nil {
		return false, false, nil, nil, nil, nil
	}
	hasSellUnlockedShares := decimal.NewFromFloat(res.GetPosition().GetTotalQty()).GreaterThan(decimal.NewFromFloat(res.GetPosition().GetSellLockUnit()))
	exchangeStatusRes, err := s.orderManagerClient.ExchangeStatus(ctx, &order.ExchangeStatusRequest{})
	if te := epifigrpc.RPCError(res, err); te != nil {
		return false, false, nil, nil, nil, errors.Wrap(te, "error getting exchange status")
	}
	return res.GetPosition().GetTotalQty() > 0, hasSellUnlockedShares, getInvestmentDisplayEntries(res.GetPosition()), getInvestmentSummary(res.GetPosition(), exchangeStatusRes.GetExtendedTradingHours()), res.GetPosition(), nil
}

// nolint:funlen
func (s *Service) getCTAs(ctx context.Context, actorId, stockId string, isOnboardedUser, isInvestedUser, hasSellUnlockedShares,
	isOnboardingInProgress, hasPendingSellOrders bool, stockStatus usstocksCatalogPb.InternalStatus, isFractionable bool) ([]*deeplink.Cta, error) {
	featureAllowed, err := utils.IsUSStocksEnabled(ctx, actorId, s.usersClient, s.userGrpClient, s.releaseEvaluator)
	if err != nil {
		return nil, errors.Wrap(err, "error in checking us stocks enabled for user")
	}
	if !featureAllowed {
		return []*deeplink.Cta{{
			Type:     deeplink.Cta_CUSTOM,
			Text:     "Join Waitlist",
			Deeplink: &deeplink.Deeplink{Screen: deeplink.Screen_USSTOCKS_LANDING_SCREEN}},
		}, nil
	}
	if !isOnboardedUser && !isOnboardingInProgress {
		createAccountCta, err2 := s.getNewUserDeeplink(ctx, actorId)
		if err2 != nil {
			return nil, err2
		}
		return []*deeplink.Cta{createAccountCta}, nil
	}
	if isOnboardingInProgress {
		return []*deeplink.Cta{
			{
				Type:     deeplink.Cta_CUSTOM,
				Text:     "Complete account setup",
				Deeplink: deeplinks.GetOnBoardingSetupScreen(),
			},
		}, nil
	}
	sellCTA, err := s.getSellCta(ctx, actorId, stockId, isInvestedUser, hasSellUnlockedShares, hasPendingSellOrders)
	if err != nil {
		return nil, errors.Wrap(err, "error getting sell cta")
	}
	// Note: Even if the user has no buying power, we redirect them to the buy screen
	// where they are asked to add funds.
	buyDeeplink, rightCTA, err := s.getBuyDeeplink(ctx, actorId, stockId, sellCTA, isFractionable)
	if err != nil {
		return nil, errors.Wrap(err, "error getting buy deeplink")
	}
	var ctas = []*deeplink.Cta{getCTAForBuyDeeplink(isInvestedUser, stockStatus, buyDeeplink)}
	if rightCTA != nil {
		ctas = append(ctas, rightCTA)
	}
	return ctas, nil
}

func (s *Service) getNewUserDeeplink(ctx context.Context, actorId string) (*deeplink.Cta, error) {
	// checking if user is a fi lite user, returning `create savings account` deeplink
	featureDetailsRes, err := s.onboardingClient.GetFeatureDetails(ctx, &onboarding.GetFeatureDetailsRequest{
		ActorId: actorId,
		Feature: onboarding.Feature_FEATURE_SA,
	})
	if err = epifigrpc.RPCError(featureDetailsRes, err); err != nil {
		return nil, errors.Wrap(err, "error getting feature details to check if Fi Lite user")
	}

	// if the user is a fi lite user, returning SA benefits screen
	if featureDetailsRes.GetIsFiLiteUser() {
		createAccountDeeplink, deeplinkErr := pkgOnboarding.GetSABenefitsScreen(ctx)
		if deeplinkErr != nil {
			return nil, errors.Wrap(deeplinkErr, "error getting SD benefits screen")
		}
		return &deeplink.Cta{
			Type:     deeplink.Cta_CUSTOM,
			Text:     "Set up account to buy",
			Deeplink: createAccountDeeplink,
		}, nil
	}
	return &deeplink.Cta{
		Type:     deeplink.Cta_CUSTOM,
		Text:     "Set up account to buy",
		Deeplink: deeplinks.GetOnBoardingSetupScreen(),
	}, nil
}

func (s *Service) getSellCta(ctx context.Context, actorId, stockId string,
	isInvestedUser, hasSellUnlockedShares, hasPendingSellOrders bool) (*deeplink.Cta, error) {
	limitOrderSupported, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(typesPb.Feature_FEATURE_US_STOCKS_LIMIT_ORDER).WithActorId(actorId))
	if err != nil {
		return nil, errors.Wrap(err, "error evaluating Limit order feature")
	}
	var sellDeeplink *deeplink.Deeplink
	if limitOrderSupported {
		sellDeeplink = &deeplink.Deeplink{
			Screen: deeplink.Screen_US_STOCKS_TRADE_DETAILS_SCREEN,
			ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&screen_options_v2.USSTradeDetailsScreenOptions{
				StockId:   stockId,
				OrderSide: usstocksFePb.OrderSide_ORDER_SIDE_SELL,
			}),
		}
	} else {
		sellDeeplink = &deeplink.Deeplink{
			Screen: deeplink.Screen_USSTOCKS_SELL_SCREEN,
			ScreenOptions: &deeplink.Deeplink_UsstocksSellOrderScreenOptions{
				UsstocksSellOrderScreenOptions: &deeplink.USStocksSellOrderScreenOptions{StockId: stockId},
			},
		}
	}

	if hasPendingSellOrders {
		return &deeplink.Cta{
			Type:         deeplink.Cta_CUSTOM,
			Text:         "Sell",
			Deeplink:     sellOrderInProgressDeeplink,
			DisplayTheme: deeplink.Cta_PRIMARY,
		}, nil
	}
	if hasSellUnlockedShares {
		if s.config.USStocks().IsSellDisabled() {
			return &deeplink.Cta{
				Type:         deeplink.Cta_CUSTOM,
				Text:         "Sell",
				Deeplink:     temporaryClosedForMaintenanceDeeplink,
				DisplayTheme: deeplink.Cta_PRIMARY,
			}, nil
		}
		isCreditFrozen, err := s.isCreditFrozen(ctx, actorId)
		if err != nil {
			return nil, errors.Wrap(err, "error checking if credit is frozen to savings account")
		}
		if isCreditFrozen {
			return &deeplink.Cta{
				Type:         deeplink.Cta_CUSTOM,
				Text:         "Sell",
				Deeplink:     creditFrozenDeeplink,
				DisplayTheme: deeplink.Cta_PRIMARY,
			}, nil
		}
		return &deeplink.Cta{
			Type:         deeplink.Cta_CUSTOM,
			Text:         "Sell",
			Deeplink:     sellDeeplink,
			DisplayTheme: deeplink.Cta_PRIMARY,
		}, nil
	}
	if isInvestedUser {
		return &deeplink.Cta{
			Type:         deeplink.Cta_CUSTOM,
			Text:         "Sell",
			Deeplink:     sellLockDeeplink,
			DisplayTheme: deeplink.Cta_PRIMARY,
			IconUrl:      "https://epifi-icons.pointz.in/usstocks_images/white_lock_icon.png",
		}, nil
	}
	return nil, nil
}

func (s *Service) getBuyDeeplink(ctx context.Context, actorId, stockId string, sellCTA *deeplink.Cta, isFractionable bool) (*deeplink.Deeplink, *deeplink.Cta, error) {
	if s.config.USStocks().IsBuyDisabled() {
		return temporaryClosedForMaintenanceDeeplink, sellCTA, nil
	}
	sipSupported, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(typesPb.Feature_FEATURE_US_STOCKS_SIP).WithActorId(actorId))
	if err != nil {
		return nil, nil, errors.Wrap(err, "error evaluating SIP feature")
	}
	limitOrderSupported, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(typesPb.Feature_FEATURE_US_STOCKS_LIMIT_ORDER).WithActorId(actorId))
	if err != nil {
		return nil, nil, errors.Wrap(err, "error evaluating Limit order feature")
	}
	var buyDeeplink *deeplink.Deeplink
	if limitOrderSupported {
		buyDeeplink = &deeplink.Deeplink{
			Screen: deeplink.Screen_US_STOCKS_TRADE_DETAILS_SCREEN,
			ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&screen_options_v2.USSTradeDetailsScreenOptions{
				StockId:   stockId,
				OrderSide: usstocksFePb.OrderSide_ORDER_SIDE_BUY,
			}),
		}
	} else {
		buyDeeplink = &deeplink.Deeplink{
			Screen: deeplink.Screen_USSTOCKS_BUY_SCREEN,
			ScreenOptions: &deeplink.Deeplink_UsstocksBuyOrderScreenOptions{
				UsstocksBuyOrderScreenOptions: &deeplink.USStocksBuyOrderScreenOptions{StockId: stockId},
			},
		}
	}

	// as we currently do not support SIP for non-fractional stocks due to sip in amount only
	// allowing sip only for fractional stocks
	if !sipSupported || !isFractionable {
		return buyDeeplink, sellCTA, nil
	}
	if sellCTA != nil {
		return getInvestmentMethodChoicesDeeplink(stockId, buyDeeplink), sellCTA, nil
	}
	rightSIPCTA := &deeplink.Cta{
		Type:         deeplink.Cta_CUSTOM,
		Text:         "Set up an SIP",
		Deeplink:     fittt.GetCustomiseUSStockSIPRuleDeeplink(stockId),
		DisplayTheme: deeplink.Cta_PRIMARY,
	}
	return buyDeeplink, rightSIPCTA, nil
}

func getCTAForBuyDeeplink(isInvestedUser bool,
	stockStatus usstocksCatalogPb.InternalStatus, buyDeeplink *deeplink.Deeplink) *deeplink.Cta {
	buyText := "Buy"
	if isInvestedUser {
		buyText = "Buy more"
	}
	enableBuyButton := deeplink.Cta_CTA_STATUS_ENABLED
	if stockStatus == usstocksCatalogPb.InternalStatus_INTERNAL_STATUS_DISABLED_BUY {
		enableBuyButton = deeplink.Cta_CTA_STATUS_DISABLED
	}
	return &deeplink.Cta{
		Type:         deeplink.Cta_CUSTOM,
		Text:         buyText,
		Deeplink:     buyDeeplink,
		DisplayTheme: deeplink.Cta_PRIMARY,
		Status:       enableBuyButton,
	}
}

func getInvestmentMethodChoicesDeeplink(stockId string, buyDeeplink *deeplink.Deeplink) *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_US_STOCKS_CHOOSE_INVESTMENT_METHOD_BOTTOM_SHEET,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&screen_options_v2.ChooseInvestmentMethodScreenOptions{
			Title: commontypes.GetTextFromStringFontColourFontStyle("Choose an option", "#333333", commontypes.FontStyle_SUBTITLE_2),
			InvestmentMethods: []*screen_options_v2.InvestmentMethod{
				{
					BgColor:  "#FFFFFF",
					LeftIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/usstocks_images/number-one-inside-circle.png"),
					TitleSubtitlePair: &ui.VerticalKeyValuePair{
						Title: &ui.IconTextComponent{
							Texts: []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Buy once", "#333333", commontypes.FontStyle_SUBTITLE_2)},
						},
						Value: &ui.IconTextComponent{
							Texts: []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Invest lump-sum", "#646464", commontypes.FontStyle_BODY_4)},
						},
						VerticalPaddingBtwTitleValue: 4,
						HAlignment:                   ui.VerticalKeyValuePairHAlignment_VERTICAL_KEY_VALUE_PAIR_H_ALIGNMENT_LEFT,
					},
					RightIcon: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/home/<USER>/right-arrow-vector.png", 28, 28),
					Deeplink:  buyDeeplink,
				},
				{
					BgColor:  "#FFFFFF",
					LeftIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/usstocks_images/month-flip-calendar-with-m.png"),
					TitleSubtitlePair: &ui.VerticalKeyValuePair{
						Title: &ui.IconTextComponent{
							Texts: []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Monthly SIP", "#333333", commontypes.FontStyle_SUBTITLE_2)},
						},
						Value: &ui.IconTextComponent{
							Texts: []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Auto-invest monthly", "#646464", commontypes.FontStyle_BODY_4)},
						},
						VerticalPaddingBtwTitleValue: 4,
						HAlignment:                   ui.VerticalKeyValuePairHAlignment_VERTICAL_KEY_VALUE_PAIR_H_ALIGNMENT_LEFT,
					},
					RightIcon: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/home/<USER>/right-arrow-vector.png", 28, 28),
					Deeplink:  fittt.GetCustomiseUSStockSIPRuleDeeplink(stockId),
				},
			},
		}),
	}
}

func getInvestmentSummary(position *usstocksPortfolioPb.Position, eth *order.ExtendedTradingHours) *usstocksFePb.StockInvestmentSummary {
	return &usstocksFePb.StockInvestmentSummary{
		CurrentMarketValue: &usstocksFePb.LabeledConvertibleAmount{
			Label: usstocksUi.GetText("INVESTMENT VALUE", usstocksUi.Steel, commontypes.FontStyle_SUBTITLE_2),
			ConvertibleAmount: &usstocksFePb.ConvertibleAmount{
				AmountInUsd: usstocksUi.GetText(money.ToDisplayStringWithPrecisionV2(position.GetMarketValue(), 2), usstocksUi.Night, commontypes.FontStyle_NUMBER_XL),
				AmountInInr: nil,
			},
			InfoPopupOptions: getPortfolioInconsistencyInfo(eth, position.GetLastRefreshedAt()),
		},
		InvestedValue: &usstocksFePb.LabeledConvertibleAmount{
			Label: usstocksUi.GetText("INVESTED", usstocksUi.Steel, commontypes.FontStyle_SUBTITLE_2),
			ConvertibleAmount: &usstocksFePb.ConvertibleAmount{
				AmountInUsd: usstocksUi.GetText(money.ToDisplayStringWithPrecisionV2(position.GetInvestedAmount(), 2), usstocksUi.Night, commontypes.FontStyle_NUMBER_M),
				AmountInInr: nil,
			},
		},
		Returns: &usstocksFePb.LabeledConvertibleAmount{
			Label: usstocksUi.GetText("RETURNS", usstocksUi.Steel, commontypes.FontStyle_SUBTITLE_2),
			ConvertibleAmount: &usstocksFePb.ConvertibleAmount{
				AmountInUsd: usstocksUi.GetText(money.ToDisplayStringWithPrecisionV2(position.GetPlAmount(), 2), usstocksUi.Night, commontypes.FontStyle_NUMBER_M),
				AmountInInr: nil,
			},
		},
		// TODO(Brijesh): Discuss with clients and change misleading field name
		DailyChange: getInvestedAmountPercentageChange(position.GetPlPercentage()),
		// TODO(Brijesh): Fix calculation
		AvgPrice: &usstocksFePb.LabeledConvertibleAmount{
			Label: usstocksUi.GetText("AVG. PRICE", usstocksUi.Steel, commontypes.FontStyle_SUBTITLE_2),
			ConvertibleAmount: &usstocksFePb.ConvertibleAmount{
				AmountInUsd: usstocksUi.GetText(money.ToDisplayStringWithPrecisionV2(position.GetAvgPrice(), 2), usstocksUi.Night, commontypes.FontStyle_NUMBER_M),
				AmountInInr: nil,
			},
		},
	}
}

func (s *Service) getStockWatchlistDetails(ctx context.Context, actorId, stockId string) (*usstocksFePb.StockWatchlistDetails, error) {
	watchlistStockMappings, err := getUserWatchlists(ctx, s.catalogManagerClient, actorId)
	if err != nil {
		return nil, fmt.Errorf("error getting user watchlists: %w", err)
	}
	watchlistStockMappingsMap := utils.GetWatchlistStockMappingsMap(watchlistStockMappings)
	watchlistStockMappingsOfStocks, _ := watchlistStockMappingsMap[stockId]
	stockWatchlistDetails := &usstocksFePb.StockWatchlistDetails{
		WatchlistedIconUrl:    "https://epifi-icons.pointz.in/usstocks_images/bookmark-filled-inverse.png",
		NotWatchlistedIconUrl: "https://epifi-icons.pointz.in/usstocks_images/bookmark-outline-inverse.png",
	}
	if len(watchlistStockMappingsOfStocks) > 0 {
		stockWatchlistDetails.IsWatchlisted = true
	}
	return stockWatchlistDetails, nil
}

func getInvestmentDisplayEntries(position *usstocksPortfolioPb.Position) []*usstocksFePb.DisplayEntry {
	return []*usstocksFePb.DisplayEntry{
		{
			TitleText: usstocksUi.GetText("INVESTMENT VALUE", usstocksUi.Steel, commontypes.FontStyle_SUBTITLE_2),
			ValueText: usstocksUi.GetText(money.ToDisplayStringWithPrecisionV2(position.GetMarketValue(), 2), usstocksUi.Night, commontypes.FontStyle_NUMBER_XL),
		},
		{
			TitleText: usstocksUi.GetText("INVESTED", usstocksUi.Steel, commontypes.FontStyle_SUBTITLE_2),
			ValueText: usstocksUi.GetText(money.ToDisplayStringWithPrecisionV2(position.GetInvestedAmount(), 2), usstocksUi.Night, commontypes.FontStyle_NUMBER_M),
		},
		{
			TitleText: usstocksUi.GetText("RETURNS", usstocksUi.Steel, commontypes.FontStyle_SUBTITLE_2),
			ValueText: usstocksUi.GetText(fmt.Sprintf("%.2f%%", position.GetPlPercentage()), usstocksUi.Night, commontypes.FontStyle_NUMBER_M),
		},
		{
			TitleText: usstocksUi.GetText("AVG. PRICE", usstocksUi.Steel, commontypes.FontStyle_SUBTITLE_2),
			ValueText: usstocksUi.GetText(money.ToDisplayStringWithPrecisionV2(position.GetAvgPrice(), 2), usstocksUi.Night, commontypes.FontStyle_NUMBER_M),
		},
	}
}

func getInvestedAmountPercentageChange(percentageChange float64) *ui.IconTextComponent {
	textColor := usstocksUi.DarkMint
	iconUrl := "https://epifi-icons.pointz.in/usstocks_images/up-icon.png"
	if percentageChange < 0 {
		textColor = usstocksUi.DarkPeach
		iconUrl = "https://epifi-icons.pointz.in/usstocks_images/down-icon.png"
	}
	return &ui.IconTextComponent{
		LeftIcon:          &commontypes.Image{ImageType: commontypes.ImageType_PNG, ImageUrl: iconUrl, Width: 6, Height: 18},
		Texts:             []*commontypes.Text{usstocksUi.GetText(fmt.Sprintf("%.2f%%", percentageChange), textColor, commontypes.FontStyle_NUMBER_S)},
		LeftImgTxtPadding: 2,
	}
}

// nolint: funlen
func getStockPricesForTimePeriods(
	stockPricesForPeriods []*usstocksCatalogPb.StockPricesForPeriod,
	devicePlatform commontypes.Platform,
	marketStatus usstocks.MarketStatus,
) ([]*usstocksFePb.StockPricesForTimePeriod, error) {
	var stockPricesForTimePeriods []*usstocksFePb.StockPricesForTimePeriod
	for _, pricesForPeriod := range stockPricesForPeriods {
		if len(pricesForPeriod.GetBars()) == 0 {
			return nil, fmt.Errorf("no data points found for period: %s", pricesForPeriod.GetPeriod())
		}
		sort.SliceStable(pricesForPeriod.GetBars(), func(i, j int) bool {
			return pricesForPeriod.GetBars()[i].GetTs().AsTime().Before(pricesForPeriod.GetBars()[j].GetTs().AsTime())
		})
		var (
			priceDataPoints                            []*usstocksFePb.TimeSeriesLineChartDataPoint
			minClosePrice, maxClosePrice, highestPrice float64
			lowestPrice                                = math.MaxFloat64
		)
		for _, bar := range pricesForPeriod.GetBars() {
			priceDataPoints = append(priceDataPoints, &usstocksFePb.TimeSeriesLineChartDataPoint{
				Timestamp: bar.GetTs(),
				Value:     bar.GetClosePrice(),
			})
			minClosePrice = math.Min(minClosePrice, bar.GetClosePrice())
			maxClosePrice = math.Max(maxClosePrice, bar.GetClosePrice())
			highestPrice = math.Max(highestPrice, bar.GetHighPrice())
			lowestPrice = math.Min(lowestPrice, bar.GetLowPrice())
		}
		chartPropInit := getChartInitObj(pricesForPeriod, devicePlatform)
		initChartCmd, err := chartPropInit.GetJsInitChartCommand()
		if err != nil {
			return nil, fmt.Errorf("error getting js chart init cmd: %w", err)
		}
		setChartDataCmd, err := chartPropInit.GetJsInitDataCommand()
		if err != nil {
			return nil, fmt.Errorf("error getting js chart data cmd: %w", err)
		}
		closePriceVal := fmt.Sprintf("%.2f", pricesForPeriod.GetBars()[len(pricesForPeriod.GetBars())-1].GetClosePrice())
		if marketStatus == usstocks.MarketStatus_MARKET_STATUS_OPEN {
			closePriceVal = "--"
		}
		feStockPricesForTimePeriod := &usstocksFePb.StockPricesForTimePeriod{
			PeriodTextForTabs:    usstocksUi.GetText(pricesForPeriod.GetPeriod(), "#F5F5F5", commontypes.FontStyle_SUBTITLE_3),
			PeriodTextForReturns: usstocksUi.GetText(pricesForPeriod.GetPeriod(), "#8D8D8D", commontypes.FontStyle_SUBTITLE_2),
			PriceChangeInPeriod:  usstocksUi.GetText(fmt.Sprintf("%.2f%%", pricesForPeriod.GetPercentagePriceChange()), getPriceChangeColor(pricesForPeriod.GetPercentagePriceChange()), commontypes.FontStyle_SUBTITLE_2),
			// Note: Start and end times are sent in a format which is parseable by Chrome and Safari that JavaScript's implementation of JavaScript Date.parse() function
			// Ideally devices should forward MinXVal and MaxXVal to web service wherever timestamp is needed instead of parsing string to date
			// TODO(Brijesh): Ask devices and web to plan to move away from this hack
			StartTime: usstocksUi.GetText(pricesForPeriod.GetPeriodStartTs().AsTime().In(datetime.IST).Format("2006-01-02T15:04"), "#8D8D8D", commontypes.FontStyle_MICRO_1),
			EndTime:   usstocksUi.GetText(pricesForPeriod.GetPeriodEndTs().AsTime().In(datetime.IST).Format("2006-01-02T15:04"), "#8D8D8D", commontypes.FontStyle_MICRO_1),
			StockPriceChart: &usstocksFePb.StockPriceChart{
				SeriesColor:           getPriceChangeColor(pricesForPeriod.GetPercentagePriceChange()),
				MinXVal:               pricesForPeriod.GetPeriodStartTs(),
				MaxXVal:               pricesForPeriod.GetPeriodEndTs(),
				MinYVal:               minClosePrice * 0.9,
				MaxYVal:               maxClosePrice * 1.1,
				StockPriceDataPoints:  priceDataPoints,
				InitChartJsCommand:    initChartCmd,
				SetChartDataJsCommand: setChartDataCmd,
			},
			ShouldUpdateRealTime: pricesForPeriod.GetShouldUpdateRealTime(),
			PercentageReturns:    getPercentagePriceChange(pricesForPeriod.GetPercentagePriceChange()),
			OhlcComponent: &usstocksFePb.OHLCComponent{
				OpenPrice:   getOhlcKVPair("OPEN", fmt.Sprintf("%.2f", pricesForPeriod.GetBars()[0].GetOpenPrice()), ui.VerticalKeyValuePairHAlignment_VERTICAL_KEY_VALUE_PAIR_H_ALIGNMENT_LEFT),
				LowPrice:    getOhlcKVPair("LOW", fmt.Sprintf("%.2f", lowestPrice), ui.VerticalKeyValuePairHAlignment_VERTICAL_KEY_VALUE_PAIR_H_ALIGNMENT_CENTER),
				HighPrice:   getOhlcKVPair("HIGH", fmt.Sprintf("%.2f", highestPrice), ui.VerticalKeyValuePairHAlignment_VERTICAL_KEY_VALUE_PAIR_H_ALIGNMENT_CENTER),
				ClosedPrice: getOhlcKVPair("CLOSE", closePriceVal, ui.VerticalKeyValuePairHAlignment_VERTICAL_KEY_VALUE_PAIR_H_ALIGNMENT_RIGHT),
			},
		}
		stockPricesForTimePeriods = append(stockPricesForTimePeriods, feStockPricesForTimePeriod)
	}
	return stockPricesForTimePeriods, nil
}

// red if negative change, green otherwise
func getPriceChangeColor(change float64) string {
	textColor := usstocksUi.DarkMint
	if change < 0 {
		textColor = usstocksUi.DarkPeach
	}
	return textColor
}

func getPercentagePriceChange(percentageChange float64) *ui.IconTextComponent {
	growthStr, textColor, iconUrl := utils.GetPriceChangeTextColorIconURL(percentageChange)
	return &ui.IconTextComponent{
		LeftIcon: &commontypes.Image{
			ImageType: commontypes.ImageType_PNG,
			ImageUrl:  iconUrl,
			Width:     16,
			Height:    16,
		},
		Texts: []*commontypes.Text{
			usstocksUi.GetText(growthStr, textColor, commontypes.FontStyle_SUBTITLE_2),
		},
		LeftImgTxtPadding: 2,
	}
}

func getChartInitObj(pricesForPeriod *usstocksCatalogPb.StockPricesForPeriod, devicePlatform commontypes.Platform) *chartPb.InitialProps {
	var chartDatapoints []*chartPb.DataPoint
	for _, datapoint := range pricesForPeriod.GetBars() {
		chartDatapoints = append(chartDatapoints, &chartPb.DataPoint{
			Timestamp: datapoint.GetTs(),
			Value:     datapoint.GetClosePrice(),
			Label:     USStockPriceChartLabel,
		})
	}
	return &chartPb.InitialProps{
		StartTime:            pricesForPeriod.GetPeriodStartTs(),
		EndTime:              pricesForPeriod.GetPeriodEndTs(),
		ChartLineColor:       getPriceChangeColor(pricesForPeriod.GetPercentagePriceChange()),
		ChartBgColor:         "#262728",
		Platform:             devicePlatform,
		TimePeriod:           pricesForPeriod.GetPeriod(),
		ShouldUpdateRealTime: pricesForPeriod.GetShouldUpdateRealTime(),
		DataPoints:           chartDatapoints,
	}
}

func getUserWatchlists(ctx context.Context, catalogManagerClient usstocksCatalogPb.CatalogManagerClient, actorId string) ([]*usstocksCatalogPb.WatchlistStockMapping, error) {
	// TODO(mounish): add stock id param to GetWatchlistStockMappingsByActorId RPC
	res, err := catalogManagerClient.GetWatchlistStockMappingsByActorId(ctx,
		&usstocksCatalogPb.GetWatchlistStockMappingsByActorIdRequest{
			ActorId: actorId,
		})
	if te := epifigrpc.RPCError(res, err); te != nil {
		return nil, fmt.Errorf("error while calling GetWatchlistStockMappingsByActorId RPC: %w", te)
	}
	return res.GetWatchlistStockMappings(), nil
}

func (s *Service) getSymbolDetailsTabs(platform commontypes.Platform, version, numActivities, inProgressOrderCount int, sipsPresent bool) ([]*usstocksFePb.SymbolDetailsTab, error) {
	switch platform {
	case commontypes.Platform_ANDROID:
		if version < int(s.config.USStocks().VersionSupport().MinAndroidAppVersionToSupportHiddenActivityTab()) {
			return getSymbolDetailsTabsV1(inProgressOrderCount), nil
		}
		return getSymbolDetailsTabsV2(numActivities, inProgressOrderCount, sipsPresent), nil
	case commontypes.Platform_IOS:
		return getSymbolDetailsTabsV2(numActivities, inProgressOrderCount, sipsPresent), nil
	default:
		return nil, errors.Errorf("unsupported platform: %s", platform.String())
	}
}

func getSymbolDetailsTabsV1(inProgressOrderCount int) []*usstocksFePb.SymbolDetailsTab {
	keyFactsTab := &usstocksFePb.SymbolDetailsTab{
		DisplayName: "Key facts",
		IsSelected:  true,
		TabType:     usstocksEnumFePb.SymbolDetailsTabType_SYMBOL_DETAILS_TAB_TYPE_KEY_FACTS,
		IconUrl:     "https://epifi-icons.pointz.in/usstocks_images/bars-trending-up-icon.png",
	}
	var activityBadge *commontypes.Text
	if inProgressOrderCount != 0 {
		activityBadge = getFeText(fmt.Sprintf("%d", inProgressOrderCount), "#AC7C44", commontypes.FontStyle_SUBTITLE_XS, false, "#F4E7BF")
	}
	myActivityTab := &usstocksFePb.SymbolDetailsTab{
		DisplayName:   "My activity",
		TabType:       usstocksEnumFePb.SymbolDetailsTabType_SYMBOL_DETAILS_TAB_TYPE_ACTIVITY,
		IconUrl:       "https://epifi-icons.pointz.in/usstocks_images/text-file-icon.png",
		ActivityBadge: activityBadge,
	}
	return []*usstocksFePb.SymbolDetailsTab{keyFactsTab, myActivityTab}
}

func getSymbolDetailsTabsV2(numActivities, inProgressOrderCount int, sipsPresent bool) []*usstocksFePb.SymbolDetailsTab {
	keyFactsTab := &usstocksFePb.SymbolDetailsTab{
		DisplayName: "Key facts",
		IsSelected:  true,
		TabType:     usstocksEnumFePb.SymbolDetailsTabType_SYMBOL_DETAILS_TAB_TYPE_KEY_FACTS,
		IconUrl:     "https://epifi-icons.pointz.in/usstocks_images/bars-trending-up-icon.png",
	}
	if numActivities == 0 && !sipsPresent {
		return []*usstocksFePb.SymbolDetailsTab{keyFactsTab}
	}
	var activityBadge *commontypes.Text
	if inProgressOrderCount != 0 {
		activityBadge = getFeText(fmt.Sprintf("%d", inProgressOrderCount), "#AC7C44", commontypes.FontStyle_SUBTITLE_XS, false, "#F4E7BF")
	}
	myActivityTab := &usstocksFePb.SymbolDetailsTab{
		DisplayName:   "My activity",
		TabType:       usstocksEnumFePb.SymbolDetailsTabType_SYMBOL_DETAILS_TAB_TYPE_ACTIVITY,
		IconUrl:       "https://epifi-icons.pointz.in/usstocks_images/text-file-icon.png",
		ActivityBadge: activityBadge,
	}
	return []*usstocksFePb.SymbolDetailsTab{keyFactsTab, myActivityTab}
}

func getOhlcKVPair(key, value string, horizontalAlignment ui.VerticalKeyValuePairHAlignment) *ui.VerticalKeyValuePair {
	return &ui.VerticalKeyValuePair{
		Title: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					DisplayValue: &commontypes.Text_PlainString{PlainString: key},
					FontColor:    "#8D8D8D",
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS},
				},
			},
		},
		Value: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					DisplayValue: &commontypes.Text_PlainString{PlainString: value},
					FontColor:    "#B9B9B9",
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS},
				},
			},
		},
		VerticalPaddingBtwTitleValue: 4,
		HAlignment:                   horizontalAlignment,
	}
}

func getFESymbolDetails(stock *usstocksCatalogPb.Stock, growthFontStyle commontypes.FontStyle,
	symbolNameColor string, symbolNameFontStyle commontypes.FontStyle) *usstocksFePb.SymbolDetails {
	var growthColor string
	var growthStr string
	if stock.GetDailyPerformance().GetDailyPercentChange() < 0 {
		growthColor = usstocksUi.DarkPeach
		growthStr = fmt.Sprintf("%.2f%%", stock.GetDailyPerformance().GetDailyPercentChange())
	} else {
		growthColor = usstocksUi.DarkMint
		growthStr = fmt.Sprintf("+%.2f%%", stock.GetDailyPerformance().GetDailyPercentChange())
	}
	growthText := getTextForString(growthStr, growthColor, growthFontStyle, false)
	return &usstocksFePb.SymbolDetails{
		StockLogoUrl:   stock.GetStockBasicDetails().GetLogoUrl(),
		SymbolNameText: getTextForString(stock.GetStockBasicDetails().GetName().GetShortName(), symbolNameColor, symbolNameFontStyle, false),
		StockPrice:     types.GetFromBeMoney(stock.GetDailyPerformance().GetClose()),
		StockPriceText: getTextForString(money.ToDisplayString(stock.GetDailyPerformance().GetClose()), "#F5F5F5", commontypes.FontStyle_SUBTITLE_2, false),
		GrowthText:     growthText,
		StockSymbol:    stock.GetSymbol(),
		SymbolDetailsScreen: &deeplink.Deeplink{
			Screen: deeplink.Screen_USSTOCKS_SYMBOL_DETAILS_SCREEN,
			ScreenOptions: &deeplink.Deeplink_UsstocksSymbolDetailsScreenOptions{
				UsstocksSymbolDetailsScreenOptions: &deeplink.USStocksSymbolDetailsScreenOptions{StockId: stock.GetId()},
			},
		},
		StockId:              stock.GetId(),
		ShouldUpdateRealTime: true,
	}
}
