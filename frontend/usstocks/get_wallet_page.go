package usstocks

import (
	"context"
	"fmt"
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	types "github.com/epifi/gamma/api/typesv2"
	usstocksCatalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	ussOrder "github.com/epifi/gamma/api/usstocks/order"
	"github.com/epifi/gamma/frontend/usstocks/activity"
	"github.com/epifi/gamma/pkg/feature/release"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"github.com/epifi/be-common/pkg/colors"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	widgetUi "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	moneyPb "github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	"github.com/epifi/gamma/api/frontend/usstocks"
	iftPb "github.com/epifi/gamma/api/pay/internationalfundtransfer"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/frontend"
	ussScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/usstocks"
	"github.com/epifi/gamma/api/typesv2/ui"
	ussBe "github.com/epifi/gamma/api/usstocks"
	"github.com/epifi/gamma/api/usstocks/account"
	usstocksUi "github.com/epifi/gamma/frontend/usstocks/ui"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/usstocks/deeplinks"
	"github.com/epifi/gamma/pkg/usstocks/utils"
)

var (
	walletAccountActivityFilter = func() *usstocks.AccountActivityFilters {
		return &usstocks.AccountActivityFilters{
			ActivityTypes: []usstocks.AccountActivityType{
				usstocks.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_WALLET_ADD_FUNDS,
				usstocks.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_WALLET_WITHDRAW_FUNDS,
				usstocks.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_STOCK_BUY,
				usstocks.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_STOCK_SELL,
				usstocks.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_DIVIDEND,
				usstocks.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_STOCK_BUY_FOR_REWARD,
				usstocks.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_WALLET_ADD_FUNDS_FOR_SIP,
				usstocks.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_STOCK_BUY_FOR_SIP,
			},
		}
	}

	// nolint: dupl
	withdrawOrderInProgressDeeplink = &deeplink.Deeplink{
		Screen: deeplink.Screen_USSTOCKS_BUY_SELL_CLOSED_SCREEN,
		ScreenOptions: &deeplink.Deeplink_UsstocksBuySellClosedScreenOptions{
			UsstocksBuySellClosedScreenOptions: &deeplink.USStocksBuySellClosedScreenOptions{
				ImageTitleSubtitle: &widgetUi.ImageTitleSubtitleElement{
					IconImage: &commontypes.Image{
						ImageType: commontypes.ImageType_PNG,
						ImageUrl:  "https://epifi-icons.pointz.in/usstocks_images/Group+*********.png",
						Width:     80,
						Height:    80,
					},
					TitleText: usstocksUi.GetText("You already have a withdraw order in process", usstocksUi.Night,
						commontypes.FontStyle_SUBTITLE_1),
					SubtitleText: usstocksUi.GetText("Please wait a moment before placing another withdraw request. We're still processing your last withdraw request.", usstocksUi.Night,
						commontypes.FontStyle_BODY_S),
				},
				Cta: &deeplink.Cta{
					Type:         deeplink.Cta_DONE,
					Text:         "Ok, got it",
					DisplayTheme: deeplink.Cta_PRIMARY,
				},
			},
		},
	}
)

func (s *Service) GetWalletPage(ctx context.Context, req *usstocks.GetWalletPageRequest) (res *usstocks.GetWalletPageResponse, err error) {
	tradingAccountRes, err := s.accountManagerClient.GetTradingAccountDetails(ctx, &account.GetTradingAccountDetailsRequest{
		Identifier: &account.TradingAccountIdentifier{
			Identifier: &account.TradingAccountIdentifier_ActorId{
				ActorId: req.GetReq().GetAuth().GetActorId(),
			},
		},
		Strategy: account.GetTradingAccountDetailsRequest_BEST_EFFORT,
	})
	if rpcErr := epifigrpc.RPCError(tradingAccountRes, err); rpcErr != nil && !tradingAccountRes.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error while fetching user's trading account details", zap.Error(rpcErr))
		return &usstocks.GetWalletPageResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(rpcErr.Error())}}, nil
	}
	if tradingAccountRes.GetStatus().IsRecordNotFound() {
		// return zero state
		return &usstocks.GetWalletPageResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()}}, nil
	}
	forexRes, err := s.internationalFundTransferClient.GetForexRate(ctx, &iftPb.GetForexRateRequest{
		Vendor:         commonvgpb.Vendor_FEDERAL_BANK,
		CurrencyCode:   moneyPb.USDCurrencyCode,
		RemittanceType: iftPb.ForexRateRemittanceType_FOREX_RATE_REMITTANCE_TYPE_INWARD,
	})
	if rpcErr := epifigrpc.RPCError(forexRes, err); rpcErr != nil {
		logger.Error(ctx, "error while fetching active inward forex rate details", zap.Error(rpcErr))
		return &usstocks.GetWalletPageResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(rpcErr.Error())}}, nil
	}

	activities, pageCtx, err := s.getActivities(ctx, req.GetReq().GetAuth().GetActorId())
	if err != nil {
		logger.Error(ctx, "error while fetching past account activities", zap.Error(err))
		return &usstocks.GetWalletPageResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(err.Error())}}, nil
	}
	showZeroState := len(activities) == 0
	walletSection, err := s.createWalletSection(ctx, tradingAccountRes.GetTradingAccount().GetWalletDetails(),
		req.GetReq().GetAuth().GetActorId())
	if err != nil {
		logger.Error(ctx, "error in creating wallet section", zap.Error(err))
		return &usstocks.GetWalletPageResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(err.Error())}}, nil
	}
	bottomSection, err := s.createBottomSection(ctx, req.GetReq().GetAuth().GetActorId(), showZeroState, activities, pageCtx)
	logger.Info(ctx, fmt.Sprintf("wallet bottom page %v", bottomSection))
	if err != nil {
		logger.Error(ctx, "error while generating bottom section for wallet page", zap.Error(err))
		return &usstocks.GetWalletPageResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(err.Error())}}, nil
	}
	return &usstocks.GetWalletPageResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		WalletPage: s.createWalletPage(ctx, walletSection, bottomSection),
	}, nil
}

func (s *Service) createWalletPage(_ context.Context, walletSection *usstocks.Wallet,
	bottomSection *usstocks.WalletPageBottomSection) *usstocks.WalletPage {
	return &usstocks.WalletPage{
		PageTitle: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: "Your USD Wallet",
					},
				},
			},
		},
		Wallet:        walletSection,
		BottomSection: bottomSection,
	}
}

// nolint:funlen
func (s *Service) createWalletSection(ctx context.Context, wallet *account.WalletDetails,
	actorId string) (*usstocks.Wallet, error) {
	balance := wallet.GetBuyingPower()
	var (
		balanceDisplay                 *ui.VerticalKeyValuePair
		addCta, withdrawCta, footerTxt *ui.IconTextComponent
	)
	if !moneyPb.IsZero(balance) {
		// this api is needed only for non-zero wallet balance users, since adding this api call in this check
		walletOrderRes, walletErr := s.orderManagerClient.GetWalletOrders(ctx, &ussOrder.GetWalletOrdersRequest{
			ActorId:   actorId,
			OrderType: []ussBe.WalletOrderType{ussBe.WalletOrderType_WALLET_ORDER_TYPE_WITHDRAW_FUNDS},
			PageContext: &rpc.PageContextRequest{
				PageSize: 1,
			},
		})
		if err := epifigrpc.RPCError(walletOrderRes, walletErr); err != nil {
			logger.Error(ctx, "error while getting latest wallet withdraw order for actor", zap.Error(err))
			return nil, fmt.Errorf("error while getting latest wallet withdraw order for actor: %w", err)
		}
		dollars, cents := moneyPb.GetDisplayStringWithValueAndPrecision(balance, 2, false, false, moneyPb.InternationalNumberSystem)
		withdrawableDollars, withdrawableCents := moneyPb.GetDisplayStringWithValueAndPrecision(balance, 2, false, false, moneyPb.InternationalNumberSystem)
		balanceDisplay = &ui.VerticalKeyValuePair{
			Title: ui.NewITC().WithTexts(
				commontypes.GetPlainStringText("$").WithFontStyle(commontypes.FontStyle_NUMBER_XL).WithFontColor(usstocksUi.Onyx),
				commontypes.GetPlainStringText(dollars).WithFontStyle(commontypes.FontStyle_NUMBER_3XL).WithFontColor(usstocksUi.Snow),
				commontypes.GetPlainStringText(cents).WithFontStyle(commontypes.FontStyle_NUMBER_XL).WithFontColor(usstocksUi.Snow),
			),
			HAlignment: ui.VerticalKeyValuePairHAlignment_VERTICAL_KEY_VALUE_PAIR_H_ALIGNMENT_CENTER,
		}
		cmp, err := moneyPb.CompareV2(wallet.GetBuyingPower(), wallet.GetCash())
		if err != nil {
			return nil, fmt.Errorf("failed to compare buying power with cash : %w", err)
		}
		// if buyingPower > cash (i.e. user sold some stocks and the sell is not yet fulfilled/rejected the buying power increases
		// but the amount the user can withdraw from wallet has not)
		// show the info popup. figma: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=27349-10394&mode=design&t=uyNaog1SDfyadxwQ-4
		if cmp == 1 {
			balanceDisplay.VerticalPaddingBtwTitleValue = 8
			balanceDisplay.Value = ui.NewITC().WithTexts(
				commontypes.GetPlainStringText("Available to withdraw ").WithFontStyle(commontypes.FontStyle_SUBTITLE_S).WithFontColor(colors.ColorOnDarkMediumEmphasis),
				commontypes.GetPlainStringText("$").WithFontStyle(commontypes.FontStyle_SUBTITLE_M).WithFontColor(colors.ColorSnow),
				commontypes.GetPlainStringText(withdrawableDollars).WithFontStyle(commontypes.FontStyle_SUBTITLE_M).WithFontColor(colors.ColorSnow),
				commontypes.GetPlainStringText(withdrawableCents).WithFontStyle(commontypes.FontStyle_SUBTITLE_M).WithFontColor(colors.ColorSnow),
				commontypes.GetPlainStringText(" ").WithFontStyle(commontypes.FontStyle_SUBTITLE_M).WithFontColor(colors.ColorSnow),
			).WithRightVisualElement(commontypes.GetVisualElementImageFromUrl(greenInfoLogoUrl).WithProperties(&commontypes.VisualElementProperties{
				Width:  20,
				Height: 20,
			})).WithDeeplink(getWithdrawInfoPopup())
		}
		withdrawCta = ui.NewITC().WithTexts(
			commontypes.GetPlainStringText(WalletWithdrawFunds).WithFontStyle(commontypes.FontStyle_HEADLINE_3).WithFontColor(usstocksUi.Forest),
		).WithContainer(
			0, 0, 19, usstocksUi.Charcoal,
		).WithContainerPadding(
			20, 24, 20, 24,
		).WithDeeplink(WalletWithdrawFundsPageDeeplink(wallet))

		// hotfix: this is to avoid multiple withdraw request by user till his first request is successful from vendor
		// usually takes around 10 sec for amount transfer completion from user's wallet to inward pool account
		// check for latest withdraw order, if latest withdraw order is within past 1-minute block user from withdraw funds, and show corresponding bottom sheet explaining this
		if len(walletOrderRes.GetOrders()) > 0 {
			order := walletOrderRes.GetOrders()[0]
			isOrderDurationBeforeThreshold := order.GetCreatedAt().AsTime().Before(time.Now().Add(-s.config.USStocks().WithdrawFundsBlockedDuration()))
			if order.GetStatus() == ussBe.WalletOrderStatus_WALLET_ORDER_STATUS_CREATED && !isOrderDurationBeforeThreshold {
				withdrawCta.Deeplink = withdrawOrderInProgressDeeplink
			}
		}
	} else {
		balanceDisplay = &ui.VerticalKeyValuePair{
			Title: ui.NewITC().WithTexts(
				commontypes.GetPlainStringText("$").WithFontStyle(commontypes.FontStyle_NUMBER_XL).WithFontColor(usstocksUi.Onyx),
				commontypes.GetPlainStringText("0").WithFontStyle(commontypes.FontStyle_NUMBER_3XL).WithFontColor(usstocksUi.Snow),
			),
		}

		withdrawCta = ui.NewITC().WithTexts(
			commontypes.GetPlainStringText(WalletWithdrawFunds).WithFontStyle(commontypes.FontStyle_HEADLINE_3).WithFontColor(usstocksUi.Lead),
		).WithContainer(
			0, 0, 19, usstocksUi.Charcoal,
		).WithContainerPadding(
			20, 24, 20, 24,
		)

		footerTxt = ui.NewITC().WithTexts(
			commontypes.GetPlainStringText(WalletSectionFooterZeroBalanceText).WithFontStyle(commontypes.FontStyle_SUBTITLE_S).WithFontColor(usstocksUi.MonochromeCoin),
		).WithLeftImageUrlHeightAndWidth(
			"https://epifi-icons.pointz.in/usstocks_image/bulb_wallet_page_icon.png", 28, 28,
		).WithLeftImagePadding(4)
	}

	walletIcon := commontypes.GetVisualElementImageFromUrl(WalletPageIconUrl).WithProperties(&commontypes.VisualElementProperties{
		Height: 56,
		Width:  56,
	})

	dl, err := s.getWalletAddFundsPageDeeplink(ctx, actorId)
	if err != nil {
		return nil, errors.Wrap(err, "error in getting wallet add funds page deeplink")
	}
	addCta = ui.NewITC().WithTexts(
		commontypes.GetPlainStringText(WalletAddFunds).WithFontStyle(commontypes.FontStyle_HEADLINE_3).WithFontColor(usstocksUi.Snow),
	).WithLeftImagePadding(
		4,
	).WithLeftImageUrlHeightAndWidth(
		usstocksUi.PlusImage, 24, 24,
	).WithContainer(
		0, 0, 19, usstocksUi.Forest,
	).WithContainerPadding(
		16, 40, 16, 40,
	).WithDeeplink(dl)

	loanAccCheckState, err := utils.CheckActiveLoanAccountsForUser(ctx, s.preApprovedLoanClient, actorId)
	if err != nil {
		logger.Error(ctx, "error in getting loan accounts for user", zap.Error(err))
		return nil, err
	}

	// presenting Add funds blocked dialog if user has an active loan account
	if loanAccCheckState == utils.ActiveLoanAccountPresent {
		addCta = addCta.WithDeeplink(
			AddFundsBlockedDeeplink(),
		)
	}
	if s.config.USStocks().DisableAddFunds() {
		addCta.Deeplink = temporaryClosedForMaintenanceDeeplink
	}
	if s.config.USStocks().DisableWithdrawFunds() {
		withdrawCta.Deeplink = routineMaintenanceDeeplink
	}
	return &usstocks.Wallet{
		WalletIcon:        walletIcon,
		WalletBalance:     balanceDisplay,
		Footer:            footerTxt,
		AddFundsCta:       addCta,
		WithdrawFundsCta:  withdrawCta,
		WithdrawFundsInfo: s.getWithdrawInfoByCta(withdrawCta),
	}, nil
}

func (s *Service) getWithdrawInfoByCta(withdrawCta *ui.IconTextComponent) *usstocks.WithdrawFundsInfo {
	if withdrawCta.GetDeeplink().GetScreen() == deeplink.Screen_USSTOCKS_WALLET_WITHDRAW_FUNDS_SCREEN {
		return &usstocks.WithdrawFundsInfo{
			Cta: withdrawCta,
			BottomSheet: deeplinkv3.GetDeeplinkV3WithoutError(deeplink.Screen_USSTOCKS_WALLET_BOTTOM_SHEET, &ussScreenOptions.USStocksWalletBottomSheetOptions{
				Title:    commontypes.GetTextFromStringFontColourFontStyle("Did you know?", "#929599", commontypes.FontStyle_HEADLINE_1),
				SubTitle: commontypes.GetTextFromStringFontColourFontStyle("You may earn more returns on your USD wallet balance if you withdraw later", "#313234", commontypes.FontStyle_HEADLINE_M),
				Img: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/usstocks_images/us-stocks-withdrawal-chart.png").
					WithImageType(commontypes.ImageType_PNG),
				Description: commontypes.GetTextFromStringFontColourFontStyle("Note: Past performance doesn't guarantee future returns", "#B2B5B9", commontypes.FontStyle_BODY_XS),
				Cta: []*ui.IconTextComponent{
					getBottomSheetCtaForDeeplinkAndText(nil, "I’ll withdraw later"),
					getBottomSheetCtaForDeeplinkAndText(withdrawCta.GetDeeplink(), "I want to withdraw now"),
				},
			}),
			BottomSheetDisplayIntervalInSeconds: int64(s.config.USStocks().WithdrawBottomSheetDisplayDuration().Seconds()),
		}
	}
	return &usstocks.WithdrawFundsInfo{
		Cta:                                 withdrawCta,
		BottomSheet:                         withdrawCta.GetDeeplink(),
		BottomSheetDisplayIntervalInSeconds: int64(s.config.USStocks().WithdrawBottomSheetDisplayDuration().Seconds()),
	}
}

func getBottomSheetCtaForDeeplinkAndText(ctaDeeplink *deeplink.Deeplink, text string) *ui.IconTextComponent {
	return ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(text, "#00B899", commontypes.FontStyle_BUTTON_M)).WithDeeplink(ctaDeeplink).WithContainer(44, 364, 20, "#F6F9FD").WithContainerPadding(12, 16, 12, 16)
}

// nolint:unparam
func (s *Service) createBottomSection(ctx context.Context, actorId string, showZeroState bool, activities []*usstocks.AccountActivityList,
	pageCtx *rpc.PageContextResponse) (*usstocks.WalletPageBottomSection, error) {
	isUSStocksSIPEnabled, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_FEATURE_US_STOCKS_SIP).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "error evaluating US stocks SIP release", zap.Error(err))
		return nil, err
	}
	if isUSStocksSIPEnabled {
		activitySection, err := s.createUpcomingSipComponentAndLatestActivity(ctx, actorId, activities, pageCtx)
		if err != nil {
			logger.Error(ctx, "error creating wallet section", zap.Error(err))
			return nil, err
		}
		if activitySection == nil {
			return &usstocks.WalletPageBottomSection{
				Components: []*usstocks.WalletPageBottomSectionComponent{
					s.createWalletZeroStateComponent(),
					s.createPartnerDisclosureComponent(),
				},
			}, nil
		}
		return &usstocks.WalletPageBottomSection{
			Components: []*usstocks.WalletPageBottomSectionComponent{
				s.createWalletBannerComponent(),
				activitySection,
				s.createPartnerDisclosureComponent(),
			},
		}, nil
	}
	// TODO: Remove after 30th November, 2024
	if showZeroState {
		return &usstocks.WalletPageBottomSection{
			Components: []*usstocks.WalletPageBottomSectionComponent{
				s.createWalletZeroStateComponent(),
				s.createPartnerDisclosureComponent(),
			},
		}, nil
	}
	return &usstocks.WalletPageBottomSection{
		Components: []*usstocks.WalletPageBottomSectionComponent{
			s.createWalletBannerComponent(),
			s.createActivitiesComponent(activities, pageCtx),
			s.createPartnerDisclosureComponent(),
		},
	}, nil
}

func (s *Service) createWalletZeroStateComponent() *usstocks.WalletPageBottomSectionComponent {
	return &usstocks.WalletPageBottomSectionComponent{
		Component: &usstocks.WalletPageBottomSectionComponent_WalletZeroState{
			WalletZeroState: &usstocks.WalletActivityZeroState{
				Title: ui.NewITC().WithTexts(
					commontypes.GetPlainStringText("Why add funds to your USD Wallet?").WithFontColor(usstocksUi.Charcoal).WithFontStyle(commontypes.FontStyle_SUBTITLE_L),
				),
				InfoTiles: []*ui.IconTextComponent{
					s.walletZeroStateTile(ShieldIconUrl, "Secure transfers with Federal Bank"),
					s.walletZeroStateTile(ShoppingCartIconUrl, "Earn dollar returns and dividends in your Wallet"),
					s.walletZeroStateTile(usstocksUi.WalletWithdrawArrow, "Withdraw funds whenever you need them"),
				},
			},
		},
	}
}

func (s *Service) walletZeroStateTile(imgUrl, text string) *ui.IconTextComponent {
	return ui.NewITC().WithContainer(
		0, 0, 20, usstocksUi.Snow,
	).WithContainerPadding(
		12, 16, 12, 12,
	).WithLeftImageUrlHeightAndWidth(
		imgUrl, 40, 40,
	).WithLeftImagePadding(
		8,
	).WithTexts(commontypes.GetPlainStringText(text).WithFontColor(usstocksUi.Charcoal).WithFontStyle(commontypes.FontStyle_SUBTITLE_S))
}

func (s *Service) createWalletBannerComponent() *usstocks.WalletPageBottomSectionComponent {
	// WalletPageBannerCollection component data is fetched from dynamic elements rpc.
	return &usstocks.WalletPageBottomSectionComponent{
		Component: &usstocks.WalletPageBottomSectionComponent_Banners{
			Banners: &usstocks.WalletPageBannerCollection{},
		},
	}
}

func (s *Service) createActivitiesComponent(activityList []*usstocks.AccountActivityList,
	pageCtx *rpc.PageContextResponse) *usstocks.WalletPageBottomSectionComponent {
	return &usstocks.WalletPageBottomSectionComponent{
		Component: &usstocks.WalletPageBottomSectionComponent_WalletActivities{
			WalletActivities: &usstocks.AccountActivities{
				Title: ui.NewITC().WithTexts(
					commontypes.GetPlainStringText("Latest activity").WithFontStyle(commontypes.FontStyle_SUBTITLE_2).WithFontColor(usstocksUi.Night),
				),
				AccountActivities: activityList,
				PageContext:       pageCtx,
				Filters:           walletAccountActivityFilter(),
			},
		},
	}
}

func (s *Service) getActivities(ctx context.Context, actorId string) ([]*usstocks.AccountActivityList, *rpc.PageContextResponse, error) {
	activityList, pageCtx, err := s.accountActivityIterator.GetAccountActivities(ctx, actorId, walletAccountActivityFilter(), nil)
	if err != nil {
		return nil, nil, fmt.Errorf("error while generating activity list : %w", err)
	}
	return activityList, pageCtx, nil
}

func (s *Service) createPartnerDisclosureComponent() *usstocks.WalletPageBottomSectionComponent {
	return &usstocks.WalletPageBottomSectionComponent{
		Component: &usstocks.WalletPageBottomSectionComponent_TextComponent{
			TextComponent: ui.NewITC().WithTexts(
				commontypes.GetPlainStringText("IN PARTNERSHIP WITH US-BASED").WithFontStyle(commontypes.FontStyle_OVERLINE_2XS_CAPS).WithFontColor(usstocksUi.MonochromeAsh),
			).
				WithRightImagePadding(4).
				WithRightImageUrlHeightAndWidth(usstocksUi.AlpacaLogo, 14, 49),
		},
	}
}

func (s *Service) getWalletAddFundsCta(ctx context.Context, actorId string) (*ui.IconTextComponent, error) {
	dl, err := s.getWalletAddFundsPageDeeplink(ctx, actorId)
	if err != nil {
		return nil, errors.Wrap(err, "error in getting wallet add funds page deeplink")
	}

	return ui.NewITC().
		WithTexts(commontypes.GetPlainStringText("Add funds to your USD wallet").
			WithFontStyle(commontypes.FontStyle_BUTTON_M).
			WithFontColor(usstocksUi.Snow)).
		WithDeeplink(dl).
		WithLeftImageUrlHeightAndWidth(usstocksUi.PlusIcon, 24, 24).
		WithLeftImagePadding(4).WithContainerProperties(
		&ui.IconTextComponent_ContainerProperties{
			BgColor:       usstocksUi.Forest,
			CornerRadius:  20,
			LeftPadding:   24,
			RightPadding:  16,
			TopPadding:    12,
			BottomPadding: 12,
		}), nil
}

func (s *Service) getWalletAddFundsPageDeeplink(ctx context.Context, actorId string) (*deeplink.Deeplink, error) {
	platform, version := epificontext.AppPlatformAndVersion(ctx)
	resp, nssErr := s.internationalFundTransferClient.GetNextSOFStep(ctx, &iftPb.GetNextSOFStepRequest{
		ActorId:             actorId,
		RedirectionDeeplink: addFundsDeeplink,
		EntryPoint:          iftPb.GetNextSOFStepRequest_SOF_ENTRY_POINT_USSTOCKS_BUY_FLOW,
		Platform:            platform,
		Version:             uint32(version),
	})
	if te := epifigrpc.RPCError(resp, nssErr); te != nil {
		return nil, errors.Wrap(te, "error while executing GetNextSOFStep")
	}
	// resp.GetDeeplink() may contain future scope deeplink, existing connected account deeplink, add external account deeplink
	// or waiting screen for fetching txn data process from newly connected account.
	// if SOF already existed or federal account is older than 6month then deeplink response would be empty.
	if resp.GetDeeplink() != nil {
		logger.Info(ctx, "Response for GetNextSOFStep in buy flow", zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.SCREEN, resp.GetDeeplink().Screen.String()), zap.String("sof_status", resp.GetSofStatus().String()))
		if resp.GetSofStatus() == iftPb.SOFStatus_SOF_STATUS_ACCOUNT_CONNECTION_IN_PROGRESS ||
			resp.GetSofStatus() == iftPb.SOFStatus_SOF_STATUS_UNSPECIFIED {
			return resp.GetDeeplink(), nil
		}
		if resp.GetSofStatus() == iftPb.SOFStatus_SOF_STATUS_CONNECTED_ACCOUNT_STATEMENT_EXPIRED {
			return &deeplink.Deeplink{
				Screen:        deeplink.Screen_US_STOCK_VALIDATION_DIALOG,
				ScreenOptions: deeplinks.GetSofExpiredDeeplinkOption(resp.GetDeeplink()),
			}, nil
		}
		return &deeplink.Deeplink{
			Screen:        deeplink.Screen_US_STOCK_VALIDATION_DIALOG,
			ScreenOptions: deeplinks.GetSofDocRequiredDeeplinkOption(resp.GetDeeplink()),
		}, nil
	}
	return addFundsDeeplink, nil
}

func AddFundsBlockedDeeplink() *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_USSTOCKS_BUY_SELL_CLOSED_SCREEN,
		ScreenOptions: &deeplink.Deeplink_UsstocksBuySellClosedScreenOptions{
			UsstocksBuySellClosedScreenOptions: &deeplink.USStocksBuySellClosedScreenOptions{
				ImageTitleSubtitle: &widgetUi.ImageTitleSubtitleElement{
					IconImage: &commontypes.Image{
						ImageType: commontypes.ImageType_PNG,
						ImageUrl:  "https://epifi-icons.pointz.in/usstocks_images/closed-icon.png",
						Width:     80,
						Height:    80,
					},
					TitleText: usstocksUi.GetText("You cannot add funds right now", usstocksUi.Night,
						commontypes.FontStyle_SUBTITLE_1),
					SubtitleText: usstocksUi.GetText("Currently, our regulated partner's remittance policy restricts adding new funds. Rest assured, your existing investments remain safe.", usstocksUi.Night,
						commontypes.FontStyle_BODY_3_PARA),
				},
				Cta: &deeplink.Cta{
					Type:         deeplink.Cta_DONE,
					Text:         "Ok, got it",
					DisplayTheme: deeplink.Cta_PRIMARY,
				},
			},
		},
	}
}

func WalletWithdrawFundsPageDeeplink(wallet *account.WalletDetails) *deeplink.Deeplink {
	// if user doesnt have withdrawal balance then show bottom sheet
	if moneyPb.IsZero(wallet.GetWithdrawableAmount()) {
		return deeplinkv3.GetDeeplinkV3WithoutError(deeplink.Screen_BOTTOM_SHEET_INFO_VIEW, &frontend.BottomSheetInfoViewOptions{
			Icon: &commontypes.Image{
				ImageUrl: "https://epifi-icons.pointz.in/usstocks_images/withdrawalBalanceIsZero.png",
				Width:    80,
				Height:   80,
			},
			Title:    commontypes.GetTextFromStringFontColourFontStyle("You don’t have enough balance to withdraw", "#333333", commontypes.FontStyle_SUBTITLE_L),
			SubTitle: commontypes.GetTextFromStringFontColourFontStyle("Amounts from recently sold stocks will not appear now, as our broker partner is still processing the settlement.", "#333333", commontypes.FontStyle_BODY_3_PARA),
			Cta: &deeplink.Cta{
				Text: "Ok, got it",
			},
		})
	}
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_USSTOCKS_WALLET_WITHDRAW_FUNDS_SCREEN,
	}
}

func getWithdrawInfoPopup() *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_INFORMATION_POPUP,
		ScreenOptions: &deeplink.Deeplink_InformationPopupOptions{
			InformationPopupOptions: &deeplink.InformationPopupOptions{
				IconUrl:   InfoLogoUrl,
				TextTitle: commontypes.GetPlainStringText("Withdrawable Amount").WithFontStyle(commontypes.FontStyle_SUBTITLE_2).WithFontColor(colors.ColorOnLightHighEmphasis),
				BodyTexts: []*commontypes.Text{
					commontypes.GetPlainStringText(
						"All funds may not be available just yet as funds may take up to 2 days to get settled. They're currently being processed with our trusted broker, and will be fully accessible soon.",
					).WithFontStyle(commontypes.FontStyle_SUBTITLE_M),
				},
				BgColor: colors.ColorSnow,
			},
		},
		ScreenOptionsV2: nil,
	}
}

// nolint:funlen
func (s *Service) createUpcomingSipComponentAndLatestActivity(ctx context.Context, actorId string, activityList []*usstocks.AccountActivityList,
	pageCtx *rpc.PageContextResponse) (*usstocks.WalletPageBottomSectionComponent, error) {
	activityTabInfos := make([]*usstocks.AccountActivityTabInfo, 0)
	sipSubscriptions, sErr := s.GetActiveUSSFitSubscriptions(ctx, actorId)
	if sErr != nil {
		return nil, sErr
	}
	if len(sipSubscriptions) == 0 && len(activityList) == 0 {
		logger.Debug(ctx, "no active sip subscriptions and activities found")
		return nil, nil
	}
	if len(activityList) != 0 {
		activityTabInfos = append(activityTabInfos, &usstocks.AccountActivityTabInfo{
			Activities: &usstocks.AccountActivities{
				AccountActivities: activityList,
				PageContext:       pageCtx,
				Filters:           walletAccountActivityFilter(),
			},
			IsSelected: true,
			SelectedText: &ui.IconTextComponent{
				Texts: []*commontypes.Text{
					commontypes.GetTextFromStringFontColourFontStyle("Latest activity", "#333333", commontypes.FontStyle_HEADLINE_M),
				},
				ContainerProperties: getContainerProperties(20, 12, 6, 12, 6),
			},
			Text: &ui.IconTextComponent{
				Texts: []*commontypes.Text{
					commontypes.GetTextFromStringFontColourFontStyle("Latest activity", "#525355", commontypes.FontStyle_HEADLINE_M),
				},
				ContainerProperties: getContainerProperties(20, 12, 6, 12, 6),
			},
		})
	}
	if len(sipSubscriptions) != 0 {
		stockInfoMap := map[string]*usstocksCatalogPb.Stock{}
		for _, subscription := range sipSubscriptions {
			stockId := GetStockIdFromSubscription(subscription)
			if _, ok := stockInfoMap[stockId]; !ok {
				stock, err := s.getStockInfo(ctx, stockId)
				if err != nil {
					return nil, err
				}
				stockInfoMap[stockId] = stock
			}
		}
		sipActivities, err := activity.GetUpcomingSipActivities(ctx, sipSubscriptions, stockInfoMap)
		if err != nil {
			return nil, err
		}
		// if there are no account activities, then sip tab should be selected by default
		isSelected := len(activityList) == 0
		activityTabInfos = append(activityTabInfos, &usstocks.AccountActivityTabInfo{
			Text: &ui.IconTextComponent{
				Texts: []*commontypes.Text{
					commontypes.GetTextFromStringFontColourFontStyle("Upcoming", "#57595D", commontypes.FontStyle_HEADLINE_M),
				},
				ContainerProperties: getContainerProperties(20, 12, 6, 12, 6),
			},
			SelectedText: &ui.IconTextComponent{
				Texts: []*commontypes.Text{
					commontypes.GetTextFromStringFontColourFontStyle("Upcoming", "#333333", commontypes.FontStyle_HEADLINE_M),
				},
				ContainerProperties: getContainerProperties(20, 12, 6, 12, 6),
			},
			IsSelected: isSelected,
			Activities: &usstocks.AccountActivities{
				AccountActivities: sipActivities,
			},
		})
	}

	return &usstocks.WalletPageBottomSectionComponent{
		Component: &usstocks.WalletPageBottomSectionComponent_ActivityTabList{
			ActivityTabList: &usstocks.AccountActivityTabList{
				ActivityTabInfos: activityTabInfos,
				SelectBgColor:    "#FFFFFF",
				BgColor:          "#E7ECF0",
			},
		},
	}, nil
}

// nolint:unparam
func getContainerProperties(cornerRadius, topPadding, leftPadding, bottomPadding, rightPadding int32) *ui.IconTextComponent_ContainerProperties {
	return &ui.IconTextComponent_ContainerProperties{
		CornerRadius:  cornerRadius,
		TopPadding:    topPadding,
		LeftPadding:   leftPadding,
		BottomPadding: bottomPadding,
		RightPadding:  rightPadding,
	}
}
