package usstocks

import (
	"context"
	"fmt"
	"math"
	"strings"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	feHdr "github.com/epifi/gamma/api/frontend/header"
	usStocksFePb "github.com/epifi/gamma/api/frontend/usstocks"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/usstocks"
	usStocksCatalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	"github.com/epifi/gamma/api/usstocks/order"
	vgStocksPb "github.com/epifi/gamma/api/vendorgateway/stocks"
	usStocksUi "github.com/epifi/gamma/frontend/usstocks/ui"
	"github.com/epifi/gamma/frontend/usstocks/utils"
)

// Note: This RPC is exposed to web and doesn't require authentication necessarily

// nolint:funlen
func (s *Service) GetSymbolHistoricalPrices(ctx context.Context, req *usStocksFePb.GetSymbolHistoricalPricesRequest) (*usStocksFePb.GetSymbolHistoricalPricesResponse, error) {
	stockId := req.GetStockId()
	// TODO: remove this log after testing the issue
	logger.Info(ctx, "getting symbol historical prices for stock", zap.String(logger.STOCK_ID, stockId))
	if stockId == "" || strings.EqualFold(stockId, "null") {
		return &usStocksFePb.GetSymbolHistoricalPricesResponse{
			RespHeader: &feHdr.ResponseHeader{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("stock id is mandatory"),
			},
		}, nil
	}

	g, gCtx := errgroup.WithContext(ctx)
	var (
		stock               *usStocksCatalogPb.Stock
		marketCategoriesMap map[string]*usStocksCatalogPb.MarketCategory
		histPrices          []*usStocksCatalogPb.StockPricesForPeriod
		stockPriceSnapshot  *vgStocksPb.GetStocksPriceSnapshotsResponse_StockPriceInfo
		marketStatus        usstocks.MarketStatus
	)
	g.Go(func() error {
		var err error
		stock, err = utils.GetStockById(gCtx, s.catalogManagerClient, stockId)
		if err != nil {
			return errors.Wrap(err, fmt.Sprintf("error getting stock by id %s", stockId))
		}
		marketCategoryVendorIds := utils.GetMarketCategoryVendorIdsFromStocks([]*usStocksCatalogPb.Stock{stock})

		// fetch market categories for all the stocks to populate stock list item tags
		marketCategoriesRes, err := s.catalogManagerClient.GetMarketCategories(gCtx,
			&usStocksCatalogPb.GetMarketCategoriesRequest{
				VendorIds: marketCategoryVendorIds,
			})
		if err = epifigrpc.RPCError(marketCategoriesRes, err); err != nil {
			return errors.Wrap(err, "error while calling GetMarketCategories RPC")
		}
		marketCategoriesMap = marketCategoriesRes.GetMarketCategories()
		spsRes, err := s.catalogManagerClient.GetStocksPriceSnapshotsBySymbols(gCtx, &usStocksCatalogPb.GetStocksPriceSnapshotsBySymbolsRequest{StockSymbols: []string{stock.GetSymbol()}})
		if err = epifigrpc.RPCError(spsRes, err); err != nil {
			if spsRes.GetStatus().IsResourceExhausted() {
				return errors.Wrap(epifierrors.ErrResourceExhausted, "resource exhausted while getting stock price snapshots")
			}
			return errors.Wrap(err, "error getting stock price snapshots")
		}
		var ok bool
		stockPriceSnapshot, ok = spsRes.GetStockPriceSnapshots()[stock.GetSymbol()]
		if !ok {
			return fmt.Errorf("no price snapshot found for stock")
		}
		return nil
	})
	g.Go(func() error {
		hspRes, err := s.catalogManagerClient.GetHistoricalStockPrices(gCtx, &usStocksCatalogPb.GetHistoricalStockPricesRequest{StockId: stockId})
		if err = epifigrpc.RPCError(hspRes, err); err != nil {
			if hspRes.GetStatus().IsResourceExhausted() {
				return errors.Wrap(epifierrors.ErrResourceExhausted, "resource exhausted while fetching historical stock prices")
			}
			return errors.Wrap(err, "error getting historical stock prices")
		}
		histPrices = hspRes.GetStockPricesForPeriods()
		return nil
	})
	g.Go(func() error {
		marketStatusRes, err := s.orderManagerClient.GetMarketStatus(gCtx, &order.GetMarketStatusRequest{})
		if te := epifigrpc.RPCError(marketStatusRes, err); te != nil {
			return fmt.Errorf("error getting market staus: %w", te)
		}
		marketStatus = marketStatusRes.GetMarketStatus()
		return nil
	})
	err := g.Wait()
	if err != nil {
		if errors.Is(err, epifierrors.ErrResourceExhausted) {
			logger.Error(ctx, "resource exhausted in error group", zap.Error(err))
			return &usStocksFePb.GetSymbolHistoricalPricesResponse{RespHeader: &feHdr.ResponseHeader{Status: rpc.StatusResourceExhausted()}}, nil
		}
		logger.Error(ctx, "error in a go routine", zap.Error(err))
		return &usStocksFePb.GetSymbolHistoricalPricesResponse{RespHeader: &feHdr.ResponseHeader{Status: rpc.StatusInternal()}}, nil
	}

	stockPricesForTimePeriods, err := getStockPricesForTimePeriods(histPrices, req.GetReq().GetAuth().GetDevice().GetPlatform(), marketStatus)
	if err != nil {
		logger.Error(ctx, "error getting stock prices for time periods", zap.Error(err))
		return &usStocksFePb.GetSymbolHistoricalPricesResponse{RespHeader: &feHdr.ResponseHeader{Status: rpc.StatusInternal()}}, nil
	}

	return &usStocksFePb.GetSymbolHistoricalPricesResponse{
		RespHeader:                &feHdr.ResponseHeader{Status: rpc.StatusOk()},
		StockLogo:                 &commontypes.Image{ImageUrl: stock.GetStockBasicDetails().GetLogoUrl()},
		StockName:                 usStocksUi.GetText(stock.GetStockBasicDetails().GetName().GetShortName(), usStocksUi.Snow, commontypes.FontStyle_SUBTITLE_1),
		CurrentPrice:              types.GetFromBeMoney(stock.GetDailyPerformance().GetClose()),
		Tags:                      utils.GetStockDetailsTags(stock, marketCategoriesMap),
		StockPricesForTimePeriods: stockPricesForTimePeriods,
		FaqArticles:               getFaqs(stock, marketCategoriesMap, histPrices, stockPriceSnapshot),
	}, nil
}

// nolint:funlen
func getFaqs(
	stock *usStocksCatalogPb.Stock,
	marketCategoriesMap map[string]*usStocksCatalogPb.MarketCategory,
	histPrices []*usStocksCatalogPb.StockPricesForPeriod,
	stockPriceSnapshot *vgStocksPb.GetStocksPriceSnapshotsResponse_StockPriceInfo,
) []*usStocksFePb.FaqArticle {
	stockName := stock.GetStockBasicDetails().GetName().GetShortName()
	var faqs []*usStocksFePb.FaqArticle

	if stock.GetDailyPerformance().GetClose() != nil {
		stockPriceQ := fmt.Sprintf("What is the price of %s share today in US?", stockName)
		stockPriceA := fmt.Sprintf("The share price of %s today in US is %s. Buy this stock at this price right now in real-time on the Fi money app.",
			stockName, money.ToDisplayString(stock.GetDailyPerformance().GetClose()))
		faqs = append(faqs, &usStocksFePb.FaqArticle{
			Question: usStocksUi.GetText(stockPriceQ, usStocksUi.Night, commontypes.FontStyle_SUBTITLE_M),
			Answer:   usStocksUi.GetText(stockPriceA, usStocksUi.Night, commontypes.FontStyle_BODY_S),
		})
	}

	for _, histPricesForPeriod := range histPrices {
		if histPricesForPeriod.GetPeriod() != "1D" {
			continue
		}
		var highPrice1D float64
		lowPrice1D := math.MaxFloat64
		for _, bar := range histPricesForPeriod.GetBars() {
			highPrice1D = math.Max(highPrice1D, bar.GetHighPrice())
			lowPrice1D = math.Min(lowPrice1D, bar.GetLowPrice())
		}
		highLow1DPricesQ := fmt.Sprintf("What are today’s High and Low prices of %s?", stockName)
		highLow1DPricesA := fmt.Sprintf("Today’s High and Low prices of %s are %s and %s.", stockName,
			money.ToDisplayString(money.ParseFloat(highPrice1D, money.USDCurrencyCode)), money.ToDisplayString(money.ParseFloat(lowPrice1D, money.USDCurrencyCode)))
		faqs = append(faqs, &usStocksFePb.FaqArticle{
			Question: usStocksUi.GetText(highLow1DPricesQ, usStocksUi.Night, commontypes.FontStyle_SUBTITLE_M),
			Answer:   usStocksUi.GetText(highLow1DPricesA, usStocksUi.Night, commontypes.FontStyle_BODY_S),
		})
	}

	howToBuyQ := fmt.Sprintf("How can I buy %s shares from India?", stockName)
	howToBuyA := fmt.Sprintf("You can buy %s stocks in real-time on the Fi money app.", stockName)
	faqs = append(faqs, &usStocksFePb.FaqArticle{
		Question: usStocksUi.GetText(howToBuyQ, usStocksUi.Night, commontypes.FontStyle_SUBTITLE_M),
		Answer:   usStocksUi.GetText(howToBuyA, usStocksUi.Night, commontypes.FontStyle_BODY_S),
	})

	onboardingDocsQ := fmt.Sprintf("What are the documents required to start investing in %s stocks?", stockName)
	onboardingDocsA := fmt.Sprintf("Enjoy a free and paperless account creation on Fi Money. You would need to keep your PAN and Aadhaar handy for this. Once you have created an account, invest hassle-free in %s stocks.",
		stockName)
	faqs = append(faqs, &usStocksFePb.FaqArticle{
		Question: usStocksUi.GetText(onboardingDocsQ, usStocksUi.Night, commontypes.FontStyle_SUBTITLE_M),
		Answer:   usStocksUi.GetText(onboardingDocsA, usStocksUi.Night, commontypes.FontStyle_BODY_S),
	})

	if stockPriceSnapshot.GetDailyBar().GetVolume() != 0 {
		dailyVolumeQ := fmt.Sprintf("What are today’s traded volumes of %s?", stockName)
		dailyVolumeA := fmt.Sprintf("The traded volumes of %s is %d.", stockName, stockPriceSnapshot.GetDailyBar().GetVolume())
		faqs = append(faqs, &usStocksFePb.FaqArticle{
			Question: usStocksUi.GetText(dailyVolumeQ, usStocksUi.Night, commontypes.FontStyle_SUBTITLE_M),
			Answer:   usStocksUi.GetText(dailyVolumeA, usStocksUi.Night, commontypes.FontStyle_BODY_S),
		})
	}

	fractionalSharesQ := fmt.Sprintf("Can I buy fractional shares of %s?", stockName)
	fractionalSharesA := fmt.Sprintf("You can buy fractional shares of %s if it is listed in the exchanges of USA, Canada, Japan, or the UK. In India, you can buy fractional shares starting at $10 through the Fi money app.",
		stockName)
	faqs = append(faqs, &usStocksFePb.FaqArticle{
		Question: usStocksUi.GetText(fractionalSharesQ, usStocksUi.Night, commontypes.FontStyle_SUBTITLE_M),
		Answer:   usStocksUi.GetText(fractionalSharesA, usStocksUi.Night, commontypes.FontStyle_BODY_S),
	})

	var marketCap *moneyPb.Money
	switch stock.GetStockType() {
	case usStocksCatalogPb.StockType_STOCK_TYPE_ETF:
		marketCap = stock.GetEtfFinancialInfo().GetMarketCapitalisation()
	case usStocksCatalogPb.StockType_STOCK_TYPE_INDIVIDUAL_COMPANY:
		marketCap = stock.GetCompanyInfo().GetMarketCap().GetMarketCapValue()
	default:
	}
	if marketCap != nil {
		marketCapStr := money.ToDisplayStringWithSuffixAndPrecision(marketCap, true, true, 2, money.InternationalNumberSystem)
		marketCapQ := fmt.Sprintf("What is today’s market capitalisation of %s?", stockName)
		marketCapA := fmt.Sprintf("The market capitalisation of %s is %s.", stockName, marketCapStr)
		faqs = append(faqs, &usStocksFePb.FaqArticle{
			Question: usStocksUi.GetText(marketCapQ, usStocksUi.Night, commontypes.FontStyle_SUBTITLE_M),
			Answer:   usStocksUi.GetText(marketCapA, usStocksUi.Night, commontypes.FontStyle_BODY_S),
		})
	}

	peRatio := stock.GetFinancialInfo().GetLatestValuationRatio().GetPriceToEps()
	if peRatio != 0 {
		peRatioQ := fmt.Sprintf("What is the P/E ratio for %s shares?", stockName)
		peRatioA := fmt.Sprintf("The P/E ratio for %s is %.2f.", stockName, peRatio)
		faqs = append(faqs, &usStocksFePb.FaqArticle{
			Question: usStocksUi.GetText(peRatioQ, usStocksUi.Night, commontypes.FontStyle_SUBTITLE_M),
			Answer:   usStocksUi.GetText(peRatioA, usStocksUi.Night, commontypes.FontStyle_BODY_S),
		})
	}

	for _, histPricesForPeriod := range histPrices {
		if histPricesForPeriod.GetPeriod() != "5Y" {
			continue
		}
		hist5YReturnsQ := fmt.Sprintf("What are the historical returns of %s?", stockName)
		hist5YReturnsA := fmt.Sprintf("The historical returns of %s is %.2f%% over last 5 years.", stockName, histPricesForPeriod.GetPercentagePriceChange())
		faqs = append(faqs, &usStocksFePb.FaqArticle{
			Question: usStocksUi.GetText(hist5YReturnsQ, usStocksUi.Night, commontypes.FontStyle_SUBTITLE_M),
			Answer:   usStocksUi.GetText(hist5YReturnsA, usStocksUi.Night, commontypes.FontStyle_BODY_S),
		})
	}

	if marketCategoriesMap[stock.GetIndustryId()].GetCategoryName() != "" {
		companySectorQ := fmt.Sprintf("Which sector does %s belong to?", stockName)
		companySectorA := fmt.Sprintf("%s belongs to the %s sector.", stockName, marketCategoriesMap[stock.GetIndustryId()].GetCategoryName())
		faqs = append(faqs, &usStocksFePb.FaqArticle{
			Question: usStocksUi.GetText(companySectorQ, usStocksUi.Night, commontypes.FontStyle_SUBTITLE_M),
			Answer:   usStocksUi.GetText(companySectorA, usStocksUi.Night, commontypes.FontStyle_BODY_S),
		})
	}
	return faqs
}
