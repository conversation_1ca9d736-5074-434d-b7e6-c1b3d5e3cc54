package usstocks

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	feHdr "github.com/epifi/gamma/api/frontend/header"
	usStocksFePb "github.com/epifi/gamma/api/frontend/usstocks"
	usStocksCatalogPb "github.com/epifi/gamma/api/usstocks/catalog"
)

func (s *Service) GetStockForSymbol(ctx context.Context, req *usStocksFePb.GetStockForSymbolRequest) (*usStocksFePb.GetStockForSymbolResponse, error) {
	res, err := s.catalogManagerClient.GetStocks(ctx, &usStocksCatalogPb.GetStocksRequest{
		Identifiers: &usStocksCatalogPb.GetStocksRequest_Symbols{Symbols: &usStocksCatalogPb.RepeatedStrings{Ids: []string{req.GetSymbol()}}},
	})
	if err = epifigrpc.RPCError(res, err); err != nil {
		// Users visiting Fi website can change the URL and try to fetch an unsupported stock by its symbol
		// To ignore alerts raised because of errors returned in such cases, a special status code is returned
		if rpc.StatusFromError(err).IsRecordNotFound() {
			return &usStocksFePb.GetStockForSymbolResponse{RespHeader: &feHdr.ResponseHeader{Status: rpc.StatusRecordNotFound()}}, nil
		}
		if res.GetStatus().IsResourceExhausted() {
			logger.Error(ctx, "resource exhausted while getting stock for symbol", zap.Error(err))
			return &usStocksFePb.GetStockForSymbolResponse{RespHeader: &feHdr.ResponseHeader{Status: rpc.StatusResourceExhausted()}}, nil
		}
		logger.Error(ctx, "error getting stock id for symbol", zap.Error(err))
		return &usStocksFePb.GetStockForSymbolResponse{RespHeader: &feHdr.ResponseHeader{Status: rpc.StatusInternal()}}, nil
	}
	if len(res.GetStocks()) == 0 {
		logger.Error(ctx, "no stocks found for symbol")
		return &usStocksFePb.GetStockForSymbolResponse{RespHeader: &feHdr.ResponseHeader{Status: rpc.StatusInternal()}}, nil
	}
	return &usStocksFePb.GetStockForSymbolResponse{
		RespHeader: &feHdr.ResponseHeader{Status: rpc.StatusOk()},
		StockId:    res.GetStocks()[req.GetSymbol()].GetId(),
	}, nil
}
