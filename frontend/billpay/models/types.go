package models

import (
	widgetPb "github.com/epifi/be-common/api/typesv2/common/ui/widget"

	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/gamma/api/typesv2/ui"
)

// RechargeAccountInfo data for the account info card shown above plans in recharge plans screen
type RechargeAccountInfo struct {
	ImageUrl          string
	Operator          string
	AccountIdentifier string
	AccountType       string
	State             string
}

// RechargePlanDetails details required to build recharge plan card
type RechargePlanDetails struct {
	Amount       *moneyPb.Money
	Tag          *string
	Infos        []KeyValuePair
	FooterBanner *PromoBanner
}

// KeyValuePair model that can be used to represent recharge details(validity, data etc) or bill details(service provider, number etc)
type KeyValuePair struct {
	Key   string
	Value string
}

// RechargePlanDetailBottomSheet data to be displayed in recharge plan details bottom sheet
type RechargePlanDetailBottomSheet struct {
	Amount      *moneyPb.Money
	Infos       []KeyValuePair
	PlanDetails string
}

// PromoBanner banner component that can be used in intro screen and recharge plans card
type PromoBanner struct {
	Itc     *ui.IconTextComponent
	BgColor *widgetPb.BackgroundColour
}

// BillDetails data required to build the bill details section
type BillDetails struct {
	Title       string
	Infos       []KeyValuePair
	Description string
}
