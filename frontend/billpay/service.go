package billpay

import (
	"context"
	"regexp"
	"strings"

	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	billpayPb "github.com/epifi/gamma/api/billpay"
	billpayEnums "github.com/epifi/gamma/api/billpay/enums"
	"github.com/epifi/gamma/api/frontend/billpay"
	frontendBillpayEnums "github.com/epifi/gamma/api/frontend/billpay/enums"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	"github.com/epifi/gamma/api/typesv2"
	layoutPb "github.com/epifi/gamma/api/typesv2/billpay"
	billPayScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/billpay"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/sections"
	"github.com/epifi/gamma/frontend/billpay/models"
	uiBuilder "github.com/epifi/gamma/frontend/billpay/ui"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
)

var (
	// unlimitedDataRegex matches unlimited data patterns in plan descriptions
	// Examples: "Data: Unlimited", "Unlimited Data"
	unlimitedDataRegex = regexp.MustCompile(`(?i)(Data\s*:\s*Unlimited|Unlimited\s+Data)`)

	// dailyDataRegex matches daily data patterns in plan descriptions
	// Examples: "2.5GB/Day", "1 GB/Day", "500MB/Day"
	dailyDataRegex = regexp.MustCompile(`(?i)(\d+(?:\.\d+)?)\s*(GB|MB|TB)\s*/\s*Day`)

	// dataRegex matches general data patterns in plan descriptions
	// Examples: "10GB", "1.5 GB", "500MB"
	dataRegex = regexp.MustCompile(`(?i)(\d+(?:\.\d+)?)\s*(GB|MB|TB)`)
)

type Service struct {
	billpay.UnimplementedBillPayServer
	billpayClient billpayPb.BillPayClient
}

func NewService(billpayClient billpayPb.BillPayClient) *Service {
	return &Service{
		billpayClient: billpayClient,
	}
}

// getOperatorLogoURL returns the logo URL for a given operator enum value
func getOperatorLogoURL(operator billpayEnums.Operator) string {
	operatorLogoMap := map[billpayEnums.Operator]string{
		billpayEnums.Operator_OPERATOR_JIO:    "https://epifi-icons.pointz.in/pay/jio.png",
		billpayEnums.Operator_OPERATOR_AIRTEL: "https://epifi-icons.pointz.in/pay/airtel.png",
		billpayEnums.Operator_OPERATOR_VI:     "https://epifi-icons.pointz.in/pay/vi.png",
		billpayEnums.Operator_OPERATOR_BSNL:   "https://epifi-icons.pointz.in/pay/bsnl.png",
		billpayEnums.Operator_OPERATOR_MTNL:   "https://epifi-icons.pointz.in/pay/mtnl.png",
	}

	if logoURL, exists := operatorLogoMap[operator]; exists {
		return logoURL
	}

	return ""
}

func (s *Service) CreateRechargeOrder(ctx context.Context, req *billpay.CreateRechargeOrderRequest) (*billpay.CreateRechargeOrderResponse, error) {
	var (
		actorId = req.GetReq().GetAuth().GetActorId()
		res     = &billpay.CreateRechargeOrderResponse{
			RespHeader: &header.ResponseHeader{},
		}
	)

	// Map account type from frontend to backend enum
	backendAccountType := billpayEnums.RechargeAccountType(billpayEnums.RechargeAccountType_value[req.GetAccountType()])
	if backendAccountType == billpayEnums.RechargeAccountType_RECHARGE_ACCOUNT_TYPE_UNSPECIFIED {
		res.RespHeader.Status = rpc.StatusInvalidArgument()
		return res, nil
	}

	// Map frontend request to backend request
	backendReq := &billpayPb.CreateRechargeOrderRequest{
		ActorId:           actorId,
		AccountType:       backendAccountType,
		AccountIdentifier: req.GetAccountIdentifier(),
		Operator:          billpayEnums.Operator(billpayEnums.Operator_value[req.GetOperatorId()]),
		PlanId:            req.GetPlanId(),
	}

	// Call backend service
	croRes, err := s.billpayClient.CreateRechargeOrder(ctx, backendReq)
	if rpcErr := epifigrpc.RPCError(croRes, err); rpcErr != nil {
		logger.Error(ctx, "error while calling billpay CreateRechargeOrder RPC", zap.Error(rpcErr))
		return &billpay.CreateRechargeOrderResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternal(),
			},
		}, nil
	}

	dl := deeplinkV3.GetDeeplinkV3WithoutError(deeplink.Screen_RECHARGE_POLLING_SCREEN, &billPayScreenOptions.RechargePollingScreenOptions{
		ClientRequestId: croRes.GetClientRequestId(),
		RetryIntervalMs: 500,
	})

	// Map backend response to frontend response
	return &billpay.CreateRechargeOrderResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		NextAction: dl,
	}, nil
}

func (s *Service) GetRechargeOrderStatus(ctx context.Context, req *billpay.GetRechargeOrderStatusRequest) (*billpay.GetRechargeOrderStatusResponse, error) {
	clientRequestId := req.GetClientRequestId()

	// Map frontend request to backend request
	backendReq := &billpayPb.GetRechargeOrderStatusRequest{
		ClientRequestId: clientRequestId,
	}

	// Call backend service
	grosRes, err := s.billpayClient.GetRechargeOrderStatus(ctx, backendReq)
	if rpcErr := epifigrpc.RPCError(grosRes, err); rpcErr != nil {
		logger.Error(ctx, "error while calling billpay GetRechargeOrderStatus RPC", zap.Error(rpcErr))
		return &billpay.GetRechargeOrderStatusResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternal(),
			},
		}, nil
	}

	// Map backend response to frontend response
	return &billpay.GetRechargeOrderStatusResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		OrderStatus: frontendBillpayEnums.RechargeOrderStatus(grosRes.GetOrderStatus()),
		NextAction:  grosRes.GetNextAction(),
	}, nil
}

func (s *Service) GetRechargeIntroScreen(ctx context.Context, req *billpay.GetRechargeIntroScreenRequest) (*billpay.GetRechargeIntroScreenResponse, error) {
	// For now, returning a basic response structure
	return &billpay.GetRechargeIntroScreenResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		Layout: &layoutPb.RechargeIntroScreen{
			BgColor:    widget.GetBlockBackgroundColour("#18191B"),
			TopNavBar:  uiBuilder.BuildTopNavBar("Mobile recharge"),
			TopSection: nil,
			ContactsSection: &layoutPb.RechargeIntroScreen_ContactsSection{
				BgColor:                 widget.GetBlockBackgroundColour("#FFFFFF"),
				SearchBar:               uiBuilder.BuildSearchBar("Enter name or mobile number"),
				ContactsPermissionNudge: nil,
			},
			Footer_Section: nil, // No need to show BBPS logo for recharges, hence nil
		},
	}, nil
}

func (s *Service) GetRechargePlansScreen(ctx context.Context, req *billpay.GetRechargePlansScreenRequest) (*billpay.GetRechargePlansScreenResponse, error) {
	var (
		actorId = req.GetReq().GetAuth().GetActorId()
		res     = &billpay.GetRechargePlansScreenResponse{
			RespHeader: &header.ResponseHeader{},
		}
	)

	// Map account type from frontend to backend enum
	backendAccountType := billpayEnums.RechargeAccountType(billpayEnums.RechargeAccountType_value[req.GetAccountType()])
	if backendAccountType == billpayEnums.RechargeAccountType_RECHARGE_ACCOUNT_TYPE_UNSPECIFIED {
		res.RespHeader.Status = rpc.StatusInvalidArgument()
		return res, nil
	}

	backendReq := &billpayPb.FetchRechargePlansRequest{
		ActorId:           actorId,
		AccountType:       backendAccountType,
		AccountIdentifier: req.GetAccountIdentifier(),
	}

	frpRes, err := s.billpayClient.FetchRechargePlans(ctx, backendReq)
	if rpcErr := epifigrpc.RPCError(frpRes, err); rpcErr != nil {
		logger.Error(ctx, "error while calling billpay FetchRechargePlans RPC", zap.Error(rpcErr))
		return &billpay.GetRechargePlansScreenResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternal(),
			},
		}, nil
	}

	// Build the screen layout
	operatorId := frpRes.GetOperator().String()
	rechargeInfoSection := s.buildRechargeInfoSection(req, frpRes)
	planSegments, defaultSegmentId := s.buildPlanSegments(frpRes)

	return &billpay.GetRechargePlansScreenResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		Layout: &layoutPb.RechargePlansScreen{
			BgColor:   widget.GetBlockBackgroundColour("#F6F9FD"),
			TopNavBar: uiBuilder.BuildTopNavBar("Mobile recharge"),
			Content: &layoutPb.RechargePlansScreen_RechargePlansContent{
				BgColor:                  widget.GetBlockBackgroundColour("#FFFFFF"),
				RechargeInfoCard:         rechargeInfoSection,
				SearchBar:                uiBuilder.BuildSearchBar("Search for a Plan like ₹456 or 4 days"),
				PlanSegments:             planSegments,
				DefaultSelectedSegmentId: defaultSegmentId,
				SegmentsUnderlineColor:   widget.GetBlockBackgroundColour("#E6E9ED"),
			},
		},
		OperatorId: operatorId,
	}, nil
}

// buildRechargeInfoSection builds the account info section from the backend response
func (s *Service) buildRechargeInfoSection(req *billpay.GetRechargePlansScreenRequest, frpRes *billpayPb.FetchRechargePlansResponse) *sections.Section {
	// Get operator name from first plan's ServiceProvider if available
	var operatorName string
	if len(frpRes.GetPlans()) > 0 {
		operatorName = frpRes.GetPlans()[0].GetServiceProvider()
	} else {
		operatorName = ""
	}

	// Get the operator logo URL based on the operator enum
	operatorLogoURL := getOperatorLogoURL(frpRes.GetOperator())

	// Build account info model
	accountInfoModel := &models.RechargeAccountInfo{
		ImageUrl:          operatorLogoURL,
		Operator:          operatorName,
		AccountIdentifier: req.GetAccountIdentifier(),
		AccountType:       "",
		State:             "",
	}

	return uiBuilder.BuildAccountInfoCard(accountInfoModel)
}

// buildPlanSegments groups plans by PlanName and builds the plan segments
func (s *Service) buildPlanSegments(frpRes *billpayPb.FetchRechargePlansResponse) ([]*layoutPb.RechargePlanSegment, string) {
	// Group plans by PlanName to create segments
	planGroups := s.groupPlansByName(frpRes.GetPlans())

	// Build plan segments from grouped plans
	var planSegments []*layoutPb.RechargePlanSegment
	var defaultSegmentId string

	for planName, plans := range planGroups {
		segment := s.buildPlanSegment(planName, plans)
		if defaultSegmentId == "" {
			defaultSegmentId = segment.Id // Set first segment as default
		}
		planSegments = append(planSegments, segment)
	}

	return planSegments, defaultSegmentId
}

// groupPlansByName groups plans by their PlanName field
func (s *Service) groupPlansByName(plans []*billpayPb.MobileRechargePlan) map[string][]*billpayPb.MobileRechargePlan {
	planGroups := make(map[string][]*billpayPb.MobileRechargePlan)

	for _, plan := range plans {
		planName := plan.GetPlanName()
		if planName == "" {
			planName = "Other" // Default group for plans without names
		}
		planGroups[planName] = append(planGroups[planName], plan)
	}

	return planGroups
}

// buildPlanSegment builds a single plan segment from a group of plans
func (s *Service) buildPlanSegment(planName string, plans []*billpayPb.MobileRechargePlan) *layoutPb.RechargePlanSegment {
	// Create segment ID from plan name
	segmentId := strings.ToLower(strings.ReplaceAll(planName, " ", "_"))

	// Build plan cards for this segment
	var planCards []*layoutPb.RechargePlanCard
	for _, plan := range plans {
		planCard := s.buildPlanCard(plan)
		planCards = append(planCards, planCard)
	}

	// Create segment
	return &layoutPb.RechargePlanSegment{
		Id: segmentId,
		DefaultDisplayText: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: planName},
			FontColor:    "#929599",
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
		},
		SelectedDisplayText: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: planName},
			FontColor:    "#00B899",
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_4},
		},
		SelectedUnderlineColor: widget.GetBlockBackgroundColour("#00B899"),
		Plans:                  planCards,
	}
}

// buildPlanCard builds a single plan card from a mobile recharge plan
func (s *Service) buildPlanCard(plan *billpayPb.MobileRechargePlan) *layoutPb.RechargePlanCard {
	// Extract data from plan description
	dataValue := extractDataFromDescription(plan.GetPlanDescription())

	// Build plan info with data if available
	infos := []models.KeyValuePair{
		{Key: "VALIDITY", Value: plan.GetValidity()},
		{Key: "TALKTIME", Value: plan.GetTalktime()},
	}

	// Add data info if extracted
	if dataValue != "" {
		infos = append(infos, models.KeyValuePair{Key: "DATA", Value: dataValue})
	}

	// Build plan model for this specific plan
	planModel := &models.RechargePlanDetails{
		Amount:       plan.GetAmount(),
		Tag:          nil, // Set tag based on plan properties if needed (eg: LIMITED TIME)
		Infos:        infos,
		FooterBanner: nil, // Set if there's a promotional banner
	}

	// Build plan card section
	planCardSection := uiBuilder.BuildRechargePlanSection(planModel)

	// Create plan card for the layout
	return &layoutPb.RechargePlanCard{
		PlanId:      plan.GetPlanId(),
		CardSection: planCardSection,
		SearchKeywords: []string{
			strings.ToLower(plan.GetTalktime()),
			strings.ToLower(plan.GetValidity()),
			strings.ToLower(formatCurrency(plan.GetAmount())),
		},
	}
}

func (s *Service) GetBillDetailsConfirmationScreen(ctx context.Context, req *billpay.GetBillDetailsConfirmationScreenRequest) (*billpay.GetBillDetailsConfirmationScreenResponse, error) {
	var (
		actorId = req.GetReq().GetAuth().GetActorId()
		res     = &billpay.GetBillDetailsConfirmationScreenResponse{
			RespHeader: &header.ResponseHeader{},
		}
	)

	// Currently there's no vendor API to fetch the details of a specific plan.
	// So we fetch all plans and filter it using the selected plan's id
	// Extract recharge parameters from request
	rechargeParams := req.GetRechargeParams()
	if rechargeParams == nil {
		res.RespHeader.Status = rpc.StatusInvalidArgument()
		return res, nil
	}

	// Map account type from frontend to backend enum
	backendAccountType := billpayEnums.RechargeAccountType(billpayEnums.RechargeAccountType_value[rechargeParams.GetAccountType()])
	if backendAccountType == billpayEnums.RechargeAccountType_RECHARGE_ACCOUNT_TYPE_UNSPECIFIED {
		res.RespHeader.Status = rpc.StatusInvalidArgument()
		return res, nil
	}

	// Map frontend request to backend request to fetch all plans
	backendReq := &billpayPb.FetchRechargePlansRequest{
		ActorId:           actorId,
		AccountType:       backendAccountType,
		AccountIdentifier: rechargeParams.GetAccountIdentifier(),
	}

	// Call backend service to fetch all plans
	frpRes, err := s.billpayClient.FetchRechargePlans(ctx, backendReq)
	if rpcErr := epifigrpc.RPCError(frpRes, err); rpcErr != nil {
		logger.Error(ctx, "error while calling billpay FetchRechargePlans RPC", zap.Error(rpcErr))
		return &billpay.GetBillDetailsConfirmationScreenResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternal(),
			},
		}, nil
	}

	// Find the specific plan with the matching PlanId
	var selectedPlan *billpayPb.MobileRechargePlan
	targetPlanId := rechargeParams.GetPlanId()
	for _, plan := range frpRes.GetPlans() {
		if plan.GetPlanId() == targetPlanId {
			selectedPlan = plan
			break
		}
	}

	// If plan not found, return error
	if selectedPlan == nil {
		logger.Error(ctx, "plan not found", zap.String("planId", targetPlanId))
		return &billpay.GetBillDetailsConfirmationScreenResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInvalidArgument(),
			},
		}, nil
	}

	// Get operator name from the selected plan or from the response
	var operatorName string
	if selectedPlan.GetServiceProvider() != "" {
		operatorName = selectedPlan.GetServiceProvider()
	} else {
		operatorName = frpRes.GetOperator().String()
	}

	// Build bill details section using the selected plan
	billDetailsModel := &models.BillDetails{
		Title: "Bill details",
		Infos: []models.KeyValuePair{
			{Key: "Service Provider", Value: operatorName},
			{Key: "Number", Value: rechargeParams.GetAccountIdentifier()},
			{Key: "Amount", Value: formatCurrency(selectedPlan.GetAmount())},
			{Key: "Plan", Value: selectedPlan.GetPlanName()},
			{Key: "Validity", Value: selectedPlan.GetValidity()},
			{Key: "Talktime", Value: selectedPlan.GetTalktime()},
		},
		Description: selectedPlan.GetPlanDescription(),
	}
	billDetailsSection := uiBuilder.BuildBillDetailsComponent(billDetailsModel)

	return &billpay.GetBillDetailsConfirmationScreenResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		Layout: &layoutPb.BillDetailsConfirmationScreen{
			BgColor:            widget.GetBlockBackgroundColour("#F6F9FD"),
			TopNavBar:          uiBuilder.BuildTopNavBar("Pay Mobile Recharge bill"),
			BillDetailsSection: billDetailsSection,
			AmountContainer: &layoutPb.BillDetailsConfirmationScreen_AmountContainer{
				BgColor: widget.GetBlockBackgroundColour("#EFF2F6"),
				Title: &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{PlainString: "PAY TOTAL"},
					FontColor:    "#6A6D70",
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_S_CAPS},
				},
				Amount: typesv2.GetFromBeMoney(selectedPlan.GetAmount()),
			},
			Cta: &deeplink.Cta{
				Type:         deeplink.Cta_CONTINUE,
				Text:         "Pay " + formatCurrency(selectedPlan.GetAmount()),
				DisplayTheme: deeplink.Cta_PRIMARY,
			},
		},
		ClientRequestId: "", // client request ID is generated from BE when client calls CreateRechargeOrder, hence passing empty
		PaymentParams: &billpay.GetBillDetailsConfirmationScreenResponse_RechargeParams{
			RechargeParams: rechargeParams,
		},
	}, nil
}

// formatCurrency formats a Money object as a currency string, showing decimals only if non-zero
func formatCurrency(amount *moneyPb.Money) string {
	// Handle nil case
	if amount == nil {
		return money.ToDisplayStringInIndianFormat(money.ZeroINR().GetPb(), 0, true)
	}

	value, precision := money.GetDisplayStringWithValueAndPrecision(amount, 2, false, true, money.IndianNumberSystem)

	// Combine value and precision (precision will be empty if decimals are zero)
	return value + precision
}

// extractDataFromDescription extracts data value from plan description
// Returns the data amount as a string (e.g., "10 GB", "1 GB", "2.5 GB/Day", "Unlimited") or empty string if not found
func extractDataFromDescription(description string) string {
	if description == "" {
		return ""
	}

	// Check for unlimited data patterns
	if unlimitedDataRegex.MatchString(description) {
		return "Unlimited"
	}

	// Look for data with /Day pattern first (e.g., "2.5GB/Day")
	if matches := dailyDataRegex.FindStringSubmatch(description); len(matches) >= 3 {
		return matches[1] + " " + strings.ToUpper(matches[2]) + "/Day"
	}

	// Look for any number followed by GB/MB/TB
	if matches := dataRegex.FindStringSubmatch(description); len(matches) >= 3 {
		return matches[1] + " " + strings.ToUpper(matches[2])
	}

	return ""
}
