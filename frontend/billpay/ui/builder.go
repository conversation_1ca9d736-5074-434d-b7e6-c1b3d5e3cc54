package ui

import (
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/anypb"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	widgetPb "github.com/epifi/be-common/api/typesv2/common/ui/widget"

	"github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/frontend/deeplink"
	layoutPb "github.com/epifi/gamma/api/typesv2/billpay"
	pkgScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/pkg"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/behaviors"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/sections"
	"github.com/epifi/gamma/frontend/billpay/models"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
)

// BuildRechargePlanSection builds the SDUI section for the BillPay pack details card.
func BuildRechargePlanSection(model *models.RechargePlanDetails) *sections.Section {
	// Inner vertical section: first 4 components with 16px padding
	divider := &components.Spacer{
		SpacingValue: 0,
		Separator: components.NewSeparator().
			WithBackgroundColor(widgetPb.GetBlockBackgroundColour("#E6E9ED")).
			WithThickness(1),
	}
	innerSection := &sections.VerticalListSection{
		IsScrollable: false,
		Components: []*components.Component{
			rechargePlanCardTopRow(model.Amount, model.Tag),
			{Content: getAnyWithoutError(&components.Spacer{SpacingValue: components.Spacing_SPACING_M})},
			{Content: getAnyWithoutError(divider)},
			{Content: getAnyWithoutError(&components.Spacer{SpacingValue: components.Spacing_SPACING_S})},
			keyValuePairsComponent(model.Infos),
			{Content: getAnyWithoutError(&components.Spacer{SpacingValue: components.Spacing_SPACING_S})},
			viewDetailsCta(model.PlanDetailsBottomSheet),
		},
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: &properties.ContainerProperty{
						Padding: &properties.PaddingProperty{
							Top: 16, Bottom: 16, Left: 16, Right: 16,
						},
						Size: &properties.Size{
							Width: &properties.Size_Dimension{Type: properties.Size_Dimension_DIMENSION_TYPE_FILL},
						},
					},
				},
			},
		},
		HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
	}

	componentsList := []*components.Component{
		{Content: getAnyWithoutError(innerSection)},
	}

	if model.FooterBanner != nil {
		componentsList = append(componentsList, buildPromoBanner(model.FooterBanner))
	}
	// Outer vertical section: apply background, corner, border here, no left/right padding
	return &sections.Section{
		Content: &sections.Section_VerticalListSection{
			VerticalListSection: &sections.VerticalListSection{
				IsScrollable: false,
				Components:   componentsList,
				VisualProperties: []*properties.VisualProperty{
					{
						Properties: &properties.VisualProperty_ContainerProperty{
							ContainerProperty: &properties.ContainerProperty{
								BgColor: widgetPb.GetBlockBackgroundColour("#FFFFFF"),
								Corner: &properties.CornerProperty{
									TopLeftCornerRadius: 16, TopRightCornerRadius: 16, BottomLeftCorner: 16, BottomRightCorner: 16,
								},
								Border: &properties.BorderProperty{
									BorderColor: "#E6E9ED", BorderThickness: 1, CornerRadius: 16,
								},
								Margin: &properties.PaddingProperty{ // Extra margin to account for the border on all edges
									Left:   1,
									Top:    1,
									Right:  1,
									Bottom: 1,
								},
							},
						},
					},
				},
			},
		},
	}
}

// rechargePlanCardTopRow builds the top row: amount, tag, arrow icon.
func rechargePlanCardTopRow(amount *moneyPb.Money, tag *string) *components.Component {
	componentsList := []*components.Component{
		{
			Content: getAnyWithoutError(
				commontypes.GetTextFromStringFontColourFontStyle(
					formatCurrency(amount),
					"#313234",
					commontypes.FontStyle_NUMBER_L,
				),
			),
		},
		{Content: getAnyWithoutError(&components.Spacer{SpacingValue: components.Spacing_SPACING_XS})},
	}

	// Only add the tag component if tag is not nil
	if tag != nil {
		componentsList = append(componentsList, &components.Component{
			Content: getAnyWithoutError(
				&ui.IconTextComponent{
					Texts: []*commontypes.Text{
						commontypes.GetTextFromStringFontColourFontStyle(*tag, "#37522A", commontypes.FontStyle_OVERLINE_2XS_CAPS),
					},
					ContainerProperties: &ui.IconTextComponent_ContainerProperties{
						BackgroundColour: widgetPb.GetBlockBackgroundColour("#D5E6CE"),
						CornerRadius:     12,
						LeftPadding:      8, RightPadding: 8, TopPadding: 2, BottomPadding: 2,
					},
				},
			),
		})
	}

	leftGroup := &sections.HorizontalListSection{
		IsScrollable:          false,
		Components:            componentsList,
		HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_START,
	}

	return &components.Component{
		Content: getAnyWithoutError(
			&sections.HorizontalListSection{
				IsScrollable: false,
				Components: []*components.Component{
					{Content: getAnyWithoutError(leftGroup)},
					{
						Content: getAnyWithoutError(
							commontypes.GetVisualElementFromUrlHeightAndWidth(
								"https://epifi-icons.pointz.in/pay/green-circle-right-chevron.png",
								24,
								24,
							),
						),
					},
				},
				VisualProperties: []*properties.VisualProperty{
					{
						Properties: &properties.VisualProperty_ContainerProperty{
							ContainerProperty: &properties.ContainerProperty{
								Size: &properties.Size{
									Width: &properties.Size_Dimension{Type: properties.Size_Dimension_DIMENSION_TYPE_FILL},
								},
							},
						},
					},
				},
				HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_SPACE_BETWEEN,
			},
		),
	}
}

// keyValuePairsComponent builds the info rows: N columns, each with two texts, spaced equally.
func keyValuePairsComponent(infos []models.KeyValuePair) *components.Component {
	var columns []*components.Component
	n := len(infos)
	if n == 0 {
		return nil
	}
	weight := float32(100.0 / float64(n))

	for _, model := range infos {
		titleText := &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: model.Key},
			FontColor:    "#B2B5B9",
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS},
			Alignment:    commontypes.Text_ALIGNMENT_LEFT,
		}
		subtitleText := &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: model.Value},
			FontColor:    "#6A6D70",
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_4},
			Alignment:    commontypes.Text_ALIGNMENT_LEFT,
		}
		verticalSection := &sections.VerticalListSection{
			IsScrollable: false,
			Components: []*components.Component{
				{Content: getAnyWithoutError(titleText)},
				{Content: getAnyWithoutError(subtitleText)},
			},
			HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
			VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_CENTER,
		}
		wrapped := &components.Component{
			Content: getAnyWithoutError(
				&sections.HorizontalListSection{
					IsScrollable:          false,
					Components:            []*components.Component{{Content: getAnyWithoutError(verticalSection)}},
					HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_START,
					VisualProperties: []*properties.VisualProperty{
						{
							Properties: &properties.VisualProperty_ContainerProperty{
								ContainerProperty: &properties.ContainerProperty{
									Size: &properties.Size{
										Width: &properties.Size_Dimension{
											Type:   properties.Size_Dimension_DIMENSION_TYPE_WEIGHT,
											Weight: &properties.Weight{Value: weight},
										},
									},
								},
							},
						},
					},
				},
			),
		}
		columns = append(columns, wrapped)
	}

	return &components.Component{
		Content: getAnyWithoutError(
			&sections.HorizontalListSection{
				IsScrollable:          false,
				Components:            columns,
				HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_START,
				VisualProperties: []*properties.VisualProperty{
					{
						Properties: &properties.VisualProperty_ContainerProperty{
							ContainerProperty: &properties.ContainerProperty{
								Size: &properties.Size{
									Width: &properties.Size_Dimension{
										Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
									},
								},
							},
						},
					},
				},
			},
		),
	}
}

// viewDetailsCta builds the ITC with a deeplink to a bottom sheet.
func viewDetailsCta(bottomSheetModel models.RechargePlanDetailBottomSheet) *components.Component {
	bottomSheetSection := &sections.Section{
		Content: &sections.Section_VerticalListSection{
			VerticalListSection: planDetailsBottomSheet(bottomSheetModel),
		},
	}
	bottomSheetDeeplink := &deeplink.Deeplink{
		Screen: deeplink.Screen_SDUI_BOTTOM_SHEET,
		ScreenOptionsV2: deeplinkV3.GetScreenOptionV2WithoutError(&pkgScreenOptionsPb.SduiBottomSheetOptions{
			Section: bottomSheetSection,
		}),
	}
	return &components.Component{
		Content: getAnyWithoutError(
			&sections.HorizontalListSection{
				IsScrollable: false,
				Components: []*components.Component{
					{
						Content: getAnyWithoutError(
							&ui.IconTextComponent{
								Texts: []*commontypes.Text{
									commontypes.GetTextFromStringFontColourFontStyle(
										"VIEW DETAILS",
										"#00B899",
										commontypes.FontStyle_BUTTON_XS,
									),
								},
							},
						),
					},
				},
				HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_START,
				InteractionBehaviors: []*behaviors.InteractionBehavior{
					{
						Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
							OnClickBehavior: &behaviors.OnClickBehavior{
								Action: getAnyWithoutError(bottomSheetDeeplink),
							},
						},
					},
				},
			},
		),
	}
}

// buildPromoBanner builds the promo banner component.
func buildPromoBanner(model *models.PromoBanner) *components.Component {
	return &components.Component{
		Content: getAnyWithoutError(
			&sections.HorizontalListSection{
				IsScrollable: false,
				Components: []*components.Component{
					{
						Content: getAnyWithoutError(
							model.Itc,
						),
					},
				},
				VisualProperties: []*properties.VisualProperty{
					{
						Properties: &properties.VisualProperty_ContainerProperty{
							ContainerProperty: &properties.ContainerProperty{
								BgColor: model.BgColor,
								Size: &properties.Size{
									Width: &properties.Size_Dimension{Type: properties.Size_Dimension_DIMENSION_TYPE_FILL},
								},
							},
						},
					},
				},
			},
		),
	}
}

// BuildAccountInfoCard builds a section containing the account info displayed above plans in recharge plans screen
func BuildAccountInfoCard(model *models.RechargeAccountInfo) *sections.Section {
	return &sections.Section{
		Content: &sections.Section_HorizontalListSection{
			HorizontalListSection: &sections.HorizontalListSection{
				IsScrollable: false,
				Components: []*components.Component{
					{
						Content: getAnyWithoutError(commontypes.GetVisualElementFromUrlHeightAndWidth(model.ImageUrl, 40, 40)),
					},
					{Content: getAnyWithoutError(&components.Spacer{SpacingValue: components.Spacing_SPACING_M})},
					{
						Content: getAnyWithoutError(
							&sections.VerticalListSection{
								IsScrollable: false,
								Components: []*components.Component{
									{
										Content: getAnyWithoutError(commontypes.
											GetTextFromStringFontColourFontStyleFontAlignment(
												model.Operator,
												"#6A6D70",
												commontypes.FontStyle_BODY_4,
												commontypes.Text_ALIGNMENT_LEFT,
											),
										),
									},
									{
										Content: getAnyWithoutError(commontypes.
											GetTextFromStringFontColourFontStyleFontAlignment(
												model.AccountIdentifier,
												"#313234",
												commontypes.FontStyle_NUMBER_M,
												commontypes.Text_ALIGNMENT_LEFT,
											).WithMaxLines(1),
										),
									},
								},
								HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
								VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_CENTER,
							},
						),
					},
				},
				VisualProperties: []*properties.VisualProperty{
					{
						Properties: &properties.VisualProperty_ContainerProperty{
							ContainerProperty: &properties.ContainerProperty{
								BgColor: &widgetPb.BackgroundColour{
									Colour: &widgetPb.BackgroundColour_LinearGradient{
										LinearGradient: &widgetPb.LinearGradient{
											Degree: 90,
											LinearColorStops: []*widgetPb.ColorStop{
												{Color: "#FFFFFF", StopPercentage: 0},
												{Color: "#EBEBEB", StopPercentage: 100},
											},
										},
									},
								},
								Padding: &properties.PaddingProperty{
									Top: 0, Bottom: 0, Left: 16, Right: 16,
								},
								Size: &properties.Size{
									Height: &properties.Size_Dimension{Type: properties.Size_Dimension_DIMENSION_TYPE_EXACT, ExactValue: 78},
									Width:  &properties.Size_Dimension{Type: properties.Size_Dimension_DIMENSION_TYPE_FILL},
								},
								Corner: &properties.CornerProperty{
									TopLeftCornerRadius: 16, TopRightCornerRadius: 16, BottomLeftCorner: 16, BottomRightCorner: 16,
								},
								Border: &properties.BorderProperty{
									BorderColor: "#EFF2F6", BorderThickness: 1, CornerRadius: 16,
								},
								Margin: &properties.PaddingProperty{ // Extra margin to account for the border on all edges
									Left:   16,
									Top:    1,
									Right:  16,
									Bottom: 1,
								},
							},
						},
					},
				},
				HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_START,
				VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_CENTER_VERTICALLY,
			},
		},
	}
}

// planDetailsBottomSheet returns the SDUI component for the recharge plan details bottom sheet.
func planDetailsBottomSheet(model models.RechargePlanDetailBottomSheet) *sections.VerticalListSection {
	amount := &commontypes.Text{
		DisplayValue: &commontypes.Text_PlainString{PlainString: formatCurrency(model.Amount)},
		FontColor:    "#313234",
		FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_NUMBER_L},
		Alignment:    commontypes.Text_ALIGNMENT_LEFT,
	}
	divider := &components.Spacer{
		SpacingValue: 0,
		Separator:    components.NewSeparator().WithBackgroundColor(widgetPb.GetBlockBackgroundColour("#E6E9ED")).WithThickness(1),
	}
	planDetailsRow := keyValuePairsComponent(model.Infos)
	sectionTitle := &commontypes.Text{
		DisplayValue: &commontypes.Text_PlainString{PlainString: "PLAN DETAILS"},
		FontColor:    "#313234",
		FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_XS},
		Alignment:    commontypes.Text_ALIGNMENT_LEFT,
	}
	description := &commontypes.Text{
		DisplayValue: &commontypes.Text_PlainString{PlainString: model.PlanDetails},
		FontColor:    "#6A6D70",
		FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_4},
		Alignment:    commontypes.Text_ALIGNMENT_LEFT,
	}
	return &sections.VerticalListSection{
		IsScrollable: false,
		Components: []*components.Component{
			{Content: getAnyWithoutError(amount)},
			{Content: getAnyWithoutError(&components.Spacer{SpacingValue: components.Spacing_SPACING_S})},
			{Content: getAnyWithoutError(divider)},
			{Content: getAnyWithoutError(&components.Spacer{SpacingValue: components.Spacing_SPACING_S})},
			planDetailsRow,
			{Content: getAnyWithoutError(&components.Spacer{SpacingValue: components.Spacing_SPACING_L})},
			{Content: getAnyWithoutError(sectionTitle)},
			{Content: getAnyWithoutError(&components.Spacer{SpacingValue: components.Spacing_SPACING_XXS})},
			{Content: getAnyWithoutError(description)},
		},
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: &properties.ContainerProperty{
						Padding: &properties.PaddingProperty{
							Top: 12, Bottom: 32, Left: 12, Right: 12,
						},
						Size: &properties.Size{
							Width: &properties.Size_Dimension{Type: properties.Size_Dimension_DIMENSION_TYPE_FILL},
						},
					},
				},
			},
		},
		HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
	}
}

// BuildBillDetailsComponent builds the bill details card section.
func BuildBillDetailsComponent(model *models.BillDetails) *sections.Section {
	titleComponent := &commontypes.Text{
		DisplayValue: &commontypes.Text_PlainString{PlainString: "Bill details"},
		FontColor:    "#6A6D70",
		FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
		Alignment:    commontypes.Text_ALIGNMENT_CENTER,
	}
	spacer1 := &components.Spacer{SpacingValue: components.Spacing_SPACING_M}
	divider := &components.Spacer{
		SpacingValue: 0,
		Separator:    components.NewSeparator().WithBackgroundColor(widgetPb.GetBlockBackgroundColour("#F6F9FD")).WithThickness(2),
	}
	spacer2 := &components.Spacer{SpacingValue: components.Spacing_SPACING_M}
	var detailRows []*components.Component
	for i, d := range model.Infos {
		label := &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: d.Key},
			FontColor:    "#929599",
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			Alignment:    commontypes.Text_ALIGNMENT_LEFT,
		}
		value := &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: d.Value},
			FontColor:    "#313234",
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
			Alignment:    commontypes.Text_ALIGNMENT_RIGHT,
		}
		rowComponent := &components.Component{
			Content: getAnyWithoutError(
				&sections.HorizontalListSection{
					IsScrollable: false,
					Components: []*components.Component{
						{Content: getAnyWithoutError(label)},
						{Content: getAnyWithoutError(value)},
					},
					HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_SPACE_BETWEEN,
				},
			),
		}
		detailRows = append(detailRows, rowComponent)
		if i < len(model.Infos)-1 {
			detailRows = append(detailRows, &components.Component{
				Content: getAnyWithoutError(&components.Spacer{SpacingValue: components.Spacing_SPACING_S}),
			})
		}
	}
	return &sections.Section{
		Content: &sections.Section_VerticalListSection{
			VerticalListSection: &sections.VerticalListSection{
				IsScrollable: false,
				Components: append(
					[]*components.Component{
						{Content: getAnyWithoutError(titleComponent)},
						{Content: getAnyWithoutError(spacer1)},
						{Content: getAnyWithoutError(divider)},
						{Content: getAnyWithoutError(spacer2)},
					},
					detailRows...,
				),
				VisualProperties: []*properties.VisualProperty{
					{
						Properties: &properties.VisualProperty_ContainerProperty{
							ContainerProperty: &properties.ContainerProperty{
								Padding: &properties.PaddingProperty{
									Top: 20, Bottom: 20, Left: 20, Right: 20,
								},
								Corner: &properties.CornerProperty{
									TopLeftCornerRadius: 20, TopRightCornerRadius: 20, BottomLeftCorner: 20, BottomRightCorner: 20,
								},
								Border: &properties.BorderProperty{
									BorderColor: "#E6E9ED", BorderThickness: 1, CornerRadius: 20,
								},
								Margin: &properties.PaddingProperty{ // Extra margin to account for the border on all sides
									Left:   1,
									Top:    1,
									Right:  1,
									Bottom: 1,
								},
								Size: &properties.Size{
									Height: &properties.Size_Dimension{Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP},
									Width:  &properties.Size_Dimension{Type: properties.Size_Dimension_DIMENSION_TYPE_FILL},
								},
							},
						},
					},
				},
			},
		},
	}
}

// BuildTopNavBar builds a top navigation bar with the given title
func BuildTopNavBar(title string) *layoutPb.TopNavBar {
	return &layoutPb.TopNavBar{
		BgColor: widgetPb.GetBlockBackgroundColour("#18191B"),
		Title: commontypes.GetTextFromStringFontColourFontStyleFontAlignment(
			title,
			"#F6F9FD",
			commontypes.FontStyle_HEADLINE_M,
			commontypes.Text_ALIGNMENT_CENTER,
		),
		RightIcon: nil, // No need to show BBPS logo for recharges, hence nil
	}
}

// BuildSearchBar builds a search bar with the given placeholder text
func BuildSearchBar(placeholder string) *layoutPb.SearchBar {
	return &layoutPb.SearchBar{
		BgColor: widgetPb.GetBlockBackgroundColour("#F6F9FD"),
		Placeholder: commontypes.GetTextFromStringFontColourFontStyleFontAlignment(
			placeholder,
			"#929599",
			commontypes.FontStyle_SUBTITLE_S,
			commontypes.Text_ALIGNMENT_LEFT,
		),
		LeftIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(
			"https://epifi-icons.pointz.in/home/<USER>/search-icon.png",
			24,
			24,
		),
		SearchTextStyle: commontypes.GetTextFromStringFontColourFontStyleFontAlignment(
			"",
			"#313234",
			commontypes.FontStyle_SUBTITLE_S,
			commontypes.Text_ALIGNMENT_LEFT,
		),
	}
}

// formatCurrency formats a Money object as a currency string, showing decimals only if non-zero
func formatCurrency(amount *moneyPb.Money) string {
	// Handle nil case
	if amount == nil {
		return money.ToDisplayStringInIndianFormat(money.ZeroINR().GetPb(), 0, true)
	}

	value, precision := money.GetDisplayStringWithValueAndPrecision(amount, 2, false, true, money.IndianNumberSystem)

	// Combine value and precision (precision will be empty if decimals are zero)
	return value + precision
}

func getAnyWithoutError(msg proto.Message) *anypb.Any {
	res, _ := anypb.New(msg)
	return res
}
