// nolint:funlen,protogetter
package card

import (
	"context"
	"fmt"
	"math"
	"strings"

	"github.com/pkg/errors"
	"google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	widgetUi "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifigrpc"
	pkggenconf "github.com/epifi/be-common/pkg/frontend/app/genconf"
	"github.com/epifi/be-common/pkg/idgen"
	moneyPb "github.com/epifi/be-common/pkg/money"
	"github.com/epifi/gamma/pkg/accrual"

	accountPb "github.com/epifi/gamma/api/accounts"
	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	accountBalanceEnums "github.com/epifi/gamma/api/accounts/balance/enums"
	"github.com/epifi/gamma/api/bankcust"
	beCardPb "github.com/epifi/gamma/api/card"
	cardProvPb "github.com/epifi/gamma/api/card/provisioning"
	fePb "github.com/epifi/gamma/api/frontend/card"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/firefly"
	kycPb "github.com/epifi/gamma/api/kyc"
	vkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/behaviors"
	cardPkg "github.com/epifi/gamma/pkg/card"
	webFeTravel "github.com/epifi/gamma/webfe/travel"

	types "github.com/epifi/gamma/api/typesv2"
	dcScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/debitcard"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/sections"
	sduiHelper "github.com/epifi/gamma/frontend/card/dashboard_sections"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/firefly/helper"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
)

// DC international widget struct
type getDCInternationalWidgetHelperModel struct {
	isWidgetToBeDisplayed        bool
	CurrencyHeaderText           *commontypes.Text
	CurrencyValue                *ui.IconTextComponent
	CurrencySubtext              *commontypes.Text
	CurrencyDescriptionFooter    *ui.IconTextComponent
	TopSectionBackgroundVisual   *commontypes.VisualElement
	MidSectionCtas               []*ui.IconTextComponent
	MidSectionBackground         *widgetUi.BackgroundColour
	CurrencySelectionCta         *ui.IconTextComponent
	BottomITC                    *ui.IconTextComponent
	BottomSectionBackground      *widgetUi.BackgroundColour
	BottomSectionBackGroundState cardProvPb.DcInternationalWidgetDetails_BottomSectionBackgroundState
}

func (s *Service) getRequestNewCardLandingScreenDeeplink(ctx context.Context, cardDetail *beCardPb.Card, vkycSummary *vkycPb.VKYCSummary) (*deeplink.Deeplink, error) {
	ctaInfo, err := s.getRequestNewCardScreenCtaInfo(ctx, cardDetail, vkycSummary)
	if err != nil {
		return nil, fmt.Errorf("error in getting cta info for request new card scrren, %w", err)
	}

	maskedCardNumber := cardDetail.GetBasicInfo().GetMaskedCardNumber()
	if len(maskedCardNumber) < 4 {
		return nil, fmt.Errorf("invalid length of maskedCardNumber %d", len(maskedCardNumber))
	}
	screen := deeplink.Screen_DEBIT_CARD_REQUEST_NEW_CARD_SCREEN
	screenOptions := &dcScreenOptionsPb.DebitCardRequestNewCardScreenOptions{
		VisualElementTitleSubtitleElement: &widgetUi.VisualElementTitleSubtitleElement{
			TitleText: &commontypes.Text{
				FontColor:    "#333333",
				DisplayValue: &commontypes.Text_PlainString{PlainString: "New Card Request"},
				FontStyle: &commontypes.Text_CustomFontStyle{
					CustomFontStyle: &commontypes.FontStyleInfo{
						FontFamily: "Gilroy",
						FontStyle:  "normal",
						FontSize:   "12",
					},
				},
			},
			SubtitleText: &commontypes.Text{
				FontColor: "#333333",
				DisplayValue: &commontypes.Text_PlainString{
					PlainString: fmt.Sprintf("This will permanently deactivate your card ending with %s and a new card will be sent to you in 3-10 days. Your first replacement card is free of charge, following which you will be charged ₹199 + GST for a new card.", maskedCardNumber[len(maskedCardNumber)-4:]),
				},
				FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_3_PARA},
			},
			VisualElement: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source: &commontypes.VisualElement_Image_Url{Url: RequestNewCardScreenImageUrl},
						Properties: &commontypes.VisualElementProperties{
							Width:  240,
							Height: 178,
						},
						ImageType: commontypes.ImageType_PNG,
					},
				},
			},
		},
		RequestNewCardReason: &dcScreenOptionsPb.RequestNewCardReason{
			Placeholder: &commontypes.Text{
				FontColor:    "#8D8D8D",
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Tell us what happened"},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_1},
			},
			SelectReasonBottomSheet: &dcScreenOptionsPb.RequestNewCardReason_DropDownBottomSheet{
				Title: &commontypes.Text{
					FontColor:    "#333333",
					DisplayValue: &commontypes.Text_PlainString{PlainString: "Tell us what happened"},
					FontStyle: &commontypes.Text_CustomFontStyle{
						CustomFontStyle: &commontypes.FontStyleInfo{
							FontFamily: "Gilroy",
							FontStyle:  "normal",
							FontSize:   "14",
						},
					},
				},
				Options: []*dcScreenOptionsPb.RequestNewCardReason_Option{
					{
						Id: 1,
						DisplayString: &commontypes.Text{
							FontColor:    "#333333",
							DisplayValue: &commontypes.Text_PlainString{PlainString: "My card is lost/stolen"},
							FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_3},
						},
					},
					{
						Id: 2,
						DisplayString: &commontypes.Text{
							FontColor:    "#333333",
							DisplayValue: &commontypes.Text_PlainString{PlainString: "My card is damaged"},
							FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_3},
						},
					},
					{
						Id: 3,
						DisplayString: &commontypes.Text{
							FontColor:    "#333333",
							DisplayValue: &commontypes.Text_PlainString{PlainString: "I’m having transaction issues"},
							FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_3},
						},
					},
					{
						Id: 4,
						DisplayString: &commontypes.Text{
							FontColor:    "#333333",
							DisplayValue: &commontypes.Text_PlainString{PlainString: "Other (I’ll write my reason)"},
							FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_3},
						},
					},
				},
			},
		},
		Cta: &deeplink.Cta{
			Text:     "NEXT",
			Deeplink: ctaInfo,
		},
	}
	if pkggenconf.IsFeatureEnabledOnPlatform(ctx, s.cardDynamicConf.EnableDcCardRenewalChargesFlow()) {
		screenOptions.VisualElementTitleSubtitleElement.SubtitleText = &commontypes.Text{
			FontColor: "#333333",
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: fmt.Sprintf("This will permanently deactivate your card ending with %s and a new card will be created.", maskedCardNumber[len(maskedCardNumber)-4:]),
			},
			FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_3_PARA},
		}
	}
	return deeplinkV3.GetDeeplinkV3(screen, screenOptions)
}

func (s *Service) getCardRenewalTypeSelectionScreenDeeplink(ctx context.Context, cardDetails *beCardPb.Card, vkycSummary *vkycPb.VKYCSummary) (*deeplink.Deeplink, error) {
	screen := deeplink.Screen_DC_CARD_RENEWAL_TYPE_SELECTION_SCREEN
	screenOptions := &dcScreenOptionsPb.DcCardRenewalTypeSelectionScreenOptions{
		ToolBar: &firefly.WrappedIconTextToolBar{
			BackArrowColor: "#646464",
			Properties: &firefly.LayoutProperties{
				Size: &firefly.Size{Width: -1, Height: -2},
			},
		},
		Title:           commontypes.GetTextFromStringFontColourFontStyle("Choose a replacement card", "#313234", commontypes.FontStyle_HEADLINE_L),
		BackgroundColor: widgetUi.GetBlockBackgroundColour("#EFF2F6"),
	}

	// get card renewal items for actor
	cardRenewalItems, err := s.getCardRenewalItems(ctx, cardDetails, vkycSummary)
	if err != nil {
		return nil, fmt.Errorf("error in getCardRenewalItems: %w", err)
	}
	screenOptions.CardRenewalItems = cardRenewalItems
	return deeplinkV3.GetDeeplinkV3(screen, screenOptions)
}

func (s *Service) getCardRenewalItems(ctx context.Context, cardDetails *beCardPb.Card, vkycSummary *vkycPb.VKYCSummary) ([]*dcScreenOptionsPb.CardRenewalItem, error) {
	totalAmountToBePaid, displayAmount, err := s.getCardRenewalChargesInfoForUser(ctx, cardDetails.GetActorId())
	if err != nil {
		return nil, fmt.Errorf("error in getting physical card charges info fro user: %w", err)
	}

	digitalCardRenewalItem := s.getRenewalItemForDigitalCardSelectionType(ctx, cardDetails)
	physicalCardRenewalItem := s.getRenewalItemForPhysicalCardSelectionType(ctx, cardDetails, displayAmount)

	// min balance check for physical card
	isSufficientBalanceCheckEnabled := s.checkFeatureReleaseConstraints(ctx, types.Feature_PHYSICAL_DEBIT_CARD_CHARGES_PAYMENT_OPTIONS_SCREEN, cardDetails.GetActorId())
	if !isSufficientBalanceCheckEnabled {
		// fetch account balance
		isBalanceSufficient, balErr := s.checkSufficientBalance(ctx, cardDetails.GetSavingsAccountId(), cardDetails.GetActorId(), totalAmountToBePaid.GetBeMoney())
		if balErr != nil {
			return nil, fmt.Errorf("error while doing sufficient balance check: %w", balErr)
		}

		// if user does not have sufficient balance in account to place request for physical debit card replacement then, disable physical card renewal component
		if !isBalanceSufficient {
			physicalCardRenewalItem.PrimaryCta.Status = deeplink.Cta_CTA_STATUS_DISABLED
			physicalCardRenewalItem.PrimaryCta.Text = "Insufficient Funds"
		}
	}

	if vkycSummary.GetStatus() == vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_REJECTED {
		return []*dcScreenOptionsPb.CardRenewalItem{digitalCardRenewalItem}, nil
	}
	return []*dcScreenOptionsPb.CardRenewalItem{digitalCardRenewalItem, physicalCardRenewalItem}, nil
}

// checkSufficientBalance - checks if user have sufficient balance to place renew card request for physical debit card
func (s *Service) checkSufficientBalance(ctx context.Context, savingsAccountId, actorId string, amount *money.Money) (bool, error) {
	getBalanceResp, getBalanceErr := s.accountBalanceClient.GetAccountBalance(ctx, &accountBalancePb.GetAccountBalanceRequest{
		Identifier: &accountBalancePb.GetAccountBalanceRequest_Id{
			Id: savingsAccountId,
		},
		ActorId:       actorId,
		DataFreshness: accountBalanceEnums.DataFreshness_HISTORICAL,
	})
	if grpcErr := epifigrpc.RPCError(getBalanceResp, getBalanceErr); grpcErr != nil {
		return false, fmt.Errorf("error in fetching account balance for actor %v %w", actorId, grpcErr)
	}
	// insufficient balance handling
	balanceSufficientResp, err := moneyPb.CompareV2(getBalanceResp.GetAvailableBalance(), amount)
	if balanceSufficientResp == -1 || err != nil {
		if err != nil {
			return false, fmt.Errorf("error fetching balance for actor: %s, error: %w", actorId, err)
		}
		return false, nil
	}
	return true, nil
}

func (s *Service) getRenewalItemForDigitalCardSelectionType(_ context.Context, cardDetails *beCardPb.Card) *dcScreenOptionsPb.CardRenewalItem {
	renewalTypeBenefitsInfo := s.getRenewalBenefitsInfoByCardForm(fePb.CardForm_DIGITAL)
	ctaDeeplink, _ := s.getInitiateCardFlowDeeplink(fePb.CardFlowType_CARD_FLOW_TYPE_RENEW_CARD, cardDetails)

	return &dcScreenOptionsPb.CardRenewalItem{
		Title: &ui.IconTextComponent{Texts: []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Digital Debit Card", "#313234", commontypes.FontStyle_SUBTITLE_M)}},
		SubTitle: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringWithCustomFontStyle("FREE", "#98712F", &commontypes.FontStyleInfo{
					FontFamily: "Gilroy",
					FontStyle:  "normal",
					FontSize:   "12",
				}),
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:      "#F6E1C1",
				CornerRadius: 4,
				LeftPadding:  4,
				RightPadding: 4,
			},
		},
		RenewalInfoComponents: renewalTypeBenefitsInfo,
		ContainerDrawableProperties: &firefly.DrawableProperties{
			BgColor: widgetUi.GetBlockBackgroundColour("#FFFFFF"),
			CornerProperty: &firefly.CornerProperty{
				TopStartCornerRadius: 12,
				TopEndCornerRadius:   12,
				BottomStartCorner:    12,
				BottomEndCorner:      12,
			},
		},
		TopSectionVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/card-images/renewal-type-digital-icon.png").
			WithImageType(commontypes.ImageType_PNG).
			WithProperties(&commontypes.VisualElementProperties{Width: 332, Height: 90}),
		TopSectionBackgroundColor: widgetUi.GetBlockBackgroundColour("#D5E6CE"),
		PrimaryCta:                &deeplink.Cta{Type: deeplink.Cta_CONTINUE, DisplayTheme: deeplink.Cta_PRIMARY, Text: "Get a digital card", Deeplink: ctaDeeplink},
		CardForm:                  fePb.CardForm_DIGITAL,
	}
}

func (s *Service) getRenewalItemForPhysicalCardSelectionType(ctx context.Context, cardDetails *beCardPb.Card, amount *types.Money) *dcScreenOptionsPb.CardRenewalItem {
	renewalTypeBenefitsInfo := s.getRenewalBenefitsInfoByCardForm(fePb.CardForm_PHYSICAL)
	amountStr := fmt.Sprintf("%s + GST",
		moneyPb.GetDisplayString(amount.GetBeMoney(), 2, false, true, moneyPb.IndianNumberSystem))
	ctaDeeplink, _ := s.getShippingAddressSelectDeeplinkLandingScreen(ctx, fePb.CardFlowType_CARD_FLOW_TYPE_RENEW_CARD, cardDetails)

	return &dcScreenOptionsPb.CardRenewalItem{
		Title: &ui.IconTextComponent{Texts: []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Physical Debit Card", "#313234", commontypes.FontStyle_SUBTITLE_M)}},
		SubTitle: &ui.IconTextComponent{
			Texts: []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle(amountStr, "#6A6D70", commontypes.FontStyle_NUMBER_S)},
		},
		RenewalInfoComponents: renewalTypeBenefitsInfo,
		ContainerDrawableProperties: &firefly.DrawableProperties{
			BgColor: widgetUi.GetBlockBackgroundColour("#FFFFFF"),
			CornerProperty: &firefly.CornerProperty{
				TopStartCornerRadius: 12,
				TopEndCornerRadius:   12,
				BottomStartCorner:    12,
				BottomEndCorner:      12,
			},
		},
		TopSectionVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/card-images/renewal-type-physical-icon.png").
			WithImageType(commontypes.ImageType_PNG).
			WithProperties(&commontypes.VisualElementProperties{Width: 332, Height: 90}),
		TopSectionBackgroundColor: widgetUi.GetBlockBackgroundColour("#BCDCE7"),
		PrimaryCta:                &deeplink.Cta{Type: deeplink.Cta_CONTINUE, DisplayTheme: deeplink.Cta_PRIMARY, Text: "Continue", Deeplink: ctaDeeplink},
		CardForm:                  fePb.CardForm_PHYSICAL,
	}
}

func (s *Service) getRenewalBenefitsInfoByCardForm(cardForm fePb.CardForm) []*ui.IconTextComponent {
	var (
		renewalTypeBenefitsInfo     = make([]*ui.IconTextComponent, 0)
		renewItemBenefitsInfoConfig []*config.DcCardRenewalTypeBenefitInfo
	)

	switch cardForm {
	case fePb.CardForm_DIGITAL:
		renewItemBenefitsInfoConfig = s.config.Card.DcCardRenewalTypeSelectionScreenConfig.DigitalCardRenewalItem.BenefitsInfo
	case fePb.CardForm_PHYSICAL:
		renewItemBenefitsInfoConfig = s.config.Card.DcCardRenewalTypeSelectionScreenConfig.PhysicalCardRenewalItem.BenefitsInfo
	default:
		return nil
	}

	for _, infoComponent := range renewItemBenefitsInfoConfig {
		renewalTypeBenefitsInfo = append(renewalTypeBenefitsInfo, &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle(accrual.ReplaceCoinWithPointIfApplicable(infoComponent.DisPlayValue, nil), infoComponent.FontColor, commontypes.FontStyle_SUBTITLE_S),
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				CornerRadius:  11,
				BgColor:       infoComponent.BgColor,
				LeftPadding:   8,
				RightPadding:  8,
				TopPadding:    4,
				BottomPadding: 4,
			},
		})
	}
	return renewalTypeBenefitsInfo
}

func (s *Service) getRequestNewCardScreenCtaInfo(ctx context.Context, cardDetails *beCardPb.Card, vkycSummary *vkycPb.VKYCSummary) (*deeplink.Deeplink, error) {
	if pkggenconf.IsFeatureEnabledOnPlatform(ctx, s.cardDynamicConf.EnableDcCardRenewalChargesFlow()) {
		return s.getCardRenewalTypeSelectionScreenDeeplink(ctx, cardDetails, vkycSummary)
	}

	switch cardDetails.GetForm() {
	case beCardPb.CardForm_PHYSICAL:
		return s.getShippingAddressSelectDeeplinkLandingScreen(ctx, fePb.CardFlowType_CARD_FLOW_TYPE_RENEW_CARD, cardDetails)
	case beCardPb.CardForm_DIGITAL:
		return s.getInitiateCardFlowDeeplink(fePb.CardFlowType_CARD_FLOW_TYPE_RENEW_CARD, cardDetails)
	default:
		return nil, fmt.Errorf("no cta found for request new card screen for cardForm: %s", cardDetails.GetForm())
	}
}

// nolint: funlen
func (s *Service) getShippingAddressSelectDeeplinkLandingScreen(ctx context.Context, cardFlowType fePb.CardFlowType, cardDetails *beCardPb.Card) (*deeplink.Deeplink, error) {
	ctaInfo, err := s.getInitiateCardFlowDeeplink(cardFlowType, cardDetails)
	if err != nil {
		return nil, fmt.Errorf("error getting cta for select shipping address screen %w", err)
	}

	screen := deeplink.Screen_DEBIT_CARD_SHIPPING_ADDRESS_SELECT_SCREEN
	screenOptions := &dcScreenOptionsPb.DebitCardShippingAddressSelectScreenOptions{
		VisualElementTitleSubtitleElement: &widgetUi.VisualElementTitleSubtitleElement{
			TitleText: &commontypes.Text{
				FontColor:    "#333333",
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Shipping your debit card!"},
				FontStyle: &commontypes.Text_CustomFontStyle{
					CustomFontStyle: &commontypes.FontStyleInfo{
						FontFamily: "Gilroy",
						FontStyle:  "normal",
						FontSize:   "20",
					},
				},
			},
			SubtitleText: &commontypes.Text{
				FontColor:    "#8D8D8D",
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Please make sure your address is up-to-date"},
				FontStyle: &commontypes.Text_CustomFontStyle{
					CustomFontStyle: &commontypes.FontStyleInfo{
						FontFamily: "Inter",
						FontStyle:  "normal",
						FontSize:   "12",
					},
				},
			},
			VisualElement: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source: &commontypes.VisualElement_Image_Url{Url: physicalCardRequestImage},
						Properties: &commontypes.VisualElementProperties{
							Width:  240,
							Height: 240,
						},
						ImageType: commontypes.ImageType_PNG,
					},
				},
			},
		},
		FooterInfo: &ui.TextWithHyperlinks{
			Text: &commontypes.Text{
				FontColor:    "#333333",
				DisplayValue: &commontypes.Text_PlainString{PlainString: "You can check out Debit Card T&C"},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_4_PARA},
			},
			HyperlinkMap: map[string]*ui.HyperLink{
				"T&C": {
					NextAction: &deeplink.Deeplink{
						Screen: deeplink.Screen_WEB_PAGE,
						ScreenOptions: &deeplink.Deeplink_WebPageScreenOptions{
							WebPageScreenOptions: &deeplink.WebpageScreenOptions{
								WebpageTitle: "Terms and Conditions",
								WebpageUrl:   "https://docs.google.com/gview?embedded=true&url=https://www.federalbank.co.in/documents/10180/45777/Terms+and+Conditions+-+Debit+card+Issuance+%26+Operations.pdf/a67c704d-36ca-4b62-b3cf-3858bf81f6e8?t=*************",
								JavaScript:   "",
							},
						},
					},
					Link: &ui.HyperLink_NextActionLink{
						NextActionLink: &deeplink.Deeplink{
							Screen: deeplink.Screen_WEB_PAGE,
							ScreenOptions: &deeplink.Deeplink_WebPageScreenOptions{
								WebPageScreenOptions: &deeplink.WebpageScreenOptions{
									WebpageTitle: "Terms and Conditions",
									WebpageUrl:   "https://docs.google.com/gview?embedded=true&url=https://www.federalbank.co.in/documents/10180/45777/Terms+and+Conditions+-+Debit+card+Issuance+%26+Operations.pdf/a67c704d-36ca-4b62-b3cf-3858bf81f6e8?t=*************",
									JavaScript:   "",
								},
							},
						},
					},
					EventParameter: "",
				},
			},
		},
		Cta: &deeplink.Cta{
			Text:     "SEND REQUEST",
			Deeplink: ctaInfo,
		},
	}

	if pkggenconf.IsFeatureEnabledOnPlatform(ctx, s.cardDynamicConf.EnableDcCardRenewalChargesFlow()) {
		screenOptions.Cta.Text = "Pay now"
	}

	amountToBePaid, displayAmount, chargesFetchErr := s.getCardRenewalChargesInfoForUser(ctx, cardDetails.GetActorId())
	if chargesFetchErr != nil {
		return nil, fmt.Errorf("error fetching physical card charges info for card renewal flow: %w", chargesFetchErr)
	}
	screenOptions.ConfirmOrderDetailsComponent = &dcScreenOptionsPb.ConfirmOrderDetailsComponent{
		DrawableProperties: &firefly.DrawableProperties{
			BgColor: widgetUi.GetBlockBackgroundColour("#EFF2F6"),
			CornerProperty: &firefly.CornerProperty{
				TopStartCornerRadius: 12,
				TopEndCornerRadius:   12,
				BottomStartCorner:    12,
				BottomEndCorner:      12,
			},
		},
		InfoItems: []*deeplink.InfoItemV2{
			{
				Title:    commontypes.GetTextFromStringFontColourFontStyle("WHERE TO SHIP YOUR CARD", "#6A6D70", commontypes.FontStyle_OVERLINE_2XS_CAPS),
				InfoType: deeplink.InfoType_CARD_ACTIVATION_INFO_TYPE_ADDRESS,
				SubTitle: commontypes.GetTextFromStringFontColourFontStyle("", "#313234", commontypes.FontStyle_SUBTITLE_M),
			},
		},
	}

	if !moneyPb.IsZero(amountToBePaid.GetBeMoney()) && !moneyPb.IsNegative(amountToBePaid.GetBeMoney()) {
		displayAmountStr := moneyPb.ToDisplayStringInIndianFormat(displayAmount.GetBeMoney(), 0, true)
		gstAmountStr := "Additional GST of ₹35 (18%) will be applied"
		screenOptions.ConfirmOrderDetailsComponent.InfoItems = append(screenOptions.ConfirmOrderDetailsComponent.InfoItems, &deeplink.InfoItemV2{
			Title: &commontypes.Text{
				FontColor:    "#929599",
				DisplayValue: &commontypes.Text_PlainString{PlainString: "CARD REPLACEMENT FEE"},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS},
			},
			SubTitle: &commontypes.Text{
				FontColor:    "#313234",
				DisplayValue: &commontypes.Text_PlainString{PlainString: displayAmountStr},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			},
			Desc: &commontypes.Text{
				FontColor:    "#929599",
				DisplayValue: &commontypes.Text_PlainString{PlainString: gstAmountStr},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
			},
			InfoType: deeplink.InfoType_CARD_ACTIVATION_INFO_TYPE_FEE_INFO,
		})
	}

	return deeplinkV3.GetDeeplinkV3(screen, screenOptions)
}

func (s *Service) getSuccessDeeplinkLandingScreen(ctx context.Context, savedCard *beCardPb.Card, title, description string) (*deeplink.Deeplink, error) {
	screen := deeplink.Screen_DEBIT_CARD_REQUEST_NEW_CARD_SUCCESS_SCREEN
	screenOptions := &dcScreenOptionsPb.DebitCardRequestNewCardSuccessScreenOptions{
		VisualElementTitleSubtitleElement: &widgetUi.VisualElementTitleSubtitleElement{
			TitleText: &commontypes.Text{
				FontColor:    "#333333",
				DisplayValue: &commontypes.Text_PlainString{PlainString: title},
				FontStyle: &commontypes.Text_CustomFontStyle{
					CustomFontStyle: &commontypes.FontStyleInfo{
						FontFamily: "Gilroy",
						FontStyle:  "normal",
						FontSize:   "20",
					},
				},
			},
			SubtitleText: &commontypes.Text{
				FontColor:    "#333333",
				DisplayValue: &commontypes.Text_PlainString{PlainString: description},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_3_PARA},
			},
			VisualElement: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source: &commontypes.VisualElement_Image_Url{
							Url: "https://epifi-icons.pointz.in/card-images/physical-card-dispatch-success-screen-img.png",
						},
						Properties: &commontypes.VisualElementProperties{
							Width:  200,
							Height: 196,
						},
						ImageType: commontypes.ImageType_PNG,
					},
				},
			},
		},
		Cta: &deeplink.Cta{
			Type: deeplink.Cta_CONTINUE,
			Text: "OK",
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_CARD_HOME_SCREEN,
				ScreenOptions: &deeplink.Deeplink_CardHomeScreenOptions{
					CardHomeScreenOptions: &deeplink.CardHomeScreenOptions{
						CardId: savedCard.GetId(),
					},
				},
			},
			DisplayTheme: deeplink.Cta_PRIMARY,
			Status:       deeplink.Cta_CTA_STATUS_ENABLED,
		},
	}

	if pkggenconf.IsFeatureEnabledOnPlatform(ctx, s.cardDynamicConf.EnablePinSetFlowRedirectionPlatformVersionCheck()) {
		screenOptions.Cta.Text = "SET PIN"
		screenOptions.Cta.Deeplink = deeplinkV3.GetDeeplinkV3WithoutError(deeplink.Screen_INITIATE_CARD_AUTH_ACTION_API,
			&dcScreenOptionsPb.InitiateCardAuthActionApiScreenOptions{
				CardId:         savedCard.GetId(),
				CardActionType: fePb.CardActionType_ACTIVATE,
				CardActionAuth: s.cardPinAuthOptions(ctx, savedCard.GetSavingsAccountId(), savedCard.GetActorId())[0].GetAuth(),
			})
	}
	return deeplinkV3.GetDeeplinkV3(screen, screenOptions)
}

func (s *Service) getInitiateCardFlowDeeplink(cardFlowType fePb.CardFlowType, cardDetail *beCardPb.Card) (*deeplink.Deeplink, error) {
	screen := deeplink.Screen_DEBIT_CARD_INITIATE_FLOW_SCREEN
	screenOptions := &dcScreenOptionsPb.DebitCardInitiateFlowScreenOptions{
		CardFlowType: cardFlowType,
	}

	switch cardFlowType {
	case fePb.CardFlowType_CARD_FLOW_TYPE_RENEW_CARD:
		screenOptions.Data = &dcScreenOptionsPb.DebitCardInitiateFlowScreenOptions_DebitCardInitiateRenewCard{
			DebitCardInitiateRenewCard: &dcScreenOptionsPb.DebitCardInitiateRenewCard{
				CardId: cardDetail.GetId(),
			},
		}
	case fePb.CardFlowType_CARD_FLOW_TYPE_CREATE_CARD:
		screenOptions.Data = &dcScreenOptionsPb.DebitCardInitiateFlowScreenOptions_DebitCardInitiateCreateCard{
			DebitCardInitiateCreateCard: &dcScreenOptionsPb.DebitCardInitiateCreateCard{
				BlockedCardId: cardDetail.GetId(),
			},
		}
	default:
		return nil, fmt.Errorf("no deeplink found to initiate card flow for cardFlowType: %s", cardFlowType)
	}
	return deeplinkV3.GetDeeplinkV3(screen, screenOptions)
}

func (s *Service) getCheckFlowStatusDeeplink(cardFlowType fePb.CardFlowType, cardId string, displayText string, nextPollTime *timestampPb.Timestamp) (*deeplink.Deeplink, error) {
	screen := deeplink.Screen_DEBIT_CARD_CHECK_FLOW_STATUS_SCREEN
	screenOptions := &dcScreenOptionsPb.DebitCardCheckFlowStatusScreenOptions{
		CardFlowType:    cardFlowType,
		NextPollTimeGap: nextPollTime.GetSeconds(),
	}

	switch cardFlowType {
	case fePb.CardFlowType_CARD_FLOW_TYPE_RENEW_CARD:
		screenOptions.Data = &dcScreenOptionsPb.DebitCardCheckFlowStatusScreenOptions_DebitCardRenewCardStatusCheckData{
			DebitCardRenewCardStatusCheckData: &dcScreenOptionsPb.DebitCardRenewCardStatusCheckData{
				DisplayText: &commontypes.Text{
					FontColor:    "#FFFFFF",
					DisplayValue: &commontypes.Text_PlainString{PlainString: displayText},
					FontStyle: &commontypes.Text_CustomFontStyle{
						CustomFontStyle: &commontypes.FontStyleInfo{
							FontFamily: "Gilroy",
							FontStyle:  "normal",
							FontSize:   "16",
						},
					},
				},
				CardId: cardId,
			},
		}
	case fePb.CardFlowType_CARD_FLOW_TYPE_CREATE_CARD:
		screenOptions.Data = &dcScreenOptionsPb.DebitCardCheckFlowStatusScreenOptions_DebitCardCreateCardStatusCheckData{
			DebitCardCreateCardStatusCheckData: &dcScreenOptionsPb.DebitCardCreateCardStatusCheckData{
				DisplayText: &commontypes.Text{
					FontColor:    "#FFFFFF",
					DisplayValue: &commontypes.Text_PlainString{PlainString: displayText},
					FontStyle: &commontypes.Text_CustomFontStyle{
						CustomFontStyle: &commontypes.FontStyleInfo{
							FontFamily: "Gilroy",
							FontStyle:  "normal",
							FontSize:   "16",
						},
					},
				},
				CardId: cardId,
			},
		}
	default:
		return nil, fmt.Errorf("no deeplink found to check flow status for cardFlowType: %s", cardFlowType)
	}
	return deeplinkV3.GetDeeplinkV3(screen, screenOptions)
}

func (s *Service) getCardRenewalChargesInfoForUser(ctx context.Context, actorId string) (*types.Money, *types.Money, error) {
	fetchResp, err := s.cardProvisioningClient.FetchCardRenewalChargesForUser(ctx, &cardProvPb.FetchCardRenewalChargesForUserRequest{
		ActorId: actorId,
	})
	if te := epifigrpc.RPCError(fetchResp, err); te != nil {
		return nil, nil, fmt.Errorf("error while fetching card renewal charges in frontend, error: %w, actor: %s", te, actorId)
	}
	return fetchResp.GetTotalAmount(), fetchResp.GetAmountWithoutGst(), nil
}

// fetchKYCLevelByActorId fetches the kyc level for the user
func (s *Service) fetchKYCLevelByActorId(ctx context.Context, actorId string) (kycPb.KYCLevel, error) {
	bcResp, errResp := s.bankCustClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
		Identifier: &bankcust.GetBankCustomerRequest_ActorId{
			ActorId: actorId,
		},
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
	})
	if err := epifigrpc.RPCError(bcResp, errResp); err != nil {
		return kycPb.KYCLevel_UNSPECIFIED, err
	}
	return bcResp.GetBankCustomer().GetKycInfo().GetKycLevel(), nil
}

// figma: https://www.figma.com/design/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-•%C2%A0Workfile?node-id=11674-33556&t=niWt5DGNlz1sOAHM-4
func (s *Service) getOrderPhysicalDebitCardSection(ctx context.Context, actorId string) *fePb.GetCardUsageAndLimitSettingsResponse_Section {
	dcToggleTravelModeFeatureEnabled := s.checkFeatureReleaseConstraints(ctx, types.Feature_FEATURE_DC_TOGGLE_TRAVEL_MODE, actorId)
	if !dcToggleTravelModeFeatureEnabled {
		return nil
	}
	dcUsageAndLimitsScreenConfig := s.cardDynamicConf.DcUsageAndLimitsScreenConfig()

	var (
		titleConf    = dcUsageAndLimitsScreenConfig.OrderPhysicalCardComponentTitle()
		subTitleConf = dcUsageAndLimitsScreenConfig.OrderPhysicalCardComponentDesc()
	)

	// UI components
	titleTypesText := commontypes.GetTextFromStringFontColourFontStyle(titleConf.Content(), titleConf.FontColor(), commontypes.FontStyle(commontypes.FontStyle_value[titleConf.FontStyle()]))
	titleTypesText.Alignment = commontypes.Text_ALIGNMENT_LEFT
	titleTypesTextComponent := &components.Component{
		Content: helper.GetAnyWithoutError(titleTypesText),
	}

	subtitleTypesText := commontypes.GetTextFromStringFontColourFontStyle(subTitleConf.Content(), subTitleConf.FontColor(), commontypes.FontStyle(commontypes.FontStyle_value[subTitleConf.FontStyle()]))
	subtitleTypesText.Alignment = commontypes.Text_ALIGNMENT_LEFT
	subtitleTypesTextComponent := &components.Component{
		Content: helper.GetAnyWithoutError(subtitleTypesText),
	}

	cardVisualElement := cardPkg.GetVisualElementComponent(orderPhysicalDebitCardHalfImage, 98, 70, commontypes.ImageType_PNG)
	rightChevronVisualElement := cardPkg.GetVisualElementComponent(rightChevronIconGray, 24, 24, commontypes.ImageType_PNG)

	// Center vertical list section with title and subtitle
	centerVerticalListSection := sduiHelper.GetVerticalSection([]*components.Component{titleTypesTextComponent, subtitleTypesTextComponent})
	centerVerticalListSection.VerticalArrangement = sections.VerticalListSection_VERTICAL_ARRANGEMENT_CENTER
	centerVerticalListSection.HorizontalAlignment = sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT
	centerVerticalListSection.VisualProperties = []*properties.VisualProperty{
		{
			Properties: &properties.VisualProperty_ContainerProperty{
				ContainerProperty: &properties.ContainerProperty{
					Margin: &properties.PaddingProperty{
						Left: 18,
					},
				},
			},
		},
	}

	// right vertical list section with right chevron icon
	rightVerticalSection := sduiHelper.GetVerticalSection([]*components.Component{rightChevronVisualElement})
	rightVerticalSection.VerticalArrangement = sections.VerticalListSection_VERTICAL_ARRANGEMENT_CENTER
	rightVerticalSection.VisualProperties = []*properties.VisualProperty{
		{
			Properties: &properties.VisualProperty_ContainerProperty{
				ContainerProperty: &properties.ContainerProperty{
					Margin: &properties.PaddingProperty{
						Right: 20,
					},
				},
			},
		},
	}

	mainHorizontalListSectionComponents := []*components.Component{
		cardVisualElement,
		sduiHelper.GetComponentFromVerticalSection(centerVerticalListSection),
		sduiHelper.GetSpacerComponent(),
		sduiHelper.GetComponentFromVerticalSection(rightVerticalSection),
	}

	orderPhysicalCardDeeplink := *deeplinkV3.GetDeeplinkV3WithoutError(
		deeplink.Screen_DEBIT_CARD_RPC_BASED_REDIRECTION,
		&dcScreenOptionsPb.DebitCardRpcBasedRedirectionScreenOption{
			RpcParams: &dcScreenOptionsPb.DebitCardRpcBasedRedirectionScreenOption_FetchPhysicalCardChargesForUserRequest{
				FetchPhysicalCardChargesForUserRequest: &fePb.FetchPhysicalCardChargesForUserRequest{},
			},
		},
	)

	mainHorizontalSection := fePb.GetCardUsageAndLimitSettingsResponse_Section{
		Section: &fePb.GetCardUsageAndLimitSettingsResponse_Section_SduiSection{
			SduiSection: &sections.Section{
				Content: &sections.Section_HorizontalListSection{
					HorizontalListSection: &sections.HorizontalListSection{
						Components: mainHorizontalListSectionComponents,
						VisualProperties: []*properties.VisualProperty{
							{
								Properties: &properties.VisualProperty_ContainerProperty{
									ContainerProperty: &properties.ContainerProperty{
										Size: &properties.Size{
											Width: &properties.Size_Dimension{
												Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
											},
											Height: &properties.Size_Dimension{
												Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
												ExactValue: 122,
											},
										},
										BgColor: widgetUi.GetBlockBackgroundColour("#FFFFFF"),
										Margin: &properties.PaddingProperty{
											Left:  24,
											Right: 24,
										},
										Corner: &properties.CornerProperty{
											TopLeftCornerRadius:  20,
											TopRightCornerRadius: 20,
											BottomLeftCorner:     20,
											BottomRightCorner:    20,
										},
									},
								},
							},
						},
						InteractionBehaviors: []*behaviors.InteractionBehavior{
							{
								Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
									OnClickBehavior: &behaviors.OnClickBehavior{
										Action: helper.GetAnyWithoutError(&orderPhysicalCardDeeplink),
									},
								},
							},
						},
					},
				},
			},
		},
	}

	return &mainHorizontalSection
}

// nolint: dupl
// This function calls the card rpc for the home page international dc widget & populates the content.
// NOTE: It does not populate the design specific fields like fonts, colors etc.
func (s *Service) getInternationalDcWidget(ctx context.Context, actorId string, countryCode string) (*getDCInternationalWidgetHelperModel, error) {
	var (
		bottomSectionVisualElementUrl string
		bottomSectionDeeplink         *deeplink.Deeplink
	)

	getWidgetRes, getWidgetErr := s.cardProvisioningClient.GetDcInternationalWidget(ctx, &cardProvPb.GetDcInternationalWidgetRequest{
		ActorId:     actorId,
		CountryCode: countryCode,
	})
	if te := epifigrpc.RPCError(getWidgetRes, getWidgetErr); te != nil {
		// do not display widget is rpc status is DO_NOT_DISPLAY_WIDGET
		if getWidgetRes.GetStatus().GetCode() == uint32(cardProvPb.GetDcInternationalWidgetResponse_DO_NOT_DISPLAY_WIDGET) {
			return &getDCInternationalWidgetHelperModel{isWidgetToBeDisplayed: false}, nil
		}
		return nil, errors.Wrap(te, "error while fetching international dc widget")
	}

	// parse states from result
	externalCountryCurrencyCode := webFeTravel.CountryInfoMap[getWidgetRes.GetDcInternationalWidgetDetails().GetCountry()].CurrencyCode
	topSectionBackGroundImageUrl := s.cardDynamicConf.InternationalDcWidgetConfig().CountryToTopSectionBackgroundImageMap().Get(getWidgetRes.GetDcInternationalWidgetDetails().GetCountry())
	if topSectionBackGroundImageUrl == "" {
		topSectionBackGroundImageUrl = s.cardDynamicConf.InternationalDcWidgetConfig().DefaultCountryTopSectionBackgroundImage()
	}

	bottomSectionVisualElementUrl = s.cardDynamicConf.InternationalDcWidgetConfig().BottomSectionStateWiseVisualElementMap().Get(getWidgetRes.GetDcInternationalWidgetDetails().GetBottomSectionBackgroundState().String())

	switch getWidgetRes.GetDcInternationalWidgetDetails().GetBottomSectionCtaState() {
	case cardProvPb.DcInternationalWidgetDetails_BOTTOM_SECTION_CTA_DC_INTERNATIONAL_USAGE_OR_LIMITS_DISABLED:
		bottomSectionDeeplink = &deeplink.Deeplink{
			Screen: deeplink.Screen_DC_USAGE_AND_LIMIT_SETTINGS_SCREEN,
		}
	case cardProvPb.DcInternationalWidgetDetails_BOTTOM_SECTION_CTA_ECOMMERCE_USAGE_OFF:
		bottomSectionDeeplink = &deeplink.Deeplink{
			Screen: deeplink.Screen_CARD_USAGE_SCREEN,
			ScreenOptions: &deeplink.Deeplink_CardUsageScreenOptions{
				CardUsageScreenOptions: &deeplink.CardUsageScreenOptions{
					CardId: getWidgetRes.GetDcInternationalWidgetDetails().GetCardId(),
				},
			},
		}
	case cardProvPb.DcInternationalWidgetDetails_BOTTOM_SECTION_CTA_ECOMMERCE_LIMITS_LOW:
		bottomSectionDeeplink = &deeplink.Deeplink{
			Screen: deeplink.Screen_CARD_LIMITS_HOME_SCREEN,
			ScreenOptions: &deeplink.Deeplink_CardLimitHomeScreenOptions{
				CardLimitHomeScreenOptions: &deeplink.CardLimitHomeScreenOptions{
					CardId: getWidgetRes.GetDcInternationalWidgetDetails().GetCardId(),
				},
			},
		}
	case cardProvPb.DcInternationalWidgetDetails_BOTTOM_SECTION_CTA_ALL_TRANSACTIONS:
		derivedAccountId, encodeErr := idgen.EncodeProtoToStdBase64(&accountPb.DerivedAccountId{InternalAccountId: getWidgetRes.GetDcInternationalWidgetDetails().GetCardSavingsAccountId()})
		if encodeErr != nil {
			return nil, errors.Wrap(encodeErr, "error while encoding derived acc id to base64")
		}
		bottomSectionDeeplink = &deeplink.Deeplink{
			Screen: deeplink.Screen_ALL_TRANSACTIONS_ACCOUNT_FILTER,
			ScreenOptions: &deeplink.Deeplink_AllTransactionsAccountFilterOptions{
				AllTransactionsAccountFilterOptions: &deeplink.AllTransactionsAccountFilterOptions{
					DerivedAccountId:    derivedAccountId,
					AccountType:         accountPb.Type_SAVINGS,
					TransactionFlowType: deeplink.AllTransactionsAccountFilterOptions_TRANSACTION_FLOW_TYPE_DEBIT_CARD,
				}},
		}
	case cardProvPb.DcInternationalWidgetDetails_BOTTOM_SECTION_CTA_DC_HOME:
		bottomSectionDeeplink = &deeplink.Deeplink{
			Screen:        deeplink.Screen_CARD_HOME_SCREEN,
			ScreenOptions: &deeplink.Deeplink_CardHomeScreenOptions{CardHomeScreenOptions: &deeplink.CardHomeScreenOptions{CardId: getWidgetRes.GetDcInternationalWidgetDetails().GetCardId()}},
		}

	}

	return &getDCInternationalWidgetHelperModel{
		isWidgetToBeDisplayed: true,
		CurrencyHeaderText: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: strings.ToUpper(s.cardDynamicConf.InternationalDcWidgetConfig().CurrencyHeaderText()),
			},
		},
		CurrencyValue: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					DisplayValue: &commontypes.Text_PlainString{PlainString: getWidgetRes.GetDcInternationalWidgetDetails().GetAccountBalance()},
				},
			},
		},
		CurrencySubtext: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: externalCountryCurrencyCode},
		},
		CurrencyDescriptionFooter: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: fmt.Sprintf(s.cardDynamicConf.InternationalDcWidgetConfig().CurrencyDescriptionFooter(),
							externalCountryCurrencyCode, getNumberWithoutTrailingZeros(float64(getWidgetRes.GetDcInternationalWidgetDetails().GetExchangeRate())))},
				},
			},
		},
		TopSectionBackgroundVisual: &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{
						Url: topSectionBackGroundImageUrl,
					},
				},
			},
		},
		MidSectionCtas: []*ui.IconTextComponent{
			{
				Texts: []*commontypes.Text{
					{
						DisplayValue: &commontypes.Text_PlainString{PlainString: s.cardDynamicConf.InternationalDcWidgetConfig().MidSection()[0].Text},
					},
				},
				LeftVisualElement: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{Url: s.cardDynamicConf.InternationalDcWidgetConfig().MidSection()[0].ImageUrl},
				}}},
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_CARD_USAGE_SCREEN,
					ScreenOptions: &deeplink.Deeplink_CardUsageScreenOptions{
						CardUsageScreenOptions: &deeplink.CardUsageScreenOptions{
							CardId: getWidgetRes.GetDcInternationalWidgetDetails().GetCardId(),
						},
					},
				},
			},
			{
				Texts: []*commontypes.Text{
					{
						DisplayValue: &commontypes.Text_PlainString{PlainString: s.cardDynamicConf.InternationalDcWidgetConfig().MidSection()[1].Text},
					},
				},
				LeftVisualElement: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{Url: s.cardDynamicConf.InternationalDcWidgetConfig().MidSection()[1].ImageUrl},
				}}},
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_WEB_PAGE,
					ScreenOptions: &deeplink.Deeplink_WebPageScreenOptions{
						WebPageScreenOptions: &deeplink.WebpageScreenOptions{
							WebpageTitle: "Currency Exchange Calculator",
							WebpageUrl:   "https://fi.money/calculators/currency-convertor",
						},
					},
				},
			},
			{
				Texts: []*commontypes.Text{
					{
						DisplayValue: &commontypes.Text_PlainString{PlainString: s.cardDynamicConf.InternationalDcWidgetConfig().MidSection()[2].Text},
					},
				},
				LeftVisualElement: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{Url: s.cardDynamicConf.InternationalDcWidgetConfig().MidSection()[2].ImageUrl},
				}}},
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_CARD_LIMITS_HOME_SCREEN,
					ScreenOptions: &deeplink.Deeplink_CardLimitHomeScreenOptions{
						CardLimitHomeScreenOptions: &deeplink.CardLimitHomeScreenOptions{
							CardId: getWidgetRes.GetDcInternationalWidgetDetails().GetCardId(),
						},
					},
				},
			},
		},
		// this is white in the design currently, so not populating it
		MidSectionBackground: nil,
		CurrencySelectionCta: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: fmt.Sprintf(
							s.cardDynamicConf.InternationalDcWidgetConfig().CurrencySelectionCtaText(),
							webFeTravel.CountryInfoMap[getWidgetRes.GetDcInternationalWidgetDetails().GetCountry()].Flag,
							strings.ToUpper(getWidgetRes.GetDcInternationalWidgetDetails().GetCountry()),
						),
					},
				},
			},
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_DC_COUNTRY_SELECTION_BOTTOM_SHEET,
				ScreenOptionsV2: deeplinkV3.GetScreenOptionV2WithoutError(
					&dcScreenOptionsPb.DcCountrySelectionBottomSheetScreenOptions{
						FlowType: dcScreenOptionsPb.DcCountrySelectionBottomSheetScreenOptions_COUNTRY_SELECTION_FLOW_TYPE_CURRENCY_CONVERSION,
					},
				),
			},
		},
		BottomITC: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					DisplayValue: &commontypes.Text_PlainString{PlainString: getWidgetRes.GetDcInternationalWidgetDetails().GetBottomSectionText()},
				},
			},
			LeftVisualElement: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{Image: &commontypes.VisualElement_Image{
				Source: &commontypes.VisualElement_Image_Url{Url: bottomSectionVisualElementUrl},
			}}},
			Deeplink: bottomSectionDeeplink,
		},
		BottomSectionBackground:      nil,
		BottomSectionBackGroundState: getWidgetRes.GetDcInternationalWidgetDetails().GetBottomSectionBackgroundState(),
	}, nil

}

func getNumberWithoutTrailingZeros(f float64) string {
	return strings.TrimRight(strings.TrimRight(fmt.Sprintf("%.4f", math.Abs(f)), "0"), ".")
}
