package journey

import (
	"context"
	"fmt"
	"sort"
	"strconv"
	"time"

	homeFePb "github.com/epifi/gamma/api/frontend/home"
	accrualPkg "github.com/epifi/gamma/pkg/accrual"

	"github.com/samber/lo"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	nudgePb "github.com/epifi/gamma/api/nudge"
	journeyPb "github.com/epifi/gamma/api/nudge/journey"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/typesv2/common/ui/widget"

	"github.com/epifi/gamma/api/typesv2/ui"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/epifi/be-common/api/rpc"

	"github.com/epifi/gamma/api/frontend/header"

	journeyFePb "github.com/epifi/gamma/api/frontend/nudge"
)

// GetActivationWidget fetches and constructs the activation widget for a user.
// Key Points to Note:
// We are powering the activation widget using the journey and nudge services.
// Each milestone in the activation widget corresponds to a nudge. Therefore, we reuse some nudge components to build the activation widget.
func (s *Service) GetActivationWidget(ctx context.Context, req *journeyFePb.GetActivationWidgetRequest) (*journeyFePb.GetActivationWidgetResponse, error) {
	actorID := req.GetReq().GetAuth().GetActorId()

	// Fetch activation journey details
	res, err := s.journeyClient.FetchActivationJourney(ctx, &journeyPb.FetchActivationJourneyRequest{
		ActorId: actorID,
	})
	if te := epifigrpc.RPCError(res, err); te != nil {
		if res.GetStatus().IsRecordNotFound() {
			logger.Debug(ctx, "activation journey not found")
			return &journeyFePb.GetActivationWidgetResponse{
				RespHeader: &header.ResponseHeader{Status: rpc.StatusRecordNotFound()},
			}, nil
		}
		logger.Error(ctx, "error fetching activation journey", zap.String(logger.ACTOR_ID_V2, actorID), zap.Error(te))
		return &journeyFePb.GetActivationWidgetResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(te.Error())},
		}, nil
	}

	journeyInstance := res.GetJourneyInstance()
	nudgeInstances := res.GetNudgeInstances()
	actorJourneyID := journeyInstance.GetActorJourneyId()

	// Sort nudge instances based on their order in journey
	sortedNudgeInstances := sortNudgeInstances(journeyInstance, nudgeInstances)

	// Find the current active nudge instance
	currentActiveNudgeInstance := findCurrentActiveNudgeInstance(sortedNudgeInstances)

	// Build sections for the activation widget
	sections := buildActivationWidgetSections(ctx, journeyInstance, sortedNudgeInstances, currentActiveNudgeInstance)

	logger.Debug(ctx, "activation journey found for activation widget", zap.String("actor_journey_id", actorJourneyID))
	return &journeyFePb.GetActivationWidgetResponse{
		RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
		ActivationWidget: &journeyFePb.ActivationWidget{
			ActorJourneyId:    actorJourneyID,
			Sections:          sections,
			AnalyticsMetadata: getAnalyticsMetadata(journeyInstance),
			BorderColor:       homeFePb.GetHomeWidgetBorderColor(),
		},
	}, nil
}

// sortNudgeInstances sorts nudge instances according to their order in the journey
func sortNudgeInstances(journeyInstance *journeyPb.JourneyInstance, nudgeInstances []*nudgePb.NudgeInstance) []*nudgePb.NudgeInstance {
	// Create a map for quick lookup of nudge order
	idOrder := make(map[string]int, len(journeyInstance.GetNudgeIds()))
	for index, id := range journeyInstance.GetNudgeIds() {
		idOrder[id] = index
	}

	// Sort based on the order in the journey
	sort.Slice(nudgeInstances, func(i, j int) bool {
		return idOrder[nudgeInstances[i].GetNudgeId()] < idOrder[nudgeInstances[j].GetNudgeId()]
	})

	return nudgeInstances
}

// findCurrentActiveNudgeInstance returns the first activated nudge or the first nudge if none are activated
func findCurrentActiveNudgeInstance(nudgeInstances []*nudgePb.NudgeInstance) *nudgePb.NudgeInstance {
	for _, nudgeInstance := range nudgeInstances {
		if nudgeInstance.GetActorNudgeStatus() == nudgePb.ActorNudgeStatus_ACTIVATED {
			return nudgeInstance
		}
	}

	if len(nudgeInstances) == 0 {
		return nil
	}

	return nudgeInstances[0]
}

// buildActivationWidgetSections constructs all sections for the activation widget
func buildActivationWidgetSections(ctx context.Context, journeyInstance *journeyPb.JourneyInstance,
	nudgeInstances []*nudgePb.NudgeInstance, currentActiveNudgeInstance *nudgePb.NudgeInstance) []*journeyFePb.ActivationWidgetSection {

	sections := make([]*journeyFePb.ActivationWidgetSection, 0, 4) // Pre-allocate for efficiency

	// Add top visual header section
	if headerSection := getTopVisualHeaderSection(journeyInstance, currentActiveNudgeInstance); headerSection != nil {
		sections = append(sections, headerSection)
	}

	// Add progress summary section
	if progressSection, err := getProgressSummarySection(ctx, journeyInstance, nudgeInstances); err == nil && progressSection != nil {
		sections = append(sections, progressSection)
	} else if err != nil {
		logger.Error(ctx, "error getting progress summary section", zap.Error(err))
	}

	// Add milestone tracker section
	if milestoneSection := getMilestoneTrackerSection(journeyInstance, nudgeInstances); milestoneSection != nil {
		sections = append(sections, milestoneSection)
	}

	// Add banner section
	if bannerSection := getMilestoneWidgetBanner(journeyInstance); bannerSection != nil {
		sections = append(sections, bannerSection)
	}

	return sections
}

// getAnalyticsMetadata creates a map of analytics data from journey instance
func getAnalyticsMetadata(journeyInstance *journeyPb.JourneyInstance) map[string]string {
	daysRemaining := journeyInstance.GetExpireAt().AsTime().Sub(time.Now()).Hours() / 24
	completedTasks := len(journeyInstance.GetProgress().GetCompletedNudgeIds())
	totalTasks := len(journeyInstance.GetNudgeIds())

	return map[string]string{
		"actor_journey_id": journeyInstance.GetActorJourneyId(),
		"journey_id":       journeyInstance.GetJourneyId(),
		"total_tasks":      fmt.Sprintf("%d", totalTasks),
		"completed_tasks":  fmt.Sprintf("%d", completedTasks),
		"days_remaining":   fmt.Sprintf("%f", daysRemaining),
	}
}

// For the Top Visual Header Section:
// 1. The Image/Lottie URL will always be taken from the current progressive nudge's NudgeDisplay field.
// 2. Optionally, the Deeplink for the image/Lottie can be taken from the current progressive nudge's Deeplink field.
// 3. If the journey is completed, the Image/Lottie URL will be taken from the journey's display field.
// 4. If the journey is completed, we send the perform completion action CTA.
// 5. If the journey is completed and confirmed by the client, we do not need to show the visual header section.
func getTopVisualHeaderSection(journeyInstance *journeyPb.JourneyInstance, nudgeInstance *nudgePb.NudgeInstance) *journeyFePb.ActivationWidgetSection {
	// If journey is completed & confirmed by client, don't need to show the visual header section
	if journeyInstance.GetStatus() == journeyPb.ActorJourneyStatus_ACTOR_JOURNEY_STATUS_COMPLETION_CONFIRMED {
		return nil
	}

	// If nudgeInstance is nil, we can't proceed
	if nudgeInstance == nil {
		return nil
	}

	// Determine which image/lottie URL and deeplink to use based on journey status
	var (
		imageUrl  = nudgeInstance.GetNudgeDisplay().GetImageUrl()
		lottieUrl = nudgeInstance.GetNudgeDisplay().GetLottieUrl()
		deeplink  = nudgeInstance.GetDeeplink()
	)

	if journeyInstance.GetStatus() == journeyPb.ActorJourneyStatus_ACTOR_JOURNEY_STATUS_COMPLETED {
		imageUrl = journeyInstance.GetDisplayConfig().GetImageUrl()
		lottieUrl = journeyInstance.GetDisplayConfig().GetLottieUrl()
		deeplink = nil
	}

	// Create visual element from URL with appropriate dimensions
	// Default to image if lottie URL is not provided
	topVisualElement := commontypes.GetVisualElementFromUrlHeightAndWidth(imageUrl, 160, 390)
	if lottieUrl != "" {
		topVisualElement = commontypes.GetVisualElementLottieFromUrlHeightAndWidth(lottieUrl, 160, 390)
	}

	return &journeyFePb.ActivationWidgetSection{
		SectionType: journeyFePb.ActivationWidgetSection_SECTION_TYPE_VISUAL_HEADER,
		Section: &journeyFePb.ActivationWidgetSection_VisualHeaderSection{
			VisualHeaderSection: &journeyFePb.VisualHeaderSection{
				VisualElement:           topVisualElement,
				Deeplink:                deeplink,
				PerformCompletionAction: getPerformCompletionActionCta(journeyInstance.GetStatus(), journeyInstance.GetDisplayConfig()),
			},
		},
	}
}

// getPerformCompletionActionCta returns CTA for journey completion based on status
func getPerformCompletionActionCta(status journeyPb.ActorJourneyStatus, journeyDisplay *journeyPb.JourneyDisplay) *ui.IconTextComponent {
	if status == journeyPb.ActorJourneyStatus_ACTOR_JOURNEY_STATUS_COMPLETED {
		return &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle(
					journeyDisplay.GetPerformCompletionActionCta().GetTitle(),
					"#FFFFFF",
					commontypes.FontStyle_BUTTON_S),
			},
			Deeplink: journeyDisplay.GetPerformCompletionActionCta().GetDeeplink(),
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:       "#00B899",
				CornerRadius:  20,
				Height:        32,
				Width:         170,
				LeftPadding:   12,
				RightPadding:  12,
				TopPadding:    8,
				BottomPadding: 8,
			},
		}
	}

	return nil
}

// For the Progress Summary Section:
// 1. The progress summary section will display the progress summary text along with additional information text, which is populated from the journey display configuration.
// 2. If the journey is completed, the progress summary section will not be shown.
func getProgressSummarySection(ctx context.Context, journeyInstance *journeyPb.JourneyInstance,
	nudgeInstances []*nudgePb.NudgeInstance) (*journeyFePb.ActivationWidgetSection, error) {
	// Don't show progress for completed journeys
	completedStatuses := []journeyPb.ActorJourneyStatus{
		journeyPb.ActorJourneyStatus_ACTOR_JOURNEY_STATUS_COMPLETED,
		journeyPb.ActorJourneyStatus_ACTOR_JOURNEY_STATUS_COMPLETION_CONFIRMED,
	}
	if lo.Contains(completedStatuses, journeyInstance.GetStatus()) {
		return nil, nil
	}

	// Calculate current and total progress
	var currentProgressValue, totalProgressValue int
	for _, nudgeInstance := range nudgeInstances {
		value, err := getProgressValue(nudgeInstance)
		if err != nil {
			logger.Error(ctx, "error getting progress value", zap.Error(err))
			return nil, err
		}

		if nudgeInstance.GetActorNudgeStatus() == nudgePb.ActorNudgeStatus_COMPLETED {
			currentProgressValue += value
		}
		totalProgressValue += value
	}

	// Format progress values based on incentive type
	progressSummary, leftVisualElement := formatProgressSummary(nudgeInstances, totalProgressValue)

	return &journeyFePb.ActivationWidgetSection{
		SectionType: journeyFePb.ActivationWidgetSection_SECTION_TYPE_PROGRESS_SUMMARY,
		Section: &journeyFePb.ActivationWidgetSection_ProgressSummarySection{
			ProgressSummarySection: &journeyFePb.ProgressSummarySection{
				ProgressSummary: &ui.IconTextComponent{
					LeftVisualElement: leftVisualElement,
					Texts: []*commontypes.Text{
						{
							FontColor:    "#648E4D",
							DisplayValue: &commontypes.Text_PlainString{PlainString: strconv.Itoa(currentProgressValue)},
							FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
						},
						{
							FontColor:    "#313234",
							DisplayValue: &commontypes.Text_PlainString{PlainString: progressSummary},
							FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
						},
					},
				},
				InfoText: &ui.IconTextComponent{
					Texts: []*commontypes.Text{
						{
							FontColor:    "#00B899",
							DisplayValue: &commontypes.Text_PlainString{PlainString: journeyInstance.GetDisplayConfig().GetAdditionalInfoText()},
							FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
						},
					},
					Deeplink: journeyInstance.GetDisplayConfig().GetInfoTextDeeplink(),
				},
			},
		},
	}, nil
}

// formatProgressSummary creates formatted progress text based on incentive type
func formatProgressSummary(nudgeInstances []*nudgePb.NudgeInstance, totalProgress int) (string, *commontypes.VisualElement) {
	// Default values
	totalProgressString := fmt.Sprintf("/%d Tasks done", totalProgress)
	var leftVisualElement *commontypes.VisualElement

	// Check first nudge's incentive type if available
	if len(nudgeInstances) > 0 {
		incentiveType := nudgeInstances[0].GetAdditionalDetails().GetIncentiveValue().GetType()

		switch incentiveType {
		case nudgePb.NudgeIncentiveType_NUDGE_INCENTIVE_TYPE_FI_COINS:
			totalProgressString = fmt.Sprintf(accrualPkg.ReplaceCoinWithPointIfApplicable("/%d Fi-Coins earned", nil), totalProgress)
			leftVisualElement = commontypes.GetVisualElementFromUrlHeightAndWidth(
				"https://epifi-icons.s3.ap-south-1.amazonaws.com/home-v2/activation/fi-coin.png", 16, 16)
		case nudgePb.NudgeIncentiveType_NUDGE_INCENTIVE_TYPE_CASH:
			totalProgressString = fmt.Sprintf("/₹%d Cash earned", totalProgress)
		}
	}

	return totalProgressString, leftVisualElement
}

// getProgressValue determines the progress value based on incentive type
func getProgressValue(nudgeInstance *nudgePb.NudgeInstance) (int, error) {
	switch nudgeInstance.GetAdditionalDetails().GetIncentiveValue().GetType() {
	case nudgePb.NudgeIncentiveType_NUDGE_INCENTIVE_TYPE_FI_COINS, nudgePb.NudgeIncentiveType_NUDGE_INCENTIVE_TYPE_CASH:
		fiCoinValue, err := strconv.Atoi(nudgeInstance.GetAdditionalDetails().GetIncentiveValue().GetIncentiveValue())
		if err != nil {
			return 0, fmt.Errorf("invalid incentive value: %w", err)
		}
		return fiCoinValue, nil
	default:
		return 1, nil
	}
}

// getCompletedMilestoneCard creates a card for completed milestones
func getCompletedMilestoneCard(nudgeInstance *nudgePb.NudgeInstance) *journeyFePb.MilestoneTrackerSection_Milestone {
	var badgeValue *ui.IconTextComponent
	incentiveValue := nudgeInstance.GetAdditionalDetails().GetIncentiveValue()

	switch incentiveValue.GetType() {
	case nudgePb.NudgeIncentiveType_NUDGE_INCENTIVE_TYPE_FI_COINS:
		// Use Fi-Coins visual element
		leftVisualElement := commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.s3.ap-south-1.amazonaws.com/home-v2/activation/fi-coin.png", 16, 16)
		badgeValue = createBadgeValueComponent(incentiveValue.GetIncentiveValue(), leftVisualElement)
	case nudgePb.NudgeIncentiveType_NUDGE_INCENTIVE_TYPE_CASH:
		// Use Rupee icon for cash
		cashIcon := commontypes.GetVisualElementFromUrlHeightAndWidth(
			"https://epifi-icons.s3.ap-south-1.amazonaws.com/rewards/rewards_summary_rupee_icon.png", 16, 16)
		badgeValue = createBadgeValueComponent(incentiveValue.GetIncentiveValue(), cashIcon)
	default:
		// No badge for other types
		badgeValue = nil
	}

	return &journeyFePb.MilestoneTrackerSection_Milestone{
		MilestoneBadge: &journeyFePb.MilestoneTrackerSection_Milestone_MilestoneBadge{
			BadgeIconText: &ui.VerticalKeyValuePair{
				Title: &ui.IconTextComponent{
					LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(
						"https://epifi-icons.s3.ap-south-1.amazonaws.com/home-v2/activation/green-tick.png", 16, 16),
				},
				Value:      badgeValue,
				HAlignment: ui.VerticalKeyValuePairHAlignment_VERTICAL_KEY_VALUE_PAIR_H_ALIGNMENT_CENTER,
			},
			BadgeBackground: widget.GetBlockBackgroundColour("#D5E6CE"),
			CornerRadius:    12,
		},
		Title: commontypes.GetTextFromStringFontColourFontStyle(
			nudgeInstance.GetNudgeDisplay().GetNudgeCompletedText(),
			"#86BA6F",
			commontypes.FontStyle_SUBTITLE_S),
		CardBackground:     widget.GetBlockBackgroundColour("#F6F9FD"),
		StepIndicatorColor: widget.GetBlockBackgroundColour("#D5E6CE"),
		StepState:          journeyFePb.MilestoneState_MILESTONE_STATE_COMPLETED,
	}
}

// createBadgeValueComponent is a helper to create badge value components
func createBadgeValueComponent(value string, leftVisualElement *commontypes.VisualElement) *ui.IconTextComponent {
	return &ui.IconTextComponent{
		Texts: []*commontypes.Text{
			{
				FontColor:    "#648E4D",
				DisplayValue: &commontypes.Text_PlainString{PlainString: value},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
			},
		},
		LeftVisualElement: leftVisualElement,
	}
}

// getActiveMilestoneCard creates a card for active or upcoming milestones
func getActiveMilestoneCard(nudgeInstance *nudgePb.NudgeInstance, isCurrentActiveMilestone bool) *journeyFePb.MilestoneTrackerSection_Milestone {
	incentiveType := nudgeInstance.GetAdditionalDetails().GetIncentiveValue().GetType()
	incentiveValue := nudgeInstance.GetAdditionalDetails().GetIncentiveValue().GetIncentiveValue()

	// Default values
	topBadgeIconText := "EARN"
	var (
		badgeValue         *ui.IconTextComponent
		badgeBackground    *widget.BackgroundColour
		titleColor         string
		cardBackground     *widget.BackgroundColour
		stepIndicatorColor *widget.BackgroundColour
		actionButton       *ui.IconTextComponent
		stepState          journeyFePb.MilestoneState
	)

	// Configure based on whether this is the current active milestone
	if isCurrentActiveMilestone {
		badgeBackground = widget.GetLinearGradientBackgroundColour(90, []*widget.ColorStop{
			{Color: "#DCEBD6", StopPercentage: 0},
			{Color: "#86BE72", StopPercentage: 100},
		})
		titleColor = "#313234"
		cardBackground = widget.GetBlockBackgroundColour("#FFFFFF")
		stepIndicatorColor = widget.GetBlockBackgroundColour("#E6E9ED")
		stepState = journeyFePb.MilestoneState_MILESTONE_STATE_IN_PROGRESS

		// Create action button for active milestone
		actionButton = &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle(
					nudgeInstance.GetNudgeDisplay().GetCtaText(),
					"#FFFFFF",
					commontypes.FontStyle_SUBTITLE_S),
			},
			RightImgTxtPadding: 4,
			Deeplink:           nudgeInstance.GetDeeplink(),
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:       "#00B899",
				CornerRadius:  12,
				Height:        32,
				Width:         93,
				LeftPadding:   12,
				RightPadding:  8,
				TopPadding:    7,
				BottomPadding: 7,
			},
			RightVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(
				"https://epifi-icons.s3.ap-south-1.amazonaws.com/home-v2/activation/chevron-right.png", 20, 20),
		}
	} else {
		badgeBackground = widget.GetBlockBackgroundColour("#EFF2F6")
		titleColor = "#B2B5B9"
		cardBackground = widget.GetBlockBackgroundColour("#F6F9FD")
		stepState = journeyFePb.MilestoneState_MILESTONE_STATE_NOT_STARTED
	}

	// Configure incentive display based on type
	switch incentiveType {
	case nudgePb.NudgeIncentiveType_NUDGE_INCENTIVE_TYPE_FI_COINS:
		leftVisualElement := commontypes.GetVisualElementFromUrlHeightAndWidth(
			"https://epifi-icons.s3.ap-south-1.amazonaws.com/home-v2/activation/fi-coin.png", 16, 16)
		badgeValue = createBadgeValueComponent(incentiveValue, leftVisualElement)
	case nudgePb.NudgeIncentiveType_NUDGE_INCENTIVE_TYPE_CASH:
		leftVisualElement := commontypes.GetVisualElementFromUrlHeightAndWidth(
			"https://epifi-icons.s3.ap-south-1.amazonaws.com/rewards/rewards_summary_rupee_icon.png", 16, 16)
		badgeValue = createBadgeValueComponent(incentiveValue, leftVisualElement)
	default:
		// No badge value for unspecified incentive type
		badgeValue = nil
		topBadgeIconText = incentiveValue
	}

	var fontColor string
	if isCurrentActiveMilestone {
		fontColor = "#37522A"
	} else {
		fontColor = "#B2B5B9"
	}

	return &journeyFePb.MilestoneTrackerSection_Milestone{
		MilestoneBadge: &journeyFePb.MilestoneTrackerSection_Milestone_MilestoneBadge{
			BadgeIconText: &ui.VerticalKeyValuePair{
				Title: &ui.IconTextComponent{
					Texts: []*commontypes.Text{
						{
							FontColor:    fontColor,
							DisplayValue: &commontypes.Text_PlainString{PlainString: topBadgeIconText},
							FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
						},
					},
				},
				Value:      badgeValue,
				HAlignment: ui.VerticalKeyValuePairHAlignment_VERTICAL_KEY_VALUE_PAIR_H_ALIGNMENT_CENTER,
			},
			BadgeBackground: badgeBackground,
			CornerRadius:    12,
		},
		Title: commontypes.GetTextFromStringFontColourFontStyle(
			nudgeInstance.GetNudgeDisplay().GetTitle(),
			titleColor,
			commontypes.FontStyle_SUBTITLE_S),
		ActionButton:       actionButton,
		CardBackground:     cardBackground,
		StepIndicatorColor: stepIndicatorColor,
		StepState:          stepState,
	}
}

// getMilestoneTrackerSection builds the milestone tracker section of the widget
func getMilestoneTrackerSection(journeyInstance *journeyPb.JourneyInstance, nudgeInstances []*nudgePb.NudgeInstance) *journeyFePb.ActivationWidgetSection {
	// Don't show milestone tracker for completed journeys
	completedStatuses := []journeyPb.ActorJourneyStatus{
		journeyPb.ActorJourneyStatus_ACTOR_JOURNEY_STATUS_COMPLETED,
		journeyPb.ActorJourneyStatus_ACTOR_JOURNEY_STATUS_COMPLETION_CONFIRMED,
	}
	if lo.Contains(completedStatuses, journeyInstance.GetStatus()) {
		return nil
	}

	// Build milestone steps
	milestoneSteps := make([]*journeyFePb.MilestoneTrackerSection_Milestone, 0, len(nudgeInstances))
	isCurrentActiveMilestone := true

	for _, nudgeInstance := range nudgeInstances {
		switch nudgeInstance.GetActorNudgeStatus() {
		case nudgePb.ActorNudgeStatus_ACTIVATED:
			milestoneSteps = append(milestoneSteps, getActiveMilestoneCard(nudgeInstance, isCurrentActiveMilestone))
			// Once we get the current milestone, we don't need to show the current milestone flag for the next active milestones
			isCurrentActiveMilestone = false
		case nudgePb.ActorNudgeStatus_COMPLETED:
			milestoneSteps = append(milestoneSteps, getCompletedMilestoneCard(nudgeInstance))
		default:
			// Ignore to show the nudge in the milestone tracker
		}
	}

	return &journeyFePb.ActivationWidgetSection{
		SectionType: journeyFePb.ActivationWidgetSection_SECTION_TYPE_MILESTONE_TRACKER,
		Section: &journeyFePb.ActivationWidgetSection_MilestoneSection{
			MilestoneSection: &journeyFePb.MilestoneTrackerSection{
				TrackerComponent: getTrackerComponent(journeyInstance),
				MilestoneSteps:   milestoneSteps,
			},
		},
	}
}

// getTrackerComponent creates the time countdown component if journey is expiring soon
func getTrackerComponent(journeyInstance *journeyPb.JourneyInstance) *journeyFePb.MilestoneTrackerSection_MilestoneTrackerHeader {
	// Calculate time remaining
	remainingHours := journeyInstance.GetExpireAt().AsTime().Sub(time.Now()).Hours()

	// Only show tracker for journeys expiring within 72 hours
	if remainingHours > 72 {
		return nil
	}

	// Format display text based on time remaining
	displayValue, fontColor, bgColor, timerImg := formatTimeRemaining(remainingHours)

	return &journeyFePb.MilestoneTrackerSection_MilestoneTrackerHeader{
		TrackerTitle: &ui.IconTextComponent{
			LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(
				timerImg, 16, 16),
			Texts: []*commontypes.Text{
				{
					FontColor:    fontColor,
					DisplayValue: &commontypes.Text_PlainString{PlainString: displayValue},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
				},
			},
		},
		TrackerBackground: &widget.BackgroundColour{
			Colour: &widget.BackgroundColour_BlockColour{BlockColour: bgColor},
		},
	}
}

// formatTimeRemaining formats remaining time as "X days left" or "X hours left"
// and returns the text & bg color & timer image for the tracker component
func formatTimeRemaining(remainingHours float64) (string, string, string, string) {
	days := int(remainingHours) / 24
	hours := int(remainingHours) % 24

	if days > 0 {
		return fmt.Sprintf("%d days left", days), "#C0723D", "#F6E1C1", "https://epifi-icons.pointz.in/home-v2/activation/timer-brown.png"
	}
	if hours > 0 {
		return "Last day to earn reward", "#6D3149", "#F0BECE", "https://epifi-icons.pointz.in/home-v2/activation/timer-red.png"
	}
	return "Time expired", "#6D3149", "#F0BECE", "https://epifi-icons.pointz.in/home-v2/activation/timer-red.png"
}

// getMilestoneWidgetBanner creates a banner for completed journeys with next steps
func getMilestoneWidgetBanner(journeyInstance *journeyPb.JourneyInstance) *journeyFePb.ActivationWidgetSection {
	// Show milestone widget banner for completed journeys
	completedStatuses := []journeyPb.ActorJourneyStatus{
		journeyPb.ActorJourneyStatus_ACTOR_JOURNEY_STATUS_COMPLETED,
		journeyPb.ActorJourneyStatus_ACTOR_JOURNEY_STATUS_COMPLETION_CONFIRMED,
	}
	if !lo.Contains(completedStatuses, journeyInstance.GetStatus()) {
		return nil
	}
	// If there are no next steps, don't show the banner
	if journeyInstance.GetDisplayConfig().GetNextJourneyInfo().GetTitle() == "" {
		return nil
	}
	remainingHours := journeyInstance.GetExpireAt().AsTime().Sub(time.Now()).Hours()

	// Convert to days and hours
	days := int(remainingHours) / 24
	hours := int(remainingHours) % 24

	timeUnit := "Days"
	timeValue := "00"
	switch {
	case days > 0:
		timeValue = fmt.Sprintf("%d", days)
	case hours > 0:
		timeUnit = "Hours"
		timeValue = fmt.Sprintf("%d", hours)
	}

	return &journeyFePb.ActivationWidgetSection{
		SectionType: journeyFePb.ActivationWidgetSection_SECTION_TYPE_MILESTONE_WIDGET_BANNER,
		Section: &journeyFePb.ActivationWidgetSection_BannerSection{
			BannerSection: &journeyFePb.MilestoneWidgetBanner{
				LeftIcon:    commontypes.GetVisualElementFromUrlHeightAndWidth(journeyInstance.GetDisplayConfig().GetNextJourneyInfo().GetImageUrl(), 56, 53),
				Description: commontypes.GetTextFromStringFontColourFontStyle(journeyInstance.GetDisplayConfig().GetNextJourneyInfo().GetTitle(), "#313234", commontypes.FontStyle_SUBTITLE_M),
				BackgroundColor: widget.GetLinearGradientBackgroundColour(90, []*widget.ColorStop{
					{
						Color:          "#EDF5EB",
						StopPercentage: 0,
					},
					{
						Color:          "#D5E6CE",
						StopPercentage: 100,
					},
				}),
				TimeLeft: &journeyFePb.MilestoneWidgetBanner_Timer{
					TimeUnit:        commontypes.GetTextFromStringFontColourFontStyle(timeUnit, "#E6E9ED", commontypes.FontStyle_OVERLINE_2XS_CAPS),
					TimeValue:       commontypes.GetTextFromStringFontColourFontStyle(timeValue, "#E6E9ED", commontypes.FontStyle_SUBTITLE_L),
					BackgroundColor: widget.GetBlockBackgroundColour("#242527"),
					TimerBgImage:    commontypes.GetVisualElementImageFromUrl("https://epifi-icons.s3.ap-south-1.amazonaws.com/home-v2/activation/timer-bg-image.png"),
				},
			},
		},
	}
}

// PerformCompletionAction updates the journey status to completion confirmed.
func (s *Service) PerformCompletionAction(ctx context.Context, req *journeyFePb.PerformCompletionActionRequest) (*journeyFePb.PerformCompletionActionResponse, error) {
	res, err := s.journeyClient.UpdateActorJourneyStatus(ctx, &journeyPb.UpdateActorJourneyStatusRequest{
		ActorJourneyId:     req.GetActorJourneyId(),
		ActorJourneyStatus: journeyPb.ActorJourneyStatus_ACTOR_JOURNEY_STATUS_COMPLETION_CONFIRMED,
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		logger.Error(ctx, "error updating actor journey status", zap.String("actor_journey_id", req.GetActorJourneyId()), zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()), zap.Error(rpcErr))
		return &journeyFePb.PerformCompletionActionResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(rpcErr.Error())},
		}, nil
	}

	return &journeyFePb.PerformCompletionActionResponse{
		RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
	}, nil
}
