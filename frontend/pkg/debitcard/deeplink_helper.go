package debitcard

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	fePb "github.com/epifi/gamma/api/frontend/card"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	dcScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/debitcard"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/pkg/accrual"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
)

// getDcActivationBenefits returns the activation benefits with migration-aware text and icons
func getDcActivationBenefits() []*widget.ImageTitleSubtitleElement {
	// Get migration-aware text and icon
	fiCoinsText := accrual.ReplaceCoinWithPointIfApplicable("Earn 2x Fi-Coins", nil)
	fiCoinsIcon := accrual.ReturnApplicableValue(
		"https://epifi-icons.pointz.in/card-images/fi_coins_img.png",
		"https://epifi-icons.pointz.in/FiCoinsToFiPoints/Fi_Points_logo.png",
		nil, true,
	).(string)

	return []*widget.ImageTitleSubtitleElement{
		{
			IconImage: &commontypes.Image{
				ImageType: commontypes.ImageType_PNG,
				ImageUrl:  "https://epifi-icons.pointz.in/card-images/discount_percent_img.png",
				Width:     62,
				Height:    62,
			},
			TitleText: &commontypes.Text{
				FontColor:    "#646464",
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Discounts on top brands"},
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS,
				},
			},
		},
		{
			IconImage: &commontypes.Image{
				ImageType: commontypes.ImageType_PNG,
				ImageUrl:  "https://epifi-icons.pointz.in/card-images/earth_rotation.png",
				Width:     62,
				Height:    62,
			},
			TitleText: &commontypes.Text{
				FontColor:    "#646464",
				DisplayValue: &commontypes.Text_PlainString{PlainString: "0 forex on select plans"},
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS,
				},
			},
		},
		{
			IconImage: &commontypes.Image{
				ImageType: commontypes.ImageType_PNG,
				ImageUrl:  fiCoinsIcon,
				Width:     62,
				Height:    62,
			},
			TitleText: &commontypes.Text{
				FontColor:    "#646464",
				DisplayValue: &commontypes.Text_PlainString{PlainString: fiCoinsText},
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS,
				},
			},
		},
		{
			IconImage: &commontypes.Image{
				ImageType: commontypes.ImageType_PNG,
				ImageUrl:  "https://epifi-icons.pointz.in/card-images/money_deck.png",
				Width:     62,
				Height:    62,
			},
			TitleText: &commontypes.Text{
				FontColor:    "#646464",
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Free ATM withdrawals"},
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS,
				},
			},
		},
	}
}

const (
	OffersTileImageUrl = "https://epifi-icons.pointz.in/card-images/Offers+banner.png"
)

func GetCardActivationLandingScreenDeeplink(cardId string, featureEnabled bool) (*dlPb.Deeplink, error) {
	qrCodeScanDl, err := GetQrCodeDeeplink(cardId, featureEnabled)
	if err != nil {
		return nil, err
	}
	screen := dlPb.Screen_DEBIT_CARD_PIN_ACTIVATION_SCREEN
	screenOptions := &dcScreenOptionsPb.DebitCardPinActivationScreenOptions{
		ScreenHeader: &widget.ImageTitleSubtitleElement{
			IconImage: &commontypes.Image{
				ImageType: commontypes.ImageType_PNG,
				ImageUrl:  OffersTileImageUrl,
				Width:     162,
				Height:    162,
			},
			TitleText: &commontypes.Text{
				FontColor:    "#383838",
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Activate your Debit Card"},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_1},
			},
			SubtitleText: &commontypes.Text{
				FontColor: "#646464",
				DisplayValue: &commontypes.Text_PlainString{
					PlainString: "By proceeding, you agree to enable online transactions and card swiping",
				},
				FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_S},
			},
		},
		ActivationBenefits: getDcActivationBenefits(),
		NextAction: &dcScreenOptionsPb.CtaWithAuthAction{
			ActivateCta: &dlPb.Cta{
				Text: "Activate card",
			},
			CardActionAuth: fePb.CardActionAuth_VALIDATE_UPI_PIN,
		},
		QrActivationText: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					FontColor:    "#646464",
					DisplayValue: &commontypes.Text_PlainString{PlainString: "Got your card kit? "},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
				},
				{
					FontColor:    "#00B899",
					DisplayValue: &commontypes.Text_PlainString{PlainString: "Activate via QR"},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
				},
			},
			Deeplink: qrCodeScanDl,
		},
		CardId: cardId,
	}
	return deeplinkV3.GetDeeplinkV3(screen, screenOptions)
}

func GetQrCodeDeeplink(cardId string, featureEnabled bool) (*dlPb.Deeplink, error) {
	screen := dlPb.Screen_DEBIT_CARD_QR_CODE_SCAN_SCREEN
	screenOptions := &dcScreenOptionsPb.DebitCardQrCodeScanScreenOptions{
		CardId: cardId,
		ScreenHeading: &commontypes.Text{
			FontColor:    "#FFFFFF",
			DisplayValue: &commontypes.Text_PlainString{PlainString: "Scan QR code to activate"},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
			},
		},
		BottomElement: &dcScreenOptionsPb.DebitCardQrCodeScanScreenOptions_BottomElement{
			Heading: &commontypes.Text{
				FontColor:    "#000000",
				BgColor:      "#FFFFFF",
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Debit Card benefits"},
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
				},
			},
			ActivationBenefits: getDcActivationBenefits(),
			BottomText: &commontypes.Text{
				FontColor:    "#383838",
				BgColor:      "#FFFFFF",
				DisplayValue: &commontypes.Text_PlainString{PlainString: "By proceeding, you agree to enable online\n transactions and card swiping"},
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_BODY_S,
				},
			},
		},
	}
	if !featureEnabled {
		screenOptions.BottomElement = nil
	}
	return deeplinkV3.GetDeeplinkV3(screen, screenOptions)
}
