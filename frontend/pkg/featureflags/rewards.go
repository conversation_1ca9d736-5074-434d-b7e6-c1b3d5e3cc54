//nolint:dupl
package featureflags

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"

	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/frontend/pkg/common"
	accrualPkg "github.com/epifi/gamma/pkg/accrual"
	"github.com/epifi/gamma/pkg/feature/release"
)

// IsFeatureRewardsCatalogMergedPageEnabledRequest is the request for IsFeatureRewardsCatalogMergedPageEnabled.
type IsFeatureRewardsCatalogMergedPageEnabledRequest struct {
	ActorId      string
	ExternalDeps *common.ExternalDependencies
}

// GetActorId returns the ActorId from the request.
func (r *IsFeatureRewardsCatalogMergedPageEnabledRequest) GetActorId() string {
	if r == nil {
		return ""
	}
	return r.ActorId
}

// GetExternalDeps returns the ExternalDependencies from the request.
func (r *IsFeatureRewardsCatalogMergedPageEnabledRequest) GetExternalDeps() *common.ExternalDependencies {
	if r == nil {
		return nil
	}
	return r.ExternalDeps
}

// Validate checks if the request is valid.
func (r *IsFeatureRewardsCatalogMergedPageEnabledRequest) Validate() error {
	env, _ := cfg.GetEnvironment()
	if cfg.IsTestEnv(env) {
		return fmt.Errorf("feature flags are not enabled in test environment")
	}

	if r == nil {
		return fmt.Errorf("request is nil")
	}
	if r.GetActorId() == "" {
		return fmt.Errorf("actorId is empty")
	}
	externalDeps := r.GetExternalDeps()
	if externalDeps == nil {
		return fmt.Errorf("external dependencies is nil")
	}
	if externalDeps.GetEvaluator() == nil {
		return fmt.Errorf("evaluator is nil")
	}
	return nil
}

// IsFeatureRewardsCatalogMergedPageEnabled checks if the rewards catalog merged page is enabled for the user
func IsFeatureRewardsCatalogMergedPageEnabled(ctx context.Context, req *IsFeatureRewardsCatalogMergedPageEnabledRequest) bool {
	if err := req.Validate(); err != nil {
		logger.Error(ctx, "Validation failed for IsFeatureRewardsCatalogMergedPageEnabledRequest", zap.Error(err))
		return false
	}

	var (
		actorId   = req.GetActorId()
		evaluator = req.GetExternalDeps().GetEvaluator()
	)

	var (
		feature           = types.Feature_FEATURE_REWARDS_CATALOG_MERGED_PAGE
		featureConstraint = release.NewCommonConstraintData(feature).WithActorId(actorId)
	)

	isFeatureEnabled, errEval := evaluator.Evaluate(ctx, featureConstraint)
	if errEval != nil {
		logger.Error(ctx, "error evaluating feature flag", zap.String(logger.FEATURE, feature.String()), zap.Error(errEval))
		isFeatureEnabled = false
	}
	return isFeatureEnabled
}

type IsFeatureFiCoinsToFiPointsPreMigrationPhaseEnabledRequest struct {
	ActorId      string
	ExternalDeps *common.ExternalDependencies
}

// GetActorId returns the ActorId from the request.
func (r *IsFeatureFiCoinsToFiPointsPreMigrationPhaseEnabledRequest) GetActorId() string {
	if r == nil {
		return ""
	}
	return r.ActorId
}

// GetExternalDeps returns the ExternalDependencies from the request.
func (r *IsFeatureFiCoinsToFiPointsPreMigrationPhaseEnabledRequest) GetExternalDeps() *common.ExternalDependencies {
	if r == nil {
		return nil
	}
	return r.ExternalDeps
}

// Validate checks if the request is valid.
func (r *IsFeatureFiCoinsToFiPointsPreMigrationPhaseEnabledRequest) Validate() error {
	env, _ := cfg.GetEnvironment()
	if cfg.IsTestEnv(env) {
		return fmt.Errorf("feature flags are not enabled in test environment")
	}

	if r == nil {
		return fmt.Errorf("request is nil")
	}
	if r.GetActorId() == "" {
		return fmt.Errorf("actorId is empty")
	}
	externalDeps := r.GetExternalDeps()
	if externalDeps == nil {
		return fmt.Errorf("external dependencies is nil")
	}
	if externalDeps.GetEvaluator() == nil {
		return fmt.Errorf("evaluator is nil")
	}
	return nil
}

// IsFeatureFiCoinsToFiPointsPreMigrationPhaseEnabled checks if the fi coins to fi points pre-migration phase feature is enabled for the user
func IsFeatureFiCoinsToFiPointsPreMigrationPhaseEnabled(ctx context.Context, req *IsFeatureFiCoinsToFiPointsPreMigrationPhaseEnabledRequest) bool {
	if err := req.Validate(); err != nil {
		logger.Error(ctx, "Validation failed for IsFeatureFiCoinsToFiPointsPreMigrationPhaseEnabledRequest", zap.Error(err))
		return false
	}
	var (
		actorId   = req.GetActorId()
		evaluator = req.GetExternalDeps().GetEvaluator()
	)
	var (
		feature           = types.Feature_FEATURE_FI_COINS_TO_FI_POINTS_PRE_MIGRATION_PHASE
		featureConstraint = release.NewCommonConstraintData(feature).WithActorId(actorId)
	)

	isFeatureEnabled, errEval := evaluator.Evaluate(ctx, featureConstraint)
	if errEval != nil {
		logger.Error(ctx, "error evaluating feature flag", zap.String(logger.FEATURE, feature.String()), zap.Error(errEval))
		isFeatureEnabled = false
	}

	return isFeatureEnabled && time.Now().Before(accrualPkg.GetFiCoinsToFiPointsMigrationTime())
}

type IsFeatureFiCoinsToFiPointsDuringMigrationPhaseEnabledRequest struct {
	ActorId      string
	ExternalDeps *common.ExternalDependencies
}

// GetActorId returns the ActorId from the request.
func (r *IsFeatureFiCoinsToFiPointsDuringMigrationPhaseEnabledRequest) GetActorId() string {
	if r == nil {
		return ""
	}
	return r.ActorId
}

// GetExternalDeps returns the ExternalDependencies from the request.
func (r *IsFeatureFiCoinsToFiPointsDuringMigrationPhaseEnabledRequest) GetExternalDeps() *common.ExternalDependencies {
	if r == nil {
		return nil
	}
	return r.ExternalDeps
}

// Validate checks if the request is valid.
func (r *IsFeatureFiCoinsToFiPointsDuringMigrationPhaseEnabledRequest) Validate() error {
	env, _ := cfg.GetEnvironment()
	if cfg.IsTestEnv(env) {
		return fmt.Errorf("feature flags are not enabled in test environment")
	}

	if r == nil {
		return fmt.Errorf("request is nil")
	}
	if r.GetActorId() == "" {
		return fmt.Errorf("actorId is empty")
	}
	externalDeps := r.GetExternalDeps()
	if externalDeps == nil {
		return fmt.Errorf("external dependencies is nil")
	}
	if externalDeps.GetEvaluator() == nil {
		return fmt.Errorf("evaluator is nil")
	}
	return nil
}

// IsFeatureFiCoinsToFiPointsDuringMigrationPhaseEnabled checks if the fi coins to fi points during migration phase feature is enabled for the user
func IsFeatureFiCoinsToFiPointsDuringMigrationPhaseEnabled(ctx context.Context, req *IsFeatureFiCoinsToFiPointsDuringMigrationPhaseEnabledRequest) bool {
	if err := req.Validate(); err != nil {
		logger.Error(ctx, "Validation failed for IsFeatureFiCoinsToFiPointsDuringMigrationPhaseEnabledRequest", zap.Error(err))
		return false
	}
	var (
		actorId   = req.GetActorId()
		evaluator = req.GetExternalDeps().GetEvaluator()
	)
	var (
		feature           = types.Feature_FEATURE_FI_COINS_TO_FI_POINTS_DURING_MIGRATION_PHASE
		featureConstraint = release.NewCommonConstraintData(feature).WithActorId(actorId)
	)

	isFeatureEnabled, errEval := evaluator.Evaluate(ctx, featureConstraint)
	if errEval != nil {
		logger.Error(ctx, "error evaluating feature flag", zap.String(logger.FEATURE, feature.String()), zap.Error(errEval))
		isFeatureEnabled = false
	}

	return isFeatureEnabled && accrualPkg.IsFiCoinsToFiPointsMigrationInProgress()
}

type IsFeatureFiCoinsToFiPointsPostMigrationPhaseEnabledRequest struct {
	ActorId      string
	ExternalDeps *common.ExternalDependencies
}

// GetActorId returns the ActorId from the request.
func (r *IsFeatureFiCoinsToFiPointsPostMigrationPhaseEnabledRequest) GetActorId() string {
	if r == nil {
		return ""
	}
	return r.ActorId
}

// GetExternalDeps returns the ExternalDependencies from the request.
func (r *IsFeatureFiCoinsToFiPointsPostMigrationPhaseEnabledRequest) GetExternalDeps() *common.ExternalDependencies {
	if r == nil {
		return nil
	}
	return r.ExternalDeps
}

// Validate checks if the request is valid.
func (r *IsFeatureFiCoinsToFiPointsPostMigrationPhaseEnabledRequest) Validate() error {
	env, _ := cfg.GetEnvironment()
	if cfg.IsTestEnv(env) {
		return fmt.Errorf("feature flags are not enabled in test environment")
	}

	if r == nil {
		return fmt.Errorf("request is nil")
	}
	if r.GetActorId() == "" {
		return fmt.Errorf("actorId is empty")
	}
	externalDeps := r.GetExternalDeps()
	if externalDeps == nil {
		return fmt.Errorf("external dependencies is nil")
	}
	if externalDeps.GetEvaluator() == nil {
		return fmt.Errorf("evaluator is nil")
	}
	return nil
}

// IsFeatureFiCoinsToFiPointsPostMigrationPhaseEnabled checks if the fi coins to fi points post migration phase feature is enabled for the user
func IsFeatureFiCoinsToFiPointsPostMigrationPhaseEnabled(ctx context.Context, req *IsFeatureFiCoinsToFiPointsPostMigrationPhaseEnabledRequest) bool {
	if err := req.Validate(); err != nil {
		logger.Error(ctx, "Validation failed for IsFeatureFiCoinsToFiPointsPostMigrationPhaseEnabledRequest", zap.Error(err))
		return false
	}
	var (
		actorId   = req.GetActorId()
		evaluator = req.GetExternalDeps().GetEvaluator()
	)
	var (
		feature           = types.Feature_FEATURE_FI_COINS_TO_FI_POINTS_POST_MIGRATION_PHASE
		featureConstraint = release.NewCommonConstraintData(feature).WithActorId(actorId)
	)

	isFeatureEnabled, errEval := evaluator.Evaluate(ctx, featureConstraint)
	if errEval != nil {
		logger.Error(ctx, "error evaluating feature flag", zap.String(logger.FEATURE, feature.String()), zap.Error(errEval))
		isFeatureEnabled = false
	}

	return isFeatureEnabled && time.Now().After(accrualPkg.GetFiCoinsToFiPointsMigrationTime())
}
