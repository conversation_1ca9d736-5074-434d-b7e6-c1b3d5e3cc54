// nolint:funlen,dupl
package compliance

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"github.com/epifi/gamma/api/cx/watson"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/consent"
	vgEkycPb "github.com/epifi/gamma/api/vendorgateway/ekyc"
	vgPanPb "github.com/epifi/gamma/api/vendorgateway/pan"
	"github.com/epifi/gamma/pkg/obfuscator"

	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/gogo/status"
	"google.golang.org/grpc/codes"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epificontext"
	eventsPkg "github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/frontend/app/apputils"
	goutils "github.com/epifi/be-common/pkg/go_utils"
	"github.com/epifi/be-common/pkg/monitoring"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/gamma/api/accounts/enums"
	empPb "github.com/epifi/gamma/api/employment"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/kyc"
	kycPb "github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/bankcust/compliance/events"
	kycPkg "github.com/epifi/gamma/pkg/kyc"

	"github.com/epifi/be-common/api/rpc"
	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/lock"
	"github.com/epifi/be-common/pkg/logger"

	operationalStatusPb "github.com/epifi/gamma/api/accounts/operstatus"
	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/bankcust"
	compliancePb "github.com/epifi/gamma/api/bankcust/compliance"
	"github.com/epifi/gamma/api/dynamic_elements"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	"github.com/epifi/gamma/bankcust/compliance/dao"
	"github.com/epifi/gamma/bankcust/compliance/metrics"
	"github.com/epifi/gamma/bankcust/config/genconf"
	"github.com/epifi/gamma/pkg/cx"
)

type KYCComplianceService struct {
	complianceDao  dao.KYCComplianceDao
	genConf        *genconf.KYCCompliance
	usersClient    user.UsersClient
	authClient     auth.AuthClient
	timeClient     datetime.Time
	opStatusClient operationalStatusPb.OperationalStatusServiceClient
	savingsClient  savings.SavingsClient
	bcClient       bankcust.BankCustomerServiceClient
	idGen          idgen.IdGenerator
	lockManager    lock.ILockManager
	txnExecutor    storagev2.TxnExecutor
	eventBroker    eventsPkg.Broker
	kycClient      kycPb.KycClient
	empFeClient    empPb.EmploymentFeClient
	orchestrator   Orchestrator
	vgEkycClient   vgEkycPb.EKYCClient
	vgPanClient    vgPanPb.PANClient
	vgCustClient   customer.CustomerClient
	watsonClient   watson.WatsonClient
}

func NewKYCComplianceService(complianceDao dao.KYCComplianceDao, genConf *genconf.Config, usersClient user.UsersClient,
	authClient auth.AuthClient, timeClient datetime.Time, opStatusClient operationalStatusPb.OperationalStatusServiceClient,
	bcClient bankcust.BankCustomerServiceClient, savingsClient savings.SavingsClient, idGen idgen.IdGenerator, lockMgr lock.ILockManager,
	txnExecutor storagev2.TxnExecutor, eventBroker eventsPkg.Broker, kycClient kycPb.KycClient, empFeClient empPb.EmploymentFeClient,
	vgEkycClient vgEkycPb.EKYCClient, vgPanClient vgPanPb.PANClient, vgCustClient customer.CustomerClient, watsonClient watson.WatsonClient) *KYCComplianceService {
	k := &KYCComplianceService{
		complianceDao:  complianceDao,
		genConf:        genConf.KYCCompliance(),
		usersClient:    usersClient,
		authClient:     authClient,
		timeClient:     timeClient,
		opStatusClient: opStatusClient,
		bcClient:       bcClient,
		savingsClient:  savingsClient,
		idGen:          idGen,
		lockManager:    lockMgr,
		txnExecutor:    txnExecutor,
		eventBroker:    eventBroker,
		kycClient:      kycClient,
		empFeClient:    empFeClient,
		vgEkycClient:   vgEkycClient,
		vgPanClient:    vgPanClient,
		vgCustClient:   vgCustClient,
		watsonClient:   watsonClient,
	}
	k.orchestrator = k
	return k
}

const (
	campaignNameField       = "campaign_name"
	periodicKYCCampaignName = "CAMPAIGN_NAME_PERIODIC_KYC"
	reKycDueDateField       = "re_kyc_due_date"
	isAccountFrozenField    = "is_account_frozen"
	reKycStatusField        = "re_kyc_status"
	yetToAttempt            = "YET_TO_ATTEMPT"
	InProgress              = "IN_PROGRESS"
	Success                 = "SUCCESS"
	smsBody                 = "KYC Y"
)

var (
	_                         compliancePb.ComplianceServer = (*KYCComplianceService)(nil)
	allowedHomeScreenSections                               = []dynamic_elements.HomeScreenAdditionalInfo_Section{
		dynamic_elements.HomeScreenAdditionalInfo_SECTION_GTM_POPUP,
		dynamic_elements.HomeScreenAdditionalInfo_SECTION_TOP_BAR,
	}
)

func (k *KYCComplianceService) GetPeriodicKYCDetails(ctx context.Context, req *compliancePb.GetPeriodicKYCDetailsRequest) (*compliancePb.GetPeriodicKYCDetailsResponse, error) {
	periodicKYCDetails, err := k.complianceDao.GetAllAttempts(ctx, dao.WithActorId(req.GetActorId()))
	if err != nil {
		logger.Error(ctx, "error in getting periodic kyc details from db", zap.Error(err))
		return &compliancePb.GetPeriodicKYCDetailsResponse{
			Status: rpcPb.StatusOk(),
		}, nil
	}
	return &compliancePb.GetPeriodicKYCDetailsResponse{
		Status:             rpcPb.StatusOk(),
		PeriodicKYCDetails: buildPeriodicKycDetails(periodicKYCDetails...),
	}, nil
}

func (k *KYCComplianceService) GetPeriodicKYCDetail(ctx context.Context, req *compliancePb.GetPeriodicKYCDetailRequest) (*compliancePb.GetPeriodicKYCDetailResponse, error) {
	var (
		notFound = func() *compliancePb.GetPeriodicKYCDetailResponse {
			return &compliancePb.GetPeriodicKYCDetailResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}
		}
		comp *compliancePb.KYCCompliance
		err  error
	)
	bankCustomer, err := k.getBankCustomer(ctx, req.GetActorId(), commonvgpb.Vendor_FEDERAL_BANK)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return nil, err
	}
	if bankCustomer.GetStatus() != bankcust.Status_STATUS_ACTIVE {
		return notFound(), nil
	}
	if req.GetFetchLatest() {
		_, comp, err = k.fetchAndStoreLatestKYCComplianceFromVendor(ctx, req.GetActorId(), bankCustomer.GetVendorCustomerId())
	} else {
		opStatus, errOpStatus := k.fetchOperationalStatusFromVendor(ctx, req.GetActorId(), commonvgpb.Vendor_FEDERAL_BANK, operationalStatusPb.GetOperationalStatusRequest_DATA_FRESHNESS_LAST_KNOWN)
		if errOpStatus != nil && !errors.Is(errOpStatus, epifierrors.ErrRecordNotFound) {
			return &compliancePb.GetPeriodicKYCDetailResponse{
				Status: rpcPb.StatusInternalWithDebugMsg(errOpStatus.Error()),
			}, nil
		}
		comp, err = k.fetchLastKnownKYCComplianceDataFromVendor(ctx, req.GetActorId(), bankCustomer.GetVendorCustomerId(), opStatus)
	}
	if errors.Is(err, epifierrors.ErrRecordNotFound) {
		return notFound(), nil
	}
	if err != nil {
		return &compliancePb.GetPeriodicKYCDetailResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	if comp.GetKYCDueAt().AsTime().After(k.timeClient.Now().Add(k.genConf.DueDateThreshold())) {
		comp.KYCComplianceStatus = compliancePb.KYCComplianceStatus_KYC_COMPLIANCE_STATUS_COMPLIED
	}
	return &compliancePb.GetPeriodicKYCDetailResponse{
		Status:            rpcPb.StatusOk(),
		PeriodicKYCDetail: buildPeriodicKycDetails(comp)[0],
	}, nil
}

func buildPeriodicKycDetails(details ...*compliancePb.KYCCompliance) []*compliancePb.PeriodicKYCDetail {
	periodicDetails := make([]*compliancePb.PeriodicKYCDetail, 0)
	for _, detail := range details {
		periodicDetails = append(periodicDetails, &compliancePb.PeriodicKYCDetail{
			KYCComplianceStatus: detail.GetKYCComplianceStatus(),
			AttemptSource:       detail.GetAttemptSource(),
			KYCCompliedAt:       detail.GetKYCCompliedAt(),
			KYCDueAt:            detail.GetKYCDueAt(),
		})
	}
	return periodicDetails
}

func (k *KYCComplianceService) getLatestKYCCompletedAttempt(ctx context.Context, actorId string) (*kycPb.KycAttempt, error) {
	resp, err := k.kycClient.GetKYCHistory(ctx, &kycPb.GetKYCHistoryRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		logger.Error(ctx, "error in getting kyc history", zap.Error(rpcErr))
		return nil, rpcErr
	}
	attempts := resp.GetKycAttempts()
	sort.SliceStable(attempts, func(i, j int) bool {
		// sorting in decreasing order of created date
		return attempts[i].GetCreatedAt().AsTime().After(attempts[j].GetCreatedAt().AsTime())
	})
	var latestKYCAttempt *kycPb.KycAttempt
	for _, attempt := range attempts {
		if attempt.GetState() != kycPb.KYCState_EKYC_DATA_RECEIVED || attempt.GetRequestParams().GetEkycSource() != kycPb.EkycSource_EKYC_SOURCE_RE_KYC {
			continue
		}
		kycVendorDataResp, respErr := k.kycClient.GetKYCVendorData(ctx, &kyc.GetKYCVendorDataRequest{
			Identifier: &kyc.GetKYCVendorDataRequest_AttemptId{
				AttemptId: attempt.GetKycAttemptId(),
			},
		})
		if rpcErr := epifigrpc.RPCError(kycVendorDataResp, respErr); rpcErr != nil {
			if rpcPb.StatusFromError(rpcErr).IsRecordNotFound() {
				continue
			}
			logger.Error(ctx, "error in getting vendor data", zap.Error(rpcErr))
			return nil, rpcErr
		}
		latestKYCAttempt = attempt
		break
	}
	return latestKYCAttempt, nil
}

func (k *KYCComplianceService) handleEKYCCompletedCheck(ctx context.Context, comp *compliancePb.KYCCompliance) (*deeplink.Deeplink, *kycPb.KycAttempt, error) {
	attempt, err := k.getLatestKYCCompletedAttempt(ctx, comp.GetActorId())
	if err != nil {
		return nil, attempt, err
	}
	if attempt == nil {
		dl, dlErr := k.startEKYCDeeplink(ctx, comp)
		if dlErr != nil {
			return dl, attempt, dlErr
		}
		return dl, attempt, err
	}
	if comp.GetProfileUpdateDetails().GetEKYCClientRequestId() != attempt.GetClientReqId() {
		if comp.GetProfileUpdateDetails() == nil {
			comp.ProfileUpdateDetails = &compliancePb.ProfileUpdateDetails{
				EKYCClientRequestId: "",
			}
		}
		comp.GetProfileUpdateDetails().EKYCClientRequestId = attempt.GetClientReqId()
		err = k.complianceDao.Update(ctx, comp, []compliancePb.KYCComplianceFieldMask{compliancePb.KYCComplianceFieldMask_KYC_COMPLIANCE_FIELD_MASK_PROFILE_UPDATE_DETAILS_PROFILE})
		if err != nil {
			logger.Error(ctx, "error in updating compliance details", zap.Error(err))
			return nil, attempt, err
		}
	}
	return nil, attempt, nil
}

func (k *KYCComplianceService) PeriodicKYCCallback(ctx context.Context, req *compliancePb.PeriodicKYCCallbackRequest) (*compliancePb.PeriodicKYCCallbackResponse, error) {
	var (
		err         error
		nextAction  *deeplink.Deeplink
		clientReqId = req.GetClientReqId()
	)
	comp, daoErr := k.complianceDao.Get(ctx, dao.WithActorId(req.GetActorId()))
	if daoErr != nil {
		if errors.Is(daoErr, epifierrors.ErrRecordNotFound) {
			return &compliancePb.PeriodicKYCCallbackResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "error in getting compliance data", zap.Error(daoErr))
		return nil, daoErr
	}
	if clientReqId == "" {
		clientReqId = comp.GetId()
	}
	action := goutils.Enum(req.GetAction(), compliancePb.Action_value, compliancePb.Action_ACTION_UNSPECIFIED)
	if action == compliancePb.Action_ACTION_UNSPECIFIED {
		logger.Error(ctx, "action is present in PeriodicKYCCallback request")
		return &compliancePb.PeriodicKYCCallbackResponse{
			Status: rpcPb.StatusInvalidArgument(),
		}, nil
	}
	switch action {
	// not getting used as of now
	case compliancePb.Action_ACTION_CONFIRM_PERIODIC_KYC_DETAILS:
		nextAction, err = k.getNextActionForConfirmDetails(ctx, clientReqId, req.GetActorId())
	case compliancePb.Action_ACTION_USER_SMS_TRIGGERED_AT:
		metrics.PushMetricsForSMSFlow(ctx, comp)
		if !cx.IsIncidentPresent(ctx, k.watsonClient, req.GetActorId(), events.WatsonIssueCategoryId) {
			k.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), events.NewReKYCAutoIdCreationEvent(req.GetActorId()))
		}
		opStatus, errOpStatus := k.fetchOperationalStatusFromVendor(ctx, req.GetActorId(), commonvgpb.Vendor_FEDERAL_BANK, operationalStatusPb.GetOperationalStatusRequest_DATA_FRESHNESS_LAST_KNOWN)
		if errOpStatus != nil && !errors.Is(errOpStatus, epifierrors.ErrRecordNotFound) {
			return &compliancePb.PeriodicKYCCallbackResponse{
				Status: rpcPb.StatusInternalWithDebugMsg(errOpStatus.Error()),
			}, nil
		}
		nextAction, err = k.handleUserSMSTriggeredAt(ctx, req.GetActorId(), req.GetClientReqId(), opStatus)
	case compliancePb.Action_ACTION_KYC_CTA_CLICKED_AT:
		if !cx.IsIncidentPresent(ctx, k.watsonClient, req.GetActorId(), events.WatsonIssueCategoryId) {
			k.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), events.NewReKYCAutoIdCreationEvent(req.GetActorId()))
		}
		metrics.PushMetricsForDocUploadedToFedPortal(ctx, comp, k.genConf.KYCCtaClickedAtThreshold())
		nextAction, err = k.handleKYCCTAClickedAt(ctx, req.GetActorId(), req.GetClientReqId())
		// get action, make update profile update, read KYC status
	case compliancePb.Action_ACTION_EKYC_COMPLETED:
		dl, attempt, ekycErr := k.handleEKYCCompletedCheck(ctx, comp)
		if ekycErr != nil {
			logger.Error(ctx, "error in getting ekyc deeplink error", zap.Error(ekycErr))
			return &compliancePb.PeriodicKYCCallbackResponse{
				Status: rpcPb.StatusInternalWithDebugMsg(ekycErr.Error()),
			}, nil
		}
		if dl != nil {
			return &compliancePb.PeriodicKYCCallbackResponse{
				Status:     rpcPb.StatusOk(),
				NextAction: dl,
			}, nil
		}
		// Todo(Bhabtosh): Need to implement profile update enquiry for aadhaar update flow, then dedupe check to confirm aadhaar is updated and then again profile update for rekyc in the same flow

		opStatus, errOpStatus := k.fetchOperationalStatusFromVendor(ctx, req.GetActorId(), commonvgpb.Vendor_FEDERAL_BANK, operationalStatusPb.GetOperationalStatusRequest_DATA_FRESHNESS_LAST_KNOWN)
		if errOpStatus != nil && !errors.Is(errOpStatus, epifierrors.ErrRecordNotFound) {
			return &compliancePb.PeriodicKYCCallbackResponse{
				Status: rpcPb.StatusInternalWithDebugMsg(errOpStatus.Error()),
			}, nil
		}

		nextAction = getAlreadyCompletedDL(opStatus.GetFreezeStatus() == enums.FreezeStatus_FREEZE_STATUS_DEBIT_FREEZE)

		isAadharUpdate := IsAadhaarUpdateRequired(comp.GetVendorCustomerId())

		switch {
		case isAadharUpdate && comp.GetProfileUpdateDetails().GetAadhaarUpdateDetails().GetAadhaarUpdateStatus() == compliancePb.AadhaarUpdateStatus_AADHAAR_UPDATE_STATUS_IN_PROGRESS:
			logger.Info(ctx, "aadhaar update in progress")
			return k.handleAadhaarUpdateInProgress(ctx, comp, attempt, nextAction)

		case isAadharUpdate:
			logger.Info(ctx, "starting aadhaar update")
			aadhaarUpdErr := k.startAadhaarUpdateAtBank(ctx, comp, attempt)
			return k.handleAadhaarUpdateError(aadhaarUpdErr, nextAction)

		default:
			logger.Info(ctx, "starting profile update for rekyc")
			profileUpdErr := k.startProfileUpdateAtBank(ctx, comp, attempt)
			return k.handleProfileUpdateError(profileUpdErr, nextAction)
		}
	}

	if err != nil {
		return &compliancePb.PeriodicKYCCallbackResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	return &compliancePb.PeriodicKYCCallbackResponse{
		Status:     rpcPb.StatusOk(),
		NextAction: nextAction,
	}, nil
}

// Helper function to create standardized responses
func createCallbackResponse(status *rpcPb.Status, nextAction *deeplink.Deeplink) *compliancePb.PeriodicKYCCallbackResponse {
	return &compliancePb.PeriodicKYCCallbackResponse{
		Status:     status,
		NextAction: nextAction,
	}
}

func (k *KYCComplianceService) handleAadhaarUpdateInProgress(ctx context.Context, comp *compliancePb.KYCCompliance, attempt *kycPb.KycAttempt, nextAction *deeplink.Deeplink) (*compliancePb.PeriodicKYCCallbackResponse, error) {
	profileUpdEnqErr := k.checkSuccessfulAadhaarUpdateAtVendor(ctx, comp, attempt)
	if profileUpdEnqErr != nil {
		switch {
		case errors.Is(profileUpdEnqErr, epifierrors.ErrInvalidArgument):
			return createCallbackResponse(rpcPb.StatusOk(), getEkycFailedDL()), nil
		case errors.Is(profileUpdEnqErr, epifierrors.ErrInProgress):
			return createCallbackResponse(rpcPb.StatusOk(), nextAction), nil
		default:
			return createCallbackResponse(rpcPb.StatusInternalWithDebugMsg(profileUpdEnqErr.Error()), nil), nil
		}
	}
	logger.Info(ctx, "aadhaar update successful, starting profile update")
	profileUpdErr := k.startProfileUpdateAtBank(ctx, comp, attempt)
	if profileUpdErr != nil {
		if errors.Is(profileUpdErr, epifierrors.ErrPermissionDenied) {
			return createCallbackResponse(rpcPb.StatusOk(), getEkycFailedDL()), nil
		}
		return createCallbackResponse(rpcPb.StatusInternalWithDebugMsg(profileUpdErr.Error()), nil), nil
	}

	return createCallbackResponse(rpcPb.StatusOk(), nextAction), nil
}

func (k *KYCComplianceService) handleAadhaarUpdateError(err error, nextAction *deeplink.Deeplink) (*compliancePb.PeriodicKYCCallbackResponse, error) {
	if err == nil {
		return createCallbackResponse(rpcPb.StatusOk(), nextAction), nil
	}

	if errors.Is(err, epifierrors.ErrPermissionDenied) {
		return createCallbackResponse(rpcPb.StatusOk(), getEkycFailedDLForAadhaarUpdate()), nil
	}

	return createCallbackResponse(rpcPb.StatusInternalWithDebugMsg(err.Error()), nil), nil
}

func (k *KYCComplianceService) handleProfileUpdateError(err error, nextAction *deeplink.Deeplink) (*compliancePb.PeriodicKYCCallbackResponse, error) {
	if err == nil {
		return createCallbackResponse(rpcPb.StatusOk(), nextAction), nil
	}

	if errors.Is(err, epifierrors.ErrPermissionDenied) {
		return createCallbackResponse(rpcPb.StatusOk(), getEkycFailedDL()), nil
	}

	return createCallbackResponse(rpcPb.StatusInternalWithDebugMsg(err.Error()), nil), nil
}

func (k *KYCComplianceService) startProfileUpdateAtBank(ctx context.Context, comp *compliancePb.KYCCompliance, attempt *kycPb.KycAttempt) error {
	profileUpdClientReqId := k.idGen.GetInAlphaNumeric(idgen.PeriodicKYC)
	panNumber := ""

	err := k.updateProfileAtBank(ctx, profileUpdClientReqId, attempt.GetKycAttemptId(), comp.GetActorId(), panNumber, bankcust.ProfileField_PROFILE_FIELD_RE_KYC)
	if err != nil {
		return err
	}

	metrics.PushMetricsForEKYCFlow(ctx, comp)

	if comp.GetProfileUpdateDetails() == nil {
		comp.ProfileUpdateDetails = &compliancePb.ProfileUpdateDetails{}
	}

	comp.ProfileUpdateDetails.ProfileUpdateStartedAt = timestampPb.New(k.timeClient.Now())
	comp.ProfileUpdateDetails.ProfileUpdateRequestId = profileUpdClientReqId
	if err = k.complianceDao.Update(ctx, comp, []compliancePb.KYCComplianceFieldMask{
		compliancePb.KYCComplianceFieldMask_KYC_COMPLIANCE_FIELD_MASK_PROFILE_UPDATE_DETAILS_PROFILE_UPDATE_STARTED_AT,
		compliancePb.KYCComplianceFieldMask_KYC_COMPLIANCE_FIELD_MASK_PROFILE_UPDATE_DETAILS_PROFILE_UPDATE_REQUEST_ID,
	}); err != nil {
		logger.Error(ctx, "error in updating compliance record with profile update details", zap.Error(err))
		return err
	}
	logger.Info(ctx, "update profile at bank started for periodic kyc")
	return nil
}
func (k *KYCComplianceService) checkSuccessfulAadhaarUpdateAtVendor(ctx context.Context, comp *compliancePb.KYCCompliance, kycAttempt *kycPb.KycAttempt) error {
	if comp.GetProfileUpdateDetails().GetProfileUpdateRequestId() == "" {
		return epifierrors.ErrInvalidArgument
	}
	enquiryRes, enquiryErr := k.bcClient.CheckProfileUpdateStatus(ctx, &bankcust.CheckProfileUpdateStatusRequest{
		ClientRequestId: comp.GetProfileUpdateDetails().GetProfileUpdateRequestId(),
	})
	if rpcErr := epifigrpc.RPCError(enquiryRes, enquiryErr); rpcErr != nil {
		switch {

		case rpc.StatusFromError(rpcErr).IsInProgress():
			return epifierrors.ErrInProgress

		case rpc.StatusFromError(rpcErr).IsRecordNotFound():
			return epifierrors.ErrRecordNotFound

		case rpc.StatusFromError(rpcErr).GetCode() == uint32(bankcust.CheckProfileUpdateStatusResponse_MANUAL_INTERVENTION):
			logger.Info(ctx, "profile update workflow for aadhaar update stuck in manual intervention")
			monitoring.KibanaInfoServiceMonitor(ctx, cfg.ONBOARDING_SERVICE, "aadhaar update through profile update stuck in manual intervention", zap.String(logger.ACTOR_ID_V2, comp.GetActorId()), zap.String(logger.VENDOR_REUQEST, comp.GetProfileUpdateDetails().GetProfileUpdateRequestId()))
			return epifierrors.ErrInProgress

		case rpc.StatusFromError(rpcErr).GetCode() == uint32(bankcust.CheckProfileUpdateStatusResponse_UPDATE_FAILED):
			logger.Info(ctx, "profile update workflow failed")
			monitoring.KibanaInfoServiceMonitor(ctx, cfg.ONBOARDING_SERVICE, "aadhaar update through profile update failed", zap.String(logger.ACTOR_ID_V2, comp.GetActorId()), zap.String(logger.VENDOR_REUQEST, comp.GetProfileUpdateDetails().GetProfileUpdateRequestId()))

			comp.ProfileUpdateDetails.AadhaarUpdateDetails.AadhaarUpdateStatus = compliancePb.AadhaarUpdateStatus_AADHAAR_UPDATE_STATUS_FAILED
			comp.ProfileUpdateDetails.AadhaarUpdateDetails.AadhaarUpdateFailureReason = compliancePb.AadhaarUpdateFailureReason_AADHAAR_UPDATE_FAILURE_REASON_PROFILE_UPDATE_FAILED

			err := k.updateComplianceDetails(ctx, comp, []compliancePb.KYCComplianceFieldMask{
				compliancePb.KYCComplianceFieldMask_KYC_COMPLIANCE_FIELD_MASK_AADHAAR_UPDATE_DETAILS_AADHAAR_UPDATE_STATUS,
				compliancePb.KYCComplianceFieldMask_KYC_COMPLIANCE_FIELD_MASK_AADHAAR_UPDATE_DETAILS_AADHAAR_UPDATE_FAILURE_REASON,
			})
			if err != nil {
				return err
			}
			return nil
		default:
			return rpcErr
		}
	}
	logger.Info(ctx, "aadhaar update successful, checking dedupe to verify")
	aUser, err := k.getUser(ctx, comp.GetActorId())
	if err != nil {
		logger.Error(ctx, "error in GetUser", zap.Error(err))
		return err
	}

	resp, errResp := k.vgCustClient.DedupeCheck(ctx, &customer.DedupeCheckRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		},
		PanNumber:       aUser.GetProfile().GetPAN(),
		PhoneNumber:     aUser.GetProfile().GetPhoneNumber(),
		UserId:          aUser.GetId(),
		DateOfBirth:     aUser.GetProfile().GetDateOfBirth(),
		RequestId:       idgen.FederalRandomDigitsSequence("DDPREK", 5),
		UidReferenceKey: kycAttempt.GetRequestParams().GetEkycRrnNo(),
	})
	if err = epifigrpc.RPCError(resp, errResp); err != nil {
		logger.Error(ctx, "error in fetching dedupe status", zap.Error(err))
		return err
	}

	if resp.GetAadhaarFlag() != customer.AadhaarFlag_AADHAAR_FLAG_EXISTS {
		logger.Info(ctx, "invalid aadhaar flag in dedupe post aadhar update through profile update")
		monitoring.KibanaInfoServiceMonitor(ctx, cfg.ONBOARDING_SERVICE, "invalid aadhaar flag in dedupe post aadhar update through profile update", zap.String(logger.ACTOR_ID_V2, comp.GetActorId()), zap.String(logger.FLAG, resp.GetAadhaarFlag().String()))
		return fmt.Errorf("invalid aadhaar flag in dedupe post aadhar update through profile update")
	}

	comp.ProfileUpdateDetails.AadhaarUpdateDetails.AadhaarUpdateStatus = compliancePb.AadhaarUpdateStatus_AADHAAR_UPDATE_STATUS_COMPLETED
	err = k.updateComplianceDetails(ctx, comp, []compliancePb.KYCComplianceFieldMask{compliancePb.KYCComplianceFieldMask_KYC_COMPLIANCE_FIELD_MASK_AADHAAR_UPDATE_DETAILS_AADHAAR_UPDATE_STATUS})
	if err != nil {
		return err
	}
	monitoring.KibanaInfoServiceMonitor(ctx, cfg.ONBOARDING_SERVICE, "aadhar update successful through profile update", zap.String(logger.ACTOR_ID_V2, comp.GetActorId()), zap.String(logger.FLAG, resp.GetAadhaarFlag().String()))
	logger.Info(ctx, "aadhaar updated successfully, now starting rekyc")

	return nil
}

func (k *KYCComplianceService) updateProfileAtBank(ctx context.Context, profileUpdClientReqId, kycAttemptId, actorId, panNumber string, profileFields bankcust.ProfileField) error {
	updRes, updErr := k.bcClient.UpdateProfileAtBank(ctx, &bankcust.UpdateProfileAtBankRequest{
		Channel:      bankcust.ProfileUpdateChannel_PROFILE_UPDATE_CHANNEL_APP,
		KycAttemptId: kycAttemptId,
		ActorId:      actorId,
		PanNumber:    panNumber,
		Vendor:       commonvgpb.Vendor_FEDERAL_BANK,
		ProfileFields: []bankcust.ProfileField{
			profileFields,
		},
		ClientRequestId: profileUpdClientReqId,
	})
	if rpcErr := epifigrpc.RPCError(updRes, updErr); rpcErr != nil &&
		!(updRes.GetStatus().IsRecordNotFound() || updRes.GetStatus().IsPermissionDenied()) {
		logger.Error(ctx, "error in updating profile at bank", zap.Error(rpcErr))
		return rpcErr
	}
	if updRes.GetStatus().IsPermissionDenied() || updRes.GetStatus().IsRecordNotFound() {
		return epifierrors.ErrPermissionDenied
	}
	return nil
}
func (k *KYCComplianceService) validateAadhaarUpdate(ctx context.Context, comp *compliancePb.KYCCompliance, user *user.User, kycAttempt *kycPb.KycAttempt) error {
	// Validate Aadhaar-Mobile linkage
	if err := k.validateAadhaarMobileLinkage(ctx, comp, user, kycAttempt); err != nil {
		return err
	}

	// Federal NSDL pan validation
	if err := k.nsdlPanValidation(ctx, comp, user); err != nil {
		return err
	}

	// Federal IDFY PAN Validation
	if err := k.validatePANAadhaar(ctx, comp, user, kycAttempt); err != nil {
		return err
	}
	return nil
}

func (k *KYCComplianceService) validateAadhaarMobileLinkage(ctx context.Context, comp *compliancePb.KYCCompliance, user *user.User, kycAttempt *kycPb.KycAttempt) error {
	validateRes, err := k.vgEkycClient.ValidateAadharMobile(ctx, &vgEkycPb.ValidateAadharMobileRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		},
		RequestId:    idgen.FederalRandomDigitsSequence("FEDAMV", 6),
		EkycRrn:      kycAttempt.GetRequestParams().GetEkycRrnNo(),
		MobileNumber: user.GetProfile().GetPhoneNumber(),
	})
	if rpcErr := epifigrpc.RPCError(validateRes, err); rpcErr != nil {
		logger.Error(ctx, "error in validating aadhaar mobile linkage",
			zap.Error(rpcErr))
		return fmt.Errorf("aadhaar mobile validation failed: %w", rpcErr)
	}

	if !validateRes.GetIsAadharLinkedToMobileNumber().ToBool() {
		logger.Info(ctx, "aadhaar not linked to mobile", zap.String(logger.REQUEST_ID, kycAttempt.GetClientReqId()))

		if comp.GetProfileUpdateDetails() == nil {
			comp.ProfileUpdateDetails = &compliancePb.ProfileUpdateDetails{}
		}
		if comp.GetProfileUpdateDetails().GetAadhaarUpdateDetails() == nil {
			comp.ProfileUpdateDetails.AadhaarUpdateDetails = &compliancePb.AadhaarUpdateDetails{}
		}

		comp.ProfileUpdateDetails.AadhaarUpdateDetails.AadhaarUpdateStatus = compliancePb.AadhaarUpdateStatus_AADHAAR_UPDATE_STATUS_FAILED
		comp.ProfileUpdateDetails.AadhaarUpdateDetails.AadhaarUpdateFailureReason = compliancePb.AadhaarUpdateFailureReason_AADHAAR_UPDATE_FAILURE_REASON_AADHAAR_MOBILE_VALIDATION_FAILED
		err = k.updateComplianceDetails(ctx, comp, []compliancePb.KYCComplianceFieldMask{
			compliancePb.KYCComplianceFieldMask_KYC_COMPLIANCE_FIELD_MASK_AADHAAR_UPDATE_DETAILS_AADHAAR_UPDATE_STATUS,
			compliancePb.KYCComplianceFieldMask_KYC_COMPLIANCE_FIELD_MASK_AADHAAR_UPDATE_DETAILS_AADHAAR_UPDATE_FAILURE_REASON,
		})
		if err != nil {
			return err
		}
		return epifierrors.ErrPermissionDenied
	}
	return nil
}

func (k *KYCComplianceService) nsdlPanValidation(ctx context.Context, comp *compliancePb.KYCCompliance, user *user.User) error {
	panResp, errResp := k.vgPanClient.ValidateV2(ctx, &vgPanPb.ValidateV2Request{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		},
		Pan:         user.GetProfile().GetPAN(),
		NameOnCard:  user.GetProfile().GetPanName().ToString(),
		DateOfBirth: user.GetProfile().GetDateOfBirth(),
	})
	if rpcErr := epifigrpc.RPCError(panResp, errResp); rpcErr != nil {
		if status.Code(errResp) == codes.ResourceExhausted || panResp.GetStatus().IsResourceExhausted() {
			return epifierrors.ErrResourceExhausted
		}
		logger.Error(ctx, "error in validating PAN-Aadhaar linkage", zap.Error(rpcErr))
		return fmt.Errorf("pan validation failed: %w", rpcErr)
	}

	if panResp.GetAadharSeedingStatus() != vgPanPb.PanAadhaarLinkStatus_PAN_AADHAAR_LINK_STATUS_TRUE {
		logger.Info(ctx, "pan aadhaar not linked")

		if comp.GetProfileUpdateDetails() == nil {
			comp.ProfileUpdateDetails = &compliancePb.ProfileUpdateDetails{}
		}
		if comp.GetProfileUpdateDetails().GetAadhaarUpdateDetails() == nil {
			comp.ProfileUpdateDetails.AadhaarUpdateDetails = &compliancePb.AadhaarUpdateDetails{}
		}

		comp.ProfileUpdateDetails.AadhaarUpdateDetails.AadhaarUpdateStatus = compliancePb.AadhaarUpdateStatus_AADHAAR_UPDATE_STATUS_FAILED
		comp.ProfileUpdateDetails.AadhaarUpdateDetails.AadhaarUpdateFailureReason = compliancePb.AadhaarUpdateFailureReason_AADHAAR_UPDATE_FAILURE_REASON_NSDL_PAN_VALIDATION_FAILED
		err := k.updateComplianceDetails(ctx, comp, []compliancePb.KYCComplianceFieldMask{
			compliancePb.KYCComplianceFieldMask_KYC_COMPLIANCE_FIELD_MASK_AADHAAR_UPDATE_DETAILS_AADHAAR_UPDATE_STATUS,
			compliancePb.KYCComplianceFieldMask_KYC_COMPLIANCE_FIELD_MASK_AADHAAR_UPDATE_DETAILS_AADHAAR_UPDATE_FAILURE_REASON,
		})
		if err != nil {
			return err
		}
		return epifierrors.ErrPermissionDenied
	}
	return nil
}

func (k *KYCComplianceService) validatePANAadhaar(ctx context.Context, comp *compliancePb.KYCCompliance, user *user.User, kycAttempt *kycPb.KycAttempt) error {

	// Get eKYC hash for comparison
	ekycNmDobHash := kycAttempt.GetRequestParams().GetEkycNameDobValidationAadhaarDigitsHash()
	ekycNmDobLast2DigitHash := kycAttempt.GetRequestParams().GetEkycNameDobValidationAadhaarLast2DigitsHash()
	if ekycNmDobHash == "" || ekycNmDobLast2DigitHash == "" {
		logger.Info(ctx, "eKYC name DOB validation Aadhaar digits hash is empty")
		return epifierrors.ErrPermissionDenied
	}

	panAadharValidationResp, errPanAadharValidation := k.vgPanClient.PANAadhaarValidation(ctx, &vgPanPb.PANAadhaarValidationRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		},
		Pan: user.GetProfile().GetPAN(),
	})

	if rpcErr := epifigrpc.RPCError(panAadharValidationResp, errPanAadharValidation); rpcErr != nil {
		// Check if error is due to PAN-Aadhaar not being linked
		if panAadharValidationResp != nil &&
			panAadharValidationResp.GetStatus().GetCode() == uint32(vgPanPb.PANAadhaarValidationResponse_PAN_AADHAAR_NOT_LINKED) {
			logger.Info(ctx, "PAN Aadhaar Not Linked, failing validation")

			if comp.GetProfileUpdateDetails() == nil {
				comp.ProfileUpdateDetails = &compliancePb.ProfileUpdateDetails{}
			}
			if comp.GetProfileUpdateDetails().GetAadhaarUpdateDetails() == nil {
				comp.ProfileUpdateDetails.AadhaarUpdateDetails = &compliancePb.AadhaarUpdateDetails{}
			}

			comp.ProfileUpdateDetails.AadhaarUpdateDetails.AadhaarUpdateStatus = compliancePb.AadhaarUpdateStatus_AADHAAR_UPDATE_STATUS_FAILED
			comp.ProfileUpdateDetails.AadhaarUpdateDetails.AadhaarUpdateFailureReason = compliancePb.AadhaarUpdateFailureReason_AADHAAR_UPDATE_FAILURE_REASON_IDFY_PAN_VALIDATION_FAILED
			err := k.updateComplianceDetails(ctx, comp, []compliancePb.KYCComplianceFieldMask{
				compliancePb.KYCComplianceFieldMask_KYC_COMPLIANCE_FIELD_MASK_AADHAAR_UPDATE_DETAILS_AADHAAR_UPDATE_STATUS,
				compliancePb.KYCComplianceFieldMask_KYC_COMPLIANCE_FIELD_MASK_AADHAAR_UPDATE_DETAILS_AADHAAR_UPDATE_FAILURE_REASON,
			})
			if err != nil {
				return err
			}

			return epifierrors.ErrPermissionDenied
		}
		logger.Error(ctx, "failed while getting pan validation response", zap.Error(rpcErr))
		return fmt.Errorf("pan-aadhaar validation failed: %w", rpcErr)
	}

	// Validate Aadhaar last 4 digits
	panAadhaarLast4Digits := panAadharValidationResp.GetPanAadhaarValidationResult().GetAadhaarLast4Digits()
	maskedAadhaar := panAadharValidationResp.GetPanAadhaarValidationResult().GetMaskedAadhaar()
	if len(maskedAadhaar) <= 2 {
		logger.Info(ctx, "masked aadhaar length is less than 2")
		return epifierrors.ErrPermissionDenied
	}
	panAadhaarLast2Digits := obfuscator.Hashed(maskedAadhaar[len(maskedAadhaar)-2:])

	if panAadhaarLast4Digits == "" && panAadhaarLast2Digits == "" {
		logger.Info(ctx, "Aadhaar last 4 and last 2 digits is empty, failing validation")
		return epifierrors.ErrPermissionDenied
	}

	// Compare hashed values
	panAadhaarLast4DigitsHash := obfuscator.Hashed(panAadhaarLast4Digits)
	panAadhaarLast2DigitsHash := obfuscator.Hashed(panAadhaarLast2Digits)
	if ekycNmDobHash != panAadhaarLast4DigitsHash && ekycNmDobLast2DigitHash != panAadhaarLast2DigitsHash {
		logger.Info(ctx, "Aadhaar last 4 and last 2 digits hash mismatch with eKYC hash")

		if comp.GetProfileUpdateDetails() == nil {
			comp.ProfileUpdateDetails = &compliancePb.ProfileUpdateDetails{}
		}
		if comp.GetProfileUpdateDetails().GetAadhaarUpdateDetails() == nil {
			comp.ProfileUpdateDetails.AadhaarUpdateDetails = &compliancePb.AadhaarUpdateDetails{}
		}

		comp.ProfileUpdateDetails.AadhaarUpdateDetails.AadhaarUpdateStatus = compliancePb.AadhaarUpdateStatus_AADHAAR_UPDATE_STATUS_FAILED
		comp.ProfileUpdateDetails.AadhaarUpdateDetails.AadhaarUpdateFailureReason = compliancePb.AadhaarUpdateFailureReason_AADHAAR_UPDATE_FAILURE_REASON_IDFY_PAN_VALIDATION_FAILED
		err := k.updateComplianceDetails(ctx, comp, []compliancePb.KYCComplianceFieldMask{
			compliancePb.KYCComplianceFieldMask_KYC_COMPLIANCE_FIELD_MASK_AADHAAR_UPDATE_DETAILS_AADHAAR_UPDATE_STATUS,
			compliancePb.KYCComplianceFieldMask_KYC_COMPLIANCE_FIELD_MASK_AADHAAR_UPDATE_DETAILS_AADHAAR_UPDATE_FAILURE_REASON,
		})
		if err != nil {
			return err
		}

		return epifierrors.ErrPermissionDenied
	}
	return nil
}

func (k *KYCComplianceService) startAadhaarUpdateAtBank(ctx context.Context, comp *compliancePb.KYCCompliance, attempt *kycPb.KycAttempt) error {

	profileUpdClientReqId := k.idGen.GetInAlphaNumeric(idgen.PeriodicKYC)

	aUser, err := k.getUser(ctx, comp.GetActorId())
	if err != nil {
		logger.Error(ctx, "error in GetUser", zap.Error(err))
		return epifierrors.ErrPermissionDenied
	}

	validateAadharErr := k.validateAadhaarUpdate(ctx, comp, aUser, attempt)
	if validateAadharErr != nil {
		logger.Error(ctx, "error in validating aadhar data", zap.Error(validateAadharErr))
		return epifierrors.ErrPermissionDenied
	}

	panNumber := aUser.GetProfile().GetPAN()
	logger.Info(ctx, "update profile at bank started for aadhaar update")

	err = k.updateProfileAtBank(ctx, profileUpdClientReqId, attempt.GetKycAttemptId(), comp.GetActorId(), panNumber, bankcust.ProfileField_PROFILE_FIELD_AADHAAR)
	if err != nil {
		return err
	}

	if comp.GetProfileUpdateDetails() == nil {
		comp.ProfileUpdateDetails = &compliancePb.ProfileUpdateDetails{}
	}
	if comp.GetProfileUpdateDetails().GetAadhaarUpdateDetails() == nil {
		comp.ProfileUpdateDetails.AadhaarUpdateDetails = &compliancePb.AadhaarUpdateDetails{}
	}

	aadhaarUpdateDetails := &compliancePb.AadhaarUpdateDetails{
		ProfileUpdateRequestId: profileUpdClientReqId,
		EKYCClientRequestId:    comp.GetProfileUpdateDetails().GetEKYCClientRequestId(),
		EKYCCompletedAt:        comp.GetProfileUpdateDetails().GetEKYCCompletedAt(),
		ProfileUpdateStartedAt: timestampPb.New(k.timeClient.Now()),
		AadhaarUpdateStatus:    compliancePb.AadhaarUpdateStatus_AADHAAR_UPDATE_STATUS_IN_PROGRESS,
	}
	comp.ProfileUpdateDetails.AadhaarUpdateDetails = aadhaarUpdateDetails
	if err = k.updateComplianceDetails(ctx, comp, []compliancePb.KYCComplianceFieldMask{
		compliancePb.KYCComplianceFieldMask_KYC_COMPLIANCE_FIELD_MASK_AADHAAR_UPDATE_DETAILS_PROFILE_UPDATE_REQUEST_ID,
		compliancePb.KYCComplianceFieldMask_KYC_COMPLIANCE_FIELD_MASK_AADHAAR_UPDATE_DETAILS_EKYC_CLIENT_REQUEST_ID,
		compliancePb.KYCComplianceFieldMask_KYC_COMPLIANCE_FIELD_MASK_AADHAAR_UPDATE_DETAILS_EKYC_COMPLETED_AT,
		compliancePb.KYCComplianceFieldMask_KYC_COMPLIANCE_FIELD_MASK_AADHAAR_UPDATE_DETAILS_PROFILE_UPDATE_STARTED_AT,
		compliancePb.KYCComplianceFieldMask_KYC_COMPLIANCE_FIELD_MASK_AADHAAR_UPDATE_DETAILS_AADHAAR_UPDATE_STATUS,
	}); err != nil {
		return err
	}
	return nil
}

func (k *KYCComplianceService) handleKYCCTAClickedAt(ctx context.Context, actorId string, clientReqId string) (*deeplink.Deeplink, error) {
	err := k.complianceDao.Update(ctx, &compliancePb.KYCCompliance{
		Id:              clientReqId,
		ActorId:         actorId,
		KYCCtaClickedAt: timestampPb.New(k.timeClient.Now()),
	}, []compliancePb.KYCComplianceFieldMask{compliancePb.KYCComplianceFieldMask_KYC_COMPLIANCE_FIELD_MASK_KYC_CTA_CLICKED_AT})
	if err != nil {
		logger.Error(ctx, "error in updating kyc cta clicked at field", zap.Error(err))
		return nil, err
	}
	logger.Info(ctx, "user clicked the kyc update cta")
	return getKYCUpdateDL(), err
}

func (k *KYCComplianceService) handleUserSMSTriggeredAt(ctx context.Context, actorId string, clientReqId string, opStatus *operationalStatusPb.OperationalStatusInfo) (*deeplink.Deeplink, error) {
	err := k.complianceDao.Update(ctx, &compliancePb.KYCCompliance{
		Id:                 clientReqId,
		ActorId:            actorId,
		UserSmsTriggeredAt: timestampPb.New(k.timeClient.Now()),
	}, []compliancePb.KYCComplianceFieldMask{compliancePb.KYCComplianceFieldMask_KYC_COMPLIANCE_FIELD_MASK_USER_SMS_TRIGGERED_AT})
	if err != nil {
		goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
			metrics.RecordSMSTriggeredMetric(metrics.SMSTriggerFailed)
		})
		logger.Error(ctx, "error in updating user sms triggered at", zap.Error(err))
		return nil, err
	}
	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		metrics.RecordSMSTriggeredMetric(metrics.SMSTriggeredSuccess)
	})
	logger.Info(ctx, "user triggered the periodic kyc sms")

	isAccountFrozen := opStatus.GetFreezeStatus() == enums.FreezeStatus_FREEZE_STATUS_DEBIT_FREEZE
	return getAlreadyCompletedDL(isAccountFrozen), err
}

func (k *KYCComplianceService) FetchDynamicElements(ctx context.Context, req *dynamic_elements.FetchDynamicElementsRequest) (*dynamic_elements.FetchDynamicElementsResponse, error) {
	var (
		notFound = func() (*dynamic_elements.FetchDynamicElementsResponse, error) {
			return &dynamic_elements.FetchDynamicElementsResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		}
	)
	if !k.genConf.EnableTopNoticeBarBanner() {
		return notFound()
	}
	temp, ok := req.GetClientContext().GetScreenAdditionalInfo().(*dynamic_elements.ClientContext_HomeInfo)
	if !ok {
		return notFound()
	}
	if !lo.Contains(allowedHomeScreenSections, temp.HomeInfo.GetSection()) {
		logger.Debug(ctx, "skipping banner since the section is valid", zap.String(logger.STATUS, temp.HomeInfo.GetSection().String()))
		return notFound()
	}
	return k.fetchDynamicElementsV2(ctx, req)
}

func (k *KYCComplianceService) fetchDynamicElementsV2(ctx context.Context, req *dynamic_elements.FetchDynamicElementsRequest) (*dynamic_elements.FetchDynamicElementsResponse, error) {
	notFound := func() (*dynamic_elements.FetchDynamicElementsResponse, error) {
		return &dynamic_elements.FetchDynamicElementsResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil
	}
	statusResp, err := k.orchestrator.GetStatus(ctx, &GetStatusRequest{
		actorId: req.GetActorId(),
	})
	if err != nil {
		return &dynamic_elements.FetchDynamicElementsResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	if statusResp == nil {
		return notFound()
	}
	switch statusResp.complianceStatus {
	case Status_CUSTOMER_DOES_NOT_EXIST:
		return notFound()
	case Status_COMPLIED, Status_IN_PROGRESS:
		// return the banner, if present
	case Status_NOT_COMPLIED, Status_UNSPECIFIED:
		statusResp, err = k.orchestrator.GetStatus(ctx, &GetStatusRequest{
			actorId:           req.GetActorId(),
			fetchLatest:       true,
			complianceData:    statusResp.complianceData,
			operationalStatus: statusResp.operationalStatus,
			bankCustomer:      statusResp.bankCustomer,
		})
		if err != nil {
			return &dynamic_elements.FetchDynamicElementsResponse{
				Status: rpc.StatusInternalWithDebugMsg(err.Error()),
			}, nil
		}
	}
	if statusResp != nil && statusResp.showBanner {
		bannerResp, errBanner := k.orchestrator.GetBanner(ctx, &GetBannerRequest{
			complianceStatus:  statusResp.complianceStatus,
			complianceData:    statusResp.complianceData,
			operationalStatus: statusResp.operationalStatus,
			homeScreenSection: req.GetClientContext().GetHomeInfo().GetSection(),
			vendorCustomerId:  statusResp.bankCustomer.GetVendorCustomerId(),
		})
		if errBanner != nil {
			return &dynamic_elements.FetchDynamicElementsResponse{
				Status: rpc.StatusInternalWithDebugMsg(errBanner.Error()),
			}, nil
		}
		if bannerResp == nil || bannerResp.dynamicElements == nil || len(bannerResp.dynamicElements) == 0 {
			return notFound()
		}
		return &dynamic_elements.FetchDynamicElementsResponse{
			Status:       rpc.StatusOk(),
			ElementsList: bannerResp.dynamicElements,
		}, nil
	}
	return notFound()
}

func (k *KYCComplianceService) getReKYCDeeplinkInstructions(ekycCta *deeplink.Cta, enableTertiaryCta bool) *ReKYCDeeplinkInstructions {
	return &ReKYCDeeplinkInstructions{
		EkycCta:           ekycCta,
		EnableTertiaryCta: enableTertiaryCta,
	}
}

func (k *KYCComplianceService) DynamicElementCallback(_ context.Context, _ *dynamic_elements.DynamicElementCallbackRequest) (*dynamic_elements.DynamicElementCallbackResponse, error) {
	// returning a no-op okay since we don't want to register callbacks
	return &dynamic_elements.DynamicElementCallbackResponse{
		Status: rpcPb.StatusOk(),
	}, nil
}

func isUserEligibleForKYCUpdateNudge(custId string) bool {
	return lo.Contains(docExpiryCustId, custId)
}

func IsAadhaarUpdateRequired(custId string) bool {
	// TODO(Bhabtosh): Once tested on prod create new table to store aadhaar update status and validate with the data to check eligibility
	return (custId == "136953785")
}

func (k *KYCComplianceService) getSimId(ctx context.Context, actorId string) (int, error) {
	// simId -1 indicates we don't have simId in backend and client will open sms pop up
	simId := -1
	authResp, err := k.authClient.GetDeviceAuth(ctx, &auth.GetDeviceAuthRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(authResp, err); rpcErr != nil && !authResp.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error in getting device auth response", zap.Error(rpcErr))
		return simId, rpcErr
	}
	if authResp.GetSimId() != "" {
		simId, err = strconv.Atoi(authResp.GetSimId())
		if err != nil {
			logger.Error(ctx, "error in getting sim id for the device", zap.Error(err))
			return simId, err
		}
	}
	return simId, nil
}

func (k *KYCComplianceService) handleUserActionTriggeredAt(ctx context.Context, actorId, custId string, actionTimestamp, kycDueAt *timestampPb.Timestamp, threshold time.Duration) (bool, error) {
	// If it has been more than threshold time since user interaction, treat it user interaction not happened and perform a fresh status check
	if k.timeClient.Now().After(actionTimestamp.AsTime().Add(threshold)) {
		logger.Info(ctx, "user has action triggered but has passed the threshold period")
		return k.handleUserActionNotTriggeredAt(ctx, actorId, custId, actionTimestamp, kycDueAt)
	}
	logger.Info(ctx, "user with in action triggered threshold period")
	// Until the threshold time from action triggered, we consider that the user is not eligible for nudge
	return false, nil
}

func (k *KYCComplianceService) handleUserActionNotTriggeredAt(ctx context.Context, actorId, custId string, actionTriggeredAt, kycDueAt *timestampPb.Timestamp) (bool, error) {
	var (
		comp *compliancePb.KYCCompliance
	)
	if err := k.allowVendorCall(ctx, actorId); errors.Is(err, epifierrors.ErrPermissionDenied) {
		return true, nil
	}

	kycFlag, err := k.getDedupeKYCFlagFromVendor(ctx, commonvgpb.Vendor_FEDERAL_BANK, actorId)
	if err != nil {
		return false, err
	}
	switch kycFlag {
	case customer.KYCFlag_KYC_FLAG_EXISTS:
		logger.Info(ctx, "kyc flag exists for the user")
		_, comp, err = k.fetchAndStoreLatestKYCComplianceFromVendor(ctx, actorId, custId)
		if err != nil {
			return false, err
		}
		if comp.GetKYCDueAt().AsTime().After(k.timeClient.Now().Add(k.genConf.DueDateThreshold())) {
			logger.Info(ctx, "user's KYC due date is after DueDateThreshold period")
			return false, nil
		}
		logger.Info(ctx, "user's KYC due date is before DueDateThreshold period")
		return true, nil
	case customer.KYCFlag_KYC_FLAG_DOES_NOT_EXISTS:
		if actionTriggeredAt != nil {
			logger.Info(ctx, "user with action triggered and non complied even after threshold period")
			// monitoring.KibanaInfoServiceMonitor(ctx, cfg.ONBOARDING_SERVICE, "user with SMS triggered and non complied even after SMSTriggeredAtThreshold period")
		}
		logger.Info(ctx, fmt.Sprintf("kyc flag does not exist: %v, show banner", kycFlag.String()))
		return true, nil
	default:
		logger.Error(ctx, fmt.Sprintf("received unexpected kyc flag: %v", kycFlag.String()))
		return false, fmt.Errorf("received unexpected kyc flag: %v", kycFlag.String())
	}
}

func (k *KYCComplianceService) getDedupeKYCFlagFromVendor(ctx context.Context, vendor commonvgpb.Vendor, actorId string) (customer.KYCFlag, error) {
	aUser, err := k.getUser(ctx, actorId)
	if err != nil {
		return customer.KYCFlag_KYC_FLAG_UNSPECIFIED, err
	}
	resp, errResp := k.usersClient.DedupeCheck(ctx, &user.DedupeCheckRequest{
		ActorId:     actorId,
		Vendor:      vendor,
		PanNumber:   aUser.GetProfile().GetPAN(),
		PhoneNumber: aUser.GetProfile().GetPhoneNumber(),
		UserId:      aUser.GetId(),
		DateOfBirth: aUser.GetProfile().GetDateOfBirth(),
		RequestType: user.DedupeCheckRequest_REQUEST_TYPE_BEST_EFFORT,
		DedupeFlow:  user.DedupeCheckRequest_FLOW_REKYC_BANNER,
	})
	if err := epifigrpc.RPCError(resp, errResp); err != nil {
		logger.Error(ctx, "error in fetching dedupe status", zap.Error(err))
		return customer.KYCFlag_KYC_FLAG_UNSPECIFIED, err
	}
	logger.Info(ctx, fmt.Sprintf("fetched user's KYC flag from Dedupe Check API: %v", resp.GetKYCFlag().String()))
	return resp.GetKYCFlag(), nil
}

func (k *KYCComplianceService) fetchLastKnownKYCComplianceDataFromVendor(ctx context.Context, actorId, custId string, opStatus *operationalStatusPb.OperationalStatusInfo) (*compliancePb.KYCCompliance, error) {
	logger.Info(ctx, "fetching user's last known KYC compliance data from operational status")

	if opStatus.GetKYCComplianceInfo() != nil &&
		opStatus.GetKYCComplianceInfo().GetKYCDueDate() != nil {
		return k.storeKYCCompliance(ctx, &compliancePb.KYCCompliance{
			Id:                  k.idGen.GetInAlphaNumeric(idgen.PeriodicKYC),
			ActorId:             actorId,
			Vendor:              commonvgpb.Vendor_FEDERAL_BANK,
			VendorCustomerId:    custId,
			KYCComplianceStatus: compliancePb.KYCComplianceStatus_KYC_COMPLIANCE_STATUS_DUE,
			AttemptSource:       compliancePb.AttemptSource_ATTEMPT_SOURCE_ACCOUNT_STATUS_ENQUIRY_API,
			KYCDueAt:            datetime.DateToTimestamp(opStatus.GetKYCComplianceInfo().GetKYCDueDate(), datetime.IST),
			KYCCompliedAt:       datetime.DateToTimestamp(opStatus.GetKYCComplianceInfo().GetKYCReviewDate(), datetime.IST),
		})
	}
	var gracePeriodTimestamp *timestampPb.Timestamp
	if opStatus.GetVendorResponse().GetFederalAccountStatusEnquiryResponse().GetKYCGracePeriodDate() != nil {
		gracePeriodTimestamp = datetime.DateToTimestamp(opStatus.GetVendorResponse().GetFederalAccountStatusEnquiryResponse().GetKYCGracePeriodDate(), datetime.IST)
	}
	if opStatus.GetVendorResponse().GetFederalAccountStatusEnquiryResponse().GetKYCDueDate() != nil {
		return k.storeKYCCompliance(ctx, &compliancePb.KYCCompliance{
			Id:                      k.idGen.GetInAlphaNumeric(idgen.PeriodicKYC),
			ActorId:                 actorId,
			Vendor:                  commonvgpb.Vendor_FEDERAL_BANK,
			VendorCustomerId:        custId,
			KYCComplianceStatus:     compliancePb.KYCComplianceStatus_KYC_COMPLIANCE_STATUS_DUE,
			AttemptSource:           compliancePb.AttemptSource_ATTEMPT_SOURCE_ACCOUNT_STATUS_ENQUIRY_API,
			KYCDueAt:                datetime.DateToTimestamp(opStatus.GetVendorResponse().GetFederalAccountStatusEnquiryResponse().GetKYCDueDate(), datetime.IST),
			KYCGracePeriodTimestamp: gracePeriodTimestamp,
			KYCCompliedAt:           datetime.DateToTimestamp(opStatus.GetVendorResponse().GetFederalAccountStatusEnquiryResponse().GetKYCReviewDate(), datetime.IST),
		})
	}
	_, comp, err := k.fetchAndStoreLatestKYCComplianceFromVendor(ctx, actorId, custId)
	if err != nil {
		return nil, err
	}
	return comp, nil
}

func (k *KYCComplianceService) fetchAndStoreLatestKYCComplianceFromVendor(ctx context.Context, actorId, custId string) (*operationalStatusPb.OperationalStatusInfo, *compliancePb.KYCCompliance, error) {
	logger.Info(ctx, "fetching user's max 10 minute stale KYC compliance data from operational status")
	opStatus, err := k.fetchOperationalStatusFromVendor(ctx, actorId, commonvgpb.Vendor_FEDERAL_BANK, operationalStatusPb.GetOperationalStatusRequest_DATA_FRESHNESS_10_MIN_STALE)
	if err != nil {
		return nil, nil, err
	}
	newKycDueAt := datetime.DateToTimestamp(opStatus.GetKYCComplianceInfo().GetKYCDueDate(), datetime.IST)
	if opStatus.GetKYCComplianceInfo().GetKYCDueDate() == nil {
		return nil, nil, errors.New("empty kyc due at in vendor response")
	}
	var gracePeriodTimestamp *timestampPb.Timestamp
	if opStatus.GetVendorResponse().GetFederalAccountStatusEnquiryResponse().GetKYCGracePeriodDate() != nil {
		gracePeriodTimestamp = datetime.DateToTimestamp(opStatus.GetVendorResponse().GetFederalAccountStatusEnquiryResponse().GetKYCGracePeriodDate(), datetime.IST)
	}
	comp, errComp := k.storeKYCCompliance(ctx, &compliancePb.KYCCompliance{
		Id:                      k.idGen.GetInAlphaNumeric(idgen.PeriodicKYC),
		ActorId:                 actorId,
		Vendor:                  commonvgpb.Vendor_FEDERAL_BANK,
		VendorCustomerId:        custId,
		KYCComplianceStatus:     compliancePb.KYCComplianceStatus_KYC_COMPLIANCE_STATUS_DUE,
		AttemptSource:           compliancePb.AttemptSource_ATTEMPT_SOURCE_ACCOUNT_STATUS_ENQUIRY_API,
		KYCDueAt:                newKycDueAt,
		KYCGracePeriodTimestamp: gracePeriodTimestamp,
		KYCCompliedAt:           datetime.DateToTimestamp(opStatus.GetKYCComplianceInfo().GetKYCReviewDate(), datetime.IST),
	})
	if errComp != nil {
		return nil, nil, errComp
	}
	return opStatus, comp, nil
}

func (k *KYCComplianceService) fetchOperationalStatusFromVendor(ctx context.Context, actorId string, vendor commonvgpb.Vendor, dataFreshness operationalStatusPb.GetOperationalStatusRequest_DataFreshness) (*operationalStatusPb.OperationalStatusInfo, error) {
	resp, errResp := k.savingsClient.GetSavingsAccountEssentials(ctx, &savings.GetSavingsAccountEssentialsRequest{
		Filter: &savings.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
			ActorIdBankFilter: &savings.ActorIdBankFilter{
				ActorId:     actorId,
				PartnerBank: vendor,
			},
		},
	})
	if err := epifigrpc.RPCError(resp, errResp); err != nil {
		if rpc.StatusFromError(err).IsRecordNotFound() {
			return nil, epifierrors.ErrRecordNotFound
		}
		logger.Error(ctx, "error in fetching savings account", zap.Error(err))
		return nil, err
	}
	opStatusResp, errResp := k.opStatusClient.GetOperationalStatus(ctx, &operationalStatusPb.GetOperationalStatusRequest{
		DataFreshness: dataFreshness,
		AccountIdentifier: &operationalStatusPb.GetOperationalStatusRequest_SavingsAccountId{
			SavingsAccountId: resp.GetAccount().GetId(),
		},
	})
	if err := epifigrpc.RPCError(opStatusResp, errResp); err != nil {
		if opStatusResp.GetStatus().IsRecordNotFound() || opStatusResp.GetStatus().GetCode() == uint32(operationalStatusPb.GetOperationalStatusResponse_ACC_NUMBER_PHONE_MISMATCH) {
			return nil, epifierrors.ErrRecordNotFound
		}
		logger.Error(ctx, "error in fetching operational status from vendor", zap.Error(err))
		return nil, err
	}
	return opStatusResp.GetOperationalStatusInfo(), nil
}

func (k *KYCComplianceService) storeKYCCompliance(ctx context.Context, data *compliancePb.KYCCompliance) (*compliancePb.KYCCompliance, error) {
	comp, err := k.complianceDao.Get(ctx, dao.WithActorId(data.GetActorId()))
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error in fetching compliance entry", zap.Error(err))
		return nil, err
	}
	if errors.Is(err, epifierrors.ErrRecordNotFound) {
		if comp, err = k.complianceDao.Create(ctx, data); err != nil {
			logger.Error(ctx, "error in creating KYC Compliance entry", zap.Error(err))
			return nil, err
		}
		return comp, nil
	}
	if proto.Equal(comp.GetKYCDueAt(), data.GetKYCDueAt()) {
		logger.Info(ctx, "old and new due date is same")
		return comp, nil
	}
	logger.Info(ctx, "deleting old and creating new kyc compliance entry")
	if err = k.complianceDao.DeleteById(ctx, comp.GetId()); err != nil {
		logger.Error(ctx, "error in deleting compliance data", zap.Error(err))
		return nil, err
	}
	oldKycDueAt := comp.GetKYCDueAt()
	if k.isReKycSuccessful(oldKycDueAt, data.GetKYCDueAt()) {
		// we got confirmation from vendor that re-kyc is successful
		// we can show success banner to user
		if data.GetComplianceMetadata() == nil {
			data.ComplianceMetadata = &compliancePb.ComplianceMetadata{}
		}
		if data.GetComplianceMetadata().GetShouldShowReKycSuccessBanner() != commontypes.BooleanEnum_FALSE {
			// set the flag to show the banner only if it is not already set to false (defensive check)
			logger.Info(ctx, "user's re-kyc is successful, setting the flag to show re-kyc success banner")
			data.ComplianceMetadata.ShouldShowReKycSuccessBanner = commontypes.BooleanEnum_TRUE
		}
	}
	compDataCopy := proto.Clone(comp).(*compliancePb.KYCCompliance)
	if comp, err = k.complianceDao.Create(ctx, data); err != nil {
		logger.Error(ctx, "error in creating KYC Compliance entry", zap.Error(err))
		return nil, err
	}
	metrics.PushMetricsForPeriodicKYCActions(ctx, data.GetKYCDueAt(), k.genConf.DueDateThreshold(), k.timeClient, compDataCopy)
	k.EmitReKYCCompliedEvent(ctx, data.GetActorId(), oldKycDueAt, data.GetKYCDueAt())
	return comp, nil
}

// isReKycSuccessful checks if the re-kyc is successful
// Returns true if the new due date timestamp is outside the re-KYC threshold window(current time + re-KYC threshold)
//
//	while the old due date was within it, indicating that the re-KYC process has successfully moved
//	the due date beyond the compliance threshold.
func (k *KYCComplianceService) isReKycSuccessful(oldDueTimeStamp, newDueTimestamp *timestampPb.Timestamp) bool {
	return newDueTimestamp.AsTime().After(k.timeClient.Now().Add(k.genConf.DueDateThreshold())) &&
		!oldDueTimeStamp.AsTime().After(k.timeClient.Now().Add(k.genConf.DueDateThreshold()))
}

func (k *KYCComplianceService) EmitReKYCCompliedEvent(ctx context.Context, actorId string, oldDueTimeStamp, newDueTimestamp *timestampPb.Timestamp) {
	if k.isReKycSuccessful(oldDueTimeStamp, newDueTimestamp) {
		events.EmitReKYCEvents(ctx, actorId, newDueTimestamp.AsTime(), k.eventBroker)
	}
}

func (k *KYCComplianceService) getBankCustomer(ctx context.Context, actorId string, vendor commonvgpb.Vendor) (*bankcust.BankCustomer, error) {
	bcResp, errResp := k.bcClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
		Vendor: vendor,
		Identifier: &bankcust.GetBankCustomerRequest_ActorId{
			ActorId: actorId,
		},
	})
	if err := epifigrpc.RPCError(bcResp, errResp); err != nil {
		if bcResp.GetStatus().IsRecordNotFound() {
			return nil, epifierrors.ErrRecordNotFound
		}
		logger.Error(ctx, "error in fetching bank customer", zap.Error(err))
		return nil, err
	}
	return bcResp.GetBankCustomer(), nil
}

func (k *KYCComplianceService) getUser(ctx context.Context, actorId string) (*user.User, error) {
	userResp, err := k.usersClient.GetUser(ctx, &user.GetUserRequest{
		Identifier: &user.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if err = epifigrpc.RPCError(userResp, err); err != nil {
		if userResp.GetStatus().IsRecordNotFound() {
			return nil, epifierrors.ErrRecordNotFound
		}
		logger.Error(ctx, "error in GetUser rpc", zap.Error(err))
		return nil, err
	}
	return userResp.GetUser(), nil
}

func (k *KYCComplianceService) allowVendorCall(ctx context.Context, actorId string) error {
	var (
		key     = allowVendorCallLockKey + actorId
		timeout = allowVendorCallLockExpiry
	)

	if !k.genConf.OptimiseVendorAPICalls() {
		logger.Info(ctx, "debug: optimise vendor api call flag disabled")
		return nil
	}

	// try acquire lock
	_, lockErr := k.lockManager.GetLock(ctx, key, timeout)
	if errors.Is(lockErr, lock.LockAlreadyAcquired) {
		logger.Info(ctx, fmt.Sprintf("debug: lock not available for key: %v", key))
		return epifierrors.ErrPermissionDenied
	}
	if lockErr != nil {
		logger.Error(ctx, "error while acquiring lock", zap.Error(lockErr))
		return lockErr
	}

	logger.Info(ctx, fmt.Sprintf("debug: lock acquired for key: %v", key))
	return nil
}

func (k *KYCComplianceService) GetPeriodicKYCNudges(ctx context.Context, req *compliancePb.GetPeriodicKYCNudgesRequest) (*compliancePb.GetPeriodicKYCNudgesResponse, error) {
	var (
		notFound = func() (*compliancePb.GetPeriodicKYCNudgesResponse, error) {
			return &compliancePb.GetPeriodicKYCNudgesResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		}
	)

	statusResp, err := k.orchestrator.GetStatus(ctx, &GetStatusRequest{
		actorId: req.GetActorId(),
	})
	if err != nil {
		return &compliancePb.GetPeriodicKYCNudgesResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	if statusResp == nil {
		return notFound()
	}
	switch statusResp.complianceStatus {
	case Status_CUSTOMER_DOES_NOT_EXIST:
		return notFound()
	case Status_COMPLIED, Status_IN_PROGRESS:
		// return the banner, if present
	case Status_NOT_COMPLIED, Status_UNSPECIFIED:
		statusResp, err = k.orchestrator.GetStatus(ctx, &GetStatusRequest{
			actorId:           req.GetActorId(),
			fetchLatest:       true,
			complianceData:    statusResp.complianceData,
			operationalStatus: statusResp.operationalStatus,
			bankCustomer:      statusResp.bankCustomer,
		})
		if err != nil {
			return &compliancePb.GetPeriodicKYCNudgesResponse{
				Status: rpc.StatusInternalWithDebugMsg(err.Error()),
			}, nil
		}
	}
	if statusResp != nil && statusResp.showBanner {
		bannerResp, errBanner := k.orchestrator.GetPeriodicKYCNudge(ctx, &GetPeriodicKYCNudgeRequest{
			complianceStatus:  statusResp.complianceStatus,
			complianceData:    statusResp.complianceData,
			operationalStatus: statusResp.operationalStatus,
			vendorCustomerId:  statusResp.bankCustomer.GetVendorCustomerId(),
			nudgeType:         req.GetNudgeType(),
		})
		if errBanner != nil {
			return &compliancePb.GetPeriodicKYCNudgesResponse{
				Status: rpc.StatusInternalWithDebugMsg(errBanner.Error()),
			}, nil
		}
		if bannerResp == nil || bannerResp.nudge == nil {
			return notFound()
		}
		return &compliancePb.GetPeriodicKYCNudgesResponse{
			Status: rpc.StatusOk(),
			Nudges: map[string]*compliancePb.PeriodicKYCNudge{
				compliancePb.NudgeType_NUDGE_TYPE_PROFILE_TOP_BANNER.String(): bannerResp.nudge,
			},
		}, nil
	}
	return notFound()
}

func (k *KYCComplianceService) acquireLockForEKYC(ctx context.Context, actorId string) (lock.ILock, error) {
	// try to acquire before initiating new EKYC request
	for i := int32(1); i <= k.genConf.EKYCLockConfig().RetryAttempts(); i++ {
		aLock, lockErr := k.lockManager.GetLock(ctx, getEkycLockKey(actorId), k.genConf.EKYCLockConfig().LockDuration())
		if lockErr == nil {
			return aLock, nil
		}
		if i == k.genConf.EKYCLockConfig().RetryAttempts() {
			logger.Debug(ctx, "lock is already acquired skipping ekyc cta")
			return nil, epifierrors.ErrRecordNotFound
		}
		time.Sleep(k.genConf.EKYCLockConfig().SleepDuration())
	}
	return nil, epifierrors.ErrRecordNotFound
}

// getEKYCCta returns CTA with EKYC deeplink if applicable, assumes that user is eligible for banner based on SMS triggered at
func (k *KYCComplianceService) getEKYCCta(ctx context.Context, actorId string) (*deeplink.Cta, error) {
	if !k.genConf.EnableEKYC() {
		return nil, nil
	}

	aLock, lockErr := k.acquireLockForEKYC(ctx, actorId)
	if lockErr != nil {
		return nil, nil
	}
	defer func() {
		// cloning ctx before releasing lock, to avoid lock release failures in case of cancelled ctx
		if err := aLock.Release(epificontext.CloneCtx(ctx)); err != nil {
			logger.Error(ctx, "error releasing lock", zap.Error(err))
		}
	}()
	complianceRecord, err := k.complianceDao.Get(ctx, dao.WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "error in getting compliance record", zap.Error(err))
		return nil, err
	}

	// User not eligible for EKYC within 24 hours of profile update
	// todo(rishu): make this config driven
	if complianceRecord.GetProfileUpdateDetails().GetProfileUpdateStartedAt() != nil &&
		k.timeClient.Since(complianceRecord.GetProfileUpdateDetails().GetProfileUpdateStartedAt().AsTime()) < 24*time.Hour {
		return nil, nil
	}

	if complianceRecord.GetProfileUpdateDetails().GetEKYCClientRequestId() == "" {
		return k.startNewEKYC(ctx, complianceRecord)
	}

	getKycRes, err := k.kycClient.GetKYCAttempt(ctx, &kyc.GetKYCAttemptRequest{
		Identifier: &kyc.GetKYCAttemptRequest_ClientReqIdType{
			ClientReqIdType: &kyc.GetKYCAttemptRequest_ClientReqIdAndType{
				ClientReqId: complianceRecord.GetProfileUpdateDetails().GetEKYCClientRequestId(),
				KycType:     kyc.KycType_EKYC,
			},
		},
	})
	if rpcErr := epifigrpc.RPCError(getKycRes, err); rpcErr != nil && !rpc.StatusFromError(rpcErr).IsRecordNotFound() {
		logger.Error(ctx, "error in getting kyc attempt", zap.Error(rpcErr))
		return nil, rpcErr
	}
	if getKycRes.GetStatus().IsRecordNotFound() {
		return k.startNewEKYC(ctx, complianceRecord)
	}
	logger.Info(ctx, "EKYC state for reKYC", zap.String(logger.STATUS, getKycRes.GetKycAttempt().GetState().String()))
	switch getKycRes.GetKycAttempt().GetState() {
	case kyc.KYCState_EKYC_FAILED:
		// fallback to existing behaviour, showing only SMS
		// todo (saiteja): confirm retry strategy
		return nil, nil
	case kyc.KYCState_EKYC_INIT:
		ekycDl, dlErr := k.startEKYCDeeplink(ctx, complianceRecord)
		if dlErr != nil {
			return nil, dlErr
		}
		return k.populateEkycCta(ekycDl, complianceRecord)
	case kyc.KYCState_EKYC_DATA_RECEIVED:
		kycVendorDataResp, respErr := k.kycClient.GetKYCVendorData(ctx, &kyc.GetKYCVendorDataRequest{
			Identifier: &kyc.GetKYCVendorDataRequest_AttemptId{
				AttemptId: getKycRes.GetKycAttempt().GetKycAttemptId(),
			},
		})
		if rpcErr := epifigrpc.RPCError(kycVendorDataResp, respErr); rpcErr != nil && !rpc.StatusFromError(rpcErr).IsRecordNotFound() {
			logger.Error(ctx, "error in getting vendor data", zap.Error(rpcErr))
			return nil, rpcErr
		}
		if kycVendorDataResp.GetStatus().IsRecordNotFound() {
			return k.startNewEKYC(ctx, complianceRecord)
		}
		// todo (saiteja): confirm expiry handling
		// continue with Profile update via PeriodicKYCCallback
		callbackDl := ReKycDlWithEkycCompletedCallbackAction(complianceRecord)
		return k.populateEkycCta(callbackDl, complianceRecord)
	default:
		logger.Error(ctx, "unexpected EKYC state", zap.String(logger.STATUS, getKycRes.GetKycAttempt().GetState().String()))
		return nil, fmt.Errorf("unexpected EKYC state")
	}
}

func getEkycLockKey(actorId string) string {
	return "EKYCLock:" + actorId
}

func (k *KYCComplianceService) startNewEKYC(ctx context.Context, complianceRecord *compliancePb.KYCCompliance) (*deeplink.Cta, error) {
	logger.Info(ctx, "initiating EKYC for REKYC")
	complianceRecord.ProfileUpdateDetails = &compliancePb.ProfileUpdateDetails{}
	eKYCClientRequestId := k.idGen.GetInAlphaNumeric(idgen.PeriodicKYC)
	if err := k.complianceDao.Update(ctx, complianceRecord, []compliancePb.KYCComplianceFieldMask{
		compliancePb.KYCComplianceFieldMask_KYC_COMPLIANCE_FIELD_MASK_PROFILE_UPDATE_DETAILS_EKYC_CLIENT_REQUEST_ID,
	}); err != nil {
		logger.Error(ctx, "error in updating compliance record with EKYC request ID", zap.Error(err))
		return nil, err
	}
	// update new requestID in record
	initEKYCRes, initErr := k.kycClient.InitiateEKYC(ctx, &kyc.InitiateEKYCRequest{
		ActorId:         complianceRecord.GetActorId(),
		EkycSource:      kyc.EkycSource_EKYC_SOURCE_RE_KYC,
		ClientRequestId: eKYCClientRequestId,
		ForceRetry:      true,
	})
	if rpcErr := epifigrpc.RPCError(initEKYCRes, initErr); rpcErr != nil {
		logger.Error(ctx, "error in initiating EKYC", zap.Error(rpcErr))
		return nil, rpcErr
	}
	complianceRecord.ProfileUpdateDetails.EKYCClientRequestId = eKYCClientRequestId
	if err := k.complianceDao.Update(ctx, complianceRecord, []compliancePb.KYCComplianceFieldMask{
		compliancePb.KYCComplianceFieldMask_KYC_COMPLIANCE_FIELD_MASK_PROFILE_UPDATE_DETAILS_EKYC_CLIENT_REQUEST_ID,
	}); err != nil {
		logger.Error(ctx, "error in updating compliance record with EKYC request ID", zap.Error(err))
		return nil, err
	}
	ekycDl, err := k.startEKYCDeeplink(ctx, complianceRecord)
	if err != nil {
		return nil, err
	}
	return k.populateEkycCta(ekycDl, complianceRecord)
}

func (k *KYCComplianceService) populateEkycCta(ekycDl *deeplink.Deeplink, complianceRecord *compliancePb.KYCCompliance) (*deeplink.Cta, error) {
	// EKYC to be shown in tertiary CTA if SMS was never triggered
	if complianceRecord.GetUserSmsTriggeredAt() == nil {
		return &deeplink.Cta{
			DisplayTheme: deeplink.Cta_TERTIARY,
			Type:         deeplink.Cta_CUSTOM,
			Text:         "Verify via Aadhaar instead",
			Deeplink:     ekycDl,
			Id:           compliancePb.Action_ACTION_EKYC_COMPLETED.String(),
		}, nil
	}
	return &deeplink.Cta{
		DisplayTheme: deeplink.Cta_PRIMARY,
		Type:         deeplink.Cta_CUSTOM,
		Text:         "Verify via Aadhaar",
		Deeplink:     ekycDl,
	}, nil
}

func (k *KYCComplianceService) startEKYCDeeplink(ctx context.Context, complianceRecord *compliancePb.KYCCompliance) (*deeplink.Deeplink, error) {
	bcRes, err := k.bcClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
		Identifier: &bankcust.GetBankCustomerRequest_ActorId{
			ActorId: complianceRecord.GetActorId(),
		},
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
	})
	if rpcErr := epifigrpc.RPCError(bcRes, err); rpcErr != nil {
		logger.Error(ctx, "error in getting bank customer record", zap.Error(rpcErr))
		return nil, rpcErr
	}
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_START_EKYC,
		ScreenOptions: &deeplink.Deeplink_StartEkycOptions{
			StartEkycOptions: &deeplink.StartEKYCOptions{
				KycLevel:   kycPkg.KycLevelMap[bcRes.GetBankCustomer().GetKycInfo().GetKycLevel()],
				EkycSource: kyc.EkycSource_EKYC_SOURCE_RE_KYC.String(),
				NextAction: ReKycDlWithEkycCompletedCallbackAction(complianceRecord),
				Title:      commontypes.GetTextFromHtmlStringFontColourFontStyle("Use Aadhaar details to do periodic KYC", "#333333", commontypes.FontStyle_SUBTITLE_1),
				Image: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/onboarding/aadhar_illustration.png").
					WithProperties(&commontypes.VisualElementProperties{
						Height: 240,
						Width:  240,
					}).WithProperties(&commontypes.VisualElementProperties{
					Height: 240,
					Width:  240,
				}).WithImageType(commontypes.ImageType_PNG),

				Description: commontypes.GetTextFromHtmlStringFontColourFontStyle("Our partner Federal Bank will verify your KYC with Aadhaar. Fi does not store or use your Aadhaar details", "#878A8D", commontypes.FontStyle_BODY_S),
			},
		},
	}, nil
}

func (k *KYCComplianceService) getNextActionForConfirmDetails(ctx context.Context, clientReq, actorId string) (*deeplink.Deeplink, error) {
	simId, err := k.getSimId(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error in getting sim id", zap.Error(err))
		return nil, err
	}
	opStatus, err := k.fetchOperationalStatusFromVendor(ctx, actorId, commonvgpb.Vendor_FEDERAL_BANK, operationalStatusPb.GetOperationalStatusRequest_DATA_FRESHNESS_LAST_KNOWN)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return nil, err
	}
	isAccountFrozen := opStatus.GetFreezeStatus() == enums.FreezeStatus_FREEZE_STATUS_DEBIT_FREEZE
	ekycCta, err := k.getEKYCCta(ctx, actorId)
	if err != nil {
		return nil, err
	}
	enableTertiaryCta := apputils.IsFeatureEnabledFromCtxDynamic(ctx, k.genConf.EnableTertiaryCta())
	logger.Debug(ctx, fmt.Sprintf("enable tertiary cta flag in compliance %v", enableTertiaryCta))
	rekycDlInstructions := k.getReKYCDeeplinkInstructions(ekycCta, enableTertiaryCta)
	return getReKYCSendSMSDeeplinkV2(ctx, int32(simId), &commontypes.PhoneNumber{
		CountryCode:    91,
		NationalNumber: k.genConf.PeriodicKYCSMSConfig().PhoneNumber(),
	}, smsBody, clientReq, isAccountFrozen, rekycDlInstructions), nil
}

func (k *KYCComplianceService) ProcessUserAck(ctx context.Context, req *compliancePb.ProcessUserAckRequest) (*compliancePb.ProcessUserAckResponse, error) {
	switch req.GetAckType() {
	case consent.AckType_ACK_TYPE_ONB_RE_KYC_SUCCESS_BANNER:
		return k.processReKYCSuccessBannerAck(ctx, req)
	default:
		return &compliancePb.ProcessUserAckResponse{
			Status: rpc.StatusInternalWithDebugMsg("ack type not implemented"),
		}, nil

	}
}

func (k *KYCComplianceService) processReKYCSuccessBannerAck(ctx context.Context, req *compliancePb.ProcessUserAckRequest) (*compliancePb.ProcessUserAckResponse, error) {
	logger.Info(ctx, "processing RE-KYC success banner ack", zap.String("ack_id", req.GetAckId()))
	complianceRecord, getErr := k.complianceDao.Get(ctx, dao.WithActorId(req.GetActorId()))
	if getErr != nil {
		logger.Error(ctx, "error in getting compliance record", zap.Error(getErr))
		return nil, getErr
	}
	if complianceRecord.GetComplianceMetadata() == nil {
		logger.WarnWithCtx(ctx, "compliance metadata not present in compliance record!!")
		complianceRecord.ComplianceMetadata = &compliancePb.ComplianceMetadata{}
	}
	complianceRecord.ComplianceMetadata.ShouldShowReKycSuccessBanner = commontypes.BooleanEnum_FALSE

	if updErr := k.complianceDao.Update(ctx, complianceRecord, []compliancePb.KYCComplianceFieldMask{
		compliancePb.KYCComplianceFieldMask_KYC_COMPLIANCE_FIELD_MASK_COMPLIANCE_METADATA,
	}); updErr != nil {
		logger.Error(ctx, "error in updating compliance record with ShouldShowReKycSuccessBanner flag", zap.Error(updErr))
		return nil, updErr
	}
	return &compliancePb.ProcessUserAckResponse{
		NextAction: &deeplink.Deeplink{
			Screen: deeplink.Screen_HOME,
		},
		Status: rpcPb.StatusOk(),
	}, nil
}
