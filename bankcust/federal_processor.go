package bankcust

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/genproto/googleapis/type/postaladdress"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/monitoring"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	preApprovedLoanPb "github.com/epifi/gamma/api/preapprovedloan"

	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/auth/afu"
	authNotificationPb "github.com/epifi/gamma/api/auth/notification"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/employment"
	"github.com/epifi/gamma/api/kyc"
	kycDocsPb "github.com/epifi/gamma/api/kyc/docs"
	"github.com/epifi/gamma/api/kyc/vkyc"
	savingsPb "github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"
	usersPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/group"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	vgHeaderPb "github.com/epifi/gamma/api/vendorgateway/openbanking/header"
	"github.com/epifi/gamma/bankcust/config/genconf"
	"github.com/epifi/gamma/bankcust/customer_creation/datafetcher"
	"github.com/epifi/gamma/bankcust/dao"
	profileManager "github.com/epifi/gamma/bankcust/profile_manager"
	wireTypes "github.com/epifi/gamma/bankcust/wire/types"
	addressPkg "github.com/epifi/gamma/pkg/address"
	"github.com/epifi/gamma/pkg/feature/release"
	"github.com/epifi/gamma/pkg/feature/release/config"
	"github.com/epifi/gamma/pkg/vendorstore"
	vkycPkg "github.com/epifi/gamma/pkg/vkyc"
	userSvc "github.com/epifi/gamma/user"
	ckycAddressDao "github.com/epifi/gamma/user/dao"
)

type FederalProc struct {
	conf                             *genconf.Config
	usersClient                      usersPb.UsersClient
	commsClient                      comms.CommsClient
	kycClient                        kyc.KycClient
	kycDocsExtractionClient          kycDocsPb.DocExtractionClient
	onbClient                        onbPb.OnboardingClient
	authClient                       auth.AuthClient
	employmentClient                 employment.EmploymentClient
	vgCustomerClient                 customer.CustomerClient
	dao                              dao.BankCustomerDao
	ckycAddressCodesDao              ckycAddressDao.CKycAddressCodesDao
	customerCreationPublisher        wireTypes.CustomerCreationPublisher
	bankCustomerUpdateEventPublisher wireTypes.BankCustomerUpdateEventPublisher
	eventBroker                      events.Broker
	vendorStore                      vendorstore.VendorStore
	methodAbstraction                MethodAbstraction
	vkycClient                       vkyc.VKYCClient
	timeClient                       datetime.Time
	txnExecutor                      storageV2.IdempotentTxnExecutor
	savingsClient                    savingsPb.SavingsClient
	profileManager                   profileManager.BankCustProfileManager
	idgen                            idgen.IdGenerator
	userGroupClient                  group.GroupClient
	actorClient                      actor.ActorClient
	preApprovedLoansClient           preApprovedLoanPb.PreApprovedLoanClient
	dataFetcher                      datafetcher.DataFetcher
}

func NewFederalProc(conf *genconf.Config, usersClient usersPb.UsersClient,
	commsClient comms.CommsClient, kycClient kyc.KycClient, authClient auth.AuthClient, employmentClient employment.EmploymentClient,
	vgCustomerClient customer.CustomerClient, dao dao.BankCustomerDao, ckycAddressCodesDao ckycAddressDao.CKycAddressCodesDao, customerCreationPublisher wireTypes.CustomerCreationPublisher,
	onboardingUserUpdateEventPublisher wireTypes.BankCustomerUpdateEventPublisher, eventBroker events.Broker, vendorStore vendorstore.VendorStore,
	vkycClient vkyc.VKYCClient, timeClient datetime.Time, txnExecutor storageV2.IdempotentTxnExecutor, savingsClient savingsPb.SavingsClient, profileManager profileManager.BankCustProfileManager,
	idgen idgen.IdGenerator, userGroupClient group.GroupClient, actorClient actor.ActorClient, kycDocsExtractionClient kycDocsPb.DocExtractionClient, onbClient onbPb.OnboardingClient,
	dataFetcher datafetcher.DataFetcher, preApprovedLoansClient preApprovedLoanPb.PreApprovedLoanClient) *FederalProc {
	fp := &FederalProc{
		conf:                             conf,
		usersClient:                      usersClient,
		commsClient:                      commsClient,
		kycClient:                        kycClient,
		authClient:                       authClient,
		employmentClient:                 employmentClient,
		vgCustomerClient:                 vgCustomerClient,
		dao:                              dao,
		ckycAddressCodesDao:              ckycAddressCodesDao,
		customerCreationPublisher:        customerCreationPublisher,
		bankCustomerUpdateEventPublisher: onboardingUserUpdateEventPublisher,
		eventBroker:                      eventBroker,
		vendorStore:                      vendorStore,
		vkycClient:                       vkycClient,
		timeClient:                       timeClient,
		txnExecutor:                      txnExecutor,
		savingsClient:                    savingsClient,
		profileManager:                   profileManager,
		idgen:                            idgen,
		userGroupClient:                  userGroupClient,
		actorClient:                      actorClient,
		kycDocsExtractionClient:          kycDocsExtractionClient,
		onbClient:                        onbClient,
		dataFetcher:                      dataFetcher,
		preApprovedLoansClient:           preApprovedLoansClient,
	}
	fp.methodAbstraction = fp
	return fp
}

var _ BankCustomerProc = &FederalProc{}

var bankCustomerStatusToCustomerCreationStatusMap = map[bankcust.CustomerCreationState]bankcust.Status{
	bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_UNSPECIFIED:                 bankcust.Status_STATUS_UNSPECIFIED,
	bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_INITIATED:                   bankcust.Status_STATUS_IN_PROGRESS,
	bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_IN_PROGRESS:                 bankcust.Status_STATUS_IN_PROGRESS,
	bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_CREATED:                     bankcust.Status_STATUS_ACTIVE,
	bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_FAILED:                      bankcust.Status_STATUS_FAILED,
	bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_DEDUPE_CUSTOMER:             bankcust.Status_STATUS_ACTIVE,
	bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_MAX_RETRY_CREATION_STEP:     bankcust.Status_STATUS_FAILED,
	bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_MAX_RETRY_STATUS_CHECK_STEP: bankcust.Status_STATUS_FAILED,
}

const (
	transactionalMaxRetry = uint(3)
	vendor                = commonvgpb.Vendor_FEDERAL_BANK
	NewSolId              = "NewFederalSolId"
	maxLengthLoanCIF      = 7 // Max length for loan CIF creation
	maxLengthDefault      = 5 // Default max length
)

var accountTypeToInActiveReasonMap = map[bankcust.AccountType]bankcust.CustomerInactiveReasonType{
	bankcust.AccountType_ACCOUNT_TYPE_NRE: bankcust.CustomerInactiveReasonType_CUSTOMER_INACTIVE_REASON_TYPE_NRE_CUSTOMER,
	bankcust.AccountType_ACCOUNT_TYPE_NRO: bankcust.CustomerInactiveReasonType_CUSTOMER_INACTIVE_REASON_TYPE_NRO_CUSTOMER,
}

var retryableFailures = []bankcust.FailureType{
	bankcust.FailureType_FAILURE_TYPE_REQUEST_MANUALLY_FAILED_FOR_RETRY,
	bankcust.FailureType_FAILURE_TYPE_FEDERAL_SERVER_TEMPORARILY_UNAVAILABLE,
	bankcust.FailureType_FAILURE_TYPE_ACTIVE_CUSTOMER_ALREADY_EXISTS_WITH_CUSTOMER_ID,
}

type MethodAbstraction interface {
	GetAddresses(context.Context, string, string, kyc.KycType, *kyc.KYCRecord, bankcust.Source) (*postaladdress.PostalAddress, *postaladdress.PostalAddress, error)
}

type ProcessCustomerCreationRequest struct {
	BankCustId  string
	IsLastRetry bool
}

type processCustomerCreationCallbackRequest struct {
	requestID        string
	vendorCustomerID string
	vendor           commonvgpb.Vendor
	status           bankcust.CustomerCreationState
	createdAt        *timestamppb.Timestamp
	vendorStatus     *commonvgpb.VendorStatus
}

type upgradeKycLevelRequest struct {
	actorId      string
	kycAttemptId string
	ekycRRN      string
}

func (fp *FederalProc) InitBankCustomer(ctx context.Context, req *initBankCustomerRequest) error {
	if req.bankCust == nil || req.source == bankcust.Source_SOURCE_UNSPECIFIED {
		logger.Info(ctx, "invalid arguments for init bank customer")
		return epifierrors.ErrInvalidArgument
	}
	bankCust := req.bankCust
	currState := bankCust.GetVendorMetadata().GetFederalMetadata().GetCustomerCreationState()
	if isCustomerCreationSuccess(currState) {
		return ErrCustCreationAlreadySuccess
	}
	if isCustomerCreationInProgress(currState) {
		return epifierrors.ErrInProgress
	}
	if isEligibleForCustomerCreation(bankCust) {
		if err := fp.updateCustomerCreationSourceAndResidency(ctx, bankCust, req.source); err != nil {
			return err
		}
		if err := fp.updateSolId(ctx, bankCust.GetActorId()); err != nil {
			return err
		}
		if err := fp.publishInitPacket(ctx, bankCust.GetId()); err != nil {
			return err
		}
		if isDedupeCustomer(currState) {
			return nil
		}
		return fp.updateCustomerCreationState(ctx, bankCust.GetActorId(), bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_INITIATED)
	}
	return ErrInvalidBankCustForInit
}

func (fp *FederalProc) ForceRetryBankCustomer(ctx context.Context, req *forceRetryBankCustomer) error {
	bankCust, err := fp.dao.GetByActorId(ctx, req.actorId, vendor)
	if err != nil {
		logger.Error(ctx, "error while fetching bank customer", zap.Error(err))
		return err
	}
	return fp.handleForceRetry(ctx, req.forceRetryType, bankCust)
}

func (fp *FederalProc) CheckBankCustomerCreationStatus(ctx context.Context, req *checkBankCustomerCreationStatus) (*bankcust.CheckBankCustomerCreationStatusResponse, error) {
	if err := validateCustomerState(ctx, req.bankCust); err != nil {
		logger.Error(ctx, "bank customer not in consistent state")
		return &bankcust.CheckBankCustomerCreationStatusResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	switch {
	case isCustomerCreationSuccess(req.bankCust.GetVendorMetadata().GetFederalMetadata().GetCustomerCreationState()):
		logger.Info(ctx, "customer creation already successful")
		return &bankcust.CheckBankCustomerCreationStatusResponse{
			Status: rpc.StatusOk(),
		}, nil
	case isCustomerCreationNonRetryableFailure(req.bankCust):
		logger.Info(ctx, "customer creation failed with non retryable failure")
		return &bankcust.CheckBankCustomerCreationStatusResponse{
			Status: rpc.NewStatusFactory(uint32(bankcust.CheckBankCustomerCreationStatusResponse_NON_RETRYABLE_FAILURE), "customer creation failed with retryable error")(),
		}, nil
	case isCustomerCreationRetryableFailure(req.bankCust):
		logger.Info(ctx, "customer creation failed with retryable failure", zap.String(logger.STATUS, req.bankCust.GetFailureReason().GetFailureType().String()))
		return &bankcust.CheckBankCustomerCreationStatusResponse{
			Status: rpc.NewStatusFactory(uint32(bankcust.CheckBankCustomerCreationStatusResponse_RETRYABLE_FAILURE), "customer creation failed with retryable error")(),
		}, nil
	case isCustomerCreationNotStarted(req.bankCust.GetVendorMetadata().GetFederalMetadata().GetCustomerCreationState()):
		logger.Info(ctx, "customer creation not initiated yet")
		return &bankcust.CheckBankCustomerCreationStatusResponse{
			Status: rpc.NewStatusFactory(uint32(bankcust.CheckBankCustomerCreationStatusResponse_NOT_STARTED), "customer creation not started")(),
		}, nil
	case isCustomerCreationInProgress(req.bankCust.GetVendorMetadata().GetFederalMetadata().GetCustomerCreationState()):
		logger.Info(ctx, "customer creation is in progress", zap.String(logger.STATUS, req.bankCust.GetVendorMetadata().GetFederalMetadata().GetCustomerCreationState().String()))
		return &bankcust.CheckBankCustomerCreationStatusResponse{
			Status: rpc.ExtendedStatusInProgress(),
		}, nil
	default:
		logger.Error(ctx, fmt.Sprintf("bank customer not in any handled state. Status: %v ; State: %v", req.bankCust.GetStatus().String(), req.bankCust.GetVendorMetadata().GetFederalMetadata().GetCustomerCreationState().String()))
		return &bankcust.CheckBankCustomerCreationStatusResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
}

func (fp *FederalProc) handleForceRetry(ctx context.Context, forceRetryParams bankcust.ForceRetryBankCustomerRequest_ForceRetryParameters_ForceRetryType, bankCust *bankcust.BankCustomer) error {
	currState := bankCust.GetVendorMetadata().GetFederalMetadata().GetCustomerCreationState()
	if currState == bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_DEDUPE_CUSTOMER {
		if err := fp.publishInitPacket(ctx, bankCust.GetId()); err != nil {
			return err
		}
	}
	if currState == bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_CREATED {
		logger.Info(ctx, "cannot force retry for users in created state")
		return nil
	}
	switch forceRetryParams {
	case bankcust.ForceRetryBankCustomerRequest_ForceRetryParameters_FORCE_ENQUIRY:
		if err := fp.updateCustomerCreationState(ctx, bankCust.GetActorId(), bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_IN_PROGRESS); err != nil {
			return err
		}
		return fp.publishInitPacket(ctx, bankCust.GetId())
	case bankcust.ForceRetryBankCustomerRequest_ForceRetryParameters_FORCE_NEW_REQUEST:
		if err := fp.updateCustomerCreationState(ctx, bankCust.GetActorId(), bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_UNSPECIFIED); err != nil {
			return err
		}
		bankCustomer, err := fp.dao.GetByActorId(ctx, bankCust.GetActorId(), vendor)
		if err != nil {
			logger.Error(ctx, "error while fetching bank customer", zap.Error(err))
			return err
		}
		return fp.InitBankCustomer(ctx, &initBankCustomerRequest{
			bankCust: bankCustomer,
			// using existing source in retries to avoid overriding current source
			source: bankCustomer.GetSource(),
		})
	default:
		return fmt.Errorf("unhandled force retry type")
	}
}

func (fp *FederalProc) publishInitPacket(ctx context.Context, id string) error {
	if _, err := fp.customerCreationPublisher.PublishWithDelay(ctx, &bankcust.ProcessCustomerCreationRequest{
		BankCustId: id,
	}, fp.conf.FederalProcessorConfig().PublishDelay()); err != nil {
		logger.Error(ctx, "error in publishing bank customer creation packet", zap.Error(err))
		return err
	}
	return nil
}

func (fp *FederalProc) ProcessCustomerCreation(ctx context.Context, req *ProcessCustomerCreationRequest) error {
	bankCustomer, err := fp.dao.GetById(ctx, req.BankCustId)
	if err != nil {
		logger.Error(ctx, "error fetching bank customer", zap.Error(err))
		return err
	}
	ctx = epificontext.CtxWithActorId(ctx, bankCustomer.GetActorId())
	currStatus := bankCustomer.GetVendorMetadata().GetFederalMetadata().GetCustomerCreationState()
	logger.Info(ctx, "processing customer creation packet", zap.String(logger.USER_ID, bankCustomer.GetUserId()), zap.String(logger.STATE, currStatus.String()))
	if err = fp.handleMaxRetry(ctx, bankCustomer.GetActorId(), currStatus, req.IsLastRetry); err != nil {
		return err
	}
	return fp.processCustomerCreationStateMachine(ctx, currStatus, bankCustomer)
}

func (fp *FederalProc) ProcessCustomerCreationCallback(ctx context.Context, req *processCustomerCreationCallbackRequest) error {
	var (
		requestId      = req.requestID
		bankCustomerId = req.vendorCustomerID
		logReqId       = zap.String(logger.REQUEST_ID, requestId)
		failLog        = func(err error) {
			logger.Error(ctx, "customer creation failed through callback", logReqId, zap.Error(err))
		}
	)
	if req.vendor != vendor {
		return fmt.Errorf("invalid vendor for callback: %v", req.vendor)
	}
	logger.Info(ctx, fmt.Sprintf("received customer creation callback request: %s", req.status), logReqId)
	bankCustomer, err := fp.dao.GetByRequestId(ctx, requestId)
	if err != nil {
		logger.Error(ctx, "failed to fetch user by request id", zap.Error(err), logReqId)
		return err
	}
	actorID := bankCustomer.GetActorId()
	ctx = epificontext.CtxWithActorId(ctx, actorID)
	// Send the vendor codes received to Vendor Store
	_ = fp.logVendorStore(ctx, actorID, req.vendorStatus, rpc.StatusOk(), vendorstore.API_CUSTOMER_CREATION_CALLBACK, requestId)
	if !isCustomerCreationTerminalState(req.status) {
		failLog(fmt.Errorf("unexpected creation status: %v", req.status))
		return errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("unexpected creation status: %v", req.status))
	}
	// Update bank customer
	if isCustomerCreationSuccess(req.status) {
		return fp.handleCustomerCreationSuccess(ctx, bankCustomer.GetUserId(), actorID, bankCustomerId, req.createdAt, false)
	}
	return fp.handleCustomerCreationFailure(ctx, bankCustomer, bankcust.FailureType_FAILURE_TYPE_UNSPECIFIED)
}

func (fp *FederalProc) UpgradeKYCLevel(ctx context.Context, req *upgradeKycLevelRequest) error {
	bankCustomer, err := fp.dao.GetByActorId(ctx, req.actorId, vendor)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Info(ctx, "failed to find bank customer", zap.Error(err))
			return nil
		}
		logger.Error(ctx, "error in fetching bank customer", zap.Error(err))
		return err
	}
	return fp.handleKYCUpgrade(ctx, req, bankCustomer)
}

func (fp *FederalProc) handleKYCUpgrade(ctx context.Context, req *upgradeKycLevelRequest, bankCust *bankcust.BankCustomer) error {
	switch bankCust.GetVendorMetadata().GetFederalMetadata().GetKycUpgradeInfo().GetStatus() {
	case bankcust.KYCUpgradeStatus_KYC_UPGRADE_STATUS_UNSPECIFIED:
		resp, errResp := fp.kycClient.GetKYCRecord(ctx, &kyc.GetKYCRecordRequest{
			ActorId: req.actorId,
		})
		if rpcErr := epifigrpc.RPCError(resp, errResp); rpcErr != nil {
			if resp.GetStatus().IsRecordNotFound() {
				logger.Info(ctx, "kyc record is not available, ignoring packet")
				return nil
			}
			logger.Error(ctx, "error in getting kyc record", zap.Error(rpcErr))
			return rpcErr
		}
		if err := fp.validateUpgradeKYCLevelRequest(ctx, req, bankCust, resp); err != nil {
			if errors.Is(err, errFailedValidations) {
				return nil
			}
			return err
		}
		if err := fp.updateKYCUpgradeStatus(ctx, req.actorId, bankcust.KYCUpgradeStatus_KYC_UPGRADE_STATUS_INITIATED); err != nil {
			return err
		}
		return epifierrors.ErrTransient
	case bankcust.KYCUpgradeStatus_KYC_UPGRADE_STATUS_INITIATED:
		return fp.handleKycUpgradeInitiated(ctx, req, bankCust)
	case bankcust.KYCUpgradeStatus_KYC_UPGRADE_STATUS_IN_PROGRESS:
		return fp.handleKycUpgradeInProgress(ctx, req, bankCust)
	case bankcust.KYCUpgradeStatus_KYC_UPGRADE_STATUS_FAILED,
		bankcust.KYCUpgradeStatus_KYC_UPGRADE_STATUS_COMPLETED:
		logger.Info(ctx, "kyc upgrade already reached a terminal state, ignoring")
		return nil
	default:
		logger.Error(ctx, "unhandled state in kyc upgrade", zap.String(logger.STATUS,
			bankCust.GetVendorMetadata().GetFederalMetadata().GetKycUpgradeInfo().GetStatus().String()))
		return epifierrors.ErrPermanent
	}
}

func (fp *FederalProc) updateKYCUpgradeStatus(ctx context.Context, actorId string, status bankcust.KYCUpgradeStatus) error {
	if err := fp.dao.UpdateByActorId(ctx, &bankcust.BankCustomer{
		ActorId: actorId,
		Vendor:  vendor,
		VendorMetadata: &bankcust.VendorMetadata{
			Vendor: &bankcust.VendorMetadata_FederalMetadata{
				FederalMetadata: &bankcust.FederalMetadata{
					KycUpgradeInfo: &bankcust.KycUpgradeInfo{
						Status: status,
					},
				},
			},
		},
	}, []bankcust.BankCustomerFieldMask{
		bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_FEDERAL_KYC_UPGRADE_STATUS,
	}); err != nil {
		logger.Error(ctx, "error in updating kyc upgrade status", zap.Error(err))
		return err
	}
	return nil
}

func (fp *FederalProc) handleKycUpgradeInitiated(ctx context.Context, req *upgradeKycLevelRequest, bankCust *bankcust.BankCustomer) error {
	reqId := fp.idgen.GetInAlphaNumeric(idgen.KYCUpgradeRequest)
	resp, errResp := fp.kycClient.GetKYCRecord(ctx, &kyc.GetKYCRecordRequest{
		ActorId:   req.actorId,
		SignImage: true,
	})
	if rpcErr := epifigrpc.RPCError(resp, errResp); rpcErr != nil {
		logger.Error(ctx, "error in getting kyc record", zap.Error(rpcErr))
		return rpcErr
	}
	if err := fp.dao.UpdateByActorId(ctx, &bankcust.BankCustomer{
		ActorId: req.actorId,
		Vendor:  vendor,
		VendorMetadata: &bankcust.VendorMetadata{
			Vendor: &bankcust.VendorMetadata_FederalMetadata{
				FederalMetadata: &bankcust.FederalMetadata{
					KycUpgradeInfo: &bankcust.KycUpgradeInfo{
						RequestId:        reqId,
						UpgradeStartedAt: timestamppb.New(fp.timeClient.Now()),
						EkycRrnNo:        req.ekycRRN,
					},
				},
			},
		},
	}, []bankcust.BankCustomerFieldMask{
		bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_FEDERAL_KYC_UPGRADE_REQUEST_ID,
		bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_FEDERAL_KYC_UPGRADE_STARTED_AT,
		bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_FEDERAL_KYC_UPGRADE_EKYC_RRN,
	}); err != nil {
		logger.Error(ctx, "error in updating kyc upgrade started at", zap.Error(err))
		return err
	}
	if err := fp.updateKYCUpgradeStatus(ctx, req.actorId, bankcust.KYCUpgradeStatus_KYC_UPGRADE_STATUS_IN_PROGRESS); err != nil {
		return err
	}
	if req.kycAttemptId == "" {
		logger.Error(ctx, "cannot update profile at bank : kyc attempt id is empty")
		return epifierrors.ErrPermanent
	}
	// Perform vendor call to upgrade KYC level
	updProfileResp, errResp := fp.profileManager.UpdateProfileAtBank(ctx, &bankcust.UpdateProfileAtBankRequest{
		ClientRequestId: reqId,
		ActorId:         req.actorId,
		Vendor:          vendor,
		ProfileFields:   []bankcust.ProfileField{bankcust.ProfileField_PROFILE_FIELD_KYC_LEVEL},
		KycAttemptId:    req.kycAttemptId,
		Channel:         bankcust.ProfileUpdateChannel_PROFILE_UPDATE_CHANNEL_BIOMETRIC,
	})
	if errResp != nil {
		monitoring.KibanaErrorServiceMonitor(ctx, cfg.ONBOARDING_SERVICE, "error in vg call for upgrade kyc level", zap.Error(errResp))
		// Move status back to initiated if profile update fails
		if err := fp.updateKYCUpgradeStatus(ctx, req.actorId, bankcust.KYCUpgradeStatus_KYC_UPGRADE_STATUS_INITIATED); err != nil {
			return err
		}
		return errResp
	}
	if updProfileResp != nil && updProfileResp.VendorRequestStartedAt != nil {
		logger.Info(ctx, "kyc level upgrade started", zap.String(logger.REQUEST_ID, reqId), zap.Time(logger.START_TIME, updProfileResp.VendorRequestStartedAt.AsTime()))
	}
	return epifierrors.ErrTransient
}

func (fp *FederalProc) handleKycUpgradeInProgress(ctx context.Context, req *upgradeKycLevelRequest, bankCust *bankcust.BankCustomer) error {
	if err := fp.checkKYCUpgradeStatus(ctx, bankCust, req.ekycRRN); err != nil {
		if errors.Is(err, epifierrors.ErrInProgress) {
			logger.Info(ctx, "kyc upgrade is in progress")
		}
		return err
	}
	return fp.handleKYCUpgradeSuccess(ctx, bankCust)
}

type ProcessKYCStateChangeRequest struct {
	kycStateChangeEvent *bankcust.ProcessKYCStateChangeEventRequest
}

func (fp *FederalProc) ProcessKYCStateChange(ctx context.Context, req *ProcessKYCStateChangeRequest) error {
	if req.kycStateChangeEvent == nil {
		return fmt.Errorf("kyc state change event is not passed in request : %w", epifierrors.ErrPermanent)
	}

	ekycRrn := vkycPkg.GetCustomerCreationEkycRrn(req.kycStateChangeEvent.GetUniqueUidaiRrn())
	if ekycRrn == "" {
		logger.Info(ctx, "empty ekyc rrn, discarding event")
		return nil
	}

	kycAttemptResp, getKycAttemptErr := fp.kycClient.GetKYCAttempt(ctx, &kyc.GetKYCAttemptRequest{
		Identifier: &kyc.GetKYCAttemptRequest_EkycRrn{
			EkycRrn: ekycRrn,
		},
	})
	if rpcErr := epifigrpc.RPCError(kycAttemptResp, getKycAttemptErr); rpcErr != nil {
		if kycAttemptResp.GetStatus().IsRecordNotFound() {
			logger.Info(ctx, "kyc attempt not found, dropping packet")
			return nil
		}
		logger.Error(ctx, "error while getting kyc attempt", zap.Error(getKycAttemptErr))
		return rpcErr
	}

	actorId := kycAttemptResp.GetKycAttempt().GetActorId()
	ctx = epificontext.CtxWithActorId(ctx, actorId)

	bankCustomer, err := fp.dao.GetByActorId(ctx, actorId, vendor)
	if err != nil {
		logger.Error(ctx, "error in fetching bank customer by actor id", zap.Error(err))
		return err
	}

	if bankCustomer.GetVendorMetadata().GetFederalMetadata().GetKycUpgradeInfo().GetStatus() != bankcust.KYCUpgradeStatus_KYC_UPGRADE_STATUS_IN_PROGRESS {
		logger.Info(ctx, "kyc upgrade is not in progress, ignoring packet")
		return nil
	}

	if !strings.EqualFold(req.kycStateChangeEvent.GetKycStatus(), "Y") {
		monitoring.KibanaInfoServiceMonitor(ctx, cfg.ONBOARDING_SERVICE, fmt.Sprintf("received %s KYC status, discarding event", req.kycStateChangeEvent.GetKycStatus()), zap.String("ekycRrn", ekycRrn))
		return nil
	}

	return fp.handleKYCUpgradeSuccess(ctx, bankCustomer)
}

// handleKYCUpgradeSuccess is supposed to be called to handle success scenarios from both enquiry and callback upon successful indication of
// KYC upgrade.
func (fp *FederalProc) handleKYCUpgradeSuccess(ctx context.Context, bankCust *bankcust.BankCustomer) error {
	// TODO(Shivam): Configure comms on success.
	if err := fp.dao.UpdateByActorId(ctx, &bankcust.BankCustomer{
		ActorId: bankCust.GetActorId(),
		Vendor:  vendor,
		VendorMetadata: &bankcust.VendorMetadata{
			Vendor: &bankcust.VendorMetadata_FederalMetadata{
				FederalMetadata: &bankcust.FederalMetadata{
					KycUpgradeInfo: &bankcust.KycUpgradeInfo{
						UpgradeCompletedAt: timestamppb.New(fp.timeClient.Now()),
					},
				},
			},
		},
	}, []bankcust.BankCustomerFieldMask{
		bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_FEDERAL_KYC_UPGRADE_COMPLETED_AT,
	}); err != nil {
		logger.Error(ctx, "error in updating kyc upgrade started at", zap.Error(err))
		return err
	}
	if err := fp.UpgradeToFullKYC(ctx, bankCust, bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_BKYC_POST_ONBOARDING); err != nil {
		return err
	}
	return fp.updateKYCUpgradeStatus(ctx, bankCust.GetActorId(), bankcust.KYCUpgradeStatus_KYC_UPGRADE_STATUS_COMPLETED)
}

func (fp *FederalProc) checkKYCUpgradeStatus(ctx context.Context, bankCust *bankcust.BankCustomer, uidRefNo string) error {
	upgradeStatus, errRes := fp.profileManager.CheckProfileUpdateStatus(ctx, &bankcust.CheckProfileUpdateStatusRequest{
		ClientRequestId: bankCust.GetVendorMetadata().GetFederalMetadata().GetKycUpgradeInfo().GetRequestId(),
	})
	if errRes != nil {
		logger.Error(ctx, "error in checking profile update status", zap.Error(errRes))
		return errRes
	}

	switch upgradeStatus.Status {
	case stagePb.Status_SUCCESSFUL:
		logger.Info(ctx, "kyc upgrade successful")
		fp.logDedupeStatusAsync(ctx, bankCust.GetActorId(), uidRefNo)
		return nil
	case stagePb.Status_FAILED:
		logger.Info(ctx, "kyc upgrade failed")
		return epifierrors.ErrPermanent
	case stagePb.Status_MANUAL_INTERVENTION:
		logger.Info(ctx, "kyc upgrade in manual intervention")
		return epifierrors.ErrPermanent
	case stagePb.Status_PENDING, stagePb.Status_CREATED, stagePb.Status_INITIATED:
		return epifierrors.ErrInProgress
	default:
		logger.Info(ctx, "checkKYCUpgradeStatus: unhandled profile update status", zap.String(logger.STATUS, upgradeStatus.Status.String()))
		return epifierrors.ErrInProgress
	}
}

func (fp *FederalProc) logDedupeStatusAsync(ctx context.Context, actorId, uidRefNo string) {
	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		userResp, errResp := fp.usersClient.GetUser(ctx, &usersPb.GetUserRequest{
			Identifier: &usersPb.GetUserRequest_ActorId{
				ActorId: actorId,
			},
		})

		if err := epifigrpc.RPCError(userResp, errResp); err != nil {
			logger.Error(ctx, "error in getting user by actor id", zap.Error(err))
			return
		}

		dedupeResp, errResp := fp.usersClient.DedupeCheck(ctx, &usersPb.DedupeCheckRequest{
			ActorId:         actorId,
			Vendor:          vendor,
			PanNumber:       userResp.GetUser().GetProfile().GetPAN(),
			PhoneNumber:     userResp.GetUser().GetProfile().GetPhoneNumber(),
			UidReferenceKey: uidRefNo,
			DateOfBirth:     userResp.GetUser().GetProfile().GetDateOfBirth(),
			EmailId:         userResp.GetUser().GetProfile().GetEmail(),
		})
		if err := epifigrpc.RPCError(dedupeResp, errResp); err != nil {
			logger.Error(ctx, "error in checking dedupe status", zap.Error(err))
			return
		}
		logger.Info(ctx, "dedupe status post successful kyc upgrade", zap.String(logger.STATUS, dedupeResp.GetDedupeStatus().String()),
			zap.String("uidRefNo", uidRefNo))
	})
}

func (fp *FederalProc) validateUpgradeKYCLevelRequest(ctx context.Context, req *upgradeKycLevelRequest, bankCustomer *bankcust.BankCustomer, resp *kyc.GetKYCRecordResponse) error {
	userGroupConstraintData := release.NewUserGroupConstraintData(&config.UserGroupConstraintConfig{
		AllowedGroups: []commontypes.UserGroup{
			commontypes.UserGroup_POST_ONBOARDING_BKYC_INTERNAL,
		},
	})
	userGroupConstraint := release.NewUserGroupConstraint(fp.actorClient, fp.usersClient, fp.userGroupClient)
	isEnabled, err := userGroupConstraint.Evaluate(ctx, userGroupConstraintData,
		release.NewCommonConstraintData(types.Feature_FEATURE_UNSPECIFIED).WithActorId(req.actorId))
	if err != nil {
		logger.Error(ctx, "error in evaluating user group constraint", zap.Error(err))
		return err
	}
	if isEnabled {
		logger.Info(ctx, "user in POST_ONBOARDING_BKYC_INTERNAL group, ignoring vendor calls for the user")
		return errFailedValidations
	}
	if bankCustomer.GetStatus() != bankcust.Status_STATUS_ACTIVE {
		logger.Info(ctx, "customer is not yet active, not eligible for post onboarding upgrade", zap.String(logger.STATUS, bankCustomer.GetStatus().String()))
		return errFailedValidations
	}
	if bankCustomer.GetKycInfo().GetKycLevel() == kyc.KYCLevel_FULL_KYC {
		logger.Info(ctx, "user is already full kyc and hence not eligible for upgrade", zap.String(logger.STATUS, bankCustomer.GetKycInfo().GetKycLevel().String()))
		return errFailedValidations
	}
	vkycRecord, err := fp.getVKYCSummary(ctx, req.actorId)
	if err != nil {
		return err
	}
	if vkycRecord.GetVkycSummary().GetStatus() == vkyc.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_IN_REVIEW {
		logger.Info(ctx, "vkyc is in review, ignoring upgrade")
		return errFailedValidations
	}
	if resp.GetKycType() != kyc.KycType_BKYC {
		logger.Info(ctx, fmt.Sprintf("fetched kyc record is of kycType: %v", resp.GetKycType().String()))
		return errFailedValidations
	}
	if resp.GetKycStatus() == kyc.KycStatus_IN_PROGRESS {
		logger.Info(ctx, fmt.Sprintf("fetched kyc record is of kycType: %v, and kyc status: %v", resp.GetKycType().String(), resp.GetKycStatus().String()))
		return epifierrors.ErrTransient
	}
	if resp.GetKycStatus() == kyc.KycStatus_ERRORED {
		logger.Info(ctx, fmt.Sprintf("fetched kyc record is of kycType: %v, and kyc status: %v", resp.GetKycType().String(), resp.GetKycStatus().String()))
		return errFailedValidations
	}
	if resp.GetKycStatus() != kyc.KycStatus_COMPLETED {
		logger.Info(ctx, fmt.Sprintf("fetched kyc record is of kycType: %v, and kyc status: %v", resp.GetKycType().String(), resp.GetKycStatus().String()))
		return errFailedValidations
	}
	if resp.GetKycAttemptId() != req.kycAttemptId {
		logger.Info(ctx, "kyc attempt id does not match", zap.Error(err))
		return errFailedValidations
	}
	return nil
}

func (fp *FederalProc) UpgradeToFullKYC(ctx context.Context, bankCust *bankcust.BankCustomer, kycLevelUpdateFlow bankcust.KycLevelUpdateFlow) error {
	if bankCust.GetKycInfo().GetKycLevel() == kyc.KYCLevel_FULL_KYC {
		logger.Info(ctx, "customer already marked Full KYC")
		return nil
	}
	if err := fp.dao.UpdateByActorId(ctx, &bankcust.BankCustomer{
		ActorId: bankCust.GetActorId(),
		Vendor:  vendor,
		DedupeInfo: &bankcust.DedupeInfo{
			KycLevel: kyc.KYCLevel_FULL_KYC,
		},
		KycInfo: &bankcust.KYCInfo{
			KycLevel: kyc.KYCLevel_FULL_KYC,
		},
		KycLevelUpdateFlow: kycLevelUpdateFlow,
	}, []bankcust.BankCustomerFieldMask{
		bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_KYC_LEVEL,
		bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_KYC_LEVEL_UPDATE_FLOW,
		bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_KYC_INFO_KYC_LEVEL,
	}); err != nil {
		logger.Error(ctx, "error in updating kyc level to full kyc", zap.Error(err))
		return err
	}
	logger.Info(ctx, "upgrade is successful for customer")
	accountRes, errResp := fp.savingsClient.GetSavingsAccountEssentials(ctx, &savingsPb.GetSavingsAccountEssentialsRequest{
		Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
			ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
				ActorId:     bankCust.GetActorId(),
				PartnerBank: vendor,
			},
		},
	})
	if err := epifigrpc.RPCError(accountRes, errResp); err != nil {
		if accountRes.GetStatus().IsRecordNotFound() {
			logger.Info(ctx, "account not created, returning early")
			return nil
		}
		logger.Error(ctx, "error in fetching account", zap.Error(err))
		return err
	}
	if accountRes.GetAccount().GetSkuInfo().GetSku() == savingsPb.SKU_INFINITY {
		logger.Info(ctx, "account already marked full kyc")
		return nil
	}
	updateAccRes, errResp := fp.savingsClient.UpdateAccount(ctx, &savingsPb.UpdateAccountRequest{
		Identifier: &savingsPb.UpdateAccountRequest_PrimaryAccountHolder{
			PrimaryAccountHolder: bankCust.GetUserId(),
		},
		SkuInfo: &savingsPb.SKUInfo{
			VendorSkuInfo: &savingsPb.SKUInfo_Federal{
				Federal: &savingsPb.FederalSKUInfo{
					Sku: "35032",
				},
			},
			Sku: savingsPb.SKU_INFINITY,
		},
		UpdateMask: []savingsPb.AccountFieldMask{
			savingsPb.AccountFieldMask_SKU_INFO_AND_METADATA,
		},
	})
	if err := epifigrpc.RPCError(updateAccRes, errResp); err != nil {
		logger.Error(ctx, "error in updating account resp", zap.Error(err))
		return err
	}
	logger.Info(ctx, "upgrade is successful for account")
	return nil
}

// handleCustomerCreationSuccess performs the following tasks when we receive SUCCESS in either Customer creation enquiry or callback
// 1. Mark Customer Creation state to success in DB
// 2. Set the Creation Time and Vendor Customer ID in DB
// 3. Send an SMS to the registered number regarding mobile number update
// 4. Publish packet to BankCustomerUpdateTopic
// 5. Trigger Account Creation for the customer
func (fp *FederalProc) handleCustomerCreationSuccess(ctx context.Context, userID, actorID, vendorId string, createdAt *timestamppb.Timestamp, isDedupe bool) error {
	if !isDedupe && !fp.conf.IsMobileUpdateSmsDisabledFromEpifi() {
		profile, err := fp.getUserProfile(ctx, userID)
		if err != nil {
			return err
		}
		_, _ = fp.sendSMSForPhoneNumUpdate(ctx, vendorId, createdAt, profile.GetPhoneNumber())
	}

	if err := fp.updateBankCustForSuccess(ctx, actorID, vendorId, createdAt); err != nil {
		if !errors.Is(err, epifierrors.ErrRowNotUpdated) {
			logger.Error(ctx, "customer creation failed", zap.Error(fmt.Errorf("error in update customer creation details: %w", err)))
			return err
		}
		return nil
	}

	bankCustomer, err := fp.dao.GetByActorId(ctx, actorID, vendor)
	if err != nil {
		logger.Error(ctx, "error in getting bank customer by actor id", zap.Error(err))
		return err
	}

	fp.logCompletedCustomerEvent(ctx, actorID, bankCustomer.GetVendorMetadata().GetFederalMetadata().GetCifRequestId(), bankCustomer.GetKycInfo().GetKycLevel(), createdAt, bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_CREATED, "", "")
	return nil
}

func (fp *FederalProc) handleCustomerCreationFailure(ctx context.Context, bankCustomer *bankcust.BankCustomer, failureReason bankcust.FailureType) error {
	currStatus := bankCustomer.GetVendorMetadata().GetFederalMetadata().GetCustomerCreationState()
	if currStatus == bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_FAILED {
		logger.Info(ctx, "cannot change failed customer creation status")
		return nil
	}
	if err := fp.updateFailureReason(ctx, bankCustomer.GetActorId(), failureReason); err != nil {
		return fmt.Errorf("db update for customer status failure reason failed: %w", err)

	}
	if err := fp.updateCustomerCreationState(ctx, bankCustomer.GetActorId(), bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_FAILED); err != nil {
		return fmt.Errorf("db update for customer status failed: %w", err)
	}
	if _, err := fp.bankCustomerUpdateEventPublisher.Publish(ctx, &bankcust.BankCustomerUpdateEvent{
		ActorId:        bankCustomer.GetActorId(),
		Vendor:         vendor,
		EventTimestamp: timestamppb.New(fp.timeClient.Now()),
		EventId:        uuid.New().String(),
		UpdatedFieldMask: []bankcust.BankCustomerFieldMask{
			bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_STATUS,
			bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_CUSTOMER_CREATION_STATE,
			bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_BANK_CUSTOMER_FAILURE_REASON,
		},
	}); err != nil {
		logger.Error(ctx, "error publishing customer creation update status", zap.Error(err))
	}
	return nil
}

func (fp *FederalProc) updateBankCustForSuccess(ctx context.Context, actorID, vendorId string, createdAt *timestamppb.Timestamp) error {
	if txnErr := fp.txnExecutor.RunIdempotentTxn(ctx, transactionalMaxRetry, func(ctx context.Context) error {
		bankCustomer, err := fp.dao.GetByActorId(ctx, actorID, vendor)
		if err != nil {
			logger.Error(ctx, "failed to fetch bank customer", zap.Error(err))
			return err
		}
		currStatus := bankCustomer.GetVendorMetadata().GetFederalMetadata().GetCustomerCreationState()

		if isDedupeCustomer(currStatus) {
			return fp.handleCustomerCreationStateForDedupe(ctx, actorID, bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_CREATED, vendorId)
		}

		// current customer creation status validations
		if isCustomerCreationSuccess(currStatus) {
			logger.Info(ctx, "customer already created")
			return epifierrors.ErrRowNotUpdated
		}
		// Update Customer Creation State
		if err = fp.updateCustomerCreationState(ctx, actorID, bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_CREATED); err != nil {
			return fmt.Errorf("db update for customer status failed: %w", err)
		}
		// set new bank customer details
		bankCustomer.VendorCustomerId = vendorId
		bankCustomer.VendorCreationSucceededAt = createdAt
		bankCustomer.FiCreationSucceededAt = timestamppb.New(fp.timeClient.Now())

		// update new bank customer details in DB
		if err = fp.dao.UpdateByActorId(ctx, bankCustomer, []bankcust.BankCustomerFieldMask{
			bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_VENDOR_CUSTOMER_ID,
			bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_VENDOR_CREATION_SUCCEEDED_AT,
			bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_FI_CREATION_SUCCEEDED_AT,
		}); err != nil {
			logger.Error(ctx, "error while updating bank customer status in DB", zap.Error(err))
			return err
		}
		return nil

	}); txnErr != nil {
		if errors.Is(txnErr, epifierrors.ErrRowNotUpdated) {
			return epifierrors.ErrRowNotUpdated
		}
		logger.Error(ctx, "txn error in update customer creation status", zap.Error(txnErr))
		return txnErr
	}
	if _, err := fp.bankCustomerUpdateEventPublisher.Publish(ctx, &bankcust.BankCustomerUpdateEvent{
		ActorId:        actorID,
		Vendor:         vendor,
		EventTimestamp: timestamppb.New(fp.timeClient.Now()),
		EventId:        uuid.New().String(),
		UpdatedFieldMask: []bankcust.BankCustomerFieldMask{
			bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_STATUS,
			bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_CUSTOMER_CREATION_STATE,
			bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_VENDOR_CUSTOMER_ID,
			bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_VENDOR_CREATION_SUCCEEDED_AT,
			bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_FI_CREATION_SUCCEEDED_AT,
		},
	}); err != nil {
		logger.Error(ctx, "error publishing customer creation update status", zap.Error(err))
	}
	return nil
}

func (fp *FederalProc) sendSMSForPhoneNumUpdate(ctx context.Context, bankCustomerId string, createdAt *timestamppb.Timestamp, phoneNum *commontypes.PhoneNumber) (*comms.SendMessageResponse, error) {
	if len(bankCustomerId) < 4 {
		return nil, fmt.Errorf("invalid string %s", bankCustomerId)
	}
	customerID4Digits := bankCustomerId[len(bankCustomerId)-4:]
	sendSmsRes, err := fp.commsClient.SendMessage(ctx, &comms.SendMessageRequest{
		Type:   comms.QoS_GUARANTEED,
		Medium: comms.Medium_SMS,
		UserIdentifier: &comms.SendMessageRequest_PhoneNumber{
			PhoneNumber: phoneNum.ToStringNationalNumber(),
		},
		Message: &comms.SendMessageRequest_Sms{
			Sms: &comms.SMSMessage{
				SmsOption: &comms.SmsOption{
					Option: &comms.SmsOption_MobileNumberAddSmsOption{
						MobileNumberAddSmsOption: &comms.MobileNumberAddSmsOption{
							SmsType: comms.SmsType_MOBILE_NUMBER_ADD,
							Option: &comms.MobileNumberAddSmsOption_MobileNumberAddSmsOptionV1{
								MobileNumberAddSmsOptionV1: &comms.MobileNumberAddSmsOptionV1{
									TemplateVersion:          comms.TemplateVersion_VERSION_V1,
									CustomerIdLastFourDigits: customerID4Digits,
									RequestAcceptedTimestamp: timestamppb.New(createdAt.AsTime().In(datetime.IST)),
									NewMobileNumber:          phoneNum,
								},
							},
						},
					},
				},
			},
		},
	})
	if rpcErr := epifigrpc.RPCError(sendSmsRes, err); rpcErr != nil {
		logger.Error(ctx, "error while sending SMS", zap.Error(err))
		return nil, fmt.Errorf("error sending sms : %w", rpcErr)
	}
	logger.Info(ctx, "sent phone number update SMS to user", zap.String("messageId", sendSmsRes.GetMessageId()))
	return sendSmsRes, nil
}

func (fp *FederalProc) processInitiatedState(ctx context.Context, id string, actorId string) error {
	reqId, err := fp.changeToInProgress(ctx, actorId)
	if err != nil {
		return err
	}
	if err = fp.triggerCustomerCreation(ctx, actorId, reqId); err != nil {
		logger.Error(ctx, "error while initiating customer creation at vendor", zap.Error(err))
		return err
	}
	// At this point the user has been marked as IN PROGRESS, we have triggered
	// customer creation on vendor side. In case the vendor request does not go through, we can handle REQUEST NOT FOUND scenario under
	// IN PROGRESS handling
	if _, err = fp.customerCreationPublisher.PublishWithDelay(ctx, &bankcust.ProcessCustomerCreationRequest{
		BankCustId: id,
	}, fp.conf.FederalProcessorConfig().EnquiryDelay()); err != nil {
		logger.Error(ctx, "error while adding message to the queue", zap.Error(err))
		return err
	}
	return nil
}

func (fp *FederalProc) processInProgressState(ctx context.Context, bankCustomer *bankcust.BankCustomer) error {
	var (
		zapReqId   = zap.String(logger.REQUEST_ID, bankCustomer.GetVendorMetadata().GetFederalMetadata().GetCifRequestId())
		currStatus = bankCustomer.GetVendorMetadata().GetFederalMetadata().GetCustomerCreationState()
	)
	// Validate account state before processing.
	// check if the account is IN PROGRESS state or not.
	if currStatus != bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_IN_PROGRESS {
		logger.Error(ctx, "customer creation state is invalid for in progress handling", zap.String(logger.STATE, currStatus.String()))
		return epifierrors.ErrTransient
	}
	bankCustId, createdAt, err := fp.checkStatusFromFederal(ctx, bankCustomer)
	if err != nil {
		switch {
		case errors.Is(err, epifierrors.ErrRequestNotFoundAtVendor):
			logger.Info(ctx, "request not found at vendor, initiating new request", zapReqId)
			if err = fp.updateCustomerCreationState(ctx, bankCustomer.GetActorId(), bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_INITIATED); err != nil {
				logger.Error(ctx, "error while updating bank customer state to initiated", zap.Error(err))
				return err
			}
			return fp.processInitiatedState(ctx, bankCustomer.GetId(), bankCustomer.GetActorId())
		case errors.Is(err, epifierrors.ErrDuplicateEntry):
			logger.Info(ctx, "received duplicate entry for customer from vendor", zapReqId)
			return fp.handleCustomerCreationFailure(ctx, bankCustomer, bankcust.FailureType_FAILURE_TYPE_ACTIVE_CUSTOMER_ALREADY_EXISTS_WITH_CUSTOMER_ID)
		case errors.Is(err, epifierrors.ErrRetryable):
			logger.Info(ctx, "received duplicate entry for customer from vendor", zapReqId)
			return fp.handleCustomerCreationFailure(ctx, bankCustomer, bankcust.FailureType_FAILURE_TYPE_REQUEST_MANUALLY_FAILED_FOR_RETRY)
		case errors.Is(err, epifierrors.ErrInProgress):
			logger.Info(ctx, "customer creation still in progress", zapReqId)
			return err
		case errors.Is(err, epifierrors.ErrPermanent):
			return fp.handleCustomerCreationFailure(ctx, bankCustomer, bankcust.FailureType_FAILURE_TYPE_UNSPECIFIED)
		default:
			logger.Error(ctx, "unexpected error from federal status check API", zap.Error(err))
			return err
		}
	}
	logger.Info(ctx, "customer creation success through enquiry")
	return fp.handleCustomerCreationSuccess(ctx, bankCustomer.GetUserId(), bankCustomer.GetActorId(), bankCustId, createdAt, false)
}

func (fp *FederalProc) handleMaxRetry(ctx context.Context, actorId string, currStatus bankcust.CustomerCreationState, lastRetry bool) error {
	var (
		newState bankcust.CustomerCreationState
	)
	if !lastRetry {
		return nil
	}
	switch currStatus {
	case bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_INITIATED:
		newState = bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_MAX_RETRY_CREATION_STEP
	case bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_IN_PROGRESS:
		newState = bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_MAX_RETRY_STATUS_CHECK_STEP
	default:
		logger.Error(ctx, "unexpected customer creation status, max retry limit reached",
			zap.String(logger.STATE, currStatus.String()))
		return epifierrors.ErrPermanent
	}
	if err := fp.updateCustomerCreationState(ctx, actorId, newState); err != nil {
		logger.Error(ctx, "error while updating customer creation state", zap.Error(err))
		return err
	}
	logger.Info(ctx, "max retries have been exhausted")
	return epifierrors.ErrPermanent
}

func (fp *FederalProc) updateCustomerCreationState(ctx context.Context, actorId string, newState bankcust.CustomerCreationState) error {
	if err := fp.dao.UpdateByActorId(ctx, &bankcust.BankCustomer{
		ActorId: actorId,
		Vendor:  vendor,
		Status:  bankCustomerStatusToCustomerCreationStatusMap[newState],
		VendorMetadata: &bankcust.VendorMetadata{
			Vendor: &bankcust.VendorMetadata_FederalMetadata{
				FederalMetadata: &bankcust.FederalMetadata{
					CustomerCreationState: newState,
				},
			},
		},
	}, []bankcust.BankCustomerFieldMask{
		bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_STATUS,
		bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_CUSTOMER_CREATION_STATE,
	}); err != nil {
		logger.Error(ctx, "error while updating customer creation state", zap.Error(err))
		return err
	}
	return nil
}

func (fp *FederalProc) updateFailureReason(ctx context.Context, actorId string, failureReason bankcust.FailureType) error {
	if err := fp.dao.UpdateByActorId(ctx, &bankcust.BankCustomer{
		ActorId: actorId,
		Vendor:  vendor,
		FailureReason: &bankcust.FailureReason{
			FailureType: failureReason,
		},
	}, []bankcust.BankCustomerFieldMask{
		bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_BANK_CUSTOMER_FAILURE_TYPE,
	}); err != nil {
		logger.Error(ctx, "error while updating failure reason", zap.Error(err))
		return err
	}
	return nil
}

func (fp *FederalProc) updateCustomerCreationSourceAndResidency(ctx context.Context, bankCust *bankcust.BankCustomer, source bankcust.Source) error {
	isNRUserRes, err := fp.usersClient.IsNonResidentUser(ctx, &usersPb.IsNonResidentUserRequest{
		Identifier: &usersPb.IsNonResidentUserRequest_ActorId{
			ActorId: bankCust.GetActorId(),
		},
	})
	if rpcErr := epifigrpc.RPCError(isNRUserRes, err); rpcErr != nil {
		logger.Error(ctx, "error in IsNonResidentUser rpc", zap.Error(rpcErr))
		return rpcErr
	}
	residencyInfo, err := evalResidencyFromSource(source, isNRUserRes.GetIsNonResidentUser().ToBool(), isNRUserRes.GetResidentCountryCode())
	if err != nil {
		logger.Error(ctx, "error in evaluating residency info", zap.Error(err))
		return err
	}
	if err := fp.dao.UpdateByActorId(ctx, &bankcust.BankCustomer{
		ActorId:       bankCust.GetActorId(),
		Vendor:        vendor,
		Source:        source,
		ResidencyInfo: residencyInfo,
	}, []bankcust.BankCustomerFieldMask{
		bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_CUSTOMER_CREATION_SOURCE,
		bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_RESIDENCY_INFO,
	}); err != nil {
		logger.Error(ctx, "error while updating customer creation source", zap.Error(err))
		return err
	}
	return nil
}

// handleCustomerCreationStateForDedupe updates customer creation and sets vendorCustomerId from dedupe info after customer creation completion
func (fp *FederalProc) handleCustomerCreationStateForDedupe(ctx context.Context, actorId string, newState bankcust.CustomerCreationState, vendorCustomerId string) error {
	now := fp.timeClient.Now()
	if err := fp.dao.UpdateByActorId(ctx, &bankcust.BankCustomer{
		ActorId: actorId,
		Vendor:  vendor,
		Status:  bankCustomerStatusToCustomerCreationStatusMap[newState],
		VendorMetadata: &bankcust.VendorMetadata{
			Vendor: &bankcust.VendorMetadata_FederalMetadata{
				FederalMetadata: &bankcust.FederalMetadata{
					CustomerCreationState: newState,
				},
			},
		},
		CreationStartedAt:     timestamppb.New(now),
		FiCreationSucceededAt: timestamppb.New(now),
		VendorCustomerId:      vendorCustomerId,
	}, []bankcust.BankCustomerFieldMask{
		bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_STATUS,
		bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_CUSTOMER_CREATION_STATE,
		bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_VENDOR_CUSTOMER_ID,
		bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_CREATION_STARTED_AT,
		bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_FI_CREATION_SUCCEEDED_AT,
	}); err != nil {
		logger.Error(ctx, "error while updating customer creation state and vendor customer Id", zap.Error(err))
		return err
	}
	return nil
}

func (fp *FederalProc) triggerCustomerCreation(ctx context.Context, actorId string, reqId string) error {
	bankCustomer, err := fp.dao.GetByActorId(ctx, actorId, vendor)
	if err != nil {
		logger.Error(ctx, "error while fetching bank customer by actor id", zap.Error(err))
		return err
	}

	isNonResidentUserKycFlow, err := isNRI(ctx, bankCustomer)
	if err != nil {
		return err
	}

	switch {
	case isNonResidentUserKycFlow:
		return fp.triggerCustomerCreationForNonResident(ctx, actorId, reqId)
	case bankCustomer.GetSource() == bankcust.Source_SOURCE_PERSONAL_LOAN_ONBOARDING:
		return fp.triggerCustomerCreationForSource(ctx, actorId, reqId, bankCustomer.GetSource())
	default:
		return fp.triggerCustomerCreationForIndianResident(ctx, actorId, reqId)
	}
}

// triggerCustomerCreationForSource triggers customer creation for given source from generator.
func (fp *FederalProc) triggerCustomerCreationForSource(ctx context.Context, actorId string, reqId string, source bankcust.Source) error {
	customerCreationGenerator, err := NewCustomerCreationGenerator(source, fp)
	if err != nil {
		logger.Error(ctx, "failed to create customer creation generator", zap.Error(err))
		return err
	}
	kycRes, err := fp.kycClient.GetKYCRecord(ctx, &kyc.GetKYCRecordRequest{
		ActorId:   actorId,
		SignImage: true,
	})
	if err = epifigrpc.RPCError(kycRes, err); err != nil {
		logger.Error(ctx, "error while fetching signed kyc record", zap.Error(err))
		return err
	}

	// Update Ekyc RRN in bank customer
	if err = fp.updateKYCInfo(ctx, actorId, kycRes.GetKycType(), kycRes.GetKycAttemptId(), kycRes.GetKycRecord()); err != nil {
		return err
	}

	// Fetch the updated bank customer
	bankCustomer, err := fp.dao.GetByActorId(ctx, actorId, vendor)
	if err != nil {
		logger.Error(ctx, "error while fetching bank customer by actor id", zap.Error(err))
		return err
	}
	res, resErr := customerCreationGenerator.CreateCustomer(ctx, &CreateCustomerRequest{
		ActorId:      actorId,
		RequestId:    reqId,
		BankCustomer: bankCustomer,
	})
	_ = fp.logVendorStore(ctx, actorId, res.GetVendorStatus(), rpc.StatusOk(), vendorstore.API_CUSTOMER_CREATION_INIT, reqId)

	switch {
	case resErr != nil:
		logger.Error(ctx, "vendor create customer call failed", zap.Error(resErr))
		return resErr
	case res.GetStatus().IsSuccess():
	case isCustomerCreationInitPermFailure(res.GetStatus().GetCode()):
		logger.Info(ctx, "customer reached a terminal state and cannot proceed with customer creation")
		// handle any other codes if needed.
	}

	// WE DO NOT RETURN ERROR IN NON SUCCESS SCENARIOS AS WE WANT TO HANDLE IT DURING ENQUIRY.
	// This is done to avoid multiple init calls during retries.
	return nil
}

func (fp *FederalProc) triggerCustomerCreationForNonResident(ctx context.Context, actorId string, reqId string) error {
	vendorReq, err := fp.generateFederalCustomerCreationRequestForNonResident(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "failed to generate vendor request for customer creation non resident user", zap.Error(err))
		return err
	}
	// Set request ID in the vendor request
	vendorReq.RequestId = reqId
	res, err := fp.vgCustomerClient.CreateCustomerForNonResident(ctx, vendorReq)
	_ = fp.logVendorStore(ctx, actorId, res.GetVendorStatus(), rpc.StatusOk(), vendorstore.API_CUSTOMER_CREATION_INIT, reqId)

	switch {
	case err != nil:
		logger.Error(ctx, "vendor create customer call failed", zap.Error(err))
		return err
	case res.GetStatus().IsSuccess():
	case isCustomerCreationInitPermFailure(res.GetStatus().GetCode()):
		logger.Info(ctx, "customer reached a terminal state and cannot proceed with customer creation")
	default:
		logger.Error(ctx, "create customer rpc returned unexpected response", zap.String(logger.STATUS_CODE, res.GetStatus().String()))
		// todo: handle errors in enquiry so that consumer reinits the request
		return epifigrpc.RPCError(res, err)
	}
	// WE DO NOT RETURN ERROR IN NON SUCCESS SCENARIOS AS WE WANT TO HANDLE IT DURING ENQUIRY.
	// This is done to avoid multiple init calls during retries.
	return nil
}

func (fp *FederalProc) triggerCustomerCreationForIndianResident(ctx context.Context, actorId string, reqId string) error {
	vendorReq, err := fp.generateFederalCustomerCreationRequest(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "failed to generate vendor request for customer creation", zap.Error(err))
		fp.logInitiatedCustomerEvent(ctx, actorId, vendorReq.GetDeviceDetails().GetDeviceId(), Failure, err)
		return err
	}
	// Set request ID in the vendor request
	vendorReq.RequestId = reqId
	logger.Info(ctx, "occupation of actor before cif creation", zap.String("occ", vendorReq.GetOccupationType().String()), zap.String(logger.ACTOR_ID_V2, actorId))
	res, err := fp.vgCustomerClient.CreateCustomer(ctx, vendorReq)
	_ = fp.logVendorStore(ctx, actorId, res.GetVendorStatus(), rpc.StatusOk(), vendorstore.API_CUSTOMER_CREATION_INIT, reqId)

	switch {
	case err != nil:
		logger.Error(ctx, "vendor create customer call failed", zap.Error(err))
		fp.logInitiatedCustomerEvent(ctx, actorId, vendorReq.GetDeviceDetails().GetDeviceId(), Failure, err)
		return err
	case res.Status.IsSuccess():
		fp.logInitiatedCustomerEvent(ctx, actorId, vendorReq.GetDeviceDetails().GetDeviceId(), Success, nil)
	case isCustomerCreationInitPermFailure(res.GetStatus().GetCode()):
		logger.Info(ctx, "customer reached a terminal state and cannot proceed with customer creation")
	default:
		logger.Error(ctx, "create customer rpc returned unexpected response", zap.String(logger.STATUS_CODE, res.GetStatus().String()))
		fp.logInitiatedCustomerEvent(ctx, actorId, vendorReq.GetDeviceDetails().GetDeviceId(), Failure, nil)
	}
	// WE DO NOT RETURN ERROR IN NON SUCCESS SCENARIOS AS WE WANT TO HANDLE IT DURING ENQUIRY.
	// This is done to avoid multiple init calls during retries.
	return nil
}

func (fp *FederalProc) processCustomerCreationStateMachine(ctx context.Context, status bankcust.CustomerCreationState, bankCust *bankcust.BankCustomer) error {
	switch status {
	case bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_UNSPECIFIED, bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_INITIATED:
		return fp.processInitiatedState(ctx, bankCust.GetId(), bankCust.GetActorId())
	case bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_IN_PROGRESS:
		return fp.processInProgressState(ctx, bankCust)
	case bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_DEDUPE_CUSTOMER:
		vendorCustId := bankCust.GetDedupeInfo().GetVendorCustomerId()
		if vendorCustId == "" && bankCust.GetVendorCustomerId() != "" {
			vendorCustId = bankCust.GetVendorCustomerId()
		}
		if vendorCustId == "" {
			monitoring.KibanaErrorServiceMonitor(ctx, cfg.ONBOARDING_SERVICE, "empty vendor customer id")
			return epifierrors.ErrInvalidArgument
		}
		return fp.handleCustomerCreationSuccess(ctx, bankCust.GetUserId(), bankCust.GetActorId(), vendorCustId, bankCust.GetVendorCreationSucceededAt(), true)
	// It is possible that callback is received for customer creation and
	// the enquiry packet is retried in success state. We'll ignore this packet
	case bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_CREATED, bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_FAILED:
		logger.Info(ctx, "customer creation reached terminal state already", zap.String(logger.STATE, status.String()))
		return nil
	default:
		logger.Error(ctx, "unexpected customer creation state in state machine", zap.String(logger.STATE, status.String()))
		return epifierrors.ErrPermanent
	}
}

// changeToInProgress performs the following tasks:
// > Generates a new request ID for Federal Customer Creation
// > Changes Customer Creation State to IN_PROGRESS
// > Updates Request ID in Federal Metadata for the bank customer
// It returns the request ID and an error
func (fp *FederalProc) changeToInProgress(ctx context.Context, actorId string) (string, error) {
	var reqId string
	if txnErr := fp.txnExecutor.RunIdempotentTxn(ctx, transactionalMaxRetry, func(ctx context.Context) error {
		bankCustomer, err := fp.dao.GetByActorId(ctx, actorId, vendor)
		if err != nil {
			logger.Error(ctx, "error while fetching bank customer", zap.Error(err))
			return err
		}
		reqId = generateRequestID(bankCustomer.GetSource())
		currStatus := bankCustomer.GetVendorMetadata().GetFederalMetadata().GetCustomerCreationState()
		// Validate customer creation state before processing.
		// check if the account is in INITIATED state or not.
		if currStatus != bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_UNSPECIFIED &&
			currStatus != bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_INITIATED {
			logger.Error(ctx, "customer creation state is invalid for initiate handling", zap.String(logger.STATE, currStatus.String()))
			return epifierrors.ErrInvalidArgument
		}
		// If current status of customer is already IN PROGRESS, then return old request id
		if bankCustomer.GetVendorMetadata().GetFederalMetadata().GetCustomerCreationState() == bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_IN_PROGRESS {
			reqId = bankCustomer.GetVendorMetadata().GetFederalMetadata().GetCifRequestId()
			return nil
		}
		if err = fp.updateCustomerCreationState(ctx, actorId, bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_IN_PROGRESS); err != nil {
			logger.Error(ctx, "error while changing the state of customer creation to in progress", zap.Error(err))
			return err
		}
		creationStartedAt := timestamppb.New(fp.timeClient.Now())
		bankCustomer.GetVendorMetadata().GetFederalMetadata().CifRequestId = reqId
		if err = fp.dao.UpdateByActorId(ctx, &bankcust.BankCustomer{
			ActorId:           bankCustomer.GetActorId(),
			Vendor:            vendor,
			CreationStartedAt: creationStartedAt,
			VendorMetadata: &bankcust.VendorMetadata{
				Vendor: &bankcust.VendorMetadata_FederalMetadata{
					FederalMetadata: &bankcust.FederalMetadata{
						CifRequestId: reqId,
					},
				},
			},
		}, []bankcust.BankCustomerFieldMask{
			bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_CIF_REQUEST_ID,
			bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_CREATION_STARTED_AT,
		}); err != nil {
			logger.Error(ctx, "error while updating request id", zap.Error(err))
			return err
		}
		return nil
	}); txnErr != nil {
		logger.Error(ctx, "could not generate request Id for the given user")
		return "", nil
	}
	return reqId, nil
}

func generateRequestID(source bankcust.Source) string {
	// for loan cif creation, federal supports max 25 characters in request id
	if source == bankcust.Source_SOURCE_PERSONAL_LOAN_ONBOARDING {
		return idgen.FederalRandomDigitsSequenceWithDate(idgen.VendorRequestMethodName[idgen.BankCustomerCreation], maxLengthLoanCIF)
	}
	return idgen.FederalRandomDigitsSequence(idgen.VendorRequestMethodName[idgen.BankCustomerCreation], maxLengthDefault)
}

func (fp *FederalProc) getDeviceDetails(ctx context.Context, actorId string) (*vgHeaderPb.Auth, error) {
	deviceDetails, err := fp.authClient.GetDeviceAuth(ctx, &auth.GetDeviceAuthRequest{
		ActorId: actorId,
	})
	if err = epifigrpc.RPCError(deviceDetails, err); err != nil {
		logger.Error(ctx, "Failed to get device details of user ", zap.Error(err))
		return nil, err
	}
	if len(deviceDetails.GetDeviceToken()) == 0 {
		logger.Error(ctx, "empty device token, device registration not successful")
		return nil, epifierrors.ErrPermanent
	}
	return &vgHeaderPb.Auth{
		DeviceId:      deviceDetails.GetDevice().GetDeviceId(),
		DeviceToken:   deviceDetails.GetDeviceToken(),
		UserProfileId: deviceDetails.GetUserProfileId(),
	}, nil
}

func (fp *FederalProc) getUserProfile(ctx context.Context, userId string) (*usersPb.Profile, error) {
	userResp, err := fp.usersClient.GetUser(ctx, &usersPb.GetUserRequest{
		Identifier: &usersPb.GetUserRequest_Id{
			Id: userId,
		},
	})
	if err = epifigrpc.RPCError(userResp, err); err != nil {
		logger.Error(ctx, "error in fetching user", zap.Error(err))
		return nil, err
	}
	return userResp.GetUser().GetProfile(), nil
}

func (fp *FederalProc) generateFederalCustomerCreationRequest(ctx context.Context, actorId string) (*customer.CreateCustomerRequest, error) {
	kycRes, err := fp.kycClient.GetKYCRecord(ctx, &kyc.GetKYCRecordRequest{
		ActorId:   actorId,
		SignImage: true,
	})
	if err = epifigrpc.RPCError(kycRes, err); err != nil {
		logger.Error(ctx, "error while fetching signed kyc record", zap.Error(err))
		return nil, err
	}
	// Blocking customer creation if kyc type is not BKYC or EKYC
	// It's a defensive check to ensure that no user gets their CIF created through CKYC or other KYC types
	if kycRes.GetKycType() != kyc.KycType_BKYC && kycRes.GetKycType() != kyc.KycType_EKYC {
		return nil, fmt.Errorf("kyc type not allowed for customer creation")
	}
	if err = fp.updateKYCInfo(ctx, actorId, kycRes.GetKycType(), kycRes.GetKycAttemptId(), kycRes.GetKycRecord()); err != nil {
		return nil, err
	}
	bankCustomer, err := fp.dao.GetByActorId(ctx, actorId, vendor)
	if err != nil {
		logger.Error(ctx, "error while fetching bank customer by actor id", zap.Error(err))
		return nil, err
	}
	if err = allowCKYCUserWithOFlag(ctx, kycRes, bankCustomer); err != nil {
		logger.Error(ctx, "cannot allow O type user through customer creation without required parameters", zap.Error(err))
		return nil, err
	}
	identityProof, addressProof, err := fetchPOIandPOAFromKYCRecord(kycRes.GetKycRecord(), kycRes.GetKycType())
	if err != nil {
		logger.Error(ctx, "failed to fetch identification proof/ address proof", zap.Error(err))
		return nil, err
	}
	permanentAddress, communicationAddress, err := fp.methodAbstraction.GetAddresses(ctx, actorId, bankCustomer.GetUserId(), kycRes.GetKycType(), kycRes.GetKycRecord(), bankCustomer.GetSource())
	if err != nil {
		return nil, err
	}
	deviceDetails, err := fp.getDeviceDetails(ctx, actorId)
	if err != nil {
		return nil, err
	}
	empRes, err := fp.employmentClient.GetEmploymentInfo(ctx, &employment.GetEmploymentInfoRequest{
		ActorId:      actorId,
		ProcessNames: []employment.ProcessName{employment.ProcessName_PROCESS_NAME_EPFO_VERIFICATION},
	})
	if err = epifigrpc.RPCError(empRes, err); err != nil {
		if !empRes.GetStatus().IsRecordNotFound() {
			logger.Error(ctx, "failed to get employment info by actor id", zap.Error(err))
			return nil, err
		}
		logger.Info(ctx, "Employment data not found, default values will be sent")
	}
	profile, err := fp.getUserProfile(ctx, bankCustomer.GetUserId())
	if err != nil {
		return nil, err
	}
	return getFederalCustomerCreationRequest(ctx, bankCustomer, kycRes, profile, identityProof, addressProof, deviceDetails, empRes.GetEmploymentData().GetEmploymentType(), permanentAddress, communicationAddress, empRes.GetEmploymentData().GetOccupationType())
}

// nolint: funlen
func (fp *FederalProc) generateFederalCustomerCreationRequestForNonResident(ctx context.Context, actorId string) (*customer.CreateCustomerForNonResidentRequest, error) {
	bankCustomer, err := fp.dao.GetByActorId(ctx, actorId, vendor)
	if err != nil {
		logger.Error(ctx, "error while fetching bank customer by actor id", zap.Error(err))
		return nil, err
	}
	if bankCustomer.GetKycInfo().GetKycLevel() != kyc.KYCLevel_FULL_KYC {
		logger.Info(ctx, "Non resident user is not full KYC")
		return nil, epifierrors.ErrPermissionDenied
	}
	passportDataRes, err := fp.onbClient.FetchPassport(ctx, &onbPb.FetchPassportRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(passportDataRes, err); rpcErr != nil {
		logger.Error(ctx, "error in getting passport verification data", zap.Error(rpcErr))
		return nil, rpcErr
	}
	passportDetails := passportDataRes.GetPassport()
	signImage, err := fp.dataFetcher.GetSignImageBase64(ctx, &datafetcher.GetSignImageBase64Request{
		Source:  bankCustomer.GetSource(),
		ActorId: actorId,
	})
	if err != nil {
		logger.Error(ctx, "error in getting sign image", zap.Error(err))
		return nil, err
	}
	maritalStatus, err := fp.dataFetcher.GetMaritalStatus(ctx, &datafetcher.GetMaritalStatusRequest{
		Source:  bankCustomer.GetSource(),
		ActorId: actorId,
	})
	if err != nil {
		logger.Error(ctx, "error in getting marital status", zap.Error(err))
		return nil, err
	}

	empRes, err := fp.employmentClient.GetEmploymentInfo(ctx, &employment.GetEmploymentInfoRequest{
		ActorId:      actorId,
		ProcessNames: []employment.ProcessName{employment.ProcessName_PROCESS_NAME_WORK_EMAIL_VERIFICATION},
	})
	if rpcErr := epifigrpc.RPCError(empRes, err); rpcErr != nil {
		if !empRes.GetStatus().IsRecordNotFound() {
			logger.Error(ctx, "failed to get employment info by actor id", zap.Error(rpcErr))
			return nil, rpcErr
		}
		logger.Info(ctx, "Employment data not found, default values will be sent")
	}
	profile, err := fp.getUserProfile(ctx, bankCustomer.GetUserId())
	if err != nil {
		return nil, err
	}

	if !strings.EqualFold(passportDetails.GetAddress().GetRegionCode(), addressPkg.CountryIndia) {
		logger.Info(ctx, "Passport address is not India")
		return nil, fmt.Errorf("non indian country in passport addrees %w", epifierrors.ErrPermissionDenied)
	}
	permanentAddress, err := fp.getAddressesInVendorFormat(ctx, kyc.KycType_KYC_TYPE_UNSPECIFIED, passportDetails.GetAddress().GetBeAddress())
	if err != nil {
		return nil, fmt.Errorf("error in converting permanent address to vendor format %w", err)
	}

	currentAddress, err := addressPkg.GetVendorFormattedAddress(ctx, fp.usersClient, profile.GetAddresses()[types.AddressType_MAILING.String()], vendor)
	if err != nil {
		return nil, fmt.Errorf("error in converting current address to vendor format %w", err)
	}
	return &customer.CreateCustomerForNonResidentRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: vendor,
		},
		Name:                 passportDetails.GetName(),
		DateOfBirth:          types.NewFromBeDate(profile.GetDateOfBirth()),
		PermanentAddress:     permanentAddress,
		CurrentAddress:       currentAddress,
		Gender:               passportDetails.GetGender(),
		PhoneNumber:          profile.GetPhoneNumber(),
		PanNumber:            strings.ToUpper(profile.GetPAN()),
		Email:                profile.GetEmail(),
		FatherName:           profile.GetFatherName().ToString(),
		MotherName:           profile.GetMotherName().ToString(),
		EmploymentType:       empRes.GetEmploymentData().GetEmploymentType(),
		CustomerCreationFlow: 0, // todo if required
		// sender code to be decided based on customer creation flow
		OccupationType: empRes.GetEmploymentData().GetOccupationType(),
		SolId:          bankCustomer.GetVendorMetadata().GetFederalMetadata().GetSolId(),
		PassportData:   passportDetails,
		AnnualIncomeRange: &types.SalaryRange{
			MinValue: &types.Money{
				Units:        int64(profile.GetSalaryRange().GetMinValue()),
				CurrencyCode: "INR",
			},
			MaxValue: &types.Money{
				Units:        int64(profile.GetSalaryRange().GetMaxValue()),
				CurrencyCode: "INR",
			},
		},
		SignImage:     signImage,
		MaritalStatus: maritalStatus,
	}, nil
}

func (fp *FederalProc) GetAddresses(ctx context.Context, actorId, userId string, kycType kyc.KycType, kycRecord *kyc.KYCRecord, bankCustSource bankcust.Source) (*postaladdress.PostalAddress, *postaladdress.PostalAddress, error) {
	permanentAddress, err := fp.getAddressesInVendorFormat(ctx, kycType, kycRecord.GetPermanentAddress())
	if err != nil {
		logger.Error(ctx, "unable to get Permanent address in vendor specified format", zap.Error(err))
		return nil, nil, err
	}

	communicationAddress, err := fp.getCommAddrInVendorFormat(ctx, actorId, bankCustSource)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error in get vendor comm address", zap.Error(err))
		return nil, nil, err
	}
	if errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Info(ctx, "comm address not found, using perm address")
		communicationAddress = permanentAddress
	}

	// For personal loan onboarding, we are capturing communication address and storing in user profile.
	// NOTE: For other sources, we are returning same communication address as permanent address due to compliance with Federal.
	// So, we are returning both permanent and communication address before making it same in next step.
	if bankCustSource == bankcust.Source_SOURCE_PERSONAL_LOAN_ONBOARDING {
		return permanentAddress, communicationAddress, nil
	}

	// Forcing the communication address to be the same as the permanent address due to new compliance
	// https://monorail.pointz.in/p/fi-app/issues/detail?id=89604
	// This is a hotfix to CP. TODO(aditya): Cleanup the code and merge to master
	if !proto.Equal(permanentAddress, communicationAddress) {
		logger.Info(ctx, "comm and perm address are different, forcing them to be same")
		communicationAddress = permanentAddress
	} else {
		// For debugging; will be removed as part of the cleanup
		logger.Info(ctx, "comm and perm address are same")
	}

	return permanentAddress, communicationAddress, nil
}

func (fp *FederalProc) logVendorStore(ctx context.Context, actorId string, vendorStatus *commonvgpb.VendorStatus, status *rpc.Status, api vendorstore.Api, requestId string) error {
	if err := fp.vendorStore.Insert(ctx, actorId, vendor, vendorStatus,
		status, api, requestId); err != nil {
		logger.Error(ctx, "error while sending vendor code to vendorstore", zap.Error(err))
		return err
	}
	return nil
}

func (fp *FederalProc) getAddressesInVendorFormat(ctx context.Context, kycType kyc.KycType, address *postaladdress.PostalAddress) (*postaladdress.PostalAddress, error) {
	var err error
	if address == nil {
		return nil, epifierrors.ErrRecordNotFound
	}
	// convert KYC addresses which are in Govt encoded format into non-encoded/readable addresses
	if address, err = userSvc.GetFormattedKycAddress(ctx, kycType, address, types.AddressFormat_USER_READABLE, fp.ckycAddressCodesDao); err != nil {
		logger.ErrorNoCtx("error transforming KYC address into readable address", zap.Error(err))
		return nil, err
	}

	// convert address properties like city, state, country into codes understood by the vendor
	formattedAddress, err := addressPkg.GetVendorFormattedAddress(ctx, fp.usersClient, address, vendor)
	if err != nil {
		logger.Error(ctx, "failed to convert address into address understood by vendor", zap.Error(err))
		return nil, err
	}
	return formattedAddress, nil
}

func (fp *FederalProc) checkStatusFromFederal(ctx context.Context, bankCustomer *bankcust.BankCustomer) (string, *timestamppb.Timestamp, error) {
	isNri, err := isNRI(ctx, bankCustomer)
	if err != nil {
		return "", nil, err
	}
	if isNri {
		return fp.checkStatusFromFederalForNonResident(ctx, bankCustomer)
	}

	switch {
	case isNri:
		return fp.checkStatusFromFederalForNonResident(ctx, bankCustomer)
	case bankCustomer.GetSource() == bankcust.Source_SOURCE_PERSONAL_LOAN_ONBOARDING:
		return fp.checkCustomerCreationStatusFromFederalForSource(ctx, bankCustomer.GetSource(), bankCustomer)
	default:
		return fp.checkStatusFromFederalForIndianResident(ctx, bankCustomer)
	}
}

// nolint: dupl
// checkCustomerCreationStatusFromFederalForSource checks the status of customer creation for the given source from Federal.
func (fp *FederalProc) checkCustomerCreationStatusFromFederalForSource(ctx context.Context, source bankcust.Source, bankCustomer *bankcust.BankCustomer) (string, *timestamppb.Timestamp, error) {
	reqId := bankCustomer.GetVendorMetadata().GetFederalMetadata().GetCifRequestId()
	customerCreationGenerator, generatorErr := NewCustomerCreationGenerator(source, fp)
	if generatorErr != nil {
		logger.Error(ctx, "failed to create customer creation generator", zap.Error(generatorErr))
		return "", nil, generatorErr
	}

	res, err := customerCreationGenerator.CheckCustomerCreationStatus(ctx, &CheckCustomerCreationStatusRequest{
		ActorId:      bankCustomer.GetActorId(),
		RequestId:    reqId,
		BankCustomer: bankCustomer,
	})
	_ = fp.logVendorStore(ctx, bankCustomer.GetActorId(), res.GetVendorStatus(), res.GetStatus(), vendorstore.API_CUSTOMER_CREATION_ENQUIRY, reqId)
	switch {
	case err != nil:
		logger.Error(ctx, "error while polling customer creation status", zap.String(logger.USER_ID, bankCustomer.GetUserId()))
		fp.logCompletedCustomerEvent(ctx, bankCustomer.GetActorId(), reqId, bankCustomer.GetDedupeInfo().GetKycLevel(), nil, bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_UNSPECIFIED, Failure, err.Error())
		return "", nil, err
	case res.GetStatus().GetCode() == uint32(customer.CreateCustomerResponse_CUSTOMER_CREATION_INPROGRESS) ||
		res.GetStatus().GetCode() == uint32(customer.CheckCustomerStatusResponse_VENDOR_INTERMITTENT_FLAKINESS):
		return "", nil, epifierrors.ErrInProgress
	case res.GetStatus().GetCode() == uint32(customer.CheckCustomerStatusResponse_DETAILS_NOT_FOUND):
		return "", nil, epifierrors.ErrRequestNotFoundAtVendor
	case isCustomerCreationEnqPermFailure(res.GetStatus().GetCode()):
		logger.Info(ctx, "customer creation failed with permanent failure")
		fp.logCompletedCustomerEvent(ctx, bankCustomer.GetActorId(), reqId, bankCustomer.GetDedupeInfo().GetKycLevel(), nil, bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_FAILED, Failure, "")
		return "", nil, epifierrors.ErrPermanent
	case res.GetStatus().IsAlreadyExists():
		logger.Info(ctx, "customer creation failed as id proof already exists")
		fp.logCompletedCustomerEvent(ctx, bankCustomer.GetActorId(), reqId, bankCustomer.GetDedupeInfo().GetKycLevel(), nil, bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_FAILED, Failure, "")
		return "", nil, epifierrors.ErrDuplicateEntry
	case res.GetStatus().GetCode() == uint32(customer.CheckCustomerStatusResponse_EXCEPTION):
		logger.Info(ctx, "customer creation failed as exception occured at vendor")
		fp.logCompletedCustomerEvent(ctx, bankCustomer.GetActorId(), reqId, bankCustomer.GetDedupeInfo().GetKycLevel(), nil, bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_FAILED, Failure, "")
		return "", nil, epifierrors.ErrRetryable
	case !res.GetStatus().IsSuccess():
		fp.logCompletedCustomerEvent(ctx, bankCustomer.GetActorId(), reqId, bankCustomer.GetDedupeInfo().GetKycLevel(), nil, bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_UNSPECIFIED, Failure, ErrorEmptyCustomerId)
		logger.Debug(ctx, "customer creation call returned non-success code",
			zap.String(logger.REQUEST_ID, reqId), zap.Uint32("rpcCode", res.GetStatus().GetCode()),
			zap.String(logger.RESPONSE_CODE, res.GetVendorStatus().GetCode()))
		return "", nil, epifierrors.ErrTransient
	}
	fp.logCompletedCustomerEvent(ctx, bankCustomer.GetActorId(), reqId, bankCustomer.GetDedupeInfo().GetKycLevel(), res.GetCifCreatedTime(), bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_CREATED, Success, "")
	return res.GetBankCustomerId(), res.GetCifCreatedTime(), nil
}

// nolint: dupl
func (fp *FederalProc) checkStatusFromFederalForNonResident(ctx context.Context, bankCustomer *bankcust.BankCustomer) (string, *timestamppb.Timestamp, error) {
	reqId := bankCustomer.GetVendorMetadata().GetFederalMetadata().GetCifRequestId()
	res, err := fp.vgCustomerClient.CheckCustomerStatusForNonResident(ctx, &customer.CheckCustomerStatusForNonResidentRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: vendor,
		},
		OriginalRequestId: reqId,
	})
	// we don't get customer creation timestamp from bank, so considering timestamp of enquiry success as proxy for custom creation timestamp
	customerCreatedAt := timestamppb.Now()
	_ = fp.logVendorStore(ctx, bankCustomer.GetActorId(), res.GetVendorStatus(), res.GetStatus(), vendorstore.API_CUSTOMER_CREATION_ENQUIRY, reqId)
	switch {
	case err != nil:
		logger.Error(ctx, "error while polling customer creation status", zap.String(logger.USER_ID, bankCustomer.GetUserId()))
		fp.logCompletedCustomerEvent(ctx, bankCustomer.GetActorId(), reqId, bankCustomer.GetDedupeInfo().GetKycLevel(), nil, bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_UNSPECIFIED, Failure, err.Error())
		return "", nil, err
	case res.GetStatus().GetCode() == uint32(customer.CreateCustomerResponse_CUSTOMER_CREATION_INPROGRESS) ||
		res.GetStatus().GetCode() == uint32(customer.CheckCustomerStatusResponse_VENDOR_INTERMITTENT_FLAKINESS):
		// TODO: Update to CheckCustomerStatusResponse_CUSTOMER_CREATION_INPROGRESS
		return "", nil, epifierrors.ErrInProgress
	case res.GetStatus().GetCode() == uint32(customer.CheckCustomerStatusResponse_DETAILS_NOT_FOUND):
		return "", nil, epifierrors.ErrRequestNotFoundAtVendor
	case isCustomerCreationEnqPermFailure(res.GetStatus().GetCode()):
		logger.Info(ctx, "customer creation failed with permanent failure")
		fp.logCompletedCustomerEvent(ctx, bankCustomer.GetActorId(), reqId, bankCustomer.GetDedupeInfo().GetKycLevel(), nil, bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_FAILED, Failure, "")
		return "", nil, epifierrors.ErrPermanent
	case res.GetStatus().IsAlreadyExists():
		logger.Info(ctx, "customer creation failed as id proof already exists")
		fp.logCompletedCustomerEvent(ctx, bankCustomer.GetActorId(), reqId, bankCustomer.GetDedupeInfo().GetKycLevel(), nil, bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_FAILED, Failure, "")
		return "", nil, epifierrors.ErrDuplicateEntry
	case res.GetStatus().GetCode() == uint32(customer.CheckCustomerStatusResponse_EXCEPTION):
		logger.Info(ctx, "customer creation failed as exception occured at vendor")
		fp.logCompletedCustomerEvent(ctx, bankCustomer.GetActorId(), reqId, bankCustomer.GetDedupeInfo().GetKycLevel(), nil, bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_FAILED, Failure, "")
		return "", nil, epifierrors.ErrRetryable
	case !res.GetStatus().IsSuccess():
		fp.logCompletedCustomerEvent(ctx, bankCustomer.GetActorId(), reqId, bankCustomer.GetDedupeInfo().GetKycLevel(), nil, bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_UNSPECIFIED, Failure, ErrorEmptyCustomerId)
		logger.Debug(ctx, "customer creation call returned non-success code",
			zap.String(logger.REQUEST_ID, reqId), zap.Uint32("rpcCode", res.GetStatus().GetCode()),
			zap.String(logger.RESPONSE_CODE, res.GetVendorStatus().GetCode()))
		return "", nil, epifierrors.ErrTransient
	}
	fp.logCompletedCustomerEvent(ctx, bankCustomer.GetActorId(), reqId, bankCustomer.GetDedupeInfo().GetKycLevel(), customerCreatedAt, bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_CREATED, Success, "")
	return res.GetBankCustomerId(), customerCreatedAt, nil
}

func (fp *FederalProc) checkStatusFromFederalForIndianResident(ctx context.Context, bankCustomer *bankcust.BankCustomer) (string, *timestamppb.Timestamp, error) {
	reqId := bankCustomer.GetVendorMetadata().GetFederalMetadata().GetCifRequestId()
	deviceDetails, err := fp.getDeviceDetails(ctx, bankCustomer.GetActorId())
	if err != nil {
		return "", nil, err
	}
	profile, err := fp.getUserProfile(ctx, bankCustomer.GetUserId())
	if err != nil {
		return "", nil, err
	}
	res, err := fp.vgCustomerClient.CheckCustomerStatus(ctx, &customer.CheckCustomerStatusRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: vendor,
		},
		OriginalRequestId: reqId,
		DeviceDetails:     deviceDetails,
		MobileNumber:      profile.GetPhoneNumber().ToString(),
	})
	_ = fp.logVendorStore(ctx, bankCustomer.GetActorId(), res.GetVendorStatus(), res.GetStatus(), vendorstore.API_CUSTOMER_CREATION_ENQUIRY, reqId)
	switch {
	case err != nil:
		logger.Error(ctx, "error while polling customer creation status", zap.String(logger.USER_ID, bankCustomer.GetUserId()))
		fp.logCompletedCustomerEvent(ctx, bankCustomer.GetActorId(), reqId, bankCustomer.GetDedupeInfo().GetKycLevel(), nil, bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_UNSPECIFIED, Failure, err.Error())
		return "", nil, err
	case res.GetStatus().GetCode() == uint32(customer.CreateCustomerResponse_CUSTOMER_CREATION_INPROGRESS) ||
		res.GetStatus().GetCode() == uint32(customer.CheckCustomerStatusResponse_VENDOR_INTERMITTENT_FLAKINESS):
		// TODO: Update to CheckCustomerStatusResponse_CUSTOMER_CREATION_INPROGRESS
		return "", nil, epifierrors.ErrInProgress
	case res.GetStatus().GetCode() == uint32(customer.CheckCustomerStatusResponse_DETAILS_NOT_FOUND):
		return "", nil, epifierrors.ErrRequestNotFoundAtVendor
	case isCustomerCreationEnqPermFailure(res.GetStatus().GetCode()):
		logger.Info(ctx, "customer creation failed with permanent failure")
		fp.logCompletedCustomerEvent(ctx, bankCustomer.GetActorId(), reqId, bankCustomer.GetDedupeInfo().GetKycLevel(), nil, bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_FAILED, Failure, "")
		return "", nil, epifierrors.ErrPermanent
	case res.GetStatus().IsAlreadyExists():
		logger.Info(ctx, "customer creation failed as id proof already exists")
		fp.logCompletedCustomerEvent(ctx, bankCustomer.GetActorId(), reqId, bankCustomer.GetDedupeInfo().GetKycLevel(), nil, bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_FAILED, Failure, "")
		return "", nil, epifierrors.ErrDuplicateEntry
	case res.GetStatus().GetCode() == uint32(customer.CheckCustomerStatusResponse_EXCEPTION):
		logger.Info(ctx, "customer creation failed as exception occured at vendor")
		fp.logCompletedCustomerEvent(ctx, bankCustomer.GetActorId(), reqId, bankCustomer.GetDedupeInfo().GetKycLevel(), nil, bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_FAILED, Failure, "")
		return "", nil, epifierrors.ErrRetryable
	case !res.GetStatus().IsSuccess():
		fp.logCompletedCustomerEvent(ctx, bankCustomer.GetActorId(), reqId, bankCustomer.GetDedupeInfo().GetKycLevel(), nil, bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_UNSPECIFIED, Failure, ErrorEmptyCustomerId)
		logger.Debug(ctx, "customer creation call returned non-success code",
			zap.String(logger.REQUEST_ID, reqId), zap.Uint32("rpcCode", res.GetStatus().GetCode()),
			zap.String(logger.RESPONSE_CODE, res.GetVendorStatus().GetCode()))
		return "", nil, epifierrors.ErrTransient
	}
	fp.logCompletedCustomerEvent(ctx, bankCustomer.GetActorId(), reqId, bankCustomer.GetDedupeInfo().GetKycLevel(), res.GetCreatedAt(), bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_CREATED, Success, "")
	return res.GetBankCustomerId(), res.GetCreatedAt(), nil
}

func isEligibleForCustomerCreation(bankCust *bankcust.BankCustomer) bool {
	return isCustomerCreationNotStarted(bankCust.GetVendorMetadata().GetFederalMetadata().GetCustomerCreationState()) ||
		isCustomerCreationRetryableFailure(bankCust)
}

func isCustomerCreationNotStarted(status bankcust.CustomerCreationState) bool {
	return status == bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_UNSPECIFIED ||
		status == bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_DEDUPE_CUSTOMER
}

func isCustomerCreationNonRetryableFailure(bankCust *bankcust.BankCustomer) bool {
	return (bankCust.GetVendorMetadata().GetFederalMetadata().GetCustomerCreationState() == bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_FAILED ||
		bankCust.GetVendorMetadata().GetFederalMetadata().GetCustomerCreationState() == bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_MAX_RETRY_CREATION_STEP ||
		bankCust.GetVendorMetadata().GetFederalMetadata().GetCustomerCreationState() == bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_MAX_RETRY_STATUS_CHECK_STEP) &&
		!isCustomerCreationRetryableFailure(bankCust)
}

func isCustomerCreationRetryableFailure(bankCust *bankcust.BankCustomer) bool {
	return (bankCust.GetVendorMetadata().GetFederalMetadata().GetCustomerCreationState() == bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_FAILED) && lo.Contains(retryableFailures, bankCust.GetFailureReason().GetFailureType())
}

func isCustomerCreationInProgress(state bankcust.CustomerCreationState) bool {
	return state == bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_IN_PROGRESS ||
		state == bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_INITIATED
}

func isDedupeCustomer(status bankcust.CustomerCreationState) bool {
	return status == bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_DEDUPE_CUSTOMER
}

func isCustomerCreationSuccess(status bankcust.CustomerCreationState) bool {
	return status == bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_CREATED
}

func isCustomerCreationTerminalState(status bankcust.CustomerCreationState) bool {
	return status == bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_FAILED || status == bankcust.CustomerCreationState_CUSTOMER_CREATION_STATE_CREATED
}

func isCustomerCreationInitPermFailure(code uint32) bool {
	return code == uint32(customer.CreateCustomerResponse_INVALID_INPUT)
}

func isCustomerCreationEnqPermFailure(code uint32) bool {
	return code == uint32(customer.CheckCustomerStatusResponse_INVALID_DEVICE_TOKEN) ||
		code == uint32(customer.CheckCustomerStatusResponse_KYC_FAILED) ||
		code == uint32(customer.CheckCustomerStatusResponse_SERVICE_NOT_AVAILABLE_FOR_SENDER_CODE) ||
		code == uint32(customer.CheckCustomerStatusResponse_RESPONSE_ACTION_FAILURE)
}

func allowCKYCUserWithOFlag(_ context.Context, kycResp *kyc.GetKYCRecordResponse, bankCustomer *bankcust.BankCustomer) error {
	for _, failureReason := range kycResp.GetCkycAttempt().GetFailureReason().GetFailures() {
		switch failureReason.GetType() {
		case kyc.FailureType_CKYC_O_FLAG:
			// cannot initiate customer creation if user ckyc failed due to CKYC O failure type reason and the user is not FULL KYC
			if bankCustomer.GetKycInfo().GetKycLevel() != kyc.KYCLevel_FULL_KYC {
				return fmt.Errorf("lso user without full kyc")
			}
			// cannot create customer for an LSO user with CustomerCreationUidReferenceKey empty
			// ideally this would happen only if no entry found in vkyc which is not expected if an LSO user has reached customer creation stage without doing VKYC
			if bankCustomer.GetVendorMetadata().GetFederalMetadata().GetEkycRrnNo() == "" {
				return fmt.Errorf("no customer creation UidReferenceKey found")
			}
			return nil
		default:
			continue
		}
	}
	// User is not a O type user
	return nil
}

// fetchPOIandPOAFromKYCRecord returns proof of address and identity from the kycRecord of the user.
func fetchPOIandPOAFromKYCRecord(kycRecord *kyc.KYCRecord, kycType kyc.KycType) (*customer.ProofDetails, *customer.ProofDetails, error) {
	// List of allowed id proofs
	acceptedPOIs := map[kyc.IdProofType]bool{
		kyc.IdProofType_PASSPORT:        true,
		kyc.IdProofType_VOTER_ID:        true,
		kyc.IdProofType_DRIVING_LICENSE: true,
		kyc.IdProofType_NREGA_JOB_CARD:  true,
		kyc.IdProofType_CKYC_RECORD:     true,
	}

	if kycType == kyc.KycType_EKYC || kycType == kyc.KycType_BKYC {
		acceptedPOIs[kyc.IdProofType_UID] = true
	}

	var poa, poi *kyc.IdProof
	var acceptedIdProofList []*kyc.IdProof
	for _, proof := range kycRecord.IdentityProofs {
		if _, ok := acceptedPOIs[proof.GetType()]; ok {
			acceptedIdProofList = append(acceptedIdProofList, proof)
		}
	}

	switch len(acceptedIdProofList) {
	case 0:
		return nil, nil, fmt.Errorf("no non PAN proofs for user in KYC")
	case 1:
		poi = acceptedIdProofList[0]
		poa = acceptedIdProofList[0]
	default:
		poi = acceptedIdProofList[0]
		poa = acceptedIdProofList[1]
	}

	// Default values for issue date and expiry date for identity proofs are given by federal as follows
	dateToday := time.Now()
	issueDateDefault := &date.Date{Year: int32(dateToday.Year()), Month: int32(dateToday.Month()), Day: int32(dateToday.Day())}
	expiryDateDefault := &date.Date{Year: 2099, Month: 12, Day: 1}

	identityProof := &customer.ProofDetails{
		Type:         poi.GetType(),
		IdNumber:     poi.GetIdValue(),
		IdIssueDate:  issueDateDefault,
		IdExpiryDate: poi.GetExpiry(),
	}
	if poi.Expiry == nil {
		identityProof.IdExpiryDate = expiryDateDefault
	}

	addressProof := &customer.ProofDetails{
		Type:         poa.GetType(),
		IdNumber:     poa.GetIdValue(),
		IdIssueDate:  issueDateDefault,
		IdExpiryDate: poa.GetExpiry(),
	}
	if poa.Expiry == nil {
		addressProof.IdExpiryDate = expiryDateDefault
	}
	return identityProof, addressProof, nil
}

func getUserSalary(salaryRange *usersPb.SalaryRange) float32 {
	if salaryRange == nil {
		return float32(0)
	}
	avgSalary := (float32(salaryRange.GetMinValue()) + float32(salaryRange.GetMaxValue())) / 2
	return avgSalary
}

// getCustomerCreationFlow return customer creation flow inferred as -
// MIN EKYC if user's kyc level is min kyc
// CKYC + EKYC if user's current attempt is ekyc but kyc level is full kyc
// CKYC if user's current kyc attempt is CKYC with full kyc
// Leaving this not behind a flag, as Student VKYC will go live before User-BankCust Split 🥲
// NOTE: Hierarchy is important here.
func getCustomerCreationFlow(ctx context.Context, bankCustomer *bankcust.BankCustomer, kycResp *kyc.GetKYCRecordResponse) (customer.CustomerCreationFlow, error) {
	switch {
	case kycResp.GetKycType() == kyc.KycType_BKYC && bankCustomer.GetKycInfo().GetKycLevel() == kyc.KYCLevel_FULL_KYC:
		logger.Info(ctx, "customer creation flow via BKYC")
		return customer.CustomerCreationFlow_CUSTOMER_CREATION_FLOW_BKYC, nil
	case bankCustomer.GetKycLevelUpdateFlow() == bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_BKYC_PRE_ONBOARDING && bankCustomer.GetKycInfo().GetKycLevel() == kyc.KYCLevel_FULL_KYC:
		logger.Info(ctx, "customer creation flow via BKYC")
		return customer.CustomerCreationFlow_CUSTOMER_CREATION_FLOW_BKYC, nil
	case bankCustomer.GetKycLevelUpdateFlow() == bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_LSO:
		logger.Info(ctx, "customer creation flow via VKYC for LSO users")
		return customer.CustomerCreationFlow_CUSTOMER_CREATION_FLOW_VKYC_ONBOARDING_LSO, nil
	case bankCustomer.GetKycLevelUpdateFlow() == bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_O:
		logger.Info(ctx, "customer creation flow via VKYC for O Type users")
		return customer.CustomerCreationFlow_CUSTOMER_CREATION_FLOW_VKYC_ONBOARDING_LSO, nil
	case kycResp.GetKycType() == kyc.KycType_EKYC && bankCustomer.GetKycInfo().GetKycLevel() == kyc.KYCLevel_MIN_KYC:
		logger.Info(ctx, "customer creation flow via EKYC")
		return customer.CustomerCreationFlow_CUSTOMER_CREATION_FLOW_MIN_EKYC, nil
	case bankCustomer.GetKycLevelUpdateFlow() == bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_EKYC_NUMBER_MISMATCH:
		logger.Info(ctx, "customer creation flow via VKYC before CIF")
		return customer.CustomerCreationFlow_CUSTOMER_CREATION_FLOW_VKYC, nil
	case bankCustomer.GetKycLevelUpdateFlow() == bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_LOW_QUALITY_USERS:
		logger.Info(ctx, "customer creation flow via VKYC before CIF")
		return customer.CustomerCreationFlow_CUSTOMER_CREATION_FLOW_VKYC, nil
	case bankCustomer.GetKycLevelUpdateFlow() == bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_DEDUPE_PARTIAL_KYC_LATEST_NO_DEDUPE:
		logger.Info(ctx, "customer creation flow via VKYC before CIF, partial kyc in dedupe and latest no dedupe")
		return customer.CustomerCreationFlow_CUSTOMER_CREATION_FLOW_VKYC, nil
	case bankCustomer.GetKycLevelUpdateFlow() == bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_FI_LITE_USERS:
		logger.Info(ctx, "customer creation flow via VKYC before CIF")
		return customer.CustomerCreationFlow_CUSTOMER_CREATION_FLOW_VKYC, nil
	case bankCustomer.GetKycLevelUpdateFlow() == bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_DEDUPE_PARTIAL_KYC:
		logger.Info(ctx, "customer creation flow via Partial Dedupe Customer")
		return customer.CustomerCreationFlow_CUSTOMER_CREATION_FLOW_DEDUPE_MIN_KYC, nil
	case bankCustomer.GetKycLevelUpdateFlow() == bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_STUDENT:
		logger.Info(ctx, "customer creation flow via VKYC before CIF")
		return customer.CustomerCreationFlow_CUSTOMER_CREATION_FLOW_VKYC, nil
	case bankCustomer.GetKycLevelUpdateFlow() == bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_CLOSED_ACCOUNT_REOPENING:
		logger.Info(ctx, "customer creation flow via VKYC before CIF")
		return customer.CustomerCreationFlow_CUSTOMER_CREATION_FLOW_VKYC, nil
	// todo: remove if this is not required as NR onboarding has a new CIF creation flow
	case bankCustomer.GetKycLevelUpdateFlow() == bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_NON_RESIDENT_ONBOARDING:
		logger.Info(ctx, "customer creation flow via VKYC before CIF for NR onboarding users")
		return customer.CustomerCreationFlow_CUSTOMER_CREATION_FLOW_VKYC, nil
	default:
		logger.Error(ctx, "reached unexpected customer creation flow", zap.String(logger.KYC_TYPE, kycResp.GetKycType().String()),
			zap.String("kyc_level", bankCustomer.GetKycInfo().GetKycLevel().String()), zap.String("kyc_level_update_flow", bankCustomer.GetKycLevelUpdateFlow().String()))
		return customer.CustomerCreationFlow_CUSTOMER_CREATION_FLOW_UNSPECIFIED, epifierrors.ErrInvalidArgument
	}
}

func getFederalCustomerCreationRequest(ctx context.Context, bankCust *bankcust.BankCustomer, kycRes *kyc.GetKYCRecordResponse, userProfile *usersPb.Profile,
	idProof, addProof *customer.ProofDetails, deviceDetails *vgHeaderPb.Auth, empType employment.EmploymentType, permAdd, commAdd *postaladdress.PostalAddress,
	occupationType employment.OccupationType) (*customer.CreateCustomerRequest, error) {
	var (
		signImage, ekycRrn string
	)
	if kycRes.GetKycType() == kyc.KycType_CKYC {
		signImage = kycRes.GetKycRecord().GetSignImage()
	}
	if kycRes.GetKycType() == kyc.KycType_EKYC {
		ekycRrn = bankCust.GetVendorMetadata().GetFederalMetadata().GetEkycRrnNo()
	}
	if kycRes.GetKycType() == kyc.KycType_BKYC {
		signImage = kycRes.GetKycRecord().GetSignImage()
		ekycRrn = bankCust.GetVendorMetadata().GetFederalMetadata().GetEkycRrnNo()
	}
	customerFlow, err := getCustomerCreationFlow(ctx, bankCust, kycRes)
	if err != nil {
		return nil, err
	}
	return &customer.CreateCustomerRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: vendor,
		},
		Name:             kycRes.GetKycRecord().GetName(),
		DateOfBirth:      userProfile.GetDateOfBirth(),
		PermanentAddress: permAdd,
		CurrentAddress:   commAdd,
		Gender:           kycRes.GetKycRecord().GetGender(),
		PhoneNumber:      userProfile.GetPhoneNumber(),
		PanNumber:        strings.ToUpper(userProfile.GetPAN()),
		Email:            userProfile.GetEmail(),
		FatherName:       userProfile.GetFatherName().ToString(),
		MotherName:       userProfile.GetMotherName().ToString(),
		IdentityProof:    idProof,
		AddressProof:     addProof,
		DeviceDetails:    deviceDetails,
		SignImage:        signImage,
		UidNo:            ekycRrn,
		AnnualIncome:     getUserSalary(userProfile.GetSalaryRange()),
		Type:             empType,
		// sender code to be decided based on customer creation flow
		CustomerCreationFlow: customerFlow,
		OccupationType:       occupationType,
		SolId:                bankCust.GetVendorMetadata().GetFederalMetadata().GetSolId(),
		Qualification:        userProfile.GetQualification(),
	}, nil
}

func (fp *FederalProc) getEkycRrn(ctx context.Context, actorId string, kycRecord *kyc.KYCRecord) (string, error) {
	ekycRrnDefault := kycRecord.GetUidReferenceKey()
	vkycRecord, err := fp.getVKYCSummary(ctx, actorId)
	if err != nil {
		return "", err
	}
	// TODO (Shivam): If we have a case later where we start sending VKYC in progress users through Customer Creation, we might need to handle VKYC in progress here.
	status := vkycRecord.GetVkycSummary().GetStatus()
	if status != vkyc.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_APPROVED {
		return ekycRrnDefault, nil
	}
	var vkycCustomerInfoId string
	for _, vkycAttempt := range vkycRecord.GetVkycAttemptDataList() {
		if vkycAttempt.GetVkycAttempt().GetStatus() == vkyc.VKYCAttemptStatus_VKYC_ATTEMPT_STATUS_APPROVED {
			vkycCustomerInfoId = vkycAttempt.GetVkycAttempt().GetCustomerInfoId()
			break
		}
	}
	for _, vkycCustInfo := range vkycRecord.GetVkycKarzaCustomerInfos() {
		if vkycCustInfo.GetId() == vkycCustomerInfoId {
			return vkycPkg.GetCustomerCreationEkycRrn(vkycCustInfo.GetTransactionMetadata().GetEkycRrnNo()), nil
		}
	}
	monitoring.KibanaInfoServiceMonitor(ctx, cfg.ONBOARDING_SERVICE, "reached unexpected state vkyc approved ekyc rrn")
	return ekycRrnDefault, nil
}

func (fp *FederalProc) getVKYCSummary(ctx context.Context, actorId string) (*vkyc.VKYCRecord, error) {
	vkycResp, errResp := fp.vkycClient.GetVKYCSummary(ctx, &vkyc.GetVKYCSummaryRequest{
		ActorId: actorId,
	})
	if err := epifigrpc.RPCError(vkycResp, errResp); err != nil && !rpc.StatusFromError(err).IsRecordNotFound() {
		logger.Error(ctx, "error in fetching vkyc summary", zap.Error(err))
		return nil, err
	}
	return vkycResp.GetVkycRecord(), nil
}

func (fp *FederalProc) updateKYCInfo(ctx context.Context, actorId string, kycType kyc.KycType, kycAttemptId string, kycRec *kyc.KYCRecord) error {
	if err := fp.updateKycAttemptId(ctx, actorId, kycAttemptId); err != nil {
		return err
	}
	if kycType == kyc.KycType_EKYC || kycType == kyc.KycType_BKYC {
		if err := fp.updateEkycRrn(ctx, actorId, kycRec); err != nil {
			return err
		}
	}
	return nil
}

func (fp *FederalProc) updateKycAttemptId(ctx context.Context, actorId string, kycAttemptId string) error {
	if err := fp.dao.UpdateByActorId(ctx, &bankcust.BankCustomer{
		ActorId: actorId,
		Vendor:  vendor,
		VendorMetadata: &bankcust.VendorMetadata{
			Vendor: &bankcust.VendorMetadata_FederalMetadata{
				FederalMetadata: &bankcust.FederalMetadata{
					KycAttemptId: kycAttemptId,
				},
			},
		},
	}, []bankcust.BankCustomerFieldMask{
		bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_FEDERAL_KYC_ATTEMPT_ID,
	}); err != nil {
		logger.Error(ctx, "error while updating kyc attempt id in bank customer", zap.Error(err))
		return err
	}
	return nil
}

func (fp *FederalProc) updateEkycRrn(ctx context.Context, actorId string, kycRec *kyc.KYCRecord) error {
	ekycRrn, err := fp.getEkycRrn(ctx, actorId, kycRec)
	if err != nil {
		logger.Error(ctx, "error in fetching ekyc rrn number", zap.Error(err))
		return err
	}
	if err = fp.dao.UpdateByActorId(ctx, &bankcust.BankCustomer{
		ActorId: actorId,
		Vendor:  vendor,
		VendorMetadata: &bankcust.VendorMetadata{
			Vendor: &bankcust.VendorMetadata_FederalMetadata{
				FederalMetadata: &bankcust.FederalMetadata{
					EkycRrnNo: ekycRrn,
				},
			},
		},
	}, []bankcust.BankCustomerFieldMask{
		bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_EKYC_RRN_NO,
	}); err != nil {
		logger.Error(ctx, "error while updating ekyc rrn number in bank customer", zap.Error(err))
		return err
	}
	return nil
}

func (fp *FederalProc) updateSolId(ctx context.Context, actorId string) error {
	solId := fp.getSolId(ctx, actorId)
	logger.Info(ctx, fmt.Sprintf("sol id returned for actor: %s", solId))
	if err := fp.dao.UpdateByActorId(ctx, &bankcust.BankCustomer{
		ActorId: actorId,
		Vendor:  vendor,
		VendorMetadata: &bankcust.VendorMetadata{
			Vendor: &bankcust.VendorMetadata_FederalMetadata{
				FederalMetadata: &bankcust.FederalMetadata{
					SolId: solId,
				},
			},
		},
	}, []bankcust.BankCustomerFieldMask{
		bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_FEDERAL_SOL_ID,
	}); err != nil {
		logger.Error(ctx, "error while updating sol id in bank customer", zap.Error(err))
		return err
	}
	return nil
}

func (fp *FederalProc) getSolId(ctx context.Context, actorId string) string {
	solId := fp.conf.FederalSolIdConf().SolId()
	if fp.conf.FederalSolIdConf().NewSolIdConf().IsEnabled() &&
		CheckReleaseStickinessConstraint(ctx, actorId, NewSolId, fp.conf.FederalSolIdConf().NewSolIdConf().RolloutPercent()) {
		solId = fp.conf.FederalSolIdConf().NewSolIdConf().SolId()
	}
	return solId
}

func (fp *FederalProc) ProcessResidentialStatusUpdateCallback(ctx context.Context, req *bankcust.ProcessResidentialStatusUpdateCallbackRequest) error {
	bankCustomer, err := fp.dao.GetByVendorId(ctx, req.GetCustomerId(), req.GetVendor())
	if err != nil {
		logger.Error(ctx, "error in getting bank customer be vendor id", zap.Error(err))
		return err
	}
	inActiveReasons := bankCustomer.GetCustomerInactiveInfo().GetReasons()
	for _, reason := range inActiveReasons {
		if req.GetUpdatedAt().AsTime().Equal(reason.GetUpdatedAt().AsTime()) {
			// TODO (Rishu Sahu): make this log as debug once stable
			logger.Info(ctx, "we received the same packet again skipping the update")
			return nil
		}
	}
	customerInActiveInfo := bankCustomer.GetCustomerInactiveInfo()
	if bankCustomer.GetCustomerInactiveInfo() == nil {
		customerInActiveInfo = &bankcust.CustomerInactiveInfo{}
	}

	reasonType := accountTypeToInActiveReasonMap[req.GetAccountType()]
	if reasonType == bankcust.CustomerInactiveReasonType_CUSTOMER_INACTIVE_REASON_TYPE_UNSPECIFIED {
		return fmt.Errorf("inactive reason mapping does not exist %v", reasonType)
	}

	customerInActiveInfo.Reasons = append(customerInActiveInfo.GetReasons(), &bankcust.InactiveReasonInfo{
		ReasonType: reasonType,
		RawReason:  req.GetReason(),
		UpdatedAt:  req.GetUpdatedAt(),
	})
	bankCustomer.CustomerInactiveInfo = customerInActiveInfo
	bankCustomer.Status = bankcust.Status_CUSTOMER_STATUS_INACTIVE
	if err = fp.dao.UpdateByActorId(ctx, bankCustomer,
		[]bankcust.BankCustomerFieldMask{bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_BANK_CUSTOMER_INACTIVE_REASON_INFO, bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_STATUS}); err != nil {
		logger.Error(ctx, "error in updating bank customer for resident status update", zap.Error(err))
		return err
	}
	return nil
}

func (fp *FederalProc) ProcessMobileNumberUpdateCallback(ctx context.Context, req *bankcust.ProcessMobileNumberUpdateCallbackRequest) error {
	bankCustomer, err := fp.dao.GetByVendorId(ctx, req.GetCustomerId(), req.GetVendor())
	if err != nil {
		logger.Error(ctx, "error in getting bank customer by vendor id", zap.Error(err))
		return err
	}

	ctx = epificontext.CtxWithActorId(ctx, bankCustomer.GetActorId())

	externalPhNumChangeInfoHistories := bankCustomer.GetVendorMetadata().GetFederalMetadata().GetExternalPhNumChangeInfos().GetExternalPhNumChangeInfo()
	for _, externalPhNumChangeInfoHistory := range externalPhNumChangeInfoHistories {
		if req.GetRequestId() == externalPhNumChangeInfoHistory.GetRequestId() {
			// TODO (Bhabtosh): make this log as debug once stable
			logger.Info(ctx, "we received the same packet again skipping the update")
			return nil
		}
	}
	userResp, errResp := fp.usersClient.GetUser(ctx, &usersPb.GetUserRequest{
		Identifier: &usersPb.GetUserRequest_ActorId{
			ActorId: bankCustomer.GetActorId(),
		},
	})

	if err = epifigrpc.RPCError(userResp, errResp); err != nil {
		logger.Error(ctx, "error in getting user by actor id", zap.Error(err))
		return err
	}

	fiRegisteredNumber := userResp.GetUser().GetProfile().GetPhoneNumber().ToString()
	// checking last 4 digits only as in the notification we get masked phone number with last 4 digits visible
	if fiRegisteredNumber[len(fiRegisteredNumber)-4:] ==
		req.GetCbsMobileNumber()[len(req.GetCbsMobileNumber())-4:] {
		if bankCustomer.GetStatus() == bankcust.Status_CUSTOMER_STATUS_INACTIVE &&
			bankCustomer.GetCustomerInactiveInfo().GetReasons()[0].GetReasonType() == bankcust.CustomerInactiveReasonType_CUSTOMER_INACTIVE_REASON_TYPE_PHONE_NUMBER_UPDATE_OUTSIDE_FI {
			bankCustomer.Status = bankcust.Status_STATUS_ACTIVE
			updateMasks := []bankcust.BankCustomerFieldMask{
				bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_STATUS,
			}
			if err = fp.dao.UpdateByActorId(ctx, bankCustomer, updateMasks); err != nil {
				logger.Error(ctx, "error in updating bank customer status", zap.Error(err))
				return err
			}
			logger.Info(ctx, "marking bank customer active as mobile number updated outside FI is reverted to original number registered at FI")
		}
		logger.Info(ctx, "new phone number is same as FI registered number, skipping the update")
		return nil
	}

	afuRes, err := fp.authClient.GetAuthFactorUpdatesForActor(ctx, &auth.GetAuthFactorUpdatesForActorRequest{
		ActorId: bankCustomer.GetActorId(),
		Count:   1,
	})

	if err = epifigrpc.RPCError(afuRes, err); err != nil && !rpc.StatusFromError(err).IsRecordNotFound() {
		logger.Error(ctx, "error in fetching afu summary", zap.Error(err))
		return err
	}

	if len(afuRes.GetAuthFactorUpdates()) > 0 && lo.Contains(afuRes.GetAuthFactorUpdates()[0].GetContext().GetAuthFactors(), afu.AuthFactor_PHONE_NUM) {
		afuPhoneNumber := afuRes.GetAuthFactorUpdates()[0].GetContext().GetNewValues().GetPhoneNumber().ToString()
		if afuPhoneNumber[len(afuPhoneNumber)-4:] == req.GetCbsMobileNumber()[len(req.GetCbsMobileNumber())-4:] &&
			(afuRes.GetAuthFactorUpdates()[0].GetVendorContext().GetState() != afu.UpdateVendorState_UPDATE_VENDOR_STATE_UNSPECIFIED ||
				afuRes.GetAuthFactorUpdates()[0].GetVendorContext().GetState() != afu.UpdateVendorState_FAILED) {
			logger.Info(ctx, "notification received for FI initiated afu, skipping the update")
			return nil
		}
	}

	customerInActiveInfo := bankCustomer.GetCustomerInactiveInfo()
	if bankCustomer.GetCustomerInactiveInfo() == nil {
		customerInActiveInfo = &bankcust.CustomerInactiveInfo{}
	}

	reg := regexp.MustCompile("[^a-zA-Z0-9]+")
	strWithoutSpecialChars := reg.ReplaceAllString(req.GetReason(), "")
	reason := strings.ReplaceAll(strWithoutSpecialChars, " ", "")
	if !strings.Contains(strings.ToLower(reason), "devicedeactivatedonmobilenumberupdation") {
		return fmt.Errorf("invalid reason received for mobile number update %v", req.GetReason())
	}
	customerInActiveInfo.Reasons = append(customerInActiveInfo.GetReasons(), &bankcust.InactiveReasonInfo{
		ReasonType: bankcust.CustomerInactiveReasonType_CUSTOMER_INACTIVE_REASON_TYPE_PHONE_NUMBER_UPDATE_OUTSIDE_FI,
		RawReason:  req.GetReason(),
		UpdatedAt:  req.GetUpdatedAt(),
	})
	bankCustomer.CustomerInactiveInfo = customerInActiveInfo
	bankCustomer.Status = bankcust.Status_CUSTOMER_STATUS_INACTIVE
	externalPhNumChangeInfos := bankCustomer.GetVendorMetadata().GetFederalMetadata().GetExternalPhNumChangeInfos()
	if bankCustomer.GetVendorMetadata().GetFederalMetadata().GetExternalPhNumChangeInfos() == nil {
		externalPhNumChangeInfos = &bankcust.ExternalPhNumChangeInfos{}
	}
	externalPhNumChangeInfos.ExternalPhNumChangeInfo = append(externalPhNumChangeInfos.GetExternalPhNumChangeInfo(), &bankcust.ExternalPhNumChangeInfo{
		UpdatedAtVendor:       req.GetUpdatedAt(),
		RequestId:             req.GetRequestId(),
		MaskedOldMobileNumber: req.GetOldMobileNumber(),
		MaskedNewMobileNumber: req.GetCbsMobileNumber(),
	})
	bankCustomer.GetVendorMetadata().GetFederalMetadata().ExternalPhNumChangeInfos = externalPhNumChangeInfos
	updateMasks := []bankcust.BankCustomerFieldMask{
		bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_BANK_CUSTOMER_INACTIVE_REASON_INFO,
		bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_STATUS,
		bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_FEDERAL_EXTERNAL_PH_NUM_CHANGE_INFOS,
	}
	if err = fp.dao.UpdateByActorId(ctx, bankCustomer, updateMasks); err != nil {
		logger.Error(ctx, "error in updating bank customer for resident status update", zap.Error(err))
		return err
	}
	logger.Info(ctx, "marking bank customer inactive as mobile number update notification received")
	fp.eventBroker.AddToBatch(ctx, NewMobileNumberUpdateNotification(bankCustomer.GetActorId(), req.GetOldMobileNumber(), req.GetCbsMobileNumber(), req.GetRequestId(), req.GetReason(), req.GetUpdatedAt().AsTime()))

	// log out user to put into force afu as device got deactivated temporarily due to number updated outside FI
	actorRes, err := fp.actorClient.GetActorById(ctx, &actor.GetActorByIdRequest{
		Id: bankCustomer.GetActorId(),
	})
	if rpcErr := epifigrpc.RPCError(actorRes, err); rpcErr != nil {
		logger.Error(ctx, "error fetching actor details", zap.Error(rpcErr))
		return rpcErr
	}
	signoutRes, err := fp.authClient.SignOut(ctx, &auth.SignOutRequest{
		Actor: actorRes.GetActor(),
	})
	if rpcErr := epifigrpc.RPCError(signoutRes, err); rpcErr != nil {
		logger.Error(ctx, "error signing out", zap.Error(rpcErr))
		return rpcErr
	}
	return nil
}

func (fp *FederalProc) ProcessAuthFactorUpdateEvent(ctx context.Context, req *authNotificationPb.AuthFactorUpdateEvent) error {
	bankCustomer, err := fp.dao.GetByActorId(ctx, req.GetActorId(), vendor)
	if err != nil {
		logger.Error(ctx, "error in getting bank customer by actor id", zap.Error(err))
		return err
	}

	if bankCustomer.GetStatus() == bankcust.Status_CUSTOMER_STATUS_INACTIVE {
		switch {
		case bankCustomer.GetCustomerInactiveInfo().GetReasons() == nil:
			logger.Error(ctx, "for inactive status inactive reason can not be empty", zap.Error(err))
			return err
		case bankCustomer.GetCustomerInactiveInfo().GetReasons()[0].GetReasonType() == bankcust.CustomerInactiveReasonType_CUSTOMER_INACTIVE_REASON_TYPE_PHONE_NUMBER_UPDATE_OUTSIDE_FI:
			bankCustomer.Status = bankcust.Status_STATUS_ACTIVE
			updateMasks := []bankcust.BankCustomerFieldMask{
				bankcust.BankCustomerFieldMask_BANK_CUSTOMER_FIELD_MASK_STATUS,
			}
			if err = fp.dao.UpdateByActorId(ctx, bankCustomer, updateMasks); err != nil {
				logger.Error(ctx, "error in updating bank customer status", zap.Error(err))
				return err
			}
			logger.Info(ctx, "marking bank customer active as afu completed")

			// Send auto ID closure event
			fp.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), NewAutoIdClosureNotification(
				bankCustomer.GetActorId(),
				time.Now(),
			))
		}
	}
	return nil
}
