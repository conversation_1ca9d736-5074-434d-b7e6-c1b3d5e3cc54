package manager

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"reflect"
	"strings"
	"time"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/lock"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/retry"

	"github.com/slack-go/slack"
	"github.com/slack-go/slack/slackevents"
	"github.com/slack-go/slack/socketmode"
	"go.uber.org/zap"

	slackPb "github.com/epifi/gamma/api/vendorgateway/slack_bot"
	"github.com/epifi/gamma/api/vendorgateway/slack_bot/types"
	"github.com/epifi/gamma/slack_bot/config/genconf"
	"github.com/epifi/gamma/slack_bot/manager/callbackevents"
	"github.com/epifi/gamma/slack_bot/manager/forms"
	"github.com/epifi/gamma/slack_bot/utils"
)

type Service struct {
	Conf                  *genconf.Config
	slackVgClient         slackPb.SlackBotClient
	slForm                *forms.SecureLog
	issueForm             *forms.Issue
	formHandler           map[FormName]Handler
	lockManager           lock.ILockManager
	cbEventHandlerFactory callbackevents.IHandlerFactory
}

const (
	slackBotServiceConnLockKey = "nebula:slackbot:connection:lock"
)

func NewSlackBotManagerService(
	conf *genconf.Config,
	slackVgClient slackPb.SlackBotClient,
	slForm *forms.SecureLog,
	issueForm *forms.Issue,
	formHandler map[FormName]Handler,
	lockManager lock.ILockManager,
	cbEventHandlerFactory callbackevents.IHandlerFactory,
) (*Service, error) {
	slackBotService := &Service{
		Conf:                  conf,
		slackVgClient:         slackVgClient,
		slForm:                slForm,
		issueForm:             issueForm,
		formHandler:           formHandler,
		lockManager:           lockManager,
		cbEventHandlerFactory: cbEventHandlerFactory,
	}

	return slackBotService, nil
}

func (s *Service) initialiseFormHandler(ctx context.Context) error {
	userGroup, userGroupErr := s.slackVgClient.GetUserGroups(context.Background(), &slackPb.GetUserGroupsRequest{IncludeDisable: false})
	if err := epifigrpc.RPCError(userGroup, userGroupErr); err != nil {
		logger.Error(ctx, "Error in fetching user group", zap.Error(err))
		return fmt.Errorf("error in fetching userGroup %w", err)
	}
	s.issueForm.UserGroups = userGroup.GetUserGroup()
	logger.Info(ctx, "No of user group found", zap.Int("No of user group found", len(userGroup.GetUserGroup())))
	return nil
}

// Start eventListener
// There will be one eventListener per instance
func (s *Service) RunService(ctx context.Context) error {
	err := s.initialiseFormHandler(ctx)
	if err != nil {
		return err
	}
	s.eventListener(ctx)
	return nil
}

// eventListener uses a VG event listener stream to receive slack event,
// connection failures are RPC errors handled using infinite retry loop,
// on successful connection, an infinite loop reads from stream and handle all the events,
// errors during event handling are handled using configured retry loop
func (s *Service) eventListener(ctx context.Context) {

	for {
		func() {
			defer time.Sleep(s.Conf.VgConnectionStrategy().VgConnectionRetryInterval)
			// every slack event should be handled only once,
			// acquire lock on VG event listener stream connection for current instance
			// to prevent multiple instances of nebula from connecting to VG steam for receiving slack-events,
			// only one instance of nebula which has acquired the lock can receive and handle the slack-events
			lockDuration := s.Conf.VgConnectionStrategy().VgStreamConnectionDuration
			vgLock, lockErr := s.lockManager.GetLockWithRegularRefresh(ctx, slackBotServiceConnLockKey, lockDuration)
			if lockErr != nil {
				logger.Info(ctx, "couldn't acquire lock. Probably some instance would have got the lock", zap.String("slackBotServiceConnLockKey", slackBotServiceConnLockKey), zap.Error(lockErr))
				return
			}
			defer func() {
				if err := vgLock.Release(ctx); err != nil {
					logger.Error(ctx, "error releasing vglock", zap.Error(err))
				}
			}()

			realTimeSlackEvent, err := s.slackVgClient.ListenSlackEvents(ctx, &slackPb.ListenSlackEventRequest{})
			if err != nil {
				logger.Error(ctx, "error getting real time slack events", zap.Error(err))
				return
			}
			defer func() { _ = realTimeSlackEvent.CloseSend() }()
			for {
				res, recvErr := realTimeSlackEvent.Recv()
				if errors.Is(recvErr, io.EOF) {
					logger.Error(ctx, "stream ended", zap.Error(recvErr))
					break
				}
				if recvErr != nil {
					logger.Error(ctx, "recv resulted in error", zap.Error(recvErr))
					break
				}
				if recvErr = epifigrpc.RPCError(res, recvErr); recvErr != nil {
					logger.Error(ctx, "received response status not ok", zap.Error(recvErr))
					continue
				}

				eventProto := res.GetEvent()
				// Handle all events in a separate goroutine
				goroutine.RunWithCtx(ctx, func(ctx context.Context) {

					ctxWithDeadline, cancelCtx := context.WithDeadline(epificontext.CloneCtx(ctx), time.Now().Add(s.Conf.RetryStrategy().MaxDeadLine))
					defer cancelCtx()
					ctxWithDeadline = epificontext.WithTraceId(ctxWithDeadline, make(map[string][]string))

					if err := retry.WithMaxAttempts(ctxWithDeadline, retry.Options{
						InitialBackoff: s.Conf.RetryStrategy().ExponentialBackOff.BaseInterval,
						MaxBackoff:     s.Conf.RetryStrategy().ExponentialBackOff.MaxBackOff,
					}, s.Conf.RetryStrategy().MaxAttempts, func() error {
						handleErr := s.handleEvents(ctxWithDeadline, eventProto)
						if errors.Is(handleErr, &utils.PermanentError{}) == true {
							logger.Error(ctxWithDeadline, "permanent failure handling event", zap.Error(handleErr))
							return nil // Do not retry on permanent failure
						}
						return handleErr
					}); err != nil {
						logger.Error(ctxWithDeadline, "error handling event", zap.Error(err))
					}
				})
			}
		}()
	}
}

func (s *Service) handleEvents(ctx context.Context, eventProto *types.Event) error {

	// Check event type - https://api.slack.com/events
	switch socketmode.EventType(eventProto.GetType()) {
	// Slash-command event - https://api.slack.com/interactivity/slash-commands
	case socketmode.EventTypeSlashCommand:
		var eventAPI slack.SlashCommand
		err := json.Unmarshal(eventProto.GetData().GetValue(), &eventAPI)
		if err != nil {
			return fmt.Errorf("event data is not a SlashCommand type %w", err)
		}

		err = s.handleSlashCommandEvent(ctx, &eventAPI)
		if err != nil {
			return fmt.Errorf("error handling SlashCommand event %w", err)
		}
	//	User-triggered interaction type events - https://api.slack.com/interactivity/handling-
	case socketmode.EventTypeInteractive:

		var eventsAPI slack.InteractionCallback
		err := json.Unmarshal(eventProto.GetData().GetValue(), &eventsAPI)
		if err != nil {
			return fmt.Errorf("event data is not InteractionCallback type %w", err)
		}

		switch eventsAPI.Type {
		case slack.InteractionTypeViewSubmission:
			formType := getFormTypeFromCallbackID(eventsAPI.View.CallbackID)
			err := s.formHandler[formType].HandleEvent(ctx, &eventsAPI, eventsAPI.View.CallbackID)
			if err != nil {
				return fmt.Errorf("error handling InteractiveViewSubmission event %w", err)
			}

		case slack.InteractionTypeInteractionMessage:
			formType := getFormTypeFromCallbackID(eventsAPI.CallbackID)
			err := s.formHandler[formType].HandleEvent(ctx, &eventsAPI, eventsAPI.CallbackID)
			if err != nil {
				return fmt.Errorf("error handling slack InteractiveMessage event %w", err)
			}
		case slack.InteractionTypeMessageAction:
			callBackId := eventsAPI.CallbackID
			callBackId = "issue:" + callBackId
			formType := getFormTypeFromCallbackID(callBackId)
			err := s.formHandler[formType].HandleEvent(ctx, &eventsAPI, callBackId)
			if err != nil {
				return fmt.Errorf("error handling slack InteractiveMessage event %w", err)
			}
		default:
			return fmt.Errorf("unhandled slack event-type %w", errors.New("eventType: "+string(eventsAPI.Type)))
		}

	case socketmode.EventTypeEventsAPI:
		logger.Debug(ctx, "received EventsAPI event", zap.Any("event", eventProto))
		eventsAPIEvent, err := s.parseEventsApiEvent(eventProto)
		if err != nil {
			logger.Error(ctx, "error parsing slack events api event", zap.Error(err))
			return fmt.Errorf("error parsing slack event %w", err)
		}

		switch eventsAPIEvent.Type {
		case slackevents.CallbackEvent:
			handler, handlerErr := s.cbEventHandlerFactory.GetHandler(eventsAPIEvent)
			if handlerErr != nil {
				return fmt.Errorf("error getting callback event handler %w", handlerErr)
			}
			handleErr := handler.HandleEvent(ctx, eventsAPIEvent)
			if handleErr != nil {
				return fmt.Errorf("error handling callback event %w", handleErr)
			}
		default:
			logger.Debug(ctx, "event type not handled", zap.String(logger.EVENT_TYPE, eventsAPIEvent.Type))
			return nil
		}

	default:
		// using Info level log to avoid unnecessary error logs during connection events
		logger.Info(ctx, "socketmode event type does not have a handler", zap.Any("event", eventProto.GetType()))
		return nil
	}
	return nil
}

func (s *Service) parseEventsApiEvent(eventProto *types.Event) (*slackevents.EventsAPIEvent, error) {
	event := &slackevents.EventsAPIEvent{}
	err := json.Unmarshal(eventProto.GetData().GetValue(), event)
	if err != nil {
		return nil, fmt.Errorf("error unmarshalling event proto, err: %w", err)
	}

	// If the event is not CallbackEvent, parse the inner event using slackevents lib method
	// WHY? - slackevents lib have a bug where it panics when parsing CallbackEvent
	if event.Type != slackevents.CallbackEvent {
		*event, err = slackevents.ParseEvent(eventProto.GetData().GetValue(), slackevents.OptionNoVerifyToken())
		if err != nil {
			return nil, fmt.Errorf("error parsing slack event, err: %w", err)
		}

		return event, nil
	}

	// parse inner event data (interface) into a struct
	innerEventDataMap, ok := event.InnerEvent.Data.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("inner event data is not of map[string]interface{} type")
	}

	innerEventDataBytes, err := json.Marshal(innerEventDataMap)
	if err != nil {
		return nil, fmt.Errorf("error marshalling inner event data map, err: %w", err)
	}

	innerEventInstance, ok := slackevents.EventsAPIInnerEventMapping[slackevents.EventsAPIType(event.InnerEvent.Type)]
	if !ok {
		return nil, fmt.Errorf("inner event type not found in slackevents.EventsAPIInnerEventMapping")
	}
	innerEventType := reflect.TypeOf(innerEventInstance)
	innerEvent := reflect.New(innerEventType).Interface()
	err = json.Unmarshal(innerEventDataBytes, innerEvent)
	if err != nil {
		return nil, fmt.Errorf("error unmarshalling inner event, err: %w", err)
	}

	event.InnerEvent.Data = innerEvent
	return event, nil
}

// Opens view for user according to channel
func (s *Service) handleSlashCommandEvent(ctx context.Context, event *slack.SlashCommand) error {
	var form *types.ViewModal
	switch event.Command {
	case "/secure-data-form":
		form = generateFormViewModal()
	case "/raise-feedback", "/issue":
		milestoneOptions, err := utils.GetMilestoneOptions(ctx, s.issueForm.GithubApiWrapper, utils.GITHUB_REPO_OWNER, utils.GITHUB_TICKETS_REPO_NAME)
		if err != nil {
			logger.Error(ctx, "error getting milestone options", zap.Error(err))
			return fmt.Errorf("error getting milestone options %w", err)
		}
		preface := "Raise Issue"
		if s.Conf.ChannelsDetail().Get(event.ChannelName) != nil && s.Conf.ChannelsDetail().Get(event.ChannelName).IssuePreface() != "" {
			preface = s.Conf.ChannelsDetail().Get(event.ChannelName).IssuePreface()
		}
		var preferredBu string
		if s.Conf.ChannelsDetail().Get(event.ChannelName) != nil && s.Conf.ChannelsDetail().Get(event.ChannelName).PreferredBU() != "" {
			preferredBu = s.Conf.ChannelsDetail().Get(event.ChannelName).PreferredBU()
		}
		templateOptions, ok := s.issueForm.FormOptionsMap[utils.ISSUE_TEMPLATE_FIELD_NAME]
		if !ok {
			logger.Error(ctx, "Issue template options not found in form options map")
			return fmt.Errorf("issue template options not found")
		}
		projectOptions, ok := s.issueForm.FormOptionsMap[utils.PROJECT_FIELD_NAME]
		if !ok {
			logger.Error(ctx, "Project options not found in form options map")
			return fmt.Errorf("project options not found")
		}

		form, err = s.issueForm.GenerateViewModal(event.ChannelID, event.ChannelName, utils.GetAllBusinessUnits(s.issueForm.BusinessUnitDetail, event.ChannelName), preface, "", "", "", "", preferredBu, milestoneOptions, templateOptions, projectOptions)
		if err != nil {
			logger.Error(ctx, "error generating view modal for issue form", zap.Error(err))
			return fmt.Errorf("error generating view modal for issue form %w", err)
		}
		resp, respErr := s.slackVgClient.OpenViewModal(ctx, &slackPb.OpenViewModalRequest{ViewModal: form, TriggerId: event.TriggerID})
		if err := epifigrpc.RPCError(resp, respErr); err != nil {
			return fmt.Errorf("error opening view modal %w", err)
		}
		return nil
	default:
		return fmt.Errorf("no appropriate action found for the slash-command %w", errors.New("slash-command: "+event.Command))
	}

	resp, respErr := s.slackVgClient.OpenViewModal(ctx, &slackPb.OpenViewModalRequest{ViewModal: form, TriggerId: event.TriggerID})
	if err := epifigrpc.RPCError(resp, respErr); err != nil {
		return fmt.Errorf("error opening view modal %w", err)
	}

	return nil
}

// Note: All the callbackIds for a form modal should be in following format
// <FormName>:<any unique string>
//
//nolint:funlen
func generateFormViewModal() *types.ViewModal {
	titleText := types.TextBlockObject{Type: "plain_text", Text: "Fi-Data-Form", Emoji: false, Verbatim: false}
	closeText := types.TextBlockObject{Type: "plain_text", Text: "Close", Emoji: false, Verbatim: false}
	submitText := types.TextBlockObject{Type: "plain_text", Text: "Submit", Emoji: false, Verbatim: false}

	headerText := types.TextBlockObject{Type: "mrkdwn", Text: "Create Secure Log Request", Emoji: false, Verbatim: false}
	headerSection := types.SectionBlock{Text: &headerText}

	reasonStringText := types.TextBlockObject{Type: "plain_text", Text: "Reason", Emoji: false, Verbatim: false}
	reasonStringPlaceholder := types.TextBlockObject{Type: "plain_text", Text: "Why is it required?", Emoji: false, Verbatim: false}
	reasonStringElement := types.PlainTextInputBlockElement{PlaceHolder: &reasonStringPlaceholder, ActionId: "reasonString"}
	reasonString := types.InputBlock{BlockId: "Reason String", Label: &reasonStringText, BlockElement: &types.InputBlock_PlainTextInputBlockElement{PlainTextInputBlockElement: &reasonStringElement}}

	queryStringText := types.TextBlockObject{Type: "plain_text", Text: "Query", Emoji: false, Verbatim: false}
	queryStringPlaceholder := types.TextBlockObject{Type: "plain_text", Text: "Enter the query using Lucene syntax", Emoji: false, Verbatim: false}
	queryStringElement := types.PlainTextInputBlockElement{PlaceHolder: &queryStringPlaceholder, ActionId: "queryString", Multiline: true}
	queryString := types.InputBlock{BlockId: "Query String", Label: &queryStringText, BlockElement: &types.InputBlock_PlainTextInputBlockElement{PlainTextInputBlockElement: &queryStringElement}}

	fieldsStringText := types.TextBlockObject{Type: "plain_text", Text: "Required Fields", Emoji: false, Verbatim: false}
	fieldsStringPlaceholder := types.TextBlockObject{Type: "plain_text", Text: "Specify fields to be fetched as comma-separated (Ex:msg,payload)", Emoji: false, Verbatim: false}
	fieldsStringHint := types.TextBlockObject{Type: "plain_text", Text: "Specify \"All\" to fetch all but the Approver may reject your request", Emoji: false, Verbatim: false}
	fieldsStringElement := types.PlainTextInputBlockElement{PlaceHolder: &fieldsStringPlaceholder, ActionId: "fieldsString"}
	fieldsString := types.InputBlock{BlockId: "Field String",
		Label:        &fieldsStringText,
		BlockElement: &types.InputBlock_PlainTextInputBlockElement{PlainTextInputBlockElement: &fieldsStringElement},
		Hint:         &fieldsStringHint,
	}

	startDateText := types.TextBlockObject{Type: "plain_text", Text: "Start Date", Emoji: false, Verbatim: false}
	startDateElement := types.DatePickerBlockElement{ActionId: "startDate"}
	startDateHint := types.TextBlockObject{Type: "plain_text", Text: "Specify Shorter Time Window", Emoji: false, Verbatim: false}
	startDate := types.InputBlock{BlockId: "Start Date", Label: &startDateText, BlockElement: &types.InputBlock_DatePickerBlockElement{DatePickerBlockElement: &startDateElement}, Hint: &startDateHint}

	redactedRespStringText := types.TextBlockObject{Type: "plain_text", Text: "Do you need unredacted credentials for debugging?", Emoji: false, Verbatim: false}
	redactedRespOptions := createOptionBlockObjects([]string{"Yes", "No"})
	redactedRespInitialOptionText := types.TextBlockObject{Type: "plain_text", Text: "No", Emoji: false, Verbatim: false}
	redactedRespInitialOption := &types.OptionBlockObject{Text: &redactedRespInitialOptionText, Value: "No"}
	redactedRespSelectStringElement := types.SelectMenuBlockElement{Type: "static_select", InitialOption: redactedRespInitialOption, Options: redactedRespOptions, ActionId: "shouldSendCredentials"}
	selectredactedRespString := types.InputBlock{BlockId: "Should Send Creds", Label: &redactedRespStringText, BlockElement: &types.InputBlock_SelectMenusBlockElement{SelectMenusBlockElement: &redactedRespSelectStringElement}}

	startTimeText := types.TextBlockObject{Type: "plain_text", Text: "Start Time", Emoji: false, Verbatim: false}
	startTimeElement := types.TimePickerBlockElement{ActionId: "startTime"}
	startTime := types.InputBlock{BlockId: "Start Time", Label: &startTimeText, BlockElement: &types.InputBlock_TimePickerBlockElement{TimePickerBlockElement: &startTimeElement}}
	startTime.Optional = true

	endDateText := types.TextBlockObject{Type: "plain_text", Text: "End Date", Emoji: false, Verbatim: false}
	endDateElement := types.DatePickerBlockElement{ActionId: "endDate"}
	endDate := types.InputBlock{BlockId: "End Date", Label: &endDateText, BlockElement: &types.InputBlock_DatePickerBlockElement{DatePickerBlockElement: &endDateElement}}
	endDate.Optional = true

	endTimeText := types.TextBlockObject{Type: "plain_text", Text: "End Time", Emoji: false, Verbatim: false}
	endTimeElement := types.TimePickerBlockElement{ActionId: "endTime"}
	endTime := types.InputBlock{BlockId: "End Time", Label: &endTimeText, BlockElement: &types.InputBlock_TimePickerBlockElement{TimePickerBlockElement: &endTimeElement}}
	endTime.Optional = true

	blocks := types.Blocks{Block: []*types.Block{
		{Block: &types.Block_SectionBlock{SectionBlock: &headerSection}},
		{Block: &types.Block_InputBlock{InputBlock: &reasonString}},
		{Block: &types.Block_InputBlock{InputBlock: &queryString}},
		{Block: &types.Block_InputBlock{InputBlock: &fieldsString}},
		{Block: &types.Block_InputBlock{InputBlock: &selectredactedRespString}},
		{Block: &types.Block_InputBlock{InputBlock: &startDate}},
		{Block: &types.Block_InputBlock{InputBlock: &startTime}},
		{Block: &types.Block_InputBlock{InputBlock: &endDate}},
		{Block: &types.Block_InputBlock{InputBlock: &endTime}},
	}}

	var modalRequest types.ViewModal
	modalRequest.Type = forms.MODAL_TYPE
	modalRequest.Title = &titleText
	modalRequest.Close = &closeText
	modalRequest.Submit = &submitText
	modalRequest.CallBackId = "secure_logs:request_modal"
	modalRequest.Blocks = &blocks

	return &modalRequest
}
func createOptionBlockObjects(options []string) []*types.OptionBlockObject {
	optionBlockObjects := make([]*types.OptionBlockObject, 0, len(options))

	for _, opt := range options {
		if opt == "all" {
			continue
		}
		optionText := types.TextBlockObject{Type: "plain_text", Text: opt, Emoji: false, Verbatim: false}
		optionBlockObjects = append(optionBlockObjects, &types.OptionBlockObject{Text: &optionText, Value: opt})
	}
	return optionBlockObjects
}

func getFormTypeFromCallbackID(callbackId string) FormName {
	formPrefix := strings.Split(callbackId, ":")[0]
	switch formPrefix {
	case "secure_logs":
		return SecureLogForm
	default:
		return IssueForm
	}
}
