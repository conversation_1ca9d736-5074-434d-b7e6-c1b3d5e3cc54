package utils

import (
	"context"
	"fmt"
	"sort"
	"time"

	githubpkg "github.com/epifi/be-common/pkg/github"
	"github.com/epifi/be-common/pkg/logger"
	"go.uber.org/zap"

	githubpb "github.com/epifi/be-common/api/pkg/github"

	"github.com/epifi/gamma/api/vendorgateway/slack_bot/types"
)

// AddIssueComment adds a comment to an existing GitHub issue
func AddIssueComment(ctx context.Context, githubApiWrapper githubpkg.IGithubApiWrapper, comment string, issueResponse *githubpb.CreateIssueResponse) (*githubpb.AddCommentResponse, error) {
	commentRequest := &githubpb.AddCommentRequest{
		Comment: &githubpb.Comment{
			ProjectOwner: GITHUB_REPO_OWNER,
			RepoName:     GITHUB_TICKETS_REPO_NAME,
			IssueNumber:  issueResponse.GetIssueNumber(),
			Body:         comment,
		},
	}

	return githubApiWrapper.AddComment(ctx, commentRequest)
}

// CreateGithubIssue creates a new GitHub issue
func CreateGithubIssue(ctx context.Context, githubApiWrapper githubpkg.IGithubApiWrapper, issueInput *githubpb.CreateIssueRequest) (*githubpb.CreateIssueResponse, error) {
	return githubApiWrapper.CreateIssue(ctx, issueInput)
}

// GetMilestoneOptions fetches milestone options from GitHub, showing only the 3 upcoming milestones plus backlog
func GetMilestoneOptions(ctx context.Context, milestones []*githubpb.Milestone) ([]*types.OptionBlockObject, error) {

	var options []*types.OptionBlockObject

	// Add backlog option first
	options = append(options, &types.OptionBlockObject{
		Text: &types.TextBlockObject{
			Type: "plain_text",
			Text: "Backlog",
		},
		Value: "Backlog",
	})

	// Filter and sort milestones by due date
	var upcomingMilestones []*githubpb.Milestone
	currentTime := time.Now()

	for _, milestone := range milestones {
		// Only include open milestones with a due date
		if milestone.State == "OPEN" && milestone.DueOn != "" {
			// Parse the due date
			dueDate, err := time.Parse(time.RFC3339, milestone.DueOn)
			if err != nil {
				logger.Error(ctx, "Failed to parse milestone due date", zap.String("milestone", milestone.Title), zap.String("due_on", milestone.DueOn),
					zap.Error(err))
				continue
			}

			// Only include future milestones or those due today
			if dueDate.After(currentTime.AddDate(0, 0, -1)) { // Include if due date is today or later
				upcomingMilestones = append(upcomingMilestones, milestone)
			}
		}
	}

	// Sort milestones by due date (earliest first)
	sort.Slice(upcomingMilestones, func(i, j int) bool {
		dueI, _ := time.Parse(time.RFC3339, upcomingMilestones[i].DueOn)
		dueJ, _ := time.Parse(time.RFC3339, upcomingMilestones[j].DueOn)
		return dueI.Before(dueJ)
	})

	// Take only the next 3 upcoming milestones
	maxMilestones := 3
	if len(upcomingMilestones) < maxMilestones {
		maxMilestones = len(upcomingMilestones)
	}

	for i := 0; i < maxMilestones; i++ {
		milestone := upcomingMilestones[i]
		// Parse due date for display
		dueDate, _ := time.Parse(time.RFC3339, milestone.DueOn)
		formattedDate := dueDate.Format("Jan 2, 2006")

		displayText := fmt.Sprintf("%s (Due: %s)", milestone.Title, formattedDate)

		options = append(options, &types.OptionBlockObject{
			Text: &types.TextBlockObject{
				Type: "plain_text",
				Text: displayText,
			},
			Value: milestone.Title,
		})
	}

	return options, nil
}
func GetProjectOptions(ctx context.Context, githubApiWrapper githubpkg.IGithubApiWrapper) ([]*types.OptionBlockObject, error) {
	projectsMap, err := githubApiWrapper.FetchOpenProjectsFromRepo(ctx, GITHUB_REPO_OWNER, GITHUB_TICKETS_REPO_NAME)
	if err != nil {
		logger.Error(ctx, "Error fetching projects from GitHub", zap.Error(err))
		return nil, fmt.Errorf("error fetching projects from GitHub: %w", err)
	}
	var options []*types.OptionBlockObject
	for name := range projectsMap {
		options = append(options, &types.OptionBlockObject{
			Text: &types.TextBlockObject{
				Type: "plain_text",
				Text: name,
			},
			Value: name,
		})
	}
	return options, nil
}

// GetIssueTemplateOptions fetches issue template options from GitHub
func GetIssueTemplateOptions(ctx context.Context, githubApiWrapper githubpkg.IGithubApiWrapper, repoOwner, repoName string) ([]*types.OptionBlockObject, error) {
	response, err := githubApiWrapper.GetIssueTemplates(ctx, &githubpb.GetIssueTemplatesRequest{
		Owner: repoOwner,
		Repo:  repoName,
	})
	if err != nil {
		logger.Error(ctx, "Error in fetching templates from GitHub", zap.Error(err))
		return nil, fmt.Errorf("error in fetching templates from GitHub: %w", err)
	}
	var options []*types.OptionBlockObject
	for _, template := range response.GetTemplates() {
		options = append(options, &types.OptionBlockObject{
			Text: &types.TextBlockObject{
				Type: "plain_text",
				Text: template.Name,
			},
			Value: template.Name,
		})
	}
	return options, nil
}

// IsDuplicateTicket checks if a ticket with the same title and reporter email has been created in the last minute.
func IsDuplicateTicket(ctx context.Context, githubApiWrapper githubpkg.IGithubApiWrapper, title, reporterEmailId string) bool {
	// Logic to check for duplicate tickets would go here
	// For now, returning false as a placeholder
	return false
}
