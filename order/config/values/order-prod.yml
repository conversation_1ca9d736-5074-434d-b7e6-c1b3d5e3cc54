Application:
  Environment: "prod"
  Name: "order"
  IsSeparateEnquiryPerProtocolEnabled: true

Server:
  Ports:
    GrpcPort: 8091
    GrpcSecurePort: 9513
    HttpPort: 9999
    HttpPProfPort: 9990

EpifiDb:
  DbType: "CRDB"
  AppName: "order"
  StatementTimeout: 15s
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  SSLMode: "verify-full"
  SSLRootCert: "prod/cockroach/ca.crt"
  SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 100
  MaxIdleConn: 8
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLogInProd: false
    EnableMultiDBSupport: true
    DBResolverList:
      - Alias: "connected_account_pgdb"
        DbDsn:
          DbType: "PGDB"
          AppName: "order"
          StatementTimeout: 1m
          Name: "connected_account"
          SSLMode: "verify-full"
          SSLRootCert: "prod/rds/rds-ca-root-2061"
          SecretName: "prod/rds/epifimetis/connected-account"

EpifiWealthDB:
  DbType: "CRDB"
  AppName: "order"
  StatementTimeout: 15s
  Username: "epifi_wealth_dev_user"
  Password: ""
  Name: "epifi_wealth"
  SSLMode: "verify-full"
  SSLRootCert: "prod/cockroach/ca.crt"
  SSLClientCert: "prod/cockroach/client.epifi_wealth_dev_user.crt"
  SSLClientKey: "prod/cockroach/client.epifi_wealth_dev_user.key"
  MaxOpenConn: 30
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLogInProd: false
    EnableMultiDBSupport: true
    DBResolverList:
      - Alias: "connected_account_pgdb"
        DbDsn:
          DbType: "PGDB"
          AppName: "order"
          StatementTimeout: 1m
          Name: "connected_account"
          SSLMode: "verify-full"
          SSLRootCert: "prod/rds/rds-ca-root-2061"
          SecretName: "prod/rds/epifimetis/connected-account"

PaymentOrchestrationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-payment-orchestration-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~51 min post that regular interval of 2 hours is followed for next 7 days.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 3
          MaxAttempts: 10
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 2
          MaxAttempts: 83
          TimeUnit: "Hour"
      MaxAttempts: 93
      CutOff: 10

IntraBankEnquirySubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-payment-intrabank-enquiry-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~51 min post that regular interval of 2 hours is followed for 3 days.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 3
          MaxAttempts: 10
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 2
          MaxAttempts: 35
          TimeUnit: "Hour"
      MaxAttempts: 45
      CutOff: 10
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 15
        Period: 1s
    Namespace: "order:intrabank"

UPIEnquirySubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-payment-upi-enquiry-queue"
  # We are modifying retry strategy as per NPCI Circular.
  # Ref - https://docs.google.com/document/d/187bzjFu8r8aZxdUFA1H7cvL3mk44B94tzbMTpRLW5-U/edit?tab=t.0
  RetryStrategy:
    RegularInterval:
      Interval: 2
      MaxAttempts: 3
      TimeUnit: "Hour"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 15
        Period: 1s
    Namespace: "order:upi"

IMPSEnquirySubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-payment-imps-enquiry-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~51 min post that regular interval of 2 hours is followed for next 3 days.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 3
          MaxAttempts: 10
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 2
          MaxAttempts: 35
          TimeUnit: "Hour"
      MaxAttempts: 45
      CutOff: 10
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 15
        Period: 1s
    Namespace: "order:imps"

NEFTEnquirySubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-payment-neft-enquiry-queue"
  RetryStrategy:
    Hybrid: # Regular interval of 15 minutes is followed is followed for first 2 hours post that regular interval of 2 hours is followed for next 3 days.
      RetryStrategy1:
        RegularInterval:
          Interval: 15
          MaxAttempts: 8
          TimeUnit: "Minute"
      RetryStrategy2:
        RegularInterval:
          Interval: 1
          MaxAttempts: 70
          TimeUnit: "Hour"
      MaxAttempts: 45
      CutOff: 10
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 5
        Period: 1s
    Namespace: "order:neft"

RTGSEnquirySubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-payment-rtgs-enquiry-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~51 min post that regular interval of 2 hours is followed for next 3 days.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 3
          MaxAttempts: 10
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 2
          MaxAttempts: 35
          TimeUnit: "Hour"
      MaxAttempts: 45
      CutOff: 10
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 15
        Period: 1s
    Namespace: "order:rtgs"

OrderOrchestrationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-order-orchestration-queue"
  # Regular interval for 1 min, as the business logic is limited to DB update
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 13
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 30
        Period: 1s
    Namespace: "order-consumer"

PaymentCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-payment-callback-update-queue"
  # Regular interval for 1 min, as the business logic is limited to DB update
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 13
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 5
        Period: 1s
    Namespace: "order"

InboundTxnSubscriber:
  StartOnServerStart: true
  NumWorkers: 5
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-inbound-txn-queue"
  # Exponential backoff is followed for first ~68 min post that regular interval is followed for next 6 hours.
  # The deposit creation notifications event are expected to be processed post deposit creation
  # Hence, retry strategies are aligned with deposit creation/closure retry strategies and status checks
  RetryStrategy:
    Hybrid:
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 80
          MaxAttempts: 2
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 20
          MaxAttempts: 3
          TimeUnit: "Minute"
      MaxAttempts: 5
      CutOff: 2
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 50
        Period: 1s
    Namespace: "order-consumer"

InboundUpiTxnSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-inbound-upi-txn-queue"
  RetryStrategy:
    Hybrid:
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 15
          MaxAttempts: 5
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 30
          MaxAttempts: 5
          TimeUnit: "Minute"
      MaxAttempts: 10
      CutOff: 5
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 50
        Period: 1s
    Namespace: "order-consumer"

OrderUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-order-update-order-notification-consumer-queue"
  RetryStrategy:
    Hybrid:
      RetryStrategy1:
        # Exponential backoff for ~50 min
        ExponentialBackOff:
          BaseInterval: 3
          MaxAttempts: 10
          TimeUnit: "Second"
      RetryStrategy2:
        # Regular interval for next 12 hours
        RegularInterval:
          Interval: 30
          MaxAttempts: 24
          TimeUnit: "Minute"
      MaxAttempts: 34
      CutOff: 10
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 75
        Period: 1s
    Namespace: "order-consumer"

OrderCollectNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-order-collect-notification-consumer-queue"
  RetryStrategy:
    Hybrid:
      RetryStrategy1:
        # Exponential backoff for ~50 min
        ExponentialBackOff:
          BaseInterval: 3
          MaxAttempts: 10
          TimeUnit: "Second"
      RetryStrategy2:
        # Regular interval for next 12 hours
        RegularInterval:
          Interval: 30
          MaxAttempts: 24
          TimeUnit: "Minute"
      MaxAttempts: 34
      CutOff: 10

OrderNotificationFallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 3
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-order-notification-fallback-consumer-queue"
  RetryStrategy:
    Hybrid:
      RetryStrategy1:
        # Exponential backoff for ~60 min
        ExponentialBackOff:
          BaseInterval: 7
          MaxAttempts: 10
          TimeUnit: "Second"
      RetryStrategy2:
        # Regular interval for next 12 hours
        RegularInterval:
          Interval: 30
          MaxAttempts: 24
          TimeUnit: "Minute"
      MaxAttempts: 34
      CutOff: 10
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 75
        Period: 1s
    Namespace: "order-consumer"

OrderWorkflowProcessingSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-order-workflow-processing-queue"
  # The retry strategy for OrderWorkflowProcessing is defined by consumer business logic, hence the strategy
  # defined here is only used to for max attempts by the subscriber library code.
  # Please check the workflow specific retry strategies in order-params.yml
  RetryStrategy:
    RegularInterval:
      Interval: 20
      MaxAttempts: 100
      TimeUnit: "Minute"

DisputeEventProcessingSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-cx-dispute-events-external-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 15
      TimeUnit: "Second"

SavingsLedgerReconSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 3
  QueueName: "prod-order-savings-ledger-recon-queue"
  # Exponential backoff till 90 second post that regular interval is followed for 90 minutes, to add tolerance against transient failure due to network failure while fetching statement
  RetryStrategy:
    Hybrid:
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 30
          MaxAttempts: 3
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 30
          MaxAttempts: 3
          TimeUnit: "Minute"
      MaxAttempts: 6
      CutOff: 3
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 3
        Period: 1s
    Namespace: "order-saving-Ledger"

AATxnSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 4
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-order-aa-txn-queue"
  # Exponential backoff till 2.5 hrs
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 12
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 80
        Period: 1s
    Namespace: "order-aa"

AAFirstDataPullTxnSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-order-aa-first-data-pull-txn-queue"
  # Exponential backoff till 2.5 hrs
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 12
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 120
        Period: 1s
    Namespace: "order-aa"

PurgeAaDataSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-order-aa-data-purge-queue"
  # Exponential backoff till 2.5 hrs
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 12
      TimeUnit: "Second"

AaTxnPurgeSubscriber:
  StartOnServerStart: false
  Disable: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-aa-txn-purge-queue"
  # Exponential backoff till 2.5 hrs
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 12
      TimeUnit: "Second"

AaDataPurgeOrchestrationSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-order-aa-data-purging-orchestration-queue"
  # Exponential backoff till 2.5 hrs
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 12
      TimeUnit: "Second"



OrderOrchestrationPublisher:
  QueueName: "prod-order-orchestration-queue"

PaymentOrchestrationPublisher:
  QueueName: "prod-payment-orchestration-queue"

OrderUpdateEventPublisher:
  TopicName: "prod-order-update-topic"

OrderMerchantMergeEventPublisher:
  TopicName: "prod-batch-order-update-topic"

AATxnPublisher:
  TopicName: "prod-order-aa-txn-topic"

TxnDetailedStatusUpdateSnsPublisher:
  TopicName: "prod-txn-detailed-status-update-topic"

WorkflowProcessingPublisher:
  QueueName: "prod-order-workflow-processing-queue"

SavingsLedgerReconPublisher:
  QueueName: "prod-order-savings-ledger-recon-queue"

OrderNotificationPublisher:
  QueueName: "prod-order-update-order-notification-consumer-queue"

OrderCollectNotificationPublisher:
  QueueName: "prod-order-collect-notification-consumer-queue"

OrderNotificationFallbackPublisher:
  QueueName: "prod-order-notification-fallback-consumer-queue"

OrderSearchPublisher:
  QueueName: "prod-order-update-indexing-consumer-queue"

IntraBankEnquiryPublisher:
  QueueName: "prod-payment-intrabank-enquiry-queue"

UPIEnquiryPublisher:
  QueueName: "prod-payment-upi-enquiry-queue"

IMPSEnquiryPublisher:
  QueueName: "prod-payment-imps-enquiry-queue"

NEFTEnquiryPublisher:
  QueueName: "prod-payment-neft-enquiry-queue"

RTGSEnquiryPublisher:
  QueueName: "prod-payment-rtgs-enquiry-queue"

AaAccountPiPurgePublisher:
  QueueName: "prod-aa-account-pi-purge-queue"

ActorPiRelationPurgePublisher:
  QueueName: "prod-actor-pi-purge-queue"

AaDataPurgeOrchestrationPublisher:
  QueueName: "prod-order-aa-data-purging-orchestration-queue"

EventsCompletedTnCPublisher:
  QueueName: "prod-event-completed-tnc-queue"

SignalWorkflowPublisher:
  QueueName: "prod-celestial-signal-workflow-queue"

TxnNotificationPublisher:
  QueueName: "prod-transaction-notification-queue"

InPaymentOrderUpdatePublisher:
  QueueName: "prod-order-in-payment-order-update-queue"

AWS:
  Region: "ap-south-1"

FundTransferWorkflow:
  LowValueTransactionUpperLimit:
    CurrencyCode: "INR"
    Units: 0
    Nanos: 0

CollectShortCircuitWorkflow:
  LowValueTransactionUpperLimit:
    CurrencyCode: "INR"
    Units: 0
    Nanos: 0
  CollectAmountLimit:
    CurrencyCode: "INR"
    Units: 2000
    Nanos: 0
  CollectExpirationDuration: "48h"

Flags:
  TrimDebugMessageFromStatus: false
  EnableBalanceV1Evaluation: false

CustomRuleDEParams:
  IsCustomRuleDERestricted: false

RudderStack:
  IntervalInSec: 10
  BatchSize: 20
  Verbose: false

PaymentProtocolDecisionParams:
  INTRAParams:
    MinAmount: 1
    MaxAmount: 1000000
    TotalTransactedAmountLimitInCoolDown: 10000
    ProfileUpdateCoolDownWindow:
      Value: 24
      TimeUnit: "Hour"
    ProtocolRestrictedWindow:
      IsEnabled: true
      Timezone: "Asia/Kolkata"
      # Should be in YYYY-MM-DD:HH:MM:SS 24-hour format (IST)
      DateTimeFormat: "2006-01-02:15:04:05"
      FromDateTime: "2025-07-29:00:00:00"
      ToDateTime: "2025-07-29:00:30:00"
    IntraCoolOffNotApplicablePis:
      - "paymentinstrument-creditcard-federal-pool-account-1"

  IMPSParams:
    MinAmount: 1
    MaxAmount: 500000
    TotalTransactedAmountLimitInCoolDown: 10000
    ProfileUpdateCoolDownWindow:
      Value: 24
      TimeUnit: "Hour"
    ProtocolRestrictedWindow:
      IsEnabled: true
      Timezone: "Asia/Kolkata"
      # Should be in YYYY-MM-DD:HH:MM:SS 24-hour format (IST)
      DateTimeFormat: "2006-01-02:15:04:05"
      FromDateTime: "2025-07-29:00:00:00"
      ToDateTime: "2025-07-29:00:30:00"
    LimitWindow:
      Value: 24
      TimeUnit: "Hour"
    TransactionCountLimit: 5

  NEFTParams:
    MinAmount: 1
    MaxAmount: 1000000
    TotalTransactedAmountLimitInCoolDown: 10000
    ProfileUpdateCoolDownWindow:
      Value: 24
      TimeUnit: "Hour"
    ProtocolRestrictedWindow:
      IsEnabled: true
      Timezone: "Asia/Kolkata"
      # Should be in YYYY-MM-DD:HH:MM:SS 24-hour format (IST)
      DateTimeFormat: "2006-01-02:15:04:05"
      FromDateTime: "2025-07-29:00:00:00"
      ToDateTime: "2025-07-29:00:30:00"

  RTGSParams:
    MinAmount: 200000
    MaxAmount: 1000000
    TotalTransactedAmountLimitInCoolDown: 10000
    ProfileUpdateCoolDownWindow:
      Value: 24
      TimeUnit: "Hour"
    ProtocolRestrictedWindow:
      IsEnabled: true
      Timezone: "Asia/Kolkata"
      # Should be in YYYY-MM-DD:HH:MM:SS 24-hour format (IST)
      DateTimeFormat: "2006-01-02:15:04:05"
      FromDateTime: "2025-07-29:00:00:00"
      ToDateTime: "2025-07-29:00:30:00"

  UPIParams:
    UpiMinAmount: 1
    UpiMaxAmount: 100000
    UpiTotalTransactedAmountLimit: 1000000
    UpiTransactionCountLimit: 50
    UpiNewUserTotalTransactedAmountLimit: 5000
    UpiCoolDownPeriodTotalTransactedAmountLimit: 10000
    UpiPinResetTotalTransactedAmountLimit: 5000
    UpiLimitWindow:
      Value: 24
      TimeUnit: "Hour"
    UpiPinResetLimitWindow:
      Value: 12
      TimeUnit: "Hour"
    UpiProfileUpdateCoolDownWindow:
      ANDROID:
        Value: 24
        TimeUnit: "Hour"
      IOS:
        Value: 24
        TimeUnit: "Hour"
    UpiProfileUpdateCoolDownTriggerDurationLimit:
      Value: 24
      TimeUnit: "Hour"
    UpiProfileUpdateAfuSummariesFetchDuration:
      Value: 24
      TimeUnit: "Hour"
    UPIRestrictedWindow:
      IsEnabled: true
      Timezone: "Asia/Kolkata"
      # Should be in YYYY-MM-DD:HH:MM:SS 24-hour format (IST)
      DateTimeFormat: "2006-01-02:15:04:05"
      FromDateTime: "2025-07-29:00:00:00"
      ToDateTime: "2025-07-29:00:30:00"

  UpiPreferredPaymentProtocolAmountParams:
    Enable: true
    MinAmount: 1
    MaxAmount: 50000

  UserPaymentParams:
    TotalTransactionAmountLimit:
      AmountLimit: 1000000
      TimeWindow:
        Value: 24
        TimeUnit: "Hour"
    NewAccountTransactionAmountLimit:
      AmountLimit: 500000
      AccountAgeLimitThreshold: "2160h" # 90 days

  # Specific MCC or payment initiation mode or purpose code for which UPI txn limit is different than normal
  # Circular: https://www.npci.org.in/PDF/npci/upi/circular/2020/UPI%20OC%2082%20-%20Implementation%20of%20Rs%20%202%20Lakh%20limit%20per%20transaction%20for%20specific%20categories%20in%20UPI.pdf
  UPILimitExceptions:
    MCC:
      "9311":
        IsLimitOnlyForVerifiedMerchant: true
        Limits:
          MaxAmount: 500000
          MinAmount: 1
      "6211":
        IsLimitOnlyForVerifiedMerchant: true
        Limits:
          MaxAmount: 500000
          MinAmount: 1
      "7322":
        IsLimitOnlyForVerifiedMerchant: true
        Limits:
          MaxAmount: 200000
          MinAmount: 1
      "5960":
        IsLimitOnlyForVerifiedMerchant: true
        Limits:
          MaxAmount: 200000
          MinAmount: 1
      "6300":
        IsLimitOnlyForVerifiedMerchant: true
        Limits:
          MaxAmount: 200000
          MinAmount: 1
      "6529":
        IsLimitOnlyForVerifiedMerchant: true
        Limits:
          MaxAmount: 200000
          MinAmount: 1
      "6540":
        Limits:
          MaxAmount: 5000
          MinAmount: 1
#      todo(82016): confirm these limits with Product / NPCI
#      "4812":
#        Limits:
#          MaxAmount: 5000
#          MinAmount: 1
#      "4814":
#        Limits:
#          MaxAmount: 5000
#          MinAmount: 1
      "8011":
        Limits:
          MaxAmount: 500000
          MinAmount: 1
      "8021":
        Limits:
          MaxAmount: 500000
          MinAmount: 1
      "8031":
        Limits:
          MaxAmount: 500000
          MinAmount: 1
      "8041":
        Limits:
          MaxAmount: 500000
          MinAmount: 1
      "8042":
        Limits:
          MaxAmount: 500000
          MinAmount: 1
      "8049":
        Limits:
          MaxAmount: 500000
          MinAmount: 1
      "8050":
        Limits:
          MaxAmount: 500000
          MinAmount: 1
      "8062":
        Limits:
          MaxAmount: 500000
          MinAmount: 1
      "8071":
        Limits:
          MaxAmount: 500000
          MinAmount: 1
      "8099":
        Limits:
          MaxAmount: 500000
          MinAmount: 1
      "0742":
        Limits:
          MaxAmount: 500000
          MinAmount: 1
      "8211":
        Limits:
          MaxAmount: 500000
          MinAmount: 1
      "8220":
        Limits:
          MaxAmount: 500000
          MinAmount: 1
      "8241":
        Limits:
          MaxAmount: 500000
          MinAmount: 1
      "8244":
        Limits:
          MaxAmount: 500000
          MinAmount: 1
      "8249":
        Limits:
          MaxAmount: 500000
          MinAmount: 1
      "8299":
        Limits:
          MaxAmount: 500000
          MinAmount: 1
      "7409":
        Limits:
          MaxAmount: 25000
          MinAmount: 1
    P2PInitiationMode:
      "04":
        Limits:
          MaxAmount: 0
          MinAmount: 0
      "05":
        Limits:
          MaxAmount: 0
          MinAmount: 0
    P2MInitiationMode:
      "12":
        IsLimitOnlyForVerifiedMerchant: true
        Limits:
          MaxAmount: 200000
          MinAmount: 1
    PurposeCode:
      "15":
        IsLimitOnlyForVerifiedMerchant: true
        Limits:
          MaxAmount: 200000
          MinAmount: 1
      # defaultPurposeUpiLiteEnablement
      "41":
        IsLimitOnlyForVerifiedMerchant: false
        Limits:
          MaxAmount: 2000
          MinAmount: 1
      # defaultPurposeUpiLiteTopUp
      "42":
        IsLimitOnlyForVerifiedMerchant: false
        Limits:
          MaxAmount: 2000
          MinAmount: 1
      # defaultPurposeUpiLiteDisablement
      "43":
        IsLimitOnlyForVerifiedMerchant: false
        Limits:
          MaxAmount: 2000
          MinAmount: 1
      # upi lite payment
      "44":
          IsLimitOnlyForVerifiedMerchant: false
          Limits:
            MaxAmount: 500
            MinAmount: 1

  TotalTransactedAmountLimitInCoolDown: 10000
  ProfileUpdateCoolDownWindow:
    Value: 24
    TimeUnit: "Hour"

RuleEngineParams:
  SalienceINTRA: 14
  SalienceIMPS: 9
  SalienceUPI: 8
  SalienceRTGS: 7
  SalienceNEFT: 6
  BumpedSalience: 20
  # It will be used to bump UPI transaction. Whenever UPI need to bumped for certain range of amount, it will be increased.
  SecondHighestBumpedSalience: 8

Secrets:
  Ids:
    RudderWriteKey: "prod/rudder/internal-writekey"
    GoogleCloudProfilingServiceAccountKey: "prod/gcloud/profiling-service-account-key"
    EventSalt: "prod/rudder/salt"
    # This is the private key and passphrase used before 22nd June 2023. Putting it here
    # since federal did not do key rotation for sftp events. So temporarily doing this here
    # will revert once key rotation is done by federal
    EpifiFederalPgpPrivateKey: "prod/pgp/pgp-epifi-fed-api-private-key-copy"
    EpifiFederalPgpPassphrase: "prod/pgp/pgp-epifi-fed-api-password-copy"
    FederalPgpPublicKey: "prod/pgp/federal-pgp-pub-key-for-epifi"
    EncryptorData: "prod/order/aes/txn-monitoring-aes-encryption-data"

# Time should be in HH:MM 24-hour format in IST Time zone
# It is quick fix for CSIS time and will be removed when CSIS service is up.
CSIS:
  IsCsisEnable: false
  StartTime: "00:30"
  EndTime: "04:30"

# Time should be in HH:MM 24-hour format
# It is quick fix for BankDownTime and will be removed when CSIS service is up.
BankDownTime:
  IsBankDownTimeEnable: false
  StartTime: "23:30"
  EndTime: "04:00"

AddFundsAlertMailingParams:
  IsEnabled: true
  TimestampFormat: "January 2, 2006 15:04:05"
  FromAddress: "<EMAIL>"
  ToAddress: "<EMAIL>"
  FromName: "Epifi"
  ToName: "Add Fund Team"

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "redis-10653.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:10653"
  AuthDetails:
    SecretPath: "prod/redis/common/prefixaccess"
    Environment: "prod"
    Region: "ap-south-1"
  ClientName: order

SMSTypeToOptionVersionMap:
  GenericPiCredit:
    Default: "V2"
    InternalUser: "V2"
  GenericPiDebit:
    Default: "V2"
    InternalUser: "V2"
  UpiDebit:
    Default: "V2"
    InternalUser: "V2"
  UpiCredit:
    Default: "V2"
    InternalUser: "V2"
  GenericPiDebitUnclearBeneficiaryDetails:
    Default: "V1"
  CashWithdrawalAtmFallback:
    Default: "V1"
    InternalUser: "V1"
  PosDebitFallback:
    Default: "V1"
    InternalUser: "V1"
  NeftDebitFallback:
    Default: "V1"
    InternalUser: "V1"
  NeftCreditFallback:
    Default: "V1"
    InternalUser: "V1"
  RtgsDebitFallback:
    Default: "V1"
    InternalUser: "V1"
  InterestPaidInSbFallback:
    Default: "V1"
    InternalUser: "V1"

DynamicVPAParamsMap:
  FEDERAL_BANK:
    AccountNumber: "**************"
    Name: "Fi - Federal Bank"
    Ifsc: "FDRL0005555"
    AccountReferenceNumber: "rRQXWpBnsS0JPK0VLkxmF14hC+DjAhMt44ALOkWYT48="
    DynamicVPAString: "addfi.%s@fbl"
    MerchantDetails:
      MerchantID: "MerchantID"
      MerchantStoreID: "MerchantStoreID"
      MerchantTerminalId: "MerchantTerminalId"
      Genre: 1
      BrandName: "Fi - Federal Bank"
      LegalName: "Fi - Federal Bank"
      FranchiseName: "Fi - Federal Bank"
      OwnerShipType: 4
      OnboardingType: 1
      MerchantType: 2
      SubCode: "7409"
      Mcc: "7409"

DynamicVPAV1ParamsMap:
  FEDERAL_BANK:
    AccountNumber: "**************"
    Name: "Fi - Federal Bank"
    Ifsc: "FDRL0005555"
    AccountReferenceNumber: "rRQXWpBnsS0JPK0VLkxmF14hC+DjAhMt44ALOkWYT48="
    DynamicVPAString: "addfi.5555@fifederal"
    MerchantDetails:
      MerchantID: "MerchantID"
      MerchantStoreID: "MerchantStoreID"
      MerchantTerminalId: "MerchantTerminalId"
      Genre: 1
      BrandName: "Fi - Federal Bank"
      LegalName: "Fi - Federal Bank"
      FranchiseName: "Fi - Federal Bank"
      OwnerShipType: 4
      OnboardingType: 1
      MerchantType: 2
      SubCode: "7409"
      Mcc: "7409"

ConnectedAccountUserGroupParams:
  IsAARestricted: false
  AllowedUserGroupForAA:
    - 1 # INTERNAL
    - 2 # FNF
    - 7 # Connected Account

AaParams:
  # 7 days
  PurgingBatchWindow: "168h"
  # defines the time at which data actual data purging starts in HH:MM format where HH is in 24 hrs format
  DataPurgingStartTime: "20:30"
  # defines the time at which data actual data purging end in HH:MM format where HH is in 24 hrs format
  DataPurgingEndTime: "22:30"

NameCheckParamsForAddFunds:
  IsNameCheckRestricted: false
  AllowedUserGroupForNameCheck:
    - 1 # INTERNAL

DeclineDataAwsSftpBucket:
  AwsBucket: "epifi-prod-federal-debit-card-data"
  AclString: "private"
  SrcFolder: "in_data/decline_data_transfer/"
  DstFolder: "processed"

TxnNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-transaction-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

DeclineCardTransactionsProcessingSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-card-txns-decline-data-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "order-consumer"

HideAccountBalanceInNotification: true

ReconRestrictedWindow:
  IsEnabled: true
  Timezone: "Asia/Kolkata"
  # Should be in YYYY-MM-DD:HH:MM:SS 24-hour format (IST)
  DateTimeFormat: "2006-01-02:15:04:05"
  FromDateTime: "2025-07-29:00:00:00"
  ToDateTime: "2025-07-29:00:30:00"

ReconParams:
  TimeLimitForExhaustedAttempt: 5 # Time limit before attempting reconciliation again (hours)
  IsReconRestricted: false
SavingsLedgerReconCacheConfig:
  IsCachingEnabled: true
  SavingsLedgerReconPrefix: "savings_ledger_recon_account_Id"
  CacheTTl: "30m"

OrderCacheConfig:
  IsCachingEnabled: true
  UseCaseToCacheConfigMap:
    # CacheConfig for RecentActivityCache used in GetRecentActivityOrdersFromCache for storing recent pay activity
    # (RecentPayActivityFromActorIdPrefix, RecentPayActivityToActorIdPrefix are cache prefixes used here).
    - RecentActivityCache:
        IsCachingEnabled: true
        CacheTTL: "48h" # 2 days
    # CacheConfig for OrderCache used in GetById for storing order
    # (OrderKeyPrefix is cache prefix used here).
    - OrderCache:
        IsCachingEnabled: true
        CacheTTL: "2h"
  RedisOptions:
    IsSecureRedis: true
    Options:
      Addr: "redis-15404.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:15404"
    AuthDetails:
      SecretPath: "prod/redis/pay/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
      ClientName: order

TransactionCacheConfig:
  IsCachingEnabled: true
  UseCaseToCacheConfigMap:
    # CacheConfig for TransactionCache used in GetById for storing transaction
    # (TransactionKeyPrefix is cache prefix used here).
    - TransactionCache:
        IsCachingEnabled: true
        CacheTTL: "2h"

Tracing:
  Enable: false

FeatureFlags:
  EnableB2CCallbackSignalToCelestial: true
  EnableRemitterInfo:
    IsRestricted: false
    AllowedUserGroupsForRemitterInfo:
      - 1 # INTERNAL
      - 2 # fnf
  AllowedUserGroupForTimelineOptimization:
  AllowedUserGroupForSuccessfulOrderWithTxnFetchOptimization:
  EnableOptimisationForGetTotalTransactionCountParams:
    IsFeatureRestricted: false
    AllowedUserGroups:
      - 1 # INTERNAL
  EnableFallbackForGetTotalTransactionCountToCrdb:
    IsFeatureRestricted: false
    AllowedUserGroups:
      - 1 # INTERNAL
  EnableOptimisationForGetTotalTransactionAmountParams:
    IsFeatureRestricted: false
    AllowedUserGroups:
      - 1 # INTERNAL
  EnableFallbackForGetTotalTransactionAmountToCrdb:
    IsFeatureRestricted: false
    AllowedUserGroups:
      - 1 # INTERNAL
  EnableTotalAmountUserCheck:
    IsFeatureRestricted: false
    AllowedUserGroups:
      - 1 #internal
  EnableHandleAddFundsWithoutFirstLegTxn: true
  EnableAllTransactionForSelectedOrderStatesParams: # feature flag to enable pending, failed and reversed txns in all txn page
    IsFeatureRestricted: false
  EnableRecentActivityForDifferentTxns:
    IsFeatureRestricted: false
    AllowedUserGroups: [ ]
  DisableCommsForP2PFundTransfer: true
  EnablePGDBDaoForAA: true
  EnableDbOptimisationForAllTransactionsFlow:
    IsFeatureRestricted: true
    AllowedUserGroups:
      - 1 #internal
  EnableParameterizedTimelineEvents:
    IsFeatureRestricted: true
    AllowedUserGroups:
      - 1 #internal
  EnableOnAppEnachExecutionInboundNotifProcessor: true
  BypassTPAPCoolDown:
    IsFeatureRestricted: false
    AllowedUserGroups:
      - 1 #internal
  EnableAllTransactionsForNoOpFailures:
    IsFeatureRestricted: true
    AllowedUserGroups:
      - 1 #internal
  EnableOffAppUpiFlow: false
  RedirectToAmountScreenFromCollectPN: false


Events:
  IncomingCreditMaxPublishDelay: "180m"

RemitterInfoSyncDelayThreshold: "60s"

OrderVpaVerificationPublisher:
  QueueName: "prod-unverified-vpa-queue"

Profiling:
  StackDriverProfiling:
    ProjectId: "epifi-prod"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-prod-automatic-profiling"
    CPUPercentageLimit: 80
    MemoryPercentageLimit: 80
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true


P2PInvestmentWorkflow:
  Payment:
    RetryStrategy:
      Hybrid: # Exponential backoff is followed for first ~68 min post that regular interval is followed for next 5 days with interval of 2 hours.
        RetryStrategy1:
          ExponentialBackOff:
            BaseInterval: 2
            MaxAttempts: 12
            TimeUnit: "Second"
        RetryStrategy2:
          RegularInterval:
            Interval: 2
            MaxAttempts: 60
            TimeUnit: "Hour"
        MaxAttempts: 72
        CutOff: 12
    Method: "/p2pinvestment.P2pInvestment/PaymentWrapper"
    ServiceName: "p2pinvestment"
  Settlement:
    RetryStrategy:
      Hybrid: # first 2 attempt at interval of 24 hour for two days, post that regular interval of 2 hour is followed for next 6 days
        RetryStrategy1:
          RegularInterval:
            Interval: 8
            MaxAttempts: 4
            TimeUnit: "Hour"
        RetryStrategy2:
          RegularInterval:
            Interval: 4
            MaxAttempts: 36
            TimeUnit: "Hour"
        MaxAttempts: 40
        CutOff: 30
    Method: "/p2pinvestment.P2pInvestment/SettleInvestment"
    ServiceName: "p2pinvestment"
  IsAutoTriggered: false

VerifyVpaDeadlineForInboundNotification: "2s"

InboundNotificationParams:
  BalanceRefreshDelay: "2h"

PaymentNotificationParams:
  SkipMFOrders: false

DeemedTransactionUPIEnquiryPublisher:
  QueueName: "prod-deemed-payment-upi-enquiry-queue"

DeemedTransactionUpiEnquirySubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-deemed-payment-upi-enquiry-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 6
      MaxAttempts: 22
      TimeUnit: "Hour"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 15
        Period: 1s
    Namespace: "order:upi"

NonTpapPspHandles: [ "fbl" ]

FeatureReleaseConfig:
  FeatureConstraints:
    TXN_RECEIPT_SCREEN_V1:
      AppVersionConstraintConfig:
        MinAndroidVersion: 267
        MinIOSVersion: 362
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1
    HEALTH_ENGINE_FOR_PAYMENTS:
      AppVersionConstraintConfig:
        MinAndroidVersion: 250
        MinIOSVersion: 345
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FETCH_ORDER_WITH_TRANSACTIONS_USING_IN_MEMORY_JOIN:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
    QUICK_RECAT:
      AppVersionConstraintConfig:
        MinAndroidVersion: 309
        MinIOSVersion: 440
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1
    FEATURE_REMITTER_INFO_BACKFILL:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups: [ ]
    IMPS_DEEMED_HANDLING:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
    HEALTH_ENGINE_FOR_INTRA_PAYMENTS:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    HEALTH_ENGINE_FOR_NEFT_PAYMENTS:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    HEALTH_ENGINE_FOR_IMPS_PAYMENTS:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    HEALTH_ENGINE_FOR_RTGS_PAYMENTS:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    SORT_RECENT_ACTIVITIES_ON_CREATED_AT:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups: [ ]
    BENEFICIARY_COOL_DOWN_RULE:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_OFF_APP_UPI_VIA_TEMPORAL:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 37 # PAY_EXPERIMENTAL
          - 1 # INTERNAL
    FEATURE_OFF_APP_UPI_PREEMPT:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 37 # PAY_EXPERIMENTAL
    REPORT_FRAUD_ORDER_RECEIPT:
      AppVersionConstraintConfig:
        MinAndroidVersion: 333
        MinIOSVersion: 487
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
    FEATURE_AMOUNT_SCREEN_IN_COLLECT_PN:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 517
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
    FEATURE_ENABLE_ACCOUNT_OPERATIONAL_STATUS_CHECK:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_ASSOCIATED_TRANSACTION_ORDER_RECEIPT:
      AppVersionConstraintConfig:
        MinAndroidVersion: 389
        MinIOSVersion: 542
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_DAILY_ON_APP_TRANSACTION_LIMIT:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 37 # PAY_EXPERIMENTAL


# MaximumPauseTimeForRemitterInfoWorkflow is in minutes
MaximumPauseTimeForRemitterInfoWorkflow: 240

QuestSdk:
  Disable: true

QuestRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "redis-12231.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:12231"
    Password: ""
  AuthDetails:
    SecretPath: "prod/redis/growth-infra/prefixaccess"
    Environment: "prod"
    Region: "ap-south-1"
  ClientName: order-quest-sdk
  HystrixCommand:
    CommandName: "quest_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 10000
      ExecutionTimeout: 1s
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 50
      SleepWindow: 15s
      FallbackMaxConcurrency: 10000

PaymentEnquiryParams:
  NotFoundMaxRetryDurationVendorMap:
    "FEDERAL_BANK":
      IntraBank: "24h"
      # We are currently enquiring at 90s and then 2h90s and then 4h90s
      # So we can mark Txn as Failed if 2nd enquiry gave U48 [a.k.a NotFound], hence we are keeping this less than 2h.
      UPI: "110m"
      NEFT: "24h"
      RTGS: "24h"
      IMPS: "24h"
  InProgressToSuccessMap: # time duration and error codes for in progress response status will be move to success
    "FEDERAL_BANK":
      FiErrorCodes:
        - "FI304"
      PaymentProtocolToDurationMap:
        "NEFT": "120m"
        "RTGS": "120m"
      PaymentProtocolToDeemedEnquiryDurationMap:
        UPI:
          # NPCI has started returning U48 within 110h of enquiry. So, we can consider the DEEMED payment as success after 105h.
          # We are changing it to 105h from 120h to ensures consistent behavior between on-app and off-app transactions
          P2P: 105h
          P2M: 105h
        IMPS:
          P2P: 105h
          P2M: 105h
      OffAppPaymentProtocolToDeadlineExceededDuration:
        UPI: 120h
      OffAppPaymentProtocolToNotFoundDuration:
        UPI: 130h
  PaymentProcessingSLAVendorMap:
    "FEDERAL_BANK":
      IntraProcessingTime: "20s"
      RtgsProcessingTime: "10s"
      ImpsProcessingTime: "30s"
      # We are increasing first Enquiry time duration to 90 seconds post txn initiation as per NPCI Circular.
      # Ref - https://drive.google.com/file/d/1RuIRq2B8GXsHKPGUIhZ2jAsSQ3Wm7SNM/view
      UpiProcessingTime: "90s"
      NeftProcessingTime: "30m"

IncidentManagerParams:
  DebitedTransactionThresholdBreachDuration: 5m

ProcrastinatorWorkflowPublisher:
  QueueName: "prod-celestial-initiate-procrastinator-workflow-queue"

CommsConfig:
  # ExcludeUsersForEmail will be used to exclude some user from getting email
  # If the value corresponding to the user id is marked as true then that user will be excluded from getting email
  ExcludeUsersForEmail:
    "4272db89-e5f3-4a09-9cc3-3c4710c3e896": true

ReportFraudConfig:
  TransactionProtocolToMaxDaysAvailableForReportFraud:
    CARD: 30.0
    IMPS: 60.0
    UPI: 60.0

RewardsInfo:
  DpandaFiStoreOrderHistoryTargetUrl: "app.dpanda.in/?publisher_eid=bGZYYnRUdys3UXZBVzBmdkVKMyt4Zz09&user_identifier=%s"
  PoshvineGiftCardStoreOrderHistoryTargetUrl: "fimoney.poshvine.com/order-history?pageType=GiftCardBooking"

# This configuration outlines different transaction scenarios and their corresponding story details.
# Priority depends on the ordering of different cases.
# Priority can be given to more specific cases of transaction attributes over more generic ones.
TransactionAttributesBasedStoryDetailsCombinations:
  - IsEnabled: true
    IsMoneyDebited: true
    CurrentTransactionStatus: "FAILED"
    DetailedApiStatus: "FAILURE"
    StatusCodePayer: "UPI1204"
    StatusCodePayee: "UPI825"
    StoryDetails:
      Description: "Money deducted but transaction Failed?"
      Title: "Money deducted but transaction Failed?"
      Url: "https://storifyme.com/stories/g-prateek-gauba-22176/91980/preview"
      Id: "PAYMENT_FAILED_UPI1204_UPI825"
      LeftIconUrl: "https://epifi-icons.pointz.in/faq-story/failedtxn"
  - IsEnabled: true
    IsMoneyDebited: true
    CurrentTransactionStatus: "FAILED"
    DetailedApiStatus: "FAILURE"
    StatusCodePayer: "UPI171"
    StatusCodePayee: "UPI784"
    StoryDetails:
      Description: "Money deducted but transaction Failed?"
      Title: "Money deducted but transaction Failed?"
      Url: "https://storifyme.com/stories/g-prateek-gauba-22176/91980/preview"
      Id: "PAYMENT_FAILED_UPI171_UPI784"
      LeftIconUrl: "https://epifi-icons.pointz.in/faq-story/failedtxn"
  - IsEnabled: true
    IsMoneyDebited: true
    CurrentTransactionStatus: "FAILED"
    DetailedApiStatus: "FAILURE"
    StatusCodePayer: "UPI784"
    StatusCodePayee: "UPI784"
    StoryDetails:
      Description: "Money deducted but transaction Failed?"
      Title: "Money deducted but transaction Failed?"
      Url: "https://storifyme.com/stories/g-prateek-gauba-22176/91980/preview"
      Id: "PAYMENT_FAILED_UPI784_UPI784"
      LeftIconUrl: "https://epifi-icons.pointz.in/faq-story/failedtxn"
  - IsEnabled: true
    IsMoneyDebited: true
    CurrentTransactionStatus: "FAILED"
    DetailedApiStatus: "FAILURE"
    StatusCodePayer: "UPI_SUCCESS"
    StatusCodePayee: "UPI_SUCCESS"
    StoryDetails:
      Description: "Money deducted but transaction Failed?"
      Title: "Money deducted but transaction Failed?"
      Url: "https://storifyme.com/stories/g-prateek-gauba-22176/91980/preview"
      Id: "PAYMENT_FAILED_UPI_SUCCESS_UPI_SUCCESS"
      LeftIconUrl: "https://epifi-icons.pointz.in/faq-story/failedtxn"
  - IsEnabled: true
    IsMoneyDebited: true
    CurrentTransactionStatus: "IN_PROGRESS"
    DetailedApiStatus: "DEEMED"
    StatusCodePayer: "*"
    StatusCodePayee: "*"
    StoryDetails:
      Description: "Money not credited to beneficiary?"
      Title: "Money not credited to beneficiary?"
      Url: "https://storifyme.com/stories/g-prateek-gauba-22176/31055/preview"
      Id: "PAYMENT_DEEMED"
      LeftIconUrl: "https://epifi-icons.pointz.in/faq-story/deemedtxn"

EnableEntitySegregation: true

OrderReceipt:
  OrderReceiptProtocolLevelBanner:
    ChequeTxnInfoBanner:
      # Time window for which OrderReceiptProtocolLevelBanner is displayed on screen when payment is success or failed.
      Window: "24h"

PinotQueryTimeout: "5s"
