// nolint:ineffassign,funlen
package notification

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/epifi/gamma/order/notification/comms/email_rules"
	"github.com/epifi/gamma/order/notification/comms/notification_rules"
	"github.com/epifi/gamma/order/notification/comms/sms_rules"

	staticconfig "github.com/epifi/gamma/order/config"

	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/golang/protobuf/ptypes"
	"github.com/golang/protobuf/ptypes/timestamp"
	"github.com/samber/lo"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
	gormv2 "gorm.io/gorm"

	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/mask"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/names"
	"github.com/epifi/be-common/pkg/queue"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/gamma/api/accounts"
	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	"github.com/epifi/gamma/api/accounts/balance/enums"
	actorPb "github.com/epifi/gamma/api/actor"
	cpPb "github.com/epifi/gamma/api/card/provisioning"
	commsPb "github.com/epifi/gamma/api/comms"
	depositPb "github.com/epifi/gamma/api/deposit"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	deeplinkTimelinePb "github.com/epifi/gamma/api/frontend/deeplink/timeline"
	"github.com/epifi/gamma/api/frontend/fcm"
	notificationPb "github.com/epifi/gamma/api/frontend/fcm"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/order/payment/notification"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/api/paymentinstrument/account_pi"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/api/savings"
	tieringPb "github.com/epifi/gamma/api/tiering"
	timelinePb "github.com/epifi/gamma/api/timeline"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/ui"
	config "github.com/epifi/gamma/order/config/genconf"
	"github.com/epifi/gamma/order/dao"
	"github.com/epifi/gamma/order/internal/actor"
	upiProcessor "github.com/epifi/gamma/order/internal/upi"
	userProcessor "github.com/epifi/gamma/order/internal/user"
	"github.com/epifi/gamma/order/notification/comms"
	orderTypes "github.com/epifi/gamma/order/types"
	"github.com/epifi/gamma/pkg/pay"
	upiPkg "github.com/epifi/gamma/pkg/upi"
	"github.com/epifi/gamma/pkg/vendors/federal"
)

type transactionDetails struct {
	id                      string
	orderId                 string
	fromAccNo               string
	fromActorName           string
	fromUpiVpa              string
	toAccNo                 string
	toActorName             string
	toUpiVpa                string
	amount                  *moneyPb.Money
	remarks                 string
	payerBalanceAmount      *moneyPb.Money
	payeeBalanceAmount      *moneyPb.Money
	date                    string
	utr                     string
	timelineId              string
	orderStatus             orderPb.OrderStatus
	protocol                paymentPb.PaymentProtocol
	expireAt                *timestamp.Timestamp
	eventTime               *timestamp.Timestamp
	debitedAt               *timestamp.Timestamp
	creditedAt              *timestamp.Timestamp
	txnLastUpdatedAt        *timestamp.Timestamp
	toActorId               string
	toActorProfileImgURL    string
	isDepositTransaction    bool
	isFitTransaction        bool
	savingAccountUserType   userType
	depositType             string
	depositName             string
	isPreClosed             bool
	isAutoClosed            bool
	fromPiType              piPb.PaymentInstrumentType
	toPiType                piPb.PaymentInstrumentType
	orderWorkflow           orderPb.OrderWorkflow
	isPayeeExternal         bool
	isPayerExternal         bool
	depositAccountId        string
	isCardTransaction       bool
	isInterestTransaction   bool
	provenance              orderPb.OrderProvenance
	orderTags               []orderPb.OrderTag
	isTodChargeRelated      bool
	IsOtherBankAtmTxnCharge bool
	IsAtmDeclineFee         bool
	IsEcsReturnCharges      bool
	IsDuplicateCardFee      bool
	IsAtmPenalty            bool
	IsIntATMcharges         bool
	isMutualFundTransaction bool
	IsEcomPosDeclineFee     bool
	IsDebitCardAmcCharge    bool
}

func (t *transactionDetails) getExecutionTs() *timestamppb.Timestamp {
	if t == nil {
		return nil
	}

	switch {
	case t.creditedAt != nil:
		return t.creditedAt
	case t.debitedAt != nil:
		return t.debitedAt
	default:
		return t.eventTime
	}
}

type userType int
type modes map[orderPb.OrderStatus]map[userType][]commsPb.Medium

const (
	PAYER                              userType = 0
	PAYEE                              userType = 1
	FromEmailId                                 = "<EMAIL>"
	FromEmailName                               = "Fi Money"
	IS_DEPOSIT_TXN                              = "isDepositTransaction"
	USER                                        = "user"
	genericPiAccNoPlaceholder                   = "your beneficiary"
	alertString                                 = "ALERT: Unable to send notification. Mandatory params missing or comms service unavailable."
	version1                                    = "V1"
	version2                                    = "V2"
	dismissibleNotificationRefIdSuffix          = "_DISMISSIBLE"
)

var (
	// notification modes for bank transfer protocols
	btNotificationModes = modes{
		// for paid it's set to notification just for testing.
		orderPb.OrderStatus_PAID: {
			PAYER: {commsPb.Medium_SMS, commsPb.Medium_NOTIFICATION},
			PAYEE: {commsPb.Medium_SMS, commsPb.Medium_NOTIFICATION},
		},
		orderPb.OrderStatus_IN_PAYMENT: {
			PAYER: {commsPb.Medium_SMS, commsPb.Medium_NOTIFICATION},
			PAYEE: {commsPb.Medium_SMS, commsPb.Medium_NOTIFICATION},
		},
		orderPb.OrderStatus_PAYMENT_FAILED: {
			PAYER: {commsPb.Medium_NOTIFICATION},
		},
		orderPb.OrderStatus_COLLECT_REGISTERED: {
			PAYER: {commsPb.Medium_SMS, commsPb.Medium_NOTIFICATION},
		},
		orderPb.OrderStatus_COLLECT_DISMISSED_BY_PAYEE: {
			PAYER: {commsPb.Medium_NOTIFICATION},
		},
		orderPb.OrderStatus_FULFILLED: {
			PAYER: {commsPb.Medium_SMS, commsPb.Medium_NOTIFICATION},
			PAYEE: {commsPb.Medium_SMS, commsPb.Medium_NOTIFICATION},
		},
		orderPb.OrderStatus_FULFILLMENT_FAILED: {
			PAYER: {commsPb.Medium_NOTIFICATION},
		},
		orderPb.OrderStatus_SETTLED: {
			PAYEE: {commsPb.Medium_SMS, commsPb.Medium_NOTIFICATION},
		},
		orderPb.OrderStatus_SETTLEMENT_FAILED: {
			PAYEE: {commsPb.Medium_NOTIFICATION},
		},
		orderPb.OrderStatus_PAYMENT_REVERSED: {
			PAYER: {commsPb.Medium_SMS, commsPb.Medium_NOTIFICATION},
		},
	}
	// notification modes for UPI protocol
	upiNotificationModes = modes{
		orderPb.OrderStatus_PAID: {
			PAYER: {commsPb.Medium_SMS, commsPb.Medium_NOTIFICATION},
			PAYEE: {commsPb.Medium_SMS, commsPb.Medium_NOTIFICATION},
		},
		orderPb.OrderStatus_IN_PAYMENT: {
			PAYER: {commsPb.Medium_SMS, commsPb.Medium_NOTIFICATION},
			PAYEE: {commsPb.Medium_SMS, commsPb.Medium_NOTIFICATION},
		},
		orderPb.OrderStatus_PAYMENT_FAILED: {
			PAYER: {commsPb.Medium_SMS, commsPb.Medium_NOTIFICATION},
		},
		orderPb.OrderStatus_COLLECT_REGISTERED: {
			PAYER: {commsPb.Medium_SMS, commsPb.Medium_NOTIFICATION},
		},
		orderPb.OrderStatus_COLLECT_DISMISSED_BY_PAYEE: {
			PAYER: {commsPb.Medium_NOTIFICATION},
		},
		orderPb.OrderStatus_FULFILLMENT_FAILED: {
			PAYER: {commsPb.Medium_NOTIFICATION},
		},
		orderPb.OrderStatus_PAYMENT_REVERSED: {
			PAYER: {commsPb.Medium_SMS, commsPb.Medium_NOTIFICATION},
		},
	}
	chequeNotificationModes = modes{
		orderPb.OrderStatus_PAID: {
			PAYER: {commsPb.Medium_SMS, commsPb.Medium_NOTIFICATION},
			PAYEE: {commsPb.Medium_SMS, commsPb.Medium_NOTIFICATION},
		},
		orderPb.OrderStatus_PAYMENT_FAILED: {
			PAYER: {commsPb.Medium_SMS, commsPb.Medium_NOTIFICATION},
		},
	}
	// error if notification template is not found for an order status and payment protocol.
	errorTemplateNotFound       = errors.New("template not found")
	errorTemplateDisabled       = errors.New("template disabled")
	errorAccountBalanceDisabled = errors.New("showing Account Balance is disabled")

	// error if SMS template is triggered before triggerAfter property duration
	errorSmsBeforeTriggerAfter = errors.New("SMS can't be triggered before TriggerAfter property")
	// error if push notification template is triggered before triggerAfter property duration
	errorPnBeforeTriggerAfter = errors.New("push notification can't be triggered before TriggerAfter property")
	// denotes the case when a PN is triggered for a debit transaction, due to a refund on the original debit transaction.
	errorPnTriggeredForDebitTxnDueToRefund = errors.New("skipping push notification for debit transaction as it is triggered due to a reversal")

	depositTypeMap = map[accounts.Type]string{
		accounts.Type_FIXED_DEPOSIT:     "Fixed",
		accounts.Type_RECURRING_DEPOSIT: "Recurring",
		accounts.Type_SMART_DEPOSIT:     "Smart",
	}

	alertCutoff        = time.Minute
	smsTypeToConfigMap map[commsPb.SmsType]*staticconfig.SMSOptionVersion

	pushNotificationRules []comms.PushNotificationRule
	smsRules              []comms.SmsRule
	emailRules            []comms.EmailRule

	orderStatusEligibleForHighPriorityNotifications = map[orderPb.OrderStatus]struct{}{
		orderPb.OrderStatus_COLLECT_IN_PROGRESS:        {},
		orderPb.OrderStatus_COLLECT_DISMISSED_BY_PAYER: {},
		orderPb.OrderStatus_COLLECT_DISMISSED_BY_PAYEE: {},
		orderPb.OrderStatus_COLLECT_REGISTERED:         {},
		orderPb.OrderStatus_COLLECT_FAILED:             {},
	}

	orderWorkFlowEligibleForHighPriorityNotifications = map[orderPb.OrderWorkflow]struct{}{
		orderPb.OrderWorkflow_P2P_COLLECT:                         {},
		orderPb.OrderWorkflow_P2P_COLLECT_SHORT_CIRCUIT:           {},
		orderPb.OrderWorkflow_ADD_FUNDS_COLLECT:                   {},
		orderPb.OrderWorkflow_COLLECT_ONE_TIME_RECURRING_PAYMENT:  {},
		orderPb.OrderWorkflow_COLLECT_RECURRING_PAYMENT_WITH_AUTH: {},
		orderPb.OrderWorkflow_COLLECT_RECURRING_PAYMENT_NO_AUTH:   {},
		orderPb.OrderWorkflow_ADD_FUNDS:                           {},
	}
)

type Service struct {
	// UnimplementedNotificationConsumerServer is embedded to have forward compatible implementations
	orderPb.UnimplementedNotificationConsumerServer

	commsClient                        commsPb.CommsClient
	actorClient                        actorPb.ActorClient
	piClient                           piPb.PiClient
	timelineClient                     timelinePb.TimelineServiceClient
	notificationParams                 *config.PaymentNotificationParams
	smsParams                          *config.PaymentSMSParams
	depositClient                      depositPb.DepositClient
	savingsClient                      savings.SavingsClient
	transactionNotificationMapDao      dao.TransactionNotificationMapDao
	accountPiRelationClient            account_pi.AccountPIRelationClient
	userProcessor                      userProcessor.UserProcessor
	orderCollectNotificationPublisher  orderTypes.OrderCollectNotificationPublisher
	orderNotificationFallbackPublisher orderTypes.OrderNotificationFallbackPublisher
	recurringPaymentClient             rpPb.RecurringPaymentServiceClient
	actorProcessor                     actor.ActorProcessor
	conf                               *config.Config
	orderDao                           dao.OrderDao
	paySavingsBalanceClient            accountBalancePb.BalanceClient
	upiProcessor                       upiProcessor.UpiProcessor
	cpClient                           cpPb.CardProvisioningClient
	tieringClient                      tieringPb.TieringClient
}

// Factory method for creating an instance of consumer service. This method will be used by the injector
// when providing the dependencies at initialization time.
func NewService(
	commsClient commsPb.CommsClient,
	actorClient actorPb.ActorClient,
	piClient piPb.PiClient,
	timelineClient timelinePb.TimelineServiceClient,
	conf *config.Config,
	depositClient depositPb.DepositClient,
	savingsClient savings.SavingsClient,
	accountPiRelationClient account_pi.AccountPIRelationClient,
	transactionNotificationMapDao dao.TransactionNotificationMapDao,
	userProcessor userProcessor.UserProcessor,
	orderCollectNotificationPublisher orderTypes.OrderCollectNotificationPublisher,
	orderNotificationFallbackPublisher orderTypes.OrderNotificationFallbackPublisher,
	recurringPaymentClient rpPb.RecurringPaymentServiceClient,
	actorProcessorClient actor.ActorProcessor,
	orderDao dao.OrderDao,
	paySavingsBalanceClient accountBalancePb.BalanceClient,
	upiProcessor upiProcessor.UpiProcessor,
	cpClient cpPb.CardProvisioningClient,
	tieringClient tieringPb.TieringClient,
) *Service {
	initSmsOptionMap(conf)
	return &Service{
		commsClient:                        commsClient,
		actorClient:                        actorClient,
		piClient:                           piClient,
		timelineClient:                     timelineClient,
		notificationParams:                 conf.PaymentNotificationParams(),
		smsParams:                          conf.PaymentSMSParams(),
		depositClient:                      depositClient,
		savingsClient:                      savingsClient,
		transactionNotificationMapDao:      transactionNotificationMapDao,
		accountPiRelationClient:            accountPiRelationClient,
		userProcessor:                      userProcessor,
		orderCollectNotificationPublisher:  orderCollectNotificationPublisher,
		orderNotificationFallbackPublisher: orderNotificationFallbackPublisher,
		recurringPaymentClient:             recurringPaymentClient,
		actorProcessor:                     actorProcessorClient,
		conf:                               conf,
		orderDao:                           orderDao,
		paySavingsBalanceClient:            paySavingsBalanceClient,
		upiProcessor:                       upiProcessor,
		cpClient:                           cpClient,
		tieringClient:                      tieringClient,
	}
}

func initSmsOptionMap(conf *config.Config) {
	smsTypeToConfigMap = map[commsPb.SmsType]*staticconfig.SMSOptionVersion{
		commsPb.SmsType_GENERIC_PI_CREDIT:                           conf.SMSTypeToOptionVersionMap().Get("GenericPiCredit"),
		commsPb.SmsType_GENERIC_PI_DEBIT:                            conf.SMSTypeToOptionVersionMap().Get("GenericPiDebit"),
		commsPb.SmsType_UPI_DEBIT:                                   conf.SMSTypeToOptionVersionMap().Get("UpiDebit"),
		commsPb.SmsType_UPI_CREDIT:                                  conf.SMSTypeToOptionVersionMap().Get("UpiCredit"),
		commsPb.SmsType_GENERIC_PI_DEBIT_UNCLEAR_BENEFICARY_DETAILS: conf.SMSTypeToOptionVersionMap().Get("GenericPiDebitUnclearBeneficiaryDetails"),
		commsPb.SmsType_CASH_WITHDRAWAL_ATM_FALLBACK:                conf.SMSTypeToOptionVersionMap().Get("CashWithdrawalAtmFallback"),
		commsPb.SmsType_POS_DEBIT_FALLBACK:                          conf.SMSTypeToOptionVersionMap().Get("PosDebitFallback"),
		commsPb.SmsType_NEFT_DEBIT_FALLBACK:                         conf.SMSTypeToOptionVersionMap().Get("NeftDebitFallback"),
		commsPb.SmsType_NEFT_CREDIT_FALLBACK:                        conf.SMSTypeToOptionVersionMap().Get("NeftCreditFallback"),
		commsPb.SmsType_RTGS_DEBIT_FALLBACK:                         conf.SMSTypeToOptionVersionMap().Get("RtgsDebitFallback"),
		commsPb.SmsType_INTEREST_PAID_IN_SB_FALLBACK:                conf.SMSTypeToOptionVersionMap().Get("InterestPaidInSbFallback"),
		commsPb.SmsType_TOD_CHARGES_DEBIT:                           conf.SMSTypeToOptionVersionMap().Get("TodChargesDebit"),
		commsPb.SmsType_TRANSACTION_REVERSED:                        conf.SMSTypeToOptionVersionMap().Get("TransactionReversed"),
		commsPb.SmsType_ECS_RETURN_CHARGES:                          conf.SMSTypeToOptionVersionMap().Get("EcsReturnCharges"),
		commsPb.SmsType_OTHER_BANK_ATM_USAGE_CHARGES:                conf.SMSTypeToOptionVersionMap().Get("OtherBankAtmTxnCharge"),
		commsPb.SmsType_ATM_DECLINE_FEES:                            conf.SMSTypeToOptionVersionMap().Get("AtmDeclineFee"),
		commsPb.SmsType_DUPLICATE_CARD_FEE:                          conf.SMSTypeToOptionVersionMap().Get("DuplicateCardFee"),
		commsPb.SmsType_ATM_WITHDRAWAL_COMPLAINT_PENALTY:            conf.SMSTypeToOptionVersionMap().Get("AtmPenalty"),
		commsPb.SmsType_INTERNATIONAL_ATM_CHARGES:                   conf.SMSTypeToOptionVersionMap().Get("IntATMcharges"),
	}
}

// ProcessOrderNotification publishes notification to use case specific queues
func (s *Service) ProcessOrderNotification(ctx context.Context, req *orderPb.OrderUpdate) (
	*orderPb.ProcessOrderNotificationResponse, error) {
	var (
		res = &orderPb.ProcessOrderNotificationResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{},
		}
		err error
	)
	order := req.GetOrderWithTransactions().GetOrder()
	if order == nil {
		logger.Error(ctx, "Received nil order in ProcessOrderNotification")
		res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_PERMANENT_FAILURE
		return res, nil
	}
	_, getByIdErr := s.orderDao.GetById(ctx, req.GetOrderWithTransactions().GetOrder().GetId())
	if getByIdErr != nil {
		if storagev2.IsRecordNotFoundError(getByIdErr) {
			// Case when order update packet is published but order and transaction were not created due to transaction roll-back
			// returning permanent failure since creation of transaction-notification-map will fail in this case
			// notification will be sent to the user when another packet will be received for the same order and transaction(since we have retry logic in the method that is publishing to this consumer)
			res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_PERMANENT_FAILURE
			return res, nil
		}
		// transient failure in case some error occurred while fetching order
		res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE
		return res, nil
	}

	s.initSmsRules()
	s.initPnRules()
	s.initEmailRules()

	// for COLLECT_REGISTERED status, we want to send regular collect notifications for both scenarios
	if (order.GetWorkflow() == orderPb.OrderWorkflow_P2P_COLLECT || order.GetWorkflow() == orderPb.OrderWorkflow_COLLECT_RECURRING_PAYMENT_WITH_AUTH) && order.GetStatus() == orderPb.OrderStatus_COLLECT_REGISTERED {
		_, err = s.orderCollectNotificationPublisher.Publish(ctx, req)
		if err != nil {
			logger.Error(ctx, "failed to publish collect order event to order-collect-notification-queue")
			res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE
			return res, nil
		}
		res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_SUCCESS
		return res, nil
	}

	// skip fallback publisher in this case. recurring workflow listens to order-update publisher
	if order.GetWorkflow().IsRecurringPaymentWorkflow() {
		_, err := s.recurringPaymentClient.ProcessRecurringPaymentsNotification(ctx, req)
		if err != nil {
			logger.Error(ctx, "Failed to process recurringPayment Notifications", zap.Error(err))
			res.ResponseHeader.Status = queue.GetStatusFrom(err)
			return res, nil
		}
		res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_SUCCESS
		return res, nil
	}

	_, err = s.orderNotificationFallbackPublisher.Publish(ctx, req)
	if err != nil {
		logger.Error(ctx, "failed to publish order update event to order-notification-fallback-queue")
		res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE
		return res, nil
	}

	res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_SUCCESS
	return res, nil
}

func (s *Service) getActorDetails(ctx context.Context, actorId string) (*actorPb.GetEntityDetailsByActorIdResponse, error) {
	res, err := s.actorProcessor.GetEntityDetailsByActorId(ctx, actorId)

	if err != nil {
		return nil, err
	}

	return res, nil
}

// notifyUser fetches the template for each medium, and notifies the user with those mediums.
// err is returned only for debit/credit transaction notifications or in case of db call failure, so that retries can happen using queue
// nolint:funlen
func (s *Service) notifyUser(
	ctx context.Context,
	txnDetails *transactionDetails,
	entityId string,
	user userType,
	userGrp []commontypes.UserGroup,
	owt *orderPb.OrderWithTransactions,
) ([]commsPb.SmsType, []*commsPb.NotificationMessage, error) {

	var notificationModes modes
	var sentSms []commsPb.SmsType
	var sentNotifications []*commsPb.NotificationMessage
	if txnDetails.protocol == paymentPb.PaymentProtocol_UPI {
		notificationModes = upiNotificationModes
	} else if txnDetails.protocol == paymentPb.PaymentProtocol_CTS {
		notificationModes = chequeNotificationModes
	} else {
		notificationModes = btNotificationModes
	}

	isFITOrder := txnDetails.isFitTransaction
	isMFOrder := txnDetails.isMutualFundTransaction
	isUpiDebitOrder := txnDetails.protocol == paymentPb.PaymentProtocol_UPI &&
		user == PAYER && (txnDetails.orderStatus == orderPb.OrderStatus_IN_PAYMENT ||
		txnDetails.orderStatus == orderPb.OrderStatus_PAID)
	for _, mode := range notificationModes[txnDetails.orderStatus][user] {
		switch mode { //nolint:exhaustive
		case commsPb.Medium_SMS:
			if (isFITOrder && s.smsParams.SkipFITOrders()) || (isUpiDebitOrder && s.conf.Flags().SkipSmsForUpiDebitTransactions()) {
				logger.Debug(ctx, "not sending sms for FIT/Upi debit orders", zap.String(logger.ORDER_ID, txnDetails.orderId))
				continue
			}
			if txnDetails.getExecutionTs() != nil && time.Since(txnDetails.getExecutionTs().AsTime()) > s.smsParams.HistoricPaymentSupportedTill() {
				logger.Info(ctx, "not sending sms due to historic payment limit", zap.String(logger.ORDER_ID, txnDetails.orderId))
				return nil, nil, nil
			}
			if txnDetails.orderStatus == orderPb.OrderStatus_PAID && user == PAYER && txnDetails.IsDuplicateCardFee {
				logger.Info(ctx, "not sending sms for debit card duplicate card fee txn", zap.String(logger.ORDER_ID, txnDetails.orderId))
				continue
			}

			switch txnDetails.orderStatus {
			// handling orders in PAID, IN_PAYMENT state here
			// using persistence to make sure we dont trigger notification twice for a particular transaction
			// doing this because 'notifyUser' gets triggered twice, once on order update and again on transaction update
			case orderPb.OrderStatus_PAID, orderPb.OrderStatus_IN_PAYMENT, orderPb.OrderStatus_SETTLED:
				txnNotifMap, qErr, err := s.getOrCreateTransactionNotificationMap(ctx, txnDetails.id)
				if err != nil {
					return sentSms, sentNotifications, fmt.Errorf("could not get or create transaction notification mapping: %v: %w", err, qErr)
				}
				switch {
				case (user == PAYEE && txnNotifMap.GetCreditSmsId() == "" && txnNotifMap.GetCommsIdForCreditEmail() == "") || (user == PAYER && txnNotifMap.GetDebitSmsId() == ""):
					smsTemplate, emailTemplate, smsType, qos, err := s.getTemplate(ctx, txnDetails, user, userGrp, owt)
					if err != nil {
						logger.Info(ctx, "No SMS/Email Template found for this case", zap.Error(err))
					} else {
						if emailTemplate.GetFromEmailId() != "" {
							emailMsgId, err := s.sendEmail(ctx, emailTemplate, entityId, txnDetails, qos)
							if err != nil {
								return sentSms, sentNotifications, err
							}
							var updateMask []notification.TransactionNotificationMapFieldMask

							txnNotifMap.CommsIdForCreditEmail = emailMsgId
							updateMask = append(updateMask, notification.TransactionNotificationMapFieldMask_COMMS_ID_FOR_CREDIT_EMAIL)

							txnNotifMapUpdateErr := s.transactionNotificationMapDao.Update(ctx, txnNotifMap, updateMask)
							if txnNotifMapUpdateErr != nil {
								logger.Error(
									ctx,
									"error in updating transaction notification map",
									zap.String(logger.ORDER_ID, txnDetails.orderId),
									zap.String(logger.TXN_ID, txnDetails.id),
									zap.Error(txnNotifMapUpdateErr),
								)
							}

						} else if smsTemplate.GetSmsOption() != nil {
							smsMsgId, err := s.sendSMS(ctx, smsTemplate, entityId, qos, txnDetails)
							logger.Debug(ctx, "sending sms", zap.String(logger.SMS_MSG_ID, smsMsgId), zap.String(logger.ENTITY_ID, entityId), zap.Error(err))

							if err != nil {
								return sentSms, sentNotifications, err
							}
							sentSms = append(sentSms, smsType)
							var updateMask []notification.TransactionNotificationMapFieldMask
							if user == PAYEE {
								txnNotifMap.CreditSmsId = smsMsgId
								updateMask = append(updateMask, notification.TransactionNotificationMapFieldMask_CREDIT_SMS_ID)
							} else if user == PAYER {
								txnNotifMap.DebitSmsId = smsMsgId
								updateMask = append(updateMask, notification.TransactionNotificationMapFieldMask_DEBIT_SMS_ID)
							}
							txnNotifMapUpdateErr := s.transactionNotificationMapDao.Update(ctx, txnNotifMap, updateMask)
							if txnNotifMapUpdateErr != nil {
								logger.Error(
									ctx,
									"error in updating transaction notification map",
									zap.String(logger.ORDER_ID, txnDetails.orderId),
									zap.String(logger.TXN_ID, txnDetails.id),
									zap.Error(txnNotifMapUpdateErr),
								)
							}
						}
					}
				}
			case orderPb.OrderStatus_PAYMENT_REVERSED:
				txnNotifMap, qErr, err := s.getOrCreateTransactionNotificationMap(ctx, txnDetails.id)
				if err != nil {
					return sentSms, sentNotifications, fmt.Errorf("could not get or create transaction notification mapping: %v: %w", err, qErr)
				}
				if txnNotifMap.ReversalSmsId != "" {
					break
				}
				if template, smsType, qos, err := s.getSmsTemplate(ctx, txnDetails, user, userGrp, owt); err != nil {
					logger.Info(ctx, "No SMS Template found for this case", zap.Error(err))
				} else {
					smsMsgId, err := s.sendSMS(ctx, template, entityId, qos, txnDetails)
					logger.Debug(ctx, "sending sms", zap.String(logger.SMS_MSG_ID, smsMsgId), zap.String(logger.ENTITY_ID, entityId), zap.Error(err))

					if err != nil {
						return sentSms, sentNotifications, err
					}
					sentSms = append(sentSms, smsType)
					var updateMask []notification.TransactionNotificationMapFieldMask
					txnNotifMap.ReversalSmsId = smsMsgId
					updateMask = append(updateMask, notification.TransactionNotificationMapFieldMask_REVERSAL_SMS_ID)
					txnNotifMapUpdateErr := s.transactionNotificationMapDao.Update(ctx, txnNotifMap, updateMask)
					if txnNotifMapUpdateErr != nil {
						logger.Error(
							ctx,
							"error in updating transaction notification map",
							zap.String(logger.ORDER_ID, txnDetails.orderId),
							zap.String(logger.TXN_ID, txnDetails.id),
							zap.Error(txnNotifMapUpdateErr),
						)
					}
				}
			default:
				// handling failure/collect cases here. Don't need persistence for the same
				if template, smsType, qos, err := s.getSmsTemplate(ctx, txnDetails, user, userGrp, owt); err != nil {
					logger.Debug(ctx, "Could not find SMS template", zap.Error(err))
				} else {
					smsMsgId, err := s.sendSMS(ctx, template, entityId, qos, txnDetails)
					logger.Debug(ctx, "sending sms", zap.String(logger.SMS_MSG_ID, smsMsgId), zap.String(logger.ENTITY_ID, entityId), zap.Error(err))
					if err != nil {
						return sentSms, sentNotifications, err
					}
					sentSms = append(sentSms, smsType)
				}
			}
			// Handling confirmation cases separately here.
			// Need this because there's no way to differentiate between debit and credit_confirmation
			if txnDetails.orderStatus == orderPb.OrderStatus_PAID && user == PAYER {
				if template, smsType, qos, err := s.getConfirmationSmsTemplate(txnDetails); err != nil {
					logger.Debug(ctx, "Could not find Confirmation SMS template", zap.Error(err))
				} else {
					_, err := s.sendSMS(ctx, template, entityId, qos, txnDetails)
					if err != nil {
						return sentSms, sentNotifications, err
					}
					sentSms = append(sentSms, smsType)
				}
			}
		case commsPb.Medium_EMAIL:
			if template, qos, err := getEmailTemplate(txnDetails, user); err != nil {
				logger.Debug(ctx, "Could not find Email template", zap.Error(err))
			} else {
				s.sendEmail(ctx, template, entityId, txnDetails, qos)
			}
		case commsPb.Medium_NOTIFICATION:
			if (isFITOrder && s.notificationParams.SkipFITOrders()) || (isMFOrder && s.notificationParams.SkipMFOrders()) {
				logger.Debug(ctx, "not sending notification for FIT and MF orders", zap.String(logger.ORDER_ID, txnDetails.orderId))
				continue
			}
			// skip us-stocks dividend credit and dividend debit transactions
			// TODO(Brijesh): Decide notifications to suppress and send from pay-side
			if s.isUSStocksDividendTransaction(txnDetails.orderTags) || s.isUSStocksGstTransaction(txnDetails.orderTags) || s.isUSStocksGstRefundTransaction(txnDetails.orderTags) {
				logger.Debug(ctx, "not sending notification for US Stocks dividend or GST order", zap.String(logger.ORDER_ID, txnDetails.orderId))
				continue
			}
			if txnDetails.getExecutionTs() != nil && time.Since(txnDetails.getExecutionTs().AsTime()) > s.notificationParams.HistoricPaymentSupportedTill() {
				logger.Info(ctx, "not sending notification due to historic payment limit", zap.String(logger.ORDER_ID, txnDetails.orderId))
				return nil, nil, nil
			}
			var (
				txnNotifMap *notification.TransactionNotificationMap
				err         error
				qErr        error // refers to the type of queue Error: transient/permanent
			)
			switch {
			// Currently only ensuring that credit/debit push notification isn't sent more than once. Failure/Collect needs to be handled later.
			// handling orders in PAID, IN_PAYMENT state here
			// using persistence to make sure we dont trigger notification twice for a particular transaction
			// doing this because 'notifyUser' gets triggered twice, once on order update and again on transaction update
			case txnDetails.orderStatus == orderPb.OrderStatus_PAID ||
				txnDetails.orderStatus == orderPb.OrderStatus_IN_PAYMENT ||
				txnDetails.orderStatus == orderPb.OrderStatus_SETTLED:
				txnNotifMap, qErr, err = s.getOrCreateTransactionNotificationMap(ctx, txnDetails.id)
				if err != nil {
					return sentSms, sentNotifications, fmt.Errorf("could not get or create transaction notification mapping: %v: %w", err, qErr)
				}
				switch {
				case (user == PAYEE && txnNotifMap.CreditPushNotifId == "") || (user == PAYER && txnNotifMap.DebitPushNotifId == ""):
					if templates, err2 := s.getNotificationTemplates(ctx, txnDetails, user, owt); err2 != nil {
						logger.Debug(ctx, "no notification template found for this case", zap.Error(err2))
					} else {
						pnMsgIdList, err := s.sendBatchPushNotification(ctx, templates, entityId)
						logger.Debug(ctx, "sending notifications", zap.Strings(logger.PN_MSG_ID_LIST, pnMsgIdList), zap.String("entity id", entityId), zap.Error(err))
						if err != nil {
							return sentSms, sentNotifications, err
						}
						sentNotifications = templates
						if len(pnMsgIdList) != 0 {
							var updateMask []notification.TransactionNotificationMapFieldMask
							if user == PAYEE {
								txnNotifMap.CreditPushNotifId = pnMsgIdList[0]
								updateMask = append(updateMask, notification.TransactionNotificationMapFieldMask_CREDIT_PUSH_NOTIF_ID)
							} else if user == PAYER {
								txnNotifMap.DebitPushNotifId = pnMsgIdList[0]
								updateMask = append(updateMask, notification.TransactionNotificationMapFieldMask_DEBIT_PUSH_NOTIF_ID)
							}
							txnNotifMapUpdateErr := s.transactionNotificationMapDao.Update(ctx, txnNotifMap, updateMask)
							if txnNotifMapUpdateErr != nil {
								logger.Error(
									ctx,
									"error in updating transaction notification map",
									zap.String(logger.ORDER_ID, txnDetails.orderId),
									zap.String(logger.TXN_ID, txnDetails.id),
									zap.Error(txnNotifMapUpdateErr),
								)
							}
						}
					}
				}
			case txnDetails.orderStatus == orderPb.OrderStatus_COLLECT_REGISTERED &&
				txnDetails.orderWorkflow == orderPb.OrderWorkflow_P2P_COLLECT && user == PAYER:
				txnNotifMap, qErr, err = s.getOrCreateTransactionNotificationMap(ctx, txnDetails.id)
				if err != nil {
					return sentSms, sentNotifications, fmt.Errorf("could not get or create transaction notification mapping: %v: %w", err, qErr)
				}
				if txnNotifMap.CollectPushNotifId != "" {
					break
				}
				if templates, err2 := s.getNotificationTemplates(ctx, txnDetails, user, owt); err2 != nil {
					logger.Info(ctx, "no notification template found for this case", zap.Error(err2))
				} else {
					pnMsgIdList, err := s.sendBatchPushNotification(ctx, templates, entityId)
					logger.Debug(ctx, "sending notifications", zap.Strings(logger.PN_MSG_ID_LIST, pnMsgIdList), zap.String(logger.ENTITY_ID, entityId), zap.Error(err))
					if err != nil {
						return sentSms, sentNotifications, err
					}
					sentNotifications = templates
					if len(pnMsgIdList) > 1 {
						var updateMask []notification.TransactionNotificationMapFieldMask
						txnNotifMap.CollectPushNotifId = pnMsgIdList[1]
						updateMask = append(updateMask, notification.TransactionNotificationMapFieldMask_COLLECT_PUSH_NOTIF_ID)
						txnNotifMapUpdateErr := s.transactionNotificationMapDao.Update(ctx, txnNotifMap, updateMask)
						if txnNotifMapUpdateErr != nil {
							logger.Error(
								ctx,
								"error in updating transaction notification map",
								zap.String(logger.ORDER_ID, txnDetails.orderId),
								zap.String(logger.TXN_ID, txnDetails.id),
								zap.Error(txnNotifMapUpdateErr),
							)
						}
					}
				}
			case txnDetails.orderStatus == orderPb.OrderStatus_PAYMENT_REVERSED:
				txnNotifMap, qErr, err = s.getOrCreateTransactionNotificationMap(ctx, txnDetails.id)
				if err != nil {
					return sentSms, sentNotifications, fmt.Errorf("could not get or create transaction notification mapping: %v: %w", err, qErr)
				}
				if txnNotifMap.ReversalPushNotifId != "" {
					break
				}
				if templates, err2 := s.getNotificationTemplates(ctx, txnDetails, user, owt); err2 != nil {
					logger.Info(ctx, "no notification template found for this case", zap.String(logger.TXN_ID, txnDetails.id), zap.Error(err2))
				} else {
					pnMsgIdList, err := s.sendBatchPushNotification(ctx, templates, entityId)
					logger.Debug(ctx, "sending notifications", zap.Strings(logger.PN_MSG_ID_LIST, pnMsgIdList), zap.String(logger.ENTITY_ID, entityId), zap.Error(err))
					if err != nil {
						return sentSms, sentNotifications, err
					}
					sentNotifications = templates
					if len(pnMsgIdList) != 0 {
						var updateMask []notification.TransactionNotificationMapFieldMask
						txnNotifMap.ReversalPushNotifId = pnMsgIdList[0]
						updateMask = append(updateMask, notification.TransactionNotificationMapFieldMask_REVERSAL_PUSH_NOTIF_ID)
						txnNotifMapUpdateErr := s.transactionNotificationMapDao.Update(ctx, txnNotifMap, updateMask)
						if txnNotifMapUpdateErr != nil {
							logger.Error(
								ctx,
								"error in updating transaction notification map",
								zap.String(logger.ORDER_ID, txnDetails.orderId),
								zap.String(logger.TXN_ID, txnDetails.id),
								zap.Error(txnNotifMapUpdateErr),
							)
						}
					}
				}
			default:
				// handling failure/collect cases here. Don't need persistence for the same
				if templates, err2 := s.getNotificationTemplates(ctx, txnDetails, user, owt); err2 != nil {
					logger.Info(ctx, "no notification template found for this case", zap.Error(err2))
				} else {
					pnMsgIdList, err := s.sendBatchPushNotification(ctx, templates, entityId)
					logger.Info(ctx, "sending notifications", zap.Strings(logger.PN_MSG_ID_LIST, pnMsgIdList), zap.String(logger.ENTITY_ID, entityId), zap.Error(err))
					if err != nil {
						return sentSms, sentNotifications, err
					}
					sentNotifications = templates
				}
			}
			if txnDetails.orderWorkflow == orderPb.OrderWorkflow_P2P_COLLECT &&
				(txnDetails.orderStatus == orderPb.OrderStatus_PAID ||
					txnDetails.orderStatus == orderPb.OrderStatus_IN_PAYMENT ||
					txnDetails.orderStatus == orderPb.OrderStatus_PAYMENT_FAILED) && txnNotifMap != nil && txnNotifMap.CollectPushNotifId != "" {
				notifRes, err := s.commsClient.UpdateNotificationStatus(ctx, &commsPb.UpdateNotificationStatusRequest{MessageId: txnNotifMap.CollectPushNotifId,
					Action: commsPb.NotificationAction_NOTIFICATION_ACTION_DISMISS})
				if err != nil {
					return sentSms, sentNotifications, fmt.Errorf("could not update notification status: %v: %w", err, epifierrors.ErrTransient)
				}
				if !notifRes.Status.IsSuccess() {
					return sentSms, sentNotifications, fmt.Errorf("Interanl server error while update notification status: %v: %w", notifRes.GetStatus().String(), epifierrors.ErrTransient)
				}
			}
		}
	}
	return sentSms, sentNotifications, nil
}

func (s *Service) getTimeline(ctx context.Context, primaryActorId, secondaryActorId string) (*timelinePb.Timeline, error) {
	getByActorIdsResponse, err := s.timelineClient.GetByActorIds(ctx, &timelinePb.GetByActorIdsRequest{
		PrimaryActorId:   primaryActorId,
		SecondaryActorId: secondaryActorId,
	})

	switch {
	case err != nil:
		return nil, fmt.Errorf("timelineClient.GetByActorIds() failed: %v: %w", err, queue.ErrRPC)
	case !getByActorIdsResponse.GetStatus().IsSuccess():
		return nil, fmt.Errorf("unsuccessful status response from timelineClient.GetByActorIds(): %s, %w",
			getByActorIdsResponse.GetStatus(), queue.ErrRPC)
	default:
		return getByActorIdsResponse.GetTimeline(), nil
	}
}

func (s *Service) getPiById(ctx context.Context, piId string) (*piPb.PaymentInstrument, error) {
	getPiByIdResponse, err := s.piClient.GetPiById(ctx, &piPb.GetPiByIdRequest{Id: piId})

	switch {
	case err != nil:
		return nil, fmt.Errorf("piClient.GetPiById() failed: %v: %w", err, queue.ErrRPC)
	case !getPiByIdResponse.GetStatus().IsSuccess():
		return nil, fmt.Errorf("unsuccessful status response from piClient.GetPiById(): %s, %w",
			getPiByIdResponse.GetStatus(), queue.ErrRPC)
	default:
		return getPiByIdResponse.GetPaymentInstrument(), nil
	}
}

func (s *Service) getAccountBalanceByAccountNoIfscV1(ctx context.Context, actorId, accountNo, ifsc string) (*moneyPb.Money, error) {

	getBalanceReqV1 := &accountBalancePb.GetAccountBalanceRequest{
		ActorId: actorId,
		Identifier: &accountBalancePb.GetAccountBalanceRequest_ExternalId{
			ExternalId: &accountBalancePb.BankAccountIdentifier{
				AccountNo: accountNo,
				IfscCode:  ifsc,
			},
		},
		DataFreshness: enums.DataFreshness_DATA_FRESHNESS_REAL_TIME,
	}

	ctxWithDeadline, cancelFun := context.WithDeadline(ctx, time.Now().Add(5*time.Second))
	defer cancelFun()

	if s.conf.HideAccountBalanceInNotification() {
		return nil, errorAccountBalanceDisabled
	}
	balanceDetails, err := s.paySavingsBalanceClient.GetAccountBalance(ctxWithDeadline, getBalanceReqV1)
	switch {
	case err != nil:
		return nil, fmt.Errorf("failed to fetch savings account balance V1 by accNo, Ifsc %v, %w", err, queue.ErrTransient)
	case balanceDetails.GetStatus().IsRecordNotFound():
		return nil, fmt.Errorf("failed to fetch savings account balance V1 by accNo, Ifsc %v, %w", err, queue.ErrPermanent)
	case !balanceDetails.GetStatus().IsSuccess():
		return nil, fmt.Errorf("failed to fetch savings account balance V1 by accNo, Ifsc %v, %w", err, queue.ErrTransient)
	}
	return balanceDetails.GetAvailableBalance(), nil

}

// getAccountIdFromPiId fetches account Id of the actor to whom the PI of id piId belongs to
func (s *Service) getAccountIdFromPiId(ctx context.Context, piId string) (string, error) {
	getPiByIdResponse, err := s.accountPiRelationClient.GetByPiId(ctx, &account_pi.GetByPiIdRequest{
		PiId: piId,
	})
	switch {
	case err != nil:
		return "", fmt.Errorf("failed to fetch accountId from PiId %v, %w", err, queue.ErrTransient)
	case getPiByIdResponse.GetStatus().IsRecordNotFound():
		return "", fmt.Errorf("failed to fetch accountId from PiId %v, %w", err, queue.ErrPermanent)
	case !getPiByIdResponse.GetStatus().IsSuccess():
		return "", fmt.Errorf("failed to fetch accountId from PiId %v, %w", err, queue.ErrTransient)
	}
	return getPiByIdResponse.GetAccountId(), nil

}

// getAccountById fetches account for the given account id
func (s *Service) getAccountById(ctx context.Context, accountId string) (*savings.GetAccountResponse, error) {
	accountResponse, err := s.savingsClient.GetAccount(ctx, &savings.GetAccountRequest{
		Identifier: &savings.GetAccountRequest_Id{
			Id: accountId,
		},
	})
	if status, _ := status.FromError(err); status != nil {
		queueError := queue.ErrTransient
		if status.Code() == codes.NotFound {
			queueError = queue.ErrPermanent
		}
		return nil, fmt.Errorf("failed to fetch account details using account id %v, %w", err, queueError)
	}
	return accountResponse, nil
}

// getTransactionDetails fetches transaction details such as amount, masked account number, timelineId,
// actor names, date, etc. needed for notification message templates.
// nolint:dupl
func (s *Service) getTransactionDetails(ctx context.Context, txn *paymentPb.Transaction, order *orderPb.Order) (*transactionDetails, error) {

	var (
		balance    *moneyPb.Money
		balanceErr error
	)

	txnDetails := transactionDetails{}

	txnDetails.orderId = order.GetId()
	txnDetails.expireAt = order.GetExpireAt()
	txnDetails.orderWorkflow = order.GetWorkflow()
	txnDetails.provenance = order.GetProvenance()
	txnDetails.id = txn.GetId()
	txnDetails.amount = txn.GetAmount()
	txnDetails.remarks = txn.GetRemarks()
	txnDetails.toActorId = order.GetToActorId()
	txnDetails.eventTime = txn.GetCreatedAt()
	txnDetails.debitedAt = txn.GetDebitedAt()
	txnDetails.creditedAt = txn.GetCreditedAt()
	txnDetails.txnLastUpdatedAt = txn.GetUpdatedAt()
	txnDetails.orderTags = order.GetTags()
	txnDetails.isFitTransaction = orderPb.IsTagExist(order.GetTags(), orderPb.OrderTag_FIT)
	txnDetails.isMutualFundTransaction = orderPb.IsTagExist(order.GetTags(), orderPb.OrderTag_MUTUAL_FUND)
	if txn.Remarks == pay.TodChargesDesc {
		txnDetails.isTodChargeRelated = true
	}

	switch {
	case txn.Remarks == pay.TodChargesDesc:
		txnDetails.isTodChargeRelated = true
	case txn.Remarks == pay.OtherBankAtmTxnChargeDesc:
		txnDetails.IsOtherBankAtmTxnCharge = true
	case txn.Remarks == pay.AtmDeclineFeeDesc:
		txnDetails.IsAtmDeclineFee = true
	case txn.Remarks == pay.EcsReturnChargesDesc:
		txnDetails.IsEcsReturnCharges = true
	case txn.Remarks == pay.DuplicateCardFeeDesc:
		txnDetails.IsDuplicateCardFee = true
	case txn.Remarks == pay.AtmPenaltyDesc:
		txnDetails.IsAtmPenalty = true
	case txn.Remarks == pay.IntATMchargesDesc:
		txnDetails.IsIntATMcharges = true
	case txn.GetRemarks() == pay.EcomPosDeclineChargeDesc:
		txnDetails.IsEcomPosDeclineFee = true
	case txn.GetRemarks() == pay.DebitCardAmcChargeDesc:
		txnDetails.IsDebitCardAmcCharge = true
	}

	if fromPi, err := s.getPiById(ctx, txn.GetPiFrom()); err != nil {
		logger.Debug(ctx, "getTransactionDetails(): Failed to get PiFrom",
			zap.String(logger.TXN_ID, txn.GetId()),
			zap.Error(err))
	} else {
		if fromPi.GetIssuerClassification() == piPb.PaymentInstrumentIssuer_EXTERNAL {
			txnDetails.isPayerExternal = true
		}
		if accounts.IsDepositAccount(fromPi.GetAccount().GetAccountType()) {
			txnDetails.savingAccountUserType = PAYEE
			txnDetails.depositType = depositTypeMap[fromPi.GetAccount().GetAccountType()]
			depositReq := &depositPb.GetByAccountNumberAndIfscRequest{
				AccountNumber: fromPi.GetAccount().GetActualAccountNumber(),
				IfscCode:      fromPi.GetAccount().GetIfscCode(),
			}
			txnDetails.fromAccNo = fromPi.GetAccount().GetActualAccountNumber()
			depositDetails, err := s.depositClient.GetByAccountNumberAndIfsc(ctx, depositReq)
			if err != nil {
				return nil, fmt.Errorf("failed to fetch deposit account %v, %w", err, queue.ErrPermanent)
			} else {
				txnDetails.depositName = depositDetails.GetAccount().GetName()
				txnDetails.depositAccountId = depositDetails.GetAccount().GetId()

				switch depositDetails.GetAccount().GetState() {
				case depositPb.DepositState_PRECLOSED:
					txnDetails.isPreClosed = true
				case depositPb.DepositState_CLOSED:
					txnDetails.isAutoClosed = true
				}
			}
		}
		switch {
		case txn.GetPaymentProtocol() == paymentPb.PaymentProtocol_UPI && fromPi.GetType() == piPb.PaymentInstrumentType_UPI:
			txnDetails.fromUpiVpa = fromPi.GetUpi().GetVpa()
			txnDetails.fromAccNo = fromPi.GetUpi().GetMaskedAccountNumber()
		case fromPi.GetType() == piPb.PaymentInstrumentType_DEBIT_CARD || fromPi.GetType() == piPb.PaymentInstrumentType_CREDIT_CARD:
			accountId, err := s.getAccountIdFromPiId(ctx, fromPi.GetId())
			if err != nil {
				logger.Error(ctx, "Failed to get accountId for fromPi",
					zap.Error(err))
				return nil, err
			}
			getAccountResponse, err := s.getAccountById(ctx, accountId)
			if err != nil {
				logger.Error(ctx, "Failed to get account details for fromPi for card transaction",
					zap.String(logger.ACCOUNT_ID, accountId), zap.Error(err))
				return nil, err
			}
			if !txnDetails.isPayerExternal {
				balance, balanceErr = s.getAccountBalanceByAccountNoIfscV1(ctx, order.GetFromActorId(), getAccountResponse.GetAccount().GetAccountNo(), getAccountResponse.GetAccount().GetIfscCode())
				if balanceErr != nil && errors.Is(balanceErr, errorAccountBalanceDisabled) {
					logger.Debug(ctx, "Failed to get account balance for fromPi account",
						zap.Error(balanceErr))
				} else {
					txnDetails.payerBalanceAmount = balance
				}
			}
			txnDetails.fromAccNo = getAccountResponse.GetAccount().GetAccountNo()
			txnDetails.isCardTransaction = true
		case fromPi.GetType() == piPb.PaymentInstrumentType_BANK_ACCOUNT:
			txnDetails.fromAccNo = fromPi.GetAccount().GetSecureAccountNumber()
			if !txnDetails.isPayerExternal && fromPi.GetAccount().GetAccountType() == accounts.Type_SAVINGS {
				balance, balanceErr = s.getAccountBalanceByAccountNoIfscV1(ctx, order.GetFromActorId(), fromPi.GetAccount().GetActualAccountNumber(), fromPi.GetAccount().GetIfscCode())
				if balanceErr != nil && errors.Is(balanceErr, errorAccountBalanceDisabled) {
					logger.Debug(ctx, "Failed to get account balance for fromPi account",
						zap.Error(balanceErr))
				} else {
					txnDetails.payerBalanceAmount = balance
				}
			}
		default:
			txnDetails.fromAccNo = fromPi.GetAccount().GetSecureAccountNumber()
		}
		txnDetails.fromPiType = fromPi.GetType()
	}

	if toPi, err := s.getPiById(ctx, txn.GetPiTo()); err != nil {
		logger.Debug(ctx, "getTransactionDetails(): Failed to get PiTo",
			zap.String(logger.TXN_ID, txn.GetId()),
			zap.Error(err))
	} else {
		if toPi.GetIssuerClassification() == piPb.PaymentInstrumentIssuer_EXTERNAL {
			txnDetails.isPayeeExternal = true
		}
		if accounts.IsDepositAccount(toPi.GetAccount().GetAccountType()) {
			txnDetails.savingAccountUserType = PAYER
			txnDetails.depositType = depositTypeMap[toPi.GetAccount().GetAccountType()]
			depositReq := &depositPb.GetByAccountNumberAndIfscRequest{
				AccountNumber: toPi.GetAccount().GetActualAccountNumber(),
				IfscCode:      toPi.GetAccount().GetIfscCode(),
			}
			txnDetails.toAccNo = toPi.GetAccount().GetActualAccountNumber()
			depositDetails, err := s.depositClient.GetByAccountNumberAndIfsc(ctx, depositReq)
			if err != nil {
				return nil, fmt.Errorf("failed to fetch deposit account %v, %w", err, queue.ErrPermanent)
			} else {
				txnDetails.depositName = depositDetails.GetAccount().GetName()
				txnDetails.depositAccountId = depositDetails.GetAccount().GetId()
			}

		}
		switch {
		case txn.GetPaymentProtocol() == paymentPb.PaymentProtocol_UPI && toPi.GetType() == piPb.PaymentInstrumentType_UPI:
			txnDetails.toUpiVpa = toPi.GetUpi().GetVpa()
			txnDetails.toAccNo = toPi.GetUpi().GetMaskedAccountNumber()
		case toPi.GetType() == piPb.PaymentInstrumentType_DEBIT_CARD || toPi.GetType() == piPb.PaymentInstrumentType_CREDIT_CARD:
			accountId, err := s.getAccountIdFromPiId(ctx, toPi.GetId())
			if err != nil {
				logger.Error(ctx, "Failed to get accountId for toPi",
					zap.Error(err))
				return nil, err
			}
			getAccountResponse, err := s.getAccountById(ctx, accountId)
			if err != nil {
				logger.Error(ctx, "Failed to get account details for toPi for card transaction",
					zap.String(logger.ACCOUNT_ID, accountId), zap.Error(err))
				return nil, err
			}
			if !txnDetails.isPayeeExternal {
				balance, balanceErr = s.getAccountBalanceByAccountNoIfscV1(ctx, order.GetFromActorId(), getAccountResponse.GetAccount().GetAccountNo(), getAccountResponse.GetAccount().GetIfscCode())
				if balanceErr != nil && errors.Is(balanceErr, errorAccountBalanceDisabled) {
					logger.Debug(ctx, "Failed to get account balance for toPi account",
						zap.Error(balanceErr))
				} else {
					txnDetails.payeeBalanceAmount = balance
				}
			}
			txnDetails.toAccNo = getAccountResponse.GetAccount().GetAccountNo()
			txnDetails.isCardTransaction = true
		case toPi.GetType() == piPb.PaymentInstrumentType_BANK_ACCOUNT:
			txnDetails.toAccNo = toPi.GetAccount().GetSecureAccountNumber()
			if !txnDetails.isPayeeExternal && toPi.GetAccount().GetAccountType() == accounts.Type_SAVINGS {
				balance, balanceErr = s.getAccountBalanceByAccountNoIfscV1(ctx, order.GetToActorId(), toPi.GetAccount().GetActualAccountNumber(), toPi.GetAccount().GetIfscCode())
				if balanceErr != nil && errors.Is(balanceErr, errorAccountBalanceDisabled) {
					logger.Debug(ctx, "Failed to get account balance for toPi account",
						zap.Error(balanceErr))
				} else {
					txnDetails.payeeBalanceAmount = balance
				}
			}
		default:
			txnDetails.toAccNo = genericPiAccNoPlaceholder
		}
		txnDetails.toPiType = toPi.GetType()
	}

	if txnDate, err := ptypes.Timestamp(txn.GetUpdatedAt()); err != nil {
		logger.Debug(ctx, "getTransactionDetails(): Failed to convert proto time to go time",
			zap.String("protoTime", ptypes.TimestampString(txn.GetUpdatedAt())),
			zap.Error(err))
	} else {
		txnDetails.date = fmt.Sprintf("%v %v, %v", txnDate.Month(), txnDate.Day(), txnDate.Year())
	}

	txnDetails.utr = txn.GetUtr()
	txnDetails.protocol = txn.GetPaymentProtocol()

	return &txnDetails, nil
}

// getConfirmationSMSContent function to get the smsOption, QoS for Confirmation SMSes for NEFT, RTGS,
// based on the payment protocol and order status.
//
//nolint:funlen
//nolint:dupl
func (s *Service) getConfirmationSMSContent(txnDetail *transactionDetails) (*commsPb.SmsOption, commsPb.SmsType, commsPb.QoS, error) {
	var smsOption *commsPb.SmsOption
	var transactionTimestamp *timestamp.Timestamp
	qos := commsPb.QoS_QOS_UNSPECIFIED
	smsType := commsPb.SmsType_SMS_TYPE_UNSPECIFIED
	if txnDetail.txnLastUpdatedAt != nil {
		transactionTimestamp = txnDetail.txnLastUpdatedAt
	} else {
		transactionTimestamp = txnDetail.eventTime
	}
	switch {
	case txnDetail.orderStatus == orderPb.OrderStatus_PAID &&
		txnDetail.protocol == paymentPb.PaymentProtocol_RTGS:
		if txnDetail.creditedAt != nil {
			transactionTimestamp = txnDetail.creditedAt
		}
		qos = commsPb.QoS_GUARANTEED
		smsType = commsPb.SmsType_RTGS_CREDIT_CONFIRMATION
		smsOption = &commsPb.SmsOption{
			Option: &commsPb.SmsOption_RtgsCreditConfirmationSmsOption{
				RtgsCreditConfirmationSmsOption: &commsPb.RtgsCreditConfirmationSmsOption{
					SmsType: commsPb.SmsType_RTGS_CREDIT_CONFIRMATION,
					Option: &commsPb.RtgsCreditConfirmationSmsOption_RtgsCreditConfirmationSmsOptionV1{
						RtgsCreditConfirmationSmsOptionV1: &commsPb.RtgsCreditConfirmationSmsOptionV1{
							TemplateVersion:      commsPb.TemplateVersion_VERSION_V1,
							TransactionAmount:    txnDetail.amount,
							TransactionTimestamp: transactionTimestamp,
							Name:                 names.ParseString(txnDetail.fromActorName),
							ReceiverName:         names.ParseString(txnDetail.toActorName),
							ReferenceNumber:      txnDetail.utr,
						},
					},
				},
			},
		}
	case txnDetail.orderStatus == orderPb.OrderStatus_PAID &&
		txnDetail.protocol == paymentPb.PaymentProtocol_NEFT:
		if txnDetail.creditedAt != nil {
			transactionTimestamp = txnDetail.creditedAt
		}
		qos = commsPb.QoS_GUARANTEED
		smsType = commsPb.SmsType_NEFT_CREDIT_CONFIRMATION
		smsOption = &commsPb.SmsOption{
			Option: &commsPb.SmsOption_NeftCreditConfirmationSmsOption{
				NeftCreditConfirmationSmsOption: &commsPb.NeftCreditConfirmationSmsOption{
					SmsType: commsPb.SmsType_NEFT_CREDIT_CONFIRMATION,
					Option: &commsPb.NeftCreditConfirmationSmsOption_NeftCreditConfirmationSmsOptionV1{
						NeftCreditConfirmationSmsOptionV1: &commsPb.NeftCreditConfirmationSmsOptionV1{
							TemplateVersion:      commsPb.TemplateVersion_VERSION_V1,
							TransactionAmount:    txnDetail.amount,
							TransactionTimestamp: transactionTimestamp,
							Name:                 names.ParseString(txnDetail.fromActorName),
							ReceiverName:         names.ParseString(txnDetail.toActorName),
							ReferenceNumber:      txnDetail.utr,
							PaymentMode:          txnDetail.protocol.String(),
						},
					},
				},
			},
		}
	default:
		return nil, smsType, qos, errorTemplateNotFound
	}
	return smsOption, smsType, qos, nil
}

// getSMSContent function to get the smsOption, qos for SMSes, based on the payment protocol and order status.
// Different smsOptions are returned for Payer and Payee.
func (s *Service) getSMSContent(ctx context.Context, txnDetail *transactionDetails, user userType, userGrp []commontypes.UserGroup, owt *orderPb.OrderWithTransactions) (*commsPb.SmsOption, commsPb.SmsType, commsPb.QoS, error) {
	var (
		smsOption *commsPb.SmsOption
		err       error
	)

	// Please use the sms rule factory instead of the switch case to return comms params for future use cases
	// Refer : order/notification/comms/comms_helper.go
	for _, smsRule := range smsRules {
		if smsRule.IsSmsApplicable(owt) {
			return smsRule.GetSmsOption(owt, txnDetail.toAccNo), smsRule.GetSmsType(), smsRule.GetSmsQos(), nil
		}
	}

	smsType := commsPb.SmsType_SMS_TYPE_UNSPECIFIED
	qos := commsPb.QoS_QOS_UNSPECIFIED
	var transactionTimestamp *timestamp.Timestamp
	if txnDetail.txnLastUpdatedAt != nil {
		transactionTimestamp = txnDetail.txnLastUpdatedAt
	} else {
		transactionTimestamp = txnDetail.eventTime
	}

	logger.Debug(ctx, "printing transaction details in sms", zap.String(logger.TXN_ID, txnDetail.id), zap.String(logger.ORDER_ID, txnDetail.orderId),
		zap.Int(USER, int(user)), zap.Bool(IS_DEPOSIT_TXN, txnDetail.isDepositTransaction), zap.String(logger.DEPOSIT_TYPE, txnDetail.depositType))

	switch {
	case txnDetail.isDepositTransaction && user == PAYEE && txnDetail.protocol == paymentPb.PaymentProtocol_INTRA_BANK &&
		(((txnDetail.orderStatus == orderPb.OrderStatus_PAID || (txnDetail.orderStatus == orderPb.OrderStatus_IN_PAYMENT && txnDetail.creditedAt != nil)) && (txnDetail.orderWorkflow == orderPb.OrderWorkflow_P2P_FUND_TRANSFER || txnDetail.orderWorkflow == orderPb.OrderWorkflow_ADD_FUNDS_SD)) ||
			(txnDetail.orderWorkflow == orderPb.OrderWorkflow_REWARDS_ADD_FUNDS_SD && txnDetail.orderStatus == orderPb.OrderStatus_FULFILLED)):
		accountNumberLastFourDigits := ""
		if len(txnDetail.toAccNo) > 4 {
			accountNumberLastFourDigits = txnDetail.toAccNo[len(txnDetail.toAccNo)-4:]
		}
		smsType = commsPb.SmsType_ADD_FUNDS_SD
		qos = commsPb.QoS_GUARANTEED
		smsOption = &commsPb.SmsOption{
			Option: &commsPb.SmsOption_AddFundsSdSmsOption{
				AddFundsSdSmsOption: &commsPb.AddFundsSdSmsOption{
					SmsType: commsPb.SmsType_ADD_FUNDS_SD,
					Option: &commsPb.AddFundsSdSmsOption_AddFundsSdSmsOptionV1{
						AddFundsSdSmsOptionV1: &commsPb.AddFundsSdSmsOptionV1{
							TemplateVersion:               commsPb.TemplateVersion_VERSION_V1,
							SdName:                        txnDetail.depositName,
							SdAccountNumberLastFourDigits: accountNumberLastFourDigits,
							SdAddAmount:                   txnDetail.amount,
						},
					},
				},
			},
		}
		// Go to Fallback Template if balanceAmount is Unavailable
	case txnDetail.isCardTransaction && user == PAYER && txnDetail.protocol == paymentPb.PaymentProtocol_CARD &&
		(txnDetail.provenance == orderPb.OrderProvenance_POS || txnDetail.provenance == orderPb.OrderProvenance_ECOMM) &&
		txnDetail.orderStatus == orderPb.OrderStatus_PAID && txnDetail.payerBalanceAmount == nil:
		smsType = commsPb.SmsType_POS_DEBIT_FALLBACK
		qos = commsPb.QoS_GUARANTEED
		smsOption, err = s.getSmsOption(ctx, smsType, txnDetail, transactionTimestamp, userGrp)
		if err != nil {
			return nil, smsType, qos, fmt.Errorf("error fetching sms option: %w", err)
		}
		// Go to Fallback Template if balanceAmount is Unavailable
	case txnDetail.isCardTransaction && user == PAYER && txnDetail.protocol == paymentPb.PaymentProtocol_CARD &&
		txnDetail.provenance == orderPb.OrderProvenance_ATM && txnDetail.orderStatus == orderPb.OrderStatus_PAID &&
		txnDetail.payerBalanceAmount == nil:
		smsType = commsPb.SmsType_CASH_WITHDRAWAL_ATM_FALLBACK
		qos = commsPb.QoS_GUARANTEED
		smsOption, err = s.getSmsOption(ctx, smsType, txnDetail, transactionTimestamp, userGrp)
		if err != nil {
			return nil, smsType, qos, fmt.Errorf("error fetching sms option: %w", err)
		}
		// Go to Fallback Template if balanceAmount is Unavailable
	case txnDetail.isInterestTransaction && txnDetail.orderStatus == orderPb.OrderStatus_PAID &&
		user == PAYEE && txnDetail.payeeBalanceAmount == nil:
		if txnDetail.creditedAt != nil {
			transactionTimestamp = txnDetail.creditedAt
		}
		smsType = commsPb.SmsType_INTEREST_PAID_IN_SB_FALLBACK
		qos = commsPb.QoS_GUARANTEED
		smsOption, err = s.getSmsOption(ctx, smsType, txnDetail, transactionTimestamp, userGrp)
		if err != nil {
			return nil, smsType, qos, fmt.Errorf("error fetching sms option: %w", err)
		}
		// Go to Fallback Template if balanceAmount is Unavailable
	case (txnDetail.orderStatus == orderPb.OrderStatus_PAID || (txnDetail.orderStatus == orderPb.OrderStatus_IN_PAYMENT && txnDetail.debitedAt != nil)) &&
		user == PAYER && txnDetail.protocol == paymentPb.PaymentProtocol_RTGS && txnDetail.payerBalanceAmount == nil:
		if txnDetail.debitedAt != nil {
			transactionTimestamp = txnDetail.debitedAt
		}
		smsType = commsPb.SmsType_RTGS_DEBIT_FALLBACK
		qos = commsPb.QoS_GUARANTEED
		smsOption, err = s.getSmsOption(ctx, smsType, txnDetail, transactionTimestamp, userGrp)
		if err != nil {
			return nil, smsType, qos, fmt.Errorf("error fetching sms option: %w", err)
		}
		// Go to Fallback Template if balanceAmount is Unavailable
	case (txnDetail.orderStatus == orderPb.OrderStatus_PAID || (txnDetail.orderStatus == orderPb.OrderStatus_IN_PAYMENT &&
		txnDetail.creditedAt != nil)) && txnDetail.protocol == paymentPb.PaymentProtocol_RTGS && user == PAYEE && txnDetail.payeeBalanceAmount == nil:
		if txnDetail.creditedAt != nil {
			transactionTimestamp = txnDetail.creditedAt
		}
		smsType = commsPb.SmsType_NEFT_CREDIT_FALLBACK
		qos = commsPb.QoS_GUARANTEED
		smsOption, err = s.getSmsOption(ctx, smsType, txnDetail, transactionTimestamp, userGrp)
		if err != nil {
			return nil, smsType, qos, fmt.Errorf("error fetching sms option: %w", err)
		}
		// Go to Fallback Template if balanceAmount is Unavailable
	case (txnDetail.orderStatus == orderPb.OrderStatus_PAID || (txnDetail.orderStatus == orderPb.OrderStatus_IN_PAYMENT && txnDetail.debitedAt != nil)) &&
		user == PAYER && txnDetail.protocol == paymentPb.PaymentProtocol_NEFT && txnDetail.payerBalanceAmount == nil:
		if txnDetail.debitedAt != nil {
			transactionTimestamp = txnDetail.debitedAt
		}
		smsType = commsPb.SmsType_NEFT_DEBIT_FALLBACK
		qos = commsPb.QoS_GUARANTEED
		smsOption, err = s.getSmsOption(ctx, smsType, txnDetail, transactionTimestamp, userGrp)
		if err != nil {
			return nil, smsType, qos, fmt.Errorf("error fetching sms option: %w", err)
		}
		// Go to Fallback Template if balanceAmount is Unavailable
	case (txnDetail.orderStatus == orderPb.OrderStatus_PAID || (txnDetail.orderStatus == orderPb.OrderStatus_IN_PAYMENT && txnDetail.creditedAt != nil)) &&
		txnDetail.protocol == paymentPb.PaymentProtocol_NEFT && user == PAYEE && txnDetail.payeeBalanceAmount == nil:
		if txnDetail.creditedAt != nil {
			transactionTimestamp = txnDetail.creditedAt
		}
		smsType = commsPb.SmsType_NEFT_CREDIT_FALLBACK
		qos = commsPb.QoS_GUARANTEED
		smsOption, err = s.getSmsOption(ctx, smsType, txnDetail, transactionTimestamp, userGrp)
		if err != nil {
			return nil, smsType, qos, fmt.Errorf("error fetching sms option: %w", err)
		}
	case txnDetail.isCardTransaction && user == PAYER && txnDetail.protocol == paymentPb.PaymentProtocol_CARD &&
		(txnDetail.provenance == orderPb.OrderProvenance_POS || txnDetail.provenance == orderPb.OrderProvenance_ECOMM) &&
		txnDetail.orderStatus == orderPb.OrderStatus_PAID:
		smsType = commsPb.SmsType_POS_DEBIT
		qos = commsPb.QoS_GUARANTEED
		smsOption = &commsPb.SmsOption{
			Option: &commsPb.SmsOption_PosDebitSmsOption{
				PosDebitSmsOption: &commsPb.PosDebitSmsOption{
					SmsType: commsPb.SmsType_POS_DEBIT,
					Option: &commsPb.PosDebitSmsOption_PosDebitSmsOptionV1{
						PosDebitSmsOptionV1: &commsPb.PosDebitSmsOptionV1{
							TemplateVersion:      commsPb.TemplateVersion_VERSION_V1,
							TransactionAmount:    txnDetail.amount,
							ReceiverName:         names.ParseString(txnDetail.toActorName),
							TransactionTimestamp: transactionTimestamp,
							BalanceAmount:        txnDetail.payerBalanceAmount,
						},
					},
				},
			},
		}
	case txnDetail.isCardTransaction && user == PAYER && txnDetail.protocol == paymentPb.PaymentProtocol_CARD &&
		txnDetail.provenance == orderPb.OrderProvenance_ATM && txnDetail.orderStatus == orderPb.OrderStatus_PAID:
		smsType = commsPb.SmsType_CASH_WITHDRAWAL_ATM
		qos = commsPb.QoS_GUARANTEED
		smsOption = &commsPb.SmsOption{
			Option: &commsPb.SmsOption_CashWithdrawalAtmSmsOption{
				CashWithdrawalAtmSmsOption: &commsPb.CashWithdrawalAtmSmsOption{
					SmsType: commsPb.SmsType_CASH_WITHDRAWAL_ATM,
					Option: &commsPb.CashWithdrawalAtmSmsOption_CashWithdrawalAtmSmsOptionV1{
						CashWithdrawalAtmSmsOptionV1: &commsPb.CashWithdrawalAtmSmsOptionV1{
							TemplateVersion:      commsPb.TemplateVersion_VERSION_V1,
							WithdrawnAmount:      txnDetail.amount,
							TransactionTimestamp: transactionTimestamp,
							BalanceAmount:        txnDetail.payerBalanceAmount,
						},
					},
				},
			},
		}
	case txnDetail.isCardTransaction && user == PAYEE && txnDetail.protocol == paymentPb.PaymentProtocol_CARD &&
		txnDetail.orderStatus == orderPb.OrderStatus_PAID:
		smsType = commsPb.SmsType_GENERIC_PI_CREDIT
		qos = commsPb.QoS_GUARANTEED
		smsOption, err = s.getSmsOption(ctx, smsType, txnDetail, transactionTimestamp, userGrp)
		if err != nil {
			return nil, smsType, qos, fmt.Errorf("error fetching sms option: %w", err)
		}
	case (txnDetail.orderStatus == orderPb.OrderStatus_PAID || (txnDetail.orderStatus == orderPb.OrderStatus_IN_PAYMENT && txnDetail.debitedAt != nil)) &&
		user == PAYER &&
		txnDetail.protocol == paymentPb.PaymentProtocol_UPI:
		if txnDetail.debitedAt != nil {
			transactionTimestamp = txnDetail.debitedAt
		}
		smsType = commsPb.SmsType_UPI_DEBIT
		qos = commsPb.QoS_GUARANTEED
		smsOption, err = s.getSmsOption(ctx, smsType, txnDetail, transactionTimestamp, userGrp)
		if err != nil {
			return nil, smsType, qos, fmt.Errorf("error fetching sms option: %w", err)
		}
	case isAddFunds(txnDetail.orderWorkflow) && txnDetail.orderStatus == orderPb.OrderStatus_SETTLED:
		if txnDetail.creditedAt != nil {
			transactionTimestamp = txnDetail.creditedAt
		}
		smsType = commsPb.SmsType_GENERIC_PI_CREDIT
		qos = commsPb.QoS_GUARANTEED
		smsOption, err = s.getSmsOption(ctx, smsType, txnDetail, transactionTimestamp, userGrp)
		if err != nil {
			return nil, smsType, qos, fmt.Errorf("error fetching sms option: %w", err)
		}
	case txnDetail.isInterestTransaction && txnDetail.orderStatus == orderPb.OrderStatus_PAID && user == PAYEE:
		if txnDetail.creditedAt != nil {
			transactionTimestamp = txnDetail.creditedAt
		}

		smsType = commsPb.SmsType_INTEREST_PAID_IN_SB
		qos = commsPb.QoS_GUARANTEED

		smsOption = &commsPb.SmsOption{
			Option: &commsPb.SmsOption_InterestPaidInSbSmsOption{
				InterestPaidInSbSmsOption: &commsPb.InterestPaidInSBSmsOption{
					SmsType: commsPb.SmsType_INTEREST_PAID_IN_SB,
					Option: &commsPb.InterestPaidInSBSmsOption_InterestPaidInSbSmsOptionV1{
						InterestPaidInSbSmsOptionV1: &commsPb.InterestPaidInSBSmsOptionV1{
							TemplateVersion:             commsPb.TemplateVersion_VERSION_V1,
							InterestAmount:              txnDetail.amount,
							Timestamp:                   transactionTimestamp,
							Balance:                     txnDetail.payeeBalanceAmount,
							AccountNumberLastFourDigits: txnDetail.toAccNo[len(txnDetail.toAccNo)-4:],
						},
					},
				},
			},
		}

	case !isAddFunds(txnDetail.orderWorkflow) && // nolint: dupl
		(txnDetail.orderStatus == orderPb.OrderStatus_PAID || (txnDetail.orderStatus == orderPb.OrderStatus_IN_PAYMENT && txnDetail.creditedAt != nil)) &&
		user == PAYEE &&
		txnDetail.protocol == paymentPb.PaymentProtocol_UPI &&
		(txnDetail.toPiType == piPb.PaymentInstrumentType_UPI && txnDetail.fromPiType == piPb.PaymentInstrumentType_UPI):
		if txnDetail.creditedAt != nil {
			transactionTimestamp = txnDetail.creditedAt
		}
		smsType = commsPb.SmsType_UPI_CREDIT
		qos = commsPb.QoS_GUARANTEED
		smsOption, err = s.getSmsOption(ctx, smsType, txnDetail, transactionTimestamp, userGrp)
		if err != nil {
			return nil, smsType, qos, fmt.Errorf("error fetching sms option: %w", err)
		}

	case !isAddFunds(txnDetail.orderWorkflow) && // nolint: dupl
		(txnDetail.orderStatus == orderPb.OrderStatus_PAID || (txnDetail.orderStatus == orderPb.OrderStatus_IN_PAYMENT && txnDetail.creditedAt != nil)) &&
		user == PAYEE &&
		txnDetail.protocol == paymentPb.PaymentProtocol_UPI &&
		(txnDetail.toPiType == piPb.PaymentInstrumentType_BANK_ACCOUNT || txnDetail.fromPiType == piPb.PaymentInstrumentType_BANK_ACCOUNT):
		if txnDetail.creditedAt != nil {
			transactionTimestamp = txnDetail.creditedAt
		}
		smsType = commsPb.SmsType_GENERIC_PI_CREDIT
		qos = commsPb.QoS_GUARANTEED
		smsOption, err = s.getSmsOption(ctx, smsType, txnDetail, transactionTimestamp, userGrp)
		if err != nil {
			return nil, smsType, qos, fmt.Errorf("error fetching sms option: %w", err)
		}
	case txnDetail.orderStatus == orderPb.OrderStatus_PAID && txnDetail.isTodChargeRelated &&
		user == PAYER && txnDetail.toPiType == piPb.PaymentInstrumentType_GENERIC:
		smsType = commsPb.SmsType_TOD_CHARGES_DEBIT
		qos = commsPb.QoS_GUARANTEED
		smsOption, err = s.getSmsOption(ctx, smsType, txnDetail, transactionTimestamp, userGrp)
		if err != nil {
			return nil, smsType, qos, fmt.Errorf("error fetching sms option: %w", err)
		}
	case txnDetail.orderStatus == orderPb.OrderStatus_PAID && txnDetail.IsEcsReturnCharges &&
		user == PAYER:
		smsType = commsPb.SmsType_ECS_RETURN_CHARGES
		qos = commsPb.QoS_GUARANTEED
		smsOption, err = s.getSmsOption(ctx, smsType, txnDetail, transactionTimestamp, userGrp)
		if err != nil {
			return nil, smsType, qos, fmt.Errorf("error fetching sms option: %w", err)
		}
	case txnDetail.orderStatus == orderPb.OrderStatus_PAID && user == PAYER && txnDetail.IsAtmDeclineFee:
		smsType = commsPb.SmsType_ATM_DECLINE_FEES
		qos = commsPb.QoS_GUARANTEED
		smsOption, err = s.getSmsOption(ctx, smsType, txnDetail, transactionTimestamp, userGrp)
		if err != nil {
			return nil, smsType, qos, fmt.Errorf("error fetching sms option: %w", err)
		}
		logger.Info(ctx, "no error found while fetching sms option ")
	case txnDetail.orderStatus == orderPb.OrderStatus_PAID && user == PAYER && txnDetail.IsDuplicateCardFee:
		smsType = commsPb.SmsType_DUPLICATE_CARD_FEE
		qos = commsPb.QoS_GUARANTEED
		smsOption, err = s.getSmsOption(ctx, smsType, txnDetail, transactionTimestamp, userGrp)
		if err != nil {
			return nil, smsType, qos, fmt.Errorf("error fetching sms option: %w", err)
		}
	case txnDetail.orderStatus == orderPb.OrderStatus_PAID && user == PAYER && txnDetail.IsAtmPenalty:
		smsType = commsPb.SmsType_ATM_WITHDRAWAL_COMPLAINT_PENALTY
		qos = commsPb.QoS_GUARANTEED
		smsOption, err = s.getSmsOption(ctx, smsType, txnDetail, transactionTimestamp, userGrp)
		if err != nil {
			return nil, smsType, qos, fmt.Errorf("error fetching sms option: %w", err)
		}
	case txnDetail.orderStatus == orderPb.OrderStatus_PAID && user == PAYER && txnDetail.IsIntATMcharges:
		smsType = commsPb.SmsType_INTERNATIONAL_ATM_CHARGES
		qos = commsPb.QoS_GUARANTEED
		smsOption, err = s.getSmsOption(ctx, smsType, txnDetail, transactionTimestamp, userGrp)
		if err != nil {
			return nil, smsType, qos, fmt.Errorf("error fetching sms option: %w", err)
		}
	case txnDetail.orderStatus == orderPb.OrderStatus_PAID && user == PAYER && txnDetail.IsOtherBankAtmTxnCharge:
		smsType = commsPb.SmsType_OTHER_BANK_ATM_USAGE_CHARGES
		qos = commsPb.QoS_GUARANTEED
		smsOption, err = s.getSmsOption(ctx, smsType, txnDetail, transactionTimestamp, userGrp)
		if err != nil {
			return nil, smsType, qos, fmt.Errorf("error fetching sms option: %w", err)
		}
	case (txnDetail.orderStatus == orderPb.OrderStatus_PAID ||
		(txnDetail.orderStatus == orderPb.OrderStatus_IN_PAYMENT && txnDetail.debitedAt != nil)) &&
		user == PAYER &&
		txnDetail.toPiType == piPb.PaymentInstrumentType_GENERIC:
		if txnDetail.debitedAt != nil {
			transactionTimestamp = txnDetail.debitedAt
		}
		if txnDetail.toAccNo == "" {
			smsType = commsPb.SmsType_GENERIC_PI_DEBIT_UNCLEAR_BENEFICARY_DETAILS
			qos = commsPb.QoS_GUARANTEED
			smsOption, err = s.getSmsOption(ctx, smsType, txnDetail, transactionTimestamp, userGrp)
			if err != nil {
				return nil, smsType, qos, fmt.Errorf("error fetching sms option: %w", err)
			}
		} else {
			smsType = commsPb.SmsType_GENERIC_PI_DEBIT
			qos = commsPb.QoS_GUARANTEED
			smsOption, err = s.getSmsOption(ctx, smsType, txnDetail, transactionTimestamp, userGrp)
			if err != nil {
				return nil, smsType, qos, fmt.Errorf("error fetching sms option: %w", err)
			}
		}
	case (txnDetail.orderStatus == orderPb.OrderStatus_PAID ||
		(txnDetail.orderStatus == orderPb.OrderStatus_IN_PAYMENT && txnDetail.creditedAt != nil)) &&
		user == PAYEE &&
		txnDetail.fromPiType == piPb.PaymentInstrumentType_GENERIC:
		if txnDetail.creditedAt != nil {
			transactionTimestamp = txnDetail.creditedAt
		}
		smsType = commsPb.SmsType_GENERIC_PI_CREDIT
		qos = commsPb.QoS_GUARANTEED
		smsOption, err = s.getSmsOption(ctx, smsType, txnDetail, transactionTimestamp, userGrp)
		if err != nil {
			return nil, smsType, qos, fmt.Errorf("error fetching sms option: %w", err)
		}
	case s.disableCommsForFundTransferV1Orders(txnDetail.orderTags) && (txnDetail.orderStatus == orderPb.OrderStatus_PAID || (txnDetail.orderStatus == orderPb.OrderStatus_IN_PAYMENT && txnDetail.debitedAt != nil)) &&
		user == PAYER && txnDetail.protocol == paymentPb.PaymentProtocol_RTGS:
		if txnDetail.debitedAt != nil {
			transactionTimestamp = txnDetail.debitedAt
		}
		smsType = commsPb.SmsType_RTGS_DEBIT
		qos = commsPb.QoS_GUARANTEED

		smsOption = &commsPb.SmsOption{
			Option: &commsPb.SmsOption_RtgsDebitSmsOption{
				RtgsDebitSmsOption: &commsPb.RtgsDebitSmsOption{
					SmsType: commsPb.SmsType_RTGS_DEBIT,
					Option: &commsPb.RtgsDebitSmsOption_RtgsDebitSmsOptionV1{
						RtgsDebitSmsOptionV1: &commsPb.RtgsDebitSmsOptionV1{
							TemplateVersion:      commsPb.TemplateVersion_VERSION_V1,
							TransactionAmount:    txnDetail.amount,
							TransactionTimestamp: transactionTimestamp,
							Name:                 names.ParseString(txnDetail.fromActorName),
							ReceiverName:         names.ParseString(txnDetail.toActorName),
							ReferenceNumber:      txnDetail.utr,
							PaymentMode:          txnDetail.protocol.String(),
							BalanceAmount:        txnDetail.payerBalanceAmount,
						},
					},
				},
			},
		}
	case (txnDetail.orderStatus == orderPb.OrderStatus_PAID || // nolint: dupl
		(txnDetail.orderStatus == orderPb.OrderStatus_IN_PAYMENT &&
			txnDetail.creditedAt != nil)) &&
		txnDetail.protocol == paymentPb.PaymentProtocol_RTGS && user == PAYEE:
		if txnDetail.creditedAt != nil {
			transactionTimestamp = txnDetail.creditedAt
		}
		smsType = commsPb.SmsType_NEFT_CREDIT
		qos = commsPb.QoS_GUARANTEED

		smsOption = &commsPb.SmsOption{
			Option: &commsPb.SmsOption_NeftCreditSmsOption{
				NeftCreditSmsOption: &commsPb.NeftCreditSmsOption{
					SmsType: commsPb.SmsType_NEFT_CREDIT,
					Option: &commsPb.NeftCreditSmsOption_NeftCreditSmsOptionV1{
						NeftCreditSmsOptionV1: &commsPb.NeftCreditSmsOptionV1{
							TemplateVersion:      commsPb.TemplateVersion_VERSION_V1,
							TransactionAmount:    txnDetail.amount,
							TransactionTimestamp: transactionTimestamp,
							Name:                 names.ParseString(txnDetail.toActorName),
							SenderName:           names.ParseString(txnDetail.fromActorName),
							ReferenceNumber:      txnDetail.utr,
							PaymentMode:          txnDetail.protocol.String(),
							BalanceAmount:        txnDetail.payeeBalanceAmount,
						},
					},
				},
			},
		}
	case s.disableCommsForFundTransferV1Orders(txnDetail.orderTags) && (txnDetail.orderStatus == orderPb.OrderStatus_PAID || (txnDetail.orderStatus == orderPb.OrderStatus_IN_PAYMENT && txnDetail.debitedAt != nil)) &&
		user == PAYER && txnDetail.protocol == paymentPb.PaymentProtocol_NEFT:
		if txnDetail.debitedAt != nil {
			transactionTimestamp = txnDetail.debitedAt
		}
		smsType = commsPb.SmsType_NEFT_DEBIT
		qos = commsPb.QoS_GUARANTEED
		smsOption = &commsPb.SmsOption{
			Option: &commsPb.SmsOption_NeftDebitSmsOption{
				NeftDebitSmsOption: &commsPb.NeftDebitSmsOption{
					SmsType: commsPb.SmsType_NEFT_DEBIT,
					Option: &commsPb.NeftDebitSmsOption_NeftDebitSmsOptionV1{
						NeftDebitSmsOptionV1: &commsPb.NeftDebitSmsOptionV1{
							TemplateVersion:      commsPb.TemplateVersion_VERSION_V1,
							TransactionAmount:    txnDetail.amount,
							TransactionTimestamp: transactionTimestamp,
							Name:                 names.ParseString(txnDetail.fromActorName),
							ReceiverName:         names.ParseString(txnDetail.toActorName),
							PaymentMode:          txnDetail.protocol.String(),
							BalanceAmount:        txnDetail.payerBalanceAmount,
							ReferenceNumber:      txnDetail.utr,
						},
					},
				},
			},
		}
	case (txnDetail.orderStatus == orderPb.OrderStatus_PAID || // nolint: dupl
		(txnDetail.orderStatus == orderPb.OrderStatus_IN_PAYMENT &&
			txnDetail.creditedAt != nil)) && txnDetail.protocol == paymentPb.PaymentProtocol_NEFT &&
		user == PAYEE:
		if txnDetail.creditedAt != nil {
			transactionTimestamp = txnDetail.creditedAt
		}
		smsType = commsPb.SmsType_NEFT_CREDIT
		qos = commsPb.QoS_GUARANTEED

		smsOption = &commsPb.SmsOption{
			Option: &commsPb.SmsOption_NeftCreditSmsOption{
				NeftCreditSmsOption: &commsPb.NeftCreditSmsOption{
					SmsType: commsPb.SmsType_NEFT_CREDIT,
					Option: &commsPb.NeftCreditSmsOption_NeftCreditSmsOptionV1{
						NeftCreditSmsOptionV1: &commsPb.NeftCreditSmsOptionV1{
							TemplateVersion:      commsPb.TemplateVersion_VERSION_V1,
							TransactionAmount:    txnDetail.amount,
							TransactionTimestamp: transactionTimestamp,
							Name:                 names.ParseString(txnDetail.toActorName),
							SenderName:           names.ParseString(txnDetail.fromActorName),
							ReferenceNumber:      txnDetail.utr,
							PaymentMode:          txnDetail.protocol.String(),
							BalanceAmount:        txnDetail.payeeBalanceAmount, // fix this
						},
					},
				},
			},
		}
	case s.disableCommsForFundTransferV1Orders(txnDetail.orderTags) && (txnDetail.orderStatus == orderPb.OrderStatus_PAID || (txnDetail.orderStatus == orderPb.OrderStatus_IN_PAYMENT && txnDetail.debitedAt != nil)) &&
		txnDetail.protocol == paymentPb.PaymentProtocol_IMPS && user == PAYER:

		if txnDetail.debitedAt != nil {
			transactionTimestamp = txnDetail.debitedAt
		}
		if txnDetail.toAccNo == "" {
			smsType = commsPb.SmsType_GENERIC_PI_DEBIT_UNCLEAR_BENEFICARY_DETAILS
			qos = commsPb.QoS_GUARANTEED
			smsOption, err = s.getSmsOption(ctx, smsType, txnDetail, transactionTimestamp, userGrp)
			if err != nil {
				return nil, smsType, qos, fmt.Errorf("error fetching sms option: %w", err)
			}
		} else {
			smsType = commsPb.SmsType_GENERIC_PI_DEBIT
			qos = commsPb.QoS_GUARANTEED
			smsOption, err = s.getSmsOption(ctx, smsType, txnDetail, transactionTimestamp, userGrp)
			if err != nil {
				return nil, smsType, qos, fmt.Errorf("error fetching sms option: %w", err)
			}
		}
	case (txnDetail.orderStatus == orderPb.OrderStatus_PAID || (txnDetail.orderStatus == orderPb.OrderStatus_IN_PAYMENT && txnDetail.creditedAt != nil)) &&
		txnDetail.protocol == paymentPb.PaymentProtocol_IMPS && user == PAYEE:
		if txnDetail.creditedAt != nil {
			transactionTimestamp = txnDetail.creditedAt
		}
		smsType = commsPb.SmsType_GENERIC_PI_CREDIT
		qos = commsPb.QoS_GUARANTEED
		smsOption, err = s.getSmsOption(ctx, smsType, txnDetail, transactionTimestamp, userGrp)
		if err != nil {
			return nil, smsType, qos, fmt.Errorf("error fetching sms option: %w", err)
		}
	case s.disableCommsForFundTransferV1Orders(txnDetail.orderTags) && !isAddFunds(txnDetail.orderWorkflow) &&
		(txnDetail.orderStatus == orderPb.OrderStatus_PAID || (txnDetail.orderStatus == orderPb.OrderStatus_IN_PAYMENT && txnDetail.debitedAt != nil)) &&
		txnDetail.protocol == paymentPb.PaymentProtocol_INTRA_BANK && user == PAYER:

		if txnDetail.debitedAt != nil {
			transactionTimestamp = txnDetail.debitedAt
		}
		if txnDetail.toAccNo == "" {
			smsType = commsPb.SmsType_GENERIC_PI_DEBIT_UNCLEAR_BENEFICARY_DETAILS
			qos = commsPb.QoS_GUARANTEED
			smsOption, err = s.getSmsOption(ctx, smsType, txnDetail, transactionTimestamp, userGrp)
			if err != nil {
				return nil, smsType, qos, fmt.Errorf("error fetching sms option: %w", err)
			}
		} else {
			smsType = commsPb.SmsType_GENERIC_PI_DEBIT
			qos = commsPb.QoS_GUARANTEED
			smsOption, err = s.getSmsOption(ctx, smsType, txnDetail, transactionTimestamp, userGrp)
			if err != nil {
				return nil, smsType, qos, fmt.Errorf("error fetching sms option: %w", err)
			}
		}
	case !isAddFunds(txnDetail.orderWorkflow) &&
		(txnDetail.orderStatus == orderPb.OrderStatus_PAID || (txnDetail.orderStatus == orderPb.OrderStatus_IN_PAYMENT && txnDetail.creditedAt != nil)) &&
		txnDetail.protocol == paymentPb.PaymentProtocol_INTRA_BANK && user == PAYEE:
		if txnDetail.creditedAt != nil {
			transactionTimestamp = txnDetail.creditedAt
		}
		smsType = commsPb.SmsType_GENERIC_PI_CREDIT
		qos = commsPb.QoS_GUARANTEED
		smsOption, err = s.getSmsOption(ctx, smsType, txnDetail, transactionTimestamp, userGrp)
		if err != nil {
			return nil, smsType, qos, fmt.Errorf("error fetching sms option: %w", err)
		}
	case txnDetail.orderStatus == orderPb.OrderStatus_PAYMENT_FAILED &&
		txnDetail.protocol == paymentPb.PaymentProtocol_UPI:
		if s.isInAppTransactionWithinSkipSmsThreshold(txnDetail, owt) {
			// TODO (Ashutosh) remove this log line after impact analysis
			logger.Info(ctx, fmt.Sprintf("Disabling sms template since its only been less than %d Seconds from execution time", s.smsParams.TransactionFailed().TriggerAfter), zap.String(logger.ORDER_ID, txnDetail.orderId))
			return nil, smsType, qos, errorSmsBeforeTriggerAfter
		}
		smsType = commsPb.SmsType_FAILED_TRANSACTION
		qos = commsPb.QoS_GUARANTEED
		smsOption = &commsPb.SmsOption{
			Option: &commsPb.SmsOption_FailedTransactonSmsOption{
				FailedTransactonSmsOption: &commsPb.FailedTransactionSmsOption{
					SmsType: commsPb.SmsType_FAILED_TRANSACTION,
					Option: &commsPb.FailedTransactionSmsOption_FailedTransactionSmsOptionV1{
						FailedTransactionSmsOptionV1: &commsPb.FailedTransactionSmsOptionV1{
							TemplateVersion:   commsPb.TemplateVersion_VERSION_V1,
							TransactionAmount: txnDetail.amount,
						},
					},
				},
			},
		}
	case txnDetail.orderStatus == orderPb.OrderStatus_COLLECT_REGISTERED:
		smsType = commsPb.SmsType_COLLECT_REQUEST
		qos = commsPb.QoS_GUARANTEED

		smsOption = &commsPb.SmsOption{
			Option: &commsPb.SmsOption_CollectRequestSmsOption{
				CollectRequestSmsOption: &commsPb.CollectRequestSmsOption{
					SmsType: commsPb.SmsType_COLLECT_REQUEST,
					Option: &commsPb.CollectRequestSmsOption_CollectRequestSmsOptionV1{
						CollectRequestSmsOptionV1: &commsPb.CollectRequestSmsOptionV1{
							TemplateVersion:   commsPb.TemplateVersion_VERSION_V1,
							TransactionAmount: txnDetail.amount,
							Name:              names.ParseString(txnDetail.fromActorName),
							SenderName:        names.ParseString(txnDetail.toActorName),
						},
					},
				},
			},
		}
	case s.disableCommsForFundTransferV1Orders(txnDetail.orderTags) && (txnDetail.orderStatus == orderPb.OrderStatus_PAID ||
		(txnDetail.orderStatus == orderPb.OrderStatus_IN_PAYMENT && txnDetail.debitedAt != nil)) &&
		user == PAYER:
		if txnDetail.debitedAt != nil {
			transactionTimestamp = txnDetail.debitedAt
		}
		if txnDetail.toAccNo == "" {
			smsType = commsPb.SmsType_GENERIC_PI_DEBIT_UNCLEAR_BENEFICARY_DETAILS
			qos = commsPb.QoS_GUARANTEED
			smsOption, err = s.getSmsOption(ctx, smsType, txnDetail, transactionTimestamp, userGrp)
			if err != nil {
				return nil, smsType, qos, fmt.Errorf("error fetching sms option: %w", err)
			}
		} else {
			smsType = commsPb.SmsType_GENERIC_PI_DEBIT
			qos = commsPb.QoS_GUARANTEED
			smsOption, err = s.getSmsOption(ctx, smsType, txnDetail, transactionTimestamp, userGrp)
			if err != nil {
				return nil, smsType, qos, fmt.Errorf("error fetching sms option: %w", err)
			}
		}
	case !isAddFunds(txnDetail.orderWorkflow) &&
		(txnDetail.orderStatus == orderPb.OrderStatus_PAID ||
			(txnDetail.orderStatus == orderPb.OrderStatus_IN_PAYMENT && txnDetail.creditedAt != nil)) &&
		user == PAYEE:
		if txnDetail.creditedAt != nil {
			transactionTimestamp = txnDetail.creditedAt
		}
		smsType = commsPb.SmsType_GENERIC_PI_CREDIT
		qos = commsPb.QoS_GUARANTEED
		smsOption, err = s.getSmsOption(ctx, smsType, txnDetail, transactionTimestamp, userGrp)
		if err != nil {
			return nil, smsType, qos, fmt.Errorf("error fetching sms option: %w", err)
		}
	case s.disableCommsForFundTransferV1Orders(txnDetail.orderTags) && txnDetail.orderStatus == orderPb.OrderStatus_PAYMENT_REVERSED && user == PAYER:
		smsType = commsPb.SmsType_TRANSACTION_REVERSED
		qos = commsPb.QoS_GUARANTEED
		smsOption, err = s.getSmsOption(ctx, smsType, txnDetail, transactionTimestamp, userGrp)
		if err != nil {
			return nil, smsType, qos, fmt.Errorf("error fetching sms option: %w", err)
		}
	default:
		return nil, smsType, qos, errorTemplateNotFound
	}
	logger.Debug(ctx, "sms template found", zap.String(logger.SMS_TEMPLATE, smsType.String()))
	return smsOption, smsType, qos, nil
}

// isInAppTransactionWithinSkipSmsThreshold checks if a transaction is in-app and is within skip sms threshold
func (s *Service) isInAppTransactionWithinSkipSmsThreshold(txnDetail *transactionDetails, orderWithTxns *orderPb.OrderWithTransactions) bool {
	if !orderWithTxns.GetOrder().IsInAppOrder() {
		return false
	}
	return txnDetail.getExecutionTs() != nil && time.Since(txnDetail.getExecutionTs().AsTime()) < s.smsParams.TransactionFailed().TriggerAfter
}

// getOrCreateTransactionNotificationMap fetches a TransactionNotificationMap record if it exists or creates and returns it
func (s *Service) getOrCreateTransactionNotificationMap(ctx context.Context, txnId string) (*notification.TransactionNotificationMap, error, error) {
	txnNotifMap, err := s.transactionNotificationMapDao.GetByTransactionId(ctx, txnId)
	if err != nil && errors.Is(err, gormv2.ErrRecordNotFound) {
		txnNotifMap, err = s.transactionNotificationMapDao.Create(ctx, &notification.TransactionNotificationMap{
			TransactionId: txnId,
		})
		switch {
		case storagev2.IsForeignKeyMissingError(err):
			return nil, queue.ErrPermanent, fmt.Errorf("failed to create TransactionNotificationMapping for txnId : %v: %w", txnId, err)
		case err != nil:
			return nil, queue.ErrTransient, fmt.Errorf("failed to create TransactionNotificationMapping for txnId : %v: %w", txnId, err)
		default:
			return txnNotifMap, nil, nil
		}
	} else if err != nil {
		return nil, queue.ErrTransient, fmt.Errorf("failed to get TransactionNotificationMapping for txnId : %v: %w", txnId, err)
	}
	return txnNotifMap, nil, nil
}

// sd_open-add_funds_rewards-NEFT_credit-RTGS_credit
// getFcmNotification function to get fcm.Notification with common fields based on the payment protocol,
// order status and userType
// nolint:funlen
func (s *Service) getFcmNotifications(ctx context.Context, commonFields *fcm.CommonTemplateFields, txnDetail *transactionDetails, user userType, owt *orderPb.OrderWithTransactions) ([]*commsPb.NotificationMessage, error) {
	if commonFields == nil {
		commonFields = &fcm.CommonTemplateFields{}
	}

	var (
		res              []*commsPb.NotificationMessage
		fcmNotifications []*fcm.Notification
	)

	logger.Debug(ctx, "printing transaction details in notification", zap.String(logger.TXN_ID, txnDetail.id), zap.String(logger.ORDER_ID, txnDetail.orderId),
		zap.Int(USER, int(user)), zap.Bool(IS_DEPOSIT_TXN, txnDetail.isDepositTransaction), zap.String(logger.DEPOSIT_TYPE, txnDetail.depositType))

	// Please use the push notifications rule factory instead of the switch case to return comms params for future use cases
	// Refer : order/notification/comms/comms_helper.go
	for _, pn := range pushNotificationRules {
		if pn.IsMessageApplicable(owt) {
			curNotifications := pn.GetNotification(owt, txnDetail.toActorName)
			fcmNotifications = append(fcmNotifications, curNotifications...)
			priority := commsPb.NotificationPriority_NORMAL
			if _, ok := orderStatusEligibleForHighPriorityNotifications[txnDetail.orderStatus]; ok {
				priority = commsPb.NotificationPriority_HIGH
			}

			if _, ok := orderWorkFlowEligibleForHighPriorityNotifications[txnDetail.orderWorkflow]; ok {
				priority = commsPb.NotificationPriority_HIGH
			}

			for _, curNotification := range curNotifications {
				res = append(res, &commsPb.NotificationMessage{
					Priority:     priority,
					Notification: curNotification,
				})
			}
			return res, nil
		}
	}

	amt := money.ToDisplayString(txnDetail.amount)
	commonFields.NotificationReferenceId = txnDetail.orderId

	switch {
	case user == PAYEE && txnDetail.isInterestTransaction && txnDetail.protocol == paymentPb.PaymentProtocol_INTRA_BANK &&
		txnDetail.orderStatus == orderPb.OrderStatus_PAID:
		if s.notificationParams.CreditInterest().Disable {
			return nil, errorTemplateDisabled
		}
		commonFields.Body = fmt.Sprintf(s.notificationParams.CreditInterest().Body, amt, txnDetail.toAccNo)
		commonFields.Title = s.notificationParams.CreditInterest().Title
		commonFields.IconAttributes = convertToFCMIconAttribute(s.notificationParams.CreditInterest().IconAttr)
		notifType := s.notificationParams.CreditInterest().NotificationType.ToFCMNotificationTypeEnum()
		notifTTL := s.notificationParams.CreditInterest().NotificationExpiry

		fcmNotifications = append(fcmNotifications, initFcmNotificationWithCommonFields(commonFields, notifType, notifTTL))

	case user == PAYEE && txnDetail.isDepositTransaction && txnDetail.protocol == paymentPb.PaymentProtocol_INTRA_BANK &&
		(((txnDetail.orderStatus == orderPb.OrderStatus_PAID || (txnDetail.orderStatus == orderPb.OrderStatus_IN_PAYMENT && txnDetail.creditedAt != nil)) && (txnDetail.orderWorkflow == orderPb.OrderWorkflow_P2P_FUND_TRANSFER || txnDetail.orderWorkflow == orderPb.OrderWorkflow_ADD_FUNDS_SD)) ||
			(txnDetail.orderWorkflow == orderPb.OrderWorkflow_REWARDS_ADD_FUNDS_SD && txnDetail.orderStatus == orderPb.OrderStatus_FULFILLED) ||
			(txnDetail.orderStatus == orderPb.OrderStatus_FULFILLED && txnDetail.orderWorkflow == orderPb.OrderWorkflow_REWARDS_CREATE_SD)):
		if s.notificationParams.CreditDeposit().Disable {
			return nil, errorTemplateDisabled
		}

		commonFields.Body = fmt.Sprintf(s.notificationParams.CreditDeposit().Body, amt, txnDetail.depositName)
		commonFields.Title = fmt.Sprintf(s.notificationParams.CreditDeposit().Title, txnDetail.depositName)
		commonFields.IconAttributes = convertToFCMIconAttribute(s.notificationParams.CreditDeposit().IconAttr)
		commonFields.Deeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_DEPOSIT_ACCOUNT_DETAILS,
			ScreenOptions: &deeplinkPb.Deeplink_DepositDetailsScreenOptions{
				DepositDetailsScreenOptions: &deeplinkPb.DepositAccountDetailsScreenOptions{
					AccountId:   txnDetail.depositAccountId,
					DepositType: accounts.Type_SMART_DEPOSIT,
				},
			},
		}
		notifType := s.notificationParams.CreditDeposit().NotificationType.ToFCMNotificationTypeEnum()
		notifTTL := s.notificationParams.CreditDeposit().NotificationExpiry

		fcmNotifications = append(fcmNotifications, initFcmNotificationWithCommonFields(commonFields, notifType, notifTTL))
	case txnDetail.orderStatus == orderPb.OrderStatus_PAID &&
		user == PAYEE && txnDetail.isDepositTransaction && txnDetail.isPreClosed:
		if s.notificationParams.ClosedDeposit().Disable {
			return nil, errorTemplateDisabled
		}

		commonFields.Body = fmt.Sprintf(s.notificationParams.ClosedDeposit().Body, amt)
		commonFields.Title = fmt.Sprintf(s.notificationParams.ClosedDeposit().Title, txnDetail.depositName)
		commonFields.IconAttributes = convertToFCMIconAttribute(s.notificationParams.ClosedDeposit().IconAttr)
		var accountType accounts.Type
		if txnDetail.depositType == "Fixed" {
			accountType = accounts.Type_FIXED_DEPOSIT
		} else if txnDetail.depositType == "Smart" {
			accountType = accounts.Type_SMART_DEPOSIT
		}
		commonFields.Deeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_DEPOSIT_LANDING_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_DepositAccountLandingScreenOption{
				DepositAccountLandingScreenOption: &deeplinkPb.DepositAccountLandingScreenOptions{
					DepositType: accountType,
				},
			},
		}
		notifType := s.notificationParams.ClosedDeposit().NotificationType.ToFCMNotificationTypeEnum()
		notifTTL := s.notificationParams.ClosedDeposit().NotificationExpiry

		fcmNotifications = append(fcmNotifications, initFcmNotificationWithCommonFields(commonFields, notifType, notifTTL))

	case txnDetail.orderStatus == orderPb.OrderStatus_PAID &&
		user == PAYEE && txnDetail.isDepositTransaction && txnDetail.isAutoClosed:
		if s.notificationParams.MaturedDeposit().Disable {
			return nil, errorTemplateDisabled
		}

		commonFields.Body = fmt.Sprintf(s.notificationParams.MaturedDeposit().Body, amt)
		commonFields.Title = fmt.Sprintf(s.notificationParams.MaturedDeposit().Title, txnDetail.depositName)
		commonFields.IconAttributes = convertToFCMIconAttribute(s.notificationParams.MaturedDeposit().IconAttr)
		var accountType accounts.Type
		if txnDetail.depositType == "Fixed" {
			accountType = accounts.Type_FIXED_DEPOSIT
		} else if txnDetail.depositType == "Smart" {
			accountType = accounts.Type_SMART_DEPOSIT
		}
		commonFields.Deeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_DEPOSIT_LANDING_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_DepositAccountLandingScreenOption{
				DepositAccountLandingScreenOption: &deeplinkPb.DepositAccountLandingScreenOptions{
					DepositType: accountType,
				},
			},
		}
		notifType := s.notificationParams.MaturedDeposit().NotificationType.ToFCMNotificationTypeEnum()
		notifTTL := s.notificationParams.MaturedDeposit().NotificationExpiry

		fcmNotifications = append(fcmNotifications, initFcmNotificationWithCommonFields(commonFields, notifType, notifTTL))
	case (txnDetail.depositType == "Fixed" || txnDetail.depositType == "Smart") &&
		txnDetail.orderStatus == orderPb.OrderStatus_FULFILLMENT_FAILED &&
		(txnDetail.orderWorkflow == orderPb.OrderWorkflow_CREATE_DEPOSIT || txnDetail.orderWorkflow == orderPb.OrderWorkflow_REWARDS_CREATE_SD):
		if s.notificationParams.DepositCreationFailed().Disable {
			return nil, errorTemplateDisabled
		}
		commonFields.Body = s.notificationParams.DepositCreationFailed().Body
		var depositTypeTitle string
		var accountType accounts.Type
		if txnDetail.depositType == "Fixed" {
			depositTypeTitle = "FD"
			accountType = accounts.Type_FIXED_DEPOSIT
		} else if txnDetail.depositType == "Smart" {
			depositTypeTitle = "SD"
			accountType = accounts.Type_SMART_DEPOSIT
		}
		commonFields.Title = fmt.Sprintf(s.notificationParams.DepositCreationFailed().Title, depositTypeTitle)
		commonFields.IconAttributes = convertToFCMIconAttribute(s.notificationParams.DepositCreationFailed().IconAttr)
		commonFields.Deeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_DEPOSIT_ACCOUNT_DETAILS,
			ScreenOptions: &deeplinkPb.Deeplink_DepositDetailsScreenOptions{
				DepositDetailsScreenOptions: &deeplinkPb.DepositAccountDetailsScreenOptions{
					AccountId:   txnDetail.depositAccountId,
					DepositType: accountType,
				},
			},
		}
		notifType := s.notificationParams.DepositCreationFailed().NotificationType.ToFCMNotificationTypeEnum()
		notifTTL := s.notificationParams.DepositCreationFailed().NotificationExpiry
		fcmNotifications = append(fcmNotifications, initFcmNotificationWithCommonFields(commonFields, notifType, notifTTL))
	case txnDetail.isDepositTransaction && user == PAYER && txnDetail.depositType == "Smart" && txnDetail.orderWorkflow == orderPb.OrderWorkflow_P2P_FUND_TRANSFER &&
		txnDetail.orderStatus == orderPb.OrderStatus_PAYMENT_FAILED:
		if s.notificationParams.DepositSdAddFundsFailed().Disable {
			return nil, errorTemplateDisabled
		}

		commonFields.Body = s.notificationParams.DepositSdAddFundsFailed().Body
		commonFields.Title = fmt.Sprintf(s.notificationParams.DepositSdAddFundsFailed().Title, txnDetail.depositName)
		commonFields.IconAttributes = convertToFCMIconAttribute(s.notificationParams.DepositSdAddFundsFailed().IconAttr)
		commonFields.Deeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_DEPOSIT_ACCOUNT_DETAILS,
			ScreenOptions: &deeplinkPb.Deeplink_DepositDetailsScreenOptions{
				DepositDetailsScreenOptions: &deeplinkPb.DepositAccountDetailsScreenOptions{
					AccountId:   txnDetail.depositAccountId,
					DepositType: accounts.Type_SMART_DEPOSIT,
				},
			},
		}
		notifType := s.notificationParams.DepositSdAddFundsFailed().NotificationType.ToFCMNotificationTypeEnum()
		notifTTL := s.notificationParams.DepositSdAddFundsFailed().NotificationExpiry

		fcmNotifications = append(fcmNotifications, initFcmNotificationWithCommonFields(commonFields, notifType, notifTTL))
	case txnDetail.orderWorkflow == orderPb.OrderWorkflow_REWARDS_ADD_FUNDS_SD && txnDetail.orderStatus == orderPb.OrderStatus_FULFILLMENT_FAILED:
		if s.notificationParams.DepositSdAddFundsFailed().Disable {
			return nil, errorTemplateDisabled
		}

		commonFields.Body = s.notificationParams.DepositSdAddFundsFailed().Body
		commonFields.Title = fmt.Sprintf(s.notificationParams.DepositSdAddFundsFailed().Title, txnDetail.depositName)
		commonFields.IconAttributes = convertToFCMIconAttribute(s.notificationParams.DepositSdAddFundsFailed().IconAttr)
		commonFields.Deeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_DEPOSIT_ACCOUNT_DETAILS,
			ScreenOptions: &deeplinkPb.Deeplink_DepositDetailsScreenOptions{
				DepositDetailsScreenOptions: &deeplinkPb.DepositAccountDetailsScreenOptions{
					AccountId:   txnDetail.depositAccountId,
					DepositType: accounts.Type_SMART_DEPOSIT,
				},
			},
		}
		notifType := s.notificationParams.DepositSdAddFundsFailed().NotificationType.ToFCMNotificationTypeEnum()
		notifTTL := s.notificationParams.DepositSdAddFundsFailed().NotificationExpiry

		fcmNotifications = append(fcmNotifications, initFcmNotificationWithCommonFields(commonFields, notifType, notifTTL))
	case txnDetail.orderWorkflow == orderPb.OrderWorkflow_ADD_FUNDS_SD && user == PAYER && txnDetail.orderStatus == orderPb.OrderStatus_PAYMENT_FAILED:
		isFitAddFundsSDTxn := hasFITTag(txnDetail.orderTags)
		if s.notificationParams.DepositSdAddFundsFailed().Disable || isFitAddFundsSDTxn {
			return nil, errorTemplateDisabled
		}

		commonFields.Body = s.notificationParams.DepositSdAddFundsFailed().Body
		commonFields.Title = fmt.Sprintf(s.notificationParams.DepositSdAddFundsFailed().Title, txnDetail.depositName)
		commonFields.IconAttributes = convertToFCMIconAttribute(s.notificationParams.DepositSdAddFundsFailed().IconAttr)
		commonFields.Deeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_DEPOSIT_ACCOUNT_DETAILS,
			ScreenOptions: &deeplinkPb.Deeplink_DepositDetailsScreenOptions{
				DepositDetailsScreenOptions: &deeplinkPb.DepositAccountDetailsScreenOptions{
					AccountId:   txnDetail.depositAccountId,
					DepositType: accounts.Type_SMART_DEPOSIT,
				},
			},
		}
		notifType := s.notificationParams.DepositSdAddFundsFailed().NotificationType.ToFCMNotificationTypeEnum()
		notifTTL := s.notificationParams.DepositSdAddFundsFailed().NotificationExpiry

		fcmNotifications = append(fcmNotifications, initFcmNotificationWithCommonFields(commonFields, notifType, notifTTL))

	case isAddFunds(txnDetail.orderWorkflow) && txnDetail.orderStatus == orderPb.OrderStatus_SETTLED:
		if s.notificationParams.Credit().Disable {
			return nil, errorTemplateDisabled
		}

		commonFields.Body = fmt.Sprintf(s.notificationParams.Credit().Body, txnDetail.fromActorName, amt)
		commonFields.Title = s.notificationParams.Credit().Title
		commonFields.IconAttributes = convertToFCMIconAttribute(s.notificationParams.Credit().IconAttr)
		notifType := s.notificationParams.Credit().NotificationType.ToFCMNotificationTypeEnum()
		notifTTL := s.notificationParams.Credit().NotificationExpiry

		fcmNotifications = append(fcmNotifications, initFcmNotificationWithCommonFields(commonFields, notifType, notifTTL))

	case (txnDetail.orderStatus == orderPb.OrderStatus_PAID ||
		(txnDetail.orderStatus == orderPb.OrderStatus_IN_PAYMENT && txnDetail.debitedAt != nil)) &&
		user == PAYER && txnDetail.protocol == paymentPb.PaymentProtocol_UPI:
		if s.notificationParams.DebitUPI().Disable {
			return nil, errorTemplateDisabled
		}

		if time.Since(txnDetail.eventTime.AsTime()) <= s.notificationParams.DebitUPI().TriggerAfter {
			return nil, errorPnBeforeTriggerAfter
		}
		// Check for creditedAt not being nil to handle for the scenario when the notification is being triggered for a
		// debit transaction, when it gets updated during a refund. For example, if the user makes a payment for authorisation (e.g in Uber),
		// and that payment is then reversed after sometime (say 24h), in that case creditedAt will get updated in the original transaction,
		// and the order update will be triggered for the original debit transaction as well.
		if txnDetail.creditedAt != nil {
			return nil, errorPnTriggeredForDebitTxnDueToRefund
		}

		commonFields.Body = s.notificationParams.DebitUPI().Body
		commonFields.Title = s.notificationParams.DebitUPI().Title
		commonFields.IconAttributes = convertToFCMIconAttribute(s.notificationParams.DebitUPI().IconAttr)
		notifType := s.notificationParams.DebitUPI().NotificationType.ToFCMNotificationTypeEnum()
		notifTTL := s.notificationParams.DebitUPI().NotificationExpiry

		fcmNotifications = append(fcmNotifications, initFcmNotificationWithCommonFields(commonFields, notifType, notifTTL))
	case !txnDetail.isDepositTransaction &&
		!isAddFunds(txnDetail.orderWorkflow) &&
		(txnDetail.orderStatus == orderPb.OrderStatus_PAID ||
			(txnDetail.orderStatus == orderPb.OrderStatus_IN_PAYMENT && txnDetail.creditedAt != nil)) &&
		user == PAYEE && txnDetail.protocol == paymentPb.PaymentProtocol_UPI:
		if s.notificationParams.CreditUPI().Disable {
			return nil, errorTemplateDisabled
		}

		commonFields.Body = fmt.Sprintf(s.notificationParams.CreditUPI().Body, txnDetail.fromActorName, amt)
		commonFields.Title = s.notificationParams.CreditUPI().Title
		commonFields.IconAttributes = convertToFCMIconAttribute(s.notificationParams.CreditUPI().IconAttr)
		notifType := s.notificationParams.CreditUPI().NotificationType.ToFCMNotificationTypeEnum()
		notifTTL := s.notificationParams.CreditUPI().NotificationExpiry

		fcmNotifications = append(fcmNotifications, initFcmNotificationWithCommonFields(commonFields, notifType, notifTTL))
	case txnDetail.orderStatus == orderPb.OrderStatus_PAID && user == PAYER && txnDetail.isTodChargeRelated:
		if s.notificationParams.TodChargesDebit().Disable {
			return nil, errorTemplateDisabled
		}

		commonFields.Body = fmt.Sprintf(s.notificationParams.TodChargesDebit().Body, amt)
		commonFields.Title = s.notificationParams.TodChargesDebit().Title
		commonFields.IconAttributes = convertToFCMIconAttribute(s.notificationParams.TodChargesDebit().IconAttr)
		notifType := s.notificationParams.TodChargesDebit().NotificationType.ToFCMNotificationTypeEnum()
		notifTTL := s.notificationParams.TodChargesDebit().NotificationExpiry

		fcmNotifications = append(fcmNotifications, initFcmNotificationWithCommonFields(commonFields, notifType, notifTTL))
	case txnDetail.orderStatus == orderPb.OrderStatus_PAID && user == PAYER && txnDetail.IsEcsReturnCharges:
		if s.notificationParams.EcsReturnCharges().Disable {
			return nil, errorTemplateDisabled
		}
		commonFields.Body = fmt.Sprintf(s.notificationParams.EcsReturnCharges().Body, amt)
		commonFields.Title = s.notificationParams.EcsReturnCharges().Title
		commonFields.IconAttributes = convertToFCMIconAttribute(s.notificationParams.EcsReturnCharges().IconAttr)
		commonFields.Deeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_EXTERNAL_REDIRECTION,
			ScreenOptions: &deeplinkPb.Deeplink_ExternalRedirectionScreenOptions{
				ExternalRedirectionScreenOptions: &deeplinkPb.ExternalRedirectionScreenOptions{
					ExternalUrl: federal.PortalLink,
				},
			},
		}
		notifType := s.notificationParams.EcsReturnCharges().NotificationType.ToFCMNotificationTypeEnum()
		notifTTL := s.notificationParams.EcsReturnCharges().NotificationExpiry

		fcmNotifications = append(fcmNotifications, initFcmNotificationWithCommonFields(commonFields, notifType, notifTTL))
		if !s.notificationParams.EcsReturnChargesInApp().Disable {
			commonFieldsInApp := proto.Clone(commonFields).(*fcm.CommonTemplateFields)
			commonFieldsInApp.Body = fmt.Sprintf(s.notificationParams.EcsReturnChargesInApp().Body, amt)
			commonFieldsInApp.Title = s.notificationParams.EcsReturnChargesInApp().Title
			commonFieldsInApp.IconAttributes = convertToFCMIconAttribute(s.notificationParams.EcsReturnChargesInApp().IconAttr)
			commonFieldsInApp.Deeplink = &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_EXTERNAL_REDIRECTION,
				ScreenOptions: &deeplinkPb.Deeplink_ExternalRedirectionScreenOptions{
					ExternalRedirectionScreenOptions: &deeplinkPb.ExternalRedirectionScreenOptions{
						ExternalUrl: federal.PortalLink,
					},
				},
			}
			notifTypeInApp := s.notificationParams.EcsReturnChargesInApp().NotificationType.ToFCMNotificationTypeEnum()
			notifTTLInApp := s.notificationParams.EcsReturnChargesInApp().NotificationExpiry
			fcmNotification := initFcmNotificationWithCommonFields(commonFieldsInApp, notifTypeInApp, notifTTLInApp)
			fcmNotification.GetInAppTemplate().NotificationPriority = fcm.InAppNotificationPriority_NOTIFICATION_PRIORITY_CRITICAL
			fcmNotification.GetInAppTemplate().ShowOnHome = commontypes.BooleanEnum_TRUE
			fcmNotifications = append(fcmNotifications, fcmNotification)
		}

	case txnDetail.orderStatus == orderPb.OrderStatus_PAID && user == PAYER && txnDetail.IsAtmDeclineFee:
		if s.notificationParams.AtmDeclineFee().Disable {
			return nil, errorTemplateDisabled
		}

		commonFields.Body = fmt.Sprintf(s.notificationParams.AtmDeclineFee().Body, amt)
		commonFields.Title = s.notificationParams.AtmDeclineFee().Title
		commonFields.IconAttributes = convertToFCMIconAttribute(s.notificationParams.AtmDeclineFee().IconAttr)
		notifType := s.notificationParams.AtmDeclineFee().NotificationType.ToFCMNotificationTypeEnum()
		notifTTL := s.notificationParams.AtmDeclineFee().NotificationExpiry

		fcmNotifications = append(fcmNotifications, initFcmNotificationWithCommonFields(commonFields, notifType, notifTTL))
	case txnDetail.orderStatus == orderPb.OrderStatus_PAID && user == PAYER && txnDetail.IsDuplicateCardFee:
		if s.notificationParams.DuplicateCardFee().Disable {
			return nil, errorTemplateDisabled
		}

		commonFields.Body = fmt.Sprintf(s.notificationParams.DuplicateCardFee().Body, amt)
		commonFields.Title = s.notificationParams.DuplicateCardFee().Title
		commonFields.IconAttributes = convertToFCMIconAttribute(s.notificationParams.DuplicateCardFee().IconAttr)
		notifType := s.notificationParams.DuplicateCardFee().NotificationType.ToFCMNotificationTypeEnum()
		notifTTL := s.notificationParams.DuplicateCardFee().NotificationExpiry

		fcmNotifications = append(fcmNotifications, initFcmNotificationWithCommonFields(commonFields, notifType, notifTTL))
	case txnDetail.orderStatus == orderPb.OrderStatus_PAID && user == PAYER && txnDetail.IsAtmPenalty:
		if s.notificationParams.AtmPenalty().Disable {
			return nil, errorTemplateDisabled
		}

		commonFields.Body = fmt.Sprintf(s.notificationParams.AtmPenalty().Body, amt)
		commonFields.Title = s.notificationParams.AtmPenalty().Title
		commonFields.IconAttributes = convertToFCMIconAttribute(s.notificationParams.AtmPenalty().IconAttr)
		notifType := s.notificationParams.AtmPenalty().NotificationType.ToFCMNotificationTypeEnum()
		notifTTL := s.notificationParams.AtmPenalty().NotificationExpiry

		fcmNotifications = append(fcmNotifications, initFcmNotificationWithCommonFields(commonFields, notifType, notifTTL))
	case txnDetail.orderStatus == orderPb.OrderStatus_PAID && user == PAYER && txnDetail.IsIntATMcharges:
		if s.notificationParams.IntATMcharges().Disable {
			return nil, errorTemplateDisabled
		}

		commonFields.Body = fmt.Sprintf(s.notificationParams.IntATMcharges().Body, amt)
		commonFields.Title = s.notificationParams.IntATMcharges().Title
		commonFields.IconAttributes = convertToFCMIconAttribute(s.notificationParams.IntATMcharges().IconAttr)
		notifType := s.notificationParams.IntATMcharges().NotificationType.ToFCMNotificationTypeEnum()
		notifTTL := s.notificationParams.IntATMcharges().NotificationExpiry

		fcmNotifications = append(fcmNotifications, initFcmNotificationWithCommonFields(commonFields, notifType, notifTTL))
	case txnDetail.orderStatus == orderPb.OrderStatus_PAID && user == PAYER && txnDetail.IsOtherBankAtmTxnCharge:
		if s.notificationParams.OtherBankAtmTxnCharge().Disable {
			return nil, errorTemplateDisabled
		}

		commonFields.Body = fmt.Sprintf(s.notificationParams.OtherBankAtmTxnCharge().Body, amt)
		commonFields.Title = s.notificationParams.OtherBankAtmTxnCharge().Title
		commonFields.IconAttributes = convertToFCMIconAttribute(s.notificationParams.OtherBankAtmTxnCharge().IconAttr)
		notifType := s.notificationParams.OtherBankAtmTxnCharge().NotificationType.ToFCMNotificationTypeEnum()
		notifTTL := s.notificationParams.OtherBankAtmTxnCharge().NotificationExpiry

		fcmNotifications = append(fcmNotifications, initFcmNotificationWithCommonFields(commonFields, notifType, notifTTL))
	case s.disableCommsForFundTransferV1Orders(txnDetail.orderTags) && (txnDetail.orderStatus == orderPb.OrderStatus_PAID ||
		(txnDetail.orderStatus == orderPb.OrderStatus_IN_PAYMENT && txnDetail.debitedAt != nil)) && user == PAYER:
		if s.notificationParams.Debit().Disable {
			return nil, errorTemplateDisabled
		}

		if time.Since(txnDetail.eventTime.AsTime()) <= s.notificationParams.Debit().TriggerAfter {
			return nil, errorPnBeforeTriggerAfter
		}

		commonFields.Body = fmt.Sprintf(s.notificationParams.Debit().Body, amt)
		commonFields.Title = s.notificationParams.Debit().Title
		commonFields.IconAttributes = convertToFCMIconAttribute(s.notificationParams.Debit().IconAttr)
		notifType := s.notificationParams.Debit().NotificationType.ToFCMNotificationTypeEnum()
		notifTTL := s.notificationParams.Debit().NotificationExpiry

		fcmNotifications = append(fcmNotifications, initFcmNotificationWithCommonFields(commonFields, notifType, notifTTL))
	case !txnDetail.isDepositTransaction &&
		!isAddFunds(txnDetail.orderWorkflow) &&
		(txnDetail.orderStatus == orderPb.OrderStatus_PAID ||
			(txnDetail.orderStatus == orderPb.OrderStatus_IN_PAYMENT && txnDetail.creditedAt != nil)) && user == PAYEE:
		if s.notificationParams.Credit().Disable {
			return nil, errorTemplateDisabled
		}

		commonFields.Body = fmt.Sprintf(s.notificationParams.Credit().Body, txnDetail.fromActorName, amt)
		commonFields.Title = s.notificationParams.Credit().Title
		commonFields.IconAttributes = convertToFCMIconAttribute(s.notificationParams.Credit().IconAttr)
		notifType := s.notificationParams.Credit().NotificationType.ToFCMNotificationTypeEnum()
		notifTTL := s.notificationParams.Credit().NotificationExpiry

		fcmNotifications = append(fcmNotifications, initFcmNotificationWithCommonFields(commonFields, notifType, notifTTL))

	case s.disableCommsForFundTransferV1Orders(txnDetail.orderTags) && txnDetail.orderStatus == orderPb.OrderStatus_PAYMENT_FAILED:
		if s.notificationParams.TransactionFailed().Disable {
			return nil, errorTemplateDisabled
		}

		if time.Since(txnDetail.eventTime.AsTime()) <= s.notificationParams.TransactionFailed().TriggerAfter {
			return nil, errorPnBeforeTriggerAfter
		}

		commonFields.Body = fmt.Sprintf(s.notificationParams.TransactionFailed().Body, amt, txnDetail.toActorName)
		commonFields.Title = s.notificationParams.TransactionFailed().Title
		commonFields.IconAttributes = convertToFCMIconAttribute(s.notificationParams.TransactionFailed().IconAttr)
		notifType := s.notificationParams.TransactionFailed().NotificationType.ToFCMNotificationTypeEnum()
		notifTTL := s.notificationParams.TransactionFailed().NotificationExpiry

		fcmNotifications = append(fcmNotifications, initFcmNotificationWithCommonFields(commonFields, notifType, notifTTL))
	case txnDetail.orderStatus == orderPb.OrderStatus_COLLECT_REGISTERED:
		commonFieldsInApp := proto.Clone(commonFields).(*fcm.CommonTemplateFields)

		if s.notificationParams.CollectReceived().Disable {
			return nil, errorTemplateDisabled
		}

		commonFields.Body = fmt.Sprintf(s.notificationParams.CollectReceived().Body, txnDetail.toActorName, amt)
		commonFields.Title = s.notificationParams.CollectReceived().Title
		commonFields.ExpireAt = txnDetail.expireAt
		commonFields.IconAttributes = convertToFCMIconAttribute(s.notificationParams.CollectReceived().IconAttr)
		// this PN can be dismissed by client if user has completed action, so adding suffix to make it unique
		commonFields.NotificationReferenceId += dismissibleNotificationRefIdSuffix
		notifType := s.notificationParams.CollectReceived().NotificationType.ToFCMNotificationTypeEnum()
		notifTTL := time.Duration(0)
		if commonFields.ExpireAt == nil {
			notifTTL = s.notificationParams.CollectReceived().NotificationExpiry
		}

		fcmNotifications = append(fcmNotifications, initFcmNotificationWithCommonFields(commonFields, notifType, notifTTL))

		if !s.notificationParams.CollectReceivedInApp().Disable {
			commonFieldsInApp.Body = fmt.Sprintf(s.notificationParams.CollectReceivedInApp().Body, txnDetail.toActorName, amt)
			commonFieldsInApp.Title = s.notificationParams.CollectReceivedInApp().Title
			commonFieldsInApp.BgColor = s.notificationParams.CollectReceivedInApp().BgColour
			commonFieldsInApp.TitleV2 = commontypes.GetTextFromStringFontColourFontStyle(commonFieldsInApp.Title, "#333333", commontypes.FontStyle_SUBTITLE_S)
			commonFieldsInApp.BodyV2 = commontypes.GetTextFromStringFontColourFontStyle(commonFieldsInApp.Body, "#333333", commontypes.FontStyle_SUBTITLE_S)
			commonFieldsInApp.NotificationShadowColor = &ui.Shadow{
				Colour: &ui.BackgroundColour{
					Colour: &ui.BackgroundColour_BlockColour{
						BlockColour: "#878A8D",
					},
				},
			}
			commonFieldsInApp.NotificationDismissIconColor = &ui.BackgroundColour{
				Colour: &ui.BackgroundColour_BlockColour{
					BlockColour: "#646464",
				},
			}
			commonFieldsInApp.NotificationDismissIconBgColor = &ui.BackgroundColour{
				Colour: &ui.BackgroundColour_BlockColour{
					BlockColour: "#ECEEF0",
				},
			}

			commonFieldsInApp.IconAttributes = convertToFCMIconAttribute(&staticconfig.IconAttribute{
				IconName:   txnDetail.toActorName,
				IconURL:    txnDetail.toActorProfileImgURL,
				ColourCode: actorPb.GetColourCodeForActor(txnDetail.toActorId),
			})

			commonFieldsInApp.ExpireAt = txnDetail.expireAt
			notifType := s.notificationParams.CollectReceivedInApp().NotificationType.ToFCMNotificationTypeEnum()
			notifTTL := time.Duration(0)
			if commonFieldsInApp.ExpireAt == nil {
				notifTTL = s.notificationParams.CollectReceivedInApp().NotificationExpiry
			}
			fcmNotification := initFcmNotificationWithCommonFields(commonFieldsInApp, notifType, notifTTL)
			fcmNotification.GetInAppTemplate().NotificationPriority = fcm.InAppNotificationPriority_NOTIFICATION_PRIORITY_CRITICAL
			fcmNotifications = append(fcmNotifications, fcmNotification)
		}
	case txnDetail.orderStatus == orderPb.OrderStatus_COLLECT_DISMISSED_BY_PAYEE && user == PAYER:

		commonFields.NotificationReferenceId = txnDetail.orderId
		notifType := s.notificationParams.CollectRequestCancelled().NotificationType.ToFCMNotificationTypeEnum()
		notifTTL := time.Duration(0)
		if commonFields.ExpireAt == nil {
			notifTTL = s.notificationParams.CollectRequestCancelled().NotificationExpiry
		}
		fcm := initFcmNotificationWithCommonFields(commonFields, notifType, notifTTL)
		fcm.GetBackgroundTemplate().BackgroundAction = notificationPb.BackgroundAction_DISMISS_COLLECT_REQUEST
		fcmNotifications = append(fcmNotifications, fcm)
	case s.disableCommsForFundTransferV1Orders(txnDetail.orderTags) && txnDetail.orderStatus == orderPb.OrderStatus_PAYMENT_REVERSED && user == PAYER:
		commonFields.Body = fmt.Sprintf(s.notificationParams.TransactionReversed().Body, amt, txnDetail.toActorName)
		commonFields.Title = s.notificationParams.TransactionReversed().Title
		commonFields.IconAttributes = convertToFCMIconAttribute(s.notificationParams.TransactionReversed().IconAttr)
		notifType := s.notificationParams.TransactionReversed().NotificationType.ToFCMNotificationTypeEnum()
		notifTTL := s.notificationParams.TransactionReversed().NotificationExpiry

		fcmNotifications = append(fcmNotifications, initFcmNotificationWithCommonFields(commonFields, notifType, notifTTL))
	default:
		return nil, errorTemplateNotFound
	}

	for _, fcm := range fcmNotifications {
		priority := commsPb.NotificationPriority_NORMAL
		if _, ok := orderStatusEligibleForHighPriorityNotifications[txnDetail.orderStatus]; ok {
			priority = commsPb.NotificationPriority_HIGH
		}

		if _, ok := orderWorkFlowEligibleForHighPriorityNotifications[txnDetail.orderWorkflow]; ok {
			priority = commsPb.NotificationPriority_HIGH
		}

		res = append(res, &commsPb.NotificationMessage{
			Priority:     priority,
			Notification: fcm,
		})
	}
	logger.Debug(ctx, "notification template found", zap.String(logger.NOTIFICATION_TEMPLATE, commonFields.GetTitle()))
	return res, nil
}

func hasFITTag(orderTags []orderPb.OrderTag) bool {
	for _, tag := range orderTags {
		if tag == orderPb.OrderTag_FIT {
			return true
		}
	}
	return false
}

// initFcmNotificationWithCommonFields initializes fcm notification with common fields based on notification type
func initFcmNotificationWithCommonFields(commonFields *fcm.CommonTemplateFields, notificationType fcm.NotificationType, notifTTL time.Duration) *fcm.Notification {
	notification := &fcm.Notification{}

	if notifTTL != 0 {
		commonFields.ExpireAt = timestamppb.New(time.Now().Add(notifTTL))
	}

	switch notificationType { //nolint:exhaustive
	case fcm.NotificationType_IN_APP:
		notification.NotificationTemplates = &fcm.Notification_InAppTemplate{
			InAppTemplate: &fcm.InAppTemplate{
				CommonTemplateFields: commonFields,
				AfterClickAction:     notificationPb.AfterClickAction_PERSIST,
			},
		}
	case fcm.NotificationType_FULL_SCREEN:
		notification.NotificationTemplates = &fcm.Notification_FullscreenTemplate{FullscreenTemplate: &fcm.FullScreenTemplate{CommonTemplateFields: commonFields}}
	case fcm.NotificationType_BACKGROUND:
		notification.NotificationTemplates = &fcm.Notification_BackgroundTemplate{BackgroundTemplate: &fcm.BackgroundTemplate{CommonTemplateFields: commonFields}}
	case fcm.NotificationType_SYSTEM_TRAY:
		notification.NotificationTemplates = &fcm.Notification_SystemTrayTemplate{SystemTrayTemplate: &fcm.SystemTrayTemplate{CommonTemplateFields: commonFields}}
	}

	notification.NotificationType = notificationType
	return notification
}

func convertToFCMIconAttribute(iconAttr *staticconfig.IconAttribute) *fcm.IconAttributes {
	if iconAttr == nil {
		return nil
	}

	return &fcm.IconAttributes{
		IconUrl:         iconAttr.IconURL,
		IconName:        iconAttr.IconName,
		BackgroundColor: iconAttr.ColourCode,
	}
}

// getConfirmationSmsTemplate gets the SMS template needed for sending confirmation SMS.
func (s *Service) getConfirmationSmsTemplate(txnDetail *transactionDetails) (*commsPb.SMSMessage, commsPb.SmsType, commsPb.QoS, error) {
	template := commsPb.SMSMessage{}
	smsOption, smsType, qos, err := s.getConfirmationSMSContent(txnDetail)
	if err != nil {
		return nil, smsType, qos, err
	} else {
		template.SmsOption = smsOption
	}

	return &template, smsType, qos, nil
}

// getSmsTemplate gets the SMS template needed for sending SMS.
func (s *Service) getSmsTemplate(
	ctx context.Context,
	txnDetail *transactionDetails,
	user userType,
	userGrp []commontypes.UserGroup,
	owt *orderPb.OrderWithTransactions,
) (*commsPb.SMSMessage, commsPb.SmsType, commsPb.QoS, error) {
	template := commsPb.SMSMessage{}
	smsOption, smsType, qos, err := s.getSMSContent(ctx, txnDetail, user, userGrp, owt)
	if err != nil {
		return nil, smsType, qos, err
	} else {
		template.SmsOption = smsOption
	}

	return &template, smsType, qos, nil
}

// getEmailTemplate gets the Email template needed for sending Email.
func getEmailTemplate(txnDetail *transactionDetails, user userType) (*commsPb.EmailMessage, commsPb.QoS, error) {
	// TODO(harish) implement getEmailTemplate
	template := commsPb.EmailMessage{}
	return &template, commsPb.QoS_QOS_UNSPECIFIED, nil
}

// getNotificationTemplate gets the notification template needed for sending push notifications. It sets the timeline
// deeplink needed for pay notifications. Also, sets the title for the notification.
func (s *Service) getNotificationTemplates(ctx context.Context, txnDetail *transactionDetails, user userType, owt *orderPb.OrderWithTransactions) ([]*commsPb.NotificationMessage, error) {
	var (
		commonFields = &fcm.CommonTemplateFields{}
	)
	commonFields.Deeplink = s.getNotificationDeeplink(ctx, owt, txnDetail, user)
	templates, err := s.getFcmNotifications(ctx, commonFields, txnDetail, user, owt)
	if err != nil {
		return nil, err
	}

	return templates, nil
}

func (s *Service) getNotificationDeeplink(ctx context.Context, orderWithTxns *orderPb.OrderWithTransactions, txnDetails *transactionDetails, user userType) *deeplinkPb.Deeplink {
	order := orderWithTxns.GetOrder()

	// This is to handle the case when the order has multiple transactions, and we are not sure which transaction to use for payeePiId,
	// hence we are not setting it in the deeplink.
	var payeePiId string
	if len(orderWithTxns.GetTransactions()) == 1 && orderWithTxns.GetTransactions()[0].GetPiTo() != "" {
		payeePiId = orderWithTxns.GetTransactions()[0].GetPiTo()
	}

	notificationDeeplink := &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_TIMELINE,
		ScreenOptions: &deeplinkPb.Deeplink_TimelineScreenOptions{
			TimelineScreenOptions: &deeplinkPb.TimelineScreenOptions{
				TimelineId: txnDetails.timelineId,
				PayeePiId:  payeePiId,
			},
		},
	}
	switch {
	case txnDetails.orderStatus == orderPb.OrderStatus_COLLECT_REGISTERED &&
		(txnDetails.orderWorkflow == orderPb.OrderWorkflow_P2P_COLLECT || txnDetails.orderWorkflow == orderPb.OrderWorkflow_P2P_COLLECT_SHORT_CIRCUIT) && user == PAYER:
		isAmountScreenAllowed, err := s.upiProcessor.ShouldRedirectToAmountScreenFromCollectPN(ctx, order.GetFromActorId())
		if err != nil {
			logger.Error(ctx, "error checking if amount screen should be sent in collect PN", zap.Error(err))
		}
		if isAmountScreenAllowed {
			notificationDeeplink = &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_ENTER_AMOUNT_SCREEN,
				ScreenOptions: &deeplinkPb.Deeplink_EnterAmountScreenOptions{
					EnterAmountScreenOptions: &deeplinkTimelinePb.EnterAmountScreenOptions{
						Timeline: &deeplinkTimelinePb.EnterAmountScreenOptions_Timeline{
							Id: txnDetails.timelineId,
							Chathead: &deeplinkTimelinePb.EnterAmountScreenOptions_Timeline_Chathead{
								TimelineId:            txnDetails.timelineId,
								SecondActorId:         txnDetails.toActorId,
								SecondActorName:       txnDetails.toActorName,
								SecondActorProfileUrl: txnDetails.toActorProfileImgURL,
								ColorCode:             actorPb.GetColourCodeForActor(txnDetails.toActorId),
							},
							IsPayButtonEnabled:     true,
							IsRequestButtonEnabled: false,
						},
						TransactionType: deeplinkTimelinePb.TransactionType_COLLECT_PAY,
						OrderId:         txnDetails.orderId,
						EnterAmountParams: &deeplinkTimelinePb.EnterAmountScreenOptions_EnterAmountParams{
							IsAmountEditable: false,
							Amount:           types.GetFromBeMoney(txnDetails.amount),
							TxnNote:          txnDetails.remarks,
						},
						UiEntryPoint: deeplinkTimelinePb.TransactionUIEntryPoint_UI_ENTRY_POINT_TIMELINE,
						PayeePiId:    payeePiId,
					},
				},
			}
		}
	default:
		return notificationDeeplink
	}
	return notificationDeeplink
}

func (s *Service) getTemplate(ctx context.Context,
	txnDetail *transactionDetails,
	user userType,
	userGrp []commontypes.UserGroup,
	owt *orderPb.OrderWithTransactions) (*commsPb.SMSMessage, *commsPb.EmailMessage, commsPb.SmsType, commsPb.QoS, error) {
	emailTemplate := &commsPb.EmailMessage{}

	smsTemplate, smsType, qos, err := s.getSmsTemplate(ctx, txnDetail, user, userGrp, owt)
	if err != nil {
		return nil, nil, smsType, qos, err
	}

	if s.conf.IsEmailNotificationEnable(ctx) && (smsType == commsPb.SmsType_GENERIC_PI_CREDIT || smsType == commsPb.SmsType_UPI_CREDIT ||
		(txnDetail.IsDebitCardAmcCharge && user == PAYER)) {

		isEligibleForEmail, err := money.CompareV2(txnDetail.amount, s.conf.AmountThresholdForNotification())
		if err != nil {
			return nil, nil, smsType, qos, err
		}

		if isEligibleForEmail != 1 {
			emailTemplate, qos, err = s.getEmailTemplate(ctx, txnDetail, smsTemplate.GetSmsOption(), owt)
			if err != nil {
				return nil, nil, smsType, qos, err
			}
		}
	}

	return smsTemplate, emailTemplate, smsType, qos, nil
}

// initSmsRules will instantiate all the pn rules in an ordered manner. So, the pn eligibility will be checked in
// the order returned by this method . So please maintain the order of the list correctly. For eg. don't put a broader rule
// at a higher position because it will mute the actual specific sms.
func (s *Service) initSmsRules() {
	smsRules = []comms.SmsRule{
		sms_rules.NewDcForexRefundSmsRule(),
		sms_rules.NewChequeCreditSmsRule(),
		sms_rules.NewChequeReversalSmsRule(),
	}
}

// initPnRules will instantiate all the pn rules in an ordered manner. So, the pn eligibility will be checked in
// the order returned by this method . So please maintain the order of the list correctly. For eg. don't put a broader rule
// at a higher position because it will mute the actual specific notification.
func (s *Service) initPnRules() {
	pushNotificationRules = []comms.PushNotificationRule{
		notification_rules.NewDcForexRefundPnRule(s.conf),
		notification_rules.NewChequeCreditRule(s.conf),
		notification_rules.NewChequeReversalRule(s.conf),
		notification_rules.NewAddFundsSecondLegRule(s.conf),
		notification_rules.NewEnachFailedTxnRule(s.conf),
	}
}

// initEmailRules will instantiate all the email rules in an ordered manner. So, the pn eligibility will be checked in
// the order returned by this method . So please maintain the order of the list correctly. For eg. don't put a broader rule
// at a higher position because it will mute the actual specific notification.
func (s *Service) initEmailRules() {
	emailRules = []comms.EmailRule{
		email_rules.NewDcAmcChargesRule(s.cpClient, s.tieringClient),
	}
}

func (s *Service) getEmailTemplate(ctx context.Context, txnDetail *transactionDetails,
	smsOption *commsPb.SmsOption, owt *orderPb.OrderWithTransactions) (*commsPb.EmailMessage, commsPb.QoS, error) {
	template := &commsPb.EmailMessage{}

	for _, emailRule := range emailRules {
		if emailRule.IsEmailApplicable(owt) {

			emailMsg, err := emailRule.GetEmailMessage(ctx, owt)
			if err != nil {
				return nil, commsPb.QoS_QOS_UNSPECIFIED, fmt.Errorf("error while getting email message for emailType: %s, error: %w",
					emailRule.GetEmailType().String(), err)
			}
			return emailMsg, emailRule.GetEmailQos(), nil
		}
	}

	senderDetails := mask.GetMaskedString(mask.DontMaskLastFourChars, txnDetail.fromAccNo)
	if senderDetails == "" && txnDetail.protocol == paymentPb.PaymentProtocol_UPI {
		// For VPA it is fine to send unmasked VPA
		senderDetails = txnDetail.fromUpiVpa
	}
	// TODO(Sundeep): Remove this log once all the cases for empty sender details are handled.
	if senderDetails == "" {
		logger.Info(
			ctx,
			"received empty sender details in txn notification",
			zap.String(logger.ORDER_ID, txnDetail.orderId),
			zap.String(logger.TXN_ID, txnDetail.id),
		)
	}

	if smsOption.GetGenericPiCreditSmsOption() != nil {
		template = &commsPb.EmailMessage{
			FromEmailId:   FromEmailId,
			FromEmailName: FromEmailName,
			EmailOption: &commsPb.EmailOption{
				Option: &commsPb.EmailOption_CreditTxnEmailOption{
					CreditTxnEmailOption: &commsPb.CreditTxnEmailOption{
						EmailType: commsPb.EmailType_CREDIT_TXN_EMAIL,
						Option: &commsPb.CreditTxnEmailOption_CreditTxnEmailOptionV1{
							CreditTxnEmailOptionV1: &commsPb.CreditTxnEmailOptionV1{
								FirstName:              txnDetail.toActorName,
								TxnAmount:              txnDetail.amount,
								TxnDate:                txnDetail.date,
								SenderDetails:          senderDetails,
								LastFourDigitOfAccount: mask.GetMaskedString(mask.DontMaskLastFourChars, txnDetail.toAccNo),
								TemplateVersion:        commsPb.TemplateVersion_VERSION_V1,
							},
						},
					},
				},
			},
		}
	} else if smsOption.GetUpiCreditSmsOption() != nil {
		// fetch bank name and account type for the payee
		bankName, accountType, err := s.getAccountDetailsForVpa(ctx, txnDetail.toUpiVpa)
		if err != nil && !storagev2.IsRecordNotFoundError(err) {
			return nil, commsPb.QoS_QOS_UNSPECIFIED, fmt.Errorf("error while fetching bank name for the payee vpa = %v, err = %w",
				txnDetail.toUpiVpa, err)
		}

		template = &commsPb.EmailMessage{
			FromEmailId:   FromEmailId,
			FromEmailName: FromEmailName,
			EmailOption: &commsPb.EmailOption{
				Option: &commsPb.EmailOption_CreditTxnEmailOption{
					CreditTxnEmailOption: &commsPb.CreditTxnEmailOption{
						EmailType: commsPb.EmailType_CREDIT_TXN_EMAIL,
						Option: &commsPb.CreditTxnEmailOption_CreditTxnEmailOptionV1{
							CreditTxnEmailOptionV1: &commsPb.CreditTxnEmailOptionV1{
								FirstName:              txnDetail.toActorName,
								TxnAmount:              txnDetail.amount,
								TxnDate:                txnDetail.date,
								SenderDetails:          txnDetail.fromUpiVpa,
								LastFourDigitOfAccount: mask.GetMaskedString(mask.DontMaskLastFourChars, txnDetail.toAccNo),
								TemplateVersion:        commsPb.TemplateVersion_VERSION_V1,
								BankName:               bankName,
								AccountType:            accountType,
							},
						},
					},
				},
			},
		}
	}

	return template, commsPb.QoS_GUARANTEED, nil
}

// getAccountDetailsForVpa: fetches the bank name and account type
// for the account corresponding to the given vpa.
func (s *Service) getAccountDetailsForVpa(ctx context.Context, vpa string) (string, string, error) {
	upiAccount, err := s.upiProcessor.GetUpiAccountForVpa(ctx, vpa)
	if err != nil {
		return "", "", err
	}

	accountType := upiAccount.GetAccountType().String()
	accountType = string(accountType[0]) + strings.ToLower(accountType[1:])
	return upiAccount.GetBankName(), accountType, nil
}

//nolint:dupl
func (s *Service) sendSMS(ctx context.Context, smsMsg *commsPb.SMSMessage, entityId string, qoS commsPb.QoS, txnDetails *transactionDetails) (string, error) {
	smsReq := &commsPb.SendMessageRequest{
		Type:                qoS,
		Medium:              commsPb.Medium_SMS,
		UserIdentifier:      &commsPb.SendMessageRequest_UserId{UserId: entityId},
		Message:             &commsPb.SendMessageRequest_Sms{Sms: smsMsg},
		ExternalReferenceId: txnDetails.id,
	}
	sendMessageResponse, err := s.commsClient.SendMessage(ctx, smsReq)
	switch {
	case err != nil:
		logger.Error(ctx, "Failed to send SMS",
			zap.String(logger.ENTITY_ID, entityId),
			zap.Error(err))
		return "", fmt.Errorf("failed to send SMS for entity id %s: %v, %w", entityId, err, queue.ErrTransient)
	case sendMessageResponse.GetStatus().IsRecordNotFound():
		logger.Error(ctx, "Failed to send SMS with status Record not found",
			zap.String(logger.ENTITY_ID, entityId))
		return "", fmt.Errorf("failed to send SMS for entity id %s: %w", entityId, queue.ErrPermanent)
	case sendMessageResponse.GetStatus().IsInvalidArgument():
		logger.Error(ctx, "Failed to send SMS with status Invalid Argument",
			zap.String(logger.ENTITY_ID, entityId))
		return "", fmt.Errorf("failed to send SMS for entity id %s: %w", entityId, queue.ErrPermanent)
	case !sendMessageResponse.GetStatus().IsSuccess():
		logger.Error(ctx, "Failed to send SMS",
			zap.String(logger.ENTITY_ID, entityId))
		return "", fmt.Errorf("failed to send SMS for entity id %s: %w", entityId, queue.ErrTransient)
	}
	return sendMessageResponse.GetMessageId(), err
}

//nolint:dupl
func (s *Service) sendEmail(ctx context.Context, emailMsg *commsPb.EmailMessage, entityId string, txnDetails *transactionDetails, qos commsPb.QoS) (string, error) {
	if exclude, ok := s.conf.CommsConfig().ExcludeUsersForEmail[entityId]; exclude && ok {
		logger.Info(ctx, "cannot send email to the user", zap.String(logger.USER_ID, entityId))
		return "", nil
	}

	emailReq := &commsPb.SendMessageRequest{
		Type:                qos,
		Medium:              commsPb.Medium_EMAIL,
		UserIdentifier:      &commsPb.SendMessageRequest_UserId{UserId: entityId},
		Message:             &commsPb.SendMessageRequest_Email{Email: emailMsg},
		ExternalReferenceId: txnDetails.id,
	}

	sendMessageResponse, err := s.commsClient.SendMessage(ctx, emailReq)
	switch {
	case err != nil:
		return "", fmt.Errorf("failed to send Email for entity id %s: %v, %w", entityId, err, queue.ErrTransient)
	case sendMessageResponse.GetStatus().IsInvalidArgument():
		return "", fmt.Errorf("invalid argument passed while sending email for entity id %s: %w", entityId, queue.ErrPermanent)
	case !sendMessageResponse.GetStatus().IsSuccess():
		return "", fmt.Errorf("non sucess while sending Email for entity id %s: %w", entityId, queue.ErrTransient)
	}
	return sendMessageResponse.GetMessageId(), err
}

//nolint:dupl
func (s *Service) sendBatchPushNotification(ctx context.Context, notificationMsgs []*commsPb.NotificationMessage, entityId string) ([]string, error) {
	var commsList []*commsPb.Communication

	for _, notificationMsgs := range notificationMsgs {
		commsList = append(commsList, &commsPb.Communication{
			Medium:  commsPb.Medium_NOTIFICATION,
			Message: &commsPb.Communication_Notification{Notification: notificationMsgs},
		})
	}

	batchNotificationRequest := &commsPb.SendMessageBatchRequest{
		Type:              commsPb.QoS_GUARANTEED,
		UserIdentifier:    &commsPb.SendMessageBatchRequest_UserId{UserId: entityId},
		CommunicationList: commsList,
	}

	sendMessageBatchResponse, err := s.commsClient.SendMessageBatch(ctx, batchNotificationRequest)

	switch {
	case err != nil:
		return nil, fmt.Errorf("failed to send batch notifications for entity id %s: %v, %w", entityId, err, queue.ErrTransient)
	case sendMessageBatchResponse.GetStatus().IsRecordNotFound():
		return nil, fmt.Errorf("failed to send batch notifications for entity id %s: %w", entityId, queue.ErrPermanent)
	case !sendMessageBatchResponse.GetStatus().IsSuccess():
		return nil, fmt.Errorf("failed to send batch notifications for entity id %s: %w", entityId, queue.ErrTransient)

	}
	return sendMessageBatchResponse.GetMessageIdList(), nil
}

func getLastFourCharForAccountNumber(accountNumber string) string {
	if len(accountNumber) > 4 {
		return accountNumber[len(accountNumber)-4:]
	}
	return accountNumber
}

// returns the sms option to be used based on sms type
func (s *Service) getSmsOption(
	ctx context.Context,
	smsType commsPb.SmsType,
	txnDetail *transactionDetails,
	transactionTimestamp *timestamp.Timestamp,
	userGrp []commontypes.UserGroup,
) (*commsPb.SmsOption, error) {
	var (
		version string
	)

	conf, ok := smsTypeToConfigMap[smsType]
	if !ok {
		return nil, fmt.Errorf("sms type not supported: %s", smsType.String())
	}
	version = conf.Default
	if conf.InternalUser != "" && commontypes.IsUserInGroup(userGrp, commontypes.UserGroup_INTERNAL) {
		version = conf.InternalUser
	}
	if conf.FnfUser != "" && commontypes.IsUserInGroup(userGrp, commontypes.UserGroup_FNF) {
		version = conf.FnfUser
	}
	switch version {
	case version1:
		return getSmsOptionForVersion1(ctx, smsType, txnDetail, transactionTimestamp)
	case version2:
		return getSmsOptionForVersion2(ctx, smsType, txnDetail, transactionTimestamp)
	default:
		return nil, fmt.Errorf("sms version not supported: %s", version)
	}

}

// nolint:funlen
func getSmsOptionForVersion1(
	ctx context.Context,
	smsType commsPb.SmsType,
	txnDetail *transactionDetails,
	transactionTimestamp *timestamp.Timestamp,
) (*commsPb.SmsOption, error) {
	switch smsType {

	case commsPb.SmsType_POS_DEBIT_FALLBACK:
		return &commsPb.SmsOption{
			Option: &commsPb.SmsOption_PosDebitFallbackSmsOption{
				PosDebitFallbackSmsOption: &commsPb.PosDebitFallbackSmsOption{
					SmsType: commsPb.SmsType_POS_DEBIT_FALLBACK,
					Option: &commsPb.PosDebitFallbackSmsOption_PosDebitSmsOptionV1{
						PosDebitSmsOptionV1: &commsPb.PosDebitFallbackSmsOptionV1{
							TemplateVersion:      commsPb.TemplateVersion_VERSION_V1,
							TransactionAmount:    txnDetail.amount,
							TransactionTimestamp: transactionTimestamp,
						},
					},
				},
			},
		}, nil
	case commsPb.SmsType_CASH_WITHDRAWAL_ATM_FALLBACK:
		return &commsPb.SmsOption{
			Option: &commsPb.SmsOption_CashWithdrawalAtmFallbackSmsOption{
				CashWithdrawalAtmFallbackSmsOption: &commsPb.CashWithdrawalAtmFallbackSmsOption{
					SmsType: commsPb.SmsType_CASH_WITHDRAWAL_ATM_FALLBACK,
					Option: &commsPb.CashWithdrawalAtmFallbackSmsOption_CashWithdrawalAtmSmsOptionV1{
						CashWithdrawalAtmSmsOptionV1: &commsPb.CashWithdrawalAtmFallbackSmsOptionV1{
							TemplateVersion:      commsPb.TemplateVersion_VERSION_V1,
							WithdrawnAmount:      txnDetail.amount,
							TransactionTimestamp: transactionTimestamp,
						},
					},
				},
			},
		}, nil
	case commsPb.SmsType_INTEREST_PAID_IN_SB_FALLBACK:
		return &commsPb.SmsOption{
			Option: &commsPb.SmsOption_InterestPaidInSbFallbackSmsOption{
				InterestPaidInSbFallbackSmsOption: &commsPb.InterestPaidInSBFallbackSmsOption{
					SmsType: commsPb.SmsType_INTEREST_PAID_IN_SB_FALLBACK,
					Option: &commsPb.InterestPaidInSBFallbackSmsOption_InterestPaidInSbSmsOptionV1{
						InterestPaidInSbSmsOptionV1: &commsPb.InterestPaidInSBFallbackSmsOptionV1{
							TemplateVersion:             commsPb.TemplateVersion_VERSION_V1,
							InterestAmount:              txnDetail.amount,
							Timestamp:                   transactionTimestamp,
							AccountNumberLastFourDigits: txnDetail.toAccNo[len(txnDetail.toAccNo)-4:],
						},
					},
				},
			},
		}, nil
	case commsPb.SmsType_RTGS_DEBIT_FALLBACK:
		return &commsPb.SmsOption{
			Option: &commsPb.SmsOption_RtgsDebitFallbackSmsOption{
				RtgsDebitFallbackSmsOption: &commsPb.RtgsDebitFallbackSmsOption{
					SmsType: commsPb.SmsType_RTGS_DEBIT_FALLBACK,
					Option: &commsPb.RtgsDebitFallbackSmsOption_RtgsDebitSmsOptionV1{
						RtgsDebitSmsOptionV1: &commsPb.RtgsDebitFallbackSmsOptionV1{
							TemplateVersion:      commsPb.TemplateVersion_VERSION_V1,
							TransactionAmount:    txnDetail.amount,
							TransactionTimestamp: transactionTimestamp,
							Name:                 names.ParseString(txnDetail.fromActorName),
							ReceiverName:         names.ParseString(txnDetail.toActorName),
							ReferenceNumber:      txnDetail.utr,
							PaymentMode:          txnDetail.protocol.String(),
						},
					},
				},
			},
		}, nil
	case commsPb.SmsType_NEFT_DEBIT_FALLBACK:
		return &commsPb.SmsOption{
			Option: &commsPb.SmsOption_NeftDebitFallbackSmsOption{
				NeftDebitFallbackSmsOption: &commsPb.NeftDebitFallbackSmsOption{
					SmsType: commsPb.SmsType_NEFT_DEBIT_FALLBACK,
					Option: &commsPb.NeftDebitFallbackSmsOption_NeftDebitSmsOptionV1{
						NeftDebitSmsOptionV1: &commsPb.NeftDebitFallbackSmsOptionV1{
							TemplateVersion:      commsPb.TemplateVersion_VERSION_V1,
							TransactionAmount:    txnDetail.amount,
							TransactionTimestamp: transactionTimestamp,
							Name:                 names.ParseString(txnDetail.fromActorName),
							ReceiverName:         names.ParseString(txnDetail.toActorName),
							PaymentMode:          txnDetail.protocol.String(),
							ReferenceNumber:      txnDetail.utr,
						},
					},
				},
			},
		}, nil
	case commsPb.SmsType_NEFT_CREDIT_FALLBACK:
		return &commsPb.SmsOption{
			Option: &commsPb.SmsOption_NeftCreditFallbackSmsOption{
				NeftCreditFallbackSmsOption: &commsPb.NeftCreditFallbackSmsOption{
					SmsType: commsPb.SmsType_NEFT_CREDIT_FALLBACK,
					Option: &commsPb.NeftCreditFallbackSmsOption_NeftCreditSmsOptionV1{
						NeftCreditSmsOptionV1: &commsPb.NeftCreditFallbackSmsOptionV1{
							TemplateVersion:      commsPb.TemplateVersion_VERSION_V1,
							TransactionAmount:    txnDetail.amount,
							TransactionTimestamp: transactionTimestamp,
							Name:                 names.ParseString(txnDetail.toActorName),
							SenderName:           names.ParseString(txnDetail.fromActorName),
							ReferenceNumber:      txnDetail.utr,
							PaymentMode:          txnDetail.protocol.String(),
						},
					},
				},
			},
		}, nil
	case commsPb.SmsType_GENERIC_PI_CREDIT:
		return &commsPb.SmsOption{
			Option: &commsPb.SmsOption_GenericPiCreditSmsOption{
				GenericPiCreditSmsOption: &commsPb.GenericPiCreditSmsOption{
					SmsType: commsPb.SmsType_GENERIC_PI_CREDIT,
					Option: &commsPb.GenericPiCreditSmsOption_GenericPiCreditSmsOptionV1{
						GenericPiCreditSmsOptionV1: &commsPb.GenericPiCreditSmsOptionV1{
							TemplateVersion:             commsPb.TemplateVersion_VERSION_V1,
							TransactionAmount:           txnDetail.amount,
							TransactionTimestamp:        transactionTimestamp,
							Name:                        names.ParseString(txnDetail.toActorName),
							SenderName:                  names.ParseString(txnDetail.fromActorName),
							AccountNumberLastFourDigits: getLastFourCharForAccountNumber(txnDetail.toAccNo),
						},
					},
				},
			},
		}, nil
	case commsPb.SmsType_GENERIC_PI_DEBIT:
		return &commsPb.SmsOption{
			Option: &commsPb.SmsOption_GenericPiDebitSmsOption{
				GenericPiDebitSmsOption: &commsPb.GenericPiDebitSmsOption{
					SmsType: commsPb.SmsType_GENERIC_PI_DEBIT,
					Option: &commsPb.GenericPiDebitSmsOption_GenericPiDebitSmsOptionV1{
						GenericPiDebitSmsOptionV1: &commsPb.GenericPiDebitSmsOptionV1{
							TemplateVersion:             commsPb.TemplateVersion_VERSION_V1,
							TransactionAmount:           txnDetail.amount,
							TransactionTimestamp:        transactionTimestamp,
							Name:                        names.ParseString(txnDetail.fromActorName),
							SenderName:                  names.ParseString(txnDetail.toActorName),
							AccountNumberLastFourDigits: getLastFourCharForAccountNumber(txnDetail.fromAccNo),
							SenderPi:                    txnDetail.toAccNo,
						},
					},
				},
			},
		}, nil
	case commsPb.SmsType_UPI_DEBIT:
		toPi := txnDetail.toAccNo
		if txnDetail.toPiType == piPb.PaymentInstrumentType_UPI {
			if txnDetail.toUpiVpa != "" {
				toPi = txnDetail.toUpiVpa
			} else {
				logger.Debug(ctx, "vpa null for upi txn with toPi of type UPI")
			}
		}
		return &commsPb.SmsOption{
			Option: &commsPb.SmsOption_UpiDebitSmsOption{
				UpiDebitSmsOption: &commsPb.UpiDebitSmsOption{
					SmsType: commsPb.SmsType_UPI_DEBIT,
					Option: &commsPb.UpiDebitSmsOption_UpiDebitSmsOptionV1{
						UpiDebitSmsOptionV1: &commsPb.UpiDebitSmsOptionV1{
							TemplateVersion:             commsPb.TemplateVersion_VERSION_V1,
							TransactionAmount:           txnDetail.amount,
							TransactionTimestamp:        transactionTimestamp,
							Name:                        names.ParseString(txnDetail.fromActorName),
							SenderPi:                    toPi,
							AccountNumberLastFourDigits: getLastFourCharForAccountNumber(txnDetail.fromAccNo),
						},
					},
				},
			},
		}, nil
	case commsPb.SmsType_UPI_CREDIT:
		return &commsPb.SmsOption{
			Option: &commsPb.SmsOption_UpiCreditSmsOption{
				UpiCreditSmsOption: &commsPb.UpiCreditSmsOption{
					SmsType: commsPb.SmsType_UPI_CREDIT,
					Option: &commsPb.UpiCreditSmsOption_UpiCreditSmsOptionV1{
						UpiCreditSmsOptionV1: &commsPb.UpiCreditSmsOptionV1{
							TemplateVersion:             commsPb.TemplateVersion_VERSION_V1,
							TransactionAmount:           txnDetail.amount,
							TransactionTimestamp:        transactionTimestamp,
							UpiId:                       txnDetail.toUpiVpa,
							Name:                        names.ParseString(txnDetail.toActorName),
							SenderName:                  names.ParseString(txnDetail.fromActorName),
							AccountNumberLastFourDigits: getLastFourCharForAccountNumber(txnDetail.toAccNo),
							SenderUpiId:                 txnDetail.fromUpiVpa,
						},
					},
				},
			},
		}, nil
	case commsPb.SmsType_GENERIC_PI_DEBIT_UNCLEAR_BENEFICARY_DETAILS:
		return &commsPb.SmsOption{
			Option: &commsPb.SmsOption_GenericPiDebitUnclearBeneficaryDetailsSmsOption{
				GenericPiDebitUnclearBeneficaryDetailsSmsOption: &commsPb.GenericPiDebitUnclearBeneficiaryDetailsSmsOption{
					SmsType: commsPb.SmsType_GENERIC_PI_DEBIT_UNCLEAR_BENEFICARY_DETAILS,
					Option: &commsPb.GenericPiDebitUnclearBeneficiaryDetailsSmsOption_GenericPiDebitUnclearBeneficaryDetailsSmsOptionV1{
						GenericPiDebitUnclearBeneficaryDetailsSmsOptionV1: &commsPb.GenericPiDebitUnclearBeneficiaryDetailsSmsOptionV1{
							TemplateVersion:             commsPb.TemplateVersion_VERSION_V1,
							TransactionAmount:           txnDetail.amount,
							TransactionTimestamp:        transactionTimestamp,
							AccountNumberLastFourDigits: getLastFourCharForAccountNumber(txnDetail.fromAccNo),
						},
					},
				},
			},
		}, nil
	case commsPb.SmsType_TOD_CHARGES_DEBIT:
		return &commsPb.SmsOption{
			Option: &commsPb.SmsOption_TodChargesDebitOption{
				TodChargesDebitOption: &commsPb.TodChargesDebitOption{
					SmsType: commsPb.SmsType_TOD_CHARGES_DEBIT,
					Option: &commsPb.TodChargesDebitOption_TodChargesDebitOptionV1{
						TodChargesDebitOptionV1: &commsPb.TodChargesDebitOptionV1{
							TemplateVersion:      commsPb.TemplateVersion_VERSION_V1,
							TransactionAmount:    txnDetail.amount,
							TransactionTimestamp: transactionTimestamp,
						},
					},
				},
			},
		}, nil
	case commsPb.SmsType_TRANSACTION_REVERSED:
		return &commsPb.SmsOption{
			Option: &commsPb.SmsOption_TransactionReversedSmsOption{
				TransactionReversedSmsOption: &commsPb.TransactionReversedSmsOption{
					SmsType: commsPb.SmsType_TRANSACTION_REVERSED,
					Option: &commsPb.TransactionReversedSmsOption_TransactionReversedV1{
						TransactionReversedV1: &commsPb.TransactionReversedSmsOptionV1{
							TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
							Amount:          txnDetail.amount,
						},
					},
				},
			},
		}, nil
	case commsPb.SmsType_ECS_RETURN_CHARGES:
		return &commsPb.SmsOption{
			Option: &commsPb.SmsOption_EcsReturnChargesSmsOption{
				EcsReturnChargesSmsOption: &commsPb.EcsReturnChargesSmsOption{
					SmsType: commsPb.SmsType_ECS_RETURN_CHARGES,
					Option: &commsPb.EcsReturnChargesSmsOption_EcsReturnChargesSmsOptionV1{
						EcsReturnChargesSmsOptionV1: &commsPb.EcsReturnChargesSmsOptionV1{
							TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
							Amount:          txnDetail.amount,
						},
					},
				},
			},
		}, nil
	case commsPb.SmsType_ATM_DECLINE_FEES:
		return &commsPb.SmsOption{
			Option: &commsPb.SmsOption_AtmDeclineFeesSmsOption{
				AtmDeclineFeesSmsOption: &commsPb.AtmDeclineFeesSmsOption{
					SmsType: commsPb.SmsType_ATM_DECLINE_FEES,
					Option: &commsPb.AtmDeclineFeesSmsOption_AtmDeclineFeesSmsOptionV1{
						AtmDeclineFeesSmsOptionV1: &commsPb.AtmDeclineFeesSmsOptionV1{
							TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
							Amount:          txnDetail.amount,
						},
					},
				},
			},
		}, nil
	case commsPb.SmsType_DUPLICATE_CARD_FEE:
		return &commsPb.SmsOption{
			Option: &commsPb.SmsOption_DuplicateCardFeeSmsOption{
				DuplicateCardFeeSmsOption: &commsPb.DuplicateCardFeeSmsOption{
					SmsType: commsPb.SmsType_DUPLICATE_CARD_FEE,
					Option: &commsPb.DuplicateCardFeeSmsOption_DuplicateCardFeeSmsOptionV1{
						DuplicateCardFeeSmsOptionV1: &commsPb.DuplicateCardFeeSmsOptionV1{
							TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
							Amount:          txnDetail.amount,
						},
					},
				},
			},
		}, nil
	case commsPb.SmsType_ATM_WITHDRAWAL_COMPLAINT_PENALTY:
		return &commsPb.SmsOption{
			Option: &commsPb.SmsOption_AtmWithdrawalComplaintPenaltySmsOption{
				AtmWithdrawalComplaintPenaltySmsOption: &commsPb.AtmWithdrawalComplaintPenaltySmsOption{
					SmsType: commsPb.SmsType_ATM_WITHDRAWAL_COMPLAINT_PENALTY,
					Option: &commsPb.AtmWithdrawalComplaintPenaltySmsOption_AtmWithdrawalComplaintPenaltySmsOptionV1{
						AtmWithdrawalComplaintPenaltySmsOptionV1: &commsPb.AtmWithdrawalComplaintPenaltySmsOptionV1{
							TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
							Amount:          txnDetail.amount,
						},
					},
				},
			},
		}, nil
	case commsPb.SmsType_INTERNATIONAL_ATM_CHARGES:
		return &commsPb.SmsOption{
			Option: &commsPb.SmsOption_InternationAtmChargesSmsOption{
				InternationAtmChargesSmsOption: &commsPb.InternationAtmChargesSmsOption{
					SmsType: commsPb.SmsType_INTERNATIONAL_ATM_CHARGES,
					Option: &commsPb.InternationAtmChargesSmsOption_InternationAtmChargesSmsOptionV1{
						InternationAtmChargesSmsOptionV1: &commsPb.InternationAtmChargesSmsOptionV1{
							TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
							Amount:          txnDetail.amount,
						},
					},
				},
			},
		}, nil
	case commsPb.SmsType_OTHER_BANK_ATM_USAGE_CHARGES:
		return &commsPb.SmsOption{
			Option: &commsPb.SmsOption_OtherBankAtmUsageChargesSmsOption{
				OtherBankAtmUsageChargesSmsOption: &commsPb.OtherBankAtmUsageChargesSmsOption{
					SmsType: commsPb.SmsType_OTHER_BANK_ATM_USAGE_CHARGES,
					Option: &commsPb.OtherBankAtmUsageChargesSmsOption_OtherBankAtmUsageChargesSmsOptionV1{
						OtherBankAtmUsageChargesSmsOptionV1: &commsPb.OtherBankAtmUsageChargesSmsOptionV1{
							TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
							Amount:          txnDetail.amount,
						},
					},
				},
			},
		}, nil
	default:
		return nil, fmt.Errorf("sms type not supported for version 1: %s", smsType.String())
	}
}

// nolint:funlen
func getSmsOptionForVersion2(
	ctx context.Context,
	smsType commsPb.SmsType,
	txnDetail *transactionDetails,
	transactionTimestamp *timestamp.Timestamp,
) (*commsPb.SmsOption, error) {
	switch smsType {
	case commsPb.SmsType_GENERIC_PI_CREDIT:
		fromPi := getLastFourCharForAccountNumber(txnDetail.fromAccNo)
		if fromPi == "" {
			fromPi = txnDetail.fromActorName
		}
		return &commsPb.SmsOption{
			Option: &commsPb.SmsOption_GenericPiCreditSmsOption{
				GenericPiCreditSmsOption: &commsPb.GenericPiCreditSmsOption{
					SmsType: commsPb.SmsType_GENERIC_PI_CREDIT,
					Option: &commsPb.GenericPiCreditSmsOption_GenericPiCreditSmsOptionV2{
						GenericPiCreditSmsOptionV2: &commsPb.GenericPiCreditSmsOptionV2{
							TemplateVersion:             commsPb.TemplateVersion_VERSION_V2,
							TransactionAmount:           txnDetail.amount,
							TransactionTimestamp:        transactionTimestamp,
							Name:                        names.ParseString(txnDetail.toActorName),
							AccountNumberLastFourDigits: getLastFourCharForAccountNumber(txnDetail.toAccNo),
							SenderPi:                    fromPi,
						},
					},
				},
			},
		}, nil
	case commsPb.SmsType_GENERIC_PI_DEBIT:
		return &commsPb.SmsOption{
			Option: &commsPb.SmsOption_GenericPiDebitSmsOption{
				GenericPiDebitSmsOption: &commsPb.GenericPiDebitSmsOption{
					SmsType: commsPb.SmsType_GENERIC_PI_DEBIT,
					Option: &commsPb.GenericPiDebitSmsOption_GenericPiDebitSmsOptionV2{
						GenericPiDebitSmsOptionV2: &commsPb.GenericPiDebitSmsOptionV2{
							TemplateVersion:             commsPb.TemplateVersion_VERSION_V2,
							TransactionAmount:           txnDetail.amount,
							TransactionTimestamp:        transactionTimestamp,
							AccountNumberLastFourDigits: getLastFourCharForAccountNumber(txnDetail.fromAccNo),
							SenderPi:                    txnDetail.toAccNo,
						},
					},
				},
			},
		}, nil
	case commsPb.SmsType_UPI_DEBIT:
		toPi := txnDetail.toAccNo
		if txnDetail.toPiType == piPb.PaymentInstrumentType_UPI {
			if txnDetail.toUpiVpa != "" {
				toPi = txnDetail.toUpiVpa
			} else {
				logger.Debug(ctx, "vpa null for upi txn with toPi of type UPI")
			}
		}
		return &commsPb.SmsOption{
			Option: &commsPb.SmsOption_UpiDebitSmsOption{
				UpiDebitSmsOption: &commsPb.UpiDebitSmsOption{
					SmsType: commsPb.SmsType_UPI_DEBIT,
					Option: &commsPb.UpiDebitSmsOption_UpiDebitSmsOptionV2{
						UpiDebitSmsOptionV2: &commsPb.UpiDebitSmsOptionV2{
							TemplateVersion:             commsPb.TemplateVersion_VERSION_V2,
							TransactionAmount:           txnDetail.amount,
							TransactionTimestamp:        transactionTimestamp,
							SenderPi:                    toPi,
							AccountNumberLastFourDigits: getLastFourCharForAccountNumber(txnDetail.fromAccNo),
						},
					},
				},
			},
		}, nil
	case commsPb.SmsType_UPI_CREDIT:
		return &commsPb.SmsOption{
			Option: &commsPb.SmsOption_UpiCreditSmsOption{
				UpiCreditSmsOption: &commsPb.UpiCreditSmsOption{
					SmsType: commsPb.SmsType_UPI_CREDIT,
					Option: &commsPb.UpiCreditSmsOption_UpiCreditSmsOptionV2{
						UpiCreditSmsOptionV2: &commsPb.UpiCreditSmsOptionV2{
							TemplateVersion:             commsPb.TemplateVersion_VERSION_V2,
							TransactionAmount:           txnDetail.amount,
							TransactionTimestamp:        transactionTimestamp,
							SenderName:                  names.ParseString(txnDetail.fromActorName),
							AccountNumberLastFourDigits: getLastFourCharForAccountNumber(txnDetail.toAccNo),
							SenderUpiId:                 txnDetail.fromUpiVpa,
						},
					},
				},
			},
		}, nil
	default:
		return nil, fmt.Errorf("sms type not supported for version 2: %s", smsType.String())
	}
}

func isAddFunds(workflow orderPb.OrderWorkflow) bool {
	return workflow == orderPb.OrderWorkflow_ADD_FUNDS_COLLECT || workflow == orderPb.OrderWorkflow_ADD_FUNDS
}

// ProcessCollectOrderNotification sends notifications for collect order
// nolint: funlen
func (s *Service) ProcessCollectOrderNotification(ctx context.Context, req *orderPb.OrderUpdate) (
	*orderPb.ProcessCollectOrderNotificationResponse, error) {
	var (
		res = &orderPb.ProcessCollectOrderNotificationResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{},
		}
		payerUserGrp []commontypes.UserGroup
		payeeUserGrp []commontypes.UserGroup
	)

	order := req.GetOrderWithTransactions().GetOrder()
	if order == nil {
		logger.Error(ctx, "Received nil order in ProcessOrderNotification")
		res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_PERMANENT_FAILURE
		return res, nil
	}

	logger.Info(ctx, "processing collect order notification packet", zap.String(logger.ORDER_ID, order.GetId()))
	numberOfTransactions := len(req.GetOrderWithTransactions().GetTransactions())
	if numberOfTransactions == 0 {
		logger.Error(ctx, "ProcessCollectOrderNotification(): No transactions found in order",
			zap.String(logger.ORDER_ID, order.GetId()))
		res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_PERMANENT_FAILURE
		return res, nil
	}

	txn := req.GetOrderWithTransactions().GetTransactions()[numberOfTransactions-1]
	txnDetails := getTransactionDetailsForCollectOrder(txn, order)
	entityRes, err := s.getActorDetails(ctx, order.GetFromActorId())
	if err != nil {
		logger.Error(ctx, "failed to get from actor name and entity id",
			zap.String(logger.ACTOR_ID, order.GetFromActorId()), zap.String(logger.ORDER_ID, order.GetId()),
			zap.Error(err))
		res.ResponseHeader.Status = queue.GetStatusFrom(err)
		return res, nil
	}
	txnDetails.fromActorName = entityRes.GetName().ToString()
	fromEntityId := entityRes.GetEntityId()
	if entityRes.GetType() == types.ActorType_USER {
		payerUserGrp, err = s.userProcessor.GetUserGroupsByActorID(ctx, order.GetFromActorId())
		if err != nil {
			logger.Debug(ctx, "error fetching user grp for actor", zap.String(logger.ACTOR_ID, order.GetFromActorId()),
				zap.String(logger.ORDER_ID, order.GetId()), zap.Error(err))
		}
	}

	entityRes, err = s.getActorDetails(ctx, order.GetToActorId())
	if err != nil {
		logger.Error(ctx, "failed to get to actor name and entity id",
			zap.String(logger.ACTOR_ID, order.GetToActorId()), zap.String(logger.ORDER_ID, order.GetId()),
			zap.Error(err))
		res.ResponseHeader.Status = queue.GetStatusFrom(err)
		return res, nil
	}
	txnDetails.toActorName = entityRes.GetName().ToString()
	toEntityId := entityRes.GetEntityId()
	if entityRes.GetType() == types.ActorType_USER {
		payeeUserGrp, err = s.userProcessor.GetUserGroupsByActorID(ctx, order.GetToActorId())
		if err != nil {
			logger.Debug(ctx, "error fetching user grp for actor", zap.String(logger.ACTOR_ID, order.GetToActorId()),
				zap.String(logger.ORDER_ID, order.GetId()), zap.Error(err))
		}
	}

	var timeline *timelinePb.Timeline
	if timeline, err = s.getTimeline(ctx, order.GetFromActorId(), order.GetToActorId()); err != nil {
		logger.Debug(ctx, "Failed to fetch timeline",
			zap.String("primaryActorId", order.GetFromActorId()),
			zap.String("secondaryActorId", order.GetToActorId()),
			zap.Error(err))
	}

	txnDetails.timelineId = timeline.GetId()

	if fromEntityId != "" && !txnDetails.isPayerExternal {
		if _, _, err := s.notifyUser(ctx, txnDetails, fromEntityId, PAYER, payerUserGrp, req.GetOrderWithTransactions()); err != nil {
			res.ResponseHeader.Status = queue.GetStatusFrom(err)
			logger.Error(ctx, "error notifying user", zap.Error(err))
			return res, nil
		}
	}
	if toEntityId != "" && !txnDetails.isPayeeExternal {
		if _, _, err := s.notifyUser(ctx, txnDetails, toEntityId, PAYEE, payeeUserGrp, req.GetOrderWithTransactions()); err != nil {
			res.ResponseHeader.Status = queue.GetStatusFrom(err)
			logger.Error(ctx, "error notifying user", zap.Error(err))
			return res, nil
		}
	}

	res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_SUCCESS
	return res, nil
}

// ProcessOrderNotificationFallback sends notifications to actors based on the mediums for the transaction status.
// It uses the Comms service for sending all types of notifications.
// Flow-
// 1) Get the transaction details needed for notifications such as Actor names, Utr, amount, date.
// 2) Get the message templates for each mode (SMS, Email, Push notification) based on the payment protocol and order status.
// 3) Send appropriate message via SMS, Email, Notification for both Payee and Payer
// nolint: funlen
func (s *Service) ProcessOrderNotificationFallback(ctx context.Context, req *orderPb.OrderUpdate) (
	*orderPb.ProcessOrderNotificationFallbackResponse, error) {
	var (
		res = &orderPb.ProcessOrderNotificationFallbackResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{},
		}
		fromEntityId   string
		toEntityId     string
		txnDetails     = &transactionDetails{}
		currentUserGrp []commontypes.UserGroup
		payerUserGrp   []commontypes.UserGroup
		payeeUserGrp   []commontypes.UserGroup
		err            error
	)
	order := req.GetOrderWithTransactions().GetOrder()
	if order == nil {
		logger.Error(ctx, "Received nil order in ProcessOrderNotification")
		res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_PERMANENT_FAILURE
		return res, nil
	}

	logger.Debug(ctx, "processing order notification fallback packet", zap.String(logger.ORDER_ID, order.GetId()))

	shouldIgnore, err := s.shouldIgnoreNotification(ctx, order, req.GetOrderWithTransactions().GetTransactions())
	switch {
	case err != nil:
		logger.Error(ctx, "error in checking condition for ignoring notification", zap.Error(err))
		res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE
		return res, nil
	case shouldIgnore:
		res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_SUCCESS
		return res, nil
	}

	numberOfTransactions := len(req.GetOrderWithTransactions().GetTransactions())
	txnDetails.orderWorkflow = order.Workflow
	txnDetails.orderTags = order.GetTags()

	// For all Order workflows other than COLLECT_SHORT_CIRCUIT, we get both order and transaction details in the
	// OrderUpdate request message. Hence, a check is needed to validate if the number of transactions is not zero.
	// For the exception case of COLLECT_SHORT_CIRCUIT workflow, we skip this check, and fetch the amount details
	// from the order and proceed with sending appropriate notifications
	if numberOfTransactions == 0 {
		switch order.GetWorkflow() {
		case orderPb.OrderWorkflow_P2P_COLLECT_SHORT_CIRCUIT:
			// if number of txns is 0, fetching amount details from order
			txnDetails.amount = order.GetAmount()
			// adding payment protocol information
			payload := &orderPb.P2PCollectShortCircuit{}
			if err = protojson.Unmarshal(order.GetOrderPayload(), payload); err != nil {
				logger.Error(ctx, "ProcessOrderNotification(): failed to get protocol from order payload",
					zap.String(logger.ORDER_ID, order.GetId()))
				res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_PERMANENT_FAILURE
				return res, nil
			}
			txnDetails.protocol = payload.GetPaymentDetails().GetPaymentProtocol()
			txnDetails.orderId = order.GetId()
			txnDetails.expireAt = order.GetExpireAt()
			txnDetails.eventTime = order.GetCreatedAt()
			txnDetails.toActorId = order.GetToActorId()
		case orderPb.OrderWorkflow_CREATE_DEPOSIT:
			payload := &depositPb.DepositInfo{}
			if err = protojson.Unmarshal(order.GetOrderPayload(), payload); err != nil {
				logger.Error(ctx, "ProcessOrderNotification(): failed to get deposit type from order payload",
					zap.String(logger.ORDER_ID, order.GetId()))
				res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_PERMANENT_FAILURE
				return res, nil
			}
			txnDetails.amount = order.GetAmount()
			txnDetails.orderId = order.GetId()
			txnDetails.expireAt = order.GetExpireAt()
			txnDetails.eventTime = order.GetCreatedAt()
			txnDetails.depositType = depositTypeMap[payload.GetType()]
		case orderPb.OrderWorkflow_REWARDS_CREATE_SD:
			txnDetails.amount = order.GetAmount()
			txnDetails.orderId = order.GetId()
			txnDetails.expireAt = order.GetExpireAt()
			txnDetails.eventTime = order.GetCreatedAt()
			txnDetails.depositType = depositTypeMap[accounts.Type_SMART_DEPOSIT]
		default:
			logger.Error(ctx, "ProcessOrderNotification(): No transactions found in order",
				zap.String(logger.ORDER_ID, order.GetId()))
			res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_PERMANENT_FAILURE
			return res, nil
		}
	} else {
		var txn *paymentPb.Transaction
		// handling add_fund here as we earlier convention was we only create second leg after first leg creation
		// but we are observing high number of failures in first leg of txn in add_funds
		// so raised a fix where order of 1st and 2nd doesn't matter
		if req.GetOrderWithTransactions().GetOrder().GetWorkflow() == orderPb.OrderWorkflow_ADD_FUNDS {
			for _, iterateTxn := range req.GetOrderWithTransactions().GetTransactions() {
				if upiPkg.IsAddFundsPi(iterateTxn.GetPiFrom()) {
					txn = iterateTxn
					break
				}
			}
		} else {
			txn = req.GetOrderWithTransactions().GetTransactions()[numberOfTransactions-1]
		}
		if txn == nil {
			txn = req.GetOrderWithTransactions().GetTransactions()[numberOfTransactions-1]
		}
		txnDetails, err = s.getTransactionDetails(ctx, txn, order)
		if err != nil {
			logger.Error(ctx, "failed to get transaction details",
				zap.Error(err))
			res.ResponseHeader.Status = queue.GetStatusFrom(err)
			return res, nil
		}
	}

	defer func() {
		var isAlertStringLoggable bool
		if res.ResponseHeader.Status == queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE {
			fromTimestamp := order.GetCreatedAt()
			if txnDetails.creditedAt != nil {
				fromTimestamp = txnDetails.creditedAt
			} else if txnDetails.debitedAt != nil {
				fromTimestamp = txnDetails.debitedAt
			}
			isAlertStringLoggable = time.Since(fromTimestamp.AsTime()) > alertCutoff

		} else if res.ResponseHeader.Status == queuePb.MessageConsumptionStatus_PERMANENT_FAILURE {
			isAlertStringLoggable = true
		}
		if isAlertStringLoggable {
			logger.Debug(ctx, alertString, zap.String(logger.ORDER_ID, txnDetails.orderId), zap.String(logger.TXN_ID, txnDetails.id), zap.Error(err))
		}
	}()

	txnDetails.orderStatus = order.GetStatus()
	if orderPb.IsTagExist(order.GetTags(), orderPb.OrderTag_DEPOSIT) {
		txnDetails.isDepositTransaction = true
	}
	if orderPb.IsTagExist(order.GetTags(), orderPb.OrderTag_INTEREST) {
		txnDetails.isInterestTransaction = true
	}
	entityRes, err := s.getActorDetails(ctx, order.GetFromActorId())
	if err != nil {
		logger.Error(ctx, "failed to get from actor name and entity id",
			zap.String(logger.ACTOR_ID, order.GetFromActorId()), zap.String(logger.ORDER_ID, order.GetId()),
			zap.Error(err))
		res.ResponseHeader.Status = queue.GetStatusFrom(err)
		return res, nil
	}
	if entityRes.GetType() == types.ActorType_USER {
		payerUserGrp, err = s.userProcessor.GetUserGroupsByActorID(ctx, order.GetFromActorId())
		if err != nil {
			logger.Debug(ctx, "error fetching user grp for actor", zap.String(logger.ACTOR_ID, order.GetFromActorId()),
				zap.String(logger.ORDER_ID, order.GetId()), zap.Error(err))
		}
	}
	txnDetails.fromActorName = entityRes.GetName().ToString()
	fromEntityId = entityRes.GetEntityId()

	entityRes, err = s.getActorDetails(ctx, order.GetToActorId())
	if err != nil {
		logger.Error(ctx, "failed to get to actor name and entity id",
			zap.String(logger.ACTOR_ID, order.GetToActorId()), zap.String(logger.ORDER_ID, order.GetId()),
			zap.Error(err))
		res.ResponseHeader.Status = queue.GetStatusFrom(err)
		return res, nil
	}
	if entityRes.GetType() == types.ActorType_USER {
		payeeUserGrp, err = s.userProcessor.GetUserGroupsByActorID(ctx, order.GetToActorId())
		if err != nil {
			logger.Debug(ctx, "error fetching user grp for actor", zap.String(logger.ACTOR_ID, order.GetToActorId()),
				zap.String(logger.ORDER_ID, order.GetId()), zap.Error(err))
		}
	}
	txnDetails.toActorName = entityRes.GetName().ToString()
	toEntityId = entityRes.GetEntityId()
	txnDetails.toActorProfileImgURL = entityRes.GetProfileImageUrl()

	var timeline *timelinePb.Timeline
	if timeline, err = s.getTimeline(ctx, order.GetFromActorId(), order.GetToActorId()); err != nil {
		logger.Debug(ctx, "Failed to fetch timeline",
			zap.String("primaryActorId", order.GetFromActorId()),
			zap.String("secondaryActorId", order.GetToActorId()),
			zap.Error(err))
	}

	txnDetails.timelineId = timeline.GetId()
	logger.Debug(ctx, "order txnID to be processed", zap.String(logger.TXN_ID, txnDetails.id))

	if txnDetails.isDepositTransaction {
		entityId := fromEntityId
		currentUserGrp = payerUserGrp
		if txnDetails.savingAccountUserType == PAYEE {
			entityId = toEntityId
			currentUserGrp = payeeUserGrp
		}
		if entityId != "" {
			if _, _, err = s.notifyUser(ctx, txnDetails, entityId, txnDetails.savingAccountUserType, currentUserGrp, req.GetOrderWithTransactions()); err != nil {
				res.ResponseHeader.Status = queue.GetStatusFrom(err)
				logger.Error(ctx, "error notifying user", zap.Error(err))
				return res, nil
			} else {
				res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_SUCCESS
			}
		}
		// if order belongs to deposit add funds, trigger add funds SD SMS, CreditDeposit notification too
		if (order.Workflow == orderPb.OrderWorkflow_P2P_FUND_TRANSFER || order.Workflow == orderPb.OrderWorkflow_REWARDS_ADD_FUNDS_SD ||
			order.Workflow == orderPb.OrderWorkflow_ADD_FUNDS_SD || order.Workflow == orderPb.OrderWorkflow_REWARDS_CREATE_SD) && toEntityId != "" {
			if _, _, err = s.notifyUser(ctx, txnDetails, toEntityId, PAYEE, currentUserGrp, req.GetOrderWithTransactions()); err != nil {
				logger.Error(ctx, "error notifying user", zap.Error(err))
				res.ResponseHeader.Status = queue.GetStatusFrom(err)
			} else {
				res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_SUCCESS
			}
		}
		return res, nil
	}

	// entity id being non-null is a identifier of it being a user
	// this is a quick fix, we should derive this logic based on
	// actor type and that should be returned from GetActorEntityDetails itself
	// TODO(nitesh): revisit and fix this after changing the GetActorEntityDetails RPC
	if fromEntityId != "" && !txnDetails.isPayerExternal {
		if _, _, err := s.notifyUser(ctx, txnDetails, fromEntityId, PAYER, payerUserGrp, req.GetOrderWithTransactions()); err != nil {
			res.ResponseHeader.Status = queue.GetStatusFrom(err)
			logger.Error(ctx, "error notifying user", zap.Error(err))
			return res, nil
		}
	}
	if toEntityId != "" && !txnDetails.isPayeeExternal {
		if _, _, err := s.notifyUser(ctx, txnDetails, toEntityId, PAYEE, payeeUserGrp, req.GetOrderWithTransactions()); err != nil {
			res.ResponseHeader.Status = queue.GetStatusFrom(err)
			logger.Error(ctx, "error notifying user", zap.Error(err))
			return res, nil
		}
	}

	res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_SUCCESS
	return res, nil
}

func getTransactionDetailsForCollectOrder(txn *paymentPb.Transaction, order *orderPb.Order) *transactionDetails {
	txnDetails := &transactionDetails{}
	txnDetails.id = txn.GetId()
	txnDetails.orderId = order.GetId()
	txnDetails.orderStatus = order.GetStatus()
	txnDetails.toActorId = order.GetToActorId()
	txnDetails.provenance = order.GetProvenance()
	txnDetails.orderWorkflow = order.GetWorkflow()
	txnDetails.expireAt = order.GetExpireAt()
	txnDetails.amount = txn.GetAmount()
	txnDetails.protocol = txn.GetPaymentProtocol()
	txnDetails.eventTime = txn.GetCreatedAt()
	txnDetails.debitedAt = txn.GetDebitedAt()
	txnDetails.creditedAt = txn.GetCreditedAt()
	txnDetails.txnLastUpdatedAt = txn.GetUpdatedAt()
	txnDetails.remarks = txn.GetRemarks()
	return txnDetails
}

// shouldIgnoreNotification will return true in case we do not need to send notification.
// Ignoring notification condition could be different for different workflow.
// TODO(harish): add proper handling for deposit interest notifications
//
//	Currently we are ignoring sending notifications for such orders
//	Monorail: https://monorail.pointz.in/p/fi-app/issues/detail?id=19473
func (s *Service) shouldIgnoreNotification(ctx context.Context, order *orderPb.Order, txns []*paymentPb.Transaction) (bool, error) {
	if order.GetWorkflow() == orderPb.OrderWorkflow_NO_OP &&
		orderPb.IsTagExist(order.GetTags(), orderPb.OrderTag_INTEREST) &&
		orderPb.IsTagExist(order.GetTags(), orderPb.OrderTag_DEPOSIT) {
		return true, nil
	}

	if order.GetWorkflow() == orderPb.OrderWorkflow_P2P_FUND_TRANSFER && orderPb.IsTagExist(order.GetTags(), orderPb.OrderTag_LOAN) {
		var (
			txn *paymentPb.Transaction
		)

		if len(txns) > 0 {
			txn = txns[len(txns)-1]
		}
		getPiRes, err := s.piClient.GetPiById(ctx, &piPb.GetPiByIdRequest{
			Id: txn.GetPiTo(),
		})
		if getPiResErr := epifigrpc.RPCError(getPiRes, err); getPiResErr != nil {
			// TODO (ASHUTOSH):  to clean this logline after debugging. (ETA: 12-13 Aug 2025)
			logger.Error(ctx, "debug log line: ", zap.String(logger.ORDER_ID, order.GetId()), zap.String(logger.TXN_ID, txn.GetId()), zap.String(logger.PI_TO, txn.GetPiTo()))
			return false, fmt.Errorf("error in getting pi details %w", getPiResErr)
		}

		if getPiRes.GetPaymentInstrument().GetAccount().GetAccountType() == accounts.Type_LOAN_ACCOUNT {
			return true, nil
		}
	}
	return false, nil
}

func (s *Service) disableCommsForFundTransferV1Orders(orderTags []orderPb.OrderTag) bool {
	return (s.conf.FeatureFlags().DisableCommsForP2PFundTransfer() && !lo.Contains(orderTags, orderPb.OrderTag_FUND_TRANSFER_V1)) ||
		!s.conf.FeatureFlags().DisableCommsForP2PFundTransfer()
}

func (s *Service) isUSStocksDividendTransaction(orderTags []orderPb.OrderTag) bool {
	return lo.Contains(orderTags, orderPb.OrderTag_US_STOCKS_DIVIDEND_CREDIT)
}

func (s *Service) isUSStocksGstTransaction(orderTags []orderPb.OrderTag) bool {
	return lo.Contains(orderTags, orderPb.OrderTag_US_STOCKS_DIVIDEND_GST_DEBIT) ||
		lo.Contains(orderTags, orderPb.OrderTag_US_STOCKS_GST)
}

// checks if transaction is for us-stocks dividend gst refund
func (s *Service) isUSStocksGstRefundTransaction(orderTags []orderPb.OrderTag) bool {
	return lo.Contains(orderTags, orderPb.OrderTag_US_STOCKS_INWARD_REMITTANCE_GST_REFUND)
}
