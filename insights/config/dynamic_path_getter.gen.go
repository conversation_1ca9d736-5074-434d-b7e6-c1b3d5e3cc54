// Code generated by tools/conf_gen/dynamic_conf_gen.go
package config

import (
	"fmt"
	"strings"
)

func (obj *Config) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "useremailaccesssubscriber":
		return obj.UserEmailAccessSubscriber.Get(dynamicFieldPath[1:])
	case "periodicemailsyncsubscriber":
		return obj.PeriodicEmailSyncSubscriber.Get(dynamicFieldPath[1:])
	case "maildataparsersubscriber":
		return obj.MailDataParserSubscriber.Get(dynamicFieldPath[1:])
	case "onboardingstageupdateeventsubscriber":
		return obj.OnboardingStageUpdateEventSubscriber.Get(dynamicFieldPath[1:])
	case "flags":
		return obj.Flags.Get(dynamicFieldPath[1:])
	case "insightserving":
		return obj.InsightServing.Get(dynamicFieldPath[1:])
	case "createorupdategenerationstatussubscriber":
		return obj.CreateOrUpdateGenerationStatusSubscriber.Get(dynamicFieldPath[1:])
	case "storegeneratedactorinsightssubscriber":
		return obj.StoreGeneratedActorInsightsSubscriber.Get(dynamicFieldPath[1:])
	case "realtimeinsightparams":
		return obj.RealTimeInsightParams.Get(dynamicFieldPath[1:])
	case "generateinsightssubscriber":
		return obj.GenerateInsightsSubscriber.Get(dynamicFieldPath[1:])
	case "storyparams":
		return obj.StoryParams.Get(dynamicFieldPath[1:])
	case "epfparams":
		return obj.EpfParams.Get(dynamicFieldPath[1:])
	case "networthparams":
		return obj.NetworthParams.Get(dynamicFieldPath[1:])
	case "networthrefreshparams":
		return obj.NetWorthRefreshParams.Get(dynamicFieldPath[1:])
	case "pmssearchconfig":
		return obj.PMSSearchConfig.Get(dynamicFieldPath[1:])
	case "aifsearchconfig":
		return obj.AIFSearchConfig.Get(dynamicFieldPath[1:])
	case "epfpassbookdataflatteningsubscriber":
		return obj.EpfPassbookDataFlatteningSubscriber.Get(dynamicFieldPath[1:])
	case "uanaccountscacheconfig":
		return obj.UANAccountsCacheConfig.Get(dynamicFieldPath[1:])
	case "processcanewdatafetcheventsubscriber":
		return obj.ProcessCaNewDataFetchEventSubscriber.Get(dynamicFieldPath[1:])
	case "llmhandlerconfig":
		return obj.LLMHandlerConfig.Get(dynamicFieldPath[1:])
	case "magicimportconfig":
		return obj.MagicImportConfig.Get(dynamicFieldPath[1:])
	case "networthmcp":
		return obj.NetworthMcp.Get(dynamicFieldPath[1:])
	case "deletecaassethistorieseventsubscriber":
		return obj.DeleteCaAssetHistoriesEventSubscriber.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Config", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Flags) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "realtimeinsightfeatureflag":
		return obj.RealTimeInsightFeatureFlag.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Flags", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *InsightServing) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "insightcappersession":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"InsightCapPerSession\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.InsightCapPerSession, nil
	case "actorinsightrecurrenceminduration":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ActorInsightRecurrenceMinDuration\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ActorInsightRecurrenceMinDuration, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for InsightServing", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *RealTimeInsightParams) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "realtimeinsightprob":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"RealTimeInsightProb\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.RealTimeInsightProb, nil
	case "segmentcooldownperiod":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SegmentCoolDownPeriod\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SegmentCoolDownPeriod, nil
	case "generatorpriority":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.GeneratorPriority, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"GeneratorPriority\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.GeneratorPriority[dynamicFieldPath[1]], nil

		}
		return obj.GeneratorPriority, nil
	case "insightgenerationlogevalparams":
		return obj.InsightGenerationLogEvalParams.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for RealTimeInsightParams", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *InsightGenerationLogEvalParams) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isevaluationenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEvaluationEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEvaluationEnabled, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for InsightGenerationLogEvalParams", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *StoryParams) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "islottieexperimentenabledforinternal":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsLottieExperimentEnabledForInternal\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsLottieExperimentEnabledForInternal, nil
	case "lottieexperimenturl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"LottieExperimentUrl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.LottieExperimentUrl, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for StoryParams", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *EpfParams) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "passbooksfetchtimeout":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PassbooksFetchTimeout\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.PassbooksFetchTimeout, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for EpfParams", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *NetworthParams) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "useschemeanalyticsapiformfaggval":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"UseSchemeAnalyticsApiForMfAggVal\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.UseSchemeAnalyticsApiForMfAggVal, nil
	case "debugactoridsfordailyreport":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.DebugActorIdsForDailyReport, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"DebugActorIdsForDailyReport\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.DebugActorIdsForDailyReport[dynamicFieldPath[1]], nil

		}
		return obj.DebugActorIdsForDailyReport, nil
	case "assetprice":
		return obj.AssetPrice.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for NetworthParams", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *AssetPrice) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "gold18caratpergramrs":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Gold18CaratPerGramRs\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Gold18CaratPerGramRs, nil
	case "gold22caratpergramrs":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Gold22CaratPerGramRs\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Gold22CaratPerGramRs, nil
	case "gold24caratpergramrs":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Gold24CaratPerGramRs\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Gold24CaratPerGramRs, nil
	case "silverpriceperkgrs":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SilverPricePerKgRs\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SilverPricePerKgRs, nil
	case "benchmarkgoldetfmfid":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BenchmarkGoldEtfMFId\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BenchmarkGoldEtfMFId, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for AssetPrice", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *NetWorthRefreshParams) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "assetsrefreshthreshold":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.AssetsRefreshThreshold, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"AssetsRefreshThreshold\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.AssetsRefreshThreshold[dynamicFieldPath[1]], nil

		}
		return obj.AssetsRefreshThreshold, nil
	case "assetsprocessingrefreshthreshold":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.AssetsProcessingRefreshThreshold, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"AssetsProcessingRefreshThreshold\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.AssetsProcessingRefreshThreshold[dynamicFieldPath[1]], nil

		}
		return obj.AssetsProcessingRefreshThreshold, nil
	case "manualassetsrefreshthreshold":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.ManualAssetsRefreshThreshold, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"ManualAssetsRefreshThreshold\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.ManualAssetsRefreshThreshold[dynamicFieldPath[1]], nil

		}
		return obj.ManualAssetsRefreshThreshold, nil
	case "instrumentsrefreshorder":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"InstrumentsRefreshOrder\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.InstrumentsRefreshOrder, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for NetWorthRefreshParams", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *SearchConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "querysize":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"QuerySize\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.QuerySize, nil
	case "ingestinsync":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IngestInSync\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IngestInSync, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for SearchConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *UANAccountsCacheConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "iscachingenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsCachingEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsCachingEnabled, nil
	case "cachettl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CacheTTL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CacheTTL, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for UANAccountsCacheConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *MagicImportConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "usecachedsystemcontext":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"UseCachedSystemContext\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.UseCachedSystemContext, nil
	case "enableimagestorage":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableImageStorage\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableImageStorage, nil
	case "modelname":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ModelName\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ModelName, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for MagicImportConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *NetworthMcp) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "networthdatafilename":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"NetWorthDataFileName\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.NetWorthDataFileName, nil
	case "filedatadescription":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FileDataDescription\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FileDataDescription, nil
	case "networthfiledescription":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"NetWorthFileDescription\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.NetWorthFileDescription, nil
	case "mfholdingsfiledescription":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MfHoldingsFileDescription\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MfHoldingsFileDescription, nil
	case "aafiledescription":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AaFileDescription\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AaFileDescription, nil
	case "epfdescription":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EpfDescription\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EpfDescription, nil
	case "creditreportdescription":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CreditReportDescription\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CreditReportDescription, nil
	case "mftransactionsdescription":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MfTransactionsDescription\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MfTransactionsDescription, nil
	case "mftransactionresponseschemadesc":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MfTransactionResponseSchemaDesc\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MfTransactionResponseSchemaDesc, nil
	case "banktransactionfiledescription":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BankTransactionFileDescription\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BankTransactionFileDescription, nil
	case "banktransactionsresponseschemadesc":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BankTransactionsResponseSchemaDesc\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BankTransactionsResponseSchemaDesc, nil
	case "manualassetsfiledescription":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ManualAssetsFileDescription\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ManualAssetsFileDescription, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for NetworthMcp", strings.Join(dynamicFieldPath, "."))
	}
}
