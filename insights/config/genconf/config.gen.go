// Code generated by tools/conf_gen/conf_gen.go
package genconf

import (
	"fmt"
	"reflect"
	"strings"
	"sync"
	"sync/atomic"

	time "time"

	questtypes "github.com/epifi/be-common/api/quest/types"
	cfg "github.com/epifi/be-common/pkg/cfg"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	gencfg "github.com/epifi/be-common/pkg/cfg/genconf"
	v "github.com/epifi/be-common/pkg/cfg/v2"
	roarray "github.com/epifi/be-common/pkg/data_structs/roarray"
	syncmap "github.com/epifi/be-common/pkg/syncmap"
	questsdk "github.com/epifi/be-common/quest/sdk"
	helper "github.com/epifi/be-common/tools/conf_gen/helper"
	config "github.com/epifi/gamma/insights/config"
	genconfig "github.com/epifi/gamma/pkg/llm/config/genconf"
)

type Config struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_UserEmailAccessSubscriber                *gencfg.SqsSubscriber
	_PeriodicEmailSyncSubscriber              *gencfg.SqsSubscriber
	_MailDataParserSubscriber                 *gencfg.SqsSubscriber
	_OnboardingStageUpdateEventSubscriber     *gencfg.SqsSubscriber
	_Flags                                    *Flags
	_InsightServing                           *InsightServing
	_CreateOrUpdateGenerationStatusSubscriber *gencfg.SqsSubscriber
	_StoreGeneratedActorInsightsSubscriber    *gencfg.SqsSubscriber
	_RealTimeInsightParams                    *RealTimeInsightParams
	_GenerateInsightsSubscriber               *gencfg.SqsSubscriber
	_StoryParams                              *StoryParams
	_EpfParams                                *EpfParams
	_NetworthParams                           *NetworthParams
	_NetWorthRefreshParams                    *NetWorthRefreshParams
	_PMSSearchConfig                          *SearchConfig
	_AIFSearchConfig                          *SearchConfig
	_EpfPassbookDataFlatteningSubscriber      *gencfg.SqsSubscriber
	_UANAccountsCacheConfig                   *UANAccountsCacheConfig
	_ProcessCaNewDataFetchEventSubscriber     *gencfg.SqsSubscriber
	_LLMHandlerConfig                         *genconfig.Config
	_MagicImportConfig                        *MagicImportConfig
	_NetworthMcp                              *NetworthMcp
	_Application                              *config.Application
	_Server                                   *config.Server
	_Logging                                  *cfg.Logging
	_InsightsDb                               *cfg.DB
	_ActorInsightsDb                          *cfg.DB
	_DbConfigMap                              *cfg.DbConfigMap
	_GmailBatchGetApiParams                   *config.GmailBatchGetApiParams
	_EmailParserParams                        *config.EmailParserParams
	_UserEmailAccessPublisher                 *cfg.SqsPublisher
	_PeriodicEmaiSyncPublisher                *cfg.SqsPublisher
	_MailDataParserPublisher                  *cfg.SqsPublisher
	_GmailUserSpendsPublisher                 *cfg.SnsPublisher
	_EpfPassbookImportEventPublisher          *cfg.SnsPublisher
	_AWS                                      *cfg.AWS
	_Secrets                                  *cfg.Secrets
	_GoogleOAuthParams                        *config.GoogleOAuthParams
	_GmailListApiParams                       *config.GmailListApiParams
	_MailFetchConcurrencyParams               *config.MailFetchConcurrencyParams
	_AddGmailAccountBannerParams              *config.AddGmailAccountBannerParams
	_Tracing                                  *cfg.Tracing
	_Profiling                                *cfg.Profiling
	_SecureLoggingV2                          *v.SecureLogging
}

func (obj *Config) UserEmailAccessSubscriber() *gencfg.SqsSubscriber {
	return obj._UserEmailAccessSubscriber
}
func (obj *Config) PeriodicEmailSyncSubscriber() *gencfg.SqsSubscriber {
	return obj._PeriodicEmailSyncSubscriber
}
func (obj *Config) MailDataParserSubscriber() *gencfg.SqsSubscriber {
	return obj._MailDataParserSubscriber
}
func (obj *Config) OnboardingStageUpdateEventSubscriber() *gencfg.SqsSubscriber {
	return obj._OnboardingStageUpdateEventSubscriber
}
func (obj *Config) Flags() *Flags {
	return obj._Flags
}
func (obj *Config) InsightServing() *InsightServing {
	return obj._InsightServing
}
func (obj *Config) CreateOrUpdateGenerationStatusSubscriber() *gencfg.SqsSubscriber {
	return obj._CreateOrUpdateGenerationStatusSubscriber
}
func (obj *Config) StoreGeneratedActorInsightsSubscriber() *gencfg.SqsSubscriber {
	return obj._StoreGeneratedActorInsightsSubscriber
}
func (obj *Config) RealTimeInsightParams() *RealTimeInsightParams {
	return obj._RealTimeInsightParams
}
func (obj *Config) GenerateInsightsSubscriber() *gencfg.SqsSubscriber {
	return obj._GenerateInsightsSubscriber
}
func (obj *Config) StoryParams() *StoryParams {
	return obj._StoryParams
}
func (obj *Config) EpfParams() *EpfParams {
	return obj._EpfParams
}
func (obj *Config) NetworthParams() *NetworthParams {
	return obj._NetworthParams
}
func (obj *Config) NetWorthRefreshParams() *NetWorthRefreshParams {
	return obj._NetWorthRefreshParams
}
func (obj *Config) PMSSearchConfig() *SearchConfig {
	return obj._PMSSearchConfig
}
func (obj *Config) AIFSearchConfig() *SearchConfig {
	return obj._AIFSearchConfig
}
func (obj *Config) EpfPassbookDataFlatteningSubscriber() *gencfg.SqsSubscriber {
	return obj._EpfPassbookDataFlatteningSubscriber
}
func (obj *Config) UANAccountsCacheConfig() *UANAccountsCacheConfig {
	return obj._UANAccountsCacheConfig
}
func (obj *Config) ProcessCaNewDataFetchEventSubscriber() *gencfg.SqsSubscriber {
	return obj._ProcessCaNewDataFetchEventSubscriber
}
func (obj *Config) LLMHandlerConfig() *genconfig.Config {
	return obj._LLMHandlerConfig
}
func (obj *Config) MagicImportConfig() *MagicImportConfig {
	return obj._MagicImportConfig
}
func (obj *Config) NetworthMcp() *NetworthMcp {
	return obj._NetworthMcp
}
func (obj *Config) Application() *config.Application {
	return obj._Application
}
func (obj *Config) Server() *config.Server {
	return obj._Server
}
func (obj *Config) Logging() *cfg.Logging {
	return obj._Logging
}
func (obj *Config) InsightsDb() *cfg.DB {
	return obj._InsightsDb
}
func (obj *Config) ActorInsightsDb() *cfg.DB {
	return obj._ActorInsightsDb
}
func (obj *Config) DbConfigMap() *cfg.DbConfigMap {
	return obj._DbConfigMap
}
func (obj *Config) GmailBatchGetApiParams() *config.GmailBatchGetApiParams {
	return obj._GmailBatchGetApiParams
}
func (obj *Config) EmailParserParams() *config.EmailParserParams {
	return obj._EmailParserParams
}
func (obj *Config) UserEmailAccessPublisher() *cfg.SqsPublisher {
	return obj._UserEmailAccessPublisher
}
func (obj *Config) PeriodicEmaiSyncPublisher() *cfg.SqsPublisher {
	return obj._PeriodicEmaiSyncPublisher
}
func (obj *Config) MailDataParserPublisher() *cfg.SqsPublisher {
	return obj._MailDataParserPublisher
}
func (obj *Config) GmailUserSpendsPublisher() *cfg.SnsPublisher {
	return obj._GmailUserSpendsPublisher
}
func (obj *Config) EpfPassbookImportEventPublisher() *cfg.SnsPublisher {
	return obj._EpfPassbookImportEventPublisher
}
func (obj *Config) AWS() *cfg.AWS {
	return obj._AWS
}
func (obj *Config) Secrets() *cfg.Secrets {
	return obj._Secrets
}
func (obj *Config) GoogleOAuthParams() *config.GoogleOAuthParams {
	return obj._GoogleOAuthParams
}
func (obj *Config) GmailListApiParams() *config.GmailListApiParams {
	return obj._GmailListApiParams
}
func (obj *Config) MailFetchConcurrencyParams() *config.MailFetchConcurrencyParams {
	return obj._MailFetchConcurrencyParams
}
func (obj *Config) AddGmailAccountBannerParams() *config.AddGmailAccountBannerParams {
	return obj._AddGmailAccountBannerParams
}
func (obj *Config) Tracing() *cfg.Tracing {
	return obj._Tracing
}
func (obj *Config) Profiling() *cfg.Profiling {
	return obj._Profiling
}
func (obj *Config) SecureLoggingV2() *v.SecureLogging {
	return obj._SecureLoggingV2
}

type Flags struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_RealTimeInsightFeatureFlag *gencfg.FeatureReleaseConfig
	_TrimDebugMessageFromStatus bool
	_ShowTeaserInsights         bool
}

func (obj *Flags) RealTimeInsightFeatureFlag() *gencfg.FeatureReleaseConfig {
	return obj._RealTimeInsightFeatureFlag
}
func (obj *Flags) TrimDebugMessageFromStatus() bool {
	return obj._TrimDebugMessageFromStatus
}
func (obj *Flags) ShowTeaserInsights() bool {
	return obj._ShowTeaserInsights
}

type InsightServing struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_InsightCapPerSession int64
	// Minimum duration for which an actor insight can't be re-served once it is consumed by the user.
	_ActorInsightRecurrenceMinDuration int64
}

func (obj *InsightServing) InsightCapPerSession() int {
	return int(atomic.LoadInt64(&obj._InsightCapPerSession))
}

// Minimum duration for which an actor insight can't be re-served once it is consumed by the user.
func (obj *InsightServing) ActorInsightRecurrenceMinDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._ActorInsightRecurrenceMinDuration))
}

type RealTimeInsightParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// with a probability dist in [1, 100], how likely is the flow for the real time insight invoked
	_RealTimeInsightProb int32
	// time duration before a segment can be considered again for real time insight
	_SegmentCoolDownPeriod int64
	// priority of each generator. Priority can be between [0, 100].
	// the priority is used as weight while selecting a generator to create realtime insight
	_GeneratorPriority              *syncmap.Map[string, int]
	_InsightGenerationLogEvalParams *InsightGenerationLogEvalParams
}

// with a probability dist in [1, 100], how likely is the flow for the real time insight invoked
func (obj *RealTimeInsightParams) RealTimeInsightProb() int32 {
	return int32(atomic.LoadInt32(&obj._RealTimeInsightProb))
}

// time duration before a segment can be considered again for real time insight
func (obj *RealTimeInsightParams) SegmentCoolDownPeriod() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._SegmentCoolDownPeriod))
}

// priority of each generator. Priority can be between [0, 100].
// the priority is used as weight while selecting a generator to create realtime insight
func (obj *RealTimeInsightParams) GeneratorPriority() *syncmap.Map[string, int] {
	return obj._GeneratorPriority
}
func (obj *RealTimeInsightParams) InsightGenerationLogEvalParams() *InsightGenerationLogEvalParams {
	return obj._InsightGenerationLogEvalParams
}

type InsightGenerationLogEvalParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsEvaluationEnabled uint32
}

func (obj *InsightGenerationLogEvalParams) IsEvaluationEnabled() bool {
	if atomic.LoadUint32(&obj._IsEvaluationEnabled) == 0 {
		return false
	} else {
		return true
	}
}

type StoryParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsLottieExperimentEnabledForInternal uint32
	_LottieExperimentUrl                  string
	_LottieExperimentUrlMutex             *sync.RWMutex
}

func (obj *StoryParams) IsLottieExperimentEnabledForInternal() bool {
	if atomic.LoadUint32(&obj._IsLottieExperimentEnabledForInternal) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *StoryParams) LottieExperimentUrl() string {
	obj._LottieExperimentUrlMutex.RLock()
	defer obj._LottieExperimentUrlMutex.RUnlock()
	return obj._LottieExperimentUrl
}

type EpfParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// timeout for fetching passbook. (note: vendor (karza) sla is 240s)
	_PassbooksFetchTimeout int64
}

// timeout for fetching passbook. (note: vendor (karza) sla is 240s)
func (obj *EpfParams) PassbooksFetchTimeout() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._PassbooksFetchTimeout))
}

type NetworthParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// Flag to use scheme analytics API for mutual fund aggregation value instead of MfPortfolioHistory API
	_UseSchemeAnalyticsApiForMfAggVal uint32
	// TODO(sainath): Remove this once its stable in prod
	// Keep this in sync with DebugActorIdsForDailyReport in frontend/config.go
	_DebugActorIdsForDailyReport *syncmap.Map[string, bool]
	_AssetPrice                  *AssetPrice
}

// Flag to use scheme analytics API for mutual fund aggregation value instead of MfPortfolioHistory API
func (obj *NetworthParams) UseSchemeAnalyticsApiForMfAggVal() bool {
	if atomic.LoadUint32(&obj._UseSchemeAnalyticsApiForMfAggVal) == 0 {
		return false
	} else {
		return true
	}
}

// TODO(sainath): Remove this once its stable in prod
// Keep this in sync with DebugActorIdsForDailyReport in frontend/config.go
func (obj *NetworthParams) DebugActorIdsForDailyReport() *syncmap.Map[string, bool] {
	return obj._DebugActorIdsForDailyReport
}
func (obj *NetworthParams) AssetPrice() *AssetPrice {
	return obj._AssetPrice
}

type AssetPrice struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_Gold18CaratPerGramRs int32
	_Gold22CaratPerGramRs int32
	_Gold24CaratPerGramRs int32
	_SilverPricePerKgRs   int32
	// Gold ETF MF that has the lowest tracking error among gold ETFs in India
	_BenchmarkGoldEtfMFId      string
	_BenchmarkGoldEtfMFIdMutex *sync.RWMutex
}

func (obj *AssetPrice) Gold18CaratPerGramRs() int64 {
	return int64(atomic.LoadInt32(&obj._Gold18CaratPerGramRs))
}
func (obj *AssetPrice) Gold22CaratPerGramRs() int64 {
	return int64(atomic.LoadInt32(&obj._Gold22CaratPerGramRs))
}
func (obj *AssetPrice) Gold24CaratPerGramRs() int64 {
	return int64(atomic.LoadInt32(&obj._Gold24CaratPerGramRs))
}
func (obj *AssetPrice) SilverPricePerKgRs() int64 {
	return int64(atomic.LoadInt32(&obj._SilverPricePerKgRs))
}

// Gold ETF MF that has the lowest tracking error among gold ETFs in India
func (obj *AssetPrice) BenchmarkGoldEtfMFId() string {
	obj._BenchmarkGoldEtfMFIdMutex.RLock()
	defer obj._BenchmarkGoldEtfMFIdMutex.RUnlock()
	return obj._BenchmarkGoldEtfMFId
}

type NetWorthRefreshParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// NetWorthRefreshAsset -> RefreshThreshold mapping
	_AssetsRefreshThreshold *syncmap.Map[string, time.Duration]
	// NetWorthRefreshAsset -> ProcessingRefreshThreshold mapping
	_AssetsProcessingRefreshThreshold *syncmap.Map[string, time.Duration]
	// InvestmentInstrumentType -> RefreshThreshold mapping
	_ManualAssetsRefreshThreshold *syncmap.Map[string, time.Duration]
	// Refresh order for NetWorth Refresh
	_InstrumentsRefreshOrder      roarray.ROArray[string]
	_InstrumentsRefreshOrderMutex *sync.RWMutex
}

// NetWorthRefreshAsset -> RefreshThreshold mapping
func (obj *NetWorthRefreshParams) AssetsRefreshThreshold() *syncmap.Map[string, time.Duration] {
	return obj._AssetsRefreshThreshold
}

// NetWorthRefreshAsset -> ProcessingRefreshThreshold mapping
func (obj *NetWorthRefreshParams) AssetsProcessingRefreshThreshold() *syncmap.Map[string, time.Duration] {
	return obj._AssetsProcessingRefreshThreshold
}

// InvestmentInstrumentType -> RefreshThreshold mapping
func (obj *NetWorthRefreshParams) ManualAssetsRefreshThreshold() *syncmap.Map[string, time.Duration] {
	return obj._ManualAssetsRefreshThreshold
}

// Refresh order for NetWorth Refresh
func (obj *NetWorthRefreshParams) InstrumentsRefreshOrder() roarray.ROArray[string] {
	obj._InstrumentsRefreshOrderMutex.RLock()
	defer obj._InstrumentsRefreshOrderMutex.RUnlock()
	return obj._InstrumentsRefreshOrder
}

type SearchConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// Max number of search hits to return for a query on the index
	_QuerySize int64
	// True, if documents should be ingested for searching synchronously during server start.
	// False, if documents can be ingested concurrently while the server is starting.
	_IngestInSync  uint32
	_ListFilePath  string
	_IndexName     string
	_IndexFilePath string
}

// Max number of search hits to return for a query on the index
func (obj *SearchConfig) QuerySize() int {
	return int(atomic.LoadInt64(&obj._QuerySize))
}

// True, if documents should be ingested for searching synchronously during server start.
// False, if documents can be ingested concurrently while the server is starting.
func (obj *SearchConfig) IngestInSync() bool {
	if atomic.LoadUint32(&obj._IngestInSync) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *SearchConfig) ListFilePath() string {
	return obj._ListFilePath
}
func (obj *SearchConfig) IndexName() string {
	return obj._IndexName
}
func (obj *SearchConfig) IndexFilePath() string {
	return obj._IndexFilePath
}

type UANAccountsCacheConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsCachingEnabled uint32
	_CacheTTL         int64
}

func (obj *UANAccountsCacheConfig) IsCachingEnabled() bool {
	if atomic.LoadUint32(&obj._IsCachingEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *UANAccountsCacheConfig) CacheTTL() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._CacheTTL))
}

type MagicImportConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// Corresponding cron job to update the cache system context
	// https://jenkins-prod.pointz.in/job/Scripts/job/Backend/job/AssetsAndAnalysis/job/ScheduledScripts/job/magic_import_cache_system_context/
	_UseCachedSystemContext uint32
	_EnableImageStorage     uint32
	// ModelName maps to api/llm/model
	_ModelName      string
	_ModelNameMutex *sync.RWMutex
	_S3BucketName   string
}

// Corresponding cron job to update the cache system context
// https://jenkins-prod.pointz.in/job/Scripts/job/Backend/job/AssetsAndAnalysis/job/ScheduledScripts/job/magic_import_cache_system_context/
func (obj *MagicImportConfig) UseCachedSystemContext() bool {
	if atomic.LoadUint32(&obj._UseCachedSystemContext) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *MagicImportConfig) EnableImageStorage() bool {
	if atomic.LoadUint32(&obj._EnableImageStorage) == 0 {
		return false
	} else {
		return true
	}
}

// ModelName maps to api/llm/model
func (obj *MagicImportConfig) ModelName() string {
	obj._ModelNameMutex.RLock()
	defer obj._ModelNameMutex.RUnlock()
	return obj._ModelName
}
func (obj *MagicImportConfig) S3BucketName() string {
	return obj._S3BucketName
}

type NetworthMcp struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_NetWorthDataFileName                    string
	_NetWorthDataFileNameMutex               *sync.RWMutex
	_FileDataDescription                     string
	_FileDataDescriptionMutex                *sync.RWMutex
	_NetWorthFileDescription                 string
	_NetWorthFileDescriptionMutex            *sync.RWMutex
	_MfHoldingsFileDescription               string
	_MfHoldingsFileDescriptionMutex          *sync.RWMutex
	_AaFileDescription                       string
	_AaFileDescriptionMutex                  *sync.RWMutex
	_EpfDescription                          string
	_EpfDescriptionMutex                     *sync.RWMutex
	_CreditReportDescription                 string
	_CreditReportDescriptionMutex            *sync.RWMutex
	_MfTransactionsDescription               string
	_MfTransactionsDescriptionMutex          *sync.RWMutex
	_MfTransactionResponseSchemaDesc         string
	_MfTransactionResponseSchemaDescMutex    *sync.RWMutex
	_BankTransactionFileDescription          string
	_BankTransactionFileDescriptionMutex     *sync.RWMutex
	_BankTransactionsResponseSchemaDesc      string
	_BankTransactionsResponseSchemaDescMutex *sync.RWMutex
	_ManualAssetsFileDescription             string
	_ManualAssetsFileDescriptionMutex        *sync.RWMutex
}

func (obj *NetworthMcp) NetWorthDataFileName() string {
	obj._NetWorthDataFileNameMutex.RLock()
	defer obj._NetWorthDataFileNameMutex.RUnlock()
	return obj._NetWorthDataFileName
}
func (obj *NetworthMcp) FileDataDescription() string {
	obj._FileDataDescriptionMutex.RLock()
	defer obj._FileDataDescriptionMutex.RUnlock()
	return obj._FileDataDescription
}
func (obj *NetworthMcp) NetWorthFileDescription() string {
	obj._NetWorthFileDescriptionMutex.RLock()
	defer obj._NetWorthFileDescriptionMutex.RUnlock()
	return obj._NetWorthFileDescription
}
func (obj *NetworthMcp) MfHoldingsFileDescription() string {
	obj._MfHoldingsFileDescriptionMutex.RLock()
	defer obj._MfHoldingsFileDescriptionMutex.RUnlock()
	return obj._MfHoldingsFileDescription
}
func (obj *NetworthMcp) AaFileDescription() string {
	obj._AaFileDescriptionMutex.RLock()
	defer obj._AaFileDescriptionMutex.RUnlock()
	return obj._AaFileDescription
}
func (obj *NetworthMcp) EpfDescription() string {
	obj._EpfDescriptionMutex.RLock()
	defer obj._EpfDescriptionMutex.RUnlock()
	return obj._EpfDescription
}
func (obj *NetworthMcp) CreditReportDescription() string {
	obj._CreditReportDescriptionMutex.RLock()
	defer obj._CreditReportDescriptionMutex.RUnlock()
	return obj._CreditReportDescription
}
func (obj *NetworthMcp) MfTransactionsDescription() string {
	obj._MfTransactionsDescriptionMutex.RLock()
	defer obj._MfTransactionsDescriptionMutex.RUnlock()
	return obj._MfTransactionsDescription
}
func (obj *NetworthMcp) MfTransactionResponseSchemaDesc() string {
	obj._MfTransactionResponseSchemaDescMutex.RLock()
	defer obj._MfTransactionResponseSchemaDescMutex.RUnlock()
	return obj._MfTransactionResponseSchemaDesc
}
func (obj *NetworthMcp) BankTransactionFileDescription() string {
	obj._BankTransactionFileDescriptionMutex.RLock()
	defer obj._BankTransactionFileDescriptionMutex.RUnlock()
	return obj._BankTransactionFileDescription
}
func (obj *NetworthMcp) BankTransactionsResponseSchemaDesc() string {
	obj._BankTransactionsResponseSchemaDescMutex.RLock()
	defer obj._BankTransactionsResponseSchemaDescMutex.RUnlock()
	return obj._BankTransactionsResponseSchemaDesc
}
func (obj *NetworthMcp) ManualAssetsFileDescription() string {
	obj._ManualAssetsFileDescriptionMutex.RLock()
	defer obj._ManualAssetsFileDescriptionMutex.RUnlock()
	return obj._ManualAssetsFileDescription
}

func NewConfig() (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_UserEmailAccessSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._UserEmailAccessSubscriber = _UserEmailAccessSubscriber
	helper.AddFieldSetters("useremailaccesssubscriber", _fieldSetters, _setters)
	_PeriodicEmailSyncSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._PeriodicEmailSyncSubscriber = _PeriodicEmailSyncSubscriber
	helper.AddFieldSetters("periodicemailsyncsubscriber", _fieldSetters, _setters)
	_MailDataParserSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._MailDataParserSubscriber = _MailDataParserSubscriber
	helper.AddFieldSetters("maildataparsersubscriber", _fieldSetters, _setters)
	_OnboardingStageUpdateEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._OnboardingStageUpdateEventSubscriber = _OnboardingStageUpdateEventSubscriber
	helper.AddFieldSetters("onboardingstageupdateeventsubscriber", _fieldSetters, _setters)
	_Flags, _fieldSetters := NewFlags()
	_obj._Flags = _Flags
	helper.AddFieldSetters("flags", _fieldSetters, _setters)
	_InsightServing, _fieldSetters := NewInsightServing()
	_obj._InsightServing = _InsightServing
	helper.AddFieldSetters("insightserving", _fieldSetters, _setters)
	_CreateOrUpdateGenerationStatusSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CreateOrUpdateGenerationStatusSubscriber = _CreateOrUpdateGenerationStatusSubscriber
	helper.AddFieldSetters("createorupdategenerationstatussubscriber", _fieldSetters, _setters)
	_StoreGeneratedActorInsightsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._StoreGeneratedActorInsightsSubscriber = _StoreGeneratedActorInsightsSubscriber
	helper.AddFieldSetters("storegeneratedactorinsightssubscriber", _fieldSetters, _setters)
	_RealTimeInsightParams, _fieldSetters := NewRealTimeInsightParams()
	_obj._RealTimeInsightParams = _RealTimeInsightParams
	helper.AddFieldSetters("realtimeinsightparams", _fieldSetters, _setters)
	_GenerateInsightsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._GenerateInsightsSubscriber = _GenerateInsightsSubscriber
	helper.AddFieldSetters("generateinsightssubscriber", _fieldSetters, _setters)
	_StoryParams, _fieldSetters := NewStoryParams()
	_obj._StoryParams = _StoryParams
	helper.AddFieldSetters("storyparams", _fieldSetters, _setters)
	_EpfParams, _fieldSetters := NewEpfParams()
	_obj._EpfParams = _EpfParams
	helper.AddFieldSetters("epfparams", _fieldSetters, _setters)
	_NetworthParams, _fieldSetters := NewNetworthParams()
	_obj._NetworthParams = _NetworthParams
	helper.AddFieldSetters("networthparams", _fieldSetters, _setters)
	_NetWorthRefreshParams, _fieldSetters := NewNetWorthRefreshParams()
	_obj._NetWorthRefreshParams = _NetWorthRefreshParams
	helper.AddFieldSetters("networthrefreshparams", _fieldSetters, _setters)
	_PMSSearchConfig, _fieldSetters := NewSearchConfig()
	_obj._PMSSearchConfig = _PMSSearchConfig
	helper.AddFieldSetters("pmssearchconfig", _fieldSetters, _setters)
	_AIFSearchConfig, _fieldSetters := NewSearchConfig()
	_obj._AIFSearchConfig = _AIFSearchConfig
	helper.AddFieldSetters("aifsearchconfig", _fieldSetters, _setters)
	_EpfPassbookDataFlatteningSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._EpfPassbookDataFlatteningSubscriber = _EpfPassbookDataFlatteningSubscriber
	helper.AddFieldSetters("epfpassbookdataflatteningsubscriber", _fieldSetters, _setters)
	_UANAccountsCacheConfig, _fieldSetters := NewUANAccountsCacheConfig()
	_obj._UANAccountsCacheConfig = _UANAccountsCacheConfig
	helper.AddFieldSetters("uanaccountscacheconfig", _fieldSetters, _setters)
	_ProcessCaNewDataFetchEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessCaNewDataFetchEventSubscriber = _ProcessCaNewDataFetchEventSubscriber
	helper.AddFieldSetters("processcanewdatafetcheventsubscriber", _fieldSetters, _setters)
	_LLMHandlerConfig, _fieldSetters := genconfig.NewConfig()
	_obj._LLMHandlerConfig = _LLMHandlerConfig
	helper.AddFieldSetters("llmhandlerconfig", _fieldSetters, _setters)
	_MagicImportConfig, _fieldSetters := NewMagicImportConfig()
	_obj._MagicImportConfig = _MagicImportConfig
	helper.AddFieldSetters("magicimportconfig", _fieldSetters, _setters)
	_NetworthMcp, _fieldSetters := NewNetworthMcp()
	_obj._NetworthMcp = _NetworthMcp
	helper.AddFieldSetters("networthmcp", _fieldSetters, _setters)
	return _obj, _setters
}

func NewConfigWithQuest(questFieldPath string) (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_UserEmailAccessSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._UserEmailAccessSubscriber = _UserEmailAccessSubscriber
	helper.AddFieldSetters("useremailaccesssubscriber", _fieldSetters, _setters)
	_PeriodicEmailSyncSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._PeriodicEmailSyncSubscriber = _PeriodicEmailSyncSubscriber
	helper.AddFieldSetters("periodicemailsyncsubscriber", _fieldSetters, _setters)
	_MailDataParserSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._MailDataParserSubscriber = _MailDataParserSubscriber
	helper.AddFieldSetters("maildataparsersubscriber", _fieldSetters, _setters)
	_OnboardingStageUpdateEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._OnboardingStageUpdateEventSubscriber = _OnboardingStageUpdateEventSubscriber
	helper.AddFieldSetters("onboardingstageupdateeventsubscriber", _fieldSetters, _setters)
	_Flags, _fieldSetters := NewFlags()
	_obj._Flags = _Flags
	helper.AddFieldSetters("flags", _fieldSetters, _setters)
	_InsightServing, _fieldSetters := NewInsightServing()
	_obj._InsightServing = _InsightServing
	helper.AddFieldSetters("insightserving", _fieldSetters, _setters)
	_CreateOrUpdateGenerationStatusSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CreateOrUpdateGenerationStatusSubscriber = _CreateOrUpdateGenerationStatusSubscriber
	helper.AddFieldSetters("createorupdategenerationstatussubscriber", _fieldSetters, _setters)
	_StoreGeneratedActorInsightsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._StoreGeneratedActorInsightsSubscriber = _StoreGeneratedActorInsightsSubscriber
	helper.AddFieldSetters("storegeneratedactorinsightssubscriber", _fieldSetters, _setters)
	_RealTimeInsightParams, _fieldSetters := NewRealTimeInsightParams()
	_obj._RealTimeInsightParams = _RealTimeInsightParams
	helper.AddFieldSetters("realtimeinsightparams", _fieldSetters, _setters)
	_GenerateInsightsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._GenerateInsightsSubscriber = _GenerateInsightsSubscriber
	helper.AddFieldSetters("generateinsightssubscriber", _fieldSetters, _setters)
	_StoryParams, _fieldSetters := NewStoryParams()
	_obj._StoryParams = _StoryParams
	helper.AddFieldSetters("storyparams", _fieldSetters, _setters)
	_EpfParams, _fieldSetters := NewEpfParams()
	_obj._EpfParams = _EpfParams
	helper.AddFieldSetters("epfparams", _fieldSetters, _setters)
	_NetworthParams, _fieldSetters := NewNetworthParams()
	_obj._NetworthParams = _NetworthParams
	helper.AddFieldSetters("networthparams", _fieldSetters, _setters)
	_NetWorthRefreshParams, _fieldSetters := NewNetWorthRefreshParams()
	_obj._NetWorthRefreshParams = _NetWorthRefreshParams
	helper.AddFieldSetters("networthrefreshparams", _fieldSetters, _setters)
	_PMSSearchConfig, _fieldSetters := NewSearchConfig()
	_obj._PMSSearchConfig = _PMSSearchConfig
	helper.AddFieldSetters("pmssearchconfig", _fieldSetters, _setters)
	_AIFSearchConfig, _fieldSetters := NewSearchConfig()
	_obj._AIFSearchConfig = _AIFSearchConfig
	helper.AddFieldSetters("aifsearchconfig", _fieldSetters, _setters)
	_EpfPassbookDataFlatteningSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._EpfPassbookDataFlatteningSubscriber = _EpfPassbookDataFlatteningSubscriber
	helper.AddFieldSetters("epfpassbookdataflatteningsubscriber", _fieldSetters, _setters)
	_UANAccountsCacheConfig, _fieldSetters := NewUANAccountsCacheConfig()
	_obj._UANAccountsCacheConfig = _UANAccountsCacheConfig
	helper.AddFieldSetters("uanaccountscacheconfig", _fieldSetters, _setters)
	_ProcessCaNewDataFetchEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessCaNewDataFetchEventSubscriber = _ProcessCaNewDataFetchEventSubscriber
	helper.AddFieldSetters("processcanewdatafetcheventsubscriber", _fieldSetters, _setters)
	_LLMHandlerConfig, _fieldSetters := genconfig.NewConfig()
	_obj._LLMHandlerConfig = _LLMHandlerConfig
	helper.AddFieldSetters("llmhandlerconfig", _fieldSetters, _setters)
	_MagicImportConfig, _fieldSetters := NewMagicImportConfig()
	_obj._MagicImportConfig = _MagicImportConfig
	helper.AddFieldSetters("magicimportconfig", _fieldSetters, _setters)
	_NetworthMcp, _fieldSetters := NewNetworthMcp()
	_obj._NetworthMcp = _NetworthMcp
	helper.AddFieldSetters("networthmcp", _fieldSetters, _setters)

	return _obj, _setters
}

func (obj *Config) Init() {
	newObj, _ := NewConfig()
	*obj = *newObj
}

func (obj *Config) SetQuestSDK(questSdk questsdk.Client) {
}

func (obj *Config) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Config) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	return vars, nil
}

func (obj *Config) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Config)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Config) setDynamicField(v *config.Config, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "useremailaccesssubscriber":
		return obj._UserEmailAccessSubscriber.Set(v.UserEmailAccessSubscriber, true, path)
	case "periodicemailsyncsubscriber":
		return obj._PeriodicEmailSyncSubscriber.Set(v.PeriodicEmailSyncSubscriber, true, path)
	case "maildataparsersubscriber":
		return obj._MailDataParserSubscriber.Set(v.MailDataParserSubscriber, true, path)
	case "onboardingstageupdateeventsubscriber":
		return obj._OnboardingStageUpdateEventSubscriber.Set(v.OnboardingStageUpdateEventSubscriber, true, path)
	case "flags":
		return obj._Flags.Set(v.Flags, true, path)
	case "insightserving":
		return obj._InsightServing.Set(v.InsightServing, true, path)
	case "createorupdategenerationstatussubscriber":
		return obj._CreateOrUpdateGenerationStatusSubscriber.Set(v.CreateOrUpdateGenerationStatusSubscriber, true, path)
	case "storegeneratedactorinsightssubscriber":
		return obj._StoreGeneratedActorInsightsSubscriber.Set(v.StoreGeneratedActorInsightsSubscriber, true, path)
	case "realtimeinsightparams":
		return obj._RealTimeInsightParams.Set(v.RealTimeInsightParams, true, path)
	case "generateinsightssubscriber":
		return obj._GenerateInsightsSubscriber.Set(v.GenerateInsightsSubscriber, true, path)
	case "storyparams":
		return obj._StoryParams.Set(v.StoryParams, true, path)
	case "epfparams":
		return obj._EpfParams.Set(v.EpfParams, true, path)
	case "networthparams":
		return obj._NetworthParams.Set(v.NetworthParams, true, path)
	case "networthrefreshparams":
		return obj._NetWorthRefreshParams.Set(v.NetWorthRefreshParams, true, path)
	case "pmssearchconfig":
		return obj._PMSSearchConfig.Set(v.PMSSearchConfig, true, path)
	case "aifsearchconfig":
		return obj._AIFSearchConfig.Set(v.AIFSearchConfig, true, path)
	case "epfpassbookdataflatteningsubscriber":
		return obj._EpfPassbookDataFlatteningSubscriber.Set(v.EpfPassbookDataFlatteningSubscriber, true, path)
	case "uanaccountscacheconfig":
		return obj._UANAccountsCacheConfig.Set(v.UANAccountsCacheConfig, true, path)
	case "processcanewdatafetcheventsubscriber":
		return obj._ProcessCaNewDataFetchEventSubscriber.Set(v.ProcessCaNewDataFetchEventSubscriber, true, path)
	case "llmhandlerconfig":
		return obj._LLMHandlerConfig.Set(v.LLMHandlerConfig, true, path)
	case "magicimportconfig":
		return obj._MagicImportConfig.Set(v.MagicImportConfig, true, path)
	case "networthmcp":
		return obj._NetworthMcp.Set(v.NetworthMcp, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Config) setDynamicFields(v *config.Config, dynamic bool, path []string) (err error) {

	err = obj._UserEmailAccessSubscriber.Set(v.UserEmailAccessSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._PeriodicEmailSyncSubscriber.Set(v.PeriodicEmailSyncSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._MailDataParserSubscriber.Set(v.MailDataParserSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._OnboardingStageUpdateEventSubscriber.Set(v.OnboardingStageUpdateEventSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._Flags.Set(v.Flags, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._InsightServing.Set(v.InsightServing, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CreateOrUpdateGenerationStatusSubscriber.Set(v.CreateOrUpdateGenerationStatusSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._StoreGeneratedActorInsightsSubscriber.Set(v.StoreGeneratedActorInsightsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RealTimeInsightParams.Set(v.RealTimeInsightParams, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._GenerateInsightsSubscriber.Set(v.GenerateInsightsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._StoryParams.Set(v.StoryParams, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EpfParams.Set(v.EpfParams, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._NetworthParams.Set(v.NetworthParams, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._NetWorthRefreshParams.Set(v.NetWorthRefreshParams, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._PMSSearchConfig.Set(v.PMSSearchConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AIFSearchConfig.Set(v.AIFSearchConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EpfPassbookDataFlatteningSubscriber.Set(v.EpfPassbookDataFlatteningSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._UANAccountsCacheConfig.Set(v.UANAccountsCacheConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProcessCaNewDataFetchEventSubscriber.Set(v.ProcessCaNewDataFetchEventSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._LLMHandlerConfig.Set(v.LLMHandlerConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._MagicImportConfig.Set(v.MagicImportConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._NetworthMcp.Set(v.NetworthMcp, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Config) setStaticFields(v *config.Config) error {

	obj._Application = v.Application
	obj._Server = v.Server
	obj._Logging = v.Logging
	obj._InsightsDb = v.InsightsDb
	obj._ActorInsightsDb = v.ActorInsightsDb
	obj._DbConfigMap = v.DbConfigMap
	obj._GmailBatchGetApiParams = v.GmailBatchGetApiParams
	obj._EmailParserParams = v.EmailParserParams
	obj._UserEmailAccessPublisher = v.UserEmailAccessPublisher
	obj._PeriodicEmaiSyncPublisher = v.PeriodicEmaiSyncPublisher
	obj._MailDataParserPublisher = v.MailDataParserPublisher
	obj._GmailUserSpendsPublisher = v.GmailUserSpendsPublisher
	obj._EpfPassbookImportEventPublisher = v.EpfPassbookImportEventPublisher
	obj._AWS = v.AWS
	obj._Secrets = v.Secrets
	obj._GoogleOAuthParams = v.GoogleOAuthParams
	obj._GmailListApiParams = v.GmailListApiParams
	obj._MailFetchConcurrencyParams = v.MailFetchConcurrencyParams
	obj._AddGmailAccountBannerParams = v.AddGmailAccountBannerParams
	obj._Tracing = v.Tracing
	obj._Profiling = v.Profiling
	obj._SecureLoggingV2 = v.SecureLoggingV2
	return nil
}

func NewFlags() (_obj *Flags, _setters map[string]dynconf.SetFunc) {
	_obj = &Flags{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_RealTimeInsightFeatureFlag, _fieldSetters := gencfg.NewFeatureReleaseConfig()
	_obj._RealTimeInsightFeatureFlag = _RealTimeInsightFeatureFlag
	helper.AddFieldSetters("realtimeinsightfeatureflag", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *Flags) Init() {
	newObj, _ := NewFlags()
	*obj = *newObj
}

func (obj *Flags) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Flags) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Flags)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Flags) setDynamicField(v *config.Flags, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "realtimeinsightfeatureflag":
		return obj._RealTimeInsightFeatureFlag.Set(v.RealTimeInsightFeatureFlag, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Flags) setDynamicFields(v *config.Flags, dynamic bool, path []string) (err error) {

	err = obj._RealTimeInsightFeatureFlag.Set(v.RealTimeInsightFeatureFlag, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Flags) setStaticFields(v *config.Flags) error {

	obj._TrimDebugMessageFromStatus = v.TrimDebugMessageFromStatus
	obj._ShowTeaserInsights = v.ShowTeaserInsights
	return nil
}

func NewInsightServing() (_obj *InsightServing, _setters map[string]dynconf.SetFunc) {
	_obj = &InsightServing{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["insightcappersession"] = _obj.SetInsightCapPerSession
	_setters["actorinsightrecurrenceminduration"] = _obj.SetActorInsightRecurrenceMinDuration
	return _obj, _setters
}

func (obj *InsightServing) Init() {
	newObj, _ := NewInsightServing()
	*obj = *newObj
}

func (obj *InsightServing) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *InsightServing) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.InsightServing)
	if !ok {
		return fmt.Errorf("invalid data type %v *InsightServing", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *InsightServing) setDynamicField(v *config.InsightServing, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "insightcappersession":
		return obj.SetInsightCapPerSession(v.InsightCapPerSession, true, nil)
	case "actorinsightrecurrenceminduration":
		return obj.SetActorInsightRecurrenceMinDuration(v.ActorInsightRecurrenceMinDuration, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *InsightServing) setDynamicFields(v *config.InsightServing, dynamic bool, path []string) (err error) {

	err = obj.SetInsightCapPerSession(v.InsightCapPerSession, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetActorInsightRecurrenceMinDuration(v.ActorInsightRecurrenceMinDuration, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *InsightServing) setStaticFields(v *config.InsightServing) error {

	return nil
}

func (obj *InsightServing) SetInsightCapPerSession(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *InsightServing.InsightCapPerSession", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._InsightCapPerSession, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "InsightCapPerSession")
	}
	return nil
}
func (obj *InsightServing) SetActorInsightRecurrenceMinDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *InsightServing.ActorInsightRecurrenceMinDuration", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._ActorInsightRecurrenceMinDuration, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "ActorInsightRecurrenceMinDuration")
	}
	return nil
}

func NewRealTimeInsightParams() (_obj *RealTimeInsightParams, _setters map[string]dynconf.SetFunc) {
	_obj = &RealTimeInsightParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["realtimeinsightprob"] = _obj.SetRealTimeInsightProb
	_setters["segmentcooldownperiod"] = _obj.SetSegmentCoolDownPeriod

	_obj._GeneratorPriority = &syncmap.Map[string, int]{}
	_setters["generatorpriority"] = _obj.SetGeneratorPriority
	_InsightGenerationLogEvalParams, _fieldSetters := NewInsightGenerationLogEvalParams()
	_obj._InsightGenerationLogEvalParams = _InsightGenerationLogEvalParams
	helper.AddFieldSetters("insightgenerationlogevalparams", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *RealTimeInsightParams) Init() {
	newObj, _ := NewRealTimeInsightParams()
	*obj = *newObj
}

func (obj *RealTimeInsightParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *RealTimeInsightParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.RealTimeInsightParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *RealTimeInsightParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *RealTimeInsightParams) setDynamicField(v *config.RealTimeInsightParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "realtimeinsightprob":
		return obj.SetRealTimeInsightProb(v.RealTimeInsightProb, true, nil)
	case "segmentcooldownperiod":
		return obj.SetSegmentCoolDownPeriod(v.SegmentCoolDownPeriod, true, nil)
	case "generatorpriority":
		return obj.SetGeneratorPriority(v.GeneratorPriority, true, path)
	case "insightgenerationlogevalparams":
		return obj._InsightGenerationLogEvalParams.Set(v.InsightGenerationLogEvalParams, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *RealTimeInsightParams) setDynamicFields(v *config.RealTimeInsightParams, dynamic bool, path []string) (err error) {

	err = obj.SetRealTimeInsightProb(v.RealTimeInsightProb, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSegmentCoolDownPeriod(v.SegmentCoolDownPeriod, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetGeneratorPriority(v.GeneratorPriority, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._InsightGenerationLogEvalParams.Set(v.InsightGenerationLogEvalParams, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *RealTimeInsightParams) setStaticFields(v *config.RealTimeInsightParams) error {

	return nil
}

func (obj *RealTimeInsightParams) SetRealTimeInsightProb(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *RealTimeInsightParams.RealTimeInsightProb", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._RealTimeInsightProb, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "RealTimeInsightProb")
	}
	return nil
}
func (obj *RealTimeInsightParams) SetSegmentCoolDownPeriod(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *RealTimeInsightParams.SegmentCoolDownPeriod", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._SegmentCoolDownPeriod, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "SegmentCoolDownPeriod")
	}
	return nil
}
func (obj *RealTimeInsightParams) SetGeneratorPriority(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]int)
	if !ok {
		return fmt.Errorf("invalid data type %v *RealTimeInsightParams.GeneratorPriority", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._GeneratorPriority, v, path)
}

func NewInsightGenerationLogEvalParams() (_obj *InsightGenerationLogEvalParams, _setters map[string]dynconf.SetFunc) {
	_obj = &InsightGenerationLogEvalParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isevaluationenabled"] = _obj.SetIsEvaluationEnabled
	return _obj, _setters
}

func (obj *InsightGenerationLogEvalParams) Init() {
	newObj, _ := NewInsightGenerationLogEvalParams()
	*obj = *newObj
}

func (obj *InsightGenerationLogEvalParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *InsightGenerationLogEvalParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.InsightGenerationLogEvalParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *InsightGenerationLogEvalParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *InsightGenerationLogEvalParams) setDynamicField(v *config.InsightGenerationLogEvalParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isevaluationenabled":
		return obj.SetIsEvaluationEnabled(v.IsEvaluationEnabled, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *InsightGenerationLogEvalParams) setDynamicFields(v *config.InsightGenerationLogEvalParams, dynamic bool, path []string) (err error) {

	err = obj.SetIsEvaluationEnabled(v.IsEvaluationEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *InsightGenerationLogEvalParams) setStaticFields(v *config.InsightGenerationLogEvalParams) error {

	return nil
}

func (obj *InsightGenerationLogEvalParams) SetIsEvaluationEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *InsightGenerationLogEvalParams.IsEvaluationEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEvaluationEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEvaluationEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEvaluationEnabled")
	}
	return nil
}

func NewStoryParams() (_obj *StoryParams, _setters map[string]dynconf.SetFunc) {
	_obj = &StoryParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["islottieexperimentenabledforinternal"] = _obj.SetIsLottieExperimentEnabledForInternal
	_setters["lottieexperimenturl"] = _obj.SetLottieExperimentUrl
	_obj._LottieExperimentUrlMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *StoryParams) Init() {
	newObj, _ := NewStoryParams()
	*obj = *newObj
}

func (obj *StoryParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *StoryParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.StoryParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *StoryParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *StoryParams) setDynamicField(v *config.StoryParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "islottieexperimentenabledforinternal":
		return obj.SetIsLottieExperimentEnabledForInternal(v.IsLottieExperimentEnabledForInternal, true, nil)
	case "lottieexperimenturl":
		return obj.SetLottieExperimentUrl(v.LottieExperimentUrl, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *StoryParams) setDynamicFields(v *config.StoryParams, dynamic bool, path []string) (err error) {

	err = obj.SetIsLottieExperimentEnabledForInternal(v.IsLottieExperimentEnabledForInternal, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetLottieExperimentUrl(v.LottieExperimentUrl, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *StoryParams) setStaticFields(v *config.StoryParams) error {

	return nil
}

func (obj *StoryParams) SetIsLottieExperimentEnabledForInternal(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *StoryParams.IsLottieExperimentEnabledForInternal", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsLottieExperimentEnabledForInternal, 1)
	} else {
		atomic.StoreUint32(&obj._IsLottieExperimentEnabledForInternal, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsLottieExperimentEnabledForInternal")
	}
	return nil
}
func (obj *StoryParams) SetLottieExperimentUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *StoryParams.LottieExperimentUrl", reflect.TypeOf(val))
	}
	obj._LottieExperimentUrlMutex.Lock()
	defer obj._LottieExperimentUrlMutex.Unlock()
	obj._LottieExperimentUrl = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "LottieExperimentUrl")
	}
	return nil
}

func NewEpfParams() (_obj *EpfParams, _setters map[string]dynconf.SetFunc) {
	_obj = &EpfParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["passbooksfetchtimeout"] = _obj.SetPassbooksFetchTimeout
	return _obj, _setters
}

func (obj *EpfParams) Init() {
	newObj, _ := NewEpfParams()
	*obj = *newObj
}

func (obj *EpfParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *EpfParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.EpfParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *EpfParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *EpfParams) setDynamicField(v *config.EpfParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "passbooksfetchtimeout":
		return obj.SetPassbooksFetchTimeout(v.PassbooksFetchTimeout, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *EpfParams) setDynamicFields(v *config.EpfParams, dynamic bool, path []string) (err error) {

	err = obj.SetPassbooksFetchTimeout(v.PassbooksFetchTimeout, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *EpfParams) setStaticFields(v *config.EpfParams) error {

	return nil
}

func (obj *EpfParams) SetPassbooksFetchTimeout(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *EpfParams.PassbooksFetchTimeout", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._PassbooksFetchTimeout, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "PassbooksFetchTimeout")
	}
	return nil
}

func NewNetworthParams() (_obj *NetworthParams, _setters map[string]dynconf.SetFunc) {
	_obj = &NetworthParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["useschemeanalyticsapiformfaggval"] = _obj.SetUseSchemeAnalyticsApiForMfAggVal

	_obj._DebugActorIdsForDailyReport = &syncmap.Map[string, bool]{}
	_setters["debugactoridsfordailyreport"] = _obj.SetDebugActorIdsForDailyReport
	_AssetPrice, _fieldSetters := NewAssetPrice()
	_obj._AssetPrice = _AssetPrice
	helper.AddFieldSetters("assetprice", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *NetworthParams) Init() {
	newObj, _ := NewNetworthParams()
	*obj = *newObj
}

func (obj *NetworthParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *NetworthParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.NetworthParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *NetworthParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *NetworthParams) setDynamicField(v *config.NetworthParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "useschemeanalyticsapiformfaggval":
		return obj.SetUseSchemeAnalyticsApiForMfAggVal(v.UseSchemeAnalyticsApiForMfAggVal, true, nil)
	case "debugactoridsfordailyreport":
		return obj.SetDebugActorIdsForDailyReport(v.DebugActorIdsForDailyReport, true, path)
	case "assetprice":
		return obj._AssetPrice.Set(v.AssetPrice, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *NetworthParams) setDynamicFields(v *config.NetworthParams, dynamic bool, path []string) (err error) {

	err = obj.SetUseSchemeAnalyticsApiForMfAggVal(v.UseSchemeAnalyticsApiForMfAggVal, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDebugActorIdsForDailyReport(v.DebugActorIdsForDailyReport, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AssetPrice.Set(v.AssetPrice, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *NetworthParams) setStaticFields(v *config.NetworthParams) error {

	return nil
}

func (obj *NetworthParams) SetUseSchemeAnalyticsApiForMfAggVal(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *NetworthParams.UseSchemeAnalyticsApiForMfAggVal", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._UseSchemeAnalyticsApiForMfAggVal, 1)
	} else {
		atomic.StoreUint32(&obj._UseSchemeAnalyticsApiForMfAggVal, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "UseSchemeAnalyticsApiForMfAggVal")
	}
	return nil
}
func (obj *NetworthParams) SetDebugActorIdsForDailyReport(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *NetworthParams.DebugActorIdsForDailyReport", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._DebugActorIdsForDailyReport, v, path)
}

func NewAssetPrice() (_obj *AssetPrice, _setters map[string]dynconf.SetFunc) {
	_obj = &AssetPrice{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["gold18caratpergramrs"] = _obj.SetGold18CaratPerGramRs
	_setters["gold22caratpergramrs"] = _obj.SetGold22CaratPerGramRs
	_setters["gold24caratpergramrs"] = _obj.SetGold24CaratPerGramRs
	_setters["silverpriceperkgrs"] = _obj.SetSilverPricePerKgRs
	_setters["benchmarkgoldetfmfid"] = _obj.SetBenchmarkGoldEtfMFId
	_obj._BenchmarkGoldEtfMFIdMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *AssetPrice) Init() {
	newObj, _ := NewAssetPrice()
	*obj = *newObj
}

func (obj *AssetPrice) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *AssetPrice) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.AssetPrice)
	if !ok {
		return fmt.Errorf("invalid data type %v *AssetPrice", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *AssetPrice) setDynamicField(v *config.AssetPrice, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "gold18caratpergramrs":
		return obj.SetGold18CaratPerGramRs(v.Gold18CaratPerGramRs, true, nil)
	case "gold22caratpergramrs":
		return obj.SetGold22CaratPerGramRs(v.Gold22CaratPerGramRs, true, nil)
	case "gold24caratpergramrs":
		return obj.SetGold24CaratPerGramRs(v.Gold24CaratPerGramRs, true, nil)
	case "silverpriceperkgrs":
		return obj.SetSilverPricePerKgRs(v.SilverPricePerKgRs, true, nil)
	case "benchmarkgoldetfmfid":
		return obj.SetBenchmarkGoldEtfMFId(v.BenchmarkGoldEtfMFId, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *AssetPrice) setDynamicFields(v *config.AssetPrice, dynamic bool, path []string) (err error) {

	err = obj.SetGold18CaratPerGramRs(v.Gold18CaratPerGramRs, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetGold22CaratPerGramRs(v.Gold22CaratPerGramRs, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetGold24CaratPerGramRs(v.Gold24CaratPerGramRs, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSilverPricePerKgRs(v.SilverPricePerKgRs, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetBenchmarkGoldEtfMFId(v.BenchmarkGoldEtfMFId, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *AssetPrice) setStaticFields(v *config.AssetPrice) error {

	return nil
}

func (obj *AssetPrice) SetGold18CaratPerGramRs(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *AssetPrice.Gold18CaratPerGramRs", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._Gold18CaratPerGramRs, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "Gold18CaratPerGramRs")
	}
	return nil
}
func (obj *AssetPrice) SetGold22CaratPerGramRs(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *AssetPrice.Gold22CaratPerGramRs", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._Gold22CaratPerGramRs, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "Gold22CaratPerGramRs")
	}
	return nil
}
func (obj *AssetPrice) SetGold24CaratPerGramRs(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *AssetPrice.Gold24CaratPerGramRs", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._Gold24CaratPerGramRs, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "Gold24CaratPerGramRs")
	}
	return nil
}
func (obj *AssetPrice) SetSilverPricePerKgRs(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *AssetPrice.SilverPricePerKgRs", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._SilverPricePerKgRs, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "SilverPricePerKgRs")
	}
	return nil
}
func (obj *AssetPrice) SetBenchmarkGoldEtfMFId(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AssetPrice.BenchmarkGoldEtfMFId", reflect.TypeOf(val))
	}
	obj._BenchmarkGoldEtfMFIdMutex.Lock()
	defer obj._BenchmarkGoldEtfMFIdMutex.Unlock()
	obj._BenchmarkGoldEtfMFId = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "BenchmarkGoldEtfMFId")
	}
	return nil
}

func NewNetWorthRefreshParams() (_obj *NetWorthRefreshParams, _setters map[string]dynconf.SetFunc) {
	_obj = &NetWorthRefreshParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_obj._AssetsRefreshThreshold = &syncmap.Map[string, time.Duration]{}
	_setters["assetsrefreshthreshold"] = _obj.SetAssetsRefreshThreshold

	_obj._AssetsProcessingRefreshThreshold = &syncmap.Map[string, time.Duration]{}
	_setters["assetsprocessingrefreshthreshold"] = _obj.SetAssetsProcessingRefreshThreshold

	_obj._ManualAssetsRefreshThreshold = &syncmap.Map[string, time.Duration]{}
	_setters["manualassetsrefreshthreshold"] = _obj.SetManualAssetsRefreshThreshold
	_setters["instrumentsrefreshorder"] = _obj.SetInstrumentsRefreshOrder
	_obj._InstrumentsRefreshOrderMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *NetWorthRefreshParams) Init() {
	newObj, _ := NewNetWorthRefreshParams()
	*obj = *newObj
}

func (obj *NetWorthRefreshParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *NetWorthRefreshParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.NetWorthRefreshParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *NetWorthRefreshParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *NetWorthRefreshParams) setDynamicField(v *config.NetWorthRefreshParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "assetsrefreshthreshold":
		return obj.SetAssetsRefreshThreshold(v.AssetsRefreshThreshold, true, path)
	case "assetsprocessingrefreshthreshold":
		return obj.SetAssetsProcessingRefreshThreshold(v.AssetsProcessingRefreshThreshold, true, path)
	case "manualassetsrefreshthreshold":
		return obj.SetManualAssetsRefreshThreshold(v.ManualAssetsRefreshThreshold, true, path)
	case "instrumentsrefreshorder":
		return obj.SetInstrumentsRefreshOrder(v.InstrumentsRefreshOrder, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *NetWorthRefreshParams) setDynamicFields(v *config.NetWorthRefreshParams, dynamic bool, path []string) (err error) {

	err = obj.SetAssetsRefreshThreshold(v.AssetsRefreshThreshold, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetAssetsProcessingRefreshThreshold(v.AssetsProcessingRefreshThreshold, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetManualAssetsRefreshThreshold(v.ManualAssetsRefreshThreshold, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetInstrumentsRefreshOrder(v.InstrumentsRefreshOrder, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *NetWorthRefreshParams) setStaticFields(v *config.NetWorthRefreshParams) error {

	return nil
}

func (obj *NetWorthRefreshParams) SetAssetsRefreshThreshold(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *NetWorthRefreshParams.AssetsRefreshThreshold", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._AssetsRefreshThreshold, v, path)
}
func (obj *NetWorthRefreshParams) SetAssetsProcessingRefreshThreshold(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *NetWorthRefreshParams.AssetsProcessingRefreshThreshold", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._AssetsProcessingRefreshThreshold, v, path)
}
func (obj *NetWorthRefreshParams) SetManualAssetsRefreshThreshold(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *NetWorthRefreshParams.ManualAssetsRefreshThreshold", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._ManualAssetsRefreshThreshold, v, path)
}
func (obj *NetWorthRefreshParams) SetInstrumentsRefreshOrder(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *NetWorthRefreshParams.InstrumentsRefreshOrder", reflect.TypeOf(val))
	}
	obj._InstrumentsRefreshOrderMutex.Lock()
	defer obj._InstrumentsRefreshOrderMutex.Unlock()
	obj._InstrumentsRefreshOrder = roarray.New[string](v)
	return nil
}

func NewSearchConfig() (_obj *SearchConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &SearchConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["querysize"] = _obj.SetQuerySize
	_setters["ingestinsync"] = _obj.SetIngestInSync
	return _obj, _setters
}

func (obj *SearchConfig) Init() {
	newObj, _ := NewSearchConfig()
	*obj = *newObj
}

func (obj *SearchConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *SearchConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.SearchConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *SearchConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *SearchConfig) setDynamicField(v *config.SearchConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "querysize":
		return obj.SetQuerySize(v.QuerySize, true, nil)
	case "ingestinsync":
		return obj.SetIngestInSync(v.IngestInSync, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *SearchConfig) setDynamicFields(v *config.SearchConfig, dynamic bool, path []string) (err error) {

	err = obj.SetQuerySize(v.QuerySize, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIngestInSync(v.IngestInSync, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *SearchConfig) setStaticFields(v *config.SearchConfig) error {

	obj._ListFilePath = v.ListFilePath
	obj._IndexName = v.IndexName
	obj._IndexFilePath = v.IndexFilePath
	return nil
}

func (obj *SearchConfig) SetQuerySize(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *SearchConfig.QuerySize", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._QuerySize, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "QuerySize")
	}
	return nil
}
func (obj *SearchConfig) SetIngestInSync(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *SearchConfig.IngestInSync", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IngestInSync, 1)
	} else {
		atomic.StoreUint32(&obj._IngestInSync, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IngestInSync")
	}
	return nil
}

func NewUANAccountsCacheConfig() (_obj *UANAccountsCacheConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &UANAccountsCacheConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["iscachingenabled"] = _obj.SetIsCachingEnabled
	_setters["cachettl"] = _obj.SetCacheTTL
	return _obj, _setters
}

func (obj *UANAccountsCacheConfig) Init() {
	newObj, _ := NewUANAccountsCacheConfig()
	*obj = *newObj
}

func (obj *UANAccountsCacheConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *UANAccountsCacheConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.UANAccountsCacheConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *UANAccountsCacheConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *UANAccountsCacheConfig) setDynamicField(v *config.UANAccountsCacheConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "iscachingenabled":
		return obj.SetIsCachingEnabled(v.IsCachingEnabled, true, nil)
	case "cachettl":
		return obj.SetCacheTTL(v.CacheTTL, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *UANAccountsCacheConfig) setDynamicFields(v *config.UANAccountsCacheConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsCachingEnabled(v.IsCachingEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCacheTTL(v.CacheTTL, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *UANAccountsCacheConfig) setStaticFields(v *config.UANAccountsCacheConfig) error {

	return nil
}

func (obj *UANAccountsCacheConfig) SetIsCachingEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *UANAccountsCacheConfig.IsCachingEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsCachingEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsCachingEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsCachingEnabled")
	}
	return nil
}
func (obj *UANAccountsCacheConfig) SetCacheTTL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *UANAccountsCacheConfig.CacheTTL", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._CacheTTL, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "CacheTTL")
	}
	return nil
}

func NewMagicImportConfig() (_obj *MagicImportConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &MagicImportConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["usecachedsystemcontext"] = _obj.SetUseCachedSystemContext
	_setters["enableimagestorage"] = _obj.SetEnableImageStorage
	_setters["modelname"] = _obj.SetModelName
	_obj._ModelNameMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *MagicImportConfig) Init() {
	newObj, _ := NewMagicImportConfig()
	*obj = *newObj
}

func (obj *MagicImportConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *MagicImportConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.MagicImportConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *MagicImportConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *MagicImportConfig) setDynamicField(v *config.MagicImportConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "usecachedsystemcontext":
		return obj.SetUseCachedSystemContext(v.UseCachedSystemContext, true, nil)
	case "enableimagestorage":
		return obj.SetEnableImageStorage(v.EnableImageStorage, true, nil)
	case "modelname":
		return obj.SetModelName(v.ModelName, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *MagicImportConfig) setDynamicFields(v *config.MagicImportConfig, dynamic bool, path []string) (err error) {

	err = obj.SetUseCachedSystemContext(v.UseCachedSystemContext, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableImageStorage(v.EnableImageStorage, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetModelName(v.ModelName, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *MagicImportConfig) setStaticFields(v *config.MagicImportConfig) error {

	obj._S3BucketName = v.S3BucketName
	return nil
}

func (obj *MagicImportConfig) SetUseCachedSystemContext(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *MagicImportConfig.UseCachedSystemContext", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._UseCachedSystemContext, 1)
	} else {
		atomic.StoreUint32(&obj._UseCachedSystemContext, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "UseCachedSystemContext")
	}
	return nil
}
func (obj *MagicImportConfig) SetEnableImageStorage(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *MagicImportConfig.EnableImageStorage", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableImageStorage, 1)
	} else {
		atomic.StoreUint32(&obj._EnableImageStorage, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableImageStorage")
	}
	return nil
}
func (obj *MagicImportConfig) SetModelName(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *MagicImportConfig.ModelName", reflect.TypeOf(val))
	}
	obj._ModelNameMutex.Lock()
	defer obj._ModelNameMutex.Unlock()
	obj._ModelName = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "ModelName")
	}
	return nil
}

func NewNetworthMcp() (_obj *NetworthMcp, _setters map[string]dynconf.SetFunc) {
	_obj = &NetworthMcp{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["networthdatafilename"] = _obj.SetNetWorthDataFileName
	_obj._NetWorthDataFileNameMutex = &sync.RWMutex{}
	_setters["filedatadescription"] = _obj.SetFileDataDescription
	_obj._FileDataDescriptionMutex = &sync.RWMutex{}
	_setters["networthfiledescription"] = _obj.SetNetWorthFileDescription
	_obj._NetWorthFileDescriptionMutex = &sync.RWMutex{}
	_setters["mfholdingsfiledescription"] = _obj.SetMfHoldingsFileDescription
	_obj._MfHoldingsFileDescriptionMutex = &sync.RWMutex{}
	_setters["aafiledescription"] = _obj.SetAaFileDescription
	_obj._AaFileDescriptionMutex = &sync.RWMutex{}
	_setters["epfdescription"] = _obj.SetEpfDescription
	_obj._EpfDescriptionMutex = &sync.RWMutex{}
	_setters["creditreportdescription"] = _obj.SetCreditReportDescription
	_obj._CreditReportDescriptionMutex = &sync.RWMutex{}
	_setters["mftransactionsdescription"] = _obj.SetMfTransactionsDescription
	_obj._MfTransactionsDescriptionMutex = &sync.RWMutex{}
	_setters["mftransactionresponseschemadesc"] = _obj.SetMfTransactionResponseSchemaDesc
	_obj._MfTransactionResponseSchemaDescMutex = &sync.RWMutex{}
	_setters["banktransactionfiledescription"] = _obj.SetBankTransactionFileDescription
	_obj._BankTransactionFileDescriptionMutex = &sync.RWMutex{}
	_setters["banktransactionsresponseschemadesc"] = _obj.SetBankTransactionsResponseSchemaDesc
	_obj._BankTransactionsResponseSchemaDescMutex = &sync.RWMutex{}
	_setters["manualassetsfiledescription"] = _obj.SetManualAssetsFileDescription
	_obj._ManualAssetsFileDescriptionMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *NetworthMcp) Init() {
	newObj, _ := NewNetworthMcp()
	*obj = *newObj
}

func (obj *NetworthMcp) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *NetworthMcp) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.NetworthMcp)
	if !ok {
		return fmt.Errorf("invalid data type %v *NetworthMcp", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *NetworthMcp) setDynamicField(v *config.NetworthMcp, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "networthdatafilename":
		return obj.SetNetWorthDataFileName(v.NetWorthDataFileName, true, nil)
	case "filedatadescription":
		return obj.SetFileDataDescription(v.FileDataDescription, true, nil)
	case "networthfiledescription":
		return obj.SetNetWorthFileDescription(v.NetWorthFileDescription, true, nil)
	case "mfholdingsfiledescription":
		return obj.SetMfHoldingsFileDescription(v.MfHoldingsFileDescription, true, nil)
	case "aafiledescription":
		return obj.SetAaFileDescription(v.AaFileDescription, true, nil)
	case "epfdescription":
		return obj.SetEpfDescription(v.EpfDescription, true, nil)
	case "creditreportdescription":
		return obj.SetCreditReportDescription(v.CreditReportDescription, true, nil)
	case "mftransactionsdescription":
		return obj.SetMfTransactionsDescription(v.MfTransactionsDescription, true, nil)
	case "mftransactionresponseschemadesc":
		return obj.SetMfTransactionResponseSchemaDesc(v.MfTransactionResponseSchemaDesc, true, nil)
	case "banktransactionfiledescription":
		return obj.SetBankTransactionFileDescription(v.BankTransactionFileDescription, true, nil)
	case "banktransactionsresponseschemadesc":
		return obj.SetBankTransactionsResponseSchemaDesc(v.BankTransactionsResponseSchemaDesc, true, nil)
	case "manualassetsfiledescription":
		return obj.SetManualAssetsFileDescription(v.ManualAssetsFileDescription, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *NetworthMcp) setDynamicFields(v *config.NetworthMcp, dynamic bool, path []string) (err error) {

	err = obj.SetNetWorthDataFileName(v.NetWorthDataFileName, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetFileDataDescription(v.FileDataDescription, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetNetWorthFileDescription(v.NetWorthFileDescription, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMfHoldingsFileDescription(v.MfHoldingsFileDescription, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetAaFileDescription(v.AaFileDescription, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEpfDescription(v.EpfDescription, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCreditReportDescription(v.CreditReportDescription, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMfTransactionsDescription(v.MfTransactionsDescription, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMfTransactionResponseSchemaDesc(v.MfTransactionResponseSchemaDesc, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetBankTransactionFileDescription(v.BankTransactionFileDescription, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetBankTransactionsResponseSchemaDesc(v.BankTransactionsResponseSchemaDesc, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetManualAssetsFileDescription(v.ManualAssetsFileDescription, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *NetworthMcp) setStaticFields(v *config.NetworthMcp) error {

	return nil
}

func (obj *NetworthMcp) SetNetWorthDataFileName(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *NetworthMcp.NetWorthDataFileName", reflect.TypeOf(val))
	}
	obj._NetWorthDataFileNameMutex.Lock()
	defer obj._NetWorthDataFileNameMutex.Unlock()
	obj._NetWorthDataFileName = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "NetWorthDataFileName")
	}
	return nil
}
func (obj *NetworthMcp) SetFileDataDescription(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *NetworthMcp.FileDataDescription", reflect.TypeOf(val))
	}
	obj._FileDataDescriptionMutex.Lock()
	defer obj._FileDataDescriptionMutex.Unlock()
	obj._FileDataDescription = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "FileDataDescription")
	}
	return nil
}
func (obj *NetworthMcp) SetNetWorthFileDescription(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *NetworthMcp.NetWorthFileDescription", reflect.TypeOf(val))
	}
	obj._NetWorthFileDescriptionMutex.Lock()
	defer obj._NetWorthFileDescriptionMutex.Unlock()
	obj._NetWorthFileDescription = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "NetWorthFileDescription")
	}
	return nil
}
func (obj *NetworthMcp) SetMfHoldingsFileDescription(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *NetworthMcp.MfHoldingsFileDescription", reflect.TypeOf(val))
	}
	obj._MfHoldingsFileDescriptionMutex.Lock()
	defer obj._MfHoldingsFileDescriptionMutex.Unlock()
	obj._MfHoldingsFileDescription = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "MfHoldingsFileDescription")
	}
	return nil
}
func (obj *NetworthMcp) SetAaFileDescription(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *NetworthMcp.AaFileDescription", reflect.TypeOf(val))
	}
	obj._AaFileDescriptionMutex.Lock()
	defer obj._AaFileDescriptionMutex.Unlock()
	obj._AaFileDescription = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "AaFileDescription")
	}
	return nil
}
func (obj *NetworthMcp) SetEpfDescription(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *NetworthMcp.EpfDescription", reflect.TypeOf(val))
	}
	obj._EpfDescriptionMutex.Lock()
	defer obj._EpfDescriptionMutex.Unlock()
	obj._EpfDescription = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "EpfDescription")
	}
	return nil
}
func (obj *NetworthMcp) SetCreditReportDescription(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *NetworthMcp.CreditReportDescription", reflect.TypeOf(val))
	}
	obj._CreditReportDescriptionMutex.Lock()
	defer obj._CreditReportDescriptionMutex.Unlock()
	obj._CreditReportDescription = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "CreditReportDescription")
	}
	return nil
}
func (obj *NetworthMcp) SetMfTransactionsDescription(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *NetworthMcp.MfTransactionsDescription", reflect.TypeOf(val))
	}
	obj._MfTransactionsDescriptionMutex.Lock()
	defer obj._MfTransactionsDescriptionMutex.Unlock()
	obj._MfTransactionsDescription = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "MfTransactionsDescription")
	}
	return nil
}
func (obj *NetworthMcp) SetMfTransactionResponseSchemaDesc(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *NetworthMcp.MfTransactionResponseSchemaDesc", reflect.TypeOf(val))
	}
	obj._MfTransactionResponseSchemaDescMutex.Lock()
	defer obj._MfTransactionResponseSchemaDescMutex.Unlock()
	obj._MfTransactionResponseSchemaDesc = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "MfTransactionResponseSchemaDesc")
	}
	return nil
}
func (obj *NetworthMcp) SetBankTransactionFileDescription(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *NetworthMcp.BankTransactionFileDescription", reflect.TypeOf(val))
	}
	obj._BankTransactionFileDescriptionMutex.Lock()
	defer obj._BankTransactionFileDescriptionMutex.Unlock()
	obj._BankTransactionFileDescription = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "BankTransactionFileDescription")
	}
	return nil
}
func (obj *NetworthMcp) SetBankTransactionsResponseSchemaDesc(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *NetworthMcp.BankTransactionsResponseSchemaDesc", reflect.TypeOf(val))
	}
	obj._BankTransactionsResponseSchemaDescMutex.Lock()
	defer obj._BankTransactionsResponseSchemaDescMutex.Unlock()
	obj._BankTransactionsResponseSchemaDesc = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "BankTransactionsResponseSchemaDesc")
	}
	return nil
}
func (obj *NetworthMcp) SetManualAssetsFileDescription(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *NetworthMcp.ManualAssetsFileDescription", reflect.TypeOf(val))
	}
	obj._ManualAssetsFileDescriptionMutex.Lock()
	defer obj._ManualAssetsFileDescriptionMutex.Unlock()
	obj._ManualAssetsFileDescription = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "ManualAssetsFileDescription")
	}
	return nil
}
