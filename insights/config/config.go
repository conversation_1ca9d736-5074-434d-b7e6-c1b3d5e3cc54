//go:generate conf_gen github.com/epifi/gamma/insights/config Config
package config

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"sync"
	"time"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/aws/v2/kms"

	awspkg "github.com/epifi/be-common/pkg/aws/v2/config"
	"github.com/epifi/be-common/pkg/cfg"
	cfgv2 "github.com/epifi/be-common/pkg/cfg/v2"

	llmPkgConf "github.com/epifi/gamma/pkg/llm/config"
)

const (
	GoogleOAuthCredentials = "GoogleOAuthCredentials"
	MailDataEncryptionKey  = "MailDataEncryptionKey"
)

var (
	once sync.Once
	// TODO(Brijesh): Add the reason behind keeping config a var outside func
	config                                *Config
	_, b, _, _                            = runtime.Caller(0)
	EmailParserInsightsDbUsernamePassword = "EmailParserInsightsDbUsernamePassword"
	ActorInsightsDbUsernamePassword       = "ActorInsightsDbUsernamePassword"
)

func Load() (*Config, error) {
	var err error
	once.Do(func() {
		config, err = loadConfig()
	})
	if err != nil {
		return nil, err
	}
	return config, err
}

func loadConfig() (*Config, error) {
	// will be used only for test environment
	configDirPath := testEnvConfigDir()

	conf := &Config{}
	// loads config from file
	k, configPath, err := cfg.LoadConfigUsingKoanf(configDirPath, cfg.INSIGHTS_SERVICE)
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}

	if err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf)); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	err = cfg.LoadAllSecretsV3(conf, conf.Application.Environment, conf.AWS.Region)
	if err != nil {
		return nil, fmt.Errorf("failed to LoadAllSecretsV3: %w", err)
	}

	keyToIdMap := cfg.AddPgdbSslCertSecretIds(conf.InsightsDb, conf.Secrets.Ids)
	keyToIdMap = cfg.AddPgdbSslCertSecretIds(conf.ActorInsightsDb, conf.Secrets.Ids)

	keyToSecret, err := cfg.LoadSecrets(keyToIdMap, conf.Application.Environment, conf.AWS.Region)
	if err != nil {
		return nil, err
	}

	if err = updateDefaultConfig(conf, keyToSecret); err != nil {
		return nil, err
	}

	conf.PMSSearchConfig.IndexFilePath = filepath.Clean(filepath.Join(*configPath, conf.PMSSearchConfig.IndexFilePath))
	conf.PMSSearchConfig.ListFilePath = filepath.Clean(filepath.Join(*configPath, conf.PMSSearchConfig.ListFilePath))
	conf.AIFSearchConfig.IndexFilePath = filepath.Clean(filepath.Join(*configPath, conf.AIFSearchConfig.IndexFilePath))
	conf.AIFSearchConfig.ListFilePath = filepath.Clean(filepath.Join(*configPath, conf.AIFSearchConfig.ListFilePath))

	return conf, nil
}

// update the env variable
// update the default secret keys in the config with the secret values fetched from Secret manager
// update db endpoint
func updateDefaultConfig(c *Config, keyToSecret map[string]string) error {
	dbServerEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_RDS)
	cfg.UpdateDbEndpointInConfig(c.InsightsDb, dbServerEndpoint)
	cfg.UpdateDbEndpointInConfig(c.ActorInsightsDb, dbServerEndpoint)

	if err := readAndSetEnv(c); err != nil {
		return fmt.Errorf("failed to read and set env var: %w", err)
	}

	cfg.UpdatePGDBSecretValues(c.InsightsDb, c.Secrets, keyToSecret)
	cfg.UpdatePGDBSecretValues(c.ActorInsightsDb, c.Secrets, keyToSecret)

	if c.Application.Environment == cfg.TestEnv || c.Application.Environment == cfg.DevelopmentEnv {
		cfg.UpdateDbUsernamePasswordInConfig(c.InsightsDb, c.Secrets.Ids[EmailParserInsightsDbUsernamePassword])
		cfg.UpdateDbUsernamePasswordInConfig(c.ActorInsightsDb, c.Secrets.Ids[ActorInsightsDbUsernamePassword])
		return nil
	}
	if _, ok := keyToSecret[EmailParserInsightsDbUsernamePassword]; !ok {
		return fmt.Errorf("db username password not fetched for insights db from secrets manager")
	}
	cfg.UpdateDbUsernamePasswordInConfig(c.InsightsDb, keyToSecret[EmailParserInsightsDbUsernamePassword])
	if _, ok := keyToSecret[ActorInsightsDbUsernamePassword]; !ok {
		return fmt.Errorf("db username password not fetched for actor insights db from secrets manager")
	}
	cfg.UpdateDbUsernamePasswordInConfig(c.ActorInsightsDb, keyToSecret[ActorInsightsDbUsernamePassword])
	return nil
}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config) error {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Name = val
	}

	if val, ok := os.LookupEnv("SERVER_PORT"); ok {
		intVal, err := strconv.Atoi(val)
		if err != nil {
			return fmt.Errorf("failed to convert port to int: %w", err)
		}

		c.Server.Ports.GrpcPort = intVal
	}

	if val, ok := os.LookupEnv("PGDB_HOST"); ok {
		c.InsightsDb.Host = val
		c.ActorInsightsDb.Host = val
	}

	return nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..", "values")
	return configPath
}

func LoadKmsKeysForLocalStackEnv(conf *Config) error {
	if conf.Application.Environment != cfg.TestEnv && conf.Application.Environment != cfg.DevelopmentEnv && !cfg.IsTeamSpaceTenant() {
		return nil
	}
	ctx := context.Background()
	awsConf, err := awspkg.NewAWSConfig(ctx, conf.AWS.Region, true)
	if err != nil {
		return errors.Wrap(err, "error to initialize AWS config")
	}
	// create kms handle
	kmsClient := kms.InitKMSClient(awsConf)
	keyIds, err := kms.GetKeysByDescription(ctx, kmsClient, []string{conf.Application.GmailDataEncrKeyKMSId})
	if err != nil {
		return errors.Wrap(err, "unable to fetch kms key ids from kms by desc")
	}
	conf.Application.GmailDataEncrKeyKMSId = keyIds[0]
	return nil
}

type Config struct {
	Application                              *Application
	Server                                   *Server
	Logging                                  *cfg.Logging
	InsightsDb                               *cfg.DB
	ActorInsightsDb                          *cfg.DB
	DbConfigMap                              *cfg.DbConfigMap
	GmailBatchGetApiParams                   *GmailBatchGetApiParams
	EmailParserParams                        *EmailParserParams
	UserEmailAccessPublisher                 *cfg.SqsPublisher
	UserEmailAccessSubscriber                *cfg.SqsSubscriber `dynamic:"true"`
	PeriodicEmaiSyncPublisher                *cfg.SqsPublisher
	PeriodicEmailSyncSubscriber              *cfg.SqsSubscriber `dynamic:"true"`
	MailDataParserPublisher                  *cfg.SqsPublisher
	MailDataParserSubscriber                 *cfg.SqsSubscriber `dynamic:"true"`
	GmailUserSpendsPublisher                 *cfg.SnsPublisher
	EpfPassbookImportEventPublisher          *cfg.SnsPublisher
	OnboardingStageUpdateEventSubscriber     *cfg.SqsSubscriber `dynamic:"true"`
	AWS                                      *cfg.AWS
	Secrets                                  *cfg.Secrets
	GoogleOAuthParams                        *GoogleOAuthParams
	GmailListApiParams                       *GmailListApiParams
	MailFetchConcurrencyParams               *MailFetchConcurrencyParams
	Flags                                    *Flags `dynamic:"true"`
	AddGmailAccountBannerParams              *AddGmailAccountBannerParams
	InsightServing                           *InsightServing    `dynamic:"true"`
	CreateOrUpdateGenerationStatusSubscriber *cfg.SqsSubscriber `dynamic:"true" iam:"external-consumer"`
	StoreGeneratedActorInsightsSubscriber    *cfg.SqsSubscriber `dynamic:"true" iam:"external-consumer"`
	Tracing                                  *cfg.Tracing
	Profiling                                *cfg.Profiling
	SecureLoggingV2                          *cfgv2.SecureLogging
	RealTimeInsightParams                    *RealTimeInsightParams  `dynamic:"true"`
	GenerateInsightsSubscriber               *cfg.SqsSubscriber      `dynamic:"true" iam:"external-consumer"`
	StoryParams                              *StoryParams            `dynamic:"true"`
	EpfParams                                *EpfParams              `dynamic:"true"`
	NetworthParams                           *NetworthParams         `dynamic:"true"`
	NetWorthRefreshParams                    *NetWorthRefreshParams  `dynamic:"true"`
	PMSSearchConfig                          *SearchConfig           `dynamic:"true"`
	AIFSearchConfig                          *SearchConfig           `dynamic:"true"`
	EpfPassbookDataFlatteningSubscriber      *cfg.SqsSubscriber      `dynamic:"true"`
	UANAccountsCacheConfig                   *UANAccountsCacheConfig `dynamic:"true"`
	ProcessCaNewDataFetchEventSubscriber     *cfg.SqsSubscriber      `dynamic:"true"`
	LLMHandlerConfig                         *llmPkgConf.Config      `dynamic:"true"`
	MagicImportConfig                        *MagicImportConfig      `dynamic:"true"`
	NetworthMcp                              *NetworthMcp            `dynamic:"true"`
	DeleteCaAssetHistoriesEventSubscriber    *cfg.SqsSubscriber      `dynamic:"true"`
}

type MagicImportConfig struct {
	// ModelName maps to api/llm/model
	ModelName string `dynamic:"true"`
	// Corresponding cron job to update the cache system context
	// https://jenkins-prod.pointz.in/job/Scripts/job/Backend/job/AssetsAndAnalysis/job/ScheduledScripts/job/magic_import_cache_system_context/
	UseCachedSystemContext bool   `dynamic:"true"`
	S3BucketName           string `iam:"s3-readwrite"`
	EnableImageStorage     bool   `dynamic:"true"`
}

type NetworthMcp struct {
	NetWorthDataFileName               string `dynamic:"true"`
	FileDataDescription                string `dynamic:"true"`
	NetWorthFileDescription            string `dynamic:"true"`
	MfHoldingsFileDescription          string `dynamic:"true"`
	AaFileDescription                  string `dynamic:"true"`
	EpfDescription                     string `dynamic:"true"`
	CreditReportDescription            string `dynamic:"true"`
	MfTransactionsDescription          string `dynamic:"true"`
	MfTransactionResponseSchemaDesc    string `dynamic:"true"`
	BankTransactionFileDescription     string `dynamic:"true"`
	BankTransactionsResponseSchemaDesc string `dynamic:"true"`
	ManualAssetsFileDescription        string `dynamic:"true"`
}

type Application struct {
	Environment                string
	Name                       string
	GmailDataEncrKeyKMSId      string
	EmailIdRegex               string
	MailFetchDateRangeInMonths int
}

type Server struct {
	Ports *cfg.ServerPorts
}

type GmailBatchGetApiParams struct {
	ClientMaxIdleConns    int
	MaxIdleConnsPerHost   int
	ClientIdleConnTimeout int
	TimeoutInMillis       int
	BatchReqUrl           string
	GetMessageApi         string
}

type EmailParserParams struct {
	ClientMaxIdleConns    int
	MaxIdleConnsPerHost   int
	ClientIdleConnTimeout int
	TimeoutInMillis       int
	ParseReqUrl           string
}

type GoogleOAuthParams struct {
	ClientMaxIdleConns    int
	MaxIdleConnsPerHost   int
	ClientIdleConnTimeout int
	TimeoutInMillis       int
	RevokeTokenUrl        string
}

type GmailListApiParams struct {
	ClientMaxIdleConns    int
	MaxIdleConnsPerHost   int
	ClientIdleConnTimeout int
	TimeoutInMillis       int
}

type MailFetchConcurrencyParams struct {
	MaxGoroutinesPerUser int
	MessagePerGoroutine  int
}

type Flags struct {
	// A flag to determine if the debug message in Status is to be trimmed
	TrimDebugMessageFromStatus bool
	// if `ShowTeaserInsights` flag is true, we'll show teaser insights to the user.
	ShowTeaserInsights         bool
	RealTimeInsightFeatureFlag *cfg.FeatureReleaseConfig `dynamic:"true"`
}

type AddGmailAccountBannerParams struct {
	ShowAddAccountBanner   bool
	AddAccountRedirectUrl  string
	AddAccountWebViewTitle string
	// list of add gmail account banner variations
	AddAccountBannerVariations []*AddEmailAccountBannerDetails
}

type AddEmailAccountBannerDetails struct {
	VariationId string
	Title       string
	Description string
	IconUrl     string
	ButtonText  string
}

type InsightServing struct {
	InsightCapPerSession int `dynamic:"true"`
	// Minimum duration for which an actor insight can't be re-served once it is consumed by the user.
	ActorInsightRecurrenceMinDuration time.Duration `dynamic:"true"`
}

type RealTimeInsightParams struct {
	// time duration before a segment can be considered again for real time insight
	SegmentCoolDownPeriod time.Duration `dynamic:"true"`
	// with a probability dist in [1, 100], how likely is the flow for the real time insight invoked
	RealTimeInsightProb int32 `dynamic:"true"`
	// params to control actor insight generation log evaluation
	InsightGenerationLogEvalParams *InsightGenerationLogEvalParams `dynamic:"true"`
	// priority of each generator. Priority can be between [0, 100].
	// the priority is used as weight while selecting a generator to create realtime insight
	GeneratorPriority map[string]int `dynamic:"true"`
}

type InsightGenerationLogEvalParams struct {
	IsEvaluationEnabled bool `dynamic:"true"`
}

type StoryParams struct {
	IsLottieExperimentEnabledForInternal bool   `dynamic:"true"`
	LottieExperimentUrl                  string `dynamic:"true"`
}

type EpfParams struct {
	// timeout for fetching passbook. (note: vendor (karza) sla is 240s)
	PassbooksFetchTimeout time.Duration `dynamic:"true"`
}

type NetworthParams struct {
	AssetPrice *AssetPrice `dynamic:"true"`
	// Flag to use scheme analytics API for mutual fund aggregation value instead of MfPortfolioHistory API
	UseSchemeAnalyticsApiForMfAggVal bool `dynamic:"true"`
	// TODO(sainath): Remove this once its stable in prod
	// Keep this in sync with DebugActorIdsForDailyReport in frontend/config.go
	DebugActorIdsForDailyReport map[string]bool `dynamic:"true"`
}

type AssetPrice struct {
	Gold18CaratPerGramRs int64 `dynamic:"true"`
	Gold22CaratPerGramRs int64 `dynamic:"true"`
	Gold24CaratPerGramRs int64 `dynamic:"true"`
	SilverPricePerKgRs   int64 `dynamic:"true"`
	// Gold ETF MF that has the lowest tracking error among gold ETFs in India
	BenchmarkGoldEtfMFId string `dynamic:"true"`
}

type NetWorthRefreshParams struct {
	// NetWorthRefreshAsset -> RefreshThreshold mapping
	AssetsRefreshThreshold map[string]time.Duration `dynamic:"true"`
	// NetWorthRefreshAsset -> ProcessingRefreshThreshold mapping
	AssetsProcessingRefreshThreshold map[string]time.Duration `dynamic:"true"`
	// InvestmentInstrumentType -> RefreshThreshold mapping
	ManualAssetsRefreshThreshold map[string]time.Duration `dynamic:"true"`
	// Refresh order for NetWorth Refresh
	InstrumentsRefreshOrder []string `dynamic:"true"`
}

type SearchConfig struct {
	// Path to the file containing the list of documents mapped to an ElasticSearch index
	// Always append more documents to this list; never delete items from the list.
	// Deleting would lead to us not being able to map registration numbers for assets declared by users earlier.
	// Modification of provider names is allowed.
	ListFilePath string

	// Name of the index to use for storing entities
	// of a particular type (e.g., PMS providers) as ElasticSearch documents
	IndexName string

	// Path to file containing index mappings for each data field of a document
	IndexFilePath string

	// True, if documents should be ingested for searching synchronously during server start.
	// False, if documents can be ingested concurrently while the server is starting.
	IngestInSync bool `dynamic:"true"`

	// Max number of search hits to return for a query on the index
	QuerySize int `dynamic:"true"`
}

// (TODO: Anubhav) remove this once we know the caching strategy is stable. it'll help avoid unnecessary check
type UANAccountsCacheConfig struct {
	IsCachingEnabled bool          `dynamic:"true"`
	CacheTTL         time.Duration `dynamic:"true"`
}
