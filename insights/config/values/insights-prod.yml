Application:
  Environment: "prod"
  Name: "insights"
  GmailDataEncrKeyKMSId: "42425d5e-75e7-481b-a8ed-42ed43cf8176"
  EmailIdRegex: "[a-zA-Z0-9.!#$%&'*+\\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*"
  MailFetchDateRangeInMonths: 3

Server:
  Ports:
    GrpcPort: 8096
    GrpcSecurePort: 9511
    HttpPort: 9999
    HttpPProfPort: 9990

InsightsDb:
  AppName: "insights"
  StatementTimeout: 10s
  Name: "insights"
  EnableDebug: false
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  SecretName: "prod/rds/epifimetis/actor-insights"
  MaxOpenConn: 20
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

ActorInsightsDb:
  AppName: "insights"
  StatementTimeout: 10s
  Name: "actor_insights"
  EnableDebug: false
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  SecretName: "prod/rds/epifimetis/actor-insights"
  MaxOpenConn: 20
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

UserEmailAccessPublisher:
  QueueName: "prod-new-user-email-access-queue"

UserEmailAccessSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-new-user-email-access-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~20 min post that regular interval is followed for next 200 mins
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 1
          MaxAttempts: 10
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 10
          MaxAttempts: 20
          TimeUnit: "Minute"
      MaxAttempts: 30
      CutOff: 10
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 100
        Period: 1m
    Namespace: "insights"

PeriodicEmaiSyncPublisher:
  QueueName: "prod-periodic-mail-sync-queue"
  QueueOwnerAccountId: "************"

PeriodicEmailSyncSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-periodic-mail-sync-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 10
      MaxAttempts: 20
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 600
        Period: 1m
    Namespace: "insights"

MailDataParserPublisher:
  QueueName: "prod-mail-data-parser-queue"

MailDataParserSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-mail-data-parser-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~20 min post that regular interval is followed for next 200 mins
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 1
          MaxAttempts: 10
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 10
          MaxAttempts: 20
          TimeUnit: "Minute"
      MaxAttempts: 30
      CutOff: 10
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1s
    Namespace: "insights"

OnboardingStageUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-insights-onb-stage-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 5
      MaxAttempts: 10
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 600
        Period: 1m
    Namespace: "insights"

GmailUserSpendsPublisher:
  TopicName: "prod-gmail-user-spends-topic"

EpfPassbookImportEventPublisher:
  TopicName: "prod-epf-passbook-import-topic"

CreateOrUpdateGenerationStatusSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-insight-generation-script-run-status"
  QueueOwnerAccountId: "************"
  RetryStrategy:
    RegularInterval:
      Interval: 10
      MaxAttempts: 20
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 50
        Period: 1m
    Namespace: "insights"

StoreGeneratedActorInsightsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-generated-actor-insights"
  QueueOwnerAccountId: "************"
  RetryStrategy:
    RegularInterval:
      Interval: 10
      MaxAttempts: 20
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 100
        Period: 1m
    Namespace: "insights"

GenerateInsightsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 2
  QueueName: "prod-generate-insights"
  QueueOwnerAccountId: "************"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 2
        Period: 1s
    Namespace: "insights"

ProcessCaNewDataFetchEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 2
  QueueName: "prod-insights-ca-data-new-data-fetch-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 30
      MaxAttempts: 10
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 40
        Period: 1s
    Namespace: "insights"

AWS:
  Region: "ap-south-1"

GmailBatchGetApiParams:
  ClientMaxIdleConns: 10
  ClientIdleConnTimeout: 30
  MaxIdleConnsPerHost: 10
  TimeoutInMillis: 15000
  BatchReqUrl: "https://www.googleapis.com/batch/gmail/v1"
  GetMessageApi: "GET /gmail/v1/users/me/messages/"

GmailListApiParams:
  ClientMaxIdleConns: 25
  MaxIdleConnsPerHost: 25
  ClientIdleConnTimeout: 30
  TimeoutInMillis: 2000

EmailParserParams:
  ClientMaxIdleConns: 10
  MaxIdleConnsPerHost: 10
  ClientIdleConnTimeout: 30
  TimeoutInMillis: 1000
  ParseReqUrl: "https://email-parser.epifi.in/v1/parse"

GoogleOAuthParams:
  ClientMaxIdleConns: 10
  MaxIdleConnsPerHost: 10
  ClientIdleConnTimeout: 30
  TimeoutInMillis: 1500
  RevokeTokenUrl: "https://oauth2.googleapis.com/revoke"

MailFetchConcurrencyParams:
  MaxGoroutinesPerUser: 5
  MessagePerGoroutine: 10

Secrets:
  Ids:
    GoogleOAuthCredentials: "prod/insights-web/gmail-oauth-cred"
    EmailParserInsightsDbUsernamePassword: "prod/rds/epifimetis/insights"
    ActorInsightsDbUsernamePassword: "prod/rds/epifimetis/actor-insights"
    MailDataEncryptionKey: "prod/insights/mail-data-encr"

Flags:
  TrimDebugMessageFromStatus: false
  ShowTeaserInsights: false

AddGmailAccountBannerParams:
  ShowAddAccountBanner: true
  AddAccountRedirectUrl: "https://fi.money/insights"
  AddAccountWebViewTitle: "Insights"

Tracing:
  Enable: true

Profiling:
  StackDriverProfiling:
    ProjectId: "epifi-prod"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-prod-automatic-profiling"
    CPUPercentageLimit: 80
    MemoryPercentageLimit: 80
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

NetworthParams:
  AssetPrice:
    BenchmarkGoldEtfMFId: "MFl77fmLnjTXa7N6hEgeG1EQ230524==" # uti etf mf
  DebugActorIdsForDailyReport:
    - "AC2HCC4Txhz6250411": true
    - "AC220103Hapts4DoRsqNUTkIYeereA==": true # Sainath's actorid to debug indian stocks
  UseSchemeAnalyticsApiForMfAggVal: true

NetWorthRefreshParams:
  AssetsRefreshThreshold:
    - "NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS": 168h # 1 week
    - "NET_WORTH_REFRESH_ASSET_EPF": 1440h # 2 month
    - "NET_WORTH_REFRESH_ASSET_CREDIT_SCORE": 720h # 1 month. should be same as CreditReportRefreshBannerDurationInDays in CreditScoreAnalyserConfig
  AssetsProcessingRefreshThreshold:
    - "NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS": 30m
    - "NET_WORTH_REFRESH_ASSET_EPF": 30m
    - "NET_WORTH_REFRESH_ASSET_CREDIT_SCORE": 30m
  ManualAssetsRefreshThreshold:
    - "REAL_ESTATE": 4320h # 6 month
    - "AIF": 8640h # 1 year
    - "PRIVATE_EQUITY": 8640h # 1 year
    - "CASH": 4320h # 6 month
    - "BOND": 8640h # 1 year
    - "ART_AND_ARTEFACTS": 8640h # 1 year
  InstrumentsRefreshOrder: [
    "NET_WORTH_REFRESH_ASSET_CREDIT_SCORE",
    "NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS",
    "NET_WORTH_REFRESH_ASSET_EPF",
    "NET_WORTH_REFRESH_ASSET_MANUAL_ASSETS" ]

EpfPassbookDataFlatteningSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-epf-passbook-data-flattening-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 15
      MaxAttempts: 10
      TimeUnit: "Second"

UANAccountsCacheConfig:
  CacheTTL: "10m"
  IsCachingEnabled: true

DbConfigMap:
  EPIFI_TECH:
    DbType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "insights"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
  FEDERAL_BANK:
    DbType: "PGDB"
    AppName: "insights"
    StatementTimeout: 5s
    Name: "insights_federal"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/insights_federal_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
  EPIFI_WEALTH:
    DbType: "PGDB"
    AppName: "insights"
    StatementTimeout: 10s
    Name: "epifi_wealth_analytics"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 20
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/epifi-wealth-analytics"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms

MagicImportConfig:
  ModelName: "MODEL_GEMINI_V2_5_FLASH"
  UseCachedSystemContext: true
  S3BucketName: "epifi-prod-wealth-insights"
  EnableImageStorage: true


LLMHandlerConfig:
  Environment: "prod"
  GeminiConf:
    GoogleCloudCredentialsSecretPath: "prod/magic-import/gemini-secret"
    GenerationConf:
      IsEnabled: true
      Temperature: 0.5
  RedisOptions:
    # using wealth redis store for magic import use case
    IsSecureRedis: true
    Options:
      Addr: "redis-11355.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:11355"
    AuthDetails:
      SecretPath: "prod/redis/wealth/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
    ClientName: wealthonboarding
    HystrixCommand:
      CommandName: "llm_redis_circuit_breaker_command"
      TemplateName: "Q20"
      OverrideTemplateConfig:
        ExecutionMaxConcurrency: 500
        ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
        RequiredConsecutiveSuccessful: 6
        HalfOpenAttemptsAllowedPerSleepWindow: 10
        ErrorThresholdPercentage: 80
  VendorApiConf:
    # HTTP client config inspired from DefaultTransport of http package
    # https://golang.org/src/net/http/transport.go?h=DefaultTransport#L42
    HttpClientConfig:
      Transport:
        DialContext:
          Timeout: 30s
          KeepAlive: 30s
        TLSHandshakeTimeout: 10s
        MaxIdleConns: 100
        IdleConnTimeout: 90s
        MaxConnsPerHost: 500
        MaxIdleConnsPerHost: 50

NetworthMcp:
  NetWorthDataFileName: "Fi_summary.txt"
  FileDataDescription: "A financial portfolio management file that provides secure access to users' financial data through Fi Money, a a financial hub for all things money. This file enables users to:\n- Access comprehensive net worth analysis with asset/liability breakdowns.\n- Retrieve detailed transaction histories for mutual funds and EPF accounts\n- View credit reports with scores, loan details, and account histories, this also contains users date of birth that can be used for calculating their age\n\nIf the person asks, you can tell about Fi Money that it is money management platform that offers below services in partnership with regulated entities:\n\nAVAILABLE SERVICES:\n- Digital savings account with zero Forex cards\n- Invest in Indian Mutual funds, US Stocks (partnership with licensed brokers), Smart and Fixed Deposits.\n- Intant Personal Loans \n- Faster UPI and Bank Transfers payments\n- Credit score monitoring and reports\n\nIMPORTANT LIMITATIONS:\n- This file retrieves only actual user data via Net worth tracker and based on consent provided by the user  and does not generate hypothetical or estimated financial information\n- In this version of the file, user's historical bank transactions, historical stocks transaction data, salary (unless catagorically declared) is not present. Don't assume these data points for any kind of analysis.\n\nCRITICAL INSTRUCTIONS FOR FINANCIAL DATA:\n\n1. DATA BOUNDARIES: Only provide information that exists in the user's Fi Money Net worth tracker. Never estimate, extrapolate, or generate hypothetical financial data.\n\n2. SPENDING ANALYSIS: If user asks about spending patterns, categories, or analysis tell the user we currently don't offer that data through the MCP:\n   - For detailed spending insights, direct them to: \"For comprehensive spending analysis and categorization, please use the Fi Money mobile app which provides detailed spending insights and budgeting tools.\"\n\n3. MISSING DATA HANDLING: If requested data is not available:\n   - Clearly state what data is missing\n   - Explain how user can connect additional accounts in Fi Money app\n   - Never fill gaps with estimated or generic information\n"
  NetWorthFileDescription: "Calculate comprehensive net worth using ONLY actual data from accounts users connected on Fi Money including:\n- Bank account balances\n- Mutual fund investment holdings\n- Indian Stocks investment holdings\n- Total US Stocks investment (If investing through Fi Money app)\n- EPF account balances\n- Credit card debt and loan balances (if credit report connected)\n- Any other assets/liabilities linked to Fi Money platform"
  MfHoldingsFileDescription: "Calculate comprehensive mutual fund holdings:\n- Mutual fund investment holdings\n\nUse this component when the user asks for:\n- Their current total investment in mutual funds\n- Analysis of their individual mutual funds\n\nERROR HANDLING:\n- If mutual funds are not detected, please ask the user whether they hold any mutual funds and prompt them to connect their mutual fund accounts via the Fi app."
  AaFileDescription: "Calculate comprehensive data from accounts users connected on Fi Money including:\n   - Bank account balances\n   - Indian Stocks investment holdings (equity, etf, reit, invit, etc.)\n   - Any deposit account\n\n   Use this data when the user asks for:\n   - Analysis of their bank account details   \n   - Analysis of their indian stocks holdings\n\n   IMPORTANT: This tool returns ONLY declared account data. It does not estimate or calculate values for unconnected accounts. If user has accounts not connected to Fi Money, they will not be included in calculations.\n\n   ERROR HANDLING:\n   - If no financial accounts are connected that the user specifically asked for, returns empty result with message to connect those accounts in Fi Money app."
  EpfDescription: "Retrieve detailed EPF (Employee Provident Fund) account information including:\n- Account balance and contributions\n- Employer and employee contribution history\n- Interest earned and credited amounts\n\nIMPORTANT LIMITATIONS:\n- Only returns EPF data if user has successfully linked their EPF account through Fi Money app\n- Data accuracy depends on EPFO integration and user's UAN verification status\n- Transaction history and account statements and passbooks are not available\n\nERROR HANDLING: If EPF account not connected, direct user to link EPF account in Fi Money app using their UAN and other required details.\n"
  CreditReportDescription: "Retrieve comprehensive credit report including scores, active loans, credit card utilization, payment history, date of birth and recent inquiries from connected credit bureaus.\n\nUse this tool when the user asks for:\n- Their credit score related information\n- This tool can provide their age related information through date of birth\n- Which loans to close first based on the loan that has the highest interest rate\n\nIMPORTANT LIMITATIONS:\n- Only returns credit data if user has successfully connected their credit profile in Fi Money app\n- Cannot access credit data from bureaus not integrated with Fi Money platform\n\nERROR HANDLING: If no credit score data is found, respond with: \"No credit score data available. Please connect your credit score in the Fi Money app and try again.\""
  MfTransactionsDescription: "Retrieve detailed transaction history from accounts connected to Fi Money platform including:\n- Mutual fund transactions\n\nUse this tool when the user asks for:\n- Their portfolio level XIRR (For now, it is only applicable to Mutual funds)\n\nIMPORTANT LIMITATIONS:\n- Current version does NOT include historical bank transactions, historical stocks transactions, NPS, deposits.\n\nERROR HANDLING: Returns only available transaction data with clear indication of data source limitations.\n"
  MfTransactionResponseSchemaDesc: "A list of mutual fund investments. We currently support %d transactions across all mutual funds(mutual funds with older transactions will be trimmed off, if limit exceeds). Each 'txns' field is a list of data arrays with schema: [ orderType(1 for BUY and 2 for SELL), transactionDate, purchasePrice, purchaseUnits, transactionAmount ]."
  BankTransactionFileDescription: "Retrieve detailed bank transactions for each bank account connected to Fi money platform"
  BankTransactionsResponseSchemaDesc: "A list of bank transactions. Each 'txns' field is a list of data arrays with schema: [transactionAmount, transactionNarration, transactionDate, transactionType (1 for CREDIT, 2 for DEBIT, 3 for OPENING, 4 for INTEREST, 5 for TDS, 6 for INSTALLMENT, 7 for CLOSING and 8 for OTHERS), transactionMode, currentBalance]."
  ManualAssetsFileDescription: "Retrieve details of manually connected assets on fi app"
