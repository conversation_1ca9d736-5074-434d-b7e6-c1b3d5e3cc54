package networth

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"path/filepath"
	"time"

	awsS3TypesPb "github.com/aws/aws-sdk-go-v2/service/s3/types"
	"go.uber.org/zap"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/epifi/be-common/pkg/epifigrpc"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epificontext"

	filePb "github.com/epifi/be-common/api/typesv2/common/file"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	networthPb "github.com/epifi/gamma/api/insights/networth"
	magicimportPb "github.com/epifi/gamma/api/insights/networth/magicimport"
	networthModelPb "github.com/epifi/gamma/api/insights/networth/model"
	llmPb "github.com/epifi/gamma/api/llm"
	"github.com/epifi/gamma/insights/networth/events"
	magicimport "github.com/epifi/gamma/insights/networth/magic_import"
)

const (
	magicImportImagesS3Prefix = "networth/magic_import_images"
)

func (s *Service) MagicImportFiles(ctx context.Context, req *networthPb.MagicImportFilesRequest) (*networthPb.MagicImportFilesResponse, error) {
	var (
		errRes = func(err error) *networthPb.MagicImportFilesResponse {
			return &networthPb.MagicImportFilesResponse{
				Status: rpc.StatusInternalWithDebugMsg(err.Error()),
			}
		}
	)
	if err := s.validateReq(req); err != nil {
		var status *rpc.Status
		switch {
		case errors.Is(err, epifierrors.ErrInvalidArgument):
			logger.Info(ctx, "validation failed for MagicImportFilesRequest", zap.Error(err))
			status = rpc.StatusInvalidArgumentWithDebugMsg(err.Error())
		case errors.Is(err, epifierrors.ErrPermissionDenied):
			logger.Info(ctx, "validation failed for MagicImportFilesRequest", zap.Error(err))
			status = rpc.StatusPermissionDeniedWithDebugMsg(err.Error())
		default:
			logger.Error(ctx, "validation failed for MagicImportFilesRequest", zap.Error(err))
			status = rpc.StatusInternalWithDebugMsg(err.Error())
		}
		return &networthPb.MagicImportFilesResponse{
			Status: status,
		}, nil
	}
	var (
		response string
		err      error
	)
	model := s.getModelForMagicImport(ctx)

	res, err := s.llmHandler.GenerateContent(ctx, &llmPb.GenerateContentRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_GOOGLE,
		},
		Model:         model,
		UseCase:       llmPb.UseCase_USE_CASE_NETWORTH_MAGIC_IMPORT,
		SystemContext: magicimport.GetMagicImportContext(),
		Attachments: &llmPb.Attachments{
			Files: req.GetFiles(),
		},
		UseCachedContext: commontypes.BoolToBooleanEnum(s.config.MagicImportConfig().UseCachedSystemContext()),
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		logger.Error(ctx, "GenerateContent API failed", zap.Error(rpcErr))
		return errRes(rpcErr), nil
	}
	response = res.GetTextContent()
	promptRes := &magicimport.MagicImportPromptResponse{}
	err = json.Unmarshal([]byte(response), &promptRes)
	if err != nil {
		logger.Error(ctx, "unmarshalling response failed", zap.Error(err))
		return &networthPb.MagicImportFilesResponse{
			Status: rpc.NewStatus(uint32(networthPb.MagicImportFilesResponse_MALFORMED_LLM_RESPONSE), "", err.Error()),
		}, nil
	}
	promptRes.RequestId = fmt.Sprint(time.Now().UnixNano())
	goroutine.Run(ctx, 30*time.Second, func(ctx context.Context) {
		fileNameToS3Path := make(map[string]interface{})
		if !s.config.MagicImportConfig().EnableImageStorage() {
			s.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), events.NewMagicImportFilesEvent(req.GetActorId(), response, fileNameToS3Path))
			return
		}
		for _, file := range req.GetFiles() {
			decodedImg, decodeErr := base64.StdEncoding.DecodeString(file.GetBase64Data())
			if decodeErr != nil {
				logger.Error(ctx, "failed to decode image base64 string", zap.Error(decodeErr))
				continue
			}
			s3FilePath := filepath.Join(magicImportImagesS3Prefix, req.GetActorId(), promptRes.RequestId, file.GetFileName())
			if s3Err := s.networthS3Client.Write(ctx, s3FilePath, decodedImg, string(awsS3TypesPb.ObjectCannedACLBucketOwnerFullControl)); s3Err != nil {
				logger.Error(ctx, "error in s3 write for magic import", zap.Error(s3Err))
				continue
			}
			// todo(saiteja): https://github.com/epiFi/tickets/issues/57212 decide if we can remove this based on the storage of file paths per request
			logger.Info(ctx, "magic import s3 upload successful", zap.String(logger.PATH, s3FilePath), zap.String(logger.FILE_NAME, file.GetFileName()))
			fileNameToS3Path[file.GetFileName()] = s3FilePath
		}
		s.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), events.NewMagicImportFilesEvent(req.GetActorId(), response, fileNameToS3Path))
	})
	assets := make([]*magicimportPb.MagicImportAssetDetails, 0)
	// Collection of conventional assets and their derived details
	for _, conventionalAsset := range promptRes.GetConventionalAssets() {
		investmentDeclaration := conventionalAsset.GetInvestmentDeclaration()
		logger.Info(ctx, "Magic Import: conventional asset info",
			zap.String(logger.REQUEST_TYPE, investmentDeclaration.GetInstrumentType().String()))
		investmentDetails, enrichErr := s.investmentDeclarationProcessor.EnrichInvestmentDetails(ctx, investmentDeclaration)
		if enrichErr != nil {
			logger.Error(ctx, "error in EnrichInvestmentDetails", zap.Error(enrichErr), zap.String(logger.FILE_TYPE, investmentDeclaration.GetInstrumentType().String()))
			return errRes(enrichErr), nil
		}
		declDetails := investmentDeclaration.GetDeclarationDetails().GetDetails()
		assetGenericDetails, ok := declDetails.(networthModelPb.GenericInvestmentDetails)
		if !ok {
			logger.Error(ctx, "failed to get generic investment details from declaration", zap.Any("declarationDetails", declDetails), zap.String(logger.REQUEST_TYPE, investmentDeclaration.GetInstrumentType().String()))
			// ignoring error to fill the remaining assets with available data
			continue
		}
		assets = append(assets, &magicimportPb.MagicImportAssetDetails{
			AssetType: magicimportPb.MagicImportAssetType_MAGIC_IMPORT_ASSET_TYPE_CONVENTIONAL,
			Details: &magicimportPb.MagicImportAssetDetails_ConventionalAssetDetails{
				ConventionalAssetDetails: &magicimportPb.ConventionalAssetDetails{
					InvestmentName:        assetGenericDetails.GetInvestmentName(),
					EstimatedValue:        investmentDetails.GetCurrentValue(),
					InvestmentDeclaration: investmentDeclaration,
					FileName:              conventionalAsset.GetFileName(),
				},
			},
		})

	}
	// Collection of unconventional assets and their derived details
	for _, unconventionalAsset := range promptRes.GetUnconventionalAssets() {
		logger.Info(ctx, "Magic Import: unconventional asset info",
			zap.String(logger.REQUEST_TYPE, unconventionalAsset.GetAssetType()),
			zap.String(logger.FILE_NAME, unconventionalAsset.GetName()))
		assets = append(assets, &magicimportPb.MagicImportAssetDetails{
			AssetType: magicimportPb.MagicImportAssetType_MAGIC_IMPORT_ASSET_TYPE_UNCONVENTIONAL,
			Details: &magicimportPb.MagicImportAssetDetails_UnconventionalAssetDetails{
				UnconventionalAssetDetails: &magicimportPb.UnconventionalAssetDetails{
					AssetName:    unconventionalAsset.GetName(),
					AssetType:    unconventionalAsset.GetAssetType(),
					AiCommentary: unconventionalAsset.GetAICommentary(),
				},
			},
		})
	}

	return &networthPb.MagicImportFilesResponse{
		Status: rpc.StatusOk(),
		MagicImportDetails: &magicimportPb.MagicImportDetails{
			AiCommentary:        promptRes.GetAICommentary(),
			OneWordAiCommentary: promptRes.GetOneWordAICommentary(),
			AssetDetailsList:    assets,
		},
	}, nil
}

func (s *Service) validateReq(req *networthPb.MagicImportFilesRequest) error {
	if len(req.GetFiles()) == 0 {
		return fmt.Errorf("no files in request %w", epifierrors.ErrInvalidArgument)
	}
	if req.GetActorId() == "" {
		return fmt.Errorf("actor id is empty %w", epifierrors.ErrInvalidArgument)
	}
	if len(req.GetFiles()) > 5 {
		return fmt.Errorf("maximum 5 files allowed, got %d files %w", len(req.GetFiles()), epifierrors.ErrPermissionDenied)
	}
	for _, file := range req.GetFiles() {
		if file.GetBase64Data() == "" || file.GetType() == filePb.FileType_FILE_TYPE_UNSPECIFIED || file.GetFileName() == "" {
			return fmt.Errorf("file base64 data or type is empty %w", epifierrors.ErrInvalidArgument)
		}
	}
	return nil
}

func (s *Service) getModelForMagicImport(ctx context.Context) llmPb.Model {
	model := llmPb.Model_MODEL_GEMINI_V2_5_FLASH
	modelValue, ok := llmPb.Model_value[s.config.MagicImportConfig().ModelName()]
	if !ok {
		logger.Info(ctx, "model name not found in llmPb.Model_value", zap.String(logger.REQUEST_TYPE, s.config.MagicImportConfig().ModelName()))
		return model
	}
	return llmPb.Model(modelValue)
}
