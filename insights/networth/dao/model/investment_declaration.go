package model

import (
	"time"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	moneyPb "github.com/epifi/be-common/pkg/money"

	"github.com/epifi/be-common/pkg/nulltypes"
	"github.com/epifi/be-common/pkg/pagination"

	modelPb "github.com/epifi/gamma/api/insights/networth/model"
	types "github.com/epifi/gamma/api/typesv2"
)

type InvestmentDeclaration struct {
	Id      string `gorm:"type:uuid;default:gen_random_uuid();primaryKey"`
	ActorId string
	// instrument in which money was invested
	InstrumentType types.InvestmentInstrumentType
	// amount invested in instrument
	InvestedAmount *moneyPb.Money
	// time at which money was invested in the instrument
	InvestedAt time.Time
	// time at which invested amount will mature
	MaturityDate nulltypes.NullTime
	// the rate at which invested amount is growing
	InterestRate nulltypes.NullFloat64
	// metadata for instrument specific details
	DeclarationDetails *modelPb.OtherDeclarationDetails
	// unique id of the declaration that can be shared externally
	ExternalId nulltypes.NullString `gorm:"type:uuid;default:gen_random_uuid()"`
	// reference to consent registered when storing the declaration
	ConsentId nulltypes.NullString
	Source    modelPb.Source
	CreatedAt time.Time `gorm:"not null"`
	UpdatedAt time.Time `gorm:"not null"`
	DeletedAt gorm.DeletedAt
}

func (inv *InvestmentDeclaration) TableName() string {
	return "investment_declarations"
}

func NewInvestmentDeclaration(proto *modelPb.InvestmentDeclaration) *InvestmentDeclaration {
	model := &InvestmentDeclaration{
		Id:                 proto.GetId(),
		ActorId:            proto.GetActorId(),
		InstrumentType:     proto.GetInstrumentType(),
		InvestedAmount:     &moneyPb.Money{Pb: proto.GetInvestedAmount()},
		InvestedAt:         proto.GetInvestedAt().AsTime(),
		InterestRate:       nulltypes.NewNullFloat64(proto.GetInterestRate()),
		DeclarationDetails: proto.GetDeclarationDetails(),
		ExternalId:         nulltypes.NewNullString(proto.GetExternalId()),
		ConsentId:          nulltypes.NewNullString(proto.GetConsentId()),
		Source:             proto.GetSource(),
	}
	if proto.GetDeletedAt() != nil {
		model.DeletedAt = gorm.DeletedAt{
			Time:  proto.GetDeletedAt().AsTime(),
			Valid: true,
		}
	}
	maturityDate := time.Time{}
	if proto.GetMaturityDate() != nil {
		maturityDate = proto.GetMaturityDate().AsTime()
	}
	model.MaturityDate = nulltypes.NewNullTime(maturityDate)

	// Even if old clients are sending values in the old field, the new field gets populated with that value too.
	if proto.GetDeclarationDetails().GetAif().GetAifName() != "" {
		model.DeclarationDetails.GetAif().Identifier = &modelPb.AIF_AifNameV2{
			AifNameV2: proto.GetDeclarationDetails().GetAif().GetAifName(),
		}
	}
	if proto.GetDeclarationDetails().GetPortfolioManagementService().GetAmcName() != "" {
		model.DeclarationDetails.GetPortfolioManagementService().Identifier = &modelPb.PortfolioManagementService_AmcNameV2{
			AmcNameV2: proto.GetDeclarationDetails().GetPortfolioManagementService().GetAmcName(),
		}
	}
	return model
}

func (inv *InvestmentDeclaration) ToProto() *modelPb.InvestmentDeclaration {
	proto := &modelPb.InvestmentDeclaration{
		Id:                 inv.Id,
		ActorId:            inv.ActorId,
		InstrumentType:     inv.InstrumentType,
		InvestedAmount:     inv.InvestedAmount.GetPb(),
		InterestRate:       inv.InterestRate.GetValue(),
		DeclarationDetails: inv.DeclarationDetails,
		MaturityDate:       inv.MaturityDate.GetProto(),
		ExternalId:         inv.ExternalId.GetValue(),
		ConsentId:          inv.ConsentId.GetValue(),
		Source:             inv.Source,
	}
	if !inv.CreatedAt.IsZero() {
		proto.CreatedAt = timestampPb.New(inv.CreatedAt)
	}
	if !inv.UpdatedAt.IsZero() {
		proto.UpdatedAt = timestampPb.New(inv.UpdatedAt)
	}
	if !inv.InvestedAt.IsZero() {
		proto.InvestedAt = timestampPb.New(inv.InvestedAt)
	}
	if inv.DeletedAt.Valid {
		proto.DeletedAt = timestampPb.New(inv.DeletedAt.Time)
	}
	// Populating the new AIF name field too with the old field's value so that
	// new clients trying to modify a form submitted with an old client version
	// do not have to depend on the old field.
	if inv.DeclarationDetails.GetAif().GetAifName() != "" {
		proto.GetDeclarationDetails().GetAif().Identifier = &modelPb.AIF_AifNameV2{
			AifNameV2: inv.DeclarationDetails.GetAif().GetAifName(),
		}
	}
	if inv.DeclarationDetails.GetPortfolioManagementService().GetAmcName() != "" {
		proto.GetDeclarationDetails().GetPortfolioManagementService().Identifier = &modelPb.PortfolioManagementService_AmcNameV2{
			AmcNameV2: inv.DeclarationDetails.GetPortfolioManagementService().GetAmcName(),
		}
	}
	convertOldRecurringDepositsToNewRecurringDeposits(proto)
	convertOldFixedDepositsToNewFixedDeposits(proto)

	return proto
}

// hotfix: there are some old records with instrument type as recurring deposits but have fixed deposit declaration details
// todo: write a cleanup script to fix the old records
func convertOldRecurringDepositsToNewRecurringDeposits(declaration *modelPb.InvestmentDeclaration) {
	if declaration.GetInstrumentType() != types.InvestmentInstrumentType_RECURRING_DEPOSIT {
		return
	}
	if declaration.GetDeclarationDetails().GetFixedDepositDeclarationDetails() != nil {
		declaration.DeclarationDetails.Details = &modelPb.OtherDeclarationDetails_RecurringDepositDeclarationDetails{
			RecurringDepositDeclarationDetails: &modelPb.RecurringDepositDeclarationDetails{
				CompoundingFrequency: declaration.GetDeclarationDetails().GetFixedDepositDeclarationDetails().GetCompoundingFrequency(),
				DepositName:          declaration.GetDeclarationDetails().GetFixedDepositDeclarationDetails().GetDepositName(),
				CurrentValue:         declaration.GetDeclarationDetails().GetFixedDepositDeclarationDetails().GetCurrentValue(),
			},
		}
	}
}

// hotfix: there are some old records with instrument type as recurring deposits but have fixed deposit declaration details
// todo: write a cleanup script to fix the old records
func convertOldFixedDepositsToNewFixedDeposits(declaration *modelPb.InvestmentDeclaration) {
	if declaration.GetInstrumentType() != types.InvestmentInstrumentType_FIXED_DEPOSIT {
		return
	}
	if declaration.GetDeclarationDetails().GetRecurringDepositDeclarationDetails() != nil {
		declaration.DeclarationDetails.Details = &modelPb.OtherDeclarationDetails_FixedDepositDeclarationDetails{
			FixedDepositDeclarationDetails: &modelPb.FixedDepositDeclarationDetails{
				CompoundingFrequency: declaration.GetDeclarationDetails().GetRecurringDepositDeclarationDetails().GetCompoundingFrequency(),
				DepositName:          declaration.GetDeclarationDetails().GetRecurringDepositDeclarationDetails().GetDepositName(),
				CurrentValue:         declaration.GetDeclarationDetails().GetRecurringDepositDeclarationDetails().GetCurrentValue(),
			},
		}
	}
}

type InvestmentDeclarations []*InvestmentDeclaration

func (m InvestmentDeclarations) Slice(start, end int) pagination.Rows { return m[start:end] }
func (m InvestmentDeclarations) GetTimestamp(index int) time.Time {
	return m[index].CreatedAt
}
func (m InvestmentDeclarations) Size() int { return len(m) }
