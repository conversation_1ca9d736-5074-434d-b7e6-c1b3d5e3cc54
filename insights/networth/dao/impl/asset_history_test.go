package impl

import (
	"context"
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/epifi/be-common/pkg/datetime"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/gamma/api/insights/networth/enums"
	networthModelPb "github.com/epifi/gamma/api/insights/networth/model"
)

const (
	testOwnership = commontypes.Ownership_FEDERAL_BANK
)

var (
	// Fixture data in Proto form for testing
	fixtureData = []*networthModelPb.AssetHistory{
		{
			ActorId:   "actor1",
			AssetType: enums.AssetType_ASSET_TYPE_INDIAN_SECURITIES,
			Data: &networthModelPb.AssetData{
				AssetData: &networthModelPb.AssetData_IndianStocksData{
					IndianStocksData: &networthModelPb.IndianStocksData{
						IndianStocks: []*networthModelPb.IndianStock{
							{
								Units:             215,
								SecurityListingId: "INS3ViAb",
							},
						},
					},
				},
			},
			HistoryDate: timestampPb.New(time.Date(2025, 6, 2, 0, 0, 0, 0, datetime.IST)),
		},
		{
			ActorId:   "actor1",
			AssetType: enums.AssetType_ASSET_TYPE_MUTUAL_FUND,
			Data: &networthModelPb.AssetData{
				AssetData: &networthModelPb.AssetData_MfData{
					MfData: &networthModelPb.MfData{
						MfSchemes: []*networthModelPb.MfScheme{
							{
								Units: 100.5,
								MfId:  "MF001",
							},
						},
					},
				},
			},
			HistoryDate: timestampPb.New(time.Date(2025, 6, 2, 0, 0, 0, 0, datetime.IST)),
		},
		{
			ActorId:   "actor2",
			AssetType: enums.AssetType_ASSET_TYPE_INDIAN_SECURITIES,
			Data: &networthModelPb.AssetData{
				AssetData: &networthModelPb.AssetData_IndianStocksData{
					IndianStocksData: &networthModelPb.IndianStocksData{
						IndianStocks: []*networthModelPb.IndianStock{
							{
								Units:             100,
								SecurityListingId: "INS4ViCd",
							},
							{
								Units:             75,
								SecurityListingId: "INS5ViEf",
							},
						},
					},
				},
			},
			HistoryDate: timestampPb.New(time.Date(2025, 6, 2, 0, 0, 0, 0, datetime.IST)),
		},
		{
			ActorId:   "actor2",
			AssetType: enums.AssetType_ASSET_TYPE_MUTUAL_FUND,
			Data: &networthModelPb.AssetData{
				AssetData: &networthModelPb.AssetData_MfData{
					MfData: &networthModelPb.MfData{
						MfSchemes: []*networthModelPb.MfScheme{
							{
								Units: 120,
								MfId:  "MF002",
							},
						},
					},
				},
			},
			HistoryDate: timestampPb.New(time.Date(2025, 6, 2, 0, 0, 0, 0, datetime.IST)),
		},
	}
)

func TestAssetHistoryPGDB_Create(t *testing.T) {
	type args struct {
		ctx          context.Context
		assetHistory *networthModelPb.AssetHistory
	}
	tests := []struct {
		name    string
		args    args
		want    *networthModelPb.AssetHistory
		wantErr bool
		err     error
	}{
		{
			name: "failed to validate asset history(empty asset type)",
			args: args{
				ctx: context.Background(),
				assetHistory: &networthModelPb.AssetHistory{
					ActorId: "actor-1",
					Data: &networthModelPb.AssetData{
						CurrentValue: &money.Money{
							CurrencyCode: "INR",
							Units:        1000,
							Nanos:        0,
						},
					},
					HistoryDate: &timestampPb.Timestamp{Seconds: **********},
				},
			},
			wantErr: true,
		},
		{
			name: "failed to validate asset history(empty data)",
			args: args{
				ctx: context.Background(),
				assetHistory: &networthModelPb.AssetHistory{
					ActorId:     "actor-1",
					AssetType:   enums.AssetType_ASSET_TYPE_NPS,
					HistoryDate: &timestampPb.Timestamp{Seconds: **********},
				},
			},
			wantErr: true,
		},
		{
			name: "failed to validate asset history(empty history date)",
			args: args{
				ctx: context.Background(),
				assetHistory: &networthModelPb.AssetHistory{
					ActorId:   "actor-1",
					AssetType: enums.AssetType_ASSET_TYPE_NPS,
					Data: &networthModelPb.AssetData{
						CurrentValue: &money.Money{
							CurrencyCode: "INR",
							Units:        1000,
							Nanos:        0,
						},
					},
				},
			},
			wantErr: true,
		},
		{
			name: "successfully create asset history",
			args: args{
				ctx: context.Background(),
				assetHistory: &networthModelPb.AssetHistory{
					ActorId:   "actor-1",
					AssetType: enums.AssetType_ASSET_TYPE_NPS,
					Data: &networthModelPb.AssetData{
						CurrentValue: &money.Money{
							CurrencyCode: "INR",
							Units:        1000,
							Nanos:        0,
						},
					},
					HistoryDate: &timestampPb.Timestamp{Seconds: **********},
				},
			},
			want: &networthModelPb.AssetHistory{
				ActorId:   "actor-1",
				AssetType: enums.AssetType_ASSET_TYPE_NPS,
				Data: &networthModelPb.AssetData{
					CurrentValue: &money.Money{
						CurrencyCode: "INR",
						Units:        1000,
						Nanos:        0,
					},
				},
				HistoryDate: &timestampPb.Timestamp{Seconds: **********},
			},
			wantErr: false,
		},
	}
	a := NewAssetHistoryPGDB(useCaseDbProvider)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := a.Create(epificontext.WithOwnership(tt.args.ctx, testOwnership), tt.args.assetHistory)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&networthModelPb.AssetHistory{}, "id", "created_at", "updated_at"),
			}
			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("Create() got = %v,\n want %v\n diff %s", got, tt.want, diff)
			}
		})
	}
}

func TestAssetHistoryPGDB_GetMultipleHistoriesByDate(t *testing.T) {
	type args struct {
		ctx         context.Context
		actorId     string
		assetTypes  []enums.AssetType
		historyDate *timestampPb.Timestamp
	}
	tests := []struct {
		name    string
		args    args
		want    map[enums.AssetType]*networthModelPb.AssetHistory
		wantErr bool
		err     error
	}{
		{
			name: "successfully get latest records when historyDate matches an existing record date",
			args: args{
				ctx:         context.Background(),
				actorId:     "actorId1",
				assetTypes:  []enums.AssetType{enums.AssetType_ASSET_TYPE_MUTUAL_FUND, enums.AssetType_ASSET_TYPE_SAVINGS_ACCOUNTS, enums.AssetType_ASSET_TYPE_NPS},
				historyDate: &timestampPb.Timestamp{Seconds: *********0}, // 2022-10-30
			},
			want: map[enums.AssetType]*networthModelPb.AssetHistory{
				enums.AssetType_ASSET_TYPE_MUTUAL_FUND: { // Data from '2022-10-30'
					ActorId:   "actorId1",
					AssetType: enums.AssetType_ASSET_TYPE_MUTUAL_FUND,
					Data: &networthModelPb.AssetData{
						CurrentValue: &money.Money{Units: 110000, CurrencyCode: "INR"},
					},
					HistoryDate: &timestampPb.Timestamp{Seconds: *********0}, // 2022-10-30
				},
				enums.AssetType_ASSET_TYPE_SAVINGS_ACCOUNTS: { // Data from '2022-10-30'
					ActorId:   "actorId1",
					AssetType: enums.AssetType_ASSET_TYPE_SAVINGS_ACCOUNTS,
					Data: &networthModelPb.AssetData{
						CurrentValue: &money.Money{Units: 2100, CurrencyCode: "INR"},
					},
					HistoryDate: &timestampPb.Timestamp{Seconds: *********0}, // 2022-10-30
				},
			},
			wantErr: false,
		},
		{
			name: "successfully get latest record when historyDate is after all existing record dates",
			args: args{
				ctx:         context.Background(),
				actorId:     "actorId1",
				assetTypes:  []enums.AssetType{enums.AssetType_ASSET_TYPE_MUTUAL_FUND},
				historyDate: &timestampPb.Timestamp{Seconds: **********}, // 2024-10-28
			},
			want: map[enums.AssetType]*networthModelPb.AssetHistory{
				enums.AssetType_ASSET_TYPE_MUTUAL_FUND: { // Should pick the latest existing record (2022-10-30)
					ActorId:   "actorId1",
					AssetType: enums.AssetType_ASSET_TYPE_MUTUAL_FUND,
					Data: &networthModelPb.AssetData{
						CurrentValue: &money.Money{Units: 110000, CurrencyCode: "INR"},
					},
					HistoryDate: &timestampPb.Timestamp{Seconds: *********0}, // 2022-10-30
				},
			},
			wantErr: false,
		},
		{
			name: "Record not found when historyDate is before any existing record dates",
			args: args{
				ctx:         context.Background(),
				actorId:     "actorId1",
				assetTypes:  []enums.AssetType{enums.AssetType_ASSET_TYPE_MUTUAL_FUND},
				historyDate: &timestampPb.Timestamp{Seconds: **********}, // 2022-01-01
			},
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
		{
			name: "successfully get latest records with various past dates",
			args: args{
				ctx:         context.Background(),
				actorId:     "actorId1",
				assetTypes:  []enums.AssetType{enums.AssetType_ASSET_TYPE_MUTUAL_FUND, enums.AssetType_ASSET_TYPE_SAVINGS_ACCOUNTS},
				historyDate: &timestampPb.Timestamp{Seconds: **********}, // 2022-10-25
			},
			want: map[enums.AssetType]*networthModelPb.AssetHistory{
				enums.AssetType_ASSET_TYPE_MUTUAL_FUND: { // Expecting MF record from 2022-10-15
					ActorId:     "actorId1",
					AssetType:   enums.AssetType_ASSET_TYPE_MUTUAL_FUND,
					Data:        &networthModelPb.AssetData{CurrentValue: &money.Money{Units: 100000, CurrencyCode: "INR"}},
					HistoryDate: &timestampPb.Timestamp{Seconds: **********}, // 2022-10-15
				},
				enums.AssetType_ASSET_TYPE_SAVINGS_ACCOUNTS: { // Expecting SA record from 2022-10-20
					ActorId:     "actorId1",
					AssetType:   enums.AssetType_ASSET_TYPE_SAVINGS_ACCOUNTS,
					Data:        &networthModelPb.AssetData{CurrentValue: &money.Money{Units: 2000, CurrencyCode: "INR"}},
					HistoryDate: &timestampPb.Timestamp{Seconds: **********}, // 2022-10-20
				},
			},
			wantErr: false,
		},
		{
			name: "Missing actor id should return error",
			args: args{
				ctx:         context.Background(),
				assetTypes:  []enums.AssetType{enums.AssetType_ASSET_TYPE_SAVINGS_ACCOUNTS},
				historyDate: &timestampPb.Timestamp{Seconds: **********}, // 2022-07-31
			},
			wantErr: true,
			err:     ActorIdEmptyErr,
		},
		{
			name: "Missing history date should return error",
			args: args{
				ctx:        context.Background(),
				actorId:    "actorId1",
				assetTypes: []enums.AssetType{enums.AssetType_ASSET_TYPE_SAVINGS_ACCOUNTS},
			},
			wantErr: true,
			err:     HistoryDateUnspecifiedErr,
		},
		{
			name: "Empty asset types should return error",
			args: args{
				ctx:         context.Background(),
				actorId:     "actorId1",
				assetTypes:  []enums.AssetType{},
				historyDate: &timestampPb.Timestamp{Seconds: **********}, // 2022-07-31
			},
			wantErr: true,
			err:     AssetTypeUnspecifiedErr,
		},
	}
	a := NewAssetHistoryPGDB(useCaseDbProvider)
	db, getResourceErr := useCaseDbProvider.GetResource(testOwnership, useCase)
	if getResourceErr != nil {
		t.Fatalf("failed to get db resource: %v", getResourceErr)
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, db, "insights_federal", affectedTestTables)
			got, err := a.GetMultipleHistoriesByDate(epificontext.WithOwnership(tt.args.ctx, testOwnership), tt.args.actorId, tt.args.assetTypes, tt.args.historyDate)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetMultipleHistoriesBydate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil && err != nil && !errors.Is(err, tt.err) {
				t.Errorf("GetMultipleHistoriesBydate() expected error = %v, got = %v", tt.err, err)
				return
			}
			if !tt.wantErr {
				opts := []cmp.Option{
					protocmp.Transform(),
					protocmp.IgnoreFields(&networthModelPb.AssetHistory{}, "id", "created_at", "updated_at"),
				}
				if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
					t.Errorf("GetMultipleHistoriesByDate() got = %v,\n want %v\n diff %s", got, tt.want, diff)
				}
			}
		})
	}
}

func TestAssetHistoryPGDB_CreateOrUpdateMultipleHistories(t *testing.T) {
	type args struct {
		ctx            context.Context
		assetHistories []*networthModelPb.AssetHistory
	}
	tests := []struct {
		name    string
		args    args
		want    []*networthModelPb.AssetHistory
		wantErr bool
		err     error
	}{
		{
			name: "successfully create multiple histories",
			args: args{
				ctx: context.Background(),
				assetHistories: []*networthModelPb.AssetHistory{
					{
						ActorId:   "actorId3",
						AssetType: enums.AssetType_ASSET_TYPE_MUTUAL_FUND,
						Data: &networthModelPb.AssetData{
							CurrentValue: &money.Money{Units: 150000, CurrencyCode: "INR"},
						},
						HistoryDate: &timestampPb.Timestamp{Seconds: **********},
					},
					{
						ActorId:   "actorId3",
						AssetType: enums.AssetType_ASSET_TYPE_SAVINGS_ACCOUNTS,
						Data: &networthModelPb.AssetData{
							CurrentValue: &money.Money{Units: 25000, CurrencyCode: "INR"},
						},
						HistoryDate: &timestampPb.Timestamp{Seconds: **********},
					},
				},
			},
			want: []*networthModelPb.AssetHistory{
				{
					ActorId:   "actorId3",
					AssetType: enums.AssetType_ASSET_TYPE_MUTUAL_FUND,
					Data: &networthModelPb.AssetData{
						CurrentValue: &money.Money{Units: 150000, CurrencyCode: "INR"},
					},
					HistoryDate: &timestampPb.Timestamp{Seconds: **********}, // timestamp with date only
				},
				{
					ActorId:   "actorId3",
					AssetType: enums.AssetType_ASSET_TYPE_SAVINGS_ACCOUNTS,
					Data: &networthModelPb.AssetData{
						CurrentValue: &money.Money{Units: 25000, CurrencyCode: "INR"},
					},
					HistoryDate: &timestampPb.Timestamp{Seconds: **********}, // timestamp with date only
				},
			},
			wantErr: false,
		},
		{
			name: "successfully update existing history",
			args: args{
				ctx: context.Background(),
				assetHistories: []*networthModelPb.AssetHistory{
					{
						ActorId:   "actorId1",
						AssetType: enums.AssetType_ASSET_TYPE_MUTUAL_FUND,
						Data: &networthModelPb.AssetData{
							CurrentValue: &money.Money{Units: 120000, CurrencyCode: "INR"},
						},
						HistoryDate: &timestampPb.Timestamp{Seconds: *********0},
					},
				},
			},
			want: []*networthModelPb.AssetHistory{
				{
					ActorId:   "actorId1",
					AssetType: enums.AssetType_ASSET_TYPE_MUTUAL_FUND,
					Data: &networthModelPb.AssetData{
						CurrentValue: &money.Money{Units: 120000, CurrencyCode: "INR"},
					},
					HistoryDate: &timestampPb.Timestamp{Seconds: *********0},
				},
			},
			wantErr: false,
		},
		{
			name: "Empty asset histories should return nil",
			args: args{
				ctx:            context.Background(),
				assetHistories: []*networthModelPb.AssetHistory{},
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "Missing actor id should return error",
			args: args{
				ctx: context.Background(),
				assetHistories: []*networthModelPb.AssetHistory{
					{
						AssetType: enums.AssetType_ASSET_TYPE_SAVINGS_ACCOUNTS,
						Data: &networthModelPb.AssetData{
							CurrentValue: &money.Money{Units: 25000, CurrencyCode: "INR"},
						},
						HistoryDate: &timestampPb.Timestamp{Seconds: **********},
					},
				},
			},
			wantErr: true,
		},
		{
			name: "should update history with different timestamp on same day",
			args: args{
				ctx: context.Background(),
				assetHistories: []*networthModelPb.AssetHistory{
					{
						ActorId:   "actorId1",
						AssetType: enums.AssetType_ASSET_TYPE_MUTUAL_FUND,
						Data: &networthModelPb.AssetData{
							CurrentValue: &money.Money{Units: 130000, CurrencyCode: "INR"},
						},
						HistoryDate: &timestampPb.Timestamp{Seconds: **********}, // 2022-10-29 21:00:00 UTC
					},
				},
			},
			want: []*networthModelPb.AssetHistory{
				{
					ActorId:   "actorId1",
					AssetType: enums.AssetType_ASSET_TYPE_MUTUAL_FUND,
					Data: &networthModelPb.AssetData{
						CurrentValue: &money.Money{Units: 130000, CurrencyCode: "INR"},
					},
					HistoryDate: &timestampPb.Timestamp{Seconds: **********}, // 2022-10-29 00:00:00 IST
				},
			},
			wantErr: false,
		},
	}
	a := NewAssetHistoryPGDB(useCaseDbProvider)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := a.CreateOrUpdateMultipleHistories(epificontext.WithOwnership(tt.args.ctx, testOwnership), tt.args.assetHistories)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateOrUpdateMultipleHistories() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil && err != nil && !errors.Is(err, tt.err) {
				t.Errorf("CreateOrUpdateMultipleHistories() expected error = %v, got = %v", tt.err, err)
				return
			}
			if !tt.wantErr {
				opts := []cmp.Option{
					protocmp.Transform(),
					protocmp.IgnoreFields(&networthModelPb.AssetHistory{}, "id", "created_at", "updated_at"),
				}
				if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
					t.Errorf("CreateOrUpdateMultipleHistories() got = %v,\n want %v\n diff %s", got, tt.want, diff)
				}
				if len(got) != len(tt.want) {
					t.Errorf("CreateOrUpdateMultipleHistories() got length = %v, want length = %v", len(got), len(tt.want))
				}
			}
		})
	}
}

func TestAssetHistoryPGDB_Get(t *testing.T) {
	type args struct {
		ctx context.Context
		id  string
	}
	tests := []struct {
		name    string
		args    args
		want    *networthModelPb.AssetHistory
		wantErr bool
		err     error
	}{
		{
			name: "successfully get asset history by id",
			args: args{
				ctx: context.Background(),
				id:  "e3e267f7-7e13-4e7a-af63-5a1b10cbaf0b",
			},
			want: &networthModelPb.AssetHistory{
				Id:        "e3e267f7-7e13-4e7a-af63-5a1b10cbaf0b",
				ActorId:   "actorId1",
				AssetType: enums.AssetType_ASSET_TYPE_MUTUAL_FUND,
				Data: &networthModelPb.AssetData{
					CurrentValue: &money.Money{Units: 110000, CurrencyCode: "INR"},
				},
				HistoryDate: &timestampPb.Timestamp{Seconds: **********},
			},
			wantErr: false,
		},
		{
			name: "successfully get savings account asset history by id",
			args: args{
				ctx: context.Background(),
				id:  "fbf8e134-8cd6-44e5-9834-87a6002fceb9",
			},
			want: &networthModelPb.AssetHistory{
				Id:        "fbf8e134-8cd6-44e5-9834-87a6002fceb9",
				ActorId:   "actorId1",
				AssetType: enums.AssetType_ASSET_TYPE_SAVINGS_ACCOUNTS,
				Data: &networthModelPb.AssetData{
					CurrentValue: &money.Money{Units: 2100, CurrencyCode: "INR"},
				},
				HistoryDate: &timestampPb.Timestamp{Seconds: **********}, // 2022-10-30
			},
			wantErr: false,
		},
		{
			name: "error - record not found",
			args: args{
				ctx: context.Background(),
				id:  "********-0000-0000-0000-********0000", // Non-existent UUID
			},
			want:    nil,
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
		{
			name: "error - invalid UUID format",
			args: args{
				ctx: context.Background(),
				id:  "invalid-uuid",
			},
			want:    nil,
			wantErr: true,
		},
	}
	a := NewAssetHistoryPGDB(useCaseDbProvider)
	db, getResourceErr := useCaseDbProvider.GetResource(testOwnership, useCase)
	if getResourceErr != nil {
		t.Fatalf("failed to get db resource: %v", getResourceErr)
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, db, "insights_federal", affectedTestTables)
			got, err := a.Get(epificontext.WithOwnership(tt.args.ctx, testOwnership), tt.args.id)
			if (err != nil) != tt.wantErr {
				t.Errorf("Get() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil && err != nil && !errors.Is(err, tt.err) {
				t.Errorf("Get() expected error = %v, got = %v", tt.err, err)
				return
			}
			if !tt.wantErr {
				opts := []cmp.Option{
					protocmp.Transform(),
					protocmp.IgnoreFields(&networthModelPb.AssetHistory{}, "created_at", "updated_at"),
				}
				if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
					t.Errorf("Get() got = %v,\n want %v\n diff %s", got, tt.want, diff)
				}
			}
		})
	}
}

func TestAssetHistoryPGDB_DeleteAssetHistoriesByActorId(t *testing.T) {
	a := NewAssetHistoryPGDB(useCaseDbProvider)
	db, getResourceErr := useCaseDbProvider.GetResource(testOwnership, useCase)
	if getResourceErr != nil {
		t.Fatalf("failed to get db resource: %v", getResourceErr)
	}
	_, createErr := a.CreateOrUpdateMultipleHistories(context.Background(), fixtureData)
	if createErr != nil {
		t.Errorf("Failed to insert fixture data: %v", createErr)
	}
	type args struct {
		ctx        context.Context
		actorId    string
		assetTypes []enums.AssetType
	}
	tests := []struct {
		name    string
		args    args
		wantErr error
	}{
		{
			name: "Empty actor id should return error",
			args: args{
				ctx:        context.Background(),
				actorId:    "",
				assetTypes: []enums.AssetType{enums.AssetType_ASSET_TYPE_MUTUAL_FUND},
			},
			wantErr: fmt.Errorf("actor id cannot be empty"),
		},
		{
			name: "Empty asset types should return error",
			args: args{
				ctx:        context.Background(),
				actorId:    "actor2",
				assetTypes: nil,
			},
			wantErr: fmt.Errorf("asset types cannot be empty"),
		},
		{
			name: "Successfully soft delete only a specified asset type",
			args: args{
				ctx:        context.Background(),
				actorId:    "actor1",
				assetTypes: []enums.AssetType{enums.AssetType_ASSET_TYPE_MUTUAL_FUND},
			},
			wantErr: nil,
		},
		{
			name: "Successfully soft delete all specified asset types",
			args: args{
				ctx:        context.Background(),
				actorId:    "actor2",
				assetTypes: []enums.AssetType{enums.AssetType_ASSET_TYPE_MUTUAL_FUND, enums.AssetType_ASSET_TYPE_INDIAN_SECURITIES},
			},
			wantErr: nil,
		},
		{
			name: "Should return ErrRecordNotFound when no records exist for actor",
			args: args{
				ctx:        context.Background(),
				actorId:    "actor2",
				assetTypes: []enums.AssetType{enums.AssetType_ASSET_TYPE_MUTUAL_FUND},
			},
			wantErr: epifierrors.ErrRecordNotFound,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, db, "insights_federal", affectedTestTables)
			// Execute deletion
			err := a.DeleteAssetHistoriesByActorId(context.Background(), tt.args.actorId, tt.args.assetTypes)
			// Check error
			if (err != nil) != (tt.wantErr != nil) {
				t.Errorf("DeleteAssetHistoriesByActorId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			// Check specific error type
			if tt.wantErr != nil && err != nil {
				// For fmt.Errorf errors, compare the error message
				if tt.wantErr.Error() != err.Error() {
					t.Errorf("DeleteAssetHistoriesByActorId() expected error = %v, got = %v", tt.wantErr, err)
					return
				}
			}
			// For successful deletion, verify the records are soft deleted
			if tt.wantErr == nil {
				if tt.args.actorId == "actor1" {
					// Verify only Mutual Fund records are soft deleted for actor1
					historyDate := timestampPb.New(time.Date(2025, 6, 2, 0, 0, 0, 0, datetime.IST))
					records, err := a.GetMultipleHistoriesByDate(context.Background(), tt.args.actorId, []enums.AssetType{enums.AssetType_ASSET_TYPE_INDIAN_SECURITIES, enums.AssetType_ASSET_TYPE_MUTUAL_FUND}, historyDate)
					if err != nil {
						t.Errorf("Failed to get records after deletion: %v", err)
					}
					_, stocksExists := records[enums.AssetType_ASSET_TYPE_INDIAN_SECURITIES]
					if !stocksExists {
						t.Errorf("Failed to get indian stocks records after deletion")
					}
					_, mfExists := records[enums.AssetType_ASSET_TYPE_MUTUAL_FUND]
					if mfExists {
						t.Errorf("MF Data should be soft deleted for actor %s, but found records", tt.args.actorId)
					}
				} else {
					historyDate := timestampPb.New(time.Date(2025, 6, 2, 0, 0, 0, 0, datetime.IST))
					_, err := a.GetMultipleHistoriesByDate(context.Background(), tt.args.actorId, tt.args.assetTypes, historyDate)
					if err == nil {
						t.Errorf("Expected records to be soft deleted for actor %s and asset types %v, but found records", tt.args.actorId, tt.args.assetTypes)
					} else if !errors.Is(err, epifierrors.ErrRecordNotFound) {
						t.Errorf("Expected ErrRecordNotFound for soft deleted records, but got: %v", err)
					}
				}
			}
		})
	}
}
