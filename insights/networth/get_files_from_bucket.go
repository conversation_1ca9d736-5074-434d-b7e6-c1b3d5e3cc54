package networth

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/aws/v2/s3"

	"github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/insights/networth"
)

func (s *Service) GetFilesFromBucket(ctx context.Context, req *networth.GetFilesFromBucketRequest) (*networth.GetFilesFromBucketResponse, error) {
	if len(req.GetFilePaths()) == 0 || req.GetBucketName() == "" {
		return &networth.GetFilesFromBucketResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("empty input s3 paths or file path"),
		}, nil
	}
	bucketNameToS3Client := map[string]s3.S3Client{
		s.config.MagicImportConfig().S3BucketName(): s.networthS3Client,
	}
	s3Client, ok := bucketNameToS3Client[req.GetBucketName()]
	if !ok {
		logger.Info(ctx, "unhandled bucket name", zap.String(logger.BUCKET_NAME, req.GetBucketName()))
		return &networth.GetFilesFromBucketResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg(fmt.Sprintf("unhandled bucket name: %v", req.GetBucketName())),
		}, nil
	}

	var signedUrls []string
	for _, path := range req.GetFilePaths() {
		signedUrl, err := s3Client.GetPreSignedUrl(ctx, path, 30*time.Minute)
		if err != nil {
			logger.Error(ctx, "error in getting signed url", zap.Error(err))
			continue
		}
		signedUrls = append(signedUrls, signedUrl)
	}

	return &networth.GetFilesFromBucketResponse{
		Status:     rpc.StatusOk(),
		SignedUrls: signedUrls,
	}, nil
}
