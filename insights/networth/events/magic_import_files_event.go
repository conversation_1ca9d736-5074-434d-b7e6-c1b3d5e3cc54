package events

import (
	"time"

	"github.com/fatih/structs"
	"github.com/google/uuid"

	"github.com/epifi/be-common/pkg/events"
)

const EventMagicImportFilesResponse = "MagicImportFilesResponse"

type MagicImportFilesEvent struct {
	EventId          string
	ActorID          string
	Status           string
	Response         string
	Error            string
	Timestamp        time.Time
	ProspectId       string
	FileNameToS3Path map[string]interface{} `structs:",flatten"`
}

func NewMagicImportFilesEvent(actorId, response string, fileNameToS3Path map[string]interface{}) *MagicImportFilesEvent {
	return &MagicImportFilesEvent{
		Timestamp:        time.Now(),
		ProspectId:       uuid.New().String(),
		EventId:          uuid.New().String(),
		Response:         response,
		ActorID:          actorId,
		FileNameToS3Path: fileNameToS3Path,
	}
}

func (s *MagicImportFilesEvent) GetEventType() string {
	return events.EventTrack
}

func (s *MagicImportFilesEvent) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(s, properties)
	return properties
}

func (s *MagicImportFilesEvent) GetEventTraits() map[string]interface{} {
	return nil
}

func (s *MagicImportFilesEvent) GetEventId() string {
	return s.EventId
}

func (s *MagicImportFilesEvent) GetUserId() string {
	return s.ActorID
}

func (s *MagicImportFilesEvent) GetProspectId() string {
	return s.ProspectId
}

func (s *MagicImportFilesEvent) GetEventName() string {
	return EventMagicImportFilesResponse
}
