package calculator

import (
	"context"
	"fmt"

	"github.com/shopspring/decimal"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/money"

	networthPb "github.com/epifi/gamma/api/insights/networth"
	networthModelPb "github.com/epifi/gamma/api/insights/networth/model"
	typesPb "github.com/epifi/gamma/api/typesv2"
)

type RecurringDepositCalculator struct {
	datetime datetime.Time
}

func NewRecurringDepositCalculator(datetime datetime.Time) *RecurringDepositCalculator {
	return &RecurringDepositCalculator{datetime: datetime}
}

// GetCurrentValue calculates current value of a recurring deposit
func (f *RecurringDepositCalculator) GetCurrentValue(ctx context.Context, declaration *networthModelPb.InvestmentDeclaration) (*moneyPb.Money, error) {
	if declaration.GetInstrumentType() != typesPb.InvestmentInstrumentType_RECURRING_DEPOSIT {
		return nil, fmt.Errorf("invalid instrument type %v", declaration.GetInstrumentType())
	}

	investmentDate := declaration.GetInvestedAt().AsTime().In(datetime.IST)
	maturityDate := declaration.GetMaturityDate().AsTime().In(datetime.IST)
	// if investment date is after maturity date, consider maturity date as investment date
	if investmentDate.After(maturityDate) {
		maturityDate = investmentDate
	}

	principal := money.ToDecimal(declaration.GetInvestedAmount())
	currentValue := decimal.NewFromInt(0)
	interest := decimal.NewFromFloat(declaration.GetInterestRate())
	investmentDatePtr := &investmentDate
	now := f.datetime.Now()

	// calculate compound interest for each rd installment separately
	for !investmentDatePtr.After(now) && !investmentDatePtr.After(maturityDate) {
		endTime := now
		if endTime.After(maturityDate) {
			endTime = maturityDate
		}

		depositValue, err := NetCompoundedValue(principal, interest, *investmentDatePtr, endTime)
		if err != nil {
			return nil, fmt.Errorf("failed to calculate current value for rd: %w", err)
		}

		currentValue = currentValue.Add(depositValue)
		investmentDatePtr = datetime.AddNMonths(investmentDatePtr, 1)
	}

	if declaration.GetDeclarationDetails() == nil {
		declaration.DeclarationDetails = &networthModelPb.OtherDeclarationDetails{}
	}
	if declaration.GetDeclarationDetails().Details == nil {
		declaration.DeclarationDetails.Details = &networthModelPb.OtherDeclarationDetails_RecurringDepositDeclarationDetails{
			RecurringDepositDeclarationDetails: &networthModelPb.RecurringDepositDeclarationDetails{},
		}
	}

	declaration.GetDeclarationDetails().GetRecurringDepositDeclarationDetails().CurrentValue = money.ParseDecimal(currentValue, declaration.GetInvestedAmount().GetCurrencyCode())
	return declaration.GetDeclarationDetails().GetRecurringDepositDeclarationDetails().GetCurrentValue(), nil
}

func (f *RecurringDepositCalculator) GetComputedInvestmentDetails(ctx context.Context, declaration *networthModelPb.InvestmentDeclaration) (*networthPb.ComputedInvestmentDetails, error) {
	return nil, nil
}
