package magicimport

import (
	"encoding/json"
	"time"

	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/gamma/api/insights/networth/model"
	"github.com/epifi/gamma/api/typesv2"
)

func GetMagicImportContext() string {
	prompt := magicImportSystemContext
	samplePrompts := getSamplePromptResponse()

	res, err := json.Marshal(samplePrompts)
	if err != nil {
		// ideally this must never fail, preferring method simplicity over error handling
		// This logic will be verified in non-prod via UT execution in CI
		panic(err)
	}
	prompt += string(res) + "\n"

	return prompt
}

func getSamplePromptResponse() *MagicImportPromptResponse {
	return &MagicImportPromptResponse{
		AICommentary:        "This is a sample AI commentary for all the assets",
		OneWordAICommentary: "Priceless",
		ConventionalAssets: []*ConventionalAssets{
			{
				FileName: "File name from which asset was extracted",
				InvestmentDeclaration: &model.InvestmentDeclaration{
					InstrumentType: typesv2.InvestmentInstrumentType_CASH,
					DeclarationDetails: &model.OtherDeclarationDetails{
						Details: &model.OtherDeclarationDetails_Cash{
							Cash: &model.Cash{
								Name: "Cash for emergency",
								CurrentAmount: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								LastViewedOn: &date.Date{
									Year:  2025,
									Month: 1,
									Day:   1,
								},
							},
						},
					},
				},
			},
			{
				FileName: "File name from which asset was extracted",
				InvestmentDeclaration: &model.InvestmentDeclaration{
					InstrumentType: typesv2.InvestmentInstrumentType_BOND,
					DeclarationDetails: &model.OtherDeclarationDetails{
						Details: &model.OtherDeclarationDetails_Bond{
							Bond: &model.Bond{
								InvestmentName: "Government Bond",
								InvestedValuePerUnit: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								NumberOfUnits: 100,
								CurrentValuePerUnit: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								InvestmentDate: &date.Date{
									Year:  2025,
									Month: 1,
									Day:   1,
								},
								MaturityDate: &date.Date{
									Year:  2030,
									Month: 1,
									Day:   1,
								},
							},
						},
					},
				},
			},
			{
				FileName: "File name from which asset was extracted",
				InvestmentDeclaration: &model.InvestmentDeclaration{
					InstrumentType: typesv2.InvestmentInstrumentType_DIGITAL_GOLD,
					DeclarationDetails: &model.OtherDeclarationDetails{
						Details: &model.OtherDeclarationDetails_DigitalGold{
							DigitalGold: &model.DigitalGold{
								InvestmentName: "Dowry gold",
								InvestedValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								QuantityInGrams: 100,
								InvestmentDate: &date.Date{
									Year:  2025,
									Month: 1,
									Day:   1,
								},
								GoldCaratValue: model.GoldCarat_GOLD_CARAT_24,
							},
						},
					},
				},
			},
			{
				FileName: "File name from which asset was extracted",
				InvestmentDeclaration: &model.InvestmentDeclaration{
					InstrumentType: typesv2.InvestmentInstrumentType_DIGITAL_SILVER,
					DeclarationDetails: &model.OtherDeclarationDetails{
						Details: &model.OtherDeclarationDetails_DigitalSilver{
							DigitalSilver: &model.DigitalSilver{
								InvestmentName: "Dowry silver",
								InvestedValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								QuantityInGrams: 100,
								InvestmentDate: &date.Date{
									Year:  2025,
									Month: 1,
									Day:   1,
								},
							},
						},
					},
				},
			},
			{
				FileName: "File name from which asset was extracted",
				InvestmentDeclaration: &model.InvestmentDeclaration{
					InstrumentType: typesv2.InvestmentInstrumentType_AIF,
					DeclarationDetails: &model.OtherDeclarationDetails{
						Details: &model.OtherDeclarationDetails_Aif{
							Aif: &model.AIF{
								Category:   model.AIF_CATEGORY_2,
								BrokerCode: "AIF123",
								InvestedValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								CurrentValue: &money.Money{
									CurrencyCode: "INR",
									Units:        200000,
								},

								InvestmentDate: &date.Date{
									Year:  2025,
									Month: 1,
									Day:   1,
								},
								EvaluationDate: &date.Date{
									Year:  2026,
									Month: 1,
									Day:   1,
								},
								Identifier: &model.AIF_AifNameV2{
									AifNameV2: "AIF Name",
								},
							},
						},
					},
				},
			},
			{
				FileName: "File name from which asset was extracted",
				InvestmentDeclaration: &model.InvestmentDeclaration{
					InstrumentType: typesv2.InvestmentInstrumentType_PRIVATE_EQUITY,
					DeclarationDetails: &model.OtherDeclarationDetails{
						Details: &model.OtherDeclarationDetails_PrivateEquity{
							PrivateEquity: &model.PrivateEquity{
								InvestmentName: "Private Equity Fund",
								InvestedValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								CurrentValue: &money.Money{
									CurrencyCode: "INR",
									Units:        200000,
								},

								InvestmentDate: &date.Date{
									Year:  2025,
									Month: 1,
									Day:   1,
								},
							},
						},
					},
				},
			},
			{
				FileName: "File name from which asset was extracted",
				InvestmentDeclaration: &model.InvestmentDeclaration{
					InstrumentType: typesv2.InvestmentInstrumentType_PORTFOLIO_MANAGEMENT_SERVICE,
					DeclarationDetails: &model.OtherDeclarationDetails{
						Details: &model.OtherDeclarationDetails_PortfolioManagementService{
							PortfolioManagementService: &model.PortfolioManagementService{
								PmsName: "Wealth Management Service",
								InvestmentDate: &date.Date{
									Year:  2025,
									Month: 1,
									Day:   1,
								},
								EvaluationDate: &date.Date{
									Year:  2026,
									Month: 1,
									Day:   1,
								},
								InvestedValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								CurrentValue: &money.Money{
									CurrencyCode: "INR",
									Units:        200000,
								},
								FolioId:    "Folio123",
								BrokerCode: "Broker123",
								Identifier: &model.PortfolioManagementService_AmcNameV2{
									AmcNameV2: "AMC Name",
								},
							},
						},
					},
				},
			},
			{
				FileName: "File name from which asset was extracted",
				InvestmentDeclaration: &model.InvestmentDeclaration{
					InstrumentType: typesv2.InvestmentInstrumentType_EMPLOYEE_STOCK_OPTION,
					DeclarationDetails: &model.OtherDeclarationDetails{
						Details: &model.OtherDeclarationDetails_EmployeeStockOption{
							EmployeeStockOption: &model.EmployeeStockOption{
								CurrencyType: model.CurrencyType_CURRENCY_TYPE_USD,
								CompanyName:  "Oracle",
								EsopInGrant:  100,
								FirstIssueDate: &date.Date{
									Year:  2025,
									Month: 1,
									Day:   1,
								},
								VestingSchedule: &model.VestingSchedule{
									YearlyVestedStock: []float64{
										25, 25, 25, 25,
									},
								},
								CurrentValuePerShare: &money.Money{
									CurrencyCode: "USD",
									Units:        100,
								},
							},
						},
					},
				},
			},
			{
				FileName: "File name from which asset was extracted",
				InvestmentDeclaration: &model.InvestmentDeclaration{
					InstrumentType: typesv2.InvestmentInstrumentType_ART_AND_ARTEFACTS,
					DeclarationDetails: &model.OtherDeclarationDetails{
						Details: &model.OtherDeclarationDetails_ArtAndArtefacts{
							ArtAndArtefacts: &model.ArtAndArtefacts{
								InvestmentName: "Laptop",
								InvestedValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								CurrentValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1000,
								},
							},
						},
					},
				},
			},
			{
				FileName: "File name from which asset was extracted",
				InvestmentDeclaration: &model.InvestmentDeclaration{
					InstrumentType: typesv2.InvestmentInstrumentType_NPS,
					DeclarationDetails: &model.OtherDeclarationDetails{
						Details: &model.OtherDeclarationDetails_NpsDetails{
							NpsDetails: &model.NPSDetails{
								SchemeName: "BSE",
								InvestedValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1000,
								},
								CurrentValue: &money.Money{
									CurrencyCode: "INR",
									Units:        2000,
								},
								InvestmentDate: &date.Date{
									Year:  2022,
									Month: 1,
									Day:   1,
								},
							},
						},
					},
				},
			},
			{
				FileName: "File name from which asset was extracted",
				InvestmentDeclaration: &model.InvestmentDeclaration{
					InstrumentType: typesv2.InvestmentInstrumentType_GADGETS,
					DeclarationDetails: &model.OtherDeclarationDetails{
						Details: &model.OtherDeclarationDetails_Gadgets{
							Gadgets: &model.Gadgets{
								InvestmentName: "Macbook Pro",
								DeviceType:     "laptop",
								CurrentValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1000,
								},
								Condition: "Good",
								YearOfPurchase: &date.Date{
									Year:  2000,
									Month: 1,
									Day:   1,
								},
							},
						},
					},
				},
			},
			{
				FileName: "File name from which asset was extracted",
				InvestmentDeclaration: &model.InvestmentDeclaration{
					InstrumentType: typesv2.InvestmentInstrumentType_VEHICLES,
					DeclarationDetails: &model.OtherDeclarationDetails{
						Details: &model.OtherDeclarationDetails_Vehicle{
							Vehicle: &model.Vehicle{
								VehicleType:    "car",
								InvestmentName: "Honda City",
								YearOfPurchase: &date.Date{
									Year:  2000,
									Month: 1,
									Day:   1,
								},
								Condition:        "Good",
								DepreciationRate: 10,
								CurrentValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1000000,
								},
							},
						},
					},
				},
			},
			{
				FileName: "File name from which asset was extracted",
				InvestmentDeclaration: &model.InvestmentDeclaration{
					InstrumentType: typesv2.InvestmentInstrumentType_CRYPTO,
					DeclarationDetails: &model.OtherDeclarationDetails{
						Details: &model.OtherDeclarationDetails_Crypto{
							Crypto: &model.Crypto{
								InvestmentName: "Bitcoin",
								NumberOfUnits:  0.1,
								CurrentValuePerUnit: &money.Money{
									CurrencyCode: "INR",
									Units:        5000000,
								},
								CurrentValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1000000,
								},
							},
						},
					},
				},
			},
			{
				FileName: "File name from which asset was extracted",
				InvestmentDeclaration: &model.InvestmentDeclaration{
					InstrumentType: typesv2.InvestmentInstrumentType_FURNITURE,
					DeclarationDetails: &model.OtherDeclarationDetails{
						Details: &model.OtherDeclarationDetails_Furniture{
							Furniture: &model.Furniture{
								InvestmentName: "Table chair",
								FurnitureType:  "table",
								YearOfPurchase: &date.Date{
									Year:  2000,
									Month: 1,
									Day:   1,
								},
								Condition: "Good",
								CurrentValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1000000,
								},
							},
						},
					},
				},
			},
			{
				FileName: "File name from which asset was extracted",
				InvestmentDeclaration: &model.InvestmentDeclaration{
					InstrumentType: typesv2.InvestmentInstrumentType_COLLECTIBLES,
					DeclarationDetails: &model.OtherDeclarationDetails{
						Details: &model.OtherDeclarationDetails_Collectible{
							Collectible: &model.Collectible{
								InvestmentName: "Comic book",
								ItemBrand:      "Into the Spider-Verse",
								ItemType:       "comic book",
								ItemModel:      "comic book",
								Condition:      "Good",
								CurrentValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1000000,
								},
							},
						},
					},
				},
			},
			{
				FileName: "File name from which asset was extracted",
				InvestmentDeclaration: &model.InvestmentDeclaration{
					InstrumentType: typesv2.InvestmentInstrumentType_JEWELLERY,
					DeclarationDetails: &model.OtherDeclarationDetails{
						Details: &model.OtherDeclarationDetails_Jewellery{
							Jewellery: &model.Jewellery{
								InvestmentName: "Gold Necklace",
								BaseMetal:      "Gold",
								Purity:         "24K",
								WeightInGrams:  100,
								CurrentValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
							},
						},
					},
				},
			},
			{
				FileName: "File name from which asset was extracted",
				InvestmentDeclaration: &model.InvestmentDeclaration{
					InstrumentType: typesv2.InvestmentInstrumentType_PUBLIC_PROVIDENT_FUND,
					DeclarationDetails: &model.OtherDeclarationDetails{
						Details: &model.OtherDeclarationDetails_PublicProvidentFund{
							PublicProvidentFund: &model.PublicProvidentFund{
								CurrentValue: &money.Money{
									CurrencyCode: "INR",
									Units:        120000,
								},
								InvestmentName: "HDFC Bank PPF",
								InvestmentDate: &date.Date{
									Year:  2022,
									Month: 1,
									Day:   1,
								},
							},
						},
					},
				},
			},
			{
				FileName: "File name from which asset was extracted",
				InvestmentDeclaration: &model.InvestmentDeclaration{
					InstrumentType: typesv2.InvestmentInstrumentType_FIXED_DEPOSIT,
					InterestRate:   10.5,
					InvestedAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        120000,
					},
					InvestedAt:   timestampPb.Now(),
					MaturityDate: timestampPb.New(time.Now().AddDate(1, 0, 0)),
					DeclarationDetails: &model.OtherDeclarationDetails{
						Details: &model.OtherDeclarationDetails_FixedDepositDeclarationDetails{
							FixedDepositDeclarationDetails: &model.FixedDepositDeclarationDetails{
								DepositName: "HDFC Bank FD",
							},
						},
					},
				},
			},
			{
				FileName: "File name from which asset was extracted",
				InvestmentDeclaration: &model.InvestmentDeclaration{
					InstrumentType: typesv2.InvestmentInstrumentType_RECURRING_DEPOSIT,
					InterestRate:   10.5,
					InvestedAmount: &money.Money{
						CurrencyCode: "INR",
						Units:        120000,
					},
					InvestedAt:   timestampPb.Now(),
					MaturityDate: timestampPb.New(time.Now().AddDate(1, 0, 0)),
					DeclarationDetails: &model.OtherDeclarationDetails{
						Details: &model.OtherDeclarationDetails_RecurringDepositDeclarationDetails{
							RecurringDepositDeclarationDetails: &model.RecurringDepositDeclarationDetails{
								DepositName: "HDFC Bank RD",
							},
						},
					},
				},
			},
			{
				FileName: "File name from which asset was extracted",
				InvestmentDeclaration: &model.InvestmentDeclaration{
					InstrumentType: typesv2.InvestmentInstrumentType_INVESTMENT_INSTRUMENT_TYPE_OTHERS,
					DeclarationDetails: &model.OtherDeclarationDetails{
						Details: &model.OtherDeclarationDetails_Others{
							Others: &model.Others{
								InvestmentName: "New unconventional asset",
								InvestmentType: "furniture",
								CurrentValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1000,
								},
								DateOfPurchase: &date.Date{
									Year:  2000,
									Month: 1,
									Day:   1,
								},
							},
						},
					},
				},
			},
			{
				FileName: "File name from which asset was extracted",
				InvestmentDeclaration: &model.InvestmentDeclaration{
					InstrumentType: typesv2.InvestmentInstrumentType_MUTUAL_FUNDS,
					DeclarationDetails: &model.OtherDeclarationDetails{
						Details: &model.OtherDeclarationDetails_MutualFundDetails{
							MutualFundDetails: &model.MutualFundDetails{
								MutualFundName: "Nifty 50",
								NumberOfUnits:  10,
								InvestedValuePerUnit: &money.Money{
									CurrencyCode: "INR",
									Units:        1000,
								},
								CurrentValuePerUnit: &money.Money{
									CurrencyCode: "INR",
									Units:        2000,
								},
								InvestmentDate: &date.Date{
									Year:  2022,
									Month: 1,
									Day:   1,
								},
							},
						},
					},
				},
			},
			{
				FileName: "File name from which asset was extracted",
				InvestmentDeclaration: &model.InvestmentDeclaration{
					InstrumentType: typesv2.InvestmentInstrumentType_INDIAN_STOCKS,
					DeclarationDetails: &model.OtherDeclarationDetails{
						Details: &model.OtherDeclarationDetails_IndianStockDetails{
							IndianStockDetails: &model.IndianStockDetails{
								StockName:     "BSE",
								NumberOfUnits: 10,
								InvestedValuePerUnit: &money.Money{
									CurrencyCode: "INR",
									Units:        1000,
								},
								CurrentValuePerUnit: &money.Money{
									CurrencyCode: "INR",
									Units:        2000,
								},
								InvestmentDate: &date.Date{
									Year:  2022,
									Month: 1,
									Day:   1,
								},
							},
						},
					},
				},
			},
		},
		UnconventionalAssets: []*UnconventionalAssets{
			{
				Name:         "Kookaburra bat",
				AssetType:    "Sports Equipment",
				AICommentary: "This cricket bat is a valuable item, possibly signed by a famous player.",
			},
		},
	}
}
