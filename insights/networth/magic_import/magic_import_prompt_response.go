package magicimport

import "github.com/epifi/gamma/api/insights/networth/model"

type MagicImportPromptResponse struct {
	RequestId            string
	OneWordAICommentary  string                  `json:"one_word_ai_commentary"`
	AICommentary         string                  `json:"ai_commentary"`
	UnconventionalAssets []*UnconventionalAssets `json:"unconventional_assets"`
	ConventionalAssets   []*ConventionalAssets   `json:"conventional_assets"`
}

type ConventionalAssets struct {
	FileName              string                       `json:"file_name"`
	InvestmentDeclaration *model.InvestmentDeclaration `json:"investment_declaration"`
}

type UnconventionalAssets struct {
	Name         string `json:"name"`
	AssetType    string `json:"asset_type"`
	AICommentary string `json:"ai_commentary"`
}

func (r *MagicImportPromptResponse) GetAICommentary() string {
	if r != nil {
		return r.AICommentary
	}
	return ""
}

func (r *MagicImportPromptResponse) GetOneWordAICommentary() string {
	if r != nil {
		return r.OneWordAICommentary
	}
	return ""
}

func (r *MagicImportPromptResponse) GetUnconventionalAssets() []*UnconventionalAssets {
	if r != nil {
		return r.UnconventionalAssets
	}
	return nil
}

func (r *MagicImportPromptResponse) GetConventionalAssets() []*ConventionalAssets {
	if r != nil {
		return r.ConventionalAssets
	}
	return nil
}

func (r *ConventionalAssets) GetFileName() string {
	if r != nil {
		return r.FileName
	}
	return ""
}

func (r *ConventionalAssets) GetInvestmentDeclaration() *model.InvestmentDeclaration {
	if r != nil {
		return r.InvestmentDeclaration
	}
	return nil
}

func (r *UnconventionalAssets) GetName() string {
	if r != nil {
		return r.Name
	}
	return ""
}

func (r *UnconventionalAssets) GetAssetType() string {
	if r != nil {
		return r.AssetType
	}
	return ""
}

func (r *UnconventionalAssets) GetAICommentary() string {
	if r != nil {
		return r.AICommentary
	}
	return ""
}
