package dao

import (
	"context"
	"fmt"
	"time"

	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	"github.com/epifi/gamma/insights/emailparser/dao/model"
)

const (
	userMailDataInsertQuery = "INSERT INTO user_mail_details(message_processing_id, mail_detail, data_encryption_key, key_secret_id, updated_at) VALUES (?, ?, ?, ?, ?) ON CONFLICT (message_processing_id) DO NOTHING;"
)

// StoreMessages writes mail data to DB
func (s *PGMessageDao) StoreMailData(ctx context.Context, messageProcessingId string, encrData string, dataEncrKey, keySecretsId string) error {
	defer metric_util.TrackDuration("insights/emailparser/dao", "PGMessageDao", "StoreMailData", time.Now())
	mailData, err := createUserMailDataModel(messageProcessingId, encrData, dataEncrKey, keySecretsId)
	if err != nil {
		return fmt.Errorf("error creating UserMailData model : %w", err)
	}

	db := gormctxv2.FromContextOrDefault(ctx, s.DB)
	if err := db.Exec(userMailDataInsertQuery, mailData.MessageProcessingId, mailData.MailDetail, mailData.DataEncryptionKey,
		mailData.KeySecretId, "now()").Error; err != nil {
		return fmt.Errorf("create UserMailDetail error : %w", err)
	}

	return nil
}

func (s *PGMessageDao) GetMailWithMessageId(ctx context.Context, messageProcessingId string) (*model.UserMailDetail, error) {
	defer metric_util.TrackDuration("insights/emailparser/dao", "PGMessageDao", "GetMailWithMessageId", time.Now())
	if messageProcessingId == "" {
		return nil, fmt.Errorf("messageProcessingId not specified")
	}
	db := gormctxv2.FromContextOrDefault(ctx, s.DB)
	var entry model.UserMailDetail
	db = db.Where(&model.UserMailDetail{MessageProcessingId: messageProcessingId}).Take(&entry)
	if db.Error != nil {
		return nil, fmt.Errorf("error fetching mail data from db : %w", db.Error)
	}
	if db.RowsAffected == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	return &entry, nil
}

func (s *PGMessageDao) DeleteMailDataForMessageId(ctx context.Context, messageId string) error {
	defer metric_util.TrackDuration("insights/emailparser/dao", "PGMessageDao", "DeleteMailDataForMessageId", time.Now())
	if messageId == "" {
		return fmt.Errorf("messageId not specified")
	}
	db := gormctxv2.FromContextOrDefault(ctx, s.DB)
	if err := db.Delete(model.UserMailDetail{}, "message_processing_id = ?", messageId).Error; err != nil {
		return fmt.Errorf("delete UserMailDetail error : %w", err)
	}
	return nil
}

func createUserMailDataModel(messageProcessingId string, data string, dataEncrKey, keySecretsId string) (*model.UserMailDetail, error) {
	if messageProcessingId == "" {
		return nil, fmt.Errorf("messageProcessingId not specified")
	}
	if data == "" {
		return nil, fmt.Errorf("mail data is not present")
	}
	if keySecretsId == "" {
		return nil, fmt.Errorf("keySecretsId not specified")
	}
	if dataEncrKey == "" {
		return nil, fmt.Errorf("dataEncrKey not specified")
	}
	return &model.UserMailDetail{
		MessageProcessingId: messageProcessingId,
		DataEncryptionKey:   dataEncrKey,
		MailDetail:          data,
		KeySecretId:         keySecretsId,
	}, nil
}
