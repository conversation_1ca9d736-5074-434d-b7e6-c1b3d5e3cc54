//nolint:gocritic
package consumer

import (
	"context"
	"errors"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/logger"
	caEnumsPb "github.com/epifi/gamma/api/connected_account/enums"
	caxPb "github.com/epifi/gamma/api/connected_account/external"
	"github.com/epifi/gamma/api/insights/consumer"
	enumsPb "github.com/epifi/gamma/api/insights/networth/enums"
	aaHelper "github.com/epifi/gamma/insights/networth/assets/helper/aa"
)

func (s *Service) DeleteCaAssetHistoriesEvent(ctx context.Context, event *caxPb.AccountUpdateEvent) (*consumer.DeleteCaAssetHistoriesEventResponse, error) {
	actorId := event.GetActorId()
	ctx = epificontext.CtxWithActorId(ctx, actorId)
	logger.Debug(ctx, "Collected DeleteCaAssetHistoriesEvent", zap.String(logger.ACC_INSTRUMENT_TYPE, event.GetAccInstrumentType().String()), zap.String(logger.ACCOUNT_STATE, event.GetAccountStatus().String()))
	// first level filtering since we only have asset histories for indian securities data for now
	// event.GetAccInstrumentType() != caEnumsPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_ETF &&
	// event.GetAccInstrumentType() != caEnumsPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_REIT &&
	// event.GetAccInstrumentType() != caEnumsPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_NPS &&
	// event.GetAccInstrumentType() != caEnumsPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_INVIT
	if event.GetAccInstrumentType() != caEnumsPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_EQUITIES {
		return &consumer.DeleteCaAssetHistoriesEventResponse{
			ResponseHeader: success,
		}, nil
	}

	// filtering out account statuses
	// we will only delete asset histories if account is disconnected or deleted
	if event.GetAccountStatus() != caEnumsPb.AccountStatus_ACCOUNT_STATUS_DELETED && event.GetAccountStatus() != caEnumsPb.AccountStatus_ACCOUNT_STATUS_DISCONNECTED {
		return &consumer.DeleteCaAssetHistoriesEventResponse{
			ResponseHeader: success,
		}, nil
	}

	// get asset type from instrument type
	assetType := aaHelper.GetAssetTypeForInstrumentType(event.GetAccInstrumentType())
	if assetType == enumsPb.AssetType_ASSET_TYPE_UNSPECIFIED {
		logger.Error(ctx, "mapping not found for account instrument type", zap.String(logger.ACC_INSTRUMENT_TYPE, event.GetAccInstrumentType().String()))
		return &consumer.DeleteCaAssetHistoriesEventResponse{
			ResponseHeader: permanentFailure,
		}, nil
	}

	ctx = epificontext.WithOwnership(ctx, commontypes.Ownership_EPIFI_WEALTH)
	err := s.assetHistoryDao.DeleteAssetHistoriesByActorId(ctx, actorId, []enumsPb.AssetType{assetType})

	// RNF is expected when there are no asset histories for the actor or all asset histories are already deleted
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error deleting asset history", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return &consumer.DeleteCaAssetHistoriesEventResponse{
			ResponseHeader: permanentFailure,
		}, nil
	}

	return &consumer.DeleteCaAssetHistoriesEventResponse{
		ResponseHeader: success,
	}, nil
}
