package investment

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"fmt"
	"math"
	"strconv"
	"time"

	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"

	mfPb "github.com/epifi/gamma/api/investment/mutualfund"
	orderPb "github.com/epifi/gamma/api/investment/mutualfund/order"
	paymentPb "github.com/epifi/gamma/api/investment/mutualfund/payment_handler"
)

// SEBIHolidays Ref: https://www.5paisa.com/stock-market-holidays-2023
// Alternative Ref used for 2024: https://www.livemint.com/market/stock-market-news/stock-market-holiday-2024-check-days-dates-and-trading-holidays-in-the-new-year-11702434332776.html
// 5paisa calendar for 2024 seems to have discrepancy with latest news articles. Hence, not used.
// SEBIHolidays list for 2025 ref: https://www.calendarlabs.com/bse-market-holidays-2025/
var SEBIHolidays = []string{
	// 2022
	"March 01 2022",
	"March 18 2022",
	"April 14 2022",
	"April 15 2022",
	"May 03 2022",
	"August 09 2022",
	"August 15 2022",
	"August 31 2022",
	"October 05 2022",
	"October 24 2022",
	"October 25 2022",
	"November 08 2022",

	// 2023
	"January 26 2023",
	"March 08 2023",
	"March 30 2023",
	"April 04 2023",
	"April 07 2023",
	"April 14 2023",
	"May 01 2023",
	"June 28 2023",
	"August 15 2023",
	"September 19 2023",
	"October 02 2023",
	"October 24 2023",
	"November 14 2023",
	"November 27 2023",
	"December 25 2023",

	// 2024
	"January 26 2024",   // Republic Day
	"March 08 2024",     // Maha Shivaratri
	"March 25 2024",     // Holi
	"March 29 2024",     // Good Friday
	"April 11 2024",     // Eid-Ul-Fitr (Ramzan Eid)
	"April 14 2024",     // Dr. Baba Saheb Ambedkar Jayanti, Sunday
	"April 17 2024",     // Ram Navami
	"April 21 2024",     // Shri Mahavir Jayanti, Sunday
	"May 01 2024",       // Maharashtra Day
	"June 17 2024",      // Bakri Eid
	"July 17 2024",      // Moharram
	"August 15 2024",    // Independence Day
	"September 07 2024", // Ganesh Chaturthi, Saturday
	"October 02 2024",   // Mahatma Gandhi Jayanti
	"October 12 2024",   // Dussehra, Saturday
	"November 01 2024",  // Diwali-Laxmi Pujan
	"November 02 2024",  // Diwali-Balipratipada, Saturday
	"November 15 2024",  // Gurunanak Jayanti
	"December 25 2024",  // Christmas

	// 2025
	"January 26 2025",  // Republic Day
	"February 26 2025", // Maha Shivaratri
	"March 14 2025",    // Holi
	"March 31 2025",    // Eid-Ul-Fitr (Ramzan Eid)
	"April 06 2025",    // Ram Navami
	"April 10 2025",    // Mahavir Jayanti
	"April 14 2025",    // Dr. Baba Saheb Ambedkar Jayanti, Sunday
	"April 18 2025",    // Good Friday
	"May 01 2025",      // Maharashtra Day
	"June 07 2025",     // Bakri Id / Eid ul-Adha
	"July 06 2025",     // Moharram
	"August 15 2025",   // Independence Day
	"August 27 2025",   // Ganesh Chaturthi, Saturday
	"October 02 2025",  // Mahatma Gandhi Jayanti, Dussehra,
	"October 22 2025",  // Diwali-Balipratipada
	"November 05 2025", // Gurunanak Jayanti
	"November 20 2025", // Diwali-Laxmi Pujan**
	"December 25 2025", // Christmas
}

// Cutoff time for ETA calculation in HH:SS 24-hour format
const createdAtCutOffTime = "14:00"
const paymentCutOffTime = "15:00"

type ETAParams struct {
	PendingOrder  *orderPb.Order
	AssetClass    mfPb.AssetClass
	PaymentStatus paymentPb.PaymentStatus
	PaymentTime   *timestamp.Timestamp
	CategoryName  mfPb.MutualFundCategoryName
}

func GetETADate(params ETAParams) (time.Time, error) {
	createdDateTime := params.PendingOrder.GetCreatedAt().AsTime().In(datetime.IST)
	paymentTime := params.PaymentTime.AsTime().In(datetime.IST)
	var expectedDays int
	switch params.PendingOrder.GetOrderType() {
	case orderPb.OrderType_BUY:
		if params.PaymentStatus == paymentPb.PaymentStatus_PAYMENT_STATUS_SUCCESSFUL {
			isPaidBeforeCutOff, err := isPaymentTimeBeforeCutOff(paymentTime)
			if err != nil {
				return time.Time{}, fmt.Errorf("error while parsing dates to compare cutOff times: %w", err)
			}
			if isPaidBeforeCutOff {
				expectedDays = 1
			} else {
				expectedDays = 2
			}
		} else {
			isCreatedBeforeCutOff, err := isCreatedTimeBeforeCutOff(createdDateTime)
			if err != nil {
				return time.Time{}, fmt.Errorf("error while parsing dates to compare cutOff times: %w", err)
			}
			if isCreatedBeforeCutOff {
				expectedDays = 1
			} else {
				expectedDays = 2
			}
		}
		// temporary because of a delay in cams processing
		// TODO(Rishab) remove later when cams processing is stable
		if params.PendingOrder.GetRta() == commonvgpb.Vendor_CAMS {
			expectedDays += 1
		}
		// temporary because of a delay in Karvy processing
		// TODO(Ayush) Move this to dynamic config
		if params.PendingOrder.GetRta() == commonvgpb.Vendor_KARVY {
			expectedDays += 1
		}
		// You can use params.CategoryName here if needed for custom logic
	case orderPb.OrderType_SELL:
		// For "SELL" orders, expected ETA varies according to asset class.
		expectedDays = GetExpectedDaysByAssetClass(params.AssetClass, params.CategoryName)
		// You can use params.CategoryName here if needed for custom logic
	default:
		return time.Time{}, fmt.Errorf("invalid MF order type")
	}

	var expectedTime time.Time
	if params.PaymentStatus == paymentPb.PaymentStatus_PAYMENT_STATUS_SUCCESSFUL {
		expectedTime = getExpectedTimeForETA(paymentTime, expectedDays)
	} else {
		expectedTime = getExpectedTimeForETA(createdDateTime, expectedDays)
	}
	// Load time in IST
	timeZoneLocation, err := time.LoadLocation(ISTTimeZoneLocation)
	if err != nil {
		return time.Time{}, fmt.Errorf("error while trying to load IST time zone: %w", err)
	}
	// Rounding off to end of day
	finalExpectedTime := time.Date(expectedTime.Year(), expectedTime.Month(), expectedTime.Day(),
		23, 59, 0, 0, timeZoneLocation)

	return finalExpectedTime, nil
}

func GetWithdrawalETADays(assetClass mfPb.AssetClass, categoryName mfPb.MutualFundCategoryName) string {
	currentTime := time.Now()
	expectedDays := GetExpectedDaysByAssetClass(assetClass, categoryName)
	expectedTime := getExpectedTimeForETA(currentTime, expectedDays).Sub(currentTime)
	return strconv.Itoa(int(math.Ceil(expectedTime.Hours() / 24.0)))
}

// Function that iterates from createdTime to createdTime+expectedDays, ignoring holidays and weekends
// returns the final expected time after ignoring holidays
func getExpectedTimeForETA(createdDateTime time.Time, expectedDays int) time.Time {
	expectedDateTime := createdDateTime
	for i := 1; i <= expectedDays; i++ {
		if IsWeekend(expectedDateTime) || IsSebiHoliday(expectedDateTime) {
			expectedDays++
		}
		expectedDateTime = expectedDateTime.AddDate(0, 0, 1)
	}
	return expectedDateTime
}

func IsWeekend(day time.Time) bool {
	if day.Weekday() == time.Saturday || day.Weekday() == time.Sunday {
		return true
	}
	return false
}

func IsSebiHoliday(day time.Time) bool {
	for _, str := range SEBIHolidays {
		holDate, _ := time.Parse("January 02 2006", str)
		if (day.Year() == holDate.Year()) && (day.YearDay() == holDate.YearDay()) {
			return true
		}
	}
	return false
}

func GetExpectedDaysByAssetClass(assetClass mfPb.AssetClass, categoryName mfPb.MutualFundCategoryName) int {
	switch assetClass {
	case mfPb.AssetClass_CASH:
		return 2
	case mfPb.AssetClass_DEBT:
		return 3
	case mfPb.AssetClass_EQUITY:
		if categoryName == mfPb.MutualFundCategoryName_GLOBAL_OTHER {
			return 8
		}
		return 4
	case mfPb.AssetClass_HYBRID:
		return 4
	default:
		return 7
	}
}

// Function to truncate date from createdDateTime and just check if the time is before the specified cut off.
// Current cut off time is 2 pm
func isCreatedTimeBeforeCutOff(createdDateTime time.Time) (bool, error) {
	createdTime, err := time.Parse("15:04", createdDateTime.Format("15:04"))
	if err != nil {
		return false, err
	}
	cutOff, err := time.Parse("15:04", createdAtCutOffTime)
	if err != nil {
		return false, err
	}
	return createdTime.Before(cutOff), nil
}

// decides whether payment was made before specified cut-off time, currently 3 p.m.
func isPaymentTimeBeforeCutOff(paymentDateTime time.Time) (bool, error) {
	paymentTime, err := time.Parse("15:04", paymentDateTime.Format("15:04"))
	if err != nil {
		return false, err
	}
	cutOff, err := time.Parse("15:04", paymentCutOffTime)
	if err != nil {
		return false, err
	}
	return paymentTime.Before(cutOff), nil
}
