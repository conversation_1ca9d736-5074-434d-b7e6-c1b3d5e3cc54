package usstocks

import (
	"context"
	"fmt"
	"sort"
	"strings"

	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/genproto/protobuf/field_mask"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/colors"
	dateTimePkg "github.com/epifi/be-common/pkg/datetime"
	decimalPkg "github.com/epifi/be-common/pkg/decimal"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/math"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/frontend/usstocks"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/typesv2/ui/charts"
	ussAccountMgPb "github.com/epifi/gamma/api/usstocks/account"
	"github.com/epifi/gamma/api/usstocks/catalog"
	"github.com/epifi/gamma/pkg/fittt"
	"github.com/epifi/gamma/pkg/usstocks/deeplinks"
)

type SIPProjectionDuration int

const (
	Zero SIPProjectionDuration = iota
	OneYear
	ThreeYears
	FiveYears
)

var (
	ErrInterestRateZero = errors.New("interest rate zero")
)

func (d SIPProjectionDuration) String() string {
	switch d {
	case OneYear:
		return "1Y"
	case ThreeYears:
		return "3Y"
	case FiveYears:
		return "5Y"
	default:
		return fmt.Sprintf("invalid duration: %s", d.String())
	}
}

func ConvertToSIPProjectionDuration(duration string) (SIPProjectionDuration, error) {
	switch duration {
	case "1Y":
		return OneYear, nil
	case "3Y":
		return ThreeYears, nil
	case "5Y":
		return FiveYears, nil
	default:
		return 0, errors.Errorf("invalid duration: %s", duration)
	}
}

func (d SIPProjectionDuration) ToHistoricalStockPriceDuration() string {
	switch d {
	case OneYear:
		return "1Y"
	case ThreeYears:
		return "3Y"
	case FiveYears:
		return "5Y"
	default:
		return fmt.Sprintf("invalid duration: %s", d.String())
	}
}

func (d SIPProjectionDuration) GetBars(stockPricesForPeriods []*catalog.StockPricesForPeriod) ([]*catalog.Bar, error) {
	var stockBars []*catalog.Bar
	for _, pricesForPeriod := range stockPricesForPeriods {
		switch d {
		case OneYear:
			if pricesForPeriod.GetPeriod() == "1Y" {
				stockBars = pricesForPeriod.GetBars()
				break
			}
		case ThreeYears:
			if pricesForPeriod.GetPeriod() == "3Y" {
				stockBars = pricesForPeriod.GetBars()
				break
			}
		case FiveYears:
			if pricesForPeriod.GetPeriod() == "5Y" {
				stockBars = pricesForPeriod.GetBars()
				break
			}
		default:
			return nil, errors.Errorf("invalid duration: %s", d.String())
		}
	}
	if len(stockBars) == 0 {
		return nil, errors.Errorf("no stock bars found for duration: %s", d.String())
	}
	return stockBars, nil
}

func (d SIPProjectionDuration) GetNumPeriods() (int, error) {
	switch d {
	case OneYear:
		return 12, nil
	case ThreeYears:
		return 36, nil
	case FiveYears:
		return 60, nil
	default:
		return 0, errors.Errorf("invalid duration: %s", d.String())
	}
}

type GetWidgetRequest struct {
	// This is optional. If not provided, we would not show any action cta on the widget
	ActorId string
	StockId string
}

type GetProjectionRequest struct {
	StockId string

	Contribution *moneyPb.Money

	Duration SIPProjectionDuration
}

type ISIPProjectionCalculator interface {
	// GetWidget returns the SIP projection widget for the given stock
	// In case there isn't enough historical data for the stock, no widget is returned
	GetWidget(ctx context.Context, req *GetWidgetRequest) (*usstocks.SIPProjectionWidget, error)

	// GetProjection returns the SIP projection for the given stock, contribution amount, and duration
	// In case there isn't enough historical data for the stock, an error is returned
	GetProjection(ctx context.Context, req *GetProjectionRequest) (*usstocks.SIPProjection, error)
}

type SIPProjectionCalculator struct {
	catalogClient       catalog.CatalogManagerClient
	ussAccountMgrClient ussAccountMgPb.AccountManagerClient
	annualBankFDRate    decimal.Decimal
	latestNifty50Value  decimal.Decimal
}

func NewSIPProjectionCalculator(
	catalogClient catalog.CatalogManagerClient,
	annualBankFDRate decimal.Decimal,
	latestNifty50Value decimal.Decimal,
	ussAccountMgrClient ussAccountMgPb.AccountManagerClient,
) *SIPProjectionCalculator {
	return &SIPProjectionCalculator{
		catalogClient:       catalogClient,
		annualBankFDRate:    annualBankFDRate,
		latestNifty50Value:  latestNifty50Value,
		ussAccountMgrClient: ussAccountMgrClient,
	}
}

type SIPProjectionAmountOption struct {
	Id string

	Amount *moneyPb.Money

	DisplayValue string

	IsDefault bool
}

var sipProjectionAmountOptions = []*SIPProjectionAmountOption{
	{
		Id:           "1000",
		Amount:       moneyPkg.AmountINR(1000).GetPb(),
		DisplayValue: "₹1000",
	},
	{
		Id:           "5000",
		Amount:       moneyPkg.AmountINR(5000).GetPb(),
		DisplayValue: "₹5000",
		IsDefault:    true,
	},
	{
		Id:           "10000",
		Amount:       moneyPkg.AmountINR(10_000).GetPb(),
		DisplayValue: "₹10,000",
	},
}

func ConvertToSIPProjectionAmountOption(optionId string) (*SIPProjectionAmountOption, error) {
	for _, amountOption := range sipProjectionAmountOptions {
		if optionId == amountOption.Id {
			return amountOption, nil
		}
	}
	return nil, errors.Errorf("invalid option id: %s", optionId)
}

type SIPProjectionDurationOption struct {
	Duration SIPProjectionDuration

	DisplayValue string

	IsDefault bool
}

var sipProjectionDurationOptions = []*SIPProjectionDurationOption{
	{
		Duration:     OneYear,
		DisplayValue: "1Y",
	},
	{
		Duration:     FiveYears,
		DisplayValue: "5Y",
		IsDefault:    true,
	},
}

func (s *SIPProjectionCalculator) GetWidget(ctx context.Context, req *GetWidgetRequest) (*usstocks.SIPProjectionWidget, error) {
	var amountOptions []*ui.StepOption
	for _, amountOption := range sipProjectionAmountOptions {
		amountOptions = append(amountOptions, &ui.StepOption{
			Id: amountOption.Id,
			DisplayValue: commontypes.GetPlainStringText(amountOption.DisplayValue).
				WithFontColor(colors.ColorLightPrimaryAction).WithFontStyle(commontypes.FontStyle_SUBTITLE_M),
			IsSelected: amountOption.IsDefault,
		})
	}
	res, err := s.catalogClient.GetHistoricalStockPrices(ctx, &catalog.GetHistoricalStockPricesRequest{
		StockId: req.StockId,
	})
	if err = epifigrpc.RPCError(res, err); err != nil {
		if res.GetStatus().IsResourceExhausted() {
			return nil, errors.Wrap(epifierrors.ErrResourceExhausted, "resource exhausted while fetching historical stock prices")
		}
		return nil, errors.Wrap(err, "error in getting historical stock prices")
	}
	var oneYearPricesFound, fiveYearPricesFound bool
	for _, pricesForPeriod := range res.GetStockPricesForPeriods() {
		if pricesForPeriod.GetPeriod() == "5Y" {
			fiveYearPricesFound = true
		}
		if pricesForPeriod.GetPeriod() == "1Y" {
			oneYearPricesFound = true
		}
	}
	var (
		durationOptions       []*ui.GridSelector_GridOption
		defaultDurationOption *SIPProjectionDurationOption
	)
	switch {
	case fiveYearPricesFound:
		defaultDurationOption = sipProjectionDurationOptions[1]
		for _, durationOption := range sipProjectionDurationOptions {
			durationOptions = append(durationOptions, &ui.GridSelector_GridOption{
				Id: durationOption.Duration.String(),
				Value: ui.NewITC().WithTexts(commontypes.GetPlainStringText(durationOption.DisplayValue).
					WithFontColor(colors.ColorOnDarkLowEmphasis).WithFontStyle(commontypes.FontStyle_SUBTITLE_3)).
					WithContainerPadding(8, 12, 8, 12).
					WithContainerBackgroundColor("#EFF2F6").WithContainerCornerRadius(12),
				IsSelected: durationOption.IsDefault,
				SelectedValue: ui.NewITC().WithTexts(commontypes.GetPlainStringText(durationOption.DisplayValue).
					WithFontColor(colors.ColorForest).WithFontStyle(commontypes.FontStyle_SUBTITLE_3)).
					WithContainerPadding(8, 12, 8, 12).
					WithContainerBackgroundColor(colors.ColorSnow).WithContainerCornerRadius(12),
			})
		}
	case oneYearPricesFound:
		defaultDurationOption = sipProjectionDurationOptions[0]
		durationOptions = []*ui.GridSelector_GridOption{
			{
				Id: defaultDurationOption.Duration.String(),
				Value: ui.NewITC().WithTexts(commontypes.GetPlainStringText(defaultDurationOption.DisplayValue).
					WithFontColor(colors.ColorForest).WithFontStyle(commontypes.FontStyle_SUBTITLE_3)).
					WithContainerPadding(8, 12, 8, 12).
					WithContainerBackgroundColor("#EFF2F6").WithContainerCornerRadius(12),
				IsSelected: defaultDurationOption.IsDefault,
				SelectedValue: ui.NewITC().WithTexts(commontypes.GetPlainStringText(defaultDurationOption.DisplayValue).
					WithFontColor(colors.ColorForest).WithFontStyle(commontypes.FontStyle_SUBTITLE_3)).
					WithContainerPadding(8, 12, 8, 12).
					WithContainerBackgroundColor(colors.ColorSnow).WithContainerCornerRadius(12),
			},
		}
	default:
		// Stock doesn't have enough historical data for SIP projection
		return nil, nil
	}
	sipProjection, err := s.GetProjection(ctx, &GetProjectionRequest{
		StockId:      req.StockId,
		Contribution: sipProjectionAmountOptions[1].Amount,
		Duration:     defaultDurationOption.Duration,
	})
	if err != nil {
		switch {
		case errors.Is(err, ErrInterestRateZero):
			return nil, nil
		default:
			return nil, errors.Wrap(err, "error getting SIP projection")
		}
	}
	var nextAction *ui.IconTextComponent
	if req.ActorId != "" {
		// checking if user's uss account is active or not
		// will show start cta to active users only, rest will be redirected to complete onboarding
		getAccountResp, getAccountErr := s.ussAccountMgrClient.GetAccount(ctx, &ussAccountMgPb.GetAccountRequest{
			Vendor:    commonvgpb.Vendor_ALPACA,
			ActorId:   req.ActorId,
			FieldMask: &field_mask.FieldMask{Paths: []string{(&ussAccountMgPb.Account{}).GetAccountAccountStatusPath()}},
		})
		if rpcErr := epifigrpc.RPCError(getAccountResp, getAccountErr); !getAccountResp.GetStatus().IsRecordNotFound() && rpcErr != nil {
			return nil, errors.Wrap(rpcErr, "failed to get account")
		}

		if getAccountResp.GetStatus().IsRecordNotFound() || getAccountResp.GetAccount().GetAccountStatus() != ussAccountMgPb.AccountStatus_ACTIVE {
			nextAction = ui.NewITC().WithTexts(commontypes.GetPlainStringText("Setup account to start SIP").WithFontColor(colors.ColorSnow).WithFontStyle(commontypes.FontStyle_BUTTON_M)).
				WithDeeplink(deeplinks.GetOnBoardingSetupScreen())
		} else {
			nextAction = ui.NewITC().WithTexts(commontypes.GetPlainStringText("Start SIP").WithFontColor(colors.ColorSnow).WithFontStyle(commontypes.FontStyle_BUTTON_M)).
				WithDeeplink(fittt.GetCustomiseUSStockSIPRuleDeeplink(req.StockId))
		}
		nextAction.WithContainerPadding(12, 16, 12, 16).WithContainerBackgroundColor(colors.ColorForest).WithContainerCornerRadius(20)
	}

	return &usstocks.SIPProjectionWidget{
		Title: ui.NewITC().WithTexts(
			commontypes.GetPlainStringText("Every month, if you had invested").
				WithFontColor(colors.ColorOnLightHighEmphasis).WithFontStyle(commontypes.FontStyle_HEADLINE_S)).
			WithContainerPadding(16, 16, 16, 16).
			WithContainerBackgroundColor(colors.ColorOnDarkHighEmphasis),
		Stepper: &ui.StepOptionsSelector{
			Title: commontypes.GetPlainStringText("SIP Amount").
				WithFontColor(colors.ColorMonochromeAsh).WithFontStyle(commontypes.FontStyle_OVERLINE_2XS_CAPS),
			DecrementIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/usstocks_images/sip-projection-calculator-minus.png"),
			IncrementIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/usstocks_images/sip-projection-calculator-plus.png"),
			Options:       amountOptions,
			BorderColor:   "#EFF2F6",
			BorderWidth:   2,
			CornerRadius:  16,
		},
		DurationSelector: &usstocks.SIPDurationSelector{
			DurationSelector: &ui.GridSelector{
				Type:                      ui.SelectorType_SELECTOR_TYPE_SINGLE_SELECT,
				Options:                   durationOptions,
				SelectedOptionBorderColor: colors.ColorLightPrimaryAction,
			},
			LeadingTitle: ui.NewITC().WithTexts(
				commontypes.GetPlainStringText("For a duration of :").
					WithFontColor(colors.ColorNight).
					WithFontStyle(commontypes.FontStyle_SUBTITLE_3),
			),
		},
		Projection: sipProjection,
		Disclaimer: ui.NewITC().WithTexts(
			commontypes.GetPlainStringText("Past performance doesn’t guarantee future returns.").
				WithFontColor(colors.ColorMonochromeAsh).WithFontStyle(commontypes.FontStyle_BODY_XS),
		),
		StartSipCta:  nextAction,
		CornerRadius: 12,
	}, nil
}

func (s *SIPProjectionCalculator) GetProjection(ctx context.Context, req *GetProjectionRequest) (*usstocks.SIPProjection, error) {
	stocksRes, err := s.catalogClient.GetStocks(ctx, &catalog.GetStocksRequest{
		Identifiers: &catalog.GetStocksRequest_StockIds{
			StockIds: &catalog.RepeatedStrings{Ids: []string{req.StockId}},
		},
	})
	if err != nil {
		return nil, errors.Wrap(err, "error in getting stock details")
	}
	stock, ok := stocksRes.GetStocks()[req.StockId]
	if !ok {
		return nil, errors.Errorf("stock not found: %s", req.StockId)
	}
	stockName := stock.GetStockBasicDetails().GetName().GetShortName()
	if len(stockName) > 10 {
		stockName = stock.GetSymbol()
	}
	res, err := s.getHistoricalSIPReturns(ctx, req)
	if err != nil {
		return nil, errors.Wrap(err, "error in getting historical SIP returns")
	}
	return &usstocks.SIPProjection{
		ProjectedValue: &usstocks.SIPProjectionSummary{
			Title: ui.NewITC().WithTexts(
				commontypes.GetPlainStringText(stockName).WithFontColor(colors.ColorLightPrimaryAction).WithFontStyle(commontypes.FontStyle_SUBTITLE_S),
				commontypes.GetPlainStringText(" could've been worth : ").WithFontColor(colors.ColorDarkLayer2).WithFontStyle(commontypes.FontStyle_SUBTITLE_S)),
			Amount: commontypes.GetPlainStringText(moneyPkg.ToDisplayStringWithSuffixAndPrecision(moneyPkg.Round(res.StockReturns.FutureValue, 0), true, true, 2, moneyPkg.IndianNumberSystem)).
				WithFontColor(colors.ColorLightPrimaryAction).WithFontStyle(commontypes.FontStyle_NUMBER_L),
		},
		Graph: &charts.BarGraph{
			Orientation: charts.BarGraphOrientation_BAR_GRAPH_ORIENTATION_HORIZONTAL,
			Bars: []*charts.Bar{
				{
					Value: res.StockReturns.PercentageChange.InexactFloat64(),
					XAxisDisplayComponent: &charts.Bar_BarDisplayComponent{
						Value: &charts.Bar_BarDisplayComponent_DisplayText{
							DisplayText: ui.NewITC().WithTexts(commontypes.GetPlainStringText(strings.ToUpper(stockName)).
								WithFontColor(colors.ColorOnDarkLowEmphasis).WithFontStyle(commontypes.FontStyle_SUBTITLE_XS)),
						},
					},
					BarColor: getBarColorForChange(res.StockReturns.PercentageChange),
					YAxisDisplayValue: commontypes.GetPlainStringText(
						fmt.Sprintf("%s%%", res.StockReturns.PercentageChange.Round(2).String())).
						WithFontColor(getTextColorForChange(res.StockReturns.PercentageChange)).
						WithFontStyle(commontypes.FontStyle_NUMBER_S),
				},
				{
					Value: res.Nifty50Returns.PercentageChange.InexactFloat64(),
					XAxisDisplayComponent: &charts.Bar_BarDisplayComponent{
						Value: &charts.Bar_BarDisplayComponent_DisplayText{
							DisplayText: ui.NewITC().WithTexts(
								commontypes.GetPlainStringText("NIFTY 50").
									WithFontColor(colors.ColorOnDarkLowEmphasis).
									WithFontStyle(commontypes.FontStyle_SUBTITLE_XS)),
						},
					},
					BarColor: getBarColorForChange(res.Nifty50Returns.PercentageChange),
					YAxisDisplayValue: commontypes.GetPlainStringText(
						fmt.Sprintf("%s%%", res.Nifty50Returns.PercentageChange.Round(2).String())).
						WithFontColor(getTextColorForChange(res.Nifty50Returns.PercentageChange)).
						WithFontStyle(commontypes.FontStyle_NUMBER_S),
				},
				{
					Value: res.BankFDReturns.PercentageChange.InexactFloat64(),
					XAxisDisplayComponent: &charts.Bar_BarDisplayComponent{
						Value: &charts.Bar_BarDisplayComponent_DisplayText{
							DisplayText: ui.NewITC().WithTexts(
								commontypes.GetPlainStringText("BANK FD").
									WithFontColor(colors.ColorOnDarkLowEmphasis).
									WithFontStyle(commontypes.FontStyle_SUBTITLE_XS)),
						},
					},
					BarColor: getBarColorForChange(res.BankFDReturns.PercentageChange),
					YAxisDisplayValue: commontypes.GetPlainStringText(
						fmt.Sprintf("%s%%", res.BankFDReturns.PercentageChange.Round(2).String())).
						WithFontColor(getTextColorForChange(res.BankFDReturns.PercentageChange)).
						WithFontStyle(commontypes.FontStyle_NUMBER_S),
				},
			},
			DefaultBarColor: colors.SupportingMoss200,
			AxisColor:       "#E6E9ED",
			MaxBarWidth:     12,
			MaxBarSpacing:   18,
		},
		CornerRadius: 16,
		BgColor:      colors.ColorOnDarkHighEmphasis,
	}, nil
}

func getTextColorForChange(change decimal.Decimal) string {
	if change.IsNegative() {
		return colors.ColorOnDarkMediumEmphasis
	}
	return colors.ColorMoss700
}

func getBarColorForChange(change decimal.Decimal) string {
	if change.IsNegative() {
		return "#D48647"
	}
	return colors.SupportingMoss200
}

type GetHistoricalSIPReturnsResponse struct {
	StockReturns *InvestmentReturns

	Nifty50Returns *InvestmentReturns

	BankFDReturns *InvestmentReturns
}

type InvestmentReturns struct {
	TotalContribution *moneyPb.Money

	FutureValue *moneyPb.Money

	PercentageChange decimal.Decimal
}

func (s *SIPProjectionCalculator) getHistoricalSIPReturns(ctx context.Context, req *GetProjectionRequest) (*GetHistoricalSIPReturnsResponse, error) {
	res, err := s.catalogClient.GetHistoricalStockPrices(ctx, &catalog.GetHistoricalStockPricesRequest{
		StockId: req.StockId,
	})
	if err = epifigrpc.RPCError(res, err); err != nil {
		return nil, errors.Wrap(err, "error in getting historical stock prices")
	}
	stockBars, err := req.Duration.GetBars(res.GetStockPricesForPeriods())
	if err != nil {
		return nil, errors.Wrap(err, "error getting stock bars for duration")
	}
	sort.SliceStable(stockBars, func(i, j int) bool {
		return stockBars[i].GetTs().AsTime().Before(stockBars[j].GetTs().AsTime())
	})
	numPeriods, err := req.Duration.GetNumPeriods()
	if err != nil {
		return nil, errors.Wrapf(err, "error getting number of periods for duration: %s", req.Duration.String())
	}
	oldestBar := stockBars[0]
	latestBar := stockBars[len(stockBars)-1]
	contributionValue := moneyPkg.ToDecimal(req.Contribution)
	stockReturns, err := s.getStockReturns(contributionValue, oldestBar, latestBar, numPeriods)
	if err != nil {
		return nil, errors.Wrap(err, "error getting stock returns")
	}
	nifty50Returns, err := s.getNifty50Returns(contributionValue, oldestBar.GetTs(), latestBar.GetTs(), numPeriods)
	if err != nil {
		return nil, errors.Wrap(err, "error getting nifty50 returns")
	}
	bankFDReturns, err := s.getBankFDReturnsForPeriod(contributionValue, numPeriods)
	if err != nil {
		return nil, errors.Wrap(err, "error getting bank FD returns")
	}
	return &GetHistoricalSIPReturnsResponse{
		StockReturns:   stockReturns,
		Nifty50Returns: nifty50Returns,
		BankFDReturns:  bankFDReturns,
	}, nil
}

func (s *SIPProjectionCalculator) getStockReturns(
	contribution decimal.Decimal,
	earliestBar, latestBar *catalog.Bar,
	numPeriods int,
) (*InvestmentReturns, error) {
	earliestForexRate, err := getForexRateForTs(earliestBar.GetTs())
	if err != nil {
		return nil, errors.Wrap(err, "error getting forex rate for the earliest bar")
	}
	latestForexRate, err := getForexRateForTs(latestBar.GetTs())
	if err != nil {
		return nil, errors.Wrap(err, "error getting forex rate for the latest bar")
	}
	earliestINRValue := decimal.NewFromFloat(earliestBar.GetClosePrice()).Mul(earliestForexRate)
	latestINRValue := decimal.NewFromFloat(latestBar.GetClosePrice()).Mul(latestForexRate)
	rate, err := math.CalculateInterestRate(&math.CalculateInterestRateRequest{
		Contribution: earliestINRValue,
		FutureValue:  latestINRValue,
		NumPeriods:   numPeriods,
	})
	if err != nil {
		return nil, errors.Wrap(err, "error calculating rate of interest")
	}
	v, err := math.CalculateFutureValueRecurring(&math.CalculateFutureValueRequest{
		Contribution: contribution,
		Rate:         rate,
		NumPeriods:   numPeriods,
	})
	if err != nil {
		return nil, errors.Wrap(err, "error calculating future value")
	}
	totalContribution := getTotalContribution(contribution, numPeriods)
	return &InvestmentReturns{
		TotalContribution: moneyPkg.ParseDecimal(totalContribution, moneyPkg.RupeeCurrencyCode),
		FutureValue:       moneyPkg.ParseDecimal(v, moneyPkg.RupeeCurrencyCode),
		PercentageChange:  v.Div(totalContribution).Sub(decimalPkg.One).Mul(decimalPkg.OneHundred),
	}, nil
}

func getForexRateForTs(ts *timestampPb.Timestamp) (decimal.Decimal, error) {
	var forexRate decimal.Decimal
	t := ts.AsTime().In(dateTimePkg.IST)
	lastForexRateDateValidTill := dateTimePkg.DateToTime(monthlyForexRates[0].Date, dateTimePkg.IST).AddDate(0, 1, 0)
	if t.Equal(lastForexRateDateValidTill) || t.After(lastForexRateDateValidTill) {
		return monthlyForexRates[0].Rate, nil
	}
	for _, r := range monthlyForexRates {
		timeOfRate := dateTimePkg.DateToTime(r.Date, dateTimePkg.IST)
		if timeOfRate.Year() == t.Year() && int(timeOfRate.Month()) == int(t.Month()) {
			forexRate = r.Rate
		}
	}
	if forexRate.IsZero() {
		return decimal.Zero, errors.Errorf("forex rate not found for the given timestamp: %s", ts.String())
	}
	return forexRate, nil
}

func (s *SIPProjectionCalculator) getNifty50Returns(
	contribution decimal.Decimal,
	startTs, endTs *timestampPb.Timestamp,
	numPeriods int,
) (*InvestmentReturns, error) {
	earliestNifty50Value, err := s.getNifty50ValueForTs(startTs)
	if err != nil {
		return nil, errors.Wrap(err, "error getting nifty value for the earliest bar")
	}
	latestNifty50Value, err := s.getNifty50ValueForTs(endTs)
	if err != nil {
		return nil, errors.Wrap(err, "error getting nifty value for the latest bar")
	}
	niftyRate, err := math.CalculateInterestRate(&math.CalculateInterestRateRequest{
		Contribution: earliestNifty50Value,
		FutureValue:  latestNifty50Value,
		NumPeriods:   numPeriods,
	})
	if err != nil {
		return nil, errors.Wrap(err, "error calculating rate of interest")
	}
	if niftyRate.IsZero() {
		return nil, ErrInterestRateZero
	}
	v, err := math.CalculateFutureValueRecurring(&math.CalculateFutureValueRequest{
		Contribution: contribution,
		Rate:         niftyRate,
		NumPeriods:   numPeriods,
	})
	if err != nil {
		return nil, errors.Wrap(err, "error calculating future value")
	}
	totalContribution := getTotalContribution(contribution, numPeriods)
	return &InvestmentReturns{
		TotalContribution: moneyPkg.ParseDecimal(totalContribution, moneyPkg.RupeeCurrencyCode),
		FutureValue:       moneyPkg.ParseDecimal(v, moneyPkg.RupeeCurrencyCode),
		PercentageChange:  v.Div(totalContribution).Sub(decimalPkg.One).Mul(decimalPkg.OneHundred),
	}, nil
}

func (s *SIPProjectionCalculator) getNifty50ValueForTs(ts *timestampPb.Timestamp) (decimal.Decimal, error) {
	var niftyValue decimal.Decimal
	t := ts.AsTime().In(dateTimePkg.IST)
	lastAveragedNiftyDateValidTill := dateTimePkg.DateToTime(monthlyNifty50Values[0].Date, dateTimePkg.IST).AddDate(0, 1, 0)
	if t.Equal(lastAveragedNiftyDateValidTill) || t.After(lastAveragedNiftyDateValidTill) {
		return s.latestNifty50Value, nil
	}
	for _, r := range monthlyNifty50Values {
		timeOfRate := dateTimePkg.DateToTime(r.Date, dateTimePkg.IST)
		if timeOfRate.Year() == t.Year() && int(timeOfRate.Month()) == int(t.Month()) {
			niftyValue = r.MarketValue
		}
	}
	if niftyValue.IsZero() {
		return decimal.Zero, errors.Errorf("nifty value not found for the given timestamp: %s", ts.String())
	}
	return niftyValue, nil
}

func (s *SIPProjectionCalculator) getBankFDReturnsForPeriod(contribution decimal.Decimal, numMonths int) (*InvestmentReturns, error) {
	// The most popular compounding frequency for FDs provided by banks in India is quarterly.
	ratePerQuarter, err := getRatePerPeriodFromAnnualInterestRate(s.annualBankFDRate, 4)
	if err != nil {
		return nil, errors.Wrap(err, "error getting rate per period from annual")
	}
	v, err := math.CalculateFutureValueRecurring(&math.CalculateFutureValueRequest{
		// Each quarter's contribution consists of 3 monthly payments
		Contribution: contribution.Mul(decimalPkg.Three),
		Rate:         ratePerQuarter,
		NumPeriods:   numMonths / 3,
	})
	if err != nil {
		return nil, errors.Wrap(err, "error calculating future value")
	}
	totalContribution := getTotalContribution(contribution, numMonths)
	return &InvestmentReturns{
		TotalContribution: moneyPkg.ParseDecimal(totalContribution, moneyPkg.RupeeCurrencyCode),
		FutureValue:       moneyPkg.ParseDecimal(v, moneyPkg.RupeeCurrencyCode),
		PercentageChange:  v.Div(totalContribution).Sub(decimalPkg.One).Mul(decimalPkg.OneHundred),
	}, nil
}

// getRatePerPeriodFromAnnualInterestRate calculates the rate of interest per period given the annual rate and number of compounding periods.
// Banks provide annual interest rates for FDs, but the interest is compounded quarterly, monthly, or daily.
// Hence, the actual rate per compounding period would be slightly lower than the annual rate divided by the compounding frequency.
// The first contribution grows for n periods, the second contribution grows for n-1 periods, and so on.
// Base formula: Annual interest rate = (1 + rate per period)^freq - 1
// => rate per period = (1 + annual interest rate)^(1/freq) - 1
func getRatePerPeriodFromAnnualInterestRate(annualRate decimal.Decimal, compoundingFrequency int) (decimal.Decimal, error) {
	if compoundingFrequency == 0 {
		return decimal.Zero, errors.New("zero frequency")
	}
	oneByFreq := decimalPkg.One.Div(decimal.NewFromInt(int64(compoundingFrequency)))
	return decimalPkg.One.Add(annualRate).Pow(oneByFreq).Sub(decimalPkg.One), nil
}

func getTotalContribution(contribution decimal.Decimal, numPeriods int) decimal.Decimal {
	return contribution.Mul(decimal.NewFromInt(int64(numPeriods)))
}
