//go:build wireinject
// +build wireinject

package wire

import (
	"net/http"

	"github.com/google/wire"

	"github.com/epifi/be-common/pkg/idgen"

	dsig "github.com/epifi/be-common/pkg/goxmldsig"

	"github.com/epifi/be-common/pkg/storage"

	"github.com/epifi/be-common/pkg/cache"

	"github.com/epifi/be-common/pkg/redactor/httpcontentredactor"
	"github.com/epifi/be-common/pkg/vendorapi"

	vendorApiGenconf "github.com/epifi/be-common/pkg/vendorapi/config/genconf"

	vendorClient "github.com/epifi/gamma/api/vendors/http"
	"github.com/epifi/gamma/pkg/llm"
	"github.com/epifi/gamma/pkg/llm/config/genconf"
	"github.com/epifi/gamma/pkg/llm/wire/types"
)

var SecureHttpClientNilSignCtxWireSet = wire.NewSet(SecureHttpClientProvider, nilSigningContextProvider)

func InitialiseLLMHandler(gconf *genconf.Config) *llm.LLMHandlerImpl {
	wire.Build(
		HttpRequestHandlerProvider,
		llm.NewLLMHandlerImpl,
		LLMCacheProvider,
		idgen.UuidGeneratorWireSet,
	)
	return &llm.LLMHandlerImpl{}
}

func HttpRequestHandlerProvider(gconf *genconf.Config) *vendorapi.HTTPRequestHandler {
	wire.Build(
		// Providers required for HTTPRequestHandler
		VendorApiConfProvider,
		EnvProvider,

		SecureHttpClientNilSignCtxWireSet,
		vendorapi.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapi.HttpDoer), new(*http.Client)))
	return &vendorapi.HTTPRequestHandler{}
}

func EnvProvider(gconf *genconf.Config) string {
	return gconf.Environment()
}

func VendorApiConfProvider(gconf *genconf.Config) *vendorApiGenconf.Config {
	return gconf.VendorApiConf()
}

func LLMCacheProvider(gconf *genconf.Config) types.LLMCacheStorage {
	llmRedisClient := storage.NewRedisClientFromConfig(gconf.RedisOptions(), false)
	redisStore := cache.NewRedisCacheStorage(llmRedisClient)
	return cache.NewRedisCacheStorageWithHystrix(redisStore, gconf.RedisOptions().HystrixCommand)
}

func nilSigningContextProvider() *dsig.SigningContext {
	return nil
}

func getHttpClient(gconf *vendorApiGenconf.Config, env string, insecure ...bool) *http.Client {
	return vendorClient.NewHttpClient(gconf.HttpClientConfig())
}

func SecureHttpClientProvider(gconf *vendorApiGenconf.Config, env string) *http.Client {
	return getHttpClient(gconf, env, false)
}
