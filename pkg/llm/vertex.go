package llm

import (
	"context"

	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"

	llmPb "github.com/epifi/gamma/api/llm"
	vertex2 "github.com/epifi/gamma/pkg/llm/vertex"
)

func (s *LLMHandlerImpl) NewVertexRequest(ctx context.Context, req proto.Message) vendorapi.SyncRequest {
	switch v := req.(type) {
	case *llmPb.GenerateContentRequest:
		generateContentReq, _ := req.(*llmPb.GenerateContentRequest)
		return &vertex2.GenerateContentRequest{
			Req:           generateContentReq,
			Ctx:           ctx,
			GeminiConf:    s.gconf.GeminiConf(),
			CacheStorage:  s.cacheStorage,
			UUIDGenerator: s.uuidGenerator,
		}
	case *llmPb.CacheSystemContextRequest:
		cacheSystemContextReq, _ := req.(*llmPb.CacheSystemContextRequest)
		return &vertex2.CacheSystemContextRequest{
			Ctx:          ctx,
			Req:          cacheSystemContextReq,
			GeminiConf:   s.gconf.GeminiConf(),
			CacheStorage: s.cacheStorage,
		}
	default:
		logger.ErrorNoCtx("Unsupported request type", zap.Any("request type", v))
		return nil
	}
}
