// nolint
package vertex

import (
	"context"
	"encoding/json"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	assert "github.com/stretchr/testify/require"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/cache"
	mocksCache "github.com/epifi/be-common/pkg/cache/mocks"
	"github.com/epifi/be-common/pkg/epifierrors"

	llmPb "github.com/epifi/gamma/api/llm"
	"github.com/epifi/gamma/pkg/llm/config"
)

func TestCacheSystemContext_Marshal(t *testing.T) {
	t.<PERSON>llel()
	var (
		sampleSystemContext = "sampleSystemContext"
	)
	type args struct {
		Req          *llmPb.CacheSystemContextRequest
		Ctx          context.Context
		GeminiConf   *config.GeminiConf
		CacheStorage cache.CacheStorage
		AccessToken  string
	}
	type mocks struct {
		cacheStorage *mocksCache.MockCacheStorage
	}
	tests := []struct {
		name      string
		args      args
		want      []byte
		wantErr   error
		wantMocks func(args args, m *mocks)
	}{
		{
			name: "no region for model",
			args: args{
				Req: &llmPb.CacheSystemContextRequest{
					UseCase: llmPb.UseCase_USE_CASE_UNSPECIFIED,
				},
			},
			want:    nil,
			wantErr: epifierrors.ErrUnimplemented,
		},
		{
			name: "success using cached context",
			args: args{
				Req: &llmPb.CacheSystemContextRequest{
					UseCase:       llmPb.UseCase_USE_CASE_NETWORTH_MAGIC_IMPORT,
					Model:         llmPb.Model_MODEL_GEMINI_V2_5_FLASH,
					SystemContext: sampleSystemContext,
				},
			},
			wantMocks: func(args args, m *mocks) {
			},
			want:    []byte(`{"displayName":"magic-import-system-context","model":"projects/projectId/locations/us-central1/publishers/google/models/gemini-2.5-flash","systemInstruction":{"parts":{"text":"sampleSystemContext"},"role":"model"},"ttl":"604800s"}`),
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			cacheStorage := mocksCache.NewMockCacheStorage(ctrl)

			m := &mocks{
				cacheStorage: cacheStorage,
			}
			r := &CacheSystemContextRequest{
				Req:          tt.args.Req,
				Ctx:          context.Background(),
				CacheStorage: cacheStorage,
				GeminiConf:   gconf.GeminiConf(),
			}
			if tt.wantMocks != nil {
				tt.wantMocks(tt.args, m)
			}
			got, err := r.Marshal()
			assert.Equal(t, true, errors.Is(err, tt.wantErr))
			assert.Equal(t, string(tt.want), string(got))
		})
	}
}

func TestCacheSystemContext_Unmarshal(t *testing.T) {
	t.Parallel()
	type args struct {
		bytes []byte
		Ctx   context.Context
		Req   *llmPb.CacheSystemContextRequest
	}
	type mocks struct {
		cacheStorage *mocksCache.MockCacheStorage
	}
	cachedContextResVar := CacheSystemContextRes{
		CachedContentPath: "projects/534727823092/locations/us-central1/cachedContents/5729406933148893184",
		Model:             "projects/wealth-llm-nonprod/locations/us-central1/publishers/google/models/gemini-2.5-flash",
	}
	cacheContextResponse, _ := json.Marshal(cachedContextResVar)

	tests := []struct {
		name      string
		args      args
		want      proto.Message
		wantErr   error
		wantMocks func(args args, m *mocks)
	}{
		{
			name: "success unmarshalling",
			args: args{
				bytes: cacheContextResponse,
				Req: &llmPb.CacheSystemContextRequest{
					UseCase: llmPb.UseCase_USE_CASE_NETWORTH_MAGIC_IMPORT,
					Model:   llmPb.Model_MODEL_GEMINI_V2_5_FLASH,
				},
			},
			want: &llmPb.CacheSystemContextResponse{
				Status: rpc.StatusOk(),
			},
			wantMocks: func(args args, m *mocks) {
				m.cacheStorage.EXPECT().Set(gomock.Any(), getCacheKeyForSystemContext(args.Req.GetModel(), args.Req.GetUseCase()), cachedContextResVar.CachedContentPath, cacheSystemContextTTL).Return(nil)
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			cacheStorage := mocksCache.NewMockCacheStorage(ctrl)

			m := &mocks{
				cacheStorage: cacheStorage,
			}
			r := &CacheSystemContextResponse{
				Ctx:          context.Background(),
				cacheStorage: cacheStorage,
				Req:          tt.args.Req,
			}
			if tt.wantMocks != nil {
				tt.wantMocks(tt.args, m)
			}
			got, err := r.Unmarshal(tt.args.bytes)
			assert.Equal(t, true, errors.Is(err, tt.wantErr))
			assert.Equal(t, tt.want, got)
		})
	}
}
