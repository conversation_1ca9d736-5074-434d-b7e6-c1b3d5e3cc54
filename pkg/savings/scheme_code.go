package savings

import (
	"context"
	"errors"

	"go.uber.org/zap"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/logger"
	kycPb "github.com/epifi/gamma/api/kyc"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/tiering/external"
)

var (
	// ErrTierToSKUMappingNotFound is returned when a tier to SKU mapping is not found.
	ErrTierToSKUMappingNotFound = errors.New("tier to sku mapping not found")
	// ErrSKUToSchemeCodeMappingNotFound is returned when a SKU to scheme code mapping is not found.
	ErrSKUToSchemeCodeMappingNotFound = errors.New("sku to scheme code mapping not found")
)

// GetSchemeCodeAndSKU returns the scheme code and SKU for a given tier, kyc level and vendor.
func GetSchemeCodeAndSKU(ctx context.Context, toTier external.Tier, kycLevel kycPb.KYCLevel, vendor commonvgpb.Vendor) (string, savingsPb.SKU, error) {
	tierToSkuInfoMap := GetTierToSKUMap(kycLevel)
	sku, skuExists := tierToSkuInfoMap[toTier]
	if !skuExists {
		return "", savingsPb.SKU_ACCOUNT_SKU_UNSPECIFIED, ErrTierToSKUMappingNotFound
	}

	schemeCode := getVendorSKU(ctx, sku, vendor)
	if schemeCode == "" {
		return "", sku, ErrSKUToSchemeCodeMappingNotFound
	}
	return schemeCode, sku, nil
}

func getVendorSKU(ctx context.Context, sku savingsPb.SKU, vendor commonvgpb.Vendor) string {
	vendorSKUMap := GetVendorSKUMap()
	vendorToSKUMap, ok := vendorSKUMap[sku]
	if !ok {
		logger.Error(ctx, "invalid sku in get vendor sku", zap.String("sku", sku.String()))
		return ""
	}

	vendorSKU, ok := vendorToSKUMap[vendor]
	if !ok {
		logger.Error(ctx, "invalid vendor in get vendor sku", zap.String("vendor", vendor.String()))
		return ""
	}
	return vendorSKU
}

// GetTierToSKUMap returns the tier to SKU mapping based on the KYC level.
// If the KYC level is not specified, it returns the full KYC mapping.
func GetTierToSKUMap(kycLevel kycPb.KYCLevel) map[external.Tier]savingsPb.SKU {
	if kycLevel == kycPb.KYCLevel_MIN_KYC {
		return map[external.Tier]savingsPb.SKU{
			external.Tier_TIER_FI_REGULAR: savingsPb.SKU_REGULAR_MIN_KYC,
			external.Tier_TIER_FI_BASIC:   savingsPb.SKU_MIN_KYC,
		}
	}

	return map[external.Tier]savingsPb.SKU{
		external.Tier_TIER_FI_REGULAR:          savingsPb.SKU_REGULAR_FULL_KYC,
		external.Tier_TIER_FI_BASIC:            savingsPb.SKU_INFINITY,
		external.Tier_TIER_FI_PLUS:             savingsPb.SKU_FINITY,
		external.Tier_TIER_FI_INFINITE:         savingsPb.SKU_ULTIMATE,
		external.Tier_TIER_FI_SALARY_LITE:      savingsPb.SKU_ULTIMATE,
		external.Tier_TIER_FI_SALARY_BASIC:     savingsPb.SKU_FINITY,
		external.Tier_TIER_FI_SALARY:           savingsPb.SKU_ULTIMATE,
		external.Tier_TIER_FI_AA_SALARY_BAND_1: savingsPb.SKU_FINITY,
		external.Tier_TIER_FI_AA_SALARY_BAND_2: savingsPb.SKU_FINITY,
		external.Tier_TIER_FI_AA_SALARY_BAND_3: savingsPb.SKU_PRIME,
	}
}

// GetVendorSKUMap returns the vendor SKU mapping.
func GetVendorSKUMap() map[savingsPb.SKU]map[commonvgpb.Vendor]string {
	return map[savingsPb.SKU]map[commonvgpb.Vendor]string{
		savingsPb.SKU_MIN_KYC: {
			commonvgpb.Vendor_FEDERAL_BANK: "35033",
		},
		savingsPb.SKU_FINITY: {
			commonvgpb.Vendor_FEDERAL_BANK: "35031",
		},
		savingsPb.SKU_INFINITY: {
			commonvgpb.Vendor_FEDERAL_BANK: "35032",
		},
		savingsPb.SKU_ULTIMATE: {
			commonvgpb.Vendor_FEDERAL_BANK: "35081",
		},
		savingsPb.SKU_NRE_ORIGINAL: {
			commonvgpb.Vendor_FEDERAL_BANK: "35148",
		},
		savingsPb.SKU_NRO_ORIGINAL: {
			commonvgpb.Vendor_FEDERAL_BANK: "35207",
		},
		savingsPb.SKU_REGULAR_MIN_KYC: {
			commonvgpb.Vendor_FEDERAL_BANK: "35026",
		},
		savingsPb.SKU_REGULAR_FULL_KYC: {
			commonvgpb.Vendor_FEDERAL_BANK: "35042",
		},
		savingsPb.SKU_PRIME: {
			commonvgpb.Vendor_FEDERAL_BANK: "35085",
		},
	}
}
