package accrual

import (
	"context"
	"fmt"
	"math"
	"regexp"
	"strings"
	"time"
	"unicode"

	"github.com/jonboulle/clockwork"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	accrualPb "github.com/epifi/gamma/api/accrual"
	fireflyPb "github.com/epifi/gamma/api/firefly"
	rewardsPb "github.com/epifi/gamma/api/rewards"
)

// envToFiCoinsToFiPointsMigrationTime a map of environment to the time after which Fi-Coins will be migrated to Fi-Points.
// After this time, Fi-Coins will not be accrued and all rewards will be in Fi-Points.
var envToFiCoinsToFiPointsMigrationTime = map[string]time.Time{
	cfg.StagingEnv:    time.Date(2025, 7, 15, 0, 0, 0, 0, datetime.IST),
	cfg.QaEnv:         time.Date(2025, 7, 29, 6, 0, 0, 0, datetime.IST),
	cfg.ProductionEnv: time.Date(2025, 8, 1, 0, 0, 0, 0, datetime.IST),
}

// GetFiCoinsToFiPointsMigrationTime returns the migration time for Fi-Coins to Fi-Points based on the current environment.
// The function falls back to the production environment timings in cases of ambiguity
func GetFiCoinsToFiPointsMigrationTime() time.Time {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.ErrorNoCtx("failed to get environment variables", zap.Error(err))
		// Default to production if environment cannot be determined
		env = cfg.ProductionEnv
	}
	migrationTime, exists := envToFiCoinsToFiPointsMigrationTime[env]
	if !exists {
		// Fallback to production if environment is not found
		migrationTime = envToFiCoinsToFiPointsMigrationTime[cfg.ProductionEnv]
	}
	return migrationTime
}

// envToFiCoinsToFiPointsMigrationInProgressMap is a map of environment to a boolean indicating whether the migration from Fi-Coins to Fi-Points migration is in progress.
// NOTE: We will have to manually update this map when we want to enable/disable the migration in any environment(keeping the default to true for production to auto start downtime).
var envToFiCoinsToFiPointsMigrationInProgressMap = map[string]bool{
	// setting false for test environments to avoid test case failures
	cfg.DevelopmentEnv: false,
	cfg.TestEnv:        false,
	cfg.StagingEnv:     false,
	cfg.QaEnv:          false,
	cfg.ProductionEnv:  true,
}

// IsFiCoinsToFiPointsMigrationInProgress checks if the migration from Fi-Coins to Fi-Points is active in the current environment.
// It returns true if the current time is after the migration time for Fi-Coins to Fi-Points and the migration is active in the current environment.
// The function falls back to the production environment flag in cases of ambiguity
func IsFiCoinsToFiPointsMigrationInProgress() bool {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.ErrorNoCtx("failed to get environment variables", zap.Error(err))
		// Default to production if environment cannot be determined
		env = cfg.ProductionEnv
	}
	active, exists := envToFiCoinsToFiPointsMigrationInProgressMap[env]
	if !exists {
		// Fallback to production if environment is not found
		active = envToFiCoinsToFiPointsMigrationInProgressMap[cfg.ProductionEnv]
	}
	return active && time.Now().After(GetFiCoinsToFiPointsMigrationTime())
}

// FetchAccrualAccountType returns the accrual account type for a given reward type and action time.
// This method is used to determine the accrual account type for a given reward type and action time.
func FetchAccrualAccountType(rewardType rewardsPb.RewardType, actionTime *timestampPb.Timestamp) accrualPb.AccountType {
	switch rewardType {
	case rewardsPb.RewardType_FI_COINS:
		if actionTime.AsTime().Before(GetFiCoinsToFiPointsMigrationTime()) {
			return accrualPb.AccountType_ACCOUNT_TYPE_FICOINS
		} else {
			return accrualPb.AccountType_ACCOUNT_TYPE_FI_POINTS
		}
	}
	return accrualPb.AccountType_ACCOUNT_TYPE_UNSPECIFIED
}

// ReplaceCoinWithPointIfApplicable checks:
// 1. If the current time is before GetFiCoinsToFiPointsMigrationTime(), it returns the input string unchanged.
// 2. If the current time is after GetFiCoinsToFiPointsMigrationTime(), it calls replaceCoinWithPoint to replace any occurrence of the
// word "coin"/"coins" (with case and plurality preserved) with "point"/"points" in the input string.
func ReplaceCoinWithPointIfApplicable(input string) string {
	// Skip replacement if current time is before GetFiCoinsToFiPointsMigrationTime()
	if time.Now().Before(GetFiCoinsToFiPointsMigrationTime()) {
		return input
	}

	return replaceCoinWithPoint(input)
}

// replaceCoinWithPoint replaces the word "coin"/"coins" (with case and plurality preserved)
// with "point"/"points" in the input string.
// Example:
// Input: "You earned 10 coins"
// Output: "You earned 10 points"
// Input: "You have 100 Fi-coins"
// Output: "You have 100 Fi-points"
// Input: "You have 100 Fi-coins"
// Output: "You have 100 Fi-points"
func replaceCoinWithPoint(input string) string {
	r := regexp.MustCompile(`(?i)\bcoin(s?)\b`)
	return r.ReplaceAllStringFunc(input, func(match string) string {
		var replacement string
		if strings.HasSuffix(strings.ToLower(match), "s") {
			replacement = "points"
		} else {
			replacement = "point"
		}

		if match == strings.ToUpper(match) {
			return strings.ToUpper(replacement)
		}

		if match == strings.ToLower(match) {
			return strings.ToLower(replacement)
		}

		runes := []rune(match)
		isTitleCase := unicode.IsUpper(runes[0])
		if len(runes) > 1 {
			for _, r := range runes[1:] {
				if !unicode.IsLower(r) {
					isTitleCase = false
					break
				}
			}
		}

		if isTitleCase {
			replacementRunes := []rune(replacement)
			replacementRunes[0] = unicode.ToUpper(replacementRunes[0])
			return string(replacementRunes)
		}

		return replacement
	})
}

// IsCCActiveUser checks if the user is a credit card active user.
func IsCCActiveUser(ctx context.Context, actorId string, fireflyClient fireflyPb.FireflyClient) (bool, error) {
	if actorId == "" {
		return false, fmt.Errorf("actorId is empty")
	}

	isCCUserRes, err := fireflyClient.IsCreditCardUser(ctx, &fireflyPb.IsCreditCardUserRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(isCCUserRes, err); rpcErr != nil {
		return false, rpcErr
	}

	return isCCUserRes.GetIsCreditCardUser(), nil
}

// ConvertFiCoinsToFiPointsForCreditTxn converts Fi-Coins to Fi-Points if applicable.
// 1. It checks if the current time is after the migration time for Fi-Coins to Fi-Points.
// 2. It checks if the action time (e.g. debitTime for fi store redemption or reward disbursal time) is before the migration time.
// 3. If both conditions are met, it converts basis user cc status and returns the converted amount.
func ConvertFiCoinsToFiPointsForCreditTxn(ctx context.Context, clock clockwork.Clock, creditAmount int32,
	actionTime *timestampPb.Timestamp, actorId string, fireflyClient fireflyPb.FireflyClient) (
	units int32, txnMetaData *accrualPb.TxnMetaData, err error) {

	fiCoinsToFiPointsMigrationTime := GetFiCoinsToFiPointsMigrationTime()
	if clock.Now().After(fiCoinsToFiPointsMigrationTime) && actionTime.AsTime().Before(fiCoinsToFiPointsMigrationTime) {
		isCCActiveUser, err := IsCCActiveUser(ctx, actorId, fireflyClient)
		if err != nil {
			return 0, nil, fmt.Errorf("error checking if user is a credit card user: %w", err)
		}
		txnMetaData = &accrualPb.TxnMetaData{
			CoinToPoints: &accrualPb.FiCoinsToFiPointsConversion{
				CoinsConverted: creditAmount,
				IsCcUser:       isCCActiveUser,
			},
		}
		return ConvertFiCoinsToFiPoints(creditAmount, isCCActiveUser), txnMetaData, nil
	}
	return creditAmount, nil, nil
}

// ConvertFiCoinsToFiPointsIfApplicable converts the given amount of Fi-Coins to Fi-Points if applicable at the given action time.
// If action time is nil, it defaults to the current time.
// If the action time is before the migration time, it will return input Fi-Coins amount without any conversion.
// If the action time is after the migration time, it will convert Fi-Coins to Fi-Points by calling ConvertFiCoinsToFiPoints
func ConvertFiCoinsToFiPointsIfApplicable(coins int32, isCCActiveUser bool, actionTime *timestampPb.Timestamp) int32 {
	if actionTime == nil {
		actionTime = timestampPb.Now()
	}

	// If the action time is before the migration time for Fi-Coins to Fi-Points, we return the input coins amount without any conversion.
	if actionTime.AsTime().Before(GetFiCoinsToFiPointsMigrationTime()) {
		return coins
	}

	return ConvertFiCoinsToFiPoints(coins, isCCActiveUser)
}

// ConvertFiCoinsToFiPoints converts the given amount of Fi-Coins to Fi-Points.
// The conversion rate is:
// 1. For cc users: 100 Fi-Coins = Rs 3 = 12 Fi-Points i.e. 1 Fi-Coin = 12/100 Fi-Points
// 2. For non-cc users:  100 Fi-Coins = Rs 2 = 8 Fi-Points i.e. 1 Fi-Coin = 8/100 Fi-Points
func ConvertFiCoinsToFiPoints(coin int32, isCCActiveUser bool) int32 {
	if isCCActiveUser {
		return int32(math.Ceil(float64(coin*12) / float64(100)))
	} else {
		return int32(math.Ceil(float64(coin*8) / float64(100)))
	}
}

// ReturnApplicableValue returns the applicable value according to the action time
// - If the action time is nil, it defaults to the current time.
// - If the action time is before the migration time for Fi-Coins to Fi-Points, it returns the pre value (Fi-Coins).
// - If the action time is after the migration time, it returns the post value (Fi-Points).
// - If shouldReplace is true, it will replace if other conditions are satisfied
// - If shouldReplace is false, it will not replace even if all conditions are satisfied
func ReturnApplicableValue(pre, post interface{}, actionTime *timestampPb.Timestamp, shouldReplace bool) interface{} {
	// If shouldReplace is false, we return the pre value without any checks.
	if !shouldReplace {
		return pre
	}

	// If action time is nil, we default to the current time.
	if actionTime == nil {
		actionTime = timestampPb.Now()
	}

	// Check if the action time is before the migration time for Fi-Coins to Fi-Points, we return the pre value.
	if actionTime.AsTime().Before(GetFiCoinsToFiPointsMigrationTime()) {
		return pre
	}

	// If the action time is after the migration time, we return the post value.
	return post
}
