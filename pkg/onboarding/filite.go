package onboarding

import (
	"context"
	"fmt"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epificontext"

	"github.com/pkg/errors"
	"github.com/samber/lo"

	"github.com/epifi/be-common/api/typesv2/common/ui/widget"

	"github.com/epifi/gamma/api/frontend/deeplink"
	onbDl "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/onboarding"
	"github.com/epifi/gamma/api/typesv2/ui"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

const (
	IconListItemColour     = "#E7ECF0"
	CarouselListItemColour = "#F0F3F7"
	WhiteColour            = "#FFFFFF"
	TransparentColour      = "#********"
)

type ImageTextComponent struct {
	ImageUrl  string
	ImageText string
}

type FeatureBenefitsScreenBuilder struct {
	screenOptions *onbDl.FeatureBenefitsScreenOptions
}

func NewFeatureBenefitsScreenBuilder(headerTitle string, entryPoint onbDl.FeatureOnboardingEntryPoint) *FeatureBenefitsScreenBuilder {
	return &FeatureBenefitsScreenBuilder{
		screenOptions: &onbDl.FeatureBenefitsScreenOptions{
			Sections: []*onbDl.Section{
				{
					Type: &onbDl.Section_SectionTypeHeader{
						SectionTypeHeader: &onbDl.SectionTypeHeader{
							Title: commontypes.GetTextFromStringFontColourFontStyle(headerTitle, CarouselListItemColour, commontypes.FontStyle_DISPLAY_XL),
						},
					},
				},
			},
			Ctas: []*deeplink.Cta{
				{
					Type: deeplink.Cta_CUSTOM,
					Text: "Continue",
					Deeplink: &deeplink.Deeplink{
						Screen: deeplink.Screen_GET_NEXT_ONBOARDING_ACTION_API,
						ScreenOptions: &deeplink.Deeplink_GetNextOnboardingActionScreenOptions{
							GetNextOnboardingActionScreenOptions: &deeplink.GetNextOnboardingActionScreenOptions{
								Title:                       "Let's start creating your savings account",
								Feature:                     onbPb.Feature_FEATURE_SA.String(),
								FeatureOnboardingEntryPoint: entryPoint.String(),
								BgColor:                     widget.GetBlockBackgroundColour("#18191B"),
								TitleV2:                     commontypes.GetTextFromStringFontColourFontStyle("Let's start creating your savings account", "#F6F9FD", commontypes.FontStyle_HEADLINE_L),
							},
						},
						ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&onbDl.GetNextOnbActionScreenOptions{
							Title:                       commontypes.GetTextFromStringFontColourFontStyle("Let's start creating your savings account", "#F6F9FD", commontypes.FontStyle_HEADLINE_L),
							Feature:                     onbPb.Feature_FEATURE_SA.String(),
							FeatureOnboardingEntryPoint: entryPoint.String(),
							BgColor:                     widget.GetBlockBackgroundColour("#18191B"),
							BottomInfoCards:             GenerateIconTextComponents(),
						}),
					},
					DisplayTheme: deeplink.Cta_PRIMARY,
				},
			},
			BgImage:                     commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/onboarding/lite/benefits_bg.png"),
			DividerColour:               widget.GetBlockBackgroundColour("#262728"),
			FeatureOnboardingEntryPoint: entryPoint.String(),
		},
	}
}

func NewFeatureBenefitsScreenBuilderV2(
	headerTitle string,
	entryPoint onbDl.FeatureOnboardingEntryPoint,
	ctaText string,
	needTncSection bool,
	appPlatform commontypes.Platform,
	appVersionCode uint32,
) *FeatureBenefitsScreenBuilder {
	var (
		tncText  *commontypes.Text
		sections []*onbDl.Section
		subTitle *ui.IconTextComponent
		ctaType  = deeplink.Cta_CUSTOM
	)

	// Initialize TNC section if needed
	if needTncSection {
		tncText = commontypes.GetTextFromHtmlStringFontColourFontStyle(
			"<a style='color: #00B899; text-decoration: none;' href=\"https://fi.money/blog/tnc\">"+
				"By proceeding you agree to open a Federal Bank Savings Account and agree to Federal Bank's T&C.</a>",
			"#00B899",
			commontypes.FontStyle_OVERLINE_2XS_CAPS,
		)
	}

	// Initialize sections slice with capacity
	sections = make([]*onbDl.Section, 0, 3)

	// Set default subtitle
	subTitle = &ui.IconTextComponent{
		Texts: []*commontypes.Text{
			commontypes.GetTextFromStringFontColourFontStyle(
				"POWERED BY",
				"#878A8D",
				commontypes.FontStyle_OVERLINE_2XS_CAPS,
			),
		},
		RightImgTxtPadding: 8,
		RightVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(
			"https://epifi-icons.pointz.in/onboarding/federal_wide.png",
			16,
			64,
		),
	}

	// add sections for common benefits with version checks
	if (appPlatform == commontypes.Platform_ANDROID && appVersionCode >= 439) || (appPlatform == commontypes.Platform_IOS && appVersionCode >= 602) {
		// Add Federal Bank logo section
		sections = append(sections, &onbDl.Section{
			Type: &onbDl.Section_SectionTypeItc{
				SectionTypeItc: &onbDl.SectionTypeIconTextComponent{
					SectionItc: &ui.IconTextComponent{
						RightVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(
							"https://epifi-icons.pointz.in/powered_by",
							40,
							80,
						),
					},
				},
			},
		})

		// Update subtitle for new version
		subTitle = &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle(
					"Trusted by 35L+ Indians",
					"#B2B5B9",
					commontypes.FontStyle_SUBTITLE_XS,
				),
			},
		}

		ctaType = deeplink.Cta_CONTINUE
	}

	// Add header section
	sections = append(sections, &onbDl.Section{
		Type: &onbDl.Section_SectionTypeHeader{
			SectionTypeHeader: &onbDl.SectionTypeHeader{
				Title:     commontypes.GetTextFromStringFontColourFontStyle(headerTitle, CarouselListItemColour, commontypes.FontStyle_DISPLAY_XL),
				PoweredBy: subTitle,
			},
		},
	})

	// Return the builder with all configurations
	return &FeatureBenefitsScreenBuilder{
		screenOptions: &onbDl.FeatureBenefitsScreenOptions{
			Sections: sections,
			Ctas: []*deeplink.Cta{
				{
					Type: ctaType,
					Text: ctaText,
					Deeplink: &deeplink.Deeplink{
						Screen: deeplink.Screen_GET_NEXT_ONBOARDING_ACTION_API,
						ScreenOptions: &deeplink.Deeplink_GetNextOnboardingActionScreenOptions{
							GetNextOnboardingActionScreenOptions: &deeplink.GetNextOnboardingActionScreenOptions{
								Title:                       "Let's start creating your savings account",
								Feature:                     onbPb.Feature_FEATURE_SA.String(),
								FeatureOnboardingEntryPoint: entryPoint.String(),
								BgColor:                     widget.GetBlockBackgroundColour("#18191B"),
								TitleV2:                     commontypes.GetTextFromStringFontColourFontStyle("Let's start creating your savings account", "#F6F9FD", commontypes.FontStyle_HEADLINE_L),
							},
						},
						ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&onbDl.GetNextOnbActionScreenOptions{
							Title:                       commontypes.GetTextFromStringFontColourFontStyle("Let's start creating your savings account", "#F6F9FD", commontypes.FontStyle_HEADLINE_L),
							Feature:                     onbPb.Feature_FEATURE_SA.String(),
							FeatureOnboardingEntryPoint: entryPoint.String(),
							BgColor:                     widget.GetBlockBackgroundColour("#18191B"),
							BottomInfoCards:             GenerateIconTextComponents(),
						}),
					},
					DisplayTheme: deeplink.Cta_PRIMARY,
				},
			},
			BgImage:                     commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/onboarding/lite/benefits_bg.png"),
			DividerColour:               widget.GetBlockBackgroundColour("#262728"),
			FeatureOnboardingEntryPoint: entryPoint.String(),
			TncText:                     tncText,
			BottomSectionBg: widget.GetLinearGradientBackgroundColour(0, []*widget.ColorStop{
				{Color: "#2F2F2F00", StopPercentage: 0},
				{Color: "#2B2B2BE8", StopPercentage: 50},
				{Color: "#282828", StopPercentage: 100},
			}),
		},
	}
}

func NewFeatureBenefitsScreenBuilderForUsStocks(headerTitle string, entryPoint onbDl.FeatureOnboardingEntryPoint, ctaText string) *FeatureBenefitsScreenBuilder {
	return &FeatureBenefitsScreenBuilder{
		screenOptions: &onbDl.FeatureBenefitsScreenOptions{
			Sections: []*onbDl.Section{
				{
					Type: &onbDl.Section_SectionTypeHeader{
						SectionTypeHeader: &onbDl.SectionTypeHeader{
							Title: commontypes.GetTextFromStringFontColourFontStyle(headerTitle, CarouselListItemColour, commontypes.FontStyle_DISPLAY_XL),
							PoweredBy: &ui.IconTextComponent{
								Texts: []*commontypes.Text{
									commontypes.GetTextFromStringFontColourFontStyle("Co-powered by Alpaca Securities LLC, US registered broker", "#929599", commontypes.FontStyle_OVERLINE_2XS_CAPS),
								},
								LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/feature_benefits/us_stocks/shield.png", 20, 20),
								LeftImgTxtPadding: 4,
							},
						},
					},
				},
			},
			Ctas: []*deeplink.Cta{
				{
					Type: deeplink.Cta_CUSTOM,
					Text: ctaText,
					Deeplink: &deeplink.Deeplink{
						Screen: deeplink.Screen_GET_NEXT_ONBOARDING_ACTION_API,
						ScreenOptions: &deeplink.Deeplink_GetNextOnboardingActionScreenOptions{
							GetNextOnboardingActionScreenOptions: &deeplink.GetNextOnboardingActionScreenOptions{
								Title:                       "Let's start creating your savings account",
								Feature:                     onbPb.Feature_FEATURE_SA.String(),
								FeatureOnboardingEntryPoint: entryPoint.String(),
								BgColor:                     widget.GetBlockBackgroundColour("#18191B"),
								TitleV2:                     commontypes.GetTextFromStringFontColourFontStyle("Let's start creating your savings account", "#F6F9FD", commontypes.FontStyle_HEADLINE_L),
							},
						},
						ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&onbDl.GetNextOnbActionScreenOptions{
							Title:                       commontypes.GetTextFromStringFontColourFontStyle("Let's start creating your savings account", "#F6F9FD", commontypes.FontStyle_HEADLINE_L),
							Feature:                     onbPb.Feature_FEATURE_SA.String(),
							FeatureOnboardingEntryPoint: entryPoint.String(),
							BgColor:                     widget.GetBlockBackgroundColour("#18191B"),
							BottomInfoCards:             GenerateIconTextComponents(),
						}),
					},
					DisplayTheme: deeplink.Cta_PRIMARY,
				},
			},
			BgImage:                     commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/onboarding/lite/benefits_bg.png"),
			DividerColour:               widget.GetBlockBackgroundColour("#262728"),
			FeatureOnboardingEntryPoint: entryPoint.String(),
			TncText:                     commontypes.GetTextFromStringFontColourFontStyle("By proceeding you agree to open a US Stocks account and agree to Alpaca Securities’ T&C.", "#929599", commontypes.FontStyle_OVERLINE_2XS_CAPS),
			BottomSectionBg: widget.GetLinearGradientBackgroundColour(0, []*widget.ColorStop{
				{
					Color:          "#2F2F2F00",
					StopPercentage: 0,
				},
				{
					Color:          "#2B2B2BE8",
					StopPercentage: 50,
				},
				{
					Color:          "#282828",
					StopPercentage: 100,
				},
			}),
		},
	}
}

// nolint: dupl
func (s *FeatureBenefitsScreenBuilder) AddBankingBenefitsSectionV2(sectionText string, listItems []*ImageTextComponent) {
	var benefitItems []*ui.IconTextComponent
	lo.ForEach(listItems, func(item *ImageTextComponent, _ int) {
		benefitItems = append(benefitItems, &ui.IconTextComponent{
			RightIcon: &commontypes.Image{
				ImageUrl: item.ImageUrl,
				Width:    64,
				Height:   64,
			},
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle(item.ImageText, WhiteColour, commontypes.FontStyle_SUBTITLE_S),
			},
			RightImgTxtPadding: 12,
		})
	})
	s.screenOptions.Sections = append(s.screenOptions.GetSections(), &onbDl.Section{
		Type: &onbDl.Section_SectionTypeList{
			SectionTypeList: &onbDl.SectionTypeList{
				SectionHeader:    commontypes.GetTextFromStringFontColourFontStyle(sectionText, WhiteColour, commontypes.FontStyle_HEADLINE_M),
				BenefitListItems: benefitItems,
				BgColor: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{
						BlockColour: "#262728",
					},
				},
			},
		},
	})
}

func GetSABenefitsScreenV2() (*deeplink.Deeplink, error) {
	builder := NewFeatureBenefitsScreenBuilderV2("Open a Savings Account \nto unlock benefits", onbDl.FeatureOnboardingEntryPoint_FEATURE_ONBOARDING_ENTRY_POINT_UNSPECIFIED, "Get a Savings Account", true, 0, 0)
	builder.AddCommonBenefitsSection([]*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/feature_benefits/sa/lighting.png",
			ImageText: "Takes less\nthan 3 min",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/feature_benefits/sa/no_bank_visits.png",
			ImageText: "No bank\nvisits",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/feature_benefits/sa/zero_balance.png",
			ImageText: "No minimum \nbalance",
		},
	})
	builder.AddProductSpecificBenefitsSection("Enjoy exciting benefits", []*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/feature_benefits/sa/debit_card.png",
			ImageText: "Get a physical Debit Card.\n0 hidden fees.",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/feature_benefits/sa/two_percent_cashback.png",
			ImageText: "Get 3% back on your Spends",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/feature_benefits/sa/rewards.png",
			ImageText: "Earn up to ₹1000 p.m.* as rewards",
		},
	})
	builder.AddBankingBenefitsSection([]*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/feature_benefits/sa/zero_forex.png",
			ImageText: "0% Forex markup",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/feature_benefits/sa/deposit.png",
			ImageText: "Get up to 7% returns p.a. on Deposits",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/feature_benefits/sa/chequebook.png",
			ImageText: "Free 10-leaf chequebook",
		},
	})
	builder.screenOptions.Sections = append(builder.screenOptions.GetSections(),
		&onbDl.Section{
			Type: &onbDl.Section_SectionTypeText{
				SectionTypeText: &onbDl.SectionTypeText{
					Text: &ui.IconTextComponent{
						Texts: []*commontypes.Text{
							commontypes.GetTextFromHtmlStringFontColourFontStyle("*T&C Apply. Valid on select Account Plans", "#929599", commontypes.FontStyle_BODY_S),
						},
					},
				},
			},
		},
	)
	return builder.GetDeeplink()
}
func GetDCBenefitsScreenV2() (*deeplink.Deeplink, error) {
	builder := NewFeatureBenefitsScreenBuilderV2("Save more with an international Debit Card", onbDl.FeatureOnboardingEntryPoint_FEATURE_ONBOARDING_ENTRY_POINT_UNSPECIFIED, "Get this Debit Card", true, 0, 0)
	builder.AddCommonBenefitsSection([]*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/feature_benefits/dc/flight.png",
			ImageText: "Great deals on flights!",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/feature_benefits/dc/tap_to_pay.png",
			ImageText: "Tap/Pay in 150 countries",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/feature_benefits/sa/zero_forex.png",
			ImageText: "No Forex charges",
		},
	})
	builder.AddProductSpecificBenefitsSection("Fi-Federal Debit Card Benefits", []*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/feature_benefits/sa/two_percent_cashback.png",
			ImageText: "2% back on all Domestic spends",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/feature_benefits/dc/cash.png",
			ImageText: "Save 3.5% on each international spend",
		},
	})
	builder.AddBankingBenefitsSectionV2("Save as you travel", []*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/feature_benefits/dc/visa.png",
			ImageText: "Accepted wherever VISA works",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/feature_benefits/dc/globe.png",
			ImageText: "100% safe for global use",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/feature_benefits/dc/atm.png",
			ImageText: "Nominal fee to use at any global ATM",
		},
	})
	builder.AddBankingBenefitsSectionV2("How to get this card", []*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/feature_benefits/dc/fi_logo.png",
			ImageText: "Step 1: Open a Savings Account via Fi",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/feature_benefits/sa/debit_card.png",
			ImageText: "Step 2: Order a physical Debit Card",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/feature_benefits/dc/calendar.png",
			ImageText: "Step 3: Card reaches you in 4-10 days*",
		},
	})
	return builder.GetDeeplink()
}

// nolint:funlen,dupl
func GetUsStocksBenefitsScreenV2() (*deeplink.Deeplink, error) {
	builder := NewFeatureBenefitsScreenBuilderForUsStocks("Invest in US Stocks from India", onbDl.FeatureOnboardingEntryPoint_FEATURE_ONBOARDING_ENTRY_POINT_UNSPECIFIED, "Explore US Stocks")
	builder.AddCommonBenefitsSection([]*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/feature_benefits/sa/lighting.png",
			ImageText: "Takes 4 mins to set-up!",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/feature_benefits/us_stocks/one_dollar.png",
			ImageText: "Start with just $1",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/feature_benefits/us_stocks/zero_3d.png",
			ImageText: "Zero penalty SIPs",
		},
	})
	builder.AddProductSpecificBenefitsSection("Added benefits", []*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/feature_benefits/us_stocks/cash_on_paper_plane.png",
			ImageText: "Automate your investments",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/feature_benefits/us_stocks/zero_3d.png",
			ImageText: "0 brokerage fees",
		},
	})
	builder.AddBankingBenefitsSectionV2("Opening a US Stocks account", []*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/feature_benefits/dc/fi_logo.png",
			ImageText: "Step 1: Open a Savings Account via Fi",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/feature_benefits/us_stocks/investment.png",
			ImageText: "Step 2: Open an Investments Account",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/feature_benefits/us_stocks/us_flag_badge.png",
			ImageText: "Step 3: Start exploring US Stocks",
		},
	})
	builder.screenOptions.Sections = append(builder.screenOptions.GetSections(),
		&onbDl.Section{
			Type: &onbDl.Section_SectionTypeText{
				SectionTypeText: &onbDl.SectionTypeText{
					Text: &ui.IconTextComponent{
						Texts: []*commontypes.Text{
							commontypes.GetTextFromHtmlStringFontColourFontStyle("Alpaca Securities LLC, an American FINRA-registered broker buys & holds stocks on your behalf", "#929599", commontypes.FontStyle_BODY_S),
						},
					},
				},
			},
		},
	)
	return builder.GetDeeplink()
}

func (s *FeatureBenefitsScreenBuilder) AddCommonBenefitsSection(listItems []*ImageTextComponent) {
	var benefitListItems []*ui.VerticalIconTextComponent
	lo.ForEach(listItems, func(item *ImageTextComponent, _ int) {
		benefitListItems = append(benefitListItems, &ui.VerticalIconTextComponent{
			TopIcon: &commontypes.Image{
				ImageUrl: item.ImageUrl,
				Width:    40,
				Height:   40,
			},
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle(item.ImageText, IconListItemColour, commontypes.FontStyle_SUBTITLE_XS),
			},
			TopImgTxtPadding: 12,
		})
	})
	s.screenOptions.Sections = append(s.screenOptions.GetSections(), &onbDl.Section{
		Type: &onbDl.Section_SectionTypeIcons{
			SectionTypeIcons: &onbDl.SectionTypeIcons{
				BenefitListItems:  benefitListItems,
				MaxElementsPerRow: 3,
				BgColor: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{
						BlockColour: TransparentColour,
					},
				},
			},
		},
	})

}

func (s *FeatureBenefitsScreenBuilder) AddProductSpecificBenefitsSection(titleStr string, listItems []*ImageTextComponent) {
	var benefitItems []*ui.VerticalIconTextComponent
	lo.ForEach(listItems, func(item *ImageTextComponent, _ int) {
		benefitItems = append(benefitItems, &ui.VerticalIconTextComponent{
			TopIcon: &commontypes.Image{
				ImageUrl: item.ImageUrl,
				Width:    86,
				Height:   86,
			},
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle(item.ImageText, CarouselListItemColour, commontypes.FontStyle_HEADLINE_L),
			},
			TopImgTxtPadding: 32,
		})
	})
	s.screenOptions.Sections = append(s.screenOptions.GetSections(), &onbDl.Section{
		Type: &onbDl.Section_SectionTypeCarousel{
			SectionTypeCarousel: &onbDl.SectionTypeCarousel{
				SectionTitle:     commontypes.GetTextFromStringFontColourFontStyle(titleStr, WhiteColour, commontypes.FontStyle_SUBTITLE_2),
				BenefitListItems: benefitItems,
				BgColor: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{
						BlockColour: "#262728",
					},
				},
			},
		},
	})
}

func (s *FeatureBenefitsScreenBuilder) AddTnCSection() {
	s.screenOptions.Sections = append(s.screenOptions.GetSections(), &onbDl.Section{
		Type: &onbDl.Section_SectionTypeText{
			SectionTypeText: &onbDl.SectionTypeText{
				Text: &ui.IconTextComponent{
					Texts: []*commontypes.Text{
						commontypes.GetTextFromStringFontColourFontStyle("More Fi Plan benefits*", WhiteColour, commontypes.FontStyle_HEADLINE_L),
					},
				},
			},
		},
	}, &onbDl.Section{
		Type: &onbDl.Section_SectionTypeText{
			SectionTypeText: &onbDl.SectionTypeText{
				Text: &ui.IconTextComponent{
					Texts: []*commontypes.Text{
						commontypes.GetTextFromHtmlStringFontColourFontStyle("You can find more details about Fi plans T&Cs <font color=#00B899><a href='https://fi.money/features/accounts#infinite'><font color=#00B899>here</font></a></font>", "#878A8D", commontypes.FontStyle_BODY_XS),
					},
				},
			},
		},
	})
}

// nolint:funlen,dupl
func (s *FeatureBenefitsScreenBuilder) AddInvestmentBenefitsSection(listItems []*ImageTextComponent) {
	var benefitItems []*ui.IconTextComponent
	lo.ForEach(listItems, func(item *ImageTextComponent, _ int) {
		benefitItems = append(benefitItems, &ui.IconTextComponent{
			RightIcon: &commontypes.Image{
				ImageUrl: item.ImageUrl,
				Width:    50,
				Height:   50,
			},
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle(item.ImageText, WhiteColour, commontypes.FontStyle_SUBTITLE_S),
			},
			RightImgTxtPadding: 12,
		})
	})
	s.screenOptions.Sections = append(s.screenOptions.GetSections(), &onbDl.Section{
		Type: &onbDl.Section_SectionTypeList{
			SectionTypeList: &onbDl.SectionTypeList{
				SectionHeader:    commontypes.GetTextFromStringFontColourFontStyle("Supercharged Investments", WhiteColour, commontypes.FontStyle_SUBTITLE_2),
				BenefitListItems: benefitItems,
				BgColor: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{
						BlockColour: "#262728",
					},
				},
			},
		},
	})
}

// nolint: dupl
func (s *FeatureBenefitsScreenBuilder) AddBankingBenefitsSection(listItems []*ImageTextComponent) {
	var benefitItems []*ui.IconTextComponent
	lo.ForEach(listItems, func(item *ImageTextComponent, _ int) {
		benefitItems = append(benefitItems, &ui.IconTextComponent{
			RightIcon: &commontypes.Image{
				ImageUrl: item.ImageUrl,
				Width:    64,
				Height:   64,
			},
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle(item.ImageText, WhiteColour, commontypes.FontStyle_SUBTITLE_S),
			},
			RightImgTxtPadding: 12,
		})
	})
	s.screenOptions.Sections = append(s.screenOptions.GetSections(), &onbDl.Section{
		Type: &onbDl.Section_SectionTypeList{
			SectionTypeList: &onbDl.SectionTypeList{
				SectionHeader:    commontypes.GetTextFromStringFontColourFontStyle("One account for all things money", WhiteColour, commontypes.FontStyle_HEADLINE_M),
				BenefitListItems: benefitItems,
				BgColor: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{
						BlockColour: "#262728",
					},
				},
			},
		},
	})
}

func (s *FeatureBenefitsScreenBuilder) GetDeeplink() (*deeplink.Deeplink, error) {
	return deeplinkv3.GetDeeplinkV3(deeplink.Screen_FEATURE_BENEFITS, s.screenOptions)
}

// GetJumpBenefitsScreen returns the deeplink for Fi lite landing screen along with benefits of jump
// https://www.figma.com/file/z14QLvmDFyFCAkJGnOypS6/%E2%9A%A1%EF%B8%8F-Fi-Lite?node-id=2125%3A100332
// nolint:funlen,dupl
func GetJumpBenefitsScreen() (*deeplink.Deeplink, error) {
	builder := NewFeatureBenefitsScreenBuilder("Upgrade to a Fi Plan to \ninvest in Jump", onbDl.FeatureOnboardingEntryPoint_FEATURE_ONBOARDING_ENTRY_POINT_JUMP)
	builder.AddCommonBenefitsSection([]*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/bank.png",
			ImageText: "Open a\nSavings A/c",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/filite/check.png",
			ImageText: "Check\neligibility",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/zero-fees.png",
			ImageText: "0 hidden\nfees",
		},
	})
	builder.AddProductSpecificBenefitsSection("Exclusive Jump Benefits", []*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/p2pinvestment/booster-badge.png",
			ImageText: "Get up to 10% returns p.a. with Jump",
		},
		{

			ImageUrl:  "https://epifi-icons.pointz.in/preapprovedloan/filite_interest_percent.png",
			ImageText: "Earn interest daily",
		},
		{

			ImageUrl:  "https://epifi-icons.pointz.in/p2pinvestment/verified_people.png",
			ImageText: "Diversified across 100+ credit-worthy borrowers",
		},
		{

			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/rupee.png",
			ImageText: "Automate Investing",
		},
	})
	builder.AddTnCSection()
	builder.AddInvestmentBenefitsSection([]*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/invest.png",
			ImageText: "Complete access to \nall Investment products",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/vault.png",
			ImageText: "Up to 7.25% returns \np.a. on Deposits",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/rupee.png",
			ImageText: "Automate savings & investing",
		},
	})
	builder.AddBankingBenefitsSection([]*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/npci_icon.png",
			ImageText: "Get 3% back on spends",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/fi_coins.png",
			ImageText: "4x Fi-Coins on spends. Redeem & win cash prizes",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/debit_card.png",
			ImageText: "Free physical Debit Card. No annual fee. No upfront fee!",
		},
	})
	return builder.GetDeeplink()
}

// GetMFBenefitsScreen returns the deeplink for Fi lite landing screen along with benefits of MF
// https://www.figma.com/file/z14QLvmDFyFCAkJGnOypS6/%E2%9A%A1%EF%B8%8F-Fi-Lite?type=design&node-id=4170-67051&t=mqEd6miC8C888hNi-0\
func GetMFBenefitsScreen() (*deeplink.Deeplink, error) {
	builder := NewFeatureBenefitsScreenBuilder("Upgrade to a Fi Plan to \ninvest in Mutual Funds", onbDl.FeatureOnboardingEntryPoint_FEATURE_ONBOARDING_ENTRY_POINT_MF)
	builder.AddCommonBenefitsSection([]*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/bank.png",
			ImageText: "Open a\nSavings A/c",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/check-phone-2.png",
			ImageText: "100%\ndigital",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/zero-fees.png",
			ImageText: "0 hidden\nfees",
		},
	})
	builder.AddProductSpecificBenefitsSection("Exclusive Benefits", []*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/mf-egg-basket.png",
			ImageText: "Up to 1.5% extra returns p.a. in Direct Mutual Funds",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/money_plane.png",
			ImageText: "Buy directly from \nyour savings account",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/rupee.png",
			ImageText: "Automate Investing",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/mf-sip-stacks.png",
			ImageText: "Start investing with just ₹10",
		},
	})
	builder.AddTnCSection()
	builder.AddInvestmentBenefitsSection([]*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/invest.png",
			ImageText: "Complete access to \nall Investment products",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/vault.png",
			ImageText: "Up to 7.25% returns \np.a. on Deposits",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/rupee.png",
			ImageText: "Automate savings & investing",
		},
	})
	builder.AddBankingBenefitsSection([]*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/npci_icon.png",
			ImageText: "Get 3% back on spends",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/fi_coins.png",
			ImageText: "4x Fi-Coins on spends. Redeem & win cash prizes",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/debit_card.png",
			ImageText: "Free physical Debit Card. No annual fee. No upfront fee!",
		},
	})
	return builder.GetDeeplink()
}

// GetSmartDepositsBenefitsScreen returns the Fi lite landing screen along with benefits of smart deposits
// maxInterestRate is the max interest rate that can be earned on smart deposits. e.g: "6.75"
func GetSmartDepositsBenefitsScreen(maxInterestRate string) (*deeplink.Deeplink, error) {
	builder := NewFeatureBenefitsScreenBuilder("Upgrade to a Fi Plan to \ncreate a Smart Deposit", onbDl.FeatureOnboardingEntryPoint_FEATURE_ONBOARDING_ENTRY_POINT_SD)
	builder.AddCommonBenefitsSection([]*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/bank.png",
			ImageText: "Open a\nSavings A/c",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/check-phone-2.png",
			ImageText: "100%\ndigital",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/zero-fees.png",
			ImageText: "0 hidden\nfees",
		},
	})
	builder.AddProductSpecificBenefitsSection("Exclusive Benefits", []*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/piggy_bank.png",
			ImageText: fmt.Sprintf("Get upto %s%% p.a. interest", maxInterestRate),
		},
		{

			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/SD_2.png",
			ImageText: "Top-up anytime & \nwithdraw instantly",
		},
		{

			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/filite/rupee-with-coins.png",
			ImageText: "Start as low as ₹300 and top up as low as ₹1",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/rupee.png",
			ImageText: "Automate Saving",
		},
	})
	builder.AddTnCSection()
	builder.AddInvestmentBenefitsSection([]*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/invest.png",
			ImageText: "Complete access to \nall Investment products",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/jump_coins.png",
			ImageText: "Earn up to 10% with Jump.",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/rupee.png",
			ImageText: "Automate savings & investing",
		},
	})
	builder.AddBankingBenefitsSection([]*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/npci_icon.png",
			ImageText: "Get 3% back on spends",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/fi_coins.png",
			ImageText: "4x Fi-Coins on spends. Redeem & win cash prizes",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/debit_card.png",
			ImageText: "Free physical Debit Card. No annual fee. No upfront fee!",
		},
	})
	return builder.GetDeeplink()
}

// GetFixedDepositsBenefitsScreen returns the screen options for Fixed Deposits Benefits screen
// maxInterestRate is the maximum interest rate that can be earned on a fixed deposit. e.g. "7.5"
func GetFixedDepositsBenefitsScreen(maxInterestRate string) (*deeplink.Deeplink, error) {
	builder := NewFeatureBenefitsScreenBuilder("Upgrade to a Fi Plan to \ncreate a Fixed Deposit", onbDl.FeatureOnboardingEntryPoint_FEATURE_ONBOARDING_ENTRY_POINT_FD)
	builder.AddCommonBenefitsSection([]*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/bank.png",
			ImageText: "Open a\nSavings A/c",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/check-phone-2.png",
			ImageText: "100%\ndigital",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/zero-fees.png",
			ImageText: "0 hidden\nfees",
		},
	})
	builder.AddProductSpecificBenefitsSection("Exclusive Benefits", []*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/FD_1.png",
			ImageText: "Get upto 7.25% p.a.",
		},
		{

			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/FD_2.png",
			ImageText: "One-time addition \n& fixed duration",
		},
		{

			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/FD_3.png",
			ImageText: "Good for your \nlong-term goals ",
		},
		{

			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/rupee.png",
			ImageText: "Automate Investing",
		},
	})
	builder.AddTnCSection()
	builder.AddInvestmentBenefitsSection([]*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/invest.png",
			ImageText: "Complete access to \nall Investment products",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/jump_coins.png",
			ImageText: "Earn up to 10% with Jump.",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/rupee.png",
			ImageText: "Automate savings & investing",
		},
	})
	builder.AddBankingBenefitsSection([]*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/npci_icon.png",
			ImageText: "Get 3% back on spends",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/fi_coins.png",
			ImageText: "4x Fi-Coins on spends. Redeem & win cash prizes",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/debit_card.png",
			ImageText: "Free physical Debit Card. No annual fee. No upfront fee!",
		},
	})
	return builder.GetDeeplink()
}

func GetUSStockBenefitsScreen() (*deeplink.Deeplink, error) {
	builder := NewFeatureBenefitsScreenBuilder("Upgrade to a Fi Plan to \ninvest in US Stocks", onbDl.FeatureOnboardingEntryPoint_FEATURE_ONBOARDING_ENTRY_POINT_USS)
	builder.AddCommonBenefitsSection([]*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/bank.png",
			ImageText: "Open a\nSavings A/c",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/check-phone-2.png",
			ImageText: "100%\ndigital",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/zero-fees.png",
			ImageText: "0 hidden\nfees",
		},
	})
	builder.AddProductSpecificBenefitsSection("Exclusive Benefits", []*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/lightning.png",
			ImageText: "Buy instantly,\nno delays",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/money_plane.png",
			ImageText: "Buy directly from \nyour savings account",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/rupee.png",
			ImageText: "Automate Investing",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/dollar_green_background.png",
			ImageText: "Invest in popular big tech companies",
		},
	})
	builder.AddTnCSection()
	builder.AddInvestmentBenefitsSection([]*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/invest.png",
			ImageText: "Complete access to \nall Investment products",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/vault.png",
			ImageText: "Up to 7.25% returns \np.a. on Deposits",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/rupee.png",
			ImageText: "Automate savings & investing",
		},
	})
	builder.AddBankingBenefitsSection([]*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/npci_icon.png",
			ImageText: "Get 3% back on spends",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/fi_coins.png",
			ImageText: "4x Fi-Coins on spends. Redeem & win cash prizes",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/debit_card.png",
			ImageText: "Free physical Debit Card. No annual fee. No upfront fee!",
		},
	})
	return builder.GetDeeplink()
}

// nolint:funlen,dupl,gosec
func GetSABenefitsScreen(ctx context.Context) (*deeplink.Deeplink, error) {
	appPlatform, appVersion := epificontext.AppPlatformAndVersion(ctx)
	return GetSABenefitsScreenV3(appPlatform, uint32(appVersion))
}

// nolint:funlen,dupl
func GetDCBenefitsScreen() (*deeplink.Deeplink, error) {
	screenOptions, err := deeplinkv3.GetScreenOptionV2(&onbDl.FeatureBenefitsScreenOptions{
		Sections: []*onbDl.Section{
			{
				Type: &onbDl.Section_SectionTypeHeader{
					SectionTypeHeader: &onbDl.SectionTypeHeader{
						Title: commontypes.GetTextFromStringFontColourFontStyle("The most rewarding savings\naccount is now yours", CarouselListItemColour, commontypes.FontStyle_HEADLINE_XL),
					},
				},
			},
			{
				Type: &onbDl.Section_SectionTypeIcons{
					SectionTypeIcons: &onbDl.SectionTypeIcons{
						BenefitListItems: []*ui.VerticalIconTextComponent{
							{
								TopIcon: &commontypes.Image{
									ImageUrl: "https://epifi-icons.pointz.in/onboarding/lite/lightning.png",
									Width:    40,
									Height:   40,
								},
								Texts: []*commontypes.Text{
									commontypes.GetTextFromStringFontColourFontStyle("Takes less\nthan 5 min", IconListItemColour, commontypes.FontStyle_SUBTITLE_S),
								},
								TopImgTxtPadding: 12,
							},
							{
								TopIcon: &commontypes.Image{
									ImageUrl: "https://epifi-icons.pointz.in/onboarding/lite/bank.png",
									Width:    40,
									Height:   40,
								},
								Texts: []*commontypes.Text{
									commontypes.GetTextFromStringFontColourFontStyle("No bank\nvisits", IconListItemColour, commontypes.FontStyle_SUBTITLE_S),
								},
								TopImgTxtPadding: 12,
							},
							{
								TopIcon: &commontypes.Image{
									ImageUrl: "https://epifi-icons.pointz.in/credit_card_images/3_percent_green.png",
									Width:    40,
									Height:   40,
								},
								Texts: []*commontypes.Text{
									commontypes.GetTextFromStringFontColourFontStyle("Up to 3% back\non all spends", IconListItemColour, commontypes.FontStyle_SUBTITLE_S),
								},
								TopImgTxtPadding: 12,
							},
						},
						MaxElementsPerRow: 3,
						BgColor:           widget.GetBlockBackgroundColour("#********"),
					},
				},
			},
			{
				Type: &onbDl.Section_SectionTypeCarousel{
					SectionTypeCarousel: &onbDl.SectionTypeCarousel{
						SectionTitle: commontypes.GetTextFromStringFontColourFontStyle("Enjoy Exciting Benefits", WhiteColour, commontypes.FontStyle_SUBTITLE_2),
						BenefitListItems: []*ui.VerticalIconTextComponent{
							{
								TopIcon: &commontypes.Image{
									ImageUrl: "https://epifi-icons.pointz.in/onboarding/lite/card.png",
									Width:    64,
									Height:   64,
								},
								Texts: []*commontypes.Text{
									commontypes.GetTextFromStringFontColourFontStyle("Free physical Debit Card. 0 hidden fees", CarouselListItemColour, commontypes.FontStyle_HEADLINE_L),
								},
								TopImgTxtPadding: 36,
							},
							{
								TopIcon: &commontypes.Image{
									ImageUrl: "https://epifi-icons.pointz.in/cards/automate-savings.png",
									Width:    78,
									Height:   78,
								},
								Texts: []*commontypes.Text{
									commontypes.GetTextFromStringFontColourFontStyle("Get 2% back on\nDebit Card spends", CarouselListItemColour, commontypes.FontStyle_HEADLINE_L),
								},
								TopImgTxtPadding: 36,
							},
							{
								TopIcon: &commontypes.Image{
									ImageUrl: "https://epifi-icons.pointz.in/cards/lock-with-coins.png",
									Width:    78,
									Height:   78,
								},
								Texts: []*commontypes.Text{
									commontypes.GetTextFromStringFontColourFontStyle("4x Fi-Coins on spends. Use to redeem & win cash prizes", CarouselListItemColour, commontypes.FontStyle_HEADLINE_L),
								},
								TopImgTxtPadding: 36,
							},
							{
								TopIcon: &commontypes.Image{
									ImageUrl: "https://epifi-icons.pointz.in/cards/insurance.png",
									Width:    78,
									Height:   78,
								},
								Texts: []*commontypes.Text{
									commontypes.GetTextFromStringFontColourFontStyle("Top-up your health insurance!", CarouselListItemColour, commontypes.FontStyle_HEADLINE_L),
								},
								TopImgTxtPadding: 36,
							},
						},
						BgColor: widget.GetBlockBackgroundColour("#262728"),
					},
				},
			},
			{
				Type: &onbDl.Section_SectionTypeText{
					SectionTypeText: &onbDl.SectionTypeText{
						Text: &ui.IconTextComponent{
							Texts: []*commontypes.Text{
								commontypes.GetTextFromStringFontColourFontStyle("More Fi Plan benefits*", WhiteColour, commontypes.FontStyle_HEADLINE_L),
							},
						},
					},
				},
			},
			{
				Type: &onbDl.Section_SectionTypeText{
					SectionTypeText: &onbDl.SectionTypeText{
						Text: &ui.IconTextComponent{
							Texts: []*commontypes.Text{
								commontypes.GetTextFromHtmlStringFontColourFontStyle("You can find more details about Fi plans T&Cs <a href='https://fi.money/features/accounts#infinite'><font color=#00B899>here</font></a>", "#878A8D", commontypes.FontStyle_BODY_XS),
							},
						},
					},
				},
			},
			{
				Type: &onbDl.Section_SectionTypeList{
					SectionTypeList: &onbDl.SectionTypeList{
						SectionHeader: commontypes.GetTextFromStringFontColourFontStyle("Supercharged Investments", WhiteColour, commontypes.FontStyle_SUBTITLE_2),
						BenefitListItems: []*ui.IconTextComponent{
							{
								RightIcon: &commontypes.Image{
									ImageUrl: "https://epifi-icons.pointz.in/onboarding/lite/invest.png",
									Width:    50,
									Height:   50,
								},
								Texts: []*commontypes.Text{
									commontypes.GetTextFromStringFontColourFontStyle("Full Access to all Invest products", WhiteColour, commontypes.FontStyle_SUBTITLE_S),
								},
								RightImgTxtPadding: 19,
							},
							{
								RightIcon: &commontypes.Image{
									ImageUrl: "https://epifi-icons.pointz.in/onboarding/lite/vault.png",
									Width:    50,
									Height:   50,
								},
								Texts: []*commontypes.Text{
									commontypes.GetTextFromStringFontColourFontStyle("Get up to 7% returns p.a. on Deposits ", WhiteColour, commontypes.FontStyle_SUBTITLE_S),
								},
								RightImgTxtPadding: 19,
							},
							{
								RightIcon: &commontypes.Image{
									ImageUrl: "https://epifi-icons.pointz.in/onboarding/lite/invest_up_arrow.png",
									Width:    50,
									Height:   50,
								},
								Texts: []*commontypes.Text{
									commontypes.GetTextFromStringFontColourFontStyle("Automate savings & investing", WhiteColour, commontypes.FontStyle_SUBTITLE_S),
								},
								RightImgTxtPadding: 17,
							},
						},
						BgColor: widget.GetBlockBackgroundColour("#262728"),
					},
				},
			},
			{
				Type: &onbDl.Section_SectionTypeList{
					SectionTypeList: &onbDl.SectionTypeList{
						SectionHeader: commontypes.GetTextFromStringFontColourFontStyle("Money Management Sorted", WhiteColour, commontypes.FontStyle_SUBTITLE_2),
						BenefitListItems: []*ui.IconTextComponent{
							{
								RightIcon: &commontypes.Image{
									ImageUrl: "https://epifi-icons.pointz.in/onboarding/lite/forex_world.png",
									Width:    56,
									Height:   56,
								},
								Texts: []*commontypes.Text{
									commontypes.GetTextFromStringFontColourFontStyle("0% Forex markup", WhiteColour, commontypes.FontStyle_SUBTITLE_S),
								},
								RightImgTxtPadding: 12,
							},
							{
								RightIcon: &commontypes.Image{
									ImageUrl: "https://epifi-icons.pointz.in/onboarding/lite_dc.png",
									Width:    56,
									Height:   56,
								},
								Texts: []*commontypes.Text{
									commontypes.GetTextFromStringFontColourFontStyle("Free physical Debit Card. \nNo hidden fees.", WhiteColour, commontypes.FontStyle_SUBTITLE_S),
								},
								RightImgTxtPadding: 12,
							},
							{
								RightIcon: &commontypes.Image{
									ImageUrl: "https://epifi-icons.pointz.in/onboarding/lite/chequebook.png",
									Width:    56,
									Height:   56,
								},
								Texts: []*commontypes.Text{
									commontypes.GetTextFromStringFontColourFontStyle("Free first 10-leaf chequebook", WhiteColour, commontypes.FontStyle_SUBTITLE_S),
								},
								RightImgTxtPadding: 10,
							},
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{
								BlockColour: "#********",
							},
						},
					},
				},
			},
		},
		Ctas: []*deeplink.Cta{
			{
				Type: deeplink.Cta_CUSTOM,
				Text: "Continue",
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_GET_NEXT_ONBOARDING_ACTION_API,
					ScreenOptions: &deeplink.Deeplink_GetNextOnboardingActionScreenOptions{
						GetNextOnboardingActionScreenOptions: &deeplink.GetNextOnboardingActionScreenOptions{
							Title:                       "Resuming savings account creation",
							Feature:                     onbPb.Feature_FEATURE_SA.String(),
							FeatureOnboardingEntryPoint: onbDl.FeatureOnboardingEntryPoint_FEATURE_ONBOARDING_ENTRY_POINT_DC.String(),
						},
					},
					ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&onbDl.GetNextOnbActionScreenOptions{
						Title:                       commontypes.GetTextFromStringFontColourFontStyle("Resuming savings account creation", "#F6F9FD", commontypes.FontStyle_HEADLINE_L),
						Feature:                     onbPb.Feature_FEATURE_SA.String(),
						BgColor:                     widget.GetBlockBackgroundColour("#18191B"),
						FeatureOnboardingEntryPoint: onbDl.FeatureOnboardingEntryPoint_FEATURE_ONBOARDING_ENTRY_POINT_DC.String(),
						BottomInfoCards:             GenerateIconTextComponents(),
					}),
				},
				DisplayTheme: deeplink.Cta_PRIMARY,
			},
		},
		BgImage:                     commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/onboarding/lite/benefits_bg.png"),
		DividerColour:               widget.GetBlockBackgroundColour("#262728"),
		FeatureOnboardingEntryPoint: onbDl.FeatureOnboardingEntryPoint_FEATURE_ONBOARDING_ENTRY_POINT_DC.String(),
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to get screen options for FeatureBenefitsScreenOptions")
	}
	return &deeplink.Deeplink{
		Screen:          deeplink.Screen_FEATURE_BENEFITS,
		ScreenOptionsV2: screenOptions,
	}, nil
}

// nolint:funlen,dupl
func GetCCBenefitsScreen() (*deeplink.Deeplink, error) {
	builder := NewFeatureBenefitsScreenBuilder("Upgrade to a Fi Plan to \napply for a credit card", onbDl.FeatureOnboardingEntryPoint_FEATURE_ONBOARDING_ENTRY_POINT_CC)
	builder.AddCommonBenefitsSection([]*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/bank.png",
			ImageText: "Open a\nSavings A/c",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/filite/check.png",
			ImageText: "Check\neligibility",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/5x_rewards.png",
			ImageText: "Get up to\n5X Rewards*",
		},
	})
	builder.AddProductSpecificBenefitsSection("Exclusive Card Benefits*", []*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/cards/welcome-benefits.png",
			ImageText: "Welcome benefits\n worth ₹5,000",
		},
		{

			ImageUrl:  "https://epifi-icons.pointz.in/cards/lounge-access.png",
			ImageText: "Lounge Access",
		},
		{

			ImageUrl:  "https://epifi-icons.pointz.in/cards/bell.png",
			ImageText: "Custom Reminders",
		},
		{

			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/rupee.png",
			ImageText: "Never miss a payment \nwith AutoPay",
		},
	})
	builder.AddTnCSection()
	builder.AddInvestmentBenefitsSection([]*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/invest.png",
			ImageText: "Complete access to \nall Investment products",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/vault.png",
			ImageText: "Up to 7.25% returns \np.a. on Deposits",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/rupee.png",
			ImageText: "Automate savings & investing",
		},
	})
	builder.AddBankingBenefitsSection([]*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/npci_icon.png",
			ImageText: "Get 3% back on spends",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/fi_coins.png",
			ImageText: "4x Fi-Coins on spends. Redeem & win cash prizes",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/debit_card.png",
			ImageText: "Free physical Debit Card. No annual fee. No upfront fee!",
		},
	})
	return builder.GetDeeplink()
}

// nolint:funlen,dupl
func GetAutoPayBenefitsScreen() (*deeplink.Deeplink, error) {
	builder := NewFeatureBenefitsScreenBuilder("Upgrade to a Fi Plan to \nAuto Pay on Fi", onbDl.FeatureOnboardingEntryPoint_FEATURE_ONBOARDING_ENTRY_POINT_AUTO_PAY)
	builder.AddCommonBenefitsSection([]*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/bank.png",
			ImageText: "Open a\nSavings A/c",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/check-phone-2.png",
			ImageText: "100%\ndigital",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/zero-fees.png",
			ImageText: "0 hidden\nfees",
		},
	})
	builder.AddProductSpecificBenefitsSection("Exclusive Auto Pay Benefits", []*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/auto-pay-arrow.png",
			ImageText: "Automate rent \npayments & EMIs",
		},
		{

			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/auto-pay-any-bank.png",
			ImageText: "Schedule payments\n to any bank",
		},
		{

			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/auto-pay-gift.png",
			ImageText: "Never forget to\nsend money home",
		},
	})
	builder.AddTnCSection()
	builder.AddInvestmentBenefitsSection([]*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/invest.png",
			ImageText: "Complete access to \nall Investment products",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/vault.png",
			ImageText: "Up to 7.25% returns \np.a. on Deposits",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/rupee.png",
			ImageText: "Automate savings & investing",
		},
	})
	builder.AddBankingBenefitsSection([]*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/forex_world.png",
			ImageText: "0% Forex markup",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite_dc.png",
			ImageText: "Free physical Debit Card. \nNo hidden fees.",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/chequebook.png",
			ImageText: "Free first 10-leaf chequebook",
		},
	})
	return builder.GetDeeplink()
}

// nolint:funlen,dupl
func GetPLBenefitsScreen() (*deeplink.Deeplink, error) {
	builder := NewFeatureBenefitsScreenBuilder("Upgrade to a Fi Plan\nto apply for a Loan", onbDl.FeatureOnboardingEntryPoint_FEATURE_ONBOARDING_ENTRY_POINT_PL)
	builder.AddCommonBenefitsSection([]*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/bank.png",
			ImageText: "Open a\nSavings A/c",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/filite/check.png",
			ImageText: "Check\neligibility",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/zero-fees.png",
			ImageText: "0 hidden\nfees",
		},
	})
	builder.AddProductSpecificBenefitsSection("Exclusive Loan Benefits", []*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/preapprovedloan/filite_money_bag.png",
			ImageText: "Get up to ₹5 lakh \nas a digital loan",
		},
		{

			ImageUrl:  "https://epifi-icons.pointz.in/preapprovedloan/filite_money.png",
			ImageText: "0 pre-closure fees",
		},
		{

			ImageUrl:  "https://epifi-icons.pointz.in/preapprovedloan/filite_interest_percent.png",
			ImageText: "Competitive \ninterest rates",
		},
	})
	builder.AddTnCSection()
	builder.AddInvestmentBenefitsSection([]*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/invest.png",
			ImageText: "Complete access to \nall Investment products",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/vault.png",
			ImageText: "Up to 7.25% returns \np.a. on Deposits",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/rupee.png",
			ImageText: "Automate savings & investing",
		},
	})
	builder.AddBankingBenefitsSection([]*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/npci_icon.png",
			ImageText: "Get 3% back on spends",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/fi_coins.png",
			ImageText: "4x Fi-Coins on spends. Redeem & win cash prizes",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/debit_card.png",
			ImageText: "Free physical Debit Card. No annual fee. No upfront fee!",
		},
	})
	return builder.GetDeeplink()
}

func GetOnboardingFailureScreenForPL() *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_DETAILED_ERROR_VIEW_SCREEN,
		ScreenOptions: &deeplink.Deeplink_DetailedErrorViewScreenOptions{
			DetailedErrorViewScreenOptions: &deeplink.DetailedErrorViewScreenOptions{
				Title:       "Instant Loan isn’t available for you at the moment.",
				Subtitle:    "Unfortunately, we are unable to offer you this feature at this time. You can come back and try again in 3 months.",
				ImageUrl:    "https://epifi-icons.pointz.in/onboarding/door_with_nails.png",
				HasFeedback: true,
				Ctas: []*deeplink.Cta{
					{
						Type: deeplink.Cta_CUSTOM,
						Text: "Know more",
						Deeplink: &deeplink.Deeplink{
							Screen: deeplink.Screen_GET_NEXT_ONBOARDING_ACTION_API,
							ScreenOptions: &deeplink.Deeplink_GetNextOnboardingActionScreenOptions{
								GetNextOnboardingActionScreenOptions: &deeplink.GetNextOnboardingActionScreenOptions{
									Feature: onbPb.Feature_FEATURE_SA.String(),
								},
							},
							ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&onbDl.GetNextOnbActionScreenOptions{
								Feature:         onbPb.Feature_FEATURE_SA.String(),
								BottomInfoCards: GenerateIconTextComponents(),
							}),
						},
						Status: deeplink.Cta_CTA_STATUS_ENABLED,
					},
				},
			},
		},
	}
}

// nolint: dupl
func GetOnboardingFailureScreenForCC() *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_DETAILED_ERROR_VIEW_SCREEN,
		ScreenOptions: &deeplink.Deeplink_DetailedErrorViewScreenOptions{
			DetailedErrorViewScreenOptions: &deeplink.DetailedErrorViewScreenOptions{
				Title:       "We are unable to offer you our credit card",
				Subtitle:    "Unfortunately, we are unable to offer you this feature at this time. You can come back and try again in 3 months.",
				ImageUrl:    "https://epifi-icons.pointz.in/onboarding/door_with_nails.png",
				HasFeedback: true,
				Ctas: []*deeplink.Cta{
					{
						Type: deeplink.Cta_CUSTOM,
						Text: "Know more",
						Deeplink: &deeplink.Deeplink{
							Screen: deeplink.Screen_GET_NEXT_ONBOARDING_ACTION_API,
							ScreenOptions: &deeplink.Deeplink_GetNextOnboardingActionScreenOptions{
								GetNextOnboardingActionScreenOptions: &deeplink.GetNextOnboardingActionScreenOptions{
									Feature: onbPb.Feature_FEATURE_SA.String(),
								},
							},
							ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&onbDl.GetNextOnbActionScreenOptions{
								Feature:         onbPb.Feature_FEATURE_SA.String(),
								BottomInfoCards: GenerateIconTextComponents(),
							}),
						},
						Status: deeplink.Cta_CTA_STATUS_ENABLED,
					},
				},
			},
		},
	}
}

// https://www.figma.com/file/z14QLvmDFyFCAkJGnOypS6/%E2%9A%A1%EF%B8%8F-Fi-Lite?type=design&node-id=3809-52040&mode=design&t=D38aQYPpcwWFfAuH-0
func GetOnboardingFailureScreenForInvest() *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_DETAILED_ERROR_VIEW_SCREEN,
		ScreenOptions: &deeplink.Deeplink_DetailedErrorViewScreenOptions{
			DetailedErrorViewScreenOptions: &deeplink.DetailedErrorViewScreenOptions{
				Title:       "Oops, you cannot invest on the Fi App right now.",
				Subtitle:    "Unfortunately, we are unable to offer you this feature at this time. You can come back and try again in 3 months.",
				ImageUrl:    "https://epifi-icons.pointz.in/onboarding/door_with_nails.png",
				HasFeedback: true,
				Ctas: []*deeplink.Cta{
					{
						Type: deeplink.Cta_CUSTOM,
						Text: "Know more",
						Deeplink: &deeplink.Deeplink{
							Screen: deeplink.Screen_GET_NEXT_ONBOARDING_ACTION_API,
							ScreenOptions: &deeplink.Deeplink_GetNextOnboardingActionScreenOptions{
								GetNextOnboardingActionScreenOptions: &deeplink.GetNextOnboardingActionScreenOptions{
									Feature: onbPb.Feature_FEATURE_SA.String(),
								},
							},
							ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&onbDl.GetNextOnbActionScreenOptions{
								Feature:         onbPb.Feature_FEATURE_SA.String(),
								BottomInfoCards: GenerateIconTextComponents(),
							}),
						},
						Status: deeplink.Cta_CTA_STATUS_ENABLED,
					},
				},
			},
		},
	}
}

// nolint:funlen,dupl
func GetSABenefitsScreenV3(appPlatform commontypes.Platform, appVersionCode uint32) (*deeplink.Deeplink, error) {
	builder := NewFeatureBenefitsScreenBuilderV2("The most rewarding \nsavings account is now yours!", onbDl.FeatureOnboardingEntryPoint_FEATURE_ONBOARDING_ENTRY_POINT_UNSPECIFIED, "Proceed", false, appPlatform, appVersionCode)
	builder.AddCommonBenefitsSection([]*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/sa-clock-icon.png",
			ImageText: "Open in less\n than 5 min",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/onboarding/lite/bank.png",
			ImageText: "No bank\nvisits",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/SA-reward-box.png",
			ImageText: "3% rewards\non all spends*",
		},
	})
	builder.AddBankingBenefitsSection([]*ImageTextComponent{
		{
			ImageUrl:  "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/federal-debit-card-icon.png",
			ImageText: "Zero Forex International Debit Card*",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/SA-US-Stocks.png",
			ImageText: "Invest in US Stocks, Mutual Funds, FDs",
		},
		{
			ImageUrl:  "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/SA-Auto-Save-Rules-icon.png",
			ImageText: "Set up AutoSave rules to save with every spend",
		},
	})

	builder.screenOptions.Sections = append(builder.screenOptions.GetSections(),
		&onbDl.Section{
			Type: &onbDl.Section_SectionTypeText{
				SectionTypeText: &onbDl.SectionTypeText{
					Text: &ui.IconTextComponent{
						Texts: []*commontypes.Text{
							commontypes.GetTextFromHtmlStringFontColourFontStyle("*ON <font color=#00B899><a href='https://fi.money/features/accounts#infinite'><font color=#00B899>SELECT</font></a></font> PLANS ONLY", "#878A8D", commontypes.FontStyle_SUBTITLE_2XS),
						},
					},
				},
			},
		},
	)

	return builder.GetDeeplink()
}
