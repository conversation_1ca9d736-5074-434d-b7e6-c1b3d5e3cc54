package upi

import (
	"context"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
	upiOnboardingEnumsPb "github.com/epifi/gamma/api/upi/onboarding/enums"
	mockUpiOnboarding "github.com/epifi/gamma/api/upi/onboarding/mocks"
	"github.com/epifi/be-common/pkg/epifierrors"
	storage "github.com/epifi/be-common/pkg/storage/v2"
)

func TestIsAccountInCoolOff(t *testing.T) {
	var (
		ctx          = context.Background()
		accountIdFix = "accId"
		timeNow      = time.Now()
	)
	type args struct {
		accountId string
		fromTime  *timestamppb.Timestamp
	}
	tests := []struct {
		name           string
		args           args
		setupFn        func(client *mockUpiOnboarding.MockUpiOnboardingClient)
		isAccInCoolOff bool
		latestUpiOnbAt *timestamppb.Timestamp
		wantErr        bool
		assertErr      func(err error) bool
	}{
		{
			name: "account in cool off",
			args: args{
				accountId: accountIdFix,
				fromTime:  timestamppb.New(timeNow),
			},
			setupFn: func(mc *mockUpiOnboarding.MockUpiOnboardingClient) {
				mc.EXPECT().GetLatestUpiOnboardingDetailForAccount(gomock.Any(), &upiOnboardingPb.GetLatestUpiOnboardingDetailForAccountRequest{
					AccountId:           accountIdFix,
					UpiOnboardingAction: upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_LINK,
					UpiOnboardingStatus: upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_SUCCESSFUL},
				).Return(&upiOnboardingPb.GetLatestUpiOnboardingDetailForAccountResponse{
					Status: rpc.StatusOk(),
					UpiOnboardingDetail: &upiOnboardingPb.UpiOnboardingDetail{
						AccountId: accountIdFix,
						UpdatedAt: timestamppb.New(timeNow.Add(1 * time.Hour)),
					},
				}, nil)
			},
			isAccInCoolOff: true,
			latestUpiOnbAt: timestamppb.New(timeNow.Add(1 * time.Hour)),
			wantErr:        false,
		},
		{
			name: "new acc linked, not in cool off",
			args: args{
				accountId: accountIdFix,
				fromTime:  timestamppb.New(timeNow),
			},
			setupFn: func(mc *mockUpiOnboarding.MockUpiOnboardingClient) {
				mc.EXPECT().GetLatestUpiOnboardingDetailForAccount(gomock.Any(), &upiOnboardingPb.GetLatestUpiOnboardingDetailForAccountRequest{
					AccountId:           accountIdFix,
					UpiOnboardingAction: upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_LINK,
					UpiOnboardingStatus: upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_SUCCESSFUL},
				).Return(&upiOnboardingPb.GetLatestUpiOnboardingDetailForAccountResponse{
					Status: rpc.StatusOk(),
					UpiOnboardingDetail: &upiOnboardingPb.UpiOnboardingDetail{
						AccountId: accountIdFix,
						UpdatedAt: timestamppb.New(timeNow.Add(-1 * time.Hour)),
					},
				}, nil)
			},
			isAccInCoolOff: false,
			latestUpiOnbAt: nil,
			wantErr:        false,
		},
		{
			name: "record not found, not in cool off",
			args: args{
				accountId: accountIdFix,
				fromTime:  timestamppb.New(timeNow),
			},
			setupFn: func(mc *mockUpiOnboarding.MockUpiOnboardingClient) {
				mc.EXPECT().GetLatestUpiOnboardingDetailForAccount(gomock.Any(), &upiOnboardingPb.GetLatestUpiOnboardingDetailForAccountRequest{
					AccountId:           accountIdFix,
					UpiOnboardingAction: upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_LINK,
					UpiOnboardingStatus: upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_SUCCESSFUL},
				).Return(&upiOnboardingPb.GetLatestUpiOnboardingDetailForAccountResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
			},
			isAccInCoolOff: false,
			latestUpiOnbAt: nil,
			wantErr:        false,
		},
		{
			name: "error, not in cool off",
			args: args{
				accountId: accountIdFix,
				fromTime:  timestamppb.New(timeNow),
			},
			setupFn: func(mc *mockUpiOnboarding.MockUpiOnboardingClient) {
				mc.EXPECT().GetLatestUpiOnboardingDetailForAccount(gomock.Any(), &upiOnboardingPb.GetLatestUpiOnboardingDetailForAccountRequest{
					AccountId:           accountIdFix,
					UpiOnboardingAction: upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_LINK,
					UpiOnboardingStatus: upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_SUCCESSFUL},
				).Return(&upiOnboardingPb.GetLatestUpiOnboardingDetailForAccountResponse{
					Status: rpc.StatusRecordNotFound(),
				}, epifierrors.ErrRecordNotFound)
			},
			isAccInCoolOff: false,
			latestUpiOnbAt: nil,
			wantErr:        true,
			assertErr:      storage.IsRecordNotFoundError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			mockUpiOnboardingClient := mockUpiOnboarding.NewMockUpiOnboardingClient(ctrl)
			if tt.setupFn != nil {
				tt.setupFn(mockUpiOnboardingClient)
			}
			isAccInCoolOff, latestUpiOnbAt, err := IsAccountInCoolOff(ctx, tt.args.accountId, tt.args.fromTime,
				mockUpiOnboardingClient)
			if (err != nil) != tt.wantErr {
				t.Errorf("IsAccountInCoolOff() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr {
				assert.True(t, tt.assertErr(err))
			}
			if isAccInCoolOff != tt.isAccInCoolOff {
				t.Errorf("IsAccountInCoolOff() gotIsAccInCoolOff = %v, want %v", isAccInCoolOff, tt.isAccInCoolOff)
			}
			if !reflect.DeepEqual(latestUpiOnbAt, tt.latestUpiOnbAt) {
				t.Errorf("IsAccountInCoolOff() gotLatestUpiOnbAt = %v, want %v", latestUpiOnbAt, tt.latestUpiOnbAt)
			}
		})
	}
}
