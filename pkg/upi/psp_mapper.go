package upi

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"
	typesPb "github.com/epifi/gamma/api/typesv2"
)

var (
	// map to contain psp handle suffix to enum. Example: For papajolly@fbl handle , it will contain map of "fbl" to enum type.
	pspHandleSuffixToPspHandleEnumMap = map[string]typesPb.PSP_Handle{
		"apl":         typesPb.PSP_Handle_PSP_APL,
		"yapl":        typesPb.PSP_Handle_PSP_YAPL,
		"rapl":        typesPb.PSP_Handle_PSP_RAPL,
		"abfspay":     typesPb.PSP_Handle_PSP_ABFSPAY,
		"axisb":       typesPb.PSP_Handle_PSP_AXISB,
		"okaxis":      typesPb.PSP_Handle_PSP_OKAXIS,
		"okhdfcbank":  typesPb.PSP_Handle_PSP_OKHDFCBANK,
		"okicici":     typesPb.PSP_Handle_PSP_OKICICI,
		"oksbi":       typesPb.PSP_Handle_PSP_OKSBI,
		"yesg":        typesPb.PSP_Handle_PSP_YESG,
		"jupiteraxis": typesPb.PSP_Handle_PSP_JUPITERAXIS,
		"ikwik":       typesPb.PSP_Handle_PSP_IKWIK,
		"ybl":         typesPb.PSP_Handle_PSP_YBL,
		"ibl":         typesPb.PSP_Handle_PSP_IBL,
		"axl":         typesPb.PSP_Handle_PSP_AXL,
		"pingpay":     typesPb.PSP_Handle_PSP_PINGPAY,
		"sliceaxis":   typesPb.PSP_Handle_PSP_SLICEAXIS,
		"tapicici":    typesPb.PSP_Handle_PSP_TAPICICI,
		"waicici":     typesPb.PSP_Handle_PSP_WAICICI,
		"waaxis":      typesPb.PSP_Handle_PSP_WAAXIS,
		"wahdfcbank":  typesPb.PSP_Handle_PSP_WAHDFCBANK,
		"wasbi":       typesPb.PSP_Handle_PSP_WASBI,
		"zoicici":     typesPb.PSP_Handle_PSP_ZOICICI,
		"paytm":       typesPb.PSP_Handle_PSP_PAYTM,
		"fbl":         typesPb.PSP_Handle_PSP_FBL,
		"fede":        typesPb.PSP_Handle_PSP_FEDERAL,
		"ptyes":       typesPb.PSP_Handle_PSP_PTYES,
		"ptaxis":      typesPb.PSP_Handle_PSP_PTAXIS,
		"pthdfc":      typesPb.PSP_Handle_PSP_PTHDFC,
		"ptsbi":       typesPb.PSP_Handle_PSP_PTSBI,
		"fifederal":   typesPb.PSP_Handle_PSP_FIFEDERAL,
	}

	// Map to contain PSP handle suffix associated with psp name. More than one handle suffix is issued for psp so
	// this may contain many-to-one relationship
	pspHandleToPspMap = map[typesPb.PSP_Handle]typesPb.PspName{
		typesPb.PSP_Handle_PSP_APL:         typesPb.PspName_PSP_NAME_AMAZON_PAY,
		typesPb.PSP_Handle_PSP_YAPL:        typesPb.PspName_PSP_NAME_AMAZON_PAY,
		typesPb.PSP_Handle_PSP_RAPL:        typesPb.PspName_PSP_NAME_AMAZON_PAY,
		typesPb.PSP_Handle_PSP_ABFSPAY:     typesPb.PspName_PSP_NAME_BAJAJFIN_SERV,
		typesPb.PSP_Handle_PSP_AXISB:       typesPb.PspName_PSP_NAME_CRED,
		typesPb.PSP_Handle_PSP_OKAXIS:      typesPb.PspName_PSP_NAME_GPAY,
		typesPb.PSP_Handle_PSP_OKHDFCBANK:  typesPb.PspName_PSP_NAME_GPAY,
		typesPb.PSP_Handle_PSP_OKICICI:     typesPb.PspName_PSP_NAME_GPAY,
		typesPb.PSP_Handle_PSP_OKSBI:       typesPb.PspName_PSP_NAME_GPAY,
		typesPb.PSP_Handle_PSP_YESG:        typesPb.PspName_PSP_NAME_GROWW,
		typesPb.PSP_Handle_PSP_JUPITERAXIS: typesPb.PspName_PSP_NAME_JUPITER_MONEY,
		typesPb.PSP_Handle_PSP_IKWIK:       typesPb.PspName_PSP_NAME_MOBIKWIK,
		typesPb.PSP_Handle_PSP_YBL:         typesPb.PspName_PSP_NAME_PHONE_PE,
		typesPb.PSP_Handle_PSP_IBL:         typesPb.PspName_PSP_NAME_PHONE_PE,
		typesPb.PSP_Handle_PSP_AXL:         typesPb.PspName_PSP_NAME_PHONE_PE,
		typesPb.PSP_Handle_PSP_PINGPAY:     typesPb.PspName_PSP_NAME_SAMSUNG_PAY,
		typesPb.PSP_Handle_PSP_SLICEAXIS:   typesPb.PspName_PSP_NAME_SLICE,
		typesPb.PSP_Handle_PSP_TAPICICI:    typesPb.PspName_PSP_NAME_TATA_NEU,
		typesPb.PSP_Handle_PSP_WAICICI:     typesPb.PspName_PSP_NAME_WHATSAPP,
		typesPb.PSP_Handle_PSP_WAAXIS:      typesPb.PspName_PSP_NAME_WHATSAPP,
		typesPb.PSP_Handle_PSP_WAHDFCBANK:  typesPb.PspName_PSP_NAME_WHATSAPP,
		typesPb.PSP_Handle_PSP_WASBI:       typesPb.PspName_PSP_NAME_WHATSAPP,
		typesPb.PSP_Handle_PSP_ZOICICI:     typesPb.PspName_PSP_NAME_ZOMATO,
		typesPb.PSP_Handle_PSP_PAYTM:       typesPb.PspName_PSP_NAME_PAYTM,
		typesPb.PSP_Handle_PSP_FBL:         typesPb.PspName_PSP_NAME_UNSPECIFIED,
		typesPb.PSP_Handle_PSP_FEDERAL:     typesPb.PspName_PSP_NAME_UNSPECIFIED,
		typesPb.PSP_Handle_PSP_UNSPECIFIED: typesPb.PspName_PSP_NAME_UNSPECIFIED,
		typesPb.PSP_Handle_PSP_PTYES:       typesPb.PspName_PSP_NAME_PAYTM,
		typesPb.PSP_Handle_PSP_PTAXIS:      typesPb.PspName_PSP_NAME_PAYTM,
		typesPb.PSP_Handle_PSP_PTHDFC:      typesPb.PspName_PSP_NAME_PAYTM,
		typesPb.PSP_Handle_PSP_PTSBI:       typesPb.PspName_PSP_NAME_PAYTM,
		typesPb.PSP_Handle_PSP_FIFEDERAL:   typesPb.PspName_PSP_NAME_FI_MONEY,
	}

	// Image of PSP against psp name.
	pspNameToImageMap = map[typesPb.PspName]string{
		typesPb.PspName_PSP_NAME_GPAY:          "https://epifi-icons.pointz.in/quick-link-icons/Gpay.png",
		typesPb.PspName_PSP_NAME_AMAZON_PAY:    "https://epifi-icons.pointz.in/quick-link-icons/AmazonPay.png",
		typesPb.PspName_PSP_NAME_BAJAJFIN_SERV: "https://epifi-icons.pointz.in/quick-link-icons/BajajFinserv.png",
		typesPb.PspName_PSP_NAME_CRED:          "https://epifi-icons.pointz.in/quick-link-icons/Cred.png",
		typesPb.PspName_PSP_NAME_GROWW:         "https://epifi-icons.pointz.in/quick-link-icons/Groww.png",
		typesPb.PspName_PSP_NAME_JUPITER_MONEY: "https://epifi-icons.pointz.in/quick-link-icons/Jupiter.png",
		typesPb.PspName_PSP_NAME_MOBIKWIK:      "https://epifi-icons.pointz.in/quick-link-icons/Mobikwik.png",
		typesPb.PspName_PSP_NAME_PHONE_PE:      "https://epifi-icons.pointz.in/quick-link-icons/PhonePe.png",
		typesPb.PspName_PSP_NAME_SAMSUNG_PAY:   "https://epifi-icons.pointz.in/quick-link-icons/SamsungPay.png",
		typesPb.PspName_PSP_NAME_SLICE:         "https://epifi-icons.pointz.in/quick-link-icons/Slice.png",
		typesPb.PspName_PSP_NAME_TATA_NEU:      "https://epifi-icons.pointz.in/quick-link-icons/TataNeu.png",
		typesPb.PspName_PSP_NAME_WHATSAPP:      "https://epifi-icons.pointz.in/quick-link-icons/WhatsApp.png",
		typesPb.PspName_PSP_NAME_PAYTM:         "https://epifi-icons.pointz.in/quick-link-icons/Paytm.png",
		typesPb.PspName_PSP_NAME_FI_MONEY:      "https://epifi-icons.pointz.in/quick-link-icons/Fi+Federal+Bank.png",
		typesPb.PspName_PSP_NAME_UNSPECIFIED:   "https://epifi-icons.pointz.in/bank/logo/default_logo.png",
	}
)

func GetPspImageForVpa(ctx context.Context, vpa string) (string, error) {

	pspNameHandleEnum, err := GetPspHandleStringToEnum(vpa)
	if err != nil {
		return "", err
	}

	pspName := GetPspName(ctx, pspNameHandleEnum)

	imageUrl, ok := pspNameToImageMap[pspName]
	if !ok {
		logger.Error(ctx, "Could not find imageUrl for pspName:", zap.String("pspName", pspName.String()))
	}
	return imageUrl, nil
}

func GetPspHandleStringToEnum(vpa string) (typesPb.PSP_Handle, error) {
	pspHandle, err := GetPspHandleFromVpa(vpa)
	if err != nil {
		return typesPb.PSP_Handle_PSP_UNSPECIFIED, err
	}

	pspHandleEnum, ok := pspHandleSuffixToPspHandleEnumMap[pspHandle]
	if !ok {
		pspHandleEnum = typesPb.PSP_Handle_PSP_UNSPECIFIED
	}
	return pspHandleEnum, nil
}

func GetPspName(ctx context.Context, pspHandle typesPb.PSP_Handle) typesPb.PspName {
	pspName, ok := pspHandleToPspMap[pspHandle]
	if !ok {
		logger.Error(ctx, "Could not find pspName for pspHandle:", zap.String(logger.PSP_HANDLE, pspHandle.String()))
	}
	return pspName
}
