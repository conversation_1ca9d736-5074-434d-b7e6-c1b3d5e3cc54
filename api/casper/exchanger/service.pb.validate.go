// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/casper/exchanger/service.proto

package exchanger

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	casper "github.com/epifi/gamma/api/casper"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = casper.TagName(0)
)

// Validate checks the field values on SubmitUserInputForChosenOptionRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *SubmitUserInputForChosenOptionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubmitUserInputForChosenOptionRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// SubmitUserInputForChosenOptionRequestMultiError, or nil if none found.
func (m *SubmitUserInputForChosenOptionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SubmitUserInputForChosenOptionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetActorId()); l < 4 || l > 100 {
		err := SubmitUserInputForChosenOptionRequestValidationError{
			field:  "ActorId",
			reason: "value length must be between 4 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetExchangerOrderId()); l < 4 || l > 100 {
		err := SubmitUserInputForChosenOptionRequestValidationError{
			field:  "ExchangerOrderId",
			reason: "value length must be between 4 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetShippingAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubmitUserInputForChosenOptionRequestValidationError{
					field:  "ShippingAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubmitUserInputForChosenOptionRequestValidationError{
					field:  "ShippingAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetShippingAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubmitUserInputForChosenOptionRequestValidationError{
				field:  "ShippingAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SubmitUserInputForChosenOptionRequestMultiError(errors)
	}

	return nil
}

// SubmitUserInputForChosenOptionRequestMultiError is an error wrapping
// multiple validation errors returned by
// SubmitUserInputForChosenOptionRequest.ValidateAll() if the designated
// constraints aren't met.
type SubmitUserInputForChosenOptionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubmitUserInputForChosenOptionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubmitUserInputForChosenOptionRequestMultiError) AllErrors() []error { return m }

// SubmitUserInputForChosenOptionRequestValidationError is the validation error
// returned by SubmitUserInputForChosenOptionRequest.Validate if the
// designated constraints aren't met.
type SubmitUserInputForChosenOptionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubmitUserInputForChosenOptionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubmitUserInputForChosenOptionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubmitUserInputForChosenOptionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubmitUserInputForChosenOptionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubmitUserInputForChosenOptionRequestValidationError) ErrorName() string {
	return "SubmitUserInputForChosenOptionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SubmitUserInputForChosenOptionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubmitUserInputForChosenOptionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubmitUserInputForChosenOptionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubmitUserInputForChosenOptionRequestValidationError{}

// Validate checks the field values on SubmitUserInputForChosenOptionResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *SubmitUserInputForChosenOptionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// SubmitUserInputForChosenOptionResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// SubmitUserInputForChosenOptionResponseMultiError, or nil if none found.
func (m *SubmitUserInputForChosenOptionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SubmitUserInputForChosenOptionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubmitUserInputForChosenOptionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubmitUserInputForChosenOptionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubmitUserInputForChosenOptionResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExchangerOfferOrder()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubmitUserInputForChosenOptionResponseValidationError{
					field:  "ExchangerOfferOrder",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubmitUserInputForChosenOptionResponseValidationError{
					field:  "ExchangerOfferOrder",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExchangerOfferOrder()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubmitUserInputForChosenOptionResponseValidationError{
				field:  "ExchangerOfferOrder",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SubmitUserInputForChosenOptionResponseMultiError(errors)
	}

	return nil
}

// SubmitUserInputForChosenOptionResponseMultiError is an error wrapping
// multiple validation errors returned by
// SubmitUserInputForChosenOptionResponse.ValidateAll() if the designated
// constraints aren't met.
type SubmitUserInputForChosenOptionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubmitUserInputForChosenOptionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubmitUserInputForChosenOptionResponseMultiError) AllErrors() []error { return m }

// SubmitUserInputForChosenOptionResponseValidationError is the validation
// error returned by SubmitUserInputForChosenOptionResponse.Validate if the
// designated constraints aren't met.
type SubmitUserInputForChosenOptionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubmitUserInputForChosenOptionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubmitUserInputForChosenOptionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubmitUserInputForChosenOptionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubmitUserInputForChosenOptionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubmitUserInputForChosenOptionResponseValidationError) ErrorName() string {
	return "SubmitUserInputForChosenOptionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SubmitUserInputForChosenOptionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubmitUserInputForChosenOptionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubmitUserInputForChosenOptionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubmitUserInputForChosenOptionResponseValidationError{}

// Validate checks the field values on CreateExchangerOfferInventoryRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CreateExchangerOfferInventoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateExchangerOfferInventoryRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreateExchangerOfferInventoryRequestMultiError, or nil if none found.
func (m *CreateExchangerOfferInventoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateExchangerOfferInventoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Description

	// no validation rules for RewardType

	// no validation rules for TotalCount

	// no validation rules for PerUserLimit

	if len(errors) > 0 {
		return CreateExchangerOfferInventoryRequestMultiError(errors)
	}

	return nil
}

// CreateExchangerOfferInventoryRequestMultiError is an error wrapping multiple
// validation errors returned by
// CreateExchangerOfferInventoryRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateExchangerOfferInventoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateExchangerOfferInventoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateExchangerOfferInventoryRequestMultiError) AllErrors() []error { return m }

// CreateExchangerOfferInventoryRequestValidationError is the validation error
// returned by CreateExchangerOfferInventoryRequest.Validate if the designated
// constraints aren't met.
type CreateExchangerOfferInventoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateExchangerOfferInventoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateExchangerOfferInventoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateExchangerOfferInventoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateExchangerOfferInventoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateExchangerOfferInventoryRequestValidationError) ErrorName() string {
	return "CreateExchangerOfferInventoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateExchangerOfferInventoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateExchangerOfferInventoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateExchangerOfferInventoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateExchangerOfferInventoryRequestValidationError{}

// Validate checks the field values on CreateExchangerOfferInventoryResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CreateExchangerOfferInventoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateExchangerOfferInventoryResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreateExchangerOfferInventoryResponseMultiError, or nil if none found.
func (m *CreateExchangerOfferInventoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateExchangerOfferInventoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateExchangerOfferInventoryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateExchangerOfferInventoryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateExchangerOfferInventoryResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExchangerOfferInventory()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateExchangerOfferInventoryResponseValidationError{
					field:  "ExchangerOfferInventory",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateExchangerOfferInventoryResponseValidationError{
					field:  "ExchangerOfferInventory",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExchangerOfferInventory()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateExchangerOfferInventoryResponseValidationError{
				field:  "ExchangerOfferInventory",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateExchangerOfferInventoryResponseMultiError(errors)
	}

	return nil
}

// CreateExchangerOfferInventoryResponseMultiError is an error wrapping
// multiple validation errors returned by
// CreateExchangerOfferInventoryResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateExchangerOfferInventoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateExchangerOfferInventoryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateExchangerOfferInventoryResponseMultiError) AllErrors() []error { return m }

// CreateExchangerOfferInventoryResponseValidationError is the validation error
// returned by CreateExchangerOfferInventoryResponse.Validate if the
// designated constraints aren't met.
type CreateExchangerOfferInventoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateExchangerOfferInventoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateExchangerOfferInventoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateExchangerOfferInventoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateExchangerOfferInventoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateExchangerOfferInventoryResponseValidationError) ErrorName() string {
	return "CreateExchangerOfferInventoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateExchangerOfferInventoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateExchangerOfferInventoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateExchangerOfferInventoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateExchangerOfferInventoryResponseValidationError{}

// Validate checks the field values on GetExchangerOfferGroupsByIdsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetExchangerOfferGroupsByIdsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetExchangerOfferGroupsByIdsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetExchangerOfferGroupsByIdsRequestMultiError, or nil if none found.
func (m *GetExchangerOfferGroupsByIdsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExchangerOfferGroupsByIdsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetExchangerOfferGroupsByIdsRequestMultiError(errors)
	}

	return nil
}

// GetExchangerOfferGroupsByIdsRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetExchangerOfferGroupsByIdsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetExchangerOfferGroupsByIdsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExchangerOfferGroupsByIdsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExchangerOfferGroupsByIdsRequestMultiError) AllErrors() []error { return m }

// GetExchangerOfferGroupsByIdsRequestValidationError is the validation error
// returned by GetExchangerOfferGroupsByIdsRequest.Validate if the designated
// constraints aren't met.
type GetExchangerOfferGroupsByIdsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExchangerOfferGroupsByIdsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExchangerOfferGroupsByIdsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExchangerOfferGroupsByIdsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExchangerOfferGroupsByIdsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExchangerOfferGroupsByIdsRequestValidationError) ErrorName() string {
	return "GetExchangerOfferGroupsByIdsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetExchangerOfferGroupsByIdsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExchangerOfferGroupsByIdsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExchangerOfferGroupsByIdsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExchangerOfferGroupsByIdsRequestValidationError{}

// Validate checks the field values on GetExchangerOfferGroupsByIdsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetExchangerOfferGroupsByIdsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetExchangerOfferGroupsByIdsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetExchangerOfferGroupsByIdsResponseMultiError, or nil if none found.
func (m *GetExchangerOfferGroupsByIdsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExchangerOfferGroupsByIdsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOfferGroupsByIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOfferGroupsByIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOfferGroupsByIdsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetGroupIdToGroupMap()))
		i := 0
		for key := range m.GetGroupIdToGroupMap() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetGroupIdToGroupMap()[key]
			_ = val

			// no validation rules for GroupIdToGroupMap[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetExchangerOfferGroupsByIdsResponseValidationError{
							field:  fmt.Sprintf("GroupIdToGroupMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetExchangerOfferGroupsByIdsResponseValidationError{
							field:  fmt.Sprintf("GroupIdToGroupMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetExchangerOfferGroupsByIdsResponseValidationError{
						field:  fmt.Sprintf("GroupIdToGroupMap[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return GetExchangerOfferGroupsByIdsResponseMultiError(errors)
	}

	return nil
}

// GetExchangerOfferGroupsByIdsResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetExchangerOfferGroupsByIdsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetExchangerOfferGroupsByIdsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExchangerOfferGroupsByIdsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExchangerOfferGroupsByIdsResponseMultiError) AllErrors() []error { return m }

// GetExchangerOfferGroupsByIdsResponseValidationError is the validation error
// returned by GetExchangerOfferGroupsByIdsResponse.Validate if the designated
// constraints aren't met.
type GetExchangerOfferGroupsByIdsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExchangerOfferGroupsByIdsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExchangerOfferGroupsByIdsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExchangerOfferGroupsByIdsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExchangerOfferGroupsByIdsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExchangerOfferGroupsByIdsResponseValidationError) ErrorName() string {
	return "GetExchangerOfferGroupsByIdsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetExchangerOfferGroupsByIdsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExchangerOfferGroupsByIdsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExchangerOfferGroupsByIdsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExchangerOfferGroupsByIdsResponseValidationError{}

// Validate checks the field values on
// GetEOGroupsRewardUnitsActorUtilisationRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetEOGroupsRewardUnitsActorUtilisationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetEOGroupsRewardUnitsActorUtilisationRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetEOGroupsRewardUnitsActorUtilisationRequestMultiError, or nil if none found.
func (m *GetEOGroupsRewardUnitsActorUtilisationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEOGroupsRewardUnitsActorUtilisationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetEOGroupsRewardUnitsActorUtilisationRequestMultiError(errors)
	}

	return nil
}

// GetEOGroupsRewardUnitsActorUtilisationRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetEOGroupsRewardUnitsActorUtilisationRequest.ValidateAll() if the
// designated constraints aren't met.
type GetEOGroupsRewardUnitsActorUtilisationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEOGroupsRewardUnitsActorUtilisationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEOGroupsRewardUnitsActorUtilisationRequestMultiError) AllErrors() []error { return m }

// GetEOGroupsRewardUnitsActorUtilisationRequestValidationError is the
// validation error returned by
// GetEOGroupsRewardUnitsActorUtilisationRequest.Validate if the designated
// constraints aren't met.
type GetEOGroupsRewardUnitsActorUtilisationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEOGroupsRewardUnitsActorUtilisationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEOGroupsRewardUnitsActorUtilisationRequestValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetEOGroupsRewardUnitsActorUtilisationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEOGroupsRewardUnitsActorUtilisationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEOGroupsRewardUnitsActorUtilisationRequestValidationError) ErrorName() string {
	return "GetEOGroupsRewardUnitsActorUtilisationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetEOGroupsRewardUnitsActorUtilisationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEOGroupsRewardUnitsActorUtilisationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEOGroupsRewardUnitsActorUtilisationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEOGroupsRewardUnitsActorUtilisationRequestValidationError{}

// Validate checks the field values on
// GetEOGroupsRewardUnitsActorUtilisationResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetEOGroupsRewardUnitsActorUtilisationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetEOGroupsRewardUnitsActorUtilisationResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetEOGroupsRewardUnitsActorUtilisationResponseMultiError, or nil if none found.
func (m *GetEOGroupsRewardUnitsActorUtilisationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEOGroupsRewardUnitsActorUtilisationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEOGroupsRewardUnitsActorUtilisationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEOGroupsRewardUnitsActorUtilisationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEOGroupsRewardUnitsActorUtilisationResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetGroupIdToUtilisationMap()))
		i := 0
		for key := range m.GetGroupIdToUtilisationMap() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetGroupIdToUtilisationMap()[key]
			_ = val

			// no validation rules for GroupIdToUtilisationMap[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetEOGroupsRewardUnitsActorUtilisationResponseValidationError{
							field:  fmt.Sprintf("GroupIdToUtilisationMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetEOGroupsRewardUnitsActorUtilisationResponseValidationError{
							field:  fmt.Sprintf("GroupIdToUtilisationMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetEOGroupsRewardUnitsActorUtilisationResponseValidationError{
						field:  fmt.Sprintf("GroupIdToUtilisationMap[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return GetEOGroupsRewardUnitsActorUtilisationResponseMultiError(errors)
	}

	return nil
}

// GetEOGroupsRewardUnitsActorUtilisationResponseMultiError is an error
// wrapping multiple validation errors returned by
// GetEOGroupsRewardUnitsActorUtilisationResponse.ValidateAll() if the
// designated constraints aren't met.
type GetEOGroupsRewardUnitsActorUtilisationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEOGroupsRewardUnitsActorUtilisationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEOGroupsRewardUnitsActorUtilisationResponseMultiError) AllErrors() []error { return m }

// GetEOGroupsRewardUnitsActorUtilisationResponseValidationError is the
// validation error returned by
// GetEOGroupsRewardUnitsActorUtilisationResponse.Validate if the designated
// constraints aren't met.
type GetEOGroupsRewardUnitsActorUtilisationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEOGroupsRewardUnitsActorUtilisationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEOGroupsRewardUnitsActorUtilisationResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetEOGroupsRewardUnitsActorUtilisationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEOGroupsRewardUnitsActorUtilisationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEOGroupsRewardUnitsActorUtilisationResponseValidationError) ErrorName() string {
	return "GetEOGroupsRewardUnitsActorUtilisationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetEOGroupsRewardUnitsActorUtilisationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEOGroupsRewardUnitsActorUtilisationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEOGroupsRewardUnitsActorUtilisationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEOGroupsRewardUnitsActorUtilisationResponseValidationError{}

// Validate checks the field values on GetExchangerOffersOrdersSummaryRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetExchangerOffersOrdersSummaryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetExchangerOffersOrdersSummaryRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetExchangerOffersOrdersSummaryRequestMultiError, or nil if none found.
func (m *GetExchangerOffersOrdersSummaryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExchangerOffersOrdersSummaryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetActorId()); l < 4 || l > 100 {
		err := GetExchangerOffersOrdersSummaryRequestValidationError{
			field:  "ActorId",
			reason: "value length must be between 4 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetFilters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOffersOrdersSummaryRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOffersOrdersSummaryRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOffersOrdersSummaryRequestValidationError{
				field:  "Filters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetExchangerOffersOrdersSummaryRequestMultiError(errors)
	}

	return nil
}

// GetExchangerOffersOrdersSummaryRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetExchangerOffersOrdersSummaryRequest.ValidateAll() if the designated
// constraints aren't met.
type GetExchangerOffersOrdersSummaryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExchangerOffersOrdersSummaryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExchangerOffersOrdersSummaryRequestMultiError) AllErrors() []error { return m }

// GetExchangerOffersOrdersSummaryRequestValidationError is the validation
// error returned by GetExchangerOffersOrdersSummaryRequest.Validate if the
// designated constraints aren't met.
type GetExchangerOffersOrdersSummaryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExchangerOffersOrdersSummaryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExchangerOffersOrdersSummaryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExchangerOffersOrdersSummaryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExchangerOffersOrdersSummaryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExchangerOffersOrdersSummaryRequestValidationError) ErrorName() string {
	return "GetExchangerOffersOrdersSummaryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetExchangerOffersOrdersSummaryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExchangerOffersOrdersSummaryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExchangerOffersOrdersSummaryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExchangerOffersOrdersSummaryRequestValidationError{}

// Validate checks the field values on GetExchangerOffersOrdersSummaryResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetExchangerOffersOrdersSummaryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetExchangerOffersOrdersSummaryResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetExchangerOffersOrdersSummaryResponseMultiError, or nil if none found.
func (m *GetExchangerOffersOrdersSummaryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExchangerOffersOrdersSummaryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOffersOrdersSummaryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOffersOrdersSummaryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOffersOrdersSummaryResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCashEarned()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOffersOrdersSummaryResponseValidationError{
					field:  "CashEarned",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOffersOrdersSummaryResponseValidationError{
					field:  "CashEarned",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCashEarned()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOffersOrdersSummaryResponseValidationError{
				field:  "CashEarned",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FiCoinsEarned

	if all {
		switch v := interface{}(m.GetInProcessCashRewardAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOffersOrdersSummaryResponseValidationError{
					field:  "InProcessCashRewardAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOffersOrdersSummaryResponseValidationError{
					field:  "InProcessCashRewardAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInProcessCashRewardAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOffersOrdersSummaryResponseValidationError{
				field:  "InProcessCashRewardAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for InProcessFiCoins

	// no validation rules for CashRewardCount

	// no validation rules for FiCoinsRewardCount

	if len(errors) > 0 {
		return GetExchangerOffersOrdersSummaryResponseMultiError(errors)
	}

	return nil
}

// GetExchangerOffersOrdersSummaryResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetExchangerOffersOrdersSummaryResponse.ValidateAll() if the designated
// constraints aren't met.
type GetExchangerOffersOrdersSummaryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExchangerOffersOrdersSummaryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExchangerOffersOrdersSummaryResponseMultiError) AllErrors() []error { return m }

// GetExchangerOffersOrdersSummaryResponseValidationError is the validation
// error returned by GetExchangerOffersOrdersSummaryResponse.Validate if the
// designated constraints aren't met.
type GetExchangerOffersOrdersSummaryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExchangerOffersOrdersSummaryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExchangerOffersOrdersSummaryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExchangerOffersOrdersSummaryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExchangerOffersOrdersSummaryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExchangerOffersOrdersSummaryResponseValidationError) ErrorName() string {
	return "GetExchangerOffersOrdersSummaryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetExchangerOffersOrdersSummaryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExchangerOffersOrdersSummaryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExchangerOffersOrdersSummaryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExchangerOffersOrdersSummaryResponseValidationError{}

// Validate checks the field values on CreateExchangerOfferRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateExchangerOfferRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateExchangerOfferRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateExchangerOfferRequestMultiError, or nil if none found.
func (m *CreateExchangerOfferRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateExchangerOfferRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _CreateExchangerOfferRequest_RedemptionCurrency_NotInLookup[m.GetRedemptionCurrency()]; ok {
		err := CreateExchangerOfferRequestValidationError{
			field:  "RedemptionCurrency",
			reason: "value must not be in list [EXCHANGER_OFFER_REDEMPTION_CURRENCY_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for RedemptionPrice

	if m.GetOfferDisplayDetails() == nil {
		err := CreateExchangerOfferRequestValidationError{
			field:  "OfferDisplayDetails",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetOfferDisplayDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateExchangerOfferRequestValidationError{
					field:  "OfferDisplayDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateExchangerOfferRequestValidationError{
					field:  "OfferDisplayDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOfferDisplayDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateExchangerOfferRequestValidationError{
				field:  "OfferDisplayDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetOfferOptionsConfig() == nil {
		err := CreateExchangerOfferRequestValidationError{
			field:  "OfferOptionsConfig",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetOfferOptionsConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateExchangerOfferRequestValidationError{
					field:  "OfferOptionsConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateExchangerOfferRequestValidationError{
					field:  "OfferOptionsConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOfferOptionsConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateExchangerOfferRequestValidationError{
				field:  "OfferOptionsConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetOfferAggregatesConfig() == nil {
		err := CreateExchangerOfferRequestValidationError{
			field:  "OfferAggregatesConfig",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetOfferAggregatesConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateExchangerOfferRequestValidationError{
					field:  "OfferAggregatesConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateExchangerOfferRequestValidationError{
					field:  "OfferAggregatesConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOfferAggregatesConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateExchangerOfferRequestValidationError{
				field:  "OfferAggregatesConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for GroupId

	if m.GetAdditionalDetails() == nil {
		err := CreateExchangerOfferRequestValidationError{
			field:  "AdditionalDetails",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAdditionalDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateExchangerOfferRequestValidationError{
					field:  "AdditionalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateExchangerOfferRequestValidationError{
					field:  "AdditionalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAdditionalDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateExchangerOfferRequestValidationError{
				field:  "AdditionalDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CategoryTag

	// no validation rules for SubCategoryTag

	if len(errors) > 0 {
		return CreateExchangerOfferRequestMultiError(errors)
	}

	return nil
}

// CreateExchangerOfferRequestMultiError is an error wrapping multiple
// validation errors returned by CreateExchangerOfferRequest.ValidateAll() if
// the designated constraints aren't met.
type CreateExchangerOfferRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateExchangerOfferRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateExchangerOfferRequestMultiError) AllErrors() []error { return m }

// CreateExchangerOfferRequestValidationError is the validation error returned
// by CreateExchangerOfferRequest.Validate if the designated constraints
// aren't met.
type CreateExchangerOfferRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateExchangerOfferRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateExchangerOfferRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateExchangerOfferRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateExchangerOfferRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateExchangerOfferRequestValidationError) ErrorName() string {
	return "CreateExchangerOfferRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateExchangerOfferRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateExchangerOfferRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateExchangerOfferRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateExchangerOfferRequestValidationError{}

var _CreateExchangerOfferRequest_RedemptionCurrency_NotInLookup = map[ExchangerOfferRedemptionCurrency]struct{}{
	0: {},
}

// Validate checks the field values on CreateExchangerOfferResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateExchangerOfferResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateExchangerOfferResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateExchangerOfferResponseMultiError, or nil if none found.
func (m *CreateExchangerOfferResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateExchangerOfferResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateExchangerOfferResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateExchangerOfferResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateExchangerOfferResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExchangerOffer()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateExchangerOfferResponseValidationError{
					field:  "ExchangerOffer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateExchangerOfferResponseValidationError{
					field:  "ExchangerOffer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExchangerOffer()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateExchangerOfferResponseValidationError{
				field:  "ExchangerOffer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateExchangerOfferResponseMultiError(errors)
	}

	return nil
}

// CreateExchangerOfferResponseMultiError is an error wrapping multiple
// validation errors returned by CreateExchangerOfferResponse.ValidateAll() if
// the designated constraints aren't met.
type CreateExchangerOfferResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateExchangerOfferResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateExchangerOfferResponseMultiError) AllErrors() []error { return m }

// CreateExchangerOfferResponseValidationError is the validation error returned
// by CreateExchangerOfferResponse.Validate if the designated constraints
// aren't met.
type CreateExchangerOfferResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateExchangerOfferResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateExchangerOfferResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateExchangerOfferResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateExchangerOfferResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateExchangerOfferResponseValidationError) ErrorName() string {
	return "CreateExchangerOfferResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateExchangerOfferResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateExchangerOfferResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateExchangerOfferResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateExchangerOfferResponseValidationError{}

// Validate checks the field values on CreateExchangerOfferListingRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateExchangerOfferListingRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateExchangerOfferListingRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreateExchangerOfferListingRequestMultiError, or nil if none found.
func (m *CreateExchangerOfferListingRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateExchangerOfferListingRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetExchangerOfferId()); l < 4 || l > 100 {
		err := CreateExchangerOfferListingRequestValidationError{
			field:  "ExchangerOfferId",
			reason: "value length must be between 4 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetActiveSince()); l < 4 || l > 100 {
		err := CreateExchangerOfferListingRequestValidationError{
			field:  "ActiveSince",
			reason: "value length must be between 4 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetActiveTill()); l < 4 || l > 100 {
		err := CreateExchangerOfferListingRequestValidationError{
			field:  "ActiveTill",
			reason: "value length must be between 4 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetDisplaySince()); l < 4 || l > 100 {
		err := CreateExchangerOfferListingRequestValidationError{
			field:  "DisplaySince",
			reason: "value length must be between 4 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetDisplayTill()); l < 4 || l > 100 {
		err := CreateExchangerOfferListingRequestValidationError{
			field:  "DisplayTill",
			reason: "value length must be between 4 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CreateExchangerOfferListingRequestMultiError(errors)
	}

	return nil
}

// CreateExchangerOfferListingRequestMultiError is an error wrapping multiple
// validation errors returned by
// CreateExchangerOfferListingRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateExchangerOfferListingRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateExchangerOfferListingRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateExchangerOfferListingRequestMultiError) AllErrors() []error { return m }

// CreateExchangerOfferListingRequestValidationError is the validation error
// returned by CreateExchangerOfferListingRequest.Validate if the designated
// constraints aren't met.
type CreateExchangerOfferListingRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateExchangerOfferListingRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateExchangerOfferListingRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateExchangerOfferListingRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateExchangerOfferListingRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateExchangerOfferListingRequestValidationError) ErrorName() string {
	return "CreateExchangerOfferListingRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateExchangerOfferListingRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateExchangerOfferListingRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateExchangerOfferListingRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateExchangerOfferListingRequestValidationError{}

// Validate checks the field values on CreateExchangerOfferListingResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateExchangerOfferListingResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateExchangerOfferListingResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreateExchangerOfferListingResponseMultiError, or nil if none found.
func (m *CreateExchangerOfferListingResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateExchangerOfferListingResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateExchangerOfferListingResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateExchangerOfferListingResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateExchangerOfferListingResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExchangerOfferListing()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateExchangerOfferListingResponseValidationError{
					field:  "ExchangerOfferListing",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateExchangerOfferListingResponseValidationError{
					field:  "ExchangerOfferListing",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExchangerOfferListing()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateExchangerOfferListingResponseValidationError{
				field:  "ExchangerOfferListing",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateExchangerOfferListingResponseMultiError(errors)
	}

	return nil
}

// CreateExchangerOfferListingResponseMultiError is an error wrapping multiple
// validation errors returned by
// CreateExchangerOfferListingResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateExchangerOfferListingResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateExchangerOfferListingResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateExchangerOfferListingResponseMultiError) AllErrors() []error { return m }

// CreateExchangerOfferListingResponseValidationError is the validation error
// returned by CreateExchangerOfferListingResponse.Validate if the designated
// constraints aren't met.
type CreateExchangerOfferListingResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateExchangerOfferListingResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateExchangerOfferListingResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateExchangerOfferListingResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateExchangerOfferListingResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateExchangerOfferListingResponseValidationError) ErrorName() string {
	return "CreateExchangerOfferListingResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateExchangerOfferListingResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateExchangerOfferListingResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateExchangerOfferListingResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateExchangerOfferListingResponseValidationError{}

// Validate checks the field values on UpdateExchangerOfferDisplayRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateExchangerOfferDisplayRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateExchangerOfferDisplayRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateExchangerOfferDisplayRequestMultiError, or nil if none found.
func (m *UpdateExchangerOfferDisplayRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateExchangerOfferDisplayRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetOfferId()); l < 4 || l > 100 {
		err := UpdateExchangerOfferDisplayRequestValidationError{
			field:  "OfferId",
			reason: "value length must be between 4 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetNewDisplayDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateExchangerOfferDisplayRequestValidationError{
					field:  "NewDisplayDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateExchangerOfferDisplayRequestValidationError{
					field:  "NewDisplayDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNewDisplayDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateExchangerOfferDisplayRequestValidationError{
				field:  "NewDisplayDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNewAdditionalDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateExchangerOfferDisplayRequestValidationError{
					field:  "NewAdditionalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateExchangerOfferDisplayRequestValidationError{
					field:  "NewAdditionalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNewAdditionalDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateExchangerOfferDisplayRequestValidationError{
				field:  "NewAdditionalDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CategoryTag

	// no validation rules for SubCategoryTag

	if len(errors) > 0 {
		return UpdateExchangerOfferDisplayRequestMultiError(errors)
	}

	return nil
}

// UpdateExchangerOfferDisplayRequestMultiError is an error wrapping multiple
// validation errors returned by
// UpdateExchangerOfferDisplayRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateExchangerOfferDisplayRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateExchangerOfferDisplayRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateExchangerOfferDisplayRequestMultiError) AllErrors() []error { return m }

// UpdateExchangerOfferDisplayRequestValidationError is the validation error
// returned by UpdateExchangerOfferDisplayRequest.Validate if the designated
// constraints aren't met.
type UpdateExchangerOfferDisplayRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateExchangerOfferDisplayRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateExchangerOfferDisplayRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateExchangerOfferDisplayRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateExchangerOfferDisplayRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateExchangerOfferDisplayRequestValidationError) ErrorName() string {
	return "UpdateExchangerOfferDisplayRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateExchangerOfferDisplayRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateExchangerOfferDisplayRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateExchangerOfferDisplayRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateExchangerOfferDisplayRequestValidationError{}

// Validate checks the field values on UpdateExchangerOfferDisplayResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateExchangerOfferDisplayResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateExchangerOfferDisplayResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateExchangerOfferDisplayResponseMultiError, or nil if none found.
func (m *UpdateExchangerOfferDisplayResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateExchangerOfferDisplayResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateExchangerOfferDisplayResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateExchangerOfferDisplayResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateExchangerOfferDisplayResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExchangerOffer()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateExchangerOfferDisplayResponseValidationError{
					field:  "ExchangerOffer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateExchangerOfferDisplayResponseValidationError{
					field:  "ExchangerOffer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExchangerOffer()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateExchangerOfferDisplayResponseValidationError{
				field:  "ExchangerOffer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateExchangerOfferDisplayResponseMultiError(errors)
	}

	return nil
}

// UpdateExchangerOfferDisplayResponseMultiError is an error wrapping multiple
// validation errors returned by
// UpdateExchangerOfferDisplayResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateExchangerOfferDisplayResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateExchangerOfferDisplayResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateExchangerOfferDisplayResponseMultiError) AllErrors() []error { return m }

// UpdateExchangerOfferDisplayResponseValidationError is the validation error
// returned by UpdateExchangerOfferDisplayResponse.Validate if the designated
// constraints aren't met.
type UpdateExchangerOfferDisplayResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateExchangerOfferDisplayResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateExchangerOfferDisplayResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateExchangerOfferDisplayResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateExchangerOfferDisplayResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateExchangerOfferDisplayResponseValidationError) ErrorName() string {
	return "UpdateExchangerOfferDisplayResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateExchangerOfferDisplayResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateExchangerOfferDisplayResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateExchangerOfferDisplayResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateExchangerOfferDisplayResponseValidationError{}

// Validate checks the field values on UpdateExchangerOfferStatusRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateExchangerOfferStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateExchangerOfferStatusRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateExchangerOfferStatusRequestMultiError, or nil if none found.
func (m *UpdateExchangerOfferStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateExchangerOfferStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OfferId

	// no validation rules for NewStatus

	if len(errors) > 0 {
		return UpdateExchangerOfferStatusRequestMultiError(errors)
	}

	return nil
}

// UpdateExchangerOfferStatusRequestMultiError is an error wrapping multiple
// validation errors returned by
// UpdateExchangerOfferStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateExchangerOfferStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateExchangerOfferStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateExchangerOfferStatusRequestMultiError) AllErrors() []error { return m }

// UpdateExchangerOfferStatusRequestValidationError is the validation error
// returned by UpdateExchangerOfferStatusRequest.Validate if the designated
// constraints aren't met.
type UpdateExchangerOfferStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateExchangerOfferStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateExchangerOfferStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateExchangerOfferStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateExchangerOfferStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateExchangerOfferStatusRequestValidationError) ErrorName() string {
	return "UpdateExchangerOfferStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateExchangerOfferStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateExchangerOfferStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateExchangerOfferStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateExchangerOfferStatusRequestValidationError{}

// Validate checks the field values on UpdateExchangerOfferStatusResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateExchangerOfferStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateExchangerOfferStatusResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateExchangerOfferStatusResponseMultiError, or nil if none found.
func (m *UpdateExchangerOfferStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateExchangerOfferStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateExchangerOfferStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateExchangerOfferStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateExchangerOfferStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExchangerOffer()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateExchangerOfferStatusResponseValidationError{
					field:  "ExchangerOffer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateExchangerOfferStatusResponseValidationError{
					field:  "ExchangerOffer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExchangerOffer()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateExchangerOfferStatusResponseValidationError{
				field:  "ExchangerOffer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateExchangerOfferStatusResponseMultiError(errors)
	}

	return nil
}

// UpdateExchangerOfferStatusResponseMultiError is an error wrapping multiple
// validation errors returned by
// UpdateExchangerOfferStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateExchangerOfferStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateExchangerOfferStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateExchangerOfferStatusResponseMultiError) AllErrors() []error { return m }

// UpdateExchangerOfferStatusResponseValidationError is the validation error
// returned by UpdateExchangerOfferStatusResponse.Validate if the designated
// constraints aren't met.
type UpdateExchangerOfferStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateExchangerOfferStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateExchangerOfferStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateExchangerOfferStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateExchangerOfferStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateExchangerOfferStatusResponseValidationError) ErrorName() string {
	return "UpdateExchangerOfferStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateExchangerOfferStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateExchangerOfferStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateExchangerOfferStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateExchangerOfferStatusResponseValidationError{}

// Validate checks the field values on UpdateExchangerOfferListingRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateExchangerOfferListingRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateExchangerOfferListingRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateExchangerOfferListingRequestMultiError, or nil if none found.
func (m *UpdateExchangerOfferListingRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateExchangerOfferListingRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetOfferListingId()); l < 4 || l > 100 {
		err := UpdateExchangerOfferListingRequestValidationError{
			field:  "OfferListingId",
			reason: "value length must be between 4 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ActiveSince

	// no validation rules for ActiveTill

	// no validation rules for DisplaySince

	// no validation rules for DisplayTill

	if len(errors) > 0 {
		return UpdateExchangerOfferListingRequestMultiError(errors)
	}

	return nil
}

// UpdateExchangerOfferListingRequestMultiError is an error wrapping multiple
// validation errors returned by
// UpdateExchangerOfferListingRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateExchangerOfferListingRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateExchangerOfferListingRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateExchangerOfferListingRequestMultiError) AllErrors() []error { return m }

// UpdateExchangerOfferListingRequestValidationError is the validation error
// returned by UpdateExchangerOfferListingRequest.Validate if the designated
// constraints aren't met.
type UpdateExchangerOfferListingRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateExchangerOfferListingRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateExchangerOfferListingRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateExchangerOfferListingRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateExchangerOfferListingRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateExchangerOfferListingRequestValidationError) ErrorName() string {
	return "UpdateExchangerOfferListingRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateExchangerOfferListingRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateExchangerOfferListingRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateExchangerOfferListingRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateExchangerOfferListingRequestValidationError{}

// Validate checks the field values on UpdateExchangerOfferListingResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateExchangerOfferListingResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateExchangerOfferListingResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateExchangerOfferListingResponseMultiError, or nil if none found.
func (m *UpdateExchangerOfferListingResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateExchangerOfferListingResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateExchangerOfferListingResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateExchangerOfferListingResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateExchangerOfferListingResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOfferListing()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateExchangerOfferListingResponseValidationError{
					field:  "OfferListing",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateExchangerOfferListingResponseValidationError{
					field:  "OfferListing",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOfferListing()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateExchangerOfferListingResponseValidationError{
				field:  "OfferListing",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateExchangerOfferListingResponseMultiError(errors)
	}

	return nil
}

// UpdateExchangerOfferListingResponseMultiError is an error wrapping multiple
// validation errors returned by
// UpdateExchangerOfferListingResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateExchangerOfferListingResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateExchangerOfferListingResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateExchangerOfferListingResponseMultiError) AllErrors() []error { return m }

// UpdateExchangerOfferListingResponseValidationError is the validation error
// returned by UpdateExchangerOfferListingResponse.Validate if the designated
// constraints aren't met.
type UpdateExchangerOfferListingResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateExchangerOfferListingResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateExchangerOfferListingResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateExchangerOfferListingResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateExchangerOfferListingResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateExchangerOfferListingResponseValidationError) ErrorName() string {
	return "UpdateExchangerOfferListingResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateExchangerOfferListingResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateExchangerOfferListingResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateExchangerOfferListingResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateExchangerOfferListingResponseValidationError{}

// Validate checks the field values on DeleteExchangerOfferListingRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *DeleteExchangerOfferListingRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteExchangerOfferListingRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// DeleteExchangerOfferListingRequestMultiError, or nil if none found.
func (m *DeleteExchangerOfferListingRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteExchangerOfferListingRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetOfferListingId()); l < 4 || l > 100 {
		err := DeleteExchangerOfferListingRequestValidationError{
			field:  "OfferListingId",
			reason: "value length must be between 4 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeleteExchangerOfferListingRequestMultiError(errors)
	}

	return nil
}

// DeleteExchangerOfferListingRequestMultiError is an error wrapping multiple
// validation errors returned by
// DeleteExchangerOfferListingRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteExchangerOfferListingRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteExchangerOfferListingRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteExchangerOfferListingRequestMultiError) AllErrors() []error { return m }

// DeleteExchangerOfferListingRequestValidationError is the validation error
// returned by DeleteExchangerOfferListingRequest.Validate if the designated
// constraints aren't met.
type DeleteExchangerOfferListingRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteExchangerOfferListingRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteExchangerOfferListingRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteExchangerOfferListingRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteExchangerOfferListingRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteExchangerOfferListingRequestValidationError) ErrorName() string {
	return "DeleteExchangerOfferListingRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteExchangerOfferListingRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteExchangerOfferListingRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteExchangerOfferListingRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteExchangerOfferListingRequestValidationError{}

// Validate checks the field values on DeleteExchangerOfferListingResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *DeleteExchangerOfferListingResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteExchangerOfferListingResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// DeleteExchangerOfferListingResponseMultiError, or nil if none found.
func (m *DeleteExchangerOfferListingResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteExchangerOfferListingResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeleteExchangerOfferListingResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeleteExchangerOfferListingResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeleteExchangerOfferListingResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeleteExchangerOfferListingResponseMultiError(errors)
	}

	return nil
}

// DeleteExchangerOfferListingResponseMultiError is an error wrapping multiple
// validation errors returned by
// DeleteExchangerOfferListingResponse.ValidateAll() if the designated
// constraints aren't met.
type DeleteExchangerOfferListingResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteExchangerOfferListingResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteExchangerOfferListingResponseMultiError) AllErrors() []error { return m }

// DeleteExchangerOfferListingResponseValidationError is the validation error
// returned by DeleteExchangerOfferListingResponse.Validate if the designated
// constraints aren't met.
type DeleteExchangerOfferListingResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteExchangerOfferListingResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteExchangerOfferListingResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteExchangerOfferListingResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteExchangerOfferListingResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteExchangerOfferListingResponseValidationError) ErrorName() string {
	return "DeleteExchangerOfferListingResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteExchangerOfferListingResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteExchangerOfferListingResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteExchangerOfferListingResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteExchangerOfferListingResponseValidationError{}

// Validate checks the field values on GetExchangerOffersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetExchangerOffersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetExchangerOffersRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetExchangerOffersRequestMultiError, or nil if none found.
func (m *GetExchangerOffersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExchangerOffersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetFilters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOffersRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOffersRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOffersRequestValidationError{
				field:  "Filters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFiltersV2()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOffersRequestValidationError{
					field:  "FiltersV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOffersRequestValidationError{
					field:  "FiltersV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFiltersV2()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOffersRequestValidationError{
				field:  "FiltersV2",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetExchangerOffersRequestMultiError(errors)
	}

	return nil
}

// GetExchangerOffersRequestMultiError is an error wrapping multiple validation
// errors returned by GetExchangerOffersRequest.ValidateAll() if the
// designated constraints aren't met.
type GetExchangerOffersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExchangerOffersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExchangerOffersRequestMultiError) AllErrors() []error { return m }

// GetExchangerOffersRequestValidationError is the validation error returned by
// GetExchangerOffersRequest.Validate if the designated constraints aren't met.
type GetExchangerOffersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExchangerOffersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExchangerOffersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExchangerOffersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExchangerOffersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExchangerOffersRequestValidationError) ErrorName() string {
	return "GetExchangerOffersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetExchangerOffersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExchangerOffersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExchangerOffersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExchangerOffersRequestValidationError{}

// Validate checks the field values on GetExchangerOffersResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetExchangerOffersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetExchangerOffersResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetExchangerOffersResponseMultiError, or nil if none found.
func (m *GetExchangerOffersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExchangerOffersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOffersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOffersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOffersResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetExchangerOffers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetExchangerOffersResponseValidationError{
						field:  fmt.Sprintf("ExchangerOffers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetExchangerOffersResponseValidationError{
						field:  fmt.Sprintf("ExchangerOffers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetExchangerOffersResponseValidationError{
					field:  fmt.Sprintf("ExchangerOffers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	{
		sorted_keys := make([]string, len(m.GetOfferIdToListingMap()))
		i := 0
		for key := range m.GetOfferIdToListingMap() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetOfferIdToListingMap()[key]
			_ = val

			// no validation rules for OfferIdToListingMap[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetExchangerOffersResponseValidationError{
							field:  fmt.Sprintf("OfferIdToListingMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetExchangerOffersResponseValidationError{
							field:  fmt.Sprintf("OfferIdToListingMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetExchangerOffersResponseValidationError{
						field:  fmt.Sprintf("OfferIdToListingMap[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return GetExchangerOffersResponseMultiError(errors)
	}

	return nil
}

// GetExchangerOffersResponseMultiError is an error wrapping multiple
// validation errors returned by GetExchangerOffersResponse.ValidateAll() if
// the designated constraints aren't met.
type GetExchangerOffersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExchangerOffersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExchangerOffersResponseMultiError) AllErrors() []error { return m }

// GetExchangerOffersResponseValidationError is the validation error returned
// by GetExchangerOffersResponse.Validate if the designated constraints aren't met.
type GetExchangerOffersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExchangerOffersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExchangerOffersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExchangerOffersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExchangerOffersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExchangerOffersResponseValidationError) ErrorName() string {
	return "GetExchangerOffersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetExchangerOffersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExchangerOffersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExchangerOffersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExchangerOffersResponseValidationError{}

// Validate checks the field values on GetExchangerOffersByIdsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetExchangerOffersByIdsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetExchangerOffersByIdsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetExchangerOffersByIdsRequestMultiError, or nil if none found.
func (m *GetExchangerOffersByIdsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExchangerOffersByIdsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOffersByIdsRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOffersByIdsRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOffersByIdsRequestValidationError{
				field:  "Filters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetExchangerOffersByIdsRequestMultiError(errors)
	}

	return nil
}

// GetExchangerOffersByIdsRequestMultiError is an error wrapping multiple
// validation errors returned by GetExchangerOffersByIdsRequest.ValidateAll()
// if the designated constraints aren't met.
type GetExchangerOffersByIdsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExchangerOffersByIdsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExchangerOffersByIdsRequestMultiError) AllErrors() []error { return m }

// GetExchangerOffersByIdsRequestValidationError is the validation error
// returned by GetExchangerOffersByIdsRequest.Validate if the designated
// constraints aren't met.
type GetExchangerOffersByIdsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExchangerOffersByIdsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExchangerOffersByIdsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExchangerOffersByIdsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExchangerOffersByIdsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExchangerOffersByIdsRequestValidationError) ErrorName() string {
	return "GetExchangerOffersByIdsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetExchangerOffersByIdsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExchangerOffersByIdsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExchangerOffersByIdsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExchangerOffersByIdsRequestValidationError{}

// Validate checks the field values on GetExchangerOffersByIdsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetExchangerOffersByIdsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetExchangerOffersByIdsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetExchangerOffersByIdsResponseMultiError, or nil if none found.
func (m *GetExchangerOffersByIdsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExchangerOffersByIdsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOffersByIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOffersByIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOffersByIdsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetExchangerOffers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetExchangerOffersByIdsResponseValidationError{
						field:  fmt.Sprintf("ExchangerOffers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetExchangerOffersByIdsResponseValidationError{
						field:  fmt.Sprintf("ExchangerOffers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetExchangerOffersByIdsResponseValidationError{
					field:  fmt.Sprintf("ExchangerOffers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetExchangerOffersByIdsResponseMultiError(errors)
	}

	return nil
}

// GetExchangerOffersByIdsResponseMultiError is an error wrapping multiple
// validation errors returned by GetExchangerOffersByIdsResponse.ValidateAll()
// if the designated constraints aren't met.
type GetExchangerOffersByIdsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExchangerOffersByIdsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExchangerOffersByIdsResponseMultiError) AllErrors() []error { return m }

// GetExchangerOffersByIdsResponseValidationError is the validation error
// returned by GetExchangerOffersByIdsResponse.Validate if the designated
// constraints aren't met.
type GetExchangerOffersByIdsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExchangerOffersByIdsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExchangerOffersByIdsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExchangerOffersByIdsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExchangerOffersByIdsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExchangerOffersByIdsResponseValidationError) ErrorName() string {
	return "GetExchangerOffersByIdsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetExchangerOffersByIdsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExchangerOffersByIdsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExchangerOffersByIdsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExchangerOffersByIdsResponseValidationError{}

// Validate checks the field values on RedeemExchangerOfferRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RedeemExchangerOfferRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RedeemExchangerOfferRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RedeemExchangerOfferRequestMultiError, or nil if none found.
func (m *RedeemExchangerOfferRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RedeemExchangerOfferRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetExchangerOfferId()); l < 4 || l > 100 {
		err := RedeemExchangerOfferRequestValidationError{
			field:  "ExchangerOfferId",
			reason: "value length must be between 4 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetActorId()); l < 4 || l > 100 {
		err := RedeemExchangerOfferRequestValidationError{
			field:  "ActorId",
			reason: "value length must be between 4 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetRequestId()); l < 4 || l > 100 {
		err := RedeemExchangerOfferRequestValidationError{
			field:  "RequestId",
			reason: "value length must be between 4 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return RedeemExchangerOfferRequestMultiError(errors)
	}

	return nil
}

// RedeemExchangerOfferRequestMultiError is an error wrapping multiple
// validation errors returned by RedeemExchangerOfferRequest.ValidateAll() if
// the designated constraints aren't met.
type RedeemExchangerOfferRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RedeemExchangerOfferRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RedeemExchangerOfferRequestMultiError) AllErrors() []error { return m }

// RedeemExchangerOfferRequestValidationError is the validation error returned
// by RedeemExchangerOfferRequest.Validate if the designated constraints
// aren't met.
type RedeemExchangerOfferRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RedeemExchangerOfferRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RedeemExchangerOfferRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RedeemExchangerOfferRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RedeemExchangerOfferRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RedeemExchangerOfferRequestValidationError) ErrorName() string {
	return "RedeemExchangerOfferRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RedeemExchangerOfferRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRedeemExchangerOfferRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RedeemExchangerOfferRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RedeemExchangerOfferRequestValidationError{}

// Validate checks the field values on RedeemExchangerOfferResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RedeemExchangerOfferResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RedeemExchangerOfferResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RedeemExchangerOfferResponseMultiError, or nil if none found.
func (m *RedeemExchangerOfferResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RedeemExchangerOfferResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RedeemExchangerOfferResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RedeemExchangerOfferResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RedeemExchangerOfferResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExchangerOfferOrder()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RedeemExchangerOfferResponseValidationError{
					field:  "ExchangerOfferOrder",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RedeemExchangerOfferResponseValidationError{
					field:  "ExchangerOfferOrder",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExchangerOfferOrder()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RedeemExchangerOfferResponseValidationError{
				field:  "ExchangerOfferOrder",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RedeemExchangerOfferResponseMultiError(errors)
	}

	return nil
}

// RedeemExchangerOfferResponseMultiError is an error wrapping multiple
// validation errors returned by RedeemExchangerOfferResponse.ValidateAll() if
// the designated constraints aren't met.
type RedeemExchangerOfferResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RedeemExchangerOfferResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RedeemExchangerOfferResponseMultiError) AllErrors() []error { return m }

// RedeemExchangerOfferResponseValidationError is the validation error returned
// by RedeemExchangerOfferResponse.Validate if the designated constraints
// aren't met.
type RedeemExchangerOfferResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RedeemExchangerOfferResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RedeemExchangerOfferResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RedeemExchangerOfferResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RedeemExchangerOfferResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RedeemExchangerOfferResponseValidationError) ErrorName() string {
	return "RedeemExchangerOfferResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RedeemExchangerOfferResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRedeemExchangerOfferResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RedeemExchangerOfferResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RedeemExchangerOfferResponseValidationError{}

// Validate checks the field values on GetExchangerOfferOrdersForActorRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetExchangerOfferOrdersForActorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetExchangerOfferOrdersForActorRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetExchangerOfferOrdersForActorRequestMultiError, or nil if none found.
func (m *GetExchangerOfferOrdersForActorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExchangerOfferOrdersForActorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetActorId()); l < 4 || l > 100 {
		err := GetExchangerOfferOrdersForActorRequestValidationError{
			field:  "ActorId",
			reason: "value length must be between 4 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetFilters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersForActorRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersForActorRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOfferOrdersForActorRequestValidationError{
				field:  "Filters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersForActorRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersForActorRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOfferOrdersForActorRequestValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetExchangerOfferOrdersForActorRequestMultiError(errors)
	}

	return nil
}

// GetExchangerOfferOrdersForActorRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetExchangerOfferOrdersForActorRequest.ValidateAll() if the designated
// constraints aren't met.
type GetExchangerOfferOrdersForActorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExchangerOfferOrdersForActorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExchangerOfferOrdersForActorRequestMultiError) AllErrors() []error { return m }

// GetExchangerOfferOrdersForActorRequestValidationError is the validation
// error returned by GetExchangerOfferOrdersForActorRequest.Validate if the
// designated constraints aren't met.
type GetExchangerOfferOrdersForActorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExchangerOfferOrdersForActorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExchangerOfferOrdersForActorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExchangerOfferOrdersForActorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExchangerOfferOrdersForActorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExchangerOfferOrdersForActorRequestValidationError) ErrorName() string {
	return "GetExchangerOfferOrdersForActorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetExchangerOfferOrdersForActorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExchangerOfferOrdersForActorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExchangerOfferOrdersForActorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExchangerOfferOrdersForActorRequestValidationError{}

// Validate checks the field values on GetExchangerOfferOrdersForActorResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetExchangerOfferOrdersForActorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetExchangerOfferOrdersForActorResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetExchangerOfferOrdersForActorResponseMultiError, or nil if none found.
func (m *GetExchangerOfferOrdersForActorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExchangerOfferOrdersForActorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOfferOrdersForActorResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetExchangerOfferOrders() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetExchangerOfferOrdersForActorResponseValidationError{
						field:  fmt.Sprintf("ExchangerOfferOrders[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetExchangerOfferOrdersForActorResponseValidationError{
						field:  fmt.Sprintf("ExchangerOfferOrders[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetExchangerOfferOrdersForActorResponseValidationError{
					field:  fmt.Sprintf("ExchangerOfferOrders[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersForActorResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersForActorResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOfferOrdersForActorResponseValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetExchangerOfferOrdersForActorResponseMultiError(errors)
	}

	return nil
}

// GetExchangerOfferOrdersForActorResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetExchangerOfferOrdersForActorResponse.ValidateAll() if the designated
// constraints aren't met.
type GetExchangerOfferOrdersForActorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExchangerOfferOrdersForActorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExchangerOfferOrdersForActorResponseMultiError) AllErrors() []error { return m }

// GetExchangerOfferOrdersForActorResponseValidationError is the validation
// error returned by GetExchangerOfferOrdersForActorResponse.Validate if the
// designated constraints aren't met.
type GetExchangerOfferOrdersForActorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExchangerOfferOrdersForActorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExchangerOfferOrdersForActorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExchangerOfferOrdersForActorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExchangerOfferOrdersForActorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExchangerOfferOrdersForActorResponseValidationError) ErrorName() string {
	return "GetExchangerOfferOrdersForActorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetExchangerOfferOrdersForActorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExchangerOfferOrdersForActorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExchangerOfferOrdersForActorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExchangerOfferOrdersForActorResponseValidationError{}

// Validate checks the field values on GetExchangerOfferOrdersRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetExchangerOfferOrdersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetExchangerOfferOrdersRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetExchangerOfferOrdersRequestMultiError, or nil if none found.
func (m *GetExchangerOfferOrdersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExchangerOfferOrdersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetFilters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOfferOrdersRequestValidationError{
				field:  "Filters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOfferOrdersRequestValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetExchangerOfferOrdersRequestMultiError(errors)
	}

	return nil
}

// GetExchangerOfferOrdersRequestMultiError is an error wrapping multiple
// validation errors returned by GetExchangerOfferOrdersRequest.ValidateAll()
// if the designated constraints aren't met.
type GetExchangerOfferOrdersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExchangerOfferOrdersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExchangerOfferOrdersRequestMultiError) AllErrors() []error { return m }

// GetExchangerOfferOrdersRequestValidationError is the validation error
// returned by GetExchangerOfferOrdersRequest.Validate if the designated
// constraints aren't met.
type GetExchangerOfferOrdersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExchangerOfferOrdersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExchangerOfferOrdersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExchangerOfferOrdersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExchangerOfferOrdersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExchangerOfferOrdersRequestValidationError) ErrorName() string {
	return "GetExchangerOfferOrdersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetExchangerOfferOrdersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExchangerOfferOrdersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExchangerOfferOrdersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExchangerOfferOrdersRequestValidationError{}

// Validate checks the field values on GetExchangerOfferOrdersResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetExchangerOfferOrdersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetExchangerOfferOrdersResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetExchangerOfferOrdersResponseMultiError, or nil if none found.
func (m *GetExchangerOfferOrdersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExchangerOfferOrdersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOfferOrdersResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetExchangerOfferOrders() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetExchangerOfferOrdersResponseValidationError{
						field:  fmt.Sprintf("ExchangerOfferOrders[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetExchangerOfferOrdersResponseValidationError{
						field:  fmt.Sprintf("ExchangerOfferOrders[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetExchangerOfferOrdersResponseValidationError{
					field:  fmt.Sprintf("ExchangerOfferOrders[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOfferOrdersResponseValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetExchangerOfferOrdersResponseMultiError(errors)
	}

	return nil
}

// GetExchangerOfferOrdersResponseMultiError is an error wrapping multiple
// validation errors returned by GetExchangerOfferOrdersResponse.ValidateAll()
// if the designated constraints aren't met.
type GetExchangerOfferOrdersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExchangerOfferOrdersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExchangerOfferOrdersResponseMultiError) AllErrors() []error { return m }

// GetExchangerOfferOrdersResponseValidationError is the validation error
// returned by GetExchangerOfferOrdersResponse.Validate if the designated
// constraints aren't met.
type GetExchangerOfferOrdersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExchangerOfferOrdersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExchangerOfferOrdersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExchangerOfferOrdersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExchangerOfferOrdersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExchangerOfferOrdersResponseValidationError) ErrorName() string {
	return "GetExchangerOfferOrdersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetExchangerOfferOrdersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExchangerOfferOrdersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExchangerOfferOrdersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExchangerOfferOrdersResponseValidationError{}

// Validate checks the field values on GetExchangerOrderByIdRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetExchangerOrderByIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetExchangerOrderByIdRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetExchangerOrderByIdRequestMultiError, or nil if none found.
func (m *GetExchangerOrderByIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExchangerOrderByIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ExchangerOrderId

	if len(errors) > 0 {
		return GetExchangerOrderByIdRequestMultiError(errors)
	}

	return nil
}

// GetExchangerOrderByIdRequestMultiError is an error wrapping multiple
// validation errors returned by GetExchangerOrderByIdRequest.ValidateAll() if
// the designated constraints aren't met.
type GetExchangerOrderByIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExchangerOrderByIdRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExchangerOrderByIdRequestMultiError) AllErrors() []error { return m }

// GetExchangerOrderByIdRequestValidationError is the validation error returned
// by GetExchangerOrderByIdRequest.Validate if the designated constraints
// aren't met.
type GetExchangerOrderByIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExchangerOrderByIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExchangerOrderByIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExchangerOrderByIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExchangerOrderByIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExchangerOrderByIdRequestValidationError) ErrorName() string {
	return "GetExchangerOrderByIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetExchangerOrderByIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExchangerOrderByIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExchangerOrderByIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExchangerOrderByIdRequestValidationError{}

// Validate checks the field values on GetExchangerOrderByIdResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetExchangerOrderByIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetExchangerOrderByIdResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetExchangerOrderByIdResponseMultiError, or nil if none found.
func (m *GetExchangerOrderByIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExchangerOrderByIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOrderByIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOrderByIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOrderByIdResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExchangerOrder()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOrderByIdResponseValidationError{
					field:  "ExchangerOrder",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOrderByIdResponseValidationError{
					field:  "ExchangerOrder",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExchangerOrder()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOrderByIdResponseValidationError{
				field:  "ExchangerOrder",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetExchangerOrderByIdResponseMultiError(errors)
	}

	return nil
}

// GetExchangerOrderByIdResponseMultiError is an error wrapping multiple
// validation errors returned by GetExchangerOrderByIdResponse.ValidateAll()
// if the designated constraints aren't met.
type GetExchangerOrderByIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExchangerOrderByIdResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExchangerOrderByIdResponseMultiError) AllErrors() []error { return m }

// GetExchangerOrderByIdResponseValidationError is the validation error
// returned by GetExchangerOrderByIdResponse.Validate if the designated
// constraints aren't met.
type GetExchangerOrderByIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExchangerOrderByIdResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExchangerOrderByIdResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExchangerOrderByIdResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExchangerOrderByIdResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExchangerOrderByIdResponseValidationError) ErrorName() string {
	return "GetExchangerOrderByIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetExchangerOrderByIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExchangerOrderByIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExchangerOrderByIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExchangerOrderByIdResponseValidationError{}

// Validate checks the field values on GetExchangerOfferActorAttemptsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetExchangerOfferActorAttemptsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetExchangerOfferActorAttemptsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetExchangerOfferActorAttemptsRequestMultiError, or nil if none found.
func (m *GetExchangerOfferActorAttemptsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExchangerOfferActorAttemptsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetActorId()); l < 4 || l > 100 {
		err := GetExchangerOfferActorAttemptsRequestValidationError{
			field:  "ActorId",
			reason: "value length must be between 4 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetExchangerOfferId()); l < 4 || l > 100 {
		err := GetExchangerOfferActorAttemptsRequestValidationError{
			field:  "ExchangerOfferId",
			reason: "value length must be between 4 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetFromAttemptedTime() == nil {
		err := GetExchangerOfferActorAttemptsRequestValidationError{
			field:  "FromAttemptedTime",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetToAttemptedTime() == nil {
		err := GetExchangerOfferActorAttemptsRequestValidationError{
			field:  "ToAttemptedTime",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetExchangerOfferActorAttemptsRequestMultiError(errors)
	}

	return nil
}

// GetExchangerOfferActorAttemptsRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetExchangerOfferActorAttemptsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetExchangerOfferActorAttemptsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExchangerOfferActorAttemptsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExchangerOfferActorAttemptsRequestMultiError) AllErrors() []error { return m }

// GetExchangerOfferActorAttemptsRequestValidationError is the validation error
// returned by GetExchangerOfferActorAttemptsRequest.Validate if the
// designated constraints aren't met.
type GetExchangerOfferActorAttemptsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExchangerOfferActorAttemptsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExchangerOfferActorAttemptsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExchangerOfferActorAttemptsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExchangerOfferActorAttemptsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExchangerOfferActorAttemptsRequestValidationError) ErrorName() string {
	return "GetExchangerOfferActorAttemptsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetExchangerOfferActorAttemptsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExchangerOfferActorAttemptsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExchangerOfferActorAttemptsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExchangerOfferActorAttemptsRequestValidationError{}

// Validate checks the field values on GetExchangerOfferActorAttemptsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetExchangerOfferActorAttemptsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetExchangerOfferActorAttemptsResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetExchangerOfferActorAttemptsResponseMultiError, or nil if none found.
func (m *GetExchangerOfferActorAttemptsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExchangerOfferActorAttemptsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOfferActorAttemptsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOfferActorAttemptsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOfferActorAttemptsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetExchangerOfferActorAttempts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetExchangerOfferActorAttemptsResponseValidationError{
						field:  fmt.Sprintf("ExchangerOfferActorAttempts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetExchangerOfferActorAttemptsResponseValidationError{
						field:  fmt.Sprintf("ExchangerOfferActorAttempts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetExchangerOfferActorAttemptsResponseValidationError{
					field:  fmt.Sprintf("ExchangerOfferActorAttempts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetExchangerOfferActorAttemptsResponseMultiError(errors)
	}

	return nil
}

// GetExchangerOfferActorAttemptsResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetExchangerOfferActorAttemptsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetExchangerOfferActorAttemptsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExchangerOfferActorAttemptsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExchangerOfferActorAttemptsResponseMultiError) AllErrors() []error { return m }

// GetExchangerOfferActorAttemptsResponseValidationError is the validation
// error returned by GetExchangerOfferActorAttemptsResponse.Validate if the
// designated constraints aren't met.
type GetExchangerOfferActorAttemptsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExchangerOfferActorAttemptsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExchangerOfferActorAttemptsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExchangerOfferActorAttemptsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExchangerOfferActorAttemptsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExchangerOfferActorAttemptsResponseValidationError) ErrorName() string {
	return "GetExchangerOfferActorAttemptsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetExchangerOfferActorAttemptsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExchangerOfferActorAttemptsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExchangerOfferActorAttemptsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExchangerOfferActorAttemptsResponseValidationError{}

// Validate checks the field values on
// GetExchangerOffersActorAttemptsCountRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetExchangerOffersActorAttemptsCountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetExchangerOffersActorAttemptsCountRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetExchangerOffersActorAttemptsCountRequestMultiError, or nil if none found.
func (m *GetExchangerOffersActorAttemptsCountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExchangerOffersActorAttemptsCountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetActorId()); l < 4 || l > 100 {
		err := GetExchangerOffersActorAttemptsCountRequestValidationError{
			field:  "ActorId",
			reason: "value length must be between 4 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetExchangerOfferIds() {
		_, _ = idx, item

		if l := utf8.RuneCountInString(item); l < 4 || l > 100 {
			err := GetExchangerOffersActorAttemptsCountRequestValidationError{
				field:  fmt.Sprintf("ExchangerOfferIds[%v]", idx),
				reason: "value length must be between 4 and 100 runes, inclusive",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetFromAttemptedTime() == nil {
		err := GetExchangerOffersActorAttemptsCountRequestValidationError{
			field:  "FromAttemptedTime",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetToAttemptedTime() == nil {
		err := GetExchangerOffersActorAttemptsCountRequestValidationError{
			field:  "ToAttemptedTime",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetExchangerOffersActorAttemptsCountRequestMultiError(errors)
	}

	return nil
}

// GetExchangerOffersActorAttemptsCountRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetExchangerOffersActorAttemptsCountRequest.ValidateAll() if the designated
// constraints aren't met.
type GetExchangerOffersActorAttemptsCountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExchangerOffersActorAttemptsCountRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExchangerOffersActorAttemptsCountRequestMultiError) AllErrors() []error { return m }

// GetExchangerOffersActorAttemptsCountRequestValidationError is the validation
// error returned by GetExchangerOffersActorAttemptsCountRequest.Validate if
// the designated constraints aren't met.
type GetExchangerOffersActorAttemptsCountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExchangerOffersActorAttemptsCountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExchangerOffersActorAttemptsCountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExchangerOffersActorAttemptsCountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExchangerOffersActorAttemptsCountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExchangerOffersActorAttemptsCountRequestValidationError) ErrorName() string {
	return "GetExchangerOffersActorAttemptsCountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetExchangerOffersActorAttemptsCountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExchangerOffersActorAttemptsCountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExchangerOffersActorAttemptsCountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExchangerOffersActorAttemptsCountRequestValidationError{}

// Validate checks the field values on
// GetExchangerOffersActorAttemptsCountResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetExchangerOffersActorAttemptsCountResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetExchangerOffersActorAttemptsCountResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetExchangerOffersActorAttemptsCountResponseMultiError, or nil if none found.
func (m *GetExchangerOffersActorAttemptsCountResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExchangerOffersActorAttemptsCountResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOffersActorAttemptsCountResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOffersActorAttemptsCountResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOffersActorAttemptsCountResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OfferIdToAttemptsCountMap

	if len(errors) > 0 {
		return GetExchangerOffersActorAttemptsCountResponseMultiError(errors)
	}

	return nil
}

// GetExchangerOffersActorAttemptsCountResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetExchangerOffersActorAttemptsCountResponse.ValidateAll() if the
// designated constraints aren't met.
type GetExchangerOffersActorAttemptsCountResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExchangerOffersActorAttemptsCountResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExchangerOffersActorAttemptsCountResponseMultiError) AllErrors() []error { return m }

// GetExchangerOffersActorAttemptsCountResponseValidationError is the
// validation error returned by
// GetExchangerOffersActorAttemptsCountResponse.Validate if the designated
// constraints aren't met.
type GetExchangerOffersActorAttemptsCountResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExchangerOffersActorAttemptsCountResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExchangerOffersActorAttemptsCountResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExchangerOffersActorAttemptsCountResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExchangerOffersActorAttemptsCountResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExchangerOffersActorAttemptsCountResponseValidationError) ErrorName() string {
	return "GetExchangerOffersActorAttemptsCountResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetExchangerOffersActorAttemptsCountResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExchangerOffersActorAttemptsCountResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExchangerOffersActorAttemptsCountResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExchangerOffersActorAttemptsCountResponseValidationError{}

// Validate checks the field values on ChooseExchangerOrderOptionRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ChooseExchangerOrderOptionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChooseExchangerOrderOptionRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ChooseExchangerOrderOptionRequestMultiError, or nil if none found.
func (m *ChooseExchangerOrderOptionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ChooseExchangerOrderOptionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetActorId()); l < 4 || l > 100 {
		err := ChooseExchangerOrderOptionRequestValidationError{
			field:  "ActorId",
			reason: "value length must be between 4 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetExchangerOrderId()); l < 4 || l > 100 {
		err := ChooseExchangerOrderOptionRequestValidationError{
			field:  "ExchangerOrderId",
			reason: "value length must be between 4 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetOptionId()); l < 4 || l > 100 {
		err := ChooseExchangerOrderOptionRequestValidationError{
			field:  "OptionId",
			reason: "value length must be between 4 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ChooseExchangerOrderOptionRequestMultiError(errors)
	}

	return nil
}

// ChooseExchangerOrderOptionRequestMultiError is an error wrapping multiple
// validation errors returned by
// ChooseExchangerOrderOptionRequest.ValidateAll() if the designated
// constraints aren't met.
type ChooseExchangerOrderOptionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChooseExchangerOrderOptionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChooseExchangerOrderOptionRequestMultiError) AllErrors() []error { return m }

// ChooseExchangerOrderOptionRequestValidationError is the validation error
// returned by ChooseExchangerOrderOptionRequest.Validate if the designated
// constraints aren't met.
type ChooseExchangerOrderOptionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChooseExchangerOrderOptionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChooseExchangerOrderOptionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChooseExchangerOrderOptionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChooseExchangerOrderOptionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChooseExchangerOrderOptionRequestValidationError) ErrorName() string {
	return "ChooseExchangerOrderOptionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ChooseExchangerOrderOptionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChooseExchangerOrderOptionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChooseExchangerOrderOptionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChooseExchangerOrderOptionRequestValidationError{}

// Validate checks the field values on ChooseExchangerOrderOptionResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ChooseExchangerOrderOptionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChooseExchangerOrderOptionResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ChooseExchangerOrderOptionResponseMultiError, or nil if none found.
func (m *ChooseExchangerOrderOptionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ChooseExchangerOrderOptionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ChooseExchangerOrderOptionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ChooseExchangerOrderOptionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ChooseExchangerOrderOptionResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExchangerOfferOrder()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ChooseExchangerOrderOptionResponseValidationError{
					field:  "ExchangerOfferOrder",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ChooseExchangerOrderOptionResponseValidationError{
					field:  "ExchangerOfferOrder",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExchangerOfferOrder()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ChooseExchangerOrderOptionResponseValidationError{
				field:  "ExchangerOfferOrder",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ChooseExchangerOrderOptionResponseMultiError(errors)
	}

	return nil
}

// ChooseExchangerOrderOptionResponseMultiError is an error wrapping multiple
// validation errors returned by
// ChooseExchangerOrderOptionResponse.ValidateAll() if the designated
// constraints aren't met.
type ChooseExchangerOrderOptionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChooseExchangerOrderOptionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChooseExchangerOrderOptionResponseMultiError) AllErrors() []error { return m }

// ChooseExchangerOrderOptionResponseValidationError is the validation error
// returned by ChooseExchangerOrderOptionResponse.Validate if the designated
// constraints aren't met.
type ChooseExchangerOrderOptionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChooseExchangerOrderOptionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChooseExchangerOrderOptionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChooseExchangerOrderOptionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChooseExchangerOrderOptionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChooseExchangerOrderOptionResponseValidationError) ErrorName() string {
	return "ChooseExchangerOrderOptionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ChooseExchangerOrderOptionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChooseExchangerOrderOptionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChooseExchangerOrderOptionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChooseExchangerOrderOptionResponseValidationError{}

// Validate checks the field values on CreateExchangerOfferGroupRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateExchangerOfferGroupRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateExchangerOfferGroupRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateExchangerOfferGroupRequestMultiError, or nil if none found.
func (m *CreateExchangerOfferGroupRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateExchangerOfferGroupRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Description

	if all {
		switch v := interface{}(m.GetEOGroupRewardUnitsCapUserAggregate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateExchangerOfferGroupRequestValidationError{
					field:  "EOGroupRewardUnitsCapUserAggregate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateExchangerOfferGroupRequestValidationError{
					field:  "EOGroupRewardUnitsCapUserAggregate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEOGroupRewardUnitsCapUserAggregate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateExchangerOfferGroupRequestValidationError{
				field:  "EOGroupRewardUnitsCapUserAggregate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateExchangerOfferGroupRequestMultiError(errors)
	}

	return nil
}

// CreateExchangerOfferGroupRequestMultiError is an error wrapping multiple
// validation errors returned by
// CreateExchangerOfferGroupRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateExchangerOfferGroupRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateExchangerOfferGroupRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateExchangerOfferGroupRequestMultiError) AllErrors() []error { return m }

// CreateExchangerOfferGroupRequestValidationError is the validation error
// returned by CreateExchangerOfferGroupRequest.Validate if the designated
// constraints aren't met.
type CreateExchangerOfferGroupRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateExchangerOfferGroupRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateExchangerOfferGroupRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateExchangerOfferGroupRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateExchangerOfferGroupRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateExchangerOfferGroupRequestValidationError) ErrorName() string {
	return "CreateExchangerOfferGroupRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateExchangerOfferGroupRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateExchangerOfferGroupRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateExchangerOfferGroupRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateExchangerOfferGroupRequestValidationError{}

// Validate checks the field values on CreateExchangerOfferGroupResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateExchangerOfferGroupResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateExchangerOfferGroupResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreateExchangerOfferGroupResponseMultiError, or nil if none found.
func (m *CreateExchangerOfferGroupResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateExchangerOfferGroupResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateExchangerOfferGroupResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateExchangerOfferGroupResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateExchangerOfferGroupResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExchangerOfferGroup()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateExchangerOfferGroupResponseValidationError{
					field:  "ExchangerOfferGroup",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateExchangerOfferGroupResponseValidationError{
					field:  "ExchangerOfferGroup",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExchangerOfferGroup()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateExchangerOfferGroupResponseValidationError{
				field:  "ExchangerOfferGroup",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateExchangerOfferGroupResponseMultiError(errors)
	}

	return nil
}

// CreateExchangerOfferGroupResponseMultiError is an error wrapping multiple
// validation errors returned by
// CreateExchangerOfferGroupResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateExchangerOfferGroupResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateExchangerOfferGroupResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateExchangerOfferGroupResponseMultiError) AllErrors() []error { return m }

// CreateExchangerOfferGroupResponseValidationError is the validation error
// returned by CreateExchangerOfferGroupResponse.Validate if the designated
// constraints aren't met.
type CreateExchangerOfferGroupResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateExchangerOfferGroupResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateExchangerOfferGroupResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateExchangerOfferGroupResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateExchangerOfferGroupResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateExchangerOfferGroupResponseValidationError) ErrorName() string {
	return "CreateExchangerOfferGroupResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateExchangerOfferGroupResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateExchangerOfferGroupResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateExchangerOfferGroupResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateExchangerOfferGroupResponseValidationError{}

// Validate checks the field values on IncrementExchangerOfferInventoryRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *IncrementExchangerOfferInventoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// IncrementExchangerOfferInventoryRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// IncrementExchangerOfferInventoryRequestMultiError, or nil if none found.
func (m *IncrementExchangerOfferInventoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *IncrementExchangerOfferInventoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ExchangerOfferInventoryId

	if m.GetIncrementCount() <= 0 {
		err := IncrementExchangerOfferInventoryRequestValidationError{
			field:  "IncrementCount",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return IncrementExchangerOfferInventoryRequestMultiError(errors)
	}

	return nil
}

// IncrementExchangerOfferInventoryRequestMultiError is an error wrapping
// multiple validation errors returned by
// IncrementExchangerOfferInventoryRequest.ValidateAll() if the designated
// constraints aren't met.
type IncrementExchangerOfferInventoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IncrementExchangerOfferInventoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IncrementExchangerOfferInventoryRequestMultiError) AllErrors() []error { return m }

// IncrementExchangerOfferInventoryRequestValidationError is the validation
// error returned by IncrementExchangerOfferInventoryRequest.Validate if the
// designated constraints aren't met.
type IncrementExchangerOfferInventoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IncrementExchangerOfferInventoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IncrementExchangerOfferInventoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IncrementExchangerOfferInventoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IncrementExchangerOfferInventoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IncrementExchangerOfferInventoryRequestValidationError) ErrorName() string {
	return "IncrementExchangerOfferInventoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e IncrementExchangerOfferInventoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIncrementExchangerOfferInventoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IncrementExchangerOfferInventoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IncrementExchangerOfferInventoryRequestValidationError{}

// Validate checks the field values on IncrementExchangerOfferInventoryResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *IncrementExchangerOfferInventoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// IncrementExchangerOfferInventoryResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// IncrementExchangerOfferInventoryResponseMultiError, or nil if none found.
func (m *IncrementExchangerOfferInventoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *IncrementExchangerOfferInventoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IncrementExchangerOfferInventoryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IncrementExchangerOfferInventoryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IncrementExchangerOfferInventoryResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExchangerOfferInventory()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IncrementExchangerOfferInventoryResponseValidationError{
					field:  "ExchangerOfferInventory",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IncrementExchangerOfferInventoryResponseValidationError{
					field:  "ExchangerOfferInventory",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExchangerOfferInventory()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IncrementExchangerOfferInventoryResponseValidationError{
				field:  "ExchangerOfferInventory",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return IncrementExchangerOfferInventoryResponseMultiError(errors)
	}

	return nil
}

// IncrementExchangerOfferInventoryResponseMultiError is an error wrapping
// multiple validation errors returned by
// IncrementExchangerOfferInventoryResponse.ValidateAll() if the designated
// constraints aren't met.
type IncrementExchangerOfferInventoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IncrementExchangerOfferInventoryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IncrementExchangerOfferInventoryResponseMultiError) AllErrors() []error { return m }

// IncrementExchangerOfferInventoryResponseValidationError is the validation
// error returned by IncrementExchangerOfferInventoryResponse.Validate if the
// designated constraints aren't met.
type IncrementExchangerOfferInventoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IncrementExchangerOfferInventoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IncrementExchangerOfferInventoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IncrementExchangerOfferInventoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IncrementExchangerOfferInventoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IncrementExchangerOfferInventoryResponseValidationError) ErrorName() string {
	return "IncrementExchangerOfferInventoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e IncrementExchangerOfferInventoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIncrementExchangerOfferInventoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IncrementExchangerOfferInventoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IncrementExchangerOfferInventoryResponseValidationError{}

// Validate checks the field values on
// DecryptExchangerOfferOrdersDetailsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DecryptExchangerOfferOrdersDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// DecryptExchangerOfferOrdersDetailsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// DecryptExchangerOfferOrdersDetailsRequestMultiError, or nil if none found.
func (m *DecryptExchangerOfferOrdersDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DecryptExchangerOfferOrdersDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetExchangerOfferOrders() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DecryptExchangerOfferOrdersDetailsRequestValidationError{
						field:  fmt.Sprintf("ExchangerOfferOrders[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DecryptExchangerOfferOrdersDetailsRequestValidationError{
						field:  fmt.Sprintf("ExchangerOfferOrders[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DecryptExchangerOfferOrdersDetailsRequestValidationError{
					field:  fmt.Sprintf("ExchangerOfferOrders[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DecryptExchangerOfferOrdersDetailsRequestMultiError(errors)
	}

	return nil
}

// DecryptExchangerOfferOrdersDetailsRequestMultiError is an error wrapping
// multiple validation errors returned by
// DecryptExchangerOfferOrdersDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type DecryptExchangerOfferOrdersDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DecryptExchangerOfferOrdersDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DecryptExchangerOfferOrdersDetailsRequestMultiError) AllErrors() []error { return m }

// DecryptExchangerOfferOrdersDetailsRequestValidationError is the validation
// error returned by DecryptExchangerOfferOrdersDetailsRequest.Validate if the
// designated constraints aren't met.
type DecryptExchangerOfferOrdersDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DecryptExchangerOfferOrdersDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DecryptExchangerOfferOrdersDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DecryptExchangerOfferOrdersDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DecryptExchangerOfferOrdersDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DecryptExchangerOfferOrdersDetailsRequestValidationError) ErrorName() string {
	return "DecryptExchangerOfferOrdersDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DecryptExchangerOfferOrdersDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDecryptExchangerOfferOrdersDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DecryptExchangerOfferOrdersDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DecryptExchangerOfferOrdersDetailsRequestValidationError{}

// Validate checks the field values on
// DecryptExchangerOfferOrdersDetailsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DecryptExchangerOfferOrdersDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// DecryptExchangerOfferOrdersDetailsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// DecryptExchangerOfferOrdersDetailsResponseMultiError, or nil if none found.
func (m *DecryptExchangerOfferOrdersDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DecryptExchangerOfferOrdersDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DecryptExchangerOfferOrdersDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DecryptExchangerOfferOrdersDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DecryptExchangerOfferOrdersDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetExchangerOfferOrders() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DecryptExchangerOfferOrdersDetailsResponseValidationError{
						field:  fmt.Sprintf("ExchangerOfferOrders[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DecryptExchangerOfferOrdersDetailsResponseValidationError{
						field:  fmt.Sprintf("ExchangerOfferOrders[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DecryptExchangerOfferOrdersDetailsResponseValidationError{
					field:  fmt.Sprintf("ExchangerOfferOrders[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DecryptExchangerOfferOrdersDetailsResponseMultiError(errors)
	}

	return nil
}

// DecryptExchangerOfferOrdersDetailsResponseMultiError is an error wrapping
// multiple validation errors returned by
// DecryptExchangerOfferOrdersDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type DecryptExchangerOfferOrdersDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DecryptExchangerOfferOrdersDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DecryptExchangerOfferOrdersDetailsResponseMultiError) AllErrors() []error { return m }

// DecryptExchangerOfferOrdersDetailsResponseValidationError is the validation
// error returned by DecryptExchangerOfferOrdersDetailsResponse.Validate if
// the designated constraints aren't met.
type DecryptExchangerOfferOrdersDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DecryptExchangerOfferOrdersDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DecryptExchangerOfferOrdersDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DecryptExchangerOfferOrdersDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DecryptExchangerOfferOrdersDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DecryptExchangerOfferOrdersDetailsResponseValidationError) ErrorName() string {
	return "DecryptExchangerOfferOrdersDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DecryptExchangerOfferOrdersDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDecryptExchangerOfferOrdersDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DecryptExchangerOfferOrdersDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DecryptExchangerOfferOrdersDetailsResponseValidationError{}

// Validate checks the field values on
// GetRedemptionCountsForActorOfferIdsInMonthRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetRedemptionCountsForActorOfferIdsInMonthRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetRedemptionCountsForActorOfferIdsInMonthRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetRedemptionCountsForActorOfferIdsInMonthRequestMultiError, or nil if none found.
func (m *GetRedemptionCountsForActorOfferIdsInMonthRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRedemptionCountsForActorOfferIdsInMonthRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetActorId()); l < 4 || l > 100 {
		err := GetRedemptionCountsForActorOfferIdsInMonthRequestValidationError{
			field:  "ActorId",
			reason: "value length must be between 4 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetOfferIds()) < 1 {
		err := GetRedemptionCountsForActorOfferIdsInMonthRequestValidationError{
			field:  "OfferIds",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetMonthTimestamp() == nil {
		err := GetRedemptionCountsForActorOfferIdsInMonthRequestValidationError{
			field:  "MonthTimestamp",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetRedemptionCountsForActorOfferIdsInMonthRequestMultiError(errors)
	}

	return nil
}

// GetRedemptionCountsForActorOfferIdsInMonthRequestMultiError is an error
// wrapping multiple validation errors returned by
// GetRedemptionCountsForActorOfferIdsInMonthRequest.ValidateAll() if the
// designated constraints aren't met.
type GetRedemptionCountsForActorOfferIdsInMonthRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRedemptionCountsForActorOfferIdsInMonthRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRedemptionCountsForActorOfferIdsInMonthRequestMultiError) AllErrors() []error { return m }

// GetRedemptionCountsForActorOfferIdsInMonthRequestValidationError is the
// validation error returned by
// GetRedemptionCountsForActorOfferIdsInMonthRequest.Validate if the
// designated constraints aren't met.
type GetRedemptionCountsForActorOfferIdsInMonthRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRedemptionCountsForActorOfferIdsInMonthRequestValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetRedemptionCountsForActorOfferIdsInMonthRequestValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetRedemptionCountsForActorOfferIdsInMonthRequestValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetRedemptionCountsForActorOfferIdsInMonthRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRedemptionCountsForActorOfferIdsInMonthRequestValidationError) ErrorName() string {
	return "GetRedemptionCountsForActorOfferIdsInMonthRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRedemptionCountsForActorOfferIdsInMonthRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRedemptionCountsForActorOfferIdsInMonthRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRedemptionCountsForActorOfferIdsInMonthRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRedemptionCountsForActorOfferIdsInMonthRequestValidationError{}

// Validate checks the field values on
// GetRedemptionCountsForActorOfferIdsInMonthResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetRedemptionCountsForActorOfferIdsInMonthResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetRedemptionCountsForActorOfferIdsInMonthResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetRedemptionCountsForActorOfferIdsInMonthResponseMultiError, or nil if
// none found.
func (m *GetRedemptionCountsForActorOfferIdsInMonthResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRedemptionCountsForActorOfferIdsInMonthResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRedemptionCountsForActorOfferIdsInMonthResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRedemptionCountsForActorOfferIdsInMonthResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRedemptionCountsForActorOfferIdsInMonthResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OfferIdToRedemptionsCountInMonthMap

	if len(errors) > 0 {
		return GetRedemptionCountsForActorOfferIdsInMonthResponseMultiError(errors)
	}

	return nil
}

// GetRedemptionCountsForActorOfferIdsInMonthResponseMultiError is an error
// wrapping multiple validation errors returned by
// GetRedemptionCountsForActorOfferIdsInMonthResponse.ValidateAll() if the
// designated constraints aren't met.
type GetRedemptionCountsForActorOfferIdsInMonthResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRedemptionCountsForActorOfferIdsInMonthResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRedemptionCountsForActorOfferIdsInMonthResponseMultiError) AllErrors() []error { return m }

// GetRedemptionCountsForActorOfferIdsInMonthResponseValidationError is the
// validation error returned by
// GetRedemptionCountsForActorOfferIdsInMonthResponse.Validate if the
// designated constraints aren't met.
type GetRedemptionCountsForActorOfferIdsInMonthResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRedemptionCountsForActorOfferIdsInMonthResponseValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetRedemptionCountsForActorOfferIdsInMonthResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetRedemptionCountsForActorOfferIdsInMonthResponseValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetRedemptionCountsForActorOfferIdsInMonthResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRedemptionCountsForActorOfferIdsInMonthResponseValidationError) ErrorName() string {
	return "GetRedemptionCountsForActorOfferIdsInMonthResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRedemptionCountsForActorOfferIdsInMonthResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRedemptionCountsForActorOfferIdsInMonthResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRedemptionCountsForActorOfferIdsInMonthResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRedemptionCountsForActorOfferIdsInMonthResponseValidationError{}

// Validate checks the field values on GetExchangerOffersByFiltersRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetExchangerOffersByFiltersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetExchangerOffersByFiltersRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetExchangerOffersByFiltersRequestMultiError, or nil if none found.
func (m *GetExchangerOffersByFiltersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExchangerOffersByFiltersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetFilters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOffersByFiltersRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOffersByFiltersRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOffersByFiltersRequestValidationError{
				field:  "Filters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetExchangerOffersByFiltersRequestMultiError(errors)
	}

	return nil
}

// GetExchangerOffersByFiltersRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetExchangerOffersByFiltersRequest.ValidateAll() if the designated
// constraints aren't met.
type GetExchangerOffersByFiltersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExchangerOffersByFiltersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExchangerOffersByFiltersRequestMultiError) AllErrors() []error { return m }

// GetExchangerOffersByFiltersRequestValidationError is the validation error
// returned by GetExchangerOffersByFiltersRequest.Validate if the designated
// constraints aren't met.
type GetExchangerOffersByFiltersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExchangerOffersByFiltersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExchangerOffersByFiltersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExchangerOffersByFiltersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExchangerOffersByFiltersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExchangerOffersByFiltersRequestValidationError) ErrorName() string {
	return "GetExchangerOffersByFiltersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetExchangerOffersByFiltersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExchangerOffersByFiltersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExchangerOffersByFiltersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExchangerOffersByFiltersRequestValidationError{}

// Validate checks the field values on ExchangerOfferFilters with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExchangerOfferFilters) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExchangerOfferFilters with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExchangerOfferFiltersMultiError, or nil if none found.
func (m *ExchangerOfferFilters) ValidateAll() error {
	return m.validate(true)
}

func (m *ExchangerOfferFilters) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCatalogFilters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExchangerOfferFiltersValidationError{
					field:  "CatalogFilters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExchangerOfferFiltersValidationError{
					field:  "CatalogFilters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCatalogFilters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExchangerOfferFiltersValidationError{
				field:  "CatalogFilters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Status

	if all {
		switch v := interface{}(m.GetFromTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExchangerOfferFiltersValidationError{
					field:  "FromTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExchangerOfferFiltersValidationError{
					field:  "FromTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExchangerOfferFiltersValidationError{
				field:  "FromTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTillTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExchangerOfferFiltersValidationError{
					field:  "TillTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExchangerOfferFiltersValidationError{
					field:  "TillTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTillTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExchangerOfferFiltersValidationError{
				field:  "TillTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ExchangerOfferFiltersMultiError(errors)
	}

	return nil
}

// ExchangerOfferFiltersMultiError is an error wrapping multiple validation
// errors returned by ExchangerOfferFilters.ValidateAll() if the designated
// constraints aren't met.
type ExchangerOfferFiltersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExchangerOfferFiltersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExchangerOfferFiltersMultiError) AllErrors() []error { return m }

// ExchangerOfferFiltersValidationError is the validation error returned by
// ExchangerOfferFilters.Validate if the designated constraints aren't met.
type ExchangerOfferFiltersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExchangerOfferFiltersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExchangerOfferFiltersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExchangerOfferFiltersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExchangerOfferFiltersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExchangerOfferFiltersValidationError) ErrorName() string {
	return "ExchangerOfferFiltersValidationError"
}

// Error satisfies the builtin error interface
func (e ExchangerOfferFiltersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExchangerOfferFilters.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExchangerOfferFiltersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExchangerOfferFiltersValidationError{}

// Validate checks the field values on GetExchangerOffersByFiltersResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetExchangerOffersByFiltersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetExchangerOffersByFiltersResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetExchangerOffersByFiltersResponseMultiError, or nil if none found.
func (m *GetExchangerOffersByFiltersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExchangerOffersByFiltersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOffersByFiltersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOffersByFiltersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOffersByFiltersResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetExchangerOffers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetExchangerOffersByFiltersResponseValidationError{
						field:  fmt.Sprintf("ExchangerOffers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetExchangerOffersByFiltersResponseValidationError{
						field:  fmt.Sprintf("ExchangerOffers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetExchangerOffersByFiltersResponseValidationError{
					field:  fmt.Sprintf("ExchangerOffers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	{
		sorted_keys := make([]string, len(m.GetOfferIdToListingMap()))
		i := 0
		for key := range m.GetOfferIdToListingMap() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetOfferIdToListingMap()[key]
			_ = val

			// no validation rules for OfferIdToListingMap[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetExchangerOffersByFiltersResponseValidationError{
							field:  fmt.Sprintf("OfferIdToListingMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetExchangerOffersByFiltersResponseValidationError{
							field:  fmt.Sprintf("OfferIdToListingMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetExchangerOffersByFiltersResponseValidationError{
						field:  fmt.Sprintf("OfferIdToListingMap[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return GetExchangerOffersByFiltersResponseMultiError(errors)
	}

	return nil
}

// GetExchangerOffersByFiltersResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetExchangerOffersByFiltersResponse.ValidateAll() if the designated
// constraints aren't met.
type GetExchangerOffersByFiltersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExchangerOffersByFiltersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExchangerOffersByFiltersResponseMultiError) AllErrors() []error { return m }

// GetExchangerOffersByFiltersResponseValidationError is the validation error
// returned by GetExchangerOffersByFiltersResponse.Validate if the designated
// constraints aren't met.
type GetExchangerOffersByFiltersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExchangerOffersByFiltersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExchangerOffersByFiltersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExchangerOffersByFiltersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExchangerOffersByFiltersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExchangerOffersByFiltersResponseValidationError) ErrorName() string {
	return "GetExchangerOffersByFiltersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetExchangerOffersByFiltersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExchangerOffersByFiltersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExchangerOffersByFiltersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExchangerOffersByFiltersResponseValidationError{}

// Validate checks the field values on
// GetExchangerOffersOrdersSummaryRequest_Filters with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetExchangerOffersOrdersSummaryRequest_Filters) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetExchangerOffersOrdersSummaryRequest_Filters with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetExchangerOffersOrdersSummaryRequest_FiltersMultiError, or nil if none found.
func (m *GetExchangerOffersOrdersSummaryRequest_Filters) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExchangerOffersOrdersSummaryRequest_Filters) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OfferId

	if len(errors) > 0 {
		return GetExchangerOffersOrdersSummaryRequest_FiltersMultiError(errors)
	}

	return nil
}

// GetExchangerOffersOrdersSummaryRequest_FiltersMultiError is an error
// wrapping multiple validation errors returned by
// GetExchangerOffersOrdersSummaryRequest_Filters.ValidateAll() if the
// designated constraints aren't met.
type GetExchangerOffersOrdersSummaryRequest_FiltersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExchangerOffersOrdersSummaryRequest_FiltersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExchangerOffersOrdersSummaryRequest_FiltersMultiError) AllErrors() []error { return m }

// GetExchangerOffersOrdersSummaryRequest_FiltersValidationError is the
// validation error returned by
// GetExchangerOffersOrdersSummaryRequest_Filters.Validate if the designated
// constraints aren't met.
type GetExchangerOffersOrdersSummaryRequest_FiltersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExchangerOffersOrdersSummaryRequest_FiltersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExchangerOffersOrdersSummaryRequest_FiltersValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetExchangerOffersOrdersSummaryRequest_FiltersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExchangerOffersOrdersSummaryRequest_FiltersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExchangerOffersOrdersSummaryRequest_FiltersValidationError) ErrorName() string {
	return "GetExchangerOffersOrdersSummaryRequest_FiltersValidationError"
}

// Error satisfies the builtin error interface
func (e GetExchangerOffersOrdersSummaryRequest_FiltersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExchangerOffersOrdersSummaryRequest_Filters.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExchangerOffersOrdersSummaryRequest_FiltersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExchangerOffersOrdersSummaryRequest_FiltersValidationError{}

// Validate checks the field values on GetExchangerOffersRequest_FiltersV2 with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetExchangerOffersRequest_FiltersV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetExchangerOffersRequest_FiltersV2
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetExchangerOffersRequest_FiltersV2MultiError, or nil if none found.
func (m *GetExchangerOffersRequest_FiltersV2) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExchangerOffersRequest_FiltersV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCatalogFilters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOffersRequest_FiltersV2ValidationError{
					field:  "CatalogFilters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOffersRequest_FiltersV2ValidationError{
					field:  "CatalogFilters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCatalogFilters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOffersRequest_FiltersV2ValidationError{
				field:  "CatalogFilters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Status

	if len(errors) > 0 {
		return GetExchangerOffersRequest_FiltersV2MultiError(errors)
	}

	return nil
}

// GetExchangerOffersRequest_FiltersV2MultiError is an error wrapping multiple
// validation errors returned by
// GetExchangerOffersRequest_FiltersV2.ValidateAll() if the designated
// constraints aren't met.
type GetExchangerOffersRequest_FiltersV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExchangerOffersRequest_FiltersV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExchangerOffersRequest_FiltersV2MultiError) AllErrors() []error { return m }

// GetExchangerOffersRequest_FiltersV2ValidationError is the validation error
// returned by GetExchangerOffersRequest_FiltersV2.Validate if the designated
// constraints aren't met.
type GetExchangerOffersRequest_FiltersV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExchangerOffersRequest_FiltersV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExchangerOffersRequest_FiltersV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExchangerOffersRequest_FiltersV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExchangerOffersRequest_FiltersV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExchangerOffersRequest_FiltersV2ValidationError) ErrorName() string {
	return "GetExchangerOffersRequest_FiltersV2ValidationError"
}

// Error satisfies the builtin error interface
func (e GetExchangerOffersRequest_FiltersV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExchangerOffersRequest_FiltersV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExchangerOffersRequest_FiltersV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExchangerOffersRequest_FiltersV2ValidationError{}

// Validate checks the field values on GetExchangerOffersByIdsRequest_Filters
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetExchangerOffersByIdsRequest_Filters) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetExchangerOffersByIdsRequest_Filters with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetExchangerOffersByIdsRequest_FiltersMultiError, or nil if none found.
func (m *GetExchangerOffersByIdsRequest_Filters) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExchangerOffersByIdsRequest_Filters) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	if len(errors) > 0 {
		return GetExchangerOffersByIdsRequest_FiltersMultiError(errors)
	}

	return nil
}

// GetExchangerOffersByIdsRequest_FiltersMultiError is an error wrapping
// multiple validation errors returned by
// GetExchangerOffersByIdsRequest_Filters.ValidateAll() if the designated
// constraints aren't met.
type GetExchangerOffersByIdsRequest_FiltersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExchangerOffersByIdsRequest_FiltersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExchangerOffersByIdsRequest_FiltersMultiError) AllErrors() []error { return m }

// GetExchangerOffersByIdsRequest_FiltersValidationError is the validation
// error returned by GetExchangerOffersByIdsRequest_Filters.Validate if the
// designated constraints aren't met.
type GetExchangerOffersByIdsRequest_FiltersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExchangerOffersByIdsRequest_FiltersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExchangerOffersByIdsRequest_FiltersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExchangerOffersByIdsRequest_FiltersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExchangerOffersByIdsRequest_FiltersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExchangerOffersByIdsRequest_FiltersValidationError) ErrorName() string {
	return "GetExchangerOffersByIdsRequest_FiltersValidationError"
}

// Error satisfies the builtin error interface
func (e GetExchangerOffersByIdsRequest_FiltersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExchangerOffersByIdsRequest_Filters.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExchangerOffersByIdsRequest_FiltersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExchangerOffersByIdsRequest_FiltersValidationError{}

// Validate checks the field values on
// GetExchangerOfferOrdersForActorRequest_Filters with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetExchangerOfferOrdersForActorRequest_Filters) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetExchangerOfferOrdersForActorRequest_Filters with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetExchangerOfferOrdersForActorRequest_FiltersMultiError, or nil if none found.
func (m *GetExchangerOfferOrdersForActorRequest_Filters) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExchangerOfferOrdersForActorRequest_Filters) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OfferId

	if all {
		switch v := interface{}(m.GetFromDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersForActorRequest_FiltersValidationError{
					field:  "FromDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersForActorRequest_FiltersValidationError{
					field:  "FromDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOfferOrdersForActorRequest_FiltersValidationError{
				field:  "FromDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUptoDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersForActorRequest_FiltersValidationError{
					field:  "UptoDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersForActorRequest_FiltersValidationError{
					field:  "UptoDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUptoDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOfferOrdersForActorRequest_FiltersValidationError{
				field:  "UptoDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RewardExpiryStatus

	if all {
		switch v := interface{}(m.GetWithinOrFilters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersForActorRequest_FiltersValidationError{
					field:  "WithinOrFilters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersForActorRequest_FiltersValidationError{
					field:  "WithinOrFilters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWithinOrFilters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOfferOrdersForActorRequest_FiltersValidationError{
				field:  "WithinOrFilters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetExchangerOfferOrdersForActorRequest_FiltersMultiError(errors)
	}

	return nil
}

// GetExchangerOfferOrdersForActorRequest_FiltersMultiError is an error
// wrapping multiple validation errors returned by
// GetExchangerOfferOrdersForActorRequest_Filters.ValidateAll() if the
// designated constraints aren't met.
type GetExchangerOfferOrdersForActorRequest_FiltersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExchangerOfferOrdersForActorRequest_FiltersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExchangerOfferOrdersForActorRequest_FiltersMultiError) AllErrors() []error { return m }

// GetExchangerOfferOrdersForActorRequest_FiltersValidationError is the
// validation error returned by
// GetExchangerOfferOrdersForActorRequest_Filters.Validate if the designated
// constraints aren't met.
type GetExchangerOfferOrdersForActorRequest_FiltersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExchangerOfferOrdersForActorRequest_FiltersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExchangerOfferOrdersForActorRequest_FiltersValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetExchangerOfferOrdersForActorRequest_FiltersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExchangerOfferOrdersForActorRequest_FiltersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExchangerOfferOrdersForActorRequest_FiltersValidationError) ErrorName() string {
	return "GetExchangerOfferOrdersForActorRequest_FiltersValidationError"
}

// Error satisfies the builtin error interface
func (e GetExchangerOfferOrdersForActorRequest_FiltersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExchangerOfferOrdersForActorRequest_Filters.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExchangerOfferOrdersForActorRequest_FiltersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExchangerOfferOrdersForActorRequest_FiltersValidationError{}

// Validate checks the field values on
// GetExchangerOfferOrdersForActorRequest_OrFilters with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetExchangerOfferOrdersForActorRequest_OrFilters) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetExchangerOfferOrdersForActorRequest_OrFilters with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetExchangerOfferOrdersForActorRequest_OrFiltersMultiError, or nil if none found.
func (m *GetExchangerOfferOrdersForActorRequest_OrFilters) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExchangerOfferOrdersForActorRequest_OrFilters) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetExchangerOfferOrdersForActorRequest_OrFiltersMultiError(errors)
	}

	return nil
}

// GetExchangerOfferOrdersForActorRequest_OrFiltersMultiError is an error
// wrapping multiple validation errors returned by
// GetExchangerOfferOrdersForActorRequest_OrFilters.ValidateAll() if the
// designated constraints aren't met.
type GetExchangerOfferOrdersForActorRequest_OrFiltersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExchangerOfferOrdersForActorRequest_OrFiltersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExchangerOfferOrdersForActorRequest_OrFiltersMultiError) AllErrors() []error { return m }

// GetExchangerOfferOrdersForActorRequest_OrFiltersValidationError is the
// validation error returned by
// GetExchangerOfferOrdersForActorRequest_OrFilters.Validate if the designated
// constraints aren't met.
type GetExchangerOfferOrdersForActorRequest_OrFiltersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExchangerOfferOrdersForActorRequest_OrFiltersValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetExchangerOfferOrdersForActorRequest_OrFiltersValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetExchangerOfferOrdersForActorRequest_OrFiltersValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetExchangerOfferOrdersForActorRequest_OrFiltersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExchangerOfferOrdersForActorRequest_OrFiltersValidationError) ErrorName() string {
	return "GetExchangerOfferOrdersForActorRequest_OrFiltersValidationError"
}

// Error satisfies the builtin error interface
func (e GetExchangerOfferOrdersForActorRequest_OrFiltersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExchangerOfferOrdersForActorRequest_OrFilters.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExchangerOfferOrdersForActorRequest_OrFiltersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExchangerOfferOrdersForActorRequest_OrFiltersValidationError{}

// Validate checks the field values on GetExchangerOfferOrdersRequest_Filters
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetExchangerOfferOrdersRequest_Filters) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetExchangerOfferOrdersRequest_Filters with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetExchangerOfferOrdersRequest_FiltersMultiError, or nil if none found.
func (m *GetExchangerOfferOrdersRequest_Filters) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExchangerOfferOrdersRequest_Filters) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OfferId

	if all {
		switch v := interface{}(m.GetFromDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersRequest_FiltersValidationError{
					field:  "FromDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersRequest_FiltersValidationError{
					field:  "FromDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOfferOrdersRequest_FiltersValidationError{
				field:  "FromDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUptoDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersRequest_FiltersValidationError{
					field:  "UptoDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExchangerOfferOrdersRequest_FiltersValidationError{
					field:  "UptoDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUptoDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExchangerOfferOrdersRequest_FiltersValidationError{
				field:  "UptoDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetExchangerOfferOrdersRequest_FiltersMultiError(errors)
	}

	return nil
}

// GetExchangerOfferOrdersRequest_FiltersMultiError is an error wrapping
// multiple validation errors returned by
// GetExchangerOfferOrdersRequest_Filters.ValidateAll() if the designated
// constraints aren't met.
type GetExchangerOfferOrdersRequest_FiltersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExchangerOfferOrdersRequest_FiltersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExchangerOfferOrdersRequest_FiltersMultiError) AllErrors() []error { return m }

// GetExchangerOfferOrdersRequest_FiltersValidationError is the validation
// error returned by GetExchangerOfferOrdersRequest_Filters.Validate if the
// designated constraints aren't met.
type GetExchangerOfferOrdersRequest_FiltersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExchangerOfferOrdersRequest_FiltersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExchangerOfferOrdersRequest_FiltersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExchangerOfferOrdersRequest_FiltersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExchangerOfferOrdersRequest_FiltersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExchangerOfferOrdersRequest_FiltersValidationError) ErrorName() string {
	return "GetExchangerOfferOrdersRequest_FiltersValidationError"
}

// Error satisfies the builtin error interface
func (e GetExchangerOfferOrdersRequest_FiltersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExchangerOfferOrdersRequest_Filters.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExchangerOfferOrdersRequest_FiltersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExchangerOfferOrdersRequest_FiltersValidationError{}
