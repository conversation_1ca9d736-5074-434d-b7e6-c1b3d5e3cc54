//go:generate gen_sql -types=ExchangerOfferRedemptionCurrency,ExchangerOfferDisplayDetails,ExchangerOfferOptionsConfig,ExchangerOfferAggregatesConfig,ExchangerOfferAdditionalDetails,ExchangerOfferStatus

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/casper/exchanger/exchanger_offer.proto

package exchanger

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	casper "github.com/epifi/gamma/api/casper"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ExchangerOfferRedemptionCurrency denotes currency that can be used for redeeming an ExchangerOffer.
type ExchangerOfferRedemptionCurrency int32

const (
	ExchangerOfferRedemptionCurrency_EXCHANGER_OFFER_REDEMPTION_CURRENCY_UNSPECIFIED ExchangerOfferRedemptionCurrency = 0
	ExchangerOfferRedemptionCurrency_EXCHANGER_OFFER_REDEMPTION_CURRENCY_FI_COINS    ExchangerOfferRedemptionCurrency = 1
)

// Enum value maps for ExchangerOfferRedemptionCurrency.
var (
	ExchangerOfferRedemptionCurrency_name = map[int32]string{
		0: "EXCHANGER_OFFER_REDEMPTION_CURRENCY_UNSPECIFIED",
		1: "EXCHANGER_OFFER_REDEMPTION_CURRENCY_FI_COINS",
	}
	ExchangerOfferRedemptionCurrency_value = map[string]int32{
		"EXCHANGER_OFFER_REDEMPTION_CURRENCY_UNSPECIFIED": 0,
		"EXCHANGER_OFFER_REDEMPTION_CURRENCY_FI_COINS":    1,
	}
)

func (x ExchangerOfferRedemptionCurrency) Enum() *ExchangerOfferRedemptionCurrency {
	p := new(ExchangerOfferRedemptionCurrency)
	*p = x
	return p
}

func (x ExchangerOfferRedemptionCurrency) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExchangerOfferRedemptionCurrency) Descriptor() protoreflect.EnumDescriptor {
	return file_api_casper_exchanger_exchanger_offer_proto_enumTypes[0].Descriptor()
}

func (ExchangerOfferRedemptionCurrency) Type() protoreflect.EnumType {
	return &file_api_casper_exchanger_exchanger_offer_proto_enumTypes[0]
}

func (x ExchangerOfferRedemptionCurrency) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExchangerOfferRedemptionCurrency.Descriptor instead.
func (ExchangerOfferRedemptionCurrency) EnumDescriptor() ([]byte, []int) {
	return file_api_casper_exchanger_exchanger_offer_proto_rawDescGZIP(), []int{0}
}

type ExchangerOfferRedemptionAnimationType int32

const (
	ExchangerOfferRedemptionAnimationType_EXCHANGER_OFFER_REDEMPTION_ANIMATION_TYPE_UNSPECIFIED ExchangerOfferRedemptionAnimationType = 0
	ExchangerOfferRedemptionAnimationType_DIGGING_DOG_ANIMATION                                 ExchangerOfferRedemptionAnimationType = 1
	ExchangerOfferRedemptionAnimationType_UNDERWATER_TREASURE_ANIMATION                         ExchangerOfferRedemptionAnimationType = 2
	ExchangerOfferRedemptionAnimationType_CRICKET_CUP_ANIMATION                                 ExchangerOfferRedemptionAnimationType = 3
)

// Enum value maps for ExchangerOfferRedemptionAnimationType.
var (
	ExchangerOfferRedemptionAnimationType_name = map[int32]string{
		0: "EXCHANGER_OFFER_REDEMPTION_ANIMATION_TYPE_UNSPECIFIED",
		1: "DIGGING_DOG_ANIMATION",
		2: "UNDERWATER_TREASURE_ANIMATION",
		3: "CRICKET_CUP_ANIMATION",
	}
	ExchangerOfferRedemptionAnimationType_value = map[string]int32{
		"EXCHANGER_OFFER_REDEMPTION_ANIMATION_TYPE_UNSPECIFIED": 0,
		"DIGGING_DOG_ANIMATION":                                 1,
		"UNDERWATER_TREASURE_ANIMATION":                         2,
		"CRICKET_CUP_ANIMATION":                                 3,
	}
)

func (x ExchangerOfferRedemptionAnimationType) Enum() *ExchangerOfferRedemptionAnimationType {
	p := new(ExchangerOfferRedemptionAnimationType)
	*p = x
	return p
}

func (x ExchangerOfferRedemptionAnimationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExchangerOfferRedemptionAnimationType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_casper_exchanger_exchanger_offer_proto_enumTypes[1].Descriptor()
}

func (ExchangerOfferRedemptionAnimationType) Type() protoreflect.EnumType {
	return &file_api_casper_exchanger_exchanger_offer_proto_enumTypes[1]
}

func (x ExchangerOfferRedemptionAnimationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExchangerOfferRedemptionAnimationType.Descriptor instead.
func (ExchangerOfferRedemptionAnimationType) EnumDescriptor() ([]byte, []int) {
	return file_api_casper_exchanger_exchanger_offer_proto_rawDescGZIP(), []int{1}
}

// ExchangerOfferStatus denotes the status of the exchanger offer
type ExchangerOfferStatus int32

const (
	ExchangerOfferStatus_EXCHANGER_OFFER_STATUS_UNSPECIFIED ExchangerOfferStatus = 0
	// initial/default state of the exchanger offer when its created
	ExchangerOfferStatus_EXCHANGER_OFFER_STATUS_CREATED ExchangerOfferStatus = 1
	// when the exchanger offer is approved by the reviewer
	ExchangerOfferStatus_EXCHANGER_OFFER_STATUS_APPROVED ExchangerOfferStatus = 2
	// exchanger offer inactive
	ExchangerOfferStatus_EXCHANGER_OFFER_STATUS_INACTIVE ExchangerOfferStatus = 3
	// exchanger offer active
	ExchangerOfferStatus_EXCHANGER_OFFER_STATUS_ACTIVE ExchangerOfferStatus = 4
	// exchanger offer terminated, i.e. it can't move to any other state after this
	ExchangerOfferStatus_EXCHANGER_OFFER_STATUS_TERMINATED ExchangerOfferStatus = 5
)

// Enum value maps for ExchangerOfferStatus.
var (
	ExchangerOfferStatus_name = map[int32]string{
		0: "EXCHANGER_OFFER_STATUS_UNSPECIFIED",
		1: "EXCHANGER_OFFER_STATUS_CREATED",
		2: "EXCHANGER_OFFER_STATUS_APPROVED",
		3: "EXCHANGER_OFFER_STATUS_INACTIVE",
		4: "EXCHANGER_OFFER_STATUS_ACTIVE",
		5: "EXCHANGER_OFFER_STATUS_TERMINATED",
	}
	ExchangerOfferStatus_value = map[string]int32{
		"EXCHANGER_OFFER_STATUS_UNSPECIFIED": 0,
		"EXCHANGER_OFFER_STATUS_CREATED":     1,
		"EXCHANGER_OFFER_STATUS_APPROVED":    2,
		"EXCHANGER_OFFER_STATUS_INACTIVE":    3,
		"EXCHANGER_OFFER_STATUS_ACTIVE":      4,
		"EXCHANGER_OFFER_STATUS_TERMINATED":  5,
	}
)

func (x ExchangerOfferStatus) Enum() *ExchangerOfferStatus {
	p := new(ExchangerOfferStatus)
	*p = x
	return p
}

func (x ExchangerOfferStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExchangerOfferStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_casper_exchanger_exchanger_offer_proto_enumTypes[2].Descriptor()
}

func (ExchangerOfferStatus) Type() protoreflect.EnumType {
	return &file_api_casper_exchanger_exchanger_offer_proto_enumTypes[2]
}

func (x ExchangerOfferStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExchangerOfferStatus.Descriptor instead.
func (ExchangerOfferStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_casper_exchanger_exchanger_offer_proto_rawDescGZIP(), []int{2}
}

// ExchangerOffer is an offer that could be redeemed using a given currency (like FI_COINS) to get a set of reward options and
// the user can choose to claim any one of those reward options.
type ExchangerOffer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unique id of an exchanger offer.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// currency through which given ExchangerOffer can be redeemed like FI_COINS.
	RedemptionCurrency ExchangerOfferRedemptionCurrency `protobuf:"varint,2,opt,name=redemption_currency,json=redemptionCurrency,proto3,enum=casper.exchanger.ExchangerOfferRedemptionCurrency" json:"redemption_currency,omitempty"`
	// price of redeeming the ExchangerOffer.
	RedemptionPrice float32 `protobuf:"fixed32,3,opt,name=redemption_price,json=redemptionPrice,proto3" json:"redemption_price,omitempty"`
	// display details of the offer.
	OfferDisplayDetails *ExchangerOfferDisplayDetails `protobuf:"bytes,4,opt,name=offer_display_details,json=offerDisplayDetails,proto3" json:"offer_display_details,omitempty"`
	// config to generate reward options on redeeming the ExchangerOffer.
	OfferOptionsConfig *ExchangerOfferOptionsConfig `protobuf:"bytes,5,opt,name=offer_options_config,json=offerOptionsConfig,proto3" json:"offer_options_config,omitempty"`
	// stores aggregates related config for an ExchangerOffer.
	OfferAggregatesConfig *ExchangerOfferAggregatesConfig `protobuf:"bytes,6,opt,name=offer_aggregates_config,json=offerAggregatesConfig,proto3" json:"offer_aggregates_config,omitempty"`
	// external_id is equivalent to exchanger offer order id, but this external_id can be displayed
	// on the app while Id shouldn't be due to security reasons.
	ExternalId string `protobuf:"bytes,7,opt,name=external_id,json=externalId,proto3" json:"external_id,omitempty"`
	// intentional gap in field number to accommodate new fields
	// created at
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// updated_at
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// group id to which the exchanger offer belongs
	GroupId string `protobuf:"bytes,12,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	// information related to tags applied to this offer (manually or automatically)
	TagsInfo *casper.TagsInfo `protobuf:"bytes,13,opt,name=tags_info,json=tagsInfo,proto3" json:"tags_info,omitempty"`
	// additional details related to offer
	AdditionalDetails *ExchangerOfferAdditionalDetails `protobuf:"bytes,14,opt,name=additional_details,json=additionalDetails,proto3" json:"additional_details,omitempty"`
	// status of the offer
	Status ExchangerOfferStatus `protobuf:"varint,15,opt,name=status,proto3,enum=casper.exchanger.ExchangerOfferStatus" json:"status,omitempty"`
}

func (x *ExchangerOffer) Reset() {
	*x = ExchangerOffer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_exchanger_offer_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangerOffer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangerOffer) ProtoMessage() {}

func (x *ExchangerOffer) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_exchanger_offer_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangerOffer.ProtoReflect.Descriptor instead.
func (*ExchangerOffer) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_exchanger_offer_proto_rawDescGZIP(), []int{0}
}

func (x *ExchangerOffer) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ExchangerOffer) GetRedemptionCurrency() ExchangerOfferRedemptionCurrency {
	if x != nil {
		return x.RedemptionCurrency
	}
	return ExchangerOfferRedemptionCurrency_EXCHANGER_OFFER_REDEMPTION_CURRENCY_UNSPECIFIED
}

func (x *ExchangerOffer) GetRedemptionPrice() float32 {
	if x != nil {
		return x.RedemptionPrice
	}
	return 0
}

func (x *ExchangerOffer) GetOfferDisplayDetails() *ExchangerOfferDisplayDetails {
	if x != nil {
		return x.OfferDisplayDetails
	}
	return nil
}

func (x *ExchangerOffer) GetOfferOptionsConfig() *ExchangerOfferOptionsConfig {
	if x != nil {
		return x.OfferOptionsConfig
	}
	return nil
}

func (x *ExchangerOffer) GetOfferAggregatesConfig() *ExchangerOfferAggregatesConfig {
	if x != nil {
		return x.OfferAggregatesConfig
	}
	return nil
}

func (x *ExchangerOffer) GetExternalId() string {
	if x != nil {
		return x.ExternalId
	}
	return ""
}

func (x *ExchangerOffer) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *ExchangerOffer) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *ExchangerOffer) GetGroupId() string {
	if x != nil {
		return x.GroupId
	}
	return ""
}

func (x *ExchangerOffer) GetTagsInfo() *casper.TagsInfo {
	if x != nil {
		return x.TagsInfo
	}
	return nil
}

func (x *ExchangerOffer) GetAdditionalDetails() *ExchangerOfferAdditionalDetails {
	if x != nil {
		return x.AdditionalDetails
	}
	return nil
}

func (x *ExchangerOffer) GetStatus() ExchangerOfferStatus {
	if x != nil {
		return x.Status
	}
	return ExchangerOfferStatus_EXCHANGER_OFFER_STATUS_UNSPECIFIED
}

// ExchangerOfferDisplayDetails stores all the display related properties of the ExchangerOffer.
type ExchangerOfferDisplayDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// offer title displayed on offer tile on exchanger catalog Eg: "Let your Fi-Coins pay for stuff".
	Title string `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// offer subtitle displayed on offer tile on exchanger catalog Eg: "Trade your Fi-Coins for cash".
	Subtitle string `protobuf:"bytes,2,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	// offer desc displayed on clicking the offer tile Eg: "Win exciting cashback rewards".
	Desc string `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	// image used for offer tile on exchanger catalog.
	ImageUrl string `protobuf:"bytes,4,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	// hex encoded background color of offer tile.
	TileBgColor string `protobuf:"bytes,5,opt,name=tile_bg_color,json=tileBgColor,proto3" json:"tile_bg_color,omitempty"`
	// ranking to be used while ordering/sorting offers, i.e. lower the value, higher the precedence during ordering
	// will be used as a default rank if screen rank is not provided in screen_display_rank_map
	DisplayRank int32 `protobuf:"varint,6,opt,name=display_rank,json=displayRank,proto3" json:"display_rank,omitempty"`
	// for e.g. "Trade your Fi-Coins for cash" in "View Details" section
	InfoBannerTitle string `protobuf:"bytes,7,opt,name=info_banner_title,json=infoBannerTitle,proto3" json:"info_banner_title,omitempty"`
	// background color of the banner in "View Details" section
	InfoBannerBgColor string `protobuf:"bytes,8,opt,name=info_banner_bg_color,json=infoBannerBgColor,proto3" json:"info_banner_bg_color,omitempty"`
	// icon used in the banner of "View Details" section
	InfoBannerIconUrl string `protobuf:"bytes,9,opt,name=info_banner_icon_url,json=infoBannerIconUrl,proto3" json:"info_banner_icon_url,omitempty"`
	// how to redeem steps
	HowToRedeem []string `protobuf:"bytes,10,rep,name=how_to_redeem,json=howToRedeem,proto3" json:"how_to_redeem,omitempty"`
	// terms and conditions
	Tnc []string `protobuf:"bytes,11,rep,name=tnc,proto3" json:"tnc,omitempty"`
	// animation that is shown on exchanger offer redemption screen
	// if unspecified, client needs to fallback to default animation
	RedemptionAnimationType ExchangerOfferRedemptionAnimationType `protobuf:"varint,12,opt,name=redemption_animation_type,json=redemptionAnimationType,proto3,enum=casper.exchanger.ExchangerOfferRedemptionAnimationType" json:"redemption_animation_type,omitempty"`
	// tag to be shown for salary account purpose.
	// Deprecated: Use is_salary_account_exclusive instead
	//
	// Deprecated: Marked as deprecated in api/casper/exchanger/exchanger_offer.proto.
	SalaryAccountTag *common.Text `protobuf:"bytes,13,opt,name=salary_account_tag,json=salaryAccountTag,proto3" json:"salary_account_tag,omitempty"`
	// offer title shown on home page offer tile
	HomeTitle string `protobuf:"bytes,14,opt,name=home_title,json=homeTitle,proto3" json:"home_title,omitempty"`
	// brand name of the product being offered
	BrandName string `protobuf:"bytes,15,opt,name=brand_name,json=brandName,proto3" json:"brand_name,omitempty"`
	// primary image to be used for displaying on home, and other tiles
	// this type will decide the positioning of the image on the tile
	// image_url will map to product, info_banner_icon_url will map to logo image content type
	TileImageContentType common.ImageContentType `protobuf:"varint,16,opt,name=tile_image_content_type,json=tileImageContentType,proto3,enum=api.typesv2.common.ImageContentType" json:"tile_image_content_type,omitempty"`
	// constraint expression to be used to show/hide offer based on certain criteria
	// eg - IsUserSalaryProgramActive() || IsUserCreditCardActive()
	DisplayConstraintExpression string `protobuf:"bytes,17,opt,name=display_constraint_expression,json=displayConstraintExpression,proto3" json:"display_constraint_expression,omitempty"`
	// map of frontend.deeplink.Screen name vs exchanger offer display rank
	// will be used to enforce ordering of offers on screen basis
	ScreenDisplayRankMap map[string]int32 `protobuf:"bytes,18,rep,name=screen_display_rank_map,json=screenDisplayRankMap,proto3" json:"screen_display_rank_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	// by default, hide_top_label will be false, hence we will show the label.
	// if we want to hide the label we can set it to true
	HideTopLabel bool `protobuf:"varint,19,opt,name=hide_top_label,json=hideTopLabel,proto3" json:"hide_top_label,omitempty"`
	// boolean flag to indicate whether the offer is experimental or not
	// offer experiments will run in the following manner -
	// whenever a new experimental offer is created, a quest variable of the form casper/exchangerOffer/offer_id is created
	// once all the offers of an experiment are created, a quest experiment is started with multiple variants
	// each experiment variant will have a set of boolean variables formed while creating the experimental offers
	// for the variant user is a part of, only those offers will be visible which have the boolean variable set as true
	IsExperimental bool `protobuf:"varint,20,opt,name=is_experimental,json=isExperimental,proto3" json:"is_experimental,omitempty"`
	// optional, if set then the offer will be visible for those actors for whom this expression is true
	SegmentExpression string `protobuf:"bytes,21,opt,name=segment_expression,json=segmentExpression,proto3" json:"segment_expression,omitempty"`
	// short title of the offer (shorter version for title which is shown on the offer card)
	// currently to be used for SDUI offer card title
	ShortTitle string `protobuf:"bytes,22,opt,name=short_title,json=shortTitle,proto3" json:"short_title,omitempty"`
}

func (x *ExchangerOfferDisplayDetails) Reset() {
	*x = ExchangerOfferDisplayDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_exchanger_offer_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangerOfferDisplayDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangerOfferDisplayDetails) ProtoMessage() {}

func (x *ExchangerOfferDisplayDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_exchanger_offer_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangerOfferDisplayDetails.ProtoReflect.Descriptor instead.
func (*ExchangerOfferDisplayDetails) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_exchanger_offer_proto_rawDescGZIP(), []int{1}
}

func (x *ExchangerOfferDisplayDetails) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ExchangerOfferDisplayDetails) GetSubtitle() string {
	if x != nil {
		return x.Subtitle
	}
	return ""
}

func (x *ExchangerOfferDisplayDetails) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *ExchangerOfferDisplayDetails) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

func (x *ExchangerOfferDisplayDetails) GetTileBgColor() string {
	if x != nil {
		return x.TileBgColor
	}
	return ""
}

func (x *ExchangerOfferDisplayDetails) GetDisplayRank() int32 {
	if x != nil {
		return x.DisplayRank
	}
	return 0
}

func (x *ExchangerOfferDisplayDetails) GetInfoBannerTitle() string {
	if x != nil {
		return x.InfoBannerTitle
	}
	return ""
}

func (x *ExchangerOfferDisplayDetails) GetInfoBannerBgColor() string {
	if x != nil {
		return x.InfoBannerBgColor
	}
	return ""
}

func (x *ExchangerOfferDisplayDetails) GetInfoBannerIconUrl() string {
	if x != nil {
		return x.InfoBannerIconUrl
	}
	return ""
}

func (x *ExchangerOfferDisplayDetails) GetHowToRedeem() []string {
	if x != nil {
		return x.HowToRedeem
	}
	return nil
}

func (x *ExchangerOfferDisplayDetails) GetTnc() []string {
	if x != nil {
		return x.Tnc
	}
	return nil
}

func (x *ExchangerOfferDisplayDetails) GetRedemptionAnimationType() ExchangerOfferRedemptionAnimationType {
	if x != nil {
		return x.RedemptionAnimationType
	}
	return ExchangerOfferRedemptionAnimationType_EXCHANGER_OFFER_REDEMPTION_ANIMATION_TYPE_UNSPECIFIED
}

// Deprecated: Marked as deprecated in api/casper/exchanger/exchanger_offer.proto.
func (x *ExchangerOfferDisplayDetails) GetSalaryAccountTag() *common.Text {
	if x != nil {
		return x.SalaryAccountTag
	}
	return nil
}

func (x *ExchangerOfferDisplayDetails) GetHomeTitle() string {
	if x != nil {
		return x.HomeTitle
	}
	return ""
}

func (x *ExchangerOfferDisplayDetails) GetBrandName() string {
	if x != nil {
		return x.BrandName
	}
	return ""
}

func (x *ExchangerOfferDisplayDetails) GetTileImageContentType() common.ImageContentType {
	if x != nil {
		return x.TileImageContentType
	}
	return common.ImageContentType(0)
}

func (x *ExchangerOfferDisplayDetails) GetDisplayConstraintExpression() string {
	if x != nil {
		return x.DisplayConstraintExpression
	}
	return ""
}

func (x *ExchangerOfferDisplayDetails) GetScreenDisplayRankMap() map[string]int32 {
	if x != nil {
		return x.ScreenDisplayRankMap
	}
	return nil
}

func (x *ExchangerOfferDisplayDetails) GetHideTopLabel() bool {
	if x != nil {
		return x.HideTopLabel
	}
	return false
}

func (x *ExchangerOfferDisplayDetails) GetIsExperimental() bool {
	if x != nil {
		return x.IsExperimental
	}
	return false
}

func (x *ExchangerOfferDisplayDetails) GetSegmentExpression() string {
	if x != nil {
		return x.SegmentExpression
	}
	return ""
}

func (x *ExchangerOfferDisplayDetails) GetShortTitle() string {
	if x != nil {
		return x.ShortTitle
	}
	return ""
}

// ExchangerOfferAggregatesConfig stores aggregates related config for an ExchangerOffer.
type ExchangerOfferAggregatesConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// max number of times a user can redeem an exchanger_offer per day.
	DailyAllowedAttemptsPerUser uint32 `protobuf:"varint,1,opt,name=daily_allowed_attempts_per_user,json=dailyAllowedAttemptsPerUser,proto3" json:"daily_allowed_attempts_per_user,omitempty"`
	// capping config for reward units based on rewardTypes at an offer level, i.e. across users at offer level
	RewardUnitsCapOfferAggregate *RewardUnitsCapAggregate `protobuf:"bytes,2,opt,name=reward_units_cap_offer_aggregate,json=rewardUnitsCapOfferAggregate,proto3" json:"reward_units_cap_offer_aggregate,omitempty"`
	// max number of redemptions of exchanger offer allowed in a month for an actor
	UserLevelMonthlyRedemptionCap uint32 `protobuf:"varint,3,opt,name=user_level_monthly_redemption_cap,json=userLevelMonthlyRedemptionCap,proto3" json:"user_level_monthly_redemption_cap,omitempty"`
}

func (x *ExchangerOfferAggregatesConfig) Reset() {
	*x = ExchangerOfferAggregatesConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_exchanger_offer_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangerOfferAggregatesConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangerOfferAggregatesConfig) ProtoMessage() {}

func (x *ExchangerOfferAggregatesConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_exchanger_offer_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangerOfferAggregatesConfig.ProtoReflect.Descriptor instead.
func (*ExchangerOfferAggregatesConfig) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_exchanger_offer_proto_rawDescGZIP(), []int{2}
}

func (x *ExchangerOfferAggregatesConfig) GetDailyAllowedAttemptsPerUser() uint32 {
	if x != nil {
		return x.DailyAllowedAttemptsPerUser
	}
	return 0
}

func (x *ExchangerOfferAggregatesConfig) GetRewardUnitsCapOfferAggregate() *RewardUnitsCapAggregate {
	if x != nil {
		return x.RewardUnitsCapOfferAggregate
	}
	return nil
}

func (x *ExchangerOfferAggregatesConfig) GetUserLevelMonthlyRedemptionCap() uint32 {
	if x != nil {
		return x.UserLevelMonthlyRedemptionCap
	}
	return 0
}

// ExchangerOfferOptionsConfig contains config to generate reward options on redeeming an ExchangerOffer.
type ExchangerOfferOptionsConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OptionsConfig []*ExchangerOfferOptionConfig `protobuf:"bytes,1,rep,name=options_config,json=optionsConfig,proto3" json:"options_config,omitempty"`
	// time given to the user to decide which option they want to choose.
	// If choice is not made within this duration then a default option would be automatically chosen.
	DefaultDecideTimeInSecs uint32 `protobuf:"varint,2,opt,name=default_decide_time_in_secs,json=defaultDecideTimeInSecs,proto3" json:"default_decide_time_in_secs,omitempty"`
}

func (x *ExchangerOfferOptionsConfig) Reset() {
	*x = ExchangerOfferOptionsConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_exchanger_offer_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangerOfferOptionsConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangerOfferOptionsConfig) ProtoMessage() {}

func (x *ExchangerOfferOptionsConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_exchanger_offer_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangerOfferOptionsConfig.ProtoReflect.Descriptor instead.
func (*ExchangerOfferOptionsConfig) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_exchanger_offer_proto_rawDescGZIP(), []int{3}
}

func (x *ExchangerOfferOptionsConfig) GetOptionsConfig() []*ExchangerOfferOptionConfig {
	if x != nil {
		return x.OptionsConfig
	}
	return nil
}

func (x *ExchangerOfferOptionsConfig) GetDefaultDecideTimeInSecs() uint32 {
	if x != nil {
		return x.DefaultDecideTimeInSecs
	}
	return 0
}

// ExchangerOfferOptionConfig contains config to generate a single reward option on redeeming an ExchangerOffer.
type ExchangerOfferOptionConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// list of RewardConfigUnit for determining the reward that should be given in current option.
	RewardConfigUnits []*ExchangerOfferOptionConfig_RewardConfigUnit `protobuf:"bytes,1,rep,name=reward_config_units,json=rewardConfigUnits,proto3" json:"reward_config_units,omitempty"`
}

func (x *ExchangerOfferOptionConfig) Reset() {
	*x = ExchangerOfferOptionConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_exchanger_offer_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangerOfferOptionConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangerOfferOptionConfig) ProtoMessage() {}

func (x *ExchangerOfferOptionConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_exchanger_offer_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangerOfferOptionConfig.ProtoReflect.Descriptor instead.
func (*ExchangerOfferOptionConfig) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_exchanger_offer_proto_rawDescGZIP(), []int{4}
}

func (x *ExchangerOfferOptionConfig) GetRewardConfigUnits() []*ExchangerOfferOptionConfig_RewardConfigUnit {
	if x != nil {
		return x.RewardConfigUnits
	}
	return nil
}

// RangeProbabilityUnitsConfig is useful for calculating reward units using static ranges and probabilities.
// Eg: for a max cash reward of 120 the config may look like this:
// ex: [(start: 0, end: 10, percentage: 40), (start: 10, end: 20, percentage: 30), (start: 20, end: 50, percentage: 10),
// (start: 50, end: 80, percentage: 10), (start: 80, end: 110, percentage: 5), (start: 110, end: 120, percentage: 5)]
// this will be calculated with provided probability
// if probability is 1 every time reward will be given
type RangeProbabilityUnitsConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConfigUnits []*RangeProbabilityUnitsConfig_ConfigUnit `protobuf:"bytes,1,rep,name=config_units,json=configUnits,proto3" json:"config_units,omitempty"`
	Probability float32                                   `protobuf:"fixed32,2,opt,name=probability,proto3" json:"probability,omitempty"`
}

func (x *RangeProbabilityUnitsConfig) Reset() {
	*x = RangeProbabilityUnitsConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_exchanger_offer_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RangeProbabilityUnitsConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RangeProbabilityUnitsConfig) ProtoMessage() {}

func (x *RangeProbabilityUnitsConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_exchanger_offer_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RangeProbabilityUnitsConfig.ProtoReflect.Descriptor instead.
func (*RangeProbabilityUnitsConfig) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_exchanger_offer_proto_rawDescGZIP(), []int{5}
}

func (x *RangeProbabilityUnitsConfig) GetConfigUnits() []*RangeProbabilityUnitsConfig_ConfigUnit {
	if x != nil {
		return x.ConfigUnits
	}
	return nil
}

func (x *RangeProbabilityUnitsConfig) GetProbability() float32 {
	if x != nil {
		return x.Probability
	}
	return 0
}

type PhysicalMerchandiseRewardConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// product sku
	SkuId string `protobuf:"bytes,1,opt,name=sku_id,json=skuId,proto3" json:"sku_id,omitempty"`
	// merch value in inr (for instrumentation purposes)
	MerchValue int32 `protobuf:"varint,2,opt,name=merch_value,json=merchValue,proto3" json:"merch_value,omitempty"`
}

func (x *PhysicalMerchandiseRewardConfig) Reset() {
	*x = PhysicalMerchandiseRewardConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_exchanger_offer_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PhysicalMerchandiseRewardConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PhysicalMerchandiseRewardConfig) ProtoMessage() {}

func (x *PhysicalMerchandiseRewardConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_exchanger_offer_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PhysicalMerchandiseRewardConfig.ProtoReflect.Descriptor instead.
func (*PhysicalMerchandiseRewardConfig) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_exchanger_offer_proto_rawDescGZIP(), []int{6}
}

func (x *PhysicalMerchandiseRewardConfig) GetSkuId() string {
	if x != nil {
		return x.SkuId
	}
	return ""
}

func (x *PhysicalMerchandiseRewardConfig) GetMerchValue() int32 {
	if x != nil {
		return x.MerchValue
	}
	return 0
}

type ExchangerOfferAdditionalDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// flag to mark the offer as being salary exclusive
	//
	// Deprecated: Marked as deprecated in api/casper/exchanger/exchanger_offer.proto.
	IsSalaryAccountExclusive bool `protobuf:"varint,1,opt,name=is_salary_account_exclusive,json=isSalaryAccountExclusive,proto3" json:"is_salary_account_exclusive,omitempty"`
	// offerDescription is used to add description about the offer
	OfferDescription *ExchangerOfferAdditionalDetails_OfferDescription `protobuf:"bytes,2,opt,name=offer_description,json=offerDescription,proto3" json:"offer_description,omitempty"`
	// to be used as alternate cta text
	// if populated the cta text for the offer will show this text instead of the default "PLAY WITH 16,000 FI-COINS" text
	// can serve as a templatized string if required
	// eg. 1. Convert {{REDEMPTION_PRICE}} Fi-Coins
	// in the above case {{REDEMPTION_PRICE}} will be replaced by the actual price
	// eg. 2. Convert Fi-Coins
	AlternateCtaText string `protobuf:"bytes,3,opt,name=alternate_cta_text,json=alternateCtaText,proto3" json:"alternate_cta_text,omitempty"`
	// fi-coins to cash conversion ratio for the offer (optional)
	ConversionRatio *FiCoinsToCashConversionRatio `protobuf:"bytes,4,opt,name=conversion_ratio,json=conversionRatio,proto3" json:"conversion_ratio,omitempty"`
	// flag to mark the offer as promotional offer,
	// for fi-coins catalog offers, if this field is set they will be given higher priority over other sort options.
	// this means that all promoted offers will be shown first on the offer catalog, (highest priority)
	// and among them the rank will be decided on the basis of promo_offer_display_rank field
	IsPromoOffer bool `protobuf:"varint,5,opt,name=is_promo_offer,json=isPromoOffer,proto3" json:"is_promo_offer,omitempty"`
	// promo_offer_display_rank is used to enforce display ordering of promoted offers.
	// for promoted offers promo_offer_display_rank will be prioritised over offer_display_rank.
	// lower the rank, earlier that offer should be displayed.
	PromoOfferDisplayRank int32 `protobuf:"varint,6,opt,name=promo_offer_display_rank,json=promoOfferDisplayRank,proto3" json:"promo_offer_display_rank,omitempty"`
	// stores watson config related to the issue category corresponding to which
	// a user activity event or an error activity event needs to be created
	// error activity event involves ticket creation when something goes wrong
	// in an exchanger offer order(redemption) against this offer and
	// resolution when the issue is resolved
	WatsonMeta *WatsonMeta `protobuf:"bytes,7,opt,name=watson_meta,json=watsonMeta,proto3" json:"watson_meta,omitempty"`
	// offer title used for promoting the offer.
	// e.g. to be used for banners
	PromoTitle string `protobuf:"bytes,8,opt,name=promo_title,json=promoTitle,proto3" json:"promo_title,omitempty"`
	// flag to know whether a reward type FI_COINS exchanger offer will be redeemed in the form of FI_POINTS
	// if unspecified, it will be considered as FI_COINS as older offers will not have this field
	IsFiPoints common.BooleanEnum `protobuf:"varint,9,opt,name=is_fi_points,json=isFiPoints,proto3,enum=api.typesv2.common.BooleanEnum" json:"is_fi_points,omitempty"`
}

func (x *ExchangerOfferAdditionalDetails) Reset() {
	*x = ExchangerOfferAdditionalDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_exchanger_offer_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangerOfferAdditionalDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangerOfferAdditionalDetails) ProtoMessage() {}

func (x *ExchangerOfferAdditionalDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_exchanger_offer_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangerOfferAdditionalDetails.ProtoReflect.Descriptor instead.
func (*ExchangerOfferAdditionalDetails) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_exchanger_offer_proto_rawDescGZIP(), []int{7}
}

// Deprecated: Marked as deprecated in api/casper/exchanger/exchanger_offer.proto.
func (x *ExchangerOfferAdditionalDetails) GetIsSalaryAccountExclusive() bool {
	if x != nil {
		return x.IsSalaryAccountExclusive
	}
	return false
}

func (x *ExchangerOfferAdditionalDetails) GetOfferDescription() *ExchangerOfferAdditionalDetails_OfferDescription {
	if x != nil {
		return x.OfferDescription
	}
	return nil
}

func (x *ExchangerOfferAdditionalDetails) GetAlternateCtaText() string {
	if x != nil {
		return x.AlternateCtaText
	}
	return ""
}

func (x *ExchangerOfferAdditionalDetails) GetConversionRatio() *FiCoinsToCashConversionRatio {
	if x != nil {
		return x.ConversionRatio
	}
	return nil
}

func (x *ExchangerOfferAdditionalDetails) GetIsPromoOffer() bool {
	if x != nil {
		return x.IsPromoOffer
	}
	return false
}

func (x *ExchangerOfferAdditionalDetails) GetPromoOfferDisplayRank() int32 {
	if x != nil {
		return x.PromoOfferDisplayRank
	}
	return 0
}

func (x *ExchangerOfferAdditionalDetails) GetWatsonMeta() *WatsonMeta {
	if x != nil {
		return x.WatsonMeta
	}
	return nil
}

func (x *ExchangerOfferAdditionalDetails) GetPromoTitle() string {
	if x != nil {
		return x.PromoTitle
	}
	return ""
}

func (x *ExchangerOfferAdditionalDetails) GetIsFiPoints() common.BooleanEnum {
	if x != nil {
		return x.IsFiPoints
	}
	return common.BooleanEnum(0)
}

// stores the conversion ratio applicable for an offer
type FiCoinsToCashConversionRatio struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// number of fi-coins and it's cash equivalent.
	FiCoinsValue   uint32 `protobuf:"varint,1,opt,name=fi_coins_value,json=fiCoinsValue,proto3" json:"fi_coins_value,omitempty"`
	CashEquivalent uint32 `protobuf:"varint,2,opt,name=cash_equivalent,json=cashEquivalent,proto3" json:"cash_equivalent,omitempty"`
}

func (x *FiCoinsToCashConversionRatio) Reset() {
	*x = FiCoinsToCashConversionRatio{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_exchanger_offer_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FiCoinsToCashConversionRatio) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FiCoinsToCashConversionRatio) ProtoMessage() {}

func (x *FiCoinsToCashConversionRatio) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_exchanger_offer_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FiCoinsToCashConversionRatio.ProtoReflect.Descriptor instead.
func (*FiCoinsToCashConversionRatio) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_exchanger_offer_proto_rawDescGZIP(), []int{8}
}

func (x *FiCoinsToCashConversionRatio) GetFiCoinsValue() uint32 {
	if x != nil {
		return x.FiCoinsValue
	}
	return 0
}

func (x *FiCoinsToCashConversionRatio) GetCashEquivalent() uint32 {
	if x != nil {
		return x.CashEquivalent
	}
	return 0
}

// stores watson config related to the issue category corresponding to which
// a user activity event or an error activity event needs to be created
// error activity event involves ticket creation when something goes wrong
// in an exchanger offer order(redemption) against this offer and
// resolution when the issue is resolved
type WatsonMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// this should be set to true when we want to create a watson event
	// when an exchanger offer order is not successfully processed
	// against this exchanger offer
	// Mapped against IsWatsonEvent when publishing an event
	ShouldCreateWatsonEvent bool   `protobuf:"varint,1,opt,name=should_create_watson_event,json=shouldCreateWatsonEvent,proto3" json:"should_create_watson_event,omitempty"`
	IssueCategoryId         string `protobuf:"bytes,2,opt,name=issue_category_id,json=issueCategoryId,proto3" json:"issue_category_id,omitempty"`
}

func (x *WatsonMeta) Reset() {
	*x = WatsonMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_exchanger_offer_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WatsonMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WatsonMeta) ProtoMessage() {}

func (x *WatsonMeta) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_exchanger_offer_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WatsonMeta.ProtoReflect.Descriptor instead.
func (*WatsonMeta) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_exchanger_offer_proto_rawDescGZIP(), []int{9}
}

func (x *WatsonMeta) GetShouldCreateWatsonEvent() bool {
	if x != nil {
		return x.ShouldCreateWatsonEvent
	}
	return false
}

func (x *WatsonMeta) GetIssueCategoryId() string {
	if x != nil {
		return x.IssueCategoryId
	}
	return ""
}

// stores display config for reward option
type ExchangerOfferOptionConfig_Display struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BeforeClaimTitleExpression    string `protobuf:"bytes,1,opt,name=before_claim_title_expression,json=beforeClaimTitleExpression,proto3" json:"before_claim_title_expression,omitempty"`
	BeforeClaimSubtitleExpression string `protobuf:"bytes,2,opt,name=before_claim_subtitle_expression,json=beforeClaimSubtitleExpression,proto3" json:"before_claim_subtitle_expression,omitempty"`
	BeforeClaimIconUrl            string `protobuf:"bytes,3,opt,name=before_claim_icon_url,json=beforeClaimIconUrl,proto3" json:"before_claim_icon_url,omitempty"`
	AfterClaimTitleExpression     string `protobuf:"bytes,4,opt,name=after_claim_title_expression,json=afterClaimTitleExpression,proto3" json:"after_claim_title_expression,omitempty"`
	AfterClaimSubtitleExpression  string `protobuf:"bytes,5,opt,name=after_claim_subtitle_expression,json=afterClaimSubtitleExpression,proto3" json:"after_claim_subtitle_expression,omitempty"`
	AfterClaimIconUrl             string `protobuf:"bytes,6,opt,name=after_claim_icon_url,json=afterClaimIconUrl,proto3" json:"after_claim_icon_url,omitempty"`
	// desc visible on reward details page, e.g. "Cashback won by trading 500 Fi-Coins"
	DescriptionExpression string `protobuf:"bytes,7,opt,name=description_expression,json=descriptionExpression,proto3" json:"description_expression,omitempty"`
	// additional display details.
	// use case (so far): To render the details for exchanger-orders on "My Orders" screen
	AdditionalDetails *ExchangerOfferOptionConfig_Display_AdditionalDetails `protobuf:"bytes,8,opt,name=additional_details,json=additionalDetails,proto3" json:"additional_details,omitempty"`
}

func (x *ExchangerOfferOptionConfig_Display) Reset() {
	*x = ExchangerOfferOptionConfig_Display{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_exchanger_offer_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangerOfferOptionConfig_Display) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangerOfferOptionConfig_Display) ProtoMessage() {}

func (x *ExchangerOfferOptionConfig_Display) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_exchanger_offer_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangerOfferOptionConfig_Display.ProtoReflect.Descriptor instead.
func (*ExchangerOfferOptionConfig_Display) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_exchanger_offer_proto_rawDescGZIP(), []int{4, 0}
}

func (x *ExchangerOfferOptionConfig_Display) GetBeforeClaimTitleExpression() string {
	if x != nil {
		return x.BeforeClaimTitleExpression
	}
	return ""
}

func (x *ExchangerOfferOptionConfig_Display) GetBeforeClaimSubtitleExpression() string {
	if x != nil {
		return x.BeforeClaimSubtitleExpression
	}
	return ""
}

func (x *ExchangerOfferOptionConfig_Display) GetBeforeClaimIconUrl() string {
	if x != nil {
		return x.BeforeClaimIconUrl
	}
	return ""
}

func (x *ExchangerOfferOptionConfig_Display) GetAfterClaimTitleExpression() string {
	if x != nil {
		return x.AfterClaimTitleExpression
	}
	return ""
}

func (x *ExchangerOfferOptionConfig_Display) GetAfterClaimSubtitleExpression() string {
	if x != nil {
		return x.AfterClaimSubtitleExpression
	}
	return ""
}

func (x *ExchangerOfferOptionConfig_Display) GetAfterClaimIconUrl() string {
	if x != nil {
		return x.AfterClaimIconUrl
	}
	return ""
}

func (x *ExchangerOfferOptionConfig_Display) GetDescriptionExpression() string {
	if x != nil {
		return x.DescriptionExpression
	}
	return ""
}

func (x *ExchangerOfferOptionConfig_Display) GetAdditionalDetails() *ExchangerOfferOptionConfig_Display_AdditionalDetails {
	if x != nil {
		return x.AdditionalDetails
	}
	return nil
}

// For generating a reward option we need to determine the reward that should be given using a probability distribution config.
// Each RewardConfigUnit contain a reward type (along with display and unit config) with the probability percentage with which that reward should be given.
type ExchangerOfferOptionConfig_RewardConfigUnit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// denotes the type of reward like CASH, FI_COINS etc present in the config unit.
	RewardType RewardType `protobuf:"varint,1,opt,name=reward_type,json=rewardType,proto3,enum=casper.exchanger.RewardType" json:"reward_type,omitempty"`
	// denotes the display config.
	DisplayConfig *ExchangerOfferOptionConfig_Display `protobuf:"bytes,2,opt,name=display_config,json=displayConfig,proto3" json:"display_config,omitempty"`
	// denotes the percentage of times the current config unit should be used for generating the reward option.
	Percentage float32 `protobuf:"fixed32,3,opt,name=percentage,proto3" json:"percentage,omitempty"`
	// denotes the config for calculating the reward units to be given in case of cash, ficoins, sd reward.
	// can be NULL for reward types that don't require unit calculation like physical-merchandise, egv rewards.
	//
	// Types that are assignable to RewardUnitsConfig:
	//
	//	*ExchangerOfferOptionConfig_RewardConfigUnit_RangeProbabilityConfig
	RewardUnitsConfig isExchangerOfferOptionConfig_RewardConfigUnit_RewardUnitsConfig `protobuf_oneof:"reward_units_config"`
	// denotes the inventory for the reward present in the config unit. can be empty if no inventory is associated with the reward type.
	ExchangerOfferInventoryId string `protobuf:"bytes,6,opt,name=exchanger_offer_inventory_id,json=exchangerOfferInventoryId,proto3" json:"exchanger_offer_inventory_id,omitempty"`
	// stores reward type specific config metadata
	//
	// Types that are assignable to RewardTypeSpecificConfig:
	//
	//	*ExchangerOfferOptionConfig_RewardConfigUnit_PhysicalMerchandiseConfig
	//	*ExchangerOfferOptionConfig_RewardConfigUnit_EgvRewardConfig
	RewardTypeSpecificConfig isExchangerOfferOptionConfig_RewardConfigUnit_RewardTypeSpecificConfig `protobuf_oneof:"reward_type_specific_config"`
	// early user multiplier can be used for rewarding users that have made less than a certain number of CBR redemptions
	EarlyUserRewardMultiplier *ExchangerOfferOptionConfig_RewardConfigUnit_PostProcessorConfig `protobuf:"bytes,9,opt,name=early_user_reward_multiplier,json=earlyUserRewardMultiplier,proto3" json:"early_user_reward_multiplier,omitempty"`
}

func (x *ExchangerOfferOptionConfig_RewardConfigUnit) Reset() {
	*x = ExchangerOfferOptionConfig_RewardConfigUnit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_exchanger_offer_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangerOfferOptionConfig_RewardConfigUnit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangerOfferOptionConfig_RewardConfigUnit) ProtoMessage() {}

func (x *ExchangerOfferOptionConfig_RewardConfigUnit) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_exchanger_offer_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangerOfferOptionConfig_RewardConfigUnit.ProtoReflect.Descriptor instead.
func (*ExchangerOfferOptionConfig_RewardConfigUnit) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_exchanger_offer_proto_rawDescGZIP(), []int{4, 1}
}

func (x *ExchangerOfferOptionConfig_RewardConfigUnit) GetRewardType() RewardType {
	if x != nil {
		return x.RewardType
	}
	return RewardType_REWARD_TYPE_UNSPECIFIED
}

func (x *ExchangerOfferOptionConfig_RewardConfigUnit) GetDisplayConfig() *ExchangerOfferOptionConfig_Display {
	if x != nil {
		return x.DisplayConfig
	}
	return nil
}

func (x *ExchangerOfferOptionConfig_RewardConfigUnit) GetPercentage() float32 {
	if x != nil {
		return x.Percentage
	}
	return 0
}

func (m *ExchangerOfferOptionConfig_RewardConfigUnit) GetRewardUnitsConfig() isExchangerOfferOptionConfig_RewardConfigUnit_RewardUnitsConfig {
	if m != nil {
		return m.RewardUnitsConfig
	}
	return nil
}

func (x *ExchangerOfferOptionConfig_RewardConfigUnit) GetRangeProbabilityConfig() *RangeProbabilityUnitsConfig {
	if x, ok := x.GetRewardUnitsConfig().(*ExchangerOfferOptionConfig_RewardConfigUnit_RangeProbabilityConfig); ok {
		return x.RangeProbabilityConfig
	}
	return nil
}

func (x *ExchangerOfferOptionConfig_RewardConfigUnit) GetExchangerOfferInventoryId() string {
	if x != nil {
		return x.ExchangerOfferInventoryId
	}
	return ""
}

func (m *ExchangerOfferOptionConfig_RewardConfigUnit) GetRewardTypeSpecificConfig() isExchangerOfferOptionConfig_RewardConfigUnit_RewardTypeSpecificConfig {
	if m != nil {
		return m.RewardTypeSpecificConfig
	}
	return nil
}

func (x *ExchangerOfferOptionConfig_RewardConfigUnit) GetPhysicalMerchandiseConfig() *PhysicalMerchandiseRewardConfig {
	if x, ok := x.GetRewardTypeSpecificConfig().(*ExchangerOfferOptionConfig_RewardConfigUnit_PhysicalMerchandiseConfig); ok {
		return x.PhysicalMerchandiseConfig
	}
	return nil
}

func (x *ExchangerOfferOptionConfig_RewardConfigUnit) GetEgvRewardConfig() *EGVRewardConfig {
	if x, ok := x.GetRewardTypeSpecificConfig().(*ExchangerOfferOptionConfig_RewardConfigUnit_EgvRewardConfig); ok {
		return x.EgvRewardConfig
	}
	return nil
}

func (x *ExchangerOfferOptionConfig_RewardConfigUnit) GetEarlyUserRewardMultiplier() *ExchangerOfferOptionConfig_RewardConfigUnit_PostProcessorConfig {
	if x != nil {
		return x.EarlyUserRewardMultiplier
	}
	return nil
}

type isExchangerOfferOptionConfig_RewardConfigUnit_RewardUnitsConfig interface {
	isExchangerOfferOptionConfig_RewardConfigUnit_RewardUnitsConfig()
}

type ExchangerOfferOptionConfig_RewardConfigUnit_RangeProbabilityConfig struct {
	RangeProbabilityConfig *RangeProbabilityUnitsConfig `protobuf:"bytes,5,opt,name=range_probability_config,json=rangeProbabilityConfig,proto3,oneof"`
}

func (*ExchangerOfferOptionConfig_RewardConfigUnit_RangeProbabilityConfig) isExchangerOfferOptionConfig_RewardConfigUnit_RewardUnitsConfig() {
}

type isExchangerOfferOptionConfig_RewardConfigUnit_RewardTypeSpecificConfig interface {
	isExchangerOfferOptionConfig_RewardConfigUnit_RewardTypeSpecificConfig()
}

type ExchangerOfferOptionConfig_RewardConfigUnit_PhysicalMerchandiseConfig struct {
	PhysicalMerchandiseConfig *PhysicalMerchandiseRewardConfig `protobuf:"bytes,7,opt,name=physical_merchandise_config,json=physicalMerchandiseConfig,proto3,oneof"`
}

type ExchangerOfferOptionConfig_RewardConfigUnit_EgvRewardConfig struct {
	EgvRewardConfig *EGVRewardConfig `protobuf:"bytes,8,opt,name=egv_reward_config,json=egvRewardConfig,proto3,oneof"`
}

func (*ExchangerOfferOptionConfig_RewardConfigUnit_PhysicalMerchandiseConfig) isExchangerOfferOptionConfig_RewardConfigUnit_RewardTypeSpecificConfig() {
}

func (*ExchangerOfferOptionConfig_RewardConfigUnit_EgvRewardConfig) isExchangerOfferOptionConfig_RewardConfigUnit_RewardTypeSpecificConfig() {
}

type ExchangerOfferOptionConfig_Display_AdditionalDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BannerLogoUrl  string `protobuf:"bytes,1,opt,name=banner_logo_url,json=bannerLogoUrl,proto3" json:"banner_logo_url,omitempty"`
	BannerImageUrl string `protobuf:"bytes,2,opt,name=banner_image_url,json=bannerImageUrl,proto3" json:"banner_image_url,omitempty"`
	// For e.g. "Sony Playstation 5 Console"
	BannerTitle string `protobuf:"bytes,3,opt,name=banner_title,json=bannerTitle,proto3" json:"banner_title,omitempty"`
	// For e.g. "1 Playstation 5 delivered at your doorstep free of charge. Game on!"
	Desc          string   `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc,omitempty"`
	NextSteps     []string `protobuf:"bytes,5,rep,name=next_steps,json=nextSteps,proto3" json:"next_steps,omitempty"`
	Tnc           []string `protobuf:"bytes,6,rep,name=tnc,proto3" json:"tnc,omitempty"`
	BannerBgColor string   `protobuf:"bytes,7,opt,name=banner_bg_color,json=bannerBgColor,proto3" json:"banner_bg_color,omitempty"`
}

func (x *ExchangerOfferOptionConfig_Display_AdditionalDetails) Reset() {
	*x = ExchangerOfferOptionConfig_Display_AdditionalDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_exchanger_offer_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangerOfferOptionConfig_Display_AdditionalDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangerOfferOptionConfig_Display_AdditionalDetails) ProtoMessage() {}

func (x *ExchangerOfferOptionConfig_Display_AdditionalDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_exchanger_offer_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangerOfferOptionConfig_Display_AdditionalDetails.ProtoReflect.Descriptor instead.
func (*ExchangerOfferOptionConfig_Display_AdditionalDetails) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_exchanger_offer_proto_rawDescGZIP(), []int{4, 0, 0}
}

func (x *ExchangerOfferOptionConfig_Display_AdditionalDetails) GetBannerLogoUrl() string {
	if x != nil {
		return x.BannerLogoUrl
	}
	return ""
}

func (x *ExchangerOfferOptionConfig_Display_AdditionalDetails) GetBannerImageUrl() string {
	if x != nil {
		return x.BannerImageUrl
	}
	return ""
}

func (x *ExchangerOfferOptionConfig_Display_AdditionalDetails) GetBannerTitle() string {
	if x != nil {
		return x.BannerTitle
	}
	return ""
}

func (x *ExchangerOfferOptionConfig_Display_AdditionalDetails) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *ExchangerOfferOptionConfig_Display_AdditionalDetails) GetNextSteps() []string {
	if x != nil {
		return x.NextSteps
	}
	return nil
}

func (x *ExchangerOfferOptionConfig_Display_AdditionalDetails) GetTnc() []string {
	if x != nil {
		return x.Tnc
	}
	return nil
}

func (x *ExchangerOfferOptionConfig_Display_AdditionalDetails) GetBannerBgColor() string {
	if x != nil {
		return x.BannerBgColor
	}
	return ""
}

// PostProcessorConfig will contain any information needed to apply a post processor to the generated reward option, e.g. a multiplier
type ExchangerOfferOptionConfig_RewardConfigUnit_PostProcessorConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// This contains the name of function that will be called for evaluating whether we need to apply this post-processor.
	// In future this field can be used for expressions.
	Condition string `protobuf:"bytes,1,opt,name=condition,proto3" json:"condition,omitempty"`
	// Types that are assignable to PostProcessorSpecificParams:
	//
	//	*ExchangerOfferOptionConfig_RewardConfigUnit_PostProcessorConfig_Multiplier
	PostProcessorSpecificParams isExchangerOfferOptionConfig_RewardConfigUnit_PostProcessorConfig_PostProcessorSpecificParams `protobuf_oneof:"PostProcessorSpecificParams"`
}

func (x *ExchangerOfferOptionConfig_RewardConfigUnit_PostProcessorConfig) Reset() {
	*x = ExchangerOfferOptionConfig_RewardConfigUnit_PostProcessorConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_exchanger_offer_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangerOfferOptionConfig_RewardConfigUnit_PostProcessorConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangerOfferOptionConfig_RewardConfigUnit_PostProcessorConfig) ProtoMessage() {}

func (x *ExchangerOfferOptionConfig_RewardConfigUnit_PostProcessorConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_exchanger_offer_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangerOfferOptionConfig_RewardConfigUnit_PostProcessorConfig.ProtoReflect.Descriptor instead.
func (*ExchangerOfferOptionConfig_RewardConfigUnit_PostProcessorConfig) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_exchanger_offer_proto_rawDescGZIP(), []int{4, 1, 0}
}

func (x *ExchangerOfferOptionConfig_RewardConfigUnit_PostProcessorConfig) GetCondition() string {
	if x != nil {
		return x.Condition
	}
	return ""
}

func (m *ExchangerOfferOptionConfig_RewardConfigUnit_PostProcessorConfig) GetPostProcessorSpecificParams() isExchangerOfferOptionConfig_RewardConfigUnit_PostProcessorConfig_PostProcessorSpecificParams {
	if m != nil {
		return m.PostProcessorSpecificParams
	}
	return nil
}

func (x *ExchangerOfferOptionConfig_RewardConfigUnit_PostProcessorConfig) GetMultiplier() uint32 {
	if x, ok := x.GetPostProcessorSpecificParams().(*ExchangerOfferOptionConfig_RewardConfigUnit_PostProcessorConfig_Multiplier); ok {
		return x.Multiplier
	}
	return 0
}

type isExchangerOfferOptionConfig_RewardConfigUnit_PostProcessorConfig_PostProcessorSpecificParams interface {
	isExchangerOfferOptionConfig_RewardConfigUnit_PostProcessorConfig_PostProcessorSpecificParams()
}

type ExchangerOfferOptionConfig_RewardConfigUnit_PostProcessorConfig_Multiplier struct {
	// value by which the generated option will be multiplied
	Multiplier uint32 `protobuf:"varint,2,opt,name=multiplier,proto3,oneof"`
}

func (*ExchangerOfferOptionConfig_RewardConfigUnit_PostProcessorConfig_Multiplier) isExchangerOfferOptionConfig_RewardConfigUnit_PostProcessorConfig_PostProcessorSpecificParams() {
}

// ConfigUnit with start: 0, end: 10, percentage: 50 denotes that 50% of the times, given reward units should be between 0-10 inclusive
type RangeProbabilityUnitsConfig_ConfigUnit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Start      uint32  `protobuf:"varint,1,opt,name=start,proto3" json:"start,omitempty"`
	End        uint32  `protobuf:"varint,2,opt,name=end,proto3" json:"end,omitempty"`
	Percentage float32 `protobuf:"fixed32,3,opt,name=percentage,proto3" json:"percentage,omitempty"`
}

func (x *RangeProbabilityUnitsConfig_ConfigUnit) Reset() {
	*x = RangeProbabilityUnitsConfig_ConfigUnit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_exchanger_offer_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RangeProbabilityUnitsConfig_ConfigUnit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RangeProbabilityUnitsConfig_ConfigUnit) ProtoMessage() {}

func (x *RangeProbabilityUnitsConfig_ConfigUnit) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_exchanger_offer_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RangeProbabilityUnitsConfig_ConfigUnit.ProtoReflect.Descriptor instead.
func (*RangeProbabilityUnitsConfig_ConfigUnit) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_exchanger_offer_proto_rawDescGZIP(), []int{5, 0}
}

func (x *RangeProbabilityUnitsConfig_ConfigUnit) GetStart() uint32 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *RangeProbabilityUnitsConfig_ConfigUnit) GetEnd() uint32 {
	if x != nil {
		return x.End
	}
	return 0
}

func (x *RangeProbabilityUnitsConfig_ConfigUnit) GetPercentage() float32 {
	if x != nil {
		return x.Percentage
	}
	return 0
}

// OfferDescription will contain heading and a list of points where each element is to be displayed as line seperated numbered points
// heading will not be numbered, numbering will start after the heading
// heading or points can be nil if not required
// eg. for offerDescription when numbered points are required with heading -
//
//	This gift box contains -
//	  1. A 500 gm bag of Vienna Roast coffee
//	  2. Easy coffee Bags (Box of 5 bags)
//
// eg. for offerDescription only a heading is required without points -
//
//	Scan the code below at the counter to get 800 off on your total bill when you shop at M&S India
type ExchangerOfferAdditionalDetails_OfferDescription struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Heading string   `protobuf:"bytes,1,opt,name=heading,proto3" json:"heading,omitempty"`
	Points  []string `protobuf:"bytes,2,rep,name=points,proto3" json:"points,omitempty"`
}

func (x *ExchangerOfferAdditionalDetails_OfferDescription) Reset() {
	*x = ExchangerOfferAdditionalDetails_OfferDescription{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_exchanger_offer_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangerOfferAdditionalDetails_OfferDescription) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangerOfferAdditionalDetails_OfferDescription) ProtoMessage() {}

func (x *ExchangerOfferAdditionalDetails_OfferDescription) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_exchanger_offer_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangerOfferAdditionalDetails_OfferDescription.ProtoReflect.Descriptor instead.
func (*ExchangerOfferAdditionalDetails_OfferDescription) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_exchanger_offer_proto_rawDescGZIP(), []int{7, 0}
}

func (x *ExchangerOfferAdditionalDetails_OfferDescription) GetHeading() string {
	if x != nil {
		return x.Heading
	}
	return ""
}

func (x *ExchangerOfferAdditionalDetails_OfferDescription) GetPoints() []string {
	if x != nil {
		return x.Points
	}
	return nil
}

var File_api_casper_exchanger_exchanger_offer_proto protoreflect.FileDescriptor

var file_api_casper_exchanger_exchanger_offer_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2f, 0x65, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72,
	0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x63, 0x61,
	0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x1a, 0x30,
	0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2f, 0x65, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x72, 0x2f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2f, 0x65, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2f,
	0x74, 0x61, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x6f,
	0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xe2, 0x06, 0x0a, 0x0e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x63, 0x0a, 0x13, 0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x32, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x52, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x63, 0x79, 0x52, 0x12, 0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x29, 0x0a, 0x10, 0x72, 0x65, 0x64, 0x65,
	0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x0f, 0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x12, 0x62, 0x0a, 0x15, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x64, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x13, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x5f, 0x0a, 0x14, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x12, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x68, 0x0a, 0x17, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x63, 0x61, 0x73, 0x70,
	0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x41, 0x67, 0x67, 0x72, 0x65,
	0x67, 0x61, 0x74, 0x65, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x15, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69,
	0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39,
	0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x09, 0x74, 0x61, 0x67, 0x73, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72,
	0x2e, 0x54, 0x61, 0x67, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x74, 0x61, 0x67, 0x73, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x60, 0x0a, 0x12, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x31, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x11, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3e, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x95, 0x09, 0x0a, 0x1c, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x1b, 0x0a, 0x09,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x22, 0x0a, 0x0d, 0x74, 0x69, 0x6c,
	0x65, 0x5f, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x74, 0x69, 0x6c, 0x65, 0x42, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x21, 0x0a,
	0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x52, 0x61, 0x6e, 0x6b,
	0x12, 0x2a, 0x0a, 0x11, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x69, 0x6e, 0x66,
	0x6f, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x2f, 0x0a, 0x14,
	0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x62, 0x67, 0x5f, 0x63,
	0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x69, 0x6e, 0x66, 0x6f,
	0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x42, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x2f, 0x0a,
	0x14, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x63, 0x6f,
	0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x69, 0x6e, 0x66,
	0x6f, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x22,
	0x0a, 0x0d, 0x68, 0x6f, 0x77, 0x5f, 0x74, 0x6f, 0x5f, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x18,
	0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x68, 0x6f, 0x77, 0x54, 0x6f, 0x52, 0x65, 0x64, 0x65,
	0x65, 0x6d, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x6e, 0x63, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x03, 0x74, 0x6e, 0x63, 0x12, 0x73, 0x0a, 0x19, 0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x61, 0x6e, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x37, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72,
	0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x41, 0x6e, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x17, 0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x6e, 0x69, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4a, 0x0a, 0x12, 0x73, 0x61, 0x6c,
	0x61, 0x72, 0x79, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x61, 0x67, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x42,
	0x02, 0x18, 0x01, 0x52, 0x10, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x54, 0x61, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x68, 0x6f, 0x6d, 0x65, 0x5f, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x68, 0x6f, 0x6d, 0x65, 0x54,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x5b, 0x0a, 0x17, 0x74, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x14, 0x74, 0x69, 0x6c, 0x65,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x42, 0x0a, 0x1d, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x73,
	0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x7f, 0x0a, 0x17, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x72, 0x61, 0x6e, 0x6b, 0x5f, 0x6d, 0x61, 0x70, 0x18,
	0x12, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x48, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x44, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x52, 0x61, 0x6e, 0x6b, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x14, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x52, 0x61,
	0x6e, 0x6b, 0x4d, 0x61, 0x70, 0x12, 0x24, 0x0a, 0x0e, 0x68, 0x69, 0x64, 0x65, 0x5f, 0x74, 0x6f,
	0x70, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x68,
	0x69, 0x64, 0x65, 0x54, 0x6f, 0x70, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x27, 0x0a, 0x0f, 0x69,
	0x73, 0x5f, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x18, 0x14,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x69, 0x73, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65,
	0x6e, 0x74, 0x61, 0x6c, 0x12, 0x2d, 0x0a, 0x12, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x54,
	0x69, 0x74, 0x6c, 0x65, 0x1a, 0x47, 0x0a, 0x19, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x44, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x52, 0x61, 0x6e, 0x6b, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xa3, 0x02,
	0x0a, 0x1e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x44, 0x0a, 0x1f, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x75,
	0x73, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x1b, 0x64, 0x61, 0x69, 0x6c, 0x79,
	0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x50,
	0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x12, 0x71, 0x0a, 0x20, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x5f, 0x63, 0x61, 0x70, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x29, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x43,
	0x61, 0x70, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x52, 0x1c, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x43, 0x61, 0x70, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x12, 0x48, 0x0a, 0x21, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x5f,
	0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x61, 0x70, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x1d, 0x75, 0x73, 0x65, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x4d,
	0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x52, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x61, 0x70, 0x22, 0xb0, 0x01, 0x0a, 0x1b, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x53, 0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x63, 0x61,
	0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3c, 0x0a, 0x1b, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x5f, 0x64, 0x65, 0x63, 0x69, 0x64, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f,
	0x69, 0x6e, 0x5f, 0x73, 0x65, 0x63, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x17, 0x64,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x44, 0x65, 0x63, 0x69, 0x64, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x49, 0x6e, 0x53, 0x65, 0x63, 0x73, 0x22, 0xb9, 0x0e, 0x0a, 0x1a, 0x45, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x6d, 0x0a, 0x13, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x55, 0x6e, 0x69,
	0x74, 0x52, 0x11, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x55,
	0x6e, 0x69, 0x74, 0x73, 0x1a, 0xa7, 0x06, 0x0a, 0x07, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x12, 0x41, 0x0a, 0x1d, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x63, 0x6c, 0x61, 0x69, 0x6d,
	0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1a, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x43,
	0x6c, 0x61, 0x69, 0x6d, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x47, 0x0a, 0x20, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x63, 0x6c,
	0x61, 0x69, 0x6d, 0x5f, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x65, 0x78, 0x70,
	0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1d, 0x62,
	0x65, 0x66, 0x6f, 0x72, 0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x53, 0x75, 0x62, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x31, 0x0a, 0x15,
	0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x5f, 0x69, 0x63, 0x6f,
	0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x62, 0x65, 0x66,
	0x6f, 0x72, 0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x49, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12,
	0x3f, 0x0a, 0x1c, 0x61, 0x66, 0x74, 0x65, 0x72, 0x5f, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x5f, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x19, 0x61, 0x66, 0x74, 0x65, 0x72, 0x43, 0x6c, 0x61, 0x69,
	0x6d, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x45, 0x0a, 0x1f, 0x61, 0x66, 0x74, 0x65, 0x72, 0x5f, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x5f,
	0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1c, 0x61, 0x66, 0x74, 0x65, 0x72,
	0x43, 0x6c, 0x61, 0x69, 0x6d, 0x53, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x45, 0x78, 0x70,
	0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2f, 0x0a, 0x14, 0x61, 0x66, 0x74, 0x65, 0x72,
	0x5f, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x61, 0x66, 0x74, 0x65, 0x72, 0x43, 0x6c, 0x61, 0x69,
	0x6d, 0x49, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x35, 0x0a, 0x16, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x75, 0x0a, 0x12, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x63, 0x61,
	0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x2e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x11, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x1a, 0xf5, 0x01, 0x0a, 0x11, 0x41, 0x64, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x26, 0x0a, 0x0f,
	0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x6c, 0x6f, 0x67, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x4c, 0x6f, 0x67,
	0x6f, 0x55, 0x72, 0x6c, 0x12, 0x28, 0x0a, 0x10, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x21,
	0x0a, 0x0c, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x54, 0x69, 0x74, 0x6c,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x73, 0x74,
	0x65, 0x70, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x65, 0x78, 0x74, 0x53,
	0x74, 0x65, 0x70, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x6e, 0x63, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x03, 0x74, 0x6e, 0x63, 0x12, 0x26, 0x0a, 0x0f, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x5f, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x42, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x1a, 0x81,
	0x07, 0x0a, 0x10, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x55,
	0x6e, 0x69, 0x74, 0x12, 0x3d, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65,
	0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x5b, 0x0a, 0x0e, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x63, 0x61, 0x73,
	0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x52, 0x0d, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x1e, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x12,
	0x69, 0x0a, 0x18, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x72, 0x6f, 0x62, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x48, 0x00, 0x52, 0x16, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3f, 0x0a, 0x1c, 0x65, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x6e,
	0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x19, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x73, 0x0a, 0x1b, 0x70,
	0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x5f, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x64,
	0x69, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x31, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x72, 0x2e, 0x50, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x4d, 0x65, 0x72, 0x63,
	0x68, 0x61, 0x6e, 0x64, 0x69, 0x73, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x48, 0x01, 0x52, 0x19, 0x70, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x4d,
	0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x64, 0x69, 0x73, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x4f, 0x0a, 0x11, 0x65, 0x67, 0x76, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x61,
	0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45,
	0x47, 0x56, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x01,
	0x52, 0x0f, 0x65, 0x67, 0x76, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x92, 0x01, 0x0a, 0x1c, 0x65, 0x61, 0x72, 0x6c, 0x79, 0x5f, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x69,
	0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x51, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65,
	0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x19, 0x65, 0x61, 0x72,
	0x6c, 0x79, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4d, 0x75, 0x6c, 0x74,
	0x69, 0x70, 0x6c, 0x69, 0x65, 0x72, 0x1a, 0x74, 0x0a, 0x13, 0x50, 0x6f, 0x73, 0x74, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1c, 0x0a,
	0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0a, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x69, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x48,
	0x00, 0x52, 0x0a, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x69, 0x65, 0x72, 0x42, 0x1d, 0x0a,
	0x1b, 0x50, 0x6f, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x53, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x42, 0x15, 0x0a, 0x13,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x42, 0x1d, 0x0a, 0x1b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x22, 0xf2, 0x01, 0x0a, 0x1b, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x72, 0x6f, 0x62,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x5b, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x75, 0x6e, 0x69,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65,
	0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x50, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x55, 0x6e, 0x69, 0x74,
	0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x55, 0x6e,
	0x69, 0x74, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x12,
	0x20, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x1a, 0x54, 0x0a, 0x0a, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x03, 0x65, 0x6e, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65,
	0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x70, 0x65, 0x72,
	0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x22, 0x59, 0x0a, 0x1f, 0x50, 0x68, 0x79, 0x73, 0x69,
	0x63, 0x61, 0x6c, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x64, 0x69, 0x73, 0x65, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x15, 0x0a, 0x06, 0x73, 0x6b,
	0x75, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x6b, 0x75, 0x49,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x22, 0xa6, 0x05, 0x0a, 0x1f, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x41, 0x0a, 0x1b, 0x69, 0x73, 0x5f, 0x73, 0x61, 0x6c,
	0x61, 0x72, 0x79, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x65, 0x78, 0x63, 0x6c,
	0x75, 0x73, 0x69, 0x76, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x42, 0x02, 0x18, 0x01, 0x52,
	0x18, 0x69, 0x73, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x45, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x12, 0x6f, 0x0a, 0x11, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x10, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x12, 0x61, 0x6c,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x74, 0x61, 0x5f, 0x74, 0x65, 0x78, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x61, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74,
	0x65, 0x43, 0x74, 0x61, 0x54, 0x65, 0x78, 0x74, 0x12, 0x59, 0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x46, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x54, 0x6f, 0x43,
	0x61, 0x73, 0x68, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x74,
	0x69, 0x6f, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x61,
	0x74, 0x69, 0x6f, 0x12, 0x24, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x5f,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x50,
	0x72, 0x6f, 0x6d, 0x6f, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x12, 0x37, 0x0a, 0x18, 0x70, 0x72, 0x6f,
	0x6d, 0x6f, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x5f, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x15, 0x70, 0x72, 0x6f,
	0x6d, 0x6f, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x52, 0x61,
	0x6e, 0x6b, 0x12, 0x3d, 0x0a, 0x0b, 0x77, 0x61, 0x74, 0x73, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x74,
	0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72,
	0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x57, 0x61, 0x74, 0x73, 0x6f,
	0x6e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0a, 0x77, 0x61, 0x74, 0x73, 0x6f, 0x6e, 0x4d, 0x65, 0x74,
	0x61, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x54, 0x69, 0x74,
	0x6c, 0x65, 0x12, 0x41, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x66, 0x69, 0x5f, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f,
	0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0a, 0x69, 0x73, 0x46, 0x69, 0x50,
	0x6f, 0x69, 0x6e, 0x74, 0x73, 0x1a, 0x44, 0x0a, 0x10, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x68, 0x65, 0x61,
	0x64, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x68, 0x65, 0x61, 0x64,
	0x69, 0x6e, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x22, 0x6d, 0x0a, 0x1c, 0x46,
	0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x54, 0x6f, 0x43, 0x61, 0x73, 0x68, 0x43, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x24, 0x0a, 0x0e, 0x66,
	0x69, 0x5f, 0x63, 0x6f, 0x69, 0x6e, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0c, 0x66, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x61, 0x73, 0x68, 0x5f, 0x65, 0x71, 0x75, 0x69, 0x76, 0x61,
	0x6c, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x63, 0x61, 0x73, 0x68,
	0x45, 0x71, 0x75, 0x69, 0x76, 0x61, 0x6c, 0x65, 0x6e, 0x74, 0x22, 0x75, 0x0a, 0x0a, 0x57, 0x61,
	0x74, 0x73, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x3b, 0x0a, 0x1a, 0x73, 0x68, 0x6f, 0x75,
	0x6c, 0x64, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x77, 0x61, 0x74, 0x73, 0x6f, 0x6e,
	0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x73, 0x68,
	0x6f, 0x75, 0x6c, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x61, 0x74, 0x73, 0x6f, 0x6e,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x69, 0x73, 0x73, 0x75, 0x65, 0x5f, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x69, 0x73, 0x73, 0x75, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49,
	0x64, 0x2a, 0x89, 0x01, 0x0a, 0x20, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x33, 0x0a, 0x2f, 0x45, 0x58, 0x43, 0x48, 0x41, 0x4e,
	0x47, 0x45, 0x52, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x44, 0x45, 0x4d, 0x50,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x55, 0x52, 0x52, 0x45, 0x4e, 0x43, 0x59, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x30, 0x0a, 0x2c, 0x45,
	0x58, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x52, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x52,
	0x45, 0x44, 0x45, 0x4d, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x55, 0x52, 0x52, 0x45, 0x4e,
	0x43, 0x59, 0x5f, 0x46, 0x49, 0x5f, 0x43, 0x4f, 0x49, 0x4e, 0x53, 0x10, 0x01, 0x2a, 0xbb, 0x01,
	0x0a, 0x25, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x52, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x6e, 0x69, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x35, 0x45, 0x58, 0x43, 0x48, 0x41,
	0x4e, 0x47, 0x45, 0x52, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x44, 0x45, 0x4d,
	0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x4e, 0x49, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x44, 0x49, 0x47, 0x47, 0x49, 0x4e, 0x47, 0x5f, 0x44, 0x4f,
	0x47, 0x5f, 0x41, 0x4e, 0x49, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x21, 0x0a,
	0x1d, 0x55, 0x4e, 0x44, 0x45, 0x52, 0x57, 0x41, 0x54, 0x45, 0x52, 0x5f, 0x54, 0x52, 0x45, 0x41,
	0x53, 0x55, 0x52, 0x45, 0x5f, 0x41, 0x4e, 0x49, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x02,
	0x12, 0x19, 0x0a, 0x15, 0x43, 0x52, 0x49, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x43, 0x55, 0x50, 0x5f,
	0x41, 0x4e, 0x49, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x03, 0x2a, 0xf6, 0x01, 0x0a, 0x14,
	0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x26, 0x0a, 0x22, 0x45, 0x58, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45,
	0x52, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x22, 0x0a, 0x1e,
	0x45, 0x58, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x52, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01,
	0x12, 0x23, 0x0a, 0x1f, 0x45, 0x58, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x52, 0x5f, 0x4f, 0x46,
	0x46, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f,
	0x56, 0x45, 0x44, 0x10, 0x02, 0x12, 0x23, 0x0a, 0x1f, 0x45, 0x58, 0x43, 0x48, 0x41, 0x4e, 0x47,
	0x45, 0x52, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x49, 0x4e, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x03, 0x12, 0x21, 0x0a, 0x1d, 0x45, 0x58,
	0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x52, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x04, 0x12, 0x25, 0x0a,
	0x21, 0x45, 0x58, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x52, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x54,
	0x45, 0x44, 0x10, 0x05, 0x42, 0x5a, 0x0a, 0x2b, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x72, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_casper_exchanger_exchanger_offer_proto_rawDescOnce sync.Once
	file_api_casper_exchanger_exchanger_offer_proto_rawDescData = file_api_casper_exchanger_exchanger_offer_proto_rawDesc
)

func file_api_casper_exchanger_exchanger_offer_proto_rawDescGZIP() []byte {
	file_api_casper_exchanger_exchanger_offer_proto_rawDescOnce.Do(func() {
		file_api_casper_exchanger_exchanger_offer_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_casper_exchanger_exchanger_offer_proto_rawDescData)
	})
	return file_api_casper_exchanger_exchanger_offer_proto_rawDescData
}

var file_api_casper_exchanger_exchanger_offer_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_api_casper_exchanger_exchanger_offer_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_api_casper_exchanger_exchanger_offer_proto_goTypes = []interface{}{
	(ExchangerOfferRedemptionCurrency)(0),                                   // 0: casper.exchanger.ExchangerOfferRedemptionCurrency
	(ExchangerOfferRedemptionAnimationType)(0),                              // 1: casper.exchanger.ExchangerOfferRedemptionAnimationType
	(ExchangerOfferStatus)(0),                                               // 2: casper.exchanger.ExchangerOfferStatus
	(*ExchangerOffer)(nil),                                                  // 3: casper.exchanger.ExchangerOffer
	(*ExchangerOfferDisplayDetails)(nil),                                    // 4: casper.exchanger.ExchangerOfferDisplayDetails
	(*ExchangerOfferAggregatesConfig)(nil),                                  // 5: casper.exchanger.ExchangerOfferAggregatesConfig
	(*ExchangerOfferOptionsConfig)(nil),                                     // 6: casper.exchanger.ExchangerOfferOptionsConfig
	(*ExchangerOfferOptionConfig)(nil),                                      // 7: casper.exchanger.ExchangerOfferOptionConfig
	(*RangeProbabilityUnitsConfig)(nil),                                     // 8: casper.exchanger.RangeProbabilityUnitsConfig
	(*PhysicalMerchandiseRewardConfig)(nil),                                 // 9: casper.exchanger.PhysicalMerchandiseRewardConfig
	(*ExchangerOfferAdditionalDetails)(nil),                                 // 10: casper.exchanger.ExchangerOfferAdditionalDetails
	(*FiCoinsToCashConversionRatio)(nil),                                    // 11: casper.exchanger.FiCoinsToCashConversionRatio
	(*WatsonMeta)(nil),                                                      // 12: casper.exchanger.WatsonMeta
	nil,                                                                     // 13: casper.exchanger.ExchangerOfferDisplayDetails.ScreenDisplayRankMapEntry
	(*ExchangerOfferOptionConfig_Display)(nil),                              // 14: casper.exchanger.ExchangerOfferOptionConfig.Display
	(*ExchangerOfferOptionConfig_RewardConfigUnit)(nil),                     // 15: casper.exchanger.ExchangerOfferOptionConfig.RewardConfigUnit
	(*ExchangerOfferOptionConfig_Display_AdditionalDetails)(nil),            // 16: casper.exchanger.ExchangerOfferOptionConfig.Display.AdditionalDetails
	(*ExchangerOfferOptionConfig_RewardConfigUnit_PostProcessorConfig)(nil), // 17: casper.exchanger.ExchangerOfferOptionConfig.RewardConfigUnit.PostProcessorConfig
	(*RangeProbabilityUnitsConfig_ConfigUnit)(nil),                          // 18: casper.exchanger.RangeProbabilityUnitsConfig.ConfigUnit
	(*ExchangerOfferAdditionalDetails_OfferDescription)(nil),                // 19: casper.exchanger.ExchangerOfferAdditionalDetails.OfferDescription
	(*timestamppb.Timestamp)(nil),                                           // 20: google.protobuf.Timestamp
	(*casper.TagsInfo)(nil),                                                 // 21: casper.TagsInfo
	(*common.Text)(nil),                                                     // 22: api.typesv2.common.Text
	(common.ImageContentType)(0),                                            // 23: api.typesv2.common.ImageContentType
	(*RewardUnitsCapAggregate)(nil),                                         // 24: casper.exchanger.RewardUnitsCapAggregate
	(common.BooleanEnum)(0),                                                 // 25: api.typesv2.common.BooleanEnum
	(RewardType)(0),                                                         // 26: casper.exchanger.RewardType
	(*EGVRewardConfig)(nil),                                                 // 27: casper.exchanger.EGVRewardConfig
}
var file_api_casper_exchanger_exchanger_offer_proto_depIdxs = []int32{
	0,  // 0: casper.exchanger.ExchangerOffer.redemption_currency:type_name -> casper.exchanger.ExchangerOfferRedemptionCurrency
	4,  // 1: casper.exchanger.ExchangerOffer.offer_display_details:type_name -> casper.exchanger.ExchangerOfferDisplayDetails
	6,  // 2: casper.exchanger.ExchangerOffer.offer_options_config:type_name -> casper.exchanger.ExchangerOfferOptionsConfig
	5,  // 3: casper.exchanger.ExchangerOffer.offer_aggregates_config:type_name -> casper.exchanger.ExchangerOfferAggregatesConfig
	20, // 4: casper.exchanger.ExchangerOffer.created_at:type_name -> google.protobuf.Timestamp
	20, // 5: casper.exchanger.ExchangerOffer.updated_at:type_name -> google.protobuf.Timestamp
	21, // 6: casper.exchanger.ExchangerOffer.tags_info:type_name -> casper.TagsInfo
	10, // 7: casper.exchanger.ExchangerOffer.additional_details:type_name -> casper.exchanger.ExchangerOfferAdditionalDetails
	2,  // 8: casper.exchanger.ExchangerOffer.status:type_name -> casper.exchanger.ExchangerOfferStatus
	1,  // 9: casper.exchanger.ExchangerOfferDisplayDetails.redemption_animation_type:type_name -> casper.exchanger.ExchangerOfferRedemptionAnimationType
	22, // 10: casper.exchanger.ExchangerOfferDisplayDetails.salary_account_tag:type_name -> api.typesv2.common.Text
	23, // 11: casper.exchanger.ExchangerOfferDisplayDetails.tile_image_content_type:type_name -> api.typesv2.common.ImageContentType
	13, // 12: casper.exchanger.ExchangerOfferDisplayDetails.screen_display_rank_map:type_name -> casper.exchanger.ExchangerOfferDisplayDetails.ScreenDisplayRankMapEntry
	24, // 13: casper.exchanger.ExchangerOfferAggregatesConfig.reward_units_cap_offer_aggregate:type_name -> casper.exchanger.RewardUnitsCapAggregate
	7,  // 14: casper.exchanger.ExchangerOfferOptionsConfig.options_config:type_name -> casper.exchanger.ExchangerOfferOptionConfig
	15, // 15: casper.exchanger.ExchangerOfferOptionConfig.reward_config_units:type_name -> casper.exchanger.ExchangerOfferOptionConfig.RewardConfigUnit
	18, // 16: casper.exchanger.RangeProbabilityUnitsConfig.config_units:type_name -> casper.exchanger.RangeProbabilityUnitsConfig.ConfigUnit
	19, // 17: casper.exchanger.ExchangerOfferAdditionalDetails.offer_description:type_name -> casper.exchanger.ExchangerOfferAdditionalDetails.OfferDescription
	11, // 18: casper.exchanger.ExchangerOfferAdditionalDetails.conversion_ratio:type_name -> casper.exchanger.FiCoinsToCashConversionRatio
	12, // 19: casper.exchanger.ExchangerOfferAdditionalDetails.watson_meta:type_name -> casper.exchanger.WatsonMeta
	25, // 20: casper.exchanger.ExchangerOfferAdditionalDetails.is_fi_points:type_name -> api.typesv2.common.BooleanEnum
	16, // 21: casper.exchanger.ExchangerOfferOptionConfig.Display.additional_details:type_name -> casper.exchanger.ExchangerOfferOptionConfig.Display.AdditionalDetails
	26, // 22: casper.exchanger.ExchangerOfferOptionConfig.RewardConfigUnit.reward_type:type_name -> casper.exchanger.RewardType
	14, // 23: casper.exchanger.ExchangerOfferOptionConfig.RewardConfigUnit.display_config:type_name -> casper.exchanger.ExchangerOfferOptionConfig.Display
	8,  // 24: casper.exchanger.ExchangerOfferOptionConfig.RewardConfigUnit.range_probability_config:type_name -> casper.exchanger.RangeProbabilityUnitsConfig
	9,  // 25: casper.exchanger.ExchangerOfferOptionConfig.RewardConfigUnit.physical_merchandise_config:type_name -> casper.exchanger.PhysicalMerchandiseRewardConfig
	27, // 26: casper.exchanger.ExchangerOfferOptionConfig.RewardConfigUnit.egv_reward_config:type_name -> casper.exchanger.EGVRewardConfig
	17, // 27: casper.exchanger.ExchangerOfferOptionConfig.RewardConfigUnit.early_user_reward_multiplier:type_name -> casper.exchanger.ExchangerOfferOptionConfig.RewardConfigUnit.PostProcessorConfig
	28, // [28:28] is the sub-list for method output_type
	28, // [28:28] is the sub-list for method input_type
	28, // [28:28] is the sub-list for extension type_name
	28, // [28:28] is the sub-list for extension extendee
	0,  // [0:28] is the sub-list for field type_name
}

func init() { file_api_casper_exchanger_exchanger_offer_proto_init() }
func file_api_casper_exchanger_exchanger_offer_proto_init() {
	if File_api_casper_exchanger_exchanger_offer_proto != nil {
		return
	}
	file_api_casper_exchanger_exchanger_offer_group_proto_init()
	file_api_casper_exchanger_reward_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_casper_exchanger_exchanger_offer_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExchangerOffer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_exchanger_offer_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExchangerOfferDisplayDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_exchanger_offer_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExchangerOfferAggregatesConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_exchanger_offer_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExchangerOfferOptionsConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_exchanger_offer_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExchangerOfferOptionConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_exchanger_offer_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RangeProbabilityUnitsConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_exchanger_offer_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PhysicalMerchandiseRewardConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_exchanger_offer_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExchangerOfferAdditionalDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_exchanger_offer_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FiCoinsToCashConversionRatio); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_exchanger_offer_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WatsonMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_exchanger_offer_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExchangerOfferOptionConfig_Display); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_exchanger_offer_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExchangerOfferOptionConfig_RewardConfigUnit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_exchanger_offer_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExchangerOfferOptionConfig_Display_AdditionalDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_exchanger_offer_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExchangerOfferOptionConfig_RewardConfigUnit_PostProcessorConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_exchanger_offer_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RangeProbabilityUnitsConfig_ConfigUnit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_exchanger_offer_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExchangerOfferAdditionalDetails_OfferDescription); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_casper_exchanger_exchanger_offer_proto_msgTypes[12].OneofWrappers = []interface{}{
		(*ExchangerOfferOptionConfig_RewardConfigUnit_RangeProbabilityConfig)(nil),
		(*ExchangerOfferOptionConfig_RewardConfigUnit_PhysicalMerchandiseConfig)(nil),
		(*ExchangerOfferOptionConfig_RewardConfigUnit_EgvRewardConfig)(nil),
	}
	file_api_casper_exchanger_exchanger_offer_proto_msgTypes[14].OneofWrappers = []interface{}{
		(*ExchangerOfferOptionConfig_RewardConfigUnit_PostProcessorConfig_Multiplier)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_casper_exchanger_exchanger_offer_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_casper_exchanger_exchanger_offer_proto_goTypes,
		DependencyIndexes: file_api_casper_exchanger_exchanger_offer_proto_depIdxs,
		EnumInfos:         file_api_casper_exchanger_exchanger_offer_proto_enumTypes,
		MessageInfos:      file_api_casper_exchanger_exchanger_offer_proto_msgTypes,
	}.Build()
	File_api_casper_exchanger_exchanger_offer_proto = out.File
	file_api_casper_exchanger_exchanger_offer_proto_rawDesc = nil
	file_api_casper_exchanger_exchanger_offer_proto_goTypes = nil
	file_api_casper_exchanger_exchanger_offer_proto_depIdxs = nil
}
