// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/casper/offer_catalog.proto

package casper

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.ImageContentType(0)
)

// Validate checks the field values on OfferImage with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *OfferImage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OfferImage with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in OfferImageMultiError, or
// nil if none found.
func (m *OfferImage) ValidateAll() error {
	return m.validate(true)
}

func (m *OfferImage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ImageType

	// no validation rules for Url

	if len(errors) > 0 {
		return OfferImageMultiError(errors)
	}

	return nil
}

// OfferImageMultiError is an error wrapping multiple validation errors
// returned by OfferImage.ValidateAll() if the designated constraints aren't met.
type OfferImageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OfferImageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OfferImageMultiError) AllErrors() []error { return m }

// OfferImageValidationError is the validation error returned by
// OfferImage.Validate if the designated constraints aren't met.
type OfferImageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OfferImageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OfferImageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OfferImageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OfferImageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OfferImageValidationError) ErrorName() string { return "OfferImageValidationError" }

// Error satisfies the builtin error interface
func (e OfferImageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOfferImage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OfferImageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OfferImageValidationError{}

// Validate checks the field values on OfferTnc with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *OfferTnc) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OfferTnc with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in OfferTncMultiError, or nil
// if none found.
func (m *OfferTnc) ValidateAll() error {
	return m.validate(true)
}

func (m *OfferTnc) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return OfferTncMultiError(errors)
	}

	return nil
}

// OfferTncMultiError is an error wrapping multiple validation errors returned
// by OfferTnc.ValidateAll() if the designated constraints aren't met.
type OfferTncMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OfferTncMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OfferTncMultiError) AllErrors() []error { return m }

// OfferTncValidationError is the validation error returned by
// OfferTnc.Validate if the designated constraints aren't met.
type OfferTncValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OfferTncValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OfferTncValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OfferTncValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OfferTncValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OfferTncValidationError) ErrorName() string { return "OfferTncValidationError" }

// Error satisfies the builtin error interface
func (e OfferTncValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOfferTnc.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OfferTncValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OfferTncValidationError{}

// Validate checks the field values on OfferAdditionalDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OfferAdditionalDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OfferAdditionalDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OfferAdditionalDetailsMultiError, or nil if none found.
func (m *OfferAdditionalDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *OfferAdditionalDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BgColor

	// no validation rules for AfterRedemptionOfferName

	// no validation rules for OfferTitle

	// no validation rules for OfferDisplayRank

	if all {
		switch v := interface{}(m.GetSalaryAccountDisplayTag()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferAdditionalDetailsValidationError{
					field:  "SalaryAccountDisplayTag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferAdditionalDetailsValidationError{
					field:  "SalaryAccountDisplayTag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSalaryAccountDisplayTag()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferAdditionalDetailsValidationError{
				field:  "SalaryAccountDisplayTag",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for HomeTitle

	// no validation rules for IsSalaryAccountExclusive

	// no validation rules for BrandName

	// no validation rules for TileImageContentType

	// no validation rules for DisplayConstraintExpression

	// no validation rules for ScreenDisplayRankMap

	// no validation rules for IsPromoOffer

	// no validation rules for PromoTitle

	// no validation rules for PromoOfferDisplayRank

	if all {
		switch v := interface{}(m.GetOfferDescription()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferAdditionalDetailsValidationError{
					field:  "OfferDescription",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferAdditionalDetailsValidationError{
					field:  "OfferDescription",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOfferDescription()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferAdditionalDetailsValidationError{
				field:  "OfferDescription",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AlternateCtaText

	for idx, item := range m.GetDisplayTags() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OfferAdditionalDetailsValidationError{
						field:  fmt.Sprintf("DisplayTags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OfferAdditionalDetailsValidationError{
						field:  fmt.Sprintf("DisplayTags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OfferAdditionalDetailsValidationError{
					field:  fmt.Sprintf("DisplayTags[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for IsExperimental

	// no validation rules for CardOfferType

	if all {
		switch v := interface{}(m.GetConversionRatio()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferAdditionalDetailsValidationError{
					field:  "ConversionRatio",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferAdditionalDetailsValidationError{
					field:  "ConversionRatio",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConversionRatio()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferAdditionalDetailsValidationError{
				field:  "ConversionRatio",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SegmentExpression

	for idx, item := range m.GetExpiryNotificationConfigs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OfferAdditionalDetailsValidationError{
						field:  fmt.Sprintf("ExpiryNotificationConfigs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OfferAdditionalDetailsValidationError{
						field:  fmt.Sprintf("ExpiryNotificationConfigs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OfferAdditionalDetailsValidationError{
					field:  fmt.Sprintf("ExpiryNotificationConfigs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetWatsonMeta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferAdditionalDetailsValidationError{
					field:  "WatsonMeta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferAdditionalDetailsValidationError{
					field:  "WatsonMeta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWatsonMeta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferAdditionalDetailsValidationError{
				field:  "WatsonMeta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ShortName

	// no validation rules for IsFiPoints

	if len(errors) > 0 {
		return OfferAdditionalDetailsMultiError(errors)
	}

	return nil
}

// OfferAdditionalDetailsMultiError is an error wrapping multiple validation
// errors returned by OfferAdditionalDetails.ValidateAll() if the designated
// constraints aren't met.
type OfferAdditionalDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OfferAdditionalDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OfferAdditionalDetailsMultiError) AllErrors() []error { return m }

// OfferAdditionalDetailsValidationError is the validation error returned by
// OfferAdditionalDetails.Validate if the designated constraints aren't met.
type OfferAdditionalDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OfferAdditionalDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OfferAdditionalDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OfferAdditionalDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OfferAdditionalDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OfferAdditionalDetailsValidationError) ErrorName() string {
	return "OfferAdditionalDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e OfferAdditionalDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOfferAdditionalDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OfferAdditionalDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OfferAdditionalDetailsValidationError{}

// Validate checks the field values on VendorOfferMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VendorOfferMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VendorOfferMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VendorOfferMetadataMultiError, or nil if none found.
func (m *VendorOfferMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *VendorOfferMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.VendorOfferMetadata.(type) {
	case *VendorOfferMetadata_LoyltyVendorOfferMetadata:
		if v == nil {
			err := VendorOfferMetadataValidationError{
				field:  "VendorOfferMetadata",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetLoyltyVendorOfferMetadata()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, VendorOfferMetadataValidationError{
						field:  "LoyltyVendorOfferMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, VendorOfferMetadataValidationError{
						field:  "LoyltyVendorOfferMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetLoyltyVendorOfferMetadata()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return VendorOfferMetadataValidationError{
					field:  "LoyltyVendorOfferMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *VendorOfferMetadata_OfflineVendorOfferMetadata:
		if v == nil {
			err := VendorOfferMetadataValidationError{
				field:  "VendorOfferMetadata",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetOfflineVendorOfferMetadata()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, VendorOfferMetadataValidationError{
						field:  "OfflineVendorOfferMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, VendorOfferMetadataValidationError{
						field:  "OfflineVendorOfferMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetOfflineVendorOfferMetadata()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return VendorOfferMetadataValidationError{
					field:  "OfflineVendorOfferMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *VendorOfferMetadata_QwikcilverVendorOfferMetadata:
		if v == nil {
			err := VendorOfferMetadataValidationError{
				field:  "VendorOfferMetadata",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetQwikcilverVendorOfferMetadata()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, VendorOfferMetadataValidationError{
						field:  "QwikcilverVendorOfferMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, VendorOfferMetadataValidationError{
						field:  "QwikcilverVendorOfferMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetQwikcilverVendorOfferMetadata()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return VendorOfferMetadataValidationError{
					field:  "QwikcilverVendorOfferMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *VendorOfferMetadata_ThriweVendorOfferMetadata:
		if v == nil {
			err := VendorOfferMetadataValidationError{
				field:  "VendorOfferMetadata",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetThriweVendorOfferMetadata()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, VendorOfferMetadataValidationError{
						field:  "ThriweVendorOfferMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, VendorOfferMetadataValidationError{
						field:  "ThriweVendorOfferMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetThriweVendorOfferMetadata()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return VendorOfferMetadataValidationError{
					field:  "ThriweVendorOfferMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *VendorOfferMetadata_InHouseVendorOfferMetadata:
		if v == nil {
			err := VendorOfferMetadataValidationError{
				field:  "VendorOfferMetadata",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetInHouseVendorOfferMetadata()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, VendorOfferMetadataValidationError{
						field:  "InHouseVendorOfferMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, VendorOfferMetadataValidationError{
						field:  "InHouseVendorOfferMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetInHouseVendorOfferMetadata()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return VendorOfferMetadataValidationError{
					field:  "InHouseVendorOfferMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return VendorOfferMetadataMultiError(errors)
	}

	return nil
}

// VendorOfferMetadataMultiError is an error wrapping multiple validation
// errors returned by VendorOfferMetadata.ValidateAll() if the designated
// constraints aren't met.
type VendorOfferMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VendorOfferMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VendorOfferMetadataMultiError) AllErrors() []error { return m }

// VendorOfferMetadataValidationError is the validation error returned by
// VendorOfferMetadata.Validate if the designated constraints aren't met.
type VendorOfferMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VendorOfferMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VendorOfferMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VendorOfferMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VendorOfferMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VendorOfferMetadataValidationError) ErrorName() string {
	return "VendorOfferMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e VendorOfferMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVendorOfferMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VendorOfferMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VendorOfferMetadataValidationError{}

// Validate checks the field values on LoyltyVendorOfferMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LoyltyVendorOfferMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoyltyVendorOfferMetadata with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LoyltyVendorOfferMetadataMultiError, or nil if none found.
func (m *LoyltyVendorOfferMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *LoyltyVendorOfferMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SkuId

	// no validation rules for ProductId

	switch v := m.OfferTypeSpecificMetadata.(type) {
	case *LoyltyVendorOfferMetadata_CharityOfferMetadata_:
		if v == nil {
			err := LoyltyVendorOfferMetadataValidationError{
				field:  "OfferTypeSpecificMetadata",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCharityOfferMetadata()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoyltyVendorOfferMetadataValidationError{
						field:  "CharityOfferMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoyltyVendorOfferMetadataValidationError{
						field:  "CharityOfferMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCharityOfferMetadata()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoyltyVendorOfferMetadataValidationError{
					field:  "CharityOfferMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return LoyltyVendorOfferMetadataMultiError(errors)
	}

	return nil
}

// LoyltyVendorOfferMetadataMultiError is an error wrapping multiple validation
// errors returned by LoyltyVendorOfferMetadata.ValidateAll() if the
// designated constraints aren't met.
type LoyltyVendorOfferMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoyltyVendorOfferMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoyltyVendorOfferMetadataMultiError) AllErrors() []error { return m }

// LoyltyVendorOfferMetadataValidationError is the validation error returned by
// LoyltyVendorOfferMetadata.Validate if the designated constraints aren't met.
type LoyltyVendorOfferMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoyltyVendorOfferMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoyltyVendorOfferMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoyltyVendorOfferMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoyltyVendorOfferMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoyltyVendorOfferMetadataValidationError) ErrorName() string {
	return "LoyltyVendorOfferMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e LoyltyVendorOfferMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoyltyVendorOfferMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoyltyVendorOfferMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoyltyVendorOfferMetadataValidationError{}

// Validate checks the field values on QwikcilverVendorOfferMetadata with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QwikcilverVendorOfferMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QwikcilverVendorOfferMetadata with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// QwikcilverVendorOfferMetadataMultiError, or nil if none found.
func (m *QwikcilverVendorOfferMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *QwikcilverVendorOfferMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SkuId

	if len(errors) > 0 {
		return QwikcilverVendorOfferMetadataMultiError(errors)
	}

	return nil
}

// QwikcilverVendorOfferMetadataMultiError is an error wrapping multiple
// validation errors returned by QwikcilverVendorOfferMetadata.ValidateAll()
// if the designated constraints aren't met.
type QwikcilverVendorOfferMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QwikcilverVendorOfferMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QwikcilverVendorOfferMetadataMultiError) AllErrors() []error { return m }

// QwikcilverVendorOfferMetadataValidationError is the validation error
// returned by QwikcilverVendorOfferMetadata.Validate if the designated
// constraints aren't met.
type QwikcilverVendorOfferMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QwikcilverVendorOfferMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QwikcilverVendorOfferMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QwikcilverVendorOfferMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QwikcilverVendorOfferMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QwikcilverVendorOfferMetadataValidationError) ErrorName() string {
	return "QwikcilverVendorOfferMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e QwikcilverVendorOfferMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQwikcilverVendorOfferMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QwikcilverVendorOfferMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QwikcilverVendorOfferMetadataValidationError{}

// Validate checks the field values on OfflineVendorOfferMetadata with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OfflineVendorOfferMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OfflineVendorOfferMetadata with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OfflineVendorOfferMetadataMultiError, or nil if none found.
func (m *OfflineVendorOfferMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *OfflineVendorOfferMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProductId

	if len(errors) > 0 {
		return OfflineVendorOfferMetadataMultiError(errors)
	}

	return nil
}

// OfflineVendorOfferMetadataMultiError is an error wrapping multiple
// validation errors returned by OfflineVendorOfferMetadata.ValidateAll() if
// the designated constraints aren't met.
type OfflineVendorOfferMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OfflineVendorOfferMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OfflineVendorOfferMetadataMultiError) AllErrors() []error { return m }

// OfflineVendorOfferMetadataValidationError is the validation error returned
// by OfflineVendorOfferMetadata.Validate if the designated constraints aren't met.
type OfflineVendorOfferMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OfflineVendorOfferMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OfflineVendorOfferMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OfflineVendorOfferMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OfflineVendorOfferMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OfflineVendorOfferMetadataValidationError) ErrorName() string {
	return "OfflineVendorOfferMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e OfflineVendorOfferMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOfflineVendorOfferMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OfflineVendorOfferMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OfflineVendorOfferMetadataValidationError{}

// Validate checks the field values on ThriweVendorOfferMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ThriweVendorOfferMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ThriweVendorOfferMetadata with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ThriweVendorOfferMetadataMultiError, or nil if none found.
func (m *ThriweVendorOfferMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *ThriweVendorOfferMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BenefitsPackageId

	// no validation rules for BenefitsPackageName

	if all {
		switch v := interface{}(m.GetExpiryDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ThriweVendorOfferMetadataValidationError{
					field:  "ExpiryDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ThriweVendorOfferMetadataValidationError{
					field:  "ExpiryDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpiryDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ThriweVendorOfferMetadataValidationError{
				field:  "ExpiryDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ThriweVendorOfferMetadataMultiError(errors)
	}

	return nil
}

// ThriweVendorOfferMetadataMultiError is an error wrapping multiple validation
// errors returned by ThriweVendorOfferMetadata.ValidateAll() if the
// designated constraints aren't met.
type ThriweVendorOfferMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ThriweVendorOfferMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ThriweVendorOfferMetadataMultiError) AllErrors() []error { return m }

// ThriweVendorOfferMetadataValidationError is the validation error returned by
// ThriweVendorOfferMetadata.Validate if the designated constraints aren't met.
type ThriweVendorOfferMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ThriweVendorOfferMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ThriweVendorOfferMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ThriweVendorOfferMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ThriweVendorOfferMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ThriweVendorOfferMetadataValidationError) ErrorName() string {
	return "ThriweVendorOfferMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e ThriweVendorOfferMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sThriweVendorOfferMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ThriweVendorOfferMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ThriweVendorOfferMetadataValidationError{}

// Validate checks the field values on Offer with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Offer) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Offer with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in OfferMultiError, or nil if none found.
func (m *Offer) ValidateAll() error {
	return m.validate(true)
}

func (m *Offer) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for Desc

	// no validation rules for Price

	// no validation rules for OfferType

	// no validation rules for RedemptionMode

	// no validation rules for VendorName

	if all {
		switch v := interface{}(m.GetVendorOfferMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferValidationError{
					field:  "VendorOfferMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferValidationError{
					field:  "VendorOfferMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVendorOfferMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferValidationError{
				field:  "VendorOfferMetadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ExternalId

	if all {
		switch v := interface{}(m.GetOfferMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferValidationError{
					field:  "OfferMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferValidationError{
					field:  "OfferMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOfferMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferValidationError{
				field:  "OfferMetadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetImages() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OfferValidationError{
						field:  fmt.Sprintf("Images[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OfferValidationError{
						field:  fmt.Sprintf("Images[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OfferValidationError{
					field:  fmt.Sprintf("Images[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetTnc()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferValidationError{
					field:  "Tnc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferValidationError{
					field:  "Tnc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTnc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferValidationError{
				field:  "Tnc",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAdditionalDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferValidationError{
					field:  "AdditionalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferValidationError{
					field:  "AdditionalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAdditionalDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferValidationError{
				field:  "AdditionalDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsDeleted

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDiscountDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferValidationError{
					field:  "DiscountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferValidationError{
					field:  "DiscountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDiscountDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferValidationError{
				field:  "DiscountDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTagsInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferValidationError{
					field:  "TagsInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferValidationError{
					field:  "TagsInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTagsInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferValidationError{
				field:  "TagsInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OfferMultiError(errors)
	}

	return nil
}

// OfferMultiError is an error wrapping multiple validation errors returned by
// Offer.ValidateAll() if the designated constraints aren't met.
type OfferMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OfferMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OfferMultiError) AllErrors() []error { return m }

// OfferValidationError is the validation error returned by Offer.Validate if
// the designated constraints aren't met.
type OfferValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OfferValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OfferValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OfferValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OfferValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OfferValidationError) ErrorName() string { return "OfferValidationError" }

// Error satisfies the builtin error interface
func (e OfferValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOffer.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OfferValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OfferValidationError{}

// Validate checks the field values on OfferMetadata with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *OfferMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OfferMetadata with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in OfferMetadataMultiError, or
// nil if none found.
func (m *OfferMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *OfferMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.OfferTypeSpecificMetadata.(type) {
	case *OfferMetadata_GiftCardMetadata:
		if v == nil {
			err := OfferMetadataValidationError{
				field:  "OfferTypeSpecificMetadata",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetGiftCardMetadata()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OfferMetadataValidationError{
						field:  "GiftCardMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OfferMetadataValidationError{
						field:  "GiftCardMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetGiftCardMetadata()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OfferMetadataValidationError{
					field:  "GiftCardMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *OfferMetadata_CharityMetadata:
		if v == nil {
			err := OfferMetadataValidationError{
				field:  "OfferTypeSpecificMetadata",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCharityMetadata()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OfferMetadataValidationError{
						field:  "CharityMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OfferMetadataValidationError{
						field:  "CharityMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCharityMetadata()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OfferMetadataValidationError{
					field:  "CharityMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *OfferMetadata_CouponMetadata:
		if v == nil {
			err := OfferMetadataValidationError{
				field:  "OfferTypeSpecificMetadata",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCouponMetadata()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OfferMetadataValidationError{
						field:  "CouponMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OfferMetadataValidationError{
						field:  "CouponMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCouponMetadata()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OfferMetadataValidationError{
					field:  "CouponMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *OfferMetadata_PhysicalMerchMetadata:
		if v == nil {
			err := OfferMetadataValidationError{
				field:  "OfferTypeSpecificMetadata",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPhysicalMerchMetadata()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OfferMetadataValidationError{
						field:  "PhysicalMerchMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OfferMetadataValidationError{
						field:  "PhysicalMerchMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPhysicalMerchMetadata()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OfferMetadataValidationError{
					field:  "PhysicalMerchMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *OfferMetadata_PowerUpOfferMetadata:
		if v == nil {
			err := OfferMetadataValidationError{
				field:  "OfferTypeSpecificMetadata",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPowerUpOfferMetadata()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OfferMetadataValidationError{
						field:  "PowerUpOfferMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OfferMetadataValidationError{
						field:  "PowerUpOfferMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPowerUpOfferMetadata()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OfferMetadataValidationError{
					field:  "PowerUpOfferMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *OfferMetadata_ThriweBenefitsPackageOfferMetadata:
		if v == nil {
			err := OfferMetadataValidationError{
				field:  "OfferTypeSpecificMetadata",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetThriweBenefitsPackageOfferMetadata()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OfferMetadataValidationError{
						field:  "ThriweBenefitsPackageOfferMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OfferMetadataValidationError{
						field:  "ThriweBenefitsPackageOfferMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetThriweBenefitsPackageOfferMetadata()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OfferMetadataValidationError{
					field:  "ThriweBenefitsPackageOfferMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *OfferMetadata_VistaraAirMilesOfferMetadata:
		if v == nil {
			err := OfferMetadataValidationError{
				field:  "OfferTypeSpecificMetadata",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetVistaraAirMilesOfferMetadata()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OfferMetadataValidationError{
						field:  "VistaraAirMilesOfferMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OfferMetadataValidationError{
						field:  "VistaraAirMilesOfferMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetVistaraAirMilesOfferMetadata()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OfferMetadataValidationError{
					field:  "VistaraAirMilesOfferMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *OfferMetadata_ExternalVendorOfferMetadata:
		if v == nil {
			err := OfferMetadataValidationError{
				field:  "OfferTypeSpecificMetadata",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetExternalVendorOfferMetadata()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OfferMetadataValidationError{
						field:  "ExternalVendorOfferMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OfferMetadataValidationError{
						field:  "ExternalVendorOfferMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetExternalVendorOfferMetadata()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OfferMetadataValidationError{
					field:  "ExternalVendorOfferMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *OfferMetadata_FiCoinsToPointsOfferMetadata:
		if v == nil {
			err := OfferMetadataValidationError{
				field:  "OfferTypeSpecificMetadata",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFiCoinsToPointsOfferMetadata()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OfferMetadataValidationError{
						field:  "FiCoinsToPointsOfferMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OfferMetadataValidationError{
						field:  "FiCoinsToPointsOfferMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFiCoinsToPointsOfferMetadata()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OfferMetadataValidationError{
					field:  "FiCoinsToPointsOfferMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return OfferMetadataMultiError(errors)
	}

	return nil
}

// OfferMetadataMultiError is an error wrapping multiple validation errors
// returned by OfferMetadata.ValidateAll() if the designated constraints
// aren't met.
type OfferMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OfferMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OfferMetadataMultiError) AllErrors() []error { return m }

// OfferMetadataValidationError is the validation error returned by
// OfferMetadata.Validate if the designated constraints aren't met.
type OfferMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OfferMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OfferMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OfferMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OfferMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OfferMetadataValidationError) ErrorName() string { return "OfferMetadataValidationError" }

// Error satisfies the builtin error interface
func (e OfferMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOfferMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OfferMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OfferMetadataValidationError{}

// Validate checks the field values on GiftCardOfferMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GiftCardOfferMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GiftCardOfferMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GiftCardOfferMetadataMultiError, or nil if none found.
func (m *GiftCardOfferMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *GiftCardOfferMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for GiftCardValue

	if len(errors) > 0 {
		return GiftCardOfferMetadataMultiError(errors)
	}

	return nil
}

// GiftCardOfferMetadataMultiError is an error wrapping multiple validation
// errors returned by GiftCardOfferMetadata.ValidateAll() if the designated
// constraints aren't met.
type GiftCardOfferMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GiftCardOfferMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GiftCardOfferMetadataMultiError) AllErrors() []error { return m }

// GiftCardOfferMetadataValidationError is the validation error returned by
// GiftCardOfferMetadata.Validate if the designated constraints aren't met.
type GiftCardOfferMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GiftCardOfferMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GiftCardOfferMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GiftCardOfferMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GiftCardOfferMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GiftCardOfferMetadataValidationError) ErrorName() string {
	return "GiftCardOfferMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e GiftCardOfferMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGiftCardOfferMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GiftCardOfferMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GiftCardOfferMetadataValidationError{}

// Validate checks the field values on CharityOfferMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CharityOfferMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CharityOfferMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CharityOfferMetadataMultiError, or nil if none found.
func (m *CharityOfferMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *CharityOfferMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CharityAmount

	if len(errors) > 0 {
		return CharityOfferMetadataMultiError(errors)
	}

	return nil
}

// CharityOfferMetadataMultiError is an error wrapping multiple validation
// errors returned by CharityOfferMetadata.ValidateAll() if the designated
// constraints aren't met.
type CharityOfferMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CharityOfferMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CharityOfferMetadataMultiError) AllErrors() []error { return m }

// CharityOfferMetadataValidationError is the validation error returned by
// CharityOfferMetadata.Validate if the designated constraints aren't met.
type CharityOfferMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CharityOfferMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CharityOfferMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CharityOfferMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CharityOfferMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CharityOfferMetadataValidationError) ErrorName() string {
	return "CharityOfferMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e CharityOfferMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCharityOfferMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CharityOfferMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CharityOfferMetadataValidationError{}

// Validate checks the field values on PhysicalMerchandiseOfferMetadata with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *PhysicalMerchandiseOfferMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PhysicalMerchandiseOfferMetadata with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// PhysicalMerchandiseOfferMetadataMultiError, or nil if none found.
func (m *PhysicalMerchandiseOfferMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *PhysicalMerchandiseOfferMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MerchValue

	if len(errors) > 0 {
		return PhysicalMerchandiseOfferMetadataMultiError(errors)
	}

	return nil
}

// PhysicalMerchandiseOfferMetadataMultiError is an error wrapping multiple
// validation errors returned by
// PhysicalMerchandiseOfferMetadata.ValidateAll() if the designated
// constraints aren't met.
type PhysicalMerchandiseOfferMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PhysicalMerchandiseOfferMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PhysicalMerchandiseOfferMetadataMultiError) AllErrors() []error { return m }

// PhysicalMerchandiseOfferMetadataValidationError is the validation error
// returned by PhysicalMerchandiseOfferMetadata.Validate if the designated
// constraints aren't met.
type PhysicalMerchandiseOfferMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PhysicalMerchandiseOfferMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PhysicalMerchandiseOfferMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PhysicalMerchandiseOfferMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PhysicalMerchandiseOfferMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PhysicalMerchandiseOfferMetadataValidationError) ErrorName() string {
	return "PhysicalMerchandiseOfferMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e PhysicalMerchandiseOfferMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPhysicalMerchandiseOfferMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PhysicalMerchandiseOfferMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PhysicalMerchandiseOfferMetadataValidationError{}

// Validate checks the field values on CouponOfferMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CouponOfferMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CouponOfferMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CouponOfferMetadataMultiError, or nil if none found.
func (m *CouponOfferMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *CouponOfferMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CouponCode

	// no validation rules for CouponCodeV2

	if len(errors) > 0 {
		return CouponOfferMetadataMultiError(errors)
	}

	return nil
}

// CouponOfferMetadataMultiError is an error wrapping multiple validation
// errors returned by CouponOfferMetadata.ValidateAll() if the designated
// constraints aren't met.
type CouponOfferMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CouponOfferMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CouponOfferMetadataMultiError) AllErrors() []error { return m }

// CouponOfferMetadataValidationError is the validation error returned by
// CouponOfferMetadata.Validate if the designated constraints aren't met.
type CouponOfferMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CouponOfferMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CouponOfferMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CouponOfferMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CouponOfferMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CouponOfferMetadataValidationError) ErrorName() string {
	return "CouponOfferMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e CouponOfferMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCouponOfferMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CouponOfferMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CouponOfferMetadataValidationError{}

// Validate checks the field values on ThriweBenefitsPackageOfferMetadata with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ThriweBenefitsPackageOfferMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ThriweBenefitsPackageOfferMetadata
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ThriweBenefitsPackageOfferMetadataMultiError, or nil if none found.
func (m *ThriweBenefitsPackageOfferMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *ThriweBenefitsPackageOfferMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActivationUrl

	if len(errors) > 0 {
		return ThriweBenefitsPackageOfferMetadataMultiError(errors)
	}

	return nil
}

// ThriweBenefitsPackageOfferMetadataMultiError is an error wrapping multiple
// validation errors returned by
// ThriweBenefitsPackageOfferMetadata.ValidateAll() if the designated
// constraints aren't met.
type ThriweBenefitsPackageOfferMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ThriweBenefitsPackageOfferMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ThriweBenefitsPackageOfferMetadataMultiError) AllErrors() []error { return m }

// ThriweBenefitsPackageOfferMetadataValidationError is the validation error
// returned by ThriweBenefitsPackageOfferMetadata.Validate if the designated
// constraints aren't met.
type ThriweBenefitsPackageOfferMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ThriweBenefitsPackageOfferMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ThriweBenefitsPackageOfferMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ThriweBenefitsPackageOfferMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ThriweBenefitsPackageOfferMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ThriweBenefitsPackageOfferMetadataValidationError) ErrorName() string {
	return "ThriweBenefitsPackageOfferMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e ThriweBenefitsPackageOfferMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sThriweBenefitsPackageOfferMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ThriweBenefitsPackageOfferMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ThriweBenefitsPackageOfferMetadataValidationError{}

// Validate checks the field values on VistaraAirMilesOfferMetadata with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VistaraAirMilesOfferMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VistaraAirMilesOfferMetadata with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VistaraAirMilesOfferMetadataMultiError, or nil if none found.
func (m *VistaraAirMilesOfferMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *VistaraAirMilesOfferMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FiCoinsToCvPointsConversionRatio

	// no validation rules for MinConvertableFiCoins

	// no validation rules for MaxConvertableFiCoins

	if all {
		switch v := interface{}(m.GetCvPointsBanner()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VistaraAirMilesOfferMetadataValidationError{
					field:  "CvPointsBanner",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VistaraAirMilesOfferMetadataValidationError{
					field:  "CvPointsBanner",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCvPointsBanner()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VistaraAirMilesOfferMetadataValidationError{
				field:  "CvPointsBanner",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VistaraAirMilesOfferMetadataMultiError(errors)
	}

	return nil
}

// VistaraAirMilesOfferMetadataMultiError is an error wrapping multiple
// validation errors returned by VistaraAirMilesOfferMetadata.ValidateAll() if
// the designated constraints aren't met.
type VistaraAirMilesOfferMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VistaraAirMilesOfferMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VistaraAirMilesOfferMetadataMultiError) AllErrors() []error { return m }

// VistaraAirMilesOfferMetadataValidationError is the validation error returned
// by VistaraAirMilesOfferMetadata.Validate if the designated constraints
// aren't met.
type VistaraAirMilesOfferMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VistaraAirMilesOfferMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VistaraAirMilesOfferMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VistaraAirMilesOfferMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VistaraAirMilesOfferMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VistaraAirMilesOfferMetadataValidationError) ErrorName() string {
	return "VistaraAirMilesOfferMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e VistaraAirMilesOfferMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVistaraAirMilesOfferMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VistaraAirMilesOfferMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VistaraAirMilesOfferMetadataValidationError{}

// Validate checks the field values on FiCoinsToPointsOfferMetadata with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FiCoinsToPointsOfferMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FiCoinsToPointsOfferMetadata with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FiCoinsToPointsOfferMetadataMultiError, or nil if none found.
func (m *FiCoinsToPointsOfferMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *FiCoinsToPointsOfferMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FiCoinsToPointsConversionMultiplier

	// no validation rules for FiCoinsStepSizeMultiplier

	// no validation rules for MinConvertableFiCoins

	// no validation rules for MaxConvertableFiCoins

	if all {
		switch v := interface{}(m.GetPointsBanner()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FiCoinsToPointsOfferMetadataValidationError{
					field:  "PointsBanner",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FiCoinsToPointsOfferMetadataValidationError{
					field:  "PointsBanner",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPointsBanner()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FiCoinsToPointsOfferMetadataValidationError{
				field:  "PointsBanner",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FiCoinsToPointsOfferMetadataMultiError(errors)
	}

	return nil
}

// FiCoinsToPointsOfferMetadataMultiError is an error wrapping multiple
// validation errors returned by FiCoinsToPointsOfferMetadata.ValidateAll() if
// the designated constraints aren't met.
type FiCoinsToPointsOfferMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FiCoinsToPointsOfferMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FiCoinsToPointsOfferMetadataMultiError) AllErrors() []error { return m }

// FiCoinsToPointsOfferMetadataValidationError is the validation error returned
// by FiCoinsToPointsOfferMetadata.Validate if the designated constraints
// aren't met.
type FiCoinsToPointsOfferMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FiCoinsToPointsOfferMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FiCoinsToPointsOfferMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FiCoinsToPointsOfferMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FiCoinsToPointsOfferMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FiCoinsToPointsOfferMetadataValidationError) ErrorName() string {
	return "FiCoinsToPointsOfferMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e FiCoinsToPointsOfferMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFiCoinsToPointsOfferMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FiCoinsToPointsOfferMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FiCoinsToPointsOfferMetadataValidationError{}

// Validate checks the field values on ExternalVendorOfferMetadata with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExternalVendorOfferMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExternalVendorOfferMetadata with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExternalVendorOfferMetadataMultiError, or nil if none found.
func (m *ExternalVendorOfferMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *ExternalVendorOfferMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BaseUrl

	// no validation rules for WebPageTitle

	if len(errors) > 0 {
		return ExternalVendorOfferMetadataMultiError(errors)
	}

	return nil
}

// ExternalVendorOfferMetadataMultiError is an error wrapping multiple
// validation errors returned by ExternalVendorOfferMetadata.ValidateAll() if
// the designated constraints aren't met.
type ExternalVendorOfferMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExternalVendorOfferMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExternalVendorOfferMetadataMultiError) AllErrors() []error { return m }

// ExternalVendorOfferMetadataValidationError is the validation error returned
// by ExternalVendorOfferMetadata.Validate if the designated constraints
// aren't met.
type ExternalVendorOfferMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExternalVendorOfferMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExternalVendorOfferMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExternalVendorOfferMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExternalVendorOfferMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExternalVendorOfferMetadataValidationError) ErrorName() string {
	return "ExternalVendorOfferMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e ExternalVendorOfferMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExternalVendorOfferMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExternalVendorOfferMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExternalVendorOfferMetadataValidationError{}

// Validate checks the field values on DiscountDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DiscountDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DiscountDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DiscountDetailsMultiError, or nil if none found.
func (m *DiscountDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *DiscountDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DiscountedPrice

	// no validation rules for DisplayMode

	if all {
		switch v := interface{}(m.GetDiscountEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DiscountDetailsValidationError{
					field:  "DiscountEndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DiscountDetailsValidationError{
					field:  "DiscountEndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDiscountEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DiscountDetailsValidationError{
				field:  "DiscountEndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DiscountDetailsMultiError(errors)
	}

	return nil
}

// DiscountDetailsMultiError is an error wrapping multiple validation errors
// returned by DiscountDetails.ValidateAll() if the designated constraints
// aren't met.
type DiscountDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DiscountDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DiscountDetailsMultiError) AllErrors() []error { return m }

// DiscountDetailsValidationError is the validation error returned by
// DiscountDetails.Validate if the designated constraints aren't met.
type DiscountDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DiscountDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DiscountDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DiscountDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DiscountDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DiscountDetailsValidationError) ErrorName() string { return "DiscountDetailsValidationError" }

// Error satisfies the builtin error interface
func (e DiscountDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDiscountDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DiscountDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DiscountDetailsValidationError{}

// Validate checks the field values on RedemptionRateLimitMetadata with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RedemptionRateLimitMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RedemptionRateLimitMetadata with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RedemptionRateLimitMetadataMultiError, or nil if none found.
func (m *RedemptionRateLimitMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *RedemptionRateLimitMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CoolOffHours

	if len(errors) > 0 {
		return RedemptionRateLimitMetadataMultiError(errors)
	}

	return nil
}

// RedemptionRateLimitMetadataMultiError is an error wrapping multiple
// validation errors returned by RedemptionRateLimitMetadata.ValidateAll() if
// the designated constraints aren't met.
type RedemptionRateLimitMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RedemptionRateLimitMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RedemptionRateLimitMetadataMultiError) AllErrors() []error { return m }

// RedemptionRateLimitMetadataValidationError is the validation error returned
// by RedemptionRateLimitMetadata.Validate if the designated constraints
// aren't met.
type RedemptionRateLimitMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RedemptionRateLimitMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RedemptionRateLimitMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RedemptionRateLimitMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RedemptionRateLimitMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RedemptionRateLimitMetadataValidationError) ErrorName() string {
	return "RedemptionRateLimitMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e RedemptionRateLimitMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRedemptionRateLimitMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RedemptionRateLimitMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RedemptionRateLimitMetadataValidationError{}

// Validate checks the field values on PowerUpOfferMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PowerUpOfferMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PowerUpOfferMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PowerUpOfferMetadataMultiError, or nil if none found.
func (m *PowerUpOfferMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *PowerUpOfferMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRedemptionRateLimitMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PowerUpOfferMetadataValidationError{
					field:  "RedemptionRateLimitMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PowerUpOfferMetadataValidationError{
					field:  "RedemptionRateLimitMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRedemptionRateLimitMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PowerUpOfferMetadataValidationError{
				field:  "RedemptionRateLimitMetadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetExtraMetadata() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PowerUpOfferMetadataValidationError{
						field:  fmt.Sprintf("ExtraMetadata[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PowerUpOfferMetadataValidationError{
						field:  fmt.Sprintf("ExtraMetadata[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PowerUpOfferMetadataValidationError{
					field:  fmt.Sprintf("ExtraMetadata[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PowerUpOfferMetadataMultiError(errors)
	}

	return nil
}

// PowerUpOfferMetadataMultiError is an error wrapping multiple validation
// errors returned by PowerUpOfferMetadata.ValidateAll() if the designated
// constraints aren't met.
type PowerUpOfferMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PowerUpOfferMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PowerUpOfferMetadataMultiError) AllErrors() []error { return m }

// PowerUpOfferMetadataValidationError is the validation error returned by
// PowerUpOfferMetadata.Validate if the designated constraints aren't met.
type PowerUpOfferMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PowerUpOfferMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PowerUpOfferMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PowerUpOfferMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PowerUpOfferMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PowerUpOfferMetadataValidationError) ErrorName() string {
	return "PowerUpOfferMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e PowerUpOfferMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPowerUpOfferMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PowerUpOfferMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PowerUpOfferMetadataValidationError{}

// Validate checks the field values on InHouseVendorOfferMetadata with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InHouseVendorOfferMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InHouseVendorOfferMetadata with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InHouseVendorOfferMetadataMultiError, or nil if none found.
func (m *InHouseVendorOfferMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *InHouseVendorOfferMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SkuId

	if len(errors) > 0 {
		return InHouseVendorOfferMetadataMultiError(errors)
	}

	return nil
}

// InHouseVendorOfferMetadataMultiError is an error wrapping multiple
// validation errors returned by InHouseVendorOfferMetadata.ValidateAll() if
// the designated constraints aren't met.
type InHouseVendorOfferMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InHouseVendorOfferMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InHouseVendorOfferMetadataMultiError) AllErrors() []error { return m }

// InHouseVendorOfferMetadataValidationError is the validation error returned
// by InHouseVendorOfferMetadata.Validate if the designated constraints aren't met.
type InHouseVendorOfferMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InHouseVendorOfferMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InHouseVendorOfferMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InHouseVendorOfferMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InHouseVendorOfferMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InHouseVendorOfferMetadataValidationError) ErrorName() string {
	return "InHouseVendorOfferMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e InHouseVendorOfferMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInHouseVendorOfferMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InHouseVendorOfferMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InHouseVendorOfferMetadataValidationError{}

// Validate checks the field values on FiCoinsToCashConversionRatio with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FiCoinsToCashConversionRatio) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FiCoinsToCashConversionRatio with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FiCoinsToCashConversionRatioMultiError, or nil if none found.
func (m *FiCoinsToCashConversionRatio) ValidateAll() error {
	return m.validate(true)
}

func (m *FiCoinsToCashConversionRatio) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FiCoinsValue

	// no validation rules for CashEquivalent

	if len(errors) > 0 {
		return FiCoinsToCashConversionRatioMultiError(errors)
	}

	return nil
}

// FiCoinsToCashConversionRatioMultiError is an error wrapping multiple
// validation errors returned by FiCoinsToCashConversionRatio.ValidateAll() if
// the designated constraints aren't met.
type FiCoinsToCashConversionRatioMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FiCoinsToCashConversionRatioMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FiCoinsToCashConversionRatioMultiError) AllErrors() []error { return m }

// FiCoinsToCashConversionRatioValidationError is the validation error returned
// by FiCoinsToCashConversionRatio.Validate if the designated constraints
// aren't met.
type FiCoinsToCashConversionRatioValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FiCoinsToCashConversionRatioValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FiCoinsToCashConversionRatioValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FiCoinsToCashConversionRatioValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FiCoinsToCashConversionRatioValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FiCoinsToCashConversionRatioValidationError) ErrorName() string {
	return "FiCoinsToCashConversionRatioValidationError"
}

// Error satisfies the builtin error interface
func (e FiCoinsToCashConversionRatioValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFiCoinsToCashConversionRatio.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FiCoinsToCashConversionRatioValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FiCoinsToCashConversionRatioValidationError{}

// Validate checks the field values on WatsonMeta with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *WatsonMeta) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WatsonMeta with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in WatsonMetaMultiError, or
// nil if none found.
func (m *WatsonMeta) ValidateAll() error {
	return m.validate(true)
}

func (m *WatsonMeta) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ShouldCreateWatsonEvent

	// no validation rules for IssueCategoryId

	if len(errors) > 0 {
		return WatsonMetaMultiError(errors)
	}

	return nil
}

// WatsonMetaMultiError is an error wrapping multiple validation errors
// returned by WatsonMeta.ValidateAll() if the designated constraints aren't met.
type WatsonMetaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WatsonMetaMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WatsonMetaMultiError) AllErrors() []error { return m }

// WatsonMetaValidationError is the validation error returned by
// WatsonMeta.Validate if the designated constraints aren't met.
type WatsonMetaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WatsonMetaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WatsonMetaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WatsonMetaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WatsonMetaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WatsonMetaValidationError) ErrorName() string { return "WatsonMetaValidationError" }

// Error satisfies the builtin error interface
func (e WatsonMetaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWatsonMeta.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WatsonMetaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WatsonMetaValidationError{}

// Validate checks the field values on OfferAdditionalDetails_OfferDescription
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *OfferAdditionalDetails_OfferDescription) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// OfferAdditionalDetails_OfferDescription with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// OfferAdditionalDetails_OfferDescriptionMultiError, or nil if none found.
func (m *OfferAdditionalDetails_OfferDescription) ValidateAll() error {
	return m.validate(true)
}

func (m *OfferAdditionalDetails_OfferDescription) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Heading

	if len(errors) > 0 {
		return OfferAdditionalDetails_OfferDescriptionMultiError(errors)
	}

	return nil
}

// OfferAdditionalDetails_OfferDescriptionMultiError is an error wrapping
// multiple validation errors returned by
// OfferAdditionalDetails_OfferDescription.ValidateAll() if the designated
// constraints aren't met.
type OfferAdditionalDetails_OfferDescriptionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OfferAdditionalDetails_OfferDescriptionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OfferAdditionalDetails_OfferDescriptionMultiError) AllErrors() []error { return m }

// OfferAdditionalDetails_OfferDescriptionValidationError is the validation
// error returned by OfferAdditionalDetails_OfferDescription.Validate if the
// designated constraints aren't met.
type OfferAdditionalDetails_OfferDescriptionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OfferAdditionalDetails_OfferDescriptionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OfferAdditionalDetails_OfferDescriptionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OfferAdditionalDetails_OfferDescriptionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OfferAdditionalDetails_OfferDescriptionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OfferAdditionalDetails_OfferDescriptionValidationError) ErrorName() string {
	return "OfferAdditionalDetails_OfferDescriptionValidationError"
}

// Error satisfies the builtin error interface
func (e OfferAdditionalDetails_OfferDescriptionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOfferAdditionalDetails_OfferDescription.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OfferAdditionalDetails_OfferDescriptionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OfferAdditionalDetails_OfferDescriptionValidationError{}

// Validate checks the field values on OfferAdditionalDetails_DisplayTag with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *OfferAdditionalDetails_DisplayTag) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OfferAdditionalDetails_DisplayTag
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// OfferAdditionalDetails_DisplayTagMultiError, or nil if none found.
func (m *OfferAdditionalDetails_DisplayTag) ValidateAll() error {
	return m.validate(true)
}

func (m *OfferAdditionalDetails_DisplayTag) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Text

	// no validation rules for TextColor

	// no validation rules for BgColor

	if len(errors) > 0 {
		return OfferAdditionalDetails_DisplayTagMultiError(errors)
	}

	return nil
}

// OfferAdditionalDetails_DisplayTagMultiError is an error wrapping multiple
// validation errors returned by
// OfferAdditionalDetails_DisplayTag.ValidateAll() if the designated
// constraints aren't met.
type OfferAdditionalDetails_DisplayTagMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OfferAdditionalDetails_DisplayTagMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OfferAdditionalDetails_DisplayTagMultiError) AllErrors() []error { return m }

// OfferAdditionalDetails_DisplayTagValidationError is the validation error
// returned by OfferAdditionalDetails_DisplayTag.Validate if the designated
// constraints aren't met.
type OfferAdditionalDetails_DisplayTagValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OfferAdditionalDetails_DisplayTagValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OfferAdditionalDetails_DisplayTagValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OfferAdditionalDetails_DisplayTagValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OfferAdditionalDetails_DisplayTagValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OfferAdditionalDetails_DisplayTagValidationError) ErrorName() string {
	return "OfferAdditionalDetails_DisplayTagValidationError"
}

// Error satisfies the builtin error interface
func (e OfferAdditionalDetails_DisplayTagValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOfferAdditionalDetails_DisplayTag.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OfferAdditionalDetails_DisplayTagValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OfferAdditionalDetails_DisplayTagValidationError{}

// Validate checks the field values on OfferAdditionalDetails_MessageConfig
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *OfferAdditionalDetails_MessageConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OfferAdditionalDetails_MessageConfig
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// OfferAdditionalDetails_MessageConfigMultiError, or nil if none found.
func (m *OfferAdditionalDetails_MessageConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *OfferAdditionalDetails_MessageConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Title

	// no validation rules for Body

	// no validation rules for ImageUrl

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferAdditionalDetails_MessageConfigValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferAdditionalDetails_MessageConfigValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferAdditionalDetails_MessageConfigValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OfferAdditionalDetails_MessageConfigMultiError(errors)
	}

	return nil
}

// OfferAdditionalDetails_MessageConfigMultiError is an error wrapping multiple
// validation errors returned by
// OfferAdditionalDetails_MessageConfig.ValidateAll() if the designated
// constraints aren't met.
type OfferAdditionalDetails_MessageConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OfferAdditionalDetails_MessageConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OfferAdditionalDetails_MessageConfigMultiError) AllErrors() []error { return m }

// OfferAdditionalDetails_MessageConfigValidationError is the validation error
// returned by OfferAdditionalDetails_MessageConfig.Validate if the designated
// constraints aren't met.
type OfferAdditionalDetails_MessageConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OfferAdditionalDetails_MessageConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OfferAdditionalDetails_MessageConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OfferAdditionalDetails_MessageConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OfferAdditionalDetails_MessageConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OfferAdditionalDetails_MessageConfigValidationError) ErrorName() string {
	return "OfferAdditionalDetails_MessageConfigValidationError"
}

// Error satisfies the builtin error interface
func (e OfferAdditionalDetails_MessageConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOfferAdditionalDetails_MessageConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OfferAdditionalDetails_MessageConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OfferAdditionalDetails_MessageConfigValidationError{}

// Validate checks the field values on
// OfferAdditionalDetails_ExpiryNotificationConfig with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *OfferAdditionalDetails_ExpiryNotificationConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// OfferAdditionalDetails_ExpiryNotificationConfig with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// OfferAdditionalDetails_ExpiryNotificationConfigMultiError, or nil if none found.
func (m *OfferAdditionalDetails_ExpiryNotificationConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *OfferAdditionalDetails_ExpiryNotificationConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DaysBeforeExpiry

	if all {
		switch v := interface{}(m.GetMessageConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferAdditionalDetails_ExpiryNotificationConfigValidationError{
					field:  "MessageConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferAdditionalDetails_ExpiryNotificationConfigValidationError{
					field:  "MessageConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMessageConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferAdditionalDetails_ExpiryNotificationConfigValidationError{
				field:  "MessageConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for NotificationType

	if len(errors) > 0 {
		return OfferAdditionalDetails_ExpiryNotificationConfigMultiError(errors)
	}

	return nil
}

// OfferAdditionalDetails_ExpiryNotificationConfigMultiError is an error
// wrapping multiple validation errors returned by
// OfferAdditionalDetails_ExpiryNotificationConfig.ValidateAll() if the
// designated constraints aren't met.
type OfferAdditionalDetails_ExpiryNotificationConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OfferAdditionalDetails_ExpiryNotificationConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OfferAdditionalDetails_ExpiryNotificationConfigMultiError) AllErrors() []error { return m }

// OfferAdditionalDetails_ExpiryNotificationConfigValidationError is the
// validation error returned by
// OfferAdditionalDetails_ExpiryNotificationConfig.Validate if the designated
// constraints aren't met.
type OfferAdditionalDetails_ExpiryNotificationConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OfferAdditionalDetails_ExpiryNotificationConfigValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e OfferAdditionalDetails_ExpiryNotificationConfigValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e OfferAdditionalDetails_ExpiryNotificationConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OfferAdditionalDetails_ExpiryNotificationConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OfferAdditionalDetails_ExpiryNotificationConfigValidationError) ErrorName() string {
	return "OfferAdditionalDetails_ExpiryNotificationConfigValidationError"
}

// Error satisfies the builtin error interface
func (e OfferAdditionalDetails_ExpiryNotificationConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOfferAdditionalDetails_ExpiryNotificationConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OfferAdditionalDetails_ExpiryNotificationConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OfferAdditionalDetails_ExpiryNotificationConfigValidationError{}

// Validate checks the field values on
// LoyltyVendorOfferMetadata_CharityOfferMetadata with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LoyltyVendorOfferMetadata_CharityOfferMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LoyltyVendorOfferMetadata_CharityOfferMetadata with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// LoyltyVendorOfferMetadata_CharityOfferMetadataMultiError, or nil if none found.
func (m *LoyltyVendorOfferMetadata_CharityOfferMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *LoyltyVendorOfferMetadata_CharityOfferMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Category

	// no validation rules for CharityName

	// no validation rules for CharityCode

	// no validation rules for ProductName

	if len(errors) > 0 {
		return LoyltyVendorOfferMetadata_CharityOfferMetadataMultiError(errors)
	}

	return nil
}

// LoyltyVendorOfferMetadata_CharityOfferMetadataMultiError is an error
// wrapping multiple validation errors returned by
// LoyltyVendorOfferMetadata_CharityOfferMetadata.ValidateAll() if the
// designated constraints aren't met.
type LoyltyVendorOfferMetadata_CharityOfferMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoyltyVendorOfferMetadata_CharityOfferMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoyltyVendorOfferMetadata_CharityOfferMetadataMultiError) AllErrors() []error { return m }

// LoyltyVendorOfferMetadata_CharityOfferMetadataValidationError is the
// validation error returned by
// LoyltyVendorOfferMetadata_CharityOfferMetadata.Validate if the designated
// constraints aren't met.
type LoyltyVendorOfferMetadata_CharityOfferMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoyltyVendorOfferMetadata_CharityOfferMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoyltyVendorOfferMetadata_CharityOfferMetadataValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e LoyltyVendorOfferMetadata_CharityOfferMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoyltyVendorOfferMetadata_CharityOfferMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoyltyVendorOfferMetadata_CharityOfferMetadataValidationError) ErrorName() string {
	return "LoyltyVendorOfferMetadata_CharityOfferMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e LoyltyVendorOfferMetadata_CharityOfferMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoyltyVendorOfferMetadata_CharityOfferMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoyltyVendorOfferMetadata_CharityOfferMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoyltyVendorOfferMetadata_CharityOfferMetadataValidationError{}

// Validate checks the field values on VistaraAirMilesOfferMetadata_Banner with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *VistaraAirMilesOfferMetadata_Banner) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VistaraAirMilesOfferMetadata_Banner
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// VistaraAirMilesOfferMetadata_BannerMultiError, or nil if none found.
func (m *VistaraAirMilesOfferMetadata_Banner) ValidateAll() error {
	return m.validate(true)
}

func (m *VistaraAirMilesOfferMetadata_Banner) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LeftImgUrl

	// no validation rules for RightImgUrl

	// no validation rules for BgColor

	if len(errors) > 0 {
		return VistaraAirMilesOfferMetadata_BannerMultiError(errors)
	}

	return nil
}

// VistaraAirMilesOfferMetadata_BannerMultiError is an error wrapping multiple
// validation errors returned by
// VistaraAirMilesOfferMetadata_Banner.ValidateAll() if the designated
// constraints aren't met.
type VistaraAirMilesOfferMetadata_BannerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VistaraAirMilesOfferMetadata_BannerMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VistaraAirMilesOfferMetadata_BannerMultiError) AllErrors() []error { return m }

// VistaraAirMilesOfferMetadata_BannerValidationError is the validation error
// returned by VistaraAirMilesOfferMetadata_Banner.Validate if the designated
// constraints aren't met.
type VistaraAirMilesOfferMetadata_BannerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VistaraAirMilesOfferMetadata_BannerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VistaraAirMilesOfferMetadata_BannerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VistaraAirMilesOfferMetadata_BannerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VistaraAirMilesOfferMetadata_BannerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VistaraAirMilesOfferMetadata_BannerValidationError) ErrorName() string {
	return "VistaraAirMilesOfferMetadata_BannerValidationError"
}

// Error satisfies the builtin error interface
func (e VistaraAirMilesOfferMetadata_BannerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVistaraAirMilesOfferMetadata_Banner.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VistaraAirMilesOfferMetadata_BannerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VistaraAirMilesOfferMetadata_BannerValidationError{}

// Validate checks the field values on FiCoinsToPointsOfferMetadata_Banner with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *FiCoinsToPointsOfferMetadata_Banner) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FiCoinsToPointsOfferMetadata_Banner
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// FiCoinsToPointsOfferMetadata_BannerMultiError, or nil if none found.
func (m *FiCoinsToPointsOfferMetadata_Banner) ValidateAll() error {
	return m.validate(true)
}

func (m *FiCoinsToPointsOfferMetadata_Banner) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Description

	// no validation rules for LeftImgUrl

	// no validation rules for RightImgUrl

	// no validation rules for BgColor

	if len(errors) > 0 {
		return FiCoinsToPointsOfferMetadata_BannerMultiError(errors)
	}

	return nil
}

// FiCoinsToPointsOfferMetadata_BannerMultiError is an error wrapping multiple
// validation errors returned by
// FiCoinsToPointsOfferMetadata_Banner.ValidateAll() if the designated
// constraints aren't met.
type FiCoinsToPointsOfferMetadata_BannerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FiCoinsToPointsOfferMetadata_BannerMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FiCoinsToPointsOfferMetadata_BannerMultiError) AllErrors() []error { return m }

// FiCoinsToPointsOfferMetadata_BannerValidationError is the validation error
// returned by FiCoinsToPointsOfferMetadata_Banner.Validate if the designated
// constraints aren't met.
type FiCoinsToPointsOfferMetadata_BannerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FiCoinsToPointsOfferMetadata_BannerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FiCoinsToPointsOfferMetadata_BannerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FiCoinsToPointsOfferMetadata_BannerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FiCoinsToPointsOfferMetadata_BannerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FiCoinsToPointsOfferMetadata_BannerValidationError) ErrorName() string {
	return "FiCoinsToPointsOfferMetadata_BannerValidationError"
}

// Error satisfies the builtin error interface
func (e FiCoinsToPointsOfferMetadata_BannerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFiCoinsToPointsOfferMetadata_Banner.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FiCoinsToPointsOfferMetadata_BannerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FiCoinsToPointsOfferMetadata_BannerValidationError{}
