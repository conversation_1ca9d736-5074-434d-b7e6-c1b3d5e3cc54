// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/casper/offer_catalog.proto

package casper

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	discounts "github.com/epifi/gamma/api/casper/discounts"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Types of Offers
type OfferType int32

const (
	// default if offer type is not mentioned
	OfferType_UNSPECIFIED_OFFER_TYPE OfferType = 0
	// for offers like amazon gift card of x amount
	// in this type a promo code will be shared to the user
	OfferType_GIFT_CARD OfferType = 1
	// in this type coupon code will be shared to the user
	// to access the offer on merchant interface(offline store/web/app)
	OfferType_COUPON OfferType = 2
	// no code will be shared to the user, instead user's identity(email/mob number)
	// will be whitelisted on the merchant
	OfferType_SUBSCRIPTION OfferType = 3
	// includes offers like Donation of 250 Rs to Give India Charity foundation.
	OfferType_CHARITY OfferType = 4
	// includes offers that need to be shipped like a gift hamper.
	OfferType_PHYSICAL_MERCHANDISE OfferType = 5
	// includes offers that provide rewards on spending on specific merchants using Fi
	OfferType_POWER_UP OfferType = 6
	// denotes a benefits package powered by Thriwe, a benefit pack can include a bundle of egvs, subscriptions etc.
	// todo (utkarsh) : evaluate if should use generic BENEFITS_PACKAGE offer type, would depend on how different/similar are offer level attributes across different vendors.
	OfferType_THRIWE_BENEFITS_PACKAGE OfferType = 7
	// specifically for offers that allow users to get club vistara points
	OfferType_VISTARA_AIR_MILES OfferType = 8
	// cms coupon type for in house vendor
	OfferType_CMS_COUPON OfferType = 9
	// for offers that provide qr code for lounge access to the user
	OfferType_LOUNGE_ACCESS OfferType = 10
	// for offers which are redeemed via external vendor systems like Dpanda, Poshvine, etc
	OfferType_EXTERNAL_VENDOR OfferType = 11
	// specifically for offers that allow users to get club itc green points
	OfferType_CLUB_ITC_GREEN_POINTS OfferType = 12
)

// Enum value maps for OfferType.
var (
	OfferType_name = map[int32]string{
		0:  "UNSPECIFIED_OFFER_TYPE",
		1:  "GIFT_CARD",
		2:  "COUPON",
		3:  "SUBSCRIPTION",
		4:  "CHARITY",
		5:  "PHYSICAL_MERCHANDISE",
		6:  "POWER_UP",
		7:  "THRIWE_BENEFITS_PACKAGE",
		8:  "VISTARA_AIR_MILES",
		9:  "CMS_COUPON",
		10: "LOUNGE_ACCESS",
		11: "EXTERNAL_VENDOR",
		12: "CLUB_ITC_GREEN_POINTS",
	}
	OfferType_value = map[string]int32{
		"UNSPECIFIED_OFFER_TYPE":  0,
		"GIFT_CARD":               1,
		"COUPON":                  2,
		"SUBSCRIPTION":            3,
		"CHARITY":                 4,
		"PHYSICAL_MERCHANDISE":    5,
		"POWER_UP":                6,
		"THRIWE_BENEFITS_PACKAGE": 7,
		"VISTARA_AIR_MILES":       8,
		"CMS_COUPON":              9,
		"LOUNGE_ACCESS":           10,
		"EXTERNAL_VENDOR":         11,
		"CLUB_ITC_GREEN_POINTS":   12,
	}
)

func (x OfferType) Enum() *OfferType {
	p := new(OfferType)
	*p = x
	return p
}

func (x OfferType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OfferType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_casper_offer_catalog_proto_enumTypes[0].Descriptor()
}

func (OfferType) Type() protoreflect.EnumType {
	return &file_api_casper_offer_catalog_proto_enumTypes[0]
}

func (x OfferType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OfferType.Descriptor instead.
func (OfferType) EnumDescriptor() ([]byte, []int) {
	return file_api_casper_offer_catalog_proto_rawDescGZIP(), []int{0}
}

// this is to segregate the offers based on the mode through which they can redeem the offer
type OfferRedemptionMode int32

const (
	// default if not specified
	OfferRedemptionMode_UNSPECIFIED_REDEMPTION_MODE OfferRedemptionMode = 0
	// if the offer is redeemable via fi coins
	OfferRedemptionMode_FI_COINS OfferRedemptionMode = 1
	// if the offer is redeemable via fi DEBIT card
	OfferRedemptionMode_FI_CARD OfferRedemptionMode = 2
	// if the offer is only redeemable via some internal flow
	OfferRedemptionMode_INTERNAL OfferRedemptionMode = 3
	// if the offer is redeemable via fi CREDIT card
	OfferRedemptionMode_FI_CREDIT_CARD OfferRedemptionMode = 4
)

// Enum value maps for OfferRedemptionMode.
var (
	OfferRedemptionMode_name = map[int32]string{
		0: "UNSPECIFIED_REDEMPTION_MODE",
		1: "FI_COINS",
		2: "FI_CARD",
		3: "INTERNAL",
		4: "FI_CREDIT_CARD",
	}
	OfferRedemptionMode_value = map[string]int32{
		"UNSPECIFIED_REDEMPTION_MODE": 0,
		"FI_COINS":                    1,
		"FI_CARD":                     2,
		"INTERNAL":                    3,
		"FI_CREDIT_CARD":              4,
	}
)

func (x OfferRedemptionMode) Enum() *OfferRedemptionMode {
	p := new(OfferRedemptionMode)
	*p = x
	return p
}

func (x OfferRedemptionMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OfferRedemptionMode) Descriptor() protoreflect.EnumDescriptor {
	return file_api_casper_offer_catalog_proto_enumTypes[1].Descriptor()
}

func (OfferRedemptionMode) Type() protoreflect.EnumType {
	return &file_api_casper_offer_catalog_proto_enumTypes[1]
}

func (x OfferRedemptionMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OfferRedemptionMode.Descriptor instead.
func (OfferRedemptionMode) EnumDescriptor() ([]byte, []int) {
	return file_api_casper_offer_catalog_proto_rawDescGZIP(), []int{1}
}

// vendor through which redemption is happening
type OfferVendor int32

const (
	// default if not specified
	OfferVendor_UNSPECIFIED OfferVendor = 0
	OfferVendor_LOYLTY      OfferVendor = 1
	OfferVendor_QWIKCILVER  OfferVendor = 2
	// offline vendors for whom we haven't integrated any apis.
	// We'll share data with them offline (post redemption) for initiating fulfillment.
	OfferVendor_NAAGIN_SAUCE_OFFLINE    OfferVendor = 10
	OfferVendor_MONSOON_HARVEST_OFFLINE OfferVendor = 11
	OfferVendor_BLUE_TOKAI_OFFLINE      OfferVendor = 12
	OfferVendor_DHORA_OFFLINE           OfferVendor = 13
	OfferVendor_SOCK_SOHO_OFFLINE       OfferVendor = 14
	OfferVendor_TRUE_ELEMENTS_OFFLINE   OfferVendor = 15
	OfferVendor_WE_WORK_OFFLINE         OfferVendor = 16
	OfferVendor_TEA_TRUNK_OFFLINE       OfferVendor = 17
	OfferVendor_LIVING_FOOD_OFFLINE     OfferVendor = 18
	OfferVendor_WHOLE_TRUTH_OFFLINE     OfferVendor = 19
	OfferVendor_BONOMI_OFFLINE          OfferVendor = 20
	OfferVendor_WHITE_WILLOW_OFFLINE    OfferVendor = 21
	OfferVendor_MAN_MATTERS_OFFLINE     OfferVendor = 22
	OfferVendor_CHAIKA_OFFLINE          OfferVendor = 23
	OfferVendor_BOLD_CARE_OFFLINE       OfferVendor = 24
	OfferVendor_BILI_HU_OFFLINE         OfferVendor = 25
	OfferVendor_HUMBLE_OFFLINE          OfferVendor = 26
	OfferVendor_BIGSMALL_OFFLINE        OfferVendor = 27
	// generic offer vendor is added to avoid of creating a new vendor enum each time for
	// a new offline vendor as the handling of different offline vendors is exactly same.
	OfferVendor_GENERIC_OFFLINE_VENDOR OfferVendor = 28
	// no-op vendor can be used in cases where we don't have any dependency on any vendor and the offer can be auto-fulfilled
	// upon redemption.
	// #### IMPORTANT NOTE - ENTRIES FOR THIS VENDOR AREN'T CREATED IN ANY VENDOR TABLE. THE ONLY ENTRY CREATED IS STORED IN `redeemed_offers` TABLE ####
	OfferVendor_NO_OP_VENDOR OfferVendor = 29
	OfferVendor_THRIWE       OfferVendor = 30
	OfferVendor_VISTARA      OfferVendor = 31
	OfferVendor_IN_HOUSE     OfferVendor = 32
	OfferVendor_DREAMFOLKS   OfferVendor = 33
	// vendors where users redeem outside of fi catalog
	OfferVendor_DPANDA   OfferVendor = 34
	OfferVendor_POSHVINE OfferVendor = 35
	OfferVendor_ITC      OfferVendor = 36
)

// Enum value maps for OfferVendor.
var (
	OfferVendor_name = map[int32]string{
		0:  "UNSPECIFIED",
		1:  "LOYLTY",
		2:  "QWIKCILVER",
		10: "NAAGIN_SAUCE_OFFLINE",
		11: "MONSOON_HARVEST_OFFLINE",
		12: "BLUE_TOKAI_OFFLINE",
		13: "DHORA_OFFLINE",
		14: "SOCK_SOHO_OFFLINE",
		15: "TRUE_ELEMENTS_OFFLINE",
		16: "WE_WORK_OFFLINE",
		17: "TEA_TRUNK_OFFLINE",
		18: "LIVING_FOOD_OFFLINE",
		19: "WHOLE_TRUTH_OFFLINE",
		20: "BONOMI_OFFLINE",
		21: "WHITE_WILLOW_OFFLINE",
		22: "MAN_MATTERS_OFFLINE",
		23: "CHAIKA_OFFLINE",
		24: "BOLD_CARE_OFFLINE",
		25: "BILI_HU_OFFLINE",
		26: "HUMBLE_OFFLINE",
		27: "BIGSMALL_OFFLINE",
		28: "GENERIC_OFFLINE_VENDOR",
		29: "NO_OP_VENDOR",
		30: "THRIWE",
		31: "VISTARA",
		32: "IN_HOUSE",
		33: "DREAMFOLKS",
		34: "DPANDA",
		35: "POSHVINE",
		36: "ITC",
	}
	OfferVendor_value = map[string]int32{
		"UNSPECIFIED":             0,
		"LOYLTY":                  1,
		"QWIKCILVER":              2,
		"NAAGIN_SAUCE_OFFLINE":    10,
		"MONSOON_HARVEST_OFFLINE": 11,
		"BLUE_TOKAI_OFFLINE":      12,
		"DHORA_OFFLINE":           13,
		"SOCK_SOHO_OFFLINE":       14,
		"TRUE_ELEMENTS_OFFLINE":   15,
		"WE_WORK_OFFLINE":         16,
		"TEA_TRUNK_OFFLINE":       17,
		"LIVING_FOOD_OFFLINE":     18,
		"WHOLE_TRUTH_OFFLINE":     19,
		"BONOMI_OFFLINE":          20,
		"WHITE_WILLOW_OFFLINE":    21,
		"MAN_MATTERS_OFFLINE":     22,
		"CHAIKA_OFFLINE":          23,
		"BOLD_CARE_OFFLINE":       24,
		"BILI_HU_OFFLINE":         25,
		"HUMBLE_OFFLINE":          26,
		"BIGSMALL_OFFLINE":        27,
		"GENERIC_OFFLINE_VENDOR":  28,
		"NO_OP_VENDOR":            29,
		"THRIWE":                  30,
		"VISTARA":                 31,
		"IN_HOUSE":                32,
		"DREAMFOLKS":              33,
		"DPANDA":                  34,
		"POSHVINE":                35,
		"ITC":                     36,
	}
)

func (x OfferVendor) Enum() *OfferVendor {
	p := new(OfferVendor)
	*p = x
	return p
}

func (x OfferVendor) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OfferVendor) Descriptor() protoreflect.EnumDescriptor {
	return file_api_casper_offer_catalog_proto_enumTypes[2].Descriptor()
}

func (OfferVendor) Type() protoreflect.EnumType {
	return &file_api_casper_offer_catalog_proto_enumTypes[2]
}

func (x OfferVendor) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OfferVendor.Descriptor instead.
func (OfferVendor) EnumDescriptor() ([]byte, []int) {
	return file_api_casper_offer_catalog_proto_rawDescGZIP(), []int{2}
}

type ImageType int32

const (
	// default if not specified
	ImageType_UNSPECIFIED_IMAGE_TYPE ImageType = 0
	// used as the background of the offer
	ImageType_BACKGROUND_IMAGE ImageType = 1
	// brand image used where ever we want to show brand as image
	ImageType_BRAND_IMAGE ImageType = 2
	// promo image is used for promoting the offer on some screens like HOME, CC landing etc.
	ImageType_PROMO_IMAGE ImageType = 3
)

// Enum value maps for ImageType.
var (
	ImageType_name = map[int32]string{
		0: "UNSPECIFIED_IMAGE_TYPE",
		1: "BACKGROUND_IMAGE",
		2: "BRAND_IMAGE",
		3: "PROMO_IMAGE",
	}
	ImageType_value = map[string]int32{
		"UNSPECIFIED_IMAGE_TYPE": 0,
		"BACKGROUND_IMAGE":       1,
		"BRAND_IMAGE":            2,
		"PROMO_IMAGE":            3,
	}
)

func (x ImageType) Enum() *ImageType {
	p := new(ImageType)
	*p = x
	return p
}

func (x ImageType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ImageType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_casper_offer_catalog_proto_enumTypes[3].Descriptor()
}

func (ImageType) Type() protoreflect.EnumType {
	return &file_api_casper_offer_catalog_proto_enumTypes[3]
}

func (x ImageType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ImageType.Descriptor instead.
func (ImageType) EnumDescriptor() ([]byte, []int) {
	return file_api_casper_offer_catalog_proto_rawDescGZIP(), []int{3}
}

// CardOfferType to track card offer source.
type CardOfferType int32

const (
	CardOfferType_CARD_OFFER_TYPE_UNSPECIFIED CardOfferType = 0
	CardOfferType_CARD_OFFER_TYPE_EPIFI       CardOfferType = 1
	CardOfferType_CARD_OFFER_TYPE_PARTNERSHIP CardOfferType = 2
	CardOfferType_CARD_OFFER_TYPE_VISA        CardOfferType = 3
)

// Enum value maps for CardOfferType.
var (
	CardOfferType_name = map[int32]string{
		0: "CARD_OFFER_TYPE_UNSPECIFIED",
		1: "CARD_OFFER_TYPE_EPIFI",
		2: "CARD_OFFER_TYPE_PARTNERSHIP",
		3: "CARD_OFFER_TYPE_VISA",
	}
	CardOfferType_value = map[string]int32{
		"CARD_OFFER_TYPE_UNSPECIFIED": 0,
		"CARD_OFFER_TYPE_EPIFI":       1,
		"CARD_OFFER_TYPE_PARTNERSHIP": 2,
		"CARD_OFFER_TYPE_VISA":        3,
	}
)

func (x CardOfferType) Enum() *CardOfferType {
	p := new(CardOfferType)
	*p = x
	return p
}

func (x CardOfferType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardOfferType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_casper_offer_catalog_proto_enumTypes[4].Descriptor()
}

func (CardOfferType) Type() protoreflect.EnumType {
	return &file_api_casper_offer_catalog_proto_enumTypes[4]
}

func (x CardOfferType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardOfferType.Descriptor instead.
func (CardOfferType) EnumDescriptor() ([]byte, []int) {
	return file_api_casper_offer_catalog_proto_rawDescGZIP(), []int{4}
}

type OfferAdditionalDetails_NotificationType int32

const (
	OfferAdditionalDetails_NOTIFICATION_TYPE_UNSPECIFIED     OfferAdditionalDetails_NotificationType = 0
	OfferAdditionalDetails_NOTIFICATION_TYPE_SYSTEM_TRAY     OfferAdditionalDetails_NotificationType = 1
	OfferAdditionalDetails_NOTIFICATION_TYPE_IN_APP_CRITICAL OfferAdditionalDetails_NotificationType = 2
	OfferAdditionalDetails_NOTIFICATION_TYPE_IN_APP          OfferAdditionalDetails_NotificationType = 3
)

// Enum value maps for OfferAdditionalDetails_NotificationType.
var (
	OfferAdditionalDetails_NotificationType_name = map[int32]string{
		0: "NOTIFICATION_TYPE_UNSPECIFIED",
		1: "NOTIFICATION_TYPE_SYSTEM_TRAY",
		2: "NOTIFICATION_TYPE_IN_APP_CRITICAL",
		3: "NOTIFICATION_TYPE_IN_APP",
	}
	OfferAdditionalDetails_NotificationType_value = map[string]int32{
		"NOTIFICATION_TYPE_UNSPECIFIED":     0,
		"NOTIFICATION_TYPE_SYSTEM_TRAY":     1,
		"NOTIFICATION_TYPE_IN_APP_CRITICAL": 2,
		"NOTIFICATION_TYPE_IN_APP":          3,
	}
)

func (x OfferAdditionalDetails_NotificationType) Enum() *OfferAdditionalDetails_NotificationType {
	p := new(OfferAdditionalDetails_NotificationType)
	*p = x
	return p
}

func (x OfferAdditionalDetails_NotificationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OfferAdditionalDetails_NotificationType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_casper_offer_catalog_proto_enumTypes[5].Descriptor()
}

func (OfferAdditionalDetails_NotificationType) Type() protoreflect.EnumType {
	return &file_api_casper_offer_catalog_proto_enumTypes[5]
}

func (x OfferAdditionalDetails_NotificationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OfferAdditionalDetails_NotificationType.Descriptor instead.
func (OfferAdditionalDetails_NotificationType) EnumDescriptor() ([]byte, []int) {
	return file_api_casper_offer_catalog_proto_rawDescGZIP(), []int{2, 0}
}

// hold all the attributes of a offer image
type OfferImage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// image type BACKGROUND_IMAGE/BRAND_IMAGE etc
	ImageType ImageType `protobuf:"varint,1,opt,name=image_type,json=imageType,proto3,enum=casper.ImageType" json:"image_type,omitempty"`
	// url of the image which we get after we upload the image on S3
	Url string `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *OfferImage) Reset() {
	*x = OfferImage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_offer_catalog_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OfferImage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OfferImage) ProtoMessage() {}

func (x *OfferImage) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_offer_catalog_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OfferImage.ProtoReflect.Descriptor instead.
func (*OfferImage) Descriptor() ([]byte, []int) {
	return file_api_casper_offer_catalog_proto_rawDescGZIP(), []int{0}
}

func (x *OfferImage) GetImageType() ImageType {
	if x != nil {
		return x.ImageType
	}
	return ImageType_UNSPECIFIED_IMAGE_TYPE
}

func (x *OfferImage) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

// hold all the attributes of offer tnc
type OfferTnc struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// all terms and conditions as list
	TncList []string `protobuf:"bytes,1,rep,name=tnc_list,json=tncList,proto3" json:"tnc_list,omitempty"`
}

func (x *OfferTnc) Reset() {
	*x = OfferTnc{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_offer_catalog_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OfferTnc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OfferTnc) ProtoMessage() {}

func (x *OfferTnc) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_offer_catalog_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OfferTnc.ProtoReflect.Descriptor instead.
func (*OfferTnc) Descriptor() ([]byte, []int) {
	return file_api_casper_offer_catalog_proto_rawDescGZIP(), []int{1}
}

func (x *OfferTnc) GetTncList() []string {
	if x != nil {
		return x.TncList
	}
	return nil
}

type OfferAdditionalDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// offer details
	OfferDetails []string `protobuf:"bytes,1,rep,name=offer_details,json=offerDetails,proto3" json:"offer_details,omitempty"`
	// steps to redeem
	HowToRedeem []string `protobuf:"bytes,2,rep,name=how_to_redeem,json=howToRedeem,proto3" json:"how_to_redeem,omitempty"`
	// background color of the tile
	BgColor string `protobuf:"bytes,3,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	// offer name on redemption screen
	AfterRedemptionOfferName string `protobuf:"bytes,4,opt,name=after_redemption_offer_name,json=afterRedemptionOfferName,proto3" json:"after_redemption_offer_name,omitempty"`
	// offer title shown on offer details page.
	OfferTitle string `protobuf:"bytes,5,opt,name=offer_title,json=offerTitle,proto3" json:"offer_title,omitempty"`
	// display_rank is used to enforce display ordering of offers on the catalog.
	// lower the rank, earlier that offer should be displayed on the catalog.
	// will be used as a default rank if screen rank is not provided in screen_display_rank_map
	OfferDisplayRank int32 `protobuf:"varint,6,opt,name=offer_display_rank,json=offerDisplayRank,proto3" json:"offer_display_rank,omitempty"`
	// steps post redemption
	NextSteps []string `protobuf:"bytes,7,rep,name=next_steps,json=nextSteps,proto3" json:"next_steps,omitempty"`
	// tag to be shown for salary account purpose.
	// Deprecated: Use is_salary_account_exclusive instead
	//
	// Deprecated: Marked as deprecated in api/casper/offer_catalog.proto.
	SalaryAccountDisplayTag *common.Text `protobuf:"bytes,8,opt,name=salary_account_display_tag,json=salaryAccountDisplayTag,proto3" json:"salary_account_display_tag,omitempty"`
	// offer title shown on home page offer tile
	HomeTitle string `protobuf:"bytes,9,opt,name=home_title,json=homeTitle,proto3" json:"home_title,omitempty"`
	// flag to mark the offer as being salary exclusive
	//
	// Deprecated: Marked as deprecated in api/casper/offer_catalog.proto.
	IsSalaryAccountExclusive bool `protobuf:"varint,10,opt,name=is_salary_account_exclusive,json=isSalaryAccountExclusive,proto3" json:"is_salary_account_exclusive,omitempty"`
	// brand name of the product being offered
	BrandName string `protobuf:"bytes,11,opt,name=brand_name,json=brandName,proto3" json:"brand_name,omitempty"`
	// primary image to be used for displaying on home, and other tiles
	// this type will decide the positioning of the image on the tile
	TileImageContentType common.ImageContentType `protobuf:"varint,12,opt,name=tile_image_content_type,json=tileImageContentType,proto3,enum=api.typesv2.common.ImageContentType" json:"tile_image_content_type,omitempty"`
	// constraint expression to be used to show/hide offer based on certain criteria
	// eg - IsUserSalaryProgramActive() || IsUserCreditCardActive()
	DisplayConstraintExpression string `protobuf:"bytes,13,opt,name=display_constraint_expression,json=displayConstraintExpression,proto3" json:"display_constraint_expression,omitempty"`
	// map of frontend.deeplink.Screen name vs offer display rank
	// will be used to enforce ordering of offers on screen basis
	ScreenDisplayRankMap map[string]int32 `protobuf:"bytes,14,rep,name=screen_display_rank_map,json=screenDisplayRankMap,proto3" json:"screen_display_rank_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	// flag to mark the offer as promotional offer,
	// used for additionally showing some special offers on screens like HOME.
	// for fi-coins catalog offers, if this field is set they will be given higher priority over other sort options
	IsPromoOffer bool `protobuf:"varint,15,opt,name=is_promo_offer,json=isPromoOffer,proto3" json:"is_promo_offer,omitempty"`
	// offer title used for promoting the offer.
	PromoTitle string `protobuf:"bytes,16,opt,name=promo_title,json=promoTitle,proto3" json:"promo_title,omitempty"`
	// promo_offer_display_rank is used to enforce display ordering of promoted offers.
	// for promoted offers promo_offer_display_rank will be prioritised over offer_display_rank.
	// lower the rank, earlier that offer should be displayed.
	PromoOfferDisplayRank int32 `protobuf:"varint,17,opt,name=promo_offer_display_rank,json=promoOfferDisplayRank,proto3" json:"promo_offer_display_rank,omitempty"`
	// offerDescription is used to add description about the offer
	OfferDescription *OfferAdditionalDetails_OfferDescription `protobuf:"bytes,18,opt,name=offer_description,json=offerDescription,proto3" json:"offer_description,omitempty"`
	// to be used as alternate cta text for coming soon offers
	// if populated the cta text for the offer will show this text instead of the default "16,000 Fi-Coins" text
	// can serve as a templatized string if required
	// eg. 1. Convert {{REDEMPTION_PRICE}} Fi-Coins
	// in the above case {{REDEMPTION_PRICE}} will be replaced by the actual price
	// eg. 2. Convert Fi-Coins
	AlternateCtaText string `protobuf:"bytes,19,opt,name=alternate_cta_text,json=alternateCtaText,proto3" json:"alternate_cta_text,omitempty"`
	// denotes the tags to be displayed on the redeemed offer tile e.g "CREDIT CARD REWARD"
	DisplayTags []*OfferAdditionalDetails_DisplayTag `protobuf:"bytes,20,rep,name=display_tags,json=displayTags,proto3" json:"display_tags,omitempty"`
	// boolean flag to indicate whether the offer is experimental or not
	// offer experiments will run in the following manner -
	// whenever a new experimental offer is created, a quest variable of the form casper/offer/offer_id is created
	// once all the offers of an experiment are created, a quest experiment is started with multiple variants
	// each experiment variant will have a set of boolean variables formed while creating the experimental offers
	// for the variant user is a part of, only those offers will be visible which have the boolean variable set as true
	IsExperimental bool `protobuf:"varint,21,opt,name=is_experimental,json=isExperimental,proto3" json:"is_experimental,omitempty"`
	// denotes what type of card offer is live if offer redemption mode is FI_CARD.
	CardOfferType CardOfferType `protobuf:"varint,22,opt,name=card_offer_type,json=cardOfferType,proto3,enum=casper.CardOfferType" json:"card_offer_type,omitempty"`
	// fi-coins to cash conversion ratio for the offer (optional)
	ConversionRatio *FiCoinsToCashConversionRatio `protobuf:"bytes,23,opt,name=conversion_ratio,json=conversionRatio,proto3" json:"conversion_ratio,omitempty"`
	// optional, if set then the offer will be visible for those actors for whom this expression is true
	SegmentExpression         string                                             `protobuf:"bytes,24,opt,name=segment_expression,json=segmentExpression,proto3" json:"segment_expression,omitempty"`
	ExpiryNotificationConfigs []*OfferAdditionalDetails_ExpiryNotificationConfig `protobuf:"bytes,25,rep,name=expiry_notification_configs,json=expiryNotificationConfigs,proto3" json:"expiry_notification_configs,omitempty"`
	// stores watson config related to the issue category corresponding to which
	// a user activity event or an error activity event needs to be created
	// error activity event involves ticket creation when something goes wrong
	// in a redeemed offer(redemption) against this offer and resolution when
	// the issue is resolved
	WatsonMeta *WatsonMeta `protobuf:"bytes,26,opt,name=watson_meta,json=watsonMeta,proto3" json:"watson_meta,omitempty"`
	// short title of the offer (shorter version for name which is shown on the offer card)
	// have kept this as short_name to indicate this is the shorter version of the name field
	// currently to be used for SDUI offer card title
	ShortName string `protobuf:"bytes,27,opt,name=short_name,json=shortName,proto3" json:"short_name,omitempty"`
	// flag to know whether a reward type FI_COINS offer will be redeemed in the form of FI_POINTS
	// if unspecified, it will be considered as FI_COINS as older offers will not have this field
	IsFiPoints common.BooleanEnum `protobuf:"varint,28,opt,name=is_fi_points,json=isFiPoints,proto3,enum=api.typesv2.common.BooleanEnum" json:"is_fi_points,omitempty"`
}

func (x *OfferAdditionalDetails) Reset() {
	*x = OfferAdditionalDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_offer_catalog_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OfferAdditionalDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OfferAdditionalDetails) ProtoMessage() {}

func (x *OfferAdditionalDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_offer_catalog_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OfferAdditionalDetails.ProtoReflect.Descriptor instead.
func (*OfferAdditionalDetails) Descriptor() ([]byte, []int) {
	return file_api_casper_offer_catalog_proto_rawDescGZIP(), []int{2}
}

func (x *OfferAdditionalDetails) GetOfferDetails() []string {
	if x != nil {
		return x.OfferDetails
	}
	return nil
}

func (x *OfferAdditionalDetails) GetHowToRedeem() []string {
	if x != nil {
		return x.HowToRedeem
	}
	return nil
}

func (x *OfferAdditionalDetails) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

func (x *OfferAdditionalDetails) GetAfterRedemptionOfferName() string {
	if x != nil {
		return x.AfterRedemptionOfferName
	}
	return ""
}

func (x *OfferAdditionalDetails) GetOfferTitle() string {
	if x != nil {
		return x.OfferTitle
	}
	return ""
}

func (x *OfferAdditionalDetails) GetOfferDisplayRank() int32 {
	if x != nil {
		return x.OfferDisplayRank
	}
	return 0
}

func (x *OfferAdditionalDetails) GetNextSteps() []string {
	if x != nil {
		return x.NextSteps
	}
	return nil
}

// Deprecated: Marked as deprecated in api/casper/offer_catalog.proto.
func (x *OfferAdditionalDetails) GetSalaryAccountDisplayTag() *common.Text {
	if x != nil {
		return x.SalaryAccountDisplayTag
	}
	return nil
}

func (x *OfferAdditionalDetails) GetHomeTitle() string {
	if x != nil {
		return x.HomeTitle
	}
	return ""
}

// Deprecated: Marked as deprecated in api/casper/offer_catalog.proto.
func (x *OfferAdditionalDetails) GetIsSalaryAccountExclusive() bool {
	if x != nil {
		return x.IsSalaryAccountExclusive
	}
	return false
}

func (x *OfferAdditionalDetails) GetBrandName() string {
	if x != nil {
		return x.BrandName
	}
	return ""
}

func (x *OfferAdditionalDetails) GetTileImageContentType() common.ImageContentType {
	if x != nil {
		return x.TileImageContentType
	}
	return common.ImageContentType(0)
}

func (x *OfferAdditionalDetails) GetDisplayConstraintExpression() string {
	if x != nil {
		return x.DisplayConstraintExpression
	}
	return ""
}

func (x *OfferAdditionalDetails) GetScreenDisplayRankMap() map[string]int32 {
	if x != nil {
		return x.ScreenDisplayRankMap
	}
	return nil
}

func (x *OfferAdditionalDetails) GetIsPromoOffer() bool {
	if x != nil {
		return x.IsPromoOffer
	}
	return false
}

func (x *OfferAdditionalDetails) GetPromoTitle() string {
	if x != nil {
		return x.PromoTitle
	}
	return ""
}

func (x *OfferAdditionalDetails) GetPromoOfferDisplayRank() int32 {
	if x != nil {
		return x.PromoOfferDisplayRank
	}
	return 0
}

func (x *OfferAdditionalDetails) GetOfferDescription() *OfferAdditionalDetails_OfferDescription {
	if x != nil {
		return x.OfferDescription
	}
	return nil
}

func (x *OfferAdditionalDetails) GetAlternateCtaText() string {
	if x != nil {
		return x.AlternateCtaText
	}
	return ""
}

func (x *OfferAdditionalDetails) GetDisplayTags() []*OfferAdditionalDetails_DisplayTag {
	if x != nil {
		return x.DisplayTags
	}
	return nil
}

func (x *OfferAdditionalDetails) GetIsExperimental() bool {
	if x != nil {
		return x.IsExperimental
	}
	return false
}

func (x *OfferAdditionalDetails) GetCardOfferType() CardOfferType {
	if x != nil {
		return x.CardOfferType
	}
	return CardOfferType_CARD_OFFER_TYPE_UNSPECIFIED
}

func (x *OfferAdditionalDetails) GetConversionRatio() *FiCoinsToCashConversionRatio {
	if x != nil {
		return x.ConversionRatio
	}
	return nil
}

func (x *OfferAdditionalDetails) GetSegmentExpression() string {
	if x != nil {
		return x.SegmentExpression
	}
	return ""
}

func (x *OfferAdditionalDetails) GetExpiryNotificationConfigs() []*OfferAdditionalDetails_ExpiryNotificationConfig {
	if x != nil {
		return x.ExpiryNotificationConfigs
	}
	return nil
}

func (x *OfferAdditionalDetails) GetWatsonMeta() *WatsonMeta {
	if x != nil {
		return x.WatsonMeta
	}
	return nil
}

func (x *OfferAdditionalDetails) GetShortName() string {
	if x != nil {
		return x.ShortName
	}
	return ""
}

func (x *OfferAdditionalDetails) GetIsFiPoints() common.BooleanEnum {
	if x != nil {
		return x.IsFiPoints
	}
	return common.BooleanEnum(0)
}

// VendorOfferMetadata stores vendor specific metadata related to offer
type VendorOfferMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to VendorOfferMetadata:
	//
	//	*VendorOfferMetadata_LoyltyVendorOfferMetadata
	//	*VendorOfferMetadata_OfflineVendorOfferMetadata
	//	*VendorOfferMetadata_QwikcilverVendorOfferMetadata
	//	*VendorOfferMetadata_ThriweVendorOfferMetadata
	//	*VendorOfferMetadata_InHouseVendorOfferMetadata
	VendorOfferMetadata isVendorOfferMetadata_VendorOfferMetadata `protobuf_oneof:"vendor_offer_metadata"`
}

func (x *VendorOfferMetadata) Reset() {
	*x = VendorOfferMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_offer_catalog_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VendorOfferMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VendorOfferMetadata) ProtoMessage() {}

func (x *VendorOfferMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_offer_catalog_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VendorOfferMetadata.ProtoReflect.Descriptor instead.
func (*VendorOfferMetadata) Descriptor() ([]byte, []int) {
	return file_api_casper_offer_catalog_proto_rawDescGZIP(), []int{3}
}

func (m *VendorOfferMetadata) GetVendorOfferMetadata() isVendorOfferMetadata_VendorOfferMetadata {
	if m != nil {
		return m.VendorOfferMetadata
	}
	return nil
}

func (x *VendorOfferMetadata) GetLoyltyVendorOfferMetadata() *LoyltyVendorOfferMetadata {
	if x, ok := x.GetVendorOfferMetadata().(*VendorOfferMetadata_LoyltyVendorOfferMetadata); ok {
		return x.LoyltyVendorOfferMetadata
	}
	return nil
}

func (x *VendorOfferMetadata) GetOfflineVendorOfferMetadata() *OfflineVendorOfferMetadata {
	if x, ok := x.GetVendorOfferMetadata().(*VendorOfferMetadata_OfflineVendorOfferMetadata); ok {
		return x.OfflineVendorOfferMetadata
	}
	return nil
}

func (x *VendorOfferMetadata) GetQwikcilverVendorOfferMetadata() *QwikcilverVendorOfferMetadata {
	if x, ok := x.GetVendorOfferMetadata().(*VendorOfferMetadata_QwikcilverVendorOfferMetadata); ok {
		return x.QwikcilverVendorOfferMetadata
	}
	return nil
}

func (x *VendorOfferMetadata) GetThriweVendorOfferMetadata() *ThriweVendorOfferMetadata {
	if x, ok := x.GetVendorOfferMetadata().(*VendorOfferMetadata_ThriweVendorOfferMetadata); ok {
		return x.ThriweVendorOfferMetadata
	}
	return nil
}

func (x *VendorOfferMetadata) GetInHouseVendorOfferMetadata() *InHouseVendorOfferMetadata {
	if x, ok := x.GetVendorOfferMetadata().(*VendorOfferMetadata_InHouseVendorOfferMetadata); ok {
		return x.InHouseVendorOfferMetadata
	}
	return nil
}

type isVendorOfferMetadata_VendorOfferMetadata interface {
	isVendorOfferMetadata_VendorOfferMetadata()
}

type VendorOfferMetadata_LoyltyVendorOfferMetadata struct {
	LoyltyVendorOfferMetadata *LoyltyVendorOfferMetadata `protobuf:"bytes,1,opt,name=loylty_vendor_offer_metadata,json=loyltyVendorOfferMetadata,proto3,oneof"`
}

type VendorOfferMetadata_OfflineVendorOfferMetadata struct {
	OfflineVendorOfferMetadata *OfflineVendorOfferMetadata `protobuf:"bytes,2,opt,name=offline_vendor_offer_metadata,json=offlineVendorOfferMetadata,proto3,oneof"`
}

type VendorOfferMetadata_QwikcilverVendorOfferMetadata struct {
	QwikcilverVendorOfferMetadata *QwikcilverVendorOfferMetadata `protobuf:"bytes,3,opt,name=qwikcilver_vendor_offer_metadata,json=qwikcilverVendorOfferMetadata,proto3,oneof"`
}

type VendorOfferMetadata_ThriweVendorOfferMetadata struct {
	ThriweVendorOfferMetadata *ThriweVendorOfferMetadata `protobuf:"bytes,4,opt,name=thriwe_vendor_offer_metadata,json=thriweVendorOfferMetadata,proto3,oneof"`
}

type VendorOfferMetadata_InHouseVendorOfferMetadata struct {
	InHouseVendorOfferMetadata *InHouseVendorOfferMetadata `protobuf:"bytes,5,opt,name=in_house_vendor_offer_metadata,json=inHouseVendorOfferMetadata,proto3,oneof"`
}

func (*VendorOfferMetadata_LoyltyVendorOfferMetadata) isVendorOfferMetadata_VendorOfferMetadata() {}

func (*VendorOfferMetadata_OfflineVendorOfferMetadata) isVendorOfferMetadata_VendorOfferMetadata() {}

func (*VendorOfferMetadata_QwikcilverVendorOfferMetadata) isVendorOfferMetadata_VendorOfferMetadata() {
}

func (*VendorOfferMetadata_ThriweVendorOfferMetadata) isVendorOfferMetadata_VendorOfferMetadata() {}

func (*VendorOfferMetadata_InHouseVendorOfferMetadata) isVendorOfferMetadata_VendorOfferMetadata() {}

type LoyltyVendorOfferMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// sku_id is something shared by loylty with us to establish a persistent mapping of our offers to their products.
	// But Loylty requires product id at the time of redemption.Mapping between sku_id and product_id is one to one
	// but the exact mapping can change with time. So we use sku_id to fetch product id at realtime.
	SkuId string `protobuf:"bytes,1,opt,name=sku_id,json=skuId,proto3" json:"sku_id,omitempty"`
	// used for redeeming loylty's egv offers.
	ProductId string `protobuf:"bytes,2,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	// Types that are assignable to OfferTypeSpecificMetadata:
	//
	//	*LoyltyVendorOfferMetadata_CharityOfferMetadata_
	OfferTypeSpecificMetadata isLoyltyVendorOfferMetadata_OfferTypeSpecificMetadata `protobuf_oneof:"offer_type_specific_metadata"`
}

func (x *LoyltyVendorOfferMetadata) Reset() {
	*x = LoyltyVendorOfferMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_offer_catalog_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoyltyVendorOfferMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoyltyVendorOfferMetadata) ProtoMessage() {}

func (x *LoyltyVendorOfferMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_offer_catalog_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoyltyVendorOfferMetadata.ProtoReflect.Descriptor instead.
func (*LoyltyVendorOfferMetadata) Descriptor() ([]byte, []int) {
	return file_api_casper_offer_catalog_proto_rawDescGZIP(), []int{4}
}

func (x *LoyltyVendorOfferMetadata) GetSkuId() string {
	if x != nil {
		return x.SkuId
	}
	return ""
}

func (x *LoyltyVendorOfferMetadata) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

func (m *LoyltyVendorOfferMetadata) GetOfferTypeSpecificMetadata() isLoyltyVendorOfferMetadata_OfferTypeSpecificMetadata {
	if m != nil {
		return m.OfferTypeSpecificMetadata
	}
	return nil
}

func (x *LoyltyVendorOfferMetadata) GetCharityOfferMetadata() *LoyltyVendorOfferMetadata_CharityOfferMetadata {
	if x, ok := x.GetOfferTypeSpecificMetadata().(*LoyltyVendorOfferMetadata_CharityOfferMetadata_); ok {
		return x.CharityOfferMetadata
	}
	return nil
}

type isLoyltyVendorOfferMetadata_OfferTypeSpecificMetadata interface {
	isLoyltyVendorOfferMetadata_OfferTypeSpecificMetadata()
}

type LoyltyVendorOfferMetadata_CharityOfferMetadata_ struct {
	CharityOfferMetadata *LoyltyVendorOfferMetadata_CharityOfferMetadata `protobuf:"bytes,6,opt,name=charity_offer_metadata,json=charityOfferMetadata,proto3,oneof"`
}

func (*LoyltyVendorOfferMetadata_CharityOfferMetadata_) isLoyltyVendorOfferMetadata_OfferTypeSpecificMetadata() {
}

type QwikcilverVendorOfferMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// sku_id is a unique identifier of a Qwikcilver product. It is used for maintaining a persistent mapping of our offer to a qwikcilver product.
	// This sku_id is needed at the time of redemption for redeeming the given product from the vendor.
	SkuId string `protobuf:"bytes,1,opt,name=sku_id,json=skuId,proto3" json:"sku_id,omitempty"`
}

func (x *QwikcilverVendorOfferMetadata) Reset() {
	*x = QwikcilverVendorOfferMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_offer_catalog_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QwikcilverVendorOfferMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QwikcilverVendorOfferMetadata) ProtoMessage() {}

func (x *QwikcilverVendorOfferMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_offer_catalog_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QwikcilverVendorOfferMetadata.ProtoReflect.Descriptor instead.
func (*QwikcilverVendorOfferMetadata) Descriptor() ([]byte, []int) {
	return file_api_casper_offer_catalog_proto_rawDescGZIP(), []int{5}
}

func (x *QwikcilverVendorOfferMetadata) GetSkuId() string {
	if x != nil {
		return x.SkuId
	}
	return ""
}

type OfflineVendorOfferMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// used for identifying the offline vendor product.
	ProductId string `protobuf:"bytes,1,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
}

func (x *OfflineVendorOfferMetadata) Reset() {
	*x = OfflineVendorOfferMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_offer_catalog_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OfflineVendorOfferMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OfflineVendorOfferMetadata) ProtoMessage() {}

func (x *OfflineVendorOfferMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_offer_catalog_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OfflineVendorOfferMetadata.ProtoReflect.Descriptor instead.
func (*OfflineVendorOfferMetadata) Descriptor() ([]byte, []int) {
	return file_api_casper_offer_catalog_proto_rawDescGZIP(), []int{6}
}

func (x *OfflineVendorOfferMetadata) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

type ThriweVendorOfferMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unique identifier of a thriwe benefits package, this is needed at the time of redemption for
	// redeeming a given benefits package from thriwe vendor.
	BenefitsPackageId string `protobuf:"bytes,1,opt,name=benefits_package_id,json=benefitsPackageId,proto3" json:"benefits_package_id,omitempty"`
	// name of the thriwe benefits package, will be shown on the thriwe benefits page.
	BenefitsPackageName string `protobuf:"bytes,2,opt,name=benefits_package_name,json=benefitsPackageName,proto3" json:"benefits_package_name,omitempty"`
	// Expiry date of the Thriwe benefits package
	ExpiryDate *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=expiry_date,json=expiryDate,proto3" json:"expiry_date,omitempty"`
}

func (x *ThriweVendorOfferMetadata) Reset() {
	*x = ThriweVendorOfferMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_offer_catalog_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThriweVendorOfferMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThriweVendorOfferMetadata) ProtoMessage() {}

func (x *ThriweVendorOfferMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_offer_catalog_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThriweVendorOfferMetadata.ProtoReflect.Descriptor instead.
func (*ThriweVendorOfferMetadata) Descriptor() ([]byte, []int) {
	return file_api_casper_offer_catalog_proto_rawDescGZIP(), []int{7}
}

func (x *ThriweVendorOfferMetadata) GetBenefitsPackageId() string {
	if x != nil {
		return x.BenefitsPackageId
	}
	return ""
}

func (x *ThriweVendorOfferMetadata) GetBenefitsPackageName() string {
	if x != nil {
		return x.BenefitsPackageName
	}
	return ""
}

func (x *ThriweVendorOfferMetadata) GetExpiryDate() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiryDate
	}
	return nil
}

type Offer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// identifier of the offer
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// name of the offer shown on the main offer page
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// description of the offer shown on the main offer page
	Desc string `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	// price of the offer in decimals
	// not attaching any units: like currency  to it as it will vary based on OfferRedemptionMode
	// for: FI_COINS: it will represent fi coins needed to redeem the offer
	// for: FI_CARD: it will represent INR needed to redeem the offer
	Price float32 `protobuf:"fixed32,4,opt,name=price,proto3" json:"price,omitempty"`
	// type of offer namely gift card, charity etc
	OfferType OfferType `protobuf:"varint,5,opt,name=offer_type,json=offerType,proto3,enum=casper.OfferType" json:"offer_type,omitempty"`
	// mode though which offer is redeemable FI_COINS/FI_CARD etc
	RedemptionMode OfferRedemptionMode `protobuf:"varint,7,opt,name=redemption_mode,json=redemptionMode,proto3,enum=casper.OfferRedemptionMode" json:"redemption_mode,omitempty"`
	// name of the vendor though which offer is redeemable
	VendorName OfferVendor `protobuf:"varint,8,opt,name=vendor_name,json=vendorName,proto3,enum=casper.OfferVendor" json:"vendor_name,omitempty"`
	// stores metadata related to vendor offer
	// used one of as the metadata would vary depending on the vendor
	VendorOfferMetadata *VendorOfferMetadata `protobuf:"bytes,10,opt,name=vendor_offer_metadata,json=vendorOfferMetadata,proto3" json:"vendor_offer_metadata,omitempty"`
	// external_id is equivalent to offer id, but this external_id can be displayed
	// on the app while Id shouldn't be due to security reasons.
	ExternalId string `protobuf:"bytes,11,opt,name=external_id,json=externalId,proto3" json:"external_id,omitempty"`
	// stores metadata for offer.
	// for eg for EGV offers it stores the gift card value.
	OfferMetadata *OfferMetadata `protobuf:"bytes,14,opt,name=offer_metadata,json=offerMetadata,proto3" json:"offer_metadata,omitempty"`
	// list of imaged attached to this offer
	Images []*OfferImage `protobuf:"bytes,15,rep,name=images,proto3" json:"images,omitempty"`
	// offer tnc
	Tnc *OfferTnc `protobuf:"bytes,16,opt,name=tnc,proto3" json:"tnc,omitempty"`
	// todo (utkarsh) : move OfferAdditionalDetails inside OfferMetadata
	// additional_details of the offer
	AdditionalDetails *OfferAdditionalDetails `protobuf:"bytes,17,opt,name=additional_details,json=additionalDetails,proto3" json:"additional_details,omitempty"`
	// soft delete flag
	IsDeleted bool `protobuf:"varint,18,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted,omitempty"`
	// created at
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,19,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// updated_at
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,20,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// discount details (can be nil)
	DiscountDetails *DiscountDetails `protobuf:"bytes,21,opt,name=discount_details,json=discountDetails,proto3" json:"discount_details,omitempty"`
	// information related to tags applied to this offer (manually or automatically)
	TagsInfo *TagsInfo `protobuf:"bytes,22,opt,name=tags_info,json=tagsInfo,proto3" json:"tags_info,omitempty"`
}

func (x *Offer) Reset() {
	*x = Offer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_offer_catalog_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Offer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Offer) ProtoMessage() {}

func (x *Offer) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_offer_catalog_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Offer.ProtoReflect.Descriptor instead.
func (*Offer) Descriptor() ([]byte, []int) {
	return file_api_casper_offer_catalog_proto_rawDescGZIP(), []int{8}
}

func (x *Offer) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Offer) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Offer) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *Offer) GetPrice() float32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *Offer) GetOfferType() OfferType {
	if x != nil {
		return x.OfferType
	}
	return OfferType_UNSPECIFIED_OFFER_TYPE
}

func (x *Offer) GetRedemptionMode() OfferRedemptionMode {
	if x != nil {
		return x.RedemptionMode
	}
	return OfferRedemptionMode_UNSPECIFIED_REDEMPTION_MODE
}

func (x *Offer) GetVendorName() OfferVendor {
	if x != nil {
		return x.VendorName
	}
	return OfferVendor_UNSPECIFIED
}

func (x *Offer) GetVendorOfferMetadata() *VendorOfferMetadata {
	if x != nil {
		return x.VendorOfferMetadata
	}
	return nil
}

func (x *Offer) GetExternalId() string {
	if x != nil {
		return x.ExternalId
	}
	return ""
}

func (x *Offer) GetOfferMetadata() *OfferMetadata {
	if x != nil {
		return x.OfferMetadata
	}
	return nil
}

func (x *Offer) GetImages() []*OfferImage {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *Offer) GetTnc() *OfferTnc {
	if x != nil {
		return x.Tnc
	}
	return nil
}

func (x *Offer) GetAdditionalDetails() *OfferAdditionalDetails {
	if x != nil {
		return x.AdditionalDetails
	}
	return nil
}

func (x *Offer) GetIsDeleted() bool {
	if x != nil {
		return x.IsDeleted
	}
	return false
}

func (x *Offer) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Offer) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Offer) GetDiscountDetails() *DiscountDetails {
	if x != nil {
		return x.DiscountDetails
	}
	return nil
}

func (x *Offer) GetTagsInfo() *TagsInfo {
	if x != nil {
		return x.TagsInfo
	}
	return nil
}

// stores metadata for offer.
// for eg for EGV offers it stores the gift card value.
type OfferMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to OfferTypeSpecificMetadata:
	//
	//	*OfferMetadata_GiftCardMetadata
	//	*OfferMetadata_CharityMetadata
	//	*OfferMetadata_CouponMetadata
	//	*OfferMetadata_PhysicalMerchMetadata
	//	*OfferMetadata_PowerUpOfferMetadata
	//	*OfferMetadata_ThriweBenefitsPackageOfferMetadata
	//	*OfferMetadata_VistaraAirMilesOfferMetadata
	//	*OfferMetadata_ExternalVendorOfferMetadata
	//	*OfferMetadata_FiCoinsToPointsOfferMetadata
	OfferTypeSpecificMetadata isOfferMetadata_OfferTypeSpecificMetadata `protobuf_oneof:"offer_type_specific_metadata"`
}

func (x *OfferMetadata) Reset() {
	*x = OfferMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_offer_catalog_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OfferMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OfferMetadata) ProtoMessage() {}

func (x *OfferMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_offer_catalog_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OfferMetadata.ProtoReflect.Descriptor instead.
func (*OfferMetadata) Descriptor() ([]byte, []int) {
	return file_api_casper_offer_catalog_proto_rawDescGZIP(), []int{9}
}

func (m *OfferMetadata) GetOfferTypeSpecificMetadata() isOfferMetadata_OfferTypeSpecificMetadata {
	if m != nil {
		return m.OfferTypeSpecificMetadata
	}
	return nil
}

func (x *OfferMetadata) GetGiftCardMetadata() *GiftCardOfferMetadata {
	if x, ok := x.GetOfferTypeSpecificMetadata().(*OfferMetadata_GiftCardMetadata); ok {
		return x.GiftCardMetadata
	}
	return nil
}

func (x *OfferMetadata) GetCharityMetadata() *CharityOfferMetadata {
	if x, ok := x.GetOfferTypeSpecificMetadata().(*OfferMetadata_CharityMetadata); ok {
		return x.CharityMetadata
	}
	return nil
}

func (x *OfferMetadata) GetCouponMetadata() *CouponOfferMetadata {
	if x, ok := x.GetOfferTypeSpecificMetadata().(*OfferMetadata_CouponMetadata); ok {
		return x.CouponMetadata
	}
	return nil
}

func (x *OfferMetadata) GetPhysicalMerchMetadata() *PhysicalMerchandiseOfferMetadata {
	if x, ok := x.GetOfferTypeSpecificMetadata().(*OfferMetadata_PhysicalMerchMetadata); ok {
		return x.PhysicalMerchMetadata
	}
	return nil
}

func (x *OfferMetadata) GetPowerUpOfferMetadata() *PowerUpOfferMetadata {
	if x, ok := x.GetOfferTypeSpecificMetadata().(*OfferMetadata_PowerUpOfferMetadata); ok {
		return x.PowerUpOfferMetadata
	}
	return nil
}

func (x *OfferMetadata) GetThriweBenefitsPackageOfferMetadata() *ThriweBenefitsPackageOfferMetadata {
	if x, ok := x.GetOfferTypeSpecificMetadata().(*OfferMetadata_ThriweBenefitsPackageOfferMetadata); ok {
		return x.ThriweBenefitsPackageOfferMetadata
	}
	return nil
}

func (x *OfferMetadata) GetVistaraAirMilesOfferMetadata() *VistaraAirMilesOfferMetadata {
	if x, ok := x.GetOfferTypeSpecificMetadata().(*OfferMetadata_VistaraAirMilesOfferMetadata); ok {
		return x.VistaraAirMilesOfferMetadata
	}
	return nil
}

func (x *OfferMetadata) GetExternalVendorOfferMetadata() *ExternalVendorOfferMetadata {
	if x, ok := x.GetOfferTypeSpecificMetadata().(*OfferMetadata_ExternalVendorOfferMetadata); ok {
		return x.ExternalVendorOfferMetadata
	}
	return nil
}

func (x *OfferMetadata) GetFiCoinsToPointsOfferMetadata() *FiCoinsToPointsOfferMetadata {
	if x, ok := x.GetOfferTypeSpecificMetadata().(*OfferMetadata_FiCoinsToPointsOfferMetadata); ok {
		return x.FiCoinsToPointsOfferMetadata
	}
	return nil
}

type isOfferMetadata_OfferTypeSpecificMetadata interface {
	isOfferMetadata_OfferTypeSpecificMetadata()
}

type OfferMetadata_GiftCardMetadata struct {
	GiftCardMetadata *GiftCardOfferMetadata `protobuf:"bytes,1,opt,name=gift_card_metadata,json=giftCardMetadata,proto3,oneof"`
}

type OfferMetadata_CharityMetadata struct {
	CharityMetadata *CharityOfferMetadata `protobuf:"bytes,2,opt,name=charity_metadata,json=charityMetadata,proto3,oneof"`
}

type OfferMetadata_CouponMetadata struct {
	CouponMetadata *CouponOfferMetadata `protobuf:"bytes,3,opt,name=coupon_metadata,json=couponMetadata,proto3,oneof"`
}

type OfferMetadata_PhysicalMerchMetadata struct {
	PhysicalMerchMetadata *PhysicalMerchandiseOfferMetadata `protobuf:"bytes,4,opt,name=physical_merch_metadata,json=physicalMerchMetadata,proto3,oneof"`
}

type OfferMetadata_PowerUpOfferMetadata struct {
	PowerUpOfferMetadata *PowerUpOfferMetadata `protobuf:"bytes,5,opt,name=power_up_offer_metadata,json=powerUpOfferMetadata,proto3,oneof"`
}

type OfferMetadata_ThriweBenefitsPackageOfferMetadata struct {
	ThriweBenefitsPackageOfferMetadata *ThriweBenefitsPackageOfferMetadata `protobuf:"bytes,6,opt,name=thriwe_benefits_package_offer_metadata,json=thriweBenefitsPackageOfferMetadata,proto3,oneof"`
}

type OfferMetadata_VistaraAirMilesOfferMetadata struct {
	VistaraAirMilesOfferMetadata *VistaraAirMilesOfferMetadata `protobuf:"bytes,7,opt,name=vistara_air_miles_offer_metadata,json=vistaraAirMilesOfferMetadata,proto3,oneof"`
}

type OfferMetadata_ExternalVendorOfferMetadata struct {
	ExternalVendorOfferMetadata *ExternalVendorOfferMetadata `protobuf:"bytes,8,opt,name=external_vendor_offer_metadata,json=externalVendorOfferMetadata,proto3,oneof"`
}

type OfferMetadata_FiCoinsToPointsOfferMetadata struct {
	FiCoinsToPointsOfferMetadata *FiCoinsToPointsOfferMetadata `protobuf:"bytes,9,opt,name=fi_coins_to_points_offer_metadata,json=fiCoinsToPointsOfferMetadata,proto3,oneof"`
}

func (*OfferMetadata_GiftCardMetadata) isOfferMetadata_OfferTypeSpecificMetadata() {}

func (*OfferMetadata_CharityMetadata) isOfferMetadata_OfferTypeSpecificMetadata() {}

func (*OfferMetadata_CouponMetadata) isOfferMetadata_OfferTypeSpecificMetadata() {}

func (*OfferMetadata_PhysicalMerchMetadata) isOfferMetadata_OfferTypeSpecificMetadata() {}

func (*OfferMetadata_PowerUpOfferMetadata) isOfferMetadata_OfferTypeSpecificMetadata() {}

func (*OfferMetadata_ThriweBenefitsPackageOfferMetadata) isOfferMetadata_OfferTypeSpecificMetadata() {
}

func (*OfferMetadata_VistaraAirMilesOfferMetadata) isOfferMetadata_OfferTypeSpecificMetadata() {}

func (*OfferMetadata_ExternalVendorOfferMetadata) isOfferMetadata_OfferTypeSpecificMetadata() {}

func (*OfferMetadata_FiCoinsToPointsOfferMetadata) isOfferMetadata_OfferTypeSpecificMetadata() {}

// metadata for GIFT_CARD offers
type GiftCardOfferMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GiftCardValue int32 `protobuf:"varint,1,opt,name=gift_card_value,json=giftCardValue,proto3" json:"gift_card_value,omitempty"`
}

func (x *GiftCardOfferMetadata) Reset() {
	*x = GiftCardOfferMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_offer_catalog_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GiftCardOfferMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GiftCardOfferMetadata) ProtoMessage() {}

func (x *GiftCardOfferMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_offer_catalog_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GiftCardOfferMetadata.ProtoReflect.Descriptor instead.
func (*GiftCardOfferMetadata) Descriptor() ([]byte, []int) {
	return file_api_casper_offer_catalog_proto_rawDescGZIP(), []int{10}
}

func (x *GiftCardOfferMetadata) GetGiftCardValue() int32 {
	if x != nil {
		return x.GiftCardValue
	}
	return 0
}

// metadata for CHARITY offers
type CharityOfferMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CharityAmount int32 `protobuf:"varint,1,opt,name=charity_amount,json=charityAmount,proto3" json:"charity_amount,omitempty"`
}

func (x *CharityOfferMetadata) Reset() {
	*x = CharityOfferMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_offer_catalog_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CharityOfferMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CharityOfferMetadata) ProtoMessage() {}

func (x *CharityOfferMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_offer_catalog_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CharityOfferMetadata.ProtoReflect.Descriptor instead.
func (*CharityOfferMetadata) Descriptor() ([]byte, []int) {
	return file_api_casper_offer_catalog_proto_rawDescGZIP(), []int{11}
}

func (x *CharityOfferMetadata) GetCharityAmount() int32 {
	if x != nil {
		return x.CharityAmount
	}
	return 0
}

// metadata for PHYSICAL_MERCHANDISE offers
type PhysicalMerchandiseOfferMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// INR value of the merchandise
	MerchValue int32 `protobuf:"varint,1,opt,name=merch_value,json=merchValue,proto3" json:"merch_value,omitempty"`
}

func (x *PhysicalMerchandiseOfferMetadata) Reset() {
	*x = PhysicalMerchandiseOfferMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_offer_catalog_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PhysicalMerchandiseOfferMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PhysicalMerchandiseOfferMetadata) ProtoMessage() {}

func (x *PhysicalMerchandiseOfferMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_offer_catalog_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PhysicalMerchandiseOfferMetadata.ProtoReflect.Descriptor instead.
func (*PhysicalMerchandiseOfferMetadata) Descriptor() ([]byte, []int) {
	return file_api_casper_offer_catalog_proto_rawDescGZIP(), []int{12}
}

func (x *PhysicalMerchandiseOfferMetadata) GetMerchValue() int32 {
	if x != nil {
		return x.MerchValue
	}
	return 0
}

// metadata for COUPON offers
type CouponOfferMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated : use coupon_code_v2 field instead
	// the data type of field was incorrectly set as int32 instead of string.
	CouponCode   int32  `protobuf:"varint,1,opt,name=coupon_code,json=couponCode,proto3" json:"coupon_code,omitempty"`
	CouponCodeV2 string `protobuf:"bytes,2,opt,name=coupon_code_v2,json=couponCodeV2,proto3" json:"coupon_code_v2,omitempty"`
}

func (x *CouponOfferMetadata) Reset() {
	*x = CouponOfferMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_offer_catalog_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CouponOfferMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CouponOfferMetadata) ProtoMessage() {}

func (x *CouponOfferMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_offer_catalog_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CouponOfferMetadata.ProtoReflect.Descriptor instead.
func (*CouponOfferMetadata) Descriptor() ([]byte, []int) {
	return file_api_casper_offer_catalog_proto_rawDescGZIP(), []int{13}
}

func (x *CouponOfferMetadata) GetCouponCode() int32 {
	if x != nil {
		return x.CouponCode
	}
	return 0
}

func (x *CouponOfferMetadata) GetCouponCodeV2() string {
	if x != nil {
		return x.CouponCodeV2
	}
	return ""
}

// todo (utkarsh) : remove this if it's not needed
type ThriweBenefitsPackageOfferMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// denotes the url where the user can activate/view the benefits package.
	ActivationUrl string `protobuf:"bytes,1,opt,name=activation_url,json=activationUrl,proto3" json:"activation_url,omitempty"`
}

func (x *ThriweBenefitsPackageOfferMetadata) Reset() {
	*x = ThriweBenefitsPackageOfferMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_offer_catalog_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThriweBenefitsPackageOfferMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThriweBenefitsPackageOfferMetadata) ProtoMessage() {}

func (x *ThriweBenefitsPackageOfferMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_offer_catalog_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThriweBenefitsPackageOfferMetadata.ProtoReflect.Descriptor instead.
func (*ThriweBenefitsPackageOfferMetadata) Descriptor() ([]byte, []int) {
	return file_api_casper_offer_catalog_proto_rawDescGZIP(), []int{14}
}

func (x *ThriweBenefitsPackageOfferMetadata) GetActivationUrl() string {
	if x != nil {
		return x.ActivationUrl
	}
	return ""
}

type VistaraAirMilesOfferMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// conversion ratio to be used for fi coins to cv points conversion
	FiCoinsToCvPointsConversionRatio float32 `protobuf:"fixed32,1,opt,name=fi_coins_to_cv_points_conversion_ratio,json=fiCoinsToCvPointsConversionRatio,proto3" json:"fi_coins_to_cv_points_conversion_ratio,omitempty"`
	// minimum number of fi coins that the user can convert to cv points
	MinConvertableFiCoins int32 `protobuf:"varint,2,opt,name=min_convertable_fi_coins,json=minConvertableFiCoins,proto3" json:"min_convertable_fi_coins,omitempty"`
	// maximum number of fi coins that the user can convert to cv points
	MaxConvertableFiCoins int32 `protobuf:"varint,3,opt,name=max_convertable_fi_coins,json=maxConvertableFiCoins,proto3" json:"max_convertable_fi_coins,omitempty"`
	// to show the number of cv points the user will get post converting fi coins
	CvPointsBanner *VistaraAirMilesOfferMetadata_Banner `protobuf:"bytes,4,opt,name=cv_points_banner,json=cvPointsBanner,proto3" json:"cv_points_banner,omitempty"`
}

func (x *VistaraAirMilesOfferMetadata) Reset() {
	*x = VistaraAirMilesOfferMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_offer_catalog_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VistaraAirMilesOfferMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VistaraAirMilesOfferMetadata) ProtoMessage() {}

func (x *VistaraAirMilesOfferMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_offer_catalog_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VistaraAirMilesOfferMetadata.ProtoReflect.Descriptor instead.
func (*VistaraAirMilesOfferMetadata) Descriptor() ([]byte, []int) {
	return file_api_casper_offer_catalog_proto_rawDescGZIP(), []int{15}
}

func (x *VistaraAirMilesOfferMetadata) GetFiCoinsToCvPointsConversionRatio() float32 {
	if x != nil {
		return x.FiCoinsToCvPointsConversionRatio
	}
	return 0
}

func (x *VistaraAirMilesOfferMetadata) GetMinConvertableFiCoins() int32 {
	if x != nil {
		return x.MinConvertableFiCoins
	}
	return 0
}

func (x *VistaraAirMilesOfferMetadata) GetMaxConvertableFiCoins() int32 {
	if x != nil {
		return x.MaxConvertableFiCoins
	}
	return 0
}

func (x *VistaraAirMilesOfferMetadata) GetCvPointsBanner() *VistaraAirMilesOfferMetadata_Banner {
	if x != nil {
		return x.CvPointsBanner
	}
	return nil
}

type FiCoinsToPointsOfferMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// multiplier to be used for fi coins to points conversion
	// eg - 0.025 means 1 point for every 40 fi coins spent
	FiCoinsToPointsConversionMultiplier float32 `protobuf:"fixed32,1,opt,name=fi_coins_to_points_conversion_multiplier,json=fiCoinsToPointsConversionMultiplier,proto3" json:"fi_coins_to_points_conversion_multiplier,omitempty"`
	// multiplier to be used to calculate the step size for fi coins slider
	// step size is calculated with the following formula - (1/fi_coins_to_points_conversion_multiplier)*fi_coins_step_size_multiplier
	// eg - for a multiplier of 5, step size will be = (1/0.025)*5 = 200
	FiCoinsStepSizeMultiplier uint32 `protobuf:"varint,2,opt,name=fi_coins_step_size_multiplier,json=fiCoinsStepSizeMultiplier,proto3" json:"fi_coins_step_size_multiplier,omitempty"`
	// minimum number of fi coins that the user can convert to points
	// this should be a multiple of (1/fi_coins_to_points_conversion_multiplier)*fi_coins_step_size_multiplier
	// eg - 200, 400, 600, 800.. and so on
	MinConvertableFiCoins int32 `protobuf:"varint,3,opt,name=min_convertable_fi_coins,json=minConvertableFiCoins,proto3" json:"min_convertable_fi_coins,omitempty"`
	// maximum number of fi coins that the user can convert to points
	// this should be a multiple of (1/fi_coins_to_points_conversion_multiplier)*fi_coins_step_size_multiplier
	MaxConvertableFiCoins int32 `protobuf:"varint,4,opt,name=max_convertable_fi_coins,json=maxConvertableFiCoins,proto3" json:"max_convertable_fi_coins,omitempty"`
	// to show the number of points the user will get post converting fi coins
	PointsBanner *FiCoinsToPointsOfferMetadata_Banner `protobuf:"bytes,5,opt,name=points_banner,json=pointsBanner,proto3" json:"points_banner,omitempty"`
}

func (x *FiCoinsToPointsOfferMetadata) Reset() {
	*x = FiCoinsToPointsOfferMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_offer_catalog_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FiCoinsToPointsOfferMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FiCoinsToPointsOfferMetadata) ProtoMessage() {}

func (x *FiCoinsToPointsOfferMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_offer_catalog_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FiCoinsToPointsOfferMetadata.ProtoReflect.Descriptor instead.
func (*FiCoinsToPointsOfferMetadata) Descriptor() ([]byte, []int) {
	return file_api_casper_offer_catalog_proto_rawDescGZIP(), []int{16}
}

func (x *FiCoinsToPointsOfferMetadata) GetFiCoinsToPointsConversionMultiplier() float32 {
	if x != nil {
		return x.FiCoinsToPointsConversionMultiplier
	}
	return 0
}

func (x *FiCoinsToPointsOfferMetadata) GetFiCoinsStepSizeMultiplier() uint32 {
	if x != nil {
		return x.FiCoinsStepSizeMultiplier
	}
	return 0
}

func (x *FiCoinsToPointsOfferMetadata) GetMinConvertableFiCoins() int32 {
	if x != nil {
		return x.MinConvertableFiCoins
	}
	return 0
}

func (x *FiCoinsToPointsOfferMetadata) GetMaxConvertableFiCoins() int32 {
	if x != nil {
		return x.MaxConvertableFiCoins
	}
	return 0
}

func (x *FiCoinsToPointsOfferMetadata) GetPointsBanner() *FiCoinsToPointsOfferMetadata_Banner {
	if x != nil {
		return x.PointsBanner
	}
	return nil
}

// metadata for running external vendor offers on catalog
type ExternalVendorOfferMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// base url of vendor's catalog page
	BaseUrl string `protobuf:"bytes,1,opt,name=base_url,json=baseUrl,proto3" json:"base_url,omitempty"`
	// title of the vendor's catalog webpage
	WebPageTitle string `protobuf:"bytes,2,opt,name=web_page_title,json=webPageTitle,proto3" json:"web_page_title,omitempty"`
}

func (x *ExternalVendorOfferMetadata) Reset() {
	*x = ExternalVendorOfferMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_offer_catalog_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExternalVendorOfferMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExternalVendorOfferMetadata) ProtoMessage() {}

func (x *ExternalVendorOfferMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_offer_catalog_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExternalVendorOfferMetadata.ProtoReflect.Descriptor instead.
func (*ExternalVendorOfferMetadata) Descriptor() ([]byte, []int) {
	return file_api_casper_offer_catalog_proto_rawDescGZIP(), []int{17}
}

func (x *ExternalVendorOfferMetadata) GetBaseUrl() string {
	if x != nil {
		return x.BaseUrl
	}
	return ""
}

func (x *ExternalVendorOfferMetadata) GetWebPageTitle() string {
	if x != nil {
		return x.WebPageTitle
	}
	return ""
}

// details of discount running for the offer, if any
type DiscountDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// new price after application of discount
	DiscountedPrice float32 `protobuf:"fixed32,1,opt,name=discounted_price,json=discountedPrice,proto3" json:"discounted_price,omitempty"`
	// display mode
	DisplayMode discounts.DisplayMode `protobuf:"varint,2,opt,name=display_mode,json=displayMode,proto3,enum=casper.DisplayMode" json:"display_mode,omitempty"`
	// time at which the discount will end
	DiscountEndTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=discount_end_time,json=discountEndTime,proto3" json:"discount_end_time,omitempty"`
}

func (x *DiscountDetails) Reset() {
	*x = DiscountDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_offer_catalog_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DiscountDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DiscountDetails) ProtoMessage() {}

func (x *DiscountDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_offer_catalog_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DiscountDetails.ProtoReflect.Descriptor instead.
func (*DiscountDetails) Descriptor() ([]byte, []int) {
	return file_api_casper_offer_catalog_proto_rawDescGZIP(), []int{18}
}

func (x *DiscountDetails) GetDiscountedPrice() float32 {
	if x != nil {
		return x.DiscountedPrice
	}
	return 0
}

func (x *DiscountDetails) GetDisplayMode() discounts.DisplayMode {
	if x != nil {
		return x.DisplayMode
	}
	return discounts.DisplayMode(0)
}

func (x *DiscountDetails) GetDiscountEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DiscountEndTime
	}
	return nil
}

// stores metadata for limiting rate of redemption of an offer
type RedemptionRateLimitMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// offer redemption will be disabled for cool off hours after each redemption
	CoolOffHours int32 `protobuf:"varint,1,opt,name=cool_off_hours,json=coolOffHours,proto3" json:"cool_off_hours,omitempty"`
}

func (x *RedemptionRateLimitMetadata) Reset() {
	*x = RedemptionRateLimitMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_offer_catalog_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RedemptionRateLimitMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RedemptionRateLimitMetadata) ProtoMessage() {}

func (x *RedemptionRateLimitMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_offer_catalog_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RedemptionRateLimitMetadata.ProtoReflect.Descriptor instead.
func (*RedemptionRateLimitMetadata) Descriptor() ([]byte, []int) {
	return file_api_casper_offer_catalog_proto_rawDescGZIP(), []int{19}
}

func (x *RedemptionRateLimitMetadata) GetCoolOffHours() int32 {
	if x != nil {
		return x.CoolOffHours
	}
	return 0
}

// metadata for power-up type of offer
type PowerUpOfferMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// stores metadata for limiting rate of redemption of the power up offer
	RedemptionRateLimitMetadata *RedemptionRateLimitMetadata `protobuf:"bytes,1,opt,name=redemption_rate_limit_metadata,json=redemptionRateLimitMetadata,proto3" json:"redemption_rate_limit_metadata,omitempty"`
	// power-up related extra metadata to be forwarded to client via dynamic data/fields
	ExtraMetadata []*OfferKeyValueMetadata `protobuf:"bytes,3,rep,name=extra_metadata,json=extraMetadata,proto3" json:"extra_metadata,omitempty"`
}

func (x *PowerUpOfferMetadata) Reset() {
	*x = PowerUpOfferMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_offer_catalog_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PowerUpOfferMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PowerUpOfferMetadata) ProtoMessage() {}

func (x *PowerUpOfferMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_offer_catalog_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PowerUpOfferMetadata.ProtoReflect.Descriptor instead.
func (*PowerUpOfferMetadata) Descriptor() ([]byte, []int) {
	return file_api_casper_offer_catalog_proto_rawDescGZIP(), []int{20}
}

func (x *PowerUpOfferMetadata) GetRedemptionRateLimitMetadata() *RedemptionRateLimitMetadata {
	if x != nil {
		return x.RedemptionRateLimitMetadata
	}
	return nil
}

func (x *PowerUpOfferMetadata) GetExtraMetadata() []*OfferKeyValueMetadata {
	if x != nil {
		return x.ExtraMetadata
	}
	return nil
}

// stores metadata related to in house redemption request
type InHouseVendorOfferMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// sku id corresponding to which coupon has to be redeemed
	SkuId string `protobuf:"bytes,1,opt,name=skuId,proto3" json:"skuId,omitempty"`
}

func (x *InHouseVendorOfferMetadata) Reset() {
	*x = InHouseVendorOfferMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_offer_catalog_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InHouseVendorOfferMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InHouseVendorOfferMetadata) ProtoMessage() {}

func (x *InHouseVendorOfferMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_offer_catalog_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InHouseVendorOfferMetadata.ProtoReflect.Descriptor instead.
func (*InHouseVendorOfferMetadata) Descriptor() ([]byte, []int) {
	return file_api_casper_offer_catalog_proto_rawDescGZIP(), []int{21}
}

func (x *InHouseVendorOfferMetadata) GetSkuId() string {
	if x != nil {
		return x.SkuId
	}
	return ""
}

// stores the conversion ratio applicable for an offer
type FiCoinsToCashConversionRatio struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// number of fi-coins and it's cash equivalent.
	FiCoinsValue   uint32 `protobuf:"varint,1,opt,name=fi_coins_value,json=fiCoinsValue,proto3" json:"fi_coins_value,omitempty"`
	CashEquivalent uint32 `protobuf:"varint,2,opt,name=cash_equivalent,json=cashEquivalent,proto3" json:"cash_equivalent,omitempty"`
}

func (x *FiCoinsToCashConversionRatio) Reset() {
	*x = FiCoinsToCashConversionRatio{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_offer_catalog_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FiCoinsToCashConversionRatio) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FiCoinsToCashConversionRatio) ProtoMessage() {}

func (x *FiCoinsToCashConversionRatio) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_offer_catalog_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FiCoinsToCashConversionRatio.ProtoReflect.Descriptor instead.
func (*FiCoinsToCashConversionRatio) Descriptor() ([]byte, []int) {
	return file_api_casper_offer_catalog_proto_rawDescGZIP(), []int{22}
}

func (x *FiCoinsToCashConversionRatio) GetFiCoinsValue() uint32 {
	if x != nil {
		return x.FiCoinsValue
	}
	return 0
}

func (x *FiCoinsToCashConversionRatio) GetCashEquivalent() uint32 {
	if x != nil {
		return x.CashEquivalent
	}
	return 0
}

// stores watson config related to the issue category corresponding to which
// a user activity event or an error activity event needs to be created
// error activity event involves ticket creation when something goes wrong
// in a redeemed offer(redemption) against this offer and resolution when
// the issue is resolved
type WatsonMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// this should be set to true when we want to create a watson event
	// when a redeemed offer is not successfully processed against this offer
	// Mapped against IsWatsonEvent when publishing an event
	ShouldCreateWatsonEvent bool   `protobuf:"varint,1,opt,name=should_create_watson_event,json=shouldCreateWatsonEvent,proto3" json:"should_create_watson_event,omitempty"`
	IssueCategoryId         string `protobuf:"bytes,2,opt,name=issue_category_id,json=issueCategoryId,proto3" json:"issue_category_id,omitempty"`
}

func (x *WatsonMeta) Reset() {
	*x = WatsonMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_offer_catalog_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WatsonMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WatsonMeta) ProtoMessage() {}

func (x *WatsonMeta) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_offer_catalog_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WatsonMeta.ProtoReflect.Descriptor instead.
func (*WatsonMeta) Descriptor() ([]byte, []int) {
	return file_api_casper_offer_catalog_proto_rawDescGZIP(), []int{23}
}

func (x *WatsonMeta) GetShouldCreateWatsonEvent() bool {
	if x != nil {
		return x.ShouldCreateWatsonEvent
	}
	return false
}

func (x *WatsonMeta) GetIssueCategoryId() string {
	if x != nil {
		return x.IssueCategoryId
	}
	return ""
}

// OfferDescription will contain heading and a list of points where each element is to be displayed as line seperated numbered points
// heading will not be numbered, numbering will start after the heading
// heading or points can be nil if not required
// eg. for offerDescription when numbered points are required with heading -
//
//	This gift box contains -
//	  1. A 500 gm bag of Vienna Roast coffee
//	  2. Easy coffee Bags (Box of 5 bags)
//
// eg. for offerDescription only a heading is required without points -
//
//	Scan the code below at the counter to get 800 off on your total bill when you shop at M&S India
type OfferAdditionalDetails_OfferDescription struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Heading string   `protobuf:"bytes,1,opt,name=heading,proto3" json:"heading,omitempty"`
	Points  []string `protobuf:"bytes,2,rep,name=points,proto3" json:"points,omitempty"`
}

func (x *OfferAdditionalDetails_OfferDescription) Reset() {
	*x = OfferAdditionalDetails_OfferDescription{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_offer_catalog_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OfferAdditionalDetails_OfferDescription) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OfferAdditionalDetails_OfferDescription) ProtoMessage() {}

func (x *OfferAdditionalDetails_OfferDescription) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_offer_catalog_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OfferAdditionalDetails_OfferDescription.ProtoReflect.Descriptor instead.
func (*OfferAdditionalDetails_OfferDescription) Descriptor() ([]byte, []int) {
	return file_api_casper_offer_catalog_proto_rawDescGZIP(), []int{2, 1}
}

func (x *OfferAdditionalDetails_OfferDescription) GetHeading() string {
	if x != nil {
		return x.Heading
	}
	return ""
}

func (x *OfferAdditionalDetails_OfferDescription) GetPoints() []string {
	if x != nil {
		return x.Points
	}
	return nil
}

type OfferAdditionalDetails_DisplayTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Text      string `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	TextColor string `protobuf:"bytes,2,opt,name=text_color,json=textColor,proto3" json:"text_color,omitempty"`
	BgColor   string `protobuf:"bytes,3,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
}

func (x *OfferAdditionalDetails_DisplayTag) Reset() {
	*x = OfferAdditionalDetails_DisplayTag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_offer_catalog_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OfferAdditionalDetails_DisplayTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OfferAdditionalDetails_DisplayTag) ProtoMessage() {}

func (x *OfferAdditionalDetails_DisplayTag) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_offer_catalog_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OfferAdditionalDetails_DisplayTag.ProtoReflect.Descriptor instead.
func (*OfferAdditionalDetails_DisplayTag) Descriptor() ([]byte, []int) {
	return file_api_casper_offer_catalog_proto_rawDescGZIP(), []int{2, 2}
}

func (x *OfferAdditionalDetails_DisplayTag) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *OfferAdditionalDetails_DisplayTag) GetTextColor() string {
	if x != nil {
		return x.TextColor
	}
	return ""
}

func (x *OfferAdditionalDetails_DisplayTag) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

type OfferAdditionalDetails_MessageConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title    string             `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Body     string             `protobuf:"bytes,2,opt,name=body,proto3" json:"body,omitempty"`
	ImageUrl string             `protobuf:"bytes,3,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	Deeplink *deeplink.Deeplink `protobuf:"bytes,4,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
}

func (x *OfferAdditionalDetails_MessageConfig) Reset() {
	*x = OfferAdditionalDetails_MessageConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_offer_catalog_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OfferAdditionalDetails_MessageConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OfferAdditionalDetails_MessageConfig) ProtoMessage() {}

func (x *OfferAdditionalDetails_MessageConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_offer_catalog_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OfferAdditionalDetails_MessageConfig.ProtoReflect.Descriptor instead.
func (*OfferAdditionalDetails_MessageConfig) Descriptor() ([]byte, []int) {
	return file_api_casper_offer_catalog_proto_rawDescGZIP(), []int{2, 3}
}

func (x *OfferAdditionalDetails_MessageConfig) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *OfferAdditionalDetails_MessageConfig) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

func (x *OfferAdditionalDetails_MessageConfig) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

func (x *OfferAdditionalDetails_MessageConfig) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

type OfferAdditionalDetails_ExpiryNotificationConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// denotes the number of days before expiry of the offer when we want to send the notification
	DaysBeforeExpiry int32                                   `protobuf:"varint,1,opt,name=days_before_expiry,json=daysBeforeExpiry,proto3" json:"days_before_expiry,omitempty"`
	MessageConfig    *OfferAdditionalDetails_MessageConfig   `protobuf:"bytes,2,opt,name=message_config,json=messageConfig,proto3" json:"message_config,omitempty"`
	NotificationType OfferAdditionalDetails_NotificationType `protobuf:"varint,3,opt,name=notification_type,json=notificationType,proto3,enum=casper.OfferAdditionalDetails_NotificationType" json:"notification_type,omitempty"`
}

func (x *OfferAdditionalDetails_ExpiryNotificationConfig) Reset() {
	*x = OfferAdditionalDetails_ExpiryNotificationConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_offer_catalog_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OfferAdditionalDetails_ExpiryNotificationConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OfferAdditionalDetails_ExpiryNotificationConfig) ProtoMessage() {}

func (x *OfferAdditionalDetails_ExpiryNotificationConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_offer_catalog_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OfferAdditionalDetails_ExpiryNotificationConfig.ProtoReflect.Descriptor instead.
func (*OfferAdditionalDetails_ExpiryNotificationConfig) Descriptor() ([]byte, []int) {
	return file_api_casper_offer_catalog_proto_rawDescGZIP(), []int{2, 4}
}

func (x *OfferAdditionalDetails_ExpiryNotificationConfig) GetDaysBeforeExpiry() int32 {
	if x != nil {
		return x.DaysBeforeExpiry
	}
	return 0
}

func (x *OfferAdditionalDetails_ExpiryNotificationConfig) GetMessageConfig() *OfferAdditionalDetails_MessageConfig {
	if x != nil {
		return x.MessageConfig
	}
	return nil
}

func (x *OfferAdditionalDetails_ExpiryNotificationConfig) GetNotificationType() OfferAdditionalDetails_NotificationType {
	if x != nil {
		return x.NotificationType
	}
	return OfferAdditionalDetails_NOTIFICATION_TYPE_UNSPECIFIED
}

// CharityOfferMetadata contains specific fields for charity offer.
// This information will be required for performing redemption of charity
// offers while calling vendor (loylty) api.
type LoyltyVendorOfferMetadata_CharityOfferMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Category    string `protobuf:"bytes,1,opt,name=category,proto3" json:"category,omitempty"`
	CharityName string `protobuf:"bytes,2,opt,name=charity_name,json=charityName,proto3" json:"charity_name,omitempty"`
	CharityCode string `protobuf:"bytes,3,opt,name=charity_code,json=charityCode,proto3" json:"charity_code,omitempty"`
	ProductName string `protobuf:"bytes,4,opt,name=product_name,json=productName,proto3" json:"product_name,omitempty"`
}

func (x *LoyltyVendorOfferMetadata_CharityOfferMetadata) Reset() {
	*x = LoyltyVendorOfferMetadata_CharityOfferMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_offer_catalog_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoyltyVendorOfferMetadata_CharityOfferMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoyltyVendorOfferMetadata_CharityOfferMetadata) ProtoMessage() {}

func (x *LoyltyVendorOfferMetadata_CharityOfferMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_offer_catalog_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoyltyVendorOfferMetadata_CharityOfferMetadata.ProtoReflect.Descriptor instead.
func (*LoyltyVendorOfferMetadata_CharityOfferMetadata) Descriptor() ([]byte, []int) {
	return file_api_casper_offer_catalog_proto_rawDescGZIP(), []int{4, 0}
}

func (x *LoyltyVendorOfferMetadata_CharityOfferMetadata) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *LoyltyVendorOfferMetadata_CharityOfferMetadata) GetCharityName() string {
	if x != nil {
		return x.CharityName
	}
	return ""
}

func (x *LoyltyVendorOfferMetadata_CharityOfferMetadata) GetCharityCode() string {
	if x != nil {
		return x.CharityCode
	}
	return ""
}

func (x *LoyltyVendorOfferMetadata_CharityOfferMetadata) GetProductName() string {
	if x != nil {
		return x.ProductName
	}
	return ""
}

// banner to be displayed on the vistara air miles redemption bottom sheet
type VistaraAirMilesOfferMetadata_Banner struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LeftImgUrl  string `protobuf:"bytes,1,opt,name=left_img_url,json=leftImgUrl,proto3" json:"left_img_url,omitempty"`
	RightImgUrl string `protobuf:"bytes,2,opt,name=right_img_url,json=rightImgUrl,proto3" json:"right_img_url,omitempty"`
	BgColor     string `protobuf:"bytes,3,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
}

func (x *VistaraAirMilesOfferMetadata_Banner) Reset() {
	*x = VistaraAirMilesOfferMetadata_Banner{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_offer_catalog_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VistaraAirMilesOfferMetadata_Banner) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VistaraAirMilesOfferMetadata_Banner) ProtoMessage() {}

func (x *VistaraAirMilesOfferMetadata_Banner) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_offer_catalog_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VistaraAirMilesOfferMetadata_Banner.ProtoReflect.Descriptor instead.
func (*VistaraAirMilesOfferMetadata_Banner) Descriptor() ([]byte, []int) {
	return file_api_casper_offer_catalog_proto_rawDescGZIP(), []int{15, 0}
}

func (x *VistaraAirMilesOfferMetadata_Banner) GetLeftImgUrl() string {
	if x != nil {
		return x.LeftImgUrl
	}
	return ""
}

func (x *VistaraAirMilesOfferMetadata_Banner) GetRightImgUrl() string {
	if x != nil {
		return x.RightImgUrl
	}
	return ""
}

func (x *VistaraAirMilesOfferMetadata_Banner) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

// banner to be displayed on the points redemption bottom sheet
type FiCoinsToPointsOfferMetadata_Banner struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Description string `protobuf:"bytes,1,opt,name=description,proto3" json:"description,omitempty"`
	LeftImgUrl  string `protobuf:"bytes,2,opt,name=left_img_url,json=leftImgUrl,proto3" json:"left_img_url,omitempty"`
	RightImgUrl string `protobuf:"bytes,3,opt,name=right_img_url,json=rightImgUrl,proto3" json:"right_img_url,omitempty"`
	BgColor     string `protobuf:"bytes,4,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
}

func (x *FiCoinsToPointsOfferMetadata_Banner) Reset() {
	*x = FiCoinsToPointsOfferMetadata_Banner{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_offer_catalog_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FiCoinsToPointsOfferMetadata_Banner) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FiCoinsToPointsOfferMetadata_Banner) ProtoMessage() {}

func (x *FiCoinsToPointsOfferMetadata_Banner) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_offer_catalog_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FiCoinsToPointsOfferMetadata_Banner.ProtoReflect.Descriptor instead.
func (*FiCoinsToPointsOfferMetadata_Banner) Descriptor() ([]byte, []int) {
	return file_api_casper_offer_catalog_proto_rawDescGZIP(), []int{16, 0}
}

func (x *FiCoinsToPointsOfferMetadata_Banner) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *FiCoinsToPointsOfferMetadata_Banner) GetLeftImgUrl() string {
	if x != nil {
		return x.LeftImgUrl
	}
	return ""
}

func (x *FiCoinsToPointsOfferMetadata_Banner) GetRightImgUrl() string {
	if x != nil {
		return x.RightImgUrl
	}
	return ""
}

func (x *FiCoinsToPointsOfferMetadata_Banner) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

var File_api_casper_offer_catalog_proto protoreflect.FileDescriptor

var file_api_casper_offer_catalog_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2f, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x5f, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x06, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61,
	0x73, 0x70, 0x65, 0x72, 0x2f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x64, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x61, 0x70,
	0x69, 0x2f, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f,
	0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2f, 0x74, 0x61, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x6f, 0x6c, 0x65,
	0x61, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x50, 0x0a,
	0x0a, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x30, 0x0a, 0x0a, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x11, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x22,
	0x25, 0x0a, 0x08, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x6e, 0x63, 0x12, 0x19, 0x0a, 0x08, 0x74,
	0x6e, 0x63, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x74,
	0x6e, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x84, 0x13, 0x0a, 0x16, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x22, 0x0a, 0x0d, 0x68, 0x6f, 0x77, 0x5f, 0x74, 0x6f,
	0x5f, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x68,
	0x6f, 0x77, 0x54, 0x6f, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x67,
	0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x67,
	0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x3d, 0x0a, 0x1b, 0x61, 0x66, 0x74, 0x65, 0x72, 0x5f, 0x72,
	0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x61, 0x66, 0x74, 0x65,
	0x72, 0x52, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x10, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x52,
	0x61, 0x6e, 0x6b, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x73, 0x74, 0x65, 0x70,
	0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x65, 0x78, 0x74, 0x53, 0x74, 0x65,
	0x70, 0x73, 0x12, 0x59, 0x0a, 0x1a, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x74, 0x61, 0x67,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74,
	0x42, 0x02, 0x18, 0x01, 0x52, 0x17, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x54, 0x61, 0x67, 0x12, 0x1d, 0x0a,
	0x0a, 0x68, 0x6f, 0x6d, 0x65, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x68, 0x6f, 0x6d, 0x65, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x41, 0x0a, 0x1b,
	0x69, 0x73, 0x5f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x08, 0x42, 0x02, 0x18, 0x01, 0x52, 0x18, 0x69, 0x73, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x5b,
	0x0a, 0x17, 0x74, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x14, 0x74, 0x69, 0x6c, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x42, 0x0a, 0x1d, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e,
	0x74, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x1b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x43, 0x6f, 0x6e, 0x73, 0x74,
	0x72, 0x61, 0x69, 0x6e, 0x74, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x6f, 0x0a, 0x17, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x5f, 0x72, 0x61, 0x6e, 0x6b, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x38, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x41,
	0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x52, 0x61,
	0x6e, 0x6b, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x14, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x52, 0x61, 0x6e, 0x6b, 0x4d, 0x61, 0x70,
	0x12, 0x24, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x5f, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x50, 0x72, 0x6f, 0x6d,
	0x6f, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x5f,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x6f,
	0x6d, 0x6f, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x37, 0x0a, 0x18, 0x70, 0x72, 0x6f, 0x6d, 0x6f,
	0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x72,
	0x61, 0x6e, 0x6b, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52, 0x15, 0x70, 0x72, 0x6f, 0x6d, 0x6f,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x52, 0x61, 0x6e, 0x6b,
	0x12, 0x5c, 0x0a, 0x11, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x63, 0x61,
	0x73, 0x70, 0x65, 0x72, 0x2e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x10, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2c,
	0x0a, 0x12, 0x61, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x74, 0x61, 0x5f,
	0x74, 0x65, 0x78, 0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x61, 0x6c, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x74, 0x65, 0x43, 0x74, 0x61, 0x54, 0x65, 0x78, 0x74, 0x12, 0x4c, 0x0a, 0x0c,
	0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x74, 0x61, 0x67, 0x73, 0x18, 0x14, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x54, 0x61, 0x67, 0x52, 0x0b, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x54, 0x61, 0x67, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x69, 0x73,
	0x5f, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x18, 0x15, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0e, 0x69, 0x73, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e,
	0x74, 0x61, 0x6c, 0x12, 0x3d, 0x0a, 0x0f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63,
	0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0d, 0x63, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x4f, 0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63,
	0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x46, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x54, 0x6f, 0x43,
	0x61, 0x73, 0x68, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x74,
	0x69, 0x6f, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x61,
	0x74, 0x69, 0x6f, 0x12, 0x2d, 0x0a, 0x12, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65,
	0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x11, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x77, 0x0a, 0x1b, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x73, 0x18, 0x19, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72,
	0x2e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x45, 0x78, 0x70, 0x69, 0x72, 0x79, 0x4e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x19, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x12, 0x33, 0x0a, 0x0b, 0x77,
	0x61, 0x74, 0x73, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x57, 0x61, 0x74, 0x73, 0x6f, 0x6e,
	0x4d, 0x65, 0x74, 0x61, 0x52, 0x0a, 0x77, 0x61, 0x74, 0x73, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61,
	0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x1b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x41, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x66, 0x69, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18,
	0x1c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65,
	0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0a, 0x69, 0x73, 0x46, 0x69, 0x50, 0x6f, 0x69, 0x6e,
	0x74, 0x73, 0x1a, 0x47, 0x0a, 0x19, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x44, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x52, 0x61, 0x6e, 0x6b, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x44, 0x0a, 0x10, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x18, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x73, 0x1a, 0x5a, 0x0a, 0x0a, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x54, 0x61, 0x67, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x65, 0x78, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6c,
	0x6f, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x1a, 0x8f, 0x01,
	0x0a, 0x0d, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x37, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x1a,
	0xfb, 0x01, 0x0a, 0x18, 0x45, 0x78, 0x70, 0x69, 0x72, 0x79, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x2c, 0x0a, 0x12,
	0x64, 0x61, 0x79, 0x73, 0x5f, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x69,
	0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x64, 0x61, 0x79, 0x73, 0x42, 0x65,
	0x66, 0x6f, 0x72, 0x65, 0x45, 0x78, 0x70, 0x69, 0x72, 0x79, 0x12, 0x53, 0x0a, 0x0e, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x0d, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x5c, 0x0a, 0x11, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2f, 0x2e, 0x63, 0x61, 0x73,
	0x70, 0x65, 0x72, 0x2e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x22, 0x9d, 0x01,
	0x0a, 0x10, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x21, 0x0a, 0x1d, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x59, 0x53, 0x54, 0x45,
	0x4d, 0x5f, 0x54, 0x52, 0x41, 0x59, 0x10, 0x01, 0x12, 0x25, 0x0a, 0x21, 0x4e, 0x4f, 0x54, 0x49,
	0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e,
	0x5f, 0x41, 0x50, 0x50, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x49, 0x43, 0x41, 0x4c, 0x10, 0x02, 0x12,
	0x1c, 0x0a, 0x18, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x10, 0x03, 0x22, 0xbf, 0x04,
	0x0a, 0x13, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x64, 0x0a, 0x1c, 0x6c, 0x6f, 0x79, 0x6c, 0x74, 0x79, 0x5f,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x6d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x61,
	0x73, 0x70, 0x65, 0x72, 0x2e, 0x4c, 0x6f, 0x79, 0x6c, 0x74, 0x79, 0x56, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x48, 0x00,
	0x52, 0x19, 0x6c, 0x6f, 0x79, 0x6c, 0x74, 0x79, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x67, 0x0a, 0x1d, 0x6f,
	0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x4f, 0x66, 0x66, 0x6c,
	0x69, 0x6e, 0x65, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x1a, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e,
	0x65, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x12, 0x70, 0x0a, 0x20, 0x71, 0x77, 0x69, 0x6b, 0x63, 0x69, 0x6c, 0x76,
	0x65, 0x72, 0x5f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f,
	0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25,
	0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x51, 0x77, 0x69, 0x6b, 0x63, 0x69, 0x6c, 0x76,
	0x65, 0x72, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x1d, 0x71, 0x77, 0x69, 0x6b, 0x63, 0x69, 0x6c,
	0x76, 0x65, 0x72, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x64, 0x0a, 0x1c, 0x74, 0x68, 0x72, 0x69, 0x77, 0x65,
	0x5f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63,
	0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x54, 0x68, 0x72, 0x69, 0x77, 0x65, 0x56, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x48,
	0x00, 0x52, 0x19, 0x74, 0x68, 0x72, 0x69, 0x77, 0x65, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x68, 0x0a, 0x1e,
	0x69, 0x6e, 0x5f, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x5f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x49, 0x6e,
	0x48, 0x6f, 0x75, 0x73, 0x65, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x1a, 0x69, 0x6e, 0x48, 0x6f,
	0x75, 0x73, 0x65, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x42, 0x17, 0x0a, 0x15, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x22,
	0xff, 0x02, 0x0a, 0x19, 0x4c, 0x6f, 0x79, 0x6c, 0x74, 0x79, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x15, 0x0a,
	0x06, 0x73, 0x6b, 0x75, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73,
	0x6b, 0x75, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x49, 0x64, 0x12, 0x6e, 0x0a, 0x16, 0x63, 0x68, 0x61, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x4c, 0x6f, 0x79,
	0x6c, 0x74, 0x79, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x69, 0x74, 0x79, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x14, 0x63,
	0x68, 0x61, 0x72, 0x69, 0x74, 0x79, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x1a, 0x9b, 0x01, 0x0a, 0x14, 0x43, 0x68, 0x61, 0x72, 0x69, 0x74, 0x79, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1a, 0x0a, 0x08,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x72,
	0x69, 0x74, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x63, 0x68, 0x61, 0x72, 0x69, 0x74, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x63,
	0x68, 0x61, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x72, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x21,
	0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x42, 0x1e, 0x0a, 0x1c, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f,
	0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x22, 0x36, 0x0a, 0x1d, 0x51, 0x77, 0x69, 0x6b, 0x63, 0x69, 0x6c, 0x76, 0x65, 0x72, 0x56,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x12, 0x15, 0x0a, 0x06, 0x73, 0x6b, 0x75, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x73, 0x6b, 0x75, 0x49, 0x64, 0x22, 0x3b, 0x0a, 0x1a, 0x4f, 0x66, 0x66,
	0x6c, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x22, 0xbc, 0x01, 0x0a, 0x19, 0x54, 0x68, 0x72, 0x69, 0x77,
	0x65, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x12, 0x2e, 0x0a, 0x13, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73,
	0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x11, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x50, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x15, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73,
	0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x13, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x50, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x69,
	0x72, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72,
	0x79, 0x44, 0x61, 0x74, 0x65, 0x22, 0xda, 0x06, 0x0a, 0x05, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x30, 0x0a,
	0x0a, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x11, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x44, 0x0a, 0x0f, 0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65,
	0x72, 0x2e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x0e, 0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x34, 0x0a, 0x0b, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x61, 0x73,
	0x70, 0x65, 0x72, 0x2e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52,
	0x0a, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x4f, 0x0a, 0x15, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x6d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x61, 0x73,
	0x70, 0x65, 0x72, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x13, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1f, 0x0a, 0x0b,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x3c, 0x0a,
	0x0e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x0d, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x2a, 0x0a, 0x06, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x61,
	0x73, 0x70, 0x65, 0x72, 0x2e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52,
	0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x22, 0x0a, 0x03, 0x74, 0x6e, 0x63, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x54, 0x6e, 0x63, 0x52, 0x03, 0x74, 0x6e, 0x63, 0x12, 0x4d, 0x0a, 0x12, 0x61,
	0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72,
	0x2e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x11, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73,
	0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09,
	0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x42, 0x0a, 0x10, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x61, 0x73, 0x70,
	0x65, 0x72, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x0f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x2d, 0x0a, 0x09, 0x74, 0x61, 0x67, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e,
	0x54, 0x61, 0x67, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x74, 0x61, 0x67, 0x73, 0x49, 0x6e,
	0x66, 0x6f, 0x22, 0x9c, 0x07, 0x0a, 0x0d, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x12, 0x4d, 0x0a, 0x12, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x63, 0x61, 0x72,
	0x64, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x48,
	0x00, 0x52, 0x10, 0x67, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x12, 0x49, 0x0a, 0x10, 0x63, 0x68, 0x61, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x6d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x69, 0x74, 0x79, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0f, 0x63,
	0x68, 0x61, 0x72, 0x69, 0x74, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x46,
	0x0a, 0x0f, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72,
	0x2e, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0e, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x62, 0x0a, 0x17, 0x70, 0x68, 0x79, 0x73, 0x69, 0x63,
	0x61, 0x6c, 0x5f, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72,
	0x2e, 0x50, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e,
	0x64, 0x69, 0x73, 0x65, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x48, 0x00, 0x52, 0x15, 0x70, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x4d, 0x65, 0x72,
	0x63, 0x68, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x55, 0x0a, 0x17, 0x70, 0x6f,
	0x77, 0x65, 0x72, 0x5f, 0x75, 0x70, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x6d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x61,
	0x73, 0x70, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x55, 0x70, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x14, 0x70, 0x6f, 0x77,
	0x65, 0x72, 0x55, 0x70, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x12, 0x80, 0x01, 0x0a, 0x26, 0x74, 0x68, 0x72, 0x69, 0x77, 0x65, 0x5f, 0x62, 0x65, 0x6e,
	0x65, 0x66, 0x69, 0x74, 0x73, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x54, 0x68, 0x72, 0x69,
	0x77, 0x65, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x48, 0x00,
	0x52, 0x22, 0x74, 0x68, 0x72, 0x69, 0x77, 0x65, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73,
	0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x12, 0x6e, 0x0a, 0x20, 0x76, 0x69, 0x73, 0x74, 0x61, 0x72, 0x61, 0x5f,
	0x61, 0x69, 0x72, 0x5f, 0x6d, 0x69, 0x6c, 0x65, 0x73, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f,
	0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x56, 0x69, 0x73, 0x74, 0x61, 0x72, 0x61, 0x41,
	0x69, 0x72, 0x4d, 0x69, 0x6c, 0x65, 0x73, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x1c, 0x76, 0x69, 0x73, 0x74, 0x61, 0x72, 0x61, 0x41,
	0x69, 0x72, 0x4d, 0x69, 0x6c, 0x65, 0x73, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x12, 0x6a, 0x0a, 0x1e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x5f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63,
	0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x56, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x48, 0x00, 0x52, 0x1b, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x56, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x12, 0x6f, 0x0a, 0x21, 0x66, 0x69, 0x5f, 0x63, 0x6f, 0x69, 0x6e, 0x73, 0x5f, 0x74, 0x6f, 0x5f,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x6d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x61,
	0x73, 0x70, 0x65, 0x72, 0x2e, 0x46, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x54, 0x6f, 0x50, 0x6f,
	0x69, 0x6e, 0x74, 0x73, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x48, 0x00, 0x52, 0x1c, 0x66, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x54, 0x6f, 0x50, 0x6f,
	0x69, 0x6e, 0x74, 0x73, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x42, 0x1e, 0x0a, 0x1c, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f,
	0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x22, 0x3f, 0x0a, 0x15, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x26, 0x0a, 0x0f, 0x67, 0x69,
	0x66, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0d, 0x67, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x22, 0x3d, 0x0a, 0x14, 0x43, 0x68, 0x61, 0x72, 0x69, 0x74, 0x79, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x68,
	0x61, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0d, 0x63, 0x68, 0x61, 0x72, 0x69, 0x74, 0x79, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x22, 0x43, 0x0a, 0x20, 0x50, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x4d, 0x65, 0x72,
	0x63, 0x68, 0x61, 0x6e, 0x64, 0x69, 0x73, 0x65, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x5f, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63,
	0x68, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x5c, 0x0a, 0x13, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x24,
	0x0a, 0x0e, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x76, 0x32,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x43, 0x6f,
	0x64, 0x65, 0x56, 0x32, 0x22, 0x4b, 0x0a, 0x22, 0x54, 0x68, 0x72, 0x69, 0x77, 0x65, 0x42, 0x65,
	0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x72,
	0x6c, 0x22, 0xa4, 0x03, 0x0a, 0x1c, 0x56, 0x69, 0x73, 0x74, 0x61, 0x72, 0x61, 0x41, 0x69, 0x72,
	0x4d, 0x69, 0x6c, 0x65, 0x73, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x12, 0x50, 0x0a, 0x26, 0x66, 0x69, 0x5f, 0x63, 0x6f, 0x69, 0x6e, 0x73, 0x5f, 0x74,
	0x6f, 0x5f, 0x63, 0x76, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x20, 0x66, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x54, 0x6f, 0x43, 0x76, 0x50,
	0x6f, 0x69, 0x6e, 0x74, 0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x61, 0x74, 0x69, 0x6f, 0x12, 0x37, 0x0a, 0x18, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x66, 0x69, 0x5f, 0x63, 0x6f, 0x69, 0x6e, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x15, 0x6d, 0x69, 0x6e, 0x43, 0x6f, 0x6e, 0x76, 0x65,
	0x72, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x12, 0x37, 0x0a,
	0x18, 0x6d, 0x61, 0x78, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x61, 0x62, 0x6c, 0x65,
	0x5f, 0x66, 0x69, 0x5f, 0x63, 0x6f, 0x69, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x15, 0x6d, 0x61, 0x78, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x46,
	0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x12, 0x55, 0x0a, 0x10, 0x63, 0x76, 0x5f, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x73, 0x5f, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2b, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x56, 0x69, 0x73, 0x74, 0x61, 0x72,
	0x61, 0x41, 0x69, 0x72, 0x4d, 0x69, 0x6c, 0x65, 0x73, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x52, 0x0e, 0x63,
	0x76, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x1a, 0x69, 0x0a,
	0x06, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x0c, 0x6c, 0x65, 0x66, 0x74, 0x5f,
	0x69, 0x6d, 0x67, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6c,
	0x65, 0x66, 0x74, 0x49, 0x6d, 0x67, 0x55, 0x72, 0x6c, 0x12, 0x22, 0x0a, 0x0d, 0x72, 0x69, 0x67,
	0x68, 0x74, 0x5f, 0x69, 0x6d, 0x67, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x72, 0x69, 0x67, 0x68, 0x74, 0x49, 0x6d, 0x67, 0x55, 0x72, 0x6c, 0x12, 0x19, 0x0a,
	0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x22, 0x89, 0x04, 0x0a, 0x1c, 0x46, 0x69, 0x43,
	0x6f, 0x69, 0x6e, 0x73, 0x54, 0x6f, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x55, 0x0a, 0x28, 0x66, 0x69, 0x5f,
	0x63, 0x6f, 0x69, 0x6e, 0x73, 0x5f, 0x74, 0x6f, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x5f,
	0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x70, 0x6c, 0x69, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x23, 0x66, 0x69, 0x43,
	0x6f, 0x69, 0x6e, 0x73, 0x54, 0x6f, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x43, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x69, 0x65, 0x72,
	0x12, 0x40, 0x0a, 0x1d, 0x66, 0x69, 0x5f, 0x63, 0x6f, 0x69, 0x6e, 0x73, 0x5f, 0x73, 0x74, 0x65,
	0x70, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x69, 0x65,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x19, 0x66, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73,
	0x53, 0x74, 0x65, 0x70, 0x53, 0x69, 0x7a, 0x65, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x69,
	0x65, 0x72, 0x12, 0x37, 0x0a, 0x18, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x66, 0x69, 0x5f, 0x63, 0x6f, 0x69, 0x6e, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x15, 0x6d, 0x69, 0x6e, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74,
	0x61, 0x62, 0x6c, 0x65, 0x46, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x12, 0x37, 0x0a, 0x18, 0x6d,
	0x61, 0x78, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x66,
	0x69, 0x5f, 0x63, 0x6f, 0x69, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x15, 0x6d,
	0x61, 0x78, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x69, 0x43,
	0x6f, 0x69, 0x6e, 0x73, 0x12, 0x50, 0x0a, 0x0d, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x5f, 0x62,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x63, 0x61,
	0x73, 0x70, 0x65, 0x72, 0x2e, 0x46, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x54, 0x6f, 0x50, 0x6f,
	0x69, 0x6e, 0x74, 0x73, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x2e, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x52, 0x0c, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73,
	0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x1a, 0x8b, 0x01, 0x0a, 0x06, 0x42, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0c, 0x6c, 0x65, 0x66, 0x74, 0x5f, 0x69, 0x6d, 0x67, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6c, 0x65, 0x66, 0x74, 0x49,
	0x6d, 0x67, 0x55, 0x72, 0x6c, 0x12, 0x22, 0x0a, 0x0d, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x69,
	0x6d, 0x67, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x69,
	0x67, 0x68, 0x74, 0x49, 0x6d, 0x67, 0x55, 0x72, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x67, 0x5f,
	0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x67, 0x43,
	0x6f, 0x6c, 0x6f, 0x72, 0x22, 0x5e, 0x0a, 0x1b, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x61, 0x73, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x24,
	0x0a, 0x0e, 0x77, 0x65, 0x62, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x77, 0x65, 0x62, 0x50, 0x61, 0x67, 0x65, 0x54,
	0x69, 0x74, 0x6c, 0x65, 0x22, 0xbc, 0x01, 0x0a, 0x0f, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x64, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x65, 0x64, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x0f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x64, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x12, 0x36, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x61, 0x73, 0x70,
	0x65, 0x72, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x0b,
	0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x46, 0x0a, 0x11, 0x64,
	0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x0f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x6e, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x22, 0x43, 0x0a, 0x1b, 0x52, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x12, 0x24, 0x0a, 0x0e, 0x63, 0x6f, 0x6f, 0x6c, 0x5f, 0x6f, 0x66, 0x66, 0x5f, 0x68,
	0x6f, 0x75, 0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x63, 0x6f, 0x6f, 0x6c,
	0x4f, 0x66, 0x66, 0x48, 0x6f, 0x75, 0x72, 0x73, 0x22, 0xc6, 0x01, 0x0a, 0x14, 0x50, 0x6f, 0x77,
	0x65, 0x72, 0x55, 0x70, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x12, 0x68, 0x0a, 0x1e, 0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x72, 0x61, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x61, 0x73, 0x70,
	0x65, 0x72, 0x2e, 0x52, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x74,
	0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x1b,
	0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x44, 0x0a, 0x0e, 0x65,
	0x78, 0x74, 0x72, 0x61, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x4b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x52, 0x0d, 0x65, 0x78, 0x74, 0x72, 0x61, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x22, 0x32, 0x0a, 0x1a, 0x49, 0x6e, 0x48, 0x6f, 0x75, 0x73, 0x65, 0x56, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12,
	0x14, 0x0a, 0x05, 0x73, 0x6b, 0x75, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x73, 0x6b, 0x75, 0x49, 0x64, 0x22, 0x6d, 0x0a, 0x1c, 0x46, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73,
	0x54, 0x6f, 0x43, 0x61, 0x73, 0x68, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x52, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x24, 0x0a, 0x0e, 0x66, 0x69, 0x5f, 0x63, 0x6f, 0x69, 0x6e,
	0x73, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x66,
	0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x63,
	0x61, 0x73, 0x68, 0x5f, 0x65, 0x71, 0x75, 0x69, 0x76, 0x61, 0x6c, 0x65, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x63, 0x61, 0x73, 0x68, 0x45, 0x71, 0x75, 0x69, 0x76, 0x61,
	0x6c, 0x65, 0x6e, 0x74, 0x22, 0x75, 0x0a, 0x0a, 0x57, 0x61, 0x74, 0x73, 0x6f, 0x6e, 0x4d, 0x65,
	0x74, 0x61, 0x12, 0x3b, 0x0a, 0x1a, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x5f, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x5f, 0x77, 0x61, 0x74, 0x73, 0x6f, 0x6e, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x57, 0x61, 0x74, 0x73, 0x6f, 0x6e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12,
	0x2a, 0x0a, 0x11, 0x69, 0x73, 0x73, 0x75, 0x65, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x69, 0x73, 0x73, 0x75,
	0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x2a, 0x90, 0x02, 0x0a, 0x09,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x47, 0x49, 0x46, 0x54, 0x5f, 0x43, 0x41,
	0x52, 0x44, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x4f, 0x55, 0x50, 0x4f, 0x4e, 0x10, 0x02,
	0x12, 0x10, 0x0a, 0x0c, 0x53, 0x55, 0x42, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x48, 0x41, 0x52, 0x49, 0x54, 0x59, 0x10, 0x04, 0x12,
	0x18, 0x0a, 0x14, 0x50, 0x48, 0x59, 0x53, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x4d, 0x45, 0x52, 0x43,
	0x48, 0x41, 0x4e, 0x44, 0x49, 0x53, 0x45, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x50, 0x4f, 0x57,
	0x45, 0x52, 0x5f, 0x55, 0x50, 0x10, 0x06, 0x12, 0x1b, 0x0a, 0x17, 0x54, 0x48, 0x52, 0x49, 0x57,
	0x45, 0x5f, 0x42, 0x45, 0x4e, 0x45, 0x46, 0x49, 0x54, 0x53, 0x5f, 0x50, 0x41, 0x43, 0x4b, 0x41,
	0x47, 0x45, 0x10, 0x07, 0x12, 0x15, 0x0a, 0x11, 0x56, 0x49, 0x53, 0x54, 0x41, 0x52, 0x41, 0x5f,
	0x41, 0x49, 0x52, 0x5f, 0x4d, 0x49, 0x4c, 0x45, 0x53, 0x10, 0x08, 0x12, 0x0e, 0x0a, 0x0a, 0x43,
	0x4d, 0x53, 0x5f, 0x43, 0x4f, 0x55, 0x50, 0x4f, 0x4e, 0x10, 0x09, 0x12, 0x11, 0x0a, 0x0d, 0x4c,
	0x4f, 0x55, 0x4e, 0x47, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x0a, 0x12, 0x13,
	0x0a, 0x0f, 0x45, 0x58, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f,
	0x52, 0x10, 0x0b, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x4c, 0x55, 0x42, 0x5f, 0x49, 0x54, 0x43, 0x5f,
	0x47, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x53, 0x10, 0x0c, 0x2a, 0x73,
	0x0a, 0x13, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x1b, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x5f, 0x52, 0x45, 0x44, 0x45, 0x4d, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x4d, 0x4f, 0x44, 0x45, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x46, 0x49, 0x5f, 0x43, 0x4f, 0x49,
	0x4e, 0x53, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x46, 0x49, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10,
	0x02, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x03, 0x12,
	0x12, 0x0a, 0x0e, 0x46, 0x49, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52,
	0x44, 0x10, 0x04, 0x2a, 0xe5, 0x04, 0x0a, 0x0b, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x56, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x4c, 0x4f, 0x59, 0x4c, 0x54, 0x59, 0x10, 0x01,
	0x12, 0x0e, 0x0a, 0x0a, 0x51, 0x57, 0x49, 0x4b, 0x43, 0x49, 0x4c, 0x56, 0x45, 0x52, 0x10, 0x02,
	0x12, 0x18, 0x0a, 0x14, 0x4e, 0x41, 0x41, 0x47, 0x49, 0x4e, 0x5f, 0x53, 0x41, 0x55, 0x43, 0x45,
	0x5f, 0x4f, 0x46, 0x46, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x0a, 0x12, 0x1b, 0x0a, 0x17, 0x4d, 0x4f,
	0x4e, 0x53, 0x4f, 0x4f, 0x4e, 0x5f, 0x48, 0x41, 0x52, 0x56, 0x45, 0x53, 0x54, 0x5f, 0x4f, 0x46,
	0x46, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x0b, 0x12, 0x16, 0x0a, 0x12, 0x42, 0x4c, 0x55, 0x45, 0x5f,
	0x54, 0x4f, 0x4b, 0x41, 0x49, 0x5f, 0x4f, 0x46, 0x46, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x0c, 0x12,
	0x11, 0x0a, 0x0d, 0x44, 0x48, 0x4f, 0x52, 0x41, 0x5f, 0x4f, 0x46, 0x46, 0x4c, 0x49, 0x4e, 0x45,
	0x10, 0x0d, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x4f, 0x43, 0x4b, 0x5f, 0x53, 0x4f, 0x48, 0x4f, 0x5f,
	0x4f, 0x46, 0x46, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x0e, 0x12, 0x19, 0x0a, 0x15, 0x54, 0x52, 0x55,
	0x45, 0x5f, 0x45, 0x4c, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x53, 0x5f, 0x4f, 0x46, 0x46, 0x4c, 0x49,
	0x4e, 0x45, 0x10, 0x0f, 0x12, 0x13, 0x0a, 0x0f, 0x57, 0x45, 0x5f, 0x57, 0x4f, 0x52, 0x4b, 0x5f,
	0x4f, 0x46, 0x46, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x10, 0x12, 0x15, 0x0a, 0x11, 0x54, 0x45, 0x41,
	0x5f, 0x54, 0x52, 0x55, 0x4e, 0x4b, 0x5f, 0x4f, 0x46, 0x46, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x11,
	0x12, 0x17, 0x0a, 0x13, 0x4c, 0x49, 0x56, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x4f, 0x4f, 0x44, 0x5f,
	0x4f, 0x46, 0x46, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x12, 0x12, 0x17, 0x0a, 0x13, 0x57, 0x48, 0x4f,
	0x4c, 0x45, 0x5f, 0x54, 0x52, 0x55, 0x54, 0x48, 0x5f, 0x4f, 0x46, 0x46, 0x4c, 0x49, 0x4e, 0x45,
	0x10, 0x13, 0x12, 0x12, 0x0a, 0x0e, 0x42, 0x4f, 0x4e, 0x4f, 0x4d, 0x49, 0x5f, 0x4f, 0x46, 0x46,
	0x4c, 0x49, 0x4e, 0x45, 0x10, 0x14, 0x12, 0x18, 0x0a, 0x14, 0x57, 0x48, 0x49, 0x54, 0x45, 0x5f,
	0x57, 0x49, 0x4c, 0x4c, 0x4f, 0x57, 0x5f, 0x4f, 0x46, 0x46, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x15,
	0x12, 0x17, 0x0a, 0x13, 0x4d, 0x41, 0x4e, 0x5f, 0x4d, 0x41, 0x54, 0x54, 0x45, 0x52, 0x53, 0x5f,
	0x4f, 0x46, 0x46, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x16, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x48, 0x41,
	0x49, 0x4b, 0x41, 0x5f, 0x4f, 0x46, 0x46, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x17, 0x12, 0x15, 0x0a,
	0x11, 0x42, 0x4f, 0x4c, 0x44, 0x5f, 0x43, 0x41, 0x52, 0x45, 0x5f, 0x4f, 0x46, 0x46, 0x4c, 0x49,
	0x4e, 0x45, 0x10, 0x18, 0x12, 0x13, 0x0a, 0x0f, 0x42, 0x49, 0x4c, 0x49, 0x5f, 0x48, 0x55, 0x5f,
	0x4f, 0x46, 0x46, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x19, 0x12, 0x12, 0x0a, 0x0e, 0x48, 0x55, 0x4d,
	0x42, 0x4c, 0x45, 0x5f, 0x4f, 0x46, 0x46, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x1a, 0x12, 0x14, 0x0a,
	0x10, 0x42, 0x49, 0x47, 0x53, 0x4d, 0x41, 0x4c, 0x4c, 0x5f, 0x4f, 0x46, 0x46, 0x4c, 0x49, 0x4e,
	0x45, 0x10, 0x1b, 0x12, 0x1a, 0x0a, 0x16, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x49, 0x43, 0x5f, 0x4f,
	0x46, 0x46, 0x4c, 0x49, 0x4e, 0x45, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0x1c, 0x12,
	0x10, 0x0a, 0x0c, 0x4e, 0x4f, 0x5f, 0x4f, 0x50, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10,
	0x1d, 0x12, 0x0a, 0x0a, 0x06, 0x54, 0x48, 0x52, 0x49, 0x57, 0x45, 0x10, 0x1e, 0x12, 0x0b, 0x0a,
	0x07, 0x56, 0x49, 0x53, 0x54, 0x41, 0x52, 0x41, 0x10, 0x1f, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e,
	0x5f, 0x48, 0x4f, 0x55, 0x53, 0x45, 0x10, 0x20, 0x12, 0x0e, 0x0a, 0x0a, 0x44, 0x52, 0x45, 0x41,
	0x4d, 0x46, 0x4f, 0x4c, 0x4b, 0x53, 0x10, 0x21, 0x12, 0x0a, 0x0a, 0x06, 0x44, 0x50, 0x41, 0x4e,
	0x44, 0x41, 0x10, 0x22, 0x12, 0x0c, 0x0a, 0x08, 0x50, 0x4f, 0x53, 0x48, 0x56, 0x49, 0x4e, 0x45,
	0x10, 0x23, 0x12, 0x07, 0x0a, 0x03, 0x49, 0x54, 0x43, 0x10, 0x24, 0x2a, 0x5f, 0x0a, 0x09, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x42, 0x41, 0x43, 0x4b, 0x47, 0x52, 0x4f, 0x55,
	0x4e, 0x44, 0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x42, 0x52,
	0x41, 0x4e, 0x44, 0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x50,
	0x52, 0x4f, 0x4d, 0x4f, 0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x10, 0x03, 0x2a, 0x86, 0x01, 0x0a,
	0x0d, 0x43, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f,
	0x0a, 0x1b, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x19, 0x0a, 0x15, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x45, 0x50, 0x49, 0x46, 0x49, 0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x41,
	0x52, 0x54, 0x4e, 0x45, 0x52, 0x53, 0x48, 0x49, 0x50, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x56,
	0x49, 0x53, 0x41, 0x10, 0x03, 0x42, 0x46, 0x0a, 0x21, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x5a, 0x21, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_casper_offer_catalog_proto_rawDescOnce sync.Once
	file_api_casper_offer_catalog_proto_rawDescData = file_api_casper_offer_catalog_proto_rawDesc
)

func file_api_casper_offer_catalog_proto_rawDescGZIP() []byte {
	file_api_casper_offer_catalog_proto_rawDescOnce.Do(func() {
		file_api_casper_offer_catalog_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_casper_offer_catalog_proto_rawDescData)
	})
	return file_api_casper_offer_catalog_proto_rawDescData
}

var file_api_casper_offer_catalog_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_api_casper_offer_catalog_proto_msgTypes = make([]protoimpl.MessageInfo, 32)
var file_api_casper_offer_catalog_proto_goTypes = []interface{}{
	(OfferType)(0),           // 0: casper.OfferType
	(OfferRedemptionMode)(0), // 1: casper.OfferRedemptionMode
	(OfferVendor)(0),         // 2: casper.OfferVendor
	(ImageType)(0),           // 3: casper.ImageType
	(CardOfferType)(0),       // 4: casper.CardOfferType
	(OfferAdditionalDetails_NotificationType)(0),            // 5: casper.OfferAdditionalDetails.NotificationType
	(*OfferImage)(nil),                                      // 6: casper.OfferImage
	(*OfferTnc)(nil),                                        // 7: casper.OfferTnc
	(*OfferAdditionalDetails)(nil),                          // 8: casper.OfferAdditionalDetails
	(*VendorOfferMetadata)(nil),                             // 9: casper.VendorOfferMetadata
	(*LoyltyVendorOfferMetadata)(nil),                       // 10: casper.LoyltyVendorOfferMetadata
	(*QwikcilverVendorOfferMetadata)(nil),                   // 11: casper.QwikcilverVendorOfferMetadata
	(*OfflineVendorOfferMetadata)(nil),                      // 12: casper.OfflineVendorOfferMetadata
	(*ThriweVendorOfferMetadata)(nil),                       // 13: casper.ThriweVendorOfferMetadata
	(*Offer)(nil),                                           // 14: casper.Offer
	(*OfferMetadata)(nil),                                   // 15: casper.OfferMetadata
	(*GiftCardOfferMetadata)(nil),                           // 16: casper.GiftCardOfferMetadata
	(*CharityOfferMetadata)(nil),                            // 17: casper.CharityOfferMetadata
	(*PhysicalMerchandiseOfferMetadata)(nil),                // 18: casper.PhysicalMerchandiseOfferMetadata
	(*CouponOfferMetadata)(nil),                             // 19: casper.CouponOfferMetadata
	(*ThriweBenefitsPackageOfferMetadata)(nil),              // 20: casper.ThriweBenefitsPackageOfferMetadata
	(*VistaraAirMilesOfferMetadata)(nil),                    // 21: casper.VistaraAirMilesOfferMetadata
	(*FiCoinsToPointsOfferMetadata)(nil),                    // 22: casper.FiCoinsToPointsOfferMetadata
	(*ExternalVendorOfferMetadata)(nil),                     // 23: casper.ExternalVendorOfferMetadata
	(*DiscountDetails)(nil),                                 // 24: casper.DiscountDetails
	(*RedemptionRateLimitMetadata)(nil),                     // 25: casper.RedemptionRateLimitMetadata
	(*PowerUpOfferMetadata)(nil),                            // 26: casper.PowerUpOfferMetadata
	(*InHouseVendorOfferMetadata)(nil),                      // 27: casper.InHouseVendorOfferMetadata
	(*FiCoinsToCashConversionRatio)(nil),                    // 28: casper.FiCoinsToCashConversionRatio
	(*WatsonMeta)(nil),                                      // 29: casper.WatsonMeta
	nil,                                                     // 30: casper.OfferAdditionalDetails.ScreenDisplayRankMapEntry
	(*OfferAdditionalDetails_OfferDescription)(nil),         // 31: casper.OfferAdditionalDetails.OfferDescription
	(*OfferAdditionalDetails_DisplayTag)(nil),               // 32: casper.OfferAdditionalDetails.DisplayTag
	(*OfferAdditionalDetails_MessageConfig)(nil),            // 33: casper.OfferAdditionalDetails.MessageConfig
	(*OfferAdditionalDetails_ExpiryNotificationConfig)(nil), // 34: casper.OfferAdditionalDetails.ExpiryNotificationConfig
	(*LoyltyVendorOfferMetadata_CharityOfferMetadata)(nil),  // 35: casper.LoyltyVendorOfferMetadata.CharityOfferMetadata
	(*VistaraAirMilesOfferMetadata_Banner)(nil),             // 36: casper.VistaraAirMilesOfferMetadata.Banner
	(*FiCoinsToPointsOfferMetadata_Banner)(nil),             // 37: casper.FiCoinsToPointsOfferMetadata.Banner
	(*common.Text)(nil),                                     // 38: api.typesv2.common.Text
	(common.ImageContentType)(0),                            // 39: api.typesv2.common.ImageContentType
	(common.BooleanEnum)(0),                                 // 40: api.typesv2.common.BooleanEnum
	(*timestamppb.Timestamp)(nil),                           // 41: google.protobuf.Timestamp
	(*TagsInfo)(nil),                                        // 42: casper.TagsInfo
	(discounts.DisplayMode)(0),                              // 43: casper.DisplayMode
	(*OfferKeyValueMetadata)(nil),                           // 44: casper.OfferKeyValueMetadata
	(*deeplink.Deeplink)(nil),                               // 45: frontend.deeplink.Deeplink
}
var file_api_casper_offer_catalog_proto_depIdxs = []int32{
	3,  // 0: casper.OfferImage.image_type:type_name -> casper.ImageType
	38, // 1: casper.OfferAdditionalDetails.salary_account_display_tag:type_name -> api.typesv2.common.Text
	39, // 2: casper.OfferAdditionalDetails.tile_image_content_type:type_name -> api.typesv2.common.ImageContentType
	30, // 3: casper.OfferAdditionalDetails.screen_display_rank_map:type_name -> casper.OfferAdditionalDetails.ScreenDisplayRankMapEntry
	31, // 4: casper.OfferAdditionalDetails.offer_description:type_name -> casper.OfferAdditionalDetails.OfferDescription
	32, // 5: casper.OfferAdditionalDetails.display_tags:type_name -> casper.OfferAdditionalDetails.DisplayTag
	4,  // 6: casper.OfferAdditionalDetails.card_offer_type:type_name -> casper.CardOfferType
	28, // 7: casper.OfferAdditionalDetails.conversion_ratio:type_name -> casper.FiCoinsToCashConversionRatio
	34, // 8: casper.OfferAdditionalDetails.expiry_notification_configs:type_name -> casper.OfferAdditionalDetails.ExpiryNotificationConfig
	29, // 9: casper.OfferAdditionalDetails.watson_meta:type_name -> casper.WatsonMeta
	40, // 10: casper.OfferAdditionalDetails.is_fi_points:type_name -> api.typesv2.common.BooleanEnum
	10, // 11: casper.VendorOfferMetadata.loylty_vendor_offer_metadata:type_name -> casper.LoyltyVendorOfferMetadata
	12, // 12: casper.VendorOfferMetadata.offline_vendor_offer_metadata:type_name -> casper.OfflineVendorOfferMetadata
	11, // 13: casper.VendorOfferMetadata.qwikcilver_vendor_offer_metadata:type_name -> casper.QwikcilverVendorOfferMetadata
	13, // 14: casper.VendorOfferMetadata.thriwe_vendor_offer_metadata:type_name -> casper.ThriweVendorOfferMetadata
	27, // 15: casper.VendorOfferMetadata.in_house_vendor_offer_metadata:type_name -> casper.InHouseVendorOfferMetadata
	35, // 16: casper.LoyltyVendorOfferMetadata.charity_offer_metadata:type_name -> casper.LoyltyVendorOfferMetadata.CharityOfferMetadata
	41, // 17: casper.ThriweVendorOfferMetadata.expiry_date:type_name -> google.protobuf.Timestamp
	0,  // 18: casper.Offer.offer_type:type_name -> casper.OfferType
	1,  // 19: casper.Offer.redemption_mode:type_name -> casper.OfferRedemptionMode
	2,  // 20: casper.Offer.vendor_name:type_name -> casper.OfferVendor
	9,  // 21: casper.Offer.vendor_offer_metadata:type_name -> casper.VendorOfferMetadata
	15, // 22: casper.Offer.offer_metadata:type_name -> casper.OfferMetadata
	6,  // 23: casper.Offer.images:type_name -> casper.OfferImage
	7,  // 24: casper.Offer.tnc:type_name -> casper.OfferTnc
	8,  // 25: casper.Offer.additional_details:type_name -> casper.OfferAdditionalDetails
	41, // 26: casper.Offer.created_at:type_name -> google.protobuf.Timestamp
	41, // 27: casper.Offer.updated_at:type_name -> google.protobuf.Timestamp
	24, // 28: casper.Offer.discount_details:type_name -> casper.DiscountDetails
	42, // 29: casper.Offer.tags_info:type_name -> casper.TagsInfo
	16, // 30: casper.OfferMetadata.gift_card_metadata:type_name -> casper.GiftCardOfferMetadata
	17, // 31: casper.OfferMetadata.charity_metadata:type_name -> casper.CharityOfferMetadata
	19, // 32: casper.OfferMetadata.coupon_metadata:type_name -> casper.CouponOfferMetadata
	18, // 33: casper.OfferMetadata.physical_merch_metadata:type_name -> casper.PhysicalMerchandiseOfferMetadata
	26, // 34: casper.OfferMetadata.power_up_offer_metadata:type_name -> casper.PowerUpOfferMetadata
	20, // 35: casper.OfferMetadata.thriwe_benefits_package_offer_metadata:type_name -> casper.ThriweBenefitsPackageOfferMetadata
	21, // 36: casper.OfferMetadata.vistara_air_miles_offer_metadata:type_name -> casper.VistaraAirMilesOfferMetadata
	23, // 37: casper.OfferMetadata.external_vendor_offer_metadata:type_name -> casper.ExternalVendorOfferMetadata
	22, // 38: casper.OfferMetadata.fi_coins_to_points_offer_metadata:type_name -> casper.FiCoinsToPointsOfferMetadata
	36, // 39: casper.VistaraAirMilesOfferMetadata.cv_points_banner:type_name -> casper.VistaraAirMilesOfferMetadata.Banner
	37, // 40: casper.FiCoinsToPointsOfferMetadata.points_banner:type_name -> casper.FiCoinsToPointsOfferMetadata.Banner
	43, // 41: casper.DiscountDetails.display_mode:type_name -> casper.DisplayMode
	41, // 42: casper.DiscountDetails.discount_end_time:type_name -> google.protobuf.Timestamp
	25, // 43: casper.PowerUpOfferMetadata.redemption_rate_limit_metadata:type_name -> casper.RedemptionRateLimitMetadata
	44, // 44: casper.PowerUpOfferMetadata.extra_metadata:type_name -> casper.OfferKeyValueMetadata
	45, // 45: casper.OfferAdditionalDetails.MessageConfig.deeplink:type_name -> frontend.deeplink.Deeplink
	33, // 46: casper.OfferAdditionalDetails.ExpiryNotificationConfig.message_config:type_name -> casper.OfferAdditionalDetails.MessageConfig
	5,  // 47: casper.OfferAdditionalDetails.ExpiryNotificationConfig.notification_type:type_name -> casper.OfferAdditionalDetails.NotificationType
	48, // [48:48] is the sub-list for method output_type
	48, // [48:48] is the sub-list for method input_type
	48, // [48:48] is the sub-list for extension type_name
	48, // [48:48] is the sub-list for extension extendee
	0,  // [0:48] is the sub-list for field type_name
}

func init() { file_api_casper_offer_catalog_proto_init() }
func file_api_casper_offer_catalog_proto_init() {
	if File_api_casper_offer_catalog_proto != nil {
		return
	}
	file_api_casper_offer_type_specific_details_proto_init()
	file_api_casper_tag_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_casper_offer_catalog_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OfferImage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_offer_catalog_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OfferTnc); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_offer_catalog_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OfferAdditionalDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_offer_catalog_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VendorOfferMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_offer_catalog_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoyltyVendorOfferMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_offer_catalog_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QwikcilverVendorOfferMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_offer_catalog_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OfflineVendorOfferMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_offer_catalog_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThriweVendorOfferMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_offer_catalog_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Offer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_offer_catalog_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OfferMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_offer_catalog_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GiftCardOfferMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_offer_catalog_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CharityOfferMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_offer_catalog_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PhysicalMerchandiseOfferMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_offer_catalog_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CouponOfferMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_offer_catalog_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThriweBenefitsPackageOfferMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_offer_catalog_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VistaraAirMilesOfferMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_offer_catalog_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FiCoinsToPointsOfferMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_offer_catalog_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExternalVendorOfferMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_offer_catalog_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DiscountDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_offer_catalog_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RedemptionRateLimitMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_offer_catalog_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PowerUpOfferMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_offer_catalog_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InHouseVendorOfferMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_offer_catalog_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FiCoinsToCashConversionRatio); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_offer_catalog_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WatsonMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_offer_catalog_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OfferAdditionalDetails_OfferDescription); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_offer_catalog_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OfferAdditionalDetails_DisplayTag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_offer_catalog_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OfferAdditionalDetails_MessageConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_offer_catalog_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OfferAdditionalDetails_ExpiryNotificationConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_offer_catalog_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoyltyVendorOfferMetadata_CharityOfferMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_offer_catalog_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VistaraAirMilesOfferMetadata_Banner); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_offer_catalog_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FiCoinsToPointsOfferMetadata_Banner); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_casper_offer_catalog_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*VendorOfferMetadata_LoyltyVendorOfferMetadata)(nil),
		(*VendorOfferMetadata_OfflineVendorOfferMetadata)(nil),
		(*VendorOfferMetadata_QwikcilverVendorOfferMetadata)(nil),
		(*VendorOfferMetadata_ThriweVendorOfferMetadata)(nil),
		(*VendorOfferMetadata_InHouseVendorOfferMetadata)(nil),
	}
	file_api_casper_offer_catalog_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*LoyltyVendorOfferMetadata_CharityOfferMetadata_)(nil),
	}
	file_api_casper_offer_catalog_proto_msgTypes[9].OneofWrappers = []interface{}{
		(*OfferMetadata_GiftCardMetadata)(nil),
		(*OfferMetadata_CharityMetadata)(nil),
		(*OfferMetadata_CouponMetadata)(nil),
		(*OfferMetadata_PhysicalMerchMetadata)(nil),
		(*OfferMetadata_PowerUpOfferMetadata)(nil),
		(*OfferMetadata_ThriweBenefitsPackageOfferMetadata)(nil),
		(*OfferMetadata_VistaraAirMilesOfferMetadata)(nil),
		(*OfferMetadata_ExternalVendorOfferMetadata)(nil),
		(*OfferMetadata_FiCoinsToPointsOfferMetadata)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_casper_offer_catalog_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   32,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_casper_offer_catalog_proto_goTypes,
		DependencyIndexes: file_api_casper_offer_catalog_proto_depIdxs,
		EnumInfos:         file_api_casper_offer_catalog_proto_enumTypes,
		MessageInfos:      file_api_casper_offer_catalog_proto_msgTypes,
	}.Build()
	File_api_casper_offer_catalog_proto = out.File
	file_api_casper_offer_catalog_proto_rawDesc = nil
	file_api_casper_offer_catalog_proto_goTypes = nil
	file_api_casper_offer_catalog_proto_depIdxs = nil
}
