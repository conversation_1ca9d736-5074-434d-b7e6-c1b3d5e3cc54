## Description

**'types.ui'** package is created to host standard UI components which can be used across the app

Any component added here should remain generic and follow standardization practices, there should not be any service specific component added here.

If there are plans on supporting a standardized component that requires specific UI treatment , please use enums/types that are standardized from the Design team.  
Eg: If design team has created a Banner, request them to standardize it and create 'BANNER_TYPE'. Use this 'BANNER_TYPE' in BE and client to differentiate between different 'banners'. 
This 'BANNER_TYPE' can either be a enum or a oneOf value.    

## Note to Client team

Any component here which you guys implement should be coded keeping in mind that you should be able to use it anywhere. 
If needed take as many fields as needed from BE to be able to render the content anywhere without making inherent screen specific assumptions like bg color, padding, corner radius etc.

## Cyclic Dependencies

To prevent cyclic dependencies when using this, following practices should be followed

- We **cannot** use '**types.ui'** dependencies in **'types'** and **'frontend.deeplink'** package.
- We **cannot** use **'frontend'** dependencies in **'types.ui'** package. An exception is made for supporting **'frontend.deeplink'** as its a different package. Thus, if needed you can import deeplinks in ui components
- Any dependency that can be present in both **'types.ui'** and **'frontend.deeplink'** has to be defined in the **'types'** package. For eg: '**types.Text**' or **types.Image**
