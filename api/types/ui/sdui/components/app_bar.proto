syntax = "proto3";

package types.ui.sdui.components;

option go_package = "github.com/epifi/gamma/api/types/ui/sdui/components";
option java_package = "com.github.epifi.gamma.api.types.ui.sdui.components";

// Note:
// AppBar is a component used to display the page's navigation bar.
// It serves as a container for other components like the back button, title, etc.
// Enums help define the alignment of components within the AppBar,
// simplifying the UI design from the backend.
// Doc: https://docs.google.com/document/d/18kiyYYCDqh2T-rUOxmjfgKTY1ReGjGw1Gna7iM0C3W8/edit?usp=sharing 
// Figma: https://www.figma.com/file/spRMTiw7iBKQI4JHsC4QNy/%F0%9F%A5%9D-Kiwi-2.0-(WIP)?type=design&node-id=5470-29582&mode=dev

enum AppBarAlignment {
  APP_BAR_ALIGNMENT_UNSPECIFIED = 0;
  APP_BAR_ALIGNMENT_LEFT = 1;
  APP_BAR_ALIGNMENT_RIGHT = 2;
  APP_BAR_ALIGNMENT_CENTER = 3;
}

enum AppBarTheme {
  APP_BAR_THEME_UNSPECIFIED = 0;
  APP_BAR_THEME_LIGHT = 1;
  APP_BAR_THEME_DARK = 2;
  APP_BAR_THEME_CUSTOM = 3;
}


