syntax = "proto3";

package types.deeplink_screen_option.info_view;

import "api/types/deeplink_screen_option/header.proto";
import "api/types/info/info_view.proto";

option go_package = "github.com/epifi/gamma/api/types/deeplink_screen_option/info_view";
option java_package = "com.github.epifi.gamma.api.types.deeplink_screen_option.info_view";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

// eg: https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=408-22399&mode=design&t=YkbGp6iMfUmYLSLx-4
message FullScreenInfoViewScreenOptions {
  ScreenOptionHeader header = 1;
  info.FullScreenInfoView full_screen_info_view = 2;
  // identifier for the screen, can be used by events for analytics
  string identifier = 3;
}
