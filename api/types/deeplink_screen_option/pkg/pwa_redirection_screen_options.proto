syntax = "proto3";

package types.deeplink_screen_option.pkg;

import "api/types/deeplink_screen_option/header.proto";
import "api/types/text.proto";
import "api/types/device.proto";
import "api/types/ui/icon_text_component.proto";

option go_package = "github.com/epifi/gamma/api/types/deeplink_screen_option/pkg";
option java_package = "com.github.epifi.gamma.api.types.deeplink_screen_option.pkg";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

// Denotes the screen_options for PWA_REDIRECTION_DEEPLINK screen.
message PwaRedirectionScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // pwa url where the user needs to be redirected to.
  string pwa_url = 2;
  // optional, sticky header section for the PWA flow.
  // figma : https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10651-39414&mode=design&t=3bi8APKxzszGoU4d-0
  HeaderSection pwa_header = 3;

  // used to let client know whether camera permission is required for any PWA,
  // can be set to false if not required
  // Deprecated in the favour of mandatory_device_permissions_info
  bool is_camera_permission_needed = 4 [deprecated = true];

  // denotes the info regarding mandatory device permissions that need to be taken from the user for continuing with the flow.
  DevicePermissionInfo mandatory_device_permissions_info = 5;

  message DevicePermissionInfo {
    // if the list is empty then it implies that no additional permissions are needed.
    repeated types.DevicePermission mandatory_permissions = 1;
    // denotes the title to be shown on permission dialog e.g. "Camera and Microphone Permissions required"
    types.Text permission_dialog_title = 2;
    // denotes the subtitle to be shown on permission dialog e.g. "Please grant the permissions to continue with the flow"
    types.Text permission_dialog_subtitle = 3;
  }

  // denotes the header section of the PWA flow.
  message HeaderSection {
    // denotes the title to be displayed in the header section e.g "Instant Loan"
    types.Text title = 1;
    // denotes the subtitle to be displayed in the header section.g "Powered by Moneyview"
    types.Text subtitle = 2;
    // denotes the background color for the header section.
    string bg_color = 3;
    // optional, left cta for the header section.
    types.ui.IconTextComponent left_cta = 4;
    // optional, right cta for the header section.
    types.ui.IconTextComponent right_cta = 5;
    // optional, denotes the style of header to be used,
    // if not passed then 'HEADER_STYLE_ROUNDED_BOTTOM_CORNERS' can be used as a default style.
    HeaderStyle header_style = 6;

    enum HeaderStyle {
      HEADER_STYLE_UNSPECIFIED = 0;
      // denotes the header would have rounded bottom corners.
      // figma : https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10651-39414&mode=design&t=HaJNCIwtunJUHdcu-0
      HEADER_STYLE_ROUNDED_BOTTOM_CORNERS = 1;
    }
  }
}
