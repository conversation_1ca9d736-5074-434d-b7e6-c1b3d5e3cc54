syntax = "proto3";

package actor;

import "api/queue/consumer_headers.proto";
import "api/typesv2/actor.proto";


option go_package = "github.com/epifi/gamma/api/actor/consumer";
option java_package = "com.github.epifi.gamma.api.actor.consumer";


message ActorCreationEvent {
  // request header
  queue.ConsumerRequestHeader request_header = 1;
  // created actor id
  string actor_id = 2;
  // type of actor created
  api.typesv2.ActorType type = 3;
}
