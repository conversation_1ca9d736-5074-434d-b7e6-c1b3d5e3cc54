// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/insights/consumer/service.proto

package consumer

import (
	context "context"
	external "github.com/epifi/gamma/api/connected_account/external"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Consumer_CreateOrUpdateGenerationStatus_FullMethodName = "/insights.consumer.Consumer/CreateOrUpdateGenerationStatus"
	Consumer_StoreGeneratedActorInsights_FullMethodName    = "/insights.consumer.Consumer/StoreGeneratedActorInsights"
	Consumer_DeliverInsights_FullMethodName                = "/insights.consumer.Consumer/DeliverInsights"
	Consumer_ProcessCaNewDataFetchEvent_FullMethodName     = "/insights.consumer.Consumer/ProcessCaNewDataFetchEvent"
	Consumer_DeleteCaAssetHistoriesEvent_FullMethodName    = "/insights.consumer.Consumer/DeleteCaAssetHistoriesEvent"
)

// ConsumerClient is the client API for Consumer service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ConsumerClient interface {
	// rpc to add and update the run_status of generation script run entry i.e started / completed / finished
	CreateOrUpdateGenerationStatus(ctx context.Context, in *CreateOrUpdateGenerationStatusRequest, opts ...grpc.CallOption) (*CreateOrUpdateGenerationStatusResponse, error)
	// rpc to store generated actor insights data in actor_insight table
	StoreGeneratedActorInsights(ctx context.Context, in *StoreGeneratedActorInsightsRequest, opts ...grpc.CallOption) (*StoreGeneratedActorInsightsResponse, error)
	// consumer rpc to generate actor insights and push/send to different destination (e.g. notification center)
	DeliverInsights(ctx context.Context, in *DeliverInsightsRequest, opts ...grpc.CallOption) (*DeliverInsightsResponse, error)
	// consumer rpc to process new data fetch event for connected accounts
	ProcessCaNewDataFetchEvent(ctx context.Context, in *external.AccountDataSyncEvent, opts ...grpc.CallOption) (*ProcessCaNewDataFetchEventResponse, error)
	// consumer rpc to delete asset histories if user deletes their connected accounts
	DeleteCaAssetHistoriesEvent(ctx context.Context, in *external.AccountUpdateEvent, opts ...grpc.CallOption) (*DeleteCaAssetHistoriesEventResponse, error)
}

type consumerClient struct {
	cc grpc.ClientConnInterface
}

func NewConsumerClient(cc grpc.ClientConnInterface) ConsumerClient {
	return &consumerClient{cc}
}

func (c *consumerClient) CreateOrUpdateGenerationStatus(ctx context.Context, in *CreateOrUpdateGenerationStatusRequest, opts ...grpc.CallOption) (*CreateOrUpdateGenerationStatusResponse, error) {
	out := new(CreateOrUpdateGenerationStatusResponse)
	err := c.cc.Invoke(ctx, Consumer_CreateOrUpdateGenerationStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consumerClient) StoreGeneratedActorInsights(ctx context.Context, in *StoreGeneratedActorInsightsRequest, opts ...grpc.CallOption) (*StoreGeneratedActorInsightsResponse, error) {
	out := new(StoreGeneratedActorInsightsResponse)
	err := c.cc.Invoke(ctx, Consumer_StoreGeneratedActorInsights_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consumerClient) DeliverInsights(ctx context.Context, in *DeliverInsightsRequest, opts ...grpc.CallOption) (*DeliverInsightsResponse, error) {
	out := new(DeliverInsightsResponse)
	err := c.cc.Invoke(ctx, Consumer_DeliverInsights_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consumerClient) ProcessCaNewDataFetchEvent(ctx context.Context, in *external.AccountDataSyncEvent, opts ...grpc.CallOption) (*ProcessCaNewDataFetchEventResponse, error) {
	out := new(ProcessCaNewDataFetchEventResponse)
	err := c.cc.Invoke(ctx, Consumer_ProcessCaNewDataFetchEvent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consumerClient) DeleteCaAssetHistoriesEvent(ctx context.Context, in *external.AccountUpdateEvent, opts ...grpc.CallOption) (*DeleteCaAssetHistoriesEventResponse, error) {
	out := new(DeleteCaAssetHistoriesEventResponse)
	err := c.cc.Invoke(ctx, Consumer_DeleteCaAssetHistoriesEvent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ConsumerServer is the server API for Consumer service.
// All implementations should embed UnimplementedConsumerServer
// for forward compatibility
type ConsumerServer interface {
	// rpc to add and update the run_status of generation script run entry i.e started / completed / finished
	CreateOrUpdateGenerationStatus(context.Context, *CreateOrUpdateGenerationStatusRequest) (*CreateOrUpdateGenerationStatusResponse, error)
	// rpc to store generated actor insights data in actor_insight table
	StoreGeneratedActorInsights(context.Context, *StoreGeneratedActorInsightsRequest) (*StoreGeneratedActorInsightsResponse, error)
	// consumer rpc to generate actor insights and push/send to different destination (e.g. notification center)
	DeliverInsights(context.Context, *DeliverInsightsRequest) (*DeliverInsightsResponse, error)
	// consumer rpc to process new data fetch event for connected accounts
	ProcessCaNewDataFetchEvent(context.Context, *external.AccountDataSyncEvent) (*ProcessCaNewDataFetchEventResponse, error)
	// consumer rpc to delete asset histories if user deletes their connected accounts
	DeleteCaAssetHistoriesEvent(context.Context, *external.AccountUpdateEvent) (*DeleteCaAssetHistoriesEventResponse, error)
}

// UnimplementedConsumerServer should be embedded to have forward compatible implementations.
type UnimplementedConsumerServer struct {
}

func (UnimplementedConsumerServer) CreateOrUpdateGenerationStatus(context.Context, *CreateOrUpdateGenerationStatusRequest) (*CreateOrUpdateGenerationStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateOrUpdateGenerationStatus not implemented")
}
func (UnimplementedConsumerServer) StoreGeneratedActorInsights(context.Context, *StoreGeneratedActorInsightsRequest) (*StoreGeneratedActorInsightsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StoreGeneratedActorInsights not implemented")
}
func (UnimplementedConsumerServer) DeliverInsights(context.Context, *DeliverInsightsRequest) (*DeliverInsightsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeliverInsights not implemented")
}
func (UnimplementedConsumerServer) ProcessCaNewDataFetchEvent(context.Context, *external.AccountDataSyncEvent) (*ProcessCaNewDataFetchEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessCaNewDataFetchEvent not implemented")
}
func (UnimplementedConsumerServer) DeleteCaAssetHistoriesEvent(context.Context, *external.AccountUpdateEvent) (*DeleteCaAssetHistoriesEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteCaAssetHistoriesEvent not implemented")
}

// UnsafeConsumerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ConsumerServer will
// result in compilation errors.
type UnsafeConsumerServer interface {
	mustEmbedUnimplementedConsumerServer()
}

func RegisterConsumerServer(s grpc.ServiceRegistrar, srv ConsumerServer) {
	s.RegisterService(&Consumer_ServiceDesc, srv)
}

func _Consumer_CreateOrUpdateGenerationStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrUpdateGenerationStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsumerServer).CreateOrUpdateGenerationStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Consumer_CreateOrUpdateGenerationStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsumerServer).CreateOrUpdateGenerationStatus(ctx, req.(*CreateOrUpdateGenerationStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Consumer_StoreGeneratedActorInsights_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StoreGeneratedActorInsightsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsumerServer).StoreGeneratedActorInsights(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Consumer_StoreGeneratedActorInsights_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsumerServer).StoreGeneratedActorInsights(ctx, req.(*StoreGeneratedActorInsightsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Consumer_DeliverInsights_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeliverInsightsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsumerServer).DeliverInsights(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Consumer_DeliverInsights_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsumerServer).DeliverInsights(ctx, req.(*DeliverInsightsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Consumer_ProcessCaNewDataFetchEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(external.AccountDataSyncEvent)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsumerServer).ProcessCaNewDataFetchEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Consumer_ProcessCaNewDataFetchEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsumerServer).ProcessCaNewDataFetchEvent(ctx, req.(*external.AccountDataSyncEvent))
	}
	return interceptor(ctx, in, info, handler)
}

func _Consumer_DeleteCaAssetHistoriesEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(external.AccountUpdateEvent)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsumerServer).DeleteCaAssetHistoriesEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Consumer_DeleteCaAssetHistoriesEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsumerServer).DeleteCaAssetHistoriesEvent(ctx, req.(*external.AccountUpdateEvent))
	}
	return interceptor(ctx, in, info, handler)
}

// Consumer_ServiceDesc is the grpc.ServiceDesc for Consumer service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Consumer_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "insights.consumer.Consumer",
	HandlerType: (*ConsumerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateOrUpdateGenerationStatus",
			Handler:    _Consumer_CreateOrUpdateGenerationStatus_Handler,
		},
		{
			MethodName: "StoreGeneratedActorInsights",
			Handler:    _Consumer_StoreGeneratedActorInsights_Handler,
		},
		{
			MethodName: "DeliverInsights",
			Handler:    _Consumer_DeliverInsights_Handler,
		},
		{
			MethodName: "ProcessCaNewDataFetchEvent",
			Handler:    _Consumer_ProcessCaNewDataFetchEvent_Handler,
		},
		{
			MethodName: "DeleteCaAssetHistoriesEvent",
			Handler:    _Consumer_DeleteCaAssetHistoriesEvent_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/insights/consumer/service.proto",
}
