// Code generated by MockGen. DO NOT EDIT.
// Source: api/insights/consumer/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	external "github.com/epifi/gamma/api/connected_account/external"
	consumer "github.com/epifi/gamma/api/insights/consumer"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockConsumerClient is a mock of ConsumerClient interface.
type MockConsumerClient struct {
	ctrl     *gomock.Controller
	recorder *MockConsumerClientMockRecorder
}

// MockConsumerClientMockRecorder is the mock recorder for MockConsumerClient.
type MockConsumerClientMockRecorder struct {
	mock *MockConsumerClient
}

// NewMockConsumerClient creates a new mock instance.
func NewMockConsumerClient(ctrl *gomock.Controller) *MockConsumerClient {
	mock := &MockConsumerClient{ctrl: ctrl}
	mock.recorder = &MockConsumerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockConsumerClient) EXPECT() *MockConsumerClientMockRecorder {
	return m.recorder
}

// CreateOrUpdateGenerationStatus mocks base method.
func (m *MockConsumerClient) CreateOrUpdateGenerationStatus(ctx context.Context, in *consumer.CreateOrUpdateGenerationStatusRequest, opts ...grpc.CallOption) (*consumer.CreateOrUpdateGenerationStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateOrUpdateGenerationStatus", varargs...)
	ret0, _ := ret[0].(*consumer.CreateOrUpdateGenerationStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateOrUpdateGenerationStatus indicates an expected call of CreateOrUpdateGenerationStatus.
func (mr *MockConsumerClientMockRecorder) CreateOrUpdateGenerationStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrUpdateGenerationStatus", reflect.TypeOf((*MockConsumerClient)(nil).CreateOrUpdateGenerationStatus), varargs...)
}

// DeleteCaAssetHistoriesEvent mocks base method.
func (m *MockConsumerClient) DeleteCaAssetHistoriesEvent(ctx context.Context, in *external.AccountUpdateEvent, opts ...grpc.CallOption) (*consumer.DeleteCaAssetHistoriesEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteCaAssetHistoriesEvent", varargs...)
	ret0, _ := ret[0].(*consumer.DeleteCaAssetHistoriesEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteCaAssetHistoriesEvent indicates an expected call of DeleteCaAssetHistoriesEvent.
func (mr *MockConsumerClientMockRecorder) DeleteCaAssetHistoriesEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteCaAssetHistoriesEvent", reflect.TypeOf((*MockConsumerClient)(nil).DeleteCaAssetHistoriesEvent), varargs...)
}

// DeliverInsights mocks base method.
func (m *MockConsumerClient) DeliverInsights(ctx context.Context, in *consumer.DeliverInsightsRequest, opts ...grpc.CallOption) (*consumer.DeliverInsightsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeliverInsights", varargs...)
	ret0, _ := ret[0].(*consumer.DeliverInsightsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeliverInsights indicates an expected call of DeliverInsights.
func (mr *MockConsumerClientMockRecorder) DeliverInsights(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeliverInsights", reflect.TypeOf((*MockConsumerClient)(nil).DeliverInsights), varargs...)
}

// ProcessCaNewDataFetchEvent mocks base method.
func (m *MockConsumerClient) ProcessCaNewDataFetchEvent(ctx context.Context, in *external.AccountDataSyncEvent, opts ...grpc.CallOption) (*consumer.ProcessCaNewDataFetchEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessCaNewDataFetchEvent", varargs...)
	ret0, _ := ret[0].(*consumer.ProcessCaNewDataFetchEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCaNewDataFetchEvent indicates an expected call of ProcessCaNewDataFetchEvent.
func (mr *MockConsumerClientMockRecorder) ProcessCaNewDataFetchEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCaNewDataFetchEvent", reflect.TypeOf((*MockConsumerClient)(nil).ProcessCaNewDataFetchEvent), varargs...)
}

// StoreGeneratedActorInsights mocks base method.
func (m *MockConsumerClient) StoreGeneratedActorInsights(ctx context.Context, in *consumer.StoreGeneratedActorInsightsRequest, opts ...grpc.CallOption) (*consumer.StoreGeneratedActorInsightsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StoreGeneratedActorInsights", varargs...)
	ret0, _ := ret[0].(*consumer.StoreGeneratedActorInsightsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StoreGeneratedActorInsights indicates an expected call of StoreGeneratedActorInsights.
func (mr *MockConsumerClientMockRecorder) StoreGeneratedActorInsights(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StoreGeneratedActorInsights", reflect.TypeOf((*MockConsumerClient)(nil).StoreGeneratedActorInsights), varargs...)
}

// MockConsumerServer is a mock of ConsumerServer interface.
type MockConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockConsumerServerMockRecorder
}

// MockConsumerServerMockRecorder is the mock recorder for MockConsumerServer.
type MockConsumerServerMockRecorder struct {
	mock *MockConsumerServer
}

// NewMockConsumerServer creates a new mock instance.
func NewMockConsumerServer(ctrl *gomock.Controller) *MockConsumerServer {
	mock := &MockConsumerServer{ctrl: ctrl}
	mock.recorder = &MockConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockConsumerServer) EXPECT() *MockConsumerServerMockRecorder {
	return m.recorder
}

// CreateOrUpdateGenerationStatus mocks base method.
func (m *MockConsumerServer) CreateOrUpdateGenerationStatus(arg0 context.Context, arg1 *consumer.CreateOrUpdateGenerationStatusRequest) (*consumer.CreateOrUpdateGenerationStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOrUpdateGenerationStatus", arg0, arg1)
	ret0, _ := ret[0].(*consumer.CreateOrUpdateGenerationStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateOrUpdateGenerationStatus indicates an expected call of CreateOrUpdateGenerationStatus.
func (mr *MockConsumerServerMockRecorder) CreateOrUpdateGenerationStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrUpdateGenerationStatus", reflect.TypeOf((*MockConsumerServer)(nil).CreateOrUpdateGenerationStatus), arg0, arg1)
}

// DeleteCaAssetHistoriesEvent mocks base method.
func (m *MockConsumerServer) DeleteCaAssetHistoriesEvent(arg0 context.Context, arg1 *external.AccountUpdateEvent) (*consumer.DeleteCaAssetHistoriesEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteCaAssetHistoriesEvent", arg0, arg1)
	ret0, _ := ret[0].(*consumer.DeleteCaAssetHistoriesEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteCaAssetHistoriesEvent indicates an expected call of DeleteCaAssetHistoriesEvent.
func (mr *MockConsumerServerMockRecorder) DeleteCaAssetHistoriesEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteCaAssetHistoriesEvent", reflect.TypeOf((*MockConsumerServer)(nil).DeleteCaAssetHistoriesEvent), arg0, arg1)
}

// DeliverInsights mocks base method.
func (m *MockConsumerServer) DeliverInsights(arg0 context.Context, arg1 *consumer.DeliverInsightsRequest) (*consumer.DeliverInsightsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeliverInsights", arg0, arg1)
	ret0, _ := ret[0].(*consumer.DeliverInsightsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeliverInsights indicates an expected call of DeliverInsights.
func (mr *MockConsumerServerMockRecorder) DeliverInsights(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeliverInsights", reflect.TypeOf((*MockConsumerServer)(nil).DeliverInsights), arg0, arg1)
}

// ProcessCaNewDataFetchEvent mocks base method.
func (m *MockConsumerServer) ProcessCaNewDataFetchEvent(arg0 context.Context, arg1 *external.AccountDataSyncEvent) (*consumer.ProcessCaNewDataFetchEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessCaNewDataFetchEvent", arg0, arg1)
	ret0, _ := ret[0].(*consumer.ProcessCaNewDataFetchEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCaNewDataFetchEvent indicates an expected call of ProcessCaNewDataFetchEvent.
func (mr *MockConsumerServerMockRecorder) ProcessCaNewDataFetchEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCaNewDataFetchEvent", reflect.TypeOf((*MockConsumerServer)(nil).ProcessCaNewDataFetchEvent), arg0, arg1)
}

// StoreGeneratedActorInsights mocks base method.
func (m *MockConsumerServer) StoreGeneratedActorInsights(arg0 context.Context, arg1 *consumer.StoreGeneratedActorInsightsRequest) (*consumer.StoreGeneratedActorInsightsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StoreGeneratedActorInsights", arg0, arg1)
	ret0, _ := ret[0].(*consumer.StoreGeneratedActorInsightsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StoreGeneratedActorInsights indicates an expected call of StoreGeneratedActorInsights.
func (mr *MockConsumerServerMockRecorder) StoreGeneratedActorInsights(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StoreGeneratedActorInsights", reflect.TypeOf((*MockConsumerServer)(nil).StoreGeneratedActorInsights), arg0, arg1)
}

// MockUnsafeConsumerServer is a mock of UnsafeConsumerServer interface.
type MockUnsafeConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeConsumerServerMockRecorder
}

// MockUnsafeConsumerServerMockRecorder is the mock recorder for MockUnsafeConsumerServer.
type MockUnsafeConsumerServerMockRecorder struct {
	mock *MockUnsafeConsumerServer
}

// NewMockUnsafeConsumerServer creates a new mock instance.
func NewMockUnsafeConsumerServer(ctrl *gomock.Controller) *MockUnsafeConsumerServer {
	mock := &MockUnsafeConsumerServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeConsumerServer) EXPECT() *MockUnsafeConsumerServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedConsumerServer mocks base method.
func (m *MockUnsafeConsumerServer) mustEmbedUnimplementedConsumerServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedConsumerServer")
}

// mustEmbedUnimplementedConsumerServer indicates an expected call of mustEmbedUnimplementedConsumerServer.
func (mr *MockUnsafeConsumerServerMockRecorder) mustEmbedUnimplementedConsumerServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedConsumerServer", reflect.TypeOf((*MockUnsafeConsumerServer)(nil).mustEmbedUnimplementedConsumerServer))
}
