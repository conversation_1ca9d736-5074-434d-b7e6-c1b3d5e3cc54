// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/insights/consumer/service.proto

package consumer

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	queue "github.com/epifi/be-common/api/queue"
	external "github.com/epifi/gamma/api/connected_account/external"
	model "github.com/epifi/gamma/api/insights/model"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateOrUpdateGenerationStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConsumerRequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=consumer_request_header,json=consumerRequestHeader,proto3" json:"consumer_request_header,omitempty"`
	// run_id
	Id          string `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	FrameworkId string `protobuf:"bytes,3,opt,name=framework_id,json=frameworkId,proto3" json:"framework_id,omitempty"`
	// airflow run id
	PlatformRunId string `protobuf:"bytes,4,opt,name=platform_run_id,json=platformRunId,proto3" json:"platform_run_id,omitempty"`
	// Whether spark or apache beam
	GenerationPlatform model.GenerationPlatform `protobuf:"varint,5,opt,name=generation_platform,json=generationPlatform,proto3,enum=insights.model.GenerationPlatform" json:"generation_platform,omitempty"`
	// defines the current status of generation i.e. started, completed or failed
	RunStatus                 model.RunStatus        `protobuf:"varint,6,opt,name=run_status,json=runStatus,proto3,enum=insights.model.RunStatus" json:"run_status,omitempty"`
	ScriptName                string                 `protobuf:"bytes,7,opt,name=script_name,json=scriptName,proto3" json:"script_name,omitempty"`
	NumberOfInsightsGenerated uint32                 `protobuf:"varint,8,opt,name=number_of_insights_generated,json=numberOfInsightsGenerated,proto3" json:"number_of_insights_generated,omitempty"`
	Timestamp                 *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *CreateOrUpdateGenerationStatusRequest) Reset() {
	*x = CreateOrUpdateGenerationStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_consumer_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOrUpdateGenerationStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrUpdateGenerationStatusRequest) ProtoMessage() {}

func (x *CreateOrUpdateGenerationStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_consumer_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrUpdateGenerationStatusRequest.ProtoReflect.Descriptor instead.
func (*CreateOrUpdateGenerationStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_insights_consumer_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreateOrUpdateGenerationStatusRequest) GetConsumerRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.ConsumerRequestHeader
	}
	return nil
}

func (x *CreateOrUpdateGenerationStatusRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CreateOrUpdateGenerationStatusRequest) GetFrameworkId() string {
	if x != nil {
		return x.FrameworkId
	}
	return ""
}

func (x *CreateOrUpdateGenerationStatusRequest) GetPlatformRunId() string {
	if x != nil {
		return x.PlatformRunId
	}
	return ""
}

func (x *CreateOrUpdateGenerationStatusRequest) GetGenerationPlatform() model.GenerationPlatform {
	if x != nil {
		return x.GenerationPlatform
	}
	return model.GenerationPlatform(0)
}

func (x *CreateOrUpdateGenerationStatusRequest) GetRunStatus() model.RunStatus {
	if x != nil {
		return x.RunStatus
	}
	return model.RunStatus(0)
}

func (x *CreateOrUpdateGenerationStatusRequest) GetScriptName() string {
	if x != nil {
		return x.ScriptName
	}
	return ""
}

func (x *CreateOrUpdateGenerationStatusRequest) GetNumberOfInsightsGenerated() uint32 {
	if x != nil {
		return x.NumberOfInsightsGenerated
	}
	return 0
}

func (x *CreateOrUpdateGenerationStatusRequest) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

type CreateOrUpdateGenerationStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *CreateOrUpdateGenerationStatusResponse) Reset() {
	*x = CreateOrUpdateGenerationStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_consumer_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOrUpdateGenerationStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrUpdateGenerationStatusResponse) ProtoMessage() {}

func (x *CreateOrUpdateGenerationStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_consumer_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrUpdateGenerationStatusResponse.ProtoReflect.Descriptor instead.
func (*CreateOrUpdateGenerationStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_insights_consumer_service_proto_rawDescGZIP(), []int{1}
}

func (x *CreateOrUpdateGenerationStatusResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ActorInsightData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TargetEntityId string                            `protobuf:"bytes,1,opt,name=target_entity_id,json=targetEntityId,proto3" json:"target_entity_id,omitempty"`
	RelevanceScore uint32                            `protobuf:"varint,2,opt,name=relevance_score,json=relevanceScore,proto3" json:"relevance_score,omitempty"`
	Values         []*model.InsightVariableValuePair `protobuf:"bytes,3,rep,name=values,proto3" json:"values,omitempty"`
}

func (x *ActorInsightData) Reset() {
	*x = ActorInsightData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_consumer_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActorInsightData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActorInsightData) ProtoMessage() {}

func (x *ActorInsightData) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_consumer_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActorInsightData.ProtoReflect.Descriptor instead.
func (*ActorInsightData) Descriptor() ([]byte, []int) {
	return file_api_insights_consumer_service_proto_rawDescGZIP(), []int{2}
}

func (x *ActorInsightData) GetTargetEntityId() string {
	if x != nil {
		return x.TargetEntityId
	}
	return ""
}

func (x *ActorInsightData) GetRelevanceScore() uint32 {
	if x != nil {
		return x.RelevanceScore
	}
	return 0
}

func (x *ActorInsightData) GetValues() []*model.InsightVariableValuePair {
	if x != nil {
		return x.Values
	}
	return nil
}

type StoreGeneratedActorInsightsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConsumerRequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=consumer_request_header,json=consumerRequestHeader,proto3" json:"consumer_request_header,omitempty"`
	// run_id (generated random uuid)
	RunId       string `protobuf:"bytes,2,opt,name=run_id,json=runId,proto3" json:"run_id,omitempty"`
	FrameworkId string `protobuf:"bytes,3,opt,name=framework_id,json=frameworkId,proto3" json:"framework_id,omitempty"`
	// consumer gets actor_insight_data in batches of size 1000 from generation platform
	ActorInsightData []*ActorInsightData `protobuf:"bytes,4,rep,name=actor_insight_data,json=actorInsightData,proto3" json:"actor_insight_data,omitempty"`
	// Defines the validity for the insights (valid_from and valid_till are both exclusive in validity)
	ValidFrom *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=valid_from,json=validFrom,proto3" json:"valid_from,omitempty"`
	ValidTill *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=valid_till,json=validTill,proto3" json:"valid_till,omitempty"`
}

func (x *StoreGeneratedActorInsightsRequest) Reset() {
	*x = StoreGeneratedActorInsightsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_consumer_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreGeneratedActorInsightsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreGeneratedActorInsightsRequest) ProtoMessage() {}

func (x *StoreGeneratedActorInsightsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_consumer_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreGeneratedActorInsightsRequest.ProtoReflect.Descriptor instead.
func (*StoreGeneratedActorInsightsRequest) Descriptor() ([]byte, []int) {
	return file_api_insights_consumer_service_proto_rawDescGZIP(), []int{3}
}

func (x *StoreGeneratedActorInsightsRequest) GetConsumerRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.ConsumerRequestHeader
	}
	return nil
}

func (x *StoreGeneratedActorInsightsRequest) GetRunId() string {
	if x != nil {
		return x.RunId
	}
	return ""
}

func (x *StoreGeneratedActorInsightsRequest) GetFrameworkId() string {
	if x != nil {
		return x.FrameworkId
	}
	return ""
}

func (x *StoreGeneratedActorInsightsRequest) GetActorInsightData() []*ActorInsightData {
	if x != nil {
		return x.ActorInsightData
	}
	return nil
}

func (x *StoreGeneratedActorInsightsRequest) GetValidFrom() *timestamppb.Timestamp {
	if x != nil {
		return x.ValidFrom
	}
	return nil
}

func (x *StoreGeneratedActorInsightsRequest) GetValidTill() *timestamppb.Timestamp {
	if x != nil {
		return x.ValidTill
	}
	return nil
}

type StoreGeneratedActorInsightsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *StoreGeneratedActorInsightsResponse) Reset() {
	*x = StoreGeneratedActorInsightsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_consumer_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreGeneratedActorInsightsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreGeneratedActorInsightsResponse) ProtoMessage() {}

func (x *StoreGeneratedActorInsightsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_consumer_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreGeneratedActorInsightsResponse.ProtoReflect.Descriptor instead.
func (*StoreGeneratedActorInsightsResponse) Descriptor() ([]byte, []int) {
	return file_api_insights_consumer_service_proto_rawDescGZIP(), []int{4}
}

func (x *StoreGeneratedActorInsightsResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type DeliverInsightsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConsumerRequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=consumer_request_header,json=consumerRequestHeader,proto3" json:"consumer_request_header,omitempty"`
	BatchActorDetails     []*BatchActorDetails         `protobuf:"bytes,2,rep,name=batch_actor_details,json=batchActorDetails,proto3" json:"batch_actor_details,omitempty"`
	Destination           model.InsightDestination     `protobuf:"varint,3,opt,name=destination,proto3,enum=insights.model.InsightDestination" json:"destination,omitempty"`
}

func (x *DeliverInsightsRequest) Reset() {
	*x = DeliverInsightsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_consumer_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeliverInsightsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeliverInsightsRequest) ProtoMessage() {}

func (x *DeliverInsightsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_consumer_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeliverInsightsRequest.ProtoReflect.Descriptor instead.
func (*DeliverInsightsRequest) Descriptor() ([]byte, []int) {
	return file_api_insights_consumer_service_proto_rawDescGZIP(), []int{5}
}

func (x *DeliverInsightsRequest) GetConsumerRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.ConsumerRequestHeader
	}
	return nil
}

func (x *DeliverInsightsRequest) GetBatchActorDetails() []*BatchActorDetails {
	if x != nil {
		return x.BatchActorDetails
	}
	return nil
}

func (x *DeliverInsightsRequest) GetDestination() model.InsightDestination {
	if x != nil {
		return x.Destination
	}
	return model.InsightDestination(0)
}

type BatchActorDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	UserId  string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *BatchActorDetails) Reset() {
	*x = BatchActorDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_consumer_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchActorDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchActorDetails) ProtoMessage() {}

func (x *BatchActorDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_consumer_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchActorDetails.ProtoReflect.Descriptor instead.
func (*BatchActorDetails) Descriptor() ([]byte, []int) {
	return file_api_insights_consumer_service_proto_rawDescGZIP(), []int{6}
}

func (x *BatchActorDetails) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *BatchActorDetails) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type DeliverInsightsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *DeliverInsightsResponse) Reset() {
	*x = DeliverInsightsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_consumer_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeliverInsightsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeliverInsightsResponse) ProtoMessage() {}

func (x *DeliverInsightsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_consumer_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeliverInsightsResponse.ProtoReflect.Descriptor instead.
func (*DeliverInsightsResponse) Descriptor() ([]byte, []int) {
	return file_api_insights_consumer_service_proto_rawDescGZIP(), []int{7}
}

func (x *DeliverInsightsResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessCaNewDataFetchEventResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessCaNewDataFetchEventResponse) Reset() {
	*x = ProcessCaNewDataFetchEventResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_consumer_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCaNewDataFetchEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCaNewDataFetchEventResponse) ProtoMessage() {}

func (x *ProcessCaNewDataFetchEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_consumer_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCaNewDataFetchEventResponse.ProtoReflect.Descriptor instead.
func (*ProcessCaNewDataFetchEventResponse) Descriptor() ([]byte, []int) {
	return file_api_insights_consumer_service_proto_rawDescGZIP(), []int{8}
}

func (x *ProcessCaNewDataFetchEventResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type DeleteCaAssetHistoriesEventResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *DeleteCaAssetHistoriesEventResponse) Reset() {
	*x = DeleteCaAssetHistoriesEventResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_consumer_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteCaAssetHistoriesEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCaAssetHistoriesEventResponse) ProtoMessage() {}

func (x *DeleteCaAssetHistoriesEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_consumer_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCaAssetHistoriesEventResponse.ProtoReflect.Descriptor instead.
func (*DeleteCaAssetHistoriesEventResponse) Descriptor() ([]byte, []int) {
	return file_api_insights_consumer_service_proto_rawDescGZIP(), []int{9}
}

func (x *DeleteCaAssetHistoriesEventResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

var File_api_insights_consumer_service_proto protoreflect.FileDescriptor

var file_api_insights_consumer_service_proto_rawDesc = []byte{
	0x0a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x63,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e,
	0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x69, 0x6e, 0x73,
	0x69, 0x67, 0x68, 0x74, 0x5f, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x5f, 0x70, 0x61, 0x69, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20,
	0x61, 0x70, 0x69, 0x2f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x72, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x2f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa0, 0x04, 0x0a, 0x25, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x54, 0x0a, 0x17, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x15, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x77,
	0x6f, 0x72, 0x6b, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x5f, 0x72, 0x75, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x12, 0x53, 0x0a,
	0x13, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x69, 0x6e, 0x73,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x47, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x52, 0x12,
	0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x12, 0x42, 0x0a, 0x0a, 0x72, 0x75, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x52, 0x75, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x09, 0x72, 0x75, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3f, 0x0a, 0x1c, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x5f, 0x6f, 0x66, 0x5f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x5f, 0x67, 0x65,
	0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x19, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x49, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x47,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x12, 0x42, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x08, 0xfa, 0x42, 0x05, 0xb2, 0x01, 0x02, 0x08,
	0x01, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x70, 0x0a, 0x26,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x65,
	0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xb0,
	0x01, 0x0a, 0x10, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x31, 0x0a, 0x10, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x65, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x45, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x6c, 0x65, 0x76, 0x61,
	0x6e, 0x63, 0x65, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0e, 0x72, 0x65, 0x6c, 0x65, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12,
	0x40, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x28, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x49, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x50, 0x61, 0x69, 0x72, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x73, 0x22, 0x99, 0x03, 0x0a, 0x22, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x47, 0x65, 0x6e, 0x65, 0x72,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x54, 0x0a, 0x17, 0x63, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75,
	0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x15, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1e,
	0x0a, 0x06, 0x72, 0x75, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x05, 0x72, 0x75, 0x6e, 0x49, 0x64, 0x12, 0x2a,
	0x0a, 0x0c, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x66,
	0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x49, 0x64, 0x12, 0x5b, 0x0a, 0x12, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x41, 0x63, 0x74, 0x6f, 0x72,
	0x49, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x44, 0x61, 0x74, 0x61, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x10, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x6e, 0x73, 0x69,
	0x67, 0x68, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x39, 0x0a, 0x0a, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x46, 0x72,
	0x6f, 0x6d, 0x12, 0x39, 0x0a, 0x0a, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x74, 0x69, 0x6c, 0x6c,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x54, 0x69, 0x6c, 0x6c, 0x22, 0x6d, 0x0a,
	0x23, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e,
	0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x8a, 0x02, 0x0a,
	0x16, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x54, 0x0a, 0x17, 0x63, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65,
	0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x15, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x54, 0x0a,
	0x13, 0x62, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x69, 0x6e, 0x73,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x11, 0x62, 0x61, 0x74, 0x63, 0x68, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x44, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x49, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x64, 0x65,
	0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x47, 0x0a, 0x11, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x19,
	0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x22, 0x61, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x73,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a,
	0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x6c, 0x0a, 0x22, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x43, 0x61, 0x4e, 0x65, 0x77, 0x44, 0x61, 0x74, 0x61, 0x46, 0x65, 0x74, 0x63, 0x68, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x22, 0x6d, 0x0a, 0x23, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x61, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x32, 0xab, 0x05, 0x0a, 0x08, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x12,
	0x95, 0x01, 0x0a, 0x1e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x38, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x63, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8c, 0x01, 0x0a, 0x1b, 0x53, 0x74, 0x6f, 0x72,
	0x65, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x12, 0x35, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x6f, 0x72,
	0x65, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36,
	0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x72, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x68, 0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65,
	0x72, 0x49, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x12, 0x29, 0x2e, 0x69, 0x6e, 0x73, 0x69,
	0x67, 0x68, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x44, 0x65,
	0x6c, 0x69, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e,
	0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72,
	0x49, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x85, 0x01, 0x0a, 0x1a, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x4e, 0x65,
	0x77, 0x44, 0x61, 0x74, 0x61, 0x46, 0x65, 0x74, 0x63, 0x68, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12,
	0x30, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x53, 0x79, 0x6e, 0x63, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x1a, 0x35, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x4e,
	0x65, 0x77, 0x44, 0x61, 0x74, 0x61, 0x46, 0x65, 0x74, 0x63, 0x68, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x85, 0x01, 0x0a, 0x1b, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x43, 0x61, 0x41, 0x73, 0x73, 0x65, 0x74, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72,
	0x69, 0x65, 0x73, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x2e, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x65, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x1a, 0x36, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x43, 0x61, 0x41, 0x73, 0x73, 0x65, 0x74, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72,
	0x69, 0x65, 0x73, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x42, 0x5c, 0x0a, 0x2c, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72,
	0x5a, 0x2c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x73,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_insights_consumer_service_proto_rawDescOnce sync.Once
	file_api_insights_consumer_service_proto_rawDescData = file_api_insights_consumer_service_proto_rawDesc
)

func file_api_insights_consumer_service_proto_rawDescGZIP() []byte {
	file_api_insights_consumer_service_proto_rawDescOnce.Do(func() {
		file_api_insights_consumer_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_insights_consumer_service_proto_rawDescData)
	})
	return file_api_insights_consumer_service_proto_rawDescData
}

var file_api_insights_consumer_service_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_api_insights_consumer_service_proto_goTypes = []interface{}{
	(*CreateOrUpdateGenerationStatusRequest)(nil),  // 0: insights.consumer.CreateOrUpdateGenerationStatusRequest
	(*CreateOrUpdateGenerationStatusResponse)(nil), // 1: insights.consumer.CreateOrUpdateGenerationStatusResponse
	(*ActorInsightData)(nil),                       // 2: insights.consumer.ActorInsightData
	(*StoreGeneratedActorInsightsRequest)(nil),     // 3: insights.consumer.StoreGeneratedActorInsightsRequest
	(*StoreGeneratedActorInsightsResponse)(nil),    // 4: insights.consumer.StoreGeneratedActorInsightsResponse
	(*DeliverInsightsRequest)(nil),                 // 5: insights.consumer.DeliverInsightsRequest
	(*BatchActorDetails)(nil),                      // 6: insights.consumer.BatchActorDetails
	(*DeliverInsightsResponse)(nil),                // 7: insights.consumer.DeliverInsightsResponse
	(*ProcessCaNewDataFetchEventResponse)(nil),     // 8: insights.consumer.ProcessCaNewDataFetchEventResponse
	(*DeleteCaAssetHistoriesEventResponse)(nil),    // 9: insights.consumer.DeleteCaAssetHistoriesEventResponse
	(*queue.ConsumerRequestHeader)(nil),            // 10: queue.ConsumerRequestHeader
	(model.GenerationPlatform)(0),                  // 11: insights.model.GenerationPlatform
	(model.RunStatus)(0),                           // 12: insights.model.RunStatus
	(*timestamppb.Timestamp)(nil),                  // 13: google.protobuf.Timestamp
	(*queue.ConsumerResponseHeader)(nil),           // 14: queue.ConsumerResponseHeader
	(*model.InsightVariableValuePair)(nil),         // 15: insights.model.InsightVariableValuePair
	(model.InsightDestination)(0),                  // 16: insights.model.InsightDestination
	(*external.AccountDataSyncEvent)(nil),          // 17: connected_account.external.AccountDataSyncEvent
	(*external.AccountUpdateEvent)(nil),            // 18: connected_account.external.AccountUpdateEvent
}
var file_api_insights_consumer_service_proto_depIdxs = []int32{
	10, // 0: insights.consumer.CreateOrUpdateGenerationStatusRequest.consumer_request_header:type_name -> queue.ConsumerRequestHeader
	11, // 1: insights.consumer.CreateOrUpdateGenerationStatusRequest.generation_platform:type_name -> insights.model.GenerationPlatform
	12, // 2: insights.consumer.CreateOrUpdateGenerationStatusRequest.run_status:type_name -> insights.model.RunStatus
	13, // 3: insights.consumer.CreateOrUpdateGenerationStatusRequest.timestamp:type_name -> google.protobuf.Timestamp
	14, // 4: insights.consumer.CreateOrUpdateGenerationStatusResponse.response_header:type_name -> queue.ConsumerResponseHeader
	15, // 5: insights.consumer.ActorInsightData.values:type_name -> insights.model.InsightVariableValuePair
	10, // 6: insights.consumer.StoreGeneratedActorInsightsRequest.consumer_request_header:type_name -> queue.ConsumerRequestHeader
	2,  // 7: insights.consumer.StoreGeneratedActorInsightsRequest.actor_insight_data:type_name -> insights.consumer.ActorInsightData
	13, // 8: insights.consumer.StoreGeneratedActorInsightsRequest.valid_from:type_name -> google.protobuf.Timestamp
	13, // 9: insights.consumer.StoreGeneratedActorInsightsRequest.valid_till:type_name -> google.protobuf.Timestamp
	14, // 10: insights.consumer.StoreGeneratedActorInsightsResponse.response_header:type_name -> queue.ConsumerResponseHeader
	10, // 11: insights.consumer.DeliverInsightsRequest.consumer_request_header:type_name -> queue.ConsumerRequestHeader
	6,  // 12: insights.consumer.DeliverInsightsRequest.batch_actor_details:type_name -> insights.consumer.BatchActorDetails
	16, // 13: insights.consumer.DeliverInsightsRequest.destination:type_name -> insights.model.InsightDestination
	14, // 14: insights.consumer.DeliverInsightsResponse.response_header:type_name -> queue.ConsumerResponseHeader
	14, // 15: insights.consumer.ProcessCaNewDataFetchEventResponse.response_header:type_name -> queue.ConsumerResponseHeader
	14, // 16: insights.consumer.DeleteCaAssetHistoriesEventResponse.response_header:type_name -> queue.ConsumerResponseHeader
	0,  // 17: insights.consumer.Consumer.CreateOrUpdateGenerationStatus:input_type -> insights.consumer.CreateOrUpdateGenerationStatusRequest
	3,  // 18: insights.consumer.Consumer.StoreGeneratedActorInsights:input_type -> insights.consumer.StoreGeneratedActorInsightsRequest
	5,  // 19: insights.consumer.Consumer.DeliverInsights:input_type -> insights.consumer.DeliverInsightsRequest
	17, // 20: insights.consumer.Consumer.ProcessCaNewDataFetchEvent:input_type -> connected_account.external.AccountDataSyncEvent
	18, // 21: insights.consumer.Consumer.DeleteCaAssetHistoriesEvent:input_type -> connected_account.external.AccountUpdateEvent
	1,  // 22: insights.consumer.Consumer.CreateOrUpdateGenerationStatus:output_type -> insights.consumer.CreateOrUpdateGenerationStatusResponse
	4,  // 23: insights.consumer.Consumer.StoreGeneratedActorInsights:output_type -> insights.consumer.StoreGeneratedActorInsightsResponse
	7,  // 24: insights.consumer.Consumer.DeliverInsights:output_type -> insights.consumer.DeliverInsightsResponse
	8,  // 25: insights.consumer.Consumer.ProcessCaNewDataFetchEvent:output_type -> insights.consumer.ProcessCaNewDataFetchEventResponse
	9,  // 26: insights.consumer.Consumer.DeleteCaAssetHistoriesEvent:output_type -> insights.consumer.DeleteCaAssetHistoriesEventResponse
	22, // [22:27] is the sub-list for method output_type
	17, // [17:22] is the sub-list for method input_type
	17, // [17:17] is the sub-list for extension type_name
	17, // [17:17] is the sub-list for extension extendee
	0,  // [0:17] is the sub-list for field type_name
}

func init() { file_api_insights_consumer_service_proto_init() }
func file_api_insights_consumer_service_proto_init() {
	if File_api_insights_consumer_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_insights_consumer_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateOrUpdateGenerationStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_consumer_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateOrUpdateGenerationStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_consumer_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActorInsightData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_consumer_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StoreGeneratedActorInsightsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_consumer_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StoreGeneratedActorInsightsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_consumer_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeliverInsightsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_consumer_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchActorDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_consumer_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeliverInsightsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_consumer_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCaNewDataFetchEventResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_consumer_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteCaAssetHistoriesEventResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_insights_consumer_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_insights_consumer_service_proto_goTypes,
		DependencyIndexes: file_api_insights_consumer_service_proto_depIdxs,
		MessageInfos:      file_api_insights_consumer_service_proto_msgTypes,
	}.Build()
	File_api_insights_consumer_service_proto = out.File
	file_api_insights_consumer_service_proto_rawDesc = nil
	file_api_insights_consumer_service_proto_goTypes = nil
	file_api_insights_consumer_service_proto_depIdxs = nil
}
