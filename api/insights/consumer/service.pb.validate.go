// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/insights/consumer/service.proto

package consumer

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	model "github.com/epifi/gamma/api/insights/model"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = model.GenerationPlatform(0)
)

// Validate checks the field values on CreateOrUpdateGenerationStatusRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CreateOrUpdateGenerationStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateOrUpdateGenerationStatusRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreateOrUpdateGenerationStatusRequestMultiError, or nil if none found.
func (m *CreateOrUpdateGenerationStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateOrUpdateGenerationStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetConsumerRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateOrUpdateGenerationStatusRequestValidationError{
					field:  "ConsumerRequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateOrUpdateGenerationStatusRequestValidationError{
					field:  "ConsumerRequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConsumerRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateOrUpdateGenerationStatusRequestValidationError{
				field:  "ConsumerRequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetId()) < 1 {
		err := CreateOrUpdateGenerationStatusRequestValidationError{
			field:  "Id",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for FrameworkId

	// no validation rules for PlatformRunId

	// no validation rules for GenerationPlatform

	if _, ok := _CreateOrUpdateGenerationStatusRequest_RunStatus_NotInLookup[m.GetRunStatus()]; ok {
		err := CreateOrUpdateGenerationStatusRequestValidationError{
			field:  "RunStatus",
			reason: "value must not be in list [RUN_STATUS_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ScriptName

	// no validation rules for NumberOfInsightsGenerated

	if m.GetTimestamp() == nil {
		err := CreateOrUpdateGenerationStatusRequestValidationError{
			field:  "Timestamp",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CreateOrUpdateGenerationStatusRequestMultiError(errors)
	}

	return nil
}

// CreateOrUpdateGenerationStatusRequestMultiError is an error wrapping
// multiple validation errors returned by
// CreateOrUpdateGenerationStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateOrUpdateGenerationStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateOrUpdateGenerationStatusRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateOrUpdateGenerationStatusRequestMultiError) AllErrors() []error { return m }

// CreateOrUpdateGenerationStatusRequestValidationError is the validation error
// returned by CreateOrUpdateGenerationStatusRequest.Validate if the
// designated constraints aren't met.
type CreateOrUpdateGenerationStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateOrUpdateGenerationStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateOrUpdateGenerationStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateOrUpdateGenerationStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateOrUpdateGenerationStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateOrUpdateGenerationStatusRequestValidationError) ErrorName() string {
	return "CreateOrUpdateGenerationStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateOrUpdateGenerationStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateOrUpdateGenerationStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateOrUpdateGenerationStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateOrUpdateGenerationStatusRequestValidationError{}

var _CreateOrUpdateGenerationStatusRequest_RunStatus_NotInLookup = map[model.RunStatus]struct{}{
	0: {},
}

// Validate checks the field values on CreateOrUpdateGenerationStatusResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CreateOrUpdateGenerationStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CreateOrUpdateGenerationStatusResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// CreateOrUpdateGenerationStatusResponseMultiError, or nil if none found.
func (m *CreateOrUpdateGenerationStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateOrUpdateGenerationStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateOrUpdateGenerationStatusResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateOrUpdateGenerationStatusResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateOrUpdateGenerationStatusResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateOrUpdateGenerationStatusResponseMultiError(errors)
	}

	return nil
}

// CreateOrUpdateGenerationStatusResponseMultiError is an error wrapping
// multiple validation errors returned by
// CreateOrUpdateGenerationStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateOrUpdateGenerationStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateOrUpdateGenerationStatusResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateOrUpdateGenerationStatusResponseMultiError) AllErrors() []error { return m }

// CreateOrUpdateGenerationStatusResponseValidationError is the validation
// error returned by CreateOrUpdateGenerationStatusResponse.Validate if the
// designated constraints aren't met.
type CreateOrUpdateGenerationStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateOrUpdateGenerationStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateOrUpdateGenerationStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateOrUpdateGenerationStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateOrUpdateGenerationStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateOrUpdateGenerationStatusResponseValidationError) ErrorName() string {
	return "CreateOrUpdateGenerationStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateOrUpdateGenerationStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateOrUpdateGenerationStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateOrUpdateGenerationStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateOrUpdateGenerationStatusResponseValidationError{}

// Validate checks the field values on ActorInsightData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ActorInsightData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActorInsightData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ActorInsightDataMultiError, or nil if none found.
func (m *ActorInsightData) ValidateAll() error {
	return m.validate(true)
}

func (m *ActorInsightData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetTargetEntityId()) < 1 {
		err := ActorInsightDataValidationError{
			field:  "TargetEntityId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for RelevanceScore

	for idx, item := range m.GetValues() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActorInsightDataValidationError{
						field:  fmt.Sprintf("Values[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActorInsightDataValidationError{
						field:  fmt.Sprintf("Values[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActorInsightDataValidationError{
					field:  fmt.Sprintf("Values[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ActorInsightDataMultiError(errors)
	}

	return nil
}

// ActorInsightDataMultiError is an error wrapping multiple validation errors
// returned by ActorInsightData.ValidateAll() if the designated constraints
// aren't met.
type ActorInsightDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActorInsightDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActorInsightDataMultiError) AllErrors() []error { return m }

// ActorInsightDataValidationError is the validation error returned by
// ActorInsightData.Validate if the designated constraints aren't met.
type ActorInsightDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActorInsightDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActorInsightDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActorInsightDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActorInsightDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActorInsightDataValidationError) ErrorName() string { return "ActorInsightDataValidationError" }

// Error satisfies the builtin error interface
func (e ActorInsightDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActorInsightData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActorInsightDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActorInsightDataValidationError{}

// Validate checks the field values on StoreGeneratedActorInsightsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *StoreGeneratedActorInsightsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoreGeneratedActorInsightsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// StoreGeneratedActorInsightsRequestMultiError, or nil if none found.
func (m *StoreGeneratedActorInsightsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *StoreGeneratedActorInsightsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetConsumerRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoreGeneratedActorInsightsRequestValidationError{
					field:  "ConsumerRequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoreGeneratedActorInsightsRequestValidationError{
					field:  "ConsumerRequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConsumerRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoreGeneratedActorInsightsRequestValidationError{
				field:  "ConsumerRequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetRunId()) < 1 {
		err := StoreGeneratedActorInsightsRequestValidationError{
			field:  "RunId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetFrameworkId()) < 1 {
		err := StoreGeneratedActorInsightsRequestValidationError{
			field:  "FrameworkId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetActorInsightData()) < 1 {
		err := StoreGeneratedActorInsightsRequestValidationError{
			field:  "ActorInsightData",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetActorInsightData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoreGeneratedActorInsightsRequestValidationError{
						field:  fmt.Sprintf("ActorInsightData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoreGeneratedActorInsightsRequestValidationError{
						field:  fmt.Sprintf("ActorInsightData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoreGeneratedActorInsightsRequestValidationError{
					field:  fmt.Sprintf("ActorInsightData[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetValidFrom()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoreGeneratedActorInsightsRequestValidationError{
					field:  "ValidFrom",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoreGeneratedActorInsightsRequestValidationError{
					field:  "ValidFrom",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValidFrom()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoreGeneratedActorInsightsRequestValidationError{
				field:  "ValidFrom",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetValidTill()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoreGeneratedActorInsightsRequestValidationError{
					field:  "ValidTill",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoreGeneratedActorInsightsRequestValidationError{
					field:  "ValidTill",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValidTill()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoreGeneratedActorInsightsRequestValidationError{
				field:  "ValidTill",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return StoreGeneratedActorInsightsRequestMultiError(errors)
	}

	return nil
}

// StoreGeneratedActorInsightsRequestMultiError is an error wrapping multiple
// validation errors returned by
// StoreGeneratedActorInsightsRequest.ValidateAll() if the designated
// constraints aren't met.
type StoreGeneratedActorInsightsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoreGeneratedActorInsightsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoreGeneratedActorInsightsRequestMultiError) AllErrors() []error { return m }

// StoreGeneratedActorInsightsRequestValidationError is the validation error
// returned by StoreGeneratedActorInsightsRequest.Validate if the designated
// constraints aren't met.
type StoreGeneratedActorInsightsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoreGeneratedActorInsightsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoreGeneratedActorInsightsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoreGeneratedActorInsightsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoreGeneratedActorInsightsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoreGeneratedActorInsightsRequestValidationError) ErrorName() string {
	return "StoreGeneratedActorInsightsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e StoreGeneratedActorInsightsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoreGeneratedActorInsightsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoreGeneratedActorInsightsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoreGeneratedActorInsightsRequestValidationError{}

// Validate checks the field values on StoreGeneratedActorInsightsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *StoreGeneratedActorInsightsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoreGeneratedActorInsightsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// StoreGeneratedActorInsightsResponseMultiError, or nil if none found.
func (m *StoreGeneratedActorInsightsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *StoreGeneratedActorInsightsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoreGeneratedActorInsightsResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoreGeneratedActorInsightsResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoreGeneratedActorInsightsResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return StoreGeneratedActorInsightsResponseMultiError(errors)
	}

	return nil
}

// StoreGeneratedActorInsightsResponseMultiError is an error wrapping multiple
// validation errors returned by
// StoreGeneratedActorInsightsResponse.ValidateAll() if the designated
// constraints aren't met.
type StoreGeneratedActorInsightsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoreGeneratedActorInsightsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoreGeneratedActorInsightsResponseMultiError) AllErrors() []error { return m }

// StoreGeneratedActorInsightsResponseValidationError is the validation error
// returned by StoreGeneratedActorInsightsResponse.Validate if the designated
// constraints aren't met.
type StoreGeneratedActorInsightsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoreGeneratedActorInsightsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoreGeneratedActorInsightsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoreGeneratedActorInsightsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoreGeneratedActorInsightsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoreGeneratedActorInsightsResponseValidationError) ErrorName() string {
	return "StoreGeneratedActorInsightsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e StoreGeneratedActorInsightsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoreGeneratedActorInsightsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoreGeneratedActorInsightsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoreGeneratedActorInsightsResponseValidationError{}

// Validate checks the field values on DeliverInsightsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeliverInsightsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeliverInsightsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeliverInsightsRequestMultiError, or nil if none found.
func (m *DeliverInsightsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeliverInsightsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetConsumerRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeliverInsightsRequestValidationError{
					field:  "ConsumerRequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeliverInsightsRequestValidationError{
					field:  "ConsumerRequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConsumerRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeliverInsightsRequestValidationError{
				field:  "ConsumerRequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetBatchActorDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DeliverInsightsRequestValidationError{
						field:  fmt.Sprintf("BatchActorDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DeliverInsightsRequestValidationError{
						field:  fmt.Sprintf("BatchActorDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DeliverInsightsRequestValidationError{
					field:  fmt.Sprintf("BatchActorDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Destination

	if len(errors) > 0 {
		return DeliverInsightsRequestMultiError(errors)
	}

	return nil
}

// DeliverInsightsRequestMultiError is an error wrapping multiple validation
// errors returned by DeliverInsightsRequest.ValidateAll() if the designated
// constraints aren't met.
type DeliverInsightsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeliverInsightsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeliverInsightsRequestMultiError) AllErrors() []error { return m }

// DeliverInsightsRequestValidationError is the validation error returned by
// DeliverInsightsRequest.Validate if the designated constraints aren't met.
type DeliverInsightsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeliverInsightsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeliverInsightsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeliverInsightsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeliverInsightsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeliverInsightsRequestValidationError) ErrorName() string {
	return "DeliverInsightsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeliverInsightsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeliverInsightsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeliverInsightsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeliverInsightsRequestValidationError{}

// Validate checks the field values on BatchActorDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *BatchActorDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchActorDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchActorDetailsMultiError, or nil if none found.
func (m *BatchActorDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchActorDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for UserId

	if len(errors) > 0 {
		return BatchActorDetailsMultiError(errors)
	}

	return nil
}

// BatchActorDetailsMultiError is an error wrapping multiple validation errors
// returned by BatchActorDetails.ValidateAll() if the designated constraints
// aren't met.
type BatchActorDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchActorDetailsMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchActorDetailsMultiError) AllErrors() []error { return m }

// BatchActorDetailsValidationError is the validation error returned by
// BatchActorDetails.Validate if the designated constraints aren't met.
type BatchActorDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchActorDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchActorDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchActorDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchActorDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchActorDetailsValidationError) ErrorName() string {
	return "BatchActorDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e BatchActorDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchActorDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchActorDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchActorDetailsValidationError{}

// Validate checks the field values on DeliverInsightsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeliverInsightsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeliverInsightsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeliverInsightsResponseMultiError, or nil if none found.
func (m *DeliverInsightsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeliverInsightsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeliverInsightsResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeliverInsightsResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeliverInsightsResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeliverInsightsResponseMultiError(errors)
	}

	return nil
}

// DeliverInsightsResponseMultiError is an error wrapping multiple validation
// errors returned by DeliverInsightsResponse.ValidateAll() if the designated
// constraints aren't met.
type DeliverInsightsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeliverInsightsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeliverInsightsResponseMultiError) AllErrors() []error { return m }

// DeliverInsightsResponseValidationError is the validation error returned by
// DeliverInsightsResponse.Validate if the designated constraints aren't met.
type DeliverInsightsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeliverInsightsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeliverInsightsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeliverInsightsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeliverInsightsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeliverInsightsResponseValidationError) ErrorName() string {
	return "DeliverInsightsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeliverInsightsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeliverInsightsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeliverInsightsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeliverInsightsResponseValidationError{}

// Validate checks the field values on ProcessCaNewDataFetchEventResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ProcessCaNewDataFetchEventResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessCaNewDataFetchEventResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessCaNewDataFetchEventResponseMultiError, or nil if none found.
func (m *ProcessCaNewDataFetchEventResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCaNewDataFetchEventResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCaNewDataFetchEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCaNewDataFetchEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCaNewDataFetchEventResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessCaNewDataFetchEventResponseMultiError(errors)
	}

	return nil
}

// ProcessCaNewDataFetchEventResponseMultiError is an error wrapping multiple
// validation errors returned by
// ProcessCaNewDataFetchEventResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessCaNewDataFetchEventResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCaNewDataFetchEventResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCaNewDataFetchEventResponseMultiError) AllErrors() []error { return m }

// ProcessCaNewDataFetchEventResponseValidationError is the validation error
// returned by ProcessCaNewDataFetchEventResponse.Validate if the designated
// constraints aren't met.
type ProcessCaNewDataFetchEventResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCaNewDataFetchEventResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCaNewDataFetchEventResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCaNewDataFetchEventResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCaNewDataFetchEventResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCaNewDataFetchEventResponseValidationError) ErrorName() string {
	return "ProcessCaNewDataFetchEventResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCaNewDataFetchEventResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCaNewDataFetchEventResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCaNewDataFetchEventResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCaNewDataFetchEventResponseValidationError{}

// Validate checks the field values on DeleteCaAssetHistoriesEventResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *DeleteCaAssetHistoriesEventResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteCaAssetHistoriesEventResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// DeleteCaAssetHistoriesEventResponseMultiError, or nil if none found.
func (m *DeleteCaAssetHistoriesEventResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteCaAssetHistoriesEventResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeleteCaAssetHistoriesEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeleteCaAssetHistoriesEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeleteCaAssetHistoriesEventResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeleteCaAssetHistoriesEventResponseMultiError(errors)
	}

	return nil
}

// DeleteCaAssetHistoriesEventResponseMultiError is an error wrapping multiple
// validation errors returned by
// DeleteCaAssetHistoriesEventResponse.ValidateAll() if the designated
// constraints aren't met.
type DeleteCaAssetHistoriesEventResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteCaAssetHistoriesEventResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteCaAssetHistoriesEventResponseMultiError) AllErrors() []error { return m }

// DeleteCaAssetHistoriesEventResponseValidationError is the validation error
// returned by DeleteCaAssetHistoriesEventResponse.Validate if the designated
// constraints aren't met.
type DeleteCaAssetHistoriesEventResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteCaAssetHistoriesEventResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteCaAssetHistoriesEventResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteCaAssetHistoriesEventResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteCaAssetHistoriesEventResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteCaAssetHistoriesEventResponseValidationError) ErrorName() string {
	return "DeleteCaAssetHistoriesEventResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteCaAssetHistoriesEventResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteCaAssetHistoriesEventResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteCaAssetHistoriesEventResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteCaAssetHistoriesEventResponseValidationError{}
