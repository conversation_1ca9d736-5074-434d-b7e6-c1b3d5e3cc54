// Code generated by MockGen. DO NOT EDIT.
// Source: api/insights/networth/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	dynamic_elements "github.com/epifi/gamma/api/dynamic_elements"
	networth "github.com/epifi/gamma/api/insights/networth"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockNetWorthClient is a mock of NetWorthClient interface.
type MockNetWorthClient struct {
	ctrl     *gomock.Controller
	recorder *MockNetWorthClientMockRecorder
}

// MockNetWorthClientMockRecorder is the mock recorder for MockNetWorthClient.
type MockNetWorthClientMockRecorder struct {
	mock *MockNetWorthClient
}

// NewMockNetWorthClient creates a new mock instance.
func NewMockNetWorthClient(ctrl *gomock.Controller) *MockNetWorthClient {
	mock := &MockNetWorthClient{ctrl: ctrl}
	mock.recorder = &MockNetWorthClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNetWorthClient) EXPECT() *MockNetWorthClientMockRecorder {
	return m.recorder
}

// CreateNetWorthRefreshSession mocks base method.
func (m *MockNetWorthClient) CreateNetWorthRefreshSession(ctx context.Context, in *networth.CreateNetWorthRefreshSessionRequest, opts ...grpc.CallOption) (*networth.CreateNetWorthRefreshSessionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateNetWorthRefreshSession", varargs...)
	ret0, _ := ret[0].(*networth.CreateNetWorthRefreshSessionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateNetWorthRefreshSession indicates an expected call of CreateNetWorthRefreshSession.
func (mr *MockNetWorthClientMockRecorder) CreateNetWorthRefreshSession(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateNetWorthRefreshSession", reflect.TypeOf((*MockNetWorthClient)(nil).CreateNetWorthRefreshSession), varargs...)
}

// DeclareInvestment mocks base method.
func (m *MockNetWorthClient) DeclareInvestment(ctx context.Context, in *networth.DeclareInvestmentRequest, opts ...grpc.CallOption) (*networth.DeclareInvestmentResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeclareInvestment", varargs...)
	ret0, _ := ret[0].(*networth.DeclareInvestmentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeclareInvestment indicates an expected call of DeclareInvestment.
func (mr *MockNetWorthClientMockRecorder) DeclareInvestment(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeclareInvestment", reflect.TypeOf((*MockNetWorthClient)(nil).DeclareInvestment), varargs...)
}

// DeleteAllInvestmentDeclaration mocks base method.
func (m *MockNetWorthClient) DeleteAllInvestmentDeclaration(ctx context.Context, in *networth.DeleteAllInvestmentDeclarationRequest, opts ...grpc.CallOption) (*networth.DeleteAllInvestmentDeclarationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteAllInvestmentDeclaration", varargs...)
	ret0, _ := ret[0].(*networth.DeleteAllInvestmentDeclarationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteAllInvestmentDeclaration indicates an expected call of DeleteAllInvestmentDeclaration.
func (mr *MockNetWorthClientMockRecorder) DeleteAllInvestmentDeclaration(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAllInvestmentDeclaration", reflect.TypeOf((*MockNetWorthClient)(nil).DeleteAllInvestmentDeclaration), varargs...)
}

// DeleteInvestmentDeclaration mocks base method.
func (m *MockNetWorthClient) DeleteInvestmentDeclaration(ctx context.Context, in *networth.DeleteInvestmentDeclarationRequest, opts ...grpc.CallOption) (*networth.DeclareInvestmentResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteInvestmentDeclaration", varargs...)
	ret0, _ := ret[0].(*networth.DeclareInvestmentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteInvestmentDeclaration indicates an expected call of DeleteInvestmentDeclaration.
func (mr *MockNetWorthClientMockRecorder) DeleteInvestmentDeclaration(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteInvestmentDeclaration", reflect.TypeOf((*MockNetWorthClient)(nil).DeleteInvestmentDeclaration), varargs...)
}

// DynamicElementCallback mocks base method.
func (m *MockNetWorthClient) DynamicElementCallback(ctx context.Context, in *dynamic_elements.DynamicElementCallbackRequest, opts ...grpc.CallOption) (*dynamic_elements.DynamicElementCallbackResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DynamicElementCallback", varargs...)
	ret0, _ := ret[0].(*dynamic_elements.DynamicElementCallbackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DynamicElementCallback indicates an expected call of DynamicElementCallback.
func (mr *MockNetWorthClientMockRecorder) DynamicElementCallback(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DynamicElementCallback", reflect.TypeOf((*MockNetWorthClient)(nil).DynamicElementCallback), varargs...)
}

// FetchDynamicElements mocks base method.
func (m *MockNetWorthClient) FetchDynamicElements(ctx context.Context, in *dynamic_elements.FetchDynamicElementsRequest, opts ...grpc.CallOption) (*dynamic_elements.FetchDynamicElementsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchDynamicElements", varargs...)
	ret0, _ := ret[0].(*dynamic_elements.FetchDynamicElementsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchDynamicElements indicates an expected call of FetchDynamicElements.
func (mr *MockNetWorthClientMockRecorder) FetchDynamicElements(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchDynamicElements", reflect.TypeOf((*MockNetWorthClient)(nil).FetchDynamicElements), varargs...)
}

// GetAssetsDayChange mocks base method.
func (m *MockNetWorthClient) GetAssetsDayChange(ctx context.Context, in *networth.GetAssetsDayChangeRequest, opts ...grpc.CallOption) (*networth.GetAssetsDayChangeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAssetsDayChange", varargs...)
	ret0, _ := ret[0].(*networth.GetAssetsDayChangeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAssetsDayChange indicates an expected call of GetAssetsDayChange.
func (mr *MockNetWorthClientMockRecorder) GetAssetsDayChange(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAssetsDayChange", reflect.TypeOf((*MockNetWorthClient)(nil).GetAssetsDayChange), varargs...)
}

// GetFilesFromBucket mocks base method.
func (m *MockNetWorthClient) GetFilesFromBucket(ctx context.Context, in *networth.GetFilesFromBucketRequest, opts ...grpc.CallOption) (*networth.GetFilesFromBucketResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFilesFromBucket", varargs...)
	ret0, _ := ret[0].(*networth.GetFilesFromBucketResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFilesFromBucket indicates an expected call of GetFilesFromBucket.
func (mr *MockNetWorthClientMockRecorder) GetFilesFromBucket(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFilesFromBucket", reflect.TypeOf((*MockNetWorthClient)(nil).GetFilesFromBucket), varargs...)
}

// GetInvestmentDeclaration mocks base method.
func (m *MockNetWorthClient) GetInvestmentDeclaration(ctx context.Context, in *networth.GetInvestmentDeclarationRequest, opts ...grpc.CallOption) (*networth.GetInvestmentDeclarationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetInvestmentDeclaration", varargs...)
	ret0, _ := ret[0].(*networth.GetInvestmentDeclarationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInvestmentDeclaration indicates an expected call of GetInvestmentDeclaration.
func (mr *MockNetWorthClientMockRecorder) GetInvestmentDeclaration(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInvestmentDeclaration", reflect.TypeOf((*MockNetWorthClient)(nil).GetInvestmentDeclaration), varargs...)
}

// GetInvestmentDeclarations mocks base method.
func (m *MockNetWorthClient) GetInvestmentDeclarations(ctx context.Context, in *networth.GetInvestmentDeclarationsRequest, opts ...grpc.CallOption) (*networth.GetInvestmentDeclarationsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetInvestmentDeclarations", varargs...)
	ret0, _ := ret[0].(*networth.GetInvestmentDeclarationsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInvestmentDeclarations indicates an expected call of GetInvestmentDeclarations.
func (mr *MockNetWorthClientMockRecorder) GetInvestmentDeclarations(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInvestmentDeclarations", reflect.TypeOf((*MockNetWorthClient)(nil).GetInvestmentDeclarations), varargs...)
}

// GetNetWorthInstrumentsRefreshDetails mocks base method.
func (m *MockNetWorthClient) GetNetWorthInstrumentsRefreshDetails(ctx context.Context, in *networth.GetNetWorthInstrumentsRefreshDetailsRequest, opts ...grpc.CallOption) (*networth.GetNetWorthInstrumentsRefreshDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNetWorthInstrumentsRefreshDetails", varargs...)
	ret0, _ := ret[0].(*networth.GetNetWorthInstrumentsRefreshDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNetWorthInstrumentsRefreshDetails indicates an expected call of GetNetWorthInstrumentsRefreshDetails.
func (mr *MockNetWorthClientMockRecorder) GetNetWorthInstrumentsRefreshDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNetWorthInstrumentsRefreshDetails", reflect.TypeOf((*MockNetWorthClient)(nil).GetNetWorthInstrumentsRefreshDetails), varargs...)
}

// GetNetWorthRefreshSession mocks base method.
func (m *MockNetWorthClient) GetNetWorthRefreshSession(ctx context.Context, in *networth.GetNetWorthRefreshSessionRequest, opts ...grpc.CallOption) (*networth.GetNetWorthRefreshSessionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNetWorthRefreshSession", varargs...)
	ret0, _ := ret[0].(*networth.GetNetWorthRefreshSessionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNetWorthRefreshSession indicates an expected call of GetNetWorthRefreshSession.
func (mr *MockNetWorthClientMockRecorder) GetNetWorthRefreshSession(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNetWorthRefreshSession", reflect.TypeOf((*MockNetWorthClient)(nil).GetNetWorthRefreshSession), varargs...)
}

// GetNetWorthValue mocks base method.
func (m *MockNetWorthClient) GetNetWorthValue(ctx context.Context, in *networth.GetNetWorthValueRequest, opts ...grpc.CallOption) (*networth.GetNetWorthValueResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNetWorthValue", varargs...)
	ret0, _ := ret[0].(*networth.GetNetWorthValueResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNetWorthValue indicates an expected call of GetNetWorthValue.
func (mr *MockNetWorthClientMockRecorder) GetNetWorthValue(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNetWorthValue", reflect.TypeOf((*MockNetWorthClient)(nil).GetNetWorthValue), varargs...)
}

// GetNetworthDataFile mocks base method.
func (m *MockNetWorthClient) GetNetworthDataFile(ctx context.Context, in *networth.GetNetworthDataFileRequest, opts ...grpc.CallOption) (*networth.GetNetworthDataFileResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNetworthDataFile", varargs...)
	ret0, _ := ret[0].(*networth.GetNetworthDataFileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNetworthDataFile indicates an expected call of GetNetworthDataFile.
func (mr *MockNetWorthClientMockRecorder) GetNetworthDataFile(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNetworthDataFile", reflect.TypeOf((*MockNetWorthClient)(nil).GetNetworthDataFile), varargs...)
}

// GetPortfolioChangeSummary mocks base method.
func (m *MockNetWorthClient) GetPortfolioChangeSummary(ctx context.Context, in *networth.GetPortfolioChangeSummaryRequest, opts ...grpc.CallOption) (*networth.GetPortfolioChangeSummaryResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPortfolioChangeSummary", varargs...)
	ret0, _ := ret[0].(*networth.GetPortfolioChangeSummaryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPortfolioChangeSummary indicates an expected call of GetPortfolioChangeSummary.
func (mr *MockNetWorthClientMockRecorder) GetPortfolioChangeSummary(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPortfolioChangeSummary", reflect.TypeOf((*MockNetWorthClient)(nil).GetPortfolioChangeSummary), varargs...)
}

// MagicImportFiles mocks base method.
func (m *MockNetWorthClient) MagicImportFiles(ctx context.Context, in *networth.MagicImportFilesRequest, opts ...grpc.CallOption) (*networth.MagicImportFilesResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MagicImportFiles", varargs...)
	ret0, _ := ret[0].(*networth.MagicImportFilesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MagicImportFiles indicates an expected call of MagicImportFiles.
func (mr *MockNetWorthClientMockRecorder) MagicImportFiles(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MagicImportFiles", reflect.TypeOf((*MockNetWorthClient)(nil).MagicImportFiles), varargs...)
}

// SearchAssetFormFieldOptions mocks base method.
func (m *MockNetWorthClient) SearchAssetFormFieldOptions(ctx context.Context, in *networth.SearchAssetFormFieldOptionsRequest, opts ...grpc.CallOption) (*networth.SearchAssetFormFieldOptionsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SearchAssetFormFieldOptions", varargs...)
	ret0, _ := ret[0].(*networth.SearchAssetFormFieldOptionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchAssetFormFieldOptions indicates an expected call of SearchAssetFormFieldOptions.
func (mr *MockNetWorthClientMockRecorder) SearchAssetFormFieldOptions(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchAssetFormFieldOptions", reflect.TypeOf((*MockNetWorthClient)(nil).SearchAssetFormFieldOptions), varargs...)
}

// StoreSnapshot mocks base method.
func (m *MockNetWorthClient) StoreSnapshot(ctx context.Context, in *networth.StoreSnapshotRequest, opts ...grpc.CallOption) (*networth.StoreSnapshotResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StoreSnapshot", varargs...)
	ret0, _ := ret[0].(*networth.StoreSnapshotResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StoreSnapshot indicates an expected call of StoreSnapshot.
func (mr *MockNetWorthClientMockRecorder) StoreSnapshot(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StoreSnapshot", reflect.TypeOf((*MockNetWorthClient)(nil).StoreSnapshot), varargs...)
}

// UpdateBulkManualAssetsCurrentValue mocks base method.
func (m *MockNetWorthClient) UpdateBulkManualAssetsCurrentValue(ctx context.Context, in *networth.UpdateBulkManualAssetsCurrentValueRequest, opts ...grpc.CallOption) (*networth.UpdateBulkManualAssetsCurrentValueResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateBulkManualAssetsCurrentValue", varargs...)
	ret0, _ := ret[0].(*networth.UpdateBulkManualAssetsCurrentValueResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateBulkManualAssetsCurrentValue indicates an expected call of UpdateBulkManualAssetsCurrentValue.
func (mr *MockNetWorthClientMockRecorder) UpdateBulkManualAssetsCurrentValue(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBulkManualAssetsCurrentValue", reflect.TypeOf((*MockNetWorthClient)(nil).UpdateBulkManualAssetsCurrentValue), varargs...)
}

// UpdateInvestmentDeclaration mocks base method.
func (m *MockNetWorthClient) UpdateInvestmentDeclaration(ctx context.Context, in *networth.UpdateInvestmentDeclarationRequest, opts ...grpc.CallOption) (*networth.UpdateInvestmentDeclarationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateInvestmentDeclaration", varargs...)
	ret0, _ := ret[0].(*networth.UpdateInvestmentDeclarationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateInvestmentDeclaration indicates an expected call of UpdateInvestmentDeclaration.
func (mr *MockNetWorthClientMockRecorder) UpdateInvestmentDeclaration(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInvestmentDeclaration", reflect.TypeOf((*MockNetWorthClient)(nil).UpdateInvestmentDeclaration), varargs...)
}

// UpdateNetWorthRefreshSession mocks base method.
func (m *MockNetWorthClient) UpdateNetWorthRefreshSession(ctx context.Context, in *networth.UpdateNetWorthRefreshSessionRequest, opts ...grpc.CallOption) (*networth.UpdateNetWorthRefreshSessionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateNetWorthRefreshSession", varargs...)
	ret0, _ := ret[0].(*networth.UpdateNetWorthRefreshSessionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateNetWorthRefreshSession indicates an expected call of UpdateNetWorthRefreshSession.
func (mr *MockNetWorthClientMockRecorder) UpdateNetWorthRefreshSession(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateNetWorthRefreshSession", reflect.TypeOf((*MockNetWorthClient)(nil).UpdateNetWorthRefreshSession), varargs...)
}

// MockNetWorthServer is a mock of NetWorthServer interface.
type MockNetWorthServer struct {
	ctrl     *gomock.Controller
	recorder *MockNetWorthServerMockRecorder
}

// MockNetWorthServerMockRecorder is the mock recorder for MockNetWorthServer.
type MockNetWorthServerMockRecorder struct {
	mock *MockNetWorthServer
}

// NewMockNetWorthServer creates a new mock instance.
func NewMockNetWorthServer(ctrl *gomock.Controller) *MockNetWorthServer {
	mock := &MockNetWorthServer{ctrl: ctrl}
	mock.recorder = &MockNetWorthServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNetWorthServer) EXPECT() *MockNetWorthServerMockRecorder {
	return m.recorder
}

// CreateNetWorthRefreshSession mocks base method.
func (m *MockNetWorthServer) CreateNetWorthRefreshSession(arg0 context.Context, arg1 *networth.CreateNetWorthRefreshSessionRequest) (*networth.CreateNetWorthRefreshSessionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateNetWorthRefreshSession", arg0, arg1)
	ret0, _ := ret[0].(*networth.CreateNetWorthRefreshSessionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateNetWorthRefreshSession indicates an expected call of CreateNetWorthRefreshSession.
func (mr *MockNetWorthServerMockRecorder) CreateNetWorthRefreshSession(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateNetWorthRefreshSession", reflect.TypeOf((*MockNetWorthServer)(nil).CreateNetWorthRefreshSession), arg0, arg1)
}

// DeclareInvestment mocks base method.
func (m *MockNetWorthServer) DeclareInvestment(arg0 context.Context, arg1 *networth.DeclareInvestmentRequest) (*networth.DeclareInvestmentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeclareInvestment", arg0, arg1)
	ret0, _ := ret[0].(*networth.DeclareInvestmentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeclareInvestment indicates an expected call of DeclareInvestment.
func (mr *MockNetWorthServerMockRecorder) DeclareInvestment(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeclareInvestment", reflect.TypeOf((*MockNetWorthServer)(nil).DeclareInvestment), arg0, arg1)
}

// DeleteAllInvestmentDeclaration mocks base method.
func (m *MockNetWorthServer) DeleteAllInvestmentDeclaration(arg0 context.Context, arg1 *networth.DeleteAllInvestmentDeclarationRequest) (*networth.DeleteAllInvestmentDeclarationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteAllInvestmentDeclaration", arg0, arg1)
	ret0, _ := ret[0].(*networth.DeleteAllInvestmentDeclarationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteAllInvestmentDeclaration indicates an expected call of DeleteAllInvestmentDeclaration.
func (mr *MockNetWorthServerMockRecorder) DeleteAllInvestmentDeclaration(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAllInvestmentDeclaration", reflect.TypeOf((*MockNetWorthServer)(nil).DeleteAllInvestmentDeclaration), arg0, arg1)
}

// DeleteInvestmentDeclaration mocks base method.
func (m *MockNetWorthServer) DeleteInvestmentDeclaration(arg0 context.Context, arg1 *networth.DeleteInvestmentDeclarationRequest) (*networth.DeclareInvestmentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteInvestmentDeclaration", arg0, arg1)
	ret0, _ := ret[0].(*networth.DeclareInvestmentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteInvestmentDeclaration indicates an expected call of DeleteInvestmentDeclaration.
func (mr *MockNetWorthServerMockRecorder) DeleteInvestmentDeclaration(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteInvestmentDeclaration", reflect.TypeOf((*MockNetWorthServer)(nil).DeleteInvestmentDeclaration), arg0, arg1)
}

// DynamicElementCallback mocks base method.
func (m *MockNetWorthServer) DynamicElementCallback(arg0 context.Context, arg1 *dynamic_elements.DynamicElementCallbackRequest) (*dynamic_elements.DynamicElementCallbackResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DynamicElementCallback", arg0, arg1)
	ret0, _ := ret[0].(*dynamic_elements.DynamicElementCallbackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DynamicElementCallback indicates an expected call of DynamicElementCallback.
func (mr *MockNetWorthServerMockRecorder) DynamicElementCallback(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DynamicElementCallback", reflect.TypeOf((*MockNetWorthServer)(nil).DynamicElementCallback), arg0, arg1)
}

// FetchDynamicElements mocks base method.
func (m *MockNetWorthServer) FetchDynamicElements(arg0 context.Context, arg1 *dynamic_elements.FetchDynamicElementsRequest) (*dynamic_elements.FetchDynamicElementsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchDynamicElements", arg0, arg1)
	ret0, _ := ret[0].(*dynamic_elements.FetchDynamicElementsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchDynamicElements indicates an expected call of FetchDynamicElements.
func (mr *MockNetWorthServerMockRecorder) FetchDynamicElements(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchDynamicElements", reflect.TypeOf((*MockNetWorthServer)(nil).FetchDynamicElements), arg0, arg1)
}

// GetAssetsDayChange mocks base method.
func (m *MockNetWorthServer) GetAssetsDayChange(arg0 context.Context, arg1 *networth.GetAssetsDayChangeRequest) (*networth.GetAssetsDayChangeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAssetsDayChange", arg0, arg1)
	ret0, _ := ret[0].(*networth.GetAssetsDayChangeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAssetsDayChange indicates an expected call of GetAssetsDayChange.
func (mr *MockNetWorthServerMockRecorder) GetAssetsDayChange(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAssetsDayChange", reflect.TypeOf((*MockNetWorthServer)(nil).GetAssetsDayChange), arg0, arg1)
}

// GetFilesFromBucket mocks base method.
func (m *MockNetWorthServer) GetFilesFromBucket(arg0 context.Context, arg1 *networth.GetFilesFromBucketRequest) (*networth.GetFilesFromBucketResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFilesFromBucket", arg0, arg1)
	ret0, _ := ret[0].(*networth.GetFilesFromBucketResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFilesFromBucket indicates an expected call of GetFilesFromBucket.
func (mr *MockNetWorthServerMockRecorder) GetFilesFromBucket(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFilesFromBucket", reflect.TypeOf((*MockNetWorthServer)(nil).GetFilesFromBucket), arg0, arg1)
}

// GetInvestmentDeclaration mocks base method.
func (m *MockNetWorthServer) GetInvestmentDeclaration(arg0 context.Context, arg1 *networth.GetInvestmentDeclarationRequest) (*networth.GetInvestmentDeclarationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInvestmentDeclaration", arg0, arg1)
	ret0, _ := ret[0].(*networth.GetInvestmentDeclarationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInvestmentDeclaration indicates an expected call of GetInvestmentDeclaration.
func (mr *MockNetWorthServerMockRecorder) GetInvestmentDeclaration(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInvestmentDeclaration", reflect.TypeOf((*MockNetWorthServer)(nil).GetInvestmentDeclaration), arg0, arg1)
}

// GetInvestmentDeclarations mocks base method.
func (m *MockNetWorthServer) GetInvestmentDeclarations(arg0 context.Context, arg1 *networth.GetInvestmentDeclarationsRequest) (*networth.GetInvestmentDeclarationsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInvestmentDeclarations", arg0, arg1)
	ret0, _ := ret[0].(*networth.GetInvestmentDeclarationsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInvestmentDeclarations indicates an expected call of GetInvestmentDeclarations.
func (mr *MockNetWorthServerMockRecorder) GetInvestmentDeclarations(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInvestmentDeclarations", reflect.TypeOf((*MockNetWorthServer)(nil).GetInvestmentDeclarations), arg0, arg1)
}

// GetNetWorthInstrumentsRefreshDetails mocks base method.
func (m *MockNetWorthServer) GetNetWorthInstrumentsRefreshDetails(arg0 context.Context, arg1 *networth.GetNetWorthInstrumentsRefreshDetailsRequest) (*networth.GetNetWorthInstrumentsRefreshDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNetWorthInstrumentsRefreshDetails", arg0, arg1)
	ret0, _ := ret[0].(*networth.GetNetWorthInstrumentsRefreshDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNetWorthInstrumentsRefreshDetails indicates an expected call of GetNetWorthInstrumentsRefreshDetails.
func (mr *MockNetWorthServerMockRecorder) GetNetWorthInstrumentsRefreshDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNetWorthInstrumentsRefreshDetails", reflect.TypeOf((*MockNetWorthServer)(nil).GetNetWorthInstrumentsRefreshDetails), arg0, arg1)
}

// GetNetWorthRefreshSession mocks base method.
func (m *MockNetWorthServer) GetNetWorthRefreshSession(arg0 context.Context, arg1 *networth.GetNetWorthRefreshSessionRequest) (*networth.GetNetWorthRefreshSessionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNetWorthRefreshSession", arg0, arg1)
	ret0, _ := ret[0].(*networth.GetNetWorthRefreshSessionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNetWorthRefreshSession indicates an expected call of GetNetWorthRefreshSession.
func (mr *MockNetWorthServerMockRecorder) GetNetWorthRefreshSession(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNetWorthRefreshSession", reflect.TypeOf((*MockNetWorthServer)(nil).GetNetWorthRefreshSession), arg0, arg1)
}

// GetNetWorthValue mocks base method.
func (m *MockNetWorthServer) GetNetWorthValue(arg0 context.Context, arg1 *networth.GetNetWorthValueRequest) (*networth.GetNetWorthValueResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNetWorthValue", arg0, arg1)
	ret0, _ := ret[0].(*networth.GetNetWorthValueResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNetWorthValue indicates an expected call of GetNetWorthValue.
func (mr *MockNetWorthServerMockRecorder) GetNetWorthValue(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNetWorthValue", reflect.TypeOf((*MockNetWorthServer)(nil).GetNetWorthValue), arg0, arg1)
}

// GetNetworthDataFile mocks base method.
func (m *MockNetWorthServer) GetNetworthDataFile(arg0 context.Context, arg1 *networth.GetNetworthDataFileRequest) (*networth.GetNetworthDataFileResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNetworthDataFile", arg0, arg1)
	ret0, _ := ret[0].(*networth.GetNetworthDataFileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNetworthDataFile indicates an expected call of GetNetworthDataFile.
func (mr *MockNetWorthServerMockRecorder) GetNetworthDataFile(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNetworthDataFile", reflect.TypeOf((*MockNetWorthServer)(nil).GetNetworthDataFile), arg0, arg1)
}

// GetPortfolioChangeSummary mocks base method.
func (m *MockNetWorthServer) GetPortfolioChangeSummary(arg0 context.Context, arg1 *networth.GetPortfolioChangeSummaryRequest) (*networth.GetPortfolioChangeSummaryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPortfolioChangeSummary", arg0, arg1)
	ret0, _ := ret[0].(*networth.GetPortfolioChangeSummaryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPortfolioChangeSummary indicates an expected call of GetPortfolioChangeSummary.
func (mr *MockNetWorthServerMockRecorder) GetPortfolioChangeSummary(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPortfolioChangeSummary", reflect.TypeOf((*MockNetWorthServer)(nil).GetPortfolioChangeSummary), arg0, arg1)
}

// MagicImportFiles mocks base method.
func (m *MockNetWorthServer) MagicImportFiles(arg0 context.Context, arg1 *networth.MagicImportFilesRequest) (*networth.MagicImportFilesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MagicImportFiles", arg0, arg1)
	ret0, _ := ret[0].(*networth.MagicImportFilesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MagicImportFiles indicates an expected call of MagicImportFiles.
func (mr *MockNetWorthServerMockRecorder) MagicImportFiles(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MagicImportFiles", reflect.TypeOf((*MockNetWorthServer)(nil).MagicImportFiles), arg0, arg1)
}

// SearchAssetFormFieldOptions mocks base method.
func (m *MockNetWorthServer) SearchAssetFormFieldOptions(arg0 context.Context, arg1 *networth.SearchAssetFormFieldOptionsRequest) (*networth.SearchAssetFormFieldOptionsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchAssetFormFieldOptions", arg0, arg1)
	ret0, _ := ret[0].(*networth.SearchAssetFormFieldOptionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchAssetFormFieldOptions indicates an expected call of SearchAssetFormFieldOptions.
func (mr *MockNetWorthServerMockRecorder) SearchAssetFormFieldOptions(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchAssetFormFieldOptions", reflect.TypeOf((*MockNetWorthServer)(nil).SearchAssetFormFieldOptions), arg0, arg1)
}

// StoreSnapshot mocks base method.
func (m *MockNetWorthServer) StoreSnapshot(arg0 context.Context, arg1 *networth.StoreSnapshotRequest) (*networth.StoreSnapshotResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StoreSnapshot", arg0, arg1)
	ret0, _ := ret[0].(*networth.StoreSnapshotResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StoreSnapshot indicates an expected call of StoreSnapshot.
func (mr *MockNetWorthServerMockRecorder) StoreSnapshot(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StoreSnapshot", reflect.TypeOf((*MockNetWorthServer)(nil).StoreSnapshot), arg0, arg1)
}

// UpdateBulkManualAssetsCurrentValue mocks base method.
func (m *MockNetWorthServer) UpdateBulkManualAssetsCurrentValue(arg0 context.Context, arg1 *networth.UpdateBulkManualAssetsCurrentValueRequest) (*networth.UpdateBulkManualAssetsCurrentValueResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateBulkManualAssetsCurrentValue", arg0, arg1)
	ret0, _ := ret[0].(*networth.UpdateBulkManualAssetsCurrentValueResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateBulkManualAssetsCurrentValue indicates an expected call of UpdateBulkManualAssetsCurrentValue.
func (mr *MockNetWorthServerMockRecorder) UpdateBulkManualAssetsCurrentValue(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBulkManualAssetsCurrentValue", reflect.TypeOf((*MockNetWorthServer)(nil).UpdateBulkManualAssetsCurrentValue), arg0, arg1)
}

// UpdateInvestmentDeclaration mocks base method.
func (m *MockNetWorthServer) UpdateInvestmentDeclaration(arg0 context.Context, arg1 *networth.UpdateInvestmentDeclarationRequest) (*networth.UpdateInvestmentDeclarationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInvestmentDeclaration", arg0, arg1)
	ret0, _ := ret[0].(*networth.UpdateInvestmentDeclarationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateInvestmentDeclaration indicates an expected call of UpdateInvestmentDeclaration.
func (mr *MockNetWorthServerMockRecorder) UpdateInvestmentDeclaration(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInvestmentDeclaration", reflect.TypeOf((*MockNetWorthServer)(nil).UpdateInvestmentDeclaration), arg0, arg1)
}

// UpdateNetWorthRefreshSession mocks base method.
func (m *MockNetWorthServer) UpdateNetWorthRefreshSession(arg0 context.Context, arg1 *networth.UpdateNetWorthRefreshSessionRequest) (*networth.UpdateNetWorthRefreshSessionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateNetWorthRefreshSession", arg0, arg1)
	ret0, _ := ret[0].(*networth.UpdateNetWorthRefreshSessionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateNetWorthRefreshSession indicates an expected call of UpdateNetWorthRefreshSession.
func (mr *MockNetWorthServerMockRecorder) UpdateNetWorthRefreshSession(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateNetWorthRefreshSession", reflect.TypeOf((*MockNetWorthServer)(nil).UpdateNetWorthRefreshSession), arg0, arg1)
}

// MockUnsafeNetWorthServer is a mock of UnsafeNetWorthServer interface.
type MockUnsafeNetWorthServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeNetWorthServerMockRecorder
}

// MockUnsafeNetWorthServerMockRecorder is the mock recorder for MockUnsafeNetWorthServer.
type MockUnsafeNetWorthServerMockRecorder struct {
	mock *MockUnsafeNetWorthServer
}

// NewMockUnsafeNetWorthServer creates a new mock instance.
func NewMockUnsafeNetWorthServer(ctrl *gomock.Controller) *MockUnsafeNetWorthServer {
	mock := &MockUnsafeNetWorthServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeNetWorthServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeNetWorthServer) EXPECT() *MockUnsafeNetWorthServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedNetWorthServer mocks base method.
func (m *MockUnsafeNetWorthServer) mustEmbedUnimplementedNetWorthServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedNetWorthServer")
}

// mustEmbedUnimplementedNetWorthServer indicates an expected call of mustEmbedUnimplementedNetWorthServer.
func (mr *MockUnsafeNetWorthServerMockRecorder) mustEmbedUnimplementedNetWorthServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedNetWorthServer", reflect.TypeOf((*MockUnsafeNetWorthServer)(nil).mustEmbedUnimplementedNetWorthServer))
}
