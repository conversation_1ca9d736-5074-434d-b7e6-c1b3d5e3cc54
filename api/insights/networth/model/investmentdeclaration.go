package model

import (
	"github.com/shopspring/decimal"
	"google.golang.org/genproto/googleapis/type/date"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/pkg/money"
)

func (p *OtherDeclarationDetails_PrivateEquity) GetCurrentValue() *moneyPb.Money {
	return p.PrivateEquity.GetCurrentValue()
}

func (p *OtherDeclarationDetails_RealEstate) GetCurrentValue() *moneyPb.Money {
	return p.RealEstate.GetCurrentValue()
}

func (p *OtherDeclarationDetails_Aif) GetCurrentValue() *moneyPb.Money {
	return p.Aif.GetCurrentValue()
}

func (p *OtherDeclarationDetails_ArtAndArtefacts) GetCurrentValue() *moneyPb.Money {
	return p.ArtAndArtefacts.GetCurrentValue()
}

func (p *OtherDeclarationDetails_Cash) GetCurrentValue() *moneyPb.Money {
	return p.Cash.GetCurrentAmount()
}

func (p *OtherDeclarationDetails_PortfolioManagementService) GetCurrentValue() *moneyPb.Money {
	return p.PortfolioManagementService.GetCurrentValue()
}

type GenericInvestmentDetails interface {
	GetInvestmentName() string
	GetInvestmentDate() *date.Date
	GetInvestedValue() *moneyPb.Money
}

type CurrentValue interface {
	GetCurrentValue() *moneyPb.Money
}

// art & artefacts
func (aa *OtherDeclarationDetails_ArtAndArtefacts) GetInvestmentName() string {
	return aa.ArtAndArtefacts.GetInvestmentName()
}

func (aa *OtherDeclarationDetails_ArtAndArtefacts) GetInvestmentDate() *date.Date {
	return aa.ArtAndArtefacts.GetPurchaseDate()
}

func (aa *OtherDeclarationDetails_ArtAndArtefacts) GetInvestedValue() *moneyPb.Money {
	return aa.ArtAndArtefacts.GetInvestedValue()
}

// bond
func (b *OtherDeclarationDetails_Bond) GetInvestmentName() string {
	return b.Bond.GetInvestmentName()
}

func (a *OtherDeclarationDetails_Bond) GetInvestmentDate() *date.Date {
	return a.Bond.GetInvestmentDate()
}

func (b *OtherDeclarationDetails_Bond) GetInvestedValue() *moneyPb.Money {
	decimalMoney := money.ToDecimal(b.Bond.GetInvestedValuePerUnit()).Mul(decimal.NewFromInt(b.Bond.GetNumberOfUnits()))
	return money.ParseDecimal(decimalMoney, b.Bond.GetInvestedValuePerUnit().GetCurrencyCode())
}

// digital gold
func (g *OtherDeclarationDetails_DigitalGold) GetInvestmentName() string {
	return g.DigitalGold.GetInvestmentName()
}

func (g *OtherDeclarationDetails_DigitalGold) GetInvestmentDate() *date.Date {
	return g.DigitalGold.GetInvestmentDate()
}

func (g *OtherDeclarationDetails_DigitalGold) GetInvestedValue() *moneyPb.Money {
	return g.DigitalGold.GetInvestedValue()
}

// digital silver
func (s *OtherDeclarationDetails_DigitalSilver) GetInvestmentName() string {
	return s.DigitalSilver.GetInvestmentName()
}

func (s *OtherDeclarationDetails_DigitalSilver) GetInvestmentDate() *date.Date {
	return s.DigitalSilver.GetInvestmentDate()
}

func (s *OtherDeclarationDetails_DigitalSilver) GetInvestedValue() *moneyPb.Money {
	return s.DigitalSilver.GetInvestedValue()
}

// private equity
func (p *OtherDeclarationDetails_PrivateEquity) GetInvestmentName() string {
	return p.PrivateEquity.GetInvestmentName()
}

func (p *OtherDeclarationDetails_PrivateEquity) GetInvestmentDate() *date.Date {
	return p.PrivateEquity.GetInvestmentDate()
}

func (p *OtherDeclarationDetails_PrivateEquity) GetInvestedValue() *moneyPb.Money {
	return p.PrivateEquity.GetInvestedValue()
}

// real estate
func (r *OtherDeclarationDetails_RealEstate) GetInvestmentName() string {
	return r.RealEstate.GetInvestmentName()
}

func (r *OtherDeclarationDetails_RealEstate) GetInvestmentDate() *date.Date {
	return r.RealEstate.GetInvestmentDate()
}

func (r *OtherDeclarationDetails_RealEstate) GetInvestedValue() *moneyPb.Money {
	return r.RealEstate.GetInvestedValue()
}

// pms
func (a *OtherDeclarationDetails_PortfolioManagementService) GetInvestmentName() string {
	return a.PortfolioManagementService.GetPmsName()
}

func (a *OtherDeclarationDetails_PortfolioManagementService) GetInvestmentDate() *date.Date {
	return a.PortfolioManagementService.GetInvestmentDate()
}

func (a *OtherDeclarationDetails_PortfolioManagementService) GetInvestedValue() *moneyPb.Money {
	return a.PortfolioManagementService.GetInvestedValue()
}

// cash
func (r *OtherDeclarationDetails_Cash) GetInvestmentName() string {
	if r.Cash.GetName() == "" {
		return "Cash"
	}
	return r.Cash.GetName()
}

func (r *OtherDeclarationDetails_Cash) GetInvestmentDate() *date.Date {
	return nil
}

func (r *OtherDeclarationDetails_Cash) GetInvestedValue() *moneyPb.Money {
	return r.Cash.GetCurrentAmount()
}

// gadgets
func (r *OtherDeclarationDetails_Gadgets) GetInvestmentName() string {
	if r.Gadgets.GetInvestmentName() == "" {
		return "Gadget"
	}
	return r.Gadgets.GetInvestmentName()
}

func (r *OtherDeclarationDetails_Gadgets) GetInvestmentDate() *date.Date {
	return nil
}

func (r *OtherDeclarationDetails_Gadgets) GetInvestedValue() *moneyPb.Money {
	return r.Gadgets.GetCurrentValue()
}

func (r *OtherDeclarationDetails_Gadgets) GetCurrentValue() *moneyPb.Money {
	return r.Gadgets.GetCurrentValue()
}

// vehicle
func (r *OtherDeclarationDetails_Vehicle) GetInvestmentName() string {
	if r.Vehicle.GetInvestmentName() == "" {
		return "Vehicle"
	}
	return r.Vehicle.GetInvestmentName()
}

func (r *OtherDeclarationDetails_Vehicle) GetInvestmentDate() *date.Date {
	return nil
}

func (r *OtherDeclarationDetails_Vehicle) GetInvestedValue() *moneyPb.Money {
	return r.Vehicle.GetCurrentValue()
}

func (r *OtherDeclarationDetails_Vehicle) GetCurrentValue() *moneyPb.Money {
	return r.Vehicle.GetCurrentValue()
}

// crypto
func (r *OtherDeclarationDetails_Crypto) GetInvestmentName() string {
	if r.Crypto.GetInvestmentName() == "" {
		return "Crypto"
	}
	return r.Crypto.GetInvestmentName()
}

func (r *OtherDeclarationDetails_Crypto) GetInvestmentDate() *date.Date {
	return nil
}

func (r *OtherDeclarationDetails_Crypto) GetInvestedValue() *moneyPb.Money {
	return r.Crypto.GetInvestedValue()
}

func (r *OtherDeclarationDetails_Crypto) GetCurrentValue() *moneyPb.Money {
	return r.Crypto.GetCurrentValue()
}

// furniture
func (r *OtherDeclarationDetails_Furniture) GetInvestmentName() string {
	if r.Furniture.GetInvestmentName() == "" {
		return "Furniture"
	}
	return r.Furniture.GetInvestmentName()
}

func (r *OtherDeclarationDetails_Furniture) GetInvestmentDate() *date.Date {
	return nil
}

func (r *OtherDeclarationDetails_Furniture) GetInvestedValue() *moneyPb.Money {
	return r.Furniture.GetCurrentValue()
}

func (r *OtherDeclarationDetails_Furniture) GetCurrentValue() *moneyPb.Money {
	return r.Furniture.GetCurrentValue()
}

// collectible
func (r *OtherDeclarationDetails_Collectible) GetInvestmentName() string {
	if r.Collectible.GetInvestmentName() == "" {
		return "Collectible"
	}
	return r.Collectible.GetInvestmentName()
}

func (r *OtherDeclarationDetails_Collectible) GetInvestmentDate() *date.Date {
	return nil
}

func (r *OtherDeclarationDetails_Collectible) GetInvestedValue() *moneyPb.Money {
	return r.Collectible.GetCurrentValue()
}

func (r *OtherDeclarationDetails_Collectible) GetCurrentValue() *moneyPb.Money {
	return r.Collectible.GetCurrentValue()
}

// jewellery
func (r *OtherDeclarationDetails_Jewellery) GetInvestmentName() string {
	if r.Jewellery.GetInvestmentName() == "" {
		return "Jewellery"
	}
	return r.Jewellery.GetInvestmentName()
}

func (r *OtherDeclarationDetails_Jewellery) GetInvestmentDate() *date.Date {
	return nil
}

func (r *OtherDeclarationDetails_Jewellery) GetInvestedValue() *moneyPb.Money {
	return r.Jewellery.GetCurrentValue()
}

func (r *OtherDeclarationDetails_Jewellery) GetCurrentValue() *moneyPb.Money {
	return r.Jewellery.GetCurrentValue()
}

// others
func (r *OtherDeclarationDetails_Others) GetInvestmentName() string {
	if r.Others.GetInvestmentName() == "" {
		return "Other Investment"
	}
	return r.Others.GetInvestmentName()
}

func (r *OtherDeclarationDetails_Others) GetInvestmentDate() *date.Date {
	return nil
}

func (r *OtherDeclarationDetails_Others) GetInvestedValue() *moneyPb.Money {
	return r.Others.GetCurrentValue()
}

func (r *OtherDeclarationDetails_Others) GetCurrentValue() *moneyPb.Money {
	return r.Others.GetCurrentValue()
}

func (r *OtherDeclarationDetails_IndianStockDetails) GetInvestmentName() string {
	return r.IndianStockDetails.GetStockName()
}

func (r *OtherDeclarationDetails_IndianStockDetails) GetInvestmentDate() *date.Date {
	return r.IndianStockDetails.GetInvestmentDate()
}

func (r *OtherDeclarationDetails_IndianStockDetails) GetInvestedValue() *moneyPb.Money {
	decimalMoney := money.ToDecimal(r.IndianStockDetails.GetInvestedValuePerUnit()).Mul(decimal.NewFromFloat(r.IndianStockDetails.GetNumberOfUnits()))
	return money.ParseDecimal(decimalMoney, r.IndianStockDetails.GetInvestedValuePerUnit().GetCurrencyCode())
}

func (r *OtherDeclarationDetails_IndianStockDetails) GetCurrentValue() *moneyPb.Money {
	decimalMoney := money.ToDecimal(r.IndianStockDetails.GetCurrentValuePerUnit()).Mul(decimal.NewFromFloat(r.IndianStockDetails.GetNumberOfUnits()))
	return money.ParseDecimal(decimalMoney, r.IndianStockDetails.GetCurrentValuePerUnit().GetCurrencyCode())
}

func (r *OtherDeclarationDetails_MutualFundDetails) GetInvestmentName() string {
	return r.MutualFundDetails.GetMutualFundName()
}

func (r *OtherDeclarationDetails_MutualFundDetails) GetInvestmentDate() *date.Date {
	return r.MutualFundDetails.GetInvestmentDate()
}

func (r *OtherDeclarationDetails_MutualFundDetails) GetInvestedValue() *moneyPb.Money {
	decimalMoney := money.ToDecimal(r.MutualFundDetails.GetInvestedValuePerUnit()).Mul(decimal.NewFromFloat(r.MutualFundDetails.GetNumberOfUnits()))
	return money.ParseDecimal(decimalMoney, r.MutualFundDetails.GetInvestedValuePerUnit().GetCurrencyCode())
}

func (r *OtherDeclarationDetails_MutualFundDetails) GetCurrentValue() *moneyPb.Money {
	decimalMoney := money.ToDecimal(r.MutualFundDetails.GetCurrentValuePerUnit()).Mul(decimal.NewFromFloat(r.MutualFundDetails.GetNumberOfUnits()))
	return money.ParseDecimal(decimalMoney, r.MutualFundDetails.GetCurrentValuePerUnit().GetCurrencyCode())
}

func (r *OtherDeclarationDetails_NpsDetails) GetInvestmentName() string {
	return r.NpsDetails.GetSchemeName()
}

func (r *OtherDeclarationDetails_NpsDetails) GetInvestmentDate() *date.Date {
	return r.NpsDetails.GetInvestmentDate()
}

func (r *OtherDeclarationDetails_NpsDetails) GetInvestedValue() *moneyPb.Money {
	return r.NpsDetails.GetInvestedValue()
}

func (r *OtherDeclarationDetails_NpsDetails) GetCurrentValue() *moneyPb.Money {
	return r.NpsDetails.GetCurrentValue()
}
func (r *OtherDeclarationDetails_PublicProvidentFund) GetInvestmentName() string {
	return r.PublicProvidentFund.GetInvestmentName()
}

func (r *OtherDeclarationDetails_PublicProvidentFund) GetInvestmentDate() *date.Date {
	return r.PublicProvidentFund.GetInvestmentDate()
}

func (r *OtherDeclarationDetails_PublicProvidentFund) GetInvestedValue() *moneyPb.Money {
	return r.PublicProvidentFund.GetCurrentValue()
}

func (r *OtherDeclarationDetails_PublicProvidentFund) GetCurrentValue() *moneyPb.Money {
	return r.PublicProvidentFund.GetCurrentValue()
}

func (r *OtherDeclarationDetails_EmployeeStockOption) GetInvestmentName() string {
	if r.EmployeeStockOption.GetCompanyName() == "" {
		return "Employee Stock Option"
	}
	return r.EmployeeStockOption.GetCompanyName()
}

func (r *OtherDeclarationDetails_EmployeeStockOption) GetInvestmentDate() *date.Date {
	return r.EmployeeStockOption.GetFirstIssueDate()
}

func (r *OtherDeclarationDetails_EmployeeStockOption) GetInvestedValue() *moneyPb.Money {
	decimalMoney := money.ToDecimal(r.EmployeeStockOption.GetCurrentValuePerShare()).Mul(decimal.NewFromInt(r.EmployeeStockOption.GetEsopInGrant()))
	return money.ParseDecimal(decimalMoney, r.EmployeeStockOption.GetCurrentValuePerShare().GetCurrencyCode())
}

// Fixed Deposit
func (r *OtherDeclarationDetails_FixedDepositDeclarationDetails) GetInvestmentName() string {
	if r.FixedDepositDeclarationDetails.GetDepositName() == "" {
		return "Fixed Deposit"
	}
	return r.FixedDepositDeclarationDetails.GetDepositName()
}

func (r *OtherDeclarationDetails_FixedDepositDeclarationDetails) GetInvestmentDate() *date.Date {
	// todo: create new fields for FD migration
	return nil
}

func (r *OtherDeclarationDetails_FixedDepositDeclarationDetails) GetInvestedValue() *moneyPb.Money {
	// todo: create new fields for RD migration
	return nil
}

// Recurring Deposit
func (r *OtherDeclarationDetails_RecurringDepositDeclarationDetails) GetInvestmentName() string {
	if r.RecurringDepositDeclarationDetails.GetDepositName() == "" {
		return "Recurring Deposit"
	}
	return r.RecurringDepositDeclarationDetails.GetDepositName()
}

func (r *OtherDeclarationDetails_RecurringDepositDeclarationDetails) GetInvestmentDate() *date.Date {
	// todo: create new fields for FD migration
	return nil
}

func (r *OtherDeclarationDetails_RecurringDepositDeclarationDetails) GetInvestedValue() *moneyPb.Money {
	// todo: create new fields for FD migration
	return nil
}
