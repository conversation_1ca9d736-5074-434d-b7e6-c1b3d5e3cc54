//go:generate gen_sql -types=InvestmentInstrument,OtherDeclarationDetails,Source

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/insights/networth/model/investmentdeclaration.proto

package model

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	date "google.golang.org/genproto/googleapis/type/date"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Source differentiates the existing ways of declaring investments, eg. Manual user addition, Magic import, etc.
type Source int32

const (
	Source_SOURCE_UNSPECIFIED  Source = 0
	Source_SOURCE_MAGIC_IMPORT Source = 1
)

// Enum value maps for Source.
var (
	Source_name = map[int32]string{
		0: "SOURCE_UNSPECIFIED",
		1: "SOURCE_MAGIC_IMPORT",
	}
	Source_value = map[string]int32{
		"SOURCE_UNSPECIFIED":  0,
		"SOURCE_MAGIC_IMPORT": 1,
	}
)

func (x Source) Enum() *Source {
	p := new(Source)
	*p = x
	return p
}

func (x Source) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Source) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_networth_model_investmentdeclaration_proto_enumTypes[0].Descriptor()
}

func (Source) Type() protoreflect.EnumType {
	return &file_api_insights_networth_model_investmentdeclaration_proto_enumTypes[0]
}

func (x Source) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Source.Descriptor instead.
func (Source) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_networth_model_investmentdeclaration_proto_rawDescGZIP(), []int{0}
}

type GoldCarat int32

const (
	GoldCarat_GOLD_CARAT_UNSPECIFIED GoldCarat = 0
	GoldCarat_GOLD_CARAT_24          GoldCarat = 1
	GoldCarat_GOLD_CARAT_22          GoldCarat = 2
	GoldCarat_GOLD_CARAT_18          GoldCarat = 3
)

// Enum value maps for GoldCarat.
var (
	GoldCarat_name = map[int32]string{
		0: "GOLD_CARAT_UNSPECIFIED",
		1: "GOLD_CARAT_24",
		2: "GOLD_CARAT_22",
		3: "GOLD_CARAT_18",
	}
	GoldCarat_value = map[string]int32{
		"GOLD_CARAT_UNSPECIFIED": 0,
		"GOLD_CARAT_24":          1,
		"GOLD_CARAT_22":          2,
		"GOLD_CARAT_18":          3,
	}
)

func (x GoldCarat) Enum() *GoldCarat {
	p := new(GoldCarat)
	*p = x
	return p
}

func (x GoldCarat) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GoldCarat) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_networth_model_investmentdeclaration_proto_enumTypes[1].Descriptor()
}

func (GoldCarat) Type() protoreflect.EnumType {
	return &file_api_insights_networth_model_investmentdeclaration_proto_enumTypes[1]
}

func (x GoldCarat) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GoldCarat.Descriptor instead.
func (GoldCarat) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_networth_model_investmentdeclaration_proto_rawDescGZIP(), []int{1}
}

type CompoundingFrequency int32

const (
	CompoundingFrequency_COMPOUNDING_FREQUENCY_UNSPECIFIED CompoundingFrequency = 0
	CompoundingFrequency_COMPOUNDING_FREQUENCY_DAILY       CompoundingFrequency = 1
	CompoundingFrequency_COMPOUNDING_FREQUENCY_MONTHLY     CompoundingFrequency = 2
	CompoundingFrequency_COMPOUNDING_FREQUENCY_YEARLY      CompoundingFrequency = 3
	CompoundingFrequency_COMPOUNDING_FREQUENCY_QUARTERLY   CompoundingFrequency = 4
	CompoundingFrequency_COMPOUNDING_FREQUENCY_HALF_YEARLY CompoundingFrequency = 5
	// interest is compounded on maturity of the investment
	CompoundingFrequency_COMPOUNDING_FREQUENCY_AT_MATURITY CompoundingFrequency = 6
)

// Enum value maps for CompoundingFrequency.
var (
	CompoundingFrequency_name = map[int32]string{
		0: "COMPOUNDING_FREQUENCY_UNSPECIFIED",
		1: "COMPOUNDING_FREQUENCY_DAILY",
		2: "COMPOUNDING_FREQUENCY_MONTHLY",
		3: "COMPOUNDING_FREQUENCY_YEARLY",
		4: "COMPOUNDING_FREQUENCY_QUARTERLY",
		5: "COMPOUNDING_FREQUENCY_HALF_YEARLY",
		6: "COMPOUNDING_FREQUENCY_AT_MATURITY",
	}
	CompoundingFrequency_value = map[string]int32{
		"COMPOUNDING_FREQUENCY_UNSPECIFIED": 0,
		"COMPOUNDING_FREQUENCY_DAILY":       1,
		"COMPOUNDING_FREQUENCY_MONTHLY":     2,
		"COMPOUNDING_FREQUENCY_YEARLY":      3,
		"COMPOUNDING_FREQUENCY_QUARTERLY":   4,
		"COMPOUNDING_FREQUENCY_HALF_YEARLY": 5,
		"COMPOUNDING_FREQUENCY_AT_MATURITY": 6,
	}
)

func (x CompoundingFrequency) Enum() *CompoundingFrequency {
	p := new(CompoundingFrequency)
	*p = x
	return p
}

func (x CompoundingFrequency) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CompoundingFrequency) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_networth_model_investmentdeclaration_proto_enumTypes[2].Descriptor()
}

func (CompoundingFrequency) Type() protoreflect.EnumType {
	return &file_api_insights_networth_model_investmentdeclaration_proto_enumTypes[2]
}

func (x CompoundingFrequency) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CompoundingFrequency.Descriptor instead.
func (CompoundingFrequency) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_networth_model_investmentdeclaration_proto_rawDescGZIP(), []int{2}
}

type InvestmentDeclarationFieldMask int32

const (
	InvestmentDeclarationFieldMask_INVESTMENT_DECLARATION_FIELD_MASKS_UNSPECIFIED         InvestmentDeclarationFieldMask = 0
	InvestmentDeclarationFieldMask_INVESTMENT_DECLARATION_FIELD_MASKS_ID                  InvestmentDeclarationFieldMask = 1
	InvestmentDeclarationFieldMask_INVESTMENT_DECLARATION_FIELD_MASKS_ACTOR_ID            InvestmentDeclarationFieldMask = 2
	InvestmentDeclarationFieldMask_INVESTMENT_DECLARATION_FIELD_MASKS_INSTRUMENT_TYPE     InvestmentDeclarationFieldMask = 3
	InvestmentDeclarationFieldMask_INVESTMENT_DECLARATION_FIELD_MASKS_INVESTED_AMOUNT     InvestmentDeclarationFieldMask = 4
	InvestmentDeclarationFieldMask_INVESTMENT_DECLARATION_FIELD_MASKS_INVESTED_AT         InvestmentDeclarationFieldMask = 5
	InvestmentDeclarationFieldMask_INVESTMENT_DECLARATION_FIELD_MASKS_MATURITY_TIME       InvestmentDeclarationFieldMask = 6
	InvestmentDeclarationFieldMask_INVESTMENT_DECLARATION_FIELD_MASKS_INTEREST_RATE       InvestmentDeclarationFieldMask = 7
	InvestmentDeclarationFieldMask_INVESTMENT_DECLARATION_FIELD_MASKS_DECLARATION_DETAILS InvestmentDeclarationFieldMask = 8
	InvestmentDeclarationFieldMask_INVESTMENT_DECLARATION_FIELD_MASKS_CREATED_AT          InvestmentDeclarationFieldMask = 9
	InvestmentDeclarationFieldMask_INVESTMENT_DECLARATION_FIELD_MASKS_UPDATED_AT          InvestmentDeclarationFieldMask = 10
	InvestmentDeclarationFieldMask_INVESTMENT_DECLARATION_FIELD_MASKS_DELETED_AT          InvestmentDeclarationFieldMask = 11
	InvestmentDeclarationFieldMask_INVESTMENT_DECLARATION_FIELD_MASKS_EXTERNAL_ID         InvestmentDeclarationFieldMask = 12
	InvestmentDeclarationFieldMask_INVESTMENT_DECLARATION_FIELD_MASKS_CONSENT_ID          InvestmentDeclarationFieldMask = 13
)

// Enum value maps for InvestmentDeclarationFieldMask.
var (
	InvestmentDeclarationFieldMask_name = map[int32]string{
		0:  "INVESTMENT_DECLARATION_FIELD_MASKS_UNSPECIFIED",
		1:  "INVESTMENT_DECLARATION_FIELD_MASKS_ID",
		2:  "INVESTMENT_DECLARATION_FIELD_MASKS_ACTOR_ID",
		3:  "INVESTMENT_DECLARATION_FIELD_MASKS_INSTRUMENT_TYPE",
		4:  "INVESTMENT_DECLARATION_FIELD_MASKS_INVESTED_AMOUNT",
		5:  "INVESTMENT_DECLARATION_FIELD_MASKS_INVESTED_AT",
		6:  "INVESTMENT_DECLARATION_FIELD_MASKS_MATURITY_TIME",
		7:  "INVESTMENT_DECLARATION_FIELD_MASKS_INTEREST_RATE",
		8:  "INVESTMENT_DECLARATION_FIELD_MASKS_DECLARATION_DETAILS",
		9:  "INVESTMENT_DECLARATION_FIELD_MASKS_CREATED_AT",
		10: "INVESTMENT_DECLARATION_FIELD_MASKS_UPDATED_AT",
		11: "INVESTMENT_DECLARATION_FIELD_MASKS_DELETED_AT",
		12: "INVESTMENT_DECLARATION_FIELD_MASKS_EXTERNAL_ID",
		13: "INVESTMENT_DECLARATION_FIELD_MASKS_CONSENT_ID",
	}
	InvestmentDeclarationFieldMask_value = map[string]int32{
		"INVESTMENT_DECLARATION_FIELD_MASKS_UNSPECIFIED":         0,
		"INVESTMENT_DECLARATION_FIELD_MASKS_ID":                  1,
		"INVESTMENT_DECLARATION_FIELD_MASKS_ACTOR_ID":            2,
		"INVESTMENT_DECLARATION_FIELD_MASKS_INSTRUMENT_TYPE":     3,
		"INVESTMENT_DECLARATION_FIELD_MASKS_INVESTED_AMOUNT":     4,
		"INVESTMENT_DECLARATION_FIELD_MASKS_INVESTED_AT":         5,
		"INVESTMENT_DECLARATION_FIELD_MASKS_MATURITY_TIME":       6,
		"INVESTMENT_DECLARATION_FIELD_MASKS_INTEREST_RATE":       7,
		"INVESTMENT_DECLARATION_FIELD_MASKS_DECLARATION_DETAILS": 8,
		"INVESTMENT_DECLARATION_FIELD_MASKS_CREATED_AT":          9,
		"INVESTMENT_DECLARATION_FIELD_MASKS_UPDATED_AT":          10,
		"INVESTMENT_DECLARATION_FIELD_MASKS_DELETED_AT":          11,
		"INVESTMENT_DECLARATION_FIELD_MASKS_EXTERNAL_ID":         12,
		"INVESTMENT_DECLARATION_FIELD_MASKS_CONSENT_ID":          13,
	}
)

func (x InvestmentDeclarationFieldMask) Enum() *InvestmentDeclarationFieldMask {
	p := new(InvestmentDeclarationFieldMask)
	*p = x
	return p
}

func (x InvestmentDeclarationFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InvestmentDeclarationFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_networth_model_investmentdeclaration_proto_enumTypes[3].Descriptor()
}

func (InvestmentDeclarationFieldMask) Type() protoreflect.EnumType {
	return &file_api_insights_networth_model_investmentdeclaration_proto_enumTypes[3]
}

func (x InvestmentDeclarationFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InvestmentDeclarationFieldMask.Descriptor instead.
func (InvestmentDeclarationFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_networth_model_investmentdeclaration_proto_rawDescGZIP(), []int{3}
}

type InvestmentFrequency int32

const (
	InvestmentFrequency_INVESTMENT_FREQUENCY_UNSPECIFIED InvestmentFrequency = 0
	InvestmentFrequency_INVESTMENT_FREQUENCY_ONE_TIME    InvestmentFrequency = 1
	InvestmentFrequency_INVESTMENT_FREQUENCY_MONTHLY     InvestmentFrequency = 2
	InvestmentFrequency_INVESTMENT_FREQUENCY_QUARTERLY   InvestmentFrequency = 3
	InvestmentFrequency_INVESTMENT_FREQUENCY_HALF_YEARLY InvestmentFrequency = 4
	InvestmentFrequency_INVESTMENT_FREQUENCY_ANNUAL      InvestmentFrequency = 5
)

// Enum value maps for InvestmentFrequency.
var (
	InvestmentFrequency_name = map[int32]string{
		0: "INVESTMENT_FREQUENCY_UNSPECIFIED",
		1: "INVESTMENT_FREQUENCY_ONE_TIME",
		2: "INVESTMENT_FREQUENCY_MONTHLY",
		3: "INVESTMENT_FREQUENCY_QUARTERLY",
		4: "INVESTMENT_FREQUENCY_HALF_YEARLY",
		5: "INVESTMENT_FREQUENCY_ANNUAL",
	}
	InvestmentFrequency_value = map[string]int32{
		"INVESTMENT_FREQUENCY_UNSPECIFIED": 0,
		"INVESTMENT_FREQUENCY_ONE_TIME":    1,
		"INVESTMENT_FREQUENCY_MONTHLY":     2,
		"INVESTMENT_FREQUENCY_QUARTERLY":   3,
		"INVESTMENT_FREQUENCY_HALF_YEARLY": 4,
		"INVESTMENT_FREQUENCY_ANNUAL":      5,
	}
)

func (x InvestmentFrequency) Enum() *InvestmentFrequency {
	p := new(InvestmentFrequency)
	*p = x
	return p
}

func (x InvestmentFrequency) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InvestmentFrequency) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_networth_model_investmentdeclaration_proto_enumTypes[4].Descriptor()
}

func (InvestmentFrequency) Type() protoreflect.EnumType {
	return &file_api_insights_networth_model_investmentdeclaration_proto_enumTypes[4]
}

func (x InvestmentFrequency) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InvestmentFrequency.Descriptor instead.
func (InvestmentFrequency) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_networth_model_investmentdeclaration_proto_rawDescGZIP(), []int{4}
}

// InterestAccumulationFrequency is used to get interest accrual period (number of times interest is calculated per year)
type InterestAccumulationFrequency int32

const (
	InterestAccumulationFrequency_INTEREST_ACCUMULATION_FREQUENCY_UNSPECIFIED InterestAccumulationFrequency = 0
	InterestAccumulationFrequency_INTEREST_ACCUMULATION_FREQUENCY_DAILY       InterestAccumulationFrequency = 1
	InterestAccumulationFrequency_INTEREST_ACCUMULATION_FREQUENCY_MONTHLY     InterestAccumulationFrequency = 2
	InterestAccumulationFrequency_INTEREST_ACCUMULATION_FREQUENCY_YEARLY      InterestAccumulationFrequency = 3
)

// Enum value maps for InterestAccumulationFrequency.
var (
	InterestAccumulationFrequency_name = map[int32]string{
		0: "INTEREST_ACCUMULATION_FREQUENCY_UNSPECIFIED",
		1: "INTEREST_ACCUMULATION_FREQUENCY_DAILY",
		2: "INTEREST_ACCUMULATION_FREQUENCY_MONTHLY",
		3: "INTEREST_ACCUMULATION_FREQUENCY_YEARLY",
	}
	InterestAccumulationFrequency_value = map[string]int32{
		"INTEREST_ACCUMULATION_FREQUENCY_UNSPECIFIED": 0,
		"INTEREST_ACCUMULATION_FREQUENCY_DAILY":       1,
		"INTEREST_ACCUMULATION_FREQUENCY_MONTHLY":     2,
		"INTEREST_ACCUMULATION_FREQUENCY_YEARLY":      3,
	}
)

func (x InterestAccumulationFrequency) Enum() *InterestAccumulationFrequency {
	p := new(InterestAccumulationFrequency)
	*p = x
	return p
}

func (x InterestAccumulationFrequency) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InterestAccumulationFrequency) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_networth_model_investmentdeclaration_proto_enumTypes[5].Descriptor()
}

func (InterestAccumulationFrequency) Type() protoreflect.EnumType {
	return &file_api_insights_networth_model_investmentdeclaration_proto_enumTypes[5]
}

func (x InterestAccumulationFrequency) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InterestAccumulationFrequency.Descriptor instead.
func (InterestAccumulationFrequency) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_networth_model_investmentdeclaration_proto_rawDescGZIP(), []int{5}
}

type CurrencyType int32

const (
	CurrencyType_CURRENCY_TYPE_UNSPECIFIED CurrencyType = 0
	CurrencyType_CURRENCY_TYPE_INR         CurrencyType = 1
	CurrencyType_CURRENCY_TYPE_USD         CurrencyType = 2
)

// Enum value maps for CurrencyType.
var (
	CurrencyType_name = map[int32]string{
		0: "CURRENCY_TYPE_UNSPECIFIED",
		1: "CURRENCY_TYPE_INR",
		2: "CURRENCY_TYPE_USD",
	}
	CurrencyType_value = map[string]int32{
		"CURRENCY_TYPE_UNSPECIFIED": 0,
		"CURRENCY_TYPE_INR":         1,
		"CURRENCY_TYPE_USD":         2,
	}
)

func (x CurrencyType) Enum() *CurrencyType {
	p := new(CurrencyType)
	*p = x
	return p
}

func (x CurrencyType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CurrencyType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_networth_model_investmentdeclaration_proto_enumTypes[6].Descriptor()
}

func (CurrencyType) Type() protoreflect.EnumType {
	return &file_api_insights_networth_model_investmentdeclaration_proto_enumTypes[6]
}

func (x CurrencyType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CurrencyType.Descriptor instead.
func (CurrencyType) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_networth_model_investmentdeclaration_proto_rawDescGZIP(), []int{6}
}

type AIF_Category int32

const (
	AIF_CATEGORY_UNSPECIFIED AIF_Category = 0
	AIF_CATEGORY_1           AIF_Category = 1
	AIF_CATEGORY_2           AIF_Category = 2
	AIF_CATEGORY_3           AIF_Category = 3
)

// Enum value maps for AIF_Category.
var (
	AIF_Category_name = map[int32]string{
		0: "CATEGORY_UNSPECIFIED",
		1: "CATEGORY_1",
		2: "CATEGORY_2",
		3: "CATEGORY_3",
	}
	AIF_Category_value = map[string]int32{
		"CATEGORY_UNSPECIFIED": 0,
		"CATEGORY_1":           1,
		"CATEGORY_2":           2,
		"CATEGORY_3":           3,
	}
)

func (x AIF_Category) Enum() *AIF_Category {
	p := new(AIF_Category)
	*p = x
	return p
}

func (x AIF_Category) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AIF_Category) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_networth_model_investmentdeclaration_proto_enumTypes[7].Descriptor()
}

func (AIF_Category) Type() protoreflect.EnumType {
	return &file_api_insights_networth_model_investmentdeclaration_proto_enumTypes[7]
}

func (x AIF_Category) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AIF_Category.Descriptor instead.
func (AIF_Category) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_networth_model_investmentdeclaration_proto_rawDescGZIP(), []int{6, 0}
}

// InvestmentDeclaration defines a investment declared by the user
type InvestmentDeclaration struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// instrument in which money was invested
	InstrumentType typesv2.InvestmentInstrumentType `protobuf:"varint,3,opt,name=instrument_type,json=instrumentType,proto3,enum=api.typesv2.InvestmentInstrumentType" json:"instrument_type,omitempty"`
	// amount invested in instrument
	InvestedAmount *money.Money `protobuf:"bytes,4,opt,name=invested_amount,json=investedAmount,proto3" json:"invested_amount,omitempty"`
	// time at which money was invested in the instrument
	InvestedAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=invested_at,json=investedAt,proto3" json:"invested_at,omitempty"`
	// time at which invested amount will mature
	MaturityDate *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=maturity_date,json=maturityDate,proto3" json:"maturity_date,omitempty"`
	// the rate at which invested amount is growing
	InterestRate float64 `protobuf:"fixed64,7,opt,name=interest_rate,json=interestRate,proto3" json:"interest_rate,omitempty"`
	// metadata for instrument specific details
	DeclarationDetails *OtherDeclarationDetails `protobuf:"bytes,8,opt,name=declaration_details,json=declarationDetails,proto3" json:"declaration_details,omitempty"`
	CreatedAt          *timestamppb.Timestamp   `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt          *timestamppb.Timestamp   `protobuf:"bytes,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt          *timestamppb.Timestamp   `protobuf:"bytes,11,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	// reference to consent recorded when user submitted this form
	ConsentId string `protobuf:"bytes,12,opt,name=consent_id,json=consentId,proto3" json:"consent_id,omitempty"`
	// external id to be shared with external system as a unique reference to this asset declaration
	ExternalId string `protobuf:"bytes,13,opt,name=external_id,json=externalId,proto3" json:"external_id,omitempty"`
	Source     Source `protobuf:"varint,14,opt,name=source,proto3,enum=insights.networth.model.Source" json:"source,omitempty"`
}

func (x *InvestmentDeclaration) Reset() {
	*x = InvestmentDeclaration{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InvestmentDeclaration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvestmentDeclaration) ProtoMessage() {}

func (x *InvestmentDeclaration) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvestmentDeclaration.ProtoReflect.Descriptor instead.
func (*InvestmentDeclaration) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_model_investmentdeclaration_proto_rawDescGZIP(), []int{0}
}

func (x *InvestmentDeclaration) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *InvestmentDeclaration) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *InvestmentDeclaration) GetInstrumentType() typesv2.InvestmentInstrumentType {
	if x != nil {
		return x.InstrumentType
	}
	return typesv2.InvestmentInstrumentType(0)
}

func (x *InvestmentDeclaration) GetInvestedAmount() *money.Money {
	if x != nil {
		return x.InvestedAmount
	}
	return nil
}

func (x *InvestmentDeclaration) GetInvestedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.InvestedAt
	}
	return nil
}

func (x *InvestmentDeclaration) GetMaturityDate() *timestamppb.Timestamp {
	if x != nil {
		return x.MaturityDate
	}
	return nil
}

func (x *InvestmentDeclaration) GetInterestRate() float64 {
	if x != nil {
		return x.InterestRate
	}
	return 0
}

func (x *InvestmentDeclaration) GetDeclarationDetails() *OtherDeclarationDetails {
	if x != nil {
		return x.DeclarationDetails
	}
	return nil
}

func (x *InvestmentDeclaration) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *InvestmentDeclaration) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *InvestmentDeclaration) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *InvestmentDeclaration) GetConsentId() string {
	if x != nil {
		return x.ConsentId
	}
	return ""
}

func (x *InvestmentDeclaration) GetExternalId() string {
	if x != nil {
		return x.ExternalId
	}
	return ""
}

func (x *InvestmentDeclaration) GetSource() Source {
	if x != nil {
		return x.Source
	}
	return Source_SOURCE_UNSPECIFIED
}

type OtherDeclarationDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Details:
	//
	//	*OtherDeclarationDetails_FixedDepositDeclarationDetails
	//	*OtherDeclarationDetails_RecurringDepositDeclarationDetails
	//	*OtherDeclarationDetails_RealEstate
	//	*OtherDeclarationDetails_Aif
	//	*OtherDeclarationDetails_PrivateEquity
	//	*OtherDeclarationDetails_DigitalGold
	//	*OtherDeclarationDetails_Cash
	//	*OtherDeclarationDetails_DigitalSilver
	//	*OtherDeclarationDetails_Bond
	//	*OtherDeclarationDetails_ArtAndArtefacts
	//	*OtherDeclarationDetails_PortfolioManagementService
	//	*OtherDeclarationDetails_PublicProvidentFund
	//	*OtherDeclarationDetails_EmployeeStockOption
	//	*OtherDeclarationDetails_Gadgets
	//	*OtherDeclarationDetails_Vehicle
	//	*OtherDeclarationDetails_Crypto
	//	*OtherDeclarationDetails_Furniture
	//	*OtherDeclarationDetails_Collectible
	//	*OtherDeclarationDetails_Jewellery
	//	*OtherDeclarationDetails_Others
	//	*OtherDeclarationDetails_IndianStockDetails
	//	*OtherDeclarationDetails_MutualFundDetails
	//	*OtherDeclarationDetails_NpsDetails
	Details isOtherDeclarationDetails_Details `protobuf_oneof:"details"`
}

func (x *OtherDeclarationDetails) Reset() {
	*x = OtherDeclarationDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OtherDeclarationDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OtherDeclarationDetails) ProtoMessage() {}

func (x *OtherDeclarationDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OtherDeclarationDetails.ProtoReflect.Descriptor instead.
func (*OtherDeclarationDetails) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_model_investmentdeclaration_proto_rawDescGZIP(), []int{1}
}

func (m *OtherDeclarationDetails) GetDetails() isOtherDeclarationDetails_Details {
	if m != nil {
		return m.Details
	}
	return nil
}

func (x *OtherDeclarationDetails) GetFixedDepositDeclarationDetails() *FixedDepositDeclarationDetails {
	if x, ok := x.GetDetails().(*OtherDeclarationDetails_FixedDepositDeclarationDetails); ok {
		return x.FixedDepositDeclarationDetails
	}
	return nil
}

func (x *OtherDeclarationDetails) GetRecurringDepositDeclarationDetails() *RecurringDepositDeclarationDetails {
	if x, ok := x.GetDetails().(*OtherDeclarationDetails_RecurringDepositDeclarationDetails); ok {
		return x.RecurringDepositDeclarationDetails
	}
	return nil
}

func (x *OtherDeclarationDetails) GetRealEstate() *RealEstate {
	if x, ok := x.GetDetails().(*OtherDeclarationDetails_RealEstate); ok {
		return x.RealEstate
	}
	return nil
}

func (x *OtherDeclarationDetails) GetAif() *AIF {
	if x, ok := x.GetDetails().(*OtherDeclarationDetails_Aif); ok {
		return x.Aif
	}
	return nil
}

func (x *OtherDeclarationDetails) GetPrivateEquity() *PrivateEquity {
	if x, ok := x.GetDetails().(*OtherDeclarationDetails_PrivateEquity); ok {
		return x.PrivateEquity
	}
	return nil
}

func (x *OtherDeclarationDetails) GetDigitalGold() *DigitalGold {
	if x, ok := x.GetDetails().(*OtherDeclarationDetails_DigitalGold); ok {
		return x.DigitalGold
	}
	return nil
}

func (x *OtherDeclarationDetails) GetCash() *Cash {
	if x, ok := x.GetDetails().(*OtherDeclarationDetails_Cash); ok {
		return x.Cash
	}
	return nil
}

func (x *OtherDeclarationDetails) GetDigitalSilver() *DigitalSilver {
	if x, ok := x.GetDetails().(*OtherDeclarationDetails_DigitalSilver); ok {
		return x.DigitalSilver
	}
	return nil
}

func (x *OtherDeclarationDetails) GetBond() *Bond {
	if x, ok := x.GetDetails().(*OtherDeclarationDetails_Bond); ok {
		return x.Bond
	}
	return nil
}

func (x *OtherDeclarationDetails) GetArtAndArtefacts() *ArtAndArtefacts {
	if x, ok := x.GetDetails().(*OtherDeclarationDetails_ArtAndArtefacts); ok {
		return x.ArtAndArtefacts
	}
	return nil
}

func (x *OtherDeclarationDetails) GetPortfolioManagementService() *PortfolioManagementService {
	if x, ok := x.GetDetails().(*OtherDeclarationDetails_PortfolioManagementService); ok {
		return x.PortfolioManagementService
	}
	return nil
}

func (x *OtherDeclarationDetails) GetPublicProvidentFund() *PublicProvidentFund {
	if x, ok := x.GetDetails().(*OtherDeclarationDetails_PublicProvidentFund); ok {
		return x.PublicProvidentFund
	}
	return nil
}

func (x *OtherDeclarationDetails) GetEmployeeStockOption() *EmployeeStockOption {
	if x, ok := x.GetDetails().(*OtherDeclarationDetails_EmployeeStockOption); ok {
		return x.EmployeeStockOption
	}
	return nil
}

func (x *OtherDeclarationDetails) GetGadgets() *Gadgets {
	if x, ok := x.GetDetails().(*OtherDeclarationDetails_Gadgets); ok {
		return x.Gadgets
	}
	return nil
}

func (x *OtherDeclarationDetails) GetVehicle() *Vehicle {
	if x, ok := x.GetDetails().(*OtherDeclarationDetails_Vehicle); ok {
		return x.Vehicle
	}
	return nil
}

func (x *OtherDeclarationDetails) GetCrypto() *Crypto {
	if x, ok := x.GetDetails().(*OtherDeclarationDetails_Crypto); ok {
		return x.Crypto
	}
	return nil
}

func (x *OtherDeclarationDetails) GetFurniture() *Furniture {
	if x, ok := x.GetDetails().(*OtherDeclarationDetails_Furniture); ok {
		return x.Furniture
	}
	return nil
}

func (x *OtherDeclarationDetails) GetCollectible() *Collectible {
	if x, ok := x.GetDetails().(*OtherDeclarationDetails_Collectible); ok {
		return x.Collectible
	}
	return nil
}

func (x *OtherDeclarationDetails) GetJewellery() *Jewellery {
	if x, ok := x.GetDetails().(*OtherDeclarationDetails_Jewellery); ok {
		return x.Jewellery
	}
	return nil
}

func (x *OtherDeclarationDetails) GetOthers() *Others {
	if x, ok := x.GetDetails().(*OtherDeclarationDetails_Others); ok {
		return x.Others
	}
	return nil
}

func (x *OtherDeclarationDetails) GetIndianStockDetails() *IndianStockDetails {
	if x, ok := x.GetDetails().(*OtherDeclarationDetails_IndianStockDetails); ok {
		return x.IndianStockDetails
	}
	return nil
}

func (x *OtherDeclarationDetails) GetMutualFundDetails() *MutualFundDetails {
	if x, ok := x.GetDetails().(*OtherDeclarationDetails_MutualFundDetails); ok {
		return x.MutualFundDetails
	}
	return nil
}

func (x *OtherDeclarationDetails) GetNpsDetails() *NPSDetails {
	if x, ok := x.GetDetails().(*OtherDeclarationDetails_NpsDetails); ok {
		return x.NpsDetails
	}
	return nil
}

type isOtherDeclarationDetails_Details interface {
	isOtherDeclarationDetails_Details()
}

type OtherDeclarationDetails_FixedDepositDeclarationDetails struct {
	FixedDepositDeclarationDetails *FixedDepositDeclarationDetails `protobuf:"bytes,1,opt,name=fixed_deposit_declaration_details,json=fixedDepositDeclarationDetails,proto3,oneof"`
}

type OtherDeclarationDetails_RecurringDepositDeclarationDetails struct {
	RecurringDepositDeclarationDetails *RecurringDepositDeclarationDetails `protobuf:"bytes,2,opt,name=recurring_deposit_declaration_details,json=recurringDepositDeclarationDetails,proto3,oneof"`
}

type OtherDeclarationDetails_RealEstate struct {
	RealEstate *RealEstate `protobuf:"bytes,3,opt,name=real_estate,json=realEstate,proto3,oneof"`
}

type OtherDeclarationDetails_Aif struct {
	Aif *AIF `protobuf:"bytes,4,opt,name=aif,proto3,oneof"`
}

type OtherDeclarationDetails_PrivateEquity struct {
	PrivateEquity *PrivateEquity `protobuf:"bytes,5,opt,name=private_equity,json=privateEquity,proto3,oneof"`
}

type OtherDeclarationDetails_DigitalGold struct {
	DigitalGold *DigitalGold `protobuf:"bytes,6,opt,name=digital_gold,json=digitalGold,proto3,oneof"`
}

type OtherDeclarationDetails_Cash struct {
	Cash *Cash `protobuf:"bytes,7,opt,name=cash,proto3,oneof"`
}

type OtherDeclarationDetails_DigitalSilver struct {
	DigitalSilver *DigitalSilver `protobuf:"bytes,8,opt,name=digital_silver,json=digitalSilver,proto3,oneof"`
}

type OtherDeclarationDetails_Bond struct {
	Bond *Bond `protobuf:"bytes,9,opt,name=bond,proto3,oneof"`
}

type OtherDeclarationDetails_ArtAndArtefacts struct {
	ArtAndArtefacts *ArtAndArtefacts `protobuf:"bytes,10,opt,name=art_and_artefacts,json=artAndArtefacts,proto3,oneof"`
}

type OtherDeclarationDetails_PortfolioManagementService struct {
	PortfolioManagementService *PortfolioManagementService `protobuf:"bytes,11,opt,name=portfolio_management_service,json=portfolioManagementService,proto3,oneof"`
}

type OtherDeclarationDetails_PublicProvidentFund struct {
	PublicProvidentFund *PublicProvidentFund `protobuf:"bytes,12,opt,name=public_provident_fund,json=publicProvidentFund,proto3,oneof"`
}

type OtherDeclarationDetails_EmployeeStockOption struct {
	EmployeeStockOption *EmployeeStockOption `protobuf:"bytes,13,opt,name=employee_stock_option,json=employeeStockOption,proto3,oneof"`
}

type OtherDeclarationDetails_Gadgets struct {
	Gadgets *Gadgets `protobuf:"bytes,14,opt,name=gadgets,proto3,oneof"`
}

type OtherDeclarationDetails_Vehicle struct {
	Vehicle *Vehicle `protobuf:"bytes,15,opt,name=vehicle,proto3,oneof"`
}

type OtherDeclarationDetails_Crypto struct {
	Crypto *Crypto `protobuf:"bytes,16,opt,name=crypto,proto3,oneof"`
}

type OtherDeclarationDetails_Furniture struct {
	Furniture *Furniture `protobuf:"bytes,17,opt,name=furniture,proto3,oneof"`
}

type OtherDeclarationDetails_Collectible struct {
	Collectible *Collectible `protobuf:"bytes,18,opt,name=collectible,proto3,oneof"`
}

type OtherDeclarationDetails_Jewellery struct {
	Jewellery *Jewellery `protobuf:"bytes,19,opt,name=jewellery,proto3,oneof"`
}

type OtherDeclarationDetails_Others struct {
	Others *Others `protobuf:"bytes,20,opt,name=others,proto3,oneof"`
}

type OtherDeclarationDetails_IndianStockDetails struct {
	IndianStockDetails *IndianStockDetails `protobuf:"bytes,21,opt,name=indian_stock_details,json=indianStockDetails,proto3,oneof"`
}

type OtherDeclarationDetails_MutualFundDetails struct {
	MutualFundDetails *MutualFundDetails `protobuf:"bytes,22,opt,name=mutual_fund_details,json=mutualFundDetails,proto3,oneof"`
}

type OtherDeclarationDetails_NpsDetails struct {
	NpsDetails *NPSDetails `protobuf:"bytes,23,opt,name=nps_details,json=npsDetails,proto3,oneof"`
}

func (*OtherDeclarationDetails_FixedDepositDeclarationDetails) isOtherDeclarationDetails_Details() {}

func (*OtherDeclarationDetails_RecurringDepositDeclarationDetails) isOtherDeclarationDetails_Details() {
}

func (*OtherDeclarationDetails_RealEstate) isOtherDeclarationDetails_Details() {}

func (*OtherDeclarationDetails_Aif) isOtherDeclarationDetails_Details() {}

func (*OtherDeclarationDetails_PrivateEquity) isOtherDeclarationDetails_Details() {}

func (*OtherDeclarationDetails_DigitalGold) isOtherDeclarationDetails_Details() {}

func (*OtherDeclarationDetails_Cash) isOtherDeclarationDetails_Details() {}

func (*OtherDeclarationDetails_DigitalSilver) isOtherDeclarationDetails_Details() {}

func (*OtherDeclarationDetails_Bond) isOtherDeclarationDetails_Details() {}

func (*OtherDeclarationDetails_ArtAndArtefacts) isOtherDeclarationDetails_Details() {}

func (*OtherDeclarationDetails_PortfolioManagementService) isOtherDeclarationDetails_Details() {}

func (*OtherDeclarationDetails_PublicProvidentFund) isOtherDeclarationDetails_Details() {}

func (*OtherDeclarationDetails_EmployeeStockOption) isOtherDeclarationDetails_Details() {}

func (*OtherDeclarationDetails_Gadgets) isOtherDeclarationDetails_Details() {}

func (*OtherDeclarationDetails_Vehicle) isOtherDeclarationDetails_Details() {}

func (*OtherDeclarationDetails_Crypto) isOtherDeclarationDetails_Details() {}

func (*OtherDeclarationDetails_Furniture) isOtherDeclarationDetails_Details() {}

func (*OtherDeclarationDetails_Collectible) isOtherDeclarationDetails_Details() {}

func (*OtherDeclarationDetails_Jewellery) isOtherDeclarationDetails_Details() {}

func (*OtherDeclarationDetails_Others) isOtherDeclarationDetails_Details() {}

func (*OtherDeclarationDetails_IndianStockDetails) isOtherDeclarationDetails_Details() {}

func (*OtherDeclarationDetails_MutualFundDetails) isOtherDeclarationDetails_Details() {}

func (*OtherDeclarationDetails_NpsDetails) isOtherDeclarationDetails_Details() {}

type NPSDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The name of the NPS scheme, e.g., "NPS Tier I"
	SchemeName string `protobuf:"bytes,1,opt,name=scheme_name,json=schemeName,proto3" json:"scheme_name,omitempty"`
	// The amount invested in the NPS scheme
	InvestedValue *money.Money `protobuf:"bytes,2,opt,name=invested_value,json=investedValue,proto3" json:"invested_value,omitempty"`
	// The current value of the NPS scheme
	CurrentValue   *money.Money `protobuf:"bytes,3,opt,name=current_value,json=currentValue,proto3" json:"current_value,omitempty"`
	InvestmentDate *date.Date   `protobuf:"bytes,4,opt,name=investment_date,json=investmentDate,proto3" json:"investment_date,omitempty"`
}

func (x *NPSDetails) Reset() {
	*x = NPSDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NPSDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NPSDetails) ProtoMessage() {}

func (x *NPSDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NPSDetails.ProtoReflect.Descriptor instead.
func (*NPSDetails) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_model_investmentdeclaration_proto_rawDescGZIP(), []int{2}
}

func (x *NPSDetails) GetSchemeName() string {
	if x != nil {
		return x.SchemeName
	}
	return ""
}

func (x *NPSDetails) GetInvestedValue() *money.Money {
	if x != nil {
		return x.InvestedValue
	}
	return nil
}

func (x *NPSDetails) GetCurrentValue() *money.Money {
	if x != nil {
		return x.CurrentValue
	}
	return nil
}

func (x *NPSDetails) GetInvestmentDate() *date.Date {
	if x != nil {
		return x.InvestmentDate
	}
	return nil
}

type MutualFundDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The name of the mutual fund, e.g., "HDFC Equity Fund"
	MutualFundName string `protobuf:"bytes,1,opt,name=mutual_fund_name,json=mutualFundName,proto3" json:"mutual_fund_name,omitempty"`
	// The number of units owned
	NumberOfUnits        float64      `protobuf:"fixed64,2,opt,name=number_of_units,json=numberOfUnits,proto3" json:"number_of_units,omitempty"`
	InvestedValuePerUnit *money.Money `protobuf:"bytes,3,opt,name=invested_value_per_unit,json=investedValuePerUnit,proto3" json:"invested_value_per_unit,omitempty"`
	CurrentValuePerUnit  *money.Money `protobuf:"bytes,4,opt,name=current_value_per_unit,json=currentValuePerUnit,proto3" json:"current_value_per_unit,omitempty"`
	InvestmentDate       *date.Date   `protobuf:"bytes,5,opt,name=investment_date,json=investmentDate,proto3" json:"investment_date,omitempty"`
}

func (x *MutualFundDetails) Reset() {
	*x = MutualFundDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MutualFundDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MutualFundDetails) ProtoMessage() {}

func (x *MutualFundDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MutualFundDetails.ProtoReflect.Descriptor instead.
func (*MutualFundDetails) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_model_investmentdeclaration_proto_rawDescGZIP(), []int{3}
}

func (x *MutualFundDetails) GetMutualFundName() string {
	if x != nil {
		return x.MutualFundName
	}
	return ""
}

func (x *MutualFundDetails) GetNumberOfUnits() float64 {
	if x != nil {
		return x.NumberOfUnits
	}
	return 0
}

func (x *MutualFundDetails) GetInvestedValuePerUnit() *money.Money {
	if x != nil {
		return x.InvestedValuePerUnit
	}
	return nil
}

func (x *MutualFundDetails) GetCurrentValuePerUnit() *money.Money {
	if x != nil {
		return x.CurrentValuePerUnit
	}
	return nil
}

func (x *MutualFundDetails) GetInvestmentDate() *date.Date {
	if x != nil {
		return x.InvestmentDate
	}
	return nil
}

type IndianStockDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The name of the stock, e.g., "Reliance Industries Limited"
	StockName string `protobuf:"bytes,1,opt,name=stock_name,json=stockName,proto3" json:"stock_name,omitempty"`
	// The number of shares owned
	NumberOfUnits        float64      `protobuf:"fixed64,2,opt,name=number_of_units,json=numberOfUnits,proto3" json:"number_of_units,omitempty"`
	InvestedValuePerUnit *money.Money `protobuf:"bytes,3,opt,name=invested_value_per_unit,json=investedValuePerUnit,proto3" json:"invested_value_per_unit,omitempty"`
	CurrentValuePerUnit  *money.Money `protobuf:"bytes,4,opt,name=current_value_per_unit,json=currentValuePerUnit,proto3" json:"current_value_per_unit,omitempty"`
	InvestmentDate       *date.Date   `protobuf:"bytes,5,opt,name=investment_date,json=investmentDate,proto3" json:"investment_date,omitempty"`
}

func (x *IndianStockDetails) Reset() {
	*x = IndianStockDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IndianStockDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IndianStockDetails) ProtoMessage() {}

func (x *IndianStockDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IndianStockDetails.ProtoReflect.Descriptor instead.
func (*IndianStockDetails) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_model_investmentdeclaration_proto_rawDescGZIP(), []int{4}
}

func (x *IndianStockDetails) GetStockName() string {
	if x != nil {
		return x.StockName
	}
	return ""
}

func (x *IndianStockDetails) GetNumberOfUnits() float64 {
	if x != nil {
		return x.NumberOfUnits
	}
	return 0
}

func (x *IndianStockDetails) GetInvestedValuePerUnit() *money.Money {
	if x != nil {
		return x.InvestedValuePerUnit
	}
	return nil
}

func (x *IndianStockDetails) GetCurrentValuePerUnit() *money.Money {
	if x != nil {
		return x.CurrentValuePerUnit
	}
	return nil
}

func (x *IndianStockDetails) GetInvestmentDate() *date.Date {
	if x != nil {
		return x.InvestmentDate
	}
	return nil
}

type RealEstate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InvestmentName string       `protobuf:"bytes,1,opt,name=investment_name,json=investmentName,proto3" json:"investment_name,omitempty"`
	InvestedValue  *money.Money `protobuf:"bytes,2,opt,name=invested_value,json=investedValue,proto3" json:"invested_value,omitempty"`
	CurrentValue   *money.Money `protobuf:"bytes,3,opt,name=current_value,json=currentValue,proto3" json:"current_value,omitempty"`
	InvestmentDate *date.Date   `protobuf:"bytes,4,opt,name=investment_date,json=investmentDate,proto3" json:"investment_date,omitempty"`
}

func (x *RealEstate) Reset() {
	*x = RealEstate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RealEstate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RealEstate) ProtoMessage() {}

func (x *RealEstate) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RealEstate.ProtoReflect.Descriptor instead.
func (*RealEstate) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_model_investmentdeclaration_proto_rawDescGZIP(), []int{5}
}

func (x *RealEstate) GetInvestmentName() string {
	if x != nil {
		return x.InvestmentName
	}
	return ""
}

func (x *RealEstate) GetInvestedValue() *money.Money {
	if x != nil {
		return x.InvestedValue
	}
	return nil
}

func (x *RealEstate) GetCurrentValue() *money.Money {
	if x != nil {
		return x.CurrentValue
	}
	return nil
}

func (x *RealEstate) GetInvestmentDate() *date.Date {
	if x != nil {
		return x.InvestmentDate
	}
	return nil
}

type AIF struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// This captures custom AIF names from when intermediary identifier was not supported.
	// It represents both the AMC name and the fund name.
	//
	// Deprecated: Marked as deprecated in api/insights/networth/model/investmentdeclaration.proto.
	AifName        string     `protobuf:"bytes,1,opt,name=aif_name,json=aifName,proto3" json:"aif_name,omitempty"`
	InvestmentDate *date.Date `protobuf:"bytes,2,opt,name=investment_date,json=investmentDate,proto3" json:"investment_date,omitempty"`
	EvaluationDate *date.Date `protobuf:"bytes,3,opt,name=evaluation_date,json=evaluationDate,proto3" json:"evaluation_date,omitempty"`
	// AMC is captured as part of the AIF name now.
	// Old records with AMC populated are not used anymore.
	// E.g., Abakkus Growth Fund captures the AMC name: Abakkus Asset Manager LLP and the fund name: Growth Fund
	//
	// Deprecated: Marked as deprecated in api/insights/networth/model/investmentdeclaration.proto.
	AmcName       string       `protobuf:"bytes,4,opt,name=amc_name,json=amcName,proto3" json:"amc_name,omitempty"`
	Category      AIF_Category `protobuf:"varint,5,opt,name=category,proto3,enum=insights.networth.model.AIF_Category" json:"category,omitempty"`
	InvestedValue *money.Money `protobuf:"bytes,6,opt,name=invested_value,json=investedValue,proto3" json:"invested_value,omitempty"`
	CurrentValue  *money.Money `protobuf:"bytes,7,opt,name=current_value,json=currentValue,proto3" json:"current_value,omitempty"`
	FolioId       string       `protobuf:"bytes,8,opt,name=folio_id,json=folioId,proto3" json:"folio_id,omitempty"`
	BrokerCode    string       `protobuf:"bytes,9,opt,name=broker_code,json=brokerCode,proto3" json:"broker_code,omitempty"`
	Remarks       string       `protobuf:"bytes,10,opt,name=remarks,proto3" json:"remarks,omitempty"`
	// Types that are assignable to Identifier:
	//
	//	*AIF_AifId
	//	*AIF_AifNameV2
	Identifier isAIF_Identifier `protobuf_oneof:"identifier"`
}

func (x *AIF) Reset() {
	*x = AIF{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AIF) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AIF) ProtoMessage() {}

func (x *AIF) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AIF.ProtoReflect.Descriptor instead.
func (*AIF) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_model_investmentdeclaration_proto_rawDescGZIP(), []int{6}
}

// Deprecated: Marked as deprecated in api/insights/networth/model/investmentdeclaration.proto.
func (x *AIF) GetAifName() string {
	if x != nil {
		return x.AifName
	}
	return ""
}

func (x *AIF) GetInvestmentDate() *date.Date {
	if x != nil {
		return x.InvestmentDate
	}
	return nil
}

func (x *AIF) GetEvaluationDate() *date.Date {
	if x != nil {
		return x.EvaluationDate
	}
	return nil
}

// Deprecated: Marked as deprecated in api/insights/networth/model/investmentdeclaration.proto.
func (x *AIF) GetAmcName() string {
	if x != nil {
		return x.AmcName
	}
	return ""
}

func (x *AIF) GetCategory() AIF_Category {
	if x != nil {
		return x.Category
	}
	return AIF_CATEGORY_UNSPECIFIED
}

func (x *AIF) GetInvestedValue() *money.Money {
	if x != nil {
		return x.InvestedValue
	}
	return nil
}

func (x *AIF) GetCurrentValue() *money.Money {
	if x != nil {
		return x.CurrentValue
	}
	return nil
}

func (x *AIF) GetFolioId() string {
	if x != nil {
		return x.FolioId
	}
	return ""
}

func (x *AIF) GetBrokerCode() string {
	if x != nil {
		return x.BrokerCode
	}
	return ""
}

func (x *AIF) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

func (m *AIF) GetIdentifier() isAIF_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *AIF) GetAifId() string {
	if x, ok := x.GetIdentifier().(*AIF_AifId); ok {
		return x.AifId
	}
	return ""
}

func (x *AIF) GetAifNameV2() string {
	if x, ok := x.GetIdentifier().(*AIF_AifNameV2); ok {
		return x.AifNameV2
	}
	return ""
}

type isAIF_Identifier interface {
	isAIF_Identifier()
}

type AIF_AifId struct {
	// A unique identifier of an AIF when it is chosen from a list of known AIFs.
	AifId string `protobuf:"bytes,11,opt,name=aif_id,json=aifId,proto3,oneof"`
}

type AIF_AifNameV2 struct {
	// This name of an AIF submitted by user, instead of choosing from a list of known AIFs.
	// Represents both the AMC name and the fund name.
	AifNameV2 string `protobuf:"bytes,12,opt,name=aif_name_v2,json=aifNameV2,proto3,oneof"`
}

func (*AIF_AifId) isAIF_Identifier() {}

func (*AIF_AifNameV2) isAIF_Identifier() {}

type PrivateEquity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InvestmentName string       `protobuf:"bytes,1,opt,name=investment_name,json=investmentName,proto3" json:"investment_name,omitempty"`
	InvestedValue  *money.Money `protobuf:"bytes,2,opt,name=invested_value,json=investedValue,proto3" json:"invested_value,omitempty"`
	CurrentValue   *money.Money `protobuf:"bytes,3,opt,name=current_value,json=currentValue,proto3" json:"current_value,omitempty"`
	InvestmentDate *date.Date   `protobuf:"bytes,4,opt,name=investment_date,json=investmentDate,proto3" json:"investment_date,omitempty"`
}

func (x *PrivateEquity) Reset() {
	*x = PrivateEquity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PrivateEquity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrivateEquity) ProtoMessage() {}

func (x *PrivateEquity) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrivateEquity.ProtoReflect.Descriptor instead.
func (*PrivateEquity) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_model_investmentdeclaration_proto_rawDescGZIP(), []int{7}
}

func (x *PrivateEquity) GetInvestmentName() string {
	if x != nil {
		return x.InvestmentName
	}
	return ""
}

func (x *PrivateEquity) GetInvestedValue() *money.Money {
	if x != nil {
		return x.InvestedValue
	}
	return nil
}

func (x *PrivateEquity) GetCurrentValue() *money.Money {
	if x != nil {
		return x.CurrentValue
	}
	return nil
}

func (x *PrivateEquity) GetInvestmentDate() *date.Date {
	if x != nil {
		return x.InvestmentDate
	}
	return nil
}

type DigitalGold struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InvestmentName  string       `protobuf:"bytes,1,opt,name=investment_name,json=investmentName,proto3" json:"investment_name,omitempty"`
	InvestedValue   *money.Money `protobuf:"bytes,2,opt,name=invested_value,json=investedValue,proto3" json:"invested_value,omitempty"`
	QuantityInGrams float64      `protobuf:"fixed64,3,opt,name=quantity_in_grams,json=quantityInGrams,proto3" json:"quantity_in_grams,omitempty"`
	InvestmentDate  *date.Date   `protobuf:"bytes,4,opt,name=investment_date,json=investmentDate,proto3" json:"investment_date,omitempty"`
	GoldCaratValue  GoldCarat    `protobuf:"varint,5,opt,name=gold_carat_value,json=goldCaratValue,proto3,enum=insights.networth.model.GoldCarat" json:"gold_carat_value,omitempty"`
}

func (x *DigitalGold) Reset() {
	*x = DigitalGold{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DigitalGold) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DigitalGold) ProtoMessage() {}

func (x *DigitalGold) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DigitalGold.ProtoReflect.Descriptor instead.
func (*DigitalGold) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_model_investmentdeclaration_proto_rawDescGZIP(), []int{8}
}

func (x *DigitalGold) GetInvestmentName() string {
	if x != nil {
		return x.InvestmentName
	}
	return ""
}

func (x *DigitalGold) GetInvestedValue() *money.Money {
	if x != nil {
		return x.InvestedValue
	}
	return nil
}

func (x *DigitalGold) GetQuantityInGrams() float64 {
	if x != nil {
		return x.QuantityInGrams
	}
	return 0
}

func (x *DigitalGold) GetInvestmentDate() *date.Date {
	if x != nil {
		return x.InvestmentDate
	}
	return nil
}

func (x *DigitalGold) GetGoldCaratValue() GoldCarat {
	if x != nil {
		return x.GoldCaratValue
	}
	return GoldCarat_GOLD_CARAT_UNSPECIFIED
}

type ArtAndArtefacts struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InvestmentName string       `protobuf:"bytes,1,opt,name=investment_name,json=investmentName,proto3" json:"investment_name,omitempty"`
	InvestedValue  *money.Money `protobuf:"bytes,2,opt,name=invested_value,json=investedValue,proto3" json:"invested_value,omitempty"`
	CurrentValue   *money.Money `protobuf:"bytes,3,opt,name=current_value,json=currentValue,proto3" json:"current_value,omitempty"`
	PurchaseDate   *date.Date   `protobuf:"bytes,4,opt,name=purchase_date,json=purchaseDate,proto3" json:"purchase_date,omitempty"`
}

func (x *ArtAndArtefacts) Reset() {
	*x = ArtAndArtefacts{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArtAndArtefacts) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArtAndArtefacts) ProtoMessage() {}

func (x *ArtAndArtefacts) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArtAndArtefacts.ProtoReflect.Descriptor instead.
func (*ArtAndArtefacts) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_model_investmentdeclaration_proto_rawDescGZIP(), []int{9}
}

func (x *ArtAndArtefacts) GetInvestmentName() string {
	if x != nil {
		return x.InvestmentName
	}
	return ""
}

func (x *ArtAndArtefacts) GetInvestedValue() *money.Money {
	if x != nil {
		return x.InvestedValue
	}
	return nil
}

func (x *ArtAndArtefacts) GetCurrentValue() *money.Money {
	if x != nil {
		return x.CurrentValue
	}
	return nil
}

func (x *ArtAndArtefacts) GetPurchaseDate() *date.Date {
	if x != nil {
		return x.PurchaseDate
	}
	return nil
}

type Bond struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InvestmentName       string       `protobuf:"bytes,1,opt,name=investment_name,json=investmentName,proto3" json:"investment_name,omitempty"`
	NumberOfUnits        int64        `protobuf:"varint,2,opt,name=number_of_units,json=numberOfUnits,proto3" json:"number_of_units,omitempty"`
	InvestedValuePerUnit *money.Money `protobuf:"bytes,3,opt,name=invested_value_per_unit,json=investedValuePerUnit,proto3" json:"invested_value_per_unit,omitempty"`
	CurrentValuePerUnit  *money.Money `protobuf:"bytes,4,opt,name=current_value_per_unit,json=currentValuePerUnit,proto3" json:"current_value_per_unit,omitempty"`
	InvestmentDate       *date.Date   `protobuf:"bytes,5,opt,name=investment_date,json=investmentDate,proto3" json:"investment_date,omitempty"`
	MaturityDate         *date.Date   `protobuf:"bytes,6,opt,name=maturity_date,json=maturityDate,proto3" json:"maturity_date,omitempty"`
}

func (x *Bond) Reset() {
	*x = Bond{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Bond) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bond) ProtoMessage() {}

func (x *Bond) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bond.ProtoReflect.Descriptor instead.
func (*Bond) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_model_investmentdeclaration_proto_rawDescGZIP(), []int{10}
}

func (x *Bond) GetInvestmentName() string {
	if x != nil {
		return x.InvestmentName
	}
	return ""
}

func (x *Bond) GetNumberOfUnits() int64 {
	if x != nil {
		return x.NumberOfUnits
	}
	return 0
}

func (x *Bond) GetInvestedValuePerUnit() *money.Money {
	if x != nil {
		return x.InvestedValuePerUnit
	}
	return nil
}

func (x *Bond) GetCurrentValuePerUnit() *money.Money {
	if x != nil {
		return x.CurrentValuePerUnit
	}
	return nil
}

func (x *Bond) GetInvestmentDate() *date.Date {
	if x != nil {
		return x.InvestmentDate
	}
	return nil
}

func (x *Bond) GetMaturityDate() *date.Date {
	if x != nil {
		return x.MaturityDate
	}
	return nil
}

type DigitalSilver struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InvestmentName  string       `protobuf:"bytes,1,opt,name=investment_name,json=investmentName,proto3" json:"investment_name,omitempty"`
	InvestedValue   *money.Money `protobuf:"bytes,2,opt,name=invested_value,json=investedValue,proto3" json:"invested_value,omitempty"`
	QuantityInGrams float64      `protobuf:"fixed64,3,opt,name=quantity_in_grams,json=quantityInGrams,proto3" json:"quantity_in_grams,omitempty"`
	InvestmentDate  *date.Date   `protobuf:"bytes,4,opt,name=investment_date,json=investmentDate,proto3" json:"investment_date,omitempty"`
}

func (x *DigitalSilver) Reset() {
	*x = DigitalSilver{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DigitalSilver) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DigitalSilver) ProtoMessage() {}

func (x *DigitalSilver) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DigitalSilver.ProtoReflect.Descriptor instead.
func (*DigitalSilver) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_model_investmentdeclaration_proto_rawDescGZIP(), []int{11}
}

func (x *DigitalSilver) GetInvestmentName() string {
	if x != nil {
		return x.InvestmentName
	}
	return ""
}

func (x *DigitalSilver) GetInvestedValue() *money.Money {
	if x != nil {
		return x.InvestedValue
	}
	return nil
}

func (x *DigitalSilver) GetQuantityInGrams() float64 {
	if x != nil {
		return x.QuantityInGrams
	}
	return 0
}

func (x *DigitalSilver) GetInvestmentDate() *date.Date {
	if x != nil {
		return x.InvestmentDate
	}
	return nil
}

type Cash struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurrentAmount *money.Money `protobuf:"bytes,1,opt,name=current_amount,json=currentAmount,proto3" json:"current_amount,omitempty"`
	LastViewedOn  *date.Date   `protobuf:"bytes,2,opt,name=last_viewed_on,json=lastViewedOn,proto3" json:"last_viewed_on,omitempty"`
	Name          string       `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *Cash) Reset() {
	*x = Cash{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Cash) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Cash) ProtoMessage() {}

func (x *Cash) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Cash.ProtoReflect.Descriptor instead.
func (*Cash) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_model_investmentdeclaration_proto_rawDescGZIP(), []int{12}
}

func (x *Cash) GetCurrentAmount() *money.Money {
	if x != nil {
		return x.CurrentAmount
	}
	return nil
}

func (x *Cash) GetLastViewedOn() *date.Date {
	if x != nil {
		return x.LastViewedOn
	}
	return nil
}

func (x *Cash) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type FixedDepositDeclarationDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CompoundingFrequency CompoundingFrequency `protobuf:"varint,1,opt,name=compounding_frequency,json=compoundingFrequency,proto3,enum=insights.networth.model.CompoundingFrequency" json:"compounding_frequency,omitempty"`
	DepositName          string               `protobuf:"bytes,2,opt,name=deposit_name,json=depositName,proto3" json:"deposit_name,omitempty"`
	CurrentValue         *money.Money         `protobuf:"bytes,3,opt,name=current_value,json=currentValue,proto3" json:"current_value,omitempty"`
}

func (x *FixedDepositDeclarationDetails) Reset() {
	*x = FixedDepositDeclarationDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FixedDepositDeclarationDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FixedDepositDeclarationDetails) ProtoMessage() {}

func (x *FixedDepositDeclarationDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FixedDepositDeclarationDetails.ProtoReflect.Descriptor instead.
func (*FixedDepositDeclarationDetails) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_model_investmentdeclaration_proto_rawDescGZIP(), []int{13}
}

func (x *FixedDepositDeclarationDetails) GetCompoundingFrequency() CompoundingFrequency {
	if x != nil {
		return x.CompoundingFrequency
	}
	return CompoundingFrequency_COMPOUNDING_FREQUENCY_UNSPECIFIED
}

func (x *FixedDepositDeclarationDetails) GetDepositName() string {
	if x != nil {
		return x.DepositName
	}
	return ""
}

func (x *FixedDepositDeclarationDetails) GetCurrentValue() *money.Money {
	if x != nil {
		return x.CurrentValue
	}
	return nil
}

type RecurringDepositDeclarationDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CompoundingFrequency CompoundingFrequency `protobuf:"varint,1,opt,name=compounding_frequency,json=compoundingFrequency,proto3,enum=insights.networth.model.CompoundingFrequency" json:"compounding_frequency,omitempty"`
	DepositName          string               `protobuf:"bytes,2,opt,name=deposit_name,json=depositName,proto3" json:"deposit_name,omitempty"`
	CurrentValue         *money.Money         `protobuf:"bytes,3,opt,name=current_value,json=currentValue,proto3" json:"current_value,omitempty"`
}

func (x *RecurringDepositDeclarationDetails) Reset() {
	*x = RecurringDepositDeclarationDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecurringDepositDeclarationDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecurringDepositDeclarationDetails) ProtoMessage() {}

func (x *RecurringDepositDeclarationDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecurringDepositDeclarationDetails.ProtoReflect.Descriptor instead.
func (*RecurringDepositDeclarationDetails) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_model_investmentdeclaration_proto_rawDescGZIP(), []int{14}
}

func (x *RecurringDepositDeclarationDetails) GetCompoundingFrequency() CompoundingFrequency {
	if x != nil {
		return x.CompoundingFrequency
	}
	return CompoundingFrequency_COMPOUNDING_FREQUENCY_UNSPECIFIED
}

func (x *RecurringDepositDeclarationDetails) GetDepositName() string {
	if x != nil {
		return x.DepositName
	}
	return ""
}

func (x *RecurringDepositDeclarationDetails) GetCurrentValue() *money.Money {
	if x != nil {
		return x.CurrentValue
	}
	return nil
}

type PortfolioManagementService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PmsName        string     `protobuf:"bytes,1,opt,name=pms_name,json=pmsName,proto3" json:"pms_name,omitempty"`
	InvestmentDate *date.Date `protobuf:"bytes,2,opt,name=investment_date,json=investmentDate,proto3" json:"investment_date,omitempty"`
	EvaluationDate *date.Date `protobuf:"bytes,3,opt,name=evaluation_date,json=evaluationDate,proto3" json:"evaluation_date,omitempty"`
	// Deprecated: Marked as deprecated in api/insights/networth/model/investmentdeclaration.proto.
	AmcName       string       `protobuf:"bytes,4,opt,name=amc_name,json=amcName,proto3" json:"amc_name,omitempty"`
	InvestedValue *money.Money `protobuf:"bytes,6,opt,name=invested_value,json=investedValue,proto3" json:"invested_value,omitempty"`
	CurrentValue  *money.Money `protobuf:"bytes,7,opt,name=current_value,json=currentValue,proto3" json:"current_value,omitempty"`
	FolioId       string       `protobuf:"bytes,8,opt,name=folio_id,json=folioId,proto3" json:"folio_id,omitempty"`
	BrokerCode    string       `protobuf:"bytes,9,opt,name=broker_code,json=brokerCode,proto3" json:"broker_code,omitempty"`
	Remarks       string       `protobuf:"bytes,10,opt,name=remarks,proto3" json:"remarks,omitempty"`
	// Types that are assignable to Identifier:
	//
	//	*PortfolioManagementService_AmcId
	//	*PortfolioManagementService_AmcNameV2
	Identifier isPortfolioManagementService_Identifier `protobuf_oneof:"identifier"`
}

func (x *PortfolioManagementService) Reset() {
	*x = PortfolioManagementService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PortfolioManagementService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PortfolioManagementService) ProtoMessage() {}

func (x *PortfolioManagementService) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PortfolioManagementService.ProtoReflect.Descriptor instead.
func (*PortfolioManagementService) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_model_investmentdeclaration_proto_rawDescGZIP(), []int{15}
}

func (x *PortfolioManagementService) GetPmsName() string {
	if x != nil {
		return x.PmsName
	}
	return ""
}

func (x *PortfolioManagementService) GetInvestmentDate() *date.Date {
	if x != nil {
		return x.InvestmentDate
	}
	return nil
}

func (x *PortfolioManagementService) GetEvaluationDate() *date.Date {
	if x != nil {
		return x.EvaluationDate
	}
	return nil
}

// Deprecated: Marked as deprecated in api/insights/networth/model/investmentdeclaration.proto.
func (x *PortfolioManagementService) GetAmcName() string {
	if x != nil {
		return x.AmcName
	}
	return ""
}

func (x *PortfolioManagementService) GetInvestedValue() *money.Money {
	if x != nil {
		return x.InvestedValue
	}
	return nil
}

func (x *PortfolioManagementService) GetCurrentValue() *money.Money {
	if x != nil {
		return x.CurrentValue
	}
	return nil
}

func (x *PortfolioManagementService) GetFolioId() string {
	if x != nil {
		return x.FolioId
	}
	return ""
}

func (x *PortfolioManagementService) GetBrokerCode() string {
	if x != nil {
		return x.BrokerCode
	}
	return ""
}

func (x *PortfolioManagementService) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

func (m *PortfolioManagementService) GetIdentifier() isPortfolioManagementService_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *PortfolioManagementService) GetAmcId() string {
	if x, ok := x.GetIdentifier().(*PortfolioManagementService_AmcId); ok {
		return x.AmcId
	}
	return ""
}

func (x *PortfolioManagementService) GetAmcNameV2() string {
	if x, ok := x.GetIdentifier().(*PortfolioManagementService_AmcNameV2); ok {
		return x.AmcNameV2
	}
	return ""
}

type isPortfolioManagementService_Identifier interface {
	isPortfolioManagementService_Identifier()
}

type PortfolioManagementService_AmcId struct {
	// A unique identifier of a PMS provider when it is chosen from a list of known providers.
	AmcId string `protobuf:"bytes,11,opt,name=amc_id,json=amcId,proto3,oneof"`
}

type PortfolioManagementService_AmcNameV2 struct {
	// This name of a PMS provider submitted by user, instead of choosing from a list of known providers.
	// Represents the AMC name.
	AmcNameV2 string `protobuf:"bytes,12,opt,name=amc_name_v2,json=amcNameV2,proto3,oneof"`
}

func (*PortfolioManagementService_AmcId) isPortfolioManagementService_Identifier() {}

func (*PortfolioManagementService_AmcNameV2) isPortfolioManagementService_Identifier() {}

type PublicProvidentFund struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InvestmentName string       `protobuf:"bytes,1,opt,name=investment_name,json=investmentName,proto3" json:"investment_name,omitempty"`
	CurrentValue   *money.Money `protobuf:"bytes,2,opt,name=current_value,json=currentValue,proto3" json:"current_value,omitempty"`
	InvestmentDate *date.Date   `protobuf:"bytes,3,opt,name=investment_date,json=investmentDate,proto3" json:"investment_date,omitempty"`
}

func (x *PublicProvidentFund) Reset() {
	*x = PublicProvidentFund{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PublicProvidentFund) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublicProvidentFund) ProtoMessage() {}

func (x *PublicProvidentFund) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublicProvidentFund.ProtoReflect.Descriptor instead.
func (*PublicProvidentFund) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_model_investmentdeclaration_proto_rawDescGZIP(), []int{16}
}

func (x *PublicProvidentFund) GetInvestmentName() string {
	if x != nil {
		return x.InvestmentName
	}
	return ""
}

func (x *PublicProvidentFund) GetCurrentValue() *money.Money {
	if x != nil {
		return x.CurrentValue
	}
	return nil
}

func (x *PublicProvidentFund) GetInvestmentDate() *date.Date {
	if x != nil {
		return x.InvestmentDate
	}
	return nil
}

type EmployeeStockOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurrencyType         CurrencyType     `protobuf:"varint,1,opt,name=currency_type,json=currencyType,proto3,enum=insights.networth.model.CurrencyType" json:"currency_type,omitempty"`
	CompanyName          string           `protobuf:"bytes,2,opt,name=company_name,json=companyName,proto3" json:"company_name,omitempty"`
	EsopInGrant          int64            `protobuf:"varint,3,opt,name=esop_in_grant,json=esopInGrant,proto3" json:"esop_in_grant,omitempty"`
	FirstIssueDate       *date.Date       `protobuf:"bytes,4,opt,name=first_issue_date,json=firstIssueDate,proto3" json:"first_issue_date,omitempty"`
	VestingSchedule      *VestingSchedule `protobuf:"bytes,5,opt,name=vesting_schedule,json=vestingSchedule,proto3" json:"vesting_schedule,omitempty"`
	CurrentValuePerShare *money.Money     `protobuf:"bytes,6,opt,name=current_value_per_share,json=currentValuePerShare,proto3" json:"current_value_per_share,omitempty"`
}

func (x *EmployeeStockOption) Reset() {
	*x = EmployeeStockOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmployeeStockOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmployeeStockOption) ProtoMessage() {}

func (x *EmployeeStockOption) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmployeeStockOption.ProtoReflect.Descriptor instead.
func (*EmployeeStockOption) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_model_investmentdeclaration_proto_rawDescGZIP(), []int{17}
}

func (x *EmployeeStockOption) GetCurrencyType() CurrencyType {
	if x != nil {
		return x.CurrencyType
	}
	return CurrencyType_CURRENCY_TYPE_UNSPECIFIED
}

func (x *EmployeeStockOption) GetCompanyName() string {
	if x != nil {
		return x.CompanyName
	}
	return ""
}

func (x *EmployeeStockOption) GetEsopInGrant() int64 {
	if x != nil {
		return x.EsopInGrant
	}
	return 0
}

func (x *EmployeeStockOption) GetFirstIssueDate() *date.Date {
	if x != nil {
		return x.FirstIssueDate
	}
	return nil
}

func (x *EmployeeStockOption) GetVestingSchedule() *VestingSchedule {
	if x != nil {
		return x.VestingSchedule
	}
	return nil
}

func (x *EmployeeStockOption) GetCurrentValuePerShare() *money.Money {
	if x != nil {
		return x.CurrentValuePerShare
	}
	return nil
}

type VestingSchedule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	YearlyVestedStock []float64 `protobuf:"fixed64,1,rep,packed,name=yearly_vested_stock,json=yearlyVestedStock,proto3" json:"yearly_vested_stock,omitempty"`
}

func (x *VestingSchedule) Reset() {
	*x = VestingSchedule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VestingSchedule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VestingSchedule) ProtoMessage() {}

func (x *VestingSchedule) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VestingSchedule.ProtoReflect.Descriptor instead.
func (*VestingSchedule) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_model_investmentdeclaration_proto_rawDescGZIP(), []int{18}
}

func (x *VestingSchedule) GetYearlyVestedStock() []float64 {
	if x != nil {
		return x.YearlyVestedStock
	}
	return nil
}

type Gadgets struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeviceType     string       `protobuf:"bytes,1,opt,name=device_type,json=deviceType,proto3" json:"device_type,omitempty"`
	InvestmentName string       `protobuf:"bytes,2,opt,name=investment_name,json=investmentName,proto3" json:"investment_name,omitempty"`
	YearOfPurchase *date.Date   `protobuf:"bytes,3,opt,name=year_of_purchase,json=yearOfPurchase,proto3" json:"year_of_purchase,omitempty"`
	Condition      string       `protobuf:"bytes,4,opt,name=condition,proto3" json:"condition,omitempty"`
	CurrentValue   *money.Money `protobuf:"bytes,5,opt,name=current_value,json=currentValue,proto3" json:"current_value,omitempty"`
}

func (x *Gadgets) Reset() {
	*x = Gadgets{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Gadgets) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Gadgets) ProtoMessage() {}

func (x *Gadgets) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Gadgets.ProtoReflect.Descriptor instead.
func (*Gadgets) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_model_investmentdeclaration_proto_rawDescGZIP(), []int{19}
}

func (x *Gadgets) GetDeviceType() string {
	if x != nil {
		return x.DeviceType
	}
	return ""
}

func (x *Gadgets) GetInvestmentName() string {
	if x != nil {
		return x.InvestmentName
	}
	return ""
}

func (x *Gadgets) GetYearOfPurchase() *date.Date {
	if x != nil {
		return x.YearOfPurchase
	}
	return nil
}

func (x *Gadgets) GetCondition() string {
	if x != nil {
		return x.Condition
	}
	return ""
}

func (x *Gadgets) GetCurrentValue() *money.Money {
	if x != nil {
		return x.CurrentValue
	}
	return nil
}

type Vehicle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VehicleType      string       `protobuf:"bytes,1,opt,name=vehicle_type,json=vehicleType,proto3" json:"vehicle_type,omitempty"`
	InvestmentName   string       `protobuf:"bytes,2,opt,name=investment_name,json=investmentName,proto3" json:"investment_name,omitempty"`
	YearOfPurchase   *date.Date   `protobuf:"bytes,3,opt,name=year_of_purchase,json=yearOfPurchase,proto3" json:"year_of_purchase,omitempty"`
	Condition        string       `protobuf:"bytes,4,opt,name=condition,proto3" json:"condition,omitempty"`
	DepreciationRate float64      `protobuf:"fixed64,5,opt,name=depreciation_rate,json=depreciationRate,proto3" json:"depreciation_rate,omitempty"`
	CurrentValue     *money.Money `protobuf:"bytes,6,opt,name=current_value,json=currentValue,proto3" json:"current_value,omitempty"`
}

func (x *Vehicle) Reset() {
	*x = Vehicle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Vehicle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Vehicle) ProtoMessage() {}

func (x *Vehicle) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Vehicle.ProtoReflect.Descriptor instead.
func (*Vehicle) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_model_investmentdeclaration_proto_rawDescGZIP(), []int{20}
}

func (x *Vehicle) GetVehicleType() string {
	if x != nil {
		return x.VehicleType
	}
	return ""
}

func (x *Vehicle) GetInvestmentName() string {
	if x != nil {
		return x.InvestmentName
	}
	return ""
}

func (x *Vehicle) GetYearOfPurchase() *date.Date {
	if x != nil {
		return x.YearOfPurchase
	}
	return nil
}

func (x *Vehicle) GetCondition() string {
	if x != nil {
		return x.Condition
	}
	return ""
}

func (x *Vehicle) GetDepreciationRate() float64 {
	if x != nil {
		return x.DepreciationRate
	}
	return 0
}

func (x *Vehicle) GetCurrentValue() *money.Money {
	if x != nil {
		return x.CurrentValue
	}
	return nil
}

type Crypto struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InvestmentName      string       `protobuf:"bytes,1,opt,name=investment_name,json=investmentName,proto3" json:"investment_name,omitempty"`
	CurrentValue        *money.Money `protobuf:"bytes,2,opt,name=current_value,json=currentValue,proto3" json:"current_value,omitempty"`
	NumberOfUnits       float64      `protobuf:"fixed64,3,opt,name=number_of_units,json=numberOfUnits,proto3" json:"number_of_units,omitempty"`
	CurrentValuePerUnit *money.Money `protobuf:"bytes,4,opt,name=current_value_per_unit,json=currentValuePerUnit,proto3" json:"current_value_per_unit,omitempty"`
	InvestedValue       *money.Money `protobuf:"bytes,5,opt,name=invested_value,json=investedValue,proto3" json:"invested_value,omitempty"`
}

func (x *Crypto) Reset() {
	*x = Crypto{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Crypto) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Crypto) ProtoMessage() {}

func (x *Crypto) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Crypto.ProtoReflect.Descriptor instead.
func (*Crypto) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_model_investmentdeclaration_proto_rawDescGZIP(), []int{21}
}

func (x *Crypto) GetInvestmentName() string {
	if x != nil {
		return x.InvestmentName
	}
	return ""
}

func (x *Crypto) GetCurrentValue() *money.Money {
	if x != nil {
		return x.CurrentValue
	}
	return nil
}

func (x *Crypto) GetNumberOfUnits() float64 {
	if x != nil {
		return x.NumberOfUnits
	}
	return 0
}

func (x *Crypto) GetCurrentValuePerUnit() *money.Money {
	if x != nil {
		return x.CurrentValuePerUnit
	}
	return nil
}

func (x *Crypto) GetInvestedValue() *money.Money {
	if x != nil {
		return x.InvestedValue
	}
	return nil
}

type Furniture struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FurnitureType  string       `protobuf:"bytes,1,opt,name=furniture_type,json=furnitureType,proto3" json:"furniture_type,omitempty"`
	InvestmentName string       `protobuf:"bytes,2,opt,name=investment_name,json=investmentName,proto3" json:"investment_name,omitempty"`
	YearOfPurchase *date.Date   `protobuf:"bytes,3,opt,name=year_of_purchase,json=yearOfPurchase,proto3" json:"year_of_purchase,omitempty"`
	Condition      string       `protobuf:"bytes,4,opt,name=condition,proto3" json:"condition,omitempty"`
	CurrentValue   *money.Money `protobuf:"bytes,5,opt,name=current_value,json=currentValue,proto3" json:"current_value,omitempty"`
}

func (x *Furniture) Reset() {
	*x = Furniture{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Furniture) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Furniture) ProtoMessage() {}

func (x *Furniture) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Furniture.ProtoReflect.Descriptor instead.
func (*Furniture) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_model_investmentdeclaration_proto_rawDescGZIP(), []int{22}
}

func (x *Furniture) GetFurnitureType() string {
	if x != nil {
		return x.FurnitureType
	}
	return ""
}

func (x *Furniture) GetInvestmentName() string {
	if x != nil {
		return x.InvestmentName
	}
	return ""
}

func (x *Furniture) GetYearOfPurchase() *date.Date {
	if x != nil {
		return x.YearOfPurchase
	}
	return nil
}

func (x *Furniture) GetCondition() string {
	if x != nil {
		return x.Condition
	}
	return ""
}

func (x *Furniture) GetCurrentValue() *money.Money {
	if x != nil {
		return x.CurrentValue
	}
	return nil
}

type Collectible struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InvestmentName string       `protobuf:"bytes,1,opt,name=investment_name,json=investmentName,proto3" json:"investment_name,omitempty"`
	ItemType       string       `protobuf:"bytes,2,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	ItemBrand      string       `protobuf:"bytes,3,opt,name=item_brand,json=itemBrand,proto3" json:"item_brand,omitempty"`
	ItemModel      string       `protobuf:"bytes,4,opt,name=item_model,json=itemModel,proto3" json:"item_model,omitempty"`
	Condition      string       `protobuf:"bytes,5,opt,name=condition,proto3" json:"condition,omitempty"`
	CurrentValue   *money.Money `protobuf:"bytes,6,opt,name=current_value,json=currentValue,proto3" json:"current_value,omitempty"`
}

func (x *Collectible) Reset() {
	*x = Collectible{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Collectible) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Collectible) ProtoMessage() {}

func (x *Collectible) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Collectible.ProtoReflect.Descriptor instead.
func (*Collectible) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_model_investmentdeclaration_proto_rawDescGZIP(), []int{23}
}

func (x *Collectible) GetInvestmentName() string {
	if x != nil {
		return x.InvestmentName
	}
	return ""
}

func (x *Collectible) GetItemType() string {
	if x != nil {
		return x.ItemType
	}
	return ""
}

func (x *Collectible) GetItemBrand() string {
	if x != nil {
		return x.ItemBrand
	}
	return ""
}

func (x *Collectible) GetItemModel() string {
	if x != nil {
		return x.ItemModel
	}
	return ""
}

func (x *Collectible) GetCondition() string {
	if x != nil {
		return x.Condition
	}
	return ""
}

func (x *Collectible) GetCurrentValue() *money.Money {
	if x != nil {
		return x.CurrentValue
	}
	return nil
}

type Jewellery struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InvestmentName string       `protobuf:"bytes,1,opt,name=investment_name,json=investmentName,proto3" json:"investment_name,omitempty"`
	BaseMetal      string       `protobuf:"bytes,2,opt,name=base_metal,json=baseMetal,proto3" json:"base_metal,omitempty"`
	Purity         string       `protobuf:"bytes,3,opt,name=purity,proto3" json:"purity,omitempty"`
	WeightInGrams  float64      `protobuf:"fixed64,4,opt,name=weight_in_grams,json=weightInGrams,proto3" json:"weight_in_grams,omitempty"`
	CurrentValue   *money.Money `protobuf:"bytes,5,opt,name=current_value,json=currentValue,proto3" json:"current_value,omitempty"`
}

func (x *Jewellery) Reset() {
	*x = Jewellery{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Jewellery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Jewellery) ProtoMessage() {}

func (x *Jewellery) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Jewellery.ProtoReflect.Descriptor instead.
func (*Jewellery) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_model_investmentdeclaration_proto_rawDescGZIP(), []int{24}
}

func (x *Jewellery) GetInvestmentName() string {
	if x != nil {
		return x.InvestmentName
	}
	return ""
}

func (x *Jewellery) GetBaseMetal() string {
	if x != nil {
		return x.BaseMetal
	}
	return ""
}

func (x *Jewellery) GetPurity() string {
	if x != nil {
		return x.Purity
	}
	return ""
}

func (x *Jewellery) GetWeightInGrams() float64 {
	if x != nil {
		return x.WeightInGrams
	}
	return 0
}

func (x *Jewellery) GetCurrentValue() *money.Money {
	if x != nil {
		return x.CurrentValue
	}
	return nil
}

type Others struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InvestmentName string       `protobuf:"bytes,1,opt,name=investment_name,json=investmentName,proto3" json:"investment_name,omitempty"`
	InvestmentType string       `protobuf:"bytes,2,opt,name=investment_type,json=investmentType,proto3" json:"investment_type,omitempty"`
	DateOfPurchase *date.Date   `protobuf:"bytes,3,opt,name=date_of_purchase,json=dateOfPurchase,proto3" json:"date_of_purchase,omitempty"`
	CurrentValue   *money.Money `protobuf:"bytes,4,opt,name=current_value,json=currentValue,proto3" json:"current_value,omitempty"`
}

func (x *Others) Reset() {
	*x = Others{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Others) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Others) ProtoMessage() {}

func (x *Others) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Others.ProtoReflect.Descriptor instead.
func (*Others) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_model_investmentdeclaration_proto_rawDescGZIP(), []int{25}
}

func (x *Others) GetInvestmentName() string {
	if x != nil {
		return x.InvestmentName
	}
	return ""
}

func (x *Others) GetInvestmentType() string {
	if x != nil {
		return x.InvestmentType
	}
	return ""
}

func (x *Others) GetDateOfPurchase() *date.Date {
	if x != nil {
		return x.DateOfPurchase
	}
	return nil
}

func (x *Others) GetCurrentValue() *money.Money {
	if x != nil {
		return x.CurrentValue
	}
	return nil
}

var File_api_insights_networth_model_investmentdeclaration_proto protoreflect.FileDescriptor

var file_api_insights_networth_model_investmentdeclaration_proto_rawDesc = []byte{
	0x0a, 0x37, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x69, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f,
	0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x72,
	0x75, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xff, 0x05, 0x0a, 0x15, 0x49, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x4e, 0x0a, 0x0f, 0x69,
	0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x73,
	0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x69, 0x6e, 0x73,
	0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3b, 0x0a, 0x0f, 0x69,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3b, 0x0a, 0x0b, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x69, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3f, 0x0a, 0x0d, 0x6d, 0x61, 0x74, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x6d, 0x61, 0x74, 0x75, 0x72, 0x69,
	0x74, 0x79, 0x44, 0x61, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65,
	0x73, 0x74, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x52, 0x61, 0x74, 0x65, 0x12, 0x61, 0x0a, 0x13, 0x64,
	0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x4f, 0x74, 0x68, 0x65, 0x72, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x12, 0x64, 0x65, 0x63, 0x6c,
	0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x39,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12,
	0x37, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1f, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0xf3, 0x0e, 0x0a, 0x17, 0x4f, 0x74, 0x68,
	0x65, 0x72, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x84, 0x01, 0x0a, 0x21, 0x66, 0x69, 0x78, 0x65, 0x64, 0x5f, 0x64,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x37, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x46, 0x69, 0x78, 0x65, 0x64,
	0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x1e, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x90, 0x01, 0x0a, 0x25,
	0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x5f, 0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x44,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x22, 0x72, 0x65, 0x63, 0x75,
	0x72, 0x72, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x44, 0x65, 0x63, 0x6c,
	0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x46,
	0x0a, 0x0b, 0x72, 0x65, 0x61, 0x6c, 0x5f, 0x65, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x52, 0x65,
	0x61, 0x6c, 0x45, 0x73, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x0a, 0x72, 0x65, 0x61, 0x6c,
	0x45, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x30, 0x0a, 0x03, 0x61, 0x69, 0x66, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x41, 0x49,
	0x46, 0x48, 0x00, 0x52, 0x03, 0x61, 0x69, 0x66, 0x12, 0x4f, 0x0a, 0x0e, 0x70, 0x72, 0x69, 0x76,
	0x61, 0x74, 0x65, 0x5f, 0x65, 0x71, 0x75, 0x69, 0x74, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x26, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50, 0x72, 0x69, 0x76, 0x61,
	0x74, 0x65, 0x45, 0x71, 0x75, 0x69, 0x74, 0x79, 0x48, 0x00, 0x52, 0x0d, 0x70, 0x72, 0x69, 0x76,
	0x61, 0x74, 0x65, 0x45, 0x71, 0x75, 0x69, 0x74, 0x79, 0x12, 0x49, 0x0a, 0x0c, 0x64, 0x69, 0x67,
	0x69, 0x74, 0x61, 0x6c, 0x5f, 0x67, 0x6f, 0x6c, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x24, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61,
	0x6c, 0x47, 0x6f, 0x6c, 0x64, 0x48, 0x00, 0x52, 0x0b, 0x64, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c,
	0x47, 0x6f, 0x6c, 0x64, 0x12, 0x33, 0x0a, 0x04, 0x63, 0x61, 0x73, 0x68, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x61, 0x73,
	0x68, 0x48, 0x00, 0x52, 0x04, 0x63, 0x61, 0x73, 0x68, 0x12, 0x4f, 0x0a, 0x0e, 0x64, 0x69, 0x67,
	0x69, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x6c, 0x76, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x44, 0x69, 0x67, 0x69,
	0x74, 0x61, 0x6c, 0x53, 0x69, 0x6c, 0x76, 0x65, 0x72, 0x48, 0x00, 0x52, 0x0d, 0x64, 0x69, 0x67,
	0x69, 0x74, 0x61, 0x6c, 0x53, 0x69, 0x6c, 0x76, 0x65, 0x72, 0x12, 0x33, 0x0a, 0x04, 0x62, 0x6f,
	0x6e, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x42, 0x6f, 0x6e, 0x64, 0x48, 0x00, 0x52, 0x04, 0x62, 0x6f, 0x6e, 0x64, 0x12,
	0x56, 0x0a, 0x11, 0x61, 0x72, 0x74, 0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x61, 0x72, 0x74, 0x65, 0x66,
	0x61, 0x63, 0x74, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x69, 0x6e, 0x73,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x41, 0x72, 0x74, 0x41, 0x6e, 0x64, 0x41, 0x72, 0x74, 0x65, 0x66,
	0x61, 0x63, 0x74, 0x73, 0x48, 0x00, 0x52, 0x0f, 0x61, 0x72, 0x74, 0x41, 0x6e, 0x64, 0x41, 0x72,
	0x74, 0x65, 0x66, 0x61, 0x63, 0x74, 0x73, 0x12, 0x77, 0x0a, 0x1c, 0x70, 0x6f, 0x72, 0x74, 0x66,
	0x6f, 0x6c, 0x69, 0x6f, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74,
	0x68, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69,
	0x6f, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x48, 0x00, 0x52, 0x1a, 0x70, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x4d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x62, 0x0a, 0x15, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x64, 0x65, 0x6e, 0x74, 0x5f, 0x66, 0x75, 0x6e, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2c, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63,
	0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x46, 0x75, 0x6e, 0x64, 0x48, 0x00, 0x52,
	0x13, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x74,
	0x46, 0x75, 0x6e, 0x64, 0x12, 0x62, 0x0a, 0x15, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65,
	0x5f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x45, 0x6d,
	0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x48, 0x00, 0x52, 0x13, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x53, 0x74, 0x6f,
	0x63, 0x6b, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3c, 0x0a, 0x07, 0x67, 0x61, 0x64, 0x67,
	0x65, 0x74, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x69, 0x6e, 0x73, 0x69,
	0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x47, 0x61, 0x64, 0x67, 0x65, 0x74, 0x73, 0x48, 0x00, 0x52, 0x07, 0x67,
	0x61, 0x64, 0x67, 0x65, 0x74, 0x73, 0x12, 0x3c, 0x0a, 0x07, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x48, 0x00, 0x52, 0x07, 0x76, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x12, 0x39, 0x0a, 0x06, 0x63, 0x72, 0x79, 0x70, 0x74, 0x6f, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43,
	0x72, 0x79, 0x70, 0x74, 0x6f, 0x48, 0x00, 0x52, 0x06, 0x63, 0x72, 0x79, 0x70, 0x74, 0x6f, 0x12,
	0x42, 0x0a, 0x09, 0x66, 0x75, 0x72, 0x6e, 0x69, 0x74, 0x75, 0x72, 0x65, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x46, 0x75, 0x72,
	0x6e, 0x69, 0x74, 0x75, 0x72, 0x65, 0x48, 0x00, 0x52, 0x09, 0x66, 0x75, 0x72, 0x6e, 0x69, 0x74,
	0x75, 0x72, 0x65, 0x12, 0x48, 0x0a, 0x0b, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x62,
	0x6c, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x62, 0x6c, 0x65, 0x48, 0x00,
	0x52, 0x0b, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x62, 0x6c, 0x65, 0x12, 0x42, 0x0a,
	0x09, 0x6a, 0x65, 0x77, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x79, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4a, 0x65, 0x77, 0x65, 0x6c,
	0x6c, 0x65, 0x72, 0x79, 0x48, 0x00, 0x52, 0x09, 0x6a, 0x65, 0x77, 0x65, 0x6c, 0x6c, 0x65, 0x72,
	0x79, 0x12, 0x39, 0x0a, 0x06, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x73, 0x18, 0x14, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4f, 0x74, 0x68, 0x65,
	0x72, 0x73, 0x48, 0x00, 0x52, 0x06, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x73, 0x12, 0x5f, 0x0a, 0x14,
	0x69, 0x6e, 0x64, 0x69, 0x61, 0x6e, 0x5f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x69, 0x6e, 0x73,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x49, 0x6e, 0x64, 0x69, 0x61, 0x6e, 0x53, 0x74, 0x6f, 0x63, 0x6b,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x12, 0x69, 0x6e, 0x64, 0x69, 0x61,
	0x6e, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x5c, 0x0a,
	0x13, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x5f, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x69, 0x6e, 0x73,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x46, 0x75, 0x6e, 0x64, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x11, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c,
	0x46, 0x75, 0x6e, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x46, 0x0a, 0x0b, 0x6e,
	0x70, 0x73, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x23, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4e, 0x50, 0x53, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x0a, 0x6e, 0x70, 0x73, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x42, 0x09, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xfc,
	0x01, 0x0a, 0x0a, 0x4e, 0x50, 0x53, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x2a, 0x0a,
	0x0b, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x0a, 0x73,
	0x63, 0x68, 0x65, 0x6d, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x43, 0x0a, 0x0e, 0x69, 0x6e, 0x76,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x0d, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x41,
	0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x3a, 0x0a, 0x0f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0e, 0x69,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x22, 0xe4, 0x02,
	0x0a, 0x11, 0x4d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x46, 0x75, 0x6e, 0x64, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x33, 0x0a, 0x10, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x5f, 0x66, 0x75,
	0x6e, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x0e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c,
	0x46, 0x75, 0x6e, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x36, 0x0a, 0x0f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x5f, 0x6f, 0x66, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x01, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x12, 0x09, 0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x52, 0x0d, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x55, 0x6e, 0x69, 0x74, 0x73,
	0x12, 0x53, 0x0a, 0x17, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x14, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x50, 0x65,
	0x72, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x51, 0x0a, 0x16, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x13, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x50, 0x65, 0x72, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x3a, 0x0a, 0x0f, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x44, 0x61, 0x74, 0x65, 0x52, 0x0e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x44, 0x61, 0x74, 0x65, 0x22, 0xda, 0x02, 0x0a, 0x12, 0x49, 0x6e, 0x64, 0x69, 0x61, 0x6e, 0x53,
	0x74, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x28, 0x0a, 0x0a, 0x73,
	0x74, 0x6f, 0x63, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x09, 0x73, 0x74, 0x6f, 0x63,
	0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x36, 0x0a, 0x0f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f,
	0x6f, 0x66, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x42, 0x0e,
	0xfa, 0x42, 0x0b, 0x12, 0x09, 0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x0d,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x12, 0x53, 0x0a,
	0x17, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f,
	0x70, 0x65, 0x72, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x14, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x50, 0x65, 0x72, 0x55, 0x6e,
	0x69, 0x74, 0x12, 0x51, 0x0a, 0x16, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x13, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x50, 0x65,
	0x72, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x3a, 0x0a, 0x0f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x52, 0x0e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74,
	0x65, 0x22, 0x84, 0x02, 0x0a, 0x0a, 0x52, 0x65, 0x61, 0x6c, 0x45, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x32, 0x0a, 0x0f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04,
	0x10, 0x01, 0x18, 0x64, 0x52, 0x0e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x43, 0x0a, 0x0e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0d, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x41, 0x0a, 0x0d, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0c,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x3a, 0x0a, 0x0f,
	0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x22, 0xc0, 0x05, 0x0a, 0x03, 0x41, 0x49, 0x46,
	0x12, 0x1d, 0x0a, 0x08, 0x61, 0x69, 0x66, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x07, 0x61, 0x69, 0x66, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x3a, 0x0a, 0x0f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0e, 0x69, 0x6e, 0x76,
	0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x44, 0x0a, 0x0f, 0x65,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x0e, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x1d, 0x0a, 0x08, 0x61, 0x6d, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x07, 0x61, 0x6d, 0x63, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x4b, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x25, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x41, 0x49, 0x46,
	0x2e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01,
	0x02, 0x20, 0x00, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x43, 0x0a,
	0x0e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x0d, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x65, 0x64, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x12, 0x41, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x22, 0x0a, 0x08, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x5f, 0x69,
	0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x64,
	0x52, 0x07, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x72, 0x6f,
	0x6b, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x64, 0x52, 0x0a, 0x62, 0x72, 0x6f, 0x6b, 0x65, 0x72, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x64, 0x52, 0x07, 0x72,
	0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x12, 0x22, 0x0a, 0x06, 0x61, 0x69, 0x66, 0x5f, 0x69, 0x64,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18,
	0x64, 0x48, 0x00, 0x52, 0x05, 0x61, 0x69, 0x66, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x0b, 0x61, 0x69,
	0x66, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x76, 0x32, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x48, 0x00, 0x52, 0x09, 0x61, 0x69,
	0x66, 0x4e, 0x61, 0x6d, 0x65, 0x56, 0x32, 0x22, 0x54, 0x0a, 0x08, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x12, 0x18, 0x0a, 0x14, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0e, 0x0a,
	0x0a, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x31, 0x10, 0x01, 0x12, 0x0e, 0x0a,
	0x0a, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x32, 0x10, 0x02, 0x12, 0x0e, 0x0a,
	0x0a, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x33, 0x10, 0x03, 0x42, 0x0c, 0x0a,
	0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0x87, 0x02, 0x0a, 0x0d,
	0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x45, 0x71, 0x75, 0x69, 0x74, 0x79, 0x12, 0x32, 0x0a,
	0x0f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18,
	0x64, 0x52, 0x0e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x43, 0x0a, 0x0e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0d, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x41, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x3a, 0x0a, 0x0f, 0x69, 0x6e, 0x76,
	0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x44, 0x61, 0x74, 0x65, 0x22, 0xd6, 0x02, 0x0a, 0x0b, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61,
	0x6c, 0x47, 0x6f, 0x6c, 0x64, 0x12, 0x32, 0x0a, 0x0f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09,
	0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x0e, 0x69, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x43, 0x0a, 0x0e, 0x69, 0x6e, 0x76,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x0d, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x3a,
	0x0a, 0x11, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x6e, 0x5f, 0x67, 0x72,
	0x61, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x12, 0x09,
	0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x0f, 0x71, 0x75, 0x61, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x49, 0x6e, 0x47, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x3a, 0x0a, 0x0f, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x56, 0x0a, 0x10, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x63,
	0x61, 0x72, 0x61, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x22, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x47, 0x6f, 0x6c, 0x64, 0x43,
	0x61, 0x72, 0x61, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0e,
	0x67, 0x6f, 0x6c, 0x64, 0x43, 0x61, 0x72, 0x61, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x85,
	0x02, 0x0a, 0x0f, 0x41, 0x72, 0x74, 0x41, 0x6e, 0x64, 0x41, 0x72, 0x74, 0x65, 0x66, 0x61, 0x63,
	0x74, 0x73, 0x12, 0x32, 0x0a, 0x0f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06,
	0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x0e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x43, 0x0a, 0x0e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0d, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x41, 0x0a, 0x0d, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x36,
	0x0a, 0x0d, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0c, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61,
	0x73, 0x65, 0x44, 0x61, 0x74, 0x65, 0x22, 0x9b, 0x03, 0x0a, 0x04, 0x42, 0x6f, 0x6e, 0x64, 0x12,
	0x32, 0x0a, 0x0f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10,
	0x01, 0x18, 0x64, 0x52, 0x0e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x0f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6f, 0x66,
	0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x55,
	0x6e, 0x69, 0x74, 0x73, 0x12, 0x53, 0x0a, 0x17, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x14, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x65, 0x64, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x50, 0x65, 0x72, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x51, 0x0a, 0x16, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x75,
	0x6e, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x13, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x50, 0x65, 0x72, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x44, 0x0a, 0x0f,
	0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02,
	0x10, 0x01, 0x52, 0x0e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x40, 0x0a, 0x0d, 0x6d, 0x61, 0x74, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x6d, 0x61, 0x74, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x44, 0x61, 0x74, 0x65, 0x22, 0x80, 0x02, 0x0a, 0x0d, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c,
	0x53, 0x69, 0x6c, 0x76, 0x65, 0x72, 0x12, 0x32, 0x0a, 0x0f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x0e, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x43, 0x0a, 0x0e, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x0d, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x3a, 0x0a, 0x11, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x6e, 0x5f, 0x67,
	0x72, 0x61, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x12,
	0x09, 0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x0f, 0x71, 0x75, 0x61, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x47, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x3a, 0x0a, 0x0f, 0x69,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x22, 0xa3, 0x01, 0x0a, 0x04, 0x43, 0x61, 0x73, 0x68,
	0x12, 0x43, 0x0a, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x37, 0x0a, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x76, 0x69,
	0x65, 0x77, 0x65, 0x64, 0x5f, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65,
	0x52, 0x0c, 0x6c, 0x61, 0x73, 0x74, 0x56, 0x69, 0x65, 0x77, 0x65, 0x64, 0x4f, 0x6e, 0x12, 0x1d,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x72, 0x04, 0x10, 0x00, 0x18, 0x64, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xe0, 0x01,
	0x0a, 0x1e, 0x46, 0x69, 0x78, 0x65, 0x64, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x44, 0x65,
	0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x62, 0x0a, 0x15, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x75, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f,
	0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2d, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x75,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x14,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x75, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x46, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x6e, 0x63, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x37, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x22, 0xe4, 0x01, 0x0a, 0x22, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x44, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x62, 0x0a, 0x15, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x75, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x75, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x46, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x14, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x75, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x64,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x37,
	0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xc5, 0x04, 0x0a, 0x1a, 0x50, 0x6f, 0x72, 0x74,
	0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x24, 0x0a, 0x08, 0x70, 0x6d, 0x73, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10,
	0x01, 0x18, 0x64, 0x52, 0x07, 0x70, 0x6d, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x44, 0x0a, 0x0f,
	0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02,
	0x10, 0x01, 0x52, 0x0e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x44, 0x0a, 0x0f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0e, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x08, 0x61, 0x6d, 0x63, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x07,
	0x61, 0x6d, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x43, 0x0a, 0x0e, 0x69, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0d, 0x69,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x41, 0x0a, 0x0d,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x22, 0x0a, 0x08, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x64, 0x52, 0x07, 0x66, 0x6f, 0x6c, 0x69,
	0x6f, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x72, 0x6f, 0x6b, 0x65, 0x72, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18,
	0x64, 0x52, 0x0a, 0x62, 0x72, 0x6f, 0x6b, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a,
	0x07, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x64, 0x52, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73,
	0x12, 0x22, 0x0a, 0x06, 0x61, 0x6d, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x48, 0x00, 0x52, 0x05, 0x61,
	0x6d, 0x63, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x0b, 0x61, 0x6d, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x5f, 0x76, 0x32, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04,
	0x10, 0x01, 0x18, 0x64, 0x48, 0x00, 0x52, 0x09, 0x61, 0x6d, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x56,
	0x32, 0x42, 0x0c, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22,
	0xd2, 0x01, 0x0a, 0x13, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64,
	0x65, 0x6e, 0x74, 0x46, 0x75, 0x6e, 0x64, 0x12, 0x32, 0x0a, 0x0f, 0x69, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x0e, 0x69, 0x6e, 0x76,
	0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x41, 0x0a, 0x0d, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x44,
	0x0a, 0x0f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x0e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x44, 0x61, 0x74, 0x65, 0x22, 0x85, 0x03, 0x0a, 0x13, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65,
	0x65, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4a, 0x0a, 0x0d,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x63, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x65,
	0x73, 0x6f, 0x70, 0x5f, 0x69, 0x6e, 0x5f, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0b, 0x65, 0x73, 0x6f, 0x70, 0x49, 0x6e, 0x47, 0x72, 0x61, 0x6e, 0x74, 0x12,
	0x3b, 0x0a, 0x10, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x69, 0x73, 0x73, 0x75, 0x65, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0e, 0x66, 0x69,
	0x72, 0x73, 0x74, 0x49, 0x73, 0x73, 0x75, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x53, 0x0a, 0x10,
	0x76, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x56, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x52, 0x0f, 0x76, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x12, 0x49, 0x0a, 0x17, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x73, 0x68, 0x61, 0x72, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x14, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x50, 0x65, 0x72, 0x53, 0x68, 0x61, 0x72, 0x65, 0x22, 0x41, 0x0a, 0x0f,
	0x56, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12,
	0x2e, 0x0a, 0x13, 0x79, 0x65, 0x61, 0x72, 0x6c, 0x79, 0x5f, 0x76, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x5f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x18, 0x01, 0x20, 0x03, 0x28, 0x01, 0x52, 0x11, 0x79, 0x65,
	0x61, 0x72, 0x6c, 0x79, 0x56, 0x65, 0x73, 0x74, 0x65, 0x64, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x22,
	0x88, 0x02, 0x0a, 0x07, 0x47, 0x61, 0x64, 0x67, 0x65, 0x74, 0x73, 0x12, 0x2a, 0x0a, 0x0b, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x0a, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x32, 0x0a, 0x0f, 0x69, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x0e, 0x69, 0x6e, 0x76,
	0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x10, 0x79,
	0x65, 0x61, 0x72, 0x5f, 0x6f, 0x66, 0x5f, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0e, 0x79, 0x65, 0x61, 0x72, 0x4f, 0x66,
	0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x12, 0x27, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06,
	0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x37, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0c, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xb7, 0x02, 0x0a, 0x07, 0x56,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x12, 0x2c, 0x0a, 0x0c, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x0b, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x32, 0x0a, 0x0f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x0e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x10, 0x79, 0x65, 0x61, 0x72,
	0x5f, 0x6f, 0x66, 0x5f, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0e, 0x79, 0x65, 0x61, 0x72, 0x4f, 0x66, 0x50, 0x75, 0x72,
	0x63, 0x68, 0x61, 0x73, 0x65, 0x12, 0x27, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10,
	0x01, 0x18, 0x64, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2b,
	0x0a, 0x11, 0x64, 0x65, 0x70, 0x72, 0x65, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72,
	0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x10, 0x64, 0x65, 0x70, 0x72, 0x65,
	0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x74, 0x65, 0x12, 0x37, 0x0a, 0x0d, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x22, 0xa1, 0x02, 0x0a, 0x06, 0x43, 0x72, 0x79, 0x70, 0x74, 0x6f, 0x12,
	0x32, 0x0a, 0x0f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10,
	0x01, 0x18, 0x64, 0x52, 0x0e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x37, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0c,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x26, 0x0a, 0x0f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0d, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x55,
	0x6e, 0x69, 0x74, 0x73, 0x12, 0x47, 0x0a, 0x16, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x13, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x50, 0x65, 0x72, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x39, 0x0a,
	0x0e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0d, 0x69, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x85, 0x02, 0x0a, 0x09, 0x46, 0x75, 0x72,
	0x6e, 0x69, 0x74, 0x75, 0x72, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x66, 0x75, 0x72, 0x6e, 0x69, 0x74,
	0x75, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x66, 0x75, 0x72, 0x6e, 0x69, 0x74, 0x75, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x32, 0x0a,
	0x0f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18,
	0x64, 0x52, 0x0e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x3b, 0x0a, 0x10, 0x79, 0x65, 0x61, 0x72, 0x5f, 0x6f, 0x66, 0x5f, 0x70, 0x75, 0x72,
	0x63, 0x68, 0x61, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0e,
	0x79, 0x65, 0x61, 0x72, 0x4f, 0x66, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x12, 0x27,
	0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x09, 0x63, 0x6f,
	0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x37, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x22, 0xf3, 0x01, 0x0a, 0x0b, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x62, 0x6c, 0x65,
	0x12, 0x32, 0x0a, 0x0f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04,
	0x10, 0x01, 0x18, 0x64, 0x52, 0x0e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x74, 0x65, 0x6d, 0x42, 0x72, 0x61, 0x6e, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x74, 0x65, 0x6d, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12,
	0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x37, 0x0a,
	0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xd7, 0x01, 0x0a, 0x09, 0x4a, 0x65, 0x77, 0x65, 0x6c,
	0x6c, 0x65, 0x72, 0x79, 0x12, 0x32, 0x0a, 0x0f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x0e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x61, 0x73, 0x65,
	0x5f, 0x6d, 0x65, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x61,
	0x73, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x75, 0x72, 0x69, 0x74, 0x79, 0x12,
	0x26, 0x0a, 0x0f, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x69, 0x6e, 0x5f, 0x67, 0x72, 0x61,
	0x6d, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0d, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x49, 0x6e, 0x47, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x37, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x22, 0xe6, 0x01, 0x0a, 0x06, 0x4f, 0x74, 0x68, 0x65, 0x72, 0x73, 0x12, 0x32, 0x0a, 0x0f, 0x69,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52,
	0x0e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x32, 0x0a, 0x0f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10,
	0x01, 0x18, 0x64, 0x52, 0x0e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x3b, 0x0a, 0x10, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x70,
	0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65,
	0x52, 0x0e, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65,
	0x12, 0x37, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0c, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x2a, 0x39, 0x0a, 0x06, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x53,
	0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x4d, 0x41, 0x47, 0x49, 0x43, 0x5f, 0x49, 0x4d, 0x50, 0x4f,
	0x52, 0x54, 0x10, 0x01, 0x2a, 0x60, 0x0a, 0x09, 0x47, 0x6f, 0x6c, 0x64, 0x43, 0x61, 0x72, 0x61,
	0x74, 0x12, 0x1a, 0x0a, 0x16, 0x47, 0x4f, 0x4c, 0x44, 0x5f, 0x43, 0x41, 0x52, 0x41, 0x54, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x11, 0x0a,
	0x0d, 0x47, 0x4f, 0x4c, 0x44, 0x5f, 0x43, 0x41, 0x52, 0x41, 0x54, 0x5f, 0x32, 0x34, 0x10, 0x01,
	0x12, 0x11, 0x0a, 0x0d, 0x47, 0x4f, 0x4c, 0x44, 0x5f, 0x43, 0x41, 0x52, 0x41, 0x54, 0x5f, 0x32,
	0x32, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x47, 0x4f, 0x4c, 0x44, 0x5f, 0x43, 0x41, 0x52, 0x41,
	0x54, 0x5f, 0x31, 0x38, 0x10, 0x03, 0x2a, 0x96, 0x02, 0x0a, 0x14, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x75, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12,
	0x25, 0x0a, 0x21, 0x43, 0x4f, 0x4d, 0x50, 0x4f, 0x55, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x46,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x4e, 0x43, 0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x4f, 0x4d, 0x50, 0x4f, 0x55,
	0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x52, 0x45, 0x51, 0x55, 0x45, 0x4e, 0x43, 0x59, 0x5f,
	0x44, 0x41, 0x49, 0x4c, 0x59, 0x10, 0x01, 0x12, 0x21, 0x0a, 0x1d, 0x43, 0x4f, 0x4d, 0x50, 0x4f,
	0x55, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x52, 0x45, 0x51, 0x55, 0x45, 0x4e, 0x43, 0x59,
	0x5f, 0x4d, 0x4f, 0x4e, 0x54, 0x48, 0x4c, 0x59, 0x10, 0x02, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x4f,
	0x4d, 0x50, 0x4f, 0x55, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x4e, 0x43, 0x59, 0x5f, 0x59, 0x45, 0x41, 0x52, 0x4c, 0x59, 0x10, 0x03, 0x12, 0x23, 0x0a, 0x1f,
	0x43, 0x4f, 0x4d, 0x50, 0x4f, 0x55, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x4e, 0x43, 0x59, 0x5f, 0x51, 0x55, 0x41, 0x52, 0x54, 0x45, 0x52, 0x4c, 0x59, 0x10,
	0x04, 0x12, 0x25, 0x0a, 0x21, 0x43, 0x4f, 0x4d, 0x50, 0x4f, 0x55, 0x4e, 0x44, 0x49, 0x4e, 0x47,
	0x5f, 0x46, 0x52, 0x45, 0x51, 0x55, 0x45, 0x4e, 0x43, 0x59, 0x5f, 0x48, 0x41, 0x4c, 0x46, 0x5f,
	0x59, 0x45, 0x41, 0x52, 0x4c, 0x59, 0x10, 0x05, 0x12, 0x25, 0x0a, 0x21, 0x43, 0x4f, 0x4d, 0x50,
	0x4f, 0x55, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x52, 0x45, 0x51, 0x55, 0x45, 0x4e, 0x43,
	0x59, 0x5f, 0x41, 0x54, 0x5f, 0x4d, 0x41, 0x54, 0x55, 0x52, 0x49, 0x54, 0x59, 0x10, 0x06, 0x2a,
	0xfc, 0x05, 0x0a, 0x1e, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65,
	0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61,
	0x73, 0x6b, 0x12, 0x32, 0x0a, 0x2e, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x44, 0x45, 0x43, 0x4c, 0x41, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x29, 0x0a, 0x25, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x43, 0x4c, 0x41, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x53, 0x5f, 0x49, 0x44, 0x10,
	0x01, 0x12, 0x2f, 0x0a, 0x2b, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x44, 0x45, 0x43, 0x4c, 0x41, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x53, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x49, 0x44,
	0x10, 0x02, 0x12, 0x36, 0x0a, 0x32, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x44, 0x45, 0x43, 0x4c, 0x41, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x53, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x52, 0x55, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x03, 0x12, 0x36, 0x0a, 0x32, 0x49, 0x4e,
	0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x43, 0x4c, 0x41, 0x52, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x53,
	0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54,
	0x10, 0x04, 0x12, 0x32, 0x0a, 0x2e, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x44, 0x45, 0x43, 0x4c, 0x41, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x53, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x45,
	0x44, 0x5f, 0x41, 0x54, 0x10, 0x05, 0x12, 0x34, 0x0a, 0x30, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x43, 0x4c, 0x41, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x53, 0x5f, 0x4d, 0x41, 0x54,
	0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x06, 0x12, 0x34, 0x0a, 0x30,
	0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x43, 0x4c, 0x41,
	0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x53, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x45, 0x53, 0x54, 0x5f, 0x52, 0x41, 0x54, 0x45,
	0x10, 0x07, 0x12, 0x3a, 0x0a, 0x36, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x44, 0x45, 0x43, 0x4c, 0x41, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x53, 0x5f, 0x44, 0x45, 0x43, 0x4c, 0x41, 0x52, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x08, 0x12, 0x31,
	0x0a, 0x2d, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x43,
	0x4c, 0x41, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x53, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10,
	0x09, 0x12, 0x31, 0x0a, 0x2d, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x44, 0x45, 0x43, 0x4c, 0x41, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x53, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x5f,
	0x41, 0x54, 0x10, 0x0a, 0x12, 0x31, 0x0a, 0x2d, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45,
	0x4e, 0x54, 0x5f, 0x44, 0x45, 0x43, 0x4c, 0x41, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x53, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54,
	0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x0b, 0x12, 0x32, 0x0a, 0x2e, 0x49, 0x4e, 0x56, 0x45, 0x53,
	0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x43, 0x4c, 0x41, 0x52, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x53, 0x5f, 0x45, 0x58,
	0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x49, 0x44, 0x10, 0x0c, 0x12, 0x31, 0x0a, 0x2d, 0x49,
	0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x43, 0x4c, 0x41, 0x52,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x53, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x44, 0x10, 0x0d, 0x2a, 0xeb,
	0x01, 0x0a, 0x13, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x46, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x24, 0x0a, 0x20, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x52, 0x45, 0x51, 0x55, 0x45, 0x4e, 0x43, 0x59, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d,
	0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x4e, 0x43, 0x59, 0x5f, 0x4f, 0x4e, 0x45, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x01, 0x12,
	0x20, 0x0a, 0x1c, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x4e, 0x43, 0x59, 0x5f, 0x4d, 0x4f, 0x4e, 0x54, 0x48, 0x4c, 0x59, 0x10,
	0x02, 0x12, 0x22, 0x0a, 0x1e, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x46, 0x52, 0x45, 0x51, 0x55, 0x45, 0x4e, 0x43, 0x59, 0x5f, 0x51, 0x55, 0x41, 0x52, 0x54, 0x45,
	0x52, 0x4c, 0x59, 0x10, 0x03, 0x12, 0x24, 0x0a, 0x20, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x46, 0x52, 0x45, 0x51, 0x55, 0x45, 0x4e, 0x43, 0x59, 0x5f, 0x48, 0x41,
	0x4c, 0x46, 0x5f, 0x59, 0x45, 0x41, 0x52, 0x4c, 0x59, 0x10, 0x04, 0x12, 0x1f, 0x0a, 0x1b, 0x49,
	0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x4e, 0x43, 0x59, 0x5f, 0x41, 0x4e, 0x4e, 0x55, 0x41, 0x4c, 0x10, 0x05, 0x2a, 0xd4, 0x01, 0x0a,
	0x1d, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x41, 0x63, 0x63, 0x75, 0x6d, 0x75, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x2f,
	0x0a, 0x2b, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x45, 0x53, 0x54, 0x5f, 0x41, 0x43, 0x43, 0x55, 0x4d,
	0x55, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x52, 0x45, 0x51, 0x55, 0x45, 0x4e, 0x43,
	0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x29, 0x0a, 0x25, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x45, 0x53, 0x54, 0x5f, 0x41, 0x43, 0x43, 0x55,
	0x4d, 0x55, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x52, 0x45, 0x51, 0x55, 0x45, 0x4e,
	0x43, 0x59, 0x5f, 0x44, 0x41, 0x49, 0x4c, 0x59, 0x10, 0x01, 0x12, 0x2b, 0x0a, 0x27, 0x49, 0x4e,
	0x54, 0x45, 0x52, 0x45, 0x53, 0x54, 0x5f, 0x41, 0x43, 0x43, 0x55, 0x4d, 0x55, 0x4c, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x52, 0x45, 0x51, 0x55, 0x45, 0x4e, 0x43, 0x59, 0x5f, 0x4d, 0x4f,
	0x4e, 0x54, 0x48, 0x4c, 0x59, 0x10, 0x02, 0x12, 0x2a, 0x0a, 0x26, 0x49, 0x4e, 0x54, 0x45, 0x52,
	0x45, 0x53, 0x54, 0x5f, 0x41, 0x43, 0x43, 0x55, 0x4d, 0x55, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x46, 0x52, 0x45, 0x51, 0x55, 0x45, 0x4e, 0x43, 0x59, 0x5f, 0x59, 0x45, 0x41, 0x52, 0x4c,
	0x59, 0x10, 0x03, 0x2a, 0x5b, 0x0a, 0x0c, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x55, 0x52, 0x52, 0x45, 0x4e, 0x43, 0x59, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x43, 0x55, 0x52, 0x52, 0x45, 0x4e, 0x43, 0x59, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x52, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x43, 0x55, 0x52,
	0x52, 0x45, 0x4e, 0x43, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x53, 0x44, 0x10, 0x02,
	0x42, 0x68, 0x0a, 0x32, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5a, 0x32, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x74, 0x68, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_api_insights_networth_model_investmentdeclaration_proto_rawDescOnce sync.Once
	file_api_insights_networth_model_investmentdeclaration_proto_rawDescData = file_api_insights_networth_model_investmentdeclaration_proto_rawDesc
)

func file_api_insights_networth_model_investmentdeclaration_proto_rawDescGZIP() []byte {
	file_api_insights_networth_model_investmentdeclaration_proto_rawDescOnce.Do(func() {
		file_api_insights_networth_model_investmentdeclaration_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_insights_networth_model_investmentdeclaration_proto_rawDescData)
	})
	return file_api_insights_networth_model_investmentdeclaration_proto_rawDescData
}

var file_api_insights_networth_model_investmentdeclaration_proto_enumTypes = make([]protoimpl.EnumInfo, 8)
var file_api_insights_networth_model_investmentdeclaration_proto_msgTypes = make([]protoimpl.MessageInfo, 26)
var file_api_insights_networth_model_investmentdeclaration_proto_goTypes = []interface{}{
	(Source)(0),                                // 0: insights.networth.model.Source
	(GoldCarat)(0),                             // 1: insights.networth.model.GoldCarat
	(CompoundingFrequency)(0),                  // 2: insights.networth.model.CompoundingFrequency
	(InvestmentDeclarationFieldMask)(0),        // 3: insights.networth.model.InvestmentDeclarationFieldMask
	(InvestmentFrequency)(0),                   // 4: insights.networth.model.InvestmentFrequency
	(InterestAccumulationFrequency)(0),         // 5: insights.networth.model.InterestAccumulationFrequency
	(CurrencyType)(0),                          // 6: insights.networth.model.CurrencyType
	(AIF_Category)(0),                          // 7: insights.networth.model.AIF.Category
	(*InvestmentDeclaration)(nil),              // 8: insights.networth.model.InvestmentDeclaration
	(*OtherDeclarationDetails)(nil),            // 9: insights.networth.model.OtherDeclarationDetails
	(*NPSDetails)(nil),                         // 10: insights.networth.model.NPSDetails
	(*MutualFundDetails)(nil),                  // 11: insights.networth.model.MutualFundDetails
	(*IndianStockDetails)(nil),                 // 12: insights.networth.model.IndianStockDetails
	(*RealEstate)(nil),                         // 13: insights.networth.model.RealEstate
	(*AIF)(nil),                                // 14: insights.networth.model.AIF
	(*PrivateEquity)(nil),                      // 15: insights.networth.model.PrivateEquity
	(*DigitalGold)(nil),                        // 16: insights.networth.model.DigitalGold
	(*ArtAndArtefacts)(nil),                    // 17: insights.networth.model.ArtAndArtefacts
	(*Bond)(nil),                               // 18: insights.networth.model.Bond
	(*DigitalSilver)(nil),                      // 19: insights.networth.model.DigitalSilver
	(*Cash)(nil),                               // 20: insights.networth.model.Cash
	(*FixedDepositDeclarationDetails)(nil),     // 21: insights.networth.model.FixedDepositDeclarationDetails
	(*RecurringDepositDeclarationDetails)(nil), // 22: insights.networth.model.RecurringDepositDeclarationDetails
	(*PortfolioManagementService)(nil),         // 23: insights.networth.model.PortfolioManagementService
	(*PublicProvidentFund)(nil),                // 24: insights.networth.model.PublicProvidentFund
	(*EmployeeStockOption)(nil),                // 25: insights.networth.model.EmployeeStockOption
	(*VestingSchedule)(nil),                    // 26: insights.networth.model.VestingSchedule
	(*Gadgets)(nil),                            // 27: insights.networth.model.Gadgets
	(*Vehicle)(nil),                            // 28: insights.networth.model.Vehicle
	(*Crypto)(nil),                             // 29: insights.networth.model.Crypto
	(*Furniture)(nil),                          // 30: insights.networth.model.Furniture
	(*Collectible)(nil),                        // 31: insights.networth.model.Collectible
	(*Jewellery)(nil),                          // 32: insights.networth.model.Jewellery
	(*Others)(nil),                             // 33: insights.networth.model.Others
	(typesv2.InvestmentInstrumentType)(0),      // 34: api.typesv2.InvestmentInstrumentType
	(*money.Money)(nil),                        // 35: google.type.Money
	(*timestamppb.Timestamp)(nil),              // 36: google.protobuf.Timestamp
	(*date.Date)(nil),                          // 37: google.type.Date
}
var file_api_insights_networth_model_investmentdeclaration_proto_depIdxs = []int32{
	34, // 0: insights.networth.model.InvestmentDeclaration.instrument_type:type_name -> api.typesv2.InvestmentInstrumentType
	35, // 1: insights.networth.model.InvestmentDeclaration.invested_amount:type_name -> google.type.Money
	36, // 2: insights.networth.model.InvestmentDeclaration.invested_at:type_name -> google.protobuf.Timestamp
	36, // 3: insights.networth.model.InvestmentDeclaration.maturity_date:type_name -> google.protobuf.Timestamp
	9,  // 4: insights.networth.model.InvestmentDeclaration.declaration_details:type_name -> insights.networth.model.OtherDeclarationDetails
	36, // 5: insights.networth.model.InvestmentDeclaration.created_at:type_name -> google.protobuf.Timestamp
	36, // 6: insights.networth.model.InvestmentDeclaration.updated_at:type_name -> google.protobuf.Timestamp
	36, // 7: insights.networth.model.InvestmentDeclaration.deleted_at:type_name -> google.protobuf.Timestamp
	0,  // 8: insights.networth.model.InvestmentDeclaration.source:type_name -> insights.networth.model.Source
	21, // 9: insights.networth.model.OtherDeclarationDetails.fixed_deposit_declaration_details:type_name -> insights.networth.model.FixedDepositDeclarationDetails
	22, // 10: insights.networth.model.OtherDeclarationDetails.recurring_deposit_declaration_details:type_name -> insights.networth.model.RecurringDepositDeclarationDetails
	13, // 11: insights.networth.model.OtherDeclarationDetails.real_estate:type_name -> insights.networth.model.RealEstate
	14, // 12: insights.networth.model.OtherDeclarationDetails.aif:type_name -> insights.networth.model.AIF
	15, // 13: insights.networth.model.OtherDeclarationDetails.private_equity:type_name -> insights.networth.model.PrivateEquity
	16, // 14: insights.networth.model.OtherDeclarationDetails.digital_gold:type_name -> insights.networth.model.DigitalGold
	20, // 15: insights.networth.model.OtherDeclarationDetails.cash:type_name -> insights.networth.model.Cash
	19, // 16: insights.networth.model.OtherDeclarationDetails.digital_silver:type_name -> insights.networth.model.DigitalSilver
	18, // 17: insights.networth.model.OtherDeclarationDetails.bond:type_name -> insights.networth.model.Bond
	17, // 18: insights.networth.model.OtherDeclarationDetails.art_and_artefacts:type_name -> insights.networth.model.ArtAndArtefacts
	23, // 19: insights.networth.model.OtherDeclarationDetails.portfolio_management_service:type_name -> insights.networth.model.PortfolioManagementService
	24, // 20: insights.networth.model.OtherDeclarationDetails.public_provident_fund:type_name -> insights.networth.model.PublicProvidentFund
	25, // 21: insights.networth.model.OtherDeclarationDetails.employee_stock_option:type_name -> insights.networth.model.EmployeeStockOption
	27, // 22: insights.networth.model.OtherDeclarationDetails.gadgets:type_name -> insights.networth.model.Gadgets
	28, // 23: insights.networth.model.OtherDeclarationDetails.vehicle:type_name -> insights.networth.model.Vehicle
	29, // 24: insights.networth.model.OtherDeclarationDetails.crypto:type_name -> insights.networth.model.Crypto
	30, // 25: insights.networth.model.OtherDeclarationDetails.furniture:type_name -> insights.networth.model.Furniture
	31, // 26: insights.networth.model.OtherDeclarationDetails.collectible:type_name -> insights.networth.model.Collectible
	32, // 27: insights.networth.model.OtherDeclarationDetails.jewellery:type_name -> insights.networth.model.Jewellery
	33, // 28: insights.networth.model.OtherDeclarationDetails.others:type_name -> insights.networth.model.Others
	12, // 29: insights.networth.model.OtherDeclarationDetails.indian_stock_details:type_name -> insights.networth.model.IndianStockDetails
	11, // 30: insights.networth.model.OtherDeclarationDetails.mutual_fund_details:type_name -> insights.networth.model.MutualFundDetails
	10, // 31: insights.networth.model.OtherDeclarationDetails.nps_details:type_name -> insights.networth.model.NPSDetails
	35, // 32: insights.networth.model.NPSDetails.invested_value:type_name -> google.type.Money
	35, // 33: insights.networth.model.NPSDetails.current_value:type_name -> google.type.Money
	37, // 34: insights.networth.model.NPSDetails.investment_date:type_name -> google.type.Date
	35, // 35: insights.networth.model.MutualFundDetails.invested_value_per_unit:type_name -> google.type.Money
	35, // 36: insights.networth.model.MutualFundDetails.current_value_per_unit:type_name -> google.type.Money
	37, // 37: insights.networth.model.MutualFundDetails.investment_date:type_name -> google.type.Date
	35, // 38: insights.networth.model.IndianStockDetails.invested_value_per_unit:type_name -> google.type.Money
	35, // 39: insights.networth.model.IndianStockDetails.current_value_per_unit:type_name -> google.type.Money
	37, // 40: insights.networth.model.IndianStockDetails.investment_date:type_name -> google.type.Date
	35, // 41: insights.networth.model.RealEstate.invested_value:type_name -> google.type.Money
	35, // 42: insights.networth.model.RealEstate.current_value:type_name -> google.type.Money
	37, // 43: insights.networth.model.RealEstate.investment_date:type_name -> google.type.Date
	37, // 44: insights.networth.model.AIF.investment_date:type_name -> google.type.Date
	37, // 45: insights.networth.model.AIF.evaluation_date:type_name -> google.type.Date
	7,  // 46: insights.networth.model.AIF.category:type_name -> insights.networth.model.AIF.Category
	35, // 47: insights.networth.model.AIF.invested_value:type_name -> google.type.Money
	35, // 48: insights.networth.model.AIF.current_value:type_name -> google.type.Money
	35, // 49: insights.networth.model.PrivateEquity.invested_value:type_name -> google.type.Money
	35, // 50: insights.networth.model.PrivateEquity.current_value:type_name -> google.type.Money
	37, // 51: insights.networth.model.PrivateEquity.investment_date:type_name -> google.type.Date
	35, // 52: insights.networth.model.DigitalGold.invested_value:type_name -> google.type.Money
	37, // 53: insights.networth.model.DigitalGold.investment_date:type_name -> google.type.Date
	1,  // 54: insights.networth.model.DigitalGold.gold_carat_value:type_name -> insights.networth.model.GoldCarat
	35, // 55: insights.networth.model.ArtAndArtefacts.invested_value:type_name -> google.type.Money
	35, // 56: insights.networth.model.ArtAndArtefacts.current_value:type_name -> google.type.Money
	37, // 57: insights.networth.model.ArtAndArtefacts.purchase_date:type_name -> google.type.Date
	35, // 58: insights.networth.model.Bond.invested_value_per_unit:type_name -> google.type.Money
	35, // 59: insights.networth.model.Bond.current_value_per_unit:type_name -> google.type.Money
	37, // 60: insights.networth.model.Bond.investment_date:type_name -> google.type.Date
	37, // 61: insights.networth.model.Bond.maturity_date:type_name -> google.type.Date
	35, // 62: insights.networth.model.DigitalSilver.invested_value:type_name -> google.type.Money
	37, // 63: insights.networth.model.DigitalSilver.investment_date:type_name -> google.type.Date
	35, // 64: insights.networth.model.Cash.current_amount:type_name -> google.type.Money
	37, // 65: insights.networth.model.Cash.last_viewed_on:type_name -> google.type.Date
	2,  // 66: insights.networth.model.FixedDepositDeclarationDetails.compounding_frequency:type_name -> insights.networth.model.CompoundingFrequency
	35, // 67: insights.networth.model.FixedDepositDeclarationDetails.current_value:type_name -> google.type.Money
	2,  // 68: insights.networth.model.RecurringDepositDeclarationDetails.compounding_frequency:type_name -> insights.networth.model.CompoundingFrequency
	35, // 69: insights.networth.model.RecurringDepositDeclarationDetails.current_value:type_name -> google.type.Money
	37, // 70: insights.networth.model.PortfolioManagementService.investment_date:type_name -> google.type.Date
	37, // 71: insights.networth.model.PortfolioManagementService.evaluation_date:type_name -> google.type.Date
	35, // 72: insights.networth.model.PortfolioManagementService.invested_value:type_name -> google.type.Money
	35, // 73: insights.networth.model.PortfolioManagementService.current_value:type_name -> google.type.Money
	35, // 74: insights.networth.model.PublicProvidentFund.current_value:type_name -> google.type.Money
	37, // 75: insights.networth.model.PublicProvidentFund.investment_date:type_name -> google.type.Date
	6,  // 76: insights.networth.model.EmployeeStockOption.currency_type:type_name -> insights.networth.model.CurrencyType
	37, // 77: insights.networth.model.EmployeeStockOption.first_issue_date:type_name -> google.type.Date
	26, // 78: insights.networth.model.EmployeeStockOption.vesting_schedule:type_name -> insights.networth.model.VestingSchedule
	35, // 79: insights.networth.model.EmployeeStockOption.current_value_per_share:type_name -> google.type.Money
	37, // 80: insights.networth.model.Gadgets.year_of_purchase:type_name -> google.type.Date
	35, // 81: insights.networth.model.Gadgets.current_value:type_name -> google.type.Money
	37, // 82: insights.networth.model.Vehicle.year_of_purchase:type_name -> google.type.Date
	35, // 83: insights.networth.model.Vehicle.current_value:type_name -> google.type.Money
	35, // 84: insights.networth.model.Crypto.current_value:type_name -> google.type.Money
	35, // 85: insights.networth.model.Crypto.current_value_per_unit:type_name -> google.type.Money
	35, // 86: insights.networth.model.Crypto.invested_value:type_name -> google.type.Money
	37, // 87: insights.networth.model.Furniture.year_of_purchase:type_name -> google.type.Date
	35, // 88: insights.networth.model.Furniture.current_value:type_name -> google.type.Money
	35, // 89: insights.networth.model.Collectible.current_value:type_name -> google.type.Money
	35, // 90: insights.networth.model.Jewellery.current_value:type_name -> google.type.Money
	37, // 91: insights.networth.model.Others.date_of_purchase:type_name -> google.type.Date
	35, // 92: insights.networth.model.Others.current_value:type_name -> google.type.Money
	93, // [93:93] is the sub-list for method output_type
	93, // [93:93] is the sub-list for method input_type
	93, // [93:93] is the sub-list for extension type_name
	93, // [93:93] is the sub-list for extension extendee
	0,  // [0:93] is the sub-list for field type_name
}

func init() { file_api_insights_networth_model_investmentdeclaration_proto_init() }
func file_api_insights_networth_model_investmentdeclaration_proto_init() {
	if File_api_insights_networth_model_investmentdeclaration_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InvestmentDeclaration); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OtherDeclarationDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NPSDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MutualFundDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IndianStockDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RealEstate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AIF); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PrivateEquity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DigitalGold); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArtAndArtefacts); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Bond); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DigitalSilver); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Cash); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FixedDepositDeclarationDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecurringDepositDeclarationDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PortfolioManagementService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PublicProvidentFund); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmployeeStockOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VestingSchedule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Gadgets); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Vehicle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Crypto); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Furniture); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Collectible); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Jewellery); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Others); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*OtherDeclarationDetails_FixedDepositDeclarationDetails)(nil),
		(*OtherDeclarationDetails_RecurringDepositDeclarationDetails)(nil),
		(*OtherDeclarationDetails_RealEstate)(nil),
		(*OtherDeclarationDetails_Aif)(nil),
		(*OtherDeclarationDetails_PrivateEquity)(nil),
		(*OtherDeclarationDetails_DigitalGold)(nil),
		(*OtherDeclarationDetails_Cash)(nil),
		(*OtherDeclarationDetails_DigitalSilver)(nil),
		(*OtherDeclarationDetails_Bond)(nil),
		(*OtherDeclarationDetails_ArtAndArtefacts)(nil),
		(*OtherDeclarationDetails_PortfolioManagementService)(nil),
		(*OtherDeclarationDetails_PublicProvidentFund)(nil),
		(*OtherDeclarationDetails_EmployeeStockOption)(nil),
		(*OtherDeclarationDetails_Gadgets)(nil),
		(*OtherDeclarationDetails_Vehicle)(nil),
		(*OtherDeclarationDetails_Crypto)(nil),
		(*OtherDeclarationDetails_Furniture)(nil),
		(*OtherDeclarationDetails_Collectible)(nil),
		(*OtherDeclarationDetails_Jewellery)(nil),
		(*OtherDeclarationDetails_Others)(nil),
		(*OtherDeclarationDetails_IndianStockDetails)(nil),
		(*OtherDeclarationDetails_MutualFundDetails)(nil),
		(*OtherDeclarationDetails_NpsDetails)(nil),
	}
	file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[6].OneofWrappers = []interface{}{
		(*AIF_AifId)(nil),
		(*AIF_AifNameV2)(nil),
	}
	file_api_insights_networth_model_investmentdeclaration_proto_msgTypes[15].OneofWrappers = []interface{}{
		(*PortfolioManagementService_AmcId)(nil),
		(*PortfolioManagementService_AmcNameV2)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_insights_networth_model_investmentdeclaration_proto_rawDesc,
			NumEnums:      8,
			NumMessages:   26,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_insights_networth_model_investmentdeclaration_proto_goTypes,
		DependencyIndexes: file_api_insights_networth_model_investmentdeclaration_proto_depIdxs,
		EnumInfos:         file_api_insights_networth_model_investmentdeclaration_proto_enumTypes,
		MessageInfos:      file_api_insights_networth_model_investmentdeclaration_proto_msgTypes,
	}.Build()
	File_api_insights_networth_model_investmentdeclaration_proto = out.File
	file_api_insights_networth_model_investmentdeclaration_proto_rawDesc = nil
	file_api_insights_networth_model_investmentdeclaration_proto_goTypes = nil
	file_api_insights_networth_model_investmentdeclaration_proto_depIdxs = nil
}
