// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/recurringpayment/service.proto

package recurringpayment

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/recurringpayment/enums"

	order "github.com/epifi/gamma/api/order"

	payment "github.com/epifi/gamma/api/order/payment"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.UpcomingTransactionSource(0)

	_ = order.OrderStatus(0)

	_ = payment.PaymentProtocol(0)

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on
// ProcessRecurringPaymentsNotificationResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProcessRecurringPaymentsNotificationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ProcessRecurringPaymentsNotificationResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ProcessRecurringPaymentsNotificationResponseMultiError, or nil if none found.
func (m *ProcessRecurringPaymentsNotificationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessRecurringPaymentsNotificationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessRecurringPaymentsNotificationResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessRecurringPaymentsNotificationResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessRecurringPaymentsNotificationResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessRecurringPaymentsNotificationResponseMultiError(errors)
	}

	return nil
}

// ProcessRecurringPaymentsNotificationResponseMultiError is an error wrapping
// multiple validation errors returned by
// ProcessRecurringPaymentsNotificationResponse.ValidateAll() if the
// designated constraints aren't met.
type ProcessRecurringPaymentsNotificationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessRecurringPaymentsNotificationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessRecurringPaymentsNotificationResponseMultiError) AllErrors() []error { return m }

// ProcessRecurringPaymentsNotificationResponseValidationError is the
// validation error returned by
// ProcessRecurringPaymentsNotificationResponse.Validate if the designated
// constraints aren't met.
type ProcessRecurringPaymentsNotificationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessRecurringPaymentsNotificationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessRecurringPaymentsNotificationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessRecurringPaymentsNotificationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessRecurringPaymentsNotificationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessRecurringPaymentsNotificationResponseValidationError) ErrorName() string {
	return "ProcessRecurringPaymentsNotificationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessRecurringPaymentsNotificationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessRecurringPaymentsNotificationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessRecurringPaymentsNotificationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessRecurringPaymentsNotificationResponseValidationError{}

// Validate checks the field values on CreateRecurringPaymentRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateRecurringPaymentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateRecurringPaymentRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateRecurringPaymentRequestMultiError, or nil if none found.
func (m *CreateRecurringPaymentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRecurringPaymentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetFromActorId()) < 1 {
		err := CreateRecurringPaymentRequestValidationError{
			field:  "FromActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetToActorId()) < 1 {
		err := CreateRecurringPaymentRequestValidationError{
			field:  "ToActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _CreateRecurringPaymentRequest_Type_NotInLookup[m.GetType()]; ok {
		err := CreateRecurringPaymentRequestValidationError{
			field:  "Type",
			reason: "value must not be in list [RECURRING_PAYMENT_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PiFrom

	// no validation rules for PiTo

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRecurringPaymentRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRecurringPaymentRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRecurringPaymentRequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AmountType

	if all {
		switch v := interface{}(m.GetInterval()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRecurringPaymentRequestValidationError{
					field:  "Interval",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRecurringPaymentRequestValidationError{
					field:  "Interval",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInterval()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRecurringPaymentRequestValidationError{
				field:  "Interval",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRecurrenceRule()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRecurringPaymentRequestValidationError{
					field:  "RecurrenceRule",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRecurringPaymentRequestValidationError{
					field:  "RecurrenceRule",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRecurrenceRule()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRecurringPaymentRequestValidationError{
				field:  "RecurrenceRule",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MaximumAllowedTxns

	if _, ok := _CreateRecurringPaymentRequest_PartnerBank_NotInLookup[m.GetPartnerBank()]; ok {
		err := CreateRecurringPaymentRequestValidationError{
			field:  "PartnerBank",
			reason: "value must not be in list [VENDOR_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PreferredPaymentProtocol

	if _, ok := _CreateRecurringPaymentRequest_Ownership_NotInLookup[m.GetOwnership()]; ok {
		err := CreateRecurringPaymentRequestValidationError{
			field:  "Ownership",
			reason: "value must not be in list [RECURRING_PAYMENT_OWNERSHIP_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _CreateRecurringPaymentRequest_Provenance_NotInLookup[m.GetProvenance()]; ok {
		err := CreateRecurringPaymentRequestValidationError{
			field:  "Provenance",
			reason: "value must not be in list [RECURRENCE_PAYMENT_PROVENANCE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for UiEntryPoint

	if l := utf8.RuneCountInString(m.GetClientRequestId()); l < 4 || l > 100 {
		err := CreateRecurringPaymentRequestValidationError{
			field:  "ClientRequestId",
			reason: "value length must be between 4 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for InitiatedBy

	// no validation rules for Payload

	// no validation rules for CurrentActorId

	// no validation rules for TransactionId

	// no validation rules for Remarks

	// no validation rules for ShareToPayee

	if all {
		switch v := interface{}(m.GetExpiry()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRecurringPaymentRequestValidationError{
					field:  "Expiry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRecurringPaymentRequestValidationError{
					field:  "Expiry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpiry()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRecurringPaymentRequestValidationError{
				field:  "Expiry",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetClientId()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRecurringPaymentRequestValidationError{
					field:  "ClientId",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRecurringPaymentRequestValidationError{
					field:  "ClientId",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClientId()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRecurringPaymentRequestValidationError{
				field:  "ClientId",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPostAuthorisationAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRecurringPaymentRequestValidationError{
					field:  "PostAuthorisationAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRecurringPaymentRequestValidationError{
					field:  "PostAuthorisationAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPostAuthorisationAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRecurringPaymentRequestValidationError{
				field:  "PostAuthorisationAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPostRecurringPaymentCreationDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRecurringPaymentRequestValidationError{
					field:  "PostRecurringPaymentCreationDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRecurringPaymentRequestValidationError{
					field:  "PostRecurringPaymentCreationDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPostRecurringPaymentCreationDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRecurringPaymentRequestValidationError{
				field:  "PostRecurringPaymentCreationDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateRecurringPaymentRequestMultiError(errors)
	}

	return nil
}

// CreateRecurringPaymentRequestMultiError is an error wrapping multiple
// validation errors returned by CreateRecurringPaymentRequest.ValidateAll()
// if the designated constraints aren't met.
type CreateRecurringPaymentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRecurringPaymentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRecurringPaymentRequestMultiError) AllErrors() []error { return m }

// CreateRecurringPaymentRequestValidationError is the validation error
// returned by CreateRecurringPaymentRequest.Validate if the designated
// constraints aren't met.
type CreateRecurringPaymentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRecurringPaymentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRecurringPaymentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRecurringPaymentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRecurringPaymentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRecurringPaymentRequestValidationError) ErrorName() string {
	return "CreateRecurringPaymentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateRecurringPaymentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRecurringPaymentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRecurringPaymentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRecurringPaymentRequestValidationError{}

var _CreateRecurringPaymentRequest_Type_NotInLookup = map[RecurringPaymentType]struct{}{
	0: {},
}

var _CreateRecurringPaymentRequest_PartnerBank_NotInLookup = map[vendorgateway.Vendor]struct{}{
	0: {},
}

var _CreateRecurringPaymentRequest_Ownership_NotInLookup = map[RecurringPaymentOwnership]struct{}{
	0: {},
}

var _CreateRecurringPaymentRequest_Provenance_NotInLookup = map[RecurrencePaymentProvenance]struct{}{
	0: {},
}

// Validate checks the field values on CreateRecurringPaymentResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateRecurringPaymentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateRecurringPaymentResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateRecurringPaymentResponseMultiError, or nil if none found.
func (m *CreateRecurringPaymentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRecurringPaymentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRecurringPaymentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRecurringPaymentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRecurringPaymentResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrderId

	// no validation rules for RecurringPaymentId

	// no validation rules for IsAuthenticationRequired

	// no validation rules for TransactionId

	// no validation rules for CredBlockType

	for idx, item := range m.GetTransactionAttributes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateRecurringPaymentResponseValidationError{
						field:  fmt.Sprintf("TransactionAttributes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateRecurringPaymentResponseValidationError{
						field:  fmt.Sprintf("TransactionAttributes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateRecurringPaymentResponseValidationError{
					field:  fmt.Sprintf("TransactionAttributes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRecurringPaymentResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRecurringPaymentResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRecurringPaymentResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateRecurringPaymentResponseMultiError(errors)
	}

	return nil
}

// CreateRecurringPaymentResponseMultiError is an error wrapping multiple
// validation errors returned by CreateRecurringPaymentResponse.ValidateAll()
// if the designated constraints aren't met.
type CreateRecurringPaymentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRecurringPaymentResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRecurringPaymentResponseMultiError) AllErrors() []error { return m }

// CreateRecurringPaymentResponseValidationError is the validation error
// returned by CreateRecurringPaymentResponse.Validate if the designated
// constraints aren't met.
type CreateRecurringPaymentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRecurringPaymentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRecurringPaymentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRecurringPaymentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRecurringPaymentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRecurringPaymentResponseValidationError) ErrorName() string {
	return "CreateRecurringPaymentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateRecurringPaymentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRecurringPaymentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRecurringPaymentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRecurringPaymentResponseValidationError{}

// Validate checks the field values on AuthoriseRecurringPaymentCreationRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *AuthoriseRecurringPaymentCreationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// AuthoriseRecurringPaymentCreationRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// AuthoriseRecurringPaymentCreationRequestMultiError, or nil if none found.
func (m *AuthoriseRecurringPaymentCreationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AuthoriseRecurringPaymentCreationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RecurringPaymentId

	// no validation rules for ReqId

	// no validation rules for CurrentActorId

	if all {
		switch v := interface{}(m.GetCredential()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthoriseRecurringPaymentCreationRequestValidationError{
					field:  "Credential",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthoriseRecurringPaymentCreationRequestValidationError{
					field:  "Credential",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCredential()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthoriseRecurringPaymentCreationRequestValidationError{
				field:  "Credential",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClientRequestId

	if all {
		switch v := interface{}(m.GetClientId()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthoriseRecurringPaymentCreationRequestValidationError{
					field:  "ClientId",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthoriseRecurringPaymentCreationRequestValidationError{
					field:  "ClientId",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClientId()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthoriseRecurringPaymentCreationRequestValidationError{
				field:  "ClientId",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AuthoriseRecurringPaymentCreationRequestMultiError(errors)
	}

	return nil
}

// AuthoriseRecurringPaymentCreationRequestMultiError is an error wrapping
// multiple validation errors returned by
// AuthoriseRecurringPaymentCreationRequest.ValidateAll() if the designated
// constraints aren't met.
type AuthoriseRecurringPaymentCreationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuthoriseRecurringPaymentCreationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuthoriseRecurringPaymentCreationRequestMultiError) AllErrors() []error { return m }

// AuthoriseRecurringPaymentCreationRequestValidationError is the validation
// error returned by AuthoriseRecurringPaymentCreationRequest.Validate if the
// designated constraints aren't met.
type AuthoriseRecurringPaymentCreationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuthoriseRecurringPaymentCreationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuthoriseRecurringPaymentCreationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuthoriseRecurringPaymentCreationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuthoriseRecurringPaymentCreationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuthoriseRecurringPaymentCreationRequestValidationError) ErrorName() string {
	return "AuthoriseRecurringPaymentCreationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AuthoriseRecurringPaymentCreationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuthoriseRecurringPaymentCreationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuthoriseRecurringPaymentCreationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuthoriseRecurringPaymentCreationRequestValidationError{}

// Validate checks the field values on Credential with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Credential) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Credential with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CredentialMultiError, or
// nil if none found.
func (m *Credential) ValidateAll() error {
	return m.validate(true)
}

func (m *Credential) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Params.(type) {
	case *Credential_PartnerSdkCredBlock:
		if v == nil {
			err := CredentialValidationError{
				field:  "Params",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for PartnerSdkCredBlock
	case *Credential_MandateHeader:
		if v == nil {
			err := CredentialValidationError{
				field:  "Params",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMandateHeader()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CredentialValidationError{
						field:  "MandateHeader",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CredentialValidationError{
						field:  "MandateHeader",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMandateHeader()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CredentialValidationError{
					field:  "MandateHeader",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return CredentialMultiError(errors)
	}

	return nil
}

// CredentialMultiError is an error wrapping multiple validation errors
// returned by Credential.ValidateAll() if the designated constraints aren't met.
type CredentialMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CredentialMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CredentialMultiError) AllErrors() []error { return m }

// CredentialValidationError is the validation error returned by
// Credential.Validate if the designated constraints aren't met.
type CredentialValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CredentialValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CredentialValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CredentialValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CredentialValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CredentialValidationError) ErrorName() string { return "CredentialValidationError" }

// Error satisfies the builtin error interface
func (e CredentialValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCredential.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CredentialValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CredentialValidationError{}

// Validate checks the field values on
// AuthoriseRecurringPaymentCreationResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AuthoriseRecurringPaymentCreationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// AuthoriseRecurringPaymentCreationResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// AuthoriseRecurringPaymentCreationResponseMultiError, or nil if none found.
func (m *AuthoriseRecurringPaymentCreationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AuthoriseRecurringPaymentCreationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthoriseRecurringPaymentCreationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthoriseRecurringPaymentCreationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthoriseRecurringPaymentCreationResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetActionDetailedStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthoriseRecurringPaymentCreationResponseValidationError{
					field:  "ActionDetailedStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthoriseRecurringPaymentCreationResponseValidationError{
					field:  "ActionDetailedStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActionDetailedStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthoriseRecurringPaymentCreationResponseValidationError{
				field:  "ActionDetailedStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPostAuthoriseDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthoriseRecurringPaymentCreationResponseValidationError{
					field:  "PostAuthoriseDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthoriseRecurringPaymentCreationResponseValidationError{
					field:  "PostAuthoriseDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPostAuthoriseDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthoriseRecurringPaymentCreationResponseValidationError{
				field:  "PostAuthoriseDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AuthoriseRecurringPaymentCreationResponseMultiError(errors)
	}

	return nil
}

// AuthoriseRecurringPaymentCreationResponseMultiError is an error wrapping
// multiple validation errors returned by
// AuthoriseRecurringPaymentCreationResponse.ValidateAll() if the designated
// constraints aren't met.
type AuthoriseRecurringPaymentCreationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuthoriseRecurringPaymentCreationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuthoriseRecurringPaymentCreationResponseMultiError) AllErrors() []error { return m }

// AuthoriseRecurringPaymentCreationResponseValidationError is the validation
// error returned by AuthoriseRecurringPaymentCreationResponse.Validate if the
// designated constraints aren't met.
type AuthoriseRecurringPaymentCreationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuthoriseRecurringPaymentCreationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuthoriseRecurringPaymentCreationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuthoriseRecurringPaymentCreationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuthoriseRecurringPaymentCreationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuthoriseRecurringPaymentCreationResponseValidationError) ErrorName() string {
	return "AuthoriseRecurringPaymentCreationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AuthoriseRecurringPaymentCreationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuthoriseRecurringPaymentCreationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuthoriseRecurringPaymentCreationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuthoriseRecurringPaymentCreationResponseValidationError{}

// Validate checks the field values on ExecuteRecurringPaymentRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExecuteRecurringPaymentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExecuteRecurringPaymentRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ExecuteRecurringPaymentRequestMultiError, or nil if none found.
func (m *ExecuteRecurringPaymentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ExecuteRecurringPaymentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RecurringPaymentId

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExecuteRecurringPaymentRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExecuteRecurringPaymentRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExecuteRecurringPaymentRequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if l := utf8.RuneCountInString(m.GetClientRequestId()); l < 4 || l > 100 {
		err := ExecuteRecurringPaymentRequestValidationError{
			field:  "ClientRequestId",
			reason: "value length must be between 4 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for OrderStatus

	if all {
		switch v := interface{}(m.GetExpiry()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExecuteRecurringPaymentRequestValidationError{
					field:  "Expiry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExecuteRecurringPaymentRequestValidationError{
					field:  "Expiry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpiry()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExecuteRecurringPaymentRequestValidationError{
				field:  "Expiry",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetClientId()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExecuteRecurringPaymentRequestValidationError{
					field:  "ClientId",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExecuteRecurringPaymentRequestValidationError{
					field:  "ClientId",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClientId()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExecuteRecurringPaymentRequestValidationError{
				field:  "ClientId",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.ExecutionPayload.(type) {
	case *ExecuteRecurringPaymentRequest_UpiMandateExecutionInfo:
		if v == nil {
			err := ExecuteRecurringPaymentRequestValidationError{
				field:  "ExecutionPayload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUpiMandateExecutionInfo()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ExecuteRecurringPaymentRequestValidationError{
						field:  "UpiMandateExecutionInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ExecuteRecurringPaymentRequestValidationError{
						field:  "UpiMandateExecutionInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUpiMandateExecutionInfo()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ExecuteRecurringPaymentRequestValidationError{
					field:  "UpiMandateExecutionInfo",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ExecuteRecurringPaymentRequestMultiError(errors)
	}

	return nil
}

// ExecuteRecurringPaymentRequestMultiError is an error wrapping multiple
// validation errors returned by ExecuteRecurringPaymentRequest.ValidateAll()
// if the designated constraints aren't met.
type ExecuteRecurringPaymentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExecuteRecurringPaymentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExecuteRecurringPaymentRequestMultiError) AllErrors() []error { return m }

// ExecuteRecurringPaymentRequestValidationError is the validation error
// returned by ExecuteRecurringPaymentRequest.Validate if the designated
// constraints aren't met.
type ExecuteRecurringPaymentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExecuteRecurringPaymentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExecuteRecurringPaymentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExecuteRecurringPaymentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExecuteRecurringPaymentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExecuteRecurringPaymentRequestValidationError) ErrorName() string {
	return "ExecuteRecurringPaymentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ExecuteRecurringPaymentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExecuteRecurringPaymentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExecuteRecurringPaymentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExecuteRecurringPaymentRequestValidationError{}

// Validate checks the field values on ExecuteRecurringPaymentResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExecuteRecurringPaymentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExecuteRecurringPaymentResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ExecuteRecurringPaymentResponseMultiError, or nil if none found.
func (m *ExecuteRecurringPaymentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ExecuteRecurringPaymentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExecuteRecurringPaymentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExecuteRecurringPaymentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExecuteRecurringPaymentResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrderId

	if all {
		switch v := interface{}(m.GetOrderWithTxn()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExecuteRecurringPaymentResponseValidationError{
					field:  "OrderWithTxn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExecuteRecurringPaymentResponseValidationError{
					field:  "OrderWithTxn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrderWithTxn()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExecuteRecurringPaymentResponseValidationError{
				field:  "OrderWithTxn",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ExecuteRecurringPaymentResponseMultiError(errors)
	}

	return nil
}

// ExecuteRecurringPaymentResponseMultiError is an error wrapping multiple
// validation errors returned by ExecuteRecurringPaymentResponse.ValidateAll()
// if the designated constraints aren't met.
type ExecuteRecurringPaymentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExecuteRecurringPaymentResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExecuteRecurringPaymentResponseMultiError) AllErrors() []error { return m }

// ExecuteRecurringPaymentResponseValidationError is the validation error
// returned by ExecuteRecurringPaymentResponse.Validate if the designated
// constraints aren't met.
type ExecuteRecurringPaymentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExecuteRecurringPaymentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExecuteRecurringPaymentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExecuteRecurringPaymentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExecuteRecurringPaymentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExecuteRecurringPaymentResponseValidationError) ErrorName() string {
	return "ExecuteRecurringPaymentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ExecuteRecurringPaymentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExecuteRecurringPaymentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExecuteRecurringPaymentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExecuteRecurringPaymentResponseValidationError{}

// Validate checks the field values on CreateModifyAttemptRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateModifyAttemptRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateModifyAttemptRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateModifyAttemptRequestMultiError, or nil if none found.
func (m *CreateModifyAttemptRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateModifyAttemptRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RecurringPaymentId

	if all {
		switch v := interface{}(m.GetMutableParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateModifyAttemptRequestValidationError{
					field:  "MutableParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateModifyAttemptRequestValidationError{
					field:  "MutableParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMutableParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateModifyAttemptRequestValidationError{
				field:  "MutableParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if l := utf8.RuneCountInString(m.GetClientRequestId()); l < 4 || l > 100 {
		err := CreateModifyAttemptRequestValidationError{
			field:  "ClientRequestId",
			reason: "value length must be between 4 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _CreateModifyAttemptRequest_Provenance_NotInLookup[m.GetProvenance()]; ok {
		err := CreateModifyAttemptRequestValidationError{
			field:  "Provenance",
			reason: "value must not be in list [RECURRENCE_PAYMENT_PROVENANCE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for CurrentActorId

	// no validation rules for InitiatedBy

	// no validation rules for Payload

	// no validation rules for ReqId

	// no validation rules for CurrentActorRole

	for idx, item := range m.GetTransactionAttributes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateModifyAttemptRequestValidationError{
						field:  fmt.Sprintf("TransactionAttributes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateModifyAttemptRequestValidationError{
						field:  fmt.Sprintf("TransactionAttributes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateModifyAttemptRequestValidationError{
					field:  fmt.Sprintf("TransactionAttributes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Remarks

	if all {
		switch v := interface{}(m.GetExpiry()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateModifyAttemptRequestValidationError{
					field:  "Expiry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateModifyAttemptRequestValidationError{
					field:  "Expiry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpiry()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateModifyAttemptRequestValidationError{
				field:  "Expiry",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetClientId()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateModifyAttemptRequestValidationError{
					field:  "ClientId",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateModifyAttemptRequestValidationError{
					field:  "ClientId",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClientId()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateModifyAttemptRequestValidationError{
				field:  "ClientId",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateModifyAttemptRequestMultiError(errors)
	}

	return nil
}

// CreateModifyAttemptRequestMultiError is an error wrapping multiple
// validation errors returned by CreateModifyAttemptRequest.ValidateAll() if
// the designated constraints aren't met.
type CreateModifyAttemptRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateModifyAttemptRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateModifyAttemptRequestMultiError) AllErrors() []error { return m }

// CreateModifyAttemptRequestValidationError is the validation error returned
// by CreateModifyAttemptRequest.Validate if the designated constraints aren't met.
type CreateModifyAttemptRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateModifyAttemptRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateModifyAttemptRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateModifyAttemptRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateModifyAttemptRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateModifyAttemptRequestValidationError) ErrorName() string {
	return "CreateModifyAttemptRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateModifyAttemptRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateModifyAttemptRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateModifyAttemptRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateModifyAttemptRequestValidationError{}

var _CreateModifyAttemptRequest_Provenance_NotInLookup = map[RecurrencePaymentProvenance]struct{}{
	0: {},
}

// Validate checks the field values on CreateModifyAttemptResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateModifyAttemptResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateModifyAttemptResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateModifyAttemptResponseMultiError, or nil if none found.
func (m *CreateModifyAttemptResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateModifyAttemptResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateModifyAttemptResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateModifyAttemptResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateModifyAttemptResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrderId

	// no validation rules for TxnId

	// no validation rules for IsAuthenticationRequired

	// no validation rules for CredBlockType

	for idx, item := range m.GetTransactionAttributes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateModifyAttemptResponseValidationError{
						field:  fmt.Sprintf("TransactionAttributes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateModifyAttemptResponseValidationError{
						field:  fmt.Sprintf("TransactionAttributes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateModifyAttemptResponseValidationError{
					field:  fmt.Sprintf("TransactionAttributes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CreateModifyAttemptResponseMultiError(errors)
	}

	return nil
}

// CreateModifyAttemptResponseMultiError is an error wrapping multiple
// validation errors returned by CreateModifyAttemptResponse.ValidateAll() if
// the designated constraints aren't met.
type CreateModifyAttemptResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateModifyAttemptResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateModifyAttemptResponseMultiError) AllErrors() []error { return m }

// CreateModifyAttemptResponseValidationError is the validation error returned
// by CreateModifyAttemptResponse.Validate if the designated constraints
// aren't met.
type CreateModifyAttemptResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateModifyAttemptResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateModifyAttemptResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateModifyAttemptResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateModifyAttemptResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateModifyAttemptResponseValidationError) ErrorName() string {
	return "CreateModifyAttemptResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateModifyAttemptResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateModifyAttemptResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateModifyAttemptResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateModifyAttemptResponseValidationError{}

// Validate checks the field values on AuthoriseRecurringPaymentModifyRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *AuthoriseRecurringPaymentModifyRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// AuthoriseRecurringPaymentModifyRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// AuthoriseRecurringPaymentModifyRequestMultiError, or nil if none found.
func (m *AuthoriseRecurringPaymentModifyRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AuthoriseRecurringPaymentModifyRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RecurringPaymentId

	if all {
		switch v := interface{}(m.GetCredential()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthoriseRecurringPaymentModifyRequestValidationError{
					field:  "Credential",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthoriseRecurringPaymentModifyRequestValidationError{
					field:  "Credential",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCredential()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthoriseRecurringPaymentModifyRequestValidationError{
				field:  "Credential",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClientRequestId

	// no validation rules for CurrentActorId

	if all {
		switch v := interface{}(m.GetClientId()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthoriseRecurringPaymentModifyRequestValidationError{
					field:  "ClientId",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthoriseRecurringPaymentModifyRequestValidationError{
					field:  "ClientId",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClientId()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthoriseRecurringPaymentModifyRequestValidationError{
				field:  "ClientId",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AuthoriseRecurringPaymentModifyRequestMultiError(errors)
	}

	return nil
}

// AuthoriseRecurringPaymentModifyRequestMultiError is an error wrapping
// multiple validation errors returned by
// AuthoriseRecurringPaymentModifyRequest.ValidateAll() if the designated
// constraints aren't met.
type AuthoriseRecurringPaymentModifyRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuthoriseRecurringPaymentModifyRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuthoriseRecurringPaymentModifyRequestMultiError) AllErrors() []error { return m }

// AuthoriseRecurringPaymentModifyRequestValidationError is the validation
// error returned by AuthoriseRecurringPaymentModifyRequest.Validate if the
// designated constraints aren't met.
type AuthoriseRecurringPaymentModifyRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuthoriseRecurringPaymentModifyRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuthoriseRecurringPaymentModifyRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuthoriseRecurringPaymentModifyRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuthoriseRecurringPaymentModifyRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuthoriseRecurringPaymentModifyRequestValidationError) ErrorName() string {
	return "AuthoriseRecurringPaymentModifyRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AuthoriseRecurringPaymentModifyRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuthoriseRecurringPaymentModifyRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuthoriseRecurringPaymentModifyRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuthoriseRecurringPaymentModifyRequestValidationError{}

// Validate checks the field values on AuthoriseRecurringPaymentModifyResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *AuthoriseRecurringPaymentModifyResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// AuthoriseRecurringPaymentModifyResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// AuthoriseRecurringPaymentModifyResponseMultiError, or nil if none found.
func (m *AuthoriseRecurringPaymentModifyResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AuthoriseRecurringPaymentModifyResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthoriseRecurringPaymentModifyResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthoriseRecurringPaymentModifyResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthoriseRecurringPaymentModifyResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetActionDetailedStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthoriseRecurringPaymentModifyResponseValidationError{
					field:  "ActionDetailedStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthoriseRecurringPaymentModifyResponseValidationError{
					field:  "ActionDetailedStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActionDetailedStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthoriseRecurringPaymentModifyResponseValidationError{
				field:  "ActionDetailedStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AuthoriseRecurringPaymentModifyResponseMultiError(errors)
	}

	return nil
}

// AuthoriseRecurringPaymentModifyResponseMultiError is an error wrapping
// multiple validation errors returned by
// AuthoriseRecurringPaymentModifyResponse.ValidateAll() if the designated
// constraints aren't met.
type AuthoriseRecurringPaymentModifyResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuthoriseRecurringPaymentModifyResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuthoriseRecurringPaymentModifyResponseMultiError) AllErrors() []error { return m }

// AuthoriseRecurringPaymentModifyResponseValidationError is the validation
// error returned by AuthoriseRecurringPaymentModifyResponse.Validate if the
// designated constraints aren't met.
type AuthoriseRecurringPaymentModifyResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuthoriseRecurringPaymentModifyResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuthoriseRecurringPaymentModifyResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuthoriseRecurringPaymentModifyResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuthoriseRecurringPaymentModifyResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuthoriseRecurringPaymentModifyResponseValidationError) ErrorName() string {
	return "AuthoriseRecurringPaymentModifyResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AuthoriseRecurringPaymentModifyResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuthoriseRecurringPaymentModifyResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuthoriseRecurringPaymentModifyResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuthoriseRecurringPaymentModifyResponseValidationError{}

// Validate checks the field values on CreateRevokeAttemptRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateRevokeAttemptRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateRevokeAttemptRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateRevokeAttemptRequestMultiError, or nil if none found.
func (m *CreateRevokeAttemptRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRevokeAttemptRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RecurringPaymentId

	if l := utf8.RuneCountInString(m.GetClientRequestId()); l < 4 || l > 100 {
		err := CreateRevokeAttemptRequestValidationError{
			field:  "ClientRequestId",
			reason: "value length must be between 4 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for CurrentActorId

	// no validation rules for InitiatedBy

	// no validation rules for TransactionId

	// no validation rules for CurrentActorRole

	if _, ok := _CreateRevokeAttemptRequest_Provenance_NotInLookup[m.GetProvenance()]; ok {
		err := CreateRevokeAttemptRequestValidationError{
			field:  "Provenance",
			reason: "value must not be in list [RECURRENCE_PAYMENT_PROVENANCE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Payload

	// no validation rules for Remarks

	if all {
		switch v := interface{}(m.GetExpiry()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRevokeAttemptRequestValidationError{
					field:  "Expiry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRevokeAttemptRequestValidationError{
					field:  "Expiry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpiry()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRevokeAttemptRequestValidationError{
				field:  "Expiry",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetClientId()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRevokeAttemptRequestValidationError{
					field:  "ClientId",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRevokeAttemptRequestValidationError{
					field:  "ClientId",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClientId()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRevokeAttemptRequestValidationError{
				field:  "ClientId",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateRevokeAttemptRequestMultiError(errors)
	}

	return nil
}

// CreateRevokeAttemptRequestMultiError is an error wrapping multiple
// validation errors returned by CreateRevokeAttemptRequest.ValidateAll() if
// the designated constraints aren't met.
type CreateRevokeAttemptRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRevokeAttemptRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRevokeAttemptRequestMultiError) AllErrors() []error { return m }

// CreateRevokeAttemptRequestValidationError is the validation error returned
// by CreateRevokeAttemptRequest.Validate if the designated constraints aren't met.
type CreateRevokeAttemptRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRevokeAttemptRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRevokeAttemptRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRevokeAttemptRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRevokeAttemptRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRevokeAttemptRequestValidationError) ErrorName() string {
	return "CreateRevokeAttemptRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateRevokeAttemptRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRevokeAttemptRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRevokeAttemptRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRevokeAttemptRequestValidationError{}

var _CreateRevokeAttemptRequest_Provenance_NotInLookup = map[RecurrencePaymentProvenance]struct{}{
	0: {},
}

// Validate checks the field values on CreateRevokeAttemptResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateRevokeAttemptResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateRevokeAttemptResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateRevokeAttemptResponseMultiError, or nil if none found.
func (m *CreateRevokeAttemptResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRevokeAttemptResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRevokeAttemptResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRevokeAttemptResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRevokeAttemptResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrderId

	// no validation rules for TxnId

	// no validation rules for IsAuthenticationRequired

	// no validation rules for CredBlockType

	for idx, item := range m.GetTransactionAttributes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateRevokeAttemptResponseValidationError{
						field:  fmt.Sprintf("TransactionAttributes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateRevokeAttemptResponseValidationError{
						field:  fmt.Sprintf("TransactionAttributes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateRevokeAttemptResponseValidationError{
					field:  fmt.Sprintf("TransactionAttributes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CreateRevokeAttemptResponseMultiError(errors)
	}

	return nil
}

// CreateRevokeAttemptResponseMultiError is an error wrapping multiple
// validation errors returned by CreateRevokeAttemptResponse.ValidateAll() if
// the designated constraints aren't met.
type CreateRevokeAttemptResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRevokeAttemptResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRevokeAttemptResponseMultiError) AllErrors() []error { return m }

// CreateRevokeAttemptResponseValidationError is the validation error returned
// by CreateRevokeAttemptResponse.Validate if the designated constraints
// aren't met.
type CreateRevokeAttemptResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRevokeAttemptResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRevokeAttemptResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRevokeAttemptResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRevokeAttemptResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRevokeAttemptResponseValidationError) ErrorName() string {
	return "CreateRevokeAttemptResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateRevokeAttemptResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRevokeAttemptResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRevokeAttemptResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRevokeAttemptResponseValidationError{}

// Validate checks the field values on AuthoriseRecurringPaymentRevokeRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *AuthoriseRecurringPaymentRevokeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// AuthoriseRecurringPaymentRevokeRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// AuthoriseRecurringPaymentRevokeRequestMultiError, or nil if none found.
func (m *AuthoriseRecurringPaymentRevokeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AuthoriseRecurringPaymentRevokeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RecurringPaymentId

	if all {
		switch v := interface{}(m.GetCredential()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthoriseRecurringPaymentRevokeRequestValidationError{
					field:  "Credential",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthoriseRecurringPaymentRevokeRequestValidationError{
					field:  "Credential",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCredential()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthoriseRecurringPaymentRevokeRequestValidationError{
				field:  "Credential",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClientRequestId

	// no validation rules for CurrentActorId

	if all {
		switch v := interface{}(m.GetClientId()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthoriseRecurringPaymentRevokeRequestValidationError{
					field:  "ClientId",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthoriseRecurringPaymentRevokeRequestValidationError{
					field:  "ClientId",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClientId()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthoriseRecurringPaymentRevokeRequestValidationError{
				field:  "ClientId",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AuthoriseRecurringPaymentRevokeRequestMultiError(errors)
	}

	return nil
}

// AuthoriseRecurringPaymentRevokeRequestMultiError is an error wrapping
// multiple validation errors returned by
// AuthoriseRecurringPaymentRevokeRequest.ValidateAll() if the designated
// constraints aren't met.
type AuthoriseRecurringPaymentRevokeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuthoriseRecurringPaymentRevokeRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuthoriseRecurringPaymentRevokeRequestMultiError) AllErrors() []error { return m }

// AuthoriseRecurringPaymentRevokeRequestValidationError is the validation
// error returned by AuthoriseRecurringPaymentRevokeRequest.Validate if the
// designated constraints aren't met.
type AuthoriseRecurringPaymentRevokeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuthoriseRecurringPaymentRevokeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuthoriseRecurringPaymentRevokeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuthoriseRecurringPaymentRevokeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuthoriseRecurringPaymentRevokeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuthoriseRecurringPaymentRevokeRequestValidationError) ErrorName() string {
	return "AuthoriseRecurringPaymentRevokeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AuthoriseRecurringPaymentRevokeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuthoriseRecurringPaymentRevokeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuthoriseRecurringPaymentRevokeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuthoriseRecurringPaymentRevokeRequestValidationError{}

// Validate checks the field values on AuthoriseRecurringPaymentRevokeResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *AuthoriseRecurringPaymentRevokeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// AuthoriseRecurringPaymentRevokeResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// AuthoriseRecurringPaymentRevokeResponseMultiError, or nil if none found.
func (m *AuthoriseRecurringPaymentRevokeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AuthoriseRecurringPaymentRevokeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthoriseRecurringPaymentRevokeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthoriseRecurringPaymentRevokeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthoriseRecurringPaymentRevokeResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetActionDetailedStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthoriseRecurringPaymentRevokeResponseValidationError{
					field:  "ActionDetailedStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthoriseRecurringPaymentRevokeResponseValidationError{
					field:  "ActionDetailedStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActionDetailedStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthoriseRecurringPaymentRevokeResponseValidationError{
				field:  "ActionDetailedStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AuthoriseRecurringPaymentRevokeResponseMultiError(errors)
	}

	return nil
}

// AuthoriseRecurringPaymentRevokeResponseMultiError is an error wrapping
// multiple validation errors returned by
// AuthoriseRecurringPaymentRevokeResponse.ValidateAll() if the designated
// constraints aren't met.
type AuthoriseRecurringPaymentRevokeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuthoriseRecurringPaymentRevokeResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuthoriseRecurringPaymentRevokeResponseMultiError) AllErrors() []error { return m }

// AuthoriseRecurringPaymentRevokeResponseValidationError is the validation
// error returned by AuthoriseRecurringPaymentRevokeResponse.Validate if the
// designated constraints aren't met.
type AuthoriseRecurringPaymentRevokeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuthoriseRecurringPaymentRevokeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuthoriseRecurringPaymentRevokeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuthoriseRecurringPaymentRevokeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuthoriseRecurringPaymentRevokeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuthoriseRecurringPaymentRevokeResponseValidationError) ErrorName() string {
	return "AuthoriseRecurringPaymentRevokeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AuthoriseRecurringPaymentRevokeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuthoriseRecurringPaymentRevokeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuthoriseRecurringPaymentRevokeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuthoriseRecurringPaymentRevokeResponseValidationError{}

// Validate checks the field values on GetRecurringPaymentDetailsByIdRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetRecurringPaymentDetailsByIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRecurringPaymentDetailsByIdRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetRecurringPaymentDetailsByIdRequestMultiError, or nil if none found.
func (m *GetRecurringPaymentDetailsByIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRecurringPaymentDetailsByIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RecurringPaymentId

	// no validation rules for CurrentActorId

	if len(errors) > 0 {
		return GetRecurringPaymentDetailsByIdRequestMultiError(errors)
	}

	return nil
}

// GetRecurringPaymentDetailsByIdRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetRecurringPaymentDetailsByIdRequest.ValidateAll() if the designated
// constraints aren't met.
type GetRecurringPaymentDetailsByIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRecurringPaymentDetailsByIdRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRecurringPaymentDetailsByIdRequestMultiError) AllErrors() []error { return m }

// GetRecurringPaymentDetailsByIdRequestValidationError is the validation error
// returned by GetRecurringPaymentDetailsByIdRequest.Validate if the
// designated constraints aren't met.
type GetRecurringPaymentDetailsByIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRecurringPaymentDetailsByIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRecurringPaymentDetailsByIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRecurringPaymentDetailsByIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRecurringPaymentDetailsByIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRecurringPaymentDetailsByIdRequestValidationError) ErrorName() string {
	return "GetRecurringPaymentDetailsByIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRecurringPaymentDetailsByIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRecurringPaymentDetailsByIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRecurringPaymentDetailsByIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRecurringPaymentDetailsByIdRequestValidationError{}

// Validate checks the field values on GetRecurringPaymentDetailsByIdResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetRecurringPaymentDetailsByIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetRecurringPaymentDetailsByIdResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetRecurringPaymentDetailsByIdResponseMultiError, or nil if none found.
func (m *GetRecurringPaymentDetailsByIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRecurringPaymentDetailsByIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRecurringPaymentDetailsByIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRecurringPaymentDetailsByIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRecurringPaymentDetailsByIdResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRecurringPayment()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRecurringPaymentDetailsByIdResponseValidationError{
					field:  "RecurringPayment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRecurringPaymentDetailsByIdResponseValidationError{
					field:  "RecurringPayment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRecurringPayment()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRecurringPaymentDetailsByIdResponseValidationError{
				field:  "RecurringPayment",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ExecutionCount

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRecurringPaymentDetailsByIdResponseValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRecurringPaymentDetailsByIdResponseValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRecurringPaymentDetailsByIdResponseValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ImageUrl

	// no validation rules for BgColor

	// no validation rules for SecondaryActorId

	switch v := m.AdditionalParams.(type) {
	case *GetRecurringPaymentDetailsByIdResponse_UpiMandateParams:
		if v == nil {
			err := GetRecurringPaymentDetailsByIdResponseValidationError{
				field:  "AdditionalParams",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUpiMandateParams()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRecurringPaymentDetailsByIdResponseValidationError{
						field:  "UpiMandateParams",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRecurringPaymentDetailsByIdResponseValidationError{
						field:  "UpiMandateParams",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUpiMandateParams()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRecurringPaymentDetailsByIdResponseValidationError{
					field:  "UpiMandateParams",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetRecurringPaymentDetailsByIdResponse_EnachMandateParams:
		if v == nil {
			err := GetRecurringPaymentDetailsByIdResponseValidationError{
				field:  "AdditionalParams",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetEnachMandateParams()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRecurringPaymentDetailsByIdResponseValidationError{
						field:  "EnachMandateParams",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRecurringPaymentDetailsByIdResponseValidationError{
						field:  "EnachMandateParams",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetEnachMandateParams()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRecurringPaymentDetailsByIdResponseValidationError{
					field:  "EnachMandateParams",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.StateParams.(type) {
	case *GetRecurringPaymentDetailsByIdResponse_PendingActionParams:
		if v == nil {
			err := GetRecurringPaymentDetailsByIdResponseValidationError{
				field:  "StateParams",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPendingActionParams()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRecurringPaymentDetailsByIdResponseValidationError{
						field:  "PendingActionParams",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRecurringPaymentDetailsByIdResponseValidationError{
						field:  "PendingActionParams",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPendingActionParams()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRecurringPaymentDetailsByIdResponseValidationError{
					field:  "PendingActionParams",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetRecurringPaymentDetailsByIdResponseMultiError(errors)
	}

	return nil
}

// GetRecurringPaymentDetailsByIdResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetRecurringPaymentDetailsByIdResponse.ValidateAll() if the designated
// constraints aren't met.
type GetRecurringPaymentDetailsByIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRecurringPaymentDetailsByIdResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRecurringPaymentDetailsByIdResponseMultiError) AllErrors() []error { return m }

// GetRecurringPaymentDetailsByIdResponseValidationError is the validation
// error returned by GetRecurringPaymentDetailsByIdResponse.Validate if the
// designated constraints aren't met.
type GetRecurringPaymentDetailsByIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRecurringPaymentDetailsByIdResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRecurringPaymentDetailsByIdResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRecurringPaymentDetailsByIdResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRecurringPaymentDetailsByIdResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRecurringPaymentDetailsByIdResponseValidationError) ErrorName() string {
	return "GetRecurringPaymentDetailsByIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRecurringPaymentDetailsByIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRecurringPaymentDetailsByIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRecurringPaymentDetailsByIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRecurringPaymentDetailsByIdResponseValidationError{}

// Validate checks the field values on GetRecurringPaymentByExternalIdRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetRecurringPaymentByExternalIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetRecurringPaymentByExternalIdRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetRecurringPaymentByExternalIdRequestMultiError, or nil if none found.
func (m *GetRecurringPaymentByExternalIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRecurringPaymentByExternalIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ExternalId

	if len(errors) > 0 {
		return GetRecurringPaymentByExternalIdRequestMultiError(errors)
	}

	return nil
}

// GetRecurringPaymentByExternalIdRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetRecurringPaymentByExternalIdRequest.ValidateAll() if the designated
// constraints aren't met.
type GetRecurringPaymentByExternalIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRecurringPaymentByExternalIdRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRecurringPaymentByExternalIdRequestMultiError) AllErrors() []error { return m }

// GetRecurringPaymentByExternalIdRequestValidationError is the validation
// error returned by GetRecurringPaymentByExternalIdRequest.Validate if the
// designated constraints aren't met.
type GetRecurringPaymentByExternalIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRecurringPaymentByExternalIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRecurringPaymentByExternalIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRecurringPaymentByExternalIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRecurringPaymentByExternalIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRecurringPaymentByExternalIdRequestValidationError) ErrorName() string {
	return "GetRecurringPaymentByExternalIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRecurringPaymentByExternalIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRecurringPaymentByExternalIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRecurringPaymentByExternalIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRecurringPaymentByExternalIdRequestValidationError{}

// Validate checks the field values on GetRecurringPaymentByExternalIdResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetRecurringPaymentByExternalIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetRecurringPaymentByExternalIdResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetRecurringPaymentByExternalIdResponseMultiError, or nil if none found.
func (m *GetRecurringPaymentByExternalIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRecurringPaymentByExternalIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRecurringPaymentByExternalIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRecurringPaymentByExternalIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRecurringPaymentByExternalIdResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRecurringPayment()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRecurringPaymentByExternalIdResponseValidationError{
					field:  "RecurringPayment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRecurringPaymentByExternalIdResponseValidationError{
					field:  "RecurringPayment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRecurringPayment()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRecurringPaymentByExternalIdResponseValidationError{
				field:  "RecurringPayment",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetRecurringPaymentByExternalIdResponseMultiError(errors)
	}

	return nil
}

// GetRecurringPaymentByExternalIdResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetRecurringPaymentByExternalIdResponse.ValidateAll() if the designated
// constraints aren't met.
type GetRecurringPaymentByExternalIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRecurringPaymentByExternalIdResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRecurringPaymentByExternalIdResponseMultiError) AllErrors() []error { return m }

// GetRecurringPaymentByExternalIdResponseValidationError is the validation
// error returned by GetRecurringPaymentByExternalIdResponse.Validate if the
// designated constraints aren't met.
type GetRecurringPaymentByExternalIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRecurringPaymentByExternalIdResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRecurringPaymentByExternalIdResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRecurringPaymentByExternalIdResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRecurringPaymentByExternalIdResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRecurringPaymentByExternalIdResponseValidationError) ErrorName() string {
	return "GetRecurringPaymentByExternalIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRecurringPaymentByExternalIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRecurringPaymentByExternalIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRecurringPaymentByExternalIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRecurringPaymentByExternalIdResponseValidationError{}

// Validate checks the field values on UPIMandateParams with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UPIMandateParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UPIMandateParams with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UPIMandateParamsMultiError, or nil if none found.
func (m *UPIMandateParams) ValidateAll() error {
	return m.validate(true)
}

func (m *UPIMandateParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MandateIdentifier

	// no validation rules for Umn

	// no validation rules for Urn

	// no validation rules for Vpa

	if len(errors) > 0 {
		return UPIMandateParamsMultiError(errors)
	}

	return nil
}

// UPIMandateParamsMultiError is an error wrapping multiple validation errors
// returned by UPIMandateParams.ValidateAll() if the designated constraints
// aren't met.
type UPIMandateParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UPIMandateParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UPIMandateParamsMultiError) AllErrors() []error { return m }

// UPIMandateParamsValidationError is the validation error returned by
// UPIMandateParams.Validate if the designated constraints aren't met.
type UPIMandateParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UPIMandateParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UPIMandateParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UPIMandateParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UPIMandateParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UPIMandateParamsValidationError) ErrorName() string { return "UPIMandateParamsValidationError" }

// Error satisfies the builtin error interface
func (e UPIMandateParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUPIMandateParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UPIMandateParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UPIMandateParamsValidationError{}

// Validate checks the field values on EnachMandateParams with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EnachMandateParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EnachMandateParams with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EnachMandateParamsMultiError, or nil if none found.
func (m *EnachMandateParams) ValidateAll() error {
	return m.validate(true)
}

func (m *EnachMandateParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Umrn

	if len(errors) > 0 {
		return EnachMandateParamsMultiError(errors)
	}

	return nil
}

// EnachMandateParamsMultiError is an error wrapping multiple validation errors
// returned by EnachMandateParams.ValidateAll() if the designated constraints
// aren't met.
type EnachMandateParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EnachMandateParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EnachMandateParamsMultiError) AllErrors() []error { return m }

// EnachMandateParamsValidationError is the validation error returned by
// EnachMandateParams.Validate if the designated constraints aren't met.
type EnachMandateParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EnachMandateParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EnachMandateParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EnachMandateParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EnachMandateParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EnachMandateParamsValidationError) ErrorName() string {
	return "EnachMandateParamsValidationError"
}

// Error satisfies the builtin error interface
func (e EnachMandateParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEnachMandateParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EnachMandateParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EnachMandateParamsValidationError{}

// Validate checks the field values on PendingActionParams with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PendingActionParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PendingActionParams with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PendingActionParamsMultiError, or nil if none found.
func (m *PendingActionParams) ValidateAll() error {
	return m.validate(true)
}

func (m *PendingActionParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TransactionId

	// no validation rules for CredBlockType

	if all {
		switch v := interface{}(m.GetExpiry()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PendingActionParamsValidationError{
					field:  "Expiry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PendingActionParamsValidationError{
					field:  "Expiry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpiry()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PendingActionParamsValidationError{
				field:  "Expiry",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClientRequestId

	for idx, item := range m.GetTransactionAttributes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PendingActionParamsValidationError{
						field:  fmt.Sprintf("TransactionAttributes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PendingActionParamsValidationError{
						field:  fmt.Sprintf("TransactionAttributes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PendingActionParamsValidationError{
					field:  fmt.Sprintf("TransactionAttributes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetPendingRequestActions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PendingActionParamsValidationError{
						field:  fmt.Sprintf("PendingRequestActions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PendingActionParamsValidationError{
						field:  fmt.Sprintf("PendingRequestActions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PendingActionParamsValidationError{
					field:  fmt.Sprintf("PendingRequestActions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	switch v := m.AdditionalParams.(type) {
	case *PendingActionParams_UpdatedParams:
		if v == nil {
			err := PendingActionParamsValidationError{
				field:  "AdditionalParams",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUpdatedParams()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PendingActionParamsValidationError{
						field:  "UpdatedParams",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PendingActionParamsValidationError{
						field:  "UpdatedParams",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUpdatedParams()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PendingActionParamsValidationError{
					field:  "UpdatedParams",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return PendingActionParamsMultiError(errors)
	}

	return nil
}

// PendingActionParamsMultiError is an error wrapping multiple validation
// errors returned by PendingActionParams.ValidateAll() if the designated
// constraints aren't met.
type PendingActionParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PendingActionParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PendingActionParamsMultiError) AllErrors() []error { return m }

// PendingActionParamsValidationError is the validation error returned by
// PendingActionParams.Validate if the designated constraints aren't met.
type PendingActionParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PendingActionParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PendingActionParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PendingActionParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PendingActionParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PendingActionParamsValidationError) ErrorName() string {
	return "PendingActionParamsValidationError"
}

// Error satisfies the builtin error interface
func (e PendingActionParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPendingActionParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PendingActionParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PendingActionParamsValidationError{}

// Validate checks the field values on GetExecutionsForRecurringPaymentRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetExecutionsForRecurringPaymentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetExecutionsForRecurringPaymentRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetExecutionsForRecurringPaymentRequestMultiError, or nil if none found.
func (m *GetExecutionsForRecurringPaymentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExecutionsForRecurringPaymentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RecurringPaymentId

	if all {
		switch v := interface{}(m.GetStartTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExecutionsForRecurringPaymentRequestValidationError{
					field:  "StartTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExecutionsForRecurringPaymentRequestValidationError{
					field:  "StartTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExecutionsForRecurringPaymentRequestValidationError{
				field:  "StartTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PageSize

	// no validation rules for Offset

	// no validation rules for Descending

	// no validation rules for CurrentActorId

	if len(errors) > 0 {
		return GetExecutionsForRecurringPaymentRequestMultiError(errors)
	}

	return nil
}

// GetExecutionsForRecurringPaymentRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetExecutionsForRecurringPaymentRequest.ValidateAll() if the designated
// constraints aren't met.
type GetExecutionsForRecurringPaymentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExecutionsForRecurringPaymentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExecutionsForRecurringPaymentRequestMultiError) AllErrors() []error { return m }

// GetExecutionsForRecurringPaymentRequestValidationError is the validation
// error returned by GetExecutionsForRecurringPaymentRequest.Validate if the
// designated constraints aren't met.
type GetExecutionsForRecurringPaymentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExecutionsForRecurringPaymentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExecutionsForRecurringPaymentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExecutionsForRecurringPaymentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExecutionsForRecurringPaymentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExecutionsForRecurringPaymentRequestValidationError) ErrorName() string {
	return "GetExecutionsForRecurringPaymentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetExecutionsForRecurringPaymentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExecutionsForRecurringPaymentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExecutionsForRecurringPaymentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExecutionsForRecurringPaymentRequestValidationError{}

// Validate checks the field values on GetExecutionsForRecurringPaymentResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetExecutionsForRecurringPaymentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetExecutionsForRecurringPaymentResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetExecutionsForRecurringPaymentResponseMultiError, or nil if none found.
func (m *GetExecutionsForRecurringPaymentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExecutionsForRecurringPaymentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExecutionsForRecurringPaymentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExecutionsForRecurringPaymentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExecutionsForRecurringPaymentResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetExecutions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetExecutionsForRecurringPaymentResponseValidationError{
						field:  fmt.Sprintf("Executions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetExecutionsForRecurringPaymentResponseValidationError{
						field:  fmt.Sprintf("Executions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetExecutionsForRecurringPaymentResponseValidationError{
					field:  fmt.Sprintf("Executions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetExecutionsForRecurringPaymentResponseMultiError(errors)
	}

	return nil
}

// GetExecutionsForRecurringPaymentResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetExecutionsForRecurringPaymentResponse.ValidateAll() if the designated
// constraints aren't met.
type GetExecutionsForRecurringPaymentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExecutionsForRecurringPaymentResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExecutionsForRecurringPaymentResponseMultiError) AllErrors() []error { return m }

// GetExecutionsForRecurringPaymentResponseValidationError is the validation
// error returned by GetExecutionsForRecurringPaymentResponse.Validate if the
// designated constraints aren't met.
type GetExecutionsForRecurringPaymentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExecutionsForRecurringPaymentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExecutionsForRecurringPaymentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExecutionsForRecurringPaymentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExecutionsForRecurringPaymentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExecutionsForRecurringPaymentResponseValidationError) ErrorName() string {
	return "GetExecutionsForRecurringPaymentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetExecutionsForRecurringPaymentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExecutionsForRecurringPaymentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExecutionsForRecurringPaymentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExecutionsForRecurringPaymentResponseValidationError{}

// Validate checks the field values on DeclineRecurringPaymentActionRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *DeclineRecurringPaymentActionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeclineRecurringPaymentActionRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// DeclineRecurringPaymentActionRequestMultiError, or nil if none found.
func (m *DeclineRecurringPaymentActionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeclineRecurringPaymentActionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RecurringPaymentId

	// no validation rules for Action

	// no validation rules for ClientRequestId

	// no validation rules for CurrentActorId

	if len(errors) > 0 {
		return DeclineRecurringPaymentActionRequestMultiError(errors)
	}

	return nil
}

// DeclineRecurringPaymentActionRequestMultiError is an error wrapping multiple
// validation errors returned by
// DeclineRecurringPaymentActionRequest.ValidateAll() if the designated
// constraints aren't met.
type DeclineRecurringPaymentActionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeclineRecurringPaymentActionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeclineRecurringPaymentActionRequestMultiError) AllErrors() []error { return m }

// DeclineRecurringPaymentActionRequestValidationError is the validation error
// returned by DeclineRecurringPaymentActionRequest.Validate if the designated
// constraints aren't met.
type DeclineRecurringPaymentActionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeclineRecurringPaymentActionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeclineRecurringPaymentActionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeclineRecurringPaymentActionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeclineRecurringPaymentActionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeclineRecurringPaymentActionRequestValidationError) ErrorName() string {
	return "DeclineRecurringPaymentActionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeclineRecurringPaymentActionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeclineRecurringPaymentActionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeclineRecurringPaymentActionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeclineRecurringPaymentActionRequestValidationError{}

// Validate checks the field values on DeclineRecurringPaymentActionResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *DeclineRecurringPaymentActionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeclineRecurringPaymentActionResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// DeclineRecurringPaymentActionResponseMultiError, or nil if none found.
func (m *DeclineRecurringPaymentActionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeclineRecurringPaymentActionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeclineRecurringPaymentActionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeclineRecurringPaymentActionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeclineRecurringPaymentActionResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeclineRecurringPaymentActionResponseMultiError(errors)
	}

	return nil
}

// DeclineRecurringPaymentActionResponseMultiError is an error wrapping
// multiple validation errors returned by
// DeclineRecurringPaymentActionResponse.ValidateAll() if the designated
// constraints aren't met.
type DeclineRecurringPaymentActionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeclineRecurringPaymentActionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeclineRecurringPaymentActionResponseMultiError) AllErrors() []error { return m }

// DeclineRecurringPaymentActionResponseValidationError is the validation error
// returned by DeclineRecurringPaymentActionResponse.Validate if the
// designated constraints aren't met.
type DeclineRecurringPaymentActionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeclineRecurringPaymentActionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeclineRecurringPaymentActionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeclineRecurringPaymentActionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeclineRecurringPaymentActionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeclineRecurringPaymentActionResponseValidationError) ErrorName() string {
	return "DeclineRecurringPaymentActionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeclineRecurringPaymentActionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeclineRecurringPaymentActionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeclineRecurringPaymentActionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeclineRecurringPaymentActionResponseValidationError{}

// Validate checks the field values on GetRecurringPaymentActionStatusRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetRecurringPaymentActionStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetRecurringPaymentActionStatusRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetRecurringPaymentActionStatusRequestMultiError, or nil if none found.
func (m *GetRecurringPaymentActionStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRecurringPaymentActionStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RecurringPaymentId

	// no validation rules for Action

	// no validation rules for ClientRequestId

	// no validation rules for CurrentActorId

	if all {
		switch v := interface{}(m.GetClientId()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRecurringPaymentActionStatusRequestValidationError{
					field:  "ClientId",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRecurringPaymentActionStatusRequestValidationError{
					field:  "ClientId",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClientId()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRecurringPaymentActionStatusRequestValidationError{
				field:  "ClientId",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetRecurringPaymentActionStatusRequestMultiError(errors)
	}

	return nil
}

// GetRecurringPaymentActionStatusRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetRecurringPaymentActionStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type GetRecurringPaymentActionStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRecurringPaymentActionStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRecurringPaymentActionStatusRequestMultiError) AllErrors() []error { return m }

// GetRecurringPaymentActionStatusRequestValidationError is the validation
// error returned by GetRecurringPaymentActionStatusRequest.Validate if the
// designated constraints aren't met.
type GetRecurringPaymentActionStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRecurringPaymentActionStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRecurringPaymentActionStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRecurringPaymentActionStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRecurringPaymentActionStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRecurringPaymentActionStatusRequestValidationError) ErrorName() string {
	return "GetRecurringPaymentActionStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRecurringPaymentActionStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRecurringPaymentActionStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRecurringPaymentActionStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRecurringPaymentActionStatusRequestValidationError{}

// Validate checks the field values on GetRecurringPaymentActionStatusResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetRecurringPaymentActionStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetRecurringPaymentActionStatusResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetRecurringPaymentActionStatusResponseMultiError, or nil if none found.
func (m *GetRecurringPaymentActionStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRecurringPaymentActionStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRecurringPaymentActionStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRecurringPaymentActionStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRecurringPaymentActionStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActionState

	if all {
		switch v := interface{}(m.GetActionDetailedStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRecurringPaymentActionStatusResponseValidationError{
					field:  "ActionDetailedStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRecurringPaymentActionStatusResponseValidationError{
					field:  "ActionDetailedStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActionDetailedStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRecurringPaymentActionStatusResponseValidationError{
				field:  "ActionDetailedStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetWarningMessage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRecurringPaymentActionStatusResponseValidationError{
					field:  "WarningMessage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRecurringPaymentActionStatusResponseValidationError{
					field:  "WarningMessage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWarningMessage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRecurringPaymentActionStatusResponseValidationError{
				field:  "WarningMessage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPostActionDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRecurringPaymentActionStatusResponseValidationError{
					field:  "PostActionDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRecurringPaymentActionStatusResponseValidationError{
					field:  "PostActionDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPostActionDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRecurringPaymentActionStatusResponseValidationError{
				field:  "PostActionDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.MetaData.(type) {
	case *GetRecurringPaymentActionStatusResponse_ExecutionMetaData:
		if v == nil {
			err := GetRecurringPaymentActionStatusResponseValidationError{
				field:  "MetaData",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetExecutionMetaData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRecurringPaymentActionStatusResponseValidationError{
						field:  "ExecutionMetaData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRecurringPaymentActionStatusResponseValidationError{
						field:  "ExecutionMetaData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetExecutionMetaData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRecurringPaymentActionStatusResponseValidationError{
					field:  "ExecutionMetaData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetRecurringPaymentActionStatusResponseMultiError(errors)
	}

	return nil
}

// GetRecurringPaymentActionStatusResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetRecurringPaymentActionStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type GetRecurringPaymentActionStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRecurringPaymentActionStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRecurringPaymentActionStatusResponseMultiError) AllErrors() []error { return m }

// GetRecurringPaymentActionStatusResponseValidationError is the validation
// error returned by GetRecurringPaymentActionStatusResponse.Validate if the
// designated constraints aren't met.
type GetRecurringPaymentActionStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRecurringPaymentActionStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRecurringPaymentActionStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRecurringPaymentActionStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRecurringPaymentActionStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRecurringPaymentActionStatusResponseValidationError) ErrorName() string {
	return "GetRecurringPaymentActionStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRecurringPaymentActionStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRecurringPaymentActionStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRecurringPaymentActionStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRecurringPaymentActionStatusResponseValidationError{}

// Validate checks the field values on GetRecurringPaymentsForActorRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetRecurringPaymentsForActorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRecurringPaymentsForActorRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetRecurringPaymentsForActorRequestMultiError, or nil if none found.
func (m *GetRecurringPaymentsForActorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRecurringPaymentsForActorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CurrentActorId

	if all {
		switch v := interface{}(m.GetStartTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRecurringPaymentsForActorRequestValidationError{
					field:  "StartTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRecurringPaymentsForActorRequestValidationError{
					field:  "StartTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRecurringPaymentsForActorRequestValidationError{
				field:  "StartTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PageSize

	// no validation rules for Offset

	// no validation rules for Descending

	if len(errors) > 0 {
		return GetRecurringPaymentsForActorRequestMultiError(errors)
	}

	return nil
}

// GetRecurringPaymentsForActorRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetRecurringPaymentsForActorRequest.ValidateAll() if the designated
// constraints aren't met.
type GetRecurringPaymentsForActorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRecurringPaymentsForActorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRecurringPaymentsForActorRequestMultiError) AllErrors() []error { return m }

// GetRecurringPaymentsForActorRequestValidationError is the validation error
// returned by GetRecurringPaymentsForActorRequest.Validate if the designated
// constraints aren't met.
type GetRecurringPaymentsForActorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRecurringPaymentsForActorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRecurringPaymentsForActorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRecurringPaymentsForActorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRecurringPaymentsForActorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRecurringPaymentsForActorRequestValidationError) ErrorName() string {
	return "GetRecurringPaymentsForActorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRecurringPaymentsForActorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRecurringPaymentsForActorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRecurringPaymentsForActorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRecurringPaymentsForActorRequestValidationError{}

// Validate checks the field values on GetRecurringPaymentsForActorResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetRecurringPaymentsForActorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRecurringPaymentsForActorResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetRecurringPaymentsForActorResponseMultiError, or nil if none found.
func (m *GetRecurringPaymentsForActorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRecurringPaymentsForActorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRecurringPaymentsForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRecurringPaymentsForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRecurringPaymentsForActorResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRecurringPayments() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRecurringPaymentsForActorResponseValidationError{
						field:  fmt.Sprintf("RecurringPayments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRecurringPaymentsForActorResponseValidationError{
						field:  fmt.Sprintf("RecurringPayments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRecurringPaymentsForActorResponseValidationError{
					field:  fmt.Sprintf("RecurringPayments[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetRecurringPaymentsForActorResponseMultiError(errors)
	}

	return nil
}

// GetRecurringPaymentsForActorResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetRecurringPaymentsForActorResponse.ValidateAll() if the designated
// constraints aren't met.
type GetRecurringPaymentsForActorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRecurringPaymentsForActorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRecurringPaymentsForActorResponseMultiError) AllErrors() []error { return m }

// GetRecurringPaymentsForActorResponseValidationError is the validation error
// returned by GetRecurringPaymentsForActorResponse.Validate if the designated
// constraints aren't met.
type GetRecurringPaymentsForActorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRecurringPaymentsForActorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRecurringPaymentsForActorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRecurringPaymentsForActorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRecurringPaymentsForActorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRecurringPaymentsForActorResponseValidationError) ErrorName() string {
	return "GetRecurringPaymentsForActorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRecurringPaymentsForActorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRecurringPaymentsForActorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRecurringPaymentsForActorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRecurringPaymentsForActorResponseValidationError{}

// Validate checks the field values on MandateAuthHeader with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *MandateAuthHeader) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MandateAuthHeader with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MandateAuthHeaderMultiError, or nil if none found.
func (m *MandateAuthHeader) ValidateAll() error {
	return m.validate(true)
}

func (m *MandateAuthHeader) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetDevice() == nil {
		err := MandateAuthHeaderValidationError{
			field:  "Device",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MandateAuthHeaderValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MandateAuthHeaderValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MandateAuthHeaderValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNpciCredBlock()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MandateAuthHeaderValidationError{
					field:  "NpciCredBlock",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MandateAuthHeaderValidationError{
					field:  "NpciCredBlock",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNpciCredBlock()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MandateAuthHeaderValidationError{
				field:  "NpciCredBlock",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MandateAuthHeaderMultiError(errors)
	}

	return nil
}

// MandateAuthHeaderMultiError is an error wrapping multiple validation errors
// returned by MandateAuthHeader.ValidateAll() if the designated constraints
// aren't met.
type MandateAuthHeaderMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MandateAuthHeaderMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MandateAuthHeaderMultiError) AllErrors() []error { return m }

// MandateAuthHeaderValidationError is the validation error returned by
// MandateAuthHeader.Validate if the designated constraints aren't met.
type MandateAuthHeaderValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MandateAuthHeaderValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MandateAuthHeaderValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MandateAuthHeaderValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MandateAuthHeaderValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MandateAuthHeaderValidationError) ErrorName() string {
	return "MandateAuthHeaderValidationError"
}

// Error satisfies the builtin error interface
func (e MandateAuthHeaderValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMandateAuthHeader.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MandateAuthHeaderValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MandateAuthHeaderValidationError{}

// Validate checks the field values on GetRecurringPaymentsCountForActorRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetRecurringPaymentsCountForActorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetRecurringPaymentsCountForActorRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetRecurringPaymentsCountForActorRequestMultiError, or nil if none found.
func (m *GetRecurringPaymentsCountForActorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRecurringPaymentsCountForActorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetRecurringPaymentsCountForActorRequestMultiError(errors)
	}

	return nil
}

// GetRecurringPaymentsCountForActorRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetRecurringPaymentsCountForActorRequest.ValidateAll() if the designated
// constraints aren't met.
type GetRecurringPaymentsCountForActorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRecurringPaymentsCountForActorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRecurringPaymentsCountForActorRequestMultiError) AllErrors() []error { return m }

// GetRecurringPaymentsCountForActorRequestValidationError is the validation
// error returned by GetRecurringPaymentsCountForActorRequest.Validate if the
// designated constraints aren't met.
type GetRecurringPaymentsCountForActorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRecurringPaymentsCountForActorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRecurringPaymentsCountForActorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRecurringPaymentsCountForActorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRecurringPaymentsCountForActorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRecurringPaymentsCountForActorRequestValidationError) ErrorName() string {
	return "GetRecurringPaymentsCountForActorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRecurringPaymentsCountForActorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRecurringPaymentsCountForActorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRecurringPaymentsCountForActorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRecurringPaymentsCountForActorRequestValidationError{}

// Validate checks the field values on
// GetRecurringPaymentsCountForActorResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetRecurringPaymentsCountForActorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetRecurringPaymentsCountForActorResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetRecurringPaymentsCountForActorResponseMultiError, or nil if none found.
func (m *GetRecurringPaymentsCountForActorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRecurringPaymentsCountForActorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRecurringPaymentsCountForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRecurringPaymentsCountForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRecurringPaymentsCountForActorResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Count

	if len(errors) > 0 {
		return GetRecurringPaymentsCountForActorResponseMultiError(errors)
	}

	return nil
}

// GetRecurringPaymentsCountForActorResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetRecurringPaymentsCountForActorResponse.ValidateAll() if the designated
// constraints aren't met.
type GetRecurringPaymentsCountForActorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRecurringPaymentsCountForActorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRecurringPaymentsCountForActorResponseMultiError) AllErrors() []error { return m }

// GetRecurringPaymentsCountForActorResponseValidationError is the validation
// error returned by GetRecurringPaymentsCountForActorResponse.Validate if the
// designated constraints aren't met.
type GetRecurringPaymentsCountForActorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRecurringPaymentsCountForActorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRecurringPaymentsCountForActorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRecurringPaymentsCountForActorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRecurringPaymentsCountForActorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRecurringPaymentsCountForActorResponseValidationError) ErrorName() string {
	return "GetRecurringPaymentsCountForActorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRecurringPaymentsCountForActorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRecurringPaymentsCountForActorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRecurringPaymentsCountForActorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRecurringPaymentsCountForActorResponseValidationError{}

// Validate checks the field values on TransactionAttribute with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TransactionAttribute) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TransactionAttribute with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TransactionAttributeMultiError, or nil if none found.
func (m *TransactionAttribute) ValidateAll() error {
	return m.validate(true)
}

func (m *TransactionAttribute) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PayerAccountId

	// no validation rules for TransactionId

	// no validation rules for MerchantRefId

	// no validation rules for PaymentProtocol

	// no validation rules for ReferenceUrl

	// no validation rules for PayeeActorName

	// no validation rules for PayerMaskedAccountNumber

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TransactionAttributeValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TransactionAttributeValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TransactionAttributeValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Remarks

	// no validation rules for PayerPaymentInstrument

	// no validation rules for PayeePaymentInstrument

	// no validation rules for DisplayPayeePaymentInstrument

	if len(errors) > 0 {
		return TransactionAttributeMultiError(errors)
	}

	return nil
}

// TransactionAttributeMultiError is an error wrapping multiple validation
// errors returned by TransactionAttribute.ValidateAll() if the designated
// constraints aren't met.
type TransactionAttributeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TransactionAttributeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TransactionAttributeMultiError) AllErrors() []error { return m }

// TransactionAttributeValidationError is the validation error returned by
// TransactionAttribute.Validate if the designated constraints aren't met.
type TransactionAttributeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TransactionAttributeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TransactionAttributeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TransactionAttributeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TransactionAttributeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TransactionAttributeValidationError) ErrorName() string {
	return "TransactionAttributeValidationError"
}

// Error satisfies the builtin error interface
func (e TransactionAttributeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTransactionAttribute.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TransactionAttributeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TransactionAttributeValidationError{}

// Validate checks the field values on RecurringPaymentPendingRequestAction
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *RecurringPaymentPendingRequestAction) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecurringPaymentPendingRequestAction
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// RecurringPaymentPendingRequestActionMultiError, or nil if none found.
func (m *RecurringPaymentPendingRequestAction) ValidateAll() error {
	return m.validate(true)
}

func (m *RecurringPaymentPendingRequestAction) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PendingAction

	// no validation rules for IsAuthRequired

	if len(errors) > 0 {
		return RecurringPaymentPendingRequestActionMultiError(errors)
	}

	return nil
}

// RecurringPaymentPendingRequestActionMultiError is an error wrapping multiple
// validation errors returned by
// RecurringPaymentPendingRequestAction.ValidateAll() if the designated
// constraints aren't met.
type RecurringPaymentPendingRequestActionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecurringPaymentPendingRequestActionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecurringPaymentPendingRequestActionMultiError) AllErrors() []error { return m }

// RecurringPaymentPendingRequestActionValidationError is the validation error
// returned by RecurringPaymentPendingRequestAction.Validate if the designated
// constraints aren't met.
type RecurringPaymentPendingRequestActionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecurringPaymentPendingRequestActionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecurringPaymentPendingRequestActionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecurringPaymentPendingRequestActionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecurringPaymentPendingRequestActionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecurringPaymentPendingRequestActionValidationError) ErrorName() string {
	return "RecurringPaymentPendingRequestActionValidationError"
}

// Error satisfies the builtin error interface
func (e RecurringPaymentPendingRequestActionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecurringPaymentPendingRequestAction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecurringPaymentPendingRequestActionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecurringPaymentPendingRequestActionValidationError{}

// Validate checks the field values on UpiMandateExecuteInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpiMandateExecuteInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpiMandateExecuteInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpiMandateExecuteInfoMultiError, or nil if none found.
func (m *UpiMandateExecuteInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *UpiMandateExecuteInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPaymentRequestInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiMandateExecuteInfoValidationError{
					field:  "PaymentRequestInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiMandateExecuteInfoValidationError{
					field:  "PaymentRequestInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPaymentRequestInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiMandateExecuteInfoValidationError{
				field:  "PaymentRequestInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Utr

	// no validation rules for Note

	// no validation rules for SeqNum

	if len(errors) > 0 {
		return UpiMandateExecuteInfoMultiError(errors)
	}

	return nil
}

// UpiMandateExecuteInfoMultiError is an error wrapping multiple validation
// errors returned by UpiMandateExecuteInfo.ValidateAll() if the designated
// constraints aren't met.
type UpiMandateExecuteInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpiMandateExecuteInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpiMandateExecuteInfoMultiError) AllErrors() []error { return m }

// UpiMandateExecuteInfoValidationError is the validation error returned by
// UpiMandateExecuteInfo.Validate if the designated constraints aren't met.
type UpiMandateExecuteInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpiMandateExecuteInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpiMandateExecuteInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpiMandateExecuteInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpiMandateExecuteInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpiMandateExecuteInfoValidationError) ErrorName() string {
	return "UpiMandateExecuteInfoValidationError"
}

// Error satisfies the builtin error interface
func (e UpiMandateExecuteInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpiMandateExecuteInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpiMandateExecuteInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpiMandateExecuteInfoValidationError{}

// Validate checks the field values on ExecutionMetaData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ExecutionMetaData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExecutionMetaData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExecutionMetaDataMultiError, or nil if none found.
func (m *ExecutionMetaData) ValidateAll() error {
	return m.validate(true)
}

func (m *ExecutionMetaData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UtrRefNumber

	if all {
		switch v := interface{}(m.GetTransactionTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExecutionMetaDataValidationError{
					field:  "TransactionTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExecutionMetaDataValidationError{
					field:  "TransactionTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransactionTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExecutionMetaDataValidationError{
				field:  "TransactionTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FailureResponseCode

	// no validation rules for ExecProtocol

	if len(errors) > 0 {
		return ExecutionMetaDataMultiError(errors)
	}

	return nil
}

// ExecutionMetaDataMultiError is an error wrapping multiple validation errors
// returned by ExecutionMetaData.ValidateAll() if the designated constraints
// aren't met.
type ExecutionMetaDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExecutionMetaDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExecutionMetaDataMultiError) AllErrors() []error { return m }

// ExecutionMetaDataValidationError is the validation error returned by
// ExecutionMetaData.Validate if the designated constraints aren't met.
type ExecutionMetaDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExecutionMetaDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExecutionMetaDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExecutionMetaDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExecutionMetaDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExecutionMetaDataValidationError) ErrorName() string {
	return "ExecutionMetaDataValidationError"
}

// Error satisfies the builtin error interface
func (e ExecutionMetaDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExecutionMetaData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExecutionMetaDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExecutionMetaDataValidationError{}

// Validate checks the field values on AuthoriseExecuteRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AuthoriseExecuteRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AuthoriseExecuteRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AuthoriseExecuteRequestMultiError, or nil if none found.
func (m *AuthoriseExecuteRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AuthoriseExecuteRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ClientRequestId

	// no validation rules for CurrentActorId

	if all {
		switch v := interface{}(m.GetCredential()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthoriseExecuteRequestValidationError{
					field:  "Credential",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthoriseExecuteRequestValidationError{
					field:  "Credential",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCredential()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthoriseExecuteRequestValidationError{
				field:  "Credential",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetClientId()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthoriseExecuteRequestValidationError{
					field:  "ClientId",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthoriseExecuteRequestValidationError{
					field:  "ClientId",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClientId()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthoriseExecuteRequestValidationError{
				field:  "ClientId",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AuthoriseExecuteRequestMultiError(errors)
	}

	return nil
}

// AuthoriseExecuteRequestMultiError is an error wrapping multiple validation
// errors returned by AuthoriseExecuteRequest.ValidateAll() if the designated
// constraints aren't met.
type AuthoriseExecuteRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuthoriseExecuteRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuthoriseExecuteRequestMultiError) AllErrors() []error { return m }

// AuthoriseExecuteRequestValidationError is the validation error returned by
// AuthoriseExecuteRequest.Validate if the designated constraints aren't met.
type AuthoriseExecuteRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuthoriseExecuteRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuthoriseExecuteRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuthoriseExecuteRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuthoriseExecuteRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuthoriseExecuteRequestValidationError) ErrorName() string {
	return "AuthoriseExecuteRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AuthoriseExecuteRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuthoriseExecuteRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuthoriseExecuteRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuthoriseExecuteRequestValidationError{}

// Validate checks the field values on AuthoriseExecuteResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AuthoriseExecuteResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AuthoriseExecuteResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AuthoriseExecuteResponseMultiError, or nil if none found.
func (m *AuthoriseExecuteResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AuthoriseExecuteResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthoriseExecuteResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthoriseExecuteResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthoriseExecuteResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AuthoriseExecuteResponseMultiError(errors)
	}

	return nil
}

// AuthoriseExecuteResponseMultiError is an error wrapping multiple validation
// errors returned by AuthoriseExecuteResponse.ValidateAll() if the designated
// constraints aren't met.
type AuthoriseExecuteResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuthoriseExecuteResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuthoriseExecuteResponseMultiError) AllErrors() []error { return m }

// AuthoriseExecuteResponseValidationError is the validation error returned by
// AuthoriseExecuteResponse.Validate if the designated constraints aren't met.
type AuthoriseExecuteResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuthoriseExecuteResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuthoriseExecuteResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuthoriseExecuteResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuthoriseExecuteResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuthoriseExecuteResponseValidationError) ErrorName() string {
	return "AuthoriseExecuteResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AuthoriseExecuteResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuthoriseExecuteResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuthoriseExecuteResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuthoriseExecuteResponseValidationError{}

// Validate checks the field values on GetRecurringPaymentByIdRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRecurringPaymentByIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRecurringPaymentByIdRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetRecurringPaymentByIdRequestMultiError, or nil if none found.
func (m *GetRecurringPaymentByIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRecurringPaymentByIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetRecurringPaymentByIdRequestMultiError(errors)
	}

	return nil
}

// GetRecurringPaymentByIdRequestMultiError is an error wrapping multiple
// validation errors returned by GetRecurringPaymentByIdRequest.ValidateAll()
// if the designated constraints aren't met.
type GetRecurringPaymentByIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRecurringPaymentByIdRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRecurringPaymentByIdRequestMultiError) AllErrors() []error { return m }

// GetRecurringPaymentByIdRequestValidationError is the validation error
// returned by GetRecurringPaymentByIdRequest.Validate if the designated
// constraints aren't met.
type GetRecurringPaymentByIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRecurringPaymentByIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRecurringPaymentByIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRecurringPaymentByIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRecurringPaymentByIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRecurringPaymentByIdRequestValidationError) ErrorName() string {
	return "GetRecurringPaymentByIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRecurringPaymentByIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRecurringPaymentByIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRecurringPaymentByIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRecurringPaymentByIdRequestValidationError{}

// Validate checks the field values on GetRecurringPaymentByIdResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRecurringPaymentByIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRecurringPaymentByIdResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetRecurringPaymentByIdResponseMultiError, or nil if none found.
func (m *GetRecurringPaymentByIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRecurringPaymentByIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRecurringPaymentByIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRecurringPaymentByIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRecurringPaymentByIdResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRecurringPayment()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRecurringPaymentByIdResponseValidationError{
					field:  "RecurringPayment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRecurringPaymentByIdResponseValidationError{
					field:  "RecurringPayment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRecurringPayment()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRecurringPaymentByIdResponseValidationError{
				field:  "RecurringPayment",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetRecurringPaymentByIdResponseMultiError(errors)
	}

	return nil
}

// GetRecurringPaymentByIdResponseMultiError is an error wrapping multiple
// validation errors returned by GetRecurringPaymentByIdResponse.ValidateAll()
// if the designated constraints aren't met.
type GetRecurringPaymentByIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRecurringPaymentByIdResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRecurringPaymentByIdResponseMultiError) AllErrors() []error { return m }

// GetRecurringPaymentByIdResponseValidationError is the validation error
// returned by GetRecurringPaymentByIdResponse.Validate if the designated
// constraints aren't met.
type GetRecurringPaymentByIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRecurringPaymentByIdResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRecurringPaymentByIdResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRecurringPaymentByIdResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRecurringPaymentByIdResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRecurringPaymentByIdResponseValidationError) ErrorName() string {
	return "GetRecurringPaymentByIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRecurringPaymentByIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRecurringPaymentByIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRecurringPaymentByIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRecurringPaymentByIdResponseValidationError{}

// Validate checks the field values on UpdateRecurringPaymentActionsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *UpdateRecurringPaymentActionsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateRecurringPaymentActionsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateRecurringPaymentActionsRequestMultiError, or nil if none found.
func (m *UpdateRecurringPaymentActionsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateRecurringPaymentActionsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ReqId

	if all {
		switch v := interface{}(m.GetActionDetailedStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateRecurringPaymentActionsRequestValidationError{
					field:  "ActionDetailedStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateRecurringPaymentActionsRequestValidationError{
					field:  "ActionDetailedStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActionDetailedStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateRecurringPaymentActionsRequestValidationError{
				field:  "ActionDetailedStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateRecurringPaymentActionsRequestMultiError(errors)
	}

	return nil
}

// UpdateRecurringPaymentActionsRequestMultiError is an error wrapping multiple
// validation errors returned by
// UpdateRecurringPaymentActionsRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateRecurringPaymentActionsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateRecurringPaymentActionsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateRecurringPaymentActionsRequestMultiError) AllErrors() []error { return m }

// UpdateRecurringPaymentActionsRequestValidationError is the validation error
// returned by UpdateRecurringPaymentActionsRequest.Validate if the designated
// constraints aren't met.
type UpdateRecurringPaymentActionsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateRecurringPaymentActionsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateRecurringPaymentActionsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateRecurringPaymentActionsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateRecurringPaymentActionsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateRecurringPaymentActionsRequestValidationError) ErrorName() string {
	return "UpdateRecurringPaymentActionsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateRecurringPaymentActionsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateRecurringPaymentActionsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateRecurringPaymentActionsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateRecurringPaymentActionsRequestValidationError{}

// Validate checks the field values on UpdateRecurringPaymentActionsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *UpdateRecurringPaymentActionsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateRecurringPaymentActionsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateRecurringPaymentActionsResponseMultiError, or nil if none found.
func (m *UpdateRecurringPaymentActionsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateRecurringPaymentActionsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateRecurringPaymentActionsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateRecurringPaymentActionsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateRecurringPaymentActionsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateRecurringPaymentActionsResponseMultiError(errors)
	}

	return nil
}

// UpdateRecurringPaymentActionsResponseMultiError is an error wrapping
// multiple validation errors returned by
// UpdateRecurringPaymentActionsResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateRecurringPaymentActionsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateRecurringPaymentActionsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateRecurringPaymentActionsResponseMultiError) AllErrors() []error { return m }

// UpdateRecurringPaymentActionsResponseValidationError is the validation error
// returned by UpdateRecurringPaymentActionsResponse.Validate if the
// designated constraints aren't met.
type UpdateRecurringPaymentActionsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateRecurringPaymentActionsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateRecurringPaymentActionsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateRecurringPaymentActionsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateRecurringPaymentActionsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateRecurringPaymentActionsResponseValidationError) ErrorName() string {
	return "UpdateRecurringPaymentActionsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateRecurringPaymentActionsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateRecurringPaymentActionsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateRecurringPaymentActionsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateRecurringPaymentActionsResponseValidationError{}

// Validate checks the field values on GetRecurringPaymentsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRecurringPaymentsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRecurringPaymentsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRecurringPaymentsRequestMultiError, or nil if none found.
func (m *GetRecurringPaymentsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRecurringPaymentsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FromActorId

	// no validation rules for ToActorId

	if len(errors) > 0 {
		return GetRecurringPaymentsRequestMultiError(errors)
	}

	return nil
}

// GetRecurringPaymentsRequestMultiError is an error wrapping multiple
// validation errors returned by GetRecurringPaymentsRequest.ValidateAll() if
// the designated constraints aren't met.
type GetRecurringPaymentsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRecurringPaymentsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRecurringPaymentsRequestMultiError) AllErrors() []error { return m }

// GetRecurringPaymentsRequestValidationError is the validation error returned
// by GetRecurringPaymentsRequest.Validate if the designated constraints
// aren't met.
type GetRecurringPaymentsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRecurringPaymentsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRecurringPaymentsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRecurringPaymentsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRecurringPaymentsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRecurringPaymentsRequestValidationError) ErrorName() string {
	return "GetRecurringPaymentsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRecurringPaymentsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRecurringPaymentsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRecurringPaymentsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRecurringPaymentsRequestValidationError{}

// Validate checks the field values on GetRecurringPaymentsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRecurringPaymentsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRecurringPaymentsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRecurringPaymentsResponseMultiError, or nil if none found.
func (m *GetRecurringPaymentsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRecurringPaymentsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRecurringPaymentsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRecurringPaymentsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRecurringPaymentsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRecurringPayments() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRecurringPaymentsResponseValidationError{
						field:  fmt.Sprintf("RecurringPayments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRecurringPaymentsResponseValidationError{
						field:  fmt.Sprintf("RecurringPayments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRecurringPaymentsResponseValidationError{
					field:  fmt.Sprintf("RecurringPayments[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetRecurringPaymentsResponseMultiError(errors)
	}

	return nil
}

// GetRecurringPaymentsResponseMultiError is an error wrapping multiple
// validation errors returned by GetRecurringPaymentsResponse.ValidateAll() if
// the designated constraints aren't met.
type GetRecurringPaymentsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRecurringPaymentsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRecurringPaymentsResponseMultiError) AllErrors() []error { return m }

// GetRecurringPaymentsResponseValidationError is the validation error returned
// by GetRecurringPaymentsResponse.Validate if the designated constraints
// aren't met.
type GetRecurringPaymentsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRecurringPaymentsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRecurringPaymentsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRecurringPaymentsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRecurringPaymentsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRecurringPaymentsResponseValidationError) ErrorName() string {
	return "GetRecurringPaymentsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRecurringPaymentsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRecurringPaymentsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRecurringPaymentsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRecurringPaymentsResponseValidationError{}

// Validate checks the field values on CreatePauseOrUnpauseAttemptRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreatePauseOrUnpauseAttemptRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreatePauseOrUnpauseAttemptRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreatePauseOrUnpauseAttemptRequestMultiError, or nil if none found.
func (m *CreatePauseOrUnpauseAttemptRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreatePauseOrUnpauseAttemptRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RecurringPaymentId

	// no validation rules for ClientRequestId

	if _, ok := _CreatePauseOrUnpauseAttemptRequest_Action_InLookup[m.GetAction()]; !ok {
		err := CreatePauseOrUnpauseAttemptRequestValidationError{
			field:  "Action",
			reason: "value must be in list [PAUSE UNPAUSE]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for CurrentActorId

	// no validation rules for InitiatedBy

	// no validation rules for TransactionId

	// no validation rules for CurrentActorRole

	if _, ok := _CreatePauseOrUnpauseAttemptRequest_Provenance_NotInLookup[m.GetProvenance()]; ok {
		err := CreatePauseOrUnpauseAttemptRequestValidationError{
			field:  "Provenance",
			reason: "value must not be in list [RECURRENCE_PAYMENT_PROVENANCE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Payload

	if all {
		switch v := interface{}(m.GetPauseInterval()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreatePauseOrUnpauseAttemptRequestValidationError{
					field:  "PauseInterval",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreatePauseOrUnpauseAttemptRequestValidationError{
					field:  "PauseInterval",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPauseInterval()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreatePauseOrUnpauseAttemptRequestValidationError{
				field:  "PauseInterval",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Remarks

	if all {
		switch v := interface{}(m.GetExpiry()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreatePauseOrUnpauseAttemptRequestValidationError{
					field:  "Expiry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreatePauseOrUnpauseAttemptRequestValidationError{
					field:  "Expiry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpiry()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreatePauseOrUnpauseAttemptRequestValidationError{
				field:  "Expiry",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetClientId()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreatePauseOrUnpauseAttemptRequestValidationError{
					field:  "ClientId",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreatePauseOrUnpauseAttemptRequestValidationError{
					field:  "ClientId",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClientId()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreatePauseOrUnpauseAttemptRequestValidationError{
				field:  "ClientId",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreatePauseOrUnpauseAttemptRequestMultiError(errors)
	}

	return nil
}

// CreatePauseOrUnpauseAttemptRequestMultiError is an error wrapping multiple
// validation errors returned by
// CreatePauseOrUnpauseAttemptRequest.ValidateAll() if the designated
// constraints aren't met.
type CreatePauseOrUnpauseAttemptRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreatePauseOrUnpauseAttemptRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreatePauseOrUnpauseAttemptRequestMultiError) AllErrors() []error { return m }

// CreatePauseOrUnpauseAttemptRequestValidationError is the validation error
// returned by CreatePauseOrUnpauseAttemptRequest.Validate if the designated
// constraints aren't met.
type CreatePauseOrUnpauseAttemptRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreatePauseOrUnpauseAttemptRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreatePauseOrUnpauseAttemptRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreatePauseOrUnpauseAttemptRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreatePauseOrUnpauseAttemptRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreatePauseOrUnpauseAttemptRequestValidationError) ErrorName() string {
	return "CreatePauseOrUnpauseAttemptRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreatePauseOrUnpauseAttemptRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreatePauseOrUnpauseAttemptRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreatePauseOrUnpauseAttemptRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreatePauseOrUnpauseAttemptRequestValidationError{}

var _CreatePauseOrUnpauseAttemptRequest_Action_InLookup = map[Action]struct{}{
	5: {},
	6: {},
}

var _CreatePauseOrUnpauseAttemptRequest_Provenance_NotInLookup = map[RecurrencePaymentProvenance]struct{}{
	0: {},
}

// Validate checks the field values on CreatePauseOrUnpauseAttemptResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreatePauseOrUnpauseAttemptResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreatePauseOrUnpauseAttemptResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreatePauseOrUnpauseAttemptResponseMultiError, or nil if none found.
func (m *CreatePauseOrUnpauseAttemptResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreatePauseOrUnpauseAttemptResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreatePauseOrUnpauseAttemptResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreatePauseOrUnpauseAttemptResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreatePauseOrUnpauseAttemptResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TxnId

	// no validation rules for IsAuthenticationRequired

	// no validation rules for CredBlockType

	for idx, item := range m.GetTransactionAttributes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreatePauseOrUnpauseAttemptResponseValidationError{
						field:  fmt.Sprintf("TransactionAttributes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreatePauseOrUnpauseAttemptResponseValidationError{
						field:  fmt.Sprintf("TransactionAttributes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreatePauseOrUnpauseAttemptResponseValidationError{
					field:  fmt.Sprintf("TransactionAttributes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CreatePauseOrUnpauseAttemptResponseMultiError(errors)
	}

	return nil
}

// CreatePauseOrUnpauseAttemptResponseMultiError is an error wrapping multiple
// validation errors returned by
// CreatePauseOrUnpauseAttemptResponse.ValidateAll() if the designated
// constraints aren't met.
type CreatePauseOrUnpauseAttemptResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreatePauseOrUnpauseAttemptResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreatePauseOrUnpauseAttemptResponseMultiError) AllErrors() []error { return m }

// CreatePauseOrUnpauseAttemptResponseValidationError is the validation error
// returned by CreatePauseOrUnpauseAttemptResponse.Validate if the designated
// constraints aren't met.
type CreatePauseOrUnpauseAttemptResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreatePauseOrUnpauseAttemptResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreatePauseOrUnpauseAttemptResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreatePauseOrUnpauseAttemptResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreatePauseOrUnpauseAttemptResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreatePauseOrUnpauseAttemptResponseValidationError) ErrorName() string {
	return "CreatePauseOrUnpauseAttemptResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreatePauseOrUnpauseAttemptResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreatePauseOrUnpauseAttemptResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreatePauseOrUnpauseAttemptResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreatePauseOrUnpauseAttemptResponseValidationError{}

// Validate checks the field values on AuthoriseRecurringPaymentActionRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *AuthoriseRecurringPaymentActionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// AuthoriseRecurringPaymentActionRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// AuthoriseRecurringPaymentActionRequestMultiError, or nil if none found.
func (m *AuthoriseRecurringPaymentActionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AuthoriseRecurringPaymentActionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RecurringPaymentId

	if all {
		switch v := interface{}(m.GetCredential()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthoriseRecurringPaymentActionRequestValidationError{
					field:  "Credential",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthoriseRecurringPaymentActionRequestValidationError{
					field:  "Credential",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCredential()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthoriseRecurringPaymentActionRequestValidationError{
				field:  "Credential",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClientRequestId

	// no validation rules for CurrentActorId

	// no validation rules for Action

	if all {
		switch v := interface{}(m.GetClientId()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthoriseRecurringPaymentActionRequestValidationError{
					field:  "ClientId",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthoriseRecurringPaymentActionRequestValidationError{
					field:  "ClientId",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClientId()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthoriseRecurringPaymentActionRequestValidationError{
				field:  "ClientId",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AuthoriseRecurringPaymentActionRequestMultiError(errors)
	}

	return nil
}

// AuthoriseRecurringPaymentActionRequestMultiError is an error wrapping
// multiple validation errors returned by
// AuthoriseRecurringPaymentActionRequest.ValidateAll() if the designated
// constraints aren't met.
type AuthoriseRecurringPaymentActionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuthoriseRecurringPaymentActionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuthoriseRecurringPaymentActionRequestMultiError) AllErrors() []error { return m }

// AuthoriseRecurringPaymentActionRequestValidationError is the validation
// error returned by AuthoriseRecurringPaymentActionRequest.Validate if the
// designated constraints aren't met.
type AuthoriseRecurringPaymentActionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuthoriseRecurringPaymentActionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuthoriseRecurringPaymentActionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuthoriseRecurringPaymentActionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuthoriseRecurringPaymentActionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuthoriseRecurringPaymentActionRequestValidationError) ErrorName() string {
	return "AuthoriseRecurringPaymentActionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AuthoriseRecurringPaymentActionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuthoriseRecurringPaymentActionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuthoriseRecurringPaymentActionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuthoriseRecurringPaymentActionRequestValidationError{}

// Validate checks the field values on AuthoriseRecurringPaymentActionResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *AuthoriseRecurringPaymentActionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// AuthoriseRecurringPaymentActionResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// AuthoriseRecurringPaymentActionResponseMultiError, or nil if none found.
func (m *AuthoriseRecurringPaymentActionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AuthoriseRecurringPaymentActionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthoriseRecurringPaymentActionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthoriseRecurringPaymentActionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthoriseRecurringPaymentActionResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetActionDetailedStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthoriseRecurringPaymentActionResponseValidationError{
					field:  "ActionDetailedStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthoriseRecurringPaymentActionResponseValidationError{
					field:  "ActionDetailedStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActionDetailedStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthoriseRecurringPaymentActionResponseValidationError{
				field:  "ActionDetailedStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AuthoriseRecurringPaymentActionResponseMultiError(errors)
	}

	return nil
}

// AuthoriseRecurringPaymentActionResponseMultiError is an error wrapping
// multiple validation errors returned by
// AuthoriseRecurringPaymentActionResponse.ValidateAll() if the designated
// constraints aren't met.
type AuthoriseRecurringPaymentActionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuthoriseRecurringPaymentActionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuthoriseRecurringPaymentActionResponseMultiError) AllErrors() []error { return m }

// AuthoriseRecurringPaymentActionResponseValidationError is the validation
// error returned by AuthoriseRecurringPaymentActionResponse.Validate if the
// designated constraints aren't met.
type AuthoriseRecurringPaymentActionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuthoriseRecurringPaymentActionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuthoriseRecurringPaymentActionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuthoriseRecurringPaymentActionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuthoriseRecurringPaymentActionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuthoriseRecurringPaymentActionResponseValidationError) ErrorName() string {
	return "AuthoriseRecurringPaymentActionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AuthoriseRecurringPaymentActionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuthoriseRecurringPaymentActionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuthoriseRecurringPaymentActionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuthoriseRecurringPaymentActionResponseValidationError{}

// Validate checks the field values on
// GetRecurringPaymentDetailsByClientReqIdRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetRecurringPaymentDetailsByClientReqIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetRecurringPaymentDetailsByClientReqIdRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetRecurringPaymentDetailsByClientReqIdRequestMultiError, or nil if none found.
func (m *GetRecurringPaymentDetailsByClientReqIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRecurringPaymentDetailsByClientReqIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ClientReqId

	if len(errors) > 0 {
		return GetRecurringPaymentDetailsByClientReqIdRequestMultiError(errors)
	}

	return nil
}

// GetRecurringPaymentDetailsByClientReqIdRequestMultiError is an error
// wrapping multiple validation errors returned by
// GetRecurringPaymentDetailsByClientReqIdRequest.ValidateAll() if the
// designated constraints aren't met.
type GetRecurringPaymentDetailsByClientReqIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRecurringPaymentDetailsByClientReqIdRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRecurringPaymentDetailsByClientReqIdRequestMultiError) AllErrors() []error { return m }

// GetRecurringPaymentDetailsByClientReqIdRequestValidationError is the
// validation error returned by
// GetRecurringPaymentDetailsByClientReqIdRequest.Validate if the designated
// constraints aren't met.
type GetRecurringPaymentDetailsByClientReqIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRecurringPaymentDetailsByClientReqIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRecurringPaymentDetailsByClientReqIdRequestValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetRecurringPaymentDetailsByClientReqIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRecurringPaymentDetailsByClientReqIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRecurringPaymentDetailsByClientReqIdRequestValidationError) ErrorName() string {
	return "GetRecurringPaymentDetailsByClientReqIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRecurringPaymentDetailsByClientReqIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRecurringPaymentDetailsByClientReqIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRecurringPaymentDetailsByClientReqIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRecurringPaymentDetailsByClientReqIdRequestValidationError{}

// Validate checks the field values on
// GetRecurringPaymentDetailsByClientReqIdResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetRecurringPaymentDetailsByClientReqIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetRecurringPaymentDetailsByClientReqIdResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetRecurringPaymentDetailsByClientReqIdResponseMultiError, or nil if none found.
func (m *GetRecurringPaymentDetailsByClientReqIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRecurringPaymentDetailsByClientReqIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRecurringPaymentDetailsByClientReqIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRecurringPaymentDetailsByClientReqIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRecurringPaymentDetailsByClientReqIdResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RecurringPaymentId

	if len(errors) > 0 {
		return GetRecurringPaymentDetailsByClientReqIdResponseMultiError(errors)
	}

	return nil
}

// GetRecurringPaymentDetailsByClientReqIdResponseMultiError is an error
// wrapping multiple validation errors returned by
// GetRecurringPaymentDetailsByClientReqIdResponse.ValidateAll() if the
// designated constraints aren't met.
type GetRecurringPaymentDetailsByClientReqIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRecurringPaymentDetailsByClientReqIdResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRecurringPaymentDetailsByClientReqIdResponseMultiError) AllErrors() []error { return m }

// GetRecurringPaymentDetailsByClientReqIdResponseValidationError is the
// validation error returned by
// GetRecurringPaymentDetailsByClientReqIdResponse.Validate if the designated
// constraints aren't met.
type GetRecurringPaymentDetailsByClientReqIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRecurringPaymentDetailsByClientReqIdResponseValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetRecurringPaymentDetailsByClientReqIdResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetRecurringPaymentDetailsByClientReqIdResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRecurringPaymentDetailsByClientReqIdResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRecurringPaymentDetailsByClientReqIdResponseValidationError) ErrorName() string {
	return "GetRecurringPaymentDetailsByClientReqIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRecurringPaymentDetailsByClientReqIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRecurringPaymentDetailsByClientReqIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRecurringPaymentDetailsByClientReqIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRecurringPaymentDetailsByClientReqIdResponseValidationError{}

// Validate checks the field values on CreateRecurringPaymentV1Request with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateRecurringPaymentV1Request) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateRecurringPaymentV1Request with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateRecurringPaymentV1RequestMultiError, or nil if none found.
func (m *CreateRecurringPaymentV1Request) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRecurringPaymentV1Request) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetClientRequestId()); l < 4 || l > 100 {
		err := CreateRecurringPaymentV1RequestValidationError{
			field:  "ClientRequestId",
			reason: "value length must be between 4 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetCurrentActorId()) < 1 {
		err := CreateRecurringPaymentV1RequestValidationError{
			field:  "CurrentActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetRecurringPaymentDetails() == nil {
		err := CreateRecurringPaymentV1RequestValidationError{
			field:  "RecurringPaymentDetails",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetRecurringPaymentDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRecurringPaymentV1RequestValidationError{
					field:  "RecurringPaymentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRecurringPaymentV1RequestValidationError{
					field:  "RecurringPaymentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRecurringPaymentDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRecurringPaymentV1RequestValidationError{
				field:  "RecurringPaymentDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExpiry()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRecurringPaymentV1RequestValidationError{
					field:  "Expiry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRecurringPaymentV1RequestValidationError{
					field:  "Expiry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpiry()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRecurringPaymentV1RequestValidationError{
				field:  "Expiry",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRecurringPaymentTypeSpecificPayload()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRecurringPaymentV1RequestValidationError{
					field:  "RecurringPaymentTypeSpecificPayload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRecurringPaymentV1RequestValidationError{
					field:  "RecurringPaymentTypeSpecificPayload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRecurringPaymentTypeSpecificPayload()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRecurringPaymentV1RequestValidationError{
				field:  "RecurringPaymentTypeSpecificPayload",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for HardPaymentRoute

	if all {
		switch v := interface{}(m.GetPostCreationDomainDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRecurringPaymentV1RequestValidationError{
					field:  "PostCreationDomainDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRecurringPaymentV1RequestValidationError{
					field:  "PostCreationDomainDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPostCreationDomainDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRecurringPaymentV1RequestValidationError{
				field:  "PostCreationDomainDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateRecurringPaymentV1RequestMultiError(errors)
	}

	return nil
}

// CreateRecurringPaymentV1RequestMultiError is an error wrapping multiple
// validation errors returned by CreateRecurringPaymentV1Request.ValidateAll()
// if the designated constraints aren't met.
type CreateRecurringPaymentV1RequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRecurringPaymentV1RequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRecurringPaymentV1RequestMultiError) AllErrors() []error { return m }

// CreateRecurringPaymentV1RequestValidationError is the validation error
// returned by CreateRecurringPaymentV1Request.Validate if the designated
// constraints aren't met.
type CreateRecurringPaymentV1RequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRecurringPaymentV1RequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRecurringPaymentV1RequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRecurringPaymentV1RequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRecurringPaymentV1RequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRecurringPaymentV1RequestValidationError) ErrorName() string {
	return "CreateRecurringPaymentV1RequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateRecurringPaymentV1RequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRecurringPaymentV1Request.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRecurringPaymentV1RequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRecurringPaymentV1RequestValidationError{}

// Validate checks the field values on CreateRecurringPaymentV1Response with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateRecurringPaymentV1Response) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateRecurringPaymentV1Response with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateRecurringPaymentV1ResponseMultiError, or nil if none found.
func (m *CreateRecurringPaymentV1Response) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRecurringPaymentV1Response) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRecurringPaymentV1ResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRecurringPaymentV1ResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRecurringPaymentV1ResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RecurringPaymentId

	if all {
		switch v := interface{}(m.GetNextActionDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRecurringPaymentV1ResponseValidationError{
					field:  "NextActionDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRecurringPaymentV1ResponseValidationError{
					field:  "NextActionDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextActionDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRecurringPaymentV1ResponseValidationError{
				field:  "NextActionDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateRecurringPaymentV1ResponseMultiError(errors)
	}

	return nil
}

// CreateRecurringPaymentV1ResponseMultiError is an error wrapping multiple
// validation errors returned by
// CreateRecurringPaymentV1Response.ValidateAll() if the designated
// constraints aren't met.
type CreateRecurringPaymentV1ResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRecurringPaymentV1ResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRecurringPaymentV1ResponseMultiError) AllErrors() []error { return m }

// CreateRecurringPaymentV1ResponseValidationError is the validation error
// returned by CreateRecurringPaymentV1Response.Validate if the designated
// constraints aren't met.
type CreateRecurringPaymentV1ResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRecurringPaymentV1ResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRecurringPaymentV1ResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRecurringPaymentV1ResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRecurringPaymentV1ResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRecurringPaymentV1ResponseValidationError) ErrorName() string {
	return "CreateRecurringPaymentV1ResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateRecurringPaymentV1ResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRecurringPaymentV1Response.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRecurringPaymentV1ResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRecurringPaymentV1ResponseValidationError{}

// Validate checks the field values on RecurringPaymentCreationDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RecurringPaymentCreationDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecurringPaymentCreationDetails with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// RecurringPaymentCreationDetailsMultiError, or nil if none found.
func (m *RecurringPaymentCreationDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *RecurringPaymentCreationDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetFromActorId()) < 1 {
		err := RecurringPaymentCreationDetailsValidationError{
			field:  "FromActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetToActorId()) < 1 {
		err := RecurringPaymentCreationDetailsValidationError{
			field:  "ToActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _RecurringPaymentCreationDetails_Type_NotInLookup[m.GetType()]; ok {
		err := RecurringPaymentCreationDetailsValidationError{
			field:  "Type",
			reason: "value must not be in list [RECURRING_PAYMENT_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PiFrom

	if utf8.RuneCountInString(m.GetPiTo()) < 1 {
		err := RecurringPaymentCreationDetailsValidationError{
			field:  "PiTo",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetAmount() == nil {
		err := RecurringPaymentCreationDetailsValidationError{
			field:  "Amount",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecurringPaymentCreationDetailsValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecurringPaymentCreationDetailsValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecurringPaymentCreationDetailsValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetInterval() == nil {
		err := RecurringPaymentCreationDetailsValidationError{
			field:  "Interval",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetInterval()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecurringPaymentCreationDetailsValidationError{
					field:  "Interval",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecurringPaymentCreationDetailsValidationError{
					field:  "Interval",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInterval()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecurringPaymentCreationDetailsValidationError{
				field:  "Interval",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetRecurrenceRule() == nil {
		err := RecurringPaymentCreationDetailsValidationError{
			field:  "RecurrenceRule",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetRecurrenceRule()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecurringPaymentCreationDetailsValidationError{
					field:  "RecurrenceRule",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecurringPaymentCreationDetailsValidationError{
					field:  "RecurrenceRule",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRecurrenceRule()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecurringPaymentCreationDetailsValidationError{
				field:  "RecurrenceRule",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MaximumAllowedTxns

	// no validation rules for PartnerBank

	if _, ok := _RecurringPaymentCreationDetails_Ownership_NotInLookup[m.GetOwnership()]; ok {
		err := RecurringPaymentCreationDetailsValidationError{
			field:  "Ownership",
			reason: "value must not be in list [RECURRING_PAYMENT_OWNERSHIP_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _RecurringPaymentCreationDetails_Provenance_NotInLookup[m.GetProvenance()]; ok {
		err := RecurringPaymentCreationDetailsValidationError{
			field:  "Provenance",
			reason: "value must not be in list [RECURRENCE_PAYMENT_PROVENANCE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _RecurringPaymentCreationDetails_UiEntryPoint_NotInLookup[m.GetUiEntryPoint()]; ok {
		err := RecurringPaymentCreationDetailsValidationError{
			field:  "UiEntryPoint",
			reason: "value must not be in list [UI_ENTRY_POINT_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _RecurringPaymentCreationDetails_AmountType_NotInLookup[m.GetAmountType()]; ok {
		err := RecurringPaymentCreationDetailsValidationError{
			field:  "AmountType",
			reason: "value must not be in list [AMOUNT_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ShareToPayee

	if len(errors) > 0 {
		return RecurringPaymentCreationDetailsMultiError(errors)
	}

	return nil
}

// RecurringPaymentCreationDetailsMultiError is an error wrapping multiple
// validation errors returned by RecurringPaymentCreationDetails.ValidateAll()
// if the designated constraints aren't met.
type RecurringPaymentCreationDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecurringPaymentCreationDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecurringPaymentCreationDetailsMultiError) AllErrors() []error { return m }

// RecurringPaymentCreationDetailsValidationError is the validation error
// returned by RecurringPaymentCreationDetails.Validate if the designated
// constraints aren't met.
type RecurringPaymentCreationDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecurringPaymentCreationDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecurringPaymentCreationDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecurringPaymentCreationDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecurringPaymentCreationDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecurringPaymentCreationDetailsValidationError) ErrorName() string {
	return "RecurringPaymentCreationDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e RecurringPaymentCreationDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecurringPaymentCreationDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecurringPaymentCreationDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecurringPaymentCreationDetailsValidationError{}

var _RecurringPaymentCreationDetails_Type_NotInLookup = map[RecurringPaymentType]struct{}{
	0: {},
}

var _RecurringPaymentCreationDetails_Ownership_NotInLookup = map[RecurringPaymentOwnership]struct{}{
	0: {},
}

var _RecurringPaymentCreationDetails_Provenance_NotInLookup = map[RecurrencePaymentProvenance]struct{}{
	0: {},
}

var _RecurringPaymentCreationDetails_UiEntryPoint_NotInLookup = map[UIEntryPoint]struct{}{
	0: {},
}

var _RecurringPaymentCreationDetails_AmountType_NotInLookup = map[AmountType]struct{}{
	0: {},
}

// Validate checks the field values on GetRecurringPaymentEssentialsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetRecurringPaymentEssentialsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRecurringPaymentEssentialsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetRecurringPaymentEssentialsRequestMultiError, or nil if none found.
func (m *GetRecurringPaymentEssentialsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRecurringPaymentEssentialsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetRecurringPaymentEssentialsRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _GetRecurringPaymentEssentialsRequest_Vendor_NotInLookup[m.GetVendor()]; ok {
		err := GetRecurringPaymentEssentialsRequestValidationError{
			field:  "Vendor",
			reason: "value must not be in list [VENDOR_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	switch v := m.Identifier.(type) {
	case *GetRecurringPaymentEssentialsRequest_EnachIdentifier:
		if v == nil {
			err := GetRecurringPaymentEssentialsRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetEnachIdentifier()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRecurringPaymentEssentialsRequestValidationError{
						field:  "EnachIdentifier",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRecurringPaymentEssentialsRequestValidationError{
						field:  "EnachIdentifier",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetEnachIdentifier()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRecurringPaymentEssentialsRequestValidationError{
					field:  "EnachIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetRecurringPaymentEssentialsRequestMultiError(errors)
	}

	return nil
}

// GetRecurringPaymentEssentialsRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetRecurringPaymentEssentialsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetRecurringPaymentEssentialsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRecurringPaymentEssentialsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRecurringPaymentEssentialsRequestMultiError) AllErrors() []error { return m }

// GetRecurringPaymentEssentialsRequestValidationError is the validation error
// returned by GetRecurringPaymentEssentialsRequest.Validate if the designated
// constraints aren't met.
type GetRecurringPaymentEssentialsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRecurringPaymentEssentialsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRecurringPaymentEssentialsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRecurringPaymentEssentialsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRecurringPaymentEssentialsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRecurringPaymentEssentialsRequestValidationError) ErrorName() string {
	return "GetRecurringPaymentEssentialsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRecurringPaymentEssentialsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRecurringPaymentEssentialsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRecurringPaymentEssentialsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRecurringPaymentEssentialsRequestValidationError{}

var _GetRecurringPaymentEssentialsRequest_Vendor_NotInLookup = map[vendorgateway.Vendor]struct{}{
	0: {},
}

// Validate checks the field values on GetRecurringPaymentEssentialsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetRecurringPaymentEssentialsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRecurringPaymentEssentialsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetRecurringPaymentEssentialsResponseMultiError, or nil if none found.
func (m *GetRecurringPaymentEssentialsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRecurringPaymentEssentialsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRecurringPaymentEssentialsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRecurringPaymentEssentialsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRecurringPaymentEssentialsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRecurringPaymentEssentials()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRecurringPaymentEssentialsResponseValidationError{
					field:  "RecurringPaymentEssentials",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRecurringPaymentEssentialsResponseValidationError{
					field:  "RecurringPaymentEssentials",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRecurringPaymentEssentials()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRecurringPaymentEssentialsResponseValidationError{
				field:  "RecurringPaymentEssentials",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.RecurringPaymentTypeSpecificData.(type) {
	case *GetRecurringPaymentEssentialsResponse_EnachData_:
		if v == nil {
			err := GetRecurringPaymentEssentialsResponseValidationError{
				field:  "RecurringPaymentTypeSpecificData",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetEnachData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRecurringPaymentEssentialsResponseValidationError{
						field:  "EnachData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRecurringPaymentEssentialsResponseValidationError{
						field:  "EnachData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetEnachData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRecurringPaymentEssentialsResponseValidationError{
					field:  "EnachData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetRecurringPaymentEssentialsResponseMultiError(errors)
	}

	return nil
}

// GetRecurringPaymentEssentialsResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetRecurringPaymentEssentialsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetRecurringPaymentEssentialsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRecurringPaymentEssentialsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRecurringPaymentEssentialsResponseMultiError) AllErrors() []error { return m }

// GetRecurringPaymentEssentialsResponseValidationError is the validation error
// returned by GetRecurringPaymentEssentialsResponse.Validate if the
// designated constraints aren't met.
type GetRecurringPaymentEssentialsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRecurringPaymentEssentialsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRecurringPaymentEssentialsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRecurringPaymentEssentialsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRecurringPaymentEssentialsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRecurringPaymentEssentialsResponseValidationError) ErrorName() string {
	return "GetRecurringPaymentEssentialsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRecurringPaymentEssentialsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRecurringPaymentEssentialsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRecurringPaymentEssentialsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRecurringPaymentEssentialsResponseValidationError{}

// Validate checks the field values on GetActionStatusV1Request with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetActionStatusV1Request) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetActionStatusV1Request with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetActionStatusV1RequestMultiError, or nil if none found.
func (m *GetActionStatusV1Request) ValidateAll() error {
	return m.validate(true)
}

func (m *GetActionStatusV1Request) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActionType

	// no validation rules for ClientRequestId

	// no validation rules for PollAttempt

	if len(errors) > 0 {
		return GetActionStatusV1RequestMultiError(errors)
	}

	return nil
}

// GetActionStatusV1RequestMultiError is an error wrapping multiple validation
// errors returned by GetActionStatusV1Request.ValidateAll() if the designated
// constraints aren't met.
type GetActionStatusV1RequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetActionStatusV1RequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetActionStatusV1RequestMultiError) AllErrors() []error { return m }

// GetActionStatusV1RequestValidationError is the validation error returned by
// GetActionStatusV1Request.Validate if the designated constraints aren't met.
type GetActionStatusV1RequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetActionStatusV1RequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetActionStatusV1RequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetActionStatusV1RequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetActionStatusV1RequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetActionStatusV1RequestValidationError) ErrorName() string {
	return "GetActionStatusV1RequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetActionStatusV1RequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetActionStatusV1Request.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetActionStatusV1RequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetActionStatusV1RequestValidationError{}

// Validate checks the field values on GetActionStatusV1Response with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetActionStatusV1Response) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetActionStatusV1Response with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetActionStatusV1ResponseMultiError, or nil if none found.
func (m *GetActionStatusV1Response) ValidateAll() error {
	return m.validate(true)
}

func (m *GetActionStatusV1Response) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActionStatusV1ResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActionStatusV1ResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActionStatusV1ResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActionStatus

	// no validation rules for ActionSubStatus

	if all {
		switch v := interface{}(m.GetNextStepDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActionStatusV1ResponseValidationError{
					field:  "NextStepDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActionStatusV1ResponseValidationError{
					field:  "NextStepDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextStepDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActionStatusV1ResponseValidationError{
				field:  "NextStepDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetActionDetailedStatusInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActionStatusV1ResponseValidationError{
					field:  "ActionDetailedStatusInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActionStatusV1ResponseValidationError{
					field:  "ActionDetailedStatusInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActionDetailedStatusInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActionStatusV1ResponseValidationError{
				field:  "ActionDetailedStatusInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetActionStatusV1ResponseMultiError(errors)
	}

	return nil
}

// GetActionStatusV1ResponseMultiError is an error wrapping multiple validation
// errors returned by GetActionStatusV1Response.ValidateAll() if the
// designated constraints aren't met.
type GetActionStatusV1ResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetActionStatusV1ResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetActionStatusV1ResponseMultiError) AllErrors() []error { return m }

// GetActionStatusV1ResponseValidationError is the validation error returned by
// GetActionStatusV1Response.Validate if the designated constraints aren't met.
type GetActionStatusV1ResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetActionStatusV1ResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetActionStatusV1ResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetActionStatusV1ResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetActionStatusV1ResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetActionStatusV1ResponseValidationError) ErrorName() string {
	return "GetActionStatusV1ResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetActionStatusV1ResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetActionStatusV1Response.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetActionStatusV1ResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetActionStatusV1ResponseValidationError{}

// Validate checks the field values on InitiateCreationAuthorizationV1Request
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *InitiateCreationAuthorizationV1Request) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// InitiateCreationAuthorizationV1Request with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// InitiateCreationAuthorizationV1RequestMultiError, or nil if none found.
func (m *InitiateCreationAuthorizationV1Request) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateCreationAuthorizationV1Request) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ClientRequestId

	if len(errors) > 0 {
		return InitiateCreationAuthorizationV1RequestMultiError(errors)
	}

	return nil
}

// InitiateCreationAuthorizationV1RequestMultiError is an error wrapping
// multiple validation errors returned by
// InitiateCreationAuthorizationV1Request.ValidateAll() if the designated
// constraints aren't met.
type InitiateCreationAuthorizationV1RequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateCreationAuthorizationV1RequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateCreationAuthorizationV1RequestMultiError) AllErrors() []error { return m }

// InitiateCreationAuthorizationV1RequestValidationError is the validation
// error returned by InitiateCreationAuthorizationV1Request.Validate if the
// designated constraints aren't met.
type InitiateCreationAuthorizationV1RequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateCreationAuthorizationV1RequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateCreationAuthorizationV1RequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateCreationAuthorizationV1RequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateCreationAuthorizationV1RequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateCreationAuthorizationV1RequestValidationError) ErrorName() string {
	return "InitiateCreationAuthorizationV1RequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateCreationAuthorizationV1RequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateCreationAuthorizationV1Request.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateCreationAuthorizationV1RequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateCreationAuthorizationV1RequestValidationError{}

// Validate checks the field values on InitiateCreationAuthorizationV1Response
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *InitiateCreationAuthorizationV1Response) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// InitiateCreationAuthorizationV1Response with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// InitiateCreationAuthorizationV1ResponseMultiError, or nil if none found.
func (m *InitiateCreationAuthorizationV1Response) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateCreationAuthorizationV1Response) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateCreationAuthorizationV1ResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateCreationAuthorizationV1ResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateCreationAuthorizationV1ResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAuthDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateCreationAuthorizationV1ResponseValidationError{
					field:  "AuthDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateCreationAuthorizationV1ResponseValidationError{
					field:  "AuthDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuthDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateCreationAuthorizationV1ResponseValidationError{
				field:  "AuthDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPostAuthRedirection()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateCreationAuthorizationV1ResponseValidationError{
					field:  "PostAuthRedirection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateCreationAuthorizationV1ResponseValidationError{
					field:  "PostAuthRedirection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPostAuthRedirection()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateCreationAuthorizationV1ResponseValidationError{
				field:  "PostAuthRedirection",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InitiateCreationAuthorizationV1ResponseMultiError(errors)
	}

	return nil
}

// InitiateCreationAuthorizationV1ResponseMultiError is an error wrapping
// multiple validation errors returned by
// InitiateCreationAuthorizationV1Response.ValidateAll() if the designated
// constraints aren't met.
type InitiateCreationAuthorizationV1ResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateCreationAuthorizationV1ResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateCreationAuthorizationV1ResponseMultiError) AllErrors() []error { return m }

// InitiateCreationAuthorizationV1ResponseValidationError is the validation
// error returned by InitiateCreationAuthorizationV1Response.Validate if the
// designated constraints aren't met.
type InitiateCreationAuthorizationV1ResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateCreationAuthorizationV1ResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateCreationAuthorizationV1ResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateCreationAuthorizationV1ResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateCreationAuthorizationV1ResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateCreationAuthorizationV1ResponseValidationError) ErrorName() string {
	return "InitiateCreationAuthorizationV1ResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateCreationAuthorizationV1ResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateCreationAuthorizationV1Response.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateCreationAuthorizationV1ResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateCreationAuthorizationV1ResponseValidationError{}

// Validate checks the field values on AuthorizeCreationV1Request with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AuthorizeCreationV1Request) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AuthorizeCreationV1Request with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AuthorizeCreationV1RequestMultiError, or nil if none found.
func (m *AuthorizeCreationV1Request) ValidateAll() error {
	return m.validate(true)
}

func (m *AuthorizeCreationV1Request) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RecurringPaymentId

	if all {
		switch v := interface{}(m.GetAuthCredential()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthorizeCreationV1RequestValidationError{
					field:  "AuthCredential",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthorizeCreationV1RequestValidationError{
					field:  "AuthCredential",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuthCredential()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthorizeCreationV1RequestValidationError{
				field:  "AuthCredential",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAuthMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthorizeCreationV1RequestValidationError{
					field:  "AuthMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthorizeCreationV1RequestValidationError{
					field:  "AuthMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuthMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthorizeCreationV1RequestValidationError{
				field:  "AuthMetadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AuthorizeCreationV1RequestMultiError(errors)
	}

	return nil
}

// AuthorizeCreationV1RequestMultiError is an error wrapping multiple
// validation errors returned by AuthorizeCreationV1Request.ValidateAll() if
// the designated constraints aren't met.
type AuthorizeCreationV1RequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuthorizeCreationV1RequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuthorizeCreationV1RequestMultiError) AllErrors() []error { return m }

// AuthorizeCreationV1RequestValidationError is the validation error returned
// by AuthorizeCreationV1Request.Validate if the designated constraints aren't met.
type AuthorizeCreationV1RequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuthorizeCreationV1RequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuthorizeCreationV1RequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuthorizeCreationV1RequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuthorizeCreationV1RequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuthorizeCreationV1RequestValidationError) ErrorName() string {
	return "AuthorizeCreationV1RequestValidationError"
}

// Error satisfies the builtin error interface
func (e AuthorizeCreationV1RequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuthorizeCreationV1Request.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuthorizeCreationV1RequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuthorizeCreationV1RequestValidationError{}

// Validate checks the field values on AuthorizeCreationV1Response with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AuthorizeCreationV1Response) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AuthorizeCreationV1Response with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AuthorizeCreationV1ResponseMultiError, or nil if none found.
func (m *AuthorizeCreationV1Response) ValidateAll() error {
	return m.validate(true)
}

func (m *AuthorizeCreationV1Response) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthorizeCreationV1ResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthorizeCreationV1ResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthorizeCreationV1ResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AuthorizeCreationV1ResponseMultiError(errors)
	}

	return nil
}

// AuthorizeCreationV1ResponseMultiError is an error wrapping multiple
// validation errors returned by AuthorizeCreationV1Response.ValidateAll() if
// the designated constraints aren't met.
type AuthorizeCreationV1ResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuthorizeCreationV1ResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuthorizeCreationV1ResponseMultiError) AllErrors() []error { return m }

// AuthorizeCreationV1ResponseValidationError is the validation error returned
// by AuthorizeCreationV1Response.Validate if the designated constraints
// aren't met.
type AuthorizeCreationV1ResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuthorizeCreationV1ResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuthorizeCreationV1ResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuthorizeCreationV1ResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuthorizeCreationV1ResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuthorizeCreationV1ResponseValidationError) ErrorName() string {
	return "AuthorizeCreationV1ResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AuthorizeCreationV1ResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuthorizeCreationV1Response.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuthorizeCreationV1ResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuthorizeCreationV1ResponseValidationError{}

// Validate checks the field values on
// GetRecurringPaymentIdByVendorRequestIdRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetRecurringPaymentIdByVendorRequestIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetRecurringPaymentIdByVendorRequestIdRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetRecurringPaymentIdByVendorRequestIdRequestMultiError, or nil if none found.
func (m *GetRecurringPaymentIdByVendorRequestIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRecurringPaymentIdByVendorRequestIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RecurringPaymentType

	// no validation rules for VendorRequestId

	if len(errors) > 0 {
		return GetRecurringPaymentIdByVendorRequestIdRequestMultiError(errors)
	}

	return nil
}

// GetRecurringPaymentIdByVendorRequestIdRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetRecurringPaymentIdByVendorRequestIdRequest.ValidateAll() if the
// designated constraints aren't met.
type GetRecurringPaymentIdByVendorRequestIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRecurringPaymentIdByVendorRequestIdRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRecurringPaymentIdByVendorRequestIdRequestMultiError) AllErrors() []error { return m }

// GetRecurringPaymentIdByVendorRequestIdRequestValidationError is the
// validation error returned by
// GetRecurringPaymentIdByVendorRequestIdRequest.Validate if the designated
// constraints aren't met.
type GetRecurringPaymentIdByVendorRequestIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRecurringPaymentIdByVendorRequestIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRecurringPaymentIdByVendorRequestIdRequestValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetRecurringPaymentIdByVendorRequestIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRecurringPaymentIdByVendorRequestIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRecurringPaymentIdByVendorRequestIdRequestValidationError) ErrorName() string {
	return "GetRecurringPaymentIdByVendorRequestIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRecurringPaymentIdByVendorRequestIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRecurringPaymentIdByVendorRequestIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRecurringPaymentIdByVendorRequestIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRecurringPaymentIdByVendorRequestIdRequestValidationError{}

// Validate checks the field values on
// GetRecurringPaymentIdByVendorRequestIdResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetRecurringPaymentIdByVendorRequestIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetRecurringPaymentIdByVendorRequestIdResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetRecurringPaymentIdByVendorRequestIdResponseMultiError, or nil if none found.
func (m *GetRecurringPaymentIdByVendorRequestIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRecurringPaymentIdByVendorRequestIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRecurringPaymentIdByVendorRequestIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRecurringPaymentIdByVendorRequestIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRecurringPaymentIdByVendorRequestIdResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RecurringPaymentId

	if len(errors) > 0 {
		return GetRecurringPaymentIdByVendorRequestIdResponseMultiError(errors)
	}

	return nil
}

// GetRecurringPaymentIdByVendorRequestIdResponseMultiError is an error
// wrapping multiple validation errors returned by
// GetRecurringPaymentIdByVendorRequestIdResponse.ValidateAll() if the
// designated constraints aren't met.
type GetRecurringPaymentIdByVendorRequestIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRecurringPaymentIdByVendorRequestIdResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRecurringPaymentIdByVendorRequestIdResponseMultiError) AllErrors() []error { return m }

// GetRecurringPaymentIdByVendorRequestIdResponseValidationError is the
// validation error returned by
// GetRecurringPaymentIdByVendorRequestIdResponse.Validate if the designated
// constraints aren't met.
type GetRecurringPaymentIdByVendorRequestIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRecurringPaymentIdByVendorRequestIdResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRecurringPaymentIdByVendorRequestIdResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetRecurringPaymentIdByVendorRequestIdResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRecurringPaymentIdByVendorRequestIdResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRecurringPaymentIdByVendorRequestIdResponseValidationError) ErrorName() string {
	return "GetRecurringPaymentIdByVendorRequestIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRecurringPaymentIdByVendorRequestIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRecurringPaymentIdByVendorRequestIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRecurringPaymentIdByVendorRequestIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRecurringPaymentIdByVendorRequestIdResponseValidationError{}

// Validate checks the field values on ExecuteRecurringPaymentV1Request with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ExecuteRecurringPaymentV1Request) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExecuteRecurringPaymentV1Request with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ExecuteRecurringPaymentV1RequestMultiError, or nil if none found.
func (m *ExecuteRecurringPaymentV1Request) ValidateAll() error {
	return m.validate(true)
}

func (m *ExecuteRecurringPaymentV1Request) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetRecurringPaymentId()) < 1 {
		err := ExecuteRecurringPaymentV1RequestValidationError{
			field:  "RecurringPaymentId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetClientRequestId()) < 1 {
		err := ExecuteRecurringPaymentV1RequestValidationError{
			field:  "ClientRequestId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetAmount() == nil {
		err := ExecuteRecurringPaymentV1RequestValidationError{
			field:  "Amount",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExecuteRecurringPaymentV1RequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExecuteRecurringPaymentV1RequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExecuteRecurringPaymentV1RequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRecurringPaymentTypeSpecificPayload()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExecuteRecurringPaymentV1RequestValidationError{
					field:  "RecurringPaymentTypeSpecificPayload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExecuteRecurringPaymentV1RequestValidationError{
					field:  "RecurringPaymentTypeSpecificPayload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRecurringPaymentTypeSpecificPayload()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExecuteRecurringPaymentV1RequestValidationError{
				field:  "RecurringPaymentTypeSpecificPayload",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ExecuteRecurringPaymentV1RequestMultiError(errors)
	}

	return nil
}

// ExecuteRecurringPaymentV1RequestMultiError is an error wrapping multiple
// validation errors returned by
// ExecuteRecurringPaymentV1Request.ValidateAll() if the designated
// constraints aren't met.
type ExecuteRecurringPaymentV1RequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExecuteRecurringPaymentV1RequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExecuteRecurringPaymentV1RequestMultiError) AllErrors() []error { return m }

// ExecuteRecurringPaymentV1RequestValidationError is the validation error
// returned by ExecuteRecurringPaymentV1Request.Validate if the designated
// constraints aren't met.
type ExecuteRecurringPaymentV1RequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExecuteRecurringPaymentV1RequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExecuteRecurringPaymentV1RequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExecuteRecurringPaymentV1RequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExecuteRecurringPaymentV1RequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExecuteRecurringPaymentV1RequestValidationError) ErrorName() string {
	return "ExecuteRecurringPaymentV1RequestValidationError"
}

// Error satisfies the builtin error interface
func (e ExecuteRecurringPaymentV1RequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExecuteRecurringPaymentV1Request.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExecuteRecurringPaymentV1RequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExecuteRecurringPaymentV1RequestValidationError{}

// Validate checks the field values on ExecuteRecurringPaymentV1Response with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ExecuteRecurringPaymentV1Response) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExecuteRecurringPaymentV1Response
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ExecuteRecurringPaymentV1ResponseMultiError, or nil if none found.
func (m *ExecuteRecurringPaymentV1Response) ValidateAll() error {
	return m.validate(true)
}

func (m *ExecuteRecurringPaymentV1Response) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExecuteRecurringPaymentV1ResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExecuteRecurringPaymentV1ResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExecuteRecurringPaymentV1ResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ExecuteRecurringPaymentV1ResponseMultiError(errors)
	}

	return nil
}

// ExecuteRecurringPaymentV1ResponseMultiError is an error wrapping multiple
// validation errors returned by
// ExecuteRecurringPaymentV1Response.ValidateAll() if the designated
// constraints aren't met.
type ExecuteRecurringPaymentV1ResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExecuteRecurringPaymentV1ResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExecuteRecurringPaymentV1ResponseMultiError) AllErrors() []error { return m }

// ExecuteRecurringPaymentV1ResponseValidationError is the validation error
// returned by ExecuteRecurringPaymentV1Response.Validate if the designated
// constraints aren't met.
type ExecuteRecurringPaymentV1ResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExecuteRecurringPaymentV1ResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExecuteRecurringPaymentV1ResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExecuteRecurringPaymentV1ResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExecuteRecurringPaymentV1ResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExecuteRecurringPaymentV1ResponseValidationError) ErrorName() string {
	return "ExecuteRecurringPaymentV1ResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ExecuteRecurringPaymentV1ResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExecuteRecurringPaymentV1Response.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExecuteRecurringPaymentV1ResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExecuteRecurringPaymentV1ResponseValidationError{}

// Validate checks the field values on GetRecurringPaymentsByIdsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetRecurringPaymentsByIdsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRecurringPaymentsByIdsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetRecurringPaymentsByIdsRequestMultiError, or nil if none found.
func (m *GetRecurringPaymentsByIdsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRecurringPaymentsByIdsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetRecurringPaymentsByIdsRequestMultiError(errors)
	}

	return nil
}

// GetRecurringPaymentsByIdsRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetRecurringPaymentsByIdsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetRecurringPaymentsByIdsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRecurringPaymentsByIdsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRecurringPaymentsByIdsRequestMultiError) AllErrors() []error { return m }

// GetRecurringPaymentsByIdsRequestValidationError is the validation error
// returned by GetRecurringPaymentsByIdsRequest.Validate if the designated
// constraints aren't met.
type GetRecurringPaymentsByIdsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRecurringPaymentsByIdsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRecurringPaymentsByIdsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRecurringPaymentsByIdsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRecurringPaymentsByIdsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRecurringPaymentsByIdsRequestValidationError) ErrorName() string {
	return "GetRecurringPaymentsByIdsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRecurringPaymentsByIdsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRecurringPaymentsByIdsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRecurringPaymentsByIdsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRecurringPaymentsByIdsRequestValidationError{}

// Validate checks the field values on GetRecurringPaymentsByIdsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetRecurringPaymentsByIdsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRecurringPaymentsByIdsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetRecurringPaymentsByIdsResponseMultiError, or nil if none found.
func (m *GetRecurringPaymentsByIdsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRecurringPaymentsByIdsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRecurringPaymentsByIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRecurringPaymentsByIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRecurringPaymentsByIdsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRecurringPayments() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRecurringPaymentsByIdsResponseValidationError{
						field:  fmt.Sprintf("RecurringPayments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRecurringPaymentsByIdsResponseValidationError{
						field:  fmt.Sprintf("RecurringPayments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRecurringPaymentsByIdsResponseValidationError{
					field:  fmt.Sprintf("RecurringPayments[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetRecurringPaymentsByIdsResponseMultiError(errors)
	}

	return nil
}

// GetRecurringPaymentsByIdsResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetRecurringPaymentsByIdsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetRecurringPaymentsByIdsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRecurringPaymentsByIdsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRecurringPaymentsByIdsResponseMultiError) AllErrors() []error { return m }

// GetRecurringPaymentsByIdsResponseValidationError is the validation error
// returned by GetRecurringPaymentsByIdsResponse.Validate if the designated
// constraints aren't met.
type GetRecurringPaymentsByIdsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRecurringPaymentsByIdsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRecurringPaymentsByIdsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRecurringPaymentsByIdsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRecurringPaymentsByIdsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRecurringPaymentsByIdsResponseValidationError) ErrorName() string {
	return "GetRecurringPaymentsByIdsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRecurringPaymentsByIdsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRecurringPaymentsByIdsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRecurringPaymentsByIdsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRecurringPaymentsByIdsResponseValidationError{}

// Validate checks the field values on GetUpcomingTransactionsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUpcomingTransactionsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUpcomingTransactionsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetUpcomingTransactionsRequestMultiError, or nil if none found.
func (m *GetUpcomingTransactionsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUpcomingTransactionsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetUpcomingTransactionsRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetFromTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUpcomingTransactionsRequestValidationError{
					field:  "FromTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUpcomingTransactionsRequestValidationError{
					field:  "FromTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUpcomingTransactionsRequestValidationError{
				field:  "FromTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetToTime() == nil {
		err := GetUpcomingTransactionsRequestValidationError{
			field:  "ToTime",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetSource()) < 1 {
		err := GetUpcomingTransactionsRequestValidationError{
			field:  "Source",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	_GetUpcomingTransactionsRequest_Source_Unique := make(map[enums.UpcomingTransactionSource]struct{}, len(m.GetSource()))

	for idx, item := range m.GetSource() {
		_, _ = idx, item

		if _, exists := _GetUpcomingTransactionsRequest_Source_Unique[item]; exists {
			err := GetUpcomingTransactionsRequestValidationError{
				field:  fmt.Sprintf("Source[%v]", idx),
				reason: "repeated value must contain unique items",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		} else {
			_GetUpcomingTransactionsRequest_Source_Unique[item] = struct{}{}
		}

		// no validation rules for Source[idx]
	}

	// no validation rules for AccountingEntry

	if len(errors) > 0 {
		return GetUpcomingTransactionsRequestMultiError(errors)
	}

	return nil
}

// GetUpcomingTransactionsRequestMultiError is an error wrapping multiple
// validation errors returned by GetUpcomingTransactionsRequest.ValidateAll()
// if the designated constraints aren't met.
type GetUpcomingTransactionsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUpcomingTransactionsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUpcomingTransactionsRequestMultiError) AllErrors() []error { return m }

// GetUpcomingTransactionsRequestValidationError is the validation error
// returned by GetUpcomingTransactionsRequest.Validate if the designated
// constraints aren't met.
type GetUpcomingTransactionsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUpcomingTransactionsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUpcomingTransactionsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUpcomingTransactionsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUpcomingTransactionsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUpcomingTransactionsRequestValidationError) ErrorName() string {
	return "GetUpcomingTransactionsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetUpcomingTransactionsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUpcomingTransactionsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUpcomingTransactionsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUpcomingTransactionsRequestValidationError{}

// Validate checks the field values on GetUpcomingTransactionsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUpcomingTransactionsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUpcomingTransactionsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetUpcomingTransactionsResponseMultiError, or nil if none found.
func (m *GetUpcomingTransactionsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUpcomingTransactionsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUpcomingTransactionsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUpcomingTransactionsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUpcomingTransactionsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetUpcomingTxns() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetUpcomingTransactionsResponseValidationError{
						field:  fmt.Sprintf("UpcomingTxns[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetUpcomingTransactionsResponseValidationError{
						field:  fmt.Sprintf("UpcomingTxns[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetUpcomingTransactionsResponseValidationError{
					field:  fmt.Sprintf("UpcomingTxns[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetAccountDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetUpcomingTransactionsResponseValidationError{
						field:  fmt.Sprintf("AccountDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetUpcomingTransactionsResponseValidationError{
						field:  fmt.Sprintf("AccountDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetUpcomingTransactionsResponseValidationError{
					field:  fmt.Sprintf("AccountDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetUpcomingTransactionsResponseMultiError(errors)
	}

	return nil
}

// GetUpcomingTransactionsResponseMultiError is an error wrapping multiple
// validation errors returned by GetUpcomingTransactionsResponse.ValidateAll()
// if the designated constraints aren't met.
type GetUpcomingTransactionsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUpcomingTransactionsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUpcomingTransactionsResponseMultiError) AllErrors() []error { return m }

// GetUpcomingTransactionsResponseValidationError is the validation error
// returned by GetUpcomingTransactionsResponse.Validate if the designated
// constraints aren't met.
type GetUpcomingTransactionsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUpcomingTransactionsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUpcomingTransactionsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUpcomingTransactionsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUpcomingTransactionsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUpcomingTransactionsResponseValidationError) ErrorName() string {
	return "GetUpcomingTransactionsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetUpcomingTransactionsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUpcomingTransactionsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUpcomingTransactionsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUpcomingTransactionsResponseValidationError{}

// Validate checks the field values on UpcomingTransaction with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpcomingTransaction) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpcomingTransaction with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpcomingTransactionMultiError, or nil if none found.
func (m *UpcomingTransaction) ValidateAll() error {
	return m.validate(true)
}

func (m *UpcomingTransaction) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for Source

	// no validation rules for EntityId

	// no validation rules for EntityName

	// no validation rules for EntityIconUrl

	if all {
		switch v := interface{}(m.GetMinTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpcomingTransactionValidationError{
					field:  "MinTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpcomingTransactionValidationError{
					field:  "MinTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMinTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpcomingTransactionValidationError{
				field:  "MinTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMaxTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpcomingTransactionValidationError{
					field:  "MaxTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpcomingTransactionValidationError{
					field:  "MaxTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaxTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpcomingTransactionValidationError{
				field:  "MaxTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMinAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpcomingTransactionValidationError{
					field:  "MinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpcomingTransactionValidationError{
					field:  "MinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMinAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpcomingTransactionValidationError{
				field:  "MinAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMaxAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpcomingTransactionValidationError{
					field:  "MaxAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpcomingTransactionValidationError{
					field:  "MaxAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaxAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpcomingTransactionValidationError{
				field:  "MaxAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EntryType

	if len(errors) > 0 {
		return UpcomingTransactionMultiError(errors)
	}

	return nil
}

// UpcomingTransactionMultiError is an error wrapping multiple validation
// errors returned by UpcomingTransaction.ValidateAll() if the designated
// constraints aren't met.
type UpcomingTransactionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpcomingTransactionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpcomingTransactionMultiError) AllErrors() []error { return m }

// UpcomingTransactionValidationError is the validation error returned by
// UpcomingTransaction.Validate if the designated constraints aren't met.
type UpcomingTransactionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpcomingTransactionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpcomingTransactionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpcomingTransactionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpcomingTransactionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpcomingTransactionValidationError) ErrorName() string {
	return "UpcomingTransactionValidationError"
}

// Error satisfies the builtin error interface
func (e UpcomingTransactionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpcomingTransaction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpcomingTransactionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpcomingTransactionValidationError{}

// Validate checks the field values on RevokeRecurringPaymentV1Request with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RevokeRecurringPaymentV1Request) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RevokeRecurringPaymentV1Request with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// RevokeRecurringPaymentV1RequestMultiError, or nil if none found.
func (m *RevokeRecurringPaymentV1Request) ValidateAll() error {
	return m.validate(true)
}

func (m *RevokeRecurringPaymentV1Request) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetRecurringPaymentId()) < 1 {
		err := RevokeRecurringPaymentV1RequestValidationError{
			field:  "RecurringPaymentId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetClientRequestId()) < 1 {
		err := RevokeRecurringPaymentV1RequestValidationError{
			field:  "ClientRequestId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return RevokeRecurringPaymentV1RequestMultiError(errors)
	}

	return nil
}

// RevokeRecurringPaymentV1RequestMultiError is an error wrapping multiple
// validation errors returned by RevokeRecurringPaymentV1Request.ValidateAll()
// if the designated constraints aren't met.
type RevokeRecurringPaymentV1RequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RevokeRecurringPaymentV1RequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RevokeRecurringPaymentV1RequestMultiError) AllErrors() []error { return m }

// RevokeRecurringPaymentV1RequestValidationError is the validation error
// returned by RevokeRecurringPaymentV1Request.Validate if the designated
// constraints aren't met.
type RevokeRecurringPaymentV1RequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RevokeRecurringPaymentV1RequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RevokeRecurringPaymentV1RequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RevokeRecurringPaymentV1RequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RevokeRecurringPaymentV1RequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RevokeRecurringPaymentV1RequestValidationError) ErrorName() string {
	return "RevokeRecurringPaymentV1RequestValidationError"
}

// Error satisfies the builtin error interface
func (e RevokeRecurringPaymentV1RequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRevokeRecurringPaymentV1Request.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RevokeRecurringPaymentV1RequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RevokeRecurringPaymentV1RequestValidationError{}

// Validate checks the field values on RevokeRecurringPaymentV1Response with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *RevokeRecurringPaymentV1Response) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RevokeRecurringPaymentV1Response with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// RevokeRecurringPaymentV1ResponseMultiError, or nil if none found.
func (m *RevokeRecurringPaymentV1Response) ValidateAll() error {
	return m.validate(true)
}

func (m *RevokeRecurringPaymentV1Response) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RevokeRecurringPaymentV1ResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RevokeRecurringPaymentV1ResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RevokeRecurringPaymentV1ResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RevokeRecurringPaymentV1ResponseMultiError(errors)
	}

	return nil
}

// RevokeRecurringPaymentV1ResponseMultiError is an error wrapping multiple
// validation errors returned by
// RevokeRecurringPaymentV1Response.ValidateAll() if the designated
// constraints aren't met.
type RevokeRecurringPaymentV1ResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RevokeRecurringPaymentV1ResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RevokeRecurringPaymentV1ResponseMultiError) AllErrors() []error { return m }

// RevokeRecurringPaymentV1ResponseValidationError is the validation error
// returned by RevokeRecurringPaymentV1Response.Validate if the designated
// constraints aren't met.
type RevokeRecurringPaymentV1ResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RevokeRecurringPaymentV1ResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RevokeRecurringPaymentV1ResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RevokeRecurringPaymentV1ResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RevokeRecurringPaymentV1ResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RevokeRecurringPaymentV1ResponseValidationError) ErrorName() string {
	return "RevokeRecurringPaymentV1ResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RevokeRecurringPaymentV1ResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRevokeRecurringPaymentV1Response.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RevokeRecurringPaymentV1ResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RevokeRecurringPaymentV1ResponseValidationError{}

// Validate checks the field values on
// GetRecurringPaymentsActionByVendorRequestIdRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetRecurringPaymentsActionByVendorRequestIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetRecurringPaymentsActionByVendorRequestIdRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetRecurringPaymentsActionByVendorRequestIdRequestMultiError, or nil if
// none found.
func (m *GetRecurringPaymentsActionByVendorRequestIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRecurringPaymentsActionByVendorRequestIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for VendorRequestId

	if len(errors) > 0 {
		return GetRecurringPaymentsActionByVendorRequestIdRequestMultiError(errors)
	}

	return nil
}

// GetRecurringPaymentsActionByVendorRequestIdRequestMultiError is an error
// wrapping multiple validation errors returned by
// GetRecurringPaymentsActionByVendorRequestIdRequest.ValidateAll() if the
// designated constraints aren't met.
type GetRecurringPaymentsActionByVendorRequestIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRecurringPaymentsActionByVendorRequestIdRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRecurringPaymentsActionByVendorRequestIdRequestMultiError) AllErrors() []error { return m }

// GetRecurringPaymentsActionByVendorRequestIdRequestValidationError is the
// validation error returned by
// GetRecurringPaymentsActionByVendorRequestIdRequest.Validate if the
// designated constraints aren't met.
type GetRecurringPaymentsActionByVendorRequestIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRecurringPaymentsActionByVendorRequestIdRequestValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetRecurringPaymentsActionByVendorRequestIdRequestValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetRecurringPaymentsActionByVendorRequestIdRequestValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetRecurringPaymentsActionByVendorRequestIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRecurringPaymentsActionByVendorRequestIdRequestValidationError) ErrorName() string {
	return "GetRecurringPaymentsActionByVendorRequestIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRecurringPaymentsActionByVendorRequestIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRecurringPaymentsActionByVendorRequestIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRecurringPaymentsActionByVendorRequestIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRecurringPaymentsActionByVendorRequestIdRequestValidationError{}

// Validate checks the field values on
// GetRecurringPaymentsActionByVendorRequestIdResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetRecurringPaymentsActionByVendorRequestIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetRecurringPaymentsActionByVendorRequestIdResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetRecurringPaymentsActionByVendorRequestIdResponseMultiError, or nil if
// none found.
func (m *GetRecurringPaymentsActionByVendorRequestIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRecurringPaymentsActionByVendorRequestIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRecurringPaymentsActionByVendorRequestIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRecurringPaymentsActionByVendorRequestIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRecurringPaymentsActionByVendorRequestIdResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRecurringPaymentsAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRecurringPaymentsActionByVendorRequestIdResponseValidationError{
					field:  "RecurringPaymentsAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRecurringPaymentsActionByVendorRequestIdResponseValidationError{
					field:  "RecurringPaymentsAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRecurringPaymentsAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRecurringPaymentsActionByVendorRequestIdResponseValidationError{
				field:  "RecurringPaymentsAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetRecurringPaymentsActionByVendorRequestIdResponseMultiError(errors)
	}

	return nil
}

// GetRecurringPaymentsActionByVendorRequestIdResponseMultiError is an error
// wrapping multiple validation errors returned by
// GetRecurringPaymentsActionByVendorRequestIdResponse.ValidateAll() if the
// designated constraints aren't met.
type GetRecurringPaymentsActionByVendorRequestIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRecurringPaymentsActionByVendorRequestIdResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRecurringPaymentsActionByVendorRequestIdResponseMultiError) AllErrors() []error { return m }

// GetRecurringPaymentsActionByVendorRequestIdResponseValidationError is the
// validation error returned by
// GetRecurringPaymentsActionByVendorRequestIdResponse.Validate if the
// designated constraints aren't met.
type GetRecurringPaymentsActionByVendorRequestIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRecurringPaymentsActionByVendorRequestIdResponseValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetRecurringPaymentsActionByVendorRequestIdResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetRecurringPaymentsActionByVendorRequestIdResponseValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetRecurringPaymentsActionByVendorRequestIdResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRecurringPaymentsActionByVendorRequestIdResponseValidationError) ErrorName() string {
	return "GetRecurringPaymentsActionByVendorRequestIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRecurringPaymentsActionByVendorRequestIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRecurringPaymentsActionByVendorRequestIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRecurringPaymentsActionByVendorRequestIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRecurringPaymentsActionByVendorRequestIdResponseValidationError{}

// Validate checks the field values on
// GetExecutionsForRecurringPaymentResponse_ExecutionInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetExecutionsForRecurringPaymentResponse_ExecutionInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetExecutionsForRecurringPaymentResponse_ExecutionInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetExecutionsForRecurringPaymentResponse_ExecutionInfoMultiError, or nil if
// none found.
func (m *GetExecutionsForRecurringPaymentResponse_ExecutionInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExecutionsForRecurringPaymentResponse_ExecutionInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExecutionsForRecurringPaymentResponse_ExecutionInfoValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExecutionsForRecurringPaymentResponse_ExecutionInfoValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExecutionsForRecurringPaymentResponse_ExecutionInfoValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExecutedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExecutionsForRecurringPaymentResponse_ExecutionInfoValidationError{
					field:  "ExecutedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExecutionsForRecurringPaymentResponse_ExecutionInfoValidationError{
					field:  "ExecutedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExecutedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExecutionsForRecurringPaymentResponse_ExecutionInfoValidationError{
				field:  "ExecutedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ImageUrl

	// no validation rules for PayeeName

	// no validation rules for BgColor

	// no validation rules for OrderId

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExecutionsForRecurringPaymentResponse_ExecutionInfoValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExecutionsForRecurringPaymentResponse_ExecutionInfoValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExecutionsForRecurringPaymentResponse_ExecutionInfoValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRecurringPaymentTypeSpecificExecutionInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExecutionsForRecurringPaymentResponse_ExecutionInfoValidationError{
					field:  "RecurringPaymentTypeSpecificExecutionInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExecutionsForRecurringPaymentResponse_ExecutionInfoValidationError{
					field:  "RecurringPaymentTypeSpecificExecutionInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRecurringPaymentTypeSpecificExecutionInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExecutionsForRecurringPaymentResponse_ExecutionInfoValidationError{
				field:  "RecurringPaymentTypeSpecificExecutionInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ExecutionActionState

	if len(errors) > 0 {
		return GetExecutionsForRecurringPaymentResponse_ExecutionInfoMultiError(errors)
	}

	return nil
}

// GetExecutionsForRecurringPaymentResponse_ExecutionInfoMultiError is an error
// wrapping multiple validation errors returned by
// GetExecutionsForRecurringPaymentResponse_ExecutionInfo.ValidateAll() if the
// designated constraints aren't met.
type GetExecutionsForRecurringPaymentResponse_ExecutionInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExecutionsForRecurringPaymentResponse_ExecutionInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExecutionsForRecurringPaymentResponse_ExecutionInfoMultiError) AllErrors() []error {
	return m
}

// GetExecutionsForRecurringPaymentResponse_ExecutionInfoValidationError is the
// validation error returned by
// GetExecutionsForRecurringPaymentResponse_ExecutionInfo.Validate if the
// designated constraints aren't met.
type GetExecutionsForRecurringPaymentResponse_ExecutionInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExecutionsForRecurringPaymentResponse_ExecutionInfoValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetExecutionsForRecurringPaymentResponse_ExecutionInfoValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetExecutionsForRecurringPaymentResponse_ExecutionInfoValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetExecutionsForRecurringPaymentResponse_ExecutionInfoValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e GetExecutionsForRecurringPaymentResponse_ExecutionInfoValidationError) ErrorName() string {
	return "GetExecutionsForRecurringPaymentResponse_ExecutionInfoValidationError"
}

// Error satisfies the builtin error interface
func (e GetExecutionsForRecurringPaymentResponse_ExecutionInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExecutionsForRecurringPaymentResponse_ExecutionInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExecutionsForRecurringPaymentResponse_ExecutionInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExecutionsForRecurringPaymentResponse_ExecutionInfoValidationError{}

// Validate checks the field values on
// GetRecurringPaymentsForActorResponse_RecurringPaymentTile with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRecurringPaymentsForActorResponse_RecurringPaymentTile) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetRecurringPaymentsForActorResponse_RecurringPaymentTile with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRecurringPaymentsForActorResponse_RecurringPaymentTileMultiError, or nil
// if none found.
func (m *GetRecurringPaymentsForActorResponse_RecurringPaymentTile) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRecurringPaymentsForActorResponse_RecurringPaymentTile) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Type

	// no validation rules for State

	// no validation rules for AllowedFrequency

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRecurringPaymentsForActorResponse_RecurringPaymentTileValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRecurringPaymentsForActorResponse_RecurringPaymentTileValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRecurringPaymentsForActorResponse_RecurringPaymentTileValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Name

	// no validation rules for ImageUrl

	if all {
		switch v := interface{}(m.GetCreationTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRecurringPaymentsForActorResponse_RecurringPaymentTileValidationError{
					field:  "CreationTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRecurringPaymentsForActorResponse_RecurringPaymentTileValidationError{
					field:  "CreationTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreationTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRecurringPaymentsForActorResponse_RecurringPaymentTileValidationError{
				field:  "CreationTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExpiry()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRecurringPaymentsForActorResponse_RecurringPaymentTileValidationError{
					field:  "Expiry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRecurringPaymentsForActorResponse_RecurringPaymentTileValidationError{
					field:  "Expiry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpiry()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRecurringPaymentsForActorResponse_RecurringPaymentTileValidationError{
				field:  "Expiry",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BgColor

	// no validation rules for ExternalId

	if all {
		switch v := interface{}(m.GetInterval()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRecurringPaymentsForActorResponse_RecurringPaymentTileValidationError{
					field:  "Interval",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRecurringPaymentsForActorResponse_RecurringPaymentTileValidationError{
					field:  "Interval",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInterval()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRecurringPaymentsForActorResponse_RecurringPaymentTileValidationError{
				field:  "Interval",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetRecurringPaymentsForActorResponse_RecurringPaymentTileMultiError(errors)
	}

	return nil
}

// GetRecurringPaymentsForActorResponse_RecurringPaymentTileMultiError is an
// error wrapping multiple validation errors returned by
// GetRecurringPaymentsForActorResponse_RecurringPaymentTile.ValidateAll() if
// the designated constraints aren't met.
type GetRecurringPaymentsForActorResponse_RecurringPaymentTileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRecurringPaymentsForActorResponse_RecurringPaymentTileMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRecurringPaymentsForActorResponse_RecurringPaymentTileMultiError) AllErrors() []error {
	return m
}

// GetRecurringPaymentsForActorResponse_RecurringPaymentTileValidationError is
// the validation error returned by
// GetRecurringPaymentsForActorResponse_RecurringPaymentTile.Validate if the
// designated constraints aren't met.
type GetRecurringPaymentsForActorResponse_RecurringPaymentTileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRecurringPaymentsForActorResponse_RecurringPaymentTileValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetRecurringPaymentsForActorResponse_RecurringPaymentTileValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetRecurringPaymentsForActorResponse_RecurringPaymentTileValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetRecurringPaymentsForActorResponse_RecurringPaymentTileValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e GetRecurringPaymentsForActorResponse_RecurringPaymentTileValidationError) ErrorName() string {
	return "GetRecurringPaymentsForActorResponse_RecurringPaymentTileValidationError"
}

// Error satisfies the builtin error interface
func (e GetRecurringPaymentsForActorResponse_RecurringPaymentTileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRecurringPaymentsForActorResponse_RecurringPaymentTile.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRecurringPaymentsForActorResponse_RecurringPaymentTileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRecurringPaymentsForActorResponse_RecurringPaymentTileValidationError{}

// Validate checks the field values on
// GetRecurringPaymentEssentialsRequest_Enach with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetRecurringPaymentEssentialsRequest_Enach) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetRecurringPaymentEssentialsRequest_Enach with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetRecurringPaymentEssentialsRequest_EnachMultiError, or nil if none found.
func (m *GetRecurringPaymentEssentialsRequest_Enach) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRecurringPaymentEssentialsRequest_Enach) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Umrn

	if len(errors) > 0 {
		return GetRecurringPaymentEssentialsRequest_EnachMultiError(errors)
	}

	return nil
}

// GetRecurringPaymentEssentialsRequest_EnachMultiError is an error wrapping
// multiple validation errors returned by
// GetRecurringPaymentEssentialsRequest_Enach.ValidateAll() if the designated
// constraints aren't met.
type GetRecurringPaymentEssentialsRequest_EnachMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRecurringPaymentEssentialsRequest_EnachMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRecurringPaymentEssentialsRequest_EnachMultiError) AllErrors() []error { return m }

// GetRecurringPaymentEssentialsRequest_EnachValidationError is the validation
// error returned by GetRecurringPaymentEssentialsRequest_Enach.Validate if
// the designated constraints aren't met.
type GetRecurringPaymentEssentialsRequest_EnachValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRecurringPaymentEssentialsRequest_EnachValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRecurringPaymentEssentialsRequest_EnachValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRecurringPaymentEssentialsRequest_EnachValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRecurringPaymentEssentialsRequest_EnachValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRecurringPaymentEssentialsRequest_EnachValidationError) ErrorName() string {
	return "GetRecurringPaymentEssentialsRequest_EnachValidationError"
}

// Error satisfies the builtin error interface
func (e GetRecurringPaymentEssentialsRequest_EnachValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRecurringPaymentEssentialsRequest_Enach.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRecurringPaymentEssentialsRequest_EnachValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRecurringPaymentEssentialsRequest_EnachValidationError{}

// Validate checks the field values on
// GetRecurringPaymentEssentialsResponse_EnachData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetRecurringPaymentEssentialsResponse_EnachData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetRecurringPaymentEssentialsResponse_EnachData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetRecurringPaymentEssentialsResponse_EnachDataMultiError, or nil if none found.
func (m *GetRecurringPaymentEssentialsResponse_EnachData) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRecurringPaymentEssentialsResponse_EnachData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Umrn

	// no validation rules for OrgName

	if len(errors) > 0 {
		return GetRecurringPaymentEssentialsResponse_EnachDataMultiError(errors)
	}

	return nil
}

// GetRecurringPaymentEssentialsResponse_EnachDataMultiError is an error
// wrapping multiple validation errors returned by
// GetRecurringPaymentEssentialsResponse_EnachData.ValidateAll() if the
// designated constraints aren't met.
type GetRecurringPaymentEssentialsResponse_EnachDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRecurringPaymentEssentialsResponse_EnachDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRecurringPaymentEssentialsResponse_EnachDataMultiError) AllErrors() []error { return m }

// GetRecurringPaymentEssentialsResponse_EnachDataValidationError is the
// validation error returned by
// GetRecurringPaymentEssentialsResponse_EnachData.Validate if the designated
// constraints aren't met.
type GetRecurringPaymentEssentialsResponse_EnachDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRecurringPaymentEssentialsResponse_EnachDataValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetRecurringPaymentEssentialsResponse_EnachDataValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetRecurringPaymentEssentialsResponse_EnachDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRecurringPaymentEssentialsResponse_EnachDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRecurringPaymentEssentialsResponse_EnachDataValidationError) ErrorName() string {
	return "GetRecurringPaymentEssentialsResponse_EnachDataValidationError"
}

// Error satisfies the builtin error interface
func (e GetRecurringPaymentEssentialsResponse_EnachDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRecurringPaymentEssentialsResponse_EnachData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRecurringPaymentEssentialsResponse_EnachDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRecurringPaymentEssentialsResponse_EnachDataValidationError{}

// Validate checks the field values on
// GetUpcomingTransactionsResponse_AccountDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetUpcomingTransactionsResponse_AccountDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetUpcomingTransactionsResponse_AccountDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetUpcomingTransactionsResponse_AccountDetailsMultiError, or nil if none found.
func (m *GetUpcomingTransactionsResponse_AccountDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUpcomingTransactionsResponse_AccountDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountId

	if all {
		switch v := interface{}(m.GetFundsToAdd()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUpcomingTransactionsResponse_AccountDetailsValidationError{
					field:  "FundsToAdd",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUpcomingTransactionsResponse_AccountDetailsValidationError{
					field:  "FundsToAdd",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFundsToAdd()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUpcomingTransactionsResponse_AccountDetailsValidationError{
				field:  "FundsToAdd",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetUpcomingTransactionsResponse_AccountDetailsMultiError(errors)
	}

	return nil
}

// GetUpcomingTransactionsResponse_AccountDetailsMultiError is an error
// wrapping multiple validation errors returned by
// GetUpcomingTransactionsResponse_AccountDetails.ValidateAll() if the
// designated constraints aren't met.
type GetUpcomingTransactionsResponse_AccountDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUpcomingTransactionsResponse_AccountDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUpcomingTransactionsResponse_AccountDetailsMultiError) AllErrors() []error { return m }

// GetUpcomingTransactionsResponse_AccountDetailsValidationError is the
// validation error returned by
// GetUpcomingTransactionsResponse_AccountDetails.Validate if the designated
// constraints aren't met.
type GetUpcomingTransactionsResponse_AccountDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUpcomingTransactionsResponse_AccountDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUpcomingTransactionsResponse_AccountDetailsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetUpcomingTransactionsResponse_AccountDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUpcomingTransactionsResponse_AccountDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUpcomingTransactionsResponse_AccountDetailsValidationError) ErrorName() string {
	return "GetUpcomingTransactionsResponse_AccountDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e GetUpcomingTransactionsResponse_AccountDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUpcomingTransactionsResponse_AccountDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUpcomingTransactionsResponse_AccountDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUpcomingTransactionsResponse_AccountDetailsValidationError{}
