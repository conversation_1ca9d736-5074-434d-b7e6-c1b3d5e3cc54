//go:generate gen_sql -types=Feature,FiLiteDetails,FeatureDetails,FeatureInfo,PanAadharLinkageDetails,StageProcLastResponse
// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/user/onboarding/internal/onboarding_details.proto

package onboarding

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	card "github.com/epifi/gamma/api/card"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	enums "github.com/epifi/gamma/api/inappreferral/enums"
	kyc "github.com/epifi/gamma/api/kyc"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	onboarding "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/onboarding"
	customer "github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type OnboardingStage int32

const (
	OnboardingStage_ONBOARDING_STAGE_UNSPECIFIED OnboardingStage = 0
	// This stage allows a user to enter a finite code.
	// Currently finite code is a must have to gain access to the app, in future this will be optional and
	// will act as just referral code.
	// Whether to provide access to the app without finite code or not will be controlled by ONB team.
	OnboardingStage_REFERRAL_FINITE_CODE OnboardingStage = 7
	// stage to collection consents such as tnc, privacy policy etc
	OnboardingStage_TNC_CONSENT OnboardingStage = 8
	// Stage to check the presence of credit report of user
	// Valid States:
	// SUCCESS - If the CB report check api was executed without failure
	// FAILURE - If CB report check api failed during execution
	// IN-PROGRESS - If CB report check is being retried
	// SKIPPED - If CB report check is to be skipped. We will have a feature flag to disable CB report presence check.
	OnboardingStage_CHECK_CREDIT_REPORT_PRESENCE OnboardingStage = 101
	// Stage to take user consent to download credit report
	// Valid States:
	// SUCCESS - If the consent was successfully collected
	// UNSPECIFIED - If the consent was not collected yet, or there was any error in consent collection
	// SKIPPED - We will skip this stage if CB report feature flag is off, or if the user's CB report is not present.
	// Negative consent will be persisted as meta-data in AppScreeningMetaData
	OnboardingStage_CONSENT_CREDIT_REPORT_DOWNLOAD OnboardingStage = 102
	// Stage to Verify credit report of user
	// Valid States:
	// SUCCESS - If the CB report was successfully verified to valid or in-valid
	// IN-PROGRESS - If the CB report download and verification is in progress
	// FAILURE - If there was any api failure in either downloading or verifying the credit report
	// SKIPPED - This will be skipped if CB report feature flag is off, or if user's CB report is not present.
	// Verification status will be persisted in metadata in AppScreeningMetaData
	OnboardingStage_CREDIT_REPORT_VERIFICATION OnboardingStage = 103
	// Employment type declaration Stage.
	//  1. Salaried employees: This stage will go to in-progress for salaried employees.
	//     Will move to terminal status SUCCESS/FAILURE after verification is complete
	//  2. For Non-salaried employees: This stage will be made success and the user will
	//     move to Manual screening, or Reject state depending on the user flow
	//
	// SUCCESS - If user's employment verification was done successfully
	// FAILURE - If user was non-salaried or employment details were not found in MCA database
	// SKIPPED - If user was screened through CB report check
	OnboardingStage_EMPLOYMENT_VERIFICATION OnboardingStage = 104
	// Stage to check the presence of credit report of user, with their PAN and PAN name.
	// Valid States:
	// SUCCESS - If the CB report check api was executed without failure
	// FAILURE - If CB report check api failed during execution
	// IN-PROGRESS - If CB report check is being retried
	// SKIPPED - If CB report check is to be skipped - based on feature flag or a previous stage success.
	OnboardingStage_CREDIT_REPORT_PRESENCE_CHECK_WITH_PAN OnboardingStage = 115
	// payu affluence check
	OnboardingStage_AFFLUENCE_CHECK OnboardingStage = 161
	// This stage checks presence of GSTIN number using pan at vendor's end
	// This stage is added after DOB_AND_PAN stage since the input required is the PAN of the user
	// This stage is considered successful and user clears app screening IF -
	// GSTIN number is found at vendor's end OR
	// API failure or retry exhaustion occurs
	// The only case when user is moved to next screening stage is when there's no API failure i.e. we get response from vendor indicating no GSTIN number found
	OnboardingStage_GSTIN_PRESENCE_CHECK OnboardingStage = 113
	// Linked in based verification of user
	// TBD how this verification is going to be done.
	// This stage is added here because the user choose to wait here until this feature is shipped
	// TODO(keerthana): Update this documentation
	OnboardingStage_LINKEDIN_VERIFICATION OnboardingStage = 109
	// Current heuristic based screening allows user to pass screening based on following
	// 1. Device model - Premium/non-Premium devices. Premium device users will be allowed to pass screener
	OnboardingStage_HEURISTIC_VERIFICATION OnboardingStage = 112
	// Form16 check
	// If the user's tax was submitted by their employer, we would allow them
	OnboardingStage_FORM16_CHECK OnboardingStage = 118
	// Mandate CB consent
	// Valid States:
	// SUCCESS - If the consent was successfully collected
	// UNSPECIFIED - If the consent was not collected yet, or there was any error in consent collection
	// SKIPPED - Stage is skipped if CB report feature flag is off, or CB report verification was already done.
	// Negative consent will be persisted as meta-data in AppScreeningMetaData
	OnboardingStage_MANDATE_CONSENT_CREDIT_REPORT OnboardingStage = 105
	// Gmail based verification is alternate way to filter user for app screening.
	// The state of this stage only represents if verification was carried out successfully or not.
	// Even if the stage is successful its possible that the user was rejected due to low threshold.
	// Valid states:
	// SUCCESS - Gmail verification was successfully performed for the user
	// FAILED - Gmail verification failed for the user
	// IN-PROGRESS - Gmail verification is in progress for the user
	// SKIPPED - Gmail verification was skipped for them. This is possible if the app-screening was done for the user
	OnboardingStage_GMAIL_VERIFICATION OnboardingStage = 108
	// work email based OTP verification
	// user will be subjected to this stage only if the user is
	OnboardingStage_WORK_EMAIL_VERIFICATION OnboardingStage = 111
	// This stage checks presence of UAN number using phone number at vendor's end
	// This stage is added immediately after credit report check to establish salaried history
	// This stage is considered successful and user clears app screening IF -
	// UAN number is found at vendor's end
	// The user is moved to next stage in cases of:
	// API failures, retries reached or UAN number not found for the phone number
	OnboardingStage_UAN_PRESENCE_CHECK OnboardingStage = 114
	// This stage runs screener check based on income estimation by in-house DS model
	OnboardingStage_INCOME_ESTIMATE_CHECK OnboardingStage = 119
	// Manual Screening stage
	// Valid States:
	// SUCCESS - Manually accepting user after app screening. User is allowed to proceed with onboarding
	// FAILED - Manually rejecting user after app screening. User is not allowed to proceed with onboarding
	// SKIPPED - This will be skipped if user is screened already through CB report or employment verification
	// INPROGRESS - user is waiting to be screened manually.
	// Reason for manual allowing or rejecting of user is persisted in AppScreeningMetaData
	OnboardingStage_MANUAL_SCREENING OnboardingStage = 106
	// Parent stage for all stages in app screening.
	// The overall status of app screening will be maintained at this stage.
	// Valid States:
	// SUCCESS - user accepted after app screening. User is allowed to proceed with onboarding
	// FAILED - user rejected app screening. User is not allowed to proceed with onboarding
	// SKIPPED - user was not subjected to screening. Currently, all the pre-waitlisted users and some special users can skip screening.
	OnboardingStage_APP_SCREENING OnboardingStage = 107
	// Stage for company search in screener 2.0
	OnboardingStage_EPFO_COMPANY_SEARCH OnboardingStage = 116
	// Stage for connected accounts screener check
	OnboardingStage_CONNECTED_ACCOUNTS OnboardingStage = 117
	// Dob and pan of user are collected along with KYC initialisation
	OnboardingStage_DOB_AND_PAN OnboardingStage = 12
	// Stage represents check to validate if user is already a federal user
	OnboardingStage_DEDUPE_CHECK OnboardingStage = 17
	// Initiate ckyc
	OnboardingStage_INITIATE_CKYC OnboardingStage = 20
	// Mother father name collection
	OnboardingStage_MOTHER_FATHER_NAME OnboardingStage = 23
	// Currently KYC_AND_LIVENESS_COMPLETION stage takes care of CKYC, EKYC and Liveness flow of the user.
	// Breaking the above stages into multiple stages to improve communication for stuck users at different stages in the above flow.
	OnboardingStage_CKYC     OnboardingStage = 25
	OnboardingStage_EKYC     OnboardingStage = 26
	OnboardingStage_LIVENESS OnboardingStage = 27
	// Stage represents CKYC, EKYC & Liveness Facematch steps
	OnboardingStage_KYC_AND_LIVENESS_COMPLETION OnboardingStage = 30
	// KYC gets expired after x days. This stage ensures that if
	// KYC is expired before customer creation, this stage can be re initiated. The steps to redo KYC
	// are similar to initial KYC but not the same. It's marked success only
	// after CUSTOMER_CREATION is done. Once bank customer is created, we
	// no longer need to ensure KYC availability.
	OnboardingStage_ENSURE_KYC_AVAILABILITY OnboardingStage = 31
	// Ensure PAN name and KYC name match
	OnboardingStage_PAN_NAME_CHECK OnboardingStage = 32
	// RISK_SCREENING is a backend stage that does risk analysis of the user and blocks them if fraud probability
	// is high otherwise passes them through. Currently using KYC address Pincode as a signal.
	// New signals can be used in future.
	OnboardingStage_RISK_SCREENING OnboardingStage = 33
	// Once kyc is done, we need to update some parameters like
	// kyc level, user image from liveness, and user legal name in user service.
	// There are some additional steps performed in customer creation, moving those here as dedupe customers will not have
	// customer creation flow.
	OnboardingStage_UPDATE_CUSTOMER_DETAILS OnboardingStage = 35
	// Validates customer's name in United Nations sanctions list.
	// If the customer name is sanctioned, the customer is blocked
	// from onboarding until the KYC details are manually verified.
	OnboardingStage_UN_NAME_CHECK OnboardingStage = 40
	// This stage calls dedupe for the user with kyc details
	OnboardingStage_KYC_DEDUPE_CHECK OnboardingStage = 45
	// This stage calls partner bank to match Name and DOB received in EKYC response.
	// The stage is skipped if the user is CKYC.
	OnboardingStage_EKYC_NAME_DOB_VALIDATION OnboardingStage = 47
	// This stage calls partner bank to match Name and DOB received in BKYC or EKYC response.
	// The stage is skipped if the user is CKYC.
	// This is being added to also allow the validation for BKYC users.
	OnboardingStage_KYC_NAME_DOB_VALIDATION OnboardingStage = 48
	// Stage represents confirming card mailing address from user
	OnboardingStage_CONFIRM_CARD_MAILING_ADDRESS OnboardingStage = 50
	// Stage represents upi consent collection from user
	OnboardingStage_UPI_CONSENT OnboardingStage = 55
	// This stage does not allow the user to move ahead IF -
	// The pan entered by the user is used by some other user and this other user is IN or PAST customer creation stage
	OnboardingStage_PAN_UNIQUENESS_CHECK OnboardingStage = 58
	// Stage represents device registration of the user device with the vendor bank
	OnboardingStage_DEVICE_REGISTRATION OnboardingStage = 60
	// Stage represents dedupe check as caution mechanism, right before the customer creation
	OnboardingStage_PRE_CUSTOMER_CREATION_DEDUPE_CHECK OnboardingStage = 75
	// Stage to do precautionary checks before initiating custom creation so that users don't get stuck on it
	OnboardingStage_PRE_CUSTOMER_CREATION_CHECK OnboardingStage = 76
	// Stage represents customer creation for the user with vendor bank
	OnboardingStage_CUSTOMER_CREATION OnboardingStage = 80
	// Stage represents account creation of user with vendor bank
	OnboardingStage_ACCOUNT_CREATION OnboardingStage = 100
	// Stage represents updating shipping address at vendor bank
	OnboardingStage_SHIPPING_ADDRESS_UPDATE OnboardingStage = 110
	// Stage represents card creation for the user
	OnboardingStage_CARD_CREATION OnboardingStage = 120
	// Makes UN NAME CHECK API call to the partner bank. If the name
	// is in sanctioned list, partner bank is notified for manual review.
	OnboardingStage_UN_NAME_CHECK_NOTIFIER OnboardingStage = 125
	// Stage represents upi id creation
	OnboardingStage_UPI_SETUP OnboardingStage = 140
	// Set Debit Card PIN
	OnboardingStage_DEBIT_CARD_PIN_SETUP OnboardingStage = 145
	// VKYC step
	OnboardingStage_VKYC OnboardingStage = 147
	// Add money/funds to the savings account
	OnboardingStage_ADD_MONEY OnboardingStage = 150
	// optional vkyc step
	OnboardingStage_OPTIONAL_VKYC OnboardingStage = 151
	// onboarding completed
	OnboardingStage_ONBOARDING_COMPLETE OnboardingStage = 160
	// This stage checks if a user is located outside india and blocks if so.
	// Currently works on location based on ip address. If no address was found for an ip, we let the user through.
	OnboardingStage_LOCATION_CHECK OnboardingStage = 162
	// users whitelisted by agent have to gone through this stage
	OnboardingStage_BKYC OnboardingStage = 163
	// This stage runs risk initial risk checks for user post TnC stage to allow access to fi-lite features.
	OnboardingStage_FI_LITE_RISK_SCREENING OnboardingStage = 164
	// This stage decides which onboarding journey to put a user through based on intent
	OnboardingStage_INTENT_SELECTION OnboardingStage = 165
	// This stage polls the next action from firefly service once the orchestration is handed to CC in case of CC onboarding
	OnboardingStage_CREDIT_CARD_ONBOARDING_STATUS_CHECK OnboardingStage = 166
	// this stage shows savings account introduction screen and collects necessary consent to open and operate a savings account
	OnboardingStage_SAVINGS_INTRO_CONSENT OnboardingStage = 167
	// stage for PL onboarding status
	OnboardingStage_PL_ONBOARDING_STATUS_CHECK OnboardingStage = 168
	// Stage for ITR intimation verification - it is a part of app screener
	// This stage fetches income and other basic details of a user from the uploaded ITR intimation
	// It then verifies these details and decides whether to pass the stage on basis of some validations and cutoffs
	OnboardingStage_ITR_INTIMATION_VERIFICATION OnboardingStage = 169
	// This stage checks the credit report of users and blocks onboarding if any check fails - example, credit score below certain threshold
	OnboardingStage_CREDIT_REPORT_CHECK OnboardingStage = 170
	// Stage to check if onboarding number matches with the number linked in aadhar
	OnboardingStage_AADHAR_MOBILE_VALIDATION OnboardingStage = 171
	// Stage for profile update at vendor
	// This stage updates the occupation for dedupe users at the vendor if the user's occupation at the vendor is listed as 'OTHERS', at time of implementation.
	OnboardingStage_UPDATE_PROFILE_DETAILS OnboardingStage = 172
	// Stage to check lendability of a user
	// Part of app screening
	OnboardingStage_LENDABILITY_CHECK OnboardingStage = 173
	// Stage to verify user passport
	// Currently applicable for NR onboarding
	OnboardingStage_PASSPORT_VERIFICATION OnboardingStage = 174
	// Stage to verify user VISA
	// Currently applicable for NR onboarding
	OnboardingStage_VISA_VERIFICATION OnboardingStage = 175
	// Stage to verify user's country ID
	// Currently applicable for NR onboarding
	OnboardingStage_COUNTRY_ID_VERIFICATION OnboardingStage = 176
	// Cross Data validation stage for NR onboarding
	OnboardingStage_NON_RESIDENT_ONBOARDING_CROSS_DATA_VALIDATION OnboardingStage = 177
	// This stage collects user's interest in various products and features to personalise the in-app experience
	OnboardingStage_SOFT_INTENT_SELECTION OnboardingStage = 180
	// This stage collects the communication address during onboarding flow.
	OnboardingStage_COMMUNICATION_ADDRESS OnboardingStage = 181
	// This stage trigger the NRO account creation if required
	OnboardingStage_NRO_ACCOUNT_CREATION OnboardingStage = 182
	// This stage trigger order physical debit card flow based on user's intent
	OnboardingStage_ORDER_PHYSICAL_CARD OnboardingStage = 183
	// This stage is for users who are allowed to open account with AMB (regular tier account) after failing screener checks
	OnboardingStage_OPEN_MIN_BALANCE_ACCOUNT OnboardingStage = 184
	// This stage collect consent to process user's SMS data and start sms parsing asynchronously
	OnboardingStage_SMS_PARSER_CONSENT OnboardingStage = 185
	// Stage for initiating credit report fetch
	OnboardingStage_INITIATE_CREDIT_REPORT_FETCH OnboardingStage = 186
	// stage for Wealth onboarding status check
	OnboardingStage_WEALTH_ANALYSER_ONBOARDING_STATUS_CHECK OnboardingStage = 187
	// stage for sms parser data verification
	OnboardingStage_SMS_PARSER_DATA_VERIFICATION OnboardingStage = 188
	// screener stage for app heuristics data verification
	OnboardingStage_INSTALLED_APPS_CHECK OnboardingStage = 189
	// stage for pre account creation add money (aka prefunding)
	OnboardingStage_PRE_ACCOUNT_CREATION_ADD_MONEY OnboardingStage = 190
	// stage to collect additional profile details for federal loans onboarding flow.
	OnboardingStage_COLLECT_ADDITIONAL_PROFILE_DETAILS_FOR_FEDERAL_LOANS OnboardingStage = 191
	// stage to collect permission from the user during onboarding flow.
	OnboardingStage_PERMISSION OnboardingStage = 192
	// This stage waits for SMS and Credit Report fetch to complete
	OnboardingStage_WAIT_FOR_AUTO_PAN OnboardingStage = 193
	OnboardingStage_FORM_60           OnboardingStage = 194
	// Stage for connected accounts for wealth builder users
	OnboardingStage_WEALTH_BUILDER_CONNECTED_ACCOUNTS_FLOW OnboardingStage = 195
	// Stage to make wealth builder onboarding as completed.
	OnboardingStage_WEALTH_ANALYSER_ONBOARDING_COMPLETE OnboardingStage = 196
	// Stage to collect declarations for savings account onboarding
	OnboardingStage_SA_DECLARATION OnboardingStage = 197
	// Stage to ensure credit report availability for the user till screener is completed.
	// If credit report is expired then this stage will re-initiate the credit report fetch.
	OnboardingStage_ENSURE_CREDIT_REPORT_AVAILABILITY OnboardingStage = 198
)

// Enum value maps for OnboardingStage.
var (
	OnboardingStage_name = map[int32]string{
		0:   "ONBOARDING_STAGE_UNSPECIFIED",
		7:   "REFERRAL_FINITE_CODE",
		8:   "TNC_CONSENT",
		101: "CHECK_CREDIT_REPORT_PRESENCE",
		102: "CONSENT_CREDIT_REPORT_DOWNLOAD",
		103: "CREDIT_REPORT_VERIFICATION",
		104: "EMPLOYMENT_VERIFICATION",
		115: "CREDIT_REPORT_PRESENCE_CHECK_WITH_PAN",
		161: "AFFLUENCE_CHECK",
		113: "GSTIN_PRESENCE_CHECK",
		109: "LINKEDIN_VERIFICATION",
		112: "HEURISTIC_VERIFICATION",
		118: "FORM16_CHECK",
		105: "MANDATE_CONSENT_CREDIT_REPORT",
		108: "GMAIL_VERIFICATION",
		111: "WORK_EMAIL_VERIFICATION",
		114: "UAN_PRESENCE_CHECK",
		119: "INCOME_ESTIMATE_CHECK",
		106: "MANUAL_SCREENING",
		107: "APP_SCREENING",
		116: "EPFO_COMPANY_SEARCH",
		117: "CONNECTED_ACCOUNTS",
		12:  "DOB_AND_PAN",
		17:  "DEDUPE_CHECK",
		20:  "INITIATE_CKYC",
		23:  "MOTHER_FATHER_NAME",
		25:  "CKYC",
		26:  "EKYC",
		27:  "LIVENESS",
		30:  "KYC_AND_LIVENESS_COMPLETION",
		31:  "ENSURE_KYC_AVAILABILITY",
		32:  "PAN_NAME_CHECK",
		33:  "RISK_SCREENING",
		35:  "UPDATE_CUSTOMER_DETAILS",
		40:  "UN_NAME_CHECK",
		45:  "KYC_DEDUPE_CHECK",
		47:  "EKYC_NAME_DOB_VALIDATION",
		48:  "KYC_NAME_DOB_VALIDATION",
		50:  "CONFIRM_CARD_MAILING_ADDRESS",
		55:  "UPI_CONSENT",
		58:  "PAN_UNIQUENESS_CHECK",
		60:  "DEVICE_REGISTRATION",
		75:  "PRE_CUSTOMER_CREATION_DEDUPE_CHECK",
		76:  "PRE_CUSTOMER_CREATION_CHECK",
		80:  "CUSTOMER_CREATION",
		100: "ACCOUNT_CREATION",
		110: "SHIPPING_ADDRESS_UPDATE",
		120: "CARD_CREATION",
		125: "UN_NAME_CHECK_NOTIFIER",
		140: "UPI_SETUP",
		145: "DEBIT_CARD_PIN_SETUP",
		147: "VKYC",
		150: "ADD_MONEY",
		151: "OPTIONAL_VKYC",
		160: "ONBOARDING_COMPLETE",
		162: "LOCATION_CHECK",
		163: "BKYC",
		164: "FI_LITE_RISK_SCREENING",
		165: "INTENT_SELECTION",
		166: "CREDIT_CARD_ONBOARDING_STATUS_CHECK",
		167: "SAVINGS_INTRO_CONSENT",
		168: "PL_ONBOARDING_STATUS_CHECK",
		169: "ITR_INTIMATION_VERIFICATION",
		170: "CREDIT_REPORT_CHECK",
		171: "AADHAR_MOBILE_VALIDATION",
		172: "UPDATE_PROFILE_DETAILS",
		173: "LENDABILITY_CHECK",
		174: "PASSPORT_VERIFICATION",
		175: "VISA_VERIFICATION",
		176: "COUNTRY_ID_VERIFICATION",
		177: "NON_RESIDENT_ONBOARDING_CROSS_DATA_VALIDATION",
		180: "SOFT_INTENT_SELECTION",
		181: "COMMUNICATION_ADDRESS",
		182: "NRO_ACCOUNT_CREATION",
		183: "ORDER_PHYSICAL_CARD",
		184: "OPEN_MIN_BALANCE_ACCOUNT",
		185: "SMS_PARSER_CONSENT",
		186: "INITIATE_CREDIT_REPORT_FETCH",
		187: "WEALTH_ANALYSER_ONBOARDING_STATUS_CHECK",
		188: "SMS_PARSER_DATA_VERIFICATION",
		189: "INSTALLED_APPS_CHECK",
		190: "PRE_ACCOUNT_CREATION_ADD_MONEY",
		191: "COLLECT_ADDITIONAL_PROFILE_DETAILS_FOR_FEDERAL_LOANS",
		192: "PERMISSION",
		193: "WAIT_FOR_AUTO_PAN",
		194: "FORM_60",
		195: "WEALTH_BUILDER_CONNECTED_ACCOUNTS_FLOW",
		196: "WEALTH_ANALYSER_ONBOARDING_COMPLETE",
		197: "SA_DECLARATION",
		198: "ENSURE_CREDIT_REPORT_AVAILABILITY",
	}
	OnboardingStage_value = map[string]int32{
		"ONBOARDING_STAGE_UNSPECIFIED":                  0,
		"REFERRAL_FINITE_CODE":                          7,
		"TNC_CONSENT":                                   8,
		"CHECK_CREDIT_REPORT_PRESENCE":                  101,
		"CONSENT_CREDIT_REPORT_DOWNLOAD":                102,
		"CREDIT_REPORT_VERIFICATION":                    103,
		"EMPLOYMENT_VERIFICATION":                       104,
		"CREDIT_REPORT_PRESENCE_CHECK_WITH_PAN":         115,
		"AFFLUENCE_CHECK":                               161,
		"GSTIN_PRESENCE_CHECK":                          113,
		"LINKEDIN_VERIFICATION":                         109,
		"HEURISTIC_VERIFICATION":                        112,
		"FORM16_CHECK":                                  118,
		"MANDATE_CONSENT_CREDIT_REPORT":                 105,
		"GMAIL_VERIFICATION":                            108,
		"WORK_EMAIL_VERIFICATION":                       111,
		"UAN_PRESENCE_CHECK":                            114,
		"INCOME_ESTIMATE_CHECK":                         119,
		"MANUAL_SCREENING":                              106,
		"APP_SCREENING":                                 107,
		"EPFO_COMPANY_SEARCH":                           116,
		"CONNECTED_ACCOUNTS":                            117,
		"DOB_AND_PAN":                                   12,
		"DEDUPE_CHECK":                                  17,
		"INITIATE_CKYC":                                 20,
		"MOTHER_FATHER_NAME":                            23,
		"CKYC":                                          25,
		"EKYC":                                          26,
		"LIVENESS":                                      27,
		"KYC_AND_LIVENESS_COMPLETION":                   30,
		"ENSURE_KYC_AVAILABILITY":                       31,
		"PAN_NAME_CHECK":                                32,
		"RISK_SCREENING":                                33,
		"UPDATE_CUSTOMER_DETAILS":                       35,
		"UN_NAME_CHECK":                                 40,
		"KYC_DEDUPE_CHECK":                              45,
		"EKYC_NAME_DOB_VALIDATION":                      47,
		"KYC_NAME_DOB_VALIDATION":                       48,
		"CONFIRM_CARD_MAILING_ADDRESS":                  50,
		"UPI_CONSENT":                                   55,
		"PAN_UNIQUENESS_CHECK":                          58,
		"DEVICE_REGISTRATION":                           60,
		"PRE_CUSTOMER_CREATION_DEDUPE_CHECK":            75,
		"PRE_CUSTOMER_CREATION_CHECK":                   76,
		"CUSTOMER_CREATION":                             80,
		"ACCOUNT_CREATION":                              100,
		"SHIPPING_ADDRESS_UPDATE":                       110,
		"CARD_CREATION":                                 120,
		"UN_NAME_CHECK_NOTIFIER":                        125,
		"UPI_SETUP":                                     140,
		"DEBIT_CARD_PIN_SETUP":                          145,
		"VKYC":                                          147,
		"ADD_MONEY":                                     150,
		"OPTIONAL_VKYC":                                 151,
		"ONBOARDING_COMPLETE":                           160,
		"LOCATION_CHECK":                                162,
		"BKYC":                                          163,
		"FI_LITE_RISK_SCREENING":                        164,
		"INTENT_SELECTION":                              165,
		"CREDIT_CARD_ONBOARDING_STATUS_CHECK":           166,
		"SAVINGS_INTRO_CONSENT":                         167,
		"PL_ONBOARDING_STATUS_CHECK":                    168,
		"ITR_INTIMATION_VERIFICATION":                   169,
		"CREDIT_REPORT_CHECK":                           170,
		"AADHAR_MOBILE_VALIDATION":                      171,
		"UPDATE_PROFILE_DETAILS":                        172,
		"LENDABILITY_CHECK":                             173,
		"PASSPORT_VERIFICATION":                         174,
		"VISA_VERIFICATION":                             175,
		"COUNTRY_ID_VERIFICATION":                       176,
		"NON_RESIDENT_ONBOARDING_CROSS_DATA_VALIDATION": 177,
		"SOFT_INTENT_SELECTION":                         180,
		"COMMUNICATION_ADDRESS":                         181,
		"NRO_ACCOUNT_CREATION":                          182,
		"ORDER_PHYSICAL_CARD":                           183,
		"OPEN_MIN_BALANCE_ACCOUNT":                      184,
		"SMS_PARSER_CONSENT":                            185,
		"INITIATE_CREDIT_REPORT_FETCH":                  186,
		"WEALTH_ANALYSER_ONBOARDING_STATUS_CHECK":       187,
		"SMS_PARSER_DATA_VERIFICATION":                  188,
		"INSTALLED_APPS_CHECK":                          189,
		"PRE_ACCOUNT_CREATION_ADD_MONEY":                190,
		"COLLECT_ADDITIONAL_PROFILE_DETAILS_FOR_FEDERAL_LOANS": 191,
		"PERMISSION":                             192,
		"WAIT_FOR_AUTO_PAN":                      193,
		"FORM_60":                                194,
		"WEALTH_BUILDER_CONNECTED_ACCOUNTS_FLOW": 195,
		"WEALTH_ANALYSER_ONBOARDING_COMPLETE":    196,
		"SA_DECLARATION":                         197,
		"ENSURE_CREDIT_REPORT_AVAILABILITY":      198,
	}
)

func (x OnboardingStage) Enum() *OnboardingStage {
	p := new(OnboardingStage)
	*p = x
	return p
}

func (x OnboardingStage) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OnboardingStage) Descriptor() protoreflect.EnumDescriptor {
	return file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[0].Descriptor()
}

func (OnboardingStage) Type() protoreflect.EnumType {
	return &file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[0]
}

func (x OnboardingStage) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OnboardingStage.Descriptor instead.
func (OnboardingStage) EnumDescriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{0}
}

type OnboardingState int32

const (
	OnboardingState_UNSPECIFIED OnboardingState = 0
	// INITIATED - State represents a stage (customer creation, account creation) is registered with epifi server
	// and message is enqueued
	OnboardingState_INITIATED OnboardingState = 1
	// INPROGRESS - State represents a stage is attempted at least once.
	OnboardingState_INPROGRESS OnboardingState = 2
	// FAILURE - State represents a stage is failed after attempting a vendor request.
	OnboardingState_FAILURE OnboardingState = 3
	// SUCCESS - State represents a stage was successful
	OnboardingState_SUCCESS OnboardingState = 4
	// MANUAL_INTERVENTION - State represents an internal error occurred during processing of stage that requires manual intervention
	OnboardingState_MANUAL_INTERVENTION OnboardingState = 5
	// SKIPPED - State represents a stage was skipped
	OnboardingState_SKIPPED OnboardingState = 6
	// When a stage is forcefully reset after going into a terminal state.
	// the stage can be retried if it's in this state.
	OnboardingState_RESET OnboardingState = 7
)

// Enum value maps for OnboardingState.
var (
	OnboardingState_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "INITIATED",
		2: "INPROGRESS",
		3: "FAILURE",
		4: "SUCCESS",
		5: "MANUAL_INTERVENTION",
		6: "SKIPPED",
		7: "RESET",
	}
	OnboardingState_value = map[string]int32{
		"UNSPECIFIED":         0,
		"INITIATED":           1,
		"INPROGRESS":          2,
		"FAILURE":             3,
		"SUCCESS":             4,
		"MANUAL_INTERVENTION": 5,
		"SKIPPED":             6,
		"RESET":               7,
	}
)

func (x OnboardingState) Enum() *OnboardingState {
	p := new(OnboardingState)
	*p = x
	return p
}

func (x OnboardingState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OnboardingState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[1].Descriptor()
}

func (OnboardingState) Type() protoreflect.EnumType {
	return &file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[1]
}

func (x OnboardingState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OnboardingState.Descriptor instead.
func (OnboardingState) EnumDescriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{1}
}

type UNNameCheckStatus int32

const (
	UNNameCheckStatus_UNNC_UNSPECIFIED UNNameCheckStatus = 0
	// Name not in sanctions list. user safe to proceed.
	UNNameCheckStatus_UNNC_PASSED UNNameCheckStatus = 1
	// Name found in Name check sanctions list.
	// User needs to be scrutinised.
	UNNameCheckStatus_UNNC_NAME_IN_SANCTIONS_LIST UNNameCheckStatus = 2
)

// Enum value maps for UNNameCheckStatus.
var (
	UNNameCheckStatus_name = map[int32]string{
		0: "UNNC_UNSPECIFIED",
		1: "UNNC_PASSED",
		2: "UNNC_NAME_IN_SANCTIONS_LIST",
	}
	UNNameCheckStatus_value = map[string]int32{
		"UNNC_UNSPECIFIED":            0,
		"UNNC_PASSED":                 1,
		"UNNC_NAME_IN_SANCTIONS_LIST": 2,
	}
)

func (x UNNameCheckStatus) Enum() *UNNameCheckStatus {
	p := new(UNNameCheckStatus)
	*p = x
	return p
}

func (x UNNameCheckStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UNNameCheckStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[2].Descriptor()
}

func (UNNameCheckStatus) Type() protoreflect.EnumType {
	return &file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[2]
}

func (x UNNameCheckStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UNNameCheckStatus.Descriptor instead.
func (UNNameCheckStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{2}
}

// FieldMask for helping db updated in onboarding_details table
type OnboardingDetailsFieldMask int32

const (
	OnboardingDetailsFieldMask_ACCOUNT_INFO               OnboardingDetailsFieldMask = 0
	OnboardingDetailsFieldMask_CARD_INFO                  OnboardingDetailsFieldMask = 1
	OnboardingDetailsFieldMask_VENDOR                     OnboardingDetailsFieldMask = 2
	OnboardingDetailsFieldMask_COMPLETED_AT               OnboardingDetailsFieldMask = 3
	OnboardingDetailsFieldMask_STAGE_METADATA             OnboardingDetailsFieldMask = 4
	OnboardingDetailsFieldMask_CURRENT_ONBOARDING_STAGE   OnboardingDetailsFieldMask = 5
	OnboardingDetailsFieldMask_FEATURE                    OnboardingDetailsFieldMask = 6
	OnboardingDetailsFieldMask_FI_LITE_DETAILS            OnboardingDetailsFieldMask = 7
	OnboardingDetailsFieldMask_FEATURE_DETAILS            OnboardingDetailsFieldMask = 8
	OnboardingDetailsFieldMask_PAN_AADHAR_LINKAGE_DETAILS OnboardingDetailsFieldMask = 9
	OnboardingDetailsFieldMask_STAGE_PROC_LAST_RESPONSE   OnboardingDetailsFieldMask = 10
)

// Enum value maps for OnboardingDetailsFieldMask.
var (
	OnboardingDetailsFieldMask_name = map[int32]string{
		0:  "ACCOUNT_INFO",
		1:  "CARD_INFO",
		2:  "VENDOR",
		3:  "COMPLETED_AT",
		4:  "STAGE_METADATA",
		5:  "CURRENT_ONBOARDING_STAGE",
		6:  "FEATURE",
		7:  "FI_LITE_DETAILS",
		8:  "FEATURE_DETAILS",
		9:  "PAN_AADHAR_LINKAGE_DETAILS",
		10: "STAGE_PROC_LAST_RESPONSE",
	}
	OnboardingDetailsFieldMask_value = map[string]int32{
		"ACCOUNT_INFO":               0,
		"CARD_INFO":                  1,
		"VENDOR":                     2,
		"COMPLETED_AT":               3,
		"STAGE_METADATA":             4,
		"CURRENT_ONBOARDING_STAGE":   5,
		"FEATURE":                    6,
		"FI_LITE_DETAILS":            7,
		"FEATURE_DETAILS":            8,
		"PAN_AADHAR_LINKAGE_DETAILS": 9,
		"STAGE_PROC_LAST_RESPONSE":   10,
	}
)

func (x OnboardingDetailsFieldMask) Enum() *OnboardingDetailsFieldMask {
	p := new(OnboardingDetailsFieldMask)
	*p = x
	return p
}

func (x OnboardingDetailsFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OnboardingDetailsFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[3].Descriptor()
}

func (OnboardingDetailsFieldMask) Type() protoreflect.EnumType {
	return &file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[3]
}

func (x OnboardingDetailsFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OnboardingDetailsFieldMask.Descriptor instead.
func (OnboardingDetailsFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{3}
}

type RiskFactor int32

const (
	RiskFactor_RISK_FACTOR_UNSPECIFIED                     RiskFactor = 0
	RiskFactor_RISK_FACTOR_RED_LISTED_CONTACT              RiskFactor = 1
	RiskFactor_RISK_FACTOR_KYC_PIN_CODE                    RiskFactor = 2 // User's KYC address pin code is in block list
	RiskFactor_RISK_FACTOR_LOCATION_PIN_CODE               RiskFactor = 3 // User's device location pin code is in block list
	RiskFactor_RISK_FACTOR_LOCATION_CO_ORDINATE            RiskFactor = 4 // User's device location co-ordinate is in block list
	RiskFactor_RISK_FACTOR_EKYC_ONBOARDING_NUMBER_MISMATCH RiskFactor = 5
	RiskFactor_RISK_FACTOR_HIGH_VELOCITY                   RiskFactor = 6
	RiskFactor_RISK_FACTOR_DEVICE_BLOCKED_LIST             RiskFactor = 7  // User's device model is in forced manual review list
	RiskFactor_RISK_FACTOR_ONBOARDING_RISK_MODEL           RiskFactor = 8  // DS Risk service marked the user as risky
	RiskFactor_RISK_FACTOR_EMPTY_LOCATION_PIN_CODE         RiskFactor = 9  // User's pin code is empty when fetched via geolocation
	RiskFactor_RISK_FACTOR_EPFO_USER                       RiskFactor = 10 // User has passed screener through EPFO and Gmail-PAN name match failed
)

// Enum value maps for RiskFactor.
var (
	RiskFactor_name = map[int32]string{
		0:  "RISK_FACTOR_UNSPECIFIED",
		1:  "RISK_FACTOR_RED_LISTED_CONTACT",
		2:  "RISK_FACTOR_KYC_PIN_CODE",
		3:  "RISK_FACTOR_LOCATION_PIN_CODE",
		4:  "RISK_FACTOR_LOCATION_CO_ORDINATE",
		5:  "RISK_FACTOR_EKYC_ONBOARDING_NUMBER_MISMATCH",
		6:  "RISK_FACTOR_HIGH_VELOCITY",
		7:  "RISK_FACTOR_DEVICE_BLOCKED_LIST",
		8:  "RISK_FACTOR_ONBOARDING_RISK_MODEL",
		9:  "RISK_FACTOR_EMPTY_LOCATION_PIN_CODE",
		10: "RISK_FACTOR_EPFO_USER",
	}
	RiskFactor_value = map[string]int32{
		"RISK_FACTOR_UNSPECIFIED":                     0,
		"RISK_FACTOR_RED_LISTED_CONTACT":              1,
		"RISK_FACTOR_KYC_PIN_CODE":                    2,
		"RISK_FACTOR_LOCATION_PIN_CODE":               3,
		"RISK_FACTOR_LOCATION_CO_ORDINATE":            4,
		"RISK_FACTOR_EKYC_ONBOARDING_NUMBER_MISMATCH": 5,
		"RISK_FACTOR_HIGH_VELOCITY":                   6,
		"RISK_FACTOR_DEVICE_BLOCKED_LIST":             7,
		"RISK_FACTOR_ONBOARDING_RISK_MODEL":           8,
		"RISK_FACTOR_EMPTY_LOCATION_PIN_CODE":         9,
		"RISK_FACTOR_EPFO_USER":                       10,
	}
)

func (x RiskFactor) Enum() *RiskFactor {
	p := new(RiskFactor)
	*p = x
	return p
}

func (x RiskFactor) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RiskFactor) Descriptor() protoreflect.EnumDescriptor {
	return file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[4].Descriptor()
}

func (RiskFactor) Type() protoreflect.EnumType {
	return &file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[4]
}

func (x RiskFactor) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RiskFactor.Descriptor instead.
func (RiskFactor) EnumDescriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{4}
}

type PanNameReviewCategory int32

const (
	PanNameReviewCategory_PAN_NAME_REVIEW_CATEGORY_UNSPECIFIED PanNameReviewCategory = 0
	// Full name mismatch ex -> Rajat and Niket Goel
	PanNameReviewCategory_PAN_NAME_REVIEW_CATEGORY_FULL_NAME_MISMATCH PanNameReviewCategory = 1
	// ex Rajat Kumar and Rahul Kumar
	PanNameReviewCategory_PAN_NAME_REVIEW_CATEGORY_FIRST_NAME_MISMATCH PanNameReviewCategory = 2
	// ex Arun Kumar and Arun singh
	PanNameReviewCategory_PAN_NAME_REVIEW_CATEGORY_LAST_NAME_MISMATCH PanNameReviewCategory = 3
	// ex Arun Kumar Choudhary and  Arun Choudhary
	PanNameReviewCategory_PAN_NAME_REVIEW_CATEGORY_COMMON_MIDDLE_NAME_MISSING PanNameReviewCategory = 4
	// ex Rajat Choudhary Vikas Singh and Rajat Choudri Vikash Singh
	PanNameReviewCategory_PAN_NAME_REVIEW_CATEGORY_SAME_PRONUNCIATION PanNameReviewCategory = 5
	// ex Lalit Mohan Singh and Lalit Mohan Singha
	PanNameReviewCategory_PAN_NAME_REVIEW_CATEGORY_ALTERNATIVE_PRONUNCIAT_COMMON_VARIATION PanNameReviewCategory = 6
	// ex SEKH MAHAMMAD and Sk Md Asif
	PanNameReviewCategory_PAN_NAME_REVIEW_CATEGORY_PRIMARY_IDENTIFIER_MISSING PanNameReviewCategory = 7
	// ex Niket T Goel and Niket A Goel
	PanNameReviewCategory_PAN_NAME_REVIEW_CATEGORY_MIDDLE_NAME_MISMATCH PanNameReviewCategory = 8
	// ex Rahul Kumar Manish Kumar Singh and Kumar Rahul Manish Singh Kumar
	PanNameReviewCategory_PAN_NAME_REVIEW_CATEGORY_JUMBLED_NAMES PanNameReviewCategory = 9
	// ex SHAHRUKH SAIFI and Sharukh
	PanNameReviewCategory_PAN_NAME_REVIEW_CATEGORY_PRIMARY_IDENTIFIER_MATCHES PanNameReviewCategory = 10
	PanNameReviewCategory_PAN_NAME_REVIEW_CATEGORY_COMMON_ABBREVIATIONS       PanNameReviewCategory = 11
	PanNameReviewCategory_PAN_NAME_REVIEW_CATEGORY_FULL_NAME_MATCH            PanNameReviewCategory = 12
	// Remark will contain detail.
	PanNameReviewCategory_PAN_NAME_REVIEW_CATEGORY_OTHER_MISMATCH PanNameReviewCategory = 13
)

// Enum value maps for PanNameReviewCategory.
var (
	PanNameReviewCategory_name = map[int32]string{
		0:  "PAN_NAME_REVIEW_CATEGORY_UNSPECIFIED",
		1:  "PAN_NAME_REVIEW_CATEGORY_FULL_NAME_MISMATCH",
		2:  "PAN_NAME_REVIEW_CATEGORY_FIRST_NAME_MISMATCH",
		3:  "PAN_NAME_REVIEW_CATEGORY_LAST_NAME_MISMATCH",
		4:  "PAN_NAME_REVIEW_CATEGORY_COMMON_MIDDLE_NAME_MISSING",
		5:  "PAN_NAME_REVIEW_CATEGORY_SAME_PRONUNCIATION",
		6:  "PAN_NAME_REVIEW_CATEGORY_ALTERNATIVE_PRONUNCIAT_COMMON_VARIATION",
		7:  "PAN_NAME_REVIEW_CATEGORY_PRIMARY_IDENTIFIER_MISSING",
		8:  "PAN_NAME_REVIEW_CATEGORY_MIDDLE_NAME_MISMATCH",
		9:  "PAN_NAME_REVIEW_CATEGORY_JUMBLED_NAMES",
		10: "PAN_NAME_REVIEW_CATEGORY_PRIMARY_IDENTIFIER_MATCHES",
		11: "PAN_NAME_REVIEW_CATEGORY_COMMON_ABBREVIATIONS",
		12: "PAN_NAME_REVIEW_CATEGORY_FULL_NAME_MATCH",
		13: "PAN_NAME_REVIEW_CATEGORY_OTHER_MISMATCH",
	}
	PanNameReviewCategory_value = map[string]int32{
		"PAN_NAME_REVIEW_CATEGORY_UNSPECIFIED":                             0,
		"PAN_NAME_REVIEW_CATEGORY_FULL_NAME_MISMATCH":                      1,
		"PAN_NAME_REVIEW_CATEGORY_FIRST_NAME_MISMATCH":                     2,
		"PAN_NAME_REVIEW_CATEGORY_LAST_NAME_MISMATCH":                      3,
		"PAN_NAME_REVIEW_CATEGORY_COMMON_MIDDLE_NAME_MISSING":              4,
		"PAN_NAME_REVIEW_CATEGORY_SAME_PRONUNCIATION":                      5,
		"PAN_NAME_REVIEW_CATEGORY_ALTERNATIVE_PRONUNCIAT_COMMON_VARIATION": 6,
		"PAN_NAME_REVIEW_CATEGORY_PRIMARY_IDENTIFIER_MISSING":              7,
		"PAN_NAME_REVIEW_CATEGORY_MIDDLE_NAME_MISMATCH":                    8,
		"PAN_NAME_REVIEW_CATEGORY_JUMBLED_NAMES":                           9,
		"PAN_NAME_REVIEW_CATEGORY_PRIMARY_IDENTIFIER_MATCHES":              10,
		"PAN_NAME_REVIEW_CATEGORY_COMMON_ABBREVIATIONS":                    11,
		"PAN_NAME_REVIEW_CATEGORY_FULL_NAME_MATCH":                         12,
		"PAN_NAME_REVIEW_CATEGORY_OTHER_MISMATCH":                          13,
	}
)

func (x PanNameReviewCategory) Enum() *PanNameReviewCategory {
	p := new(PanNameReviewCategory)
	*p = x
	return p
}

func (x PanNameReviewCategory) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PanNameReviewCategory) Descriptor() protoreflect.EnumDescriptor {
	return file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[5].Descriptor()
}

func (PanNameReviewCategory) Type() protoreflect.EnumType {
	return &file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[5]
}

func (x PanNameReviewCategory) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PanNameReviewCategory.Descriptor instead.
func (PanNameReviewCategory) EnumDescriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{5}
}

type Verdict int32

const (
	Verdict_VERDICT_UNSPECIFIED Verdict = 0
	Verdict_VERDICT_PASS        Verdict = 1
	Verdict_VERDICT_FAIL        Verdict = 2
)

// Enum value maps for Verdict.
var (
	Verdict_name = map[int32]string{
		0: "VERDICT_UNSPECIFIED",
		1: "VERDICT_PASS",
		2: "VERDICT_FAIL",
	}
	Verdict_value = map[string]int32{
		"VERDICT_UNSPECIFIED": 0,
		"VERDICT_PASS":        1,
		"VERDICT_FAIL":        2,
	}
)

func (x Verdict) Enum() *Verdict {
	p := new(Verdict)
	*p = x
	return p
}

func (x Verdict) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Verdict) Descriptor() protoreflect.EnumDescriptor {
	return file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[6].Descriptor()
}

func (Verdict) Type() protoreflect.EnumType {
	return &file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[6]
}

func (x Verdict) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Verdict.Descriptor instead.
func (Verdict) EnumDescriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{6}
}

// reason why the in-app screening was skipped for the user during onboarding
type ScreeningSkipReason int32

const (
	ScreeningSkipReason_SCREENING_REASON_UNSPECIFIED ScreeningSkipReason = 0
	// feature flag is turned off
	ScreeningSkipReason_FEATURE_FLAG_OFF ScreeningSkipReason = 1
	// platform and app version number check fails
	ScreeningSkipReason_APP_VERSION_CHECK_FAILED ScreeningSkipReason = 2
	// a wait-listed user has already gone through screening
	ScreeningSkipReason_WL_USER ScreeningSkipReason = 3
	// Special PRIVILEGE in the entered FINITE CODE
	ScreeningSkipReason_FINITE_CODE_PRIVILEGE ScreeningSkipReason = 4
	// Non wait-listed old user. The user started onboarding even before we introduced waitlist as a feature.
	ScreeningSkipReason_NON_WL_OLD_USER ScreeningSkipReason = 5
	// B2B Salary program user is whitelisted by manual screening
	ScreeningSkipReason_B2B_SALARY_PROGRAM_USER ScreeningSkipReason = 6
	// users in DC acquisition user group are whitelisted
	ScreeningSkipReason_DEBIT_CARD_PARTNER_ACQUISITION_USER ScreeningSkipReason = 7
	// user didn't succeed screening and opted for min balance account
	ScreeningSkipReason_USER_OPTED_FOR_MIN_BALANCE_ACCOUNT ScreeningSkipReason = 8
)

// Enum value maps for ScreeningSkipReason.
var (
	ScreeningSkipReason_name = map[int32]string{
		0: "SCREENING_REASON_UNSPECIFIED",
		1: "FEATURE_FLAG_OFF",
		2: "APP_VERSION_CHECK_FAILED",
		3: "WL_USER",
		4: "FINITE_CODE_PRIVILEGE",
		5: "NON_WL_OLD_USER",
		6: "B2B_SALARY_PROGRAM_USER",
		7: "DEBIT_CARD_PARTNER_ACQUISITION_USER",
		8: "USER_OPTED_FOR_MIN_BALANCE_ACCOUNT",
	}
	ScreeningSkipReason_value = map[string]int32{
		"SCREENING_REASON_UNSPECIFIED":        0,
		"FEATURE_FLAG_OFF":                    1,
		"APP_VERSION_CHECK_FAILED":            2,
		"WL_USER":                             3,
		"FINITE_CODE_PRIVILEGE":               4,
		"NON_WL_OLD_USER":                     5,
		"B2B_SALARY_PROGRAM_USER":             6,
		"DEBIT_CARD_PARTNER_ACQUISITION_USER": 7,
		"USER_OPTED_FOR_MIN_BALANCE_ACCOUNT":  8,
	}
)

func (x ScreeningSkipReason) Enum() *ScreeningSkipReason {
	p := new(ScreeningSkipReason)
	*p = x
	return p
}

func (x ScreeningSkipReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ScreeningSkipReason) Descriptor() protoreflect.EnumDescriptor {
	return file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[7].Descriptor()
}

func (ScreeningSkipReason) Type() protoreflect.EnumType {
	return &file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[7]
}

func (x ScreeningSkipReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ScreeningSkipReason.Descriptor instead.
func (ScreeningSkipReason) EnumDescriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{7}
}

// reason why credit report flow was skipped from in-app screening for the user during onboarding
type CreditReportScreeningSkipReason int32

const (
	CreditReportScreeningSkipReason_SKIP_REASON_UNSPECIFIED CreditReportScreeningSkipReason = 0
	// credit report flow flag is turned off
	CreditReportScreeningSkipReason_SKIP_REASON_FLAG_OFF CreditReportScreeningSkipReason = 1
	// credit report screening is skipped if presence check times out
	CreditReportScreeningSkipReason_SKIP_REASON_PRESENCE_CHECK_API_MAX_RETRIES CreditReportScreeningSkipReason = 2
	// credit report screening is skipped between defined time interval conveyed by vendor
	CreditReportScreeningSkipReason_SKIP_REASON_VENDOR_API_DOWNTIME CreditReportScreeningSkipReason = 3
)

// Enum value maps for CreditReportScreeningSkipReason.
var (
	CreditReportScreeningSkipReason_name = map[int32]string{
		0: "SKIP_REASON_UNSPECIFIED",
		1: "SKIP_REASON_FLAG_OFF",
		2: "SKIP_REASON_PRESENCE_CHECK_API_MAX_RETRIES",
		3: "SKIP_REASON_VENDOR_API_DOWNTIME",
	}
	CreditReportScreeningSkipReason_value = map[string]int32{
		"SKIP_REASON_UNSPECIFIED":                    0,
		"SKIP_REASON_FLAG_OFF":                       1,
		"SKIP_REASON_PRESENCE_CHECK_API_MAX_RETRIES": 2,
		"SKIP_REASON_VENDOR_API_DOWNTIME":            3,
	}
)

func (x CreditReportScreeningSkipReason) Enum() *CreditReportScreeningSkipReason {
	p := new(CreditReportScreeningSkipReason)
	*p = x
	return p
}

func (x CreditReportScreeningSkipReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreditReportScreeningSkipReason) Descriptor() protoreflect.EnumDescriptor {
	return file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[8].Descriptor()
}

func (CreditReportScreeningSkipReason) Type() protoreflect.EnumType {
	return &file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[8]
}

func (x CreditReportScreeningSkipReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreditReportScreeningSkipReason.Descriptor instead.
func (CreditReportScreeningSkipReason) EnumDescriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{8}
}

type EmploymentVerificationSuccessReason int32

const (
	EmploymentVerificationSuccessReason_EMPLOYMENT_VERIFICATION_SUCCESS_REASON_UNSPECIFIED EmploymentVerificationSuccessReason = 0
	// we were able to successfully verify epf data
	EmploymentVerificationSuccessReason_EPF_DATA_VERIFIED EmploymentVerificationSuccessReason = 1
	// we were able to successfully verify domain name in personal profile link
	EmploymentVerificationSuccessReason_DOMAIN_NAME_VERIFIED EmploymentVerificationSuccessReason = 2
	// employment verification consumer process timed out.
	EmploymentVerificationSuccessReason_VERIFICATION_PROCESS_TIMED_OUT EmploymentVerificationSuccessReason = 3
	// company selected by the user is present in MCA DB but is not registered with epfo
	EmploymentVerificationSuccessReason_PASS_NON_EPFO_REG_COMPANY EmploymentVerificationSuccessReason = 4
	// employment verification is marked successful if user is a referee
	EmploymentVerificationSuccessReason_ACCEPT_INAPP_REFEREE EmploymentVerificationSuccessReason = 5
	// employment verification is marked successful when user is subjected to employee declaration only - to collect employment data
	EmploymentVerificationSuccessReason_EMPLOYMENT_DECLARATION_ONLY EmploymentVerificationSuccessReason = 6
	// employment verification is marked successful for iOS users based on ActionOnEmploymentVerificationFailForIOS config flag
	EmploymentVerificationSuccessReason_ACCEPT_IOS_USER EmploymentVerificationSuccessReason = 7
	// employment verification is marked successful for Android users based on ActionOnEmploymentVerificationFailForAndroid config flag
	EmploymentVerificationSuccessReason_ACCEPT_ANDROID_PROD_MOVEMENT EmploymentVerificationSuccessReason = 8
)

// Enum value maps for EmploymentVerificationSuccessReason.
var (
	EmploymentVerificationSuccessReason_name = map[int32]string{
		0: "EMPLOYMENT_VERIFICATION_SUCCESS_REASON_UNSPECIFIED",
		1: "EPF_DATA_VERIFIED",
		2: "DOMAIN_NAME_VERIFIED",
		3: "VERIFICATION_PROCESS_TIMED_OUT",
		4: "PASS_NON_EPFO_REG_COMPANY",
		5: "ACCEPT_INAPP_REFEREE",
		6: "EMPLOYMENT_DECLARATION_ONLY",
		7: "ACCEPT_IOS_USER",
		8: "ACCEPT_ANDROID_PROD_MOVEMENT",
	}
	EmploymentVerificationSuccessReason_value = map[string]int32{
		"EMPLOYMENT_VERIFICATION_SUCCESS_REASON_UNSPECIFIED": 0,
		"EPF_DATA_VERIFIED":              1,
		"DOMAIN_NAME_VERIFIED":           2,
		"VERIFICATION_PROCESS_TIMED_OUT": 3,
		"PASS_NON_EPFO_REG_COMPANY":      4,
		"ACCEPT_INAPP_REFEREE":           5,
		"EMPLOYMENT_DECLARATION_ONLY":    6,
		"ACCEPT_IOS_USER":                7,
		"ACCEPT_ANDROID_PROD_MOVEMENT":   8,
	}
)

func (x EmploymentVerificationSuccessReason) Enum() *EmploymentVerificationSuccessReason {
	p := new(EmploymentVerificationSuccessReason)
	*p = x
	return p
}

func (x EmploymentVerificationSuccessReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EmploymentVerificationSuccessReason) Descriptor() protoreflect.EnumDescriptor {
	return file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[9].Descriptor()
}

func (EmploymentVerificationSuccessReason) Type() protoreflect.EnumType {
	return &file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[9]
}

func (x EmploymentVerificationSuccessReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EmploymentVerificationSuccessReason.Descriptor instead.
func (EmploymentVerificationSuccessReason) EnumDescriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{9}
}

type WorkEmailVerificationFailureReason int32

const (
	WorkEmailVerificationFailureReason_WORK_EMAIL_VERIFICATION_FAILURE_REASON_UNSPECIFIED WorkEmailVerificationFailureReason = 0
	// denotes attempts to send otp to email are exhausted
	WorkEmailVerificationFailureReason_WORK_EMAIL_VERIFICATION_FAILURE_REASON_SEND_OTP_ATTEMPTS_EXHAUSTED WorkEmailVerificationFailureReason = 1
	// denotes attempts to verify otp are exhausted
	WorkEmailVerificationFailureReason_WORK_EMAIL_VERIFICATION_FAILURE_REASON_VERIFY_OTP_ATTEMPTS_EXHAUSTED WorkEmailVerificationFailureReason = 2
	// denotes invalid email domain entered by the user
	WorkEmailVerificationFailureReason_WORK_EMAIL_VERIFICATION_FAILURE_REASON_INVALID_EMAIL_DOMAIN WorkEmailVerificationFailureReason = 3
	// denotes record with provided CIN not found at vendor
	WorkEmailVerificationFailureReason_WORK_EMAIL_VERIFICATION_FAILURE_REASON_COMPANY_EMAIL_NOT_FOUND WorkEmailVerificationFailureReason = 4
	// denotes failure due to vendor api attempts exhaustion
	WorkEmailVerificationFailureReason_WORK_EMAIL_VERIFICATION_FAILURE_REASON_VENDOR_API_ATTEMPTS_EXHAUSTED WorkEmailVerificationFailureReason = 5
)

// Enum value maps for WorkEmailVerificationFailureReason.
var (
	WorkEmailVerificationFailureReason_name = map[int32]string{
		0: "WORK_EMAIL_VERIFICATION_FAILURE_REASON_UNSPECIFIED",
		1: "WORK_EMAIL_VERIFICATION_FAILURE_REASON_SEND_OTP_ATTEMPTS_EXHAUSTED",
		2: "WORK_EMAIL_VERIFICATION_FAILURE_REASON_VERIFY_OTP_ATTEMPTS_EXHAUSTED",
		3: "WORK_EMAIL_VERIFICATION_FAILURE_REASON_INVALID_EMAIL_DOMAIN",
		4: "WORK_EMAIL_VERIFICATION_FAILURE_REASON_COMPANY_EMAIL_NOT_FOUND",
		5: "WORK_EMAIL_VERIFICATION_FAILURE_REASON_VENDOR_API_ATTEMPTS_EXHAUSTED",
	}
	WorkEmailVerificationFailureReason_value = map[string]int32{
		"WORK_EMAIL_VERIFICATION_FAILURE_REASON_UNSPECIFIED":                   0,
		"WORK_EMAIL_VERIFICATION_FAILURE_REASON_SEND_OTP_ATTEMPTS_EXHAUSTED":   1,
		"WORK_EMAIL_VERIFICATION_FAILURE_REASON_VERIFY_OTP_ATTEMPTS_EXHAUSTED": 2,
		"WORK_EMAIL_VERIFICATION_FAILURE_REASON_INVALID_EMAIL_DOMAIN":          3,
		"WORK_EMAIL_VERIFICATION_FAILURE_REASON_COMPANY_EMAIL_NOT_FOUND":       4,
		"WORK_EMAIL_VERIFICATION_FAILURE_REASON_VENDOR_API_ATTEMPTS_EXHAUSTED": 5,
	}
)

func (x WorkEmailVerificationFailureReason) Enum() *WorkEmailVerificationFailureReason {
	p := new(WorkEmailVerificationFailureReason)
	*p = x
	return p
}

func (x WorkEmailVerificationFailureReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WorkEmailVerificationFailureReason) Descriptor() protoreflect.EnumDescriptor {
	return file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[10].Descriptor()
}

func (WorkEmailVerificationFailureReason) Type() protoreflect.EnumType {
	return &file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[10]
}

func (x WorkEmailVerificationFailureReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WorkEmailVerificationFailureReason.Descriptor instead.
func (WorkEmailVerificationFailureReason) EnumDescriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{10}
}

// reason why credit report verification was marked success
type CreditReportVerificationSuccessReason int32

const (
	CreditReportVerificationSuccessReason_CREDIT_REPORT_VERIFICACTION_SUCCESS_REASON_UNSPECIFIED CreditReportVerificationSuccessReason = 0
	// cb verification stage is success if report verification retries exhausts
	CreditReportVerificationSuccessReason_CREDIT_REPORT_VERIFICACTION_SUCCESS_REASON_API_MAX_RETRIES CreditReportVerificationSuccessReason = 1
	// cb verification stage is success if vendor responds with report not found on attempt to download report
	CreditReportVerificationSuccessReason_CREDIT_REPORT_VERIFICACTION_SUCCESS_REASON_REPORT_NOT_FOUND CreditReportVerificationSuccessReason = 2
	// cb verification stage is success if report was downloaded and verified successfully
	CreditReportVerificationSuccessReason_CREDIT_REPORT_VERIFICACTION_SUCCESS_REASON_CREDIT_REPORT_VERIFIED CreditReportVerificationSuccessReason = 3
)

// Enum value maps for CreditReportVerificationSuccessReason.
var (
	CreditReportVerificationSuccessReason_name = map[int32]string{
		0: "CREDIT_REPORT_VERIFICACTION_SUCCESS_REASON_UNSPECIFIED",
		1: "CREDIT_REPORT_VERIFICACTION_SUCCESS_REASON_API_MAX_RETRIES",
		2: "CREDIT_REPORT_VERIFICACTION_SUCCESS_REASON_REPORT_NOT_FOUND",
		3: "CREDIT_REPORT_VERIFICACTION_SUCCESS_REASON_CREDIT_REPORT_VERIFIED",
	}
	CreditReportVerificationSuccessReason_value = map[string]int32{
		"CREDIT_REPORT_VERIFICACTION_SUCCESS_REASON_UNSPECIFIED":            0,
		"CREDIT_REPORT_VERIFICACTION_SUCCESS_REASON_API_MAX_RETRIES":        1,
		"CREDIT_REPORT_VERIFICACTION_SUCCESS_REASON_REPORT_NOT_FOUND":       2,
		"CREDIT_REPORT_VERIFICACTION_SUCCESS_REASON_CREDIT_REPORT_VERIFIED": 3,
	}
)

func (x CreditReportVerificationSuccessReason) Enum() *CreditReportVerificationSuccessReason {
	p := new(CreditReportVerificationSuccessReason)
	*p = x
	return p
}

func (x CreditReportVerificationSuccessReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreditReportVerificationSuccessReason) Descriptor() protoreflect.EnumDescriptor {
	return file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[11].Descriptor()
}

func (CreditReportVerificationSuccessReason) Type() protoreflect.EnumType {
	return &file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[11]
}

func (x CreditReportVerificationSuccessReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreditReportVerificationSuccessReason.Descriptor instead.
func (CreditReportVerificationSuccessReason) EnumDescriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{11}
}

type PanValidationSuspendReason int32

const (
	PanValidationSuspendReason_PAN_VALIDATION_SUSPEND_REASON_UNSPECIFIED PanValidationSuspendReason = 0
	// denotes when a user is blocked in pan validation stage due to max retries by user
	PanValidationSuspendReason_PAN_VALIDATION_SUSPEND_REASON_USER_MAX_RETRIES PanValidationSuspendReason = 1
	// denotes when a user is blocked in pan validation stage due to vendor rate limiting
	PanValidationSuspendReason_PAN_VALIDATION_SUSPEND_REASON_VENDOR_RATE_LIMIT_REACHED PanValidationSuspendReason = 2
)

// Enum value maps for PanValidationSuspendReason.
var (
	PanValidationSuspendReason_name = map[int32]string{
		0: "PAN_VALIDATION_SUSPEND_REASON_UNSPECIFIED",
		1: "PAN_VALIDATION_SUSPEND_REASON_USER_MAX_RETRIES",
		2: "PAN_VALIDATION_SUSPEND_REASON_VENDOR_RATE_LIMIT_REACHED",
	}
	PanValidationSuspendReason_value = map[string]int32{
		"PAN_VALIDATION_SUSPEND_REASON_UNSPECIFIED":               0,
		"PAN_VALIDATION_SUSPEND_REASON_USER_MAX_RETRIES":          1,
		"PAN_VALIDATION_SUSPEND_REASON_VENDOR_RATE_LIMIT_REACHED": 2,
	}
)

func (x PanValidationSuspendReason) Enum() *PanValidationSuspendReason {
	p := new(PanValidationSuspendReason)
	*p = x
	return p
}

func (x PanValidationSuspendReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PanValidationSuspendReason) Descriptor() protoreflect.EnumDescriptor {
	return file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[12].Descriptor()
}

func (PanValidationSuspendReason) Type() protoreflect.EnumType {
	return &file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[12]
}

func (x PanValidationSuspendReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PanValidationSuspendReason.Descriptor instead.
func (PanValidationSuspendReason) EnumDescriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{12}
}

// Linkedin Verification is expected to evolve over time - vendor might change, validation condition might change
// This enum represents roughly what validation condition was used to pass or reject user
type LinkedInValidationType int32

const (
	LinkedInValidationType_LINKEDIN_VALIDATION_TYPE_UNSPECIFIED LinkedInValidationType = 0
	// This validation include user's name match and company name match with LinkedIn profile
	LinkedInValidationType_LINKEDIN_VALIDATION_SEON_USER_NAME_COMPANY_NAME_MATCH LinkedInValidationType = 1
)

// Enum value maps for LinkedInValidationType.
var (
	LinkedInValidationType_name = map[int32]string{
		0: "LINKEDIN_VALIDATION_TYPE_UNSPECIFIED",
		1: "LINKEDIN_VALIDATION_SEON_USER_NAME_COMPANY_NAME_MATCH",
	}
	LinkedInValidationType_value = map[string]int32{
		"LINKEDIN_VALIDATION_TYPE_UNSPECIFIED":                  0,
		"LINKEDIN_VALIDATION_SEON_USER_NAME_COMPANY_NAME_MATCH": 1,
	}
)

func (x LinkedInValidationType) Enum() *LinkedInValidationType {
	p := new(LinkedInValidationType)
	*p = x
	return p
}

func (x LinkedInValidationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LinkedInValidationType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[13].Descriptor()
}

func (LinkedInValidationType) Type() protoreflect.EnumType {
	return &file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[13]
}

func (x LinkedInValidationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LinkedInValidationType.Descriptor instead.
func (LinkedInValidationType) EnumDescriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{13}
}

type GSTINPresenceCheckSuccessReason int32

const (
	GSTINPresenceCheckSuccessReason_GSTIN_PRESENCE_CHECK_SUCCESS_REASON_UNSPECIFIED GSTINPresenceCheckSuccessReason = 0
	// user gstin found at vendor
	GSTINPresenceCheckSuccessReason_GSTIN_PRESENCE_CHECK_SUCCESS_REASON_GSTIN_FOUND GSTINPresenceCheckSuccessReason = 1
	// api failure
	GSTINPresenceCheckSuccessReason_GSTIN_PRESENCE_CHECK_SUCCESS_REASON_API_FAILURE GSTINPresenceCheckSuccessReason = 2
	// retry exhausted
	GSTINPresenceCheckSuccessReason_GSTIN_PRESENCE_CHECK_SUCCESS_REASON_RETRIES_EXHAUSTED GSTINPresenceCheckSuccessReason = 3
)

// Enum value maps for GSTINPresenceCheckSuccessReason.
var (
	GSTINPresenceCheckSuccessReason_name = map[int32]string{
		0: "GSTIN_PRESENCE_CHECK_SUCCESS_REASON_UNSPECIFIED",
		1: "GSTIN_PRESENCE_CHECK_SUCCESS_REASON_GSTIN_FOUND",
		2: "GSTIN_PRESENCE_CHECK_SUCCESS_REASON_API_FAILURE",
		3: "GSTIN_PRESENCE_CHECK_SUCCESS_REASON_RETRIES_EXHAUSTED",
	}
	GSTINPresenceCheckSuccessReason_value = map[string]int32{
		"GSTIN_PRESENCE_CHECK_SUCCESS_REASON_UNSPECIFIED":       0,
		"GSTIN_PRESENCE_CHECK_SUCCESS_REASON_GSTIN_FOUND":       1,
		"GSTIN_PRESENCE_CHECK_SUCCESS_REASON_API_FAILURE":       2,
		"GSTIN_PRESENCE_CHECK_SUCCESS_REASON_RETRIES_EXHAUSTED": 3,
	}
)

func (x GSTINPresenceCheckSuccessReason) Enum() *GSTINPresenceCheckSuccessReason {
	p := new(GSTINPresenceCheckSuccessReason)
	*p = x
	return p
}

func (x GSTINPresenceCheckSuccessReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GSTINPresenceCheckSuccessReason) Descriptor() protoreflect.EnumDescriptor {
	return file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[14].Descriptor()
}

func (GSTINPresenceCheckSuccessReason) Type() protoreflect.EnumType {
	return &file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[14]
}

func (x GSTINPresenceCheckSuccessReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GSTINPresenceCheckSuccessReason.Descriptor instead.
func (GSTINPresenceCheckSuccessReason) EnumDescriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{14}
}

type VKYCOption int32

const (
	VKYCOption_VKYC_OPTION_UNSPECIFIED VKYCOption = 0
	// user subjected to vkyc during onboarding due to probability enabling
	VKYCOption_VKYC_OPTION_ONBOARDING VKYCOption = 1
	// user subjected to vkyc during onboarding due to having L, S or O as prefix in ckyc number
	VKYCOption_VKYC_OPTION_LSO VKYCOption = 2
	// user subjected to vkyc during onboarding due to having CKYC O as prefix in ckyc number
	VKYCOption_VKYC_OPTION_CKYC_O VKYCOption = 3
	// user subjected to vkyc during onboarding by force due to employment type student.
	VKYCOption_VKYC_OPTION_STUDENT VKYCOption = 4
	// Partial KYC dedupe user subjected to VKYC
	VKYCOption_VKYC_OPTION_PARTIAL_KYC_DEDUPE VKYCOption = 5
	// Users with EKYC & Onboarding Number Mismatch
	VKYCOption_VKYC_OPTION_EKYC_NUMBER_MISMATCH VKYCOption = 6
	// Users with Affluence Score 5 and from Tier 3 cities
	VKYCOption_VKYC_OPTION_LOW_QUALITY_USERS VKYCOption = 7
	// Fi lite users
	VKYCOption_VKYC_OPTION_FI_LITE_USERS            VKYCOption = 8
	VKYCOption_VKYC_OPTION_CLOSED_ACCOUNT_REOPENING VKYCOption = 9
	// Fi lite users continuing CC onboarding
	VKYCOption_VKYC_OPTION_FI_LITE_CC_USERS        VKYCOption = 10
	VKYCOption_VKYC_OPTION_NON_RESIDENT_ONBOARDING VKYCOption = 11
	VKYCOption_VKYC_OPTION_FEDERAL_LOANS           VKYCOption = 12
)

// Enum value maps for VKYCOption.
var (
	VKYCOption_name = map[int32]string{
		0:  "VKYC_OPTION_UNSPECIFIED",
		1:  "VKYC_OPTION_ONBOARDING",
		2:  "VKYC_OPTION_LSO",
		3:  "VKYC_OPTION_CKYC_O",
		4:  "VKYC_OPTION_STUDENT",
		5:  "VKYC_OPTION_PARTIAL_KYC_DEDUPE",
		6:  "VKYC_OPTION_EKYC_NUMBER_MISMATCH",
		7:  "VKYC_OPTION_LOW_QUALITY_USERS",
		8:  "VKYC_OPTION_FI_LITE_USERS",
		9:  "VKYC_OPTION_CLOSED_ACCOUNT_REOPENING",
		10: "VKYC_OPTION_FI_LITE_CC_USERS",
		11: "VKYC_OPTION_NON_RESIDENT_ONBOARDING",
		12: "VKYC_OPTION_FEDERAL_LOANS",
	}
	VKYCOption_value = map[string]int32{
		"VKYC_OPTION_UNSPECIFIED":              0,
		"VKYC_OPTION_ONBOARDING":               1,
		"VKYC_OPTION_LSO":                      2,
		"VKYC_OPTION_CKYC_O":                   3,
		"VKYC_OPTION_STUDENT":                  4,
		"VKYC_OPTION_PARTIAL_KYC_DEDUPE":       5,
		"VKYC_OPTION_EKYC_NUMBER_MISMATCH":     6,
		"VKYC_OPTION_LOW_QUALITY_USERS":        7,
		"VKYC_OPTION_FI_LITE_USERS":            8,
		"VKYC_OPTION_CLOSED_ACCOUNT_REOPENING": 9,
		"VKYC_OPTION_FI_LITE_CC_USERS":         10,
		"VKYC_OPTION_NON_RESIDENT_ONBOARDING":  11,
		"VKYC_OPTION_FEDERAL_LOANS":            12,
	}
)

func (x VKYCOption) Enum() *VKYCOption {
	p := new(VKYCOption)
	*p = x
	return p
}

func (x VKYCOption) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VKYCOption) Descriptor() protoreflect.EnumDescriptor {
	return file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[15].Descriptor()
}

func (VKYCOption) Type() protoreflect.EnumType {
	return &file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[15]
}

func (x VKYCOption) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VKYCOption.Descriptor instead.
func (VKYCOption) EnumDescriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{15}
}

type HeuristicPassReason int32

const (
	HeuristicPassReason_HEURISTIC_PASS_REASON_UNSPECIFIED HeuristicPassReason = 0
	// User was passed screener based on their device model and manufacturer
	HeuristicPassReason_HEURISTIC_PASS_REASON_DEVICE_MODEL_CHECK HeuristicPassReason = 1
	// User was passed based on their age
	HeuristicPassReason_HEURISTIC_PASS_REASON_USER_AGE HeuristicPassReason = 2
	// User was passed screener based on their phone number billing type
	HeuristicPassReason_HEURISTIC_PASS_REASON_PHONE_BILLING_TYPE HeuristicPassReason = 3
)

// Enum value maps for HeuristicPassReason.
var (
	HeuristicPassReason_name = map[int32]string{
		0: "HEURISTIC_PASS_REASON_UNSPECIFIED",
		1: "HEURISTIC_PASS_REASON_DEVICE_MODEL_CHECK",
		2: "HEURISTIC_PASS_REASON_USER_AGE",
		3: "HEURISTIC_PASS_REASON_PHONE_BILLING_TYPE",
	}
	HeuristicPassReason_value = map[string]int32{
		"HEURISTIC_PASS_REASON_UNSPECIFIED":        0,
		"HEURISTIC_PASS_REASON_DEVICE_MODEL_CHECK": 1,
		"HEURISTIC_PASS_REASON_USER_AGE":           2,
		"HEURISTIC_PASS_REASON_PHONE_BILLING_TYPE": 3,
	}
)

func (x HeuristicPassReason) Enum() *HeuristicPassReason {
	p := new(HeuristicPassReason)
	*p = x
	return p
}

func (x HeuristicPassReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HeuristicPassReason) Descriptor() protoreflect.EnumDescriptor {
	return file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[16].Descriptor()
}

func (HeuristicPassReason) Type() protoreflect.EnumType {
	return &file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[16]
}

func (x HeuristicPassReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HeuristicPassReason.Descriptor instead.
func (HeuristicPassReason) EnumDescriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{16}
}

// Feature names of various onboarding journeys
type Feature int32

const (
	Feature_FEATURE_UNSPECIFIED           Feature = 0
	Feature_FEATURE_SA                    Feature = 1 // Savings Account
	Feature_FEATURE_FI_LITE               Feature = 2
	Feature_FEATURE_CC                    Feature = 3 // Credit Card
	Feature_FEATURE_PL                    Feature = 4 // Personal Loans
	Feature_FEATURE_UPI_TPAP              Feature = 5
	Feature_FEATURE_NON_RESIDENT_SA       Feature = 6 // Savings Account for NRI
	Feature_FEATURE_NON_RESIDENT_SA_US    Feature = 7 // Savings Account for US NRI
	Feature_FEATURE_WEALTH_ANALYSER       Feature = 8 // Wealth Analyser onboarding journey
	Feature_FEATURE_NON_RESIDENT_SA_QATAR Feature = 9 // Savings Account for Qatar NRI
)

// Enum value maps for Feature.
var (
	Feature_name = map[int32]string{
		0: "FEATURE_UNSPECIFIED",
		1: "FEATURE_SA",
		2: "FEATURE_FI_LITE",
		3: "FEATURE_CC",
		4: "FEATURE_PL",
		5: "FEATURE_UPI_TPAP",
		6: "FEATURE_NON_RESIDENT_SA",
		7: "FEATURE_NON_RESIDENT_SA_US",
		8: "FEATURE_WEALTH_ANALYSER",
		9: "FEATURE_NON_RESIDENT_SA_QATAR",
	}
	Feature_value = map[string]int32{
		"FEATURE_UNSPECIFIED":           0,
		"FEATURE_SA":                    1,
		"FEATURE_FI_LITE":               2,
		"FEATURE_CC":                    3,
		"FEATURE_PL":                    4,
		"FEATURE_UPI_TPAP":              5,
		"FEATURE_NON_RESIDENT_SA":       6,
		"FEATURE_NON_RESIDENT_SA_US":    7,
		"FEATURE_WEALTH_ANALYSER":       8,
		"FEATURE_NON_RESIDENT_SA_QATAR": 9,
	}
)

func (x Feature) Enum() *Feature {
	p := new(Feature)
	*p = x
	return p
}

func (x Feature) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Feature) Descriptor() protoreflect.EnumDescriptor {
	return file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[17].Descriptor()
}

func (Feature) Type() protoreflect.EnumType {
	return &file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[17]
}

func (x Feature) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Feature.Descriptor instead.
func (Feature) EnumDescriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{17}
}

// Source through which Fi Lite was being accessed
type FiLiteSource int32

const (
	FiLiteSource_FI_LITE_SOURCE_UNSPECIFIED                  FiLiteSource = 0
	FiLiteSource_FI_LITE_SOURCE_PAN_DOB                      FiLiteSource = 1
	FiLiteSource_FI_LITE_SOURCE_SCREENER                     FiLiteSource = 2
	FiLiteSource_FI_LITE_SOURCE_KYC_DEDUPE                   FiLiteSource = 3
	FiLiteSource_FI_LITE_SOURCE_DEVICE_REGISTRATION          FiLiteSource = 4
	FiLiteSource_FI_LITE_SOURCE_EKYC_NAME_DOB_VALIDATION     FiLiteSource = 5
	FiLiteSource_FI_LITE_SOURCE_INTENT_SELECTION             FiLiteSource = 6
	FiLiteSource_FI_LITE_SOURCE_AFFLUENCE_CLASS_7            FiLiteSource = 7
	FiLiteSource_FI_LITE_SOURCE_SA_CUSTOMER_CREATION_FAILURE FiLiteSource = 8
	FiLiteSource_FI_LITE_SOURCE_DIRECT_TO_FI_LITE            FiLiteSource = 9
	FiLiteSource_FI_LITE_SOURCE_STUCK_USER                   FiLiteSource = 10
	FiLiteSource_FI_LITE_SOURCE_DIRECT_TO_HOME               FiLiteSource = 11
	// This is used when a user has dropped off from onboarding and we manually move them to Wealth Analyser
	// before calling them back on the app
	FiLiteSource_FI_LITE_SOURCE_DROPPED_OFF_USER_TO_WEALTH_ANALYSER FiLiteSource = 12
	// This is marked when user starts their eligibility check for loans from the intent screen
	// this is done to get the user to land on home when they come back on the app
	FiLiteSource_FI_LITE_SOURCE_LOANS_ELIGIBILITY_CHECK FiLiteSource = 13
	// this is used when user didn't complete their SA onboarding journey within a specified number of days
	FiLiteSource_FI_LITE_SOURCE_SA_ONBOARDING_EXPIRED FiLiteSource = 14
	// This is marked when user starts their credit card onboarding via SDK
	// this is done to get the user to land on home when they come back on the app
	FiLiteSource_FI_LITE_SOURCE_CREDIT_CARD_SDK_ONBOARDING FiLiteSource = 15
)

// Enum value maps for FiLiteSource.
var (
	FiLiteSource_name = map[int32]string{
		0:  "FI_LITE_SOURCE_UNSPECIFIED",
		1:  "FI_LITE_SOURCE_PAN_DOB",
		2:  "FI_LITE_SOURCE_SCREENER",
		3:  "FI_LITE_SOURCE_KYC_DEDUPE",
		4:  "FI_LITE_SOURCE_DEVICE_REGISTRATION",
		5:  "FI_LITE_SOURCE_EKYC_NAME_DOB_VALIDATION",
		6:  "FI_LITE_SOURCE_INTENT_SELECTION",
		7:  "FI_LITE_SOURCE_AFFLUENCE_CLASS_7",
		8:  "FI_LITE_SOURCE_SA_CUSTOMER_CREATION_FAILURE",
		9:  "FI_LITE_SOURCE_DIRECT_TO_FI_LITE",
		10: "FI_LITE_SOURCE_STUCK_USER",
		11: "FI_LITE_SOURCE_DIRECT_TO_HOME",
		12: "FI_LITE_SOURCE_DROPPED_OFF_USER_TO_WEALTH_ANALYSER",
		13: "FI_LITE_SOURCE_LOANS_ELIGIBILITY_CHECK",
		14: "FI_LITE_SOURCE_SA_ONBOARDING_EXPIRED",
		15: "FI_LITE_SOURCE_CREDIT_CARD_SDK_ONBOARDING",
	}
	FiLiteSource_value = map[string]int32{
		"FI_LITE_SOURCE_UNSPECIFIED":                         0,
		"FI_LITE_SOURCE_PAN_DOB":                             1,
		"FI_LITE_SOURCE_SCREENER":                            2,
		"FI_LITE_SOURCE_KYC_DEDUPE":                          3,
		"FI_LITE_SOURCE_DEVICE_REGISTRATION":                 4,
		"FI_LITE_SOURCE_EKYC_NAME_DOB_VALIDATION":            5,
		"FI_LITE_SOURCE_INTENT_SELECTION":                    6,
		"FI_LITE_SOURCE_AFFLUENCE_CLASS_7":                   7,
		"FI_LITE_SOURCE_SA_CUSTOMER_CREATION_FAILURE":        8,
		"FI_LITE_SOURCE_DIRECT_TO_FI_LITE":                   9,
		"FI_LITE_SOURCE_STUCK_USER":                          10,
		"FI_LITE_SOURCE_DIRECT_TO_HOME":                      11,
		"FI_LITE_SOURCE_DROPPED_OFF_USER_TO_WEALTH_ANALYSER": 12,
		"FI_LITE_SOURCE_LOANS_ELIGIBILITY_CHECK":             13,
		"FI_LITE_SOURCE_SA_ONBOARDING_EXPIRED":               14,
		"FI_LITE_SOURCE_CREDIT_CARD_SDK_ONBOARDING":          15,
	}
)

func (x FiLiteSource) Enum() *FiLiteSource {
	p := new(FiLiteSource)
	*p = x
	return p
}

func (x FiLiteSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FiLiteSource) Descriptor() protoreflect.EnumDescriptor {
	return file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[18].Descriptor()
}

func (FiLiteSource) Type() protoreflect.EnumType {
	return &file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[18]
}

func (x FiLiteSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FiLiteSource.Descriptor instead.
func (FiLiteSource) EnumDescriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{18}
}

// FeatureStatus enum is used to denote the current status of the feature
type FeatureStatus int32

const (
	FeatureStatus_FEATURE_STATUS_UNSPECIFIED FeatureStatus = 0
	// Onboarding is in progress for the feature, this shall be used when no user input is needed, and it is in progress
	FeatureStatus_FEATURE_STATUS_ONBOARDING_IN_PROGRESS FeatureStatus = 1
	// Failure is used when onboarding has failed in between
	FeatureStatus_FEATURE_STATUS_ONBOARDING_FAILURE FeatureStatus = 2
	// Active is used when onboarding is completed for a feature, and it is ready to use
	FeatureStatus_FEATURE_STATUS_ACTIVE FeatureStatus = 3
	// Inactive is used when onboarding is successfully complete, but the feature is unavailable for use.
	// e.g. SA got closed
	FeatureStatus_FEATURE_STATUS_INACTIVE FeatureStatus = 4
	// Rejected is used when the feature has been declined, e.g., when a personal loan is rejected for the user.
	FeatureStatus_FEATURE_STATUS_REJECTED FeatureStatus = 5
)

// Enum value maps for FeatureStatus.
var (
	FeatureStatus_name = map[int32]string{
		0: "FEATURE_STATUS_UNSPECIFIED",
		1: "FEATURE_STATUS_ONBOARDING_IN_PROGRESS",
		2: "FEATURE_STATUS_ONBOARDING_FAILURE",
		3: "FEATURE_STATUS_ACTIVE",
		4: "FEATURE_STATUS_INACTIVE",
		5: "FEATURE_STATUS_REJECTED",
	}
	FeatureStatus_value = map[string]int32{
		"FEATURE_STATUS_UNSPECIFIED":            0,
		"FEATURE_STATUS_ONBOARDING_IN_PROGRESS": 1,
		"FEATURE_STATUS_ONBOARDING_FAILURE":     2,
		"FEATURE_STATUS_ACTIVE":                 3,
		"FEATURE_STATUS_INACTIVE":               4,
		"FEATURE_STATUS_REJECTED":               5,
	}
)

func (x FeatureStatus) Enum() *FeatureStatus {
	p := new(FeatureStatus)
	*p = x
	return p
}

func (x FeatureStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FeatureStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[19].Descriptor()
}

func (FeatureStatus) Type() protoreflect.EnumType {
	return &file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[19]
}

func (x FeatureStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FeatureStatus.Descriptor instead.
func (FeatureStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{19}
}

type PassportVerificationStatus int32

const (
	PassportVerificationStatus_PASSPORT_VERIFICATION_STATUS_UNSPECIFIED      PassportVerificationStatus = 0
	PassportVerificationStatus_PASSPORT_VERIFICATION_STATUS_FRONT_DOWNLOADED PassportVerificationStatus = 1
	PassportVerificationStatus_PASSPORT_VERIFICATION_STATUS_BACK_DOWNLOADED  PassportVerificationStatus = 2
	PassportVerificationStatus_PASSPORT_VERIFICATION_STATUS_OCR_COMPLETED    PassportVerificationStatus = 3
	PassportVerificationStatus_PASSPORT_VERIFICATION_STATUS_SUCCESS          PassportVerificationStatus = 4
	PassportVerificationStatus_PASSPORT_VERIFICATION_STATUS_FAILED           PassportVerificationStatus = 5
	// data from passport is confirmed by user
	PassportVerificationStatus_PASSPORT_VERIFICATION_STATUS_DATA_CONFIRMED PassportVerificationStatus = 6
	// The passport is under manual review.
	// This applies to cases where the passport was issued outside India.
	// Manual verification is conducted using the ARN from the Global Passport Seva Kendra website.
	PassportVerificationStatus_PASSPORT_VERIFICATION_STATUS_MANUAL_REVIEW PassportVerificationStatus = 7
)

// Enum value maps for PassportVerificationStatus.
var (
	PassportVerificationStatus_name = map[int32]string{
		0: "PASSPORT_VERIFICATION_STATUS_UNSPECIFIED",
		1: "PASSPORT_VERIFICATION_STATUS_FRONT_DOWNLOADED",
		2: "PASSPORT_VERIFICATION_STATUS_BACK_DOWNLOADED",
		3: "PASSPORT_VERIFICATION_STATUS_OCR_COMPLETED",
		4: "PASSPORT_VERIFICATION_STATUS_SUCCESS",
		5: "PASSPORT_VERIFICATION_STATUS_FAILED",
		6: "PASSPORT_VERIFICATION_STATUS_DATA_CONFIRMED",
		7: "PASSPORT_VERIFICATION_STATUS_MANUAL_REVIEW",
	}
	PassportVerificationStatus_value = map[string]int32{
		"PASSPORT_VERIFICATION_STATUS_UNSPECIFIED":      0,
		"PASSPORT_VERIFICATION_STATUS_FRONT_DOWNLOADED": 1,
		"PASSPORT_VERIFICATION_STATUS_BACK_DOWNLOADED":  2,
		"PASSPORT_VERIFICATION_STATUS_OCR_COMPLETED":    3,
		"PASSPORT_VERIFICATION_STATUS_SUCCESS":          4,
		"PASSPORT_VERIFICATION_STATUS_FAILED":           5,
		"PASSPORT_VERIFICATION_STATUS_DATA_CONFIRMED":   6,
		"PASSPORT_VERIFICATION_STATUS_MANUAL_REVIEW":    7,
	}
)

func (x PassportVerificationStatus) Enum() *PassportVerificationStatus {
	p := new(PassportVerificationStatus)
	*p = x
	return p
}

func (x PassportVerificationStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PassportVerificationStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[20].Descriptor()
}

func (PassportVerificationStatus) Type() protoreflect.EnumType {
	return &file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[20]
}

func (x PassportVerificationStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PassportVerificationStatus.Descriptor instead.
func (PassportVerificationStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{20}
}

type PassportVerificationFailureReason int32

const (
	PassportVerificationFailureReason_PASSPORT_VERIFICATION_FAILURE_REASON_UNSPECIFIED PassportVerificationFailureReason = 0
	// passport issued outside India
	PassportVerificationFailureReason_PASSPORT_VERIFICATION_FAILURE_REASON_PASSPORT_ISSUED_OUTSIDE_INDIA PassportVerificationFailureReason = 1
	// passport name mismatch with passport seva kendra data
	PassportVerificationFailureReason_PASSPORT_VERIFICATION_FAILURE_REASON_PASSPORT_NAME_MISMATCH_WITH_PSK_DATA PassportVerificationFailureReason = 2
	// passport name mismatch with passport seva kendra data
	PassportVerificationFailureReason_PASSPORT_VERIFICATION_FAILURE_REASON_PASSPORT_NUMBER_MISMATCH_WITH_PSK_DATA PassportVerificationFailureReason = 3
	// passport data not found in passport seva kendra
	PassportVerificationFailureReason_PASSPORT_VERIFICATION_FAILURE_REASON_PASSPORT_DATA_NOT_FOUND_IN_PSK PassportVerificationFailureReason = 4
)

// Enum value maps for PassportVerificationFailureReason.
var (
	PassportVerificationFailureReason_name = map[int32]string{
		0: "PASSPORT_VERIFICATION_FAILURE_REASON_UNSPECIFIED",
		1: "PASSPORT_VERIFICATION_FAILURE_REASON_PASSPORT_ISSUED_OUTSIDE_INDIA",
		2: "PASSPORT_VERIFICATION_FAILURE_REASON_PASSPORT_NAME_MISMATCH_WITH_PSK_DATA",
		3: "PASSPORT_VERIFICATION_FAILURE_REASON_PASSPORT_NUMBER_MISMATCH_WITH_PSK_DATA",
		4: "PASSPORT_VERIFICATION_FAILURE_REASON_PASSPORT_DATA_NOT_FOUND_IN_PSK",
	}
	PassportVerificationFailureReason_value = map[string]int32{
		"PASSPORT_VERIFICATION_FAILURE_REASON_UNSPECIFIED":                            0,
		"PASSPORT_VERIFICATION_FAILURE_REASON_PASSPORT_ISSUED_OUTSIDE_INDIA":          1,
		"PASSPORT_VERIFICATION_FAILURE_REASON_PASSPORT_NAME_MISMATCH_WITH_PSK_DATA":   2,
		"PASSPORT_VERIFICATION_FAILURE_REASON_PASSPORT_NUMBER_MISMATCH_WITH_PSK_DATA": 3,
		"PASSPORT_VERIFICATION_FAILURE_REASON_PASSPORT_DATA_NOT_FOUND_IN_PSK":         4,
	}
)

func (x PassportVerificationFailureReason) Enum() *PassportVerificationFailureReason {
	p := new(PassportVerificationFailureReason)
	*p = x
	return p
}

func (x PassportVerificationFailureReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PassportVerificationFailureReason) Descriptor() protoreflect.EnumDescriptor {
	return file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[21].Descriptor()
}

func (PassportVerificationFailureReason) Type() protoreflect.EnumType {
	return &file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[21]
}

func (x PassportVerificationFailureReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PassportVerificationFailureReason.Descriptor instead.
func (PassportVerificationFailureReason) EnumDescriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{21}
}

type PreCustomerCreationMetadata_FailureReason int32

const (
	PreCustomerCreationMetadata_FAILURE_REASON_UNSPECIFIED          PreCustomerCreationMetadata_FailureReason = 0
	PreCustomerCreationMetadata_FAILURE_REASON_PAN_UNIQUENESS_CHECK PreCustomerCreationMetadata_FailureReason = 1
)

// Enum value maps for PreCustomerCreationMetadata_FailureReason.
var (
	PreCustomerCreationMetadata_FailureReason_name = map[int32]string{
		0: "FAILURE_REASON_UNSPECIFIED",
		1: "FAILURE_REASON_PAN_UNIQUENESS_CHECK",
	}
	PreCustomerCreationMetadata_FailureReason_value = map[string]int32{
		"FAILURE_REASON_UNSPECIFIED":          0,
		"FAILURE_REASON_PAN_UNIQUENESS_CHECK": 1,
	}
)

func (x PreCustomerCreationMetadata_FailureReason) Enum() *PreCustomerCreationMetadata_FailureReason {
	p := new(PreCustomerCreationMetadata_FailureReason)
	*p = x
	return p
}

func (x PreCustomerCreationMetadata_FailureReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PreCustomerCreationMetadata_FailureReason) Descriptor() protoreflect.EnumDescriptor {
	return file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[22].Descriptor()
}

func (PreCustomerCreationMetadata_FailureReason) Type() protoreflect.EnumType {
	return &file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[22]
}

func (x PreCustomerCreationMetadata_FailureReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PreCustomerCreationMetadata_FailureReason.Descriptor instead.
func (PreCustomerCreationMetadata_FailureReason) EnumDescriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{14, 0}
}

// eligibility status of the feature
type FeatureEligibility_Status int32

const (
	FeatureEligibility_STATUS_UNSPECIFIED FeatureEligibility_Status = 0
	// user is eligible for the feature
	FeatureEligibility_STATUS_PASSED FeatureEligibility_Status = 1
	// user is not eligible for the feature
	FeatureEligibility_STATUS_FAILED FeatureEligibility_Status = 2
	// additional data is required to evaluate
	FeatureEligibility_STATUS_UNKNOWN FeatureEligibility_Status = 3
)

// Enum value maps for FeatureEligibility_Status.
var (
	FeatureEligibility_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "STATUS_PASSED",
		2: "STATUS_FAILED",
		3: "STATUS_UNKNOWN",
	}
	FeatureEligibility_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"STATUS_PASSED":      1,
		"STATUS_FAILED":      2,
		"STATUS_UNKNOWN":     3,
	}
)

func (x FeatureEligibility_Status) Enum() *FeatureEligibility_Status {
	p := new(FeatureEligibility_Status)
	*p = x
	return p
}

func (x FeatureEligibility_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FeatureEligibility_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[23].Descriptor()
}

func (FeatureEligibility_Status) Type() protoreflect.EnumType {
	return &file_api_user_onboarding_internal_onboarding_details_proto_enumTypes[23]
}

func (x FeatureEligibility_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FeatureEligibility_Status.Descriptor instead.
func (FeatureEligibility_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{38, 0}
}

type OnboardingDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Actor id of the user
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Details regarding the states of stage, current stage are maintained in here
	StageDetails *StageDetails `protobuf:"bytes,2,opt,name=stage_details,json=stageDetails,proto3" json:"stage_details,omitempty"`
	// Parameter dump of all required parameter related to user account
	AccountInfo *AccountInformationInternal `protobuf:"bytes,3,opt,name=account_info,json=accountInfo,proto3" json:"account_info,omitempty"`
	// Parameter dump of all required parameter related to customer card
	CardInfo *CardInformationInternal `protobuf:"bytes,4,opt,name=card_info,json=cardInfo,proto3" json:"card_info,omitempty"`
	// Bank vendor with whom the user holds the account
	Vendor vendorgateway.Vendor `protobuf:"varint,5,opt,name=vendor,proto3,enum=vendorgateway.Vendor" json:"vendor,omitempty"`
	// onboarding id
	OnboardingId string `protobuf:"bytes,6,opt,name=onboarding_id,json=onboardingId,proto3" json:"onboarding_id,omitempty"`
	// User id
	UserId string `protobuf:"bytes,7,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// completed at to denote when onboarding was complete
	CompletedAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=completed_at,json=completedAt,proto3" json:"completed_at,omitempty"`
	// This will contain all the detailed information of different stage.
	// TODO (keerthana): account_info, and card_info to be migrated to this json.
	StageMetadata *StageMetadata `protobuf:"bytes,9,opt,name=stage_metadata,json=stageMetadata,proto3" json:"stage_metadata,omitempty"`
	// created_at to denote when onboarding was started
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// current onboarding stage of the user
	CurrentOnboardingStage OnboardingStage `protobuf:"varint,11,opt,name=current_onboarding_stage,json=currentOnboardingStage,proto3,enum=user.onboarding.OnboardingStage" json:"current_onboarding_stage,omitempty"`
	Feature                Feature         `protobuf:"varint,12,opt,name=feature,proto3,enum=user.onboarding.Feature" json:"feature,omitempty"`
	FiLiteDetails          *FiLiteDetails  `protobuf:"bytes,13,opt,name=fi_lite_details,json=fiLiteDetails,proto3" json:"fi_lite_details,omitempty"`
	FeatureDetails         *FeatureDetails `protobuf:"bytes,14,opt,name=feature_details,json=featureDetails,proto3" json:"feature_details,omitempty"`
	// deleted_at to denote when onboarding record was deleted
	DeletedAt               *timestamppb.Timestamp   `protobuf:"bytes,15,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	PanAadharLinkageDetails *PanAadharLinkageDetails `protobuf:"bytes,16,opt,name=pan_aadhar_linkage_details,json=panAadharLinkageDetails,proto3" json:"pan_aadhar_linkage_details,omitempty"`
	// Represents the information about response (action/error) returned by a stage processor in last execution of orchestrator
	StageProcLastResponse *StageProcLastResponse `protobuf:"bytes,17,opt,name=stage_proc_last_response,json=stageProcLastResponse,proto3" json:"stage_proc_last_response,omitempty"`
}

func (x *OnboardingDetails) Reset() {
	*x = OnboardingDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OnboardingDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnboardingDetails) ProtoMessage() {}

func (x *OnboardingDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnboardingDetails.ProtoReflect.Descriptor instead.
func (*OnboardingDetails) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{0}
}

func (x *OnboardingDetails) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *OnboardingDetails) GetStageDetails() *StageDetails {
	if x != nil {
		return x.StageDetails
	}
	return nil
}

func (x *OnboardingDetails) GetAccountInfo() *AccountInformationInternal {
	if x != nil {
		return x.AccountInfo
	}
	return nil
}

func (x *OnboardingDetails) GetCardInfo() *CardInformationInternal {
	if x != nil {
		return x.CardInfo
	}
	return nil
}

func (x *OnboardingDetails) GetVendor() vendorgateway.Vendor {
	if x != nil {
		return x.Vendor
	}
	return vendorgateway.Vendor(0)
}

func (x *OnboardingDetails) GetOnboardingId() string {
	if x != nil {
		return x.OnboardingId
	}
	return ""
}

func (x *OnboardingDetails) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *OnboardingDetails) GetCompletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletedAt
	}
	return nil
}

func (x *OnboardingDetails) GetStageMetadata() *StageMetadata {
	if x != nil {
		return x.StageMetadata
	}
	return nil
}

func (x *OnboardingDetails) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *OnboardingDetails) GetCurrentOnboardingStage() OnboardingStage {
	if x != nil {
		return x.CurrentOnboardingStage
	}
	return OnboardingStage_ONBOARDING_STAGE_UNSPECIFIED
}

func (x *OnboardingDetails) GetFeature() Feature {
	if x != nil {
		return x.Feature
	}
	return Feature_FEATURE_UNSPECIFIED
}

func (x *OnboardingDetails) GetFiLiteDetails() *FiLiteDetails {
	if x != nil {
		return x.FiLiteDetails
	}
	return nil
}

func (x *OnboardingDetails) GetFeatureDetails() *FeatureDetails {
	if x != nil {
		return x.FeatureDetails
	}
	return nil
}

func (x *OnboardingDetails) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *OnboardingDetails) GetPanAadharLinkageDetails() *PanAadharLinkageDetails {
	if x != nil {
		return x.PanAadharLinkageDetails
	}
	return nil
}

func (x *OnboardingDetails) GetStageProcLastResponse() *StageProcLastResponse {
	if x != nil {
		return x.StageProcLastResponse
	}
	return nil
}

type AccountInformationInternal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Account number of the savings account
	AccountNumber string `protobuf:"bytes,1,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	// Account id of savings account
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// denotes if the account is primary or not
	IsPrimary bool `protobuf:"varint,3,opt,name=is_primary,json=isPrimary,proto3" json:"is_primary,omitempty"`
}

func (x *AccountInformationInternal) Reset() {
	*x = AccountInformationInternal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountInformationInternal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountInformationInternal) ProtoMessage() {}

func (x *AccountInformationInternal) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountInformationInternal.ProtoReflect.Descriptor instead.
func (*AccountInformationInternal) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{1}
}

func (x *AccountInformationInternal) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *AccountInformationInternal) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *AccountInformationInternal) GetIsPrimary() bool {
	if x != nil {
		return x.IsPrimary
	}
	return false
}

type CardInformationInternal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// multiple cards can be issued to the user
	CardDetails []*SingleCardInfo `protobuf:"bytes,1,rep,name=card_details,json=cardDetails,proto3" json:"card_details,omitempty"`
}

func (x *CardInformationInternal) Reset() {
	*x = CardInformationInternal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardInformationInternal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardInformationInternal) ProtoMessage() {}

func (x *CardInformationInternal) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardInformationInternal.ProtoReflect.Descriptor instead.
func (*CardInformationInternal) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{2}
}

func (x *CardInformationInternal) GetCardDetails() []*SingleCardInfo {
	if x != nil {
		return x.CardDetails
	}
	return nil
}

type SingleCardInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Card id of the user debit card
	CardId string `protobuf:"bytes,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// Card information of the user debit card
	BasicCardInfo *card.BasicCardInfo `protobuf:"bytes,2,opt,name=basic_card_info,json=basicCardInfo,proto3" json:"basic_card_info,omitempty"`
}

func (x *SingleCardInfo) Reset() {
	*x = SingleCardInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SingleCardInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SingleCardInfo) ProtoMessage() {}

func (x *SingleCardInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SingleCardInfo.ProtoReflect.Descriptor instead.
func (*SingleCardInfo) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{3}
}

func (x *SingleCardInfo) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *SingleCardInfo) GetBasicCardInfo() *card.BasicCardInfo {
	if x != nil {
		return x.BasicCardInfo
	}
	return nil
}

type StageDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Details of each stage in onboarding process
	// This will contain the history of all the stages - customer creation, account creation, card creation etc.
	// The key for the map is Stage.string()
	StageMapping map[string]*StageInfo `protobuf:"bytes,1,rep,name=stage_mapping,json=stageMapping,proto3" json:"stage_mapping,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *StageDetails) Reset() {
	*x = StageDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StageDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StageDetails) ProtoMessage() {}

func (x *StageDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StageDetails.ProtoReflect.Descriptor instead.
func (*StageDetails) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{4}
}

func (x *StageDetails) GetStageMapping() map[string]*StageInfo {
	if x != nil {
		return x.StageMapping
	}
	return nil
}

type KYCMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Timestamp at which the kyc of user expires,
	// and if the user's kyc expires before customer creation, we will have to do kyc again.
	ExpiryAt *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=expiry_at,json=expiryAt,proto3" json:"expiry_at,omitempty"`
	// Cache last updated value of KYC level in user service.
	// Optimises unnecessary update user API calls.
	LastUpdatedKycLevel kyc.KYCLevel `protobuf:"varint,2,opt,name=last_updated_kyc_level,json=lastUpdatedKycLevel,proto3,enum=kyc.KYCLevel" json:"last_updated_kyc_level,omitempty"`
	// Cache the error received for KYC.
	// Optimises unnecessary calls for checking KYC failure reasons and sending specific communication for those failures.
	FailureType kyc.FailureType `protobuf:"varint,3,opt,name=failure_type,json=failureType,proto3,enum=kyc.FailureType" json:"failure_type,omitempty"`
	// Cache the error received for CKYC.
	// Storing this explicitly to avoid value change due to ekyc failures
	CkycFailureType kyc.FailureType `protobuf:"varint,4,opt,name=ckyc_failure_type,json=ckycFailureType,proto3,enum=kyc.FailureType" json:"ckyc_failure_type,omitempty"`
}

func (x *KYCMetadata) Reset() {
	*x = KYCMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KYCMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KYCMetadata) ProtoMessage() {}

func (x *KYCMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KYCMetadata.ProtoReflect.Descriptor instead.
func (*KYCMetadata) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{5}
}

func (x *KYCMetadata) GetExpiryAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiryAt
	}
	return nil
}

func (x *KYCMetadata) GetLastUpdatedKycLevel() kyc.KYCLevel {
	if x != nil {
		return x.LastUpdatedKycLevel
	}
	return kyc.KYCLevel(0)
}

func (x *KYCMetadata) GetFailureType() kyc.FailureType {
	if x != nil {
		return x.FailureType
	}
	return kyc.FailureType(0)
}

func (x *KYCMetadata) GetCkycFailureType() kyc.FailureType {
	if x != nil {
		return x.CkycFailureType
	}
	return kyc.FailureType(0)
}

// Message contains required information of a single stage
type StageInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The state of current stage - Initiated, InProgress, Success etc.
	State OnboardingState `protobuf:"varint,1,opt,name=state,proto3,enum=user.onboarding.OnboardingState" json:"state,omitempty"`
	// The timestamp at which the stage was updated with the state.
	// This information will be useful for debugging in case of failures,
	// and to analyze which stages have taken longer to completion etc.
	LastUpdatedAt *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=last_updated_at,json=lastUpdatedAt,proto3" json:"last_updated_at,omitempty"`
	// The timestamp at which the stage was deemed to be started.
	StartedAt *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`
	// obfuscated GPS coordinates location identifier.
	// Since location identifier is a sensitive user information, it's important
	// for us to restrict the exposure keeping user's privacy in mind.
	LocationToken string `protobuf:"bytes,4,opt,name=location_token,json=locationToken,proto3" json:"location_token,omitempty"`
}

func (x *StageInfo) Reset() {
	*x = StageInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StageInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StageInfo) ProtoMessage() {}

func (x *StageInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StageInfo.ProtoReflect.Descriptor instead.
func (*StageInfo) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{6}
}

func (x *StageInfo) GetState() OnboardingState {
	if x != nil {
		return x.State
	}
	return OnboardingState_UNSPECIFIED
}

func (x *StageInfo) GetLastUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LastUpdatedAt
	}
	return nil
}

func (x *StageInfo) GetStartedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.StartedAt
	}
	return nil
}

func (x *StageInfo) GetLocationToken() string {
	if x != nil {
		return x.LocationToken
	}
	return ""
}

type StageProcLastResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Screen name returned by the stage processor
	ScreenName string `protobuf:"bytes,1,opt,name=screen_name,json=screenName,proto3" json:"screen_name,omitempty"`
	// Stage from which the response was last returned
	Stage OnboardingStage `protobuf:"varint,2,opt,name=stage,proto3,enum=user.onboarding.OnboardingStage" json:"stage,omitempty"`
	// A map of analytics properties for the server events
	// key is the event properties name and
	// value is the event properties value
	// Note: Do not send PII in this field.
	AnalyticsEventProperties map[string]string `protobuf:"bytes,3,rep,name=analytics_event_properties,json=analyticsEventProperties,proto3" json:"analytics_event_properties,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// error string return by the stage processor
	Error string `protobuf:"bytes,4,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *StageProcLastResponse) Reset() {
	*x = StageProcLastResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StageProcLastResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StageProcLastResponse) ProtoMessage() {}

func (x *StageProcLastResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StageProcLastResponse.ProtoReflect.Descriptor instead.
func (*StageProcLastResponse) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{7}
}

func (x *StageProcLastResponse) GetScreenName() string {
	if x != nil {
		return x.ScreenName
	}
	return ""
}

func (x *StageProcLastResponse) GetStage() OnboardingStage {
	if x != nil {
		return x.Stage
	}
	return OnboardingStage_ONBOARDING_STAGE_UNSPECIFIED
}

func (x *StageProcLastResponse) GetAnalyticsEventProperties() map[string]string {
	if x != nil {
		return x.AnalyticsEventProperties
	}
	return nil
}

func (x *StageProcLastResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

type PANNameCheck struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Inhouse DS name match API decision & score
	InhouseNameMatchPassed bool    `protobuf:"varint,1,opt,name=inhouse_name_match_passed,json=inhouseNameMatchPassed,proto3" json:"inhouse_name_match_passed,omitempty"`
	InhouseNameMatchScore  float32 `protobuf:"fixed32,2,opt,name=inhouse_name_match_score,json=inhouseNameMatchScore,proto3" json:"inhouse_name_match_score,omitempty"`
	// Old(Already being used) name match API decision & score
	OldNameMatchPassed bool    `protobuf:"varint,3,opt,name=old_name_match_passed,json=oldNameMatchPassed,proto3" json:"old_name_match_passed,omitempty"`
	OldNameMatchScore  float32 `protobuf:"fixed32,4,opt,name=old_name_match_score,json=oldNameMatchScore,proto3" json:"old_name_match_score,omitempty"`
	// Deprecated: Marked as deprecated in api/user/onboarding/internal/onboarding_details.proto.
	InhouseNameMatchFeatureDict string `protobuf:"bytes,5,opt,name=inhouse_name_match_feature_dict,json=inhouseNameMatchFeatureDict,proto3" json:"inhouse_name_match_feature_dict,omitempty"`
	InhouseNameMatchRiskMatch   int32  `protobuf:"varint,6,opt,name=inhouse_name_match_risk_match,json=inhouseNameMatchRiskMatch,proto3" json:"inhouse_name_match_risk_match,omitempty"`
}

func (x *PANNameCheck) Reset() {
	*x = PANNameCheck{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PANNameCheck) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PANNameCheck) ProtoMessage() {}

func (x *PANNameCheck) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PANNameCheck.ProtoReflect.Descriptor instead.
func (*PANNameCheck) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{8}
}

func (x *PANNameCheck) GetInhouseNameMatchPassed() bool {
	if x != nil {
		return x.InhouseNameMatchPassed
	}
	return false
}

func (x *PANNameCheck) GetInhouseNameMatchScore() float32 {
	if x != nil {
		return x.InhouseNameMatchScore
	}
	return 0
}

func (x *PANNameCheck) GetOldNameMatchPassed() bool {
	if x != nil {
		return x.OldNameMatchPassed
	}
	return false
}

func (x *PANNameCheck) GetOldNameMatchScore() float32 {
	if x != nil {
		return x.OldNameMatchScore
	}
	return 0
}

// Deprecated: Marked as deprecated in api/user/onboarding/internal/onboarding_details.proto.
func (x *PANNameCheck) GetInhouseNameMatchFeatureDict() string {
	if x != nil {
		return x.InhouseNameMatchFeatureDict
	}
	return ""
}

func (x *PANNameCheck) GetInhouseNameMatchRiskMatch() int32 {
	if x != nil {
		return x.InhouseNameMatchRiskMatch
	}
	return 0
}

type StageMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// kyc Stage specific details
	KycMetadata           *KYCMetadata               `protobuf:"bytes,1,opt,name=kyc_metadata,json=kycMetadata,proto3" json:"kyc_metadata,omitempty"`
	DedupeStatus          customer.DedupeStatus      `protobuf:"varint,2,opt,name=dedupe_status,json=dedupeStatus,proto3,enum=vendorgateway.openbanking.customer.DedupeStatus" json:"dedupe_status,omitempty"`
	KycDedupeStatus       customer.DedupeStatus      `protobuf:"varint,3,opt,name=kyc_dedupe_status,json=kycDedupeStatus,proto3,enum=vendorgateway.openbanking.customer.DedupeStatus" json:"kyc_dedupe_status,omitempty"`
	KycDedupeRetryCount   int32                      `protobuf:"varint,4,opt,name=kyc_dedupe_retry_count,json=kycDedupeRetryCount,proto3" json:"kyc_dedupe_retry_count,omitempty"`
	PanNameCheck          *PANNameCheck              `protobuf:"bytes,5,opt,name=pan_name_check,json=panNameCheck,proto3" json:"pan_name_check,omitempty"`
	AppScreeningData      *AppScreeningMetaData      `protobuf:"bytes,6,opt,name=app_screening_data,json=appScreeningData,proto3" json:"app_screening_data,omitempty"`
	DebitCardNameCheck    *DebitCardNameCheck        `protobuf:"bytes,7,opt,name=debit_card_name_check,json=debitCardNameCheck,proto3" json:"debit_card_name_check,omitempty"`
	EkycNameDobValidation *EKYCNameDOBValidationData `protobuf:"bytes,8,opt,name=ekyc_name_dob_validation,json=ekycNameDobValidation,proto3" json:"ekyc_name_dob_validation,omitempty"`
	PanValidation         *PanValidation             `protobuf:"bytes,9,opt,name=pan_validation,json=panValidation,proto3" json:"pan_validation,omitempty"`
	// Deprecated: Marked as deprecated in api/user/onboarding/internal/onboarding_details.proto.
	UNNameCheckStatus               UNNameCheckStatus          `protobuf:"varint,10,opt,name=u_n_name_check_status,json=uNNameCheckStatus,proto3,enum=user.onboarding.UNNameCheckStatus" json:"u_n_name_check_status,omitempty"`
	PreCustomerCreationDedupeStatus customer.DedupeStatus      `protobuf:"varint,11,opt,name=pre_customer_creation_dedupe_status,json=preCustomerCreationDedupeStatus,proto3,enum=vendorgateway.openbanking.customer.DedupeStatus" json:"pre_customer_creation_dedupe_status,omitempty"`
	PanManualReviewAnnotation       *PanManualReviewAnnotation `protobuf:"bytes,12,opt,name=pan_manual_review_annotation,json=panManualReviewAnnotation,proto3" json:"pan_manual_review_annotation,omitempty"`
	VkycMetadata                    *VKYCMetadata              `protobuf:"bytes,13,opt,name=vkyc_metadata,json=vkycMetadata,proto3" json:"vkyc_metadata,omitempty"`
	GmailPanNameMatchScore          float32                    `protobuf:"fixed32,14,opt,name=gmail_pan_name_match_score,json=gmailPanNameMatchScore,proto3" json:"gmail_pan_name_match_score,omitempty"`
	RiskScreeningMetadata           *RiskScreeningMetadata     `protobuf:"bytes,15,opt,name=risk_screening_metadata,json=riskScreeningMetadata,proto3" json:"risk_screening_metadata,omitempty"`
	DedupeDobRetryCount             int32                      `protobuf:"varint,16,opt,name=dedupe_dob_retry_count,json=dedupeDobRetryCount,proto3" json:"dedupe_dob_retry_count,omitempty"`
	// We perform dedupe checks for a user multiple times during their onboarding in DEDUPE_CHECK, KYC_DEDUPE_CHECK & PRE_CUSTOMER_CREATION_CHECK stages.
	// This field stores the dedupe status from the latest dedupe status fetched from the vendor.
	LatestDedupeStatus               customer.DedupeStatus          `protobuf:"varint,17,opt,name=latest_dedupe_status,json=latestDedupeStatus,proto3,enum=vendorgateway.openbanking.customer.DedupeStatus" json:"latest_dedupe_status,omitempty"`
	LivenessMetadata                 *LivenessMetadata              `protobuf:"bytes,18,opt,name=liveness_metadata,json=livenessMetadata,proto3" json:"liveness_metadata,omitempty"`
	UNNameCheckStatusV2              kyc.UNNameCheckStatus          `protobuf:"varint,19,opt,name=u_n_name_check_status_v2,json=uNNameCheckStatusV2,proto3,enum=kyc.UNNameCheckStatus" json:"u_n_name_check_status_v2,omitempty"`
	IntentSelectionMetadata          *IntentSelectionMetadata       `protobuf:"bytes,20,opt,name=intent_selection_metadata,json=intentSelectionMetadata,proto3" json:"intent_selection_metadata,omitempty"`
	PreCustomerCreationMetadata      *PreCustomerCreationMetadata   `protobuf:"bytes,21,opt,name=pre_customer_creation_metadata,json=preCustomerCreationMetadata,proto3" json:"pre_customer_creation_metadata,omitempty"`
	UpdateProfileDetailsMetadata     *UpdateProfileDetailsMetadata  `protobuf:"bytes,22,opt,name=update_profile_details_metadata,json=updateProfileDetailsMetadata,proto3" json:"update_profile_details_metadata,omitempty"`
	SoftIntentSelectionMetadata      *SoftIntentSelectionMetadata   `protobuf:"bytes,23,opt,name=soft_intent_selection_metadata,json=softIntentSelectionMetadata,proto3" json:"soft_intent_selection_metadata,omitempty"`
	PassportVerificationMetadata     *PassportVerificationMetadata  `protobuf:"bytes,24,opt,name=passport_verification_metadata,json=passportVerificationMetadata,proto3" json:"passport_verification_metadata,omitempty"`
	CountryIdVerificationMetadata    *CountryIdVerificationMetadata `protobuf:"bytes,25,opt,name=country_id_verification_metadata,json=countryIdVerificationMetadata,proto3" json:"country_id_verification_metadata,omitempty"`
	NonResidentCrossValidationResult *CrossValidationResult         `protobuf:"bytes,26,opt,name=non_resident_cross_validation_result,json=nonResidentCrossValidationResult,proto3" json:"non_resident_cross_validation_result,omitempty"`
	// when user failed screener checks but was allowed to open account with AMB (regular tier account)
	UserAllowedToOpenAmbAccount        bool                                `protobuf:"varint,27,opt,name=user_allowed_to_open_amb_account,json=userAllowedToOpenAmbAccount,proto3" json:"user_allowed_to_open_amb_account,omitempty"`
	CreditReportFetchMetadata          *CreditReportFetchMetadata          `protobuf:"bytes,28,opt,name=credit_report_fetch_metadata,json=creditReportFetchMetadata,proto3" json:"credit_report_fetch_metadata,omitempty"`
	PreAccountCreationAddMoneyMetadata *PreAccountCreationAddMoneyMetadata `protobuf:"bytes,29,opt,name=pre_account_creation_add_money_metadata,json=preAccountCreationAddMoneyMetadata,proto3" json:"pre_account_creation_add_money_metadata,omitempty"`
	PermissionMetadata                 *PermissionMetaData                 `protobuf:"bytes,30,opt,name=permission_metadata,json=permissionMetadata,proto3" json:"permission_metadata,omitempty"`
	AddMoneyMetadata                   *AddMoneyMetadata                   `protobuf:"bytes,31,opt,name=add_money_metadata,json=addMoneyMetadata,proto3" json:"add_money_metadata,omitempty"`
}

func (x *StageMetadata) Reset() {
	*x = StageMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StageMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StageMetadata) ProtoMessage() {}

func (x *StageMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StageMetadata.ProtoReflect.Descriptor instead.
func (*StageMetadata) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{9}
}

func (x *StageMetadata) GetKycMetadata() *KYCMetadata {
	if x != nil {
		return x.KycMetadata
	}
	return nil
}

func (x *StageMetadata) GetDedupeStatus() customer.DedupeStatus {
	if x != nil {
		return x.DedupeStatus
	}
	return customer.DedupeStatus(0)
}

func (x *StageMetadata) GetKycDedupeStatus() customer.DedupeStatus {
	if x != nil {
		return x.KycDedupeStatus
	}
	return customer.DedupeStatus(0)
}

func (x *StageMetadata) GetKycDedupeRetryCount() int32 {
	if x != nil {
		return x.KycDedupeRetryCount
	}
	return 0
}

func (x *StageMetadata) GetPanNameCheck() *PANNameCheck {
	if x != nil {
		return x.PanNameCheck
	}
	return nil
}

func (x *StageMetadata) GetAppScreeningData() *AppScreeningMetaData {
	if x != nil {
		return x.AppScreeningData
	}
	return nil
}

func (x *StageMetadata) GetDebitCardNameCheck() *DebitCardNameCheck {
	if x != nil {
		return x.DebitCardNameCheck
	}
	return nil
}

func (x *StageMetadata) GetEkycNameDobValidation() *EKYCNameDOBValidationData {
	if x != nil {
		return x.EkycNameDobValidation
	}
	return nil
}

func (x *StageMetadata) GetPanValidation() *PanValidation {
	if x != nil {
		return x.PanValidation
	}
	return nil
}

// Deprecated: Marked as deprecated in api/user/onboarding/internal/onboarding_details.proto.
func (x *StageMetadata) GetUNNameCheckStatus() UNNameCheckStatus {
	if x != nil {
		return x.UNNameCheckStatus
	}
	return UNNameCheckStatus_UNNC_UNSPECIFIED
}

func (x *StageMetadata) GetPreCustomerCreationDedupeStatus() customer.DedupeStatus {
	if x != nil {
		return x.PreCustomerCreationDedupeStatus
	}
	return customer.DedupeStatus(0)
}

func (x *StageMetadata) GetPanManualReviewAnnotation() *PanManualReviewAnnotation {
	if x != nil {
		return x.PanManualReviewAnnotation
	}
	return nil
}

func (x *StageMetadata) GetVkycMetadata() *VKYCMetadata {
	if x != nil {
		return x.VkycMetadata
	}
	return nil
}

func (x *StageMetadata) GetGmailPanNameMatchScore() float32 {
	if x != nil {
		return x.GmailPanNameMatchScore
	}
	return 0
}

func (x *StageMetadata) GetRiskScreeningMetadata() *RiskScreeningMetadata {
	if x != nil {
		return x.RiskScreeningMetadata
	}
	return nil
}

func (x *StageMetadata) GetDedupeDobRetryCount() int32 {
	if x != nil {
		return x.DedupeDobRetryCount
	}
	return 0
}

func (x *StageMetadata) GetLatestDedupeStatus() customer.DedupeStatus {
	if x != nil {
		return x.LatestDedupeStatus
	}
	return customer.DedupeStatus(0)
}

func (x *StageMetadata) GetLivenessMetadata() *LivenessMetadata {
	if x != nil {
		return x.LivenessMetadata
	}
	return nil
}

func (x *StageMetadata) GetUNNameCheckStatusV2() kyc.UNNameCheckStatus {
	if x != nil {
		return x.UNNameCheckStatusV2
	}
	return kyc.UNNameCheckStatus(0)
}

func (x *StageMetadata) GetIntentSelectionMetadata() *IntentSelectionMetadata {
	if x != nil {
		return x.IntentSelectionMetadata
	}
	return nil
}

func (x *StageMetadata) GetPreCustomerCreationMetadata() *PreCustomerCreationMetadata {
	if x != nil {
		return x.PreCustomerCreationMetadata
	}
	return nil
}

func (x *StageMetadata) GetUpdateProfileDetailsMetadata() *UpdateProfileDetailsMetadata {
	if x != nil {
		return x.UpdateProfileDetailsMetadata
	}
	return nil
}

func (x *StageMetadata) GetSoftIntentSelectionMetadata() *SoftIntentSelectionMetadata {
	if x != nil {
		return x.SoftIntentSelectionMetadata
	}
	return nil
}

func (x *StageMetadata) GetPassportVerificationMetadata() *PassportVerificationMetadata {
	if x != nil {
		return x.PassportVerificationMetadata
	}
	return nil
}

func (x *StageMetadata) GetCountryIdVerificationMetadata() *CountryIdVerificationMetadata {
	if x != nil {
		return x.CountryIdVerificationMetadata
	}
	return nil
}

func (x *StageMetadata) GetNonResidentCrossValidationResult() *CrossValidationResult {
	if x != nil {
		return x.NonResidentCrossValidationResult
	}
	return nil
}

func (x *StageMetadata) GetUserAllowedToOpenAmbAccount() bool {
	if x != nil {
		return x.UserAllowedToOpenAmbAccount
	}
	return false
}

func (x *StageMetadata) GetCreditReportFetchMetadata() *CreditReportFetchMetadata {
	if x != nil {
		return x.CreditReportFetchMetadata
	}
	return nil
}

func (x *StageMetadata) GetPreAccountCreationAddMoneyMetadata() *PreAccountCreationAddMoneyMetadata {
	if x != nil {
		return x.PreAccountCreationAddMoneyMetadata
	}
	return nil
}

func (x *StageMetadata) GetPermissionMetadata() *PermissionMetaData {
	if x != nil {
		return x.PermissionMetadata
	}
	return nil
}

func (x *StageMetadata) GetAddMoneyMetadata() *AddMoneyMetadata {
	if x != nil {
		return x.AddMoneyMetadata
	}
	return nil
}

type PermissionMetaData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Indicates whether the client has acknowledged granting the required permission.
	// If true, the PERMISSION stage is marked as successful.
	PermissionAckReceived bool `protobuf:"varint,1,opt,name=permission_ack_received,json=permissionAckReceived,proto3" json:"permission_ack_received,omitempty"`
}

func (x *PermissionMetaData) Reset() {
	*x = PermissionMetaData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PermissionMetaData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PermissionMetaData) ProtoMessage() {}

func (x *PermissionMetaData) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PermissionMetaData.ProtoReflect.Descriptor instead.
func (*PermissionMetaData) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{10}
}

func (x *PermissionMetaData) GetPermissionAckReceived() bool {
	if x != nil {
		return x.PermissionAckReceived
	}
	return false
}

type AddMoneyMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// only one of skip reason / success reason will be populated
	SkipReason    string `protobuf:"bytes,1,opt,name=skip_reason,json=skipReason,proto3" json:"skip_reason,omitempty"`
	SuccessReason string `protobuf:"bytes,2,opt,name=success_reason,json=successReason,proto3" json:"success_reason,omitempty"`
}

func (x *AddMoneyMetadata) Reset() {
	*x = AddMoneyMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddMoneyMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddMoneyMetadata) ProtoMessage() {}

func (x *AddMoneyMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddMoneyMetadata.ProtoReflect.Descriptor instead.
func (*AddMoneyMetadata) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{11}
}

func (x *AddMoneyMetadata) GetSkipReason() string {
	if x != nil {
		return x.SkipReason
	}
	return ""
}

func (x *AddMoneyMetadata) GetSuccessReason() string {
	if x != nil {
		return x.SuccessReason
	}
	return ""
}

type PreAccountCreationAddMoneyMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// deprecated in favour of success/skip reason
	//
	// Deprecated: Marked as deprecated in api/user/onboarding/internal/onboarding_details.proto.
	SkippedAfterSavingsAccCreation bool `protobuf:"varint,1,opt,name=skipped_after_savings_acc_creation,json=skippedAfterSavingsAccCreation,proto3" json:"skipped_after_savings_acc_creation,omitempty"`
	// marked if add money order is marked as SUCCESS
	// deprecated in favour of success/skip reason
	//
	// Deprecated: Marked as deprecated in api/user/onboarding/internal/onboarding_details.proto.
	SkippedAfterSuccessfulAddMoney bool `protobuf:"varint,2,opt,name=skipped_after_successful_add_money,json=skippedAfterSuccessfulAddMoney,proto3" json:"skipped_after_successful_add_money,omitempty"`
	// only one of skip reason / success reason will be populated
	SkipReason    string `protobuf:"bytes,3,opt,name=skip_reason,json=skipReason,proto3" json:"skip_reason,omitempty"`
	SuccessReason string `protobuf:"bytes,4,opt,name=success_reason,json=successReason,proto3" json:"success_reason,omitempty"`
}

func (x *PreAccountCreationAddMoneyMetadata) Reset() {
	*x = PreAccountCreationAddMoneyMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreAccountCreationAddMoneyMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreAccountCreationAddMoneyMetadata) ProtoMessage() {}

func (x *PreAccountCreationAddMoneyMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreAccountCreationAddMoneyMetadata.ProtoReflect.Descriptor instead.
func (*PreAccountCreationAddMoneyMetadata) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{12}
}

// Deprecated: Marked as deprecated in api/user/onboarding/internal/onboarding_details.proto.
func (x *PreAccountCreationAddMoneyMetadata) GetSkippedAfterSavingsAccCreation() bool {
	if x != nil {
		return x.SkippedAfterSavingsAccCreation
	}
	return false
}

// Deprecated: Marked as deprecated in api/user/onboarding/internal/onboarding_details.proto.
func (x *PreAccountCreationAddMoneyMetadata) GetSkippedAfterSuccessfulAddMoney() bool {
	if x != nil {
		return x.SkippedAfterSuccessfulAddMoney
	}
	return false
}

func (x *PreAccountCreationAddMoneyMetadata) GetSkipReason() string {
	if x != nil {
		return x.SkipReason
	}
	return ""
}

func (x *PreAccountCreationAddMoneyMetadata) GetSuccessReason() string {
	if x != nil {
		return x.SuccessReason
	}
	return ""
}

// During SA onboarding, there can be multiple attempts to fetch credit report of a user - with PAN and without PAN.
// This message contains the client request ids for both the attempts.
type CreditReportFetchMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientReqIdFetchWithoutPan string `protobuf:"bytes,1,opt,name=client_req_id_fetch_without_pan,json=clientReqIdFetchWithoutPan,proto3" json:"client_req_id_fetch_without_pan,omitempty"`
	ClientReqIdFetchWithPan    string `protobuf:"bytes,2,opt,name=client_req_id_fetch_with_pan,json=clientReqIdFetchWithPan,proto3" json:"client_req_id_fetch_with_pan,omitempty"`
}

func (x *CreditReportFetchMetadata) Reset() {
	*x = CreditReportFetchMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreditReportFetchMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreditReportFetchMetadata) ProtoMessage() {}

func (x *CreditReportFetchMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreditReportFetchMetadata.ProtoReflect.Descriptor instead.
func (*CreditReportFetchMetadata) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{13}
}

func (x *CreditReportFetchMetadata) GetClientReqIdFetchWithoutPan() string {
	if x != nil {
		return x.ClientReqIdFetchWithoutPan
	}
	return ""
}

func (x *CreditReportFetchMetadata) GetClientReqIdFetchWithPan() string {
	if x != nil {
		return x.ClientReqIdFetchWithPan
	}
	return ""
}

type PreCustomerCreationMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FailureReason PreCustomerCreationMetadata_FailureReason `protobuf:"varint,1,opt,name=failure_reason,json=failureReason,proto3,enum=user.onboarding.PreCustomerCreationMetadata_FailureReason" json:"failure_reason,omitempty"`
}

func (x *PreCustomerCreationMetadata) Reset() {
	*x = PreCustomerCreationMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreCustomerCreationMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreCustomerCreationMetadata) ProtoMessage() {}

func (x *PreCustomerCreationMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreCustomerCreationMetadata.ProtoReflect.Descriptor instead.
func (*PreCustomerCreationMetadata) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{14}
}

func (x *PreCustomerCreationMetadata) GetFailureReason() PreCustomerCreationMetadata_FailureReason {
	if x != nil {
		return x.FailureReason
	}
	return PreCustomerCreationMetadata_FAILURE_REASON_UNSPECIFIED
}

type UpdateProfileDetailsMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// identifier for profile update request, with mapping in Temporal workflow
	RequestId string `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
}

func (x *UpdateProfileDetailsMetadata) Reset() {
	*x = UpdateProfileDetailsMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateProfileDetailsMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateProfileDetailsMetadata) ProtoMessage() {}

func (x *UpdateProfileDetailsMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateProfileDetailsMetadata.ProtoReflect.Descriptor instead.
func (*UpdateProfileDetailsMetadata) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{15}
}

func (x *UpdateProfileDetailsMetadata) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

type LivenessMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Latest liveness summary requestID for the actor
	RequestId string `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
}

func (x *LivenessMetadata) Reset() {
	*x = LivenessMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LivenessMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LivenessMetadata) ProtoMessage() {}

func (x *LivenessMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LivenessMetadata.ProtoReflect.Descriptor instead.
func (*LivenessMetadata) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{16}
}

func (x *LivenessMetadata) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

type RiskScreeningMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// deprecated: use risk data (risk svc) instead
	//
	// Deprecated: Marked as deprecated in api/user/onboarding/internal/onboarding_details.proto.
	RiskBlockingReason RiskFactor `protobuf:"varint,1,opt,name=risk_blocking_reason,json=riskBlockingReason,proto3,enum=user.onboarding.RiskFactor" json:"risk_blocking_reason,omitempty"`
	// deprecated: use risk data (risk svc) instead
	//
	// Deprecated: Marked as deprecated in api/user/onboarding/internal/onboarding_details.proto.
	ForcedManualReviewReason RiskFactor `protobuf:"varint,2,opt,name=forced_manual_review_reason,json=forcedManualReviewReason,proto3,enum=user.onboarding.RiskFactor" json:"forced_manual_review_reason,omitempty"`
	// client request id for the risk service request
	ClientRequestId string `protobuf:"bytes,3,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
}

func (x *RiskScreeningMetadata) Reset() {
	*x = RiskScreeningMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskScreeningMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskScreeningMetadata) ProtoMessage() {}

func (x *RiskScreeningMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskScreeningMetadata.ProtoReflect.Descriptor instead.
func (*RiskScreeningMetadata) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{17}
}

// Deprecated: Marked as deprecated in api/user/onboarding/internal/onboarding_details.proto.
func (x *RiskScreeningMetadata) GetRiskBlockingReason() RiskFactor {
	if x != nil {
		return x.RiskBlockingReason
	}
	return RiskFactor_RISK_FACTOR_UNSPECIFIED
}

// Deprecated: Marked as deprecated in api/user/onboarding/internal/onboarding_details.proto.
func (x *RiskScreeningMetadata) GetForcedManualReviewReason() RiskFactor {
	if x != nil {
		return x.ForcedManualReviewReason
	}
	return RiskFactor_RISK_FACTOR_UNSPECIFIED
}

func (x *RiskScreeningMetadata) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

type PanManualReviewAnnotation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Verdict               Verdict                 `protobuf:"varint,1,opt,name=verdict,proto3,enum=user.onboarding.Verdict" json:"verdict,omitempty"`
	ReviewedBy            string                  `protobuf:"bytes,2,opt,name=reviewed_by,json=reviewedBy,proto3" json:"reviewed_by,omitempty"`
	ReviewedOn            *timestamppb.Timestamp  `protobuf:"bytes,3,opt,name=reviewed_on,json=reviewedOn,proto3" json:"reviewed_on,omitempty"`
	PanNameReviewCategory []PanNameReviewCategory `protobuf:"varint,4,rep,packed,name=pan_name_review_category,json=panNameReviewCategory,proto3,enum=user.onboarding.PanNameReviewCategory" json:"pan_name_review_category,omitempty"`
	Remarks               string                  `protobuf:"bytes,5,opt,name=remarks,proto3" json:"remarks,omitempty"`
}

func (x *PanManualReviewAnnotation) Reset() {
	*x = PanManualReviewAnnotation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PanManualReviewAnnotation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PanManualReviewAnnotation) ProtoMessage() {}

func (x *PanManualReviewAnnotation) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PanManualReviewAnnotation.ProtoReflect.Descriptor instead.
func (*PanManualReviewAnnotation) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{18}
}

func (x *PanManualReviewAnnotation) GetVerdict() Verdict {
	if x != nil {
		return x.Verdict
	}
	return Verdict_VERDICT_UNSPECIFIED
}

func (x *PanManualReviewAnnotation) GetReviewedBy() string {
	if x != nil {
		return x.ReviewedBy
	}
	return ""
}

func (x *PanManualReviewAnnotation) GetReviewedOn() *timestamppb.Timestamp {
	if x != nil {
		return x.ReviewedOn
	}
	return nil
}

func (x *PanManualReviewAnnotation) GetPanNameReviewCategory() []PanNameReviewCategory {
	if x != nil {
		return x.PanNameReviewCategory
	}
	return nil
}

func (x *PanManualReviewAnnotation) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

type DebitCardNameCheck struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Inhouse DS name match API decision & score
	InhouseNameMatchPassed bool    `protobuf:"varint,1,opt,name=inhouse_name_match_passed,json=inhouseNameMatchPassed,proto3" json:"inhouse_name_match_passed,omitempty"`
	InhouseNameMatchScore  float32 `protobuf:"fixed32,2,opt,name=inhouse_name_match_score,json=inhouseNameMatchScore,proto3" json:"inhouse_name_match_score,omitempty"`
	// Old(Already being used) name match API decision & score
	OldNameMatchPassed bool    `protobuf:"varint,3,opt,name=old_name_match_passed,json=oldNameMatchPassed,proto3" json:"old_name_match_passed,omitempty"`
	OldNameMatchScore  float32 `protobuf:"fixed32,4,opt,name=old_name_match_score,json=oldNameMatchScore,proto3" json:"old_name_match_score,omitempty"`
	// Number of retries for name check.
	// User will be redirected to error screen if name check fails
	// for more than a given threshold.
	NameCheckRetryCount int32 `protobuf:"varint,5,opt,name=name_check_retry_count,json=nameCheckRetryCount,proto3" json:"name_check_retry_count,omitempty"`
}

func (x *DebitCardNameCheck) Reset() {
	*x = DebitCardNameCheck{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DebitCardNameCheck) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DebitCardNameCheck) ProtoMessage() {}

func (x *DebitCardNameCheck) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DebitCardNameCheck.ProtoReflect.Descriptor instead.
func (*DebitCardNameCheck) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{19}
}

func (x *DebitCardNameCheck) GetInhouseNameMatchPassed() bool {
	if x != nil {
		return x.InhouseNameMatchPassed
	}
	return false
}

func (x *DebitCardNameCheck) GetInhouseNameMatchScore() float32 {
	if x != nil {
		return x.InhouseNameMatchScore
	}
	return 0
}

func (x *DebitCardNameCheck) GetOldNameMatchPassed() bool {
	if x != nil {
		return x.OldNameMatchPassed
	}
	return false
}

func (x *DebitCardNameCheck) GetOldNameMatchScore() float32 {
	if x != nil {
		return x.OldNameMatchScore
	}
	return 0
}

func (x *DebitCardNameCheck) GetNameCheckRetryCount() int32 {
	if x != nil {
		return x.NameCheckRetryCount
	}
	return 0
}

// Following meta data needs to be persisted in order to avoid repeated fetching
// of statuses of validation from domain services - Employment verification and credit report verification services
type AppScreeningMetaData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// boolean to represent whether credit report was found
	CreditReportFound common.BooleanEnum `protobuf:"varint,1,opt,name=credit_report_found,json=creditReportFound,proto3,enum=api.typesv2.common.BooleanEnum" json:"credit_report_found,omitempty"`
	// boolean to represent whether pan based credit report presence check has been attempted
	// this essentially represents if CREDIT_REPORT_PRESENCE_CHECK_WITH_PAN has completed
	PanCreditReportCheckAttempted common.BooleanEnum `protobuf:"varint,23,opt,name=pan_credit_report_check_attempted,json=panCreditReportCheckAttempted,proto3,enum=api.typesv2.common.BooleanEnum" json:"pan_credit_report_check_attempted,omitempty"`
	// boolean to represent whether consent was given by user to download credit report
	// 1. True being positive consent
	// 2. False being negative consent
	// 3. Unspecified if consent was not yet taken
	ConsentCreditReportDownload common.BooleanEnum `protobuf:"varint,2,opt,name=consent_credit_report_download,json=consentCreditReportDownload,proto3,enum=api.typesv2.common.BooleanEnum" json:"consent_credit_report_download,omitempty"`
	// boolean to represent if credit report verification passed for the user
	CreditReportVerificationPassed common.BooleanEnum `protobuf:"varint,3,opt,name=credit_report_verification_passed,json=creditReportVerificationPassed,proto3,enum=api.typesv2.common.BooleanEnum" json:"credit_report_verification_passed,omitempty"`
	// boolean to represent if employment verification passed for the user
	EmploymentVerificationPassed common.BooleanEnum `protobuf:"varint,4,opt,name=employment_verification_passed,json=employmentVerificationPassed,proto3,enum=api.typesv2.common.BooleanEnum" json:"employment_verification_passed,omitempty"`
	// reason to allow/reject manual screening
	ManualScreeningReason string `protobuf:"bytes,5,opt,name=manual_screening_reason,json=manualScreeningReason,proto3" json:"manual_screening_reason,omitempty"`
	// screening skip reason as enum
	SkipReason ScreeningSkipReason `protobuf:"varint,6,opt,name=skip_reason,json=skipReason,proto3,enum=user.onboarding.ScreeningSkipReason" json:"skip_reason,omitempty"`
	// reason to skip credit report screening stages
	CreditReportFlowSkipReason CreditReportScreeningSkipReason `protobuf:"varint,7,opt,name=credit_report_flow_skip_reason,json=creditReportFlowSkipReason,proto3,enum=user.onboarding.CreditReportScreeningSkipReason" json:"credit_report_flow_skip_reason,omitempty"`
	// reason why `employment_verification_passed` field is marked as TRUE
	EmploymentVerificationSuccessReason EmploymentVerificationSuccessReason `protobuf:"varint,8,opt,name=employment_verification_success_reason,json=employmentVerificationSuccessReason,proto3,enum=user.onboarding.EmploymentVerificationSuccessReason" json:"employment_verification_success_reason,omitempty"`
	// reason to mark credit report verification stage as success
	CreditReportVerificationSuccessReason CreditReportVerificationSuccessReason `protobuf:"varint,9,opt,name=credit_report_verification_success_reason,json=creditReportVerificationSuccessReason,proto3,enum=user.onboarding.CreditReportVerificationSuccessReason" json:"credit_report_verification_success_reason,omitempty"`
	// denotes the user channel incase of finite code
	FiniteCodeChannel enums.FiniteCodeChannel `protobuf:"varint,10,opt,name=finite_code_channel,json=finiteCodeChannel,proto3,enum=inappreferral.enums.FiniteCodeChannel" json:"finite_code_channel,omitempty"`
	// denotes the different types of Finite Code
	FiniteCodeType enums.FiniteCodeType `protobuf:"varint,11,opt,name=finite_code_type,json=finiteCodeType,proto3,enum=inappreferral.enums.FiniteCodeType" json:"finite_code_type,omitempty"`
	// When true, indicates that user is to be subjected to employment declaration ONLY(employment verification is to be skipped)
	EmploymentDeclarationOnly common.BooleanEnum `protobuf:"varint,12,opt,name=employment_declaration_only,json=employmentDeclarationOnly,proto3,enum=api.typesv2.common.BooleanEnum" json:"employment_declaration_only,omitempty"`
	// denotes if gmail verification threshold was crossed by the user or not
	GmailVerificationPassed common.BooleanEnum `protobuf:"varint,13,opt,name=gmail_verification_passed,json=gmailVerificationPassed,proto3,enum=api.typesv2.common.BooleanEnum" json:"gmail_verification_passed,omitempty"`
	// boolean to represent if work email verification passed for the user
	WorkEmailVerificationPassed common.BooleanEnum `protobuf:"varint,14,opt,name=work_email_verification_passed,json=workEmailVerificationPassed,proto3,enum=api.typesv2.common.BooleanEnum" json:"work_email_verification_passed,omitempty"`
	// reason to fail work email verification
	WorkEmailVerificationFailureReason WorkEmailVerificationFailureReason `protobuf:"varint,15,opt,name=work_email_verification_failure_reason,json=workEmailVerificationFailureReason,proto3,enum=user.onboarding.WorkEmailVerificationFailureReason" json:"work_email_verification_failure_reason,omitempty"`
	// boolean to represent if LinkedIn verification passed
	LinkedinVerificationPassed common.BooleanEnum `protobuf:"varint,16,opt,name=linkedin_verification_passed,json=linkedinVerificationPassed,proto3,enum=api.typesv2.common.BooleanEnum" json:"linkedin_verification_passed,omitempty"`
	// enum to represent how linkedin was passed for user
	LinkedinValidationType LinkedInValidationType `protobuf:"varint,17,opt,name=linkedin_validation_type,json=linkedinValidationType,proto3,enum=user.onboarding.LinkedInValidationType" json:"linkedin_validation_type,omitempty"`
	// whether heuristic verification passed
	HeuristicScreeningPassed common.BooleanEnum `protobuf:"varint,18,opt,name=heuristic_screening_passed,json=heuristicScreeningPassed,proto3,enum=api.typesv2.common.BooleanEnum" json:"heuristic_screening_passed,omitempty"`
	// boolean to represent if user gstin presence check stage was passed
	GstinPresenceCheckPassed common.BooleanEnum `protobuf:"varint,19,opt,name=gstin_presence_check_passed,json=gstinPresenceCheckPassed,proto3,enum=api.typesv2.common.BooleanEnum" json:"gstin_presence_check_passed,omitempty"`
	// reason why `gstin_presence_check_passed` field is marked as TRUE
	GstinPresenceCheckSuccessReason GSTINPresenceCheckSuccessReason `protobuf:"varint,20,opt,name=gstin_presence_check_success_reason,json=gstinPresenceCheckSuccessReason,proto3,enum=user.onboarding.GSTINPresenceCheckSuccessReason" json:"gstin_presence_check_success_reason,omitempty"`
	// whether user succeeded screening with experimental bypass
	ExperimentalScreeningBypass common.BooleanEnum `protobuf:"varint,21,opt,name=experimental_screening_bypass,json=experimentalScreeningBypass,proto3,enum=api.typesv2.common.BooleanEnum" json:"experimental_screening_bypass,omitempty"`
	// metadata to denote if screening passed
	ScreeningPassed common.BooleanEnum `protobuf:"varint,22,opt,name=screening_passed,json=screeningPassed,proto3,enum=api.typesv2.common.BooleanEnum" json:"screening_passed,omitempty"`
	// Heuristic screener pass reason
	HeuristicScreeningPassReason HeuristicPassReason `protobuf:"varint,24,opt,name=heuristic_screening_pass_reason,json=heuristicScreeningPassReason,proto3,enum=user.onboarding.HeuristicPassReason" json:"heuristic_screening_pass_reason,omitempty"`
	// boolean to represent if form16 check passed or not
	Form16CheckPassed common.BooleanEnum `protobuf:"varint,25,opt,name=form16_check_passed,json=form16CheckPassed,proto3,enum=api.typesv2.common.BooleanEnum" json:"form16_check_passed,omitempty"`
	// boolean to represent if uan presence check passed or not
	UanPresenceCheckPassed common.BooleanEnum `protobuf:"varint,26,opt,name=uan_presence_check_passed,json=uanPresenceCheckPassed,proto3,enum=api.typesv2.common.BooleanEnum" json:"uan_presence_check_passed,omitempty"`
	// boolean to represent if affluence check of user passed
	AffluenceCheckPassed common.BooleanEnum `protobuf:"varint,27,opt,name=affluence_check_passed,json=affluenceCheckPassed,proto3,enum=api.typesv2.common.BooleanEnum" json:"affluence_check_passed,omitempty"`
	// affluence score of the user with payu
	AffluenceScore         int32              `protobuf:"varint,28,opt,name=affluence_score,json=affluenceScore,proto3" json:"affluence_score,omitempty"`
	CreditReportPanMatched common.BooleanEnum `protobuf:"varint,29,opt,name=credit_report_pan_matched,json=creditReportPanMatched,proto3,enum=api.typesv2.common.BooleanEnum" json:"credit_report_pan_matched,omitempty"`
}

func (x *AppScreeningMetaData) Reset() {
	*x = AppScreeningMetaData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppScreeningMetaData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppScreeningMetaData) ProtoMessage() {}

func (x *AppScreeningMetaData) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppScreeningMetaData.ProtoReflect.Descriptor instead.
func (*AppScreeningMetaData) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{20}
}

func (x *AppScreeningMetaData) GetCreditReportFound() common.BooleanEnum {
	if x != nil {
		return x.CreditReportFound
	}
	return common.BooleanEnum(0)
}

func (x *AppScreeningMetaData) GetPanCreditReportCheckAttempted() common.BooleanEnum {
	if x != nil {
		return x.PanCreditReportCheckAttempted
	}
	return common.BooleanEnum(0)
}

func (x *AppScreeningMetaData) GetConsentCreditReportDownload() common.BooleanEnum {
	if x != nil {
		return x.ConsentCreditReportDownload
	}
	return common.BooleanEnum(0)
}

func (x *AppScreeningMetaData) GetCreditReportVerificationPassed() common.BooleanEnum {
	if x != nil {
		return x.CreditReportVerificationPassed
	}
	return common.BooleanEnum(0)
}

func (x *AppScreeningMetaData) GetEmploymentVerificationPassed() common.BooleanEnum {
	if x != nil {
		return x.EmploymentVerificationPassed
	}
	return common.BooleanEnum(0)
}

func (x *AppScreeningMetaData) GetManualScreeningReason() string {
	if x != nil {
		return x.ManualScreeningReason
	}
	return ""
}

func (x *AppScreeningMetaData) GetSkipReason() ScreeningSkipReason {
	if x != nil {
		return x.SkipReason
	}
	return ScreeningSkipReason_SCREENING_REASON_UNSPECIFIED
}

func (x *AppScreeningMetaData) GetCreditReportFlowSkipReason() CreditReportScreeningSkipReason {
	if x != nil {
		return x.CreditReportFlowSkipReason
	}
	return CreditReportScreeningSkipReason_SKIP_REASON_UNSPECIFIED
}

func (x *AppScreeningMetaData) GetEmploymentVerificationSuccessReason() EmploymentVerificationSuccessReason {
	if x != nil {
		return x.EmploymentVerificationSuccessReason
	}
	return EmploymentVerificationSuccessReason_EMPLOYMENT_VERIFICATION_SUCCESS_REASON_UNSPECIFIED
}

func (x *AppScreeningMetaData) GetCreditReportVerificationSuccessReason() CreditReportVerificationSuccessReason {
	if x != nil {
		return x.CreditReportVerificationSuccessReason
	}
	return CreditReportVerificationSuccessReason_CREDIT_REPORT_VERIFICACTION_SUCCESS_REASON_UNSPECIFIED
}

func (x *AppScreeningMetaData) GetFiniteCodeChannel() enums.FiniteCodeChannel {
	if x != nil {
		return x.FiniteCodeChannel
	}
	return enums.FiniteCodeChannel(0)
}

func (x *AppScreeningMetaData) GetFiniteCodeType() enums.FiniteCodeType {
	if x != nil {
		return x.FiniteCodeType
	}
	return enums.FiniteCodeType(0)
}

func (x *AppScreeningMetaData) GetEmploymentDeclarationOnly() common.BooleanEnum {
	if x != nil {
		return x.EmploymentDeclarationOnly
	}
	return common.BooleanEnum(0)
}

func (x *AppScreeningMetaData) GetGmailVerificationPassed() common.BooleanEnum {
	if x != nil {
		return x.GmailVerificationPassed
	}
	return common.BooleanEnum(0)
}

func (x *AppScreeningMetaData) GetWorkEmailVerificationPassed() common.BooleanEnum {
	if x != nil {
		return x.WorkEmailVerificationPassed
	}
	return common.BooleanEnum(0)
}

func (x *AppScreeningMetaData) GetWorkEmailVerificationFailureReason() WorkEmailVerificationFailureReason {
	if x != nil {
		return x.WorkEmailVerificationFailureReason
	}
	return WorkEmailVerificationFailureReason_WORK_EMAIL_VERIFICATION_FAILURE_REASON_UNSPECIFIED
}

func (x *AppScreeningMetaData) GetLinkedinVerificationPassed() common.BooleanEnum {
	if x != nil {
		return x.LinkedinVerificationPassed
	}
	return common.BooleanEnum(0)
}

func (x *AppScreeningMetaData) GetLinkedinValidationType() LinkedInValidationType {
	if x != nil {
		return x.LinkedinValidationType
	}
	return LinkedInValidationType_LINKEDIN_VALIDATION_TYPE_UNSPECIFIED
}

func (x *AppScreeningMetaData) GetHeuristicScreeningPassed() common.BooleanEnum {
	if x != nil {
		return x.HeuristicScreeningPassed
	}
	return common.BooleanEnum(0)
}

func (x *AppScreeningMetaData) GetGstinPresenceCheckPassed() common.BooleanEnum {
	if x != nil {
		return x.GstinPresenceCheckPassed
	}
	return common.BooleanEnum(0)
}

func (x *AppScreeningMetaData) GetGstinPresenceCheckSuccessReason() GSTINPresenceCheckSuccessReason {
	if x != nil {
		return x.GstinPresenceCheckSuccessReason
	}
	return GSTINPresenceCheckSuccessReason_GSTIN_PRESENCE_CHECK_SUCCESS_REASON_UNSPECIFIED
}

func (x *AppScreeningMetaData) GetExperimentalScreeningBypass() common.BooleanEnum {
	if x != nil {
		return x.ExperimentalScreeningBypass
	}
	return common.BooleanEnum(0)
}

func (x *AppScreeningMetaData) GetScreeningPassed() common.BooleanEnum {
	if x != nil {
		return x.ScreeningPassed
	}
	return common.BooleanEnum(0)
}

func (x *AppScreeningMetaData) GetHeuristicScreeningPassReason() HeuristicPassReason {
	if x != nil {
		return x.HeuristicScreeningPassReason
	}
	return HeuristicPassReason_HEURISTIC_PASS_REASON_UNSPECIFIED
}

func (x *AppScreeningMetaData) GetForm16CheckPassed() common.BooleanEnum {
	if x != nil {
		return x.Form16CheckPassed
	}
	return common.BooleanEnum(0)
}

func (x *AppScreeningMetaData) GetUanPresenceCheckPassed() common.BooleanEnum {
	if x != nil {
		return x.UanPresenceCheckPassed
	}
	return common.BooleanEnum(0)
}

func (x *AppScreeningMetaData) GetAffluenceCheckPassed() common.BooleanEnum {
	if x != nil {
		return x.AffluenceCheckPassed
	}
	return common.BooleanEnum(0)
}

func (x *AppScreeningMetaData) GetAffluenceScore() int32 {
	if x != nil {
		return x.AffluenceScore
	}
	return 0
}

func (x *AppScreeningMetaData) GetCreditReportPanMatched() common.BooleanEnum {
	if x != nil {
		return x.CreditReportPanMatched
	}
	return common.BooleanEnum(0)
}

// EKYCNameDOBValidationData stores info related to EKYC_NAME_DOB_VALIDATION stage
type EKYCNameDOBValidationData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// API retries for tracking errored vendor API responses
	Retries int32 `protobuf:"varint,1,opt,name=retries,proto3" json:"retries,omitempty"`
	// Validation failure description
	FailureDesc string `protobuf:"bytes,2,opt,name=failure_desc,json=failureDesc,proto3" json:"failure_desc,omitempty"`
	// API raw response
	RawResponse string `protobuf:"bytes,3,opt,name=raw_response,json=rawResponse,proto3" json:"raw_response,omitempty"`
}

func (x *EKYCNameDOBValidationData) Reset() {
	*x = EKYCNameDOBValidationData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EKYCNameDOBValidationData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EKYCNameDOBValidationData) ProtoMessage() {}

func (x *EKYCNameDOBValidationData) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EKYCNameDOBValidationData.ProtoReflect.Descriptor instead.
func (*EKYCNameDOBValidationData) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{21}
}

func (x *EKYCNameDOBValidationData) GetRetries() int32 {
	if x != nil {
		return x.Retries
	}
	return 0
}

func (x *EKYCNameDOBValidationData) GetFailureDesc() string {
	if x != nil {
		return x.FailureDesc
	}
	return ""
}

func (x *EKYCNameDOBValidationData) GetRawResponse() string {
	if x != nil {
		return x.RawResponse
	}
	return ""
}

type PanValidation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// block_till denotes the time till which the user will not be allowed to do ckyc
	BlockTill                  *timestamppb.Timestamp     `protobuf:"bytes,3,opt,name=block_till,json=blockTill,proto3" json:"block_till,omitempty"`
	PanValidationSuspendReason PanValidationSuspendReason `protobuf:"varint,4,opt,name=pan_validation_suspend_reason,json=panValidationSuspendReason,proto3,enum=user.onboarding.PanValidationSuspendReason" json:"pan_validation_suspend_reason,omitempty"`
}

func (x *PanValidation) Reset() {
	*x = PanValidation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PanValidation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PanValidation) ProtoMessage() {}

func (x *PanValidation) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PanValidation.ProtoReflect.Descriptor instead.
func (*PanValidation) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{22}
}

func (x *PanValidation) GetBlockTill() *timestamppb.Timestamp {
	if x != nil {
		return x.BlockTill
	}
	return nil
}

func (x *PanValidation) GetPanValidationSuspendReason() PanValidationSuspendReason {
	if x != nil {
		return x.PanValidationSuspendReason
	}
	return PanValidationSuspendReason_PAN_VALIDATION_SUSPEND_REASON_UNSPECIFIED
}

type VKYCMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Since we have a stateless probabilistic way to show vkyc option to user, this can be set to true to persist that info once vkyc is shown to user
	PerformVkycCheck bool `protobuf:"varint,1,opt,name=perform_vkyc_check,json=performVkycCheck,proto3" json:"perform_vkyc_check,omitempty"`
	// vkyc option used, current flow could be due to plain onboarding or for a ckyc L, S or O user
	VkycOption  VKYCOption   `protobuf:"varint,2,opt,name=vkyc_option,json=vkycOption,proto3,enum=user.onboarding.VKYCOption" json:"vkyc_option,omitempty"`
	InhouseVKYC *InhouseVKYC `protobuf:"bytes,3,opt,name=inhouse_v_k_y_c,json=inhouseVKYC,proto3" json:"inhouse_v_k_y_c,omitempty"`
}

func (x *VKYCMetadata) Reset() {
	*x = VKYCMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VKYCMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VKYCMetadata) ProtoMessage() {}

func (x *VKYCMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VKYCMetadata.ProtoReflect.Descriptor instead.
func (*VKYCMetadata) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{23}
}

func (x *VKYCMetadata) GetPerformVkycCheck() bool {
	if x != nil {
		return x.PerformVkycCheck
	}
	return false
}

func (x *VKYCMetadata) GetVkycOption() VKYCOption {
	if x != nil {
		return x.VkycOption
	}
	return VKYCOption_VKYC_OPTION_UNSPECIFIED
}

func (x *VKYCMetadata) GetInhouseVKYC() *InhouseVKYC {
	if x != nil {
		return x.InhouseVKYC
	}
	return nil
}

type InhouseVKYC struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// application id is for nr vkyc, used to get applicant details
	ApplicationId string `protobuf:"bytes,1,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	// application id is for nr vkyc, used to check call status
	CallId string `protobuf:"bytes,2,opt,name=call_id,json=callId,proto3" json:"call_id,omitempty"`
}

func (x *InhouseVKYC) Reset() {
	*x = InhouseVKYC{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InhouseVKYC) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InhouseVKYC) ProtoMessage() {}

func (x *InhouseVKYC) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InhouseVKYC.ProtoReflect.Descriptor instead.
func (*InhouseVKYC) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{24}
}

func (x *InhouseVKYC) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *InhouseVKYC) GetCallId() string {
	if x != nil {
		return x.CallId
	}
	return ""
}

type IntentSelectionMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// intent selected by user or the intent of the user identified through acquisition channel
	Selection OnboardingIntent `protobuf:"varint,1,opt,name=selection,proto3,enum=user.onboarding.OnboardingIntent" json:"selection,omitempty"`
	// list of intent choices that were showed to the user,
	// this will be empty in case intent was identified by acquisition channel
	Choices []OnboardingIntent `protobuf:"varint,2,rep,packed,name=choices,proto3,enum=user.onboarding.OnboardingIntent" json:"choices,omitempty"`
	// whether intent was received through acquisition channel
	AutoIntent         bool                `protobuf:"varint,3,opt,name=auto_intent,json=autoIntent,proto3" json:"auto_intent,omitempty"`
	IntentOverrideInfo *IntentOverrideInfo `protobuf:"bytes,4,opt,name=intent_override_info,json=intentOverrideInfo,proto3" json:"intent_override_info,omitempty"`
}

func (x *IntentSelectionMetadata) Reset() {
	*x = IntentSelectionMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IntentSelectionMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IntentSelectionMetadata) ProtoMessage() {}

func (x *IntentSelectionMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IntentSelectionMetadata.ProtoReflect.Descriptor instead.
func (*IntentSelectionMetadata) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{25}
}

func (x *IntentSelectionMetadata) GetSelection() OnboardingIntent {
	if x != nil {
		return x.Selection
	}
	return OnboardingIntent_ONBOARDING_INTENT_UNSPECIFIED
}

func (x *IntentSelectionMetadata) GetChoices() []OnboardingIntent {
	if x != nil {
		return x.Choices
	}
	return nil
}

func (x *IntentSelectionMetadata) GetAutoIntent() bool {
	if x != nil {
		return x.AutoIntent
	}
	return false
}

func (x *IntentSelectionMetadata) GetIntentOverrideInfo() *IntentOverrideInfo {
	if x != nil {
		return x.IntentOverrideInfo
	}
	return nil
}

type IntentOverrideInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// whether intent was overriden by any active leads
	OverriddenByActiveLead bool `protobuf:"varint,1,opt,name=overridden_by_active_lead,json=overriddenByActiveLead,proto3" json:"overridden_by_active_lead,omitempty"`
}

func (x *IntentOverrideInfo) Reset() {
	*x = IntentOverrideInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IntentOverrideInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IntentOverrideInfo) ProtoMessage() {}

func (x *IntentOverrideInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IntentOverrideInfo.ProtoReflect.Descriptor instead.
func (*IntentOverrideInfo) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{26}
}

func (x *IntentOverrideInfo) GetOverriddenByActiveLead() bool {
	if x != nil {
		return x.OverriddenByActiveLead
	}
	return false
}

type SoftIntentSelectionMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// soft intents selected by user
	Selection []OnboardingSoftIntent `protobuf:"varint,1,rep,packed,name=selection,proto3,enum=user.onboarding.OnboardingSoftIntent" json:"selection,omitempty"`
	// request id of the credit report download process
	CreditReportReqId string `protobuf:"bytes,2,opt,name=credit_report_req_id,json=creditReportReqId,proto3" json:"credit_report_req_id,omitempty"`
}

func (x *SoftIntentSelectionMetadata) Reset() {
	*x = SoftIntentSelectionMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SoftIntentSelectionMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SoftIntentSelectionMetadata) ProtoMessage() {}

func (x *SoftIntentSelectionMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SoftIntentSelectionMetadata.ProtoReflect.Descriptor instead.
func (*SoftIntentSelectionMetadata) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{27}
}

func (x *SoftIntentSelectionMetadata) GetSelection() []OnboardingSoftIntent {
	if x != nil {
		return x.Selection
	}
	return nil
}

func (x *SoftIntentSelectionMetadata) GetCreditReportReqId() string {
	if x != nil {
		return x.CreditReportReqId
	}
	return ""
}

// Details of Fi Lite onboarding
type FiLiteDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsEnabled    common.BooleanEnum `protobuf:"varint,1,opt,name=is_enabled,json=isEnabled,proto3,enum=api.typesv2.common.BooleanEnum" json:"is_enabled,omitempty"`
	FiLiteSource FiLiteSource       `protobuf:"varint,2,opt,name=fi_lite_source,json=fiLiteSource,proto3,enum=user.onboarding.FiLiteSource" json:"fi_lite_source,omitempty"`
	// Timestamp when a user converted to a fi lite user, this is updated only once and is never overwritten
	AccessibilityEnabledAt *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=accessibility_enabled_at,json=accessibilityEnabledAt,proto3" json:"accessibility_enabled_at,omitempty"`
}

func (x *FiLiteDetails) Reset() {
	*x = FiLiteDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FiLiteDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FiLiteDetails) ProtoMessage() {}

func (x *FiLiteDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FiLiteDetails.ProtoReflect.Descriptor instead.
func (*FiLiteDetails) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{28}
}

func (x *FiLiteDetails) GetIsEnabled() common.BooleanEnum {
	if x != nil {
		return x.IsEnabled
	}
	return common.BooleanEnum(0)
}

func (x *FiLiteDetails) GetFiLiteSource() FiLiteSource {
	if x != nil {
		return x.FiLiteSource
	}
	return FiLiteSource_FI_LITE_SOURCE_UNSPECIFIED
}

func (x *FiLiteDetails) GetAccessibilityEnabledAt() *timestamppb.Timestamp {
	if x != nil {
		return x.AccessibilityEnabledAt
	}
	return nil
}

type FeatureInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurrentStage                OnboardingStage                        `protobuf:"varint,1,opt,name=current_stage,json=currentStage,proto3,enum=user.onboarding.OnboardingStage" json:"current_stage,omitempty"`
	CompletedAt                 *timestamppb.Timestamp                 `protobuf:"bytes,2,opt,name=completed_at,json=completedAt,proto3" json:"completed_at,omitempty"`
	FeatureStatus               FeatureStatus                          `protobuf:"varint,3,opt,name=feature_status,json=featureStatus,proto3,enum=user.onboarding.FeatureStatus" json:"feature_status,omitempty"`
	FeatureOnboardingEntryPoint onboarding.FeatureOnboardingEntryPoint `protobuf:"varint,4,opt,name=feature_onboarding_entry_point,json=featureOnboardingEntryPoint,proto3,enum=api.typesv2.deeplink_screen_option.onboarding.FeatureOnboardingEntryPoint" json:"feature_onboarding_entry_point,omitempty"`
}

func (x *FeatureInfo) Reset() {
	*x = FeatureInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeatureInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeatureInfo) ProtoMessage() {}

func (x *FeatureInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeatureInfo.ProtoReflect.Descriptor instead.
func (*FeatureInfo) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{29}
}

func (x *FeatureInfo) GetCurrentStage() OnboardingStage {
	if x != nil {
		return x.CurrentStage
	}
	return OnboardingStage_ONBOARDING_STAGE_UNSPECIFIED
}

func (x *FeatureInfo) GetCompletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletedAt
	}
	return nil
}

func (x *FeatureInfo) GetFeatureStatus() FeatureStatus {
	if x != nil {
		return x.FeatureStatus
	}
	return FeatureStatus_FEATURE_STATUS_UNSPECIFIED
}

func (x *FeatureInfo) GetFeatureOnboardingEntryPoint() onboarding.FeatureOnboardingEntryPoint {
	if x != nil {
		return x.FeatureOnboardingEntryPoint
	}
	return onboarding.FeatureOnboardingEntryPoint(0)
}

// Details regarding the various onboarding journey per feature
type FeatureDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FeatureInfo map[string]*FeatureInfo `protobuf:"bytes,1,rep,name=feature_info,json=featureInfo,proto3" json:"feature_info,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *FeatureDetails) Reset() {
	*x = FeatureDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeatureDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeatureDetails) ProtoMessage() {}

func (x *FeatureDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeatureDetails.ProtoReflect.Descriptor instead.
func (*FeatureDetails) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{30}
}

func (x *FeatureDetails) GetFeatureInfo() map[string]*FeatureInfo {
	if x != nil {
		return x.FeatureInfo
	}
	return nil
}

type PanAadharLinkageDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// hash of the last 4 digits of aadhar received from ekyc name dob validation API
	EkycNameDobValidationAadharDigitsHash string `protobuf:"bytes,1,opt,name=ekyc_name_dob_validation_aadhar_digits_hash,json=ekycNameDobValidationAadharDigitsHash,proto3" json:"ekyc_name_dob_validation_aadhar_digits_hash,omitempty"`
	// hash of the last 4 digits of aadhar received from pan aadhar linkage API
	PanLinkedAadharDigitsHash string `protobuf:"bytes,2,opt,name=pan_linked_aadhar_digits_hash,json=panLinkedAadharDigitsHash,proto3" json:"pan_linked_aadhar_digits_hash,omitempty"`
	// boolean flag to store the mismatch of the hash
	IsAadharDigitsHashMismatch common.BooleanEnum `protobuf:"varint,3,opt,name=is_aadhar_digits_hash_mismatch,json=isAadharDigitsHashMismatch,proto3,enum=api.typesv2.common.BooleanEnum" json:"is_aadhar_digits_hash_mismatch,omitempty"`
	// boolean flag to store the pan aadhaar link status
	PanAadhaarLinked common.BooleanEnum `protobuf:"varint,4,opt,name=pan_aadhaar_linked,json=panAadhaarLinked,proto3,enum=api.typesv2.common.BooleanEnum" json:"pan_aadhaar_linked,omitempty"`
	// hash of the last 2 digits of aadhar received from ekyc name dob validation API
	EkycNameDobValidationAadharLast2DigitsHash string `protobuf:"bytes,5,opt,name=ekyc_name_dob_validation_aadhar_last2_digits_hash,json=ekycNameDobValidationAadharLast2DigitsHash,proto3" json:"ekyc_name_dob_validation_aadhar_last2_digits_hash,omitempty"`
	// hash of the last 2 digits of aadhar received from pan aadhar linkage API
	PanLinkedAadhaarLast2DigitsHash string `protobuf:"bytes,6,opt,name=pan_linked_aadhaar_last2_digits_hash,json=panLinkedAadhaarLast2DigitsHash,proto3" json:"pan_linked_aadhaar_last2_digits_hash,omitempty"`
}

func (x *PanAadharLinkageDetails) Reset() {
	*x = PanAadharLinkageDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PanAadharLinkageDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PanAadharLinkageDetails) ProtoMessage() {}

func (x *PanAadharLinkageDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PanAadharLinkageDetails.ProtoReflect.Descriptor instead.
func (*PanAadharLinkageDetails) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{31}
}

func (x *PanAadharLinkageDetails) GetEkycNameDobValidationAadharDigitsHash() string {
	if x != nil {
		return x.EkycNameDobValidationAadharDigitsHash
	}
	return ""
}

func (x *PanAadharLinkageDetails) GetPanLinkedAadharDigitsHash() string {
	if x != nil {
		return x.PanLinkedAadharDigitsHash
	}
	return ""
}

func (x *PanAadharLinkageDetails) GetIsAadharDigitsHashMismatch() common.BooleanEnum {
	if x != nil {
		return x.IsAadharDigitsHashMismatch
	}
	return common.BooleanEnum(0)
}

func (x *PanAadharLinkageDetails) GetPanAadhaarLinked() common.BooleanEnum {
	if x != nil {
		return x.PanAadhaarLinked
	}
	return common.BooleanEnum(0)
}

func (x *PanAadharLinkageDetails) GetEkycNameDobValidationAadharLast2DigitsHash() string {
	if x != nil {
		return x.EkycNameDobValidationAadharLast2DigitsHash
	}
	return ""
}

func (x *PanAadharLinkageDetails) GetPanLinkedAadhaarLast2DigitsHash() string {
	if x != nil {
		return x.PanLinkedAadhaarLast2DigitsHash
	}
	return ""
}

type PassportVerificationMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// stores client_request_id needed to get the passport data from DocExtraction service
	DocExtractClientReqId              string                                                     `protobuf:"bytes,1,opt,name=doc_extract_client_req_id,json=docExtractClientReqId,proto3" json:"doc_extract_client_req_id,omitempty"`
	PassportFrontUrl                   string                                                     `protobuf:"bytes,2,opt,name=passport_front_url,json=passportFrontUrl,proto3" json:"passport_front_url,omitempty"`
	PassportBackUrl                    string                                                     `protobuf:"bytes,3,opt,name=passport_back_url,json=passportBackUrl,proto3" json:"passport_back_url,omitempty"`
	Status                             PassportVerificationStatus                                 `protobuf:"varint,4,opt,name=status,proto3,enum=user.onboarding.PassportVerificationStatus" json:"status,omitempty"`
	PassportFrontDocExtractClientReqId string                                                     `protobuf:"bytes,5,opt,name=passport_front_doc_extract_client_req_id,json=passportFrontDocExtractClientReqId,proto3" json:"passport_front_doc_extract_client_req_id,omitempty"`
	PassportBackDocExtractClientReqId  string                                                     `protobuf:"bytes,6,opt,name=passport_back_doc_extract_client_req_id,json=passportBackDocExtractClientReqId,proto3" json:"passport_back_doc_extract_client_req_id,omitempty"`
	AttemptedAt                        *timestamppb.Timestamp                                     `protobuf:"bytes,7,opt,name=attempted_at,json=attemptedAt,proto3" json:"attempted_at,omitempty"`
	CurrentAttemptsInCooldownWindow    int64                                                      `protobuf:"varint,8,opt,name=current_attempts_in_cooldown_window,json=currentAttemptsInCooldownWindow,proto3" json:"current_attempts_in_cooldown_window,omitempty"`
	FaceImageUrl                       string                                                     `protobuf:"bytes,9,opt,name=face_image_url,json=faceImageUrl,proto3" json:"face_image_url,omitempty"`
	VerificationVendorApiResults       *PassportVerificationMetadata_VerificationVendorAPIResults `protobuf:"bytes,10,opt,name=verification_vendor_api_results,json=verificationVendorApiResults,proto3" json:"verification_vendor_api_results,omitempty"`
	// old passport file number is used to verify old passport details from PSK
	// in case if new passport is issued outside India
	OldPassportFileNumber string                            `protobuf:"bytes,11,opt,name=old_passport_file_number,json=oldPassportFileNumber,proto3" json:"old_passport_file_number,omitempty"`
	FailureReason         PassportVerificationFailureReason `protobuf:"varint,12,opt,name=failure_reason,json=failureReason,proto3,enum=user.onboarding.PassportVerificationFailureReason" json:"failure_reason,omitempty"`
	// ARN of the passport issued outside india
	PassportArn string `protobuf:"bytes,13,opt,name=passport_arn,json=passportArn,proto3" json:"passport_arn,omitempty"`
	// Manual review annotation for the passport verification
	ManualReviewAnnotation *PassportManualReviewDetails `protobuf:"bytes,14,opt,name=manual_review_annotation,json=manualReviewAnnotation,proto3" json:"manual_review_annotation,omitempty"`
}

func (x *PassportVerificationMetadata) Reset() {
	*x = PassportVerificationMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PassportVerificationMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PassportVerificationMetadata) ProtoMessage() {}

func (x *PassportVerificationMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PassportVerificationMetadata.ProtoReflect.Descriptor instead.
func (*PassportVerificationMetadata) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{32}
}

func (x *PassportVerificationMetadata) GetDocExtractClientReqId() string {
	if x != nil {
		return x.DocExtractClientReqId
	}
	return ""
}

func (x *PassportVerificationMetadata) GetPassportFrontUrl() string {
	if x != nil {
		return x.PassportFrontUrl
	}
	return ""
}

func (x *PassportVerificationMetadata) GetPassportBackUrl() string {
	if x != nil {
		return x.PassportBackUrl
	}
	return ""
}

func (x *PassportVerificationMetadata) GetStatus() PassportVerificationStatus {
	if x != nil {
		return x.Status
	}
	return PassportVerificationStatus_PASSPORT_VERIFICATION_STATUS_UNSPECIFIED
}

func (x *PassportVerificationMetadata) GetPassportFrontDocExtractClientReqId() string {
	if x != nil {
		return x.PassportFrontDocExtractClientReqId
	}
	return ""
}

func (x *PassportVerificationMetadata) GetPassportBackDocExtractClientReqId() string {
	if x != nil {
		return x.PassportBackDocExtractClientReqId
	}
	return ""
}

func (x *PassportVerificationMetadata) GetAttemptedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.AttemptedAt
	}
	return nil
}

func (x *PassportVerificationMetadata) GetCurrentAttemptsInCooldownWindow() int64 {
	if x != nil {
		return x.CurrentAttemptsInCooldownWindow
	}
	return 0
}

func (x *PassportVerificationMetadata) GetFaceImageUrl() string {
	if x != nil {
		return x.FaceImageUrl
	}
	return ""
}

func (x *PassportVerificationMetadata) GetVerificationVendorApiResults() *PassportVerificationMetadata_VerificationVendorAPIResults {
	if x != nil {
		return x.VerificationVendorApiResults
	}
	return nil
}

func (x *PassportVerificationMetadata) GetOldPassportFileNumber() string {
	if x != nil {
		return x.OldPassportFileNumber
	}
	return ""
}

func (x *PassportVerificationMetadata) GetFailureReason() PassportVerificationFailureReason {
	if x != nil {
		return x.FailureReason
	}
	return PassportVerificationFailureReason_PASSPORT_VERIFICATION_FAILURE_REASON_UNSPECIFIED
}

func (x *PassportVerificationMetadata) GetPassportArn() string {
	if x != nil {
		return x.PassportArn
	}
	return ""
}

func (x *PassportVerificationMetadata) GetManualReviewAnnotation() *PassportManualReviewDetails {
	if x != nil {
		return x.ManualReviewAnnotation
	}
	return nil
}

type PassportManualReviewDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Verdict    Verdict                `protobuf:"varint,1,opt,name=verdict,proto3,enum=user.onboarding.Verdict" json:"verdict,omitempty"`
	ReviewedBy string                 `protobuf:"bytes,2,opt,name=reviewed_by,json=reviewedBy,proto3" json:"reviewed_by,omitempty"`
	ReviewedOn *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=reviewed_on,json=reviewedOn,proto3" json:"reviewed_on,omitempty"`
	Remarks    string                 `protobuf:"bytes,4,opt,name=remarks,proto3" json:"remarks,omitempty"`
	// Screenshots/proof image of the PSK portal
	ReviewProofImageUrl string `protobuf:"bytes,5,opt,name=review_proof_image_url,json=reviewProofImageUrl,proto3" json:"review_proof_image_url,omitempty"`
}

func (x *PassportManualReviewDetails) Reset() {
	*x = PassportManualReviewDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PassportManualReviewDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PassportManualReviewDetails) ProtoMessage() {}

func (x *PassportManualReviewDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PassportManualReviewDetails.ProtoReflect.Descriptor instead.
func (*PassportManualReviewDetails) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{33}
}

func (x *PassportManualReviewDetails) GetVerdict() Verdict {
	if x != nil {
		return x.Verdict
	}
	return Verdict_VERDICT_UNSPECIFIED
}

func (x *PassportManualReviewDetails) GetReviewedBy() string {
	if x != nil {
		return x.ReviewedBy
	}
	return ""
}

func (x *PassportManualReviewDetails) GetReviewedOn() *timestamppb.Timestamp {
	if x != nil {
		return x.ReviewedOn
	}
	return nil
}

func (x *PassportManualReviewDetails) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

func (x *PassportManualReviewDetails) GetReviewProofImageUrl() string {
	if x != nil {
		return x.ReviewProofImageUrl
	}
	return ""
}

type CrossValidationResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Verdict CrossValidationVerdict `protobuf:"varint,1,opt,name=verdict,proto3,enum=user.onboarding.CrossValidationVerdict" json:"verdict,omitempty"`
	// Below fields are populated only if verdict is failed
	// check for which verdict has failed
	Check                           CrossValidationCheck             `protobuf:"varint,2,opt,name=check,proto3,enum=user.onboarding.CrossValidationCheck" json:"check,omitempty"`
	FailureReason                   CrossValidationFailureReason     `protobuf:"varint,3,opt,name=failure_reason,json=failureReason,proto3,enum=user.onboarding.CrossValidationFailureReason" json:"failure_reason,omitempty"`
	DataSource_1                    CrossValidationDataSource        `protobuf:"varint,4,opt,name=data_source_1,json=dataSource1,proto3,enum=user.onboarding.CrossValidationDataSource" json:"data_source_1,omitempty"`
	DataSource_2                    CrossValidationDataSource        `protobuf:"varint,5,opt,name=data_source_2,json=dataSource2,proto3,enum=user.onboarding.CrossValidationDataSource" json:"data_source_2,omitempty"`
	CrossValidationManualReviewInfo *CrossValidationManualReviewInfo `protobuf:"bytes,6,opt,name=cross_validation_manual_review_info,json=crossValidationManualReviewInfo,proto3" json:"cross_validation_manual_review_info,omitempty"`
}

func (x *CrossValidationResult) Reset() {
	*x = CrossValidationResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CrossValidationResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CrossValidationResult) ProtoMessage() {}

func (x *CrossValidationResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CrossValidationResult.ProtoReflect.Descriptor instead.
func (*CrossValidationResult) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{34}
}

func (x *CrossValidationResult) GetVerdict() CrossValidationVerdict {
	if x != nil {
		return x.Verdict
	}
	return CrossValidationVerdict_CROSS_VALIDATION_VERDICT_UNSPECIFIED
}

func (x *CrossValidationResult) GetCheck() CrossValidationCheck {
	if x != nil {
		return x.Check
	}
	return CrossValidationCheck_CROSS_VALIDATION_CHECK_UNSPECIFIED
}

func (x *CrossValidationResult) GetFailureReason() CrossValidationFailureReason {
	if x != nil {
		return x.FailureReason
	}
	return CrossValidationFailureReason_CROSS_VALIDATION_FAILURE_REASON_UNSPECIFIED
}

func (x *CrossValidationResult) GetDataSource_1() CrossValidationDataSource {
	if x != nil {
		return x.DataSource_1
	}
	return CrossValidationDataSource_CROSS_VALIDATION_DATA_SOURCE_UNSPECIFIED
}

func (x *CrossValidationResult) GetDataSource_2() CrossValidationDataSource {
	if x != nil {
		return x.DataSource_2
	}
	return CrossValidationDataSource_CROSS_VALIDATION_DATA_SOURCE_UNSPECIFIED
}

func (x *CrossValidationResult) GetCrossValidationManualReviewInfo() *CrossValidationManualReviewInfo {
	if x != nil {
		return x.CrossValidationManualReviewInfo
	}
	return nil
}

type CrossValidationManualReviewInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AgentEmail string                 `protobuf:"bytes,1,opt,name=agent_email,json=agentEmail,proto3" json:"agent_email,omitempty"`
	Remarks    string                 `protobuf:"bytes,2,opt,name=remarks,proto3" json:"remarks,omitempty"`
	ReviewedAt *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=reviewed_at,json=reviewedAt,proto3" json:"reviewed_at,omitempty"`
}

func (x *CrossValidationManualReviewInfo) Reset() {
	*x = CrossValidationManualReviewInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CrossValidationManualReviewInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CrossValidationManualReviewInfo) ProtoMessage() {}

func (x *CrossValidationManualReviewInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CrossValidationManualReviewInfo.ProtoReflect.Descriptor instead.
func (*CrossValidationManualReviewInfo) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{35}
}

func (x *CrossValidationManualReviewInfo) GetAgentEmail() string {
	if x != nil {
		return x.AgentEmail
	}
	return ""
}

func (x *CrossValidationManualReviewInfo) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

func (x *CrossValidationManualReviewInfo) GetReviewedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ReviewedAt
	}
	return nil
}

type CountryIdVerificationMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// stores client_request_id needed to get the country id (emirates id in case of UAE) data from DocExtraction service
	DocExtractClientReqId string `protobuf:"bytes,1,opt,name=doc_extract_client_req_id,json=docExtractClientReqId,proto3" json:"doc_extract_client_req_id,omitempty"`
}

func (x *CountryIdVerificationMetadata) Reset() {
	*x = CountryIdVerificationMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CountryIdVerificationMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountryIdVerificationMetadata) ProtoMessage() {}

func (x *CountryIdVerificationMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountryIdVerificationMetadata.ProtoReflect.Descriptor instead.
func (*CountryIdVerificationMetadata) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{36}
}

func (x *CountryIdVerificationMetadata) GetDocExtractClientReqId() string {
	if x != nil {
		return x.DocExtractClientReqId
	}
	return ""
}

type FeatureIntentSelectionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// whether the user has selected the feature on hard intent screen or not
	SelectedAsHardIntent bool `protobuf:"varint,1,opt,name=selected_as_hard_intent,json=selectedAsHardIntent,proto3" json:"selected_as_hard_intent,omitempty"`
	// list of soft intents selected by the user and fall within the feature
	// eg - for SA feature: ONBOARDING_SOFT_INTENT_CASHBACK_ON_UPI, ONBOARDING_SOFT_INTENT_ZERO_BALANCE_SAVINGS_ACCOUNT, etc
	SelectedSoftIntents []OnboardingSoftIntent `protobuf:"varint,2,rep,packed,name=selected_soft_intents,json=selectedSoftIntents,proto3,enum=user.onboarding.OnboardingSoftIntent" json:"selected_soft_intents,omitempty"`
}

func (x *FeatureIntentSelectionInfo) Reset() {
	*x = FeatureIntentSelectionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeatureIntentSelectionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeatureIntentSelectionInfo) ProtoMessage() {}

func (x *FeatureIntentSelectionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeatureIntentSelectionInfo.ProtoReflect.Descriptor instead.
func (*FeatureIntentSelectionInfo) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{37}
}

func (x *FeatureIntentSelectionInfo) GetSelectedAsHardIntent() bool {
	if x != nil {
		return x.SelectedAsHardIntent
	}
	return false
}

func (x *FeatureIntentSelectionInfo) GetSelectedSoftIntents() []OnboardingSoftIntent {
	if x != nil {
		return x.SelectedSoftIntents
	}
	return nil
}

type FeatureEligibility struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                 FeatureEligibility_Status                  `protobuf:"varint,1,opt,name=status,proto3,enum=user.onboarding.FeatureEligibility_Status" json:"status,omitempty"`
	AdditionalDataRequired *FeatureEligibility_AdditionalDataRequired `protobuf:"bytes,2,opt,name=additional_data_required,json=additionalDataRequired,proto3" json:"additional_data_required,omitempty"`
}

func (x *FeatureEligibility) Reset() {
	*x = FeatureEligibility{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeatureEligibility) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeatureEligibility) ProtoMessage() {}

func (x *FeatureEligibility) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeatureEligibility.ProtoReflect.Descriptor instead.
func (*FeatureEligibility) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{38}
}

func (x *FeatureEligibility) GetStatus() FeatureEligibility_Status {
	if x != nil {
		return x.Status
	}
	return FeatureEligibility_STATUS_UNSPECIFIED
}

func (x *FeatureEligibility) GetAdditionalDataRequired() *FeatureEligibility_AdditionalDataRequired {
	if x != nil {
		return x.AdditionalDataRequired
	}
	return nil
}

type FeatureLifecycle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Feature Feature `protobuf:"varint,1,opt,name=feature,proto3,enum=user.onboarding.Feature" json:"feature,omitempty"`
	// intent selection info of the feature
	IntentSelectionInfo *FeatureIntentSelectionInfo `protobuf:"bytes,2,opt,name=intent_selection_info,json=intentSelectionInfo,proto3" json:"intent_selection_info,omitempty"`
	// eligibility status of the feature
	EligibilityStatus *FeatureEligibility `protobuf:"bytes,4,opt,name=eligibility_status,json=eligibilityStatus,proto3" json:"eligibility_status,omitempty"`
	// activation status of the feature
	ActivationStatus FeatureStatus `protobuf:"varint,5,opt,name=activation_status,json=activationStatus,proto3,enum=user.onboarding.FeatureStatus" json:"activation_status,omitempty"`
}

func (x *FeatureLifecycle) Reset() {
	*x = FeatureLifecycle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeatureLifecycle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeatureLifecycle) ProtoMessage() {}

func (x *FeatureLifecycle) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeatureLifecycle.ProtoReflect.Descriptor instead.
func (*FeatureLifecycle) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{39}
}

func (x *FeatureLifecycle) GetFeature() Feature {
	if x != nil {
		return x.Feature
	}
	return Feature_FEATURE_UNSPECIFIED
}

func (x *FeatureLifecycle) GetIntentSelectionInfo() *FeatureIntentSelectionInfo {
	if x != nil {
		return x.IntentSelectionInfo
	}
	return nil
}

func (x *FeatureLifecycle) GetEligibilityStatus() *FeatureEligibility {
	if x != nil {
		return x.EligibilityStatus
	}
	return nil
}

func (x *FeatureLifecycle) GetActivationStatus() FeatureStatus {
	if x != nil {
		return x.ActivationStatus
	}
	return FeatureStatus_FEATURE_STATUS_UNSPECIFIED
}

// Results of Passport OCR data being matched with passport details fetched from PSK (Passport Seva Kendra) services.
type PassportVerificationMetadata_VerificationVendorAPIResults struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PassportNumberMatch typesv2.Verdict `protobuf:"varint,1,opt,name=passport_number_match,json=passportNumberMatch,proto3,enum=api.typesv2.Verdict" json:"passport_number_match,omitempty"`
	UserNameMatch       typesv2.Verdict `protobuf:"varint,2,opt,name=user_name_match,json=userNameMatch,proto3,enum=api.typesv2.Verdict" json:"user_name_match,omitempty"`
	DateOfIssueMatch    typesv2.Verdict `protobuf:"varint,3,opt,name=date_of_issue_match,json=dateOfIssueMatch,proto3,enum=api.typesv2.Verdict" json:"date_of_issue_match,omitempty"`
}

func (x *PassportVerificationMetadata_VerificationVendorAPIResults) Reset() {
	*x = PassportVerificationMetadata_VerificationVendorAPIResults{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PassportVerificationMetadata_VerificationVendorAPIResults) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PassportVerificationMetadata_VerificationVendorAPIResults) ProtoMessage() {}

func (x *PassportVerificationMetadata_VerificationVendorAPIResults) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PassportVerificationMetadata_VerificationVendorAPIResults.ProtoReflect.Descriptor instead.
func (*PassportVerificationMetadata_VerificationVendorAPIResults) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{32, 0}
}

func (x *PassportVerificationMetadata_VerificationVendorAPIResults) GetPassportNumberMatch() typesv2.Verdict {
	if x != nil {
		return x.PassportNumberMatch
	}
	return typesv2.Verdict(0)
}

func (x *PassportVerificationMetadata_VerificationVendorAPIResults) GetUserNameMatch() typesv2.Verdict {
	if x != nil {
		return x.UserNameMatch
	}
	return typesv2.Verdict(0)
}

func (x *PassportVerificationMetadata_VerificationVendorAPIResults) GetDateOfIssueMatch() typesv2.Verdict {
	if x != nil {
		return x.DateOfIssueMatch
	}
	return typesv2.Verdict(0)
}

// additional data required to determine eligibility
type FeatureEligibility_AdditionalDataRequired struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// deeplink to redirect to for collecting data
	Deeplink *deeplink.Deeplink `protobuf:"bytes,1,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
}

func (x *FeatureEligibility_AdditionalDataRequired) Reset() {
	*x = FeatureEligibility_AdditionalDataRequired{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeatureEligibility_AdditionalDataRequired) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeatureEligibility_AdditionalDataRequired) ProtoMessage() {}

func (x *FeatureEligibility_AdditionalDataRequired) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeatureEligibility_AdditionalDataRequired.ProtoReflect.Descriptor instead.
func (*FeatureEligibility_AdditionalDataRequired) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP(), []int{38, 0}
}

func (x *FeatureEligibility_AdditionalDataRequired) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

var File_api_user_onboarding_internal_onboarding_details_proto protoreflect.FileDescriptor

var file_api_user_onboarding_internal_onboarding_details_proto_rawDesc = []byte{
	0x0a, 0x35, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x6f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0f, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x1a, 0x13, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61,
	0x72, 0x64, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61,
	0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x61, 0x70, 0x70, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x6b,
	0x79, 0x63, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x6b, 0x79, 0x63, 0x5f,
	0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x61,
	0x70, 0x69, 0x2f, 0x6b, 0x79, 0x63, 0x2f, 0x6b, 0x79, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x4a, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x2f, 0x66, 0x69, 0x5f, 0x6c, 0x69, 0x74, 0x65, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x76, 0x65, 0x72, 0x64,
	0x69, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x75,
	0x73, 0x65, 0x72, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69,
	0x6e, 0x67, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xdc, 0x08, 0x0a, 0x11, 0x4f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x42, 0x0a, 0x0d, 0x73,
	0x74, 0x61, 0x67, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x0c, 0x73, 0x74, 0x61, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x4e, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x45, 0x0a, 0x09, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x28, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x52, 0x08, 0x63, 0x61,
	0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2d, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x6e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x45, 0x0a, 0x0e, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x74, 0x61,
	0x67, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x0d, 0x73, 0x74, 0x61, 0x67,
	0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x5a, 0x0a, 0x18, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f,
	0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x61, 0x67, 0x65,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x16, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x67, 0x65,
	0x12, 0x32, 0x0a, 0x07, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x18, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x52, 0x07, 0x66, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x66, 0x69, 0x5f, 0x6c, 0x69, 0x74, 0x65, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x46, 0x69, 0x4c, 0x69, 0x74, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0d, 0x66,
	0x69, 0x4c, 0x69, 0x74, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x48, 0x0a, 0x0f,
	0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x65, 0x0a, 0x1a, 0x70, 0x61, 0x6e, 0x5f, 0x61, 0x61, 0x64, 0x68, 0x61, 0x72, 0x5f,
	0x6c, 0x69, 0x6e, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x61, 0x6e, 0x41, 0x61, 0x64, 0x68, 0x61,
	0x72, 0x4c, 0x69, 0x6e, 0x6b, 0x61, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x17, 0x70, 0x61, 0x6e, 0x41, 0x61, 0x64, 0x68, 0x61, 0x72, 0x4c, 0x69, 0x6e, 0x6b, 0x61, 0x67,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x5f, 0x0a, 0x18, 0x73, 0x74, 0x61, 0x67,
	0x65, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x74, 0x61,
	0x67, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x4c, 0x61, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x52, 0x15, 0x73, 0x74, 0x61, 0x67, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x4c, 0x61, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x81, 0x01, 0x0a, 0x1a, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x22, 0x5d, 0x0a,
	0x17, 0x43, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x12, 0x42, 0x0a, 0x0c, 0x63, 0x61, 0x72, 0x64,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x43, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x0b, 0x63, 0x61, 0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x66, 0x0a, 0x0e,
	0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x43, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17,
	0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x0f, 0x62, 0x61, 0x73, 0x69, 0x63,
	0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x42, 0x61, 0x73, 0x69, 0x63, 0x43, 0x61, 0x72,
	0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x62, 0x61, 0x73, 0x69, 0x63, 0x43, 0x61, 0x72, 0x64,
	0x49, 0x6e, 0x66, 0x6f, 0x22, 0xc1, 0x01, 0x0a, 0x0c, 0x53, 0x74, 0x61, 0x67, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x54, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x6d,
	0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x53,
	0x74, 0x61, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x53, 0x74, 0x61, 0x67,
	0x65, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x73,
	0x74, 0x61, 0x67, 0x65, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x1a, 0x5b, 0x0a, 0x11, 0x53,
	0x74, 0x61, 0x67, 0x65, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x30, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xfd, 0x01, 0x0a, 0x0b, 0x4b, 0x59, 0x43,
	0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x37, 0x0a, 0x09, 0x65, 0x78, 0x70, 0x69,
	0x72, 0x79, 0x5f, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x41,
	0x74, 0x12, 0x42, 0x0a, 0x16, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x6b, 0x79, 0x63, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x0d, 0x2e, 0x6b, 0x79, 0x63, 0x2e, 0x4b, 0x59, 0x43, 0x4c, 0x65, 0x76, 0x65, 0x6c,
	0x52, 0x13, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x4b, 0x79, 0x63,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x33, 0x0a, 0x0c, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x6b, 0x79,
	0x63, 0x2e, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x66,
	0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3c, 0x0a, 0x11, 0x63, 0x6b,
	0x79, 0x63, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x6b, 0x79, 0x63, 0x2e, 0x46, 0x61, 0x69, 0x6c,
	0x75, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x63, 0x6b, 0x79, 0x63, 0x46, 0x61, 0x69,
	0x6c, 0x75, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0xe9, 0x01, 0x0a, 0x09, 0x53, 0x74, 0x61,
	0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x36, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x42,
	0x0a, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x25, 0x0a,
	0x0e, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xd8, 0x02, 0x0a, 0x15, 0x53, 0x74, 0x61, 0x67, 0x65, 0x50, 0x72,
	0x6f, 0x63, 0x4c, 0x61, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x36, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x67, 0x65,
	0x52, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x12, 0x82, 0x01, 0x0a, 0x1a, 0x61, 0x6e, 0x61, 0x6c,
	0x79, 0x74, 0x69, 0x63, 0x73, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x53,
	0x74, 0x61, 0x67, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x4c, 0x61, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x18, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x1a, 0x4b, 0x0a, 0x1d, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0xf2, 0x02, 0x0a, 0x0c, 0x50, 0x41, 0x4e, 0x4e, 0x61, 0x6d, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x12, 0x39, 0x0a, 0x19, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x65, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x16, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x4d, 0x61, 0x74, 0x63, 0x68, 0x50, 0x61, 0x73, 0x73, 0x65, 0x64, 0x12, 0x37, 0x0a, 0x18, 0x69,
	0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63,
	0x68, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x15, 0x69,
	0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53,
	0x63, 0x6f, 0x72, 0x65, 0x12, 0x31, 0x0a, 0x15, 0x6f, 0x6c, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x65, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x12, 0x6f, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x74, 0x63,
	0x68, 0x50, 0x61, 0x73, 0x73, 0x65, 0x64, 0x12, 0x2f, 0x0a, 0x14, 0x6f, 0x6c, 0x64, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x11, 0x6f, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x4d, 0x61,
	0x74, 0x63, 0x68, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x48, 0x0a, 0x1f, 0x69, 0x6e, 0x68, 0x6f,
	0x75, 0x73, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x66,
	0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x64, 0x69, 0x63, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x1b, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x44, 0x69,
	0x63, 0x74, 0x12, 0x40, 0x0a, 0x1d, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x6d, 0x61,
	0x74, 0x63, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x19, 0x69, 0x6e, 0x68, 0x6f, 0x75,
	0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x69, 0x73, 0x6b, 0x4d,
	0x61, 0x74, 0x63, 0x68, 0x22, 0xc5, 0x16, 0x0a, 0x0d, 0x53, 0x74, 0x61, 0x67, 0x65, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x3f, 0x0a, 0x0c, 0x6b, 0x79, 0x63, 0x5f, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x4b,
	0x59, 0x43, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x0b, 0x6b, 0x79, 0x63, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x55, 0x0a, 0x0d, 0x64, 0x65, 0x64, 0x75, 0x70,
	0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f,
	0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x64, 0x75, 0x70, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x0c, 0x64, 0x65, 0x64, 0x75, 0x70, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x5c,
	0x0a, 0x11, 0x6b, 0x79, 0x63, 0x5f, 0x64, 0x65, 0x64, 0x75, 0x70, 0x65, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61,
	0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x44,
	0x65, 0x64, 0x75, 0x70, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0f, 0x6b, 0x79, 0x63,
	0x44, 0x65, 0x64, 0x75, 0x70, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x33, 0x0a, 0x16,
	0x6b, 0x79, 0x63, 0x5f, 0x64, 0x65, 0x64, 0x75, 0x70, 0x65, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x79,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x6b, 0x79,
	0x63, 0x44, 0x65, 0x64, 0x75, 0x70, 0x65, 0x52, 0x65, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x43, 0x0a, 0x0e, 0x70, 0x61, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x41, 0x4e, 0x4e,
	0x61, 0x6d, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x0c, 0x70, 0x61, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x53, 0x0a, 0x12, 0x61, 0x70, 0x70, 0x5f, 0x73, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x25, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x70, 0x70, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e,
	0x67, 0x4d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x52, 0x10, 0x61, 0x70, 0x70, 0x53, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12, 0x56, 0x0a, 0x15, 0x64,
	0x65, 0x62, 0x69, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x63,
	0x68, 0x65, 0x63, 0x6b, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x65, 0x62,
	0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52,
	0x12, 0x64, 0x65, 0x62, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x12, 0x63, 0x0a, 0x18, 0x65, 0x6b, 0x79, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x5f, 0x64, 0x6f, 0x62, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x4b, 0x59, 0x43, 0x4e, 0x61, 0x6d, 0x65,
	0x44, 0x4f, 0x42, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x15, 0x65, 0x6b, 0x79, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x44, 0x6f, 0x62, 0x56, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x45, 0x0a, 0x0e, 0x70, 0x61, 0x6e, 0x5f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x2e, 0x50, 0x61, 0x6e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0d, 0x70, 0x61, 0x6e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x58, 0x0a, 0x15, 0x75, 0x5f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x63,
	0x6b, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x55, 0x4e, 0x4e, 0x61, 0x6d, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x42, 0x02, 0x18, 0x01, 0x52, 0x11, 0x75, 0x4e, 0x4e, 0x61, 0x6d, 0x65, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x7e, 0x0a, 0x23, 0x70, 0x72, 0x65,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x64, 0x75, 0x70, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x64, 0x75,
	0x70, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x1f, 0x70, 0x72, 0x65, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x64,
	0x75, 0x70, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x6b, 0x0a, 0x1c, 0x70, 0x61, 0x6e,
	0x5f, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x61,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x50, 0x61, 0x6e, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x52, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x19, 0x70, 0x61, 0x6e,
	0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x41, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x42, 0x0a, 0x0d, 0x76, 0x6b, 0x79, 0x63, 0x5f, 0x6d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x56, 0x4b, 0x59, 0x43, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x0c, 0x76, 0x6b,
	0x79, 0x63, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x3a, 0x0a, 0x1a, 0x67, 0x6d,
	0x61, 0x69, 0x6c, 0x5f, 0x70, 0x61, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6d, 0x61, 0x74,
	0x63, 0x68, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x02, 0x52, 0x16,
	0x67, 0x6d, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x74, 0x63,
	0x68, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x5e, 0x0a, 0x17, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52,
	0x15, 0x72, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x33, 0x0a, 0x16, 0x64, 0x65, 0x64, 0x75, 0x70, 0x65,
	0x5f, 0x64, 0x6f, 0x62, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x64, 0x65, 0x64, 0x75, 0x70, 0x65, 0x44, 0x6f,
	0x62, 0x52, 0x65, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x62, 0x0a, 0x14, 0x6c,
	0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x64, 0x65, 0x64, 0x75, 0x70, 0x65, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61,
	0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x44,
	0x65, 0x64, 0x75, 0x70, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x12, 0x6c, 0x61, 0x74,
	0x65, 0x73, 0x74, 0x44, 0x65, 0x64, 0x75, 0x70, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x4e, 0x0a, 0x11, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x4c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x10, 0x6c,
	0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12,
	0x4d, 0x0a, 0x18, 0x75, 0x5f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x63,
	0x6b, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x76, 0x32, 0x18, 0x13, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x16, 0x2e, 0x6b, 0x79, 0x63, 0x2e, 0x55, 0x4e, 0x4e, 0x61, 0x6d, 0x65, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x13, 0x75, 0x4e, 0x4e, 0x61, 0x6d,
	0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x56, 0x32, 0x12, 0x64,
	0x0a, 0x19, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x14, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x28, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x17, 0x69, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x12, 0x71, 0x0a, 0x1e, 0x70, 0x72, 0x65, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x50,
	0x72, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x1b, 0x70, 0x72, 0x65, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x74, 0x0a, 0x1f, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2d, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52,
	0x1c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x71, 0x0a,
	0x1e, 0x73, 0x6f, 0x66, 0x74, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x6f, 0x66, 0x74, 0x49, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x52, 0x1b, 0x73, 0x6f, 0x66, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x53,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x12, 0x73, 0x0a, 0x1e, 0x70, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x76, 0x65, 0x72,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x61, 0x73, 0x73, 0x70,
	0x6f, 0x72, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x1c, 0x70, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72,
	0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x77, 0x0a, 0x20, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x5f, 0x69, 0x64, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2e, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x49, 0x64, 0x56, 0x65, 0x72, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52,
	0x1d, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x49, 0x64, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x76,
	0x0a, 0x24, 0x6e, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x63,
	0x72, 0x6f, 0x73, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x43,
	0x72, 0x6f, 0x73, 0x73, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x52, 0x20, 0x6e, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x69, 0x64, 0x65, 0x6e,
	0x74, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x45, 0x0a, 0x20, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x61,
	0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x74, 0x6f, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x61,
	0x6d, 0x62, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x1b, 0x75, 0x73, 0x65, 0x72, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x54, 0x6f, 0x4f,
	0x70, 0x65, 0x6e, 0x41, 0x6d, 0x62, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x6b, 0x0a,
	0x1c, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x66,
	0x65, 0x74, 0x63, 0x68, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x1c, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x46, 0x65, 0x74, 0x63, 0x68, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52,
	0x19, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x65, 0x74,
	0x63, 0x68, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x88, 0x01, 0x0a, 0x27, 0x70,
	0x72, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x64, 0x64, 0x5f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x5f, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x50,
	0x72, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x41, 0x64, 0x64, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x52, 0x22, 0x70, 0x72, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x64, 0x64, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x54, 0x0a, 0x13, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x1e, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4d,
	0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x52, 0x12, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x4f, 0x0a, 0x12, 0x61,
	0x64, 0x64, 0x5f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x64, 0x64, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x10, 0x61, 0x64, 0x64, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x22, 0x4c, 0x0a, 0x12,
	0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x36, 0x0a, 0x17, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x61, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x15, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41,
	0x63, 0x6b, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x22, 0x5a, 0x0a, 0x10, 0x41, 0x64,
	0x64, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1f,
	0x0a, 0x0b, 0x73, 0x6b, 0x69, 0x70, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x6b, 0x69, 0x70, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12,
	0x25, 0x0a, 0x0e, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x8c, 0x02, 0x0a, 0x22, 0x50, 0x72, 0x65, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x64, 0x64,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x4e, 0x0a,
	0x22, 0x73, 0x6b, 0x69, 0x70, 0x70, 0x65, 0x64, 0x5f, 0x61, 0x66, 0x74, 0x65, 0x72, 0x5f, 0x73,
	0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x42, 0x02, 0x18, 0x01, 0x52, 0x1e, 0x73,
	0x6b, 0x69, 0x70, 0x70, 0x65, 0x64, 0x41, 0x66, 0x74, 0x65, 0x72, 0x53, 0x61, 0x76, 0x69, 0x6e,
	0x67, 0x73, 0x41, 0x63, 0x63, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4e, 0x0a,
	0x22, 0x73, 0x6b, 0x69, 0x70, 0x70, 0x65, 0x64, 0x5f, 0x61, 0x66, 0x74, 0x65, 0x72, 0x5f, 0x73,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x66, 0x75, 0x6c, 0x5f, 0x61, 0x64, 0x64, 0x5f, 0x6d, 0x6f,
	0x6e, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x42, 0x02, 0x18, 0x01, 0x52, 0x1e, 0x73,
	0x6b, 0x69, 0x70, 0x70, 0x65, 0x64, 0x41, 0x66, 0x74, 0x65, 0x72, 0x53, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x66, 0x75, 0x6c, 0x41, 0x64, 0x64, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x12, 0x1f, 0x0a,
	0x0b, 0x73, 0x6b, 0x69, 0x70, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x73, 0x6b, 0x69, 0x70, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x25,
	0x0a, 0x0e, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x9f, 0x01, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x65, 0x74, 0x63, 0x68, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x12, 0x43, 0x0a, 0x1f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65,
	0x71, 0x5f, 0x69, 0x64, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x6f,
	0x75, 0x74, 0x5f, 0x70, 0x61, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1a, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x46, 0x65, 0x74, 0x63, 0x68, 0x57, 0x69,
	0x74, 0x68, 0x6f, 0x75, 0x74, 0x50, 0x61, 0x6e, 0x12, 0x3d, 0x0a, 0x1c, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x5f,
	0x77, 0x69, 0x74, 0x68, 0x5f, 0x70, 0x61, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x17,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x46, 0x65, 0x74, 0x63, 0x68,
	0x57, 0x69, 0x74, 0x68, 0x50, 0x61, 0x6e, 0x22, 0xda, 0x01, 0x0a, 0x1b, 0x50, 0x72, 0x65, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x61, 0x0a, 0x0e, 0x66, 0x61, 0x69, 0x6c, 0x75,
	0x72, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x3a, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x50, 0x72, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x46, 0x61,
	0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x0d, 0x66, 0x61, 0x69,
	0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x58, 0x0a, 0x0d, 0x46, 0x61,
	0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x1a, 0x46,
	0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x27, 0x0a, 0x23, 0x46,
	0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x50, 0x41,
	0x4e, 0x5f, 0x55, 0x4e, 0x49, 0x51, 0x55, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x43, 0x48, 0x45,
	0x43, 0x4b, 0x10, 0x01, 0x22, 0x3d, 0x0a, 0x1c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x49, 0x64, 0x22, 0x31, 0x0a, 0x10, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0xf6, 0x01, 0x0a, 0x15, 0x52, 0x69, 0x73, 0x6b, 0x53,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x12, 0x51, 0x0a, 0x14, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x42, 0x02, 0x18, 0x01, 0x52,
	0x12, 0x72, 0x69, 0x73, 0x6b, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x12, 0x5e, 0x0a, 0x1b, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x64, 0x5f, 0x6d, 0x61,
	0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x72, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x46,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x42, 0x02, 0x18, 0x01, 0x52, 0x18, 0x66, 0x6f, 0x72, 0x63, 0x65,
	0x64, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22,
	0xa8, 0x02, 0x0a, 0x19, 0x50, 0x61, 0x6e, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x52, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x0a,
	0x07, 0x76, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x56, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x52, 0x07, 0x76, 0x65, 0x72, 0x64, 0x69, 0x63,
	0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x64, 0x5f, 0x62, 0x79,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x64,
	0x42, 0x79, 0x12, 0x3b, 0x0a, 0x0b, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x64, 0x5f, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0a, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x64, 0x4f, 0x6e, 0x12,
	0x5f, 0x0a, 0x18, 0x70, 0x61, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x26, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x50, 0x61, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x15, 0x70, 0x61, 0x6e, 0x4e, 0x61,
	0x6d, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x22, 0xa1, 0x02, 0x0a, 0x12, 0x44,
	0x65, 0x62, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x12, 0x39, 0x0a, 0x19, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x65, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x50, 0x61, 0x73, 0x73, 0x65, 0x64, 0x12, 0x37, 0x0a, 0x18,
	0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6d, 0x61, 0x74,
	0x63, 0x68, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x15,
	0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68,
	0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x31, 0x0a, 0x15, 0x6f, 0x6c, 0x64, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x65, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x6f, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x74,
	0x63, 0x68, 0x50, 0x61, 0x73, 0x73, 0x65, 0x64, 0x12, 0x2f, 0x0a, 0x14, 0x6f, 0x6c, 0x64, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x11, 0x6f, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x4d,
	0x61, 0x74, 0x63, 0x68, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x33, 0x0a, 0x16, 0x6e, 0x61, 0x6d,
	0x65, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x6e, 0x61, 0x6d, 0x65, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xa0,
	0x16, 0x0a, 0x14, 0x41, 0x70, 0x70, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x4d,
	0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x12, 0x4f, 0x0a, 0x13, 0x63, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x66, 0x6f, 0x75, 0x6e, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61,
	0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x11, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x12, 0x69, 0x0a, 0x21, 0x70, 0x61, 0x6e, 0x5f,
	0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x65, 0x64, 0x18, 0x17, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e,
	0x45, 0x6e, 0x75, 0x6d, 0x52, 0x1d, 0x70, 0x61, 0x6e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70,
	0x74, 0x65, 0x64, 0x12, 0x64, 0x0a, 0x1e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x63,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x64, 0x6f, 0x77,
	0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x1b, 0x63, 0x6f,
	0x6e, 0x73, 0x65, 0x6e, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x6a, 0x0a, 0x21, 0x63, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x65, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61,
	0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x1e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50,
	0x61, 0x73, 0x73, 0x65, 0x64, 0x12, 0x65, 0x0a, 0x1e, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x70, 0x61, 0x73, 0x73, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x1c,
	0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x73, 0x73, 0x65, 0x64, 0x12, 0x36, 0x0a, 0x17,
	0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67,
	0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x6d,
	0x61, 0x6e, 0x75, 0x61, 0x6c, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x12, 0x45, 0x0a, 0x0b, 0x73, 0x6b, 0x69, 0x70, 0x5f, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x69, 0x6e, 0x67, 0x53, 0x6b, 0x69, 0x70, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52,
	0x0a, 0x73, 0x6b, 0x69, 0x70, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x74, 0x0a, 0x1e, 0x63,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x66, 0x6c, 0x6f,
	0x77, 0x5f, 0x73, 0x6b, 0x69, 0x70, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x53, 0x6b, 0x69, 0x70, 0x52,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x1a, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x46, 0x6c, 0x6f, 0x77, 0x53, 0x6b, 0x69, 0x70, 0x52, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x12, 0x89, 0x01, 0x0a, 0x26, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x34, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x56,
	0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x23, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x90, 0x01,
	0x0a, 0x29, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f,
	0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x36, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x25, 0x63, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x12, 0x56, 0x0a, 0x13, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e,
	0x69, 0x6e, 0x61, 0x70, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x2e, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x46, 0x69, 0x6e, 0x69, 0x74, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x43, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x11, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x65, 0x43, 0x6f, 0x64,
	0x65, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x4d, 0x0a, 0x10, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x23, 0x2e, 0x69, 0x6e, 0x61, 0x70, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72,
	0x61, 0x6c, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x46, 0x69, 0x6e, 0x69, 0x74, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x5f, 0x0a, 0x1b, 0x65, 0x6d, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6f, 0x6e, 0x6c, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x19, 0x65,
	0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x6e, 0x6c, 0x79, 0x12, 0x5b, 0x0a, 0x19, 0x67, 0x6d, 0x61, 0x69,
	0x6c, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70,
	0x61, 0x73, 0x73, 0x65, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x17, 0x67, 0x6d,
	0x61, 0x69, 0x6c, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50,
	0x61, 0x73, 0x73, 0x65, 0x64, 0x12, 0x64, 0x0a, 0x1e, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x70, 0x61, 0x73, 0x73, 0x65, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x1b,
	0x77, 0x6f, 0x72, 0x6b, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x73, 0x73, 0x65, 0x64, 0x12, 0x87, 0x01, 0x0a, 0x26,
	0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f,
	0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x57,
	0x6f, 0x72, 0x6b, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x52, 0x22, 0x77, 0x6f, 0x72, 0x6b, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x56, 0x65, 0x72, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x61, 0x0a, 0x1c, 0x6c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x69,
	0x6e, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70,
	0x61, 0x73, 0x73, 0x65, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x1a, 0x6c, 0x69,
	0x6e, 0x6b, 0x65, 0x64, 0x69, 0x6e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x50, 0x61, 0x73, 0x73, 0x65, 0x64, 0x12, 0x61, 0x0a, 0x18, 0x6c, 0x69, 0x6e, 0x6b,
	0x65, 0x64, 0x69, 0x6e, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x4c, 0x69, 0x6e,
	0x6b, 0x65, 0x64, 0x49, 0x6e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x16, 0x6c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x69, 0x6e, 0x56, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x5d, 0x0a, 0x1a, 0x68,
	0x65, 0x75, 0x72, 0x69, 0x73, 0x74, 0x69, 0x63, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69,
	0x6e, 0x67, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x65, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d,
	0x52, 0x18, 0x68, 0x65, 0x75, 0x72, 0x69, 0x73, 0x74, 0x69, 0x63, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x73, 0x73, 0x65, 0x64, 0x12, 0x5e, 0x0a, 0x1b, 0x67, 0x73,
	0x74, 0x69, 0x6e, 0x5f, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x68, 0x65,
	0x63, 0x6b, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x65, 0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d,
	0x52, 0x18, 0x67, 0x73, 0x74, 0x69, 0x6e, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x50, 0x61, 0x73, 0x73, 0x65, 0x64, 0x12, 0x7e, 0x0a, 0x23, 0x67, 0x73,
	0x74, 0x69, 0x6e, 0x5f, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x68, 0x65,
	0x63, 0x6b, 0x5f, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x53, 0x54, 0x49, 0x4e, 0x50,
	0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x1f, 0x67, 0x73, 0x74, 0x69, 0x6e,
	0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x63, 0x0a, 0x1d, 0x65, 0x78,
	0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x62, 0x79, 0x70, 0x61, 0x73, 0x73, 0x18, 0x15, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e,
	0x75, 0x6d, 0x52, 0x1b, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c,
	0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x42, 0x79, 0x70, 0x61, 0x73, 0x73, 0x12,
	0x4a, 0x0a, 0x10, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x73,
	0x73, 0x65, 0x64, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42,
	0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0f, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x73, 0x73, 0x65, 0x64, 0x12, 0x6b, 0x0a, 0x1f, 0x68,
	0x65, 0x75, 0x72, 0x69, 0x73, 0x74, 0x69, 0x63, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69,
	0x6e, 0x67, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x18,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x48, 0x65, 0x75, 0x72, 0x69, 0x73, 0x74, 0x69, 0x63,
	0x50, 0x61, 0x73, 0x73, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x1c, 0x68, 0x65, 0x75, 0x72,
	0x69, 0x73, 0x74, 0x69, 0x63, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x50, 0x61,
	0x73, 0x73, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x4f, 0x0a, 0x13, 0x66, 0x6f, 0x72, 0x6d,
	0x31, 0x36, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x65, 0x64, 0x18,
	0x19, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65,
	0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x11, 0x66, 0x6f, 0x72, 0x6d, 0x31, 0x36, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x50, 0x61, 0x73, 0x73, 0x65, 0x64, 0x12, 0x5a, 0x0a, 0x19, 0x75, 0x61, 0x6e,
	0x5f, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f,
	0x70, 0x61, 0x73, 0x73, 0x65, 0x64, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x16, 0x75,
	0x61, 0x6e, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50,
	0x61, 0x73, 0x73, 0x65, 0x64, 0x12, 0x55, 0x0a, 0x16, 0x61, 0x66, 0x66, 0x6c, 0x75, 0x65, 0x6e,
	0x63, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x65, 0x64, 0x18,
	0x1b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65,
	0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x14, 0x61, 0x66, 0x66, 0x6c, 0x75, 0x65, 0x6e, 0x63,
	0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50, 0x61, 0x73, 0x73, 0x65, 0x64, 0x12, 0x27, 0x0a, 0x0f,
	0x61, 0x66, 0x66, 0x6c, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18,
	0x1c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x61, 0x66, 0x66, 0x6c, 0x75, 0x65, 0x6e, 0x63, 0x65,
	0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x5a, 0x0a, 0x19, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x70, 0x61, 0x6e, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68,
	0x65, 0x64, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f,
	0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x16, 0x63, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x61, 0x6e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x65,
	0x64, 0x22, 0x7b, 0x0a, 0x19, 0x45, 0x4b, 0x59, 0x43, 0x4e, 0x61, 0x6d, 0x65, 0x44, 0x4f, 0x42,
	0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x18,
	0x0a, 0x07, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x07, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x61, 0x69, 0x6c,
	0x75, 0x72, 0x65, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x44, 0x65, 0x73, 0x63, 0x12, 0x21, 0x0a, 0x0c, 0x72,
	0x61, 0x77, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x72, 0x61, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xba,
	0x01, 0x0a, 0x0d, 0x50, 0x61, 0x6e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x39, 0x0a, 0x0a, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x74, 0x69, 0x6c, 0x6c, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x69, 0x6c, 0x6c, 0x12, 0x6e, 0x0a, 0x1d, 0x70,
	0x61, 0x6e, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x75,
	0x73, 0x70, 0x65, 0x6e, 0x64, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x61, 0x6e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x75, 0x73, 0x70, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52,
	0x1a, 0x70, 0x61, 0x6e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75,
	0x73, 0x70, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0xbe, 0x01, 0x0a, 0x0c,
	0x56, 0x4b, 0x59, 0x43, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x2c, 0x0a, 0x12,
	0x70, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x76, 0x6b, 0x79, 0x63, 0x5f, 0x63, 0x68, 0x65,
	0x63, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x70, 0x65, 0x72, 0x66, 0x6f, 0x72,
	0x6d, 0x56, 0x6b, 0x79, 0x63, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x3c, 0x0a, 0x0b, 0x76, 0x6b,
	0x79, 0x63, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x56, 0x4b, 0x59, 0x43, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x76, 0x6b,
	0x79, 0x63, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x42, 0x0a, 0x0f, 0x69, 0x6e, 0x68, 0x6f,
	0x75, 0x73, 0x65, 0x5f, 0x76, 0x5f, 0x6b, 0x5f, 0x79, 0x5f, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x49, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x56, 0x4b, 0x59, 0x43, 0x52,
	0x0b, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x56, 0x4b, 0x59, 0x43, 0x22, 0x4d, 0x0a, 0x0b,
	0x49, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x56, 0x4b, 0x59, 0x43, 0x12, 0x25, 0x0a, 0x0e, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x22, 0x8f, 0x02, 0x0a, 0x17,
	0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x3f, 0x0a, 0x09, 0x73, 0x65, 0x6c, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x4f, 0x6e, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x09, 0x73,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3b, 0x0a, 0x07, 0x63, 0x68, 0x6f, 0x69,
	0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x4f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x63, 0x68,
	0x6f, 0x69, 0x63, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x69, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x61, 0x75, 0x74, 0x6f,
	0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x55, 0x0a, 0x14, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x4f, 0x76, 0x65,
	0x72, 0x72, 0x69, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x12, 0x69, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x4f, 0x0a,
	0x12, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x39, 0x0a, 0x19, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x64, 0x65,
	0x6e, 0x5f, 0x62, 0x79, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x6c, 0x65, 0x61, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x64,
	0x65, 0x6e, 0x42, 0x79, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4c, 0x65, 0x61, 0x64, 0x22, 0x93,
	0x01, 0x0a, 0x1b, 0x53, 0x6f, 0x66, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x43,
	0x0a, 0x09, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x25, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x6f,
	0x66, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x09, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x2f, 0x0a, 0x14, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x11, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52,
	0x65, 0x71, 0x49, 0x64, 0x22, 0xea, 0x01, 0x0a, 0x0d, 0x46, 0x69, 0x4c, 0x69, 0x74, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3e, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x09, 0x69, 0x73, 0x45,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x43, 0x0a, 0x0e, 0x66, 0x69, 0x5f, 0x6c, 0x69, 0x74,
	0x65, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x46, 0x69, 0x4c, 0x69, 0x74, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x0c, 0x66,
	0x69, 0x4c, 0x69, 0x74, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x54, 0x0a, 0x18, 0x61,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x16, 0x61, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x41,
	0x74, 0x22, 0xec, 0x02, 0x0a, 0x0b, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x45, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61,
	0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x3d, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70,
	0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x70,
	0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x45, 0x0a, 0x0e, 0x66, 0x65, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1e, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x0d, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x8f,
	0x01, 0x0a, 0x1e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x6f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x4a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6f, 0x6e, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x4f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f,
	0x69, 0x6e, 0x74, 0x52, 0x1b, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x4f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74,
	0x22, 0xc3, 0x01, 0x0a, 0x0e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x53, 0x0a, 0x0c, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x66, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x5c, 0x0a, 0x10, 0x46, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x32,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xa1, 0x04, 0x0a, 0x17, 0x50, 0x61, 0x6e, 0x41, 0x61,
	0x64, 0x68, 0x61, 0x72, 0x4c, 0x69, 0x6e, 0x6b, 0x61, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x5a, 0x0a, 0x2b, 0x65, 0x6b, 0x79, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f,
	0x64, 0x6f, 0x62, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61,
	0x61, 0x64, 0x68, 0x61, 0x72, 0x5f, 0x64, 0x69, 0x67, 0x69, 0x74, 0x73, 0x5f, 0x68, 0x61, 0x73,
	0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x25, 0x65, 0x6b, 0x79, 0x63, 0x4e, 0x61, 0x6d,
	0x65, 0x44, 0x6f, 0x62, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x61,
	0x64, 0x68, 0x61, 0x72, 0x44, 0x69, 0x67, 0x69, 0x74, 0x73, 0x48, 0x61, 0x73, 0x68, 0x12, 0x40,
	0x0a, 0x1d, 0x70, 0x61, 0x6e, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x5f, 0x61, 0x61, 0x64,
	0x68, 0x61, 0x72, 0x5f, 0x64, 0x69, 0x67, 0x69, 0x74, 0x73, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x19, 0x70, 0x61, 0x6e, 0x4c, 0x69, 0x6e, 0x6b, 0x65, 0x64,
	0x41, 0x61, 0x64, 0x68, 0x61, 0x72, 0x44, 0x69, 0x67, 0x69, 0x74, 0x73, 0x48, 0x61, 0x73, 0x68,
	0x12, 0x63, 0x0a, 0x1e, 0x69, 0x73, 0x5f, 0x61, 0x61, 0x64, 0x68, 0x61, 0x72, 0x5f, 0x64, 0x69,
	0x67, 0x69, 0x74, 0x73, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x5f, 0x6d, 0x69, 0x73, 0x6d, 0x61, 0x74,
	0x63, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f,
	0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x1a, 0x69, 0x73, 0x41, 0x61, 0x64,
	0x68, 0x61, 0x72, 0x44, 0x69, 0x67, 0x69, 0x74, 0x73, 0x48, 0x61, 0x73, 0x68, 0x4d, 0x69, 0x73,
	0x6d, 0x61, 0x74, 0x63, 0x68, 0x12, 0x4d, 0x0a, 0x12, 0x70, 0x61, 0x6e, 0x5f, 0x61, 0x61, 0x64,
	0x68, 0x61, 0x61, 0x72, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e,
	0x75, 0x6d, 0x52, 0x10, 0x70, 0x61, 0x6e, 0x41, 0x61, 0x64, 0x68, 0x61, 0x61, 0x72, 0x4c, 0x69,
	0x6e, 0x6b, 0x65, 0x64, 0x12, 0x65, 0x0a, 0x31, 0x65, 0x6b, 0x79, 0x63, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x5f, 0x64, 0x6f, 0x62, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x61, 0x61, 0x64, 0x68, 0x61, 0x72, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x32, 0x5f, 0x64, 0x69,
	0x67, 0x69, 0x74, 0x73, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x2a, 0x65, 0x6b, 0x79, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x44, 0x6f, 0x62, 0x56, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x61, 0x64, 0x68, 0x61, 0x72, 0x4c, 0x61, 0x73, 0x74,
	0x32, 0x44, 0x69, 0x67, 0x69, 0x74, 0x73, 0x48, 0x61, 0x73, 0x68, 0x12, 0x4d, 0x0a, 0x24, 0x70,
	0x61, 0x6e, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x5f, 0x61, 0x61, 0x64, 0x68, 0x61, 0x61,
	0x72, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x32, 0x5f, 0x64, 0x69, 0x67, 0x69, 0x74, 0x73, 0x5f, 0x68,
	0x61, 0x73, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1f, 0x70, 0x61, 0x6e, 0x4c, 0x69,
	0x6e, 0x6b, 0x65, 0x64, 0x41, 0x61, 0x64, 0x68, 0x61, 0x61, 0x72, 0x4c, 0x61, 0x73, 0x74, 0x32,
	0x44, 0x69, 0x67, 0x69, 0x74, 0x73, 0x48, 0x61, 0x73, 0x68, 0x22, 0xf5, 0x09, 0x0a, 0x1c, 0x50,
	0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x38, 0x0a, 0x19, 0x64,
	0x6f, 0x63, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15,
	0x64, 0x6f, 0x63, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x70, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72,
	0x74, 0x5f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x70, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x72, 0x6f, 0x6e, 0x74,
	0x55, 0x72, 0x6c, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x5f,
	0x62, 0x61, 0x63, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x70, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x42, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x12,
	0x43, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x50, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x54, 0x0a, 0x28, 0x70, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74,
	0x5f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x5f, 0x64, 0x6f, 0x63, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x22, 0x70, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74,
	0x46, 0x72, 0x6f, 0x6e, 0x74, 0x44, 0x6f, 0x63, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x43,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x12, 0x52, 0x0a, 0x27, 0x70, 0x61,
	0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x64, 0x6f, 0x63, 0x5f,
	0x65, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72,
	0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x21, 0x70, 0x61, 0x73,
	0x73, 0x70, 0x6f, 0x72, 0x74, 0x42, 0x61, 0x63, 0x6b, 0x44, 0x6f, 0x63, 0x45, 0x78, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x12, 0x3d,
	0x0a, 0x0c, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0b, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x4c, 0x0a,
	0x23, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74,
	0x73, 0x5f, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6f, 0x6c, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x77, 0x69,
	0x6e, 0x64, 0x6f, 0x77, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x1f, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x49, 0x6e, 0x43, 0x6f, 0x6f,
	0x6c, 0x64, 0x6f, 0x77, 0x6e, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x12, 0x24, 0x0a, 0x0e, 0x66,
	0x61, 0x63, 0x65, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x61, 0x63, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72,
	0x6c, 0x12, 0x91, 0x01, 0x0a, 0x1f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x61, 0x70, 0x69, 0x5f, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4a, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x61,
	0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x41, 0x50, 0x49,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x52, 0x1c, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x41, 0x70, 0x69, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x73, 0x12, 0x37, 0x0a, 0x18, 0x6f, 0x6c, 0x64, 0x5f, 0x70, 0x61, 0x73,
	0x73, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x6f, 0x6c, 0x64, 0x50, 0x61, 0x73, 0x73,
	0x70, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x59,
	0x0a, 0x0e, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72,
	0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x61, 0x69,
	0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x0d, 0x66, 0x61, 0x69, 0x6c,
	0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x73,
	0x73, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x61, 0x72, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x70, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x41, 0x72, 0x6e, 0x12, 0x66, 0x0a, 0x18,
	0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x50, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x52,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x16, 0x6d, 0x61,
	0x6e, 0x75, 0x61, 0x6c, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x1a, 0xeb, 0x01, 0x0a, 0x1c, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x41, 0x50, 0x49, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x73, 0x12, 0x48, 0x0a, 0x15, 0x70, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72,
	0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x56, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x52, 0x13, 0x70, 0x61, 0x73, 0x73,
	0x70, 0x6f, 0x72, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x12,
	0x3c, 0x0a, 0x0f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6d, 0x61, 0x74,
	0x63, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x56, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x52, 0x0d,
	0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x12, 0x43, 0x0a,
	0x13, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x69, 0x73, 0x73, 0x75, 0x65, 0x5f, 0x6d,
	0x61, 0x74, 0x63, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x56, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74,
	0x52, 0x10, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x49, 0x73, 0x73, 0x75, 0x65, 0x4d, 0x61, 0x74,
	0x63, 0x68, 0x22, 0xfe, 0x01, 0x0a, 0x1b, 0x50, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x4d,
	0x61, 0x6e, 0x75, 0x61, 0x6c, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x32, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x56, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x52, 0x07, 0x76,
	0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x65, 0x64, 0x42, 0x79, 0x12, 0x3b, 0x0a, 0x0b, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x65, 0x64, 0x5f, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x65, 0x64, 0x4f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x12, 0x33,
	0x0a, 0x16, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x70, 0x72, 0x6f, 0x6f, 0x66, 0x5f, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x50, 0x72, 0x6f, 0x6f, 0x66, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x55, 0x72, 0x6c, 0x22, 0x8d, 0x04, 0x0a, 0x15, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x56, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x41, 0x0a,
	0x07, 0x76, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x56, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x52, 0x07, 0x76, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74,
	0x12, 0x3b, 0x0a, 0x05, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x25, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x05, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x54, 0x0a,
	0x0e, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x56, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x52, 0x0d, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x12, 0x4e, 0x0a, 0x0d, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x31, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x72, 0x6f,
	0x73, 0x73, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x0b, 0x64, 0x61, 0x74, 0x61, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x31, 0x12, 0x4e, 0x0a, 0x0d, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x32, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x72, 0x6f,
	0x73, 0x73, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x0b, 0x64, 0x61, 0x74, 0x61, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x32, 0x12, 0x7e, 0x0a, 0x23, 0x63, 0x72, 0x6f, 0x73, 0x73, 0x5f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x30, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x2e, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x1f, 0x63, 0x72, 0x6f, 0x73, 0x73, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x49,
	0x6e, 0x66, 0x6f, 0x22, 0x99, 0x01, 0x0a, 0x1f, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x56, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x52, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x6d, 0x61,
	0x72, 0x6b, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72,
	0x6b, 0x73, 0x12, 0x3b, 0x0a, 0x0b, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0a, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x64, 0x41, 0x74, 0x22,
	0x59, 0x0a, 0x1d, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x49, 0x64, 0x56, 0x65, 0x72, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x12, 0x38, 0x0a, 0x19, 0x64, 0x6f, 0x63, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x5f,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x15, 0x64, 0x6f, 0x63, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x43,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x22, 0xae, 0x01, 0x0a, 0x1a, 0x46,
	0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x35, 0x0a, 0x17, 0x73, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x73, 0x5f, 0x68, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x73, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x41, 0x73, 0x48, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x12, 0x59, 0x0a, 0x15, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x6f, 0x66,
	0x74, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x25, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x6f, 0x66, 0x74,
	0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x13, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x53, 0x6f, 0x66, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x22, 0xfd, 0x02, 0x0a, 0x12,
	0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x12, 0x42, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x45, 0x6c, 0x69, 0x67,
	0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x74, 0x0a, 0x18, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x2e, 0x41, 0x64,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x52, 0x16, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x1a, 0x51, 0x0a, 0x16,
	0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x37, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x22,
	0x5a, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x41, 0x53, 0x53,
	0x45, 0x44, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46,
	0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x03, 0x22, 0xc8, 0x02, 0x0a, 0x10,
	0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x4c, 0x69, 0x66, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65,
	0x12, 0x32, 0x0a, 0x07, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x18, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x52, 0x07, 0x66, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x12, 0x5f, 0x0a, 0x15, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x73,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x13, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x52, 0x0a, 0x12, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x23, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x45, 0x6c, 0x69, 0x67, 0x69,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x11, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4b, 0x0a, 0x11, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x10, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2a, 0x9d, 0x13, 0x0a, 0x0f, 0x4f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x20, 0x0a, 0x1c, 0x4f, 0x4e,
	0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14,
	0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c, 0x5f, 0x46, 0x49, 0x4e, 0x49, 0x54, 0x45, 0x5f,
	0x43, 0x4f, 0x44, 0x45, 0x10, 0x07, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x4e, 0x43, 0x5f, 0x43, 0x4f,
	0x4e, 0x53, 0x45, 0x4e, 0x54, 0x10, 0x08, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x48, 0x45, 0x43, 0x4b,
	0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x50,
	0x52, 0x45, 0x53, 0x45, 0x4e, 0x43, 0x45, 0x10, 0x65, 0x12, 0x22, 0x0a, 0x1e, 0x43, 0x4f, 0x4e,
	0x53, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x50, 0x4f,
	0x52, 0x54, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x10, 0x66, 0x12, 0x1e, 0x0a,
	0x1a, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x56,
	0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x67, 0x12, 0x1b, 0x0a,
	0x17, 0x45, 0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x56, 0x45, 0x52, 0x49,
	0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x68, 0x12, 0x29, 0x0a, 0x25, 0x43, 0x52,
	0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x50, 0x52, 0x45, 0x53,
	0x45, 0x4e, 0x43, 0x45, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f,
	0x50, 0x41, 0x4e, 0x10, 0x73, 0x12, 0x14, 0x0a, 0x0f, 0x41, 0x46, 0x46, 0x4c, 0x55, 0x45, 0x4e,
	0x43, 0x45, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0xa1, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x47,
	0x53, 0x54, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x45, 0x53, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x43, 0x48,
	0x45, 0x43, 0x4b, 0x10, 0x71, 0x12, 0x19, 0x0a, 0x15, 0x4c, 0x49, 0x4e, 0x4b, 0x45, 0x44, 0x49,
	0x4e, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x6d,
	0x12, 0x1a, 0x0a, 0x16, 0x48, 0x45, 0x55, 0x52, 0x49, 0x53, 0x54, 0x49, 0x43, 0x5f, 0x56, 0x45,
	0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x70, 0x12, 0x10, 0x0a, 0x0c,
	0x46, 0x4f, 0x52, 0x4d, 0x31, 0x36, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0x76, 0x12, 0x21,
	0x0a, 0x1d, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e,
	0x54, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x10,
	0x69, 0x12, 0x16, 0x0a, 0x12, 0x47, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46,
	0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x6c, 0x12, 0x1b, 0x0a, 0x17, 0x57, 0x4f, 0x52,
	0x4b, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x6f, 0x12, 0x16, 0x0a, 0x12, 0x55, 0x41, 0x4e, 0x5f, 0x50, 0x52,
	0x45, 0x53, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0x72, 0x12, 0x19,
	0x0a, 0x15, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x45, 0x53, 0x54, 0x49, 0x4d, 0x41, 0x54,
	0x45, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0x77, 0x12, 0x14, 0x0a, 0x10, 0x4d, 0x41, 0x4e,
	0x55, 0x41, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x6a, 0x12,
	0x11, 0x0a, 0x0d, 0x41, 0x50, 0x50, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x49, 0x4e, 0x47,
	0x10, 0x6b, 0x12, 0x17, 0x0a, 0x13, 0x45, 0x50, 0x46, 0x4f, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x41,
	0x4e, 0x59, 0x5f, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x10, 0x74, 0x12, 0x16, 0x0a, 0x12, 0x43,
	0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x53, 0x10, 0x75, 0x12, 0x0f, 0x0a, 0x0b, 0x44, 0x4f, 0x42, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x50,
	0x41, 0x4e, 0x10, 0x0c, 0x12, 0x10, 0x0a, 0x0c, 0x44, 0x45, 0x44, 0x55, 0x50, 0x45, 0x5f, 0x43,
	0x48, 0x45, 0x43, 0x4b, 0x10, 0x11, 0x12, 0x11, 0x0a, 0x0d, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41,
	0x54, 0x45, 0x5f, 0x43, 0x4b, 0x59, 0x43, 0x10, 0x14, 0x12, 0x16, 0x0a, 0x12, 0x4d, 0x4f, 0x54,
	0x48, 0x45, 0x52, 0x5f, 0x46, 0x41, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x10,
	0x17, 0x12, 0x08, 0x0a, 0x04, 0x43, 0x4b, 0x59, 0x43, 0x10, 0x19, 0x12, 0x08, 0x0a, 0x04, 0x45,
	0x4b, 0x59, 0x43, 0x10, 0x1a, 0x12, 0x0c, 0x0a, 0x08, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53,
	0x53, 0x10, 0x1b, 0x12, 0x1f, 0x0a, 0x1b, 0x4b, 0x59, 0x43, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x4c,
	0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0x1e, 0x12, 0x1b, 0x0a, 0x17, 0x45, 0x4e, 0x53, 0x55, 0x52, 0x45, 0x5f, 0x4b,
	0x59, 0x43, 0x5f, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x10,
	0x1f, 0x12, 0x12, 0x0a, 0x0e, 0x50, 0x41, 0x4e, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x43, 0x48,
	0x45, 0x43, 0x4b, 0x10, 0x20, 0x12, 0x12, 0x0a, 0x0e, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x21, 0x12, 0x1b, 0x0a, 0x17, 0x55, 0x50, 0x44,
	0x41, 0x54, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x54,
	0x41, 0x49, 0x4c, 0x53, 0x10, 0x23, 0x12, 0x11, 0x0a, 0x0d, 0x55, 0x4e, 0x5f, 0x4e, 0x41, 0x4d,
	0x45, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0x28, 0x12, 0x14, 0x0a, 0x10, 0x4b, 0x59, 0x43,
	0x5f, 0x44, 0x45, 0x44, 0x55, 0x50, 0x45, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0x2d, 0x12,
	0x1c, 0x0a, 0x18, 0x45, 0x4b, 0x59, 0x43, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x44, 0x4f, 0x42,
	0x5f, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x2f, 0x12, 0x1b, 0x0a,
	0x17, 0x4b, 0x59, 0x43, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x44, 0x4f, 0x42, 0x5f, 0x56, 0x41,
	0x4c, 0x49, 0x44, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x30, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x4f,
	0x4e, 0x46, 0x49, 0x52, 0x4d, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4d, 0x41, 0x49, 0x4c, 0x49,
	0x4e, 0x47, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x10, 0x32, 0x12, 0x0f, 0x0a, 0x0b,
	0x55, 0x50, 0x49, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x10, 0x37, 0x12, 0x18, 0x0a,
	0x14, 0x50, 0x41, 0x4e, 0x5f, 0x55, 0x4e, 0x49, 0x51, 0x55, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f,
	0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0x3a, 0x12, 0x17, 0x0a, 0x13, 0x44, 0x45, 0x56, 0x49, 0x43,
	0x45, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x3c,
	0x12, 0x26, 0x0a, 0x22, 0x50, 0x52, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52,
	0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x44, 0x55, 0x50, 0x45,
	0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0x4b, 0x12, 0x1f, 0x0a, 0x1b, 0x50, 0x52, 0x45, 0x5f,
	0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0x4c, 0x12, 0x15, 0x0a, 0x11, 0x43, 0x55, 0x53,
	0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x50,
	0x12, 0x14, 0x0a, 0x10, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x64, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x48, 0x49, 0x50, 0x50, 0x49,
	0x4e, 0x47, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54,
	0x45, 0x10, 0x6e, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x78, 0x12, 0x1a, 0x0a, 0x16, 0x55, 0x4e, 0x5f, 0x4e, 0x41, 0x4d,
	0x45, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52,
	0x10, 0x7d, 0x12, 0x0e, 0x0a, 0x09, 0x55, 0x50, 0x49, 0x5f, 0x53, 0x45, 0x54, 0x55, 0x50, 0x10,
	0x8c, 0x01, 0x12, 0x19, 0x0a, 0x14, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x53, 0x45, 0x54, 0x55, 0x50, 0x10, 0x91, 0x01, 0x12, 0x09, 0x0a,
	0x04, 0x56, 0x4b, 0x59, 0x43, 0x10, 0x93, 0x01, 0x12, 0x0e, 0x0a, 0x09, 0x41, 0x44, 0x44, 0x5f,
	0x4d, 0x4f, 0x4e, 0x45, 0x59, 0x10, 0x96, 0x01, 0x12, 0x12, 0x0a, 0x0d, 0x4f, 0x50, 0x54, 0x49,
	0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x56, 0x4b, 0x59, 0x43, 0x10, 0x97, 0x01, 0x12, 0x18, 0x0a, 0x13,
	0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c,
	0x45, 0x54, 0x45, 0x10, 0xa0, 0x01, 0x12, 0x13, 0x0a, 0x0e, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0xa2, 0x01, 0x12, 0x09, 0x0a, 0x04, 0x42,
	0x4b, 0x59, 0x43, 0x10, 0xa3, 0x01, 0x12, 0x1b, 0x0a, 0x16, 0x46, 0x49, 0x5f, 0x4c, 0x49, 0x54,
	0x45, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x49, 0x4e, 0x47,
	0x10, 0xa4, 0x01, 0x12, 0x15, 0x0a, 0x10, 0x49, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x45,
	0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xa5, 0x01, 0x12, 0x28, 0x0a, 0x23, 0x43, 0x52,
	0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52,
	0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x48, 0x45, 0x43,
	0x4b, 0x10, 0xa6, 0x01, 0x12, 0x1a, 0x0a, 0x15, 0x53, 0x41, 0x56, 0x49, 0x4e, 0x47, 0x53, 0x5f,
	0x49, 0x4e, 0x54, 0x52, 0x4f, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x10, 0xa7, 0x01,
	0x12, 0x1f, 0x0a, 0x1a, 0x50, 0x4c, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e,
	0x47, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0xa8,
	0x01, 0x12, 0x20, 0x0a, 0x1b, 0x49, 0x54, 0x52, 0x5f, 0x49, 0x4e, 0x54, 0x49, 0x4d, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0xa9, 0x01, 0x12, 0x18, 0x0a, 0x13, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x45,
	0x50, 0x4f, 0x52, 0x54, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0xaa, 0x01, 0x12, 0x1d, 0x0a,
	0x18, 0x41, 0x41, 0x44, 0x48, 0x41, 0x52, 0x5f, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x5f, 0x56,
	0x41, 0x4c, 0x49, 0x44, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xab, 0x01, 0x12, 0x1b, 0x0a, 0x16,
	0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x44,
	0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0xac, 0x01, 0x12, 0x16, 0x0a, 0x11, 0x4c, 0x45, 0x4e,
	0x44, 0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0xad,
	0x01, 0x12, 0x1a, 0x0a, 0x15, 0x50, 0x41, 0x53, 0x53, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x56, 0x45,
	0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xae, 0x01, 0x12, 0x16, 0x0a,
	0x11, 0x56, 0x49, 0x53, 0x41, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0xaf, 0x01, 0x12, 0x1c, 0x0a, 0x17, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x52, 0x59,
	0x5f, 0x49, 0x44, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0xb0, 0x01, 0x12, 0x32, 0x0a, 0x2d, 0x4e, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x49, 0x44,
	0x45, 0x4e, 0x54, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x43,
	0x52, 0x4f, 0x53, 0x53, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0xb1, 0x01, 0x12, 0x1a, 0x0a, 0x15, 0x53, 0x4f, 0x46, 0x54, 0x5f,
	0x49, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0xb4, 0x01, 0x12, 0x1a, 0x0a, 0x15, 0x43, 0x4f, 0x4d, 0x4d, 0x55, 0x4e, 0x49, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x10, 0xb5, 0x01, 0x12,
	0x19, 0x0a, 0x14, 0x4e, 0x52, 0x4f, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x43,
	0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xb6, 0x01, 0x12, 0x18, 0x0a, 0x13, 0x4f, 0x52,
	0x44, 0x45, 0x52, 0x5f, 0x50, 0x48, 0x59, 0x53, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x43, 0x41, 0x52,
	0x44, 0x10, 0xb7, 0x01, 0x12, 0x1d, 0x0a, 0x18, 0x4f, 0x50, 0x45, 0x4e, 0x5f, 0x4d, 0x49, 0x4e,
	0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x10, 0xb8, 0x01, 0x12, 0x17, 0x0a, 0x12, 0x53, 0x4d, 0x53, 0x5f, 0x50, 0x41, 0x52, 0x53, 0x45,
	0x52, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x10, 0xb9, 0x01, 0x12, 0x21, 0x0a, 0x1c,
	0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f,
	0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x46, 0x45, 0x54, 0x43, 0x48, 0x10, 0xba, 0x01, 0x12,
	0x2c, 0x0a, 0x27, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53,
	0x45, 0x52, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0xbb, 0x01, 0x12, 0x21, 0x0a,
	0x1c, 0x53, 0x4d, 0x53, 0x5f, 0x50, 0x41, 0x52, 0x53, 0x45, 0x52, 0x5f, 0x44, 0x41, 0x54, 0x41,
	0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xbc, 0x01,
	0x12, 0x19, 0x0a, 0x14, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x45, 0x44, 0x5f, 0x41, 0x50,
	0x50, 0x53, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0xbd, 0x01, 0x12, 0x23, 0x0a, 0x1e, 0x50,
	0x52, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x4d, 0x4f, 0x4e, 0x45, 0x59, 0x10, 0xbe, 0x01,
	0x12, 0x39, 0x0a, 0x34, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x5f, 0x41, 0x44, 0x44, 0x49,
	0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x44,
	0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x46, 0x45, 0x44, 0x45, 0x52,
	0x41, 0x4c, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x10, 0xbf, 0x01, 0x12, 0x0f, 0x0a, 0x0a, 0x50,
	0x45, 0x52, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x10, 0xc0, 0x01, 0x12, 0x16, 0x0a, 0x11,
	0x57, 0x41, 0x49, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x50, 0x41,
	0x4e, 0x10, 0xc1, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x36, 0x30, 0x10,
	0xc2, 0x01, 0x12, 0x2b, 0x0a, 0x26, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x42, 0x55, 0x49,
	0x4c, 0x44, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x41,
	0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x53, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x10, 0xc3, 0x01, 0x12,
	0x28, 0x0a, 0x23, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53,
	0x45, 0x52, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x43, 0x4f,
	0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x10, 0xc4, 0x01, 0x12, 0x13, 0x0a, 0x0e, 0x53, 0x41, 0x5f,
	0x44, 0x45, 0x43, 0x4c, 0x41, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xc5, 0x01, 0x12, 0x26,
	0x0a, 0x21, 0x45, 0x4e, 0x53, 0x55, 0x52, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f,
	0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41, 0x42, 0x49, 0x4c,
	0x49, 0x54, 0x59, 0x10, 0xc6, 0x01, 0x22, 0x06, 0x08, 0xb2, 0x01, 0x10, 0xb2, 0x01, 0x22, 0x06,
	0x08, 0xb3, 0x01, 0x10, 0xb3, 0x01, 0x2a, 0x8c, 0x01, 0x0a, 0x0f, 0x4f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x49,
	0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x49, 0x4e,
	0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x46, 0x41,
	0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x55, 0x43, 0x43, 0x45,
	0x53, 0x53, 0x10, 0x04, 0x12, 0x17, 0x0a, 0x13, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x49,
	0x4e, 0x54, 0x45, 0x52, 0x56, 0x45, 0x4e, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x05, 0x12, 0x0b, 0x0a,
	0x07, 0x53, 0x4b, 0x49, 0x50, 0x50, 0x45, 0x44, 0x10, 0x06, 0x12, 0x09, 0x0a, 0x05, 0x52, 0x45,
	0x53, 0x45, 0x54, 0x10, 0x07, 0x2a, 0x5b, 0x0a, 0x11, 0x55, 0x4e, 0x4e, 0x61, 0x6d, 0x65, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14, 0x0a, 0x10, 0x55, 0x4e,
	0x4e, 0x43, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x4e, 0x4e, 0x43, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x45, 0x44, 0x10,
	0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x55, 0x4e, 0x4e, 0x43, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x49,
	0x4e, 0x5f, 0x53, 0x41, 0x4e, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x5f, 0x4c, 0x49, 0x53, 0x54,
	0x10, 0x02, 0x2a, 0x82, 0x02, 0x0a, 0x1a, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73,
	0x6b, 0x12, 0x10, 0x0a, 0x0c, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x46,
	0x4f, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x49, 0x4e, 0x46, 0x4f,
	0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0x02, 0x12, 0x10,
	0x0a, 0x0c, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x03,
	0x12, 0x12, 0x0a, 0x0e, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x4d, 0x45, 0x54, 0x41, 0x44, 0x41,
	0x54, 0x41, 0x10, 0x04, 0x12, 0x1c, 0x0a, 0x18, 0x43, 0x55, 0x52, 0x52, 0x45, 0x4e, 0x54, 0x5f,
	0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45,
	0x10, 0x05, 0x12, 0x0b, 0x0a, 0x07, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x10, 0x06, 0x12,
	0x13, 0x0a, 0x0f, 0x46, 0x49, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49,
	0x4c, 0x53, 0x10, 0x07, 0x12, 0x13, 0x0a, 0x0f, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f,
	0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x08, 0x12, 0x1e, 0x0a, 0x1a, 0x50, 0x41, 0x4e,
	0x5f, 0x41, 0x41, 0x44, 0x48, 0x41, 0x52, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x41, 0x47, 0x45, 0x5f,
	0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x09, 0x12, 0x1c, 0x0a, 0x18, 0x53, 0x54, 0x41,
	0x47, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x5f, 0x4c, 0x41, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x53,
	0x50, 0x4f, 0x4e, 0x53, 0x45, 0x10, 0x0a, 0x2a, 0x94, 0x03, 0x0a, 0x0a, 0x52, 0x69, 0x73, 0x6b,
	0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x1b, 0x0a, 0x17, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x46,
	0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x22, 0x0a, 0x1e, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x46, 0x41, 0x43, 0x54,
	0x4f, 0x52, 0x5f, 0x52, 0x45, 0x44, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x45, 0x44, 0x5f, 0x43, 0x4f,
	0x4e, 0x54, 0x41, 0x43, 0x54, 0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x52, 0x49, 0x53, 0x4b, 0x5f,
	0x46, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x43,
	0x4f, 0x44, 0x45, 0x10, 0x02, 0x12, 0x21, 0x0a, 0x1d, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x46, 0x41,
	0x43, 0x54, 0x4f, 0x52, 0x5f, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x49,
	0x4e, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x03, 0x12, 0x24, 0x0a, 0x20, 0x52, 0x49, 0x53, 0x4b,
	0x5f, 0x46, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x43, 0x4f, 0x5f, 0x4f, 0x52, 0x44, 0x49, 0x4e, 0x41, 0x54, 0x45, 0x10, 0x04, 0x12, 0x2f,
	0x0a, 0x2b, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x46, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x45, 0x4b,
	0x59, 0x43, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x4e, 0x55,
	0x4d, 0x42, 0x45, 0x52, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x05, 0x12,
	0x1d, 0x0a, 0x19, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x46, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x48,
	0x49, 0x47, 0x48, 0x5f, 0x56, 0x45, 0x4c, 0x4f, 0x43, 0x49, 0x54, 0x59, 0x10, 0x06, 0x12, 0x23,
	0x0a, 0x1f, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x46, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x44, 0x45,
	0x56, 0x49, 0x43, 0x45, 0x5f, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x45, 0x44, 0x5f, 0x4c, 0x49, 0x53,
	0x54, 0x10, 0x07, 0x12, 0x25, 0x0a, 0x21, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x46, 0x41, 0x43, 0x54,
	0x4f, 0x52, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x49,
	0x53, 0x4b, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x10, 0x08, 0x12, 0x27, 0x0a, 0x23, 0x52, 0x49,
	0x53, 0x4b, 0x5f, 0x46, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x45, 0x4d, 0x50, 0x54, 0x59, 0x5f,
	0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x43, 0x4f, 0x44,
	0x45, 0x10, 0x09, 0x12, 0x19, 0x0a, 0x15, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x46, 0x41, 0x43, 0x54,
	0x4f, 0x52, 0x5f, 0x45, 0x50, 0x46, 0x4f, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x10, 0x0a, 0x2a, 0xe4,
	0x05, 0x0a, 0x15, 0x50, 0x61, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x28, 0x0a, 0x24, 0x50, 0x41, 0x4e, 0x5f,
	0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x43, 0x41, 0x54, 0x45,
	0x47, 0x4f, 0x52, 0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x2f, 0x0a, 0x2b, 0x50, 0x41, 0x4e, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x52,
	0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x46,
	0x55, 0x4c, 0x4c, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54, 0x43,
	0x48, 0x10, 0x01, 0x12, 0x30, 0x0a, 0x2c, 0x50, 0x41, 0x4e, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f,
	0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f,
	0x46, 0x49, 0x52, 0x53, 0x54, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41,
	0x54, 0x43, 0x48, 0x10, 0x02, 0x12, 0x2f, 0x0a, 0x2b, 0x50, 0x41, 0x4e, 0x5f, 0x4e, 0x41, 0x4d,
	0x45, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52,
	0x59, 0x5f, 0x4c, 0x41, 0x53, 0x54, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4d, 0x49, 0x53, 0x4d,
	0x41, 0x54, 0x43, 0x48, 0x10, 0x03, 0x12, 0x37, 0x0a, 0x33, 0x50, 0x41, 0x4e, 0x5f, 0x4e, 0x41,
	0x4d, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f,
	0x52, 0x59, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x4e, 0x5f, 0x4d, 0x49, 0x44, 0x44, 0x4c, 0x45,
	0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x10, 0x04, 0x12,
	0x2f, 0x0a, 0x2b, 0x50, 0x41, 0x4e, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x49,
	0x45, 0x57, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x53, 0x41, 0x4d, 0x45,
	0x5f, 0x50, 0x52, 0x4f, 0x4e, 0x55, 0x4e, 0x43, 0x49, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x05,
	0x12, 0x44, 0x0a, 0x40, 0x50, 0x41, 0x4e, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x52, 0x45, 0x56,
	0x49, 0x45, 0x57, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x41, 0x4c, 0x54,
	0x45, 0x52, 0x4e, 0x41, 0x54, 0x49, 0x56, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x4e, 0x55, 0x4e, 0x43,
	0x49, 0x41, 0x54, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x4e, 0x5f, 0x56, 0x41, 0x52, 0x49, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x06, 0x12, 0x37, 0x0a, 0x33, 0x50, 0x41, 0x4e, 0x5f, 0x4e, 0x41,
	0x4d, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f,
	0x52, 0x59, 0x5f, 0x50, 0x52, 0x49, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54,
	0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x10, 0x07, 0x12,
	0x31, 0x0a, 0x2d, 0x50, 0x41, 0x4e, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x49,
	0x45, 0x57, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4d, 0x49, 0x44, 0x44,
	0x4c, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54, 0x43, 0x48,
	0x10, 0x08, 0x12, 0x2a, 0x0a, 0x26, 0x50, 0x41, 0x4e, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x52,
	0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4a,
	0x55, 0x4d, 0x42, 0x4c, 0x45, 0x44, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x53, 0x10, 0x09, 0x12, 0x37,
	0x0a, 0x33, 0x50, 0x41, 0x4e, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45,
	0x57, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x50, 0x52, 0x49, 0x4d, 0x41,
	0x52, 0x59, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x4d, 0x41,
	0x54, 0x43, 0x48, 0x45, 0x53, 0x10, 0x0a, 0x12, 0x31, 0x0a, 0x2d, 0x50, 0x41, 0x4e, 0x5f, 0x4e,
	0x41, 0x4d, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47,
	0x4f, 0x52, 0x59, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x4e, 0x5f, 0x41, 0x42, 0x42, 0x52, 0x45,
	0x56, 0x49, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x0b, 0x12, 0x2c, 0x0a, 0x28, 0x50, 0x41,
	0x4e, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x43, 0x41,
	0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x46, 0x55, 0x4c, 0x4c, 0x5f, 0x4e, 0x41, 0x4d, 0x45,
	0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x0c, 0x12, 0x2b, 0x0a, 0x27, 0x50, 0x41, 0x4e, 0x5f,
	0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x43, 0x41, 0x54, 0x45,
	0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41,
	0x54, 0x43, 0x48, 0x10, 0x0d, 0x2a, 0x46, 0x0a, 0x07, 0x56, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74,
	0x12, 0x17, 0x0a, 0x13, 0x56, 0x45, 0x52, 0x44, 0x49, 0x43, 0x54, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x56, 0x45, 0x52,
	0x44, 0x49, 0x43, 0x54, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x56,
	0x45, 0x52, 0x44, 0x49, 0x43, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x10, 0x02, 0x2a, 0x96, 0x02,
	0x0a, 0x13, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x53, 0x6b, 0x69, 0x70, 0x52,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x1c, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x49,
	0x4e, 0x47, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x46, 0x45, 0x41, 0x54, 0x55,
	0x52, 0x45, 0x5f, 0x46, 0x4c, 0x41, 0x47, 0x5f, 0x4f, 0x46, 0x46, 0x10, 0x01, 0x12, 0x1c, 0x0a,
	0x18, 0x41, 0x50, 0x50, 0x5f, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x48, 0x45,
	0x43, 0x4b, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x57,
	0x4c, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x10, 0x03, 0x12, 0x19, 0x0a, 0x15, 0x46, 0x49, 0x4e, 0x49,
	0x54, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x52, 0x49, 0x56, 0x49, 0x4c, 0x45, 0x47,
	0x45, 0x10, 0x04, 0x12, 0x13, 0x0a, 0x0f, 0x4e, 0x4f, 0x4e, 0x5f, 0x57, 0x4c, 0x5f, 0x4f, 0x4c,
	0x44, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x10, 0x05, 0x12, 0x1b, 0x0a, 0x17, 0x42, 0x32, 0x42, 0x5f,
	0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f, 0x55,
	0x53, 0x45, 0x52, 0x10, 0x06, 0x12, 0x27, 0x0a, 0x23, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x4e, 0x45, 0x52, 0x5f, 0x41, 0x43, 0x51, 0x55,
	0x49, 0x53, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x10, 0x07, 0x12, 0x26,
	0x0a, 0x22, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x4f, 0x50, 0x54, 0x45, 0x44, 0x5f, 0x46, 0x4f, 0x52,
	0x5f, 0x4d, 0x49, 0x4e, 0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x41, 0x43, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x10, 0x08, 0x2a, 0xad, 0x01, 0x0a, 0x1f, 0x43, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67,
	0x53, 0x6b, 0x69, 0x70, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x4b,
	0x49, 0x50, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x53, 0x4b, 0x49, 0x50, 0x5f,
	0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x46, 0x4c, 0x41, 0x47, 0x5f, 0x4f, 0x46, 0x46, 0x10,
	0x01, 0x12, 0x2e, 0x0a, 0x2a, 0x53, 0x4b, 0x49, 0x50, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e,
	0x5f, 0x50, 0x52, 0x45, 0x53, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f,
	0x41, 0x50, 0x49, 0x5f, 0x4d, 0x41, 0x58, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x49, 0x45, 0x53, 0x10,
	0x02, 0x12, 0x23, 0x0a, 0x1f, 0x53, 0x4b, 0x49, 0x50, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e,
	0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x41, 0x50, 0x49, 0x5f, 0x44, 0x4f, 0x57, 0x4e,
	0x54, 0x49, 0x4d, 0x45, 0x10, 0x03, 0x2a, 0xc3, 0x02, 0x0a, 0x23, 0x45, 0x6d, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x36,
	0x0a, 0x32, 0x45, 0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x56, 0x45, 0x52,
	0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53,
	0x53, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x45, 0x50, 0x46, 0x5f, 0x44, 0x41,
	0x54, 0x41, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x01, 0x12, 0x18, 0x0a,
	0x14, 0x44, 0x4f, 0x4d, 0x41, 0x49, 0x4e, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x56, 0x45, 0x52,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x02, 0x12, 0x22, 0x0a, 0x1e, 0x56, 0x45, 0x52, 0x49, 0x46,
	0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x5f,
	0x54, 0x49, 0x4d, 0x45, 0x44, 0x5f, 0x4f, 0x55, 0x54, 0x10, 0x03, 0x12, 0x1d, 0x0a, 0x19, 0x50,
	0x41, 0x53, 0x53, 0x5f, 0x4e, 0x4f, 0x4e, 0x5f, 0x45, 0x50, 0x46, 0x4f, 0x5f, 0x52, 0x45, 0x47,
	0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x41, 0x4e, 0x59, 0x10, 0x04, 0x12, 0x18, 0x0a, 0x14, 0x41, 0x43,
	0x43, 0x45, 0x50, 0x54, 0x5f, 0x49, 0x4e, 0x41, 0x50, 0x50, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52,
	0x45, 0x45, 0x10, 0x05, 0x12, 0x1f, 0x0a, 0x1b, 0x45, 0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x4d, 0x45,
	0x4e, 0x54, 0x5f, 0x44, 0x45, 0x43, 0x4c, 0x41, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4f,
	0x4e, 0x4c, 0x59, 0x10, 0x06, 0x12, 0x13, 0x0a, 0x0f, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x5f,
	0x49, 0x4f, 0x53, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x10, 0x07, 0x12, 0x20, 0x0a, 0x1c, 0x41, 0x43,
	0x43, 0x45, 0x50, 0x54, 0x5f, 0x41, 0x4e, 0x44, 0x52, 0x4f, 0x49, 0x44, 0x5f, 0x50, 0x52, 0x4f,
	0x44, 0x5f, 0x4d, 0x4f, 0x56, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x08, 0x2a, 0xbd, 0x03, 0x0a,
	0x22, 0x57, 0x6f, 0x72, 0x6b, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x12, 0x36, 0x0a, 0x32, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x45, 0x4d, 0x41, 0x49,
	0x4c, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46,
	0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x46, 0x0a, 0x42, 0x57,
	0x4f, 0x52, 0x4b, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49,
	0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52,
	0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x53, 0x45, 0x4e, 0x44, 0x5f, 0x4f, 0x54, 0x50, 0x5f, 0x41,
	0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x53, 0x5f, 0x45, 0x58, 0x48, 0x41, 0x55, 0x53, 0x54, 0x45,
	0x44, 0x10, 0x01, 0x12, 0x48, 0x0a, 0x44, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x45, 0x4d, 0x41, 0x49,
	0x4c, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46,
	0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x56, 0x45,
	0x52, 0x49, 0x46, 0x59, 0x5f, 0x4f, 0x54, 0x50, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54,
	0x53, 0x5f, 0x45, 0x58, 0x48, 0x41, 0x55, 0x53, 0x54, 0x45, 0x44, 0x10, 0x02, 0x12, 0x3f, 0x0a,
	0x3b, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x56, 0x45, 0x52, 0x49,
	0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45,
	0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f,
	0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x44, 0x4f, 0x4d, 0x41, 0x49, 0x4e, 0x10, 0x03, 0x12, 0x42,
	0x0a, 0x3e, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x56, 0x45, 0x52,
	0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52,
	0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x41, 0x4e, 0x59,
	0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44,
	0x10, 0x04, 0x12, 0x48, 0x0a, 0x44, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c,
	0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41,
	0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x56, 0x45, 0x4e,
	0x44, 0x4f, 0x52, 0x5f, 0x41, 0x50, 0x49, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x53,
	0x5f, 0x45, 0x58, 0x48, 0x41, 0x55, 0x53, 0x54, 0x45, 0x44, 0x10, 0x05, 0x2a, 0xab, 0x02, 0x0a,
	0x25, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x56, 0x65, 0x72,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x3a, 0x0a, 0x36, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54,
	0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45,
	0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x3e, 0x0a, 0x3a, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x50,
	0x4f, 0x52, 0x54, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e,
	0x5f, 0x41, 0x50, 0x49, 0x5f, 0x4d, 0x41, 0x58, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x49, 0x45, 0x53,
	0x10, 0x01, 0x12, 0x3f, 0x0a, 0x3b, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x50,
	0x4f, 0x52, 0x54, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e,
	0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e,
	0x44, 0x10, 0x02, 0x12, 0x45, 0x0a, 0x41, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x45,
	0x50, 0x4f, 0x52, 0x54, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f,
	0x4e, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f,
	0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x03, 0x2a, 0xbc, 0x01, 0x0a, 0x1a, 0x50,
	0x61, 0x6e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x73, 0x70,
	0x65, 0x6e, 0x64, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x2d, 0x0a, 0x29, 0x50, 0x41, 0x4e,
	0x5f, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x53, 0x50,
	0x45, 0x4e, 0x44, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x32, 0x0a, 0x2e, 0x50, 0x41, 0x4e, 0x5f,
	0x56, 0x41, 0x4c, 0x49, 0x44, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x53, 0x50, 0x45,
	0x4e, 0x44, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x4d,
	0x41, 0x58, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x49, 0x45, 0x53, 0x10, 0x01, 0x12, 0x3b, 0x0a, 0x37,
	0x50, 0x41, 0x4e, 0x5f, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x55, 0x53, 0x50, 0x45, 0x4e, 0x44, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x56, 0x45,
	0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f,
	0x52, 0x45, 0x41, 0x43, 0x48, 0x45, 0x44, 0x10, 0x02, 0x2a, 0x7d, 0x0a, 0x16, 0x4c, 0x69, 0x6e,
	0x6b, 0x65, 0x64, 0x49, 0x6e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x24, 0x4c, 0x49, 0x4e, 0x4b, 0x45, 0x44, 0x49, 0x4e, 0x5f,
	0x56, 0x41, 0x4c, 0x49, 0x44, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x39, 0x0a,
	0x35, 0x4c, 0x49, 0x4e, 0x4b, 0x45, 0x44, 0x49, 0x4e, 0x5f, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x45, 0x4f, 0x4e, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x4e,
	0x41, 0x4d, 0x45, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x41, 0x4e, 0x59, 0x5f, 0x4e, 0x41, 0x4d, 0x45,
	0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x01, 0x2a, 0xfb, 0x01, 0x0a, 0x1f, 0x47, 0x53, 0x54,
	0x49, 0x4e, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x33, 0x0a, 0x2f,
	0x47, 0x53, 0x54, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x45, 0x53, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x43,
	0x48, 0x45, 0x43, 0x4b, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x41,
	0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x33, 0x0a, 0x2f, 0x47, 0x53, 0x54, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x45, 0x53, 0x45,
	0x4e, 0x43, 0x45, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53,
	0x53, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x47, 0x53, 0x54, 0x49, 0x4e, 0x5f, 0x46,
	0x4f, 0x55, 0x4e, 0x44, 0x10, 0x01, 0x12, 0x33, 0x0a, 0x2f, 0x47, 0x53, 0x54, 0x49, 0x4e, 0x5f,
	0x50, 0x52, 0x45, 0x53, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x53,
	0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x41, 0x50,
	0x49, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x02, 0x12, 0x39, 0x0a, 0x35, 0x47,
	0x53, 0x54, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x45, 0x53, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x43, 0x48,
	0x45, 0x43, 0x4b, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x41, 0x53,
	0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x49, 0x45, 0x53, 0x5f, 0x45, 0x58, 0x48, 0x41, 0x55,
	0x53, 0x54, 0x45, 0x44, 0x10, 0x03, 0x2a, 0xab, 0x03, 0x0a, 0x0a, 0x56, 0x4b, 0x59, 0x43, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x17, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x4f, 0x50,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x13,
	0x0a, 0x0f, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x53,
	0x4f, 0x10, 0x02, 0x12, 0x16, 0x0a, 0x12, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x4f, 0x50, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x43, 0x4b, 0x59, 0x43, 0x5f, 0x4f, 0x10, 0x03, 0x12, 0x17, 0x0a, 0x13, 0x56,
	0x4b, 0x59, 0x43, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x55, 0x44, 0x45,
	0x4e, 0x54, 0x10, 0x04, 0x12, 0x22, 0x0a, 0x1e, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x4f, 0x50, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x49, 0x41, 0x4c, 0x5f, 0x4b, 0x59, 0x43, 0x5f,
	0x44, 0x45, 0x44, 0x55, 0x50, 0x45, 0x10, 0x05, 0x12, 0x24, 0x0a, 0x20, 0x56, 0x4b, 0x59, 0x43,
	0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x4b, 0x59, 0x43, 0x5f, 0x4e, 0x55, 0x4d,
	0x42, 0x45, 0x52, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x06, 0x12, 0x21,
	0x0a, 0x1d, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x4f,
	0x57, 0x5f, 0x51, 0x55, 0x41, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x53, 0x10,
	0x07, 0x12, 0x1d, 0x0a, 0x19, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x46, 0x49, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x53, 0x10, 0x08,
	0x12, 0x28, 0x0a, 0x24, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x43, 0x4c, 0x4f, 0x53, 0x45, 0x44, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x52,
	0x45, 0x4f, 0x50, 0x45, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x09, 0x12, 0x20, 0x0a, 0x1c, 0x56, 0x4b,
	0x59, 0x43, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x5f, 0x4c, 0x49, 0x54,
	0x45, 0x5f, 0x43, 0x43, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x53, 0x10, 0x0a, 0x12, 0x27, 0x0a, 0x23,
	0x56, 0x4b, 0x59, 0x43, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4e, 0x4f, 0x4e, 0x5f,
	0x52, 0x45, 0x53, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44,
	0x49, 0x4e, 0x47, 0x10, 0x0b, 0x12, 0x1d, 0x0a, 0x19, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x4f, 0x50,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x45, 0x44, 0x45, 0x52, 0x41, 0x4c, 0x5f, 0x4c, 0x4f, 0x41,
	0x4e, 0x53, 0x10, 0x0c, 0x2a, 0xbc, 0x01, 0x0a, 0x13, 0x48, 0x65, 0x75, 0x72, 0x69, 0x73, 0x74,
	0x69, 0x63, 0x50, 0x61, 0x73, 0x73, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x21,
	0x48, 0x45, 0x55, 0x52, 0x49, 0x53, 0x54, 0x49, 0x43, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x5f, 0x52,
	0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x2c, 0x0a, 0x28, 0x48, 0x45, 0x55, 0x52, 0x49, 0x53, 0x54, 0x49, 0x43,
	0x5f, 0x50, 0x41, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x56,
	0x49, 0x43, 0x45, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10,
	0x01, 0x12, 0x22, 0x0a, 0x1e, 0x48, 0x45, 0x55, 0x52, 0x49, 0x53, 0x54, 0x49, 0x43, 0x5f, 0x50,
	0x41, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f,
	0x41, 0x47, 0x45, 0x10, 0x02, 0x12, 0x2c, 0x0a, 0x28, 0x48, 0x45, 0x55, 0x52, 0x49, 0x53, 0x54,
	0x49, 0x43, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x50,
	0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x42, 0x49, 0x4c, 0x4c, 0x49, 0x4e, 0x47, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x10, 0x03, 0x2a, 0xfa, 0x01, 0x0a, 0x07, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12,
	0x17, 0x0a, 0x13, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x46, 0x45, 0x41, 0x54,
	0x55, 0x52, 0x45, 0x5f, 0x53, 0x41, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x46, 0x45, 0x41, 0x54,
	0x55, 0x52, 0x45, 0x5f, 0x46, 0x49, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x10, 0x02, 0x12, 0x0e, 0x0a,
	0x0a, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x43, 0x43, 0x10, 0x03, 0x12, 0x0e, 0x0a,
	0x0a, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x50, 0x4c, 0x10, 0x04, 0x12, 0x14, 0x0a,
	0x10, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x55, 0x50, 0x49, 0x5f, 0x54, 0x50, 0x41,
	0x50, 0x10, 0x05, 0x12, 0x1b, 0x0a, 0x17, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x4e,
	0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x41, 0x10, 0x06,
	0x12, 0x1e, 0x0a, 0x1a, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x4e, 0x4f, 0x4e, 0x5f,
	0x52, 0x45, 0x53, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x41, 0x5f, 0x55, 0x53, 0x10, 0x07,
	0x12, 0x1b, 0x0a, 0x17, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x57, 0x45, 0x41, 0x4c,
	0x54, 0x48, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x10, 0x08, 0x12, 0x21, 0x0a,
	0x1d, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x4e, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53,
	0x49, 0x44, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x41, 0x5f, 0x51, 0x41, 0x54, 0x41, 0x52, 0x10, 0x09,
	0x2a, 0xfc, 0x04, 0x0a, 0x0c, 0x46, 0x69, 0x4c, 0x69, 0x74, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x12, 0x1e, 0x0a, 0x1a, 0x46, 0x49, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x5f, 0x53, 0x4f, 0x55,
	0x52, 0x43, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x1a, 0x0a, 0x16, 0x46, 0x49, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x5f, 0x53, 0x4f, 0x55,
	0x52, 0x43, 0x45, 0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x44, 0x4f, 0x42, 0x10, 0x01, 0x12, 0x1b, 0x0a,
	0x17, 0x46, 0x49, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x46, 0x49,
	0x5f, 0x4c, 0x49, 0x54, 0x45, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x4b, 0x59, 0x43,
	0x5f, 0x44, 0x45, 0x44, 0x55, 0x50, 0x45, 0x10, 0x03, 0x12, 0x26, 0x0a, 0x22, 0x46, 0x49, 0x5f,
	0x4c, 0x49, 0x54, 0x45, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x44, 0x45, 0x56, 0x49,
	0x43, 0x45, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10,
	0x04, 0x12, 0x2b, 0x0a, 0x27, 0x46, 0x49, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x5f, 0x53, 0x4f, 0x55,
	0x52, 0x43, 0x45, 0x5f, 0x45, 0x4b, 0x59, 0x43, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x44, 0x4f,
	0x42, 0x5f, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x05, 0x12, 0x23,
	0x0a, 0x1f, 0x46, 0x49, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45,
	0x5f, 0x49, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0x06, 0x12, 0x24, 0x0a, 0x20, 0x46, 0x49, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x5f, 0x53,
	0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x41, 0x46, 0x46, 0x4c, 0x55, 0x45, 0x4e, 0x43, 0x45, 0x5f,
	0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x37, 0x10, 0x07, 0x12, 0x2f, 0x0a, 0x2b, 0x46, 0x49, 0x5f,
	0x4c, 0x49, 0x54, 0x45, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x53, 0x41, 0x5f, 0x43,
	0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x08, 0x12, 0x24, 0x0a, 0x20, 0x46, 0x49,
	0x5f, 0x4c, 0x49, 0x54, 0x45, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x44, 0x49, 0x52,
	0x45, 0x43, 0x54, 0x5f, 0x54, 0x4f, 0x5f, 0x46, 0x49, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x10, 0x09,
	0x12, 0x1d, 0x0a, 0x19, 0x46, 0x49, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x5f, 0x53, 0x4f, 0x55, 0x52,
	0x43, 0x45, 0x5f, 0x53, 0x54, 0x55, 0x43, 0x4b, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x10, 0x0a, 0x12,
	0x21, 0x0a, 0x1d, 0x46, 0x49, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43,
	0x45, 0x5f, 0x44, 0x49, 0x52, 0x45, 0x43, 0x54, 0x5f, 0x54, 0x4f, 0x5f, 0x48, 0x4f, 0x4d, 0x45,
	0x10, 0x0b, 0x12, 0x36, 0x0a, 0x32, 0x46, 0x49, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x5f, 0x53, 0x4f,
	0x55, 0x52, 0x43, 0x45, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x50, 0x45, 0x44, 0x5f, 0x4f, 0x46, 0x46,
	0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x54, 0x4f, 0x5f, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f,
	0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x10, 0x0c, 0x12, 0x2a, 0x0a, 0x26, 0x46, 0x49,
	0x5f, 0x4c, 0x49, 0x54, 0x45, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x4c, 0x4f, 0x41,
	0x4e, 0x53, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x43,
	0x48, 0x45, 0x43, 0x4b, 0x10, 0x0d, 0x12, 0x28, 0x0a, 0x24, 0x46, 0x49, 0x5f, 0x4c, 0x49, 0x54,
	0x45, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x53, 0x41, 0x5f, 0x4f, 0x4e, 0x42, 0x4f,
	0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0x0e,
	0x12, 0x2d, 0x0a, 0x29, 0x46, 0x49, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x5f, 0x53, 0x4f, 0x55, 0x52,
	0x43, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x53,
	0x44, 0x4b, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x0f, 0x2a,
	0xd6, 0x01, 0x0a, 0x0d, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x1e, 0x0a, 0x1a, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x29, 0x0a, 0x25, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x49,
	0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x25, 0x0a, 0x21,
	0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4f,
	0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52,
	0x45, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x03, 0x12, 0x1b,
	0x0a, 0x17, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x49, 0x4e, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x04, 0x12, 0x1b, 0x0a, 0x17, 0x46,
	0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45,
	0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x05, 0x2a, 0x93, 0x03, 0x0a, 0x1a, 0x50, 0x61, 0x73,
	0x73, 0x70, 0x6f, 0x72, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2c, 0x0a, 0x28, 0x50, 0x41, 0x53, 0x53, 0x50,
	0x4f, 0x52, 0x54, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x31, 0x0a, 0x2d, 0x50, 0x41, 0x53, 0x53, 0x50, 0x4f, 0x52,
	0x54, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x52, 0x4f, 0x4e, 0x54, 0x5f, 0x44, 0x4f, 0x57, 0x4e,
	0x4c, 0x4f, 0x41, 0x44, 0x45, 0x44, 0x10, 0x01, 0x12, 0x30, 0x0a, 0x2c, 0x50, 0x41, 0x53, 0x53,
	0x50, 0x4f, 0x52, 0x54, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x44, 0x4f,
	0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x45, 0x44, 0x10, 0x02, 0x12, 0x2e, 0x0a, 0x2a, 0x50, 0x41,
	0x53, 0x53, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4f, 0x43, 0x52, 0x5f, 0x43,
	0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x03, 0x12, 0x28, 0x0a, 0x24, 0x50, 0x41,
	0x53, 0x53, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45,
	0x53, 0x53, 0x10, 0x04, 0x12, 0x27, 0x0a, 0x23, 0x50, 0x41, 0x53, 0x53, 0x50, 0x4f, 0x52, 0x54,
	0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x05, 0x12, 0x2f, 0x0a,
	0x2b, 0x50, 0x41, 0x53, 0x53, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49,
	0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x41,
	0x54, 0x41, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x52, 0x4d, 0x45, 0x44, 0x10, 0x06, 0x12, 0x2e,
	0x0a, 0x2a, 0x50, 0x41, 0x53, 0x53, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46,
	0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4d,
	0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x10, 0x07, 0x2a, 0x8a,
	0x03, 0x0a, 0x21, 0x50, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x30, 0x50, 0x41, 0x53, 0x53, 0x50, 0x4f, 0x52, 0x54,
	0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41,
	0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x46, 0x0a, 0x42, 0x50, 0x41,
	0x53, 0x53, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53,
	0x4f, 0x4e, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x49, 0x53, 0x53, 0x55,
	0x45, 0x44, 0x5f, 0x4f, 0x55, 0x54, 0x53, 0x49, 0x44, 0x45, 0x5f, 0x49, 0x4e, 0x44, 0x49, 0x41,
	0x10, 0x01, 0x12, 0x4d, 0x0a, 0x49, 0x50, 0x41, 0x53, 0x53, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x56,
	0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c,
	0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x50,
	0x4f, 0x52, 0x54, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54, 0x43,
	0x48, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x50, 0x53, 0x4b, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x10,
	0x02, 0x12, 0x4f, 0x0a, 0x4b, 0x50, 0x41, 0x53, 0x53, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x56, 0x45,
	0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55,
	0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x50, 0x4f,
	0x52, 0x54, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54,
	0x43, 0x48, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x50, 0x53, 0x4b, 0x5f, 0x44, 0x41, 0x54, 0x41,
	0x10, 0x03, 0x12, 0x47, 0x0a, 0x43, 0x50, 0x41, 0x53, 0x53, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x56,
	0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c,
	0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x50,
	0x4f, 0x52, 0x54, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55,
	0x4e, 0x44, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x53, 0x4b, 0x10, 0x04, 0x42, 0x58, 0x0a, 0x2a, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5a, 0x2a, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_user_onboarding_internal_onboarding_details_proto_rawDescOnce sync.Once
	file_api_user_onboarding_internal_onboarding_details_proto_rawDescData = file_api_user_onboarding_internal_onboarding_details_proto_rawDesc
)

func file_api_user_onboarding_internal_onboarding_details_proto_rawDescGZIP() []byte {
	file_api_user_onboarding_internal_onboarding_details_proto_rawDescOnce.Do(func() {
		file_api_user_onboarding_internal_onboarding_details_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_user_onboarding_internal_onboarding_details_proto_rawDescData)
	})
	return file_api_user_onboarding_internal_onboarding_details_proto_rawDescData
}

var file_api_user_onboarding_internal_onboarding_details_proto_enumTypes = make([]protoimpl.EnumInfo, 24)
var file_api_user_onboarding_internal_onboarding_details_proto_msgTypes = make([]protoimpl.MessageInfo, 45)
var file_api_user_onboarding_internal_onboarding_details_proto_goTypes = []interface{}{
	(OnboardingStage)(0),                           // 0: user.onboarding.OnboardingStage
	(OnboardingState)(0),                           // 1: user.onboarding.OnboardingState
	(UNNameCheckStatus)(0),                         // 2: user.onboarding.UNNameCheckStatus
	(OnboardingDetailsFieldMask)(0),                // 3: user.onboarding.OnboardingDetailsFieldMask
	(RiskFactor)(0),                                // 4: user.onboarding.RiskFactor
	(PanNameReviewCategory)(0),                     // 5: user.onboarding.PanNameReviewCategory
	(Verdict)(0),                                   // 6: user.onboarding.Verdict
	(ScreeningSkipReason)(0),                       // 7: user.onboarding.ScreeningSkipReason
	(CreditReportScreeningSkipReason)(0),           // 8: user.onboarding.CreditReportScreeningSkipReason
	(EmploymentVerificationSuccessReason)(0),       // 9: user.onboarding.EmploymentVerificationSuccessReason
	(WorkEmailVerificationFailureReason)(0),        // 10: user.onboarding.WorkEmailVerificationFailureReason
	(CreditReportVerificationSuccessReason)(0),     // 11: user.onboarding.CreditReportVerificationSuccessReason
	(PanValidationSuspendReason)(0),                // 12: user.onboarding.PanValidationSuspendReason
	(LinkedInValidationType)(0),                    // 13: user.onboarding.LinkedInValidationType
	(GSTINPresenceCheckSuccessReason)(0),           // 14: user.onboarding.GSTINPresenceCheckSuccessReason
	(VKYCOption)(0),                                // 15: user.onboarding.VKYCOption
	(HeuristicPassReason)(0),                       // 16: user.onboarding.HeuristicPassReason
	(Feature)(0),                                   // 17: user.onboarding.Feature
	(FiLiteSource)(0),                              // 18: user.onboarding.FiLiteSource
	(FeatureStatus)(0),                             // 19: user.onboarding.FeatureStatus
	(PassportVerificationStatus)(0),                // 20: user.onboarding.PassportVerificationStatus
	(PassportVerificationFailureReason)(0),         // 21: user.onboarding.PassportVerificationFailureReason
	(PreCustomerCreationMetadata_FailureReason)(0), // 22: user.onboarding.PreCustomerCreationMetadata.FailureReason
	(FeatureEligibility_Status)(0),                 // 23: user.onboarding.FeatureEligibility.Status
	(*OnboardingDetails)(nil),                      // 24: user.onboarding.OnboardingDetails
	(*AccountInformationInternal)(nil),             // 25: user.onboarding.AccountInformationInternal
	(*CardInformationInternal)(nil),                // 26: user.onboarding.CardInformationInternal
	(*SingleCardInfo)(nil),                         // 27: user.onboarding.SingleCardInfo
	(*StageDetails)(nil),                           // 28: user.onboarding.StageDetails
	(*KYCMetadata)(nil),                            // 29: user.onboarding.KYCMetadata
	(*StageInfo)(nil),                              // 30: user.onboarding.StageInfo
	(*StageProcLastResponse)(nil),                  // 31: user.onboarding.StageProcLastResponse
	(*PANNameCheck)(nil),                           // 32: user.onboarding.PANNameCheck
	(*StageMetadata)(nil),                          // 33: user.onboarding.StageMetadata
	(*PermissionMetaData)(nil),                     // 34: user.onboarding.PermissionMetaData
	(*AddMoneyMetadata)(nil),                       // 35: user.onboarding.AddMoneyMetadata
	(*PreAccountCreationAddMoneyMetadata)(nil),     // 36: user.onboarding.PreAccountCreationAddMoneyMetadata
	(*CreditReportFetchMetadata)(nil),              // 37: user.onboarding.CreditReportFetchMetadata
	(*PreCustomerCreationMetadata)(nil),            // 38: user.onboarding.PreCustomerCreationMetadata
	(*UpdateProfileDetailsMetadata)(nil),           // 39: user.onboarding.UpdateProfileDetailsMetadata
	(*LivenessMetadata)(nil),                       // 40: user.onboarding.LivenessMetadata
	(*RiskScreeningMetadata)(nil),                  // 41: user.onboarding.RiskScreeningMetadata
	(*PanManualReviewAnnotation)(nil),              // 42: user.onboarding.PanManualReviewAnnotation
	(*DebitCardNameCheck)(nil),                     // 43: user.onboarding.DebitCardNameCheck
	(*AppScreeningMetaData)(nil),                   // 44: user.onboarding.AppScreeningMetaData
	(*EKYCNameDOBValidationData)(nil),              // 45: user.onboarding.EKYCNameDOBValidationData
	(*PanValidation)(nil),                          // 46: user.onboarding.PanValidation
	(*VKYCMetadata)(nil),                           // 47: user.onboarding.VKYCMetadata
	(*InhouseVKYC)(nil),                            // 48: user.onboarding.InhouseVKYC
	(*IntentSelectionMetadata)(nil),                // 49: user.onboarding.IntentSelectionMetadata
	(*IntentOverrideInfo)(nil),                     // 50: user.onboarding.IntentOverrideInfo
	(*SoftIntentSelectionMetadata)(nil),            // 51: user.onboarding.SoftIntentSelectionMetadata
	(*FiLiteDetails)(nil),                          // 52: user.onboarding.FiLiteDetails
	(*FeatureInfo)(nil),                            // 53: user.onboarding.FeatureInfo
	(*FeatureDetails)(nil),                         // 54: user.onboarding.FeatureDetails
	(*PanAadharLinkageDetails)(nil),                // 55: user.onboarding.PanAadharLinkageDetails
	(*PassportVerificationMetadata)(nil),           // 56: user.onboarding.PassportVerificationMetadata
	(*PassportManualReviewDetails)(nil),            // 57: user.onboarding.PassportManualReviewDetails
	(*CrossValidationResult)(nil),                  // 58: user.onboarding.CrossValidationResult
	(*CrossValidationManualReviewInfo)(nil),        // 59: user.onboarding.CrossValidationManualReviewInfo
	(*CountryIdVerificationMetadata)(nil),          // 60: user.onboarding.CountryIdVerificationMetadata
	(*FeatureIntentSelectionInfo)(nil),             // 61: user.onboarding.FeatureIntentSelectionInfo
	(*FeatureEligibility)(nil),                     // 62: user.onboarding.FeatureEligibility
	(*FeatureLifecycle)(nil),                       // 63: user.onboarding.FeatureLifecycle
	nil,                                            // 64: user.onboarding.StageDetails.StageMappingEntry
	nil,                                            // 65: user.onboarding.StageProcLastResponse.AnalyticsEventPropertiesEntry
	nil,                                            // 66: user.onboarding.FeatureDetails.FeatureInfoEntry
	(*PassportVerificationMetadata_VerificationVendorAPIResults)(nil), // 67: user.onboarding.PassportVerificationMetadata.VerificationVendorAPIResults
	(*FeatureEligibility_AdditionalDataRequired)(nil),                 // 68: user.onboarding.FeatureEligibility.AdditionalDataRequired
	(vendorgateway.Vendor)(0),                                         // 69: vendorgateway.Vendor
	(*timestamppb.Timestamp)(nil),                                     // 70: google.protobuf.Timestamp
	(*card.BasicCardInfo)(nil),                                        // 71: card.BasicCardInfo
	(kyc.KYCLevel)(0),                                                 // 72: kyc.KYCLevel
	(kyc.FailureType)(0),                                              // 73: kyc.FailureType
	(customer.DedupeStatus)(0),                                        // 74: vendorgateway.openbanking.customer.DedupeStatus
	(kyc.UNNameCheckStatus)(0),                                        // 75: kyc.UNNameCheckStatus
	(common.BooleanEnum)(0),                                           // 76: api.typesv2.common.BooleanEnum
	(enums.FiniteCodeChannel)(0),                                      // 77: inappreferral.enums.FiniteCodeChannel
	(enums.FiniteCodeType)(0),                                         // 78: inappreferral.enums.FiniteCodeType
	(OnboardingIntent)(0),                                             // 79: user.onboarding.OnboardingIntent
	(OnboardingSoftIntent)(0),                                         // 80: user.onboarding.OnboardingSoftIntent
	(onboarding.FeatureOnboardingEntryPoint)(0),                       // 81: api.typesv2.deeplink_screen_option.onboarding.FeatureOnboardingEntryPoint
	(CrossValidationVerdict)(0),                                       // 82: user.onboarding.CrossValidationVerdict
	(CrossValidationCheck)(0),                                         // 83: user.onboarding.CrossValidationCheck
	(CrossValidationFailureReason)(0),                                 // 84: user.onboarding.CrossValidationFailureReason
	(CrossValidationDataSource)(0),                                    // 85: user.onboarding.CrossValidationDataSource
	(typesv2.Verdict)(0),                                              // 86: api.typesv2.Verdict
	(*deeplink.Deeplink)(nil),                                         // 87: frontend.deeplink.Deeplink
}
var file_api_user_onboarding_internal_onboarding_details_proto_depIdxs = []int32{
	28,  // 0: user.onboarding.OnboardingDetails.stage_details:type_name -> user.onboarding.StageDetails
	25,  // 1: user.onboarding.OnboardingDetails.account_info:type_name -> user.onboarding.AccountInformationInternal
	26,  // 2: user.onboarding.OnboardingDetails.card_info:type_name -> user.onboarding.CardInformationInternal
	69,  // 3: user.onboarding.OnboardingDetails.vendor:type_name -> vendorgateway.Vendor
	70,  // 4: user.onboarding.OnboardingDetails.completed_at:type_name -> google.protobuf.Timestamp
	33,  // 5: user.onboarding.OnboardingDetails.stage_metadata:type_name -> user.onboarding.StageMetadata
	70,  // 6: user.onboarding.OnboardingDetails.created_at:type_name -> google.protobuf.Timestamp
	0,   // 7: user.onboarding.OnboardingDetails.current_onboarding_stage:type_name -> user.onboarding.OnboardingStage
	17,  // 8: user.onboarding.OnboardingDetails.feature:type_name -> user.onboarding.Feature
	52,  // 9: user.onboarding.OnboardingDetails.fi_lite_details:type_name -> user.onboarding.FiLiteDetails
	54,  // 10: user.onboarding.OnboardingDetails.feature_details:type_name -> user.onboarding.FeatureDetails
	70,  // 11: user.onboarding.OnboardingDetails.deleted_at:type_name -> google.protobuf.Timestamp
	55,  // 12: user.onboarding.OnboardingDetails.pan_aadhar_linkage_details:type_name -> user.onboarding.PanAadharLinkageDetails
	31,  // 13: user.onboarding.OnboardingDetails.stage_proc_last_response:type_name -> user.onboarding.StageProcLastResponse
	27,  // 14: user.onboarding.CardInformationInternal.card_details:type_name -> user.onboarding.SingleCardInfo
	71,  // 15: user.onboarding.SingleCardInfo.basic_card_info:type_name -> card.BasicCardInfo
	64,  // 16: user.onboarding.StageDetails.stage_mapping:type_name -> user.onboarding.StageDetails.StageMappingEntry
	70,  // 17: user.onboarding.KYCMetadata.expiry_at:type_name -> google.protobuf.Timestamp
	72,  // 18: user.onboarding.KYCMetadata.last_updated_kyc_level:type_name -> kyc.KYCLevel
	73,  // 19: user.onboarding.KYCMetadata.failure_type:type_name -> kyc.FailureType
	73,  // 20: user.onboarding.KYCMetadata.ckyc_failure_type:type_name -> kyc.FailureType
	1,   // 21: user.onboarding.StageInfo.state:type_name -> user.onboarding.OnboardingState
	70,  // 22: user.onboarding.StageInfo.last_updated_at:type_name -> google.protobuf.Timestamp
	70,  // 23: user.onboarding.StageInfo.started_at:type_name -> google.protobuf.Timestamp
	0,   // 24: user.onboarding.StageProcLastResponse.stage:type_name -> user.onboarding.OnboardingStage
	65,  // 25: user.onboarding.StageProcLastResponse.analytics_event_properties:type_name -> user.onboarding.StageProcLastResponse.AnalyticsEventPropertiesEntry
	29,  // 26: user.onboarding.StageMetadata.kyc_metadata:type_name -> user.onboarding.KYCMetadata
	74,  // 27: user.onboarding.StageMetadata.dedupe_status:type_name -> vendorgateway.openbanking.customer.DedupeStatus
	74,  // 28: user.onboarding.StageMetadata.kyc_dedupe_status:type_name -> vendorgateway.openbanking.customer.DedupeStatus
	32,  // 29: user.onboarding.StageMetadata.pan_name_check:type_name -> user.onboarding.PANNameCheck
	44,  // 30: user.onboarding.StageMetadata.app_screening_data:type_name -> user.onboarding.AppScreeningMetaData
	43,  // 31: user.onboarding.StageMetadata.debit_card_name_check:type_name -> user.onboarding.DebitCardNameCheck
	45,  // 32: user.onboarding.StageMetadata.ekyc_name_dob_validation:type_name -> user.onboarding.EKYCNameDOBValidationData
	46,  // 33: user.onboarding.StageMetadata.pan_validation:type_name -> user.onboarding.PanValidation
	2,   // 34: user.onboarding.StageMetadata.u_n_name_check_status:type_name -> user.onboarding.UNNameCheckStatus
	74,  // 35: user.onboarding.StageMetadata.pre_customer_creation_dedupe_status:type_name -> vendorgateway.openbanking.customer.DedupeStatus
	42,  // 36: user.onboarding.StageMetadata.pan_manual_review_annotation:type_name -> user.onboarding.PanManualReviewAnnotation
	47,  // 37: user.onboarding.StageMetadata.vkyc_metadata:type_name -> user.onboarding.VKYCMetadata
	41,  // 38: user.onboarding.StageMetadata.risk_screening_metadata:type_name -> user.onboarding.RiskScreeningMetadata
	74,  // 39: user.onboarding.StageMetadata.latest_dedupe_status:type_name -> vendorgateway.openbanking.customer.DedupeStatus
	40,  // 40: user.onboarding.StageMetadata.liveness_metadata:type_name -> user.onboarding.LivenessMetadata
	75,  // 41: user.onboarding.StageMetadata.u_n_name_check_status_v2:type_name -> kyc.UNNameCheckStatus
	49,  // 42: user.onboarding.StageMetadata.intent_selection_metadata:type_name -> user.onboarding.IntentSelectionMetadata
	38,  // 43: user.onboarding.StageMetadata.pre_customer_creation_metadata:type_name -> user.onboarding.PreCustomerCreationMetadata
	39,  // 44: user.onboarding.StageMetadata.update_profile_details_metadata:type_name -> user.onboarding.UpdateProfileDetailsMetadata
	51,  // 45: user.onboarding.StageMetadata.soft_intent_selection_metadata:type_name -> user.onboarding.SoftIntentSelectionMetadata
	56,  // 46: user.onboarding.StageMetadata.passport_verification_metadata:type_name -> user.onboarding.PassportVerificationMetadata
	60,  // 47: user.onboarding.StageMetadata.country_id_verification_metadata:type_name -> user.onboarding.CountryIdVerificationMetadata
	58,  // 48: user.onboarding.StageMetadata.non_resident_cross_validation_result:type_name -> user.onboarding.CrossValidationResult
	37,  // 49: user.onboarding.StageMetadata.credit_report_fetch_metadata:type_name -> user.onboarding.CreditReportFetchMetadata
	36,  // 50: user.onboarding.StageMetadata.pre_account_creation_add_money_metadata:type_name -> user.onboarding.PreAccountCreationAddMoneyMetadata
	34,  // 51: user.onboarding.StageMetadata.permission_metadata:type_name -> user.onboarding.PermissionMetaData
	35,  // 52: user.onboarding.StageMetadata.add_money_metadata:type_name -> user.onboarding.AddMoneyMetadata
	22,  // 53: user.onboarding.PreCustomerCreationMetadata.failure_reason:type_name -> user.onboarding.PreCustomerCreationMetadata.FailureReason
	4,   // 54: user.onboarding.RiskScreeningMetadata.risk_blocking_reason:type_name -> user.onboarding.RiskFactor
	4,   // 55: user.onboarding.RiskScreeningMetadata.forced_manual_review_reason:type_name -> user.onboarding.RiskFactor
	6,   // 56: user.onboarding.PanManualReviewAnnotation.verdict:type_name -> user.onboarding.Verdict
	70,  // 57: user.onboarding.PanManualReviewAnnotation.reviewed_on:type_name -> google.protobuf.Timestamp
	5,   // 58: user.onboarding.PanManualReviewAnnotation.pan_name_review_category:type_name -> user.onboarding.PanNameReviewCategory
	76,  // 59: user.onboarding.AppScreeningMetaData.credit_report_found:type_name -> api.typesv2.common.BooleanEnum
	76,  // 60: user.onboarding.AppScreeningMetaData.pan_credit_report_check_attempted:type_name -> api.typesv2.common.BooleanEnum
	76,  // 61: user.onboarding.AppScreeningMetaData.consent_credit_report_download:type_name -> api.typesv2.common.BooleanEnum
	76,  // 62: user.onboarding.AppScreeningMetaData.credit_report_verification_passed:type_name -> api.typesv2.common.BooleanEnum
	76,  // 63: user.onboarding.AppScreeningMetaData.employment_verification_passed:type_name -> api.typesv2.common.BooleanEnum
	7,   // 64: user.onboarding.AppScreeningMetaData.skip_reason:type_name -> user.onboarding.ScreeningSkipReason
	8,   // 65: user.onboarding.AppScreeningMetaData.credit_report_flow_skip_reason:type_name -> user.onboarding.CreditReportScreeningSkipReason
	9,   // 66: user.onboarding.AppScreeningMetaData.employment_verification_success_reason:type_name -> user.onboarding.EmploymentVerificationSuccessReason
	11,  // 67: user.onboarding.AppScreeningMetaData.credit_report_verification_success_reason:type_name -> user.onboarding.CreditReportVerificationSuccessReason
	77,  // 68: user.onboarding.AppScreeningMetaData.finite_code_channel:type_name -> inappreferral.enums.FiniteCodeChannel
	78,  // 69: user.onboarding.AppScreeningMetaData.finite_code_type:type_name -> inappreferral.enums.FiniteCodeType
	76,  // 70: user.onboarding.AppScreeningMetaData.employment_declaration_only:type_name -> api.typesv2.common.BooleanEnum
	76,  // 71: user.onboarding.AppScreeningMetaData.gmail_verification_passed:type_name -> api.typesv2.common.BooleanEnum
	76,  // 72: user.onboarding.AppScreeningMetaData.work_email_verification_passed:type_name -> api.typesv2.common.BooleanEnum
	10,  // 73: user.onboarding.AppScreeningMetaData.work_email_verification_failure_reason:type_name -> user.onboarding.WorkEmailVerificationFailureReason
	76,  // 74: user.onboarding.AppScreeningMetaData.linkedin_verification_passed:type_name -> api.typesv2.common.BooleanEnum
	13,  // 75: user.onboarding.AppScreeningMetaData.linkedin_validation_type:type_name -> user.onboarding.LinkedInValidationType
	76,  // 76: user.onboarding.AppScreeningMetaData.heuristic_screening_passed:type_name -> api.typesv2.common.BooleanEnum
	76,  // 77: user.onboarding.AppScreeningMetaData.gstin_presence_check_passed:type_name -> api.typesv2.common.BooleanEnum
	14,  // 78: user.onboarding.AppScreeningMetaData.gstin_presence_check_success_reason:type_name -> user.onboarding.GSTINPresenceCheckSuccessReason
	76,  // 79: user.onboarding.AppScreeningMetaData.experimental_screening_bypass:type_name -> api.typesv2.common.BooleanEnum
	76,  // 80: user.onboarding.AppScreeningMetaData.screening_passed:type_name -> api.typesv2.common.BooleanEnum
	16,  // 81: user.onboarding.AppScreeningMetaData.heuristic_screening_pass_reason:type_name -> user.onboarding.HeuristicPassReason
	76,  // 82: user.onboarding.AppScreeningMetaData.form16_check_passed:type_name -> api.typesv2.common.BooleanEnum
	76,  // 83: user.onboarding.AppScreeningMetaData.uan_presence_check_passed:type_name -> api.typesv2.common.BooleanEnum
	76,  // 84: user.onboarding.AppScreeningMetaData.affluence_check_passed:type_name -> api.typesv2.common.BooleanEnum
	76,  // 85: user.onboarding.AppScreeningMetaData.credit_report_pan_matched:type_name -> api.typesv2.common.BooleanEnum
	70,  // 86: user.onboarding.PanValidation.block_till:type_name -> google.protobuf.Timestamp
	12,  // 87: user.onboarding.PanValidation.pan_validation_suspend_reason:type_name -> user.onboarding.PanValidationSuspendReason
	15,  // 88: user.onboarding.VKYCMetadata.vkyc_option:type_name -> user.onboarding.VKYCOption
	48,  // 89: user.onboarding.VKYCMetadata.inhouse_v_k_y_c:type_name -> user.onboarding.InhouseVKYC
	79,  // 90: user.onboarding.IntentSelectionMetadata.selection:type_name -> user.onboarding.OnboardingIntent
	79,  // 91: user.onboarding.IntentSelectionMetadata.choices:type_name -> user.onboarding.OnboardingIntent
	50,  // 92: user.onboarding.IntentSelectionMetadata.intent_override_info:type_name -> user.onboarding.IntentOverrideInfo
	80,  // 93: user.onboarding.SoftIntentSelectionMetadata.selection:type_name -> user.onboarding.OnboardingSoftIntent
	76,  // 94: user.onboarding.FiLiteDetails.is_enabled:type_name -> api.typesv2.common.BooleanEnum
	18,  // 95: user.onboarding.FiLiteDetails.fi_lite_source:type_name -> user.onboarding.FiLiteSource
	70,  // 96: user.onboarding.FiLiteDetails.accessibility_enabled_at:type_name -> google.protobuf.Timestamp
	0,   // 97: user.onboarding.FeatureInfo.current_stage:type_name -> user.onboarding.OnboardingStage
	70,  // 98: user.onboarding.FeatureInfo.completed_at:type_name -> google.protobuf.Timestamp
	19,  // 99: user.onboarding.FeatureInfo.feature_status:type_name -> user.onboarding.FeatureStatus
	81,  // 100: user.onboarding.FeatureInfo.feature_onboarding_entry_point:type_name -> api.typesv2.deeplink_screen_option.onboarding.FeatureOnboardingEntryPoint
	66,  // 101: user.onboarding.FeatureDetails.feature_info:type_name -> user.onboarding.FeatureDetails.FeatureInfoEntry
	76,  // 102: user.onboarding.PanAadharLinkageDetails.is_aadhar_digits_hash_mismatch:type_name -> api.typesv2.common.BooleanEnum
	76,  // 103: user.onboarding.PanAadharLinkageDetails.pan_aadhaar_linked:type_name -> api.typesv2.common.BooleanEnum
	20,  // 104: user.onboarding.PassportVerificationMetadata.status:type_name -> user.onboarding.PassportVerificationStatus
	70,  // 105: user.onboarding.PassportVerificationMetadata.attempted_at:type_name -> google.protobuf.Timestamp
	67,  // 106: user.onboarding.PassportVerificationMetadata.verification_vendor_api_results:type_name -> user.onboarding.PassportVerificationMetadata.VerificationVendorAPIResults
	21,  // 107: user.onboarding.PassportVerificationMetadata.failure_reason:type_name -> user.onboarding.PassportVerificationFailureReason
	57,  // 108: user.onboarding.PassportVerificationMetadata.manual_review_annotation:type_name -> user.onboarding.PassportManualReviewDetails
	6,   // 109: user.onboarding.PassportManualReviewDetails.verdict:type_name -> user.onboarding.Verdict
	70,  // 110: user.onboarding.PassportManualReviewDetails.reviewed_on:type_name -> google.protobuf.Timestamp
	82,  // 111: user.onboarding.CrossValidationResult.verdict:type_name -> user.onboarding.CrossValidationVerdict
	83,  // 112: user.onboarding.CrossValidationResult.check:type_name -> user.onboarding.CrossValidationCheck
	84,  // 113: user.onboarding.CrossValidationResult.failure_reason:type_name -> user.onboarding.CrossValidationFailureReason
	85,  // 114: user.onboarding.CrossValidationResult.data_source_1:type_name -> user.onboarding.CrossValidationDataSource
	85,  // 115: user.onboarding.CrossValidationResult.data_source_2:type_name -> user.onboarding.CrossValidationDataSource
	59,  // 116: user.onboarding.CrossValidationResult.cross_validation_manual_review_info:type_name -> user.onboarding.CrossValidationManualReviewInfo
	70,  // 117: user.onboarding.CrossValidationManualReviewInfo.reviewed_at:type_name -> google.protobuf.Timestamp
	80,  // 118: user.onboarding.FeatureIntentSelectionInfo.selected_soft_intents:type_name -> user.onboarding.OnboardingSoftIntent
	23,  // 119: user.onboarding.FeatureEligibility.status:type_name -> user.onboarding.FeatureEligibility.Status
	68,  // 120: user.onboarding.FeatureEligibility.additional_data_required:type_name -> user.onboarding.FeatureEligibility.AdditionalDataRequired
	17,  // 121: user.onboarding.FeatureLifecycle.feature:type_name -> user.onboarding.Feature
	61,  // 122: user.onboarding.FeatureLifecycle.intent_selection_info:type_name -> user.onboarding.FeatureIntentSelectionInfo
	62,  // 123: user.onboarding.FeatureLifecycle.eligibility_status:type_name -> user.onboarding.FeatureEligibility
	19,  // 124: user.onboarding.FeatureLifecycle.activation_status:type_name -> user.onboarding.FeatureStatus
	30,  // 125: user.onboarding.StageDetails.StageMappingEntry.value:type_name -> user.onboarding.StageInfo
	53,  // 126: user.onboarding.FeatureDetails.FeatureInfoEntry.value:type_name -> user.onboarding.FeatureInfo
	86,  // 127: user.onboarding.PassportVerificationMetadata.VerificationVendorAPIResults.passport_number_match:type_name -> api.typesv2.Verdict
	86,  // 128: user.onboarding.PassportVerificationMetadata.VerificationVendorAPIResults.user_name_match:type_name -> api.typesv2.Verdict
	86,  // 129: user.onboarding.PassportVerificationMetadata.VerificationVendorAPIResults.date_of_issue_match:type_name -> api.typesv2.Verdict
	87,  // 130: user.onboarding.FeatureEligibility.AdditionalDataRequired.deeplink:type_name -> frontend.deeplink.Deeplink
	131, // [131:131] is the sub-list for method output_type
	131, // [131:131] is the sub-list for method input_type
	131, // [131:131] is the sub-list for extension type_name
	131, // [131:131] is the sub-list for extension extendee
	0,   // [0:131] is the sub-list for field type_name
}

func init() { file_api_user_onboarding_internal_onboarding_details_proto_init() }
func file_api_user_onboarding_internal_onboarding_details_proto_init() {
	if File_api_user_onboarding_internal_onboarding_details_proto != nil {
		return
	}
	file_api_user_onboarding_internal_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OnboardingDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountInformationInternal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardInformationInternal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SingleCardInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StageDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KYCMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StageInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StageProcLastResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PANNameCheck); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StageMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PermissionMetaData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddMoneyMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreAccountCreationAddMoneyMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreditReportFetchMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreCustomerCreationMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateProfileDetailsMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LivenessMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskScreeningMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PanManualReviewAnnotation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DebitCardNameCheck); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppScreeningMetaData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EKYCNameDOBValidationData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PanValidation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VKYCMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InhouseVKYC); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IntentSelectionMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IntentOverrideInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SoftIntentSelectionMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FiLiteDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeatureInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeatureDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PanAadharLinkageDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PassportVerificationMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PassportManualReviewDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CrossValidationResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CrossValidationManualReviewInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CountryIdVerificationMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeatureIntentSelectionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeatureEligibility); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeatureLifecycle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PassportVerificationMetadata_VerificationVendorAPIResults); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_internal_onboarding_details_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeatureEligibility_AdditionalDataRequired); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_user_onboarding_internal_onboarding_details_proto_rawDesc,
			NumEnums:      24,
			NumMessages:   45,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_user_onboarding_internal_onboarding_details_proto_goTypes,
		DependencyIndexes: file_api_user_onboarding_internal_onboarding_details_proto_depIdxs,
		EnumInfos:         file_api_user_onboarding_internal_onboarding_details_proto_enumTypes,
		MessageInfos:      file_api_user_onboarding_internal_onboarding_details_proto_msgTypes,
	}.Build()
	File_api_user_onboarding_internal_onboarding_details_proto = out.File
	file_api_user_onboarding_internal_onboarding_details_proto_rawDesc = nil
	file_api_user_onboarding_internal_onboarding_details_proto_goTypes = nil
	file_api_user_onboarding_internal_onboarding_details_proto_depIdxs = nil
}
