// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/vendornotification/cx/sprinklr/sprinklr.proto

package sprinklr

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	SprinklrEventHandling_ProcessSprinklrEvent_FullMethodName = "/vendornotification.cx.sprinklr.SprinklrEventHandling/ProcessSprinklrEvent"
)

// SprinklrEventHandlingClient is the client API for SprinklrEventHandling service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SprinklrEventHandlingClient interface {
	ProcessSprinklrEvent(ctx context.Context, in *ProcessSprinklrEventRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type sprinklrEventHandlingClient struct {
	cc grpc.ClientConnInterface
}

func NewSprinklrEventHandlingClient(cc grpc.ClientConnInterface) SprinklrEventHandlingClient {
	return &sprinklrEventHandlingClient{cc}
}

func (c *sprinklrEventHandlingClient) ProcessSprinklrEvent(ctx context.Context, in *ProcessSprinklrEventRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, SprinklrEventHandling_ProcessSprinklrEvent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SprinklrEventHandlingServer is the server API for SprinklrEventHandling service.
// All implementations should embed UnimplementedSprinklrEventHandlingServer
// for forward compatibility
type SprinklrEventHandlingServer interface {
	ProcessSprinklrEvent(context.Context, *ProcessSprinklrEventRequest) (*emptypb.Empty, error)
}

// UnimplementedSprinklrEventHandlingServer should be embedded to have forward compatible implementations.
type UnimplementedSprinklrEventHandlingServer struct {
}

func (UnimplementedSprinklrEventHandlingServer) ProcessSprinklrEvent(context.Context, *ProcessSprinklrEventRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessSprinklrEvent not implemented")
}

// UnsafeSprinklrEventHandlingServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SprinklrEventHandlingServer will
// result in compilation errors.
type UnsafeSprinklrEventHandlingServer interface {
	mustEmbedUnimplementedSprinklrEventHandlingServer()
}

func RegisterSprinklrEventHandlingServer(s grpc.ServiceRegistrar, srv SprinklrEventHandlingServer) {
	s.RegisterService(&SprinklrEventHandling_ServiceDesc, srv)
}

func _SprinklrEventHandling_ProcessSprinklrEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessSprinklrEventRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SprinklrEventHandlingServer).ProcessSprinklrEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SprinklrEventHandling_ProcessSprinklrEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SprinklrEventHandlingServer).ProcessSprinklrEvent(ctx, req.(*ProcessSprinklrEventRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SprinklrEventHandling_ServiceDesc is the grpc.ServiceDesc for SprinklrEventHandling service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SprinklrEventHandling_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "vendornotification.cx.sprinklr.SprinklrEventHandling",
	HandlerType: (*SprinklrEventHandlingServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ProcessSprinklrEvent",
			Handler:    _SprinklrEventHandling_ProcessSprinklrEvent_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/vendornotification/cx/sprinklr/sprinklr.proto",
}
