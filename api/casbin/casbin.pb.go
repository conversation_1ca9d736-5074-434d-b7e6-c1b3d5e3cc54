// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/casbin/casbin.proto

package casbin

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AccessLevel int32

const (
	AccessLevel_ACCESS_LEVEL_UNSPECIFIED AccessLevel = 0
	// basic access for an agent to view some information about customer
	// This is at CX support level
	AccessLevel_AGENT AccessLevel = 1
	// full admin access, can create/update policies, do any actions
	// This is mainly for epifi employee/engineer, when the issue is escalated from CX to us
	AccessLevel_ADMIN AccessLevel = 2
	// Developer access, for debugging. Bypass any ticket checks if present
	AccessLevel_DEVELOPER AccessLevel = 3
	// QA access, for skipping freshdesk auth check based on ticket
	AccessLevel_QA AccessLevel = 4
	// role with access to view liveness videos
	AccessLevel_VIEW_LIVENESS_VIDEO AccessLevel = 5
	// super admin role in sherlock for payouts etc
	AccessLevel_SUPER_ADMIN AccessLevel = 6
	// this role will have access to waitlist freelance user approval flow using freshdesk ticket id
	AccessLevel_WAITLIST_APPROVER AccessLevel = 7
	// this roles will have access to waitlist freelance user approval flow list view and can approve without ticket check
	AccessLevel_WAITLIST_ADMIN_APPROVER AccessLevel = 8
	// this role will have access to call recordings and transcript in addition to what a normal agent has access to
	AccessLevel_QA_LEAD AccessLevel = 9
	// this role will be used for federal agent who will be accessing sherlock
	// this role will only have access to limited views and rpc's made for federal agents specifically
	AccessLevel_FEDERAL_AGENT AccessLevel = 10
	// this role will specifically have access to view liveness video, FM images(and some other views in future) and won't have any developer privileges
	AccessLevel_RISK_OPS AccessLevel = 11
	// FIT_ADMIN role allows access to manage configurations, rules and other features for FIT.
	AccessLevel_FIT_ADMIN AccessLevel = 12
	// restricted admin access, will have access to most of admins resources and action
	// with restriction on some of the critical flow
	AccessLevel_ADMIN_RESTRICTED AccessLevel = 13
	// this role will have access to user details for a given account number/ customer id
	AccessLevel_BIZ_ADMIN AccessLevel = 14
	// WEALTH_DEVELOPER will be super set of DEVELOPER role and will have same access to everything as DEVELOPER role + additional wealth related access.
	AccessLevel_WEALTH_DEVELOPER AccessLevel = 15
	// WEALTH_OPS will be super set of RISK_OPS role and will have same access to everything as RISK_OPS role + additional required access.
	AccessLevel_WEALTH_OPS AccessLevel = 16
	// RECURRING_PAYMENT_DEV role allows access to certain features for recurring payment
	AccessLevel_RECURRING_PAYMENT_DEV AccessLevel = 17
	// WEB_DEVELOPER role for web related tasks
	AccessLevel_WEB_DEVELOPER AccessLevel = 18
	// REWARDS_ADMIN role allows access to only rewards related dev actions
	AccessLevel_REWARDS_ADMIN AccessLevel = 19
	// ACCOUNT_OPS role allows access to account related activities like external account validation
	AccessLevel_ACCOUNT_OPS AccessLevel = 20
	// SALARY_DATA_OPS role allows access to salary program related data operations
	AccessLevel_SALARY_DATA_OPS AccessLevel = 21
	// Risk ops admin role will be used for some sensitive risk ops flow to restrict access to only a limited set of folks from risk ops team
	AccessLevel_RISK_OPS_ADMIN AccessLevel = 22
	// Replica of biz admin role with lesser information available- will be used to share sensitive information to external(e.g. CTS) admins
	AccessLevel_BIZ_ADMIN_RESTRICTED AccessLevel = 23
	// Data Ops is role for data ops analysts to make use of sherlock to perform activities like whitelisting screener domains etc.
	AccessLevel_DATA_OPS AccessLevel = 24
	// Federal Inward Remitter is a role to perform all inward international fund transfer for file activities like Inward Fund Transfer ops.
	AccessLevel_FEDERAL_INWARD_REMITTER AccessLevel = 25
	// Federal Outward Remitter is a role to perform all outward international fund transfer for file activities like LRS check, International transfer, etc.
	AccessLevel_FEDERAL_OUTWARD_REMITTER AccessLevel = 26
	// Federal Master Remitter is a role to perform all inward and outward international fund transfer ops for file activities.
	AccessLevel_FEDERAL_MASTER_REMITTER AccessLevel = 27
	// JUMP VENDOR RESPONSE is a role that have access to sensitive data to solve customer and vendor side issues for jump
	AccessLevel_JUMP_SENSITIVE_VENDOR_RESPONSE AccessLevel = 28
	// JUMP OPS is a role that get access to the jump recon file - on call dev can be part of this role
	AccessLevel_JUMP_OPS AccessLevel = 29
	// Risk eng role will be given to backend and ds engineers working on risk related features, This role will have access to risk specifc actions like upload risk cases
	AccessLevel_RISK_ENG AccessLevel = 30
	// Preapproved Loan liveness video reviewer federal role for CKYC user's verification through manual review
	AccessLevel_FEDERAL_LOAN_LIVENESS_REVIEWER AccessLevel = 31
	// us stocks ops role will be given to us stocks ops agents for all us stocks ops flows
	AccessLevel_US_STOCKS_OPS AccessLevel = 32
	// Role with access to all txn risk related components
	AccessLevel_RISK_OPS_TXN AccessLevel = 33
	// Risk Analyst tasked to perform KYC / Profile review of users
	AccessLevel_KYC_ANALYST AccessLevel = 34
	// Role for Finance team to run dev action
	AccessLevel_FINANCE_ADMIN AccessLevel = 35
	// Role will be given to agents who can whitelist users for salary B2B program
	AccessLevel_SALARY_WHITELIST_B2B AccessLevel = 36
	// This role helps an Ops agent to just view the file generated and can view the state of the file
	// but the Ops person will not be able to affect the remittance
	// The intention of this role is to help the ops agent and judge the remittance process do necessary actions like follow up
	AccessLevel_FEDERAL_MASTER_REMITTANCE_OPS AccessLevel = 37
	// Role for outcall agent to gather info from user in case management flow.
	AccessLevel_USER_OUTCALL AccessLevel = 38
	// role to manage different types of kyc agents
	AccessLevel_KYC_AGENT_ADMIN AccessLevel = 39
	// role for kyc agents who will perform in-person biometric kyc for users
	AccessLevel_BIOMETRIC_KYC_AGENT AccessLevel = 40
	// role that have access to sensitive user data related to US stocks.
	AccessLevel_USSTOCKS_SENSITIVE_DATA AccessLevel = 41
	// role that have access to sensitive user data related to mutual funds.
	AccessLevel_MUTUAL_FUNDS_SENSITIVE_DATA AccessLevel = 42
	// role that have access to sensitive user data related to wealth onboarding.
	AccessLevel_WEALTH_ONBOARDING_SENSITIVE_DATA AccessLevel = 43
	// role that have access to use data extraction
	AccessLevel_DATA_RETRIEVER AccessLevel = 44
	// role with access to salary related resources(eg. dev action)
	AccessLevel_SALARY_ADMIN AccessLevel = 45
	// role with access to approve segments
	AccessLevel_SEGMENTATION_ADMIN AccessLevel = 46
	// role with access to communicate with user's issue to cx support LLM.
	AccessLevel_CX_SUPPORT_LLM_TESTER AccessLevel = 47
	// role with access to connect VKYC call with users for verification
	AccessLevel_VKYC_CALL_AGENT AccessLevel = 48
	// role with access to audit VKYC call reports
	AccessLevel_VKYC_CALL_AUDITOR AccessLevel = 49
	// role with access to connect NBFC VKYC call with users for verification - VKYC agents
	AccessLevel_STOCK_GUARDIAN_VKYC_CALL_AGENT AccessLevel = 50
	// role with access to audit NBFC VKYC call reports - VKYC auditors
	AccessLevel_STOCK_GUARDIAN_VKYC_CALL_AUDITOR AccessLevel = 51
	// role with access to manual ops processes for managing loan accounts
	// eg. Mandate presentation, Disbursement, etc.
	AccessLevel_STOCK_GUARDIAN_LENDING_OPS AccessLevel = 52
	// role with access to DB states and dev actions for stockguardian services
	AccessLevel_STOCK_GUARDIAN_DEVELOPER AccessLevel = 53
	// role with access to view loans out call ticket details
	AccessLevel_LOANS_USER_OUTCALL AccessLevel = 54
	// federal escalation
	AccessLevel_ESCALATIONS AccessLevel = 55
	// Role required to view details for wealth insights use cases
	// For example, images captured from magic import for analysis
	AccessLevel_WEALTH_INSIGHTS AccessLevel = 56
)

// Enum value maps for AccessLevel.
var (
	AccessLevel_name = map[int32]string{
		0:  "ACCESS_LEVEL_UNSPECIFIED",
		1:  "AGENT",
		2:  "ADMIN",
		3:  "DEVELOPER",
		4:  "QA",
		5:  "VIEW_LIVENESS_VIDEO",
		6:  "SUPER_ADMIN",
		7:  "WAITLIST_APPROVER",
		8:  "WAITLIST_ADMIN_APPROVER",
		9:  "QA_LEAD",
		10: "FEDERAL_AGENT",
		11: "RISK_OPS",
		12: "FIT_ADMIN",
		13: "ADMIN_RESTRICTED",
		14: "BIZ_ADMIN",
		15: "WEALTH_DEVELOPER",
		16: "WEALTH_OPS",
		17: "RECURRING_PAYMENT_DEV",
		18: "WEB_DEVELOPER",
		19: "REWARDS_ADMIN",
		20: "ACCOUNT_OPS",
		21: "SALARY_DATA_OPS",
		22: "RISK_OPS_ADMIN",
		23: "BIZ_ADMIN_RESTRICTED",
		24: "DATA_OPS",
		25: "FEDERAL_INWARD_REMITTER",
		26: "FEDERAL_OUTWARD_REMITTER",
		27: "FEDERAL_MASTER_REMITTER",
		28: "JUMP_SENSITIVE_VENDOR_RESPONSE",
		29: "JUMP_OPS",
		30: "RISK_ENG",
		31: "FEDERAL_LOAN_LIVENESS_REVIEWER",
		32: "US_STOCKS_OPS",
		33: "RISK_OPS_TXN",
		34: "KYC_ANALYST",
		35: "FINANCE_ADMIN",
		36: "SALARY_WHITELIST_B2B",
		37: "FEDERAL_MASTER_REMITTANCE_OPS",
		38: "USER_OUTCALL",
		39: "KYC_AGENT_ADMIN",
		40: "BIOMETRIC_KYC_AGENT",
		41: "USSTOCKS_SENSITIVE_DATA",
		42: "MUTUAL_FUNDS_SENSITIVE_DATA",
		43: "WEALTH_ONBOARDING_SENSITIVE_DATA",
		44: "DATA_RETRIEVER",
		45: "SALARY_ADMIN",
		46: "SEGMENTATION_ADMIN",
		47: "CX_SUPPORT_LLM_TESTER",
		48: "VKYC_CALL_AGENT",
		49: "VKYC_CALL_AUDITOR",
		50: "STOCK_GUARDIAN_VKYC_CALL_AGENT",
		51: "STOCK_GUARDIAN_VKYC_CALL_AUDITOR",
		52: "STOCK_GUARDIAN_LENDING_OPS",
		53: "STOCK_GUARDIAN_DEVELOPER",
		54: "LOANS_USER_OUTCALL",
		55: "ESCALATIONS",
		56: "WEALTH_INSIGHTS",
	}
	AccessLevel_value = map[string]int32{
		"ACCESS_LEVEL_UNSPECIFIED":         0,
		"AGENT":                            1,
		"ADMIN":                            2,
		"DEVELOPER":                        3,
		"QA":                               4,
		"VIEW_LIVENESS_VIDEO":              5,
		"SUPER_ADMIN":                      6,
		"WAITLIST_APPROVER":                7,
		"WAITLIST_ADMIN_APPROVER":          8,
		"QA_LEAD":                          9,
		"FEDERAL_AGENT":                    10,
		"RISK_OPS":                         11,
		"FIT_ADMIN":                        12,
		"ADMIN_RESTRICTED":                 13,
		"BIZ_ADMIN":                        14,
		"WEALTH_DEVELOPER":                 15,
		"WEALTH_OPS":                       16,
		"RECURRING_PAYMENT_DEV":            17,
		"WEB_DEVELOPER":                    18,
		"REWARDS_ADMIN":                    19,
		"ACCOUNT_OPS":                      20,
		"SALARY_DATA_OPS":                  21,
		"RISK_OPS_ADMIN":                   22,
		"BIZ_ADMIN_RESTRICTED":             23,
		"DATA_OPS":                         24,
		"FEDERAL_INWARD_REMITTER":          25,
		"FEDERAL_OUTWARD_REMITTER":         26,
		"FEDERAL_MASTER_REMITTER":          27,
		"JUMP_SENSITIVE_VENDOR_RESPONSE":   28,
		"JUMP_OPS":                         29,
		"RISK_ENG":                         30,
		"FEDERAL_LOAN_LIVENESS_REVIEWER":   31,
		"US_STOCKS_OPS":                    32,
		"RISK_OPS_TXN":                     33,
		"KYC_ANALYST":                      34,
		"FINANCE_ADMIN":                    35,
		"SALARY_WHITELIST_B2B":             36,
		"FEDERAL_MASTER_REMITTANCE_OPS":    37,
		"USER_OUTCALL":                     38,
		"KYC_AGENT_ADMIN":                  39,
		"BIOMETRIC_KYC_AGENT":              40,
		"USSTOCKS_SENSITIVE_DATA":          41,
		"MUTUAL_FUNDS_SENSITIVE_DATA":      42,
		"WEALTH_ONBOARDING_SENSITIVE_DATA": 43,
		"DATA_RETRIEVER":                   44,
		"SALARY_ADMIN":                     45,
		"SEGMENTATION_ADMIN":               46,
		"CX_SUPPORT_LLM_TESTER":            47,
		"VKYC_CALL_AGENT":                  48,
		"VKYC_CALL_AUDITOR":                49,
		"STOCK_GUARDIAN_VKYC_CALL_AGENT":   50,
		"STOCK_GUARDIAN_VKYC_CALL_AUDITOR": 51,
		"STOCK_GUARDIAN_LENDING_OPS":       52,
		"STOCK_GUARDIAN_DEVELOPER":         53,
		"LOANS_USER_OUTCALL":               54,
		"ESCALATIONS":                      55,
		"WEALTH_INSIGHTS":                  56,
	}
)

func (x AccessLevel) Enum() *AccessLevel {
	p := new(AccessLevel)
	*p = x
	return p
}

func (x AccessLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AccessLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_api_casbin_casbin_proto_enumTypes[0].Descriptor()
}

func (AccessLevel) Type() protoreflect.EnumType {
	return &file_api_casbin_casbin_proto_enumTypes[0]
}

func (x AccessLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AccessLevel.Descriptor instead.
func (AccessLevel) EnumDescriptor() ([]byte, []int) {
	return file_api_casbin_casbin_proto_rawDescGZIP(), []int{0}
}

type Permission struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccessLevel string `protobuf:"bytes,1,opt,name=access_level,json=accessLevel,proto3" json:"access_level,omitempty"`
	Resource    string `protobuf:"bytes,2,opt,name=resource,proto3" json:"resource,omitempty"`
}

func (x *Permission) Reset() {
	*x = Permission{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casbin_casbin_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Permission) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Permission) ProtoMessage() {}

func (x *Permission) ProtoReflect() protoreflect.Message {
	mi := &file_api_casbin_casbin_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Permission.ProtoReflect.Descriptor instead.
func (*Permission) Descriptor() ([]byte, []int) {
	return file_api_casbin_casbin_proto_rawDescGZIP(), []int{0}
}

func (x *Permission) GetAccessLevel() string {
	if x != nil {
		return x.AccessLevel
	}
	return ""
}

func (x *Permission) GetResource() string {
	if x != nil {
		return x.Resource
	}
	return ""
}

type ResourceAccessibilityInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// resource name for which casbin evaluated the access request
	Resource string `protobuf:"bytes,1,opt,name=resource,proto3" json:"resource,omitempty"`
	// indicates whether the resource is accessible
	IsResourceAccessible common.BooleanEnum `protobuf:"varint,2,opt,name=is_resource_accessible,json=isResourceAccessible,proto3,enum=api.typesv2.common.BooleanEnum" json:"is_resource_accessible,omitempty"`
}

func (x *ResourceAccessibilityInfo) Reset() {
	*x = ResourceAccessibilityInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casbin_casbin_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceAccessibilityInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceAccessibilityInfo) ProtoMessage() {}

func (x *ResourceAccessibilityInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_casbin_casbin_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceAccessibilityInfo.ProtoReflect.Descriptor instead.
func (*ResourceAccessibilityInfo) Descriptor() ([]byte, []int) {
	return file_api_casbin_casbin_proto_rawDescGZIP(), []int{1}
}

func (x *ResourceAccessibilityInfo) GetResource() string {
	if x != nil {
		return x.Resource
	}
	return ""
}

func (x *ResourceAccessibilityInfo) GetIsResourceAccessible() common.BooleanEnum {
	if x != nil {
		return x.IsResourceAccessible
	}
	return common.BooleanEnum(0)
}

var File_api_casbin_casbin_proto protoreflect.FileDescriptor

var file_api_casbin_casbin_proto_rawDesc = []byte{
	0x0a, 0x17, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x73, 0x62, 0x69, 0x6e, 0x2f, 0x63, 0x61, 0x73,
	0x62, 0x69, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x63, 0x61, 0x73, 0x62, 0x69,
	0x6e, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x4b, 0x0a, 0x0a, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4c,
	0x65, 0x76, 0x65, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x22, 0x8e, 0x01, 0x0a, 0x19, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a,
	0x0a, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x55, 0x0a, 0x16, 0x69, 0x73,
	0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x14, 0x69, 0x73, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x69, 0x62, 0x6c,
	0x65, 0x2a, 0x92, 0x0a, 0x0a, 0x0b, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4c, 0x65, 0x76, 0x65,
	0x6c, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x4c, 0x45, 0x56, 0x45,
	0x4c, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x09, 0x0a, 0x05, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x41, 0x44,
	0x4d, 0x49, 0x4e, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x44, 0x45, 0x56, 0x45, 0x4c, 0x4f, 0x50,
	0x45, 0x52, 0x10, 0x03, 0x12, 0x06, 0x0a, 0x02, 0x51, 0x41, 0x10, 0x04, 0x12, 0x17, 0x0a, 0x13,
	0x56, 0x49, 0x45, 0x57, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x56, 0x49,
	0x44, 0x45, 0x4f, 0x10, 0x05, 0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x55, 0x50, 0x45, 0x52, 0x5f, 0x41,
	0x44, 0x4d, 0x49, 0x4e, 0x10, 0x06, 0x12, 0x15, 0x0a, 0x11, 0x57, 0x41, 0x49, 0x54, 0x4c, 0x49,
	0x53, 0x54, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x52, 0x10, 0x07, 0x12, 0x1b, 0x0a,
	0x17, 0x57, 0x41, 0x49, 0x54, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x41, 0x44, 0x4d, 0x49, 0x4e, 0x5f,
	0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x52, 0x10, 0x08, 0x12, 0x0b, 0x0a, 0x07, 0x51, 0x41,
	0x5f, 0x4c, 0x45, 0x41, 0x44, 0x10, 0x09, 0x12, 0x11, 0x0a, 0x0d, 0x46, 0x45, 0x44, 0x45, 0x52,
	0x41, 0x4c, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x10, 0x0a, 0x12, 0x0c, 0x0a, 0x08, 0x52, 0x49,
	0x53, 0x4b, 0x5f, 0x4f, 0x50, 0x53, 0x10, 0x0b, 0x12, 0x0d, 0x0a, 0x09, 0x46, 0x49, 0x54, 0x5f,
	0x41, 0x44, 0x4d, 0x49, 0x4e, 0x10, 0x0c, 0x12, 0x14, 0x0a, 0x10, 0x41, 0x44, 0x4d, 0x49, 0x4e,
	0x5f, 0x52, 0x45, 0x53, 0x54, 0x52, 0x49, 0x43, 0x54, 0x45, 0x44, 0x10, 0x0d, 0x12, 0x0d, 0x0a,
	0x09, 0x42, 0x49, 0x5a, 0x5f, 0x41, 0x44, 0x4d, 0x49, 0x4e, 0x10, 0x0e, 0x12, 0x14, 0x0a, 0x10,
	0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x44, 0x45, 0x56, 0x45, 0x4c, 0x4f, 0x50, 0x45, 0x52,
	0x10, 0x0f, 0x12, 0x0e, 0x0a, 0x0a, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x4f, 0x50, 0x53,
	0x10, 0x10, 0x12, 0x19, 0x0a, 0x15, 0x52, 0x45, 0x43, 0x55, 0x52, 0x52, 0x49, 0x4e, 0x47, 0x5f,
	0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x56, 0x10, 0x11, 0x12, 0x11, 0x0a,
	0x0d, 0x57, 0x45, 0x42, 0x5f, 0x44, 0x45, 0x56, 0x45, 0x4c, 0x4f, 0x50, 0x45, 0x52, 0x10, 0x12,
	0x12, 0x11, 0x0a, 0x0d, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x53, 0x5f, 0x41, 0x44, 0x4d, 0x49,
	0x4e, 0x10, 0x13, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4f,
	0x50, 0x53, 0x10, 0x14, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x44,
	0x41, 0x54, 0x41, 0x5f, 0x4f, 0x50, 0x53, 0x10, 0x15, 0x12, 0x12, 0x0a, 0x0e, 0x52, 0x49, 0x53,
	0x4b, 0x5f, 0x4f, 0x50, 0x53, 0x5f, 0x41, 0x44, 0x4d, 0x49, 0x4e, 0x10, 0x16, 0x12, 0x18, 0x0a,
	0x14, 0x42, 0x49, 0x5a, 0x5f, 0x41, 0x44, 0x4d, 0x49, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x54, 0x52,
	0x49, 0x43, 0x54, 0x45, 0x44, 0x10, 0x17, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x41, 0x54, 0x41, 0x5f,
	0x4f, 0x50, 0x53, 0x10, 0x18, 0x12, 0x1b, 0x0a, 0x17, 0x46, 0x45, 0x44, 0x45, 0x52, 0x41, 0x4c,
	0x5f, 0x49, 0x4e, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x4d, 0x49, 0x54, 0x54, 0x45, 0x52,
	0x10, 0x19, 0x12, 0x1c, 0x0a, 0x18, 0x46, 0x45, 0x44, 0x45, 0x52, 0x41, 0x4c, 0x5f, 0x4f, 0x55,
	0x54, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x4d, 0x49, 0x54, 0x54, 0x45, 0x52, 0x10, 0x1a,
	0x12, 0x1b, 0x0a, 0x17, 0x46, 0x45, 0x44, 0x45, 0x52, 0x41, 0x4c, 0x5f, 0x4d, 0x41, 0x53, 0x54,
	0x45, 0x52, 0x5f, 0x52, 0x45, 0x4d, 0x49, 0x54, 0x54, 0x45, 0x52, 0x10, 0x1b, 0x12, 0x22, 0x0a,
	0x1e, 0x4a, 0x55, 0x4d, 0x50, 0x5f, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0x5f,
	0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x10,
	0x1c, 0x12, 0x0c, 0x0a, 0x08, 0x4a, 0x55, 0x4d, 0x50, 0x5f, 0x4f, 0x50, 0x53, 0x10, 0x1d, 0x12,
	0x0c, 0x0a, 0x08, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x45, 0x4e, 0x47, 0x10, 0x1e, 0x12, 0x22, 0x0a,
	0x1e, 0x46, 0x45, 0x44, 0x45, 0x52, 0x41, 0x4c, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4c, 0x49,
	0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x45, 0x52, 0x10,
	0x1f, 0x12, 0x11, 0x0a, 0x0d, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x4f,
	0x50, 0x53, 0x10, 0x20, 0x12, 0x10, 0x0a, 0x0c, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x4f, 0x50, 0x53,
	0x5f, 0x54, 0x58, 0x4e, 0x10, 0x21, 0x12, 0x0f, 0x0a, 0x0b, 0x4b, 0x59, 0x43, 0x5f, 0x41, 0x4e,
	0x41, 0x4c, 0x59, 0x53, 0x54, 0x10, 0x22, 0x12, 0x11, 0x0a, 0x0d, 0x46, 0x49, 0x4e, 0x41, 0x4e,
	0x43, 0x45, 0x5f, 0x41, 0x44, 0x4d, 0x49, 0x4e, 0x10, 0x23, 0x12, 0x18, 0x0a, 0x14, 0x53, 0x41,
	0x4c, 0x41, 0x52, 0x59, 0x5f, 0x57, 0x48, 0x49, 0x54, 0x45, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x42,
	0x32, 0x42, 0x10, 0x24, 0x12, 0x21, 0x0a, 0x1d, 0x46, 0x45, 0x44, 0x45, 0x52, 0x41, 0x4c, 0x5f,
	0x4d, 0x41, 0x53, 0x54, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x4d, 0x49, 0x54, 0x54, 0x41, 0x4e, 0x43,
	0x45, 0x5f, 0x4f, 0x50, 0x53, 0x10, 0x25, 0x12, 0x10, 0x0a, 0x0c, 0x55, 0x53, 0x45, 0x52, 0x5f,
	0x4f, 0x55, 0x54, 0x43, 0x41, 0x4c, 0x4c, 0x10, 0x26, 0x12, 0x13, 0x0a, 0x0f, 0x4b, 0x59, 0x43,
	0x5f, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x44, 0x4d, 0x49, 0x4e, 0x10, 0x27, 0x12, 0x17,
	0x0a, 0x13, 0x42, 0x49, 0x4f, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43, 0x5f, 0x4b, 0x59, 0x43, 0x5f,
	0x41, 0x47, 0x45, 0x4e, 0x54, 0x10, 0x28, 0x12, 0x1b, 0x0a, 0x17, 0x55, 0x53, 0x53, 0x54, 0x4f,
	0x43, 0x4b, 0x53, 0x5f, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0x5f, 0x44, 0x41,
	0x54, 0x41, 0x10, 0x29, 0x12, 0x1f, 0x0a, 0x1b, 0x4d, 0x55, 0x54, 0x55, 0x41, 0x4c, 0x5f, 0x46,
	0x55, 0x4e, 0x44, 0x53, 0x5f, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0x5f, 0x44,
	0x41, 0x54, 0x41, 0x10, 0x2a, 0x12, 0x24, 0x0a, 0x20, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f,
	0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x45, 0x4e, 0x53, 0x49,
	0x54, 0x49, 0x56, 0x45, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x10, 0x2b, 0x12, 0x12, 0x0a, 0x0e, 0x44,
	0x41, 0x54, 0x41, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x49, 0x45, 0x56, 0x45, 0x52, 0x10, 0x2c, 0x12,
	0x10, 0x0a, 0x0c, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x41, 0x44, 0x4d, 0x49, 0x4e, 0x10,
	0x2d, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x45, 0x47, 0x4d, 0x45, 0x4e, 0x54, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x41, 0x44, 0x4d, 0x49, 0x4e, 0x10, 0x2e, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x58, 0x5f,
	0x53, 0x55, 0x50, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x4c, 0x4c, 0x4d, 0x5f, 0x54, 0x45, 0x53, 0x54,
	0x45, 0x52, 0x10, 0x2f, 0x12, 0x13, 0x0a, 0x0f, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x43, 0x41, 0x4c,
	0x4c, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x10, 0x30, 0x12, 0x15, 0x0a, 0x11, 0x56, 0x4b, 0x59,
	0x43, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x41, 0x55, 0x44, 0x49, 0x54, 0x4f, 0x52, 0x10, 0x31,
	0x12, 0x22, 0x0a, 0x1e, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x47, 0x55, 0x41, 0x52, 0x44, 0x49,
	0x41, 0x4e, 0x5f, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x41, 0x47, 0x45,
	0x4e, 0x54, 0x10, 0x32, 0x12, 0x24, 0x0a, 0x20, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x47, 0x55,
	0x41, 0x52, 0x44, 0x49, 0x41, 0x4e, 0x5f, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x43, 0x41, 0x4c, 0x4c,
	0x5f, 0x41, 0x55, 0x44, 0x49, 0x54, 0x4f, 0x52, 0x10, 0x33, 0x12, 0x1e, 0x0a, 0x1a, 0x53, 0x54,
	0x4f, 0x43, 0x4b, 0x5f, 0x47, 0x55, 0x41, 0x52, 0x44, 0x49, 0x41, 0x4e, 0x5f, 0x4c, 0x45, 0x4e,
	0x44, 0x49, 0x4e, 0x47, 0x5f, 0x4f, 0x50, 0x53, 0x10, 0x34, 0x12, 0x1c, 0x0a, 0x18, 0x53, 0x54,
	0x4f, 0x43, 0x4b, 0x5f, 0x47, 0x55, 0x41, 0x52, 0x44, 0x49, 0x41, 0x4e, 0x5f, 0x44, 0x45, 0x56,
	0x45, 0x4c, 0x4f, 0x50, 0x45, 0x52, 0x10, 0x35, 0x12, 0x16, 0x0a, 0x12, 0x4c, 0x4f, 0x41, 0x4e,
	0x53, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x4f, 0x55, 0x54, 0x43, 0x41, 0x4c, 0x4c, 0x10, 0x36,
	0x12, 0x0f, 0x0a, 0x0b, 0x45, 0x53, 0x43, 0x41, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10,
	0x37, 0x12, 0x13, 0x0a, 0x0f, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x49, 0x4e, 0x53, 0x49,
	0x47, 0x48, 0x54, 0x53, 0x10, 0x38, 0x42, 0x46, 0x0a, 0x21, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x61, 0x73, 0x62, 0x69, 0x6e, 0x5a, 0x21, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x73, 0x62, 0x69, 0x6e, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_casbin_casbin_proto_rawDescOnce sync.Once
	file_api_casbin_casbin_proto_rawDescData = file_api_casbin_casbin_proto_rawDesc
)

func file_api_casbin_casbin_proto_rawDescGZIP() []byte {
	file_api_casbin_casbin_proto_rawDescOnce.Do(func() {
		file_api_casbin_casbin_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_casbin_casbin_proto_rawDescData)
	})
	return file_api_casbin_casbin_proto_rawDescData
}

var file_api_casbin_casbin_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_casbin_casbin_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_casbin_casbin_proto_goTypes = []interface{}{
	(AccessLevel)(0),                  // 0: casbin.AccessLevel
	(*Permission)(nil),                // 1: casbin.Permission
	(*ResourceAccessibilityInfo)(nil), // 2: casbin.ResourceAccessibilityInfo
	(common.BooleanEnum)(0),           // 3: api.typesv2.common.BooleanEnum
}
var file_api_casbin_casbin_proto_depIdxs = []int32{
	3, // 0: casbin.ResourceAccessibilityInfo.is_resource_accessible:type_name -> api.typesv2.common.BooleanEnum
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_api_casbin_casbin_proto_init() }
func file_api_casbin_casbin_proto_init() {
	if File_api_casbin_casbin_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_casbin_casbin_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Permission); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casbin_casbin_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceAccessibilityInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_casbin_casbin_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_casbin_casbin_proto_goTypes,
		DependencyIndexes: file_api_casbin_casbin_proto_depIdxs,
		EnumInfos:         file_api_casbin_casbin_proto_enumTypes,
		MessageInfos:      file_api_casbin_casbin_proto_msgTypes,
	}.Build()
	File_api_casbin_casbin_proto = out.File
	file_api_casbin_casbin_proto_rawDesc = nil
	file_api_casbin_casbin_proto_goTypes = nil
	file_api_casbin_casbin_proto_depIdxs = nil
}
