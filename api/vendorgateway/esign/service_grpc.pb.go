// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/vendorgateway/esign/service.proto

package esign

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ESign_CreateESign_FullMethodName      = "/vendorgateway.esign.ESign/CreateESign"
	ESign_CheckESignStatus_FullMethodName = "/vendorgateway.esign.ESign/CheckESignStatus"
)

// ESignClient is the client API for ESign service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ESignClient interface {
	CreateESign(ctx context.Context, in *CreateESignRequest, opts ...grpc.CallOption) (*CreateESignResponse, error)
	CheckESignStatus(ctx context.Context, in *CheckESignStatusRequest, opts ...grpc.CallOption) (*CheckESignStatusResponse, error)
}

type eSignClient struct {
	cc grpc.ClientConnInterface
}

func NewESignClient(cc grpc.ClientConnInterface) ESignClient {
	return &eSignClient{cc}
}

func (c *eSignClient) CreateESign(ctx context.Context, in *CreateESignRequest, opts ...grpc.CallOption) (*CreateESignResponse, error) {
	out := new(CreateESignResponse)
	err := c.cc.Invoke(ctx, ESign_CreateESign_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eSignClient) CheckESignStatus(ctx context.Context, in *CheckESignStatusRequest, opts ...grpc.CallOption) (*CheckESignStatusResponse, error) {
	out := new(CheckESignStatusResponse)
	err := c.cc.Invoke(ctx, ESign_CheckESignStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ESignServer is the server API for ESign service.
// All implementations should embed UnimplementedESignServer
// for forward compatibility
type ESignServer interface {
	CreateESign(context.Context, *CreateESignRequest) (*CreateESignResponse, error)
	CheckESignStatus(context.Context, *CheckESignStatusRequest) (*CheckESignStatusResponse, error)
}

// UnimplementedESignServer should be embedded to have forward compatible implementations.
type UnimplementedESignServer struct {
}

func (UnimplementedESignServer) CreateESign(context.Context, *CreateESignRequest) (*CreateESignResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateESign not implemented")
}
func (UnimplementedESignServer) CheckESignStatus(context.Context, *CheckESignStatusRequest) (*CheckESignStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckESignStatus not implemented")
}

// UnsafeESignServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ESignServer will
// result in compilation errors.
type UnsafeESignServer interface {
	mustEmbedUnimplementedESignServer()
}

func RegisterESignServer(s grpc.ServiceRegistrar, srv ESignServer) {
	s.RegisterService(&ESign_ServiceDesc, srv)
}

func _ESign_CreateESign_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateESignRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESignServer).CreateESign(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ESign_CreateESign_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESignServer).CreateESign(ctx, req.(*CreateESignRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ESign_CheckESignStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckESignStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESignServer).CheckESignStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ESign_CheckESignStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESignServer).CheckESignStatus(ctx, req.(*CheckESignStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ESign_ServiceDesc is the grpc.ServiceDesc for ESign service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ESign_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "vendorgateway.esign.ESign",
	HandlerType: (*ESignServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateESign",
			Handler:    _ESign_CreateESign_Handler,
		},
		{
			MethodName: "CheckESignStatus",
			Handler:    _ESign_CheckESignStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/vendorgateway/esign/service.proto",
}
