// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendorgateway/risk/service.proto

package risk

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	afu "github.com/epifi/gamma/api/auth/afu"
	liveness "github.com/epifi/gamma/api/auth/liveness"
	employment "github.com/epifi/gamma/api/employment"
	kyc "github.com/epifi/gamma/api/kyc"
	case_management "github.com/epifi/gamma/api/risk/case_management"
	screener "github.com/epifi/gamma/api/screener"
	external "github.com/epifi/gamma/api/tiering/external"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	user "github.com/epifi/gamma/api/user"
	inhouse "github.com/epifi/gamma/api/vendors/inhouse"
	date "google.golang.org/genproto/googleapis/type/date"
	latlng "google.golang.org/genproto/googleapis/type/latlng"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Outcome int32

const (
	Outcome_OUTCOME_UNSPECIFIED Outcome = 0
	// Indicates the request has been accepted and the full response is available.
	Outcome_OUTCOME_ACCEPTED Outcome = 1
	// Indicates the request is still in progress for some services.
	Outcome_OUTCOME_AWAITING Outcome = 2
	// Indicates the request has been rejected.
	Outcome_OUTCOME_REJECTED Outcome = 3
)

// Enum value maps for Outcome.
var (
	Outcome_name = map[int32]string{
		0: "OUTCOME_UNSPECIFIED",
		1: "OUTCOME_ACCEPTED",
		2: "OUTCOME_AWAITING",
		3: "OUTCOME_REJECTED",
	}
	Outcome_value = map[string]int32{
		"OUTCOME_UNSPECIFIED": 0,
		"OUTCOME_ACCEPTED":    1,
		"OUTCOME_AWAITING":    2,
		"OUTCOME_REJECTED":    3,
	}
)

func (x Outcome) Enum() *Outcome {
	p := new(Outcome)
	*p = x
	return p
}

func (x Outcome) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Outcome) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_risk_service_proto_enumTypes[0].Descriptor()
}

func (Outcome) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_risk_service_proto_enumTypes[0]
}

func (x Outcome) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Outcome.Descriptor instead.
func (Outcome) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_risk_service_proto_rawDescGZIP(), []int{0}
}

// AttributesStatus represents the status of a particular attributes service i.e. EmailAttributes, PhoneAttributes, etc.
type AttributeStatus int32

const (
	AttributeStatus_ATTRIBUTE_STATUS_OK     AttributeStatus = 0
	AttributeStatus_ATTRIBUTE_STATUS_FAILED AttributeStatus = 1
	// NOT_FOUND indicates attributes not found
	AttributeStatus_ATTRIBUTE_STATUS_NOT_FOUND AttributeStatus = 2
	// NOT_EXECUTED indicates attribute service not executed
	AttributeStatus_ATTRIBUTE_STATUS_NOT_EXECUTED AttributeStatus = 3
)

// Enum value maps for AttributeStatus.
var (
	AttributeStatus_name = map[int32]string{
		0: "ATTRIBUTE_STATUS_OK",
		1: "ATTRIBUTE_STATUS_FAILED",
		2: "ATTRIBUTE_STATUS_NOT_FOUND",
		3: "ATTRIBUTE_STATUS_NOT_EXECUTED",
	}
	AttributeStatus_value = map[string]int32{
		"ATTRIBUTE_STATUS_OK":           0,
		"ATTRIBUTE_STATUS_FAILED":       1,
		"ATTRIBUTE_STATUS_NOT_FOUND":    2,
		"ATTRIBUTE_STATUS_NOT_EXECUTED": 3,
	}
)

func (x AttributeStatus) Enum() *AttributeStatus {
	p := new(AttributeStatus)
	*p = x
	return p
}

func (x AttributeStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AttributeStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_risk_service_proto_enumTypes[1].Descriptor()
}

func (AttributeStatus) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_risk_service_proto_enumTypes[1]
}

func (x AttributeStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AttributeStatus.Descriptor instead.
func (AttributeStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_risk_service_proto_rawDescGZIP(), []int{1}
}

type DetectRiskRequest_ModelVersion int32

const (
	DetectRiskRequest_MODEL_VERSION_UNSPECIFIED DetectRiskRequest_ModelVersion = 0
	DetectRiskRequest_MODEL_VERSION_V0          DetectRiskRequest_ModelVersion = 1
	DetectRiskRequest_MODEL_VERSION_V1          DetectRiskRequest_ModelVersion = 2
)

// Enum value maps for DetectRiskRequest_ModelVersion.
var (
	DetectRiskRequest_ModelVersion_name = map[int32]string{
		0: "MODEL_VERSION_UNSPECIFIED",
		1: "MODEL_VERSION_V0",
		2: "MODEL_VERSION_V1",
	}
	DetectRiskRequest_ModelVersion_value = map[string]int32{
		"MODEL_VERSION_UNSPECIFIED": 0,
		"MODEL_VERSION_V0":          1,
		"MODEL_VERSION_V1":          2,
	}
)

func (x DetectRiskRequest_ModelVersion) Enum() *DetectRiskRequest_ModelVersion {
	p := new(DetectRiskRequest_ModelVersion)
	*p = x
	return p
}

func (x DetectRiskRequest_ModelVersion) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DetectRiskRequest_ModelVersion) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_risk_service_proto_enumTypes[2].Descriptor()
}

func (DetectRiskRequest_ModelVersion) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_risk_service_proto_enumTypes[2]
}

func (x DetectRiskRequest_ModelVersion) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DetectRiskRequest_ModelVersion.Descriptor instead.
func (DetectRiskRequest_ModelVersion) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_risk_service_proto_rawDescGZIP(), []int{16, 0}
}

type DetectLocationRiskResponse_Status int32

const (
	DetectLocationRiskResponse_OK               DetectLocationRiskResponse_Status = 0
	DetectLocationRiskResponse_INVALID_ARGUMENT DetectLocationRiskResponse_Status = 3
	DetectLocationRiskResponse_NOT_FOUND        DetectLocationRiskResponse_Status = 5
	DetectLocationRiskResponse_INTERNAL         DetectLocationRiskResponse_Status = 13
)

// Enum value maps for DetectLocationRiskResponse_Status.
var (
	DetectLocationRiskResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		5:  "NOT_FOUND",
		13: "INTERNAL",
	}
	DetectLocationRiskResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"NOT_FOUND":        5,
		"INTERNAL":         13,
	}
)

func (x DetectLocationRiskResponse_Status) Enum() *DetectLocationRiskResponse_Status {
	p := new(DetectLocationRiskResponse_Status)
	*p = x
	return p
}

func (x DetectLocationRiskResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DetectLocationRiskResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_risk_service_proto_enumTypes[3].Descriptor()
}

func (DetectLocationRiskResponse_Status) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_risk_service_proto_enumTypes[3]
}

func (x DetectLocationRiskResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DetectLocationRiskResponse_Status.Descriptor instead.
func (DetectLocationRiskResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_risk_service_proto_rawDescGZIP(), []int{19, 0}
}

type GetCasePrioritisationScoreRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header               *vendorgateway.RequestHeader                   `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	AlertWithRuleDetails []*case_management.AlertWithRuleDetails        `protobuf:"bytes,2,rep,name=alert_with_rule_details,json=alertWithRuleDetails,proto3" json:"alert_with_rule_details,omitempty"`
	ActorId              string                                         `protobuf:"bytes,3,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	RequestId            string                                         `protobuf:"bytes,4,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	ModelVersion         string                                         `protobuf:"bytes,5,opt,name=model_version,json=modelVersion,proto3" json:"model_version,omitempty"`
	CaseDetails          *GetCasePrioritisationScoreRequest_CaseDetails `protobuf:"bytes,6,opt,name=case_details,json=caseDetails,proto3" json:"case_details,omitempty"`
}

func (x *GetCasePrioritisationScoreRequest) Reset() {
	*x = GetCasePrioritisationScoreRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_risk_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCasePrioritisationScoreRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCasePrioritisationScoreRequest) ProtoMessage() {}

func (x *GetCasePrioritisationScoreRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_risk_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCasePrioritisationScoreRequest.ProtoReflect.Descriptor instead.
func (*GetCasePrioritisationScoreRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_risk_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetCasePrioritisationScoreRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetCasePrioritisationScoreRequest) GetAlertWithRuleDetails() []*case_management.AlertWithRuleDetails {
	if x != nil {
		return x.AlertWithRuleDetails
	}
	return nil
}

func (x *GetCasePrioritisationScoreRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetCasePrioritisationScoreRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *GetCasePrioritisationScoreRequest) GetModelVersion() string {
	if x != nil {
		return x.ModelVersion
	}
	return ""
}

func (x *GetCasePrioritisationScoreRequest) GetCaseDetails() *GetCasePrioritisationScoreRequest_CaseDetails {
	if x != nil {
		return x.CaseDetails
	}
	return nil
}

type ModelResponseInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string  `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Score float32 `protobuf:"fixed32,2,opt,name=score,proto3" json:"score,omitempty"`
}

func (x *ModelResponseInfo) Reset() {
	*x = ModelResponseInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_risk_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelResponseInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelResponseInfo) ProtoMessage() {}

func (x *ModelResponseInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_risk_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelResponseInfo.ProtoReflect.Descriptor instead.
func (*ModelResponseInfo) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_risk_service_proto_rawDescGZIP(), []int{1}
}

func (x *ModelResponseInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ModelResponseInfo) GetScore() float32 {
	if x != nil {
		return x.Score
	}
	return 0
}

type GetCasePrioritisationScoreResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status    *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	RequestId string      `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// depricated, use ModelResponseInfo in place of model_version and score
	ModelVersion      string               `protobuf:"bytes,3,opt,name=model_version,json=modelVersion,proto3" json:"model_version,omitempty"`
	Score             float32              `protobuf:"fixed32,4,opt,name=score,proto3" json:"score,omitempty"`
	RawVendorResponse string               `protobuf:"bytes,5,opt,name=raw_vendor_response,json=rawVendorResponse,proto3" json:"raw_vendor_response,omitempty"`
	ModelInfo         []*ModelResponseInfo `protobuf:"bytes,6,rep,name=model_info,json=modelInfo,proto3" json:"model_info,omitempty"`
}

func (x *GetCasePrioritisationScoreResponse) Reset() {
	*x = GetCasePrioritisationScoreResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_risk_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCasePrioritisationScoreResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCasePrioritisationScoreResponse) ProtoMessage() {}

func (x *GetCasePrioritisationScoreResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_risk_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCasePrioritisationScoreResponse.ProtoReflect.Descriptor instead.
func (*GetCasePrioritisationScoreResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_risk_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetCasePrioritisationScoreResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetCasePrioritisationScoreResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *GetCasePrioritisationScoreResponse) GetModelVersion() string {
	if x != nil {
		return x.ModelVersion
	}
	return ""
}

func (x *GetCasePrioritisationScoreResponse) GetScore() float32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *GetCasePrioritisationScoreResponse) GetRawVendorResponse() string {
	if x != nil {
		return x.RawVendorResponse
	}
	return ""
}

func (x *GetCasePrioritisationScoreResponse) GetModelInfo() []*ModelResponseInfo {
	if x != nil {
		return x.ModelInfo
	}
	return nil
}

type GetBureauIdRiskDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header    *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	RequestId string                       `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	UserData  *UserData                    `protobuf:"bytes,3,opt,name=user_data,json=userData,proto3" json:"user_data,omitempty"`
}

func (x *GetBureauIdRiskDetailsRequest) Reset() {
	*x = GetBureauIdRiskDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_risk_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBureauIdRiskDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBureauIdRiskDetailsRequest) ProtoMessage() {}

func (x *GetBureauIdRiskDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_risk_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBureauIdRiskDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetBureauIdRiskDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_risk_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetBureauIdRiskDetailsRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetBureauIdRiskDetailsRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *GetBureauIdRiskDetailsRequest) GetUserData() *UserData {
	if x != nil {
		return x.UserData
	}
	return nil
}

type UserData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string              `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	EmailId     string              `protobuf:"bytes,2,opt,name=email_id,json=emailId,proto3" json:"email_id,omitempty"`
	PhoneNumber *common.PhoneNumber `protobuf:"bytes,3,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
}

func (x *UserData) Reset() {
	*x = UserData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_risk_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserData) ProtoMessage() {}

func (x *UserData) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_risk_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserData.ProtoReflect.Descriptor instead.
func (*UserData) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_risk_service_proto_rawDescGZIP(), []int{4}
}

func (x *UserData) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UserData) GetEmailId() string {
	if x != nil {
		return x.EmailId
	}
	return ""
}

func (x *UserData) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

type GetBureauIdRiskDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                 *rpc.Status             `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Outcome                Outcome                 `protobuf:"varint,2,opt,name=outcome,proto3,enum=vendorgateway.risk.Outcome" json:"outcome,omitempty"`
	RequestId              string                  `protobuf:"bytes,3,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	EmailAttributes        *EmailAttributes        `protobuf:"bytes,4,opt,name=email_attributes,json=emailAttributes,proto3" json:"email_attributes,omitempty"`
	EmailNameAttributes    *EmailNameAttributes    `protobuf:"bytes,5,opt,name=email_name_attributes,json=emailNameAttributes,proto3" json:"email_name_attributes,omitempty"`
	PhoneAttributes        *PhoneAttributes        `protobuf:"bytes,6,opt,name=phone_attributes,json=phoneAttributes,proto3" json:"phone_attributes,omitempty"`
	PhoneNameAttributes    *PhoneNameAttributes    `protobuf:"bytes,7,opt,name=phone_name_attributes,json=phoneNameAttributes,proto3" json:"phone_name_attributes,omitempty"`
	PhoneNetworkAttributes *PhoneNetworkAttributes `protobuf:"bytes,8,opt,name=phone_network_attributes,json=phoneNetworkAttributes,proto3" json:"phone_network_attributes,omitempty"`
	EmailSocialAttributes  *EmailSocialAttributes  `protobuf:"bytes,9,opt,name=email_social_attributes,json=emailSocialAttributes,proto3" json:"email_social_attributes,omitempty"`
	PhoneSocialAttributes  *PhoneSocialAttributes  `protobuf:"bytes,10,opt,name=phone_social_attributes,json=phoneSocialAttributes,proto3" json:"phone_social_attributes,omitempty"`
	RiskAttributes         *RiskAttributes         `protobuf:"bytes,11,opt,name=risk_attributes,json=riskAttributes,proto3" json:"risk_attributes,omitempty"`
	RawResponse            string                  `protobuf:"bytes,12,opt,name=raw_response,json=rawResponse,proto3" json:"raw_response,omitempty"`
}

func (x *GetBureauIdRiskDetailsResponse) Reset() {
	*x = GetBureauIdRiskDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_risk_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBureauIdRiskDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBureauIdRiskDetailsResponse) ProtoMessage() {}

func (x *GetBureauIdRiskDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_risk_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBureauIdRiskDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetBureauIdRiskDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_risk_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetBureauIdRiskDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetBureauIdRiskDetailsResponse) GetOutcome() Outcome {
	if x != nil {
		return x.Outcome
	}
	return Outcome_OUTCOME_UNSPECIFIED
}

func (x *GetBureauIdRiskDetailsResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *GetBureauIdRiskDetailsResponse) GetEmailAttributes() *EmailAttributes {
	if x != nil {
		return x.EmailAttributes
	}
	return nil
}

func (x *GetBureauIdRiskDetailsResponse) GetEmailNameAttributes() *EmailNameAttributes {
	if x != nil {
		return x.EmailNameAttributes
	}
	return nil
}

func (x *GetBureauIdRiskDetailsResponse) GetPhoneAttributes() *PhoneAttributes {
	if x != nil {
		return x.PhoneAttributes
	}
	return nil
}

func (x *GetBureauIdRiskDetailsResponse) GetPhoneNameAttributes() *PhoneNameAttributes {
	if x != nil {
		return x.PhoneNameAttributes
	}
	return nil
}

func (x *GetBureauIdRiskDetailsResponse) GetPhoneNetworkAttributes() *PhoneNetworkAttributes {
	if x != nil {
		return x.PhoneNetworkAttributes
	}
	return nil
}

func (x *GetBureauIdRiskDetailsResponse) GetEmailSocialAttributes() *EmailSocialAttributes {
	if x != nil {
		return x.EmailSocialAttributes
	}
	return nil
}

func (x *GetBureauIdRiskDetailsResponse) GetPhoneSocialAttributes() *PhoneSocialAttributes {
	if x != nil {
		return x.PhoneSocialAttributes
	}
	return nil
}

func (x *GetBureauIdRiskDetailsResponse) GetRiskAttributes() *RiskAttributes {
	if x != nil {
		return x.RiskAttributes
	}
	return nil
}

func (x *GetBureauIdRiskDetailsResponse) GetRawResponse() string {
	if x != nil {
		return x.RawResponse
	}
	return ""
}

type EmailAttributes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AttributeStatus          AttributeStatus `protobuf:"varint,1,opt,name=attribute_status,json=attributeStatus,proto3,enum=vendorgateway.risk.AttributeStatus" json:"attribute_status,omitempty"`
	EmailExists              bool            `protobuf:"varint,2,opt,name=email_exists,json=emailExists,proto3" json:"email_exists,omitempty"`
	DigitalAge               int32           `protobuf:"varint,3,opt,name=digital_age,json=digitalAge,proto3" json:"digital_age,omitempty"`
	DomainExists             bool            `protobuf:"varint,4,opt,name=domain_exists,json=domainExists,proto3" json:"domain_exists,omitempty"`
	DomainRiskLevel          string          `protobuf:"bytes,5,opt,name=domain_risk_level,json=domainRiskLevel,proto3" json:"domain_risk_level,omitempty"`
	DomainCategory           string          `protobuf:"bytes,6,opt,name=domain_category,json=domainCategory,proto3" json:"domain_category,omitempty"`
	DomainCorporate          string          `protobuf:"bytes,7,opt,name=domain_corporate,json=domainCorporate,proto3" json:"domain_corporate,omitempty"`
	UniqueHits               int32           `protobuf:"varint,8,opt,name=unique_hits,json=uniqueHits,proto3" json:"unique_hits,omitempty"`
	EmailFinalRecommendation string          `protobuf:"bytes,9,opt,name=email_final_recommendation,json=emailFinalRecommendation,proto3" json:"email_final_recommendation,omitempty"`
	LastVerificationDate     string          `protobuf:"bytes,10,opt,name=last_verification_date,json=lastVerificationDate,proto3" json:"last_verification_date,omitempty"`
	FirstVerificationDate    string          `protobuf:"bytes,11,opt,name=first_verification_date,json=firstVerificationDate,proto3" json:"first_verification_date,omitempty"`
}

func (x *EmailAttributes) Reset() {
	*x = EmailAttributes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_risk_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmailAttributes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmailAttributes) ProtoMessage() {}

func (x *EmailAttributes) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_risk_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmailAttributes.ProtoReflect.Descriptor instead.
func (*EmailAttributes) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_risk_service_proto_rawDescGZIP(), []int{6}
}

func (x *EmailAttributes) GetAttributeStatus() AttributeStatus {
	if x != nil {
		return x.AttributeStatus
	}
	return AttributeStatus_ATTRIBUTE_STATUS_OK
}

func (x *EmailAttributes) GetEmailExists() bool {
	if x != nil {
		return x.EmailExists
	}
	return false
}

func (x *EmailAttributes) GetDigitalAge() int32 {
	if x != nil {
		return x.DigitalAge
	}
	return 0
}

func (x *EmailAttributes) GetDomainExists() bool {
	if x != nil {
		return x.DomainExists
	}
	return false
}

func (x *EmailAttributes) GetDomainRiskLevel() string {
	if x != nil {
		return x.DomainRiskLevel
	}
	return ""
}

func (x *EmailAttributes) GetDomainCategory() string {
	if x != nil {
		return x.DomainCategory
	}
	return ""
}

func (x *EmailAttributes) GetDomainCorporate() string {
	if x != nil {
		return x.DomainCorporate
	}
	return ""
}

func (x *EmailAttributes) GetUniqueHits() int32 {
	if x != nil {
		return x.UniqueHits
	}
	return 0
}

func (x *EmailAttributes) GetEmailFinalRecommendation() string {
	if x != nil {
		return x.EmailFinalRecommendation
	}
	return ""
}

func (x *EmailAttributes) GetLastVerificationDate() string {
	if x != nil {
		return x.LastVerificationDate
	}
	return ""
}

func (x *EmailAttributes) GetFirstVerificationDate() string {
	if x != nil {
		return x.FirstVerificationDate
	}
	return ""
}

type EmailNameAttributes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AttributeStatus        AttributeStatus `protobuf:"varint,1,opt,name=attribute_status,json=attributeStatus,proto3,enum=vendorgateway.risk.AttributeStatus" json:"attribute_status,omitempty"`
	Address                string          `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	DigitalAge             int32           `protobuf:"varint,3,opt,name=digital_age,json=digitalAge,proto3" json:"digital_age,omitempty"`
	PhoneNumber            int64           `protobuf:"varint,4,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	FirstNameMatch         bool            `protobuf:"varint,5,opt,name=first_name_match,json=firstNameMatch,proto3" json:"first_name_match,omitempty"`
	LastNameMatch          bool            `protobuf:"varint,6,opt,name=last_name_match,json=lastNameMatch,proto3" json:"last_name_match,omitempty"`
	BusinessNameDetected   bool            `protobuf:"varint,7,opt,name=business_name_detected,json=businessNameDetected,proto3" json:"business_name_detected,omitempty"`
	EmailFootprintStrength string          `protobuf:"bytes,8,opt,name=email_footprint_strength,json=emailFootprintStrength,proto3" json:"email_footprint_strength,omitempty"`
	EmailNameDigitalAge    int32           `protobuf:"varint,9,opt,name=email_name_digital_age,json=emailNameDigitalAge,proto3" json:"email_name_digital_age,omitempty"`
	EmailSpclChars         int32           `protobuf:"varint,10,opt,name=email_spcl_chars,json=emailSpclChars,proto3" json:"email_spcl_chars,omitempty"`
	// the maximum similarity score between names extracted from an email address and names provided in the request.
	NdrScore float64 `protobuf:"fixed64,11,opt,name=ndr_score,json=ndrScore,proto3" json:"ndr_score,omitempty"`
	// the ratio of number of names that were unmatched to the total number of names found in the request.
	UnrScore float64 `protobuf:"fixed64,12,opt,name=unr_score,json=unrScore,proto3" json:"unr_score,omitempty"`
	// represents at which extent the name matches with email
	NameMatchScore        float64 `protobuf:"fixed64,13,opt,name=name_match_score,json=nameMatchScore,proto3" json:"name_match_score,omitempty"`
	NameEmailMatch        int32   `protobuf:"varint,14,opt,name=name_email_match,json=nameEmailMatch,proto3" json:"name_email_match,omitempty"`
	MultiplePhoneAttached bool    `protobuf:"varint,15,opt,name=multiple_phone_attached,json=multiplePhoneAttached,proto3" json:"multiple_phone_attached,omitempty"`
}

func (x *EmailNameAttributes) Reset() {
	*x = EmailNameAttributes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_risk_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmailNameAttributes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmailNameAttributes) ProtoMessage() {}

func (x *EmailNameAttributes) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_risk_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmailNameAttributes.ProtoReflect.Descriptor instead.
func (*EmailNameAttributes) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_risk_service_proto_rawDescGZIP(), []int{7}
}

func (x *EmailNameAttributes) GetAttributeStatus() AttributeStatus {
	if x != nil {
		return x.AttributeStatus
	}
	return AttributeStatus_ATTRIBUTE_STATUS_OK
}

func (x *EmailNameAttributes) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *EmailNameAttributes) GetDigitalAge() int32 {
	if x != nil {
		return x.DigitalAge
	}
	return 0
}

func (x *EmailNameAttributes) GetPhoneNumber() int64 {
	if x != nil {
		return x.PhoneNumber
	}
	return 0
}

func (x *EmailNameAttributes) GetFirstNameMatch() bool {
	if x != nil {
		return x.FirstNameMatch
	}
	return false
}

func (x *EmailNameAttributes) GetLastNameMatch() bool {
	if x != nil {
		return x.LastNameMatch
	}
	return false
}

func (x *EmailNameAttributes) GetBusinessNameDetected() bool {
	if x != nil {
		return x.BusinessNameDetected
	}
	return false
}

func (x *EmailNameAttributes) GetEmailFootprintStrength() string {
	if x != nil {
		return x.EmailFootprintStrength
	}
	return ""
}

func (x *EmailNameAttributes) GetEmailNameDigitalAge() int32 {
	if x != nil {
		return x.EmailNameDigitalAge
	}
	return 0
}

func (x *EmailNameAttributes) GetEmailSpclChars() int32 {
	if x != nil {
		return x.EmailSpclChars
	}
	return 0
}

func (x *EmailNameAttributes) GetNdrScore() float64 {
	if x != nil {
		return x.NdrScore
	}
	return 0
}

func (x *EmailNameAttributes) GetUnrScore() float64 {
	if x != nil {
		return x.UnrScore
	}
	return 0
}

func (x *EmailNameAttributes) GetNameMatchScore() float64 {
	if x != nil {
		return x.NameMatchScore
	}
	return 0
}

func (x *EmailNameAttributes) GetNameEmailMatch() int32 {
	if x != nil {
		return x.NameEmailMatch
	}
	return 0
}

func (x *EmailNameAttributes) GetMultiplePhoneAttached() bool {
	if x != nil {
		return x.MultiplePhoneAttached
	}
	return false
}

type PhoneAttributes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AttributeStatus AttributeStatus `protobuf:"varint,1,opt,name=attribute_status,json=attributeStatus,proto3,enum=vendorgateway.risk.AttributeStatus" json:"attribute_status,omitempty"`
	Name            string          `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Source          string          `protobuf:"bytes,3,opt,name=source,proto3" json:"source,omitempty"`
	Vpa             string          `protobuf:"bytes,4,opt,name=vpa,proto3" json:"vpa,omitempty"`
}

func (x *PhoneAttributes) Reset() {
	*x = PhoneAttributes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_risk_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PhoneAttributes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PhoneAttributes) ProtoMessage() {}

func (x *PhoneAttributes) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_risk_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PhoneAttributes.ProtoReflect.Descriptor instead.
func (*PhoneAttributes) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_risk_service_proto_rawDescGZIP(), []int{8}
}

func (x *PhoneAttributes) GetAttributeStatus() AttributeStatus {
	if x != nil {
		return x.AttributeStatus
	}
	return AttributeStatus_ATTRIBUTE_STATUS_OK
}

func (x *PhoneAttributes) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PhoneAttributes) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *PhoneAttributes) GetVpa() string {
	if x != nil {
		return x.Vpa
	}
	return ""
}

type PhoneNameAttributes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AttributeStatus          AttributeStatus `protobuf:"varint,1,opt,name=attribute_status,json=attributeStatus,proto3,enum=vendorgateway.risk.AttributeStatus" json:"attribute_status,omitempty"`
	Address                  string          `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	DigitalAge               int32           `protobuf:"varint,3,opt,name=digital_age,json=digitalAge,proto3" json:"digital_age,omitempty"`
	FirstNameMatch           bool            `protobuf:"varint,4,opt,name=first_name_match,json=firstNameMatch,proto3" json:"first_name_match,omitempty"`
	LastNameMatch            bool            `protobuf:"varint,5,opt,name=last_name_match,json=lastNameMatch,proto3" json:"last_name_match,omitempty"`
	BusinessNameDetected     bool            `protobuf:"varint,6,opt,name=business_name_detected,json=businessNameDetected,proto3" json:"business_name_detected,omitempty"`
	NdrScore                 float64         `protobuf:"fixed64,7,opt,name=ndr_score,json=ndrScore,proto3" json:"ndr_score,omitempty"`
	UnrScore                 float64         `protobuf:"fixed64,8,opt,name=unr_score,json=unrScore,proto3" json:"unr_score,omitempty"`
	NameMatchScore           float64         `protobuf:"fixed64,9,opt,name=name_match_score,json=nameMatchScore,proto3" json:"name_match_score,omitempty"`
	FootprintStrengthOverall string          `protobuf:"bytes,10,opt,name=footprint_strength_overall,json=footprintStrengthOverall,proto3" json:"footprint_strength_overall,omitempty"`
	PhoneNameDigitalAge      int32           `protobuf:"varint,11,opt,name=phone_name_digital_age,json=phoneNameDigitalAge,proto3" json:"phone_name_digital_age,omitempty"`
}

func (x *PhoneNameAttributes) Reset() {
	*x = PhoneNameAttributes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_risk_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PhoneNameAttributes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PhoneNameAttributes) ProtoMessage() {}

func (x *PhoneNameAttributes) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_risk_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PhoneNameAttributes.ProtoReflect.Descriptor instead.
func (*PhoneNameAttributes) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_risk_service_proto_rawDescGZIP(), []int{9}
}

func (x *PhoneNameAttributes) GetAttributeStatus() AttributeStatus {
	if x != nil {
		return x.AttributeStatus
	}
	return AttributeStatus_ATTRIBUTE_STATUS_OK
}

func (x *PhoneNameAttributes) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *PhoneNameAttributes) GetDigitalAge() int32 {
	if x != nil {
		return x.DigitalAge
	}
	return 0
}

func (x *PhoneNameAttributes) GetFirstNameMatch() bool {
	if x != nil {
		return x.FirstNameMatch
	}
	return false
}

func (x *PhoneNameAttributes) GetLastNameMatch() bool {
	if x != nil {
		return x.LastNameMatch
	}
	return false
}

func (x *PhoneNameAttributes) GetBusinessNameDetected() bool {
	if x != nil {
		return x.BusinessNameDetected
	}
	return false
}

func (x *PhoneNameAttributes) GetNdrScore() float64 {
	if x != nil {
		return x.NdrScore
	}
	return 0
}

func (x *PhoneNameAttributes) GetUnrScore() float64 {
	if x != nil {
		return x.UnrScore
	}
	return 0
}

func (x *PhoneNameAttributes) GetNameMatchScore() float64 {
	if x != nil {
		return x.NameMatchScore
	}
	return 0
}

func (x *PhoneNameAttributes) GetFootprintStrengthOverall() string {
	if x != nil {
		return x.FootprintStrengthOverall
	}
	return ""
}

func (x *PhoneNameAttributes) GetPhoneNameDigitalAge() int32 {
	if x != nil {
		return x.PhoneNameDigitalAge
	}
	return 0
}

type PhoneNetworkAttributes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AttributeStatus                  AttributeStatus `protobuf:"varint,1,opt,name=attribute_status,json=attributeStatus,proto3,enum=vendorgateway.risk.AttributeStatus" json:"attribute_status,omitempty"`
	Imsi                             string          `protobuf:"bytes,2,opt,name=imsi,proto3" json:"imsi,omitempty"`
	CurrentNetworkName               string          `protobuf:"bytes,3,opt,name=current_network_name,json=currentNetworkName,proto3" json:"current_network_name,omitempty"`
	CurrentNetworkRegion             string          `protobuf:"bytes,4,opt,name=current_network_region,json=currentNetworkRegion,proto3" json:"current_network_region,omitempty"`
	CurrentNetworkCountryCodeIso2    string          `protobuf:"bytes,5,opt,name=current_network_country_code_iso2,json=currentNetworkCountryCodeIso2,proto3" json:"current_network_country_code_iso2,omitempty"`
	IsPhoneReachable                 bool            `protobuf:"varint,6,opt,name=is_phone_reachable,json=isPhoneReachable,proto3" json:"is_phone_reachable,omitempty"`
	IsValidPhoneNumber               bool            `protobuf:"varint,7,opt,name=is_valid_phone_number,json=isValidPhoneNumber,proto3" json:"is_valid_phone_number,omitempty"`
	NumberBillingType                string          `protobuf:"bytes,8,opt,name=number_billing_type,json=numberBillingType,proto3" json:"number_billing_type,omitempty"`
	NumberHasPortingHistory          bool            `protobuf:"varint,9,opt,name=number_has_porting_history,json=numberHasPortingHistory,proto3" json:"number_has_porting_history,omitempty"`
	PortedFromNetworkName            string          `protobuf:"bytes,10,opt,name=ported_from_network_name,json=portedFromNetworkName,proto3" json:"ported_from_network_name,omitempty"`
	PortedFromNetworkRegion          string          `protobuf:"bytes,11,opt,name=ported_from_network_region,json=portedFromNetworkRegion,proto3" json:"ported_from_network_region,omitempty"`
	PortedFromNetworkCountryCodeIso2 string          `protobuf:"bytes,12,opt,name=ported_from_network_country_code_iso2,json=portedFromNetworkCountryCodeIso2,proto3" json:"ported_from_network_country_code_iso2,omitempty"`
	Roaming                          bool            `protobuf:"varint,13,opt,name=roaming,proto3" json:"roaming,omitempty"`
	RoamingNetworkName               string          `protobuf:"bytes,14,opt,name=roaming_network_name,json=roamingNetworkName,proto3" json:"roaming_network_name,omitempty"`
	RoamingNetworkRegion             string          `protobuf:"bytes,15,opt,name=roaming_network_region,json=roamingNetworkRegion,proto3" json:"roaming_network_region,omitempty"`
	RoamingNetworkCountryCodeIso2    string          `protobuf:"bytes,16,opt,name=roaming_network_country_code_iso2,json=roamingNetworkCountryCodeIso2,proto3" json:"roaming_network_country_code_iso2,omitempty"`
}

func (x *PhoneNetworkAttributes) Reset() {
	*x = PhoneNetworkAttributes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_risk_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PhoneNetworkAttributes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PhoneNetworkAttributes) ProtoMessage() {}

func (x *PhoneNetworkAttributes) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_risk_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PhoneNetworkAttributes.ProtoReflect.Descriptor instead.
func (*PhoneNetworkAttributes) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_risk_service_proto_rawDescGZIP(), []int{10}
}

func (x *PhoneNetworkAttributes) GetAttributeStatus() AttributeStatus {
	if x != nil {
		return x.AttributeStatus
	}
	return AttributeStatus_ATTRIBUTE_STATUS_OK
}

func (x *PhoneNetworkAttributes) GetImsi() string {
	if x != nil {
		return x.Imsi
	}
	return ""
}

func (x *PhoneNetworkAttributes) GetCurrentNetworkName() string {
	if x != nil {
		return x.CurrentNetworkName
	}
	return ""
}

func (x *PhoneNetworkAttributes) GetCurrentNetworkRegion() string {
	if x != nil {
		return x.CurrentNetworkRegion
	}
	return ""
}

func (x *PhoneNetworkAttributes) GetCurrentNetworkCountryCodeIso2() string {
	if x != nil {
		return x.CurrentNetworkCountryCodeIso2
	}
	return ""
}

func (x *PhoneNetworkAttributes) GetIsPhoneReachable() bool {
	if x != nil {
		return x.IsPhoneReachable
	}
	return false
}

func (x *PhoneNetworkAttributes) GetIsValidPhoneNumber() bool {
	if x != nil {
		return x.IsValidPhoneNumber
	}
	return false
}

func (x *PhoneNetworkAttributes) GetNumberBillingType() string {
	if x != nil {
		return x.NumberBillingType
	}
	return ""
}

func (x *PhoneNetworkAttributes) GetNumberHasPortingHistory() bool {
	if x != nil {
		return x.NumberHasPortingHistory
	}
	return false
}

func (x *PhoneNetworkAttributes) GetPortedFromNetworkName() string {
	if x != nil {
		return x.PortedFromNetworkName
	}
	return ""
}

func (x *PhoneNetworkAttributes) GetPortedFromNetworkRegion() string {
	if x != nil {
		return x.PortedFromNetworkRegion
	}
	return ""
}

func (x *PhoneNetworkAttributes) GetPortedFromNetworkCountryCodeIso2() string {
	if x != nil {
		return x.PortedFromNetworkCountryCodeIso2
	}
	return ""
}

func (x *PhoneNetworkAttributes) GetRoaming() bool {
	if x != nil {
		return x.Roaming
	}
	return false
}

func (x *PhoneNetworkAttributes) GetRoamingNetworkName() string {
	if x != nil {
		return x.RoamingNetworkName
	}
	return ""
}

func (x *PhoneNetworkAttributes) GetRoamingNetworkRegion() string {
	if x != nil {
		return x.RoamingNetworkRegion
	}
	return ""
}

func (x *PhoneNetworkAttributes) GetRoamingNetworkCountryCodeIso2() string {
	if x != nil {
		return x.RoamingNetworkCountryCodeIso2
	}
	return ""
}

type PhoneSocialAttributes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AttributeStatus AttributeStatus `protobuf:"varint,1,opt,name=attribute_status,json=attributeStatus,proto3,enum=vendorgateway.risk.AttributeStatus" json:"attribute_status,omitempty"`
	Amazon          string          `protobuf:"bytes,2,opt,name=amazon,proto3" json:"amazon,omitempty"`
	Flipkart        string          `protobuf:"bytes,5,opt,name=flipkart,proto3" json:"flipkart,omitempty"`
	Housing         string          `protobuf:"bytes,6,opt,name=housing,proto3" json:"housing,omitempty"`
	Indiamart       string          `protobuf:"bytes,7,opt,name=indiamart,proto3" json:"indiamart,omitempty"`
	Instagram       string          `protobuf:"bytes,8,opt,name=instagram,proto3" json:"instagram,omitempty"`
	IsWABusiness    string          `protobuf:"bytes,9,opt,name=isWABusiness,proto3" json:"isWABusiness,omitempty"`
	Jeevansaathi    string          `protobuf:"bytes,10,opt,name=jeevansaathi,proto3" json:"jeevansaathi,omitempty"`
	Jiomart         string          `protobuf:"bytes,11,opt,name=jiomart,proto3" json:"jiomart,omitempty"`
	Microsoft       string          `protobuf:"bytes,12,opt,name=microsoft,proto3" json:"microsoft,omitempty"`
	Mobile          string          `protobuf:"bytes,13,opt,name=mobile,proto3" json:"mobile,omitempty"`
	Paytm           string          `protobuf:"bytes,14,opt,name=paytm,proto3" json:"paytm,omitempty"`
	Shaadi          string          `protobuf:"bytes,15,opt,name=shaadi,proto3" json:"shaadi,omitempty"`
	Skype           string          `protobuf:"bytes,16,opt,name=skype,proto3" json:"skype,omitempty"`
	Swiggy          string          `protobuf:"bytes,19,opt,name=swiggy,proto3" json:"swiggy,omitempty"`
	Toi             string          `protobuf:"bytes,20,opt,name=toi,proto3" json:"toi,omitempty"`
	Whatsapp        string          `protobuf:"bytes,21,opt,name=whatsapp,proto3" json:"whatsapp,omitempty"`
	Yatra           string          `protobuf:"bytes,22,opt,name=yatra,proto3" json:"yatra,omitempty"`
	Zoho            string          `protobuf:"bytes,23,opt,name=zoho,proto3" json:"zoho,omitempty"`
}

func (x *PhoneSocialAttributes) Reset() {
	*x = PhoneSocialAttributes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_risk_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PhoneSocialAttributes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PhoneSocialAttributes) ProtoMessage() {}

func (x *PhoneSocialAttributes) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_risk_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PhoneSocialAttributes.ProtoReflect.Descriptor instead.
func (*PhoneSocialAttributes) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_risk_service_proto_rawDescGZIP(), []int{11}
}

func (x *PhoneSocialAttributes) GetAttributeStatus() AttributeStatus {
	if x != nil {
		return x.AttributeStatus
	}
	return AttributeStatus_ATTRIBUTE_STATUS_OK
}

func (x *PhoneSocialAttributes) GetAmazon() string {
	if x != nil {
		return x.Amazon
	}
	return ""
}

func (x *PhoneSocialAttributes) GetFlipkart() string {
	if x != nil {
		return x.Flipkart
	}
	return ""
}

func (x *PhoneSocialAttributes) GetHousing() string {
	if x != nil {
		return x.Housing
	}
	return ""
}

func (x *PhoneSocialAttributes) GetIndiamart() string {
	if x != nil {
		return x.Indiamart
	}
	return ""
}

func (x *PhoneSocialAttributes) GetInstagram() string {
	if x != nil {
		return x.Instagram
	}
	return ""
}

func (x *PhoneSocialAttributes) GetIsWABusiness() string {
	if x != nil {
		return x.IsWABusiness
	}
	return ""
}

func (x *PhoneSocialAttributes) GetJeevansaathi() string {
	if x != nil {
		return x.Jeevansaathi
	}
	return ""
}

func (x *PhoneSocialAttributes) GetJiomart() string {
	if x != nil {
		return x.Jiomart
	}
	return ""
}

func (x *PhoneSocialAttributes) GetMicrosoft() string {
	if x != nil {
		return x.Microsoft
	}
	return ""
}

func (x *PhoneSocialAttributes) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *PhoneSocialAttributes) GetPaytm() string {
	if x != nil {
		return x.Paytm
	}
	return ""
}

func (x *PhoneSocialAttributes) GetShaadi() string {
	if x != nil {
		return x.Shaadi
	}
	return ""
}

func (x *PhoneSocialAttributes) GetSkype() string {
	if x != nil {
		return x.Skype
	}
	return ""
}

func (x *PhoneSocialAttributes) GetSwiggy() string {
	if x != nil {
		return x.Swiggy
	}
	return ""
}

func (x *PhoneSocialAttributes) GetToi() string {
	if x != nil {
		return x.Toi
	}
	return ""
}

func (x *PhoneSocialAttributes) GetWhatsapp() string {
	if x != nil {
		return x.Whatsapp
	}
	return ""
}

func (x *PhoneSocialAttributes) GetYatra() string {
	if x != nil {
		return x.Yatra
	}
	return ""
}

func (x *PhoneSocialAttributes) GetZoho() string {
	if x != nil {
		return x.Zoho
	}
	return ""
}

type EmailSocialAttributes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AttributeStatus AttributeStatus `protobuf:"varint,1,opt,name=attribute_status,json=attributeStatus,proto3,enum=vendorgateway.risk.AttributeStatus" json:"attribute_status,omitempty"`
	Amazon          string          `protobuf:"bytes,2,opt,name=amazon,proto3" json:"amazon,omitempty"`
	Booking         string          `protobuf:"bytes,3,opt,name=booking,proto3" json:"booking,omitempty"`
	Email           string          `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty"`
	Flickr          string          `protobuf:"bytes,5,opt,name=flickr,proto3" json:"flickr,omitempty"`
	Flipkart        string          `protobuf:"bytes,6,opt,name=flipkart,proto3" json:"flipkart,omitempty"`
	Housing         string          `protobuf:"bytes,7,opt,name=housing,proto3" json:"housing,omitempty"`
	Instagram       string          `protobuf:"bytes,8,opt,name=instagram,proto3" json:"instagram,omitempty"`
	Jeevansaathi    string          `protobuf:"bytes,9,opt,name=jeevansaathi,proto3" json:"jeevansaathi,omitempty"`
	Microsoft       string          `protobuf:"bytes,10,opt,name=microsoft,proto3" json:"microsoft,omitempty"`
	Paytm           string          `protobuf:"bytes,11,opt,name=paytm,proto3" json:"paytm,omitempty"`
	Pinterest       string          `protobuf:"bytes,12,opt,name=pinterest,proto3" json:"pinterest,omitempty"`
	Quora           string          `protobuf:"bytes,13,opt,name=quora,proto3" json:"quora,omitempty"`
	Shaadi          string          `protobuf:"bytes,14,opt,name=shaadi,proto3" json:"shaadi,omitempty"`
	Skype           string          `protobuf:"bytes,15,opt,name=skype,proto3" json:"skype,omitempty"`
	Spotify         string          `protobuf:"bytes,16,opt,name=spotify,proto3" json:"spotify,omitempty"`
	Toi             string          `protobuf:"bytes,17,opt,name=toi,proto3" json:"toi,omitempty"`
	Wordpress       string          `protobuf:"bytes,18,opt,name=wordpress,proto3" json:"wordpress,omitempty"`
	Yatra           string          `protobuf:"bytes,19,opt,name=yatra,proto3" json:"yatra,omitempty"`
	Zoho            string          `protobuf:"bytes,20,opt,name=zoho,proto3" json:"zoho,omitempty"`
}

func (x *EmailSocialAttributes) Reset() {
	*x = EmailSocialAttributes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_risk_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmailSocialAttributes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmailSocialAttributes) ProtoMessage() {}

func (x *EmailSocialAttributes) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_risk_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmailSocialAttributes.ProtoReflect.Descriptor instead.
func (*EmailSocialAttributes) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_risk_service_proto_rawDescGZIP(), []int{12}
}

func (x *EmailSocialAttributes) GetAttributeStatus() AttributeStatus {
	if x != nil {
		return x.AttributeStatus
	}
	return AttributeStatus_ATTRIBUTE_STATUS_OK
}

func (x *EmailSocialAttributes) GetAmazon() string {
	if x != nil {
		return x.Amazon
	}
	return ""
}

func (x *EmailSocialAttributes) GetBooking() string {
	if x != nil {
		return x.Booking
	}
	return ""
}

func (x *EmailSocialAttributes) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *EmailSocialAttributes) GetFlickr() string {
	if x != nil {
		return x.Flickr
	}
	return ""
}

func (x *EmailSocialAttributes) GetFlipkart() string {
	if x != nil {
		return x.Flipkart
	}
	return ""
}

func (x *EmailSocialAttributes) GetHousing() string {
	if x != nil {
		return x.Housing
	}
	return ""
}

func (x *EmailSocialAttributes) GetInstagram() string {
	if x != nil {
		return x.Instagram
	}
	return ""
}

func (x *EmailSocialAttributes) GetJeevansaathi() string {
	if x != nil {
		return x.Jeevansaathi
	}
	return ""
}

func (x *EmailSocialAttributes) GetMicrosoft() string {
	if x != nil {
		return x.Microsoft
	}
	return ""
}

func (x *EmailSocialAttributes) GetPaytm() string {
	if x != nil {
		return x.Paytm
	}
	return ""
}

func (x *EmailSocialAttributes) GetPinterest() string {
	if x != nil {
		return x.Pinterest
	}
	return ""
}

func (x *EmailSocialAttributes) GetQuora() string {
	if x != nil {
		return x.Quora
	}
	return ""
}

func (x *EmailSocialAttributes) GetShaadi() string {
	if x != nil {
		return x.Shaadi
	}
	return ""
}

func (x *EmailSocialAttributes) GetSkype() string {
	if x != nil {
		return x.Skype
	}
	return ""
}

func (x *EmailSocialAttributes) GetSpotify() string {
	if x != nil {
		return x.Spotify
	}
	return ""
}

func (x *EmailSocialAttributes) GetToi() string {
	if x != nil {
		return x.Toi
	}
	return ""
}

func (x *EmailSocialAttributes) GetWordpress() string {
	if x != nil {
		return x.Wordpress
	}
	return ""
}

func (x *EmailSocialAttributes) GetYatra() string {
	if x != nil {
		return x.Yatra
	}
	return ""
}

func (x *EmailSocialAttributes) GetZoho() string {
	if x != nil {
		return x.Zoho
	}
	return ""
}

type RiskAttributes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AttributeStatus          AttributeStatus `protobuf:"varint,1,opt,name=attribute_status,json=attributeStatus,proto3,enum=vendorgateway.risk.AttributeStatus" json:"attribute_status,omitempty"`
	Address1                 string          `protobuf:"bytes,2,opt,name=address1,proto3" json:"address1,omitempty"`
	Address2                 string          `protobuf:"bytes,3,opt,name=address2,proto3" json:"address2,omitempty"`
	Landmark                 string          `protobuf:"bytes,4,opt,name=landmark,proto3" json:"landmark,omitempty"`
	AreaName                 string          `protobuf:"bytes,5,opt,name=area_name,json=areaName,proto3" json:"area_name,omitempty"`
	AreaPincode              string          `protobuf:"bytes,6,opt,name=area_pincode,json=areaPincode,proto3" json:"area_pincode,omitempty"`
	SocietyName              string          `protobuf:"bytes,7,opt,name=society_name,json=societyName,proto3" json:"society_name,omitempty"`
	StreetName               string          `protobuf:"bytes,8,opt,name=street_name,json=streetName,proto3" json:"street_name,omitempty"`
	CityName                 string          `protobuf:"bytes,9,opt,name=city_name,json=cityName,proto3" json:"city_name,omitempty"`
	StateName                string          `protobuf:"bytes,10,opt,name=state_name,json=stateName,proto3" json:"state_name,omitempty"`
	AddressInsights          string          `protobuf:"bytes,11,opt,name=address_insights,json=addressInsights,proto3" json:"address_insights,omitempty"`
	AddressRisk              string          `protobuf:"bytes,12,opt,name=address_risk,json=addressRisk,proto3" json:"address_risk,omitempty"`
	AddressCompletenessScore float32         `protobuf:"fixed32,13,opt,name=address_completeness_score,json=addressCompletenessScore,proto3" json:"address_completeness_score,omitempty"`
	CommonEmailCount         int32           `protobuf:"varint,14,opt,name=common_email_count,json=commonEmailCount,proto3" json:"common_email_count,omitempty"`
	EmailFraud               bool            `protobuf:"varint,15,opt,name=email_fraud,json=emailFraud,proto3" json:"email_fraud,omitempty"`
	EmailFraudCount          int32           `protobuf:"varint,16,opt,name=email_fraud_count,json=emailFraudCount,proto3" json:"email_fraud_count,omitempty"`
	EmailFraudNetwork        bool            `protobuf:"varint,17,opt,name=email_fraud_network,json=emailFraudNetwork,proto3" json:"email_fraud_network,omitempty"`
	EmailIdentityTrust       string          `protobuf:"bytes,18,opt,name=email_identity_trust,json=emailIdentityTrust,proto3" json:"email_identity_trust,omitempty"`
	EmailSocialMediaCount    int32           `protobuf:"varint,19,opt,name=email_social_media_count,json=emailSocialMediaCount,proto3" json:"email_social_media_count,omitempty"`
	IdentityConfidence       string          `protobuf:"bytes,20,opt,name=identity_confidence,json=identityConfidence,proto3" json:"identity_confidence,omitempty"`
	TelecomRisk              string          `protobuf:"bytes,21,opt,name=telecom_risk,json=telecomRisk,proto3" json:"telecom_risk,omitempty"`
	IsPhoneReachable         bool            `protobuf:"varint,22,opt,name=is_phone_reachable,json=isPhoneReachable,proto3" json:"is_phone_reachable,omitempty"`
	PhoneFraud               bool            `protobuf:"varint,23,opt,name=phone_fraud,json=phoneFraud,proto3" json:"phone_fraud,omitempty"`
	PhoneFraudCount          int32           `protobuf:"varint,24,opt,name=phone_fraud_count,json=phoneFraudCount,proto3" json:"phone_fraud_count,omitempty"`
	PhoneFraudNetwork        bool            `protobuf:"varint,25,opt,name=phone_fraud_network,json=phoneFraudNetwork,proto3" json:"phone_fraud_network,omitempty"`
	PhoneIdentityTrust       string          `protobuf:"bytes,26,opt,name=phone_identity_trust,json=phoneIdentityTrust,proto3" json:"phone_identity_trust,omitempty"`
	PhoneSocialMediaCount    int32           `protobuf:"varint,27,opt,name=phone_social_media_count,json=phoneSocialMediaCount,proto3" json:"phone_social_media_count,omitempty"`
	NegativeInsights         []string        `protobuf:"bytes,28,rep,name=negative_insights,json=negativeInsights,proto3" json:"negative_insights,omitempty"`
	PositiveInsights         []string        `protobuf:"bytes,29,rep,name=positive_insights,json=positiveInsights,proto3" json:"positive_insights,omitempty"`
	UpiPhoneNameMatch        int32           `protobuf:"varint,30,opt,name=upi_phone_name_match,json=upiPhoneNameMatch,proto3" json:"upi_phone_name_match,omitempty"`
	UpiPhoneNameMatchScore   float64         `protobuf:"fixed64,31,opt,name=upi_phone_name_match_score,json=upiPhoneNameMatchScore,proto3" json:"upi_phone_name_match_score,omitempty"`
	AlternateRiskScore       float64         `protobuf:"fixed64,32,opt,name=alternate_risk_score,json=alternateRiskScore,proto3" json:"alternate_risk_score,omitempty"`
}

func (x *RiskAttributes) Reset() {
	*x = RiskAttributes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_risk_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskAttributes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskAttributes) ProtoMessage() {}

func (x *RiskAttributes) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_risk_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskAttributes.ProtoReflect.Descriptor instead.
func (*RiskAttributes) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_risk_service_proto_rawDescGZIP(), []int{13}
}

func (x *RiskAttributes) GetAttributeStatus() AttributeStatus {
	if x != nil {
		return x.AttributeStatus
	}
	return AttributeStatus_ATTRIBUTE_STATUS_OK
}

func (x *RiskAttributes) GetAddress1() string {
	if x != nil {
		return x.Address1
	}
	return ""
}

func (x *RiskAttributes) GetAddress2() string {
	if x != nil {
		return x.Address2
	}
	return ""
}

func (x *RiskAttributes) GetLandmark() string {
	if x != nil {
		return x.Landmark
	}
	return ""
}

func (x *RiskAttributes) GetAreaName() string {
	if x != nil {
		return x.AreaName
	}
	return ""
}

func (x *RiskAttributes) GetAreaPincode() string {
	if x != nil {
		return x.AreaPincode
	}
	return ""
}

func (x *RiskAttributes) GetSocietyName() string {
	if x != nil {
		return x.SocietyName
	}
	return ""
}

func (x *RiskAttributes) GetStreetName() string {
	if x != nil {
		return x.StreetName
	}
	return ""
}

func (x *RiskAttributes) GetCityName() string {
	if x != nil {
		return x.CityName
	}
	return ""
}

func (x *RiskAttributes) GetStateName() string {
	if x != nil {
		return x.StateName
	}
	return ""
}

func (x *RiskAttributes) GetAddressInsights() string {
	if x != nil {
		return x.AddressInsights
	}
	return ""
}

func (x *RiskAttributes) GetAddressRisk() string {
	if x != nil {
		return x.AddressRisk
	}
	return ""
}

func (x *RiskAttributes) GetAddressCompletenessScore() float32 {
	if x != nil {
		return x.AddressCompletenessScore
	}
	return 0
}

func (x *RiskAttributes) GetCommonEmailCount() int32 {
	if x != nil {
		return x.CommonEmailCount
	}
	return 0
}

func (x *RiskAttributes) GetEmailFraud() bool {
	if x != nil {
		return x.EmailFraud
	}
	return false
}

func (x *RiskAttributes) GetEmailFraudCount() int32 {
	if x != nil {
		return x.EmailFraudCount
	}
	return 0
}

func (x *RiskAttributes) GetEmailFraudNetwork() bool {
	if x != nil {
		return x.EmailFraudNetwork
	}
	return false
}

func (x *RiskAttributes) GetEmailIdentityTrust() string {
	if x != nil {
		return x.EmailIdentityTrust
	}
	return ""
}

func (x *RiskAttributes) GetEmailSocialMediaCount() int32 {
	if x != nil {
		return x.EmailSocialMediaCount
	}
	return 0
}

func (x *RiskAttributes) GetIdentityConfidence() string {
	if x != nil {
		return x.IdentityConfidence
	}
	return ""
}

func (x *RiskAttributes) GetTelecomRisk() string {
	if x != nil {
		return x.TelecomRisk
	}
	return ""
}

func (x *RiskAttributes) GetIsPhoneReachable() bool {
	if x != nil {
		return x.IsPhoneReachable
	}
	return false
}

func (x *RiskAttributes) GetPhoneFraud() bool {
	if x != nil {
		return x.PhoneFraud
	}
	return false
}

func (x *RiskAttributes) GetPhoneFraudCount() int32 {
	if x != nil {
		return x.PhoneFraudCount
	}
	return 0
}

func (x *RiskAttributes) GetPhoneFraudNetwork() bool {
	if x != nil {
		return x.PhoneFraudNetwork
	}
	return false
}

func (x *RiskAttributes) GetPhoneIdentityTrust() string {
	if x != nil {
		return x.PhoneIdentityTrust
	}
	return ""
}

func (x *RiskAttributes) GetPhoneSocialMediaCount() int32 {
	if x != nil {
		return x.PhoneSocialMediaCount
	}
	return 0
}

func (x *RiskAttributes) GetNegativeInsights() []string {
	if x != nil {
		return x.NegativeInsights
	}
	return nil
}

func (x *RiskAttributes) GetPositiveInsights() []string {
	if x != nil {
		return x.PositiveInsights
	}
	return nil
}

func (x *RiskAttributes) GetUpiPhoneNameMatch() int32 {
	if x != nil {
		return x.UpiPhoneNameMatch
	}
	return 0
}

func (x *RiskAttributes) GetUpiPhoneNameMatchScore() float64 {
	if x != nil {
		return x.UpiPhoneNameMatchScore
	}
	return 0
}

func (x *RiskAttributes) GetAlternateRiskScore() float64 {
	if x != nil {
		return x.AlternateRiskScore
	}
	return 0
}

type DetectReOnboardingRiskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header    *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ActorId   string                       `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	RequestId string                       `protobuf:"bytes,3,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// Actor ID of the user who referred the current user
	ReferrerActorId string `protobuf:"bytes,4,opt,name=referrer_actor_id,json=referrerActorId,proto3" json:"referrer_actor_id,omitempty"`
	// Device of the user
	Device                      *common.Device     `protobuf:"bytes,5,opt,name=device,proto3" json:"device,omitempty"`
	EmailId                     string             `protobuf:"bytes,6,opt,name=email_id,json=emailId,proto3" json:"email_id,omitempty"`
	IsCreditReportPresent       common.BooleanEnum `protobuf:"varint,7,opt,name=is_credit_report_present,json=isCreditReportPresent,proto3,enum=api.typesv2.common.BooleanEnum" json:"is_credit_report_present,omitempty"`
	CreditReportDownloadConsent common.BooleanEnum `protobuf:"varint,8,opt,name=credit_report_download_consent,json=creditReportDownloadConsent,proto3,enum=api.typesv2.common.BooleanEnum" json:"credit_report_download_consent,omitempty"`
	IsDevicePremium             common.BooleanEnum `protobuf:"varint,10,opt,name=is_device_premium,json=isDevicePremium,proto3,enum=api.typesv2.common.BooleanEnum" json:"is_device_premium,omitempty"`
	Dob                         *date.Date         `protobuf:"bytes,11,opt,name=dob,proto3" json:"dob,omitempty"`
	// Name match score between gmail and pan name
	GmailPanNameMatchScore float32             `protobuf:"fixed32,12,opt,name=gmail_pan_name_match_score,json=gmailPanNameMatchScore,proto3" json:"gmail_pan_name_match_score,omitempty"`
	PhoneNumber            *common.PhoneNumber `protobuf:"bytes,13,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// The count of users which have used the same email in screener
	ScreenerMailCount int32          `protobuf:"varint,14,opt,name=screener_mail_count,json=screenerMailCount,proto3" json:"screener_mail_count,omitempty"`
	Geolocation       *latlng.LatLng `protobuf:"bytes,15,opt,name=geolocation,proto3" json:"geolocation,omitempty"`
	// Optional threshold to pass if we want to control the threshold from backend
	// If not passed, then threshold will be controlled from risk service side
	Threshold                    float32                   `protobuf:"fixed32,16,opt,name=threshold,proto3" json:"threshold,omitempty"`
	LivenessStatuses             []liveness.LivenessStatus `protobuf:"varint,17,rep,packed,name=liveness_statuses,json=livenessStatuses,proto3,enum=auth.liveness.LivenessStatus" json:"liveness_statuses,omitempty"`
	LivenessScores               []float32                 `protobuf:"fixed32,18,rep,packed,name=liveness_scores,json=livenessScores,proto3" json:"liveness_scores,omitempty"`
	LivenessInhouseErrors        []string                  `protobuf:"bytes,19,rep,name=liveness_inhouse_errors,json=livenessInhouseErrors,proto3" json:"liveness_inhouse_errors,omitempty"`
	LivenessInhouseScores        []float32                 `protobuf:"fixed32,20,rep,packed,name=liveness_inhouse_scores,json=livenessInhouseScores,proto3" json:"liveness_inhouse_scores,omitempty"`
	FacematchScores              []float32                 `protobuf:"fixed32,21,rep,packed,name=facematch_scores,json=facematchScores,proto3" json:"facematch_scores,omitempty"`
	CkycErrors                   []kyc.FailureType         `protobuf:"varint,22,rep,packed,name=ckyc_errors,json=ckycErrors,proto3,enum=kyc.FailureType" json:"ckyc_errors,omitempty"`
	HashedPhoneNumber            string                    `protobuf:"bytes,23,opt,name=hashed_phone_number,json=hashedPhoneNumber,proto3" json:"hashed_phone_number,omitempty"`
	FatherName                   *common.Name              `protobuf:"bytes,25,opt,name=father_name,json=fatherName,proto3" json:"father_name,omitempty"`
	MotherName                   *common.Name              `protobuf:"bytes,26,opt,name=mother_name,json=motherName,proto3" json:"mother_name,omitempty"`
	OnboardingEkycNumberMismatch common.BooleanEnum        `protobuf:"varint,27,opt,name=onboarding_ekyc_number_mismatch,json=onboardingEkycNumberMismatch,proto3,enum=api.typesv2.common.BooleanEnum" json:"onboarding_ekyc_number_mismatch,omitempty"`
	UserPanName                  *common.Name              `protobuf:"bytes,28,opt,name=user_pan_name,json=userPanName,proto3" json:"user_pan_name,omitempty"`
	OtpScores                    []float32                 `protobuf:"fixed32,29,rep,packed,name=otp_scores,json=otpScores,proto3" json:"otp_scores,omitempty"`
	UserCity                     string                    `protobuf:"bytes,30,opt,name=user_city,json=userCity,proto3" json:"user_city,omitempty"`
	UserPostalCode               string                    `protobuf:"bytes,31,opt,name=user_postal_code,json=userPostalCode,proto3" json:"user_postal_code,omitempty"`
	KycLevel                     kyc.KYCLevel              `protobuf:"varint,32,opt,name=kyc_level,json=kycLevel,proto3,enum=kyc.KYCLevel" json:"kyc_level,omitempty"`
	OverallAfuStatus             afu.OverallStatus         `protobuf:"varint,33,opt,name=overall_afu_status,json=overallAfuStatus,proto3,enum=auth.afu.OverallStatus" json:"overall_afu_status,omitempty"`
	AuthFactors                  []afu.AuthFactor          `protobuf:"varint,34,rep,packed,name=auth_factors,json=authFactors,proto3,enum=auth.afu.AuthFactor" json:"auth_factors,omitempty"`
	FailureReason                afu.FailureReason         `protobuf:"varint,35,opt,name=failure_reason,json=failureReason,proto3,enum=auth.afu.FailureReason" json:"failure_reason,omitempty"`
	Context                      *afu.Context              `protobuf:"bytes,36,opt,name=context,proto3" json:"context,omitempty"`
	AfuAttemptNum                int64                     `protobuf:"varint,37,opt,name=afu_attempt_num,json=afuAttemptNum,proto3" json:"afu_attempt_num,omitempty"`
	AfuLivenessScore             []float32                 `protobuf:"fixed32,38,rep,packed,name=afu_liveness_score,json=afuLivenessScore,proto3" json:"afu_liveness_score,omitempty"`
	AfuOtpScore                  []float32                 `protobuf:"fixed32,39,rep,packed,name=afu_otp_score,json=afuOtpScore,proto3" json:"afu_otp_score,omitempty"`
	AfuFacematchScore            []float32                 `protobuf:"fixed32,40,rep,packed,name=afu_facematch_score,json=afuFacematchScore,proto3" json:"afu_facematch_score,omitempty"`
	// state (administrative area) of the address provided by the user in shipping address
	UserState              string                               `protobuf:"bytes,41,opt,name=user_state,json=userState,proto3" json:"user_state,omitempty"`
	AfuGeolocation         *latlng.LatLng                       `protobuf:"bytes,42,opt,name=afu_geolocation,json=afuGeolocation,proto3" json:"afu_geolocation,omitempty"`
	OnboardingCompletedAt  string                               `protobuf:"bytes,43,opt,name=onboarding_completed_at,json=onboardingCompletedAt,proto3" json:"onboarding_completed_at,omitempty"`
	CbDetails              []*inhouse.CreditReportAttributeInfo `protobuf:"bytes,44,rep,name=cb_details,json=cbDetails,proto3" json:"cb_details,omitempty"`
	AfuLivenessStatuses    []liveness.LivenessStatus            `protobuf:"varint,45,rep,packed,name=afu_liveness_statuses,json=afuLivenessStatuses,proto3,enum=auth.liveness.LivenessStatus" json:"afu_liveness_statuses,omitempty"`
	EmploymentData         *employment.EmploymentData           `protobuf:"bytes,46,opt,name=employment_data,json=employmentData,proto3" json:"employment_data,omitempty"`
	CheckDetails           []*screener.CheckDetails             `protobuf:"bytes,47,rep,name=check_details,json=checkDetails,proto3" json:"check_details,omitempty"`
	AfuAttempts            []*AFUAttempt                        `protobuf:"bytes,48,rep,name=afu_attempts,json=afuAttempts,proto3" json:"afu_attempts,omitempty"`
	AccountInfo            *AccountInfo                         `protobuf:"bytes,49,opt,name=account_info,json=accountInfo,proto3" json:"account_info,omitempty"`
	Profile                *user.Profile                        `protobuf:"bytes,50,opt,name=profile,proto3" json:"profile,omitempty"`
	DevicePropertyValueMap map[string]*typesv2.PropertyValue    `protobuf:"bytes,51,rep,name=device_property_value_map,json=devicePropertyValueMap,proto3" json:"device_property_value_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	OnboardingDetails      *OnboardingDetails                   `protobuf:"bytes,52,opt,name=onboarding_details,json=onboardingDetails,proto3" json:"onboarding_details,omitempty"`
	OldDevice              *common.Device                       `protobuf:"bytes,53,opt,name=old_device,json=oldDevice,proto3" json:"old_device,omitempty"`
}

func (x *DetectReOnboardingRiskRequest) Reset() {
	*x = DetectReOnboardingRiskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_risk_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DetectReOnboardingRiskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectReOnboardingRiskRequest) ProtoMessage() {}

func (x *DetectReOnboardingRiskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_risk_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectReOnboardingRiskRequest.ProtoReflect.Descriptor instead.
func (*DetectReOnboardingRiskRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_risk_service_proto_rawDescGZIP(), []int{14}
}

func (x *DetectReOnboardingRiskRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *DetectReOnboardingRiskRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *DetectReOnboardingRiskRequest) GetReferrerActorId() string {
	if x != nil {
		return x.ReferrerActorId
	}
	return ""
}

func (x *DetectReOnboardingRiskRequest) GetDevice() *common.Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetEmailId() string {
	if x != nil {
		return x.EmailId
	}
	return ""
}

func (x *DetectReOnboardingRiskRequest) GetIsCreditReportPresent() common.BooleanEnum {
	if x != nil {
		return x.IsCreditReportPresent
	}
	return common.BooleanEnum(0)
}

func (x *DetectReOnboardingRiskRequest) GetCreditReportDownloadConsent() common.BooleanEnum {
	if x != nil {
		return x.CreditReportDownloadConsent
	}
	return common.BooleanEnum(0)
}

func (x *DetectReOnboardingRiskRequest) GetIsDevicePremium() common.BooleanEnum {
	if x != nil {
		return x.IsDevicePremium
	}
	return common.BooleanEnum(0)
}

func (x *DetectReOnboardingRiskRequest) GetDob() *date.Date {
	if x != nil {
		return x.Dob
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetGmailPanNameMatchScore() float32 {
	if x != nil {
		return x.GmailPanNameMatchScore
	}
	return 0
}

func (x *DetectReOnboardingRiskRequest) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetScreenerMailCount() int32 {
	if x != nil {
		return x.ScreenerMailCount
	}
	return 0
}

func (x *DetectReOnboardingRiskRequest) GetGeolocation() *latlng.LatLng {
	if x != nil {
		return x.Geolocation
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetThreshold() float32 {
	if x != nil {
		return x.Threshold
	}
	return 0
}

func (x *DetectReOnboardingRiskRequest) GetLivenessStatuses() []liveness.LivenessStatus {
	if x != nil {
		return x.LivenessStatuses
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetLivenessScores() []float32 {
	if x != nil {
		return x.LivenessScores
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetLivenessInhouseErrors() []string {
	if x != nil {
		return x.LivenessInhouseErrors
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetLivenessInhouseScores() []float32 {
	if x != nil {
		return x.LivenessInhouseScores
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetFacematchScores() []float32 {
	if x != nil {
		return x.FacematchScores
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetCkycErrors() []kyc.FailureType {
	if x != nil {
		return x.CkycErrors
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetHashedPhoneNumber() string {
	if x != nil {
		return x.HashedPhoneNumber
	}
	return ""
}

func (x *DetectReOnboardingRiskRequest) GetFatherName() *common.Name {
	if x != nil {
		return x.FatherName
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetMotherName() *common.Name {
	if x != nil {
		return x.MotherName
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetOnboardingEkycNumberMismatch() common.BooleanEnum {
	if x != nil {
		return x.OnboardingEkycNumberMismatch
	}
	return common.BooleanEnum(0)
}

func (x *DetectReOnboardingRiskRequest) GetUserPanName() *common.Name {
	if x != nil {
		return x.UserPanName
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetOtpScores() []float32 {
	if x != nil {
		return x.OtpScores
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetUserCity() string {
	if x != nil {
		return x.UserCity
	}
	return ""
}

func (x *DetectReOnboardingRiskRequest) GetUserPostalCode() string {
	if x != nil {
		return x.UserPostalCode
	}
	return ""
}

func (x *DetectReOnboardingRiskRequest) GetKycLevel() kyc.KYCLevel {
	if x != nil {
		return x.KycLevel
	}
	return kyc.KYCLevel(0)
}

func (x *DetectReOnboardingRiskRequest) GetOverallAfuStatus() afu.OverallStatus {
	if x != nil {
		return x.OverallAfuStatus
	}
	return afu.OverallStatus(0)
}

func (x *DetectReOnboardingRiskRequest) GetAuthFactors() []afu.AuthFactor {
	if x != nil {
		return x.AuthFactors
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetFailureReason() afu.FailureReason {
	if x != nil {
		return x.FailureReason
	}
	return afu.FailureReason(0)
}

func (x *DetectReOnboardingRiskRequest) GetContext() *afu.Context {
	if x != nil {
		return x.Context
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetAfuAttemptNum() int64 {
	if x != nil {
		return x.AfuAttemptNum
	}
	return 0
}

func (x *DetectReOnboardingRiskRequest) GetAfuLivenessScore() []float32 {
	if x != nil {
		return x.AfuLivenessScore
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetAfuOtpScore() []float32 {
	if x != nil {
		return x.AfuOtpScore
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetAfuFacematchScore() []float32 {
	if x != nil {
		return x.AfuFacematchScore
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetUserState() string {
	if x != nil {
		return x.UserState
	}
	return ""
}

func (x *DetectReOnboardingRiskRequest) GetAfuGeolocation() *latlng.LatLng {
	if x != nil {
		return x.AfuGeolocation
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetOnboardingCompletedAt() string {
	if x != nil {
		return x.OnboardingCompletedAt
	}
	return ""
}

func (x *DetectReOnboardingRiskRequest) GetCbDetails() []*inhouse.CreditReportAttributeInfo {
	if x != nil {
		return x.CbDetails
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetAfuLivenessStatuses() []liveness.LivenessStatus {
	if x != nil {
		return x.AfuLivenessStatuses
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetEmploymentData() *employment.EmploymentData {
	if x != nil {
		return x.EmploymentData
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetCheckDetails() []*screener.CheckDetails {
	if x != nil {
		return x.CheckDetails
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetAfuAttempts() []*AFUAttempt {
	if x != nil {
		return x.AfuAttempts
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetAccountInfo() *AccountInfo {
	if x != nil {
		return x.AccountInfo
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetProfile() *user.Profile {
	if x != nil {
		return x.Profile
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetDevicePropertyValueMap() map[string]*typesv2.PropertyValue {
	if x != nil {
		return x.DevicePropertyValueMap
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetOnboardingDetails() *OnboardingDetails {
	if x != nil {
		return x.OnboardingDetails
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetOldDevice() *common.Device {
	if x != nil {
		return x.OldDevice
	}
	return nil
}

type DetectReOnboardingRiskResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status    *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Score     float32     `protobuf:"fixed32,2,opt,name=score,proto3" json:"score,omitempty"`
	Threshold float32     `protobuf:"fixed32,3,opt,name=threshold,proto3" json:"threshold,omitempty"`
	// bool flag to denote if the user is risky
	RiskyUser common.BooleanEnum `protobuf:"varint,4,opt,name=risky_user,json=riskyUser,proto3,enum=api.typesv2.common.BooleanEnum" json:"risky_user,omitempty"`
	// response time of the API in seconds
	Time *durationpb.Duration `protobuf:"bytes,5,opt,name=time,proto3" json:"time,omitempty"`
	// list of errors returned by the API
	Error             []string `protobuf:"bytes,6,rep,name=error,proto3" json:"error,omitempty"`
	RawVendorResponse string   `protobuf:"bytes,7,opt,name=raw_vendor_response,json=rawVendorResponse,proto3" json:"raw_vendor_response,omitempty"`
}

func (x *DetectReOnboardingRiskResponse) Reset() {
	*x = DetectReOnboardingRiskResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_risk_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DetectReOnboardingRiskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectReOnboardingRiskResponse) ProtoMessage() {}

func (x *DetectReOnboardingRiskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_risk_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectReOnboardingRiskResponse.ProtoReflect.Descriptor instead.
func (*DetectReOnboardingRiskResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_risk_service_proto_rawDescGZIP(), []int{15}
}

func (x *DetectReOnboardingRiskResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *DetectReOnboardingRiskResponse) GetScore() float32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *DetectReOnboardingRiskResponse) GetThreshold() float32 {
	if x != nil {
		return x.Threshold
	}
	return 0
}

func (x *DetectReOnboardingRiskResponse) GetRiskyUser() common.BooleanEnum {
	if x != nil {
		return x.RiskyUser
	}
	return common.BooleanEnum(0)
}

func (x *DetectReOnboardingRiskResponse) GetTime() *durationpb.Duration {
	if x != nil {
		return x.Time
	}
	return nil
}

func (x *DetectReOnboardingRiskResponse) GetError() []string {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *DetectReOnboardingRiskResponse) GetRawVendorResponse() string {
	if x != nil {
		return x.RawVendorResponse
	}
	return ""
}

type DetectRiskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header    *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ActorId   string                       `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	RequestId string                       `protobuf:"bytes,3,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// Actor ID of the user who referred the current user
	ReferrerActorId string `protobuf:"bytes,4,opt,name=referrer_actor_id,json=referrerActorId,proto3" json:"referrer_actor_id,omitempty"`
	// Device of the user
	Device                      *common.Device     `protobuf:"bytes,5,opt,name=device,proto3" json:"device,omitempty"`
	EmailId                     string             `protobuf:"bytes,6,opt,name=email_id,json=emailId,proto3" json:"email_id,omitempty"`
	IsCreditReportPresent       common.BooleanEnum `protobuf:"varint,7,opt,name=is_credit_report_present,json=isCreditReportPresent,proto3,enum=api.typesv2.common.BooleanEnum" json:"is_credit_report_present,omitempty"`
	CreditReportDownloadConsent common.BooleanEnum `protobuf:"varint,8,opt,name=credit_report_download_consent,json=creditReportDownloadConsent,proto3,enum=api.typesv2.common.BooleanEnum" json:"credit_report_download_consent,omitempty"`
	IsDevicePremium             common.BooleanEnum `protobuf:"varint,10,opt,name=is_device_premium,json=isDevicePremium,proto3,enum=api.typesv2.common.BooleanEnum" json:"is_device_premium,omitempty"`
	Dob                         *date.Date         `protobuf:"bytes,11,opt,name=dob,proto3" json:"dob,omitempty"`
	// Name match score between gmail and pan name
	GmailPanNameMatchScore float32             `protobuf:"fixed32,12,opt,name=gmail_pan_name_match_score,json=gmailPanNameMatchScore,proto3" json:"gmail_pan_name_match_score,omitempty"`
	PhoneNumber            *common.PhoneNumber `protobuf:"bytes,13,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// The count of users which have used the same email in screener
	ScreenerMailCount int32          `protobuf:"varint,14,opt,name=screener_mail_count,json=screenerMailCount,proto3" json:"screener_mail_count,omitempty"`
	Geolocation       *latlng.LatLng `protobuf:"bytes,15,opt,name=geolocation,proto3" json:"geolocation,omitempty"`
	// Optional threshold to pass if we want to control the threshold from backend
	// If not passed, then threshold will be controlled from risk service side
	Threshold                    float32                              `protobuf:"fixed32,16,opt,name=threshold,proto3" json:"threshold,omitempty"`
	LivenessStatuses             []liveness.LivenessStatus            `protobuf:"varint,17,rep,packed,name=liveness_statuses,json=livenessStatuses,proto3,enum=auth.liveness.LivenessStatus" json:"liveness_statuses,omitempty"`
	LivenessScores               []float32                            `protobuf:"fixed32,18,rep,packed,name=liveness_scores,json=livenessScores,proto3" json:"liveness_scores,omitempty"`
	LivenessInhouseErrors        []string                             `protobuf:"bytes,19,rep,name=liveness_inhouse_errors,json=livenessInhouseErrors,proto3" json:"liveness_inhouse_errors,omitempty"`
	LivenessInhouseScores        []float32                            `protobuf:"fixed32,20,rep,packed,name=liveness_inhouse_scores,json=livenessInhouseScores,proto3" json:"liveness_inhouse_scores,omitempty"`
	FacematchScores              []float32                            `protobuf:"fixed32,21,rep,packed,name=facematch_scores,json=facematchScores,proto3" json:"facematch_scores,omitempty"`
	CkycErrors                   []kyc.FailureType                    `protobuf:"varint,22,rep,packed,name=ckyc_errors,json=ckycErrors,proto3,enum=kyc.FailureType" json:"ckyc_errors,omitempty"`
	HashedPhoneNumber            string                               `protobuf:"bytes,23,opt,name=hashed_phone_number,json=hashedPhoneNumber,proto3" json:"hashed_phone_number,omitempty"`
	FatherName                   *common.Name                         `protobuf:"bytes,25,opt,name=father_name,json=fatherName,proto3" json:"father_name,omitempty"`
	MotherName                   *common.Name                         `protobuf:"bytes,26,opt,name=mother_name,json=motherName,proto3" json:"mother_name,omitempty"`
	OnboardingEkycNumberMismatch common.BooleanEnum                   `protobuf:"varint,27,opt,name=onboarding_ekyc_number_mismatch,json=onboardingEkycNumberMismatch,proto3,enum=api.typesv2.common.BooleanEnum" json:"onboarding_ekyc_number_mismatch,omitempty"`
	UserPanName                  *common.Name                         `protobuf:"bytes,28,opt,name=user_pan_name,json=userPanName,proto3" json:"user_pan_name,omitempty"`
	OtpScores                    []float32                            `protobuf:"fixed32,29,rep,packed,name=otp_scores,json=otpScores,proto3" json:"otp_scores,omitempty"`
	CbDetails                    []*inhouse.CreditReportAttributeInfo `protobuf:"bytes,30,rep,name=cb_details,json=cbDetails,proto3" json:"cb_details,omitempty"`
	EmploymentData               *employment.EmploymentData           `protobuf:"bytes,31,opt,name=employment_data,json=employmentData,proto3" json:"employment_data,omitempty"`
	DevicePropertyValueMap       map[string]*typesv2.PropertyValue    `protobuf:"bytes,32,rep,name=device_property_value_map,json=devicePropertyValueMap,proto3" json:"device_property_value_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// this specifies which version of the ds model will be used
	ModelVersion DetectRiskRequest_ModelVersion `protobuf:"varint,33,opt,name=model_version,json=modelVersion,proto3,enum=vendorgateway.risk.DetectRiskRequest_ModelVersion" json:"model_version,omitempty"`
}

func (x *DetectRiskRequest) Reset() {
	*x = DetectRiskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_risk_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DetectRiskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectRiskRequest) ProtoMessage() {}

func (x *DetectRiskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_risk_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectRiskRequest.ProtoReflect.Descriptor instead.
func (*DetectRiskRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_risk_service_proto_rawDescGZIP(), []int{16}
}

func (x *DetectRiskRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *DetectRiskRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *DetectRiskRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *DetectRiskRequest) GetReferrerActorId() string {
	if x != nil {
		return x.ReferrerActorId
	}
	return ""
}

func (x *DetectRiskRequest) GetDevice() *common.Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *DetectRiskRequest) GetEmailId() string {
	if x != nil {
		return x.EmailId
	}
	return ""
}

func (x *DetectRiskRequest) GetIsCreditReportPresent() common.BooleanEnum {
	if x != nil {
		return x.IsCreditReportPresent
	}
	return common.BooleanEnum(0)
}

func (x *DetectRiskRequest) GetCreditReportDownloadConsent() common.BooleanEnum {
	if x != nil {
		return x.CreditReportDownloadConsent
	}
	return common.BooleanEnum(0)
}

func (x *DetectRiskRequest) GetIsDevicePremium() common.BooleanEnum {
	if x != nil {
		return x.IsDevicePremium
	}
	return common.BooleanEnum(0)
}

func (x *DetectRiskRequest) GetDob() *date.Date {
	if x != nil {
		return x.Dob
	}
	return nil
}

func (x *DetectRiskRequest) GetGmailPanNameMatchScore() float32 {
	if x != nil {
		return x.GmailPanNameMatchScore
	}
	return 0
}

func (x *DetectRiskRequest) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *DetectRiskRequest) GetScreenerMailCount() int32 {
	if x != nil {
		return x.ScreenerMailCount
	}
	return 0
}

func (x *DetectRiskRequest) GetGeolocation() *latlng.LatLng {
	if x != nil {
		return x.Geolocation
	}
	return nil
}

func (x *DetectRiskRequest) GetThreshold() float32 {
	if x != nil {
		return x.Threshold
	}
	return 0
}

func (x *DetectRiskRequest) GetLivenessStatuses() []liveness.LivenessStatus {
	if x != nil {
		return x.LivenessStatuses
	}
	return nil
}

func (x *DetectRiskRequest) GetLivenessScores() []float32 {
	if x != nil {
		return x.LivenessScores
	}
	return nil
}

func (x *DetectRiskRequest) GetLivenessInhouseErrors() []string {
	if x != nil {
		return x.LivenessInhouseErrors
	}
	return nil
}

func (x *DetectRiskRequest) GetLivenessInhouseScores() []float32 {
	if x != nil {
		return x.LivenessInhouseScores
	}
	return nil
}

func (x *DetectRiskRequest) GetFacematchScores() []float32 {
	if x != nil {
		return x.FacematchScores
	}
	return nil
}

func (x *DetectRiskRequest) GetCkycErrors() []kyc.FailureType {
	if x != nil {
		return x.CkycErrors
	}
	return nil
}

func (x *DetectRiskRequest) GetHashedPhoneNumber() string {
	if x != nil {
		return x.HashedPhoneNumber
	}
	return ""
}

func (x *DetectRiskRequest) GetFatherName() *common.Name {
	if x != nil {
		return x.FatherName
	}
	return nil
}

func (x *DetectRiskRequest) GetMotherName() *common.Name {
	if x != nil {
		return x.MotherName
	}
	return nil
}

func (x *DetectRiskRequest) GetOnboardingEkycNumberMismatch() common.BooleanEnum {
	if x != nil {
		return x.OnboardingEkycNumberMismatch
	}
	return common.BooleanEnum(0)
}

func (x *DetectRiskRequest) GetUserPanName() *common.Name {
	if x != nil {
		return x.UserPanName
	}
	return nil
}

func (x *DetectRiskRequest) GetOtpScores() []float32 {
	if x != nil {
		return x.OtpScores
	}
	return nil
}

func (x *DetectRiskRequest) GetCbDetails() []*inhouse.CreditReportAttributeInfo {
	if x != nil {
		return x.CbDetails
	}
	return nil
}

func (x *DetectRiskRequest) GetEmploymentData() *employment.EmploymentData {
	if x != nil {
		return x.EmploymentData
	}
	return nil
}

func (x *DetectRiskRequest) GetDevicePropertyValueMap() map[string]*typesv2.PropertyValue {
	if x != nil {
		return x.DevicePropertyValueMap
	}
	return nil
}

func (x *DetectRiskRequest) GetModelVersion() DetectRiskRequest_ModelVersion {
	if x != nil {
		return x.ModelVersion
	}
	return DetectRiskRequest_MODEL_VERSION_UNSPECIFIED
}

type DetectRiskResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status    *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Score     float32     `protobuf:"fixed32,2,opt,name=score,proto3" json:"score,omitempty"`
	Threshold float32     `protobuf:"fixed32,3,opt,name=threshold,proto3" json:"threshold,omitempty"`
	// bool flag to denote if the user is risky
	RiskyUser common.BooleanEnum `protobuf:"varint,4,opt,name=risky_user,json=riskyUser,proto3,enum=api.typesv2.common.BooleanEnum" json:"risky_user,omitempty"`
	// response time of the API in seconds
	Time *durationpb.Duration `protobuf:"bytes,5,opt,name=time,proto3" json:"time,omitempty"`
	// list of errors returned by the API
	Error             []string `protobuf:"bytes,6,rep,name=error,proto3" json:"error,omitempty"`
	RawVendorResponse string   `protobuf:"bytes,7,opt,name=raw_vendor_response,json=rawVendorResponse,proto3" json:"raw_vendor_response,omitempty"`
}

func (x *DetectRiskResponse) Reset() {
	*x = DetectRiskResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_risk_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DetectRiskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectRiskResponse) ProtoMessage() {}

func (x *DetectRiskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_risk_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectRiskResponse.ProtoReflect.Descriptor instead.
func (*DetectRiskResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_risk_service_proto_rawDescGZIP(), []int{17}
}

func (x *DetectRiskResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *DetectRiskResponse) GetScore() float32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *DetectRiskResponse) GetThreshold() float32 {
	if x != nil {
		return x.Threshold
	}
	return 0
}

func (x *DetectRiskResponse) GetRiskyUser() common.BooleanEnum {
	if x != nil {
		return x.RiskyUser
	}
	return common.BooleanEnum(0)
}

func (x *DetectRiskResponse) GetTime() *durationpb.Duration {
	if x != nil {
		return x.Time
	}
	return nil
}

func (x *DetectRiskResponse) GetError() []string {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *DetectRiskResponse) GetRawVendorResponse() string {
	if x != nil {
		return x.RawVendorResponse
	}
	return ""
}

type DetectLocationRiskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header    *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ActorId   string                       `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	RequestId string                       `protobuf:"bytes,3,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	LatLng    *latlng.LatLng               `protobuf:"bytes,4,opt,name=lat_lng,json=latLng,proto3" json:"lat_lng,omitempty"`
	Pincode   string                       `protobuf:"bytes,5,opt,name=pincode,proto3" json:"pincode,omitempty"`
}

func (x *DetectLocationRiskRequest) Reset() {
	*x = DetectLocationRiskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_risk_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DetectLocationRiskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectLocationRiskRequest) ProtoMessage() {}

func (x *DetectLocationRiskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_risk_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectLocationRiskRequest.ProtoReflect.Descriptor instead.
func (*DetectLocationRiskRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_risk_service_proto_rawDescGZIP(), []int{18}
}

func (x *DetectLocationRiskRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *DetectLocationRiskRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *DetectLocationRiskRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *DetectLocationRiskRequest) GetLatLng() *latlng.LatLng {
	if x != nil {
		return x.LatLng
	}
	return nil
}

func (x *DetectLocationRiskRequest) GetPincode() string {
	if x != nil {
		return x.Pincode
	}
	return ""
}

type DetectLocationRiskResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// location risk score will be in range [0, 1]
	Score             float32      `protobuf:"fixed32,2,opt,name=score,proto3" json:"score,omitempty"`
	RiskSeverity      RiskSeverity `protobuf:"varint,3,opt,name=risk_severity,json=riskSeverity,proto3,enum=vendorgateway.risk.RiskSeverity" json:"risk_severity,omitempty"`
	ModelVersion      string       `protobuf:"bytes,4,opt,name=model_version,json=modelVersion,proto3" json:"model_version,omitempty"`
	RawVendorResponse string       `protobuf:"bytes,5,opt,name=raw_vendor_response,json=rawVendorResponse,proto3" json:"raw_vendor_response,omitempty"`
}

func (x *DetectLocationRiskResponse) Reset() {
	*x = DetectLocationRiskResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_risk_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DetectLocationRiskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectLocationRiskResponse) ProtoMessage() {}

func (x *DetectLocationRiskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_risk_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectLocationRiskResponse.ProtoReflect.Descriptor instead.
func (*DetectLocationRiskResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_risk_service_proto_rawDescGZIP(), []int{19}
}

func (x *DetectLocationRiskResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *DetectLocationRiskResponse) GetScore() float32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *DetectLocationRiskResponse) GetRiskSeverity() RiskSeverity {
	if x != nil {
		return x.RiskSeverity
	}
	return RiskSeverity_RISK_SEVERITY_UNSPECIFIED
}

func (x *DetectLocationRiskResponse) GetModelVersion() string {
	if x != nil {
		return x.ModelVersion
	}
	return ""
}

func (x *DetectLocationRiskResponse) GetRawVendorResponse() string {
	if x != nil {
		return x.RawVendorResponse
	}
	return ""
}

// AFUAttempt contains afu data for a single afu attempt such as liveness and facematch scores.
type AFUAttempt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OverallStatus    afu.OverallStatus         `protobuf:"varint,1,opt,name=overall_status,json=overallStatus,proto3,enum=auth.afu.OverallStatus" json:"overall_status,omitempty"`
	AuthFactors      []afu.AuthFactor          `protobuf:"varint,2,rep,packed,name=auth_factors,json=authFactors,proto3,enum=auth.afu.AuthFactor" json:"auth_factors,omitempty"`
	LivenessScores   []float32                 `protobuf:"fixed32,3,rep,packed,name=liveness_scores,json=livenessScores,proto3" json:"liveness_scores,omitempty"`
	OtpScores        []float32                 `protobuf:"fixed32,4,rep,packed,name=otp_scores,json=otpScores,proto3" json:"otp_scores,omitempty"`
	FacematchScores  []float32                 `protobuf:"fixed32,5,rep,packed,name=facematch_scores,json=facematchScores,proto3" json:"facematch_scores,omitempty"`
	CreatedAt        *timestamppb.Timestamp    `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	LivenessStatuses []liveness.LivenessStatus `protobuf:"varint,7,rep,packed,name=liveness_statuses,json=livenessStatuses,proto3,enum=auth.liveness.LivenessStatus" json:"liveness_statuses,omitempty"`
}

func (x *AFUAttempt) Reset() {
	*x = AFUAttempt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_risk_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AFUAttempt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AFUAttempt) ProtoMessage() {}

func (x *AFUAttempt) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_risk_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AFUAttempt.ProtoReflect.Descriptor instead.
func (*AFUAttempt) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_risk_service_proto_rawDescGZIP(), []int{20}
}

func (x *AFUAttempt) GetOverallStatus() afu.OverallStatus {
	if x != nil {
		return x.OverallStatus
	}
	return afu.OverallStatus(0)
}

func (x *AFUAttempt) GetAuthFactors() []afu.AuthFactor {
	if x != nil {
		return x.AuthFactors
	}
	return nil
}

func (x *AFUAttempt) GetLivenessScores() []float32 {
	if x != nil {
		return x.LivenessScores
	}
	return nil
}

func (x *AFUAttempt) GetOtpScores() []float32 {
	if x != nil {
		return x.OtpScores
	}
	return nil
}

func (x *AFUAttempt) GetFacematchScores() []float32 {
	if x != nil {
		return x.FacematchScores
	}
	return nil
}

func (x *AFUAttempt) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *AFUAttempt) GetLivenessStatuses() []liveness.LivenessStatus {
	if x != nil {
		return x.LivenessStatuses
	}
	return nil
}

// AccountInfo contains info related to user account.
type AccountInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tier      external.Tier          `protobuf:"varint,1,opt,name=tier,proto3,enum=tiering.external.Tier" json:"tier,omitempty"`
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *AccountInfo) Reset() {
	*x = AccountInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_risk_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountInfo) ProtoMessage() {}

func (x *AccountInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_risk_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountInfo.ProtoReflect.Descriptor instead.
func (*AccountInfo) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_risk_service_proto_rawDescGZIP(), []int{21}
}

func (x *AccountInfo) GetTier() external.Tier {
	if x != nil {
		return x.Tier
	}
	return external.Tier(0)
}

func (x *AccountInfo) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

// OnboardingDetails contains onboarding details and onboarding risk checks related info for user.
type OnboardingDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OnboardingRiskModelScore float32 `protobuf:"fixed32,1,opt,name=onboarding_risk_model_score,json=onboardingRiskModelScore,proto3" json:"onboarding_risk_model_score,omitempty"`
}

func (x *OnboardingDetails) Reset() {
	*x = OnboardingDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_risk_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OnboardingDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnboardingDetails) ProtoMessage() {}

func (x *OnboardingDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_risk_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnboardingDetails.ProtoReflect.Descriptor instead.
func (*OnboardingDetails) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_risk_service_proto_rawDescGZIP(), []int{22}
}

func (x *OnboardingDetails) GetOnboardingRiskModelScore() float32 {
	if x != nil {
		return x.OnboardingRiskModelScore
	}
	return 0
}

type GetCasePrioritisationScoreRequest_CaseDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CaseId               string                                  `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	AlertWithRuleDetails []*case_management.AlertWithRuleDetails `protobuf:"bytes,2,rep,name=alert_with_rule_details,json=alertWithRuleDetails,proto3" json:"alert_with_rule_details,omitempty"`
}

func (x *GetCasePrioritisationScoreRequest_CaseDetails) Reset() {
	*x = GetCasePrioritisationScoreRequest_CaseDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_risk_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCasePrioritisationScoreRequest_CaseDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCasePrioritisationScoreRequest_CaseDetails) ProtoMessage() {}

func (x *GetCasePrioritisationScoreRequest_CaseDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_risk_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCasePrioritisationScoreRequest_CaseDetails.ProtoReflect.Descriptor instead.
func (*GetCasePrioritisationScoreRequest_CaseDetails) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_risk_service_proto_rawDescGZIP(), []int{0, 0}
}

func (x *GetCasePrioritisationScoreRequest_CaseDetails) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *GetCasePrioritisationScoreRequest_CaseDetails) GetAlertWithRuleDetails() []*case_management.AlertWithRuleDetails {
	if x != nil {
		return x.AlertWithRuleDetails
	}
	return nil
}

var File_api_vendorgateway_risk_service_proto protoreflect.FileDescriptor

var file_api_vendorgateway_risk_service_proto_rawDesc = []byte{
	0x0a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x1a, 0x2a, 0x61, 0x70, 0x69, 0x2f,
	0x61, 0x75, 0x74, 0x68, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x61, 0x75,
	0x74, 0x68, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68,
	0x2f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x65, 0x6d, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61, 0x70, 0x69,
	0x2f, 0x6b, 0x79, 0x63, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x6b, 0x79,
	0x63, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x11, 0x61, 0x70, 0x69, 0x2f, 0x6b, 0x79, 0x63, 0x2f, 0x6b, 0x79, 0x63, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73,
	0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x61, 0x6c, 0x65,
	0x72, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70,
	0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a,
	0x61, 0x70, 0x69, 0x2f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x2f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f,
	0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x2f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x20, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6e, 0x61, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x13, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x69,
	0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6c, 0x61, 0x74, 0x6c, 0x6e, 0x67, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8d, 0x04,
	0x0a, 0x21, 0x47, 0x65, 0x74, 0x43, 0x61, 0x73, 0x65, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74,
	0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x61, 0x0a, 0x17, 0x61, 0x6c, 0x65,
	0x72, 0x74, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x57, 0x69, 0x74, 0x68, 0x52, 0x75, 0x6c, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x14, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x57, 0x69, 0x74,
	0x68, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x64, 0x0a, 0x0c, 0x63,
	0x61, 0x73, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x41, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x73, 0x65, 0x50, 0x72,
	0x69, 0x6f, 0x72, 0x69, 0x74, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x6f, 0x72,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x0b, 0x63, 0x61, 0x73, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x1a, 0x89, 0x01, 0x0a, 0x0b, 0x43, 0x61, 0x73, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x61, 0x0a, 0x17, 0x61, 0x6c,
	0x65, 0x72, 0x74, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x57, 0x69, 0x74, 0x68, 0x52, 0x75, 0x6c, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x14, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x57, 0x69,
	0x74, 0x68, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x3d, 0x0a,
	0x11, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x22, 0x99, 0x02, 0x0a,
	0x22, 0x47, 0x65, 0x74, 0x43, 0x61, 0x73, 0x65, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x69,
	0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05,
	0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x61, 0x77, 0x5f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x11, 0x72, 0x61, 0x77, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x44, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xaf, 0x01, 0x0a, 0x1d, 0x47, 0x65, 0x74,
	0x42, 0x75, 0x72, 0x65, 0x61, 0x75, 0x49, 0x64, 0x52, 0x69, 0x73, 0x6b, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12,
	0x39, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x22, 0x7d, 0x0a, 0x08, 0x55, 0x73,
	0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0b, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x91, 0x07, 0x0a, 0x1e, 0x47, 0x65,
	0x74, 0x42, 0x75, 0x72, 0x65, 0x61, 0x75, 0x49, 0x64, 0x52, 0x69, 0x73, 0x6b, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x35, 0x0a, 0x07, 0x6f, 0x75, 0x74, 0x63, 0x6f, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x4f, 0x75, 0x74, 0x63, 0x6f, 0x6d, 0x65, 0x52,
	0x07, 0x6f, 0x75, 0x74, 0x63, 0x6f, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x4e, 0x0a, 0x10, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x5f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x23, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x41, 0x74, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x52, 0x0f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x41, 0x74, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x5b, 0x0a, 0x15, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x45, 0x6d, 0x61, 0x69,
	0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x52,
	0x13, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x73, 0x12, 0x4e, 0x0a, 0x10, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x61, 0x74,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x72,
	0x69, 0x73, 0x6b, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x73, 0x52, 0x0f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x73, 0x12, 0x5b, 0x0a, 0x15, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x52, 0x13, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65,
	0x73, 0x12, 0x64, 0x0a, 0x18, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x52,
	0x16, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x41, 0x74, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x61, 0x0a, 0x17, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x5f, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x6c, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x45, 0x6d,
	0x61, 0x69, 0x6c, 0x53, 0x6f, 0x63, 0x69, 0x61, 0x6c, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x73, 0x52, 0x15, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x6f, 0x63, 0x69, 0x61, 0x6c,
	0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x61, 0x0a, 0x17, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x5f, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x6c, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x53, 0x6f, 0x63, 0x69, 0x61, 0x6c, 0x41, 0x74, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x52, 0x15, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x53, 0x6f, 0x63,
	0x69, 0x61, 0x6c, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x4b, 0x0a,
	0x0f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x52, 0x69, 0x73, 0x6b,
	0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x52, 0x0e, 0x72, 0x69, 0x73, 0x6b,
	0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x61,
	0x77, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x72, 0x61, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x97, 0x04,
	0x0a, 0x0f, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65,
	0x73, 0x12, 0x4e, 0x0a, 0x10, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x0f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x21, 0x0a, 0x0c, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x65, 0x78, 0x69, 0x73, 0x74,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x45, 0x78,
	0x69, 0x73, 0x74, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x5f,
	0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x64, 0x69, 0x67, 0x69, 0x74,
	0x61, 0x6c, 0x41, 0x67, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5f,
	0x65, 0x78, 0x69, 0x73, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x45, 0x78, 0x69, 0x73, 0x74, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x69, 0x73,
	0x6b, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x27, 0x0a, 0x0f, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12,
	0x29, 0x0a, 0x10, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x72, 0x70, 0x6f, 0x72,
	0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x64, 0x6f, 0x6d, 0x61, 0x69,
	0x6e, 0x43, 0x6f, 0x72, 0x70, 0x6f, 0x72, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x6e,
	0x69, 0x71, 0x75, 0x65, 0x5f, 0x68, 0x69, 0x74, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0a, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x48, 0x69, 0x74, 0x73, 0x12, 0x3c, 0x0a, 0x1a, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x18, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x46, 0x69, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x16, 0x6c, 0x61, 0x73,
	0x74, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x6c, 0x61, 0x73, 0x74, 0x56,
	0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x36, 0x0a, 0x17, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x15, 0x66, 0x69, 0x72, 0x73, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x22, 0xaa, 0x05, 0x0a, 0x13, 0x45, 0x6d, 0x61, 0x69,
	0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12,
	0x4e, 0x0a, 0x10, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x41,
	0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0f,
	0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x69, 0x67,
	0x69, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x64, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x41, 0x67, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x28, 0x0a,
	0x10, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63,
	0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x12,
	0x34, 0x0a, 0x16, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x5f, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x14, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x74,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x12, 0x38, 0x0a, 0x18, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x66,
	0x6f, 0x6f, 0x74, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x6e, 0x67, 0x74,
	0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x46, 0x6f,
	0x6f, 0x74, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x53, 0x74, 0x72, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12,
	0x33, 0x0a, 0x16, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x64, 0x69,
	0x67, 0x69, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x67, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x13, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61,
	0x6c, 0x41, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x73, 0x70,
	0x63, 0x6c, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x70, 0x63, 0x6c, 0x43, 0x68, 0x61, 0x72, 0x73, 0x12, 0x1b,
	0x0a, 0x09, 0x6e, 0x64, 0x72, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x08, 0x6e, 0x64, 0x72, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x75,
	0x6e, 0x72, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08,
	0x75, 0x6e, 0x72, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x6e, 0x61, 0x6d, 0x65,
	0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x0e, 0x6e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x63, 0x6f,
	0x72, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x6e, 0x61,
	0x6d, 0x65, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x12, 0x36, 0x0a, 0x17,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x61,
	0x74, 0x74, 0x61, 0x63, 0x68, 0x65, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x41, 0x74, 0x74, 0x61,
	0x63, 0x68, 0x65, 0x64, 0x22, 0x9f, 0x01, 0x0a, 0x0f, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x41, 0x74,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x4e, 0x0a, 0x10, 0x61, 0x74, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x23, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x70, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x76, 0x70, 0x61, 0x22, 0xff, 0x03, 0x0a, 0x13, 0x50, 0x68, 0x6f, 0x6e, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x4e,
	0x0a, 0x10, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x41, 0x74,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0f, 0x61,
	0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18,
	0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x69, 0x67, 0x69,
	0x74, 0x61, 0x6c, 0x5f, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x64,
	0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x41, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x66, 0x69, 0x72,
	0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0e, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x4d, 0x61,
	0x74, 0x63, 0x68, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x6c, 0x61,
	0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x12, 0x34, 0x0a, 0x16, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x64, 0x65, 0x74,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x64, 0x72, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x6e, 0x64, 0x72, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x75, 0x6e, 0x72, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x08, 0x75, 0x6e, 0x72, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x6e,
	0x61, 0x6d, 0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x6e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68,
	0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x3c, 0x0a, 0x1a, 0x66, 0x6f, 0x6f, 0x74, 0x70, 0x72, 0x69,
	0x6e, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x5f, 0x6f, 0x76, 0x65, 0x72,
	0x61, 0x6c, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x66, 0x6f, 0x6f, 0x74, 0x70,
	0x72, 0x69, 0x6e, 0x74, 0x53, 0x74, 0x72, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x4f, 0x76, 0x65, 0x72,
	0x61, 0x6c, 0x6c, 0x12, 0x33, 0x0a, 0x16, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x5f, 0x64, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x67, 0x65, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x13, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x44, 0x69,
	0x67, 0x69, 0x74, 0x61, 0x6c, 0x41, 0x67, 0x65, 0x22, 0x8f, 0x07, 0x0a, 0x16, 0x50, 0x68, 0x6f,
	0x6e, 0x65, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x73, 0x12, 0x4e, 0x0a, 0x10, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x0f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6d, 0x73, 0x69, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x69, 0x6d, 0x73, 0x69, 0x12, 0x30, 0x0a, 0x14, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x4e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x72, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12,
	0x48, 0x0a, 0x21, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f,
	0x69, 0x73, 0x6f, 0x32, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1d, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72,
	0x79, 0x43, 0x6f, 0x64, 0x65, 0x49, 0x73, 0x6f, 0x32, 0x12, 0x2c, 0x0a, 0x12, 0x69, 0x73, 0x5f,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x63, 0x68, 0x61, 0x62, 0x6c, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x69, 0x73, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x52, 0x65,
	0x61, 0x63, 0x68, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x31, 0x0a, 0x15, 0x69, 0x73, 0x5f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x69, 0x73, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x50,
	0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2e, 0x0a, 0x13, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x5f, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x42,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3b, 0x0a, 0x1a, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x5f, 0x68, 0x61, 0x73, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x5f, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x48, 0x61, 0x73, 0x50, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x37, 0x0a, 0x18, 0x70, 0x6f, 0x72, 0x74, 0x65,
	0x64, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x70, 0x6f, 0x72, 0x74, 0x65,
	0x64, 0x46, 0x72, 0x6f, 0x6d, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x3b, 0x0a, 0x1a, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x5f,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x17, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x46, 0x72, 0x6f, 0x6d,
	0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x4f, 0x0a,
	0x25, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x5f, 0x69, 0x73, 0x6f, 0x32, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x20, 0x70, 0x6f,
	0x72, 0x74, 0x65, 0x64, 0x46, 0x72, 0x6f, 0x6d, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x49, 0x73, 0x6f, 0x32, 0x12, 0x18,
	0x0a, 0x07, 0x72, 0x6f, 0x61, 0x6d, 0x69, 0x6e, 0x67, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x72, 0x6f, 0x61, 0x6d, 0x69, 0x6e, 0x67, 0x12, 0x30, 0x0a, 0x14, 0x72, 0x6f, 0x61, 0x6d,
	0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x72, 0x6f, 0x61, 0x6d, 0x69, 0x6e, 0x67, 0x4e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x72, 0x6f,
	0x61, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x72, 0x6f, 0x61, 0x6d,
	0x69, 0x6e, 0x67, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x12, 0x48, 0x0a, 0x21, 0x72, 0x6f, 0x61, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x5f, 0x69, 0x73, 0x6f, 0x32, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1d, 0x72, 0x6f, 0x61,
	0x6d, 0x69, 0x6e, 0x67, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x49, 0x73, 0x6f, 0x32, 0x22, 0xbd, 0x04, 0x0a, 0x15, 0x50,
	0x68, 0x6f, 0x6e, 0x65, 0x53, 0x6f, 0x63, 0x69, 0x61, 0x6c, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x73, 0x12, 0x4e, 0x0a, 0x10, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x72,
	0x69, 0x73, 0x6b, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x0f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x61, 0x7a, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6d, 0x61, 0x7a, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08,
	0x66, 0x6c, 0x69, 0x70, 0x6b, 0x61, 0x72, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x66, 0x6c, 0x69, 0x70, 0x6b, 0x61, 0x72, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x68, 0x6f, 0x75, 0x73,
	0x69, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x68, 0x6f, 0x75, 0x73, 0x69,
	0x6e, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x6e, 0x64, 0x69, 0x61, 0x6d, 0x61, 0x72, 0x74, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6e, 0x64, 0x69, 0x61, 0x6d, 0x61, 0x72, 0x74,
	0x12, 0x1c, 0x0a, 0x09, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x12, 0x22,
	0x0a, 0x0c, 0x69, 0x73, 0x57, 0x41, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x73, 0x57, 0x41, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x6a, 0x65, 0x65, 0x76, 0x61, 0x6e, 0x73, 0x61, 0x61, 0x74,
	0x68, 0x69, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6a, 0x65, 0x65, 0x76, 0x61, 0x6e,
	0x73, 0x61, 0x61, 0x74, 0x68, 0x69, 0x12, 0x18, 0x0a, 0x07, 0x6a, 0x69, 0x6f, 0x6d, 0x61, 0x72,
	0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6a, 0x69, 0x6f, 0x6d, 0x61, 0x72, 0x74,
	0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x6f, 0x66, 0x74, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x6f, 0x66, 0x74, 0x12, 0x16,
	0x0a, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x61, 0x79, 0x74, 0x6d, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x61, 0x79, 0x74, 0x6d, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x68, 0x61, 0x61, 0x64, 0x69, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68,
	0x61, 0x61, 0x64, 0x69, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x6b, 0x79, 0x70, 0x65, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x6b, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x77,
	0x69, 0x67, 0x67, 0x79, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x77, 0x69, 0x67,
	0x67, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x6f, 0x69, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x74, 0x6f, 0x69, 0x12, 0x1a, 0x0a, 0x08, 0x77, 0x68, 0x61, 0x74, 0x73, 0x61, 0x70, 0x70,
	0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x77, 0x68, 0x61, 0x74, 0x73, 0x61, 0x70, 0x70,
	0x12, 0x14, 0x0a, 0x05, 0x79, 0x61, 0x74, 0x72, 0x61, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x79, 0x61, 0x74, 0x72, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x68, 0x6f, 0x18, 0x17,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x68, 0x6f, 0x22, 0xc9, 0x04, 0x0a, 0x15, 0x45,
	0x6d, 0x61, 0x69, 0x6c, 0x53, 0x6f, 0x63, 0x69, 0x61, 0x6c, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x73, 0x12, 0x4e, 0x0a, 0x10, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x72,
	0x69, 0x73, 0x6b, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x0f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x61, 0x7a, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6d, 0x61, 0x7a, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x16, 0x0a, 0x06,
	0x66, 0x6c, 0x69, 0x63, 0x6b, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x6c,
	0x69, 0x63, 0x6b, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x6c, 0x69, 0x70, 0x6b, 0x61, 0x72, 0x74,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x6c, 0x69, 0x70, 0x6b, 0x61, 0x72, 0x74,
	0x12, 0x18, 0x0a, 0x07, 0x68, 0x6f, 0x75, 0x73, 0x69, 0x6e, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x68, 0x6f, 0x75, 0x73, 0x69, 0x6e, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x12, 0x22, 0x0a, 0x0c, 0x6a, 0x65, 0x65, 0x76,
	0x61, 0x6e, 0x73, 0x61, 0x61, 0x74, 0x68, 0x69, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x6a, 0x65, 0x65, 0x76, 0x61, 0x6e, 0x73, 0x61, 0x61, 0x74, 0x68, 0x69, 0x12, 0x1c, 0x0a, 0x09,
	0x6d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x6f, 0x66, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x6f, 0x66, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x61,
	0x79, 0x74, 0x6d, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x61, 0x79, 0x74, 0x6d,
	0x12, 0x1c, 0x0a, 0x09, 0x70, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x71, 0x75, 0x6f, 0x72, 0x61, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x71,
	0x75, 0x6f, 0x72, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x61, 0x64, 0x69, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x61, 0x61, 0x64, 0x69, 0x12, 0x14, 0x0a, 0x05,
	0x73, 0x6b, 0x79, 0x70, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x6b, 0x79,
	0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x70, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x70, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x74, 0x6f, 0x69, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x6f, 0x69, 0x12, 0x1c,
	0x0a, 0x09, 0x77, 0x6f, 0x72, 0x64, 0x70, 0x72, 0x65, 0x73, 0x73, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x77, 0x6f, 0x72, 0x64, 0x70, 0x72, 0x65, 0x73, 0x73, 0x12, 0x14, 0x0a, 0x05,
	0x79, 0x61, 0x74, 0x72, 0x61, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x79, 0x61, 0x74,
	0x72, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x68, 0x6f, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x7a, 0x6f, 0x68, 0x6f, 0x22, 0xf9, 0x0a, 0x0a, 0x0e, 0x52, 0x69, 0x73, 0x6b, 0x41,
	0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x4e, 0x0a, 0x10, 0x61, 0x74, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x31, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x31, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x32, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x32, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x64, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x64, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x1b, 0x0a,
	0x09, 0x61, 0x72, 0x65, 0x61, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x61, 0x72, 0x65, 0x61, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x72,
	0x65, 0x61, 0x5f, 0x70, 0x69, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x61, 0x72, 0x65, 0x61, 0x50, 0x69, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a,
	0x0c, 0x73, 0x6f, 0x63, 0x69, 0x65, 0x74, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x6f, 0x63, 0x69, 0x65, 0x74, 0x79, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x72, 0x65, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x74, 0x72, 0x65, 0x65, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x69, 0x74, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a,
	0x10, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x49, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x69, 0x73, 0x6b, 0x12, 0x3c, 0x0a, 0x1a, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x18, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65,
	0x6e, 0x65, 0x73, 0x73, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x45, 0x6d, 0x61,
	0x69, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x5f, 0x66, 0x72, 0x61, 0x75, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x46, 0x72, 0x61, 0x75, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x5f, 0x66, 0x72, 0x61, 0x75, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x46, 0x72, 0x61, 0x75, 0x64, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2e, 0x0a, 0x13, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x66, 0x72,
	0x61, 0x75, 0x64, 0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x11, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x46, 0x72, 0x61, 0x75, 0x64, 0x4e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x12, 0x30, 0x0a, 0x14, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x72, 0x75, 0x73, 0x74, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x12, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x54, 0x72, 0x75, 0x73, 0x74, 0x12, 0x37, 0x0a, 0x18, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f,
	0x73, 0x6f, 0x63, 0x69, 0x61, 0x6c, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52, 0x15, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x53,
	0x6f, 0x63, 0x69, 0x61, 0x6c, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x2f, 0x0a, 0x13, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x69, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65,
	0x12, 0x21, 0x0a, 0x0c, 0x74, 0x65, 0x6c, 0x65, 0x63, 0x6f, 0x6d, 0x5f, 0x72, 0x69, 0x73, 0x6b,
	0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x65, 0x6c, 0x65, 0x63, 0x6f, 0x6d, 0x52,
	0x69, 0x73, 0x6b, 0x12, 0x2c, 0x0a, 0x12, 0x69, 0x73, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f,
	0x72, 0x65, 0x61, 0x63, 0x68, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x10, 0x69, 0x73, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x52, 0x65, 0x61, 0x63, 0x68, 0x61, 0x62, 0x6c,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x66, 0x72, 0x61, 0x75, 0x64,
	0x18, 0x17, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x46, 0x72, 0x61,
	0x75, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x66, 0x72, 0x61, 0x75,
	0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x18, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x46, 0x72, 0x61, 0x75, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2e,
	0x0a, 0x13, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x66, 0x72, 0x61, 0x75, 0x64, 0x5f, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x19, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x46, 0x72, 0x61, 0x75, 0x64, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x30,
	0x0a, 0x14, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x5f, 0x74, 0x72, 0x75, 0x73, 0x74, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x72, 0x75, 0x73, 0x74,
	0x12, 0x37, 0x0a, 0x18, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x6c,
	0x5f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x1b, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x15, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x53, 0x6f, 0x63, 0x69, 0x61, 0x6c, 0x4d,
	0x65, 0x64, 0x69, 0x61, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x6e, 0x65, 0x67,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x18, 0x1c,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x6e, 0x65, 0x67, 0x61, 0x74, 0x69, 0x76, 0x65, 0x49, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x18, 0x1d, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x10, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x49, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x12, 0x2f, 0x0a, 0x14, 0x75, 0x70, 0x69, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x18, 0x1e, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x11, 0x75, 0x70, 0x69, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x4d,
	0x61, 0x74, 0x63, 0x68, 0x12, 0x3a, 0x0a, 0x1a, 0x75, 0x70, 0x69, 0x5f, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x01, 0x52, 0x16, 0x75, 0x70, 0x69, 0x50, 0x68, 0x6f,
	0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x12, 0x30, 0x0a, 0x14, 0x61, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x65, 0x5f, 0x72, 0x69,
	0x73, 0x6b, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x20, 0x20, 0x01, 0x28, 0x01, 0x52, 0x12,
	0x61, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f,
	0x72, 0x65, 0x22, 0xe1, 0x17, 0x0a, 0x1d, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x65, 0x4f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x69, 0x73, 0x6b, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x72,
	0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x32, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12,
	0x58, 0x0a, 0x18, 0x69, 0x73, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x5f, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e,
	0x75, 0x6d, 0x52, 0x15, 0x69, 0x73, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x74, 0x12, 0x64, 0x0a, 0x1e, 0x63, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e,
	0x75, 0x6d, 0x52, 0x1b, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x12,
	0x4b, 0x0a, 0x11, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x65,
	0x6d, 0x69, 0x75, 0x6d, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0f, 0x69, 0x73, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x65, 0x6d, 0x69, 0x75, 0x6d, 0x12, 0x23, 0x0a, 0x03,
	0x64, 0x6f, 0x62, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x03, 0x64, 0x6f,
	0x62, 0x12, 0x3a, 0x0a, 0x1a, 0x67, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x70, 0x61, 0x6e, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x02, 0x52, 0x16, 0x67, 0x6d, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x6e, 0x4e,
	0x61, 0x6d, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x42, 0x0a,
	0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x5f, 0x6d, 0x61,
	0x69, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x4d, 0x61, 0x69, 0x6c, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x35, 0x0a, 0x0b, 0x67, 0x65, 0x6f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x52, 0x0b, 0x67, 0x65, 0x6f,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x68, 0x72, 0x65,
	0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x74, 0x68, 0x72,
	0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x4a, 0x0a, 0x11, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x18, 0x11, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x1d, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73,
	0x73, 0x2e, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x10, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x65, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x73, 0x18, 0x12, 0x20, 0x03, 0x28, 0x02, 0x52, 0x0e, 0x6c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x12, 0x36, 0x0a, 0x17, 0x6c,
	0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x5f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x18, 0x13, 0x20, 0x03, 0x28, 0x09, 0x52, 0x15, 0x6c, 0x69,
	0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x73, 0x12, 0x36, 0x0a, 0x17, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x18, 0x14,
	0x20, 0x03, 0x28, 0x02, 0x52, 0x15, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x6e,
	0x68, 0x6f, 0x75, 0x73, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x66,
	0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x18,
	0x15, 0x20, 0x03, 0x28, 0x02, 0x52, 0x0f, 0x66, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63, 0x68,
	0x53, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x12, 0x31, 0x0a, 0x0b, 0x63, 0x6b, 0x79, 0x63, 0x5f, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x73, 0x18, 0x16, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x6b, 0x79,
	0x63, 0x2e, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x63,
	0x6b, 0x79, 0x63, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x68, 0x61, 0x73,
	0x68, 0x65, 0x64, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x68, 0x61, 0x73, 0x68, 0x65, 0x64, 0x50, 0x68,
	0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x39, 0x0a, 0x0b, 0x66, 0x61, 0x74,
	0x68, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0a, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x0b, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e,
	0x61, 0x6d, 0x65, 0x52, 0x0a, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x66, 0x0a, 0x1f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6b,
	0x79, 0x63, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6d, 0x69, 0x73, 0x6d, 0x61, 0x74,
	0x63, 0x68, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f,
	0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x1c, 0x6f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x45, 0x6b, 0x79, 0x63, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4d,
	0x69, 0x73, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x12, 0x3c, 0x0a, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x70, 0x61, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x50, 0x61,
	0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x74, 0x70, 0x5f, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x73, 0x18, 0x1d, 0x20, 0x03, 0x28, 0x02, 0x52, 0x09, 0x6f, 0x74, 0x70, 0x53, 0x63,
	0x6f, 0x72, 0x65, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x69, 0x74,
	0x79, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x43, 0x69, 0x74,
	0x79, 0x12, 0x28, 0x0a, 0x10, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x75, 0x73, 0x65,
	0x72, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2a, 0x0a, 0x09, 0x6b,
	0x79, 0x63, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x20, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0d,
	0x2e, 0x6b, 0x79, 0x63, 0x2e, 0x4b, 0x59, 0x43, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x08, 0x6b,
	0x79, 0x63, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x45, 0x0a, 0x12, 0x6f, 0x76, 0x65, 0x72, 0x61,
	0x6c, 0x6c, 0x5f, 0x61, 0x66, 0x75, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x21, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x66, 0x75, 0x2e, 0x4f,
	0x76, 0x65, 0x72, 0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x10, 0x6f, 0x76,
	0x65, 0x72, 0x61, 0x6c, 0x6c, 0x41, 0x66, 0x75, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x37,
	0x0a, 0x0c, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x22,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x66, 0x75, 0x2e,
	0x41, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x0b, 0x61, 0x75, 0x74, 0x68,
	0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x12, 0x3e, 0x0a, 0x0e, 0x66, 0x61, 0x69, 0x6c, 0x75,
	0x72, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x23, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x17, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x66, 0x75, 0x2e, 0x46, 0x61, 0x69, 0x6c, 0x75,
	0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x0d, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72,
	0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x2b, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x18, 0x24, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e,
	0x61, 0x66, 0x75, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x07, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x61, 0x66, 0x75, 0x5f, 0x61, 0x74, 0x74, 0x65,
	0x6d, 0x70, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x25, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61,
	0x66, 0x75, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x4e, 0x75, 0x6d, 0x12, 0x2c, 0x0a, 0x12,
	0x61, 0x66, 0x75, 0x5f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x18, 0x26, 0x20, 0x03, 0x28, 0x02, 0x52, 0x10, 0x61, 0x66, 0x75, 0x4c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x61, 0x66,
	0x75, 0x5f, 0x6f, 0x74, 0x70, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x27, 0x20, 0x03, 0x28,
	0x02, 0x52, 0x0b, 0x61, 0x66, 0x75, 0x4f, 0x74, 0x70, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x2e,
	0x0a, 0x13, 0x61, 0x66, 0x75, 0x5f, 0x66, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f,
	0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x28, 0x20, 0x03, 0x28, 0x02, 0x52, 0x11, 0x61, 0x66, 0x75,
	0x46, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x29, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x3c, 0x0a,
	0x0f, 0x61, 0x66, 0x75, 0x5f, 0x67, 0x65, 0x6f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x2a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x52, 0x0e, 0x61, 0x66, 0x75,
	0x47, 0x65, 0x6f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x36, 0x0a, 0x17, 0x6f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x2b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x6f, 0x6e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x49, 0x0a, 0x0a, 0x63, 0x62, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x2c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x73, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x09, 0x63, 0x62, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x51,
	0x0a, 0x15, 0x61, 0x66, 0x75, 0x5f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x18, 0x2d, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1d, 0x2e,
	0x61, 0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x4c, 0x69,
	0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x13, 0x61, 0x66,
	0x75, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65,
	0x73, 0x12, 0x43, 0x0a, 0x0f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x2e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x65, 0x6d, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0e, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3b, 0x0a, 0x0d, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x2f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0c, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x41, 0x0a, 0x0c, 0x61, 0x66, 0x75, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d,
	0x70, 0x74, 0x73, 0x18, 0x30, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x41,
	0x46, 0x55, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x52, 0x0b, 0x61, 0x66, 0x75, 0x41, 0x74,
	0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x12, 0x42, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x31, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x27, 0x0a, 0x07, 0x70, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x32, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x12, 0x88, 0x01, 0x0a, 0x19, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x6d, 0x61,
	0x70, 0x18, 0x33, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x44, 0x65, 0x74,
	0x65, 0x63, 0x74, 0x52, 0x65, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x52,
	0x69, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4d, 0x61,
	0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x16, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4d, 0x61, 0x70, 0x12, 0x54,
	0x0a, 0x12, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x18, 0x34, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x11, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x6f, 0x6c, 0x64, 0x5f, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x18, 0x35, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x52, 0x09, 0x6f, 0x6c, 0x64, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x1a,
	0x65, 0x0a, 0x1b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x30, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x50, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xae, 0x02, 0x0a, 0x1e, 0x44, 0x65, 0x74, 0x65, 0x63,
	0x74, 0x52, 0x65, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x69, 0x73,
	0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14,
	0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f,
	0x6c, 0x64, 0x12, 0x3e, 0x0a, 0x0a, 0x72, 0x69, 0x73, 0x6b, 0x79, 0x5f, 0x75, 0x73, 0x65, 0x72,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c,
	0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x09, 0x72, 0x69, 0x73, 0x6b, 0x79, 0x55, 0x73,
	0x65, 0x72, 0x12, 0x2d, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x04, 0x74, 0x69, 0x6d,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x61, 0x77, 0x5f, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x61, 0x77, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x8c, 0x10, 0x0a, 0x11, 0x44, 0x65, 0x74, 0x65,
	0x63, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a,
	0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a,
	0x11, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x72, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72,
	0x65, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x06, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x19, 0x0a,
	0x08, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x58, 0x0a, 0x18, 0x69, 0x73, 0x5f, 0x63,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x70, 0x72, 0x65,
	0x73, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x15, 0x69, 0x73, 0x43,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65,
	0x6e, 0x74, 0x12, 0x64, 0x0a, 0x1e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x63, 0x6f, 0x6e,
	0x73, 0x65, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x1b, 0x63, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61,
	0x64, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x12, 0x4b, 0x0a, 0x11, 0x69, 0x73, 0x5f, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x65, 0x6d, 0x69, 0x75, 0x6d, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e,
	0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0f, 0x69, 0x73, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72,
	0x65, 0x6d, 0x69, 0x75, 0x6d, 0x12, 0x23, 0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x03, 0x64, 0x6f, 0x62, 0x12, 0x3a, 0x0a, 0x1a, 0x67, 0x6d,
	0x61, 0x69, 0x6c, 0x5f, 0x70, 0x61, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6d, 0x61, 0x74,
	0x63, 0x68, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x02, 0x52, 0x16,
	0x67, 0x6d, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x74, 0x63,
	0x68, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0b, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x5f, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65,
	0x72, 0x4d, 0x61, 0x69, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x35, 0x0a, 0x0b, 0x67, 0x65,
	0x6f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61,
	0x74, 0x4c, 0x6e, 0x67, 0x52, 0x0b, 0x67, 0x65, 0x6f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12,
	0x4a, 0x0a, 0x11, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x65, 0x73, 0x18, 0x11, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x61, 0x75, 0x74,
	0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x4c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x10, 0x6c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x6c,
	0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x18, 0x12,
	0x20, 0x03, 0x28, 0x02, 0x52, 0x0e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x63,
	0x6f, 0x72, 0x65, 0x73, 0x12, 0x36, 0x0a, 0x17, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x18,
	0x13, 0x20, 0x03, 0x28, 0x09, 0x52, 0x15, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x49,
	0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x12, 0x36, 0x0a, 0x17,
	0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65,
	0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x18, 0x14, 0x20, 0x03, 0x28, 0x02, 0x52, 0x15, 0x6c,
	0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x53, 0x63,
	0x6f, 0x72, 0x65, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x66, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63,
	0x68, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x18, 0x15, 0x20, 0x03, 0x28, 0x02, 0x52, 0x0f,
	0x66, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x12,
	0x31, 0x0a, 0x0b, 0x63, 0x6b, 0x79, 0x63, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x18, 0x16,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x6b, 0x79, 0x63, 0x2e, 0x46, 0x61, 0x69, 0x6c, 0x75,
	0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x63, 0x6b, 0x79, 0x63, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x68, 0x61, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x11, 0x68, 0x61, 0x73, 0x68, 0x65, 0x64, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x39, 0x0a, 0x0b, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d,
	0x65, 0x52, 0x0a, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a,
	0x0b, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x1a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0a, 0x6d, 0x6f,
	0x74, 0x68, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x66, 0x0a, 0x1f, 0x6f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6b, 0x79, 0x63, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x5f, 0x6d, 0x69, 0x73, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x18, 0x1b, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e,
	0x75, 0x6d, 0x52, 0x1c, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x45, 0x6b,
	0x79, 0x63, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4d, 0x69, 0x73, 0x6d, 0x61, 0x74, 0x63, 0x68,
	0x12, 0x3c, 0x0a, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x6e, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d,
	0x65, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x50, 0x61, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x6f, 0x74, 0x70, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x18, 0x1d, 0x20, 0x03,
	0x28, 0x02, 0x52, 0x09, 0x6f, 0x74, 0x70, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x12, 0x49, 0x0a,
	0x0a, 0x63, 0x62, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x1e, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2a, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e, 0x68, 0x6f,
	0x75, 0x73, 0x65, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x63,
	0x62, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x43, 0x0a, 0x0f, 0x65, 0x6d, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x1f, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x45,
	0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0e, 0x65,
	0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x7c, 0x0a,
	0x19, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x20, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x41, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x69, 0x73, 0x6b,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4d, 0x61, 0x70, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x16, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4d, 0x61, 0x70, 0x12, 0x57, 0x0a, 0x0d, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x21, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x32, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x69,
	0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x1a, 0x65, 0x0a, 0x1b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4d, 0x61, 0x70, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x30, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x59, 0x0a, 0x0c, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x19, 0x4d,
	0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x4d, 0x4f,
	0x44, 0x45, 0x4c, 0x5f, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x56, 0x30, 0x10, 0x01,
	0x12, 0x14, 0x0a, 0x10, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f,
	0x4e, 0x5f, 0x56, 0x31, 0x10, 0x02, 0x22, 0xa2, 0x02, 0x0a, 0x12, 0x44, 0x65, 0x74, 0x65, 0x63,
	0x74, 0x52, 0x69, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x68, 0x72, 0x65,
	0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x74, 0x68, 0x72,
	0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x3e, 0x0a, 0x0a, 0x72, 0x69, 0x73, 0x6b, 0x79, 0x5f,
	0x75, 0x73, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x09, 0x72, 0x69, 0x73,
	0x6b, 0x79, 0x55, 0x73, 0x65, 0x72, 0x12, 0x2d, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x2e, 0x0a, 0x13, 0x72,
	0x61, 0x77, 0x5f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x61, 0x77, 0x56, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x82, 0x02, 0x0a, 0x19,
	0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x69,
	0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3e, 0x0a, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x26, 0x0a,
	0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x07, 0x6c, 0x61, 0x74, 0x5f, 0x6c, 0x6e, 0x67,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x6c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x12, 0x21, 0x0a,
	0x07, 0x70, 0x69, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x70, 0x69, 0x6e, 0x63, 0x6f, 0x64, 0x65,
	0x22, 0xb8, 0x02, 0x0a, 0x1a, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x45, 0x0a, 0x0d, 0x72, 0x69,
	0x73, 0x6b, 0x5f, 0x73, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x20, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x65, 0x76, 0x65, 0x72,
	0x69, 0x74, 0x79, 0x52, 0x0c, 0x72, 0x69, 0x73, 0x6b, 0x53, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74,
	0x79, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x61, 0x77, 0x5f, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x61, 0x77, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x43, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41,
	0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x0d,
	0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a,
	0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0xff, 0x02, 0x0a, 0x0a,
	0x41, 0x46, 0x55, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x12, 0x3e, 0x0a, 0x0e, 0x6f, 0x76,
	0x65, 0x72, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x66, 0x75, 0x2e, 0x4f, 0x76,
	0x65, 0x72, 0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x6f, 0x76, 0x65,
	0x72, 0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x37, 0x0a, 0x0c, 0x61, 0x75,
	0x74, 0x68, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e,
	0x32, 0x14, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x66, 0x75, 0x2e, 0x41, 0x75, 0x74, 0x68,
	0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x0b, 0x61, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x73, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x02, 0x52, 0x0e, 0x6c, 0x69,
	0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x12, 0x1d, 0x0a, 0x0a,
	0x6f, 0x74, 0x70, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x02,
	0x52, 0x09, 0x6f, 0x74, 0x70, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x66,
	0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x02, 0x52, 0x0f, 0x66, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63, 0x68,
	0x53, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x4a, 0x0a, 0x11, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x61,
	0x75, 0x74, 0x68, 0x2e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x4c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x10, 0x6c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x22, 0x74, 0x0a,
	0x0b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2a, 0x0a, 0x04,
	0x74, 0x69, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x69, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x54, 0x69,
	0x65, 0x72, 0x52, 0x04, 0x74, 0x69, 0x65, 0x72, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x22, 0x52, 0x0a, 0x11, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3d, 0x0a, 0x1b, 0x6f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x18, 0x6f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x69, 0x73, 0x6b, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x2a, 0x64, 0x0a, 0x07, 0x4f, 0x75, 0x74, 0x63, 0x6f,
	0x6d, 0x65, 0x12, 0x17, 0x0a, 0x13, 0x4f, 0x55, 0x54, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x4f,
	0x55, 0x54, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x45, 0x44, 0x10,
	0x01, 0x12, 0x14, 0x0a, 0x10, 0x4f, 0x55, 0x54, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x41, 0x57, 0x41,
	0x49, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x4f, 0x55, 0x54, 0x43, 0x4f,
	0x4d, 0x45, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x03, 0x2a, 0x8a, 0x01,
	0x0a, 0x0f, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x17, 0x0a, 0x13, 0x41, 0x54, 0x54, 0x52, 0x49, 0x42, 0x55, 0x54, 0x45, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x54,
	0x54, 0x52, 0x49, 0x42, 0x55, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46,
	0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x41, 0x54, 0x54, 0x52, 0x49,
	0x42, 0x55, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f,
	0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x02, 0x12, 0x21, 0x0a, 0x1d, 0x41, 0x54, 0x54, 0x52, 0x49,
	0x42, 0x55, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f,
	0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x45, 0x44, 0x10, 0x03, 0x32, 0xe8, 0x04, 0x0a, 0x04, 0x52,
	0x69, 0x73, 0x6b, 0x12, 0x5b, 0x0a, 0x0a, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x69, 0x73,
	0x6b, 0x12, 0x25, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x69, 0x73,
	0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x44, 0x65,
	0x74, 0x65, 0x63, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x7f, 0x0a, 0x16, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x65, 0x4f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x69, 0x73, 0x6b, 0x12, 0x31, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x65, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x52, 0x69, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x65, 0x4f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x69, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x73, 0x0a, 0x12, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x12, 0x2d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x44, 0x65, 0x74,
	0x65, 0x63, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x44, 0x65, 0x74, 0x65,
	0x63, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7f, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x42, 0x75, 0x72,
	0x65, 0x61, 0x75, 0x49, 0x64, 0x52, 0x69, 0x73, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x31, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x75, 0x72, 0x65, 0x61, 0x75, 0x49,
	0x64, 0x52, 0x69, 0x73, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x75, 0x72, 0x65,
	0x61, 0x75, 0x49, 0x64, 0x52, 0x69, 0x73, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8b, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x43,
	0x61, 0x73, 0x65, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x35, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x47, 0x65, 0x74, 0x43,
	0x61, 0x73, 0x65, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x73, 0x65, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69,
	0x74, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x5e, 0x0a, 0x2d, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5a, 0x2d, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2f, 0x72, 0x69, 0x73, 0x6b, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendorgateway_risk_service_proto_rawDescOnce sync.Once
	file_api_vendorgateway_risk_service_proto_rawDescData = file_api_vendorgateway_risk_service_proto_rawDesc
)

func file_api_vendorgateway_risk_service_proto_rawDescGZIP() []byte {
	file_api_vendorgateway_risk_service_proto_rawDescOnce.Do(func() {
		file_api_vendorgateway_risk_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendorgateway_risk_service_proto_rawDescData)
	})
	return file_api_vendorgateway_risk_service_proto_rawDescData
}

var file_api_vendorgateway_risk_service_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_api_vendorgateway_risk_service_proto_msgTypes = make([]protoimpl.MessageInfo, 26)
var file_api_vendorgateway_risk_service_proto_goTypes = []interface{}{
	(Outcome)(0),                                          // 0: vendorgateway.risk.Outcome
	(AttributeStatus)(0),                                  // 1: vendorgateway.risk.AttributeStatus
	(DetectRiskRequest_ModelVersion)(0),                   // 2: vendorgateway.risk.DetectRiskRequest.ModelVersion
	(DetectLocationRiskResponse_Status)(0),                // 3: vendorgateway.risk.DetectLocationRiskResponse.Status
	(*GetCasePrioritisationScoreRequest)(nil),             // 4: vendorgateway.risk.GetCasePrioritisationScoreRequest
	(*ModelResponseInfo)(nil),                             // 5: vendorgateway.risk.ModelResponseInfo
	(*GetCasePrioritisationScoreResponse)(nil),            // 6: vendorgateway.risk.GetCasePrioritisationScoreResponse
	(*GetBureauIdRiskDetailsRequest)(nil),                 // 7: vendorgateway.risk.GetBureauIdRiskDetailsRequest
	(*UserData)(nil),                                      // 8: vendorgateway.risk.UserData
	(*GetBureauIdRiskDetailsResponse)(nil),                // 9: vendorgateway.risk.GetBureauIdRiskDetailsResponse
	(*EmailAttributes)(nil),                               // 10: vendorgateway.risk.EmailAttributes
	(*EmailNameAttributes)(nil),                           // 11: vendorgateway.risk.EmailNameAttributes
	(*PhoneAttributes)(nil),                               // 12: vendorgateway.risk.PhoneAttributes
	(*PhoneNameAttributes)(nil),                           // 13: vendorgateway.risk.PhoneNameAttributes
	(*PhoneNetworkAttributes)(nil),                        // 14: vendorgateway.risk.PhoneNetworkAttributes
	(*PhoneSocialAttributes)(nil),                         // 15: vendorgateway.risk.PhoneSocialAttributes
	(*EmailSocialAttributes)(nil),                         // 16: vendorgateway.risk.EmailSocialAttributes
	(*RiskAttributes)(nil),                                // 17: vendorgateway.risk.RiskAttributes
	(*DetectReOnboardingRiskRequest)(nil),                 // 18: vendorgateway.risk.DetectReOnboardingRiskRequest
	(*DetectReOnboardingRiskResponse)(nil),                // 19: vendorgateway.risk.DetectReOnboardingRiskResponse
	(*DetectRiskRequest)(nil),                             // 20: vendorgateway.risk.DetectRiskRequest
	(*DetectRiskResponse)(nil),                            // 21: vendorgateway.risk.DetectRiskResponse
	(*DetectLocationRiskRequest)(nil),                     // 22: vendorgateway.risk.DetectLocationRiskRequest
	(*DetectLocationRiskResponse)(nil),                    // 23: vendorgateway.risk.DetectLocationRiskResponse
	(*AFUAttempt)(nil),                                    // 24: vendorgateway.risk.AFUAttempt
	(*AccountInfo)(nil),                                   // 25: vendorgateway.risk.AccountInfo
	(*OnboardingDetails)(nil),                             // 26: vendorgateway.risk.OnboardingDetails
	(*GetCasePrioritisationScoreRequest_CaseDetails)(nil), // 27: vendorgateway.risk.GetCasePrioritisationScoreRequest.CaseDetails
	nil,                                 // 28: vendorgateway.risk.DetectReOnboardingRiskRequest.DevicePropertyValueMapEntry
	nil,                                 // 29: vendorgateway.risk.DetectRiskRequest.DevicePropertyValueMapEntry
	(*vendorgateway.RequestHeader)(nil), // 30: vendorgateway.RequestHeader
	(*case_management.AlertWithRuleDetails)(nil), // 31: risk.case_management.AlertWithRuleDetails
	(*rpc.Status)(nil),                           // 32: rpc.Status
	(*common.PhoneNumber)(nil),                   // 33: api.typesv2.common.PhoneNumber
	(*common.Device)(nil),                        // 34: api.typesv2.common.Device
	(common.BooleanEnum)(0),                      // 35: api.typesv2.common.BooleanEnum
	(*date.Date)(nil),                            // 36: google.type.Date
	(*latlng.LatLng)(nil),                        // 37: google.type.LatLng
	(liveness.LivenessStatus)(0),                 // 38: auth.liveness.LivenessStatus
	(kyc.FailureType)(0),                         // 39: kyc.FailureType
	(*common.Name)(nil),                          // 40: api.typesv2.common.Name
	(kyc.KYCLevel)(0),                            // 41: kyc.KYCLevel
	(afu.OverallStatus)(0),                       // 42: auth.afu.OverallStatus
	(afu.AuthFactor)(0),                          // 43: auth.afu.AuthFactor
	(afu.FailureReason)(0),                       // 44: auth.afu.FailureReason
	(*afu.Context)(nil),                          // 45: auth.afu.Context
	(*inhouse.CreditReportAttributeInfo)(nil),    // 46: vendors.inhouse.CreditReportAttributeInfo
	(*employment.EmploymentData)(nil),            // 47: employment.EmploymentData
	(*screener.CheckDetails)(nil),                // 48: screener.CheckDetails
	(*user.Profile)(nil),                         // 49: user.Profile
	(*durationpb.Duration)(nil),                  // 50: google.protobuf.Duration
	(RiskSeverity)(0),                            // 51: vendorgateway.risk.RiskSeverity
	(*timestamppb.Timestamp)(nil),                // 52: google.protobuf.Timestamp
	(external.Tier)(0),                           // 53: tiering.external.Tier
	(*typesv2.PropertyValue)(nil),                // 54: api.typesv2.PropertyValue
}
var file_api_vendorgateway_risk_service_proto_depIdxs = []int32{
	30, // 0: vendorgateway.risk.GetCasePrioritisationScoreRequest.header:type_name -> vendorgateway.RequestHeader
	31, // 1: vendorgateway.risk.GetCasePrioritisationScoreRequest.alert_with_rule_details:type_name -> risk.case_management.AlertWithRuleDetails
	27, // 2: vendorgateway.risk.GetCasePrioritisationScoreRequest.case_details:type_name -> vendorgateway.risk.GetCasePrioritisationScoreRequest.CaseDetails
	32, // 3: vendorgateway.risk.GetCasePrioritisationScoreResponse.status:type_name -> rpc.Status
	5,  // 4: vendorgateway.risk.GetCasePrioritisationScoreResponse.model_info:type_name -> vendorgateway.risk.ModelResponseInfo
	30, // 5: vendorgateway.risk.GetBureauIdRiskDetailsRequest.header:type_name -> vendorgateway.RequestHeader
	8,  // 6: vendorgateway.risk.GetBureauIdRiskDetailsRequest.user_data:type_name -> vendorgateway.risk.UserData
	33, // 7: vendorgateway.risk.UserData.phone_number:type_name -> api.typesv2.common.PhoneNumber
	32, // 8: vendorgateway.risk.GetBureauIdRiskDetailsResponse.status:type_name -> rpc.Status
	0,  // 9: vendorgateway.risk.GetBureauIdRiskDetailsResponse.outcome:type_name -> vendorgateway.risk.Outcome
	10, // 10: vendorgateway.risk.GetBureauIdRiskDetailsResponse.email_attributes:type_name -> vendorgateway.risk.EmailAttributes
	11, // 11: vendorgateway.risk.GetBureauIdRiskDetailsResponse.email_name_attributes:type_name -> vendorgateway.risk.EmailNameAttributes
	12, // 12: vendorgateway.risk.GetBureauIdRiskDetailsResponse.phone_attributes:type_name -> vendorgateway.risk.PhoneAttributes
	13, // 13: vendorgateway.risk.GetBureauIdRiskDetailsResponse.phone_name_attributes:type_name -> vendorgateway.risk.PhoneNameAttributes
	14, // 14: vendorgateway.risk.GetBureauIdRiskDetailsResponse.phone_network_attributes:type_name -> vendorgateway.risk.PhoneNetworkAttributes
	16, // 15: vendorgateway.risk.GetBureauIdRiskDetailsResponse.email_social_attributes:type_name -> vendorgateway.risk.EmailSocialAttributes
	15, // 16: vendorgateway.risk.GetBureauIdRiskDetailsResponse.phone_social_attributes:type_name -> vendorgateway.risk.PhoneSocialAttributes
	17, // 17: vendorgateway.risk.GetBureauIdRiskDetailsResponse.risk_attributes:type_name -> vendorgateway.risk.RiskAttributes
	1,  // 18: vendorgateway.risk.EmailAttributes.attribute_status:type_name -> vendorgateway.risk.AttributeStatus
	1,  // 19: vendorgateway.risk.EmailNameAttributes.attribute_status:type_name -> vendorgateway.risk.AttributeStatus
	1,  // 20: vendorgateway.risk.PhoneAttributes.attribute_status:type_name -> vendorgateway.risk.AttributeStatus
	1,  // 21: vendorgateway.risk.PhoneNameAttributes.attribute_status:type_name -> vendorgateway.risk.AttributeStatus
	1,  // 22: vendorgateway.risk.PhoneNetworkAttributes.attribute_status:type_name -> vendorgateway.risk.AttributeStatus
	1,  // 23: vendorgateway.risk.PhoneSocialAttributes.attribute_status:type_name -> vendorgateway.risk.AttributeStatus
	1,  // 24: vendorgateway.risk.EmailSocialAttributes.attribute_status:type_name -> vendorgateway.risk.AttributeStatus
	1,  // 25: vendorgateway.risk.RiskAttributes.attribute_status:type_name -> vendorgateway.risk.AttributeStatus
	30, // 26: vendorgateway.risk.DetectReOnboardingRiskRequest.header:type_name -> vendorgateway.RequestHeader
	34, // 27: vendorgateway.risk.DetectReOnboardingRiskRequest.device:type_name -> api.typesv2.common.Device
	35, // 28: vendorgateway.risk.DetectReOnboardingRiskRequest.is_credit_report_present:type_name -> api.typesv2.common.BooleanEnum
	35, // 29: vendorgateway.risk.DetectReOnboardingRiskRequest.credit_report_download_consent:type_name -> api.typesv2.common.BooleanEnum
	35, // 30: vendorgateway.risk.DetectReOnboardingRiskRequest.is_device_premium:type_name -> api.typesv2.common.BooleanEnum
	36, // 31: vendorgateway.risk.DetectReOnboardingRiskRequest.dob:type_name -> google.type.Date
	33, // 32: vendorgateway.risk.DetectReOnboardingRiskRequest.phone_number:type_name -> api.typesv2.common.PhoneNumber
	37, // 33: vendorgateway.risk.DetectReOnboardingRiskRequest.geolocation:type_name -> google.type.LatLng
	38, // 34: vendorgateway.risk.DetectReOnboardingRiskRequest.liveness_statuses:type_name -> auth.liveness.LivenessStatus
	39, // 35: vendorgateway.risk.DetectReOnboardingRiskRequest.ckyc_errors:type_name -> kyc.FailureType
	40, // 36: vendorgateway.risk.DetectReOnboardingRiskRequest.father_name:type_name -> api.typesv2.common.Name
	40, // 37: vendorgateway.risk.DetectReOnboardingRiskRequest.mother_name:type_name -> api.typesv2.common.Name
	35, // 38: vendorgateway.risk.DetectReOnboardingRiskRequest.onboarding_ekyc_number_mismatch:type_name -> api.typesv2.common.BooleanEnum
	40, // 39: vendorgateway.risk.DetectReOnboardingRiskRequest.user_pan_name:type_name -> api.typesv2.common.Name
	41, // 40: vendorgateway.risk.DetectReOnboardingRiskRequest.kyc_level:type_name -> kyc.KYCLevel
	42, // 41: vendorgateway.risk.DetectReOnboardingRiskRequest.overall_afu_status:type_name -> auth.afu.OverallStatus
	43, // 42: vendorgateway.risk.DetectReOnboardingRiskRequest.auth_factors:type_name -> auth.afu.AuthFactor
	44, // 43: vendorgateway.risk.DetectReOnboardingRiskRequest.failure_reason:type_name -> auth.afu.FailureReason
	45, // 44: vendorgateway.risk.DetectReOnboardingRiskRequest.context:type_name -> auth.afu.Context
	37, // 45: vendorgateway.risk.DetectReOnboardingRiskRequest.afu_geolocation:type_name -> google.type.LatLng
	46, // 46: vendorgateway.risk.DetectReOnboardingRiskRequest.cb_details:type_name -> vendors.inhouse.CreditReportAttributeInfo
	38, // 47: vendorgateway.risk.DetectReOnboardingRiskRequest.afu_liveness_statuses:type_name -> auth.liveness.LivenessStatus
	47, // 48: vendorgateway.risk.DetectReOnboardingRiskRequest.employment_data:type_name -> employment.EmploymentData
	48, // 49: vendorgateway.risk.DetectReOnboardingRiskRequest.check_details:type_name -> screener.CheckDetails
	24, // 50: vendorgateway.risk.DetectReOnboardingRiskRequest.afu_attempts:type_name -> vendorgateway.risk.AFUAttempt
	25, // 51: vendorgateway.risk.DetectReOnboardingRiskRequest.account_info:type_name -> vendorgateway.risk.AccountInfo
	49, // 52: vendorgateway.risk.DetectReOnboardingRiskRequest.profile:type_name -> user.Profile
	28, // 53: vendorgateway.risk.DetectReOnboardingRiskRequest.device_property_value_map:type_name -> vendorgateway.risk.DetectReOnboardingRiskRequest.DevicePropertyValueMapEntry
	26, // 54: vendorgateway.risk.DetectReOnboardingRiskRequest.onboarding_details:type_name -> vendorgateway.risk.OnboardingDetails
	34, // 55: vendorgateway.risk.DetectReOnboardingRiskRequest.old_device:type_name -> api.typesv2.common.Device
	32, // 56: vendorgateway.risk.DetectReOnboardingRiskResponse.status:type_name -> rpc.Status
	35, // 57: vendorgateway.risk.DetectReOnboardingRiskResponse.risky_user:type_name -> api.typesv2.common.BooleanEnum
	50, // 58: vendorgateway.risk.DetectReOnboardingRiskResponse.time:type_name -> google.protobuf.Duration
	30, // 59: vendorgateway.risk.DetectRiskRequest.header:type_name -> vendorgateway.RequestHeader
	34, // 60: vendorgateway.risk.DetectRiskRequest.device:type_name -> api.typesv2.common.Device
	35, // 61: vendorgateway.risk.DetectRiskRequest.is_credit_report_present:type_name -> api.typesv2.common.BooleanEnum
	35, // 62: vendorgateway.risk.DetectRiskRequest.credit_report_download_consent:type_name -> api.typesv2.common.BooleanEnum
	35, // 63: vendorgateway.risk.DetectRiskRequest.is_device_premium:type_name -> api.typesv2.common.BooleanEnum
	36, // 64: vendorgateway.risk.DetectRiskRequest.dob:type_name -> google.type.Date
	33, // 65: vendorgateway.risk.DetectRiskRequest.phone_number:type_name -> api.typesv2.common.PhoneNumber
	37, // 66: vendorgateway.risk.DetectRiskRequest.geolocation:type_name -> google.type.LatLng
	38, // 67: vendorgateway.risk.DetectRiskRequest.liveness_statuses:type_name -> auth.liveness.LivenessStatus
	39, // 68: vendorgateway.risk.DetectRiskRequest.ckyc_errors:type_name -> kyc.FailureType
	40, // 69: vendorgateway.risk.DetectRiskRequest.father_name:type_name -> api.typesv2.common.Name
	40, // 70: vendorgateway.risk.DetectRiskRequest.mother_name:type_name -> api.typesv2.common.Name
	35, // 71: vendorgateway.risk.DetectRiskRequest.onboarding_ekyc_number_mismatch:type_name -> api.typesv2.common.BooleanEnum
	40, // 72: vendorgateway.risk.DetectRiskRequest.user_pan_name:type_name -> api.typesv2.common.Name
	46, // 73: vendorgateway.risk.DetectRiskRequest.cb_details:type_name -> vendors.inhouse.CreditReportAttributeInfo
	47, // 74: vendorgateway.risk.DetectRiskRequest.employment_data:type_name -> employment.EmploymentData
	29, // 75: vendorgateway.risk.DetectRiskRequest.device_property_value_map:type_name -> vendorgateway.risk.DetectRiskRequest.DevicePropertyValueMapEntry
	2,  // 76: vendorgateway.risk.DetectRiskRequest.model_version:type_name -> vendorgateway.risk.DetectRiskRequest.ModelVersion
	32, // 77: vendorgateway.risk.DetectRiskResponse.status:type_name -> rpc.Status
	35, // 78: vendorgateway.risk.DetectRiskResponse.risky_user:type_name -> api.typesv2.common.BooleanEnum
	50, // 79: vendorgateway.risk.DetectRiskResponse.time:type_name -> google.protobuf.Duration
	30, // 80: vendorgateway.risk.DetectLocationRiskRequest.header:type_name -> vendorgateway.RequestHeader
	37, // 81: vendorgateway.risk.DetectLocationRiskRequest.lat_lng:type_name -> google.type.LatLng
	32, // 82: vendorgateway.risk.DetectLocationRiskResponse.status:type_name -> rpc.Status
	51, // 83: vendorgateway.risk.DetectLocationRiskResponse.risk_severity:type_name -> vendorgateway.risk.RiskSeverity
	42, // 84: vendorgateway.risk.AFUAttempt.overall_status:type_name -> auth.afu.OverallStatus
	43, // 85: vendorgateway.risk.AFUAttempt.auth_factors:type_name -> auth.afu.AuthFactor
	52, // 86: vendorgateway.risk.AFUAttempt.created_at:type_name -> google.protobuf.Timestamp
	38, // 87: vendorgateway.risk.AFUAttempt.liveness_statuses:type_name -> auth.liveness.LivenessStatus
	53, // 88: vendorgateway.risk.AccountInfo.tier:type_name -> tiering.external.Tier
	52, // 89: vendorgateway.risk.AccountInfo.created_at:type_name -> google.protobuf.Timestamp
	31, // 90: vendorgateway.risk.GetCasePrioritisationScoreRequest.CaseDetails.alert_with_rule_details:type_name -> risk.case_management.AlertWithRuleDetails
	54, // 91: vendorgateway.risk.DetectReOnboardingRiskRequest.DevicePropertyValueMapEntry.value:type_name -> api.typesv2.PropertyValue
	54, // 92: vendorgateway.risk.DetectRiskRequest.DevicePropertyValueMapEntry.value:type_name -> api.typesv2.PropertyValue
	20, // 93: vendorgateway.risk.Risk.DetectRisk:input_type -> vendorgateway.risk.DetectRiskRequest
	18, // 94: vendorgateway.risk.Risk.DetectReOnboardingRisk:input_type -> vendorgateway.risk.DetectReOnboardingRiskRequest
	22, // 95: vendorgateway.risk.Risk.DetectLocationRisk:input_type -> vendorgateway.risk.DetectLocationRiskRequest
	7,  // 96: vendorgateway.risk.Risk.GetBureauIdRiskDetails:input_type -> vendorgateway.risk.GetBureauIdRiskDetailsRequest
	4,  // 97: vendorgateway.risk.Risk.GetCasePrioritisationScore:input_type -> vendorgateway.risk.GetCasePrioritisationScoreRequest
	21, // 98: vendorgateway.risk.Risk.DetectRisk:output_type -> vendorgateway.risk.DetectRiskResponse
	19, // 99: vendorgateway.risk.Risk.DetectReOnboardingRisk:output_type -> vendorgateway.risk.DetectReOnboardingRiskResponse
	23, // 100: vendorgateway.risk.Risk.DetectLocationRisk:output_type -> vendorgateway.risk.DetectLocationRiskResponse
	9,  // 101: vendorgateway.risk.Risk.GetBureauIdRiskDetails:output_type -> vendorgateway.risk.GetBureauIdRiskDetailsResponse
	6,  // 102: vendorgateway.risk.Risk.GetCasePrioritisationScore:output_type -> vendorgateway.risk.GetCasePrioritisationScoreResponse
	98, // [98:103] is the sub-list for method output_type
	93, // [93:98] is the sub-list for method input_type
	93, // [93:93] is the sub-list for extension type_name
	93, // [93:93] is the sub-list for extension extendee
	0,  // [0:93] is the sub-list for field type_name
}

func init() { file_api_vendorgateway_risk_service_proto_init() }
func file_api_vendorgateway_risk_service_proto_init() {
	if File_api_vendorgateway_risk_service_proto != nil {
		return
	}
	file_api_vendorgateway_risk_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_vendorgateway_risk_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCasePrioritisationScoreRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_risk_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelResponseInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_risk_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCasePrioritisationScoreResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_risk_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBureauIdRiskDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_risk_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_risk_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBureauIdRiskDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_risk_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmailAttributes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_risk_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmailNameAttributes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_risk_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PhoneAttributes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_risk_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PhoneNameAttributes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_risk_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PhoneNetworkAttributes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_risk_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PhoneSocialAttributes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_risk_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmailSocialAttributes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_risk_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskAttributes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_risk_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DetectReOnboardingRiskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_risk_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DetectReOnboardingRiskResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_risk_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DetectRiskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_risk_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DetectRiskResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_risk_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DetectLocationRiskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_risk_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DetectLocationRiskResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_risk_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AFUAttempt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_risk_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_risk_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OnboardingDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_risk_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCasePrioritisationScoreRequest_CaseDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendorgateway_risk_service_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   26,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_vendorgateway_risk_service_proto_goTypes,
		DependencyIndexes: file_api_vendorgateway_risk_service_proto_depIdxs,
		EnumInfos:         file_api_vendorgateway_risk_service_proto_enumTypes,
		MessageInfos:      file_api_vendorgateway_risk_service_proto_msgTypes,
	}.Build()
	File_api_vendorgateway_risk_service_proto = out.File
	file_api_vendorgateway_risk_service_proto_rawDesc = nil
	file_api_vendorgateway_risk_service_proto_goTypes = nil
	file_api_vendorgateway_risk_service_proto_depIdxs = nil
}
