syntax = "proto3";

package vendorgateway.openbanking.dispute;

import "api/vendorgateway/openbanking/dispute/enums.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/openbanking/dispute";
option java_package = "com.github.epifi.gamma.api.vendorgateway.openbanking.dispute";

message DisputeDetailsForTransaction {
  // transaction number
  string transaction_number = 1;
  // date on which transaction happened
  google.protobuf.Timestamp transaction_date = 2;
  //describes detailed status of dispute on this transaction
  WorkFlowStatus work_flow_status = 3;
  // comment on current work flow
  string work_flow_comments = 4;
  // sender of the transaction, who raised the dispute
  string user_name = 5;
  // Name of the department who has updated the status. SQD/OPD/TMFPD etc.
  string department = 6;
  // status order - defines workflow order
  int64 status_order = 7;
  // raw workflow status
  string raw_workflow_status = 8;
}

message TransactionDetails {
  // Federal transaction id, reverted to us in response to process transaction
  string transaction_id = 1;
  // value date: is the date when the value of an asset that fluctuates in price is determined
  // same as transaction date
  string value_date = 2;
  // transaction time: the time at which transaction happen
  string transaction_time = 3;
  // transaction particular
  string transaction_particulars = 4;
  // transaction amount
  google.type.Money transaction_amount = 5;
  // transaction channel type, ex: UPI, IMPS
  Channel channel = 6;
  // transaction date: the time at which transaction happen
  string transaction_date = 7;
}

// QuestionnaireResult asked to user through app or sherlock
message QuestionnaireResult {
  // question code: identify the the question asked to user when raising the dispute
  string question_code = 1;
  // response data type
  string questionnaire_response_data_type = 2;
  // response value
  string response = 3;
}

// this field should be used if you just need to access custom field values for display purpose
message CustomFieldsWithValue {
  string dispute_status = 1;
  string dispute_case_number = 2;
  string transaction_number = 3;
  string date = 4;
  string work_flow_status = 5;
  string work_flow_comments = 6;
  string user_name = 7;
  string department = 8;
  string status_order = 9;
  string channel = 10;
}

// RawDisputeDetails message is required to capture details returned from DMP APIs in raw format
message RawDisputeDetails {
  string dispute_status = 1;
  string dispute_case_number = 2;
  string transaction_number = 3;
  string date = 4;
  string work_flow_status = 5;
  string work_flow_comments = 6;
  string user_name = 7;
  string department = 8;
  string status_order = 9;
  string channel = 10;
}
