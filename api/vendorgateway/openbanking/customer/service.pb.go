// protolint:disable MAX_LINE_LENGTH

// Vendor gateway APIs for customer management with a partner bank. This includes operations
// like create, update, UN name check, and other operations.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendorgateway/openbanking/customer/service.proto

package customer

import (
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	employment "github.com/epifi/gamma/api/employment"
	kyc "github.com/epifi/gamma/api/kyc"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	dedupe "github.com/epifi/gamma/api/vendorgateway/openbanking/customer/dedupe"
	header "github.com/epifi/gamma/api/vendorgateway/openbanking/header"
	date "google.golang.org/genproto/googleapis/type/date"
	postaladdress "google.golang.org/genproto/googleapis/type/postaladdress"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DedupeStatus int32

const (
	DedupeStatus_DEDUPE_STATE_UNSPECIFIED DedupeStatus = 0
	// The given customer doesnt have a prior account with vendor
	DedupeStatus_CUSTOMER_DOEST_NOT_EXISTS DedupeStatus = 1
	// Dedupe call has failed on vendor
	DedupeStatus_FAILED DedupeStatus = 2
	// Customer has a prior account with vendor
	DedupeStatus_CUSTOMER_EXISTS DedupeStatus = 3
	// Customer is duplicate customer, but the dob and/or phone number details doesnt match with the federal account
	//
	// Deprecated: Marked as deprecated in api/vendorgateway/openbanking/customer/service.proto.
	DedupeStatus_CUSTOMER_EXISTS_DETAILS_MISMATCH DedupeStatus = 4
	// Customer exists but is NRI
	DedupeStatus_CUSTOMER_EXISTS_NRI DedupeStatus = 5
	// Customer exists but is Minor
	DedupeStatus_CUSTOMER_EXISTS_MINOR DedupeStatus = 6
	// Customer exists but kyc but restricted from opening some accounts
	DedupeStatus_CUSTOMER_EXISTS_PARTIAL_KYC DedupeStatus = 7
	// Multiple customer exists in federal
	DedupeStatus_CUSTOMER_EXISTS_MULTIPLE_CUSTOMER_ID DedupeStatus = 8
	// Phone number mismatch - Customer is dedupe customer, but the phone number doesnt match with the federal account
	DedupeStatus_CUSTOMER_EXISTS_PHONE_MISMATCH DedupeStatus = 9
	// Dob mismatch - Customer is dedupe customer, but the dob doesnt match with the federal account
	DedupeStatus_CUSTOMER_EXISTS_DOB_MISMATCH DedupeStatus = 10
	// The Phone number is already linked to multiple accounts
	DedupeStatus_CUSTOMER_EXISTS_PHONE_NUMBER_LINKED_TO_MULTIPLE_ACCOUNTS DedupeStatus = 11
	// The Phone number is linked to an existing account
	DedupeStatus_CUSTOMER_EXISTS_PHONE_NUMBER_LINKED_TO_EXISTING_ACCOUNT DedupeStatus = 12
	// Customer has an existing account but the partner bank didn't return a valid customer id
	// to be mapped to the user.
	DedupeStatus_CUSTOMER_EXISTS_MISSING_CUSTOMER_ID DedupeStatus = 13
	// Customer has an existing account with partial profile details (kyc_profile_flag = N)
	// User already has a savings account where some profile details are not updated.
	// User will have to update profile details with Federal before they can continue.
	DedupeStatus_CUSTOMER_EXISTS_PARTIAL_PROFILE DedupeStatus = 14
	// Customer has been blocked by the bank
	DedupeStatus_BLOCKED DedupeStatus = 15
	// Customer blocked due to days past dues as they had pending dues availing credit services from bank.
	DedupeStatus_BLOCKED_AS_DAYS_PAST_DUES DedupeStatus = 16
	// Email is not linked to this customer.
	DedupeStatus_CUSTOMER_EXISTS_EMAIL_MISMATCH DedupeStatus = 17
	// Email is linked to multiple accounts at the vendor's side.
	DedupeStatus_CUSTOMER_EXISTS_EMAIL_LINKED_TO_MULTIPLE_ACCOUNTS DedupeStatus = 18
	// Email is linked to some existing account at the vendor's side.
	DedupeStatus_CUSTOMER_EXISTS_EMAIL_LINKED_TO_EXISTING_ACCOUNT DedupeStatus = 19
	// Customer with KYC expired or due at the vendor's end.
	DedupeStatus_CUSTOMER_EXISTS_KYC_NOT_VALID DedupeStatus = 20
	// Phone number is linked to other customer
	DedupeStatus_CUSTOMER_DOES_NOT_EXIST_PHONE_NUMBER_LINKED_TO_EXISTING_ACCOUNT DedupeStatus = 21
	// Email is linked to other customer
	DedupeStatus_CUSTOMER_DOES_NOT_EXIST_EMAIL_LINKED_TO_EXISTING_ACCOUNT DedupeStatus = 22
	// No customer found with PAN, but phone number is linked to multiple accounts at the vendor's side.
	DedupeStatus_CUSTOMER_DOES_NOT_EXIST_PHONE_NUMBER_LINKED_TO_MULTIPLE_ACCOUNTS DedupeStatus = 23
	// No customer found with PAN, but email is linked to other multiple accounts at the vendor's side.
	DedupeStatus_CUSTOMER_DOES_NOT_EXIST_EMAIL_LINKED_TO_MULTIPLE_ACCOUNTS DedupeStatus = 24
	// Customer has an existing NRI account but the partner bank didn't return a valid customer id
	DedupeStatus_CUSTOMER_EXISTS_NRI_MISSING_CUSTOMER_ID DedupeStatus = 25
)

// Enum value maps for DedupeStatus.
var (
	DedupeStatus_name = map[int32]string{
		0:  "DEDUPE_STATE_UNSPECIFIED",
		1:  "CUSTOMER_DOEST_NOT_EXISTS",
		2:  "FAILED",
		3:  "CUSTOMER_EXISTS",
		4:  "CUSTOMER_EXISTS_DETAILS_MISMATCH",
		5:  "CUSTOMER_EXISTS_NRI",
		6:  "CUSTOMER_EXISTS_MINOR",
		7:  "CUSTOMER_EXISTS_PARTIAL_KYC",
		8:  "CUSTOMER_EXISTS_MULTIPLE_CUSTOMER_ID",
		9:  "CUSTOMER_EXISTS_PHONE_MISMATCH",
		10: "CUSTOMER_EXISTS_DOB_MISMATCH",
		11: "CUSTOMER_EXISTS_PHONE_NUMBER_LINKED_TO_MULTIPLE_ACCOUNTS",
		12: "CUSTOMER_EXISTS_PHONE_NUMBER_LINKED_TO_EXISTING_ACCOUNT",
		13: "CUSTOMER_EXISTS_MISSING_CUSTOMER_ID",
		14: "CUSTOMER_EXISTS_PARTIAL_PROFILE",
		15: "BLOCKED",
		16: "BLOCKED_AS_DAYS_PAST_DUES",
		17: "CUSTOMER_EXISTS_EMAIL_MISMATCH",
		18: "CUSTOMER_EXISTS_EMAIL_LINKED_TO_MULTIPLE_ACCOUNTS",
		19: "CUSTOMER_EXISTS_EMAIL_LINKED_TO_EXISTING_ACCOUNT",
		20: "CUSTOMER_EXISTS_KYC_NOT_VALID",
		21: "CUSTOMER_DOES_NOT_EXIST_PHONE_NUMBER_LINKED_TO_EXISTING_ACCOUNT",
		22: "CUSTOMER_DOES_NOT_EXIST_EMAIL_LINKED_TO_EXISTING_ACCOUNT",
		23: "CUSTOMER_DOES_NOT_EXIST_PHONE_NUMBER_LINKED_TO_MULTIPLE_ACCOUNTS",
		24: "CUSTOMER_DOES_NOT_EXIST_EMAIL_LINKED_TO_MULTIPLE_ACCOUNTS",
		25: "CUSTOMER_EXISTS_NRI_MISSING_CUSTOMER_ID",
	}
	DedupeStatus_value = map[string]int32{
		"DEDUPE_STATE_UNSPECIFIED":                                         0,
		"CUSTOMER_DOEST_NOT_EXISTS":                                        1,
		"FAILED":                                                           2,
		"CUSTOMER_EXISTS":                                                  3,
		"CUSTOMER_EXISTS_DETAILS_MISMATCH":                                 4,
		"CUSTOMER_EXISTS_NRI":                                              5,
		"CUSTOMER_EXISTS_MINOR":                                            6,
		"CUSTOMER_EXISTS_PARTIAL_KYC":                                      7,
		"CUSTOMER_EXISTS_MULTIPLE_CUSTOMER_ID":                             8,
		"CUSTOMER_EXISTS_PHONE_MISMATCH":                                   9,
		"CUSTOMER_EXISTS_DOB_MISMATCH":                                     10,
		"CUSTOMER_EXISTS_PHONE_NUMBER_LINKED_TO_MULTIPLE_ACCOUNTS":         11,
		"CUSTOMER_EXISTS_PHONE_NUMBER_LINKED_TO_EXISTING_ACCOUNT":          12,
		"CUSTOMER_EXISTS_MISSING_CUSTOMER_ID":                              13,
		"CUSTOMER_EXISTS_PARTIAL_PROFILE":                                  14,
		"BLOCKED":                                                          15,
		"BLOCKED_AS_DAYS_PAST_DUES":                                        16,
		"CUSTOMER_EXISTS_EMAIL_MISMATCH":                                   17,
		"CUSTOMER_EXISTS_EMAIL_LINKED_TO_MULTIPLE_ACCOUNTS":                18,
		"CUSTOMER_EXISTS_EMAIL_LINKED_TO_EXISTING_ACCOUNT":                 19,
		"CUSTOMER_EXISTS_KYC_NOT_VALID":                                    20,
		"CUSTOMER_DOES_NOT_EXIST_PHONE_NUMBER_LINKED_TO_EXISTING_ACCOUNT":  21,
		"CUSTOMER_DOES_NOT_EXIST_EMAIL_LINKED_TO_EXISTING_ACCOUNT":         22,
		"CUSTOMER_DOES_NOT_EXIST_PHONE_NUMBER_LINKED_TO_MULTIPLE_ACCOUNTS": 23,
		"CUSTOMER_DOES_NOT_EXIST_EMAIL_LINKED_TO_MULTIPLE_ACCOUNTS":        24,
		"CUSTOMER_EXISTS_NRI_MISSING_CUSTOMER_ID":                          25,
	}
)

func (x DedupeStatus) Enum() *DedupeStatus {
	p := new(DedupeStatus)
	*p = x
	return p
}

func (x DedupeStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DedupeStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_openbanking_customer_service_proto_enumTypes[0].Descriptor()
}

func (DedupeStatus) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_openbanking_customer_service_proto_enumTypes[0]
}

func (x DedupeStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DedupeStatus.Descriptor instead.
func (DedupeStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{0}
}

type KYCFlag int32

const (
	KYCFlag_KYC_FLAG_UNSPECIFIED     KYCFlag = 0
	KYCFlag_KYC_FLAG_EXISTS          KYCFlag = 1
	KYCFlag_KYC_FLAG_DOES_NOT_EXISTS KYCFlag = 2
)

// Enum value maps for KYCFlag.
var (
	KYCFlag_name = map[int32]string{
		0: "KYC_FLAG_UNSPECIFIED",
		1: "KYC_FLAG_EXISTS",
		2: "KYC_FLAG_DOES_NOT_EXISTS",
	}
	KYCFlag_value = map[string]int32{
		"KYC_FLAG_UNSPECIFIED":     0,
		"KYC_FLAG_EXISTS":          1,
		"KYC_FLAG_DOES_NOT_EXISTS": 2,
	}
)

func (x KYCFlag) Enum() *KYCFlag {
	p := new(KYCFlag)
	*p = x
	return p
}

func (x KYCFlag) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (KYCFlag) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_openbanking_customer_service_proto_enumTypes[1].Descriptor()
}

func (KYCFlag) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_openbanking_customer_service_proto_enumTypes[1]
}

func (x KYCFlag) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use KYCFlag.Descriptor instead.
func (KYCFlag) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{1}
}

type CreditCardFlag int32

const (
	CreditCardFlag_CREDIT_CARD_FLAG_UNSPECIFIED CreditCardFlag = 0
	// in case credit card exists and cc team needs to stop onboarding for a user.
	CreditCardFlag_CREDIT_CARD_FLAG_ALREADY_EXISTS CreditCardFlag = 1
	CreditCardFlag_CREDIT_CARD_FLAG_DOES_NOT_EXIST CreditCardFlag = 2
)

// Enum value maps for CreditCardFlag.
var (
	CreditCardFlag_name = map[int32]string{
		0: "CREDIT_CARD_FLAG_UNSPECIFIED",
		1: "CREDIT_CARD_FLAG_ALREADY_EXISTS",
		2: "CREDIT_CARD_FLAG_DOES_NOT_EXIST",
	}
	CreditCardFlag_value = map[string]int32{
		"CREDIT_CARD_FLAG_UNSPECIFIED":    0,
		"CREDIT_CARD_FLAG_ALREADY_EXISTS": 1,
		"CREDIT_CARD_FLAG_DOES_NOT_EXIST": 2,
	}
)

func (x CreditCardFlag) Enum() *CreditCardFlag {
	p := new(CreditCardFlag)
	*p = x
	return p
}

func (x CreditCardFlag) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreditCardFlag) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_openbanking_customer_service_proto_enumTypes[2].Descriptor()
}

func (CreditCardFlag) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_openbanking_customer_service_proto_enumTypes[2]
}

func (x CreditCardFlag) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreditCardFlag.Descriptor instead.
func (CreditCardFlag) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{2}
}

type PanFlag int32

const (
	PanFlag_PAN_FLAG_UNSPECIFIED    PanFlag = 0
	PanFlag_PAN_FLAG_EXISTS         PanFlag = 1
	PanFlag_PAN_FLAG_DOES_NOT_EXIST PanFlag = 2
)

// Enum value maps for PanFlag.
var (
	PanFlag_name = map[int32]string{
		0: "PAN_FLAG_UNSPECIFIED",
		1: "PAN_FLAG_EXISTS",
		2: "PAN_FLAG_DOES_NOT_EXIST",
	}
	PanFlag_value = map[string]int32{
		"PAN_FLAG_UNSPECIFIED":    0,
		"PAN_FLAG_EXISTS":         1,
		"PAN_FLAG_DOES_NOT_EXIST": 2,
	}
)

func (x PanFlag) Enum() *PanFlag {
	p := new(PanFlag)
	*p = x
	return p
}

func (x PanFlag) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PanFlag) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_openbanking_customer_service_proto_enumTypes[3].Descriptor()
}

func (PanFlag) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_openbanking_customer_service_proto_enumTypes[3]
}

func (x PanFlag) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PanFlag.Descriptor instead.
func (PanFlag) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{3}
}

type AadhaarFlag int32

const (
	AadhaarFlag_AADHAAR_FLAG_UNSPECIFIED    AadhaarFlag = 0
	AadhaarFlag_AADHAAR_FLAG_EXISTS         AadhaarFlag = 1
	AadhaarFlag_AADHAAR_FLAG_DOES_NOT_EXIST AadhaarFlag = 2
)

// Enum value maps for AadhaarFlag.
var (
	AadhaarFlag_name = map[int32]string{
		0: "AADHAAR_FLAG_UNSPECIFIED",
		1: "AADHAAR_FLAG_EXISTS",
		2: "AADHAAR_FLAG_DOES_NOT_EXIST",
	}
	AadhaarFlag_value = map[string]int32{
		"AADHAAR_FLAG_UNSPECIFIED":    0,
		"AADHAAR_FLAG_EXISTS":         1,
		"AADHAAR_FLAG_DOES_NOT_EXIST": 2,
	}
)

func (x AadhaarFlag) Enum() *AadhaarFlag {
	p := new(AadhaarFlag)
	*p = x
	return p
}

func (x AadhaarFlag) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AadhaarFlag) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_openbanking_customer_service_proto_enumTypes[4].Descriptor()
}

func (AadhaarFlag) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_openbanking_customer_service_proto_enumTypes[4]
}

func (x AadhaarFlag) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AadhaarFlag.Descriptor instead.
func (AadhaarFlag) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{4}
}

type ChannelType int32

const (
	ChannelType_CHANNEL_TYPE_UNSPECIFIED ChannelType = 0
	ChannelType_APP                      ChannelType = 1
	// customer care / sherlock
	ChannelType_CC ChannelType = 2
)

// Enum value maps for ChannelType.
var (
	ChannelType_name = map[int32]string{
		0: "CHANNEL_TYPE_UNSPECIFIED",
		1: "APP",
		2: "CC",
	}
	ChannelType_value = map[string]int32{
		"CHANNEL_TYPE_UNSPECIFIED": 0,
		"APP":                      1,
		"CC":                       2,
	}
)

func (x ChannelType) Enum() *ChannelType {
	p := new(ChannelType)
	*p = x
	return p
}

func (x ChannelType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ChannelType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_openbanking_customer_service_proto_enumTypes[5].Descriptor()
}

func (ChannelType) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_openbanking_customer_service_proto_enumTypes[5]
}

func (x ChannelType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ChannelType.Descriptor instead.
func (ChannelType) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{5}
}

// ProductType here enumerates the types of product that can be created
type ProductType int32

const (
	ProductType_PRODUCT_TYPE_UNSPECIFIED ProductType = 0
	// indicates savings account
	ProductType_SAVINGS_ACCOUNT ProductType = 1
)

// Enum value maps for ProductType.
var (
	ProductType_name = map[int32]string{
		0: "PRODUCT_TYPE_UNSPECIFIED",
		1: "SAVINGS_ACCOUNT",
	}
	ProductType_value = map[string]int32{
		"PRODUCT_TYPE_UNSPECIFIED": 0,
		"SAVINGS_ACCOUNT":          1,
	}
)

func (x ProductType) Enum() *ProductType {
	p := new(ProductType)
	*p = x
	return p
}

func (x ProductType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProductType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_openbanking_customer_service_proto_enumTypes[6].Descriptor()
}

func (ProductType) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_openbanking_customer_service_proto_enumTypes[6]
}

func (x ProductType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProductType.Descriptor instead.
func (ProductType) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{6}
}

type CustomerCreationFlow int32

const (
	CustomerCreationFlow_CUSTOMER_CREATION_FLOW_UNSPECIFIED CustomerCreationFlow = 0
	// indicates onboarding O type flow, can be extended to CKYC L and S type
	CustomerCreationFlow_CUSTOMER_CREATION_FLOW_VKYC_ONBOARDING_LSO CustomerCreationFlow = 1
	// indicates OTP based Min KYC CIF creation(CKYC failed, user subjected to EKYC)
	CustomerCreationFlow_CUSTOMER_CREATION_FLOW_MIN_EKYC CustomerCreationFlow = 2
	// CKYC based details fetched with PAN & Aadhar. Since Aadhar data is hashed, customer had to be routed to EKYC to fetch aadhar number
	// subsequently we onboard as full KYC customer
	CustomerCreationFlow_CUSTOMER_CREATION_FLOW_CKYC_EKYC CustomerCreationFlow = 3
	// indicates CKYC based full KYC
	CustomerCreationFlow_CUSTOMER_CREATION_FLOW_CKYC CustomerCreationFlow = 4
	// indicates that the customer has come to customer creation after completing VKYC (a blanket type for all use cases other than LSO or MIN KYC Dedupe)
	CustomerCreationFlow_CUSTOMER_CREATION_FLOW_VKYC CustomerCreationFlow = 5
	// indicates that the customer is a min kyc dedupe, and we performed vkyc to convert customer to full KYC.
	CustomerCreationFlow_CUSTOMER_CREATION_FLOW_DEDUPE_MIN_KYC CustomerCreationFlow = 6
	// indicates that the customer is full KYC through biometric kyc
	CustomerCreationFlow_CUSTOMER_CREATION_FLOW_BKYC CustomerCreationFlow = 7
)

// Enum value maps for CustomerCreationFlow.
var (
	CustomerCreationFlow_name = map[int32]string{
		0: "CUSTOMER_CREATION_FLOW_UNSPECIFIED",
		1: "CUSTOMER_CREATION_FLOW_VKYC_ONBOARDING_LSO",
		2: "CUSTOMER_CREATION_FLOW_MIN_EKYC",
		3: "CUSTOMER_CREATION_FLOW_CKYC_EKYC",
		4: "CUSTOMER_CREATION_FLOW_CKYC",
		5: "CUSTOMER_CREATION_FLOW_VKYC",
		6: "CUSTOMER_CREATION_FLOW_DEDUPE_MIN_KYC",
		7: "CUSTOMER_CREATION_FLOW_BKYC",
	}
	CustomerCreationFlow_value = map[string]int32{
		"CUSTOMER_CREATION_FLOW_UNSPECIFIED":         0,
		"CUSTOMER_CREATION_FLOW_VKYC_ONBOARDING_LSO": 1,
		"CUSTOMER_CREATION_FLOW_MIN_EKYC":            2,
		"CUSTOMER_CREATION_FLOW_CKYC_EKYC":           3,
		"CUSTOMER_CREATION_FLOW_CKYC":                4,
		"CUSTOMER_CREATION_FLOW_VKYC":                5,
		"CUSTOMER_CREATION_FLOW_DEDUPE_MIN_KYC":      6,
		"CUSTOMER_CREATION_FLOW_BKYC":                7,
	}
)

func (x CustomerCreationFlow) Enum() *CustomerCreationFlow {
	p := new(CustomerCreationFlow)
	*p = x
	return p
}

func (x CustomerCreationFlow) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CustomerCreationFlow) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_openbanking_customer_service_proto_enumTypes[7].Descriptor()
}

func (CustomerCreationFlow) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_openbanking_customer_service_proto_enumTypes[7]
}

func (x CustomerCreationFlow) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CustomerCreationFlow.Descriptor instead.
func (CustomerCreationFlow) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{7}
}

type VKYCStatus int32

const (
	VKYCStatus_VKYC_STATUS_UNSPECIFIED VKYCStatus = 0
	// indicates vkyc is still in review auditor action pending
	VKYCStatus_VKYC_STATUS_IN_REVIEW VKYCStatus = 1
	// indicates vkyc is approved by auditor
	VKYCStatus_VKYC_STATUS_APPROVED VKYCStatus = 2
	// indicates vkyc is rejected by auditor
	VKYCStatus_VKYC_STATUS_REJECTED VKYCStatus = 3
	// agent call back in missing on federal end
	VKYCStatus_VKYC_STATUS_AGENT_CALL_BACK_MISSING VKYCStatus = 4
	// auditor callback is missing on federal end
	VKYCStatus_VKYC_STATUS_AUDITOR_CALL_BACK_MISSING VKYCStatus = 5
	// ekyc rrn is not valid
	VKYCStatus_VKYC_STATUS_INVALID_CUSTOMER_ID VKYCStatus = 6
	// no valid account exist for given ekyc rrn
	VKYCStatus_VKYC_STATUS_ACCOUNT_NOT_EXIST VKYCStatus = 7
	// vkyc approved on fed bt scheme change didnt change on fed end
	VKYCStatus_VKYC_STATUS_VKYC_SCHEME_CHANGE_ERROR VKYCStatus = 8
	// auditor marked vkyc under recapture bucket
	VKYCStatus_VKYC_STATUS_AUDITOR_RECAPTURE VKYCStatus = 9
	// KYC full KYC upgradation pending, Kyc haven't processed yet and will be approved in the future
	VKYCStatus_VKYC_STATUS_FULL_KYC_UPGRADE_PENDING VKYCStatus = 10
	// indicates vkyc is rejected by agent
	VKYCStatus_VKYC_STATUS_AGENT_REJECTED VKYCStatus = 11
)

// Enum value maps for VKYCStatus.
var (
	VKYCStatus_name = map[int32]string{
		0:  "VKYC_STATUS_UNSPECIFIED",
		1:  "VKYC_STATUS_IN_REVIEW",
		2:  "VKYC_STATUS_APPROVED",
		3:  "VKYC_STATUS_REJECTED",
		4:  "VKYC_STATUS_AGENT_CALL_BACK_MISSING",
		5:  "VKYC_STATUS_AUDITOR_CALL_BACK_MISSING",
		6:  "VKYC_STATUS_INVALID_CUSTOMER_ID",
		7:  "VKYC_STATUS_ACCOUNT_NOT_EXIST",
		8:  "VKYC_STATUS_VKYC_SCHEME_CHANGE_ERROR",
		9:  "VKYC_STATUS_AUDITOR_RECAPTURE",
		10: "VKYC_STATUS_FULL_KYC_UPGRADE_PENDING",
		11: "VKYC_STATUS_AGENT_REJECTED",
	}
	VKYCStatus_value = map[string]int32{
		"VKYC_STATUS_UNSPECIFIED":               0,
		"VKYC_STATUS_IN_REVIEW":                 1,
		"VKYC_STATUS_APPROVED":                  2,
		"VKYC_STATUS_REJECTED":                  3,
		"VKYC_STATUS_AGENT_CALL_BACK_MISSING":   4,
		"VKYC_STATUS_AUDITOR_CALL_BACK_MISSING": 5,
		"VKYC_STATUS_INVALID_CUSTOMER_ID":       6,
		"VKYC_STATUS_ACCOUNT_NOT_EXIST":         7,
		"VKYC_STATUS_VKYC_SCHEME_CHANGE_ERROR":  8,
		"VKYC_STATUS_AUDITOR_RECAPTURE":         9,
		"VKYC_STATUS_FULL_KYC_UPGRADE_PENDING":  10,
		"VKYC_STATUS_AGENT_REJECTED":            11,
	}
)

func (x VKYCStatus) Enum() *VKYCStatus {
	p := new(VKYCStatus)
	*p = x
	return p
}

func (x VKYCStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VKYCStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_openbanking_customer_service_proto_enumTypes[8].Descriptor()
}

func (VKYCStatus) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_openbanking_customer_service_proto_enumTypes[8]
}

func (x VKYCStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VKYCStatus.Descriptor instead.
func (VKYCStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{8}
}

type CreateCustomerResponse_Status int32

const (
	CreateCustomerResponse_OK                           CreateCustomerResponse_Status = 0
	CreateCustomerResponse_INVALID_SENDER_DETAILS       CreateCustomerResponse_Status = 100
	CreateCustomerResponse_SENDER_NOT_ENABLED           CreateCustomerResponse_Status = 101
	CreateCustomerResponse_DUPLICATE_REQUESTID          CreateCustomerResponse_Status = 102
	CreateCustomerResponse_NO_REQUESTID                 CreateCustomerResponse_Status = 103
	CreateCustomerResponse_EXCEPTION                    CreateCustomerResponse_Status = 104
	CreateCustomerResponse_INVALID_DEVICE               CreateCustomerResponse_Status = 105
	CreateCustomerResponse_INVALID_TRAN                 CreateCustomerResponse_Status = 106
	CreateCustomerResponse_FIELD_MISSING                CreateCustomerResponse_Status = 107
	CreateCustomerResponse_INVALID_INPUT                CreateCustomerResponse_Status = 108
	CreateCustomerResponse_CUSTOMER_CREATION_INPROGRESS CreateCustomerResponse_Status = 109
	CreateCustomerResponse_DUPLICATE_CUSTOMER_ID        CreateCustomerResponse_Status = 110
)

// Enum value maps for CreateCustomerResponse_Status.
var (
	CreateCustomerResponse_Status_name = map[int32]string{
		0:   "OK",
		100: "INVALID_SENDER_DETAILS",
		101: "SENDER_NOT_ENABLED",
		102: "DUPLICATE_REQUESTID",
		103: "NO_REQUESTID",
		104: "EXCEPTION",
		105: "INVALID_DEVICE",
		106: "INVALID_TRAN",
		107: "FIELD_MISSING",
		108: "INVALID_INPUT",
		109: "CUSTOMER_CREATION_INPROGRESS",
		110: "DUPLICATE_CUSTOMER_ID",
	}
	CreateCustomerResponse_Status_value = map[string]int32{
		"OK":                           0,
		"INVALID_SENDER_DETAILS":       100,
		"SENDER_NOT_ENABLED":           101,
		"DUPLICATE_REQUESTID":          102,
		"NO_REQUESTID":                 103,
		"EXCEPTION":                    104,
		"INVALID_DEVICE":               105,
		"INVALID_TRAN":                 106,
		"FIELD_MISSING":                107,
		"INVALID_INPUT":                108,
		"CUSTOMER_CREATION_INPROGRESS": 109,
		"DUPLICATE_CUSTOMER_ID":        110,
	}
)

func (x CreateCustomerResponse_Status) Enum() *CreateCustomerResponse_Status {
	p := new(CreateCustomerResponse_Status)
	*p = x
	return p
}

func (x CreateCustomerResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreateCustomerResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_openbanking_customer_service_proto_enumTypes[9].Descriptor()
}

func (CreateCustomerResponse_Status) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_openbanking_customer_service_proto_enumTypes[9]
}

func (x CreateCustomerResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreateCustomerResponse_Status.Descriptor instead.
func (CreateCustomerResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{2, 0}
}

type CreateCustomerForNonResidentResponse_Status int32

const (
	CreateCustomerForNonResidentResponse_OK                           CreateCustomerForNonResidentResponse_Status = 0
	CreateCustomerForNonResidentResponse_INVALID_SENDER_DETAILS       CreateCustomerForNonResidentResponse_Status = 100
	CreateCustomerForNonResidentResponse_SENDER_NOT_ENABLED           CreateCustomerForNonResidentResponse_Status = 101
	CreateCustomerForNonResidentResponse_DUPLICATE_REQUESTID          CreateCustomerForNonResidentResponse_Status = 102
	CreateCustomerForNonResidentResponse_NO_REQUESTID                 CreateCustomerForNonResidentResponse_Status = 103
	CreateCustomerForNonResidentResponse_EXCEPTION                    CreateCustomerForNonResidentResponse_Status = 104
	CreateCustomerForNonResidentResponse_INVALID_DEVICE               CreateCustomerForNonResidentResponse_Status = 105
	CreateCustomerForNonResidentResponse_INVALID_TRAN                 CreateCustomerForNonResidentResponse_Status = 106
	CreateCustomerForNonResidentResponse_FIELD_MISSING                CreateCustomerForNonResidentResponse_Status = 107
	CreateCustomerForNonResidentResponse_INVALID_INPUT                CreateCustomerForNonResidentResponse_Status = 108
	CreateCustomerForNonResidentResponse_CUSTOMER_CREATION_INPROGRESS CreateCustomerForNonResidentResponse_Status = 109
	CreateCustomerForNonResidentResponse_DUPLICATE_CUSTOMER_ID        CreateCustomerForNonResidentResponse_Status = 110
)

// Enum value maps for CreateCustomerForNonResidentResponse_Status.
var (
	CreateCustomerForNonResidentResponse_Status_name = map[int32]string{
		0:   "OK",
		100: "INVALID_SENDER_DETAILS",
		101: "SENDER_NOT_ENABLED",
		102: "DUPLICATE_REQUESTID",
		103: "NO_REQUESTID",
		104: "EXCEPTION",
		105: "INVALID_DEVICE",
		106: "INVALID_TRAN",
		107: "FIELD_MISSING",
		108: "INVALID_INPUT",
		109: "CUSTOMER_CREATION_INPROGRESS",
		110: "DUPLICATE_CUSTOMER_ID",
	}
	CreateCustomerForNonResidentResponse_Status_value = map[string]int32{
		"OK":                           0,
		"INVALID_SENDER_DETAILS":       100,
		"SENDER_NOT_ENABLED":           101,
		"DUPLICATE_REQUESTID":          102,
		"NO_REQUESTID":                 103,
		"EXCEPTION":                    104,
		"INVALID_DEVICE":               105,
		"INVALID_TRAN":                 106,
		"FIELD_MISSING":                107,
		"INVALID_INPUT":                108,
		"CUSTOMER_CREATION_INPROGRESS": 109,
		"DUPLICATE_CUSTOMER_ID":        110,
	}
)

func (x CreateCustomerForNonResidentResponse_Status) Enum() *CreateCustomerForNonResidentResponse_Status {
	p := new(CreateCustomerForNonResidentResponse_Status)
	*p = x
	return p
}

func (x CreateCustomerForNonResidentResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreateCustomerForNonResidentResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_openbanking_customer_service_proto_enumTypes[10].Descriptor()
}

func (CreateCustomerForNonResidentResponse_Status) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_openbanking_customer_service_proto_enumTypes[10]
}

func (x CreateCustomerForNonResidentResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreateCustomerForNonResidentResponse_Status.Descriptor instead.
func (CreateCustomerForNonResidentResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{4, 0}
}

type CheckCustomerStatusResponse_Status int32

const (
	CheckCustomerStatusResponse_OK                 CheckCustomerStatusResponse_Status = 0
	CheckCustomerStatusResponse_SENDER_NOT_ENABLED CheckCustomerStatusResponse_Status = 100
	CheckCustomerStatusResponse_INVALID_INPUT      CheckCustomerStatusResponse_Status = 101
	CheckCustomerStatusResponse_SERVER_ERROR       CheckCustomerStatusResponse_Status = 102
	CheckCustomerStatusResponse_FIELD_MISSING      CheckCustomerStatusResponse_Status = 103
	CheckCustomerStatusResponse_SOCKET_EXCEPTION   CheckCustomerStatusResponse_Status = 104
	CheckCustomerStatusResponse_EXCEPTION          CheckCustomerStatusResponse_Status = 105
	// If the initial customer creation request did not reach vendor we get details not found.
	// We can retry customer creation with the same requestId
	CheckCustomerStatusResponse_DETAILS_NOT_FOUND CheckCustomerStatusResponse_Status = 106
	// For many customer creation enquiry requests we are getting response like
	// "timeout occurred whilst performing", "a remote host did not respond within the timeout",
	// but the customer creation is still succeeding for such requests, hence handling them
	// to reduce noise in alerts
	CheckCustomerStatusResponse_VENDOR_INTERMITTENT_FLAKINESS         CheckCustomerStatusResponse_Status = 107
	CheckCustomerStatusResponse_KYC_FAILED                            CheckCustomerStatusResponse_Status = 108
	CheckCustomerStatusResponse_INVALID_DEVICE_TOKEN                  CheckCustomerStatusResponse_Status = 109
	CheckCustomerStatusResponse_SERVICE_NOT_AVAILABLE_FOR_SENDER_CODE CheckCustomerStatusResponse_Status = 110
	// For some customer creation enquiry request, we get FAILURE in ResponseAction and on every subsequent polling,
	// we get the same response. This status will be used to track that and mark them as terminally failed in consumer.
	CheckCustomerStatusResponse_RESPONSE_ACTION_FAILURE CheckCustomerStatusResponse_Status = 111
)

// Enum value maps for CheckCustomerStatusResponse_Status.
var (
	CheckCustomerStatusResponse_Status_name = map[int32]string{
		0:   "OK",
		100: "SENDER_NOT_ENABLED",
		101: "INVALID_INPUT",
		102: "SERVER_ERROR",
		103: "FIELD_MISSING",
		104: "SOCKET_EXCEPTION",
		105: "EXCEPTION",
		106: "DETAILS_NOT_FOUND",
		107: "VENDOR_INTERMITTENT_FLAKINESS",
		108: "KYC_FAILED",
		109: "INVALID_DEVICE_TOKEN",
		110: "SERVICE_NOT_AVAILABLE_FOR_SENDER_CODE",
		111: "RESPONSE_ACTION_FAILURE",
	}
	CheckCustomerStatusResponse_Status_value = map[string]int32{
		"OK":                                    0,
		"SENDER_NOT_ENABLED":                    100,
		"INVALID_INPUT":                         101,
		"SERVER_ERROR":                          102,
		"FIELD_MISSING":                         103,
		"SOCKET_EXCEPTION":                      104,
		"EXCEPTION":                             105,
		"DETAILS_NOT_FOUND":                     106,
		"VENDOR_INTERMITTENT_FLAKINESS":         107,
		"KYC_FAILED":                            108,
		"INVALID_DEVICE_TOKEN":                  109,
		"SERVICE_NOT_AVAILABLE_FOR_SENDER_CODE": 110,
		"RESPONSE_ACTION_FAILURE":               111,
	}
)

func (x CheckCustomerStatusResponse_Status) Enum() *CheckCustomerStatusResponse_Status {
	p := new(CheckCustomerStatusResponse_Status)
	*p = x
	return p
}

func (x CheckCustomerStatusResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CheckCustomerStatusResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_openbanking_customer_service_proto_enumTypes[11].Descriptor()
}

func (CheckCustomerStatusResponse_Status) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_openbanking_customer_service_proto_enumTypes[11]
}

func (x CheckCustomerStatusResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CheckCustomerStatusResponse_Status.Descriptor instead.
func (CheckCustomerStatusResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{6, 0}
}

type CheckCustomerStatusForNonResidentResponse_Status int32

const (
	CheckCustomerStatusForNonResidentResponse_OK                 CheckCustomerStatusForNonResidentResponse_Status = 0
	CheckCustomerStatusForNonResidentResponse_SENDER_NOT_ENABLED CheckCustomerStatusForNonResidentResponse_Status = 100
	CheckCustomerStatusForNonResidentResponse_INVALID_INPUT      CheckCustomerStatusForNonResidentResponse_Status = 101
	CheckCustomerStatusForNonResidentResponse_SERVER_ERROR       CheckCustomerStatusForNonResidentResponse_Status = 102
	CheckCustomerStatusForNonResidentResponse_FIELD_MISSING      CheckCustomerStatusForNonResidentResponse_Status = 103
	CheckCustomerStatusForNonResidentResponse_SOCKET_EXCEPTION   CheckCustomerStatusForNonResidentResponse_Status = 104
	CheckCustomerStatusForNonResidentResponse_EXCEPTION          CheckCustomerStatusForNonResidentResponse_Status = 105
	// If the initial customer creation request did not reach vendor we get details not found.
	// We can retry customer creation with the same requestId
	CheckCustomerStatusForNonResidentResponse_DETAILS_NOT_FOUND CheckCustomerStatusForNonResidentResponse_Status = 106
	// For many customer creation enquiry requests we are getting response like
	// "timeout occurred whilst performing", "a remote host did not respond within the timeout",
	// but the customer creation is still succeeding for such requests, hence handling them
	// to reduce noise in alerts
	CheckCustomerStatusForNonResidentResponse_VENDOR_INTERMITTENT_FLAKINESS         CheckCustomerStatusForNonResidentResponse_Status = 107
	CheckCustomerStatusForNonResidentResponse_KYC_FAILED                            CheckCustomerStatusForNonResidentResponse_Status = 108
	CheckCustomerStatusForNonResidentResponse_INVALID_DEVICE_TOKEN                  CheckCustomerStatusForNonResidentResponse_Status = 109
	CheckCustomerStatusForNonResidentResponse_SERVICE_NOT_AVAILABLE_FOR_SENDER_CODE CheckCustomerStatusForNonResidentResponse_Status = 110
	// For some customer creation enquiry request, we get FAILURE in ResponseAction and on every subsequent polling,
	// we get the same response. This status will be used to track that and mark them as terminally failed in consumer.
	CheckCustomerStatusForNonResidentResponse_RESPONSE_ACTION_FAILURE CheckCustomerStatusForNonResidentResponse_Status = 111
)

// Enum value maps for CheckCustomerStatusForNonResidentResponse_Status.
var (
	CheckCustomerStatusForNonResidentResponse_Status_name = map[int32]string{
		0:   "OK",
		100: "SENDER_NOT_ENABLED",
		101: "INVALID_INPUT",
		102: "SERVER_ERROR",
		103: "FIELD_MISSING",
		104: "SOCKET_EXCEPTION",
		105: "EXCEPTION",
		106: "DETAILS_NOT_FOUND",
		107: "VENDOR_INTERMITTENT_FLAKINESS",
		108: "KYC_FAILED",
		109: "INVALID_DEVICE_TOKEN",
		110: "SERVICE_NOT_AVAILABLE_FOR_SENDER_CODE",
		111: "RESPONSE_ACTION_FAILURE",
	}
	CheckCustomerStatusForNonResidentResponse_Status_value = map[string]int32{
		"OK":                                    0,
		"SENDER_NOT_ENABLED":                    100,
		"INVALID_INPUT":                         101,
		"SERVER_ERROR":                          102,
		"FIELD_MISSING":                         103,
		"SOCKET_EXCEPTION":                      104,
		"EXCEPTION":                             105,
		"DETAILS_NOT_FOUND":                     106,
		"VENDOR_INTERMITTENT_FLAKINESS":         107,
		"KYC_FAILED":                            108,
		"INVALID_DEVICE_TOKEN":                  109,
		"SERVICE_NOT_AVAILABLE_FOR_SENDER_CODE": 110,
		"RESPONSE_ACTION_FAILURE":               111,
	}
)

func (x CheckCustomerStatusForNonResidentResponse_Status) Enum() *CheckCustomerStatusForNonResidentResponse_Status {
	p := new(CheckCustomerStatusForNonResidentResponse_Status)
	*p = x
	return p
}

func (x CheckCustomerStatusForNonResidentResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CheckCustomerStatusForNonResidentResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_openbanking_customer_service_proto_enumTypes[12].Descriptor()
}

func (CheckCustomerStatusForNonResidentResponse_Status) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_openbanking_customer_service_proto_enumTypes[12]
}

func (x CheckCustomerStatusForNonResidentResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CheckCustomerStatusForNonResidentResponse_Status.Descriptor instead.
func (CheckCustomerStatusForNonResidentResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{8, 0}
}

type FetchCustomerDetailsResponse_Status int32

const (
	FetchCustomerDetailsResponse_OK FetchCustomerDetailsResponse_Status = 0
)

// Enum value maps for FetchCustomerDetailsResponse_Status.
var (
	FetchCustomerDetailsResponse_Status_name = map[int32]string{
		0: "OK",
	}
	FetchCustomerDetailsResponse_Status_value = map[string]int32{
		"OK": 0,
	}
)

func (x FetchCustomerDetailsResponse_Status) Enum() *FetchCustomerDetailsResponse_Status {
	p := new(FetchCustomerDetailsResponse_Status)
	*p = x
	return p
}

func (x FetchCustomerDetailsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FetchCustomerDetailsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_openbanking_customer_service_proto_enumTypes[13].Descriptor()
}

func (FetchCustomerDetailsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_openbanking_customer_service_proto_enumTypes[13]
}

func (x FetchCustomerDetailsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FetchCustomerDetailsResponse_Status.Descriptor instead.
func (FetchCustomerDetailsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{12, 0}
}

type EnquireVKYCStatusResponse_Status int32

const (
	EnquireVKYCStatusResponse_SUCCESS                  EnquireVKYCStatusResponse_Status = 0
	EnquireVKYCStatusResponse_PARSING_ERROR            EnquireVKYCStatusResponse_Status = 101
	EnquireVKYCStatusResponse_INVALID_INPUTS           EnquireVKYCStatusResponse_Status = 102
	EnquireVKYCStatusResponse_INTERNAL                 EnquireVKYCStatusResponse_Status = 103
	EnquireVKYCStatusResponse_MANDATORY_FIELDS_MISSING EnquireVKYCStatusResponse_Status = 104
	EnquireVKYCStatusResponse_INVALID_CONTENT_TYPE     EnquireVKYCStatusResponse_Status = 105
	EnquireVKYCStatusResponse_INVALID_REQUEST_TYPE     EnquireVKYCStatusResponse_Status = 106
)

// Enum value maps for EnquireVKYCStatusResponse_Status.
var (
	EnquireVKYCStatusResponse_Status_name = map[int32]string{
		0:   "SUCCESS",
		101: "PARSING_ERROR",
		102: "INVALID_INPUTS",
		103: "INTERNAL",
		104: "MANDATORY_FIELDS_MISSING",
		105: "INVALID_CONTENT_TYPE",
		106: "INVALID_REQUEST_TYPE",
	}
	EnquireVKYCStatusResponse_Status_value = map[string]int32{
		"SUCCESS":                  0,
		"PARSING_ERROR":            101,
		"INVALID_INPUTS":           102,
		"INTERNAL":                 103,
		"MANDATORY_FIELDS_MISSING": 104,
		"INVALID_CONTENT_TYPE":     105,
		"INVALID_REQUEST_TYPE":     106,
	}
)

func (x EnquireVKYCStatusResponse_Status) Enum() *EnquireVKYCStatusResponse_Status {
	p := new(EnquireVKYCStatusResponse_Status)
	*p = x
	return p
}

func (x EnquireVKYCStatusResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EnquireVKYCStatusResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_openbanking_customer_service_proto_enumTypes[14].Descriptor()
}

func (EnquireVKYCStatusResponse_Status) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_openbanking_customer_service_proto_enumTypes[14]
}

func (x EnquireVKYCStatusResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EnquireVKYCStatusResponse_Status.Descriptor instead.
func (EnquireVKYCStatusResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{18, 0}
}

// Request to create a customer with the bank.
type CreateCustomerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all vendorgateway APIs.
	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Unique identifier for this request to be generated by epiFi.
	// In case of status check or retries, this request ID is to be
	// sent.
	RequestId string `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// The full legal name of the customer.
	Name *common.Name `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// Date of birth of the customer.
	DateOfBirth *date.Date `protobuf:"bytes,4,opt,name=date_of_birth,json=dateOfBirth,proto3" json:"date_of_birth,omitempty"`
	// Permanent address of the customer.
	PermanentAddress *postaladdress.PostalAddress `protobuf:"bytes,5,opt,name=permanent_address,json=permanentAddress,proto3" json:"permanent_address,omitempty"`
	// Current address of the customer.
	CurrentAddress *postaladdress.PostalAddress `protobuf:"bytes,6,opt,name=current_address,json=currentAddress,proto3" json:"current_address,omitempty"`
	Gender         typesv2.Gender               `protobuf:"varint,7,opt,name=gender,proto3,enum=api.typesv2.Gender" json:"gender,omitempty"`
	PhoneNumber    *common.PhoneNumber          `protobuf:"bytes,8,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// PAN number of the customer.
	PanNumber string `protobuf:"bytes,9,opt,name=pan_number,json=panNumber,proto3" json:"pan_number,omitempty"`
	// Correspondence email of the customer.
	Email string `protobuf:"bytes,10,opt,name=email,proto3" json:"email,omitempty"`
	// father name of the customer
	FatherName string `protobuf:"bytes,11,opt,name=father_name,json=fatherName,proto3" json:"father_name,omitempty"`
	// mother name of customer
	MotherName string `protobuf:"bytes,12,opt,name=mother_name,json=motherName,proto3" json:"mother_name,omitempty"`
	// details of identity proof
	IdentityProof *ProofDetails `protobuf:"bytes,13,opt,name=identity_proof,json=identityProof,proto3" json:"identity_proof,omitempty"`
	// details of address proof
	AddressProof *ProofDetails `protobuf:"bytes,14,opt,name=address_proof,json=addressProof,proto3" json:"address_proof,omitempty"`
	// device details of the customer
	DeviceDetails *header.Auth `protobuf:"bytes,15,opt,name=device_details,json=deviceDetails,proto3" json:"device_details,omitempty"`
	// UID reference key received in eKYC OTP verification response
	UidNo string `protobuf:"bytes,16,opt,name=uid_no,json=uidNo,proto3" json:"uid_no,omitempty"`
	// Sign ID in case of CKYC download data
	SignImage string `protobuf:"bytes,17,opt,name=sign_image,json=signImage,proto3" json:"sign_image,omitempty"`
	// Employment type of user
	Type employment.EmploymentType `protobuf:"varint,18,opt,name=type,proto3,enum=employment.EmploymentType" json:"type,omitempty"`
	// Annual income of user
	AnnualIncome float32 `protobuf:"fixed32,19,opt,name=annual_income,json=annualIncome,proto3" json:"annual_income,omitempty"`
	// flow via which kyc level was updated used for deciding sender code currently
	CustomerCreationFlow CustomerCreationFlow `protobuf:"varint,20,opt,name=customer_creation_flow,json=customerCreationFlow,proto3,enum=vendorgateway.openbanking.customer.CustomerCreationFlow" json:"customer_creation_flow,omitempty"`
	// Occupation of user
	OccupationType employment.OccupationType `protobuf:"varint,21,opt,name=occupation_type,json=occupationType,proto3,enum=employment.OccupationType" json:"occupation_type,omitempty"`
	// Sol ID (Branch ID) for the customer creation
	SolId          string                 `protobuf:"bytes,22,opt,name=sol_id,json=solId,proto3" json:"sol_id,omitempty"`
	DisabilityType typesv2.DisabilityType `protobuf:"varint,23,opt,name=disability_type,json=disabilityType,proto3,enum=api.typesv2.DisabilityType" json:"disability_type,omitempty"`
	Category       typesv2.Category       `protobuf:"varint,24,opt,name=category,proto3,enum=api.typesv2.Category" json:"category,omitempty"`
	PepCategory    typesv2.PepCategory    `protobuf:"varint,25,opt,name=pep_category,json=pepCategory,proto3,enum=api.typesv2.PepCategory" json:"pep_category,omitempty"`
	Qualification  typesv2.Qualification  `protobuf:"varint,26,opt,name=qualification,proto3,enum=api.typesv2.Qualification" json:"qualification,omitempty"`
}

func (x *CreateCustomerRequest) Reset() {
	*x = CreateCustomerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCustomerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomerRequest) ProtoMessage() {}

func (x *CreateCustomerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomerRequest.ProtoReflect.Descriptor instead.
func (*CreateCustomerRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreateCustomerRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CreateCustomerRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *CreateCustomerRequest) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *CreateCustomerRequest) GetDateOfBirth() *date.Date {
	if x != nil {
		return x.DateOfBirth
	}
	return nil
}

func (x *CreateCustomerRequest) GetPermanentAddress() *postaladdress.PostalAddress {
	if x != nil {
		return x.PermanentAddress
	}
	return nil
}

func (x *CreateCustomerRequest) GetCurrentAddress() *postaladdress.PostalAddress {
	if x != nil {
		return x.CurrentAddress
	}
	return nil
}

func (x *CreateCustomerRequest) GetGender() typesv2.Gender {
	if x != nil {
		return x.Gender
	}
	return typesv2.Gender(0)
}

func (x *CreateCustomerRequest) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *CreateCustomerRequest) GetPanNumber() string {
	if x != nil {
		return x.PanNumber
	}
	return ""
}

func (x *CreateCustomerRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *CreateCustomerRequest) GetFatherName() string {
	if x != nil {
		return x.FatherName
	}
	return ""
}

func (x *CreateCustomerRequest) GetMotherName() string {
	if x != nil {
		return x.MotherName
	}
	return ""
}

func (x *CreateCustomerRequest) GetIdentityProof() *ProofDetails {
	if x != nil {
		return x.IdentityProof
	}
	return nil
}

func (x *CreateCustomerRequest) GetAddressProof() *ProofDetails {
	if x != nil {
		return x.AddressProof
	}
	return nil
}

func (x *CreateCustomerRequest) GetDeviceDetails() *header.Auth {
	if x != nil {
		return x.DeviceDetails
	}
	return nil
}

func (x *CreateCustomerRequest) GetUidNo() string {
	if x != nil {
		return x.UidNo
	}
	return ""
}

func (x *CreateCustomerRequest) GetSignImage() string {
	if x != nil {
		return x.SignImage
	}
	return ""
}

func (x *CreateCustomerRequest) GetType() employment.EmploymentType {
	if x != nil {
		return x.Type
	}
	return employment.EmploymentType(0)
}

func (x *CreateCustomerRequest) GetAnnualIncome() float32 {
	if x != nil {
		return x.AnnualIncome
	}
	return 0
}

func (x *CreateCustomerRequest) GetCustomerCreationFlow() CustomerCreationFlow {
	if x != nil {
		return x.CustomerCreationFlow
	}
	return CustomerCreationFlow_CUSTOMER_CREATION_FLOW_UNSPECIFIED
}

func (x *CreateCustomerRequest) GetOccupationType() employment.OccupationType {
	if x != nil {
		return x.OccupationType
	}
	return employment.OccupationType(0)
}

func (x *CreateCustomerRequest) GetSolId() string {
	if x != nil {
		return x.SolId
	}
	return ""
}

func (x *CreateCustomerRequest) GetDisabilityType() typesv2.DisabilityType {
	if x != nil {
		return x.DisabilityType
	}
	return typesv2.DisabilityType(0)
}

func (x *CreateCustomerRequest) GetCategory() typesv2.Category {
	if x != nil {
		return x.Category
	}
	return typesv2.Category(0)
}

func (x *CreateCustomerRequest) GetPepCategory() typesv2.PepCategory {
	if x != nil {
		return x.PepCategory
	}
	return typesv2.PepCategory(0)
}

func (x *CreateCustomerRequest) GetQualification() typesv2.Qualification {
	if x != nil {
		return x.Qualification
	}
	return typesv2.Qualification(0)
}

type ProofDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type         kyc.IdProofType `protobuf:"varint,1,opt,name=type,proto3,enum=kyc.IdProofType" json:"type,omitempty"`
	IdNumber     string          `protobuf:"bytes,2,opt,name=id_number,json=idNumber,proto3" json:"id_number,omitempty"`
	IdIssueDate  *date.Date      `protobuf:"bytes,3,opt,name=id_issue_date,json=idIssueDate,proto3" json:"id_issue_date,omitempty"`
	IdExpiryDate *date.Date      `protobuf:"bytes,4,opt,name=id_expiry_date,json=idExpiryDate,proto3" json:"id_expiry_date,omitempty"`
}

func (x *ProofDetails) Reset() {
	*x = ProofDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProofDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProofDetails) ProtoMessage() {}

func (x *ProofDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProofDetails.ProtoReflect.Descriptor instead.
func (*ProofDetails) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{1}
}

func (x *ProofDetails) GetType() kyc.IdProofType {
	if x != nil {
		return x.Type
	}
	return kyc.IdProofType(0)
}

func (x *ProofDetails) GetIdNumber() string {
	if x != nil {
		return x.IdNumber
	}
	return ""
}

func (x *ProofDetails) GetIdIssueDate() *date.Date {
	if x != nil {
		return x.IdIssueDate
	}
	return nil
}

func (x *ProofDetails) GetIdExpiryDate() *date.Date {
	if x != nil {
		return x.IdExpiryDate
	}
	return nil
}

// Response for the CreateCustomer method. Returns with either a success or
// failure. Actual response is to be obtained with a status check API.
type CreateCustomerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status       *rpc.Status                 `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	VendorStatus *vendorgateway.VendorStatus `protobuf:"bytes,2,opt,name=vendor_status,json=vendorStatus,proto3" json:"vendor_status,omitempty"`
}

func (x *CreateCustomerResponse) Reset() {
	*x = CreateCustomerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCustomerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomerResponse) ProtoMessage() {}

func (x *CreateCustomerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomerResponse.ProtoReflect.Descriptor instead.
func (*CreateCustomerResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{2}
}

func (x *CreateCustomerResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CreateCustomerResponse) GetVendorStatus() *vendorgateway.VendorStatus {
	if x != nil {
		return x.VendorStatus
	}
	return nil
}

// Request to create a customer for non resident user with the bank.
type CreateCustomerForNonResidentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all vendorgateway APIs.
	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Unique identifier for this request to be generated by epiFi.
	// In case of status check or retries, this request ID is to be
	// sent.
	RequestId string `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// The full legal name of the customer.
	Name *common.Name `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// Date of birth of the customer.
	DateOfBirth *typesv2.Date `protobuf:"bytes,4,opt,name=date_of_birth,json=dateOfBirth,proto3" json:"date_of_birth,omitempty"`
	// Permanent address of the customer.
	PermanentAddress *postaladdress.PostalAddress `protobuf:"bytes,5,opt,name=permanent_address,json=permanentAddress,proto3" json:"permanent_address,omitempty"`
	// Current address of the customer.
	CurrentAddress *postaladdress.PostalAddress `protobuf:"bytes,6,opt,name=current_address,json=currentAddress,proto3" json:"current_address,omitempty"`
	Gender         typesv2.Gender               `protobuf:"varint,7,opt,name=gender,proto3,enum=api.typesv2.Gender" json:"gender,omitempty"`
	PhoneNumber    *common.PhoneNumber          `protobuf:"bytes,8,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// PAN number of the customer.
	PanNumber string `protobuf:"bytes,9,opt,name=pan_number,json=panNumber,proto3" json:"pan_number,omitempty"`
	// Correspondence email of the customer.
	Email string `protobuf:"bytes,10,opt,name=email,proto3" json:"email,omitempty"`
	// father name of the customer
	FatherName string `protobuf:"bytes,11,opt,name=father_name,json=fatherName,proto3" json:"father_name,omitempty"`
	// mother name of customer
	MotherName string `protobuf:"bytes,12,opt,name=mother_name,json=motherName,proto3" json:"mother_name,omitempty"`
	// Employment type of user
	EmploymentType employment.EmploymentType `protobuf:"varint,13,opt,name=employment_type,json=employmentType,proto3,enum=employment.EmploymentType" json:"employment_type,omitempty"`
	// flow via which kyc level was updated used for deciding sender code currently
	CustomerCreationFlow CustomerCreationFlow `protobuf:"varint,15,opt,name=customer_creation_flow,json=customerCreationFlow,proto3,enum=vendorgateway.openbanking.customer.CustomerCreationFlow" json:"customer_creation_flow,omitempty"`
	// Occupation of user
	OccupationType employment.OccupationType `protobuf:"varint,16,opt,name=occupation_type,json=occupationType,proto3,enum=employment.OccupationType" json:"occupation_type,omitempty"`
	// Sol ID (Branch ID) for the customer creation
	SolId             string                `protobuf:"bytes,17,opt,name=sol_id,json=solId,proto3" json:"sol_id,omitempty"`
	PassportData      *typesv2.PassportData `protobuf:"bytes,18,opt,name=passport_data,json=passportData,proto3" json:"passport_data,omitempty"`
	AnnualIncomeRange *typesv2.SalaryRange  `protobuf:"bytes,19,opt,name=annual_income_range,json=annualIncomeRange,proto3" json:"annual_income_range,omitempty"`
	// user's signature image in base64 (optional field)
	SignImage     string                `protobuf:"bytes,20,opt,name=sign_image,json=signImage,proto3" json:"sign_image,omitempty"`
	MaritalStatus typesv2.MaritalStatus `protobuf:"varint,21,opt,name=marital_status,json=maritalStatus,proto3,enum=api.typesv2.MaritalStatus" json:"marital_status,omitempty"`
	// PAN is not required if user opted for form 60
	Form_60OptedFlag common.BooleanEnum `protobuf:"varint,22,opt,name=form_60_opted_flag,json=form60OptedFlag,proto3,enum=api.typesv2.common.BooleanEnum" json:"form_60_opted_flag,omitempty"`
}

func (x *CreateCustomerForNonResidentRequest) Reset() {
	*x = CreateCustomerForNonResidentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCustomerForNonResidentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomerForNonResidentRequest) ProtoMessage() {}

func (x *CreateCustomerForNonResidentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomerForNonResidentRequest.ProtoReflect.Descriptor instead.
func (*CreateCustomerForNonResidentRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{3}
}

func (x *CreateCustomerForNonResidentRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CreateCustomerForNonResidentRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *CreateCustomerForNonResidentRequest) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *CreateCustomerForNonResidentRequest) GetDateOfBirth() *typesv2.Date {
	if x != nil {
		return x.DateOfBirth
	}
	return nil
}

func (x *CreateCustomerForNonResidentRequest) GetPermanentAddress() *postaladdress.PostalAddress {
	if x != nil {
		return x.PermanentAddress
	}
	return nil
}

func (x *CreateCustomerForNonResidentRequest) GetCurrentAddress() *postaladdress.PostalAddress {
	if x != nil {
		return x.CurrentAddress
	}
	return nil
}

func (x *CreateCustomerForNonResidentRequest) GetGender() typesv2.Gender {
	if x != nil {
		return x.Gender
	}
	return typesv2.Gender(0)
}

func (x *CreateCustomerForNonResidentRequest) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *CreateCustomerForNonResidentRequest) GetPanNumber() string {
	if x != nil {
		return x.PanNumber
	}
	return ""
}

func (x *CreateCustomerForNonResidentRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *CreateCustomerForNonResidentRequest) GetFatherName() string {
	if x != nil {
		return x.FatherName
	}
	return ""
}

func (x *CreateCustomerForNonResidentRequest) GetMotherName() string {
	if x != nil {
		return x.MotherName
	}
	return ""
}

func (x *CreateCustomerForNonResidentRequest) GetEmploymentType() employment.EmploymentType {
	if x != nil {
		return x.EmploymentType
	}
	return employment.EmploymentType(0)
}

func (x *CreateCustomerForNonResidentRequest) GetCustomerCreationFlow() CustomerCreationFlow {
	if x != nil {
		return x.CustomerCreationFlow
	}
	return CustomerCreationFlow_CUSTOMER_CREATION_FLOW_UNSPECIFIED
}

func (x *CreateCustomerForNonResidentRequest) GetOccupationType() employment.OccupationType {
	if x != nil {
		return x.OccupationType
	}
	return employment.OccupationType(0)
}

func (x *CreateCustomerForNonResidentRequest) GetSolId() string {
	if x != nil {
		return x.SolId
	}
	return ""
}

func (x *CreateCustomerForNonResidentRequest) GetPassportData() *typesv2.PassportData {
	if x != nil {
		return x.PassportData
	}
	return nil
}

func (x *CreateCustomerForNonResidentRequest) GetAnnualIncomeRange() *typesv2.SalaryRange {
	if x != nil {
		return x.AnnualIncomeRange
	}
	return nil
}

func (x *CreateCustomerForNonResidentRequest) GetSignImage() string {
	if x != nil {
		return x.SignImage
	}
	return ""
}

func (x *CreateCustomerForNonResidentRequest) GetMaritalStatus() typesv2.MaritalStatus {
	if x != nil {
		return x.MaritalStatus
	}
	return typesv2.MaritalStatus(0)
}

func (x *CreateCustomerForNonResidentRequest) GetForm_60OptedFlag() common.BooleanEnum {
	if x != nil {
		return x.Form_60OptedFlag
	}
	return common.BooleanEnum(0)
}

// Response for the CreateCustomerForNonResident method. Returns with either a success or
// failure. Actual response is to be obtained with a status check API.
type CreateCustomerForNonResidentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status       *rpc.Status                 `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	VendorStatus *vendorgateway.VendorStatus `protobuf:"bytes,2,opt,name=vendor_status,json=vendorStatus,proto3" json:"vendor_status,omitempty"`
}

func (x *CreateCustomerForNonResidentResponse) Reset() {
	*x = CreateCustomerForNonResidentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCustomerForNonResidentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomerForNonResidentResponse) ProtoMessage() {}

func (x *CreateCustomerForNonResidentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomerForNonResidentResponse.ProtoReflect.Descriptor instead.
func (*CreateCustomerForNonResidentResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{4}
}

func (x *CreateCustomerForNonResidentResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CreateCustomerForNonResidentResponse) GetVendorStatus() *vendorgateway.VendorStatus {
	if x != nil {
		return x.VendorStatus
	}
	return nil
}

// Request message for customer status enquiry.
type CheckCustomerStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all vendorgateway APIs.
	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// The same request id sent in the customer creation API.
	OriginalRequestId string `protobuf:"bytes,2,opt,name=original_request_id,json=originalRequestId,proto3" json:"original_request_id,omitempty"`
	// device auth details - device id, device token, user profile id
	DeviceDetails *header.Auth `protobuf:"bytes,3,opt,name=device_details,json=deviceDetails,proto3" json:"device_details,omitempty"`
	// mobile number
	MobileNumber string `protobuf:"bytes,4,opt,name=mobile_number,json=mobileNumber,proto3" json:"mobile_number,omitempty"`
}

func (x *CheckCustomerStatusRequest) Reset() {
	*x = CheckCustomerStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckCustomerStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckCustomerStatusRequest) ProtoMessage() {}

func (x *CheckCustomerStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckCustomerStatusRequest.ProtoReflect.Descriptor instead.
func (*CheckCustomerStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{5}
}

func (x *CheckCustomerStatusRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CheckCustomerStatusRequest) GetOriginalRequestId() string {
	if x != nil {
		return x.OriginalRequestId
	}
	return ""
}

func (x *CheckCustomerStatusRequest) GetDeviceDetails() *header.Auth {
	if x != nil {
		return x.DeviceDetails
	}
	return nil
}

func (x *CheckCustomerStatusRequest) GetMobileNumber() string {
	if x != nil {
		return x.MobileNumber
	}
	return ""
}

// Response message for customer status enquiry.
type CheckCustomerStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BankCustomerId string      `protobuf:"bytes,1,opt,name=bank_customer_id,json=bankCustomerId,proto3" json:"bank_customer_id,omitempty"`
	Status         *rpc.Status `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	// time at which customer creation was successful at vendor's end
	CreatedAt    *timestamppb.Timestamp      `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	VendorStatus *vendorgateway.VendorStatus `protobuf:"bytes,9,opt,name=vendor_status,json=vendorStatus,proto3" json:"vendor_status,omitempty"`
}

func (x *CheckCustomerStatusResponse) Reset() {
	*x = CheckCustomerStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckCustomerStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckCustomerStatusResponse) ProtoMessage() {}

func (x *CheckCustomerStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckCustomerStatusResponse.ProtoReflect.Descriptor instead.
func (*CheckCustomerStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{6}
}

func (x *CheckCustomerStatusResponse) GetBankCustomerId() string {
	if x != nil {
		return x.BankCustomerId
	}
	return ""
}

func (x *CheckCustomerStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CheckCustomerStatusResponse) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *CheckCustomerStatusResponse) GetVendorStatus() *vendorgateway.VendorStatus {
	if x != nil {
		return x.VendorStatus
	}
	return nil
}

type CheckCustomerStatusForNonResidentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all vendorgateway APIs.
	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// The same request id sent in the customer creation API.
	OriginalRequestId string `protobuf:"bytes,2,opt,name=original_request_id,json=originalRequestId,proto3" json:"original_request_id,omitempty"`
}

func (x *CheckCustomerStatusForNonResidentRequest) Reset() {
	*x = CheckCustomerStatusForNonResidentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckCustomerStatusForNonResidentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckCustomerStatusForNonResidentRequest) ProtoMessage() {}

func (x *CheckCustomerStatusForNonResidentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckCustomerStatusForNonResidentRequest.ProtoReflect.Descriptor instead.
func (*CheckCustomerStatusForNonResidentRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{7}
}

func (x *CheckCustomerStatusForNonResidentRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CheckCustomerStatusForNonResidentRequest) GetOriginalRequestId() string {
	if x != nil {
		return x.OriginalRequestId
	}
	return ""
}

type CheckCustomerStatusForNonResidentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status         *rpc.Status                 `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	BankCustomerId string                      `protobuf:"bytes,2,opt,name=bank_customer_id,json=bankCustomerId,proto3" json:"bank_customer_id,omitempty"`
	VendorStatus   *vendorgateway.VendorStatus `protobuf:"bytes,3,opt,name=vendor_status,json=vendorStatus,proto3" json:"vendor_status,omitempty"`
}

func (x *CheckCustomerStatusForNonResidentResponse) Reset() {
	*x = CheckCustomerStatusForNonResidentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckCustomerStatusForNonResidentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckCustomerStatusForNonResidentResponse) ProtoMessage() {}

func (x *CheckCustomerStatusForNonResidentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckCustomerStatusForNonResidentResponse.ProtoReflect.Descriptor instead.
func (*CheckCustomerStatusForNonResidentResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{8}
}

func (x *CheckCustomerStatusForNonResidentResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CheckCustomerStatusForNonResidentResponse) GetBankCustomerId() string {
	if x != nil {
		return x.BankCustomerId
	}
	return ""
}

func (x *CheckCustomerStatusForNonResidentResponse) GetVendorStatus() *vendorgateway.VendorStatus {
	if x != nil {
		return x.VendorStatus
	}
	return nil
}

type DedupeCheckRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all vendorgateway APIs.
	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// PAN no of the user
	PanNumber string `protobuf:"bytes,2,opt,name=pan_number,json=panNumber,proto3" json:"pan_number,omitempty"`
	// Aadhar number of the user
	AadhaarNumber string `protobuf:"bytes,3,opt,name=aadhaar_number,json=aadhaarNumber,proto3" json:"aadhaar_number,omitempty"`
	// Passport number of the user
	PassportNumber string `protobuf:"bytes,4,opt,name=passport_number,json=passportNumber,proto3" json:"passport_number,omitempty"`
	// Driving license of the user
	DrivingLicense string `protobuf:"bytes,5,opt,name=driving_license,json=drivingLicense,proto3" json:"driving_license,omitempty"`
	// Voter id of the user
	VoterId string `protobuf:"bytes,6,opt,name=voter_id,json=voterId,proto3" json:"voter_id,omitempty"`
	// mobile number of the user
	//
	// Deprecated: Marked as deprecated in api/vendorgateway/openbanking/customer/service.proto.
	MobileNum   string              `protobuf:"bytes,7,opt,name=mobile_num,json=mobileNum,proto3" json:"mobile_num,omitempty"`
	PhoneNumber *common.PhoneNumber `protobuf:"bytes,14,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// user id
	UserId string `protobuf:"bytes,8,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// denotes UidReferenceKey(rrn)
	UidReferenceKey string `protobuf:"bytes,10,opt,name=uid_reference_key,json=uidReferenceKey,proto3" json:"uid_reference_key,omitempty"`
	// date of birth of the user
	DateOfBirth *date.Date `protobuf:"bytes,11,opt,name=date_of_birth,json=dateOfBirth,proto3" json:"date_of_birth,omitempty"`
	// id to uniquely identify each request.
	RequestId string `protobuf:"bytes,12,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	EmailId   string `protobuf:"bytes,13,opt,name=email_id,json=emailId,proto3" json:"email_id,omitempty"`
	// It used to decide userId in dedupe request ('EPIFI' or 'EPIFINR')
	Flow dedupe.Flow `protobuf:"varint,15,opt,name=flow,proto3,enum=vendorgateway.openbanking.customer.dedupe.Flow" json:"flow,omitempty"`
}

func (x *DedupeCheckRequest) Reset() {
	*x = DedupeCheckRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DedupeCheckRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DedupeCheckRequest) ProtoMessage() {}

func (x *DedupeCheckRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DedupeCheckRequest.ProtoReflect.Descriptor instead.
func (*DedupeCheckRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{9}
}

func (x *DedupeCheckRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *DedupeCheckRequest) GetPanNumber() string {
	if x != nil {
		return x.PanNumber
	}
	return ""
}

func (x *DedupeCheckRequest) GetAadhaarNumber() string {
	if x != nil {
		return x.AadhaarNumber
	}
	return ""
}

func (x *DedupeCheckRequest) GetPassportNumber() string {
	if x != nil {
		return x.PassportNumber
	}
	return ""
}

func (x *DedupeCheckRequest) GetDrivingLicense() string {
	if x != nil {
		return x.DrivingLicense
	}
	return ""
}

func (x *DedupeCheckRequest) GetVoterId() string {
	if x != nil {
		return x.VoterId
	}
	return ""
}

// Deprecated: Marked as deprecated in api/vendorgateway/openbanking/customer/service.proto.
func (x *DedupeCheckRequest) GetMobileNum() string {
	if x != nil {
		return x.MobileNum
	}
	return ""
}

func (x *DedupeCheckRequest) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *DedupeCheckRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *DedupeCheckRequest) GetUidReferenceKey() string {
	if x != nil {
		return x.UidReferenceKey
	}
	return ""
}

func (x *DedupeCheckRequest) GetDateOfBirth() *date.Date {
	if x != nil {
		return x.DateOfBirth
	}
	return nil
}

func (x *DedupeCheckRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *DedupeCheckRequest) GetEmailId() string {
	if x != nil {
		return x.EmailId
	}
	return ""
}

func (x *DedupeCheckRequest) GetFlow() dedupe.Flow {
	if x != nil {
		return x.Flow
	}
	return dedupe.Flow(0)
}

type DedupeCheckResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Flag denoting if date of birth matches the bank record or not
	DobFlag string `protobuf:"bytes,3,opt,name=dob_flag,json=dobFlag,proto3" json:"dob_flag,omitempty"`
	// Flag denoting if mobile no matches the bank record or not
	MobileFlag string `protobuf:"bytes,4,opt,name=mobile_flag,json=mobileFlag,proto3" json:"mobile_flag,omitempty"`
	// Customer id of the user registered with bank
	CustomerId string `protobuf:"bytes,5,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// rpc status for the api response
	Status *rpc.Status `protobuf:"bytes,6,opt,name=status,proto3" json:"status,omitempty"`
	// Customer name
	CustomerName string       `protobuf:"bytes,7,opt,name=customer_name,json=customerName,proto3" json:"customer_name,omitempty"`
	DedupeStatus DedupeStatus `protobuf:"varint,8,opt,name=dedupe_status,json=dedupeStatus,proto3,enum=vendorgateway.openbanking.customer.DedupeStatus" json:"dedupe_status,omitempty"`
	// customers linked with phone number
	CustomersLinkedWithPhoneNumber int64 `protobuf:"varint,9,opt,name=customers_linked_with_phone_number,json=customersLinkedWithPhoneNumber,proto3" json:"customers_linked_with_phone_number,omitempty"`
	// raw response from vendor api
	RawResponse                string         `protobuf:"bytes,10,opt,name=raw_response,json=rawResponse,proto3" json:"raw_response,omitempty"`
	KYCFlag                    KYCFlag        `protobuf:"varint,11,opt,name=k_y_c_flag,json=kYCFlag,proto3,enum=vendorgateway.openbanking.customer.KYCFlag" json:"k_y_c_flag,omitempty"`
	CreditCardFlag             CreditCardFlag `protobuf:"varint,12,opt,name=credit_card_flag,json=creditCardFlag,proto3,enum=vendorgateway.openbanking.customer.CreditCardFlag" json:"credit_card_flag,omitempty"`
	CustomersLinkedWithEmailId int64          `protobuf:"varint,13,opt,name=customers_linked_with_email_id,json=customersLinkedWithEmailId,proto3" json:"customers_linked_with_email_id,omitempty"`
	// etb customer pan flag
	PanFlag PanFlag `protobuf:"varint,14,opt,name=pan_flag,json=panFlag,proto3,enum=vendorgateway.openbanking.customer.PanFlag" json:"pan_flag,omitempty"`
	// etb customer aadhaar flag
	AadhaarFlag  AadhaarFlag                 `protobuf:"varint,15,opt,name=aadhaar_flag,json=aadhaarFlag,proto3,enum=vendorgateway.openbanking.customer.AadhaarFlag" json:"aadhaar_flag,omitempty"`
	VendorStatus *vendorgateway.VendorStatus `protobuf:"bytes,16,opt,name=vendor_status,json=vendorStatus,proto3" json:"vendor_status,omitempty"`
}

func (x *DedupeCheckResponse) Reset() {
	*x = DedupeCheckResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DedupeCheckResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DedupeCheckResponse) ProtoMessage() {}

func (x *DedupeCheckResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DedupeCheckResponse.ProtoReflect.Descriptor instead.
func (*DedupeCheckResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{10}
}

func (x *DedupeCheckResponse) GetDobFlag() string {
	if x != nil {
		return x.DobFlag
	}
	return ""
}

func (x *DedupeCheckResponse) GetMobileFlag() string {
	if x != nil {
		return x.MobileFlag
	}
	return ""
}

func (x *DedupeCheckResponse) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

func (x *DedupeCheckResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *DedupeCheckResponse) GetCustomerName() string {
	if x != nil {
		return x.CustomerName
	}
	return ""
}

func (x *DedupeCheckResponse) GetDedupeStatus() DedupeStatus {
	if x != nil {
		return x.DedupeStatus
	}
	return DedupeStatus_DEDUPE_STATE_UNSPECIFIED
}

func (x *DedupeCheckResponse) GetCustomersLinkedWithPhoneNumber() int64 {
	if x != nil {
		return x.CustomersLinkedWithPhoneNumber
	}
	return 0
}

func (x *DedupeCheckResponse) GetRawResponse() string {
	if x != nil {
		return x.RawResponse
	}
	return ""
}

func (x *DedupeCheckResponse) GetKYCFlag() KYCFlag {
	if x != nil {
		return x.KYCFlag
	}
	return KYCFlag_KYC_FLAG_UNSPECIFIED
}

func (x *DedupeCheckResponse) GetCreditCardFlag() CreditCardFlag {
	if x != nil {
		return x.CreditCardFlag
	}
	return CreditCardFlag_CREDIT_CARD_FLAG_UNSPECIFIED
}

func (x *DedupeCheckResponse) GetCustomersLinkedWithEmailId() int64 {
	if x != nil {
		return x.CustomersLinkedWithEmailId
	}
	return 0
}

func (x *DedupeCheckResponse) GetPanFlag() PanFlag {
	if x != nil {
		return x.PanFlag
	}
	return PanFlag_PAN_FLAG_UNSPECIFIED
}

func (x *DedupeCheckResponse) GetAadhaarFlag() AadhaarFlag {
	if x != nil {
		return x.AadhaarFlag
	}
	return AadhaarFlag_AADHAAR_FLAG_UNSPECIFIED
}

func (x *DedupeCheckResponse) GetVendorStatus() *vendorgateway.VendorStatus {
	if x != nil {
		return x.VendorStatus
	}
	return nil
}

type FetchCustomerDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all vendorgateway APIs.
	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// device auth details - device id, device token, user profile id
	Auth        *header.Auth        `protobuf:"bytes,2,opt,name=auth,proto3" json:"auth,omitempty"`
	RequestId   string              `protobuf:"bytes,3,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	PhoneNumber *common.PhoneNumber `protobuf:"bytes,4,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	ChannelType ChannelType         `protobuf:"varint,5,opt,name=channel_type,json=channelType,proto3,enum=vendorgateway.openbanking.customer.ChannelType" json:"channel_type,omitempty"`
}

func (x *FetchCustomerDetailsRequest) Reset() {
	*x = FetchCustomerDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchCustomerDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchCustomerDetailsRequest) ProtoMessage() {}

func (x *FetchCustomerDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchCustomerDetailsRequest.ProtoReflect.Descriptor instead.
func (*FetchCustomerDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{11}
}

func (x *FetchCustomerDetailsRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *FetchCustomerDetailsRequest) GetAuth() *header.Auth {
	if x != nil {
		return x.Auth
	}
	return nil
}

func (x *FetchCustomerDetailsRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *FetchCustomerDetailsRequest) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *FetchCustomerDetailsRequest) GetChannelType() ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return ChannelType_CHANNEL_TYPE_UNSPECIFIED
}

type FetchCustomerDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status               *rpc.Status           `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	CustomerName         *common.Name          `protobuf:"bytes,2,opt,name=customer_name,json=customerName,proto3" json:"customer_name,omitempty"`
	Gender               typesv2.Gender        `protobuf:"varint,3,opt,name=gender,proto3,enum=api.typesv2.Gender" json:"gender,omitempty"`
	FatherOrHusbandName  *common.Name          `protobuf:"bytes,4,opt,name=father_or_husband_name,json=fatherOrHusbandName,proto3" json:"father_or_husband_name,omitempty"`
	MotherMaidenName     string                `protobuf:"bytes,5,opt,name=mother_maiden_name,json=motherMaidenName,proto3" json:"mother_maiden_name,omitempty"`
	PhoneNumber          *common.PhoneNumber   `protobuf:"bytes,6,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	EmailId              string                `protobuf:"bytes,7,opt,name=email_id,json=emailId,proto3" json:"email_id,omitempty"`
	DateOfBirth          *date.Date            `protobuf:"bytes,8,opt,name=date_of_birth,json=dateOfBirth,proto3" json:"date_of_birth,omitempty"`
	MaritalStatus        typesv2.MaritalStatus `protobuf:"varint,9,opt,name=marital_status,json=maritalStatus,proto3,enum=api.typesv2.MaritalStatus" json:"marital_status,omitempty"`
	Nre                  bool                  `protobuf:"varint,10,opt,name=Nre,proto3" json:"Nre,omitempty"`
	Occupation           string                `protobuf:"bytes,11,opt,name=occupation,proto3" json:"occupation,omitempty"`
	CommunicationAddress *VendorAddress        `protobuf:"bytes,12,opt,name=communication_address,json=communicationAddress,proto3" json:"communication_address,omitempty"`
	PermanentAddress     *VendorAddress        `protobuf:"bytes,13,opt,name=permanent_address,json=permanentAddress,proto3" json:"permanent_address,omitempty"`
	ShippingAddress      *VendorAddress        `protobuf:"bytes,14,opt,name=shipping_address,json=shippingAddress,proto3" json:"shipping_address,omitempty"`
	Salutation           string                `protobuf:"bytes,15,opt,name=salutation,proto3" json:"salutation,omitempty"`
	// list of all the accounts present for a customer at Federal's end
	// this includes savings and deposit accounts
	AccountList    []*Account                  `protobuf:"bytes,16,rep,name=account_list,json=accountList,proto3" json:"account_list,omitempty"`
	OccupationType employment.OccupationType   `protobuf:"varint,17,opt,name=occupation_type,json=occupationType,proto3,enum=employment.OccupationType" json:"occupation_type,omitempty"`
	VendorStatus   *vendorgateway.VendorStatus `protobuf:"bytes,18,opt,name=vendor_status,json=vendorStatus,proto3" json:"vendor_status,omitempty"`
}

func (x *FetchCustomerDetailsResponse) Reset() {
	*x = FetchCustomerDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchCustomerDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchCustomerDetailsResponse) ProtoMessage() {}

func (x *FetchCustomerDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchCustomerDetailsResponse.ProtoReflect.Descriptor instead.
func (*FetchCustomerDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{12}
}

func (x *FetchCustomerDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *FetchCustomerDetailsResponse) GetCustomerName() *common.Name {
	if x != nil {
		return x.CustomerName
	}
	return nil
}

func (x *FetchCustomerDetailsResponse) GetGender() typesv2.Gender {
	if x != nil {
		return x.Gender
	}
	return typesv2.Gender(0)
}

func (x *FetchCustomerDetailsResponse) GetFatherOrHusbandName() *common.Name {
	if x != nil {
		return x.FatherOrHusbandName
	}
	return nil
}

func (x *FetchCustomerDetailsResponse) GetMotherMaidenName() string {
	if x != nil {
		return x.MotherMaidenName
	}
	return ""
}

func (x *FetchCustomerDetailsResponse) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *FetchCustomerDetailsResponse) GetEmailId() string {
	if x != nil {
		return x.EmailId
	}
	return ""
}

func (x *FetchCustomerDetailsResponse) GetDateOfBirth() *date.Date {
	if x != nil {
		return x.DateOfBirth
	}
	return nil
}

func (x *FetchCustomerDetailsResponse) GetMaritalStatus() typesv2.MaritalStatus {
	if x != nil {
		return x.MaritalStatus
	}
	return typesv2.MaritalStatus(0)
}

func (x *FetchCustomerDetailsResponse) GetNre() bool {
	if x != nil {
		return x.Nre
	}
	return false
}

func (x *FetchCustomerDetailsResponse) GetOccupation() string {
	if x != nil {
		return x.Occupation
	}
	return ""
}

func (x *FetchCustomerDetailsResponse) GetCommunicationAddress() *VendorAddress {
	if x != nil {
		return x.CommunicationAddress
	}
	return nil
}

func (x *FetchCustomerDetailsResponse) GetPermanentAddress() *VendorAddress {
	if x != nil {
		return x.PermanentAddress
	}
	return nil
}

func (x *FetchCustomerDetailsResponse) GetShippingAddress() *VendorAddress {
	if x != nil {
		return x.ShippingAddress
	}
	return nil
}

func (x *FetchCustomerDetailsResponse) GetSalutation() string {
	if x != nil {
		return x.Salutation
	}
	return ""
}

func (x *FetchCustomerDetailsResponse) GetAccountList() []*Account {
	if x != nil {
		return x.AccountList
	}
	return nil
}

func (x *FetchCustomerDetailsResponse) GetOccupationType() employment.OccupationType {
	if x != nil {
		return x.OccupationType
	}
	return employment.OccupationType(0)
}

func (x *FetchCustomerDetailsResponse) GetVendorStatus() *vendorgateway.VendorStatus {
	if x != nil {
		return x.VendorStatus
	}
	return nil
}

type VendorAddress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AddressLine1 string `protobuf:"bytes,1,opt,name=address_line1,json=addressLine1,proto3" json:"address_line1,omitempty"`
	AddressLine2 string `protobuf:"bytes,2,opt,name=address_line2,json=addressLine2,proto3" json:"address_line2,omitempty"`
	CityCode     string `protobuf:"bytes,3,opt,name=city_code,json=cityCode,proto3" json:"city_code,omitempty"`
	StateCode    string `protobuf:"bytes,4,opt,name=state_code,json=stateCode,proto3" json:"state_code,omitempty"`
	CountryCode  string `protobuf:"bytes,5,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`
	PinCode      string `protobuf:"bytes,6,opt,name=pin_code,json=pinCode,proto3" json:"pin_code,omitempty"`
}

func (x *VendorAddress) Reset() {
	*x = VendorAddress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VendorAddress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VendorAddress) ProtoMessage() {}

func (x *VendorAddress) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VendorAddress.ProtoReflect.Descriptor instead.
func (*VendorAddress) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{13}
}

func (x *VendorAddress) GetAddressLine1() string {
	if x != nil {
		return x.AddressLine1
	}
	return ""
}

func (x *VendorAddress) GetAddressLine2() string {
	if x != nil {
		return x.AddressLine2
	}
	return ""
}

func (x *VendorAddress) GetCityCode() string {
	if x != nil {
		return x.CityCode
	}
	return ""
}

func (x *VendorAddress) GetStateCode() string {
	if x != nil {
		return x.StateCode
	}
	return ""
}

func (x *VendorAddress) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *VendorAddress) GetPinCode() string {
	if x != nil {
		return x.PinCode
	}
	return ""
}

type Account struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountNumber string `protobuf:"bytes,1,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	AccountType   string `protobuf:"bytes,2,opt,name=account_type,json=accountType,proto3" json:"account_type,omitempty"`
	SchemeCode    string `protobuf:"bytes,3,opt,name=scheme_code,json=schemeCode,proto3" json:"scheme_code,omitempty"`
}

func (x *Account) Reset() {
	*x = Account{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Account) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Account) ProtoMessage() {}

func (x *Account) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Account.ProtoReflect.Descriptor instead.
func (*Account) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{14}
}

func (x *Account) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *Account) GetAccountType() string {
	if x != nil {
		return x.AccountType
	}
	return ""
}

func (x *Account) GetSchemeCode() string {
	if x != nil {
		return x.SchemeCode
	}
	return ""
}

type CheckProductEligibilityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all vendorgateway APIs.
	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// unique identifier for this request
	RequestId string `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// user mobile number
	MobileNumber *common.PhoneNumber `protobuf:"bytes,3,opt,name=mobile_number,json=mobileNumber,proto3" json:"mobile_number,omitempty"`
	// user's pan number
	Pan string `protobuf:"bytes,4,opt,name=pan,proto3" json:"pan,omitempty"`
	// user's deviceId
	DeviceId string `protobuf:"bytes,5,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	// the type of product for which we are checking the eligibility
	ProductType ProductType `protobuf:"varint,6,opt,name=product_type,json=productType,proto3,enum=vendorgateway.openbanking.customer.ProductType" json:"product_type,omitempty"`
}

func (x *CheckProductEligibilityRequest) Reset() {
	*x = CheckProductEligibilityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckProductEligibilityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckProductEligibilityRequest) ProtoMessage() {}

func (x *CheckProductEligibilityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckProductEligibilityRequest.ProtoReflect.Descriptor instead.
func (*CheckProductEligibilityRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{15}
}

func (x *CheckProductEligibilityRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CheckProductEligibilityRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *CheckProductEligibilityRequest) GetMobileNumber() *common.PhoneNumber {
	if x != nil {
		return x.MobileNumber
	}
	return nil
}

func (x *CheckProductEligibilityRequest) GetPan() string {
	if x != nil {
		return x.Pan
	}
	return ""
}

func (x *CheckProductEligibilityRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *CheckProductEligibilityRequest) GetProductType() ProductType {
	if x != nil {
		return x.ProductType
	}
	return ProductType_PRODUCT_TYPE_UNSPECIFIED
}

type CheckProductEligibilityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// application reference id is the one with which the user's whole onboarding journey is done.
	ApplicationReferenceId string `protobuf:"bytes,2,opt,name=application_reference_id,json=applicationReferenceId,proto3" json:"application_reference_id,omitempty"`
	// tells us whether the customer is new or existing one to the bank - dedupe status
	DedupeStatus DedupeStatus `protobuf:"varint,3,opt,name=dedupe_status,json=dedupeStatus,proto3,enum=vendorgateway.openbanking.customer.DedupeStatus" json:"dedupe_status,omitempty"`
	// tells us whether the user is eligible
	IsEligible bool `protobuf:"varint,4,opt,name=is_eligible,json=isEligible,proto3" json:"is_eligible,omitempty"`
	// token that needs to be passed during eKYC
	EkycToken string `protobuf:"bytes,5,opt,name=ekyc_token,json=ekycToken,proto3" json:"ekyc_token,omitempty"`
	// url for doing eKYC
	EkycUrl string `protobuf:"bytes,6,opt,name=ekyc_url,json=ekycUrl,proto3" json:"ekyc_url,omitempty"`
	// code and description received from vendor
	VendorStatus *vendorgateway.VendorStatus `protobuf:"bytes,7,opt,name=vendor_status,json=vendorStatus,proto3" json:"vendor_status,omitempty"`
}

func (x *CheckProductEligibilityResponse) Reset() {
	*x = CheckProductEligibilityResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckProductEligibilityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckProductEligibilityResponse) ProtoMessage() {}

func (x *CheckProductEligibilityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckProductEligibilityResponse.ProtoReflect.Descriptor instead.
func (*CheckProductEligibilityResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{16}
}

func (x *CheckProductEligibilityResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CheckProductEligibilityResponse) GetApplicationReferenceId() string {
	if x != nil {
		return x.ApplicationReferenceId
	}
	return ""
}

func (x *CheckProductEligibilityResponse) GetDedupeStatus() DedupeStatus {
	if x != nil {
		return x.DedupeStatus
	}
	return DedupeStatus_DEDUPE_STATE_UNSPECIFIED
}

func (x *CheckProductEligibilityResponse) GetIsEligible() bool {
	if x != nil {
		return x.IsEligible
	}
	return false
}

func (x *CheckProductEligibilityResponse) GetEkycToken() string {
	if x != nil {
		return x.EkycToken
	}
	return ""
}

func (x *CheckProductEligibilityResponse) GetEkycUrl() string {
	if x != nil {
		return x.EkycUrl
	}
	return ""
}

func (x *CheckProductEligibilityResponse) GetVendorStatus() *vendorgateway.VendorStatus {
	if x != nil {
		return x.VendorStatus
	}
	return nil
}

type EnquireVKYCStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all vendorgateway APIs.
	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// ekycrrn of user
	EkycRrn string `protobuf:"bytes,2,opt,name=ekyc_rrn,json=ekycRrn,proto3" json:"ekyc_rrn,omitempty"`
}

func (x *EnquireVKYCStatusRequest) Reset() {
	*x = EnquireVKYCStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnquireVKYCStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnquireVKYCStatusRequest) ProtoMessage() {}

func (x *EnquireVKYCStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnquireVKYCStatusRequest.ProtoReflect.Descriptor instead.
func (*EnquireVKYCStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{17}
}

func (x *EnquireVKYCStatusRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *EnquireVKYCStatusRequest) GetEkycRrn() string {
	if x != nil {
		return x.EkycRrn
	}
	return ""
}

type EnquireVKYCStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// call status on federal end
	VkycStatus VKYCStatus `protobuf:"varint,2,opt,name=vkyc_status,json=vkycStatus,proto3,enum=vendorgateway.openbanking.customer.VKYCStatus" json:"vkyc_status,omitempty"`
	// remark for vkyc
	Message string `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	// vkyc callback date
	Date *date.Date `protobuf:"bytes,4,opt,name=date,proto3" json:"date,omitempty"`
}

func (x *EnquireVKYCStatusResponse) Reset() {
	*x = EnquireVKYCStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnquireVKYCStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnquireVKYCStatusResponse) ProtoMessage() {}

func (x *EnquireVKYCStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnquireVKYCStatusResponse.ProtoReflect.Descriptor instead.
func (*EnquireVKYCStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{18}
}

func (x *EnquireVKYCStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *EnquireVKYCStatusResponse) GetVkycStatus() VKYCStatus {
	if x != nil {
		return x.VkycStatus
	}
	return VKYCStatus_VKYC_STATUS_UNSPECIFIED
}

func (x *EnquireVKYCStatusResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *EnquireVKYCStatusResponse) GetDate() *date.Date {
	if x != nil {
		return x.Date
	}
	return nil
}

type UpgradeKYCLevelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all vendorgateway APIs.
	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// customer id of the user at the vendor's end
	VendorCustomerId string `protobuf:"bytes,2,opt,name=vendor_customer_id,json=vendorCustomerId,proto3" json:"vendor_customer_id,omitempty"`
	// ekyc rrn obtained post successful kyc
	EkycRrn string `protobuf:"bytes,3,opt,name=ekyc_rrn,json=ekycRrn,proto3" json:"ekyc_rrn,omitempty"`
	// sign capture during KYC process
	SignImage string `protobuf:"bytes,4,opt,name=sign_image,json=signImage,proto3" json:"sign_image,omitempty"`
}

func (x *UpgradeKYCLevelRequest) Reset() {
	*x = UpgradeKYCLevelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpgradeKYCLevelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpgradeKYCLevelRequest) ProtoMessage() {}

func (x *UpgradeKYCLevelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpgradeKYCLevelRequest.ProtoReflect.Descriptor instead.
func (*UpgradeKYCLevelRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{19}
}

func (x *UpgradeKYCLevelRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *UpgradeKYCLevelRequest) GetVendorCustomerId() string {
	if x != nil {
		return x.VendorCustomerId
	}
	return ""
}

func (x *UpgradeKYCLevelRequest) GetEkycRrn() string {
	if x != nil {
		return x.EkycRrn
	}
	return ""
}

func (x *UpgradeKYCLevelRequest) GetSignImage() string {
	if x != nil {
		return x.SignImage
	}
	return ""
}

type UpgradeKYCLevelResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// code and description received from vendor
	VendorStatus *vendorgateway.VendorStatus `protobuf:"bytes,2,opt,name=vendor_status,json=vendorStatus,proto3" json:"vendor_status,omitempty"`
}

func (x *UpgradeKYCLevelResponse) Reset() {
	*x = UpgradeKYCLevelResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpgradeKYCLevelResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpgradeKYCLevelResponse) ProtoMessage() {}

func (x *UpgradeKYCLevelResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpgradeKYCLevelResponse.ProtoReflect.Descriptor instead.
func (*UpgradeKYCLevelResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{20}
}

func (x *UpgradeKYCLevelResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *UpgradeKYCLevelResponse) GetVendorStatus() *vendorgateway.VendorStatus {
	if x != nil {
		return x.VendorStatus
	}
	return nil
}

type CreateLoanCustomerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header        *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	RequestId     string                       `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	SolId         string                       `protobuf:"bytes,3,opt,name=sol_id,json=solId,proto3" json:"sol_id,omitempty"`
	BreRefNumber  string                       `protobuf:"bytes,4,opt,name=bre_ref_number,json=breRefNumber,proto3" json:"bre_ref_number,omitempty"`
	UidNo         string                       `protobuf:"bytes,5,opt,name=uid_no,json=uidNo,proto3" json:"uid_no,omitempty"`
	Name          *common.Name                 `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
	FatherName    string                       `protobuf:"bytes,7,opt,name=father_name,json=fatherName,proto3" json:"father_name,omitempty"`
	MotherName    string                       `protobuf:"bytes,8,opt,name=mother_name,json=motherName,proto3" json:"mother_name,omitempty"`
	Email         string                       `protobuf:"bytes,9,opt,name=email,proto3" json:"email,omitempty"`
	DateOfBirth   *date.Date                   `protobuf:"bytes,10,opt,name=date_of_birth,json=dateOfBirth,proto3" json:"date_of_birth,omitempty"`
	Gender        typesv2.Gender               `protobuf:"varint,11,opt,name=gender,proto3,enum=api.typesv2.Gender" json:"gender,omitempty"`
	MaritalStatus typesv2.MaritalStatus        `protobuf:"varint,12,opt,name=marital_status,json=maritalStatus,proto3,enum=api.typesv2.MaritalStatus" json:"marital_status,omitempty"`
	PhoneNumber   *common.PhoneNumber          `protobuf:"bytes,13,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// Contact details of the customer.
	CommunicationAddress *postaladdress.PostalAddress `protobuf:"bytes,14,opt,name=communication_address,json=communicationAddress,proto3" json:"communication_address,omitempty"`
	PermanentAddress     *postaladdress.PostalAddress `protobuf:"bytes,15,opt,name=permanent_address,json=permanentAddress,proto3" json:"permanent_address,omitempty"`
	IdentityProof        *ProofDetails                `protobuf:"bytes,16,opt,name=identity_proof,json=identityProof,proto3" json:"identity_proof,omitempty"`
	// details of address proof
	AddressProof *ProofDetails `protobuf:"bytes,17,opt,name=address_proof,json=addressProof,proto3" json:"address_proof,omitempty"`
	PanNumber    string        `protobuf:"bytes,18,opt,name=pan_number,json=panNumber,proto3" json:"pan_number,omitempty"`
	// base64 string of the user's signature image
	SignImage      string                    `protobuf:"bytes,19,opt,name=sign_image,json=signImage,proto3" json:"sign_image,omitempty"`
	EmploymentType employment.EmploymentType `protobuf:"varint,20,opt,name=employment_type,json=employmentType,proto3,enum=employment.EmploymentType" json:"employment_type,omitempty"`
	AnnualIncome   float32                   `protobuf:"fixed32,21,opt,name=annual_income,json=annualIncome,proto3" json:"annual_income,omitempty"`
	OccupationType employment.OccupationType `protobuf:"varint,22,opt,name=occupation_type,json=occupationType,proto3,enum=employment.OccupationType" json:"occupation_type,omitempty"`
	Community      typesv2.Community         `protobuf:"varint,23,opt,name=community,proto3,enum=api.typesv2.Community" json:"community,omitempty"`
	Qualification  typesv2.Qualification     `protobuf:"varint,24,opt,name=qualification,proto3,enum=api.typesv2.Qualification" json:"qualification,omitempty"`
	Designation    typesv2.Designation       `protobuf:"varint,25,opt,name=designation,proto3,enum=api.typesv2.Designation" json:"designation,omitempty"`
	DisabilityType typesv2.DisabilityType    `protobuf:"varint,26,opt,name=disability_type,json=disabilityType,proto3,enum=api.typesv2.DisabilityType" json:"disability_type,omitempty"`
	Category       typesv2.Category          `protobuf:"varint,27,opt,name=category,proto3,enum=api.typesv2.Category" json:"category,omitempty"`
}

func (x *CreateLoanCustomerRequest) Reset() {
	*x = CreateLoanCustomerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateLoanCustomerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLoanCustomerRequest) ProtoMessage() {}

func (x *CreateLoanCustomerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLoanCustomerRequest.ProtoReflect.Descriptor instead.
func (*CreateLoanCustomerRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{21}
}

func (x *CreateLoanCustomerRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CreateLoanCustomerRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *CreateLoanCustomerRequest) GetSolId() string {
	if x != nil {
		return x.SolId
	}
	return ""
}

func (x *CreateLoanCustomerRequest) GetBreRefNumber() string {
	if x != nil {
		return x.BreRefNumber
	}
	return ""
}

func (x *CreateLoanCustomerRequest) GetUidNo() string {
	if x != nil {
		return x.UidNo
	}
	return ""
}

func (x *CreateLoanCustomerRequest) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *CreateLoanCustomerRequest) GetFatherName() string {
	if x != nil {
		return x.FatherName
	}
	return ""
}

func (x *CreateLoanCustomerRequest) GetMotherName() string {
	if x != nil {
		return x.MotherName
	}
	return ""
}

func (x *CreateLoanCustomerRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *CreateLoanCustomerRequest) GetDateOfBirth() *date.Date {
	if x != nil {
		return x.DateOfBirth
	}
	return nil
}

func (x *CreateLoanCustomerRequest) GetGender() typesv2.Gender {
	if x != nil {
		return x.Gender
	}
	return typesv2.Gender(0)
}

func (x *CreateLoanCustomerRequest) GetMaritalStatus() typesv2.MaritalStatus {
	if x != nil {
		return x.MaritalStatus
	}
	return typesv2.MaritalStatus(0)
}

func (x *CreateLoanCustomerRequest) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *CreateLoanCustomerRequest) GetCommunicationAddress() *postaladdress.PostalAddress {
	if x != nil {
		return x.CommunicationAddress
	}
	return nil
}

func (x *CreateLoanCustomerRequest) GetPermanentAddress() *postaladdress.PostalAddress {
	if x != nil {
		return x.PermanentAddress
	}
	return nil
}

func (x *CreateLoanCustomerRequest) GetIdentityProof() *ProofDetails {
	if x != nil {
		return x.IdentityProof
	}
	return nil
}

func (x *CreateLoanCustomerRequest) GetAddressProof() *ProofDetails {
	if x != nil {
		return x.AddressProof
	}
	return nil
}

func (x *CreateLoanCustomerRequest) GetPanNumber() string {
	if x != nil {
		return x.PanNumber
	}
	return ""
}

func (x *CreateLoanCustomerRequest) GetSignImage() string {
	if x != nil {
		return x.SignImage
	}
	return ""
}

func (x *CreateLoanCustomerRequest) GetEmploymentType() employment.EmploymentType {
	if x != nil {
		return x.EmploymentType
	}
	return employment.EmploymentType(0)
}

func (x *CreateLoanCustomerRequest) GetAnnualIncome() float32 {
	if x != nil {
		return x.AnnualIncome
	}
	return 0
}

func (x *CreateLoanCustomerRequest) GetOccupationType() employment.OccupationType {
	if x != nil {
		return x.OccupationType
	}
	return employment.OccupationType(0)
}

func (x *CreateLoanCustomerRequest) GetCommunity() typesv2.Community {
	if x != nil {
		return x.Community
	}
	return typesv2.Community(0)
}

func (x *CreateLoanCustomerRequest) GetQualification() typesv2.Qualification {
	if x != nil {
		return x.Qualification
	}
	return typesv2.Qualification(0)
}

func (x *CreateLoanCustomerRequest) GetDesignation() typesv2.Designation {
	if x != nil {
		return x.Designation
	}
	return typesv2.Designation(0)
}

func (x *CreateLoanCustomerRequest) GetDisabilityType() typesv2.DisabilityType {
	if x != nil {
		return x.DisabilityType
	}
	return typesv2.DisabilityType(0)
}

func (x *CreateLoanCustomerRequest) GetCategory() typesv2.Category {
	if x != nil {
		return x.Category
	}
	return typesv2.Category(0)
}

type CreateLoanCustomerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status       *rpc.Status                 `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	RequestId    string                      `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	VendorStatus *vendorgateway.VendorStatus `protobuf:"bytes,3,opt,name=vendor_status,json=vendorStatus,proto3" json:"vendor_status,omitempty"`
}

func (x *CreateLoanCustomerResponse) Reset() {
	*x = CreateLoanCustomerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateLoanCustomerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLoanCustomerResponse) ProtoMessage() {}

func (x *CreateLoanCustomerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLoanCustomerResponse.ProtoReflect.Descriptor instead.
func (*CreateLoanCustomerResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{22}
}

func (x *CreateLoanCustomerResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CreateLoanCustomerResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *CreateLoanCustomerResponse) GetVendorStatus() *vendorgateway.VendorStatus {
	if x != nil {
		return x.VendorStatus
	}
	return nil
}

type LoanCustomerCreationStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Todo(vipul/sharath) : Need to understand what is expected to be sent here
	RequestId string `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// Todo(vipul/sharath) : Need to understand what is expected to be sent here
	CifRequestId string `protobuf:"bytes,3,opt,name=cif_request_id,json=cifRequestId,proto3" json:"cif_request_id,omitempty"`
}

func (x *LoanCustomerCreationStatusRequest) Reset() {
	*x = LoanCustomerCreationStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanCustomerCreationStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanCustomerCreationStatusRequest) ProtoMessage() {}

func (x *LoanCustomerCreationStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanCustomerCreationStatusRequest.ProtoReflect.Descriptor instead.
func (*LoanCustomerCreationStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{23}
}

func (x *LoanCustomerCreationStatusRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *LoanCustomerCreationStatusRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *LoanCustomerCreationStatusRequest) GetCifRequestId() string {
	if x != nil {
		return x.CifRequestId
	}
	return ""
}

type LoanCustomerCreationStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId      string                      `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	CustomerId     string                      `protobuf:"bytes,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	CustomerName   string                      `protobuf:"bytes,3,opt,name=customer_name,json=customerName,proto3" json:"customer_name,omitempty"`
	Status         *rpc.Status                 `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`
	VendorStatus   *vendorgateway.VendorStatus `protobuf:"bytes,5,opt,name=vendor_status,json=vendorStatus,proto3" json:"vendor_status,omitempty"`
	CifCreatedTime *timestamppb.Timestamp      `protobuf:"bytes,6,opt,name=cif_created_time,json=cifCreatedTime,proto3" json:"cif_created_time,omitempty"`
}

func (x *LoanCustomerCreationStatusResponse) Reset() {
	*x = LoanCustomerCreationStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanCustomerCreationStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanCustomerCreationStatusResponse) ProtoMessage() {}

func (x *LoanCustomerCreationStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanCustomerCreationStatusResponse.ProtoReflect.Descriptor instead.
func (*LoanCustomerCreationStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP(), []int{24}
}

func (x *LoanCustomerCreationStatusResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *LoanCustomerCreationStatusResponse) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

func (x *LoanCustomerCreationStatusResponse) GetCustomerName() string {
	if x != nil {
		return x.CustomerName
	}
	return ""
}

func (x *LoanCustomerCreationStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *LoanCustomerCreationStatusResponse) GetVendorStatus() *vendorgateway.VendorStatus {
	if x != nil {
		return x.VendorStatus
	}
	return nil
}

func (x *LoanCustomerCreationStatusResponse) GetCifCreatedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CifCreatedTime
	}
	return nil
}

var File_api_vendorgateway_openbanking_customer_service_proto protoreflect.FileDescriptor

var file_api_vendorgateway_openbanking_customer_service_proto_rawDesc = []byte{
	0x0a, 0x34, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x22, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f,
	0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x65, 0x6d, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x11, 0x61, 0x70, 0x69, 0x2f, 0x6b, 0x79, 0x63, 0x2f, 0x6b, 0x79, 0x63, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2f, 0x70, 0x65, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x6e, 0x61, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x16, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x69, 0x73, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x18, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x67, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x6d, 0x61, 0x72, 0x69, 0x74, 0x61, 0x6c, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x38, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61,
	0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x64,
	0x65, 0x64, 0x75, 0x70, 0x65, 0x2f, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x95, 0x0b, 0x0a, 0x15,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61,
	0x6d, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x0d, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x6f, 0x66, 0x5f, 0x62, 0x69, 0x72, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61,
	0x74, 0x65, 0x52, 0x0b, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x42, 0x69, 0x72, 0x74, 0x68, 0x12,
	0x47, 0x0a, 0x11, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x10, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e,
	0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x43, 0x0a, 0x0f, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x0e, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x2b, 0x0a,
	0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x61, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x6e, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x14, 0x0a,
	0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x6f, 0x74, 0x68, 0x65,
	0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x57, 0x0a, 0x0e, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x5f, 0x70, 0x72, 0x6f, 0x6f, 0x66, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70,
	0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x6f, 0x66, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x0d, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x50, 0x72, 0x6f, 0x6f, 0x66, 0x12, 0x55,
	0x0a, 0x0d, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x6f, 0x66, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x6f, 0x66,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0c, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x50, 0x72, 0x6f, 0x6f, 0x66, 0x12, 0x4d, 0x0a, 0x0e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70,
	0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x2e, 0x41, 0x75, 0x74, 0x68, 0x52, 0x0d, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x75, 0x69, 0x64, 0x5f, 0x6e, 0x6f, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x75, 0x69, 0x64, 0x4e, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x73,
	0x69, 0x67, 0x6e, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x73, 0x69, 0x67, 0x6e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x2e, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x65, 0x6d, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x6e,
	0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x0c, 0x61, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x12,
	0x6e, 0x0a, 0x16, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x38, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x14, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6c, 0x6f, 0x77, 0x12,
	0x43, 0x0a, 0x0f, 0x6f, 0x63, 0x63, 0x75, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x65, 0x6d, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4f, 0x63, 0x63, 0x75, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x6f, 0x63, 0x63, 0x75, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x73, 0x6f, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x16,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x6f, 0x6c, 0x49, 0x64, 0x12, 0x44, 0x0a, 0x0f, 0x64,
	0x69, 0x73, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x17,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x44, 0x69, 0x73, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0e, 0x64, 0x69, 0x73, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x31, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x18, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x12, 0x3b, 0x0a, 0x0c, 0x70, 0x65, 0x70, 0x5f, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x50, 0x65, 0x70, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x52, 0x0b, 0x70, 0x65, 0x70, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x12, 0x40, 0x0a, 0x0d, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0xc1, 0x01, 0x0a, 0x0c, 0x50, 0x72, 0x6f, 0x6f, 0x66, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x24, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x10, 0x2e, 0x6b, 0x79, 0x63, 0x2e, 0x49, 0x64, 0x50, 0x72, 0x6f, 0x6f, 0x66,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x64,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69,
	0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x35, 0x0a, 0x0d, 0x69, 0x64, 0x5f, 0x69, 0x73,
	0x73, 0x75, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x52, 0x0b, 0x69, 0x64, 0x49, 0x73, 0x73, 0x75, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x37,
	0x0a, 0x0e, 0x69, 0x64, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0c, 0x69, 0x64, 0x45, 0x78, 0x70,
	0x69, 0x72, 0x79, 0x44, 0x61, 0x74, 0x65, 0x22, 0x89, 0x03, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x40, 0x0a, 0x0d, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x87, 0x02, 0x0a, 0x06, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16,
	0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x53, 0x45, 0x4e, 0x44, 0x45, 0x52, 0x5f, 0x44,
	0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x64, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x45, 0x4e, 0x44,
	0x45, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x44, 0x10, 0x65,
	0x12, 0x17, 0x0a, 0x13, 0x44, 0x55, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x44, 0x10, 0x66, 0x12, 0x10, 0x0a, 0x0c, 0x4e, 0x4f, 0x5f,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x44, 0x10, 0x67, 0x12, 0x0d, 0x0a, 0x09, 0x45,
	0x58, 0x43, 0x45, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x68, 0x12, 0x12, 0x0a, 0x0e, 0x49, 0x4e,
	0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x10, 0x69, 0x12, 0x10,
	0x0a, 0x0c, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x10, 0x6a,
	0x12, 0x11, 0x0a, 0x0d, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4e,
	0x47, 0x10, 0x6b, 0x12, 0x11, 0x0a, 0x0d, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x49,
	0x4e, 0x50, 0x55, 0x54, 0x10, 0x6c, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d,
	0x45, 0x52, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x50, 0x52,
	0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x6d, 0x12, 0x19, 0x0a, 0x15, 0x44, 0x55, 0x50, 0x4c,
	0x49, 0x43, 0x41, 0x54, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x49,
	0x44, 0x10, 0x6e, 0x22, 0xa0, 0x09, 0x0a, 0x23, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x4e, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x69,
	0x64, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64,
	0x12, 0x2c, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x35,
	0x0a, 0x0d, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x62, 0x69, 0x72, 0x74, 0x68, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66,
	0x42, 0x69, 0x72, 0x74, 0x68, 0x12, 0x47, 0x0a, 0x11, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65,
	0x6e, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x50,
	0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x10, 0x70, 0x65,
	0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x43,
	0x0a, 0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x52, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x12, 0x2b, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x12, 0x42, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e,
	0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x6e, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x61, 0x74,
	0x68, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x6f,
	0x74, 0x68, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x43, 0x0a, 0x0f, 0x65,
	0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0e, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x6e, 0x0a, 0x16, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x38, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x14, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6c, 0x6f, 0x77,
	0x12, 0x43, 0x0a, 0x0f, 0x6f, 0x63, 0x63, 0x75, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x65, 0x6d, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4f, 0x63, 0x63, 0x75, 0x70, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x6f, 0x63, 0x63, 0x75, 0x70, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x73, 0x6f, 0x6c, 0x5f, 0x69, 0x64, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x6f, 0x6c, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x0d,
	0x70, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x12, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x50, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0c,
	0x70, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x48, 0x0a, 0x13,
	0x61, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x5f, 0x72, 0x61,
	0x6e, 0x67, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x52, 0x61,
	0x6e, 0x67, 0x65, 0x52, 0x11, 0x61, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x49, 0x6e, 0x63, 0x6f, 0x6d,
	0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x41, 0x0a, 0x0e, 0x6d, 0x61, 0x72, 0x69, 0x74, 0x61, 0x6c,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4d, 0x61, 0x72, 0x69,
	0x74, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x6d, 0x61, 0x72, 0x69, 0x74,
	0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4c, 0x0a, 0x12, 0x66, 0x6f, 0x72, 0x6d,
	0x5f, 0x36, 0x30, 0x5f, 0x6f, 0x70, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x16,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61,
	0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0f, 0x66, 0x6f, 0x72, 0x6d, 0x36, 0x30, 0x4f, 0x70, 0x74,
	0x65, 0x64, 0x46, 0x6c, 0x61, 0x67, 0x22, 0x97, 0x03, 0x0a, 0x24, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x4e, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x40, 0x0a, 0x0d, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x87, 0x02, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x49, 0x4e, 0x56,
	0x41, 0x4c, 0x49, 0x44, 0x5f, 0x53, 0x45, 0x4e, 0x44, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x54, 0x41,
	0x49, 0x4c, 0x53, 0x10, 0x64, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x45, 0x4e, 0x44, 0x45, 0x52, 0x5f,
	0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x44, 0x10, 0x65, 0x12, 0x17, 0x0a,
	0x13, 0x44, 0x55, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x49, 0x44, 0x10, 0x66, 0x12, 0x10, 0x0a, 0x0c, 0x4e, 0x4f, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x49, 0x44, 0x10, 0x67, 0x12, 0x0d, 0x0a, 0x09, 0x45, 0x58, 0x43, 0x45,
	0x50, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x68, 0x12, 0x12, 0x0a, 0x0e, 0x49, 0x4e, 0x56, 0x41, 0x4c,
	0x49, 0x44, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x10, 0x69, 0x12, 0x10, 0x0a, 0x0c, 0x49,
	0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x10, 0x6a, 0x12, 0x11, 0x0a,
	0x0d, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x10, 0x6b,
	0x12, 0x11, 0x0a, 0x0d, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x49, 0x4e, 0x50, 0x55,
	0x54, 0x10, 0x6c, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f,
	0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x50, 0x52, 0x4f, 0x47, 0x52,
	0x45, 0x53, 0x53, 0x10, 0x6d, 0x12, 0x19, 0x0a, 0x15, 0x44, 0x55, 0x50, 0x4c, 0x49, 0x43, 0x41,
	0x54, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x6e,
	0x22, 0xf6, 0x01, 0x0a, 0x1a, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2e, 0x0a, 0x13, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61,
	0x6c, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x11, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x4d, 0x0a, 0x0e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70,
	0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x2e, 0x41, 0x75, 0x74, 0x68, 0x52, 0x0d, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x6f, 0x62,
	0x69, 0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x9d, 0x04, 0x0a, 0x1b, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x62, 0x61, 0x6e,
	0x6b, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x62, 0x61, 0x6e, 0x6b, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x40, 0x0a, 0x0d, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xb1, 0x02, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x45, 0x4e, 0x44,
	0x45, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x44, 0x10, 0x64,
	0x12, 0x11, 0x0a, 0x0d, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x49, 0x4e, 0x50, 0x55,
	0x54, 0x10, 0x65, 0x12, 0x10, 0x0a, 0x0c, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x45, 0x52,
	0x52, 0x4f, 0x52, 0x10, 0x66, 0x12, 0x11, 0x0a, 0x0d, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x49, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x10, 0x67, 0x12, 0x14, 0x0a, 0x10, 0x53, 0x4f, 0x43, 0x4b,
	0x45, 0x54, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x68, 0x12, 0x0d,
	0x0a, 0x09, 0x45, 0x58, 0x43, 0x45, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x69, 0x12, 0x15, 0x0a,
	0x11, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55,
	0x4e, 0x44, 0x10, 0x6a, 0x12, 0x21, 0x0a, 0x1d, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x49,
	0x4e, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x54, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x4c, 0x41, 0x4b,
	0x49, 0x4e, 0x45, 0x53, 0x53, 0x10, 0x6b, 0x12, 0x0e, 0x0a, 0x0a, 0x4b, 0x59, 0x43, 0x5f, 0x46,
	0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x6c, 0x12, 0x18, 0x0a, 0x14, 0x49, 0x4e, 0x56, 0x41, 0x4c,
	0x49, 0x44, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x10,
	0x6d, 0x12, 0x29, 0x0a, 0x25, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x4e, 0x4f, 0x54,
	0x5f, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x53,
	0x45, 0x4e, 0x44, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x6e, 0x12, 0x1b, 0x0a, 0x17,
	0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x6f, 0x22, 0x90, 0x01, 0x0a, 0x28, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x46, 0x6f, 0x72, 0x4e, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2e, 0x0a, 0x13,
	0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6f, 0x72, 0x69, 0x67, 0x69,
	0x6e, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0xf0, 0x03, 0x0a,
	0x29, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x46, 0x6f, 0x72, 0x4e, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x69, 0x64, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x28, 0x0a, 0x10, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x62, 0x61, 0x6e, 0x6b, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x0d, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xb1, 0x02, 0x0a, 0x06,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x16,
	0x0a, 0x12, 0x53, 0x45, 0x4e, 0x44, 0x45, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x4e, 0x41,
	0x42, 0x4c, 0x45, 0x44, 0x10, 0x64, 0x12, 0x11, 0x0a, 0x0d, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49,
	0x44, 0x5f, 0x49, 0x4e, 0x50, 0x55, 0x54, 0x10, 0x65, 0x12, 0x10, 0x0a, 0x0c, 0x53, 0x45, 0x52,
	0x56, 0x45, 0x52, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x66, 0x12, 0x11, 0x0a, 0x0d, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x10, 0x67, 0x12, 0x14,
	0x0a, 0x10, 0x53, 0x4f, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x50, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0x68, 0x12, 0x0d, 0x0a, 0x09, 0x45, 0x58, 0x43, 0x45, 0x50, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0x69, 0x12, 0x15, 0x0a, 0x11, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x4e,
	0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x6a, 0x12, 0x21, 0x0a, 0x1d, 0x56, 0x45,
	0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x54, 0x54, 0x45, 0x4e,
	0x54, 0x5f, 0x46, 0x4c, 0x41, 0x4b, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x10, 0x6b, 0x12, 0x0e, 0x0a,
	0x0a, 0x4b, 0x59, 0x43, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x6c, 0x12, 0x18, 0x0a,
	0x14, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f,
	0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x10, 0x6d, 0x12, 0x29, 0x0a, 0x25, 0x53, 0x45, 0x52, 0x56, 0x49,
	0x43, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41, 0x42, 0x4c, 0x45,
	0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x53, 0x45, 0x4e, 0x44, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x44, 0x45,
	0x10, 0x6e, 0x12, 0x1b, 0x0a, 0x17, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x41,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x6f, 0x22,
	0xdf, 0x04, 0x0a, 0x12, 0x44, 0x65, 0x64, 0x75, 0x70, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a,
	0x70, 0x61, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x70, 0x61, 0x6e, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x61,
	0x61, 0x64, 0x68, 0x61, 0x61, 0x72, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x61, 0x64, 0x68, 0x61, 0x61, 0x72, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x61, 0x73,
	0x73, 0x70, 0x6f, 0x72, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x27, 0x0a, 0x0f, 0x64,
	0x72, 0x69, 0x76, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x72, 0x69, 0x76, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x63,
	0x65, 0x6e, 0x73, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x76, 0x6f, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x6f, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x21, 0x0a, 0x0a, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x09, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x4e,
	0x75, 0x6d, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68,
	0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x2a, 0x0a, 0x11, 0x75, 0x69, 0x64, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x5f, 0x6b, 0x65, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x75, 0x69, 0x64, 0x52,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x35, 0x0a, 0x0d, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x62, 0x69, 0x72, 0x74, 0x68, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x42, 0x69, 0x72,
	0x74, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49,
	0x64, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x43, 0x0a, 0x04,
	0x66, 0x6c, 0x6f, 0x77, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2f, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62,
	0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e,
	0x64, 0x65, 0x64, 0x75, 0x70, 0x65, 0x2e, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x04, 0x66, 0x6c, 0x6f,
	0x77, 0x22, 0xcc, 0x06, 0x0a, 0x13, 0x44, 0x65, 0x64, 0x75, 0x70, 0x65, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x6f, 0x62,
	0x5f, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x6f, 0x62,
	0x46, 0x6c, 0x61, 0x67, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x5f, 0x66,
	0x6c, 0x61, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x6f, 0x62, 0x69, 0x6c,
	0x65, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x55, 0x0a, 0x0d, 0x64, 0x65, 0x64, 0x75, 0x70, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x64,
	0x75, 0x70, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x64, 0x65, 0x64, 0x75, 0x70,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4a, 0x0a, 0x22, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x73, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x5f, 0x77, 0x69, 0x74, 0x68,
	0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x1e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x4c, 0x69,
	0x6e, 0x6b, 0x65, 0x64, 0x57, 0x69, 0x74, 0x68, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x61, 0x77, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x61, 0x77, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x48, 0x0a, 0x0a, 0x6b, 0x5f, 0x79, 0x5f, 0x63, 0x5f,
	0x66, 0x6c, 0x61, 0x67, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62,
	0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e,
	0x4b, 0x59, 0x43, 0x46, 0x6c, 0x61, 0x67, 0x52, 0x07, 0x6b, 0x59, 0x43, 0x46, 0x6c, 0x61, 0x67,
	0x12, 0x5c, 0x0a, 0x10, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f,
	0x66, 0x6c, 0x61, 0x67, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62,
	0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e,
	0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x46, 0x6c, 0x61, 0x67, 0x52, 0x0e,
	0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x42,
	0x0a, 0x1e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x5f, 0x6c, 0x69, 0x6e, 0x6b,
	0x65, 0x64, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x1a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x73, 0x4c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x57, 0x69, 0x74, 0x68, 0x45, 0x6d, 0x61, 0x69, 0x6c,
	0x49, 0x64, 0x12, 0x46, 0x0a, 0x08, 0x70, 0x61, 0x6e, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x50, 0x61, 0x6e, 0x46, 0x6c, 0x61,
	0x67, 0x52, 0x07, 0x70, 0x61, 0x6e, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x52, 0x0a, 0x0c, 0x61, 0x61,
	0x64, 0x68, 0x61, 0x61, 0x72, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x41, 0x61, 0x64, 0x68, 0x61, 0x61, 0x72, 0x46, 0x6c, 0x61,
	0x67, 0x52, 0x0b, 0x61, 0x61, 0x64, 0x68, 0x61, 0x61, 0x72, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x40,
	0x0a, 0x0d, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x0c, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0xc6, 0x02, 0x0a, 0x1b, 0x46, 0x65, 0x74, 0x63, 0x68, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x3a, 0x0a, 0x04, 0x61, 0x75, 0x74, 0x68, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x52, 0x04, 0x61, 0x75,
	0x74, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49,
	0x64, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f,
	0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x52, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2f, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e,
	0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x22, 0xe6, 0x08, 0x0a, 0x1c, 0x46, 0x65,
	0x74, 0x63, 0x68, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x3d, 0x0a, 0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65,
	0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2b,
	0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x4d, 0x0a, 0x16, 0x66,
	0x61, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x6f, 0x72, 0x5f, 0x68, 0x75, 0x73, 0x62, 0x61, 0x6e, 0x64,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x13, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x4f, 0x72, 0x48,
	0x75, 0x73, 0x62, 0x61, 0x6e, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x6d, 0x6f,
	0x74, 0x68, 0x65, 0x72, 0x5f, 0x6d, 0x61, 0x69, 0x64, 0x65, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x4d, 0x61,
	0x69, 0x64, 0x65, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52,
	0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x35, 0x0a, 0x0d, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x6f, 0x66, 0x5f, 0x62, 0x69, 0x72, 0x74, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x52, 0x0b, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x42, 0x69, 0x72, 0x74, 0x68, 0x12, 0x41,
	0x0a, 0x0e, 0x6d, 0x61, 0x72, 0x69, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x4d, 0x61, 0x72, 0x69, 0x74, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x0d, 0x6d, 0x61, 0x72, 0x69, 0x74, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x10, 0x0a, 0x03, 0x4e, 0x72, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x03,
	0x4e, 0x72, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x6f, 0x63, 0x63, 0x75, 0x70, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x63, 0x63, 0x75, 0x70, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x66, 0x0a, 0x15, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x31, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x14, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x5e, 0x0a, 0x11, 0x70,
	0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x56, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x10, 0x70, 0x65, 0x72, 0x6d, 0x61,
	0x6e, 0x65, 0x6e, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x5c, 0x0a, 0x10, 0x73,
	0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x0f, 0x73, 0x68, 0x69, 0x70, 0x70, 0x69,
	0x6e, 0x67, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x61, 0x6c,
	0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73,
	0x61, 0x6c, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4e, 0x0a, 0x0c, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2b, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x0b, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0f, 0x6f, 0x63, 0x63,
	0x75, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x4f, 0x63, 0x63, 0x75, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e,
	0x6f, 0x63, 0x63, 0x75, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x40,
	0x0a, 0x0d, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x0c, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0x10, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b,
	0x10, 0x00, 0x22, 0xd3, 0x01, 0x0a, 0x0d, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f,
	0x6c, 0x69, 0x6e, 0x65, 0x31, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x6e, 0x65, 0x31, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x32, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x6e, 0x65, 0x32, 0x12, 0x1b,
	0x0a, 0x09, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x63, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x73, 0x74, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x19, 0x0a,
	0x08, 0x70, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x70, 0x69, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x74, 0x0a, 0x07, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x22, 0xbe,
	0x02, 0x0a, 0x1e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x45,
	0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x44, 0x0a, 0x0d, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0c,
	0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03,
	0x70, 0x61, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x70, 0x61, 0x6e, 0x12, 0x1b,
	0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x52, 0x0a, 0x0c, 0x70,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22,
	0xf4, 0x02, 0x0a, 0x1f, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x38, 0x0a, 0x18, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x49, 0x64, 0x12, 0x55, 0x0a, 0x0d, 0x64, 0x65, 0x64, 0x75, 0x70, 0x65, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61,
	0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x44,
	0x65, 0x64, 0x75, 0x70, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x64, 0x65, 0x64,
	0x75, 0x70, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f,
	0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a,
	0x69, 0x73, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x6b,
	0x79, 0x63, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x65, 0x6b, 0x79, 0x63, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6b, 0x79,
	0x63, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6b, 0x79,
	0x63, 0x55, 0x72, 0x6c, 0x12, 0x40, 0x0a, 0x0d, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x6b, 0x0a, 0x18, 0x45, 0x6e, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x56, 0x4b, 0x59, 0x43, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6b, 0x79, 0x63,
	0x5f, 0x72, 0x72, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6b, 0x79, 0x63,
	0x52, 0x72, 0x6e, 0x22, 0xf1, 0x02, 0x0a, 0x19, 0x45, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x65, 0x56,
	0x4b, 0x59, 0x43, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4f, 0x0a, 0x0b, 0x76, 0x6b, 0x79, 0x63, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e,
	0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x56, 0x4b, 0x59, 0x43, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0a, 0x76, 0x6b, 0x79,
	0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x25, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61,
	0x74, 0x65, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x22, 0x9c, 0x01, 0x0a, 0x06, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x00,
	0x12, 0x11, 0x0a, 0x0d, 0x50, 0x41, 0x52, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x52, 0x52, 0x4f,
	0x52, 0x10, 0x65, 0x12, 0x12, 0x0a, 0x0e, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x49,
	0x4e, 0x50, 0x55, 0x54, 0x53, 0x10, 0x66, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52,
	0x4e, 0x41, 0x4c, 0x10, 0x67, 0x12, 0x1c, 0x0a, 0x18, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x4f,
	0x52, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x53, 0x5f, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4e,
	0x47, 0x10, 0x68, 0x12, 0x18, 0x0a, 0x14, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x43,
	0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x69, 0x12, 0x18, 0x0a,
	0x14, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x6a, 0x22, 0xb6, 0x01, 0x0a, 0x16, 0x55, 0x70, 0x67, 0x72,
	0x61, 0x64, 0x65, 0x4b, 0x59, 0x43, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x12, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6b, 0x79, 0x63, 0x5f, 0x72,
	0x72, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6b, 0x79, 0x63, 0x52, 0x72,
	0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x22, 0x80, 0x01, 0x0a, 0x17, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x4b, 0x59, 0x43, 0x4c,
	0x65, 0x76, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x40, 0x0a, 0x0d, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x99, 0x0b, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f,
	0x61, 0x6e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x73, 0x6f, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x6f, 0x6c, 0x49, 0x64, 0x12, 0x24, 0x0a,
	0x0e, 0x62, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x66, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x72, 0x65, 0x52, 0x65, 0x66, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x15, 0x0a, 0x06, 0x75, 0x69, 0x64, 0x5f, 0x6e, 0x6f, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x75, 0x69, 0x64, 0x4e, 0x6f, 0x12, 0x2c, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61,
	0x6d, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x61, 0x74, 0x68,
	0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66,
	0x61, 0x74, 0x68, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x6f, 0x74,
	0x68, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x12, 0x35, 0x0a, 0x0d, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x62, 0x69, 0x72, 0x74,
	0x68, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x64, 0x61, 0x74, 0x65,
	0x4f, 0x66, 0x42, 0x69, 0x72, 0x74, 0x68, 0x12, 0x2b, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x52, 0x06, 0x67, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x12, 0x41, 0x0a, 0x0e, 0x6d, 0x61, 0x72, 0x69, 0x74, 0x61, 0x6c, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4d, 0x61, 0x72, 0x69, 0x74,
	0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x6d, 0x61, 0x72, 0x69, 0x74, 0x61,
	0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0b,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x4f, 0x0a, 0x15, 0x63,
	0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x14, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x47, 0x0a, 0x11,
	0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x52, 0x10, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x57, 0x0a, 0x0e, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x5f, 0x70, 0x72, 0x6f, 0x6f, 0x66, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70,
	0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x6f, 0x66, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x0d, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x50, 0x72, 0x6f, 0x6f, 0x66, 0x12, 0x55,
	0x0a, 0x0d, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x6f, 0x66, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x6f, 0x66,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0c, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x50, 0x72, 0x6f, 0x6f, 0x66, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x6e, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x6e, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x12, 0x43, 0x0a, 0x0f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x65,
	0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x6e, 0x6e, 0x75,
	0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x0c, 0x61, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x12, 0x43, 0x0a,
	0x0f, 0x6f, 0x63, 0x63, 0x75, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x16, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x4f, 0x63, 0x63, 0x75, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0e, 0x6f, 0x63, 0x63, 0x75, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x34, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x18,
	0x17, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x52, 0x09, 0x63,
	0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x12, 0x40, 0x0a, 0x0d, 0x71, 0x75, 0x61, 0x6c,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x51, 0x75,
	0x61, 0x6c, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x71, 0x75, 0x61,
	0x6c, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3a, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x44, 0x65,
	0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x69, 0x67,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x44, 0x0a, 0x0f, 0x64, 0x69, 0x73, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x44, 0x69,
	0x73, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x64, 0x69,
	0x73, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x31, 0x0a, 0x08,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x22,
	0xa2, 0x01, 0x0a, 0x1a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x61, 0x6e, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x49, 0x64, 0x12, 0x40, 0x0a, 0x0d, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0x9e, 0x01, 0x0a, 0x21, 0x4c, 0x6f, 0x61, 0x6e, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12,
	0x24, 0x0a, 0x0e, 0x63, 0x69, 0x66, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x69, 0x66, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0xb6, 0x02, 0x0a, 0x22, 0x4c, 0x6f, 0x61, 0x6e, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x40, 0x0a, 0x0d, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x44, 0x0a, 0x10, 0x63, 0x69, 0x66, 0x5f,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0e,
	0x63, 0x69, 0x66, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x2a, 0xb9,
	0x08, 0x0a, 0x0c, 0x44, 0x65, 0x64, 0x75, 0x70, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x1c, 0x0a, 0x18, 0x44, 0x45, 0x44, 0x55, 0x50, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1d, 0x0a,
	0x19, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x44, 0x4f, 0x45, 0x53, 0x54, 0x5f,
	0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06,
	0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x55, 0x53, 0x54,
	0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x10, 0x03, 0x12, 0x28, 0x0a,
	0x20, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53,
	0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54, 0x43,
	0x48, 0x10, 0x04, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x43, 0x55, 0x53, 0x54, 0x4f,
	0x4d, 0x45, 0x52, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x5f, 0x4e, 0x52, 0x49, 0x10, 0x05,
	0x12, 0x19, 0x0a, 0x15, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x45, 0x58, 0x49,
	0x53, 0x54, 0x53, 0x5f, 0x4d, 0x49, 0x4e, 0x4f, 0x52, 0x10, 0x06, 0x12, 0x1f, 0x0a, 0x1b, 0x43,
	0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x5f, 0x50,
	0x41, 0x52, 0x54, 0x49, 0x41, 0x4c, 0x5f, 0x4b, 0x59, 0x43, 0x10, 0x07, 0x12, 0x28, 0x0a, 0x24,
	0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x5f,
	0x4d, 0x55, 0x4c, 0x54, 0x49, 0x50, 0x4c, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45,
	0x52, 0x5f, 0x49, 0x44, 0x10, 0x08, 0x12, 0x22, 0x0a, 0x1e, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d,
	0x45, 0x52, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f,
	0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x09, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x55,
	0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x5f, 0x44, 0x4f,
	0x42, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x0a, 0x12, 0x3c, 0x0a, 0x38,
	0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x5f,
	0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x4c, 0x49, 0x4e,
	0x4b, 0x45, 0x44, 0x5f, 0x54, 0x4f, 0x5f, 0x4d, 0x55, 0x4c, 0x54, 0x49, 0x50, 0x4c, 0x45, 0x5f,
	0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x53, 0x10, 0x0b, 0x12, 0x3b, 0x0a, 0x37, 0x43, 0x55,
	0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x5f, 0x50, 0x48,
	0x4f, 0x4e, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x45,
	0x44, 0x5f, 0x54, 0x4f, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x43,
	0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x0c, 0x12, 0x27, 0x0a, 0x23, 0x43, 0x55, 0x53, 0x54, 0x4f,
	0x4d, 0x45, 0x52, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x5f, 0x4d, 0x49, 0x53, 0x53, 0x49,
	0x4e, 0x47, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x0d,
	0x12, 0x23, 0x0a, 0x1f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x45, 0x58, 0x49,
	0x53, 0x54, 0x53, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x49, 0x41, 0x4c, 0x5f, 0x50, 0x52, 0x4f, 0x46,
	0x49, 0x4c, 0x45, 0x10, 0x0e, 0x12, 0x0b, 0x0a, 0x07, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x45, 0x44,
	0x10, 0x0f, 0x12, 0x1d, 0x0a, 0x19, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x45, 0x44, 0x5f, 0x41, 0x53,
	0x5f, 0x44, 0x41, 0x59, 0x53, 0x5f, 0x50, 0x41, 0x53, 0x54, 0x5f, 0x44, 0x55, 0x45, 0x53, 0x10,
	0x10, 0x12, 0x22, 0x0a, 0x1e, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x45, 0x58,
	0x49, 0x53, 0x54, 0x53, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41,
	0x54, 0x43, 0x48, 0x10, 0x11, 0x12, 0x35, 0x0a, 0x31, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45,
	0x52, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x4c,
	0x49, 0x4e, 0x4b, 0x45, 0x44, 0x5f, 0x54, 0x4f, 0x5f, 0x4d, 0x55, 0x4c, 0x54, 0x49, 0x50, 0x4c,
	0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x53, 0x10, 0x12, 0x12, 0x34, 0x0a, 0x30,
	0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x5f,
	0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x45, 0x44, 0x5f, 0x54, 0x4f, 0x5f,
	0x45, 0x58, 0x49, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x10, 0x13, 0x12, 0x21, 0x0a, 0x1d, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x45,
	0x58, 0x49, 0x53, 0x54, 0x53, 0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x56, 0x41,
	0x4c, 0x49, 0x44, 0x10, 0x14, 0x12, 0x43, 0x0a, 0x3f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45,
	0x52, 0x5f, 0x44, 0x4f, 0x45, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54,
	0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x4c, 0x49,
	0x4e, 0x4b, 0x45, 0x44, 0x5f, 0x54, 0x4f, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x49, 0x4e, 0x47,
	0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x15, 0x12, 0x3c, 0x0a, 0x38, 0x43, 0x55,
	0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x44, 0x4f, 0x45, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f,
	0x45, 0x58, 0x49, 0x53, 0x54, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x4c, 0x49, 0x4e, 0x4b,
	0x45, 0x44, 0x5f, 0x54, 0x4f, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x41,
	0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x16, 0x12, 0x44, 0x0a, 0x40, 0x43, 0x55, 0x53, 0x54,
	0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x44, 0x4f, 0x45, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58,
	0x49, 0x53, 0x54, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52,
	0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x45, 0x44, 0x5f, 0x54, 0x4f, 0x5f, 0x4d, 0x55, 0x4c, 0x54, 0x49,
	0x50, 0x4c, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x53, 0x10, 0x17, 0x12, 0x3d,
	0x0a, 0x39, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x44, 0x4f, 0x45, 0x53, 0x5f,
	0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f,
	0x4c, 0x49, 0x4e, 0x4b, 0x45, 0x44, 0x5f, 0x54, 0x4f, 0x5f, 0x4d, 0x55, 0x4c, 0x54, 0x49, 0x50,
	0x4c, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x53, 0x10, 0x18, 0x12, 0x2b, 0x0a,
	0x27, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53,
	0x5f, 0x4e, 0x52, 0x49, 0x5f, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x43, 0x55, 0x53,
	0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x19, 0x2a, 0x56, 0x0a, 0x07, 0x4b, 0x59,
	0x43, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x18, 0x0a, 0x14, 0x4b, 0x59, 0x43, 0x5f, 0x46, 0x4c, 0x41,
	0x47, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x13, 0x0a, 0x0f, 0x4b, 0x59, 0x43, 0x5f, 0x46, 0x4c, 0x41, 0x47, 0x5f, 0x45, 0x58, 0x49, 0x53,
	0x54, 0x53, 0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x4b, 0x59, 0x43, 0x5f, 0x46, 0x4c, 0x41, 0x47,
	0x5f, 0x44, 0x4f, 0x45, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53,
	0x10, 0x02, 0x2a, 0x7c, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64,
	0x46, 0x6c, 0x61, 0x67, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x46, 0x4c, 0x41, 0x47, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x23, 0x0a, 0x1f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54,
	0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x46, 0x4c, 0x41, 0x47, 0x5f, 0x41, 0x4c, 0x52, 0x45, 0x41,
	0x44, 0x59, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x10, 0x01, 0x12, 0x23, 0x0a, 0x1f, 0x43,
	0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x46, 0x4c, 0x41, 0x47, 0x5f,
	0x44, 0x4f, 0x45, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x10, 0x02,
	0x2a, 0x55, 0x0a, 0x07, 0x50, 0x61, 0x6e, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x18, 0x0a, 0x14, 0x50,
	0x41, 0x4e, 0x5f, 0x46, 0x4c, 0x41, 0x47, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x50, 0x41, 0x4e, 0x5f, 0x46, 0x4c, 0x41,
	0x47, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x10, 0x01, 0x12, 0x1b, 0x0a, 0x17, 0x50, 0x41,
	0x4e, 0x5f, 0x46, 0x4c, 0x41, 0x47, 0x5f, 0x44, 0x4f, 0x45, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f,
	0x45, 0x58, 0x49, 0x53, 0x54, 0x10, 0x02, 0x2a, 0x65, 0x0a, 0x0b, 0x41, 0x61, 0x64, 0x68, 0x61,
	0x61, 0x72, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x41, 0x44, 0x48, 0x41, 0x41,
	0x52, 0x5f, 0x46, 0x4c, 0x41, 0x47, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x41, 0x41, 0x44, 0x48, 0x41, 0x41, 0x52, 0x5f,
	0x46, 0x4c, 0x41, 0x47, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x10, 0x01, 0x12, 0x1f, 0x0a,
	0x1b, 0x41, 0x41, 0x44, 0x48, 0x41, 0x41, 0x52, 0x5f, 0x46, 0x4c, 0x41, 0x47, 0x5f, 0x44, 0x4f,
	0x45, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x10, 0x02, 0x2a, 0x3c,
	0x0a, 0x0b, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a,
	0x18, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x41,
	0x50, 0x50, 0x10, 0x01, 0x12, 0x06, 0x0a, 0x02, 0x43, 0x43, 0x10, 0x02, 0x2a, 0x40, 0x0a, 0x0b,
	0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x50,
	0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x41, 0x56,
	0x49, 0x4e, 0x47, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x01, 0x2a, 0xc7,
	0x02, 0x0a, 0x14, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x26, 0x0a, 0x22, 0x43, 0x55, 0x53, 0x54, 0x4f,
	0x4d, 0x45, 0x52, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x4c, 0x4f,
	0x57, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x2e, 0x0a, 0x2a, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x43, 0x52, 0x45, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x4f,
	0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x4c, 0x53, 0x4f, 0x10, 0x01, 0x12,
	0x23, 0x0a, 0x1f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x43, 0x52, 0x45, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x4d, 0x49, 0x4e, 0x5f, 0x45, 0x4b,
	0x59, 0x43, 0x10, 0x02, 0x12, 0x24, 0x0a, 0x20, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52,
	0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x43,
	0x4b, 0x59, 0x43, 0x5f, 0x45, 0x4b, 0x59, 0x43, 0x10, 0x03, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x55,
	0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x43, 0x4b, 0x59, 0x43, 0x10, 0x04, 0x12, 0x1f, 0x0a, 0x1b, 0x43,
	0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x56, 0x4b, 0x59, 0x43, 0x10, 0x05, 0x12, 0x29, 0x0a, 0x25,
	0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x44, 0x45, 0x44, 0x55, 0x50, 0x45, 0x5f, 0x4d, 0x49,
	0x4e, 0x5f, 0x4b, 0x59, 0x43, 0x10, 0x06, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x55, 0x53, 0x54, 0x4f,
	0x4d, 0x45, 0x52, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x4c, 0x4f,
	0x57, 0x5f, 0x42, 0x4b, 0x59, 0x43, 0x10, 0x07, 0x2a, 0xab, 0x03, 0x0a, 0x0a, 0x56, 0x4b, 0x59,
	0x43, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x17, 0x56, 0x4b, 0x59, 0x43, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x10, 0x01, 0x12,
	0x18, 0x0a, 0x14, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41,
	0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x44, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x56, 0x4b, 0x59,
	0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45,
	0x44, 0x10, 0x03, 0x12, 0x27, 0x0a, 0x23, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x42, 0x41,
	0x43, 0x4b, 0x5f, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x10, 0x04, 0x12, 0x29, 0x0a, 0x25,
	0x56, 0x4b, 0x59, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x55, 0x44, 0x49,
	0x54, 0x4f, 0x52, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x4d, 0x49,
	0x53, 0x53, 0x49, 0x4e, 0x47, 0x10, 0x05, 0x12, 0x23, 0x0a, 0x1f, 0x56, 0x4b, 0x59, 0x43, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x43,
	0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x06, 0x12, 0x21, 0x0a, 0x1d,
	0x56, 0x4b, 0x59, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x10, 0x07, 0x12,
	0x28, 0x0a, 0x24, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x56,
	0x4b, 0x59, 0x43, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x4d, 0x45, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x47,
	0x45, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x08, 0x12, 0x21, 0x0a, 0x1d, 0x56, 0x4b, 0x59,
	0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x55, 0x44, 0x49, 0x54, 0x4f, 0x52,
	0x5f, 0x52, 0x45, 0x43, 0x41, 0x50, 0x54, 0x55, 0x52, 0x45, 0x10, 0x09, 0x12, 0x28, 0x0a, 0x24,
	0x56, 0x4b, 0x59, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x55, 0x4c, 0x4c,
	0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x55, 0x50, 0x47, 0x52, 0x41, 0x44, 0x45, 0x5f, 0x50, 0x45, 0x4e,
	0x44, 0x49, 0x4e, 0x47, 0x10, 0x0a, 0x12, 0x1e, 0x0a, 0x1a, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x4a, 0x45,
	0x43, 0x54, 0x45, 0x44, 0x10, 0x0b, 0x32, 0xc9, 0x0d, 0x0a, 0x08, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x12, 0x87, 0x01, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x12, 0x39, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x3a, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x96, 0x01,
	0x0a, 0x13, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xb1, 0x01, 0x0a, 0x1c, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x4e, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x12, 0x47, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x4e, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x48, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x4e, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x69, 0x64, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xc0, 0x01, 0x0a, 0x21, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x46, 0x6f, 0x72, 0x4e, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x69, 0x64, 0x65, 0x6e, 0x74,
	0x12, 0x4c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x46, 0x6f, 0x72, 0x4e, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x4d,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f,
	0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x46, 0x6f, 0x72, 0x4e, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7e, 0x0a,
	0x0b, 0x44, 0x65, 0x64, 0x75, 0x70, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x36, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65,
	0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2e, 0x44, 0x65, 0x64, 0x75, 0x70, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x64, 0x75, 0x70, 0x65,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x99, 0x01,
	0x0a, 0x14, 0x46, 0x65, 0x74, 0x63, 0x68, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x46, 0x65, 0x74, 0x63,
	0x68, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x40, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x46, 0x65, 0x74,
	0x63, 0x68, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa2, 0x01, 0x0a, 0x17, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x42, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x43, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61,
	0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x45, 0x6c, 0x69, 0x67, 0x69,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x90,
	0x01, 0x0a, 0x11, 0x45, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x65, 0x56, 0x4b, 0x59, 0x43, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x3c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x45, 0x6e, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x56, 0x4b, 0x59, 0x43, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x3d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x45, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x65, 0x56,
	0x4b, 0x59, 0x43, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x8a, 0x01, 0x0a, 0x0f, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x4b, 0x59, 0x43,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x3a, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x67, 0x72, 0x61,
	0x64, 0x65, 0x4b, 0x59, 0x43, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x3b, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x4b, 0x59,
	0x43, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x93,
	0x01, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x61, 0x6e, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x12, 0x3d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x4c, 0x6f, 0x61, 0x6e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x4c, 0x6f, 0x61, 0x6e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0xab, 0x01, 0x0a, 0x1a, 0x4c, 0x6f, 0x61, 0x6e, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x45, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x46, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62,
	0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e,
	0x4c, 0x6f, 0x61, 0x6e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x42, 0x7e, 0x0a, 0x3d, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f,
	0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5a, 0x3d, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x6f, 0x70,
	0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendorgateway_openbanking_customer_service_proto_rawDescOnce sync.Once
	file_api_vendorgateway_openbanking_customer_service_proto_rawDescData = file_api_vendorgateway_openbanking_customer_service_proto_rawDesc
)

func file_api_vendorgateway_openbanking_customer_service_proto_rawDescGZIP() []byte {
	file_api_vendorgateway_openbanking_customer_service_proto_rawDescOnce.Do(func() {
		file_api_vendorgateway_openbanking_customer_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendorgateway_openbanking_customer_service_proto_rawDescData)
	})
	return file_api_vendorgateway_openbanking_customer_service_proto_rawDescData
}

var file_api_vendorgateway_openbanking_customer_service_proto_enumTypes = make([]protoimpl.EnumInfo, 15)
var file_api_vendorgateway_openbanking_customer_service_proto_msgTypes = make([]protoimpl.MessageInfo, 25)
var file_api_vendorgateway_openbanking_customer_service_proto_goTypes = []interface{}{
	(DedupeStatus)(0),                  // 0: vendorgateway.openbanking.customer.DedupeStatus
	(KYCFlag)(0),                       // 1: vendorgateway.openbanking.customer.KYCFlag
	(CreditCardFlag)(0),                // 2: vendorgateway.openbanking.customer.CreditCardFlag
	(PanFlag)(0),                       // 3: vendorgateway.openbanking.customer.PanFlag
	(AadhaarFlag)(0),                   // 4: vendorgateway.openbanking.customer.AadhaarFlag
	(ChannelType)(0),                   // 5: vendorgateway.openbanking.customer.ChannelType
	(ProductType)(0),                   // 6: vendorgateway.openbanking.customer.ProductType
	(CustomerCreationFlow)(0),          // 7: vendorgateway.openbanking.customer.CustomerCreationFlow
	(VKYCStatus)(0),                    // 8: vendorgateway.openbanking.customer.VKYCStatus
	(CreateCustomerResponse_Status)(0), // 9: vendorgateway.openbanking.customer.CreateCustomerResponse.Status
	(CreateCustomerForNonResidentResponse_Status)(0),      // 10: vendorgateway.openbanking.customer.CreateCustomerForNonResidentResponse.Status
	(CheckCustomerStatusResponse_Status)(0),               // 11: vendorgateway.openbanking.customer.CheckCustomerStatusResponse.Status
	(CheckCustomerStatusForNonResidentResponse_Status)(0), // 12: vendorgateway.openbanking.customer.CheckCustomerStatusForNonResidentResponse.Status
	(FetchCustomerDetailsResponse_Status)(0),              // 13: vendorgateway.openbanking.customer.FetchCustomerDetailsResponse.Status
	(EnquireVKYCStatusResponse_Status)(0),                 // 14: vendorgateway.openbanking.customer.EnquireVKYCStatusResponse.Status
	(*CreateCustomerRequest)(nil),                         // 15: vendorgateway.openbanking.customer.CreateCustomerRequest
	(*ProofDetails)(nil),                                  // 16: vendorgateway.openbanking.customer.ProofDetails
	(*CreateCustomerResponse)(nil),                        // 17: vendorgateway.openbanking.customer.CreateCustomerResponse
	(*CreateCustomerForNonResidentRequest)(nil),           // 18: vendorgateway.openbanking.customer.CreateCustomerForNonResidentRequest
	(*CreateCustomerForNonResidentResponse)(nil),          // 19: vendorgateway.openbanking.customer.CreateCustomerForNonResidentResponse
	(*CheckCustomerStatusRequest)(nil),                    // 20: vendorgateway.openbanking.customer.CheckCustomerStatusRequest
	(*CheckCustomerStatusResponse)(nil),                   // 21: vendorgateway.openbanking.customer.CheckCustomerStatusResponse
	(*CheckCustomerStatusForNonResidentRequest)(nil),      // 22: vendorgateway.openbanking.customer.CheckCustomerStatusForNonResidentRequest
	(*CheckCustomerStatusForNonResidentResponse)(nil),     // 23: vendorgateway.openbanking.customer.CheckCustomerStatusForNonResidentResponse
	(*DedupeCheckRequest)(nil),                            // 24: vendorgateway.openbanking.customer.DedupeCheckRequest
	(*DedupeCheckResponse)(nil),                           // 25: vendorgateway.openbanking.customer.DedupeCheckResponse
	(*FetchCustomerDetailsRequest)(nil),                   // 26: vendorgateway.openbanking.customer.FetchCustomerDetailsRequest
	(*FetchCustomerDetailsResponse)(nil),                  // 27: vendorgateway.openbanking.customer.FetchCustomerDetailsResponse
	(*VendorAddress)(nil),                                 // 28: vendorgateway.openbanking.customer.VendorAddress
	(*Account)(nil),                                       // 29: vendorgateway.openbanking.customer.Account
	(*CheckProductEligibilityRequest)(nil),                // 30: vendorgateway.openbanking.customer.CheckProductEligibilityRequest
	(*CheckProductEligibilityResponse)(nil),               // 31: vendorgateway.openbanking.customer.CheckProductEligibilityResponse
	(*EnquireVKYCStatusRequest)(nil),                      // 32: vendorgateway.openbanking.customer.EnquireVKYCStatusRequest
	(*EnquireVKYCStatusResponse)(nil),                     // 33: vendorgateway.openbanking.customer.EnquireVKYCStatusResponse
	(*UpgradeKYCLevelRequest)(nil),                        // 34: vendorgateway.openbanking.customer.UpgradeKYCLevelRequest
	(*UpgradeKYCLevelResponse)(nil),                       // 35: vendorgateway.openbanking.customer.UpgradeKYCLevelResponse
	(*CreateLoanCustomerRequest)(nil),                     // 36: vendorgateway.openbanking.customer.CreateLoanCustomerRequest
	(*CreateLoanCustomerResponse)(nil),                    // 37: vendorgateway.openbanking.customer.CreateLoanCustomerResponse
	(*LoanCustomerCreationStatusRequest)(nil),             // 38: vendorgateway.openbanking.customer.LoanCustomerCreationStatusRequest
	(*LoanCustomerCreationStatusResponse)(nil),            // 39: vendorgateway.openbanking.customer.LoanCustomerCreationStatusResponse
	(*vendorgateway.RequestHeader)(nil),                   // 40: vendorgateway.RequestHeader
	(*common.Name)(nil),                                   // 41: api.typesv2.common.Name
	(*date.Date)(nil),                                     // 42: google.type.Date
	(*postaladdress.PostalAddress)(nil),                   // 43: google.type.PostalAddress
	(typesv2.Gender)(0),                                   // 44: api.typesv2.Gender
	(*common.PhoneNumber)(nil),                            // 45: api.typesv2.common.PhoneNumber
	(*header.Auth)(nil),                                   // 46: vendorgateway.openbanking.header.Auth
	(employment.EmploymentType)(0),                        // 47: employment.EmploymentType
	(employment.OccupationType)(0),                        // 48: employment.OccupationType
	(typesv2.DisabilityType)(0),                           // 49: api.typesv2.DisabilityType
	(typesv2.Category)(0),                                 // 50: api.typesv2.Category
	(typesv2.PepCategory)(0),                              // 51: api.typesv2.PepCategory
	(typesv2.Qualification)(0),                            // 52: api.typesv2.Qualification
	(kyc.IdProofType)(0),                                  // 53: kyc.IdProofType
	(*rpc.Status)(nil),                                    // 54: rpc.Status
	(*vendorgateway.VendorStatus)(nil),                    // 55: vendorgateway.VendorStatus
	(*typesv2.Date)(nil),                                  // 56: api.typesv2.Date
	(*typesv2.PassportData)(nil),                          // 57: api.typesv2.PassportData
	(*typesv2.SalaryRange)(nil),                           // 58: api.typesv2.SalaryRange
	(typesv2.MaritalStatus)(0),                            // 59: api.typesv2.MaritalStatus
	(common.BooleanEnum)(0),                               // 60: api.typesv2.common.BooleanEnum
	(*timestamppb.Timestamp)(nil),                         // 61: google.protobuf.Timestamp
	(dedupe.Flow)(0),                                      // 62: vendorgateway.openbanking.customer.dedupe.Flow
	(typesv2.Community)(0),                                // 63: api.typesv2.Community
	(typesv2.Designation)(0),                              // 64: api.typesv2.Designation
}
var file_api_vendorgateway_openbanking_customer_service_proto_depIdxs = []int32{
	40,  // 0: vendorgateway.openbanking.customer.CreateCustomerRequest.header:type_name -> vendorgateway.RequestHeader
	41,  // 1: vendorgateway.openbanking.customer.CreateCustomerRequest.name:type_name -> api.typesv2.common.Name
	42,  // 2: vendorgateway.openbanking.customer.CreateCustomerRequest.date_of_birth:type_name -> google.type.Date
	43,  // 3: vendorgateway.openbanking.customer.CreateCustomerRequest.permanent_address:type_name -> google.type.PostalAddress
	43,  // 4: vendorgateway.openbanking.customer.CreateCustomerRequest.current_address:type_name -> google.type.PostalAddress
	44,  // 5: vendorgateway.openbanking.customer.CreateCustomerRequest.gender:type_name -> api.typesv2.Gender
	45,  // 6: vendorgateway.openbanking.customer.CreateCustomerRequest.phone_number:type_name -> api.typesv2.common.PhoneNumber
	16,  // 7: vendorgateway.openbanking.customer.CreateCustomerRequest.identity_proof:type_name -> vendorgateway.openbanking.customer.ProofDetails
	16,  // 8: vendorgateway.openbanking.customer.CreateCustomerRequest.address_proof:type_name -> vendorgateway.openbanking.customer.ProofDetails
	46,  // 9: vendorgateway.openbanking.customer.CreateCustomerRequest.device_details:type_name -> vendorgateway.openbanking.header.Auth
	47,  // 10: vendorgateway.openbanking.customer.CreateCustomerRequest.type:type_name -> employment.EmploymentType
	7,   // 11: vendorgateway.openbanking.customer.CreateCustomerRequest.customer_creation_flow:type_name -> vendorgateway.openbanking.customer.CustomerCreationFlow
	48,  // 12: vendorgateway.openbanking.customer.CreateCustomerRequest.occupation_type:type_name -> employment.OccupationType
	49,  // 13: vendorgateway.openbanking.customer.CreateCustomerRequest.disability_type:type_name -> api.typesv2.DisabilityType
	50,  // 14: vendorgateway.openbanking.customer.CreateCustomerRequest.category:type_name -> api.typesv2.Category
	51,  // 15: vendorgateway.openbanking.customer.CreateCustomerRequest.pep_category:type_name -> api.typesv2.PepCategory
	52,  // 16: vendorgateway.openbanking.customer.CreateCustomerRequest.qualification:type_name -> api.typesv2.Qualification
	53,  // 17: vendorgateway.openbanking.customer.ProofDetails.type:type_name -> kyc.IdProofType
	42,  // 18: vendorgateway.openbanking.customer.ProofDetails.id_issue_date:type_name -> google.type.Date
	42,  // 19: vendorgateway.openbanking.customer.ProofDetails.id_expiry_date:type_name -> google.type.Date
	54,  // 20: vendorgateway.openbanking.customer.CreateCustomerResponse.status:type_name -> rpc.Status
	55,  // 21: vendorgateway.openbanking.customer.CreateCustomerResponse.vendor_status:type_name -> vendorgateway.VendorStatus
	40,  // 22: vendorgateway.openbanking.customer.CreateCustomerForNonResidentRequest.header:type_name -> vendorgateway.RequestHeader
	41,  // 23: vendorgateway.openbanking.customer.CreateCustomerForNonResidentRequest.name:type_name -> api.typesv2.common.Name
	56,  // 24: vendorgateway.openbanking.customer.CreateCustomerForNonResidentRequest.date_of_birth:type_name -> api.typesv2.Date
	43,  // 25: vendorgateway.openbanking.customer.CreateCustomerForNonResidentRequest.permanent_address:type_name -> google.type.PostalAddress
	43,  // 26: vendorgateway.openbanking.customer.CreateCustomerForNonResidentRequest.current_address:type_name -> google.type.PostalAddress
	44,  // 27: vendorgateway.openbanking.customer.CreateCustomerForNonResidentRequest.gender:type_name -> api.typesv2.Gender
	45,  // 28: vendorgateway.openbanking.customer.CreateCustomerForNonResidentRequest.phone_number:type_name -> api.typesv2.common.PhoneNumber
	47,  // 29: vendorgateway.openbanking.customer.CreateCustomerForNonResidentRequest.employment_type:type_name -> employment.EmploymentType
	7,   // 30: vendorgateway.openbanking.customer.CreateCustomerForNonResidentRequest.customer_creation_flow:type_name -> vendorgateway.openbanking.customer.CustomerCreationFlow
	48,  // 31: vendorgateway.openbanking.customer.CreateCustomerForNonResidentRequest.occupation_type:type_name -> employment.OccupationType
	57,  // 32: vendorgateway.openbanking.customer.CreateCustomerForNonResidentRequest.passport_data:type_name -> api.typesv2.PassportData
	58,  // 33: vendorgateway.openbanking.customer.CreateCustomerForNonResidentRequest.annual_income_range:type_name -> api.typesv2.SalaryRange
	59,  // 34: vendorgateway.openbanking.customer.CreateCustomerForNonResidentRequest.marital_status:type_name -> api.typesv2.MaritalStatus
	60,  // 35: vendorgateway.openbanking.customer.CreateCustomerForNonResidentRequest.form_60_opted_flag:type_name -> api.typesv2.common.BooleanEnum
	54,  // 36: vendorgateway.openbanking.customer.CreateCustomerForNonResidentResponse.status:type_name -> rpc.Status
	55,  // 37: vendorgateway.openbanking.customer.CreateCustomerForNonResidentResponse.vendor_status:type_name -> vendorgateway.VendorStatus
	40,  // 38: vendorgateway.openbanking.customer.CheckCustomerStatusRequest.header:type_name -> vendorgateway.RequestHeader
	46,  // 39: vendorgateway.openbanking.customer.CheckCustomerStatusRequest.device_details:type_name -> vendorgateway.openbanking.header.Auth
	54,  // 40: vendorgateway.openbanking.customer.CheckCustomerStatusResponse.status:type_name -> rpc.Status
	61,  // 41: vendorgateway.openbanking.customer.CheckCustomerStatusResponse.created_at:type_name -> google.protobuf.Timestamp
	55,  // 42: vendorgateway.openbanking.customer.CheckCustomerStatusResponse.vendor_status:type_name -> vendorgateway.VendorStatus
	40,  // 43: vendorgateway.openbanking.customer.CheckCustomerStatusForNonResidentRequest.header:type_name -> vendorgateway.RequestHeader
	54,  // 44: vendorgateway.openbanking.customer.CheckCustomerStatusForNonResidentResponse.status:type_name -> rpc.Status
	55,  // 45: vendorgateway.openbanking.customer.CheckCustomerStatusForNonResidentResponse.vendor_status:type_name -> vendorgateway.VendorStatus
	40,  // 46: vendorgateway.openbanking.customer.DedupeCheckRequest.header:type_name -> vendorgateway.RequestHeader
	45,  // 47: vendorgateway.openbanking.customer.DedupeCheckRequest.phone_number:type_name -> api.typesv2.common.PhoneNumber
	42,  // 48: vendorgateway.openbanking.customer.DedupeCheckRequest.date_of_birth:type_name -> google.type.Date
	62,  // 49: vendorgateway.openbanking.customer.DedupeCheckRequest.flow:type_name -> vendorgateway.openbanking.customer.dedupe.Flow
	54,  // 50: vendorgateway.openbanking.customer.DedupeCheckResponse.status:type_name -> rpc.Status
	0,   // 51: vendorgateway.openbanking.customer.DedupeCheckResponse.dedupe_status:type_name -> vendorgateway.openbanking.customer.DedupeStatus
	1,   // 52: vendorgateway.openbanking.customer.DedupeCheckResponse.k_y_c_flag:type_name -> vendorgateway.openbanking.customer.KYCFlag
	2,   // 53: vendorgateway.openbanking.customer.DedupeCheckResponse.credit_card_flag:type_name -> vendorgateway.openbanking.customer.CreditCardFlag
	3,   // 54: vendorgateway.openbanking.customer.DedupeCheckResponse.pan_flag:type_name -> vendorgateway.openbanking.customer.PanFlag
	4,   // 55: vendorgateway.openbanking.customer.DedupeCheckResponse.aadhaar_flag:type_name -> vendorgateway.openbanking.customer.AadhaarFlag
	55,  // 56: vendorgateway.openbanking.customer.DedupeCheckResponse.vendor_status:type_name -> vendorgateway.VendorStatus
	40,  // 57: vendorgateway.openbanking.customer.FetchCustomerDetailsRequest.header:type_name -> vendorgateway.RequestHeader
	46,  // 58: vendorgateway.openbanking.customer.FetchCustomerDetailsRequest.auth:type_name -> vendorgateway.openbanking.header.Auth
	45,  // 59: vendorgateway.openbanking.customer.FetchCustomerDetailsRequest.phone_number:type_name -> api.typesv2.common.PhoneNumber
	5,   // 60: vendorgateway.openbanking.customer.FetchCustomerDetailsRequest.channel_type:type_name -> vendorgateway.openbanking.customer.ChannelType
	54,  // 61: vendorgateway.openbanking.customer.FetchCustomerDetailsResponse.status:type_name -> rpc.Status
	41,  // 62: vendorgateway.openbanking.customer.FetchCustomerDetailsResponse.customer_name:type_name -> api.typesv2.common.Name
	44,  // 63: vendorgateway.openbanking.customer.FetchCustomerDetailsResponse.gender:type_name -> api.typesv2.Gender
	41,  // 64: vendorgateway.openbanking.customer.FetchCustomerDetailsResponse.father_or_husband_name:type_name -> api.typesv2.common.Name
	45,  // 65: vendorgateway.openbanking.customer.FetchCustomerDetailsResponse.phone_number:type_name -> api.typesv2.common.PhoneNumber
	42,  // 66: vendorgateway.openbanking.customer.FetchCustomerDetailsResponse.date_of_birth:type_name -> google.type.Date
	59,  // 67: vendorgateway.openbanking.customer.FetchCustomerDetailsResponse.marital_status:type_name -> api.typesv2.MaritalStatus
	28,  // 68: vendorgateway.openbanking.customer.FetchCustomerDetailsResponse.communication_address:type_name -> vendorgateway.openbanking.customer.VendorAddress
	28,  // 69: vendorgateway.openbanking.customer.FetchCustomerDetailsResponse.permanent_address:type_name -> vendorgateway.openbanking.customer.VendorAddress
	28,  // 70: vendorgateway.openbanking.customer.FetchCustomerDetailsResponse.shipping_address:type_name -> vendorgateway.openbanking.customer.VendorAddress
	29,  // 71: vendorgateway.openbanking.customer.FetchCustomerDetailsResponse.account_list:type_name -> vendorgateway.openbanking.customer.Account
	48,  // 72: vendorgateway.openbanking.customer.FetchCustomerDetailsResponse.occupation_type:type_name -> employment.OccupationType
	55,  // 73: vendorgateway.openbanking.customer.FetchCustomerDetailsResponse.vendor_status:type_name -> vendorgateway.VendorStatus
	40,  // 74: vendorgateway.openbanking.customer.CheckProductEligibilityRequest.header:type_name -> vendorgateway.RequestHeader
	45,  // 75: vendorgateway.openbanking.customer.CheckProductEligibilityRequest.mobile_number:type_name -> api.typesv2.common.PhoneNumber
	6,   // 76: vendorgateway.openbanking.customer.CheckProductEligibilityRequest.product_type:type_name -> vendorgateway.openbanking.customer.ProductType
	54,  // 77: vendorgateway.openbanking.customer.CheckProductEligibilityResponse.status:type_name -> rpc.Status
	0,   // 78: vendorgateway.openbanking.customer.CheckProductEligibilityResponse.dedupe_status:type_name -> vendorgateway.openbanking.customer.DedupeStatus
	55,  // 79: vendorgateway.openbanking.customer.CheckProductEligibilityResponse.vendor_status:type_name -> vendorgateway.VendorStatus
	40,  // 80: vendorgateway.openbanking.customer.EnquireVKYCStatusRequest.header:type_name -> vendorgateway.RequestHeader
	54,  // 81: vendorgateway.openbanking.customer.EnquireVKYCStatusResponse.status:type_name -> rpc.Status
	8,   // 82: vendorgateway.openbanking.customer.EnquireVKYCStatusResponse.vkyc_status:type_name -> vendorgateway.openbanking.customer.VKYCStatus
	42,  // 83: vendorgateway.openbanking.customer.EnquireVKYCStatusResponse.date:type_name -> google.type.Date
	40,  // 84: vendorgateway.openbanking.customer.UpgradeKYCLevelRequest.header:type_name -> vendorgateway.RequestHeader
	54,  // 85: vendorgateway.openbanking.customer.UpgradeKYCLevelResponse.status:type_name -> rpc.Status
	55,  // 86: vendorgateway.openbanking.customer.UpgradeKYCLevelResponse.vendor_status:type_name -> vendorgateway.VendorStatus
	40,  // 87: vendorgateway.openbanking.customer.CreateLoanCustomerRequest.header:type_name -> vendorgateway.RequestHeader
	41,  // 88: vendorgateway.openbanking.customer.CreateLoanCustomerRequest.name:type_name -> api.typesv2.common.Name
	42,  // 89: vendorgateway.openbanking.customer.CreateLoanCustomerRequest.date_of_birth:type_name -> google.type.Date
	44,  // 90: vendorgateway.openbanking.customer.CreateLoanCustomerRequest.gender:type_name -> api.typesv2.Gender
	59,  // 91: vendorgateway.openbanking.customer.CreateLoanCustomerRequest.marital_status:type_name -> api.typesv2.MaritalStatus
	45,  // 92: vendorgateway.openbanking.customer.CreateLoanCustomerRequest.phone_number:type_name -> api.typesv2.common.PhoneNumber
	43,  // 93: vendorgateway.openbanking.customer.CreateLoanCustomerRequest.communication_address:type_name -> google.type.PostalAddress
	43,  // 94: vendorgateway.openbanking.customer.CreateLoanCustomerRequest.permanent_address:type_name -> google.type.PostalAddress
	16,  // 95: vendorgateway.openbanking.customer.CreateLoanCustomerRequest.identity_proof:type_name -> vendorgateway.openbanking.customer.ProofDetails
	16,  // 96: vendorgateway.openbanking.customer.CreateLoanCustomerRequest.address_proof:type_name -> vendorgateway.openbanking.customer.ProofDetails
	47,  // 97: vendorgateway.openbanking.customer.CreateLoanCustomerRequest.employment_type:type_name -> employment.EmploymentType
	48,  // 98: vendorgateway.openbanking.customer.CreateLoanCustomerRequest.occupation_type:type_name -> employment.OccupationType
	63,  // 99: vendorgateway.openbanking.customer.CreateLoanCustomerRequest.community:type_name -> api.typesv2.Community
	52,  // 100: vendorgateway.openbanking.customer.CreateLoanCustomerRequest.qualification:type_name -> api.typesv2.Qualification
	64,  // 101: vendorgateway.openbanking.customer.CreateLoanCustomerRequest.designation:type_name -> api.typesv2.Designation
	49,  // 102: vendorgateway.openbanking.customer.CreateLoanCustomerRequest.disability_type:type_name -> api.typesv2.DisabilityType
	50,  // 103: vendorgateway.openbanking.customer.CreateLoanCustomerRequest.category:type_name -> api.typesv2.Category
	54,  // 104: vendorgateway.openbanking.customer.CreateLoanCustomerResponse.status:type_name -> rpc.Status
	55,  // 105: vendorgateway.openbanking.customer.CreateLoanCustomerResponse.vendor_status:type_name -> vendorgateway.VendorStatus
	40,  // 106: vendorgateway.openbanking.customer.LoanCustomerCreationStatusRequest.header:type_name -> vendorgateway.RequestHeader
	54,  // 107: vendorgateway.openbanking.customer.LoanCustomerCreationStatusResponse.status:type_name -> rpc.Status
	55,  // 108: vendorgateway.openbanking.customer.LoanCustomerCreationStatusResponse.vendor_status:type_name -> vendorgateway.VendorStatus
	61,  // 109: vendorgateway.openbanking.customer.LoanCustomerCreationStatusResponse.cif_created_time:type_name -> google.protobuf.Timestamp
	15,  // 110: vendorgateway.openbanking.customer.Customer.CreateCustomer:input_type -> vendorgateway.openbanking.customer.CreateCustomerRequest
	20,  // 111: vendorgateway.openbanking.customer.Customer.CheckCustomerStatus:input_type -> vendorgateway.openbanking.customer.CheckCustomerStatusRequest
	18,  // 112: vendorgateway.openbanking.customer.Customer.CreateCustomerForNonResident:input_type -> vendorgateway.openbanking.customer.CreateCustomerForNonResidentRequest
	22,  // 113: vendorgateway.openbanking.customer.Customer.CheckCustomerStatusForNonResident:input_type -> vendorgateway.openbanking.customer.CheckCustomerStatusForNonResidentRequest
	24,  // 114: vendorgateway.openbanking.customer.Customer.DedupeCheck:input_type -> vendorgateway.openbanking.customer.DedupeCheckRequest
	26,  // 115: vendorgateway.openbanking.customer.Customer.FetchCustomerDetails:input_type -> vendorgateway.openbanking.customer.FetchCustomerDetailsRequest
	30,  // 116: vendorgateway.openbanking.customer.Customer.CheckProductEligibility:input_type -> vendorgateway.openbanking.customer.CheckProductEligibilityRequest
	32,  // 117: vendorgateway.openbanking.customer.Customer.EnquireVKYCStatus:input_type -> vendorgateway.openbanking.customer.EnquireVKYCStatusRequest
	34,  // 118: vendorgateway.openbanking.customer.Customer.UpgradeKYCLevel:input_type -> vendorgateway.openbanking.customer.UpgradeKYCLevelRequest
	36,  // 119: vendorgateway.openbanking.customer.Customer.CreateLoanCustomer:input_type -> vendorgateway.openbanking.customer.CreateLoanCustomerRequest
	38,  // 120: vendorgateway.openbanking.customer.Customer.LoanCustomerCreationStatus:input_type -> vendorgateway.openbanking.customer.LoanCustomerCreationStatusRequest
	17,  // 121: vendorgateway.openbanking.customer.Customer.CreateCustomer:output_type -> vendorgateway.openbanking.customer.CreateCustomerResponse
	21,  // 122: vendorgateway.openbanking.customer.Customer.CheckCustomerStatus:output_type -> vendorgateway.openbanking.customer.CheckCustomerStatusResponse
	19,  // 123: vendorgateway.openbanking.customer.Customer.CreateCustomerForNonResident:output_type -> vendorgateway.openbanking.customer.CreateCustomerForNonResidentResponse
	23,  // 124: vendorgateway.openbanking.customer.Customer.CheckCustomerStatusForNonResident:output_type -> vendorgateway.openbanking.customer.CheckCustomerStatusForNonResidentResponse
	25,  // 125: vendorgateway.openbanking.customer.Customer.DedupeCheck:output_type -> vendorgateway.openbanking.customer.DedupeCheckResponse
	27,  // 126: vendorgateway.openbanking.customer.Customer.FetchCustomerDetails:output_type -> vendorgateway.openbanking.customer.FetchCustomerDetailsResponse
	31,  // 127: vendorgateway.openbanking.customer.Customer.CheckProductEligibility:output_type -> vendorgateway.openbanking.customer.CheckProductEligibilityResponse
	33,  // 128: vendorgateway.openbanking.customer.Customer.EnquireVKYCStatus:output_type -> vendorgateway.openbanking.customer.EnquireVKYCStatusResponse
	35,  // 129: vendorgateway.openbanking.customer.Customer.UpgradeKYCLevel:output_type -> vendorgateway.openbanking.customer.UpgradeKYCLevelResponse
	37,  // 130: vendorgateway.openbanking.customer.Customer.CreateLoanCustomer:output_type -> vendorgateway.openbanking.customer.CreateLoanCustomerResponse
	39,  // 131: vendorgateway.openbanking.customer.Customer.LoanCustomerCreationStatus:output_type -> vendorgateway.openbanking.customer.LoanCustomerCreationStatusResponse
	121, // [121:132] is the sub-list for method output_type
	110, // [110:121] is the sub-list for method input_type
	110, // [110:110] is the sub-list for extension type_name
	110, // [110:110] is the sub-list for extension extendee
	0,   // [0:110] is the sub-list for field type_name
}

func init() { file_api_vendorgateway_openbanking_customer_service_proto_init() }
func file_api_vendorgateway_openbanking_customer_service_proto_init() {
	if File_api_vendorgateway_openbanking_customer_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCustomerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProofDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCustomerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCustomerForNonResidentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCustomerForNonResidentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckCustomerStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckCustomerStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckCustomerStatusForNonResidentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckCustomerStatusForNonResidentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DedupeCheckRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DedupeCheckResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchCustomerDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchCustomerDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VendorAddress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Account); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckProductEligibilityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckProductEligibilityResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnquireVKYCStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnquireVKYCStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpgradeKYCLevelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpgradeKYCLevelResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateLoanCustomerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateLoanCustomerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanCustomerCreationStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_customer_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanCustomerCreationStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendorgateway_openbanking_customer_service_proto_rawDesc,
			NumEnums:      15,
			NumMessages:   25,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_vendorgateway_openbanking_customer_service_proto_goTypes,
		DependencyIndexes: file_api_vendorgateway_openbanking_customer_service_proto_depIdxs,
		EnumInfos:         file_api_vendorgateway_openbanking_customer_service_proto_enumTypes,
		MessageInfos:      file_api_vendorgateway_openbanking_customer_service_proto_msgTypes,
	}.Build()
	File_api_vendorgateway_openbanking_customer_service_proto = out.File
	file_api_vendorgateway_openbanking_customer_service_proto_rawDesc = nil
	file_api_vendorgateway_openbanking_customer_service_proto_goTypes = nil
	file_api_vendorgateway_openbanking_customer_service_proto_depIdxs = nil
}
