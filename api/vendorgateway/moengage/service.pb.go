// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendorgateway/moengage/service.proto

package moengage

import (
	rpc "github.com/epifi/be-common/api/rpc"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	moengage "github.com/epifi/gamma/api/vendors/moengage"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MergeUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// This field contains the list of UID pairs of the users who are to be merged
	MergeData []*moengage.MergeUserRequest_MergeData `protobuf:"bytes,2,rep,name=merge_data,json=mergeData,proto3" json:"merge_data,omitempty"`
}

func (x *MergeUserRequest) Reset() {
	*x = MergeUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_moengage_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MergeUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MergeUserRequest) ProtoMessage() {}

func (x *MergeUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_moengage_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MergeUserRequest.ProtoReflect.Descriptor instead.
func (*MergeUserRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_moengage_service_proto_rawDescGZIP(), []int{0}
}

func (x *MergeUserRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *MergeUserRequest) GetMergeData() []*moengage.MergeUserRequest_MergeData {
	if x != nil {
		return x.MergeData
	}
	return nil
}

type MergeUserResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *MergeUserResponse) Reset() {
	*x = MergeUserResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_moengage_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MergeUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MergeUserResponse) ProtoMessage() {}

func (x *MergeUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_moengage_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MergeUserResponse.ProtoReflect.Descriptor instead.
func (*MergeUserResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_moengage_service_proto_rawDescGZIP(), []int{1}
}

func (x *MergeUserResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// This field is used to specify the identifiers for the users for whom the data needs to be fetched.
	Data *moengage.GetUserRequest_Data `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	// This field is used to specify the fields that need to be fetched for the user specified in Identifiers.
	UserFieldsToExport []string `protobuf:"bytes,3,rep,name=user_fields_to_export,json=userFieldsToExport,proto3" json:"user_fields_to_export,omitempty"`
}

func (x *GetUserRequest) Reset() {
	*x = GetUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_moengage_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserRequest) ProtoMessage() {}

func (x *GetUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_moengage_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserRequest.ProtoReflect.Descriptor instead.
func (*GetUserRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_moengage_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetUserRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetUserRequest) GetData() *moengage.GetUserRequest_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetUserRequest) GetUserFieldsToExport() []string {
	if x != nil {
		return x.UserFieldsToExport
	}
	return nil
}

type GetUserResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// This field contains the list of users -
	// 1. who were not found in MoEngage
	// 2. who were found in MoEngage.
	Data *moengage.GetUserResponse_Data `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetUserResponse) Reset() {
	*x = GetUserResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_moengage_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserResponse) ProtoMessage() {}

func (x *GetUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_moengage_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserResponse.ProtoReflect.Descriptor instead.
func (*GetUserResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_moengage_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetUserResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetUserResponse) GetData() *moengage.GetUserResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type DeleteUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Allowed Values: moengage_id (the MoEngageID of the user in the User Profile), customer_id (ID field in the User Profile)
	IdentityType string `protobuf:"bytes,2,opt,name=identity_type,json=identityType,proto3" json:"identity_type,omitempty"`
	// This field specifies the unique identifier that identifies the user.
	IdentityValue string `protobuf:"bytes,3,opt,name=identity_value,json=identityValue,proto3" json:"identity_value,omitempty"`
}

func (x *DeleteUserRequest) Reset() {
	*x = DeleteUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_moengage_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserRequest) ProtoMessage() {}

func (x *DeleteUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_moengage_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserRequest.ProtoReflect.Descriptor instead.
func (*DeleteUserRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_moengage_service_proto_rawDescGZIP(), []int{4}
}

func (x *DeleteUserRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *DeleteUserRequest) GetIdentityType() string {
	if x != nil {
		return x.IdentityType
	}
	return ""
}

func (x *DeleteUserRequest) GetIdentityValue() string {
	if x != nil {
		return x.IdentityValue
	}
	return ""
}

type DeleteUserResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// This field contains information about whether the response is being processed, the user is deleted, and so on.
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *DeleteUserResponse) Reset() {
	*x = DeleteUserResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_moengage_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserResponse) ProtoMessage() {}

func (x *DeleteUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_moengage_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserResponse.ProtoReflect.Descriptor instead.
func (*DeleteUserResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_moengage_service_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteUserResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *DeleteUserResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_api_vendorgateway_moengage_service_proto protoreflect.FileDescriptor

var file_api_vendorgateway_moengage_service_proto_rawDesc = []byte{
	0x0a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x2f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6d, 0x6f, 0x65, 0x6e, 0x67, 0x61,
	0x67, 0x65, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x6d, 0x6f,
	0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x95, 0x01, 0x0a, 0x10, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x4b, 0x0a, 0x0a,
	0x6d, 0x65, 0x72, 0x67, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6d, 0x6f, 0x65, 0x6e, 0x67,
	0x61, 0x67, 0x65, 0x2e, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x09,
	0x6d, 0x65, 0x72, 0x67, 0x65, 0x44, 0x61, 0x74, 0x61, 0x22, 0x38, 0x0a, 0x11, 0x4d, 0x65, 0x72,
	0x67, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0xb4, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x39, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6d, 0x6f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x2e, 0x47, 0x65,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x31, 0x0a, 0x15, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x5f, 0x74, 0x6f, 0x5f, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x12, 0x75, 0x73, 0x65, 0x72, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x73, 0x54, 0x6f, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x22, 0x72, 0x0a, 0x0f, 0x47, 0x65,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x3a, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x26, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6d, 0x6f, 0x65, 0x6e, 0x67,
	0x61, 0x67, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x95,
	0x01, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x25, 0x0a, 0x0e, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x53, 0x0a, 0x12, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x32, 0xad, 0x02, 0x0a, 0x08,
	0x4d, 0x6f, 0x45, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x12, 0x5a, 0x0a, 0x07, 0x47, 0x65, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x12, 0x26, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x6d, 0x6f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x2e, 0x47, 0x65, 0x74,
	0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6d, 0x6f, 0x65, 0x6e,
	0x67, 0x61, 0x67, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x60, 0x0a, 0x09, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x12, 0x28, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x6d, 0x6f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x2e, 0x4d, 0x65, 0x72, 0x67, 0x65,
	0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6d, 0x6f, 0x65, 0x6e,
	0x67, 0x61, 0x67, 0x65, 0x2e, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x63, 0x0a, 0x0a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x55, 0x73, 0x65, 0x72, 0x12, 0x29, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x6d, 0x6f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x2e, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2a, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x6d, 0x6f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x66, 0x0a, 0x31, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6d, 0x6f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65,
	0x5a, 0x31, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x6e, 0x67,
	0x61, 0x67, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendorgateway_moengage_service_proto_rawDescOnce sync.Once
	file_api_vendorgateway_moengage_service_proto_rawDescData = file_api_vendorgateway_moengage_service_proto_rawDesc
)

func file_api_vendorgateway_moengage_service_proto_rawDescGZIP() []byte {
	file_api_vendorgateway_moengage_service_proto_rawDescOnce.Do(func() {
		file_api_vendorgateway_moengage_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendorgateway_moengage_service_proto_rawDescData)
	})
	return file_api_vendorgateway_moengage_service_proto_rawDescData
}

var file_api_vendorgateway_moengage_service_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_vendorgateway_moengage_service_proto_goTypes = []interface{}{
	(*MergeUserRequest)(nil),                    // 0: vendorgateway.moengage.MergeUserRequest
	(*MergeUserResponse)(nil),                   // 1: vendorgateway.moengage.MergeUserResponse
	(*GetUserRequest)(nil),                      // 2: vendorgateway.moengage.GetUserRequest
	(*GetUserResponse)(nil),                     // 3: vendorgateway.moengage.GetUserResponse
	(*DeleteUserRequest)(nil),                   // 4: vendorgateway.moengage.DeleteUserRequest
	(*DeleteUserResponse)(nil),                  // 5: vendorgateway.moengage.DeleteUserResponse
	(*vendorgateway.RequestHeader)(nil),         // 6: vendorgateway.RequestHeader
	(*moengage.MergeUserRequest_MergeData)(nil), // 7: vendors.moengage.MergeUserRequest.MergeData
	(*rpc.Status)(nil),                          // 8: rpc.Status
	(*moengage.GetUserRequest_Data)(nil),        // 9: vendors.moengage.GetUserRequest.Data
	(*moengage.GetUserResponse_Data)(nil),       // 10: vendors.moengage.GetUserResponse.Data
}
var file_api_vendorgateway_moengage_service_proto_depIdxs = []int32{
	6,  // 0: vendorgateway.moengage.MergeUserRequest.header:type_name -> vendorgateway.RequestHeader
	7,  // 1: vendorgateway.moengage.MergeUserRequest.merge_data:type_name -> vendors.moengage.MergeUserRequest.MergeData
	8,  // 2: vendorgateway.moengage.MergeUserResponse.status:type_name -> rpc.Status
	6,  // 3: vendorgateway.moengage.GetUserRequest.header:type_name -> vendorgateway.RequestHeader
	9,  // 4: vendorgateway.moengage.GetUserRequest.data:type_name -> vendors.moengage.GetUserRequest.Data
	8,  // 5: vendorgateway.moengage.GetUserResponse.status:type_name -> rpc.Status
	10, // 6: vendorgateway.moengage.GetUserResponse.data:type_name -> vendors.moengage.GetUserResponse.Data
	6,  // 7: vendorgateway.moengage.DeleteUserRequest.header:type_name -> vendorgateway.RequestHeader
	8,  // 8: vendorgateway.moengage.DeleteUserResponse.status:type_name -> rpc.Status
	2,  // 9: vendorgateway.moengage.MoEngage.GetUser:input_type -> vendorgateway.moengage.GetUserRequest
	0,  // 10: vendorgateway.moengage.MoEngage.MergeUser:input_type -> vendorgateway.moengage.MergeUserRequest
	4,  // 11: vendorgateway.moengage.MoEngage.DeleteUser:input_type -> vendorgateway.moengage.DeleteUserRequest
	3,  // 12: vendorgateway.moengage.MoEngage.GetUser:output_type -> vendorgateway.moengage.GetUserResponse
	1,  // 13: vendorgateway.moengage.MoEngage.MergeUser:output_type -> vendorgateway.moengage.MergeUserResponse
	5,  // 14: vendorgateway.moengage.MoEngage.DeleteUser:output_type -> vendorgateway.moengage.DeleteUserResponse
	12, // [12:15] is the sub-list for method output_type
	9,  // [9:12] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_api_vendorgateway_moengage_service_proto_init() }
func file_api_vendorgateway_moengage_service_proto_init() {
	if File_api_vendorgateway_moengage_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendorgateway_moengage_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MergeUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_moengage_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MergeUserResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_moengage_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_moengage_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_moengage_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_moengage_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteUserResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendorgateway_moengage_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_vendorgateway_moengage_service_proto_goTypes,
		DependencyIndexes: file_api_vendorgateway_moengage_service_proto_depIdxs,
		MessageInfos:      file_api_vendorgateway_moengage_service_proto_msgTypes,
	}.Build()
	File_api_vendorgateway_moengage_service_proto = out.File
	file_api_vendorgateway_moengage_service_proto_rawDesc = nil
	file_api_vendorgateway_moengage_service_proto_goTypes = nil
	file_api_vendorgateway_moengage_service_proto_depIdxs = nil
}
