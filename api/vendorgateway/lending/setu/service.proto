syntax = "proto3";

package vendorgateway.lending.setu;

import "api/rpc/status.proto";
import "api/typesv2/address.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/gender.proto";
import "api/vendorgateway/lending/setu/enums.proto";
import "api/vendorgateway/request_header.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/lending/setu";
option java_package = "com.github.epifi.gamma.api.vendorgateway.lending.setu";

service Setu {
  rpc CreateConsent (CreateConsentRequest) returns (CreateConsentResponse);

  rpc GetJourneyStatus (GetJourneyStatusRequest) returns (GetJourneyStatusResponse);
}

message CreateConsentRequest {
  vendorgateway.RequestHeader header = 1;
  ConsentDataRange consent_data_range = 2;
  UserDetails user_details = 3;
  ApplicationType application_type = 4;
  repeated LoanType loan_types = 5;
  // this field allows us to send the maximum loan amount which we are
  // assuming to get as a approved loan amount from setu
  google.type.Money loan_amount = 6;
  int32 consent_duration = 7;

  message ConsentDataRange {
    google.protobuf.Timestamp start = 1;
    google.protobuf.Timestamp end = 2;
  }

  message UserDetails {
    string customer_id = 1;
    string pan = 2;
    api.typesv2.common.Name name = 3;
    string source_ip = 4;
    api.typesv2.PostalAddress address = 5;
    api.typesv2.common.PhoneNumber contact_number = 6;
    string email = 7;
    google.type.Date dob = 8;
    api.typesv2.Gender gender = 9;
    ApplicantType applicant_type = 10;
    string employer_name = 11;
  }
}

message CreateConsentResponse {
  enum Status {
    OK = 0;
    // if offer is already generated for the user post successful consent flow.
    ELIGIBILITY_ALREADY_EXISTS = 101;
  }
  rpc.Status status = 1;
  string consent_id = 2;
  string webview_url = 3;
  repeated ConsentType consent_types = 4;
  ConsentStatus consent_status = 5;
  string vendor_trace_id = 6;
}

message GetJourneyStatusRequest {
  vendorgateway.RequestHeader header = 1;
  string consent_id = 2;
}

message GetJourneyStatusResponse {
  rpc.Status status = 1;
  LoanDecisioningStatus loan_decisioning_status = 2;
  // deprecating in favour of failure_reason_str because we are not aware of the exhaustive set of reasons,
  // and there is no functional need to have stronger type validation on this field.
  FailureReason failure_reason = 3 [deprecated = true];
  OfferDetails offer_details = 4;
  string failure_reason_str = 5;

  message OfferDetails {
    google.type.Money approved_limit = 1;
    double roi = 2;
    google.type.Money max_emi_amount = 3;
    double processing_fees = 4;
  }
}
