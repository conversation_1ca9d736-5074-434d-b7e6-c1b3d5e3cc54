syntax = "proto3";

package vendorgateway.lending.preapprovedloan.idfc;

import "api/accounts/account_type.proto";
import "api/rpc/status.proto";
import "api/typesv2/address.proto";
import "api/typesv2/bank_account_details.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/employment_type.proto";
import "api/typesv2/gender.proto";
import "api/typesv2/marital_status.proto";
import "api/vendorgateway/lending/preapprovedloan/idfc/enums.proto";
import "api/vendorgateway/request_header.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/idfc";
option java_package = "com.github.epifi.gamma.api.vendorgateway.lending.preapprovedloan.idfc";

service Idfc {
  rpc VerifyPan (VerifyPanRequest) returns (VerifyPanResponse);

  rpc SearchCkyc (SearchCkycRequest) returns (SearchCkycResponse);

  rpc DownloadCkyc (DownloadCkycRequest) returns (DownloadCkycResponse);

  rpc CheckStatus (CheckStatusRequest) returns (CheckStatusResponse);

  rpc StartLoan (StartLoanRequest) returns (StartLoanResponse);

  rpc OfflineDataUpload (OfflineDataUploadRequest) returns (OfflineDataUploadResponse);

  rpc CreateLoan (CreateLoanRequest) returns (CreateLoanResponse);

  rpc LoanUtilities (LoanUtilitiesRequest) returns (LoanUtilitiesResponse);

  rpc AutoDisbursal (AutoDisbursalRequest) returns (AutoDisbursalResponse);

  rpc TermLoanCancellation (TermLoanCancellationRequest) returns (TermLoanCancellationResponse);

  rpc ForeClosure (ForeClosureRequest) returns (ForeClosureResponse);

  rpc PaymentPosting (PaymentPostingRequest) returns (PaymentPostingResponse);

  rpc Repayment (RepaymentRequest) returns (RepaymentResponse);

  rpc GetMandateWebPage (GetMandateWebPageRequest) returns (GetMandateWebPageResponse);

  rpc GetMandateStatus (GetMandateStatusRequest) returns (GetMandateStatusResponse);

  // CheckPositiveConfirmation returns the status of positive confirmation from CKYC in an enum. The possible values can be
  // "YES" - KYC details are matched and verified
  // "NO" - VKYC is needed
  // "IN PROGRESS" - verification in progress
  rpc CheckPositiveConfirmation (CheckPositiveConfirmationRequest) returns (CheckPositiveConfirmationResponse);

  rpc CreateVkycProfile (CreateVkycProfileRequest) returns (CreateVkycProfileResponse);

  rpc GetIfscDetails (GetIfscDetailsRequest) returns (GetIfscDetailsResponse);
}

message VerifyPanRequest {
  vendorgateway.RequestHeader header = 1;
  string pan_number = 2;
  string req_id = 3;
  string correlation_id = 4;
  google.type.Date dob = 5;
  api.typesv2.common.Name name = 6;
}

message VerifyPanResponse {
  rpc.Status status = 1;
  PanVerificationResponse pan_verification_response = 2;
  string req_id = 3;

  message PanVerificationResponse {
    string response_id = 1;
    PanVerificationStatus status = 2;
    PanResponse response = 3;

    message PanResponse {
      string pan_number = 1;
      api.typesv2.common.Name name = 2;
      string pan_status = 3;     // TODO check if it is an enum
      string pn_sts = 4;         // TODO check if it is an enum
      string return_code = 5;    // TODO check if it is an enum
      google.type.Date last_updated_date = 6;
      string name_printed_on_pan_card = 7;
      string type_of_holder = 8;  // TODO check if it is an enum
      string adhar_status = 9;
    }
  }
}

message SearchCkycRequest {
  vendorgateway.RequestHeader header = 1;
  CkycRequest ckyc_request = 2;
  string correlation_id = 3;

  message CkycRequest {
    string request_id = 1;
    repeated CkycDetails details = 2;

    message CkycDetails {
      string transaction_id = 1;
      string input_id_type = 2;      // TODO check if it is an enum
      string input_id_no = 3;
    }
  }
}

message SearchCkycResponse {
  rpc.Status status = 1;
  CkycResponse ckyc_response = 2;

  message CkycResponse {
    string request_id = 1;
    string request_status = 2;
    repeated CkycDetails details = 3;

    message CkycDetails {
      string transaction_id = 1;
      string transaction_status = 2;              // TODO check if it is an enum
      string transaction_rejection_code = 3;      // TODO check if it is an enum
      string transaction_rejection_description = 4;
      string branch_code = 5;
      string record_identifier = 6;
      string application_form_no = 7;
      string ckyc_available = 8;
      idfc.CkycAccountType ckyc_acc_type = 9;
      string ckyc_id = 10;
      api.typesv2.common.Name ckyc_name = 11;
      int32 ckyc_age = 12;
      api.typesv2.common.Name ckyc_father_name = 13;
      string ckyc_photo_image_type = 14;           // TODO check if it is an enum
      google.type.Date ckyc_kyc_date = 15;
      google.type.Date ckyc_gen_date = 16;
      string ckyc_photo = 17;
      string ckyc_request_id = 18;
      google.type.Date ckyc_request_date = 19;
      google.type.Date ckyc_updated_date = 20;
      string ckyc_remarks = 21;
      string ckyc_photo_bytes = 22;
    }
  }
}

message DownloadCkycRequest {
  vendorgateway.RequestHeader header = 1;
  string request_id = 2;
  repeated CkycDownloadDetails details = 3;
  string correlation_id = 4;

  message CkycDownloadDetails {
    string transaction_id = 1;
    string ckyc_number = 2;
    google.type.Date dob = 3;
    api.typesv2.common.PhoneNumber mobile_number = 4;
  }
}

message DownloadCkycResponse {
  rpc.Status status = 1;
  string request_id = 2;
  CkycDownloadDetails details = 3;
  repeated PersonalDetails ckyc_personal_detail = 4;
  string request_status = 5;
  repeated ImageDetails image_details = 6;
  PositiveConfirmation positive_confirmation = 7;

  message CkycDownloadDetails {
    string transaction_id = 1;
    idfc.TransactionStatus transaction_status = 2;
    idfc.CkycRejectReason ckyc_reject_reason = 3;
  }

  message PersonalDetails {
    string record_identifier = 1;
    string application_form_no = 2;
    string branch_code = 3;
    string ckyc_consti_type = 4;
    idfc.CkycAccountType ckyc_acc_type = 5;
    string ckyc_number = 6;
    api.typesv2.common.Name ckyc_full_name = 7;
    api.typesv2.common.Name ckyc_maiden_full_name = 8;
    api.typesv2.common.Name ckyc_father_full_name = 9;
    api.typesv2.common.Name ckyc_mother_full_name = 10;
    api.typesv2.Gender ckyc_gender = 11;
    api.typesv2.MaritalStatus ckyc_marital_status = 12;
    api.typesv2.EmploymentType ckyc_occupation = 14;
    google.type.Date ckyc_dob = 15;
    api.typesv2.AddressType ckyc_per_add_type = 22;
    api.typesv2.common.PhoneNumber ckyc_mobile_number = 23;
    string ckyc_email_add = 24;
    google.type.Date ckyc_date_of_declaration = 25;
    string ckyc_place_of_declaration = 26;
    google.type.Date ckyc_kyc_verification_date = 27;
    api.typesv2.PostalAddress permanent_address = 28;
    api.typesv2.PostalAddress correspondence_address = 29;
    CkycAddressPinCodeType address_pin_code_type = 30;
  }

  message ImageDetails {
    string branch = 1;
    string data = 2;
    CkycImageType type = 3;
    string extension = 4;
  }
}

message CheckStatusRequest {
  vendorgateway.RequestHeader header = 1;
  string req_id = 2;
  string correlation_id = 3;
}

message CheckStatusResponse {
  rpc.Status status = 1;
  string crn = 2;
  string loan_application_no = 3;
  idfc.ApplicationStatus application_status = 4;
  idfc.ApplicationRejectReason reject_reason = 5;
  // unique service request number for create vkyc profile request
  string reference_id = 6;
  VKYCStatus vkyc_status = 7;
  string limit_amount = 8;
  string interest_rate = 9;
  string tenure = 10;
  // link to initiate video kyc
  string vkyc_url = 11;
  google.protobuf.Timestamp url_expires_at = 12;
  // e.g: "Pan card mismatch/Damaged pan card"
  string vkyc_reject_reason = 13;
}

message StartLoanRequest {
  vendorgateway.RequestHeader header = 1;
  string req_id = 2;
  idfc.KycMode kyc_mode = 3;
  LivenessCheck liveness_check = 4;
  ComparePhoto compare_photo = 5;
  UnderWriting under_writing = 6;
  string correlation_id = 7;

  message LivenessCheck {
    string source = 1;
    idfc.BooleanResult is_live = 2;
    int32 liveness_score = 3;
    string final_liveness_check_status = 4;
  }

  message ComparePhoto {
    string source = 1;
    idfc.BooleanResult match = 2;
    int32 match_score = 3;
    string final_photo_match_status = 4;
  }

  message UnderWriting {
    string partner_score = 1;
    api.typesv2.EmploymentType employment_type = 2;
    google.type.Money total_income = 3;
    google.type.Money net_monthly_income = 4;
    int32 tenure = 5;
    google.type.Money loan_amount = 6;
    double interest_rate = 7;
    int32 bureau_obligation = 8;
    string vintage_band = 9;
    google.type.Money eligible_loan_amount = 10;
  }
}

message StartLoanResponse {
  enum Status {
    OK = 0;
    FAILED_PRECONDITION_KYC_REJECTED = 101;
    // for "Liveliness / Photomatch checks are failed in validation service" error from vendor
    FAILED_PRECONDITION_PHOTO_MATCH_CHECKS_FAILED = 102;
    // for PAN aadhaar linkage mismatch failure
    FAILED_PRECONDITION_KYC_REJECTED_OVD_ID_MISMATCH = 103;
    // for "KYC expired, please re-fetch demographic details" when KYC (e.g.: CKYC) data is older than 3 days
    KYC_DATA_EXPIRED = 104;
  }
  rpc.Status status = 1;
  string req_id = 2;
  string source = 3;
  string start_loan_status = 4;
  string error_code = 5;
  string error_description = 6;
}

message OfflineDataUploadRequest {
  vendorgateway.RequestHeader header = 1;
  string req_id = 2;
  string source = 3;
  string loan_id = 4;
  idfc.KycMode kyc_mode = 5;
  repeated Document documents = 6;
  repeated Data offline_upload_data = 7;
  string correlation_id = 8;

  message Document {
    string type = 1;
    string source = 2;
    string document_name = 3;
    string file_extension = 4;
    string base64_document_content = 5;
  }

  message Data {
    string type = 1;
    string source = 2;
    string content = 3;
  }
}

message OfflineDataUploadResponse {
  rpc.Status status = 1;
  string req_id = 2;
  string source = 3;
  string offline_data_upload_status = 4;
  string error_code = 5;
  string error_description = 6;
}

message CreateLoanRequest {
  vendorgateway.RequestHeader header = 1;
  string req_id = 2;
  string branch_id = 3;
  string product = 4;
  idfc.CreateLoanRequestType request_type = 5;
  google.type.Money loan_amount = 6;
  int32 scheme = 7;
  int32 tenure = 8;
  string due_day = 9;
  string source_system = 10;
  string crn_no = 11;
  string loan_id = 12;
  string rate_emi_flag = 13;
  double interest_rate = 14;
  string ckyc_no = 15;
  string correlation_id = 16;
  google.type.Money charge_amount = 17;
  string charge_id = 18;
  // name of the borrower
  api.typesv2.common.Name name = 19;
  // gender
  api.typesv2.Gender gender = 20;
  // dateOfBirth
  google.type.Date dob = 21;
  // pan
  string pan = 22;
  api.typesv2.PostalAddress permanent_address = 23;
  api.typesv2.PostalAddress correspondence_address = 24;
  // ip address
  string ip_addr = 25;
  // user's phone number
  api.typesv2.common.PhoneNumber phone_number = 26;
  // kyc type
  string kyc_type = 27; // TODO: To use a common enum type instead of string
  // bank account no
  string bank_acc_no = 28;
  // Savings, current etc
  accounts.Type bank_acc_type = 29;
  ConsentForLoanOnboarding consent_for_loan_onboarding = 30;
}

message ConsentForLoanOnboarding {
  CustomerConsent pan_verify_consent = 1;
  CustomerConsent bureau_pull_consent = 2;
  CustomerConsent uw_data_sharing_consent = 3;
  CustomerConsent kyc_init_consent = 4;
  CustomerConsent kyc_accept_consent = 5;
  CustomerConsent third_party_data_sharing_consent = 6;
  CustomerConsent otp_consent = 7;
  CustomerConsent e_mandate_consent = 8;
  CustomerConsent tnc_consent = 9;
}

message CustomerConsent {
  // timestamp at which consent was provided
  google.protobuf.Timestamp consent_time = 1;
}

message CreateLoanResponse {
  enum Status {
    OK = 0;
    // CreateLoan had already been called for the given loan_id
    // (Business Rule Error;Duplicate Loan Id)
    DUPLICATE_LOAN_ID = 101;
  }
  rpc.Status status = 1;
  CreateLoanResult create_loan_result = 2;
  string req_id = 3;

  message CreateLoanResult {
    string msg = 1;
    repeated LoanResponse loan_response = 2;
    message LoanResponse {
      string loan_id = 1;
      string entity_req_id = 2;
      repeated KfsDetails kfs_details = 3;
      repeated Charges chargess = 4;
      repeated RepaySchedule repay_schedules = 5;
    }
  }
}

message LoanUtilitiesRequest {
  vendorgateway.RequestHeader header = 1;
  // Deprecated: would be populated in VG
  string entity_name = 2 [deprecated = true];
  string agreement_id = 3;
  string crn_no = 4;
  // TODO: use enum
  string request_code = 5;
  string correlation_id = 6;
}

message LoanUtilitiesResponse {
  rpc.Status status = 1;
  string loan_utilities_status = 2;
  string message = 3;
  string code = 4;
  string service = 5;
  // this field will be deprecated in future after we move to the data field
  repeated ResourceData resource_data = 6;
  repeated Data data = 7;

  message Data {
    oneof data {
      // will be populated if request_code is "OUTSTANDINGS"
      OutstandingDetails outstanding_details = 1;
      // add other data types here for other request codes
    }
  }

  message ResourceData {
    string msg = 1;
    string loan_status = 2;
    string boarded_in_lms = 3;
    double roi = 4;
    google.type.Money disbursed_amount = 5;
    string boarded_in_cas = 6;
    google.type.Money pre_emi_charge_amount = 7;
    google.type.Date disbursement_date = 8;
    int64 request_id = 9;
    string loan_id = 10;
  }
}


message OutstandingDetails {
  // principal outstanding = disbursed_amount (total principal) - principal_paid till date
  google.type.Money principal = 1;
  google.type.Money interest = 2;
  google.type.Money charges = 3;
  // total outstanding amount = principal + interest + charges - excess
  google.type.Money total = 4;
  google.type.Money excess = 5;
}

message AutoDisbursalRequest {
  vendorgateway.RequestHeader header = 1;
  string entity_code = 2;
  string pre_auth_id = 3;
  string entity_id = 4;
  string loan_id = 5;
  idfc.AutoDisbursalRequestType req_type = 6;
  string correlation_id = 7;
}

message AutoDisbursalResponse {
  rpc.Status status = 1;
  AutoDisbursalStatus auto_disbursal_status = 2;
  string message = 3;
  repeated ResourceData resource_data = 4;
  string code = 5;
  string service = 6;

  message ResourceData {
    string entity_req_id = 1;
    string loan_id = 2;
    repeated KfsDetails kfs_details = 3;
    repeated Charges charges = 4;
    repeated RepaySchedule repay_schedules = 5;
  }
}

message TermLoanCancellationRequest {
  vendorgateway.RequestHeader header = 1;
  string entity_code = 2;
  string entity_req_id = 3;
  idfc.ProductType product_type = 4;
  string loan_id = 5;
  string dr_req_id = 6;
  string recon_id = 7;
  google.type.Date transaction_date = 8;
  google.type.Money cancel_amount = 9;
  string cancel_reason = 10;
  string correlation_id = 11;
}

message TermLoanCancellationResponse {
  rpc.Status status = 1;
  string term_loan_cancellation_status = 2;
  string message = 3;
  repeated ResourceData resource_data = 4;
  string code = 5;
  string service = 6;

  message ResourceData {
    string entity_req_id = 1;
    string transaction_id = 2;
  }
}

message ForeClosureRequest {
  vendorgateway.RequestHeader header = 1;
  string entity_req_id = 2;
  idfc.SearchType search_type = 3;
  string search_value = 4;
  google.type.Date transaction_date = 5;
  idfc.ActivityType activity_type = 6;
  // Deprecated: will be hardcoded in VG
  idfc.PrepayPenaltyMatrix prepay_penalty_matrix = 7 [deprecated = true];
  uint64 prepay_penalty = 8;
  string correlation_id = 9;
}

message ForeClosureResponse {
  enum Status {
    OK = 0;
    // foreclosure/foreclosure simulation is not allowed on due date
    // e.g.: "Business Rule Error;Foreclosure Simulation Date should be greater than due date ->03-MAY-24."
    FORECLOSURE_SIMULATION_DATE_SHOULD_BE_GREATER_THAN_DUE_DATE = 101;
    // End of day / Beginning of day activities are running
    // e.g.:"EOD/ BOD Running;Please retry once EOD/ BOD is completed."
    EOD_BOD_RUNNING = 102;
    // this status is returned if we are trying to fetch foreclosure details for an inactive loan account
    LOAN_ACCOUNT_INACTIVE = 103;
  }
  rpc.Status status = 1;
  string fore_closure_status = 2;
  string message = 3;
  repeated ResourceData resource_data = 4;
  string code = 5;
  string service = 6;

  message ResourceData {
    string entity_req_id = 1;
    string loan_id = 2;
    google.type.Money foreclosure_amount = 3;
    google.type.Money penalty_amount = 4;
  }
}

message PaymentPostingRequest {
  vendorgateway.RequestHeader header = 1;
  string entity_req_id = 2;
  idfc.SourceSystemName source_system_name = 3;
  // Deprecated: Do not use. Entity name will be populated in VG.
  string entity_name = 4 [deprecated = true];
  string loan_id = 5;
  google.type.Date transaction_date = 6;
  string receipt_no = 7;
  string cheque_no = 8;
  google.type.Money cheque_amount = 9;
  // Deprecated: Do not use.
  string correlation_id = 10 [deprecated = true];
  // Loan EMI which include Principal + Interest component
  // (optional)
  google.type.Money emi_amount = 11;
  // This amount will be sent if there is any late payment penalty levied.
  // (optional)
  google.type.Money lpp_amount = 12;
  // This amount will be sent if there are any cheque bounce charges applied.
  // (optional)
  google.type.Money cbc_amount = 13;
  // This is to be passed for non-digital cases only for online receipts.
  // This charge is applicable if customer changes repayment bank account from one bank to another for paying EMI.
  // (optional)
  google.type.Money swap_amount = 14;
  // This is to be passed for non-digital cases only for online receipts. This is EBC fee (Easy Buy Card) which is collected from customer. If the EBC charges are not existing then FinnOne will create for the amount collected and knockoff same upon receipt posting. In case if there is partial EBC charges then upon posting FinnOne will knockoff the partial EBC amount and then will create new EBC charge for remaining amount and then knockoff the remaining amount again. If there is EBC charge same as amount collected then FinnOne will knockoff with same upon receipt posting.
  // (optional)
  google.type.Money ebc_amount = 15;
  // This is collection pick up amount if the customer is requesting for payment pick up. If the CPC charges are not existing then FinnOne will create for the amount collected and knockoff same upon receipt posting. In case if there is partial CPC charges then upon posting FinnOne will knock off the partial CPC amount and then will create new CPC charge for remaining amount and then knockoff the remaining amount again. If there is CPC charge same as amount collected then FinnOne will knockoff with same upon receipt posting.
  // (optional)
  google.type.Money coll_pick_amount = 16;
  // This is excess amount received which will get allocated with the existing due in FinnOne at EOD through Auto knock off functionality. In case if loan is Writeoff then excess amount will get allocated against balance principal amount first and there after towards other charges present in loan. If no dues in loan then it will appear as RPA in loan.
  // (optional)
  google.type.Money excess_amount = 17;
  // This is free field which is used for capturing certain alphanumeric remarks coming from vendor/partner. Example mode name, batch no, PIS no etc e.g home loan @ 9% interest rate test remark
  // (optional)
  string remarks = 18;
  // Account number where the payment to be credited. This field is also dependent on payment mode.
  // (optional)
  string deposit_account_number = 19;
}

message PaymentPostingResponse {
  rpc.Status status = 1;
  string payment_posting_status = 2;
  string message = 3;
  repeated ResourceData resource_data = 4;
  string code = 5;
  string service = 6;

  message ResourceData {
    string cheque_id = 1;
    string entity_req_id = 2;
  }
}

message RepaymentRequest {
  vendorgateway.RequestHeader header = 1;
  string entity_code = 2;
  string loan_id = 3;
  string correlation_id = 4;
}

message RepaymentResponse {
  rpc.Status status = 1;
  string repayment_status = 2;
  string message = 3;
  string version = 4;
  repeated ResourceData resource_data = 5;
  string code = 6;
  string service = 7;

  message ResourceData {
    string loan_id = 1;
    string instl_num = 2;
    google.type.Date due_date = 3;
    google.type.Money emi = 4;
    google.type.Money prin_comp = 5;
    google.type.Money int_comp = 6;
    google.type.Money installment_received = 7;
    google.type.Money prin_comp_recd = 8;
    google.type.Money int_comp_recd = 9;
    google.type.Money opening_bal_princ = 10;
    google.type.Money closing_bal_princ = 11;
    string bill_flag = 12; // BooleanResult
    double eff_rate = 13;
    int32 days = 14;
    string adv_flag = 15;  // BooleanResult
  }
}

message GetAccessTokenRequest {
  // scope takes the space separated URL values for which this generated token will be valid
  string scope = 1;
}

message GetAccessTokenResponse {
  rpc.Status status = 1;
  string access_token = 2;
  AccessTokenType access_token_type = 3;
  int32 expires_in = 4;
  string scope = 5;
}

message GetMandateWebPageRequest {
  string merchant_txn_id = 1;
  string consumer_id = 2;
  string bank_id = 3;
  google.type.Money txn_amount = 4;
  google.type.Money max_amount = 5;
  google.type.Date debit_start_date = 6;
  google.type.Date debit_end_date = 7;
  api.typesv2.common.PhoneNumber phone_number = 8;
  api.typesv2.BankAccountDetails bank_account_details = 9;
  string email = 10;
  string debit_card_code = 11;
}

message GetMandateWebPageResponse {
  rpc.Status status = 1;
  string html_response = 2;
}

message GetMandateStatusRequest {
  string merchant_txn_id = 1;
  string consumer_id = 2;
  string correlation_id = 3;
}

message GetMandateStatusResponse {
  rpc.Status status = 1;
  MandateStatus mandate_status = 2;
}

// placed this outside as this can be used for multiple APIs
message KfsDetails {
  string agreement_date = 1;
  string agreement_place = 2;
  google.type.Money sanctioned_limit = 3;
  google.type.Money facility_amount = 4;
  google.type.Money loan_amount = 5;
  google.type.Money total_interest = 6;
  google.type.Money process_fee = 7;
  google.type.Money net_disbursed_amount = 8;
  google.type.Money total_payable_amount = 9;
  double app_roi = 10;
  double apr = 11;
  int32 tenure = 12;
  string repay_freq = 13;
  int32 installments_count = 14;
  google.type.Money installment_amount = 15;
  google.type.Money bounce_charge = 16;
  google.type.Money late_pay_charge = 17;
  google.type.Money foreclosure_charge = 18;
  google.type.Money overdue_charges = 19;
  string borrower_detail = 20;
  string bank_name = 21;
  string band_address = 22;
  int32 cool_off_period = 23;
  string lsp_details = 24;
  string phn_number = 25;
}

message Charges {
  string charge_name = 1;
  google.type.Money charge_amount = 2;
}

message RepaySchedule {
  int32 instalment_number = 1;
  google.type.Date due_date = 2;
  google.type.Money outstanding_principal = 3;
  google.type.Money principal_component = 4;
  google.type.Money interest_component = 5;
  google.type.Money installment = 6;
  string loan_id = 7;
}

message CheckPositiveConfirmationRequest {
  vendorgateway.RequestHeader header = 1;
  string req_id = 2;
  string correlation_id = 3;
}

message CheckPositiveConfirmationResponse {
  rpc.Status status = 1;
  string req_id = 2;
  string source = 3;
  PositiveConfirmation positive_confirmation = 4;
}

message CreateVkycProfileRequest {
  vendorgateway.RequestHeader header = 1;
  // unique request id
  string req_id = 2;
  // unique service request number for create vkyc profile request. This is returned by the CheckApprovalStatusResponse
  string reference_id = 3;
  string correlation_id = 4;
}

message CreateVkycProfileResponse {
  enum Status {
    OK = 0;
    // for "KYC expired, please re-fetch demographic details" when KYC (e.g.: CKYC) data is older than 3 days
    KYC_DATA_EXPIRED = 101;
  }
  rpc.Status status = 1;
  string req_id = 2;
  string message = 3;
  string version = 4;
  // profile creation time
  google.protobuf.Timestamp create_time = 5;
  string profile_id = 6;
  // link to initiate VKYC
  string vkyc_url = 7;
  // expiry time of vkyc url
  google.protobuf.Timestamp expire_at = 8;
}

message GetIfscDetailsRequest {
  vendorgateway.RequestHeader header = 1;
  string correlation_id = 2;
  Identifier identifier = 3;
  // keeping this field as separate message for now
  // do not have clarity from IDFC if this should be oneof kind of field
  message Identifier {
    string ifsc_code = 1;
  }
}

message GetIfscDetailsResponse {
  rpc.Status status = 1;
  repeated Details details = 2;
  message Details {
    string debit_card_code = 1;
    string ifsc_code = 2;
    string bank_name = 3;
    string bank_id = 4;
    string net_bank_code = 5;
  }
}
