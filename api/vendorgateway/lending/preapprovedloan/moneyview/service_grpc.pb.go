// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/vendorgateway/lending/preapprovedloan/moneyview/service.proto

package moneyview

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Moneyview_CreateLead_FullMethodName              = "/vendorgateway.lending.preapprovedloan.moneyview.Moneyview/CreateLead"
	Moneyview_GetLeadStatus_FullMethodName           = "/vendorgateway.lending.preapprovedloan.moneyview.Moneyview/GetLeadStatus"
	Moneyview_GetOffers_FullMethodName               = "/vendorgateway.lending.preapprovedloan.moneyview.Moneyview/GetOffers"
	Moneyview_GetPWAJourneyUrl_FullMethodName        = "/vendorgateway.lending.preapprovedloan.moneyview.Moneyview/GetPWAJourneyUrl"
	Moneyview_GetDeDupeStatus_FullMethodName         = "/vendorgateway.lending.preapprovedloan.moneyview.Moneyview/GetDeDupeStatus"
	Moneyview_GetSingleDeDupeStatus_FullMethodName   = "/vendorgateway.lending.preapprovedloan.moneyview.Moneyview/GetSingleDeDupeStatus"
	Moneyview_GenerateMvBlackBoxOffer_FullMethodName = "/vendorgateway.lending.preapprovedloan.moneyview.Moneyview/GenerateMvBlackBoxOffer"
	Moneyview_SaveMvBlackBoxOffer_FullMethodName     = "/vendorgateway.lending.preapprovedloan.moneyview.Moneyview/SaveMvBlackBoxOffer"
)

// MoneyviewClient is the client API for Moneyview service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MoneyviewClient interface {
	// CreateLead is useful to create a lead for a loan offer.
	CreateLead(ctx context.Context, in *CreateLeadRequest, opts ...grpc.CallOption) (*CreateLeadResponse, error)
	// GetLeadStatus is useful to fetch the status of an already created lead.
	GetLeadStatus(ctx context.Context, in *GetLeadStatusRequest, opts ...grpc.CallOption) (*GetLeadStatusResponse, error)
	// GetOffers is useful to fetch the offers from for a given lead.
	GetOffers(ctx context.Context, in *GetOffersRequest, opts ...grpc.CallOption) (*GetOffersResponse, error)
	// GetPWAJourneyUrl is useful to fetch the moneyview PWA journey url for a loan application/account created for a given lead.
	GetPWAJourneyUrl(ctx context.Context, in *GetPWAJourneyUrlRequest, opts ...grpc.CallOption) (*GetPWAJourneyUrlResponse, error)
	// GetDeDupeStatus is useful to check if some loan request is already in progress.
	GetDeDupeStatus(ctx context.Context, in *GetDeDupeStatusRequest, opts ...grpc.CallOption) (*GetDeDupeStatusResponse, error)
	// GetSingleDeDupeStatus is useful to check if a loan request is already in progress for a single user.
	GetSingleDeDupeStatus(ctx context.Context, in *GetSingleDeDupeStatusRequest, opts ...grpc.CallOption) (*GetSingleDeDupeStatusResponse, error)
	// GenerateMvBlackBoxOffer makes a call to MV blackbox to get offer detials for a user
	GenerateMvBlackBoxOffer(ctx context.Context, in *GenerateMvBlackBoxOfferRequest, opts ...grpc.CallOption) (*GenerateMvBlackBoxOfferResponse, error)
	// This rpc saves the  offer details created by MV BlackBox with MV
	SaveMvBlackBoxOffer(ctx context.Context, in *SaveMvBlackBoxOfferRequest, opts ...grpc.CallOption) (*SaveMvBlackBoxOfferResponse, error)
}

type moneyviewClient struct {
	cc grpc.ClientConnInterface
}

func NewMoneyviewClient(cc grpc.ClientConnInterface) MoneyviewClient {
	return &moneyviewClient{cc}
}

func (c *moneyviewClient) CreateLead(ctx context.Context, in *CreateLeadRequest, opts ...grpc.CallOption) (*CreateLeadResponse, error) {
	out := new(CreateLeadResponse)
	err := c.cc.Invoke(ctx, Moneyview_CreateLead_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moneyviewClient) GetLeadStatus(ctx context.Context, in *GetLeadStatusRequest, opts ...grpc.CallOption) (*GetLeadStatusResponse, error) {
	out := new(GetLeadStatusResponse)
	err := c.cc.Invoke(ctx, Moneyview_GetLeadStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moneyviewClient) GetOffers(ctx context.Context, in *GetOffersRequest, opts ...grpc.CallOption) (*GetOffersResponse, error) {
	out := new(GetOffersResponse)
	err := c.cc.Invoke(ctx, Moneyview_GetOffers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moneyviewClient) GetPWAJourneyUrl(ctx context.Context, in *GetPWAJourneyUrlRequest, opts ...grpc.CallOption) (*GetPWAJourneyUrlResponse, error) {
	out := new(GetPWAJourneyUrlResponse)
	err := c.cc.Invoke(ctx, Moneyview_GetPWAJourneyUrl_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moneyviewClient) GetDeDupeStatus(ctx context.Context, in *GetDeDupeStatusRequest, opts ...grpc.CallOption) (*GetDeDupeStatusResponse, error) {
	out := new(GetDeDupeStatusResponse)
	err := c.cc.Invoke(ctx, Moneyview_GetDeDupeStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moneyviewClient) GetSingleDeDupeStatus(ctx context.Context, in *GetSingleDeDupeStatusRequest, opts ...grpc.CallOption) (*GetSingleDeDupeStatusResponse, error) {
	out := new(GetSingleDeDupeStatusResponse)
	err := c.cc.Invoke(ctx, Moneyview_GetSingleDeDupeStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moneyviewClient) GenerateMvBlackBoxOffer(ctx context.Context, in *GenerateMvBlackBoxOfferRequest, opts ...grpc.CallOption) (*GenerateMvBlackBoxOfferResponse, error) {
	out := new(GenerateMvBlackBoxOfferResponse)
	err := c.cc.Invoke(ctx, Moneyview_GenerateMvBlackBoxOffer_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moneyviewClient) SaveMvBlackBoxOffer(ctx context.Context, in *SaveMvBlackBoxOfferRequest, opts ...grpc.CallOption) (*SaveMvBlackBoxOfferResponse, error) {
	out := new(SaveMvBlackBoxOfferResponse)
	err := c.cc.Invoke(ctx, Moneyview_SaveMvBlackBoxOffer_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MoneyviewServer is the server API for Moneyview service.
// All implementations should embed UnimplementedMoneyviewServer
// for forward compatibility
type MoneyviewServer interface {
	// CreateLead is useful to create a lead for a loan offer.
	CreateLead(context.Context, *CreateLeadRequest) (*CreateLeadResponse, error)
	// GetLeadStatus is useful to fetch the status of an already created lead.
	GetLeadStatus(context.Context, *GetLeadStatusRequest) (*GetLeadStatusResponse, error)
	// GetOffers is useful to fetch the offers from for a given lead.
	GetOffers(context.Context, *GetOffersRequest) (*GetOffersResponse, error)
	// GetPWAJourneyUrl is useful to fetch the moneyview PWA journey url for a loan application/account created for a given lead.
	GetPWAJourneyUrl(context.Context, *GetPWAJourneyUrlRequest) (*GetPWAJourneyUrlResponse, error)
	// GetDeDupeStatus is useful to check if some loan request is already in progress.
	GetDeDupeStatus(context.Context, *GetDeDupeStatusRequest) (*GetDeDupeStatusResponse, error)
	// GetSingleDeDupeStatus is useful to check if a loan request is already in progress for a single user.
	GetSingleDeDupeStatus(context.Context, *GetSingleDeDupeStatusRequest) (*GetSingleDeDupeStatusResponse, error)
	// GenerateMvBlackBoxOffer makes a call to MV blackbox to get offer detials for a user
	GenerateMvBlackBoxOffer(context.Context, *GenerateMvBlackBoxOfferRequest) (*GenerateMvBlackBoxOfferResponse, error)
	// This rpc saves the  offer details created by MV BlackBox with MV
	SaveMvBlackBoxOffer(context.Context, *SaveMvBlackBoxOfferRequest) (*SaveMvBlackBoxOfferResponse, error)
}

// UnimplementedMoneyviewServer should be embedded to have forward compatible implementations.
type UnimplementedMoneyviewServer struct {
}

func (UnimplementedMoneyviewServer) CreateLead(context.Context, *CreateLeadRequest) (*CreateLeadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateLead not implemented")
}
func (UnimplementedMoneyviewServer) GetLeadStatus(context.Context, *GetLeadStatusRequest) (*GetLeadStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLeadStatus not implemented")
}
func (UnimplementedMoneyviewServer) GetOffers(context.Context, *GetOffersRequest) (*GetOffersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOffers not implemented")
}
func (UnimplementedMoneyviewServer) GetPWAJourneyUrl(context.Context, *GetPWAJourneyUrlRequest) (*GetPWAJourneyUrlResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPWAJourneyUrl not implemented")
}
func (UnimplementedMoneyviewServer) GetDeDupeStatus(context.Context, *GetDeDupeStatusRequest) (*GetDeDupeStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDeDupeStatus not implemented")
}
func (UnimplementedMoneyviewServer) GetSingleDeDupeStatus(context.Context, *GetSingleDeDupeStatusRequest) (*GetSingleDeDupeStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSingleDeDupeStatus not implemented")
}
func (UnimplementedMoneyviewServer) GenerateMvBlackBoxOffer(context.Context, *GenerateMvBlackBoxOfferRequest) (*GenerateMvBlackBoxOfferResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateMvBlackBoxOffer not implemented")
}
func (UnimplementedMoneyviewServer) SaveMvBlackBoxOffer(context.Context, *SaveMvBlackBoxOfferRequest) (*SaveMvBlackBoxOfferResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveMvBlackBoxOffer not implemented")
}

// UnsafeMoneyviewServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MoneyviewServer will
// result in compilation errors.
type UnsafeMoneyviewServer interface {
	mustEmbedUnimplementedMoneyviewServer()
}

func RegisterMoneyviewServer(s grpc.ServiceRegistrar, srv MoneyviewServer) {
	s.RegisterService(&Moneyview_ServiceDesc, srv)
}

func _Moneyview_CreateLead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateLeadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoneyviewServer).CreateLead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Moneyview_CreateLead_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoneyviewServer).CreateLead(ctx, req.(*CreateLeadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Moneyview_GetLeadStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLeadStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoneyviewServer).GetLeadStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Moneyview_GetLeadStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoneyviewServer).GetLeadStatus(ctx, req.(*GetLeadStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Moneyview_GetOffers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOffersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoneyviewServer).GetOffers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Moneyview_GetOffers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoneyviewServer).GetOffers(ctx, req.(*GetOffersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Moneyview_GetPWAJourneyUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPWAJourneyUrlRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoneyviewServer).GetPWAJourneyUrl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Moneyview_GetPWAJourneyUrl_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoneyviewServer).GetPWAJourneyUrl(ctx, req.(*GetPWAJourneyUrlRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Moneyview_GetDeDupeStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDeDupeStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoneyviewServer).GetDeDupeStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Moneyview_GetDeDupeStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoneyviewServer).GetDeDupeStatus(ctx, req.(*GetDeDupeStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Moneyview_GetSingleDeDupeStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSingleDeDupeStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoneyviewServer).GetSingleDeDupeStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Moneyview_GetSingleDeDupeStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoneyviewServer).GetSingleDeDupeStatus(ctx, req.(*GetSingleDeDupeStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Moneyview_GenerateMvBlackBoxOffer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateMvBlackBoxOfferRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoneyviewServer).GenerateMvBlackBoxOffer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Moneyview_GenerateMvBlackBoxOffer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoneyviewServer).GenerateMvBlackBoxOffer(ctx, req.(*GenerateMvBlackBoxOfferRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Moneyview_SaveMvBlackBoxOffer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveMvBlackBoxOfferRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoneyviewServer).SaveMvBlackBoxOffer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Moneyview_SaveMvBlackBoxOffer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoneyviewServer).SaveMvBlackBoxOffer(ctx, req.(*SaveMvBlackBoxOfferRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Moneyview_ServiceDesc is the grpc.ServiceDesc for Moneyview service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Moneyview_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "vendorgateway.lending.preapprovedloan.moneyview.Moneyview",
	HandlerType: (*MoneyviewServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateLead",
			Handler:    _Moneyview_CreateLead_Handler,
		},
		{
			MethodName: "GetLeadStatus",
			Handler:    _Moneyview_GetLeadStatus_Handler,
		},
		{
			MethodName: "GetOffers",
			Handler:    _Moneyview_GetOffers_Handler,
		},
		{
			MethodName: "GetPWAJourneyUrl",
			Handler:    _Moneyview_GetPWAJourneyUrl_Handler,
		},
		{
			MethodName: "GetDeDupeStatus",
			Handler:    _Moneyview_GetDeDupeStatus_Handler,
		},
		{
			MethodName: "GetSingleDeDupeStatus",
			Handler:    _Moneyview_GetSingleDeDupeStatus_Handler,
		},
		{
			MethodName: "GenerateMvBlackBoxOffer",
			Handler:    _Moneyview_GenerateMvBlackBoxOffer_Handler,
		},
		{
			MethodName: "SaveMvBlackBoxOffer",
			Handler:    _Moneyview_SaveMvBlackBoxOffer_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/vendorgateway/lending/preapprovedloan/moneyview/service.proto",
}
