// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendorgateway/lending/preapprovedloan/moneyview/enums.proto

package moneyview

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EmploymentType int32

const (
	EmploymentType_EMPLOYMENT_TYPE_UNSPECIFIED   EmploymentType = 0
	EmploymentType_EMPLOYMENT_TYPE_SALARIED      EmploymentType = 1
	EmploymentType_EMPLOYMENT_TYPE_SELF_EMPLOYED EmploymentType = 2
)

// Enum value maps for EmploymentType.
var (
	EmploymentType_name = map[int32]string{
		0: "EMPLOYMENT_TYPE_UNSPECIFIED",
		1: "EMPLOYMENT_TYPE_SALARIED",
		2: "EMPLOYMENT_TYPE_SELF_EMPLOYED",
	}
	EmploymentType_value = map[string]int32{
		"EMPLOYMENT_TYPE_UNSPECIFIED":   0,
		"EMPLOYMENT_TYPE_SALARIED":      1,
		"EMPLOYMENT_TYPE_SELF_EMPLOYED": 2,
	}
)

func (x EmploymentType) Enum() *EmploymentType {
	p := new(EmploymentType)
	*p = x
	return p
}

func (x EmploymentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EmploymentType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_enumTypes[0].Descriptor()
}

func (EmploymentType) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_enumTypes[0]
}

func (x EmploymentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EmploymentType.Descriptor instead.
func (EmploymentType) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_rawDescGZIP(), []int{0}
}

type AddressType int32

const (
	AddressType_ADDRESS_TYPE_UNSPECIFIED AddressType = 0
	AddressType_ADDRESS_TYPE_CURRENT     AddressType = 1
	AddressType_ADDRESS_TYPE_WORK        AddressType = 2
)

// Enum value maps for AddressType.
var (
	AddressType_name = map[int32]string{
		0: "ADDRESS_TYPE_UNSPECIFIED",
		1: "ADDRESS_TYPE_CURRENT",
		2: "ADDRESS_TYPE_WORK",
	}
	AddressType_value = map[string]int32{
		"ADDRESS_TYPE_UNSPECIFIED": 0,
		"ADDRESS_TYPE_CURRENT":     1,
		"ADDRESS_TYPE_WORK":        2,
	}
)

func (x AddressType) Enum() *AddressType {
	p := new(AddressType)
	*p = x
	return p
}

func (x AddressType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AddressType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_enumTypes[1].Descriptor()
}

func (AddressType) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_enumTypes[1]
}

func (x AddressType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AddressType.Descriptor instead.
func (AddressType) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_rawDescGZIP(), []int{1}
}

type LeadStatus int32

const (
	LeadStatus_LEAD_STATUS_UNSPECIFIED LeadStatus = 0
	// denotes that lead is created
	LeadStatus_LEAD_STATUS_CREATED LeadStatus = 1
	// denotes that a loan offer is created for the lead.
	LeadStatus_LEAD_STATUS_OFFERED LeadStatus = 2
	// denotes that the loan application is in pending state.
	LeadStatus_LEAD_STATUS_PENDING LeadStatus = 3
	// denotes that nach stage has just started for the user in loan application flow.
	LeadStatus_LEAD_STATUS_NACH_READY LeadStatus = 4
	// denotes that loan application has expired.
	LeadStatus_LEAD_STATUS_EXPIRED LeadStatus = 5
	// denotes that loan application was rejected.
	LeadStatus_LEAD_STATUS_REJECTED LeadStatus = 6
	// denotes that the loan application was approved.
	LeadStatus_LEAD_STATUS_APPROVED LeadStatus = 7
	// denotes that the loan amount is disbursed.
	LeadStatus_LEAD_STATUS_DISBURSED LeadStatus = 8
	// denotes that loan account is closed.
	LeadStatus_LEAD_STATUS_CLOSED LeadStatus = 9
)

// Enum value maps for LeadStatus.
var (
	LeadStatus_name = map[int32]string{
		0: "LEAD_STATUS_UNSPECIFIED",
		1: "LEAD_STATUS_CREATED",
		2: "LEAD_STATUS_OFFERED",
		3: "LEAD_STATUS_PENDING",
		4: "LEAD_STATUS_NACH_READY",
		5: "LEAD_STATUS_EXPIRED",
		6: "LEAD_STATUS_REJECTED",
		7: "LEAD_STATUS_APPROVED",
		8: "LEAD_STATUS_DISBURSED",
		9: "LEAD_STATUS_CLOSED",
	}
	LeadStatus_value = map[string]int32{
		"LEAD_STATUS_UNSPECIFIED": 0,
		"LEAD_STATUS_CREATED":     1,
		"LEAD_STATUS_OFFERED":     2,
		"LEAD_STATUS_PENDING":     3,
		"LEAD_STATUS_NACH_READY":  4,
		"LEAD_STATUS_EXPIRED":     5,
		"LEAD_STATUS_REJECTED":    6,
		"LEAD_STATUS_APPROVED":    7,
		"LEAD_STATUS_DISBURSED":   8,
		"LEAD_STATUS_CLOSED":      9,
	}
)

func (x LeadStatus) Enum() *LeadStatus {
	p := new(LeadStatus)
	*p = x
	return p
}

func (x LeadStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LeadStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_enumTypes[2].Descriptor()
}

func (LeadStatus) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_enumTypes[2]
}

func (x LeadStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LeadStatus.Descriptor instead.
func (LeadStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_rawDescGZIP(), []int{2}
}

type MvUserType int32

const (
	MvUserType_MV_USER_TYPE_UNSPECIFIED       MvUserType = 0
	MvUserType_MV_USER_TYPE_WHITELISTED_OFFER MvUserType = 1
	MvUserType_MV_USER_TYPE_OPEN_MARKET       MvUserType = 2
)

// Enum value maps for MvUserType.
var (
	MvUserType_name = map[int32]string{
		0: "MV_USER_TYPE_UNSPECIFIED",
		1: "MV_USER_TYPE_WHITELISTED_OFFER",
		2: "MV_USER_TYPE_OPEN_MARKET",
	}
	MvUserType_value = map[string]int32{
		"MV_USER_TYPE_UNSPECIFIED":       0,
		"MV_USER_TYPE_WHITELISTED_OFFER": 1,
		"MV_USER_TYPE_OPEN_MARKET":       2,
	}
)

func (x MvUserType) Enum() *MvUserType {
	p := new(MvUserType)
	*p = x
	return p
}

func (x MvUserType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MvUserType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_enumTypes[3].Descriptor()
}

func (MvUserType) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_enumTypes[3]
}

func (x MvUserType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MvUserType.Descriptor instead.
func (MvUserType) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_rawDescGZIP(), []int{3}
}

type DeDupeStatus int32

const (
	DeDupeStatus_DEDUPE_STATUS_UNSPECIFIED DeDupeStatus = 0
	// denotes that shared pan passes the dedupe check i.e currently user does not has any active loan application at moneyview's end.
	DeDupeStatus_DEDUPE_STATUS_DEDUPE_PASSED DeDupeStatus = 1
	// denotes that shared pan does NOT passed the dedupe check i.e currently user does has an active loan application at moneyview's end.
	DeDupeStatus_DEDUPE_STATUS_DEDUPE_FAILED DeDupeStatus = 2
)

// Enum value maps for DeDupeStatus.
var (
	DeDupeStatus_name = map[int32]string{
		0: "DEDUPE_STATUS_UNSPECIFIED",
		1: "DEDUPE_STATUS_DEDUPE_PASSED",
		2: "DEDUPE_STATUS_DEDUPE_FAILED",
	}
	DeDupeStatus_value = map[string]int32{
		"DEDUPE_STATUS_UNSPECIFIED":   0,
		"DEDUPE_STATUS_DEDUPE_PASSED": 1,
		"DEDUPE_STATUS_DEDUPE_FAILED": 2,
	}
)

func (x DeDupeStatus) Enum() *DeDupeStatus {
	p := new(DeDupeStatus)
	*p = x
	return p
}

func (x DeDupeStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeDupeStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_enumTypes[4].Descriptor()
}

func (DeDupeStatus) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_enumTypes[4]
}

func (x DeDupeStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeDupeStatus.Descriptor instead.
func (DeDupeStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_rawDescGZIP(), []int{4}
}

type BlackBoxOfferGenerationStatus int32

const (
	BlackBoxOfferGenerationStatus_BLACK_BOX_OFFER_GENERATION_STATUS_UNSPECIFIED BlackBoxOfferGenerationStatus = 0
	BlackBoxOfferGenerationStatus_BLACK_BOX_OFFER_GENERATION_STATUS_SUCCESS     BlackBoxOfferGenerationStatus = 1
	BlackBoxOfferGenerationStatus_BLACK_BOX_OFFER_GENERATION_STATUS_REJECTED    BlackBoxOfferGenerationStatus = 2
)

// Enum value maps for BlackBoxOfferGenerationStatus.
var (
	BlackBoxOfferGenerationStatus_name = map[int32]string{
		0: "BLACK_BOX_OFFER_GENERATION_STATUS_UNSPECIFIED",
		1: "BLACK_BOX_OFFER_GENERATION_STATUS_SUCCESS",
		2: "BLACK_BOX_OFFER_GENERATION_STATUS_REJECTED",
	}
	BlackBoxOfferGenerationStatus_value = map[string]int32{
		"BLACK_BOX_OFFER_GENERATION_STATUS_UNSPECIFIED": 0,
		"BLACK_BOX_OFFER_GENERATION_STATUS_SUCCESS":     1,
		"BLACK_BOX_OFFER_GENERATION_STATUS_REJECTED":    2,
	}
)

func (x BlackBoxOfferGenerationStatus) Enum() *BlackBoxOfferGenerationStatus {
	p := new(BlackBoxOfferGenerationStatus)
	*p = x
	return p
}

func (x BlackBoxOfferGenerationStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BlackBoxOfferGenerationStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_enumTypes[5].Descriptor()
}

func (BlackBoxOfferGenerationStatus) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_enumTypes[5]
}

func (x BlackBoxOfferGenerationStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BlackBoxOfferGenerationStatus.Descriptor instead.
func (BlackBoxOfferGenerationStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_rawDescGZIP(), []int{5}
}

var File_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto protoreflect.FileDescriptor

var file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_rawDesc = []byte{
	0x0a, 0x3f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x72, 0x65, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x6d, 0x6f, 0x6e, 0x65,
	0x79, 0x76, 0x69, 0x65, 0x77, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x76, 0x69,
	0x65, 0x77, 0x2a, 0x72, 0x0a, 0x0e, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x1b, 0x45, 0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x4d, 0x45,
	0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x45, 0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x49, 0x45,
	0x44, 0x10, 0x01, 0x12, 0x21, 0x0a, 0x1d, 0x45, 0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x45, 0x4c, 0x46, 0x5f, 0x45, 0x4d, 0x50, 0x4c,
	0x4f, 0x59, 0x45, 0x44, 0x10, 0x02, 0x2a, 0x5c, 0x0a, 0x0b, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x43, 0x55, 0x52, 0x52, 0x45, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x15, 0x0a,
	0x11, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x57, 0x4f,
	0x52, 0x4b, 0x10, 0x02, 0x2a, 0x90, 0x02, 0x0a, 0x0a, 0x4c, 0x65, 0x61, 0x64, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x17, 0x4c, 0x45, 0x41, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x17, 0x0a, 0x13, 0x4c, 0x45, 0x41, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x4c, 0x45, 0x41,
	0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x45, 0x44,
	0x10, 0x02, 0x12, 0x17, 0x0a, 0x13, 0x4c, 0x45, 0x41, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x1a, 0x0a, 0x16, 0x4c,
	0x45, 0x41, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4e, 0x41, 0x43, 0x48, 0x5f,
	0x52, 0x45, 0x41, 0x44, 0x59, 0x10, 0x04, 0x12, 0x17, 0x0a, 0x13, 0x4c, 0x45, 0x41, 0x44, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0x05,
	0x12, 0x18, 0x0a, 0x14, 0x4c, 0x45, 0x41, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x06, 0x12, 0x18, 0x0a, 0x14, 0x4c, 0x45,
	0x41, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56,
	0x45, 0x44, 0x10, 0x07, 0x12, 0x19, 0x0a, 0x15, 0x4c, 0x45, 0x41, 0x44, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x44, 0x49, 0x53, 0x42, 0x55, 0x52, 0x53, 0x45, 0x44, 0x10, 0x08, 0x12,
	0x16, 0x0a, 0x12, 0x4c, 0x45, 0x41, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43,
	0x4c, 0x4f, 0x53, 0x45, 0x44, 0x10, 0x09, 0x2a, 0x6c, 0x0a, 0x0a, 0x4d, 0x76, 0x55, 0x73, 0x65,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x4d, 0x56, 0x5f, 0x55, 0x53, 0x45, 0x52,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x22, 0x0a, 0x1e, 0x4d, 0x56, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x57, 0x48, 0x49, 0x54, 0x45, 0x4c, 0x49, 0x53, 0x54, 0x45, 0x44, 0x5f,
	0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x4d, 0x56, 0x5f, 0x55, 0x53,
	0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f, 0x50, 0x45, 0x4e, 0x5f, 0x4d, 0x41, 0x52,
	0x4b, 0x45, 0x54, 0x10, 0x02, 0x2a, 0x6f, 0x0a, 0x0c, 0x44, 0x65, 0x44, 0x75, 0x70, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x19, 0x44, 0x45, 0x44, 0x55, 0x50, 0x45, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x44, 0x45, 0x44, 0x55, 0x50, 0x45, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x45, 0x44, 0x55, 0x50, 0x45, 0x5f, 0x50, 0x41, 0x53,
	0x53, 0x45, 0x44, 0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x44, 0x45, 0x44, 0x55, 0x50, 0x45, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x45, 0x44, 0x55, 0x50, 0x45, 0x5f, 0x46, 0x41,
	0x49, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x2a, 0xb1, 0x01, 0x0a, 0x1d, 0x42, 0x6c, 0x61, 0x63, 0x6b,
	0x42, 0x6f, 0x78, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x31, 0x0a, 0x2d, 0x42, 0x4c, 0x41, 0x43,
	0x4b, 0x5f, 0x42, 0x4f, 0x58, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x47, 0x45, 0x4e, 0x45,
	0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2d, 0x0a, 0x29, 0x42,
	0x4c, 0x41, 0x43, 0x4b, 0x5f, 0x42, 0x4f, 0x58, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x47,
	0x45, 0x4e, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x2e, 0x0a, 0x2a, 0x42, 0x4c,
	0x41, 0x43, 0x4b, 0x5f, 0x42, 0x4f, 0x58, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x47, 0x45,
	0x4e, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x02, 0x42, 0x98, 0x01, 0x0a, 0x4a, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e,
	0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x76, 0x69, 0x65, 0x77, 0x5a, 0x4a, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x72, 0x65, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x6d, 0x6f, 0x6e, 0x65,
	0x79, 0x76, 0x69, 0x65, 0x77, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_rawDescOnce sync.Once
	file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_rawDescData = file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_rawDesc
)

func file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_rawDescGZIP() []byte {
	file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_rawDescOnce.Do(func() {
		file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_rawDescData)
	})
	return file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_rawDescData
}

var file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_goTypes = []interface{}{
	(EmploymentType)(0),                // 0: vendorgateway.lending.preapprovedloan.moneyview.EmploymentType
	(AddressType)(0),                   // 1: vendorgateway.lending.preapprovedloan.moneyview.AddressType
	(LeadStatus)(0),                    // 2: vendorgateway.lending.preapprovedloan.moneyview.LeadStatus
	(MvUserType)(0),                    // 3: vendorgateway.lending.preapprovedloan.moneyview.MvUserType
	(DeDupeStatus)(0),                  // 4: vendorgateway.lending.preapprovedloan.moneyview.DeDupeStatus
	(BlackBoxOfferGenerationStatus)(0), // 5: vendorgateway.lending.preapprovedloan.moneyview.BlackBoxOfferGenerationStatus
}
var file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_init() }
func file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_init() {
	if File_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_goTypes,
		DependencyIndexes: file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_depIdxs,
		EnumInfos:         file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_enumTypes,
	}.Build()
	File_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto = out.File
	file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_rawDesc = nil
	file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_goTypes = nil
	file_api_vendorgateway_lending_preapprovedloan_moneyview_enums_proto_depIdxs = nil
}
