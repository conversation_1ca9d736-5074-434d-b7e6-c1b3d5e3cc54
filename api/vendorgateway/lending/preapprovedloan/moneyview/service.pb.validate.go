// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendorgateway/lending/preapprovedloan/moneyview/service.proto

package moneyview

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	typesv2 "github.com/epifi/gamma/api/typesv2"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = typesv2.Gender(0)
)

// Validate checks the field values on GetDeDupeStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDeDupeStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDeDupeStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDeDupeStatusRequestMultiError, or nil if none found.
func (m *GetDeDupeStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDeDupeStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDeDupeStatusRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDeDupeStatusRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDeDupeStatusRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if l := len(m.GetPans()); l < 1 || l > 1000 {
		err := GetDeDupeStatusRequestValidationError{
			field:  "Pans",
			reason: "value must contain between 1 and 1000 items, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _GetDeDupeStatusRequest_UserType_NotInLookup[m.GetUserType()]; ok {
		err := GetDeDupeStatusRequestValidationError{
			field:  "UserType",
			reason: "value must not be in list [MV_USER_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetDeDupeStatusRequestMultiError(errors)
	}

	return nil
}

// GetDeDupeStatusRequestMultiError is an error wrapping multiple validation
// errors returned by GetDeDupeStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type GetDeDupeStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDeDupeStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDeDupeStatusRequestMultiError) AllErrors() []error { return m }

// GetDeDupeStatusRequestValidationError is the validation error returned by
// GetDeDupeStatusRequest.Validate if the designated constraints aren't met.
type GetDeDupeStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDeDupeStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDeDupeStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDeDupeStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDeDupeStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDeDupeStatusRequestValidationError) ErrorName() string {
	return "GetDeDupeStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetDeDupeStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDeDupeStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDeDupeStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDeDupeStatusRequestValidationError{}

var _GetDeDupeStatusRequest_UserType_NotInLookup = map[MvUserType]struct{}{
	0: {},
}

// Validate checks the field values on GetDeDupeStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDeDupeStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDeDupeStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDeDupeStatusResponseMultiError, or nil if none found.
func (m *GetDeDupeStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDeDupeStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDeDupeStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDeDupeStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDeDupeStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetPanDedupeStatuses() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetDeDupeStatusResponseValidationError{
						field:  fmt.Sprintf("PanDedupeStatuses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetDeDupeStatusResponseValidationError{
						field:  fmt.Sprintf("PanDedupeStatuses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetDeDupeStatusResponseValidationError{
					field:  fmt.Sprintf("PanDedupeStatuses[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetDeDupeStatusResponseMultiError(errors)
	}

	return nil
}

// GetDeDupeStatusResponseMultiError is an error wrapping multiple validation
// errors returned by GetDeDupeStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type GetDeDupeStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDeDupeStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDeDupeStatusResponseMultiError) AllErrors() []error { return m }

// GetDeDupeStatusResponseValidationError is the validation error returned by
// GetDeDupeStatusResponse.Validate if the designated constraints aren't met.
type GetDeDupeStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDeDupeStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDeDupeStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDeDupeStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDeDupeStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDeDupeStatusResponseValidationError) ErrorName() string {
	return "GetDeDupeStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetDeDupeStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDeDupeStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDeDupeStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDeDupeStatusResponseValidationError{}

// Validate checks the field values on GenerateAuthTokenResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateAuthTokenResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateAuthTokenResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateAuthTokenResponseMultiError, or nil if none found.
func (m *GenerateAuthTokenResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateAuthTokenResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateAuthTokenResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateAuthTokenResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateAuthTokenResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AuthToken

	if len(errors) > 0 {
		return GenerateAuthTokenResponseMultiError(errors)
	}

	return nil
}

// GenerateAuthTokenResponseMultiError is an error wrapping multiple validation
// errors returned by GenerateAuthTokenResponse.ValidateAll() if the
// designated constraints aren't met.
type GenerateAuthTokenResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateAuthTokenResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateAuthTokenResponseMultiError) AllErrors() []error { return m }

// GenerateAuthTokenResponseValidationError is the validation error returned by
// GenerateAuthTokenResponse.Validate if the designated constraints aren't met.
type GenerateAuthTokenResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateAuthTokenResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateAuthTokenResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateAuthTokenResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateAuthTokenResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateAuthTokenResponseValidationError) ErrorName() string {
	return "GenerateAuthTokenResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateAuthTokenResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateAuthTokenResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateAuthTokenResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateAuthTokenResponseValidationError{}

// Validate checks the field values on CreateLeadRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateLeadRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateLeadRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateLeadRequestMultiError, or nil if none found.
func (m *CreateLeadRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateLeadRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLeadRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLeadRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLeadRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetPartnerRefId()) < 1 {
		err := CreateLeadRequestValidationError{
			field:  "PartnerRefId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetPhoneNumber() == nil {
		err := CreateLeadRequestValidationError{
			field:  "PhoneNumber",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLeadRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLeadRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLeadRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetPan()) < 1 {
		err := CreateLeadRequestValidationError{
			field:  "Pan",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetName() == nil {
		err := CreateLeadRequestValidationError{
			field:  "Name",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLeadRequestValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLeadRequestValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLeadRequestValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Gender

	if m.GetDob() == nil {
		err := CreateLeadRequestValidationError{
			field:  "Dob",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLeadRequestValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLeadRequestValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLeadRequestValidationError{
				field:  "Dob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if _, ok := _CreateLeadRequest_EmploymentType_NotInLookup[m.GetEmploymentType()]; ok {
		err := CreateLeadRequestValidationError{
			field:  "EmploymentType",
			reason: "value must not be in list [EMPLOYMENT_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetDeclaredIncome() == nil {
		err := CreateLeadRequestValidationError{
			field:  "DeclaredIncome",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetDeclaredIncome()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLeadRequestValidationError{
					field:  "DeclaredIncome",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLeadRequestValidationError{
					field:  "DeclaredIncome",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeclaredIncome()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLeadRequestValidationError{
				field:  "DeclaredIncome",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(m.GetAddresses()) < 1 {
		err := CreateLeadRequestValidationError{
			field:  "Addresses",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetAddresses() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateLeadRequestValidationError{
						field:  fmt.Sprintf("Addresses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateLeadRequestValidationError{
						field:  fmt.Sprintf("Addresses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateLeadRequestValidationError{
					field:  fmt.Sprintf("Addresses[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if utf8.RuneCountInString(m.GetEmailId()) < 1 {
		err := CreateLeadRequestValidationError{
			field:  "EmailId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAnnualFamilyIncome()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLeadRequestValidationError{
					field:  "AnnualFamilyIncome",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLeadRequestValidationError{
					field:  "AnnualFamilyIncome",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAnnualFamilyIncome()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLeadRequestValidationError{
				field:  "AnnualFamilyIncome",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if _, ok := _CreateLeadRequest_UserType_NotInLookup[m.GetUserType()]; ok {
		err := CreateLeadRequestValidationError{
			field:  "UserType",
			reason: "value must not be in list [MV_USER_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CreateLeadRequestMultiError(errors)
	}

	return nil
}

// CreateLeadRequestMultiError is an error wrapping multiple validation errors
// returned by CreateLeadRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateLeadRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateLeadRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateLeadRequestMultiError) AllErrors() []error { return m }

// CreateLeadRequestValidationError is the validation error returned by
// CreateLeadRequest.Validate if the designated constraints aren't met.
type CreateLeadRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateLeadRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateLeadRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateLeadRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateLeadRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateLeadRequestValidationError) ErrorName() string {
	return "CreateLeadRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateLeadRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateLeadRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateLeadRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateLeadRequestValidationError{}

var _CreateLeadRequest_EmploymentType_NotInLookup = map[EmploymentType]struct{}{
	0: {},
}

var _CreateLeadRequest_UserType_NotInLookup = map[MvUserType]struct{}{
	0: {},
}

// Validate checks the field values on CreateLeadResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateLeadResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateLeadResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateLeadResponseMultiError, or nil if none found.
func (m *CreateLeadResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateLeadResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLeadResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLeadResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLeadResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LeadId

	if len(errors) > 0 {
		return CreateLeadResponseMultiError(errors)
	}

	return nil
}

// CreateLeadResponseMultiError is an error wrapping multiple validation errors
// returned by CreateLeadResponse.ValidateAll() if the designated constraints
// aren't met.
type CreateLeadResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateLeadResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateLeadResponseMultiError) AllErrors() []error { return m }

// CreateLeadResponseValidationError is the validation error returned by
// CreateLeadResponse.Validate if the designated constraints aren't met.
type CreateLeadResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateLeadResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateLeadResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateLeadResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateLeadResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateLeadResponseValidationError) ErrorName() string {
	return "CreateLeadResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateLeadResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateLeadResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateLeadResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateLeadResponseValidationError{}

// Validate checks the field values on GetLeadStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLeadStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLeadStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLeadStatusRequestMultiError, or nil if none found.
func (m *GetLeadStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLeadStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLeadStatusRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLeadStatusRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLeadStatusRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LeadId

	if _, ok := _GetLeadStatusRequest_UserType_NotInLookup[m.GetUserType()]; ok {
		err := GetLeadStatusRequestValidationError{
			field:  "UserType",
			reason: "value must not be in list [MV_USER_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetLeadStatusRequestMultiError(errors)
	}

	return nil
}

// GetLeadStatusRequestMultiError is an error wrapping multiple validation
// errors returned by GetLeadStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type GetLeadStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLeadStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLeadStatusRequestMultiError) AllErrors() []error { return m }

// GetLeadStatusRequestValidationError is the validation error returned by
// GetLeadStatusRequest.Validate if the designated constraints aren't met.
type GetLeadStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLeadStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLeadStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLeadStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLeadStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLeadStatusRequestValidationError) ErrorName() string {
	return "GetLeadStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLeadStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLeadStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLeadStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLeadStatusRequestValidationError{}

var _GetLeadStatusRequest_UserType_NotInLookup = map[MvUserType]struct{}{
	0: {},
}

// Validate checks the field values on GetLeadStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLeadStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLeadStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLeadStatusResponseMultiError, or nil if none found.
func (m *GetLeadStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLeadStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLeadStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLeadStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLeadStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LeadStatus

	if all {
		switch v := interface{}(m.GetLoanAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLeadStatusResponseValidationError{
					field:  "LoanAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLeadStatusResponseValidationError{
					field:  "LoanAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLeadStatusResponseValidationError{
				field:  "LoanAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDisbursedAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLeadStatusResponseValidationError{
					field:  "DisbursedAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLeadStatusResponseValidationError{
					field:  "DisbursedAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisbursedAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLeadStatusResponseValidationError{
				field:  "DisbursedAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLeadStatusResponseMultiError(errors)
	}

	return nil
}

// GetLeadStatusResponseMultiError is an error wrapping multiple validation
// errors returned by GetLeadStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type GetLeadStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLeadStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLeadStatusResponseMultiError) AllErrors() []error { return m }

// GetLeadStatusResponseValidationError is the validation error returned by
// GetLeadStatusResponse.Validate if the designated constraints aren't met.
type GetLeadStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLeadStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLeadStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLeadStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLeadStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLeadStatusResponseValidationError) ErrorName() string {
	return "GetLeadStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLeadStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLeadStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLeadStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLeadStatusResponseValidationError{}

// Validate checks the field values on GetOffersRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetOffersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOffersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOffersRequestMultiError, or nil if none found.
func (m *GetOffersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOffersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOffersRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOffersRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOffersRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetLeadId()) < 1 {
		err := GetOffersRequestValidationError{
			field:  "LeadId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _GetOffersRequest_UserType_NotInLookup[m.GetUserType()]; ok {
		err := GetOffersRequestValidationError{
			field:  "UserType",
			reason: "value must not be in list [MV_USER_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetOffersRequestMultiError(errors)
	}

	return nil
}

// GetOffersRequestMultiError is an error wrapping multiple validation errors
// returned by GetOffersRequest.ValidateAll() if the designated constraints
// aren't met.
type GetOffersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOffersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOffersRequestMultiError) AllErrors() []error { return m }

// GetOffersRequestValidationError is the validation error returned by
// GetOffersRequest.Validate if the designated constraints aren't met.
type GetOffersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOffersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOffersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOffersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOffersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOffersRequestValidationError) ErrorName() string { return "GetOffersRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetOffersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOffersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOffersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOffersRequestValidationError{}

var _GetOffersRequest_UserType_NotInLookup = map[MvUserType]struct{}{
	0: {},
}

// Validate checks the field values on GetOffersResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetOffersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOffersResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOffersResponseMultiError, or nil if none found.
func (m *GetOffersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOffersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOffersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOffersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOffersResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetOffers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetOffersResponseValidationError{
						field:  fmt.Sprintf("Offers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetOffersResponseValidationError{
						field:  fmt.Sprintf("Offers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetOffersResponseValidationError{
					field:  fmt.Sprintf("Offers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetOffersResponseMultiError(errors)
	}

	return nil
}

// GetOffersResponseMultiError is an error wrapping multiple validation errors
// returned by GetOffersResponse.ValidateAll() if the designated constraints
// aren't met.
type GetOffersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOffersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOffersResponseMultiError) AllErrors() []error { return m }

// GetOffersResponseValidationError is the validation error returned by
// GetOffersResponse.Validate if the designated constraints aren't met.
type GetOffersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOffersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOffersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOffersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOffersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOffersResponseValidationError) ErrorName() string {
	return "GetOffersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetOffersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOffersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOffersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOffersResponseValidationError{}

// Validate checks the field values on GetPWAJourneyUrlRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPWAJourneyUrlRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPWAJourneyUrlRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPWAJourneyUrlRequestMultiError, or nil if none found.
func (m *GetPWAJourneyUrlRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPWAJourneyUrlRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPWAJourneyUrlRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPWAJourneyUrlRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPWAJourneyUrlRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetLeadId()) < 1 {
		err := GetPWAJourneyUrlRequestValidationError{
			field:  "LeadId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _GetPWAJourneyUrlRequest_UserType_NotInLookup[m.GetUserType()]; ok {
		err := GetPWAJourneyUrlRequestValidationError{
			field:  "UserType",
			reason: "value must not be in list [MV_USER_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetPWAJourneyUrlRequestMultiError(errors)
	}

	return nil
}

// GetPWAJourneyUrlRequestMultiError is an error wrapping multiple validation
// errors returned by GetPWAJourneyUrlRequest.ValidateAll() if the designated
// constraints aren't met.
type GetPWAJourneyUrlRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPWAJourneyUrlRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPWAJourneyUrlRequestMultiError) AllErrors() []error { return m }

// GetPWAJourneyUrlRequestValidationError is the validation error returned by
// GetPWAJourneyUrlRequest.Validate if the designated constraints aren't met.
type GetPWAJourneyUrlRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPWAJourneyUrlRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPWAJourneyUrlRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPWAJourneyUrlRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPWAJourneyUrlRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPWAJourneyUrlRequestValidationError) ErrorName() string {
	return "GetPWAJourneyUrlRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPWAJourneyUrlRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPWAJourneyUrlRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPWAJourneyUrlRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPWAJourneyUrlRequestValidationError{}

var _GetPWAJourneyUrlRequest_UserType_NotInLookup = map[MvUserType]struct{}{
	0: {},
}

// Validate checks the field values on GetPWAJourneyUrlResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPWAJourneyUrlResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPWAJourneyUrlResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPWAJourneyUrlResponseMultiError, or nil if none found.
func (m *GetPWAJourneyUrlResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPWAJourneyUrlResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPWAJourneyUrlResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPWAJourneyUrlResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPWAJourneyUrlResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SsoUrl

	if len(errors) > 0 {
		return GetPWAJourneyUrlResponseMultiError(errors)
	}

	return nil
}

// GetPWAJourneyUrlResponseMultiError is an error wrapping multiple validation
// errors returned by GetPWAJourneyUrlResponse.ValidateAll() if the designated
// constraints aren't met.
type GetPWAJourneyUrlResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPWAJourneyUrlResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPWAJourneyUrlResponseMultiError) AllErrors() []error { return m }

// GetPWAJourneyUrlResponseValidationError is the validation error returned by
// GetPWAJourneyUrlResponse.Validate if the designated constraints aren't met.
type GetPWAJourneyUrlResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPWAJourneyUrlResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPWAJourneyUrlResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPWAJourneyUrlResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPWAJourneyUrlResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPWAJourneyUrlResponseValidationError) ErrorName() string {
	return "GetPWAJourneyUrlResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPWAJourneyUrlResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPWAJourneyUrlResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPWAJourneyUrlResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPWAJourneyUrlResponseValidationError{}

// Validate checks the field values on GetSingleDeDupeStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSingleDeDupeStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSingleDeDupeStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSingleDeDupeStatusRequestMultiError, or nil if none found.
func (m *GetSingleDeDupeStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSingleDeDupeStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSingleDeDupeStatusRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSingleDeDupeStatusRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSingleDeDupeStatusRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetPanNumber()) < 1 {
		err := GetSingleDeDupeStatusRequestValidationError{
			field:  "PanNumber",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetEmail()) < 1 {
		err := GetSingleDeDupeStatusRequestValidationError{
			field:  "Email",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetMobileNumber()) < 1 {
		err := GetSingleDeDupeStatusRequestValidationError{
			field:  "MobileNumber",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _GetSingleDeDupeStatusRequest_UserType_NotInLookup[m.GetUserType()]; ok {
		err := GetSingleDeDupeStatusRequestValidationError{
			field:  "UserType",
			reason: "value must not be in list [MV_USER_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetSingleDeDupeStatusRequestMultiError(errors)
	}

	return nil
}

// GetSingleDeDupeStatusRequestMultiError is an error wrapping multiple
// validation errors returned by GetSingleDeDupeStatusRequest.ValidateAll() if
// the designated constraints aren't met.
type GetSingleDeDupeStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSingleDeDupeStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSingleDeDupeStatusRequestMultiError) AllErrors() []error { return m }

// GetSingleDeDupeStatusRequestValidationError is the validation error returned
// by GetSingleDeDupeStatusRequest.Validate if the designated constraints
// aren't met.
type GetSingleDeDupeStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSingleDeDupeStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSingleDeDupeStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSingleDeDupeStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSingleDeDupeStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSingleDeDupeStatusRequestValidationError) ErrorName() string {
	return "GetSingleDeDupeStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSingleDeDupeStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSingleDeDupeStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSingleDeDupeStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSingleDeDupeStatusRequestValidationError{}

var _GetSingleDeDupeStatusRequest_UserType_NotInLookup = map[MvUserType]struct{}{
	0: {},
}

// Validate checks the field values on GetSingleDeDupeStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSingleDeDupeStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSingleDeDupeStatusResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetSingleDeDupeStatusResponseMultiError, or nil if none found.
func (m *GetSingleDeDupeStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSingleDeDupeStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSingleDeDupeStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSingleDeDupeStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSingleDeDupeStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DedupeStatus

	if len(errors) > 0 {
		return GetSingleDeDupeStatusResponseMultiError(errors)
	}

	return nil
}

// GetSingleDeDupeStatusResponseMultiError is an error wrapping multiple
// validation errors returned by GetSingleDeDupeStatusResponse.ValidateAll()
// if the designated constraints aren't met.
type GetSingleDeDupeStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSingleDeDupeStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSingleDeDupeStatusResponseMultiError) AllErrors() []error { return m }

// GetSingleDeDupeStatusResponseValidationError is the validation error
// returned by GetSingleDeDupeStatusResponse.Validate if the designated
// constraints aren't met.
type GetSingleDeDupeStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSingleDeDupeStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSingleDeDupeStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSingleDeDupeStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSingleDeDupeStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSingleDeDupeStatusResponseValidationError) ErrorName() string {
	return "GetSingleDeDupeStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSingleDeDupeStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSingleDeDupeStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSingleDeDupeStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSingleDeDupeStatusResponseValidationError{}

// Validate checks the field values on GenerateMvBlackBoxOfferRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateMvBlackBoxOfferRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateMvBlackBoxOfferRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GenerateMvBlackBoxOfferRequestMultiError, or nil if none found.
func (m *GenerateMvBlackBoxOfferRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateMvBlackBoxOfferRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateMvBlackBoxOfferRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateMvBlackBoxOfferRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateMvBlackBoxOfferRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LeadUserRef

	// no validation rules for LeadSource

	// no validation rules for DataFormat

	// no validation rules for Pincode

	// no validation rules for EmploymentType

	if all {
		switch v := interface{}(m.GetDeclaredIncome()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateMvBlackBoxOfferRequestValidationError{
					field:  "DeclaredIncome",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateMvBlackBoxOfferRequestValidationError{
					field:  "DeclaredIncome",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeclaredIncome()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateMvBlackBoxOfferRequestValidationError{
				field:  "DeclaredIncome",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateMvBlackBoxOfferRequestValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateMvBlackBoxOfferRequestValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateMvBlackBoxOfferRequestValidationError{
				field:  "Dob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BureauProvider

	if all {
		switch v := interface{}(m.GetPkgInfoData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateMvBlackBoxOfferRequestValidationError{
					field:  "PkgInfoData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateMvBlackBoxOfferRequestValidationError{
					field:  "PkgInfoData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPkgInfoData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateMvBlackBoxOfferRequestValidationError{
				field:  "PkgInfoData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSmsData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateMvBlackBoxOfferRequestValidationError{
					field:  "SmsData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateMvBlackBoxOfferRequestValidationError{
					field:  "SmsData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSmsData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateMvBlackBoxOfferRequestValidationError{
				field:  "SmsData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RawBureauReport

	if len(errors) > 0 {
		return GenerateMvBlackBoxOfferRequestMultiError(errors)
	}

	return nil
}

// GenerateMvBlackBoxOfferRequestMultiError is an error wrapping multiple
// validation errors returned by GenerateMvBlackBoxOfferRequest.ValidateAll()
// if the designated constraints aren't met.
type GenerateMvBlackBoxOfferRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateMvBlackBoxOfferRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateMvBlackBoxOfferRequestMultiError) AllErrors() []error { return m }

// GenerateMvBlackBoxOfferRequestValidationError is the validation error
// returned by GenerateMvBlackBoxOfferRequest.Validate if the designated
// constraints aren't met.
type GenerateMvBlackBoxOfferRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateMvBlackBoxOfferRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateMvBlackBoxOfferRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateMvBlackBoxOfferRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateMvBlackBoxOfferRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateMvBlackBoxOfferRequestValidationError) ErrorName() string {
	return "GenerateMvBlackBoxOfferRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateMvBlackBoxOfferRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateMvBlackBoxOfferRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateMvBlackBoxOfferRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateMvBlackBoxOfferRequestValidationError{}

// Validate checks the field values on PkgInfoData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PkgInfoData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PkgInfoData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PkgInfoDataMultiError, or
// nil if none found.
func (m *PkgInfoData) ValidateAll() error {
	return m.validate(true)
}

func (m *PkgInfoData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetPkgInfoDtoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PkgInfoDataValidationError{
						field:  fmt.Sprintf("PkgInfoDtoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PkgInfoDataValidationError{
						field:  fmt.Sprintf("PkgInfoDtoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PkgInfoDataValidationError{
					field:  fmt.Sprintf("PkgInfoDtoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PkgInfoDataMultiError(errors)
	}

	return nil
}

// PkgInfoDataMultiError is an error wrapping multiple validation errors
// returned by PkgInfoData.ValidateAll() if the designated constraints aren't met.
type PkgInfoDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PkgInfoDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PkgInfoDataMultiError) AllErrors() []error { return m }

// PkgInfoDataValidationError is the validation error returned by
// PkgInfoData.Validate if the designated constraints aren't met.
type PkgInfoDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PkgInfoDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PkgInfoDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PkgInfoDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PkgInfoDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PkgInfoDataValidationError) ErrorName() string { return "PkgInfoDataValidationError" }

// Error satisfies the builtin error interface
func (e PkgInfoDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPkgInfoData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PkgInfoDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PkgInfoDataValidationError{}

// Validate checks the field values on PkgInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PkgInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PkgInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in PkgInfoMultiError, or nil if none found.
func (m *PkgInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *PkgInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PkgName

	// no validation rules for UserId

	if all {
		switch v := interface{}(m.GetDateCreated()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PkgInfoValidationError{
					field:  "DateCreated",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PkgInfoValidationError{
					field:  "DateCreated",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDateCreated()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PkgInfoValidationError{
				field:  "DateCreated",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PkgInfoMultiError(errors)
	}

	return nil
}

// PkgInfoMultiError is an error wrapping multiple validation errors returned
// by PkgInfo.ValidateAll() if the designated constraints aren't met.
type PkgInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PkgInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PkgInfoMultiError) AllErrors() []error { return m }

// PkgInfoValidationError is the validation error returned by PkgInfo.Validate
// if the designated constraints aren't met.
type PkgInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PkgInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PkgInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PkgInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PkgInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PkgInfoValidationError) ErrorName() string { return "PkgInfoValidationError" }

// Error satisfies the builtin error interface
func (e PkgInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPkgInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PkgInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PkgInfoValidationError{}

// Validate checks the field values on SmsData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SmsData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SmsData with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in SmsDataMultiError, or nil if none found.
func (m *SmsData) ValidateAll() error {
	return m.validate(true)
}

func (m *SmsData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetSmsList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SmsDataValidationError{
						field:  fmt.Sprintf("SmsList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SmsDataValidationError{
						field:  fmt.Sprintf("SmsList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SmsDataValidationError{
					field:  fmt.Sprintf("SmsList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SmsDataMultiError(errors)
	}

	return nil
}

// SmsDataMultiError is an error wrapping multiple validation errors returned
// by SmsData.ValidateAll() if the designated constraints aren't met.
type SmsDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SmsDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SmsDataMultiError) AllErrors() []error { return m }

// SmsDataValidationError is the validation error returned by SmsData.Validate
// if the designated constraints aren't met.
type SmsDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SmsDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SmsDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SmsDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SmsDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SmsDataValidationError) ErrorName() string { return "SmsDataValidationError" }

// Error satisfies the builtin error interface
func (e SmsDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSmsData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SmsDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SmsDataValidationError{}

// Validate checks the field values on Sms with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Sms) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Sms with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in SmsMultiError, or nil if none found.
func (m *Sms) ValidateAll() error {
	return m.validate(true)
}

func (m *Sms) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Msg

	// no validation rules for Address

	if all {
		switch v := interface{}(m.GetDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SmsValidationError{
					field:  "Date",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SmsValidationError{
					field:  "Date",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SmsValidationError{
				field:  "Date",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SmsMultiError(errors)
	}

	return nil
}

// SmsMultiError is an error wrapping multiple validation errors returned by
// Sms.ValidateAll() if the designated constraints aren't met.
type SmsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SmsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SmsMultiError) AllErrors() []error { return m }

// SmsValidationError is the validation error returned by Sms.Validate if the
// designated constraints aren't met.
type SmsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SmsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SmsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SmsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SmsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SmsValidationError) ErrorName() string { return "SmsValidationError" }

// Error satisfies the builtin error interface
func (e SmsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSms.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SmsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SmsValidationError{}

// Validate checks the field values on GenerateMvBlackBoxOfferResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateMvBlackBoxOfferResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateMvBlackBoxOfferResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GenerateMvBlackBoxOfferResponseMultiError, or nil if none found.
func (m *GenerateMvBlackBoxOfferResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateMvBlackBoxOfferResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetOfferDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateMvBlackBoxOfferResponseValidationError{
					field:  "OfferDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateMvBlackBoxOfferResponseValidationError{
					field:  "OfferDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOfferDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateMvBlackBoxOfferResponseValidationError{
				field:  "OfferDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateMvBlackBoxOfferResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateMvBlackBoxOfferResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateMvBlackBoxOfferResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GenerateMvBlackBoxOfferResponseMultiError(errors)
	}

	return nil
}

// GenerateMvBlackBoxOfferResponseMultiError is an error wrapping multiple
// validation errors returned by GenerateMvBlackBoxOfferResponse.ValidateAll()
// if the designated constraints aren't met.
type GenerateMvBlackBoxOfferResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateMvBlackBoxOfferResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateMvBlackBoxOfferResponseMultiError) AllErrors() []error { return m }

// GenerateMvBlackBoxOfferResponseValidationError is the validation error
// returned by GenerateMvBlackBoxOfferResponse.Validate if the designated
// constraints aren't met.
type GenerateMvBlackBoxOfferResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateMvBlackBoxOfferResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateMvBlackBoxOfferResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateMvBlackBoxOfferResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateMvBlackBoxOfferResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateMvBlackBoxOfferResponseValidationError) ErrorName() string {
	return "GenerateMvBlackBoxOfferResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateMvBlackBoxOfferResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateMvBlackBoxOfferResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateMvBlackBoxOfferResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateMvBlackBoxOfferResponseValidationError{}

// Validate checks the field values on OfferDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *OfferDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OfferDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in OfferDetailsMultiError, or
// nil if none found.
func (m *OfferDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *OfferDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LeadUserRef

	// no validation rules for Status

	// no validation rules for BestOfferAmount

	// no validation rules for BestOfferTenure

	// no validation rules for BestOfferRoi

	// no validation rules for ExpiryDate

	// no validation rules for Category

	// no validation rules for LeadSource

	// no validation rules for MetaIncome

	// no validation rules for MetaTagName

	// no validation rules for EmploymentType

	// no validation rules for SummaryVariables

	if len(errors) > 0 {
		return OfferDetailsMultiError(errors)
	}

	return nil
}

// OfferDetailsMultiError is an error wrapping multiple validation errors
// returned by OfferDetails.ValidateAll() if the designated constraints aren't met.
type OfferDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OfferDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OfferDetailsMultiError) AllErrors() []error { return m }

// OfferDetailsValidationError is the validation error returned by
// OfferDetails.Validate if the designated constraints aren't met.
type OfferDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OfferDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OfferDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OfferDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OfferDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OfferDetailsValidationError) ErrorName() string { return "OfferDetailsValidationError" }

// Error satisfies the builtin error interface
func (e OfferDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOfferDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OfferDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OfferDetailsValidationError{}

// Validate checks the field values on SaveMvBlackBoxOfferRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SaveMvBlackBoxOfferRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SaveMvBlackBoxOfferRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SaveMvBlackBoxOfferRequestMultiError, or nil if none found.
func (m *SaveMvBlackBoxOfferRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SaveMvBlackBoxOfferRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SaveMvBlackBoxOfferRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SaveMvBlackBoxOfferRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SaveMvBlackBoxOfferRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LeadUserRef

	// no validation rules for Status

	// no validation rules for BestOfferAmount

	// no validation rules for BestOfferTenure

	// no validation rules for BestOfferRoi

	// no validation rules for ExpiryDate

	// no validation rules for Category

	// no validation rules for LeadSource

	// no validation rules for MetaIncome

	// no validation rules for MetaTagName

	// no validation rules for EmploymentType

	// no validation rules for SummaryVariables

	if len(errors) > 0 {
		return SaveMvBlackBoxOfferRequestMultiError(errors)
	}

	return nil
}

// SaveMvBlackBoxOfferRequestMultiError is an error wrapping multiple
// validation errors returned by SaveMvBlackBoxOfferRequest.ValidateAll() if
// the designated constraints aren't met.
type SaveMvBlackBoxOfferRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SaveMvBlackBoxOfferRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SaveMvBlackBoxOfferRequestMultiError) AllErrors() []error { return m }

// SaveMvBlackBoxOfferRequestValidationError is the validation error returned
// by SaveMvBlackBoxOfferRequest.Validate if the designated constraints aren't met.
type SaveMvBlackBoxOfferRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SaveMvBlackBoxOfferRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SaveMvBlackBoxOfferRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SaveMvBlackBoxOfferRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SaveMvBlackBoxOfferRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SaveMvBlackBoxOfferRequestValidationError) ErrorName() string {
	return "SaveMvBlackBoxOfferRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SaveMvBlackBoxOfferRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSaveMvBlackBoxOfferRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SaveMvBlackBoxOfferRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SaveMvBlackBoxOfferRequestValidationError{}

// Validate checks the field values on SaveMvBlackBoxOfferResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SaveMvBlackBoxOfferResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SaveMvBlackBoxOfferResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SaveMvBlackBoxOfferResponseMultiError, or nil if none found.
func (m *SaveMvBlackBoxOfferResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SaveMvBlackBoxOfferResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SaveMvBlackBoxOfferResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SaveMvBlackBoxOfferResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SaveMvBlackBoxOfferResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Code

	// no validation rules for Message

	// no validation rules for VendorStatus

	if len(errors) > 0 {
		return SaveMvBlackBoxOfferResponseMultiError(errors)
	}

	return nil
}

// SaveMvBlackBoxOfferResponseMultiError is an error wrapping multiple
// validation errors returned by SaveMvBlackBoxOfferResponse.ValidateAll() if
// the designated constraints aren't met.
type SaveMvBlackBoxOfferResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SaveMvBlackBoxOfferResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SaveMvBlackBoxOfferResponseMultiError) AllErrors() []error { return m }

// SaveMvBlackBoxOfferResponseValidationError is the validation error returned
// by SaveMvBlackBoxOfferResponse.Validate if the designated constraints
// aren't met.
type SaveMvBlackBoxOfferResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SaveMvBlackBoxOfferResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SaveMvBlackBoxOfferResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SaveMvBlackBoxOfferResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SaveMvBlackBoxOfferResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SaveMvBlackBoxOfferResponseValidationError) ErrorName() string {
	return "SaveMvBlackBoxOfferResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SaveMvBlackBoxOfferResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSaveMvBlackBoxOfferResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SaveMvBlackBoxOfferResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SaveMvBlackBoxOfferResponseValidationError{}

// Validate checks the field values on GetDeDupeStatusResponse_PanDeDupeStatus
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetDeDupeStatusResponse_PanDeDupeStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetDeDupeStatusResponse_PanDeDupeStatus with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetDeDupeStatusResponse_PanDeDupeStatusMultiError, or nil if none found.
func (m *GetDeDupeStatusResponse_PanDeDupeStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDeDupeStatusResponse_PanDeDupeStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Pan

	// no validation rules for Status

	if len(errors) > 0 {
		return GetDeDupeStatusResponse_PanDeDupeStatusMultiError(errors)
	}

	return nil
}

// GetDeDupeStatusResponse_PanDeDupeStatusMultiError is an error wrapping
// multiple validation errors returned by
// GetDeDupeStatusResponse_PanDeDupeStatus.ValidateAll() if the designated
// constraints aren't met.
type GetDeDupeStatusResponse_PanDeDupeStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDeDupeStatusResponse_PanDeDupeStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDeDupeStatusResponse_PanDeDupeStatusMultiError) AllErrors() []error { return m }

// GetDeDupeStatusResponse_PanDeDupeStatusValidationError is the validation
// error returned by GetDeDupeStatusResponse_PanDeDupeStatus.Validate if the
// designated constraints aren't met.
type GetDeDupeStatusResponse_PanDeDupeStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDeDupeStatusResponse_PanDeDupeStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDeDupeStatusResponse_PanDeDupeStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDeDupeStatusResponse_PanDeDupeStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDeDupeStatusResponse_PanDeDupeStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDeDupeStatusResponse_PanDeDupeStatusValidationError) ErrorName() string {
	return "GetDeDupeStatusResponse_PanDeDupeStatusValidationError"
}

// Error satisfies the builtin error interface
func (e GetDeDupeStatusResponse_PanDeDupeStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDeDupeStatusResponse_PanDeDupeStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDeDupeStatusResponse_PanDeDupeStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDeDupeStatusResponse_PanDeDupeStatusValidationError{}

// Validate checks the field values on CreateLeadRequest_AddressWithType with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateLeadRequest_AddressWithType) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateLeadRequest_AddressWithType
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreateLeadRequest_AddressWithTypeMultiError, or nil if none found.
func (m *CreateLeadRequest_AddressWithType) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateLeadRequest_AddressWithType) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _CreateLeadRequest_AddressWithType_Type_NotInLookup[m.GetType()]; ok {
		err := CreateLeadRequest_AddressWithTypeValidationError{
			field:  "Type",
			reason: "value must not be in list [ADDRESS_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetAddress() == nil {
		err := CreateLeadRequest_AddressWithTypeValidationError{
			field:  "Address",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLeadRequest_AddressWithTypeValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLeadRequest_AddressWithTypeValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLeadRequest_AddressWithTypeValidationError{
				field:  "Address",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateLeadRequest_AddressWithTypeMultiError(errors)
	}

	return nil
}

// CreateLeadRequest_AddressWithTypeMultiError is an error wrapping multiple
// validation errors returned by
// CreateLeadRequest_AddressWithType.ValidateAll() if the designated
// constraints aren't met.
type CreateLeadRequest_AddressWithTypeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateLeadRequest_AddressWithTypeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateLeadRequest_AddressWithTypeMultiError) AllErrors() []error { return m }

// CreateLeadRequest_AddressWithTypeValidationError is the validation error
// returned by CreateLeadRequest_AddressWithType.Validate if the designated
// constraints aren't met.
type CreateLeadRequest_AddressWithTypeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateLeadRequest_AddressWithTypeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateLeadRequest_AddressWithTypeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateLeadRequest_AddressWithTypeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateLeadRequest_AddressWithTypeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateLeadRequest_AddressWithTypeValidationError) ErrorName() string {
	return "CreateLeadRequest_AddressWithTypeValidationError"
}

// Error satisfies the builtin error interface
func (e CreateLeadRequest_AddressWithTypeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateLeadRequest_AddressWithType.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateLeadRequest_AddressWithTypeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateLeadRequest_AddressWithTypeValidationError{}

var _CreateLeadRequest_AddressWithType_Type_NotInLookup = map[AddressType]struct{}{
	0: {},
}
