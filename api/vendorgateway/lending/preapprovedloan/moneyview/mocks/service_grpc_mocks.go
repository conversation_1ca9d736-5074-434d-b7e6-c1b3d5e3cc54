// Code generated by MockGen. DO NOT EDIT.
// Source: api/vendorgateway/lending/preapprovedloan/moneyview/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	moneyview "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/moneyview"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockMoneyviewClient is a mock of MoneyviewClient interface.
type MockMoneyviewClient struct {
	ctrl     *gomock.Controller
	recorder *MockMoneyviewClientMockRecorder
}

// MockMoneyviewClientMockRecorder is the mock recorder for MockMoneyviewClient.
type MockMoneyviewClientMockRecorder struct {
	mock *MockMoneyviewClient
}

// NewMockMoneyviewClient creates a new mock instance.
func NewMockMoneyviewClient(ctrl *gomock.Controller) *MockMoneyviewClient {
	mock := &MockMoneyviewClient{ctrl: ctrl}
	mock.recorder = &MockMoneyviewClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMoneyviewClient) EXPECT() *MockMoneyviewClientMockRecorder {
	return m.recorder
}

// CreateLead mocks base method.
func (m *MockMoneyviewClient) CreateLead(ctx context.Context, in *moneyview.CreateLeadRequest, opts ...grpc.CallOption) (*moneyview.CreateLeadResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateLead", varargs...)
	ret0, _ := ret[0].(*moneyview.CreateLeadResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateLead indicates an expected call of CreateLead.
func (mr *MockMoneyviewClientMockRecorder) CreateLead(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateLead", reflect.TypeOf((*MockMoneyviewClient)(nil).CreateLead), varargs...)
}

// GenerateMvBlackBoxOffer mocks base method.
func (m *MockMoneyviewClient) GenerateMvBlackBoxOffer(ctx context.Context, in *moneyview.GenerateMvBlackBoxOfferRequest, opts ...grpc.CallOption) (*moneyview.GenerateMvBlackBoxOfferResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateMvBlackBoxOffer", varargs...)
	ret0, _ := ret[0].(*moneyview.GenerateMvBlackBoxOfferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateMvBlackBoxOffer indicates an expected call of GenerateMvBlackBoxOffer.
func (mr *MockMoneyviewClientMockRecorder) GenerateMvBlackBoxOffer(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateMvBlackBoxOffer", reflect.TypeOf((*MockMoneyviewClient)(nil).GenerateMvBlackBoxOffer), varargs...)
}

// GetDeDupeStatus mocks base method.
func (m *MockMoneyviewClient) GetDeDupeStatus(ctx context.Context, in *moneyview.GetDeDupeStatusRequest, opts ...grpc.CallOption) (*moneyview.GetDeDupeStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetDeDupeStatus", varargs...)
	ret0, _ := ret[0].(*moneyview.GetDeDupeStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDeDupeStatus indicates an expected call of GetDeDupeStatus.
func (mr *MockMoneyviewClientMockRecorder) GetDeDupeStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDeDupeStatus", reflect.TypeOf((*MockMoneyviewClient)(nil).GetDeDupeStatus), varargs...)
}

// GetLeadStatus mocks base method.
func (m *MockMoneyviewClient) GetLeadStatus(ctx context.Context, in *moneyview.GetLeadStatusRequest, opts ...grpc.CallOption) (*moneyview.GetLeadStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLeadStatus", varargs...)
	ret0, _ := ret[0].(*moneyview.GetLeadStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLeadStatus indicates an expected call of GetLeadStatus.
func (mr *MockMoneyviewClientMockRecorder) GetLeadStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLeadStatus", reflect.TypeOf((*MockMoneyviewClient)(nil).GetLeadStatus), varargs...)
}

// GetOffers mocks base method.
func (m *MockMoneyviewClient) GetOffers(ctx context.Context, in *moneyview.GetOffersRequest, opts ...grpc.CallOption) (*moneyview.GetOffersResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetOffers", varargs...)
	ret0, _ := ret[0].(*moneyview.GetOffersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOffers indicates an expected call of GetOffers.
func (mr *MockMoneyviewClientMockRecorder) GetOffers(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOffers", reflect.TypeOf((*MockMoneyviewClient)(nil).GetOffers), varargs...)
}

// GetPWAJourneyUrl mocks base method.
func (m *MockMoneyviewClient) GetPWAJourneyUrl(ctx context.Context, in *moneyview.GetPWAJourneyUrlRequest, opts ...grpc.CallOption) (*moneyview.GetPWAJourneyUrlResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPWAJourneyUrl", varargs...)
	ret0, _ := ret[0].(*moneyview.GetPWAJourneyUrlResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPWAJourneyUrl indicates an expected call of GetPWAJourneyUrl.
func (mr *MockMoneyviewClientMockRecorder) GetPWAJourneyUrl(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPWAJourneyUrl", reflect.TypeOf((*MockMoneyviewClient)(nil).GetPWAJourneyUrl), varargs...)
}

// GetSingleDeDupeStatus mocks base method.
func (m *MockMoneyviewClient) GetSingleDeDupeStatus(ctx context.Context, in *moneyview.GetSingleDeDupeStatusRequest, opts ...grpc.CallOption) (*moneyview.GetSingleDeDupeStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSingleDeDupeStatus", varargs...)
	ret0, _ := ret[0].(*moneyview.GetSingleDeDupeStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSingleDeDupeStatus indicates an expected call of GetSingleDeDupeStatus.
func (mr *MockMoneyviewClientMockRecorder) GetSingleDeDupeStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSingleDeDupeStatus", reflect.TypeOf((*MockMoneyviewClient)(nil).GetSingleDeDupeStatus), varargs...)
}

// SaveMvBlackBoxOffer mocks base method.
func (m *MockMoneyviewClient) SaveMvBlackBoxOffer(ctx context.Context, in *moneyview.SaveMvBlackBoxOfferRequest, opts ...grpc.CallOption) (*moneyview.SaveMvBlackBoxOfferResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SaveMvBlackBoxOffer", varargs...)
	ret0, _ := ret[0].(*moneyview.SaveMvBlackBoxOfferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SaveMvBlackBoxOffer indicates an expected call of SaveMvBlackBoxOffer.
func (mr *MockMoneyviewClientMockRecorder) SaveMvBlackBoxOffer(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveMvBlackBoxOffer", reflect.TypeOf((*MockMoneyviewClient)(nil).SaveMvBlackBoxOffer), varargs...)
}

// MockMoneyviewServer is a mock of MoneyviewServer interface.
type MockMoneyviewServer struct {
	ctrl     *gomock.Controller
	recorder *MockMoneyviewServerMockRecorder
}

// MockMoneyviewServerMockRecorder is the mock recorder for MockMoneyviewServer.
type MockMoneyviewServerMockRecorder struct {
	mock *MockMoneyviewServer
}

// NewMockMoneyviewServer creates a new mock instance.
func NewMockMoneyviewServer(ctrl *gomock.Controller) *MockMoneyviewServer {
	mock := &MockMoneyviewServer{ctrl: ctrl}
	mock.recorder = &MockMoneyviewServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMoneyviewServer) EXPECT() *MockMoneyviewServerMockRecorder {
	return m.recorder
}

// CreateLead mocks base method.
func (m *MockMoneyviewServer) CreateLead(arg0 context.Context, arg1 *moneyview.CreateLeadRequest) (*moneyview.CreateLeadResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateLead", arg0, arg1)
	ret0, _ := ret[0].(*moneyview.CreateLeadResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateLead indicates an expected call of CreateLead.
func (mr *MockMoneyviewServerMockRecorder) CreateLead(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateLead", reflect.TypeOf((*MockMoneyviewServer)(nil).CreateLead), arg0, arg1)
}

// GenerateMvBlackBoxOffer mocks base method.
func (m *MockMoneyviewServer) GenerateMvBlackBoxOffer(arg0 context.Context, arg1 *moneyview.GenerateMvBlackBoxOfferRequest) (*moneyview.GenerateMvBlackBoxOfferResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateMvBlackBoxOffer", arg0, arg1)
	ret0, _ := ret[0].(*moneyview.GenerateMvBlackBoxOfferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateMvBlackBoxOffer indicates an expected call of GenerateMvBlackBoxOffer.
func (mr *MockMoneyviewServerMockRecorder) GenerateMvBlackBoxOffer(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateMvBlackBoxOffer", reflect.TypeOf((*MockMoneyviewServer)(nil).GenerateMvBlackBoxOffer), arg0, arg1)
}

// GetDeDupeStatus mocks base method.
func (m *MockMoneyviewServer) GetDeDupeStatus(arg0 context.Context, arg1 *moneyview.GetDeDupeStatusRequest) (*moneyview.GetDeDupeStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDeDupeStatus", arg0, arg1)
	ret0, _ := ret[0].(*moneyview.GetDeDupeStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDeDupeStatus indicates an expected call of GetDeDupeStatus.
func (mr *MockMoneyviewServerMockRecorder) GetDeDupeStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDeDupeStatus", reflect.TypeOf((*MockMoneyviewServer)(nil).GetDeDupeStatus), arg0, arg1)
}

// GetLeadStatus mocks base method.
func (m *MockMoneyviewServer) GetLeadStatus(arg0 context.Context, arg1 *moneyview.GetLeadStatusRequest) (*moneyview.GetLeadStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLeadStatus", arg0, arg1)
	ret0, _ := ret[0].(*moneyview.GetLeadStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLeadStatus indicates an expected call of GetLeadStatus.
func (mr *MockMoneyviewServerMockRecorder) GetLeadStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLeadStatus", reflect.TypeOf((*MockMoneyviewServer)(nil).GetLeadStatus), arg0, arg1)
}

// GetOffers mocks base method.
func (m *MockMoneyviewServer) GetOffers(arg0 context.Context, arg1 *moneyview.GetOffersRequest) (*moneyview.GetOffersResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOffers", arg0, arg1)
	ret0, _ := ret[0].(*moneyview.GetOffersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOffers indicates an expected call of GetOffers.
func (mr *MockMoneyviewServerMockRecorder) GetOffers(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOffers", reflect.TypeOf((*MockMoneyviewServer)(nil).GetOffers), arg0, arg1)
}

// GetPWAJourneyUrl mocks base method.
func (m *MockMoneyviewServer) GetPWAJourneyUrl(arg0 context.Context, arg1 *moneyview.GetPWAJourneyUrlRequest) (*moneyview.GetPWAJourneyUrlResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPWAJourneyUrl", arg0, arg1)
	ret0, _ := ret[0].(*moneyview.GetPWAJourneyUrlResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPWAJourneyUrl indicates an expected call of GetPWAJourneyUrl.
func (mr *MockMoneyviewServerMockRecorder) GetPWAJourneyUrl(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPWAJourneyUrl", reflect.TypeOf((*MockMoneyviewServer)(nil).GetPWAJourneyUrl), arg0, arg1)
}

// GetSingleDeDupeStatus mocks base method.
func (m *MockMoneyviewServer) GetSingleDeDupeStatus(arg0 context.Context, arg1 *moneyview.GetSingleDeDupeStatusRequest) (*moneyview.GetSingleDeDupeStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSingleDeDupeStatus", arg0, arg1)
	ret0, _ := ret[0].(*moneyview.GetSingleDeDupeStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSingleDeDupeStatus indicates an expected call of GetSingleDeDupeStatus.
func (mr *MockMoneyviewServerMockRecorder) GetSingleDeDupeStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSingleDeDupeStatus", reflect.TypeOf((*MockMoneyviewServer)(nil).GetSingleDeDupeStatus), arg0, arg1)
}

// SaveMvBlackBoxOffer mocks base method.
func (m *MockMoneyviewServer) SaveMvBlackBoxOffer(arg0 context.Context, arg1 *moneyview.SaveMvBlackBoxOfferRequest) (*moneyview.SaveMvBlackBoxOfferResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveMvBlackBoxOffer", arg0, arg1)
	ret0, _ := ret[0].(*moneyview.SaveMvBlackBoxOfferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SaveMvBlackBoxOffer indicates an expected call of SaveMvBlackBoxOffer.
func (mr *MockMoneyviewServerMockRecorder) SaveMvBlackBoxOffer(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveMvBlackBoxOffer", reflect.TypeOf((*MockMoneyviewServer)(nil).SaveMvBlackBoxOffer), arg0, arg1)
}

// MockUnsafeMoneyviewServer is a mock of UnsafeMoneyviewServer interface.
type MockUnsafeMoneyviewServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeMoneyviewServerMockRecorder
}

// MockUnsafeMoneyviewServerMockRecorder is the mock recorder for MockUnsafeMoneyviewServer.
type MockUnsafeMoneyviewServerMockRecorder struct {
	mock *MockUnsafeMoneyviewServer
}

// NewMockUnsafeMoneyviewServer creates a new mock instance.
func NewMockUnsafeMoneyviewServer(ctrl *gomock.Controller) *MockUnsafeMoneyviewServer {
	mock := &MockUnsafeMoneyviewServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeMoneyviewServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeMoneyviewServer) EXPECT() *MockUnsafeMoneyviewServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedMoneyviewServer mocks base method.
func (m *MockUnsafeMoneyviewServer) mustEmbedUnimplementedMoneyviewServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedMoneyviewServer")
}

// mustEmbedUnimplementedMoneyviewServer indicates an expected call of mustEmbedUnimplementedMoneyviewServer.
func (mr *MockUnsafeMoneyviewServerMockRecorder) mustEmbedUnimplementedMoneyviewServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedMoneyviewServer", reflect.TypeOf((*MockUnsafeMoneyviewServer)(nil).mustEmbedUnimplementedMoneyviewServer))
}
