package errors

import "errors"

var (
	FieldParseErr              = errors.New("error in parsing message field")
	UseCaseNotSupportedErr     = errors.New("use case not supported")
	MessageNilErr              = errors.New("input message is empty")
	InvalidMessageErr          = errors.New("input message is invalid")
	VendorNotSupportedErr      = errors.New("vendor not supported in conversion layer")
	InvalidTicketForUseCaseErr = errors.New("ticket type not valid for use case")
	MandatoryFieldMissingErr   = errors.New("mandatory field is not present")
)
