syntax = "proto3";

package parser.publisher;

import "api/order/order.proto";
import "api/order/payment/transaction.proto";
import "api/queue/consumer_headers.proto";

option go_package = "github.com/epifi/gamma/api/parser/publisher";
option java_package = "com.github.epifi.gamma.api.parser.publisher";

message PayTxnBackfillPublisher {
  // common request header across all publishers
  queue.ConsumerRequestHeader request_header = 1;

  // Field which contains list of transaction along with its order and the respective field masks
  // which represent the fields changed during txn backfill process
  repeated OrderAndTransactionsWithFieldMasks order_with_transactions = 2;

  enum BackfillEventSource{
    Backfill_Event_Source_UNSPECIFIED = 0;
    Backfill_Event_Source_REMITTER_INFO_BACKFILL = 1;
    Backfill_Event_Source_TRANSACTION_BACKFILL = 2;
  }
  // Used to identify the workflow which  published the event to the backfill topic
  BackfillEventSource backfill_event_source = 3;
}

// OrderAndTransactionsWithFieldMasks contains transaction with corresponding order along with their
// respective field masks.
message OrderAndTransactionsWithFieldMasks{
  repeated TxnWithFieldMask txn_with_field_mask = 1;
  OrderWithFieldMask order_with_field_mask = 2;
}

// TxnWithFieldMask contains transactions which are changed during backfilling process.
// It contains transaction model with all necessary details along with the field masks.
message TxnWithFieldMask {
  order.payment.Transaction transaction_detail = 1;
  repeated order.payment.TransactionFieldMask field_masks = 2;
}

// OrderWithFieldMask contains orders which are changed during backfilling process.
// It contains order model with all necessary details along with the field masks.
message OrderWithFieldMask {
  order.Order order_detail = 1;
  repeated order.OrderFieldMask field_masks = 2;
}
