// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/savings/service.proto

package savings

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	dynamic_elements "github.com/epifi/gamma/api/dynamic_elements"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	account "github.com/epifi/gamma/api/typesv2/account"
	date "google.golang.org/genproto/googleapis/type/date"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SignStatus int32

const (
	SignStatus_SIGN_STATUS_UNSPECIFIED SignStatus = 0
	// if account status api have sign count >=1
	SignStatus_SIGN_STATUS_ALREADY_AVAILABLE_WITH_BANK SignStatus = 1
	// if last attempt is successful but bank don't have sign
	SignStatus_SIGN_STATUS_SIGN_IN_REVIEW SignStatus = 2
	// first attempt created
	SignStatus_SIGN_STATUS_FIRST_ATTEMPT_CREATED SignStatus = 3
	// last attempt is in progress, in this case we return same url
	SignStatus_SIGN_STATUS_LAST_ATTEMPT_IN_PROGRESS SignStatus = 4
	// last attempt failed, we create a new attempt
	SignStatus_SIGN_STATUS_LAST_ATTEMPT_FAILED SignStatus = 5
	// last attempt is signed but federal sign count < 1 even after breaching time threshold
	SignStatus_SIGN_STATUS_LAST_ATTEMPT_FAILED_SINCE_PENDING_AT_BANK SignStatus = 6
)

// Enum value maps for SignStatus.
var (
	SignStatus_name = map[int32]string{
		0: "SIGN_STATUS_UNSPECIFIED",
		1: "SIGN_STATUS_ALREADY_AVAILABLE_WITH_BANK",
		2: "SIGN_STATUS_SIGN_IN_REVIEW",
		3: "SIGN_STATUS_FIRST_ATTEMPT_CREATED",
		4: "SIGN_STATUS_LAST_ATTEMPT_IN_PROGRESS",
		5: "SIGN_STATUS_LAST_ATTEMPT_FAILED",
		6: "SIGN_STATUS_LAST_ATTEMPT_FAILED_SINCE_PENDING_AT_BANK",
	}
	SignStatus_value = map[string]int32{
		"SIGN_STATUS_UNSPECIFIED":                               0,
		"SIGN_STATUS_ALREADY_AVAILABLE_WITH_BANK":               1,
		"SIGN_STATUS_SIGN_IN_REVIEW":                            2,
		"SIGN_STATUS_FIRST_ATTEMPT_CREATED":                     3,
		"SIGN_STATUS_LAST_ATTEMPT_IN_PROGRESS":                  4,
		"SIGN_STATUS_LAST_ATTEMPT_FAILED":                       5,
		"SIGN_STATUS_LAST_ATTEMPT_FAILED_SINCE_PENDING_AT_BANK": 6,
	}
)

func (x SignStatus) Enum() *SignStatus {
	p := new(SignStatus)
	*p = x
	return p
}

func (x SignStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SignStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_savings_service_proto_enumTypes[0].Descriptor()
}

func (SignStatus) Type() protoreflect.EnumType {
	return &file_api_savings_service_proto_enumTypes[0]
}

func (x SignStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SignStatus.Descriptor instead.
func (SignStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{0}
}

type UpdateBalanceResponse_Status int32

const (
	UpdateBalanceResponse_OK UpdateBalanceResponse_Status = 0
	// Account data corresponding to the given identifier not found
	UpdateBalanceResponse_NOT_FOUND UpdateBalanceResponse_Status = 5
	// Internal server
	UpdateBalanceResponse_INTERNAL UpdateBalanceResponse_Status = 13
)

// Enum value maps for UpdateBalanceResponse_Status.
var (
	UpdateBalanceResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "NOT_FOUND",
		13: "INTERNAL",
	}
	UpdateBalanceResponse_Status_value = map[string]int32{
		"OK":        0,
		"NOT_FOUND": 5,
		"INTERNAL":  13,
	}
)

func (x UpdateBalanceResponse_Status) Enum() *UpdateBalanceResponse_Status {
	p := new(UpdateBalanceResponse_Status)
	*p = x
	return p
}

func (x UpdateBalanceResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpdateBalanceResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_savings_service_proto_enumTypes[1].Descriptor()
}

func (UpdateBalanceResponse_Status) Type() protoreflect.EnumType {
	return &file_api_savings_service_proto_enumTypes[1]
}

func (x UpdateBalanceResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpdateBalanceResponse_Status.Descriptor instead.
func (UpdateBalanceResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{6, 0}
}

type GetAccountBalanceV1Request_DataFreshness int32

const (
	GetAccountBalanceV1Request_DATA_FRESHNESS_UNSPECIFIED GetAccountBalanceV1Request_DataFreshness = 0
	// Data has to be real time
	// Using this option will lead to higher latency as this usually means we hit
	// partner banks to fetch real time data
	GetAccountBalanceV1Request_REAL_TIME GetAccountBalanceV1Request_DataFreshness = 1
	// Data can be stale by a delay of 20 seconds
	// This option is a hybrid between `REAL_TIME` and `STALE` and offers a balance of latency and data freshness
	// Recommendation: Should work for most business use cases unless it blocks user facing application
	// e.g., Fund transfer
	GetAccountBalanceV1Request_NEAR_REAL_TIME GetAccountBalanceV1Request_DataFreshness = 2
	// Data is expected to be stale by up to 60 seconds
	// Recommendation: Use for analytical purposes
	GetAccountBalanceV1Request_STALE GetAccountBalanceV1Request_DataFreshness = 3
	// Data is expected to be stale by 60 seconds and more.
	// The balance data if older than 60s is attempted to be refreshed from vendor.
	// In case, the refresh fails historical data that is present in the DB cache is returned.
	GetAccountBalanceV1Request_HISTORICAL GetAccountBalanceV1Request_DataFreshness = 4
	// LAST_KNOWN_BALANCE_FROM_DB will return balance which is store at DB.
	GetAccountBalanceV1Request_LAST_KNOWN_BALANCE_FROM_DB GetAccountBalanceV1Request_DataFreshness = 5
)

// Enum value maps for GetAccountBalanceV1Request_DataFreshness.
var (
	GetAccountBalanceV1Request_DataFreshness_name = map[int32]string{
		0: "DATA_FRESHNESS_UNSPECIFIED",
		1: "REAL_TIME",
		2: "NEAR_REAL_TIME",
		3: "STALE",
		4: "HISTORICAL",
		5: "LAST_KNOWN_BALANCE_FROM_DB",
	}
	GetAccountBalanceV1Request_DataFreshness_value = map[string]int32{
		"DATA_FRESHNESS_UNSPECIFIED": 0,
		"REAL_TIME":                  1,
		"NEAR_REAL_TIME":             2,
		"STALE":                      3,
		"HISTORICAL":                 4,
		"LAST_KNOWN_BALANCE_FROM_DB": 5,
	}
)

func (x GetAccountBalanceV1Request_DataFreshness) Enum() *GetAccountBalanceV1Request_DataFreshness {
	p := new(GetAccountBalanceV1Request_DataFreshness)
	*p = x
	return p
}

func (x GetAccountBalanceV1Request_DataFreshness) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetAccountBalanceV1Request_DataFreshness) Descriptor() protoreflect.EnumDescriptor {
	return file_api_savings_service_proto_enumTypes[2].Descriptor()
}

func (GetAccountBalanceV1Request_DataFreshness) Type() protoreflect.EnumType {
	return &file_api_savings_service_proto_enumTypes[2]
}

func (x GetAccountBalanceV1Request_DataFreshness) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetAccountBalanceV1Request_DataFreshness.Descriptor instead.
func (GetAccountBalanceV1Request_DataFreshness) EnumDescriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{7, 0}
}

type GetAccountBalanceV1Request_VendorApiOption int32

const (
	GetAccountBalanceV1Request_VENDOR_API_OPTION_UNSPECIFIED GetAccountBalanceV1Request_VendorApiOption = 0
	// Using this option will lead to not comparing old and new vendor api.
	// It will give response of new vendor api.
	GetAccountBalanceV1Request_NEW_VENDOR_API GetAccountBalanceV1Request_VendorApiOption = 1
)

// Enum value maps for GetAccountBalanceV1Request_VendorApiOption.
var (
	GetAccountBalanceV1Request_VendorApiOption_name = map[int32]string{
		0: "VENDOR_API_OPTION_UNSPECIFIED",
		1: "NEW_VENDOR_API",
	}
	GetAccountBalanceV1Request_VendorApiOption_value = map[string]int32{
		"VENDOR_API_OPTION_UNSPECIFIED": 0,
		"NEW_VENDOR_API":                1,
	}
)

func (x GetAccountBalanceV1Request_VendorApiOption) Enum() *GetAccountBalanceV1Request_VendorApiOption {
	p := new(GetAccountBalanceV1Request_VendorApiOption)
	*p = x
	return p
}

func (x GetAccountBalanceV1Request_VendorApiOption) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetAccountBalanceV1Request_VendorApiOption) Descriptor() protoreflect.EnumDescriptor {
	return file_api_savings_service_proto_enumTypes[3].Descriptor()
}

func (GetAccountBalanceV1Request_VendorApiOption) Type() protoreflect.EnumType {
	return &file_api_savings_service_proto_enumTypes[3]
}

func (x GetAccountBalanceV1Request_VendorApiOption) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetAccountBalanceV1Request_VendorApiOption.Descriptor instead.
func (GetAccountBalanceV1Request_VendorApiOption) EnumDescriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{7, 1}
}

type GetAccountBalanceV1Response_Status int32

const (
	GetAccountBalanceV1Response_OK GetAccountBalanceV1Response_Status = 0
	// Account data corresponding to the given identifier not found
	GetAccountBalanceV1Response_NOT_FOUND GetAccountBalanceV1Response_Status = 5
	// The actor does not have permission to execute the specified operation.
	// One of the reasons could be that Actor is not owner of the account
	GetAccountBalanceV1Response_PERMISSION_DENIED GetAccountBalanceV1Response_Status = 7
	// Internal server
	GetAccountBalanceV1Response_INTERNAL GetAccountBalanceV1Response_Status = 13
	// account number not created (creation in progress or max retries exhausted)
	GetAccountBalanceV1Response_ACCOUNT_NUMBER_NOT_CREATED GetAccountBalanceV1Response_Status = 100
	// account has no access level (cannot call vg in this case)
	// an account can have no access level in scnerios like (but not limited to):
	// 1. Min kyc account expired
	// 2. Risk Threshold breached
	// In such cases, there is no point of calling vendor since we do not get the desired response i.e. balance
	GetAccountBalanceV1Response_ACCOUNT_NO_LEVEL_ACCESS GetAccountBalanceV1Response_Status = 101
)

// Enum value maps for GetAccountBalanceV1Response_Status.
var (
	GetAccountBalanceV1Response_Status_name = map[int32]string{
		0:   "OK",
		5:   "NOT_FOUND",
		7:   "PERMISSION_DENIED",
		13:  "INTERNAL",
		100: "ACCOUNT_NUMBER_NOT_CREATED",
		101: "ACCOUNT_NO_LEVEL_ACCESS",
	}
	GetAccountBalanceV1Response_Status_value = map[string]int32{
		"OK":                         0,
		"NOT_FOUND":                  5,
		"PERMISSION_DENIED":          7,
		"INTERNAL":                   13,
		"ACCOUNT_NUMBER_NOT_CREATED": 100,
		"ACCOUNT_NO_LEVEL_ACCESS":    101,
	}
)

func (x GetAccountBalanceV1Response_Status) Enum() *GetAccountBalanceV1Response_Status {
	p := new(GetAccountBalanceV1Response_Status)
	*p = x
	return p
}

func (x GetAccountBalanceV1Response_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetAccountBalanceV1Response_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_savings_service_proto_enumTypes[4].Descriptor()
}

func (GetAccountBalanceV1Response_Status) Type() protoreflect.EnumType {
	return &file_api_savings_service_proto_enumTypes[4]
}

func (x GetAccountBalanceV1Response_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetAccountBalanceV1Response_Status.Descriptor instead.
func (GetAccountBalanceV1Response_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{8, 0}
}

// This enum denotes from which stage the account creation needs to be restarted. There are two
// steps in account creation -
// 1. Account creation
// 2. Enquiry for account creation
type CreateAccountRequest_ForceRetryOption int32

const (
	// When retry is not needed
	CreateAccountRequest_UNSPECIFIED CreateAccountRequest_ForceRetryOption = 0
	// Force retry needs to be triggered from account creation
	CreateAccountRequest_FORCE_NEW_REQUEST CreateAccountRequest_ForceRetryOption = 1
	// Force retry needs to be triggered from enquiry status
	CreateAccountRequest_FORCE_INQUIRY CreateAccountRequest_ForceRetryOption = 2
)

// Enum value maps for CreateAccountRequest_ForceRetryOption.
var (
	CreateAccountRequest_ForceRetryOption_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "FORCE_NEW_REQUEST",
		2: "FORCE_INQUIRY",
	}
	CreateAccountRequest_ForceRetryOption_value = map[string]int32{
		"UNSPECIFIED":       0,
		"FORCE_NEW_REQUEST": 1,
		"FORCE_INQUIRY":     2,
	}
)

func (x CreateAccountRequest_ForceRetryOption) Enum() *CreateAccountRequest_ForceRetryOption {
	p := new(CreateAccountRequest_ForceRetryOption)
	*p = x
	return p
}

func (x CreateAccountRequest_ForceRetryOption) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreateAccountRequest_ForceRetryOption) Descriptor() protoreflect.EnumDescriptor {
	return file_api_savings_service_proto_enumTypes[5].Descriptor()
}

func (CreateAccountRequest_ForceRetryOption) Type() protoreflect.EnumType {
	return &file_api_savings_service_proto_enumTypes[5]
}

func (x CreateAccountRequest_ForceRetryOption) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreateAccountRequest_ForceRetryOption.Descriptor instead.
func (CreateAccountRequest_ForceRetryOption) EnumDescriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{9, 0}
}

type CreateAccountResponse_Status int32

const (
	CreateAccountResponse_OK CreateAccountResponse_Status = 0
	// INVALID_DEDUPE_STATUS indicates that the dedupe check before account creation returned a dedupe status that is not
	// supported for account creation.
	CreateAccountResponse_INVALID_DEDUPE_STATUS CreateAccountResponse_Status = 100
)

// Enum value maps for CreateAccountResponse_Status.
var (
	CreateAccountResponse_Status_name = map[int32]string{
		0:   "OK",
		100: "INVALID_DEDUPE_STATUS",
	}
	CreateAccountResponse_Status_value = map[string]int32{
		"OK":                    0,
		"INVALID_DEDUPE_STATUS": 100,
	}
)

func (x CreateAccountResponse_Status) Enum() *CreateAccountResponse_Status {
	p := new(CreateAccountResponse_Status)
	*p = x
	return p
}

func (x CreateAccountResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreateAccountResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_savings_service_proto_enumTypes[6].Descriptor()
}

func (CreateAccountResponse_Status) Type() protoreflect.EnumType {
	return &file_api_savings_service_proto_enumTypes[6]
}

func (x CreateAccountResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreateAccountResponse_Status.Descriptor instead.
func (CreateAccountResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{10, 0}
}

type GetAccountsListResponse_Status int32

const (
	GetAccountsListResponse_OK GetAccountsListResponse_Status = 0
	// Account data corresponding to the given identifier not found
	GetAccountsListResponse_NOT_FOUND GetAccountsListResponse_Status = 5
	// Internal server
	GetAccountsListResponse_INTERNAL GetAccountsListResponse_Status = 13
)

// Enum value maps for GetAccountsListResponse_Status.
var (
	GetAccountsListResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "NOT_FOUND",
		13: "INTERNAL",
	}
	GetAccountsListResponse_Status_value = map[string]int32{
		"OK":        0,
		"NOT_FOUND": 5,
		"INTERNAL":  13,
	}
)

func (x GetAccountsListResponse_Status) Enum() *GetAccountsListResponse_Status {
	p := new(GetAccountsListResponse_Status)
	*p = x
	return p
}

func (x GetAccountsListResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetAccountsListResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_savings_service_proto_enumTypes[7].Descriptor()
}

func (GetAccountsListResponse_Status) Type() protoreflect.EnumType {
	return &file_api_savings_service_proto_enumTypes[7]
}

func (x GetAccountsListResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetAccountsListResponse_Status.Descriptor instead.
func (GetAccountsListResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{15, 0}
}

type GetAccountBalanceRequest_DataFreshness int32

const (
	GetAccountBalanceRequest_DATA_FRESHNESS_UNSPECIFIED GetAccountBalanceRequest_DataFreshness = 0
	// Data has to be real time
	// Using this option will lead to higher latency as this usually means we hit
	// partner banks to fetch real time data
	GetAccountBalanceRequest_REAL_TIME GetAccountBalanceRequest_DataFreshness = 1
	// Data can be stale by a delay of 10 seconds
	// This option is a hybrid between `REAL_TIME` and `STALE` and offers a balance of latency and data freshness
	// Recommendation: Should work for most business use cases unless it blocks user facing application
	// e.g., Fund transfer
	GetAccountBalanceRequest_NEAR_REAL_TIME GetAccountBalanceRequest_DataFreshness = 2
	// Data is expected to be stale by up to 60 seconds
	// Recommendation: Use for analytical purposes
	GetAccountBalanceRequest_STALE GetAccountBalanceRequest_DataFreshness = 3
)

// Enum value maps for GetAccountBalanceRequest_DataFreshness.
var (
	GetAccountBalanceRequest_DataFreshness_name = map[int32]string{
		0: "DATA_FRESHNESS_UNSPECIFIED",
		1: "REAL_TIME",
		2: "NEAR_REAL_TIME",
		3: "STALE",
	}
	GetAccountBalanceRequest_DataFreshness_value = map[string]int32{
		"DATA_FRESHNESS_UNSPECIFIED": 0,
		"REAL_TIME":                  1,
		"NEAR_REAL_TIME":             2,
		"STALE":                      3,
	}
)

func (x GetAccountBalanceRequest_DataFreshness) Enum() *GetAccountBalanceRequest_DataFreshness {
	p := new(GetAccountBalanceRequest_DataFreshness)
	*p = x
	return p
}

func (x GetAccountBalanceRequest_DataFreshness) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetAccountBalanceRequest_DataFreshness) Descriptor() protoreflect.EnumDescriptor {
	return file_api_savings_service_proto_enumTypes[8].Descriptor()
}

func (GetAccountBalanceRequest_DataFreshness) Type() protoreflect.EnumType {
	return &file_api_savings_service_proto_enumTypes[8]
}

func (x GetAccountBalanceRequest_DataFreshness) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetAccountBalanceRequest_DataFreshness.Descriptor instead.
func (GetAccountBalanceRequest_DataFreshness) EnumDescriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{19, 0}
}

type GetAccountBalanceResponse_Status int32

const (
	GetAccountBalanceResponse_OK GetAccountBalanceResponse_Status = 0
	// Account data corresponding to the given identifier not found
	GetAccountBalanceResponse_NOT_FOUND GetAccountBalanceResponse_Status = 5
	// The actor does not have permission to execute the specified operation.
	// One of the reasons could be that Actor is not owner of the account
	GetAccountBalanceResponse_PERMISSION_DENIED GetAccountBalanceResponse_Status = 7
	// Internal server
	GetAccountBalanceResponse_INTERNAL GetAccountBalanceResponse_Status = 13
	// account number not created (creation in progress or max retries exhausted)
	GetAccountBalanceResponse_ACCOUNT_NUMBER_NOT_CREATED GetAccountBalanceResponse_Status = 100
	// account has no access level (cannot call vg in this case)
	GetAccountBalanceResponse_ACCOUNT_NO_LEVEL_ACCESS GetAccountBalanceResponse_Status = 101
)

// Enum value maps for GetAccountBalanceResponse_Status.
var (
	GetAccountBalanceResponse_Status_name = map[int32]string{
		0:   "OK",
		5:   "NOT_FOUND",
		7:   "PERMISSION_DENIED",
		13:  "INTERNAL",
		100: "ACCOUNT_NUMBER_NOT_CREATED",
		101: "ACCOUNT_NO_LEVEL_ACCESS",
	}
	GetAccountBalanceResponse_Status_value = map[string]int32{
		"OK":                         0,
		"NOT_FOUND":                  5,
		"PERMISSION_DENIED":          7,
		"INTERNAL":                   13,
		"ACCOUNT_NUMBER_NOT_CREATED": 100,
		"ACCOUNT_NO_LEVEL_ACCESS":    101,
	}
)

func (x GetAccountBalanceResponse_Status) Enum() *GetAccountBalanceResponse_Status {
	p := new(GetAccountBalanceResponse_Status)
	*p = x
	return p
}

func (x GetAccountBalanceResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetAccountBalanceResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_savings_service_proto_enumTypes[9].Descriptor()
}

func (GetAccountBalanceResponse_Status) Type() protoreflect.EnumType {
	return &file_api_savings_service_proto_enumTypes[9]
}

func (x GetAccountBalanceResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetAccountBalanceResponse_Status.Descriptor instead.
func (GetAccountBalanceResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{20, 0}
}

type UpdateAccountResponse_Status int32

const (
	UpdateAccountResponse_OK UpdateAccountResponse_Status = 0
	// Account data corresponding to the given identifier not found
	UpdateAccountResponse_NOT_FOUND UpdateAccountResponse_Status = 5
	// Internal server
	UpdateAccountResponse_INTERNAL UpdateAccountResponse_Status = 13
)

// Enum value maps for UpdateAccountResponse_Status.
var (
	UpdateAccountResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "NOT_FOUND",
		13: "INTERNAL",
	}
	UpdateAccountResponse_Status_value = map[string]int32{
		"OK":        0,
		"NOT_FOUND": 5,
		"INTERNAL":  13,
	}
)

func (x UpdateAccountResponse_Status) Enum() *UpdateAccountResponse_Status {
	p := new(UpdateAccountResponse_Status)
	*p = x
	return p
}

func (x UpdateAccountResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpdateAccountResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_savings_service_proto_enumTypes[10].Descriptor()
}

func (UpdateAccountResponse_Status) Type() protoreflect.EnumType {
	return &file_api_savings_service_proto_enumTypes[10]
}

func (x UpdateAccountResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpdateAccountResponse_Status.Descriptor instead.
func (UpdateAccountResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{22, 0}
}

type GetOpeningBalanceResponse_Status int32

const (
	GetOpeningBalanceResponse_OK GetOpeningBalanceResponse_Status = 0
	// Account data corresponding to the given identifier not found
	GetOpeningBalanceResponse_NOT_FOUND GetOpeningBalanceResponse_Status = 5
	// The actor does not have permission to execute the specified operation.
	// One of the reasons could be that Actor is not owner of the account
	GetOpeningBalanceResponse_PERMISSION_DENIED GetOpeningBalanceResponse_Status = 7
	// Internal server
	GetOpeningBalanceResponse_INTERNAL GetOpeningBalanceResponse_Status = 13
)

// Enum value maps for GetOpeningBalanceResponse_Status.
var (
	GetOpeningBalanceResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "NOT_FOUND",
		7:  "PERMISSION_DENIED",
		13: "INTERNAL",
	}
	GetOpeningBalanceResponse_Status_value = map[string]int32{
		"OK":                0,
		"NOT_FOUND":         5,
		"PERMISSION_DENIED": 7,
		"INTERNAL":          13,
	}
)

func (x GetOpeningBalanceResponse_Status) Enum() *GetOpeningBalanceResponse_Status {
	p := new(GetOpeningBalanceResponse_Status)
	*p = x
	return p
}

func (x GetOpeningBalanceResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetOpeningBalanceResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_savings_service_proto_enumTypes[11].Descriptor()
}

func (GetOpeningBalanceResponse_Status) Type() protoreflect.EnumType {
	return &file_api_savings_service_proto_enumTypes[11]
}

func (x GetOpeningBalanceResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetOpeningBalanceResponse_Status.Descriptor instead.
func (GetOpeningBalanceResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{24, 0}
}

type GetAccountBalanceWithSummaryRequest_Range int32

const (
	GetAccountBalanceWithSummaryRequest_RANGE_UNSPECIFIED GetAccountBalanceWithSummaryRequest_Range = 0
	GetAccountBalanceWithSummaryRequest_MONTH             GetAccountBalanceWithSummaryRequest_Range = 1
)

// Enum value maps for GetAccountBalanceWithSummaryRequest_Range.
var (
	GetAccountBalanceWithSummaryRequest_Range_name = map[int32]string{
		0: "RANGE_UNSPECIFIED",
		1: "MONTH",
	}
	GetAccountBalanceWithSummaryRequest_Range_value = map[string]int32{
		"RANGE_UNSPECIFIED": 0,
		"MONTH":             1,
	}
)

func (x GetAccountBalanceWithSummaryRequest_Range) Enum() *GetAccountBalanceWithSummaryRequest_Range {
	p := new(GetAccountBalanceWithSummaryRequest_Range)
	*p = x
	return p
}

func (x GetAccountBalanceWithSummaryRequest_Range) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetAccountBalanceWithSummaryRequest_Range) Descriptor() protoreflect.EnumDescriptor {
	return file_api_savings_service_proto_enumTypes[12].Descriptor()
}

func (GetAccountBalanceWithSummaryRequest_Range) Type() protoreflect.EnumType {
	return &file_api_savings_service_proto_enumTypes[12]
}

func (x GetAccountBalanceWithSummaryRequest_Range) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetAccountBalanceWithSummaryRequest_Range.Descriptor instead.
func (GetAccountBalanceWithSummaryRequest_Range) EnumDescriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{25, 0}
}

// enum sent from the client to identify if the service is requesting to update balance on his home screen.
// Optional field: If not sent then default FORCE_BALANCE_UPDATE_UNSPECIFIED will be set
// If FORCE_BALANCE_UPDATE_NEEDED is passed as value then we will fetch the balance from vendor GetBalance first and fallback will be GetBalanceV1 api
// and vice versa if the FORCE_BALANCE_UPDATE_NEEDED is not provided
type GetAccountBalanceWithSummaryRequest_ForceBalanceUpdate int32

const (
	GetAccountBalanceWithSummaryRequest_FORCE_BALANCE_UPDATE_UNSPECIFIED GetAccountBalanceWithSummaryRequest_ForceBalanceUpdate = 0
	GetAccountBalanceWithSummaryRequest_FORCE_BALANCE_UPDATE_NEEDED      GetAccountBalanceWithSummaryRequest_ForceBalanceUpdate = 1
	GetAccountBalanceWithSummaryRequest_FORCE_BALANCE_UPDATE_NOT_NEEDED  GetAccountBalanceWithSummaryRequest_ForceBalanceUpdate = 2
)

// Enum value maps for GetAccountBalanceWithSummaryRequest_ForceBalanceUpdate.
var (
	GetAccountBalanceWithSummaryRequest_ForceBalanceUpdate_name = map[int32]string{
		0: "FORCE_BALANCE_UPDATE_UNSPECIFIED",
		1: "FORCE_BALANCE_UPDATE_NEEDED",
		2: "FORCE_BALANCE_UPDATE_NOT_NEEDED",
	}
	GetAccountBalanceWithSummaryRequest_ForceBalanceUpdate_value = map[string]int32{
		"FORCE_BALANCE_UPDATE_UNSPECIFIED": 0,
		"FORCE_BALANCE_UPDATE_NEEDED":      1,
		"FORCE_BALANCE_UPDATE_NOT_NEEDED":  2,
	}
)

func (x GetAccountBalanceWithSummaryRequest_ForceBalanceUpdate) Enum() *GetAccountBalanceWithSummaryRequest_ForceBalanceUpdate {
	p := new(GetAccountBalanceWithSummaryRequest_ForceBalanceUpdate)
	*p = x
	return p
}

func (x GetAccountBalanceWithSummaryRequest_ForceBalanceUpdate) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetAccountBalanceWithSummaryRequest_ForceBalanceUpdate) Descriptor() protoreflect.EnumDescriptor {
	return file_api_savings_service_proto_enumTypes[13].Descriptor()
}

func (GetAccountBalanceWithSummaryRequest_ForceBalanceUpdate) Type() protoreflect.EnumType {
	return &file_api_savings_service_proto_enumTypes[13]
}

func (x GetAccountBalanceWithSummaryRequest_ForceBalanceUpdate) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetAccountBalanceWithSummaryRequest_ForceBalanceUpdate.Descriptor instead.
func (GetAccountBalanceWithSummaryRequest_ForceBalanceUpdate) EnumDescriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{25, 1}
}

type GetAccountBalanceWithSummaryResponse_Status int32

const (
	GetAccountBalanceWithSummaryResponse_OK                             GetAccountBalanceWithSummaryResponse_Status = 0
	GetAccountBalanceWithSummaryResponse_INTERNAL                       GetAccountBalanceWithSummaryResponse_Status = 13
	GetAccountBalanceWithSummaryResponse_DEVICE_TEMPORARILY_DEACTIVATED GetAccountBalanceWithSummaryResponse_Status = 100
)

// Enum value maps for GetAccountBalanceWithSummaryResponse_Status.
var (
	GetAccountBalanceWithSummaryResponse_Status_name = map[int32]string{
		0:   "OK",
		13:  "INTERNAL",
		100: "DEVICE_TEMPORARILY_DEACTIVATED",
	}
	GetAccountBalanceWithSummaryResponse_Status_value = map[string]int32{
		"OK":                             0,
		"INTERNAL":                       13,
		"DEVICE_TEMPORARILY_DEACTIVATED": 100,
	}
)

func (x GetAccountBalanceWithSummaryResponse_Status) Enum() *GetAccountBalanceWithSummaryResponse_Status {
	p := new(GetAccountBalanceWithSummaryResponse_Status)
	*p = x
	return p
}

func (x GetAccountBalanceWithSummaryResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetAccountBalanceWithSummaryResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_savings_service_proto_enumTypes[14].Descriptor()
}

func (GetAccountBalanceWithSummaryResponse_Status) Type() protoreflect.EnumType {
	return &file_api_savings_service_proto_enumTypes[14]
}

func (x GetAccountBalanceWithSummaryResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetAccountBalanceWithSummaryResponse_Status.Descriptor instead.
func (GetAccountBalanceWithSummaryResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{26, 0}
}

type AccountSummaryBucketDetails_Bucket int32

const (
	AccountSummaryBucketDetails_BUCKET_UNSPECIFIED AccountSummaryBucketDetails_Bucket = 0
	AccountSummaryBucketDetails_CREDIT             AccountSummaryBucketDetails_Bucket = 1
	AccountSummaryBucketDetails_SPENT              AccountSummaryBucketDetails_Bucket = 2
	AccountSummaryBucketDetails_SAVED              AccountSummaryBucketDetails_Bucket = 3
)

// Enum value maps for AccountSummaryBucketDetails_Bucket.
var (
	AccountSummaryBucketDetails_Bucket_name = map[int32]string{
		0: "BUCKET_UNSPECIFIED",
		1: "CREDIT",
		2: "SPENT",
		3: "SAVED",
	}
	AccountSummaryBucketDetails_Bucket_value = map[string]int32{
		"BUCKET_UNSPECIFIED": 0,
		"CREDIT":             1,
		"SPENT":              2,
		"SAVED":              3,
	}
)

func (x AccountSummaryBucketDetails_Bucket) Enum() *AccountSummaryBucketDetails_Bucket {
	p := new(AccountSummaryBucketDetails_Bucket)
	*p = x
	return p
}

func (x AccountSummaryBucketDetails_Bucket) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AccountSummaryBucketDetails_Bucket) Descriptor() protoreflect.EnumDescriptor {
	return file_api_savings_service_proto_enumTypes[15].Descriptor()
}

func (AccountSummaryBucketDetails_Bucket) Type() protoreflect.EnumType {
	return &file_api_savings_service_proto_enumTypes[15]
}

func (x AccountSummaryBucketDetails_Bucket) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AccountSummaryBucketDetails_Bucket.Descriptor instead.
func (AccountSummaryBucketDetails_Bucket) EnumDescriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{27, 0}
}

type GetListOfActiveAccountsResponse_Status int32

const (
	GetListOfActiveAccountsResponse_OK GetListOfActiveAccountsResponse_Status = 0
	// No active accounts found
	GetListOfActiveAccountsResponse_NOT_FOUND GetListOfActiveAccountsResponse_Status = 5
	// Internal server error
	GetListOfActiveAccountsResponse_INTERNAL GetListOfActiveAccountsResponse_Status = 13
)

// Enum value maps for GetListOfActiveAccountsResponse_Status.
var (
	GetListOfActiveAccountsResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "NOT_FOUND",
		13: "INTERNAL",
	}
	GetListOfActiveAccountsResponse_Status_value = map[string]int32{
		"OK":        0,
		"NOT_FOUND": 5,
		"INTERNAL":  13,
	}
)

func (x GetListOfActiveAccountsResponse_Status) Enum() *GetListOfActiveAccountsResponse_Status {
	p := new(GetListOfActiveAccountsResponse_Status)
	*p = x
	return p
}

func (x GetListOfActiveAccountsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetListOfActiveAccountsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_savings_service_proto_enumTypes[16].Descriptor()
}

func (GetListOfActiveAccountsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_savings_service_proto_enumTypes[16]
}

func (x GetListOfActiveAccountsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetListOfActiveAccountsResponse_Status.Descriptor instead.
func (GetListOfActiveAccountsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{29, 0}
}

type IsTxnAllowedRequest_TxnType int32

const (
	IsTxnAllowedRequest_TXN_TYPE_UNSPECIFIED IsTxnAllowedRequest_TxnType = 0
	IsTxnAllowedRequest_CREDIT               IsTxnAllowedRequest_TxnType = 1
	IsTxnAllowedRequest_DEBIT                IsTxnAllowedRequest_TxnType = 2
)

// Enum value maps for IsTxnAllowedRequest_TxnType.
var (
	IsTxnAllowedRequest_TxnType_name = map[int32]string{
		0: "TXN_TYPE_UNSPECIFIED",
		1: "CREDIT",
		2: "DEBIT",
	}
	IsTxnAllowedRequest_TxnType_value = map[string]int32{
		"TXN_TYPE_UNSPECIFIED": 0,
		"CREDIT":               1,
		"DEBIT":                2,
	}
)

func (x IsTxnAllowedRequest_TxnType) Enum() *IsTxnAllowedRequest_TxnType {
	p := new(IsTxnAllowedRequest_TxnType)
	*p = x
	return p
}

func (x IsTxnAllowedRequest_TxnType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IsTxnAllowedRequest_TxnType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_savings_service_proto_enumTypes[17].Descriptor()
}

func (IsTxnAllowedRequest_TxnType) Type() protoreflect.EnumType {
	return &file_api_savings_service_proto_enumTypes[17]
}

func (x IsTxnAllowedRequest_TxnType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IsTxnAllowedRequest_TxnType.Descriptor instead.
func (IsTxnAllowedRequest_TxnType) EnumDescriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{34, 0}
}

type IsTxnAllowedResponse_Status int32

const (
	// credit transaction allowed
	IsTxnAllowedResponse_OK IsTxnAllowedResponse_Status = 0
	// No accounts found
	IsTxnAllowedResponse_NOT_FOUND IsTxnAllowedResponse_Status = 5
	// Internal server error
	IsTxnAllowedResponse_INTERNAL IsTxnAllowedResponse_Status = 13
	// credit transaction not allowed as one or more min-kyc checks failed
	IsTxnAllowedResponse_NOT_ALLOWED_MIN_KYC_CHECK_FAILURE IsTxnAllowedResponse_Status = 101
)

// Enum value maps for IsTxnAllowedResponse_Status.
var (
	IsTxnAllowedResponse_Status_name = map[int32]string{
		0:   "OK",
		5:   "NOT_FOUND",
		13:  "INTERNAL",
		101: "NOT_ALLOWED_MIN_KYC_CHECK_FAILURE",
	}
	IsTxnAllowedResponse_Status_value = map[string]int32{
		"OK":                                0,
		"NOT_FOUND":                         5,
		"INTERNAL":                          13,
		"NOT_ALLOWED_MIN_KYC_CHECK_FAILURE": 101,
	}
)

func (x IsTxnAllowedResponse_Status) Enum() *IsTxnAllowedResponse_Status {
	p := new(IsTxnAllowedResponse_Status)
	*p = x
	return p
}

func (x IsTxnAllowedResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IsTxnAllowedResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_savings_service_proto_enumTypes[18].Descriptor()
}

func (IsTxnAllowedResponse_Status) Type() protoreflect.EnumType {
	return &file_api_savings_service_proto_enumTypes[18]
}

func (x IsTxnAllowedResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IsTxnAllowedResponse_Status.Descriptor instead.
func (IsTxnAllowedResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{35, 0}
}

type IsTxnAllowedResponse_Reason int32

const (
	IsTxnAllowedResponse_REASON_UNSPECIFIED                     IsTxnAllowedResponse_Reason = 0
	IsTxnAllowedResponse_MIN_KYC_ACCOUNT_DURATION_CHECK_FAILURE IsTxnAllowedResponse_Reason = 1
	IsTxnAllowedResponse_MIN_KYC_MAX_BALANCE_CHECK_FAILURE      IsTxnAllowedResponse_Reason = 2
	IsTxnAllowedResponse_MIN_KYC_MAX_CREDIT_LIMIT_CHECK_FAILURE IsTxnAllowedResponse_Reason = 3
	// new user here signifies that user's account was created after
	// MinKycLimitChangeDate (savings/config/config.go)
	IsTxnAllowedResponse_MIN_KYC_MAX_BALANCE_CHECK_FAILURE_FOR_NEW_USER IsTxnAllowedResponse_Reason = 4
	// new user here signifies that user's account was created after
	// MinKycLimitChangeDate (savings/config/config.go)
	IsTxnAllowedResponse_MIN_KYC_MAX_CREDIT_LIMIT_CHECK_FAILURE_FOR_NEW_USER IsTxnAllowedResponse_Reason = 5
)

// Enum value maps for IsTxnAllowedResponse_Reason.
var (
	IsTxnAllowedResponse_Reason_name = map[int32]string{
		0: "REASON_UNSPECIFIED",
		1: "MIN_KYC_ACCOUNT_DURATION_CHECK_FAILURE",
		2: "MIN_KYC_MAX_BALANCE_CHECK_FAILURE",
		3: "MIN_KYC_MAX_CREDIT_LIMIT_CHECK_FAILURE",
		4: "MIN_KYC_MAX_BALANCE_CHECK_FAILURE_FOR_NEW_USER",
		5: "MIN_KYC_MAX_CREDIT_LIMIT_CHECK_FAILURE_FOR_NEW_USER",
	}
	IsTxnAllowedResponse_Reason_value = map[string]int32{
		"REASON_UNSPECIFIED":                                  0,
		"MIN_KYC_ACCOUNT_DURATION_CHECK_FAILURE":              1,
		"MIN_KYC_MAX_BALANCE_CHECK_FAILURE":                   2,
		"MIN_KYC_MAX_CREDIT_LIMIT_CHECK_FAILURE":              3,
		"MIN_KYC_MAX_BALANCE_CHECK_FAILURE_FOR_NEW_USER":      4,
		"MIN_KYC_MAX_CREDIT_LIMIT_CHECK_FAILURE_FOR_NEW_USER": 5,
	}
)

func (x IsTxnAllowedResponse_Reason) Enum() *IsTxnAllowedResponse_Reason {
	p := new(IsTxnAllowedResponse_Reason)
	*p = x
	return p
}

func (x IsTxnAllowedResponse_Reason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IsTxnAllowedResponse_Reason) Descriptor() protoreflect.EnumDescriptor {
	return file_api_savings_service_proto_enumTypes[19].Descriptor()
}

func (IsTxnAllowedResponse_Reason) Type() protoreflect.EnumType {
	return &file_api_savings_service_proto_enumTypes[19]
}

func (x IsTxnAllowedResponse_Reason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IsTxnAllowedResponse_Reason.Descriptor instead.
func (IsTxnAllowedResponse_Reason) EnumDescriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{35, 1}
}

type UpdateSaClosureRequestStatusResponse_Status int32

const (
	// rpc successful
	UpdateSaClosureRequestStatusResponse_OK UpdateSaClosureRequestStatusResponse_Status = 0
	// Internal error while processing the request
	UpdateSaClosureRequestStatusResponse_INTERNAL                           UpdateSaClosureRequestStatusResponse_Status = 13
	UpdateSaClosureRequestStatusResponse_INVALID_STATUS_UPDATE              UpdateSaClosureRequestStatusResponse_Status = 101
	UpdateSaClosureRequestStatusResponse_TERMINAL_STATUS_UPDATE_NOT_ALLOWED UpdateSaClosureRequestStatusResponse_Status = 102
	// when from status and to status is same
	UpdateSaClosureRequestStatusResponse_DUPLICATE_STATUS_UPDATE_NOT_ALLOWED UpdateSaClosureRequestStatusResponse_Status = 103
)

// Enum value maps for UpdateSaClosureRequestStatusResponse_Status.
var (
	UpdateSaClosureRequestStatusResponse_Status_name = map[int32]string{
		0:   "OK",
		13:  "INTERNAL",
		101: "INVALID_STATUS_UPDATE",
		102: "TERMINAL_STATUS_UPDATE_NOT_ALLOWED",
		103: "DUPLICATE_STATUS_UPDATE_NOT_ALLOWED",
	}
	UpdateSaClosureRequestStatusResponse_Status_value = map[string]int32{
		"OK":                                  0,
		"INTERNAL":                            13,
		"INVALID_STATUS_UPDATE":               101,
		"TERMINAL_STATUS_UPDATE_NOT_ALLOWED":  102,
		"DUPLICATE_STATUS_UPDATE_NOT_ALLOWED": 103,
	}
)

func (x UpdateSaClosureRequestStatusResponse_Status) Enum() *UpdateSaClosureRequestStatusResponse_Status {
	p := new(UpdateSaClosureRequestStatusResponse_Status)
	*p = x
	return p
}

func (x UpdateSaClosureRequestStatusResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpdateSaClosureRequestStatusResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_savings_service_proto_enumTypes[20].Descriptor()
}

func (UpdateSaClosureRequestStatusResponse_Status) Type() protoreflect.EnumType {
	return &file_api_savings_service_proto_enumTypes[20]
}

func (x UpdateSaClosureRequestStatusResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpdateSaClosureRequestStatusResponse_Status.Descriptor instead.
func (UpdateSaClosureRequestStatusResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{59, 0}
}

type VerifyPanForAccountClosureResponse_Status int32

const (
	VerifyPanForAccountClosureResponse_OK               VerifyPanForAccountClosureResponse_Status = 0
	VerifyPanForAccountClosureResponse_INVALID_ARGUMENT VerifyPanForAccountClosureResponse_Status = 3
	VerifyPanForAccountClosureResponse_INTERNAL         VerifyPanForAccountClosureResponse_Status = 13
	// PAN is not correct
	VerifyPanForAccountClosureResponse_PAN_INCORRECT VerifyPanForAccountClosureResponse_Status = 101
	// Verification is locked temporarily
	// There have been too many incorrect attempts
	// Wait for some time and try again
	VerifyPanForAccountClosureResponse_PAN_INCORRECT_LOCKED VerifyPanForAccountClosureResponse_Status = 102
)

// Enum value maps for VerifyPanForAccountClosureResponse_Status.
var (
	VerifyPanForAccountClosureResponse_Status_name = map[int32]string{
		0:   "OK",
		3:   "INVALID_ARGUMENT",
		13:  "INTERNAL",
		101: "PAN_INCORRECT",
		102: "PAN_INCORRECT_LOCKED",
	}
	VerifyPanForAccountClosureResponse_Status_value = map[string]int32{
		"OK":                   0,
		"INVALID_ARGUMENT":     3,
		"INTERNAL":             13,
		"PAN_INCORRECT":        101,
		"PAN_INCORRECT_LOCKED": 102,
	}
)

func (x VerifyPanForAccountClosureResponse_Status) Enum() *VerifyPanForAccountClosureResponse_Status {
	p := new(VerifyPanForAccountClosureResponse_Status)
	*p = x
	return p
}

func (x VerifyPanForAccountClosureResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VerifyPanForAccountClosureResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_savings_service_proto_enumTypes[21].Descriptor()
}

func (VerifyPanForAccountClosureResponse_Status) Type() protoreflect.EnumType {
	return &file_api_savings_service_proto_enumTypes[21]
}

func (x VerifyPanForAccountClosureResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VerifyPanForAccountClosureResponse_Status.Descriptor instead.
func (VerifyPanForAccountClosureResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{67, 0}
}

type UpdateSavingsAccountNomineesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor id for whom nominee needs to be updated
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// nominee details
	NomineeDetails *NomineeDetails `protobuf:"bytes,2,opt,name=nominee_details,json=nomineeDetails,proto3" json:"nominee_details,omitempty"`
}

func (x *UpdateSavingsAccountNomineesRequest) Reset() {
	*x = UpdateSavingsAccountNomineesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSavingsAccountNomineesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSavingsAccountNomineesRequest) ProtoMessage() {}

func (x *UpdateSavingsAccountNomineesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSavingsAccountNomineesRequest.ProtoReflect.Descriptor instead.
func (*UpdateSavingsAccountNomineesRequest) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{0}
}

func (x *UpdateSavingsAccountNomineesRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *UpdateSavingsAccountNomineesRequest) GetNomineeDetails() *NomineeDetails {
	if x != nil {
		return x.NomineeDetails
	}
	return nil
}

type UpdateSavingsAccountNomineesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpdateSavingsAccountNomineesResponse) Reset() {
	*x = UpdateSavingsAccountNomineesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSavingsAccountNomineesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSavingsAccountNomineesResponse) ProtoMessage() {}

func (x *UpdateSavingsAccountNomineesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSavingsAccountNomineesResponse.ProtoReflect.Descriptor instead.
func (*UpdateSavingsAccountNomineesResponse) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{1}
}

func (x *UpdateSavingsAccountNomineesResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetSavingsAccountNomineesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Savings account id for which nominees are to be fetched
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GetSavingsAccountNomineesRequest) Reset() {
	*x = GetSavingsAccountNomineesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSavingsAccountNomineesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSavingsAccountNomineesRequest) ProtoMessage() {}

func (x *GetSavingsAccountNomineesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSavingsAccountNomineesRequest.ProtoReflect.Descriptor instead.
func (*GetSavingsAccountNomineesRequest) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetSavingsAccountNomineesRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type GetSavingsAccountNomineesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// nominee details
	// deprecated: in favour of SavingsAccountNominee
	//
	// Deprecated: Marked as deprecated in api/savings/service.proto.
	Nominee []*typesv2.Nominee `protobuf:"bytes,2,rep,name=nominee,proto3" json:"nominee,omitempty"`
	// savings account nominee details ( wrapper around api.typesv2.Nominee to contain
	// savings account nominees specific details e.g. percentage_share )
	SavingsAccountNominees []*SavingsAccountNominee `protobuf:"bytes,3,rep,name=savings_account_nominees,json=savingsAccountNominees,proto3" json:"savings_account_nominees,omitempty"`
}

func (x *GetSavingsAccountNomineesResponse) Reset() {
	*x = GetSavingsAccountNomineesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSavingsAccountNomineesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSavingsAccountNomineesResponse) ProtoMessage() {}

func (x *GetSavingsAccountNomineesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSavingsAccountNomineesResponse.ProtoReflect.Descriptor instead.
func (*GetSavingsAccountNomineesResponse) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetSavingsAccountNomineesResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

// Deprecated: Marked as deprecated in api/savings/service.proto.
func (x *GetSavingsAccountNomineesResponse) GetNominee() []*typesv2.Nominee {
	if x != nil {
		return x.Nominee
	}
	return nil
}

func (x *GetSavingsAccountNomineesResponse) GetSavingsAccountNominees() []*SavingsAccountNominee {
	if x != nil {
		return x.SavingsAccountNominees
	}
	return nil
}

type SavingsAccountNominee struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Nominee Information
	Nominee *typesv2.Nominee `protobuf:"bytes,1,opt,name=nominee,proto3" json:"nominee,omitempty"`
	// Share Percentage of the nominee. Value ranges from 0-100.
	PercentageShare uint32 `protobuf:"varint,2,opt,name=percentage_share,json=percentageShare,proto3" json:"percentage_share,omitempty"`
}

func (x *SavingsAccountNominee) Reset() {
	*x = SavingsAccountNominee{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SavingsAccountNominee) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SavingsAccountNominee) ProtoMessage() {}

func (x *SavingsAccountNominee) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SavingsAccountNominee.ProtoReflect.Descriptor instead.
func (*SavingsAccountNominee) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{4}
}

func (x *SavingsAccountNominee) GetNominee() *typesv2.Nominee {
	if x != nil {
		return x.Nominee
	}
	return nil
}

func (x *SavingsAccountNominee) GetPercentageShare() uint32 {
	if x != nil {
		return x.PercentageShare
	}
	return 0
}

type UpdateBalanceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor corresponding to whom, saving account needs to be updated
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// timestamp at which savings account was updated the last time
	PrevUpdatedAt *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=prev_updated_at,json=prevUpdatedAt,proto3" json:"prev_updated_at,omitempty"`
}

func (x *UpdateBalanceRequest) Reset() {
	*x = UpdateBalanceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBalanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBalanceRequest) ProtoMessage() {}

func (x *UpdateBalanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBalanceRequest.ProtoReflect.Descriptor instead.
func (*UpdateBalanceRequest) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateBalanceRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *UpdateBalanceRequest) GetPrevUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.PrevUpdatedAt
	}
	return nil
}

type UpdateBalanceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpdateBalanceResponse) Reset() {
	*x = UpdateBalanceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBalanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBalanceResponse) ProtoMessage() {}

func (x *UpdateBalanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBalanceResponse.ProtoReflect.Descriptor instead.
func (*UpdateBalanceResponse) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateBalanceResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetAccountBalanceV1Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Identifier:
	//
	//	*GetAccountBalanceV1Request_Id
	//	*GetAccountBalanceV1Request_ExternalId
	Identifier isGetAccountBalanceV1Request_Identifier `protobuf_oneof:"identifier"`
	// actor to whom the account belongs to.
	ActorId string `protobuf:"bytes,3,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// enum used to give options to client to decide on how much stale response is acceptable
	DataFreshness GetAccountBalanceV1Request_DataFreshness `protobuf:"varint,4,opt,name=data_freshness,json=dataFreshness,proto3,enum=savings.GetAccountBalanceV1Request_DataFreshness" json:"data_freshness,omitempty"`
	// enum used to give options to client to decide whether to use new vendor api response directly.
	// Client should use this option to explicitly invoke newer version of vendor's balance api call.
	VendorApiOption GetAccountBalanceV1Request_VendorApiOption `protobuf:"varint,5,opt,name=vendor_api_option,json=vendorApiOption,proto3,enum=savings.GetAccountBalanceV1Request_VendorApiOption" json:"vendor_api_option,omitempty"`
}

func (x *GetAccountBalanceV1Request) Reset() {
	*x = GetAccountBalanceV1Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountBalanceV1Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountBalanceV1Request) ProtoMessage() {}

func (x *GetAccountBalanceV1Request) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountBalanceV1Request.ProtoReflect.Descriptor instead.
func (*GetAccountBalanceV1Request) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{7}
}

func (m *GetAccountBalanceV1Request) GetIdentifier() isGetAccountBalanceV1Request_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *GetAccountBalanceV1Request) GetId() string {
	if x, ok := x.GetIdentifier().(*GetAccountBalanceV1Request_Id); ok {
		return x.Id
	}
	return ""
}

func (x *GetAccountBalanceV1Request) GetExternalId() *BankAccountIdentifier {
	if x, ok := x.GetIdentifier().(*GetAccountBalanceV1Request_ExternalId); ok {
		return x.ExternalId
	}
	return nil
}

func (x *GetAccountBalanceV1Request) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetAccountBalanceV1Request) GetDataFreshness() GetAccountBalanceV1Request_DataFreshness {
	if x != nil {
		return x.DataFreshness
	}
	return GetAccountBalanceV1Request_DATA_FRESHNESS_UNSPECIFIED
}

func (x *GetAccountBalanceV1Request) GetVendorApiOption() GetAccountBalanceV1Request_VendorApiOption {
	if x != nil {
		return x.VendorApiOption
	}
	return GetAccountBalanceV1Request_VENDOR_API_OPTION_UNSPECIFIED
}

type isGetAccountBalanceV1Request_Identifier interface {
	isGetAccountBalanceV1Request_Identifier()
}

type GetAccountBalanceV1Request_Id struct {
	// Internal account id.
	Id string `protobuf:"bytes,1,opt,name=id,proto3,oneof"`
}

type GetAccountBalanceV1Request_ExternalId struct {
	// External account id, contains identifier provided by banking partner.
	ExternalId *BankAccountIdentifier `protobuf:"bytes,2,opt,name=external_id,json=externalId,proto3,oneof"`
}

func (*GetAccountBalanceV1Request_Id) isGetAccountBalanceV1Request_Identifier() {}

func (*GetAccountBalanceV1Request_ExternalId) isGetAccountBalanceV1Request_Identifier() {}

type GetAccountBalanceV1Response struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Ledger balance of the account (Opening balance on the given day)
	LedgerBalance *money.Money `protobuf:"bytes,2,opt,name=ledger_balance,json=ledgerBalance,proto3" json:"ledger_balance,omitempty"`
	// Contains latest balance of an account.
	AvailableBalance *money.Money `protobuf:"bytes,3,opt,name=available_balance,json=availableBalance,proto3" json:"available_balance,omitempty"`
	// Optional: Clearance balance refers to the amount which is in clearance state. This typically happens for cheques.
	ClearanceBalance *money.Money `protobuf:"bytes,4,opt,name=clearance_balance,json=clearanceBalance,proto3" json:"clearance_balance,omitempty"`
	// Optional: Lien balance is the amount which the bank has put a hold on. Can happen for failed payments
	LienBalance *money.Money `protobuf:"bytes,6,opt,name=lien_balance,json=lienBalance,proto3" json:"lien_balance,omitempty"`
	// Timestamp for which the given balance was calculated
	BalanceAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=balance_at,json=balanceAt,proto3" json:"balance_at,omitempty"`
	// Optional:  There can be freeze conditions with respect to an account, freeze status gives a standard code about it
	FreezeRawCode string `protobuf:"bytes,9,opt,name=freeze_raw_code,json=freezeRawCode,proto3" json:"freeze_raw_code,omitempty"`
	// Optional:  Reason for the freeze status
	FreezeReason string `protobuf:"bytes,10,opt,name=freeze_reason,json=freezeReason,proto3" json:"freeze_reason,omitempty"`
}

func (x *GetAccountBalanceV1Response) Reset() {
	*x = GetAccountBalanceV1Response{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountBalanceV1Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountBalanceV1Response) ProtoMessage() {}

func (x *GetAccountBalanceV1Response) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountBalanceV1Response.ProtoReflect.Descriptor instead.
func (*GetAccountBalanceV1Response) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetAccountBalanceV1Response) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetAccountBalanceV1Response) GetLedgerBalance() *money.Money {
	if x != nil {
		return x.LedgerBalance
	}
	return nil
}

func (x *GetAccountBalanceV1Response) GetAvailableBalance() *money.Money {
	if x != nil {
		return x.AvailableBalance
	}
	return nil
}

func (x *GetAccountBalanceV1Response) GetClearanceBalance() *money.Money {
	if x != nil {
		return x.ClearanceBalance
	}
	return nil
}

func (x *GetAccountBalanceV1Response) GetLienBalance() *money.Money {
	if x != nil {
		return x.LienBalance
	}
	return nil
}

func (x *GetAccountBalanceV1Response) GetBalanceAt() *timestamppb.Timestamp {
	if x != nil {
		return x.BalanceAt
	}
	return nil
}

func (x *GetAccountBalanceV1Response) GetFreezeRawCode() string {
	if x != nil {
		return x.FreezeRawCode
	}
	return ""
}

func (x *GetAccountBalanceV1Response) GetFreezeReason() string {
	if x != nil {
		return x.FreezeReason
	}
	return ""
}

// Request message for creating a new account in the database.
type CreateAccountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// User Id of the user for whom account need to be created.
	PrimaryAccountHolderId string `protobuf:"bytes,1,opt,name=primary_account_holder_id,json=primaryAccountHolderId,proto3" json:"primary_account_holder_id,omitempty"`
	// Request Id to be passed to create account at vendor's end
	ReqId string `protobuf:"bytes,2,opt,name=req_id,json=reqId,proto3" json:"req_id,omitempty"`
	// Parameter to force retry account creation. This parameter is to be used only in special failure scenarios,
	// where we want to redo account creation post manual fixes. Parameter to be set true either through script/sherlock.
	ForceRetry CreateAccountRequest_ForceRetryOption `protobuf:"varint,3,opt,name=force_retry,json=forceRetry,proto3,enum=savings.CreateAccountRequest_ForceRetryOption" json:"force_retry,omitempty"`
	// Savings account creation request made to the partner bank will be of this account type.
	// Pass only for NRE and NRO account api.typesv2. For rest of the accounts, SKU is evaluated internally.
	// should_open_amb_account flag is consumed for evaluating SKU for min AMB accounts.
	Sku     SKU                           `protobuf:"varint,4,opt,name=sku,proto3,enum=savings.SKU" json:"sku,omitempty"`
	ActorId string                        `protobuf:"bytes,5,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	Options *CreateAccountRequest_Options `protobuf:"bytes,6,opt,name=options,proto3" json:"options,omitempty"`
}

func (x *CreateAccountRequest) Reset() {
	*x = CreateAccountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAccountRequest) ProtoMessage() {}

func (x *CreateAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAccountRequest.ProtoReflect.Descriptor instead.
func (*CreateAccountRequest) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{9}
}

func (x *CreateAccountRequest) GetPrimaryAccountHolderId() string {
	if x != nil {
		return x.PrimaryAccountHolderId
	}
	return ""
}

func (x *CreateAccountRequest) GetReqId() string {
	if x != nil {
		return x.ReqId
	}
	return ""
}

func (x *CreateAccountRequest) GetForceRetry() CreateAccountRequest_ForceRetryOption {
	if x != nil {
		return x.ForceRetry
	}
	return CreateAccountRequest_UNSPECIFIED
}

func (x *CreateAccountRequest) GetSku() SKU {
	if x != nil {
		return x.Sku
	}
	return SKU_ACCOUNT_SKU_UNSPECIFIED
}

func (x *CreateAccountRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *CreateAccountRequest) GetOptions() *CreateAccountRequest_Options {
	if x != nil {
		return x.Options
	}
	return nil
}

// Response message for creating a new account. Contains the newly created account.
type CreateAccountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Account that was written to the database. Contains the `id` field populated.
	Account *Account `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	// RPC status to return when a call is made
	Status *rpc.Status `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *CreateAccountResponse) Reset() {
	*x = CreateAccountResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAccountResponse) ProtoMessage() {}

func (x *CreateAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAccountResponse.ProtoReflect.Descriptor instead.
func (*CreateAccountResponse) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{10}
}

func (x *CreateAccountResponse) GetAccount() *Account {
	if x != nil {
		return x.Account
	}
	return nil
}

func (x *CreateAccountResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

// A struct to uniquely identify account using identifiers provided by banking partner.
type BankAccountIdentifier struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Account number provided by bank.
	AccountNo string `protobuf:"bytes,1,opt,name=account_no,json=accountNo,proto3" json:"account_no,omitempty"`
	// IFSC code belonging to the account.
	IfscCode string `protobuf:"bytes,2,opt,name=ifsc_code,json=ifscCode,proto3" json:"ifsc_code,omitempty"`
}

func (x *BankAccountIdentifier) Reset() {
	*x = BankAccountIdentifier{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BankAccountIdentifier) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BankAccountIdentifier) ProtoMessage() {}

func (x *BankAccountIdentifier) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BankAccountIdentifier.ProtoReflect.Descriptor instead.
func (*BankAccountIdentifier) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{11}
}

func (x *BankAccountIdentifier) GetAccountNo() string {
	if x != nil {
		return x.AccountNo
	}
	return ""
}

func (x *BankAccountIdentifier) GetIfscCode() string {
	if x != nil {
		return x.IfscCode
	}
	return ""
}

// Request message for reading an account.
type GetAccountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Identifier:
	//
	//	*GetAccountRequest_Id
	//	*GetAccountRequest_ExternalId
	//	*GetAccountRequest_PrimaryUserId
	//	*GetAccountRequest_ActorId
	//	*GetAccountRequest_AccountNumBankFilter
	//	*GetAccountRequest_ActorUniqueAccountIdentifier
	Identifier isGetAccountRequest_Identifier `protobuf_oneof:"identifier"`
}

func (x *GetAccountRequest) Reset() {
	*x = GetAccountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountRequest) ProtoMessage() {}

func (x *GetAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountRequest.ProtoReflect.Descriptor instead.
func (*GetAccountRequest) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{12}
}

func (m *GetAccountRequest) GetIdentifier() isGetAccountRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *GetAccountRequest) GetId() string {
	if x, ok := x.GetIdentifier().(*GetAccountRequest_Id); ok {
		return x.Id
	}
	return ""
}

// Deprecated: Marked as deprecated in api/savings/service.proto.
func (x *GetAccountRequest) GetExternalId() *BankAccountIdentifier {
	if x, ok := x.GetIdentifier().(*GetAccountRequest_ExternalId); ok {
		return x.ExternalId
	}
	return nil
}

// Deprecated: Marked as deprecated in api/savings/service.proto.
func (x *GetAccountRequest) GetPrimaryUserId() string {
	if x, ok := x.GetIdentifier().(*GetAccountRequest_PrimaryUserId); ok {
		return x.PrimaryUserId
	}
	return ""
}

// Deprecated: Marked as deprecated in api/savings/service.proto.
func (x *GetAccountRequest) GetActorId() string {
	if x, ok := x.GetIdentifier().(*GetAccountRequest_ActorId); ok {
		return x.ActorId
	}
	return ""
}

func (x *GetAccountRequest) GetAccountNumBankFilter() *AccountNumberBankFilter {
	if x, ok := x.GetIdentifier().(*GetAccountRequest_AccountNumBankFilter); ok {
		return x.AccountNumBankFilter
	}
	return nil
}

func (x *GetAccountRequest) GetActorUniqueAccountIdentifier() *ActorUniqueAccountIdentifier {
	if x, ok := x.GetIdentifier().(*GetAccountRequest_ActorUniqueAccountIdentifier); ok {
		return x.ActorUniqueAccountIdentifier
	}
	return nil
}

type isGetAccountRequest_Identifier interface {
	isGetAccountRequest_Identifier()
}

type GetAccountRequest_Id struct {
	// Internal account id.
	Id string `protobuf:"bytes,1,opt,name=id,proto3,oneof"`
}

type GetAccountRequest_ExternalId struct {
	// External account id, contains identifier provided by banking partner.
	// deprecated: use AccountNumberBankFilter instead
	//
	// Deprecated: Marked as deprecated in api/savings/service.proto.
	ExternalId *BankAccountIdentifier `protobuf:"bytes,2,opt,name=external_id,json=externalId,proto3,oneof"`
}

type GetAccountRequest_PrimaryUserId struct {
	// Id of the primary account holder
	// deprecated : use ActorUniqueAccountIdentifier instead
	//
	// Deprecated: Marked as deprecated in api/savings/service.proto.
	PrimaryUserId string `protobuf:"bytes,3,opt,name=primary_user_id,json=primaryUserId,proto3,oneof"`
}

type GetAccountRequest_ActorId struct {
	// deprecated : use ActorUniqueAccountIdentifier instead
	//
	// Deprecated: Marked as deprecated in api/savings/service.proto.
	ActorId string `protobuf:"bytes,4,opt,name=actor_id,json=actorId,proto3,oneof"`
}

type GetAccountRequest_AccountNumBankFilter struct {
	AccountNumBankFilter *AccountNumberBankFilter `protobuf:"bytes,5,opt,name=account_num_bank_filter,json=accountNumBankFilter,proto3,oneof"`
}

type GetAccountRequest_ActorUniqueAccountIdentifier struct {
	// Identifier to uniquely determine the account using actor id, APO and Vendor
	ActorUniqueAccountIdentifier *ActorUniqueAccountIdentifier `protobuf:"bytes,6,opt,name=actor_unique_account_identifier,json=actorUniqueAccountIdentifier,proto3,oneof"`
}

func (*GetAccountRequest_Id) isGetAccountRequest_Identifier() {}

func (*GetAccountRequest_ExternalId) isGetAccountRequest_Identifier() {}

func (*GetAccountRequest_PrimaryUserId) isGetAccountRequest_Identifier() {}

func (*GetAccountRequest_ActorId) isGetAccountRequest_Identifier() {}

func (*GetAccountRequest_AccountNumBankFilter) isGetAccountRequest_Identifier() {}

func (*GetAccountRequest_ActorUniqueAccountIdentifier) isGetAccountRequest_Identifier() {}

// Response message for reading an account. Contains the requested account.
type GetAccountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account *Account    `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	Status  *rpc.Status `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *GetAccountResponse) Reset() {
	*x = GetAccountResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountResponse) ProtoMessage() {}

func (x *GetAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountResponse.ProtoReflect.Descriptor instead.
func (*GetAccountResponse) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetAccountResponse) GetAccount() *Account {
	if x != nil {
		return x.Account
	}
	return nil
}

func (x *GetAccountResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetAccountsListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// identifier to be used for making RPC call
	//
	// Types that are assignable to Identifier:
	//
	//	*GetAccountsListRequest_UserIds
	//	*GetAccountsListRequest_BulkActorIdentifier
	Identifier isGetAccountsListRequest_Identifier `protobuf_oneof:"identifier"`
}

func (x *GetAccountsListRequest) Reset() {
	*x = GetAccountsListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountsListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountsListRequest) ProtoMessage() {}

func (x *GetAccountsListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountsListRequest.ProtoReflect.Descriptor instead.
func (*GetAccountsListRequest) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{14}
}

func (m *GetAccountsListRequest) GetIdentifier() isGetAccountsListRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

// Deprecated: Marked as deprecated in api/savings/service.proto.
func (x *GetAccountsListRequest) GetUserIds() *PrimaryUserIdentifier {
	if x, ok := x.GetIdentifier().(*GetAccountsListRequest_UserIds); ok {
		return x.UserIds
	}
	return nil
}

func (x *GetAccountsListRequest) GetBulkActorIdentifier() *BulkActorIdentifier {
	if x, ok := x.GetIdentifier().(*GetAccountsListRequest_BulkActorIdentifier); ok {
		return x.BulkActorIdentifier
	}
	return nil
}

type isGetAccountsListRequest_Identifier interface {
	isGetAccountsListRequest_Identifier()
}

type GetAccountsListRequest_UserIds struct {
	// list of primary_user_id to get respective list of account
	// deprecated in favour of BulkActorIdentifier
	//
	// Deprecated: Marked as deprecated in api/savings/service.proto.
	UserIds *PrimaryUserIdentifier `protobuf:"bytes,1,opt,name=user_ids,json=userIds,proto3,oneof"`
}

type GetAccountsListRequest_BulkActorIdentifier struct {
	// actor id list and APO list to get respective list of account
	BulkActorIdentifier *BulkActorIdentifier `protobuf:"bytes,2,opt,name=bulk_actor_identifier,json=bulkActorIdentifier,proto3,oneof"`
}

func (*GetAccountsListRequest_UserIds) isGetAccountsListRequest_Identifier() {}

func (*GetAccountsListRequest_BulkActorIdentifier) isGetAccountsListRequest_Identifier() {}

type GetAccountsListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// RPC status to return when a call is made
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// list of accounts for given identifier
	Accounts []*Account `protobuf:"bytes,2,rep,name=accounts,proto3" json:"accounts,omitempty"`
}

func (x *GetAccountsListResponse) Reset() {
	*x = GetAccountsListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountsListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountsListResponse) ProtoMessage() {}

func (x *GetAccountsListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountsListResponse.ProtoReflect.Descriptor instead.
func (*GetAccountsListResponse) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{15}
}

func (x *GetAccountsListResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetAccountsListResponse) GetAccounts() []*Account {
	if x != nil {
		return x.Accounts
	}
	return nil
}

type PrimaryUserIdentifier struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PrimaryUserIds []string `protobuf:"bytes,2,rep,name=primary_user_ids,json=primaryUserIds,proto3" json:"primary_user_ids,omitempty"`
	// Request param to control which account (Regular, NRE or NRO) types to return
	// To maintain backward compatibility, the rpc returns Regular savings account incase when Account Product Offering list is empty
	AccountProductOfferingList []account.AccountProductOffering `protobuf:"varint,3,rep,packed,name=account_product_offering_list,json=accountProductOfferingList,proto3,enum=api.typesv2.account.AccountProductOffering" json:"account_product_offering_list,omitempty"`
}

func (x *PrimaryUserIdentifier) Reset() {
	*x = PrimaryUserIdentifier{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PrimaryUserIdentifier) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrimaryUserIdentifier) ProtoMessage() {}

func (x *PrimaryUserIdentifier) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrimaryUserIdentifier.ProtoReflect.Descriptor instead.
func (*PrimaryUserIdentifier) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{16}
}

func (x *PrimaryUserIdentifier) GetPrimaryUserIds() []string {
	if x != nil {
		return x.PrimaryUserIds
	}
	return nil
}

func (x *PrimaryUserIdentifier) GetAccountProductOfferingList() []account.AccountProductOffering {
	if x != nil {
		return x.AccountProductOfferingList
	}
	return nil
}

type BulkActorIdentifier struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorIds []string `protobuf:"bytes,1,rep,name=actor_ids,json=actorIds,proto3" json:"actor_ids,omitempty"`
	// Request param to control which account (Regular, NRE or NRO) types to return
	// Returns error if Account Product Offering list is empty or contains Unspecified type
	AccountProductOfferings []account.AccountProductOffering `protobuf:"varint,2,rep,packed,name=account_product_offerings,json=accountProductOfferings,proto3,enum=api.typesv2.account.AccountProductOffering" json:"account_product_offerings,omitempty"`
	// Request param to control which Partner Bank accounts to return
	// Returns error if Partner Bank Account list is empty or contains Unspecified type
	PartnerBanks []vendorgateway.Vendor `protobuf:"varint,3,rep,packed,name=partner_banks,json=partnerBanks,proto3,enum=vendorgateway.Vendor" json:"partner_banks,omitempty"`
}

func (x *BulkActorIdentifier) Reset() {
	*x = BulkActorIdentifier{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BulkActorIdentifier) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkActorIdentifier) ProtoMessage() {}

func (x *BulkActorIdentifier) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkActorIdentifier.ProtoReflect.Descriptor instead.
func (*BulkActorIdentifier) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{17}
}

func (x *BulkActorIdentifier) GetActorIds() []string {
	if x != nil {
		return x.ActorIds
	}
	return nil
}

func (x *BulkActorIdentifier) GetAccountProductOfferings() []account.AccountProductOffering {
	if x != nil {
		return x.AccountProductOfferings
	}
	return nil
}

func (x *BulkActorIdentifier) GetPartnerBanks() []vendorgateway.Vendor {
	if x != nil {
		return x.PartnerBanks
	}
	return nil
}

type ActorUniqueAccountIdentifier struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Request param to control which account (Regular, NRE or NRO) type to return
	// Returns error if Account Product Offering is unspecified
	AccountProductOffering account.AccountProductOffering `protobuf:"varint,2,opt,name=account_product_offering,json=accountProductOffering,proto3,enum=api.typesv2.account.AccountProductOffering" json:"account_product_offering,omitempty"`
	// Request param to control which Partner Bank account to return
	// Returns error if Partner Bank is unspecified
	PartnerBank vendorgateway.Vendor `protobuf:"varint,3,opt,name=partner_bank,json=partnerBank,proto3,enum=vendorgateway.Vendor" json:"partner_bank,omitempty"`
}

func (x *ActorUniqueAccountIdentifier) Reset() {
	*x = ActorUniqueAccountIdentifier{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActorUniqueAccountIdentifier) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActorUniqueAccountIdentifier) ProtoMessage() {}

func (x *ActorUniqueAccountIdentifier) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActorUniqueAccountIdentifier.ProtoReflect.Descriptor instead.
func (*ActorUniqueAccountIdentifier) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{18}
}

func (x *ActorUniqueAccountIdentifier) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *ActorUniqueAccountIdentifier) GetAccountProductOffering() account.AccountProductOffering {
	if x != nil {
		return x.AccountProductOffering
	}
	return account.AccountProductOffering(0)
}

func (x *ActorUniqueAccountIdentifier) GetPartnerBank() vendorgateway.Vendor {
	if x != nil {
		return x.PartnerBank
	}
	return vendorgateway.Vendor(0)
}

// Request message for fetching account balance.
type GetAccountBalanceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Identifier:
	//
	//	*GetAccountBalanceRequest_Id
	//	*GetAccountBalanceRequest_ExternalId
	Identifier isGetAccountBalanceRequest_Identifier `protobuf_oneof:"identifier"`
	// actor to whom the account belongs to.
	ActorId string `protobuf:"bytes,3,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// enum used to give options to client to decide on how much stale response is acceptable
	DataFreshness GetAccountBalanceRequest_DataFreshness `protobuf:"varint,4,opt,name=data_freshness,json=dataFreshness,proto3,enum=savings.GetAccountBalanceRequest_DataFreshness" json:"data_freshness,omitempty"`
}

func (x *GetAccountBalanceRequest) Reset() {
	*x = GetAccountBalanceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountBalanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountBalanceRequest) ProtoMessage() {}

func (x *GetAccountBalanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountBalanceRequest.ProtoReflect.Descriptor instead.
func (*GetAccountBalanceRequest) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{19}
}

func (m *GetAccountBalanceRequest) GetIdentifier() isGetAccountBalanceRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *GetAccountBalanceRequest) GetId() string {
	if x, ok := x.GetIdentifier().(*GetAccountBalanceRequest_Id); ok {
		return x.Id
	}
	return ""
}

func (x *GetAccountBalanceRequest) GetExternalId() *BankAccountIdentifier {
	if x, ok := x.GetIdentifier().(*GetAccountBalanceRequest_ExternalId); ok {
		return x.ExternalId
	}
	return nil
}

func (x *GetAccountBalanceRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetAccountBalanceRequest) GetDataFreshness() GetAccountBalanceRequest_DataFreshness {
	if x != nil {
		return x.DataFreshness
	}
	return GetAccountBalanceRequest_DATA_FRESHNESS_UNSPECIFIED
}

type isGetAccountBalanceRequest_Identifier interface {
	isGetAccountBalanceRequest_Identifier()
}

type GetAccountBalanceRequest_Id struct {
	// Internal account id.
	Id string `protobuf:"bytes,1,opt,name=id,proto3,oneof"`
}

type GetAccountBalanceRequest_ExternalId struct {
	// External account id, contains identifier provided by banking partner.
	ExternalId *BankAccountIdentifier `protobuf:"bytes,2,opt,name=external_id,json=externalId,proto3,oneof"`
}

func (*GetAccountBalanceRequest_Id) isGetAccountBalanceRequest_Identifier() {}

func (*GetAccountBalanceRequest_ExternalId) isGetAccountBalanceRequest_Identifier() {}

// Response message for fetching account balance.
type GetAccountBalanceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Contains latest balance of an account.
	AvailableBalance *money.Money `protobuf:"bytes,2,opt,name=available_balance,json=availableBalance,proto3" json:"available_balance,omitempty"`
	// Ledger balance of the account (Opening balance on the given day)
	LedgerBalance *money.Money `protobuf:"bytes,3,opt,name=ledger_balance,json=ledgerBalance,proto3" json:"ledger_balance,omitempty"`
	// Timestamp when this balance was last updated
	LastUpdatedAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=last_updated_at,json=lastUpdatedAt,proto3" json:"last_updated_at,omitempty"`
}

func (x *GetAccountBalanceResponse) Reset() {
	*x = GetAccountBalanceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountBalanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountBalanceResponse) ProtoMessage() {}

func (x *GetAccountBalanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountBalanceResponse.ProtoReflect.Descriptor instead.
func (*GetAccountBalanceResponse) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{20}
}

func (x *GetAccountBalanceResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetAccountBalanceResponse) GetAvailableBalance() *money.Money {
	if x != nil {
		return x.AvailableBalance
	}
	return nil
}

func (x *GetAccountBalanceResponse) GetLedgerBalance() *money.Money {
	if x != nil {
		return x.LedgerBalance
	}
	return nil
}

func (x *GetAccountBalanceResponse) GetLastUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LastUpdatedAt
	}
	return nil
}

// Request message for updating various account info.
type UpdateAccountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Identifier:
	//
	//	*UpdateAccountRequest_Id
	//	*UpdateAccountRequest_ExternalId
	//	*UpdateAccountRequest_PrimaryAccountHolder
	Identifier isUpdateAccountRequest_Identifier `protobuf_oneof:"identifier"`
	// New balance to be updated in the account.
	Balance *money.Money `protobuf:"bytes,3,opt,name=balance,proto3" json:"balance,omitempty"`
	// Set of account constraints to be changed.
	Constraints *AccountConstraints `protobuf:"bytes,4,opt,name=constraints,proto3" json:"constraints,omitempty"`
	// New account status to be updated
	State State `protobuf:"varint,5,opt,name=state,proto3,enum=savings.State" json:"state,omitempty"`
	// New phone number to be updated for account
	PhoneNumber *common.PhoneNumber `protobuf:"bytes,6,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// New email id to be updated for account
	EmailId string `protobuf:"bytes,7,opt,name=email_id,json=emailId,proto3" json:"email_id,omitempty"`
	// The fields that need to be updated. This is repeated because a single update
	// call can update multiple fields. The update request ignores any fields that aren't specified in the field mask,
	// leaving them with their current values.
	UpdateMask []AccountFieldMask `protobuf:"varint,8,rep,packed,name=update_mask,json=updateMask,proto3,enum=savings.AccountFieldMask" json:"update_mask,omitempty"`
	//	sku to be updated
	//
	// Deprecated: Marked as deprecated in api/savings/service.proto.
	Sku     SKU    `protobuf:"varint,10,opt,name=sku,proto3,enum=savings.SKU" json:"sku,omitempty"`
	ActorId string `protobuf:"bytes,11,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// sku and sku info to be updated
	SkuInfo *SKUInfo `protobuf:"bytes,12,opt,name=sku_info,json=skuInfo,proto3" json:"sku_info,omitempty"`
}

func (x *UpdateAccountRequest) Reset() {
	*x = UpdateAccountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAccountRequest) ProtoMessage() {}

func (x *UpdateAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAccountRequest.ProtoReflect.Descriptor instead.
func (*UpdateAccountRequest) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{21}
}

func (m *UpdateAccountRequest) GetIdentifier() isUpdateAccountRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *UpdateAccountRequest) GetId() string {
	if x, ok := x.GetIdentifier().(*UpdateAccountRequest_Id); ok {
		return x.Id
	}
	return ""
}

func (x *UpdateAccountRequest) GetExternalId() *BankAccountIdentifier {
	if x, ok := x.GetIdentifier().(*UpdateAccountRequest_ExternalId); ok {
		return x.ExternalId
	}
	return nil
}

func (x *UpdateAccountRequest) GetPrimaryAccountHolder() string {
	if x, ok := x.GetIdentifier().(*UpdateAccountRequest_PrimaryAccountHolder); ok {
		return x.PrimaryAccountHolder
	}
	return ""
}

func (x *UpdateAccountRequest) GetBalance() *money.Money {
	if x != nil {
		return x.Balance
	}
	return nil
}

func (x *UpdateAccountRequest) GetConstraints() *AccountConstraints {
	if x != nil {
		return x.Constraints
	}
	return nil
}

func (x *UpdateAccountRequest) GetState() State {
	if x != nil {
		return x.State
	}
	return State_STATE_UNSPECIFIED
}

func (x *UpdateAccountRequest) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *UpdateAccountRequest) GetEmailId() string {
	if x != nil {
		return x.EmailId
	}
	return ""
}

func (x *UpdateAccountRequest) GetUpdateMask() []AccountFieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

// Deprecated: Marked as deprecated in api/savings/service.proto.
func (x *UpdateAccountRequest) GetSku() SKU {
	if x != nil {
		return x.Sku
	}
	return SKU_ACCOUNT_SKU_UNSPECIFIED
}

func (x *UpdateAccountRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *UpdateAccountRequest) GetSkuInfo() *SKUInfo {
	if x != nil {
		return x.SkuInfo
	}
	return nil
}

type isUpdateAccountRequest_Identifier interface {
	isUpdateAccountRequest_Identifier()
}

type UpdateAccountRequest_Id struct {
	// Internal account id.
	Id string `protobuf:"bytes,1,opt,name=id,proto3,oneof"`
}

type UpdateAccountRequest_ExternalId struct {
	// External account id, contains identifier provided by banking partner.
	ExternalId *BankAccountIdentifier `protobuf:"bytes,2,opt,name=external_id,json=externalId,proto3,oneof"`
}

type UpdateAccountRequest_PrimaryAccountHolder struct {
	// user id corresponding to the primary account holder
	PrimaryAccountHolder string `protobuf:"bytes,9,opt,name=primary_account_holder,json=primaryAccountHolder,proto3,oneof"`
}

func (*UpdateAccountRequest_Id) isUpdateAccountRequest_Identifier() {}

func (*UpdateAccountRequest_ExternalId) isUpdateAccountRequest_Identifier() {}

func (*UpdateAccountRequest_PrimaryAccountHolder) isUpdateAccountRequest_Identifier() {}

// Response message for updating account info.
type UpdateAccountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Contains latest balance of an account.
	// Contains updated account info.
	Account *Account `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
}

func (x *UpdateAccountResponse) Reset() {
	*x = UpdateAccountResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAccountResponse) ProtoMessage() {}

func (x *UpdateAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAccountResponse.ProtoReflect.Descriptor instead.
func (*UpdateAccountResponse) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{22}
}

func (x *UpdateAccountResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *UpdateAccountResponse) GetAccount() *Account {
	if x != nil {
		return x.Account
	}
	return nil
}

type GetOpeningBalanceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Identifier:
	//
	//	*GetOpeningBalanceRequest_Id
	//	*GetOpeningBalanceRequest_ExternalId
	//	*GetOpeningBalanceRequest_PrimaryAccountHolderActor
	Identifier isGetOpeningBalanceRequest_Identifier `protobuf_oneof:"identifier"`
	// date on which opening balance need to be enquired
	Date    *date.Date `protobuf:"bytes,4,opt,name=date,proto3" json:"date,omitempty"`
	ActorId string     `protobuf:"bytes,5,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GetOpeningBalanceRequest) Reset() {
	*x = GetOpeningBalanceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOpeningBalanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOpeningBalanceRequest) ProtoMessage() {}

func (x *GetOpeningBalanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOpeningBalanceRequest.ProtoReflect.Descriptor instead.
func (*GetOpeningBalanceRequest) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{23}
}

func (m *GetOpeningBalanceRequest) GetIdentifier() isGetOpeningBalanceRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *GetOpeningBalanceRequest) GetId() string {
	if x, ok := x.GetIdentifier().(*GetOpeningBalanceRequest_Id); ok {
		return x.Id
	}
	return ""
}

func (x *GetOpeningBalanceRequest) GetExternalId() *BankAccountIdentifier {
	if x, ok := x.GetIdentifier().(*GetOpeningBalanceRequest_ExternalId); ok {
		return x.ExternalId
	}
	return nil
}

func (x *GetOpeningBalanceRequest) GetPrimaryAccountHolderActor() string {
	if x, ok := x.GetIdentifier().(*GetOpeningBalanceRequest_PrimaryAccountHolderActor); ok {
		return x.PrimaryAccountHolderActor
	}
	return ""
}

func (x *GetOpeningBalanceRequest) GetDate() *date.Date {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *GetOpeningBalanceRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type isGetOpeningBalanceRequest_Identifier interface {
	isGetOpeningBalanceRequest_Identifier()
}

type GetOpeningBalanceRequest_Id struct {
	// internal account id
	Id string `protobuf:"bytes,1,opt,name=id,proto3,oneof"`
}

type GetOpeningBalanceRequest_ExternalId struct {
	// External account id, contains identifier provided by banking partner.
	// like account no and ifsc
	ExternalId *BankAccountIdentifier `protobuf:"bytes,2,opt,name=external_id,json=externalId,proto3,oneof"`
}

type GetOpeningBalanceRequest_PrimaryAccountHolderActor struct {
	// actor id corresponding to the primary account holder
	// to be passed by client if sum of opening balance corresponding
	// to all the accounts of the actor is required.
	// In case data specific to a single account is needed please use
	// `id` and `external_id`
	PrimaryAccountHolderActor string `protobuf:"bytes,3,opt,name=primary_account_holder_actor,json=primaryAccountHolderActor,proto3,oneof"`
}

func (*GetOpeningBalanceRequest_Id) isGetOpeningBalanceRequest_Identifier() {}

func (*GetOpeningBalanceRequest_ExternalId) isGetOpeningBalanceRequest_Identifier() {}

func (*GetOpeningBalanceRequest_PrimaryAccountHolderActor) isGetOpeningBalanceRequest_Identifier() {}

type GetOpeningBalanceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// opening balance corresponding to the specified request parameter
	OpeningBalance *money.Money `protobuf:"bytes,2,opt,name=opening_balance,json=openingBalance,proto3" json:"opening_balance,omitempty"`
}

func (x *GetOpeningBalanceResponse) Reset() {
	*x = GetOpeningBalanceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOpeningBalanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOpeningBalanceResponse) ProtoMessage() {}

func (x *GetOpeningBalanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOpeningBalanceResponse.ProtoReflect.Descriptor instead.
func (*GetOpeningBalanceResponse) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{24}
}

func (x *GetOpeningBalanceResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetOpeningBalanceResponse) GetOpeningBalance() *money.Money {
	if x != nil {
		return x.OpeningBalance
	}
	return nil
}

type GetAccountBalanceWithSummaryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Identifier:
	//
	//	*GetAccountBalanceWithSummaryRequest_Id
	//	*GetAccountBalanceWithSummaryRequest_ExternalId
	//	*GetAccountBalanceWithSummaryRequest_PrimaryAccountHolderActor
	Identifier isGetAccountBalanceWithSummaryRequest_Identifier `protobuf_oneof:"identifier"`
	ActorId    string                                           `protobuf:"bytes,4,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// start time from which we fetch the opening balance and txn aggregates for given account (end time is time.now())
	// currently using start time as max(account opening date ,beginning of current month)
	TimeRange GetAccountBalanceWithSummaryRequest_Range `protobuf:"varint,5,opt,name=time_range,json=timeRange,proto3,enum=savings.GetAccountBalanceWithSummaryRequest_Range" json:"time_range,omitempty"`
	// Enum to identify if the user wants to get his balance refreshed.
	ForceBalanceUpdate GetAccountBalanceWithSummaryRequest_ForceBalanceUpdate `protobuf:"varint,6,opt,name=force_balance_update,json=forceBalanceUpdate,proto3,enum=savings.GetAccountBalanceWithSummaryRequest_ForceBalanceUpdate" json:"force_balance_update,omitempty"`
}

func (x *GetAccountBalanceWithSummaryRequest) Reset() {
	*x = GetAccountBalanceWithSummaryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountBalanceWithSummaryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountBalanceWithSummaryRequest) ProtoMessage() {}

func (x *GetAccountBalanceWithSummaryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountBalanceWithSummaryRequest.ProtoReflect.Descriptor instead.
func (*GetAccountBalanceWithSummaryRequest) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{25}
}

func (m *GetAccountBalanceWithSummaryRequest) GetIdentifier() isGetAccountBalanceWithSummaryRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *GetAccountBalanceWithSummaryRequest) GetId() string {
	if x, ok := x.GetIdentifier().(*GetAccountBalanceWithSummaryRequest_Id); ok {
		return x.Id
	}
	return ""
}

func (x *GetAccountBalanceWithSummaryRequest) GetExternalId() *BankAccountIdentifier {
	if x, ok := x.GetIdentifier().(*GetAccountBalanceWithSummaryRequest_ExternalId); ok {
		return x.ExternalId
	}
	return nil
}

func (x *GetAccountBalanceWithSummaryRequest) GetPrimaryAccountHolderActor() string {
	if x, ok := x.GetIdentifier().(*GetAccountBalanceWithSummaryRequest_PrimaryAccountHolderActor); ok {
		return x.PrimaryAccountHolderActor
	}
	return ""
}

func (x *GetAccountBalanceWithSummaryRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetAccountBalanceWithSummaryRequest) GetTimeRange() GetAccountBalanceWithSummaryRequest_Range {
	if x != nil {
		return x.TimeRange
	}
	return GetAccountBalanceWithSummaryRequest_RANGE_UNSPECIFIED
}

func (x *GetAccountBalanceWithSummaryRequest) GetForceBalanceUpdate() GetAccountBalanceWithSummaryRequest_ForceBalanceUpdate {
	if x != nil {
		return x.ForceBalanceUpdate
	}
	return GetAccountBalanceWithSummaryRequest_FORCE_BALANCE_UPDATE_UNSPECIFIED
}

type isGetAccountBalanceWithSummaryRequest_Identifier interface {
	isGetAccountBalanceWithSummaryRequest_Identifier()
}

type GetAccountBalanceWithSummaryRequest_Id struct {
	// internal account id
	Id string `protobuf:"bytes,1,opt,name=id,proto3,oneof"`
}

type GetAccountBalanceWithSummaryRequest_ExternalId struct {
	// External account id, contains identifier provided by banking partner.
	// like account no and ifsc
	ExternalId *BankAccountIdentifier `protobuf:"bytes,2,opt,name=external_id,json=externalId,proto3,oneof"`
}

type GetAccountBalanceWithSummaryRequest_PrimaryAccountHolderActor struct {
	// actor id corresponding to the primary account holder
	// to be passed by client if sum of opening balance corresponding
	// to all the accounts of the actor is required.
	// In case data specific to a single account is needed please use
	// `id` and `external_id`
	PrimaryAccountHolderActor string `protobuf:"bytes,3,opt,name=primary_account_holder_actor,json=primaryAccountHolderActor,proto3,oneof"`
}

func (*GetAccountBalanceWithSummaryRequest_Id) isGetAccountBalanceWithSummaryRequest_Identifier() {}

func (*GetAccountBalanceWithSummaryRequest_ExternalId) isGetAccountBalanceWithSummaryRequest_Identifier() {
}

func (*GetAccountBalanceWithSummaryRequest_PrimaryAccountHolderActor) isGetAccountBalanceWithSummaryRequest_Identifier() {
}

type GetAccountBalanceWithSummaryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status         *rpc.Status  `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	OpeningBalance *money.Money `protobuf:"bytes,2,opt,name=opening_balance,json=openingBalance,proto3" json:"opening_balance,omitempty"`
	// available balance in account which can be used
	// for transaction by the actor
	AvailableBalance *money.Money `protobuf:"bytes,3,opt,name=available_balance,json=availableBalance,proto3" json:"available_balance,omitempty"`
	// balance in the account ledger
	// in general ledger balance is always same as available
	// balance, only in certain scenarios like e-mandates, etc.
	// ledger balance might differ from available balance until
	// the money in hold is released
	LedgerBalance *money.Money `protobuf:"bytes,4,opt,name=ledger_balance,json=ledgerBalance,proto3" json:"ledger_balance,omitempty"`
	// total amount spent by the actor (total debit - saved)
	//
	// Deprecated: Marked as deprecated in api/savings/service.proto.
	TotalSpent *money.Money `protobuf:"bytes,5,opt,name=total_spent,json=totalSpent,proto3" json:"total_spent,omitempty"`
	// total amount credited to the account
	TotalCredit *money.Money `protobuf:"bytes,6,opt,name=total_credit,json=totalCredit,proto3" json:"total_credit,omitempty"`
	// total amount debited from the account
	TotalDebit *money.Money `protobuf:"bytes,7,opt,name=total_debit,json=totalDebit,proto3" json:"total_debit,omitempty"`
	// detailed summary of each bucket of account summary
	//
	// Deprecated: Marked as deprecated in api/savings/service.proto.
	BucketDetails []*AccountSummaryBucketDetails `protobuf:"bytes,8,rep,name=bucket_details,json=bucketDetails,proto3" json:"bucket_details,omitempty"`
	// returns true in case computed balance epiFi's end is stale
	// due to missing transactions in the ledger.
	// the aggregates in this case may not sum up to the balance returned
	IsComputedBalanceStale bool `protobuf:"varint,9,opt,name=is_computed_balance_stale,json=isComputedBalanceStale,proto3" json:"is_computed_balance_stale,omitempty"`
	// balance as of timestamp
	BalanceAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=balance_at,json=balanceAt,proto3" json:"balance_at,omitempty"`
	// returns true in case stale balance is returned from the server cache
	IsBalanceStale bool `protobuf:"varint,11,opt,name=is_balance_stale,json=isBalanceStale,proto3" json:"is_balance_stale,omitempty"`
}

func (x *GetAccountBalanceWithSummaryResponse) Reset() {
	*x = GetAccountBalanceWithSummaryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountBalanceWithSummaryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountBalanceWithSummaryResponse) ProtoMessage() {}

func (x *GetAccountBalanceWithSummaryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountBalanceWithSummaryResponse.ProtoReflect.Descriptor instead.
func (*GetAccountBalanceWithSummaryResponse) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{26}
}

func (x *GetAccountBalanceWithSummaryResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetAccountBalanceWithSummaryResponse) GetOpeningBalance() *money.Money {
	if x != nil {
		return x.OpeningBalance
	}
	return nil
}

func (x *GetAccountBalanceWithSummaryResponse) GetAvailableBalance() *money.Money {
	if x != nil {
		return x.AvailableBalance
	}
	return nil
}

func (x *GetAccountBalanceWithSummaryResponse) GetLedgerBalance() *money.Money {
	if x != nil {
		return x.LedgerBalance
	}
	return nil
}

// Deprecated: Marked as deprecated in api/savings/service.proto.
func (x *GetAccountBalanceWithSummaryResponse) GetTotalSpent() *money.Money {
	if x != nil {
		return x.TotalSpent
	}
	return nil
}

func (x *GetAccountBalanceWithSummaryResponse) GetTotalCredit() *money.Money {
	if x != nil {
		return x.TotalCredit
	}
	return nil
}

func (x *GetAccountBalanceWithSummaryResponse) GetTotalDebit() *money.Money {
	if x != nil {
		return x.TotalDebit
	}
	return nil
}

// Deprecated: Marked as deprecated in api/savings/service.proto.
func (x *GetAccountBalanceWithSummaryResponse) GetBucketDetails() []*AccountSummaryBucketDetails {
	if x != nil {
		return x.BucketDetails
	}
	return nil
}

func (x *GetAccountBalanceWithSummaryResponse) GetIsComputedBalanceStale() bool {
	if x != nil {
		return x.IsComputedBalanceStale
	}
	return false
}

func (x *GetAccountBalanceWithSummaryResponse) GetBalanceAt() *timestamppb.Timestamp {
	if x != nil {
		return x.BalanceAt
	}
	return nil
}

func (x *GetAccountBalanceWithSummaryResponse) GetIsBalanceStale() bool {
	if x != nil {
		return x.IsBalanceStale
	}
	return false
}

// Account summary comprises of
type AccountSummaryBucketDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bucket AccountSummaryBucketDetails_Bucket `protobuf:"varint,1,opt,name=bucket,proto3,enum=savings.AccountSummaryBucketDetails_Bucket" json:"bucket,omitempty"`
	// money value spent in the current category
	Value *money.Money `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	// percentage increase or decrease of current category w.r.t previous month
	PercentChange float32 `protobuf:"fixed32,3,opt,name=percent_change,json=percentChange,proto3" json:"percent_change,omitempty"`
	// array of sub section/ or sub categories (eg. under spent category we can have bills, food as subcategories) in account summary
	Sections []*AccountSummaryBucketDetails `protobuf:"bytes,4,rep,name=sections,proto3" json:"sections,omitempty"`
}

func (x *AccountSummaryBucketDetails) Reset() {
	*x = AccountSummaryBucketDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountSummaryBucketDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountSummaryBucketDetails) ProtoMessage() {}

func (x *AccountSummaryBucketDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountSummaryBucketDetails.ProtoReflect.Descriptor instead.
func (*AccountSummaryBucketDetails) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{27}
}

func (x *AccountSummaryBucketDetails) GetBucket() AccountSummaryBucketDetails_Bucket {
	if x != nil {
		return x.Bucket
	}
	return AccountSummaryBucketDetails_BUCKET_UNSPECIFIED
}

func (x *AccountSummaryBucketDetails) GetValue() *money.Money {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *AccountSummaryBucketDetails) GetPercentChange() float32 {
	if x != nil {
		return x.PercentChange
	}
	return 0
}

func (x *AccountSummaryBucketDetails) GetSections() []*AccountSummaryBucketDetails {
	if x != nil {
		return x.Sections
	}
	return nil
}

type GetListOfActiveAccountsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Optional: timestamp before which active account needs to be fetched
	// in case empty all accounts created till current time will be returned
	CreatedBefore *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=created_before,json=createdBefore,proto3" json:"created_before,omitempty"`
	// Optional: timestamp after which active account needs to be fetched
	// in case empty all accounts created starting from the first account will be returned
	CreatedAfter *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=created_after,json=createdAfter,proto3" json:"created_after,omitempty"`
	// Optional: size of page returned by the RPC. Ideally, caller should specify this
	// when the time duration for the query is expected to be huge.
	// E.g. if the client want to fetch all the active accounts till current date then
	// it should specify the page size as the response can be enormous
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// Optional: offset for response to be returned
	// In case of pagination caller can use the offset value passed in the call response
	// to fetch the subsequent pages.
	Offset int64 `protobuf:"varint,4,opt,name=offset,proto3" json:"offset,omitempty"`
}

func (x *GetListOfActiveAccountsRequest) Reset() {
	*x = GetListOfActiveAccountsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetListOfActiveAccountsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetListOfActiveAccountsRequest) ProtoMessage() {}

func (x *GetListOfActiveAccountsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetListOfActiveAccountsRequest.ProtoReflect.Descriptor instead.
func (*GetListOfActiveAccountsRequest) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{28}
}

func (x *GetListOfActiveAccountsRequest) GetCreatedBefore() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedBefore
	}
	return nil
}

func (x *GetListOfActiveAccountsRequest) GetCreatedAfter() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAfter
	}
	return nil
}

func (x *GetListOfActiveAccountsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetListOfActiveAccountsRequest) GetOffset() int64 {
	if x != nil {
		return x.Offset
	}
	return 0
}

// list of all active savings account for which monthly statement will be generated
type GetListOfActiveAccountsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in api/savings/service.proto.
	Account []*GetListOfActiveAccountsResponse_Account `protobuf:"bytes,1,rep,name=account,proto3" json:"account,omitempty"`
	Status  *rpc.Status                                `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	// list of savings account returned based on the input argument
	AccountList []*Account `protobuf:"bytes,3,rep,name=account_list,json=accountList,proto3" json:"account_list,omitempty"`
	// offset to be passed in next RPC call in order to fetch subsequent accounts
	// to be used by caller in case account details are being fetched in a paginated manner
	NextOffset int64 `protobuf:"varint,4,opt,name=next_offset,json=nextOffset,proto3" json:"next_offset,omitempty"`
}

func (x *GetListOfActiveAccountsResponse) Reset() {
	*x = GetListOfActiveAccountsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetListOfActiveAccountsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetListOfActiveAccountsResponse) ProtoMessage() {}

func (x *GetListOfActiveAccountsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetListOfActiveAccountsResponse.ProtoReflect.Descriptor instead.
func (*GetListOfActiveAccountsResponse) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{29}
}

// Deprecated: Marked as deprecated in api/savings/service.proto.
func (x *GetListOfActiveAccountsResponse) GetAccount() []*GetListOfActiveAccountsResponse_Account {
	if x != nil {
		return x.Account
	}
	return nil
}

func (x *GetListOfActiveAccountsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetListOfActiveAccountsResponse) GetAccountList() []*Account {
	if x != nil {
		return x.AccountList
	}
	return nil
}

func (x *GetListOfActiveAccountsResponse) GetNextOffset() int64 {
	if x != nil {
		return x.NextOffset
	}
	return 0
}

type CloseAccountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Identifier:
	//
	//	*CloseAccountRequest_Id
	//	*CloseAccountRequest_ExternalId
	//	*CloseAccountRequest_PrimaryUserId
	Identifier isCloseAccountRequest_Identifier `protobuf_oneof:"identifier"`
}

func (x *CloseAccountRequest) Reset() {
	*x = CloseAccountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CloseAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloseAccountRequest) ProtoMessage() {}

func (x *CloseAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloseAccountRequest.ProtoReflect.Descriptor instead.
func (*CloseAccountRequest) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{30}
}

func (m *CloseAccountRequest) GetIdentifier() isCloseAccountRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *CloseAccountRequest) GetId() string {
	if x, ok := x.GetIdentifier().(*CloseAccountRequest_Id); ok {
		return x.Id
	}
	return ""
}

func (x *CloseAccountRequest) GetExternalId() *BankAccountIdentifier {
	if x, ok := x.GetIdentifier().(*CloseAccountRequest_ExternalId); ok {
		return x.ExternalId
	}
	return nil
}

func (x *CloseAccountRequest) GetPrimaryUserId() string {
	if x, ok := x.GetIdentifier().(*CloseAccountRequest_PrimaryUserId); ok {
		return x.PrimaryUserId
	}
	return ""
}

type isCloseAccountRequest_Identifier interface {
	isCloseAccountRequest_Identifier()
}

type CloseAccountRequest_Id struct {
	// Internal account id.
	Id string `protobuf:"bytes,1,opt,name=id,proto3,oneof"`
}

type CloseAccountRequest_ExternalId struct {
	// External account id, contains identifier provided by banking partner.
	ExternalId *BankAccountIdentifier `protobuf:"bytes,2,opt,name=external_id,json=externalId,proto3,oneof"`
}

type CloseAccountRequest_PrimaryUserId struct {
	// Id of the primary account holder
	PrimaryUserId string `protobuf:"bytes,3,opt,name=primary_user_id,json=primaryUserId,proto3,oneof"`
}

func (*CloseAccountRequest_Id) isCloseAccountRequest_Identifier() {}

func (*CloseAccountRequest_ExternalId) isCloseAccountRequest_Identifier() {}

func (*CloseAccountRequest_PrimaryUserId) isCloseAccountRequest_Identifier() {}

type CloseAccountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Contains updated account info.
	Account *Account `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
}

func (x *CloseAccountResponse) Reset() {
	*x = CloseAccountResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CloseAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloseAccountResponse) ProtoMessage() {}

func (x *CloseAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloseAccountResponse.ProtoReflect.Descriptor instead.
func (*CloseAccountResponse) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{31}
}

func (x *CloseAccountResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CloseAccountResponse) GetAccount() *Account {
	if x != nil {
		return x.Account
	}
	return nil
}

type ReopenAccountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Identifier:
	//
	//	*ReopenAccountRequest_Id
	//	*ReopenAccountRequest_ExternalId
	//	*ReopenAccountRequest_PrimaryUserId
	Identifier isReopenAccountRequest_Identifier `protobuf_oneof:"identifier"`
}

func (x *ReopenAccountRequest) Reset() {
	*x = ReopenAccountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReopenAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReopenAccountRequest) ProtoMessage() {}

func (x *ReopenAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReopenAccountRequest.ProtoReflect.Descriptor instead.
func (*ReopenAccountRequest) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{32}
}

func (m *ReopenAccountRequest) GetIdentifier() isReopenAccountRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *ReopenAccountRequest) GetId() string {
	if x, ok := x.GetIdentifier().(*ReopenAccountRequest_Id); ok {
		return x.Id
	}
	return ""
}

func (x *ReopenAccountRequest) GetExternalId() *BankAccountIdentifier {
	if x, ok := x.GetIdentifier().(*ReopenAccountRequest_ExternalId); ok {
		return x.ExternalId
	}
	return nil
}

func (x *ReopenAccountRequest) GetPrimaryUserId() string {
	if x, ok := x.GetIdentifier().(*ReopenAccountRequest_PrimaryUserId); ok {
		return x.PrimaryUserId
	}
	return ""
}

type isReopenAccountRequest_Identifier interface {
	isReopenAccountRequest_Identifier()
}

type ReopenAccountRequest_Id struct {
	// Internal account id.
	Id string `protobuf:"bytes,1,opt,name=id,proto3,oneof"`
}

type ReopenAccountRequest_ExternalId struct {
	// External account id, contains identifier provided by banking partner.
	ExternalId *BankAccountIdentifier `protobuf:"bytes,2,opt,name=external_id,json=externalId,proto3,oneof"`
}

type ReopenAccountRequest_PrimaryUserId struct {
	// Id of the primary account holder
	PrimaryUserId string `protobuf:"bytes,3,opt,name=primary_user_id,json=primaryUserId,proto3,oneof"`
}

func (*ReopenAccountRequest_Id) isReopenAccountRequest_Identifier() {}

func (*ReopenAccountRequest_ExternalId) isReopenAccountRequest_Identifier() {}

func (*ReopenAccountRequest_PrimaryUserId) isReopenAccountRequest_Identifier() {}

type ReopenAccountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Contains updated account info.
	Account *Account `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
}

func (x *ReopenAccountResponse) Reset() {
	*x = ReopenAccountResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReopenAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReopenAccountResponse) ProtoMessage() {}

func (x *ReopenAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReopenAccountResponse.ProtoReflect.Descriptor instead.
func (*ReopenAccountResponse) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{33}
}

func (x *ReopenAccountResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ReopenAccountResponse) GetAccount() *Account {
	if x != nil {
		return x.Account
	}
	return nil
}

type IsTxnAllowedRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Identifier:
	//
	//	*IsTxnAllowedRequest_Id
	//	*IsTxnAllowedRequest_ExternalId
	//	*IsTxnAllowedRequest_PrimaryAccountHolderActor
	Identifier isIsTxnAllowedRequest_Identifier `protobuf_oneof:"identifier"`
	// Amount to be credited to the account
	Amount *money.Money `protobuf:"bytes,4,opt,name=amount,proto3" json:"amount,omitempty"`
	// field to specify if the requested transaction is debit or credit one.
	TxnType IsTxnAllowedRequest_TxnType `protobuf:"varint,5,opt,name=txn_type,json=txnType,proto3,enum=savings.IsTxnAllowedRequest_TxnType" json:"txn_type,omitempty"`
}

func (x *IsTxnAllowedRequest) Reset() {
	*x = IsTxnAllowedRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsTxnAllowedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsTxnAllowedRequest) ProtoMessage() {}

func (x *IsTxnAllowedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsTxnAllowedRequest.ProtoReflect.Descriptor instead.
func (*IsTxnAllowedRequest) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{34}
}

func (m *IsTxnAllowedRequest) GetIdentifier() isIsTxnAllowedRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *IsTxnAllowedRequest) GetId() string {
	if x, ok := x.GetIdentifier().(*IsTxnAllowedRequest_Id); ok {
		return x.Id
	}
	return ""
}

func (x *IsTxnAllowedRequest) GetExternalId() *BankAccountIdentifier {
	if x, ok := x.GetIdentifier().(*IsTxnAllowedRequest_ExternalId); ok {
		return x.ExternalId
	}
	return nil
}

func (x *IsTxnAllowedRequest) GetPrimaryAccountHolderActor() string {
	if x, ok := x.GetIdentifier().(*IsTxnAllowedRequest_PrimaryAccountHolderActor); ok {
		return x.PrimaryAccountHolderActor
	}
	return ""
}

func (x *IsTxnAllowedRequest) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *IsTxnAllowedRequest) GetTxnType() IsTxnAllowedRequest_TxnType {
	if x != nil {
		return x.TxnType
	}
	return IsTxnAllowedRequest_TXN_TYPE_UNSPECIFIED
}

type isIsTxnAllowedRequest_Identifier interface {
	isIsTxnAllowedRequest_Identifier()
}

type IsTxnAllowedRequest_Id struct {
	// internal account id
	Id string `protobuf:"bytes,1,opt,name=id,proto3,oneof"`
}

type IsTxnAllowedRequest_ExternalId struct {
	// External account id, contains identifier provided by banking partner.
	// like account no and ifsc
	ExternalId *BankAccountIdentifier `protobuf:"bytes,2,opt,name=external_id,json=externalId,proto3,oneof"`
}

type IsTxnAllowedRequest_PrimaryAccountHolderActor struct {
	// actor id corresponding to the primary account holder
	// to be passed by client if sum of opening balance corresponding
	// to all the accounts of the actor is required.
	// In case data specific to a single account is needed please use
	// `id` and `external_id`
	PrimaryAccountHolderActor string `protobuf:"bytes,3,opt,name=primary_account_holder_actor,json=primaryAccountHolderActor,proto3,oneof"`
}

func (*IsTxnAllowedRequest_Id) isIsTxnAllowedRequest_Identifier() {}

func (*IsTxnAllowedRequest_ExternalId) isIsTxnAllowedRequest_Identifier() {}

func (*IsTxnAllowedRequest_PrimaryAccountHolderActor) isIsTxnAllowedRequest_Identifier() {}

type IsTxnAllowedResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// field to represent credit amount allowed for transaction.
	// This value will be based on the min of (total credited allowed - total credited till now) and (max savings balance - current savings balance)
	CreditAmountAllowed *money.Money `protobuf:"bytes,2,opt,name=credit_amount_allowed,json=creditAmountAllowed,proto3" json:"credit_amount_allowed,omitempty"`
	// for min-kyc accounts, date on which savings account will freeze
	// as per the current min-kyc limits, accounts will freeze one year after creation
	// In case of account being full-kyc, this field will be null
	AccountFreezeDate *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=account_freeze_date,json=accountFreezeDate,proto3" json:"account_freeze_date,omitempty"`
	// Reason why credit transaction isn't allowed
	NotAllowedReason IsTxnAllowedResponse_Reason `protobuf:"varint,4,opt,name=not_allowed_reason,json=notAllowedReason,proto3,enum=savings.IsTxnAllowedResponse_Reason" json:"not_allowed_reason,omitempty"`
	// denotes various limits related to txn like savings account limit, credit limit for now
	SavingsTxnLimits *IsTxnAllowedResponse_SavingsTxnLimits `protobuf:"bytes,5,opt,name=savings_txn_limits,json=savingsTxnLimits,proto3" json:"savings_txn_limits,omitempty"`
}

func (x *IsTxnAllowedResponse) Reset() {
	*x = IsTxnAllowedResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsTxnAllowedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsTxnAllowedResponse) ProtoMessage() {}

func (x *IsTxnAllowedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsTxnAllowedResponse.ProtoReflect.Descriptor instead.
func (*IsTxnAllowedResponse) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{35}
}

func (x *IsTxnAllowedResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *IsTxnAllowedResponse) GetCreditAmountAllowed() *money.Money {
	if x != nil {
		return x.CreditAmountAllowed
	}
	return nil
}

func (x *IsTxnAllowedResponse) GetAccountFreezeDate() *timestamppb.Timestamp {
	if x != nil {
		return x.AccountFreezeDate
	}
	return nil
}

func (x *IsTxnAllowedResponse) GetNotAllowedReason() IsTxnAllowedResponse_Reason {
	if x != nil {
		return x.NotAllowedReason
	}
	return IsTxnAllowedResponse_REASON_UNSPECIFIED
}

func (x *IsTxnAllowedResponse) GetSavingsTxnLimits() *IsTxnAllowedResponse_SavingsTxnLimits {
	if x != nil {
		return x.SavingsTxnLimits
	}
	return nil
}

type StoreClosedAccountBalTransferDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data *ClosedAccountBalanceTransfer `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *StoreClosedAccountBalTransferDataRequest) Reset() {
	*x = StoreClosedAccountBalTransferDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreClosedAccountBalTransferDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreClosedAccountBalTransferDataRequest) ProtoMessage() {}

func (x *StoreClosedAccountBalTransferDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreClosedAccountBalTransferDataRequest.ProtoReflect.Descriptor instead.
func (*StoreClosedAccountBalTransferDataRequest) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{36}
}

func (x *StoreClosedAccountBalTransferDataRequest) GetData() *ClosedAccountBalanceTransfer {
	if x != nil {
		return x.Data
	}
	return nil
}

type StoreClosedAccountBalTransferDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *StoreClosedAccountBalTransferDataResponse) Reset() {
	*x = StoreClosedAccountBalTransferDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreClosedAccountBalTransferDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreClosedAccountBalTransferDataResponse) ProtoMessage() {}

func (x *StoreClosedAccountBalTransferDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreClosedAccountBalTransferDataResponse.ProtoReflect.Descriptor instead.
func (*StoreClosedAccountBalTransferDataResponse) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{37}
}

func (x *StoreClosedAccountBalTransferDataResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type StoreClosedAccountBalTransferDataFromStatementRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// savings_account_id is the id in savings_accounts table
	SavingsAccountId string `protobuf:"bytes,1,opt,name=savings_account_id,json=savingsAccountId,proto3" json:"savings_account_id,omitempty"`
}

func (x *StoreClosedAccountBalTransferDataFromStatementRequest) Reset() {
	*x = StoreClosedAccountBalTransferDataFromStatementRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreClosedAccountBalTransferDataFromStatementRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreClosedAccountBalTransferDataFromStatementRequest) ProtoMessage() {}

func (x *StoreClosedAccountBalTransferDataFromStatementRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreClosedAccountBalTransferDataFromStatementRequest.ProtoReflect.Descriptor instead.
func (*StoreClosedAccountBalTransferDataFromStatementRequest) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{38}
}

func (x *StoreClosedAccountBalTransferDataFromStatementRequest) GetSavingsAccountId() string {
	if x != nil {
		return x.SavingsAccountId
	}
	return ""
}

type StoreClosedAccountBalTransferDataFromStatementResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *StoreClosedAccountBalTransferDataFromStatementResponse) Reset() {
	*x = StoreClosedAccountBalTransferDataFromStatementResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreClosedAccountBalTransferDataFromStatementResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreClosedAccountBalTransferDataFromStatementResponse) ProtoMessage() {}

func (x *StoreClosedAccountBalTransferDataFromStatementResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreClosedAccountBalTransferDataFromStatementResponse.ProtoReflect.Descriptor instead.
func (*StoreClosedAccountBalTransferDataFromStatementResponse) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{39}
}

func (x *StoreClosedAccountBalTransferDataFromStatementResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetClosedAccountBalTransferDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// savings_account_id is the id in savings_accounts table
	SavingsAccountId string `protobuf:"bytes,1,opt,name=savings_account_id,json=savingsAccountId,proto3" json:"savings_account_id,omitempty"`
}

func (x *GetClosedAccountBalTransferDataRequest) Reset() {
	*x = GetClosedAccountBalTransferDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetClosedAccountBalTransferDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClosedAccountBalTransferDataRequest) ProtoMessage() {}

func (x *GetClosedAccountBalTransferDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClosedAccountBalTransferDataRequest.ProtoReflect.Descriptor instead.
func (*GetClosedAccountBalTransferDataRequest) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{40}
}

func (x *GetClosedAccountBalTransferDataRequest) GetSavingsAccountId() string {
	if x != nil {
		return x.SavingsAccountId
	}
	return ""
}

type GetClosedAccountBalTransferDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  *rpc.Status                     `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Entries []*ClosedAccountBalanceTransfer `protobuf:"bytes,2,rep,name=entries,proto3" json:"entries,omitempty"`
}

func (x *GetClosedAccountBalTransferDataResponse) Reset() {
	*x = GetClosedAccountBalTransferDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetClosedAccountBalTransferDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClosedAccountBalTransferDataResponse) ProtoMessage() {}

func (x *GetClosedAccountBalTransferDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClosedAccountBalTransferDataResponse.ProtoReflect.Descriptor instead.
func (*GetClosedAccountBalTransferDataResponse) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{41}
}

func (x *GetClosedAccountBalTransferDataResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetClosedAccountBalTransferDataResponse) GetEntries() []*ClosedAccountBalanceTransfer {
	if x != nil {
		return x.Entries
	}
	return nil
}

type GetEODSavBalanceHistoryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Start time of balance history
	StartTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// End time of balance history
	EndTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
}

func (x *GetEODSavBalanceHistoryRequest) Reset() {
	*x = GetEODSavBalanceHistoryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEODSavBalanceHistoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEODSavBalanceHistoryRequest) ProtoMessage() {}

func (x *GetEODSavBalanceHistoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEODSavBalanceHistoryRequest.ProtoReflect.Descriptor instead.
func (*GetEODSavBalanceHistoryRequest) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{42}
}

func (x *GetEODSavBalanceHistoryRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetEODSavBalanceHistoryRequest) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *GetEODSavBalanceHistoryRequest) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

type GetEODSavBalanceHistoryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// List of balances by date
	BalanceByDates []*BalanceByDate `protobuf:"bytes,2,rep,name=balance_by_dates,json=balanceByDates,proto3" json:"balance_by_dates,omitempty"`
}

func (x *GetEODSavBalanceHistoryResponse) Reset() {
	*x = GetEODSavBalanceHistoryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEODSavBalanceHistoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEODSavBalanceHistoryResponse) ProtoMessage() {}

func (x *GetEODSavBalanceHistoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEODSavBalanceHistoryResponse.ProtoReflect.Descriptor instead.
func (*GetEODSavBalanceHistoryResponse) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{43}
}

func (x *GetEODSavBalanceHistoryResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetEODSavBalanceHistoryResponse) GetBalanceByDates() []*BalanceByDate {
	if x != nil {
		return x.BalanceByDates
	}
	return nil
}

type BalanceByDate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Date *date.Date `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	// Balance amount
	EodBalance *money.Money `protobuf:"bytes,2,opt,name=eod_balance,json=eodBalance,proto3" json:"eod_balance,omitempty"`
}

func (x *BalanceByDate) Reset() {
	*x = BalanceByDate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BalanceByDate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BalanceByDate) ProtoMessage() {}

func (x *BalanceByDate) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BalanceByDate.ProtoReflect.Descriptor instead.
func (*BalanceByDate) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{44}
}

func (x *BalanceByDate) GetDate() *date.Date {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *BalanceByDate) GetEodBalance() *money.Money {
	if x != nil {
		return x.EodBalance
	}
	return nil
}

type UpdateClosedAccountBalTransferDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// closed account id is used as primary identifier
	Data       *ClosedAccountBalanceTransfer `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	FieldMasks []CbtFieldMask                `protobuf:"varint,2,rep,packed,name=field_masks,json=fieldMasks,proto3,enum=savings.CbtFieldMask" json:"field_masks,omitempty"`
}

func (x *UpdateClosedAccountBalTransferDataRequest) Reset() {
	*x = UpdateClosedAccountBalTransferDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateClosedAccountBalTransferDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateClosedAccountBalTransferDataRequest) ProtoMessage() {}

func (x *UpdateClosedAccountBalTransferDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateClosedAccountBalTransferDataRequest.ProtoReflect.Descriptor instead.
func (*UpdateClosedAccountBalTransferDataRequest) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{45}
}

func (x *UpdateClosedAccountBalTransferDataRequest) GetData() *ClosedAccountBalanceTransfer {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *UpdateClosedAccountBalTransferDataRequest) GetFieldMasks() []CbtFieldMask {
	if x != nil {
		return x.FieldMasks
	}
	return nil
}

type UpdateClosedAccountBalTransferDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpdateClosedAccountBalTransferDataResponse) Reset() {
	*x = UpdateClosedAccountBalTransferDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateClosedAccountBalTransferDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateClosedAccountBalTransferDataResponse) ProtoMessage() {}

func (x *UpdateClosedAccountBalTransferDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateClosedAccountBalTransferDataResponse.ProtoReflect.Descriptor instead.
func (*UpdateClosedAccountBalTransferDataResponse) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{46}
}

func (x *UpdateClosedAccountBalTransferDataResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type FetchOrCreateSignAttemptRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *FetchOrCreateSignAttemptRequest) Reset() {
	*x = FetchOrCreateSignAttemptRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchOrCreateSignAttemptRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchOrCreateSignAttemptRequest) ProtoMessage() {}

func (x *FetchOrCreateSignAttemptRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchOrCreateSignAttemptRequest.ProtoReflect.Descriptor instead.
func (*FetchOrCreateSignAttemptRequest) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{47}
}

func (x *FetchOrCreateSignAttemptRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type FetchOrCreateSignAttemptResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	SignStatus SignStatus  `protobuf:"varint,2,opt,name=sign_status,json=signStatus,proto3,enum=savings.SignStatus" json:"sign_status,omitempty"`
	// sign_url contains url in case of sign state is created, failed, or in progress
	SignUrl string `protobuf:"bytes,3,opt,name=sign_url,json=signUrl,proto3" json:"sign_url,omitempty"`
	// used by client to decide they have to close webview
	ExitUrl string `protobuf:"bytes,4,opt,name=exit_url,json=exitUrl,proto3" json:"exit_url,omitempty"`
}

func (x *FetchOrCreateSignAttemptResponse) Reset() {
	*x = FetchOrCreateSignAttemptResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchOrCreateSignAttemptResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchOrCreateSignAttemptResponse) ProtoMessage() {}

func (x *FetchOrCreateSignAttemptResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchOrCreateSignAttemptResponse.ProtoReflect.Descriptor instead.
func (*FetchOrCreateSignAttemptResponse) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{48}
}

func (x *FetchOrCreateSignAttemptResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *FetchOrCreateSignAttemptResponse) GetSignStatus() SignStatus {
	if x != nil {
		return x.SignStatus
	}
	return SignStatus_SIGN_STATUS_UNSPECIFIED
}

func (x *FetchOrCreateSignAttemptResponse) GetSignUrl() string {
	if x != nil {
		return x.SignUrl
	}
	return ""
}

func (x *FetchOrCreateSignAttemptResponse) GetExitUrl() string {
	if x != nil {
		return x.ExitUrl
	}
	return ""
}

type ActorIdBankFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId     string               `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	PartnerBank vendorgateway.Vendor `protobuf:"varint,2,opt,name=partner_bank,json=partnerBank,proto3,enum=vendorgateway.Vendor" json:"partner_bank,omitempty"`
}

func (x *ActorIdBankFilter) Reset() {
	*x = ActorIdBankFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActorIdBankFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActorIdBankFilter) ProtoMessage() {}

func (x *ActorIdBankFilter) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActorIdBankFilter.ProtoReflect.Descriptor instead.
func (*ActorIdBankFilter) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{49}
}

func (x *ActorIdBankFilter) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *ActorIdBankFilter) GetPartnerBank() vendorgateway.Vendor {
	if x != nil {
		return x.PartnerBank
	}
	return vendorgateway.Vendor(0)
}

type AccountNumberBankFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// External account number that's visible to the user.
	AccountNumber string               `protobuf:"bytes,1,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	PartnerBank   vendorgateway.Vendor `protobuf:"varint,2,opt,name=partner_bank,json=partnerBank,proto3,enum=vendorgateway.Vendor" json:"partner_bank,omitempty"`
}

func (x *AccountNumberBankFilter) Reset() {
	*x = AccountNumberBankFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountNumberBankFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountNumberBankFilter) ProtoMessage() {}

func (x *AccountNumberBankFilter) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountNumberBankFilter.ProtoReflect.Descriptor instead.
func (*AccountNumberBankFilter) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{50}
}

func (x *AccountNumberBankFilter) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *AccountNumberBankFilter) GetPartnerBank() vendorgateway.Vendor {
	if x != nil {
		return x.PartnerBank
	}
	return vendorgateway.Vendor(0)
}

type GetSavingsAccountEssentialsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Filter:
	//
	//	*GetSavingsAccountEssentialsRequest_ActorIdBankFilter
	//	*GetSavingsAccountEssentialsRequest_AccountNumBankFilter
	//	*GetSavingsAccountEssentialsRequest_ActorUniqueAccountIdentifier
	Filter isGetSavingsAccountEssentialsRequest_Filter `protobuf_oneof:"filter"`
}

func (x *GetSavingsAccountEssentialsRequest) Reset() {
	*x = GetSavingsAccountEssentialsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSavingsAccountEssentialsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSavingsAccountEssentialsRequest) ProtoMessage() {}

func (x *GetSavingsAccountEssentialsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSavingsAccountEssentialsRequest.ProtoReflect.Descriptor instead.
func (*GetSavingsAccountEssentialsRequest) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{51}
}

func (m *GetSavingsAccountEssentialsRequest) GetFilter() isGetSavingsAccountEssentialsRequest_Filter {
	if m != nil {
		return m.Filter
	}
	return nil
}

// Deprecated: Marked as deprecated in api/savings/service.proto.
func (x *GetSavingsAccountEssentialsRequest) GetActorIdBankFilter() *ActorIdBankFilter {
	if x, ok := x.GetFilter().(*GetSavingsAccountEssentialsRequest_ActorIdBankFilter); ok {
		return x.ActorIdBankFilter
	}
	return nil
}

func (x *GetSavingsAccountEssentialsRequest) GetAccountNumBankFilter() *AccountNumberBankFilter {
	if x, ok := x.GetFilter().(*GetSavingsAccountEssentialsRequest_AccountNumBankFilter); ok {
		return x.AccountNumBankFilter
	}
	return nil
}

func (x *GetSavingsAccountEssentialsRequest) GetActorUniqueAccountIdentifier() *ActorUniqueAccountIdentifier {
	if x, ok := x.GetFilter().(*GetSavingsAccountEssentialsRequest_ActorUniqueAccountIdentifier); ok {
		return x.ActorUniqueAccountIdentifier
	}
	return nil
}

type isGetSavingsAccountEssentialsRequest_Filter interface {
	isGetSavingsAccountEssentialsRequest_Filter()
}

type GetSavingsAccountEssentialsRequest_ActorIdBankFilter struct {
	// deprecated in favour of ActorUniqueAccountIdentifier
	//
	// Deprecated: Marked as deprecated in api/savings/service.proto.
	ActorIdBankFilter *ActorIdBankFilter `protobuf:"bytes,1,opt,name=actor_id_bank_filter,json=actorIdBankFilter,proto3,oneof"`
}

type GetSavingsAccountEssentialsRequest_AccountNumBankFilter struct {
	AccountNumBankFilter *AccountNumberBankFilter `protobuf:"bytes,2,opt,name=account_num_bank_filter,json=accountNumBankFilter,proto3,oneof"`
}

type GetSavingsAccountEssentialsRequest_ActorUniqueAccountIdentifier struct {
	ActorUniqueAccountIdentifier *ActorUniqueAccountIdentifier `protobuf:"bytes,3,opt,name=actor_unique_account_identifier,json=actorUniqueAccountIdentifier,proto3,oneof"`
}

func (*GetSavingsAccountEssentialsRequest_ActorIdBankFilter) isGetSavingsAccountEssentialsRequest_Filter() {
}

func (*GetSavingsAccountEssentialsRequest_AccountNumBankFilter) isGetSavingsAccountEssentialsRequest_Filter() {
}

func (*GetSavingsAccountEssentialsRequest_ActorUniqueAccountIdentifier) isGetSavingsAccountEssentialsRequest_Filter() {
}

type SavingsAccountEssentials struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// internal ID referencing the account.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// External account number that's visible to the user.
	AccountNo string `protobuf:"bytes,2,opt,name=account_no,json=accountNo,proto3" json:"account_no,omitempty"`
	ActorId   string `protobuf:"bytes,3,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// IFSC code corresponding of the account
	IfscCode string `protobuf:"bytes,5,opt,name=ifsc_code,json=ifscCode,proto3" json:"ifsc_code,omitempty"`
	// Current Account status
	State State `protobuf:"varint,6,opt,name=state,proto3,enum=savings.State" json:"state,omitempty"`
	// Partner bank to which the account belongs to
	PartnerBank vendorgateway.Vendor `protobuf:"varint,7,opt,name=partner_bank,json=partnerBank,proto3,enum=vendorgateway.Vendor" json:"partner_bank,omitempty"`
	SkuInfo     *SKUInfo             `protobuf:"bytes,8,opt,name=sku_info,json=skuInfo,proto3" json:"sku_info,omitempty"`
	// cache_version will be used to invalidate older MinimalAccount cached data in case we add more
	// fields in MinimalAccount. The current version of the cache will be set as a constant in code.
	CacheVersion int32 `protobuf:"varint,10,opt,name=cache_version,json=cacheVersion,proto3" json:"cache_version,omitempty"`
}

func (x *SavingsAccountEssentials) Reset() {
	*x = SavingsAccountEssentials{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SavingsAccountEssentials) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SavingsAccountEssentials) ProtoMessage() {}

func (x *SavingsAccountEssentials) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SavingsAccountEssentials.ProtoReflect.Descriptor instead.
func (*SavingsAccountEssentials) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{52}
}

func (x *SavingsAccountEssentials) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SavingsAccountEssentials) GetAccountNo() string {
	if x != nil {
		return x.AccountNo
	}
	return ""
}

func (x *SavingsAccountEssentials) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *SavingsAccountEssentials) GetIfscCode() string {
	if x != nil {
		return x.IfscCode
	}
	return ""
}

func (x *SavingsAccountEssentials) GetState() State {
	if x != nil {
		return x.State
	}
	return State_STATE_UNSPECIFIED
}

func (x *SavingsAccountEssentials) GetPartnerBank() vendorgateway.Vendor {
	if x != nil {
		return x.PartnerBank
	}
	return vendorgateway.Vendor(0)
}

func (x *SavingsAccountEssentials) GetSkuInfo() *SKUInfo {
	if x != nil {
		return x.SkuInfo
	}
	return nil
}

func (x *SavingsAccountEssentials) GetCacheVersion() int32 {
	if x != nil {
		return x.CacheVersion
	}
	return 0
}

type GetSavingsAccountEssentialsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  *rpc.Status               `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Account *SavingsAccountEssentials `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
}

func (x *GetSavingsAccountEssentialsResponse) Reset() {
	*x = GetSavingsAccountEssentialsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSavingsAccountEssentialsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSavingsAccountEssentialsResponse) ProtoMessage() {}

func (x *GetSavingsAccountEssentialsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSavingsAccountEssentialsResponse.ProtoReflect.Descriptor instead.
func (*GetSavingsAccountEssentialsResponse) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{53}
}

func (x *GetSavingsAccountEssentialsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetSavingsAccountEssentialsResponse) GetAccount() *SavingsAccountEssentials {
	if x != nil {
		return x.Account
	}
	return nil
}

type CreateOrGetSaClosureRequestRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor id of the user requesting for account closure
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// entry point of the SA Closure flow
	// entry point is recorded for the user when the request creation is done
	// user might resume the closure request from different entry point - this will not be tracked
	EntryPoint SAClosureRequestEntryPoint `protobuf:"varint,2,opt,name=entry_point,json=entryPoint,proto3,enum=savings.SAClosureRequestEntryPoint" json:"entry_point,omitempty"`
	// field mask to select the fields from closure request
	FieldMasks []SavingsAccountClosureRequestFieldMask `protobuf:"varint,3,rep,packed,name=field_masks,json=fieldMasks,proto3,enum=savings.SavingsAccountClosureRequestFieldMask" json:"field_masks,omitempty"`
}

func (x *CreateOrGetSaClosureRequestRequest) Reset() {
	*x = CreateOrGetSaClosureRequestRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOrGetSaClosureRequestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrGetSaClosureRequestRequest) ProtoMessage() {}

func (x *CreateOrGetSaClosureRequestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrGetSaClosureRequestRequest.ProtoReflect.Descriptor instead.
func (*CreateOrGetSaClosureRequestRequest) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{54}
}

func (x *CreateOrGetSaClosureRequestRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *CreateOrGetSaClosureRequestRequest) GetEntryPoint() SAClosureRequestEntryPoint {
	if x != nil {
		return x.EntryPoint
	}
	return SAClosureRequestEntryPoint_SA_CLOSURE_REQUEST_ENTRY_POINT_UNSPECIFIED
}

func (x *CreateOrGetSaClosureRequestRequest) GetFieldMasks() []SavingsAccountClosureRequestFieldMask {
	if x != nil {
		return x.FieldMasks
	}
	return nil
}

type CreateOrGetSaClosureRequestResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// closure request of the user
	ClosureRequest *SavingsAccountClosureRequest `protobuf:"bytes,2,opt,name=closure_request,json=closureRequest,proto3" json:"closure_request,omitempty"`
}

func (x *CreateOrGetSaClosureRequestResponse) Reset() {
	*x = CreateOrGetSaClosureRequestResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOrGetSaClosureRequestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrGetSaClosureRequestResponse) ProtoMessage() {}

func (x *CreateOrGetSaClosureRequestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrGetSaClosureRequestResponse.ProtoReflect.Descriptor instead.
func (*CreateOrGetSaClosureRequestResponse) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{55}
}

func (x *CreateOrGetSaClosureRequestResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CreateOrGetSaClosureRequestResponse) GetClosureRequest() *SavingsAccountClosureRequest {
	if x != nil {
		return x.ClosureRequest
	}
	return nil
}

type GetActiveSaClosureRequestForUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor id of the user requesting for account closure
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// field mask to select the fields from closure request
	FieldMasks []SavingsAccountClosureRequestFieldMask `protobuf:"varint,2,rep,packed,name=field_masks,json=fieldMasks,proto3,enum=savings.SavingsAccountClosureRequestFieldMask" json:"field_masks,omitempty"`
}

func (x *GetActiveSaClosureRequestForUserRequest) Reset() {
	*x = GetActiveSaClosureRequestForUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActiveSaClosureRequestForUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActiveSaClosureRequestForUserRequest) ProtoMessage() {}

func (x *GetActiveSaClosureRequestForUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActiveSaClosureRequestForUserRequest.ProtoReflect.Descriptor instead.
func (*GetActiveSaClosureRequestForUserRequest) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{56}
}

func (x *GetActiveSaClosureRequestForUserRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetActiveSaClosureRequestForUserRequest) GetFieldMasks() []SavingsAccountClosureRequestFieldMask {
	if x != nil {
		return x.FieldMasks
	}
	return nil
}

type GetActiveSaClosureRequestForUserResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// closure request of the user
	ClosureRequest *SavingsAccountClosureRequest `protobuf:"bytes,2,opt,name=closure_request,json=closureRequest,proto3" json:"closure_request,omitempty"`
}

func (x *GetActiveSaClosureRequestForUserResponse) Reset() {
	*x = GetActiveSaClosureRequestForUserResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActiveSaClosureRequestForUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActiveSaClosureRequestForUserResponse) ProtoMessage() {}

func (x *GetActiveSaClosureRequestForUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActiveSaClosureRequestForUserResponse.ProtoReflect.Descriptor instead.
func (*GetActiveSaClosureRequestForUserResponse) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{57}
}

func (x *GetActiveSaClosureRequestForUserResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetActiveSaClosureRequestForUserResponse) GetClosureRequest() *SavingsAccountClosureRequest {
	if x != nil {
		return x.ClosureRequest
	}
	return nil
}

type UpdateSaClosureRequestStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// primary id of the sa closure request table to update
	ClosureRequestId string                       `protobuf:"bytes,1,opt,name=closure_request_id,json=closureRequestId,proto3" json:"closure_request_id,omitempty"`
	Status           SAClosureRequestStatus       `protobuf:"varint,2,opt,name=status,proto3,enum=savings.SAClosureRequestStatus" json:"status,omitempty"`
	StatusReason     SAClosureRequestStatusReason `protobuf:"varint,3,opt,name=status_reason,json=statusReason,proto3,enum=savings.SAClosureRequestStatusReason" json:"status_reason,omitempty"`
}

func (x *UpdateSaClosureRequestStatusRequest) Reset() {
	*x = UpdateSaClosureRequestStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSaClosureRequestStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSaClosureRequestStatusRequest) ProtoMessage() {}

func (x *UpdateSaClosureRequestStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSaClosureRequestStatusRequest.ProtoReflect.Descriptor instead.
func (*UpdateSaClosureRequestStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{58}
}

func (x *UpdateSaClosureRequestStatusRequest) GetClosureRequestId() string {
	if x != nil {
		return x.ClosureRequestId
	}
	return ""
}

func (x *UpdateSaClosureRequestStatusRequest) GetStatus() SAClosureRequestStatus {
	if x != nil {
		return x.Status
	}
	return SAClosureRequestStatus_SA_CLOSURE_REQUEST_STATUS_UNSPECIFIED
}

func (x *UpdateSaClosureRequestStatusRequest) GetStatusReason() SAClosureRequestStatusReason {
	if x != nil {
		return x.StatusReason
	}
	return SAClosureRequestStatusReason_SA_CLOSURE_REQUEST_STATUS_REASON_UNSPECIFIED
}

type UpdateSaClosureRequestStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpdateSaClosureRequestStatusResponse) Reset() {
	*x = UpdateSaClosureRequestStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSaClosureRequestStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSaClosureRequestStatusResponse) ProtoMessage() {}

func (x *UpdateSaClosureRequestStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSaClosureRequestStatusResponse.ProtoReflect.Descriptor instead.
func (*UpdateSaClosureRequestStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{59}
}

func (x *UpdateSaClosureRequestStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type RecordSaClosureUserFeedbackRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// primary id of the sa closure request table to update
	ClosureRequestId string                        `protobuf:"bytes,1,opt,name=closure_request_id,json=closureRequestId,proto3" json:"closure_request_id,omitempty"`
	UserFeedback     *SaClosureRequestUserFeedback `protobuf:"bytes,2,opt,name=user_feedback,json=userFeedback,proto3" json:"user_feedback,omitempty"`
}

func (x *RecordSaClosureUserFeedbackRequest) Reset() {
	*x = RecordSaClosureUserFeedbackRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecordSaClosureUserFeedbackRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecordSaClosureUserFeedbackRequest) ProtoMessage() {}

func (x *RecordSaClosureUserFeedbackRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecordSaClosureUserFeedbackRequest.ProtoReflect.Descriptor instead.
func (*RecordSaClosureUserFeedbackRequest) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{60}
}

func (x *RecordSaClosureUserFeedbackRequest) GetClosureRequestId() string {
	if x != nil {
		return x.ClosureRequestId
	}
	return ""
}

func (x *RecordSaClosureUserFeedbackRequest) GetUserFeedback() *SaClosureRequestUserFeedback {
	if x != nil {
		return x.UserFeedback
	}
	return nil
}

type RecordSaClosureUserFeedbackResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *RecordSaClosureUserFeedbackResponse) Reset() {
	*x = RecordSaClosureUserFeedbackResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecordSaClosureUserFeedbackResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecordSaClosureUserFeedbackResponse) ProtoMessage() {}

func (x *RecordSaClosureUserFeedbackResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecordSaClosureUserFeedbackResponse.ProtoReflect.Descriptor instead.
func (*RecordSaClosureUserFeedbackResponse) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{61}
}

func (x *RecordSaClosureUserFeedbackResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetSubmittedSaClosureRequestsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageContext   *rpc.PageContextRequest `protobuf:"bytes,1,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
	FromTimestamp *timestamppb.Timestamp  `protobuf:"bytes,2,opt,name=from_timestamp,json=fromTimestamp,proto3" json:"from_timestamp,omitempty"`
	ToTimestamp   *timestamppb.Timestamp  `protobuf:"bytes,3,opt,name=to_timestamp,json=toTimestamp,proto3" json:"to_timestamp,omitempty"`
}

func (x *GetSubmittedSaClosureRequestsRequest) Reset() {
	*x = GetSubmittedSaClosureRequestsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSubmittedSaClosureRequestsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSubmittedSaClosureRequestsRequest) ProtoMessage() {}

func (x *GetSubmittedSaClosureRequestsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSubmittedSaClosureRequestsRequest.ProtoReflect.Descriptor instead.
func (*GetSubmittedSaClosureRequestsRequest) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{62}
}

func (x *GetSubmittedSaClosureRequestsRequest) GetPageContext() *rpc.PageContextRequest {
	if x != nil {
		return x.PageContext
	}
	return nil
}

func (x *GetSubmittedSaClosureRequestsRequest) GetFromTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.FromTimestamp
	}
	return nil
}

func (x *GetSubmittedSaClosureRequestsRequest) GetToTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.ToTimestamp
	}
	return nil
}

type GetSubmittedSaClosureRequestsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                        *rpc.Status                     `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	PageContext                   *rpc.PageContextResponse        `protobuf:"bytes,2,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
	SavingsAccountClosureRequests []*SavingsAccountClosureRequest `protobuf:"bytes,3,rep,name=savings_account_closure_requests,json=savingsAccountClosureRequests,proto3" json:"savings_account_closure_requests,omitempty"`
}

func (x *GetSubmittedSaClosureRequestsResponse) Reset() {
	*x = GetSubmittedSaClosureRequestsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSubmittedSaClosureRequestsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSubmittedSaClosureRequestsResponse) ProtoMessage() {}

func (x *GetSubmittedSaClosureRequestsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSubmittedSaClosureRequestsResponse.ProtoReflect.Descriptor instead.
func (*GetSubmittedSaClosureRequestsResponse) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{63}
}

func (x *GetSubmittedSaClosureRequestsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetSubmittedSaClosureRequestsResponse) GetPageContext() *rpc.PageContextResponse {
	if x != nil {
		return x.PageContext
	}
	return nil
}

func (x *GetSubmittedSaClosureRequestsResponse) GetSavingsAccountClosureRequests() []*SavingsAccountClosureRequest {
	if x != nil {
		return x.SavingsAccountClosureRequests
	}
	return nil
}

// check for indexes in db before creating a new filter
type GetSaClosureRequestsByFilterRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageContext            *rpc.PageContextRequest                 `protobuf:"bytes,1,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
	FieldMasks             []SavingsAccountClosureRequestFieldMask `protobuf:"varint,2,rep,packed,name=field_masks,json=fieldMasks,proto3,enum=savings.SavingsAccountClosureRequestFieldMask" json:"field_masks,omitempty"`
	ActorIdFilter          []string                                `protobuf:"bytes,3,rep,name=actor_id_filter,json=actorIdFilter,proto3" json:"actor_id_filter,omitempty"`
	ClosureRequestIdFilter []string                                `protobuf:"bytes,4,rep,name=closure_request_id_filter,json=closureRequestIdFilter,proto3" json:"closure_request_id_filter,omitempty"`
	StatusFilter           []SAClosureRequestStatus                `protobuf:"varint,5,rep,packed,name=status_filter,json=statusFilter,proto3,enum=savings.SAClosureRequestStatus" json:"status_filter,omitempty"`
}

func (x *GetSaClosureRequestsByFilterRequest) Reset() {
	*x = GetSaClosureRequestsByFilterRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSaClosureRequestsByFilterRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSaClosureRequestsByFilterRequest) ProtoMessage() {}

func (x *GetSaClosureRequestsByFilterRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSaClosureRequestsByFilterRequest.ProtoReflect.Descriptor instead.
func (*GetSaClosureRequestsByFilterRequest) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{64}
}

func (x *GetSaClosureRequestsByFilterRequest) GetPageContext() *rpc.PageContextRequest {
	if x != nil {
		return x.PageContext
	}
	return nil
}

func (x *GetSaClosureRequestsByFilterRequest) GetFieldMasks() []SavingsAccountClosureRequestFieldMask {
	if x != nil {
		return x.FieldMasks
	}
	return nil
}

func (x *GetSaClosureRequestsByFilterRequest) GetActorIdFilter() []string {
	if x != nil {
		return x.ActorIdFilter
	}
	return nil
}

func (x *GetSaClosureRequestsByFilterRequest) GetClosureRequestIdFilter() []string {
	if x != nil {
		return x.ClosureRequestIdFilter
	}
	return nil
}

func (x *GetSaClosureRequestsByFilterRequest) GetStatusFilter() []SAClosureRequestStatus {
	if x != nil {
		return x.StatusFilter
	}
	return nil
}

type GetSaClosureRequestsByFilterResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                        *rpc.Status                     `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	PageContext                   *rpc.PageContextResponse        `protobuf:"bytes,2,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
	SavingsAccountClosureRequests []*SavingsAccountClosureRequest `protobuf:"bytes,3,rep,name=savings_account_closure_requests,json=savingsAccountClosureRequests,proto3" json:"savings_account_closure_requests,omitempty"`
}

func (x *GetSaClosureRequestsByFilterResponse) Reset() {
	*x = GetSaClosureRequestsByFilterResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSaClosureRequestsByFilterResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSaClosureRequestsByFilterResponse) ProtoMessage() {}

func (x *GetSaClosureRequestsByFilterResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSaClosureRequestsByFilterResponse.ProtoReflect.Descriptor instead.
func (*GetSaClosureRequestsByFilterResponse) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{65}
}

func (x *GetSaClosureRequestsByFilterResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetSaClosureRequestsByFilterResponse) GetPageContext() *rpc.PageContextResponse {
	if x != nil {
		return x.PageContext
	}
	return nil
}

func (x *GetSaClosureRequestsByFilterResponse) GetSavingsAccountClosureRequests() []*SavingsAccountClosureRequest {
	if x != nil {
		return x.SavingsAccountClosureRequests
	}
	return nil
}

type VerifyPanForAccountClosureRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	Pan     string `protobuf:"bytes,2,opt,name=pan,proto3" json:"pan,omitempty"`
}

func (x *VerifyPanForAccountClosureRequest) Reset() {
	*x = VerifyPanForAccountClosureRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyPanForAccountClosureRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyPanForAccountClosureRequest) ProtoMessage() {}

func (x *VerifyPanForAccountClosureRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyPanForAccountClosureRequest.ProtoReflect.Descriptor instead.
func (*VerifyPanForAccountClosureRequest) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{66}
}

func (x *VerifyPanForAccountClosureRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *VerifyPanForAccountClosureRequest) GetPan() string {
	if x != nil {
		return x.Pan
	}
	return ""
}

type VerifyPanForAccountClosureResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// in case of not ok response, number of attempts left is sent back
	AttemptsLeft int32 `protobuf:"varint,2,opt,name=attempts_left,json=attemptsLeft,proto3" json:"attempts_left,omitempty"`
}

func (x *VerifyPanForAccountClosureResponse) Reset() {
	*x = VerifyPanForAccountClosureResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyPanForAccountClosureResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyPanForAccountClosureResponse) ProtoMessage() {}

func (x *VerifyPanForAccountClosureResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyPanForAccountClosureResponse.ProtoReflect.Descriptor instead.
func (*VerifyPanForAccountClosureResponse) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{67}
}

func (x *VerifyPanForAccountClosureResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *VerifyPanForAccountClosureResponse) GetAttemptsLeft() int32 {
	if x != nil {
		return x.AttemptsLeft
	}
	return 0
}

type CreateAccountRequest_Options struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// flag mentioning if user gave consent to open min AMB account
	// savings account will be created with Regular tier schemes codes (with respective KYC scheme) if this flag is set
	ShouldOpenAmbAccount bool `protobuf:"varint,6,opt,name=should_open_amb_account,json=shouldOpenAmbAccount,proto3" json:"should_open_amb_account,omitempty"`
}

func (x *CreateAccountRequest_Options) Reset() {
	*x = CreateAccountRequest_Options{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAccountRequest_Options) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAccountRequest_Options) ProtoMessage() {}

func (x *CreateAccountRequest_Options) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAccountRequest_Options.ProtoReflect.Descriptor instead.
func (*CreateAccountRequest_Options) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{9, 0}
}

func (x *CreateAccountRequest_Options) GetShouldOpenAmbAccount() bool {
	if x != nil {
		return x.ShouldOpenAmbAccount
	}
	return false
}

// savings account for which monthly statement will be generated
//
// Deprecated: Marked as deprecated in api/savings/service.proto.
type GetListOfActiveAccountsResponse_Account struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// internal ID referencing the account.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// User id of the primary account holder
	PrimaryAccountHolderUserId string `protobuf:"bytes,4,opt,name=primary_account_holder_user_id,json=primaryAccountHolderUserId,proto3" json:"primary_account_holder_user_id,omitempty"`
}

func (x *GetListOfActiveAccountsResponse_Account) Reset() {
	*x = GetListOfActiveAccountsResponse_Account{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetListOfActiveAccountsResponse_Account) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetListOfActiveAccountsResponse_Account) ProtoMessage() {}

func (x *GetListOfActiveAccountsResponse_Account) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetListOfActiveAccountsResponse_Account.ProtoReflect.Descriptor instead.
func (*GetListOfActiveAccountsResponse_Account) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{29, 0}
}

func (x *GetListOfActiveAccountsResponse_Account) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *GetListOfActiveAccountsResponse_Account) GetPrimaryAccountHolderUserId() string {
	if x != nil {
		return x.PrimaryAccountHolderUserId
	}
	return ""
}

type IsTxnAllowedResponse_SavingsTxnLimits struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// denotes the remaining/allowed credit limit percentage rounded down
	// deprecated in favour of allowed_credit_limit_percent_float
	//
	// Deprecated: Marked as deprecated in api/savings/service.proto.
	AllowedCreditLimitPercent int32 `protobuf:"varint,1,opt,name=allowed_credit_limit_percent,json=allowedCreditLimitPercent,proto3" json:"allowed_credit_limit_percent,omitempty"`
	// denotes the remaining/allowed credit limit amount
	AllowedCreditLimitAmount *money.Money `protobuf:"bytes,2,opt,name=allowed_credit_limit_amount,json=allowedCreditLimitAmount,proto3" json:"allowed_credit_limit_amount,omitempty"`
	// denotes the remaining/allowed savings balance limit percentage rounded down
	// deprecated in favour of allowed_savings_limit_percent_float
	//
	// Deprecated: Marked as deprecated in api/savings/service.proto.
	AllowedSavingsLimitPercent int32 `protobuf:"varint,3,opt,name=allowed_savings_limit_percent,json=allowedSavingsLimitPercent,proto3" json:"allowed_savings_limit_percent,omitempty"`
	// denotes the remaining/allowed savings balance limit amount
	AllowedSavingsLimitAmount *money.Money `protobuf:"bytes,4,opt,name=allowed_savings_limit_amount,json=allowedSavingsLimitAmount,proto3" json:"allowed_savings_limit_amount,omitempty"`
	// denotes the max savings balance amount a user is allowed to have
	MaxAllowedSavingsLimitAmount *money.Money `protobuf:"bytes,5,opt,name=max_allowed_savings_limit_amount,json=maxAllowedSavingsLimitAmount,proto3" json:"max_allowed_savings_limit_amount,omitempty"`
	// denotes the total credit amount a user is allowed to have
	TotalAllowedCreditLimitAmount *money.Money `protobuf:"bytes,6,opt,name=total_allowed_credit_limit_amount,json=totalAllowedCreditLimitAmount,proto3" json:"total_allowed_credit_limit_amount,omitempty"`
	// denotes the remaining/allowed credit limit percentage, (might loose precision)
	AllowedCreditLimitPercentFloat float64 `protobuf:"fixed64,7,opt,name=allowed_credit_limit_percent_float,json=allowedCreditLimitPercentFloat,proto3" json:"allowed_credit_limit_percent_float,omitempty"`
	// denotes the remaining/allowed savings balance limit, (might loose precision)
	AllowedSavingsLimitPercentFloat float64 `protobuf:"fixed64,8,opt,name=allowed_savings_limit_percent_float,json=allowedSavingsLimitPercentFloat,proto3" json:"allowed_savings_limit_percent_float,omitempty"`
}

func (x *IsTxnAllowedResponse_SavingsTxnLimits) Reset() {
	*x = IsTxnAllowedResponse_SavingsTxnLimits{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_service_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsTxnAllowedResponse_SavingsTxnLimits) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsTxnAllowedResponse_SavingsTxnLimits) ProtoMessage() {}

func (x *IsTxnAllowedResponse_SavingsTxnLimits) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_service_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsTxnAllowedResponse_SavingsTxnLimits.ProtoReflect.Descriptor instead.
func (*IsTxnAllowedResponse_SavingsTxnLimits) Descriptor() ([]byte, []int) {
	return file_api_savings_service_proto_rawDescGZIP(), []int{35, 0}
}

// Deprecated: Marked as deprecated in api/savings/service.proto.
func (x *IsTxnAllowedResponse_SavingsTxnLimits) GetAllowedCreditLimitPercent() int32 {
	if x != nil {
		return x.AllowedCreditLimitPercent
	}
	return 0
}

func (x *IsTxnAllowedResponse_SavingsTxnLimits) GetAllowedCreditLimitAmount() *money.Money {
	if x != nil {
		return x.AllowedCreditLimitAmount
	}
	return nil
}

// Deprecated: Marked as deprecated in api/savings/service.proto.
func (x *IsTxnAllowedResponse_SavingsTxnLimits) GetAllowedSavingsLimitPercent() int32 {
	if x != nil {
		return x.AllowedSavingsLimitPercent
	}
	return 0
}

func (x *IsTxnAllowedResponse_SavingsTxnLimits) GetAllowedSavingsLimitAmount() *money.Money {
	if x != nil {
		return x.AllowedSavingsLimitAmount
	}
	return nil
}

func (x *IsTxnAllowedResponse_SavingsTxnLimits) GetMaxAllowedSavingsLimitAmount() *money.Money {
	if x != nil {
		return x.MaxAllowedSavingsLimitAmount
	}
	return nil
}

func (x *IsTxnAllowedResponse_SavingsTxnLimits) GetTotalAllowedCreditLimitAmount() *money.Money {
	if x != nil {
		return x.TotalAllowedCreditLimitAmount
	}
	return nil
}

func (x *IsTxnAllowedResponse_SavingsTxnLimits) GetAllowedCreditLimitPercentFloat() float64 {
	if x != nil {
		return x.AllowedCreditLimitPercentFloat
	}
	return 0
}

func (x *IsTxnAllowedResponse_SavingsTxnLimits) GetAllowedSavingsLimitPercentFloat() float64 {
	if x != nil {
		return x.AllowedSavingsLimitPercentFloat
	}
	return 0
}

var File_api_savings_service_proto protoreflect.FileDescriptor

var file_api_savings_service_proto_rawDesc = []byte{
	0x0a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x73, 0x61, 0x76,
	0x69, 0x6e, 0x67, 0x73, 0x1a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69,
	0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2f, 0x64, 0x79, 0x6e, 0x61, 0x6d,
	0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x12, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x61, 0x67, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x61, 0x70, 0x69,
	0x2f, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x61, 0x76, 0x69,
	0x6e, 0x67, 0x73, 0x2f, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x73, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x66, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x61, 0x70, 0x69, 0x2f,
	0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73,
	0x2f, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x82, 0x01, 0x0a, 0x23, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53,
	0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x6f, 0x6d,
	0x69, 0x6e, 0x65, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x0f, 0x6e, 0x6f, 0x6d, 0x69, 0x6e,
	0x65, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x4e, 0x6f, 0x6d, 0x69, 0x6e,
	0x65, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0e, 0x6e, 0x6f, 0x6d, 0x69, 0x6e,
	0x65, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x4b, 0x0a, 0x24, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x3d, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x53, 0x61, 0x76,
	0x69, 0x6e, 0x67, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x6f, 0x6d, 0x69, 0x6e,
	0x65, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0xd6, 0x01, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x53, 0x61, 0x76,
	0x69, 0x6e, 0x67, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x6f, 0x6d, 0x69, 0x6e,
	0x65, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x32, 0x0a, 0x07, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52, 0x07, 0x6e, 0x6f, 0x6d,
	0x69, 0x6e, 0x65, 0x65, 0x12, 0x58, 0x0a, 0x18, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73,
	0x2e, 0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e,
	0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x52, 0x16, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x73, 0x22, 0x72,
	0x0a, 0x15, 0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x12, 0x2e, 0x0a, 0x07, 0x6e, 0x6f, 0x6d, 0x69, 0x6e,
	0x65, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x52, 0x07,
	0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x70, 0x65, 0x72, 0x63, 0x65,
	0x6e, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x68, 0x61, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x53, 0x68, 0x61,
	0x72, 0x65, 0x22, 0x75, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x42, 0x0a, 0x0f, 0x70, 0x72, 0x65, 0x76, 0x5f, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x70, 0x72, 0x65, 0x76,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x6b, 0x0a, 0x15, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x2d, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54,
	0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45,
	0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0xaf, 0x04, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x56, 0x31, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x41, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x73,
	0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x48, 0x00, 0x52, 0x0a,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x58, 0x0a, 0x0e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x72,
	0x65, 0x73, 0x68, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e,
	0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x56, 0x31, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x46, 0x72, 0x65, 0x73, 0x68, 0x6e, 0x65, 0x73, 0x73,
	0x52, 0x0d, 0x64, 0x61, 0x74, 0x61, 0x46, 0x72, 0x65, 0x73, 0x68, 0x6e, 0x65, 0x73, 0x73, 0x12,
	0x5f, 0x0a, 0x11, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x61, 0x70, 0x69, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x73, 0x61, 0x76,
	0x69, 0x6e, 0x67, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x56, 0x31, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x41, 0x70, 0x69, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x41, 0x70, 0x69, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x22, 0x8d, 0x01, 0x0a, 0x0d, 0x44, 0x61, 0x74, 0x61, 0x46, 0x72, 0x65, 0x73, 0x68, 0x6e, 0x65,
	0x73, 0x73, 0x12, 0x1e, 0x0a, 0x1a, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x46, 0x52, 0x45, 0x53, 0x48,
	0x4e, 0x45, 0x53, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x52, 0x45, 0x41, 0x4c, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10,
	0x01, 0x12, 0x12, 0x0a, 0x0e, 0x4e, 0x45, 0x41, 0x52, 0x5f, 0x52, 0x45, 0x41, 0x4c, 0x5f, 0x54,
	0x49, 0x4d, 0x45, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x53, 0x54, 0x41, 0x4c, 0x45, 0x10, 0x03,
	0x12, 0x0e, 0x0a, 0x0a, 0x48, 0x49, 0x53, 0x54, 0x4f, 0x52, 0x49, 0x43, 0x41, 0x4c, 0x10, 0x04,
	0x12, 0x1e, 0x0a, 0x1a, 0x4c, 0x41, 0x53, 0x54, 0x5f, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x42,
	0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x46, 0x52, 0x4f, 0x4d, 0x5f, 0x44, 0x42, 0x10, 0x05,
	0x22, 0x48, 0x0a, 0x0f, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x41, 0x70, 0x69, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x1d, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x41, 0x50,
	0x49, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x4e, 0x45, 0x57, 0x5f, 0x56, 0x45,
	0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x41, 0x50, 0x49, 0x10, 0x01, 0x42, 0x0c, 0x0a, 0x0a, 0x69, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0xc2, 0x04, 0x0a, 0x1b, 0x47, 0x65, 0x74,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x56, 0x31,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x39, 0x0a,
	0x0e, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0d, 0x6c, 0x65, 0x64, 0x67, 0x65,
	0x72, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x3f, 0x0a, 0x11, 0x61, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x10, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x3f, 0x0a, 0x11, 0x63, 0x6c, 0x65,
	0x61, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x10, 0x63, 0x6c, 0x65, 0x61, 0x72, 0x61,
	0x6e, 0x63, 0x65, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x35, 0x0a, 0x0c, 0x6c, 0x69,
	0x65, 0x6e, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0b, 0x6c, 0x69, 0x65, 0x6e, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x12, 0x39, 0x0a, 0x0a, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x61, 0x74, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x41, 0x74, 0x12, 0x26, 0x0a, 0x0f,
	0x66, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x5f, 0x72, 0x61, 0x77, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x52, 0x61, 0x77,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x5f, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x72, 0x65,
	0x65, 0x7a, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x81, 0x01, 0x0a, 0x06, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09,
	0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x15, 0x0a, 0x11, 0x50,
	0x45, 0x52, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x4e, 0x49, 0x45, 0x44,
	0x10, 0x07, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d,
	0x12, 0x1e, 0x0a, 0x1a, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4e, 0x55, 0x4d, 0x42,
	0x45, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x64,
	0x12, 0x1b, 0x0a, 0x17, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4e, 0x4f, 0x5f, 0x4c,
	0x45, 0x56, 0x45, 0x4c, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x65, 0x22, 0xc6, 0x03,
	0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x39, 0x0a, 0x19, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72,
	0x79, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x70, 0x72, 0x69, 0x6d, 0x61,
	0x72, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x72, 0x65, 0x71, 0x49, 0x64, 0x12, 0x4f, 0x0a, 0x0b, 0x66, 0x6f, 0x72, 0x63,
	0x65, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e,
	0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x6f, 0x72,
	0x63, 0x65, 0x52, 0x65, 0x74, 0x72, 0x79, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x66,
	0x6f, 0x72, 0x63, 0x65, 0x52, 0x65, 0x74, 0x72, 0x79, 0x12, 0x1e, 0x0a, 0x03, 0x73, 0x6b, 0x75,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0c, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73,
	0x2e, 0x53, 0x4b, 0x55, 0x52, 0x03, 0x73, 0x6b, 0x75, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x3f, 0x0a, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x07, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x40, 0x0a, 0x07, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x35, 0x0a, 0x17, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x5f,
	0x61, 0x6d, 0x62, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x14, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x4f, 0x70, 0x65, 0x6e, 0x41, 0x6d, 0x62,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x4d, 0x0a, 0x10, 0x46, 0x6f, 0x72, 0x63, 0x65,
	0x52, 0x65, 0x74, 0x72, 0x79, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0f, 0x0a, 0x0b, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11,
	0x46, 0x4f, 0x52, 0x43, 0x45, 0x5f, 0x4e, 0x45, 0x57, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x46, 0x4f, 0x52, 0x43, 0x45, 0x5f, 0x49, 0x4e, 0x51,
	0x55, 0x49, 0x52, 0x59, 0x10, 0x02, 0x22, 0x95, 0x01, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x2a, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0x2b, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f,
	0x4b, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x44,
	0x45, 0x44, 0x55, 0x50, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x64, 0x22, 0x53,
	0x0a, 0x15, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x66, 0x73, 0x63, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x66, 0x73, 0x63, 0x43,
	0x6f, 0x64, 0x65, 0x22, 0x94, 0x03, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x45, 0x0a, 0x0b, 0x65,
	0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72,
	0x42, 0x02, 0x18, 0x01, 0x48, 0x00, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0f, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x48,
	0x00, 0x52, 0x0d, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x1f, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x48, 0x00, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x12, 0x59, 0x0a, 0x17, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d,
	0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x20, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x48, 0x00, 0x52, 0x14, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e,
	0x75, 0x6d, 0x42, 0x61, 0x6e, 0x6b, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x6e, 0x0a, 0x1f,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e,
	0x41, 0x63, 0x74, 0x6f, 0x72, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x48, 0x00, 0x52, 0x1c,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x42, 0x0c, 0x0a, 0x0a,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0x65, 0x0a, 0x12, 0x47, 0x65,
	0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x2a, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0xbb, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3f, 0x0a, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79,
	0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x42, 0x02,
	0x18, 0x01, 0x48, 0x00, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x52, 0x0a,
	0x15, 0x62, 0x75, 0x6c, 0x6b, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x73,
	0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x42, 0x75, 0x6c, 0x6b, 0x41, 0x63, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x48, 0x00, 0x52, 0x13, 0x62, 0x75,
	0x6c, 0x6b, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x42, 0x0c, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22,
	0x9b, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x2c, 0x0a, 0x08, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x10, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x08, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x22, 0x2d,
	0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00,
	0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12,
	0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0xb1, 0x01,
	0x0a, 0x15, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x10, 0x70, 0x72, 0x69, 0x6d, 0x61,
	0x72, 0x79, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0e, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x73, 0x12, 0x6e, 0x0a, 0x1d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x1a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x73,
	0x74, 0x22, 0xd7, 0x01, 0x0a, 0x13, 0x42, 0x75, 0x6c, 0x6b, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x73, 0x12, 0x67, 0x0a, 0x19, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x17, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x73, 0x12,
	0x3a, 0x0a, 0x0d, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x0c, 0x70,
	0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x73, 0x22, 0xda, 0x01, 0x0a, 0x1c,
	0x41, 0x63, 0x74, 0x6f, 0x72, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x65, 0x0a, 0x18, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x16, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x38,
	0x0a, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x0b, 0x70, 0x61, 0x72,
	0x74, 0x6e, 0x65, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x22, 0xcf, 0x02, 0x0a, 0x18, 0x47, 0x65, 0x74,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x41, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x73,
	0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x48, 0x00, 0x52, 0x0a,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x56, 0x0a, 0x0e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x72,
	0x65, 0x73, 0x68, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2f, 0x2e,
	0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x44, 0x61, 0x74, 0x61, 0x46, 0x72, 0x65, 0x73, 0x68, 0x6e, 0x65, 0x73, 0x73, 0x52, 0x0d,
	0x64, 0x61, 0x74, 0x61, 0x46, 0x72, 0x65, 0x73, 0x68, 0x6e, 0x65, 0x73, 0x73, 0x22, 0x5d, 0x0a,
	0x0d, 0x44, 0x61, 0x74, 0x61, 0x46, 0x72, 0x65, 0x73, 0x68, 0x6e, 0x65, 0x73, 0x73, 0x12, 0x1e,
	0x0a, 0x1a, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x46, 0x52, 0x45, 0x53, 0x48, 0x4e, 0x45, 0x53, 0x53,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0d,
	0x0a, 0x09, 0x52, 0x45, 0x41, 0x4c, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x01, 0x12, 0x12, 0x0a,
	0x0e, 0x4e, 0x45, 0x41, 0x52, 0x5f, 0x52, 0x45, 0x41, 0x4c, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10,
	0x02, 0x12, 0x09, 0x0a, 0x05, 0x53, 0x54, 0x41, 0x4c, 0x45, 0x10, 0x03, 0x42, 0x0c, 0x0a, 0x0a,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0x84, 0x03, 0x0a, 0x19, 0x47,
	0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3f, 0x0a,
	0x11, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x10, 0x61, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x39,
	0x0a, 0x0e, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0d, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x72, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x42, 0x0a, 0x0f, 0x6c, 0x61, 0x73,
	0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d,
	0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x81, 0x01,
	0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00,
	0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12,
	0x15, 0x0a, 0x11, 0x50, 0x45, 0x52, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45,
	0x4e, 0x49, 0x45, 0x44, 0x10, 0x07, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e,
	0x41, 0x4c, 0x10, 0x0d, 0x12, 0x1e, 0x0a, 0x1a, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f,
	0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54,
	0x45, 0x44, 0x10, 0x64, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f,
	0x4e, 0x4f, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10,
	0x65, 0x22, 0xcb, 0x04, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x41, 0x0a, 0x0b,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x42, 0x61, 0x6e, 0x6b,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x48, 0x00, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12,
	0x36, 0x0a, 0x16, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x00, 0x52, 0x14, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x07, 0x62, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x3d, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61,
	0x69, 0x6e, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x73, 0x61, 0x76,
	0x69, 0x6e, 0x67, 0x73, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x73,
	0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61,
	0x69, 0x6e, 0x74, 0x73, 0x12, 0x24, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x19,
	0x0a, 0x08, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x3a, 0x0a, 0x0b, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x19,
	0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x22, 0x0a, 0x03, 0x73, 0x6b, 0x75, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x0c, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x53, 0x4b, 0x55,
	0x42, 0x02, 0x18, 0x01, 0x52, 0x03, 0x73, 0x6b, 0x75, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x08, 0x73, 0x6b, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73,
	0x2e, 0x53, 0x4b, 0x55, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x73, 0x6b, 0x75, 0x49, 0x6e, 0x66,
	0x6f, 0x42, 0x0c, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22,
	0x97, 0x01, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2a,
	0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x2d, 0x0a, 0x06, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09,
	0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49,
	0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x95, 0x02, 0x0a, 0x18, 0x47, 0x65,
	0x74, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x41, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x48, 0x00, 0x52,
	0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x1c, 0x70,
	0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x68,
	0x6f, 0x6c, 0x64, 0x65, 0x72, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x19, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x2f,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12,
	0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x42, 0x0c, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x22, 0xc3, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x6e, 0x67,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x3b, 0x0a, 0x0f, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x5f,
	0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x0e, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x22, 0x44, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f,
	0x4b, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44,
	0x10, 0x05, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x45, 0x52, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f, 0x4e,
	0x5f, 0x44, 0x45, 0x4e, 0x49, 0x45, 0x44, 0x10, 0x07, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54,
	0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0xe4, 0x04, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x57, 0x69, 0x74,
	0x68, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x10, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x41, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73,
	0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x48, 0x00, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x1c, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x5f, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x19, 0x70, 0x72,
	0x69, 0x6d, 0x61, 0x72, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x48, 0x6f, 0x6c, 0x64,
	0x65, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x12, 0x5b, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x57, 0x69, 0x74, 0x68, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82,
	0x01, 0x02, 0x20, 0x00, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12,
	0x71, 0x0a, 0x14, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e,
	0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x57, 0x69, 0x74, 0x68, 0x53, 0x75, 0x6d,
	0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x6f, 0x72, 0x63,
	0x65, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x12,
	0x66, 0x6f, 0x72, 0x63, 0x65, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x22, 0x29, 0x0a, 0x05, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x15, 0x0a, 0x11, 0x52,
	0x41, 0x4e, 0x47, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x4d, 0x4f, 0x4e, 0x54, 0x48, 0x10, 0x01, 0x22, 0x80, 0x01,
	0x0a, 0x12, 0x46, 0x6f, 0x72, 0x63, 0x65, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x20, 0x46, 0x4f, 0x52, 0x43, 0x45, 0x5f, 0x42, 0x41,
	0x4c, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x46, 0x4f,
	0x52, 0x43, 0x45, 0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x55, 0x50, 0x44, 0x41,
	0x54, 0x45, 0x5f, 0x4e, 0x45, 0x45, 0x44, 0x45, 0x44, 0x10, 0x01, 0x12, 0x23, 0x0a, 0x1f, 0x46,
	0x4f, 0x52, 0x43, 0x45, 0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x55, 0x50, 0x44,
	0x41, 0x54, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x4e, 0x45, 0x45, 0x44, 0x45, 0x44, 0x10, 0x02,
	0x42, 0x0c, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0xde,
	0x05, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x57, 0x69, 0x74, 0x68, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3b, 0x0a, 0x0f,
	0x6f, 0x70, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x6f, 0x70, 0x65, 0x6e, 0x69,
	0x6e, 0x67, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x3f, 0x0a, 0x11, 0x61, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x10, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x6c, 0x65, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x39, 0x0a, 0x0e, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0d, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x72, 0x42, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x37, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x73,
	0x70, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x53, 0x70, 0x65, 0x6e, 0x74, 0x12, 0x35,
	0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x12, 0x33, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x64,
	0x65, 0x62, 0x69, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0a,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x44, 0x65, 0x62, 0x69, 0x74, 0x12, 0x4f, 0x0a, 0x0e, 0x62, 0x75,
	0x63, 0x6b, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x08, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x24, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x42, 0x75, 0x63, 0x6b, 0x65,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0d, 0x62, 0x75,
	0x63, 0x6b, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x39, 0x0a, 0x19, 0x69,
	0x73, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x6c, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16,
	0x69, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x53, 0x74, 0x61, 0x6c, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x41,
	0x74, 0x12, 0x28, 0x0a, 0x10, 0x69, 0x73, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f,
	0x73, 0x74, 0x61, 0x6c, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x69, 0x73, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x61, 0x6c, 0x65, 0x22, 0x42, 0x0a, 0x06, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a,
	0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x22, 0x0a, 0x1e, 0x44,
	0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4f, 0x52, 0x41, 0x52, 0x49, 0x4c,
	0x59, 0x5f, 0x44, 0x45, 0x41, 0x43, 0x54, 0x49, 0x56, 0x41, 0x54, 0x45, 0x44, 0x10, 0x64, 0x22,
	0xb9, 0x02, 0x0a, 0x1b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x43, 0x0a, 0x06, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2b, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x06, 0x62, 0x75,
	0x63, 0x6b, 0x65, 0x74, 0x12, 0x28, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x25,
	0x0a, 0x0e, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x40, 0x0a, 0x08, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67,
	0x73, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79,
	0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x08, 0x73,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x42, 0x0a, 0x06, 0x42, 0x75, 0x63, 0x6b, 0x65,
	0x74, 0x12, 0x16, 0x0a, 0x12, 0x42, 0x55, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x52, 0x45,
	0x44, 0x49, 0x54, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x53, 0x50, 0x45, 0x4e, 0x54, 0x10, 0x02,
	0x12, 0x09, 0x0a, 0x05, 0x53, 0x41, 0x56, 0x45, 0x44, 0x10, 0x03, 0x22, 0xd9, 0x01, 0x0a, 0x1e,
	0x47, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x66, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x41,
	0x0a, 0x0e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x0d, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x65, 0x66, 0x6f, 0x72,
	0x65, 0x12, 0x3f, 0x0a, 0x0d, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x66, 0x74,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x66, 0x74,
	0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x22, 0xfe, 0x02, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x4f, 0x66, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4e, 0x0a, 0x07, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x73,
	0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x66,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x33, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73,
	0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x6f, 0x66,
	0x66, 0x73, 0x65, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74,
	0x4f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x1a, 0x61, 0x0a, 0x07, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x42, 0x0a, 0x1e, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x5f, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1a, 0x70, 0x72, 0x69, 0x6d, 0x61,
	0x72, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x3a, 0x02, 0x18, 0x01, 0x22, 0x2d, 0x0a, 0x06, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4e,
	0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e,
	0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0xa2, 0x01, 0x0a, 0x13, 0x43, 0x6c, 0x6f,
	0x73, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x10, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x41, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67,
	0x73, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x48, 0x00, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0f, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79,
	0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00,
	0x52, 0x0d, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x42,
	0x0c, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0x67, 0x0a,
	0x14, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2a, 0x0a, 0x07, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x73, 0x61,
	0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x07, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xa3, 0x01, 0x0a, 0x14, 0x52, 0x65, 0x6f, 0x70, 0x65,
	0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x10, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x41, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73,
	0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x48, 0x00, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0f, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52,
	0x0d, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x42, 0x0c,
	0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0x68, 0x0a, 0x15,
	0x52, 0x65, 0x6f, 0x70, 0x65, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2a, 0x0a, 0x07, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x73, 0x61,
	0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x07, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xe4, 0x02, 0x0a, 0x13, 0x49, 0x73, 0x54, 0x78, 0x6e,
	0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x41, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e,
	0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x48, 0x00, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x1c, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x5f, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x19, 0x70, 0x72, 0x69,
	0x6d, 0x61, 0x72, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x48, 0x6f, 0x6c, 0x64, 0x65,
	0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x3f, 0x0a, 0x08, 0x74, 0x78, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x49,
	0x73, 0x54, 0x78, 0x6e, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x54, 0x78, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x74, 0x78, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x22, 0x3a, 0x0a, 0x07, 0x54, 0x78, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18,
	0x0a, 0x14, 0x54, 0x58, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x52, 0x45, 0x44,
	0x49, 0x54, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x44, 0x45, 0x42, 0x49, 0x54, 0x10, 0x02, 0x42,
	0x0c, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0x83, 0x0b,
	0x0a, 0x14, 0x49, 0x73, 0x54, 0x78, 0x6e, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x46, 0x0a, 0x15, 0x63,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x61, 0x6c, 0x6c,
	0x6f, 0x77, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x13,
	0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x6c, 0x6c, 0x6f,
	0x77, 0x65, 0x64, 0x12, 0x4a, 0x0a, 0x13, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x66,
	0x72, 0x65, 0x65, 0x7a, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x11, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x46, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x52, 0x0a, 0x12, 0x6e, 0x6f, 0x74, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x73, 0x61,
	0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x49, 0x73, 0x54, 0x78, 0x6e, 0x41, 0x6c, 0x6c, 0x6f, 0x77,
	0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x52, 0x10, 0x6e, 0x6f, 0x74, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x52, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x12, 0x5c, 0x0a, 0x12, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x5f, 0x74,
	0x78, 0x6e, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2e, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x49, 0x73, 0x54, 0x78, 0x6e, 0x41,
	0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53,
	0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x54, 0x78, 0x6e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x52,
	0x10, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x54, 0x78, 0x6e, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x73, 0x1a, 0x9a, 0x05, 0x0a, 0x10, 0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x54, 0x78, 0x6e,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x12, 0x43, 0x0a, 0x1c, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65,
	0x64, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x70,
	0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x02, 0x18, 0x01,
	0x52, 0x19, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x12, 0x51, 0x0a, 0x1b, 0x61,
	0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x18, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x45,
	0x0a, 0x1d, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67,
	0x73, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x02, 0x18, 0x01, 0x52, 0x1a, 0x61, 0x6c, 0x6c, 0x6f, 0x77,
	0x65, 0x64, 0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x50, 0x65,
	0x72, 0x63, 0x65, 0x6e, 0x74, 0x12, 0x53, 0x0a, 0x1c, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64,
	0x5f, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x19, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x5a, 0x0a, 0x20, 0x6d, 0x61,
	0x78, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67,
	0x73, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x1c, 0x6d, 0x61, 0x78, 0x41, 0x6c, 0x6c,
	0x6f, 0x77, 0x65, 0x64, 0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x5c, 0x0a, 0x21, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x1d, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6c, 0x6c, 0x6f,
	0x77, 0x65, 0x64, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4a, 0x0a, 0x22, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f,
	0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x70, 0x65, 0x72,
	0x63, 0x65, 0x6e, 0x74, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x1e, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x46, 0x6c, 0x6f, 0x61, 0x74,
	0x12, 0x4c, 0x0a, 0x23, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x73, 0x61, 0x76, 0x69,
	0x6e, 0x67, 0x73, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e,
	0x74, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x01, 0x52, 0x1f, 0x61,
	0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x22, 0x54,
	0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00,
	0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12,
	0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x25, 0x0a,
	0x21, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x4c, 0x4c, 0x4f, 0x57, 0x45, 0x44, 0x5f, 0x4d, 0x49, 0x4e,
	0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55,
	0x52, 0x45, 0x10, 0x65, 0x22, 0x8c, 0x02, 0x0a, 0x06, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12,
	0x16, 0x0a, 0x12, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2a, 0x0a, 0x26, 0x4d, 0x49, 0x4e, 0x5f, 0x4b,
	0x59, 0x43, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x44, 0x55, 0x52, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52,
	0x45, 0x10, 0x01, 0x12, 0x25, 0x0a, 0x21, 0x4d, 0x49, 0x4e, 0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x4d,
	0x41, 0x58, 0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x02, 0x12, 0x2a, 0x0a, 0x26, 0x4d, 0x49,
	0x4e, 0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x4d, 0x41, 0x58, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54,
	0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x46, 0x41, 0x49,
	0x4c, 0x55, 0x52, 0x45, 0x10, 0x03, 0x12, 0x32, 0x0a, 0x2e, 0x4d, 0x49, 0x4e, 0x5f, 0x4b, 0x59,
	0x43, 0x5f, 0x4d, 0x41, 0x58, 0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x43, 0x48,
	0x45, 0x43, 0x4b, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x5f,
	0x4e, 0x45, 0x57, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x10, 0x04, 0x12, 0x37, 0x0a, 0x33, 0x4d, 0x49,
	0x4e, 0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x4d, 0x41, 0x58, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54,
	0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x46, 0x41, 0x49,
	0x4c, 0x55, 0x52, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x4e, 0x45, 0x57, 0x5f, 0x55, 0x53, 0x45,
	0x52, 0x10, 0x05, 0x22, 0x65, 0x0a, 0x28, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x43, 0x6c, 0x6f, 0x73,
	0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x66, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x39, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x66, 0x65, 0x72, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x50, 0x0a, 0x29, 0x53, 0x74,
	0x6f, 0x72, 0x65, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x42, 0x61, 0x6c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x65, 0x0a, 0x35,
	0x53, 0x74, 0x6f, 0x72, 0x65, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x42, 0x61, 0x6c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x44, 0x61, 0x74,
	0x61, 0x46, 0x72, 0x6f, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x49, 0x64, 0x22, 0x5d, 0x0a, 0x36, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x43, 0x6c, 0x6f, 0x73,
	0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x66, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x46, 0x72, 0x6f, 0x6d, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x22, 0x56, 0x0a, 0x26, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65,
	0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x12,
	0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67,
	0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x8f, 0x01, 0x0a, 0x27, 0x47,
	0x65, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42,
	0x61, 0x6c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3f, 0x0a, 0x07, 0x65,
	0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x73,
	0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x66, 0x65, 0x72, 0x52, 0x07, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x22, 0xad, 0x01, 0x0a,
	0x1e, 0x47, 0x65, 0x74, 0x45, 0x4f, 0x44, 0x53, 0x61, 0x76, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x88, 0x01, 0x0a,
	0x1f, 0x47, 0x65, 0x74, 0x45, 0x4f, 0x44, 0x53, 0x61, 0x76, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x40, 0x0a, 0x10, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x5f, 0x62, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x42, 0x79, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0e, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x42, 0x79, 0x44, 0x61, 0x74, 0x65, 0x73, 0x22, 0x6b, 0x0a, 0x0d, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x42, 0x79, 0x44, 0x61, 0x74, 0x65, 0x12, 0x25, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12,
	0x33, 0x0a, 0x0b, 0x65, 0x6f, 0x64, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0a, 0x65, 0x6f, 0x64, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x22, 0x9e, 0x01, 0x0a, 0x29, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43,
	0x6c, 0x6f, 0x73, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x39, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x25, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x43, 0x6c, 0x6f, 0x73, 0x65,
	0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x36, 0x0a,
	0x0b, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x15, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x43, 0x62, 0x74,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x4d, 0x61, 0x73, 0x6b, 0x73, 0x22, 0x51, 0x0a, 0x2a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43,
	0x6c, 0x6f, 0x73, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x3c, 0x0a, 0x1f, 0x46, 0x65, 0x74, 0x63,
	0x68, 0x4f, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x41, 0x74, 0x74,
	0x65, 0x6d, 0x70, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0xb3, 0x01, 0x0a, 0x20, 0x46, 0x65, 0x74, 0x63, 0x68,
	0x4f, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x41, 0x74, 0x74, 0x65,
	0x6d, 0x70, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x34, 0x0a, 0x0b, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e,
	0x53, 0x69, 0x67, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0a, 0x73, 0x69, 0x67, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x69, 0x67, 0x6e, 0x55, 0x72,
	0x6c, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x78, 0x69, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x78, 0x69, 0x74, 0x55, 0x72, 0x6c, 0x22, 0x68, 0x0a, 0x11,
	0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x42, 0x61, 0x6e, 0x6b, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x0c,
	0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x0b, 0x70, 0x61, 0x72, 0x74, 0x6e,
	0x65, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x22, 0x7a, 0x0a, 0x17, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x38, 0x0a, 0x0c, 0x70, 0x61, 0x72, 0x74,
	0x6e, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x0b, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x42, 0x61,
	0x6e, 0x6b, 0x22, 0xcc, 0x02, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x53, 0x61, 0x76, 0x69, 0x6e, 0x67,
	0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x73, 0x73, 0x65, 0x6e, 0x74, 0x69, 0x61,
	0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x51, 0x0a, 0x14, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67,
	0x73, 0x2e, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x42, 0x61, 0x6e, 0x6b, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x42, 0x02, 0x18, 0x01, 0x48, 0x00, 0x52, 0x11, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x42, 0x61, 0x6e, 0x6b, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x59, 0x0a, 0x17,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x5f, 0x62, 0x61, 0x6e, 0x6b,
	0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x48,
	0x00, 0x52, 0x14, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x42, 0x61, 0x6e,
	0x6b, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x6e, 0x0a, 0x1f, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x5f, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x25, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x41, 0x63, 0x74, 0x6f, 0x72,
	0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x48, 0x00, 0x52, 0x1c, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x42, 0x08, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x22, 0xb3, 0x02, 0x0a, 0x18, 0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x45, 0x73, 0x73, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x6f, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x6f, 0x12, 0x19, 0x0a,
	0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x66, 0x73, 0x63,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x66, 0x73,
	0x63, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x24, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x38, 0x0a, 0x0c, 0x70,
	0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x0b, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65,
	0x72, 0x42, 0x61, 0x6e, 0x6b, 0x12, 0x2b, 0x0a, 0x08, 0x73, 0x6b, 0x75, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67,
	0x73, 0x2e, 0x53, 0x4b, 0x55, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x73, 0x6b, 0x75, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x63, 0x68, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x63, 0x61, 0x63, 0x68, 0x65,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x87, 0x01, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x53,
	0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x73, 0x73,
	0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x3b, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e,
	0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x73,
	0x73, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x22, 0xdf, 0x01, 0x0a, 0x22, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x47, 0x65,
	0x74, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x44, 0x0a, 0x0b,
	0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x23, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x53, 0x41, 0x43, 0x6c,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x0a, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x69,
	0x6e, 0x74, 0x12, 0x4f, 0x0a, 0x0b, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x61, 0x73, 0x6b,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67,
	0x73, 0x2e, 0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61,
	0x73, 0x6b, 0x73, 0x22, 0x9a, 0x01, 0x0a, 0x23, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72,
	0x47, 0x65, 0x74, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x4e, 0x0a, 0x0f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x73, 0x61, 0x76, 0x69,
	0x6e, 0x67, 0x73, 0x2e, 0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x0e, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x22, 0x9e, 0x01, 0x0a, 0x27, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x53, 0x61,
	0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x46, 0x6f,
	0x72, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x4f, 0x0a, 0x0b, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e,
	0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b,
	0x73, 0x22, 0x9f, 0x01, 0x0a, 0x28, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x53,
	0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x46,
	0x6f, 0x72, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x4e, 0x0a, 0x0f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x73,
	0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x52, 0x0e, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x22, 0xe1, 0x01, 0x0a, 0x23, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x61,
	0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x35, 0x0a, 0x12, 0x63,
	0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x10, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x49, 0x64, 0x12, 0x37, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x53, 0x41, 0x43,
	0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4a, 0x0a, 0x0d, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x25, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x53, 0x41, 0x43,
	0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x0c, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0xd8, 0x01, 0x0a, 0x24, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x8a, 0x01, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45,
	0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x19, 0x0a, 0x15, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49,
	0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x10,
	0x65, 0x12, 0x26, 0x0a, 0x22, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f,
	0x41, 0x4c, 0x4c, 0x4f, 0x57, 0x45, 0x44, 0x10, 0x66, 0x12, 0x27, 0x0a, 0x23, 0x44, 0x55, 0x50,
	0x4c, 0x49, 0x43, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x50,
	0x44, 0x41, 0x54, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x4c, 0x4c, 0x4f, 0x57, 0x45, 0x44,
	0x10, 0x67, 0x22, 0xa7, 0x01, 0x0a, 0x22, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x61, 0x43,
	0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x55, 0x73, 0x65, 0x72, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61,
	0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x35, 0x0a, 0x12, 0x63, 0x6c, 0x6f,
	0x73, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x10,
	0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64,
	0x12, 0x4a, 0x0a, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63,
	0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67,
	0x73, 0x2e, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x55, 0x73, 0x65, 0x72, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x0c,
	0x75, 0x73, 0x65, 0x72, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x22, 0x4a, 0x0a, 0x23,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xe4, 0x01, 0x0a, 0x24, 0x47, 0x65, 0x74,
	0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75,
	0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x3a, 0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x61,
	0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x41, 0x0a,
	0x0e, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x0d, 0x66, 0x72, 0x6f, 0x6d, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x12, 0x3d, 0x0a, 0x0c, 0x74, 0x6f, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x0b, 0x74, 0x6f, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22,
	0xf9, 0x01, 0x0a, 0x25, 0x47, 0x65, 0x74, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64,
	0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3b,
	0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0b,
	0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x6e, 0x0a, 0x20, 0x73,
	0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63,
	0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e,
	0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x1d, 0x73, 0x61,
	0x76, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x6f, 0x73,
	0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x22, 0xdb, 0x02, 0x0a, 0x23,
	0x47, 0x65, 0x74, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x73, 0x42, 0x79, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x52, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12,
	0x4f, 0x0a, 0x0b, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x53,
	0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x6f,
	0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x4d, 0x61, 0x73, 0x6b, 0x52, 0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x73,
	0x12, 0x26, 0x0a, 0x0f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x5f, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x39, 0x0a, 0x19, 0x63, 0x6c, 0x6f, 0x73,
	0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x5f, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x16, 0x63, 0x6c, 0x6f,
	0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x12, 0x44, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x73, 0x61, 0x76,
	0x69, 0x6e, 0x67, 0x73, 0x2e, 0x53, 0x41, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0xf8, 0x01, 0x0a, 0x24, 0x47, 0x65,
	0x74, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x73, 0x42, 0x79, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3b, 0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x12, 0x6e, 0x0a, 0x20, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25,
	0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x1d, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x73, 0x22, 0x50, 0x0a, 0x21, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x50, 0x61,
	0x6e, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x75,
	0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x70, 0x61, 0x6e, 0x22, 0xd1, 0x01, 0x0a, 0x22, 0x56, 0x65, 0x72, 0x69, 0x66,
	0x79, 0x50, 0x61, 0x6e, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x5f, 0x6c,
	0x65, 0x66, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x61, 0x74, 0x74, 0x65, 0x6d,
	0x70, 0x74, 0x73, 0x4c, 0x65, 0x66, 0x74, 0x22, 0x61, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56,
	0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12,
	0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x11, 0x0a,
	0x0d, 0x50, 0x41, 0x4e, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x52, 0x52, 0x45, 0x43, 0x54, 0x10, 0x65,
	0x12, 0x18, 0x0a, 0x14, 0x50, 0x41, 0x4e, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x52, 0x52, 0x45, 0x43,
	0x54, 0x5f, 0x4c, 0x4f, 0x43, 0x4b, 0x45, 0x44, 0x10, 0x66, 0x2a, 0xa7, 0x02, 0x0a, 0x0a, 0x53,
	0x69, 0x67, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x49, 0x47,
	0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2b, 0x0a, 0x27, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x41, 0x56,
	0x41, 0x49, 0x4c, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x42, 0x41, 0x4e,
	0x4b, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x49, 0x4e, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45,
	0x57, 0x10, 0x02, 0x12, 0x25, 0x0a, 0x21, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x46, 0x49, 0x52, 0x53, 0x54, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54,
	0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x03, 0x12, 0x28, 0x0a, 0x24, 0x53, 0x49,
	0x47, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4c, 0x41, 0x53, 0x54, 0x5f, 0x41,
	0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45,
	0x53, 0x53, 0x10, 0x04, 0x12, 0x23, 0x0a, 0x1f, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x4c, 0x41, 0x53, 0x54, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x05, 0x12, 0x39, 0x0a, 0x35, 0x53, 0x49, 0x47,
	0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4c, 0x41, 0x53, 0x54, 0x5f, 0x41, 0x54,
	0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x53, 0x49, 0x4e,
	0x43, 0x45, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x54, 0x5f, 0x42, 0x41,
	0x4e, 0x4b, 0x10, 0x06, 0x32, 0x9e, 0x1b, 0x0a, 0x07, 0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73,
	0x12, 0x50, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x1d, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1e, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x47, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x1a, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x73,
	0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x56, 0x0a, 0x0f, 0x47,
	0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1f,
	0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x20, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x5f, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x21, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e,
	0x67, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x73, 0x61,
	0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x03, 0x88, 0x02, 0x01, 0x12, 0x50, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5c, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x65,
	0x6e, 0x69, 0x6e, 0x67, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x21, 0x2e, 0x73, 0x61,
	0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x6e, 0x67,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22,
	0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x65, 0x6e,
	0x69, 0x6e, 0x67, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x7b, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x57, 0x69, 0x74, 0x68, 0x53, 0x75, 0x6d,
	0x6d, 0x61, 0x72, 0x79, 0x12, 0x2c, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x47,
	0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x57, 0x69, 0x74, 0x68, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x47, 0x65, 0x74,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x57, 0x69,
	0x74, 0x68, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x6c, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x66, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x27, 0x2e, 0x73,
	0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x66,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e,
	0x47, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x66, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x4b, 0x0a, 0x0c, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x1c, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e,
	0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4e, 0x0a, 0x0d,
	0x52, 0x65, 0x6f, 0x70, 0x65, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x2e,
	0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x52, 0x65, 0x6f, 0x70, 0x65, 0x6e, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x73,
	0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x52, 0x65, 0x6f, 0x70, 0x65, 0x6e, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0c,
	0x49, 0x73, 0x54, 0x78, 0x6e, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x12, 0x1c, 0x2e, 0x73,
	0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x49, 0x73, 0x54, 0x78, 0x6e, 0x41, 0x6c, 0x6c, 0x6f,
	0x77, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x73, 0x61, 0x76,
	0x69, 0x6e, 0x67, 0x73, 0x2e, 0x49, 0x73, 0x54, 0x78, 0x6e, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x65, 0x0a, 0x13, 0x47, 0x65, 0x74,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x56, 0x31,
	0x12, 0x23, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x56, 0x31, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e,
	0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x56, 0x31, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x03, 0x88, 0x02, 0x01,
	0x12, 0x4e, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x12, 0x1d, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1e, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x8a, 0x01, 0x0a, 0x21, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66,
	0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x31, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73,
	0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x73, 0x61, 0x76, 0x69,
	0x6e, 0x67, 0x73, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65,
	0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xb1, 0x01,
	0x0a, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x44,
	0x61, 0x74, 0x61, 0x46, 0x72, 0x6f, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x12, 0x3e, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65,
	0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x46, 0x72, 0x6f, 0x6d,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x3f, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65,
	0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x46, 0x72, 0x6f, 0x6d,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x84, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65,
	0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2f, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e,
	0x47, 0x65, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x42, 0x61, 0x6c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73,
	0x2e, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x42, 0x61, 0x6c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x75, 0x0a, 0x14, 0x46, 0x65, 0x74, 0x63,
	0x68, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x12, 0x2d, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63,
	0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2e, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x45,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x7b, 0x0a, 0x16, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x2f, 0x2e, 0x64, 0x79, 0x6e, 0x61,
	0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x44, 0x79, 0x6e,
	0x61, 0x6d, 0x69, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x62,
	0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x64, 0x79, 0x6e,
	0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x44, 0x79,
	0x6e, 0x61, 0x6d, 0x69, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x6c, 0x6c,
	0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6c, 0x0a, 0x17,
	0x47, 0x65, 0x74, 0x45, 0x4f, 0x44, 0x53, 0x61, 0x76, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x27, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67,
	0x73, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x4f, 0x44, 0x53, 0x61, 0x76, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x28, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x4f,
	0x44, 0x53, 0x61, 0x76, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8d, 0x01, 0x0a, 0x22, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x42, 0x61, 0x6c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x32, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42,
	0x61, 0x6c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6f, 0x0a, 0x18, 0x46, 0x65,
	0x74, 0x63, 0x68, 0x4f, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x41,
	0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x12, 0x28, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73,
	0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x4f, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x69,
	0x67, 0x6e, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x29, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68,
	0x4f, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x41, 0x74, 0x74, 0x65,
	0x6d, 0x70, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x78, 0x0a, 0x1b, 0x47,
	0x65, 0x74, 0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x45, 0x73, 0x73, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x12, 0x2b, 0x2e, 0x73, 0x61, 0x76,
	0x69, 0x6e, 0x67, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x73, 0x73, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67,
	0x73, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x45, 0x73, 0x73, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x78, 0x0a, 0x1b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f,
	0x72, 0x47, 0x65, 0x74, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x47, 0x65, 0x74, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73,
	0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2c, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x4f, 0x72, 0x47, 0x65, 0x74, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x87, 0x01, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x53, 0x61, 0x43,
	0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x46, 0x6f, 0x72,
	0x55, 0x73, 0x65, 0x72, 0x12, 0x30, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x47,
	0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x46, 0x6f, 0x72, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73,
	0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x46, 0x6f, 0x72, 0x55, 0x73, 0x65,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7b, 0x0a, 0x1c, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2c, 0x2e, 0x73, 0x61, 0x76, 0x69,
	0x6e, 0x67, 0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73,
	0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67,
	0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x78, 0x0a, 0x1b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x55, 0x73, 0x65, 0x72, 0x46, 0x65, 0x65,
	0x64, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x2b, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x55, 0x73, 0x65, 0x72,
	0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x7e, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64,
	0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x73, 0x12, 0x2d, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x53,
	0x75, 0x62, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2e, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x75,
	0x62, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x7b, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x42, 0x79, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x12, 0x2c, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x61,
	0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x42,
	0x79, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d,
	0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x61, 0x43, 0x6c,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x42, 0x79, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x75, 0x0a,
	0x1a, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x50, 0x61, 0x6e, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x12, 0x2a, 0x2e, 0x73, 0x61,
	0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x50, 0x61, 0x6e, 0x46,
	0x6f, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67,
	0x73, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x50, 0x61, 0x6e, 0x46, 0x6f, 0x72, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x72, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x53, 0x61, 0x76, 0x69, 0x6e,
	0x67, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65,
	0x73, 0x12, 0x29, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x53,
	0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x6f, 0x6d,
	0x69, 0x6e, 0x65, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x73,
	0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x61, 0x76, 0x69, 0x6e, 0x67,
	0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7b, 0x0a, 0x1c, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x73, 0x12, 0x2c, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e,
	0x67, 0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x48, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x5a, 0x22, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_savings_service_proto_rawDescOnce sync.Once
	file_api_savings_service_proto_rawDescData = file_api_savings_service_proto_rawDesc
)

func file_api_savings_service_proto_rawDescGZIP() []byte {
	file_api_savings_service_proto_rawDescOnce.Do(func() {
		file_api_savings_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_savings_service_proto_rawDescData)
	})
	return file_api_savings_service_proto_rawDescData
}

var file_api_savings_service_proto_enumTypes = make([]protoimpl.EnumInfo, 22)
var file_api_savings_service_proto_msgTypes = make([]protoimpl.MessageInfo, 71)
var file_api_savings_service_proto_goTypes = []interface{}{
	(SignStatus)(0),                                                // 0: savings.SignStatus
	(UpdateBalanceResponse_Status)(0),                              // 1: savings.UpdateBalanceResponse.Status
	(GetAccountBalanceV1Request_DataFreshness)(0),                  // 2: savings.GetAccountBalanceV1Request.DataFreshness
	(GetAccountBalanceV1Request_VendorApiOption)(0),                // 3: savings.GetAccountBalanceV1Request.VendorApiOption
	(GetAccountBalanceV1Response_Status)(0),                        // 4: savings.GetAccountBalanceV1Response.Status
	(CreateAccountRequest_ForceRetryOption)(0),                     // 5: savings.CreateAccountRequest.ForceRetryOption
	(CreateAccountResponse_Status)(0),                              // 6: savings.CreateAccountResponse.Status
	(GetAccountsListResponse_Status)(0),                            // 7: savings.GetAccountsListResponse.Status
	(GetAccountBalanceRequest_DataFreshness)(0),                    // 8: savings.GetAccountBalanceRequest.DataFreshness
	(GetAccountBalanceResponse_Status)(0),                          // 9: savings.GetAccountBalanceResponse.Status
	(UpdateAccountResponse_Status)(0),                              // 10: savings.UpdateAccountResponse.Status
	(GetOpeningBalanceResponse_Status)(0),                          // 11: savings.GetOpeningBalanceResponse.Status
	(GetAccountBalanceWithSummaryRequest_Range)(0),                 // 12: savings.GetAccountBalanceWithSummaryRequest.Range
	(GetAccountBalanceWithSummaryRequest_ForceBalanceUpdate)(0),    // 13: savings.GetAccountBalanceWithSummaryRequest.ForceBalanceUpdate
	(GetAccountBalanceWithSummaryResponse_Status)(0),               // 14: savings.GetAccountBalanceWithSummaryResponse.Status
	(AccountSummaryBucketDetails_Bucket)(0),                        // 15: savings.AccountSummaryBucketDetails.Bucket
	(GetListOfActiveAccountsResponse_Status)(0),                    // 16: savings.GetListOfActiveAccountsResponse.Status
	(IsTxnAllowedRequest_TxnType)(0),                               // 17: savings.IsTxnAllowedRequest.TxnType
	(IsTxnAllowedResponse_Status)(0),                               // 18: savings.IsTxnAllowedResponse.Status
	(IsTxnAllowedResponse_Reason)(0),                               // 19: savings.IsTxnAllowedResponse.Reason
	(UpdateSaClosureRequestStatusResponse_Status)(0),               // 20: savings.UpdateSaClosureRequestStatusResponse.Status
	(VerifyPanForAccountClosureResponse_Status)(0),                 // 21: savings.VerifyPanForAccountClosureResponse.Status
	(*UpdateSavingsAccountNomineesRequest)(nil),                    // 22: savings.UpdateSavingsAccountNomineesRequest
	(*UpdateSavingsAccountNomineesResponse)(nil),                   // 23: savings.UpdateSavingsAccountNomineesResponse
	(*GetSavingsAccountNomineesRequest)(nil),                       // 24: savings.GetSavingsAccountNomineesRequest
	(*GetSavingsAccountNomineesResponse)(nil),                      // 25: savings.GetSavingsAccountNomineesResponse
	(*SavingsAccountNominee)(nil),                                  // 26: savings.SavingsAccountNominee
	(*UpdateBalanceRequest)(nil),                                   // 27: savings.UpdateBalanceRequest
	(*UpdateBalanceResponse)(nil),                                  // 28: savings.UpdateBalanceResponse
	(*GetAccountBalanceV1Request)(nil),                             // 29: savings.GetAccountBalanceV1Request
	(*GetAccountBalanceV1Response)(nil),                            // 30: savings.GetAccountBalanceV1Response
	(*CreateAccountRequest)(nil),                                   // 31: savings.CreateAccountRequest
	(*CreateAccountResponse)(nil),                                  // 32: savings.CreateAccountResponse
	(*BankAccountIdentifier)(nil),                                  // 33: savings.BankAccountIdentifier
	(*GetAccountRequest)(nil),                                      // 34: savings.GetAccountRequest
	(*GetAccountResponse)(nil),                                     // 35: savings.GetAccountResponse
	(*GetAccountsListRequest)(nil),                                 // 36: savings.GetAccountsListRequest
	(*GetAccountsListResponse)(nil),                                // 37: savings.GetAccountsListResponse
	(*PrimaryUserIdentifier)(nil),                                  // 38: savings.PrimaryUserIdentifier
	(*BulkActorIdentifier)(nil),                                    // 39: savings.BulkActorIdentifier
	(*ActorUniqueAccountIdentifier)(nil),                           // 40: savings.ActorUniqueAccountIdentifier
	(*GetAccountBalanceRequest)(nil),                               // 41: savings.GetAccountBalanceRequest
	(*GetAccountBalanceResponse)(nil),                              // 42: savings.GetAccountBalanceResponse
	(*UpdateAccountRequest)(nil),                                   // 43: savings.UpdateAccountRequest
	(*UpdateAccountResponse)(nil),                                  // 44: savings.UpdateAccountResponse
	(*GetOpeningBalanceRequest)(nil),                               // 45: savings.GetOpeningBalanceRequest
	(*GetOpeningBalanceResponse)(nil),                              // 46: savings.GetOpeningBalanceResponse
	(*GetAccountBalanceWithSummaryRequest)(nil),                    // 47: savings.GetAccountBalanceWithSummaryRequest
	(*GetAccountBalanceWithSummaryResponse)(nil),                   // 48: savings.GetAccountBalanceWithSummaryResponse
	(*AccountSummaryBucketDetails)(nil),                            // 49: savings.AccountSummaryBucketDetails
	(*GetListOfActiveAccountsRequest)(nil),                         // 50: savings.GetListOfActiveAccountsRequest
	(*GetListOfActiveAccountsResponse)(nil),                        // 51: savings.GetListOfActiveAccountsResponse
	(*CloseAccountRequest)(nil),                                    // 52: savings.CloseAccountRequest
	(*CloseAccountResponse)(nil),                                   // 53: savings.CloseAccountResponse
	(*ReopenAccountRequest)(nil),                                   // 54: savings.ReopenAccountRequest
	(*ReopenAccountResponse)(nil),                                  // 55: savings.ReopenAccountResponse
	(*IsTxnAllowedRequest)(nil),                                    // 56: savings.IsTxnAllowedRequest
	(*IsTxnAllowedResponse)(nil),                                   // 57: savings.IsTxnAllowedResponse
	(*StoreClosedAccountBalTransferDataRequest)(nil),               // 58: savings.StoreClosedAccountBalTransferDataRequest
	(*StoreClosedAccountBalTransferDataResponse)(nil),              // 59: savings.StoreClosedAccountBalTransferDataResponse
	(*StoreClosedAccountBalTransferDataFromStatementRequest)(nil),  // 60: savings.StoreClosedAccountBalTransferDataFromStatementRequest
	(*StoreClosedAccountBalTransferDataFromStatementResponse)(nil), // 61: savings.StoreClosedAccountBalTransferDataFromStatementResponse
	(*GetClosedAccountBalTransferDataRequest)(nil),                 // 62: savings.GetClosedAccountBalTransferDataRequest
	(*GetClosedAccountBalTransferDataResponse)(nil),                // 63: savings.GetClosedAccountBalTransferDataResponse
	(*GetEODSavBalanceHistoryRequest)(nil),                         // 64: savings.GetEODSavBalanceHistoryRequest
	(*GetEODSavBalanceHistoryResponse)(nil),                        // 65: savings.GetEODSavBalanceHistoryResponse
	(*BalanceByDate)(nil),                                          // 66: savings.BalanceByDate
	(*UpdateClosedAccountBalTransferDataRequest)(nil),              // 67: savings.UpdateClosedAccountBalTransferDataRequest
	(*UpdateClosedAccountBalTransferDataResponse)(nil),             // 68: savings.UpdateClosedAccountBalTransferDataResponse
	(*FetchOrCreateSignAttemptRequest)(nil),                        // 69: savings.FetchOrCreateSignAttemptRequest
	(*FetchOrCreateSignAttemptResponse)(nil),                       // 70: savings.FetchOrCreateSignAttemptResponse
	(*ActorIdBankFilter)(nil),                                      // 71: savings.ActorIdBankFilter
	(*AccountNumberBankFilter)(nil),                                // 72: savings.AccountNumberBankFilter
	(*GetSavingsAccountEssentialsRequest)(nil),                     // 73: savings.GetSavingsAccountEssentialsRequest
	(*SavingsAccountEssentials)(nil),                               // 74: savings.SavingsAccountEssentials
	(*GetSavingsAccountEssentialsResponse)(nil),                    // 75: savings.GetSavingsAccountEssentialsResponse
	(*CreateOrGetSaClosureRequestRequest)(nil),                     // 76: savings.CreateOrGetSaClosureRequestRequest
	(*CreateOrGetSaClosureRequestResponse)(nil),                    // 77: savings.CreateOrGetSaClosureRequestResponse
	(*GetActiveSaClosureRequestForUserRequest)(nil),                // 78: savings.GetActiveSaClosureRequestForUserRequest
	(*GetActiveSaClosureRequestForUserResponse)(nil),               // 79: savings.GetActiveSaClosureRequestForUserResponse
	(*UpdateSaClosureRequestStatusRequest)(nil),                    // 80: savings.UpdateSaClosureRequestStatusRequest
	(*UpdateSaClosureRequestStatusResponse)(nil),                   // 81: savings.UpdateSaClosureRequestStatusResponse
	(*RecordSaClosureUserFeedbackRequest)(nil),                     // 82: savings.RecordSaClosureUserFeedbackRequest
	(*RecordSaClosureUserFeedbackResponse)(nil),                    // 83: savings.RecordSaClosureUserFeedbackResponse
	(*GetSubmittedSaClosureRequestsRequest)(nil),                   // 84: savings.GetSubmittedSaClosureRequestsRequest
	(*GetSubmittedSaClosureRequestsResponse)(nil),                  // 85: savings.GetSubmittedSaClosureRequestsResponse
	(*GetSaClosureRequestsByFilterRequest)(nil),                    // 86: savings.GetSaClosureRequestsByFilterRequest
	(*GetSaClosureRequestsByFilterResponse)(nil),                   // 87: savings.GetSaClosureRequestsByFilterResponse
	(*VerifyPanForAccountClosureRequest)(nil),                      // 88: savings.VerifyPanForAccountClosureRequest
	(*VerifyPanForAccountClosureResponse)(nil),                     // 89: savings.VerifyPanForAccountClosureResponse
	(*CreateAccountRequest_Options)(nil),                           // 90: savings.CreateAccountRequest.Options
	(*GetListOfActiveAccountsResponse_Account)(nil),                // 91: savings.GetListOfActiveAccountsResponse.Account
	(*IsTxnAllowedResponse_SavingsTxnLimits)(nil),                  // 92: savings.IsTxnAllowedResponse.SavingsTxnLimits
	(*NomineeDetails)(nil),                                         // 93: savings.NomineeDetails
	(*rpc.Status)(nil),                                             // 94: rpc.Status
	(*typesv2.Nominee)(nil),                                        // 95: api.typesv2.Nominee
	(*timestamppb.Timestamp)(nil),                                  // 96: google.protobuf.Timestamp
	(*money.Money)(nil),                                            // 97: google.type.Money
	(SKU)(0),                                                       // 98: savings.SKU
	(*Account)(nil),                                                // 99: savings.Account
	(account.AccountProductOffering)(0),                            // 100: api.typesv2.account.AccountProductOffering
	(vendorgateway.Vendor)(0),                                      // 101: vendorgateway.Vendor
	(*AccountConstraints)(nil),                                     // 102: savings.AccountConstraints
	(State)(0),                                                     // 103: savings.State
	(*common.PhoneNumber)(nil),                                     // 104: api.typesv2.common.PhoneNumber
	(AccountFieldMask)(0),                                          // 105: savings.AccountFieldMask
	(*SKUInfo)(nil),                                                // 106: savings.SKUInfo
	(*date.Date)(nil),                                              // 107: google.type.Date
	(*ClosedAccountBalanceTransfer)(nil),                           // 108: savings.ClosedAccountBalanceTransfer
	(CbtFieldMask)(0),                                              // 109: savings.CbtFieldMask
	(SAClosureRequestEntryPoint)(0),                                // 110: savings.SAClosureRequestEntryPoint
	(SavingsAccountClosureRequestFieldMask)(0),                     // 111: savings.SavingsAccountClosureRequestFieldMask
	(*SavingsAccountClosureRequest)(nil),                           // 112: savings.SavingsAccountClosureRequest
	(SAClosureRequestStatus)(0),                                    // 113: savings.SAClosureRequestStatus
	(SAClosureRequestStatusReason)(0),                              // 114: savings.SAClosureRequestStatusReason
	(*SaClosureRequestUserFeedback)(nil),                           // 115: savings.SaClosureRequestUserFeedback
	(*rpc.PageContextRequest)(nil),                                 // 116: rpc.PageContextRequest
	(*rpc.PageContextResponse)(nil),                                // 117: rpc.PageContextResponse
	(*dynamic_elements.FetchDynamicElementsRequest)(nil),           // 118: dynamic_elements.FetchDynamicElementsRequest
	(*dynamic_elements.DynamicElementCallbackRequest)(nil),         // 119: dynamic_elements.DynamicElementCallbackRequest
	(*dynamic_elements.FetchDynamicElementsResponse)(nil),          // 120: dynamic_elements.FetchDynamicElementsResponse
	(*dynamic_elements.DynamicElementCallbackResponse)(nil),        // 121: dynamic_elements.DynamicElementCallbackResponse
}
var file_api_savings_service_proto_depIdxs = []int32{
	93,  // 0: savings.UpdateSavingsAccountNomineesRequest.nominee_details:type_name -> savings.NomineeDetails
	94,  // 1: savings.UpdateSavingsAccountNomineesResponse.status:type_name -> rpc.Status
	94,  // 2: savings.GetSavingsAccountNomineesResponse.status:type_name -> rpc.Status
	95,  // 3: savings.GetSavingsAccountNomineesResponse.nominee:type_name -> api.typesv2.Nominee
	26,  // 4: savings.GetSavingsAccountNomineesResponse.savings_account_nominees:type_name -> savings.SavingsAccountNominee
	95,  // 5: savings.SavingsAccountNominee.nominee:type_name -> api.typesv2.Nominee
	96,  // 6: savings.UpdateBalanceRequest.prev_updated_at:type_name -> google.protobuf.Timestamp
	94,  // 7: savings.UpdateBalanceResponse.status:type_name -> rpc.Status
	33,  // 8: savings.GetAccountBalanceV1Request.external_id:type_name -> savings.BankAccountIdentifier
	2,   // 9: savings.GetAccountBalanceV1Request.data_freshness:type_name -> savings.GetAccountBalanceV1Request.DataFreshness
	3,   // 10: savings.GetAccountBalanceV1Request.vendor_api_option:type_name -> savings.GetAccountBalanceV1Request.VendorApiOption
	94,  // 11: savings.GetAccountBalanceV1Response.status:type_name -> rpc.Status
	97,  // 12: savings.GetAccountBalanceV1Response.ledger_balance:type_name -> google.type.Money
	97,  // 13: savings.GetAccountBalanceV1Response.available_balance:type_name -> google.type.Money
	97,  // 14: savings.GetAccountBalanceV1Response.clearance_balance:type_name -> google.type.Money
	97,  // 15: savings.GetAccountBalanceV1Response.lien_balance:type_name -> google.type.Money
	96,  // 16: savings.GetAccountBalanceV1Response.balance_at:type_name -> google.protobuf.Timestamp
	5,   // 17: savings.CreateAccountRequest.force_retry:type_name -> savings.CreateAccountRequest.ForceRetryOption
	98,  // 18: savings.CreateAccountRequest.sku:type_name -> savings.SKU
	90,  // 19: savings.CreateAccountRequest.options:type_name -> savings.CreateAccountRequest.Options
	99,  // 20: savings.CreateAccountResponse.account:type_name -> savings.Account
	94,  // 21: savings.CreateAccountResponse.status:type_name -> rpc.Status
	33,  // 22: savings.GetAccountRequest.external_id:type_name -> savings.BankAccountIdentifier
	72,  // 23: savings.GetAccountRequest.account_num_bank_filter:type_name -> savings.AccountNumberBankFilter
	40,  // 24: savings.GetAccountRequest.actor_unique_account_identifier:type_name -> savings.ActorUniqueAccountIdentifier
	99,  // 25: savings.GetAccountResponse.account:type_name -> savings.Account
	94,  // 26: savings.GetAccountResponse.status:type_name -> rpc.Status
	38,  // 27: savings.GetAccountsListRequest.user_ids:type_name -> savings.PrimaryUserIdentifier
	39,  // 28: savings.GetAccountsListRequest.bulk_actor_identifier:type_name -> savings.BulkActorIdentifier
	94,  // 29: savings.GetAccountsListResponse.status:type_name -> rpc.Status
	99,  // 30: savings.GetAccountsListResponse.accounts:type_name -> savings.Account
	100, // 31: savings.PrimaryUserIdentifier.account_product_offering_list:type_name -> api.typesv2.account.AccountProductOffering
	100, // 32: savings.BulkActorIdentifier.account_product_offerings:type_name -> api.typesv2.account.AccountProductOffering
	101, // 33: savings.BulkActorIdentifier.partner_banks:type_name -> vendorgateway.Vendor
	100, // 34: savings.ActorUniqueAccountIdentifier.account_product_offering:type_name -> api.typesv2.account.AccountProductOffering
	101, // 35: savings.ActorUniqueAccountIdentifier.partner_bank:type_name -> vendorgateway.Vendor
	33,  // 36: savings.GetAccountBalanceRequest.external_id:type_name -> savings.BankAccountIdentifier
	8,   // 37: savings.GetAccountBalanceRequest.data_freshness:type_name -> savings.GetAccountBalanceRequest.DataFreshness
	94,  // 38: savings.GetAccountBalanceResponse.status:type_name -> rpc.Status
	97,  // 39: savings.GetAccountBalanceResponse.available_balance:type_name -> google.type.Money
	97,  // 40: savings.GetAccountBalanceResponse.ledger_balance:type_name -> google.type.Money
	96,  // 41: savings.GetAccountBalanceResponse.last_updated_at:type_name -> google.protobuf.Timestamp
	33,  // 42: savings.UpdateAccountRequest.external_id:type_name -> savings.BankAccountIdentifier
	97,  // 43: savings.UpdateAccountRequest.balance:type_name -> google.type.Money
	102, // 44: savings.UpdateAccountRequest.constraints:type_name -> savings.AccountConstraints
	103, // 45: savings.UpdateAccountRequest.state:type_name -> savings.State
	104, // 46: savings.UpdateAccountRequest.phone_number:type_name -> api.typesv2.common.PhoneNumber
	105, // 47: savings.UpdateAccountRequest.update_mask:type_name -> savings.AccountFieldMask
	98,  // 48: savings.UpdateAccountRequest.sku:type_name -> savings.SKU
	106, // 49: savings.UpdateAccountRequest.sku_info:type_name -> savings.SKUInfo
	94,  // 50: savings.UpdateAccountResponse.status:type_name -> rpc.Status
	99,  // 51: savings.UpdateAccountResponse.account:type_name -> savings.Account
	33,  // 52: savings.GetOpeningBalanceRequest.external_id:type_name -> savings.BankAccountIdentifier
	107, // 53: savings.GetOpeningBalanceRequest.date:type_name -> google.type.Date
	94,  // 54: savings.GetOpeningBalanceResponse.status:type_name -> rpc.Status
	97,  // 55: savings.GetOpeningBalanceResponse.opening_balance:type_name -> google.type.Money
	33,  // 56: savings.GetAccountBalanceWithSummaryRequest.external_id:type_name -> savings.BankAccountIdentifier
	12,  // 57: savings.GetAccountBalanceWithSummaryRequest.time_range:type_name -> savings.GetAccountBalanceWithSummaryRequest.Range
	13,  // 58: savings.GetAccountBalanceWithSummaryRequest.force_balance_update:type_name -> savings.GetAccountBalanceWithSummaryRequest.ForceBalanceUpdate
	94,  // 59: savings.GetAccountBalanceWithSummaryResponse.status:type_name -> rpc.Status
	97,  // 60: savings.GetAccountBalanceWithSummaryResponse.opening_balance:type_name -> google.type.Money
	97,  // 61: savings.GetAccountBalanceWithSummaryResponse.available_balance:type_name -> google.type.Money
	97,  // 62: savings.GetAccountBalanceWithSummaryResponse.ledger_balance:type_name -> google.type.Money
	97,  // 63: savings.GetAccountBalanceWithSummaryResponse.total_spent:type_name -> google.type.Money
	97,  // 64: savings.GetAccountBalanceWithSummaryResponse.total_credit:type_name -> google.type.Money
	97,  // 65: savings.GetAccountBalanceWithSummaryResponse.total_debit:type_name -> google.type.Money
	49,  // 66: savings.GetAccountBalanceWithSummaryResponse.bucket_details:type_name -> savings.AccountSummaryBucketDetails
	96,  // 67: savings.GetAccountBalanceWithSummaryResponse.balance_at:type_name -> google.protobuf.Timestamp
	15,  // 68: savings.AccountSummaryBucketDetails.bucket:type_name -> savings.AccountSummaryBucketDetails.Bucket
	97,  // 69: savings.AccountSummaryBucketDetails.value:type_name -> google.type.Money
	49,  // 70: savings.AccountSummaryBucketDetails.sections:type_name -> savings.AccountSummaryBucketDetails
	96,  // 71: savings.GetListOfActiveAccountsRequest.created_before:type_name -> google.protobuf.Timestamp
	96,  // 72: savings.GetListOfActiveAccountsRequest.created_after:type_name -> google.protobuf.Timestamp
	91,  // 73: savings.GetListOfActiveAccountsResponse.account:type_name -> savings.GetListOfActiveAccountsResponse.Account
	94,  // 74: savings.GetListOfActiveAccountsResponse.status:type_name -> rpc.Status
	99,  // 75: savings.GetListOfActiveAccountsResponse.account_list:type_name -> savings.Account
	33,  // 76: savings.CloseAccountRequest.external_id:type_name -> savings.BankAccountIdentifier
	94,  // 77: savings.CloseAccountResponse.status:type_name -> rpc.Status
	99,  // 78: savings.CloseAccountResponse.account:type_name -> savings.Account
	33,  // 79: savings.ReopenAccountRequest.external_id:type_name -> savings.BankAccountIdentifier
	94,  // 80: savings.ReopenAccountResponse.status:type_name -> rpc.Status
	99,  // 81: savings.ReopenAccountResponse.account:type_name -> savings.Account
	33,  // 82: savings.IsTxnAllowedRequest.external_id:type_name -> savings.BankAccountIdentifier
	97,  // 83: savings.IsTxnAllowedRequest.amount:type_name -> google.type.Money
	17,  // 84: savings.IsTxnAllowedRequest.txn_type:type_name -> savings.IsTxnAllowedRequest.TxnType
	94,  // 85: savings.IsTxnAllowedResponse.status:type_name -> rpc.Status
	97,  // 86: savings.IsTxnAllowedResponse.credit_amount_allowed:type_name -> google.type.Money
	96,  // 87: savings.IsTxnAllowedResponse.account_freeze_date:type_name -> google.protobuf.Timestamp
	19,  // 88: savings.IsTxnAllowedResponse.not_allowed_reason:type_name -> savings.IsTxnAllowedResponse.Reason
	92,  // 89: savings.IsTxnAllowedResponse.savings_txn_limits:type_name -> savings.IsTxnAllowedResponse.SavingsTxnLimits
	108, // 90: savings.StoreClosedAccountBalTransferDataRequest.data:type_name -> savings.ClosedAccountBalanceTransfer
	94,  // 91: savings.StoreClosedAccountBalTransferDataResponse.status:type_name -> rpc.Status
	94,  // 92: savings.StoreClosedAccountBalTransferDataFromStatementResponse.status:type_name -> rpc.Status
	94,  // 93: savings.GetClosedAccountBalTransferDataResponse.status:type_name -> rpc.Status
	108, // 94: savings.GetClosedAccountBalTransferDataResponse.entries:type_name -> savings.ClosedAccountBalanceTransfer
	96,  // 95: savings.GetEODSavBalanceHistoryRequest.start_time:type_name -> google.protobuf.Timestamp
	96,  // 96: savings.GetEODSavBalanceHistoryRequest.end_time:type_name -> google.protobuf.Timestamp
	94,  // 97: savings.GetEODSavBalanceHistoryResponse.status:type_name -> rpc.Status
	66,  // 98: savings.GetEODSavBalanceHistoryResponse.balance_by_dates:type_name -> savings.BalanceByDate
	107, // 99: savings.BalanceByDate.date:type_name -> google.type.Date
	97,  // 100: savings.BalanceByDate.eod_balance:type_name -> google.type.Money
	108, // 101: savings.UpdateClosedAccountBalTransferDataRequest.data:type_name -> savings.ClosedAccountBalanceTransfer
	109, // 102: savings.UpdateClosedAccountBalTransferDataRequest.field_masks:type_name -> savings.CbtFieldMask
	94,  // 103: savings.UpdateClosedAccountBalTransferDataResponse.status:type_name -> rpc.Status
	94,  // 104: savings.FetchOrCreateSignAttemptResponse.status:type_name -> rpc.Status
	0,   // 105: savings.FetchOrCreateSignAttemptResponse.sign_status:type_name -> savings.SignStatus
	101, // 106: savings.ActorIdBankFilter.partner_bank:type_name -> vendorgateway.Vendor
	101, // 107: savings.AccountNumberBankFilter.partner_bank:type_name -> vendorgateway.Vendor
	71,  // 108: savings.GetSavingsAccountEssentialsRequest.actor_id_bank_filter:type_name -> savings.ActorIdBankFilter
	72,  // 109: savings.GetSavingsAccountEssentialsRequest.account_num_bank_filter:type_name -> savings.AccountNumberBankFilter
	40,  // 110: savings.GetSavingsAccountEssentialsRequest.actor_unique_account_identifier:type_name -> savings.ActorUniqueAccountIdentifier
	103, // 111: savings.SavingsAccountEssentials.state:type_name -> savings.State
	101, // 112: savings.SavingsAccountEssentials.partner_bank:type_name -> vendorgateway.Vendor
	106, // 113: savings.SavingsAccountEssentials.sku_info:type_name -> savings.SKUInfo
	94,  // 114: savings.GetSavingsAccountEssentialsResponse.status:type_name -> rpc.Status
	74,  // 115: savings.GetSavingsAccountEssentialsResponse.account:type_name -> savings.SavingsAccountEssentials
	110, // 116: savings.CreateOrGetSaClosureRequestRequest.entry_point:type_name -> savings.SAClosureRequestEntryPoint
	111, // 117: savings.CreateOrGetSaClosureRequestRequest.field_masks:type_name -> savings.SavingsAccountClosureRequestFieldMask
	94,  // 118: savings.CreateOrGetSaClosureRequestResponse.status:type_name -> rpc.Status
	112, // 119: savings.CreateOrGetSaClosureRequestResponse.closure_request:type_name -> savings.SavingsAccountClosureRequest
	111, // 120: savings.GetActiveSaClosureRequestForUserRequest.field_masks:type_name -> savings.SavingsAccountClosureRequestFieldMask
	94,  // 121: savings.GetActiveSaClosureRequestForUserResponse.status:type_name -> rpc.Status
	112, // 122: savings.GetActiveSaClosureRequestForUserResponse.closure_request:type_name -> savings.SavingsAccountClosureRequest
	113, // 123: savings.UpdateSaClosureRequestStatusRequest.status:type_name -> savings.SAClosureRequestStatus
	114, // 124: savings.UpdateSaClosureRequestStatusRequest.status_reason:type_name -> savings.SAClosureRequestStatusReason
	94,  // 125: savings.UpdateSaClosureRequestStatusResponse.status:type_name -> rpc.Status
	115, // 126: savings.RecordSaClosureUserFeedbackRequest.user_feedback:type_name -> savings.SaClosureRequestUserFeedback
	94,  // 127: savings.RecordSaClosureUserFeedbackResponse.status:type_name -> rpc.Status
	116, // 128: savings.GetSubmittedSaClosureRequestsRequest.page_context:type_name -> rpc.PageContextRequest
	96,  // 129: savings.GetSubmittedSaClosureRequestsRequest.from_timestamp:type_name -> google.protobuf.Timestamp
	96,  // 130: savings.GetSubmittedSaClosureRequestsRequest.to_timestamp:type_name -> google.protobuf.Timestamp
	94,  // 131: savings.GetSubmittedSaClosureRequestsResponse.status:type_name -> rpc.Status
	117, // 132: savings.GetSubmittedSaClosureRequestsResponse.page_context:type_name -> rpc.PageContextResponse
	112, // 133: savings.GetSubmittedSaClosureRequestsResponse.savings_account_closure_requests:type_name -> savings.SavingsAccountClosureRequest
	116, // 134: savings.GetSaClosureRequestsByFilterRequest.page_context:type_name -> rpc.PageContextRequest
	111, // 135: savings.GetSaClosureRequestsByFilterRequest.field_masks:type_name -> savings.SavingsAccountClosureRequestFieldMask
	113, // 136: savings.GetSaClosureRequestsByFilterRequest.status_filter:type_name -> savings.SAClosureRequestStatus
	94,  // 137: savings.GetSaClosureRequestsByFilterResponse.status:type_name -> rpc.Status
	117, // 138: savings.GetSaClosureRequestsByFilterResponse.page_context:type_name -> rpc.PageContextResponse
	112, // 139: savings.GetSaClosureRequestsByFilterResponse.savings_account_closure_requests:type_name -> savings.SavingsAccountClosureRequest
	94,  // 140: savings.VerifyPanForAccountClosureResponse.status:type_name -> rpc.Status
	97,  // 141: savings.IsTxnAllowedResponse.SavingsTxnLimits.allowed_credit_limit_amount:type_name -> google.type.Money
	97,  // 142: savings.IsTxnAllowedResponse.SavingsTxnLimits.allowed_savings_limit_amount:type_name -> google.type.Money
	97,  // 143: savings.IsTxnAllowedResponse.SavingsTxnLimits.max_allowed_savings_limit_amount:type_name -> google.type.Money
	97,  // 144: savings.IsTxnAllowedResponse.SavingsTxnLimits.total_allowed_credit_limit_amount:type_name -> google.type.Money
	31,  // 145: savings.Savings.CreateAccount:input_type -> savings.CreateAccountRequest
	34,  // 146: savings.Savings.GetAccount:input_type -> savings.GetAccountRequest
	36,  // 147: savings.Savings.GetAccountsList:input_type -> savings.GetAccountsListRequest
	41,  // 148: savings.Savings.GetAccountBalance:input_type -> savings.GetAccountBalanceRequest
	43,  // 149: savings.Savings.UpdateAccount:input_type -> savings.UpdateAccountRequest
	45,  // 150: savings.Savings.GetOpeningBalance:input_type -> savings.GetOpeningBalanceRequest
	47,  // 151: savings.Savings.GetAccountBalanceWithSummary:input_type -> savings.GetAccountBalanceWithSummaryRequest
	50,  // 152: savings.Savings.GetListOfActiveAccounts:input_type -> savings.GetListOfActiveAccountsRequest
	52,  // 153: savings.Savings.CloseAccount:input_type -> savings.CloseAccountRequest
	54,  // 154: savings.Savings.ReopenAccount:input_type -> savings.ReopenAccountRequest
	56,  // 155: savings.Savings.IsTxnAllowed:input_type -> savings.IsTxnAllowedRequest
	29,  // 156: savings.Savings.GetAccountBalanceV1:input_type -> savings.GetAccountBalanceV1Request
	27,  // 157: savings.Savings.UpdateBalance:input_type -> savings.UpdateBalanceRequest
	58,  // 158: savings.Savings.StoreClosedAccountBalTransferData:input_type -> savings.StoreClosedAccountBalTransferDataRequest
	60,  // 159: savings.Savings.StoreClosedAccountBalTransferDataFromStatement:input_type -> savings.StoreClosedAccountBalTransferDataFromStatementRequest
	62,  // 160: savings.Savings.GetClosedAccountBalTransferData:input_type -> savings.GetClosedAccountBalTransferDataRequest
	118, // 161: savings.Savings.FetchDynamicElements:input_type -> dynamic_elements.FetchDynamicElementsRequest
	119, // 162: savings.Savings.DynamicElementCallback:input_type -> dynamic_elements.DynamicElementCallbackRequest
	64,  // 163: savings.Savings.GetEODSavBalanceHistory:input_type -> savings.GetEODSavBalanceHistoryRequest
	67,  // 164: savings.Savings.UpdateClosedAccountBalTransferData:input_type -> savings.UpdateClosedAccountBalTransferDataRequest
	69,  // 165: savings.Savings.FetchOrCreateSignAttempt:input_type -> savings.FetchOrCreateSignAttemptRequest
	73,  // 166: savings.Savings.GetSavingsAccountEssentials:input_type -> savings.GetSavingsAccountEssentialsRequest
	76,  // 167: savings.Savings.CreateOrGetSaClosureRequest:input_type -> savings.CreateOrGetSaClosureRequestRequest
	78,  // 168: savings.Savings.GetActiveSaClosureRequestForUser:input_type -> savings.GetActiveSaClosureRequestForUserRequest
	80,  // 169: savings.Savings.UpdateSaClosureRequestStatus:input_type -> savings.UpdateSaClosureRequestStatusRequest
	82,  // 170: savings.Savings.RecordSaClosureUserFeedback:input_type -> savings.RecordSaClosureUserFeedbackRequest
	84,  // 171: savings.Savings.GetSubmittedSaClosureRequests:input_type -> savings.GetSubmittedSaClosureRequestsRequest
	86,  // 172: savings.Savings.GetSaClosureRequestsByFilter:input_type -> savings.GetSaClosureRequestsByFilterRequest
	88,  // 173: savings.Savings.VerifyPanForAccountClosure:input_type -> savings.VerifyPanForAccountClosureRequest
	24,  // 174: savings.Savings.GetSavingsAccountNominees:input_type -> savings.GetSavingsAccountNomineesRequest
	22,  // 175: savings.Savings.UpdateSavingsAccountNominees:input_type -> savings.UpdateSavingsAccountNomineesRequest
	32,  // 176: savings.Savings.CreateAccount:output_type -> savings.CreateAccountResponse
	35,  // 177: savings.Savings.GetAccount:output_type -> savings.GetAccountResponse
	37,  // 178: savings.Savings.GetAccountsList:output_type -> savings.GetAccountsListResponse
	42,  // 179: savings.Savings.GetAccountBalance:output_type -> savings.GetAccountBalanceResponse
	44,  // 180: savings.Savings.UpdateAccount:output_type -> savings.UpdateAccountResponse
	46,  // 181: savings.Savings.GetOpeningBalance:output_type -> savings.GetOpeningBalanceResponse
	48,  // 182: savings.Savings.GetAccountBalanceWithSummary:output_type -> savings.GetAccountBalanceWithSummaryResponse
	51,  // 183: savings.Savings.GetListOfActiveAccounts:output_type -> savings.GetListOfActiveAccountsResponse
	53,  // 184: savings.Savings.CloseAccount:output_type -> savings.CloseAccountResponse
	55,  // 185: savings.Savings.ReopenAccount:output_type -> savings.ReopenAccountResponse
	57,  // 186: savings.Savings.IsTxnAllowed:output_type -> savings.IsTxnAllowedResponse
	30,  // 187: savings.Savings.GetAccountBalanceV1:output_type -> savings.GetAccountBalanceV1Response
	28,  // 188: savings.Savings.UpdateBalance:output_type -> savings.UpdateBalanceResponse
	59,  // 189: savings.Savings.StoreClosedAccountBalTransferData:output_type -> savings.StoreClosedAccountBalTransferDataResponse
	61,  // 190: savings.Savings.StoreClosedAccountBalTransferDataFromStatement:output_type -> savings.StoreClosedAccountBalTransferDataFromStatementResponse
	63,  // 191: savings.Savings.GetClosedAccountBalTransferData:output_type -> savings.GetClosedAccountBalTransferDataResponse
	120, // 192: savings.Savings.FetchDynamicElements:output_type -> dynamic_elements.FetchDynamicElementsResponse
	121, // 193: savings.Savings.DynamicElementCallback:output_type -> dynamic_elements.DynamicElementCallbackResponse
	65,  // 194: savings.Savings.GetEODSavBalanceHistory:output_type -> savings.GetEODSavBalanceHistoryResponse
	68,  // 195: savings.Savings.UpdateClosedAccountBalTransferData:output_type -> savings.UpdateClosedAccountBalTransferDataResponse
	70,  // 196: savings.Savings.FetchOrCreateSignAttempt:output_type -> savings.FetchOrCreateSignAttemptResponse
	75,  // 197: savings.Savings.GetSavingsAccountEssentials:output_type -> savings.GetSavingsAccountEssentialsResponse
	77,  // 198: savings.Savings.CreateOrGetSaClosureRequest:output_type -> savings.CreateOrGetSaClosureRequestResponse
	79,  // 199: savings.Savings.GetActiveSaClosureRequestForUser:output_type -> savings.GetActiveSaClosureRequestForUserResponse
	81,  // 200: savings.Savings.UpdateSaClosureRequestStatus:output_type -> savings.UpdateSaClosureRequestStatusResponse
	83,  // 201: savings.Savings.RecordSaClosureUserFeedback:output_type -> savings.RecordSaClosureUserFeedbackResponse
	85,  // 202: savings.Savings.GetSubmittedSaClosureRequests:output_type -> savings.GetSubmittedSaClosureRequestsResponse
	87,  // 203: savings.Savings.GetSaClosureRequestsByFilter:output_type -> savings.GetSaClosureRequestsByFilterResponse
	89,  // 204: savings.Savings.VerifyPanForAccountClosure:output_type -> savings.VerifyPanForAccountClosureResponse
	25,  // 205: savings.Savings.GetSavingsAccountNominees:output_type -> savings.GetSavingsAccountNomineesResponse
	23,  // 206: savings.Savings.UpdateSavingsAccountNominees:output_type -> savings.UpdateSavingsAccountNomineesResponse
	176, // [176:207] is the sub-list for method output_type
	145, // [145:176] is the sub-list for method input_type
	145, // [145:145] is the sub-list for extension type_name
	145, // [145:145] is the sub-list for extension extendee
	0,   // [0:145] is the sub-list for field type_name
}

func init() { file_api_savings_service_proto_init() }
func file_api_savings_service_proto_init() {
	if File_api_savings_service_proto != nil {
		return
	}
	file_api_savings_account_proto_init()
	file_api_savings_closed_accounts_balance_transfer_proto_init()
	file_api_savings_enums_proto_init()
	file_api_savings_savings_account_closure_request_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_savings_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSavingsAccountNomineesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSavingsAccountNomineesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSavingsAccountNomineesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSavingsAccountNomineesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SavingsAccountNominee); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBalanceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBalanceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAccountBalanceV1Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAccountBalanceV1Response); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAccountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAccountResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BankAccountIdentifier); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAccountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAccountResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAccountsListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAccountsListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PrimaryUserIdentifier); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BulkActorIdentifier); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActorUniqueAccountIdentifier); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAccountBalanceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAccountBalanceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAccountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAccountResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOpeningBalanceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOpeningBalanceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAccountBalanceWithSummaryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAccountBalanceWithSummaryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountSummaryBucketDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetListOfActiveAccountsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetListOfActiveAccountsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CloseAccountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CloseAccountResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReopenAccountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReopenAccountResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsTxnAllowedRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsTxnAllowedResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StoreClosedAccountBalTransferDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StoreClosedAccountBalTransferDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StoreClosedAccountBalTransferDataFromStatementRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StoreClosedAccountBalTransferDataFromStatementResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetClosedAccountBalTransferDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetClosedAccountBalTransferDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEODSavBalanceHistoryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEODSavBalanceHistoryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BalanceByDate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateClosedAccountBalTransferDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateClosedAccountBalTransferDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchOrCreateSignAttemptRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchOrCreateSignAttemptResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActorIdBankFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountNumberBankFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSavingsAccountEssentialsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SavingsAccountEssentials); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSavingsAccountEssentialsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateOrGetSaClosureRequestRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateOrGetSaClosureRequestResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActiveSaClosureRequestForUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActiveSaClosureRequestForUserResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSaClosureRequestStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSaClosureRequestStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecordSaClosureUserFeedbackRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecordSaClosureUserFeedbackResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSubmittedSaClosureRequestsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSubmittedSaClosureRequestsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSaClosureRequestsByFilterRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSaClosureRequestsByFilterResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyPanForAccountClosureRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyPanForAccountClosureResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAccountRequest_Options); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetListOfActiveAccountsResponse_Account); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_service_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsTxnAllowedResponse_SavingsTxnLimits); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_savings_service_proto_msgTypes[7].OneofWrappers = []interface{}{
		(*GetAccountBalanceV1Request_Id)(nil),
		(*GetAccountBalanceV1Request_ExternalId)(nil),
	}
	file_api_savings_service_proto_msgTypes[12].OneofWrappers = []interface{}{
		(*GetAccountRequest_Id)(nil),
		(*GetAccountRequest_ExternalId)(nil),
		(*GetAccountRequest_PrimaryUserId)(nil),
		(*GetAccountRequest_ActorId)(nil),
		(*GetAccountRequest_AccountNumBankFilter)(nil),
		(*GetAccountRequest_ActorUniqueAccountIdentifier)(nil),
	}
	file_api_savings_service_proto_msgTypes[14].OneofWrappers = []interface{}{
		(*GetAccountsListRequest_UserIds)(nil),
		(*GetAccountsListRequest_BulkActorIdentifier)(nil),
	}
	file_api_savings_service_proto_msgTypes[19].OneofWrappers = []interface{}{
		(*GetAccountBalanceRequest_Id)(nil),
		(*GetAccountBalanceRequest_ExternalId)(nil),
	}
	file_api_savings_service_proto_msgTypes[21].OneofWrappers = []interface{}{
		(*UpdateAccountRequest_Id)(nil),
		(*UpdateAccountRequest_ExternalId)(nil),
		(*UpdateAccountRequest_PrimaryAccountHolder)(nil),
	}
	file_api_savings_service_proto_msgTypes[23].OneofWrappers = []interface{}{
		(*GetOpeningBalanceRequest_Id)(nil),
		(*GetOpeningBalanceRequest_ExternalId)(nil),
		(*GetOpeningBalanceRequest_PrimaryAccountHolderActor)(nil),
	}
	file_api_savings_service_proto_msgTypes[25].OneofWrappers = []interface{}{
		(*GetAccountBalanceWithSummaryRequest_Id)(nil),
		(*GetAccountBalanceWithSummaryRequest_ExternalId)(nil),
		(*GetAccountBalanceWithSummaryRequest_PrimaryAccountHolderActor)(nil),
	}
	file_api_savings_service_proto_msgTypes[30].OneofWrappers = []interface{}{
		(*CloseAccountRequest_Id)(nil),
		(*CloseAccountRequest_ExternalId)(nil),
		(*CloseAccountRequest_PrimaryUserId)(nil),
	}
	file_api_savings_service_proto_msgTypes[32].OneofWrappers = []interface{}{
		(*ReopenAccountRequest_Id)(nil),
		(*ReopenAccountRequest_ExternalId)(nil),
		(*ReopenAccountRequest_PrimaryUserId)(nil),
	}
	file_api_savings_service_proto_msgTypes[34].OneofWrappers = []interface{}{
		(*IsTxnAllowedRequest_Id)(nil),
		(*IsTxnAllowedRequest_ExternalId)(nil),
		(*IsTxnAllowedRequest_PrimaryAccountHolderActor)(nil),
	}
	file_api_savings_service_proto_msgTypes[51].OneofWrappers = []interface{}{
		(*GetSavingsAccountEssentialsRequest_ActorIdBankFilter)(nil),
		(*GetSavingsAccountEssentialsRequest_AccountNumBankFilter)(nil),
		(*GetSavingsAccountEssentialsRequest_ActorUniqueAccountIdentifier)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_savings_service_proto_rawDesc,
			NumEnums:      22,
			NumMessages:   71,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_savings_service_proto_goTypes,
		DependencyIndexes: file_api_savings_service_proto_depIdxs,
		EnumInfos:         file_api_savings_service_proto_enumTypes,
		MessageInfos:      file_api_savings_service_proto_msgTypes,
	}.Build()
	File_api_savings_service_proto = out.File
	file_api_savings_service_proto_rawDesc = nil
	file_api_savings_service_proto_goTypes = nil
	file_api_savings_service_proto_depIdxs = nil
}
