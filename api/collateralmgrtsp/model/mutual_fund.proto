//go:generate gen_sql -types=AssetDetails
syntax = "proto3";

package collateralmgrtsp.model;

import "api/collateralmgrtsp/common/enums.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/collateralmgrtsp/model";
option java_package = "com.github.epifi.gamma.api.collateralmgrtsp.model";

message MutualFund {
  string id = 1;
  string isin = 2;
  // latest known nav of the scheme
  google.type.Money nav = 3;
  // date on which NAV was captured
  google.protobuf.Timestamp nav_date = 4;
  // timestamp of updating the nav value in db
  google.protobuf.Timestamp nav_updated_at = 5;
  // source of the NAV
  common.MutualFundNavSource nav_source = 6;
  google.protobuf.Timestamp created_at = 7;
  google.protobuf.Timestamp updated_at = 8;
  google.protobuf.Timestamp deleted_at = 9;
}

enum MutualFundFieldMask {
  MUTUAL_FUND_FIELD_MASK_UNSPECIFIED = 0;
  MUTUAL_FUND_FIELD_MASK_ID = 1;
  MUTUAL_FUND_FIELD_MASK_ISIN = 2;
  MUTUAL_FUND_FIELD_MASK_NAV = 3;
  MUTUAL_FUND_FIELD_MASK_NAV_DATE = 4;
  MUTUAL_FUND_FIELD_MASK_NAV_UPDATED_AT = 5;
  MUTUAL_FUND_FIELD_MASK_NAV_SOURCE = 6;
  MUTUAL_FUND_FIELD_MASK_CREATED_AT = 7;
  MUTUAL_FUND_FIELD_MASK_UPDATED_AT = 8;
  MUTUAL_FUND_FIELD_MASK_DELETED_AT = 9;
}
