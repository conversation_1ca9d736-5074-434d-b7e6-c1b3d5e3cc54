package sherlock_user

import (
	alPb "github.com/epifi/gamma/api/cx/audit_log"
)

func (r *CreateSherlockUserRequest) GetAction() alPb.Action {
	return alPb.Action_CREATE
}

func (r *CreateSherlockUserRequest) GetObject() alPb.Object {
	return alPb.Object_SHERLOCK_USER
}

func (r *UpdateSherlockUserInfoRequest) GetAction() alPb.Action {
	return alPb.Action_UPDATE
}

func (r *UpdateSherlockUserInfoRequest) GetObject() alPb.Object {
	return alPb.Object_SHERLOCK_USER
}

func (r *GetSherlockUserRequest) GetAction() alPb.Action {
	return alPb.Action_FETCH
}

func (r *GetSherlockUserRequest) GetObject() alPb.Object {
	return alPb.Object_SHERLOCK_USER
}

func (r *GetAllSherlockUsersRequest) GetAction() alPb.Action {
	return alPb.Action_FETCH
}

func (r *GetAllSherlockUsersRequest) GetObject() alPb.Object {
	return alPb.Object_SHERLOCK_USER
}

func (r *FetchLoggedInUserInfoRequest) GetAction() alPb.Action {
	return alPb.Action_FETCH
}

func (r *FetchLoggedInUserInfoRequest) GetObject() alPb.Object {
	return alPb.Object_SHERLOCK_USER
}
