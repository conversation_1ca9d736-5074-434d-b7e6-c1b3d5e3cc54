// Code generated by MockGen. DO NOT EDIT.
// Source: api/cx/risk_ops/chart/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	chart "github.com/epifi/gamma/api/cx/risk_ops/chart"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockChartServiceClient is a mock of ChartServiceClient interface.
type MockChartServiceClient struct {
	ctrl     *gomock.Controller
	recorder *MockChartServiceClientMockRecorder
}

// MockChartServiceClientMockRecorder is the mock recorder for MockChartServiceClient.
type MockChartServiceClientMockRecorder struct {
	mock *MockChartServiceClient
}

// NewMockChartServiceClient creates a new mock instance.
func NewMockChartServiceClient(ctrl *gomock.Controller) *MockChartServiceClient {
	mock := &MockChartServiceClient{ctrl: ctrl}
	mock.recorder = &MockChartServiceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChartServiceClient) EXPECT() *MockChartServiceClientMockRecorder {
	return m.recorder
}

// GetCharts mocks base method.
func (m *MockChartServiceClient) GetCharts(ctx context.Context, in *chart.GetChartsRequest, opts ...grpc.CallOption) (*chart.GetChartsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCharts", varargs...)
	ret0, _ := ret[0].(*chart.GetChartsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCharts indicates an expected call of GetCharts.
func (mr *MockChartServiceClientMockRecorder) GetCharts(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCharts", reflect.TypeOf((*MockChartServiceClient)(nil).GetCharts), varargs...)
}

// GetHeatMap mocks base method.
func (m *MockChartServiceClient) GetHeatMap(ctx context.Context, in *chart.GetHeatMapRequest, opts ...grpc.CallOption) (*chart.GetHeatMapResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetHeatMap", varargs...)
	ret0, _ := ret[0].(*chart.GetHeatMapResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHeatMap indicates an expected call of GetHeatMap.
func (mr *MockChartServiceClientMockRecorder) GetHeatMap(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHeatMap", reflect.TypeOf((*MockChartServiceClient)(nil).GetHeatMap), varargs...)
}

// MockChartServiceServer is a mock of ChartServiceServer interface.
type MockChartServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockChartServiceServerMockRecorder
}

// MockChartServiceServerMockRecorder is the mock recorder for MockChartServiceServer.
type MockChartServiceServerMockRecorder struct {
	mock *MockChartServiceServer
}

// NewMockChartServiceServer creates a new mock instance.
func NewMockChartServiceServer(ctrl *gomock.Controller) *MockChartServiceServer {
	mock := &MockChartServiceServer{ctrl: ctrl}
	mock.recorder = &MockChartServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChartServiceServer) EXPECT() *MockChartServiceServerMockRecorder {
	return m.recorder
}

// GetCharts mocks base method.
func (m *MockChartServiceServer) GetCharts(arg0 context.Context, arg1 *chart.GetChartsRequest) (*chart.GetChartsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCharts", arg0, arg1)
	ret0, _ := ret[0].(*chart.GetChartsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCharts indicates an expected call of GetCharts.
func (mr *MockChartServiceServerMockRecorder) GetCharts(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCharts", reflect.TypeOf((*MockChartServiceServer)(nil).GetCharts), arg0, arg1)
}

// GetHeatMap mocks base method.
func (m *MockChartServiceServer) GetHeatMap(arg0 context.Context, arg1 *chart.GetHeatMapRequest) (*chart.GetHeatMapResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHeatMap", arg0, arg1)
	ret0, _ := ret[0].(*chart.GetHeatMapResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHeatMap indicates an expected call of GetHeatMap.
func (mr *MockChartServiceServerMockRecorder) GetHeatMap(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHeatMap", reflect.TypeOf((*MockChartServiceServer)(nil).GetHeatMap), arg0, arg1)
}

// MockUnsafeChartServiceServer is a mock of UnsafeChartServiceServer interface.
type MockUnsafeChartServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeChartServiceServerMockRecorder
}

// MockUnsafeChartServiceServerMockRecorder is the mock recorder for MockUnsafeChartServiceServer.
type MockUnsafeChartServiceServerMockRecorder struct {
	mock *MockUnsafeChartServiceServer
}

// NewMockUnsafeChartServiceServer creates a new mock instance.
func NewMockUnsafeChartServiceServer(ctrl *gomock.Controller) *MockUnsafeChartServiceServer {
	mock := &MockUnsafeChartServiceServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeChartServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeChartServiceServer) EXPECT() *MockUnsafeChartServiceServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedChartServiceServer mocks base method.
func (m *MockUnsafeChartServiceServer) mustEmbedUnimplementedChartServiceServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedChartServiceServer")
}

// mustEmbedUnimplementedChartServiceServer indicates an expected call of mustEmbedUnimplementedChartServiceServer.
func (mr *MockUnsafeChartServiceServerMockRecorder) mustEmbedUnimplementedChartServiceServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedChartServiceServer", reflect.TypeOf((*MockUnsafeChartServiceServer)(nil).mustEmbedUnimplementedChartServiceServer))
}
