// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/cx/risk_ops/chart/enums.proto

package chart

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ChartType int32

const (
	ChartType_CHART_TYPE_UNSPECIFIED ChartType = 0
	ChartType_CHART_TYPE_BAR         ChartType = 1
)

// Enum value maps for ChartType.
var (
	ChartType_name = map[int32]string{
		0: "CHART_TYPE_UNSPECIFIED",
		1: "CHART_TYPE_BAR",
	}
	ChartType_value = map[string]int32{
		"CHART_TYPE_UNSPECIFIED": 0,
		"CHART_TYPE_BAR":         1,
	}
)

func (x ChartType) Enum() *ChartType {
	p := new(ChartType)
	*p = x
	return p
}

func (x ChartType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ChartType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_risk_ops_chart_enums_proto_enumTypes[0].Descriptor()
}

func (ChartType) Type() protoreflect.EnumType {
	return &file_api_cx_risk_ops_chart_enums_proto_enumTypes[0]
}

func (x ChartType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ChartType.Descriptor instead.
func (ChartType) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_risk_ops_chart_enums_proto_rawDescGZIP(), []int{0}
}

type AggregateOption int32

const (
	AggregateOption_AGGREGATE_OPTION_UNSPECIFIED      AggregateOption = 0
	AggregateOption_AGGREGATE_OPTION_MONTH_OVER_MONTH AggregateOption = 1
	AggregateOption_AGGREGATE_OPTION_WEEK_OVER_WEEK   AggregateOption = 2
	AggregateOption_AGGREGATE_OPTION_DAY_WISE         AggregateOption = 3
)

// Enum value maps for AggregateOption.
var (
	AggregateOption_name = map[int32]string{
		0: "AGGREGATE_OPTION_UNSPECIFIED",
		1: "AGGREGATE_OPTION_MONTH_OVER_MONTH",
		2: "AGGREGATE_OPTION_WEEK_OVER_WEEK",
		3: "AGGREGATE_OPTION_DAY_WISE",
	}
	AggregateOption_value = map[string]int32{
		"AGGREGATE_OPTION_UNSPECIFIED":      0,
		"AGGREGATE_OPTION_MONTH_OVER_MONTH": 1,
		"AGGREGATE_OPTION_WEEK_OVER_WEEK":   2,
		"AGGREGATE_OPTION_DAY_WISE":         3,
	}
)

func (x AggregateOption) Enum() *AggregateOption {
	p := new(AggregateOption)
	*p = x
	return p
}

func (x AggregateOption) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AggregateOption) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_risk_ops_chart_enums_proto_enumTypes[1].Descriptor()
}

func (AggregateOption) Type() protoreflect.EnumType {
	return &file_api_cx_risk_ops_chart_enums_proto_enumTypes[1]
}

func (x AggregateOption) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AggregateOption.Descriptor instead.
func (AggregateOption) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_risk_ops_chart_enums_proto_rawDescGZIP(), []int{1}
}

var File_api_cx_risk_ops_chart_enums_proto protoreflect.FileDescriptor

var file_api_cx_risk_ops_chart_enums_proto_rawDesc = []byte{
	0x0a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x6f, 0x70,
	0x73, 0x2f, 0x63, 0x68, 0x61, 0x72, 0x74, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x11, 0x63, 0x78, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x6f, 0x70, 0x73,
	0x2e, 0x63, 0x68, 0x61, 0x72, 0x74, 0x2a, 0x3b, 0x0a, 0x09, 0x43, 0x68, 0x61, 0x72, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x43, 0x48, 0x41, 0x52, 0x54, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x12, 0x0a, 0x0e, 0x43, 0x48, 0x41, 0x52, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x41,
	0x52, 0x10, 0x01, 0x2a, 0x9e, 0x01, 0x0a, 0x0f, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74,
	0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x1c, 0x41, 0x47, 0x47, 0x52, 0x45,
	0x47, 0x41, 0x54, 0x45, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x25, 0x0a, 0x21, 0x41, 0x47, 0x47,
	0x52, 0x45, 0x47, 0x41, 0x54, 0x45, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x4f,
	0x4e, 0x54, 0x48, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x5f, 0x4d, 0x4f, 0x4e, 0x54, 0x48, 0x10, 0x01,
	0x12, 0x23, 0x0a, 0x1f, 0x41, 0x47, 0x47, 0x52, 0x45, 0x47, 0x41, 0x54, 0x45, 0x5f, 0x4f, 0x50,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x57, 0x45, 0x45, 0x4b, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x5f, 0x57,
	0x45, 0x45, 0x4b, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x41, 0x47, 0x47, 0x52, 0x45, 0x47, 0x41,
	0x54, 0x45, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x41, 0x59, 0x5f, 0x57, 0x49,
	0x53, 0x45, 0x10, 0x03, 0x42, 0x5c, 0x0a, 0x2c, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x63, 0x78, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x63,
	0x68, 0x61, 0x72, 0x74, 0x5a, 0x2c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x63, 0x78, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x6f, 0x70, 0x73, 0x2f, 0x63, 0x68, 0x61,
	0x72, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_cx_risk_ops_chart_enums_proto_rawDescOnce sync.Once
	file_api_cx_risk_ops_chart_enums_proto_rawDescData = file_api_cx_risk_ops_chart_enums_proto_rawDesc
)

func file_api_cx_risk_ops_chart_enums_proto_rawDescGZIP() []byte {
	file_api_cx_risk_ops_chart_enums_proto_rawDescOnce.Do(func() {
		file_api_cx_risk_ops_chart_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_cx_risk_ops_chart_enums_proto_rawDescData)
	})
	return file_api_cx_risk_ops_chart_enums_proto_rawDescData
}

var file_api_cx_risk_ops_chart_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_cx_risk_ops_chart_enums_proto_goTypes = []interface{}{
	(ChartType)(0),       // 0: cx.risk_ops.chart.ChartType
	(AggregateOption)(0), // 1: cx.risk_ops.chart.AggregateOption
}
var file_api_cx_risk_ops_chart_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_cx_risk_ops_chart_enums_proto_init() }
func file_api_cx_risk_ops_chart_enums_proto_init() {
	if File_api_cx_risk_ops_chart_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_cx_risk_ops_chart_enums_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_cx_risk_ops_chart_enums_proto_goTypes,
		DependencyIndexes: file_api_cx_risk_ops_chart_enums_proto_depIdxs,
		EnumInfos:         file_api_cx_risk_ops_chart_enums_proto_enumTypes,
	}.Build()
	File_api_cx_risk_ops_chart_enums_proto = out.File
	file_api_cx_risk_ops_chart_enums_proto_rawDesc = nil
	file_api_cx_risk_ops_chart_enums_proto_goTypes = nil
	file_api_cx_risk_ops_chart_enums_proto_depIdxs = nil
}
