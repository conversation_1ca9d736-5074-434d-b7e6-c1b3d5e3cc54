// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/cx/risk_ops/chart/service.proto

package chart

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	cx "github.com/epifi/gamma/api/cx"
	webui "github.com/epifi/gamma/api/typesv2/webui"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Status int32

const (
	Status_OK Status = 0
	// ISE resposne due to some internal error in the rpc
	Status_INTERNAL_SERVER_ERROR Status = 1
	// Invalid argument passed in the request
	Status_INVALID_ARGUMENT Status = 3
)

// Enum value maps for Status.
var (
	Status_name = map[int32]string{
		0: "OK",
		1: "INTERNAL_SERVER_ERROR",
		3: "INVALID_ARGUMENT",
	}
	Status_value = map[string]int32{
		"OK":                    0,
		"INTERNAL_SERVER_ERROR": 1,
		"INVALID_ARGUMENT":      3,
	}
)

func (x Status) Enum() *Status {
	p := new(Status)
	*p = x
	return p
}

func (x Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_risk_ops_chart_service_proto_enumTypes[0].Descriptor()
}

func (Status) Type() protoreflect.EnumType {
	return &file_api_cx_risk_ops_chart_service_proto_enumTypes[0]
}

func (x Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Status.Descriptor instead.
func (Status) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_risk_ops_chart_service_proto_rawDescGZIP(), []int{0}
}

type GetChartsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// header is mandatory
	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	CaseId string     `protobuf:"bytes,2,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
}

func (x *GetChartsRequest) Reset() {
	*x = GetChartsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_risk_ops_chart_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetChartsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChartsRequest) ProtoMessage() {}

func (x *GetChartsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_risk_ops_chart_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChartsRequest.ProtoReflect.Descriptor instead.
func (*GetChartsRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_risk_ops_chart_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetChartsRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetChartsRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

type GetChartsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Charts []*Chart    `protobuf:"bytes,2,rep,name=Charts,proto3" json:"Charts,omitempty"`
}

func (x *GetChartsResponse) Reset() {
	*x = GetChartsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_risk_ops_chart_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetChartsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChartsResponse) ProtoMessage() {}

func (x *GetChartsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_risk_ops_chart_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChartsResponse.ProtoReflect.Descriptor instead.
func (*GetChartsResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_risk_ops_chart_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetChartsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetChartsResponse) GetCharts() []*Chart {
	if x != nil {
		return x.Charts
	}
	return nil
}

type GetHeatMapRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// header is mandatory
	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	CaseId string     `protobuf:"bytes,2,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	// aggregate option for month over month or week over week.
	// for P0 we would only support month over month by default
	AggregateOption AggregateOption `protobuf:"varint,3,opt,name=aggregate_option,json=aggregateOption,proto3,enum=cx.risk_ops.chart.AggregateOption" json:"aggregate_option,omitempty"`
	// date after which the data for heat map would be fetched
	FilterAfterDate *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=filter_after_date,json=filterAfterDate,proto3" json:"filter_after_date,omitempty"`
}

func (x *GetHeatMapRequest) Reset() {
	*x = GetHeatMapRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_risk_ops_chart_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetHeatMapRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetHeatMapRequest) ProtoMessage() {}

func (x *GetHeatMapRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_risk_ops_chart_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetHeatMapRequest.ProtoReflect.Descriptor instead.
func (*GetHeatMapRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_risk_ops_chart_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetHeatMapRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetHeatMapRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *GetHeatMapRequest) GetAggregateOption() AggregateOption {
	if x != nil {
		return x.AggregateOption
	}
	return AggregateOption_AGGREGATE_OPTION_UNSPECIFIED
}

func (x *GetHeatMapRequest) GetFilterAfterDate() *timestamppb.Timestamp {
	if x != nil {
		return x.FilterAfterDate
	}
	return nil
}

type GetHeatMapResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Contains all the rows and columns for the heat map table.
	Table *webui.Table `protobuf:"bytes,2,opt,name=table,proto3" json:"table,omitempty"`
}

func (x *GetHeatMapResponse) Reset() {
	*x = GetHeatMapResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_risk_ops_chart_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetHeatMapResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetHeatMapResponse) ProtoMessage() {}

func (x *GetHeatMapResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_risk_ops_chart_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetHeatMapResponse.ProtoReflect.Descriptor instead.
func (*GetHeatMapResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_risk_ops_chart_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetHeatMapResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetHeatMapResponse) GetTable() *webui.Table {
	if x != nil {
		return x.Table
	}
	return nil
}

var File_api_cx_risk_ops_chart_service_proto protoreflect.FileDescriptor

var file_api_cx_risk_ops_chart_service_proto_rawDesc = []byte{
	0x0a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x6f, 0x70,
	0x73, 0x2f, 0x63, 0x68, 0x61, 0x72, 0x74, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x63, 0x78, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x6f,
	0x70, 0x73, 0x2e, 0x63, 0x68, 0x61, 0x72, 0x74, 0x1a, 0x13, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78,
	0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x61,
	0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f,
	0x63, 0x78, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x6f, 0x70, 0x73, 0x2f, 0x63, 0x68, 0x61, 0x72,
	0x74, 0x2f, 0x63, 0x68, 0x61, 0x72, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x61,
	0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x6f, 0x70, 0x73, 0x2f, 0x63,
	0x68, 0x61, 0x72, 0x74, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x77, 0x65, 0x62, 0x75, 0x69, 0x2f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x62, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x72, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x78, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x20, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x63, 0x61, 0x73,
	0x65, 0x49, 0x64, 0x22, 0x6a, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x72, 0x74, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x30, 0x0a,
	0x06, 0x43, 0x68, 0x61, 0x72, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x63, 0x78, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x63, 0x68, 0x61, 0x72,
	0x74, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x74, 0x52, 0x06, 0x43, 0x68, 0x61, 0x72, 0x74, 0x73, 0x22,
	0xfa, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x48, 0x65, 0x61, 0x74, 0x4d, 0x61, 0x70, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x78, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x63,
	0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x4d, 0x0a, 0x10, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61,
	0x74, 0x65, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x22, 0x2e, 0x63, 0x78, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x63, 0x68,
	0x61, 0x72, 0x74, 0x2e, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x46, 0x0a, 0x11, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x61,
	0x66, 0x74, 0x65, 0x72, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0f, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x41, 0x66, 0x74, 0x65, 0x72, 0x44, 0x61, 0x74, 0x65, 0x22, 0x69, 0x0a, 0x12,
	0x47, 0x65, 0x74, 0x48, 0x65, 0x61, 0x74, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x77, 0x65, 0x62, 0x75, 0x69, 0x2e, 0x54, 0x61, 0x62, 0x6c, 0x65,
	0x52, 0x05, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2a, 0x41, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x49, 0x4e, 0x54,
	0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x45, 0x52, 0x52,
	0x4f, 0x52, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f,
	0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x32, 0x8d, 0x02, 0x0a, 0x0c, 0x43,
	0x68, 0x61, 0x72, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x7c, 0x0a, 0x09, 0x47,
	0x65, 0x74, 0x43, 0x68, 0x61, 0x72, 0x74, 0x73, 0x12, 0x23, 0x2e, 0x63, 0x78, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x63, 0x68, 0x61, 0x72, 0x74, 0x2e, 0x47, 0x65, 0x74,
	0x43, 0x68, 0x61, 0x72, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e,
	0x63, 0x78, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x63, 0x68, 0x61, 0x72,
	0x74, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x72, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x00, 0xda, 0xc2, 0xd7, 0x0a, 0x0b, 0x49,
	0x4e, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x01, 0xa8,
	0x8d, 0xe8, 0x6a, 0x01, 0xf0, 0x92, 0xe8, 0x6a, 0x00, 0x12, 0x7f, 0x0a, 0x0a, 0x47, 0x65, 0x74,
	0x48, 0x65, 0x61, 0x74, 0x4d, 0x61, 0x70, 0x12, 0x24, 0x2e, 0x63, 0x78, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x63, 0x68, 0x61, 0x72, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x48,
	0x65, 0x61, 0x74, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e,
	0x63, 0x78, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x63, 0x68, 0x61, 0x72,
	0x74, 0x2e, 0x47, 0x65, 0x74, 0x48, 0x65, 0x61, 0x74, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x00, 0xda, 0xc2, 0xd7, 0x0a, 0x0b,
	0x49, 0x4e, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x01,
	0xa8, 0x8d, 0xe8, 0x6a, 0x01, 0xf0, 0x92, 0xe8, 0x6a, 0x00, 0x42, 0x5c, 0x0a, 0x2c, 0x63, 0x6f,
	0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x78, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x63, 0x68, 0x61, 0x72, 0x74, 0x5a, 0x2c, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x6f,
	0x70, 0x73, 0x2f, 0x63, 0x68, 0x61, 0x72, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_cx_risk_ops_chart_service_proto_rawDescOnce sync.Once
	file_api_cx_risk_ops_chart_service_proto_rawDescData = file_api_cx_risk_ops_chart_service_proto_rawDesc
)

func file_api_cx_risk_ops_chart_service_proto_rawDescGZIP() []byte {
	file_api_cx_risk_ops_chart_service_proto_rawDescOnce.Do(func() {
		file_api_cx_risk_ops_chart_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_cx_risk_ops_chart_service_proto_rawDescData)
	})
	return file_api_cx_risk_ops_chart_service_proto_rawDescData
}

var file_api_cx_risk_ops_chart_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_cx_risk_ops_chart_service_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_cx_risk_ops_chart_service_proto_goTypes = []interface{}{
	(Status)(0),                   // 0: cx.risk_ops.chart.Status
	(*GetChartsRequest)(nil),      // 1: cx.risk_ops.chart.GetChartsRequest
	(*GetChartsResponse)(nil),     // 2: cx.risk_ops.chart.GetChartsResponse
	(*GetHeatMapRequest)(nil),     // 3: cx.risk_ops.chart.GetHeatMapRequest
	(*GetHeatMapResponse)(nil),    // 4: cx.risk_ops.chart.GetHeatMapResponse
	(*cx.Header)(nil),             // 5: cx.Header
	(*rpc.Status)(nil),            // 6: rpc.Status
	(*Chart)(nil),                 // 7: cx.risk_ops.chart.Chart
	(AggregateOption)(0),          // 8: cx.risk_ops.chart.AggregateOption
	(*timestamppb.Timestamp)(nil), // 9: google.protobuf.Timestamp
	(*webui.Table)(nil),           // 10: api.typesv2.webui.Table
}
var file_api_cx_risk_ops_chart_service_proto_depIdxs = []int32{
	5,  // 0: cx.risk_ops.chart.GetChartsRequest.header:type_name -> cx.Header
	6,  // 1: cx.risk_ops.chart.GetChartsResponse.status:type_name -> rpc.Status
	7,  // 2: cx.risk_ops.chart.GetChartsResponse.Charts:type_name -> cx.risk_ops.chart.Chart
	5,  // 3: cx.risk_ops.chart.GetHeatMapRequest.header:type_name -> cx.Header
	8,  // 4: cx.risk_ops.chart.GetHeatMapRequest.aggregate_option:type_name -> cx.risk_ops.chart.AggregateOption
	9,  // 5: cx.risk_ops.chart.GetHeatMapRequest.filter_after_date:type_name -> google.protobuf.Timestamp
	6,  // 6: cx.risk_ops.chart.GetHeatMapResponse.status:type_name -> rpc.Status
	10, // 7: cx.risk_ops.chart.GetHeatMapResponse.table:type_name -> api.typesv2.webui.Table
	1,  // 8: cx.risk_ops.chart.ChartService.GetCharts:input_type -> cx.risk_ops.chart.GetChartsRequest
	3,  // 9: cx.risk_ops.chart.ChartService.GetHeatMap:input_type -> cx.risk_ops.chart.GetHeatMapRequest
	2,  // 10: cx.risk_ops.chart.ChartService.GetCharts:output_type -> cx.risk_ops.chart.GetChartsResponse
	4,  // 11: cx.risk_ops.chart.ChartService.GetHeatMap:output_type -> cx.risk_ops.chart.GetHeatMapResponse
	10, // [10:12] is the sub-list for method output_type
	8,  // [8:10] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_api_cx_risk_ops_chart_service_proto_init() }
func file_api_cx_risk_ops_chart_service_proto_init() {
	if File_api_cx_risk_ops_chart_service_proto != nil {
		return
	}
	file_api_cx_risk_ops_chart_chart_proto_init()
	file_api_cx_risk_ops_chart_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_cx_risk_ops_chart_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetChartsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_risk_ops_chart_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetChartsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_risk_ops_chart_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetHeatMapRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_risk_ops_chart_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetHeatMapResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_cx_risk_ops_chart_service_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_cx_risk_ops_chart_service_proto_goTypes,
		DependencyIndexes: file_api_cx_risk_ops_chart_service_proto_depIdxs,
		EnumInfos:         file_api_cx_risk_ops_chart_service_proto_enumTypes,
		MessageInfos:      file_api_cx_risk_ops_chart_service_proto_msgTypes,
	}.Build()
	File_api_cx_risk_ops_chart_service_proto = out.File
	file_api_cx_risk_ops_chart_service_proto_rawDesc = nil
	file_api_cx_risk_ops_chart_service_proto_goTypes = nil
	file_api_cx_risk_ops_chart_service_proto_depIdxs = nil
}
