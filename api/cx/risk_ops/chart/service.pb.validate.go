// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/cx/risk_ops/chart/service.proto

package chart

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetChartsRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetChartsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetChartsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetChartsRequestMultiError, or nil if none found.
func (m *GetChartsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetChartsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetChartsRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetChartsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetChartsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetChartsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetCaseId()) < 1 {
		err := GetChartsRequestValidationError{
			field:  "CaseId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetChartsRequestMultiError(errors)
	}

	return nil
}

// GetChartsRequestMultiError is an error wrapping multiple validation errors
// returned by GetChartsRequest.ValidateAll() if the designated constraints
// aren't met.
type GetChartsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetChartsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetChartsRequestMultiError) AllErrors() []error { return m }

// GetChartsRequestValidationError is the validation error returned by
// GetChartsRequest.Validate if the designated constraints aren't met.
type GetChartsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetChartsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetChartsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetChartsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetChartsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetChartsRequestValidationError) ErrorName() string { return "GetChartsRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetChartsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetChartsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetChartsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetChartsRequestValidationError{}

// Validate checks the field values on GetChartsResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetChartsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetChartsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetChartsResponseMultiError, or nil if none found.
func (m *GetChartsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetChartsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetChartsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetChartsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetChartsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCharts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetChartsResponseValidationError{
						field:  fmt.Sprintf("Charts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetChartsResponseValidationError{
						field:  fmt.Sprintf("Charts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetChartsResponseValidationError{
					field:  fmt.Sprintf("Charts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetChartsResponseMultiError(errors)
	}

	return nil
}

// GetChartsResponseMultiError is an error wrapping multiple validation errors
// returned by GetChartsResponse.ValidateAll() if the designated constraints
// aren't met.
type GetChartsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetChartsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetChartsResponseMultiError) AllErrors() []error { return m }

// GetChartsResponseValidationError is the validation error returned by
// GetChartsResponse.Validate if the designated constraints aren't met.
type GetChartsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetChartsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetChartsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetChartsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetChartsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetChartsResponseValidationError) ErrorName() string {
	return "GetChartsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetChartsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetChartsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetChartsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetChartsResponseValidationError{}

// Validate checks the field values on GetHeatMapRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetHeatMapRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetHeatMapRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetHeatMapRequestMultiError, or nil if none found.
func (m *GetHeatMapRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetHeatMapRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetHeatMapRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetHeatMapRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetHeatMapRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetHeatMapRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetCaseId()) < 1 {
		err := GetHeatMapRequestValidationError{
			field:  "CaseId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for AggregateOption

	if all {
		switch v := interface{}(m.GetFilterAfterDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetHeatMapRequestValidationError{
					field:  "FilterAfterDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetHeatMapRequestValidationError{
					field:  "FilterAfterDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilterAfterDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetHeatMapRequestValidationError{
				field:  "FilterAfterDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetHeatMapRequestMultiError(errors)
	}

	return nil
}

// GetHeatMapRequestMultiError is an error wrapping multiple validation errors
// returned by GetHeatMapRequest.ValidateAll() if the designated constraints
// aren't met.
type GetHeatMapRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetHeatMapRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetHeatMapRequestMultiError) AllErrors() []error { return m }

// GetHeatMapRequestValidationError is the validation error returned by
// GetHeatMapRequest.Validate if the designated constraints aren't met.
type GetHeatMapRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetHeatMapRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetHeatMapRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetHeatMapRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetHeatMapRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetHeatMapRequestValidationError) ErrorName() string {
	return "GetHeatMapRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetHeatMapRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetHeatMapRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetHeatMapRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetHeatMapRequestValidationError{}

// Validate checks the field values on GetHeatMapResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetHeatMapResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetHeatMapResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetHeatMapResponseMultiError, or nil if none found.
func (m *GetHeatMapResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetHeatMapResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetHeatMapResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetHeatMapResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetHeatMapResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTable()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetHeatMapResponseValidationError{
					field:  "Table",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetHeatMapResponseValidationError{
					field:  "Table",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTable()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetHeatMapResponseValidationError{
				field:  "Table",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetHeatMapResponseMultiError(errors)
	}

	return nil
}

// GetHeatMapResponseMultiError is an error wrapping multiple validation errors
// returned by GetHeatMapResponse.ValidateAll() if the designated constraints
// aren't met.
type GetHeatMapResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetHeatMapResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetHeatMapResponseMultiError) AllErrors() []error { return m }

// GetHeatMapResponseValidationError is the validation error returned by
// GetHeatMapResponse.Validate if the designated constraints aren't met.
type GetHeatMapResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetHeatMapResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetHeatMapResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetHeatMapResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetHeatMapResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetHeatMapResponseValidationError) ErrorName() string {
	return "GetHeatMapResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetHeatMapResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetHeatMapResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetHeatMapResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetHeatMapResponseValidationError{}
