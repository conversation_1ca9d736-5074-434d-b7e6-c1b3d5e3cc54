// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/cx/developer/actions/enums.proto

package actions

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// List of available actions in sherlock for developers
type DeveloperActions int32

const (
	DeveloperActions_DEVELOPER_ACTIONS_UNSPECIFIED DeveloperActions = 0
	DeveloperActions_RETRY_LIVENESS                DeveloperActions = 1
	// https://github.com/epiFi/protos/blob/bb52b54cec939223c9342c83f7b58ea891005c52/api/user/service.proto#L253
	DeveloperActions_RETRY_CREATE_BANK_CUSTOMER DeveloperActions = 2
	// https://github.com/epiFi/protos/blob/bb52b54cec939223c9342c83f7b58ea891005c52/api/savings/service.proto#L19
	DeveloperActions_RETRY_CREATE_ACCOUNT DeveloperActions = 3
	// Calls KYC Service's InitiateEKYC RPC with force retry flag enabled
	DeveloperActions_RETRY_EKYC DeveloperActions = 4
	// Calls KYC Service's InitiateCKYC RPC
	DeveloperActions_RETRY_CKYC DeveloperActions = 5
	// creates new card for customer
	DeveloperActions_CREATE_CARD DeveloperActions = 6
	// creates a new reward offer
	DeveloperActions_CREATE_REWARD_OFFER DeveloperActions = 7
	// updates the status of reward offer to APPROVED/ACTIVE/IN_ACTIVE
	DeveloperActions_UPDATE_REWARD_OFFER_STATUS DeveloperActions = 8
	// creates a new lucky draw campaign
	DeveloperActions_CREATE_LUCKY_DRAW_CAMPAIGN DeveloperActions = 9
	// creates a new lucky draw
	DeveloperActions_CREATE_LUCKY_DRAW      DeveloperActions = 10
	DeveloperActions_PULL_APP_LOGS          DeveloperActions = 11
	DeveloperActions_ADD_USER_GROUP_MAPPING DeveloperActions = 12
	// Calls onboarding service and delete user.
	// It allow a user to begin onboarding again from scratch.
	DeveloperActions_DELETE_USER                DeveloperActions = 13
	DeveloperActions_RECOVER_AUTH_FACTOR_UPDATE DeveloperActions = 14
	// Reallow user to retry waitlist.
	DeveloperActions_REALLOW_USER_TO_TRY_WAITLIST DeveloperActions = 15
	// updates the display properties of existing reward offer.
	DeveloperActions_UPDATE_REWARD_OFFER_DISPLAY DeveloperActions = 16
	// Retrigger failed waitlist emails to users
	DeveloperActions_TRIGGER_FAILED_WAITLIST_EMAIL DeveloperActions = 17
	// retrigger indexing pis for actor
	DeveloperActions_TRIGGER_FAILED_PIS_INDEXING DeveloperActions = 18
	// creates an offer
	DeveloperActions_CREATE_OFFER DeveloperActions = 19
	// creates an offer listing.
	DeveloperActions_CREATE_OFFER_LISTING DeveloperActions = 20
	// updates an offer listing.
	DeveloperActions_UPDATE_OFFER_LISTING DeveloperActions = 21
	// deletes an offer listing.
	DeveloperActions_DELETE_OFFER_LISTING DeveloperActions = 22
	// creates an offer inventory
	DeveloperActions_CREATE_OFFER_INVENTORY DeveloperActions = 23
	// adds offers to inventory.
	DeveloperActions_ADD_OFFER_TO_INVENTORY DeveloperActions = 24
	// deletes an offer inventory.
	DeveloperActions_DELETE_OFFER_INVENTORY DeveloperActions = 25
	// force trigger recon for an account from a given date
	DeveloperActions_FORCE_TRIGGER_RECON DeveloperActions = 26
	// retrigger gmail data onboarded actor and waitlist actor sync
	DeveloperActions_TRIGGER_GMAIL_ACTOR_SYNC DeveloperActions = 27
	// remove access to read mail data for an emailId
	DeveloperActions_UNLINK_GMAIL_ACCOUNT DeveloperActions = 28
	// Delete waitlist user from user ID.
	DeveloperActions_DELETE_WAITLIST_USER_FROM_USER_ID DeveloperActions = 29
	// Mark waitlist users early access based on current day ranking.
	DeveloperActions_MARK_WAITLIST_USERS_EARLY_ACCESS DeveloperActions = 30
	// Send app access emails to early access waitlisted users.
	DeveloperActions_SEND_EMAIL_TO_EARLY_ACCESS DeveloperActions = 31
	// sync onboarding states and gets next action
	DeveloperActions_SYNC_ONBOARDING            DeveloperActions = 32
	DeveloperActions_FITTT_CREATE_NEW_RULE      DeveloperActions = 33
	DeveloperActions_FITTT_UPDATE_RULE          DeveloperActions = 34
	DeveloperActions_FITTT_GET_RULES_FOR_CLIENT DeveloperActions = 35
	// force update status of an onboarding state
	DeveloperActions_UPDATE_ONBOARDING_STAGE     DeveloperActions = 36
	DeveloperActions_SEND_COMMS_TO_WAITLIST_USER DeveloperActions = 37
	// updates the display properties of existing offer.
	DeveloperActions_UPDATE_OFFER_DISPLAY DeveloperActions = 38
	// Seed fi.nite codes to waitlist tables.
	DeveloperActions_SEED_FINITE_CODES DeveloperActions = 39
	// Action to initate match update for cricket rules
	DeveloperActions_FITTT_INITIATE_MATCH_UPDATE DeveloperActions = 40
	// Action to trigger card notifications
	DeveloperActions_INITIATE_CARD_NOTIFICATIONS DeveloperActions = 41
	// Action to mark liveness as passed for actorId
	DeveloperActions_MARK_LIVENESS_PASSED DeveloperActions = 43
	DeveloperActions_CREATE_PREFERENCE    DeveloperActions = 44
	// updates shipping address at vendor - gives option whether or not do to card creation
	DeveloperActions_UPDATE_SHIPPING_ADDRESS_AT_VENDOR DeveloperActions = 45
	// Crates a new shipping preference for a user for an item
	DeveloperActions_CREATE_SHIPPING_PREFERENCE DeveloperActions = 46
	// Retry payment order event in rule manager service(rms)
	DeveloperActions_RETRY_PAY_ORDER_RMS_EVENT DeveloperActions = 47
	// Send introductory welcome whatsapp message to users
	DeveloperActions_SEND_WELCOME_WHATSAPP DeveloperActions = 48
	// Send comm for reward campaigns.
	DeveloperActions_SEND_REWARDS_CAMPAIGN_COMM DeveloperActions = 49
	// Fetch unredacted user details using user id or mob num or email
	DeveloperActions_UNREDACTED_USER DeveloperActions = 50
	// Action to process the order
	// To be used to fetch the latest status of the order irrespective of its state
	DeveloperActions_FORCE_PROCESS_ORDER DeveloperActions = 51
	// Action to retry processing of a stuck reward.
	DeveloperActions_RETRY_REWARD_PROCESSING DeveloperActions = 52
	// action to get txn aggregate for fit txns from search
	DeveloperActions_GET_FIT_TXN_AGGREGATE_SEARCH DeveloperActions = 53
	// action to change user's revoke access
	// deprecated in favour of UPDATE_ACCOUNT_FREEZE_STATUS
	//
	// Deprecated: Marked as deprecated in api/cx/developer/actions/enums.proto.
	DeveloperActions_UPDATE_USER_ACCESS_REVOKE_STATE DeveloperActions = 54
	// action to update user's firstName and lastName in user profile
	DeveloperActions_UPDATE_USER_PROFILE_NAME DeveloperActions = 55
	// Action to generate account statement for a specific account id within a given time period
	DeveloperActions_GENERATE_ACCOUNT_STATEMENT DeveloperActions = 56
	// Action to fetch active onboarding alerts
	DeveloperActions_ONBOARDING_SNAPSHOT DeveloperActions = 57
	// Action to backfill contact details for a ticket in case webhook flow failed for that ticket for some reason
	DeveloperActions_BACKFILL_FRESHDESK_TICKET_CONTACTS DeveloperActions = 58
	// Action to mark an onboarding stage success after manual review
	DeveloperActions_PASS_ONBOARDING_STAGE DeveloperActions = 59
	// Action to create a reward offer group.
	DeveloperActions_CREATE_REWARD_OFFER_GROUP DeveloperActions = 60
	// Action to reset user kyc name dob retries
	DeveloperActions_RESET_KYC_NAME_DOB_RETRY DeveloperActions = 61
	// Action to retry processing of a stuck offer redemption.
	DeveloperActions_RETRY_OFFER_REDEMPTION DeveloperActions = 62
	// Action to resolve user in manual screening stage
	DeveloperActions_MANUAL_SCREENING_UPDATE DeveloperActions = 63
	// Action to unblock UN NAME check user
	DeveloperActions_UN_NAME_CHECK_UNBLOCK DeveloperActions = 64
	// Action to reset user debit card name retries
	DeveloperActions_RESET_DEBIT_CARD_NAME_RETRY DeveloperActions = 65
	// Action to stop schedules and cancel jobs
	DeveloperActions_FITTT_STOP_SCHEDULES_JOBS DeveloperActions = 66
	// Action to check vkyc status and refresh based on scheme code
	DeveloperActions_REFRESH_VKYC_STATUS DeveloperActions = 67
	// Action to process card pin set for users who have already set their card pin using Federal IVR
	DeveloperActions_UPDATE_CARD_PIN_SET DeveloperActions = 68
	// Raise AA consent given customer ID and actor ID
	DeveloperActions_RAISE_AA_CONSENT DeveloperActions = 69
	// Action to trigger enquiry for card creation
	DeveloperActions_FORCE_CARD_CREATION_ENQUIRY DeveloperActions = 70
	// Mark facematch status as passed
	DeveloperActions_MARK_FACEMATCH_PASSED DeveloperActions = 71
	// Action to trigger manual giveaway event for giving giveaway reward to user.
	DeveloperActions_TRIGGER_REWARDS_MANUAL_GIVEAWAY_EVENT DeveloperActions = 72
	// Action to delete user group mapping
	DeveloperActions_DELETE_USER_GROUP_MAPPING DeveloperActions = 73
	// Actor to trigger next action of wealth onboarding
	DeveloperActions_SYNC_WEALTH_ONBOARDING DeveloperActions = 74
	// Manual In-App-Referral unlock for testing purposes
	DeveloperActions_UNLOCK_IN_APP_REFERRAL DeveloperActions = 75
	// trigger vkyc agent/auditor callback
	DeveloperActions_TRIGGER_VKYC_CALLBACK DeveloperActions = 76
	// Action to mark savings account closed in our db.
	DeveloperActions_HANDLE_SAVINGS_ACCOUNT_CLOSURE DeveloperActions = 77
	// Add/Remove gmail insights merchants
	DeveloperActions_UPDATE_GMAIL_INSIGHTS_MERCHANTS DeveloperActions = 78
	// Add/Remove gmail insights merchant queries
	DeveloperActions_UPDATE_GMAIL_INSIGHTS_MERCHANT_QUERIES DeveloperActions = 79
	// Update FAQs (Category, Folder, Articles)
	DeveloperActions_UPDATE_INAPPHELP_FAQ DeveloperActions = 80
	// Non prod action for QA to update consent status in simulator
	DeveloperActions_AA_CONSENT_STATUS_UPDATE DeveloperActions = 81
	// Non prod action for QA to delink AA account from simulator
	DeveloperActions_AA_ACCOUNT_DELINK DeveloperActions = 82
	// Action to reactivate device of a user at vendor's end
	DeveloperActions_REACTIVATE_DEVICE DeveloperActions = 83
	// weightage is used for ordering rules on UI, this action will help change the ordering of rules on app using sherlock
	DeveloperActions_FITTT_UPDATE_RULES_WEIGHTAGE DeveloperActions = 84
	// Action to re-open a savings account. This will only mark the account as CREATED in our DB.
	// Does not make any calls to Federal.
	DeveloperActions_REOPEN_SAVINGS_ACCOUNT_IN_DB DeveloperActions = 85
	// Action to update status of wealth onboarding
	DeveloperActions_UPDATE_WEALTH_ONBOARDING_STATUS DeveloperActions = 86
	// create fittt home card
	DeveloperActions_FITTT_CREATE_HOME_CARD DeveloperActions = 87
	// update fittt home card
	DeveloperActions_FITTT_UPDATE_HOME_CARD DeveloperActions = 88
	// Action to update pan name check verdict
	DeveloperActions_UPDATE_PAN_NAME_REVIEW DeveloperActions = 89
	// Action to add new to media playlist
	DeveloperActions_ADD_MEDIA_PLAYLIST DeveloperActions = 90
	// Action to update existing media playlist
	DeveloperActions_UPDATE_MEDIA_PLAYLIST DeveloperActions = 91
	// Action to add new media content story
	DeveloperActions_ADD_MEDIA_CONTENT_STORY DeveloperActions = 92
	// Action to update existing media content story
	DeveloperActions_UPDATE_MEDIA_CONTENT_STORY DeveloperActions = 93
	// Action to create new schedule in fittt's scheduler service
	DeveloperActions_FITTT_CREATE_SCHEDULE DeveloperActions = 94
	// Action to add UIContext to Media Playlist mapping
	DeveloperActions_ADD_UI_CONTEXT_TO_MEDIA_PLAYLIST_MAPPING DeveloperActions = 95
	// Action to delete UIContext to Media Playlist mapping
	DeveloperActions_DELETE_UI_CONTEXT_TO_MEDIA_PLAYLIST_MAPPING DeveloperActions = 96
	// Action to add Media Playlist to Media Content mapping
	DeveloperActions_ADD_MEDIA_PLAYLIST_TO_MEDIA_CONTENT_MAPPING DeveloperActions = 97
	// Action to delete Media Playlist to Media Content mapping
	DeveloperActions_DELETE_MEDIA_PLAYLIST_TO_MEDIA_CONTENT_MAPPING DeveloperActions = 98
	// Action to add meta data like reference number for credit mis report for the corresponding file in the db.
	DeveloperActions_ADD_CREDIT_MIS_FILE_META_DATA DeveloperActions = 99
	// Action to bulk update subscriptions state for a rule
	DeveloperActions_FITTT_BULK_UPDATE_SUBSCRIPTIONS_STATE DeveloperActions = 100
	// Action to add new Mutual Fund to mutual fund catalog
	DeveloperActions_CREATE_MUTUAL_FUND DeveloperActions = 103
	// Action to update Mutual Fund in mutual fund catalog
	DeveloperActions_UPDATE_MUTUAL_FUND DeveloperActions = 102
	// Action to upload a mutual fund reverse feed file.
	DeveloperActions_MUTUAL_FUND_UPLOAD_REVERSE_FEED_FILE DeveloperActions = 101
	// Replay AA Account Events
	DeveloperActions_REPLAY_AA_ACCOUNT_EVENTS DeveloperActions = 104
	// action to create an exchanger offer
	DeveloperActions_CREATE_EXCHANGER_OFFER DeveloperActions = 105
	// action to create a listing for an exchanger offer
	DeveloperActions_CREATE_EXCHANGER_OFFER_LISTING DeveloperActions = 106
	// action to update display of an exchanger offer
	DeveloperActions_UPDATE_EXCHANGER_OFFER_DISPLAY DeveloperActions = 107
	// action to update an exchanger offer listing
	DeveloperActions_UPDATE_EXCHANGER_OFFER_LISTING DeveloperActions = 108
	// action to delete an exchanger offer listing
	DeveloperActions_DELETE_EXCHANGER_OFFER_LISTING DeveloperActions = 109
	// Replay AA Transaction Events
	DeveloperActions_REPLAY_AA_TXN_EVENTS DeveloperActions = 110
	// archives requested rules and subscriptions
	DeveloperActions_FITTT_ARCHIVE_RULES_AND_SUBSCRIPTIONS DeveloperActions = 111
	// action to force process a deposit request
	DeveloperActions_FORCE_PROCESS_DEPOSIT_REQUEST DeveloperActions = 112
	// search parse query base
	DeveloperActions_SEARCH_PARSE_QUERY_BASE DeveloperActions = 113
	// mark wealthonboarding livness passed
	DeveloperActions_MARK_WEALTH_ONBOARDING_LIVENESS_PASSED  DeveloperActions = 114
	DeveloperActions_MUTUAL_FUND_DEACTIVATE_ENTITY_FROM_FILE DeveloperActions = 115
	// action to create new fittt collection
	DeveloperActions_FITTT_CREATE_COLLECTION DeveloperActions = 116
	// action to update existing fittt collection
	DeveloperActions_FITTT_UPDATE_COLLECTION DeveloperActions = 117
	// mark wealthonboarding redaction review passed
	DeveloperActions_MARK_WEALTH_ONBOARDING_REDACTION_PASSED DeveloperActions = 118
	// updates order status of a list of mutual funds
	DeveloperActions_UPDATE_MUTUAL_FUND_ORDER_STATUS DeveloperActions = 119
	// trigger Recurring Payment Execution
	DeveloperActions_TRIGGER_RECURRING_PAYMENT_EXECUTION DeveloperActions = 120
	// Action to upload a csv for custom user campaign usecase
	DeveloperActions_UPLOAD_MARKETING_CAMPAIGN_USERS_LIST DeveloperActions = 121
	// mark wealthonboarding expiry review passed
	DeveloperActions_MARK_WEALTH_ONBOARDING_EXPIRY_PASSED DeveloperActions = 122
	// Action to download credit mis report
	DeveloperActions_MUTUAL_FUND_DOWNLOAD_CREDIT_MIS_REPORT DeveloperActions = 123
	// Action to add In App targeted comms elements like banner, bottom sheet etc.
	DeveloperActions_ADD_IN_APP_TARGETED_COMMS_ELEMENT    DeveloperActions = 124
	DeveloperActions_UPDATE_IN_APP_TARGETED_COMMS_ELEMENT DeveloperActions = 125
	DeveloperActions_ADD_IN_APP_TARGETED_COMMS_MAPPING    DeveloperActions = 126
	DeveloperActions_DELETE_IN_APP_TARGETED_COMMS_MAPPING DeveloperActions = 127
	DeveloperActions_CREATE_EXCHANGER_OFFER_GROUP         DeveloperActions = 128
	// Action to generate and download mutual fund ops file
	DeveloperActions_MF_DOWNLOAD_OPS_FILE DeveloperActions = 129
	// Action to generate and download mutual fund ops file
	DeveloperActions_MF_RE_TRIGGER_PRE_REQUISITES DeveloperActions = 130
	// Action to un-suspend/activate card
	DeveloperActions_MANUAL_CARD_UNSUSPEND DeveloperActions = 131
	// Action to update insight framework
	DeveloperActions_UPDATE_INSIGHT_FRAMEWORK DeveloperActions = 132
	// Action to update insight segment
	DeveloperActions_UPDATE_INSIGHT_SEGMENT DeveloperActions = 133
	// Action to update insight content template
	DeveloperActions_UPDATE_INSIGHT_CONTENT_TEMPLATE DeveloperActions = 134
	// action to bulk create/update mutual fund entries
	// action will upload a file to s3 bucket
	DeveloperActions_MF_UPLOAD_CATALOG_UPDATE DeveloperActions = 135
	// action to update offer display ranks
	DeveloperActions_UPDATE_OFFER_DISPLAY_RANK DeveloperActions = 136
	// Action to mock uploading of Credit MIS Report to vendor in non-prod envs
	DeveloperActions_MF_UPLOAD_CREDIT_MIS_NON_PROD    DeveloperActions = 137
	DeveloperActions_CREATE_EXCHANGER_OFFER_INVENTORY DeveloperActions = 138
	// action to update reward display rank
	DeveloperActions_UPDATE_REWARD_OFFER_DISPLAY_RANK DeveloperActions = 139
	// action to add user to VKYC priority list
	DeveloperActions_ADD_USER_TO_VKYC_PRIORITY DeveloperActions = 140
	// action to update user DOB
	DeveloperActions_UPDATE_USER_DOB DeveloperActions = 141
	// action to update investment transaction status
	DeveloperActions_UPDATE_P2P_INVESTMENT_TRANSACTION_STATUS DeveloperActions = 142
	// action to update user's photo. Note :- This photo is used to do facematch while AFU
	DeveloperActions_UPDATE_USER_PHOTO DeveloperActions = 143
	// action to update investor total investment count
	DeveloperActions_UPDATE_P2P_INVESTOR_TOTAL_INVESTMENT_COUNT DeveloperActions = 144
	// Action to create a mutual fund collection
	DeveloperActions_MF_CREATE_COLLECTION DeveloperActions = 145
	// Action to update details of a mutual fund collection
	DeveloperActions_MF_UPDATE_COLLECTION DeveloperActions = 146
	// Action to add a mutual fund to a collection
	DeveloperActions_MF_ADD_FUND_TO_COLLECTION DeveloperActions = 147
	// Action to update mutual funds info in a collection
	DeveloperActions_MF_UPDATE_FUND_IN_COLLECTION DeveloperActions = 148
	// Action to remove mutual funds from a collection
	DeveloperActions_MF_REMOVE_FUNDS_FROM_COLLECTION DeveloperActions = 149
	// action to process a reverse feed file for mutual fund.
	DeveloperActions_MF_PROCESS_REVERSE_FEED_FILE DeveloperActions = 150
	// Action to update liquiloans approval status for P2P
	DeveloperActions_UPDATE_P2P_VENDOR_RESPONSE_APPROVAL_STATUS DeveloperActions = 151
	// action to request for a physical card dispatch for user who currently has a digital card
	DeveloperActions_PHYSICAL_CARD_REQUEST DeveloperActions = 152
	// Increment exchanger offer inventory
	DeveloperActions_INCREMENT_EXCHANGER_OFFER_INVENTORY DeveloperActions = 153
	// action to create a new progressive referral rewards season
	DeveloperActions_CREATE_REFERRALS_SEASON DeveloperActions = 154
	// action to update existing progressive referral rewards season with new values
	DeveloperActions_UPDATE_REFERRALS_SEASON DeveloperActions = 155
	// action to delete segment from rdb
	DeveloperActions_DELETE_SEGMENT DeveloperActions = 156
	// action to mark step stale for wealthOnboarding
	DeveloperActions_MARK_STEP_STALE_WEALTH_ONBOARDING DeveloperActions = 157
	// action to blocking the existing card and request a new card with address type sent in the request
	DeveloperActions_REQUEST_NEW_CARD DeveloperActions = 158
	// trigger VPA Creation
	DeveloperActions_TRIGGER_VPA_CREATION DeveloperActions = 159
	// add manual call routing mappings
	DeveloperActions_ADD_MANUAL_CALL_ROUTING_MAPPINGS DeveloperActions = 160
	// action to trigger segment export
	DeveloperActions_TRIGGER_SEGMENT_EXPORT DeveloperActions = 161
	// action to perform different reconciliation for mutual funds
	DeveloperActions_MF_RECONCILIATION DeveloperActions = 162
	// action to create a ticket category tags to title, description transformation for showing tickets in the app
	DeveloperActions_CREATE_TICKET_DETAILS_TRANSFORMATION DeveloperActions = 163
	// action to update a ticket category tags to title, description transformation for showing tickets in the app
	DeveloperActions_UPDATE_TICKET_DETAILS_TRANSFORMATION DeveloperActions = 164
	// action to update a ticket category tags to title, description transformation for showing tickets in the app
	DeveloperActions_DELETE_TICKET_DETAILS_TRANSFORMATION DeveloperActions = 165
	// action to convert deeplink to base64 encoding
	DeveloperActions_DEEPLINK_BASE64_ENCODER DeveloperActions = 166
	// action for Op agents to manually review and approve/reject liveness/facematch and other checks for preapprovedloan
	DeveloperActions_PRE_APPROVED_LOAN_MANUAL_REVIEW DeveloperActions = 167
	// action to create a loon offer for a user.
	DeveloperActions_PRE_APPROVED_LOAN_CREATE_OFFER DeveloperActions = 168
	// action to be used to approve a loan request for a user. Will be used only in non-prod envs
	DeveloperActions_PRE_APPROVED_LOAN_UPDATE_LOAN_STATUS DeveloperActions = 169
	// CATEGORISE_SCREENER_DOMAINS dev action is used to allow/block work/student email domains in screener
	DeveloperActions_CATEGORISE_SCREENER_DOMAINS DeveloperActions = 170
	// action to create a salaryprogram referrals season
	DeveloperActions_CREATE_SALARY_PROGRAM_REFERRALS_SEASON DeveloperActions = 171
	// action to start a new user action
	DeveloperActions_START_USER_ACTION DeveloperActions = 172
	// action to create user nudge
	DeveloperActions_CREATE_NUDGE DeveloperActions = 173
	// Edit nudge for user
	DeveloperActions_EDIT_NUDGE DeveloperActions = 174
	// used to perform risk related savings account activities like freezing, credit freezing, unfreezing etc.
	DeveloperActions_SAVINGS_RISK_BANK_ACTIONS DeveloperActions = 175
	// action to update father name
	DeveloperActions_UPDATE_USER_FATHER_NAME DeveloperActions = 176
	// used to update savings account freeze status in account simulator db
	DeveloperActions_UPDATE_ACCOUNT_FREEZE_STATUS_IN_SIMULATOR DeveloperActions = 177
	// action to update lrs limit for users
	DeveloperActions_INTERNATIONAL_FUND_TRANSFER_UPLOAD_LRS_CHECK_FILE DeveloperActions = 178
	// action to acknowledge approved international fund transfer transactions and mark them as completed
	DeveloperActions_INTERNATIONAL_FUND_TRANSFER_ACKNOWLEDGE_SWIFT_TRANSFER DeveloperActions = 179
	// action to acknowledge approved inward fund transfer transactions and mark them as completed
	DeveloperActions_INTERNATIONAL_FUND_TRANSFER_ACKNOWLEDGE_INWARD_FUND_TRANSFER DeveloperActions = 180
	// action to fetch all users for whom LRS is to be checked
	DeveloperActions_PAY_DOWNLOAD_LRS_CHECK_FILE DeveloperActions = 181
	// action to process inward remittance flow from us stock broker and foreign remittance partner
	DeveloperActions_FOREIGN_REMITTANCE_PROCESS_INWARD_REMITTANCE DeveloperActions = 182
	// used to update savings account freeze status (full/credit freeze/unfreeze)in savings and revoke
	// app access if applicable
	DeveloperActions_UPDATE_ACCOUNT_FREEZE_STATUS DeveloperActions = 183
	// action to create a new discount
	DeveloperActions_CREATE_DISCOUNT DeveloperActions = 184
	// action to delete discount
	DeveloperActions_DELETE_DISCOUNT DeveloperActions = 185
	// action to upload user specific info to be displayed to agents
	DeveloperActions_UPLOAD_USER_ISSUE_INFO_FOR_AGENTS DeveloperActions = 186
	// action to update users employment data (salary and occupation)
	DeveloperActions_UPDATE_USER_EMPLOYMENT DeveloperActions = 187
	// action to create or update segment mapping
	DeveloperActions_CREATE_OR_UPDATE_SEGMENT_MAPPING DeveloperActions = 188
	// action to add new deposit interest rates
	DeveloperActions_DEPOSIT_ADD_INTEREST_RATE DeveloperActions = 189
	// action to update the deposit interest rates of existing entries
	DeveloperActions_DEPOSIT_UPDATE_INTEREST_RATE DeveloperActions = 190
	// action to delete the deposit interest rate of existing entry
	DeveloperActions_DEPOSIT_DELETE_INTEREST_RATE DeveloperActions = 191
	// action to get docket urls for given pan numbers
	//
	// Deprecated: Marked as deprecated in api/cx/developer/actions/enums.proto.
	DeveloperActions_GET_DOCKET_URL_WEALTH_ONBOARDING DeveloperActions = 192
	// update exchanger offer status
	DeveloperActions_UPDATE_EXCHANGER_OFFER_STATUS DeveloperActions = 193
	// action to change the status to Hunter api response
	DeveloperActions_PROFILE_EVALUATOR_ACTIONS DeveloperActions = 194
	// action to update verification status for income and occupation discrepancy
	DeveloperActions_INCOME_OCCUPATION_DISCREPANCY_VERIFICATION DeveloperActions = 195
	// action to create a segmented component for referrals
	DeveloperActions_CREATE_REFERRALS_SEGMENTED_COMPONENT DeveloperActions = 196
	// action to update a segmented component for referrals
	DeveloperActions_UPDATE_REFERRALS_SEGMENTED_COMPONENT DeveloperActions = 197
	// action to delete a segmented component for referrals
	DeveloperActions_DELETE_REFERRALS_SEGMENTED_COMPONENT DeveloperActions = 198
	// action to upload list of risk cases to be reviewed
	// this dev action will take case of all the transformations and steps required to push the cases in the queue for review by ops team
	DeveloperActions_UPLOAD_RISK_CASES DeveloperActions = 199
	// action to create a segment
	DeveloperActions_CREATE_SEGMENT DeveloperActions = 200
	// dev action to be used for taking action on a txn manual review case
	DeveloperActions_RISK_TXN_CASE_MANUAL_REVIEW DeveloperActions = 201
	// dev action for removing red list entry
	DeveloperActions_REMOVE_RED_LIST_ENTRY DeveloperActions = 202
	// dev action for adding new red list entry
	DeveloperActions_ADD_RED_LIST_ENTRY DeveloperActions = 203
	// DEV ACTION TO FETCH INVESTOR JUMP DASHBOARD
	DeveloperActions_P2P_GET_INVESTOR_DASHBOARD DeveloperActions = 204
	// DEV ACTION TO FETCH JUMP INVESTMENT SUMMARY FOR INVESTOR
	DeveloperActions_P2P_GET_INVESTMENT_SUMMARY DeveloperActions = 205
	// DEV ACTION TO GET P2P RECON FILE FOR OPS
	DeveloperActions_P2P_DOWNLOAD_RECON_FILE DeveloperActions = 206
	// dev action to get signed url for given s3 path for wealth onboarding bucket
	DeveloperActions_GET_SIGNED_URL_WEALTH DeveloperActions = 207
	// dev action for updating credit card request status
	DeveloperActions_CREDIT_CARD_UPDATE_CARD_REQUEST_STATUS DeveloperActions = 208
	// to refresh stock details like market cap, revenue, etc.
	DeveloperActions_USSTOCKS_REFRESH_STOCK_DETAILS DeveloperActions = 209
	// to update status of a nudge
	DeveloperActions_UPDATE_NUDGE_STATUS DeveloperActions = 210
	// dev action to upload aml reports
	DeveloperActions_UPLOAD_AML_REPORT DeveloperActions = 211
	// dev action to deactivate device
	DeveloperActions_DEACTIVATE_DEVICE DeveloperActions = 212
	// action for Federal agents to manually review and approve/reject liveness for preapprovedloan FOR CKYC users
	DeveloperActions_FEDERAL_LOAN_LIVENESS_REVIEW DeveloperActions = 213
	// dev action to create reward offer in bulk
	DeveloperActions_CREATE_REWARD_OFFER_IN_BULK DeveloperActions = 214
	// dev action to publish shark tank event, this event will trigger relevant rule execution
	DeveloperActions_FITTT_PUBLISH_SHARK_TANK_EVENT DeveloperActions = 215
	// dev action to create a new forex rate entry
	DeveloperActions_CREATE_FOREX_RATE DeveloperActions = 216
	// dev action to update a forex rate entry
	DeveloperActions_UPDATE_FOREX_RATE DeveloperActions = 217
	// dev action to give another chance to user to re attempt vkyc
	DeveloperActions_REPRIEVE_VKYC DeveloperActions = 218
	// dev action to get gross summary report for given dates (us stocks)
	DeveloperActions_USSTOCKS_GROSS_SUMMARY DeveloperActions = 219
	// dev action to update file generation status
	DeveloperActions_AML_UPDATE_FILE_GEN_STATUS DeveloperActions = 220
	// dev action to download file from s3 bucket
	DeveloperActions_MF_DOWNLOAD_FILE_FROM_BUCKET DeveloperActions = 221
	// dev action to perform action for risk transaction review
	DeveloperActions_PERFORM_RISK_REVIEW_ACTION DeveloperActions = 222
	// dev action to generate and get forex rate report for the given dates
	DeveloperActions_FOREX_RATE_REPORT DeveloperActions = 223
	// dev action to get the current live forex rate
	DeveloperActions_CURRENT_FOREX_RATE DeveloperActions = 224
	// dev action to upload corrected wealth kyc images for an actor
	DeveloperActions_UPLOAD_WEALTH_DOCUMENT DeveloperActions = 225
	// dev action to update number of days remaining to update
	DeveloperActions_UPDATE_P2P_VENDOR_RESPONSE_MATURITY_TRANSACTION_DAYS_TO_EXPIRE DeveloperActions = 226
	// dev action to fetch forex rates with filters
	DeveloperActions_GET_FOREX_RATES DeveloperActions = 227
	// dev action to be used to approve a applicant/mandate status for pre-approved loan created on Liquiloans side
	DeveloperActions_PRE_APPROVED_LOAN_LL_UPDATE_ENTITY_STATUS DeveloperActions = 228
	// dev action to create temporal schedule which triggers a workflow periodically
	// NOTE : Currently we have added support for interval based scheduling action
	DeveloperActions_CREATE_TEMPORAL_SCHEDULE DeveloperActions = 229
	// Dev action to add new promo banner
	DeveloperActions_ADD_HOME_PROMO_BANNER DeveloperActions = 230
	// dev action to get account statement for given dates for us stocks pool accounts (inward and outward)
	DeveloperActions_USSTOCKS_ACCOUNT_STATEMENT DeveloperActions = 231
	// dev action to retry mutual fund orders
	DeveloperActions_RETRY_MUTUAL_FUND_ORDER DeveloperActions = 232
	// dev action to create allowed annotation for case management
	DeveloperActions_RISK_CREATE_ALLOWED_ANNOTATION DeveloperActions = 233
	// dev action to add manual override states under risk bank action
	DeveloperActions_RISK_BANK_ACTION_MANUAL_OVERRIDE DeveloperActions = 234
	// dev action to fetch s3 from usstocks bucket
	DeveloperActions_USSTOCKS_DOWNLOAD_FILE_FROM_BUCKET DeveloperActions = 235
	// dev action to create nudges in bulk
	DeveloperActions_CREATE_NUDGES_IN_BULK DeveloperActions = 236
	// dev action for vendor account penny drop verification
	DeveloperActions_VENDOR_ACCOUNT_PENNY_DROP DeveloperActions = 237
	// dev action to update pending user data status
	DeveloperActions_UPDATE_WEALTH_USER_INPUT_PENDING_DATA DeveloperActions = 238
	// action to create offers in bulk
	DeveloperActions_CREATE_OFFERS_IN_BULK              DeveloperActions = 239
	DeveloperActions_RESET_CELESTIAL_WORKFLOW_EXECUTION DeveloperActions = 240
	// action to create notification config infos
	DeveloperActions_CREATE_REFERRAL_NOTIFICATION_CONFIG DeveloperActions = 241
	// action to update notification config status
	DeveloperActions_UPDATE_REFERRAL_NOTIFICATION_CONFIG_STATUS DeveloperActions = 242
	// action to update notification config content
	DeveloperActions_UPDATE_REFERRAL_NOTIFICATION_CONFIG_CONTENT DeveloperActions = 243
	// action to delete notification config infos
	DeveloperActions_DELETE_REFERRAL_NOTIFICATION_CONFIG DeveloperActions = 244
	// action for uploading lea report csvs
	DeveloperActions_UPLOAD_LEA_COMPLAINTS DeveloperActions = 245
	// Create pop up for home
	DeveloperActions_ADD_HOME_POP_UP_BANNER DeveloperActions = 246
	// action to list all the deposit accounts for an actor from a vendor
	DeveloperActions_DEPOSIT_LIST_ACCOUNTS_VENDOR DeveloperActions = 247
	// dev action to mark loan request as cancelled will be used in prod
	DeveloperActions_MARK_LOAN_REQUEST_CANCEL DeveloperActions = 248
	// Generate aggregated tax report over a time period
	DeveloperActions_IFT_GENERATE_AGGREGATED_TAX_REPORT DeveloperActions = 249
	// create or update dynamic ui element variant
	DeveloperActions_CREATE_OR_UPDATE_DYNAMIC_UI_ELEMENT_VARIANT DeveloperActions = 250
	// updates the dynamic ui element evaluator config
	DeveloperActions_UPDATE_DYNAMIC_UI_ELEMENT_EVALUATOR_CONFIG DeveloperActions = 251
	// dev action to create a product for cms
	DeveloperActions_CREATE_CMS_PRODUCT DeveloperActions = 252
	// dev action to create a sku for cms
	DeveloperActions_CREATE_CMS_SKU DeveloperActions = 253
	// dev action to create a product for cms
	DeveloperActions_CREATE_CMS_COUPON_IN_BULK DeveloperActions = 254
	// Create home layout
	DeveloperActions_CREATE_HOME_LAYOUT DeveloperActions = 255
	// Create layout with mapping on segments
	DeveloperActions_CREATE_HOME_LAYOUT_SEGMENT_MAPPING DeveloperActions = 256
	// Delete layout and all associated segment mappings
	DeveloperActions_DELETE_HOME_LAYOUT DeveloperActions = 257
	// Delete a given segment mapping
	DeveloperActions_DELETE_HOME_LAYOUT_SEGMENT_MAPPING DeveloperActions = 258
	DeveloperActions_MARK_USERS_BY_ACQUISITION_INFO     DeveloperActions = 259
	// Get home layout
	DeveloperActions_GET_HOME_LAYOUT DeveloperActions = 260
	// Get home layout segment mapping
	DeveloperActions_GET_HOME_LAYOUT_SEGMENT_MAPPING DeveloperActions = 261
	// Add a new survey in feedback engine
	DeveloperActions_ADD_INAPPHELP_FEEDBACK_ENGINE_SURVEY DeveloperActions = 262
	// Update an existing survey in feedback engine
	DeveloperActions_UPDATE_INAPPHELP_FEEDBACK_ENGINE_SURVEY DeveloperActions = 263
	// Add a new question in feedback engine
	DeveloperActions_ADD_INAPPHELP_FEEDBACK_ENGINE_QUESTION DeveloperActions = 264
	// Update an existing question in feedback engine
	DeveloperActions_UPDATE_INAPPHELP_FEEDBACK_ENGINE_QUESTION DeveloperActions = 265
	// dev action to mark card delivery tracking state to received
	DeveloperActions_MARK_CARD_DELIVERY_TRACKING_STATE_RECEIVED DeveloperActions = 266
	// dev action to mark any loan step success of fail
	DeveloperActions_LOAN_UPDATE_LOAN_STEP_STATUS DeveloperActions = 267
	// Action to create a usstocks  collection
	DeveloperActions_USSTOCKS_CREATE_COLLECTION DeveloperActions = 268
	// Action to update details of a usstocks collection
	DeveloperActions_USSTOCKS_UPDATE_COLLECTION DeveloperActions = 269
	// Action to add a usstocks to a collection
	DeveloperActions_USSTOCKS_ADD_STOCK_TO_COLLECTION DeveloperActions = 270
	// Action to update usstocks info in a collection
	DeveloperActions_USSTOCKS_UPDATE_STOCK_IN_COLLECTION DeveloperActions = 271
	// Action to remove usstocks from a collection
	DeveloperActions_USSTOCKS_REMOVE_STOCK_FROM_COLLECTION DeveloperActions = 272
	// action to update a segment
	DeveloperActions_UPDATE_SEGMENT DeveloperActions = 273
	// dev action to add and index employers to employer database.
	// Employer db is used by salaryprogram and onboarding to store the company which a user works in
	DeveloperActions_ADD_EMPLOYERS DeveloperActions = 274
	// Reject transactions in bulk outward SWIFT transfer file
	DeveloperActions_IFT_REJECT_TRANSACTIONS_IN_OUTWARD_SWIFT_FILE DeveloperActions = 275
	// action to whitelist an ephemeral email id in QA
	DeveloperActions_WHITELIST_EMAIL_ID DeveloperActions = 276
	// dev action to create a new kyc agent.
	// Kyc Agent creation involves creating entries in agent, casbin, actor. It also generates initial login credentials
	// and mails them to mentioned email id.
	DeveloperActions_CREATE_KYC_AGENT DeveloperActions = 277
	// data extraction tool for various data extraction requests
	DeveloperActions_DATA_EXTRACTION DeveloperActions = 278
	// dev action to create issue config
	// new record is created in issue_configs table, containing a payload corresponding to issue_category_id and config_type
	DeveloperActions_CREATE_ISSUE_CONFIG DeveloperActions = 279
	// action to update reward Offer details
	DeveloperActions_UPDATE_REWARD_OFFER DeveloperActions = 280
	// action to generate chatbot access token for debugging purpose
	DeveloperActions_GENERATE_CHATBOT_ACCESS_TOKEN DeveloperActions = 281
	// action to register banking details for a jump investor
	DeveloperActions_P2P_REGISTER_BANKING_DETAILS DeveloperActions = 282
	// To upload narrations for lea complaints.
	DeveloperActions_UPLOAD_LEA_COMPLAINT_NARRATIONS DeveloperActions = 283
	// To update the employer details.
	DeveloperActions_UPDATE_EMPLOYER_DETAILS DeveloperActions = 284
	// Bulk setup referral segmented components
	DeveloperActions_BULK_SETUP_REFERRAL_SEGMENTED_COMPONENTS DeveloperActions = 285
	// To upload lea complaint source details
	// i.e law enforcement agency details from which the complaint was received.
	DeveloperActions_UPLOAD_LEA_COMPLAINT_SOURCE_DETAILS DeveloperActions = 286
	// Control user communications
	DeveloperActions_USER_COMMS_CONTROL DeveloperActions = 287
	// To Update Card Req stage status for credit card workflows
	DeveloperActions_CREDIT_CARD_UPDATE_CARD_REQUEST_STAGE_STATUS DeveloperActions = 288
	// Dev action to simulate outward fund transfer for testing in non-prod
	// NOTE: this dev action will not be present in Prod env
	DeveloperActions_SIMULATE_USSTOCKS_OUTWARD_FUND_TRANSFER_NON_PROD DeveloperActions = 289
	// To create mapping of list of FAQs corresponding to a context value
	DeveloperActions_CREATE_FAQ_CONTEXT_MAPPING DeveloperActions = 290
	// To update any field in p2p investment transaction
	DeveloperActions_P2P_UPDATE_INVESTMENT_TRANSACTION DeveloperActions = 291
	// Fetch current account status from bank api
	DeveloperActions_FETCH_ACCOUNT_STATUS DeveloperActions = 292
	// dev action to create home simulator layout
	DeveloperActions_CREATE_HOME_SIMULATOR_LAYOUT DeveloperActions = 293
	// Get wealth onboarding details by PAN for CX agents
	DeveloperActions_GET_WEALTH_ONBOARDING_DETAILS_BY_PAN DeveloperActions = 294
	// dev action to create mapping of ticket details required to be populated for specific Watson use-case
	DeveloperActions_CREATE_WATSON_TICKET_DETAILS DeveloperActions = 295
	// dev action to update the Watson ticket details mapping
	DeveloperActions_UPDATE_WATSON_TICKET_DETAILS DeveloperActions = 296
	// dev action to link bank account with firm account at us stocks broker
	// associated bank account is used for inward fund transfer
	DeveloperActions_USSTOCKS_CREATE_BANK_RELATIONSHIP_WITH_BROKER DeveloperActions = 297
	// dev action for anything releated to ABFL changes in simulator vg of PAL
	DeveloperActions_LOANS_ABFL_ACTIONS DeveloperActions = 298
	// dev action to trigger kyc name dob validation at Federal
	DeveloperActions_TRIGGER_KYC_NAME_DOB_VALIDATION DeveloperActions = 299
	// dev action to get onboarding troubleshooting details
	DeveloperActions_ONBOARDING_TROUBLESHOOTING_DETAILS DeveloperActions = 300
	// dev action to create config mapped against specific event in Sherlock DB
	DeveloperActions_CREATE_EVENT_CONFIG DeveloperActions = 301
	// dev action to update config mapped against specific event in Sherlock DB
	DeveloperActions_UPDATE_EVENT_CONFIG DeveloperActions = 302
	// dev action to fail bank customer to retry
	DeveloperActions_FAIL_BANK_CUSTOMER DeveloperActions = 303
	// dev action for P2P CASH LEDGER
	DeveloperActions_P2P_GET_CASH_LEDGER DeveloperActions = 304
	// dev action to show all folio from actorId
	DeveloperActions_GET_MF_FOLIO_BALANCE DeveloperActions = 305
	// Initiate refund for one or more international outward remittances backed by concrete reasons
	DeveloperActions_IFT_INITIATE_REFUND DeveloperActions = 306
	// dev action for raising manual salary verification requests in bulk from csv as input
	DeveloperActions_RAISE_MANUAL_SALARY_VERIFICATION_REQUESTS_IN_BULK DeveloperActions = 307
	// dev action to simulate reward generation flow and publish rewards if not generated for some reason from event id and offer id
	DeveloperActions_SIMULATE_REWARD_GENERATION DeveloperActions = 308
	// dev action to update salaryprogram referrals season
	DeveloperActions_UPDATE_SALARY_PROGRAM_REFERRALS_SEASON DeveloperActions = 309
	// dev action to process itc points hand back file and update corresponding redeemed offer status
	DeveloperActions_PROCESS_ITC_POINTS_HANDBACK_FILE DeveloperActions = 310
	// Dev action to upload fraud/false disputes shared by federal.
	DeveloperActions_RISK_UPLOAD_DISPUTES DeveloperActions = 311
	// dev action for fetching, updating and creating segment metadata
	DeveloperActions_SEGMENT_METADATA_ACTION DeveloperActions = 312
	// dev action for approving the segments metadata
	DeveloperActions_SET_SEGMENT_METADATA_APPROVAL_STATUS DeveloperActions = 313
	// dev action for overriding payment health engine status
	DeveloperActions_OVERRIDE_PAYMENT_HEALTH_ENGINE_STATUS DeveloperActions = 314
	// dev action for overriding CSIS health engine status
	DeveloperActions_OVERRIDE_CSIS_HEALTH_ENGINE_STATUS DeveloperActions = 315
	// dev action to manual override sof limits (to be used by ops post sof review of users)
	DeveloperActions_SOF_LIMIT_MANUAL_OVERRIDE DeveloperActions = 316
	// Dev action to pass screener attempt for an actor
	DeveloperActions_PASS_RISK_SCREENER_ATTEMPT DeveloperActions = 317
	// Dev action to add a new entry in pin code master table
	DeveloperActions_ADD_PIN_CODE_ENTRY DeveloperActions = 318
	// Dev action to extract PAN detected manually in KRA-verified KYC docket of a user
	DeveloperActions_EXTRACT_PAN_FROM_KRA_DOCKET DeveloperActions = 319
	// Dev action to reset us stocks onboarding data
	DeveloperActions_USSTOCKS_RESET_ONBOARDING_DATA DeveloperActions = 320
	// dev action to add or remove multiple members from whitelist
	DeveloperActions_RISK_MANAGE_WHITELIST DeveloperActions = 321
	// Dev action to manually add investor address in uss
	DeveloperActions_USSTOCKS_ADD_INVESTOR_ADDRESS DeveloperActions = 322
	// dev action to create activity metadata record into DB
	DeveloperActions_CREATE_ACTIVITY_METADATA DeveloperActions = 323
	// Dev action to manually trigger categorisation for a set of transactions
	DeveloperActions_TRIGGER_TXN_CATEGORISATION DeveloperActions = 324
	// Dev action to get a us stocks account activities in csv format
	DeveloperActions_GET_US_STOCK_ACCOUNT_ACTIVITIES_CSV DeveloperActions = 325
	// Dev Action to manually trigger renewal fee reversal for unsecured credit card
	DeveloperActions_TRIGGER_UNSECURED_CC_RENEWAL_FEE_REVERSAL DeveloperActions = 326
	// For remitting transactions which were excluded in
	// the original batch inward remittance process
	// because of the accounts being credit-frozen, etc.
	DeveloperActions_USSTOCKS_GENERATE_ADHOC_INWARD_REMITTANCE_FILES DeveloperActions = 327
	// dev-action to respond any user's query by leveraging user's context and epifi knowledge-base
	// this dev-action will be used by internal Fi-employees for testing purpose only
	DeveloperActions_GENERATE_MODEL_RESPONSE_FOR_USERS_CX_QUERY DeveloperActions = 328
	// For excluding any actors txns from inward file and regenerate
	DeveloperActions_USSTOCKS_REGENERATE_INWARD_FILE DeveloperActions = 329
	// Dev action to manually map a child txn id to parent txn id once
	// refund is completed for the txn
	DeveloperActions_MAP_DEBIT_CARD_FOREX_TXN DeveloperActions = 330
	// dev action to create journey record in DB
	DeveloperActions_CREATE_JOURNEY DeveloperActions = 331
	// dev action to update journey record in DB
	DeveloperActions_UPDATE_JOURNEY DeveloperActions = 332
	// dev action to update activity metadata in db
	DeveloperActions_UPDATE_ACTIVITY_METADATA DeveloperActions = 333
	// dev action to handle vkyc call state
	DeveloperActions_HANDLE_VKYC_CALL_STATE DeveloperActions = 334
	// dev action to upload risk related unified lea complaints
	DeveloperActions_UPLOAD_UNIFIED_LEA_COMPLAINTS DeveloperActions = 335
	// dev action to soft delete service request in non-prod environment
	DeveloperActions_DELETE_SERVICE_REQUEST DeveloperActions = 336
	// dev action for failing debit card creation request
	DeveloperActions_FAIL_DEBIT_CARD_CREATION DeveloperActions = 337
	// dev action to get trading account details for account closure
	DeveloperActions_USSTOCKS_TRADING_ACCOUNT_SUMMARY DeveloperActions = 338
	// dev action to cache model response for standard questions in CX contact us flow
	DeveloperActions_CACHE_CONTACT_US_MODEL_RESPONSE DeveloperActions = 339
	// dev action for b2b users onboarding status tracking
	DeveloperActions_B2B_USERS_ONBOARDING_STATUS_TRACKING DeveloperActions = 340
	// dev action to update internal status of usstocks
	DeveloperActions_USSTOCKS_UPDATE_DETAILS DeveloperActions = 341
	// dev action for downloading mutual fund file (ex: feed file) by vendor order id
	DeveloperActions_MF_DOWNLOAD_FILE_BY_VENDOR_ORDER_IDS DeveloperActions = 342
	DeveloperActions_STOCK_GUARDIAN_REDACT_CKYC_DOCUMENT  DeveloperActions = 343
	// Dev Action to download Lead Mgmt Excel file
	DeveloperActions_LEAD_MGMT_DOWNLOAD_FILE DeveloperActions = 344
	// dev action for changing the state of Deposit Account by depositID or depositAccountNumber
	DeveloperActions_DEPOSIT_UPDATE_STATE DeveloperActions = 345
	// dev action to execute EMI mandate for stock guardian loan accounts
	DeveloperActions_STOCK_GUARDIAN_EXECUTE_EMI_MANDATE DeveloperActions = 346
	// dev action to get sa closure eligibility for list of users
	DeveloperActions_GET_SAVINGS_ACCOUNT_CLOSURE_ELIGIBILITY_IN_BULK DeveloperActions = 347
	// dev action to soft delete kyc agent.
	DeveloperActions_DELETE_KYC_AGENT DeveloperActions = 348
	// dev action for updating or creating dynamic ui element variant
	DeveloperActions_SALARY_PROGRAM_DYNAMIC_UI_ELEMENT_UPDATE_OR_CREATE_VARIANT DeveloperActions = 349
	// dev action for updating dynamic ui element evaluator config
	DeveloperActions_SALARY_PROGRAM_DYNAMIC_UI_ELEMENT_UPDATE_OR_CREATE_EVALUATOR_CONFIG DeveloperActions = 350
	// dev action to process cross validation manual review for non-resident users
	DeveloperActions_PROCESS_NON_RESIDENT_CROSS_VALIDATION_MANUAL_REVIEW DeveloperActions = 351
	// dev action to review the passport which is issued outside india manually by ops team
	// ops team will review the passport and mark the stage as success/failure
	DeveloperActions_NR_ONBOARDING_PASSPORT_MANUAL_REVIEW DeveloperActions = 352
	// dev action for adding stock in catalog
	DeveloperActions_ADD_STOCK_WITH_ISINS                   DeveloperActions = 353
	DeveloperActions_STOCK_GUARDIAN_EXECUTE_DISBURSAL_RETRY DeveloperActions = 354
	// dev action to bulk upload redlist in db
	DeveloperActions_UPLOAD_RISK_REDLIST DeveloperActions = 355
	// dev action to trigger simulator for recurring payment validation
	DeveloperActions_SEND_ENACH_MANDATE_NOTIFICATION_CALLBACK DeveloperActions = 356
	// dev action for b2b users onboarding limited details
	DeveloperActions_B2B_USERS_ONBOARDING_STATUS_LIMITED_DETAILS DeveloperActions = 357
	// dev action to update loans outcall ticket
	DeveloperActions_UPDATE_LOANS_OUTCALL_TICKET      DeveloperActions = 358
	DeveloperActions_CREATE_SUGGESTED_ACTION_FOR_RULE DeveloperActions = 359
	// dev action to create rule review type mapping
	DeveloperActions_CREATE_RULE_REVIEW_TYPE_MAPPING DeveloperActions = 360
	DeveloperActions_FEDERAL_ESCALATION_CREATION     DeveloperActions = 361
	// dev action for b2b rms to send email for UN name check failure
	DeveloperActions_B2B_UN_NAME_CHECK_FAILURE_EMAIL DeveloperActions = 362
	// dev action to update user name and dob from partner bank
	DeveloperActions_REFRESH_USER_INFO_FROM_PARTNER_BANK DeveloperActions = 363
	// dev action to delete user assets
	DeveloperActions_DELETE_USER_ASSETS DeveloperActions = 364
	// dev action to update image to Epifi Icons S3 bucket
	DeveloperActions_UPLOAD_IMAGE_TO_EPIFI_ICONS_S3_BUCKET DeveloperActions = 365
	// dev action to update loec status to expired
	DeveloperActions_EXPIRE_LOEC DeveloperActions = 366
	// Dev action to update employment details in loan application entity
	DeveloperActions_STOCK_GUARDIAN_UPDATE_EMPLOYMENT_DETAILS DeveloperActions = 367
	// Dev action to fetch utrs from log
	DeveloperActions_FETCH_UTRS_FROM_LOG DeveloperActions = 368
	// dev action to update manual_intervention status of lr and lse to in_progress
	DeveloperActions_RESET_LOAN_REQUEST DeveloperActions = 369
	// Dev action to get files from wealth networth insights bucket
	DeveloperActions_WEALTH_NETWORTH_INSIGHTS_GET_FILES_FROM_BUCKET DeveloperActions = 370
)

// Enum value maps for DeveloperActions.
var (
	DeveloperActions_name = map[int32]string{
		0:   "DEVELOPER_ACTIONS_UNSPECIFIED",
		1:   "RETRY_LIVENESS",
		2:   "RETRY_CREATE_BANK_CUSTOMER",
		3:   "RETRY_CREATE_ACCOUNT",
		4:   "RETRY_EKYC",
		5:   "RETRY_CKYC",
		6:   "CREATE_CARD",
		7:   "CREATE_REWARD_OFFER",
		8:   "UPDATE_REWARD_OFFER_STATUS",
		9:   "CREATE_LUCKY_DRAW_CAMPAIGN",
		10:  "CREATE_LUCKY_DRAW",
		11:  "PULL_APP_LOGS",
		12:  "ADD_USER_GROUP_MAPPING",
		13:  "DELETE_USER",
		14:  "RECOVER_AUTH_FACTOR_UPDATE",
		15:  "REALLOW_USER_TO_TRY_WAITLIST",
		16:  "UPDATE_REWARD_OFFER_DISPLAY",
		17:  "TRIGGER_FAILED_WAITLIST_EMAIL",
		18:  "TRIGGER_FAILED_PIS_INDEXING",
		19:  "CREATE_OFFER",
		20:  "CREATE_OFFER_LISTING",
		21:  "UPDATE_OFFER_LISTING",
		22:  "DELETE_OFFER_LISTING",
		23:  "CREATE_OFFER_INVENTORY",
		24:  "ADD_OFFER_TO_INVENTORY",
		25:  "DELETE_OFFER_INVENTORY",
		26:  "FORCE_TRIGGER_RECON",
		27:  "TRIGGER_GMAIL_ACTOR_SYNC",
		28:  "UNLINK_GMAIL_ACCOUNT",
		29:  "DELETE_WAITLIST_USER_FROM_USER_ID",
		30:  "MARK_WAITLIST_USERS_EARLY_ACCESS",
		31:  "SEND_EMAIL_TO_EARLY_ACCESS",
		32:  "SYNC_ONBOARDING",
		33:  "FITTT_CREATE_NEW_RULE",
		34:  "FITTT_UPDATE_RULE",
		35:  "FITTT_GET_RULES_FOR_CLIENT",
		36:  "UPDATE_ONBOARDING_STAGE",
		37:  "SEND_COMMS_TO_WAITLIST_USER",
		38:  "UPDATE_OFFER_DISPLAY",
		39:  "SEED_FINITE_CODES",
		40:  "FITTT_INITIATE_MATCH_UPDATE",
		41:  "INITIATE_CARD_NOTIFICATIONS",
		43:  "MARK_LIVENESS_PASSED",
		44:  "CREATE_PREFERENCE",
		45:  "UPDATE_SHIPPING_ADDRESS_AT_VENDOR",
		46:  "CREATE_SHIPPING_PREFERENCE",
		47:  "RETRY_PAY_ORDER_RMS_EVENT",
		48:  "SEND_WELCOME_WHATSAPP",
		49:  "SEND_REWARDS_CAMPAIGN_COMM",
		50:  "UNREDACTED_USER",
		51:  "FORCE_PROCESS_ORDER",
		52:  "RETRY_REWARD_PROCESSING",
		53:  "GET_FIT_TXN_AGGREGATE_SEARCH",
		54:  "UPDATE_USER_ACCESS_REVOKE_STATE",
		55:  "UPDATE_USER_PROFILE_NAME",
		56:  "GENERATE_ACCOUNT_STATEMENT",
		57:  "ONBOARDING_SNAPSHOT",
		58:  "BACKFILL_FRESHDESK_TICKET_CONTACTS",
		59:  "PASS_ONBOARDING_STAGE",
		60:  "CREATE_REWARD_OFFER_GROUP",
		61:  "RESET_KYC_NAME_DOB_RETRY",
		62:  "RETRY_OFFER_REDEMPTION",
		63:  "MANUAL_SCREENING_UPDATE",
		64:  "UN_NAME_CHECK_UNBLOCK",
		65:  "RESET_DEBIT_CARD_NAME_RETRY",
		66:  "FITTT_STOP_SCHEDULES_JOBS",
		67:  "REFRESH_VKYC_STATUS",
		68:  "UPDATE_CARD_PIN_SET",
		69:  "RAISE_AA_CONSENT",
		70:  "FORCE_CARD_CREATION_ENQUIRY",
		71:  "MARK_FACEMATCH_PASSED",
		72:  "TRIGGER_REWARDS_MANUAL_GIVEAWAY_EVENT",
		73:  "DELETE_USER_GROUP_MAPPING",
		74:  "SYNC_WEALTH_ONBOARDING",
		75:  "UNLOCK_IN_APP_REFERRAL",
		76:  "TRIGGER_VKYC_CALLBACK",
		77:  "HANDLE_SAVINGS_ACCOUNT_CLOSURE",
		78:  "UPDATE_GMAIL_INSIGHTS_MERCHANTS",
		79:  "UPDATE_GMAIL_INSIGHTS_MERCHANT_QUERIES",
		80:  "UPDATE_INAPPHELP_FAQ",
		81:  "AA_CONSENT_STATUS_UPDATE",
		82:  "AA_ACCOUNT_DELINK",
		83:  "REACTIVATE_DEVICE",
		84:  "FITTT_UPDATE_RULES_WEIGHTAGE",
		85:  "REOPEN_SAVINGS_ACCOUNT_IN_DB",
		86:  "UPDATE_WEALTH_ONBOARDING_STATUS",
		87:  "FITTT_CREATE_HOME_CARD",
		88:  "FITTT_UPDATE_HOME_CARD",
		89:  "UPDATE_PAN_NAME_REVIEW",
		90:  "ADD_MEDIA_PLAYLIST",
		91:  "UPDATE_MEDIA_PLAYLIST",
		92:  "ADD_MEDIA_CONTENT_STORY",
		93:  "UPDATE_MEDIA_CONTENT_STORY",
		94:  "FITTT_CREATE_SCHEDULE",
		95:  "ADD_UI_CONTEXT_TO_MEDIA_PLAYLIST_MAPPING",
		96:  "DELETE_UI_CONTEXT_TO_MEDIA_PLAYLIST_MAPPING",
		97:  "ADD_MEDIA_PLAYLIST_TO_MEDIA_CONTENT_MAPPING",
		98:  "DELETE_MEDIA_PLAYLIST_TO_MEDIA_CONTENT_MAPPING",
		99:  "ADD_CREDIT_MIS_FILE_META_DATA",
		100: "FITTT_BULK_UPDATE_SUBSCRIPTIONS_STATE",
		103: "CREATE_MUTUAL_FUND",
		102: "UPDATE_MUTUAL_FUND",
		101: "MUTUAL_FUND_UPLOAD_REVERSE_FEED_FILE",
		104: "REPLAY_AA_ACCOUNT_EVENTS",
		105: "CREATE_EXCHANGER_OFFER",
		106: "CREATE_EXCHANGER_OFFER_LISTING",
		107: "UPDATE_EXCHANGER_OFFER_DISPLAY",
		108: "UPDATE_EXCHANGER_OFFER_LISTING",
		109: "DELETE_EXCHANGER_OFFER_LISTING",
		110: "REPLAY_AA_TXN_EVENTS",
		111: "FITTT_ARCHIVE_RULES_AND_SUBSCRIPTIONS",
		112: "FORCE_PROCESS_DEPOSIT_REQUEST",
		113: "SEARCH_PARSE_QUERY_BASE",
		114: "MARK_WEALTH_ONBOARDING_LIVENESS_PASSED",
		115: "MUTUAL_FUND_DEACTIVATE_ENTITY_FROM_FILE",
		116: "FITTT_CREATE_COLLECTION",
		117: "FITTT_UPDATE_COLLECTION",
		118: "MARK_WEALTH_ONBOARDING_REDACTION_PASSED",
		119: "UPDATE_MUTUAL_FUND_ORDER_STATUS",
		120: "TRIGGER_RECURRING_PAYMENT_EXECUTION",
		121: "UPLOAD_MARKETING_CAMPAIGN_USERS_LIST",
		122: "MARK_WEALTH_ONBOARDING_EXPIRY_PASSED",
		123: "MUTUAL_FUND_DOWNLOAD_CREDIT_MIS_REPORT",
		124: "ADD_IN_APP_TARGETED_COMMS_ELEMENT",
		125: "UPDATE_IN_APP_TARGETED_COMMS_ELEMENT",
		126: "ADD_IN_APP_TARGETED_COMMS_MAPPING",
		127: "DELETE_IN_APP_TARGETED_COMMS_MAPPING",
		128: "CREATE_EXCHANGER_OFFER_GROUP",
		129: "MF_DOWNLOAD_OPS_FILE",
		130: "MF_RE_TRIGGER_PRE_REQUISITES",
		131: "MANUAL_CARD_UNSUSPEND",
		132: "UPDATE_INSIGHT_FRAMEWORK",
		133: "UPDATE_INSIGHT_SEGMENT",
		134: "UPDATE_INSIGHT_CONTENT_TEMPLATE",
		135: "MF_UPLOAD_CATALOG_UPDATE",
		136: "UPDATE_OFFER_DISPLAY_RANK",
		137: "MF_UPLOAD_CREDIT_MIS_NON_PROD",
		138: "CREATE_EXCHANGER_OFFER_INVENTORY",
		139: "UPDATE_REWARD_OFFER_DISPLAY_RANK",
		140: "ADD_USER_TO_VKYC_PRIORITY",
		141: "UPDATE_USER_DOB",
		142: "UPDATE_P2P_INVESTMENT_TRANSACTION_STATUS",
		143: "UPDATE_USER_PHOTO",
		144: "UPDATE_P2P_INVESTOR_TOTAL_INVESTMENT_COUNT",
		145: "MF_CREATE_COLLECTION",
		146: "MF_UPDATE_COLLECTION",
		147: "MF_ADD_FUND_TO_COLLECTION",
		148: "MF_UPDATE_FUND_IN_COLLECTION",
		149: "MF_REMOVE_FUNDS_FROM_COLLECTION",
		150: "MF_PROCESS_REVERSE_FEED_FILE",
		151: "UPDATE_P2P_VENDOR_RESPONSE_APPROVAL_STATUS",
		152: "PHYSICAL_CARD_REQUEST",
		153: "INCREMENT_EXCHANGER_OFFER_INVENTORY",
		154: "CREATE_REFERRALS_SEASON",
		155: "UPDATE_REFERRALS_SEASON",
		156: "DELETE_SEGMENT",
		157: "MARK_STEP_STALE_WEALTH_ONBOARDING",
		158: "REQUEST_NEW_CARD",
		159: "TRIGGER_VPA_CREATION",
		160: "ADD_MANUAL_CALL_ROUTING_MAPPINGS",
		161: "TRIGGER_SEGMENT_EXPORT",
		162: "MF_RECONCILIATION",
		163: "CREATE_TICKET_DETAILS_TRANSFORMATION",
		164: "UPDATE_TICKET_DETAILS_TRANSFORMATION",
		165: "DELETE_TICKET_DETAILS_TRANSFORMATION",
		166: "DEEPLINK_BASE64_ENCODER",
		167: "PRE_APPROVED_LOAN_MANUAL_REVIEW",
		168: "PRE_APPROVED_LOAN_CREATE_OFFER",
		169: "PRE_APPROVED_LOAN_UPDATE_LOAN_STATUS",
		170: "CATEGORISE_SCREENER_DOMAINS",
		171: "CREATE_SALARY_PROGRAM_REFERRALS_SEASON",
		172: "START_USER_ACTION",
		173: "CREATE_NUDGE",
		174: "EDIT_NUDGE",
		175: "SAVINGS_RISK_BANK_ACTIONS",
		176: "UPDATE_USER_FATHER_NAME",
		177: "UPDATE_ACCOUNT_FREEZE_STATUS_IN_SIMULATOR",
		178: "INTERNATIONAL_FUND_TRANSFER_UPLOAD_LRS_CHECK_FILE",
		179: "INTERNATIONAL_FUND_TRANSFER_ACKNOWLEDGE_SWIFT_TRANSFER",
		180: "INTERNATIONAL_FUND_TRANSFER_ACKNOWLEDGE_INWARD_FUND_TRANSFER",
		181: "PAY_DOWNLOAD_LRS_CHECK_FILE",
		182: "FOREIGN_REMITTANCE_PROCESS_INWARD_REMITTANCE",
		183: "UPDATE_ACCOUNT_FREEZE_STATUS",
		184: "CREATE_DISCOUNT",
		185: "DELETE_DISCOUNT",
		186: "UPLOAD_USER_ISSUE_INFO_FOR_AGENTS",
		187: "UPDATE_USER_EMPLOYMENT",
		188: "CREATE_OR_UPDATE_SEGMENT_MAPPING",
		189: "DEPOSIT_ADD_INTEREST_RATE",
		190: "DEPOSIT_UPDATE_INTEREST_RATE",
		191: "DEPOSIT_DELETE_INTEREST_RATE",
		192: "GET_DOCKET_URL_WEALTH_ONBOARDING",
		193: "UPDATE_EXCHANGER_OFFER_STATUS",
		194: "PROFILE_EVALUATOR_ACTIONS",
		195: "INCOME_OCCUPATION_DISCREPANCY_VERIFICATION",
		196: "CREATE_REFERRALS_SEGMENTED_COMPONENT",
		197: "UPDATE_REFERRALS_SEGMENTED_COMPONENT",
		198: "DELETE_REFERRALS_SEGMENTED_COMPONENT",
		199: "UPLOAD_RISK_CASES",
		200: "CREATE_SEGMENT",
		201: "RISK_TXN_CASE_MANUAL_REVIEW",
		202: "REMOVE_RED_LIST_ENTRY",
		203: "ADD_RED_LIST_ENTRY",
		204: "P2P_GET_INVESTOR_DASHBOARD",
		205: "P2P_GET_INVESTMENT_SUMMARY",
		206: "P2P_DOWNLOAD_RECON_FILE",
		207: "GET_SIGNED_URL_WEALTH",
		208: "CREDIT_CARD_UPDATE_CARD_REQUEST_STATUS",
		209: "USSTOCKS_REFRESH_STOCK_DETAILS",
		210: "UPDATE_NUDGE_STATUS",
		211: "UPLOAD_AML_REPORT",
		212: "DEACTIVATE_DEVICE",
		213: "FEDERAL_LOAN_LIVENESS_REVIEW",
		214: "CREATE_REWARD_OFFER_IN_BULK",
		215: "FITTT_PUBLISH_SHARK_TANK_EVENT",
		216: "CREATE_FOREX_RATE",
		217: "UPDATE_FOREX_RATE",
		218: "REPRIEVE_VKYC",
		219: "USSTOCKS_GROSS_SUMMARY",
		220: "AML_UPDATE_FILE_GEN_STATUS",
		221: "MF_DOWNLOAD_FILE_FROM_BUCKET",
		222: "PERFORM_RISK_REVIEW_ACTION",
		223: "FOREX_RATE_REPORT",
		224: "CURRENT_FOREX_RATE",
		225: "UPLOAD_WEALTH_DOCUMENT",
		226: "UPDATE_P2P_VENDOR_RESPONSE_MATURITY_TRANSACTION_DAYS_TO_EXPIRE",
		227: "GET_FOREX_RATES",
		228: "PRE_APPROVED_LOAN_LL_UPDATE_ENTITY_STATUS",
		229: "CREATE_TEMPORAL_SCHEDULE",
		230: "ADD_HOME_PROMO_BANNER",
		231: "USSTOCKS_ACCOUNT_STATEMENT",
		232: "RETRY_MUTUAL_FUND_ORDER",
		233: "RISK_CREATE_ALLOWED_ANNOTATION",
		234: "RISK_BANK_ACTION_MANUAL_OVERRIDE",
		235: "USSTOCKS_DOWNLOAD_FILE_FROM_BUCKET",
		236: "CREATE_NUDGES_IN_BULK",
		237: "VENDOR_ACCOUNT_PENNY_DROP",
		238: "UPDATE_WEALTH_USER_INPUT_PENDING_DATA",
		239: "CREATE_OFFERS_IN_BULK",
		240: "RESET_CELESTIAL_WORKFLOW_EXECUTION",
		241: "CREATE_REFERRAL_NOTIFICATION_CONFIG",
		242: "UPDATE_REFERRAL_NOTIFICATION_CONFIG_STATUS",
		243: "UPDATE_REFERRAL_NOTIFICATION_CONFIG_CONTENT",
		244: "DELETE_REFERRAL_NOTIFICATION_CONFIG",
		245: "UPLOAD_LEA_COMPLAINTS",
		246: "ADD_HOME_POP_UP_BANNER",
		247: "DEPOSIT_LIST_ACCOUNTS_VENDOR",
		248: "MARK_LOAN_REQUEST_CANCEL",
		249: "IFT_GENERATE_AGGREGATED_TAX_REPORT",
		250: "CREATE_OR_UPDATE_DYNAMIC_UI_ELEMENT_VARIANT",
		251: "UPDATE_DYNAMIC_UI_ELEMENT_EVALUATOR_CONFIG",
		252: "CREATE_CMS_PRODUCT",
		253: "CREATE_CMS_SKU",
		254: "CREATE_CMS_COUPON_IN_BULK",
		255: "CREATE_HOME_LAYOUT",
		256: "CREATE_HOME_LAYOUT_SEGMENT_MAPPING",
		257: "DELETE_HOME_LAYOUT",
		258: "DELETE_HOME_LAYOUT_SEGMENT_MAPPING",
		259: "MARK_USERS_BY_ACQUISITION_INFO",
		260: "GET_HOME_LAYOUT",
		261: "GET_HOME_LAYOUT_SEGMENT_MAPPING",
		262: "ADD_INAPPHELP_FEEDBACK_ENGINE_SURVEY",
		263: "UPDATE_INAPPHELP_FEEDBACK_ENGINE_SURVEY",
		264: "ADD_INAPPHELP_FEEDBACK_ENGINE_QUESTION",
		265: "UPDATE_INAPPHELP_FEEDBACK_ENGINE_QUESTION",
		266: "MARK_CARD_DELIVERY_TRACKING_STATE_RECEIVED",
		267: "LOAN_UPDATE_LOAN_STEP_STATUS",
		268: "USSTOCKS_CREATE_COLLECTION",
		269: "USSTOCKS_UPDATE_COLLECTION",
		270: "USSTOCKS_ADD_STOCK_TO_COLLECTION",
		271: "USSTOCKS_UPDATE_STOCK_IN_COLLECTION",
		272: "USSTOCKS_REMOVE_STOCK_FROM_COLLECTION",
		273: "UPDATE_SEGMENT",
		274: "ADD_EMPLOYERS",
		275: "IFT_REJECT_TRANSACTIONS_IN_OUTWARD_SWIFT_FILE",
		276: "WHITELIST_EMAIL_ID",
		277: "CREATE_KYC_AGENT",
		278: "DATA_EXTRACTION",
		279: "CREATE_ISSUE_CONFIG",
		280: "UPDATE_REWARD_OFFER",
		281: "GENERATE_CHATBOT_ACCESS_TOKEN",
		282: "P2P_REGISTER_BANKING_DETAILS",
		283: "UPLOAD_LEA_COMPLAINT_NARRATIONS",
		284: "UPDATE_EMPLOYER_DETAILS",
		285: "BULK_SETUP_REFERRAL_SEGMENTED_COMPONENTS",
		286: "UPLOAD_LEA_COMPLAINT_SOURCE_DETAILS",
		287: "USER_COMMS_CONTROL",
		288: "CREDIT_CARD_UPDATE_CARD_REQUEST_STAGE_STATUS",
		289: "SIMULATE_USSTOCKS_OUTWARD_FUND_TRANSFER_NON_PROD",
		290: "CREATE_FAQ_CONTEXT_MAPPING",
		291: "P2P_UPDATE_INVESTMENT_TRANSACTION",
		292: "FETCH_ACCOUNT_STATUS",
		293: "CREATE_HOME_SIMULATOR_LAYOUT",
		294: "GET_WEALTH_ONBOARDING_DETAILS_BY_PAN",
		295: "CREATE_WATSON_TICKET_DETAILS",
		296: "UPDATE_WATSON_TICKET_DETAILS",
		297: "USSTOCKS_CREATE_BANK_RELATIONSHIP_WITH_BROKER",
		298: "LOANS_ABFL_ACTIONS",
		299: "TRIGGER_KYC_NAME_DOB_VALIDATION",
		300: "ONBOARDING_TROUBLESHOOTING_DETAILS",
		301: "CREATE_EVENT_CONFIG",
		302: "UPDATE_EVENT_CONFIG",
		303: "FAIL_BANK_CUSTOMER",
		304: "P2P_GET_CASH_LEDGER",
		305: "GET_MF_FOLIO_BALANCE",
		306: "IFT_INITIATE_REFUND",
		307: "RAISE_MANUAL_SALARY_VERIFICATION_REQUESTS_IN_BULK",
		308: "SIMULATE_REWARD_GENERATION",
		309: "UPDATE_SALARY_PROGRAM_REFERRALS_SEASON",
		310: "PROCESS_ITC_POINTS_HANDBACK_FILE",
		311: "RISK_UPLOAD_DISPUTES",
		312: "SEGMENT_METADATA_ACTION",
		313: "SET_SEGMENT_METADATA_APPROVAL_STATUS",
		314: "OVERRIDE_PAYMENT_HEALTH_ENGINE_STATUS",
		315: "OVERRIDE_CSIS_HEALTH_ENGINE_STATUS",
		316: "SOF_LIMIT_MANUAL_OVERRIDE",
		317: "PASS_RISK_SCREENER_ATTEMPT",
		318: "ADD_PIN_CODE_ENTRY",
		319: "EXTRACT_PAN_FROM_KRA_DOCKET",
		320: "USSTOCKS_RESET_ONBOARDING_DATA",
		321: "RISK_MANAGE_WHITELIST",
		322: "USSTOCKS_ADD_INVESTOR_ADDRESS",
		323: "CREATE_ACTIVITY_METADATA",
		324: "TRIGGER_TXN_CATEGORISATION",
		325: "GET_US_STOCK_ACCOUNT_ACTIVITIES_CSV",
		326: "TRIGGER_UNSECURED_CC_RENEWAL_FEE_REVERSAL",
		327: "USSTOCKS_GENERATE_ADHOC_INWARD_REMITTANCE_FILES",
		328: "GENERATE_MODEL_RESPONSE_FOR_USERS_CX_QUERY",
		329: "USSTOCKS_REGENERATE_INWARD_FILE",
		330: "MAP_DEBIT_CARD_FOREX_TXN",
		331: "CREATE_JOURNEY",
		332: "UPDATE_JOURNEY",
		333: "UPDATE_ACTIVITY_METADATA",
		334: "HANDLE_VKYC_CALL_STATE",
		335: "UPLOAD_UNIFIED_LEA_COMPLAINTS",
		336: "DELETE_SERVICE_REQUEST",
		337: "FAIL_DEBIT_CARD_CREATION",
		338: "USSTOCKS_TRADING_ACCOUNT_SUMMARY",
		339: "CACHE_CONTACT_US_MODEL_RESPONSE",
		340: "B2B_USERS_ONBOARDING_STATUS_TRACKING",
		341: "USSTOCKS_UPDATE_DETAILS",
		342: "MF_DOWNLOAD_FILE_BY_VENDOR_ORDER_IDS",
		343: "STOCK_GUARDIAN_REDACT_CKYC_DOCUMENT",
		344: "LEAD_MGMT_DOWNLOAD_FILE",
		345: "DEPOSIT_UPDATE_STATE",
		346: "STOCK_GUARDIAN_EXECUTE_EMI_MANDATE",
		347: "GET_SAVINGS_ACCOUNT_CLOSURE_ELIGIBILITY_IN_BULK",
		348: "DELETE_KYC_AGENT",
		349: "SALARY_PROGRAM_DYNAMIC_UI_ELEMENT_UPDATE_OR_CREATE_VARIANT",
		350: "SALARY_PROGRAM_DYNAMIC_UI_ELEMENT_UPDATE_OR_CREATE_EVALUATOR_CONFIG",
		351: "PROCESS_NON_RESIDENT_CROSS_VALIDATION_MANUAL_REVIEW",
		352: "NR_ONBOARDING_PASSPORT_MANUAL_REVIEW",
		353: "ADD_STOCK_WITH_ISINS",
		354: "STOCK_GUARDIAN_EXECUTE_DISBURSAL_RETRY",
		355: "UPLOAD_RISK_REDLIST",
		356: "SEND_ENACH_MANDATE_NOTIFICATION_CALLBACK",
		357: "B2B_USERS_ONBOARDING_STATUS_LIMITED_DETAILS",
		358: "UPDATE_LOANS_OUTCALL_TICKET",
		359: "CREATE_SUGGESTED_ACTION_FOR_RULE",
		360: "CREATE_RULE_REVIEW_TYPE_MAPPING",
		361: "FEDERAL_ESCALATION_CREATION",
		362: "B2B_UN_NAME_CHECK_FAILURE_EMAIL",
		363: "REFRESH_USER_INFO_FROM_PARTNER_BANK",
		364: "DELETE_USER_ASSETS",
		365: "UPLOAD_IMAGE_TO_EPIFI_ICONS_S3_BUCKET",
		366: "EXPIRE_LOEC",
		367: "STOCK_GUARDIAN_UPDATE_EMPLOYMENT_DETAILS",
		368: "FETCH_UTRS_FROM_LOG",
		369: "RESET_LOAN_REQUEST",
		370: "WEALTH_NETWORTH_INSIGHTS_GET_FILES_FROM_BUCKET",
	}
	DeveloperActions_value = map[string]int32{
		"DEVELOPER_ACTIONS_UNSPECIFIED":                                       0,
		"RETRY_LIVENESS":                                                      1,
		"RETRY_CREATE_BANK_CUSTOMER":                                          2,
		"RETRY_CREATE_ACCOUNT":                                                3,
		"RETRY_EKYC":                                                          4,
		"RETRY_CKYC":                                                          5,
		"CREATE_CARD":                                                         6,
		"CREATE_REWARD_OFFER":                                                 7,
		"UPDATE_REWARD_OFFER_STATUS":                                          8,
		"CREATE_LUCKY_DRAW_CAMPAIGN":                                          9,
		"CREATE_LUCKY_DRAW":                                                   10,
		"PULL_APP_LOGS":                                                       11,
		"ADD_USER_GROUP_MAPPING":                                              12,
		"DELETE_USER":                                                         13,
		"RECOVER_AUTH_FACTOR_UPDATE":                                          14,
		"REALLOW_USER_TO_TRY_WAITLIST":                                        15,
		"UPDATE_REWARD_OFFER_DISPLAY":                                         16,
		"TRIGGER_FAILED_WAITLIST_EMAIL":                                       17,
		"TRIGGER_FAILED_PIS_INDEXING":                                         18,
		"CREATE_OFFER":                                                        19,
		"CREATE_OFFER_LISTING":                                                20,
		"UPDATE_OFFER_LISTING":                                                21,
		"DELETE_OFFER_LISTING":                                                22,
		"CREATE_OFFER_INVENTORY":                                              23,
		"ADD_OFFER_TO_INVENTORY":                                              24,
		"DELETE_OFFER_INVENTORY":                                              25,
		"FORCE_TRIGGER_RECON":                                                 26,
		"TRIGGER_GMAIL_ACTOR_SYNC":                                            27,
		"UNLINK_GMAIL_ACCOUNT":                                                28,
		"DELETE_WAITLIST_USER_FROM_USER_ID":                                   29,
		"MARK_WAITLIST_USERS_EARLY_ACCESS":                                    30,
		"SEND_EMAIL_TO_EARLY_ACCESS":                                          31,
		"SYNC_ONBOARDING":                                                     32,
		"FITTT_CREATE_NEW_RULE":                                               33,
		"FITTT_UPDATE_RULE":                                                   34,
		"FITTT_GET_RULES_FOR_CLIENT":                                          35,
		"UPDATE_ONBOARDING_STAGE":                                             36,
		"SEND_COMMS_TO_WAITLIST_USER":                                         37,
		"UPDATE_OFFER_DISPLAY":                                                38,
		"SEED_FINITE_CODES":                                                   39,
		"FITTT_INITIATE_MATCH_UPDATE":                                         40,
		"INITIATE_CARD_NOTIFICATIONS":                                         41,
		"MARK_LIVENESS_PASSED":                                                43,
		"CREATE_PREFERENCE":                                                   44,
		"UPDATE_SHIPPING_ADDRESS_AT_VENDOR":                                   45,
		"CREATE_SHIPPING_PREFERENCE":                                          46,
		"RETRY_PAY_ORDER_RMS_EVENT":                                           47,
		"SEND_WELCOME_WHATSAPP":                                               48,
		"SEND_REWARDS_CAMPAIGN_COMM":                                          49,
		"UNREDACTED_USER":                                                     50,
		"FORCE_PROCESS_ORDER":                                                 51,
		"RETRY_REWARD_PROCESSING":                                             52,
		"GET_FIT_TXN_AGGREGATE_SEARCH":                                        53,
		"UPDATE_USER_ACCESS_REVOKE_STATE":                                     54,
		"UPDATE_USER_PROFILE_NAME":                                            55,
		"GENERATE_ACCOUNT_STATEMENT":                                          56,
		"ONBOARDING_SNAPSHOT":                                                 57,
		"BACKFILL_FRESHDESK_TICKET_CONTACTS":                                  58,
		"PASS_ONBOARDING_STAGE":                                               59,
		"CREATE_REWARD_OFFER_GROUP":                                           60,
		"RESET_KYC_NAME_DOB_RETRY":                                            61,
		"RETRY_OFFER_REDEMPTION":                                              62,
		"MANUAL_SCREENING_UPDATE":                                             63,
		"UN_NAME_CHECK_UNBLOCK":                                               64,
		"RESET_DEBIT_CARD_NAME_RETRY":                                         65,
		"FITTT_STOP_SCHEDULES_JOBS":                                           66,
		"REFRESH_VKYC_STATUS":                                                 67,
		"UPDATE_CARD_PIN_SET":                                                 68,
		"RAISE_AA_CONSENT":                                                    69,
		"FORCE_CARD_CREATION_ENQUIRY":                                         70,
		"MARK_FACEMATCH_PASSED":                                               71,
		"TRIGGER_REWARDS_MANUAL_GIVEAWAY_EVENT":                               72,
		"DELETE_USER_GROUP_MAPPING":                                           73,
		"SYNC_WEALTH_ONBOARDING":                                              74,
		"UNLOCK_IN_APP_REFERRAL":                                              75,
		"TRIGGER_VKYC_CALLBACK":                                               76,
		"HANDLE_SAVINGS_ACCOUNT_CLOSURE":                                      77,
		"UPDATE_GMAIL_INSIGHTS_MERCHANTS":                                     78,
		"UPDATE_GMAIL_INSIGHTS_MERCHANT_QUERIES":                              79,
		"UPDATE_INAPPHELP_FAQ":                                                80,
		"AA_CONSENT_STATUS_UPDATE":                                            81,
		"AA_ACCOUNT_DELINK":                                                   82,
		"REACTIVATE_DEVICE":                                                   83,
		"FITTT_UPDATE_RULES_WEIGHTAGE":                                        84,
		"REOPEN_SAVINGS_ACCOUNT_IN_DB":                                        85,
		"UPDATE_WEALTH_ONBOARDING_STATUS":                                     86,
		"FITTT_CREATE_HOME_CARD":                                              87,
		"FITTT_UPDATE_HOME_CARD":                                              88,
		"UPDATE_PAN_NAME_REVIEW":                                              89,
		"ADD_MEDIA_PLAYLIST":                                                  90,
		"UPDATE_MEDIA_PLAYLIST":                                               91,
		"ADD_MEDIA_CONTENT_STORY":                                             92,
		"UPDATE_MEDIA_CONTENT_STORY":                                          93,
		"FITTT_CREATE_SCHEDULE":                                               94,
		"ADD_UI_CONTEXT_TO_MEDIA_PLAYLIST_MAPPING":                            95,
		"DELETE_UI_CONTEXT_TO_MEDIA_PLAYLIST_MAPPING":                         96,
		"ADD_MEDIA_PLAYLIST_TO_MEDIA_CONTENT_MAPPING":                         97,
		"DELETE_MEDIA_PLAYLIST_TO_MEDIA_CONTENT_MAPPING":                      98,
		"ADD_CREDIT_MIS_FILE_META_DATA":                                       99,
		"FITTT_BULK_UPDATE_SUBSCRIPTIONS_STATE":                               100,
		"CREATE_MUTUAL_FUND":                                                  103,
		"UPDATE_MUTUAL_FUND":                                                  102,
		"MUTUAL_FUND_UPLOAD_REVERSE_FEED_FILE":                                101,
		"REPLAY_AA_ACCOUNT_EVENTS":                                            104,
		"CREATE_EXCHANGER_OFFER":                                              105,
		"CREATE_EXCHANGER_OFFER_LISTING":                                      106,
		"UPDATE_EXCHANGER_OFFER_DISPLAY":                                      107,
		"UPDATE_EXCHANGER_OFFER_LISTING":                                      108,
		"DELETE_EXCHANGER_OFFER_LISTING":                                      109,
		"REPLAY_AA_TXN_EVENTS":                                                110,
		"FITTT_ARCHIVE_RULES_AND_SUBSCRIPTIONS":                               111,
		"FORCE_PROCESS_DEPOSIT_REQUEST":                                       112,
		"SEARCH_PARSE_QUERY_BASE":                                             113,
		"MARK_WEALTH_ONBOARDING_LIVENESS_PASSED":                              114,
		"MUTUAL_FUND_DEACTIVATE_ENTITY_FROM_FILE":                             115,
		"FITTT_CREATE_COLLECTION":                                             116,
		"FITTT_UPDATE_COLLECTION":                                             117,
		"MARK_WEALTH_ONBOARDING_REDACTION_PASSED":                             118,
		"UPDATE_MUTUAL_FUND_ORDER_STATUS":                                     119,
		"TRIGGER_RECURRING_PAYMENT_EXECUTION":                                 120,
		"UPLOAD_MARKETING_CAMPAIGN_USERS_LIST":                                121,
		"MARK_WEALTH_ONBOARDING_EXPIRY_PASSED":                                122,
		"MUTUAL_FUND_DOWNLOAD_CREDIT_MIS_REPORT":                              123,
		"ADD_IN_APP_TARGETED_COMMS_ELEMENT":                                   124,
		"UPDATE_IN_APP_TARGETED_COMMS_ELEMENT":                                125,
		"ADD_IN_APP_TARGETED_COMMS_MAPPING":                                   126,
		"DELETE_IN_APP_TARGETED_COMMS_MAPPING":                                127,
		"CREATE_EXCHANGER_OFFER_GROUP":                                        128,
		"MF_DOWNLOAD_OPS_FILE":                                                129,
		"MF_RE_TRIGGER_PRE_REQUISITES":                                        130,
		"MANUAL_CARD_UNSUSPEND":                                               131,
		"UPDATE_INSIGHT_FRAMEWORK":                                            132,
		"UPDATE_INSIGHT_SEGMENT":                                              133,
		"UPDATE_INSIGHT_CONTENT_TEMPLATE":                                     134,
		"MF_UPLOAD_CATALOG_UPDATE":                                            135,
		"UPDATE_OFFER_DISPLAY_RANK":                                           136,
		"MF_UPLOAD_CREDIT_MIS_NON_PROD":                                       137,
		"CREATE_EXCHANGER_OFFER_INVENTORY":                                    138,
		"UPDATE_REWARD_OFFER_DISPLAY_RANK":                                    139,
		"ADD_USER_TO_VKYC_PRIORITY":                                           140,
		"UPDATE_USER_DOB":                                                     141,
		"UPDATE_P2P_INVESTMENT_TRANSACTION_STATUS":                            142,
		"UPDATE_USER_PHOTO":                                                   143,
		"UPDATE_P2P_INVESTOR_TOTAL_INVESTMENT_COUNT":                          144,
		"MF_CREATE_COLLECTION":                                                145,
		"MF_UPDATE_COLLECTION":                                                146,
		"MF_ADD_FUND_TO_COLLECTION":                                           147,
		"MF_UPDATE_FUND_IN_COLLECTION":                                        148,
		"MF_REMOVE_FUNDS_FROM_COLLECTION":                                     149,
		"MF_PROCESS_REVERSE_FEED_FILE":                                        150,
		"UPDATE_P2P_VENDOR_RESPONSE_APPROVAL_STATUS":                          151,
		"PHYSICAL_CARD_REQUEST":                                               152,
		"INCREMENT_EXCHANGER_OFFER_INVENTORY":                                 153,
		"CREATE_REFERRALS_SEASON":                                             154,
		"UPDATE_REFERRALS_SEASON":                                             155,
		"DELETE_SEGMENT":                                                      156,
		"MARK_STEP_STALE_WEALTH_ONBOARDING":                                   157,
		"REQUEST_NEW_CARD":                                                    158,
		"TRIGGER_VPA_CREATION":                                                159,
		"ADD_MANUAL_CALL_ROUTING_MAPPINGS":                                    160,
		"TRIGGER_SEGMENT_EXPORT":                                              161,
		"MF_RECONCILIATION":                                                   162,
		"CREATE_TICKET_DETAILS_TRANSFORMATION":                                163,
		"UPDATE_TICKET_DETAILS_TRANSFORMATION":                                164,
		"DELETE_TICKET_DETAILS_TRANSFORMATION":                                165,
		"DEEPLINK_BASE64_ENCODER":                                             166,
		"PRE_APPROVED_LOAN_MANUAL_REVIEW":                                     167,
		"PRE_APPROVED_LOAN_CREATE_OFFER":                                      168,
		"PRE_APPROVED_LOAN_UPDATE_LOAN_STATUS":                                169,
		"CATEGORISE_SCREENER_DOMAINS":                                         170,
		"CREATE_SALARY_PROGRAM_REFERRALS_SEASON":                              171,
		"START_USER_ACTION":                                                   172,
		"CREATE_NUDGE":                                                        173,
		"EDIT_NUDGE":                                                          174,
		"SAVINGS_RISK_BANK_ACTIONS":                                           175,
		"UPDATE_USER_FATHER_NAME":                                             176,
		"UPDATE_ACCOUNT_FREEZE_STATUS_IN_SIMULATOR":                           177,
		"INTERNATIONAL_FUND_TRANSFER_UPLOAD_LRS_CHECK_FILE":                   178,
		"INTERNATIONAL_FUND_TRANSFER_ACKNOWLEDGE_SWIFT_TRANSFER":              179,
		"INTERNATIONAL_FUND_TRANSFER_ACKNOWLEDGE_INWARD_FUND_TRANSFER":        180,
		"PAY_DOWNLOAD_LRS_CHECK_FILE":                                         181,
		"FOREIGN_REMITTANCE_PROCESS_INWARD_REMITTANCE":                        182,
		"UPDATE_ACCOUNT_FREEZE_STATUS":                                        183,
		"CREATE_DISCOUNT":                                                     184,
		"DELETE_DISCOUNT":                                                     185,
		"UPLOAD_USER_ISSUE_INFO_FOR_AGENTS":                                   186,
		"UPDATE_USER_EMPLOYMENT":                                              187,
		"CREATE_OR_UPDATE_SEGMENT_MAPPING":                                    188,
		"DEPOSIT_ADD_INTEREST_RATE":                                           189,
		"DEPOSIT_UPDATE_INTEREST_RATE":                                        190,
		"DEPOSIT_DELETE_INTEREST_RATE":                                        191,
		"GET_DOCKET_URL_WEALTH_ONBOARDING":                                    192,
		"UPDATE_EXCHANGER_OFFER_STATUS":                                       193,
		"PROFILE_EVALUATOR_ACTIONS":                                           194,
		"INCOME_OCCUPATION_DISCREPANCY_VERIFICATION":                          195,
		"CREATE_REFERRALS_SEGMENTED_COMPONENT":                                196,
		"UPDATE_REFERRALS_SEGMENTED_COMPONENT":                                197,
		"DELETE_REFERRALS_SEGMENTED_COMPONENT":                                198,
		"UPLOAD_RISK_CASES":                                                   199,
		"CREATE_SEGMENT":                                                      200,
		"RISK_TXN_CASE_MANUAL_REVIEW":                                         201,
		"REMOVE_RED_LIST_ENTRY":                                               202,
		"ADD_RED_LIST_ENTRY":                                                  203,
		"P2P_GET_INVESTOR_DASHBOARD":                                          204,
		"P2P_GET_INVESTMENT_SUMMARY":                                          205,
		"P2P_DOWNLOAD_RECON_FILE":                                             206,
		"GET_SIGNED_URL_WEALTH":                                               207,
		"CREDIT_CARD_UPDATE_CARD_REQUEST_STATUS":                              208,
		"USSTOCKS_REFRESH_STOCK_DETAILS":                                      209,
		"UPDATE_NUDGE_STATUS":                                                 210,
		"UPLOAD_AML_REPORT":                                                   211,
		"DEACTIVATE_DEVICE":                                                   212,
		"FEDERAL_LOAN_LIVENESS_REVIEW":                                        213,
		"CREATE_REWARD_OFFER_IN_BULK":                                         214,
		"FITTT_PUBLISH_SHARK_TANK_EVENT":                                      215,
		"CREATE_FOREX_RATE":                                                   216,
		"UPDATE_FOREX_RATE":                                                   217,
		"REPRIEVE_VKYC":                                                       218,
		"USSTOCKS_GROSS_SUMMARY":                                              219,
		"AML_UPDATE_FILE_GEN_STATUS":                                          220,
		"MF_DOWNLOAD_FILE_FROM_BUCKET":                                        221,
		"PERFORM_RISK_REVIEW_ACTION":                                          222,
		"FOREX_RATE_REPORT":                                                   223,
		"CURRENT_FOREX_RATE":                                                  224,
		"UPLOAD_WEALTH_DOCUMENT":                                              225,
		"UPDATE_P2P_VENDOR_RESPONSE_MATURITY_TRANSACTION_DAYS_TO_EXPIRE":      226,
		"GET_FOREX_RATES":                                                     227,
		"PRE_APPROVED_LOAN_LL_UPDATE_ENTITY_STATUS":                           228,
		"CREATE_TEMPORAL_SCHEDULE":                                            229,
		"ADD_HOME_PROMO_BANNER":                                               230,
		"USSTOCKS_ACCOUNT_STATEMENT":                                          231,
		"RETRY_MUTUAL_FUND_ORDER":                                             232,
		"RISK_CREATE_ALLOWED_ANNOTATION":                                      233,
		"RISK_BANK_ACTION_MANUAL_OVERRIDE":                                    234,
		"USSTOCKS_DOWNLOAD_FILE_FROM_BUCKET":                                  235,
		"CREATE_NUDGES_IN_BULK":                                               236,
		"VENDOR_ACCOUNT_PENNY_DROP":                                           237,
		"UPDATE_WEALTH_USER_INPUT_PENDING_DATA":                               238,
		"CREATE_OFFERS_IN_BULK":                                               239,
		"RESET_CELESTIAL_WORKFLOW_EXECUTION":                                  240,
		"CREATE_REFERRAL_NOTIFICATION_CONFIG":                                 241,
		"UPDATE_REFERRAL_NOTIFICATION_CONFIG_STATUS":                          242,
		"UPDATE_REFERRAL_NOTIFICATION_CONFIG_CONTENT":                         243,
		"DELETE_REFERRAL_NOTIFICATION_CONFIG":                                 244,
		"UPLOAD_LEA_COMPLAINTS":                                               245,
		"ADD_HOME_POP_UP_BANNER":                                              246,
		"DEPOSIT_LIST_ACCOUNTS_VENDOR":                                        247,
		"MARK_LOAN_REQUEST_CANCEL":                                            248,
		"IFT_GENERATE_AGGREGATED_TAX_REPORT":                                  249,
		"CREATE_OR_UPDATE_DYNAMIC_UI_ELEMENT_VARIANT":                         250,
		"UPDATE_DYNAMIC_UI_ELEMENT_EVALUATOR_CONFIG":                          251,
		"CREATE_CMS_PRODUCT":                                                  252,
		"CREATE_CMS_SKU":                                                      253,
		"CREATE_CMS_COUPON_IN_BULK":                                           254,
		"CREATE_HOME_LAYOUT":                                                  255,
		"CREATE_HOME_LAYOUT_SEGMENT_MAPPING":                                  256,
		"DELETE_HOME_LAYOUT":                                                  257,
		"DELETE_HOME_LAYOUT_SEGMENT_MAPPING":                                  258,
		"MARK_USERS_BY_ACQUISITION_INFO":                                      259,
		"GET_HOME_LAYOUT":                                                     260,
		"GET_HOME_LAYOUT_SEGMENT_MAPPING":                                     261,
		"ADD_INAPPHELP_FEEDBACK_ENGINE_SURVEY":                                262,
		"UPDATE_INAPPHELP_FEEDBACK_ENGINE_SURVEY":                             263,
		"ADD_INAPPHELP_FEEDBACK_ENGINE_QUESTION":                              264,
		"UPDATE_INAPPHELP_FEEDBACK_ENGINE_QUESTION":                           265,
		"MARK_CARD_DELIVERY_TRACKING_STATE_RECEIVED":                          266,
		"LOAN_UPDATE_LOAN_STEP_STATUS":                                        267,
		"USSTOCKS_CREATE_COLLECTION":                                          268,
		"USSTOCKS_UPDATE_COLLECTION":                                          269,
		"USSTOCKS_ADD_STOCK_TO_COLLECTION":                                    270,
		"USSTOCKS_UPDATE_STOCK_IN_COLLECTION":                                 271,
		"USSTOCKS_REMOVE_STOCK_FROM_COLLECTION":                               272,
		"UPDATE_SEGMENT":                                                      273,
		"ADD_EMPLOYERS":                                                       274,
		"IFT_REJECT_TRANSACTIONS_IN_OUTWARD_SWIFT_FILE":                       275,
		"WHITELIST_EMAIL_ID":                                                  276,
		"CREATE_KYC_AGENT":                                                    277,
		"DATA_EXTRACTION":                                                     278,
		"CREATE_ISSUE_CONFIG":                                                 279,
		"UPDATE_REWARD_OFFER":                                                 280,
		"GENERATE_CHATBOT_ACCESS_TOKEN":                                       281,
		"P2P_REGISTER_BANKING_DETAILS":                                        282,
		"UPLOAD_LEA_COMPLAINT_NARRATIONS":                                     283,
		"UPDATE_EMPLOYER_DETAILS":                                             284,
		"BULK_SETUP_REFERRAL_SEGMENTED_COMPONENTS":                            285,
		"UPLOAD_LEA_COMPLAINT_SOURCE_DETAILS":                                 286,
		"USER_COMMS_CONTROL":                                                  287,
		"CREDIT_CARD_UPDATE_CARD_REQUEST_STAGE_STATUS":                        288,
		"SIMULATE_USSTOCKS_OUTWARD_FUND_TRANSFER_NON_PROD":                    289,
		"CREATE_FAQ_CONTEXT_MAPPING":                                          290,
		"P2P_UPDATE_INVESTMENT_TRANSACTION":                                   291,
		"FETCH_ACCOUNT_STATUS":                                                292,
		"CREATE_HOME_SIMULATOR_LAYOUT":                                        293,
		"GET_WEALTH_ONBOARDING_DETAILS_BY_PAN":                                294,
		"CREATE_WATSON_TICKET_DETAILS":                                        295,
		"UPDATE_WATSON_TICKET_DETAILS":                                        296,
		"USSTOCKS_CREATE_BANK_RELATIONSHIP_WITH_BROKER":                       297,
		"LOANS_ABFL_ACTIONS":                                                  298,
		"TRIGGER_KYC_NAME_DOB_VALIDATION":                                     299,
		"ONBOARDING_TROUBLESHOOTING_DETAILS":                                  300,
		"CREATE_EVENT_CONFIG":                                                 301,
		"UPDATE_EVENT_CONFIG":                                                 302,
		"FAIL_BANK_CUSTOMER":                                                  303,
		"P2P_GET_CASH_LEDGER":                                                 304,
		"GET_MF_FOLIO_BALANCE":                                                305,
		"IFT_INITIATE_REFUND":                                                 306,
		"RAISE_MANUAL_SALARY_VERIFICATION_REQUESTS_IN_BULK":                   307,
		"SIMULATE_REWARD_GENERATION":                                          308,
		"UPDATE_SALARY_PROGRAM_REFERRALS_SEASON":                              309,
		"PROCESS_ITC_POINTS_HANDBACK_FILE":                                    310,
		"RISK_UPLOAD_DISPUTES":                                                311,
		"SEGMENT_METADATA_ACTION":                                             312,
		"SET_SEGMENT_METADATA_APPROVAL_STATUS":                                313,
		"OVERRIDE_PAYMENT_HEALTH_ENGINE_STATUS":                               314,
		"OVERRIDE_CSIS_HEALTH_ENGINE_STATUS":                                  315,
		"SOF_LIMIT_MANUAL_OVERRIDE":                                           316,
		"PASS_RISK_SCREENER_ATTEMPT":                                          317,
		"ADD_PIN_CODE_ENTRY":                                                  318,
		"EXTRACT_PAN_FROM_KRA_DOCKET":                                         319,
		"USSTOCKS_RESET_ONBOARDING_DATA":                                      320,
		"RISK_MANAGE_WHITELIST":                                               321,
		"USSTOCKS_ADD_INVESTOR_ADDRESS":                                       322,
		"CREATE_ACTIVITY_METADATA":                                            323,
		"TRIGGER_TXN_CATEGORISATION":                                          324,
		"GET_US_STOCK_ACCOUNT_ACTIVITIES_CSV":                                 325,
		"TRIGGER_UNSECURED_CC_RENEWAL_FEE_REVERSAL":                           326,
		"USSTOCKS_GENERATE_ADHOC_INWARD_REMITTANCE_FILES":                     327,
		"GENERATE_MODEL_RESPONSE_FOR_USERS_CX_QUERY":                          328,
		"USSTOCKS_REGENERATE_INWARD_FILE":                                     329,
		"MAP_DEBIT_CARD_FOREX_TXN":                                            330,
		"CREATE_JOURNEY":                                                      331,
		"UPDATE_JOURNEY":                                                      332,
		"UPDATE_ACTIVITY_METADATA":                                            333,
		"HANDLE_VKYC_CALL_STATE":                                              334,
		"UPLOAD_UNIFIED_LEA_COMPLAINTS":                                       335,
		"DELETE_SERVICE_REQUEST":                                              336,
		"FAIL_DEBIT_CARD_CREATION":                                            337,
		"USSTOCKS_TRADING_ACCOUNT_SUMMARY":                                    338,
		"CACHE_CONTACT_US_MODEL_RESPONSE":                                     339,
		"B2B_USERS_ONBOARDING_STATUS_TRACKING":                                340,
		"USSTOCKS_UPDATE_DETAILS":                                             341,
		"MF_DOWNLOAD_FILE_BY_VENDOR_ORDER_IDS":                                342,
		"STOCK_GUARDIAN_REDACT_CKYC_DOCUMENT":                                 343,
		"LEAD_MGMT_DOWNLOAD_FILE":                                             344,
		"DEPOSIT_UPDATE_STATE":                                                345,
		"STOCK_GUARDIAN_EXECUTE_EMI_MANDATE":                                  346,
		"GET_SAVINGS_ACCOUNT_CLOSURE_ELIGIBILITY_IN_BULK":                     347,
		"DELETE_KYC_AGENT":                                                    348,
		"SALARY_PROGRAM_DYNAMIC_UI_ELEMENT_UPDATE_OR_CREATE_VARIANT":          349,
		"SALARY_PROGRAM_DYNAMIC_UI_ELEMENT_UPDATE_OR_CREATE_EVALUATOR_CONFIG": 350,
		"PROCESS_NON_RESIDENT_CROSS_VALIDATION_MANUAL_REVIEW":                 351,
		"NR_ONBOARDING_PASSPORT_MANUAL_REVIEW":                                352,
		"ADD_STOCK_WITH_ISINS":                                                353,
		"STOCK_GUARDIAN_EXECUTE_DISBURSAL_RETRY":                              354,
		"UPLOAD_RISK_REDLIST":                                                 355,
		"SEND_ENACH_MANDATE_NOTIFICATION_CALLBACK":                            356,
		"B2B_USERS_ONBOARDING_STATUS_LIMITED_DETAILS":                         357,
		"UPDATE_LOANS_OUTCALL_TICKET":                                         358,
		"CREATE_SUGGESTED_ACTION_FOR_RULE":                                    359,
		"CREATE_RULE_REVIEW_TYPE_MAPPING":                                     360,
		"FEDERAL_ESCALATION_CREATION":                                         361,
		"B2B_UN_NAME_CHECK_FAILURE_EMAIL":                                     362,
		"REFRESH_USER_INFO_FROM_PARTNER_BANK":                                 363,
		"DELETE_USER_ASSETS":                                                  364,
		"UPLOAD_IMAGE_TO_EPIFI_ICONS_S3_BUCKET":                               365,
		"EXPIRE_LOEC":                                                         366,
		"STOCK_GUARDIAN_UPDATE_EMPLOYMENT_DETAILS":                            367,
		"FETCH_UTRS_FROM_LOG":                                                 368,
		"RESET_LOAN_REQUEST":                                                  369,
		"WEALTH_NETWORTH_INSIGHTS_GET_FILES_FROM_BUCKET":                      370,
	}
)

func (x DeveloperActions) Enum() *DeveloperActions {
	p := new(DeveloperActions)
	*p = x
	return p
}

func (x DeveloperActions) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeveloperActions) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_developer_actions_enums_proto_enumTypes[0].Descriptor()
}

func (DeveloperActions) Type() protoreflect.EnumType {
	return &file_api_cx_developer_actions_enums_proto_enumTypes[0]
}

func (x DeveloperActions) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeveloperActions.Descriptor instead.
func (DeveloperActions) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_developer_actions_enums_proto_rawDescGZIP(), []int{0}
}

// list of action response type
type ActionResponseType int32

const (
	ActionResponseType_ACTION_RESPONSE_TYPE_UNSPECIFIED ActionResponseType = 0
	// response will be shown as raw string on UI as result
	ActionResponseType_STRING ActionResponseType = 1
	// response json will be beautified and shown on UI
	ActionResponseType_JSON ActionResponseType = 2
	// when response contains a html table
	ActionResponseType_TABLE ActionResponseType = 3
	// downloadable url
	ActionResponseType_DOWNLOAD_URL ActionResponseType = 4
)

// Enum value maps for ActionResponseType.
var (
	ActionResponseType_name = map[int32]string{
		0: "ACTION_RESPONSE_TYPE_UNSPECIFIED",
		1: "STRING",
		2: "JSON",
		3: "TABLE",
		4: "DOWNLOAD_URL",
	}
	ActionResponseType_value = map[string]int32{
		"ACTION_RESPONSE_TYPE_UNSPECIFIED": 0,
		"STRING":                           1,
		"JSON":                             2,
		"TABLE":                            3,
		"DOWNLOAD_URL":                     4,
	}
)

func (x ActionResponseType) Enum() *ActionResponseType {
	p := new(ActionResponseType)
	*p = x
	return p
}

func (x ActionResponseType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ActionResponseType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_developer_actions_enums_proto_enumTypes[1].Descriptor()
}

func (ActionResponseType) Type() protoreflect.EnumType {
	return &file_api_cx_developer_actions_enums_proto_enumTypes[1]
}

func (x ActionResponseType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ActionResponseType.Descriptor instead.
func (ActionResponseType) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_developer_actions_enums_proto_rawDescGZIP(), []int{1}
}

var File_api_cx_developer_actions_enums_proto protoreflect.FileDescriptor

var file_api_cx_developer_actions_enums_proto_rawDesc = []byte{
	0x0a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70,
	0x65, 0x72, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x63, 0x78, 0x2e, 0x64, 0x65, 0x76, 0x65, 0x6c,
	0x6f, 0x70, 0x65, 0x72, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2a, 0xca, 0x62, 0x0a,
	0x10, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x21, 0x0a, 0x1d, 0x44, 0x45, 0x56, 0x45, 0x4c, 0x4f, 0x50, 0x45, 0x52, 0x5f, 0x41,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x52, 0x45, 0x54, 0x52, 0x59, 0x5f, 0x4c, 0x49,
	0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x52, 0x45, 0x54, 0x52,
	0x59, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x43, 0x55,
	0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x52, 0x45, 0x54, 0x52,
	0x59, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x10, 0x03, 0x12, 0x0e, 0x0a, 0x0a, 0x52, 0x45, 0x54, 0x52, 0x59, 0x5f, 0x45, 0x4b, 0x59, 0x43,
	0x10, 0x04, 0x12, 0x0e, 0x0a, 0x0a, 0x52, 0x45, 0x54, 0x52, 0x59, 0x5f, 0x43, 0x4b, 0x59, 0x43,
	0x10, 0x05, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x43, 0x41, 0x52,
	0x44, 0x10, 0x06, 0x12, 0x17, 0x0a, 0x13, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45,
	0x57, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x07, 0x12, 0x1e, 0x0a, 0x1a,
	0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x46,
	0x46, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x08, 0x12, 0x1e, 0x0a, 0x1a,
	0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x4c, 0x55, 0x43, 0x4b, 0x59, 0x5f, 0x44, 0x52, 0x41,
	0x57, 0x5f, 0x43, 0x41, 0x4d, 0x50, 0x41, 0x49, 0x47, 0x4e, 0x10, 0x09, 0x12, 0x15, 0x0a, 0x11,
	0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x4c, 0x55, 0x43, 0x4b, 0x59, 0x5f, 0x44, 0x52, 0x41,
	0x57, 0x10, 0x0a, 0x12, 0x11, 0x0a, 0x0d, 0x50, 0x55, 0x4c, 0x4c, 0x5f, 0x41, 0x50, 0x50, 0x5f,
	0x4c, 0x4f, 0x47, 0x53, 0x10, 0x0b, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x44, 0x44, 0x5f, 0x55, 0x53,
	0x45, 0x52, 0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x4d, 0x41, 0x50, 0x50, 0x49, 0x4e, 0x47,
	0x10, 0x0c, 0x12, 0x0f, 0x0a, 0x0b, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x5f, 0x55, 0x53, 0x45,
	0x52, 0x10, 0x0d, 0x12, 0x1e, 0x0a, 0x1a, 0x52, 0x45, 0x43, 0x4f, 0x56, 0x45, 0x52, 0x5f, 0x41,
	0x55, 0x54, 0x48, 0x5f, 0x46, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54,
	0x45, 0x10, 0x0e, 0x12, 0x20, 0x0a, 0x1c, 0x52, 0x45, 0x41, 0x4c, 0x4c, 0x4f, 0x57, 0x5f, 0x55,
	0x53, 0x45, 0x52, 0x5f, 0x54, 0x4f, 0x5f, 0x54, 0x52, 0x59, 0x5f, 0x57, 0x41, 0x49, 0x54, 0x4c,
	0x49, 0x53, 0x54, 0x10, 0x0f, 0x12, 0x1f, 0x0a, 0x1b, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f,
	0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x44, 0x49, 0x53,
	0x50, 0x4c, 0x41, 0x59, 0x10, 0x10, 0x12, 0x21, 0x0a, 0x1d, 0x54, 0x52, 0x49, 0x47, 0x47, 0x45,
	0x52, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x57, 0x41, 0x49, 0x54, 0x4c, 0x49, 0x53,
	0x54, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x11, 0x12, 0x1f, 0x0a, 0x1b, 0x54, 0x52, 0x49,
	0x47, 0x47, 0x45, 0x52, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x50, 0x49, 0x53, 0x5f,
	0x49, 0x4e, 0x44, 0x45, 0x58, 0x49, 0x4e, 0x47, 0x10, 0x12, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x52,
	0x45, 0x41, 0x54, 0x45, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x13, 0x12, 0x18, 0x0a, 0x14,
	0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x4c, 0x49, 0x53,
	0x54, 0x49, 0x4e, 0x47, 0x10, 0x14, 0x12, 0x18, 0x0a, 0x14, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45,
	0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x15,
	0x12, 0x18, 0x0a, 0x14, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52,
	0x5f, 0x4c, 0x49, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x16, 0x12, 0x1a, 0x0a, 0x16, 0x43, 0x52,
	0x45, 0x41, 0x54, 0x45, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x4e,
	0x54, 0x4f, 0x52, 0x59, 0x10, 0x17, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x44, 0x44, 0x5f, 0x4f, 0x46,
	0x46, 0x45, 0x52, 0x5f, 0x54, 0x4f, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x4e, 0x54, 0x4f, 0x52, 0x59,
	0x10, 0x18, 0x12, 0x1a, 0x0a, 0x16, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x5f, 0x4f, 0x46, 0x46,
	0x45, 0x52, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x4e, 0x54, 0x4f, 0x52, 0x59, 0x10, 0x19, 0x12, 0x17,
	0x0a, 0x13, 0x46, 0x4f, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x52, 0x49, 0x47, 0x47, 0x45, 0x52, 0x5f,
	0x52, 0x45, 0x43, 0x4f, 0x4e, 0x10, 0x1a, 0x12, 0x1c, 0x0a, 0x18, 0x54, 0x52, 0x49, 0x47, 0x47,
	0x45, 0x52, 0x5f, 0x47, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x53,
	0x59, 0x4e, 0x43, 0x10, 0x1b, 0x12, 0x18, 0x0a, 0x14, 0x55, 0x4e, 0x4c, 0x49, 0x4e, 0x4b, 0x5f,
	0x47, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x1c, 0x12,
	0x25, 0x0a, 0x21, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x5f, 0x57, 0x41, 0x49, 0x54, 0x4c, 0x49,
	0x53, 0x54, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x46, 0x52, 0x4f, 0x4d, 0x5f, 0x55, 0x53, 0x45,
	0x52, 0x5f, 0x49, 0x44, 0x10, 0x1d, 0x12, 0x24, 0x0a, 0x20, 0x4d, 0x41, 0x52, 0x4b, 0x5f, 0x57,
	0x41, 0x49, 0x54, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x53, 0x5f, 0x45, 0x41,
	0x52, 0x4c, 0x59, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x1e, 0x12, 0x1e, 0x0a, 0x1a,
	0x53, 0x45, 0x4e, 0x44, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x54, 0x4f, 0x5f, 0x45, 0x41,
	0x52, 0x4c, 0x59, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x1f, 0x12, 0x13, 0x0a, 0x0f,
	0x53, 0x59, 0x4e, 0x43, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x10,
	0x20, 0x12, 0x19, 0x0a, 0x15, 0x46, 0x49, 0x54, 0x54, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54,
	0x45, 0x5f, 0x4e, 0x45, 0x57, 0x5f, 0x52, 0x55, 0x4c, 0x45, 0x10, 0x21, 0x12, 0x15, 0x0a, 0x11,
	0x46, 0x49, 0x54, 0x54, 0x54, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x55, 0x4c,
	0x45, 0x10, 0x22, 0x12, 0x1e, 0x0a, 0x1a, 0x46, 0x49, 0x54, 0x54, 0x54, 0x5f, 0x47, 0x45, 0x54,
	0x5f, 0x52, 0x55, 0x4c, 0x45, 0x53, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e,
	0x54, 0x10, 0x23, 0x12, 0x1b, 0x0a, 0x17, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x4f, 0x4e,
	0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x10, 0x24,
	0x12, 0x1f, 0x0a, 0x1b, 0x53, 0x45, 0x4e, 0x44, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x53, 0x5f, 0x54,
	0x4f, 0x5f, 0x57, 0x41, 0x49, 0x54, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x10,
	0x25, 0x12, 0x18, 0x0a, 0x14, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x4f, 0x46, 0x46, 0x45,
	0x52, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x4c, 0x41, 0x59, 0x10, 0x26, 0x12, 0x15, 0x0a, 0x11, 0x53,
	0x45, 0x45, 0x44, 0x5f, 0x46, 0x49, 0x4e, 0x49, 0x54, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x53,
	0x10, 0x27, 0x12, 0x1f, 0x0a, 0x1b, 0x46, 0x49, 0x54, 0x54, 0x54, 0x5f, 0x49, 0x4e, 0x49, 0x54,
	0x49, 0x41, 0x54, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54,
	0x45, 0x10, 0x28, 0x12, 0x1f, 0x0a, 0x1b, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x5f,
	0x43, 0x41, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x53, 0x10, 0x29, 0x12, 0x18, 0x0a, 0x14, 0x4d, 0x41, 0x52, 0x4b, 0x5f, 0x4c, 0x49, 0x56,
	0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x45, 0x44, 0x10, 0x2b, 0x12, 0x15,
	0x0a, 0x11, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x50, 0x52, 0x45, 0x46, 0x45, 0x52, 0x45,
	0x4e, 0x43, 0x45, 0x10, 0x2c, 0x12, 0x25, 0x0a, 0x21, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f,
	0x53, 0x48, 0x49, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53,
	0x5f, 0x41, 0x54, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0x2d, 0x12, 0x1e, 0x0a, 0x1a,
	0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x48, 0x49, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x5f,
	0x50, 0x52, 0x45, 0x46, 0x45, 0x52, 0x45, 0x4e, 0x43, 0x45, 0x10, 0x2e, 0x12, 0x1d, 0x0a, 0x19,
	0x52, 0x45, 0x54, 0x52, 0x59, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f,
	0x52, 0x4d, 0x53, 0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x10, 0x2f, 0x12, 0x19, 0x0a, 0x15, 0x53,
	0x45, 0x4e, 0x44, 0x5f, 0x57, 0x45, 0x4c, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x57, 0x48, 0x41, 0x54,
	0x53, 0x41, 0x50, 0x50, 0x10, 0x30, 0x12, 0x1e, 0x0a, 0x1a, 0x53, 0x45, 0x4e, 0x44, 0x5f, 0x52,
	0x45, 0x57, 0x41, 0x52, 0x44, 0x53, 0x5f, 0x43, 0x41, 0x4d, 0x50, 0x41, 0x49, 0x47, 0x4e, 0x5f,
	0x43, 0x4f, 0x4d, 0x4d, 0x10, 0x31, 0x12, 0x13, 0x0a, 0x0f, 0x55, 0x4e, 0x52, 0x45, 0x44, 0x41,
	0x43, 0x54, 0x45, 0x44, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x10, 0x32, 0x12, 0x17, 0x0a, 0x13, 0x46,
	0x4f, 0x52, 0x43, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x4f, 0x52, 0x44,
	0x45, 0x52, 0x10, 0x33, 0x12, 0x1b, 0x0a, 0x17, 0x52, 0x45, 0x54, 0x52, 0x59, 0x5f, 0x52, 0x45,
	0x57, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x10,
	0x34, 0x12, 0x20, 0x0a, 0x1c, 0x47, 0x45, 0x54, 0x5f, 0x46, 0x49, 0x54, 0x5f, 0x54, 0x58, 0x4e,
	0x5f, 0x41, 0x47, 0x47, 0x52, 0x45, 0x47, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x45, 0x41, 0x52, 0x43,
	0x48, 0x10, 0x35, 0x12, 0x27, 0x0a, 0x1f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x53,
	0x45, 0x52, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x56, 0x4f, 0x4b, 0x45,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x10, 0x36, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x1c, 0x0a, 0x18,
	0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x50, 0x52, 0x4f, 0x46,
	0x49, 0x4c, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x10, 0x37, 0x12, 0x1e, 0x0a, 0x1a, 0x47, 0x45,
	0x4e, 0x45, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x38, 0x12, 0x17, 0x0a, 0x13, 0x4f, 0x4e,
	0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x4e, 0x41, 0x50, 0x53, 0x48, 0x4f,
	0x54, 0x10, 0x39, 0x12, 0x26, 0x0a, 0x22, 0x42, 0x41, 0x43, 0x4b, 0x46, 0x49, 0x4c, 0x4c, 0x5f,
	0x46, 0x52, 0x45, 0x53, 0x48, 0x44, 0x45, 0x53, 0x4b, 0x5f, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54,
	0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x43, 0x54, 0x53, 0x10, 0x3a, 0x12, 0x19, 0x0a, 0x15, 0x50,
	0x41, 0x53, 0x53, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53,
	0x54, 0x41, 0x47, 0x45, 0x10, 0x3b, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45,
	0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x47, 0x52,
	0x4f, 0x55, 0x50, 0x10, 0x3c, 0x12, 0x1c, 0x0a, 0x18, 0x52, 0x45, 0x53, 0x45, 0x54, 0x5f, 0x4b,
	0x59, 0x43, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x44, 0x4f, 0x42, 0x5f, 0x52, 0x45, 0x54, 0x52,
	0x59, 0x10, 0x3d, 0x12, 0x1a, 0x0a, 0x16, 0x52, 0x45, 0x54, 0x52, 0x59, 0x5f, 0x4f, 0x46, 0x46,
	0x45, 0x52, 0x5f, 0x52, 0x45, 0x44, 0x45, 0x4d, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x3e, 0x12,
	0x1b, 0x0a, 0x17, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x49, 0x4e, 0x47, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x10, 0x3f, 0x12, 0x19, 0x0a, 0x15,
	0x55, 0x4e, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x55, 0x4e,
	0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x10, 0x40, 0x12, 0x1f, 0x0a, 0x1b, 0x52, 0x45, 0x53, 0x45, 0x54,
	0x5f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4e, 0x41, 0x4d, 0x45,
	0x5f, 0x52, 0x45, 0x54, 0x52, 0x59, 0x10, 0x41, 0x12, 0x1d, 0x0a, 0x19, 0x46, 0x49, 0x54, 0x54,
	0x54, 0x5f, 0x53, 0x54, 0x4f, 0x50, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x53,
	0x5f, 0x4a, 0x4f, 0x42, 0x53, 0x10, 0x42, 0x12, 0x17, 0x0a, 0x13, 0x52, 0x45, 0x46, 0x52, 0x45,
	0x53, 0x48, 0x5f, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x43,
	0x12, 0x17, 0x0a, 0x13, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f,
	0x50, 0x49, 0x4e, 0x5f, 0x53, 0x45, 0x54, 0x10, 0x44, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x41, 0x49,
	0x53, 0x45, 0x5f, 0x41, 0x41, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x10, 0x45, 0x12,
	0x1f, 0x0a, 0x1b, 0x46, 0x4f, 0x52, 0x43, 0x45, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x52,
	0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x4e, 0x51, 0x55, 0x49, 0x52, 0x59, 0x10, 0x46,
	0x12, 0x19, 0x0a, 0x15, 0x4d, 0x41, 0x52, 0x4b, 0x5f, 0x46, 0x41, 0x43, 0x45, 0x4d, 0x41, 0x54,
	0x43, 0x48, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x45, 0x44, 0x10, 0x47, 0x12, 0x29, 0x0a, 0x25, 0x54,
	0x52, 0x49, 0x47, 0x47, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x53, 0x5f, 0x4d,
	0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x47, 0x49, 0x56, 0x45, 0x41, 0x57, 0x41, 0x59, 0x5f, 0x45,
	0x56, 0x45, 0x4e, 0x54, 0x10, 0x48, 0x12, 0x1d, 0x0a, 0x19, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45,
	0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x4d, 0x41, 0x50, 0x50,
	0x49, 0x4e, 0x47, 0x10, 0x49, 0x12, 0x1a, 0x0a, 0x16, 0x53, 0x59, 0x4e, 0x43, 0x5f, 0x57, 0x45,
	0x41, 0x4c, 0x54, 0x48, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x10,
	0x4a, 0x12, 0x1a, 0x0a, 0x16, 0x55, 0x4e, 0x4c, 0x4f, 0x43, 0x4b, 0x5f, 0x49, 0x4e, 0x5f, 0x41,
	0x50, 0x50, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c, 0x10, 0x4b, 0x12, 0x19, 0x0a,
	0x15, 0x54, 0x52, 0x49, 0x47, 0x47, 0x45, 0x52, 0x5f, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x43, 0x41,
	0x4c, 0x4c, 0x42, 0x41, 0x43, 0x4b, 0x10, 0x4c, 0x12, 0x22, 0x0a, 0x1e, 0x48, 0x41, 0x4e, 0x44,
	0x4c, 0x45, 0x5f, 0x53, 0x41, 0x56, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55,
	0x4e, 0x54, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x55, 0x52, 0x45, 0x10, 0x4d, 0x12, 0x23, 0x0a, 0x1f,
	0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x47, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x49, 0x4e, 0x53,
	0x49, 0x47, 0x48, 0x54, 0x53, 0x5f, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x41, 0x4e, 0x54, 0x53, 0x10,
	0x4e, 0x12, 0x2a, 0x0a, 0x26, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x47, 0x4d, 0x41, 0x49,
	0x4c, 0x5f, 0x49, 0x4e, 0x53, 0x49, 0x47, 0x48, 0x54, 0x53, 0x5f, 0x4d, 0x45, 0x52, 0x43, 0x48,
	0x41, 0x4e, 0x54, 0x5f, 0x51, 0x55, 0x45, 0x52, 0x49, 0x45, 0x53, 0x10, 0x4f, 0x12, 0x18, 0x0a,
	0x14, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x49, 0x4e, 0x41, 0x50, 0x50, 0x48, 0x45, 0x4c,
	0x50, 0x5f, 0x46, 0x41, 0x51, 0x10, 0x50, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x41, 0x5f, 0x43, 0x4f,
	0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x50, 0x44,
	0x41, 0x54, 0x45, 0x10, 0x51, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x41, 0x5f, 0x41, 0x43, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x4c, 0x49, 0x4e, 0x4b, 0x10, 0x52, 0x12, 0x15, 0x0a, 0x11,
	0x52, 0x45, 0x41, 0x43, 0x54, 0x49, 0x56, 0x41, 0x54, 0x45, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43,
	0x45, 0x10, 0x53, 0x12, 0x20, 0x0a, 0x1c, 0x46, 0x49, 0x54, 0x54, 0x54, 0x5f, 0x55, 0x50, 0x44,
	0x41, 0x54, 0x45, 0x5f, 0x52, 0x55, 0x4c, 0x45, 0x53, 0x5f, 0x57, 0x45, 0x49, 0x47, 0x48, 0x54,
	0x41, 0x47, 0x45, 0x10, 0x54, 0x12, 0x20, 0x0a, 0x1c, 0x52, 0x45, 0x4f, 0x50, 0x45, 0x4e, 0x5f,
	0x53, 0x41, 0x56, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f,
	0x49, 0x4e, 0x5f, 0x44, 0x42, 0x10, 0x55, 0x12, 0x23, 0x0a, 0x1f, 0x55, 0x50, 0x44, 0x41, 0x54,
	0x45, 0x5f, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44,
	0x49, 0x4e, 0x47, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x56, 0x12, 0x1a, 0x0a, 0x16,
	0x46, 0x49, 0x54, 0x54, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x48, 0x4f, 0x4d,
	0x45, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x57, 0x12, 0x1a, 0x0a, 0x16, 0x46, 0x49, 0x54, 0x54,
	0x54, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x43, 0x41,
	0x52, 0x44, 0x10, 0x58, 0x12, 0x1a, 0x0a, 0x16, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x50,
	0x41, 0x4e, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x10, 0x59,
	0x12, 0x16, 0x0a, 0x12, 0x41, 0x44, 0x44, 0x5f, 0x4d, 0x45, 0x44, 0x49, 0x41, 0x5f, 0x50, 0x4c,
	0x41, 0x59, 0x4c, 0x49, 0x53, 0x54, 0x10, 0x5a, 0x12, 0x19, 0x0a, 0x15, 0x55, 0x50, 0x44, 0x41,
	0x54, 0x45, 0x5f, 0x4d, 0x45, 0x44, 0x49, 0x41, 0x5f, 0x50, 0x4c, 0x41, 0x59, 0x4c, 0x49, 0x53,
	0x54, 0x10, 0x5b, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x44, 0x44, 0x5f, 0x4d, 0x45, 0x44, 0x49, 0x41,
	0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x4f, 0x52, 0x59, 0x10, 0x5c,
	0x12, 0x1e, 0x0a, 0x1a, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x4d, 0x45, 0x44, 0x49, 0x41,
	0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x4f, 0x52, 0x59, 0x10, 0x5d,
	0x12, 0x19, 0x0a, 0x15, 0x46, 0x49, 0x54, 0x54, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45,
	0x5f, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x10, 0x5e, 0x12, 0x2c, 0x0a, 0x28, 0x41,
	0x44, 0x44, 0x5f, 0x55, 0x49, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x58, 0x54, 0x5f, 0x54, 0x4f,
	0x5f, 0x4d, 0x45, 0x44, 0x49, 0x41, 0x5f, 0x50, 0x4c, 0x41, 0x59, 0x4c, 0x49, 0x53, 0x54, 0x5f,
	0x4d, 0x41, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x10, 0x5f, 0x12, 0x2f, 0x0a, 0x2b, 0x44, 0x45, 0x4c,
	0x45, 0x54, 0x45, 0x5f, 0x55, 0x49, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x58, 0x54, 0x5f, 0x54,
	0x4f, 0x5f, 0x4d, 0x45, 0x44, 0x49, 0x41, 0x5f, 0x50, 0x4c, 0x41, 0x59, 0x4c, 0x49, 0x53, 0x54,
	0x5f, 0x4d, 0x41, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x10, 0x60, 0x12, 0x2f, 0x0a, 0x2b, 0x41, 0x44,
	0x44, 0x5f, 0x4d, 0x45, 0x44, 0x49, 0x41, 0x5f, 0x50, 0x4c, 0x41, 0x59, 0x4c, 0x49, 0x53, 0x54,
	0x5f, 0x54, 0x4f, 0x5f, 0x4d, 0x45, 0x44, 0x49, 0x41, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e,
	0x54, 0x5f, 0x4d, 0x41, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x10, 0x61, 0x12, 0x32, 0x0a, 0x2e, 0x44,
	0x45, 0x4c, 0x45, 0x54, 0x45, 0x5f, 0x4d, 0x45, 0x44, 0x49, 0x41, 0x5f, 0x50, 0x4c, 0x41, 0x59,
	0x4c, 0x49, 0x53, 0x54, 0x5f, 0x54, 0x4f, 0x5f, 0x4d, 0x45, 0x44, 0x49, 0x41, 0x5f, 0x43, 0x4f,
	0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x41, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x10, 0x62, 0x12,
	0x21, 0x0a, 0x1d, 0x41, 0x44, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x4d, 0x49,
	0x53, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x4d, 0x45, 0x54, 0x41, 0x5f, 0x44, 0x41, 0x54, 0x41,
	0x10, 0x63, 0x12, 0x29, 0x0a, 0x25, 0x46, 0x49, 0x54, 0x54, 0x54, 0x5f, 0x42, 0x55, 0x4c, 0x4b,
	0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x55, 0x42, 0x53, 0x43, 0x52, 0x49, 0x50,
	0x54, 0x49, 0x4f, 0x4e, 0x53, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x10, 0x64, 0x12, 0x16, 0x0a,
	0x12, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x4d, 0x55, 0x54, 0x55, 0x41, 0x4c, 0x5f, 0x46,
	0x55, 0x4e, 0x44, 0x10, 0x67, 0x12, 0x16, 0x0a, 0x12, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f,
	0x4d, 0x55, 0x54, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x10, 0x66, 0x12, 0x28, 0x0a,
	0x24, 0x4d, 0x55, 0x54, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x55, 0x50, 0x4c,
	0x4f, 0x41, 0x44, 0x5f, 0x52, 0x45, 0x56, 0x45, 0x52, 0x53, 0x45, 0x5f, 0x46, 0x45, 0x45, 0x44,
	0x5f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x52, 0x45, 0x50, 0x4c, 0x41,
	0x59, 0x5f, 0x41, 0x41, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x45, 0x56, 0x45,
	0x4e, 0x54, 0x53, 0x10, 0x68, 0x12, 0x1a, 0x0a, 0x16, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f,
	0x45, 0x58, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x52, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10,
	0x69, 0x12, 0x22, 0x0a, 0x1e, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x58, 0x43, 0x48,
	0x41, 0x4e, 0x47, 0x45, 0x52, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x4c, 0x49, 0x53, 0x54,
	0x49, 0x4e, 0x47, 0x10, 0x6a, 0x12, 0x22, 0x0a, 0x1e, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f,
	0x45, 0x58, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x52, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f,
	0x44, 0x49, 0x53, 0x50, 0x4c, 0x41, 0x59, 0x10, 0x6b, 0x12, 0x22, 0x0a, 0x1e, 0x55, 0x50, 0x44,
	0x41, 0x54, 0x45, 0x5f, 0x45, 0x58, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x52, 0x5f, 0x4f, 0x46,
	0x46, 0x45, 0x52, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x6c, 0x12, 0x22, 0x0a,
	0x1e, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x5f, 0x45, 0x58, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45,
	0x52, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x10,
	0x6d, 0x12, 0x18, 0x0a, 0x14, 0x52, 0x45, 0x50, 0x4c, 0x41, 0x59, 0x5f, 0x41, 0x41, 0x5f, 0x54,
	0x58, 0x4e, 0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x53, 0x10, 0x6e, 0x12, 0x29, 0x0a, 0x25, 0x46,
	0x49, 0x54, 0x54, 0x54, 0x5f, 0x41, 0x52, 0x43, 0x48, 0x49, 0x56, 0x45, 0x5f, 0x52, 0x55, 0x4c,
	0x45, 0x53, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x53, 0x55, 0x42, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54,
	0x49, 0x4f, 0x4e, 0x53, 0x10, 0x6f, 0x12, 0x21, 0x0a, 0x1d, 0x46, 0x4f, 0x52, 0x43, 0x45, 0x5f,
	0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x10, 0x70, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x45, 0x41,
	0x52, 0x43, 0x48, 0x5f, 0x50, 0x41, 0x52, 0x53, 0x45, 0x5f, 0x51, 0x55, 0x45, 0x52, 0x59, 0x5f,
	0x42, 0x41, 0x53, 0x45, 0x10, 0x71, 0x12, 0x2a, 0x0a, 0x26, 0x4d, 0x41, 0x52, 0x4b, 0x5f, 0x57,
	0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47,
	0x5f, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x45, 0x44,
	0x10, 0x72, 0x12, 0x2b, 0x0a, 0x27, 0x4d, 0x55, 0x54, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e,
	0x44, 0x5f, 0x44, 0x45, 0x41, 0x43, 0x54, 0x49, 0x56, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x4e, 0x54,
	0x49, 0x54, 0x59, 0x5f, 0x46, 0x52, 0x4f, 0x4d, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x73, 0x12,
	0x1b, 0x0a, 0x17, 0x46, 0x49, 0x54, 0x54, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f,
	0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x74, 0x12, 0x1b, 0x0a, 0x17,
	0x46, 0x49, 0x54, 0x54, 0x54, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x43, 0x4f, 0x4c,
	0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x75, 0x12, 0x2b, 0x0a, 0x27, 0x4d, 0x41, 0x52,
	0x4b, 0x5f, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44,
	0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x44, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x41,
	0x53, 0x53, 0x45, 0x44, 0x10, 0x76, 0x12, 0x23, 0x0a, 0x1f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45,
	0x5f, 0x4d, 0x55, 0x54, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x4f, 0x52, 0x44,
	0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x77, 0x12, 0x27, 0x0a, 0x23, 0x54,
	0x52, 0x49, 0x47, 0x47, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x43, 0x55, 0x52, 0x52, 0x49, 0x4e, 0x47,
	0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0x78, 0x12, 0x28, 0x0a, 0x24, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x4d,
	0x41, 0x52, 0x4b, 0x45, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x43, 0x41, 0x4d, 0x50, 0x41, 0x49, 0x47,
	0x4e, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x53, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x10, 0x79, 0x12, 0x28,
	0x0a, 0x24, 0x4d, 0x41, 0x52, 0x4b, 0x5f, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x4f, 0x4e,
	0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x59, 0x5f,
	0x50, 0x41, 0x53, 0x53, 0x45, 0x44, 0x10, 0x7a, 0x12, 0x2a, 0x0a, 0x26, 0x4d, 0x55, 0x54, 0x55,
	0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44,
	0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x4d, 0x49, 0x53, 0x5f, 0x52, 0x45, 0x50, 0x4f,
	0x52, 0x54, 0x10, 0x7b, 0x12, 0x25, 0x0a, 0x21, 0x41, 0x44, 0x44, 0x5f, 0x49, 0x4e, 0x5f, 0x41,
	0x50, 0x50, 0x5f, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x45, 0x44, 0x5f, 0x43, 0x4f, 0x4d, 0x4d,
	0x53, 0x5f, 0x45, 0x4c, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x7c, 0x12, 0x28, 0x0a, 0x24, 0x55,
	0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x49, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x54, 0x41, 0x52,
	0x47, 0x45, 0x54, 0x45, 0x44, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x53, 0x5f, 0x45, 0x4c, 0x45, 0x4d,
	0x45, 0x4e, 0x54, 0x10, 0x7d, 0x12, 0x25, 0x0a, 0x21, 0x41, 0x44, 0x44, 0x5f, 0x49, 0x4e, 0x5f,
	0x41, 0x50, 0x50, 0x5f, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x45, 0x44, 0x5f, 0x43, 0x4f, 0x4d,
	0x4d, 0x53, 0x5f, 0x4d, 0x41, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x10, 0x7e, 0x12, 0x28, 0x0a, 0x24,
	0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x5f, 0x49, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x54, 0x41,
	0x52, 0x47, 0x45, 0x54, 0x45, 0x44, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x53, 0x5f, 0x4d, 0x41, 0x50,
	0x50, 0x49, 0x4e, 0x47, 0x10, 0x7f, 0x12, 0x21, 0x0a, 0x1c, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45,
	0x5f, 0x45, 0x58, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x52, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52,
	0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x10, 0x80, 0x01, 0x12, 0x19, 0x0a, 0x14, 0x4d, 0x46, 0x5f,
	0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x4f, 0x50, 0x53, 0x5f, 0x46, 0x49, 0x4c,
	0x45, 0x10, 0x81, 0x01, 0x12, 0x21, 0x0a, 0x1c, 0x4d, 0x46, 0x5f, 0x52, 0x45, 0x5f, 0x54, 0x52,
	0x49, 0x47, 0x47, 0x45, 0x52, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x53,
	0x49, 0x54, 0x45, 0x53, 0x10, 0x82, 0x01, 0x12, 0x1a, 0x0a, 0x15, 0x4d, 0x41, 0x4e, 0x55, 0x41,
	0x4c, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x55, 0x4e, 0x53, 0x55, 0x53, 0x50, 0x45, 0x4e, 0x44,
	0x10, 0x83, 0x01, 0x12, 0x1d, 0x0a, 0x18, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x49, 0x4e,
	0x53, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x46, 0x52, 0x41, 0x4d, 0x45, 0x57, 0x4f, 0x52, 0x4b, 0x10,
	0x84, 0x01, 0x12, 0x1b, 0x0a, 0x16, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x49, 0x4e, 0x53,
	0x49, 0x47, 0x48, 0x54, 0x5f, 0x53, 0x45, 0x47, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x85, 0x01, 0x12,
	0x24, 0x0a, 0x1f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x49, 0x4e, 0x53, 0x49, 0x47, 0x48,
	0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41,
	0x54, 0x45, 0x10, 0x86, 0x01, 0x12, 0x1d, 0x0a, 0x18, 0x4d, 0x46, 0x5f, 0x55, 0x50, 0x4c, 0x4f,
	0x41, 0x44, 0x5f, 0x43, 0x41, 0x54, 0x41, 0x4c, 0x4f, 0x47, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54,
	0x45, 0x10, 0x87, 0x01, 0x12, 0x1e, 0x0a, 0x19, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x4f,
	0x46, 0x46, 0x45, 0x52, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x4c, 0x41, 0x59, 0x5f, 0x52, 0x41, 0x4e,
	0x4b, 0x10, 0x88, 0x01, 0x12, 0x22, 0x0a, 0x1d, 0x4d, 0x46, 0x5f, 0x55, 0x50, 0x4c, 0x4f, 0x41,
	0x44, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x4d, 0x49, 0x53, 0x5f, 0x4e, 0x4f, 0x4e,
	0x5f, 0x50, 0x52, 0x4f, 0x44, 0x10, 0x89, 0x01, 0x12, 0x25, 0x0a, 0x20, 0x43, 0x52, 0x45, 0x41,
	0x54, 0x45, 0x5f, 0x45, 0x58, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x52, 0x5f, 0x4f, 0x46, 0x46,
	0x45, 0x52, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x4e, 0x54, 0x4f, 0x52, 0x59, 0x10, 0x8a, 0x01, 0x12,
	0x25, 0x0a, 0x20, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44,
	0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x4c, 0x41, 0x59, 0x5f, 0x52,
	0x41, 0x4e, 0x4b, 0x10, 0x8b, 0x01, 0x12, 0x1e, 0x0a, 0x19, 0x41, 0x44, 0x44, 0x5f, 0x55, 0x53,
	0x45, 0x52, 0x5f, 0x54, 0x4f, 0x5f, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x50, 0x52, 0x49, 0x4f, 0x52,
	0x49, 0x54, 0x59, 0x10, 0x8c, 0x01, 0x12, 0x14, 0x0a, 0x0f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45,
	0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x44, 0x4f, 0x42, 0x10, 0x8d, 0x01, 0x12, 0x2d, 0x0a, 0x28,
	0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x50, 0x32, 0x50, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53,
	0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x8e, 0x01, 0x12, 0x16, 0x0a, 0x11, 0x55,
	0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x50, 0x48, 0x4f, 0x54, 0x4f,
	0x10, 0x8f, 0x01, 0x12, 0x2f, 0x0a, 0x2a, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x50, 0x32,
	0x50, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4f, 0x52, 0x5f, 0x54, 0x4f, 0x54, 0x41, 0x4c,
	0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x10, 0x90, 0x01, 0x12, 0x19, 0x0a, 0x14, 0x4d, 0x46, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54,
	0x45, 0x5f, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x91, 0x01, 0x12,
	0x19, 0x0a, 0x14, 0x4d, 0x46, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x43, 0x4f, 0x4c,
	0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x92, 0x01, 0x12, 0x1e, 0x0a, 0x19, 0x4d, 0x46,
	0x5f, 0x41, 0x44, 0x44, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x54, 0x4f, 0x5f, 0x43, 0x4f, 0x4c,
	0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x93, 0x01, 0x12, 0x21, 0x0a, 0x1c, 0x4d, 0x46,
	0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x49, 0x4e, 0x5f,
	0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x94, 0x01, 0x12, 0x24, 0x0a,
	0x1f, 0x4d, 0x46, 0x5f, 0x52, 0x45, 0x4d, 0x4f, 0x56, 0x45, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53,
	0x5f, 0x46, 0x52, 0x4f, 0x4d, 0x5f, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0x95, 0x01, 0x12, 0x21, 0x0a, 0x1c, 0x4d, 0x46, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53,
	0x53, 0x5f, 0x52, 0x45, 0x56, 0x45, 0x52, 0x53, 0x45, 0x5f, 0x46, 0x45, 0x45, 0x44, 0x5f, 0x46,
	0x49, 0x4c, 0x45, 0x10, 0x96, 0x01, 0x12, 0x2f, 0x0a, 0x2a, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45,
	0x5f, 0x50, 0x32, 0x50, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x52, 0x45, 0x53, 0x50,
	0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x41, 0x4c, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x10, 0x97, 0x01, 0x12, 0x1a, 0x0a, 0x15, 0x50, 0x48, 0x59, 0x53, 0x49,
	0x43, 0x41, 0x4c, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x10, 0x98, 0x01, 0x12, 0x28, 0x0a, 0x23, 0x49, 0x4e, 0x43, 0x52, 0x45, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x45, 0x58, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x52, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52,
	0x5f, 0x49, 0x4e, 0x56, 0x45, 0x4e, 0x54, 0x4f, 0x52, 0x59, 0x10, 0x99, 0x01, 0x12, 0x1c, 0x0a,
	0x17, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c,
	0x53, 0x5f, 0x53, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x10, 0x9a, 0x01, 0x12, 0x1c, 0x0a, 0x17, 0x55,
	0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c, 0x53, 0x5f,
	0x53, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x10, 0x9b, 0x01, 0x12, 0x13, 0x0a, 0x0e, 0x44, 0x45, 0x4c,
	0x45, 0x54, 0x45, 0x5f, 0x53, 0x45, 0x47, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x9c, 0x01, 0x12, 0x26,
	0x0a, 0x21, 0x4d, 0x41, 0x52, 0x4b, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x4c,
	0x45, 0x5f, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44,
	0x49, 0x4e, 0x47, 0x10, 0x9d, 0x01, 0x12, 0x15, 0x0a, 0x10, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x5f, 0x4e, 0x45, 0x57, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x9e, 0x01, 0x12, 0x19, 0x0a,
	0x14, 0x54, 0x52, 0x49, 0x47, 0x47, 0x45, 0x52, 0x5f, 0x56, 0x50, 0x41, 0x5f, 0x43, 0x52, 0x45,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x9f, 0x01, 0x12, 0x25, 0x0a, 0x20, 0x41, 0x44, 0x44, 0x5f,
	0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x52, 0x4f, 0x55, 0x54,
	0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x41, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x53, 0x10, 0xa0, 0x01, 0x12,
	0x1b, 0x0a, 0x16, 0x54, 0x52, 0x49, 0x47, 0x47, 0x45, 0x52, 0x5f, 0x53, 0x45, 0x47, 0x4d, 0x45,
	0x4e, 0x54, 0x5f, 0x45, 0x58, 0x50, 0x4f, 0x52, 0x54, 0x10, 0xa1, 0x01, 0x12, 0x16, 0x0a, 0x11,
	0x4d, 0x46, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x4e, 0x43, 0x49, 0x4c, 0x49, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0xa2, 0x01, 0x12, 0x29, 0x0a, 0x24, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x54,
	0x49, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x54, 0x52,
	0x41, 0x4e, 0x53, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xa3, 0x01, 0x12,
	0x29, 0x0a, 0x24, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54,
	0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x4f,
	0x52, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xa4, 0x01, 0x12, 0x29, 0x0a, 0x24, 0x44, 0x45,
	0x4c, 0x45, 0x54, 0x45, 0x5f, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x44, 0x45, 0x54, 0x41,
	0x49, 0x4c, 0x53, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0xa5, 0x01, 0x12, 0x1c, 0x0a, 0x17, 0x44, 0x45, 0x45, 0x50, 0x4c, 0x49, 0x4e,
	0x4b, 0x5f, 0x42, 0x41, 0x53, 0x45, 0x36, 0x34, 0x5f, 0x45, 0x4e, 0x43, 0x4f, 0x44, 0x45, 0x52,
	0x10, 0xa6, 0x01, 0x12, 0x24, 0x0a, 0x1f, 0x50, 0x52, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f,
	0x56, 0x45, 0x44, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f,
	0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x10, 0xa7, 0x01, 0x12, 0x23, 0x0a, 0x1e, 0x50, 0x52, 0x45,
	0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x44, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x43,
	0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0xa8, 0x01, 0x12, 0x29,
	0x0a, 0x24, 0x50, 0x52, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x44, 0x5f, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0xa9, 0x01, 0x12, 0x20, 0x0a, 0x1b, 0x43, 0x41, 0x54,
	0x45, 0x47, 0x4f, 0x52, 0x49, 0x53, 0x45, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x45, 0x52,
	0x5f, 0x44, 0x4f, 0x4d, 0x41, 0x49, 0x4e, 0x53, 0x10, 0xaa, 0x01, 0x12, 0x2b, 0x0a, 0x26, 0x43,
	0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x50, 0x52, 0x4f,
	0x47, 0x52, 0x41, 0x4d, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c, 0x53, 0x5f, 0x53,
	0x45, 0x41, 0x53, 0x4f, 0x4e, 0x10, 0xab, 0x01, 0x12, 0x16, 0x0a, 0x11, 0x53, 0x54, 0x41, 0x52,
	0x54, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xac, 0x01,
	0x12, 0x11, 0x0a, 0x0c, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x4e, 0x55, 0x44, 0x47, 0x45,
	0x10, 0xad, 0x01, 0x12, 0x0f, 0x0a, 0x0a, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x4e, 0x55, 0x44, 0x47,
	0x45, 0x10, 0xae, 0x01, 0x12, 0x1e, 0x0a, 0x19, 0x53, 0x41, 0x56, 0x49, 0x4e, 0x47, 0x53, 0x5f,
	0x52, 0x49, 0x53, 0x4b, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x53, 0x10, 0xaf, 0x01, 0x12, 0x1c, 0x0a, 0x17, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x55,
	0x53, 0x45, 0x52, 0x5f, 0x46, 0x41, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x10,
	0xb0, 0x01, 0x12, 0x2e, 0x0a, 0x29, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x41, 0x43, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x52, 0x45, 0x45, 0x5a, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x53, 0x49, 0x4d, 0x55, 0x4c, 0x41, 0x54, 0x4f, 0x52, 0x10,
	0xb1, 0x01, 0x12, 0x36, 0x0a, 0x31, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45,
	0x52, 0x5f, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x4c, 0x52, 0x53, 0x5f, 0x43, 0x48, 0x45,
	0x43, 0x4b, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0xb2, 0x01, 0x12, 0x3b, 0x0a, 0x36, 0x49, 0x4e,
	0x54, 0x45, 0x52, 0x4e, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44,
	0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45, 0x52, 0x5f, 0x41, 0x43, 0x4b, 0x4e, 0x4f, 0x57,
	0x4c, 0x45, 0x44, 0x47, 0x45, 0x5f, 0x53, 0x57, 0x49, 0x46, 0x54, 0x5f, 0x54, 0x52, 0x41, 0x4e,
	0x53, 0x46, 0x45, 0x52, 0x10, 0xb3, 0x01, 0x12, 0x41, 0x0a, 0x3c, 0x49, 0x4e, 0x54, 0x45, 0x52,
	0x4e, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x54, 0x52,
	0x41, 0x4e, 0x53, 0x46, 0x45, 0x52, 0x5f, 0x41, 0x43, 0x4b, 0x4e, 0x4f, 0x57, 0x4c, 0x45, 0x44,
	0x47, 0x45, 0x5f, 0x49, 0x4e, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x54,
	0x52, 0x41, 0x4e, 0x53, 0x46, 0x45, 0x52, 0x10, 0xb4, 0x01, 0x12, 0x20, 0x0a, 0x1b, 0x50, 0x41,
	0x59, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x4c, 0x52, 0x53, 0x5f, 0x43,
	0x48, 0x45, 0x43, 0x4b, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0xb5, 0x01, 0x12, 0x31, 0x0a, 0x2c,
	0x46, 0x4f, 0x52, 0x45, 0x49, 0x47, 0x4e, 0x5f, 0x52, 0x45, 0x4d, 0x49, 0x54, 0x54, 0x41, 0x4e,
	0x43, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x49, 0x4e, 0x57, 0x41, 0x52,
	0x44, 0x5f, 0x52, 0x45, 0x4d, 0x49, 0x54, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x10, 0xb6, 0x01, 0x12,
	0x21, 0x0a, 0x1c, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x5f, 0x46, 0x52, 0x45, 0x45, 0x5a, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10,
	0xb7, 0x01, 0x12, 0x14, 0x0a, 0x0f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x44, 0x49, 0x53,
	0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0xb8, 0x01, 0x12, 0x14, 0x0a, 0x0f, 0x44, 0x45, 0x4c, 0x45,
	0x54, 0x45, 0x5f, 0x44, 0x49, 0x53, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0xb9, 0x01, 0x12, 0x26,
	0x0a, 0x21, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x49, 0x53,
	0x53, 0x55, 0x45, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x41, 0x47, 0x45,
	0x4e, 0x54, 0x53, 0x10, 0xba, 0x01, 0x12, 0x1b, 0x0a, 0x16, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45,
	0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x45, 0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x4d, 0x45, 0x4e, 0x54,
	0x10, 0xbb, 0x01, 0x12, 0x25, 0x0a, 0x20, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x4f, 0x52,
	0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x45, 0x47, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x4d, 0x41, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x10, 0xbc, 0x01, 0x12, 0x1e, 0x0a, 0x19, 0x44, 0x45,
	0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x45,
	0x53, 0x54, 0x5f, 0x52, 0x41, 0x54, 0x45, 0x10, 0xbd, 0x01, 0x12, 0x21, 0x0a, 0x1c, 0x44, 0x45,
	0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x49, 0x4e, 0x54,
	0x45, 0x52, 0x45, 0x53, 0x54, 0x5f, 0x52, 0x41, 0x54, 0x45, 0x10, 0xbe, 0x01, 0x12, 0x21, 0x0a,
	0x1c, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x5f,
	0x49, 0x4e, 0x54, 0x45, 0x52, 0x45, 0x53, 0x54, 0x5f, 0x52, 0x41, 0x54, 0x45, 0x10, 0xbf, 0x01,
	0x12, 0x29, 0x0a, 0x20, 0x47, 0x45, 0x54, 0x5f, 0x44, 0x4f, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x55,
	0x52, 0x4c, 0x5f, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52,
	0x44, 0x49, 0x4e, 0x47, 0x10, 0xc0, 0x01, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x22, 0x0a, 0x1d, 0x55,
	0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x58, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x52, 0x5f,
	0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0xc1, 0x01, 0x12,
	0x1e, 0x0a, 0x19, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x45, 0x56, 0x41, 0x4c, 0x55,
	0x41, 0x54, 0x4f, 0x52, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0xc2, 0x01, 0x12,
	0x2f, 0x0a, 0x2a, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x4f, 0x43, 0x43, 0x55, 0x50, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x49, 0x53, 0x43, 0x52, 0x45, 0x50, 0x41, 0x4e, 0x43, 0x59,
	0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xc3, 0x01,
	0x12, 0x29, 0x0a, 0x24, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52,
	0x52, 0x41, 0x4c, 0x53, 0x5f, 0x53, 0x45, 0x47, 0x4d, 0x45, 0x4e, 0x54, 0x45, 0x44, 0x5f, 0x43,
	0x4f, 0x4d, 0x50, 0x4f, 0x4e, 0x45, 0x4e, 0x54, 0x10, 0xc4, 0x01, 0x12, 0x29, 0x0a, 0x24, 0x55,
	0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c, 0x53, 0x5f,
	0x53, 0x45, 0x47, 0x4d, 0x45, 0x4e, 0x54, 0x45, 0x44, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4f, 0x4e,
	0x45, 0x4e, 0x54, 0x10, 0xc5, 0x01, 0x12, 0x29, 0x0a, 0x24, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45,
	0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c, 0x53, 0x5f, 0x53, 0x45, 0x47, 0x4d, 0x45,
	0x4e, 0x54, 0x45, 0x44, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4f, 0x4e, 0x45, 0x4e, 0x54, 0x10, 0xc6,
	0x01, 0x12, 0x16, 0x0a, 0x11, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x52, 0x49, 0x53, 0x4b,
	0x5f, 0x43, 0x41, 0x53, 0x45, 0x53, 0x10, 0xc7, 0x01, 0x12, 0x13, 0x0a, 0x0e, 0x43, 0x52, 0x45,
	0x41, 0x54, 0x45, 0x5f, 0x53, 0x45, 0x47, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0xc8, 0x01, 0x12, 0x20,
	0x0a, 0x1b, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x54, 0x58, 0x4e, 0x5f, 0x43, 0x41, 0x53, 0x45, 0x5f,
	0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x10, 0xc9, 0x01,
	0x12, 0x1a, 0x0a, 0x15, 0x52, 0x45, 0x4d, 0x4f, 0x56, 0x45, 0x5f, 0x52, 0x45, 0x44, 0x5f, 0x4c,
	0x49, 0x53, 0x54, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x10, 0xca, 0x01, 0x12, 0x17, 0x0a, 0x12,
	0x41, 0x44, 0x44, 0x5f, 0x52, 0x45, 0x44, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x45, 0x4e, 0x54,
	0x52, 0x59, 0x10, 0xcb, 0x01, 0x12, 0x1f, 0x0a, 0x1a, 0x50, 0x32, 0x50, 0x5f, 0x47, 0x45, 0x54,
	0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4f, 0x52, 0x5f, 0x44, 0x41, 0x53, 0x48, 0x42, 0x4f,
	0x41, 0x52, 0x44, 0x10, 0xcc, 0x01, 0x12, 0x1f, 0x0a, 0x1a, 0x50, 0x32, 0x50, 0x5f, 0x47, 0x45,
	0x54, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x55, 0x4d,
	0x4d, 0x41, 0x52, 0x59, 0x10, 0xcd, 0x01, 0x12, 0x1c, 0x0a, 0x17, 0x50, 0x32, 0x50, 0x5f, 0x44,
	0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x4e, 0x5f, 0x46, 0x49,
	0x4c, 0x45, 0x10, 0xce, 0x01, 0x12, 0x1a, 0x0a, 0x15, 0x47, 0x45, 0x54, 0x5f, 0x53, 0x49, 0x47,
	0x4e, 0x45, 0x44, 0x5f, 0x55, 0x52, 0x4c, 0x5f, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x10, 0xcf,
	0x01, 0x12, 0x2b, 0x0a, 0x26, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0xd0, 0x01, 0x12, 0x23,
	0x0a, 0x1e, 0x55, 0x53, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x52, 0x45, 0x46, 0x52, 0x45,
	0x53, 0x48, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53,
	0x10, 0xd1, 0x01, 0x12, 0x18, 0x0a, 0x13, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x4e, 0x55,
	0x44, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0xd2, 0x01, 0x12, 0x16, 0x0a,
	0x11, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x41, 0x4d, 0x4c, 0x5f, 0x52, 0x45, 0x50, 0x4f,
	0x52, 0x54, 0x10, 0xd3, 0x01, 0x12, 0x16, 0x0a, 0x11, 0x44, 0x45, 0x41, 0x43, 0x54, 0x49, 0x56,
	0x41, 0x54, 0x45, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x10, 0xd4, 0x01, 0x12, 0x21, 0x0a,
	0x1c, 0x46, 0x45, 0x44, 0x45, 0x52, 0x41, 0x4c, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4c, 0x49,
	0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x10, 0xd5, 0x01,
	0x12, 0x20, 0x0a, 0x1b, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52,
	0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x5f, 0x42, 0x55, 0x4c, 0x4b, 0x10,
	0xd6, 0x01, 0x12, 0x23, 0x0a, 0x1e, 0x46, 0x49, 0x54, 0x54, 0x54, 0x5f, 0x50, 0x55, 0x42, 0x4c,
	0x49, 0x53, 0x48, 0x5f, 0x53, 0x48, 0x41, 0x52, 0x4b, 0x5f, 0x54, 0x41, 0x4e, 0x4b, 0x5f, 0x45,
	0x56, 0x45, 0x4e, 0x54, 0x10, 0xd7, 0x01, 0x12, 0x16, 0x0a, 0x11, 0x43, 0x52, 0x45, 0x41, 0x54,
	0x45, 0x5f, 0x46, 0x4f, 0x52, 0x45, 0x58, 0x5f, 0x52, 0x41, 0x54, 0x45, 0x10, 0xd8, 0x01, 0x12,
	0x16, 0x0a, 0x11, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x45, 0x58, 0x5f,
	0x52, 0x41, 0x54, 0x45, 0x10, 0xd9, 0x01, 0x12, 0x12, 0x0a, 0x0d, 0x52, 0x45, 0x50, 0x52, 0x49,
	0x45, 0x56, 0x45, 0x5f, 0x56, 0x4b, 0x59, 0x43, 0x10, 0xda, 0x01, 0x12, 0x1b, 0x0a, 0x16, 0x55,
	0x53, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x47, 0x52, 0x4f, 0x53, 0x53, 0x5f, 0x53, 0x55,
	0x4d, 0x4d, 0x41, 0x52, 0x59, 0x10, 0xdb, 0x01, 0x12, 0x1f, 0x0a, 0x1a, 0x41, 0x4d, 0x4c, 0x5f,
	0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x47, 0x45, 0x4e, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0xdc, 0x01, 0x12, 0x21, 0x0a, 0x1c, 0x4d, 0x46, 0x5f,
	0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x46, 0x52,
	0x4f, 0x4d, 0x5f, 0x42, 0x55, 0x43, 0x4b, 0x45, 0x54, 0x10, 0xdd, 0x01, 0x12, 0x1f, 0x0a, 0x1a,
	0x50, 0x45, 0x52, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x52, 0x45, 0x56,
	0x49, 0x45, 0x57, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xde, 0x01, 0x12, 0x16, 0x0a,
	0x11, 0x46, 0x4f, 0x52, 0x45, 0x58, 0x5f, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x50, 0x4f,
	0x52, 0x54, 0x10, 0xdf, 0x01, 0x12, 0x17, 0x0a, 0x12, 0x43, 0x55, 0x52, 0x52, 0x45, 0x4e, 0x54,
	0x5f, 0x46, 0x4f, 0x52, 0x45, 0x58, 0x5f, 0x52, 0x41, 0x54, 0x45, 0x10, 0xe0, 0x01, 0x12, 0x1b,
	0x0a, 0x16, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f,
	0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0xe1, 0x01, 0x12, 0x43, 0x0a, 0x3e, 0x55,
	0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x50, 0x32, 0x50, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52,
	0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x55, 0x52, 0x49,
	0x54, 0x59, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44,
	0x41, 0x59, 0x53, 0x5f, 0x54, 0x4f, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x10, 0xe2, 0x01,
	0x12, 0x14, 0x0a, 0x0f, 0x47, 0x45, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x45, 0x58, 0x5f, 0x52, 0x41,
	0x54, 0x45, 0x53, 0x10, 0xe3, 0x01, 0x12, 0x2e, 0x0a, 0x29, 0x50, 0x52, 0x45, 0x5f, 0x41, 0x50,
	0x50, 0x52, 0x4f, 0x56, 0x45, 0x44, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4c, 0x4c, 0x5f, 0x55,
	0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x10, 0xe4, 0x01, 0x12, 0x1d, 0x0a, 0x18, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45,
	0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4f, 0x52, 0x41, 0x4c, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55,
	0x4c, 0x45, 0x10, 0xe5, 0x01, 0x12, 0x1a, 0x0a, 0x15, 0x41, 0x44, 0x44, 0x5f, 0x48, 0x4f, 0x4d,
	0x45, 0x5f, 0x50, 0x52, 0x4f, 0x4d, 0x4f, 0x5f, 0x42, 0x41, 0x4e, 0x4e, 0x45, 0x52, 0x10, 0xe6,
	0x01, 0x12, 0x1f, 0x0a, 0x1a, 0x55, 0x53, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x41, 0x43,
	0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10,
	0xe7, 0x01, 0x12, 0x1c, 0x0a, 0x17, 0x52, 0x45, 0x54, 0x52, 0x59, 0x5f, 0x4d, 0x55, 0x54, 0x55,
	0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x10, 0xe8, 0x01,
	0x12, 0x23, 0x0a, 0x1e, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f,
	0x41, 0x4c, 0x4c, 0x4f, 0x57, 0x45, 0x44, 0x5f, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0xe9, 0x01, 0x12, 0x25, 0x0a, 0x20, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x42, 0x41,
	0x4e, 0x4b, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c,
	0x5f, 0x4f, 0x56, 0x45, 0x52, 0x52, 0x49, 0x44, 0x45, 0x10, 0xea, 0x01, 0x12, 0x27, 0x0a, 0x22,
	0x55, 0x53, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41,
	0x44, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x46, 0x52, 0x4f, 0x4d, 0x5f, 0x42, 0x55, 0x43, 0x4b,
	0x45, 0x54, 0x10, 0xeb, 0x01, 0x12, 0x1a, 0x0a, 0x15, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f,
	0x4e, 0x55, 0x44, 0x47, 0x45, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x42, 0x55, 0x4c, 0x4b, 0x10, 0xec,
	0x01, 0x12, 0x1e, 0x0a, 0x19, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x41, 0x43, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x5f, 0x50, 0x45, 0x4e, 0x4e, 0x59, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x10, 0xed,
	0x01, 0x12, 0x2a, 0x0a, 0x25, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x57, 0x45, 0x41, 0x4c,
	0x54, 0x48, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x50, 0x55, 0x54, 0x5f, 0x50, 0x45,
	0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x10, 0xee, 0x01, 0x12, 0x1a, 0x0a,
	0x15, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x53, 0x5f, 0x49,
	0x4e, 0x5f, 0x42, 0x55, 0x4c, 0x4b, 0x10, 0xef, 0x01, 0x12, 0x27, 0x0a, 0x22, 0x52, 0x45, 0x53,
	0x45, 0x54, 0x5f, 0x43, 0x45, 0x4c, 0x45, 0x53, 0x54, 0x49, 0x41, 0x4c, 0x5f, 0x57, 0x4f, 0x52,
	0x4b, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x10,
	0xf0, 0x01, 0x12, 0x28, 0x0a, 0x23, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x46,
	0x45, 0x52, 0x52, 0x41, 0x4c, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x47, 0x10, 0xf1, 0x01, 0x12, 0x2f, 0x0a, 0x2a,
	0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c, 0x5f,
	0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x4e,
	0x46, 0x49, 0x47, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0xf2, 0x01, 0x12, 0x30, 0x0a,
	0x2b, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c,
	0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4f,
	0x4e, 0x46, 0x49, 0x47, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x10, 0xf3, 0x01, 0x12,
	0x28, 0x0a, 0x23, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52,
	0x41, 0x4c, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x43, 0x4f, 0x4e, 0x46, 0x49, 0x47, 0x10, 0xf4, 0x01, 0x12, 0x1a, 0x0a, 0x15, 0x55, 0x50, 0x4c,
	0x4f, 0x41, 0x44, 0x5f, 0x4c, 0x45, 0x41, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e,
	0x54, 0x53, 0x10, 0xf5, 0x01, 0x12, 0x1b, 0x0a, 0x16, 0x41, 0x44, 0x44, 0x5f, 0x48, 0x4f, 0x4d,
	0x45, 0x5f, 0x50, 0x4f, 0x50, 0x5f, 0x55, 0x50, 0x5f, 0x42, 0x41, 0x4e, 0x4e, 0x45, 0x52, 0x10,
	0xf6, 0x01, 0x12, 0x21, 0x0a, 0x1c, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x4c, 0x49,
	0x53, 0x54, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x53, 0x5f, 0x56, 0x45, 0x4e, 0x44,
	0x4f, 0x52, 0x10, 0xf7, 0x01, 0x12, 0x1d, 0x0a, 0x18, 0x4d, 0x41, 0x52, 0x4b, 0x5f, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45,
	0x4c, 0x10, 0xf8, 0x01, 0x12, 0x27, 0x0a, 0x22, 0x49, 0x46, 0x54, 0x5f, 0x47, 0x45, 0x4e, 0x45,
	0x52, 0x41, 0x54, 0x45, 0x5f, 0x41, 0x47, 0x47, 0x52, 0x45, 0x47, 0x41, 0x54, 0x45, 0x44, 0x5f,
	0x54, 0x41, 0x58, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x10, 0xf9, 0x01, 0x12, 0x30, 0x0a,
	0x2b, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x4f, 0x52, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54,
	0x45, 0x5f, 0x44, 0x59, 0x4e, 0x41, 0x4d, 0x49, 0x43, 0x5f, 0x55, 0x49, 0x5f, 0x45, 0x4c, 0x45,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x56, 0x41, 0x52, 0x49, 0x41, 0x4e, 0x54, 0x10, 0xfa, 0x01, 0x12,
	0x2f, 0x0a, 0x2a, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x44, 0x59, 0x4e, 0x41, 0x4d, 0x49,
	0x43, 0x5f, 0x55, 0x49, 0x5f, 0x45, 0x4c, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x45, 0x56, 0x41,
	0x4c, 0x55, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x47, 0x10, 0xfb, 0x01,
	0x12, 0x17, 0x0a, 0x12, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x43, 0x4d, 0x53, 0x5f, 0x50,
	0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x10, 0xfc, 0x01, 0x12, 0x13, 0x0a, 0x0e, 0x43, 0x52, 0x45,
	0x41, 0x54, 0x45, 0x5f, 0x43, 0x4d, 0x53, 0x5f, 0x53, 0x4b, 0x55, 0x10, 0xfd, 0x01, 0x12, 0x1e,
	0x0a, 0x19, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x43, 0x4d, 0x53, 0x5f, 0x43, 0x4f, 0x55,
	0x50, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x5f, 0x42, 0x55, 0x4c, 0x4b, 0x10, 0xfe, 0x01, 0x12, 0x17,
	0x0a, 0x12, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x4c, 0x41,
	0x59, 0x4f, 0x55, 0x54, 0x10, 0xff, 0x01, 0x12, 0x27, 0x0a, 0x22, 0x43, 0x52, 0x45, 0x41, 0x54,
	0x45, 0x5f, 0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x4c, 0x41, 0x59, 0x4f, 0x55, 0x54, 0x5f, 0x53, 0x45,
	0x47, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x41, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x10, 0x80, 0x02,
	0x12, 0x17, 0x0a, 0x12, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x5f, 0x48, 0x4f, 0x4d, 0x45, 0x5f,
	0x4c, 0x41, 0x59, 0x4f, 0x55, 0x54, 0x10, 0x81, 0x02, 0x12, 0x27, 0x0a, 0x22, 0x44, 0x45, 0x4c,
	0x45, 0x54, 0x45, 0x5f, 0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x4c, 0x41, 0x59, 0x4f, 0x55, 0x54, 0x5f,
	0x53, 0x45, 0x47, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x41, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x10,
	0x82, 0x02, 0x12, 0x23, 0x0a, 0x1e, 0x4d, 0x41, 0x52, 0x4b, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x53,
	0x5f, 0x42, 0x59, 0x5f, 0x41, 0x43, 0x51, 0x55, 0x49, 0x53, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x49, 0x4e, 0x46, 0x4f, 0x10, 0x83, 0x02, 0x12, 0x14, 0x0a, 0x0f, 0x47, 0x45, 0x54, 0x5f, 0x48,
	0x4f, 0x4d, 0x45, 0x5f, 0x4c, 0x41, 0x59, 0x4f, 0x55, 0x54, 0x10, 0x84, 0x02, 0x12, 0x24, 0x0a,
	0x1f, 0x47, 0x45, 0x54, 0x5f, 0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x4c, 0x41, 0x59, 0x4f, 0x55, 0x54,
	0x5f, 0x53, 0x45, 0x47, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x41, 0x50, 0x50, 0x49, 0x4e, 0x47,
	0x10, 0x85, 0x02, 0x12, 0x29, 0x0a, 0x24, 0x41, 0x44, 0x44, 0x5f, 0x49, 0x4e, 0x41, 0x50, 0x50,
	0x48, 0x45, 0x4c, 0x50, 0x5f, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x45, 0x4e,
	0x47, 0x49, 0x4e, 0x45, 0x5f, 0x53, 0x55, 0x52, 0x56, 0x45, 0x59, 0x10, 0x86, 0x02, 0x12, 0x2c,
	0x0a, 0x27, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x49, 0x4e, 0x41, 0x50, 0x50, 0x48, 0x45,
	0x4c, 0x50, 0x5f, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x45, 0x4e, 0x47, 0x49,
	0x4e, 0x45, 0x5f, 0x53, 0x55, 0x52, 0x56, 0x45, 0x59, 0x10, 0x87, 0x02, 0x12, 0x2b, 0x0a, 0x26,
	0x41, 0x44, 0x44, 0x5f, 0x49, 0x4e, 0x41, 0x50, 0x50, 0x48, 0x45, 0x4c, 0x50, 0x5f, 0x46, 0x45,
	0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x45, 0x4e, 0x47, 0x49, 0x4e, 0x45, 0x5f, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x88, 0x02, 0x12, 0x2e, 0x0a, 0x29, 0x55, 0x50, 0x44,
	0x41, 0x54, 0x45, 0x5f, 0x49, 0x4e, 0x41, 0x50, 0x50, 0x48, 0x45, 0x4c, 0x50, 0x5f, 0x46, 0x45,
	0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x45, 0x4e, 0x47, 0x49, 0x4e, 0x45, 0x5f, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x89, 0x02, 0x12, 0x2f, 0x0a, 0x2a, 0x4d, 0x41, 0x52,
	0x4b, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x59, 0x5f,
	0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x52,
	0x45, 0x43, 0x45, 0x49, 0x56, 0x45, 0x44, 0x10, 0x8a, 0x02, 0x12, 0x21, 0x0a, 0x1c, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53,
	0x54, 0x45, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x8b, 0x02, 0x12, 0x1f, 0x0a,
	0x1a, 0x55, 0x53, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45,
	0x5f, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x8c, 0x02, 0x12, 0x1f,
	0x0a, 0x1a, 0x55, 0x53, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54,
	0x45, 0x5f, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x8d, 0x02, 0x12,
	0x25, 0x0a, 0x20, 0x55, 0x53, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x41, 0x44, 0x44, 0x5f,
	0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x54, 0x4f, 0x5f, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x10, 0x8e, 0x02, 0x12, 0x28, 0x0a, 0x23, 0x55, 0x53, 0x53, 0x54, 0x4f, 0x43,
	0x4b, 0x53, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f,
	0x49, 0x4e, 0x5f, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x8f, 0x02,
	0x12, 0x2a, 0x0a, 0x25, 0x55, 0x53, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x52, 0x45, 0x4d,
	0x4f, 0x56, 0x45, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x46, 0x52, 0x4f, 0x4d, 0x5f, 0x43,
	0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x90, 0x02, 0x12, 0x13, 0x0a, 0x0e,
	0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x45, 0x47, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x91,
	0x02, 0x12, 0x12, 0x0a, 0x0d, 0x41, 0x44, 0x44, 0x5f, 0x45, 0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x45,
	0x52, 0x53, 0x10, 0x92, 0x02, 0x12, 0x32, 0x0a, 0x2d, 0x49, 0x46, 0x54, 0x5f, 0x52, 0x45, 0x4a,
	0x45, 0x43, 0x54, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53,
	0x5f, 0x49, 0x4e, 0x5f, 0x4f, 0x55, 0x54, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x57, 0x49, 0x46,
	0x54, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x93, 0x02, 0x12, 0x17, 0x0a, 0x12, 0x57, 0x48, 0x49,
	0x54, 0x45, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x49, 0x44, 0x10,
	0x94, 0x02, 0x12, 0x15, 0x0a, 0x10, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x4b, 0x59, 0x43,
	0x5f, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x10, 0x95, 0x02, 0x12, 0x14, 0x0a, 0x0f, 0x44, 0x41, 0x54,
	0x41, 0x5f, 0x45, 0x58, 0x54, 0x52, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x96, 0x02, 0x12,
	0x18, 0x0a, 0x13, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x5f,
	0x43, 0x4f, 0x4e, 0x46, 0x49, 0x47, 0x10, 0x97, 0x02, 0x12, 0x18, 0x0a, 0x13, 0x55, 0x50, 0x44,
	0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52,
	0x10, 0x98, 0x02, 0x12, 0x22, 0x0a, 0x1d, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x41, 0x54, 0x45, 0x5f,
	0x43, 0x48, 0x41, 0x54, 0x42, 0x4f, 0x54, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x54,
	0x4f, 0x4b, 0x45, 0x4e, 0x10, 0x99, 0x02, 0x12, 0x21, 0x0a, 0x1c, 0x50, 0x32, 0x50, 0x5f, 0x52,
	0x45, 0x47, 0x49, 0x53, 0x54, 0x45, 0x52, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x49, 0x4e, 0x47, 0x5f,
	0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x9a, 0x02, 0x12, 0x24, 0x0a, 0x1f, 0x55, 0x50,
	0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x4c, 0x45, 0x41, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49,
	0x4e, 0x54, 0x5f, 0x4e, 0x41, 0x52, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x9b, 0x02,
	0x12, 0x1c, 0x0a, 0x17, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x4d, 0x50, 0x4c, 0x4f,
	0x59, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x9c, 0x02, 0x12, 0x2d,
	0x0a, 0x28, 0x42, 0x55, 0x4c, 0x4b, 0x5f, 0x53, 0x45, 0x54, 0x55, 0x50, 0x5f, 0x52, 0x45, 0x46,
	0x45, 0x52, 0x52, 0x41, 0x4c, 0x5f, 0x53, 0x45, 0x47, 0x4d, 0x45, 0x4e, 0x54, 0x45, 0x44, 0x5f,
	0x43, 0x4f, 0x4d, 0x50, 0x4f, 0x4e, 0x45, 0x4e, 0x54, 0x53, 0x10, 0x9d, 0x02, 0x12, 0x28, 0x0a,
	0x23, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x4c, 0x45, 0x41, 0x5f, 0x43, 0x4f, 0x4d, 0x50,
	0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x44, 0x45, 0x54,
	0x41, 0x49, 0x4c, 0x53, 0x10, 0x9e, 0x02, 0x12, 0x17, 0x0a, 0x12, 0x55, 0x53, 0x45, 0x52, 0x5f,
	0x43, 0x4f, 0x4d, 0x4d, 0x53, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x52, 0x4f, 0x4c, 0x10, 0x9f, 0x02,
	0x12, 0x31, 0x0a, 0x2c, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f,
	0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x10, 0xa0, 0x02, 0x12, 0x35, 0x0a, 0x30, 0x53, 0x49, 0x4d, 0x55, 0x4c, 0x41, 0x54, 0x45, 0x5f,
	0x55, 0x53, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x4f, 0x55, 0x54, 0x57, 0x41, 0x52, 0x44,
	0x5f, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45, 0x52, 0x5f, 0x4e,
	0x4f, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x10, 0xa1, 0x02, 0x12, 0x1f, 0x0a, 0x1a, 0x43, 0x52,
	0x45, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x41, 0x51, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x58, 0x54,
	0x5f, 0x4d, 0x41, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x10, 0xa2, 0x02, 0x12, 0x26, 0x0a, 0x21, 0x50,
	0x32, 0x50, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0xa3, 0x02, 0x12, 0x19, 0x0a, 0x14, 0x46, 0x45, 0x54, 0x43, 0x48, 0x5f, 0x41, 0x43, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0xa4, 0x02, 0x12, 0x21,
	0x0a, 0x1c, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x53, 0x49,
	0x4d, 0x55, 0x4c, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x4c, 0x41, 0x59, 0x4f, 0x55, 0x54, 0x10, 0xa5,
	0x02, 0x12, 0x29, 0x0a, 0x24, 0x47, 0x45, 0x54, 0x5f, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f,
	0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49,
	0x4c, 0x53, 0x5f, 0x42, 0x59, 0x5f, 0x50, 0x41, 0x4e, 0x10, 0xa6, 0x02, 0x12, 0x21, 0x0a, 0x1c,
	0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x57, 0x41, 0x54, 0x53, 0x4f, 0x4e, 0x5f, 0x54, 0x49,
	0x43, 0x4b, 0x45, 0x54, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0xa7, 0x02, 0x12,
	0x21, 0x0a, 0x1c, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x57, 0x41, 0x54, 0x53, 0x4f, 0x4e,
	0x5f, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10,
	0xa8, 0x02, 0x12, 0x32, 0x0a, 0x2d, 0x55, 0x53, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x43,
	0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x52, 0x45, 0x4c, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x53, 0x48, 0x49, 0x50, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x42, 0x52, 0x4f,
	0x4b, 0x45, 0x52, 0x10, 0xa9, 0x02, 0x12, 0x17, 0x0a, 0x12, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f,
	0x41, 0x42, 0x46, 0x4c, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0xaa, 0x02, 0x12,
	0x24, 0x0a, 0x1f, 0x54, 0x52, 0x49, 0x47, 0x47, 0x45, 0x52, 0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x4e,
	0x41, 0x4d, 0x45, 0x5f, 0x44, 0x4f, 0x42, 0x5f, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0xab, 0x02, 0x12, 0x27, 0x0a, 0x22, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44,
	0x49, 0x4e, 0x47, 0x5f, 0x54, 0x52, 0x4f, 0x55, 0x42, 0x4c, 0x45, 0x53, 0x48, 0x4f, 0x4f, 0x54,
	0x49, 0x4e, 0x47, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0xac, 0x02, 0x12, 0x18,
	0x0a, 0x13, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x43,
	0x4f, 0x4e, 0x46, 0x49, 0x47, 0x10, 0xad, 0x02, 0x12, 0x18, 0x0a, 0x13, 0x55, 0x50, 0x44, 0x41,
	0x54, 0x45, 0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x47, 0x10,
	0xae, 0x02, 0x12, 0x17, 0x0a, 0x12, 0x46, 0x41, 0x49, 0x4c, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x5f,
	0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x10, 0xaf, 0x02, 0x12, 0x18, 0x0a, 0x13, 0x50,
	0x32, 0x50, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x43, 0x41, 0x53, 0x48, 0x5f, 0x4c, 0x45, 0x44, 0x47,
	0x45, 0x52, 0x10, 0xb0, 0x02, 0x12, 0x19, 0x0a, 0x14, 0x47, 0x45, 0x54, 0x5f, 0x4d, 0x46, 0x5f,
	0x46, 0x4f, 0x4c, 0x49, 0x4f, 0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x10, 0xb1, 0x02,
	0x12, 0x18, 0x0a, 0x13, 0x49, 0x46, 0x54, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45,
	0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x10, 0xb2, 0x02, 0x12, 0x36, 0x0a, 0x31, 0x52, 0x41,
	0x49, 0x53, 0x45, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52,
	0x59, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x42, 0x55, 0x4c, 0x4b, 0x10,
	0xb3, 0x02, 0x12, 0x1f, 0x0a, 0x1a, 0x53, 0x49, 0x4d, 0x55, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x52,
	0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0xb4, 0x02, 0x12, 0x2b, 0x0a, 0x26, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x41,
	0x4c, 0x41, 0x52, 0x59, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f, 0x52, 0x45, 0x46,
	0x45, 0x52, 0x52, 0x41, 0x4c, 0x53, 0x5f, 0x53, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x10, 0xb5, 0x02,
	0x12, 0x25, 0x0a, 0x20, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x49, 0x54, 0x43, 0x5f,
	0x50, 0x4f, 0x49, 0x4e, 0x54, 0x53, 0x5f, 0x48, 0x41, 0x4e, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f,
	0x46, 0x49, 0x4c, 0x45, 0x10, 0xb6, 0x02, 0x12, 0x19, 0x0a, 0x14, 0x52, 0x49, 0x53, 0x4b, 0x5f,
	0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x53, 0x10,
	0xb7, 0x02, 0x12, 0x1c, 0x0a, 0x17, 0x53, 0x45, 0x47, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x45,
	0x54, 0x41, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xb8, 0x02,
	0x12, 0x29, 0x0a, 0x24, 0x53, 0x45, 0x54, 0x5f, 0x53, 0x45, 0x47, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x4d, 0x45, 0x54, 0x41, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x41,
	0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0xb9, 0x02, 0x12, 0x2a, 0x0a, 0x25, 0x4f,
	0x56, 0x45, 0x52, 0x52, 0x49, 0x44, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x48, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x45, 0x4e, 0x47, 0x49, 0x4e, 0x45, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x10, 0xba, 0x02, 0x12, 0x27, 0x0a, 0x22, 0x4f, 0x56, 0x45, 0x52, 0x52,
	0x49, 0x44, 0x45, 0x5f, 0x43, 0x53, 0x49, 0x53, 0x5f, 0x48, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f,
	0x45, 0x4e, 0x47, 0x49, 0x4e, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0xbb, 0x02,
	0x12, 0x1e, 0x0a, 0x19, 0x53, 0x4f, 0x46, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x4d, 0x41,
	0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x52, 0x49, 0x44, 0x45, 0x10, 0xbc, 0x02,
	0x12, 0x1f, 0x0a, 0x1a, 0x50, 0x41, 0x53, 0x53, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x10, 0xbd,
	0x02, 0x12, 0x17, 0x0a, 0x12, 0x41, 0x44, 0x44, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x10, 0xbe, 0x02, 0x12, 0x20, 0x0a, 0x1b, 0x45, 0x58,
	0x54, 0x52, 0x41, 0x43, 0x54, 0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x46, 0x52, 0x4f, 0x4d, 0x5f, 0x4b,
	0x52, 0x41, 0x5f, 0x44, 0x4f, 0x43, 0x4b, 0x45, 0x54, 0x10, 0xbf, 0x02, 0x12, 0x23, 0x0a, 0x1e,
	0x55, 0x53, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x52, 0x45, 0x53, 0x45, 0x54, 0x5f, 0x4f,
	0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x10, 0xc0,
	0x02, 0x12, 0x1a, 0x0a, 0x15, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x4d, 0x41, 0x4e, 0x41, 0x47, 0x45,
	0x5f, 0x57, 0x48, 0x49, 0x54, 0x45, 0x4c, 0x49, 0x53, 0x54, 0x10, 0xc1, 0x02, 0x12, 0x22, 0x0a,
	0x1d, 0x55, 0x53, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x49, 0x4e,
	0x56, 0x45, 0x53, 0x54, 0x4f, 0x52, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x10, 0xc2,
	0x02, 0x12, 0x1d, 0x0a, 0x18, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49,
	0x56, 0x49, 0x54, 0x59, 0x5f, 0x4d, 0x45, 0x54, 0x41, 0x44, 0x41, 0x54, 0x41, 0x10, 0xc3, 0x02,
	0x12, 0x1f, 0x0a, 0x1a, 0x54, 0x52, 0x49, 0x47, 0x47, 0x45, 0x52, 0x5f, 0x54, 0x58, 0x4e, 0x5f,
	0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x49, 0x53, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xc4,
	0x02, 0x12, 0x28, 0x0a, 0x23, 0x47, 0x45, 0x54, 0x5f, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43,
	0x4b, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49,
	0x54, 0x49, 0x45, 0x53, 0x5f, 0x43, 0x53, 0x56, 0x10, 0xc5, 0x02, 0x12, 0x2e, 0x0a, 0x29, 0x54,
	0x52, 0x49, 0x47, 0x47, 0x45, 0x52, 0x5f, 0x55, 0x4e, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x44,
	0x5f, 0x43, 0x43, 0x5f, 0x52, 0x45, 0x4e, 0x45, 0x57, 0x41, 0x4c, 0x5f, 0x46, 0x45, 0x45, 0x5f,
	0x52, 0x45, 0x56, 0x45, 0x52, 0x53, 0x41, 0x4c, 0x10, 0xc6, 0x02, 0x12, 0x34, 0x0a, 0x2f, 0x55,
	0x53, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x41, 0x54, 0x45,
	0x5f, 0x41, 0x44, 0x48, 0x4f, 0x43, 0x5f, 0x49, 0x4e, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45,
	0x4d, 0x49, 0x54, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x53, 0x10, 0xc7,
	0x02, 0x12, 0x2f, 0x0a, 0x2a, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x4d, 0x4f,
	0x44, 0x45, 0x4c, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x46, 0x4f, 0x52,
	0x5f, 0x55, 0x53, 0x45, 0x52, 0x53, 0x5f, 0x43, 0x58, 0x5f, 0x51, 0x55, 0x45, 0x52, 0x59, 0x10,
	0xc8, 0x02, 0x12, 0x24, 0x0a, 0x1f, 0x55, 0x53, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x52,
	0x45, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x49, 0x4e, 0x57, 0x41, 0x52, 0x44,
	0x5f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0xc9, 0x02, 0x12, 0x1d, 0x0a, 0x18, 0x4d, 0x41, 0x50, 0x5f,
	0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x46, 0x4f, 0x52, 0x45, 0x58,
	0x5f, 0x54, 0x58, 0x4e, 0x10, 0xca, 0x02, 0x12, 0x13, 0x0a, 0x0e, 0x43, 0x52, 0x45, 0x41, 0x54,
	0x45, 0x5f, 0x4a, 0x4f, 0x55, 0x52, 0x4e, 0x45, 0x59, 0x10, 0xcb, 0x02, 0x12, 0x13, 0x0a, 0x0e,
	0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x4a, 0x4f, 0x55, 0x52, 0x4e, 0x45, 0x59, 0x10, 0xcc,
	0x02, 0x12, 0x1d, 0x0a, 0x18, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49,
	0x56, 0x49, 0x54, 0x59, 0x5f, 0x4d, 0x45, 0x54, 0x41, 0x44, 0x41, 0x54, 0x41, 0x10, 0xcd, 0x02,
	0x12, 0x1b, 0x0a, 0x16, 0x48, 0x41, 0x4e, 0x44, 0x4c, 0x45, 0x5f, 0x56, 0x4b, 0x59, 0x43, 0x5f,
	0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x10, 0xce, 0x02, 0x12, 0x22, 0x0a,
	0x1d, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x55, 0x4e, 0x49, 0x46, 0x49, 0x45, 0x44, 0x5f,
	0x4c, 0x45, 0x41, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x53, 0x10, 0xcf,
	0x02, 0x12, 0x1b, 0x0a, 0x16, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x5f, 0x53, 0x45, 0x52, 0x56,
	0x49, 0x43, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x10, 0xd0, 0x02, 0x12, 0x1d,
	0x0a, 0x18, 0x46, 0x41, 0x49, 0x4c, 0x5f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xd1, 0x02, 0x12, 0x25, 0x0a,
	0x20, 0x55, 0x53, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x54, 0x52, 0x41, 0x44, 0x49, 0x4e,
	0x47, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52,
	0x59, 0x10, 0xd2, 0x02, 0x12, 0x24, 0x0a, 0x1f, 0x43, 0x41, 0x43, 0x48, 0x45, 0x5f, 0x43, 0x4f,
	0x4e, 0x54, 0x41, 0x43, 0x54, 0x5f, 0x55, 0x53, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x52,
	0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x10, 0xd3, 0x02, 0x12, 0x29, 0x0a, 0x24, 0x42, 0x32,
	0x42, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x53, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49,
	0x4e, 0x47, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49,
	0x4e, 0x47, 0x10, 0xd4, 0x02, 0x12, 0x1c, 0x0a, 0x17, 0x55, 0x53, 0x53, 0x54, 0x4f, 0x43, 0x4b,
	0x53, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53,
	0x10, 0xd5, 0x02, 0x12, 0x29, 0x0a, 0x24, 0x4d, 0x46, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f,
	0x41, 0x44, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x42, 0x59, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f,
	0x52, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x49, 0x44, 0x53, 0x10, 0xd6, 0x02, 0x12, 0x28,
	0x0a, 0x23, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x47, 0x55, 0x41, 0x52, 0x44, 0x49, 0x41, 0x4e,
	0x5f, 0x52, 0x45, 0x44, 0x41, 0x43, 0x54, 0x5f, 0x43, 0x4b, 0x59, 0x43, 0x5f, 0x44, 0x4f, 0x43,
	0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0xd7, 0x02, 0x12, 0x1c, 0x0a, 0x17, 0x4c, 0x45, 0x41, 0x44,
	0x5f, 0x4d, 0x47, 0x4d, 0x54, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x46,
	0x49, 0x4c, 0x45, 0x10, 0xd8, 0x02, 0x12, 0x19, 0x0a, 0x14, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49,
	0x54, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x10, 0xd9,
	0x02, 0x12, 0x27, 0x0a, 0x22, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x47, 0x55, 0x41, 0x52, 0x44,
	0x49, 0x41, 0x4e, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x45, 0x5f, 0x45, 0x4d, 0x49, 0x5f,
	0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x10, 0xda, 0x02, 0x12, 0x34, 0x0a, 0x2f, 0x47, 0x45,
	0x54, 0x5f, 0x53, 0x41, 0x56, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x55, 0x52, 0x45, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42,
	0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x49, 0x4e, 0x5f, 0x42, 0x55, 0x4c, 0x4b, 0x10, 0xdb, 0x02,
	0x12, 0x15, 0x0a, 0x10, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x41,
	0x47, 0x45, 0x4e, 0x54, 0x10, 0xdc, 0x02, 0x12, 0x3f, 0x0a, 0x3a, 0x53, 0x41, 0x4c, 0x41, 0x52,
	0x59, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f, 0x44, 0x59, 0x4e, 0x41, 0x4d, 0x49,
	0x43, 0x5f, 0x55, 0x49, 0x5f, 0x45, 0x4c, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x55, 0x50, 0x44,
	0x41, 0x54, 0x45, 0x5f, 0x4f, 0x52, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x56, 0x41,
	0x52, 0x49, 0x41, 0x4e, 0x54, 0x10, 0xdd, 0x02, 0x12, 0x48, 0x0a, 0x43, 0x53, 0x41, 0x4c, 0x41,
	0x52, 0x59, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f, 0x44, 0x59, 0x4e, 0x41, 0x4d,
	0x49, 0x43, 0x5f, 0x55, 0x49, 0x5f, 0x45, 0x4c, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x55, 0x50,
	0x44, 0x41, 0x54, 0x45, 0x5f, 0x4f, 0x52, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x45,
	0x56, 0x41, 0x4c, 0x55, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x47, 0x10,
	0xde, 0x02, 0x12, 0x38, 0x0a, 0x33, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x4e, 0x4f,
	0x4e, 0x5f, 0x52, 0x45, 0x53, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x52, 0x4f, 0x53, 0x53,
	0x5f, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x41, 0x4e, 0x55,
	0x41, 0x4c, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x10, 0xdf, 0x02, 0x12, 0x29, 0x0a, 0x24,
	0x4e, 0x52, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x41,
	0x53, 0x53, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x52, 0x45,
	0x56, 0x49, 0x45, 0x57, 0x10, 0xe0, 0x02, 0x12, 0x19, 0x0a, 0x14, 0x41, 0x44, 0x44, 0x5f, 0x53,
	0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x49, 0x53, 0x49, 0x4e, 0x53, 0x10,
	0xe1, 0x02, 0x12, 0x2b, 0x0a, 0x26, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x47, 0x55, 0x41, 0x52,
	0x44, 0x49, 0x41, 0x4e, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x45, 0x5f, 0x44, 0x49, 0x53,
	0x42, 0x55, 0x52, 0x53, 0x41, 0x4c, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x59, 0x10, 0xe2, 0x02, 0x12,
	0x18, 0x0a, 0x13, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x52,
	0x45, 0x44, 0x4c, 0x49, 0x53, 0x54, 0x10, 0xe3, 0x02, 0x12, 0x2d, 0x0a, 0x28, 0x53, 0x45, 0x4e,
	0x44, 0x5f, 0x45, 0x4e, 0x41, 0x43, 0x48, 0x5f, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f,
	0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x41, 0x4c,
	0x4c, 0x42, 0x41, 0x43, 0x4b, 0x10, 0xe4, 0x02, 0x12, 0x30, 0x0a, 0x2b, 0x42, 0x32, 0x42, 0x5f,
	0x55, 0x53, 0x45, 0x52, 0x53, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x45, 0x44, 0x5f,
	0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0xe5, 0x02, 0x12, 0x20, 0x0a, 0x1b, 0x55, 0x50,
	0x44, 0x41, 0x54, 0x45, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x4f, 0x55, 0x54, 0x43, 0x41,
	0x4c, 0x4c, 0x5f, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54, 0x10, 0xe6, 0x02, 0x12, 0x25, 0x0a, 0x20,
	0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x55, 0x47, 0x47, 0x45, 0x53, 0x54, 0x45, 0x44,
	0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x52, 0x55, 0x4c, 0x45,
	0x10, 0xe7, 0x02, 0x12, 0x24, 0x0a, 0x1f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x55,
	0x4c, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d,
	0x41, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x10, 0xe8, 0x02, 0x12, 0x20, 0x0a, 0x1b, 0x46, 0x45, 0x44,
	0x45, 0x52, 0x41, 0x4c, 0x5f, 0x45, 0x53, 0x43, 0x41, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xe9, 0x02, 0x12, 0x24, 0x0a, 0x1f, 0x42,
	0x32, 0x42, 0x5f, 0x55, 0x4e, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0xea,
	0x02, 0x12, 0x28, 0x0a, 0x23, 0x52, 0x45, 0x46, 0x52, 0x45, 0x53, 0x48, 0x5f, 0x55, 0x53, 0x45,
	0x52, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x46, 0x52, 0x4f, 0x4d, 0x5f, 0x50, 0x41, 0x52, 0x54,
	0x4e, 0x45, 0x52, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x10, 0xeb, 0x02, 0x12, 0x17, 0x0a, 0x12, 0x44,
	0x45, 0x4c, 0x45, 0x54, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54,
	0x53, 0x10, 0xec, 0x02, 0x12, 0x2a, 0x0a, 0x25, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x49,
	0x4d, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x4f, 0x5f, 0x45, 0x50, 0x49, 0x46, 0x49, 0x5f, 0x49, 0x43,
	0x4f, 0x4e, 0x53, 0x5f, 0x53, 0x33, 0x5f, 0x42, 0x55, 0x43, 0x4b, 0x45, 0x54, 0x10, 0xed, 0x02,
	0x12, 0x10, 0x0a, 0x0b, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x5f, 0x4c, 0x4f, 0x45, 0x43, 0x10,
	0xee, 0x02, 0x12, 0x2d, 0x0a, 0x28, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x47, 0x55, 0x41, 0x52,
	0x44, 0x49, 0x41, 0x4e, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x4d, 0x50, 0x4c,
	0x4f, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0xef,
	0x02, 0x12, 0x18, 0x0a, 0x13, 0x46, 0x45, 0x54, 0x43, 0x48, 0x5f, 0x55, 0x54, 0x52, 0x53, 0x5f,
	0x46, 0x52, 0x4f, 0x4d, 0x5f, 0x4c, 0x4f, 0x47, 0x10, 0xf0, 0x02, 0x12, 0x17, 0x0a, 0x12, 0x52,
	0x45, 0x53, 0x45, 0x54, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x10, 0xf1, 0x02, 0x12, 0x33, 0x0a, 0x2e, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x4e,
	0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x49, 0x4e, 0x53, 0x49, 0x47, 0x48, 0x54, 0x53,
	0x5f, 0x47, 0x45, 0x54, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x53, 0x5f, 0x46, 0x52, 0x4f, 0x4d, 0x5f,
	0x42, 0x55, 0x43, 0x4b, 0x45, 0x54, 0x10, 0xf2, 0x02, 0x2a, 0x6d, 0x0a, 0x12, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x24, 0x0a, 0x20, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e,
	0x53, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x54, 0x52, 0x49, 0x4e, 0x47, 0x10,
	0x01, 0x12, 0x08, 0x0a, 0x04, 0x4a, 0x53, 0x4f, 0x4e, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x54,
	0x41, 0x42, 0x4c, 0x45, 0x10, 0x03, 0x12, 0x10, 0x0a, 0x0c, 0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f,
	0x41, 0x44, 0x5f, 0x55, 0x52, 0x4c, 0x10, 0x04, 0x42, 0x62, 0x0a, 0x2f, 0x63, 0x6f, 0x6d, 0x2e,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f,
	0x70, 0x65, 0x72, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5a, 0x2f, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x64, 0x65, 0x76, 0x65, 0x6c,
	0x6f, 0x70, 0x65, 0x72, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_cx_developer_actions_enums_proto_rawDescOnce sync.Once
	file_api_cx_developer_actions_enums_proto_rawDescData = file_api_cx_developer_actions_enums_proto_rawDesc
)

func file_api_cx_developer_actions_enums_proto_rawDescGZIP() []byte {
	file_api_cx_developer_actions_enums_proto_rawDescOnce.Do(func() {
		file_api_cx_developer_actions_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_cx_developer_actions_enums_proto_rawDescData)
	})
	return file_api_cx_developer_actions_enums_proto_rawDescData
}

var file_api_cx_developer_actions_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_cx_developer_actions_enums_proto_goTypes = []interface{}{
	(DeveloperActions)(0),   // 0: cx.developer.actions.DeveloperActions
	(ActionResponseType)(0), // 1: cx.developer.actions.ActionResponseType
}
var file_api_cx_developer_actions_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_cx_developer_actions_enums_proto_init() }
func file_api_cx_developer_actions_enums_proto_init() {
	if File_api_cx_developer_actions_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_cx_developer_actions_enums_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_cx_developer_actions_enums_proto_goTypes,
		DependencyIndexes: file_api_cx_developer_actions_enums_proto_depIdxs,
		EnumInfos:         file_api_cx_developer_actions_enums_proto_enumTypes,
	}.Build()
	File_api_cx_developer_actions_enums_proto = out.File
	file_api_cx_developer_actions_enums_proto_rawDesc = nil
	file_api_cx_developer_actions_enums_proto_goTypes = nil
	file_api_cx_developer_actions_enums_proto_depIdxs = nil
}
