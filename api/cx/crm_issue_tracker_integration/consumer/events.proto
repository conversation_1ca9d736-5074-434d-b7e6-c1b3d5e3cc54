syntax = "proto3";

package cx.crm_issue_tracker_integration.consumer;

import "api/cx/ticket/ticket.proto";
import "api/pkg/monorail/issue.proto";
import "api/vendors/freshdesk/ticket.proto";

option go_package = "github.com/epifi/gamma/api/cx/crm_issue_tracker_integration/consumer";
option java_package = "com.github.epifi.gamma.api.cx.crm_issue_tracker_integration.consumer";

// Enum representing different types of event payloads that can be published to the queue
enum EventType {
  EVENT_TYPE_UNSPECIFIED = 0;
  // when there is a change in ticket states (create or update ticket) on Freshdesk
  EVENT_TYPE_FRESHDESK_TICKET_CHANGE = 1;
  // When there is a conversation(eg: private note) on Freshdesk ticket
  EVENT_TYPE_FRESHDESK_TICKET_CONVERSATION = 2;
  // For updates or comments on a Monorail Issue
  EVENT_TYPE_MONORAIL_ISSUE_UPDATES_COMMENTS = 3;
}

// The payload for Ticket change event.
// This event is published whenever there is new ticket created or there is an update in ticket attributes
message FreshdeskTicketChangePayload {
  // The ticket which is currently existing in our system
  cx.ticket.TicketDetails existing_ticket_details = 1;
  // The newly created ticket in our system
  cx.ticket.TicketDetails new_ticket_details = 2;
}

// The payload for freshdesk conversation event.
// This event will be triggered on a conversation creation on a Freshdesk Ticket.
// currently the use case is only for private notes
message FreshdeskConversationPayload{
  vendors.freshdesk.Conversation conversation = 1;
}

// The payload for Monorail Issue Change or Issue comment
// NOTE: This event will be fired from a scheduled job
message MonorailIssueUpdatesCommentsPayload {
  // Monorail issue attributes
  pkg.monorail.Issue monorail_issue = 1;
  // whether this update involves status change of the Monorail Issue
  bool is_status_update = 2;
  // Latest comments on the Monorail Issue
  // There can be multiple comments since it is fired from a scheduled job which pulls the comments using Monorail APIs
  repeated pkg.monorail.Comment issue_comments = 3;
}
