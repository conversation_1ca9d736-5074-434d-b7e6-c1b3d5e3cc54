package card

import alPb "github.com/epifi/gamma/api/cx/audit_log"

func (m *GetCardsForCustomerRequest) GetAction() alPb.Action {
	return alPb.Action_FETCH
}

func (m *GetCardsForCustomerRequest) GetObject() alPb.Object {
	return alPb.Object_DEBIT_CARD
}

func (m *BlockCustomerCardRequest) GetAction() alPb.Action {
	return alPb.Action_BLOCK
}

func (m *BlockCustomerCardRequest) GetObject() alPb.Object {
	return alPb.Object_DEBIT_CARD
}

func (m *SuspendCustomerCardRequest) GetAction() alPb.Action {
	return alPb.Action_SUSPEND
}

func (m *SuspendCustomerCardRequest) GetObject() alPb.Object {
	return alPb.Object_DEBIT_CARD
}

func (m *GetChannelAndLimitSettingsRequest) GetAction() alPb.Action {
	return alPb.Action_FETCH
}

func (m *GetChannelAndLimitSettingsRequest) GetObject() alPb.Object {
	return alPb.Object_DEBIT_CARD
}

func (m *GetCardDeliveryTrackingDataRequest) GetAction() alPb.Action {
	return alPb.Action_FETCH
}

func (m *GetCardDeliveryTrackingDataRequest) GetObject() alPb.Object {
	return alPb.Object_DEBIT_CARD
}

func (m *GetAllCardsForCustomerRequest) GetAction() alPb.Action {
	return alPb.Action_FETCH
}

func (m *GetAllCardsForCustomerRequest) GetObject() alPb.Object {
	return alPb.Object_DEBIT_CARD
}

func (m *UpdateFreeCardReplacementRequest) GetAction() alPb.Action {
	return alPb.Action_UPDATE_FREE_REPLACEMENT
}

func (m *UpdateFreeCardReplacementRequest) GetObject() alPb.Object {
	return alPb.Object_DEBIT_CARD
}

func (m *GetCardDeliveryTrackingDataV2Request) GetAction() alPb.Action {
	return alPb.Action_FETCH
}

func (m *GetCardDeliveryTrackingDataV2Request) GetObject() alPb.Object {
	return alPb.Object_DEBIT_CARD
}

func (m *FetchForexRefundInfoForDcTxnRequest) GetObject() alPb.Object {
	return alPb.Object_DEBIT_CARD
}

func (m *FetchForexRefundInfoForDcTxnRequest) GetAction() alPb.Action {
	return alPb.Action_FETCH
}

func (m *FetchPhysicalCardDispatchStatusRequest) GetObject() alPb.Object {
	return alPb.Object_DEBIT_CARD
}

func (m *FetchPhysicalCardDispatchStatusRequest) GetAction() alPb.Action {
	return alPb.Action_FETCH
}

func (m *FetchAmcInfoRequest) GetObject() alPb.Object {
	return alPb.Object_DEBIT_CARD
}

func (m *FetchAmcInfoRequest) GetAction() alPb.Action {
	return alPb.Action_FETCH
}
