// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/cx/data_collector/rewards/service.proto

package rewards

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	casper "github.com/epifi/gamma/api/casper"
	exchanger "github.com/epifi/gamma/api/casper/exchanger"
	external_vendor_redemption "github.com/epifi/gamma/api/casper/external_vendor_redemption"
	redemption "github.com/epifi/gamma/api/casper/redemption"
	cx "github.com/epifi/gamma/api/cx"
	customer_auth "github.com/epifi/gamma/api/cx/customer_auth"
	rewards "github.com/epifi/gamma/api/rewards"
	webui "github.com/epifi/gamma/api/typesv2/webui"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// RewardOfferTypesOptions will solely used for fetching reward details for particular reward offer type.
// **NOTE** this reward offer type is completely different from reward offer type in rewards service.
type RewardOfferTypesOptions int32

const (
	RewardOfferTypesOptions_REWARD_OFFER_TYPES_OPTIONS_UNSPECIFIED                                      RewardOfferTypesOptions = 0
	RewardOfferTypesOptions_REWARD_OFFER_TYPES_OPTIONS_INFINITE_OR_SALARY_TIER_2_PERCENT_CASHBACK_OFFER RewardOfferTypesOptions = 1
	RewardOfferTypesOptions_REWARD_OFFER_TYPES_OPTIONS_AA_SALARY_BAND_1_TIER_1_PERCENT_CASHBACK_OFFER   RewardOfferTypesOptions = 2
	RewardOfferTypesOptions_REWARD_OFFER_TYPES_OPTIONS_AA_SALARY_BAND_2_TIER_2_PERCENT_CASHBACK_OFFER   RewardOfferTypesOptions = 3
	RewardOfferTypesOptions_REWARD_OFFER_TYPES_OPTIONS_AA_SALARY_BAND_3_TIER_3_PERCENT_CASHBACK_OFFER   RewardOfferTypesOptions = 4
	RewardOfferTypesOptions_REWARD_OFFER_TYPES_OPTIONS_SALARY_LITE_TIER_1_PERCENT_CASHBACK_OFFER        RewardOfferTypesOptions = 5
	RewardOfferTypesOptions_REWARD_OFFER_TYPES_OPTIONS_PLUS_TIER_1_PERCENT_CASHBACK_OFFER               RewardOfferTypesOptions = 6
	RewardOfferTypesOptions_REWARD_OFFER_TYPES_OPTIONS_INFINITE_TIER_2_PERCENT_CASHBACK_OFFER           RewardOfferTypesOptions = 7
	RewardOfferTypesOptions_REWARD_OFFER_TYPES_OPTIONS_SALARY_TIER_2_PERCENT_CASHBACK_OFFER             RewardOfferTypesOptions = 8
)

// Enum value maps for RewardOfferTypesOptions.
var (
	RewardOfferTypesOptions_name = map[int32]string{
		0: "REWARD_OFFER_TYPES_OPTIONS_UNSPECIFIED",
		1: "REWARD_OFFER_TYPES_OPTIONS_INFINITE_OR_SALARY_TIER_2_PERCENT_CASHBACK_OFFER",
		2: "REWARD_OFFER_TYPES_OPTIONS_AA_SALARY_BAND_1_TIER_1_PERCENT_CASHBACK_OFFER",
		3: "REWARD_OFFER_TYPES_OPTIONS_AA_SALARY_BAND_2_TIER_2_PERCENT_CASHBACK_OFFER",
		4: "REWARD_OFFER_TYPES_OPTIONS_AA_SALARY_BAND_3_TIER_3_PERCENT_CASHBACK_OFFER",
		5: "REWARD_OFFER_TYPES_OPTIONS_SALARY_LITE_TIER_1_PERCENT_CASHBACK_OFFER",
		6: "REWARD_OFFER_TYPES_OPTIONS_PLUS_TIER_1_PERCENT_CASHBACK_OFFER",
		7: "REWARD_OFFER_TYPES_OPTIONS_INFINITE_TIER_2_PERCENT_CASHBACK_OFFER",
		8: "REWARD_OFFER_TYPES_OPTIONS_SALARY_TIER_2_PERCENT_CASHBACK_OFFER",
	}
	RewardOfferTypesOptions_value = map[string]int32{
		"REWARD_OFFER_TYPES_OPTIONS_UNSPECIFIED":                                      0,
		"REWARD_OFFER_TYPES_OPTIONS_INFINITE_OR_SALARY_TIER_2_PERCENT_CASHBACK_OFFER": 1,
		"REWARD_OFFER_TYPES_OPTIONS_AA_SALARY_BAND_1_TIER_1_PERCENT_CASHBACK_OFFER":   2,
		"REWARD_OFFER_TYPES_OPTIONS_AA_SALARY_BAND_2_TIER_2_PERCENT_CASHBACK_OFFER":   3,
		"REWARD_OFFER_TYPES_OPTIONS_AA_SALARY_BAND_3_TIER_3_PERCENT_CASHBACK_OFFER":   4,
		"REWARD_OFFER_TYPES_OPTIONS_SALARY_LITE_TIER_1_PERCENT_CASHBACK_OFFER":        5,
		"REWARD_OFFER_TYPES_OPTIONS_PLUS_TIER_1_PERCENT_CASHBACK_OFFER":               6,
		"REWARD_OFFER_TYPES_OPTIONS_INFINITE_TIER_2_PERCENT_CASHBACK_OFFER":           7,
		"REWARD_OFFER_TYPES_OPTIONS_SALARY_TIER_2_PERCENT_CASHBACK_OFFER":             8,
	}
)

func (x RewardOfferTypesOptions) Enum() *RewardOfferTypesOptions {
	p := new(RewardOfferTypesOptions)
	*p = x
	return p
}

func (x RewardOfferTypesOptions) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RewardOfferTypesOptions) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_data_collector_rewards_service_proto_enumTypes[0].Descriptor()
}

func (RewardOfferTypesOptions) Type() protoreflect.EnumType {
	return &file_api_cx_data_collector_rewards_service_proto_enumTypes[0]
}

func (x RewardOfferTypesOptions) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RewardOfferTypesOptions.Descriptor instead.
func (RewardOfferTypesOptions) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_data_collector_rewards_service_proto_rawDescGZIP(), []int{0}
}

// some offers like EGV can expire.
type GetRedeemedOffersRequest_ExpiryStatus int32

const (
	GetRedeemedOffersRequest_UNSPECIFIED_EXPIRY_STATUS GetRedeemedOffersRequest_ExpiryStatus = 0
	// denotes offer has expired
	GetRedeemedOffersRequest_EXPIRED GetRedeemedOffersRequest_ExpiryStatus = 2
	// denotes ofer is still active
	GetRedeemedOffersRequest_NOT_EXPIRED GetRedeemedOffersRequest_ExpiryStatus = 3
)

// Enum value maps for GetRedeemedOffersRequest_ExpiryStatus.
var (
	GetRedeemedOffersRequest_ExpiryStatus_name = map[int32]string{
		0: "UNSPECIFIED_EXPIRY_STATUS",
		2: "EXPIRED",
		3: "NOT_EXPIRED",
	}
	GetRedeemedOffersRequest_ExpiryStatus_value = map[string]int32{
		"UNSPECIFIED_EXPIRY_STATUS": 0,
		"EXPIRED":                   2,
		"NOT_EXPIRED":               3,
	}
)

func (x GetRedeemedOffersRequest_ExpiryStatus) Enum() *GetRedeemedOffersRequest_ExpiryStatus {
	p := new(GetRedeemedOffersRequest_ExpiryStatus)
	*p = x
	return p
}

func (x GetRedeemedOffersRequest_ExpiryStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetRedeemedOffersRequest_ExpiryStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_data_collector_rewards_service_proto_enumTypes[1].Descriptor()
}

func (GetRedeemedOffersRequest_ExpiryStatus) Type() protoreflect.EnumType {
	return &file_api_cx_data_collector_rewards_service_proto_enumTypes[1]
}

func (x GetRedeemedOffersRequest_ExpiryStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetRedeemedOffersRequest_ExpiryStatus.Descriptor instead.
func (GetRedeemedOffersRequest_ExpiryStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_data_collector_rewards_service_proto_rawDescGZIP(), []int{6, 0}
}

type GetRewardOffersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agent email and access token are mandatory
	// ticket id is mandatory
	Header                          *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	FetchOffersOnlyApplicableToUser bool       `protobuf:"varint,2,opt,name=fetch_offers_only_applicable_to_user,json=fetchOffersOnlyApplicableToUser,proto3" json:"fetch_offers_only_applicable_to_user,omitempty"`
	// for fetching reward offers whose active till > passed date
	Date *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=date,proto3" json:"date,omitempty"`
	// for fetching reward offers whose active since <= passed date
	FromTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=from_time,json=fromTime,proto3" json:"from_time,omitempty"`
}

func (x *GetRewardOffersRequest) Reset() {
	*x = GetRewardOffersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardOffersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardOffersRequest) ProtoMessage() {}

func (x *GetRewardOffersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardOffersRequest.ProtoReflect.Descriptor instead.
func (*GetRewardOffersRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_rewards_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetRewardOffersRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetRewardOffersRequest) GetFetchOffersOnlyApplicableToUser() bool {
	if x != nil {
		return x.FetchOffersOnlyApplicableToUser
	}
	return false
}

func (x *GetRewardOffersRequest) GetDate() *timestamppb.Timestamp {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *GetRewardOffersRequest) GetFromTime() *timestamppb.Timestamp {
	if x != nil {
		return x.FromTime
	}
	return nil
}

type GetRewardOffersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status           *rpc.Status                     `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	SherlockDeepLink *customer_auth.SherlockDeepLink `protobuf:"bytes,2,opt,name=sherlock_deep_link,json=sherlockDeepLink,proto3" json:"sherlock_deep_link,omitempty"`
	// list of reward offers
	RewardOffers []*RewardOffer `protobuf:"bytes,3,rep,name=reward_offers,json=rewardOffers,proto3" json:"reward_offers,omitempty"`
}

func (x *GetRewardOffersResponse) Reset() {
	*x = GetRewardOffersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardOffersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardOffersResponse) ProtoMessage() {}

func (x *GetRewardOffersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardOffersResponse.ProtoReflect.Descriptor instead.
func (*GetRewardOffersResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_rewards_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetRewardOffersResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetRewardOffersResponse) GetSherlockDeepLink() *customer_auth.SherlockDeepLink {
	if x != nil {
		return x.SherlockDeepLink
	}
	return nil
}

func (x *GetRewardOffersResponse) GetRewardOffers() []*RewardOffer {
	if x != nil {
		return x.RewardOffers
	}
	return nil
}

type GetOffersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agent email and access token are mandatory
	// ticket id is mandatory
	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// mode through which offer is redeemable FI_COINS/FI_CARD etc
	RedemptionMode OfferRedemptionMode `protobuf:"varint,2,opt,name=redemption_mode,json=redemptionMode,proto3,enum=cx.data_collector.rewards.OfferRedemptionMode" json:"redemption_mode,omitempty"`
	// page size is fixed for now just pass the page tokens for now
	PageContext *cx.PageContextRequest `protobuf:"bytes,3,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
	// card offer type if selected FI_CARD as offer redemption mode.
	CardOffersType casper.CardOfferType `protobuf:"varint,4,opt,name=card_offers_type,json=cardOffersType,proto3,enum=casper.CardOfferType" json:"card_offers_type,omitempty"`
	// for fetching offers whose active till > passed date
	Date *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=date,proto3" json:"date,omitempty"`
	// for fetching offers whose active since <= passed date
	FromTime *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=from_time,json=fromTime,proto3" json:"from_time,omitempty"`
}

func (x *GetOffersRequest) Reset() {
	*x = GetOffersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOffersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOffersRequest) ProtoMessage() {}

func (x *GetOffersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOffersRequest.ProtoReflect.Descriptor instead.
func (*GetOffersRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_rewards_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetOffersRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetOffersRequest) GetRedemptionMode() OfferRedemptionMode {
	if x != nil {
		return x.RedemptionMode
	}
	return OfferRedemptionMode_UNSPECIFIED_REDEMPTION_MODE
}

func (x *GetOffersRequest) GetPageContext() *cx.PageContextRequest {
	if x != nil {
		return x.PageContext
	}
	return nil
}

func (x *GetOffersRequest) GetCardOffersType() casper.CardOfferType {
	if x != nil {
		return x.CardOffersType
	}
	return casper.CardOfferType(0)
}

func (x *GetOffersRequest) GetDate() *timestamppb.Timestamp {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *GetOffersRequest) GetFromTime() *timestamppb.Timestamp {
	if x != nil {
		return x.FromTime
	}
	return nil
}

type GetOffersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status           *rpc.Status                     `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	SherlockDeepLink *customer_auth.SherlockDeepLink `protobuf:"bytes,2,opt,name=sherlock_deep_link,json=sherlockDeepLink,proto3" json:"sherlock_deep_link,omitempty"`
	// list of offers
	Offers []*Offer `protobuf:"bytes,3,rep,name=offers,proto3" json:"offers,omitempty"`
	// page size is fixed for now just pass the page tokens for now
	PageContext *cx.PageContextResponse `protobuf:"bytes,4,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
}

func (x *GetOffersResponse) Reset() {
	*x = GetOffersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOffersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOffersResponse) ProtoMessage() {}

func (x *GetOffersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOffersResponse.ProtoReflect.Descriptor instead.
func (*GetOffersResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_rewards_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetOffersResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetOffersResponse) GetSherlockDeepLink() *customer_auth.SherlockDeepLink {
	if x != nil {
		return x.SherlockDeepLink
	}
	return nil
}

func (x *GetOffersResponse) GetOffers() []*Offer {
	if x != nil {
		return x.Offers
	}
	return nil
}

func (x *GetOffersResponse) GetPageContext() *cx.PageContextResponse {
	if x != nil {
		return x.PageContext
	}
	return nil
}

type GetUserRewardsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agent email and access token are mandatory
	// ticket id is mandatory
	Header         *cx.Header             `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	RewardId       string                 `protobuf:"bytes,2,opt,name=reward_id,json=rewardId,proto3" json:"reward_id,omitempty"`
	RewardType     rewards.RewardType     `protobuf:"varint,3,opt,name=reward_type,json=rewardType,proto3,enum=rewards.RewardType" json:"reward_type,omitempty"`
	VisibilityType rewards.VisibilityType `protobuf:"varint,4,opt,name=visibility_type,json=visibilityType,proto3,enum=rewards.VisibilityType" json:"visibility_type,omitempty"`
	StartDate      *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate        *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	PageContext    *cx.PageContextRequest `protobuf:"bytes,7,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
	RewardOfferId  string                 `protobuf:"bytes,8,opt,name=reward_offer_id,json=rewardOfferId,proto3" json:"reward_offer_id,omitempty"`
}

func (x *GetUserRewardsRequest) Reset() {
	*x = GetUserRewardsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserRewardsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserRewardsRequest) ProtoMessage() {}

func (x *GetUserRewardsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserRewardsRequest.ProtoReflect.Descriptor instead.
func (*GetUserRewardsRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_rewards_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetUserRewardsRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetUserRewardsRequest) GetRewardId() string {
	if x != nil {
		return x.RewardId
	}
	return ""
}

func (x *GetUserRewardsRequest) GetRewardType() rewards.RewardType {
	if x != nil {
		return x.RewardType
	}
	return rewards.RewardType(0)
}

func (x *GetUserRewardsRequest) GetVisibilityType() rewards.VisibilityType {
	if x != nil {
		return x.VisibilityType
	}
	return rewards.VisibilityType(0)
}

func (x *GetUserRewardsRequest) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *GetUserRewardsRequest) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *GetUserRewardsRequest) GetPageContext() *cx.PageContextRequest {
	if x != nil {
		return x.PageContext
	}
	return nil
}

func (x *GetUserRewardsRequest) GetRewardOfferId() string {
	if x != nil {
		return x.RewardOfferId
	}
	return ""
}

type GetUserRewardsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status      *rpc.Status             `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	PageContext *cx.PageContextResponse `protobuf:"bytes,2,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
	// Deprecated: Marked as deprecated in api/cx/data_collector/rewards/service.proto.
	Rewards          []*rewards.Reward               `protobuf:"bytes,3,rep,name=rewards,proto3" json:"rewards,omitempty"`
	SherlockDeepLink *customer_auth.SherlockDeepLink `protobuf:"bytes,4,opt,name=sherlock_deep_link,json=sherlockDeepLink,proto3" json:"sherlock_deep_link,omitempty"`
	// cx reward msg
	RewardCxWrapperList []*RewardCxWrapper `protobuf:"bytes,5,rep,name=reward_cx_wrapper_list,json=rewardCxWrapperList,proto3" json:"reward_cx_wrapper_list,omitempty"`
}

func (x *GetUserRewardsResponse) Reset() {
	*x = GetUserRewardsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserRewardsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserRewardsResponse) ProtoMessage() {}

func (x *GetUserRewardsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserRewardsResponse.ProtoReflect.Descriptor instead.
func (*GetUserRewardsResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_rewards_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetUserRewardsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetUserRewardsResponse) GetPageContext() *cx.PageContextResponse {
	if x != nil {
		return x.PageContext
	}
	return nil
}

// Deprecated: Marked as deprecated in api/cx/data_collector/rewards/service.proto.
func (x *GetUserRewardsResponse) GetRewards() []*rewards.Reward {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *GetUserRewardsResponse) GetSherlockDeepLink() *customer_auth.SherlockDeepLink {
	if x != nil {
		return x.SherlockDeepLink
	}
	return nil
}

func (x *GetUserRewardsResponse) GetRewardCxWrapperList() []*RewardCxWrapper {
	if x != nil {
		return x.RewardCxWrapperList
	}
	return nil
}

type GetRedeemedOffersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agent email and access token are mandatory
	// ticket id is mandatory
	Header          *cx.Header                      `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	OfferType       casper.OfferType                `protobuf:"varint,2,opt,name=offer_type,json=offerType,proto3,enum=casper.OfferType" json:"offer_type,omitempty"`
	Vendor          casper.OfferVendor              `protobuf:"varint,3,opt,name=vendor,proto3,enum=casper.OfferVendor" json:"vendor,omitempty"`
	RedemptionState redemption.OfferRedemptionState `protobuf:"varint,4,opt,name=redemption_state,json=redemptionState,proto3,enum=casper.OfferRedemptionState" json:"redemption_state,omitempty"`
	FromDate        *timestamppb.Timestamp          `protobuf:"bytes,5,opt,name=from_date,json=fromDate,proto3" json:"from_date,omitempty"`
	UptoDate        *timestamppb.Timestamp          `protobuf:"bytes,6,opt,name=upto_date,json=uptoDate,proto3" json:"upto_date,omitempty"`
	// if expiry_status is not specified both expired
	// and not expired offers will be fetched.
	ExpiryStatus    GetRedeemedOffersRequest_ExpiryStatus `protobuf:"varint,7,opt,name=expiry_status,json=expiryStatus,proto3,enum=cx.data_collector.rewards.GetRedeemedOffersRequest_ExpiryStatus" json:"expiry_status,omitempty"`
	RedeemedOfferId string                                `protobuf:"bytes,8,opt,name=redeemed_offer_id,json=redeemedOfferId,proto3" json:"redeemed_offer_id,omitempty"`
	PageContext     *cx.PageContextRequest                `protobuf:"bytes,9,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
}

func (x *GetRedeemedOffersRequest) Reset() {
	*x = GetRedeemedOffersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRedeemedOffersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRedeemedOffersRequest) ProtoMessage() {}

func (x *GetRedeemedOffersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRedeemedOffersRequest.ProtoReflect.Descriptor instead.
func (*GetRedeemedOffersRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_rewards_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetRedeemedOffersRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetRedeemedOffersRequest) GetOfferType() casper.OfferType {
	if x != nil {
		return x.OfferType
	}
	return casper.OfferType(0)
}

func (x *GetRedeemedOffersRequest) GetVendor() casper.OfferVendor {
	if x != nil {
		return x.Vendor
	}
	return casper.OfferVendor(0)
}

func (x *GetRedeemedOffersRequest) GetRedemptionState() redemption.OfferRedemptionState {
	if x != nil {
		return x.RedemptionState
	}
	return redemption.OfferRedemptionState(0)
}

func (x *GetRedeemedOffersRequest) GetFromDate() *timestamppb.Timestamp {
	if x != nil {
		return x.FromDate
	}
	return nil
}

func (x *GetRedeemedOffersRequest) GetUptoDate() *timestamppb.Timestamp {
	if x != nil {
		return x.UptoDate
	}
	return nil
}

func (x *GetRedeemedOffersRequest) GetExpiryStatus() GetRedeemedOffersRequest_ExpiryStatus {
	if x != nil {
		return x.ExpiryStatus
	}
	return GetRedeemedOffersRequest_UNSPECIFIED_EXPIRY_STATUS
}

func (x *GetRedeemedOffersRequest) GetRedeemedOfferId() string {
	if x != nil {
		return x.RedeemedOfferId
	}
	return ""
}

func (x *GetRedeemedOffersRequest) GetPageContext() *cx.PageContextRequest {
	if x != nil {
		return x.PageContext
	}
	return nil
}

type GetRedeemedOffersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status      *rpc.Status             `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	PageContext *cx.PageContextResponse `protobuf:"bytes,2,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
	// Deprecated: Marked as deprecated in api/cx/data_collector/rewards/service.proto.
	RedeemedOffers   []*redemption.RedeemedOffer     `protobuf:"bytes,3,rep,name=redeemed_offers,json=redeemedOffers,proto3" json:"redeemed_offers,omitempty"`
	SherlockDeepLink *customer_auth.SherlockDeepLink `protobuf:"bytes,4,opt,name=sherlock_deep_link,json=sherlockDeepLink,proto3" json:"sherlock_deep_link,omitempty"`
	// cx redeemed offer msg
	RedeemedOfferCxWrapperList []*RedeemedOfferCxWrapper `protobuf:"bytes,5,rep,name=redeemed_offer_cx_wrapper_list,json=redeemedOfferCxWrapperList,proto3" json:"redeemed_offer_cx_wrapper_list,omitempty"`
}

func (x *GetRedeemedOffersResponse) Reset() {
	*x = GetRedeemedOffersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRedeemedOffersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRedeemedOffersResponse) ProtoMessage() {}

func (x *GetRedeemedOffersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRedeemedOffersResponse.ProtoReflect.Descriptor instead.
func (*GetRedeemedOffersResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_rewards_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetRedeemedOffersResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetRedeemedOffersResponse) GetPageContext() *cx.PageContextResponse {
	if x != nil {
		return x.PageContext
	}
	return nil
}

// Deprecated: Marked as deprecated in api/cx/data_collector/rewards/service.proto.
func (x *GetRedeemedOffersResponse) GetRedeemedOffers() []*redemption.RedeemedOffer {
	if x != nil {
		return x.RedeemedOffers
	}
	return nil
}

func (x *GetRedeemedOffersResponse) GetSherlockDeepLink() *customer_auth.SherlockDeepLink {
	if x != nil {
		return x.SherlockDeepLink
	}
	return nil
}

func (x *GetRedeemedOffersResponse) GetRedeemedOfferCxWrapperList() []*RedeemedOfferCxWrapper {
	if x != nil {
		return x.RedeemedOfferCxWrapperList
	}
	return nil
}

type GetRewardOfferByIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ticket id is mandatory
	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// reward offer id for which details needs to be fetched
	RewardOfferId string `protobuf:"bytes,2,opt,name=reward_offer_id,json=rewardOfferId,proto3" json:"reward_offer_id,omitempty"`
}

func (x *GetRewardOfferByIdRequest) Reset() {
	*x = GetRewardOfferByIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardOfferByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardOfferByIdRequest) ProtoMessage() {}

func (x *GetRewardOfferByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardOfferByIdRequest.ProtoReflect.Descriptor instead.
func (*GetRewardOfferByIdRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_rewards_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetRewardOfferByIdRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetRewardOfferByIdRequest) GetRewardOfferId() string {
	if x != nil {
		return x.RewardOfferId
	}
	return ""
}

type GetRewardOfferByIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// will return
	// OK for success
	// Internal for server errors
	Status           *rpc.Status                     `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	SherlockDeepLink *customer_auth.SherlockDeepLink `protobuf:"bytes,2,opt,name=sherlock_deep_link,json=sherlockDeepLink,proto3" json:"sherlock_deep_link,omitempty"`
	// Reward offer details
	RewardOffer *RewardOffer `protobuf:"bytes,3,opt,name=reward_offer,json=rewardOffer,proto3" json:"reward_offer,omitempty"`
}

func (x *GetRewardOfferByIdResponse) Reset() {
	*x = GetRewardOfferByIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardOfferByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardOfferByIdResponse) ProtoMessage() {}

func (x *GetRewardOfferByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardOfferByIdResponse.ProtoReflect.Descriptor instead.
func (*GetRewardOfferByIdResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_rewards_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetRewardOfferByIdResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetRewardOfferByIdResponse) GetSherlockDeepLink() *customer_auth.SherlockDeepLink {
	if x != nil {
		return x.SherlockDeepLink
	}
	return nil
}

func (x *GetRewardOfferByIdResponse) GetRewardOffer() *RewardOffer {
	if x != nil {
		return x.RewardOffer
	}
	return nil
}

type GetExchangerOffersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agent email and access token are mandatory
	// ticket id is mandatory
	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// for fetching reward offers whose active till > passed date
	Date *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=date,proto3" json:"date,omitempty"`
	// for fetching reward offers whose active since <= passed date
	FromTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=from_time,json=fromTime,proto3" json:"from_time,omitempty"`
}

func (x *GetExchangerOffersRequest) Reset() {
	*x = GetExchangerOffersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangerOffersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangerOffersRequest) ProtoMessage() {}

func (x *GetExchangerOffersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangerOffersRequest.ProtoReflect.Descriptor instead.
func (*GetExchangerOffersRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_rewards_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetExchangerOffersRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetExchangerOffersRequest) GetDate() *timestamppb.Timestamp {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *GetExchangerOffersRequest) GetFromTime() *timestamppb.Timestamp {
	if x != nil {
		return x.FromTime
	}
	return nil
}

type GetExchangerOffersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status           *rpc.Status                     `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	SherlockDeepLink *customer_auth.SherlockDeepLink `protobuf:"bytes,2,opt,name=sherlock_deep_link,json=sherlockDeepLink,proto3" json:"sherlock_deep_link,omitempty"`
	// list of exchanger offers
	ExchangerOffers []*ExchangerOffer `protobuf:"bytes,3,rep,name=exchanger_offers,json=exchangerOffers,proto3" json:"exchanger_offers,omitempty"`
}

func (x *GetExchangerOffersResponse) Reset() {
	*x = GetExchangerOffersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangerOffersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangerOffersResponse) ProtoMessage() {}

func (x *GetExchangerOffersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangerOffersResponse.ProtoReflect.Descriptor instead.
func (*GetExchangerOffersResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_rewards_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetExchangerOffersResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetExchangerOffersResponse) GetSherlockDeepLink() *customer_auth.SherlockDeepLink {
	if x != nil {
		return x.SherlockDeepLink
	}
	return nil
}

func (x *GetExchangerOffersResponse) GetExchangerOffers() []*ExchangerOffer {
	if x != nil {
		return x.ExchangerOffers
	}
	return nil
}

type GetExchangerOfferOrdersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agent email and access token are mandatory
	// ticket id is mandatory
	Header          *cx.Header                         `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	RedemptionState exchanger.ExchangerOfferOrderState `protobuf:"varint,2,opt,name=redemption_state,json=redemptionState,proto3,enum=casper.exchanger.ExchangerOfferOrderState" json:"redemption_state,omitempty"`
	FromDate        *timestamppb.Timestamp             `protobuf:"bytes,3,opt,name=from_date,json=fromDate,proto3" json:"from_date,omitempty"`
	UptoDate        *timestamppb.Timestamp             `protobuf:"bytes,4,opt,name=upto_date,json=uptoDate,proto3" json:"upto_date,omitempty"`
	PageContext     *cx.PageContextRequest             `protobuf:"bytes,5,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
}

func (x *GetExchangerOfferOrdersRequest) Reset() {
	*x = GetExchangerOfferOrdersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangerOfferOrdersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangerOfferOrdersRequest) ProtoMessage() {}

func (x *GetExchangerOfferOrdersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangerOfferOrdersRequest.ProtoReflect.Descriptor instead.
func (*GetExchangerOfferOrdersRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_rewards_service_proto_rawDescGZIP(), []int{12}
}

func (x *GetExchangerOfferOrdersRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetExchangerOfferOrdersRequest) GetRedemptionState() exchanger.ExchangerOfferOrderState {
	if x != nil {
		return x.RedemptionState
	}
	return exchanger.ExchangerOfferOrderState(0)
}

func (x *GetExchangerOfferOrdersRequest) GetFromDate() *timestamppb.Timestamp {
	if x != nil {
		return x.FromDate
	}
	return nil
}

func (x *GetExchangerOfferOrdersRequest) GetUptoDate() *timestamppb.Timestamp {
	if x != nil {
		return x.UptoDate
	}
	return nil
}

func (x *GetExchangerOfferOrdersRequest) GetPageContext() *cx.PageContextRequest {
	if x != nil {
		return x.PageContext
	}
	return nil
}

type GetExchangerOfferOrdersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status      *rpc.Status             `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	PageContext *cx.PageContextResponse `protobuf:"bytes,2,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
	// Deprecated: Marked as deprecated in api/cx/data_collector/rewards/service.proto.
	ExchangerOfferOrders []*exchanger.ExchangerOfferOrder `protobuf:"bytes,3,rep,name=exchanger_offer_orders,json=exchangerOfferOrders,proto3" json:"exchanger_offer_orders,omitempty"`
	SherlockDeepLink     *customer_auth.SherlockDeepLink  `protobuf:"bytes,4,opt,name=sherlock_deep_link,json=sherlockDeepLink,proto3" json:"sherlock_deep_link,omitempty"`
	// cx ExchangerOfferOrders proto
	ExchangerOfferOrderCxWrapperList []*ExchangerOfferOrderCxWrapper `protobuf:"bytes,5,rep,name=exchanger_offer_order_cx_wrapper_list,json=exchangerOfferOrderCxWrapperList,proto3" json:"exchanger_offer_order_cx_wrapper_list,omitempty"`
}

func (x *GetExchangerOfferOrdersResponse) Reset() {
	*x = GetExchangerOfferOrdersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangerOfferOrdersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangerOfferOrdersResponse) ProtoMessage() {}

func (x *GetExchangerOfferOrdersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangerOfferOrdersResponse.ProtoReflect.Descriptor instead.
func (*GetExchangerOfferOrdersResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_rewards_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetExchangerOfferOrdersResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetExchangerOfferOrdersResponse) GetPageContext() *cx.PageContextResponse {
	if x != nil {
		return x.PageContext
	}
	return nil
}

// Deprecated: Marked as deprecated in api/cx/data_collector/rewards/service.proto.
func (x *GetExchangerOfferOrdersResponse) GetExchangerOfferOrders() []*exchanger.ExchangerOfferOrder {
	if x != nil {
		return x.ExchangerOfferOrders
	}
	return nil
}

func (x *GetExchangerOfferOrdersResponse) GetSherlockDeepLink() *customer_auth.SherlockDeepLink {
	if x != nil {
		return x.SherlockDeepLink
	}
	return nil
}

func (x *GetExchangerOfferOrdersResponse) GetExchangerOfferOrderCxWrapperList() []*ExchangerOfferOrderCxWrapper {
	if x != nil {
		return x.ExchangerOfferOrderCxWrapperList
	}
	return nil
}

type GetRewardUnitsUtilisationForActorAndOfferInMonthRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agent email and access token are mandatory
	// ticket id is mandatory
	Header        *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	RewardOfferId string     `protobuf:"bytes,2,opt,name=reward_offer_id,json=rewardOfferId,proto3" json:"reward_offer_id,omitempty"`
	// any date of the month in which utilization is to be checked
	Date *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=date,proto3" json:"date,omitempty"`
}

func (x *GetRewardUnitsUtilisationForActorAndOfferInMonthRequest) Reset() {
	*x = GetRewardUnitsUtilisationForActorAndOfferInMonthRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardUnitsUtilisationForActorAndOfferInMonthRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardUnitsUtilisationForActorAndOfferInMonthRequest) ProtoMessage() {}

func (x *GetRewardUnitsUtilisationForActorAndOfferInMonthRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardUnitsUtilisationForActorAndOfferInMonthRequest.ProtoReflect.Descriptor instead.
func (*GetRewardUnitsUtilisationForActorAndOfferInMonthRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_rewards_service_proto_rawDescGZIP(), []int{14}
}

func (x *GetRewardUnitsUtilisationForActorAndOfferInMonthRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetRewardUnitsUtilisationForActorAndOfferInMonthRequest) GetRewardOfferId() string {
	if x != nil {
		return x.RewardOfferId
	}
	return ""
}

func (x *GetRewardUnitsUtilisationForActorAndOfferInMonthRequest) GetDate() *timestamppb.Timestamp {
	if x != nil {
		return x.Date
	}
	return nil
}

type GetRewardUnitsUtilisationForActorAndOfferInMonthResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                               *rpc.Status                                                                         `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	UnitsUtilizedAndCappingList          []*GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCapping `protobuf:"bytes,2,rep,name=units_utilized_and_capping_list,json=unitsUtilizedAndCappingList,proto3" json:"units_utilized_and_capping_list,omitempty"`
	RewardCount                          uint32                                                                              `protobuf:"varint,3,opt,name=reward_count,json=rewardCount,proto3" json:"reward_count,omitempty"`
	RewardTypeUtilizationAndCappingTable *webui.Table                                                                        `protobuf:"bytes,4,opt,name=reward_type_utilization_and_capping_table,json=rewardTypeUtilizationAndCappingTable,proto3" json:"reward_type_utilization_and_capping_table,omitempty"`
}

func (x *GetRewardUnitsUtilisationForActorAndOfferInMonthResponse) Reset() {
	*x = GetRewardUnitsUtilisationForActorAndOfferInMonthResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardUnitsUtilisationForActorAndOfferInMonthResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardUnitsUtilisationForActorAndOfferInMonthResponse) ProtoMessage() {}

func (x *GetRewardUnitsUtilisationForActorAndOfferInMonthResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardUnitsUtilisationForActorAndOfferInMonthResponse.ProtoReflect.Descriptor instead.
func (*GetRewardUnitsUtilisationForActorAndOfferInMonthResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_rewards_service_proto_rawDescGZIP(), []int{15}
}

func (x *GetRewardUnitsUtilisationForActorAndOfferInMonthResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetRewardUnitsUtilisationForActorAndOfferInMonthResponse) GetUnitsUtilizedAndCappingList() []*GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCapping {
	if x != nil {
		return x.UnitsUtilizedAndCappingList
	}
	return nil
}

func (x *GetRewardUnitsUtilisationForActorAndOfferInMonthResponse) GetRewardCount() uint32 {
	if x != nil {
		return x.RewardCount
	}
	return 0
}

func (x *GetRewardUnitsUtilisationForActorAndOfferInMonthResponse) GetRewardTypeUtilizationAndCappingTable() *webui.Table {
	if x != nil {
		return x.RewardTypeUtilizationAndCappingTable
	}
	return nil
}

type GetRewardOfferTypesOptionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agent email and access token are mandatory
	// ticket id is mandatory
	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
}

func (x *GetRewardOfferTypesOptionsRequest) Reset() {
	*x = GetRewardOfferTypesOptionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardOfferTypesOptionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardOfferTypesOptionsRequest) ProtoMessage() {}

func (x *GetRewardOfferTypesOptionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardOfferTypesOptionsRequest.ProtoReflect.Descriptor instead.
func (*GetRewardOfferTypesOptionsRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_rewards_service_proto_rawDescGZIP(), []int{16}
}

func (x *GetRewardOfferTypesOptionsRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

type GetRewardOfferTypesOptionsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// key will be enum name as string and value will be actual text that we want to show in dropdown
	OfferTypes map[string]string `protobuf:"bytes,2,rep,name=offer_types,json=offerTypes,proto3" json:"offer_types,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetRewardOfferTypesOptionsResponse) Reset() {
	*x = GetRewardOfferTypesOptionsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardOfferTypesOptionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardOfferTypesOptionsResponse) ProtoMessage() {}

func (x *GetRewardOfferTypesOptionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardOfferTypesOptionsResponse.ProtoReflect.Descriptor instead.
func (*GetRewardOfferTypesOptionsResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_rewards_service_proto_rawDescGZIP(), []int{17}
}

func (x *GetRewardOfferTypesOptionsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetRewardOfferTypesOptionsResponse) GetOfferTypes() map[string]string {
	if x != nil {
		return x.OfferTypes
	}
	return nil
}

type GetRewardDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agent email and access token are mandatory
	// ticket id is mandatory
	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// reward offer type
	RewardOfferType string `protobuf:"bytes,2,opt,name=reward_offer_type,json=rewardOfferType,proto3" json:"reward_offer_type,omitempty"`
	// from and upto date - max date range allowed is 31 days
	FromDate *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=from_date,json=fromDate,proto3" json:"from_date,omitempty"`
	ToDate   *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=to_date,json=toDate,proto3" json:"to_date,omitempty"`
	// external txn id to check reward txn eligibility.
	ExternalTxnId string `protobuf:"bytes,5,opt,name=external_txn_id,json=externalTxnId,proto3" json:"external_txn_id,omitempty"`
}

func (x *GetRewardDetailsRequest) Reset() {
	*x = GetRewardDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardDetailsRequest) ProtoMessage() {}

func (x *GetRewardDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetRewardDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_rewards_service_proto_rawDescGZIP(), []int{18}
}

func (x *GetRewardDetailsRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetRewardDetailsRequest) GetRewardOfferType() string {
	if x != nil {
		return x.RewardOfferType
	}
	return ""
}

func (x *GetRewardDetailsRequest) GetFromDate() *timestamppb.Timestamp {
	if x != nil {
		return x.FromDate
	}
	return nil
}

func (x *GetRewardDetailsRequest) GetToDate() *timestamppb.Timestamp {
	if x != nil {
		return x.ToDate
	}
	return nil
}

func (x *GetRewardDetailsRequest) GetExternalTxnId() string {
	if x != nil {
		return x.ExternalTxnId
	}
	return ""
}

type GetRewardDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// table contains the reward details for given request
	// Columns Headers -
	// 1. Reward offer id
	// 2. Txn Id/ Ref Id
	// 3. Projected Reward Cash Back
	// 4. Actual Reward Cash Back
	// 5. Projected Reward Fi Coins
	// 6. Actual Reward Fi Coins
	// 7. Timestamp
	RewardDetailsTable *webui.Table `protobuf:"bytes,4,opt,name=reward_details_table,json=rewardDetailsTable,proto3" json:"reward_details_table,omitempty"`
}

func (x *GetRewardDetailsResponse) Reset() {
	*x = GetRewardDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardDetailsResponse) ProtoMessage() {}

func (x *GetRewardDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetRewardDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_rewards_service_proto_rawDescGZIP(), []int{19}
}

func (x *GetRewardDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetRewardDetailsResponse) GetRewardDetailsTable() *webui.Table {
	if x != nil {
		return x.RewardDetailsTable
	}
	return nil
}

type GetFiStoreRedemptionsDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agent email and access token are mandatory
	// ticket id is mandatory
	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// vendor reference id of the order
	OrderId string `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// category of the fi store orders
	Category external_vendor_redemption.Category `protobuf:"varint,3,opt,name=category,proto3,enum=casper.external_vendor_redemption.Category" json:"category,omitempty"`
	// from and upto date - time range window of created_at
	FromDate *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=from_date,json=fromDate,proto3" json:"from_date,omitempty"`
	ToDate   *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=to_date,json=toDate,proto3" json:"to_date,omitempty"`
	// page context for pagination
	PageContext *cx.PageContextRequest `protobuf:"bytes,6,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
}

func (x *GetFiStoreRedemptionsDetailsRequest) Reset() {
	*x = GetFiStoreRedemptionsDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFiStoreRedemptionsDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFiStoreRedemptionsDetailsRequest) ProtoMessage() {}

func (x *GetFiStoreRedemptionsDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFiStoreRedemptionsDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetFiStoreRedemptionsDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_rewards_service_proto_rawDescGZIP(), []int{20}
}

func (x *GetFiStoreRedemptionsDetailsRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetFiStoreRedemptionsDetailsRequest) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *GetFiStoreRedemptionsDetailsRequest) GetCategory() external_vendor_redemption.Category {
	if x != nil {
		return x.Category
	}
	return external_vendor_redemption.Category(0)
}

func (x *GetFiStoreRedemptionsDetailsRequest) GetFromDate() *timestamppb.Timestamp {
	if x != nil {
		return x.FromDate
	}
	return nil
}

func (x *GetFiStoreRedemptionsDetailsRequest) GetToDate() *timestamppb.Timestamp {
	if x != nil {
		return x.ToDate
	}
	return nil
}

func (x *GetFiStoreRedemptionsDetailsRequest) GetPageContext() *cx.PageContextRequest {
	if x != nil {
		return x.PageContext
	}
	return nil
}

type GetFiStoreRedemptionsDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// table contains the fi store details for given request
	// for each tableRow, id of the redemption is sent in meta field.
	FiStoreRewardDetailsTable *webui.Table `protobuf:"bytes,2,opt,name=fi_store_reward_details_table,json=fiStoreRewardDetailsTable,proto3" json:"fi_store_reward_details_table,omitempty"`
	// page context for pagination
	PageContext *cx.PageContextResponse `protobuf:"bytes,3,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
}

func (x *GetFiStoreRedemptionsDetailsResponse) Reset() {
	*x = GetFiStoreRedemptionsDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFiStoreRedemptionsDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFiStoreRedemptionsDetailsResponse) ProtoMessage() {}

func (x *GetFiStoreRedemptionsDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFiStoreRedemptionsDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetFiStoreRedemptionsDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_rewards_service_proto_rawDescGZIP(), []int{21}
}

func (x *GetFiStoreRedemptionsDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetFiStoreRedemptionsDetailsResponse) GetFiStoreRewardDetailsTable() *webui.Table {
	if x != nil {
		return x.FiStoreRewardDetailsTable
	}
	return nil
}

func (x *GetFiStoreRedemptionsDetailsResponse) GetPageContext() *cx.PageContextResponse {
	if x != nil {
		return x.PageContext
	}
	return nil
}

type GetFiStoreRedemptionAdditionalDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agent email and access token are mandatory
	// ticket id is mandatory
	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// id of the fi store redemption
	RedemptionId string `protobuf:"bytes,2,opt,name=redemption_id,json=redemptionId,proto3" json:"redemption_id,omitempty"`
}

func (x *GetFiStoreRedemptionAdditionalDetailsRequest) Reset() {
	*x = GetFiStoreRedemptionAdditionalDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFiStoreRedemptionAdditionalDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFiStoreRedemptionAdditionalDetailsRequest) ProtoMessage() {}

func (x *GetFiStoreRedemptionAdditionalDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFiStoreRedemptionAdditionalDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetFiStoreRedemptionAdditionalDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_rewards_service_proto_rawDescGZIP(), []int{22}
}

func (x *GetFiStoreRedemptionAdditionalDetailsRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetFiStoreRedemptionAdditionalDetailsRequest) GetRedemptionId() string {
	if x != nil {
		return x.RedemptionId
	}
	return ""
}

type GetFiStoreRedemptionAdditionalDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// contains label value pairs of fi store redemption details.
	RewardDetails []*webui.LabelValueV2 `protobuf:"bytes,2,rep,name=reward_details,json=rewardDetails,proto3" json:"reward_details,omitempty"`
}

func (x *GetFiStoreRedemptionAdditionalDetailsResponse) Reset() {
	*x = GetFiStoreRedemptionAdditionalDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFiStoreRedemptionAdditionalDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFiStoreRedemptionAdditionalDetailsResponse) ProtoMessage() {}

func (x *GetFiStoreRedemptionAdditionalDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFiStoreRedemptionAdditionalDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetFiStoreRedemptionAdditionalDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_rewards_service_proto_rawDescGZIP(), []int{23}
}

func (x *GetFiStoreRedemptionAdditionalDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetFiStoreRedemptionAdditionalDetailsResponse) GetRewardDetails() []*webui.LabelValueV2 {
	if x != nil {
		return x.RewardDetails
	}
	return nil
}

type GetFiCoinsFiPointsDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agent email and access token are mandatory
	// ticket id is mandatory
	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
}

func (x *GetFiCoinsFiPointsDetailsRequest) Reset() {
	*x = GetFiCoinsFiPointsDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFiCoinsFiPointsDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFiCoinsFiPointsDetailsRequest) ProtoMessage() {}

func (x *GetFiCoinsFiPointsDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFiCoinsFiPointsDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetFiCoinsFiPointsDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_rewards_service_proto_rawDescGZIP(), []int{24}
}

func (x *GetFiCoinsFiPointsDetailsRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

type GetFiCoinsFiPointsDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Total Fi Coins migrated to Fi Points (31st July)
	FiCoinsMigrated int32 `protobuf:"varint,2,opt,name=fi_coins_migrated,json=fiCoinsMigrated,proto3" json:"fi_coins_migrated,omitempty"`
	// Total Fi Points migrated from Fi Coins (31st July)
	FiPointsMigrated int32 `protobuf:"varint,3,opt,name=fi_points_migrated,json=fiPointsMigrated,proto3" json:"fi_points_migrated,omitempty"`
	// Current Fi Points balance
	FiPointsCurrent int32 `protobuf:"varint,4,opt,name=fi_points_current,json=fiPointsCurrent,proto3" json:"fi_points_current,omitempty"`
	// Conversion rule text (i.e. from FiCoins to INR and FiPoints to INR) based on user's credit card history.
	ConversionRule string `protobuf:"bytes,5,opt,name=conversion_rule,json=conversionRule,proto3" json:"conversion_rule,omitempty"`
}

func (x *GetFiCoinsFiPointsDetailsResponse) Reset() {
	*x = GetFiCoinsFiPointsDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFiCoinsFiPointsDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFiCoinsFiPointsDetailsResponse) ProtoMessage() {}

func (x *GetFiCoinsFiPointsDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFiCoinsFiPointsDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetFiCoinsFiPointsDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_rewards_service_proto_rawDescGZIP(), []int{25}
}

func (x *GetFiCoinsFiPointsDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetFiCoinsFiPointsDetailsResponse) GetFiCoinsMigrated() int32 {
	if x != nil {
		return x.FiCoinsMigrated
	}
	return 0
}

func (x *GetFiCoinsFiPointsDetailsResponse) GetFiPointsMigrated() int32 {
	if x != nil {
		return x.FiPointsMigrated
	}
	return 0
}

func (x *GetFiCoinsFiPointsDetailsResponse) GetFiPointsCurrent() int32 {
	if x != nil {
		return x.FiPointsCurrent
	}
	return 0
}

func (x *GetFiCoinsFiPointsDetailsResponse) GetConversionRule() string {
	if x != nil {
		return x.ConversionRule
	}
	return ""
}

// number of units utilized for the particular reward type
type GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCapping struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardType    rewards.RewardType `protobuf:"varint,1,opt,name=reward_type,json=rewardType,proto3,enum=rewards.RewardType" json:"reward_type,omitempty"`
	UnitsUtilized uint32             `protobuf:"varint,2,opt,name=units_utilized,json=unitsUtilized,proto3" json:"units_utilized,omitempty"`
	Capping       uint32             `protobuf:"varint,3,opt,name=capping,proto3" json:"capping,omitempty"`
}

func (x *GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCapping) Reset() {
	*x = GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCapping{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCapping) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCapping) ProtoMessage() {
}

func (x *GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCapping) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_rewards_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCapping.ProtoReflect.Descriptor instead.
func (*GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCapping) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_rewards_service_proto_rawDescGZIP(), []int{15, 0}
}

func (x *GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCapping) GetRewardType() rewards.RewardType {
	if x != nil {
		return x.RewardType
	}
	return rewards.RewardType(0)
}

func (x *GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCapping) GetUnitsUtilized() uint32 {
	if x != nil {
		return x.UnitsUtilized
	}
	return 0
}

func (x *GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCapping) GetCapping() uint32 {
	if x != nil {
		return x.Capping
	}
	return 0
}

var File_api_cx_data_collector_rewards_service_proto protoreflect.FileDescriptor

var file_api_cx_data_collector_rewards_service_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x63,
	0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x1a, 0x30, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61,
	0x73, 0x70, 0x65, 0x72, 0x2f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2f, 0x65,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x46, 0x61, 0x70, 0x69, 0x2f,
	0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x5f, 0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x5f, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x2a, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2f, 0x72,
	0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d,
	0x65, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28,
	0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x61, 0x75, 0x74, 0x68, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x61, 0x75,
	0x74, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78,
	0x2f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2f,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x61, 0x70, 0x69, 0x2f,
	0x63, 0x78, 0x2f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70,
	0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f,
	0x77, 0x65, 0x62, 0x75, 0x69, 0x2f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x77,
	0x65, 0x62, 0x75, 0x69, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xfe, 0x01, 0x0a, 0x16, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x78, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x4d, 0x0a, 0x24, 0x66, 0x65, 0x74, 0x63, 0x68, 0x5f, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x73, 0x5f, 0x6f, 0x6e, 0x6c, 0x79, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x1f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x4f, 0x6e, 0x6c,
	0x79, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x6f, 0x55, 0x73, 0x65,
	0x72, 0x12, 0x2e, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x65, 0x12, 0x37, 0x0a, 0x09, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x08, 0x66, 0x72, 0x6f, 0x6d, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xdd, 0x01, 0x0a, 0x17, 0x47,
	0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x50, 0x0a, 0x12, 0x73,
	0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x64, 0x65, 0x65, 0x70, 0x5f, 0x6c, 0x69, 0x6e,
	0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x53, 0x68, 0x65, 0x72, 0x6c,
	0x6f, 0x63, 0x6b, 0x44, 0x65, 0x65, 0x70, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x10, 0x73, 0x68, 0x65,
	0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x65, 0x70, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x4b, 0x0a,
	0x0d, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x0c, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x22, 0xfe, 0x02, 0x0a, 0x10, 0x47,
	0x65, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x2c, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0a, 0x2e, 0x63, 0x78, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x57, 0x0a,
	0x0f, 0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x2e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x0e, 0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x39, 0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63,
	0x78, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x52, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x12, 0x3f, 0x0a, 0x10, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x61,
	0x73, 0x70, 0x65, 0x72, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0e, 0x63, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x2e, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x65, 0x12, 0x37, 0x0a, 0x09, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x08, 0x66, 0x72, 0x6f, 0x6d, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x80, 0x02, 0x0a, 0x11,
	0x47, 0x65, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x50, 0x0a, 0x12, 0x73, 0x68, 0x65, 0x72, 0x6c, 0x6f,
	0x63, 0x6b, 0x5f, 0x64, 0x65, 0x65, 0x70, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x53, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x65,
	0x65, 0x70, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x10, 0x73, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b,
	0x44, 0x65, 0x65, 0x70, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x38, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x2e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x73, 0x12, 0x3a, 0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x78, 0x2e, 0x50, 0x61,
	0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x52, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x22, 0xaf,
	0x03, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x78, 0x2e, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x40, 0x0a, 0x0f, 0x76, 0x69, 0x73,
	0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x17, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x56, 0x69, 0x73,
	0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x76, 0x69, 0x73,
	0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x35, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x39, 0x0a,
	0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x78, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0b, 0x70, 0x61, 0x67,
	0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64,
	0x22, 0xdb, 0x02, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x3a, 0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x78, 0x2e, 0x50, 0x61, 0x67, 0x65,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52,
	0x0b, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x2d, 0x0a, 0x07,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x12, 0x50, 0x0a, 0x12, 0x73,
	0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x64, 0x65, 0x65, 0x70, 0x5f, 0x6c, 0x69, 0x6e,
	0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x53, 0x68, 0x65, 0x72, 0x6c,
	0x6f, 0x63, 0x6b, 0x44, 0x65, 0x65, 0x70, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x10, 0x73, 0x68, 0x65,
	0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x65, 0x70, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x5f, 0x0a,
	0x16, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x63, 0x78, 0x5f, 0x77, 0x72, 0x61, 0x70, 0x70,
	0x65, 0x72, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x43, 0x78, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x52, 0x13, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x43, 0x78, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xfd,
	0x04, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x65, 0x64, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x78,
	0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x30, 0x0a, 0x0a, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e,
	0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x09, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a, 0x06, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x61,
	0x73, 0x70, 0x65, 0x72, 0x2e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x47, 0x0a, 0x10, 0x72, 0x65, 0x64, 0x65,
	0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x52, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x0f, 0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x37, 0x0a, 0x09, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x08, 0x66, 0x72, 0x6f, 0x6d, 0x44, 0x61, 0x74, 0x65, 0x12, 0x37, 0x0a, 0x09, 0x75, 0x70,
	0x74, 0x6f, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x75, 0x70, 0x74, 0x6f, 0x44,
	0x61, 0x74, 0x65, 0x12, 0x65, 0x0a, 0x0d, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x40, 0x2e, 0x63, 0x78, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d,
	0x65, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x45, 0x78, 0x70, 0x69, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x72, 0x65,
	0x64, 0x65, 0x65, 0x6d, 0x65, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x65, 0x64, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63,
	0x78, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x52, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x22, 0x4b, 0x0a, 0x0c, 0x45, 0x78, 0x70, 0x69, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x1d, 0x0a, 0x19, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x00,
	0x12, 0x0b, 0x0a, 0x07, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0x02, 0x12, 0x0f, 0x0a,
	0x0b, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0x03, 0x22, 0x89,
	0x03, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x65, 0x64, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x3a, 0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x78, 0x2e, 0x50, 0x61, 0x67,
	0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x52, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x42, 0x0a,
	0x0f, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x65, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e,
	0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x65, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x42, 0x02, 0x18,
	0x01, 0x52, 0x0e, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x65, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x73, 0x12, 0x50, 0x0a, 0x12, 0x73, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x64, 0x65,
	0x65, 0x70, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x63, 0x78, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x61, 0x75, 0x74, 0x68,
	0x2e, 0x53, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x65, 0x70, 0x4c, 0x69, 0x6e,
	0x6b, 0x52, 0x10, 0x73, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x65, 0x70, 0x4c,
	0x69, 0x6e, 0x6b, 0x12, 0x75, 0x0a, 0x1e, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x65, 0x64, 0x5f,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x63, 0x78, 0x5f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x63, 0x78,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x65, 0x64,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x43, 0x78, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x52, 0x1a,
	0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x65, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x43, 0x78, 0x57,
	0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x71, 0x0a, 0x19, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x42, 0x79, 0x49, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x78, 0x2e, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x0f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x22, 0xde, 0x01,
	0x0a, 0x1a, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x50, 0x0a, 0x12, 0x73, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x64, 0x65,
	0x65, 0x70, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x63, 0x78, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x61, 0x75, 0x74, 0x68,
	0x2e, 0x53, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x65, 0x70, 0x4c, 0x69, 0x6e,
	0x6b, 0x52, 0x10, 0x73, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x65, 0x70, 0x4c,
	0x69, 0x6e, 0x6b, 0x12, 0x49, 0x0a, 0x0c, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x78, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x52, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x22, 0xb2,
	0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63,
	0x78, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02,
	0x10, 0x01, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2e, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x37, 0x0a, 0x09, 0x66, 0x72,
	0x6f, 0x6d, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x66, 0x72, 0x6f, 0x6d, 0x54,
	0x69, 0x6d, 0x65, 0x22, 0xe9, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x50, 0x0a, 0x12, 0x73, 0x68, 0x65, 0x72, 0x6c,
	0x6f, 0x63, 0x6b, 0x5f, 0x64, 0x65, 0x65, 0x70, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x53, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x44,
	0x65, 0x65, 0x70, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x10, 0x73, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63,
	0x6b, 0x44, 0x65, 0x65, 0x70, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x54, 0x0a, 0x10, 0x65, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e,
	0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x0f,
	0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x22,
	0xd2, 0x02, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x2c, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x78, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x55, 0x0a, 0x10, 0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x63, 0x61, 0x73,
	0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0f, 0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x37, 0x0a, 0x09, 0x66, 0x72, 0x6f, 0x6d, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x66, 0x72, 0x6f, 0x6d, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x37, 0x0a, 0x09, 0x75, 0x70, 0x74, 0x6f, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x08, 0x75, 0x70, 0x74, 0x6f, 0x44, 0x61, 0x74, 0x65, 0x12, 0x39, 0x0a, 0x0c, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x63, 0x78, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x22, 0xc0, 0x03, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3a, 0x0a,
	0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x78, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0b, 0x70, 0x61,
	0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x5f, 0x0a, 0x16, 0x65, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x73, 0x70,
	0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x42, 0x02, 0x18, 0x01, 0x52, 0x14, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x50, 0x0a, 0x12, 0x73, 0x68,
	0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x64, 0x65, 0x65, 0x70, 0x5f, 0x6c, 0x69, 0x6e, 0x6b,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x53, 0x68, 0x65, 0x72, 0x6c, 0x6f,
	0x63, 0x6b, 0x44, 0x65, 0x65, 0x70, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x10, 0x73, 0x68, 0x65, 0x72,
	0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x65, 0x70, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x88, 0x01, 0x0a,
	0x25, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x78, 0x5f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65,
	0x72, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x63,
	0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x78, 0x57, 0x72,
	0x61, 0x70, 0x70, 0x65, 0x72, 0x52, 0x20, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x78, 0x57, 0x72, 0x61, 0x70,
	0x70, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xbf, 0x01, 0x0a, 0x37, 0x47, 0x65, 0x74, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x73,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x6e, 0x64,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x78, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x26, 0x0a, 0x0f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x22, 0xbc, 0x04, 0x0a, 0x38, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x55, 0x74, 0x69, 0x6c,
	0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x41,
	0x6e, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0xb1, 0x01, 0x0a, 0x1f,
	0x75, 0x6e, 0x69, 0x74, 0x73, 0x5f, 0x75, 0x74, 0x69, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x61,
	0x6e, 0x64, 0x5f, 0x63, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x6b, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73,
	0x55, 0x74, 0x69, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x41, 0x63,
	0x74, 0x6f, 0x72, 0x41, 0x6e, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x6e, 0x4d, 0x6f, 0x6e,
	0x74, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x55, 0x6e, 0x69, 0x74, 0x73,
	0x55, 0x74, 0x69, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x41, 0x6e, 0x64, 0x43, 0x61, 0x70, 0x70, 0x69,
	0x6e, 0x67, 0x52, 0x1b, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x7a, 0x65,
	0x64, 0x41, 0x6e, 0x64, 0x43, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x21, 0x0a, 0x0c, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x71, 0x0a, 0x29, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x5f, 0x75, 0x74, 0x69, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x6e,
	0x64, 0x5f, 0x63, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x77, 0x65, 0x62, 0x75, 0x69, 0x2e, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x52,
	0x24, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x55, 0x74, 0x69, 0x6c, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x6e, 0x64, 0x43, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67,
	0x54, 0x61, 0x62, 0x6c, 0x65, 0x1a, 0x90, 0x01, 0x0a, 0x17, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x55,
	0x74, 0x69, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x41, 0x6e, 0x64, 0x43, 0x61, 0x70, 0x70, 0x69, 0x6e,
	0x67, 0x12, 0x34, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x75, 0x6e, 0x69, 0x74, 0x73,
	0x5f, 0x75, 0x74, 0x69, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0d, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x07, 0x63, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x22, 0x51, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x73, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a,
	0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e,
	0x63, 0x78, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xf8, 0x01, 0x0a, 0x22,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x6e, 0x0a, 0x0b, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4d, 0x2e, 0x63,
	0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x73, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x73, 0x1a, 0x3d, 0x0a, 0x0f, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x89, 0x02, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x2c, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x78, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x2a, 0x0a, 0x11, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x37, 0x0a, 0x09,
	0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x66, 0x72, 0x6f,
	0x6d, 0x44, 0x61, 0x74, 0x65, 0x12, 0x33, 0x0a, 0x07, 0x74, 0x6f, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x06, 0x74, 0x6f, 0x44, 0x61, 0x74, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x65, 0x78,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x54, 0x78, 0x6e,
	0x49, 0x64, 0x22, 0x8b, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x4a, 0x0a, 0x14, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x5f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x77, 0x65, 0x62, 0x75, 0x69, 0x2e, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x12, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x54, 0x61, 0x62, 0x6c, 0x65,
	0x22, 0xe0, 0x02, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x46, 0x69, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x52,
	0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x78, 0x2e, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x47, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x64,
	0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x37, 0x0a, 0x09, 0x66, 0x72,
	0x6f, 0x6d, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x66, 0x72, 0x6f, 0x6d, 0x44,
	0x61, 0x74, 0x65, 0x12, 0x33, 0x0a, 0x07, 0x74, 0x6f, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x06, 0x74, 0x6f, 0x44, 0x61, 0x74, 0x65, 0x12, 0x39, 0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x63, 0x78, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x78, 0x74, 0x22, 0xe3, 0x01, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x46, 0x69, 0x53, 0x74, 0x6f,
	0x72, 0x65, 0x52, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x5a, 0x0a, 0x1d, 0x66, 0x69, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x5f, 0x74, 0x61, 0x62,
	0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x77, 0x65, 0x62, 0x75, 0x69, 0x2e, 0x54, 0x61, 0x62,
	0x6c, 0x65, 0x52, 0x19, 0x66, 0x69, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x3a, 0x0a,
	0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x78, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0b, 0x70, 0x61,
	0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x22, 0x81, 0x01, 0x0a, 0x2c, 0x47, 0x65,
	0x74, 0x46, 0x69, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x78, 0x2e,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x64, 0x65,
	0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x9c, 0x01,
	0x0a, 0x2d, 0x47, 0x65, 0x74, 0x46, 0x69, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x64, 0x65,
	0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x46, 0x0a, 0x0e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x77, 0x65, 0x62, 0x75, 0x69,
	0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x56, 0x32, 0x52, 0x0d, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x50, 0x0a, 0x20,
	0x47, 0x65, 0x74, 0x46, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x46, 0x69, 0x50, 0x6f, 0x69, 0x6e,
	0x74, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x2c, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0a, 0x2e, 0x63, 0x78, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xf7,
	0x01, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x46, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x46, 0x69, 0x50,
	0x6f, 0x69, 0x6e, 0x74, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x66, 0x69, 0x5f,
	0x63, 0x6f, 0x69, 0x6e, 0x73, 0x5f, 0x6d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x66, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x4d, 0x69, 0x67,
	0x72, 0x61, 0x74, 0x65, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x66, 0x69, 0x5f, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x73, 0x5f, 0x6d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x10, 0x66, 0x69, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x4d, 0x69, 0x67, 0x72, 0x61,
	0x74, 0x65, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x66, 0x69, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73,
	0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f,
	0x66, 0x69, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x12,
	0x27, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x75,
	0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x75, 0x6c, 0x65, 0x2a, 0x9c, 0x05, 0x0a, 0x17, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x73, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2a, 0x0a, 0x26, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x4f,
	0x46, 0x46, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x53, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f,
	0x4e, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x4f, 0x0a, 0x4b, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x53, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x5f, 0x49,
	0x4e, 0x46, 0x49, 0x4e, 0x49, 0x54, 0x45, 0x5f, 0x4f, 0x52, 0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52,
	0x59, 0x5f, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x32, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54,
	0x5f, 0x43, 0x41, 0x53, 0x48, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10,
	0x01, 0x12, 0x4d, 0x0a, 0x49, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45,
	0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x53, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x5f,
	0x41, 0x41, 0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x42, 0x41, 0x4e, 0x44, 0x5f, 0x31,
	0x5f, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x31, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x5f,
	0x43, 0x41, 0x53, 0x48, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x02,
	0x12, 0x4d, 0x0a, 0x49, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x53, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x5f, 0x41,
	0x41, 0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x42, 0x41, 0x4e, 0x44, 0x5f, 0x32, 0x5f,
	0x54, 0x49, 0x45, 0x52, 0x5f, 0x32, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x5f, 0x43,
	0x41, 0x53, 0x48, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x03, 0x12,
	0x4d, 0x0a, 0x49, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x53, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x5f, 0x41, 0x41,
	0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x42, 0x41, 0x4e, 0x44, 0x5f, 0x33, 0x5f, 0x54,
	0x49, 0x45, 0x52, 0x5f, 0x33, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x41,
	0x53, 0x48, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x04, 0x12, 0x48,
	0x0a, 0x44, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x53, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x5f, 0x53, 0x41, 0x4c,
	0x41, 0x52, 0x59, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x5f, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x31, 0x5f,
	0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x41, 0x53, 0x48, 0x42, 0x41, 0x43, 0x4b,
	0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x05, 0x12, 0x41, 0x0a, 0x3d, 0x52, 0x45, 0x57, 0x41,
	0x52, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x53, 0x5f, 0x4f,
	0x50, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x5f, 0x50, 0x4c, 0x55, 0x53, 0x5f, 0x54, 0x49, 0x45, 0x52,
	0x5f, 0x31, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x41, 0x53, 0x48, 0x42,
	0x41, 0x43, 0x4b, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x06, 0x12, 0x45, 0x0a, 0x41, 0x52,
	0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x53, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x5f, 0x49, 0x4e, 0x46, 0x49, 0x4e, 0x49,
	0x54, 0x45, 0x5f, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x32, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e,
	0x54, 0x5f, 0x43, 0x41, 0x53, 0x48, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52,
	0x10, 0x07, 0x12, 0x43, 0x0a, 0x3f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x46, 0x46,
	0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x53, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x53,
	0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x32, 0x5f, 0x50,
	0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x41, 0x53, 0x48, 0x42, 0x41, 0x43, 0x4b, 0x5f,
	0x4f, 0x46, 0x46, 0x45, 0x52, 0x10, 0x08, 0x32, 0xd4, 0x13, 0x0a, 0x07, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x12, 0xa7, 0x01, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x12, 0x31, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x63, 0x78, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2d,
	0xd8, 0xba, 0xd7, 0x0a, 0x01, 0xda, 0xc2, 0xd7, 0x0a, 0x14, 0x4d, 0x4f, 0x44, 0x45, 0x52, 0x41,
	0x54, 0x45, 0x4c, 0x59, 0x5f, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d,
	0xe8, 0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a, 0x01, 0xf0, 0x92, 0xe8, 0x6a, 0x01, 0x12, 0x95, 0x01,
	0x0a, 0x09, 0x47, 0x65, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x12, 0x2b, 0x2e, 0x63, 0x78,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2d, 0xd8, 0xba, 0xd7, 0x0a, 0x01, 0xda, 0xc2, 0xd7,
	0x0a, 0x14, 0x4d, 0x4f, 0x44, 0x45, 0x52, 0x41, 0x54, 0x45, 0x4c, 0x59, 0x5f, 0x53, 0x45, 0x4e,
	0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a, 0x01,
	0xf0, 0x92, 0xe8, 0x6a, 0x01, 0x12, 0xa4, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x12, 0x30, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x63, 0x78, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2d, 0xd8,
	0xba, 0xd7, 0x0a, 0x01, 0xda, 0xc2, 0xd7, 0x0a, 0x14, 0x4d, 0x4f, 0x44, 0x45, 0x52, 0x41, 0x54,
	0x45, 0x4c, 0x59, 0x5f, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8,
	0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a, 0x01, 0xf0, 0x92, 0xe8, 0x6a, 0x01, 0x12, 0xad, 0x01, 0x0a,
	0x11, 0x47, 0x65, 0x74, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x65, 0x64, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x73, 0x12, 0x33, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47,
	0x65, 0x74, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x65, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x65, 0x64, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2d, 0xd8,
	0xba, 0xd7, 0x0a, 0x01, 0xda, 0xc2, 0xd7, 0x0a, 0x14, 0x4d, 0x4f, 0x44, 0x45, 0x52, 0x41, 0x54,
	0x45, 0x4c, 0x59, 0x5f, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8,
	0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a, 0x01, 0xf0, 0x92, 0xe8, 0x6a, 0x01, 0x12, 0xb0, 0x01, 0x0a,
	0x12, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x42,
	0x79, 0x49, 0x64, 0x12, 0x34, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x42, 0x79,
	0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x63, 0x78, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x2d, 0xd8, 0xba, 0xd7, 0x0a, 0x01, 0xda, 0xc2, 0xd7, 0x0a, 0x14, 0x4d, 0x4f, 0x44, 0x45,
	0x52, 0x41, 0x54, 0x45, 0x4c, 0x59, 0x5f, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45,
	0xa0, 0x8d, 0xe8, 0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a, 0x01, 0xf0, 0x92, 0xe8, 0x6a, 0x01, 0x12,
	0xb0, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x12, 0x34, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x63,
	0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x2d, 0xd8, 0xba, 0xd7, 0x0a, 0x01, 0xda, 0xc2, 0xd7, 0x0a, 0x14, 0x4d,
	0x4f, 0x44, 0x45, 0x52, 0x41, 0x54, 0x45, 0x4c, 0x59, 0x5f, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54,
	0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a, 0x01, 0xf0, 0x92, 0xe8,
	0x6a, 0x01, 0x12, 0xbf, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x39,
	0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x63, 0x78, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2d, 0xd8, 0xba, 0xd7, 0x0a, 0x01, 0xda, 0xc2, 0xd7, 0x0a,
	0x14, 0x4d, 0x4f, 0x44, 0x45, 0x52, 0x41, 0x54, 0x45, 0x4c, 0x59, 0x5f, 0x53, 0x45, 0x4e, 0x53,
	0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a, 0x01, 0xf0,
	0x92, 0xe8, 0x6a, 0x01, 0x12, 0x8a, 0x02, 0x0a, 0x30, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x6e, 0x64, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x49, 0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x12, 0x52, 0x2e, 0x63, 0x78, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55,
	0x6e, 0x69, 0x74, 0x73, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46,
	0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x6e, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49,
	0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x53, 0x2e,
	0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x73, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x6e, 0x64, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x49, 0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x2d, 0xd8, 0xba, 0xd7, 0x0a, 0x01, 0xda, 0xc2, 0xd7, 0x0a, 0x14, 0x4d, 0x4f,
	0x44, 0x45, 0x52, 0x41, 0x54, 0x45, 0x4c, 0x59, 0x5f, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49,
	0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a, 0x01, 0xf0, 0x92, 0xe8, 0x6a,
	0x01, 0x12, 0xc8, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x3c, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3d,
	0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x73, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2d, 0xd8,
	0xba, 0xd7, 0x0a, 0x01, 0xda, 0xc2, 0xd7, 0x0a, 0x14, 0x4d, 0x4f, 0x44, 0x45, 0x52, 0x41, 0x54,
	0x45, 0x4c, 0x59, 0x5f, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8,
	0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a, 0x01, 0xf0, 0x92, 0xe8, 0x6a, 0x01, 0x12, 0xaa, 0x01, 0x0a,
	0x10, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x32, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2d, 0xd8, 0xba, 0xd7, 0x0a,
	0x01, 0xda, 0xc2, 0xd7, 0x0a, 0x14, 0x4d, 0x4f, 0x44, 0x45, 0x52, 0x41, 0x54, 0x45, 0x4c, 0x59,
	0x5f, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x01, 0xa8,
	0x8d, 0xe8, 0x6a, 0x01, 0xf0, 0x92, 0xe8, 0x6a, 0x01, 0x12, 0xce, 0x01, 0x0a, 0x1c, 0x47, 0x65,
	0x74, 0x46, 0x69, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3e, 0x2e, 0x63, 0x78, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x69, 0x53, 0x74, 0x6f, 0x72,
	0x65, 0x52, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f, 0x2e, 0x63, 0x78, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x69, 0x53, 0x74, 0x6f, 0x72,
	0x65, 0x52, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2d, 0xd8, 0xba, 0xd7,
	0x0a, 0x01, 0xda, 0xc2, 0xd7, 0x0a, 0x14, 0x4d, 0x4f, 0x44, 0x45, 0x52, 0x41, 0x54, 0x45, 0x4c,
	0x59, 0x5f, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x01,
	0xa8, 0x8d, 0xe8, 0x6a, 0x01, 0xf0, 0x92, 0xe8, 0x6a, 0x01, 0x12, 0xe9, 0x01, 0x0a, 0x25, 0x47,
	0x65, 0x74, 0x46, 0x69, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x47, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x2e, 0x47, 0x65, 0x74, 0x46, 0x69, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x64, 0x65, 0x6d,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x48, 0x2e,
	0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x69, 0x53,
	0x74, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x64,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2d, 0xd8, 0xba, 0xd7, 0x0a, 0x01, 0xda, 0xc2,
	0xd7, 0x0a, 0x14, 0x4d, 0x4f, 0x44, 0x45, 0x52, 0x41, 0x54, 0x45, 0x4c, 0x59, 0x5f, 0x53, 0x45,
	0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a,
	0x01, 0xf0, 0x92, 0xe8, 0x6a, 0x01, 0x12, 0xc5, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x46, 0x69,
	0x43, 0x6f, 0x69, 0x6e, 0x73, 0x46, 0x69, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x3b, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x2e, 0x47, 0x65, 0x74, 0x46, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x46, 0x69, 0x50, 0x6f, 0x69,
	0x6e, 0x74, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x3c, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x47, 0x65,
	0x74, 0x46, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x46, 0x69, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x2d, 0xd8, 0xba, 0xd7, 0x0a, 0x01, 0xda, 0xc2, 0xd7, 0x0a, 0x14, 0x4d, 0x4f, 0x44, 0x45, 0x52,
	0x41, 0x54, 0x45, 0x4c, 0x59, 0x5f, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0,
	0x8d, 0xe8, 0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a, 0x01, 0xf0, 0x92, 0xe8, 0x6a, 0x01, 0x42, 0x6c,
	0x0a, 0x34, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x78, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x5a, 0x34, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_cx_data_collector_rewards_service_proto_rawDescOnce sync.Once
	file_api_cx_data_collector_rewards_service_proto_rawDescData = file_api_cx_data_collector_rewards_service_proto_rawDesc
)

func file_api_cx_data_collector_rewards_service_proto_rawDescGZIP() []byte {
	file_api_cx_data_collector_rewards_service_proto_rawDescOnce.Do(func() {
		file_api_cx_data_collector_rewards_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_cx_data_collector_rewards_service_proto_rawDescData)
	})
	return file_api_cx_data_collector_rewards_service_proto_rawDescData
}

var file_api_cx_data_collector_rewards_service_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_cx_data_collector_rewards_service_proto_msgTypes = make([]protoimpl.MessageInfo, 28)
var file_api_cx_data_collector_rewards_service_proto_goTypes = []interface{}{
	(RewardOfferTypesOptions)(0),                                                             // 0: cx.data_collector.rewards.RewardOfferTypesOptions
	(GetRedeemedOffersRequest_ExpiryStatus)(0),                                               // 1: cx.data_collector.rewards.GetRedeemedOffersRequest.ExpiryStatus
	(*GetRewardOffersRequest)(nil),                                                           // 2: cx.data_collector.rewards.GetRewardOffersRequest
	(*GetRewardOffersResponse)(nil),                                                          // 3: cx.data_collector.rewards.GetRewardOffersResponse
	(*GetOffersRequest)(nil),                                                                 // 4: cx.data_collector.rewards.GetOffersRequest
	(*GetOffersResponse)(nil),                                                                // 5: cx.data_collector.rewards.GetOffersResponse
	(*GetUserRewardsRequest)(nil),                                                            // 6: cx.data_collector.rewards.GetUserRewardsRequest
	(*GetUserRewardsResponse)(nil),                                                           // 7: cx.data_collector.rewards.GetUserRewardsResponse
	(*GetRedeemedOffersRequest)(nil),                                                         // 8: cx.data_collector.rewards.GetRedeemedOffersRequest
	(*GetRedeemedOffersResponse)(nil),                                                        // 9: cx.data_collector.rewards.GetRedeemedOffersResponse
	(*GetRewardOfferByIdRequest)(nil),                                                        // 10: cx.data_collector.rewards.GetRewardOfferByIdRequest
	(*GetRewardOfferByIdResponse)(nil),                                                       // 11: cx.data_collector.rewards.GetRewardOfferByIdResponse
	(*GetExchangerOffersRequest)(nil),                                                        // 12: cx.data_collector.rewards.GetExchangerOffersRequest
	(*GetExchangerOffersResponse)(nil),                                                       // 13: cx.data_collector.rewards.GetExchangerOffersResponse
	(*GetExchangerOfferOrdersRequest)(nil),                                                   // 14: cx.data_collector.rewards.GetExchangerOfferOrdersRequest
	(*GetExchangerOfferOrdersResponse)(nil),                                                  // 15: cx.data_collector.rewards.GetExchangerOfferOrdersResponse
	(*GetRewardUnitsUtilisationForActorAndOfferInMonthRequest)(nil),                          // 16: cx.data_collector.rewards.GetRewardUnitsUtilisationForActorAndOfferInMonthRequest
	(*GetRewardUnitsUtilisationForActorAndOfferInMonthResponse)(nil),                         // 17: cx.data_collector.rewards.GetRewardUnitsUtilisationForActorAndOfferInMonthResponse
	(*GetRewardOfferTypesOptionsRequest)(nil),                                                // 18: cx.data_collector.rewards.GetRewardOfferTypesOptionsRequest
	(*GetRewardOfferTypesOptionsResponse)(nil),                                               // 19: cx.data_collector.rewards.GetRewardOfferTypesOptionsResponse
	(*GetRewardDetailsRequest)(nil),                                                          // 20: cx.data_collector.rewards.GetRewardDetailsRequest
	(*GetRewardDetailsResponse)(nil),                                                         // 21: cx.data_collector.rewards.GetRewardDetailsResponse
	(*GetFiStoreRedemptionsDetailsRequest)(nil),                                              // 22: cx.data_collector.rewards.GetFiStoreRedemptionsDetailsRequest
	(*GetFiStoreRedemptionsDetailsResponse)(nil),                                             // 23: cx.data_collector.rewards.GetFiStoreRedemptionsDetailsResponse
	(*GetFiStoreRedemptionAdditionalDetailsRequest)(nil),                                     // 24: cx.data_collector.rewards.GetFiStoreRedemptionAdditionalDetailsRequest
	(*GetFiStoreRedemptionAdditionalDetailsResponse)(nil),                                    // 25: cx.data_collector.rewards.GetFiStoreRedemptionAdditionalDetailsResponse
	(*GetFiCoinsFiPointsDetailsRequest)(nil),                                                 // 26: cx.data_collector.rewards.GetFiCoinsFiPointsDetailsRequest
	(*GetFiCoinsFiPointsDetailsResponse)(nil),                                                // 27: cx.data_collector.rewards.GetFiCoinsFiPointsDetailsResponse
	(*GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCapping)(nil), // 28: cx.data_collector.rewards.GetRewardUnitsUtilisationForActorAndOfferInMonthResponse.UnitsUtilizedAndCapping
	nil,                                      // 29: cx.data_collector.rewards.GetRewardOfferTypesOptionsResponse.OfferTypesEntry
	(*cx.Header)(nil),                        // 30: cx.Header
	(*timestamppb.Timestamp)(nil),            // 31: google.protobuf.Timestamp
	(*rpc.Status)(nil),                       // 32: rpc.Status
	(*customer_auth.SherlockDeepLink)(nil),   // 33: cx.customer_auth.SherlockDeepLink
	(*RewardOffer)(nil),                      // 34: cx.data_collector.rewards.RewardOffer
	(OfferRedemptionMode)(0),                 // 35: cx.data_collector.rewards.OfferRedemptionMode
	(*cx.PageContextRequest)(nil),            // 36: cx.PageContextRequest
	(casper.CardOfferType)(0),                // 37: casper.CardOfferType
	(*Offer)(nil),                            // 38: cx.data_collector.rewards.Offer
	(*cx.PageContextResponse)(nil),           // 39: cx.PageContextResponse
	(rewards.RewardType)(0),                  // 40: rewards.RewardType
	(rewards.VisibilityType)(0),              // 41: rewards.VisibilityType
	(*rewards.Reward)(nil),                   // 42: rewards.Reward
	(*RewardCxWrapper)(nil),                  // 43: cx.data_collector.rewards.RewardCxWrapper
	(casper.OfferType)(0),                    // 44: casper.OfferType
	(casper.OfferVendor)(0),                  // 45: casper.OfferVendor
	(redemption.OfferRedemptionState)(0),     // 46: casper.OfferRedemptionState
	(*redemption.RedeemedOffer)(nil),         // 47: casper.RedeemedOffer
	(*RedeemedOfferCxWrapper)(nil),           // 48: cx.data_collector.rewards.RedeemedOfferCxWrapper
	(*ExchangerOffer)(nil),                   // 49: cx.data_collector.rewards.ExchangerOffer
	(exchanger.ExchangerOfferOrderState)(0),  // 50: casper.exchanger.ExchangerOfferOrderState
	(*exchanger.ExchangerOfferOrder)(nil),    // 51: casper.exchanger.ExchangerOfferOrder
	(*ExchangerOfferOrderCxWrapper)(nil),     // 52: cx.data_collector.rewards.ExchangerOfferOrderCxWrapper
	(*webui.Table)(nil),                      // 53: api.typesv2.webui.Table
	(external_vendor_redemption.Category)(0), // 54: casper.external_vendor_redemption.Category
	(*webui.LabelValueV2)(nil),               // 55: api.typesv2.webui.LabelValueV2
}
var file_api_cx_data_collector_rewards_service_proto_depIdxs = []int32{
	30,  // 0: cx.data_collector.rewards.GetRewardOffersRequest.header:type_name -> cx.Header
	31,  // 1: cx.data_collector.rewards.GetRewardOffersRequest.date:type_name -> google.protobuf.Timestamp
	31,  // 2: cx.data_collector.rewards.GetRewardOffersRequest.from_time:type_name -> google.protobuf.Timestamp
	32,  // 3: cx.data_collector.rewards.GetRewardOffersResponse.status:type_name -> rpc.Status
	33,  // 4: cx.data_collector.rewards.GetRewardOffersResponse.sherlock_deep_link:type_name -> cx.customer_auth.SherlockDeepLink
	34,  // 5: cx.data_collector.rewards.GetRewardOffersResponse.reward_offers:type_name -> cx.data_collector.rewards.RewardOffer
	30,  // 6: cx.data_collector.rewards.GetOffersRequest.header:type_name -> cx.Header
	35,  // 7: cx.data_collector.rewards.GetOffersRequest.redemption_mode:type_name -> cx.data_collector.rewards.OfferRedemptionMode
	36,  // 8: cx.data_collector.rewards.GetOffersRequest.page_context:type_name -> cx.PageContextRequest
	37,  // 9: cx.data_collector.rewards.GetOffersRequest.card_offers_type:type_name -> casper.CardOfferType
	31,  // 10: cx.data_collector.rewards.GetOffersRequest.date:type_name -> google.protobuf.Timestamp
	31,  // 11: cx.data_collector.rewards.GetOffersRequest.from_time:type_name -> google.protobuf.Timestamp
	32,  // 12: cx.data_collector.rewards.GetOffersResponse.status:type_name -> rpc.Status
	33,  // 13: cx.data_collector.rewards.GetOffersResponse.sherlock_deep_link:type_name -> cx.customer_auth.SherlockDeepLink
	38,  // 14: cx.data_collector.rewards.GetOffersResponse.offers:type_name -> cx.data_collector.rewards.Offer
	39,  // 15: cx.data_collector.rewards.GetOffersResponse.page_context:type_name -> cx.PageContextResponse
	30,  // 16: cx.data_collector.rewards.GetUserRewardsRequest.header:type_name -> cx.Header
	40,  // 17: cx.data_collector.rewards.GetUserRewardsRequest.reward_type:type_name -> rewards.RewardType
	41,  // 18: cx.data_collector.rewards.GetUserRewardsRequest.visibility_type:type_name -> rewards.VisibilityType
	31,  // 19: cx.data_collector.rewards.GetUserRewardsRequest.start_date:type_name -> google.protobuf.Timestamp
	31,  // 20: cx.data_collector.rewards.GetUserRewardsRequest.end_date:type_name -> google.protobuf.Timestamp
	36,  // 21: cx.data_collector.rewards.GetUserRewardsRequest.page_context:type_name -> cx.PageContextRequest
	32,  // 22: cx.data_collector.rewards.GetUserRewardsResponse.status:type_name -> rpc.Status
	39,  // 23: cx.data_collector.rewards.GetUserRewardsResponse.page_context:type_name -> cx.PageContextResponse
	42,  // 24: cx.data_collector.rewards.GetUserRewardsResponse.rewards:type_name -> rewards.Reward
	33,  // 25: cx.data_collector.rewards.GetUserRewardsResponse.sherlock_deep_link:type_name -> cx.customer_auth.SherlockDeepLink
	43,  // 26: cx.data_collector.rewards.GetUserRewardsResponse.reward_cx_wrapper_list:type_name -> cx.data_collector.rewards.RewardCxWrapper
	30,  // 27: cx.data_collector.rewards.GetRedeemedOffersRequest.header:type_name -> cx.Header
	44,  // 28: cx.data_collector.rewards.GetRedeemedOffersRequest.offer_type:type_name -> casper.OfferType
	45,  // 29: cx.data_collector.rewards.GetRedeemedOffersRequest.vendor:type_name -> casper.OfferVendor
	46,  // 30: cx.data_collector.rewards.GetRedeemedOffersRequest.redemption_state:type_name -> casper.OfferRedemptionState
	31,  // 31: cx.data_collector.rewards.GetRedeemedOffersRequest.from_date:type_name -> google.protobuf.Timestamp
	31,  // 32: cx.data_collector.rewards.GetRedeemedOffersRequest.upto_date:type_name -> google.protobuf.Timestamp
	1,   // 33: cx.data_collector.rewards.GetRedeemedOffersRequest.expiry_status:type_name -> cx.data_collector.rewards.GetRedeemedOffersRequest.ExpiryStatus
	36,  // 34: cx.data_collector.rewards.GetRedeemedOffersRequest.page_context:type_name -> cx.PageContextRequest
	32,  // 35: cx.data_collector.rewards.GetRedeemedOffersResponse.status:type_name -> rpc.Status
	39,  // 36: cx.data_collector.rewards.GetRedeemedOffersResponse.page_context:type_name -> cx.PageContextResponse
	47,  // 37: cx.data_collector.rewards.GetRedeemedOffersResponse.redeemed_offers:type_name -> casper.RedeemedOffer
	33,  // 38: cx.data_collector.rewards.GetRedeemedOffersResponse.sherlock_deep_link:type_name -> cx.customer_auth.SherlockDeepLink
	48,  // 39: cx.data_collector.rewards.GetRedeemedOffersResponse.redeemed_offer_cx_wrapper_list:type_name -> cx.data_collector.rewards.RedeemedOfferCxWrapper
	30,  // 40: cx.data_collector.rewards.GetRewardOfferByIdRequest.header:type_name -> cx.Header
	32,  // 41: cx.data_collector.rewards.GetRewardOfferByIdResponse.status:type_name -> rpc.Status
	33,  // 42: cx.data_collector.rewards.GetRewardOfferByIdResponse.sherlock_deep_link:type_name -> cx.customer_auth.SherlockDeepLink
	34,  // 43: cx.data_collector.rewards.GetRewardOfferByIdResponse.reward_offer:type_name -> cx.data_collector.rewards.RewardOffer
	30,  // 44: cx.data_collector.rewards.GetExchangerOffersRequest.header:type_name -> cx.Header
	31,  // 45: cx.data_collector.rewards.GetExchangerOffersRequest.date:type_name -> google.protobuf.Timestamp
	31,  // 46: cx.data_collector.rewards.GetExchangerOffersRequest.from_time:type_name -> google.protobuf.Timestamp
	32,  // 47: cx.data_collector.rewards.GetExchangerOffersResponse.status:type_name -> rpc.Status
	33,  // 48: cx.data_collector.rewards.GetExchangerOffersResponse.sherlock_deep_link:type_name -> cx.customer_auth.SherlockDeepLink
	49,  // 49: cx.data_collector.rewards.GetExchangerOffersResponse.exchanger_offers:type_name -> cx.data_collector.rewards.ExchangerOffer
	30,  // 50: cx.data_collector.rewards.GetExchangerOfferOrdersRequest.header:type_name -> cx.Header
	50,  // 51: cx.data_collector.rewards.GetExchangerOfferOrdersRequest.redemption_state:type_name -> casper.exchanger.ExchangerOfferOrderState
	31,  // 52: cx.data_collector.rewards.GetExchangerOfferOrdersRequest.from_date:type_name -> google.protobuf.Timestamp
	31,  // 53: cx.data_collector.rewards.GetExchangerOfferOrdersRequest.upto_date:type_name -> google.protobuf.Timestamp
	36,  // 54: cx.data_collector.rewards.GetExchangerOfferOrdersRequest.page_context:type_name -> cx.PageContextRequest
	32,  // 55: cx.data_collector.rewards.GetExchangerOfferOrdersResponse.status:type_name -> rpc.Status
	39,  // 56: cx.data_collector.rewards.GetExchangerOfferOrdersResponse.page_context:type_name -> cx.PageContextResponse
	51,  // 57: cx.data_collector.rewards.GetExchangerOfferOrdersResponse.exchanger_offer_orders:type_name -> casper.exchanger.ExchangerOfferOrder
	33,  // 58: cx.data_collector.rewards.GetExchangerOfferOrdersResponse.sherlock_deep_link:type_name -> cx.customer_auth.SherlockDeepLink
	52,  // 59: cx.data_collector.rewards.GetExchangerOfferOrdersResponse.exchanger_offer_order_cx_wrapper_list:type_name -> cx.data_collector.rewards.ExchangerOfferOrderCxWrapper
	30,  // 60: cx.data_collector.rewards.GetRewardUnitsUtilisationForActorAndOfferInMonthRequest.header:type_name -> cx.Header
	31,  // 61: cx.data_collector.rewards.GetRewardUnitsUtilisationForActorAndOfferInMonthRequest.date:type_name -> google.protobuf.Timestamp
	32,  // 62: cx.data_collector.rewards.GetRewardUnitsUtilisationForActorAndOfferInMonthResponse.status:type_name -> rpc.Status
	28,  // 63: cx.data_collector.rewards.GetRewardUnitsUtilisationForActorAndOfferInMonthResponse.units_utilized_and_capping_list:type_name -> cx.data_collector.rewards.GetRewardUnitsUtilisationForActorAndOfferInMonthResponse.UnitsUtilizedAndCapping
	53,  // 64: cx.data_collector.rewards.GetRewardUnitsUtilisationForActorAndOfferInMonthResponse.reward_type_utilization_and_capping_table:type_name -> api.typesv2.webui.Table
	30,  // 65: cx.data_collector.rewards.GetRewardOfferTypesOptionsRequest.header:type_name -> cx.Header
	32,  // 66: cx.data_collector.rewards.GetRewardOfferTypesOptionsResponse.status:type_name -> rpc.Status
	29,  // 67: cx.data_collector.rewards.GetRewardOfferTypesOptionsResponse.offer_types:type_name -> cx.data_collector.rewards.GetRewardOfferTypesOptionsResponse.OfferTypesEntry
	30,  // 68: cx.data_collector.rewards.GetRewardDetailsRequest.header:type_name -> cx.Header
	31,  // 69: cx.data_collector.rewards.GetRewardDetailsRequest.from_date:type_name -> google.protobuf.Timestamp
	31,  // 70: cx.data_collector.rewards.GetRewardDetailsRequest.to_date:type_name -> google.protobuf.Timestamp
	32,  // 71: cx.data_collector.rewards.GetRewardDetailsResponse.status:type_name -> rpc.Status
	53,  // 72: cx.data_collector.rewards.GetRewardDetailsResponse.reward_details_table:type_name -> api.typesv2.webui.Table
	30,  // 73: cx.data_collector.rewards.GetFiStoreRedemptionsDetailsRequest.header:type_name -> cx.Header
	54,  // 74: cx.data_collector.rewards.GetFiStoreRedemptionsDetailsRequest.category:type_name -> casper.external_vendor_redemption.Category
	31,  // 75: cx.data_collector.rewards.GetFiStoreRedemptionsDetailsRequest.from_date:type_name -> google.protobuf.Timestamp
	31,  // 76: cx.data_collector.rewards.GetFiStoreRedemptionsDetailsRequest.to_date:type_name -> google.protobuf.Timestamp
	36,  // 77: cx.data_collector.rewards.GetFiStoreRedemptionsDetailsRequest.page_context:type_name -> cx.PageContextRequest
	32,  // 78: cx.data_collector.rewards.GetFiStoreRedemptionsDetailsResponse.status:type_name -> rpc.Status
	53,  // 79: cx.data_collector.rewards.GetFiStoreRedemptionsDetailsResponse.fi_store_reward_details_table:type_name -> api.typesv2.webui.Table
	39,  // 80: cx.data_collector.rewards.GetFiStoreRedemptionsDetailsResponse.page_context:type_name -> cx.PageContextResponse
	30,  // 81: cx.data_collector.rewards.GetFiStoreRedemptionAdditionalDetailsRequest.header:type_name -> cx.Header
	32,  // 82: cx.data_collector.rewards.GetFiStoreRedemptionAdditionalDetailsResponse.status:type_name -> rpc.Status
	55,  // 83: cx.data_collector.rewards.GetFiStoreRedemptionAdditionalDetailsResponse.reward_details:type_name -> api.typesv2.webui.LabelValueV2
	30,  // 84: cx.data_collector.rewards.GetFiCoinsFiPointsDetailsRequest.header:type_name -> cx.Header
	32,  // 85: cx.data_collector.rewards.GetFiCoinsFiPointsDetailsResponse.status:type_name -> rpc.Status
	40,  // 86: cx.data_collector.rewards.GetRewardUnitsUtilisationForActorAndOfferInMonthResponse.UnitsUtilizedAndCapping.reward_type:type_name -> rewards.RewardType
	2,   // 87: cx.data_collector.rewards.Rewards.GetRewardOffers:input_type -> cx.data_collector.rewards.GetRewardOffersRequest
	4,   // 88: cx.data_collector.rewards.Rewards.GetOffers:input_type -> cx.data_collector.rewards.GetOffersRequest
	6,   // 89: cx.data_collector.rewards.Rewards.GetUserRewards:input_type -> cx.data_collector.rewards.GetUserRewardsRequest
	8,   // 90: cx.data_collector.rewards.Rewards.GetRedeemedOffers:input_type -> cx.data_collector.rewards.GetRedeemedOffersRequest
	10,  // 91: cx.data_collector.rewards.Rewards.GetRewardOfferById:input_type -> cx.data_collector.rewards.GetRewardOfferByIdRequest
	12,  // 92: cx.data_collector.rewards.Rewards.GetExchangerOffers:input_type -> cx.data_collector.rewards.GetExchangerOffersRequest
	14,  // 93: cx.data_collector.rewards.Rewards.GetExchangerOfferOrders:input_type -> cx.data_collector.rewards.GetExchangerOfferOrdersRequest
	16,  // 94: cx.data_collector.rewards.Rewards.GetRewardUnitsUtilisationForActorAndOfferInMonth:input_type -> cx.data_collector.rewards.GetRewardUnitsUtilisationForActorAndOfferInMonthRequest
	18,  // 95: cx.data_collector.rewards.Rewards.GetRewardOfferTypesOptions:input_type -> cx.data_collector.rewards.GetRewardOfferTypesOptionsRequest
	20,  // 96: cx.data_collector.rewards.Rewards.GetRewardDetails:input_type -> cx.data_collector.rewards.GetRewardDetailsRequest
	22,  // 97: cx.data_collector.rewards.Rewards.GetFiStoreRedemptionsDetails:input_type -> cx.data_collector.rewards.GetFiStoreRedemptionsDetailsRequest
	24,  // 98: cx.data_collector.rewards.Rewards.GetFiStoreRedemptionAdditionalDetails:input_type -> cx.data_collector.rewards.GetFiStoreRedemptionAdditionalDetailsRequest
	26,  // 99: cx.data_collector.rewards.Rewards.GetFiCoinsFiPointsDetails:input_type -> cx.data_collector.rewards.GetFiCoinsFiPointsDetailsRequest
	3,   // 100: cx.data_collector.rewards.Rewards.GetRewardOffers:output_type -> cx.data_collector.rewards.GetRewardOffersResponse
	5,   // 101: cx.data_collector.rewards.Rewards.GetOffers:output_type -> cx.data_collector.rewards.GetOffersResponse
	7,   // 102: cx.data_collector.rewards.Rewards.GetUserRewards:output_type -> cx.data_collector.rewards.GetUserRewardsResponse
	9,   // 103: cx.data_collector.rewards.Rewards.GetRedeemedOffers:output_type -> cx.data_collector.rewards.GetRedeemedOffersResponse
	11,  // 104: cx.data_collector.rewards.Rewards.GetRewardOfferById:output_type -> cx.data_collector.rewards.GetRewardOfferByIdResponse
	13,  // 105: cx.data_collector.rewards.Rewards.GetExchangerOffers:output_type -> cx.data_collector.rewards.GetExchangerOffersResponse
	15,  // 106: cx.data_collector.rewards.Rewards.GetExchangerOfferOrders:output_type -> cx.data_collector.rewards.GetExchangerOfferOrdersResponse
	17,  // 107: cx.data_collector.rewards.Rewards.GetRewardUnitsUtilisationForActorAndOfferInMonth:output_type -> cx.data_collector.rewards.GetRewardUnitsUtilisationForActorAndOfferInMonthResponse
	19,  // 108: cx.data_collector.rewards.Rewards.GetRewardOfferTypesOptions:output_type -> cx.data_collector.rewards.GetRewardOfferTypesOptionsResponse
	21,  // 109: cx.data_collector.rewards.Rewards.GetRewardDetails:output_type -> cx.data_collector.rewards.GetRewardDetailsResponse
	23,  // 110: cx.data_collector.rewards.Rewards.GetFiStoreRedemptionsDetails:output_type -> cx.data_collector.rewards.GetFiStoreRedemptionsDetailsResponse
	25,  // 111: cx.data_collector.rewards.Rewards.GetFiStoreRedemptionAdditionalDetails:output_type -> cx.data_collector.rewards.GetFiStoreRedemptionAdditionalDetailsResponse
	27,  // 112: cx.data_collector.rewards.Rewards.GetFiCoinsFiPointsDetails:output_type -> cx.data_collector.rewards.GetFiCoinsFiPointsDetailsResponse
	100, // [100:113] is the sub-list for method output_type
	87,  // [87:100] is the sub-list for method input_type
	87,  // [87:87] is the sub-list for extension type_name
	87,  // [87:87] is the sub-list for extension extendee
	0,   // [0:87] is the sub-list for field type_name
}

func init() { file_api_cx_data_collector_rewards_service_proto_init() }
func file_api_cx_data_collector_rewards_service_proto_init() {
	if File_api_cx_data_collector_rewards_service_proto != nil {
		return
	}
	file_api_cx_data_collector_rewards_rewards_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_cx_data_collector_rewards_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardOffersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_rewards_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardOffersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_rewards_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOffersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_rewards_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOffersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_rewards_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserRewardsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_rewards_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserRewardsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_rewards_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRedeemedOffersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_rewards_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRedeemedOffersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_rewards_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardOfferByIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_rewards_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardOfferByIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_rewards_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangerOffersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_rewards_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangerOffersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_rewards_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangerOfferOrdersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_rewards_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangerOfferOrdersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_rewards_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardUnitsUtilisationForActorAndOfferInMonthRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_rewards_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardUnitsUtilisationForActorAndOfferInMonthResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_rewards_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardOfferTypesOptionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_rewards_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardOfferTypesOptionsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_rewards_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_rewards_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_rewards_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFiStoreRedemptionsDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_rewards_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFiStoreRedemptionsDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_rewards_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFiStoreRedemptionAdditionalDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_rewards_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFiStoreRedemptionAdditionalDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_rewards_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFiCoinsFiPointsDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_rewards_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFiCoinsFiPointsDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_rewards_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCapping); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_cx_data_collector_rewards_service_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   28,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_cx_data_collector_rewards_service_proto_goTypes,
		DependencyIndexes: file_api_cx_data_collector_rewards_service_proto_depIdxs,
		EnumInfos:         file_api_cx_data_collector_rewards_service_proto_enumTypes,
		MessageInfos:      file_api_cx_data_collector_rewards_service_proto_msgTypes,
	}.Build()
	File_api_cx_data_collector_rewards_service_proto = out.File
	file_api_cx_data_collector_rewards_service_proto_rawDesc = nil
	file_api_cx_data_collector_rewards_service_proto_goTypes = nil
	file_api_cx_data_collector_rewards_service_proto_depIdxs = nil
}
